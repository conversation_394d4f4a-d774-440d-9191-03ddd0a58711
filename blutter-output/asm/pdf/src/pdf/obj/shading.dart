// lib: , url: package:pdf/src/pdf/obj/shading.dart

// class id: 1050816, size: 0x8
class :: {
}

// class id: 871, size: 0x50, field offset: 0x2c
class PdfShading extends PdfObject<dynamic> {

  _ prepare(/* No info */) {
    // ** addr: 0x7cc938, size: 0x700
    // 0x7cc938: EnterFrame
    //     0x7cc938: stp             fp, lr, [SP, #-0x10]!
    //     0x7cc93c: mov             fp, SP
    // 0x7cc940: AllocStack(0x58)
    //     0x7cc940: sub             SP, SP, #0x58
    // 0x7cc944: SetupParameters(PdfShading this /* r1 => r2, fp-0x20 */)
    //     0x7cc944: mov             x2, x1
    //     0x7cc948: stur            x1, [fp, #-0x20]
    // 0x7cc94c: CheckStackOverflow
    //     0x7cc94c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cc950: cmp             SP, x16
    //     0x7cc954: b.ls            #0x7ccf48
    // 0x7cc958: LoadField: r3 = r2->field_1b
    //     0x7cc958: ldur            w3, [x2, #0x1b]
    // 0x7cc95c: DecompressPointer r3
    //     0x7cc95c: add             x3, x3, HEAP, lsl #32
    // 0x7cc960: stur            x3, [fp, #-0x18]
    // 0x7cc964: LoadField: r4 = r2->field_2b
    //     0x7cc964: ldur            w4, [x2, #0x2b]
    // 0x7cc968: DecompressPointer r4
    //     0x7cc968: add             x4, x4, HEAP, lsl #32
    // 0x7cc96c: stur            x4, [fp, #-0x10]
    // 0x7cc970: LoadField: r0 = r4->field_7
    //     0x7cc970: ldur            x0, [x4, #7]
    // 0x7cc974: add             x5, x0, #2
    // 0x7cc978: r0 = BoxInt64Instr(r5)
    //     0x7cc978: sbfiz           x0, x5, #1, #0x1f
    //     0x7cc97c: cmp             x5, x0, asr #1
    //     0x7cc980: b.eq            #0x7cc98c
    //     0x7cc984: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7cc988: stur            x5, [x0, #7]
    // 0x7cc98c: stur            x0, [fp, #-8]
    // 0x7cc990: r0 = PdfNum()
    //     0x7cc990: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7cc994: mov             x3, x0
    // 0x7cc998: ldur            x0, [fp, #-8]
    // 0x7cc99c: stur            x3, [fp, #-0x28]
    // 0x7cc9a0: StoreField: r3->field_7 = r0
    //     0x7cc9a0: stur            w0, [x3, #7]
    // 0x7cc9a4: ldur            x4, [fp, #-0x18]
    // 0x7cc9a8: LoadField: r5 = r4->field_7
    //     0x7cc9a8: ldur            w5, [x4, #7]
    // 0x7cc9ac: DecompressPointer r5
    //     0x7cc9ac: add             x5, x5, HEAP, lsl #32
    // 0x7cc9b0: mov             x0, x3
    // 0x7cc9b4: mov             x2, x5
    // 0x7cc9b8: stur            x5, [fp, #-8]
    // 0x7cc9bc: r1 = Null
    //     0x7cc9bc: mov             x1, NULL
    // 0x7cc9c0: cmp             w2, NULL
    // 0x7cc9c4: b.eq            #0x7cc9e8
    // 0x7cc9c8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cc9c8: ldur            w4, [x2, #0x17]
    // 0x7cc9cc: DecompressPointer r4
    //     0x7cc9cc: add             x4, x4, HEAP, lsl #32
    // 0x7cc9d0: r8 = X0 bound PdfDataType
    //     0x7cc9d0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cc9d4: ldr             x8, [x8, #0x6d8]
    // 0x7cc9d8: LoadField: r9 = r4->field_7
    //     0x7cc9d8: ldur            x9, [x4, #7]
    // 0x7cc9dc: r3 = Null
    //     0x7cc9dc: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5af80] Null
    //     0x7cc9e0: ldr             x3, [x3, #0xf80]
    // 0x7cc9e4: blr             x9
    // 0x7cc9e8: ldur            x0, [fp, #-0x18]
    // 0x7cc9ec: LoadField: r4 = r0->field_b
    //     0x7cc9ec: ldur            w4, [x0, #0xb]
    // 0x7cc9f0: DecompressPointer r4
    //     0x7cc9f0: add             x4, x4, HEAP, lsl #32
    // 0x7cc9f4: mov             x1, x4
    // 0x7cc9f8: ldur            x3, [fp, #-0x28]
    // 0x7cc9fc: stur            x4, [fp, #-0x30]
    // 0x7cca00: r2 = "/ShadingType"
    //     0x7cca00: add             x2, PP, #0x5a, lsl #12  ; [pp+0x5af90] "/ShadingType"
    //     0x7cca04: ldr             x2, [x2, #0xf90]
    // 0x7cca08: r0 = []=()
    //     0x7cca08: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cca0c: ldur            x2, [fp, #-8]
    // 0x7cca10: r0 = Instance_PdfBool
    //     0x7cca10: add             x0, PP, #0x5a, lsl #12  ; [pp+0x5af98] Obj!PdfBool@e0c981
    //     0x7cca14: ldr             x0, [x0, #0xf98]
    // 0x7cca18: r1 = Null
    //     0x7cca18: mov             x1, NULL
    // 0x7cca1c: cmp             w2, NULL
    // 0x7cca20: b.eq            #0x7cca44
    // 0x7cca24: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cca24: ldur            w4, [x2, #0x17]
    // 0x7cca28: DecompressPointer r4
    //     0x7cca28: add             x4, x4, HEAP, lsl #32
    // 0x7cca2c: r8 = X0 bound PdfDataType
    //     0x7cca2c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cca30: ldr             x8, [x8, #0x6d8]
    // 0x7cca34: LoadField: r9 = r4->field_7
    //     0x7cca34: ldur            x9, [x4, #7]
    // 0x7cca38: r3 = Null
    //     0x7cca38: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5afa0] Null
    //     0x7cca3c: ldr             x3, [x3, #0xfa0]
    // 0x7cca40: blr             x9
    // 0x7cca44: ldur            x1, [fp, #-0x30]
    // 0x7cca48: r2 = "/AntiAlias"
    //     0x7cca48: add             x2, PP, #0x5a, lsl #12  ; [pp+0x5afb0] "/AntiAlias"
    //     0x7cca4c: ldr             x2, [x2, #0xfb0]
    // 0x7cca50: r3 = Instance_PdfBool
    //     0x7cca50: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5af98] Obj!PdfBool@e0c981
    //     0x7cca54: ldr             x3, [x3, #0xf98]
    // 0x7cca58: r0 = []=()
    //     0x7cca58: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7cca5c: ldur            x2, [fp, #-8]
    // 0x7cca60: r0 = Instance_PdfName
    //     0x7cca60: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eb98] Obj!PdfName@e0c841
    //     0x7cca64: ldr             x0, [x0, #0xb98]
    // 0x7cca68: r1 = Null
    //     0x7cca68: mov             x1, NULL
    // 0x7cca6c: cmp             w2, NULL
    // 0x7cca70: b.eq            #0x7cca94
    // 0x7cca74: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7cca74: ldur            w4, [x2, #0x17]
    // 0x7cca78: DecompressPointer r4
    //     0x7cca78: add             x4, x4, HEAP, lsl #32
    // 0x7cca7c: r8 = X0 bound PdfDataType
    //     0x7cca7c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cca80: ldr             x8, [x8, #0x6d8]
    // 0x7cca84: LoadField: r9 = r4->field_7
    //     0x7cca84: ldur            x9, [x4, #7]
    // 0x7cca88: r3 = Null
    //     0x7cca88: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5afb8] Null
    //     0x7cca8c: ldr             x3, [x3, #0xfb8]
    // 0x7cca90: blr             x9
    // 0x7cca94: ldur            x1, [fp, #-0x30]
    // 0x7cca98: r2 = "/ColorSpace"
    //     0x7cca98: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ebb0] "/ColorSpace"
    //     0x7cca9c: ldr             x2, [x2, #0xbb0]
    // 0x7ccaa0: r3 = Instance_PdfName
    //     0x7ccaa0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb98] Obj!PdfName@e0c841
    //     0x7ccaa4: ldr             x3, [x3, #0xb98]
    // 0x7ccaa8: r0 = []=()
    //     0x7ccaa8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ccaac: ldur            x0, [fp, #-0x10]
    // 0x7ccab0: r16 = Instance_PdfShadingType
    //     0x7ccab0: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a68] Obj!PdfShadingType@e2ebe1
    //     0x7ccab4: ldr             x16, [x16, #0xa68]
    // 0x7ccab8: cmp             w0, w16
    // 0x7ccabc: b.ne            #0x7ccc3c
    // 0x7ccac0: ldur            x0, [fp, #-0x20]
    // 0x7ccac4: r3 = 8
    //     0x7ccac4: movz            x3, #0x8
    // 0x7ccac8: LoadField: r1 = r0->field_33
    //     0x7ccac8: ldur            w1, [x0, #0x33]
    // 0x7ccacc: DecompressPointer r1
    //     0x7ccacc: add             x1, x1, HEAP, lsl #32
    // 0x7ccad0: LoadField: d0 = r1->field_7
    //     0x7ccad0: ldur            d0, [x1, #7]
    // 0x7ccad4: LoadField: d1 = r1->field_f
    //     0x7ccad4: ldur            d1, [x1, #0xf]
    // 0x7ccad8: stur            d1, [fp, #-0x50]
    // 0x7ccadc: LoadField: r1 = r0->field_37
    //     0x7ccadc: ldur            w1, [x0, #0x37]
    // 0x7ccae0: DecompressPointer r1
    //     0x7ccae0: add             x1, x1, HEAP, lsl #32
    // 0x7ccae4: LoadField: d2 = r1->field_7
    //     0x7ccae4: ldur            d2, [x1, #7]
    // 0x7ccae8: stur            d2, [fp, #-0x48]
    // 0x7ccaec: LoadField: d3 = r1->field_f
    //     0x7ccaec: ldur            d3, [x1, #0xf]
    // 0x7ccaf0: stur            d3, [fp, #-0x40]
    // 0x7ccaf4: r4 = inline_Allocate_Double()
    //     0x7ccaf4: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0x7ccaf8: add             x4, x4, #0x10
    //     0x7ccafc: cmp             x1, x4
    //     0x7ccb00: b.ls            #0x7ccf50
    //     0x7ccb04: str             x4, [THR, #0x50]  ; THR::top
    //     0x7ccb08: sub             x4, x4, #0xf
    //     0x7ccb0c: movz            x1, #0xe15c
    //     0x7ccb10: movk            x1, #0x3, lsl #16
    //     0x7ccb14: stur            x1, [x4, #-1]
    // 0x7ccb18: StoreField: r4->field_7 = d0
    //     0x7ccb18: stur            d0, [x4, #7]
    // 0x7ccb1c: mov             x2, x3
    // 0x7ccb20: stur            x4, [fp, #-0x18]
    // 0x7ccb24: r1 = Null
    //     0x7ccb24: mov             x1, NULL
    // 0x7ccb28: r0 = AllocateArray()
    //     0x7ccb28: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7ccb2c: mov             x2, x0
    // 0x7ccb30: ldur            x0, [fp, #-0x18]
    // 0x7ccb34: stur            x2, [fp, #-0x28]
    // 0x7ccb38: StoreField: r2->field_f = r0
    //     0x7ccb38: stur            w0, [x2, #0xf]
    // 0x7ccb3c: ldur            d0, [fp, #-0x50]
    // 0x7ccb40: r0 = inline_Allocate_Double()
    //     0x7ccb40: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7ccb44: add             x0, x0, #0x10
    //     0x7ccb48: cmp             x1, x0
    //     0x7ccb4c: b.ls            #0x7ccf74
    //     0x7ccb50: str             x0, [THR, #0x50]  ; THR::top
    //     0x7ccb54: sub             x0, x0, #0xf
    //     0x7ccb58: movz            x1, #0xe15c
    //     0x7ccb5c: movk            x1, #0x3, lsl #16
    //     0x7ccb60: stur            x1, [x0, #-1]
    // 0x7ccb64: StoreField: r0->field_7 = d0
    //     0x7ccb64: stur            d0, [x0, #7]
    // 0x7ccb68: StoreField: r2->field_13 = r0
    //     0x7ccb68: stur            w0, [x2, #0x13]
    // 0x7ccb6c: ldur            d0, [fp, #-0x48]
    // 0x7ccb70: r0 = inline_Allocate_Double()
    //     0x7ccb70: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7ccb74: add             x0, x0, #0x10
    //     0x7ccb78: cmp             x1, x0
    //     0x7ccb7c: b.ls            #0x7ccf8c
    //     0x7ccb80: str             x0, [THR, #0x50]  ; THR::top
    //     0x7ccb84: sub             x0, x0, #0xf
    //     0x7ccb88: movz            x1, #0xe15c
    //     0x7ccb8c: movk            x1, #0x3, lsl #16
    //     0x7ccb90: stur            x1, [x0, #-1]
    // 0x7ccb94: StoreField: r0->field_7 = d0
    //     0x7ccb94: stur            d0, [x0, #7]
    // 0x7ccb98: ArrayStore: r2[0] = r0  ; List_4
    //     0x7ccb98: stur            w0, [x2, #0x17]
    // 0x7ccb9c: ldur            d0, [fp, #-0x40]
    // 0x7ccba0: r0 = inline_Allocate_Double()
    //     0x7ccba0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7ccba4: add             x0, x0, #0x10
    //     0x7ccba8: cmp             x1, x0
    //     0x7ccbac: b.ls            #0x7ccfa4
    //     0x7ccbb0: str             x0, [THR, #0x50]  ; THR::top
    //     0x7ccbb4: sub             x0, x0, #0xf
    //     0x7ccbb8: movz            x1, #0xe15c
    //     0x7ccbbc: movk            x1, #0x3, lsl #16
    //     0x7ccbc0: stur            x1, [x0, #-1]
    // 0x7ccbc4: StoreField: r0->field_7 = d0
    //     0x7ccbc4: stur            d0, [x0, #7]
    // 0x7ccbc8: StoreField: r2->field_1b = r0
    //     0x7ccbc8: stur            w0, [x2, #0x1b]
    // 0x7ccbcc: r1 = <num>
    //     0x7ccbcc: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0x7ccbd0: r0 = AllocateGrowableArray()
    //     0x7ccbd0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7ccbd4: mov             x1, x0
    // 0x7ccbd8: ldur            x0, [fp, #-0x28]
    // 0x7ccbdc: StoreField: r1->field_f = r0
    //     0x7ccbdc: stur            w0, [x1, #0xf]
    // 0x7ccbe0: r0 = 8
    //     0x7ccbe0: movz            x0, #0x8
    // 0x7ccbe4: StoreField: r1->field_b = r0
    //     0x7ccbe4: stur            w0, [x1, #0xb]
    // 0x7ccbe8: r0 = fromNum()
    //     0x7ccbe8: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7ccbec: ldur            x2, [fp, #-8]
    // 0x7ccbf0: mov             x3, x0
    // 0x7ccbf4: r1 = Null
    //     0x7ccbf4: mov             x1, NULL
    // 0x7ccbf8: stur            x3, [fp, #-0x18]
    // 0x7ccbfc: cmp             w2, NULL
    // 0x7ccc00: b.eq            #0x7ccc24
    // 0x7ccc04: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ccc04: ldur            w4, [x2, #0x17]
    // 0x7ccc08: DecompressPointer r4
    //     0x7ccc08: add             x4, x4, HEAP, lsl #32
    // 0x7ccc0c: r8 = X0 bound PdfDataType
    //     0x7ccc0c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ccc10: ldr             x8, [x8, #0x6d8]
    // 0x7ccc14: LoadField: r9 = r4->field_7
    //     0x7ccc14: ldur            x9, [x4, #7]
    // 0x7ccc18: r3 = Null
    //     0x7ccc18: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5afc8] Null
    //     0x7ccc1c: ldr             x3, [x3, #0xfc8]
    // 0x7ccc20: blr             x9
    // 0x7ccc24: ldur            x1, [fp, #-0x30]
    // 0x7ccc28: ldur            x3, [fp, #-0x18]
    // 0x7ccc2c: r2 = "/Coords"
    //     0x7ccc2c: add             x2, PP, #0x5a, lsl #12  ; [pp+0x5afd8] "/Coords"
    //     0x7ccc30: ldr             x2, [x2, #0xfd8]
    // 0x7ccc34: r0 = []=()
    //     0x7ccc34: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ccc38: b               #0x7ccdfc
    // 0x7ccc3c: r16 = Instance_PdfShadingType
    //     0x7ccc3c: add             x16, PP, #0x57, lsl #12  ; [pp+0x579e8] Obj!PdfShadingType@e2ebc1
    //     0x7ccc40: ldr             x16, [x16, #0x9e8]
    // 0x7ccc44: cmp             w0, w16
    // 0x7ccc48: b.ne            #0x7ccdfc
    // 0x7ccc4c: ldur            x0, [fp, #-0x20]
    // 0x7ccc50: r3 = 12
    //     0x7ccc50: movz            x3, #0xc
    // 0x7ccc54: LoadField: r1 = r0->field_33
    //     0x7ccc54: ldur            w1, [x0, #0x33]
    // 0x7ccc58: DecompressPointer r1
    //     0x7ccc58: add             x1, x1, HEAP, lsl #32
    // 0x7ccc5c: LoadField: d0 = r1->field_7
    //     0x7ccc5c: ldur            d0, [x1, #7]
    // 0x7ccc60: LoadField: d1 = r1->field_f
    //     0x7ccc60: ldur            d1, [x1, #0xf]
    // 0x7ccc64: stur            d1, [fp, #-0x50]
    // 0x7ccc68: LoadField: r4 = r0->field_47
    //     0x7ccc68: ldur            w4, [x0, #0x47]
    // 0x7ccc6c: DecompressPointer r4
    //     0x7ccc6c: add             x4, x4, HEAP, lsl #32
    // 0x7ccc70: stur            x4, [fp, #-0x28]
    // 0x7ccc74: cmp             w4, NULL
    // 0x7ccc78: b.eq            #0x7ccfbc
    // 0x7ccc7c: LoadField: r1 = r0->field_37
    //     0x7ccc7c: ldur            w1, [x0, #0x37]
    // 0x7ccc80: DecompressPointer r1
    //     0x7ccc80: add             x1, x1, HEAP, lsl #32
    // 0x7ccc84: LoadField: d2 = r1->field_7
    //     0x7ccc84: ldur            d2, [x1, #7]
    // 0x7ccc88: stur            d2, [fp, #-0x48]
    // 0x7ccc8c: LoadField: d3 = r1->field_f
    //     0x7ccc8c: ldur            d3, [x1, #0xf]
    // 0x7ccc90: stur            d3, [fp, #-0x40]
    // 0x7ccc94: LoadField: r5 = r0->field_4b
    //     0x7ccc94: ldur            w5, [x0, #0x4b]
    // 0x7ccc98: DecompressPointer r5
    //     0x7ccc98: add             x5, x5, HEAP, lsl #32
    // 0x7ccc9c: stur            x5, [fp, #-0x18]
    // 0x7ccca0: cmp             w5, NULL
    // 0x7ccca4: b.eq            #0x7ccfc0
    // 0x7ccca8: r6 = inline_Allocate_Double()
    //     0x7ccca8: ldp             x6, x1, [THR, #0x50]  ; THR::top
    //     0x7cccac: add             x6, x6, #0x10
    //     0x7cccb0: cmp             x1, x6
    //     0x7cccb4: b.ls            #0x7ccfc4
    //     0x7cccb8: str             x6, [THR, #0x50]  ; THR::top
    //     0x7cccbc: sub             x6, x6, #0xf
    //     0x7cccc0: movz            x1, #0xe15c
    //     0x7cccc4: movk            x1, #0x3, lsl #16
    //     0x7cccc8: stur            x1, [x6, #-1]
    // 0x7ccccc: StoreField: r6->field_7 = d0
    //     0x7ccccc: stur            d0, [x6, #7]
    // 0x7cccd0: mov             x2, x3
    // 0x7cccd4: stur            x6, [fp, #-0x10]
    // 0x7cccd8: r1 = Null
    //     0x7cccd8: mov             x1, NULL
    // 0x7cccdc: r0 = AllocateArray()
    //     0x7cccdc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7ccce0: mov             x2, x0
    // 0x7ccce4: ldur            x0, [fp, #-0x10]
    // 0x7ccce8: stur            x2, [fp, #-0x38]
    // 0x7cccec: StoreField: r2->field_f = r0
    //     0x7cccec: stur            w0, [x2, #0xf]
    // 0x7cccf0: ldur            d0, [fp, #-0x50]
    // 0x7cccf4: r0 = inline_Allocate_Double()
    //     0x7cccf4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7cccf8: add             x0, x0, #0x10
    //     0x7cccfc: cmp             x1, x0
    //     0x7ccd00: b.ls            #0x7ccff0
    //     0x7ccd04: str             x0, [THR, #0x50]  ; THR::top
    //     0x7ccd08: sub             x0, x0, #0xf
    //     0x7ccd0c: movz            x1, #0xe15c
    //     0x7ccd10: movk            x1, #0x3, lsl #16
    //     0x7ccd14: stur            x1, [x0, #-1]
    // 0x7ccd18: StoreField: r0->field_7 = d0
    //     0x7ccd18: stur            d0, [x0, #7]
    // 0x7ccd1c: StoreField: r2->field_13 = r0
    //     0x7ccd1c: stur            w0, [x2, #0x13]
    // 0x7ccd20: ldur            x0, [fp, #-0x28]
    // 0x7ccd24: ArrayStore: r2[0] = r0  ; List_4
    //     0x7ccd24: stur            w0, [x2, #0x17]
    // 0x7ccd28: ldur            d0, [fp, #-0x48]
    // 0x7ccd2c: r0 = inline_Allocate_Double()
    //     0x7ccd2c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7ccd30: add             x0, x0, #0x10
    //     0x7ccd34: cmp             x1, x0
    //     0x7ccd38: b.ls            #0x7cd008
    //     0x7ccd3c: str             x0, [THR, #0x50]  ; THR::top
    //     0x7ccd40: sub             x0, x0, #0xf
    //     0x7ccd44: movz            x1, #0xe15c
    //     0x7ccd48: movk            x1, #0x3, lsl #16
    //     0x7ccd4c: stur            x1, [x0, #-1]
    // 0x7ccd50: StoreField: r0->field_7 = d0
    //     0x7ccd50: stur            d0, [x0, #7]
    // 0x7ccd54: StoreField: r2->field_1b = r0
    //     0x7ccd54: stur            w0, [x2, #0x1b]
    // 0x7ccd58: ldur            d0, [fp, #-0x40]
    // 0x7ccd5c: r0 = inline_Allocate_Double()
    //     0x7ccd5c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7ccd60: add             x0, x0, #0x10
    //     0x7ccd64: cmp             x1, x0
    //     0x7ccd68: b.ls            #0x7cd020
    //     0x7ccd6c: str             x0, [THR, #0x50]  ; THR::top
    //     0x7ccd70: sub             x0, x0, #0xf
    //     0x7ccd74: movz            x1, #0xe15c
    //     0x7ccd78: movk            x1, #0x3, lsl #16
    //     0x7ccd7c: stur            x1, [x0, #-1]
    // 0x7ccd80: StoreField: r0->field_7 = d0
    //     0x7ccd80: stur            d0, [x0, #7]
    // 0x7ccd84: StoreField: r2->field_1f = r0
    //     0x7ccd84: stur            w0, [x2, #0x1f]
    // 0x7ccd88: ldur            x0, [fp, #-0x18]
    // 0x7ccd8c: StoreField: r2->field_23 = r0
    //     0x7ccd8c: stur            w0, [x2, #0x23]
    // 0x7ccd90: r1 = <num>
    //     0x7ccd90: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0x7ccd94: r0 = AllocateGrowableArray()
    //     0x7ccd94: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7ccd98: mov             x1, x0
    // 0x7ccd9c: ldur            x0, [fp, #-0x38]
    // 0x7ccda0: StoreField: r1->field_f = r0
    //     0x7ccda0: stur            w0, [x1, #0xf]
    // 0x7ccda4: r0 = 12
    //     0x7ccda4: movz            x0, #0xc
    // 0x7ccda8: StoreField: r1->field_b = r0
    //     0x7ccda8: stur            w0, [x1, #0xb]
    // 0x7ccdac: r0 = fromNum()
    //     0x7ccdac: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0x7ccdb0: ldur            x2, [fp, #-8]
    // 0x7ccdb4: mov             x3, x0
    // 0x7ccdb8: r1 = Null
    //     0x7ccdb8: mov             x1, NULL
    // 0x7ccdbc: stur            x3, [fp, #-0x10]
    // 0x7ccdc0: cmp             w2, NULL
    // 0x7ccdc4: b.eq            #0x7ccde8
    // 0x7ccdc8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ccdc8: ldur            w4, [x2, #0x17]
    // 0x7ccdcc: DecompressPointer r4
    //     0x7ccdcc: add             x4, x4, HEAP, lsl #32
    // 0x7ccdd0: r8 = X0 bound PdfDataType
    //     0x7ccdd0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ccdd4: ldr             x8, [x8, #0x6d8]
    // 0x7ccdd8: LoadField: r9 = r4->field_7
    //     0x7ccdd8: ldur            x9, [x4, #7]
    // 0x7ccddc: r3 = Null
    //     0x7ccddc: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5afe0] Null
    //     0x7ccde0: ldr             x3, [x3, #0xfe0]
    // 0x7ccde4: blr             x9
    // 0x7ccde8: ldur            x1, [fp, #-0x30]
    // 0x7ccdec: ldur            x3, [fp, #-0x10]
    // 0x7ccdf0: r2 = "/Coords"
    //     0x7ccdf0: add             x2, PP, #0x5a, lsl #12  ; [pp+0x5afd8] "/Coords"
    //     0x7ccdf4: ldr             x2, [x2, #0xfd8]
    // 0x7ccdf8: r0 = []=()
    //     0x7ccdf8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ccdfc: ldur            x0, [fp, #-0x20]
    // 0x7cce00: r0 = PdfBool()
    //     0x7cce00: bl              #0x7cb5b8  ; AllocatePdfBoolStub -> PdfBool (size=0xc)
    // 0x7cce04: mov             x1, x0
    // 0x7cce08: r0 = true
    //     0x7cce08: add             x0, NULL, #0x20  ; true
    // 0x7cce0c: stur            x1, [fp, #-0x10]
    // 0x7cce10: StoreField: r1->field_7 = r0
    //     0x7cce10: stur            w0, [x1, #7]
    // 0x7cce14: r0 = PdfBool()
    //     0x7cce14: bl              #0x7cb5b8  ; AllocatePdfBoolStub -> PdfBool (size=0xc)
    // 0x7cce18: mov             x3, x0
    // 0x7cce1c: r0 = true
    //     0x7cce1c: add             x0, NULL, #0x20  ; true
    // 0x7cce20: stur            x3, [fp, #-0x18]
    // 0x7cce24: StoreField: r3->field_7 = r0
    //     0x7cce24: stur            w0, [x3, #7]
    // 0x7cce28: r1 = Null
    //     0x7cce28: mov             x1, NULL
    // 0x7cce2c: r2 = 4
    //     0x7cce2c: movz            x2, #0x4
    // 0x7cce30: r0 = AllocateArray()
    //     0x7cce30: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7cce34: mov             x2, x0
    // 0x7cce38: ldur            x0, [fp, #-0x10]
    // 0x7cce3c: stur            x2, [fp, #-0x28]
    // 0x7cce40: StoreField: r2->field_f = r0
    //     0x7cce40: stur            w0, [x2, #0xf]
    // 0x7cce44: ldur            x0, [fp, #-0x18]
    // 0x7cce48: StoreField: r2->field_13 = r0
    //     0x7cce48: stur            w0, [x2, #0x13]
    // 0x7cce4c: r1 = <PdfBool>
    //     0x7cce4c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aff0] TypeArguments: <PdfBool>
    //     0x7cce50: ldr             x1, [x1, #0xff0]
    // 0x7cce54: r0 = AllocateGrowableArray()
    //     0x7cce54: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7cce58: mov             x2, x0
    // 0x7cce5c: ldur            x0, [fp, #-0x28]
    // 0x7cce60: stur            x2, [fp, #-0x10]
    // 0x7cce64: StoreField: r2->field_f = r0
    //     0x7cce64: stur            w0, [x2, #0xf]
    // 0x7cce68: r0 = 4
    //     0x7cce68: movz            x0, #0x4
    // 0x7cce6c: StoreField: r2->field_b = r0
    //     0x7cce6c: stur            w0, [x2, #0xb]
    // 0x7cce70: r1 = <PdfBool>
    //     0x7cce70: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aff0] TypeArguments: <PdfBool>
    //     0x7cce74: ldr             x1, [x1, #0xff0]
    // 0x7cce78: r0 = PdfArray()
    //     0x7cce78: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0x7cce7c: stur            x0, [fp, #-0x18]
    // 0x7cce80: ldur            x16, [fp, #-0x10]
    // 0x7cce84: str             x16, [SP]
    // 0x7cce88: mov             x1, x0
    // 0x7cce8c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7cce8c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7cce90: r0 = PdfArray()
    //     0x7cce90: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0x7cce94: ldur            x0, [fp, #-0x18]
    // 0x7cce98: ldur            x2, [fp, #-8]
    // 0x7cce9c: r1 = Null
    //     0x7cce9c: mov             x1, NULL
    // 0x7ccea0: cmp             w2, NULL
    // 0x7ccea4: b.eq            #0x7ccec8
    // 0x7ccea8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ccea8: ldur            w4, [x2, #0x17]
    // 0x7cceac: DecompressPointer r4
    //     0x7cceac: add             x4, x4, HEAP, lsl #32
    // 0x7cceb0: r8 = X0 bound PdfDataType
    //     0x7cceb0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7cceb4: ldr             x8, [x8, #0x6d8]
    // 0x7cceb8: LoadField: r9 = r4->field_7
    //     0x7cceb8: ldur            x9, [x4, #7]
    // 0x7ccebc: r3 = Null
    //     0x7ccebc: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5aff8] Null
    //     0x7ccec0: ldr             x3, [x3, #0xff8]
    // 0x7ccec4: blr             x9
    // 0x7ccec8: ldur            x1, [fp, #-0x30]
    // 0x7ccecc: ldur            x3, [fp, #-0x18]
    // 0x7cced0: r2 = "/Extend"
    //     0x7cced0: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b008] "/Extend"
    //     0x7cced4: ldr             x2, [x2, #8]
    // 0x7cced8: r0 = []=()
    //     0x7cced8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ccedc: ldur            x0, [fp, #-0x20]
    // 0x7ccee0: LoadField: r1 = r0->field_2f
    //     0x7ccee0: ldur            w1, [x0, #0x2f]
    // 0x7ccee4: DecompressPointer r1
    //     0x7ccee4: add             x1, x1, HEAP, lsl #32
    // 0x7ccee8: r0 = ref()
    //     0x7ccee8: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7cceec: ldur            x2, [fp, #-8]
    // 0x7ccef0: mov             x3, x0
    // 0x7ccef4: r1 = Null
    //     0x7ccef4: mov             x1, NULL
    // 0x7ccef8: stur            x3, [fp, #-8]
    // 0x7ccefc: cmp             w2, NULL
    // 0x7ccf00: b.eq            #0x7ccf24
    // 0x7ccf04: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ccf04: ldur            w4, [x2, #0x17]
    // 0x7ccf08: DecompressPointer r4
    //     0x7ccf08: add             x4, x4, HEAP, lsl #32
    // 0x7ccf0c: r8 = X0 bound PdfDataType
    //     0x7ccf0c: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7ccf10: ldr             x8, [x8, #0x6d8]
    // 0x7ccf14: LoadField: r9 = r4->field_7
    //     0x7ccf14: ldur            x9, [x4, #7]
    // 0x7ccf18: r3 = Null
    //     0x7ccf18: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b010] Null
    //     0x7ccf1c: ldr             x3, [x3, #0x10]
    // 0x7ccf20: blr             x9
    // 0x7ccf24: ldur            x1, [fp, #-0x30]
    // 0x7ccf28: ldur            x3, [fp, #-8]
    // 0x7ccf2c: r2 = "/Function"
    //     0x7ccf2c: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b020] "/Function"
    //     0x7ccf30: ldr             x2, [x2, #0x20]
    // 0x7ccf34: r0 = []=()
    //     0x7ccf34: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7ccf38: r0 = Null
    //     0x7ccf38: mov             x0, NULL
    // 0x7ccf3c: LeaveFrame
    //     0x7ccf3c: mov             SP, fp
    //     0x7ccf40: ldp             fp, lr, [SP], #0x10
    // 0x7ccf44: ret
    //     0x7ccf44: ret             
    // 0x7ccf48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ccf48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ccf4c: b               #0x7cc958
    // 0x7ccf50: stp             q2, q3, [SP, #-0x20]!
    // 0x7ccf54: stp             q0, q1, [SP, #-0x20]!
    // 0x7ccf58: stp             x0, x3, [SP, #-0x10]!
    // 0x7ccf5c: r0 = AllocateDouble()
    //     0x7ccf5c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7ccf60: mov             x4, x0
    // 0x7ccf64: ldp             x0, x3, [SP], #0x10
    // 0x7ccf68: ldp             q0, q1, [SP], #0x20
    // 0x7ccf6c: ldp             q2, q3, [SP], #0x20
    // 0x7ccf70: b               #0x7ccb18
    // 0x7ccf74: SaveReg d0
    //     0x7ccf74: str             q0, [SP, #-0x10]!
    // 0x7ccf78: SaveReg r2
    //     0x7ccf78: str             x2, [SP, #-8]!
    // 0x7ccf7c: r0 = AllocateDouble()
    //     0x7ccf7c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7ccf80: RestoreReg r2
    //     0x7ccf80: ldr             x2, [SP], #8
    // 0x7ccf84: RestoreReg d0
    //     0x7ccf84: ldr             q0, [SP], #0x10
    // 0x7ccf88: b               #0x7ccb64
    // 0x7ccf8c: SaveReg d0
    //     0x7ccf8c: str             q0, [SP, #-0x10]!
    // 0x7ccf90: SaveReg r2
    //     0x7ccf90: str             x2, [SP, #-8]!
    // 0x7ccf94: r0 = AllocateDouble()
    //     0x7ccf94: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7ccf98: RestoreReg r2
    //     0x7ccf98: ldr             x2, [SP], #8
    // 0x7ccf9c: RestoreReg d0
    //     0x7ccf9c: ldr             q0, [SP], #0x10
    // 0x7ccfa0: b               #0x7ccb94
    // 0x7ccfa4: SaveReg d0
    //     0x7ccfa4: str             q0, [SP, #-0x10]!
    // 0x7ccfa8: SaveReg r2
    //     0x7ccfa8: str             x2, [SP, #-8]!
    // 0x7ccfac: r0 = AllocateDouble()
    //     0x7ccfac: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7ccfb0: RestoreReg r2
    //     0x7ccfb0: ldr             x2, [SP], #8
    // 0x7ccfb4: RestoreReg d0
    //     0x7ccfb4: ldr             q0, [SP], #0x10
    // 0x7ccfb8: b               #0x7ccbc4
    // 0x7ccfbc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7ccfbc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7ccfc0: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7ccfc0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7ccfc4: stp             q2, q3, [SP, #-0x20]!
    // 0x7ccfc8: stp             q0, q1, [SP, #-0x20]!
    // 0x7ccfcc: stp             x4, x5, [SP, #-0x10]!
    // 0x7ccfd0: stp             x0, x3, [SP, #-0x10]!
    // 0x7ccfd4: r0 = AllocateDouble()
    //     0x7ccfd4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7ccfd8: mov             x6, x0
    // 0x7ccfdc: ldp             x0, x3, [SP], #0x10
    // 0x7ccfe0: ldp             x4, x5, [SP], #0x10
    // 0x7ccfe4: ldp             q0, q1, [SP], #0x20
    // 0x7ccfe8: ldp             q2, q3, [SP], #0x20
    // 0x7ccfec: b               #0x7ccccc
    // 0x7ccff0: SaveReg d0
    //     0x7ccff0: str             q0, [SP, #-0x10]!
    // 0x7ccff4: SaveReg r2
    //     0x7ccff4: str             x2, [SP, #-8]!
    // 0x7ccff8: r0 = AllocateDouble()
    //     0x7ccff8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7ccffc: RestoreReg r2
    //     0x7ccffc: ldr             x2, [SP], #8
    // 0x7cd000: RestoreReg d0
    //     0x7cd000: ldr             q0, [SP], #0x10
    // 0x7cd004: b               #0x7ccd18
    // 0x7cd008: SaveReg d0
    //     0x7cd008: str             q0, [SP, #-0x10]!
    // 0x7cd00c: SaveReg r2
    //     0x7cd00c: str             x2, [SP, #-8]!
    // 0x7cd010: r0 = AllocateDouble()
    //     0x7cd010: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7cd014: RestoreReg r2
    //     0x7cd014: ldr             x2, [SP], #8
    // 0x7cd018: RestoreReg d0
    //     0x7cd018: ldr             q0, [SP], #0x10
    // 0x7cd01c: b               #0x7ccd50
    // 0x7cd020: SaveReg d0
    //     0x7cd020: str             q0, [SP, #-0x10]!
    // 0x7cd024: SaveReg r2
    //     0x7cd024: str             x2, [SP, #-8]!
    // 0x7cd028: r0 = AllocateDouble()
    //     0x7cd028: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7cd02c: RestoreReg r2
    //     0x7cd02c: ldr             x2, [SP], #8
    // 0x7cd030: RestoreReg d0
    //     0x7cd030: ldr             q0, [SP], #0x10
    // 0x7cd034: b               #0x7ccd80
  }
  _ PdfShading(/* No info */) {
    // ** addr: 0xeaa4b4, size: 0x1d4
    // 0xeaa4b4: EnterFrame
    //     0xeaa4b4: stp             fp, lr, [SP, #-0x10]!
    //     0xeaa4b8: mov             fp, SP
    // 0xeaa4bc: AllocStack(0x18)
    //     0xeaa4bc: sub             SP, SP, #0x18
    // 0xeaa4c0: SetupParameters(PdfShading this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r5, fp-0x10 */, dynamic _ /* r5 => r2 */, dynamic _ /* r6 => r0 */, dynamic _ /* r7 => r1 */, {dynamic radius0 = Null /* r9 */, dynamic radius1 = Null /* r7 */})
    //     0xeaa4c0: mov             x0, x6
    //     0xeaa4c4: mov             x6, x1
    //     0xeaa4c8: stur            x2, [fp, #-0x10]
    //     0xeaa4cc: mov             x16, x5
    //     0xeaa4d0: mov             x5, x2
    //     0xeaa4d4: mov             x2, x16
    //     0xeaa4d8: stur            x1, [fp, #-8]
    //     0xeaa4dc: mov             x1, x7
    //     0xeaa4e0: ldur            w7, [x4, #0x13]
    //     0xeaa4e4: ldur            w8, [x4, #0x1f]
    //     0xeaa4e8: add             x8, x8, HEAP, lsl #32
    //     0xeaa4ec: add             x16, PP, #0x57, lsl #12  ; [pp+0x579f8] "radius0"
    //     0xeaa4f0: ldr             x16, [x16, #0x9f8]
    //     0xeaa4f4: cmp             w8, w16
    //     0xeaa4f8: b.ne            #0xeaa51c
    //     0xeaa4fc: ldur            w8, [x4, #0x23]
    //     0xeaa500: add             x8, x8, HEAP, lsl #32
    //     0xeaa504: sub             w9, w7, w8
    //     0xeaa508: add             x8, fp, w9, sxtw #2
    //     0xeaa50c: ldr             x8, [x8, #8]
    //     0xeaa510: mov             x9, x8
    //     0xeaa514: movz            x8, #0x1
    //     0xeaa518: b               #0xeaa524
    //     0xeaa51c: mov             x9, NULL
    //     0xeaa520: movz            x8, #0
    //     0xeaa524: lsl             x10, x8, #1
    //     0xeaa528: lsl             w8, w10, #1
    //     0xeaa52c: add             w10, w8, #8
    //     0xeaa530: add             x16, x4, w10, sxtw #1
    //     0xeaa534: ldur            w11, [x16, #0xf]
    //     0xeaa538: add             x11, x11, HEAP, lsl #32
    //     0xeaa53c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57a00] "radius1"
    //     0xeaa540: ldr             x16, [x16, #0xa00]
    //     0xeaa544: cmp             w11, w16
    //     0xeaa548: b.ne            #0xeaa56c
    //     0xeaa54c: add             w10, w8, #0xa
    //     0xeaa550: add             x16, x4, w10, sxtw #1
    //     0xeaa554: ldur            w8, [x16, #0xf]
    //     0xeaa558: add             x8, x8, HEAP, lsl #32
    //     0xeaa55c: sub             w4, w7, w8
    //     0xeaa560: add             x7, fp, w4, sxtw #2
    //     0xeaa564: ldr             x7, [x7, #8]
    //     0xeaa568: b               #0xeaa570
    //     0xeaa56c: mov             x7, NULL
    //     0xeaa570: add             x4, NULL, #0x20  ; true
    // 0xeaa570: r4 = true
    // 0xeaa574: CheckStackOverflow
    //     0xeaa574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa578: cmp             SP, x16
    //     0xeaa57c: b.ls            #0xeaa680
    // 0xeaa580: StoreField: r6->field_2b = r0
    //     0xeaa580: stur            w0, [x6, #0x2b]
    //     0xeaa584: ldurb           w16, [x6, #-1]
    //     0xeaa588: ldurb           w17, [x0, #-1]
    //     0xeaa58c: and             x16, x17, x16, lsr #2
    //     0xeaa590: tst             x16, HEAP, lsr #32
    //     0xeaa594: b.eq            #0xeaa59c
    //     0xeaa598: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xeaa59c: mov             x0, x2
    // 0xeaa5a0: StoreField: r6->field_2f = r0
    //     0xeaa5a0: stur            w0, [x6, #0x2f]
    //     0xeaa5a4: ldurb           w16, [x6, #-1]
    //     0xeaa5a8: ldurb           w17, [x0, #-1]
    //     0xeaa5ac: and             x16, x17, x16, lsr #2
    //     0xeaa5b0: tst             x16, HEAP, lsr #32
    //     0xeaa5b4: b.eq            #0xeaa5bc
    //     0xeaa5b8: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xeaa5bc: mov             x0, x1
    // 0xeaa5c0: StoreField: r6->field_33 = r0
    //     0xeaa5c0: stur            w0, [x6, #0x33]
    //     0xeaa5c4: ldurb           w16, [x6, #-1]
    //     0xeaa5c8: ldurb           w17, [x0, #-1]
    //     0xeaa5cc: and             x16, x17, x16, lsr #2
    //     0xeaa5d0: tst             x16, HEAP, lsr #32
    //     0xeaa5d4: b.eq            #0xeaa5dc
    //     0xeaa5d8: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xeaa5dc: mov             x0, x3
    // 0xeaa5e0: StoreField: r6->field_37 = r0
    //     0xeaa5e0: stur            w0, [x6, #0x37]
    //     0xeaa5e4: ldurb           w16, [x6, #-1]
    //     0xeaa5e8: ldurb           w17, [x0, #-1]
    //     0xeaa5ec: and             x16, x17, x16, lsr #2
    //     0xeaa5f0: tst             x16, HEAP, lsr #32
    //     0xeaa5f4: b.eq            #0xeaa5fc
    //     0xeaa5f8: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xeaa5fc: mov             x0, x9
    // 0xeaa600: StoreField: r6->field_47 = r0
    //     0xeaa600: stur            w0, [x6, #0x47]
    //     0xeaa604: ldurb           w16, [x6, #-1]
    //     0xeaa608: ldurb           w17, [x0, #-1]
    //     0xeaa60c: and             x16, x17, x16, lsr #2
    //     0xeaa610: tst             x16, HEAP, lsr #32
    //     0xeaa614: b.eq            #0xeaa61c
    //     0xeaa618: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xeaa61c: mov             x0, x7
    // 0xeaa620: StoreField: r6->field_4b = r0
    //     0xeaa620: stur            w0, [x6, #0x4b]
    //     0xeaa624: ldurb           w16, [x6, #-1]
    //     0xeaa628: ldurb           w17, [x0, #-1]
    //     0xeaa62c: and             x16, x17, x16, lsr #2
    //     0xeaa630: tst             x16, HEAP, lsr #32
    //     0xeaa634: b.eq            #0xeaa63c
    //     0xeaa638: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xeaa63c: StoreField: r6->field_3f = r4
    //     0xeaa63c: stur            w4, [x6, #0x3f]
    // 0xeaa640: StoreField: r6->field_43 = r4
    //     0xeaa640: stur            w4, [x6, #0x43]
    // 0xeaa644: r1 = <PdfDataType>
    //     0xeaa644: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xeaa648: ldr             x1, [x1, #0x4c8]
    // 0xeaa64c: r0 = PdfDict()
    //     0xeaa64c: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xeaa650: mov             x1, x0
    // 0xeaa654: stur            x0, [fp, #-0x18]
    // 0xeaa658: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xeaa658: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xeaa65c: r0 = PdfDict()
    //     0xeaa65c: bl              #0x7b5d6c  ; [package:pdf/src/pdf/format/dict.dart] PdfDict::PdfDict
    // 0xeaa660: ldur            x1, [fp, #-8]
    // 0xeaa664: ldur            x2, [fp, #-0x10]
    // 0xeaa668: ldur            x3, [fp, #-0x18]
    // 0xeaa66c: r0 = PdfObject()
    //     0xeaa66c: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xeaa670: r0 = Null
    //     0xeaa670: mov             x0, NULL
    // 0xeaa674: LeaveFrame
    //     0xeaa674: mov             SP, fp
    //     0xeaa678: ldp             fp, lr, [SP], #0x10
    // 0xeaa67c: ret
    //     0xeaa67c: ret             
    // 0xeaa680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa680: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa684: b               #0xeaa580
  }
}

// class id: 6809, size: 0x14, field offset: 0x14
enum PdfShadingType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4dc08, size: 0x64
    // 0xc4dc08: EnterFrame
    //     0xc4dc08: stp             fp, lr, [SP, #-0x10]!
    //     0xc4dc0c: mov             fp, SP
    // 0xc4dc10: AllocStack(0x10)
    //     0xc4dc10: sub             SP, SP, #0x10
    // 0xc4dc14: SetupParameters(PdfShadingType this /* r1 => r0, fp-0x8 */)
    //     0xc4dc14: mov             x0, x1
    //     0xc4dc18: stur            x1, [fp, #-8]
    // 0xc4dc1c: CheckStackOverflow
    //     0xc4dc1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4dc20: cmp             SP, x16
    //     0xc4dc24: b.ls            #0xc4dc64
    // 0xc4dc28: r1 = Null
    //     0xc4dc28: mov             x1, NULL
    // 0xc4dc2c: r2 = 4
    //     0xc4dc2c: movz            x2, #0x4
    // 0xc4dc30: r0 = AllocateArray()
    //     0xc4dc30: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4dc34: r16 = "PdfShadingType."
    //     0xc4dc34: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b028] "PdfShadingType."
    //     0xc4dc38: ldr             x16, [x16, #0x28]
    // 0xc4dc3c: StoreField: r0->field_f = r16
    //     0xc4dc3c: stur            w16, [x0, #0xf]
    // 0xc4dc40: ldur            x1, [fp, #-8]
    // 0xc4dc44: LoadField: r2 = r1->field_f
    //     0xc4dc44: ldur            w2, [x1, #0xf]
    // 0xc4dc48: DecompressPointer r2
    //     0xc4dc48: add             x2, x2, HEAP, lsl #32
    // 0xc4dc4c: StoreField: r0->field_13 = r2
    //     0xc4dc4c: stur            w2, [x0, #0x13]
    // 0xc4dc50: str             x0, [SP]
    // 0xc4dc54: r0 = _interpolate()
    //     0xc4dc54: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4dc58: LeaveFrame
    //     0xc4dc58: mov             SP, fp
    //     0xc4dc5c: ldp             fp, lr, [SP], #0x10
    // 0xc4dc60: ret
    //     0xc4dc60: ret             
    // 0xc4dc64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4dc64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4dc68: b               #0xc4dc28
  }
}
