// lib: , url: package:pdf/src/pdf/obj/smask.dart

// class id: 1050818, size: 0x8
class :: {
}

// class id: 860, size: 0x18, field offset: 0x8
class PdfSoftMask extends Object {

  late PdfGraphicXObject _mask; // offset: 0xc

  _ output(/* No info */) {
    // ** addr: 0x7b5ba8, size: 0xe8
    // 0x7b5ba8: EnterFrame
    //     0x7b5ba8: stp             fp, lr, [SP, #-0x10]!
    //     0x7b5bac: mov             fp, SP
    // 0x7b5bb0: AllocStack(0x20)
    //     0x7b5bb0: sub             SP, SP, #0x20
    // 0x7b5bb4: SetupParameters(PdfSoftMask this /* r1 => r0, fp-0x8 */)
    //     0x7b5bb4: mov             x0, x1
    //     0x7b5bb8: stur            x1, [fp, #-8]
    // 0x7b5bbc: CheckStackOverflow
    //     0x7b5bbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b5bc0: cmp             SP, x16
    //     0x7b5bc4: b.ls            #0x7b5c7c
    // 0x7b5bc8: r1 = Null
    //     0x7b5bc8: mov             x1, NULL
    // 0x7b5bcc: r2 = 8
    //     0x7b5bcc: movz            x2, #0x8
    // 0x7b5bd0: r0 = AllocateArray()
    //     0x7b5bd0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b5bd4: stur            x0, [fp, #-0x10]
    // 0x7b5bd8: r16 = "/S"
    //     0x7b5bd8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eef0] "/S"
    //     0x7b5bdc: ldr             x16, [x16, #0xef0]
    // 0x7b5be0: StoreField: r0->field_f = r16
    //     0x7b5be0: stur            w16, [x0, #0xf]
    // 0x7b5be4: r16 = Instance_PdfName
    //     0x7b5be4: add             x16, PP, #0x47, lsl #12  ; [pp+0x47810] Obj!PdfName@e0c7e1
    //     0x7b5be8: ldr             x16, [x16, #0x810]
    // 0x7b5bec: StoreField: r0->field_13 = r16
    //     0x7b5bec: stur            w16, [x0, #0x13]
    // 0x7b5bf0: r16 = "/G"
    //     0x7b5bf0: add             x16, PP, #0x47, lsl #12  ; [pp+0x47818] "/G"
    //     0x7b5bf4: ldr             x16, [x16, #0x818]
    // 0x7b5bf8: ArrayStore: r0[0] = r16  ; List_4
    //     0x7b5bf8: stur            w16, [x0, #0x17]
    // 0x7b5bfc: ldur            x1, [fp, #-8]
    // 0x7b5c00: LoadField: r2 = r1->field_b
    //     0x7b5c00: ldur            w2, [x1, #0xb]
    // 0x7b5c04: DecompressPointer r2
    //     0x7b5c04: add             x2, x2, HEAP, lsl #32
    // 0x7b5c08: r16 = Sentinel
    //     0x7b5c08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b5c0c: cmp             w2, w16
    // 0x7b5c10: b.eq            #0x7b5c84
    // 0x7b5c14: mov             x1, x2
    // 0x7b5c18: r0 = ref()
    //     0x7b5c18: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7b5c1c: ldur            x1, [fp, #-0x10]
    // 0x7b5c20: ArrayStore: r1[3] = r0  ; List_4
    //     0x7b5c20: add             x25, x1, #0x1b
    //     0x7b5c24: str             w0, [x25]
    //     0x7b5c28: tbz             w0, #0, #0x7b5c44
    //     0x7b5c2c: ldurb           w16, [x1, #-1]
    //     0x7b5c30: ldurb           w17, [x0, #-1]
    //     0x7b5c34: and             x16, x17, x16, lsr #2
    //     0x7b5c38: tst             x16, HEAP, lsr #32
    //     0x7b5c3c: b.eq            #0x7b5c44
    //     0x7b5c40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b5c44: r16 = <String, PdfDataType>
    //     0x7b5c44: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0x7b5c48: ldr             x16, [x16, #0x820]
    // 0x7b5c4c: ldur            lr, [fp, #-0x10]
    // 0x7b5c50: stp             lr, x16, [SP]
    // 0x7b5c54: r0 = Map._fromLiteral()
    //     0x7b5c54: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b5c58: r1 = <PdfDataType>
    //     0x7b5c58: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b5c5c: ldr             x1, [x1, #0x4c8]
    // 0x7b5c60: stur            x0, [fp, #-8]
    // 0x7b5c64: r0 = PdfDict()
    //     0x7b5c64: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7b5c68: ldur            x1, [fp, #-8]
    // 0x7b5c6c: StoreField: r0->field_b = r1
    //     0x7b5c6c: stur            w1, [x0, #0xb]
    // 0x7b5c70: LeaveFrame
    //     0x7b5c70: mov             SP, fp
    //     0x7b5c74: ldp             fp, lr, [SP], #0x10
    // 0x7b5c78: ret
    //     0x7b5c78: ret             
    // 0x7b5c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b5c7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b5c80: b               #0x7b5bc8
    // 0x7b5c84: r9 = _mask
    //     0x7b5c84: add             x9, PP, #0x47, lsl #12  ; [pp+0x47820] Field <PdfSoftMask._mask@2241026049>: late (offset: 0xc)
    //     0x7b5c88: ldr             x9, [x9, #0x820]
    // 0x7b5c8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b5c8c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xc357ec, size: 0x3c
    // 0xc357ec: EnterFrame
    //     0xc357ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc357f0: mov             fp, SP
    // 0xc357f4: AllocStack(0x8)
    //     0xc357f4: sub             SP, SP, #8
    // 0xc357f8: CheckStackOverflow
    //     0xc357f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc357fc: cmp             SP, x16
    //     0xc35800: b.ls            #0xc35820
    // 0xc35804: r16 = PdfSoftMask
    //     0xc35804: add             x16, PP, #0x47, lsl #12  ; [pp+0x47780] Type: PdfSoftMask
    //     0xc35808: ldr             x16, [x16, #0x780]
    // 0xc3580c: str             x16, [SP]
    // 0xc35810: r0 = toString()
    //     0xc35810: bl              #0xc46764  ; [dart:core] _AbstractType::toString
    // 0xc35814: LeaveFrame
    //     0xc35814: mov             SP, fp
    //     0xc35818: ldp             fp, lr, [SP], #0x10
    // 0xc3581c: ret
    //     0xc3581c: ret             
    // 0xc35820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc35820: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35824: b               #0xc35804
  }
  _ PdfSoftMask(/* No info */) {
    // ** addr: 0xe46b00, size: 0x2f0
    // 0xe46b00: EnterFrame
    //     0xe46b00: stp             fp, lr, [SP, #-0x10]!
    //     0xe46b04: mov             fp, SP
    // 0xe46b08: AllocStack(0x38)
    //     0xe46b08: sub             SP, SP, #0x38
    // 0xe46b0c: r0 = Sentinel
    //     0xe46b0c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe46b10: mov             x4, x1
    // 0xe46b14: stur            x1, [fp, #-8]
    // 0xe46b18: stur            x2, [fp, #-0x10]
    // 0xe46b1c: stur            x3, [fp, #-0x18]
    // 0xe46b20: CheckStackOverflow
    //     0xe46b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe46b24: cmp             SP, x16
    //     0xe46b28: b.ls            #0xe46d80
    // 0xe46b2c: StoreField: r4->field_b = r0
    //     0xe46b2c: stur            w0, [x4, #0xb]
    // 0xe46b30: mov             x0, x2
    // 0xe46b34: StoreField: r4->field_7 = r0
    //     0xe46b34: stur            w0, [x4, #7]
    //     0xe46b38: ldurb           w16, [x4, #-1]
    //     0xe46b3c: ldurb           w17, [x0, #-1]
    //     0xe46b40: and             x16, x17, x16, lsr #2
    //     0xe46b44: tst             x16, HEAP, lsr #32
    //     0xe46b48: b.eq            #0xe46b50
    //     0xe46b4c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe46b50: r1 = <PdfDict<PdfDataType>>
    //     0xe46b50: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe46b54: ldr             x1, [x1, #0x758]
    // 0xe46b58: r0 = PdfGraphicXObject()
    //     0xe46b58: bl              #0xe472ec  ; AllocatePdfGraphicXObjectStub -> PdfGraphicXObject (size=0x50)
    // 0xe46b5c: mov             x1, x0
    // 0xe46b60: ldur            x2, [fp, #-0x10]
    // 0xe46b64: stur            x0, [fp, #-0x10]
    // 0xe46b68: r0 = _PdfGraphicXObject&PdfXObject&PdfGraphicStream()
    //     0xe46b68: bl              #0xe46f4c  ; [package:pdf/src/pdf/obj/graphic_stream.dart] _PdfGraphicXObject&PdfXObject&PdfGraphicStream::_PdfGraphicXObject&PdfXObject&PdfGraphicStream
    // 0xe46b6c: ldur            x0, [fp, #-0x10]
    // 0xe46b70: ldur            x3, [fp, #-8]
    // 0xe46b74: StoreField: r3->field_b = r0
    //     0xe46b74: stur            w0, [x3, #0xb]
    //     0xe46b78: ldurb           w16, [x3, #-1]
    //     0xe46b7c: ldurb           w17, [x0, #-1]
    //     0xe46b80: and             x16, x17, x16, lsr #2
    //     0xe46b84: tst             x16, HEAP, lsr #32
    //     0xe46b88: b.eq            #0xe46b90
    //     0xe46b8c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe46b90: ldur            x0, [fp, #-0x10]
    // 0xe46b94: LoadField: r4 = r0->field_1b
    //     0xe46b94: ldur            w4, [x0, #0x1b]
    // 0xe46b98: DecompressPointer r4
    //     0xe46b98: add             x4, x4, HEAP, lsl #32
    // 0xe46b9c: ldur            x0, [fp, #-0x18]
    // 0xe46ba0: stur            x4, [fp, #-0x20]
    // 0xe46ba4: LoadField: d0 = r0->field_7
    //     0xe46ba4: ldur            d0, [x0, #7]
    // 0xe46ba8: LoadField: d1 = r0->field_f
    //     0xe46ba8: ldur            d1, [x0, #0xf]
    // 0xe46bac: stur            d1, [fp, #-0x38]
    // 0xe46bb0: ArrayLoad: d2 = r0[0]  ; List_8
    //     0xe46bb0: ldur            d2, [x0, #0x17]
    // 0xe46bb4: stur            d2, [fp, #-0x30]
    // 0xe46bb8: LoadField: d3 = r0->field_1f
    //     0xe46bb8: ldur            d3, [x0, #0x1f]
    // 0xe46bbc: stur            d3, [fp, #-0x28]
    // 0xe46bc0: r0 = inline_Allocate_Double()
    //     0xe46bc0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe46bc4: add             x0, x0, #0x10
    //     0xe46bc8: cmp             x1, x0
    //     0xe46bcc: b.ls            #0xe46d88
    //     0xe46bd0: str             x0, [THR, #0x50]  ; THR::top
    //     0xe46bd4: sub             x0, x0, #0xf
    //     0xe46bd8: movz            x1, #0xe15c
    //     0xe46bdc: movk            x1, #0x3, lsl #16
    //     0xe46be0: stur            x1, [x0, #-1]
    // 0xe46be4: StoreField: r0->field_7 = d0
    //     0xe46be4: stur            d0, [x0, #7]
    // 0xe46be8: stur            x0, [fp, #-0x10]
    // 0xe46bec: r1 = Null
    //     0xe46bec: mov             x1, NULL
    // 0xe46bf0: r2 = 8
    //     0xe46bf0: movz            x2, #0x8
    // 0xe46bf4: r0 = AllocateArray()
    //     0xe46bf4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe46bf8: mov             x2, x0
    // 0xe46bfc: ldur            x0, [fp, #-0x10]
    // 0xe46c00: stur            x2, [fp, #-0x18]
    // 0xe46c04: StoreField: r2->field_f = r0
    //     0xe46c04: stur            w0, [x2, #0xf]
    // 0xe46c08: ldur            d0, [fp, #-0x38]
    // 0xe46c0c: r0 = inline_Allocate_Double()
    //     0xe46c0c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe46c10: add             x0, x0, #0x10
    //     0xe46c14: cmp             x1, x0
    //     0xe46c18: b.ls            #0xe46da8
    //     0xe46c1c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe46c20: sub             x0, x0, #0xf
    //     0xe46c24: movz            x1, #0xe15c
    //     0xe46c28: movk            x1, #0x3, lsl #16
    //     0xe46c2c: stur            x1, [x0, #-1]
    // 0xe46c30: StoreField: r0->field_7 = d0
    //     0xe46c30: stur            d0, [x0, #7]
    // 0xe46c34: StoreField: r2->field_13 = r0
    //     0xe46c34: stur            w0, [x2, #0x13]
    // 0xe46c38: ldur            d0, [fp, #-0x30]
    // 0xe46c3c: r0 = inline_Allocate_Double()
    //     0xe46c3c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe46c40: add             x0, x0, #0x10
    //     0xe46c44: cmp             x1, x0
    //     0xe46c48: b.ls            #0xe46dc0
    //     0xe46c4c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe46c50: sub             x0, x0, #0xf
    //     0xe46c54: movz            x1, #0xe15c
    //     0xe46c58: movk            x1, #0x3, lsl #16
    //     0xe46c5c: stur            x1, [x0, #-1]
    // 0xe46c60: StoreField: r0->field_7 = d0
    //     0xe46c60: stur            d0, [x0, #7]
    // 0xe46c64: ArrayStore: r2[0] = r0  ; List_4
    //     0xe46c64: stur            w0, [x2, #0x17]
    // 0xe46c68: ldur            d0, [fp, #-0x28]
    // 0xe46c6c: r0 = inline_Allocate_Double()
    //     0xe46c6c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe46c70: add             x0, x0, #0x10
    //     0xe46c74: cmp             x1, x0
    //     0xe46c78: b.ls            #0xe46dd8
    //     0xe46c7c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe46c80: sub             x0, x0, #0xf
    //     0xe46c84: movz            x1, #0xe15c
    //     0xe46c88: movk            x1, #0x3, lsl #16
    //     0xe46c8c: stur            x1, [x0, #-1]
    // 0xe46c90: StoreField: r0->field_7 = d0
    //     0xe46c90: stur            d0, [x0, #7]
    // 0xe46c94: StoreField: r2->field_1b = r0
    //     0xe46c94: stur            w0, [x2, #0x1b]
    // 0xe46c98: r1 = <num>
    //     0xe46c98: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0xe46c9c: r0 = AllocateGrowableArray()
    //     0xe46c9c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe46ca0: mov             x1, x0
    // 0xe46ca4: ldur            x0, [fp, #-0x18]
    // 0xe46ca8: StoreField: r1->field_f = r0
    //     0xe46ca8: stur            w0, [x1, #0xf]
    // 0xe46cac: r0 = 8
    //     0xe46cac: movz            x0, #0x8
    // 0xe46cb0: StoreField: r1->field_b = r0
    //     0xe46cb0: stur            w0, [x1, #0xb]
    // 0xe46cb4: r0 = fromNum()
    //     0xe46cb4: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0xe46cb8: mov             x4, x0
    // 0xe46cbc: ldur            x3, [fp, #-0x20]
    // 0xe46cc0: stur            x4, [fp, #-0x10]
    // 0xe46cc4: LoadField: r2 = r3->field_7
    //     0xe46cc4: ldur            w2, [x3, #7]
    // 0xe46cc8: DecompressPointer r2
    //     0xe46cc8: add             x2, x2, HEAP, lsl #32
    // 0xe46ccc: mov             x0, x4
    // 0xe46cd0: r1 = Null
    //     0xe46cd0: mov             x1, NULL
    // 0xe46cd4: cmp             w2, NULL
    // 0xe46cd8: b.eq            #0xe46cfc
    // 0xe46cdc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe46cdc: ldur            w4, [x2, #0x17]
    // 0xe46ce0: DecompressPointer r4
    //     0xe46ce0: add             x4, x4, HEAP, lsl #32
    // 0xe46ce4: r8 = X0 bound PdfDataType
    //     0xe46ce4: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe46ce8: ldr             x8, [x8, #0x6d8]
    // 0xe46cec: LoadField: r9 = r4->field_7
    //     0xe46cec: ldur            x9, [x4, #7]
    // 0xe46cf0: r3 = Null
    //     0xe46cf0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ea38] Null
    //     0xe46cf4: ldr             x3, [x3, #0xa38]
    // 0xe46cf8: blr             x9
    // 0xe46cfc: ldur            x0, [fp, #-0x20]
    // 0xe46d00: LoadField: r1 = r0->field_b
    //     0xe46d00: ldur            w1, [x0, #0xb]
    // 0xe46d04: DecompressPointer r1
    //     0xe46d04: add             x1, x1, HEAP, lsl #32
    // 0xe46d08: ldur            x3, [fp, #-0x10]
    // 0xe46d0c: r2 = "/BBox"
    //     0xe46d0c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ea48] "/BBox"
    //     0xe46d10: ldr             x2, [x2, #0xa48]
    // 0xe46d14: r0 = []=()
    //     0xe46d14: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe46d18: ldur            x0, [fp, #-8]
    // 0xe46d1c: LoadField: r2 = r0->field_b
    //     0xe46d1c: ldur            w2, [x0, #0xb]
    // 0xe46d20: DecompressPointer r2
    //     0xe46d20: add             x2, x2, HEAP, lsl #32
    // 0xe46d24: stur            x2, [fp, #-0x18]
    // 0xe46d28: LoadField: r3 = r2->field_2b
    //     0xe46d28: ldur            w3, [x2, #0x2b]
    // 0xe46d2c: DecompressPointer r3
    //     0xe46d2c: add             x3, x3, HEAP, lsl #32
    // 0xe46d30: stur            x3, [fp, #-0x10]
    // 0xe46d34: r0 = PdfGraphics()
    //     0xe46d34: bl              #0xe46f40  ; AllocatePdfGraphicsStub -> PdfGraphics (size=0x18)
    // 0xe46d38: mov             x1, x0
    // 0xe46d3c: ldur            x2, [fp, #-0x18]
    // 0xe46d40: ldur            x3, [fp, #-0x10]
    // 0xe46d44: stur            x0, [fp, #-0x10]
    // 0xe46d48: r0 = PdfGraphics()
    //     0xe46d48: bl              #0xe46df0  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::PdfGraphics
    // 0xe46d4c: ldur            x0, [fp, #-0x10]
    // 0xe46d50: ldur            x1, [fp, #-8]
    // 0xe46d54: StoreField: r1->field_f = r0
    //     0xe46d54: stur            w0, [x1, #0xf]
    //     0xe46d58: ldurb           w16, [x1, #-1]
    //     0xe46d5c: ldurb           w17, [x0, #-1]
    //     0xe46d60: and             x16, x17, x16, lsr #2
    //     0xe46d64: tst             x16, HEAP, lsr #32
    //     0xe46d68: b.eq            #0xe46d70
    //     0xe46d6c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe46d70: r0 = Null
    //     0xe46d70: mov             x0, NULL
    // 0xe46d74: LeaveFrame
    //     0xe46d74: mov             SP, fp
    //     0xe46d78: ldp             fp, lr, [SP], #0x10
    // 0xe46d7c: ret
    //     0xe46d7c: ret             
    // 0xe46d80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe46d80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe46d84: b               #0xe46b2c
    // 0xe46d88: stp             q2, q3, [SP, #-0x20]!
    // 0xe46d8c: stp             q0, q1, [SP, #-0x20]!
    // 0xe46d90: stp             x3, x4, [SP, #-0x10]!
    // 0xe46d94: r0 = AllocateDouble()
    //     0xe46d94: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe46d98: ldp             x3, x4, [SP], #0x10
    // 0xe46d9c: ldp             q0, q1, [SP], #0x20
    // 0xe46da0: ldp             q2, q3, [SP], #0x20
    // 0xe46da4: b               #0xe46be4
    // 0xe46da8: SaveReg d0
    //     0xe46da8: str             q0, [SP, #-0x10]!
    // 0xe46dac: SaveReg r2
    //     0xe46dac: str             x2, [SP, #-8]!
    // 0xe46db0: r0 = AllocateDouble()
    //     0xe46db0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe46db4: RestoreReg r2
    //     0xe46db4: ldr             x2, [SP], #8
    // 0xe46db8: RestoreReg d0
    //     0xe46db8: ldr             q0, [SP], #0x10
    // 0xe46dbc: b               #0xe46c30
    // 0xe46dc0: SaveReg d0
    //     0xe46dc0: str             q0, [SP, #-0x10]!
    // 0xe46dc4: SaveReg r2
    //     0xe46dc4: str             x2, [SP, #-8]!
    // 0xe46dc8: r0 = AllocateDouble()
    //     0xe46dc8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe46dcc: RestoreReg r2
    //     0xe46dcc: ldr             x2, [SP], #8
    // 0xe46dd0: RestoreReg d0
    //     0xe46dd0: ldr             q0, [SP], #0x10
    // 0xe46dd4: b               #0xe46c60
    // 0xe46dd8: SaveReg d0
    //     0xe46dd8: str             q0, [SP, #-0x10]!
    // 0xe46ddc: SaveReg r2
    //     0xe46ddc: str             x2, [SP, #-8]!
    // 0xe46de0: r0 = AllocateDouble()
    //     0xe46de0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe46de4: RestoreReg r2
    //     0xe46de4: ldr             x2, [SP], #8
    // 0xe46de8: RestoreReg d0
    //     0xe46de8: ldr             q0, [SP], #0x10
    // 0xe46dec: b               #0xe46c90
  }
}
