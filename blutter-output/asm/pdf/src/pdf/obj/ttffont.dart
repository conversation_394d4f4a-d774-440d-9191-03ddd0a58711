// lib: , url: package:pdf/src/pdf/obj/ttffont.dart

// class id: 1050819, size: 0x8
class :: {
}

// class id: 896, size: 0x44, field offset: 0x30
class PdfTtfFont extends PdfFont {

  late PdfObjectStream file; // offset: 0x38
  late PdfFontDescriptor descriptor; // offset: 0x34
  late PdfObject<PdfArray<PdfDataType>> widthsObject; // offset: 0x3c
  late PdfUnicodeCmap unicodeCMap; // offset: 0x30

  _ prepare(/* No info */) {
    // ** addr: 0x7b65e4, size: 0x11c
    // 0x7b65e4: EnterFrame
    //     0x7b65e4: stp             fp, lr, [SP, #-0x10]!
    //     0x7b65e8: mov             fp, SP
    // 0x7b65ec: AllocStack(0x8)
    //     0x7b65ec: sub             SP, SP, #8
    // 0x7b65f0: SetupParameters(PdfTtfFont this /* r1 => r0, fp-0x8 */)
    //     0x7b65f0: mov             x0, x1
    //     0x7b65f4: stur            x1, [fp, #-8]
    // 0x7b65f8: CheckStackOverflow
    //     0x7b65f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b65fc: cmp             SP, x16
    //     0x7b6600: b.ls            #0x7b66f4
    // 0x7b6604: mov             x1, x0
    // 0x7b6608: r0 = prepare()
    //     0x7b6608: bl              #0x7c913c  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::prepare
    // 0x7b660c: ldur            x2, [fp, #-8]
    // 0x7b6610: LoadField: r0 = r2->field_3f
    //     0x7b6610: ldur            w0, [x2, #0x3f]
    // 0x7b6614: DecompressPointer r0
    //     0x7b6614: add             x0, x0, HEAP, lsl #32
    // 0x7b6618: LoadField: r3 = r0->field_7
    //     0x7b6618: ldur            w3, [x0, #7]
    // 0x7b661c: DecompressPointer r3
    //     0x7b661c: add             x3, x3, HEAP, lsl #32
    // 0x7b6620: LoadField: r0 = r3->field_13
    //     0x7b6620: ldur            w0, [x3, #0x13]
    // 0x7b6624: r1 = LoadInt32Instr(r0)
    //     0x7b6624: sbfx            x1, x0, #1, #0x1f
    // 0x7b6628: sub             x0, x1, #3
    // 0x7b662c: r1 = 0
    //     0x7b662c: movz            x1, #0
    // 0x7b6630: cmp             x1, x0
    // 0x7b6634: b.hs            #0x7b66fc
    // 0x7b6638: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x7b6638: ldur            w0, [x3, #0x17]
    // 0x7b663c: DecompressPointer r0
    //     0x7b663c: add             x0, x0, HEAP, lsl #32
    // 0x7b6640: LoadField: r1 = r3->field_1b
    //     0x7b6640: ldur            w1, [x3, #0x1b]
    // 0x7b6644: LoadField: r3 = r0->field_7
    //     0x7b6644: ldur            x3, [x0, #7]
    // 0x7b6648: asr             w16, w1, #1
    // 0x7b664c: add             x16, x3, w16, sxtw
    // 0x7b6650: ldr             w0, [x16]
    // 0x7b6654: r1 = 4278255360
    //     0x7b6654: movz            x1, #0xff00
    //     0x7b6658: movk            x1, #0xff00, lsl #16
    // 0x7b665c: and             x3, x0, x1
    // 0x7b6660: ubfx            x3, x3, #0, #0x20
    // 0x7b6664: asr             x1, x3, #8
    // 0x7b6668: r3 = 16711935
    //     0x7b6668: movz            x3, #0xff
    //     0x7b666c: movk            x3, #0xff, lsl #16
    // 0x7b6670: and             x4, x0, x3
    // 0x7b6674: ubfx            x4, x4, #0, #0x20
    // 0x7b6678: lsl             x0, x4, #8
    // 0x7b667c: orr             x3, x1, x0
    // 0x7b6680: mov             x0, x3
    // 0x7b6684: ubfx            x0, x0, #0, #0x20
    // 0x7b6688: r1 = 4294901760
    //     0x7b6688: orr             x1, xzr, #0xffff0000
    // 0x7b668c: and             x4, x0, x1
    // 0x7b6690: ubfx            x4, x4, #0, #0x20
    // 0x7b6694: asr             x0, x4, #0x10
    // 0x7b6698: ubfx            x3, x3, #0, #0x20
    // 0x7b669c: r1 = 65535
    //     0x7b669c: orr             x1, xzr, #0xffff
    // 0x7b66a0: and             x4, x3, x1
    // 0x7b66a4: ubfx            x4, x4, #0, #0x20
    // 0x7b66a8: lsl             x1, x4, #0x10
    // 0x7b66ac: orr             x3, x0, x1
    // 0x7b66b0: cmp             x3, #0x10, lsl #12
    // 0x7b66b4: b.ne            #0x7b66d0
    // 0x7b66b8: LoadField: r0 = r2->field_1b
    //     0x7b66b8: ldur            w0, [x2, #0x1b]
    // 0x7b66bc: DecompressPointer r0
    //     0x7b66bc: add             x0, x0, HEAP, lsl #32
    // 0x7b66c0: mov             x1, x2
    // 0x7b66c4: mov             x2, x0
    // 0x7b66c8: r0 = _buildType0()
    //     0x7b66c8: bl              #0x7b8280  ; [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::_buildType0
    // 0x7b66cc: b               #0x7b66e4
    // 0x7b66d0: LoadField: r0 = r2->field_1b
    //     0x7b66d0: ldur            w0, [x2, #0x1b]
    // 0x7b66d4: DecompressPointer r0
    //     0x7b66d4: add             x0, x0, HEAP, lsl #32
    // 0x7b66d8: mov             x1, x2
    // 0x7b66dc: mov             x2, x0
    // 0x7b66e0: r0 = _buildTrueType()
    //     0x7b66e0: bl              #0x7b6724  ; [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::_buildTrueType
    // 0x7b66e4: r0 = Null
    //     0x7b66e4: mov             x0, NULL
    // 0x7b66e8: LeaveFrame
    //     0x7b66e8: mov             SP, fp
    //     0x7b66ec: ldp             fp, lr, [SP], #0x10
    // 0x7b66f0: ret
    //     0x7b66f0: ret             
    // 0x7b66f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b66f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b66f8: b               #0x7b6604
    // 0x7b66fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b66fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildTrueType(/* No info */) {
    // ** addr: 0x7b6724, size: 0x5ac
    // 0x7b6724: EnterFrame
    //     0x7b6724: stp             fp, lr, [SP, #-0x10]!
    //     0x7b6728: mov             fp, SP
    // 0x7b672c: AllocStack(0x48)
    //     0x7b672c: sub             SP, SP, #0x48
    // 0x7b6730: SetupParameters(PdfTtfFont this /* r1 => r3, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */)
    //     0x7b6730: mov             x3, x1
    //     0x7b6734: stur            x1, [fp, #-0x20]
    //     0x7b6738: stur            x2, [fp, #-0x28]
    // 0x7b673c: CheckStackOverflow
    //     0x7b673c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b6740: cmp             SP, x16
    //     0x7b6744: b.ls            #0x7b6c70
    // 0x7b6748: LoadField: r0 = r3->field_37
    //     0x7b6748: ldur            w0, [x3, #0x37]
    // 0x7b674c: DecompressPointer r0
    //     0x7b674c: add             x0, x0, HEAP, lsl #32
    // 0x7b6750: r16 = Sentinel
    //     0x7b6750: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b6754: cmp             w0, w16
    // 0x7b6758: b.eq            #0x7b6c78
    // 0x7b675c: LoadField: r4 = r0->field_2b
    //     0x7b675c: ldur            w4, [x0, #0x2b]
    // 0x7b6760: DecompressPointer r4
    //     0x7b6760: add             x4, x4, HEAP, lsl #32
    // 0x7b6764: stur            x4, [fp, #-0x18]
    // 0x7b6768: LoadField: r5 = r3->field_3f
    //     0x7b6768: ldur            w5, [x3, #0x3f]
    // 0x7b676c: DecompressPointer r5
    //     0x7b676c: add             x5, x5, HEAP, lsl #32
    // 0x7b6770: stur            x5, [fp, #-0x10]
    // 0x7b6774: LoadField: r6 = r5->field_7
    //     0x7b6774: ldur            w6, [x5, #7]
    // 0x7b6778: DecompressPointer r6
    //     0x7b6778: add             x6, x6, HEAP, lsl #32
    // 0x7b677c: stur            x6, [fp, #-8]
    // 0x7b6780: r0 = LoadClassIdInstr(r6)
    //     0x7b6780: ldur            x0, [x6, #-1]
    //     0x7b6784: ubfx            x0, x0, #0xc, #0x14
    // 0x7b6788: mov             x1, x6
    // 0x7b678c: r0 = GDT[cid_x0 + -0xf60]()
    //     0x7b678c: sub             lr, x0, #0xf60
    //     0x7b6790: ldr             lr, [x21, lr, lsl #3]
    //     0x7b6794: blr             lr
    // 0x7b6798: r1 = LoadClassIdInstr(r0)
    //     0x7b6798: ldur            x1, [x0, #-1]
    //     0x7b679c: ubfx            x1, x1, #0xc, #0x14
    // 0x7b67a0: mov             x16, x0
    // 0x7b67a4: mov             x0, x1
    // 0x7b67a8: mov             x1, x16
    // 0x7b67ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7b67ac: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7b67b0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7b67b0: sub             lr, x0, #1, lsl #12
    //     0x7b67b4: ldr             lr, [x21, lr, lsl #3]
    //     0x7b67b8: blr             lr
    // 0x7b67bc: ldur            x1, [fp, #-0x18]
    // 0x7b67c0: mov             x2, x0
    // 0x7b67c4: r0 = putBytes()
    //     0x7b67c4: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0x7b67c8: ldur            x1, [fp, #-0x20]
    // 0x7b67cc: LoadField: r0 = r1->field_37
    //     0x7b67cc: ldur            w0, [x1, #0x37]
    // 0x7b67d0: DecompressPointer r0
    //     0x7b67d0: add             x0, x0, HEAP, lsl #32
    // 0x7b67d4: LoadField: r2 = r0->field_1b
    //     0x7b67d4: ldur            w2, [x0, #0x1b]
    // 0x7b67d8: DecompressPointer r2
    //     0x7b67d8: add             x2, x2, HEAP, lsl #32
    // 0x7b67dc: ldur            x0, [fp, #-8]
    // 0x7b67e0: stur            x2, [fp, #-0x30]
    // 0x7b67e4: LoadField: r3 = r0->field_13
    //     0x7b67e4: ldur            w3, [x0, #0x13]
    // 0x7b67e8: stur            x3, [fp, #-0x18]
    // 0x7b67ec: r0 = PdfNum()
    //     0x7b67ec: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b67f0: mov             x3, x0
    // 0x7b67f4: ldur            x0, [fp, #-0x18]
    // 0x7b67f8: stur            x3, [fp, #-8]
    // 0x7b67fc: StoreField: r3->field_7 = r0
    //     0x7b67fc: stur            w0, [x3, #7]
    // 0x7b6800: ldur            x4, [fp, #-0x30]
    // 0x7b6804: LoadField: r2 = r4->field_7
    //     0x7b6804: ldur            w2, [x4, #7]
    // 0x7b6808: DecompressPointer r2
    //     0x7b6808: add             x2, x2, HEAP, lsl #32
    // 0x7b680c: mov             x0, x3
    // 0x7b6810: r1 = Null
    //     0x7b6810: mov             x1, NULL
    // 0x7b6814: cmp             w2, NULL
    // 0x7b6818: b.eq            #0x7b683c
    // 0x7b681c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b681c: ldur            w4, [x2, #0x17]
    // 0x7b6820: DecompressPointer r4
    //     0x7b6820: add             x4, x4, HEAP, lsl #32
    // 0x7b6824: r8 = X0 bound PdfDataType
    //     0x7b6824: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b6828: ldr             x8, [x8, #0x6d8]
    // 0x7b682c: LoadField: r9 = r4->field_7
    //     0x7b682c: ldur            x9, [x4, #7]
    // 0x7b6830: r3 = Null
    //     0x7b6830: add             x3, PP, #0x47, lsl #12  ; [pp+0x47548] Null
    //     0x7b6834: ldr             x3, [x3, #0x548]
    // 0x7b6838: blr             x9
    // 0x7b683c: ldur            x0, [fp, #-0x30]
    // 0x7b6840: LoadField: r1 = r0->field_b
    //     0x7b6840: ldur            w1, [x0, #0xb]
    // 0x7b6844: DecompressPointer r1
    //     0x7b6844: add             x1, x1, HEAP, lsl #32
    // 0x7b6848: ldur            x3, [fp, #-8]
    // 0x7b684c: r2 = "/Length1"
    //     0x7b684c: add             x2, PP, #0x47, lsl #12  ; [pp+0x47558] "/Length1"
    //     0x7b6850: ldr             x2, [x2, #0x558]
    // 0x7b6854: r0 = []=()
    //     0x7b6854: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b6858: r1 = Null
    //     0x7b6858: mov             x1, NULL
    // 0x7b685c: r2 = 4
    //     0x7b685c: movz            x2, #0x4
    // 0x7b6860: r0 = AllocateArray()
    //     0x7b6860: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b6864: stur            x0, [fp, #-8]
    // 0x7b6868: r16 = "/"
    //     0x7b6868: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x7b686c: StoreField: r0->field_f = r16
    //     0x7b686c: stur            w16, [x0, #0xf]
    // 0x7b6870: ldur            x1, [fp, #-0x10]
    // 0x7b6874: r0 = fontName()
    //     0x7b6874: bl              #0x7b73dc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::fontName
    // 0x7b6878: ldur            x1, [fp, #-8]
    // 0x7b687c: ArrayStore: r1[1] = r0  ; List_4
    //     0x7b687c: add             x25, x1, #0x13
    //     0x7b6880: str             w0, [x25]
    //     0x7b6884: tbz             w0, #0, #0x7b68a0
    //     0x7b6888: ldurb           w16, [x1, #-1]
    //     0x7b688c: ldurb           w17, [x0, #-1]
    //     0x7b6890: and             x16, x17, x16, lsr #2
    //     0x7b6894: tst             x16, HEAP, lsr #32
    //     0x7b6898: b.eq            #0x7b68a0
    //     0x7b689c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b68a0: ldur            x16, [fp, #-8]
    // 0x7b68a4: str             x16, [SP]
    // 0x7b68a8: r0 = _interpolate()
    //     0x7b68a8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7b68ac: stur            x0, [fp, #-8]
    // 0x7b68b0: r0 = PdfName()
    //     0x7b68b0: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0x7b68b4: mov             x3, x0
    // 0x7b68b8: ldur            x0, [fp, #-8]
    // 0x7b68bc: stur            x3, [fp, #-0x10]
    // 0x7b68c0: StoreField: r3->field_7 = r0
    //     0x7b68c0: stur            w0, [x3, #7]
    // 0x7b68c4: ldur            x4, [fp, #-0x28]
    // 0x7b68c8: LoadField: r5 = r4->field_7
    //     0x7b68c8: ldur            w5, [x4, #7]
    // 0x7b68cc: DecompressPointer r5
    //     0x7b68cc: add             x5, x5, HEAP, lsl #32
    // 0x7b68d0: mov             x0, x3
    // 0x7b68d4: mov             x2, x5
    // 0x7b68d8: stur            x5, [fp, #-8]
    // 0x7b68dc: r1 = Null
    //     0x7b68dc: mov             x1, NULL
    // 0x7b68e0: cmp             w2, NULL
    // 0x7b68e4: b.eq            #0x7b6908
    // 0x7b68e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b68e8: ldur            w4, [x2, #0x17]
    // 0x7b68ec: DecompressPointer r4
    //     0x7b68ec: add             x4, x4, HEAP, lsl #32
    // 0x7b68f0: r8 = X0 bound PdfDataType
    //     0x7b68f0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b68f4: ldr             x8, [x8, #0x6d8]
    // 0x7b68f8: LoadField: r9 = r4->field_7
    //     0x7b68f8: ldur            x9, [x4, #7]
    // 0x7b68fc: r3 = Null
    //     0x7b68fc: add             x3, PP, #0x47, lsl #12  ; [pp+0x47560] Null
    //     0x7b6900: ldr             x3, [x3, #0x560]
    // 0x7b6904: blr             x9
    // 0x7b6908: ldur            x0, [fp, #-0x28]
    // 0x7b690c: LoadField: r4 = r0->field_b
    //     0x7b690c: ldur            w4, [x0, #0xb]
    // 0x7b6910: DecompressPointer r4
    //     0x7b6910: add             x4, x4, HEAP, lsl #32
    // 0x7b6914: mov             x1, x4
    // 0x7b6918: ldur            x3, [fp, #-0x10]
    // 0x7b691c: stur            x4, [fp, #-0x18]
    // 0x7b6920: r2 = "/BaseFont"
    //     0x7b6920: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3de60] "/BaseFont"
    //     0x7b6924: ldr             x2, [x2, #0xe60]
    // 0x7b6928: r0 = []=()
    //     0x7b6928: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b692c: ldur            x0, [fp, #-0x20]
    // 0x7b6930: LoadField: r1 = r0->field_33
    //     0x7b6930: ldur            w1, [x0, #0x33]
    // 0x7b6934: DecompressPointer r1
    //     0x7b6934: add             x1, x1, HEAP, lsl #32
    // 0x7b6938: r16 = Sentinel
    //     0x7b6938: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b693c: cmp             w1, w16
    // 0x7b6940: b.eq            #0x7b6c84
    // 0x7b6944: r0 = ref()
    //     0x7b6944: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7b6948: ldur            x2, [fp, #-8]
    // 0x7b694c: mov             x3, x0
    // 0x7b6950: r1 = Null
    //     0x7b6950: mov             x1, NULL
    // 0x7b6954: stur            x3, [fp, #-0x10]
    // 0x7b6958: cmp             w2, NULL
    // 0x7b695c: b.eq            #0x7b6980
    // 0x7b6960: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b6960: ldur            w4, [x2, #0x17]
    // 0x7b6964: DecompressPointer r4
    //     0x7b6964: add             x4, x4, HEAP, lsl #32
    // 0x7b6968: r8 = X0 bound PdfDataType
    //     0x7b6968: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b696c: ldr             x8, [x8, #0x6d8]
    // 0x7b6970: LoadField: r9 = r4->field_7
    //     0x7b6970: ldur            x9, [x4, #7]
    // 0x7b6974: r3 = Null
    //     0x7b6974: add             x3, PP, #0x47, lsl #12  ; [pp+0x47570] Null
    //     0x7b6978: ldr             x3, [x3, #0x570]
    // 0x7b697c: blr             x9
    // 0x7b6980: ldur            x1, [fp, #-0x18]
    // 0x7b6984: ldur            x3, [fp, #-0x10]
    // 0x7b6988: r2 = "/FontDescriptor"
    //     0x7b6988: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3df38] "/FontDescriptor"
    //     0x7b698c: ldr             x2, [x2, #0xf38]
    // 0x7b6990: r0 = []=()
    //     0x7b6990: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b6994: r4 = 32
    //     0x7b6994: movz            x4, #0x20
    // 0x7b6998: ldur            x3, [fp, #-0x20]
    // 0x7b699c: stur            x4, [fp, #-0x38]
    // 0x7b69a0: CheckStackOverflow
    //     0x7b69a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b69a4: cmp             SP, x16
    //     0x7b69a8: b.ls            #0x7b6c90
    // 0x7b69ac: cmp             x4, #0xff
    // 0x7b69b0: b.gt            #0x7b6b3c
    // 0x7b69b4: LoadField: r0 = r3->field_3b
    //     0x7b69b4: ldur            w0, [x3, #0x3b]
    // 0x7b69b8: DecompressPointer r0
    //     0x7b69b8: add             x0, x0, HEAP, lsl #32
    // 0x7b69bc: r16 = Sentinel
    //     0x7b69bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b69c0: cmp             w0, w16
    // 0x7b69c4: b.eq            #0x7b6c98
    // 0x7b69c8: LoadField: r5 = r0->field_1b
    //     0x7b69c8: ldur            w5, [x0, #0x1b]
    // 0x7b69cc: DecompressPointer r5
    //     0x7b69cc: add             x5, x5, HEAP, lsl #32
    // 0x7b69d0: stur            x5, [fp, #-0x10]
    // 0x7b69d4: r0 = BoxInt64Instr(r4)
    //     0x7b69d4: sbfiz           x0, x4, #1, #0x1f
    //     0x7b69d8: cmp             x4, x0, asr #1
    //     0x7b69dc: b.eq            #0x7b69e8
    //     0x7b69e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7b69e4: stur            x4, [x0, #7]
    // 0x7b69e8: mov             x1, x3
    // 0x7b69ec: mov             x2, x0
    // 0x7b69f0: r0 = glyphMetrics()
    //     0x7b69f0: bl              #0x7b6cd0  ; [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::glyphMetrics
    // 0x7b69f4: LoadField: d0 = r0->field_37
    //     0x7b69f4: ldur            d0, [x0, #0x37]
    // 0x7b69f8: d1 = 1000.000000
    //     0x7b69f8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x7b69fc: ldr             d1, [x17, #0x238]
    // 0x7b6a00: fmul            d2, d0, d1
    // 0x7b6a04: fcmp            d2, d2
    // 0x7b6a08: b.vs            #0x7b6ca4
    // 0x7b6a0c: fcvtzs          x0, d2
    // 0x7b6a10: asr             x16, x0, #0x1e
    // 0x7b6a14: cmp             x16, x0, asr #63
    // 0x7b6a18: b.ne            #0x7b6ca4
    // 0x7b6a1c: lsl             x0, x0, #1
    // 0x7b6a20: stur            x0, [fp, #-0x28]
    // 0x7b6a24: r0 = PdfNum()
    //     0x7b6a24: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b6a28: mov             x3, x0
    // 0x7b6a2c: ldur            x0, [fp, #-0x28]
    // 0x7b6a30: stur            x3, [fp, #-0x30]
    // 0x7b6a34: StoreField: r3->field_7 = r0
    //     0x7b6a34: stur            w0, [x3, #7]
    // 0x7b6a38: ldur            x4, [fp, #-0x10]
    // 0x7b6a3c: LoadField: r2 = r4->field_7
    //     0x7b6a3c: ldur            w2, [x4, #7]
    // 0x7b6a40: DecompressPointer r2
    //     0x7b6a40: add             x2, x2, HEAP, lsl #32
    // 0x7b6a44: mov             x0, x3
    // 0x7b6a48: r1 = Null
    //     0x7b6a48: mov             x1, NULL
    // 0x7b6a4c: cmp             w2, NULL
    // 0x7b6a50: b.eq            #0x7b6a74
    // 0x7b6a54: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b6a54: ldur            w4, [x2, #0x17]
    // 0x7b6a58: DecompressPointer r4
    //     0x7b6a58: add             x4, x4, HEAP, lsl #32
    // 0x7b6a5c: r8 = X0 bound PdfDataType
    //     0x7b6a5c: add             x8, PP, #0x47, lsl #12  ; [pp+0x47580] TypeParameter: X0 bound PdfDataType
    //     0x7b6a60: ldr             x8, [x8, #0x580]
    // 0x7b6a64: LoadField: r9 = r4->field_7
    //     0x7b6a64: ldur            x9, [x4, #7]
    // 0x7b6a68: r3 = Null
    //     0x7b6a68: add             x3, PP, #0x47, lsl #12  ; [pp+0x47588] Null
    //     0x7b6a6c: ldr             x3, [x3, #0x588]
    // 0x7b6a70: blr             x9
    // 0x7b6a74: ldur            x0, [fp, #-0x10]
    // 0x7b6a78: LoadField: r3 = r0->field_b
    //     0x7b6a78: ldur            w3, [x0, #0xb]
    // 0x7b6a7c: DecompressPointer r3
    //     0x7b6a7c: add             x3, x3, HEAP, lsl #32
    // 0x7b6a80: stur            x3, [fp, #-0x28]
    // 0x7b6a84: LoadField: r2 = r3->field_7
    //     0x7b6a84: ldur            w2, [x3, #7]
    // 0x7b6a88: DecompressPointer r2
    //     0x7b6a88: add             x2, x2, HEAP, lsl #32
    // 0x7b6a8c: ldur            x0, [fp, #-0x30]
    // 0x7b6a90: r1 = Null
    //     0x7b6a90: mov             x1, NULL
    // 0x7b6a94: cmp             w2, NULL
    // 0x7b6a98: b.eq            #0x7b6ab8
    // 0x7b6a9c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b6a9c: ldur            w4, [x2, #0x17]
    // 0x7b6aa0: DecompressPointer r4
    //     0x7b6aa0: add             x4, x4, HEAP, lsl #32
    // 0x7b6aa4: r8 = X0
    //     0x7b6aa4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7b6aa8: LoadField: r9 = r4->field_7
    //     0x7b6aa8: ldur            x9, [x4, #7]
    // 0x7b6aac: r3 = Null
    //     0x7b6aac: add             x3, PP, #0x47, lsl #12  ; [pp+0x47598] Null
    //     0x7b6ab0: ldr             x3, [x3, #0x598]
    // 0x7b6ab4: blr             x9
    // 0x7b6ab8: ldur            x0, [fp, #-0x28]
    // 0x7b6abc: LoadField: r1 = r0->field_b
    //     0x7b6abc: ldur            w1, [x0, #0xb]
    // 0x7b6ac0: LoadField: r2 = r0->field_f
    //     0x7b6ac0: ldur            w2, [x0, #0xf]
    // 0x7b6ac4: DecompressPointer r2
    //     0x7b6ac4: add             x2, x2, HEAP, lsl #32
    // 0x7b6ac8: LoadField: r3 = r2->field_b
    //     0x7b6ac8: ldur            w3, [x2, #0xb]
    // 0x7b6acc: r2 = LoadInt32Instr(r1)
    //     0x7b6acc: sbfx            x2, x1, #1, #0x1f
    // 0x7b6ad0: stur            x2, [fp, #-0x40]
    // 0x7b6ad4: r1 = LoadInt32Instr(r3)
    //     0x7b6ad4: sbfx            x1, x3, #1, #0x1f
    // 0x7b6ad8: cmp             x2, x1
    // 0x7b6adc: b.ne            #0x7b6ae8
    // 0x7b6ae0: mov             x1, x0
    // 0x7b6ae4: r0 = _growToNextCapacity()
    //     0x7b6ae4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b6ae8: ldur            x3, [fp, #-0x38]
    // 0x7b6aec: ldur            x0, [fp, #-0x28]
    // 0x7b6af0: ldur            x2, [fp, #-0x40]
    // 0x7b6af4: add             x1, x2, #1
    // 0x7b6af8: lsl             x4, x1, #1
    // 0x7b6afc: StoreField: r0->field_b = r4
    //     0x7b6afc: stur            w4, [x0, #0xb]
    // 0x7b6b00: LoadField: r1 = r0->field_f
    //     0x7b6b00: ldur            w1, [x0, #0xf]
    // 0x7b6b04: DecompressPointer r1
    //     0x7b6b04: add             x1, x1, HEAP, lsl #32
    // 0x7b6b08: ldur            x0, [fp, #-0x30]
    // 0x7b6b0c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7b6b0c: add             x25, x1, x2, lsl #2
    //     0x7b6b10: add             x25, x25, #0xf
    //     0x7b6b14: str             w0, [x25]
    //     0x7b6b18: tbz             w0, #0, #0x7b6b34
    //     0x7b6b1c: ldurb           w16, [x1, #-1]
    //     0x7b6b20: ldurb           w17, [x0, #-1]
    //     0x7b6b24: and             x16, x17, x16, lsr #2
    //     0x7b6b28: tst             x16, HEAP, lsr #32
    //     0x7b6b2c: b.eq            #0x7b6b34
    //     0x7b6b30: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b6b34: add             x4, x3, #1
    // 0x7b6b38: b               #0x7b6998
    // 0x7b6b3c: mov             x0, x3
    // 0x7b6b40: r0 = PdfNum()
    //     0x7b6b40: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b6b44: mov             x3, x0
    // 0x7b6b48: r0 = 64
    //     0x7b6b48: movz            x0, #0x40
    // 0x7b6b4c: stur            x3, [fp, #-0x10]
    // 0x7b6b50: StoreField: r3->field_7 = r0
    //     0x7b6b50: stur            w0, [x3, #7]
    // 0x7b6b54: mov             x0, x3
    // 0x7b6b58: ldur            x2, [fp, #-8]
    // 0x7b6b5c: r1 = Null
    //     0x7b6b5c: mov             x1, NULL
    // 0x7b6b60: cmp             w2, NULL
    // 0x7b6b64: b.eq            #0x7b6b88
    // 0x7b6b68: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b6b68: ldur            w4, [x2, #0x17]
    // 0x7b6b6c: DecompressPointer r4
    //     0x7b6b6c: add             x4, x4, HEAP, lsl #32
    // 0x7b6b70: r8 = X0 bound PdfDataType
    //     0x7b6b70: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b6b74: ldr             x8, [x8, #0x6d8]
    // 0x7b6b78: LoadField: r9 = r4->field_7
    //     0x7b6b78: ldur            x9, [x4, #7]
    // 0x7b6b7c: r3 = Null
    //     0x7b6b7c: add             x3, PP, #0x47, lsl #12  ; [pp+0x475a8] Null
    //     0x7b6b80: ldr             x3, [x3, #0x5a8]
    // 0x7b6b84: blr             x9
    // 0x7b6b88: ldur            x1, [fp, #-0x18]
    // 0x7b6b8c: ldur            x3, [fp, #-0x10]
    // 0x7b6b90: r2 = "/FirstChar"
    //     0x7b6b90: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3de78] "/FirstChar"
    //     0x7b6b94: ldr             x2, [x2, #0xe78]
    // 0x7b6b98: r0 = []=()
    //     0x7b6b98: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b6b9c: r0 = PdfNum()
    //     0x7b6b9c: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b6ba0: mov             x3, x0
    // 0x7b6ba4: r0 = 510
    //     0x7b6ba4: movz            x0, #0x1fe
    // 0x7b6ba8: stur            x3, [fp, #-0x10]
    // 0x7b6bac: StoreField: r3->field_7 = r0
    //     0x7b6bac: stur            w0, [x3, #7]
    // 0x7b6bb0: mov             x0, x3
    // 0x7b6bb4: ldur            x2, [fp, #-8]
    // 0x7b6bb8: r1 = Null
    //     0x7b6bb8: mov             x1, NULL
    // 0x7b6bbc: cmp             w2, NULL
    // 0x7b6bc0: b.eq            #0x7b6be4
    // 0x7b6bc4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b6bc4: ldur            w4, [x2, #0x17]
    // 0x7b6bc8: DecompressPointer r4
    //     0x7b6bc8: add             x4, x4, HEAP, lsl #32
    // 0x7b6bcc: r8 = X0 bound PdfDataType
    //     0x7b6bcc: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b6bd0: ldr             x8, [x8, #0x6d8]
    // 0x7b6bd4: LoadField: r9 = r4->field_7
    //     0x7b6bd4: ldur            x9, [x4, #7]
    // 0x7b6bd8: r3 = Null
    //     0x7b6bd8: add             x3, PP, #0x47, lsl #12  ; [pp+0x475b8] Null
    //     0x7b6bdc: ldr             x3, [x3, #0x5b8]
    // 0x7b6be0: blr             x9
    // 0x7b6be4: ldur            x1, [fp, #-0x18]
    // 0x7b6be8: ldur            x3, [fp, #-0x10]
    // 0x7b6bec: r2 = "/LastChar"
    //     0x7b6bec: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3de98] "/LastChar"
    //     0x7b6bf0: ldr             x2, [x2, #0xe98]
    // 0x7b6bf4: r0 = []=()
    //     0x7b6bf4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b6bf8: ldur            x0, [fp, #-0x20]
    // 0x7b6bfc: LoadField: r1 = r0->field_3b
    //     0x7b6bfc: ldur            w1, [x0, #0x3b]
    // 0x7b6c00: DecompressPointer r1
    //     0x7b6c00: add             x1, x1, HEAP, lsl #32
    // 0x7b6c04: r16 = Sentinel
    //     0x7b6c04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b6c08: cmp             w1, w16
    // 0x7b6c0c: b.eq            #0x7b6cc4
    // 0x7b6c10: r0 = ref()
    //     0x7b6c10: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7b6c14: ldur            x2, [fp, #-8]
    // 0x7b6c18: mov             x3, x0
    // 0x7b6c1c: r1 = Null
    //     0x7b6c1c: mov             x1, NULL
    // 0x7b6c20: stur            x3, [fp, #-8]
    // 0x7b6c24: cmp             w2, NULL
    // 0x7b6c28: b.eq            #0x7b6c4c
    // 0x7b6c2c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b6c2c: ldur            w4, [x2, #0x17]
    // 0x7b6c30: DecompressPointer r4
    //     0x7b6c30: add             x4, x4, HEAP, lsl #32
    // 0x7b6c34: r8 = X0 bound PdfDataType
    //     0x7b6c34: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b6c38: ldr             x8, [x8, #0x6d8]
    // 0x7b6c3c: LoadField: r9 = r4->field_7
    //     0x7b6c3c: ldur            x9, [x4, #7]
    // 0x7b6c40: r3 = Null
    //     0x7b6c40: add             x3, PP, #0x47, lsl #12  ; [pp+0x475c8] Null
    //     0x7b6c44: ldr             x3, [x3, #0x5c8]
    // 0x7b6c48: blr             x9
    // 0x7b6c4c: ldur            x1, [fp, #-0x18]
    // 0x7b6c50: ldur            x3, [fp, #-8]
    // 0x7b6c54: r2 = "/Widths"
    //     0x7b6c54: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3deb8] "/Widths"
    //     0x7b6c58: ldr             x2, [x2, #0xeb8]
    // 0x7b6c5c: r0 = []=()
    //     0x7b6c5c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b6c60: r0 = Null
    //     0x7b6c60: mov             x0, NULL
    // 0x7b6c64: LeaveFrame
    //     0x7b6c64: mov             SP, fp
    //     0x7b6c68: ldp             fp, lr, [SP], #0x10
    // 0x7b6c6c: ret
    //     0x7b6c6c: ret             
    // 0x7b6c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b6c70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b6c74: b               #0x7b6748
    // 0x7b6c78: r9 = file
    //     0x7b6c78: add             x9, PP, #0x47, lsl #12  ; [pp+0x475d8] Field <PdfTtfFont.file>: late (offset: 0x38)
    //     0x7b6c7c: ldr             x9, [x9, #0x5d8]
    // 0x7b6c80: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b6c80: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7b6c84: r9 = descriptor
    //     0x7b6c84: add             x9, PP, #0x47, lsl #12  ; [pp+0x475e0] Field <PdfTtfFont.descriptor>: late (offset: 0x34)
    //     0x7b6c88: ldr             x9, [x9, #0x5e0]
    // 0x7b6c8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b6c8c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7b6c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b6c90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b6c94: b               #0x7b69ac
    // 0x7b6c98: r9 = widthsObject
    //     0x7b6c98: add             x9, PP, #0x47, lsl #12  ; [pp+0x475e8] Field <PdfTtfFont.widthsObject>: late (offset: 0x3c)
    //     0x7b6c9c: ldr             x9, [x9, #0x5e8]
    // 0x7b6ca0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b6ca0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7b6ca4: stp             q1, q2, [SP, #-0x20]!
    // 0x7b6ca8: d0 = 0.000000
    //     0x7b6ca8: fmov            d0, d2
    // 0x7b6cac: r0 = 74
    //     0x7b6cac: movz            x0, #0x4a
    // 0x7b6cb0: r30 = DoubleToIntegerStub
    //     0x7b6cb0: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x7b6cb4: LoadField: r30 = r30->field_7
    //     0x7b6cb4: ldur            lr, [lr, #7]
    // 0x7b6cb8: blr             lr
    // 0x7b6cbc: ldp             q1, q2, [SP], #0x20
    // 0x7b6cc0: b               #0x7b6a20
    // 0x7b6cc4: r9 = widthsObject
    //     0x7b6cc4: add             x9, PP, #0x47, lsl #12  ; [pp+0x475e8] Field <PdfTtfFont.widthsObject>: late (offset: 0x3c)
    //     0x7b6cc8: ldr             x9, [x9, #0x5e8]
    // 0x7b6ccc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b6ccc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ glyphMetrics(/* No info */) {
    // ** addr: 0x7b6cd0, size: 0x178
    // 0x7b6cd0: EnterFrame
    //     0x7b6cd0: stp             fp, lr, [SP, #-0x10]!
    //     0x7b6cd4: mov             fp, SP
    // 0x7b6cd8: AllocStack(0x18)
    //     0x7b6cd8: sub             SP, SP, #0x18
    // 0x7b6cdc: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x7b6cdc: mov             x0, x2
    //     0x7b6ce0: stur            x2, [fp, #-0x18]
    // 0x7b6ce4: CheckStackOverflow
    //     0x7b6ce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b6ce8: cmp             SP, x16
    //     0x7b6cec: b.ls            #0x7b6e40
    // 0x7b6cf0: LoadField: r3 = r1->field_3f
    //     0x7b6cf0: ldur            w3, [x1, #0x3f]
    // 0x7b6cf4: DecompressPointer r3
    //     0x7b6cf4: add             x3, x3, HEAP, lsl #32
    // 0x7b6cf8: stur            x3, [fp, #-0x10]
    // 0x7b6cfc: LoadField: r4 = r3->field_13
    //     0x7b6cfc: ldur            w4, [x3, #0x13]
    // 0x7b6d00: DecompressPointer r4
    //     0x7b6d00: add             x4, x4, HEAP, lsl #32
    // 0x7b6d04: mov             x1, x4
    // 0x7b6d08: mov             x2, x0
    // 0x7b6d0c: stur            x4, [fp, #-8]
    // 0x7b6d10: r0 = _getValueOrData()
    //     0x7b6d10: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7b6d14: mov             x1, x0
    // 0x7b6d18: ldur            x0, [fp, #-8]
    // 0x7b6d1c: LoadField: r2 = r0->field_f
    //     0x7b6d1c: ldur            w2, [x0, #0xf]
    // 0x7b6d20: DecompressPointer r2
    //     0x7b6d20: add             x2, x2, HEAP, lsl #32
    // 0x7b6d24: cmp             w2, w1
    // 0x7b6d28: b.ne            #0x7b6d34
    // 0x7b6d2c: r2 = Null
    //     0x7b6d2c: mov             x2, NULL
    // 0x7b6d30: b               #0x7b6d38
    // 0x7b6d34: mov             x2, x1
    // 0x7b6d38: stur            x2, [fp, #-8]
    // 0x7b6d3c: cmp             w2, NULL
    // 0x7b6d40: b.ne            #0x7b6d58
    // 0x7b6d44: r0 = Instance_PdfFontMetrics
    //     0x7b6d44: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e120] Obj!PdfFontMetrics@e0c991
    //     0x7b6d48: ldr             x0, [x0, #0x120]
    // 0x7b6d4c: LeaveFrame
    //     0x7b6d4c: mov             SP, fp
    //     0x7b6d50: ldp             fp, lr, [SP], #0x10
    // 0x7b6d54: ret
    //     0x7b6d54: ret             
    // 0x7b6d58: ldur            x0, [fp, #-0x18]
    // 0x7b6d5c: r1 = LoadInt32Instr(r0)
    //     0x7b6d5c: sbfx            x1, x0, #1, #0x1f
    //     0x7b6d60: tbz             w0, #0, #0x7b6d68
    //     0x7b6d64: ldur            x1, [x0, #7]
    // 0x7b6d68: r0 = isArabicDiacriticValue()
    //     0x7b6d68: bl              #0x7b7204  ; [package:pdf/src/pdf/font/bidi_utils.dart] ::isArabicDiacriticValue
    // 0x7b6d6c: tbnz            w0, #4, #0x7b6de0
    // 0x7b6d70: ldur            x0, [fp, #-0x10]
    // 0x7b6d74: LoadField: r3 = r0->field_1f
    //     0x7b6d74: ldur            w3, [x0, #0x1f]
    // 0x7b6d78: DecompressPointer r3
    //     0x7b6d78: add             x3, x3, HEAP, lsl #32
    // 0x7b6d7c: mov             x1, x3
    // 0x7b6d80: ldur            x2, [fp, #-8]
    // 0x7b6d84: stur            x3, [fp, #-0x18]
    // 0x7b6d88: r0 = _getValueOrData()
    //     0x7b6d88: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7b6d8c: mov             x1, x0
    // 0x7b6d90: ldur            x0, [fp, #-0x18]
    // 0x7b6d94: LoadField: r2 = r0->field_f
    //     0x7b6d94: ldur            w2, [x0, #0xf]
    // 0x7b6d98: DecompressPointer r2
    //     0x7b6d98: add             x2, x2, HEAP, lsl #32
    // 0x7b6d9c: cmp             w2, w1
    // 0x7b6da0: b.ne            #0x7b6dac
    // 0x7b6da4: r0 = Null
    //     0x7b6da4: mov             x0, NULL
    // 0x7b6da8: b               #0x7b6db0
    // 0x7b6dac: mov             x0, x1
    // 0x7b6db0: cmp             w0, NULL
    // 0x7b6db4: b.ne            #0x7b6dc4
    // 0x7b6db8: r1 = Instance_PdfFontMetrics
    //     0x7b6db8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e120] Obj!PdfFontMetrics@e0c991
    //     0x7b6dbc: ldr             x1, [x1, #0x120]
    // 0x7b6dc0: b               #0x7b6dc8
    // 0x7b6dc4: mov             x1, x0
    // 0x7b6dc8: d0 = 0.000000
    //     0x7b6dc8: eor             v0.16b, v0.16b, v0.16b
    // 0x7b6dcc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7b6dcc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7b6dd0: r0 = copyWith()
    //     0x7b6dd0: bl              #0x7b6ea4  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::copyWith
    // 0x7b6dd4: LeaveFrame
    //     0x7b6dd4: mov             SP, fp
    //     0x7b6dd8: ldp             fp, lr, [SP], #0x10
    // 0x7b6ddc: ret
    //     0x7b6ddc: ret             
    // 0x7b6de0: ldur            x0, [fp, #-0x10]
    // 0x7b6de4: LoadField: r3 = r0->field_1f
    //     0x7b6de4: ldur            w3, [x0, #0x1f]
    // 0x7b6de8: DecompressPointer r3
    //     0x7b6de8: add             x3, x3, HEAP, lsl #32
    // 0x7b6dec: mov             x1, x3
    // 0x7b6df0: ldur            x2, [fp, #-8]
    // 0x7b6df4: stur            x3, [fp, #-0x18]
    // 0x7b6df8: r0 = _getValueOrData()
    //     0x7b6df8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7b6dfc: ldur            x1, [fp, #-0x18]
    // 0x7b6e00: LoadField: r2 = r1->field_f
    //     0x7b6e00: ldur            w2, [x1, #0xf]
    // 0x7b6e04: DecompressPointer r2
    //     0x7b6e04: add             x2, x2, HEAP, lsl #32
    // 0x7b6e08: cmp             w2, w0
    // 0x7b6e0c: b.ne            #0x7b6e18
    // 0x7b6e10: r1 = Null
    //     0x7b6e10: mov             x1, NULL
    // 0x7b6e14: b               #0x7b6e1c
    // 0x7b6e18: mov             x1, x0
    // 0x7b6e1c: cmp             w1, NULL
    // 0x7b6e20: b.ne            #0x7b6e30
    // 0x7b6e24: r0 = Instance_PdfFontMetrics
    //     0x7b6e24: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e120] Obj!PdfFontMetrics@e0c991
    //     0x7b6e28: ldr             x0, [x0, #0x120]
    // 0x7b6e2c: b               #0x7b6e34
    // 0x7b6e30: mov             x0, x1
    // 0x7b6e34: LeaveFrame
    //     0x7b6e34: mov             SP, fp
    //     0x7b6e38: ldp             fp, lr, [SP], #0x10
    // 0x7b6e3c: ret
    //     0x7b6e3c: ret             
    // 0x7b6e40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b6e40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b6e44: b               #0x7b6cf0
  }
  [closure] PdfFontMetrics glyphMetrics(dynamic, int) {
    // ** addr: 0x7b6e48, size: 0x3c
    // 0x7b6e48: EnterFrame
    //     0x7b6e48: stp             fp, lr, [SP, #-0x10]!
    //     0x7b6e4c: mov             fp, SP
    // 0x7b6e50: ldr             x0, [fp, #0x18]
    // 0x7b6e54: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7b6e54: ldur            w1, [x0, #0x17]
    // 0x7b6e58: DecompressPointer r1
    //     0x7b6e58: add             x1, x1, HEAP, lsl #32
    // 0x7b6e5c: CheckStackOverflow
    //     0x7b6e5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b6e60: cmp             SP, x16
    //     0x7b6e64: b.ls            #0x7b6e7c
    // 0x7b6e68: ldr             x2, [fp, #0x10]
    // 0x7b6e6c: r0 = glyphMetrics()
    //     0x7b6e6c: bl              #0x7b6cd0  ; [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::glyphMetrics
    // 0x7b6e70: LeaveFrame
    //     0x7b6e70: mov             SP, fp
    //     0x7b6e74: ldp             fp, lr, [SP], #0x10
    // 0x7b6e78: ret
    //     0x7b6e78: ret             
    // 0x7b6e7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b6e7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b6e80: b               #0x7b6e68
  }
  _ _buildType0(/* No info */) {
    // ** addr: 0x7b8280, size: 0x978
    // 0x7b8280: EnterFrame
    //     0x7b8280: stp             fp, lr, [SP, #-0x10]!
    //     0x7b8284: mov             fp, SP
    // 0x7b8288: AllocStack(0x58)
    //     0x7b8288: sub             SP, SP, #0x58
    // 0x7b828c: SetupParameters(PdfTtfFont this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x7b828c: stur            x1, [fp, #-0x10]
    //     0x7b8290: stur            x2, [fp, #-0x18]
    // 0x7b8294: CheckStackOverflow
    //     0x7b8294: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b8298: cmp             SP, x16
    //     0x7b829c: b.ls            #0x7b8b94
    // 0x7b82a0: LoadField: r0 = r1->field_3f
    //     0x7b82a0: ldur            w0, [x1, #0x3f]
    // 0x7b82a4: DecompressPointer r0
    //     0x7b82a4: add             x0, x0, HEAP, lsl #32
    // 0x7b82a8: stur            x0, [fp, #-8]
    // 0x7b82ac: r0 = TtfWriter()
    //     0x7b82ac: bl              #0x7c9130  ; AllocateTtfWriterStub -> TtfWriter (size=0xc)
    // 0x7b82b0: mov             x1, x0
    // 0x7b82b4: ldur            x0, [fp, #-8]
    // 0x7b82b8: StoreField: r1->field_7 = r0
    //     0x7b82b8: stur            w0, [x1, #7]
    // 0x7b82bc: ldur            x3, [fp, #-0x10]
    // 0x7b82c0: LoadField: r2 = r3->field_2f
    //     0x7b82c0: ldur            w2, [x3, #0x2f]
    // 0x7b82c4: DecompressPointer r2
    //     0x7b82c4: add             x2, x2, HEAP, lsl #32
    // 0x7b82c8: r16 = Sentinel
    //     0x7b82c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b82cc: cmp             w2, w16
    // 0x7b82d0: b.eq            #0x7b8b9c
    // 0x7b82d4: LoadField: r4 = r2->field_33
    //     0x7b82d4: ldur            w4, [x2, #0x33]
    // 0x7b82d8: DecompressPointer r4
    //     0x7b82d8: add             x4, x4, HEAP, lsl #32
    // 0x7b82dc: mov             x2, x4
    // 0x7b82e0: r0 = withChars()
    //     0x7b82e0: bl              #0x7b91c0  ; [package:pdf/src/pdf/font/ttf_writer.dart] TtfWriter::withChars
    // 0x7b82e4: mov             x3, x0
    // 0x7b82e8: ldur            x0, [fp, #-0x10]
    // 0x7b82ec: stur            x3, [fp, #-0x20]
    // 0x7b82f0: LoadField: r1 = r0->field_37
    //     0x7b82f0: ldur            w1, [x0, #0x37]
    // 0x7b82f4: DecompressPointer r1
    //     0x7b82f4: add             x1, x1, HEAP, lsl #32
    // 0x7b82f8: r16 = Sentinel
    //     0x7b82f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b82fc: cmp             w1, w16
    // 0x7b8300: b.eq            #0x7b8ba8
    // 0x7b8304: LoadField: r2 = r1->field_2b
    //     0x7b8304: ldur            w2, [x1, #0x2b]
    // 0x7b8308: DecompressPointer r2
    //     0x7b8308: add             x2, x2, HEAP, lsl #32
    // 0x7b830c: mov             x1, x2
    // 0x7b8310: mov             x2, x3
    // 0x7b8314: r0 = putBytes()
    //     0x7b8314: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0x7b8318: ldur            x1, [fp, #-0x10]
    // 0x7b831c: LoadField: r0 = r1->field_37
    //     0x7b831c: ldur            w0, [x1, #0x37]
    // 0x7b8320: DecompressPointer r0
    //     0x7b8320: add             x0, x0, HEAP, lsl #32
    // 0x7b8324: LoadField: r2 = r0->field_1b
    //     0x7b8324: ldur            w2, [x0, #0x1b]
    // 0x7b8328: DecompressPointer r2
    //     0x7b8328: add             x2, x2, HEAP, lsl #32
    // 0x7b832c: ldur            x0, [fp, #-0x20]
    // 0x7b8330: stur            x2, [fp, #-0x30]
    // 0x7b8334: LoadField: r3 = r0->field_13
    //     0x7b8334: ldur            w3, [x0, #0x13]
    // 0x7b8338: stur            x3, [fp, #-0x28]
    // 0x7b833c: r0 = PdfNum()
    //     0x7b833c: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b8340: mov             x3, x0
    // 0x7b8344: ldur            x0, [fp, #-0x28]
    // 0x7b8348: stur            x3, [fp, #-0x20]
    // 0x7b834c: StoreField: r3->field_7 = r0
    //     0x7b834c: stur            w0, [x3, #7]
    // 0x7b8350: ldur            x4, [fp, #-0x30]
    // 0x7b8354: LoadField: r2 = r4->field_7
    //     0x7b8354: ldur            w2, [x4, #7]
    // 0x7b8358: DecompressPointer r2
    //     0x7b8358: add             x2, x2, HEAP, lsl #32
    // 0x7b835c: mov             x0, x3
    // 0x7b8360: r1 = Null
    //     0x7b8360: mov             x1, NULL
    // 0x7b8364: cmp             w2, NULL
    // 0x7b8368: b.eq            #0x7b838c
    // 0x7b836c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b836c: ldur            w4, [x2, #0x17]
    // 0x7b8370: DecompressPointer r4
    //     0x7b8370: add             x4, x4, HEAP, lsl #32
    // 0x7b8374: r8 = X0 bound PdfDataType
    //     0x7b8374: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b8378: ldr             x8, [x8, #0x6d8]
    // 0x7b837c: LoadField: r9 = r4->field_7
    //     0x7b837c: ldur            x9, [x4, #7]
    // 0x7b8380: r3 = Null
    //     0x7b8380: add             x3, PP, #0x47, lsl #12  ; [pp+0x475f0] Null
    //     0x7b8384: ldr             x3, [x3, #0x5f0]
    // 0x7b8388: blr             x9
    // 0x7b838c: ldur            x0, [fp, #-0x30]
    // 0x7b8390: LoadField: r1 = r0->field_b
    //     0x7b8390: ldur            w1, [x0, #0xb]
    // 0x7b8394: DecompressPointer r1
    //     0x7b8394: add             x1, x1, HEAP, lsl #32
    // 0x7b8398: ldur            x3, [fp, #-0x20]
    // 0x7b839c: r2 = "/Length1"
    //     0x7b839c: add             x2, PP, #0x47, lsl #12  ; [pp+0x47558] "/Length1"
    //     0x7b83a0: ldr             x2, [x2, #0x558]
    // 0x7b83a4: r0 = []=()
    //     0x7b83a4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b83a8: r1 = Null
    //     0x7b83a8: mov             x1, NULL
    // 0x7b83ac: r2 = 36
    //     0x7b83ac: movz            x2, #0x24
    // 0x7b83b0: r0 = AllocateArray()
    //     0x7b83b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b83b4: stur            x0, [fp, #-0x20]
    // 0x7b83b8: r16 = "/Type"
    //     0x7b83b8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0x7b83bc: ldr             x16, [x16, #0x630]
    // 0x7b83c0: StoreField: r0->field_f = r16
    //     0x7b83c0: stur            w16, [x0, #0xf]
    // 0x7b83c4: r16 = Instance_PdfName
    //     0x7b83c4: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df40] Obj!PdfName@e0c821
    //     0x7b83c8: ldr             x16, [x16, #0xf40]
    // 0x7b83cc: StoreField: r0->field_13 = r16
    //     0x7b83cc: stur            w16, [x0, #0x13]
    // 0x7b83d0: r16 = "/BaseFont"
    //     0x7b83d0: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3de60] "/BaseFont"
    //     0x7b83d4: ldr             x16, [x16, #0xe60]
    // 0x7b83d8: ArrayStore: r0[0] = r16  ; List_4
    //     0x7b83d8: stur            w16, [x0, #0x17]
    // 0x7b83dc: r1 = Null
    //     0x7b83dc: mov             x1, NULL
    // 0x7b83e0: r2 = 4
    //     0x7b83e0: movz            x2, #0x4
    // 0x7b83e4: r0 = AllocateArray()
    //     0x7b83e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b83e8: stur            x0, [fp, #-0x28]
    // 0x7b83ec: r16 = "/"
    //     0x7b83ec: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x7b83f0: StoreField: r0->field_f = r16
    //     0x7b83f0: stur            w16, [x0, #0xf]
    // 0x7b83f4: ldur            x1, [fp, #-8]
    // 0x7b83f8: r0 = fontName()
    //     0x7b83f8: bl              #0x7b73dc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::fontName
    // 0x7b83fc: ldur            x1, [fp, #-0x28]
    // 0x7b8400: ArrayStore: r1[1] = r0  ; List_4
    //     0x7b8400: add             x25, x1, #0x13
    //     0x7b8404: str             w0, [x25]
    //     0x7b8408: tbz             w0, #0, #0x7b8424
    //     0x7b840c: ldurb           w16, [x1, #-1]
    //     0x7b8410: ldurb           w17, [x0, #-1]
    //     0x7b8414: and             x16, x17, x16, lsr #2
    //     0x7b8418: tst             x16, HEAP, lsr #32
    //     0x7b841c: b.eq            #0x7b8424
    //     0x7b8420: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b8424: ldur            x16, [fp, #-0x28]
    // 0x7b8428: str             x16, [SP]
    // 0x7b842c: r0 = _interpolate()
    //     0x7b842c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7b8430: stur            x0, [fp, #-0x28]
    // 0x7b8434: r0 = PdfName()
    //     0x7b8434: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0x7b8438: mov             x1, x0
    // 0x7b843c: ldur            x0, [fp, #-0x28]
    // 0x7b8440: StoreField: r1->field_7 = r0
    //     0x7b8440: stur            w0, [x1, #7]
    // 0x7b8444: mov             x0, x1
    // 0x7b8448: ldur            x1, [fp, #-0x20]
    // 0x7b844c: ArrayStore: r1[3] = r0  ; List_4
    //     0x7b844c: add             x25, x1, #0x1b
    //     0x7b8450: str             w0, [x25]
    //     0x7b8454: tbz             w0, #0, #0x7b8470
    //     0x7b8458: ldurb           w16, [x1, #-1]
    //     0x7b845c: ldurb           w17, [x0, #-1]
    //     0x7b8460: and             x16, x17, x16, lsr #2
    //     0x7b8464: tst             x16, HEAP, lsr #32
    //     0x7b8468: b.eq            #0x7b8470
    //     0x7b846c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b8470: ldur            x0, [fp, #-0x20]
    // 0x7b8474: r16 = "/FontFile2"
    //     0x7b8474: add             x16, PP, #0x46, lsl #12  ; [pp+0x46fa8] "/FontFile2"
    //     0x7b8478: ldr             x16, [x16, #0xfa8]
    // 0x7b847c: StoreField: r0->field_1f = r16
    //     0x7b847c: stur            w16, [x0, #0x1f]
    // 0x7b8480: ldur            x2, [fp, #-0x10]
    // 0x7b8484: LoadField: r1 = r2->field_37
    //     0x7b8484: ldur            w1, [x2, #0x37]
    // 0x7b8488: DecompressPointer r1
    //     0x7b8488: add             x1, x1, HEAP, lsl #32
    // 0x7b848c: r0 = ref()
    //     0x7b848c: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7b8490: ldur            x1, [fp, #-0x20]
    // 0x7b8494: ArrayStore: r1[5] = r0  ; List_4
    //     0x7b8494: add             x25, x1, #0x23
    //     0x7b8498: str             w0, [x25]
    //     0x7b849c: tbz             w0, #0, #0x7b84b8
    //     0x7b84a0: ldurb           w16, [x1, #-1]
    //     0x7b84a4: ldurb           w17, [x0, #-1]
    //     0x7b84a8: and             x16, x17, x16, lsr #2
    //     0x7b84ac: tst             x16, HEAP, lsr #32
    //     0x7b84b0: b.eq            #0x7b84b8
    //     0x7b84b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b84b8: ldur            x0, [fp, #-0x20]
    // 0x7b84bc: r16 = "/FontDescriptor"
    //     0x7b84bc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df38] "/FontDescriptor"
    //     0x7b84c0: ldr             x16, [x16, #0xf38]
    // 0x7b84c4: StoreField: r0->field_27 = r16
    //     0x7b84c4: stur            w16, [x0, #0x27]
    // 0x7b84c8: ldur            x2, [fp, #-0x10]
    // 0x7b84cc: LoadField: r1 = r2->field_33
    //     0x7b84cc: ldur            w1, [x2, #0x33]
    // 0x7b84d0: DecompressPointer r1
    //     0x7b84d0: add             x1, x1, HEAP, lsl #32
    // 0x7b84d4: r16 = Sentinel
    //     0x7b84d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b84d8: cmp             w1, w16
    // 0x7b84dc: b.eq            #0x7b8bb4
    // 0x7b84e0: r0 = ref()
    //     0x7b84e0: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7b84e4: ldur            x1, [fp, #-0x20]
    // 0x7b84e8: ArrayStore: r1[7] = r0  ; List_4
    //     0x7b84e8: add             x25, x1, #0x2b
    //     0x7b84ec: str             w0, [x25]
    //     0x7b84f0: tbz             w0, #0, #0x7b850c
    //     0x7b84f4: ldurb           w16, [x1, #-1]
    //     0x7b84f8: ldurb           w17, [x0, #-1]
    //     0x7b84fc: and             x16, x17, x16, lsr #2
    //     0x7b8500: tst             x16, HEAP, lsr #32
    //     0x7b8504: b.eq            #0x7b850c
    //     0x7b8508: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b850c: ldur            x0, [fp, #-0x20]
    // 0x7b8510: r16 = "/W"
    //     0x7b8510: add             x16, PP, #0x36, lsl #12  ; [pp+0x36658] "/W"
    //     0x7b8514: ldr             x16, [x16, #0x658]
    // 0x7b8518: StoreField: r0->field_2f = r16
    //     0x7b8518: stur            w16, [x0, #0x2f]
    // 0x7b851c: ldur            x2, [fp, #-0x10]
    // 0x7b8520: LoadField: r1 = r2->field_3b
    //     0x7b8520: ldur            w1, [x2, #0x3b]
    // 0x7b8524: DecompressPointer r1
    //     0x7b8524: add             x1, x1, HEAP, lsl #32
    // 0x7b8528: r16 = Sentinel
    //     0x7b8528: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7b852c: cmp             w1, w16
    // 0x7b8530: b.eq            #0x7b8bc0
    // 0x7b8534: r0 = ref()
    //     0x7b8534: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7b8538: r1 = Null
    //     0x7b8538: mov             x1, NULL
    // 0x7b853c: r2 = 4
    //     0x7b853c: movz            x2, #0x4
    // 0x7b8540: stur            x0, [fp, #-0x28]
    // 0x7b8544: r0 = AllocateArray()
    //     0x7b8544: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b8548: stur            x0, [fp, #-0x30]
    // 0x7b854c: r16 = Instance_PdfNum
    //     0x7b854c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c148] Obj!PdfNum@e0c771
    //     0x7b8550: ldr             x16, [x16, #0x148]
    // 0x7b8554: StoreField: r0->field_f = r16
    //     0x7b8554: stur            w16, [x0, #0xf]
    // 0x7b8558: ldur            x1, [fp, #-0x28]
    // 0x7b855c: StoreField: r0->field_13 = r1
    //     0x7b855c: stur            w1, [x0, #0x13]
    // 0x7b8560: r1 = <PdfDataType>
    //     0x7b8560: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b8564: ldr             x1, [x1, #0x4c8]
    // 0x7b8568: r0 = AllocateGrowableArray()
    //     0x7b8568: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7b856c: mov             x2, x0
    // 0x7b8570: ldur            x0, [fp, #-0x30]
    // 0x7b8574: stur            x2, [fp, #-0x28]
    // 0x7b8578: StoreField: r2->field_f = r0
    //     0x7b8578: stur            w0, [x2, #0xf]
    // 0x7b857c: r0 = 4
    //     0x7b857c: movz            x0, #0x4
    // 0x7b8580: StoreField: r2->field_b = r0
    //     0x7b8580: stur            w0, [x2, #0xb]
    // 0x7b8584: r1 = <PdfDataType>
    //     0x7b8584: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b8588: ldr             x1, [x1, #0x4c8]
    // 0x7b858c: r0 = PdfArray()
    //     0x7b858c: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0x7b8590: stur            x0, [fp, #-0x30]
    // 0x7b8594: ldur            x16, [fp, #-0x28]
    // 0x7b8598: str             x16, [SP]
    // 0x7b859c: mov             x1, x0
    // 0x7b85a0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7b85a0: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7b85a4: r0 = PdfArray()
    //     0x7b85a4: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0x7b85a8: ldur            x1, [fp, #-0x20]
    // 0x7b85ac: ldur            x0, [fp, #-0x30]
    // 0x7b85b0: ArrayStore: r1[9] = r0  ; List_4
    //     0x7b85b0: add             x25, x1, #0x33
    //     0x7b85b4: str             w0, [x25]
    //     0x7b85b8: tbz             w0, #0, #0x7b85d4
    //     0x7b85bc: ldurb           w16, [x1, #-1]
    //     0x7b85c0: ldurb           w17, [x0, #-1]
    //     0x7b85c4: and             x16, x17, x16, lsr #2
    //     0x7b85c8: tst             x16, HEAP, lsr #32
    //     0x7b85cc: b.eq            #0x7b85d4
    //     0x7b85d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b85d4: ldur            x0, [fp, #-0x20]
    // 0x7b85d8: r16 = "/CIDToGIDMap"
    //     0x7b85d8: add             x16, PP, #0x47, lsl #12  ; [pp+0x47600] "/CIDToGIDMap"
    //     0x7b85dc: ldr             x16, [x16, #0x600]
    // 0x7b85e0: StoreField: r0->field_37 = r16
    //     0x7b85e0: stur            w16, [x0, #0x37]
    // 0x7b85e4: r16 = Instance_PdfName
    //     0x7b85e4: add             x16, PP, #0x47, lsl #12  ; [pp+0x47608] Obj!PdfName@e0c811
    //     0x7b85e8: ldr             x16, [x16, #0x608]
    // 0x7b85ec: StoreField: r0->field_3b = r16
    //     0x7b85ec: stur            w16, [x0, #0x3b]
    // 0x7b85f0: r16 = "/DW"
    //     0x7b85f0: add             x16, PP, #0x47, lsl #12  ; [pp+0x47610] "/DW"
    //     0x7b85f4: ldr             x16, [x16, #0x610]
    // 0x7b85f8: StoreField: r0->field_3f = r16
    //     0x7b85f8: stur            w16, [x0, #0x3f]
    // 0x7b85fc: r16 = Instance_PdfNum
    //     0x7b85fc: add             x16, PP, #0x47, lsl #12  ; [pp+0x47618] Obj!PdfNum@e0c781
    //     0x7b8600: ldr             x16, [x16, #0x618]
    // 0x7b8604: StoreField: r0->field_43 = r16
    //     0x7b8604: stur            w16, [x0, #0x43]
    // 0x7b8608: r16 = "/Subtype"
    //     0x7b8608: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea78] "/Subtype"
    //     0x7b860c: ldr             x16, [x16, #0xa78]
    // 0x7b8610: StoreField: r0->field_47 = r16
    //     0x7b8610: stur            w16, [x0, #0x47]
    // 0x7b8614: r16 = Instance_PdfName
    //     0x7b8614: add             x16, PP, #0x47, lsl #12  ; [pp+0x47620] Obj!PdfName@e0c801
    //     0x7b8618: ldr             x16, [x16, #0x620]
    // 0x7b861c: StoreField: r0->field_4b = r16
    //     0x7b861c: stur            w16, [x0, #0x4b]
    // 0x7b8620: r16 = "/CIDSystemInfo"
    //     0x7b8620: add             x16, PP, #0x47, lsl #12  ; [pp+0x47628] "/CIDSystemInfo"
    //     0x7b8624: ldr             x16, [x16, #0x628]
    // 0x7b8628: StoreField: r0->field_4f = r16
    //     0x7b8628: stur            w16, [x0, #0x4f]
    // 0x7b862c: r1 = Null
    //     0x7b862c: mov             x1, NULL
    // 0x7b8630: r2 = 12
    //     0x7b8630: movz            x2, #0xc
    // 0x7b8634: r0 = AllocateArray()
    //     0x7b8634: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b8638: stur            x0, [fp, #-0x28]
    // 0x7b863c: r16 = "/Supplement"
    //     0x7b863c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47630] "/Supplement"
    //     0x7b8640: ldr             x16, [x16, #0x630]
    // 0x7b8644: StoreField: r0->field_f = r16
    //     0x7b8644: stur            w16, [x0, #0xf]
    // 0x7b8648: r16 = Instance_PdfNum
    //     0x7b8648: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c148] Obj!PdfNum@e0c771
    //     0x7b864c: ldr             x16, [x16, #0x148]
    // 0x7b8650: StoreField: r0->field_13 = r16
    //     0x7b8650: stur            w16, [x0, #0x13]
    // 0x7b8654: r16 = "/Registry"
    //     0x7b8654: add             x16, PP, #0x47, lsl #12  ; [pp+0x47638] "/Registry"
    //     0x7b8658: ldr             x16, [x16, #0x638]
    // 0x7b865c: ArrayStore: r0[0] = r16  ; List_4
    //     0x7b865c: stur            w16, [x0, #0x17]
    // 0x7b8660: r1 = Null
    //     0x7b8660: mov             x1, NULL
    // 0x7b8664: r2 = "Adobe"
    //     0x7b8664: add             x2, PP, #0x47, lsl #12  ; [pp+0x47640] "Adobe"
    //     0x7b8668: ldr             x2, [x2, #0x640]
    // 0x7b866c: r0 = PdfString.fromString()
    //     0x7b866c: bl              #0x7b8bf8  ; [package:pdf/src/pdf/format/string.dart] PdfString::PdfString.fromString
    // 0x7b8670: ldur            x1, [fp, #-0x28]
    // 0x7b8674: ArrayStore: r1[3] = r0  ; List_4
    //     0x7b8674: add             x25, x1, #0x1b
    //     0x7b8678: str             w0, [x25]
    //     0x7b867c: tbz             w0, #0, #0x7b8698
    //     0x7b8680: ldurb           w16, [x1, #-1]
    //     0x7b8684: ldurb           w17, [x0, #-1]
    //     0x7b8688: and             x16, x17, x16, lsr #2
    //     0x7b868c: tst             x16, HEAP, lsr #32
    //     0x7b8690: b.eq            #0x7b8698
    //     0x7b8694: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b8698: ldur            x0, [fp, #-0x28]
    // 0x7b869c: r16 = "/Ordering"
    //     0x7b869c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47648] "/Ordering"
    //     0x7b86a0: ldr             x16, [x16, #0x648]
    // 0x7b86a4: StoreField: r0->field_1f = r16
    //     0x7b86a4: stur            w16, [x0, #0x1f]
    // 0x7b86a8: r1 = Null
    //     0x7b86a8: mov             x1, NULL
    // 0x7b86ac: r2 = "Identity-H"
    //     0x7b86ac: add             x2, PP, #0x47, lsl #12  ; [pp+0x47650] "Identity-H"
    //     0x7b86b0: ldr             x2, [x2, #0x650]
    // 0x7b86b4: r0 = PdfString.fromString()
    //     0x7b86b4: bl              #0x7b8bf8  ; [package:pdf/src/pdf/format/string.dart] PdfString::PdfString.fromString
    // 0x7b86b8: ldur            x1, [fp, #-0x28]
    // 0x7b86bc: ArrayStore: r1[5] = r0  ; List_4
    //     0x7b86bc: add             x25, x1, #0x23
    //     0x7b86c0: str             w0, [x25]
    //     0x7b86c4: tbz             w0, #0, #0x7b86e0
    //     0x7b86c8: ldurb           w16, [x1, #-1]
    //     0x7b86cc: ldurb           w17, [x0, #-1]
    //     0x7b86d0: and             x16, x17, x16, lsr #2
    //     0x7b86d4: tst             x16, HEAP, lsr #32
    //     0x7b86d8: b.eq            #0x7b86e0
    //     0x7b86dc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b86e0: r16 = <String, PdfDataType>
    //     0x7b86e0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0x7b86e4: ldr             x16, [x16, #0x820]
    // 0x7b86e8: ldur            lr, [fp, #-0x28]
    // 0x7b86ec: stp             lr, x16, [SP]
    // 0x7b86f0: r0 = Map._fromLiteral()
    //     0x7b86f0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b86f4: r1 = <PdfDataType>
    //     0x7b86f4: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b86f8: ldr             x1, [x1, #0x4c8]
    // 0x7b86fc: stur            x0, [fp, #-0x28]
    // 0x7b8700: r0 = PdfDict()
    //     0x7b8700: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7b8704: mov             x1, x0
    // 0x7b8708: ldur            x0, [fp, #-0x28]
    // 0x7b870c: StoreField: r1->field_b = r0
    //     0x7b870c: stur            w0, [x1, #0xb]
    // 0x7b8710: mov             x0, x1
    // 0x7b8714: ldur            x1, [fp, #-0x20]
    // 0x7b8718: ArrayStore: r1[17] = r0  ; List_4
    //     0x7b8718: add             x25, x1, #0x53
    //     0x7b871c: str             w0, [x25]
    //     0x7b8720: tbz             w0, #0, #0x7b873c
    //     0x7b8724: ldurb           w16, [x1, #-1]
    //     0x7b8728: ldurb           w17, [x0, #-1]
    //     0x7b872c: and             x16, x17, x16, lsr #2
    //     0x7b8730: tst             x16, HEAP, lsr #32
    //     0x7b8734: b.eq            #0x7b873c
    //     0x7b8738: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b873c: r16 = <String, PdfDataType>
    //     0x7b873c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0x7b8740: ldr             x16, [x16, #0x820]
    // 0x7b8744: ldur            lr, [fp, #-0x20]
    // 0x7b8748: stp             lr, x16, [SP]
    // 0x7b874c: r0 = Map._fromLiteral()
    //     0x7b874c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7b8750: r1 = <PdfDataType>
    //     0x7b8750: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0x7b8754: ldr             x1, [x1, #0x4c8]
    // 0x7b8758: stur            x0, [fp, #-0x20]
    // 0x7b875c: r0 = PdfDict()
    //     0x7b875c: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0x7b8760: mov             x3, x0
    // 0x7b8764: ldur            x0, [fp, #-0x20]
    // 0x7b8768: stur            x3, [fp, #-0x28]
    // 0x7b876c: StoreField: r3->field_b = r0
    //     0x7b876c: stur            w0, [x3, #0xb]
    // 0x7b8770: r1 = Null
    //     0x7b8770: mov             x1, NULL
    // 0x7b8774: r2 = 4
    //     0x7b8774: movz            x2, #0x4
    // 0x7b8778: r0 = AllocateArray()
    //     0x7b8778: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b877c: stur            x0, [fp, #-0x20]
    // 0x7b8780: r16 = "/"
    //     0x7b8780: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x7b8784: StoreField: r0->field_f = r16
    //     0x7b8784: stur            w16, [x0, #0xf]
    // 0x7b8788: ldur            x1, [fp, #-8]
    // 0x7b878c: r0 = fontName()
    //     0x7b878c: bl              #0x7b73dc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::fontName
    // 0x7b8790: ldur            x1, [fp, #-0x20]
    // 0x7b8794: ArrayStore: r1[1] = r0  ; List_4
    //     0x7b8794: add             x25, x1, #0x13
    //     0x7b8798: str             w0, [x25]
    //     0x7b879c: tbz             w0, #0, #0x7b87b8
    //     0x7b87a0: ldurb           w16, [x1, #-1]
    //     0x7b87a4: ldurb           w17, [x0, #-1]
    //     0x7b87a8: and             x16, x17, x16, lsr #2
    //     0x7b87ac: tst             x16, HEAP, lsr #32
    //     0x7b87b0: b.eq            #0x7b87b8
    //     0x7b87b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b87b8: ldur            x16, [fp, #-0x20]
    // 0x7b87bc: str             x16, [SP]
    // 0x7b87c0: r0 = _interpolate()
    //     0x7b87c0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7b87c4: stur            x0, [fp, #-8]
    // 0x7b87c8: r0 = PdfName()
    //     0x7b87c8: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0x7b87cc: mov             x3, x0
    // 0x7b87d0: ldur            x0, [fp, #-8]
    // 0x7b87d4: stur            x3, [fp, #-0x20]
    // 0x7b87d8: StoreField: r3->field_7 = r0
    //     0x7b87d8: stur            w0, [x3, #7]
    // 0x7b87dc: ldur            x4, [fp, #-0x18]
    // 0x7b87e0: LoadField: r5 = r4->field_7
    //     0x7b87e0: ldur            w5, [x4, #7]
    // 0x7b87e4: DecompressPointer r5
    //     0x7b87e4: add             x5, x5, HEAP, lsl #32
    // 0x7b87e8: mov             x0, x3
    // 0x7b87ec: mov             x2, x5
    // 0x7b87f0: stur            x5, [fp, #-8]
    // 0x7b87f4: r1 = Null
    //     0x7b87f4: mov             x1, NULL
    // 0x7b87f8: cmp             w2, NULL
    // 0x7b87fc: b.eq            #0x7b8820
    // 0x7b8800: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b8800: ldur            w4, [x2, #0x17]
    // 0x7b8804: DecompressPointer r4
    //     0x7b8804: add             x4, x4, HEAP, lsl #32
    // 0x7b8808: r8 = X0 bound PdfDataType
    //     0x7b8808: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b880c: ldr             x8, [x8, #0x6d8]
    // 0x7b8810: LoadField: r9 = r4->field_7
    //     0x7b8810: ldur            x9, [x4, #7]
    // 0x7b8814: r3 = Null
    //     0x7b8814: add             x3, PP, #0x47, lsl #12  ; [pp+0x47658] Null
    //     0x7b8818: ldr             x3, [x3, #0x658]
    // 0x7b881c: blr             x9
    // 0x7b8820: ldur            x0, [fp, #-0x18]
    // 0x7b8824: LoadField: r4 = r0->field_b
    //     0x7b8824: ldur            w4, [x0, #0xb]
    // 0x7b8828: DecompressPointer r4
    //     0x7b8828: add             x4, x4, HEAP, lsl #32
    // 0x7b882c: mov             x1, x4
    // 0x7b8830: ldur            x3, [fp, #-0x20]
    // 0x7b8834: stur            x4, [fp, #-0x30]
    // 0x7b8838: r2 = "/BaseFont"
    //     0x7b8838: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3de60] "/BaseFont"
    //     0x7b883c: ldr             x2, [x2, #0xe60]
    // 0x7b8840: r0 = []=()
    //     0x7b8840: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b8844: ldur            x2, [fp, #-8]
    // 0x7b8848: r0 = Instance_PdfName
    //     0x7b8848: add             x0, PP, #0x47, lsl #12  ; [pp+0x47668] Obj!PdfName@e0c7f1
    //     0x7b884c: ldr             x0, [x0, #0x668]
    // 0x7b8850: r1 = Null
    //     0x7b8850: mov             x1, NULL
    // 0x7b8854: cmp             w2, NULL
    // 0x7b8858: b.eq            #0x7b887c
    // 0x7b885c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b885c: ldur            w4, [x2, #0x17]
    // 0x7b8860: DecompressPointer r4
    //     0x7b8860: add             x4, x4, HEAP, lsl #32
    // 0x7b8864: r8 = X0 bound PdfDataType
    //     0x7b8864: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b8868: ldr             x8, [x8, #0x6d8]
    // 0x7b886c: LoadField: r9 = r4->field_7
    //     0x7b886c: ldur            x9, [x4, #7]
    // 0x7b8870: r3 = Null
    //     0x7b8870: add             x3, PP, #0x47, lsl #12  ; [pp+0x47670] Null
    //     0x7b8874: ldr             x3, [x3, #0x670]
    // 0x7b8878: blr             x9
    // 0x7b887c: ldur            x1, [fp, #-0x30]
    // 0x7b8880: r2 = "/Encoding"
    //     0x7b8880: add             x2, PP, #0x47, lsl #12  ; [pp+0x47680] "/Encoding"
    //     0x7b8884: ldr             x2, [x2, #0x680]
    // 0x7b8888: r3 = Instance_PdfName
    //     0x7b8888: add             x3, PP, #0x47, lsl #12  ; [pp+0x47668] Obj!PdfName@e0c7f1
    //     0x7b888c: ldr             x3, [x3, #0x668]
    // 0x7b8890: r0 = []=()
    //     0x7b8890: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b8894: r1 = Null
    //     0x7b8894: mov             x1, NULL
    // 0x7b8898: r2 = 2
    //     0x7b8898: movz            x2, #0x2
    // 0x7b889c: r0 = AllocateArray()
    //     0x7b889c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7b88a0: mov             x2, x0
    // 0x7b88a4: ldur            x0, [fp, #-0x28]
    // 0x7b88a8: stur            x2, [fp, #-0x18]
    // 0x7b88ac: StoreField: r2->field_f = r0
    //     0x7b88ac: stur            w0, [x2, #0xf]
    // 0x7b88b0: r1 = <PdfDict<PdfDataType>>
    //     0x7b88b0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0x7b88b4: ldr             x1, [x1, #0x758]
    // 0x7b88b8: r0 = AllocateGrowableArray()
    //     0x7b88b8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x7b88bc: mov             x2, x0
    // 0x7b88c0: ldur            x0, [fp, #-0x18]
    // 0x7b88c4: stur            x2, [fp, #-0x20]
    // 0x7b88c8: StoreField: r2->field_f = r0
    //     0x7b88c8: stur            w0, [x2, #0xf]
    // 0x7b88cc: r0 = 2
    //     0x7b88cc: movz            x0, #0x2
    // 0x7b88d0: StoreField: r2->field_b = r0
    //     0x7b88d0: stur            w0, [x2, #0xb]
    // 0x7b88d4: r1 = <PdfDict<PdfDataType>>
    //     0x7b88d4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0x7b88d8: ldr             x1, [x1, #0x758]
    // 0x7b88dc: r0 = PdfArray()
    //     0x7b88dc: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0x7b88e0: stur            x0, [fp, #-0x18]
    // 0x7b88e4: ldur            x16, [fp, #-0x20]
    // 0x7b88e8: str             x16, [SP]
    // 0x7b88ec: mov             x1, x0
    // 0x7b88f0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7b88f0: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7b88f4: r0 = PdfArray()
    //     0x7b88f4: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0x7b88f8: ldur            x0, [fp, #-0x18]
    // 0x7b88fc: ldur            x2, [fp, #-8]
    // 0x7b8900: r1 = Null
    //     0x7b8900: mov             x1, NULL
    // 0x7b8904: cmp             w2, NULL
    // 0x7b8908: b.eq            #0x7b892c
    // 0x7b890c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b890c: ldur            w4, [x2, #0x17]
    // 0x7b8910: DecompressPointer r4
    //     0x7b8910: add             x4, x4, HEAP, lsl #32
    // 0x7b8914: r8 = X0 bound PdfDataType
    //     0x7b8914: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b8918: ldr             x8, [x8, #0x6d8]
    // 0x7b891c: LoadField: r9 = r4->field_7
    //     0x7b891c: ldur            x9, [x4, #7]
    // 0x7b8920: r3 = Null
    //     0x7b8920: add             x3, PP, #0x47, lsl #12  ; [pp+0x47688] Null
    //     0x7b8924: ldr             x3, [x3, #0x688]
    // 0x7b8928: blr             x9
    // 0x7b892c: ldur            x1, [fp, #-0x30]
    // 0x7b8930: ldur            x3, [fp, #-0x18]
    // 0x7b8934: r2 = "/DescendantFonts"
    //     0x7b8934: add             x2, PP, #0x47, lsl #12  ; [pp+0x47698] "/DescendantFonts"
    //     0x7b8938: ldr             x2, [x2, #0x698]
    // 0x7b893c: r0 = []=()
    //     0x7b893c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b8940: ldur            x0, [fp, #-0x10]
    // 0x7b8944: LoadField: r1 = r0->field_2f
    //     0x7b8944: ldur            w1, [x0, #0x2f]
    // 0x7b8948: DecompressPointer r1
    //     0x7b8948: add             x1, x1, HEAP, lsl #32
    // 0x7b894c: r0 = ref()
    //     0x7b894c: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0x7b8950: ldur            x2, [fp, #-8]
    // 0x7b8954: mov             x3, x0
    // 0x7b8958: r1 = Null
    //     0x7b8958: mov             x1, NULL
    // 0x7b895c: stur            x3, [fp, #-8]
    // 0x7b8960: cmp             w2, NULL
    // 0x7b8964: b.eq            #0x7b8988
    // 0x7b8968: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b8968: ldur            w4, [x2, #0x17]
    // 0x7b896c: DecompressPointer r4
    //     0x7b896c: add             x4, x4, HEAP, lsl #32
    // 0x7b8970: r8 = X0 bound PdfDataType
    //     0x7b8970: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0x7b8974: ldr             x8, [x8, #0x6d8]
    // 0x7b8978: LoadField: r9 = r4->field_7
    //     0x7b8978: ldur            x9, [x4, #7]
    // 0x7b897c: r3 = Null
    //     0x7b897c: add             x3, PP, #0x47, lsl #12  ; [pp+0x476a0] Null
    //     0x7b8980: ldr             x3, [x3, #0x6a0]
    // 0x7b8984: blr             x9
    // 0x7b8988: ldur            x1, [fp, #-0x30]
    // 0x7b898c: ldur            x3, [fp, #-8]
    // 0x7b8990: r2 = "/ToUnicode"
    //     0x7b8990: add             x2, PP, #0x47, lsl #12  ; [pp+0x476b0] "/ToUnicode"
    //     0x7b8994: ldr             x2, [x2, #0x6b0]
    // 0x7b8998: r0 = []=()
    //     0x7b8998: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x7b899c: ldur            x3, [fp, #-0x10]
    // 0x7b89a0: LoadField: r0 = r3->field_2f
    //     0x7b89a0: ldur            w0, [x3, #0x2f]
    // 0x7b89a4: DecompressPointer r0
    //     0x7b89a4: add             x0, x0, HEAP, lsl #32
    // 0x7b89a8: LoadField: r1 = r0->field_33
    //     0x7b89a8: ldur            w1, [x0, #0x33]
    // 0x7b89ac: DecompressPointer r1
    //     0x7b89ac: add             x1, x1, HEAP, lsl #32
    // 0x7b89b0: LoadField: r0 = r1->field_b
    //     0x7b89b0: ldur            w0, [x1, #0xb]
    // 0x7b89b4: r1 = LoadInt32Instr(r0)
    //     0x7b89b4: sbfx            x1, x0, #1, #0x1f
    // 0x7b89b8: sub             x4, x1, #1
    // 0x7b89bc: stur            x4, [fp, #-0x40]
    // 0x7b89c0: r5 = 0
    //     0x7b89c0: movz            x5, #0
    // 0x7b89c4: stur            x5, [fp, #-0x38]
    // 0x7b89c8: CheckStackOverflow
    //     0x7b89c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b89cc: cmp             SP, x16
    //     0x7b89d0: b.ls            #0x7b8bcc
    // 0x7b89d4: cmp             x5, x4
    // 0x7b89d8: b.gt            #0x7b8b84
    // 0x7b89dc: LoadField: r0 = r3->field_3b
    //     0x7b89dc: ldur            w0, [x3, #0x3b]
    // 0x7b89e0: DecompressPointer r0
    //     0x7b89e0: add             x0, x0, HEAP, lsl #32
    // 0x7b89e4: LoadField: r6 = r0->field_1b
    //     0x7b89e4: ldur            w6, [x0, #0x1b]
    // 0x7b89e8: DecompressPointer r6
    //     0x7b89e8: add             x6, x6, HEAP, lsl #32
    // 0x7b89ec: stur            x6, [fp, #-8]
    // 0x7b89f0: LoadField: r0 = r3->field_2f
    //     0x7b89f0: ldur            w0, [x3, #0x2f]
    // 0x7b89f4: DecompressPointer r0
    //     0x7b89f4: add             x0, x0, HEAP, lsl #32
    // 0x7b89f8: LoadField: r2 = r0->field_33
    //     0x7b89f8: ldur            w2, [x0, #0x33]
    // 0x7b89fc: DecompressPointer r2
    //     0x7b89fc: add             x2, x2, HEAP, lsl #32
    // 0x7b8a00: LoadField: r0 = r2->field_b
    //     0x7b8a00: ldur            w0, [x2, #0xb]
    // 0x7b8a04: r1 = LoadInt32Instr(r0)
    //     0x7b8a04: sbfx            x1, x0, #1, #0x1f
    // 0x7b8a08: mov             x0, x1
    // 0x7b8a0c: mov             x1, x5
    // 0x7b8a10: cmp             x1, x0
    // 0x7b8a14: b.hs            #0x7b8bd4
    // 0x7b8a18: LoadField: r0 = r2->field_f
    //     0x7b8a18: ldur            w0, [x2, #0xf]
    // 0x7b8a1c: DecompressPointer r0
    //     0x7b8a1c: add             x0, x0, HEAP, lsl #32
    // 0x7b8a20: ArrayLoad: r2 = r0[r5]  ; Unknown_4
    //     0x7b8a20: add             x16, x0, x5, lsl #2
    //     0x7b8a24: ldur            w2, [x16, #0xf]
    // 0x7b8a28: DecompressPointer r2
    //     0x7b8a28: add             x2, x2, HEAP, lsl #32
    // 0x7b8a2c: mov             x1, x3
    // 0x7b8a30: r0 = glyphMetrics()
    //     0x7b8a30: bl              #0x7b6cd0  ; [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::glyphMetrics
    // 0x7b8a34: LoadField: d0 = r0->field_37
    //     0x7b8a34: ldur            d0, [x0, #0x37]
    // 0x7b8a38: d1 = 1000.000000
    //     0x7b8a38: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x7b8a3c: ldr             d1, [x17, #0x238]
    // 0x7b8a40: fmul            d2, d0, d1
    // 0x7b8a44: fcmp            d2, d2
    // 0x7b8a48: b.vs            #0x7b8bd8
    // 0x7b8a4c: fcvtzs          x0, d2
    // 0x7b8a50: asr             x16, x0, #0x1e
    // 0x7b8a54: cmp             x16, x0, asr #63
    // 0x7b8a58: b.ne            #0x7b8bd8
    // 0x7b8a5c: lsl             x0, x0, #1
    // 0x7b8a60: stur            x0, [fp, #-0x18]
    // 0x7b8a64: r0 = PdfNum()
    //     0x7b8a64: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0x7b8a68: mov             x3, x0
    // 0x7b8a6c: ldur            x0, [fp, #-0x18]
    // 0x7b8a70: stur            x3, [fp, #-0x20]
    // 0x7b8a74: StoreField: r3->field_7 = r0
    //     0x7b8a74: stur            w0, [x3, #7]
    // 0x7b8a78: ldur            x4, [fp, #-8]
    // 0x7b8a7c: LoadField: r2 = r4->field_7
    //     0x7b8a7c: ldur            w2, [x4, #7]
    // 0x7b8a80: DecompressPointer r2
    //     0x7b8a80: add             x2, x2, HEAP, lsl #32
    // 0x7b8a84: mov             x0, x3
    // 0x7b8a88: r1 = Null
    //     0x7b8a88: mov             x1, NULL
    // 0x7b8a8c: cmp             w2, NULL
    // 0x7b8a90: b.eq            #0x7b8ab4
    // 0x7b8a94: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b8a94: ldur            w4, [x2, #0x17]
    // 0x7b8a98: DecompressPointer r4
    //     0x7b8a98: add             x4, x4, HEAP, lsl #32
    // 0x7b8a9c: r8 = X0 bound PdfDataType
    //     0x7b8a9c: add             x8, PP, #0x47, lsl #12  ; [pp+0x47580] TypeParameter: X0 bound PdfDataType
    //     0x7b8aa0: ldr             x8, [x8, #0x580]
    // 0x7b8aa4: LoadField: r9 = r4->field_7
    //     0x7b8aa4: ldur            x9, [x4, #7]
    // 0x7b8aa8: r3 = Null
    //     0x7b8aa8: add             x3, PP, #0x47, lsl #12  ; [pp+0x476b8] Null
    //     0x7b8aac: ldr             x3, [x3, #0x6b8]
    // 0x7b8ab0: blr             x9
    // 0x7b8ab4: ldur            x0, [fp, #-8]
    // 0x7b8ab8: LoadField: r3 = r0->field_b
    //     0x7b8ab8: ldur            w3, [x0, #0xb]
    // 0x7b8abc: DecompressPointer r3
    //     0x7b8abc: add             x3, x3, HEAP, lsl #32
    // 0x7b8ac0: stur            x3, [fp, #-0x18]
    // 0x7b8ac4: LoadField: r2 = r3->field_7
    //     0x7b8ac4: ldur            w2, [x3, #7]
    // 0x7b8ac8: DecompressPointer r2
    //     0x7b8ac8: add             x2, x2, HEAP, lsl #32
    // 0x7b8acc: ldur            x0, [fp, #-0x20]
    // 0x7b8ad0: r1 = Null
    //     0x7b8ad0: mov             x1, NULL
    // 0x7b8ad4: cmp             w2, NULL
    // 0x7b8ad8: b.eq            #0x7b8af8
    // 0x7b8adc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b8adc: ldur            w4, [x2, #0x17]
    // 0x7b8ae0: DecompressPointer r4
    //     0x7b8ae0: add             x4, x4, HEAP, lsl #32
    // 0x7b8ae4: r8 = X0
    //     0x7b8ae4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7b8ae8: LoadField: r9 = r4->field_7
    //     0x7b8ae8: ldur            x9, [x4, #7]
    // 0x7b8aec: r3 = Null
    //     0x7b8aec: add             x3, PP, #0x47, lsl #12  ; [pp+0x476c8] Null
    //     0x7b8af0: ldr             x3, [x3, #0x6c8]
    // 0x7b8af4: blr             x9
    // 0x7b8af8: ldur            x0, [fp, #-0x18]
    // 0x7b8afc: LoadField: r1 = r0->field_b
    //     0x7b8afc: ldur            w1, [x0, #0xb]
    // 0x7b8b00: LoadField: r2 = r0->field_f
    //     0x7b8b00: ldur            w2, [x0, #0xf]
    // 0x7b8b04: DecompressPointer r2
    //     0x7b8b04: add             x2, x2, HEAP, lsl #32
    // 0x7b8b08: LoadField: r3 = r2->field_b
    //     0x7b8b08: ldur            w3, [x2, #0xb]
    // 0x7b8b0c: r2 = LoadInt32Instr(r1)
    //     0x7b8b0c: sbfx            x2, x1, #1, #0x1f
    // 0x7b8b10: stur            x2, [fp, #-0x48]
    // 0x7b8b14: r1 = LoadInt32Instr(r3)
    //     0x7b8b14: sbfx            x1, x3, #1, #0x1f
    // 0x7b8b18: cmp             x2, x1
    // 0x7b8b1c: b.ne            #0x7b8b28
    // 0x7b8b20: mov             x1, x0
    // 0x7b8b24: r0 = _growToNextCapacity()
    //     0x7b8b24: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7b8b28: ldur            x4, [fp, #-0x38]
    // 0x7b8b2c: ldur            x2, [fp, #-0x18]
    // 0x7b8b30: ldur            x3, [fp, #-0x48]
    // 0x7b8b34: add             x5, x3, #1
    // 0x7b8b38: lsl             x6, x5, #1
    // 0x7b8b3c: StoreField: r2->field_b = r6
    //     0x7b8b3c: stur            w6, [x2, #0xb]
    // 0x7b8b40: LoadField: r1 = r2->field_f
    //     0x7b8b40: ldur            w1, [x2, #0xf]
    // 0x7b8b44: DecompressPointer r1
    //     0x7b8b44: add             x1, x1, HEAP, lsl #32
    // 0x7b8b48: ldur            x0, [fp, #-0x20]
    // 0x7b8b4c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x7b8b4c: add             x25, x1, x3, lsl #2
    //     0x7b8b50: add             x25, x25, #0xf
    //     0x7b8b54: str             w0, [x25]
    //     0x7b8b58: tbz             w0, #0, #0x7b8b74
    //     0x7b8b5c: ldurb           w16, [x1, #-1]
    //     0x7b8b60: ldurb           w17, [x0, #-1]
    //     0x7b8b64: and             x16, x17, x16, lsr #2
    //     0x7b8b68: tst             x16, HEAP, lsr #32
    //     0x7b8b6c: b.eq            #0x7b8b74
    //     0x7b8b70: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7b8b74: add             x5, x4, #1
    // 0x7b8b78: ldur            x3, [fp, #-0x10]
    // 0x7b8b7c: ldur            x4, [fp, #-0x40]
    // 0x7b8b80: b               #0x7b89c4
    // 0x7b8b84: r0 = Null
    //     0x7b8b84: mov             x0, NULL
    // 0x7b8b88: LeaveFrame
    //     0x7b8b88: mov             SP, fp
    //     0x7b8b8c: ldp             fp, lr, [SP], #0x10
    // 0x7b8b90: ret
    //     0x7b8b90: ret             
    // 0x7b8b94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b8b94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b8b98: b               #0x7b82a0
    // 0x7b8b9c: r9 = unicodeCMap
    //     0x7b8b9c: add             x9, PP, #0x47, lsl #12  ; [pp+0x476d8] Field <PdfTtfFont.unicodeCMap>: late (offset: 0x30)
    //     0x7b8ba0: ldr             x9, [x9, #0x6d8]
    // 0x7b8ba4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b8ba4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7b8ba8: r9 = file
    //     0x7b8ba8: add             x9, PP, #0x47, lsl #12  ; [pp+0x475d8] Field <PdfTtfFont.file>: late (offset: 0x38)
    //     0x7b8bac: ldr             x9, [x9, #0x5d8]
    // 0x7b8bb0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b8bb0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7b8bb4: r9 = descriptor
    //     0x7b8bb4: add             x9, PP, #0x47, lsl #12  ; [pp+0x475e0] Field <PdfTtfFont.descriptor>: late (offset: 0x34)
    //     0x7b8bb8: ldr             x9, [x9, #0x5e0]
    // 0x7b8bbc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b8bbc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7b8bc0: r9 = widthsObject
    //     0x7b8bc0: add             x9, PP, #0x47, lsl #12  ; [pp+0x475e8] Field <PdfTtfFont.widthsObject>: late (offset: 0x3c)
    //     0x7b8bc4: ldr             x9, [x9, #0x5e8]
    // 0x7b8bc8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7b8bc8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7b8bcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b8bcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b8bd0: b               #0x7b89d4
    // 0x7b8bd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7b8bd4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7b8bd8: stp             q1, q2, [SP, #-0x20]!
    // 0x7b8bdc: d0 = 0.000000
    //     0x7b8bdc: fmov            d0, d2
    // 0x7b8be0: r0 = 74
    //     0x7b8be0: movz            x0, #0x4a
    // 0x7b8be4: r30 = DoubleToIntegerStub
    //     0x7b8be4: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x7b8be8: LoadField: r30 = r30->field_7
    //     0x7b8be8: ldur            lr, [lr, #7]
    // 0x7b8bec: blr             lr
    // 0x7b8bf0: ldp             q1, q2, [SP], #0x20
    // 0x7b8bf4: b               #0x7b8a60
  }
  _ PdfTtfFont(/* No info */) {
    // ** addr: 0xe65c90, size: 0x1f4
    // 0xe65c90: EnterFrame
    //     0xe65c90: stp             fp, lr, [SP, #-0x10]!
    //     0xe65c94: mov             fp, SP
    // 0xe65c98: AllocStack(0x20)
    //     0xe65c98: sub             SP, SP, #0x20
    // 0xe65c9c: r0 = Sentinel
    //     0xe65c9c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe65ca0: stur            x1, [fp, #-8]
    // 0xe65ca4: mov             x16, x2
    // 0xe65ca8: mov             x2, x1
    // 0xe65cac: mov             x1, x16
    // 0xe65cb0: mov             x16, x3
    // 0xe65cb4: mov             x3, x2
    // 0xe65cb8: mov             x2, x16
    // 0xe65cbc: stur            x1, [fp, #-0x10]
    // 0xe65cc0: stur            x2, [fp, #-0x18]
    // 0xe65cc4: CheckStackOverflow
    //     0xe65cc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe65cc8: cmp             SP, x16
    //     0xe65ccc: b.ls            #0xe65e7c
    // 0xe65cd0: StoreField: r3->field_2f = r0
    //     0xe65cd0: stur            w0, [x3, #0x2f]
    // 0xe65cd4: StoreField: r3->field_33 = r0
    //     0xe65cd4: stur            w0, [x3, #0x33]
    // 0xe65cd8: StoreField: r3->field_37 = r0
    //     0xe65cd8: stur            w0, [x3, #0x37]
    // 0xe65cdc: StoreField: r3->field_3b = r0
    //     0xe65cdc: stur            w0, [x3, #0x3b]
    // 0xe65ce0: r0 = TtfParser()
    //     0xe65ce0: bl              #0xc39db8  ; AllocateTtfParserStub -> TtfParser (size=0x28)
    // 0xe65ce4: mov             x1, x0
    // 0xe65ce8: ldur            x2, [fp, #-0x18]
    // 0xe65cec: stur            x0, [fp, #-0x18]
    // 0xe65cf0: r0 = TtfParser()
    //     0xe65cf0: bl              #0xc369f4  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::TtfParser
    // 0xe65cf4: ldur            x0, [fp, #-0x18]
    // 0xe65cf8: ldur            x4, [fp, #-8]
    // 0xe65cfc: StoreField: r4->field_3f = r0
    //     0xe65cfc: stur            w0, [x4, #0x3f]
    //     0xe65d00: ldurb           w16, [x4, #-1]
    //     0xe65d04: ldurb           w17, [x0, #-1]
    //     0xe65d08: and             x16, x17, x16, lsr #2
    //     0xe65d0c: tst             x16, HEAP, lsr #32
    //     0xe65d10: b.eq            #0xe65d18
    //     0xe65d14: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe65d18: mov             x1, x4
    // 0xe65d1c: ldur            x2, [fp, #-0x10]
    // 0xe65d20: r3 = "/TrueType"
    //     0xe65d20: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e0a0] "/TrueType"
    //     0xe65d24: ldr             x3, [x3, #0xa0]
    // 0xe65d28: r0 = PdfFont.create()
    //     0xe65d28: bl              #0xe6604c  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.create
    // 0xe65d2c: r1 = <PdfDict<PdfDataType>>
    //     0xe65d2c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe65d30: ldr             x1, [x1, #0x758]
    // 0xe65d34: r0 = PdfObjectStream()
    //     0xe65d34: bl              #0xe66040  ; AllocatePdfObjectStreamStub -> PdfObjectStream (size=0x34)
    // 0xe65d38: stur            x0, [fp, #-0x18]
    // 0xe65d3c: r16 = true
    //     0xe65d3c: add             x16, NULL, #0x20  ; true
    // 0xe65d40: str             x16, [SP]
    // 0xe65d44: mov             x1, x0
    // 0xe65d48: ldur            x2, [fp, #-0x10]
    // 0xe65d4c: r4 = const [0, 0x3, 0x1, 0x2, isBinary, 0x2, null]
    //     0xe65d4c: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e0a8] List(7) [0, 0x3, 0x1, 0x2, "isBinary", 0x2, Null]
    //     0xe65d50: ldr             x4, [x4, #0xa8]
    // 0xe65d54: r0 = PdfObjectStream()
    //     0xe65d54: bl              #0xe4714c  ; [package:pdf/src/pdf/obj/object_stream.dart] PdfObjectStream::PdfObjectStream
    // 0xe65d58: ldur            x0, [fp, #-0x18]
    // 0xe65d5c: ldur            x2, [fp, #-8]
    // 0xe65d60: StoreField: r2->field_37 = r0
    //     0xe65d60: stur            w0, [x2, #0x37]
    //     0xe65d64: ldurb           w16, [x2, #-1]
    //     0xe65d68: ldurb           w17, [x0, #-1]
    //     0xe65d6c: and             x16, x17, x16, lsr #2
    //     0xe65d70: tst             x16, HEAP, lsr #32
    //     0xe65d74: b.eq            #0xe65d7c
    //     0xe65d78: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe65d7c: r1 = <PdfDict<PdfDataType>>
    //     0xe65d7c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe65d80: ldr             x1, [x1, #0x758]
    // 0xe65d84: r0 = PdfUnicodeCmap()
    //     0xe65d84: bl              #0xe66034  ; AllocatePdfUnicodeCmapStub -> PdfUnicodeCmap (size=0x3c)
    // 0xe65d88: mov             x1, x0
    // 0xe65d8c: ldur            x2, [fp, #-0x10]
    // 0xe65d90: stur            x0, [fp, #-0x18]
    // 0xe65d94: r0 = PdfUnicodeCmap()
    //     0xe65d94: bl              #0xe65f88  ; [package:pdf/src/pdf/obj/unicode_cmap.dart] PdfUnicodeCmap::PdfUnicodeCmap
    // 0xe65d98: ldur            x0, [fp, #-0x18]
    // 0xe65d9c: ldur            x2, [fp, #-8]
    // 0xe65da0: StoreField: r2->field_2f = r0
    //     0xe65da0: stur            w0, [x2, #0x2f]
    //     0xe65da4: ldurb           w16, [x2, #-1]
    //     0xe65da8: ldurb           w17, [x0, #-1]
    //     0xe65dac: and             x16, x17, x16, lsr #2
    //     0xe65db0: tst             x16, HEAP, lsr #32
    //     0xe65db4: b.eq            #0xe65dbc
    //     0xe65db8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe65dbc: LoadField: r3 = r2->field_37
    //     0xe65dbc: ldur            w3, [x2, #0x37]
    // 0xe65dc0: DecompressPointer r3
    //     0xe65dc0: add             x3, x3, HEAP, lsl #32
    // 0xe65dc4: stur            x3, [fp, #-0x18]
    // 0xe65dc8: r1 = <PdfDict<PdfDataType>>
    //     0xe65dc8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe65dcc: ldr             x1, [x1, #0x758]
    // 0xe65dd0: r0 = PdfFontDescriptor()
    //     0xe65dd0: bl              #0xe65f7c  ; AllocatePdfFontDescriptorStub -> PdfFontDescriptor (size=0x34)
    // 0xe65dd4: mov             x1, x0
    // 0xe65dd8: ldur            x2, [fp, #-8]
    // 0xe65ddc: ldur            x3, [fp, #-0x18]
    // 0xe65de0: stur            x0, [fp, #-0x18]
    // 0xe65de4: r0 = PdfFontDescriptor()
    //     0xe65de4: bl              #0xe65e90  ; [package:pdf/src/pdf/obj/font_descriptor.dart] PdfFontDescriptor::PdfFontDescriptor
    // 0xe65de8: ldur            x0, [fp, #-0x18]
    // 0xe65dec: ldur            x2, [fp, #-8]
    // 0xe65df0: StoreField: r2->field_33 = r0
    //     0xe65df0: stur            w0, [x2, #0x33]
    //     0xe65df4: ldurb           w16, [x2, #-1]
    //     0xe65df8: ldurb           w17, [x0, #-1]
    //     0xe65dfc: and             x16, x17, x16, lsr #2
    //     0xe65e00: tst             x16, HEAP, lsr #32
    //     0xe65e04: b.eq            #0xe65e0c
    //     0xe65e08: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe65e0c: r1 = <PdfDataType>
    //     0xe65e0c: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe65e10: ldr             x1, [x1, #0x4c8]
    // 0xe65e14: r0 = PdfArray()
    //     0xe65e14: bl              #0x7b64e4  ; AllocatePdfArrayStub -> PdfArray<X0 bound PdfDataType> (size=0x10)
    // 0xe65e18: mov             x1, x0
    // 0xe65e1c: stur            x0, [fp, #-0x18]
    // 0xe65e20: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe65e20: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe65e24: r0 = PdfArray()
    //     0xe65e24: bl              #0x7b6438  ; [package:pdf/src/pdf/format/array.dart] PdfArray::PdfArray
    // 0xe65e28: r1 = <PdfArray<PdfDataType>>
    //     0xe65e28: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e0b0] TypeArguments: <PdfArray<PdfDataType>>
    //     0xe65e2c: ldr             x1, [x1, #0xb0]
    // 0xe65e30: r0 = PdfObject()
    //     0xe65e30: bl              #0xe65e84  ; AllocatePdfObjectStub -> PdfObject<X0 bound PdfDataType> (size=0x2c)
    // 0xe65e34: mov             x1, x0
    // 0xe65e38: ldur            x2, [fp, #-0x10]
    // 0xe65e3c: ldur            x3, [fp, #-0x18]
    // 0xe65e40: stur            x0, [fp, #-0x10]
    // 0xe65e44: r0 = PdfObject()
    //     0xe65e44: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xe65e48: ldur            x0, [fp, #-0x10]
    // 0xe65e4c: ldur            x1, [fp, #-8]
    // 0xe65e50: StoreField: r1->field_3b = r0
    //     0xe65e50: stur            w0, [x1, #0x3b]
    //     0xe65e54: ldurb           w16, [x1, #-1]
    //     0xe65e58: ldurb           w17, [x0, #-1]
    //     0xe65e5c: and             x16, x17, x16, lsr #2
    //     0xe65e60: tst             x16, HEAP, lsr #32
    //     0xe65e64: b.eq            #0xe65e6c
    //     0xe65e68: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe65e6c: r0 = Null
    //     0xe65e6c: mov             x0, NULL
    // 0xe65e70: LeaveFrame
    //     0xe65e70: mov             SP, fp
    //     0xe65e74: ldp             fp, lr, [SP], #0x10
    // 0xe65e78: ret
    //     0xe65e78: ret             
    // 0xe65e7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe65e7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe65e80: b               #0xe65cd0
  }
  get _ ascent(/* No info */) {
    // ** addr: 0xe8e35c, size: 0x7c
    // 0xe8e35c: EnterFrame
    //     0xe8e35c: stp             fp, lr, [SP, #-0x10]!
    //     0xe8e360: mov             fp, SP
    // 0xe8e364: AllocStack(0x18)
    //     0xe8e364: sub             SP, SP, #0x18
    // 0xe8e368: CheckStackOverflow
    //     0xe8e368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8e36c: cmp             SP, x16
    //     0xe8e370: b.ls            #0xe8e3d0
    // 0xe8e374: LoadField: r0 = r1->field_3f
    //     0xe8e374: ldur            w0, [x1, #0x3f]
    // 0xe8e378: DecompressPointer r0
    //     0xe8e378: add             x0, x0, HEAP, lsl #32
    // 0xe8e37c: mov             x1, x0
    // 0xe8e380: stur            x0, [fp, #-8]
    // 0xe8e384: r0 = ascent()
    //     0xe8e384: bl              #0xc3831c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::ascent
    // 0xe8e388: mov             x2, x0
    // 0xe8e38c: r0 = BoxInt64Instr(r2)
    //     0xe8e38c: sbfiz           x0, x2, #1, #0x1f
    //     0xe8e390: cmp             x2, x0, asr #1
    //     0xe8e394: b.eq            #0xe8e3a0
    //     0xe8e398: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8e39c: stur            x2, [x0, #7]
    // 0xe8e3a0: stp             x0, NULL, [SP]
    // 0xe8e3a4: r0 = _Double.fromInteger()
    //     0xe8e3a4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe8e3a8: ldur            x1, [fp, #-8]
    // 0xe8e3ac: stur            x0, [fp, #-8]
    // 0xe8e3b0: r0 = unitsPerEm()
    //     0xe8e3b0: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0xe8e3b4: scvtf           d1, x0
    // 0xe8e3b8: ldur            x0, [fp, #-8]
    // 0xe8e3bc: LoadField: d2 = r0->field_7
    //     0xe8e3bc: ldur            d2, [x0, #7]
    // 0xe8e3c0: fdiv            d0, d2, d1
    // 0xe8e3c4: LeaveFrame
    //     0xe8e3c4: mov             SP, fp
    //     0xe8e3c8: ldp             fp, lr, [SP], #0x10
    // 0xe8e3cc: ret
    //     0xe8e3cc: ret             
    // 0xe8e3d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8e3d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8e3d4: b               #0xe8e374
  }
  _ putText(/* No info */) {
    // ** addr: 0xe9ff78, size: 0x414
    // 0xe9ff78: EnterFrame
    //     0xe9ff78: stp             fp, lr, [SP, #-0x10]!
    //     0xe9ff7c: mov             fp, SP
    // 0xe9ff80: AllocStack(0x48)
    //     0xe9ff80: sub             SP, SP, #0x48
    // 0xe9ff84: r7 = 4278255360
    //     0xe9ff84: movz            x7, #0xff00
    //     0xe9ff88: movk            x7, #0xff00, lsl #16
    // 0xe9ff8c: r6 = 16711935
    //     0xe9ff8c: movz            x6, #0xff
    //     0xe9ff90: movk            x6, #0xff, lsl #16
    // 0xe9ff94: r5 = 4294901760
    //     0xe9ff94: orr             x5, xzr, #0xffff0000
    // 0xe9ff98: r4 = 65535
    //     0xe9ff98: orr             x4, xzr, #0xffff
    // 0xe9ff9c: mov             x10, x1
    // 0xe9ffa0: mov             x9, x2
    // 0xe9ffa4: mov             x8, x3
    // 0xe9ffa8: stur            x1, [fp, #-8]
    // 0xe9ffac: stur            x2, [fp, #-0x10]
    // 0xe9ffb0: stur            x3, [fp, #-0x18]
    // 0xe9ffb4: CheckStackOverflow
    //     0xe9ffb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9ffb8: cmp             SP, x16
    //     0xe9ffbc: b.ls            #0xea0354
    // 0xe9ffc0: LoadField: r0 = r10->field_3f
    //     0xe9ffc0: ldur            w0, [x10, #0x3f]
    // 0xe9ffc4: DecompressPointer r0
    //     0xe9ffc4: add             x0, x0, HEAP, lsl #32
    // 0xe9ffc8: LoadField: r2 = r0->field_7
    //     0xe9ffc8: ldur            w2, [x0, #7]
    // 0xe9ffcc: DecompressPointer r2
    //     0xe9ffcc: add             x2, x2, HEAP, lsl #32
    // 0xe9ffd0: LoadField: r0 = r2->field_13
    //     0xe9ffd0: ldur            w0, [x2, #0x13]
    // 0xe9ffd4: r1 = LoadInt32Instr(r0)
    //     0xe9ffd4: sbfx            x1, x0, #1, #0x1f
    // 0xe9ffd8: sub             x0, x1, #3
    // 0xe9ffdc: r1 = 0
    //     0xe9ffdc: movz            x1, #0
    // 0xe9ffe0: cmp             x1, x0
    // 0xe9ffe4: b.hs            #0xea035c
    // 0xe9ffe8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xe9ffe8: ldur            w0, [x2, #0x17]
    // 0xe9ffec: DecompressPointer r0
    //     0xe9ffec: add             x0, x0, HEAP, lsl #32
    // 0xe9fff0: LoadField: r1 = r2->field_1b
    //     0xe9fff0: ldur            w1, [x2, #0x1b]
    // 0xe9fff4: LoadField: r2 = r0->field_7
    //     0xe9fff4: ldur            x2, [x0, #7]
    // 0xe9fff8: asr             w16, w1, #1
    // 0xe9fffc: add             x16, x2, w16, sxtw
    // 0xea0000: ldr             w0, [x16]
    // 0xea0004: and             x1, x0, x7
    // 0xea0008: ubfx            x1, x1, #0, #0x20
    // 0xea000c: asr             x2, x1, #8
    // 0xea0010: and             x1, x0, x6
    // 0xea0014: ubfx            x1, x1, #0, #0x20
    // 0xea0018: lsl             x0, x1, #8
    // 0xea001c: orr             x1, x2, x0
    // 0xea0020: mov             x0, x1
    // 0xea0024: ubfx            x0, x0, #0, #0x20
    // 0xea0028: and             x2, x0, x5
    // 0xea002c: ubfx            x2, x2, #0, #0x20
    // 0xea0030: asr             x0, x2, #0x10
    // 0xea0034: ubfx            x1, x1, #0, #0x20
    // 0xea0038: and             x2, x1, x4
    // 0xea003c: ubfx            x2, x2, #0, #0x20
    // 0xea0040: lsl             x1, x2, #0x10
    // 0xea0044: orr             x2, x0, x1
    // 0xea0048: cmp             x2, #0x10, lsl #12
    // 0xea004c: b.eq            #0xea0060
    // 0xea0050: mov             x1, x10
    // 0xea0054: mov             x2, x9
    // 0xea0058: mov             x3, x8
    // 0xea005c: r0 = putText()
    //     0xea005c: bl              #0xea038c  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::putText
    // 0xea0060: ldur            x0, [fp, #-0x18]
    // 0xea0064: ldur            x1, [fp, #-0x10]
    // 0xea0068: r2 = 60
    //     0xea0068: movz            x2, #0x3c
    // 0xea006c: r0 = putByte()
    //     0xea006c: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0xea0070: ldur            x2, [fp, #-0x18]
    // 0xea0074: LoadField: r0 = r2->field_7
    //     0xea0074: ldur            w0, [x2, #7]
    // 0xea0078: r3 = LoadInt32Instr(r0)
    //     0xea0078: sbfx            x3, x0, #1, #0x1f
    // 0xea007c: stur            x3, [fp, #-0x48]
    // 0xea0080: r4 = LoadClassIdInstr(r2)
    //     0xea0080: ldur            x4, [x2, #-1]
    //     0xea0084: ubfx            x4, x4, #0xc, #0x14
    // 0xea0088: lsl             x4, x4, #1
    // 0xea008c: stur            x4, [fp, #-0x40]
    // 0xea0090: ldur            x5, [fp, #-0x10]
    // 0xea0094: r9 = 0
    //     0xea0094: movz            x9, #0
    // 0xea0098: ldur            x6, [fp, #-8]
    // 0xea009c: r8 = 64512
    //     0xea009c: orr             x8, xzr, #0xfc00
    // 0xea00a0: r7 = 1023
    //     0xea00a0: movz            x7, #0x3ff
    // 0xea00a4: CheckStackOverflow
    //     0xea00a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea00a8: cmp             SP, x16
    //     0xea00ac: b.ls            #0xea0360
    // 0xea00b0: cmp             x9, x3
    // 0xea00b4: b.eq            #0xea0338
    // 0xea00b8: mov             x0, x3
    // 0xea00bc: mov             x1, x9
    // 0xea00c0: cmp             x1, x0
    // 0xea00c4: b.hs            #0xea0368
    // 0xea00c8: cmp             w4, #0xbc
    // 0xea00cc: b.ne            #0xea00dc
    // 0xea00d0: ArrayLoad: r0 = r2[r9]  ; TypedUnsigned_1
    //     0xea00d0: add             x16, x2, x9
    //     0xea00d4: ldrb            w0, [x16, #0xf]
    // 0xea00d8: b               #0xea00e4
    // 0xea00dc: add             x16, x2, x9, lsl #1
    // 0xea00e0: ldurh           w0, [x16, #0xf]
    // 0xea00e4: add             x1, x9, #1
    // 0xea00e8: mov             x9, x0
    // 0xea00ec: ubfx            x9, x9, #0, #0x20
    // 0xea00f0: and             x10, x9, x8
    // 0xea00f4: ubfx            x10, x10, #0, #0x20
    // 0xea00f8: r17 = 55296
    //     0xea00f8: movz            x17, #0xd800
    // 0xea00fc: cmp             x10, x17
    // 0xea0100: b.ne            #0xea0174
    // 0xea0104: cmp             x1, x3
    // 0xea0108: b.ge            #0xea0174
    // 0xea010c: cmp             w4, #0xbc
    // 0xea0110: b.ne            #0xea0120
    // 0xea0114: ArrayLoad: r9 = r2[r1]  ; TypedUnsigned_1
    //     0xea0114: add             x16, x2, x1
    //     0xea0118: ldrb            w9, [x16, #0xf]
    // 0xea011c: b               #0xea0128
    // 0xea0120: add             x16, x2, x1, lsl #1
    // 0xea0124: ldurh           w9, [x16, #0xf]
    // 0xea0128: mov             x10, x9
    // 0xea012c: ubfx            x10, x10, #0, #0x20
    // 0xea0130: and             x11, x10, x8
    // 0xea0134: ubfx            x11, x11, #0, #0x20
    // 0xea0138: r17 = 56320
    //     0xea0138: movz            x17, #0xdc00
    // 0xea013c: cmp             x11, x17
    // 0xea0140: b.ne            #0xea0174
    // 0xea0144: add             x10, x1, #1
    // 0xea0148: ubfx            x0, x0, #0, #0x20
    // 0xea014c: and             x1, x0, x7
    // 0xea0150: ubfx            x1, x1, #0, #0x20
    // 0xea0154: lsl             x0, x1, #0xa
    // 0xea0158: add             x1, x0, #0x10, lsl #12
    // 0xea015c: ubfx            x9, x9, #0, #0x20
    // 0xea0160: and             x0, x9, x7
    // 0xea0164: ubfx            x0, x0, #0, #0x20
    // 0xea0168: add             x9, x1, x0
    // 0xea016c: mov             x0, x9
    // 0xea0170: b               #0xea0178
    // 0xea0174: mov             x10, x1
    // 0xea0178: stur            x10, [fp, #-0x30]
    // 0xea017c: stur            x0, [fp, #-0x38]
    // 0xea0180: LoadField: r1 = r6->field_2f
    //     0xea0180: ldur            w1, [x6, #0x2f]
    // 0xea0184: DecompressPointer r1
    //     0xea0184: add             x1, x1, HEAP, lsl #32
    // 0xea0188: r16 = Sentinel
    //     0xea0188: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea018c: cmp             w1, w16
    // 0xea0190: b.eq            #0xea036c
    // 0xea0194: LoadField: r9 = r1->field_33
    //     0xea0194: ldur            w9, [x1, #0x33]
    // 0xea0198: DecompressPointer r9
    //     0xea0198: add             x9, x9, HEAP, lsl #32
    // 0xea019c: LoadField: r1 = r9->field_b
    //     0xea019c: ldur            w1, [x9, #0xb]
    // 0xea01a0: r11 = LoadInt32Instr(r1)
    //     0xea01a0: sbfx            x11, x1, #1, #0x1f
    // 0xea01a4: LoadField: r1 = r9->field_f
    //     0xea01a4: ldur            w1, [x9, #0xf]
    // 0xea01a8: DecompressPointer r1
    //     0xea01a8: add             x1, x1, HEAP, lsl #32
    // 0xea01ac: r9 = 0
    //     0xea01ac: movz            x9, #0
    // 0xea01b0: CheckStackOverflow
    //     0xea01b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea01b4: cmp             SP, x16
    //     0xea01b8: b.ls            #0xea0378
    // 0xea01bc: cmp             x9, x11
    // 0xea01c0: b.ge            #0xea01f8
    // 0xea01c4: ArrayLoad: r12 = r1[r9]  ; Unknown_4
    //     0xea01c4: add             x16, x1, x9, lsl #2
    //     0xea01c8: ldur            w12, [x16, #0xf]
    // 0xea01cc: DecompressPointer r12
    //     0xea01cc: add             x12, x12, HEAP, lsl #32
    // 0xea01d0: r13 = LoadInt32Instr(r12)
    //     0xea01d0: sbfx            x13, x12, #1, #0x1f
    //     0xea01d4: tbz             w12, #0, #0xea01dc
    //     0xea01d8: ldur            x13, [x12, #7]
    // 0xea01dc: cmp             x13, x0
    // 0xea01e0: b.eq            #0xea01f0
    // 0xea01e4: add             x12, x9, #1
    // 0xea01e8: mov             x9, x12
    // 0xea01ec: b               #0xea01b0
    // 0xea01f0: mov             x1, x9
    // 0xea01f4: b               #0xea01fc
    // 0xea01f8: r1 = -1
    //     0xea01f8: movn            x1, #0
    // 0xea01fc: cmn             x1, #1
    // 0xea0200: b.ne            #0xea02a8
    // 0xea0204: LoadField: r1 = r6->field_2f
    //     0xea0204: ldur            w1, [x6, #0x2f]
    // 0xea0208: DecompressPointer r1
    //     0xea0208: add             x1, x1, HEAP, lsl #32
    // 0xea020c: r16 = Sentinel
    //     0xea020c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea0210: cmp             w1, w16
    // 0xea0214: b.eq            #0xea0380
    // 0xea0218: LoadField: r9 = r1->field_33
    //     0xea0218: ldur            w9, [x1, #0x33]
    // 0xea021c: DecompressPointer r9
    //     0xea021c: add             x9, x9, HEAP, lsl #32
    // 0xea0220: stur            x9, [fp, #-0x28]
    // 0xea0224: LoadField: r1 = r9->field_b
    //     0xea0224: ldur            w1, [x9, #0xb]
    // 0xea0228: LoadField: r11 = r9->field_f
    //     0xea0228: ldur            w11, [x9, #0xf]
    // 0xea022c: DecompressPointer r11
    //     0xea022c: add             x11, x11, HEAP, lsl #32
    // 0xea0230: LoadField: r12 = r11->field_b
    //     0xea0230: ldur            w12, [x11, #0xb]
    // 0xea0234: r11 = LoadInt32Instr(r1)
    //     0xea0234: sbfx            x11, x1, #1, #0x1f
    // 0xea0238: stur            x11, [fp, #-0x20]
    // 0xea023c: r1 = LoadInt32Instr(r12)
    //     0xea023c: sbfx            x1, x12, #1, #0x1f
    // 0xea0240: cmp             x11, x1
    // 0xea0244: b.ne            #0xea0250
    // 0xea0248: mov             x1, x9
    // 0xea024c: r0 = _growToNextCapacity()
    //     0xea024c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xea0250: ldur            x1, [fp, #-0x28]
    // 0xea0254: ldur            x2, [fp, #-0x20]
    // 0xea0258: ldur            x0, [fp, #-0x38]
    // 0xea025c: add             x3, x2, #1
    // 0xea0260: lsl             x4, x3, #1
    // 0xea0264: StoreField: r1->field_b = r4
    //     0xea0264: stur            w4, [x1, #0xb]
    // 0xea0268: LoadField: r3 = r1->field_f
    //     0xea0268: ldur            w3, [x1, #0xf]
    // 0xea026c: DecompressPointer r3
    //     0xea026c: add             x3, x3, HEAP, lsl #32
    // 0xea0270: lsl             x1, x0, #1
    // 0xea0274: mov             x0, x1
    // 0xea0278: mov             x1, x3
    // 0xea027c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xea027c: add             x25, x1, x2, lsl #2
    //     0xea0280: add             x25, x25, #0xf
    //     0xea0284: str             w0, [x25]
    //     0xea0288: tbz             w0, #0, #0xea02a4
    //     0xea028c: ldurb           w16, [x1, #-1]
    //     0xea0290: ldurb           w17, [x0, #-1]
    //     0xea0294: and             x16, x17, x16, lsr #2
    //     0xea0298: tst             x16, HEAP, lsr #32
    //     0xea029c: b.eq            #0xea02a4
    //     0xea02a0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xea02a4: mov             x1, x2
    // 0xea02a8: ldur            x0, [fp, #-0x10]
    // 0xea02ac: lsl             x2, x1, #1
    // 0xea02b0: mov             x1, x2
    // 0xea02b4: r0 = _toPow2String()
    //     0xea02b4: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0xea02b8: mov             x1, x0
    // 0xea02bc: r2 = 4
    //     0xea02bc: movz            x2, #0x4
    // 0xea02c0: r3 = "0"
    //     0xea02c0: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0xea02c4: r0 = padLeft()
    //     0xea02c4: bl              #0xebe370  ; [dart:core] _OneByteString::padLeft
    // 0xea02c8: mov             x2, x0
    // 0xea02cc: r1 = Instance_Latin1Encoder
    //     0xea02cc: ldr             x1, [PP, #0xe10]  ; [pp+0xe10] Obj!Latin1Encoder@e2ce21
    // 0xea02d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xea02d0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xea02d4: r0 = convert()
    //     0xea02d4: bl              #0xcf64b0  ; [dart:convert] _UnicodeSubsetEncoder::convert
    // 0xea02d8: stur            x0, [fp, #-0x28]
    // 0xea02dc: LoadField: r1 = r0->field_13
    //     0xea02dc: ldur            w1, [x0, #0x13]
    // 0xea02e0: r3 = LoadInt32Instr(r1)
    //     0xea02e0: sbfx            x3, x1, #1, #0x1f
    // 0xea02e4: ldur            x1, [fp, #-0x10]
    // 0xea02e8: mov             x2, x3
    // 0xea02ec: stur            x3, [fp, #-0x20]
    // 0xea02f0: r0 = _ensureCapacity()
    //     0xea02f0: bl              #0x7b8054  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::_ensureCapacity
    // 0xea02f4: ldur            x0, [fp, #-0x10]
    // 0xea02f8: LoadField: r1 = r0->field_7
    //     0xea02f8: ldur            w1, [x0, #7]
    // 0xea02fc: DecompressPointer r1
    //     0xea02fc: add             x1, x1, HEAP, lsl #32
    // 0xea0300: LoadField: r2 = r0->field_b
    //     0xea0300: ldur            x2, [x0, #0xb]
    // 0xea0304: ldur            x3, [fp, #-0x28]
    // 0xea0308: r0 = setAll()
    //     0xea0308: bl              #0x7b7e30  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::setAll
    // 0xea030c: ldur            x1, [fp, #-0x10]
    // 0xea0310: LoadField: r0 = r1->field_b
    //     0xea0310: ldur            x0, [x1, #0xb]
    // 0xea0314: ldur            x2, [fp, #-0x20]
    // 0xea0318: add             x3, x0, x2
    // 0xea031c: StoreField: r1->field_b = r3
    //     0xea031c: stur            x3, [x1, #0xb]
    // 0xea0320: ldur            x9, [fp, #-0x30]
    // 0xea0324: mov             x5, x1
    // 0xea0328: ldur            x2, [fp, #-0x18]
    // 0xea032c: ldur            x4, [fp, #-0x40]
    // 0xea0330: ldur            x3, [fp, #-0x48]
    // 0xea0334: b               #0xea0098
    // 0xea0338: mov             x1, x5
    // 0xea033c: r2 = 62
    //     0xea033c: movz            x2, #0x3e
    // 0xea0340: r0 = putByte()
    //     0xea0340: bl              #0x862100  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putByte
    // 0xea0344: r0 = Null
    //     0xea0344: mov             x0, NULL
    // 0xea0348: LeaveFrame
    //     0xea0348: mov             SP, fp
    //     0xea034c: ldp             fp, lr, [SP], #0x10
    // 0xea0350: ret
    //     0xea0350: ret             
    // 0xea0354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea0354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea0358: b               #0xe9ffc0
    // 0xea035c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea035c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea0360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea0360: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea0364: b               #0xea00b0
    // 0xea0368: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea0368: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea036c: r9 = unicodeCMap
    //     0xea036c: add             x9, PP, #0x47, lsl #12  ; [pp+0x476d8] Field <PdfTtfFont.unicodeCMap>: late (offset: 0x30)
    //     0xea0370: ldr             x9, [x9, #0x6d8]
    // 0xea0374: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea0374: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea0378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea0378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea037c: b               #0xea01bc
    // 0xea0380: r9 = unicodeCMap
    //     0xea0380: add             x9, PP, #0x47, lsl #12  ; [pp+0x476d8] Field <PdfTtfFont.unicodeCMap>: late (offset: 0x30)
    //     0xea0384: ldr             x9, [x9, #0x6d8]
    // 0xea0388: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea0388: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ descent(/* No info */) {
    // ** addr: 0xea0a04, size: 0x7c
    // 0xea0a04: EnterFrame
    //     0xea0a04: stp             fp, lr, [SP, #-0x10]!
    //     0xea0a08: mov             fp, SP
    // 0xea0a0c: AllocStack(0x18)
    //     0xea0a0c: sub             SP, SP, #0x18
    // 0xea0a10: CheckStackOverflow
    //     0xea0a10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea0a14: cmp             SP, x16
    //     0xea0a18: b.ls            #0xea0a78
    // 0xea0a1c: LoadField: r0 = r1->field_3f
    //     0xea0a1c: ldur            w0, [x1, #0x3f]
    // 0xea0a20: DecompressPointer r0
    //     0xea0a20: add             x0, x0, HEAP, lsl #32
    // 0xea0a24: mov             x1, x0
    // 0xea0a28: stur            x0, [fp, #-8]
    // 0xea0a2c: r0 = descent()
    //     0xea0a2c: bl              #0xc3820c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::descent
    // 0xea0a30: mov             x2, x0
    // 0xea0a34: r0 = BoxInt64Instr(r2)
    //     0xea0a34: sbfiz           x0, x2, #1, #0x1f
    //     0xea0a38: cmp             x2, x0, asr #1
    //     0xea0a3c: b.eq            #0xea0a48
    //     0xea0a40: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea0a44: stur            x2, [x0, #7]
    // 0xea0a48: stp             x0, NULL, [SP]
    // 0xea0a4c: r0 = _Double.fromInteger()
    //     0xea0a4c: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xea0a50: ldur            x1, [fp, #-8]
    // 0xea0a54: stur            x0, [fp, #-8]
    // 0xea0a58: r0 = unitsPerEm()
    //     0xea0a58: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0xea0a5c: scvtf           d1, x0
    // 0xea0a60: ldur            x0, [fp, #-8]
    // 0xea0a64: LoadField: d2 = r0->field_7
    //     0xea0a64: ldur            d2, [x0, #7]
    // 0xea0a68: fdiv            d0, d2, d1
    // 0xea0a6c: LeaveFrame
    //     0xea0a6c: mov             SP, fp
    //     0xea0a70: ldp             fp, lr, [SP], #0x10
    // 0xea0a74: ret
    //     0xea0a74: ret             
    // 0xea0a78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea0a78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea0a7c: b               #0xea0a1c
  }
  _ stringMetrics(/* No info */) {
    // ** addr: 0xea0a80, size: 0x208
    // 0xea0a80: EnterFrame
    //     0xea0a80: stp             fp, lr, [SP, #-0x10]!
    //     0xea0a84: mov             fp, SP
    // 0xea0a88: AllocStack(0x38)
    //     0xea0a88: sub             SP, SP, #0x38
    // 0xea0a8c: SetupParameters(PdfTtfFont this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, {_Double letterSpacing = 0.000000 /* d0, fp-0x20 */})
    //     0xea0a8c: mov             x3, x1
    //     0xea0a90: stur            x1, [fp, #-8]
    //     0xea0a94: stur            x2, [fp, #-0x10]
    //     0xea0a98: ldur            w0, [x4, #0x13]
    //     0xea0a9c: ldur            w1, [x4, #0x1f]
    //     0xea0aa0: add             x1, x1, HEAP, lsl #32
    //     0xea0aa4: ldr             x16, [PP, #0x4788]  ; [pp+0x4788] "letterSpacing"
    //     0xea0aa8: cmp             w1, w16
    //     0xea0aac: b.ne            #0xea0acc
    //     0xea0ab0: ldur            w1, [x4, #0x23]
    //     0xea0ab4: add             x1, x1, HEAP, lsl #32
    //     0xea0ab8: sub             w4, w0, w1
    //     0xea0abc: add             x0, fp, w4, sxtw #2
    //     0xea0ac0: ldr             x0, [x0, #8]
    //     0xea0ac4: ldur            d0, [x0, #7]
    //     0xea0ac8: b               #0xea0ad0
    //     0xea0acc: eor             v0.16b, v0.16b, v0.16b
    //     0xea0ad0: stur            d0, [fp, #-0x20]
    // 0xea0ad4: CheckStackOverflow
    //     0xea0ad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea0ad8: cmp             SP, x16
    //     0xea0adc: b.ls            #0xea0c64
    // 0xea0ae0: LoadField: r0 = r2->field_7
    //     0xea0ae0: ldur            w0, [x2, #7]
    // 0xea0ae4: cbz             w0, #0xea0b90
    // 0xea0ae8: r7 = 4278255360
    //     0xea0ae8: movz            x7, #0xff00
    //     0xea0aec: movk            x7, #0xff00, lsl #16
    // 0xea0af0: r6 = 16711935
    //     0xea0af0: movz            x6, #0xff
    //     0xea0af4: movk            x6, #0xff, lsl #16
    // 0xea0af8: r5 = 4294901760
    //     0xea0af8: orr             x5, xzr, #0xffff0000
    // 0xea0afc: r4 = 65535
    //     0xea0afc: orr             x4, xzr, #0xffff
    // 0xea0b00: LoadField: r0 = r3->field_3f
    //     0xea0b00: ldur            w0, [x3, #0x3f]
    // 0xea0b04: DecompressPointer r0
    //     0xea0b04: add             x0, x0, HEAP, lsl #32
    // 0xea0b08: LoadField: r8 = r0->field_7
    //     0xea0b08: ldur            w8, [x0, #7]
    // 0xea0b0c: DecompressPointer r8
    //     0xea0b0c: add             x8, x8, HEAP, lsl #32
    // 0xea0b10: LoadField: r0 = r8->field_13
    //     0xea0b10: ldur            w0, [x8, #0x13]
    // 0xea0b14: r1 = LoadInt32Instr(r0)
    //     0xea0b14: sbfx            x1, x0, #1, #0x1f
    // 0xea0b18: sub             x0, x1, #3
    // 0xea0b1c: r1 = 0
    //     0xea0b1c: movz            x1, #0
    // 0xea0b20: cmp             x1, x0
    // 0xea0b24: b.hs            #0xea0c6c
    // 0xea0b28: ArrayLoad: r0 = r8[0]  ; List_4
    //     0xea0b28: ldur            w0, [x8, #0x17]
    // 0xea0b2c: DecompressPointer r0
    //     0xea0b2c: add             x0, x0, HEAP, lsl #32
    // 0xea0b30: LoadField: r1 = r8->field_1b
    //     0xea0b30: ldur            w1, [x8, #0x1b]
    // 0xea0b34: LoadField: r8 = r0->field_7
    //     0xea0b34: ldur            x8, [x0, #7]
    // 0xea0b38: asr             w16, w1, #1
    // 0xea0b3c: add             x16, x8, w16, sxtw
    // 0xea0b40: ldr             w0, [x16]
    // 0xea0b44: and             x1, x0, x7
    // 0xea0b48: ubfx            x1, x1, #0, #0x20
    // 0xea0b4c: asr             x7, x1, #8
    // 0xea0b50: and             x1, x0, x6
    // 0xea0b54: ubfx            x1, x1, #0, #0x20
    // 0xea0b58: lsl             x0, x1, #8
    // 0xea0b5c: orr             x1, x7, x0
    // 0xea0b60: mov             x0, x1
    // 0xea0b64: ubfx            x0, x0, #0, #0x20
    // 0xea0b68: and             x6, x0, x5
    // 0xea0b6c: ubfx            x6, x6, #0, #0x20
    // 0xea0b70: asr             x0, x6, #0x10
    // 0xea0b74: ubfx            x1, x1, #0, #0x20
    // 0xea0b78: and             x5, x1, x4
    // 0xea0b7c: ubfx            x5, x5, #0, #0x20
    // 0xea0b80: lsl             x1, x5, #0x10
    // 0xea0b84: orr             x4, x0, x1
    // 0xea0b88: cmp             x4, #0x10, lsl #12
    // 0xea0b8c: b.eq            #0xea0bd8
    // 0xea0b90: r0 = inline_Allocate_Double()
    //     0xea0b90: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xea0b94: add             x0, x0, #0x10
    //     0xea0b98: cmp             x1, x0
    //     0xea0b9c: b.ls            #0xea0c70
    //     0xea0ba0: str             x0, [THR, #0x50]  ; THR::top
    //     0xea0ba4: sub             x0, x0, #0xf
    //     0xea0ba8: movz            x1, #0xe15c
    //     0xea0bac: movk            x1, #0x3, lsl #16
    //     0xea0bb0: stur            x1, [x0, #-1]
    // 0xea0bb4: StoreField: r0->field_7 = d0
    //     0xea0bb4: stur            d0, [x0, #7]
    // 0xea0bb8: str             x0, [SP]
    // 0xea0bbc: mov             x1, x3
    // 0xea0bc0: r4 = const [0, 0x3, 0x1, 0x2, letterSpacing, 0x2, null]
    //     0xea0bc0: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e128] List(7) [0, 0x3, 0x1, 0x2, "letterSpacing", 0x2, Null]
    //     0xea0bc4: ldr             x4, [x4, #0x128]
    // 0xea0bc8: r0 = stringMetrics()
    //     0xea0bc8: bl              #0xea0c88  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::stringMetrics
    // 0xea0bcc: LeaveFrame
    //     0xea0bcc: mov             SP, fp
    //     0xea0bd0: ldp             fp, lr, [SP], #0x10
    // 0xea0bd4: ret
    //     0xea0bd4: ret             
    // 0xea0bd8: r1 = <int>
    //     0xea0bd8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xea0bdc: r0 = Runes()
    //     0xea0bdc: bl              #0x7bc644  ; AllocateRunesStub -> Runes (size=0x10)
    // 0xea0be0: mov             x3, x0
    // 0xea0be4: ldur            x0, [fp, #-0x10]
    // 0xea0be8: stur            x3, [fp, #-0x18]
    // 0xea0bec: StoreField: r3->field_b = r0
    //     0xea0bec: stur            w0, [x3, #0xb]
    // 0xea0bf0: r1 = <int>
    //     0xea0bf0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xea0bf4: r2 = 0
    //     0xea0bf4: movz            x2, #0
    // 0xea0bf8: r0 = _GrowableList()
    //     0xea0bf8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xea0bfc: mov             x2, x0
    // 0xea0c00: r1 = Function 'add':.
    //     0xea0c00: add             x1, PP, #0x21, lsl #12  ; [pp+0x21af8] AnonymousClosure: (0x5ff390), in [dart:core] _GrowableList::add (0x6eb7b8)
    //     0xea0c04: ldr             x1, [x1, #0xaf8]
    // 0xea0c08: stur            x0, [fp, #-0x10]
    // 0xea0c0c: r0 = AllocateClosure()
    //     0xea0c0c: bl              #0xec1630  ; AllocateClosureStub
    // 0xea0c10: ldur            x1, [fp, #-0x18]
    // 0xea0c14: mov             x2, x0
    // 0xea0c18: r0 = forEach()
    //     0xea0c18: bl              #0x7e1920  ; [dart:core] Iterable::forEach
    // 0xea0c1c: ldur            x2, [fp, #-8]
    // 0xea0c20: r1 = Function 'glyphMetrics':.
    //     0xea0c20: add             x1, PP, #0x47, lsl #12  ; [pp+0x47520] AnonymousClosure: (0x7b6e48), in [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::glyphMetrics (0x7b6cd0)
    //     0xea0c24: ldr             x1, [x1, #0x520]
    // 0xea0c28: r0 = AllocateClosure()
    //     0xea0c28: bl              #0xec1630  ; AllocateClosureStub
    // 0xea0c2c: r16 = <PdfFontMetrics>
    //     0xea0c2c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47528] TypeArguments: <PdfFontMetrics>
    //     0xea0c30: ldr             x16, [x16, #0x528]
    // 0xea0c34: ldur            lr, [fp, #-0x10]
    // 0xea0c38: stp             lr, x16, [SP, #8]
    // 0xea0c3c: str             x0, [SP]
    // 0xea0c40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xea0c40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xea0c44: r0 = map()
    //     0xea0c44: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xea0c48: mov             x2, x0
    // 0xea0c4c: ldur            d0, [fp, #-0x20]
    // 0xea0c50: r1 = Null
    //     0xea0c50: mov             x1, NULL
    // 0xea0c54: r0 = PdfFontMetrics.append()
    //     0xea0c54: bl              #0xe701bc  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::PdfFontMetrics.append
    // 0xea0c58: LeaveFrame
    //     0xea0c58: mov             SP, fp
    //     0xea0c5c: ldp             fp, lr, [SP], #0x10
    // 0xea0c60: ret
    //     0xea0c60: ret             
    // 0xea0c64: r0 = StackOverflowSharedWithFPURegs()
    //     0xea0c64: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xea0c68: b               #0xea0ae0
    // 0xea0c6c: r0 = RangeErrorSharedWithFPURegs()
    //     0xea0c6c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xea0c70: SaveReg d0
    //     0xea0c70: str             q0, [SP, #-0x10]!
    // 0xea0c74: stp             x2, x3, [SP, #-0x10]!
    // 0xea0c78: r0 = AllocateDouble()
    //     0xea0c78: bl              #0xec2254  ; AllocateDoubleStub
    // 0xea0c7c: ldp             x2, x3, [SP], #0x10
    // 0xea0c80: RestoreReg d0
    //     0xea0c80: ldr             q0, [SP], #0x10
    // 0xea0c84: b               #0xea0bb4
  }
}
