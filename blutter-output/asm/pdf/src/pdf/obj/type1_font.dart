// lib: , url: package:pdf/src/pdf/obj/type1_font.dart

// class id: 1050820, size: 0x8
class :: {
}

// class id: 895, size: 0x50, field offset: 0x30
class PdfType1Font extends PdfFont {

  _ PdfType1Font.create(/* No info */) {
    // ** addr: 0xe66524, size: 0xa70
    // 0xe66524: EnterFrame
    //     0xe66524: stp             fp, lr, [SP, #-0x10]!
    //     0xe66528: mov             fp, SP
    // 0xe6652c: AllocStack(0x98)
    //     0xe6652c: sub             SP, SP, #0x98
    // 0xe66530: SetupParameters(PdfType1Font this /* r1 => r5, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */, dynamic _ /* r3 => r3, fp-0x30 */, dynamic _ /* r5 => r1, fp-0x38 */, dynamic _ /* r6 => r0, fp-0x40 */, dynamic _ /* r7 => r7, fp-0x48 */, dynamic _ /* d0 => d0, fp-0x78 */, dynamic _ /* d1 => d1, fp-0x80 */, dynamic _ /* r9, fp-0x18 */, {dynamic isFixedPitch = false /* r10, fp-0x10 */, _Double italicAngle = 0.000000 /* d2, fp-0x70 */, dynamic widths = const [] /* r4, fp-0x8 */})
    //     0xe66530: stur            x1, [fp, #-0x20]
    //     0xe66534: mov             x16, x5
    //     0xe66538: mov             x5, x1
    //     0xe6653c: mov             x1, x16
    //     0xe66540: mov             x0, x6
    //     0xe66544: stur            x2, [fp, #-0x28]
    //     0xe66548: stur            x3, [fp, #-0x30]
    //     0xe6654c: stur            x1, [fp, #-0x38]
    //     0xe66550: stur            x6, [fp, #-0x40]
    //     0xe66554: stur            x7, [fp, #-0x48]
    //     0xe66558: stur            d0, [fp, #-0x78]
    //     0xe6655c: stur            d1, [fp, #-0x80]
    //     0xe66560: ldur            w6, [x4, #0x13]
    //     0xe66564: sub             x8, x6, #0x12
    //     0xe66568: add             x9, fp, w8, sxtw #2
    //     0xe6656c: ldr             x9, [x9, #0x10]
    //     0xe66570: stur            x9, [fp, #-0x18]
    //     0xe66574: ldur            w8, [x4, #0x1f]
    //     0xe66578: add             x8, x8, HEAP, lsl #32
    //     0xe6657c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3de30] "isFixedPitch"
    //     0xe66580: ldr             x16, [x16, #0xe30]
    //     0xe66584: cmp             w8, w16
    //     0xe66588: b.ne            #0xe665ac
    //     0xe6658c: ldur            w8, [x4, #0x23]
    //     0xe66590: add             x8, x8, HEAP, lsl #32
    //     0xe66594: sub             w10, w6, w8
    //     0xe66598: add             x8, fp, w10, sxtw #2
    //     0xe6659c: ldr             x8, [x8, #8]
    //     0xe665a0: mov             x10, x8
    //     0xe665a4: movz            x8, #0x1
    //     0xe665a8: b               #0xe665b4
    //     0xe665ac: add             x10, NULL, #0x30  ; false
    //     0xe665b0: movz            x8, #0
    //     0xe665b4: stur            x10, [fp, #-0x10]
    //     0xe665b8: lsl             x11, x8, #1
    //     0xe665bc: lsl             w12, w11, #1
    //     0xe665c0: add             w13, w12, #8
    //     0xe665c4: add             x16, x4, w13, sxtw #1
    //     0xe665c8: ldur            w14, [x16, #0xf]
    //     0xe665cc: add             x14, x14, HEAP, lsl #32
    //     0xe665d0: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3de38] "italicAngle"
    //     0xe665d4: ldr             x16, [x16, #0xe38]
    //     0xe665d8: cmp             w14, w16
    //     0xe665dc: b.ne            #0xe66610
    //     0xe665e0: add             w8, w12, #0xa
    //     0xe665e4: add             x16, x4, w8, sxtw #1
    //     0xe665e8: ldur            w12, [x16, #0xf]
    //     0xe665ec: add             x12, x12, HEAP, lsl #32
    //     0xe665f0: sub             w8, w6, w12
    //     0xe665f4: add             x12, fp, w8, sxtw #2
    //     0xe665f8: ldr             x12, [x12, #8]
    //     0xe665fc: add             w8, w11, #2
    //     0xe66600: ldur            d2, [x12, #7]
    //     0xe66604: sbfx            x11, x8, #1, #0x1f
    //     0xe66608: mov             x8, x11
    //     0xe6660c: b               #0xe66614
    //     0xe66610: eor             v2.16b, v2.16b, v2.16b
    //     0xe66614: stur            d2, [fp, #-0x70]
    //     0xe66618: lsl             x11, x8, #1
    //     0xe6661c: lsl             w8, w11, #1
    //     0xe66620: add             w11, w8, #8
    //     0xe66624: add             x16, x4, w11, sxtw #1
    //     0xe66628: ldur            w12, [x16, #0xf]
    //     0xe6662c: add             x12, x12, HEAP, lsl #32
    //     0xe66630: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3de40] "widths"
    //     0xe66634: ldr             x16, [x16, #0xe40]
    //     0xe66638: cmp             w12, w16
    //     0xe6663c: b.ne            #0xe66664
    //     0xe66640: add             w11, w8, #0xa
    //     0xe66644: add             x16, x4, w11, sxtw #1
    //     0xe66648: ldur            w8, [x16, #0xf]
    //     0xe6664c: add             x8, x8, HEAP, lsl #32
    //     0xe66650: sub             w4, w6, w8
    //     0xe66654: add             x6, fp, w4, sxtw #2
    //     0xe66658: ldr             x6, [x6, #8]
    //     0xe6665c: mov             x4, x6
    //     0xe66660: b               #0xe6666c
    //     0xe66664: add             x4, PP, #0x25, lsl #12  ; [pp+0x25e48] List<double>(0)
    //     0xe66668: ldr             x4, [x4, #0xe48]
    //     0xe6666c: stur            x4, [fp, #-8]
    // 0xe66670: CheckStackOverflow
    //     0xe66670: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe66674: cmp             SP, x16
    //     0xe66678: b.ls            #0xe66edc
    // 0xe6667c: r1 = 1
    //     0xe6667c: movz            x1, #0x1
    // 0xe66680: r0 = AllocateContext()
    //     0xe66680: bl              #0xec126c  ; AllocateContextStub
    // 0xe66684: mov             x5, x0
    // 0xe66688: ldur            x4, [fp, #-0x20]
    // 0xe6668c: stur            x5, [fp, #-0x50]
    // 0xe66690: StoreField: r5->field_f = r4
    //     0xe66690: stur            w4, [x5, #0xf]
    // 0xe66694: ldur            x0, [fp, #-0x40]
    // 0xe66698: StoreField: r4->field_2f = r0
    //     0xe66698: stur            w0, [x4, #0x2f]
    //     0xe6669c: ldurb           w16, [x4, #-1]
    //     0xe666a0: ldurb           w17, [x0, #-1]
    //     0xe666a4: and             x16, x17, x16, lsr #2
    //     0xe666a8: tst             x16, HEAP, lsr #32
    //     0xe666ac: b.eq            #0xe666b4
    //     0xe666b0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe666b4: ldur            d0, [fp, #-0x78]
    // 0xe666b8: StoreField: r4->field_33 = d0
    //     0xe666b8: stur            d0, [x4, #0x33]
    // 0xe666bc: ldur            d1, [fp, #-0x80]
    // 0xe666c0: StoreField: r4->field_3b = d1
    //     0xe666c0: stur            d1, [x4, #0x3b]
    // 0xe666c4: d2 = 0.600000
    //     0xe666c4: ldr             d2, [PP, #0x5480]  ; [pp+0x5480] IMM: double(0.6) from 0x3fe3333333333333
    // 0xe666c8: StoreField: r4->field_47 = d2
    //     0xe666c8: stur            d2, [x4, #0x47]
    // 0xe666cc: ldur            x0, [fp, #-8]
    // 0xe666d0: StoreField: r4->field_43 = r0
    //     0xe666d0: stur            w0, [x4, #0x43]
    //     0xe666d4: ldurb           w16, [x4, #-1]
    //     0xe666d8: ldurb           w17, [x0, #-1]
    //     0xe666dc: and             x16, x17, x16, lsr #2
    //     0xe666e0: tst             x16, HEAP, lsr #32
    //     0xe666e4: b.eq            #0xe666ec
    //     0xe666e8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe666ec: mov             x1, x4
    // 0xe666f0: ldur            x2, [fp, #-0x28]
    // 0xe666f4: r3 = "/Type1"
    //     0xe666f4: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3de48] "/Type1"
    //     0xe666f8: ldr             x3, [x3, #0xe48]
    // 0xe666fc: r0 = PdfFont.create()
    //     0xe666fc: bl              #0xe6604c  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.create
    // 0xe66700: ldur            x0, [fp, #-0x20]
    // 0xe66704: LoadField: r3 = r0->field_1b
    //     0xe66704: ldur            w3, [x0, #0x1b]
    // 0xe66708: DecompressPointer r3
    //     0xe66708: add             x3, x3, HEAP, lsl #32
    // 0xe6670c: stur            x3, [fp, #-0x58]
    // 0xe66710: r1 = Null
    //     0xe66710: mov             x1, NULL
    // 0xe66714: r2 = 4
    //     0xe66714: movz            x2, #0x4
    // 0xe66718: r0 = AllocateArray()
    //     0xe66718: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6671c: r16 = "/"
    //     0xe6671c: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xe66720: StoreField: r0->field_f = r16
    //     0xe66720: stur            w16, [x0, #0xf]
    // 0xe66724: ldur            x1, [fp, #-0x40]
    // 0xe66728: StoreField: r0->field_13 = r1
    //     0xe66728: stur            w1, [x0, #0x13]
    // 0xe6672c: str             x0, [SP]
    // 0xe66730: r0 = _interpolate()
    //     0xe66730: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe66734: stur            x0, [fp, #-0x20]
    // 0xe66738: r0 = PdfName()
    //     0xe66738: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0xe6673c: mov             x3, x0
    // 0xe66740: ldur            x0, [fp, #-0x20]
    // 0xe66744: stur            x3, [fp, #-0x60]
    // 0xe66748: StoreField: r3->field_7 = r0
    //     0xe66748: stur            w0, [x3, #7]
    // 0xe6674c: ldur            x4, [fp, #-0x58]
    // 0xe66750: LoadField: r5 = r4->field_7
    //     0xe66750: ldur            w5, [x4, #7]
    // 0xe66754: DecompressPointer r5
    //     0xe66754: add             x5, x5, HEAP, lsl #32
    // 0xe66758: mov             x0, x3
    // 0xe6675c: mov             x2, x5
    // 0xe66760: stur            x5, [fp, #-0x20]
    // 0xe66764: r1 = Null
    //     0xe66764: mov             x1, NULL
    // 0xe66768: cmp             w2, NULL
    // 0xe6676c: b.eq            #0xe66790
    // 0xe66770: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe66770: ldur            w4, [x2, #0x17]
    // 0xe66774: DecompressPointer r4
    //     0xe66774: add             x4, x4, HEAP, lsl #32
    // 0xe66778: r8 = X0 bound PdfDataType
    //     0xe66778: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe6677c: ldr             x8, [x8, #0x6d8]
    // 0xe66780: LoadField: r9 = r4->field_7
    //     0xe66780: ldur            x9, [x4, #7]
    // 0xe66784: r3 = Null
    //     0xe66784: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3de50] Null
    //     0xe66788: ldr             x3, [x3, #0xe50]
    // 0xe6678c: blr             x9
    // 0xe66790: ldur            x0, [fp, #-0x58]
    // 0xe66794: LoadField: r4 = r0->field_b
    //     0xe66794: ldur            w4, [x0, #0xb]
    // 0xe66798: DecompressPointer r4
    //     0xe66798: add             x4, x4, HEAP, lsl #32
    // 0xe6679c: mov             x1, x4
    // 0xe667a0: ldur            x3, [fp, #-0x60]
    // 0xe667a4: stur            x4, [fp, #-0x68]
    // 0xe667a8: r2 = "/BaseFont"
    //     0xe667a8: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3de60] "/BaseFont"
    //     0xe667ac: ldr             x2, [x2, #0xe60]
    // 0xe667b0: r0 = []=()
    //     0xe667b0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe667b4: ldur            x2, [fp, #-0x20]
    // 0xe667b8: r0 = Instance_PdfNum
    //     0xe667b8: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c148] Obj!PdfNum@e0c771
    //     0xe667bc: ldr             x0, [x0, #0x148]
    // 0xe667c0: r1 = Null
    //     0xe667c0: mov             x1, NULL
    // 0xe667c4: cmp             w2, NULL
    // 0xe667c8: b.eq            #0xe667ec
    // 0xe667cc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe667cc: ldur            w4, [x2, #0x17]
    // 0xe667d0: DecompressPointer r4
    //     0xe667d0: add             x4, x4, HEAP, lsl #32
    // 0xe667d4: r8 = X0 bound PdfDataType
    //     0xe667d4: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe667d8: ldr             x8, [x8, #0x6d8]
    // 0xe667dc: LoadField: r9 = r4->field_7
    //     0xe667dc: ldur            x9, [x4, #7]
    // 0xe667e0: r3 = Null
    //     0xe667e0: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3de68] Null
    //     0xe667e4: ldr             x3, [x3, #0xe68]
    // 0xe667e8: blr             x9
    // 0xe667ec: ldur            x1, [fp, #-0x68]
    // 0xe667f0: r2 = "/FirstChar"
    //     0xe667f0: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3de78] "/FirstChar"
    //     0xe667f4: ldr             x2, [x2, #0xe78]
    // 0xe667f8: r3 = Instance_PdfNum
    //     0xe667f8: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c148] Obj!PdfNum@e0c771
    //     0xe667fc: ldr             x3, [x3, #0x148]
    // 0xe66800: r0 = []=()
    //     0xe66800: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe66804: ldur            x2, [fp, #-0x20]
    // 0xe66808: r0 = Instance_PdfNum
    //     0xe66808: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3de80] Obj!PdfNum@e0c7c1
    //     0xe6680c: ldr             x0, [x0, #0xe80]
    // 0xe66810: r1 = Null
    //     0xe66810: mov             x1, NULL
    // 0xe66814: cmp             w2, NULL
    // 0xe66818: b.eq            #0xe6683c
    // 0xe6681c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6681c: ldur            w4, [x2, #0x17]
    // 0xe66820: DecompressPointer r4
    //     0xe66820: add             x4, x4, HEAP, lsl #32
    // 0xe66824: r8 = X0 bound PdfDataType
    //     0xe66824: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe66828: ldr             x8, [x8, #0x6d8]
    // 0xe6682c: LoadField: r9 = r4->field_7
    //     0xe6682c: ldur            x9, [x4, #7]
    // 0xe66830: r3 = Null
    //     0xe66830: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3de88] Null
    //     0xe66834: ldr             x3, [x3, #0xe88]
    // 0xe66838: blr             x9
    // 0xe6683c: ldur            x1, [fp, #-0x68]
    // 0xe66840: r2 = "/LastChar"
    //     0xe66840: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3de98] "/LastChar"
    //     0xe66844: ldr             x2, [x2, #0xe98]
    // 0xe66848: r3 = Instance_PdfNum
    //     0xe66848: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3de80] Obj!PdfNum@e0c7c1
    //     0xe6684c: ldr             x3, [x3, #0xe80]
    // 0xe66850: r0 = []=()
    //     0xe66850: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe66854: ldur            x0, [fp, #-8]
    // 0xe66858: LoadField: r1 = r0->field_b
    //     0xe66858: ldur            w1, [x0, #0xb]
    // 0xe6685c: cbz             w1, #0xe668e0
    // 0xe66860: ldur            x2, [fp, #-0x50]
    // 0xe66864: r1 = Function '<anonymous closure>':.
    //     0xe66864: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dea0] AnonymousClosure: (0xe66f94), in [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::PdfType1Font.create (0xe66524)
    //     0xe66868: ldr             x1, [x1, #0xea0]
    // 0xe6686c: r0 = AllocateClosure()
    //     0xe6686c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe66870: r16 = <num>
    //     0xe66870: ldr             x16, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0xe66874: ldur            lr, [fp, #-8]
    // 0xe66878: stp             lr, x16, [SP, #8]
    // 0xe6687c: str             x0, [SP]
    // 0xe66880: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe66880: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe66884: r0 = map()
    //     0xe66884: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xe66888: mov             x1, x0
    // 0xe6688c: r0 = fromNum()
    //     0xe6688c: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0xe66890: ldur            x2, [fp, #-0x20]
    // 0xe66894: mov             x3, x0
    // 0xe66898: r1 = Null
    //     0xe66898: mov             x1, NULL
    // 0xe6689c: stur            x3, [fp, #-8]
    // 0xe668a0: cmp             w2, NULL
    // 0xe668a4: b.eq            #0xe668c8
    // 0xe668a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe668a8: ldur            w4, [x2, #0x17]
    // 0xe668ac: DecompressPointer r4
    //     0xe668ac: add             x4, x4, HEAP, lsl #32
    // 0xe668b0: r8 = X0 bound PdfDataType
    //     0xe668b0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe668b4: ldr             x8, [x8, #0x6d8]
    // 0xe668b8: LoadField: r9 = r4->field_7
    //     0xe668b8: ldur            x9, [x4, #7]
    // 0xe668bc: r3 = Null
    //     0xe668bc: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dea8] Null
    //     0xe668c0: ldr             x3, [x3, #0xea8]
    // 0xe668c4: blr             x9
    // 0xe668c8: ldur            x1, [fp, #-0x68]
    // 0xe668cc: ldur            x3, [fp, #-8]
    // 0xe668d0: r2 = "/Widths"
    //     0xe668d0: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3deb8] "/Widths"
    //     0xe668d4: ldr             x2, [x2, #0xeb8]
    // 0xe668d8: r0 = []=()
    //     0xe668d8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe668dc: b               #0xe669c0
    // 0xe668e0: d0 = 600.000000
    //     0xe668e0: add             x17, PP, #0x34, lsl #12  ; [pp+0x34d20] IMM: double(600) from 0x4082c00000000000
    //     0xe668e4: ldr             d0, [x17, #0xd20]
    // 0xe668e8: fcmp            d0, d0
    // 0xe668ec: b.vs            #0xe66ee4
    // 0xe668f0: fcvtzs          x0, d0
    // 0xe668f4: asr             x16, x0, #0x1e
    // 0xe668f8: cmp             x16, x0, asr #63
    // 0xe668fc: b.ne            #0xe66ee4
    // 0xe66900: lsl             x0, x0, #1
    // 0xe66904: stur            x0, [fp, #-8]
    // 0xe66908: r1 = <int>
    //     0xe66908: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe6690c: r2 = 512
    //     0xe6690c: movz            x2, #0x200
    // 0xe66910: r0 = AllocateArray()
    //     0xe66910: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe66914: mov             x2, x0
    // 0xe66918: r3 = 0
    //     0xe66918: movz            x3, #0
    // 0xe6691c: CheckStackOverflow
    //     0xe6691c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe66920: cmp             SP, x16
    //     0xe66924: b.ls            #0xe66f00
    // 0xe66928: cmp             x3, #0x100
    // 0xe6692c: b.ge            #0xe6696c
    // 0xe66930: mov             x1, x2
    // 0xe66934: ldur            x0, [fp, #-8]
    // 0xe66938: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe66938: add             x25, x1, x3, lsl #2
    //     0xe6693c: add             x25, x25, #0xf
    //     0xe66940: str             w0, [x25]
    //     0xe66944: tbz             w0, #0, #0xe66960
    //     0xe66948: ldurb           w16, [x1, #-1]
    //     0xe6694c: ldurb           w17, [x0, #-1]
    //     0xe66950: and             x16, x17, x16, lsr #2
    //     0xe66954: tst             x16, HEAP, lsr #32
    //     0xe66958: b.eq            #0xe66960
    //     0xe6695c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66960: add             x0, x3, #1
    // 0xe66964: mov             x3, x0
    // 0xe66968: b               #0xe6691c
    // 0xe6696c: mov             x1, x2
    // 0xe66970: r0 = fromNum()
    //     0xe66970: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0xe66974: ldur            x2, [fp, #-0x20]
    // 0xe66978: mov             x3, x0
    // 0xe6697c: r1 = Null
    //     0xe6697c: mov             x1, NULL
    // 0xe66980: stur            x3, [fp, #-8]
    // 0xe66984: cmp             w2, NULL
    // 0xe66988: b.eq            #0xe669ac
    // 0xe6698c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe6698c: ldur            w4, [x2, #0x17]
    // 0xe66990: DecompressPointer r4
    //     0xe66990: add             x4, x4, HEAP, lsl #32
    // 0xe66994: r8 = X0 bound PdfDataType
    //     0xe66994: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe66998: ldr             x8, [x8, #0x6d8]
    // 0xe6699c: LoadField: r9 = r4->field_7
    //     0xe6699c: ldur            x9, [x4, #7]
    // 0xe669a0: r3 = Null
    //     0xe669a0: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dec0] Null
    //     0xe669a4: ldr             x3, [x3, #0xec0]
    // 0xe669a8: blr             x9
    // 0xe669ac: ldur            x1, [fp, #-0x68]
    // 0xe669b0: ldur            x3, [fp, #-8]
    // 0xe669b4: r2 = "/Widths"
    //     0xe669b4: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3deb8] "/Widths"
    //     0xe669b8: ldr             x2, [x2, #0xeb8]
    // 0xe669bc: r0 = []=()
    //     0xe669bc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe669c0: ldur            d0, [fp, #-0x78]
    // 0xe669c4: ldur            x4, [fp, #-0x30]
    // 0xe669c8: ldur            d1, [fp, #-0x80]
    // 0xe669cc: ldur            x0, [fp, #-0x40]
    // 0xe669d0: ldur            x3, [fp, #-0x48]
    // 0xe669d4: ldur            x5, [fp, #-0x18]
    // 0xe669d8: ldur            x6, [fp, #-0x10]
    // 0xe669dc: ldur            d2, [fp, #-0x70]
    // 0xe669e0: r1 = Null
    //     0xe669e0: mov             x1, NULL
    // 0xe669e4: r2 = 44
    //     0xe669e4: movz            x2, #0x2c
    // 0xe669e8: r0 = AllocateArray()
    //     0xe669e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe669ec: stur            x0, [fp, #-8]
    // 0xe669f0: r16 = "/Type"
    //     0xe669f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36630] "/Type"
    //     0xe669f4: ldr             x16, [x16, #0x630]
    // 0xe669f8: StoreField: r0->field_f = r16
    //     0xe669f8: stur            w16, [x0, #0xf]
    // 0xe669fc: r16 = Instance_PdfName
    //     0xe669fc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ded0] Obj!PdfName@e0c8b1
    //     0xe66a00: ldr             x16, [x16, #0xed0]
    // 0xe66a04: StoreField: r0->field_13 = r16
    //     0xe66a04: stur            w16, [x0, #0x13]
    // 0xe66a08: r16 = "/FontName"
    //     0xe66a08: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ded8] "/FontName"
    //     0xe66a0c: ldr             x16, [x16, #0xed8]
    // 0xe66a10: ArrayStore: r0[0] = r16  ; List_4
    //     0xe66a10: stur            w16, [x0, #0x17]
    // 0xe66a14: r1 = Null
    //     0xe66a14: mov             x1, NULL
    // 0xe66a18: r2 = 4
    //     0xe66a18: movz            x2, #0x4
    // 0xe66a1c: r0 = AllocateArray()
    //     0xe66a1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe66a20: r16 = "/"
    //     0xe66a20: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xe66a24: StoreField: r0->field_f = r16
    //     0xe66a24: stur            w16, [x0, #0xf]
    // 0xe66a28: ldur            x1, [fp, #-0x40]
    // 0xe66a2c: StoreField: r0->field_13 = r1
    //     0xe66a2c: stur            w1, [x0, #0x13]
    // 0xe66a30: str             x0, [SP]
    // 0xe66a34: r0 = _interpolate()
    //     0xe66a34: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe66a38: stur            x0, [fp, #-0x40]
    // 0xe66a3c: r0 = PdfName()
    //     0xe66a3c: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0xe66a40: mov             x1, x0
    // 0xe66a44: ldur            x0, [fp, #-0x40]
    // 0xe66a48: StoreField: r1->field_7 = r0
    //     0xe66a48: stur            w0, [x1, #7]
    // 0xe66a4c: mov             x0, x1
    // 0xe66a50: ldur            x1, [fp, #-8]
    // 0xe66a54: ArrayStore: r1[3] = r0  ; List_4
    //     0xe66a54: add             x25, x1, #0x1b
    //     0xe66a58: str             w0, [x25]
    //     0xe66a5c: tbz             w0, #0, #0xe66a78
    //     0xe66a60: ldurb           w16, [x1, #-1]
    //     0xe66a64: ldurb           w17, [x0, #-1]
    //     0xe66a68: and             x16, x17, x16, lsr #2
    //     0xe66a6c: tst             x16, HEAP, lsr #32
    //     0xe66a70: b.eq            #0xe66a78
    //     0xe66a74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66a78: ldur            x1, [fp, #-8]
    // 0xe66a7c: r16 = "/Flags"
    //     0xe66a7c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dee0] "/Flags"
    //     0xe66a80: ldr             x16, [x16, #0xee0]
    // 0xe66a84: StoreField: r1->field_1f = r16
    //     0xe66a84: stur            w16, [x1, #0x1f]
    // 0xe66a88: ldur            x0, [fp, #-0x10]
    // 0xe66a8c: tst             x0, #0x10
    // 0xe66a90: cset            x2, eq
    // 0xe66a94: lsl             x2, x2, #1
    // 0xe66a98: r0 = LoadInt32Instr(r2)
    //     0xe66a98: sbfx            x0, x2, #1, #0x1f
    // 0xe66a9c: add             x2, x0, #0x20
    // 0xe66aa0: lsl             x0, x2, #1
    // 0xe66aa4: stur            x0, [fp, #-0x10]
    // 0xe66aa8: r0 = PdfNum()
    //     0xe66aa8: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe66aac: mov             x1, x0
    // 0xe66ab0: ldur            x0, [fp, #-0x10]
    // 0xe66ab4: StoreField: r1->field_7 = r0
    //     0xe66ab4: stur            w0, [x1, #7]
    // 0xe66ab8: mov             x0, x1
    // 0xe66abc: ldur            x1, [fp, #-8]
    // 0xe66ac0: ArrayStore: r1[5] = r0  ; List_4
    //     0xe66ac0: add             x25, x1, #0x23
    //     0xe66ac4: str             w0, [x25]
    //     0xe66ac8: tbz             w0, #0, #0xe66ae4
    //     0xe66acc: ldurb           w16, [x1, #-1]
    //     0xe66ad0: ldurb           w17, [x0, #-1]
    //     0xe66ad4: and             x16, x17, x16, lsr #2
    //     0xe66ad8: tst             x16, HEAP, lsr #32
    //     0xe66adc: b.eq            #0xe66ae4
    //     0xe66ae0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66ae4: ldur            x0, [fp, #-8]
    // 0xe66ae8: r16 = "/FontBBox"
    //     0xe66ae8: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dee8] "/FontBBox"
    //     0xe66aec: ldr             x16, [x16, #0xee8]
    // 0xe66af0: StoreField: r0->field_27 = r16
    //     0xe66af0: stur            w16, [x0, #0x27]
    // 0xe66af4: ldur            x1, [fp, #-0x38]
    // 0xe66af8: r0 = fromNum()
    //     0xe66af8: bl              #0x7c9bb8  ; [package:pdf/src/pdf/format/array.dart] PdfArray::fromNum
    // 0xe66afc: ldur            x1, [fp, #-8]
    // 0xe66b00: ArrayStore: r1[7] = r0  ; List_4
    //     0xe66b00: add             x25, x1, #0x2b
    //     0xe66b04: str             w0, [x25]
    //     0xe66b08: tbz             w0, #0, #0xe66b24
    //     0xe66b0c: ldurb           w16, [x1, #-1]
    //     0xe66b10: ldurb           w17, [x0, #-1]
    //     0xe66b14: and             x16, x17, x16, lsr #2
    //     0xe66b18: tst             x16, HEAP, lsr #32
    //     0xe66b1c: b.eq            #0xe66b24
    //     0xe66b20: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66b24: ldur            x1, [fp, #-8]
    // 0xe66b28: r16 = "/Ascent"
    //     0xe66b28: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3def0] "/Ascent"
    //     0xe66b2c: ldr             x16, [x16, #0xef0]
    // 0xe66b30: StoreField: r1->field_2f = r16
    //     0xe66b30: stur            w16, [x1, #0x2f]
    // 0xe66b34: ldur            d0, [fp, #-0x78]
    // 0xe66b38: d1 = 1000.000000
    //     0xe66b38: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0xe66b3c: ldr             d1, [x17, #0x238]
    // 0xe66b40: fmul            d2, d0, d1
    // 0xe66b44: fcmp            d2, d2
    // 0xe66b48: b.vs            #0xe66f08
    // 0xe66b4c: fcvtzs          x0, d2
    // 0xe66b50: asr             x16, x0, #0x1e
    // 0xe66b54: cmp             x16, x0, asr #63
    // 0xe66b58: b.ne            #0xe66f08
    // 0xe66b5c: lsl             x0, x0, #1
    // 0xe66b60: stur            x0, [fp, #-0x10]
    // 0xe66b64: r0 = PdfNum()
    //     0xe66b64: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe66b68: mov             x1, x0
    // 0xe66b6c: ldur            x0, [fp, #-0x10]
    // 0xe66b70: StoreField: r1->field_7 = r0
    //     0xe66b70: stur            w0, [x1, #7]
    // 0xe66b74: mov             x0, x1
    // 0xe66b78: ldur            x1, [fp, #-8]
    // 0xe66b7c: ArrayStore: r1[9] = r0  ; List_4
    //     0xe66b7c: add             x25, x1, #0x33
    //     0xe66b80: str             w0, [x25]
    //     0xe66b84: tbz             w0, #0, #0xe66ba0
    //     0xe66b88: ldurb           w16, [x1, #-1]
    //     0xe66b8c: ldurb           w17, [x0, #-1]
    //     0xe66b90: and             x16, x17, x16, lsr #2
    //     0xe66b94: tst             x16, HEAP, lsr #32
    //     0xe66b98: b.eq            #0xe66ba0
    //     0xe66b9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66ba0: ldur            x1, [fp, #-8]
    // 0xe66ba4: r16 = "/Descent"
    //     0xe66ba4: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3def8] "/Descent"
    //     0xe66ba8: ldr             x16, [x16, #0xef8]
    // 0xe66bac: StoreField: r1->field_37 = r16
    //     0xe66bac: stur            w16, [x1, #0x37]
    // 0xe66bb0: ldur            d1, [fp, #-0x80]
    // 0xe66bb4: d0 = 1000.000000
    //     0xe66bb4: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0xe66bb8: ldr             d0, [x17, #0x238]
    // 0xe66bbc: fmul            d2, d1, d0
    // 0xe66bc0: fcmp            d2, d2
    // 0xe66bc4: b.vs            #0xe66f30
    // 0xe66bc8: fcvtzs          x0, d2
    // 0xe66bcc: asr             x16, x0, #0x1e
    // 0xe66bd0: cmp             x16, x0, asr #63
    // 0xe66bd4: b.ne            #0xe66f30
    // 0xe66bd8: lsl             x0, x0, #1
    // 0xe66bdc: stur            x0, [fp, #-0x10]
    // 0xe66be0: r0 = PdfNum()
    //     0xe66be0: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe66be4: mov             x1, x0
    // 0xe66be8: ldur            x0, [fp, #-0x10]
    // 0xe66bec: StoreField: r1->field_7 = r0
    //     0xe66bec: stur            w0, [x1, #7]
    // 0xe66bf0: mov             x0, x1
    // 0xe66bf4: ldur            x1, [fp, #-8]
    // 0xe66bf8: ArrayStore: r1[11] = r0  ; List_4
    //     0xe66bf8: add             x25, x1, #0x3b
    //     0xe66bfc: str             w0, [x25]
    //     0xe66c00: tbz             w0, #0, #0xe66c1c
    //     0xe66c04: ldurb           w16, [x1, #-1]
    //     0xe66c08: ldurb           w17, [x0, #-1]
    //     0xe66c0c: and             x16, x17, x16, lsr #2
    //     0xe66c10: tst             x16, HEAP, lsr #32
    //     0xe66c14: b.eq            #0xe66c1c
    //     0xe66c18: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66c1c: ldur            x1, [fp, #-8]
    // 0xe66c20: r16 = "/ItalicAngle"
    //     0xe66c20: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df00] "/ItalicAngle"
    //     0xe66c24: ldr             x16, [x16, #0xf00]
    // 0xe66c28: StoreField: r1->field_3f = r16
    //     0xe66c28: stur            w16, [x1, #0x3f]
    // 0xe66c2c: ldur            d0, [fp, #-0x70]
    // 0xe66c30: r0 = inline_Allocate_Double()
    //     0xe66c30: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe66c34: add             x0, x0, #0x10
    //     0xe66c38: cmp             x2, x0
    //     0xe66c3c: b.ls            #0xe66f58
    //     0xe66c40: str             x0, [THR, #0x50]  ; THR::top
    //     0xe66c44: sub             x0, x0, #0xf
    //     0xe66c48: movz            x2, #0xe15c
    //     0xe66c4c: movk            x2, #0x3, lsl #16
    //     0xe66c50: stur            x2, [x0, #-1]
    // 0xe66c54: StoreField: r0->field_7 = d0
    //     0xe66c54: stur            d0, [x0, #7]
    // 0xe66c58: stur            x0, [fp, #-0x10]
    // 0xe66c5c: r0 = PdfNum()
    //     0xe66c5c: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe66c60: mov             x1, x0
    // 0xe66c64: ldur            x0, [fp, #-0x10]
    // 0xe66c68: StoreField: r1->field_7 = r0
    //     0xe66c68: stur            w0, [x1, #7]
    // 0xe66c6c: mov             x0, x1
    // 0xe66c70: ldur            x1, [fp, #-8]
    // 0xe66c74: ArrayStore: r1[13] = r0  ; List_4
    //     0xe66c74: add             x25, x1, #0x43
    //     0xe66c78: str             w0, [x25]
    //     0xe66c7c: tbz             w0, #0, #0xe66c98
    //     0xe66c80: ldurb           w16, [x1, #-1]
    //     0xe66c84: ldurb           w17, [x0, #-1]
    //     0xe66c88: and             x16, x17, x16, lsr #2
    //     0xe66c8c: tst             x16, HEAP, lsr #32
    //     0xe66c90: b.eq            #0xe66c98
    //     0xe66c94: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66c98: ldur            x1, [fp, #-8]
    // 0xe66c9c: r16 = "/CapHeight"
    //     0xe66c9c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df08] "/CapHeight"
    //     0xe66ca0: ldr             x16, [x16, #0xf08]
    // 0xe66ca4: StoreField: r1->field_47 = r16
    //     0xe66ca4: stur            w16, [x1, #0x47]
    // 0xe66ca8: ldur            x0, [fp, #-0x30]
    // 0xe66cac: lsl             x2, x0, #1
    // 0xe66cb0: stur            x2, [fp, #-0x10]
    // 0xe66cb4: r0 = PdfNum()
    //     0xe66cb4: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe66cb8: mov             x1, x0
    // 0xe66cbc: ldur            x0, [fp, #-0x10]
    // 0xe66cc0: StoreField: r1->field_7 = r0
    //     0xe66cc0: stur            w0, [x1, #7]
    // 0xe66cc4: mov             x0, x1
    // 0xe66cc8: ldur            x1, [fp, #-8]
    // 0xe66ccc: ArrayStore: r1[15] = r0  ; List_4
    //     0xe66ccc: add             x25, x1, #0x4b
    //     0xe66cd0: str             w0, [x25]
    //     0xe66cd4: tbz             w0, #0, #0xe66cf0
    //     0xe66cd8: ldurb           w16, [x1, #-1]
    //     0xe66cdc: ldurb           w17, [x0, #-1]
    //     0xe66ce0: and             x16, x17, x16, lsr #2
    //     0xe66ce4: tst             x16, HEAP, lsr #32
    //     0xe66ce8: b.eq            #0xe66cf0
    //     0xe66cec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66cf0: ldur            x2, [fp, #-8]
    // 0xe66cf4: r16 = "/StemV"
    //     0xe66cf4: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df10] "/StemV"
    //     0xe66cf8: ldr             x16, [x16, #0xf10]
    // 0xe66cfc: StoreField: r2->field_4f = r16
    //     0xe66cfc: stur            w16, [x2, #0x4f]
    // 0xe66d00: ldur            x3, [fp, #-0x18]
    // 0xe66d04: r0 = BoxInt64Instr(r3)
    //     0xe66d04: sbfiz           x0, x3, #1, #0x1f
    //     0xe66d08: cmp             x3, x0, asr #1
    //     0xe66d0c: b.eq            #0xe66d18
    //     0xe66d10: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe66d14: stur            x3, [x0, #7]
    // 0xe66d18: stur            x0, [fp, #-0x10]
    // 0xe66d1c: r0 = PdfNum()
    //     0xe66d1c: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe66d20: mov             x1, x0
    // 0xe66d24: ldur            x0, [fp, #-0x10]
    // 0xe66d28: StoreField: r1->field_7 = r0
    //     0xe66d28: stur            w0, [x1, #7]
    // 0xe66d2c: mov             x0, x1
    // 0xe66d30: ldur            x1, [fp, #-8]
    // 0xe66d34: ArrayStore: r1[17] = r0  ; List_4
    //     0xe66d34: add             x25, x1, #0x53
    //     0xe66d38: str             w0, [x25]
    //     0xe66d3c: tbz             w0, #0, #0xe66d58
    //     0xe66d40: ldurb           w16, [x1, #-1]
    //     0xe66d44: ldurb           w17, [x0, #-1]
    //     0xe66d48: and             x16, x17, x16, lsr #2
    //     0xe66d4c: tst             x16, HEAP, lsr #32
    //     0xe66d50: b.eq            #0xe66d58
    //     0xe66d54: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66d58: ldur            x1, [fp, #-8]
    // 0xe66d5c: r16 = "/StemH"
    //     0xe66d5c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df18] "/StemH"
    //     0xe66d60: ldr             x16, [x16, #0xf18]
    // 0xe66d64: StoreField: r1->field_57 = r16
    //     0xe66d64: stur            w16, [x1, #0x57]
    // 0xe66d68: ldur            x0, [fp, #-0x48]
    // 0xe66d6c: lsl             x2, x0, #1
    // 0xe66d70: stur            x2, [fp, #-0x10]
    // 0xe66d74: r0 = PdfNum()
    //     0xe66d74: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe66d78: mov             x1, x0
    // 0xe66d7c: ldur            x0, [fp, #-0x10]
    // 0xe66d80: StoreField: r1->field_7 = r0
    //     0xe66d80: stur            w0, [x1, #7]
    // 0xe66d84: mov             x0, x1
    // 0xe66d88: ldur            x1, [fp, #-8]
    // 0xe66d8c: ArrayStore: r1[19] = r0  ; List_4
    //     0xe66d8c: add             x25, x1, #0x5b
    //     0xe66d90: str             w0, [x25]
    //     0xe66d94: tbz             w0, #0, #0xe66db0
    //     0xe66d98: ldurb           w16, [x1, #-1]
    //     0xe66d9c: ldurb           w17, [x0, #-1]
    //     0xe66da0: and             x16, x17, x16, lsr #2
    //     0xe66da4: tst             x16, HEAP, lsr #32
    //     0xe66da8: b.eq            #0xe66db0
    //     0xe66dac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66db0: ldur            x1, [fp, #-8]
    // 0xe66db4: r16 = "/MissingWidth"
    //     0xe66db4: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3df20] "/MissingWidth"
    //     0xe66db8: ldr             x16, [x16, #0xf20]
    // 0xe66dbc: StoreField: r1->field_5f = r16
    //     0xe66dbc: stur            w16, [x1, #0x5f]
    // 0xe66dc0: d0 = 600.000000
    //     0xe66dc0: add             x17, PP, #0x34, lsl #12  ; [pp+0x34d20] IMM: double(600) from 0x4082c00000000000
    //     0xe66dc4: ldr             d0, [x17, #0xd20]
    // 0xe66dc8: fcmp            d0, d0
    // 0xe66dcc: b.vs            #0xe66f70
    // 0xe66dd0: fcvtzs          x0, d0
    // 0xe66dd4: asr             x16, x0, #0x1e
    // 0xe66dd8: cmp             x16, x0, asr #63
    // 0xe66ddc: b.ne            #0xe66f70
    // 0xe66de0: lsl             x0, x0, #1
    // 0xe66de4: stur            x0, [fp, #-0x10]
    // 0xe66de8: r0 = PdfNum()
    //     0xe66de8: bl              #0x7b5d40  ; AllocatePdfNumStub -> PdfNum (size=0xc)
    // 0xe66dec: mov             x1, x0
    // 0xe66df0: ldur            x0, [fp, #-0x10]
    // 0xe66df4: StoreField: r1->field_7 = r0
    //     0xe66df4: stur            w0, [x1, #7]
    // 0xe66df8: mov             x0, x1
    // 0xe66dfc: ldur            x1, [fp, #-8]
    // 0xe66e00: ArrayStore: r1[21] = r0  ; List_4
    //     0xe66e00: add             x25, x1, #0x63
    //     0xe66e04: str             w0, [x25]
    //     0xe66e08: tbz             w0, #0, #0xe66e24
    //     0xe66e0c: ldurb           w16, [x1, #-1]
    //     0xe66e10: ldurb           w17, [x0, #-1]
    //     0xe66e14: and             x16, x17, x16, lsr #2
    //     0xe66e18: tst             x16, HEAP, lsr #32
    //     0xe66e1c: b.eq            #0xe66e24
    //     0xe66e20: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe66e24: r16 = <String, PdfDataType>
    //     0xe66e24: add             x16, PP, #0x36, lsl #12  ; [pp+0x36820] TypeArguments: <String, PdfDataType>
    //     0xe66e28: ldr             x16, [x16, #0x820]
    // 0xe66e2c: ldur            lr, [fp, #-8]
    // 0xe66e30: stp             lr, x16, [SP]
    // 0xe66e34: r0 = Map._fromLiteral()
    //     0xe66e34: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe66e38: r1 = <PdfDataType>
    //     0xe66e38: add             x1, PP, #0x31, lsl #12  ; [pp+0x314c8] TypeArguments: <PdfDataType>
    //     0xe66e3c: ldr             x1, [x1, #0x4c8]
    // 0xe66e40: stur            x0, [fp, #-8]
    // 0xe66e44: r0 = PdfDict()
    //     0xe66e44: bl              #0x7b5e34  ; AllocatePdfDictStub -> PdfDict<X0 bound PdfDataType> (size=0x10)
    // 0xe66e48: mov             x2, x0
    // 0xe66e4c: ldur            x0, [fp, #-8]
    // 0xe66e50: stur            x2, [fp, #-0x10]
    // 0xe66e54: StoreField: r2->field_b = r0
    //     0xe66e54: stur            w0, [x2, #0xb]
    // 0xe66e58: r1 = <PdfDict<PdfDataType>>
    //     0xe66e58: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66e5c: ldr             x1, [x1, #0x758]
    // 0xe66e60: r0 = PdfObject()
    //     0xe66e60: bl              #0xe65e84  ; AllocatePdfObjectStub -> PdfObject<X0 bound PdfDataType> (size=0x2c)
    // 0xe66e64: mov             x1, x0
    // 0xe66e68: ldur            x2, [fp, #-0x28]
    // 0xe66e6c: ldur            x3, [fp, #-0x10]
    // 0xe66e70: stur            x0, [fp, #-8]
    // 0xe66e74: r0 = PdfObject()
    //     0xe66e74: bl              #0x7cb490  ; [package:pdf/src/pdf/obj/object.dart] PdfObject::PdfObject
    // 0xe66e78: ldur            x1, [fp, #-8]
    // 0xe66e7c: r0 = ref()
    //     0xe66e7c: bl              #0x7b5c90  ; [package:pdf/src/pdf/format/object_base.dart] PdfObjectBase::ref
    // 0xe66e80: ldur            x2, [fp, #-0x20]
    // 0xe66e84: mov             x3, x0
    // 0xe66e88: r1 = Null
    //     0xe66e88: mov             x1, NULL
    // 0xe66e8c: stur            x3, [fp, #-8]
    // 0xe66e90: cmp             w2, NULL
    // 0xe66e94: b.eq            #0xe66eb8
    // 0xe66e98: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe66e98: ldur            w4, [x2, #0x17]
    // 0xe66e9c: DecompressPointer r4
    //     0xe66e9c: add             x4, x4, HEAP, lsl #32
    // 0xe66ea0: r8 = X0 bound PdfDataType
    //     0xe66ea0: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe66ea4: ldr             x8, [x8, #0x6d8]
    // 0xe66ea8: LoadField: r9 = r4->field_7
    //     0xe66ea8: ldur            x9, [x4, #7]
    // 0xe66eac: r3 = Null
    //     0xe66eac: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3df28] Null
    //     0xe66eb0: ldr             x3, [x3, #0xf28]
    // 0xe66eb4: blr             x9
    // 0xe66eb8: ldur            x1, [fp, #-0x68]
    // 0xe66ebc: ldur            x3, [fp, #-8]
    // 0xe66ec0: r2 = "/FontDescriptor"
    //     0xe66ec0: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3df38] "/FontDescriptor"
    //     0xe66ec4: ldr             x2, [x2, #0xf38]
    // 0xe66ec8: r0 = []=()
    //     0xe66ec8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe66ecc: r0 = Null
    //     0xe66ecc: mov             x0, NULL
    // 0xe66ed0: LeaveFrame
    //     0xe66ed0: mov             SP, fp
    //     0xe66ed4: ldp             fp, lr, [SP], #0x10
    // 0xe66ed8: ret
    //     0xe66ed8: ret             
    // 0xe66edc: r0 = StackOverflowSharedWithFPURegs()
    //     0xe66edc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe66ee0: b               #0xe6667c
    // 0xe66ee4: SaveReg d0
    //     0xe66ee4: str             q0, [SP, #-0x10]!
    // 0xe66ee8: r0 = 74
    //     0xe66ee8: movz            x0, #0x4a
    // 0xe66eec: r30 = DoubleToIntegerStub
    //     0xe66eec: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xe66ef0: LoadField: r30 = r30->field_7
    //     0xe66ef0: ldur            lr, [lr, #7]
    // 0xe66ef4: blr             lr
    // 0xe66ef8: RestoreReg d0
    //     0xe66ef8: ldr             q0, [SP], #0x10
    // 0xe66efc: b               #0xe66904
    // 0xe66f00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe66f00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe66f04: b               #0xe66928
    // 0xe66f08: stp             q1, q2, [SP, #-0x20]!
    // 0xe66f0c: SaveReg r1
    //     0xe66f0c: str             x1, [SP, #-8]!
    // 0xe66f10: d0 = 0.000000
    //     0xe66f10: fmov            d0, d2
    // 0xe66f14: r0 = 74
    //     0xe66f14: movz            x0, #0x4a
    // 0xe66f18: r30 = DoubleToIntegerStub
    //     0xe66f18: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xe66f1c: LoadField: r30 = r30->field_7
    //     0xe66f1c: ldur            lr, [lr, #7]
    // 0xe66f20: blr             lr
    // 0xe66f24: RestoreReg r1
    //     0xe66f24: ldr             x1, [SP], #8
    // 0xe66f28: ldp             q1, q2, [SP], #0x20
    // 0xe66f2c: b               #0xe66b60
    // 0xe66f30: SaveReg d2
    //     0xe66f30: str             q2, [SP, #-0x10]!
    // 0xe66f34: SaveReg r1
    //     0xe66f34: str             x1, [SP, #-8]!
    // 0xe66f38: d0 = 0.000000
    //     0xe66f38: fmov            d0, d2
    // 0xe66f3c: r0 = 74
    //     0xe66f3c: movz            x0, #0x4a
    // 0xe66f40: r30 = DoubleToIntegerStub
    //     0xe66f40: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xe66f44: LoadField: r30 = r30->field_7
    //     0xe66f44: ldur            lr, [lr, #7]
    // 0xe66f48: blr             lr
    // 0xe66f4c: RestoreReg r1
    //     0xe66f4c: ldr             x1, [SP], #8
    // 0xe66f50: RestoreReg d2
    //     0xe66f50: ldr             q2, [SP], #0x10
    // 0xe66f54: b               #0xe66bdc
    // 0xe66f58: SaveReg d0
    //     0xe66f58: str             q0, [SP, #-0x10]!
    // 0xe66f5c: SaveReg r1
    //     0xe66f5c: str             x1, [SP, #-8]!
    // 0xe66f60: r0 = AllocateDouble()
    //     0xe66f60: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe66f64: RestoreReg r1
    //     0xe66f64: ldr             x1, [SP], #8
    // 0xe66f68: RestoreReg d0
    //     0xe66f68: ldr             q0, [SP], #0x10
    // 0xe66f6c: b               #0xe66c54
    // 0xe66f70: SaveReg d0
    //     0xe66f70: str             q0, [SP, #-0x10]!
    // 0xe66f74: SaveReg r1
    //     0xe66f74: str             x1, [SP, #-8]!
    // 0xe66f78: r0 = 74
    //     0xe66f78: movz            x0, #0x4a
    // 0xe66f7c: r30 = DoubleToIntegerStub
    //     0xe66f7c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xe66f80: LoadField: r30 = r30->field_7
    //     0xe66f80: ldur            lr, [lr, #7]
    // 0xe66f84: blr             lr
    // 0xe66f88: RestoreReg r1
    //     0xe66f88: ldr             x1, [SP], #8
    // 0xe66f8c: RestoreReg d0
    //     0xe66f8c: ldr             q0, [SP], #0x10
    // 0xe66f90: b               #0xe66de4
  }
  [closure] int <anonymous closure>(dynamic, double) {
    // ** addr: 0xe66f94, size: 0x64
    // 0xe66f94: EnterFrame
    //     0xe66f94: stp             fp, lr, [SP, #-0x10]!
    //     0xe66f98: mov             fp, SP
    // 0xe66f9c: d0 = 1000.000000
    //     0xe66f9c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0xe66fa0: ldr             d0, [x17, #0x238]
    // 0xe66fa4: ldr             x1, [fp, #0x10]
    // 0xe66fa8: LoadField: d1 = r1->field_7
    //     0xe66fa8: ldur            d1, [x1, #7]
    // 0xe66fac: fmul            d2, d1, d0
    // 0xe66fb0: fcmp            d2, d2
    // 0xe66fb4: b.vs            #0xe66fd8
    // 0xe66fb8: fcvtzs          x0, d2
    // 0xe66fbc: asr             x16, x0, #0x1e
    // 0xe66fc0: cmp             x16, x0, asr #63
    // 0xe66fc4: b.ne            #0xe66fd8
    // 0xe66fc8: lsl             x0, x0, #1
    // 0xe66fcc: LeaveFrame
    //     0xe66fcc: mov             SP, fp
    //     0xe66fd0: ldp             fp, lr, [SP], #0x10
    // 0xe66fd4: ret
    //     0xe66fd4: ret             
    // 0xe66fd8: SaveReg d2
    //     0xe66fd8: str             q2, [SP, #-0x10]!
    // 0xe66fdc: d0 = 0.000000
    //     0xe66fdc: fmov            d0, d2
    // 0xe66fe0: r0 = 74
    //     0xe66fe0: movz            x0, #0x4a
    // 0xe66fe4: r30 = DoubleToIntegerStub
    //     0xe66fe4: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xe66fe8: LoadField: r30 = r30->field_7
    //     0xe66fe8: ldur            lr, [lr, #7]
    // 0xe66fec: blr             lr
    // 0xe66ff0: RestoreReg d2
    //     0xe66ff0: ldr             q2, [SP], #0x10
    // 0xe66ff4: b               #0xe66fcc
  }
  [closure] PdfFontMetrics glyphMetrics(dynamic, int) {
    // ** addr: 0xe71018, size: 0x3c
    // 0xe71018: EnterFrame
    //     0xe71018: stp             fp, lr, [SP, #-0x10]!
    //     0xe7101c: mov             fp, SP
    // 0xe71020: ldr             x0, [fp, #0x18]
    // 0xe71024: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe71024: ldur            w1, [x0, #0x17]
    // 0xe71028: DecompressPointer r1
    //     0xe71028: add             x1, x1, HEAP, lsl #32
    // 0xe7102c: CheckStackOverflow
    //     0xe7102c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe71030: cmp             SP, x16
    //     0xe71034: b.ls            #0xe7104c
    // 0xe71038: ldr             x2, [fp, #0x10]
    // 0xe7103c: r0 = glyphMetrics()
    //     0xe7103c: bl              #0xe71054  ; [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::glyphMetrics
    // 0xe71040: LeaveFrame
    //     0xe71040: mov             SP, fp
    //     0xe71044: ldp             fp, lr, [SP], #0x10
    // 0xe71048: ret
    //     0xe71048: ret             
    // 0xe7104c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7104c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe71050: b               #0xe71038
  }
  _ glyphMetrics(/* No info */) {
    // ** addr: 0xe71054, size: 0x1a0
    // 0xe71054: EnterFrame
    //     0xe71054: stp             fp, lr, [SP, #-0x10]!
    //     0xe71058: mov             fp, SP
    // 0xe7105c: AllocStack(0x38)
    //     0xe7105c: sub             SP, SP, #0x38
    // 0xe71060: SetupParameters(PdfType1Font this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe71060: mov             x3, x1
    //     0xe71064: mov             x0, x2
    //     0xe71068: stur            x1, [fp, #-8]
    //     0xe7106c: stur            x2, [fp, #-0x10]
    // 0xe71070: CheckStackOverflow
    //     0xe71070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe71074: cmp             SP, x16
    //     0xe71078: b.ls            #0xe711e8
    // 0xe7107c: r2 = LoadInt32Instr(r0)
    //     0xe7107c: sbfx            x2, x0, #1, #0x1f
    //     0xe71080: tbz             w0, #0, #0xe71088
    //     0xe71084: ldur            x2, [x0, #7]
    // 0xe71088: tbnz            x2, #0x3f, #0xe7112c
    // 0xe7108c: cmp             x2, #0xff
    // 0xe71090: b.gt            #0xe7112c
    // 0xe71094: LoadField: d0 = r3->field_3b
    //     0xe71094: ldur            d0, [x3, #0x3b]
    // 0xe71098: stur            d0, [fp, #-0x30]
    // 0xe7109c: LoadField: r4 = r3->field_43
    //     0xe7109c: ldur            w4, [x3, #0x43]
    // 0xe710a0: DecompressPointer r4
    //     0xe710a0: add             x4, x4, HEAP, lsl #32
    // 0xe710a4: LoadField: r0 = r4->field_b
    //     0xe710a4: ldur            w0, [x4, #0xb]
    // 0xe710a8: r1 = LoadInt32Instr(r0)
    //     0xe710a8: sbfx            x1, x0, #1, #0x1f
    // 0xe710ac: cmp             x2, x1
    // 0xe710b0: b.ge            #0xe710d8
    // 0xe710b4: mov             x0, x1
    // 0xe710b8: mov             x1, x2
    // 0xe710bc: cmp             x1, x0
    // 0xe710c0: b.hs            #0xe711f0
    // 0xe710c4: ArrayLoad: r0 = r4[r2]  ; Unknown_4
    //     0xe710c4: add             x16, x4, x2, lsl #2
    //     0xe710c8: ldur            w0, [x16, #0xf]
    // 0xe710cc: DecompressPointer r0
    //     0xe710cc: add             x0, x0, HEAP, lsl #32
    // 0xe710d0: LoadField: d1 = r0->field_7
    //     0xe710d0: ldur            d1, [x0, #7]
    // 0xe710d4: b               #0xe710dc
    // 0xe710d8: d1 = 0.600000
    //     0xe710d8: ldr             d1, [PP, #0x5480]  ; [pp+0x5480] IMM: double(0.6) from 0x3fe3333333333333
    // 0xe710dc: stur            d1, [fp, #-0x28]
    // 0xe710e0: LoadField: d2 = r3->field_33
    //     0xe710e0: ldur            d2, [x3, #0x33]
    // 0xe710e4: stur            d2, [fp, #-0x20]
    // 0xe710e8: r0 = PdfFontMetrics()
    //     0xe710e8: bl              #0x7b71f8  ; AllocatePdfFontMetricsStub -> PdfFontMetrics (size=0x48)
    // 0xe710ec: StoreField: r0->field_7 = rZR
    //     0xe710ec: stur            xzr, [x0, #7]
    // 0xe710f0: ldur            d0, [fp, #-0x30]
    // 0xe710f4: StoreField: r0->field_f = d0
    //     0xe710f4: stur            d0, [x0, #0xf]
    // 0xe710f8: ldur            d1, [fp, #-0x28]
    // 0xe710fc: StoreField: r0->field_1f = d1
    //     0xe710fc: stur            d1, [x0, #0x1f]
    // 0xe71100: ldur            d2, [fp, #-0x20]
    // 0xe71104: ArrayStore: r0[0] = d2  ; List_8
    //     0xe71104: stur            d2, [x0, #0x17]
    // 0xe71108: StoreField: r0->field_27 = d2
    //     0xe71108: stur            d2, [x0, #0x27]
    // 0xe7110c: StoreField: r0->field_2f = d0
    //     0xe7110c: stur            d0, [x0, #0x2f]
    // 0xe71110: d0 = 0.000000
    //     0xe71110: eor             v0.16b, v0.16b, v0.16b
    // 0xe71114: fsub            d2, d1, d0
    // 0xe71118: StoreField: r0->field_37 = d2
    //     0xe71118: stur            d2, [x0, #0x37]
    // 0xe7111c: StoreField: r0->field_3f = rZR
    //     0xe7111c: stur            xzr, [x0, #0x3f]
    // 0xe71120: LeaveFrame
    //     0xe71120: mov             SP, fp
    //     0xe71124: ldp             fp, lr, [SP], #0x10
    // 0xe71128: ret
    //     0xe71128: ret             
    // 0xe7112c: r1 = Null
    //     0xe7112c: mov             x1, NULL
    // 0xe71130: r2 = 8
    //     0xe71130: movz            x2, #0x8
    // 0xe71134: r0 = AllocateArray()
    //     0xe71134: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe71138: stur            x0, [fp, #-0x18]
    // 0xe7113c: r16 = "Unable to display U+"
    //     0xe7113c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e188] "Unable to display U+"
    //     0xe71140: ldr             x16, [x16, #0x188]
    // 0xe71144: StoreField: r0->field_f = r16
    //     0xe71144: stur            w16, [x0, #0xf]
    // 0xe71148: ldur            x1, [fp, #-0x10]
    // 0xe7114c: r0 = _toPow2String()
    //     0xe7114c: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0xe71150: ldur            x1, [fp, #-0x18]
    // 0xe71154: ArrayStore: r1[1] = r0  ; List_4
    //     0xe71154: add             x25, x1, #0x13
    //     0xe71158: str             w0, [x25]
    //     0xe7115c: tbz             w0, #0, #0xe71178
    //     0xe71160: ldurb           w16, [x1, #-1]
    //     0xe71164: ldurb           w17, [x0, #-1]
    //     0xe71168: and             x16, x17, x16, lsr #2
    //     0xe7116c: tst             x16, HEAP, lsr #32
    //     0xe71170: b.eq            #0xe71178
    //     0xe71174: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71178: ldur            x2, [fp, #-0x18]
    // 0xe7117c: r16 = " with "
    //     0xe7117c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e190] " with "
    //     0xe71180: ldr             x16, [x16, #0x190]
    // 0xe71184: ArrayStore: r2[0] = r16  ; List_4
    //     0xe71184: stur            w16, [x2, #0x17]
    // 0xe71188: ldur            x0, [fp, #-8]
    // 0xe7118c: LoadField: r1 = r0->field_2f
    //     0xe7118c: ldur            w1, [x0, #0x2f]
    // 0xe71190: DecompressPointer r1
    //     0xe71190: add             x1, x1, HEAP, lsl #32
    // 0xe71194: mov             x0, x1
    // 0xe71198: mov             x1, x2
    // 0xe7119c: ArrayStore: r1[3] = r0  ; List_4
    //     0xe7119c: add             x25, x1, #0x1b
    //     0xe711a0: str             w0, [x25]
    //     0xe711a4: tbz             w0, #0, #0xe711c0
    //     0xe711a8: ldurb           w16, [x1, #-1]
    //     0xe711ac: ldurb           w17, [x0, #-1]
    //     0xe711b0: and             x16, x17, x16, lsr #2
    //     0xe711b4: tst             x16, HEAP, lsr #32
    //     0xe711b8: b.eq            #0xe711c0
    //     0xe711bc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe711c0: str             x2, [SP]
    // 0xe711c4: r0 = _interpolate()
    //     0xe711c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe711c8: stur            x0, [fp, #-8]
    // 0xe711cc: r0 = _Exception()
    //     0xe711cc: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xe711d0: mov             x1, x0
    // 0xe711d4: ldur            x0, [fp, #-8]
    // 0xe711d8: StoreField: r1->field_7 = r0
    //     0xe711d8: stur            w0, [x1, #7]
    // 0xe711dc: mov             x0, x1
    // 0xe711e0: r0 = Throw()
    //     0xe711e0: bl              #0xec04b8  ; ThrowStub
    // 0xe711e4: brk             #0
    // 0xe711e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe711e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe711ec: b               #0xe7107c
    // 0xe711f0: r0 = RangeErrorSharedWithFPURegs()
    //     0xe711f0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
}
