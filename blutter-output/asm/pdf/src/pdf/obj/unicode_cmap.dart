// lib: , url: package:pdf/src/pdf/obj/unicode_cmap.dart

// class id: 1050821, size: 0x8
class :: {
}

// class id: 885, size: 0x3c, field offset: 0x34
class PdfUnicodeCmap extends PdfObjectStream {

  _ prepare(/* No info */) {
    // ** addr: 0x7cb6b0, size: 0x224
    // 0x7cb6b0: EnterFrame
    //     0x7cb6b0: stp             fp, lr, [SP, #-0x10]!
    //     0x7cb6b4: mov             fp, SP
    // 0x7cb6b8: AllocStack(0x38)
    //     0x7cb6b8: sub             SP, SP, #0x38
    // 0x7cb6bc: SetupParameters(PdfUnicodeCmap this /* r1 => r0, fp-0x10 */)
    //     0x7cb6bc: mov             x0, x1
    //     0x7cb6c0: stur            x1, [fp, #-0x10]
    // 0x7cb6c4: CheckStackOverflow
    //     0x7cb6c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb6c8: cmp             SP, x16
    //     0x7cb6cc: b.ls            #0x7cb8c4
    // 0x7cb6d0: LoadField: r3 = r0->field_2b
    //     0x7cb6d0: ldur            w3, [x0, #0x2b]
    // 0x7cb6d4: DecompressPointer r3
    //     0x7cb6d4: add             x3, x3, HEAP, lsl #32
    // 0x7cb6d8: stur            x3, [fp, #-8]
    // 0x7cb6dc: r1 = Null
    //     0x7cb6dc: mov             x1, NULL
    // 0x7cb6e0: r2 = 6
    //     0x7cb6e0: movz            x2, #0x6
    // 0x7cb6e4: r0 = AllocateArray()
    //     0x7cb6e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7cb6e8: r16 = "/CIDInit/ProcSet\nfindresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo<<\n/Registry (Adobe)\n/Ordering (UCS)\n/Supplement 0\n>> def\n/CMapName/Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000> <FFFF>\nendcodespacerange\n"
    //     0x7cb6e8: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f68] "/CIDInit/ProcSet\nfindresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo<<\n/Registry (Adobe)\n/Ordering (UCS)\n/Supplement 0\n>> def\n/CMapName/Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000> <FFFF>\nendcodespacerange\n"
    //     0x7cb6ec: ldr             x16, [x16, #0xf68]
    // 0x7cb6f0: StoreField: r0->field_f = r16
    //     0x7cb6f0: stur            w16, [x0, #0xf]
    // 0x7cb6f4: ldur            x1, [fp, #-0x10]
    // 0x7cb6f8: LoadField: r2 = r1->field_33
    //     0x7cb6f8: ldur            w2, [x1, #0x33]
    // 0x7cb6fc: DecompressPointer r2
    //     0x7cb6fc: add             x2, x2, HEAP, lsl #32
    // 0x7cb700: stur            x2, [fp, #-0x18]
    // 0x7cb704: LoadField: r1 = r2->field_b
    //     0x7cb704: ldur            w1, [x2, #0xb]
    // 0x7cb708: StoreField: r0->field_13 = r1
    //     0x7cb708: stur            w1, [x0, #0x13]
    // 0x7cb70c: r16 = " beginbfchar\n"
    //     0x7cb70c: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f70] " beginbfchar\n"
    //     0x7cb710: ldr             x16, [x16, #0xf70]
    // 0x7cb714: ArrayStore: r0[0] = r16  ; List_4
    //     0x7cb714: stur            w16, [x0, #0x17]
    // 0x7cb718: str             x0, [SP]
    // 0x7cb71c: r0 = _interpolate()
    //     0x7cb71c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7cb720: ldur            x1, [fp, #-8]
    // 0x7cb724: mov             x2, x0
    // 0x7cb728: r0 = putString()
    //     0x7cb728: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0x7cb72c: r3 = 0
    //     0x7cb72c: movz            x3, #0
    // 0x7cb730: ldur            x0, [fp, #-0x18]
    // 0x7cb734: stur            x3, [fp, #-0x28]
    // 0x7cb738: CheckStackOverflow
    //     0x7cb738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cb73c: cmp             SP, x16
    //     0x7cb740: b.ls            #0x7cb8cc
    // 0x7cb744: LoadField: r1 = r0->field_b
    //     0x7cb744: ldur            w1, [x0, #0xb]
    // 0x7cb748: r2 = LoadInt32Instr(r1)
    //     0x7cb748: sbfx            x2, x1, #1, #0x1f
    // 0x7cb74c: cmp             x3, x2
    // 0x7cb750: b.ge            #0x7cb8a4
    // 0x7cb754: LoadField: r1 = r0->field_f
    //     0x7cb754: ldur            w1, [x0, #0xf]
    // 0x7cb758: DecompressPointer r1
    //     0x7cb758: add             x1, x1, HEAP, lsl #32
    // 0x7cb75c: lsl             x4, x3, #1
    // 0x7cb760: stur            x4, [fp, #-0x20]
    // 0x7cb764: ArrayLoad: r5 = r1[r3]  ; Unknown_4
    //     0x7cb764: add             x16, x1, x3, lsl #2
    //     0x7cb768: ldur            w5, [x16, #0xf]
    // 0x7cb76c: DecompressPointer r5
    //     0x7cb76c: add             x5, x5, HEAP, lsl #32
    // 0x7cb770: stur            x5, [fp, #-0x10]
    // 0x7cb774: r1 = Null
    //     0x7cb774: mov             x1, NULL
    // 0x7cb778: r2 = 10
    //     0x7cb778: movz            x2, #0xa
    // 0x7cb77c: r0 = AllocateArray()
    //     0x7cb77c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7cb780: stur            x0, [fp, #-0x30]
    // 0x7cb784: r16 = "<"
    //     0x7cb784: ldr             x16, [PP, #0x510]  ; [pp+0x510] "<"
    // 0x7cb788: StoreField: r0->field_f = r16
    //     0x7cb788: stur            w16, [x0, #0xf]
    // 0x7cb78c: ldur            x1, [fp, #-0x20]
    // 0x7cb790: r0 = _toPow2String()
    //     0x7cb790: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0x7cb794: str             x0, [SP]
    // 0x7cb798: r0 = toUpperCase()
    //     0x7cb798: bl              #0xebe0d0  ; [dart:core] _OneByteString::toUpperCase
    // 0x7cb79c: r1 = LoadClassIdInstr(r0)
    //     0x7cb79c: ldur            x1, [x0, #-1]
    //     0x7cb7a0: ubfx            x1, x1, #0xc, #0x14
    // 0x7cb7a4: mov             x16, x0
    // 0x7cb7a8: mov             x0, x1
    // 0x7cb7ac: mov             x1, x16
    // 0x7cb7b0: r2 = 4
    //     0x7cb7b0: movz            x2, #0x4
    // 0x7cb7b4: r3 = "0"
    //     0x7cb7b4: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x7cb7b8: r0 = GDT[cid_x0 + -0xff8]()
    //     0x7cb7b8: sub             lr, x0, #0xff8
    //     0x7cb7bc: ldr             lr, [x21, lr, lsl #3]
    //     0x7cb7c0: blr             lr
    // 0x7cb7c4: ldur            x1, [fp, #-0x30]
    // 0x7cb7c8: ArrayStore: r1[1] = r0  ; List_4
    //     0x7cb7c8: add             x25, x1, #0x13
    //     0x7cb7cc: str             w0, [x25]
    //     0x7cb7d0: tbz             w0, #0, #0x7cb7ec
    //     0x7cb7d4: ldurb           w16, [x1, #-1]
    //     0x7cb7d8: ldurb           w17, [x0, #-1]
    //     0x7cb7dc: and             x16, x17, x16, lsr #2
    //     0x7cb7e0: tst             x16, HEAP, lsr #32
    //     0x7cb7e4: b.eq            #0x7cb7ec
    //     0x7cb7e8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7cb7ec: ldur            x0, [fp, #-0x30]
    // 0x7cb7f0: r16 = "> <"
    //     0x7cb7f0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33788] "> <"
    //     0x7cb7f4: ldr             x16, [x16, #0x788]
    // 0x7cb7f8: ArrayStore: r0[0] = r16  ; List_4
    //     0x7cb7f8: stur            w16, [x0, #0x17]
    // 0x7cb7fc: ldur            x1, [fp, #-0x10]
    // 0x7cb800: r0 = _toPow2String()
    //     0x7cb800: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0x7cb804: str             x0, [SP]
    // 0x7cb808: r0 = toUpperCase()
    //     0x7cb808: bl              #0xebe0d0  ; [dart:core] _OneByteString::toUpperCase
    // 0x7cb80c: r1 = LoadClassIdInstr(r0)
    //     0x7cb80c: ldur            x1, [x0, #-1]
    //     0x7cb810: ubfx            x1, x1, #0xc, #0x14
    // 0x7cb814: mov             x16, x0
    // 0x7cb818: mov             x0, x1
    // 0x7cb81c: mov             x1, x16
    // 0x7cb820: r2 = 4
    //     0x7cb820: movz            x2, #0x4
    // 0x7cb824: r3 = "0"
    //     0x7cb824: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x7cb828: r0 = GDT[cid_x0 + -0xff8]()
    //     0x7cb828: sub             lr, x0, #0xff8
    //     0x7cb82c: ldr             lr, [x21, lr, lsl #3]
    //     0x7cb830: blr             lr
    // 0x7cb834: ldur            x1, [fp, #-0x30]
    // 0x7cb838: ArrayStore: r1[3] = r0  ; List_4
    //     0x7cb838: add             x25, x1, #0x1b
    //     0x7cb83c: str             w0, [x25]
    //     0x7cb840: tbz             w0, #0, #0x7cb85c
    //     0x7cb844: ldurb           w16, [x1, #-1]
    //     0x7cb848: ldurb           w17, [x0, #-1]
    //     0x7cb84c: and             x16, x17, x16, lsr #2
    //     0x7cb850: tst             x16, HEAP, lsr #32
    //     0x7cb854: b.eq            #0x7cb85c
    //     0x7cb858: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7cb85c: ldur            x0, [fp, #-0x30]
    // 0x7cb860: r16 = ">\n"
    //     0x7cb860: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f78] ">\n"
    //     0x7cb864: ldr             x16, [x16, #0xf78]
    // 0x7cb868: StoreField: r0->field_1f = r16
    //     0x7cb868: stur            w16, [x0, #0x1f]
    // 0x7cb86c: str             x0, [SP]
    // 0x7cb870: r0 = _interpolate()
    //     0x7cb870: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7cb874: r1 = <int>
    //     0x7cb874: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7cb878: stur            x0, [fp, #-0x10]
    // 0x7cb87c: r0 = CodeUnits()
    //     0x7cb87c: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0x7cb880: mov             x1, x0
    // 0x7cb884: ldur            x0, [fp, #-0x10]
    // 0x7cb888: StoreField: r1->field_b = r0
    //     0x7cb888: stur            w0, [x1, #0xb]
    // 0x7cb88c: mov             x2, x1
    // 0x7cb890: ldur            x1, [fp, #-8]
    // 0x7cb894: r0 = putBytes()
    //     0x7cb894: bl              #0x7b7d70  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putBytes
    // 0x7cb898: ldur            x0, [fp, #-0x28]
    // 0x7cb89c: add             x3, x0, #1
    // 0x7cb8a0: b               #0x7cb730
    // 0x7cb8a4: ldur            x1, [fp, #-8]
    // 0x7cb8a8: r2 = "endbfchar\nendcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"
    //     0x7cb8a8: add             x2, PP, #0x46, lsl #12  ; [pp+0x46f80] "endbfchar\nendcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"
    //     0x7cb8ac: ldr             x2, [x2, #0xf80]
    // 0x7cb8b0: r0 = putString()
    //     0x7cb8b0: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0x7cb8b4: r0 = Null
    //     0x7cb8b4: mov             x0, NULL
    // 0x7cb8b8: LeaveFrame
    //     0x7cb8b8: mov             SP, fp
    //     0x7cb8bc: ldp             fp, lr, [SP], #0x10
    // 0x7cb8c0: ret
    //     0x7cb8c0: ret             
    // 0x7cb8c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb8c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb8c8: b               #0x7cb6d0
    // 0x7cb8cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cb8cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cb8d0: b               #0x7cb744
  }
  _ PdfUnicodeCmap(/* No info */) {
    // ** addr: 0xe65f88, size: 0xac
    // 0xe65f88: EnterFrame
    //     0xe65f88: stp             fp, lr, [SP, #-0x10]!
    //     0xe65f8c: mov             fp, SP
    // 0xe65f90: AllocStack(0x18)
    //     0xe65f90: sub             SP, SP, #0x18
    // 0xe65f94: r0 = 2
    //     0xe65f94: movz            x0, #0x2
    // 0xe65f98: mov             x4, x1
    // 0xe65f9c: mov             x3, x2
    // 0xe65fa0: stur            x1, [fp, #-8]
    // 0xe65fa4: stur            x2, [fp, #-0x10]
    // 0xe65fa8: CheckStackOverflow
    //     0xe65fa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe65fac: cmp             SP, x16
    //     0xe65fb0: b.ls            #0xe6602c
    // 0xe65fb4: mov             x2, x0
    // 0xe65fb8: r1 = Null
    //     0xe65fb8: mov             x1, NULL
    // 0xe65fbc: r0 = AllocateArray()
    //     0xe65fbc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe65fc0: stur            x0, [fp, #-0x18]
    // 0xe65fc4: StoreField: r0->field_f = rZR
    //     0xe65fc4: stur            wzr, [x0, #0xf]
    // 0xe65fc8: r1 = <int>
    //     0xe65fc8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe65fcc: r0 = AllocateGrowableArray()
    //     0xe65fcc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe65fd0: mov             x1, x0
    // 0xe65fd4: ldur            x0, [fp, #-0x18]
    // 0xe65fd8: StoreField: r1->field_f = r0
    //     0xe65fd8: stur            w0, [x1, #0xf]
    // 0xe65fdc: r0 = 2
    //     0xe65fdc: movz            x0, #0x2
    // 0xe65fe0: StoreField: r1->field_b = r0
    //     0xe65fe0: stur            w0, [x1, #0xb]
    // 0xe65fe4: mov             x0, x1
    // 0xe65fe8: ldur            x1, [fp, #-8]
    // 0xe65fec: StoreField: r1->field_33 = r0
    //     0xe65fec: stur            w0, [x1, #0x33]
    //     0xe65ff0: ldurb           w16, [x1, #-1]
    //     0xe65ff4: ldurb           w17, [x0, #-1]
    //     0xe65ff8: and             x16, x17, x16, lsr #2
    //     0xe65ffc: tst             x16, HEAP, lsr #32
    //     0xe66000: b.eq            #0xe66008
    //     0xe66004: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe66008: r0 = false
    //     0xe66008: add             x0, NULL, #0x30  ; false
    // 0xe6600c: StoreField: r1->field_37 = r0
    //     0xe6600c: stur            w0, [x1, #0x37]
    // 0xe66010: ldur            x2, [fp, #-0x10]
    // 0xe66014: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe66014: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe66018: r0 = PdfObjectStream()
    //     0xe66018: bl              #0xe4714c  ; [package:pdf/src/pdf/obj/object_stream.dart] PdfObjectStream::PdfObjectStream
    // 0xe6601c: r0 = Null
    //     0xe6601c: mov             x0, NULL
    // 0xe66020: LeaveFrame
    //     0xe66020: mov             SP, fp
    //     0xe66024: ldp             fp, lr, [SP], #0x10
    // 0xe66028: ret
    //     0xe66028: ret             
    // 0xe6602c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6602c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe66030: b               #0xe65fb4
  }
}
