// lib: , url: package:pdf/src/pdf/obj/xobject.dart

// class id: 1050822, size: 0x8
class :: {
}

// class id: 886, size: 0x34, field offset: 0x34
abstract class PdfXObject extends PdfObjectStream {

  _ PdfXObject(/* No info */) {
    // ** addr: 0xe47078, size: 0xd4
    // 0xe47078: EnterFrame
    //     0xe47078: stp             fp, lr, [SP, #-0x10]!
    //     0xe4707c: mov             fp, SP
    // 0xe47080: AllocStack(0x28)
    //     0xe47080: sub             SP, SP, #0x28
    // 0xe47084: SetupParameters(PdfXObject this /* r1 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xe47084: mov             x0, x1
    //     0xe47088: stur            x1, [fp, #-8]
    //     0xe4708c: stur            x3, [fp, #-0x10]
    // 0xe47090: CheckStackOverflow
    //     0xe47090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe47094: cmp             SP, x16
    //     0xe47098: b.ls            #0xe47144
    // 0xe4709c: r16 = "/XObject"
    //     0xe4709c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea58] "/XObject"
    //     0xe470a0: ldr             x16, [x16, #0xa58]
    // 0xe470a4: stp             x5, x16, [SP]
    // 0xe470a8: mov             x1, x0
    // 0xe470ac: r4 = const [0, 0x4, 0x2, 0x2, isBinary, 0x3, type, 0x2, null]
    //     0xe470ac: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ea60] List(9) [0, 0x4, 0x2, 0x2, "isBinary", 0x3, "type", 0x2, Null]
    //     0xe470b0: ldr             x4, [x4, #0xa60]
    // 0xe470b4: r0 = PdfObjectStream()
    //     0xe470b4: bl              #0xe4714c  ; [package:pdf/src/pdf/obj/object_stream.dart] PdfObjectStream::PdfObjectStream
    // 0xe470b8: ldur            x0, [fp, #-8]
    // 0xe470bc: LoadField: r1 = r0->field_1b
    //     0xe470bc: ldur            w1, [x0, #0x1b]
    // 0xe470c0: DecompressPointer r1
    //     0xe470c0: add             x1, x1, HEAP, lsl #32
    // 0xe470c4: stur            x1, [fp, #-0x18]
    // 0xe470c8: r0 = PdfName()
    //     0xe470c8: bl              #0x7b5d34  ; AllocatePdfNameStub -> PdfName (size=0xc)
    // 0xe470cc: mov             x3, x0
    // 0xe470d0: ldur            x0, [fp, #-0x10]
    // 0xe470d4: stur            x3, [fp, #-8]
    // 0xe470d8: StoreField: r3->field_7 = r0
    //     0xe470d8: stur            w0, [x3, #7]
    // 0xe470dc: ldur            x4, [fp, #-0x18]
    // 0xe470e0: LoadField: r2 = r4->field_7
    //     0xe470e0: ldur            w2, [x4, #7]
    // 0xe470e4: DecompressPointer r2
    //     0xe470e4: add             x2, x2, HEAP, lsl #32
    // 0xe470e8: mov             x0, x3
    // 0xe470ec: r1 = Null
    //     0xe470ec: mov             x1, NULL
    // 0xe470f0: cmp             w2, NULL
    // 0xe470f4: b.eq            #0xe47118
    // 0xe470f8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe470f8: ldur            w4, [x2, #0x17]
    // 0xe470fc: DecompressPointer r4
    //     0xe470fc: add             x4, x4, HEAP, lsl #32
    // 0xe47100: r8 = X0 bound PdfDataType
    //     0xe47100: add             x8, PP, #0x36, lsl #12  ; [pp+0x366d8] TypeParameter: X0 bound PdfDataType
    //     0xe47104: ldr             x8, [x8, #0x6d8]
    // 0xe47108: LoadField: r9 = r4->field_7
    //     0xe47108: ldur            x9, [x4, #7]
    // 0xe4710c: r3 = Null
    //     0xe4710c: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ea68] Null
    //     0xe47110: ldr             x3, [x3, #0xa68]
    // 0xe47114: blr             x9
    // 0xe47118: ldur            x0, [fp, #-0x18]
    // 0xe4711c: LoadField: r1 = r0->field_b
    //     0xe4711c: ldur            w1, [x0, #0xb]
    // 0xe47120: DecompressPointer r1
    //     0xe47120: add             x1, x1, HEAP, lsl #32
    // 0xe47124: ldur            x3, [fp, #-8]
    // 0xe47128: r2 = "/Subtype"
    //     0xe47128: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ea78] "/Subtype"
    //     0xe4712c: ldr             x2, [x2, #0xa78]
    // 0xe47130: r0 = []=()
    //     0xe47130: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe47134: r0 = Null
    //     0xe47134: mov             x0, NULL
    // 0xe47138: LeaveFrame
    //     0xe47138: mov             SP, fp
    //     0xe4713c: ldp             fp, lr, [SP], #0x10
    // 0xe47140: ret
    //     0xe47140: ret             
    // 0xe47144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47144: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47148: b               #0xe4709c
  }
}
