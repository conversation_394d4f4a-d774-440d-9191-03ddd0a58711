// lib: , url: package:pdf/src/pdf/page_format.dart

// class id: 1050823, size: 0x8
class :: {
}

// class id: 859, size: 0x38, field offset: 0x8
//   const constructor, 
class PdfPageFormat extends Object {

  _Double field_8;
  _Double field_10;
  _Double field_18;
  _Double field_20;
  _Double field_28;
  _Double field_30;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf1b74, size: 0x58
    // 0xbf1b74: EnterFrame
    //     0xbf1b74: stp             fp, lr, [SP, #-0x10]!
    //     0xbf1b78: mov             fp, SP
    // 0xbf1b7c: AllocStack(0x8)
    //     0xbf1b7c: sub             SP, SP, #8
    // 0xbf1b80: CheckStackOverflow
    //     0xbf1b80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf1b84: cmp             SP, x16
    //     0xbf1b88: b.ls            #0xbf1bc4
    // 0xbf1b8c: ldr             x16, [fp, #0x10]
    // 0xbf1b90: str             x16, [SP]
    // 0xbf1b94: r0 = toString()
    //     0xbf1b94: bl              #0xc35828  ; [package:pdf/src/pdf/page_format.dart] PdfPageFormat::toString
    // 0xbf1b98: r1 = LoadClassIdInstr(r0)
    //     0xbf1b98: ldur            x1, [x0, #-1]
    //     0xbf1b9c: ubfx            x1, x1, #0xc, #0x14
    // 0xbf1ba0: str             x0, [SP]
    // 0xbf1ba4: mov             x0, x1
    // 0xbf1ba8: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1ba8: movz            x17, #0x64af
    //     0xbf1bac: add             lr, x0, x17
    //     0xbf1bb0: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1bb4: blr             lr
    // 0xbf1bb8: LeaveFrame
    //     0xbf1bb8: mov             SP, fp
    //     0xbf1bbc: ldp             fp, lr, [SP], #0x10
    // 0xbf1bc0: ret
    //     0xbf1bc0: ret             
    // 0xbf1bc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1bc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1bc8: b               #0xbf1b8c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc35828, size: 0x310
    // 0xc35828: EnterFrame
    //     0xc35828: stp             fp, lr, [SP, #-0x10]!
    //     0xc3582c: mov             fp, SP
    // 0xc35830: AllocStack(0x8)
    //     0xc35830: sub             SP, SP, #8
    // 0xc35834: CheckStackOverflow
    //     0xc35834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35838: cmp             SP, x16
    //     0xc3583c: b.ls            #0xc35aa0
    // 0xc35840: r1 = Null
    //     0xc35840: mov             x1, NULL
    // 0xc35844: r2 = 26
    //     0xc35844: movz            x2, #0x1a
    // 0xc35848: r0 = AllocateArray()
    //     0xc35848: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3584c: mov             x2, x0
    // 0xc35850: r16 = PdfPageFormat
    //     0xc35850: add             x16, PP, #0x33, lsl #12  ; [pp+0x33910] Type: PdfPageFormat
    //     0xc35854: ldr             x16, [x16, #0x910]
    // 0xc35858: StoreField: r2->field_f = r16
    //     0xc35858: stur            w16, [x2, #0xf]
    // 0xc3585c: r16 = " "
    //     0xc3585c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc35860: StoreField: r2->field_13 = r16
    //     0xc35860: stur            w16, [x2, #0x13]
    // 0xc35864: ldr             x3, [fp, #0x10]
    // 0xc35868: LoadField: d0 = r3->field_7
    //     0xc35868: ldur            d0, [x3, #7]
    // 0xc3586c: r0 = inline_Allocate_Double()
    //     0xc3586c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc35870: add             x0, x0, #0x10
    //     0xc35874: cmp             x1, x0
    //     0xc35878: b.ls            #0xc35aa8
    //     0xc3587c: str             x0, [THR, #0x50]  ; THR::top
    //     0xc35880: sub             x0, x0, #0xf
    //     0xc35884: movz            x1, #0xe15c
    //     0xc35888: movk            x1, #0x3, lsl #16
    //     0xc3588c: stur            x1, [x0, #-1]
    // 0xc35890: StoreField: r0->field_7 = d0
    //     0xc35890: stur            d0, [x0, #7]
    // 0xc35894: mov             x1, x2
    // 0xc35898: ArrayStore: r1[2] = r0  ; List_4
    //     0xc35898: add             x25, x1, #0x17
    //     0xc3589c: str             w0, [x25]
    //     0xc358a0: tbz             w0, #0, #0xc358bc
    //     0xc358a4: ldurb           w16, [x1, #-1]
    //     0xc358a8: ldurb           w17, [x0, #-1]
    //     0xc358ac: and             x16, x17, x16, lsr #2
    //     0xc358b0: tst             x16, HEAP, lsr #32
    //     0xc358b4: b.eq            #0xc358bc
    //     0xc358b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc358bc: r16 = "x"
    //     0xc358bc: ldr             x16, [PP, #0x71a0]  ; [pp+0x71a0] "x"
    // 0xc358c0: StoreField: r2->field_1b = r16
    //     0xc358c0: stur            w16, [x2, #0x1b]
    // 0xc358c4: LoadField: d0 = r3->field_f
    //     0xc358c4: ldur            d0, [x3, #0xf]
    // 0xc358c8: r0 = inline_Allocate_Double()
    //     0xc358c8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc358cc: add             x0, x0, #0x10
    //     0xc358d0: cmp             x1, x0
    //     0xc358d4: b.ls            #0xc35ac0
    //     0xc358d8: str             x0, [THR, #0x50]  ; THR::top
    //     0xc358dc: sub             x0, x0, #0xf
    //     0xc358e0: movz            x1, #0xe15c
    //     0xc358e4: movk            x1, #0x3, lsl #16
    //     0xc358e8: stur            x1, [x0, #-1]
    // 0xc358ec: StoreField: r0->field_7 = d0
    //     0xc358ec: stur            d0, [x0, #7]
    // 0xc358f0: mov             x1, x2
    // 0xc358f4: ArrayStore: r1[4] = r0  ; List_4
    //     0xc358f4: add             x25, x1, #0x1f
    //     0xc358f8: str             w0, [x25]
    //     0xc358fc: tbz             w0, #0, #0xc35918
    //     0xc35900: ldurb           w16, [x1, #-1]
    //     0xc35904: ldurb           w17, [x0, #-1]
    //     0xc35908: and             x16, x17, x16, lsr #2
    //     0xc3590c: tst             x16, HEAP, lsr #32
    //     0xc35910: b.eq            #0xc35918
    //     0xc35914: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35918: r16 = " margins:"
    //     0xc35918: add             x16, PP, #0x33, lsl #12  ; [pp+0x33918] " margins:"
    //     0xc3591c: ldr             x16, [x16, #0x918]
    // 0xc35920: StoreField: r2->field_23 = r16
    //     0xc35920: stur            w16, [x2, #0x23]
    // 0xc35924: LoadField: d0 = r3->field_27
    //     0xc35924: ldur            d0, [x3, #0x27]
    // 0xc35928: r0 = inline_Allocate_Double()
    //     0xc35928: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc3592c: add             x0, x0, #0x10
    //     0xc35930: cmp             x1, x0
    //     0xc35934: b.ls            #0xc35ad8
    //     0xc35938: str             x0, [THR, #0x50]  ; THR::top
    //     0xc3593c: sub             x0, x0, #0xf
    //     0xc35940: movz            x1, #0xe15c
    //     0xc35944: movk            x1, #0x3, lsl #16
    //     0xc35948: stur            x1, [x0, #-1]
    // 0xc3594c: StoreField: r0->field_7 = d0
    //     0xc3594c: stur            d0, [x0, #7]
    // 0xc35950: mov             x1, x2
    // 0xc35954: ArrayStore: r1[6] = r0  ; List_4
    //     0xc35954: add             x25, x1, #0x27
    //     0xc35958: str             w0, [x25]
    //     0xc3595c: tbz             w0, #0, #0xc35978
    //     0xc35960: ldurb           w16, [x1, #-1]
    //     0xc35964: ldurb           w17, [x0, #-1]
    //     0xc35968: and             x16, x17, x16, lsr #2
    //     0xc3596c: tst             x16, HEAP, lsr #32
    //     0xc35970: b.eq            #0xc35978
    //     0xc35974: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35978: r16 = ", "
    //     0xc35978: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3597c: StoreField: r2->field_2b = r16
    //     0xc3597c: stur            w16, [x2, #0x2b]
    // 0xc35980: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc35980: ldur            d0, [x3, #0x17]
    // 0xc35984: r0 = inline_Allocate_Double()
    //     0xc35984: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc35988: add             x0, x0, #0x10
    //     0xc3598c: cmp             x1, x0
    //     0xc35990: b.ls            #0xc35af0
    //     0xc35994: str             x0, [THR, #0x50]  ; THR::top
    //     0xc35998: sub             x0, x0, #0xf
    //     0xc3599c: movz            x1, #0xe15c
    //     0xc359a0: movk            x1, #0x3, lsl #16
    //     0xc359a4: stur            x1, [x0, #-1]
    // 0xc359a8: StoreField: r0->field_7 = d0
    //     0xc359a8: stur            d0, [x0, #7]
    // 0xc359ac: mov             x1, x2
    // 0xc359b0: ArrayStore: r1[8] = r0  ; List_4
    //     0xc359b0: add             x25, x1, #0x2f
    //     0xc359b4: str             w0, [x25]
    //     0xc359b8: tbz             w0, #0, #0xc359d4
    //     0xc359bc: ldurb           w16, [x1, #-1]
    //     0xc359c0: ldurb           w17, [x0, #-1]
    //     0xc359c4: and             x16, x17, x16, lsr #2
    //     0xc359c8: tst             x16, HEAP, lsr #32
    //     0xc359cc: b.eq            #0xc359d4
    //     0xc359d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc359d4: r16 = ", "
    //     0xc359d4: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc359d8: StoreField: r2->field_33 = r16
    //     0xc359d8: stur            w16, [x2, #0x33]
    // 0xc359dc: LoadField: d0 = r3->field_2f
    //     0xc359dc: ldur            d0, [x3, #0x2f]
    // 0xc359e0: r0 = inline_Allocate_Double()
    //     0xc359e0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc359e4: add             x0, x0, #0x10
    //     0xc359e8: cmp             x1, x0
    //     0xc359ec: b.ls            #0xc35b08
    //     0xc359f0: str             x0, [THR, #0x50]  ; THR::top
    //     0xc359f4: sub             x0, x0, #0xf
    //     0xc359f8: movz            x1, #0xe15c
    //     0xc359fc: movk            x1, #0x3, lsl #16
    //     0xc35a00: stur            x1, [x0, #-1]
    // 0xc35a04: StoreField: r0->field_7 = d0
    //     0xc35a04: stur            d0, [x0, #7]
    // 0xc35a08: mov             x1, x2
    // 0xc35a0c: ArrayStore: r1[10] = r0  ; List_4
    //     0xc35a0c: add             x25, x1, #0x37
    //     0xc35a10: str             w0, [x25]
    //     0xc35a14: tbz             w0, #0, #0xc35a30
    //     0xc35a18: ldurb           w16, [x1, #-1]
    //     0xc35a1c: ldurb           w17, [x0, #-1]
    //     0xc35a20: and             x16, x17, x16, lsr #2
    //     0xc35a24: tst             x16, HEAP, lsr #32
    //     0xc35a28: b.eq            #0xc35a30
    //     0xc35a2c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35a30: r16 = ", "
    //     0xc35a30: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc35a34: StoreField: r2->field_3b = r16
    //     0xc35a34: stur            w16, [x2, #0x3b]
    // 0xc35a38: LoadField: d0 = r3->field_1f
    //     0xc35a38: ldur            d0, [x3, #0x1f]
    // 0xc35a3c: r0 = inline_Allocate_Double()
    //     0xc35a3c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc35a40: add             x0, x0, #0x10
    //     0xc35a44: cmp             x1, x0
    //     0xc35a48: b.ls            #0xc35b20
    //     0xc35a4c: str             x0, [THR, #0x50]  ; THR::top
    //     0xc35a50: sub             x0, x0, #0xf
    //     0xc35a54: movz            x1, #0xe15c
    //     0xc35a58: movk            x1, #0x3, lsl #16
    //     0xc35a5c: stur            x1, [x0, #-1]
    // 0xc35a60: StoreField: r0->field_7 = d0
    //     0xc35a60: stur            d0, [x0, #7]
    // 0xc35a64: mov             x1, x2
    // 0xc35a68: ArrayStore: r1[12] = r0  ; List_4
    //     0xc35a68: add             x25, x1, #0x3f
    //     0xc35a6c: str             w0, [x25]
    //     0xc35a70: tbz             w0, #0, #0xc35a8c
    //     0xc35a74: ldurb           w16, [x1, #-1]
    //     0xc35a78: ldurb           w17, [x0, #-1]
    //     0xc35a7c: and             x16, x17, x16, lsr #2
    //     0xc35a80: tst             x16, HEAP, lsr #32
    //     0xc35a84: b.eq            #0xc35a8c
    //     0xc35a88: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35a8c: str             x2, [SP]
    // 0xc35a90: r0 = _interpolate()
    //     0xc35a90: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc35a94: LeaveFrame
    //     0xc35a94: mov             SP, fp
    //     0xc35a98: ldp             fp, lr, [SP], #0x10
    // 0xc35a9c: ret
    //     0xc35a9c: ret             
    // 0xc35aa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc35aa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35aa4: b               #0xc35840
    // 0xc35aa8: SaveReg d0
    //     0xc35aa8: str             q0, [SP, #-0x10]!
    // 0xc35aac: stp             x2, x3, [SP, #-0x10]!
    // 0xc35ab0: r0 = AllocateDouble()
    //     0xc35ab0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35ab4: ldp             x2, x3, [SP], #0x10
    // 0xc35ab8: RestoreReg d0
    //     0xc35ab8: ldr             q0, [SP], #0x10
    // 0xc35abc: b               #0xc35890
    // 0xc35ac0: SaveReg d0
    //     0xc35ac0: str             q0, [SP, #-0x10]!
    // 0xc35ac4: stp             x2, x3, [SP, #-0x10]!
    // 0xc35ac8: r0 = AllocateDouble()
    //     0xc35ac8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35acc: ldp             x2, x3, [SP], #0x10
    // 0xc35ad0: RestoreReg d0
    //     0xc35ad0: ldr             q0, [SP], #0x10
    // 0xc35ad4: b               #0xc358ec
    // 0xc35ad8: SaveReg d0
    //     0xc35ad8: str             q0, [SP, #-0x10]!
    // 0xc35adc: stp             x2, x3, [SP, #-0x10]!
    // 0xc35ae0: r0 = AllocateDouble()
    //     0xc35ae0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35ae4: ldp             x2, x3, [SP], #0x10
    // 0xc35ae8: RestoreReg d0
    //     0xc35ae8: ldr             q0, [SP], #0x10
    // 0xc35aec: b               #0xc3594c
    // 0xc35af0: SaveReg d0
    //     0xc35af0: str             q0, [SP, #-0x10]!
    // 0xc35af4: stp             x2, x3, [SP, #-0x10]!
    // 0xc35af8: r0 = AllocateDouble()
    //     0xc35af8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35afc: ldp             x2, x3, [SP], #0x10
    // 0xc35b00: RestoreReg d0
    //     0xc35b00: ldr             q0, [SP], #0x10
    // 0xc35b04: b               #0xc359a8
    // 0xc35b08: SaveReg d0
    //     0xc35b08: str             q0, [SP, #-0x10]!
    // 0xc35b0c: stp             x2, x3, [SP, #-0x10]!
    // 0xc35b10: r0 = AllocateDouble()
    //     0xc35b10: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35b14: ldp             x2, x3, [SP], #0x10
    // 0xc35b18: RestoreReg d0
    //     0xc35b18: ldr             q0, [SP], #0x10
    // 0xc35b1c: b               #0xc35a04
    // 0xc35b20: SaveReg d0
    //     0xc35b20: str             q0, [SP, #-0x10]!
    // 0xc35b24: SaveReg r2
    //     0xc35b24: str             x2, [SP, #-8]!
    // 0xc35b28: r0 = AllocateDouble()
    //     0xc35b28: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35b2c: RestoreReg r2
    //     0xc35b2c: ldr             x2, [SP], #8
    // 0xc35b30: RestoreReg d0
    //     0xc35b30: ldr             q0, [SP], #0x10
    // 0xc35b34: b               #0xc35a60
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7cae8, size: 0x94
    // 0xd7cae8: ldr             x1, [SP]
    // 0xd7caec: cmp             w1, NULL
    // 0xd7caf0: b.ne            #0xd7cafc
    // 0xd7caf4: r0 = false
    //     0xd7caf4: add             x0, NULL, #0x30  ; false
    // 0xd7caf8: ret
    //     0xd7caf8: ret             
    // 0xd7cafc: r2 = 60
    //     0xd7cafc: movz            x2, #0x3c
    // 0xd7cb00: branchIfSmi(r1, 0xd7cb0c)
    //     0xd7cb00: tbz             w1, #0, #0xd7cb0c
    // 0xd7cb04: r2 = LoadClassIdInstr(r1)
    //     0xd7cb04: ldur            x2, [x1, #-1]
    //     0xd7cb08: ubfx            x2, x2, #0xc, #0x14
    // 0xd7cb0c: cmp             x2, #0x35b
    // 0xd7cb10: b.eq            #0xd7cb1c
    // 0xd7cb14: r0 = false
    //     0xd7cb14: add             x0, NULL, #0x30  ; false
    // 0xd7cb18: ret
    //     0xd7cb18: ret             
    // 0xd7cb1c: d0 = 595.275591
    //     0xd7cb1c: add             x17, PP, #0x33, lsl #12  ; [pp+0x338f8] IMM: double(595.275590551181) from 0x40829a3468d1a346
    //     0xd7cb20: ldr             d0, [x17, #0x8f8]
    // 0xd7cb24: fcmp            d0, d0
    // 0xd7cb28: b.ne            #0xd7cb74
    // 0xd7cb2c: d0 = 841.889764
    //     0xd7cb2c: add             x17, PP, #0x33, lsl #12  ; [pp+0x33900] IMM: double(841.8897637795275) from 0x408a4f1e3c78f1e3
    //     0xd7cb30: ldr             d0, [x17, #0x900]
    // 0xd7cb34: fcmp            d0, d0
    // 0xd7cb38: b.ne            #0xd7cb74
    // 0xd7cb3c: d0 = 56.692913
    //     0xd7cb3c: add             x17, PP, #0x33, lsl #12  ; [pp+0x33908] IMM: double(56.69291338582677) from 0x404c58b162c58b16
    //     0xd7cb40: ldr             d0, [x17, #0x908]
    // 0xd7cb44: fcmp            d0, d0
    // 0xd7cb48: b.ne            #0xd7cb74
    // 0xd7cb4c: fcmp            d0, d0
    // 0xd7cb50: b.ne            #0xd7cb74
    // 0xd7cb54: fcmp            d0, d0
    // 0xd7cb58: b.ne            #0xd7cb74
    // 0xd7cb5c: fcmp            d0, d0
    // 0xd7cb60: r16 = true
    //     0xd7cb60: add             x16, NULL, #0x20  ; true
    // 0xd7cb64: r17 = false
    //     0xd7cb64: add             x17, NULL, #0x30  ; false
    // 0xd7cb68: csel            x1, x16, x17, eq
    // 0xd7cb6c: mov             x0, x1
    // 0xd7cb70: b               #0xd7cb78
    // 0xd7cb74: r0 = false
    //     0xd7cb74: add             x0, NULL, #0x30  ; false
    // 0xd7cb78: ret
    //     0xd7cb78: ret             
  }
}
