// lib: , url: package:pdf/src/pdf/point.dart

// class id: 1050824, size: 0x8
class :: {
}

// class id: 858, size: 0x18, field offset: 0x8
//   const constructor, 
class PdfPoint extends Object {

  _Mint field_8;
  _Mint field_10;

  _ toString(/* No info */) {
    // ** addr: 0xc35b38, size: 0xf8
    // 0xc35b38: EnterFrame
    //     0xc35b38: stp             fp, lr, [SP, #-0x10]!
    //     0xc35b3c: mov             fp, SP
    // 0xc35b40: AllocStack(0x8)
    //     0xc35b40: sub             SP, SP, #8
    // 0xc35b44: CheckStackOverflow
    //     0xc35b44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35b48: cmp             SP, x16
    //     0xc35b4c: b.ls            #0xc35bf0
    // 0xc35b50: r1 = Null
    //     0xc35b50: mov             x1, NULL
    // 0xc35b54: r2 = 10
    //     0xc35b54: movz            x2, #0xa
    // 0xc35b58: r0 = AllocateArray()
    //     0xc35b58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc35b5c: r16 = "PdfPoint("
    //     0xc35b5c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ee00] "PdfPoint("
    //     0xc35b60: ldr             x16, [x16, #0xe00]
    // 0xc35b64: StoreField: r0->field_f = r16
    //     0xc35b64: stur            w16, [x0, #0xf]
    // 0xc35b68: ldr             x1, [fp, #0x10]
    // 0xc35b6c: LoadField: d0 = r1->field_7
    //     0xc35b6c: ldur            d0, [x1, #7]
    // 0xc35b70: r2 = inline_Allocate_Double()
    //     0xc35b70: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xc35b74: add             x2, x2, #0x10
    //     0xc35b78: cmp             x3, x2
    //     0xc35b7c: b.ls            #0xc35bf8
    //     0xc35b80: str             x2, [THR, #0x50]  ; THR::top
    //     0xc35b84: sub             x2, x2, #0xf
    //     0xc35b88: movz            x3, #0xe15c
    //     0xc35b8c: movk            x3, #0x3, lsl #16
    //     0xc35b90: stur            x3, [x2, #-1]
    // 0xc35b94: StoreField: r2->field_7 = d0
    //     0xc35b94: stur            d0, [x2, #7]
    // 0xc35b98: StoreField: r0->field_13 = r2
    //     0xc35b98: stur            w2, [x0, #0x13]
    // 0xc35b9c: r16 = ", "
    //     0xc35b9c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc35ba0: ArrayStore: r0[0] = r16  ; List_4
    //     0xc35ba0: stur            w16, [x0, #0x17]
    // 0xc35ba4: LoadField: d0 = r1->field_f
    //     0xc35ba4: ldur            d0, [x1, #0xf]
    // 0xc35ba8: r1 = inline_Allocate_Double()
    //     0xc35ba8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc35bac: add             x1, x1, #0x10
    //     0xc35bb0: cmp             x2, x1
    //     0xc35bb4: b.ls            #0xc35c14
    //     0xc35bb8: str             x1, [THR, #0x50]  ; THR::top
    //     0xc35bbc: sub             x1, x1, #0xf
    //     0xc35bc0: movz            x2, #0xe15c
    //     0xc35bc4: movk            x2, #0x3, lsl #16
    //     0xc35bc8: stur            x2, [x1, #-1]
    // 0xc35bcc: StoreField: r1->field_7 = d0
    //     0xc35bcc: stur            d0, [x1, #7]
    // 0xc35bd0: StoreField: r0->field_1b = r1
    //     0xc35bd0: stur            w1, [x0, #0x1b]
    // 0xc35bd4: r16 = ")"
    //     0xc35bd4: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc35bd8: StoreField: r0->field_1f = r16
    //     0xc35bd8: stur            w16, [x0, #0x1f]
    // 0xc35bdc: str             x0, [SP]
    // 0xc35be0: r0 = _interpolate()
    //     0xc35be0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc35be4: LeaveFrame
    //     0xc35be4: mov             SP, fp
    //     0xc35be8: ldp             fp, lr, [SP], #0x10
    // 0xc35bec: ret
    //     0xc35bec: ret             
    // 0xc35bf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc35bf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35bf4: b               #0xc35b50
    // 0xc35bf8: SaveReg d0
    //     0xc35bf8: str             q0, [SP, #-0x10]!
    // 0xc35bfc: stp             x0, x1, [SP, #-0x10]!
    // 0xc35c00: r0 = AllocateDouble()
    //     0xc35c00: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35c04: mov             x2, x0
    // 0xc35c08: ldp             x0, x1, [SP], #0x10
    // 0xc35c0c: RestoreReg d0
    //     0xc35c0c: ldr             q0, [SP], #0x10
    // 0xc35c10: b               #0xc35b94
    // 0xc35c14: SaveReg d0
    //     0xc35c14: str             q0, [SP, #-0x10]!
    // 0xc35c18: SaveReg r0
    //     0xc35c18: str             x0, [SP, #-8]!
    // 0xc35c1c: r0 = AllocateDouble()
    //     0xc35c1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35c20: mov             x1, x0
    // 0xc35c24: RestoreReg r0
    //     0xc35c24: ldr             x0, [SP], #8
    // 0xc35c28: RestoreReg d0
    //     0xc35c28: ldr             q0, [SP], #0x10
    // 0xc35c2c: b               #0xc35bcc
  }
  _ translate(/* No info */) {
    // ** addr: 0xe68ed4, size: 0x44
    // 0xe68ed4: EnterFrame
    //     0xe68ed4: stp             fp, lr, [SP, #-0x10]!
    //     0xe68ed8: mov             fp, SP
    // 0xe68edc: AllocStack(0x10)
    //     0xe68edc: sub             SP, SP, #0x10
    // 0xe68ee0: LoadField: d2 = r1->field_7
    //     0xe68ee0: ldur            d2, [x1, #7]
    // 0xe68ee4: fadd            d3, d2, d0
    // 0xe68ee8: stur            d3, [fp, #-0x10]
    // 0xe68eec: LoadField: d0 = r1->field_f
    //     0xe68eec: ldur            d0, [x1, #0xf]
    // 0xe68ef0: fadd            d2, d0, d1
    // 0xe68ef4: stur            d2, [fp, #-8]
    // 0xe68ef8: r0 = PdfPoint()
    //     0xe68ef8: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe68efc: ldur            d0, [fp, #-0x10]
    // 0xe68f00: StoreField: r0->field_7 = d0
    //     0xe68f00: stur            d0, [x0, #7]
    // 0xe68f04: ldur            d0, [fp, #-8]
    // 0xe68f08: StoreField: r0->field_f = d0
    //     0xe68f08: stur            d0, [x0, #0xf]
    // 0xe68f0c: LeaveFrame
    //     0xe68f0c: mov             SP, fp
    //     0xe68f10: ldp             fp, lr, [SP], #0x10
    // 0xe68f14: ret
    //     0xe68f14: ret             
  }
}
