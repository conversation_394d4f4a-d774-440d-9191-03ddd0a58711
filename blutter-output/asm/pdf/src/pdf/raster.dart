// lib: , url: package:pdf/src/pdf/raster.dart

// class id: 1050825, size: 0x8
class :: {
}

// class id: 856, size: 0x20, field offset: 0x8
//   const constructor, 
class PdfRasterBase extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc35c30, size: 0xc8
    // 0xc35c30: EnterFrame
    //     0xc35c30: stp             fp, lr, [SP, #-0x10]!
    //     0xc35c34: mov             fp, SP
    // 0xc35c38: AllocStack(0x8)
    //     0xc35c38: sub             SP, SP, #8
    // 0xc35c3c: CheckStackOverflow
    //     0xc35c3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35c40: cmp             SP, x16
    //     0xc35c44: b.ls            #0xc35cf0
    // 0xc35c48: r1 = Null
    //     0xc35c48: mov             x1, NULL
    // 0xc35c4c: r2 = 14
    //     0xc35c4c: movz            x2, #0xe
    // 0xc35c50: r0 = AllocateArray()
    //     0xc35c50: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc35c54: mov             x2, x0
    // 0xc35c58: r16 = "Image "
    //     0xc35c58: add             x16, PP, #0x51, lsl #12  ; [pp+0x51370] "Image "
    //     0xc35c5c: ldr             x16, [x16, #0x370]
    // 0xc35c60: StoreField: r2->field_f = r16
    //     0xc35c60: stur            w16, [x2, #0xf]
    // 0xc35c64: ldr             x3, [fp, #0x10]
    // 0xc35c68: LoadField: r4 = r3->field_7
    //     0xc35c68: ldur            x4, [x3, #7]
    // 0xc35c6c: r0 = BoxInt64Instr(r4)
    //     0xc35c6c: sbfiz           x0, x4, #1, #0x1f
    //     0xc35c70: cmp             x4, x0, asr #1
    //     0xc35c74: b.eq            #0xc35c80
    //     0xc35c78: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35c7c: stur            x4, [x0, #7]
    // 0xc35c80: StoreField: r2->field_13 = r0
    //     0xc35c80: stur            w0, [x2, #0x13]
    // 0xc35c84: r16 = "x"
    //     0xc35c84: ldr             x16, [PP, #0x71a0]  ; [pp+0x71a0] "x"
    // 0xc35c88: ArrayStore: r2[0] = r16  ; List_4
    //     0xc35c88: stur            w16, [x2, #0x17]
    // 0xc35c8c: LoadField: r5 = r3->field_f
    //     0xc35c8c: ldur            x5, [x3, #0xf]
    // 0xc35c90: r0 = BoxInt64Instr(r5)
    //     0xc35c90: sbfiz           x0, x5, #1, #0x1f
    //     0xc35c94: cmp             x5, x0, asr #1
    //     0xc35c98: b.eq            #0xc35ca4
    //     0xc35c9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35ca0: stur            x5, [x0, #7]
    // 0xc35ca4: StoreField: r2->field_1b = r0
    //     0xc35ca4: stur            w0, [x2, #0x1b]
    // 0xc35ca8: r16 = " "
    //     0xc35ca8: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc35cac: StoreField: r2->field_1f = r16
    //     0xc35cac: stur            w16, [x2, #0x1f]
    // 0xc35cb0: mul             x0, x4, x5
    // 0xc35cb4: lsl             x3, x0, #2
    // 0xc35cb8: r0 = BoxInt64Instr(r3)
    //     0xc35cb8: sbfiz           x0, x3, #1, #0x1f
    //     0xc35cbc: cmp             x3, x0, asr #1
    //     0xc35cc0: b.eq            #0xc35ccc
    //     0xc35cc4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc35cc8: stur            x3, [x0, #7]
    // 0xc35ccc: StoreField: r2->field_23 = r0
    //     0xc35ccc: stur            w0, [x2, #0x23]
    // 0xc35cd0: r16 = " bytes"
    //     0xc35cd0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e598] " bytes"
    //     0xc35cd4: ldr             x16, [x16, #0x598]
    // 0xc35cd8: StoreField: r2->field_27 = r16
    //     0xc35cd8: stur            w16, [x2, #0x27]
    // 0xc35cdc: str             x2, [SP]
    // 0xc35ce0: r0 = _interpolate()
    //     0xc35ce0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc35ce4: LeaveFrame
    //     0xc35ce4: mov             SP, fp
    //     0xc35ce8: ldp             fp, lr, [SP], #0x10
    // 0xc35cec: ret
    //     0xc35cec: ret             
    // 0xc35cf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc35cf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35cf4: b               #0xc35c48
  }
  factory _ PdfRasterBase.fromImage(/* No info */) {
    // ** addr: 0xe69cf4, size: 0x130
    // 0xe69cf4: EnterFrame
    //     0xe69cf4: stp             fp, lr, [SP, #-0x10]!
    //     0xe69cf8: mov             fp, SP
    // 0xe69cfc: AllocStack(0x30)
    //     0xe69cfc: sub             SP, SP, #0x30
    // 0xe69d00: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xe69d00: mov             x0, x2
    //     0xe69d04: stur            x2, [fp, #-8]
    // 0xe69d08: CheckStackOverflow
    //     0xe69d08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe69d0c: cmp             SP, x16
    //     0xe69d10: b.ls            #0xe69e1c
    // 0xe69d14: r16 = Instance_Format
    //     0xe69d14: add             x16, PP, #0x47, lsl #12  ; [pp+0x47108] Obj!Format@e32821
    //     0xe69d18: ldr             x16, [x16, #0x108]
    // 0xe69d1c: r30 = true
    //     0xe69d1c: add             lr, NULL, #0x20  ; true
    // 0xe69d20: stp             lr, x16, [SP]
    // 0xe69d24: mov             x1, x0
    // 0xe69d28: r2 = 4
    //     0xe69d28: movz            x2, #0x4
    // 0xe69d2c: r4 = const [0, 0x4, 0x2, 0x2, format, 0x2, noAnimation, 0x3, null]
    //     0xe69d2c: add             x4, PP, #0x47, lsl #12  ; [pp+0x47110] List(9) [0, 0x4, 0x2, 0x2, "format", 0x2, "noAnimation", 0x3, Null]
    //     0xe69d30: ldr             x4, [x4, #0x110]
    // 0xe69d34: r0 = convert()
    //     0xe69d34: bl              #0xc896d0  ; [package:image/src/image/image.dart] Image::convert
    // 0xe69d38: mov             x1, x0
    // 0xe69d3c: r0 = toUint8List()
    //     0xe69d3c: bl              #0xc77e90  ; [package:image/src/image/image.dart] Image::toUint8List
    // 0xe69d40: mov             x2, x0
    // 0xe69d44: ldur            x0, [fp, #-8]
    // 0xe69d48: stur            x2, [fp, #-0x20]
    // 0xe69d4c: LoadField: r3 = r0->field_b
    //     0xe69d4c: ldur            w3, [x0, #0xb]
    // 0xe69d50: DecompressPointer r3
    //     0xe69d50: add             x3, x3, HEAP, lsl #32
    // 0xe69d54: cmp             w3, NULL
    // 0xe69d58: b.ne            #0xe69d64
    // 0xe69d5c: r0 = Null
    //     0xe69d5c: mov             x0, NULL
    // 0xe69d60: b               #0xe69d7c
    // 0xe69d64: LoadField: r4 = r3->field_b
    //     0xe69d64: ldur            x4, [x3, #0xb]
    // 0xe69d68: r0 = BoxInt64Instr(r4)
    //     0xe69d68: sbfiz           x0, x4, #1, #0x1f
    //     0xe69d6c: cmp             x4, x0, asr #1
    //     0xe69d70: b.eq            #0xe69d7c
    //     0xe69d74: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe69d78: stur            x4, [x0, #7]
    // 0xe69d7c: cmp             w0, NULL
    // 0xe69d80: b.ne            #0xe69d8c
    // 0xe69d84: r4 = 0
    //     0xe69d84: movz            x4, #0
    // 0xe69d88: b               #0xe69d9c
    // 0xe69d8c: r1 = LoadInt32Instr(r0)
    //     0xe69d8c: sbfx            x1, x0, #1, #0x1f
    //     0xe69d90: tbz             w0, #0, #0xe69d98
    //     0xe69d94: ldur            x1, [x0, #7]
    // 0xe69d98: mov             x4, x1
    // 0xe69d9c: stur            x4, [fp, #-0x18]
    // 0xe69da0: cmp             w3, NULL
    // 0xe69da4: b.ne            #0xe69db0
    // 0xe69da8: r0 = Null
    //     0xe69da8: mov             x0, NULL
    // 0xe69dac: b               #0xe69dc8
    // 0xe69db0: LoadField: r5 = r3->field_13
    //     0xe69db0: ldur            x5, [x3, #0x13]
    // 0xe69db4: r0 = BoxInt64Instr(r5)
    //     0xe69db4: sbfiz           x0, x5, #1, #0x1f
    //     0xe69db8: cmp             x5, x0, asr #1
    //     0xe69dbc: b.eq            #0xe69dc8
    //     0xe69dc0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe69dc4: stur            x5, [x0, #7]
    // 0xe69dc8: cmp             w0, NULL
    // 0xe69dcc: b.ne            #0xe69dd8
    // 0xe69dd0: r0 = 0
    //     0xe69dd0: movz            x0, #0
    // 0xe69dd4: b               #0xe69de8
    // 0xe69dd8: r1 = LoadInt32Instr(r0)
    //     0xe69dd8: sbfx            x1, x0, #1, #0x1f
    //     0xe69ddc: tbz             w0, #0, #0xe69de4
    //     0xe69de0: ldur            x1, [x0, #7]
    // 0xe69de4: mov             x0, x1
    // 0xe69de8: stur            x0, [fp, #-0x10]
    // 0xe69dec: r0 = PdfRasterBase()
    //     0xe69dec: bl              #0xe69e24  ; AllocatePdfRasterBaseStub -> PdfRasterBase (size=0x20)
    // 0xe69df0: ldur            x1, [fp, #-0x18]
    // 0xe69df4: StoreField: r0->field_7 = r1
    //     0xe69df4: stur            x1, [x0, #7]
    // 0xe69df8: ldur            x1, [fp, #-0x10]
    // 0xe69dfc: StoreField: r0->field_f = r1
    //     0xe69dfc: stur            x1, [x0, #0xf]
    // 0xe69e00: r1 = true
    //     0xe69e00: add             x1, NULL, #0x20  ; true
    // 0xe69e04: ArrayStore: r0[0] = r1  ; List_4
    //     0xe69e04: stur            w1, [x0, #0x17]
    // 0xe69e08: ldur            x1, [fp, #-0x20]
    // 0xe69e0c: StoreField: r0->field_1b = r1
    //     0xe69e0c: stur            w1, [x0, #0x1b]
    // 0xe69e10: LeaveFrame
    //     0xe69e10: mov             SP, fp
    //     0xe69e14: ldp             fp, lr, [SP], #0x10
    // 0xe69e18: ret
    //     0xe69e18: ret             
    // 0xe69e1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe69e1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe69e20: b               #0xe69d14
  }
}
