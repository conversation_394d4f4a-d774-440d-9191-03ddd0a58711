// lib: , url: package:pdf/src/pdf/rect.dart

// class id: 1050826, size: 0x8
class :: {
}

// class id: 855, size: 0x28, field offset: 0x8
//   const constructor, 
class PdfRect extends Object {

  _Mint field_8;
  _Mint field_10;
  _Mint field_18;
  _Mint field_20;

  _ toString(/* No info */) {
    // ** addr: 0xc35cf8, size: 0x224
    // 0xc35cf8: EnterFrame
    //     0xc35cf8: stp             fp, lr, [SP, #-0x10]!
    //     0xc35cfc: mov             fp, SP
    // 0xc35d00: AllocStack(0x8)
    //     0xc35d00: sub             SP, SP, #8
    // 0xc35d04: CheckStackOverflow
    //     0xc35d04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35d08: cmp             SP, x16
    //     0xc35d0c: b.ls            #0xc35eb4
    // 0xc35d10: r1 = Null
    //     0xc35d10: mov             x1, NULL
    // 0xc35d14: r2 = 18
    //     0xc35d14: movz            x2, #0x12
    // 0xc35d18: r0 = AllocateArray()
    //     0xc35d18: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc35d1c: mov             x2, x0
    // 0xc35d20: r16 = "PdfRect("
    //     0xc35d20: add             x16, PP, #0x33, lsl #12  ; [pp+0x338f0] "PdfRect("
    //     0xc35d24: ldr             x16, [x16, #0x8f0]
    // 0xc35d28: StoreField: r2->field_f = r16
    //     0xc35d28: stur            w16, [x2, #0xf]
    // 0xc35d2c: ldr             x3, [fp, #0x10]
    // 0xc35d30: LoadField: d0 = r3->field_7
    //     0xc35d30: ldur            d0, [x3, #7]
    // 0xc35d34: r0 = inline_Allocate_Double()
    //     0xc35d34: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc35d38: add             x0, x0, #0x10
    //     0xc35d3c: cmp             x1, x0
    //     0xc35d40: b.ls            #0xc35ebc
    //     0xc35d44: str             x0, [THR, #0x50]  ; THR::top
    //     0xc35d48: sub             x0, x0, #0xf
    //     0xc35d4c: movz            x1, #0xe15c
    //     0xc35d50: movk            x1, #0x3, lsl #16
    //     0xc35d54: stur            x1, [x0, #-1]
    // 0xc35d58: StoreField: r0->field_7 = d0
    //     0xc35d58: stur            d0, [x0, #7]
    // 0xc35d5c: mov             x1, x2
    // 0xc35d60: ArrayStore: r1[1] = r0  ; List_4
    //     0xc35d60: add             x25, x1, #0x13
    //     0xc35d64: str             w0, [x25]
    //     0xc35d68: tbz             w0, #0, #0xc35d84
    //     0xc35d6c: ldurb           w16, [x1, #-1]
    //     0xc35d70: ldurb           w17, [x0, #-1]
    //     0xc35d74: and             x16, x17, x16, lsr #2
    //     0xc35d78: tst             x16, HEAP, lsr #32
    //     0xc35d7c: b.eq            #0xc35d84
    //     0xc35d80: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35d84: r16 = ", "
    //     0xc35d84: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc35d88: ArrayStore: r2[0] = r16  ; List_4
    //     0xc35d88: stur            w16, [x2, #0x17]
    // 0xc35d8c: LoadField: d0 = r3->field_f
    //     0xc35d8c: ldur            d0, [x3, #0xf]
    // 0xc35d90: r0 = inline_Allocate_Double()
    //     0xc35d90: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc35d94: add             x0, x0, #0x10
    //     0xc35d98: cmp             x1, x0
    //     0xc35d9c: b.ls            #0xc35ed4
    //     0xc35da0: str             x0, [THR, #0x50]  ; THR::top
    //     0xc35da4: sub             x0, x0, #0xf
    //     0xc35da8: movz            x1, #0xe15c
    //     0xc35dac: movk            x1, #0x3, lsl #16
    //     0xc35db0: stur            x1, [x0, #-1]
    // 0xc35db4: StoreField: r0->field_7 = d0
    //     0xc35db4: stur            d0, [x0, #7]
    // 0xc35db8: mov             x1, x2
    // 0xc35dbc: ArrayStore: r1[3] = r0  ; List_4
    //     0xc35dbc: add             x25, x1, #0x1b
    //     0xc35dc0: str             w0, [x25]
    //     0xc35dc4: tbz             w0, #0, #0xc35de0
    //     0xc35dc8: ldurb           w16, [x1, #-1]
    //     0xc35dcc: ldurb           w17, [x0, #-1]
    //     0xc35dd0: and             x16, x17, x16, lsr #2
    //     0xc35dd4: tst             x16, HEAP, lsr #32
    //     0xc35dd8: b.eq            #0xc35de0
    //     0xc35ddc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35de0: r16 = ", "
    //     0xc35de0: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc35de4: StoreField: r2->field_1f = r16
    //     0xc35de4: stur            w16, [x2, #0x1f]
    // 0xc35de8: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc35de8: ldur            d0, [x3, #0x17]
    // 0xc35dec: r0 = inline_Allocate_Double()
    //     0xc35dec: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc35df0: add             x0, x0, #0x10
    //     0xc35df4: cmp             x1, x0
    //     0xc35df8: b.ls            #0xc35eec
    //     0xc35dfc: str             x0, [THR, #0x50]  ; THR::top
    //     0xc35e00: sub             x0, x0, #0xf
    //     0xc35e04: movz            x1, #0xe15c
    //     0xc35e08: movk            x1, #0x3, lsl #16
    //     0xc35e0c: stur            x1, [x0, #-1]
    // 0xc35e10: StoreField: r0->field_7 = d0
    //     0xc35e10: stur            d0, [x0, #7]
    // 0xc35e14: mov             x1, x2
    // 0xc35e18: ArrayStore: r1[5] = r0  ; List_4
    //     0xc35e18: add             x25, x1, #0x23
    //     0xc35e1c: str             w0, [x25]
    //     0xc35e20: tbz             w0, #0, #0xc35e3c
    //     0xc35e24: ldurb           w16, [x1, #-1]
    //     0xc35e28: ldurb           w17, [x0, #-1]
    //     0xc35e2c: and             x16, x17, x16, lsr #2
    //     0xc35e30: tst             x16, HEAP, lsr #32
    //     0xc35e34: b.eq            #0xc35e3c
    //     0xc35e38: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35e3c: r16 = ", "
    //     0xc35e3c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc35e40: StoreField: r2->field_27 = r16
    //     0xc35e40: stur            w16, [x2, #0x27]
    // 0xc35e44: LoadField: d0 = r3->field_1f
    //     0xc35e44: ldur            d0, [x3, #0x1f]
    // 0xc35e48: r0 = inline_Allocate_Double()
    //     0xc35e48: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc35e4c: add             x0, x0, #0x10
    //     0xc35e50: cmp             x1, x0
    //     0xc35e54: b.ls            #0xc35f04
    //     0xc35e58: str             x0, [THR, #0x50]  ; THR::top
    //     0xc35e5c: sub             x0, x0, #0xf
    //     0xc35e60: movz            x1, #0xe15c
    //     0xc35e64: movk            x1, #0x3, lsl #16
    //     0xc35e68: stur            x1, [x0, #-1]
    // 0xc35e6c: StoreField: r0->field_7 = d0
    //     0xc35e6c: stur            d0, [x0, #7]
    // 0xc35e70: mov             x1, x2
    // 0xc35e74: ArrayStore: r1[7] = r0  ; List_4
    //     0xc35e74: add             x25, x1, #0x2b
    //     0xc35e78: str             w0, [x25]
    //     0xc35e7c: tbz             w0, #0, #0xc35e98
    //     0xc35e80: ldurb           w16, [x1, #-1]
    //     0xc35e84: ldurb           w17, [x0, #-1]
    //     0xc35e88: and             x16, x17, x16, lsr #2
    //     0xc35e8c: tst             x16, HEAP, lsr #32
    //     0xc35e90: b.eq            #0xc35e98
    //     0xc35e94: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc35e98: r16 = ")"
    //     0xc35e98: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc35e9c: StoreField: r2->field_2f = r16
    //     0xc35e9c: stur            w16, [x2, #0x2f]
    // 0xc35ea0: str             x2, [SP]
    // 0xc35ea4: r0 = _interpolate()
    //     0xc35ea4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc35ea8: LeaveFrame
    //     0xc35ea8: mov             SP, fp
    //     0xc35eac: ldp             fp, lr, [SP], #0x10
    // 0xc35eb0: ret
    //     0xc35eb0: ret             
    // 0xc35eb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc35eb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc35eb8: b               #0xc35d10
    // 0xc35ebc: SaveReg d0
    //     0xc35ebc: str             q0, [SP, #-0x10]!
    // 0xc35ec0: stp             x2, x3, [SP, #-0x10]!
    // 0xc35ec4: r0 = AllocateDouble()
    //     0xc35ec4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35ec8: ldp             x2, x3, [SP], #0x10
    // 0xc35ecc: RestoreReg d0
    //     0xc35ecc: ldr             q0, [SP], #0x10
    // 0xc35ed0: b               #0xc35d58
    // 0xc35ed4: SaveReg d0
    //     0xc35ed4: str             q0, [SP, #-0x10]!
    // 0xc35ed8: stp             x2, x3, [SP, #-0x10]!
    // 0xc35edc: r0 = AllocateDouble()
    //     0xc35edc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35ee0: ldp             x2, x3, [SP], #0x10
    // 0xc35ee4: RestoreReg d0
    //     0xc35ee4: ldr             q0, [SP], #0x10
    // 0xc35ee8: b               #0xc35db4
    // 0xc35eec: SaveReg d0
    //     0xc35eec: str             q0, [SP, #-0x10]!
    // 0xc35ef0: stp             x2, x3, [SP, #-0x10]!
    // 0xc35ef4: r0 = AllocateDouble()
    //     0xc35ef4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35ef8: ldp             x2, x3, [SP], #0x10
    // 0xc35efc: RestoreReg d0
    //     0xc35efc: ldr             q0, [SP], #0x10
    // 0xc35f00: b               #0xc35e10
    // 0xc35f04: SaveReg d0
    //     0xc35f04: str             q0, [SP, #-0x10]!
    // 0xc35f08: SaveReg r2
    //     0xc35f08: str             x2, [SP, #-8]!
    // 0xc35f0c: r0 = AllocateDouble()
    //     0xc35f0c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc35f10: RestoreReg r2
    //     0xc35f10: ldr             x2, [SP], #8
    // 0xc35f14: RestoreReg d0
    //     0xc35f14: ldr             q0, [SP], #0x10
    // 0xc35f18: b               #0xc35e6c
  }
  get _ offset(/* No info */) {
    // ** addr: 0xc3adb0, size: 0x3c
    // 0xc3adb0: EnterFrame
    //     0xc3adb0: stp             fp, lr, [SP, #-0x10]!
    //     0xc3adb4: mov             fp, SP
    // 0xc3adb8: AllocStack(0x10)
    //     0xc3adb8: sub             SP, SP, #0x10
    // 0xc3adbc: LoadField: d0 = r1->field_7
    //     0xc3adbc: ldur            d0, [x1, #7]
    // 0xc3adc0: stur            d0, [fp, #-0x10]
    // 0xc3adc4: LoadField: d1 = r1->field_f
    //     0xc3adc4: ldur            d1, [x1, #0xf]
    // 0xc3adc8: stur            d1, [fp, #-8]
    // 0xc3adcc: r0 = PdfPoint()
    //     0xc3adcc: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xc3add0: ldur            d0, [fp, #-0x10]
    // 0xc3add4: StoreField: r0->field_7 = d0
    //     0xc3add4: stur            d0, [x0, #7]
    // 0xc3add8: ldur            d0, [fp, #-8]
    // 0xc3addc: StoreField: r0->field_f = d0
    //     0xc3addc: stur            d0, [x0, #0xf]
    // 0xc3ade0: LeaveFrame
    //     0xc3ade0: mov             SP, fp
    //     0xc3ade4: ldp             fp, lr, [SP], #0x10
    // 0xc3ade8: ret
    //     0xc3ade8: ret             
  }
  factory _ PdfRect.fromLTRB(/* No info */) {
    // ** addr: 0xe68604, size: 0x54
    // 0xe68604: EnterFrame
    //     0xe68604: stp             fp, lr, [SP, #-0x10]!
    //     0xe68608: mov             fp, SP
    // 0xe6860c: AllocStack(0x20)
    //     0xe6860c: sub             SP, SP, #0x20
    // 0xe68610: SetupParameters(dynamic _ /* d0 => d0, fp-0x18 */, dynamic _ /* d1 => d1, fp-0x20 */)
    //     0xe68610: stur            d0, [fp, #-0x18]
    //     0xe68614: stur            d1, [fp, #-0x20]
    // 0xe68618: fsub            d4, d2, d0
    // 0xe6861c: stur            d4, [fp, #-0x10]
    // 0xe68620: fsub            d2, d3, d1
    // 0xe68624: stur            d2, [fp, #-8]
    // 0xe68628: r0 = PdfRect()
    //     0xe68628: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe6862c: ldur            d0, [fp, #-0x18]
    // 0xe68630: StoreField: r0->field_7 = d0
    //     0xe68630: stur            d0, [x0, #7]
    // 0xe68634: ldur            d0, [fp, #-0x20]
    // 0xe68638: StoreField: r0->field_f = d0
    //     0xe68638: stur            d0, [x0, #0xf]
    // 0xe6863c: ldur            d0, [fp, #-0x10]
    // 0xe68640: ArrayStore: r0[0] = d0  ; List_8
    //     0xe68640: stur            d0, [x0, #0x17]
    // 0xe68644: ldur            d0, [fp, #-8]
    // 0xe68648: StoreField: r0->field_1f = d0
    //     0xe68648: stur            d0, [x0, #0x1f]
    // 0xe6864c: LeaveFrame
    //     0xe6864c: mov             SP, fp
    //     0xe68650: ldp             fp, lr, [SP], #0x10
    // 0xe68654: ret
    //     0xe68654: ret             
  }
  get _ size(/* No info */) {
    // ** addr: 0xe68b8c, size: 0x3c
    // 0xe68b8c: EnterFrame
    //     0xe68b8c: stp             fp, lr, [SP, #-0x10]!
    //     0xe68b90: mov             fp, SP
    // 0xe68b94: AllocStack(0x10)
    //     0xe68b94: sub             SP, SP, #0x10
    // 0xe68b98: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe68b98: ldur            d0, [x1, #0x17]
    // 0xe68b9c: stur            d0, [fp, #-0x10]
    // 0xe68ba0: LoadField: d1 = r1->field_1f
    //     0xe68ba0: ldur            d1, [x1, #0x1f]
    // 0xe68ba4: stur            d1, [fp, #-8]
    // 0xe68ba8: r0 = PdfPoint()
    //     0xe68ba8: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe68bac: ldur            d0, [fp, #-0x10]
    // 0xe68bb0: StoreField: r0->field_7 = d0
    //     0xe68bb0: stur            d0, [x0, #7]
    // 0xe68bb4: ldur            d0, [fp, #-8]
    // 0xe68bb8: StoreField: r0->field_f = d0
    //     0xe68bb8: stur            d0, [x0, #0xf]
    // 0xe68bbc: LeaveFrame
    //     0xe68bbc: mov             SP, fp
    //     0xe68bc0: ldp             fp, lr, [SP], #0x10
    // 0xe68bc4: ret
    //     0xe68bc4: ret             
  }
  factory _ PdfRect.fromPoints(/* No info */) {
    // ** addr: 0xe68e78, size: 0x5c
    // 0xe68e78: EnterFrame
    //     0xe68e78: stp             fp, lr, [SP, #-0x10]!
    //     0xe68e7c: mov             fp, SP
    // 0xe68e80: AllocStack(0x20)
    //     0xe68e80: sub             SP, SP, #0x20
    // 0xe68e84: LoadField: d0 = r2->field_7
    //     0xe68e84: ldur            d0, [x2, #7]
    // 0xe68e88: stur            d0, [fp, #-0x20]
    // 0xe68e8c: LoadField: d1 = r2->field_f
    //     0xe68e8c: ldur            d1, [x2, #0xf]
    // 0xe68e90: stur            d1, [fp, #-0x18]
    // 0xe68e94: LoadField: d2 = r3->field_7
    //     0xe68e94: ldur            d2, [x3, #7]
    // 0xe68e98: stur            d2, [fp, #-0x10]
    // 0xe68e9c: LoadField: d3 = r3->field_f
    //     0xe68e9c: ldur            d3, [x3, #0xf]
    // 0xe68ea0: stur            d3, [fp, #-8]
    // 0xe68ea4: r0 = PdfRect()
    //     0xe68ea4: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe68ea8: ldur            d0, [fp, #-0x20]
    // 0xe68eac: StoreField: r0->field_7 = d0
    //     0xe68eac: stur            d0, [x0, #7]
    // 0xe68eb0: ldur            d0, [fp, #-0x18]
    // 0xe68eb4: StoreField: r0->field_f = d0
    //     0xe68eb4: stur            d0, [x0, #0xf]
    // 0xe68eb8: ldur            d0, [fp, #-0x10]
    // 0xe68ebc: ArrayStore: r0[0] = d0  ; List_8
    //     0xe68ebc: stur            d0, [x0, #0x17]
    // 0xe68ec0: ldur            d0, [fp, #-8]
    // 0xe68ec4: StoreField: r0->field_1f = d0
    //     0xe68ec4: stur            d0, [x0, #0x1f]
    // 0xe68ec8: LeaveFrame
    //     0xe68ec8: mov             SP, fp
    //     0xe68ecc: ldp             fp, lr, [SP], #0x10
    // 0xe68ed0: ret
    //     0xe68ed0: ret             
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xe89a1c, size: 0x54
    // 0xe89a1c: EnterFrame
    //     0xe89a1c: stp             fp, lr, [SP, #-0x10]!
    //     0xe89a20: mov             fp, SP
    // 0xe89a24: AllocStack(0x20)
    //     0xe89a24: sub             SP, SP, #0x20
    // 0xe89a28: SetupParameters(dynamic _ /* d0 => d0, fp-0x18 */, dynamic _ /* d1 => d1, fp-0x20 */)
    //     0xe89a28: stur            d0, [fp, #-0x18]
    //     0xe89a2c: stur            d1, [fp, #-0x20]
    // 0xe89a30: ArrayLoad: d2 = r1[0]  ; List_8
    //     0xe89a30: ldur            d2, [x1, #0x17]
    // 0xe89a34: stur            d2, [fp, #-0x10]
    // 0xe89a38: LoadField: d3 = r1->field_1f
    //     0xe89a38: ldur            d3, [x1, #0x1f]
    // 0xe89a3c: stur            d3, [fp, #-8]
    // 0xe89a40: r0 = PdfRect()
    //     0xe89a40: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe89a44: ldur            d0, [fp, #-0x18]
    // 0xe89a48: StoreField: r0->field_7 = d0
    //     0xe89a48: stur            d0, [x0, #7]
    // 0xe89a4c: ldur            d0, [fp, #-0x20]
    // 0xe89a50: StoreField: r0->field_f = d0
    //     0xe89a50: stur            d0, [x0, #0xf]
    // 0xe89a54: ldur            d0, [fp, #-0x10]
    // 0xe89a58: ArrayStore: r0[0] = d0  ; List_8
    //     0xe89a58: stur            d0, [x0, #0x17]
    // 0xe89a5c: ldur            d0, [fp, #-8]
    // 0xe89a60: StoreField: r0->field_1f = d0
    //     0xe89a60: stur            d0, [x0, #0x1f]
    // 0xe89a64: LeaveFrame
    //     0xe89a64: mov             SP, fp
    //     0xe89a68: ldp             fp, lr, [SP], #0x10
    // 0xe89a6c: ret
    //     0xe89a6c: ret             
  }
}
