// lib: , url: package:pdf/src/svg/brush.dart

// class id: 1050827, size: 0x8
class :: {
}

// class id: 854, size: 0x54, field offset: 0x8
//   const constructor, 
class SvgBrush extends Object {

  _Double field_8;
  SvgColor field_c;
  bool field_10;
  _Double field_14;
  SvgColor field_18;
  _Double field_1c;
  SvgNumeric field_20;
  _ImmutableList<double> field_24;
  _Double field_28;
  PdfLineCap field_2c;
  PdfLineJoin field_30;
  _Double field_34;
  SvgNumeric field_38;
  _OneByteString field_3c;
  _OneByteString field_40;
  _OneByteString field_44;
  SvgTextAnchor field_48;

  _ toString(/* No info */) {
    // ** addr: 0xc35f1c, size: 0x118
    // 0xc35f1c: EnterFrame
    //     0xc35f1c: stp             fp, lr, [SP, #-0x10]!
    //     0xc35f20: mov             fp, SP
    // 0xc35f24: AllocStack(0x8)
    //     0xc35f24: sub             SP, SP, #8
    // 0xc35f28: CheckStackOverflow
    //     0xc35f28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc35f2c: cmp             SP, x16
    //     0xc35f30: b.ls            #0xc3602c
    // 0xc35f34: r1 = Null
    //     0xc35f34: mov             x1, NULL
    // 0xc35f38: r2 = 36
    //     0xc35f38: movz            x2, #0x24
    // 0xc35f3c: r0 = AllocateArray()
    //     0xc35f3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc35f40: r16 = SvgBrush
    //     0xc35f40: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f18] Type: SvgBrush
    //     0xc35f44: ldr             x16, [x16, #0xf18]
    // 0xc35f48: StoreField: r0->field_f = r16
    //     0xc35f48: stur            w16, [x0, #0xf]
    // 0xc35f4c: r16 = " fill: "
    //     0xc35f4c: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f20] " fill: "
    //     0xc35f50: ldr             x16, [x16, #0xf20]
    // 0xc35f54: StoreField: r0->field_13 = r16
    //     0xc35f54: stur            w16, [x0, #0x13]
    // 0xc35f58: ldr             x1, [fp, #0x10]
    // 0xc35f5c: LoadField: r2 = r1->field_b
    //     0xc35f5c: ldur            w2, [x1, #0xb]
    // 0xc35f60: DecompressPointer r2
    //     0xc35f60: add             x2, x2, HEAP, lsl #32
    // 0xc35f64: ArrayStore: r0[0] = r2  ; List_4
    //     0xc35f64: stur            w2, [x0, #0x17]
    // 0xc35f68: r16 = " fillEvenOdd: "
    //     0xc35f68: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f28] " fillEvenOdd: "
    //     0xc35f6c: ldr             x16, [x16, #0xf28]
    // 0xc35f70: StoreField: r0->field_1b = r16
    //     0xc35f70: stur            w16, [x0, #0x1b]
    // 0xc35f74: LoadField: r2 = r1->field_f
    //     0xc35f74: ldur            w2, [x1, #0xf]
    // 0xc35f78: DecompressPointer r2
    //     0xc35f78: add             x2, x2, HEAP, lsl #32
    // 0xc35f7c: StoreField: r0->field_1f = r2
    //     0xc35f7c: stur            w2, [x0, #0x1f]
    // 0xc35f80: r16 = " stroke:"
    //     0xc35f80: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f30] " stroke:"
    //     0xc35f84: ldr             x16, [x16, #0xf30]
    // 0xc35f88: StoreField: r0->field_23 = r16
    //     0xc35f88: stur            w16, [x0, #0x23]
    // 0xc35f8c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc35f8c: ldur            w2, [x1, #0x17]
    // 0xc35f90: DecompressPointer r2
    //     0xc35f90: add             x2, x2, HEAP, lsl #32
    // 0xc35f94: StoreField: r0->field_27 = r2
    //     0xc35f94: stur            w2, [x0, #0x27]
    // 0xc35f98: r16 = " strokeWidth:"
    //     0xc35f98: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f38] " strokeWidth:"
    //     0xc35f9c: ldr             x16, [x16, #0xf38]
    // 0xc35fa0: StoreField: r0->field_2b = r16
    //     0xc35fa0: stur            w16, [x0, #0x2b]
    // 0xc35fa4: LoadField: r2 = r1->field_1f
    //     0xc35fa4: ldur            w2, [x1, #0x1f]
    // 0xc35fa8: DecompressPointer r2
    //     0xc35fa8: add             x2, x2, HEAP, lsl #32
    // 0xc35fac: StoreField: r0->field_2f = r2
    //     0xc35fac: stur            w2, [x0, #0x2f]
    // 0xc35fb0: r16 = " strokeDashArray:"
    //     0xc35fb0: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f40] " strokeDashArray:"
    //     0xc35fb4: ldr             x16, [x16, #0xf40]
    // 0xc35fb8: StoreField: r0->field_33 = r16
    //     0xc35fb8: stur            w16, [x0, #0x33]
    // 0xc35fbc: LoadField: r2 = r1->field_23
    //     0xc35fbc: ldur            w2, [x1, #0x23]
    // 0xc35fc0: DecompressPointer r2
    //     0xc35fc0: add             x2, x2, HEAP, lsl #32
    // 0xc35fc4: StoreField: r0->field_37 = r2
    //     0xc35fc4: stur            w2, [x0, #0x37]
    // 0xc35fc8: r16 = " fontSize:"
    //     0xc35fc8: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f48] " fontSize:"
    //     0xc35fcc: ldr             x16, [x16, #0xf48]
    // 0xc35fd0: StoreField: r0->field_3b = r16
    //     0xc35fd0: stur            w16, [x0, #0x3b]
    // 0xc35fd4: LoadField: r2 = r1->field_37
    //     0xc35fd4: ldur            w2, [x1, #0x37]
    // 0xc35fd8: DecompressPointer r2
    //     0xc35fd8: add             x2, x2, HEAP, lsl #32
    // 0xc35fdc: StoreField: r0->field_3f = r2
    //     0xc35fdc: stur            w2, [x0, #0x3f]
    // 0xc35fe0: r16 = " fontFamily:"
    //     0xc35fe0: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f50] " fontFamily:"
    //     0xc35fe4: ldr             x16, [x16, #0xf50]
    // 0xc35fe8: StoreField: r0->field_43 = r16
    //     0xc35fe8: stur            w16, [x0, #0x43]
    // 0xc35fec: LoadField: r2 = r1->field_3b
    //     0xc35fec: ldur            w2, [x1, #0x3b]
    // 0xc35ff0: DecompressPointer r2
    //     0xc35ff0: add             x2, x2, HEAP, lsl #32
    // 0xc35ff4: StoreField: r0->field_47 = r2
    //     0xc35ff4: stur            w2, [x0, #0x47]
    // 0xc35ff8: r16 = " textAnchor:"
    //     0xc35ff8: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f58] " textAnchor:"
    //     0xc35ffc: ldr             x16, [x16, #0xf58]
    // 0xc36000: StoreField: r0->field_4b = r16
    //     0xc36000: stur            w16, [x0, #0x4b]
    // 0xc36004: LoadField: r2 = r1->field_47
    //     0xc36004: ldur            w2, [x1, #0x47]
    // 0xc36008: DecompressPointer r2
    //     0xc36008: add             x2, x2, HEAP, lsl #32
    // 0xc3600c: StoreField: r0->field_4f = r2
    //     0xc3600c: stur            w2, [x0, #0x4f]
    // 0xc36010: r16 = " "
    //     0xc36010: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc36014: StoreField: r0->field_53 = r16
    //     0xc36014: stur            w16, [x0, #0x53]
    // 0xc36018: str             x0, [SP]
    // 0xc3601c: r0 = _interpolate()
    //     0xc3601c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc36020: LeaveFrame
    //     0xc36020: mov             SP, fp
    //     0xc36024: ldp             fp, lr, [SP], #0x10
    // 0xc36028: ret
    //     0xc36028: ret             
    // 0xc3602c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3602c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc36030: b               #0xc35f34
  }
  factory _ SvgBrush.fromXml(/* No info */) {
    // ** addr: 0xe7332c, size: 0x4f0
    // 0xe7332c: EnterFrame
    //     0xe7332c: stp             fp, lr, [SP, #-0x10]!
    //     0xe73330: mov             fp, SP
    // 0xe73334: AllocStack(0xc0)
    //     0xe73334: sub             SP, SP, #0xc0
    // 0xe73338: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0xe73338: mov             x0, x3
    //     0xe7333c: stur            x3, [fp, #-0x10]
    //     0xe73340: mov             x3, x5
    //     0xe73344: stur            x2, [fp, #-8]
    //     0xe73348: stur            x5, [fp, #-0x18]
    // 0xe7334c: CheckStackOverflow
    //     0xe7334c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe73350: cmp             SP, x16
    //     0xe73354: b.ls            #0xe73804
    // 0xe73358: mov             x1, x2
    // 0xe7335c: r0 = convertStyle()
    //     0xe7335c: bl              #0xe767ac  ; [package:pdf/src/svg/parser.dart] SvgParser::convertStyle
    // 0xe73360: ldur            x1, [fp, #-8]
    // 0xe73364: r2 = "stroke-dasharray"
    //     0xe73364: add             x2, PP, #0x25, lsl #12  ; [pp+0x25e30] "stroke-dasharray"
    //     0xe73368: ldr             x2, [x2, #0xe30]
    // 0xe7336c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe7336c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe73370: r0 = getAttribute()
    //     0xe73370: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe73374: ldur            x1, [fp, #-8]
    // 0xe73378: r2 = "fill-rule"
    //     0xe73378: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a58] "fill-rule"
    //     0xe7337c: ldr             x2, [x2, #0xa58]
    // 0xe73380: stur            x0, [fp, #-0x20]
    // 0xe73384: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe73384: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe73388: r0 = getAttribute()
    //     0xe73388: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe7338c: ldur            x1, [fp, #-8]
    // 0xe73390: r2 = "stroke-linecap"
    //     0xe73390: add             x2, PP, #0x25, lsl #12  ; [pp+0x25e60] "stroke-linecap"
    //     0xe73394: ldr             x2, [x2, #0xe60]
    // 0xe73398: stur            x0, [fp, #-0x28]
    // 0xe7339c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe7339c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe733a0: r0 = getAttribute()
    //     0xe733a0: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe733a4: ldur            x1, [fp, #-8]
    // 0xe733a8: r2 = "stroke-linejoin"
    //     0xe733a8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25e68] "stroke-linejoin"
    //     0xe733ac: ldr             x2, [x2, #0xe68]
    // 0xe733b0: stur            x0, [fp, #-0x30]
    // 0xe733b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe733b4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe733b8: r0 = getAttribute()
    //     0xe733b8: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe733bc: ldur            x1, [fp, #-8]
    // 0xe733c0: r2 = "mix-blend-mode"
    //     0xe733c0: add             x2, PP, #0x25, lsl #12  ; [pp+0x25ac0] "mix-blend-mode"
    //     0xe733c4: ldr             x2, [x2, #0xac0]
    // 0xe733c8: stur            x0, [fp, #-0x38]
    // 0xe733cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe733cc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe733d0: r0 = getAttribute()
    //     0xe733d0: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe733d4: ldur            x1, [fp, #-8]
    // 0xe733d8: r2 = "opacity"
    //     0xe733d8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25db0] "opacity"
    //     0xe733dc: ldr             x2, [x2, #0xdb0]
    // 0xe733e0: r3 = Null
    //     0xe733e0: mov             x3, NULL
    // 0xe733e4: stur            x0, [fp, #-0x40]
    // 0xe733e8: r0 = getDouble()
    //     0xe733e8: bl              #0xe76710  ; [package:pdf/src/svg/parser.dart] SvgParser::getDouble
    // 0xe733ec: ldur            x2, [fp, #-0x40]
    // 0xe733f0: stur            x0, [fp, #-0x48]
    // 0xe733f4: cmp             w2, NULL
    // 0xe733f8: b.ne            #0xe73404
    // 0xe733fc: r4 = Null
    //     0xe733fc: mov             x4, NULL
    // 0xe73400: b               #0xe73414
    // 0xe73404: r1 = _ConstMap len:16
    //     0xe73404: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ecb8] Map<String, PdfBlendMode>(16)
    //     0xe73408: ldr             x1, [x1, #0xcb8]
    // 0xe7340c: r0 = []()
    //     0xe7340c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xe73410: mov             x4, x0
    // 0xe73414: ldur            x0, [fp, #-0x30]
    // 0xe73418: ldur            x1, [fp, #-8]
    // 0xe7341c: stur            x4, [fp, #-0x40]
    // 0xe73420: r2 = "fill-opacity"
    //     0xe73420: add             x2, PP, #0x25, lsl #12  ; [pp+0x25dd8] "fill-opacity"
    //     0xe73424: ldr             x2, [x2, #0xdd8]
    // 0xe73428: r3 = Null
    //     0xe73428: mov             x3, NULL
    // 0xe7342c: r0 = getDouble()
    //     0xe7342c: bl              #0xe76710  ; [package:pdf/src/svg/parser.dart] SvgParser::getDouble
    // 0xe73430: ldur            x1, [fp, #-8]
    // 0xe73434: r2 = "stroke-opacity"
    //     0xe73434: add             x2, PP, #0x25, lsl #12  ; [pp+0x25e58] "stroke-opacity"
    //     0xe73438: ldr             x2, [x2, #0xe58]
    // 0xe7343c: r3 = Null
    //     0xe7343c: mov             x3, NULL
    // 0xe73440: stur            x0, [fp, #-0x50]
    // 0xe73444: r0 = getDouble()
    //     0xe73444: bl              #0xe76710  ; [package:pdf/src/svg/parser.dart] SvgParser::getDouble
    // 0xe73448: ldur            x2, [fp, #-0x30]
    // 0xe7344c: stur            x0, [fp, #-0x58]
    // 0xe73450: cmp             w2, NULL
    // 0xe73454: b.ne            #0xe73460
    // 0xe73458: r0 = Null
    //     0xe73458: mov             x0, NULL
    // 0xe7345c: b               #0xe7346c
    // 0xe73460: r1 = _ConstMap len:3
    //     0xe73460: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ecc0] Map<String, PdfLineCap>(3)
    //     0xe73464: ldr             x1, [x1, #0xcc0]
    // 0xe73468: r0 = []()
    //     0xe73468: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xe7346c: ldur            x2, [fp, #-0x38]
    // 0xe73470: stur            x0, [fp, #-0x30]
    // 0xe73474: cmp             w2, NULL
    // 0xe73478: b.ne            #0xe73484
    // 0xe7347c: r4 = Null
    //     0xe7347c: mov             x4, NULL
    // 0xe73480: b               #0xe73494
    // 0xe73484: r1 = _ConstMap len:3
    //     0xe73484: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ecc8] Map<String, PdfLineJoin>(3)
    //     0xe73488: ldr             x1, [x1, #0xcc8]
    // 0xe7348c: r0 = []()
    //     0xe7348c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xe73490: mov             x4, x0
    // 0xe73494: ldur            x0, [fp, #-0x28]
    // 0xe73498: ldur            x1, [fp, #-8]
    // 0xe7349c: stur            x4, [fp, #-0x38]
    // 0xe734a0: r2 = "stroke-miterlimit"
    //     0xe734a0: add             x2, PP, #0x25, lsl #12  ; [pp+0x25e70] "stroke-miterlimit"
    //     0xe734a4: ldr             x2, [x2, #0xe70]
    // 0xe734a8: r3 = Null
    //     0xe734a8: mov             x3, NULL
    // 0xe734ac: r0 = getDouble()
    //     0xe734ac: bl              #0xe76710  ; [package:pdf/src/svg/parser.dart] SvgParser::getDouble
    // 0xe734b0: ldur            x1, [fp, #-8]
    // 0xe734b4: r2 = "fill"
    //     0xe734b4: add             x2, PP, #0x25, lsl #12  ; [pp+0x25dd0] "fill"
    //     0xe734b8: ldr             x2, [x2, #0xdd0]
    // 0xe734bc: stur            x0, [fp, #-0x60]
    // 0xe734c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe734c0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe734c4: r0 = getAttribute()
    //     0xe734c4: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe734c8: mov             x2, x0
    // 0xe734cc: ldur            x3, [fp, #-0x18]
    // 0xe734d0: r1 = Null
    //     0xe734d0: mov             x1, NULL
    // 0xe734d4: r0 = SvgColor.fromXml()
    //     0xe734d4: bl              #0xe74098  ; [package:pdf/src/svg/color.dart] SvgColor::SvgColor.fromXml
    // 0xe734d8: mov             x1, x0
    // 0xe734dc: ldur            x0, [fp, #-0x28]
    // 0xe734e0: stur            x1, [fp, #-0x68]
    // 0xe734e4: cmp             w0, NULL
    // 0xe734e8: b.ne            #0xe734f4
    // 0xe734ec: r3 = Null
    //     0xe734ec: mov             x3, NULL
    // 0xe734f0: b               #0xe7351c
    // 0xe734f4: r2 = LoadClassIdInstr(r0)
    //     0xe734f4: ldur            x2, [x0, #-1]
    //     0xe734f8: ubfx            x2, x2, #0xc, #0x14
    // 0xe734fc: r16 = "evenodd"
    //     0xe734fc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25db8] "evenodd"
    //     0xe73500: ldr             x16, [x16, #0xdb8]
    // 0xe73504: stp             x16, x0, [SP]
    // 0xe73508: mov             x0, x2
    // 0xe7350c: mov             lr, x0
    // 0xe73510: ldr             lr, [x21, lr, lsl #3]
    // 0xe73514: blr             lr
    // 0xe73518: mov             x3, x0
    // 0xe7351c: ldur            x0, [fp, #-0x20]
    // 0xe73520: ldur            x1, [fp, #-8]
    // 0xe73524: stur            x3, [fp, #-0x28]
    // 0xe73528: r2 = "stroke"
    //     0xe73528: add             x2, PP, #0x25, lsl #12  ; [pp+0x25e50] "stroke"
    //     0xe7352c: ldr             x2, [x2, #0xe50]
    // 0xe73530: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe73530: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe73534: r0 = getAttribute()
    //     0xe73534: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe73538: mov             x2, x0
    // 0xe7353c: ldur            x3, [fp, #-0x18]
    // 0xe73540: r1 = Null
    //     0xe73540: mov             x1, NULL
    // 0xe73544: r0 = SvgColor.fromXml()
    //     0xe73544: bl              #0xe74098  ; [package:pdf/src/svg/color.dart] SvgColor::SvgColor.fromXml
    // 0xe73548: ldur            x1, [fp, #-8]
    // 0xe7354c: ldur            x3, [fp, #-0x10]
    // 0xe73550: r2 = "stroke-width"
    //     0xe73550: add             x2, PP, #0x25, lsl #12  ; [pp+0x25e78] "stroke-width"
    //     0xe73554: ldr             x2, [x2, #0xe78]
    // 0xe73558: stur            x0, [fp, #-0x70]
    // 0xe7355c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe7355c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe73560: r0 = getNumeric()
    //     0xe73560: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe73564: mov             x2, x0
    // 0xe73568: ldur            x1, [fp, #-0x20]
    // 0xe7356c: stur            x2, [fp, #-0x78]
    // 0xe73570: cmp             w1, NULL
    // 0xe73574: b.ne            #0xe73580
    // 0xe73578: r0 = Null
    //     0xe73578: mov             x0, NULL
    // 0xe7357c: b               #0xe735fc
    // 0xe73580: r0 = LoadClassIdInstr(r1)
    //     0xe73580: ldur            x0, [x1, #-1]
    //     0xe73584: ubfx            x0, x0, #0xc, #0x14
    // 0xe73588: r16 = "none"
    //     0xe73588: add             x16, PP, #0x25, lsl #12  ; [pp+0x25b58] "none"
    //     0xe7358c: ldr             x16, [x16, #0xb58]
    // 0xe73590: stp             x16, x1, [SP]
    // 0xe73594: mov             lr, x0
    // 0xe73598: ldr             lr, [x21, lr, lsl #3]
    // 0xe7359c: blr             lr
    // 0xe735a0: tbnz            w0, #4, #0xe735b4
    // 0xe735a4: r1 = <double>
    //     0xe735a4: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe735a8: r2 = 0
    //     0xe735a8: movz            x2, #0
    // 0xe735ac: r0 = _GrowableList()
    //     0xe735ac: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe735b0: b               #0xe735fc
    // 0xe735b4: ldur            x1, [fp, #-0x20]
    // 0xe735b8: ldur            x2, [fp, #-0x10]
    // 0xe735bc: r0 = splitNumeric()
    //     0xe735bc: bl              #0xe73f7c  ; [package:pdf/src/svg/parser.dart] SvgParser::splitNumeric
    // 0xe735c0: r1 = Function '<anonymous closure>': static.
    //     0xe735c0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ecd0] AnonymousClosure: static (0xe76dcc), in [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml (0xe7332c)
    //     0xe735c4: ldr             x1, [x1, #0xcd0]
    // 0xe735c8: r2 = Null
    //     0xe735c8: mov             x2, NULL
    // 0xe735cc: stur            x0, [fp, #-0x20]
    // 0xe735d0: r0 = AllocateClosure()
    //     0xe735d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe735d4: r16 = <double>
    //     0xe735d4: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe735d8: ldur            lr, [fp, #-0x20]
    // 0xe735dc: stp             lr, x16, [SP, #8]
    // 0xe735e0: str             x0, [SP]
    // 0xe735e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe735e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe735e8: r0 = map()
    //     0xe735e8: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xe735ec: LoadField: r1 = r0->field_7
    //     0xe735ec: ldur            w1, [x0, #7]
    // 0xe735f0: DecompressPointer r1
    //     0xe735f0: add             x1, x1, HEAP, lsl #32
    // 0xe735f4: mov             x2, x0
    // 0xe735f8: r0 = _GrowableList.of()
    //     0xe735f8: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe735fc: ldur            x1, [fp, #-8]
    // 0xe73600: ldur            x3, [fp, #-0x10]
    // 0xe73604: stur            x0, [fp, #-0x20]
    // 0xe73608: r2 = "stroke-dashoffset"
    //     0xe73608: add             x2, PP, #0x25, lsl #12  ; [pp+0x25e18] "stroke-dashoffset"
    //     0xe7360c: ldr             x2, [x2, #0xe18]
    // 0xe73610: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe73610: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe73614: r0 = getNumeric()
    //     0xe73614: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe73618: cmp             w0, NULL
    // 0xe7361c: b.ne            #0xe73628
    // 0xe73620: r19 = Null
    //     0xe73620: mov             x19, NULL
    // 0xe73624: b               #0xe7365c
    // 0xe73628: mov             x1, x0
    // 0xe7362c: r0 = sizeValue()
    //     0xe7362c: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe73630: r0 = inline_Allocate_Double()
    //     0xe73630: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe73634: add             x0, x0, #0x10
    //     0xe73638: cmp             x1, x0
    //     0xe7363c: b.ls            #0xe7380c
    //     0xe73640: str             x0, [THR, #0x50]  ; THR::top
    //     0xe73644: sub             x0, x0, #0xf
    //     0xe73648: movz            x1, #0xe15c
    //     0xe7364c: movk            x1, #0x3, lsl #16
    //     0xe73650: stur            x1, [x0, #-1]
    // 0xe73654: StoreField: r0->field_7 = d0
    //     0xe73654: stur            d0, [x0, #7]
    // 0xe73658: mov             x19, x0
    // 0xe7365c: ldur            x14, [fp, #-0x48]
    // 0xe73660: ldur            x13, [fp, #-0x40]
    // 0xe73664: ldur            x12, [fp, #-0x50]
    // 0xe73668: ldur            x11, [fp, #-0x58]
    // 0xe7366c: ldur            x10, [fp, #-0x30]
    // 0xe73670: ldur            x9, [fp, #-0x38]
    // 0xe73674: ldur            x8, [fp, #-0x60]
    // 0xe73678: ldur            x7, [fp, #-0x68]
    // 0xe7367c: ldur            x6, [fp, #-0x28]
    // 0xe73680: ldur            x5, [fp, #-0x70]
    // 0xe73684: ldur            x4, [fp, #-0x78]
    // 0xe73688: ldur            x0, [fp, #-0x20]
    // 0xe7368c: ldur            x1, [fp, #-8]
    // 0xe73690: ldur            x3, [fp, #-0x10]
    // 0xe73694: stur            x19, [fp, #-0x80]
    // 0xe73698: r2 = "font-size"
    //     0xe73698: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a68] "font-size"
    //     0xe7369c: ldr             x2, [x2, #0xa68]
    // 0xe736a0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe736a0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe736a4: r0 = getNumeric()
    //     0xe736a4: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe736a8: ldur            x1, [fp, #-8]
    // 0xe736ac: r2 = "font-family"
    //     0xe736ac: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a60] "font-family"
    //     0xe736b0: ldr             x2, [x2, #0xa60]
    // 0xe736b4: stur            x0, [fp, #-0x88]
    // 0xe736b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe736b8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe736bc: r0 = getAttribute()
    //     0xe736bc: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe736c0: ldur            x1, [fp, #-8]
    // 0xe736c4: r2 = "font-style"
    //     0xe736c4: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a78] "font-style"
    //     0xe736c8: ldr             x2, [x2, #0xa78]
    // 0xe736cc: stur            x0, [fp, #-0x90]
    // 0xe736d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe736d0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe736d4: r0 = getAttribute()
    //     0xe736d4: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe736d8: ldur            x1, [fp, #-8]
    // 0xe736dc: r2 = "font-weight"
    //     0xe736dc: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a70] "font-weight"
    //     0xe736e0: ldr             x2, [x2, #0xa70]
    // 0xe736e4: stur            x0, [fp, #-0x98]
    // 0xe736e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe736e8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe736ec: r0 = getAttribute()
    //     0xe736ec: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe736f0: ldur            x1, [fp, #-8]
    // 0xe736f4: r2 = "text-anchor"
    //     0xe736f4: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a80] "text-anchor"
    //     0xe736f8: ldr             x2, [x2, #0xa80]
    // 0xe736fc: stur            x0, [fp, #-0xa0]
    // 0xe73700: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe73700: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe73704: r0 = getAttribute()
    //     0xe73704: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe73708: mov             x2, x0
    // 0xe7370c: r1 = _ConstMap len:3
    //     0xe7370c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ecd8] Map<String, SvgTextAnchor>(3)
    //     0xe73710: ldr             x1, [x1, #0xcd8]
    // 0xe73714: r0 = []()
    //     0xe73714: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xe73718: stur            x0, [fp, #-0xa8]
    // 0xe7371c: r0 = SvgBrush()
    //     0xe7371c: bl              #0xe73f70  ; AllocateSvgBrushStub -> SvgBrush (size=0x54)
    // 0xe73720: mov             x1, x0
    // 0xe73724: ldur            x0, [fp, #-0x48]
    // 0xe73728: StoreField: r1->field_7 = r0
    //     0xe73728: stur            w0, [x1, #7]
    // 0xe7372c: ldur            x0, [fp, #-0x68]
    // 0xe73730: StoreField: r1->field_b = r0
    //     0xe73730: stur            w0, [x1, #0xb]
    // 0xe73734: ldur            x0, [fp, #-0x28]
    // 0xe73738: StoreField: r1->field_f = r0
    //     0xe73738: stur            w0, [x1, #0xf]
    // 0xe7373c: ldur            x0, [fp, #-0x50]
    // 0xe73740: StoreField: r1->field_13 = r0
    //     0xe73740: stur            w0, [x1, #0x13]
    // 0xe73744: ldur            x0, [fp, #-0x70]
    // 0xe73748: ArrayStore: r1[0] = r0  ; List_4
    //     0xe73748: stur            w0, [x1, #0x17]
    // 0xe7374c: ldur            x0, [fp, #-0x58]
    // 0xe73750: StoreField: r1->field_1b = r0
    //     0xe73750: stur            w0, [x1, #0x1b]
    // 0xe73754: ldur            x0, [fp, #-0x78]
    // 0xe73758: StoreField: r1->field_1f = r0
    //     0xe73758: stur            w0, [x1, #0x1f]
    // 0xe7375c: ldur            x0, [fp, #-0x20]
    // 0xe73760: StoreField: r1->field_23 = r0
    //     0xe73760: stur            w0, [x1, #0x23]
    // 0xe73764: ldur            x0, [fp, #-0x80]
    // 0xe73768: StoreField: r1->field_27 = r0
    //     0xe73768: stur            w0, [x1, #0x27]
    // 0xe7376c: ldur            x0, [fp, #-0x30]
    // 0xe73770: StoreField: r1->field_2b = r0
    //     0xe73770: stur            w0, [x1, #0x2b]
    // 0xe73774: ldur            x0, [fp, #-0x38]
    // 0xe73778: StoreField: r1->field_2f = r0
    //     0xe73778: stur            w0, [x1, #0x2f]
    // 0xe7377c: ldur            x0, [fp, #-0x60]
    // 0xe73780: StoreField: r1->field_33 = r0
    //     0xe73780: stur            w0, [x1, #0x33]
    // 0xe73784: ldur            x0, [fp, #-0x90]
    // 0xe73788: StoreField: r1->field_3b = r0
    //     0xe73788: stur            w0, [x1, #0x3b]
    // 0xe7378c: ldur            x0, [fp, #-0x88]
    // 0xe73790: StoreField: r1->field_37 = r0
    //     0xe73790: stur            w0, [x1, #0x37]
    // 0xe73794: ldur            x0, [fp, #-0x98]
    // 0xe73798: StoreField: r1->field_3f = r0
    //     0xe73798: stur            w0, [x1, #0x3f]
    // 0xe7379c: ldur            x0, [fp, #-0xa0]
    // 0xe737a0: StoreField: r1->field_43 = r0
    //     0xe737a0: stur            w0, [x1, #0x43]
    // 0xe737a4: ldur            x0, [fp, #-0xa8]
    // 0xe737a8: StoreField: r1->field_47 = r0
    //     0xe737a8: stur            w0, [x1, #0x47]
    // 0xe737ac: ldur            x0, [fp, #-0x40]
    // 0xe737b0: StoreField: r1->field_4b = r0
    //     0xe737b0: stur            w0, [x1, #0x4b]
    // 0xe737b4: mov             x2, x1
    // 0xe737b8: ldur            x1, [fp, #-0x10]
    // 0xe737bc: r0 = merge()
    //     0xe737bc: bl              #0xe73b88  ; [package:pdf/src/svg/brush.dart] SvgBrush::merge
    // 0xe737c0: ldur            x1, [fp, #-8]
    // 0xe737c4: ldur            x2, [fp, #-0x18]
    // 0xe737c8: mov             x3, x0
    // 0xe737cc: stur            x0, [fp, #-8]
    // 0xe737d0: r0 = fromXml()
    //     0xe737d0: bl              #0xe739ac  ; [package:pdf/src/svg/mask_path.dart] SvgMaskPath::fromXml
    // 0xe737d4: cmp             w0, NULL
    // 0xe737d8: b.eq            #0xe737f4
    // 0xe737dc: ldur            x1, [fp, #-8]
    // 0xe737e0: mov             x2, x0
    // 0xe737e4: r0 = copyWith()
    //     0xe737e4: bl              #0xe7381c  ; [package:pdf/src/svg/brush.dart] SvgBrush::copyWith
    // 0xe737e8: LeaveFrame
    //     0xe737e8: mov             SP, fp
    //     0xe737ec: ldp             fp, lr, [SP], #0x10
    // 0xe737f0: ret
    //     0xe737f0: ret             
    // 0xe737f4: ldur            x0, [fp, #-8]
    // 0xe737f8: LeaveFrame
    //     0xe737f8: mov             SP, fp
    //     0xe737fc: ldp             fp, lr, [SP], #0x10
    // 0xe73800: ret
    //     0xe73800: ret             
    // 0xe73804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe73804: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe73808: b               #0xe73358
    // 0xe7380c: SaveReg d0
    //     0xe7380c: str             q0, [SP, #-0x10]!
    // 0xe73810: r0 = AllocateDouble()
    //     0xe73810: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe73814: RestoreReg d0
    //     0xe73814: ldr             q0, [SP], #0x10
    // 0xe73818: b               #0xe73654
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xe7381c, size: 0x190
    // 0xe7381c: EnterFrame
    //     0xe7381c: stp             fp, lr, [SP, #-0x10]!
    //     0xe73820: mov             fp, SP
    // 0xe73824: AllocStack(0x98)
    //     0xe73824: sub             SP, SP, #0x98
    // 0xe73828: SetupParameters(dynamic _ /* r2 => r2, fp-0x98 */)
    //     0xe73828: stur            x2, [fp, #-0x98]
    // 0xe7382c: LoadField: r0 = r1->field_7
    //     0xe7382c: ldur            w0, [x1, #7]
    // 0xe73830: DecompressPointer r0
    //     0xe73830: add             x0, x0, HEAP, lsl #32
    // 0xe73834: stur            x0, [fp, #-0x90]
    // 0xe73838: LoadField: r3 = r1->field_b
    //     0xe73838: ldur            w3, [x1, #0xb]
    // 0xe7383c: DecompressPointer r3
    //     0xe7383c: add             x3, x3, HEAP, lsl #32
    // 0xe73840: stur            x3, [fp, #-0x88]
    // 0xe73844: LoadField: r4 = r1->field_f
    //     0xe73844: ldur            w4, [x1, #0xf]
    // 0xe73848: DecompressPointer r4
    //     0xe73848: add             x4, x4, HEAP, lsl #32
    // 0xe7384c: stur            x4, [fp, #-0x80]
    // 0xe73850: LoadField: r5 = r1->field_13
    //     0xe73850: ldur            w5, [x1, #0x13]
    // 0xe73854: DecompressPointer r5
    //     0xe73854: add             x5, x5, HEAP, lsl #32
    // 0xe73858: stur            x5, [fp, #-0x78]
    // 0xe7385c: ArrayLoad: r6 = r1[0]  ; List_4
    //     0xe7385c: ldur            w6, [x1, #0x17]
    // 0xe73860: DecompressPointer r6
    //     0xe73860: add             x6, x6, HEAP, lsl #32
    // 0xe73864: stur            x6, [fp, #-0x70]
    // 0xe73868: LoadField: r7 = r1->field_1b
    //     0xe73868: ldur            w7, [x1, #0x1b]
    // 0xe7386c: DecompressPointer r7
    //     0xe7386c: add             x7, x7, HEAP, lsl #32
    // 0xe73870: stur            x7, [fp, #-0x68]
    // 0xe73874: LoadField: r8 = r1->field_1f
    //     0xe73874: ldur            w8, [x1, #0x1f]
    // 0xe73878: DecompressPointer r8
    //     0xe73878: add             x8, x8, HEAP, lsl #32
    // 0xe7387c: stur            x8, [fp, #-0x60]
    // 0xe73880: LoadField: r9 = r1->field_23
    //     0xe73880: ldur            w9, [x1, #0x23]
    // 0xe73884: DecompressPointer r9
    //     0xe73884: add             x9, x9, HEAP, lsl #32
    // 0xe73888: stur            x9, [fp, #-0x58]
    // 0xe7388c: LoadField: r10 = r1->field_27
    //     0xe7388c: ldur            w10, [x1, #0x27]
    // 0xe73890: DecompressPointer r10
    //     0xe73890: add             x10, x10, HEAP, lsl #32
    // 0xe73894: stur            x10, [fp, #-0x50]
    // 0xe73898: LoadField: r11 = r1->field_2b
    //     0xe73898: ldur            w11, [x1, #0x2b]
    // 0xe7389c: DecompressPointer r11
    //     0xe7389c: add             x11, x11, HEAP, lsl #32
    // 0xe738a0: stur            x11, [fp, #-0x48]
    // 0xe738a4: LoadField: r12 = r1->field_2f
    //     0xe738a4: ldur            w12, [x1, #0x2f]
    // 0xe738a8: DecompressPointer r12
    //     0xe738a8: add             x12, x12, HEAP, lsl #32
    // 0xe738ac: stur            x12, [fp, #-0x40]
    // 0xe738b0: LoadField: r13 = r1->field_33
    //     0xe738b0: ldur            w13, [x1, #0x33]
    // 0xe738b4: DecompressPointer r13
    //     0xe738b4: add             x13, x13, HEAP, lsl #32
    // 0xe738b8: stur            x13, [fp, #-0x38]
    // 0xe738bc: LoadField: r14 = r1->field_37
    //     0xe738bc: ldur            w14, [x1, #0x37]
    // 0xe738c0: DecompressPointer r14
    //     0xe738c0: add             x14, x14, HEAP, lsl #32
    // 0xe738c4: stur            x14, [fp, #-0x30]
    // 0xe738c8: LoadField: r19 = r1->field_3b
    //     0xe738c8: ldur            w19, [x1, #0x3b]
    // 0xe738cc: DecompressPointer r19
    //     0xe738cc: add             x19, x19, HEAP, lsl #32
    // 0xe738d0: stur            x19, [fp, #-0x28]
    // 0xe738d4: LoadField: r20 = r1->field_3f
    //     0xe738d4: ldur            w20, [x1, #0x3f]
    // 0xe738d8: DecompressPointer r20
    //     0xe738d8: add             x20, x20, HEAP, lsl #32
    // 0xe738dc: stur            x20, [fp, #-0x20]
    // 0xe738e0: LoadField: r23 = r1->field_43
    //     0xe738e0: ldur            w23, [x1, #0x43]
    // 0xe738e4: DecompressPointer r23
    //     0xe738e4: add             x23, x23, HEAP, lsl #32
    // 0xe738e8: stur            x23, [fp, #-0x18]
    // 0xe738ec: LoadField: r24 = r1->field_47
    //     0xe738ec: ldur            w24, [x1, #0x47]
    // 0xe738f0: DecompressPointer r24
    //     0xe738f0: add             x24, x24, HEAP, lsl #32
    // 0xe738f4: stur            x24, [fp, #-0x10]
    // 0xe738f8: LoadField: r25 = r1->field_4b
    //     0xe738f8: ldur            w25, [x1, #0x4b]
    // 0xe738fc: DecompressPointer r25
    //     0xe738fc: add             x25, x25, HEAP, lsl #32
    // 0xe73900: stur            x25, [fp, #-8]
    // 0xe73904: r0 = SvgBrush()
    //     0xe73904: bl              #0xe73f70  ; AllocateSvgBrushStub -> SvgBrush (size=0x54)
    // 0xe73908: ldur            x1, [fp, #-0x90]
    // 0xe7390c: StoreField: r0->field_7 = r1
    //     0xe7390c: stur            w1, [x0, #7]
    // 0xe73910: ldur            x1, [fp, #-0x88]
    // 0xe73914: StoreField: r0->field_b = r1
    //     0xe73914: stur            w1, [x0, #0xb]
    // 0xe73918: ldur            x1, [fp, #-0x80]
    // 0xe7391c: StoreField: r0->field_f = r1
    //     0xe7391c: stur            w1, [x0, #0xf]
    // 0xe73920: ldur            x1, [fp, #-0x78]
    // 0xe73924: StoreField: r0->field_13 = r1
    //     0xe73924: stur            w1, [x0, #0x13]
    // 0xe73928: ldur            x1, [fp, #-0x70]
    // 0xe7392c: ArrayStore: r0[0] = r1  ; List_4
    //     0xe7392c: stur            w1, [x0, #0x17]
    // 0xe73930: ldur            x1, [fp, #-0x68]
    // 0xe73934: StoreField: r0->field_1b = r1
    //     0xe73934: stur            w1, [x0, #0x1b]
    // 0xe73938: ldur            x1, [fp, #-0x60]
    // 0xe7393c: StoreField: r0->field_1f = r1
    //     0xe7393c: stur            w1, [x0, #0x1f]
    // 0xe73940: ldur            x1, [fp, #-0x58]
    // 0xe73944: StoreField: r0->field_23 = r1
    //     0xe73944: stur            w1, [x0, #0x23]
    // 0xe73948: ldur            x1, [fp, #-0x50]
    // 0xe7394c: StoreField: r0->field_27 = r1
    //     0xe7394c: stur            w1, [x0, #0x27]
    // 0xe73950: ldur            x1, [fp, #-0x48]
    // 0xe73954: StoreField: r0->field_2b = r1
    //     0xe73954: stur            w1, [x0, #0x2b]
    // 0xe73958: ldur            x1, [fp, #-0x40]
    // 0xe7395c: StoreField: r0->field_2f = r1
    //     0xe7395c: stur            w1, [x0, #0x2f]
    // 0xe73960: ldur            x1, [fp, #-0x38]
    // 0xe73964: StoreField: r0->field_33 = r1
    //     0xe73964: stur            w1, [x0, #0x33]
    // 0xe73968: ldur            x1, [fp, #-0x28]
    // 0xe7396c: StoreField: r0->field_3b = r1
    //     0xe7396c: stur            w1, [x0, #0x3b]
    // 0xe73970: ldur            x1, [fp, #-0x30]
    // 0xe73974: StoreField: r0->field_37 = r1
    //     0xe73974: stur            w1, [x0, #0x37]
    // 0xe73978: ldur            x1, [fp, #-0x20]
    // 0xe7397c: StoreField: r0->field_3f = r1
    //     0xe7397c: stur            w1, [x0, #0x3f]
    // 0xe73980: ldur            x1, [fp, #-0x18]
    // 0xe73984: StoreField: r0->field_43 = r1
    //     0xe73984: stur            w1, [x0, #0x43]
    // 0xe73988: ldur            x1, [fp, #-0x10]
    // 0xe7398c: StoreField: r0->field_47 = r1
    //     0xe7398c: stur            w1, [x0, #0x47]
    // 0xe73990: ldur            x1, [fp, #-8]
    // 0xe73994: StoreField: r0->field_4b = r1
    //     0xe73994: stur            w1, [x0, #0x4b]
    // 0xe73998: ldur            x1, [fp, #-0x98]
    // 0xe7399c: StoreField: r0->field_4f = r1
    //     0xe7399c: stur            w1, [x0, #0x4f]
    // 0xe739a0: LeaveFrame
    //     0xe739a0: mov             SP, fp
    //     0xe739a4: ldp             fp, lr, [SP], #0x10
    // 0xe739a8: ret
    //     0xe739a8: ret             
  }
  _ merge(/* No info */) {
    // ** addr: 0xe73b88, size: 0x394
    // 0xe73b88: EnterFrame
    //     0xe73b88: stp             fp, lr, [SP, #-0x10]!
    //     0xe73b8c: mov             fp, SP
    // 0xe73b90: AllocStack(0xa0)
    //     0xe73b90: sub             SP, SP, #0xa0
    // 0xe73b94: SetupParameters(SvgBrush this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe73b94: mov             x3, x1
    //     0xe73b98: mov             x0, x2
    //     0xe73b9c: stur            x1, [fp, #-8]
    //     0xe73ba0: stur            x2, [fp, #-0x10]
    // 0xe73ba4: CheckStackOverflow
    //     0xe73ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe73ba8: cmp             SP, x16
    //     0xe73bac: b.ls            #0xe73ebc
    // 0xe73bb0: LoadField: r2 = r0->field_b
    //     0xe73bb0: ldur            w2, [x0, #0xb]
    // 0xe73bb4: DecompressPointer r2
    //     0xe73bb4: add             x2, x2, HEAP, lsl #32
    // 0xe73bb8: LoadField: r1 = r2->field_f
    //     0xe73bb8: ldur            w1, [x2, #0xf]
    // 0xe73bbc: DecompressPointer r1
    //     0xe73bbc: add             x1, x1, HEAP, lsl #32
    // 0xe73bc0: tbnz            w1, #4, #0xe73bd8
    // 0xe73bc4: LoadField: r1 = r3->field_b
    //     0xe73bc4: ldur            w1, [x3, #0xb]
    // 0xe73bc8: DecompressPointer r1
    //     0xe73bc8: add             x1, x1, HEAP, lsl #32
    // 0xe73bcc: r0 = merge()
    //     0xe73bcc: bl              #0xe73f1c  ; [package:pdf/src/svg/color.dart] SvgColor::merge
    // 0xe73bd0: mov             x3, x0
    // 0xe73bd4: b               #0xe73bdc
    // 0xe73bd8: mov             x3, x2
    // 0xe73bdc: ldur            x0, [fp, #-0x10]
    // 0xe73be0: stur            x3, [fp, #-0x18]
    // 0xe73be4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xe73be4: ldur            w2, [x0, #0x17]
    // 0xe73be8: DecompressPointer r2
    //     0xe73be8: add             x2, x2, HEAP, lsl #32
    // 0xe73bec: LoadField: r1 = r2->field_f
    //     0xe73bec: ldur            w1, [x2, #0xf]
    // 0xe73bf0: DecompressPointer r1
    //     0xe73bf0: add             x1, x1, HEAP, lsl #32
    // 0xe73bf4: tbnz            w1, #4, #0xe73c10
    // 0xe73bf8: ldur            x4, [fp, #-8]
    // 0xe73bfc: ArrayLoad: r1 = r4[0]  ; List_4
    //     0xe73bfc: ldur            w1, [x4, #0x17]
    // 0xe73c00: DecompressPointer r1
    //     0xe73c00: add             x1, x1, HEAP, lsl #32
    // 0xe73c04: r0 = merge()
    //     0xe73c04: bl              #0xe73f1c  ; [package:pdf/src/svg/color.dart] SvgColor::merge
    // 0xe73c08: mov             x1, x0
    // 0xe73c0c: b               #0xe73c14
    // 0xe73c10: mov             x1, x2
    // 0xe73c14: ldur            x0, [fp, #-0x10]
    // 0xe73c18: stur            x1, [fp, #-0xa0]
    // 0xe73c1c: LoadField: r2 = r0->field_7
    //     0xe73c1c: ldur            w2, [x0, #7]
    // 0xe73c20: DecompressPointer r2
    //     0xe73c20: add             x2, x2, HEAP, lsl #32
    // 0xe73c24: cmp             w2, NULL
    // 0xe73c28: b.ne            #0xe73c34
    // 0xe73c2c: d0 = 1.000000
    //     0xe73c2c: fmov            d0, #1.00000000
    // 0xe73c30: b               #0xe73c38
    // 0xe73c34: LoadField: d0 = r2->field_7
    //     0xe73c34: ldur            d0, [x2, #7]
    // 0xe73c38: LoadField: r2 = r0->field_4b
    //     0xe73c38: ldur            w2, [x0, #0x4b]
    // 0xe73c3c: DecompressPointer r2
    //     0xe73c3c: add             x2, x2, HEAP, lsl #32
    // 0xe73c40: stur            x2, [fp, #-0x98]
    // 0xe73c44: LoadField: r3 = r0->field_13
    //     0xe73c44: ldur            w3, [x0, #0x13]
    // 0xe73c48: DecompressPointer r3
    //     0xe73c48: add             x3, x3, HEAP, lsl #32
    // 0xe73c4c: cmp             w3, NULL
    // 0xe73c50: b.ne            #0xe73c64
    // 0xe73c54: ldur            x4, [fp, #-8]
    // 0xe73c58: LoadField: r3 = r4->field_13
    //     0xe73c58: ldur            w3, [x4, #0x13]
    // 0xe73c5c: DecompressPointer r3
    //     0xe73c5c: add             x3, x3, HEAP, lsl #32
    // 0xe73c60: b               #0xe73c68
    // 0xe73c64: ldur            x4, [fp, #-8]
    // 0xe73c68: stur            x3, [fp, #-0x90]
    // 0xe73c6c: LoadField: r5 = r0->field_1b
    //     0xe73c6c: ldur            w5, [x0, #0x1b]
    // 0xe73c70: DecompressPointer r5
    //     0xe73c70: add             x5, x5, HEAP, lsl #32
    // 0xe73c74: cmp             w5, NULL
    // 0xe73c78: b.ne            #0xe73c84
    // 0xe73c7c: LoadField: r5 = r4->field_1b
    //     0xe73c7c: ldur            w5, [x4, #0x1b]
    // 0xe73c80: DecompressPointer r5
    //     0xe73c80: add             x5, x5, HEAP, lsl #32
    // 0xe73c84: stur            x5, [fp, #-0x88]
    // 0xe73c88: LoadField: r6 = r0->field_f
    //     0xe73c88: ldur            w6, [x0, #0xf]
    // 0xe73c8c: DecompressPointer r6
    //     0xe73c8c: add             x6, x6, HEAP, lsl #32
    // 0xe73c90: cmp             w6, NULL
    // 0xe73c94: b.ne            #0xe73ca0
    // 0xe73c98: LoadField: r6 = r4->field_f
    //     0xe73c98: ldur            w6, [x4, #0xf]
    // 0xe73c9c: DecompressPointer r6
    //     0xe73c9c: add             x6, x6, HEAP, lsl #32
    // 0xe73ca0: stur            x6, [fp, #-0x80]
    // 0xe73ca4: LoadField: r7 = r0->field_1f
    //     0xe73ca4: ldur            w7, [x0, #0x1f]
    // 0xe73ca8: DecompressPointer r7
    //     0xe73ca8: add             x7, x7, HEAP, lsl #32
    // 0xe73cac: cmp             w7, NULL
    // 0xe73cb0: b.ne            #0xe73cbc
    // 0xe73cb4: LoadField: r7 = r4->field_1f
    //     0xe73cb4: ldur            w7, [x4, #0x1f]
    // 0xe73cb8: DecompressPointer r7
    //     0xe73cb8: add             x7, x7, HEAP, lsl #32
    // 0xe73cbc: stur            x7, [fp, #-0x78]
    // 0xe73cc0: LoadField: r8 = r0->field_23
    //     0xe73cc0: ldur            w8, [x0, #0x23]
    // 0xe73cc4: DecompressPointer r8
    //     0xe73cc4: add             x8, x8, HEAP, lsl #32
    // 0xe73cc8: cmp             w8, NULL
    // 0xe73ccc: b.ne            #0xe73cd8
    // 0xe73cd0: LoadField: r8 = r4->field_23
    //     0xe73cd0: ldur            w8, [x4, #0x23]
    // 0xe73cd4: DecompressPointer r8
    //     0xe73cd4: add             x8, x8, HEAP, lsl #32
    // 0xe73cd8: stur            x8, [fp, #-0x70]
    // 0xe73cdc: LoadField: r9 = r0->field_27
    //     0xe73cdc: ldur            w9, [x0, #0x27]
    // 0xe73ce0: DecompressPointer r9
    //     0xe73ce0: add             x9, x9, HEAP, lsl #32
    // 0xe73ce4: cmp             w9, NULL
    // 0xe73ce8: b.ne            #0xe73cf4
    // 0xe73cec: LoadField: r9 = r4->field_27
    //     0xe73cec: ldur            w9, [x4, #0x27]
    // 0xe73cf0: DecompressPointer r9
    //     0xe73cf0: add             x9, x9, HEAP, lsl #32
    // 0xe73cf4: stur            x9, [fp, #-0x68]
    // 0xe73cf8: LoadField: r10 = r0->field_37
    //     0xe73cf8: ldur            w10, [x0, #0x37]
    // 0xe73cfc: DecompressPointer r10
    //     0xe73cfc: add             x10, x10, HEAP, lsl #32
    // 0xe73d00: cmp             w10, NULL
    // 0xe73d04: b.ne            #0xe73d10
    // 0xe73d08: LoadField: r10 = r4->field_37
    //     0xe73d08: ldur            w10, [x4, #0x37]
    // 0xe73d0c: DecompressPointer r10
    //     0xe73d0c: add             x10, x10, HEAP, lsl #32
    // 0xe73d10: stur            x10, [fp, #-0x60]
    // 0xe73d14: LoadField: r11 = r0->field_3b
    //     0xe73d14: ldur            w11, [x0, #0x3b]
    // 0xe73d18: DecompressPointer r11
    //     0xe73d18: add             x11, x11, HEAP, lsl #32
    // 0xe73d1c: cmp             w11, NULL
    // 0xe73d20: b.ne            #0xe73d2c
    // 0xe73d24: LoadField: r11 = r4->field_3b
    //     0xe73d24: ldur            w11, [x4, #0x3b]
    // 0xe73d28: DecompressPointer r11
    //     0xe73d28: add             x11, x11, HEAP, lsl #32
    // 0xe73d2c: stur            x11, [fp, #-0x58]
    // 0xe73d30: LoadField: r12 = r0->field_3f
    //     0xe73d30: ldur            w12, [x0, #0x3f]
    // 0xe73d34: DecompressPointer r12
    //     0xe73d34: add             x12, x12, HEAP, lsl #32
    // 0xe73d38: cmp             w12, NULL
    // 0xe73d3c: b.ne            #0xe73d48
    // 0xe73d40: LoadField: r12 = r4->field_3f
    //     0xe73d40: ldur            w12, [x4, #0x3f]
    // 0xe73d44: DecompressPointer r12
    //     0xe73d44: add             x12, x12, HEAP, lsl #32
    // 0xe73d48: stur            x12, [fp, #-0x50]
    // 0xe73d4c: LoadField: r13 = r0->field_43
    //     0xe73d4c: ldur            w13, [x0, #0x43]
    // 0xe73d50: DecompressPointer r13
    //     0xe73d50: add             x13, x13, HEAP, lsl #32
    // 0xe73d54: cmp             w13, NULL
    // 0xe73d58: b.ne            #0xe73d64
    // 0xe73d5c: LoadField: r13 = r4->field_43
    //     0xe73d5c: ldur            w13, [x4, #0x43]
    // 0xe73d60: DecompressPointer r13
    //     0xe73d60: add             x13, x13, HEAP, lsl #32
    // 0xe73d64: stur            x13, [fp, #-0x48]
    // 0xe73d68: LoadField: r14 = r0->field_47
    //     0xe73d68: ldur            w14, [x0, #0x47]
    // 0xe73d6c: DecompressPointer r14
    //     0xe73d6c: add             x14, x14, HEAP, lsl #32
    // 0xe73d70: cmp             w14, NULL
    // 0xe73d74: b.ne            #0xe73d80
    // 0xe73d78: LoadField: r14 = r4->field_47
    //     0xe73d78: ldur            w14, [x4, #0x47]
    // 0xe73d7c: DecompressPointer r14
    //     0xe73d7c: add             x14, x14, HEAP, lsl #32
    // 0xe73d80: stur            x14, [fp, #-0x40]
    // 0xe73d84: LoadField: r19 = r0->field_2b
    //     0xe73d84: ldur            w19, [x0, #0x2b]
    // 0xe73d88: DecompressPointer r19
    //     0xe73d88: add             x19, x19, HEAP, lsl #32
    // 0xe73d8c: cmp             w19, NULL
    // 0xe73d90: b.ne            #0xe73d9c
    // 0xe73d94: LoadField: r19 = r4->field_2b
    //     0xe73d94: ldur            w19, [x4, #0x2b]
    // 0xe73d98: DecompressPointer r19
    //     0xe73d98: add             x19, x19, HEAP, lsl #32
    // 0xe73d9c: stur            x19, [fp, #-0x38]
    // 0xe73da0: LoadField: r20 = r0->field_2f
    //     0xe73da0: ldur            w20, [x0, #0x2f]
    // 0xe73da4: DecompressPointer r20
    //     0xe73da4: add             x20, x20, HEAP, lsl #32
    // 0xe73da8: cmp             w20, NULL
    // 0xe73dac: b.ne            #0xe73db8
    // 0xe73db0: LoadField: r20 = r4->field_2f
    //     0xe73db0: ldur            w20, [x4, #0x2f]
    // 0xe73db4: DecompressPointer r20
    //     0xe73db4: add             x20, x20, HEAP, lsl #32
    // 0xe73db8: stur            x20, [fp, #-0x30]
    // 0xe73dbc: LoadField: r23 = r0->field_33
    //     0xe73dbc: ldur            w23, [x0, #0x33]
    // 0xe73dc0: DecompressPointer r23
    //     0xe73dc0: add             x23, x23, HEAP, lsl #32
    // 0xe73dc4: cmp             w23, NULL
    // 0xe73dc8: b.ne            #0xe73dd4
    // 0xe73dcc: LoadField: r23 = r4->field_33
    //     0xe73dcc: ldur            w23, [x4, #0x33]
    // 0xe73dd0: DecompressPointer r23
    //     0xe73dd0: add             x23, x23, HEAP, lsl #32
    // 0xe73dd4: ldur            x4, [fp, #-0x18]
    // 0xe73dd8: stur            x23, [fp, #-0x28]
    // 0xe73ddc: LoadField: r24 = r0->field_4f
    //     0xe73ddc: ldur            w24, [x0, #0x4f]
    // 0xe73de0: DecompressPointer r24
    //     0xe73de0: add             x24, x24, HEAP, lsl #32
    // 0xe73de4: stur            x24, [fp, #-0x20]
    // 0xe73de8: r0 = inline_Allocate_Double()
    //     0xe73de8: ldp             x0, x25, [THR, #0x50]  ; THR::top
    //     0xe73dec: add             x0, x0, #0x10
    //     0xe73df0: cmp             x25, x0
    //     0xe73df4: b.ls            #0xe73ec4
    //     0xe73df8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe73dfc: sub             x0, x0, #0xf
    //     0xe73e00: movz            x25, #0xe15c
    //     0xe73e04: movk            x25, #0x3, lsl #16
    //     0xe73e08: stur            x25, [x0, #-1]
    // 0xe73e0c: StoreField: r0->field_7 = d0
    //     0xe73e0c: stur            d0, [x0, #7]
    // 0xe73e10: stur            x0, [fp, #-8]
    // 0xe73e14: r0 = SvgBrush()
    //     0xe73e14: bl              #0xe73f70  ; AllocateSvgBrushStub -> SvgBrush (size=0x54)
    // 0xe73e18: ldur            x1, [fp, #-8]
    // 0xe73e1c: StoreField: r0->field_7 = r1
    //     0xe73e1c: stur            w1, [x0, #7]
    // 0xe73e20: ldur            x1, [fp, #-0x18]
    // 0xe73e24: StoreField: r0->field_b = r1
    //     0xe73e24: stur            w1, [x0, #0xb]
    // 0xe73e28: ldur            x1, [fp, #-0x80]
    // 0xe73e2c: StoreField: r0->field_f = r1
    //     0xe73e2c: stur            w1, [x0, #0xf]
    // 0xe73e30: ldur            x1, [fp, #-0x90]
    // 0xe73e34: StoreField: r0->field_13 = r1
    //     0xe73e34: stur            w1, [x0, #0x13]
    // 0xe73e38: ldur            x1, [fp, #-0xa0]
    // 0xe73e3c: ArrayStore: r0[0] = r1  ; List_4
    //     0xe73e3c: stur            w1, [x0, #0x17]
    // 0xe73e40: ldur            x1, [fp, #-0x88]
    // 0xe73e44: StoreField: r0->field_1b = r1
    //     0xe73e44: stur            w1, [x0, #0x1b]
    // 0xe73e48: ldur            x1, [fp, #-0x78]
    // 0xe73e4c: StoreField: r0->field_1f = r1
    //     0xe73e4c: stur            w1, [x0, #0x1f]
    // 0xe73e50: ldur            x1, [fp, #-0x70]
    // 0xe73e54: StoreField: r0->field_23 = r1
    //     0xe73e54: stur            w1, [x0, #0x23]
    // 0xe73e58: ldur            x1, [fp, #-0x68]
    // 0xe73e5c: StoreField: r0->field_27 = r1
    //     0xe73e5c: stur            w1, [x0, #0x27]
    // 0xe73e60: ldur            x1, [fp, #-0x38]
    // 0xe73e64: StoreField: r0->field_2b = r1
    //     0xe73e64: stur            w1, [x0, #0x2b]
    // 0xe73e68: ldur            x1, [fp, #-0x30]
    // 0xe73e6c: StoreField: r0->field_2f = r1
    //     0xe73e6c: stur            w1, [x0, #0x2f]
    // 0xe73e70: ldur            x1, [fp, #-0x28]
    // 0xe73e74: StoreField: r0->field_33 = r1
    //     0xe73e74: stur            w1, [x0, #0x33]
    // 0xe73e78: ldur            x1, [fp, #-0x58]
    // 0xe73e7c: StoreField: r0->field_3b = r1
    //     0xe73e7c: stur            w1, [x0, #0x3b]
    // 0xe73e80: ldur            x1, [fp, #-0x60]
    // 0xe73e84: StoreField: r0->field_37 = r1
    //     0xe73e84: stur            w1, [x0, #0x37]
    // 0xe73e88: ldur            x1, [fp, #-0x50]
    // 0xe73e8c: StoreField: r0->field_3f = r1
    //     0xe73e8c: stur            w1, [x0, #0x3f]
    // 0xe73e90: ldur            x1, [fp, #-0x48]
    // 0xe73e94: StoreField: r0->field_43 = r1
    //     0xe73e94: stur            w1, [x0, #0x43]
    // 0xe73e98: ldur            x1, [fp, #-0x40]
    // 0xe73e9c: StoreField: r0->field_47 = r1
    //     0xe73e9c: stur            w1, [x0, #0x47]
    // 0xe73ea0: ldur            x1, [fp, #-0x98]
    // 0xe73ea4: StoreField: r0->field_4b = r1
    //     0xe73ea4: stur            w1, [x0, #0x4b]
    // 0xe73ea8: ldur            x1, [fp, #-0x20]
    // 0xe73eac: StoreField: r0->field_4f = r1
    //     0xe73eac: stur            w1, [x0, #0x4f]
    // 0xe73eb0: LeaveFrame
    //     0xe73eb0: mov             SP, fp
    //     0xe73eb4: ldp             fp, lr, [SP], #0x10
    // 0xe73eb8: ret
    //     0xe73eb8: ret             
    // 0xe73ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe73ebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe73ec0: b               #0xe73bb0
    // 0xe73ec4: SaveReg d0
    //     0xe73ec4: str             q0, [SP, #-0x10]!
    // 0xe73ec8: stp             x23, x24, [SP, #-0x10]!
    // 0xe73ecc: stp             x19, x20, [SP, #-0x10]!
    // 0xe73ed0: stp             x13, x14, [SP, #-0x10]!
    // 0xe73ed4: stp             x11, x12, [SP, #-0x10]!
    // 0xe73ed8: stp             x9, x10, [SP, #-0x10]!
    // 0xe73edc: stp             x7, x8, [SP, #-0x10]!
    // 0xe73ee0: stp             x5, x6, [SP, #-0x10]!
    // 0xe73ee4: stp             x3, x4, [SP, #-0x10]!
    // 0xe73ee8: stp             x1, x2, [SP, #-0x10]!
    // 0xe73eec: r0 = AllocateDouble()
    //     0xe73eec: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe73ef0: ldp             x1, x2, [SP], #0x10
    // 0xe73ef4: ldp             x3, x4, [SP], #0x10
    // 0xe73ef8: ldp             x5, x6, [SP], #0x10
    // 0xe73efc: ldp             x7, x8, [SP], #0x10
    // 0xe73f00: ldp             x9, x10, [SP], #0x10
    // 0xe73f04: ldp             x11, x12, [SP], #0x10
    // 0xe73f08: ldp             x13, x14, [SP], #0x10
    // 0xe73f0c: ldp             x19, x20, [SP], #0x10
    // 0xe73f10: ldp             x23, x24, [SP], #0x10
    // 0xe73f14: RestoreReg d0
    //     0xe73f14: ldr             q0, [SP], #0x10
    // 0xe73f18: b               #0xe73e0c
  }
  [closure] static double <anonymous closure>(dynamic, SvgNumeric) {
    // ** addr: 0xe76dcc, size: 0x54
    // 0xe76dcc: EnterFrame
    //     0xe76dcc: stp             fp, lr, [SP, #-0x10]!
    //     0xe76dd0: mov             fp, SP
    // 0xe76dd4: ldr             x1, [fp, #0x10]
    // 0xe76dd8: LoadField: d0 = r1->field_7
    //     0xe76dd8: ldur            d0, [x1, #7]
    // 0xe76ddc: r0 = inline_Allocate_Double()
    //     0xe76ddc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe76de0: add             x0, x0, #0x10
    //     0xe76de4: cmp             x1, x0
    //     0xe76de8: b.ls            #0xe76e10
    //     0xe76dec: str             x0, [THR, #0x50]  ; THR::top
    //     0xe76df0: sub             x0, x0, #0xf
    //     0xe76df4: movz            x1, #0xe15c
    //     0xe76df8: movk            x1, #0x3, lsl #16
    //     0xe76dfc: stur            x1, [x0, #-1]
    // 0xe76e00: StoreField: r0->field_7 = d0
    //     0xe76e00: stur            d0, [x0, #7]
    // 0xe76e04: LeaveFrame
    //     0xe76e04: mov             SP, fp
    //     0xe76e08: ldp             fp, lr, [SP], #0x10
    // 0xe76e0c: ret
    //     0xe76e0c: ret             
    // 0xe76e10: SaveReg d0
    //     0xe76e10: str             q0, [SP, #-0x10]!
    // 0xe76e14: r0 = AllocateDouble()
    //     0xe76e14: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe76e18: RestoreReg d0
    //     0xe76e18: ldr             q0, [SP], #0x10
    // 0xe76e1c: b               #0xe76e00
  }
}

// class id: 6808, size: 0x14, field offset: 0x14
enum SvgTextAnchor extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4dc6c, size: 0x64
    // 0xc4dc6c: EnterFrame
    //     0xc4dc6c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4dc70: mov             fp, SP
    // 0xc4dc74: AllocStack(0x10)
    //     0xc4dc74: sub             SP, SP, #0x10
    // 0xc4dc78: SetupParameters(SvgTextAnchor this /* r1 => r0, fp-0x8 */)
    //     0xc4dc78: mov             x0, x1
    //     0xc4dc7c: stur            x1, [fp, #-8]
    // 0xc4dc80: CheckStackOverflow
    //     0xc4dc80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4dc84: cmp             SP, x16
    //     0xc4dc88: b.ls            #0xc4dcc8
    // 0xc4dc8c: r1 = Null
    //     0xc4dc8c: mov             x1, NULL
    // 0xc4dc90: r2 = 4
    //     0xc4dc90: movz            x2, #0x4
    // 0xc4dc94: r0 = AllocateArray()
    //     0xc4dc94: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4dc98: r16 = "SvgTextAnchor."
    //     0xc4dc98: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f60] "SvgTextAnchor."
    //     0xc4dc9c: ldr             x16, [x16, #0xf60]
    // 0xc4dca0: StoreField: r0->field_f = r16
    //     0xc4dca0: stur            w16, [x0, #0xf]
    // 0xc4dca4: ldur            x1, [fp, #-8]
    // 0xc4dca8: LoadField: r2 = r1->field_f
    //     0xc4dca8: ldur            w2, [x1, #0xf]
    // 0xc4dcac: DecompressPointer r2
    //     0xc4dcac: add             x2, x2, HEAP, lsl #32
    // 0xc4dcb0: StoreField: r0->field_13 = r2
    //     0xc4dcb0: stur            w2, [x0, #0x13]
    // 0xc4dcb4: str             x0, [SP]
    // 0xc4dcb8: r0 = _interpolate()
    //     0xc4dcb8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4dcbc: LeaveFrame
    //     0xc4dcbc: mov             SP, fp
    //     0xc4dcc0: ldp             fp, lr, [SP], #0x10
    // 0xc4dcc4: ret
    //     0xc4dcc4: ret             
    // 0xc4dcc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4dcc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4dccc: b               #0xc4dc8c
  }
}
