// lib: , url: package:pdf/src/svg/clip_path.dart

// class id: 1050828, size: 0x8
class :: {
}

// class id: 853, size: 0x14, field offset: 0x8
//   const constructor, 
class SvgClipPath extends Object {

  bool field_c;

  _ apply(/* No info */) {
    // ** addr: 0xe47724, size: 0x1fc
    // 0xe47724: EnterFrame
    //     0xe47724: stp             fp, lr, [SP, #-0x10]!
    //     0xe47728: mov             fp, SP
    // 0xe4772c: AllocStack(0x40)
    //     0xe4772c: sub             SP, SP, #0x40
    // 0xe47730: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xe47730: mov             x0, x2
    //     0xe47734: stur            x2, [fp, #-8]
    // 0xe47738: CheckStackOverflow
    //     0xe47738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4773c: cmp             SP, x16
    //     0xe47740: b.ls            #0xe47908
    // 0xe47744: LoadField: r2 = r1->field_b
    //     0xe47744: ldur            w2, [x1, #0xb]
    // 0xe47748: DecompressPointer r2
    //     0xe47748: add             x2, x2, HEAP, lsl #32
    // 0xe4774c: tbnz            w2, #4, #0xe47760
    // 0xe47750: r0 = Null
    //     0xe47750: mov             x0, NULL
    // 0xe47754: LeaveFrame
    //     0xe47754: mov             SP, fp
    //     0xe47758: ldp             fp, lr, [SP], #0x10
    // 0xe4775c: ret
    //     0xe4775c: ret             
    // 0xe47760: LoadField: r2 = r1->field_7
    //     0xe47760: ldur            w2, [x1, #7]
    // 0xe47764: DecompressPointer r2
    //     0xe47764: add             x2, x2, HEAP, lsl #32
    // 0xe47768: cmp             w2, NULL
    // 0xe4776c: b.eq            #0xe47910
    // 0xe47770: mov             x1, x2
    // 0xe47774: r0 = iterator()
    //     0xe47774: bl              #0x887d48  ; [dart:_internal] MappedIterable::iterator
    // 0xe47778: mov             x2, x0
    // 0xe4777c: stur            x2, [fp, #-0x28]
    // 0xe47780: LoadField: r3 = r2->field_f
    //     0xe47780: ldur            w3, [x2, #0xf]
    // 0xe47784: DecompressPointer r3
    //     0xe47784: add             x3, x3, HEAP, lsl #32
    // 0xe47788: stur            x3, [fp, #-0x20]
    // 0xe4778c: LoadField: r4 = r2->field_13
    //     0xe4778c: ldur            w4, [x2, #0x13]
    // 0xe47790: DecompressPointer r4
    //     0xe47790: add             x4, x4, HEAP, lsl #32
    // 0xe47794: stur            x4, [fp, #-0x18]
    // 0xe47798: LoadField: r5 = r2->field_7
    //     0xe47798: ldur            w5, [x2, #7]
    // 0xe4779c: DecompressPointer r5
    //     0xe4779c: add             x5, x5, HEAP, lsl #32
    // 0xe477a0: stur            x5, [fp, #-0x10]
    // 0xe477a4: CheckStackOverflow
    //     0xe477a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe477a8: cmp             SP, x16
    //     0xe477ac: b.ls            #0xe47914
    // 0xe477b0: r0 = LoadClassIdInstr(r3)
    //     0xe477b0: ldur            x0, [x3, #-1]
    //     0xe477b4: ubfx            x0, x0, #0xc, #0x14
    // 0xe477b8: mov             x1, x3
    // 0xe477bc: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe477bc: movz            x17, #0x292d
    //     0xe477c0: movk            x17, #0x1, lsl #16
    //     0xe477c4: add             lr, x0, x17
    //     0xe477c8: ldr             lr, [x21, lr, lsl #3]
    //     0xe477cc: blr             lr
    // 0xe477d0: tbnz            w0, #4, #0xe478e8
    // 0xe477d4: ldur            x2, [fp, #-0x28]
    // 0xe477d8: ldur            x3, [fp, #-0x20]
    // 0xe477dc: r0 = LoadClassIdInstr(r3)
    //     0xe477dc: ldur            x0, [x3, #-1]
    //     0xe477e0: ubfx            x0, x0, #0xc, #0x14
    // 0xe477e4: mov             x1, x3
    // 0xe477e8: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe477e8: movz            x17, #0x384d
    //     0xe477ec: movk            x17, #0x1, lsl #16
    //     0xe477f0: add             lr, x0, x17
    //     0xe477f4: ldr             lr, [x21, lr, lsl #3]
    //     0xe477f8: blr             lr
    // 0xe477fc: ldur            x16, [fp, #-0x18]
    // 0xe47800: stp             x0, x16, [SP]
    // 0xe47804: ldur            x0, [fp, #-0x18]
    // 0xe47808: ClosureCall
    //     0xe47808: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe4780c: ldur            x2, [x0, #0x1f]
    //     0xe47810: blr             x2
    // 0xe47814: mov             x4, x0
    // 0xe47818: ldur            x3, [fp, #-0x28]
    // 0xe4781c: stur            x4, [fp, #-0x30]
    // 0xe47820: StoreField: r3->field_b = r0
    //     0xe47820: stur            w0, [x3, #0xb]
    //     0xe47824: tbz             w0, #0, #0xe47840
    //     0xe47828: ldurb           w16, [x3, #-1]
    //     0xe4782c: ldurb           w17, [x0, #-1]
    //     0xe47830: and             x16, x17, x16, lsr #2
    //     0xe47834: tst             x16, HEAP, lsr #32
    //     0xe47838: b.eq            #0xe47840
    //     0xe4783c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe47840: cmp             w4, NULL
    // 0xe47844: b.ne            #0xe47878
    // 0xe47848: mov             x0, x4
    // 0xe4784c: ldur            x2, [fp, #-0x10]
    // 0xe47850: r1 = Null
    //     0xe47850: mov             x1, NULL
    // 0xe47854: cmp             w2, NULL
    // 0xe47858: b.eq            #0xe47878
    // 0xe4785c: LoadField: r4 = r2->field_1b
    //     0xe4785c: ldur            w4, [x2, #0x1b]
    // 0xe47860: DecompressPointer r4
    //     0xe47860: add             x4, x4, HEAP, lsl #32
    // 0xe47864: r8 = X1
    //     0xe47864: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xe47868: LoadField: r9 = r4->field_7
    //     0xe47868: ldur            x9, [x4, #7]
    // 0xe4786c: r3 = Null
    //     0xe4786c: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ea88] Null
    //     0xe47870: ldr             x3, [x3, #0xa88]
    // 0xe47874: blr             x9
    // 0xe47878: ldur            x0, [fp, #-0x30]
    // 0xe4787c: cmp             w0, NULL
    // 0xe47880: b.eq            #0xe4791c
    // 0xe47884: ldur            x1, [fp, #-8]
    // 0xe47888: r0 = saveContext()
    //     0xe47888: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe4788c: ldur            x0, [fp, #-0x30]
    // 0xe47890: LoadField: r1 = r0->field_f
    //     0xe47890: ldur            w1, [x0, #0xf]
    // 0xe47894: DecompressPointer r1
    //     0xe47894: add             x1, x1, HEAP, lsl #32
    // 0xe47898: LoadField: r2 = r1->field_7
    //     0xe47898: ldur            w2, [x1, #7]
    // 0xe4789c: DecompressPointer r2
    //     0xe4789c: add             x2, x2, HEAP, lsl #32
    // 0xe478a0: cmp             w2, NULL
    // 0xe478a4: b.eq            #0xe478b0
    // 0xe478a8: ldur            x1, [fp, #-8]
    // 0xe478ac: r0 = setTransform()
    //     0xe478ac: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe478b0: ldur            x1, [fp, #-0x30]
    // 0xe478b4: r0 = LoadClassIdInstr(r1)
    //     0xe478b4: ldur            x0, [x1, #-1]
    //     0xe478b8: ubfx            x0, x0, #0xc, #0x14
    // 0xe478bc: ldur            x2, [fp, #-8]
    // 0xe478c0: r0 = GDT[cid_x0 + -0xded]()
    //     0xe478c0: sub             lr, x0, #0xded
    //     0xe478c4: ldr             lr, [x21, lr, lsl #3]
    //     0xe478c8: blr             lr
    // 0xe478cc: ldur            x1, [fp, #-8]
    // 0xe478d0: r0 = restoreContext()
    //     0xe478d0: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe478d4: ldur            x2, [fp, #-0x28]
    // 0xe478d8: ldur            x5, [fp, #-0x10]
    // 0xe478dc: ldur            x3, [fp, #-0x20]
    // 0xe478e0: ldur            x4, [fp, #-0x18]
    // 0xe478e4: b               #0xe477a4
    // 0xe478e8: ldur            x0, [fp, #-0x28]
    // 0xe478ec: StoreField: r0->field_b = rNULL
    //     0xe478ec: stur            NULL, [x0, #0xb]
    // 0xe478f0: ldur            x1, [fp, #-8]
    // 0xe478f4: r0 = clipPath()
    //     0xe478f4: bl              #0xe479b0  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::clipPath
    // 0xe478f8: r0 = Null
    //     0xe478f8: mov             x0, NULL
    // 0xe478fc: LeaveFrame
    //     0xe478fc: mov             SP, fp
    //     0xe47900: ldp             fp, lr, [SP], #0x10
    // 0xe47904: ret
    //     0xe47904: ret             
    // 0xe47908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47908: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4790c: b               #0xe47744
    // 0xe47910: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe47910: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe47914: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47914: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47918: b               #0xe477b0
    // 0xe4791c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4791c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  factory _ SvgClipPath.fromXml(/* No info */) {
    // ** addr: 0xe6ef7c, size: 0x1b0
    // 0xe6ef7c: EnterFrame
    //     0xe6ef7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe6ef80: mov             fp, SP
    // 0xe6ef84: AllocStack(0x38)
    //     0xe6ef84: sub             SP, SP, #0x38
    // 0xe6ef88: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0xe6ef88: mov             x0, x1
    //     0xe6ef8c: mov             x1, x2
    //     0xe6ef90: stur            x2, [fp, #-8]
    //     0xe6ef94: stur            x3, [fp, #-0x10]
    //     0xe6ef98: stur            x5, [fp, #-0x18]
    // 0xe6ef9c: CheckStackOverflow
    //     0xe6ef9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6efa0: cmp             SP, x16
    //     0xe6efa4: b.ls            #0xe6f124
    // 0xe6efa8: r1 = 2
    //     0xe6efa8: movz            x1, #0x2
    // 0xe6efac: r0 = AllocateContext()
    //     0xe6efac: bl              #0xec126c  ; AllocateContextStub
    // 0xe6efb0: mov             x3, x0
    // 0xe6efb4: ldur            x0, [fp, #-0x10]
    // 0xe6efb8: stur            x3, [fp, #-0x20]
    // 0xe6efbc: StoreField: r3->field_f = r0
    //     0xe6efbc: stur            w0, [x3, #0xf]
    // 0xe6efc0: ldur            x0, [fp, #-0x18]
    // 0xe6efc4: StoreField: r3->field_13 = r0
    //     0xe6efc4: stur            w0, [x3, #0x13]
    // 0xe6efc8: ldur            x1, [fp, #-8]
    // 0xe6efcc: r2 = "clip-path"
    //     0xe6efcc: add             x2, PP, #0x25, lsl #12  ; [pp+0x25d98] "clip-path"
    //     0xe6efd0: ldr             x2, [x2, #0xd98]
    // 0xe6efd4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6efd4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6efd8: r0 = getAttribute()
    //     0xe6efd8: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe6efdc: stur            x0, [fp, #-8]
    // 0xe6efe0: cmp             w0, NULL
    // 0xe6efe4: b.ne            #0xe6effc
    // 0xe6efe8: r0 = Instance_SvgClipPath
    //     0xe6efe8: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eca8] Obj!SvgClipPath@e0c641
    //     0xe6efec: ldr             x0, [x0, #0xca8]
    // 0xe6eff0: LeaveFrame
    //     0xe6eff0: mov             SP, fp
    //     0xe6eff4: ldp             fp, lr, [SP], #0x10
    // 0xe6eff8: ret
    //     0xe6eff8: ret             
    // 0xe6effc: mov             x1, x0
    // 0xe6f000: r2 = "url(#"
    //     0xe6f000: add             x2, PP, #0x25, lsl #12  ; [pp+0x25fa8] "url(#"
    //     0xe6f004: ldr             x2, [x2, #0xfa8]
    // 0xe6f008: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6f008: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6f00c: r0 = startsWith()
    //     0xe6f00c: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe6f010: tbnz            w0, #4, #0xe6f110
    // 0xe6f014: ldur            x0, [fp, #-0x20]
    // 0xe6f018: ldur            x1, [fp, #-8]
    // 0xe6f01c: r2 = ")"
    //     0xe6f01c: ldr             x2, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xe6f020: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6f020: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6f024: r0 = lastIndexOf()
    //     0xe6f024: bl              #0x642150  ; [dart:core] _StringBase::lastIndexOf
    // 0xe6f028: mov             x2, x0
    // 0xe6f02c: r0 = BoxInt64Instr(r2)
    //     0xe6f02c: sbfiz           x0, x2, #1, #0x1f
    //     0xe6f030: cmp             x2, x0, asr #1
    //     0xe6f034: b.eq            #0xe6f040
    //     0xe6f038: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe6f03c: stur            x2, [x0, #7]
    // 0xe6f040: str             x0, [SP]
    // 0xe6f044: ldur            x1, [fp, #-8]
    // 0xe6f048: r2 = 5
    //     0xe6f048: movz            x2, #0x5
    // 0xe6f04c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe6f04c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe6f050: r0 = substring()
    //     0xe6f050: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe6f054: mov             x1, x0
    // 0xe6f058: ldur            x0, [fp, #-0x20]
    // 0xe6f05c: LoadField: r2 = r0->field_f
    //     0xe6f05c: ldur            w2, [x0, #0xf]
    // 0xe6f060: DecompressPointer r2
    //     0xe6f060: add             x2, x2, HEAP, lsl #32
    // 0xe6f064: LoadField: r3 = r2->field_7
    //     0xe6f064: ldur            w3, [x2, #7]
    // 0xe6f068: DecompressPointer r3
    //     0xe6f068: add             x3, x3, HEAP, lsl #32
    // 0xe6f06c: mov             x2, x1
    // 0xe6f070: mov             x1, x3
    // 0xe6f074: r0 = findById()
    //     0xe6f074: bl              #0xe6f138  ; [package:pdf/src/svg/parser.dart] SvgParser::findById
    // 0xe6f078: cmp             w0, NULL
    // 0xe6f07c: b.eq            #0xe6f110
    // 0xe6f080: ldur            x2, [fp, #-0x20]
    // 0xe6f084: LoadField: r1 = r0->field_f
    //     0xe6f084: ldur            w1, [x0, #0xf]
    // 0xe6f088: DecompressPointer r1
    //     0xe6f088: add             x1, x1, HEAP, lsl #32
    // 0xe6f08c: r16 = <XmlElement>
    //     0xe6f08c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea98] TypeArguments: <XmlElement>
    //     0xe6f090: ldr             x16, [x16, #0xa98]
    // 0xe6f094: stp             x1, x16, [SP]
    // 0xe6f098: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe6f098: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe6f09c: r0 = whereType()
    //     0xe6f09c: bl              #0x7b5364  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::whereType
    // 0xe6f0a0: ldur            x2, [fp, #-0x20]
    // 0xe6f0a4: r1 = Function '<anonymous closure>': static.
    //     0xe6f0a4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ecb0] AnonymousClosure: static (0xe6f2bc), in [package:pdf/src/svg/group.dart] SvgGroup::SvgGroup.fromXml (0xe6dfc0)
    //     0xe6f0a8: ldr             x1, [x1, #0xcb0]
    // 0xe6f0ac: stur            x0, [fp, #-8]
    // 0xe6f0b0: r0 = AllocateClosure()
    //     0xe6f0b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe6f0b4: r16 = <SvgOperation?>
    //     0xe6f0b4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eab0] TypeArguments: <SvgOperation?>
    //     0xe6f0b8: ldr             x16, [x16, #0xab0]
    // 0xe6f0bc: ldur            lr, [fp, #-8]
    // 0xe6f0c0: stp             lr, x16, [SP, #8]
    // 0xe6f0c4: str             x0, [SP]
    // 0xe6f0c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe6f0c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe6f0cc: r0 = map()
    //     0xe6f0cc: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xe6f0d0: mov             x1, x0
    // 0xe6f0d4: ldur            x0, [fp, #-0x20]
    // 0xe6f0d8: stur            x1, [fp, #-0x10]
    // 0xe6f0dc: LoadField: r2 = r0->field_f
    //     0xe6f0dc: ldur            w2, [x0, #0xf]
    // 0xe6f0e0: DecompressPointer r2
    //     0xe6f0e0: add             x2, x2, HEAP, lsl #32
    // 0xe6f0e4: stur            x2, [fp, #-8]
    // 0xe6f0e8: r0 = SvgClipPath()
    //     0xe6f0e8: bl              #0xe6f12c  ; AllocateSvgClipPathStub -> SvgClipPath (size=0x14)
    // 0xe6f0ec: ldur            x1, [fp, #-0x10]
    // 0xe6f0f0: StoreField: r0->field_7 = r1
    //     0xe6f0f0: stur            w1, [x0, #7]
    // 0xe6f0f4: r1 = false
    //     0xe6f0f4: add             x1, NULL, #0x30  ; false
    // 0xe6f0f8: StoreField: r0->field_b = r1
    //     0xe6f0f8: stur            w1, [x0, #0xb]
    // 0xe6f0fc: ldur            x1, [fp, #-8]
    // 0xe6f100: StoreField: r0->field_f = r1
    //     0xe6f100: stur            w1, [x0, #0xf]
    // 0xe6f104: LeaveFrame
    //     0xe6f104: mov             SP, fp
    //     0xe6f108: ldp             fp, lr, [SP], #0x10
    // 0xe6f10c: ret
    //     0xe6f10c: ret             
    // 0xe6f110: r0 = Instance_SvgClipPath
    //     0xe6f110: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eca8] Obj!SvgClipPath@e0c641
    //     0xe6f114: ldr             x0, [x0, #0xca8]
    // 0xe6f118: LeaveFrame
    //     0xe6f118: mov             SP, fp
    //     0xe6f11c: ldp             fp, lr, [SP], #0x10
    // 0xe6f120: ret
    //     0xe6f120: ret             
    // 0xe6f124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6f124: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6f128: b               #0xe6efa8
  }
}
