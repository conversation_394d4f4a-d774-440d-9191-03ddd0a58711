// lib: , url: package:pdf/src/svg/color.dart

// class id: 1050829, size: 0x8
class :: {
}

// class id: 849, size: 0x14, field offset: 0x8
//   const constructor, 
class SvgColor extends Object {

  PdfColor field_8;
  bool field_10;

  _ toString(/* No info */) {
    // ** addr: 0xc36534, size: 0xe8
    // 0xc36534: EnterFrame
    //     0xc36534: stp             fp, lr, [SP, #-0x10]!
    //     0xc36538: mov             fp, SP
    // 0xc3653c: AllocStack(0x10)
    //     0xc3653c: sub             SP, SP, #0x10
    // 0xc36540: CheckStackOverflow
    //     0xc36540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc36544: cmp             SP, x16
    //     0xc36548: b.ls            #0xc36614
    // 0xc3654c: ldr             x16, [fp, #0x10]
    // 0xc36550: str             x16, [SP]
    // 0xc36554: r0 = runtimeType()
    //     0xc36554: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xc36558: r1 = Null
    //     0xc36558: mov             x1, NULL
    // 0xc3655c: r2 = 14
    //     0xc3655c: movz            x2, #0xe
    // 0xc36560: stur            x0, [fp, #-8]
    // 0xc36564: r0 = AllocateArray()
    //     0xc36564: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc36568: mov             x1, x0
    // 0xc3656c: ldur            x0, [fp, #-8]
    // 0xc36570: StoreField: r1->field_f = r0
    //     0xc36570: stur            w0, [x1, #0xf]
    // 0xc36574: r16 = " color: "
    //     0xc36574: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f00] " color: "
    //     0xc36578: ldr             x16, [x16, #0xf00]
    // 0xc3657c: StoreField: r1->field_13 = r16
    //     0xc3657c: stur            w16, [x1, #0x13]
    // 0xc36580: ldr             x0, [fp, #0x10]
    // 0xc36584: LoadField: r2 = r0->field_7
    //     0xc36584: ldur            w2, [x0, #7]
    // 0xc36588: DecompressPointer r2
    //     0xc36588: add             x2, x2, HEAP, lsl #32
    // 0xc3658c: ArrayStore: r1[0] = r2  ; List_4
    //     0xc3658c: stur            w2, [x1, #0x17]
    // 0xc36590: r16 = " inherit:"
    //     0xc36590: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f08] " inherit:"
    //     0xc36594: ldr             x16, [x16, #0xf08]
    // 0xc36598: StoreField: r1->field_1b = r16
    //     0xc36598: stur            w16, [x1, #0x1b]
    // 0xc3659c: LoadField: r3 = r0->field_f
    //     0xc3659c: ldur            w3, [x0, #0xf]
    // 0xc365a0: DecompressPointer r3
    //     0xc365a0: add             x3, x3, HEAP, lsl #32
    // 0xc365a4: StoreField: r1->field_1f = r3
    //     0xc365a4: stur            w3, [x1, #0x1f]
    // 0xc365a8: r16 = " isEmpty: "
    //     0xc365a8: add             x16, PP, #0x46, lsl #12  ; [pp+0x46f10] " isEmpty: "
    //     0xc365ac: ldr             x16, [x16, #0xf10]
    // 0xc365b0: StoreField: r1->field_23 = r16
    //     0xc365b0: stur            w16, [x1, #0x23]
    // 0xc365b4: r3 = LoadClassIdInstr(r0)
    //     0xc365b4: ldur            x3, [x0, #-1]
    //     0xc365b8: ubfx            x3, x3, #0xc, #0x14
    // 0xc365bc: sub             x16, x3, #0x353
    // 0xc365c0: cmp             x16, #1
    // 0xc365c4: b.hi            #0xc365ec
    // 0xc365c8: LoadField: r2 = r0->field_1b
    //     0xc365c8: ldur            w2, [x0, #0x1b]
    // 0xc365cc: DecompressPointer r2
    //     0xc365cc: add             x2, x2, HEAP, lsl #32
    // 0xc365d0: LoadField: r0 = r2->field_b
    //     0xc365d0: ldur            w0, [x2, #0xb]
    // 0xc365d4: cbz             w0, #0xc365e0
    // 0xc365d8: r2 = false
    //     0xc365d8: add             x2, NULL, #0x30  ; false
    // 0xc365dc: b               #0xc365e4
    // 0xc365e0: r2 = true
    //     0xc365e0: add             x2, NULL, #0x20  ; true
    // 0xc365e4: mov             x0, x2
    // 0xc365e8: b               #0xc365fc
    // 0xc365ec: cmp             w2, NULL
    // 0xc365f0: r16 = true
    //     0xc365f0: add             x16, NULL, #0x20  ; true
    // 0xc365f4: r17 = false
    //     0xc365f4: add             x17, NULL, #0x30  ; false
    // 0xc365f8: csel            x0, x16, x17, eq
    // 0xc365fc: StoreField: r1->field_27 = r0
    //     0xc365fc: stur            w0, [x1, #0x27]
    // 0xc36600: str             x1, [SP]
    // 0xc36604: r0 = _interpolate()
    //     0xc36604: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc36608: LeaveFrame
    //     0xc36608: mov             SP, fp
    //     0xc3660c: ldp             fp, lr, [SP], #0x10
    // 0xc36610: ret
    //     0xc36610: ret             
    // 0xc36614: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc36614: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc36618: b               #0xc3654c
  }
  get _ isNotEmpty(/* No info */) {
    // ** addr: 0xe4998c, size: 0x58
    // 0xe4998c: r2 = LoadClassIdInstr(r1)
    //     0xe4998c: ldur            x2, [x1, #-1]
    //     0xe49990: ubfx            x2, x2, #0xc, #0x14
    // 0xe49994: sub             x16, x2, #0x353
    // 0xe49998: cmp             x16, #1
    // 0xe4999c: b.hi            #0xe499c4
    // 0xe499a0: LoadField: r2 = r1->field_1b
    //     0xe499a0: ldur            w2, [x1, #0x1b]
    // 0xe499a4: DecompressPointer r2
    //     0xe499a4: add             x2, x2, HEAP, lsl #32
    // 0xe499a8: LoadField: r3 = r2->field_b
    //     0xe499a8: ldur            w3, [x2, #0xb]
    // 0xe499ac: cbz             w3, #0xe499b8
    // 0xe499b0: r2 = false
    //     0xe499b0: add             x2, NULL, #0x30  ; false
    // 0xe499b4: b               #0xe499bc
    // 0xe499b8: r2 = true
    //     0xe499b8: add             x2, NULL, #0x20  ; true
    // 0xe499bc: mov             x1, x2
    // 0xe499c0: b               #0xe499dc
    // 0xe499c4: LoadField: r2 = r1->field_7
    //     0xe499c4: ldur            w2, [x1, #7]
    // 0xe499c8: DecompressPointer r2
    //     0xe499c8: add             x2, x2, HEAP, lsl #32
    // 0xe499cc: cmp             w2, NULL
    // 0xe499d0: r16 = true
    //     0xe499d0: add             x16, NULL, #0x20  ; true
    // 0xe499d4: r17 = false
    //     0xe499d4: add             x17, NULL, #0x30  ; false
    // 0xe499d8: csel            x1, x16, x17, eq
    // 0xe499dc: eor             x0, x1, #0x10
    // 0xe499e0: ret
    //     0xe499e0: ret             
  }
  _ merge(/* No info */) {
    // ** addr: 0xe73f1c, size: 0x48
    // 0xe73f1c: EnterFrame
    //     0xe73f1c: stp             fp, lr, [SP, #-0x10]!
    //     0xe73f20: mov             fp, SP
    // 0xe73f24: AllocStack(0x8)
    //     0xe73f24: sub             SP, SP, #8
    // 0xe73f28: LoadField: r0 = r2->field_7
    //     0xe73f28: ldur            w0, [x2, #7]
    // 0xe73f2c: DecompressPointer r0
    //     0xe73f2c: add             x0, x0, HEAP, lsl #32
    // 0xe73f30: cmp             w0, NULL
    // 0xe73f34: b.ne            #0xe73f40
    // 0xe73f38: LoadField: r0 = r1->field_7
    //     0xe73f38: ldur            w0, [x1, #7]
    // 0xe73f3c: DecompressPointer r0
    //     0xe73f3c: add             x0, x0, HEAP, lsl #32
    // 0xe73f40: stur            x0, [fp, #-8]
    // 0xe73f44: r0 = SvgColor()
    //     0xe73f44: bl              #0xe73f64  ; AllocateSvgColorStub -> SvgColor (size=0x14)
    // 0xe73f48: ldur            x1, [fp, #-8]
    // 0xe73f4c: StoreField: r0->field_7 = r1
    //     0xe73f4c: stur            w1, [x0, #7]
    // 0xe73f50: r1 = false
    //     0xe73f50: add             x1, NULL, #0x30  ; false
    // 0xe73f54: StoreField: r0->field_f = r1
    //     0xe73f54: stur            w1, [x0, #0xf]
    // 0xe73f58: LeaveFrame
    //     0xe73f58: mov             SP, fp
    //     0xe73f5c: ldp             fp, lr, [SP], #0x10
    // 0xe73f60: ret
    //     0xe73f60: ret             
  }
  factory _ SvgColor.fromXml(/* No info */) {
    // ** addr: 0xe74098, size: 0x8ac
    // 0xe74098: EnterFrame
    //     0xe74098: stp             fp, lr, [SP, #-0x10]!
    //     0xe7409c: mov             fp, SP
    // 0xe740a0: AllocStack(0x98)
    //     0xe740a0: sub             SP, SP, #0x98
    // 0xe740a4: SetupParameters(dynamic _ /* r2 => r2, fp-0x48 */, dynamic _ /* r3 => r3, fp-0x50 */)
    //     0xe740a4: stur            x2, [fp, #-0x48]
    //     0xe740a8: stur            x3, [fp, #-0x50]
    // 0xe740ac: CheckStackOverflow
    //     0xe740ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe740b0: cmp             SP, x16
    //     0xe740b4: b.ls            #0xe74910
    // 0xe740b8: cmp             w2, NULL
    // 0xe740bc: b.ne            #0xe740d4
    // 0xe740c0: r0 = Instance_SvgColor
    //     0xe740c0: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3ecf8] Obj!SvgColor@e0c601
    //     0xe740c4: ldr             x0, [x0, #0xcf8]
    // 0xe740c8: LeaveFrame
    //     0xe740c8: mov             SP, fp
    //     0xe740cc: ldp             fp, lr, [SP], #0x10
    // 0xe740d0: ret
    //     0xe740d0: ret             
    // 0xe740d4: r0 = LoadClassIdInstr(r2)
    //     0xe740d4: ldur            x0, [x2, #-1]
    //     0xe740d8: ubfx            x0, x0, #0xc, #0x14
    // 0xe740dc: r16 = "none"
    //     0xe740dc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25b58] "none"
    //     0xe740e0: ldr             x16, [x16, #0xb58]
    // 0xe740e4: stp             x16, x2, [SP]
    // 0xe740e8: mov             lr, x0
    // 0xe740ec: ldr             lr, [x21, lr, lsl #3]
    // 0xe740f0: blr             lr
    // 0xe740f4: tbnz            w0, #4, #0xe7410c
    // 0xe740f8: r0 = Instance_SvgColor
    //     0xe740f8: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3ed00] Obj!SvgColor@e0c5e1
    //     0xe740fc: ldr             x0, [x0, #0xd00]
    // 0xe74100: LeaveFrame
    //     0xe74100: mov             SP, fp
    //     0xe74104: ldp             fp, lr, [SP], #0x10
    // 0xe74108: ret
    //     0xe74108: ret             
    // 0xe7410c: ldur            x2, [fp, #-0x48]
    // 0xe74110: r1 = _ConstMap len:148
    //     0xe74110: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ed08] Map<String, PdfColor>(148)
    //     0xe74114: ldr             x1, [x1, #0xd08]
    // 0xe74118: r0 = containsKey()
    //     0xe74118: bl              #0xd7847c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::containsKey
    // 0xe7411c: tbnz            w0, #4, #0xe7415c
    // 0xe74120: ldur            x2, [fp, #-0x48]
    // 0xe74124: r1 = _ConstMap len:148
    //     0xe74124: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ed08] Map<String, PdfColor>(148)
    //     0xe74128: ldr             x1, [x1, #0xd08]
    // 0xe7412c: r0 = []()
    //     0xe7412c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xe74130: stur            x0, [fp, #-0x58]
    // 0xe74134: r0 = SvgColor()
    //     0xe74134: bl              #0xe73f64  ; AllocateSvgColorStub -> SvgColor (size=0x14)
    // 0xe74138: mov             x1, x0
    // 0xe7413c: ldur            x0, [fp, #-0x58]
    // 0xe74140: StoreField: r1->field_7 = r0
    //     0xe74140: stur            w0, [x1, #7]
    // 0xe74144: r2 = false
    //     0xe74144: add             x2, NULL, #0x30  ; false
    // 0xe74148: StoreField: r1->field_f = r2
    //     0xe74148: stur            w2, [x1, #0xf]
    // 0xe7414c: mov             x0, x1
    // 0xe74150: LeaveFrame
    //     0xe74150: mov             SP, fp
    //     0xe74154: ldp             fp, lr, [SP], #0x10
    // 0xe74158: ret
    //     0xe74158: ret             
    // 0xe7415c: ldur            x1, [fp, #-0x48]
    // 0xe74160: r2 = false
    //     0xe74160: add             x2, NULL, #0x30  ; false
    // 0xe74164: r0 = LoadClassIdInstr(r1)
    //     0xe74164: ldur            x0, [x1, #-1]
    //     0xe74168: ubfx            x0, x0, #0xc, #0x14
    // 0xe7416c: str             x1, [SP]
    // 0xe74170: r0 = GDT[cid_x0 + -0xffe]()
    //     0xe74170: sub             lr, x0, #0xffe
    //     0xe74174: ldr             lr, [x21, lr, lsl #3]
    //     0xe74178: blr             lr
    // 0xe7417c: mov             x1, x0
    // 0xe74180: r2 = "rgba"
    //     0xe74180: add             x2, PP, #0x25, lsl #12  ; [pp+0x25b68] "rgba"
    //     0xe74184: ldr             x2, [x2, #0xb68]
    // 0xe74188: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe74188: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe7418c: r0 = startsWith()
    //     0xe7418c: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe74190: tbnz            w0, #4, #0xe74354
    // 0xe74194: ldur            x3, [fp, #-0x48]
    // 0xe74198: r0 = LoadClassIdInstr(r3)
    //     0xe74198: ldur            x0, [x3, #-1]
    //     0xe7419c: ubfx            x0, x0, #0xc, #0x14
    // 0xe741a0: mov             x1, x3
    // 0xe741a4: r2 = "("
    //     0xe741a4: add             x2, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xe741a8: ldr             x2, [x2, #0xf08]
    // 0xe741ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe741ac: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe741b0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe741b0: sub             lr, x0, #0xffa
    //     0xe741b4: ldr             lr, [x21, lr, lsl #3]
    //     0xe741b8: blr             lr
    // 0xe741bc: add             x3, x0, #1
    // 0xe741c0: ldur            x4, [fp, #-0x48]
    // 0xe741c4: stur            x3, [fp, #-0x60]
    // 0xe741c8: r0 = LoadClassIdInstr(r4)
    //     0xe741c8: ldur            x0, [x4, #-1]
    //     0xe741cc: ubfx            x0, x0, #0xc, #0x14
    // 0xe741d0: mov             x1, x4
    // 0xe741d4: r2 = ")"
    //     0xe741d4: ldr             x2, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xe741d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe741d8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe741dc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe741dc: sub             lr, x0, #0xffa
    //     0xe741e0: ldr             lr, [x21, lr, lsl #3]
    //     0xe741e4: blr             lr
    // 0xe741e8: mov             x2, x0
    // 0xe741ec: r0 = BoxInt64Instr(r2)
    //     0xe741ec: sbfiz           x0, x2, #1, #0x1f
    //     0xe741f0: cmp             x2, x0, asr #1
    //     0xe741f4: b.eq            #0xe74200
    //     0xe741f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe741fc: stur            x2, [x0, #7]
    // 0xe74200: str             x0, [SP]
    // 0xe74204: ldur            x1, [fp, #-0x48]
    // 0xe74208: ldur            x2, [fp, #-0x60]
    // 0xe7420c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe7420c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe74210: r0 = substring()
    //     0xe74210: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe74214: mov             x1, x0
    // 0xe74218: r2 = Null
    //     0xe74218: mov             x2, NULL
    // 0xe7421c: r0 = splitNumeric()
    //     0xe7421c: bl              #0xe73f7c  ; [package:pdf/src/svg/parser.dart] SvgParser::splitNumeric
    // 0xe74220: LoadField: r1 = r0->field_7
    //     0xe74220: ldur            w1, [x0, #7]
    // 0xe74224: DecompressPointer r1
    //     0xe74224: add             x1, x1, HEAP, lsl #32
    // 0xe74228: mov             x2, x0
    // 0xe7422c: r0 = _GrowableList.of()
    //     0xe7422c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe74230: mov             x2, x0
    // 0xe74234: stur            x2, [fp, #-0x58]
    // 0xe74238: LoadField: r0 = r2->field_b
    //     0xe74238: ldur            w0, [x2, #0xb]
    // 0xe7423c: r1 = LoadInt32Instr(r0)
    //     0xe7423c: sbfx            x1, x0, #1, #0x1f
    // 0xe74240: mov             x0, x1
    // 0xe74244: r1 = 0
    //     0xe74244: movz            x1, #0
    // 0xe74248: cmp             x1, x0
    // 0xe7424c: b.hs            #0xe74918
    // 0xe74250: LoadField: r0 = r2->field_f
    //     0xe74250: ldur            w0, [x2, #0xf]
    // 0xe74254: DecompressPointer r0
    //     0xe74254: add             x0, x0, HEAP, lsl #32
    // 0xe74258: LoadField: r1 = r0->field_f
    //     0xe74258: ldur            w1, [x0, #0xf]
    // 0xe7425c: DecompressPointer r1
    //     0xe7425c: add             x1, x1, HEAP, lsl #32
    // 0xe74260: r0 = colorValue()
    //     0xe74260: bl              #0xe765b0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::colorValue
    // 0xe74264: ldur            x2, [fp, #-0x58]
    // 0xe74268: stur            d0, [fp, #-0x70]
    // 0xe7426c: LoadField: r0 = r2->field_b
    //     0xe7426c: ldur            w0, [x2, #0xb]
    // 0xe74270: r1 = LoadInt32Instr(r0)
    //     0xe74270: sbfx            x1, x0, #1, #0x1f
    // 0xe74274: mov             x0, x1
    // 0xe74278: r1 = 1
    //     0xe74278: movz            x1, #0x1
    // 0xe7427c: cmp             x1, x0
    // 0xe74280: b.hs            #0xe7491c
    // 0xe74284: LoadField: r0 = r2->field_f
    //     0xe74284: ldur            w0, [x2, #0xf]
    // 0xe74288: DecompressPointer r0
    //     0xe74288: add             x0, x0, HEAP, lsl #32
    // 0xe7428c: LoadField: r1 = r0->field_13
    //     0xe7428c: ldur            w1, [x0, #0x13]
    // 0xe74290: DecompressPointer r1
    //     0xe74290: add             x1, x1, HEAP, lsl #32
    // 0xe74294: r0 = colorValue()
    //     0xe74294: bl              #0xe765b0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::colorValue
    // 0xe74298: ldur            x2, [fp, #-0x58]
    // 0xe7429c: stur            d0, [fp, #-0x78]
    // 0xe742a0: LoadField: r0 = r2->field_b
    //     0xe742a0: ldur            w0, [x2, #0xb]
    // 0xe742a4: r1 = LoadInt32Instr(r0)
    //     0xe742a4: sbfx            x1, x0, #1, #0x1f
    // 0xe742a8: mov             x0, x1
    // 0xe742ac: r1 = 2
    //     0xe742ac: movz            x1, #0x2
    // 0xe742b0: cmp             x1, x0
    // 0xe742b4: b.hs            #0xe74920
    // 0xe742b8: LoadField: r0 = r2->field_f
    //     0xe742b8: ldur            w0, [x2, #0xf]
    // 0xe742bc: DecompressPointer r0
    //     0xe742bc: add             x0, x0, HEAP, lsl #32
    // 0xe742c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe742c0: ldur            w1, [x0, #0x17]
    // 0xe742c4: DecompressPointer r1
    //     0xe742c4: add             x1, x1, HEAP, lsl #32
    // 0xe742c8: r0 = colorValue()
    //     0xe742c8: bl              #0xe765b0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::colorValue
    // 0xe742cc: ldur            x2, [fp, #-0x58]
    // 0xe742d0: stur            d0, [fp, #-0x88]
    // 0xe742d4: LoadField: r0 = r2->field_b
    //     0xe742d4: ldur            w0, [x2, #0xb]
    // 0xe742d8: r1 = LoadInt32Instr(r0)
    //     0xe742d8: sbfx            x1, x0, #1, #0x1f
    // 0xe742dc: mov             x0, x1
    // 0xe742e0: r1 = 3
    //     0xe742e0: movz            x1, #0x3
    // 0xe742e4: cmp             x1, x0
    // 0xe742e8: b.hs            #0xe74924
    // 0xe742ec: LoadField: r0 = r2->field_f
    //     0xe742ec: ldur            w0, [x2, #0xf]
    // 0xe742f0: DecompressPointer r0
    //     0xe742f0: add             x0, x0, HEAP, lsl #32
    // 0xe742f4: LoadField: r1 = r0->field_1b
    //     0xe742f4: ldur            w1, [x0, #0x1b]
    // 0xe742f8: DecompressPointer r1
    //     0xe742f8: add             x1, x1, HEAP, lsl #32
    // 0xe742fc: LoadField: d1 = r1->field_7
    //     0xe742fc: ldur            d1, [x1, #7]
    // 0xe74300: stur            d1, [fp, #-0x80]
    // 0xe74304: r0 = PdfColor()
    //     0xe74304: bl              #0xb121d4  ; AllocatePdfColorStub -> PdfColor (size=0x28)
    // 0xe74308: ldur            d0, [fp, #-0x70]
    // 0xe7430c: stur            x0, [fp, #-0x58]
    // 0xe74310: StoreField: r0->field_f = d0
    //     0xe74310: stur            d0, [x0, #0xf]
    // 0xe74314: ldur            d0, [fp, #-0x78]
    // 0xe74318: ArrayStore: r0[0] = d0  ; List_8
    //     0xe74318: stur            d0, [x0, #0x17]
    // 0xe7431c: ldur            d0, [fp, #-0x88]
    // 0xe74320: StoreField: r0->field_1f = d0
    //     0xe74320: stur            d0, [x0, #0x1f]
    // 0xe74324: ldur            d0, [fp, #-0x80]
    // 0xe74328: StoreField: r0->field_7 = d0
    //     0xe74328: stur            d0, [x0, #7]
    // 0xe7432c: r0 = SvgColor()
    //     0xe7432c: bl              #0xe73f64  ; AllocateSvgColorStub -> SvgColor (size=0x14)
    // 0xe74330: mov             x1, x0
    // 0xe74334: ldur            x0, [fp, #-0x58]
    // 0xe74338: StoreField: r1->field_7 = r0
    //     0xe74338: stur            w0, [x1, #7]
    // 0xe7433c: r2 = false
    //     0xe7433c: add             x2, NULL, #0x30  ; false
    // 0xe74340: StoreField: r1->field_f = r2
    //     0xe74340: stur            w2, [x1, #0xf]
    // 0xe74344: mov             x0, x1
    // 0xe74348: LeaveFrame
    //     0xe74348: mov             SP, fp
    //     0xe7434c: ldp             fp, lr, [SP], #0x10
    // 0xe74350: ret
    //     0xe74350: ret             
    // 0xe74354: ldur            x1, [fp, #-0x48]
    // 0xe74358: r2 = false
    //     0xe74358: add             x2, NULL, #0x30  ; false
    // 0xe7435c: r0 = LoadClassIdInstr(r1)
    //     0xe7435c: ldur            x0, [x1, #-1]
    //     0xe74360: ubfx            x0, x0, #0xc, #0x14
    // 0xe74364: str             x1, [SP]
    // 0xe74368: r0 = GDT[cid_x0 + -0xffe]()
    //     0xe74368: sub             lr, x0, #0xffe
    //     0xe7436c: ldr             lr, [x21, lr, lsl #3]
    //     0xe74370: blr             lr
    // 0xe74374: mov             x1, x0
    // 0xe74378: r2 = "hsl"
    //     0xe74378: add             x2, PP, #0x25, lsl #12  ; [pp+0x25b80] "hsl"
    //     0xe7437c: ldr             x2, [x2, #0xb80]
    // 0xe74380: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe74380: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe74384: r0 = startsWith()
    //     0xe74384: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe74388: tbnz            w0, #4, #0xe74504
    // 0xe7438c: ldur            x3, [fp, #-0x48]
    // 0xe74390: r0 = LoadClassIdInstr(r3)
    //     0xe74390: ldur            x0, [x3, #-1]
    //     0xe74394: ubfx            x0, x0, #0xc, #0x14
    // 0xe74398: mov             x1, x3
    // 0xe7439c: r2 = "("
    //     0xe7439c: add             x2, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xe743a0: ldr             x2, [x2, #0xf08]
    // 0xe743a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe743a4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe743a8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe743a8: sub             lr, x0, #0xffa
    //     0xe743ac: ldr             lr, [x21, lr, lsl #3]
    //     0xe743b0: blr             lr
    // 0xe743b4: add             x3, x0, #1
    // 0xe743b8: ldur            x4, [fp, #-0x48]
    // 0xe743bc: stur            x3, [fp, #-0x60]
    // 0xe743c0: r0 = LoadClassIdInstr(r4)
    //     0xe743c0: ldur            x0, [x4, #-1]
    //     0xe743c4: ubfx            x0, x0, #0xc, #0x14
    // 0xe743c8: mov             x1, x4
    // 0xe743cc: r2 = ")"
    //     0xe743cc: ldr             x2, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xe743d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe743d0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe743d4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe743d4: sub             lr, x0, #0xffa
    //     0xe743d8: ldr             lr, [x21, lr, lsl #3]
    //     0xe743dc: blr             lr
    // 0xe743e0: mov             x2, x0
    // 0xe743e4: r0 = BoxInt64Instr(r2)
    //     0xe743e4: sbfiz           x0, x2, #1, #0x1f
    //     0xe743e8: cmp             x2, x0, asr #1
    //     0xe743ec: b.eq            #0xe743f8
    //     0xe743f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe743f4: stur            x2, [x0, #7]
    // 0xe743f8: str             x0, [SP]
    // 0xe743fc: ldur            x1, [fp, #-0x48]
    // 0xe74400: ldur            x2, [fp, #-0x60]
    // 0xe74404: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe74404: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe74408: r0 = substring()
    //     0xe74408: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe7440c: mov             x1, x0
    // 0xe74410: r2 = Null
    //     0xe74410: mov             x2, NULL
    // 0xe74414: r0 = splitNumeric()
    //     0xe74414: bl              #0xe73f7c  ; [package:pdf/src/svg/parser.dart] SvgParser::splitNumeric
    // 0xe74418: LoadField: r1 = r0->field_7
    //     0xe74418: ldur            w1, [x0, #7]
    // 0xe7441c: DecompressPointer r1
    //     0xe7441c: add             x1, x1, HEAP, lsl #32
    // 0xe74420: mov             x2, x0
    // 0xe74424: r0 = _GrowableList.of()
    //     0xe74424: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe74428: mov             x2, x0
    // 0xe7442c: stur            x2, [fp, #-0x58]
    // 0xe74430: LoadField: r0 = r2->field_b
    //     0xe74430: ldur            w0, [x2, #0xb]
    // 0xe74434: r1 = LoadInt32Instr(r0)
    //     0xe74434: sbfx            x1, x0, #1, #0x1f
    // 0xe74438: mov             x0, x1
    // 0xe7443c: r1 = 0
    //     0xe7443c: movz            x1, #0
    // 0xe74440: cmp             x1, x0
    // 0xe74444: b.hs            #0xe74928
    // 0xe74448: LoadField: r0 = r2->field_f
    //     0xe74448: ldur            w0, [x2, #0xf]
    // 0xe7444c: DecompressPointer r0
    //     0xe7444c: add             x0, x0, HEAP, lsl #32
    // 0xe74450: LoadField: r1 = r0->field_f
    //     0xe74450: ldur            w1, [x0, #0xf]
    // 0xe74454: DecompressPointer r1
    //     0xe74454: add             x1, x1, HEAP, lsl #32
    // 0xe74458: r0 = colorValue()
    //     0xe74458: bl              #0xe765b0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::colorValue
    // 0xe7445c: ldur            x2, [fp, #-0x58]
    // 0xe74460: stur            d0, [fp, #-0x70]
    // 0xe74464: LoadField: r0 = r2->field_b
    //     0xe74464: ldur            w0, [x2, #0xb]
    // 0xe74468: r1 = LoadInt32Instr(r0)
    //     0xe74468: sbfx            x1, x0, #1, #0x1f
    // 0xe7446c: mov             x0, x1
    // 0xe74470: r1 = 1
    //     0xe74470: movz            x1, #0x1
    // 0xe74474: cmp             x1, x0
    // 0xe74478: b.hs            #0xe7492c
    // 0xe7447c: LoadField: r0 = r2->field_f
    //     0xe7447c: ldur            w0, [x2, #0xf]
    // 0xe74480: DecompressPointer r0
    //     0xe74480: add             x0, x0, HEAP, lsl #32
    // 0xe74484: LoadField: r1 = r0->field_13
    //     0xe74484: ldur            w1, [x0, #0x13]
    // 0xe74488: DecompressPointer r1
    //     0xe74488: add             x1, x1, HEAP, lsl #32
    // 0xe7448c: r0 = colorValue()
    //     0xe7448c: bl              #0xe765b0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::colorValue
    // 0xe74490: ldur            x2, [fp, #-0x58]
    // 0xe74494: stur            d0, [fp, #-0x78]
    // 0xe74498: LoadField: r0 = r2->field_b
    //     0xe74498: ldur            w0, [x2, #0xb]
    // 0xe7449c: r1 = LoadInt32Instr(r0)
    //     0xe7449c: sbfx            x1, x0, #1, #0x1f
    // 0xe744a0: mov             x0, x1
    // 0xe744a4: r1 = 2
    //     0xe744a4: movz            x1, #0x2
    // 0xe744a8: cmp             x1, x0
    // 0xe744ac: b.hs            #0xe74930
    // 0xe744b0: LoadField: r0 = r2->field_f
    //     0xe744b0: ldur            w0, [x2, #0xf]
    // 0xe744b4: DecompressPointer r0
    //     0xe744b4: add             x0, x0, HEAP, lsl #32
    // 0xe744b8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe744b8: ldur            w1, [x0, #0x17]
    // 0xe744bc: DecompressPointer r1
    //     0xe744bc: add             x1, x1, HEAP, lsl #32
    // 0xe744c0: r0 = colorValue()
    //     0xe744c0: bl              #0xe765b0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::colorValue
    // 0xe744c4: mov             v2.16b, v0.16b
    // 0xe744c8: ldur            d0, [fp, #-0x70]
    // 0xe744cc: ldur            d1, [fp, #-0x78]
    // 0xe744d0: r1 = Null
    //     0xe744d0: mov             x1, NULL
    // 0xe744d4: r0 = PdfColorHsl()
    //     0xe744d4: bl              #0xe7626c  ; [package:pdf/src/pdf/color.dart] PdfColorHsl::PdfColorHsl
    // 0xe744d8: stur            x0, [fp, #-0x58]
    // 0xe744dc: r0 = SvgColor()
    //     0xe744dc: bl              #0xe73f64  ; AllocateSvgColorStub -> SvgColor (size=0x14)
    // 0xe744e0: mov             x1, x0
    // 0xe744e4: ldur            x0, [fp, #-0x58]
    // 0xe744e8: StoreField: r1->field_7 = r0
    //     0xe744e8: stur            w0, [x1, #7]
    // 0xe744ec: r2 = false
    //     0xe744ec: add             x2, NULL, #0x30  ; false
    // 0xe744f0: StoreField: r1->field_f = r2
    //     0xe744f0: stur            w2, [x1, #0xf]
    // 0xe744f4: mov             x0, x1
    // 0xe744f8: LeaveFrame
    //     0xe744f8: mov             SP, fp
    //     0xe744fc: ldp             fp, lr, [SP], #0x10
    // 0xe74500: ret
    //     0xe74500: ret             
    // 0xe74504: ldur            x1, [fp, #-0x48]
    // 0xe74508: r2 = false
    //     0xe74508: add             x2, NULL, #0x30  ; false
    // 0xe7450c: r0 = LoadClassIdInstr(r1)
    //     0xe7450c: ldur            x0, [x1, #-1]
    //     0xe74510: ubfx            x0, x0, #0xc, #0x14
    // 0xe74514: str             x1, [SP]
    // 0xe74518: r0 = GDT[cid_x0 + -0xffe]()
    //     0xe74518: sub             lr, x0, #0xffe
    //     0xe7451c: ldr             lr, [x21, lr, lsl #3]
    //     0xe74520: blr             lr
    // 0xe74524: mov             x1, x0
    // 0xe74528: r2 = "rgb"
    //     0xe74528: add             x2, PP, #0x25, lsl #12  ; [pp+0x25bc8] "rgb"
    //     0xe7452c: ldr             x2, [x2, #0xbc8]
    // 0xe74530: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe74530: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe74534: r0 = startsWith()
    //     0xe74534: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe74538: tbnz            w0, #4, #0xe746c8
    // 0xe7453c: ldur            x3, [fp, #-0x48]
    // 0xe74540: r0 = LoadClassIdInstr(r3)
    //     0xe74540: ldur            x0, [x3, #-1]
    //     0xe74544: ubfx            x0, x0, #0xc, #0x14
    // 0xe74548: mov             x1, x3
    // 0xe7454c: r2 = "("
    //     0xe7454c: add             x2, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xe74550: ldr             x2, [x2, #0xf08]
    // 0xe74554: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe74554: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe74558: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe74558: sub             lr, x0, #0xffa
    //     0xe7455c: ldr             lr, [x21, lr, lsl #3]
    //     0xe74560: blr             lr
    // 0xe74564: add             x3, x0, #1
    // 0xe74568: ldur            x4, [fp, #-0x48]
    // 0xe7456c: stur            x3, [fp, #-0x60]
    // 0xe74570: r0 = LoadClassIdInstr(r4)
    //     0xe74570: ldur            x0, [x4, #-1]
    //     0xe74574: ubfx            x0, x0, #0xc, #0x14
    // 0xe74578: mov             x1, x4
    // 0xe7457c: r2 = ")"
    //     0xe7457c: ldr             x2, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xe74580: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe74580: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe74584: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe74584: sub             lr, x0, #0xffa
    //     0xe74588: ldr             lr, [x21, lr, lsl #3]
    //     0xe7458c: blr             lr
    // 0xe74590: mov             x2, x0
    // 0xe74594: r0 = BoxInt64Instr(r2)
    //     0xe74594: sbfiz           x0, x2, #1, #0x1f
    //     0xe74598: cmp             x2, x0, asr #1
    //     0xe7459c: b.eq            #0xe745a8
    //     0xe745a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe745a4: stur            x2, [x0, #7]
    // 0xe745a8: str             x0, [SP]
    // 0xe745ac: ldur            x1, [fp, #-0x48]
    // 0xe745b0: ldur            x2, [fp, #-0x60]
    // 0xe745b4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe745b4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe745b8: r0 = substring()
    //     0xe745b8: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe745bc: mov             x1, x0
    // 0xe745c0: r2 = Null
    //     0xe745c0: mov             x2, NULL
    // 0xe745c4: r0 = splitNumeric()
    //     0xe745c4: bl              #0xe73f7c  ; [package:pdf/src/svg/parser.dart] SvgParser::splitNumeric
    // 0xe745c8: LoadField: r1 = r0->field_7
    //     0xe745c8: ldur            w1, [x0, #7]
    // 0xe745cc: DecompressPointer r1
    //     0xe745cc: add             x1, x1, HEAP, lsl #32
    // 0xe745d0: mov             x2, x0
    // 0xe745d4: r0 = _GrowableList.of()
    //     0xe745d4: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe745d8: mov             x2, x0
    // 0xe745dc: stur            x2, [fp, #-0x58]
    // 0xe745e0: LoadField: r0 = r2->field_b
    //     0xe745e0: ldur            w0, [x2, #0xb]
    // 0xe745e4: r1 = LoadInt32Instr(r0)
    //     0xe745e4: sbfx            x1, x0, #1, #0x1f
    // 0xe745e8: mov             x0, x1
    // 0xe745ec: r1 = 0
    //     0xe745ec: movz            x1, #0
    // 0xe745f0: cmp             x1, x0
    // 0xe745f4: b.hs            #0xe74934
    // 0xe745f8: LoadField: r0 = r2->field_f
    //     0xe745f8: ldur            w0, [x2, #0xf]
    // 0xe745fc: DecompressPointer r0
    //     0xe745fc: add             x0, x0, HEAP, lsl #32
    // 0xe74600: LoadField: r1 = r0->field_f
    //     0xe74600: ldur            w1, [x0, #0xf]
    // 0xe74604: DecompressPointer r1
    //     0xe74604: add             x1, x1, HEAP, lsl #32
    // 0xe74608: r0 = colorValue()
    //     0xe74608: bl              #0xe765b0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::colorValue
    // 0xe7460c: ldur            x2, [fp, #-0x58]
    // 0xe74610: stur            d0, [fp, #-0x70]
    // 0xe74614: LoadField: r0 = r2->field_b
    //     0xe74614: ldur            w0, [x2, #0xb]
    // 0xe74618: r1 = LoadInt32Instr(r0)
    //     0xe74618: sbfx            x1, x0, #1, #0x1f
    // 0xe7461c: mov             x0, x1
    // 0xe74620: r1 = 1
    //     0xe74620: movz            x1, #0x1
    // 0xe74624: cmp             x1, x0
    // 0xe74628: b.hs            #0xe74938
    // 0xe7462c: LoadField: r0 = r2->field_f
    //     0xe7462c: ldur            w0, [x2, #0xf]
    // 0xe74630: DecompressPointer r0
    //     0xe74630: add             x0, x0, HEAP, lsl #32
    // 0xe74634: LoadField: r1 = r0->field_13
    //     0xe74634: ldur            w1, [x0, #0x13]
    // 0xe74638: DecompressPointer r1
    //     0xe74638: add             x1, x1, HEAP, lsl #32
    // 0xe7463c: r0 = colorValue()
    //     0xe7463c: bl              #0xe765b0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::colorValue
    // 0xe74640: ldur            x2, [fp, #-0x58]
    // 0xe74644: stur            d0, [fp, #-0x78]
    // 0xe74648: LoadField: r0 = r2->field_b
    //     0xe74648: ldur            w0, [x2, #0xb]
    // 0xe7464c: r1 = LoadInt32Instr(r0)
    //     0xe7464c: sbfx            x1, x0, #1, #0x1f
    // 0xe74650: mov             x0, x1
    // 0xe74654: r1 = 2
    //     0xe74654: movz            x1, #0x2
    // 0xe74658: cmp             x1, x0
    // 0xe7465c: b.hs            #0xe7493c
    // 0xe74660: LoadField: r0 = r2->field_f
    //     0xe74660: ldur            w0, [x2, #0xf]
    // 0xe74664: DecompressPointer r0
    //     0xe74664: add             x0, x0, HEAP, lsl #32
    // 0xe74668: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe74668: ldur            w1, [x0, #0x17]
    // 0xe7466c: DecompressPointer r1
    //     0xe7466c: add             x1, x1, HEAP, lsl #32
    // 0xe74670: r0 = colorValue()
    //     0xe74670: bl              #0xe765b0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::colorValue
    // 0xe74674: stur            d0, [fp, #-0x80]
    // 0xe74678: r0 = PdfColor()
    //     0xe74678: bl              #0xb121d4  ; AllocatePdfColorStub -> PdfColor (size=0x28)
    // 0xe7467c: ldur            d0, [fp, #-0x70]
    // 0xe74680: stur            x0, [fp, #-0x58]
    // 0xe74684: StoreField: r0->field_f = d0
    //     0xe74684: stur            d0, [x0, #0xf]
    // 0xe74688: ldur            d0, [fp, #-0x78]
    // 0xe7468c: ArrayStore: r0[0] = d0  ; List_8
    //     0xe7468c: stur            d0, [x0, #0x17]
    // 0xe74690: ldur            d0, [fp, #-0x80]
    // 0xe74694: StoreField: r0->field_1f = d0
    //     0xe74694: stur            d0, [x0, #0x1f]
    // 0xe74698: d0 = 1.000000
    //     0xe74698: fmov            d0, #1.00000000
    // 0xe7469c: StoreField: r0->field_7 = d0
    //     0xe7469c: stur            d0, [x0, #7]
    // 0xe746a0: r0 = SvgColor()
    //     0xe746a0: bl              #0xe73f64  ; AllocateSvgColorStub -> SvgColor (size=0x14)
    // 0xe746a4: mov             x1, x0
    // 0xe746a8: ldur            x0, [fp, #-0x58]
    // 0xe746ac: StoreField: r1->field_7 = r0
    //     0xe746ac: stur            w0, [x1, #7]
    // 0xe746b0: r2 = false
    //     0xe746b0: add             x2, NULL, #0x30  ; false
    // 0xe746b4: StoreField: r1->field_f = r2
    //     0xe746b4: stur            w2, [x1, #0xf]
    // 0xe746b8: mov             x0, x1
    // 0xe746bc: LeaveFrame
    //     0xe746bc: mov             SP, fp
    //     0xe746c0: ldp             fp, lr, [SP], #0x10
    // 0xe746c4: ret
    //     0xe746c4: ret             
    // 0xe746c8: ldur            x1, [fp, #-0x48]
    // 0xe746cc: r2 = false
    //     0xe746cc: add             x2, NULL, #0x30  ; false
    // 0xe746d0: r0 = LoadClassIdInstr(r1)
    //     0xe746d0: ldur            x0, [x1, #-1]
    //     0xe746d4: ubfx            x0, x0, #0xc, #0x14
    // 0xe746d8: str             x1, [SP]
    // 0xe746dc: r0 = GDT[cid_x0 + -0xffe]()
    //     0xe746dc: sub             lr, x0, #0xffe
    //     0xe746e0: ldr             lr, [x21, lr, lsl #3]
    //     0xe746e4: blr             lr
    // 0xe746e8: mov             x1, x0
    // 0xe746ec: r2 = "url(#"
    //     0xe746ec: add             x2, PP, #0x25, lsl #12  ; [pp+0x25fa8] "url(#"
    //     0xe746f0: ldr             x2, [x2, #0xfa8]
    // 0xe746f4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe746f4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe746f8: r0 = startsWith()
    //     0xe746f8: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe746fc: tbnz            w0, #4, #0xe7487c
    // 0xe74700: ldur            x3, [fp, #-0x48]
    // 0xe74704: ldur            x4, [fp, #-0x50]
    // 0xe74708: LoadField: r5 = r4->field_7
    //     0xe74708: ldur            w5, [x4, #7]
    // 0xe7470c: DecompressPointer r5
    //     0xe7470c: add             x5, x5, HEAP, lsl #32
    // 0xe74710: stur            x5, [fp, #-0x58]
    // 0xe74714: r0 = LoadClassIdInstr(r3)
    //     0xe74714: ldur            x0, [x3, #-1]
    //     0xe74718: ubfx            x0, x0, #0xc, #0x14
    // 0xe7471c: mov             x1, x3
    // 0xe74720: r2 = ")"
    //     0xe74720: ldr             x2, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xe74724: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe74724: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe74728: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe74728: sub             lr, x0, #0xffa
    //     0xe7472c: ldr             lr, [x21, lr, lsl #3]
    //     0xe74730: blr             lr
    // 0xe74734: mov             x2, x0
    // 0xe74738: r0 = BoxInt64Instr(r2)
    //     0xe74738: sbfiz           x0, x2, #1, #0x1f
    //     0xe7473c: cmp             x2, x0, asr #1
    //     0xe74740: b.eq            #0xe7474c
    //     0xe74744: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe74748: stur            x2, [x0, #7]
    // 0xe7474c: str             x0, [SP]
    // 0xe74750: ldur            x1, [fp, #-0x48]
    // 0xe74754: r2 = 5
    //     0xe74754: movz            x2, #0x5
    // 0xe74758: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe74758: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe7475c: r0 = substring()
    //     0xe7475c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe74760: ldur            x1, [fp, #-0x58]
    // 0xe74764: mov             x2, x0
    // 0xe74768: r0 = findById()
    //     0xe74768: bl              #0xe6f138  ; [package:pdf/src/svg/parser.dart] SvgParser::findById
    // 0xe7476c: mov             x1, x0
    // 0xe74770: stur            x1, [fp, #-0x68]
    // 0xe74774: cmp             w1, NULL
    // 0xe74778: b.eq            #0xe74940
    // 0xe7477c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xe7477c: ldur            w2, [x1, #0x17]
    // 0xe74780: DecompressPointer r2
    //     0xe74780: add             x2, x2, HEAP, lsl #32
    // 0xe74784: stur            x2, [fp, #-0x58]
    // 0xe74788: r3 = LoadClassIdInstr(r2)
    //     0xe74788: ldur            x3, [x2, #-1]
    //     0xe7478c: ubfx            x3, x3, #0xc, #0x14
    // 0xe74790: stur            x3, [fp, #-0x60]
    // 0xe74794: cmp             x3, #0xe2
    // 0xe74798: b.ne            #0xe747a8
    // 0xe7479c: LoadField: r0 = r2->field_b
    //     0xe7479c: ldur            w0, [x2, #0xb]
    // 0xe747a0: DecompressPointer r0
    //     0xe747a0: add             x0, x0, HEAP, lsl #32
    // 0xe747a4: b               #0xe747b0
    // 0xe747a8: LoadField: r0 = r2->field_f
    //     0xe747a8: ldur            w0, [x2, #0xf]
    // 0xe747ac: DecompressPointer r0
    //     0xe747ac: add             x0, x0, HEAP, lsl #32
    // 0xe747b0: r4 = LoadClassIdInstr(r0)
    //     0xe747b0: ldur            x4, [x0, #-1]
    //     0xe747b4: ubfx            x4, x4, #0xc, #0x14
    // 0xe747b8: r16 = "linearGradient"
    //     0xe747b8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26368] "linearGradient"
    //     0xe747bc: ldr             x16, [x16, #0x368]
    // 0xe747c0: stp             x16, x0, [SP]
    // 0xe747c4: mov             x0, x4
    // 0xe747c8: mov             lr, x0
    // 0xe747cc: ldr             lr, [x21, lr, lsl #3]
    // 0xe747d0: blr             lr
    // 0xe747d4: tbnz            w0, #4, #0xe747f4
    // 0xe747d8: ldur            x2, [fp, #-0x68]
    // 0xe747dc: ldur            x3, [fp, #-0x50]
    // 0xe747e0: r1 = Null
    //     0xe747e0: mov             x1, NULL
    // 0xe747e4: r0 = SvgLinearGradient.fromXml()
    //     0xe747e4: bl              #0xe75794  ; [package:pdf/src/svg/gradient.dart] SvgLinearGradient::SvgLinearGradient.fromXml
    // 0xe747e8: LeaveFrame
    //     0xe747e8: mov             SP, fp
    //     0xe747ec: ldp             fp, lr, [SP], #0x10
    // 0xe747f0: ret
    //     0xe747f0: ret             
    // 0xe747f4: ldur            x0, [fp, #-0x60]
    // 0xe747f8: cmp             x0, #0xe2
    // 0xe747fc: b.ne            #0xe74814
    // 0xe74800: ldur            x0, [fp, #-0x58]
    // 0xe74804: LoadField: r1 = r0->field_b
    //     0xe74804: ldur            w1, [x0, #0xb]
    // 0xe74808: DecompressPointer r1
    //     0xe74808: add             x1, x1, HEAP, lsl #32
    // 0xe7480c: mov             x0, x1
    // 0xe74810: b               #0xe74824
    // 0xe74814: ldur            x0, [fp, #-0x58]
    // 0xe74818: LoadField: r1 = r0->field_f
    //     0xe74818: ldur            w1, [x0, #0xf]
    // 0xe7481c: DecompressPointer r1
    //     0xe7481c: add             x1, x1, HEAP, lsl #32
    // 0xe74820: mov             x0, x1
    // 0xe74824: r1 = LoadClassIdInstr(r0)
    //     0xe74824: ldur            x1, [x0, #-1]
    //     0xe74828: ubfx            x1, x1, #0xc, #0x14
    // 0xe7482c: r16 = "radialGradient"
    //     0xe7482c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26428] "radialGradient"
    //     0xe74830: ldr             x16, [x16, #0x428]
    // 0xe74834: stp             x16, x0, [SP]
    // 0xe74838: mov             x0, x1
    // 0xe7483c: mov             lr, x0
    // 0xe74840: ldr             lr, [x21, lr, lsl #3]
    // 0xe74844: blr             lr
    // 0xe74848: tbnz            w0, #4, #0xe74868
    // 0xe7484c: ldur            x2, [fp, #-0x68]
    // 0xe74850: ldur            x3, [fp, #-0x50]
    // 0xe74854: r1 = Null
    //     0xe74854: mov             x1, NULL
    // 0xe74858: r0 = SvgRadialGradient.fromXml()
    //     0xe74858: bl              #0xe74c48  ; [package:pdf/src/svg/gradient.dart] SvgRadialGradient::SvgRadialGradient.fromXml
    // 0xe7485c: LeaveFrame
    //     0xe7485c: mov             SP, fp
    //     0xe74860: ldp             fp, lr, [SP], #0x10
    // 0xe74864: ret
    //     0xe74864: ret             
    // 0xe74868: r0 = Instance_SvgColor
    //     0xe74868: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3ed00] Obj!SvgColor@e0c5e1
    //     0xe7486c: ldr             x0, [x0, #0xd00]
    // 0xe74870: LeaveFrame
    //     0xe74870: mov             SP, fp
    //     0xe74874: ldp             fp, lr, [SP], #0x10
    // 0xe74878: ret
    //     0xe74878: ret             
    // 0xe7487c: r0 = SvgColor()
    //     0xe7487c: bl              #0xe73f64  ; AllocateSvgColorStub -> SvgColor (size=0x14)
    // 0xe74880: ldur            x2, [fp, #-0x48]
    // 0xe74884: r1 = Null
    //     0xe74884: mov             x1, NULL
    // 0xe74888: stur            x0, [fp, #-0x50]
    // 0xe7488c: r0 = PdfColor.fromHex()
    //     0xe7488c: bl              #0xe74944  ; [package:pdf/src/pdf/color.dart] PdfColor::PdfColor.fromHex
    // 0xe74890: ldur            x1, [fp, #-0x50]
    // 0xe74894: StoreField: r1->field_7 = r0
    //     0xe74894: stur            w0, [x1, #7]
    //     0xe74898: ldurb           w16, [x1, #-1]
    //     0xe7489c: ldurb           w17, [x0, #-1]
    //     0xe748a0: and             x16, x17, x16, lsr #2
    //     0xe748a4: tst             x16, HEAP, lsr #32
    //     0xe748a8: b.eq            #0xe748b0
    //     0xe748ac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe748b0: r0 = false
    //     0xe748b0: add             x0, NULL, #0x30  ; false
    // 0xe748b4: StoreField: r1->field_f = r0
    //     0xe748b4: stur            w0, [x1, #0xf]
    // 0xe748b8: mov             x0, x1
    // 0xe748bc: LeaveFrame
    //     0xe748bc: mov             SP, fp
    //     0xe748c0: ldp             fp, lr, [SP], #0x10
    // 0xe748c4: ret
    //     0xe748c4: ret             
    // 0xe748c8: sub             SP, fp, #0x98
    // 0xe748cc: r1 = Null
    //     0xe748cc: mov             x1, NULL
    // 0xe748d0: r2 = 4
    //     0xe748d0: movz            x2, #0x4
    // 0xe748d4: r0 = AllocateArray()
    //     0xe748d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe748d8: r16 = "Unknown color: "
    //     0xe748d8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed10] "Unknown color: "
    //     0xe748dc: ldr             x16, [x16, #0xd10]
    // 0xe748e0: StoreField: r0->field_f = r16
    //     0xe748e0: stur            w16, [x0, #0xf]
    // 0xe748e4: ldur            x1, [fp, #-0x38]
    // 0xe748e8: StoreField: r0->field_13 = r1
    //     0xe748e8: stur            w1, [x0, #0x13]
    // 0xe748ec: str             x0, [SP]
    // 0xe748f0: r0 = _interpolate()
    //     0xe748f0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe748f4: mov             x1, x0
    // 0xe748f8: r0 = print()
    //     0xe748f8: bl              #0x63fe38  ; [dart:core] ::print
    // 0xe748fc: r0 = Instance_SvgColor
    //     0xe748fc: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3ed00] Obj!SvgColor@e0c5e1
    //     0xe74900: ldr             x0, [x0, #0xd00]
    // 0xe74904: LeaveFrame
    //     0xe74904: mov             SP, fp
    //     0xe74908: ldp             fp, lr, [SP], #0x10
    // 0xe7490c: ret
    //     0xe7490c: ret             
    // 0xe74910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe74910: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe74914: b               #0xe740b8
    // 0xe74918: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe74918: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7491c: r0 = RangeErrorSharedWithFPURegs()
    //     0xe7491c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe74920: r0 = RangeErrorSharedWithFPURegs()
    //     0xe74920: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe74924: r0 = RangeErrorSharedWithFPURegs()
    //     0xe74924: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe74928: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe74928: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7492c: r0 = RangeErrorSharedWithFPURegs()
    //     0xe7492c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe74930: r0 = RangeErrorSharedWithFPURegs()
    //     0xe74930: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe74934: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe74934: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe74938: r0 = RangeErrorSharedWithFPURegs()
    //     0xe74938: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe7493c: r0 = RangeErrorSharedWithFPURegs()
    //     0xe7493c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe74940: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe74940: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setStrokeColor(/* No info */) {
    // ** addr: 0xea1640, size: 0x58
    // 0xea1640: EnterFrame
    //     0xea1640: stp             fp, lr, [SP, #-0x10]!
    //     0xea1644: mov             fp, SP
    // 0xea1648: mov             x0, x1
    // 0xea164c: mov             x1, x3
    // 0xea1650: CheckStackOverflow
    //     0xea1650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1654: cmp             SP, x16
    //     0xea1658: b.ls            #0xea1690
    // 0xea165c: LoadField: r2 = r0->field_7
    //     0xea165c: ldur            w2, [x0, #7]
    // 0xea1660: DecompressPointer r2
    //     0xea1660: add             x2, x2, HEAP, lsl #32
    // 0xea1664: cmp             w2, NULL
    // 0xea1668: b.ne            #0xea167c
    // 0xea166c: r0 = Null
    //     0xea166c: mov             x0, NULL
    // 0xea1670: LeaveFrame
    //     0xea1670: mov             SP, fp
    //     0xea1674: ldp             fp, lr, [SP], #0x10
    // 0xea1678: ret
    //     0xea1678: ret             
    // 0xea167c: r0 = setStrokeColor()
    //     0xea167c: bl              #0xe65994  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setStrokeColor
    // 0xea1680: r0 = Null
    //     0xea1680: mov             x0, NULL
    // 0xea1684: LeaveFrame
    //     0xea1684: mov             SP, fp
    //     0xea1688: ldp             fp, lr, [SP], #0x10
    // 0xea168c: ret
    //     0xea168c: ret             
    // 0xea1690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1690: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1694: b               #0xea165c
  }
  _ setFillColor(/* No info */) {
    // ** addr: 0xea1b00, size: 0x58
    // 0xea1b00: EnterFrame
    //     0xea1b00: stp             fp, lr, [SP, #-0x10]!
    //     0xea1b04: mov             fp, SP
    // 0xea1b08: mov             x0, x1
    // 0xea1b0c: mov             x1, x3
    // 0xea1b10: CheckStackOverflow
    //     0xea1b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1b14: cmp             SP, x16
    //     0xea1b18: b.ls            #0xea1b50
    // 0xea1b1c: LoadField: r2 = r0->field_7
    //     0xea1b1c: ldur            w2, [x0, #7]
    // 0xea1b20: DecompressPointer r2
    //     0xea1b20: add             x2, x2, HEAP, lsl #32
    // 0xea1b24: cmp             w2, NULL
    // 0xea1b28: b.ne            #0xea1b3c
    // 0xea1b2c: r0 = Null
    //     0xea1b2c: mov             x0, NULL
    // 0xea1b30: LeaveFrame
    //     0xea1b30: mov             SP, fp
    //     0xea1b34: ldp             fp, lr, [SP], #0x10
    // 0xea1b38: ret
    //     0xea1b38: ret             
    // 0xea1b3c: r0 = setFillColor()
    //     0xea1b3c: bl              #0xe64b2c  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setFillColor
    // 0xea1b40: r0 = Null
    //     0xea1b40: mov             x0, NULL
    // 0xea1b44: LeaveFrame
    //     0xea1b44: mov             SP, fp
    //     0xea1b48: ldp             fp, lr, [SP], #0x10
    // 0xea1b4c: ret
    //     0xea1b4c: ret             
    // 0xea1b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1b54: b               #0xea1b1c
  }
}
