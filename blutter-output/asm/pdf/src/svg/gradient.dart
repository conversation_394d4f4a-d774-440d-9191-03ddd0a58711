// lib: , url: package:pdf/src/svg/gradient.dart

// class id: 1050830, size: 0x8
class :: {
}

// class id: 850, size: 0x28, field offset: 0x14
//   const constructor, 
abstract class SvgGradient extends SvgColor {

  _ setStrokeColor(/* No info */) {
    // ** addr: 0xea14e4, size: 0x7c
    // 0xea14e4: EnterFrame
    //     0xea14e4: stp             fp, lr, [SP, #-0x10]!
    //     0xea14e8: mov             fp, SP
    // 0xea14ec: AllocStack(0x8)
    //     0xea14ec: sub             SP, SP, #8
    // 0xea14f0: SetupParameters(dynamic _ /* r3 => r4, fp-0x8 */)
    //     0xea14f0: mov             x4, x3
    //     0xea14f4: stur            x3, [fp, #-8]
    // 0xea14f8: CheckStackOverflow
    //     0xea14f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea14fc: cmp             SP, x16
    //     0xea1500: b.ls            #0xea1558
    // 0xea1504: LoadField: r5 = r1->field_1b
    //     0xea1504: ldur            w5, [x1, #0x1b]
    // 0xea1508: DecompressPointer r5
    //     0xea1508: add             x5, x5, HEAP, lsl #32
    // 0xea150c: LoadField: r0 = r5->field_b
    //     0xea150c: ldur            w0, [x5, #0xb]
    // 0xea1510: cbnz            w0, #0xea1524
    // 0xea1514: r0 = Null
    //     0xea1514: mov             x0, NULL
    // 0xea1518: LeaveFrame
    //     0xea1518: mov             SP, fp
    //     0xea151c: ldp             fp, lr, [SP], #0x10
    // 0xea1520: ret
    //     0xea1520: ret             
    // 0xea1524: r0 = LoadClassIdInstr(r1)
    //     0xea1524: ldur            x0, [x1, #-1]
    //     0xea1528: ubfx            x0, x0, #0xc, #0x14
    // 0xea152c: mov             x3, x4
    // 0xea1530: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea1530: sub             lr, x0, #1, lsl #12
    //     0xea1534: ldr             lr, [x21, lr, lsl #3]
    //     0xea1538: blr             lr
    // 0xea153c: ldur            x1, [fp, #-8]
    // 0xea1540: mov             x2, x0
    // 0xea1544: r0 = setStrokePattern()
    //     0xea1544: bl              #0xea1560  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setStrokePattern
    // 0xea1548: r0 = Null
    //     0xea1548: mov             x0, NULL
    // 0xea154c: LeaveFrame
    //     0xea154c: mov             SP, fp
    //     0xea1550: ldp             fp, lr, [SP], #0x10
    // 0xea1554: ret
    //     0xea1554: ret             
    // 0xea1558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1558: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea155c: b               #0xea1504
  }
  _ setFillColor(/* No info */) {
    // ** addr: 0xea1698, size: 0x21c
    // 0xea1698: EnterFrame
    //     0xea1698: stp             fp, lr, [SP, #-0x10]!
    //     0xea169c: mov             fp, SP
    // 0xea16a0: AllocStack(0x50)
    //     0xea16a0: sub             SP, SP, #0x50
    // 0xea16a4: SetupParameters(SvgGradient this /* r1 => r7, fp-0x10 */, dynamic _ /* r2 => r6, fp-0x18 */, dynamic _ /* r3 => r4, fp-0x20 */)
    //     0xea16a4: mov             x7, x1
    //     0xea16a8: mov             x6, x2
    //     0xea16ac: mov             x4, x3
    //     0xea16b0: stur            x1, [fp, #-0x10]
    //     0xea16b4: stur            x2, [fp, #-0x18]
    //     0xea16b8: stur            x3, [fp, #-0x20]
    // 0xea16bc: CheckStackOverflow
    //     0xea16bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea16c0: cmp             SP, x16
    //     0xea16c4: b.ls            #0xea18a8
    // 0xea16c8: LoadField: r8 = r7->field_1b
    //     0xea16c8: ldur            w8, [x7, #0x1b]
    // 0xea16cc: DecompressPointer r8
    //     0xea16cc: add             x8, x8, HEAP, lsl #32
    // 0xea16d0: stur            x8, [fp, #-8]
    // 0xea16d4: LoadField: r0 = r8->field_b
    //     0xea16d4: ldur            w0, [x8, #0xb]
    // 0xea16d8: cbnz            w0, #0xea16ec
    // 0xea16dc: r0 = Null
    //     0xea16dc: mov             x0, NULL
    // 0xea16e0: LeaveFrame
    //     0xea16e0: mov             SP, fp
    //     0xea16e4: ldp             fp, lr, [SP], #0x10
    // 0xea16e8: ret
    //     0xea16e8: ret             
    // 0xea16ec: r0 = LoadClassIdInstr(r7)
    //     0xea16ec: ldur            x0, [x7, #-1]
    //     0xea16f0: ubfx            x0, x0, #0xc, #0x14
    // 0xea16f4: mov             x1, x7
    // 0xea16f8: mov             x2, x6
    // 0xea16fc: mov             x3, x4
    // 0xea1700: mov             x5, x8
    // 0xea1704: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea1704: sub             lr, x0, #1, lsl #12
    //     0xea1708: ldr             lr, [x21, lr, lsl #3]
    //     0xea170c: blr             lr
    // 0xea1710: ldur            x1, [fp, #-0x20]
    // 0xea1714: mov             x2, x0
    // 0xea1718: r0 = setFillPattern()
    //     0xea1718: bl              #0xea18b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setFillPattern
    // 0xea171c: ldur            x0, [fp, #-0x10]
    // 0xea1720: LoadField: r3 = r0->field_23
    //     0xea1720: ldur            w3, [x0, #0x23]
    // 0xea1724: DecompressPointer r3
    //     0xea1724: add             x3, x3, HEAP, lsl #32
    // 0xea1728: stur            x3, [fp, #-0x28]
    // 0xea172c: r1 = Function '<anonymous closure>':.
    //     0xea172c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51250] AnonymousClosure: (0xea1a10), in [package:pdf/src/svg/gradient.dart] SvgGradient::setFillColor (0xea1698)
    //     0xea1730: ldr             x1, [x1, #0x250]
    // 0xea1734: r2 = Null
    //     0xea1734: mov             x2, NULL
    // 0xea1738: r0 = AllocateClosure()
    //     0xea1738: bl              #0xec1630  ; AllocateClosureStub
    // 0xea173c: ldur            x1, [fp, #-0x28]
    // 0xea1740: mov             x2, x0
    // 0xea1744: r0 = any()
    //     0xea1744: bl              #0x7b4a2c  ; [dart:collection] ListBase::any
    // 0xea1748: tbnz            w0, #4, #0xea1898
    // 0xea174c: ldur            x1, [fp, #-0x10]
    // 0xea1750: ldur            x0, [fp, #-0x18]
    // 0xea1754: LoadField: r2 = r0->field_13
    //     0xea1754: ldur            w2, [x0, #0x13]
    // 0xea1758: DecompressPointer r2
    //     0xea1758: add             x2, x2, HEAP, lsl #32
    // 0xea175c: LoadField: r3 = r2->field_f
    //     0xea175c: ldur            w3, [x2, #0xf]
    // 0xea1760: DecompressPointer r3
    //     0xea1760: add             x3, x3, HEAP, lsl #32
    // 0xea1764: stur            x3, [fp, #-0x38]
    // 0xea1768: LoadField: r4 = r2->field_13
    //     0xea1768: ldur            w4, [x2, #0x13]
    // 0xea176c: DecompressPointer r4
    //     0xea176c: add             x4, x4, HEAP, lsl #32
    // 0xea1770: stur            x4, [fp, #-0x30]
    // 0xea1774: r0 = PdfSoftMask()
    //     0xea1774: bl              #0xe472f8  ; AllocatePdfSoftMaskStub -> PdfSoftMask (size=0x18)
    // 0xea1778: mov             x1, x0
    // 0xea177c: ldur            x2, [fp, #-0x38]
    // 0xea1780: ldur            x3, [fp, #-0x30]
    // 0xea1784: stur            x0, [fp, #-0x30]
    // 0xea1788: r0 = PdfSoftMask()
    //     0xea1788: bl              #0xe46b00  ; [package:pdf/src/pdf/obj/smask.dart] PdfSoftMask::PdfSoftMask
    // 0xea178c: r0 = PdfGraphicState()
    //     0xea178c: bl              #0xe473a8  ; AllocatePdfGraphicStateStub -> PdfGraphicState (size=0x1c)
    // 0xea1790: mov             x1, x0
    // 0xea1794: ldur            x0, [fp, #-0x30]
    // 0xea1798: StoreField: r1->field_13 = r0
    //     0xea1798: stur            w0, [x1, #0x13]
    // 0xea179c: mov             x2, x1
    // 0xea17a0: ldur            x1, [fp, #-0x20]
    // 0xea17a4: r0 = setGraphicState()
    //     0xea17a4: bl              #0xe47304  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setGraphicState
    // 0xea17a8: ldur            x0, [fp, #-0x30]
    // 0xea17ac: LoadField: r2 = r0->field_f
    //     0xea17ac: ldur            w2, [x0, #0xf]
    // 0xea17b0: DecompressPointer r2
    //     0xea17b0: add             x2, x2, HEAP, lsl #32
    // 0xea17b4: stur            x2, [fp, #-0x38]
    // 0xea17b8: cmp             w2, NULL
    // 0xea17bc: b.eq            #0xea18b0
    // 0xea17c0: ldur            x3, [fp, #-0x18]
    // 0xea17c4: r0 = LoadClassIdInstr(r3)
    //     0xea17c4: ldur            x0, [x3, #-1]
    //     0xea17c8: ubfx            x0, x0, #0xc, #0x14
    // 0xea17cc: mov             x1, x3
    // 0xea17d0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea17d0: sub             lr, x0, #1, lsl #12
    //     0xea17d4: ldr             lr, [x21, lr, lsl #3]
    //     0xea17d8: blr             lr
    // 0xea17dc: ldur            x1, [fp, #-0x38]
    // 0xea17e0: mov             x2, x0
    // 0xea17e4: r0 = drawBox()
    //     0xea17e4: bl              #0xe64ce4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawBox
    // 0xea17e8: r1 = Function '<anonymous closure>':.
    //     0xea17e8: add             x1, PP, #0x51, lsl #12  ; [pp+0x51258] AnonymousClosure: (0xea19d0), in [package:pdf/src/svg/gradient.dart] SvgGradient::setFillColor (0xea1698)
    //     0xea17ec: ldr             x1, [x1, #0x258]
    // 0xea17f0: r2 = Null
    //     0xea17f0: mov             x2, NULL
    // 0xea17f4: r0 = AllocateClosure()
    //     0xea17f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xea17f8: r16 = <PdfColor>
    //     0xea17f8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51260] TypeArguments: <PdfColor>
    //     0xea17fc: ldr             x16, [x16, #0x260]
    // 0xea1800: ldur            lr, [fp, #-0x28]
    // 0xea1804: stp             lr, x16, [SP, #8]
    // 0xea1808: str             x0, [SP]
    // 0xea180c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xea180c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xea1810: r0 = map()
    //     0xea1810: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xea1814: LoadField: r1 = r0->field_7
    //     0xea1814: ldur            w1, [x0, #7]
    // 0xea1818: DecompressPointer r1
    //     0xea1818: add             x1, x1, HEAP, lsl #32
    // 0xea181c: mov             x2, x0
    // 0xea1820: r0 = _GrowableList.of()
    //     0xea1820: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xea1824: ldur            x4, [fp, #-0x10]
    // 0xea1828: r1 = LoadClassIdInstr(r4)
    //     0xea1828: ldur            x1, [x4, #-1]
    //     0xea182c: ubfx            x1, x1, #0xc, #0x14
    // 0xea1830: mov             x5, x0
    // 0xea1834: mov             x0, x1
    // 0xea1838: mov             x1, x4
    // 0xea183c: ldur            x2, [fp, #-0x18]
    // 0xea1840: ldur            x3, [fp, #-0x38]
    // 0xea1844: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea1844: sub             lr, x0, #1, lsl #12
    //     0xea1848: ldr             lr, [x21, lr, lsl #3]
    //     0xea184c: blr             lr
    // 0xea1850: ldur            x1, [fp, #-0x38]
    // 0xea1854: mov             x2, x0
    // 0xea1858: r0 = setFillPattern()
    //     0xea1858: bl              #0xea18b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setFillPattern
    // 0xea185c: ldur            x1, [fp, #-0x38]
    // 0xea1860: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xea1860: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xea1864: r0 = fillPath()
    //     0xea1864: bl              #0xe496d4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::fillPath
    // 0xea1868: ldur            x1, [fp, #-0x10]
    // 0xea186c: r0 = LoadClassIdInstr(r1)
    //     0xea186c: ldur            x0, [x1, #-1]
    //     0xea1870: ubfx            x0, x0, #0xc, #0x14
    // 0xea1874: ldur            x2, [fp, #-0x18]
    // 0xea1878: ldur            x3, [fp, #-0x20]
    // 0xea187c: ldur            x5, [fp, #-8]
    // 0xea1880: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea1880: sub             lr, x0, #1, lsl #12
    //     0xea1884: ldr             lr, [x21, lr, lsl #3]
    //     0xea1888: blr             lr
    // 0xea188c: ldur            x1, [fp, #-0x20]
    // 0xea1890: mov             x2, x0
    // 0xea1894: r0 = setFillPattern()
    //     0xea1894: bl              #0xea18b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setFillPattern
    // 0xea1898: r0 = Null
    //     0xea1898: mov             x0, NULL
    // 0xea189c: LeaveFrame
    //     0xea189c: mov             SP, fp
    //     0xea18a0: ldp             fp, lr, [SP], #0x10
    // 0xea18a4: ret
    //     0xea18a4: ret             
    // 0xea18a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea18a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea18ac: b               #0xea16c8
    // 0xea18b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea18b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] PdfColor <anonymous closure>(dynamic, double) {
    // ** addr: 0xea19d0, size: 0x40
    // 0xea19d0: EnterFrame
    //     0xea19d0: stp             fp, lr, [SP, #-0x10]!
    //     0xea19d4: mov             fp, SP
    // 0xea19d8: AllocStack(0x8)
    //     0xea19d8: sub             SP, SP, #8
    // 0xea19dc: ldr             x0, [fp, #0x10]
    // 0xea19e0: LoadField: d0 = r0->field_7
    //     0xea19e0: ldur            d0, [x0, #7]
    // 0xea19e4: stur            d0, [fp, #-8]
    // 0xea19e8: r0 = PdfColor()
    //     0xea19e8: bl              #0xb121d4  ; AllocatePdfColorStub -> PdfColor (size=0x28)
    // 0xea19ec: ldur            d0, [fp, #-8]
    // 0xea19f0: StoreField: r0->field_f = d0
    //     0xea19f0: stur            d0, [x0, #0xf]
    // 0xea19f4: ArrayStore: r0[0] = d0  ; List_8
    //     0xea19f4: stur            d0, [x0, #0x17]
    // 0xea19f8: StoreField: r0->field_1f = d0
    //     0xea19f8: stur            d0, [x0, #0x1f]
    // 0xea19fc: d0 = 1.000000
    //     0xea19fc: fmov            d0, #1.00000000
    // 0xea1a00: StoreField: r0->field_7 = d0
    //     0xea1a00: stur            d0, [x0, #7]
    // 0xea1a04: LeaveFrame
    //     0xea1a04: mov             SP, fp
    //     0xea1a08: ldp             fp, lr, [SP], #0x10
    // 0xea1a0c: ret
    //     0xea1a0c: ret             
  }
  [closure] bool <anonymous closure>(dynamic, double) {
    // ** addr: 0xea1a10, size: 0x20
    // 0xea1a10: d0 = 1.000000
    //     0xea1a10: fmov            d0, #1.00000000
    // 0xea1a14: ldr             x1, [SP]
    // 0xea1a18: LoadField: d1 = r1->field_7
    //     0xea1a18: ldur            d1, [x1, #7]
    // 0xea1a1c: fcmp            d0, d1
    // 0xea1a20: r16 = true
    //     0xea1a20: add             x16, NULL, #0x20  ; true
    // 0xea1a24: r17 = false
    //     0xea1a24: add             x17, NULL, #0x30  ; false
    // 0xea1a28: csel            x0, x16, x17, gt
    // 0xea1a2c: ret
    //     0xea1a2c: ret             
  }
}

// class id: 851, size: 0x58, field offset: 0x28
//   const constructor, 
class SvgRadialGradient extends SvgGradient {

  _ toString(/* No info */) {
    // ** addr: 0xc36144, size: 0x3f0
    // 0xc36144: EnterFrame
    //     0xc36144: stp             fp, lr, [SP, #-0x10]!
    //     0xc36148: mov             fp, SP
    // 0xc3614c: AllocStack(0x8)
    //     0xc3614c: sub             SP, SP, #8
    // 0xc36150: CheckStackOverflow
    //     0xc36150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc36154: cmp             SP, x16
    //     0xc36158: b.ls            #0xc3649c
    // 0xc3615c: r1 = Null
    //     0xc3615c: mov             x1, NULL
    // 0xc36160: r2 = 42
    //     0xc36160: movz            x2, #0x2a
    // 0xc36164: r0 = AllocateArray()
    //     0xc36164: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc36168: mov             x2, x0
    // 0xc3616c: r16 = SvgRadialGradient
    //     0xc3616c: add             x16, PP, #0x46, lsl #12  ; [pp+0x46e78] Type: SvgRadialGradient
    //     0xc36170: ldr             x16, [x16, #0xe78]
    // 0xc36174: StoreField: r2->field_f = r16
    //     0xc36174: stur            w16, [x2, #0xf]
    // 0xc36178: r16 = " userSpace:"
    //     0xc36178: add             x16, PP, #0x46, lsl #12  ; [pp+0x46e80] " userSpace:"
    //     0xc3617c: ldr             x16, [x16, #0xe80]
    // 0xc36180: StoreField: r2->field_13 = r16
    //     0xc36180: stur            w16, [x2, #0x13]
    // 0xc36184: ldr             x3, [fp, #0x10]
    // 0xc36188: LoadField: r0 = r3->field_13
    //     0xc36188: ldur            w0, [x3, #0x13]
    // 0xc3618c: DecompressPointer r0
    //     0xc3618c: add             x0, x0, HEAP, lsl #32
    // 0xc36190: ArrayStore: r2[0] = r0  ; List_4
    //     0xc36190: stur            w0, [x2, #0x17]
    // 0xc36194: r16 = " cx:"
    //     0xc36194: add             x16, PP, #0x46, lsl #12  ; [pp+0x46e88] " cx:"
    //     0xc36198: ldr             x16, [x16, #0xe88]
    // 0xc3619c: StoreField: r2->field_1b = r16
    //     0xc3619c: stur            w16, [x2, #0x1b]
    // 0xc361a0: LoadField: d0 = r3->field_2f
    //     0xc361a0: ldur            d0, [x3, #0x2f]
    // 0xc361a4: r0 = inline_Allocate_Double()
    //     0xc361a4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc361a8: add             x0, x0, #0x10
    //     0xc361ac: cmp             x1, x0
    //     0xc361b0: b.ls            #0xc364a4
    //     0xc361b4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc361b8: sub             x0, x0, #0xf
    //     0xc361bc: movz            x1, #0xe15c
    //     0xc361c0: movk            x1, #0x3, lsl #16
    //     0xc361c4: stur            x1, [x0, #-1]
    // 0xc361c8: StoreField: r0->field_7 = d0
    //     0xc361c8: stur            d0, [x0, #7]
    // 0xc361cc: mov             x1, x2
    // 0xc361d0: ArrayStore: r1[4] = r0  ; List_4
    //     0xc361d0: add             x25, x1, #0x1f
    //     0xc361d4: str             w0, [x25]
    //     0xc361d8: tbz             w0, #0, #0xc361f4
    //     0xc361dc: ldurb           w16, [x1, #-1]
    //     0xc361e0: ldurb           w17, [x0, #-1]
    //     0xc361e4: and             x16, x17, x16, lsr #2
    //     0xc361e8: tst             x16, HEAP, lsr #32
    //     0xc361ec: b.eq            #0xc361f4
    //     0xc361f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc361f4: r16 = " cy:"
    //     0xc361f4: add             x16, PP, #0x46, lsl #12  ; [pp+0x46e90] " cy:"
    //     0xc361f8: ldr             x16, [x16, #0xe90]
    // 0xc361fc: StoreField: r2->field_23 = r16
    //     0xc361fc: stur            w16, [x2, #0x23]
    // 0xc36200: LoadField: d0 = r3->field_37
    //     0xc36200: ldur            d0, [x3, #0x37]
    // 0xc36204: r0 = inline_Allocate_Double()
    //     0xc36204: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc36208: add             x0, x0, #0x10
    //     0xc3620c: cmp             x1, x0
    //     0xc36210: b.ls            #0xc364bc
    //     0xc36214: str             x0, [THR, #0x50]  ; THR::top
    //     0xc36218: sub             x0, x0, #0xf
    //     0xc3621c: movz            x1, #0xe15c
    //     0xc36220: movk            x1, #0x3, lsl #16
    //     0xc36224: stur            x1, [x0, #-1]
    // 0xc36228: StoreField: r0->field_7 = d0
    //     0xc36228: stur            d0, [x0, #7]
    // 0xc3622c: mov             x1, x2
    // 0xc36230: ArrayStore: r1[6] = r0  ; List_4
    //     0xc36230: add             x25, x1, #0x27
    //     0xc36234: str             w0, [x25]
    //     0xc36238: tbz             w0, #0, #0xc36254
    //     0xc3623c: ldurb           w16, [x1, #-1]
    //     0xc36240: ldurb           w17, [x0, #-1]
    //     0xc36244: and             x16, x17, x16, lsr #2
    //     0xc36248: tst             x16, HEAP, lsr #32
    //     0xc3624c: b.eq            #0xc36254
    //     0xc36250: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc36254: r16 = " r:"
    //     0xc36254: add             x16, PP, #0x46, lsl #12  ; [pp+0x46e98] " r:"
    //     0xc36258: ldr             x16, [x16, #0xe98]
    // 0xc3625c: StoreField: r2->field_2b = r16
    //     0xc3625c: stur            w16, [x2, #0x2b]
    // 0xc36260: LoadField: d0 = r3->field_27
    //     0xc36260: ldur            d0, [x3, #0x27]
    // 0xc36264: r0 = inline_Allocate_Double()
    //     0xc36264: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc36268: add             x0, x0, #0x10
    //     0xc3626c: cmp             x1, x0
    //     0xc36270: b.ls            #0xc364d4
    //     0xc36274: str             x0, [THR, #0x50]  ; THR::top
    //     0xc36278: sub             x0, x0, #0xf
    //     0xc3627c: movz            x1, #0xe15c
    //     0xc36280: movk            x1, #0x3, lsl #16
    //     0xc36284: stur            x1, [x0, #-1]
    // 0xc36288: StoreField: r0->field_7 = d0
    //     0xc36288: stur            d0, [x0, #7]
    // 0xc3628c: mov             x1, x2
    // 0xc36290: ArrayStore: r1[8] = r0  ; List_4
    //     0xc36290: add             x25, x1, #0x2f
    //     0xc36294: str             w0, [x25]
    //     0xc36298: tbz             w0, #0, #0xc362b4
    //     0xc3629c: ldurb           w16, [x1, #-1]
    //     0xc362a0: ldurb           w17, [x0, #-1]
    //     0xc362a4: and             x16, x17, x16, lsr #2
    //     0xc362a8: tst             x16, HEAP, lsr #32
    //     0xc362ac: b.eq            #0xc362b4
    //     0xc362b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc362b4: r16 = " fx:"
    //     0xc362b4: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ea0] " fx:"
    //     0xc362b8: ldr             x16, [x16, #0xea0]
    // 0xc362bc: StoreField: r2->field_33 = r16
    //     0xc362bc: stur            w16, [x2, #0x33]
    // 0xc362c0: LoadField: d0 = r3->field_47
    //     0xc362c0: ldur            d0, [x3, #0x47]
    // 0xc362c4: r0 = inline_Allocate_Double()
    //     0xc362c4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc362c8: add             x0, x0, #0x10
    //     0xc362cc: cmp             x1, x0
    //     0xc362d0: b.ls            #0xc364ec
    //     0xc362d4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc362d8: sub             x0, x0, #0xf
    //     0xc362dc: movz            x1, #0xe15c
    //     0xc362e0: movk            x1, #0x3, lsl #16
    //     0xc362e4: stur            x1, [x0, #-1]
    // 0xc362e8: StoreField: r0->field_7 = d0
    //     0xc362e8: stur            d0, [x0, #7]
    // 0xc362ec: mov             x1, x2
    // 0xc362f0: ArrayStore: r1[10] = r0  ; List_4
    //     0xc362f0: add             x25, x1, #0x37
    //     0xc362f4: str             w0, [x25]
    //     0xc362f8: tbz             w0, #0, #0xc36314
    //     0xc362fc: ldurb           w16, [x1, #-1]
    //     0xc36300: ldurb           w17, [x0, #-1]
    //     0xc36304: and             x16, x17, x16, lsr #2
    //     0xc36308: tst             x16, HEAP, lsr #32
    //     0xc3630c: b.eq            #0xc36314
    //     0xc36310: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc36314: r16 = " fy:"
    //     0xc36314: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ea8] " fy:"
    //     0xc36318: ldr             x16, [x16, #0xea8]
    // 0xc3631c: StoreField: r2->field_3b = r16
    //     0xc3631c: stur            w16, [x2, #0x3b]
    // 0xc36320: LoadField: d0 = r3->field_4f
    //     0xc36320: ldur            d0, [x3, #0x4f]
    // 0xc36324: r0 = inline_Allocate_Double()
    //     0xc36324: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc36328: add             x0, x0, #0x10
    //     0xc3632c: cmp             x1, x0
    //     0xc36330: b.ls            #0xc36504
    //     0xc36334: str             x0, [THR, #0x50]  ; THR::top
    //     0xc36338: sub             x0, x0, #0xf
    //     0xc3633c: movz            x1, #0xe15c
    //     0xc36340: movk            x1, #0x3, lsl #16
    //     0xc36344: stur            x1, [x0, #-1]
    // 0xc36348: StoreField: r0->field_7 = d0
    //     0xc36348: stur            d0, [x0, #7]
    // 0xc3634c: mov             x1, x2
    // 0xc36350: ArrayStore: r1[12] = r0  ; List_4
    //     0xc36350: add             x25, x1, #0x3f
    //     0xc36354: str             w0, [x25]
    //     0xc36358: tbz             w0, #0, #0xc36374
    //     0xc3635c: ldurb           w16, [x1, #-1]
    //     0xc36360: ldurb           w17, [x0, #-1]
    //     0xc36364: and             x16, x17, x16, lsr #2
    //     0xc36368: tst             x16, HEAP, lsr #32
    //     0xc3636c: b.eq            #0xc36374
    //     0xc36370: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc36374: r16 = " fr:"
    //     0xc36374: add             x16, PP, #0x46, lsl #12  ; [pp+0x46eb0] " fr:"
    //     0xc36378: ldr             x16, [x16, #0xeb0]
    // 0xc3637c: StoreField: r2->field_43 = r16
    //     0xc3637c: stur            w16, [x2, #0x43]
    // 0xc36380: LoadField: d0 = r3->field_3f
    //     0xc36380: ldur            d0, [x3, #0x3f]
    // 0xc36384: r0 = inline_Allocate_Double()
    //     0xc36384: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc36388: add             x0, x0, #0x10
    //     0xc3638c: cmp             x1, x0
    //     0xc36390: b.ls            #0xc3651c
    //     0xc36394: str             x0, [THR, #0x50]  ; THR::top
    //     0xc36398: sub             x0, x0, #0xf
    //     0xc3639c: movz            x1, #0xe15c
    //     0xc363a0: movk            x1, #0x3, lsl #16
    //     0xc363a4: stur            x1, [x0, #-1]
    // 0xc363a8: StoreField: r0->field_7 = d0
    //     0xc363a8: stur            d0, [x0, #7]
    // 0xc363ac: mov             x1, x2
    // 0xc363b0: ArrayStore: r1[14] = r0  ; List_4
    //     0xc363b0: add             x25, x1, #0x47
    //     0xc363b4: str             w0, [x25]
    //     0xc363b8: tbz             w0, #0, #0xc363d4
    //     0xc363bc: ldurb           w16, [x1, #-1]
    //     0xc363c0: ldurb           w17, [x0, #-1]
    //     0xc363c4: and             x16, x17, x16, lsr #2
    //     0xc363c8: tst             x16, HEAP, lsr #32
    //     0xc363cc: b.eq            #0xc363d4
    //     0xc363d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc363d4: r16 = " colors:"
    //     0xc363d4: add             x16, PP, #0x46, lsl #12  ; [pp+0x46eb8] " colors:"
    //     0xc363d8: ldr             x16, [x16, #0xeb8]
    // 0xc363dc: StoreField: r2->field_4b = r16
    //     0xc363dc: stur            w16, [x2, #0x4b]
    // 0xc363e0: LoadField: r0 = r3->field_1b
    //     0xc363e0: ldur            w0, [x3, #0x1b]
    // 0xc363e4: DecompressPointer r0
    //     0xc363e4: add             x0, x0, HEAP, lsl #32
    // 0xc363e8: mov             x1, x2
    // 0xc363ec: ArrayStore: r1[16] = r0  ; List_4
    //     0xc363ec: add             x25, x1, #0x4f
    //     0xc363f0: str             w0, [x25]
    //     0xc363f4: tbz             w0, #0, #0xc36410
    //     0xc363f8: ldurb           w16, [x1, #-1]
    //     0xc363fc: ldurb           w17, [x0, #-1]
    //     0xc36400: and             x16, x17, x16, lsr #2
    //     0xc36404: tst             x16, HEAP, lsr #32
    //     0xc36408: b.eq            #0xc36410
    //     0xc3640c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc36410: r16 = " stops:"
    //     0xc36410: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ec0] " stops:"
    //     0xc36414: ldr             x16, [x16, #0xec0]
    // 0xc36418: StoreField: r2->field_53 = r16
    //     0xc36418: stur            w16, [x2, #0x53]
    // 0xc3641c: LoadField: r0 = r3->field_1f
    //     0xc3641c: ldur            w0, [x3, #0x1f]
    // 0xc36420: DecompressPointer r0
    //     0xc36420: add             x0, x0, HEAP, lsl #32
    // 0xc36424: mov             x1, x2
    // 0xc36428: ArrayStore: r1[18] = r0  ; List_4
    //     0xc36428: add             x25, x1, #0x57
    //     0xc3642c: str             w0, [x25]
    //     0xc36430: tbz             w0, #0, #0xc3644c
    //     0xc36434: ldurb           w16, [x1, #-1]
    //     0xc36438: ldurb           w17, [x0, #-1]
    //     0xc3643c: and             x16, x17, x16, lsr #2
    //     0xc36440: tst             x16, HEAP, lsr #32
    //     0xc36444: b.eq            #0xc3644c
    //     0xc36448: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3644c: r16 = " opacityList:"
    //     0xc3644c: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ec8] " opacityList:"
    //     0xc36450: ldr             x16, [x16, #0xec8]
    // 0xc36454: StoreField: r2->field_5b = r16
    //     0xc36454: stur            w16, [x2, #0x5b]
    // 0xc36458: LoadField: r0 = r3->field_23
    //     0xc36458: ldur            w0, [x3, #0x23]
    // 0xc3645c: DecompressPointer r0
    //     0xc3645c: add             x0, x0, HEAP, lsl #32
    // 0xc36460: mov             x1, x2
    // 0xc36464: ArrayStore: r1[20] = r0  ; List_4
    //     0xc36464: add             x25, x1, #0x5f
    //     0xc36468: str             w0, [x25]
    //     0xc3646c: tbz             w0, #0, #0xc36488
    //     0xc36470: ldurb           w16, [x1, #-1]
    //     0xc36474: ldurb           w17, [x0, #-1]
    //     0xc36478: and             x16, x17, x16, lsr #2
    //     0xc3647c: tst             x16, HEAP, lsr #32
    //     0xc36480: b.eq            #0xc36488
    //     0xc36484: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc36488: str             x2, [SP]
    // 0xc3648c: r0 = _interpolate()
    //     0xc3648c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc36490: LeaveFrame
    //     0xc36490: mov             SP, fp
    //     0xc36494: ldp             fp, lr, [SP], #0x10
    // 0xc36498: ret
    //     0xc36498: ret             
    // 0xc3649c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3649c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc364a0: b               #0xc3615c
    // 0xc364a4: SaveReg d0
    //     0xc364a4: str             q0, [SP, #-0x10]!
    // 0xc364a8: stp             x2, x3, [SP, #-0x10]!
    // 0xc364ac: r0 = AllocateDouble()
    //     0xc364ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc364b0: ldp             x2, x3, [SP], #0x10
    // 0xc364b4: RestoreReg d0
    //     0xc364b4: ldr             q0, [SP], #0x10
    // 0xc364b8: b               #0xc361c8
    // 0xc364bc: SaveReg d0
    //     0xc364bc: str             q0, [SP, #-0x10]!
    // 0xc364c0: stp             x2, x3, [SP, #-0x10]!
    // 0xc364c4: r0 = AllocateDouble()
    //     0xc364c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc364c8: ldp             x2, x3, [SP], #0x10
    // 0xc364cc: RestoreReg d0
    //     0xc364cc: ldr             q0, [SP], #0x10
    // 0xc364d0: b               #0xc36228
    // 0xc364d4: SaveReg d0
    //     0xc364d4: str             q0, [SP, #-0x10]!
    // 0xc364d8: stp             x2, x3, [SP, #-0x10]!
    // 0xc364dc: r0 = AllocateDouble()
    //     0xc364dc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc364e0: ldp             x2, x3, [SP], #0x10
    // 0xc364e4: RestoreReg d0
    //     0xc364e4: ldr             q0, [SP], #0x10
    // 0xc364e8: b               #0xc36288
    // 0xc364ec: SaveReg d0
    //     0xc364ec: str             q0, [SP, #-0x10]!
    // 0xc364f0: stp             x2, x3, [SP, #-0x10]!
    // 0xc364f4: r0 = AllocateDouble()
    //     0xc364f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc364f8: ldp             x2, x3, [SP], #0x10
    // 0xc364fc: RestoreReg d0
    //     0xc364fc: ldr             q0, [SP], #0x10
    // 0xc36500: b               #0xc362e8
    // 0xc36504: SaveReg d0
    //     0xc36504: str             q0, [SP, #-0x10]!
    // 0xc36508: stp             x2, x3, [SP, #-0x10]!
    // 0xc3650c: r0 = AllocateDouble()
    //     0xc3650c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc36510: ldp             x2, x3, [SP], #0x10
    // 0xc36514: RestoreReg d0
    //     0xc36514: ldr             q0, [SP], #0x10
    // 0xc36518: b               #0xc36348
    // 0xc3651c: SaveReg d0
    //     0xc3651c: str             q0, [SP, #-0x10]!
    // 0xc36520: stp             x2, x3, [SP, #-0x10]!
    // 0xc36524: r0 = AllocateDouble()
    //     0xc36524: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc36528: ldp             x2, x3, [SP], #0x10
    // 0xc3652c: RestoreReg d0
    //     0xc3652c: ldr             q0, [SP], #0x10
    // 0xc36530: b               #0xc363a8
  }
  factory _ SvgRadialGradient.fromXml(/* No info */) {
    // ** addr: 0xe74c48, size: 0x944
    // 0xe74c48: EnterFrame
    //     0xe74c48: stp             fp, lr, [SP, #-0x10]!
    //     0xe74c4c: mov             fp, SP
    // 0xe74c50: AllocStack(0xa0)
    //     0xe74c50: sub             SP, SP, #0xa0
    // 0xe74c54: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xe74c54: mov             x4, x2
    //     0xe74c58: mov             x0, x3
    //     0xe74c5c: stur            x2, [fp, #-8]
    //     0xe74c60: stur            x3, [fp, #-0x10]
    // 0xe74c64: CheckStackOverflow
    //     0xe74c64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe74c68: cmp             SP, x16
    //     0xe74c6c: b.ls            #0xe7550c
    // 0xe74c70: r16 = 0.500000
    //     0xe74c70: ldr             x16, [PP, #0x4928]  ; [pp+0x4928] 0.5
    // 0xe74c74: str             x16, [SP]
    // 0xe74c78: mov             x1, x4
    // 0xe74c7c: r2 = "r"
    //     0xe74c7c: add             x2, PP, #0x26, lsl #12  ; [pp+0x262f0] "r"
    //     0xe74c80: ldr             x2, [x2, #0x2f0]
    // 0xe74c84: r3 = Null
    //     0xe74c84: mov             x3, NULL
    // 0xe74c88: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe74c88: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe74c8c: ldr             x4, [x4, #0xaf0]
    // 0xe74c90: r0 = getNumeric()
    //     0xe74c90: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe74c94: mov             x1, x0
    // 0xe74c98: r0 = sizeValue()
    //     0xe74c98: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe74c9c: stur            d0, [fp, #-0x58]
    // 0xe74ca0: r16 = 0.500000
    //     0xe74ca0: ldr             x16, [PP, #0x4928]  ; [pp+0x4928] 0.5
    // 0xe74ca4: str             x16, [SP]
    // 0xe74ca8: ldur            x1, [fp, #-8]
    // 0xe74cac: r2 = "cx"
    //     0xe74cac: add             x2, PP, #0x26, lsl #12  ; [pp+0x26120] "cx"
    //     0xe74cb0: ldr             x2, [x2, #0x120]
    // 0xe74cb4: r3 = Null
    //     0xe74cb4: mov             x3, NULL
    // 0xe74cb8: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe74cb8: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe74cbc: ldr             x4, [x4, #0xaf0]
    // 0xe74cc0: r0 = getNumeric()
    //     0xe74cc0: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe74cc4: mov             x1, x0
    // 0xe74cc8: r0 = sizeValue()
    //     0xe74cc8: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe74ccc: stur            d0, [fp, #-0x60]
    // 0xe74cd0: r16 = 0.500000
    //     0xe74cd0: ldr             x16, [PP, #0x4928]  ; [pp+0x4928] 0.5
    // 0xe74cd4: str             x16, [SP]
    // 0xe74cd8: ldur            x1, [fp, #-8]
    // 0xe74cdc: r2 = "cy"
    //     0xe74cdc: add             x2, PP, #9, lsl #12  ; [pp+0x9560] "cy"
    //     0xe74ce0: ldr             x2, [x2, #0x560]
    // 0xe74ce4: r3 = Null
    //     0xe74ce4: mov             x3, NULL
    // 0xe74ce8: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe74ce8: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe74cec: ldr             x4, [x4, #0xaf0]
    // 0xe74cf0: r0 = getNumeric()
    //     0xe74cf0: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe74cf4: mov             x1, x0
    // 0xe74cf8: r0 = sizeValue()
    //     0xe74cf8: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe74cfc: stur            d0, [fp, #-0x68]
    // 0xe74d00: r16 = 0.000000
    //     0xe74d00: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe74d04: str             x16, [SP]
    // 0xe74d08: ldur            x1, [fp, #-8]
    // 0xe74d0c: r2 = "fr"
    //     0xe74d0c: add             x2, PP, #9, lsl #12  ; [pp+0x96b0] "fr"
    //     0xe74d10: ldr             x2, [x2, #0x6b0]
    // 0xe74d14: r3 = Null
    //     0xe74d14: mov             x3, NULL
    // 0xe74d18: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe74d18: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe74d1c: ldr             x4, [x4, #0xaf0]
    // 0xe74d20: r0 = getNumeric()
    //     0xe74d20: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe74d24: mov             x1, x0
    // 0xe74d28: r0 = sizeValue()
    //     0xe74d28: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe74d2c: mov             v1.16b, v0.16b
    // 0xe74d30: ldur            d0, [fp, #-0x60]
    // 0xe74d34: stur            d1, [fp, #-0x70]
    // 0xe74d38: r0 = inline_Allocate_Double()
    //     0xe74d38: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe74d3c: add             x0, x0, #0x10
    //     0xe74d40: cmp             x1, x0
    //     0xe74d44: b.ls            #0xe75514
    //     0xe74d48: str             x0, [THR, #0x50]  ; THR::top
    //     0xe74d4c: sub             x0, x0, #0xf
    //     0xe74d50: movz            x1, #0xe15c
    //     0xe74d54: movk            x1, #0x3, lsl #16
    //     0xe74d58: stur            x1, [x0, #-1]
    // 0xe74d5c: StoreField: r0->field_7 = d0
    //     0xe74d5c: stur            d0, [x0, #7]
    // 0xe74d60: str             x0, [SP]
    // 0xe74d64: ldur            x1, [fp, #-8]
    // 0xe74d68: r2 = "fx"
    //     0xe74d68: add             x2, PP, #0x26, lsl #12  ; [pp+0x26418] "fx"
    //     0xe74d6c: ldr             x2, [x2, #0x418]
    // 0xe74d70: r3 = Null
    //     0xe74d70: mov             x3, NULL
    // 0xe74d74: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe74d74: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe74d78: ldr             x4, [x4, #0xaf0]
    // 0xe74d7c: r0 = getNumeric()
    //     0xe74d7c: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe74d80: mov             x1, x0
    // 0xe74d84: r0 = sizeValue()
    //     0xe74d84: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe74d88: mov             v1.16b, v0.16b
    // 0xe74d8c: ldur            d0, [fp, #-0x68]
    // 0xe74d90: stur            d1, [fp, #-0x78]
    // 0xe74d94: r0 = inline_Allocate_Double()
    //     0xe74d94: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe74d98: add             x0, x0, #0x10
    //     0xe74d9c: cmp             x1, x0
    //     0xe74da0: b.ls            #0xe75524
    //     0xe74da4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe74da8: sub             x0, x0, #0xf
    //     0xe74dac: movz            x1, #0xe15c
    //     0xe74db0: movk            x1, #0x3, lsl #16
    //     0xe74db4: stur            x1, [x0, #-1]
    // 0xe74db8: StoreField: r0->field_7 = d0
    //     0xe74db8: stur            d0, [x0, #7]
    // 0xe74dbc: str             x0, [SP]
    // 0xe74dc0: ldur            x1, [fp, #-8]
    // 0xe74dc4: r2 = "fy"
    //     0xe74dc4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26420] "fy"
    //     0xe74dc8: ldr             x2, [x2, #0x420]
    // 0xe74dcc: r3 = Null
    //     0xe74dcc: mov             x3, NULL
    // 0xe74dd0: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe74dd0: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe74dd4: ldr             x4, [x4, #0xaf0]
    // 0xe74dd8: r0 = getNumeric()
    //     0xe74dd8: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe74ddc: mov             x1, x0
    // 0xe74de0: r0 = sizeValue()
    //     0xe74de0: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe74de4: r1 = <PdfColor?>
    //     0xe74de4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ed18] TypeArguments: <PdfColor?>
    //     0xe74de8: ldr             x1, [x1, #0xd18]
    // 0xe74dec: r2 = 0
    //     0xe74dec: movz            x2, #0
    // 0xe74df0: stur            d0, [fp, #-0x80]
    // 0xe74df4: r0 = _GrowableList()
    //     0xe74df4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe74df8: r1 = <double>
    //     0xe74df8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe74dfc: r2 = 0
    //     0xe74dfc: movz            x2, #0
    // 0xe74e00: stur            x0, [fp, #-0x18]
    // 0xe74e04: r0 = _GrowableList()
    //     0xe74e04: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe74e08: r1 = <double>
    //     0xe74e08: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe74e0c: r2 = 0
    //     0xe74e0c: movz            x2, #0
    // 0xe74e10: stur            x0, [fp, #-0x20]
    // 0xe74e14: r0 = _GrowableList()
    //     0xe74e14: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe74e18: ldur            x1, [fp, #-8]
    // 0xe74e1c: stur            x0, [fp, #-0x28]
    // 0xe74e20: LoadField: r2 = r1->field_f
    //     0xe74e20: ldur            w2, [x1, #0xf]
    // 0xe74e24: DecompressPointer r2
    //     0xe74e24: add             x2, x2, HEAP, lsl #32
    // 0xe74e28: r16 = <XmlElement>
    //     0xe74e28: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea98] TypeArguments: <XmlElement>
    //     0xe74e2c: ldr             x16, [x16, #0xa98]
    // 0xe74e30: stp             x2, x16, [SP]
    // 0xe74e34: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe74e34: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe74e38: r0 = whereType()
    //     0xe74e38: bl              #0x7b5364  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::whereType
    // 0xe74e3c: r1 = Function '<anonymous closure>': static.
    //     0xe74e3c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ed20] AnonymousClosure: static (0xe756e4), in [package:pdf/src/svg/gradient.dart] SvgRadialGradient::SvgRadialGradient.fromXml (0xe74c48)
    //     0xe74e40: ldr             x1, [x1, #0xd20]
    // 0xe74e44: r2 = Null
    //     0xe74e44: mov             x2, NULL
    // 0xe74e48: stur            x0, [fp, #-0x30]
    // 0xe74e4c: r0 = AllocateClosure()
    //     0xe74e4c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe74e50: ldur            x1, [fp, #-0x30]
    // 0xe74e54: mov             x2, x0
    // 0xe74e58: r0 = where()
    //     0xe74e58: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xe74e5c: mov             x1, x0
    // 0xe74e60: r0 = iterator()
    //     0xe74e60: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0xe74e64: LoadField: r2 = r0->field_b
    //     0xe74e64: ldur            w2, [x0, #0xb]
    // 0xe74e68: DecompressPointer r2
    //     0xe74e68: add             x2, x2, HEAP, lsl #32
    // 0xe74e6c: stur            x2, [fp, #-0x38]
    // 0xe74e70: LoadField: r3 = r0->field_f
    //     0xe74e70: ldur            w3, [x0, #0xf]
    // 0xe74e74: DecompressPointer r3
    //     0xe74e74: add             x3, x3, HEAP, lsl #32
    // 0xe74e78: stur            x3, [fp, #-0x30]
    // 0xe74e7c: ldur            x4, [fp, #-0x28]
    // 0xe74e80: ldur            x6, [fp, #-0x18]
    // 0xe74e84: ldur            x5, [fp, #-0x20]
    // 0xe74e88: CheckStackOverflow
    //     0xe74e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe74e8c: cmp             SP, x16
    //     0xe74e90: b.ls            #0xe75534
    // 0xe74e94: CheckStackOverflow
    //     0xe74e94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe74e98: cmp             SP, x16
    //     0xe74e9c: b.ls            #0xe7553c
    // 0xe74ea0: r0 = LoadClassIdInstr(r2)
    //     0xe74ea0: ldur            x0, [x2, #-1]
    //     0xe74ea4: ubfx            x0, x0, #0xc, #0x14
    // 0xe74ea8: mov             x1, x2
    // 0xe74eac: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe74eac: movz            x17, #0x292d
    //     0xe74eb0: movk            x17, #0x1, lsl #16
    //     0xe74eb4: add             lr, x0, x17
    //     0xe74eb8: ldr             lr, [x21, lr, lsl #3]
    //     0xe74ebc: blr             lr
    // 0xe74ec0: tbnz            w0, #4, #0xe75338
    // 0xe74ec4: ldur            x2, [fp, #-0x38]
    // 0xe74ec8: r0 = LoadClassIdInstr(r2)
    //     0xe74ec8: ldur            x0, [x2, #-1]
    //     0xe74ecc: ubfx            x0, x0, #0xc, #0x14
    // 0xe74ed0: mov             x1, x2
    // 0xe74ed4: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe74ed4: movz            x17, #0x384d
    //     0xe74ed8: movk            x17, #0x1, lsl #16
    //     0xe74edc: add             lr, x0, x17
    //     0xe74ee0: ldr             lr, [x21, lr, lsl #3]
    //     0xe74ee4: blr             lr
    // 0xe74ee8: ldur            x16, [fp, #-0x30]
    // 0xe74eec: stp             x0, x16, [SP]
    // 0xe74ef0: ldur            x0, [fp, #-0x30]
    // 0xe74ef4: ClosureCall
    //     0xe74ef4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe74ef8: ldur            x2, [x0, #0x1f]
    //     0xe74efc: blr             x2
    // 0xe74f00: r16 = true
    //     0xe74f00: add             x16, NULL, #0x20  ; true
    // 0xe74f04: cmp             w0, w16
    // 0xe74f08: b.eq            #0xe74f24
    // 0xe74f0c: ldur            x6, [fp, #-0x18]
    // 0xe74f10: ldur            x5, [fp, #-0x20]
    // 0xe74f14: ldur            x4, [fp, #-0x28]
    // 0xe74f18: ldur            x2, [fp, #-0x38]
    // 0xe74f1c: ldur            x3, [fp, #-0x30]
    // 0xe74f20: b               #0xe74e94
    // 0xe74f24: ldur            x2, [fp, #-0x38]
    // 0xe74f28: r0 = LoadClassIdInstr(r2)
    //     0xe74f28: ldur            x0, [x2, #-1]
    //     0xe74f2c: ubfx            x0, x0, #0xc, #0x14
    // 0xe74f30: mov             x1, x2
    // 0xe74f34: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe74f34: movz            x17, #0x384d
    //     0xe74f38: movk            x17, #0x1, lsl #16
    //     0xe74f3c: add             lr, x0, x17
    //     0xe74f40: ldr             lr, [x21, lr, lsl #3]
    //     0xe74f44: blr             lr
    // 0xe74f48: mov             x1, x0
    // 0xe74f4c: stur            x0, [fp, #-0x40]
    // 0xe74f50: r0 = convertStyle()
    //     0xe74f50: bl              #0xe767ac  ; [package:pdf/src/svg/parser.dart] SvgParser::convertStyle
    // 0xe74f54: ldur            x1, [fp, #-0x40]
    // 0xe74f58: r2 = "stop-color"
    //     0xe74f58: add             x2, PP, #0x26, lsl #12  ; [pp+0x26398] "stop-color"
    //     0xe74f5c: ldr             x2, [x2, #0x398]
    // 0xe74f60: r3 = Null
    //     0xe74f60: mov             x3, NULL
    // 0xe74f64: r0 = getAttributeNode()
    //     0xe74f64: bl              #0xb1481c  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttributeNode
    // 0xe74f68: cmp             w0, NULL
    // 0xe74f6c: b.ne            #0xe74f78
    // 0xe74f70: r0 = Null
    //     0xe74f70: mov             x0, NULL
    // 0xe74f74: b               #0xe74f84
    // 0xe74f78: LoadField: r1 = r0->field_f
    //     0xe74f78: ldur            w1, [x0, #0xf]
    // 0xe74f7c: DecompressPointer r1
    //     0xe74f7c: add             x1, x1, HEAP, lsl #32
    // 0xe74f80: mov             x0, x1
    // 0xe74f84: cmp             w0, NULL
    // 0xe74f88: b.ne            #0xe74f98
    // 0xe74f8c: r2 = "black"
    //     0xe74f8c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ed28] "black"
    //     0xe74f90: ldr             x2, [x2, #0xd28]
    // 0xe74f94: b               #0xe74f9c
    // 0xe74f98: mov             x2, x0
    // 0xe74f9c: ldur            x3, [fp, #-0x10]
    // 0xe74fa0: r1 = Null
    //     0xe74fa0: mov             x1, NULL
    // 0xe74fa4: r0 = SvgColor.fromXml()
    //     0xe74fa4: bl              #0xe74098  ; [package:pdf/src/svg/color.dart] SvgColor::SvgColor.fromXml
    // 0xe74fa8: stur            x0, [fp, #-0x48]
    // 0xe74fac: str             NULL, [SP]
    // 0xe74fb0: ldur            x1, [fp, #-0x40]
    // 0xe74fb4: r2 = "stop-opacity"
    //     0xe74fb4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26390] "stop-opacity"
    //     0xe74fb8: ldr             x2, [x2, #0x390]
    // 0xe74fbc: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xe74fbc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xe74fc0: ldr             x4, [x4, #0x388]
    // 0xe74fc4: r0 = getAttribute()
    //     0xe74fc4: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe74fc8: cmp             w0, NULL
    // 0xe74fcc: b.ne            #0xe74fd8
    // 0xe74fd0: d0 = 1.000000
    //     0xe74fd0: fmov            d0, #1.00000000
    // 0xe74fd4: b               #0xe74fe0
    // 0xe74fd8: mov             x1, x0
    // 0xe74fdc: r0 = parse()
    //     0xe74fdc: bl              #0x61ca34  ; [dart:core] double::parse
    // 0xe74fe0: stur            d0, [fp, #-0x88]
    // 0xe74fe4: str             NULL, [SP]
    // 0xe74fe8: ldur            x1, [fp, #-0x40]
    // 0xe74fec: r2 = "offset"
    //     0xe74fec: add             x2, PP, #0x26, lsl #12  ; [pp+0x263b0] "offset"
    //     0xe74ff0: ldr             x2, [x2, #0x3b0]
    // 0xe74ff4: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xe74ff4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xe74ff8: ldr             x4, [x4, #0x388]
    // 0xe74ffc: r0 = getAttribute()
    //     0xe74ffc: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75000: cmp             w0, NULL
    // 0xe75004: b.ne            #0xe75020
    // 0xe75008: r0 = SvgNumeric()
    //     0xe75008: bl              #0xb1478c  ; AllocateSvgNumericStub -> SvgNumeric (size=0x18)
    // 0xe7500c: StoreField: r0->field_7 = rZR
    //     0xe7500c: stur            xzr, [x0, #7]
    // 0xe75010: r4 = Instance_SvgUnit
    //     0xe75010: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e390] Obj!SvgUnit@e2eb01
    //     0xe75014: ldr             x4, [x4, #0x390]
    // 0xe75018: StoreField: r0->field_f = r4
    //     0xe75018: stur            w4, [x0, #0xf]
    // 0xe7501c: b               #0xe75038
    // 0xe75020: r4 = Instance_SvgUnit
    //     0xe75020: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e390] Obj!SvgUnit@e2eb01
    //     0xe75024: ldr             x4, [x4, #0x390]
    // 0xe75028: mov             x2, x0
    // 0xe7502c: r1 = Null
    //     0xe7502c: mov             x1, NULL
    // 0xe75030: r3 = Null
    //     0xe75030: mov             x3, NULL
    // 0xe75034: r0 = SvgNumeric()
    //     0xe75034: bl              #0xb146a4  ; [package:pdf/src/svg/parser.dart] SvgNumeric::SvgNumeric
    // 0xe75038: LoadField: r1 = r0->field_f
    //     0xe75038: ldur            w1, [x0, #0xf]
    // 0xe7503c: DecompressPointer r1
    //     0xe7503c: add             x1, x1, HEAP, lsl #32
    // 0xe75040: LoadField: r2 = r1->field_7
    //     0xe75040: ldur            x2, [x1, #7]
    // 0xe75044: cmp             x2, #3
    // 0xe75048: b.gt            #0xe750d0
    // 0xe7504c: cmp             x2, #1
    // 0xe75050: b.gt            #0xe75080
    // 0xe75054: cmp             x2, #0
    // 0xe75058: b.gt            #0xe75064
    // 0xe7505c: d0 = 100.000000
    //     0xe7505c: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75060: b               #0xe75158
    // 0xe75064: d0 = 2.834646
    //     0xe75064: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e368] IMM: double(2.834645669291339) from 0x4006ad5ab56ad5ac
    //     0xe75068: ldr             d0, [x17, #0x368]
    // 0xe7506c: LoadField: d1 = r0->field_7
    //     0xe7506c: ldur            d1, [x0, #7]
    // 0xe75070: fmul            d2, d1, d0
    // 0xe75074: mov             v1.16b, v2.16b
    // 0xe75078: d0 = 100.000000
    //     0xe75078: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe7507c: b               #0xe7515c
    // 0xe75080: d0 = 2.834646
    //     0xe75080: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e368] IMM: double(2.834645669291339) from 0x4006ad5ab56ad5ac
    //     0xe75084: ldr             d0, [x17, #0x368]
    // 0xe75088: cmp             x2, #2
    // 0xe7508c: b.gt            #0xe750ac
    // 0xe75090: d1 = 28.346457
    //     0xe75090: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e370] IMM: double(28.346456692913385) from 0x403c58b162c58b16
    //     0xe75094: ldr             d1, [x17, #0x370]
    // 0xe75098: LoadField: d2 = r0->field_7
    //     0xe75098: ldur            d2, [x0, #7]
    // 0xe7509c: fmul            d3, d2, d1
    // 0xe750a0: mov             v1.16b, v3.16b
    // 0xe750a4: d0 = 100.000000
    //     0xe750a4: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe750a8: b               #0xe7515c
    // 0xe750ac: d1 = 28.346457
    //     0xe750ac: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e370] IMM: double(28.346456692913385) from 0x403c58b162c58b16
    //     0xe750b0: ldr             d1, [x17, #0x370]
    // 0xe750b4: d2 = 72.000000
    //     0xe750b4: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e378] IMM: double(72) from 0x4052000000000000
    //     0xe750b8: ldr             d2, [x17, #0x378]
    // 0xe750bc: LoadField: d3 = r0->field_7
    //     0xe750bc: ldur            d3, [x0, #7]
    // 0xe750c0: fmul            d4, d3, d2
    // 0xe750c4: mov             v1.16b, v4.16b
    // 0xe750c8: d0 = 100.000000
    //     0xe750c8: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe750cc: b               #0xe7515c
    // 0xe750d0: d0 = 2.834646
    //     0xe750d0: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e368] IMM: double(2.834645669291339) from 0x4006ad5ab56ad5ac
    //     0xe750d4: ldr             d0, [x17, #0x368]
    // 0xe750d8: d1 = 28.346457
    //     0xe750d8: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e370] IMM: double(28.346456692913385) from 0x403c58b162c58b16
    //     0xe750dc: ldr             d1, [x17, #0x370]
    // 0xe750e0: d2 = 72.000000
    //     0xe750e0: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e378] IMM: double(72) from 0x4052000000000000
    //     0xe750e4: ldr             d2, [x17, #0x378]
    // 0xe750e8: cmp             x2, #5
    // 0xe750ec: b.gt            #0xe75154
    // 0xe750f0: cmp             x2, #4
    // 0xe750f4: b.gt            #0xe75140
    // 0xe750f8: LoadField: d3 = r0->field_7
    //     0xe750f8: ldur            d3, [x0, #7]
    // 0xe750fc: stur            d3, [fp, #-0x90]
    // 0xe75100: LoadField: r1 = r0->field_13
    //     0xe75100: ldur            w1, [x0, #0x13]
    // 0xe75104: DecompressPointer r1
    //     0xe75104: add             x1, x1, HEAP, lsl #32
    // 0xe75108: cmp             w1, NULL
    // 0xe7510c: b.eq            #0xe75544
    // 0xe75110: LoadField: r0 = r1->field_37
    //     0xe75110: ldur            w0, [x1, #0x37]
    // 0xe75114: DecompressPointer r0
    //     0xe75114: add             x0, x0, HEAP, lsl #32
    // 0xe75118: cmp             w0, NULL
    // 0xe7511c: b.eq            #0xe75548
    // 0xe75120: mov             x1, x0
    // 0xe75124: r0 = sizeValue()
    //     0xe75124: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe75128: mov             v1.16b, v0.16b
    // 0xe7512c: ldur            d0, [fp, #-0x90]
    // 0xe75130: fmul            d2, d0, d1
    // 0xe75134: mov             v1.16b, v2.16b
    // 0xe75138: d0 = 100.000000
    //     0xe75138: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe7513c: b               #0xe7515c
    // 0xe75140: d0 = 100.000000
    //     0xe75140: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75144: LoadField: d1 = r0->field_7
    //     0xe75144: ldur            d1, [x0, #7]
    // 0xe75148: fdiv            d2, d1, d0
    // 0xe7514c: mov             v1.16b, v2.16b
    // 0xe75150: b               #0xe7515c
    // 0xe75154: d0 = 100.000000
    //     0xe75154: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75158: LoadField: d1 = r0->field_7
    //     0xe75158: ldur            d1, [x0, #7]
    // 0xe7515c: ldur            x2, [fp, #-0x18]
    // 0xe75160: ldur            x0, [fp, #-0x48]
    // 0xe75164: stur            d1, [fp, #-0x90]
    // 0xe75168: LoadField: r3 = r0->field_7
    //     0xe75168: ldur            w3, [x0, #7]
    // 0xe7516c: DecompressPointer r3
    //     0xe7516c: add             x3, x3, HEAP, lsl #32
    // 0xe75170: stur            x3, [fp, #-0x40]
    // 0xe75174: LoadField: r0 = r2->field_b
    //     0xe75174: ldur            w0, [x2, #0xb]
    // 0xe75178: LoadField: r1 = r2->field_f
    //     0xe75178: ldur            w1, [x2, #0xf]
    // 0xe7517c: DecompressPointer r1
    //     0xe7517c: add             x1, x1, HEAP, lsl #32
    // 0xe75180: LoadField: r4 = r1->field_b
    //     0xe75180: ldur            w4, [x1, #0xb]
    // 0xe75184: r5 = LoadInt32Instr(r0)
    //     0xe75184: sbfx            x5, x0, #1, #0x1f
    // 0xe75188: stur            x5, [fp, #-0x50]
    // 0xe7518c: r0 = LoadInt32Instr(r4)
    //     0xe7518c: sbfx            x0, x4, #1, #0x1f
    // 0xe75190: cmp             x5, x0
    // 0xe75194: b.ne            #0xe751a0
    // 0xe75198: mov             x1, x2
    // 0xe7519c: r0 = _growToNextCapacity()
    //     0xe7519c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe751a0: ldur            x2, [fp, #-0x18]
    // 0xe751a4: ldur            x4, [fp, #-0x20]
    // 0xe751a8: ldur            x3, [fp, #-0x50]
    // 0xe751ac: add             x0, x3, #1
    // 0xe751b0: lsl             x1, x0, #1
    // 0xe751b4: StoreField: r2->field_b = r1
    //     0xe751b4: stur            w1, [x2, #0xb]
    // 0xe751b8: LoadField: r1 = r2->field_f
    //     0xe751b8: ldur            w1, [x2, #0xf]
    // 0xe751bc: DecompressPointer r1
    //     0xe751bc: add             x1, x1, HEAP, lsl #32
    // 0xe751c0: ldur            x0, [fp, #-0x40]
    // 0xe751c4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe751c4: add             x25, x1, x3, lsl #2
    //     0xe751c8: add             x25, x25, #0xf
    //     0xe751cc: str             w0, [x25]
    //     0xe751d0: tbz             w0, #0, #0xe751ec
    //     0xe751d4: ldurb           w16, [x1, #-1]
    //     0xe751d8: ldurb           w17, [x0, #-1]
    //     0xe751dc: and             x16, x17, x16, lsr #2
    //     0xe751e0: tst             x16, HEAP, lsr #32
    //     0xe751e4: b.eq            #0xe751ec
    //     0xe751e8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe751ec: LoadField: r0 = r4->field_b
    //     0xe751ec: ldur            w0, [x4, #0xb]
    // 0xe751f0: LoadField: r1 = r4->field_f
    //     0xe751f0: ldur            w1, [x4, #0xf]
    // 0xe751f4: DecompressPointer r1
    //     0xe751f4: add             x1, x1, HEAP, lsl #32
    // 0xe751f8: LoadField: r3 = r1->field_b
    //     0xe751f8: ldur            w3, [x1, #0xb]
    // 0xe751fc: r5 = LoadInt32Instr(r0)
    //     0xe751fc: sbfx            x5, x0, #1, #0x1f
    // 0xe75200: stur            x5, [fp, #-0x50]
    // 0xe75204: r0 = LoadInt32Instr(r3)
    //     0xe75204: sbfx            x0, x3, #1, #0x1f
    // 0xe75208: cmp             x5, x0
    // 0xe7520c: b.ne            #0xe75218
    // 0xe75210: mov             x1, x4
    // 0xe75214: r0 = _growToNextCapacity()
    //     0xe75214: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe75218: ldur            x2, [fp, #-0x20]
    // 0xe7521c: ldur            x4, [fp, #-0x28]
    // 0xe75220: ldur            d0, [fp, #-0x90]
    // 0xe75224: ldur            x3, [fp, #-0x50]
    // 0xe75228: add             x0, x3, #1
    // 0xe7522c: lsl             x1, x0, #1
    // 0xe75230: StoreField: r2->field_b = r1
    //     0xe75230: stur            w1, [x2, #0xb]
    // 0xe75234: LoadField: r1 = r2->field_f
    //     0xe75234: ldur            w1, [x2, #0xf]
    // 0xe75238: DecompressPointer r1
    //     0xe75238: add             x1, x1, HEAP, lsl #32
    // 0xe7523c: r0 = inline_Allocate_Double()
    //     0xe7523c: ldp             x0, x5, [THR, #0x50]  ; THR::top
    //     0xe75240: add             x0, x0, #0x10
    //     0xe75244: cmp             x5, x0
    //     0xe75248: b.ls            #0xe7554c
    //     0xe7524c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe75250: sub             x0, x0, #0xf
    //     0xe75254: movz            x5, #0xe15c
    //     0xe75258: movk            x5, #0x3, lsl #16
    //     0xe7525c: stur            x5, [x0, #-1]
    // 0xe75260: StoreField: r0->field_7 = d0
    //     0xe75260: stur            d0, [x0, #7]
    // 0xe75264: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe75264: add             x25, x1, x3, lsl #2
    //     0xe75268: add             x25, x25, #0xf
    //     0xe7526c: str             w0, [x25]
    //     0xe75270: tbz             w0, #0, #0xe7528c
    //     0xe75274: ldurb           w16, [x1, #-1]
    //     0xe75278: ldurb           w17, [x0, #-1]
    //     0xe7527c: and             x16, x17, x16, lsr #2
    //     0xe75280: tst             x16, HEAP, lsr #32
    //     0xe75284: b.eq            #0xe7528c
    //     0xe75288: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7528c: LoadField: r0 = r4->field_b
    //     0xe7528c: ldur            w0, [x4, #0xb]
    // 0xe75290: LoadField: r1 = r4->field_f
    //     0xe75290: ldur            w1, [x4, #0xf]
    // 0xe75294: DecompressPointer r1
    //     0xe75294: add             x1, x1, HEAP, lsl #32
    // 0xe75298: LoadField: r3 = r1->field_b
    //     0xe75298: ldur            w3, [x1, #0xb]
    // 0xe7529c: r5 = LoadInt32Instr(r0)
    //     0xe7529c: sbfx            x5, x0, #1, #0x1f
    // 0xe752a0: stur            x5, [fp, #-0x50]
    // 0xe752a4: r0 = LoadInt32Instr(r3)
    //     0xe752a4: sbfx            x0, x3, #1, #0x1f
    // 0xe752a8: cmp             x5, x0
    // 0xe752ac: b.ne            #0xe752b8
    // 0xe752b0: mov             x1, x4
    // 0xe752b4: r0 = _growToNextCapacity()
    //     0xe752b4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe752b8: ldur            x3, [fp, #-0x28]
    // 0xe752bc: ldur            d0, [fp, #-0x88]
    // 0xe752c0: ldur            x2, [fp, #-0x50]
    // 0xe752c4: add             x0, x2, #1
    // 0xe752c8: lsl             x1, x0, #1
    // 0xe752cc: StoreField: r3->field_b = r1
    //     0xe752cc: stur            w1, [x3, #0xb]
    // 0xe752d0: LoadField: r1 = r3->field_f
    //     0xe752d0: ldur            w1, [x3, #0xf]
    // 0xe752d4: DecompressPointer r1
    //     0xe752d4: add             x1, x1, HEAP, lsl #32
    // 0xe752d8: r0 = inline_Allocate_Double()
    //     0xe752d8: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xe752dc: add             x0, x0, #0x10
    //     0xe752e0: cmp             x4, x0
    //     0xe752e4: b.ls            #0xe7556c
    //     0xe752e8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe752ec: sub             x0, x0, #0xf
    //     0xe752f0: movz            x4, #0xe15c
    //     0xe752f4: movk            x4, #0x3, lsl #16
    //     0xe752f8: stur            x4, [x0, #-1]
    // 0xe752fc: StoreField: r0->field_7 = d0
    //     0xe752fc: stur            d0, [x0, #7]
    // 0xe75300: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe75300: add             x25, x1, x2, lsl #2
    //     0xe75304: add             x25, x25, #0xf
    //     0xe75308: str             w0, [x25]
    //     0xe7530c: tbz             w0, #0, #0xe75328
    //     0xe75310: ldurb           w16, [x1, #-1]
    //     0xe75314: ldurb           w17, [x0, #-1]
    //     0xe75318: and             x16, x17, x16, lsr #2
    //     0xe7531c: tst             x16, HEAP, lsr #32
    //     0xe75320: b.eq            #0xe75328
    //     0xe75324: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe75328: mov             x4, x3
    // 0xe7532c: ldur            x2, [fp, #-0x38]
    // 0xe75330: ldur            x3, [fp, #-0x30]
    // 0xe75334: b               #0xe74e80
    // 0xe75338: ldur            x3, [fp, #-0x28]
    // 0xe7533c: ldur            x1, [fp, #-8]
    // 0xe75340: r2 = "gradientUnits"
    //     0xe75340: add             x2, PP, #0x26, lsl #12  ; [pp+0x26338] "gradientUnits"
    //     0xe75344: ldr             x2, [x2, #0x338]
    // 0xe75348: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe75348: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe7534c: r0 = getAttribute()
    //     0xe7534c: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75350: stur            x0, [fp, #-0x30]
    // 0xe75354: r16 = "userSpaceOnUse"
    //     0xe75354: add             x16, PP, #0x26, lsl #12  ; [pp+0x26340] "userSpaceOnUse"
    //     0xe75358: ldr             x16, [x16, #0x340]
    // 0xe7535c: stp             x0, x16, [SP]
    // 0xe75360: r0 = ==()
    //     0xe75360: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe75364: tbnz            w0, #4, #0xe75374
    // 0xe75368: r5 = Instance_GradientUnits
    //     0xe75368: add             x5, PP, #0x3e, lsl #12  ; [pp+0x3ed30] Obj!GradientUnits@e2eb41
    //     0xe7536c: ldr             x5, [x5, #0xd30]
    // 0xe75370: b               #0xe7539c
    // 0xe75374: r16 = "objectBoundingBox"
    //     0xe75374: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed38] "objectBoundingBox"
    //     0xe75378: ldr             x16, [x16, #0xd38]
    // 0xe7537c: ldur            lr, [fp, #-0x30]
    // 0xe75380: stp             lr, x16, [SP]
    // 0xe75384: r0 = ==()
    //     0xe75384: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe75388: tbnz            w0, #4, #0xe75398
    // 0xe7538c: r5 = Instance_GradientUnits
    //     0xe7538c: add             x5, PP, #0x3e, lsl #12  ; [pp+0x3ed40] Obj!GradientUnits@e2eb21
    //     0xe75390: ldr             x5, [x5, #0xd40]
    // 0xe75394: b               #0xe7539c
    // 0xe75398: r5 = Null
    //     0xe75398: mov             x5, NULL
    // 0xe7539c: ldur            d5, [fp, #-0x58]
    // 0xe753a0: ldur            d3, [fp, #-0x60]
    // 0xe753a4: ldur            d1, [fp, #-0x68]
    // 0xe753a8: ldur            d4, [fp, #-0x70]
    // 0xe753ac: ldur            d2, [fp, #-0x78]
    // 0xe753b0: ldur            d0, [fp, #-0x80]
    // 0xe753b4: ldur            x4, [fp, #-0x18]
    // 0xe753b8: ldur            x3, [fp, #-0x20]
    // 0xe753bc: ldur            x0, [fp, #-0x28]
    // 0xe753c0: ldur            x1, [fp, #-8]
    // 0xe753c4: stur            x5, [fp, #-0x30]
    // 0xe753c8: r2 = "gradientTransform"
    //     0xe753c8: add             x2, PP, #0x26, lsl #12  ; [pp+0x26358] "gradientTransform"
    //     0xe753cc: ldr             x2, [x2, #0x358]
    // 0xe753d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe753d0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe753d4: r0 = getAttribute()
    //     0xe753d4: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe753d8: mov             x2, x0
    // 0xe753dc: r1 = Null
    //     0xe753dc: mov             x1, NULL
    // 0xe753e0: r0 = SvgTransform.fromString()
    //     0xe753e0: bl              #0xe6e1a0  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromString
    // 0xe753e4: stur            x0, [fp, #-0x38]
    // 0xe753e8: r0 = SvgRadialGradient()
    //     0xe753e8: bl              #0xe756d8  ; AllocateSvgRadialGradientStub -> SvgRadialGradient (size=0x58)
    // 0xe753ec: ldur            d0, [fp, #-0x58]
    // 0xe753f0: stur            x0, [fp, #-0x40]
    // 0xe753f4: StoreField: r0->field_27 = d0
    //     0xe753f4: stur            d0, [x0, #0x27]
    // 0xe753f8: ldur            d0, [fp, #-0x60]
    // 0xe753fc: StoreField: r0->field_2f = d0
    //     0xe753fc: stur            d0, [x0, #0x2f]
    // 0xe75400: ldur            d0, [fp, #-0x68]
    // 0xe75404: StoreField: r0->field_37 = d0
    //     0xe75404: stur            d0, [x0, #0x37]
    // 0xe75408: ldur            d0, [fp, #-0x70]
    // 0xe7540c: StoreField: r0->field_3f = d0
    //     0xe7540c: stur            d0, [x0, #0x3f]
    // 0xe75410: ldur            d0, [fp, #-0x78]
    // 0xe75414: StoreField: r0->field_47 = d0
    //     0xe75414: stur            d0, [x0, #0x47]
    // 0xe75418: ldur            d0, [fp, #-0x80]
    // 0xe7541c: StoreField: r0->field_4f = d0
    //     0xe7541c: stur            d0, [x0, #0x4f]
    // 0xe75420: ldur            x1, [fp, #-0x30]
    // 0xe75424: StoreField: r0->field_13 = r1
    //     0xe75424: stur            w1, [x0, #0x13]
    // 0xe75428: ldur            x1, [fp, #-0x38]
    // 0xe7542c: ArrayStore: r0[0] = r1  ; List_4
    //     0xe7542c: stur            w1, [x0, #0x17]
    // 0xe75430: ldur            x1, [fp, #-0x18]
    // 0xe75434: StoreField: r0->field_1b = r1
    //     0xe75434: stur            w1, [x0, #0x1b]
    // 0xe75438: ldur            x1, [fp, #-0x20]
    // 0xe7543c: StoreField: r0->field_1f = r1
    //     0xe7543c: stur            w1, [x0, #0x1f]
    // 0xe75440: ldur            x1, [fp, #-0x28]
    // 0xe75444: StoreField: r0->field_23 = r1
    //     0xe75444: stur            w1, [x0, #0x23]
    // 0xe75448: r1 = false
    //     0xe75448: add             x1, NULL, #0x30  ; false
    // 0xe7544c: StoreField: r0->field_f = r1
    //     0xe7544c: stur            w1, [x0, #0xf]
    // 0xe75450: ldur            x1, [fp, #-8]
    // 0xe75454: r2 = "href"
    //     0xe75454: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xe75458: ldr             x2, [x2, #0x28]
    // 0xe7545c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe7545c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe75460: r0 = getAttribute()
    //     0xe75460: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75464: cmp             w0, NULL
    // 0xe75468: b.ne            #0xe75498
    // 0xe7546c: r16 = "http://www.w3.org/1999/xlink"
    //     0xe7546c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eaf8] "http://www.w3.org/1999/xlink"
    //     0xe75470: ldr             x16, [x16, #0xaf8]
    // 0xe75474: str             x16, [SP]
    // 0xe75478: ldur            x1, [fp, #-8]
    // 0xe7547c: r2 = "href"
    //     0xe7547c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xe75480: ldr             x2, [x2, #0x28]
    // 0xe75484: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xe75484: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xe75488: ldr             x4, [x4, #0x388]
    // 0xe7548c: r0 = getAttribute()
    //     0xe7548c: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75490: mov             x1, x0
    // 0xe75494: b               #0xe7549c
    // 0xe75498: mov             x1, x0
    // 0xe7549c: cmp             w1, NULL
    // 0xe754a0: b.eq            #0xe754fc
    // 0xe754a4: ldur            x3, [fp, #-0x10]
    // 0xe754a8: LoadField: r0 = r3->field_7
    //     0xe754a8: ldur            w0, [x3, #7]
    // 0xe754ac: DecompressPointer r0
    //     0xe754ac: add             x0, x0, HEAP, lsl #32
    // 0xe754b0: stur            x0, [fp, #-8]
    // 0xe754b4: r2 = 1
    //     0xe754b4: movz            x2, #0x1
    // 0xe754b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe754b8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe754bc: r0 = substring()
    //     0xe754bc: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe754c0: ldur            x1, [fp, #-8]
    // 0xe754c4: mov             x2, x0
    // 0xe754c8: r0 = findById()
    //     0xe754c8: bl              #0xe6f138  ; [package:pdf/src/svg/parser.dart] SvgParser::findById
    // 0xe754cc: cmp             w0, NULL
    // 0xe754d0: b.eq            #0xe754fc
    // 0xe754d4: mov             x2, x0
    // 0xe754d8: ldur            x3, [fp, #-0x10]
    // 0xe754dc: r1 = Null
    //     0xe754dc: mov             x1, NULL
    // 0xe754e0: r0 = SvgRadialGradient.fromXml()
    //     0xe754e0: bl              #0xe74c48  ; [package:pdf/src/svg/gradient.dart] SvgRadialGradient::SvgRadialGradient.fromXml
    // 0xe754e4: mov             x1, x0
    // 0xe754e8: ldur            x2, [fp, #-0x40]
    // 0xe754ec: r0 = mergeWith()
    //     0xe754ec: bl              #0xe7558c  ; [package:pdf/src/svg/gradient.dart] SvgRadialGradient::mergeWith
    // 0xe754f0: LeaveFrame
    //     0xe754f0: mov             SP, fp
    //     0xe754f4: ldp             fp, lr, [SP], #0x10
    // 0xe754f8: ret
    //     0xe754f8: ret             
    // 0xe754fc: ldur            x0, [fp, #-0x40]
    // 0xe75500: LeaveFrame
    //     0xe75500: mov             SP, fp
    //     0xe75504: ldp             fp, lr, [SP], #0x10
    // 0xe75508: ret
    //     0xe75508: ret             
    // 0xe7550c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7550c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe75510: b               #0xe74c70
    // 0xe75514: stp             q0, q1, [SP, #-0x20]!
    // 0xe75518: r0 = AllocateDouble()
    //     0xe75518: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7551c: ldp             q0, q1, [SP], #0x20
    // 0xe75520: b               #0xe74d5c
    // 0xe75524: stp             q0, q1, [SP, #-0x20]!
    // 0xe75528: r0 = AllocateDouble()
    //     0xe75528: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7552c: ldp             q0, q1, [SP], #0x20
    // 0xe75530: b               #0xe74db8
    // 0xe75534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe75534: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe75538: b               #0xe74e94
    // 0xe7553c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7553c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe75540: b               #0xe74ea0
    // 0xe75544: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe75544: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe75548: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe75548: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe7554c: SaveReg d0
    //     0xe7554c: str             q0, [SP, #-0x10]!
    // 0xe75550: stp             x3, x4, [SP, #-0x10]!
    // 0xe75554: stp             x1, x2, [SP, #-0x10]!
    // 0xe75558: r0 = AllocateDouble()
    //     0xe75558: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7555c: ldp             x1, x2, [SP], #0x10
    // 0xe75560: ldp             x3, x4, [SP], #0x10
    // 0xe75564: RestoreReg d0
    //     0xe75564: ldr             q0, [SP], #0x10
    // 0xe75568: b               #0xe75260
    // 0xe7556c: SaveReg d0
    //     0xe7556c: str             q0, [SP, #-0x10]!
    // 0xe75570: stp             x2, x3, [SP, #-0x10]!
    // 0xe75574: SaveReg r1
    //     0xe75574: str             x1, [SP, #-8]!
    // 0xe75578: r0 = AllocateDouble()
    //     0xe75578: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7557c: RestoreReg r1
    //     0xe7557c: ldr             x1, [SP], #8
    // 0xe75580: ldp             x2, x3, [SP], #0x10
    // 0xe75584: RestoreReg d0
    //     0xe75584: ldr             q0, [SP], #0x10
    // 0xe75588: b               #0xe752fc
  }
  _ mergeWith(/* No info */) {
    // ** addr: 0xe7558c, size: 0x14c
    // 0xe7558c: EnterFrame
    //     0xe7558c: stp             fp, lr, [SP, #-0x10]!
    //     0xe75590: mov             fp, SP
    // 0xe75594: AllocStack(0x58)
    //     0xe75594: sub             SP, SP, #0x58
    // 0xe75598: LoadField: r0 = r2->field_13
    //     0xe75598: ldur            w0, [x2, #0x13]
    // 0xe7559c: DecompressPointer r0
    //     0xe7559c: add             x0, x0, HEAP, lsl #32
    // 0xe755a0: cmp             w0, NULL
    // 0xe755a4: b.ne            #0xe755b0
    // 0xe755a8: LoadField: r0 = r1->field_13
    //     0xe755a8: ldur            w0, [x1, #0x13]
    // 0xe755ac: DecompressPointer r0
    //     0xe755ac: add             x0, x0, HEAP, lsl #32
    // 0xe755b0: stur            x0, [fp, #-0x28]
    // 0xe755b4: LoadField: d0 = r2->field_27
    //     0xe755b4: ldur            d0, [x2, #0x27]
    // 0xe755b8: stur            d0, [fp, #-0x58]
    // 0xe755bc: LoadField: d1 = r2->field_2f
    //     0xe755bc: ldur            d1, [x2, #0x2f]
    // 0xe755c0: stur            d1, [fp, #-0x50]
    // 0xe755c4: LoadField: d2 = r2->field_37
    //     0xe755c4: ldur            d2, [x2, #0x37]
    // 0xe755c8: stur            d2, [fp, #-0x48]
    // 0xe755cc: LoadField: d3 = r2->field_3f
    //     0xe755cc: ldur            d3, [x2, #0x3f]
    // 0xe755d0: stur            d3, [fp, #-0x40]
    // 0xe755d4: LoadField: d4 = r2->field_47
    //     0xe755d4: ldur            d4, [x2, #0x47]
    // 0xe755d8: stur            d4, [fp, #-0x38]
    // 0xe755dc: LoadField: d5 = r2->field_4f
    //     0xe755dc: ldur            d5, [x2, #0x4f]
    // 0xe755e0: stur            d5, [fp, #-0x30]
    // 0xe755e4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe755e4: ldur            w3, [x2, #0x17]
    // 0xe755e8: DecompressPointer r3
    //     0xe755e8: add             x3, x3, HEAP, lsl #32
    // 0xe755ec: LoadField: r4 = r3->field_7
    //     0xe755ec: ldur            w4, [x3, #7]
    // 0xe755f0: DecompressPointer r4
    //     0xe755f0: add             x4, x4, HEAP, lsl #32
    // 0xe755f4: cmp             w4, NULL
    // 0xe755f8: b.ne            #0xe75604
    // 0xe755fc: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xe755fc: ldur            w3, [x1, #0x17]
    // 0xe75600: DecompressPointer r3
    //     0xe75600: add             x3, x3, HEAP, lsl #32
    // 0xe75604: stur            x3, [fp, #-0x20]
    // 0xe75608: LoadField: r4 = r2->field_1b
    //     0xe75608: ldur            w4, [x2, #0x1b]
    // 0xe7560c: DecompressPointer r4
    //     0xe7560c: add             x4, x4, HEAP, lsl #32
    // 0xe75610: LoadField: r5 = r4->field_b
    //     0xe75610: ldur            w5, [x4, #0xb]
    // 0xe75614: cbnz            w5, #0xe75620
    // 0xe75618: LoadField: r4 = r1->field_1b
    //     0xe75618: ldur            w4, [x1, #0x1b]
    // 0xe7561c: DecompressPointer r4
    //     0xe7561c: add             x4, x4, HEAP, lsl #32
    // 0xe75620: stur            x4, [fp, #-0x18]
    // 0xe75624: LoadField: r5 = r2->field_1f
    //     0xe75624: ldur            w5, [x2, #0x1f]
    // 0xe75628: DecompressPointer r5
    //     0xe75628: add             x5, x5, HEAP, lsl #32
    // 0xe7562c: LoadField: r6 = r5->field_b
    //     0xe7562c: ldur            w6, [x5, #0xb]
    // 0xe75630: cbnz            w6, #0xe7563c
    // 0xe75634: LoadField: r5 = r1->field_1f
    //     0xe75634: ldur            w5, [x1, #0x1f]
    // 0xe75638: DecompressPointer r5
    //     0xe75638: add             x5, x5, HEAP, lsl #32
    // 0xe7563c: stur            x5, [fp, #-0x10]
    // 0xe75640: LoadField: r6 = r2->field_23
    //     0xe75640: ldur            w6, [x2, #0x23]
    // 0xe75644: DecompressPointer r6
    //     0xe75644: add             x6, x6, HEAP, lsl #32
    // 0xe75648: LoadField: r2 = r6->field_b
    //     0xe75648: ldur            w2, [x6, #0xb]
    // 0xe7564c: cbz             w2, #0xe75658
    // 0xe75650: mov             x1, x6
    // 0xe75654: b               #0xe75664
    // 0xe75658: LoadField: r2 = r1->field_23
    //     0xe75658: ldur            w2, [x1, #0x23]
    // 0xe7565c: DecompressPointer r2
    //     0xe7565c: add             x2, x2, HEAP, lsl #32
    // 0xe75660: mov             x1, x2
    // 0xe75664: stur            x1, [fp, #-8]
    // 0xe75668: r0 = SvgRadialGradient()
    //     0xe75668: bl              #0xe756d8  ; AllocateSvgRadialGradientStub -> SvgRadialGradient (size=0x58)
    // 0xe7566c: ldur            d0, [fp, #-0x58]
    // 0xe75670: StoreField: r0->field_27 = d0
    //     0xe75670: stur            d0, [x0, #0x27]
    // 0xe75674: ldur            d0, [fp, #-0x50]
    // 0xe75678: StoreField: r0->field_2f = d0
    //     0xe75678: stur            d0, [x0, #0x2f]
    // 0xe7567c: ldur            d0, [fp, #-0x48]
    // 0xe75680: StoreField: r0->field_37 = d0
    //     0xe75680: stur            d0, [x0, #0x37]
    // 0xe75684: ldur            d0, [fp, #-0x40]
    // 0xe75688: StoreField: r0->field_3f = d0
    //     0xe75688: stur            d0, [x0, #0x3f]
    // 0xe7568c: ldur            d0, [fp, #-0x38]
    // 0xe75690: StoreField: r0->field_47 = d0
    //     0xe75690: stur            d0, [x0, #0x47]
    // 0xe75694: ldur            d0, [fp, #-0x30]
    // 0xe75698: StoreField: r0->field_4f = d0
    //     0xe75698: stur            d0, [x0, #0x4f]
    // 0xe7569c: ldur            x1, [fp, #-0x28]
    // 0xe756a0: StoreField: r0->field_13 = r1
    //     0xe756a0: stur            w1, [x0, #0x13]
    // 0xe756a4: ldur            x1, [fp, #-0x20]
    // 0xe756a8: ArrayStore: r0[0] = r1  ; List_4
    //     0xe756a8: stur            w1, [x0, #0x17]
    // 0xe756ac: ldur            x1, [fp, #-0x18]
    // 0xe756b0: StoreField: r0->field_1b = r1
    //     0xe756b0: stur            w1, [x0, #0x1b]
    // 0xe756b4: ldur            x1, [fp, #-0x10]
    // 0xe756b8: StoreField: r0->field_1f = r1
    //     0xe756b8: stur            w1, [x0, #0x1f]
    // 0xe756bc: ldur            x1, [fp, #-8]
    // 0xe756c0: StoreField: r0->field_23 = r1
    //     0xe756c0: stur            w1, [x0, #0x23]
    // 0xe756c4: r1 = false
    //     0xe756c4: add             x1, NULL, #0x30  ; false
    // 0xe756c8: StoreField: r0->field_f = r1
    //     0xe756c8: stur            w1, [x0, #0xf]
    // 0xe756cc: LeaveFrame
    //     0xe756cc: mov             SP, fp
    //     0xe756d0: ldp             fp, lr, [SP], #0x10
    // 0xe756d4: ret
    //     0xe756d4: ret             
  }
  [closure] static bool <anonymous closure>(dynamic, XmlElement) {
    // ** addr: 0xe756e4, size: 0x80
    // 0xe756e4: EnterFrame
    //     0xe756e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe756e8: mov             fp, SP
    // 0xe756ec: AllocStack(0x10)
    //     0xe756ec: sub             SP, SP, #0x10
    // 0xe756f0: CheckStackOverflow
    //     0xe756f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe756f4: cmp             SP, x16
    //     0xe756f8: b.ls            #0xe7575c
    // 0xe756fc: ldr             x0, [fp, #0x10]
    // 0xe75700: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe75700: ldur            w1, [x0, #0x17]
    // 0xe75704: DecompressPointer r1
    //     0xe75704: add             x1, x1, HEAP, lsl #32
    // 0xe75708: r0 = LoadClassIdInstr(r1)
    //     0xe75708: ldur            x0, [x1, #-1]
    //     0xe7570c: ubfx            x0, x0, #0xc, #0x14
    // 0xe75710: cmp             x0, #0xe2
    // 0xe75714: b.ne            #0xe75724
    // 0xe75718: LoadField: r0 = r1->field_b
    //     0xe75718: ldur            w0, [x1, #0xb]
    // 0xe7571c: DecompressPointer r0
    //     0xe7571c: add             x0, x0, HEAP, lsl #32
    // 0xe75720: b               #0xe7572c
    // 0xe75724: LoadField: r0 = r1->field_f
    //     0xe75724: ldur            w0, [x1, #0xf]
    // 0xe75728: DecompressPointer r0
    //     0xe75728: add             x0, x0, HEAP, lsl #32
    // 0xe7572c: r1 = LoadClassIdInstr(r0)
    //     0xe7572c: ldur            x1, [x0, #-1]
    //     0xe75730: ubfx            x1, x1, #0xc, #0x14
    // 0xe75734: r16 = "stop"
    //     0xe75734: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed48] "stop"
    //     0xe75738: ldr             x16, [x16, #0xd48]
    // 0xe7573c: stp             x16, x0, [SP]
    // 0xe75740: mov             x0, x1
    // 0xe75744: mov             lr, x0
    // 0xe75748: ldr             lr, [x21, lr, lsl #3]
    // 0xe7574c: blr             lr
    // 0xe75750: LeaveFrame
    //     0xe75750: mov             SP, fp
    //     0xe75754: ldp             fp, lr, [SP], #0x10
    // 0xe75758: ret
    //     0xe75758: ret             
    // 0xe7575c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7575c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe75760: b               #0xe756fc
  }
  _ buildGradient(/* No info */) {
    // ** addr: 0xeab0c4, size: 0x304
    // 0xeab0c4: EnterFrame
    //     0xeab0c4: stp             fp, lr, [SP, #-0x10]!
    //     0xeab0c8: mov             fp, SP
    // 0xeab0cc: AllocStack(0x60)
    //     0xeab0cc: sub             SP, SP, #0x60
    // 0xeab0d0: SetupParameters(SvgRadialGradient this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r1 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0xeab0d0: mov             x0, x2
    //     0xeab0d4: stur            x2, [fp, #-0x10]
    //     0xeab0d8: mov             x2, x1
    //     0xeab0dc: stur            x1, [fp, #-8]
    //     0xeab0e0: mov             x1, x3
    //     0xeab0e4: mov             x3, x5
    //     0xeab0e8: stur            x5, [fp, #-0x18]
    // 0xeab0ec: CheckStackOverflow
    //     0xeab0ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeab0f0: cmp             SP, x16
    //     0xeab0f4: b.ls            #0xeab35c
    // 0xeab0f8: r0 = getTransform()
    //     0xeab0f8: bl              #0xe6daa4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::getTransform
    // 0xeab0fc: mov             x3, x0
    // 0xeab100: ldur            x2, [fp, #-8]
    // 0xeab104: stur            x3, [fp, #-0x20]
    // 0xeab108: LoadField: r0 = r2->field_13
    //     0xeab108: ldur            w0, [x2, #0x13]
    // 0xeab10c: DecompressPointer r0
    //     0xeab10c: add             x0, x0, HEAP, lsl #32
    // 0xeab110: r16 = Instance_GradientUnits
    //     0xeab110: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed30] Obj!GradientUnits@e2eb41
    //     0xeab114: ldr             x16, [x16, #0xd30]
    // 0xeab118: cmp             w0, w16
    // 0xeab11c: b.eq            #0xeab1bc
    // 0xeab120: ldur            x4, [fp, #-0x10]
    // 0xeab124: r0 = LoadClassIdInstr(r4)
    //     0xeab124: ldur            x0, [x4, #-1]
    //     0xeab128: ubfx            x0, x0, #0xc, #0x14
    // 0xeab12c: mov             x1, x4
    // 0xeab130: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeab130: sub             lr, x0, #1, lsl #12
    //     0xeab134: ldr             lr, [x21, lr, lsl #3]
    //     0xeab138: blr             lr
    // 0xeab13c: stur            x0, [fp, #-0x28]
    // 0xeab140: LoadField: d0 = r0->field_7
    //     0xeab140: ldur            d0, [x0, #7]
    // 0xeab144: LoadField: d1 = r0->field_f
    //     0xeab144: ldur            d1, [x0, #0xf]
    // 0xeab148: ldur            x1, [fp, #-0x20]
    // 0xeab14c: r0 = translate()
    //     0xeab14c: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xeab150: ldur            x0, [fp, #-0x28]
    // 0xeab154: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xeab154: ldur            d0, [x0, #0x17]
    // 0xeab158: LoadField: d1 = r0->field_1f
    //     0xeab158: ldur            d1, [x0, #0x1f]
    // 0xeab15c: r2 = inline_Allocate_Double()
    //     0xeab15c: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xeab160: add             x2, x2, #0x10
    //     0xeab164: cmp             x0, x2
    //     0xeab168: b.ls            #0xeab364
    //     0xeab16c: str             x2, [THR, #0x50]  ; THR::top
    //     0xeab170: sub             x2, x2, #0xf
    //     0xeab174: movz            x0, #0xe15c
    //     0xeab178: movk            x0, #0x3, lsl #16
    //     0xeab17c: stur            x0, [x2, #-1]
    // 0xeab180: StoreField: r2->field_7 = d0
    //     0xeab180: stur            d0, [x2, #7]
    // 0xeab184: r0 = inline_Allocate_Double()
    //     0xeab184: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xeab188: add             x0, x0, #0x10
    //     0xeab18c: cmp             x1, x0
    //     0xeab190: b.ls            #0xeab378
    //     0xeab194: str             x0, [THR, #0x50]  ; THR::top
    //     0xeab198: sub             x0, x0, #0xf
    //     0xeab19c: movz            x1, #0xe15c
    //     0xeab1a0: movk            x1, #0x3, lsl #16
    //     0xeab1a4: stur            x1, [x0, #-1]
    // 0xeab1a8: StoreField: r0->field_7 = d1
    //     0xeab1a8: stur            d1, [x0, #7]
    // 0xeab1ac: str             x0, [SP]
    // 0xeab1b0: ldur            x1, [fp, #-0x20]
    // 0xeab1b4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeab1b4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeab1b8: r0 = scale()
    //     0xeab1b8: bl              #0x645258  ; [package:vector_math/vector_math_64.dart] Matrix4::scale
    // 0xeab1bc: ldur            x0, [fp, #-8]
    // 0xeab1c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeab1c0: ldur            w1, [x0, #0x17]
    // 0xeab1c4: DecompressPointer r1
    //     0xeab1c4: add             x1, x1, HEAP, lsl #32
    // 0xeab1c8: LoadField: r2 = r1->field_7
    //     0xeab1c8: ldur            w2, [x1, #7]
    // 0xeab1cc: DecompressPointer r2
    //     0xeab1cc: add             x2, x2, HEAP, lsl #32
    // 0xeab1d0: cmp             w2, NULL
    // 0xeab1d4: b.eq            #0xeab1e0
    // 0xeab1d8: ldur            x1, [fp, #-0x20]
    // 0xeab1dc: r0 = multiply()
    //     0xeab1dc: bl              #0x680524  ; [package:vector_math/vector_math_64.dart] Matrix4::multiply
    // 0xeab1e0: ldur            x0, [fp, #-8]
    // 0xeab1e4: ldur            x1, [fp, #-0x10]
    // 0xeab1e8: LoadField: r2 = r1->field_13
    //     0xeab1e8: ldur            w2, [x1, #0x13]
    // 0xeab1ec: DecompressPointer r2
    //     0xeab1ec: add             x2, x2, HEAP, lsl #32
    // 0xeab1f0: LoadField: r4 = r2->field_f
    //     0xeab1f0: ldur            w4, [x2, #0xf]
    // 0xeab1f4: DecompressPointer r4
    //     0xeab1f4: add             x4, x4, HEAP, lsl #32
    // 0xeab1f8: stur            x4, [fp, #-0x10]
    // 0xeab1fc: LoadField: r5 = r0->field_1f
    //     0xeab1fc: ldur            w5, [x0, #0x1f]
    // 0xeab200: DecompressPointer r5
    //     0xeab200: add             x5, x5, HEAP, lsl #32
    // 0xeab204: mov             x2, x4
    // 0xeab208: ldur            x3, [fp, #-0x18]
    // 0xeab20c: r1 = <PdfDict<PdfDataType>>
    //     0xeab20c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeab210: ldr             x1, [x1, #0x758]
    // 0xeab214: r0 = PdfBaseFunction.colorsAndStops()
    //     0xeab214: bl              #0xeaa694  ; [package:pdf/src/pdf/obj/function.dart] PdfBaseFunction::PdfBaseFunction.colorsAndStops
    // 0xeab218: mov             x1, x0
    // 0xeab21c: ldur            x0, [fp, #-8]
    // 0xeab220: stur            x1, [fp, #-0x18]
    // 0xeab224: LoadField: d0 = r0->field_47
    //     0xeab224: ldur            d0, [x0, #0x47]
    // 0xeab228: stur            d0, [fp, #-0x50]
    // 0xeab22c: LoadField: d1 = r0->field_4f
    //     0xeab22c: ldur            d1, [x0, #0x4f]
    // 0xeab230: stur            d1, [fp, #-0x48]
    // 0xeab234: r0 = PdfPoint()
    //     0xeab234: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xeab238: ldur            d0, [fp, #-0x50]
    // 0xeab23c: stur            x0, [fp, #-0x28]
    // 0xeab240: StoreField: r0->field_7 = d0
    //     0xeab240: stur            d0, [x0, #7]
    // 0xeab244: ldur            d0, [fp, #-0x48]
    // 0xeab248: StoreField: r0->field_f = d0
    //     0xeab248: stur            d0, [x0, #0xf]
    // 0xeab24c: ldur            x1, [fp, #-8]
    // 0xeab250: LoadField: d0 = r1->field_2f
    //     0xeab250: ldur            d0, [x1, #0x2f]
    // 0xeab254: stur            d0, [fp, #-0x50]
    // 0xeab258: LoadField: d1 = r1->field_37
    //     0xeab258: ldur            d1, [x1, #0x37]
    // 0xeab25c: stur            d1, [fp, #-0x48]
    // 0xeab260: r0 = PdfPoint()
    //     0xeab260: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xeab264: ldur            d0, [fp, #-0x50]
    // 0xeab268: stur            x0, [fp, #-0x38]
    // 0xeab26c: StoreField: r0->field_7 = d0
    //     0xeab26c: stur            d0, [x0, #7]
    // 0xeab270: ldur            d0, [fp, #-0x48]
    // 0xeab274: StoreField: r0->field_f = d0
    //     0xeab274: stur            d0, [x0, #0xf]
    // 0xeab278: ldur            x1, [fp, #-8]
    // 0xeab27c: LoadField: d0 = r1->field_3f
    //     0xeab27c: ldur            d0, [x1, #0x3f]
    // 0xeab280: LoadField: d1 = r1->field_27
    //     0xeab280: ldur            d1, [x1, #0x27]
    // 0xeab284: r2 = inline_Allocate_Double()
    //     0xeab284: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0xeab288: add             x2, x2, #0x10
    //     0xeab28c: cmp             x1, x2
    //     0xeab290: b.ls            #0xeab390
    //     0xeab294: str             x2, [THR, #0x50]  ; THR::top
    //     0xeab298: sub             x2, x2, #0xf
    //     0xeab29c: movz            x1, #0xe15c
    //     0xeab2a0: movk            x1, #0x3, lsl #16
    //     0xeab2a4: stur            x1, [x2, #-1]
    // 0xeab2a8: StoreField: r2->field_7 = d0
    //     0xeab2a8: stur            d0, [x2, #7]
    // 0xeab2ac: stur            x2, [fp, #-0x30]
    // 0xeab2b0: r3 = inline_Allocate_Double()
    //     0xeab2b0: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xeab2b4: add             x3, x3, #0x10
    //     0xeab2b8: cmp             x1, x3
    //     0xeab2bc: b.ls            #0xeab3ac
    //     0xeab2c0: str             x3, [THR, #0x50]  ; THR::top
    //     0xeab2c4: sub             x3, x3, #0xf
    //     0xeab2c8: movz            x1, #0xe15c
    //     0xeab2cc: movk            x1, #0x3, lsl #16
    //     0xeab2d0: stur            x1, [x3, #-1]
    // 0xeab2d4: StoreField: r3->field_7 = d1
    //     0xeab2d4: stur            d1, [x3, #7]
    // 0xeab2d8: stur            x3, [fp, #-8]
    // 0xeab2dc: r1 = <PdfDict<PdfDataType>>
    //     0xeab2dc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeab2e0: ldr             x1, [x1, #0x758]
    // 0xeab2e4: r0 = PdfShading()
    //     0xeab2e4: bl              #0xeaa688  ; AllocatePdfShadingStub -> PdfShading (size=0x50)
    // 0xeab2e8: stur            x0, [fp, #-0x40]
    // 0xeab2ec: ldur            x16, [fp, #-0x30]
    // 0xeab2f0: ldur            lr, [fp, #-8]
    // 0xeab2f4: stp             lr, x16, [SP]
    // 0xeab2f8: mov             x1, x0
    // 0xeab2fc: ldur            x2, [fp, #-0x10]
    // 0xeab300: ldur            x3, [fp, #-0x38]
    // 0xeab304: ldur            x5, [fp, #-0x18]
    // 0xeab308: ldur            x7, [fp, #-0x28]
    // 0xeab30c: r6 = Instance_PdfShadingType
    //     0xeab30c: add             x6, PP, #0x57, lsl #12  ; [pp+0x579e8] Obj!PdfShadingType@e2ebc1
    //     0xeab310: ldr             x6, [x6, #0x9e8]
    // 0xeab314: r4 = const [0, 0x8, 0x2, 0x6, radius0, 0x6, radius1, 0x7, null]
    //     0xeab314: add             x4, PP, #0x57, lsl #12  ; [pp+0x579f0] List(9) [0, 0x8, 0x2, 0x6, "radius0", 0x6, "radius1", 0x7, Null]
    //     0xeab318: ldr             x4, [x4, #0x9f0]
    // 0xeab31c: r0 = PdfShading()
    //     0xeab31c: bl              #0xeaa4b4  ; [package:pdf/src/pdf/obj/shading.dart] PdfShading::PdfShading
    // 0xeab320: r1 = <PdfDict<PdfDataType>>
    //     0xeab320: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeab324: ldr             x1, [x1, #0x758]
    // 0xeab328: r0 = PdfShadingPattern()
    //     0xeab328: bl              #0xeaa4a8  ; AllocatePdfShadingPatternStub -> PdfShadingPattern (size=0x40)
    // 0xeab32c: mov             x4, x0
    // 0xeab330: ldur            x0, [fp, #-0x40]
    // 0xeab334: stur            x4, [fp, #-8]
    // 0xeab338: StoreField: r4->field_37 = r0
    //     0xeab338: stur            w0, [x4, #0x37]
    // 0xeab33c: mov             x1, x4
    // 0xeab340: ldur            x2, [fp, #-0x10]
    // 0xeab344: ldur            x3, [fp, #-0x20]
    // 0xeab348: r0 = PdfPattern()
    //     0xeab348: bl              #0xeaa410  ; [package:pdf/src/pdf/obj/pattern.dart] PdfPattern::PdfPattern
    // 0xeab34c: ldur            x0, [fp, #-8]
    // 0xeab350: LeaveFrame
    //     0xeab350: mov             SP, fp
    //     0xeab354: ldp             fp, lr, [SP], #0x10
    // 0xeab358: ret
    //     0xeab358: ret             
    // 0xeab35c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeab35c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeab360: b               #0xeab0f8
    // 0xeab364: stp             q0, q1, [SP, #-0x20]!
    // 0xeab368: r0 = AllocateDouble()
    //     0xeab368: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeab36c: mov             x2, x0
    // 0xeab370: ldp             q0, q1, [SP], #0x20
    // 0xeab374: b               #0xeab180
    // 0xeab378: SaveReg d1
    //     0xeab378: str             q1, [SP, #-0x10]!
    // 0xeab37c: SaveReg r2
    //     0xeab37c: str             x2, [SP, #-8]!
    // 0xeab380: r0 = AllocateDouble()
    //     0xeab380: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeab384: RestoreReg r2
    //     0xeab384: ldr             x2, [SP], #8
    // 0xeab388: RestoreReg d1
    //     0xeab388: ldr             q1, [SP], #0x10
    // 0xeab38c: b               #0xeab1a8
    // 0xeab390: stp             q0, q1, [SP, #-0x20]!
    // 0xeab394: SaveReg r0
    //     0xeab394: str             x0, [SP, #-8]!
    // 0xeab398: r0 = AllocateDouble()
    //     0xeab398: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeab39c: mov             x2, x0
    // 0xeab3a0: RestoreReg r0
    //     0xeab3a0: ldr             x0, [SP], #8
    // 0xeab3a4: ldp             q0, q1, [SP], #0x20
    // 0xeab3a8: b               #0xeab2a8
    // 0xeab3ac: SaveReg d1
    //     0xeab3ac: str             q1, [SP, #-0x10]!
    // 0xeab3b0: stp             x0, x2, [SP, #-0x10]!
    // 0xeab3b4: r0 = AllocateDouble()
    //     0xeab3b4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeab3b8: mov             x3, x0
    // 0xeab3bc: ldp             x0, x2, [SP], #0x10
    // 0xeab3c0: RestoreReg d1
    //     0xeab3c0: ldr             q1, [SP], #0x10
    // 0xeab3c4: b               #0xeab2d4
  }
}

// class id: 852, size: 0x38, field offset: 0x28
//   const constructor, 
class SvgLinearGradient extends SvgGradient {

  _ toString(/* No info */) {
    // ** addr: 0xc36034, size: 0x110
    // 0xc36034: EnterFrame
    //     0xc36034: stp             fp, lr, [SP, #-0x10]!
    //     0xc36038: mov             fp, SP
    // 0xc3603c: AllocStack(0x8)
    //     0xc3603c: sub             SP, SP, #8
    // 0xc36040: CheckStackOverflow
    //     0xc36040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc36044: cmp             SP, x16
    //     0xc36048: b.ls            #0xc3613c
    // 0xc3604c: r1 = Null
    //     0xc3604c: mov             x1, NULL
    // 0xc36050: r2 = 34
    //     0xc36050: movz            x2, #0x22
    // 0xc36054: r0 = AllocateArray()
    //     0xc36054: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc36058: r16 = SvgLinearGradient
    //     0xc36058: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ed8] Type: SvgLinearGradient
    //     0xc3605c: ldr             x16, [x16, #0xed8]
    // 0xc36060: StoreField: r0->field_f = r16
    //     0xc36060: stur            w16, [x0, #0xf]
    // 0xc36064: r16 = " userSpace:"
    //     0xc36064: add             x16, PP, #0x46, lsl #12  ; [pp+0x46e80] " userSpace:"
    //     0xc36068: ldr             x16, [x16, #0xe80]
    // 0xc3606c: StoreField: r0->field_13 = r16
    //     0xc3606c: stur            w16, [x0, #0x13]
    // 0xc36070: ldr             x1, [fp, #0x10]
    // 0xc36074: LoadField: r2 = r1->field_13
    //     0xc36074: ldur            w2, [x1, #0x13]
    // 0xc36078: DecompressPointer r2
    //     0xc36078: add             x2, x2, HEAP, lsl #32
    // 0xc3607c: ArrayStore: r0[0] = r2  ; List_4
    //     0xc3607c: stur            w2, [x0, #0x17]
    // 0xc36080: r16 = " x1:"
    //     0xc36080: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ee0] " x1:"
    //     0xc36084: ldr             x16, [x16, #0xee0]
    // 0xc36088: StoreField: r0->field_1b = r16
    //     0xc36088: stur            w16, [x0, #0x1b]
    // 0xc3608c: LoadField: r2 = r1->field_27
    //     0xc3608c: ldur            w2, [x1, #0x27]
    // 0xc36090: DecompressPointer r2
    //     0xc36090: add             x2, x2, HEAP, lsl #32
    // 0xc36094: StoreField: r0->field_1f = r2
    //     0xc36094: stur            w2, [x0, #0x1f]
    // 0xc36098: r16 = " y1:"
    //     0xc36098: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ee8] " y1:"
    //     0xc3609c: ldr             x16, [x16, #0xee8]
    // 0xc360a0: StoreField: r0->field_23 = r16
    //     0xc360a0: stur            w16, [x0, #0x23]
    // 0xc360a4: LoadField: r2 = r1->field_2b
    //     0xc360a4: ldur            w2, [x1, #0x2b]
    // 0xc360a8: DecompressPointer r2
    //     0xc360a8: add             x2, x2, HEAP, lsl #32
    // 0xc360ac: StoreField: r0->field_27 = r2
    //     0xc360ac: stur            w2, [x0, #0x27]
    // 0xc360b0: r16 = " x2:"
    //     0xc360b0: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ef0] " x2:"
    //     0xc360b4: ldr             x16, [x16, #0xef0]
    // 0xc360b8: StoreField: r0->field_2b = r16
    //     0xc360b8: stur            w16, [x0, #0x2b]
    // 0xc360bc: LoadField: r2 = r1->field_2f
    //     0xc360bc: ldur            w2, [x1, #0x2f]
    // 0xc360c0: DecompressPointer r2
    //     0xc360c0: add             x2, x2, HEAP, lsl #32
    // 0xc360c4: StoreField: r0->field_2f = r2
    //     0xc360c4: stur            w2, [x0, #0x2f]
    // 0xc360c8: r16 = " y2:"
    //     0xc360c8: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ef8] " y2:"
    //     0xc360cc: ldr             x16, [x16, #0xef8]
    // 0xc360d0: StoreField: r0->field_33 = r16
    //     0xc360d0: stur            w16, [x0, #0x33]
    // 0xc360d4: LoadField: r2 = r1->field_33
    //     0xc360d4: ldur            w2, [x1, #0x33]
    // 0xc360d8: DecompressPointer r2
    //     0xc360d8: add             x2, x2, HEAP, lsl #32
    // 0xc360dc: StoreField: r0->field_37 = r2
    //     0xc360dc: stur            w2, [x0, #0x37]
    // 0xc360e0: r16 = " colors:"
    //     0xc360e0: add             x16, PP, #0x46, lsl #12  ; [pp+0x46eb8] " colors:"
    //     0xc360e4: ldr             x16, [x16, #0xeb8]
    // 0xc360e8: StoreField: r0->field_3b = r16
    //     0xc360e8: stur            w16, [x0, #0x3b]
    // 0xc360ec: LoadField: r2 = r1->field_1b
    //     0xc360ec: ldur            w2, [x1, #0x1b]
    // 0xc360f0: DecompressPointer r2
    //     0xc360f0: add             x2, x2, HEAP, lsl #32
    // 0xc360f4: StoreField: r0->field_3f = r2
    //     0xc360f4: stur            w2, [x0, #0x3f]
    // 0xc360f8: r16 = " stops:"
    //     0xc360f8: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ec0] " stops:"
    //     0xc360fc: ldr             x16, [x16, #0xec0]
    // 0xc36100: StoreField: r0->field_43 = r16
    //     0xc36100: stur            w16, [x0, #0x43]
    // 0xc36104: LoadField: r2 = r1->field_1f
    //     0xc36104: ldur            w2, [x1, #0x1f]
    // 0xc36108: DecompressPointer r2
    //     0xc36108: add             x2, x2, HEAP, lsl #32
    // 0xc3610c: StoreField: r0->field_47 = r2
    //     0xc3610c: stur            w2, [x0, #0x47]
    // 0xc36110: r16 = " opacityList:"
    //     0xc36110: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ec8] " opacityList:"
    //     0xc36114: ldr             x16, [x16, #0xec8]
    // 0xc36118: StoreField: r0->field_4b = r16
    //     0xc36118: stur            w16, [x0, #0x4b]
    // 0xc3611c: LoadField: r2 = r1->field_23
    //     0xc3611c: ldur            w2, [x1, #0x23]
    // 0xc36120: DecompressPointer r2
    //     0xc36120: add             x2, x2, HEAP, lsl #32
    // 0xc36124: StoreField: r0->field_4f = r2
    //     0xc36124: stur            w2, [x0, #0x4f]
    // 0xc36128: str             x0, [SP]
    // 0xc3612c: r0 = _interpolate()
    //     0xc3612c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc36130: LeaveFrame
    //     0xc36130: mov             SP, fp
    //     0xc36134: ldp             fp, lr, [SP], #0x10
    // 0xc36138: ret
    //     0xc36138: ret             
    // 0xc3613c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3613c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc36140: b               #0xc3604c
  }
  factory _ SvgLinearGradient.fromXml(/* No info */) {
    // ** addr: 0xe75794, size: 0x950
    // 0xe75794: EnterFrame
    //     0xe75794: stp             fp, lr, [SP, #-0x10]!
    //     0xe75798: mov             fp, SP
    // 0xe7579c: AllocStack(0x90)
    //     0xe7579c: sub             SP, SP, #0x90
    // 0xe757a0: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xe757a0: mov             x4, x2
    //     0xe757a4: mov             x0, x3
    //     0xe757a8: stur            x2, [fp, #-8]
    //     0xe757ac: stur            x3, [fp, #-0x10]
    // 0xe757b0: CheckStackOverflow
    //     0xe757b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe757b4: cmp             SP, x16
    //     0xe757b8: b.ls            #0xe76044
    // 0xe757bc: mov             x1, x4
    // 0xe757c0: r2 = "x1"
    //     0xe757c0: add             x2, PP, #0x26, lsl #12  ; [pp+0x260d0] "x1"
    //     0xe757c4: ldr             x2, [x2, #0xd0]
    // 0xe757c8: r3 = Null
    //     0xe757c8: mov             x3, NULL
    // 0xe757cc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe757cc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe757d0: r0 = getNumeric()
    //     0xe757d0: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe757d4: cmp             w0, NULL
    // 0xe757d8: b.ne            #0xe757e4
    // 0xe757dc: r0 = Null
    //     0xe757dc: mov             x0, NULL
    // 0xe757e0: b               #0xe75814
    // 0xe757e4: mov             x1, x0
    // 0xe757e8: r0 = sizeValue()
    //     0xe757e8: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe757ec: r0 = inline_Allocate_Double()
    //     0xe757ec: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe757f0: add             x0, x0, #0x10
    //     0xe757f4: cmp             x1, x0
    //     0xe757f8: b.ls            #0xe7604c
    //     0xe757fc: str             x0, [THR, #0x50]  ; THR::top
    //     0xe75800: sub             x0, x0, #0xf
    //     0xe75804: movz            x1, #0xe15c
    //     0xe75808: movk            x1, #0x3, lsl #16
    //     0xe7580c: stur            x1, [x0, #-1]
    // 0xe75810: StoreField: r0->field_7 = d0
    //     0xe75810: stur            d0, [x0, #7]
    // 0xe75814: ldur            x1, [fp, #-8]
    // 0xe75818: stur            x0, [fp, #-0x18]
    // 0xe7581c: r2 = "y1"
    //     0xe7581c: add             x2, PP, #0x26, lsl #12  ; [pp+0x260e0] "y1"
    //     0xe75820: ldr             x2, [x2, #0xe0]
    // 0xe75824: r3 = Null
    //     0xe75824: mov             x3, NULL
    // 0xe75828: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe75828: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe7582c: r0 = getNumeric()
    //     0xe7582c: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe75830: cmp             w0, NULL
    // 0xe75834: b.ne            #0xe75840
    // 0xe75838: r0 = Null
    //     0xe75838: mov             x0, NULL
    // 0xe7583c: b               #0xe75870
    // 0xe75840: mov             x1, x0
    // 0xe75844: r0 = sizeValue()
    //     0xe75844: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe75848: r0 = inline_Allocate_Double()
    //     0xe75848: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe7584c: add             x0, x0, #0x10
    //     0xe75850: cmp             x1, x0
    //     0xe75854: b.ls            #0xe7605c
    //     0xe75858: str             x0, [THR, #0x50]  ; THR::top
    //     0xe7585c: sub             x0, x0, #0xf
    //     0xe75860: movz            x1, #0xe15c
    //     0xe75864: movk            x1, #0x3, lsl #16
    //     0xe75868: stur            x1, [x0, #-1]
    // 0xe7586c: StoreField: r0->field_7 = d0
    //     0xe7586c: stur            d0, [x0, #7]
    // 0xe75870: ldur            x1, [fp, #-8]
    // 0xe75874: stur            x0, [fp, #-0x20]
    // 0xe75878: r2 = "x2"
    //     0xe75878: add             x2, PP, #0x26, lsl #12  ; [pp+0x260d8] "x2"
    //     0xe7587c: ldr             x2, [x2, #0xd8]
    // 0xe75880: r3 = Null
    //     0xe75880: mov             x3, NULL
    // 0xe75884: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe75884: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe75888: r0 = getNumeric()
    //     0xe75888: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe7588c: cmp             w0, NULL
    // 0xe75890: b.ne            #0xe7589c
    // 0xe75894: r0 = Null
    //     0xe75894: mov             x0, NULL
    // 0xe75898: b               #0xe758cc
    // 0xe7589c: mov             x1, x0
    // 0xe758a0: r0 = sizeValue()
    //     0xe758a0: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe758a4: r0 = inline_Allocate_Double()
    //     0xe758a4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe758a8: add             x0, x0, #0x10
    //     0xe758ac: cmp             x1, x0
    //     0xe758b0: b.ls            #0xe7606c
    //     0xe758b4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe758b8: sub             x0, x0, #0xf
    //     0xe758bc: movz            x1, #0xe15c
    //     0xe758c0: movk            x1, #0x3, lsl #16
    //     0xe758c4: stur            x1, [x0, #-1]
    // 0xe758c8: StoreField: r0->field_7 = d0
    //     0xe758c8: stur            d0, [x0, #7]
    // 0xe758cc: ldur            x1, [fp, #-8]
    // 0xe758d0: stur            x0, [fp, #-0x28]
    // 0xe758d4: r2 = "y2"
    //     0xe758d4: add             x2, PP, #0x26, lsl #12  ; [pp+0x260e8] "y2"
    //     0xe758d8: ldr             x2, [x2, #0xe8]
    // 0xe758dc: r3 = Null
    //     0xe758dc: mov             x3, NULL
    // 0xe758e0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe758e0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe758e4: r0 = getNumeric()
    //     0xe758e4: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe758e8: cmp             w0, NULL
    // 0xe758ec: b.ne            #0xe758f8
    // 0xe758f0: r3 = Null
    //     0xe758f0: mov             x3, NULL
    // 0xe758f4: b               #0xe7592c
    // 0xe758f8: mov             x1, x0
    // 0xe758fc: r0 = sizeValue()
    //     0xe758fc: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe75900: r0 = inline_Allocate_Double()
    //     0xe75900: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe75904: add             x0, x0, #0x10
    //     0xe75908: cmp             x1, x0
    //     0xe7590c: b.ls            #0xe7607c
    //     0xe75910: str             x0, [THR, #0x50]  ; THR::top
    //     0xe75914: sub             x0, x0, #0xf
    //     0xe75918: movz            x1, #0xe15c
    //     0xe7591c: movk            x1, #0x3, lsl #16
    //     0xe75920: stur            x1, [x0, #-1]
    // 0xe75924: StoreField: r0->field_7 = d0
    //     0xe75924: stur            d0, [x0, #7]
    // 0xe75928: mov             x3, x0
    // 0xe7592c: ldur            x0, [fp, #-8]
    // 0xe75930: stur            x3, [fp, #-0x30]
    // 0xe75934: r1 = <PdfColor?>
    //     0xe75934: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ed18] TypeArguments: <PdfColor?>
    //     0xe75938: ldr             x1, [x1, #0xd18]
    // 0xe7593c: r2 = 0
    //     0xe7593c: movz            x2, #0
    // 0xe75940: r0 = _GrowableList()
    //     0xe75940: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe75944: r1 = <double>
    //     0xe75944: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe75948: r2 = 0
    //     0xe75948: movz            x2, #0
    // 0xe7594c: stur            x0, [fp, #-0x38]
    // 0xe75950: r0 = _GrowableList()
    //     0xe75950: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe75954: r1 = <double>
    //     0xe75954: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe75958: r2 = 0
    //     0xe75958: movz            x2, #0
    // 0xe7595c: stur            x0, [fp, #-0x40]
    // 0xe75960: r0 = _GrowableList()
    //     0xe75960: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe75964: ldur            x1, [fp, #-8]
    // 0xe75968: stur            x0, [fp, #-0x48]
    // 0xe7596c: LoadField: r2 = r1->field_f
    //     0xe7596c: ldur            w2, [x1, #0xf]
    // 0xe75970: DecompressPointer r2
    //     0xe75970: add             x2, x2, HEAP, lsl #32
    // 0xe75974: r16 = <XmlElement>
    //     0xe75974: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea98] TypeArguments: <XmlElement>
    //     0xe75978: ldr             x16, [x16, #0xa98]
    // 0xe7597c: stp             x2, x16, [SP]
    // 0xe75980: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe75980: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe75984: r0 = whereType()
    //     0xe75984: bl              #0x7b5364  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::whereType
    // 0xe75988: r1 = Function '<anonymous closure>': static.
    //     0xe75988: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ed50] AnonymousClosure: static (0xe756e4), in [package:pdf/src/svg/gradient.dart] SvgRadialGradient::SvgRadialGradient.fromXml (0xe74c48)
    //     0xe7598c: ldr             x1, [x1, #0xd50]
    // 0xe75990: r2 = Null
    //     0xe75990: mov             x2, NULL
    // 0xe75994: stur            x0, [fp, #-0x50]
    // 0xe75998: r0 = AllocateClosure()
    //     0xe75998: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7599c: ldur            x1, [fp, #-0x50]
    // 0xe759a0: mov             x2, x0
    // 0xe759a4: r0 = where()
    //     0xe759a4: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xe759a8: mov             x1, x0
    // 0xe759ac: r0 = iterator()
    //     0xe759ac: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0xe759b0: LoadField: r2 = r0->field_b
    //     0xe759b0: ldur            w2, [x0, #0xb]
    // 0xe759b4: DecompressPointer r2
    //     0xe759b4: add             x2, x2, HEAP, lsl #32
    // 0xe759b8: stur            x2, [fp, #-0x58]
    // 0xe759bc: LoadField: r3 = r0->field_f
    //     0xe759bc: ldur            w3, [x0, #0xf]
    // 0xe759c0: DecompressPointer r3
    //     0xe759c0: add             x3, x3, HEAP, lsl #32
    // 0xe759c4: stur            x3, [fp, #-0x50]
    // 0xe759c8: ldur            x4, [fp, #-0x48]
    // 0xe759cc: ldur            x6, [fp, #-0x38]
    // 0xe759d0: ldur            x5, [fp, #-0x40]
    // 0xe759d4: CheckStackOverflow
    //     0xe759d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe759d8: cmp             SP, x16
    //     0xe759dc: b.ls            #0xe7608c
    // 0xe759e0: CheckStackOverflow
    //     0xe759e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe759e4: cmp             SP, x16
    //     0xe759e8: b.ls            #0xe76094
    // 0xe759ec: r0 = LoadClassIdInstr(r2)
    //     0xe759ec: ldur            x0, [x2, #-1]
    //     0xe759f0: ubfx            x0, x0, #0xc, #0x14
    // 0xe759f4: mov             x1, x2
    // 0xe759f8: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe759f8: movz            x17, #0x292d
    //     0xe759fc: movk            x17, #0x1, lsl #16
    //     0xe75a00: add             lr, x0, x17
    //     0xe75a04: ldr             lr, [x21, lr, lsl #3]
    //     0xe75a08: blr             lr
    // 0xe75a0c: tbnz            w0, #4, #0xe75e84
    // 0xe75a10: ldur            x2, [fp, #-0x58]
    // 0xe75a14: r0 = LoadClassIdInstr(r2)
    //     0xe75a14: ldur            x0, [x2, #-1]
    //     0xe75a18: ubfx            x0, x0, #0xc, #0x14
    // 0xe75a1c: mov             x1, x2
    // 0xe75a20: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe75a20: movz            x17, #0x384d
    //     0xe75a24: movk            x17, #0x1, lsl #16
    //     0xe75a28: add             lr, x0, x17
    //     0xe75a2c: ldr             lr, [x21, lr, lsl #3]
    //     0xe75a30: blr             lr
    // 0xe75a34: ldur            x16, [fp, #-0x50]
    // 0xe75a38: stp             x0, x16, [SP]
    // 0xe75a3c: ldur            x0, [fp, #-0x50]
    // 0xe75a40: ClosureCall
    //     0xe75a40: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe75a44: ldur            x2, [x0, #0x1f]
    //     0xe75a48: blr             x2
    // 0xe75a4c: r16 = true
    //     0xe75a4c: add             x16, NULL, #0x20  ; true
    // 0xe75a50: cmp             w0, w16
    // 0xe75a54: b.eq            #0xe75a70
    // 0xe75a58: ldur            x6, [fp, #-0x38]
    // 0xe75a5c: ldur            x5, [fp, #-0x40]
    // 0xe75a60: ldur            x4, [fp, #-0x48]
    // 0xe75a64: ldur            x2, [fp, #-0x58]
    // 0xe75a68: ldur            x3, [fp, #-0x50]
    // 0xe75a6c: b               #0xe759e0
    // 0xe75a70: ldur            x2, [fp, #-0x58]
    // 0xe75a74: r0 = LoadClassIdInstr(r2)
    //     0xe75a74: ldur            x0, [x2, #-1]
    //     0xe75a78: ubfx            x0, x0, #0xc, #0x14
    // 0xe75a7c: mov             x1, x2
    // 0xe75a80: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe75a80: movz            x17, #0x384d
    //     0xe75a84: movk            x17, #0x1, lsl #16
    //     0xe75a88: add             lr, x0, x17
    //     0xe75a8c: ldr             lr, [x21, lr, lsl #3]
    //     0xe75a90: blr             lr
    // 0xe75a94: mov             x1, x0
    // 0xe75a98: stur            x0, [fp, #-0x60]
    // 0xe75a9c: r0 = convertStyle()
    //     0xe75a9c: bl              #0xe767ac  ; [package:pdf/src/svg/parser.dart] SvgParser::convertStyle
    // 0xe75aa0: ldur            x1, [fp, #-0x60]
    // 0xe75aa4: r2 = "stop-color"
    //     0xe75aa4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26398] "stop-color"
    //     0xe75aa8: ldr             x2, [x2, #0x398]
    // 0xe75aac: r3 = Null
    //     0xe75aac: mov             x3, NULL
    // 0xe75ab0: r0 = getAttributeNode()
    //     0xe75ab0: bl              #0xb1481c  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttributeNode
    // 0xe75ab4: cmp             w0, NULL
    // 0xe75ab8: b.ne            #0xe75ac4
    // 0xe75abc: r0 = Null
    //     0xe75abc: mov             x0, NULL
    // 0xe75ac0: b               #0xe75ad0
    // 0xe75ac4: LoadField: r1 = r0->field_f
    //     0xe75ac4: ldur            w1, [x0, #0xf]
    // 0xe75ac8: DecompressPointer r1
    //     0xe75ac8: add             x1, x1, HEAP, lsl #32
    // 0xe75acc: mov             x0, x1
    // 0xe75ad0: cmp             w0, NULL
    // 0xe75ad4: b.ne            #0xe75ae4
    // 0xe75ad8: r2 = "black"
    //     0xe75ad8: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ed28] "black"
    //     0xe75adc: ldr             x2, [x2, #0xd28]
    // 0xe75ae0: b               #0xe75ae8
    // 0xe75ae4: mov             x2, x0
    // 0xe75ae8: ldur            x3, [fp, #-0x10]
    // 0xe75aec: r1 = Null
    //     0xe75aec: mov             x1, NULL
    // 0xe75af0: r0 = SvgColor.fromXml()
    //     0xe75af0: bl              #0xe74098  ; [package:pdf/src/svg/color.dart] SvgColor::SvgColor.fromXml
    // 0xe75af4: stur            x0, [fp, #-0x68]
    // 0xe75af8: str             NULL, [SP]
    // 0xe75afc: ldur            x1, [fp, #-0x60]
    // 0xe75b00: r2 = "stop-opacity"
    //     0xe75b00: add             x2, PP, #0x26, lsl #12  ; [pp+0x26390] "stop-opacity"
    //     0xe75b04: ldr             x2, [x2, #0x390]
    // 0xe75b08: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xe75b08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xe75b0c: ldr             x4, [x4, #0x388]
    // 0xe75b10: r0 = getAttribute()
    //     0xe75b10: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75b14: cmp             w0, NULL
    // 0xe75b18: b.ne            #0xe75b24
    // 0xe75b1c: d0 = 1.000000
    //     0xe75b1c: fmov            d0, #1.00000000
    // 0xe75b20: b               #0xe75b2c
    // 0xe75b24: mov             x1, x0
    // 0xe75b28: r0 = parse()
    //     0xe75b28: bl              #0x61ca34  ; [dart:core] double::parse
    // 0xe75b2c: stur            d0, [fp, #-0x78]
    // 0xe75b30: str             NULL, [SP]
    // 0xe75b34: ldur            x1, [fp, #-0x60]
    // 0xe75b38: r2 = "offset"
    //     0xe75b38: add             x2, PP, #0x26, lsl #12  ; [pp+0x263b0] "offset"
    //     0xe75b3c: ldr             x2, [x2, #0x3b0]
    // 0xe75b40: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xe75b40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xe75b44: ldr             x4, [x4, #0x388]
    // 0xe75b48: r0 = getAttribute()
    //     0xe75b48: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75b4c: cmp             w0, NULL
    // 0xe75b50: b.ne            #0xe75b6c
    // 0xe75b54: r0 = SvgNumeric()
    //     0xe75b54: bl              #0xb1478c  ; AllocateSvgNumericStub -> SvgNumeric (size=0x18)
    // 0xe75b58: StoreField: r0->field_7 = rZR
    //     0xe75b58: stur            xzr, [x0, #7]
    // 0xe75b5c: r4 = Instance_SvgUnit
    //     0xe75b5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e390] Obj!SvgUnit@e2eb01
    //     0xe75b60: ldr             x4, [x4, #0x390]
    // 0xe75b64: StoreField: r0->field_f = r4
    //     0xe75b64: stur            w4, [x0, #0xf]
    // 0xe75b68: b               #0xe75b84
    // 0xe75b6c: r4 = Instance_SvgUnit
    //     0xe75b6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e390] Obj!SvgUnit@e2eb01
    //     0xe75b70: ldr             x4, [x4, #0x390]
    // 0xe75b74: mov             x2, x0
    // 0xe75b78: r1 = Null
    //     0xe75b78: mov             x1, NULL
    // 0xe75b7c: r3 = Null
    //     0xe75b7c: mov             x3, NULL
    // 0xe75b80: r0 = SvgNumeric()
    //     0xe75b80: bl              #0xb146a4  ; [package:pdf/src/svg/parser.dart] SvgNumeric::SvgNumeric
    // 0xe75b84: LoadField: r1 = r0->field_f
    //     0xe75b84: ldur            w1, [x0, #0xf]
    // 0xe75b88: DecompressPointer r1
    //     0xe75b88: add             x1, x1, HEAP, lsl #32
    // 0xe75b8c: LoadField: r2 = r1->field_7
    //     0xe75b8c: ldur            x2, [x1, #7]
    // 0xe75b90: cmp             x2, #3
    // 0xe75b94: b.gt            #0xe75c1c
    // 0xe75b98: cmp             x2, #1
    // 0xe75b9c: b.gt            #0xe75bcc
    // 0xe75ba0: cmp             x2, #0
    // 0xe75ba4: b.gt            #0xe75bb0
    // 0xe75ba8: d0 = 100.000000
    //     0xe75ba8: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75bac: b               #0xe75ca4
    // 0xe75bb0: d0 = 2.834646
    //     0xe75bb0: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e368] IMM: double(2.834645669291339) from 0x4006ad5ab56ad5ac
    //     0xe75bb4: ldr             d0, [x17, #0x368]
    // 0xe75bb8: LoadField: d1 = r0->field_7
    //     0xe75bb8: ldur            d1, [x0, #7]
    // 0xe75bbc: fmul            d2, d1, d0
    // 0xe75bc0: mov             v1.16b, v2.16b
    // 0xe75bc4: d0 = 100.000000
    //     0xe75bc4: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75bc8: b               #0xe75ca8
    // 0xe75bcc: d0 = 2.834646
    //     0xe75bcc: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e368] IMM: double(2.834645669291339) from 0x4006ad5ab56ad5ac
    //     0xe75bd0: ldr             d0, [x17, #0x368]
    // 0xe75bd4: cmp             x2, #2
    // 0xe75bd8: b.gt            #0xe75bf8
    // 0xe75bdc: d1 = 28.346457
    //     0xe75bdc: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e370] IMM: double(28.346456692913385) from 0x403c58b162c58b16
    //     0xe75be0: ldr             d1, [x17, #0x370]
    // 0xe75be4: LoadField: d2 = r0->field_7
    //     0xe75be4: ldur            d2, [x0, #7]
    // 0xe75be8: fmul            d3, d2, d1
    // 0xe75bec: mov             v1.16b, v3.16b
    // 0xe75bf0: d0 = 100.000000
    //     0xe75bf0: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75bf4: b               #0xe75ca8
    // 0xe75bf8: d1 = 28.346457
    //     0xe75bf8: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e370] IMM: double(28.346456692913385) from 0x403c58b162c58b16
    //     0xe75bfc: ldr             d1, [x17, #0x370]
    // 0xe75c00: d2 = 72.000000
    //     0xe75c00: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e378] IMM: double(72) from 0x4052000000000000
    //     0xe75c04: ldr             d2, [x17, #0x378]
    // 0xe75c08: LoadField: d3 = r0->field_7
    //     0xe75c08: ldur            d3, [x0, #7]
    // 0xe75c0c: fmul            d4, d3, d2
    // 0xe75c10: mov             v1.16b, v4.16b
    // 0xe75c14: d0 = 100.000000
    //     0xe75c14: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75c18: b               #0xe75ca8
    // 0xe75c1c: d0 = 2.834646
    //     0xe75c1c: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e368] IMM: double(2.834645669291339) from 0x4006ad5ab56ad5ac
    //     0xe75c20: ldr             d0, [x17, #0x368]
    // 0xe75c24: d1 = 28.346457
    //     0xe75c24: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e370] IMM: double(28.346456692913385) from 0x403c58b162c58b16
    //     0xe75c28: ldr             d1, [x17, #0x370]
    // 0xe75c2c: d2 = 72.000000
    //     0xe75c2c: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e378] IMM: double(72) from 0x4052000000000000
    //     0xe75c30: ldr             d2, [x17, #0x378]
    // 0xe75c34: cmp             x2, #5
    // 0xe75c38: b.gt            #0xe75ca0
    // 0xe75c3c: cmp             x2, #4
    // 0xe75c40: b.gt            #0xe75c8c
    // 0xe75c44: LoadField: d3 = r0->field_7
    //     0xe75c44: ldur            d3, [x0, #7]
    // 0xe75c48: stur            d3, [fp, #-0x80]
    // 0xe75c4c: LoadField: r1 = r0->field_13
    //     0xe75c4c: ldur            w1, [x0, #0x13]
    // 0xe75c50: DecompressPointer r1
    //     0xe75c50: add             x1, x1, HEAP, lsl #32
    // 0xe75c54: cmp             w1, NULL
    // 0xe75c58: b.eq            #0xe7609c
    // 0xe75c5c: LoadField: r0 = r1->field_37
    //     0xe75c5c: ldur            w0, [x1, #0x37]
    // 0xe75c60: DecompressPointer r0
    //     0xe75c60: add             x0, x0, HEAP, lsl #32
    // 0xe75c64: cmp             w0, NULL
    // 0xe75c68: b.eq            #0xe760a0
    // 0xe75c6c: mov             x1, x0
    // 0xe75c70: r0 = sizeValue()
    //     0xe75c70: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe75c74: mov             v1.16b, v0.16b
    // 0xe75c78: ldur            d0, [fp, #-0x80]
    // 0xe75c7c: fmul            d2, d0, d1
    // 0xe75c80: mov             v1.16b, v2.16b
    // 0xe75c84: d0 = 100.000000
    //     0xe75c84: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75c88: b               #0xe75ca8
    // 0xe75c8c: d0 = 100.000000
    //     0xe75c8c: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75c90: LoadField: d1 = r0->field_7
    //     0xe75c90: ldur            d1, [x0, #7]
    // 0xe75c94: fdiv            d2, d1, d0
    // 0xe75c98: mov             v1.16b, v2.16b
    // 0xe75c9c: b               #0xe75ca8
    // 0xe75ca0: d0 = 100.000000
    //     0xe75ca0: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe75ca4: LoadField: d1 = r0->field_7
    //     0xe75ca4: ldur            d1, [x0, #7]
    // 0xe75ca8: ldur            x2, [fp, #-0x38]
    // 0xe75cac: ldur            x0, [fp, #-0x68]
    // 0xe75cb0: stur            d1, [fp, #-0x80]
    // 0xe75cb4: LoadField: r3 = r0->field_7
    //     0xe75cb4: ldur            w3, [x0, #7]
    // 0xe75cb8: DecompressPointer r3
    //     0xe75cb8: add             x3, x3, HEAP, lsl #32
    // 0xe75cbc: stur            x3, [fp, #-0x60]
    // 0xe75cc0: LoadField: r0 = r2->field_b
    //     0xe75cc0: ldur            w0, [x2, #0xb]
    // 0xe75cc4: LoadField: r1 = r2->field_f
    //     0xe75cc4: ldur            w1, [x2, #0xf]
    // 0xe75cc8: DecompressPointer r1
    //     0xe75cc8: add             x1, x1, HEAP, lsl #32
    // 0xe75ccc: LoadField: r4 = r1->field_b
    //     0xe75ccc: ldur            w4, [x1, #0xb]
    // 0xe75cd0: r5 = LoadInt32Instr(r0)
    //     0xe75cd0: sbfx            x5, x0, #1, #0x1f
    // 0xe75cd4: stur            x5, [fp, #-0x70]
    // 0xe75cd8: r0 = LoadInt32Instr(r4)
    //     0xe75cd8: sbfx            x0, x4, #1, #0x1f
    // 0xe75cdc: cmp             x5, x0
    // 0xe75ce0: b.ne            #0xe75cec
    // 0xe75ce4: mov             x1, x2
    // 0xe75ce8: r0 = _growToNextCapacity()
    //     0xe75ce8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe75cec: ldur            x2, [fp, #-0x38]
    // 0xe75cf0: ldur            x4, [fp, #-0x40]
    // 0xe75cf4: ldur            x3, [fp, #-0x70]
    // 0xe75cf8: add             x0, x3, #1
    // 0xe75cfc: lsl             x1, x0, #1
    // 0xe75d00: StoreField: r2->field_b = r1
    //     0xe75d00: stur            w1, [x2, #0xb]
    // 0xe75d04: LoadField: r1 = r2->field_f
    //     0xe75d04: ldur            w1, [x2, #0xf]
    // 0xe75d08: DecompressPointer r1
    //     0xe75d08: add             x1, x1, HEAP, lsl #32
    // 0xe75d0c: ldur            x0, [fp, #-0x60]
    // 0xe75d10: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe75d10: add             x25, x1, x3, lsl #2
    //     0xe75d14: add             x25, x25, #0xf
    //     0xe75d18: str             w0, [x25]
    //     0xe75d1c: tbz             w0, #0, #0xe75d38
    //     0xe75d20: ldurb           w16, [x1, #-1]
    //     0xe75d24: ldurb           w17, [x0, #-1]
    //     0xe75d28: and             x16, x17, x16, lsr #2
    //     0xe75d2c: tst             x16, HEAP, lsr #32
    //     0xe75d30: b.eq            #0xe75d38
    //     0xe75d34: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe75d38: LoadField: r0 = r4->field_b
    //     0xe75d38: ldur            w0, [x4, #0xb]
    // 0xe75d3c: LoadField: r1 = r4->field_f
    //     0xe75d3c: ldur            w1, [x4, #0xf]
    // 0xe75d40: DecompressPointer r1
    //     0xe75d40: add             x1, x1, HEAP, lsl #32
    // 0xe75d44: LoadField: r3 = r1->field_b
    //     0xe75d44: ldur            w3, [x1, #0xb]
    // 0xe75d48: r5 = LoadInt32Instr(r0)
    //     0xe75d48: sbfx            x5, x0, #1, #0x1f
    // 0xe75d4c: stur            x5, [fp, #-0x70]
    // 0xe75d50: r0 = LoadInt32Instr(r3)
    //     0xe75d50: sbfx            x0, x3, #1, #0x1f
    // 0xe75d54: cmp             x5, x0
    // 0xe75d58: b.ne            #0xe75d64
    // 0xe75d5c: mov             x1, x4
    // 0xe75d60: r0 = _growToNextCapacity()
    //     0xe75d60: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe75d64: ldur            x2, [fp, #-0x40]
    // 0xe75d68: ldur            x4, [fp, #-0x48]
    // 0xe75d6c: ldur            d0, [fp, #-0x80]
    // 0xe75d70: ldur            x3, [fp, #-0x70]
    // 0xe75d74: add             x0, x3, #1
    // 0xe75d78: lsl             x1, x0, #1
    // 0xe75d7c: StoreField: r2->field_b = r1
    //     0xe75d7c: stur            w1, [x2, #0xb]
    // 0xe75d80: LoadField: r1 = r2->field_f
    //     0xe75d80: ldur            w1, [x2, #0xf]
    // 0xe75d84: DecompressPointer r1
    //     0xe75d84: add             x1, x1, HEAP, lsl #32
    // 0xe75d88: r0 = inline_Allocate_Double()
    //     0xe75d88: ldp             x0, x5, [THR, #0x50]  ; THR::top
    //     0xe75d8c: add             x0, x0, #0x10
    //     0xe75d90: cmp             x5, x0
    //     0xe75d94: b.ls            #0xe760a4
    //     0xe75d98: str             x0, [THR, #0x50]  ; THR::top
    //     0xe75d9c: sub             x0, x0, #0xf
    //     0xe75da0: movz            x5, #0xe15c
    //     0xe75da4: movk            x5, #0x3, lsl #16
    //     0xe75da8: stur            x5, [x0, #-1]
    // 0xe75dac: StoreField: r0->field_7 = d0
    //     0xe75dac: stur            d0, [x0, #7]
    // 0xe75db0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe75db0: add             x25, x1, x3, lsl #2
    //     0xe75db4: add             x25, x25, #0xf
    //     0xe75db8: str             w0, [x25]
    //     0xe75dbc: tbz             w0, #0, #0xe75dd8
    //     0xe75dc0: ldurb           w16, [x1, #-1]
    //     0xe75dc4: ldurb           w17, [x0, #-1]
    //     0xe75dc8: and             x16, x17, x16, lsr #2
    //     0xe75dcc: tst             x16, HEAP, lsr #32
    //     0xe75dd0: b.eq            #0xe75dd8
    //     0xe75dd4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe75dd8: LoadField: r0 = r4->field_b
    //     0xe75dd8: ldur            w0, [x4, #0xb]
    // 0xe75ddc: LoadField: r1 = r4->field_f
    //     0xe75ddc: ldur            w1, [x4, #0xf]
    // 0xe75de0: DecompressPointer r1
    //     0xe75de0: add             x1, x1, HEAP, lsl #32
    // 0xe75de4: LoadField: r3 = r1->field_b
    //     0xe75de4: ldur            w3, [x1, #0xb]
    // 0xe75de8: r5 = LoadInt32Instr(r0)
    //     0xe75de8: sbfx            x5, x0, #1, #0x1f
    // 0xe75dec: stur            x5, [fp, #-0x70]
    // 0xe75df0: r0 = LoadInt32Instr(r3)
    //     0xe75df0: sbfx            x0, x3, #1, #0x1f
    // 0xe75df4: cmp             x5, x0
    // 0xe75df8: b.ne            #0xe75e04
    // 0xe75dfc: mov             x1, x4
    // 0xe75e00: r0 = _growToNextCapacity()
    //     0xe75e00: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe75e04: ldur            x3, [fp, #-0x48]
    // 0xe75e08: ldur            d0, [fp, #-0x78]
    // 0xe75e0c: ldur            x2, [fp, #-0x70]
    // 0xe75e10: add             x0, x2, #1
    // 0xe75e14: lsl             x1, x0, #1
    // 0xe75e18: StoreField: r3->field_b = r1
    //     0xe75e18: stur            w1, [x3, #0xb]
    // 0xe75e1c: LoadField: r1 = r3->field_f
    //     0xe75e1c: ldur            w1, [x3, #0xf]
    // 0xe75e20: DecompressPointer r1
    //     0xe75e20: add             x1, x1, HEAP, lsl #32
    // 0xe75e24: r0 = inline_Allocate_Double()
    //     0xe75e24: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xe75e28: add             x0, x0, #0x10
    //     0xe75e2c: cmp             x4, x0
    //     0xe75e30: b.ls            #0xe760c4
    //     0xe75e34: str             x0, [THR, #0x50]  ; THR::top
    //     0xe75e38: sub             x0, x0, #0xf
    //     0xe75e3c: movz            x4, #0xe15c
    //     0xe75e40: movk            x4, #0x3, lsl #16
    //     0xe75e44: stur            x4, [x0, #-1]
    // 0xe75e48: StoreField: r0->field_7 = d0
    //     0xe75e48: stur            d0, [x0, #7]
    // 0xe75e4c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe75e4c: add             x25, x1, x2, lsl #2
    //     0xe75e50: add             x25, x25, #0xf
    //     0xe75e54: str             w0, [x25]
    //     0xe75e58: tbz             w0, #0, #0xe75e74
    //     0xe75e5c: ldurb           w16, [x1, #-1]
    //     0xe75e60: ldurb           w17, [x0, #-1]
    //     0xe75e64: and             x16, x17, x16, lsr #2
    //     0xe75e68: tst             x16, HEAP, lsr #32
    //     0xe75e6c: b.eq            #0xe75e74
    //     0xe75e70: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe75e74: mov             x4, x3
    // 0xe75e78: ldur            x2, [fp, #-0x58]
    // 0xe75e7c: ldur            x3, [fp, #-0x50]
    // 0xe75e80: b               #0xe759cc
    // 0xe75e84: ldur            x3, [fp, #-0x48]
    // 0xe75e88: ldur            x1, [fp, #-8]
    // 0xe75e8c: r2 = "gradientUnits"
    //     0xe75e8c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26338] "gradientUnits"
    //     0xe75e90: ldr             x2, [x2, #0x338]
    // 0xe75e94: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe75e94: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe75e98: r0 = getAttribute()
    //     0xe75e98: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75e9c: stur            x0, [fp, #-0x50]
    // 0xe75ea0: r16 = "userSpaceOnUse"
    //     0xe75ea0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26340] "userSpaceOnUse"
    //     0xe75ea4: ldr             x16, [x16, #0x340]
    // 0xe75ea8: stp             x0, x16, [SP]
    // 0xe75eac: r0 = ==()
    //     0xe75eac: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe75eb0: tbnz            w0, #4, #0xe75ec0
    // 0xe75eb4: r9 = Instance_GradientUnits
    //     0xe75eb4: add             x9, PP, #0x3e, lsl #12  ; [pp+0x3ed30] Obj!GradientUnits@e2eb41
    //     0xe75eb8: ldr             x9, [x9, #0xd30]
    // 0xe75ebc: b               #0xe75ee8
    // 0xe75ec0: r16 = "objectBoundingBox"
    //     0xe75ec0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed38] "objectBoundingBox"
    //     0xe75ec4: ldr             x16, [x16, #0xd38]
    // 0xe75ec8: ldur            lr, [fp, #-0x50]
    // 0xe75ecc: stp             lr, x16, [SP]
    // 0xe75ed0: r0 = ==()
    //     0xe75ed0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe75ed4: tbnz            w0, #4, #0xe75ee4
    // 0xe75ed8: r9 = Instance_GradientUnits
    //     0xe75ed8: add             x9, PP, #0x3e, lsl #12  ; [pp+0x3ed40] Obj!GradientUnits@e2eb21
    //     0xe75edc: ldr             x9, [x9, #0xd40]
    // 0xe75ee0: b               #0xe75ee8
    // 0xe75ee4: r9 = Null
    //     0xe75ee4: mov             x9, NULL
    // 0xe75ee8: ldur            x8, [fp, #-0x18]
    // 0xe75eec: ldur            x7, [fp, #-0x20]
    // 0xe75ef0: ldur            x6, [fp, #-0x28]
    // 0xe75ef4: ldur            x5, [fp, #-0x30]
    // 0xe75ef8: ldur            x4, [fp, #-0x38]
    // 0xe75efc: ldur            x3, [fp, #-0x40]
    // 0xe75f00: ldur            x0, [fp, #-0x48]
    // 0xe75f04: ldur            x1, [fp, #-8]
    // 0xe75f08: stur            x9, [fp, #-0x50]
    // 0xe75f0c: r2 = "gradientTransform"
    //     0xe75f0c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26358] "gradientTransform"
    //     0xe75f10: ldr             x2, [x2, #0x358]
    // 0xe75f14: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe75f14: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe75f18: r0 = getAttribute()
    //     0xe75f18: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75f1c: mov             x2, x0
    // 0xe75f20: r1 = Null
    //     0xe75f20: mov             x1, NULL
    // 0xe75f24: r0 = SvgTransform.fromString()
    //     0xe75f24: bl              #0xe6e1a0  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromString
    // 0xe75f28: stur            x0, [fp, #-0x58]
    // 0xe75f2c: r0 = SvgLinearGradient()
    //     0xe75f2c: bl              #0xe76260  ; AllocateSvgLinearGradientStub -> SvgLinearGradient (size=0x38)
    // 0xe75f30: mov             x3, x0
    // 0xe75f34: ldur            x0, [fp, #-0x18]
    // 0xe75f38: stur            x3, [fp, #-0x60]
    // 0xe75f3c: StoreField: r3->field_27 = r0
    //     0xe75f3c: stur            w0, [x3, #0x27]
    // 0xe75f40: ldur            x0, [fp, #-0x20]
    // 0xe75f44: StoreField: r3->field_2b = r0
    //     0xe75f44: stur            w0, [x3, #0x2b]
    // 0xe75f48: ldur            x0, [fp, #-0x28]
    // 0xe75f4c: StoreField: r3->field_2f = r0
    //     0xe75f4c: stur            w0, [x3, #0x2f]
    // 0xe75f50: ldur            x0, [fp, #-0x30]
    // 0xe75f54: StoreField: r3->field_33 = r0
    //     0xe75f54: stur            w0, [x3, #0x33]
    // 0xe75f58: ldur            x0, [fp, #-0x50]
    // 0xe75f5c: StoreField: r3->field_13 = r0
    //     0xe75f5c: stur            w0, [x3, #0x13]
    // 0xe75f60: ldur            x0, [fp, #-0x58]
    // 0xe75f64: ArrayStore: r3[0] = r0  ; List_4
    //     0xe75f64: stur            w0, [x3, #0x17]
    // 0xe75f68: ldur            x0, [fp, #-0x38]
    // 0xe75f6c: StoreField: r3->field_1b = r0
    //     0xe75f6c: stur            w0, [x3, #0x1b]
    // 0xe75f70: ldur            x0, [fp, #-0x40]
    // 0xe75f74: StoreField: r3->field_1f = r0
    //     0xe75f74: stur            w0, [x3, #0x1f]
    // 0xe75f78: ldur            x0, [fp, #-0x48]
    // 0xe75f7c: StoreField: r3->field_23 = r0
    //     0xe75f7c: stur            w0, [x3, #0x23]
    // 0xe75f80: r0 = false
    //     0xe75f80: add             x0, NULL, #0x30  ; false
    // 0xe75f84: StoreField: r3->field_f = r0
    //     0xe75f84: stur            w0, [x3, #0xf]
    // 0xe75f88: ldur            x1, [fp, #-8]
    // 0xe75f8c: r2 = "href"
    //     0xe75f8c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xe75f90: ldr             x2, [x2, #0x28]
    // 0xe75f94: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe75f94: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe75f98: r0 = getAttribute()
    //     0xe75f98: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75f9c: cmp             w0, NULL
    // 0xe75fa0: b.ne            #0xe75fd0
    // 0xe75fa4: r16 = "http://www.w3.org/1999/xlink"
    //     0xe75fa4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eaf8] "http://www.w3.org/1999/xlink"
    //     0xe75fa8: ldr             x16, [x16, #0xaf8]
    // 0xe75fac: str             x16, [SP]
    // 0xe75fb0: ldur            x1, [fp, #-8]
    // 0xe75fb4: r2 = "href"
    //     0xe75fb4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xe75fb8: ldr             x2, [x2, #0x28]
    // 0xe75fbc: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xe75fbc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xe75fc0: ldr             x4, [x4, #0x388]
    // 0xe75fc4: r0 = getAttribute()
    //     0xe75fc4: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe75fc8: mov             x1, x0
    // 0xe75fcc: b               #0xe75fd4
    // 0xe75fd0: mov             x1, x0
    // 0xe75fd4: cmp             w1, NULL
    // 0xe75fd8: b.eq            #0xe76034
    // 0xe75fdc: ldur            x3, [fp, #-0x10]
    // 0xe75fe0: LoadField: r0 = r3->field_7
    //     0xe75fe0: ldur            w0, [x3, #7]
    // 0xe75fe4: DecompressPointer r0
    //     0xe75fe4: add             x0, x0, HEAP, lsl #32
    // 0xe75fe8: stur            x0, [fp, #-8]
    // 0xe75fec: r2 = 1
    //     0xe75fec: movz            x2, #0x1
    // 0xe75ff0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe75ff0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe75ff4: r0 = substring()
    //     0xe75ff4: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe75ff8: ldur            x1, [fp, #-8]
    // 0xe75ffc: mov             x2, x0
    // 0xe76000: r0 = findById()
    //     0xe76000: bl              #0xe6f138  ; [package:pdf/src/svg/parser.dart] SvgParser::findById
    // 0xe76004: cmp             w0, NULL
    // 0xe76008: b.eq            #0xe76034
    // 0xe7600c: mov             x2, x0
    // 0xe76010: ldur            x3, [fp, #-0x10]
    // 0xe76014: r1 = Null
    //     0xe76014: mov             x1, NULL
    // 0xe76018: r0 = SvgLinearGradient.fromXml()
    //     0xe76018: bl              #0xe75794  ; [package:pdf/src/svg/gradient.dart] SvgLinearGradient::SvgLinearGradient.fromXml
    // 0xe7601c: mov             x1, x0
    // 0xe76020: ldur            x2, [fp, #-0x60]
    // 0xe76024: r0 = mergeWith()
    //     0xe76024: bl              #0xe760e4  ; [package:pdf/src/svg/gradient.dart] SvgLinearGradient::mergeWith
    // 0xe76028: LeaveFrame
    //     0xe76028: mov             SP, fp
    //     0xe7602c: ldp             fp, lr, [SP], #0x10
    // 0xe76030: ret
    //     0xe76030: ret             
    // 0xe76034: ldur            x0, [fp, #-0x60]
    // 0xe76038: LeaveFrame
    //     0xe76038: mov             SP, fp
    //     0xe7603c: ldp             fp, lr, [SP], #0x10
    // 0xe76040: ret
    //     0xe76040: ret             
    // 0xe76044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe76044: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76048: b               #0xe757bc
    // 0xe7604c: SaveReg d0
    //     0xe7604c: str             q0, [SP, #-0x10]!
    // 0xe76050: r0 = AllocateDouble()
    //     0xe76050: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe76054: RestoreReg d0
    //     0xe76054: ldr             q0, [SP], #0x10
    // 0xe76058: b               #0xe75810
    // 0xe7605c: SaveReg d0
    //     0xe7605c: str             q0, [SP, #-0x10]!
    // 0xe76060: r0 = AllocateDouble()
    //     0xe76060: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe76064: RestoreReg d0
    //     0xe76064: ldr             q0, [SP], #0x10
    // 0xe76068: b               #0xe7586c
    // 0xe7606c: SaveReg d0
    //     0xe7606c: str             q0, [SP, #-0x10]!
    // 0xe76070: r0 = AllocateDouble()
    //     0xe76070: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe76074: RestoreReg d0
    //     0xe76074: ldr             q0, [SP], #0x10
    // 0xe76078: b               #0xe758c8
    // 0xe7607c: SaveReg d0
    //     0xe7607c: str             q0, [SP, #-0x10]!
    // 0xe76080: r0 = AllocateDouble()
    //     0xe76080: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe76084: RestoreReg d0
    //     0xe76084: ldr             q0, [SP], #0x10
    // 0xe76088: b               #0xe75924
    // 0xe7608c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7608c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76090: b               #0xe759e0
    // 0xe76094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe76094: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76098: b               #0xe759ec
    // 0xe7609c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe7609c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe760a0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe760a0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe760a4: SaveReg d0
    //     0xe760a4: str             q0, [SP, #-0x10]!
    // 0xe760a8: stp             x3, x4, [SP, #-0x10]!
    // 0xe760ac: stp             x1, x2, [SP, #-0x10]!
    // 0xe760b0: r0 = AllocateDouble()
    //     0xe760b0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe760b4: ldp             x1, x2, [SP], #0x10
    // 0xe760b8: ldp             x3, x4, [SP], #0x10
    // 0xe760bc: RestoreReg d0
    //     0xe760bc: ldr             q0, [SP], #0x10
    // 0xe760c0: b               #0xe75dac
    // 0xe760c4: SaveReg d0
    //     0xe760c4: str             q0, [SP, #-0x10]!
    // 0xe760c8: stp             x2, x3, [SP, #-0x10]!
    // 0xe760cc: SaveReg r1
    //     0xe760cc: str             x1, [SP, #-8]!
    // 0xe760d0: r0 = AllocateDouble()
    //     0xe760d0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe760d4: RestoreReg r1
    //     0xe760d4: ldr             x1, [SP], #8
    // 0xe760d8: ldp             x2, x3, [SP], #0x10
    // 0xe760dc: RestoreReg d0
    //     0xe760dc: ldr             q0, [SP], #0x10
    // 0xe760e0: b               #0xe75e48
  }
  _ mergeWith(/* No info */) {
    // ** addr: 0xe760e4, size: 0x17c
    // 0xe760e4: EnterFrame
    //     0xe760e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe760e8: mov             fp, SP
    // 0xe760ec: AllocStack(0x48)
    //     0xe760ec: sub             SP, SP, #0x48
    // 0xe760f0: LoadField: r0 = r2->field_13
    //     0xe760f0: ldur            w0, [x2, #0x13]
    // 0xe760f4: DecompressPointer r0
    //     0xe760f4: add             x0, x0, HEAP, lsl #32
    // 0xe760f8: cmp             w0, NULL
    // 0xe760fc: b.ne            #0xe76108
    // 0xe76100: LoadField: r0 = r1->field_13
    //     0xe76100: ldur            w0, [x1, #0x13]
    // 0xe76104: DecompressPointer r0
    //     0xe76104: add             x0, x0, HEAP, lsl #32
    // 0xe76108: stur            x0, [fp, #-0x48]
    // 0xe7610c: LoadField: r3 = r2->field_27
    //     0xe7610c: ldur            w3, [x2, #0x27]
    // 0xe76110: DecompressPointer r3
    //     0xe76110: add             x3, x3, HEAP, lsl #32
    // 0xe76114: cmp             w3, NULL
    // 0xe76118: b.ne            #0xe76124
    // 0xe7611c: LoadField: r3 = r1->field_27
    //     0xe7611c: ldur            w3, [x1, #0x27]
    // 0xe76120: DecompressPointer r3
    //     0xe76120: add             x3, x3, HEAP, lsl #32
    // 0xe76124: stur            x3, [fp, #-0x40]
    // 0xe76128: LoadField: r4 = r2->field_2b
    //     0xe76128: ldur            w4, [x2, #0x2b]
    // 0xe7612c: DecompressPointer r4
    //     0xe7612c: add             x4, x4, HEAP, lsl #32
    // 0xe76130: cmp             w4, NULL
    // 0xe76134: b.ne            #0xe76140
    // 0xe76138: LoadField: r4 = r1->field_2b
    //     0xe76138: ldur            w4, [x1, #0x2b]
    // 0xe7613c: DecompressPointer r4
    //     0xe7613c: add             x4, x4, HEAP, lsl #32
    // 0xe76140: stur            x4, [fp, #-0x38]
    // 0xe76144: LoadField: r5 = r2->field_2f
    //     0xe76144: ldur            w5, [x2, #0x2f]
    // 0xe76148: DecompressPointer r5
    //     0xe76148: add             x5, x5, HEAP, lsl #32
    // 0xe7614c: cmp             w5, NULL
    // 0xe76150: b.ne            #0xe7615c
    // 0xe76154: LoadField: r5 = r1->field_2f
    //     0xe76154: ldur            w5, [x1, #0x2f]
    // 0xe76158: DecompressPointer r5
    //     0xe76158: add             x5, x5, HEAP, lsl #32
    // 0xe7615c: stur            x5, [fp, #-0x30]
    // 0xe76160: LoadField: r6 = r2->field_33
    //     0xe76160: ldur            w6, [x2, #0x33]
    // 0xe76164: DecompressPointer r6
    //     0xe76164: add             x6, x6, HEAP, lsl #32
    // 0xe76168: cmp             w6, NULL
    // 0xe7616c: b.ne            #0xe76178
    // 0xe76170: LoadField: r6 = r1->field_33
    //     0xe76170: ldur            w6, [x1, #0x33]
    // 0xe76174: DecompressPointer r6
    //     0xe76174: add             x6, x6, HEAP, lsl #32
    // 0xe76178: stur            x6, [fp, #-0x28]
    // 0xe7617c: ArrayLoad: r7 = r2[0]  ; List_4
    //     0xe7617c: ldur            w7, [x2, #0x17]
    // 0xe76180: DecompressPointer r7
    //     0xe76180: add             x7, x7, HEAP, lsl #32
    // 0xe76184: LoadField: r8 = r7->field_7
    //     0xe76184: ldur            w8, [x7, #7]
    // 0xe76188: DecompressPointer r8
    //     0xe76188: add             x8, x8, HEAP, lsl #32
    // 0xe7618c: cmp             w8, NULL
    // 0xe76190: b.ne            #0xe7619c
    // 0xe76194: ArrayLoad: r7 = r1[0]  ; List_4
    //     0xe76194: ldur            w7, [x1, #0x17]
    // 0xe76198: DecompressPointer r7
    //     0xe76198: add             x7, x7, HEAP, lsl #32
    // 0xe7619c: stur            x7, [fp, #-0x20]
    // 0xe761a0: LoadField: r8 = r2->field_1b
    //     0xe761a0: ldur            w8, [x2, #0x1b]
    // 0xe761a4: DecompressPointer r8
    //     0xe761a4: add             x8, x8, HEAP, lsl #32
    // 0xe761a8: LoadField: r9 = r8->field_b
    //     0xe761a8: ldur            w9, [x8, #0xb]
    // 0xe761ac: cbnz            w9, #0xe761b8
    // 0xe761b0: LoadField: r8 = r1->field_1b
    //     0xe761b0: ldur            w8, [x1, #0x1b]
    // 0xe761b4: DecompressPointer r8
    //     0xe761b4: add             x8, x8, HEAP, lsl #32
    // 0xe761b8: stur            x8, [fp, #-0x18]
    // 0xe761bc: LoadField: r9 = r2->field_1f
    //     0xe761bc: ldur            w9, [x2, #0x1f]
    // 0xe761c0: DecompressPointer r9
    //     0xe761c0: add             x9, x9, HEAP, lsl #32
    // 0xe761c4: LoadField: r10 = r9->field_b
    //     0xe761c4: ldur            w10, [x9, #0xb]
    // 0xe761c8: cbnz            w10, #0xe761d4
    // 0xe761cc: LoadField: r9 = r1->field_1f
    //     0xe761cc: ldur            w9, [x1, #0x1f]
    // 0xe761d0: DecompressPointer r9
    //     0xe761d0: add             x9, x9, HEAP, lsl #32
    // 0xe761d4: stur            x9, [fp, #-0x10]
    // 0xe761d8: LoadField: r10 = r2->field_23
    //     0xe761d8: ldur            w10, [x2, #0x23]
    // 0xe761dc: DecompressPointer r10
    //     0xe761dc: add             x10, x10, HEAP, lsl #32
    // 0xe761e0: LoadField: r2 = r10->field_b
    //     0xe761e0: ldur            w2, [x10, #0xb]
    // 0xe761e4: cbz             w2, #0xe761f0
    // 0xe761e8: mov             x1, x10
    // 0xe761ec: b               #0xe761fc
    // 0xe761f0: LoadField: r2 = r1->field_23
    //     0xe761f0: ldur            w2, [x1, #0x23]
    // 0xe761f4: DecompressPointer r2
    //     0xe761f4: add             x2, x2, HEAP, lsl #32
    // 0xe761f8: mov             x1, x2
    // 0xe761fc: stur            x1, [fp, #-8]
    // 0xe76200: r0 = SvgLinearGradient()
    //     0xe76200: bl              #0xe76260  ; AllocateSvgLinearGradientStub -> SvgLinearGradient (size=0x38)
    // 0xe76204: ldur            x1, [fp, #-0x40]
    // 0xe76208: StoreField: r0->field_27 = r1
    //     0xe76208: stur            w1, [x0, #0x27]
    // 0xe7620c: ldur            x1, [fp, #-0x38]
    // 0xe76210: StoreField: r0->field_2b = r1
    //     0xe76210: stur            w1, [x0, #0x2b]
    // 0xe76214: ldur            x1, [fp, #-0x30]
    // 0xe76218: StoreField: r0->field_2f = r1
    //     0xe76218: stur            w1, [x0, #0x2f]
    // 0xe7621c: ldur            x1, [fp, #-0x28]
    // 0xe76220: StoreField: r0->field_33 = r1
    //     0xe76220: stur            w1, [x0, #0x33]
    // 0xe76224: ldur            x1, [fp, #-0x48]
    // 0xe76228: StoreField: r0->field_13 = r1
    //     0xe76228: stur            w1, [x0, #0x13]
    // 0xe7622c: ldur            x1, [fp, #-0x20]
    // 0xe76230: ArrayStore: r0[0] = r1  ; List_4
    //     0xe76230: stur            w1, [x0, #0x17]
    // 0xe76234: ldur            x1, [fp, #-0x18]
    // 0xe76238: StoreField: r0->field_1b = r1
    //     0xe76238: stur            w1, [x0, #0x1b]
    // 0xe7623c: ldur            x1, [fp, #-0x10]
    // 0xe76240: StoreField: r0->field_1f = r1
    //     0xe76240: stur            w1, [x0, #0x1f]
    // 0xe76244: ldur            x1, [fp, #-8]
    // 0xe76248: StoreField: r0->field_23 = r1
    //     0xe76248: stur            w1, [x0, #0x23]
    // 0xe7624c: r1 = false
    //     0xe7624c: add             x1, NULL, #0x30  ; false
    // 0xe76250: StoreField: r0->field_f = r1
    //     0xe76250: stur            w1, [x0, #0xf]
    // 0xe76254: LeaveFrame
    //     0xe76254: mov             SP, fp
    //     0xe76258: ldp             fp, lr, [SP], #0x10
    // 0xe7625c: ret
    //     0xe7625c: ret             
  }
  _ buildGradient(/* No info */) {
    // ** addr: 0xeaa158, size: 0x2b8
    // 0xeaa158: EnterFrame
    //     0xeaa158: stp             fp, lr, [SP, #-0x10]!
    //     0xeaa15c: mov             fp, SP
    // 0xeaa160: AllocStack(0x40)
    //     0xeaa160: sub             SP, SP, #0x40
    // 0xeaa164: SetupParameters(SvgLinearGradient this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r1 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0xeaa164: mov             x0, x2
    //     0xeaa168: stur            x2, [fp, #-0x10]
    //     0xeaa16c: mov             x2, x1
    //     0xeaa170: stur            x1, [fp, #-8]
    //     0xeaa174: mov             x1, x3
    //     0xeaa178: mov             x3, x5
    //     0xeaa17c: stur            x5, [fp, #-0x18]
    // 0xeaa180: CheckStackOverflow
    //     0xeaa180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa184: cmp             SP, x16
    //     0xeaa188: b.ls            #0xeaa3dc
    // 0xeaa18c: r0 = getTransform()
    //     0xeaa18c: bl              #0xe6daa4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::getTransform
    // 0xeaa190: mov             x3, x0
    // 0xeaa194: ldur            x2, [fp, #-8]
    // 0xeaa198: stur            x3, [fp, #-0x20]
    // 0xeaa19c: LoadField: r0 = r2->field_13
    //     0xeaa19c: ldur            w0, [x2, #0x13]
    // 0xeaa1a0: DecompressPointer r0
    //     0xeaa1a0: add             x0, x0, HEAP, lsl #32
    // 0xeaa1a4: r16 = Instance_GradientUnits
    //     0xeaa1a4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed30] Obj!GradientUnits@e2eb41
    //     0xeaa1a8: ldr             x16, [x16, #0xd30]
    // 0xeaa1ac: cmp             w0, w16
    // 0xeaa1b0: b.eq            #0xeaa250
    // 0xeaa1b4: ldur            x4, [fp, #-0x10]
    // 0xeaa1b8: r0 = LoadClassIdInstr(r4)
    //     0xeaa1b8: ldur            x0, [x4, #-1]
    //     0xeaa1bc: ubfx            x0, x0, #0xc, #0x14
    // 0xeaa1c0: mov             x1, x4
    // 0xeaa1c4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeaa1c4: sub             lr, x0, #1, lsl #12
    //     0xeaa1c8: ldr             lr, [x21, lr, lsl #3]
    //     0xeaa1cc: blr             lr
    // 0xeaa1d0: stur            x0, [fp, #-0x28]
    // 0xeaa1d4: LoadField: d0 = r0->field_7
    //     0xeaa1d4: ldur            d0, [x0, #7]
    // 0xeaa1d8: LoadField: d1 = r0->field_f
    //     0xeaa1d8: ldur            d1, [x0, #0xf]
    // 0xeaa1dc: ldur            x1, [fp, #-0x20]
    // 0xeaa1e0: r0 = translate()
    //     0xeaa1e0: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xeaa1e4: ldur            x0, [fp, #-0x28]
    // 0xeaa1e8: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xeaa1e8: ldur            d0, [x0, #0x17]
    // 0xeaa1ec: LoadField: d1 = r0->field_1f
    //     0xeaa1ec: ldur            d1, [x0, #0x1f]
    // 0xeaa1f0: r2 = inline_Allocate_Double()
    //     0xeaa1f0: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xeaa1f4: add             x2, x2, #0x10
    //     0xeaa1f8: cmp             x0, x2
    //     0xeaa1fc: b.ls            #0xeaa3e4
    //     0xeaa200: str             x2, [THR, #0x50]  ; THR::top
    //     0xeaa204: sub             x2, x2, #0xf
    //     0xeaa208: movz            x0, #0xe15c
    //     0xeaa20c: movk            x0, #0x3, lsl #16
    //     0xeaa210: stur            x0, [x2, #-1]
    // 0xeaa214: StoreField: r2->field_7 = d0
    //     0xeaa214: stur            d0, [x2, #7]
    // 0xeaa218: r0 = inline_Allocate_Double()
    //     0xeaa218: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xeaa21c: add             x0, x0, #0x10
    //     0xeaa220: cmp             x1, x0
    //     0xeaa224: b.ls            #0xeaa3f8
    //     0xeaa228: str             x0, [THR, #0x50]  ; THR::top
    //     0xeaa22c: sub             x0, x0, #0xf
    //     0xeaa230: movz            x1, #0xe15c
    //     0xeaa234: movk            x1, #0x3, lsl #16
    //     0xeaa238: stur            x1, [x0, #-1]
    // 0xeaa23c: StoreField: r0->field_7 = d1
    //     0xeaa23c: stur            d1, [x0, #7]
    // 0xeaa240: str             x0, [SP]
    // 0xeaa244: ldur            x1, [fp, #-0x20]
    // 0xeaa248: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeaa248: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeaa24c: r0 = scale()
    //     0xeaa24c: bl              #0x645258  ; [package:vector_math/vector_math_64.dart] Matrix4::scale
    // 0xeaa250: ldur            x0, [fp, #-8]
    // 0xeaa254: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeaa254: ldur            w1, [x0, #0x17]
    // 0xeaa258: DecompressPointer r1
    //     0xeaa258: add             x1, x1, HEAP, lsl #32
    // 0xeaa25c: LoadField: r2 = r1->field_7
    //     0xeaa25c: ldur            w2, [x1, #7]
    // 0xeaa260: DecompressPointer r2
    //     0xeaa260: add             x2, x2, HEAP, lsl #32
    // 0xeaa264: cmp             w2, NULL
    // 0xeaa268: b.eq            #0xeaa274
    // 0xeaa26c: ldur            x1, [fp, #-0x20]
    // 0xeaa270: r0 = multiply()
    //     0xeaa270: bl              #0x680524  ; [package:vector_math/vector_math_64.dart] Matrix4::multiply
    // 0xeaa274: ldur            x0, [fp, #-8]
    // 0xeaa278: ldur            x1, [fp, #-0x10]
    // 0xeaa27c: LoadField: r2 = r1->field_13
    //     0xeaa27c: ldur            w2, [x1, #0x13]
    // 0xeaa280: DecompressPointer r2
    //     0xeaa280: add             x2, x2, HEAP, lsl #32
    // 0xeaa284: LoadField: r4 = r2->field_f
    //     0xeaa284: ldur            w4, [x2, #0xf]
    // 0xeaa288: DecompressPointer r4
    //     0xeaa288: add             x4, x4, HEAP, lsl #32
    // 0xeaa28c: stur            x4, [fp, #-0x10]
    // 0xeaa290: LoadField: r5 = r0->field_1f
    //     0xeaa290: ldur            w5, [x0, #0x1f]
    // 0xeaa294: DecompressPointer r5
    //     0xeaa294: add             x5, x5, HEAP, lsl #32
    // 0xeaa298: mov             x2, x4
    // 0xeaa29c: ldur            x3, [fp, #-0x18]
    // 0xeaa2a0: r1 = <PdfDict<PdfDataType>>
    //     0xeaa2a0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeaa2a4: ldr             x1, [x1, #0x758]
    // 0xeaa2a8: r0 = PdfBaseFunction.colorsAndStops()
    //     0xeaa2a8: bl              #0xeaa694  ; [package:pdf/src/pdf/obj/function.dart] PdfBaseFunction::PdfBaseFunction.colorsAndStops
    // 0xeaa2ac: mov             x1, x0
    // 0xeaa2b0: ldur            x0, [fp, #-8]
    // 0xeaa2b4: stur            x1, [fp, #-0x18]
    // 0xeaa2b8: LoadField: r2 = r0->field_27
    //     0xeaa2b8: ldur            w2, [x0, #0x27]
    // 0xeaa2bc: DecompressPointer r2
    //     0xeaa2bc: add             x2, x2, HEAP, lsl #32
    // 0xeaa2c0: cmp             w2, NULL
    // 0xeaa2c4: b.ne            #0xeaa2d0
    // 0xeaa2c8: d0 = 0.000000
    //     0xeaa2c8: eor             v0.16b, v0.16b, v0.16b
    // 0xeaa2cc: b               #0xeaa2d4
    // 0xeaa2d0: LoadField: d0 = r2->field_7
    //     0xeaa2d0: ldur            d0, [x2, #7]
    // 0xeaa2d4: stur            d0, [fp, #-0x38]
    // 0xeaa2d8: LoadField: r2 = r0->field_2b
    //     0xeaa2d8: ldur            w2, [x0, #0x2b]
    // 0xeaa2dc: DecompressPointer r2
    //     0xeaa2dc: add             x2, x2, HEAP, lsl #32
    // 0xeaa2e0: cmp             w2, NULL
    // 0xeaa2e4: b.ne            #0xeaa2f0
    // 0xeaa2e8: d1 = 0.000000
    //     0xeaa2e8: eor             v1.16b, v1.16b, v1.16b
    // 0xeaa2ec: b               #0xeaa2f4
    // 0xeaa2f0: LoadField: d1 = r2->field_7
    //     0xeaa2f0: ldur            d1, [x2, #7]
    // 0xeaa2f4: stur            d1, [fp, #-0x30]
    // 0xeaa2f8: r0 = PdfPoint()
    //     0xeaa2f8: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xeaa2fc: ldur            d0, [fp, #-0x38]
    // 0xeaa300: stur            x0, [fp, #-0x28]
    // 0xeaa304: StoreField: r0->field_7 = d0
    //     0xeaa304: stur            d0, [x0, #7]
    // 0xeaa308: ldur            d0, [fp, #-0x30]
    // 0xeaa30c: StoreField: r0->field_f = d0
    //     0xeaa30c: stur            d0, [x0, #0xf]
    // 0xeaa310: ldur            x1, [fp, #-8]
    // 0xeaa314: LoadField: r2 = r1->field_2f
    //     0xeaa314: ldur            w2, [x1, #0x2f]
    // 0xeaa318: DecompressPointer r2
    //     0xeaa318: add             x2, x2, HEAP, lsl #32
    // 0xeaa31c: cmp             w2, NULL
    // 0xeaa320: b.ne            #0xeaa32c
    // 0xeaa324: d0 = 1.000000
    //     0xeaa324: fmov            d0, #1.00000000
    // 0xeaa328: b               #0xeaa330
    // 0xeaa32c: LoadField: d0 = r2->field_7
    //     0xeaa32c: ldur            d0, [x2, #7]
    // 0xeaa330: stur            d0, [fp, #-0x38]
    // 0xeaa334: LoadField: r2 = r1->field_33
    //     0xeaa334: ldur            w2, [x1, #0x33]
    // 0xeaa338: DecompressPointer r2
    //     0xeaa338: add             x2, x2, HEAP, lsl #32
    // 0xeaa33c: cmp             w2, NULL
    // 0xeaa340: b.ne            #0xeaa34c
    // 0xeaa344: d1 = 0.000000
    //     0xeaa344: eor             v1.16b, v1.16b, v1.16b
    // 0xeaa348: b               #0xeaa350
    // 0xeaa34c: LoadField: d1 = r2->field_7
    //     0xeaa34c: ldur            d1, [x2, #7]
    // 0xeaa350: stur            d1, [fp, #-0x30]
    // 0xeaa354: r0 = PdfPoint()
    //     0xeaa354: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xeaa358: ldur            d0, [fp, #-0x38]
    // 0xeaa35c: stur            x0, [fp, #-8]
    // 0xeaa360: StoreField: r0->field_7 = d0
    //     0xeaa360: stur            d0, [x0, #7]
    // 0xeaa364: ldur            d0, [fp, #-0x30]
    // 0xeaa368: StoreField: r0->field_f = d0
    //     0xeaa368: stur            d0, [x0, #0xf]
    // 0xeaa36c: r1 = <PdfDict<PdfDataType>>
    //     0xeaa36c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeaa370: ldr             x1, [x1, #0x758]
    // 0xeaa374: r0 = PdfShading()
    //     0xeaa374: bl              #0xeaa688  ; AllocatePdfShadingStub -> PdfShading (size=0x50)
    // 0xeaa378: mov             x1, x0
    // 0xeaa37c: ldur            x2, [fp, #-0x10]
    // 0xeaa380: ldur            x3, [fp, #-8]
    // 0xeaa384: ldur            x5, [fp, #-0x18]
    // 0xeaa388: ldur            x7, [fp, #-0x28]
    // 0xeaa38c: r6 = Instance_PdfShadingType
    //     0xeaa38c: add             x6, PP, #0x57, lsl #12  ; [pp+0x57a68] Obj!PdfShadingType@e2ebe1
    //     0xeaa390: ldr             x6, [x6, #0xa68]
    // 0xeaa394: stur            x0, [fp, #-8]
    // 0xeaa398: r4 = const [0, 0x6, 0, 0x6, null]
    //     0xeaa398: ldr             x4, [PP, #0x7508]  ; [pp+0x7508] List(5) [0, 0x6, 0, 0x6, Null]
    // 0xeaa39c: r0 = PdfShading()
    //     0xeaa39c: bl              #0xeaa4b4  ; [package:pdf/src/pdf/obj/shading.dart] PdfShading::PdfShading
    // 0xeaa3a0: r1 = <PdfDict<PdfDataType>>
    //     0xeaa3a0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xeaa3a4: ldr             x1, [x1, #0x758]
    // 0xeaa3a8: r0 = PdfShadingPattern()
    //     0xeaa3a8: bl              #0xeaa4a8  ; AllocatePdfShadingPatternStub -> PdfShadingPattern (size=0x40)
    // 0xeaa3ac: mov             x4, x0
    // 0xeaa3b0: ldur            x0, [fp, #-8]
    // 0xeaa3b4: stur            x4, [fp, #-0x18]
    // 0xeaa3b8: StoreField: r4->field_37 = r0
    //     0xeaa3b8: stur            w0, [x4, #0x37]
    // 0xeaa3bc: mov             x1, x4
    // 0xeaa3c0: ldur            x2, [fp, #-0x10]
    // 0xeaa3c4: ldur            x3, [fp, #-0x20]
    // 0xeaa3c8: r0 = PdfPattern()
    //     0xeaa3c8: bl              #0xeaa410  ; [package:pdf/src/pdf/obj/pattern.dart] PdfPattern::PdfPattern
    // 0xeaa3cc: ldur            x0, [fp, #-0x18]
    // 0xeaa3d0: LeaveFrame
    //     0xeaa3d0: mov             SP, fp
    //     0xeaa3d4: ldp             fp, lr, [SP], #0x10
    // 0xeaa3d8: ret
    //     0xeaa3d8: ret             
    // 0xeaa3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa3dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa3e0: b               #0xeaa18c
    // 0xeaa3e4: stp             q0, q1, [SP, #-0x20]!
    // 0xeaa3e8: r0 = AllocateDouble()
    //     0xeaa3e8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeaa3ec: mov             x2, x0
    // 0xeaa3f0: ldp             q0, q1, [SP], #0x20
    // 0xeaa3f4: b               #0xeaa214
    // 0xeaa3f8: SaveReg d1
    //     0xeaa3f8: str             q1, [SP, #-0x10]!
    // 0xeaa3fc: SaveReg r2
    //     0xeaa3fc: str             x2, [SP, #-8]!
    // 0xeaa400: r0 = AllocateDouble()
    //     0xeaa400: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeaa404: RestoreReg r2
    //     0xeaa404: ldr             x2, [SP], #8
    // 0xeaa408: RestoreReg d1
    //     0xeaa408: ldr             q1, [SP], #0x10
    // 0xeaa40c: b               #0xeaa23c
  }
}

// class id: 6807, size: 0x14, field offset: 0x14
enum GradientUnits extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4dcd0, size: 0x64
    // 0xc4dcd0: EnterFrame
    //     0xc4dcd0: stp             fp, lr, [SP, #-0x10]!
    //     0xc4dcd4: mov             fp, SP
    // 0xc4dcd8: AllocStack(0x10)
    //     0xc4dcd8: sub             SP, SP, #0x10
    // 0xc4dcdc: SetupParameters(GradientUnits this /* r1 => r0, fp-0x8 */)
    //     0xc4dcdc: mov             x0, x1
    //     0xc4dce0: stur            x1, [fp, #-8]
    // 0xc4dce4: CheckStackOverflow
    //     0xc4dce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4dce8: cmp             SP, x16
    //     0xc4dcec: b.ls            #0xc4dd2c
    // 0xc4dcf0: r1 = Null
    //     0xc4dcf0: mov             x1, NULL
    // 0xc4dcf4: r2 = 4
    //     0xc4dcf4: movz            x2, #0x4
    // 0xc4dcf8: r0 = AllocateArray()
    //     0xc4dcf8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4dcfc: r16 = "GradientUnits."
    //     0xc4dcfc: add             x16, PP, #0x46, lsl #12  ; [pp+0x46ed0] "GradientUnits."
    //     0xc4dd00: ldr             x16, [x16, #0xed0]
    // 0xc4dd04: StoreField: r0->field_f = r16
    //     0xc4dd04: stur            w16, [x0, #0xf]
    // 0xc4dd08: ldur            x1, [fp, #-8]
    // 0xc4dd0c: LoadField: r2 = r1->field_f
    //     0xc4dd0c: ldur            w2, [x1, #0xf]
    // 0xc4dd10: DecompressPointer r2
    //     0xc4dd10: add             x2, x2, HEAP, lsl #32
    // 0xc4dd14: StoreField: r0->field_13 = r2
    //     0xc4dd14: stur            w2, [x0, #0x13]
    // 0xc4dd18: str             x0, [SP]
    // 0xc4dd1c: r0 = _interpolate()
    //     0xc4dd1c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4dd20: LeaveFrame
    //     0xc4dd20: mov             SP, fp
    //     0xc4dd24: ldp             fp, lr, [SP], #0x10
    // 0xc4dd28: ret
    //     0xc4dd28: ret             
    // 0xc4dd2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4dd2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4dd30: b               #0xc4dcf0
  }
}
