// lib: , url: package:pdf/src/svg/group.dart

// class id: 1050831, size: 0x8
class :: {
}

// class id: 847, size: 0x1c, field offset: 0x18
class SvgGroup extends SvgOperation {

  _ paintShape(/* No info */) {
    // ** addr: 0xe47acc, size: 0x1f4
    // 0xe47acc: EnterFrame
    //     0xe47acc: stp             fp, lr, [SP, #-0x10]!
    //     0xe47ad0: mov             fp, SP
    // 0xe47ad4: AllocStack(0x20)
    //     0xe47ad4: sub             SP, SP, #0x20
    // 0xe47ad8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe47ad8: stur            x2, [fp, #-8]
    // 0xe47adc: CheckStackOverflow
    //     0xe47adc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe47ae0: cmp             SP, x16
    //     0xe47ae4: b.ls            #0xe47ca8
    // 0xe47ae8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xe47ae8: ldur            w0, [x1, #0x17]
    // 0xe47aec: DecompressPointer r0
    //     0xe47aec: add             x0, x0, HEAP, lsl #32
    // 0xe47af0: mov             x1, x0
    // 0xe47af4: r0 = iterator()
    //     0xe47af4: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0xe47af8: LoadField: r2 = r0->field_b
    //     0xe47af8: ldur            w2, [x0, #0xb]
    // 0xe47afc: DecompressPointer r2
    //     0xe47afc: add             x2, x2, HEAP, lsl #32
    // 0xe47b00: stur            x2, [fp, #-0x18]
    // 0xe47b04: LoadField: r3 = r0->field_7
    //     0xe47b04: ldur            w3, [x0, #7]
    // 0xe47b08: DecompressPointer r3
    //     0xe47b08: add             x3, x3, HEAP, lsl #32
    // 0xe47b0c: stur            x3, [fp, #-0x10]
    // 0xe47b10: CheckStackOverflow
    //     0xe47b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe47b14: cmp             SP, x16
    //     0xe47b18: b.ls            #0xe47cb0
    // 0xe47b1c: CheckStackOverflow
    //     0xe47b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe47b20: cmp             SP, x16
    //     0xe47b24: b.ls            #0xe47cb8
    // 0xe47b28: r0 = LoadClassIdInstr(r2)
    //     0xe47b28: ldur            x0, [x2, #-1]
    //     0xe47b2c: ubfx            x0, x0, #0xc, #0x14
    // 0xe47b30: mov             x1, x2
    // 0xe47b34: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe47b34: movz            x17, #0x292d
    //     0xe47b38: movk            x17, #0x1, lsl #16
    //     0xe47b3c: add             lr, x0, x17
    //     0xe47b40: ldr             lr, [x21, lr, lsl #3]
    //     0xe47b44: blr             lr
    // 0xe47b48: tbnz            w0, #4, #0xe47c98
    // 0xe47b4c: ldur            x2, [fp, #-0x18]
    // 0xe47b50: r0 = LoadClassIdInstr(r2)
    //     0xe47b50: ldur            x0, [x2, #-1]
    //     0xe47b54: ubfx            x0, x0, #0xc, #0x14
    // 0xe47b58: mov             x1, x2
    // 0xe47b5c: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe47b5c: movz            x17, #0x384d
    //     0xe47b60: movk            x17, #0x1, lsl #16
    //     0xe47b64: add             lr, x0, x17
    //     0xe47b68: ldr             lr, [x21, lr, lsl #3]
    //     0xe47b6c: blr             lr
    // 0xe47b70: ldur            x2, [fp, #-0x10]
    // 0xe47b74: r1 = Null
    //     0xe47b74: mov             x1, NULL
    // 0xe47b78: cmp             w2, NULL
    // 0xe47b7c: b.eq            #0xe47c14
    // 0xe47b80: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe47b80: ldur            w3, [x2, #0x17]
    // 0xe47b84: DecompressPointer r3
    //     0xe47b84: add             x3, x3, HEAP, lsl #32
    // 0xe47b88: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xe47b8c: cmp             w3, w16
    // 0xe47b90: b.eq            #0xe47c14
    // 0xe47b94: r16 = Object?
    //     0xe47b94: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xe47b98: cmp             w3, w16
    // 0xe47b9c: b.eq            #0xe47c14
    // 0xe47ba0: r16 = void?
    //     0xe47ba0: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xe47ba4: cmp             w3, w16
    // 0xe47ba8: b.eq            #0xe47c14
    // 0xe47bac: tbnz            w0, #0, #0xe47bc8
    // 0xe47bb0: r16 = int
    //     0xe47bb0: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe47bb4: cmp             w3, w16
    // 0xe47bb8: b.eq            #0xe47c14
    // 0xe47bbc: r16 = num
    //     0xe47bbc: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xe47bc0: cmp             w3, w16
    // 0xe47bc4: b.eq            #0xe47c14
    // 0xe47bc8: r3 = SubtypeTestCache
    //     0xe47bc8: add             x3, PP, #0x46, lsl #12  ; [pp+0x46e50] SubtypeTestCache
    //     0xe47bcc: ldr             x3, [x3, #0xe50]
    // 0xe47bd0: r30 = Subtype6TestCacheStub
    //     0xe47bd0: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27cc)
    // 0xe47bd4: LoadField: r30 = r30->field_7
    //     0xe47bd4: ldur            lr, [lr, #7]
    // 0xe47bd8: blr             lr
    // 0xe47bdc: cmp             w7, NULL
    // 0xe47be0: b.eq            #0xe47bec
    // 0xe47be4: tbnz            w7, #4, #0xe47c0c
    // 0xe47be8: b               #0xe47c14
    // 0xe47bec: r8 = X0
    //     0xe47bec: add             x8, PP, #0x46, lsl #12  ; [pp+0x46e58] TypeParameter: X0
    //     0xe47bf0: ldr             x8, [x8, #0xe58]
    // 0xe47bf4: r3 = SubtypeTestCache
    //     0xe47bf4: add             x3, PP, #0x46, lsl #12  ; [pp+0x46e60] SubtypeTestCache
    //     0xe47bf8: ldr             x3, [x3, #0xe60]
    // 0xe47bfc: r30 = InstanceOfStub
    //     0xe47bfc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe47c00: LoadField: r30 = r30->field_7
    //     0xe47c00: ldur            lr, [lr, #7]
    // 0xe47c04: blr             lr
    // 0xe47c08: b               #0xe47c18
    // 0xe47c0c: r0 = false
    //     0xe47c0c: add             x0, NULL, #0x30  ; false
    // 0xe47c10: b               #0xe47c18
    // 0xe47c14: r0 = true
    //     0xe47c14: add             x0, NULL, #0x20  ; true
    // 0xe47c18: tbz             w0, #4, #0xe47c28
    // 0xe47c1c: ldur            x2, [fp, #-0x18]
    // 0xe47c20: ldur            x3, [fp, #-0x10]
    // 0xe47c24: b               #0xe47b1c
    // 0xe47c28: ldur            x2, [fp, #-0x18]
    // 0xe47c2c: r0 = LoadClassIdInstr(r2)
    //     0xe47c2c: ldur            x0, [x2, #-1]
    //     0xe47c30: ubfx            x0, x0, #0xc, #0x14
    // 0xe47c34: mov             x1, x2
    // 0xe47c38: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe47c38: movz            x17, #0x384d
    //     0xe47c3c: movk            x17, #0x1, lsl #16
    //     0xe47c40: add             lr, x0, x17
    //     0xe47c44: ldr             lr, [x21, lr, lsl #3]
    //     0xe47c48: blr             lr
    // 0xe47c4c: ldur            x2, [fp, #-0x10]
    // 0xe47c50: mov             x3, x0
    // 0xe47c54: r1 = Null
    //     0xe47c54: mov             x1, NULL
    // 0xe47c58: stur            x3, [fp, #-0x20]
    // 0xe47c5c: cmp             w2, NULL
    // 0xe47c60: b.eq            #0xe47c80
    // 0xe47c64: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe47c64: ldur            w4, [x2, #0x17]
    // 0xe47c68: DecompressPointer r4
    //     0xe47c68: add             x4, x4, HEAP, lsl #32
    // 0xe47c6c: r8 = X0
    //     0xe47c6c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe47c70: LoadField: r9 = r4->field_7
    //     0xe47c70: ldur            x9, [x4, #7]
    // 0xe47c74: r3 = Null
    //     0xe47c74: add             x3, PP, #0x46, lsl #12  ; [pp+0x46e68] Null
    //     0xe47c78: ldr             x3, [x3, #0xe68]
    // 0xe47c7c: blr             x9
    // 0xe47c80: ldur            x1, [fp, #-0x20]
    // 0xe47c84: ldur            x2, [fp, #-8]
    // 0xe47c88: r0 = paint()
    //     0xe47c88: bl              #0xe46704  ; [package:pdf/src/svg/operation.dart] SvgOperation::paint
    // 0xe47c8c: ldur            x2, [fp, #-0x18]
    // 0xe47c90: ldur            x3, [fp, #-0x10]
    // 0xe47c94: b               #0xe47b10
    // 0xe47c98: r0 = Null
    //     0xe47c98: mov             x0, NULL
    // 0xe47c9c: LeaveFrame
    //     0xe47c9c: mov             SP, fp
    //     0xe47ca0: ldp             fp, lr, [SP], #0x10
    // 0xe47ca4: ret
    //     0xe47ca4: ret             
    // 0xe47ca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47ca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47cac: b               #0xe47ae8
    // 0xe47cb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47cb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47cb4: b               #0xe47b1c
    // 0xe47cb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47cb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47cbc: b               #0xe47b28
  }
  _ drawShape(/* No info */) {
    // ** addr: 0xe53e74, size: 0x23c
    // 0xe53e74: EnterFrame
    //     0xe53e74: stp             fp, lr, [SP, #-0x10]!
    //     0xe53e78: mov             fp, SP
    // 0xe53e7c: AllocStack(0x20)
    //     0xe53e7c: sub             SP, SP, #0x20
    // 0xe53e80: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xe53e80: mov             x0, x2
    //     0xe53e84: stur            x2, [fp, #-8]
    // 0xe53e88: CheckStackOverflow
    //     0xe53e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe53e8c: cmp             SP, x16
    //     0xe53e90: b.ls            #0xe54098
    // 0xe53e94: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xe53e94: ldur            w2, [x1, #0x17]
    // 0xe53e98: DecompressPointer r2
    //     0xe53e98: add             x2, x2, HEAP, lsl #32
    // 0xe53e9c: mov             x1, x2
    // 0xe53ea0: r0 = iterator()
    //     0xe53ea0: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0xe53ea4: LoadField: r2 = r0->field_b
    //     0xe53ea4: ldur            w2, [x0, #0xb]
    // 0xe53ea8: DecompressPointer r2
    //     0xe53ea8: add             x2, x2, HEAP, lsl #32
    // 0xe53eac: stur            x2, [fp, #-0x18]
    // 0xe53eb0: LoadField: r3 = r0->field_7
    //     0xe53eb0: ldur            w3, [x0, #7]
    // 0xe53eb4: DecompressPointer r3
    //     0xe53eb4: add             x3, x3, HEAP, lsl #32
    // 0xe53eb8: stur            x3, [fp, #-0x10]
    // 0xe53ebc: CheckStackOverflow
    //     0xe53ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe53ec0: cmp             SP, x16
    //     0xe53ec4: b.ls            #0xe540a0
    // 0xe53ec8: CheckStackOverflow
    //     0xe53ec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe53ecc: cmp             SP, x16
    //     0xe53ed0: b.ls            #0xe540a8
    // 0xe53ed4: r0 = LoadClassIdInstr(r2)
    //     0xe53ed4: ldur            x0, [x2, #-1]
    //     0xe53ed8: ubfx            x0, x0, #0xc, #0x14
    // 0xe53edc: mov             x1, x2
    // 0xe53ee0: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe53ee0: movz            x17, #0x292d
    //     0xe53ee4: movk            x17, #0x1, lsl #16
    //     0xe53ee8: add             lr, x0, x17
    //     0xe53eec: ldr             lr, [x21, lr, lsl #3]
    //     0xe53ef0: blr             lr
    // 0xe53ef4: tbnz            w0, #4, #0xe54088
    // 0xe53ef8: ldur            x2, [fp, #-0x18]
    // 0xe53efc: r0 = LoadClassIdInstr(r2)
    //     0xe53efc: ldur            x0, [x2, #-1]
    //     0xe53f00: ubfx            x0, x0, #0xc, #0x14
    // 0xe53f04: mov             x1, x2
    // 0xe53f08: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe53f08: movz            x17, #0x384d
    //     0xe53f0c: movk            x17, #0x1, lsl #16
    //     0xe53f10: add             lr, x0, x17
    //     0xe53f14: ldr             lr, [x21, lr, lsl #3]
    //     0xe53f18: blr             lr
    // 0xe53f1c: ldur            x2, [fp, #-0x10]
    // 0xe53f20: r1 = Null
    //     0xe53f20: mov             x1, NULL
    // 0xe53f24: cmp             w2, NULL
    // 0xe53f28: b.eq            #0xe53fc0
    // 0xe53f2c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe53f2c: ldur            w3, [x2, #0x17]
    // 0xe53f30: DecompressPointer r3
    //     0xe53f30: add             x3, x3, HEAP, lsl #32
    // 0xe53f34: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xe53f38: cmp             w3, w16
    // 0xe53f3c: b.eq            #0xe53fc0
    // 0xe53f40: r16 = Object?
    //     0xe53f40: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xe53f44: cmp             w3, w16
    // 0xe53f48: b.eq            #0xe53fc0
    // 0xe53f4c: r16 = void?
    //     0xe53f4c: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xe53f50: cmp             w3, w16
    // 0xe53f54: b.eq            #0xe53fc0
    // 0xe53f58: tbnz            w0, #0, #0xe53f74
    // 0xe53f5c: r16 = int
    //     0xe53f5c: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe53f60: cmp             w3, w16
    // 0xe53f64: b.eq            #0xe53fc0
    // 0xe53f68: r16 = num
    //     0xe53f68: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xe53f6c: cmp             w3, w16
    // 0xe53f70: b.eq            #0xe53fc0
    // 0xe53f74: r3 = SubtypeTestCache
    //     0xe53f74: add             x3, PP, #0x46, lsl #12  ; [pp+0x46e28] SubtypeTestCache
    //     0xe53f78: ldr             x3, [x3, #0xe28]
    // 0xe53f7c: r30 = Subtype6TestCacheStub
    //     0xe53f7c: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27cc)
    // 0xe53f80: LoadField: r30 = r30->field_7
    //     0xe53f80: ldur            lr, [lr, #7]
    // 0xe53f84: blr             lr
    // 0xe53f88: cmp             w7, NULL
    // 0xe53f8c: b.eq            #0xe53f98
    // 0xe53f90: tbnz            w7, #4, #0xe53fb8
    // 0xe53f94: b               #0xe53fc0
    // 0xe53f98: r8 = X0
    //     0xe53f98: add             x8, PP, #0x46, lsl #12  ; [pp+0x46e30] TypeParameter: X0
    //     0xe53f9c: ldr             x8, [x8, #0xe30]
    // 0xe53fa0: r3 = SubtypeTestCache
    //     0xe53fa0: add             x3, PP, #0x46, lsl #12  ; [pp+0x46e38] SubtypeTestCache
    //     0xe53fa4: ldr             x3, [x3, #0xe38]
    // 0xe53fa8: r30 = InstanceOfStub
    //     0xe53fa8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe53fac: LoadField: r30 = r30->field_7
    //     0xe53fac: ldur            lr, [lr, #7]
    // 0xe53fb0: blr             lr
    // 0xe53fb4: b               #0xe53fc4
    // 0xe53fb8: r0 = false
    //     0xe53fb8: add             x0, NULL, #0x30  ; false
    // 0xe53fbc: b               #0xe53fc4
    // 0xe53fc0: r0 = true
    //     0xe53fc0: add             x0, NULL, #0x20  ; true
    // 0xe53fc4: tbz             w0, #4, #0xe53fd4
    // 0xe53fc8: ldur            x2, [fp, #-0x18]
    // 0xe53fcc: ldur            x3, [fp, #-0x10]
    // 0xe53fd0: b               #0xe53ec8
    // 0xe53fd4: ldur            x2, [fp, #-0x18]
    // 0xe53fd8: r0 = LoadClassIdInstr(r2)
    //     0xe53fd8: ldur            x0, [x2, #-1]
    //     0xe53fdc: ubfx            x0, x0, #0xc, #0x14
    // 0xe53fe0: mov             x1, x2
    // 0xe53fe4: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe53fe4: movz            x17, #0x384d
    //     0xe53fe8: movk            x17, #0x1, lsl #16
    //     0xe53fec: add             lr, x0, x17
    //     0xe53ff0: ldr             lr, [x21, lr, lsl #3]
    //     0xe53ff4: blr             lr
    // 0xe53ff8: ldur            x2, [fp, #-0x10]
    // 0xe53ffc: mov             x3, x0
    // 0xe54000: r1 = Null
    //     0xe54000: mov             x1, NULL
    // 0xe54004: stur            x3, [fp, #-0x20]
    // 0xe54008: cmp             w2, NULL
    // 0xe5400c: b.eq            #0xe5402c
    // 0xe54010: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe54010: ldur            w4, [x2, #0x17]
    // 0xe54014: DecompressPointer r4
    //     0xe54014: add             x4, x4, HEAP, lsl #32
    // 0xe54018: r8 = X0
    //     0xe54018: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe5401c: LoadField: r9 = r4->field_7
    //     0xe5401c: ldur            x9, [x4, #7]
    // 0xe54020: r3 = Null
    //     0xe54020: add             x3, PP, #0x46, lsl #12  ; [pp+0x46e40] Null
    //     0xe54024: ldr             x3, [x3, #0xe40]
    // 0xe54028: blr             x9
    // 0xe5402c: ldur            x1, [fp, #-8]
    // 0xe54030: r0 = saveContext()
    //     0xe54030: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe54034: ldur            x0, [fp, #-0x20]
    // 0xe54038: LoadField: r1 = r0->field_f
    //     0xe54038: ldur            w1, [x0, #0xf]
    // 0xe5403c: DecompressPointer r1
    //     0xe5403c: add             x1, x1, HEAP, lsl #32
    // 0xe54040: LoadField: r2 = r1->field_7
    //     0xe54040: ldur            w2, [x1, #7]
    // 0xe54044: DecompressPointer r2
    //     0xe54044: add             x2, x2, HEAP, lsl #32
    // 0xe54048: cmp             w2, NULL
    // 0xe5404c: b.eq            #0xe54058
    // 0xe54050: ldur            x1, [fp, #-8]
    // 0xe54054: r0 = setTransform()
    //     0xe54054: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe54058: ldur            x1, [fp, #-0x20]
    // 0xe5405c: r0 = LoadClassIdInstr(r1)
    //     0xe5405c: ldur            x0, [x1, #-1]
    //     0xe54060: ubfx            x0, x0, #0xc, #0x14
    // 0xe54064: ldur            x2, [fp, #-8]
    // 0xe54068: r0 = GDT[cid_x0 + -0xded]()
    //     0xe54068: sub             lr, x0, #0xded
    //     0xe5406c: ldr             lr, [x21, lr, lsl #3]
    //     0xe54070: blr             lr
    // 0xe54074: ldur            x1, [fp, #-8]
    // 0xe54078: r0 = restoreContext()
    //     0xe54078: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe5407c: ldur            x2, [fp, #-0x18]
    // 0xe54080: ldur            x3, [fp, #-0x10]
    // 0xe54084: b               #0xe53ebc
    // 0xe54088: r0 = Null
    //     0xe54088: mov             x0, NULL
    // 0xe5408c: LeaveFrame
    //     0xe5408c: mov             SP, fp
    //     0xe54090: ldp             fp, lr, [SP], #0x10
    // 0xe54094: ret
    //     0xe54094: ret             
    // 0xe54098: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe54098: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5409c: b               #0xe53e94
    // 0xe540a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe540a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe540a4: b               #0xe53ec8
    // 0xe540a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe540a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe540ac: b               #0xe53ed4
  }
  factory _ SvgGroup.fromXml(/* No info */) {
    // ** addr: 0xe6dfc0, size: 0x18c
    // 0xe6dfc0: EnterFrame
    //     0xe6dfc0: stp             fp, lr, [SP, #-0x10]!
    //     0xe6dfc4: mov             fp, SP
    // 0xe6dfc8: AllocStack(0x48)
    //     0xe6dfc8: sub             SP, SP, #0x48
    // 0xe6dfcc: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r5, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0xe6dfcc: stur            x3, [fp, #-0x10]
    //     0xe6dfd0: mov             x16, x5
    //     0xe6dfd4: mov             x5, x3
    //     0xe6dfd8: mov             x3, x16
    //     0xe6dfdc: stur            x2, [fp, #-8]
    //     0xe6dfe0: stur            x3, [fp, #-0x18]
    // 0xe6dfe4: CheckStackOverflow
    //     0xe6dfe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6dfe8: cmp             SP, x16
    //     0xe6dfec: b.ls            #0xe6e144
    // 0xe6dff0: r1 = 2
    //     0xe6dff0: movz            x1, #0x2
    // 0xe6dff4: r0 = AllocateContext()
    //     0xe6dff4: bl              #0xec126c  ; AllocateContextStub
    // 0xe6dff8: ldur            x5, [fp, #-0x10]
    // 0xe6dffc: stur            x0, [fp, #-0x20]
    // 0xe6e000: StoreField: r0->field_f = r5
    //     0xe6e000: stur            w5, [x0, #0xf]
    // 0xe6e004: ldur            x2, [fp, #-8]
    // 0xe6e008: ldur            x3, [fp, #-0x18]
    // 0xe6e00c: r1 = Null
    //     0xe6e00c: mov             x1, NULL
    // 0xe6e010: r0 = SvgBrush.fromXml()
    //     0xe6e010: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe6e014: mov             x1, x0
    // 0xe6e018: ldur            x2, [fp, #-0x20]
    // 0xe6e01c: stur            x1, [fp, #-0x10]
    // 0xe6e020: StoreField: r2->field_13 = r0
    //     0xe6e020: stur            w0, [x2, #0x13]
    //     0xe6e024: ldurb           w16, [x2, #-1]
    //     0xe6e028: ldurb           w17, [x0, #-1]
    //     0xe6e02c: and             x16, x17, x16, lsr #2
    //     0xe6e030: tst             x16, HEAP, lsr #32
    //     0xe6e034: b.eq            #0xe6e03c
    //     0xe6e038: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe6e03c: ldur            x0, [fp, #-8]
    // 0xe6e040: LoadField: r3 = r0->field_f
    //     0xe6e040: ldur            w3, [x0, #0xf]
    // 0xe6e044: DecompressPointer r3
    //     0xe6e044: add             x3, x3, HEAP, lsl #32
    // 0xe6e048: r16 = <XmlElement>
    //     0xe6e048: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea98] TypeArguments: <XmlElement>
    //     0xe6e04c: ldr             x16, [x16, #0xa98]
    // 0xe6e050: stp             x3, x16, [SP]
    // 0xe6e054: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe6e054: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe6e058: r0 = whereType()
    //     0xe6e058: bl              #0x7b5364  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::whereType
    // 0xe6e05c: r1 = Function '<anonymous closure>': static.
    //     0xe6e05c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eaa0] AnonymousClosure: static (0xe76e20), in [package:pdf/src/svg/group.dart] SvgGroup::SvgGroup.fromXml (0xe6dfc0)
    //     0xe6e060: ldr             x1, [x1, #0xaa0]
    // 0xe6e064: r2 = Null
    //     0xe6e064: mov             x2, NULL
    // 0xe6e068: stur            x0, [fp, #-0x18]
    // 0xe6e06c: r0 = AllocateClosure()
    //     0xe6e06c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe6e070: ldur            x1, [fp, #-0x18]
    // 0xe6e074: mov             x2, x0
    // 0xe6e078: r0 = where()
    //     0xe6e078: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xe6e07c: ldur            x2, [fp, #-0x20]
    // 0xe6e080: r1 = Function '<anonymous closure>': static.
    //     0xe6e080: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eaa8] AnonymousClosure: static (0xe6f2bc), in [package:pdf/src/svg/group.dart] SvgGroup::SvgGroup.fromXml (0xe6dfc0)
    //     0xe6e084: ldr             x1, [x1, #0xaa8]
    // 0xe6e088: stur            x0, [fp, #-0x18]
    // 0xe6e08c: r0 = AllocateClosure()
    //     0xe6e08c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe6e090: r16 = <SvgOperation?>
    //     0xe6e090: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eab0] TypeArguments: <SvgOperation?>
    //     0xe6e094: ldr             x16, [x16, #0xab0]
    // 0xe6e098: ldur            lr, [fp, #-0x18]
    // 0xe6e09c: stp             lr, x16, [SP, #8]
    // 0xe6e0a0: str             x0, [SP]
    // 0xe6e0a4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe6e0a4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe6e0a8: r0 = map()
    //     0xe6e0a8: bl              #0x7abfa0  ; [dart:_internal] WhereIterable::map
    // 0xe6e0ac: r16 = <SvgOperation>
    //     0xe6e0ac: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eab8] TypeArguments: <SvgOperation>
    //     0xe6e0b0: ldr             x16, [x16, #0xab8]
    // 0xe6e0b4: stp             x0, x16, [SP]
    // 0xe6e0b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe6e0b8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe6e0bc: r0 = whereType()
    //     0xe6e0bc: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0xe6e0c0: mov             x4, x0
    // 0xe6e0c4: ldur            x0, [fp, #-0x20]
    // 0xe6e0c8: stur            x4, [fp, #-0x18]
    // 0xe6e0cc: LoadField: r3 = r0->field_f
    //     0xe6e0cc: ldur            w3, [x0, #0xf]
    // 0xe6e0d0: DecompressPointer r3
    //     0xe6e0d0: add             x3, x3, HEAP, lsl #32
    // 0xe6e0d4: ldur            x2, [fp, #-8]
    // 0xe6e0d8: ldur            x5, [fp, #-0x10]
    // 0xe6e0dc: r1 = Null
    //     0xe6e0dc: mov             x1, NULL
    // 0xe6e0e0: r0 = SvgClipPath.fromXml()
    //     0xe6e0e0: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe6e0e4: ldur            x2, [fp, #-8]
    // 0xe6e0e8: r1 = Null
    //     0xe6e0e8: mov             x1, NULL
    // 0xe6e0ec: stur            x0, [fp, #-8]
    // 0xe6e0f0: r0 = SvgTransform.fromXml()
    //     0xe6e0f0: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe6e0f4: mov             x1, x0
    // 0xe6e0f8: ldur            x0, [fp, #-0x20]
    // 0xe6e0fc: stur            x1, [fp, #-0x30]
    // 0xe6e100: LoadField: r2 = r0->field_f
    //     0xe6e100: ldur            w2, [x0, #0xf]
    // 0xe6e104: DecompressPointer r2
    //     0xe6e104: add             x2, x2, HEAP, lsl #32
    // 0xe6e108: stur            x2, [fp, #-0x28]
    // 0xe6e10c: r0 = SvgGroup()
    //     0xe6e10c: bl              #0xe6e14c  ; AllocateSvgGroupStub -> SvgGroup (size=0x1c)
    // 0xe6e110: ldur            x1, [fp, #-0x18]
    // 0xe6e114: ArrayStore: r0[0] = r1  ; List_4
    //     0xe6e114: stur            w1, [x0, #0x17]
    // 0xe6e118: ldur            x1, [fp, #-0x10]
    // 0xe6e11c: StoreField: r0->field_7 = r1
    //     0xe6e11c: stur            w1, [x0, #7]
    // 0xe6e120: ldur            x1, [fp, #-8]
    // 0xe6e124: StoreField: r0->field_b = r1
    //     0xe6e124: stur            w1, [x0, #0xb]
    // 0xe6e128: ldur            x1, [fp, #-0x30]
    // 0xe6e12c: StoreField: r0->field_f = r1
    //     0xe6e12c: stur            w1, [x0, #0xf]
    // 0xe6e130: ldur            x1, [fp, #-0x28]
    // 0xe6e134: StoreField: r0->field_13 = r1
    //     0xe6e134: stur            w1, [x0, #0x13]
    // 0xe6e138: LeaveFrame
    //     0xe6e138: mov             SP, fp
    //     0xe6e13c: ldp             fp, lr, [SP], #0x10
    // 0xe6e140: ret
    //     0xe6e140: ret             
    // 0xe6e144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6e144: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6e148: b               #0xe6dff0
  }
  [closure] static SvgOperation? <anonymous closure>(dynamic, XmlElement) {
    // ** addr: 0xe6f2bc, size: 0x4c
    // 0xe6f2bc: EnterFrame
    //     0xe6f2bc: stp             fp, lr, [SP, #-0x10]!
    //     0xe6f2c0: mov             fp, SP
    // 0xe6f2c4: ldr             x0, [fp, #0x18]
    // 0xe6f2c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe6f2c8: ldur            w1, [x0, #0x17]
    // 0xe6f2cc: DecompressPointer r1
    //     0xe6f2cc: add             x1, x1, HEAP, lsl #32
    // 0xe6f2d0: CheckStackOverflow
    //     0xe6f2d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6f2d4: cmp             SP, x16
    //     0xe6f2d8: b.ls            #0xe6f300
    // 0xe6f2dc: LoadField: r2 = r1->field_f
    //     0xe6f2dc: ldur            w2, [x1, #0xf]
    // 0xe6f2e0: DecompressPointer r2
    //     0xe6f2e0: add             x2, x2, HEAP, lsl #32
    // 0xe6f2e4: LoadField: r3 = r1->field_13
    //     0xe6f2e4: ldur            w3, [x1, #0x13]
    // 0xe6f2e8: DecompressPointer r3
    //     0xe6f2e8: add             x3, x3, HEAP, lsl #32
    // 0xe6f2ec: ldr             x1, [fp, #0x10]
    // 0xe6f2f0: r0 = fromXml()
    //     0xe6f2f0: bl              #0xe6f308  ; [package:pdf/src/svg/operation.dart] SvgOperation::fromXml
    // 0xe6f2f4: LeaveFrame
    //     0xe6f2f4: mov             SP, fp
    //     0xe6f2f8: ldp             fp, lr, [SP], #0x10
    // 0xe6f2fc: ret
    //     0xe6f2fc: ret             
    // 0xe6f300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6f300: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6f304: b               #0xe6f2dc
  }
  [closure] static bool <anonymous closure>(dynamic, XmlElement) {
    // ** addr: 0xe76e20, size: 0x88
    // 0xe76e20: EnterFrame
    //     0xe76e20: stp             fp, lr, [SP, #-0x10]!
    //     0xe76e24: mov             fp, SP
    // 0xe76e28: AllocStack(0x10)
    //     0xe76e28: sub             SP, SP, #0x10
    // 0xe76e2c: CheckStackOverflow
    //     0xe76e2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe76e30: cmp             SP, x16
    //     0xe76e34: b.ls            #0xe76ea0
    // 0xe76e38: ldr             x0, [fp, #0x10]
    // 0xe76e3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe76e3c: ldur            w1, [x0, #0x17]
    // 0xe76e40: DecompressPointer r1
    //     0xe76e40: add             x1, x1, HEAP, lsl #32
    // 0xe76e44: r0 = LoadClassIdInstr(r1)
    //     0xe76e44: ldur            x0, [x1, #-1]
    //     0xe76e48: ubfx            x0, x0, #0xc, #0x14
    // 0xe76e4c: cmp             x0, #0xe2
    // 0xe76e50: b.ne            #0xe76e60
    // 0xe76e54: LoadField: r0 = r1->field_b
    //     0xe76e54: ldur            w0, [x1, #0xb]
    // 0xe76e58: DecompressPointer r0
    //     0xe76e58: add             x0, x0, HEAP, lsl #32
    // 0xe76e5c: b               #0xe76e68
    // 0xe76e60: LoadField: r0 = r1->field_f
    //     0xe76e60: ldur            w0, [x1, #0xf]
    // 0xe76e64: DecompressPointer r0
    //     0xe76e64: add             x0, x0, HEAP, lsl #32
    // 0xe76e68: r1 = LoadClassIdInstr(r0)
    //     0xe76e68: ldur            x1, [x0, #-1]
    //     0xe76e6c: ubfx            x1, x1, #0xc, #0x14
    // 0xe76e70: r16 = "symbol"
    //     0xe76e70: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eae8] "symbol"
    //     0xe76e74: ldr             x16, [x16, #0xae8]
    // 0xe76e78: stp             x16, x0, [SP]
    // 0xe76e7c: mov             x0, x1
    // 0xe76e80: mov             lr, x0
    // 0xe76e84: ldr             lr, [x21, lr, lsl #3]
    // 0xe76e88: blr             lr
    // 0xe76e8c: eor             x1, x0, #0x10
    // 0xe76e90: mov             x0, x1
    // 0xe76e94: LeaveFrame
    //     0xe76e94: mov             SP, fp
    //     0xe76e98: ldp             fp, lr, [SP], #0x10
    // 0xe76e9c: ret
    //     0xe76e9c: ret             
    // 0xe76ea0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe76ea0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76ea4: b               #0xe76e38
  }
}
