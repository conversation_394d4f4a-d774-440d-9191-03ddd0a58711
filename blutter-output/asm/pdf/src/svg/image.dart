// lib: , url: package:pdf/src/svg/image.dart

// class id: 1050832, size: 0x8
class :: {
}

// class id: 846, size: 0x3c, field offset: 0x18
class SvgImg extends SvgOperation {

  _ paintShape(/* No info */) {
    // ** addr: 0xe47cc0, size: 0x1bc
    // 0xe47cc0: EnterFrame
    //     0xe47cc0: stp             fp, lr, [SP, #-0x10]!
    //     0xe47cc4: mov             fp, SP
    // 0xe47cc8: AllocStack(0x40)
    //     0xe47cc8: sub             SP, SP, #0x40
    // 0xe47ccc: SetupParameters(SvgImg this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xe47ccc: mov             x0, x1
    //     0xe47cd0: stur            x1, [fp, #-0x10]
    //     0xe47cd4: mov             x1, x2
    //     0xe47cd8: stur            x2, [fp, #-0x18]
    // 0xe47cdc: CheckStackOverflow
    //     0xe47cdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe47ce0: cmp             SP, x16
    //     0xe47ce4: b.ls            #0xe47e48
    // 0xe47ce8: LoadField: r2 = r0->field_37
    //     0xe47ce8: ldur            w2, [x0, #0x37]
    // 0xe47cec: DecompressPointer r2
    //     0xe47cec: add             x2, x2, HEAP, lsl #32
    // 0xe47cf0: stur            x2, [fp, #-8]
    // 0xe47cf4: cmp             w2, NULL
    // 0xe47cf8: b.ne            #0xe47d0c
    // 0xe47cfc: r0 = Null
    //     0xe47cfc: mov             x0, NULL
    // 0xe47d00: LeaveFrame
    //     0xe47d00: mov             SP, fp
    //     0xe47d04: ldp             fp, lr, [SP], #0x10
    // 0xe47d08: ret
    //     0xe47d08: ret             
    // 0xe47d0c: LoadField: d0 = r0->field_27
    //     0xe47d0c: ldur            d0, [x0, #0x27]
    // 0xe47d10: LoadField: r3 = r2->field_43
    //     0xe47d10: ldur            w3, [x2, #0x43]
    // 0xe47d14: DecompressPointer r3
    //     0xe47d14: add             x3, x3, HEAP, lsl #32
    // 0xe47d18: LoadField: r4 = r3->field_7
    //     0xe47d18: ldur            x4, [x3, #7]
    // 0xe47d1c: cmp             x4, #4
    // 0xe47d20: b.lt            #0xe47d2c
    // 0xe47d24: LoadField: r3 = r2->field_3b
    //     0xe47d24: ldur            x3, [x2, #0x3b]
    // 0xe47d28: b               #0xe47d30
    // 0xe47d2c: LoadField: r3 = r2->field_33
    //     0xe47d2c: ldur            x3, [x2, #0x33]
    // 0xe47d30: scvtf           d1, x3
    // 0xe47d34: fdiv            d2, d0, d1
    // 0xe47d38: stur            d2, [fp, #-0x38]
    // 0xe47d3c: LoadField: d0 = r0->field_2f
    //     0xe47d3c: ldur            d0, [x0, #0x2f]
    // 0xe47d40: stur            d0, [fp, #-0x30]
    // 0xe47d44: cmp             x4, #4
    // 0xe47d48: b.ge            #0xe47d54
    // 0xe47d4c: LoadField: r3 = r2->field_3b
    //     0xe47d4c: ldur            x3, [x2, #0x3b]
    // 0xe47d50: b               #0xe47d58
    // 0xe47d54: LoadField: r3 = r2->field_33
    //     0xe47d54: ldur            x3, [x2, #0x33]
    // 0xe47d58: scvtf           d1, x3
    // 0xe47d5c: fdiv            d3, d0, d1
    // 0xe47d60: stur            d3, [fp, #-0x28]
    // 0xe47d64: r0 = Matrix4()
    //     0xe47d64: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe47d68: r4 = 32
    //     0xe47d68: movz            x4, #0x20
    // 0xe47d6c: stur            x0, [fp, #-0x20]
    // 0xe47d70: r0 = AllocateFloat64Array()
    //     0xe47d70: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe47d74: mov             x1, x0
    // 0xe47d78: ldur            x0, [fp, #-0x20]
    // 0xe47d7c: StoreField: r0->field_7 = r1
    //     0xe47d7c: stur            w1, [x0, #7]
    // 0xe47d80: mov             x1, x0
    // 0xe47d84: r0 = setIdentity()
    //     0xe47d84: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe47d88: ldur            x0, [fp, #-0x10]
    // 0xe47d8c: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe47d8c: ldur            d0, [x0, #0x17]
    // 0xe47d90: LoadField: d1 = r0->field_1f
    //     0xe47d90: ldur            d1, [x0, #0x1f]
    // 0xe47d94: ldur            d2, [fp, #-0x30]
    // 0xe47d98: fadd            d3, d1, d2
    // 0xe47d9c: ldur            x1, [fp, #-0x20]
    // 0xe47da0: mov             v1.16b, v3.16b
    // 0xe47da4: r0 = translate()
    //     0xe47da4: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe47da8: ldur            d0, [fp, #-0x28]
    // 0xe47dac: fneg            d1, d0
    // 0xe47db0: ldur            d0, [fp, #-0x38]
    // 0xe47db4: r2 = inline_Allocate_Double()
    //     0xe47db4: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xe47db8: add             x2, x2, #0x10
    //     0xe47dbc: cmp             x0, x2
    //     0xe47dc0: b.ls            #0xe47e50
    //     0xe47dc4: str             x2, [THR, #0x50]  ; THR::top
    //     0xe47dc8: sub             x2, x2, #0xf
    //     0xe47dcc: movz            x0, #0xe15c
    //     0xe47dd0: movk            x0, #0x3, lsl #16
    //     0xe47dd4: stur            x0, [x2, #-1]
    // 0xe47dd8: StoreField: r2->field_7 = d0
    //     0xe47dd8: stur            d0, [x2, #7]
    // 0xe47ddc: r0 = inline_Allocate_Double()
    //     0xe47ddc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe47de0: add             x0, x0, #0x10
    //     0xe47de4: cmp             x1, x0
    //     0xe47de8: b.ls            #0xe47e64
    //     0xe47dec: str             x0, [THR, #0x50]  ; THR::top
    //     0xe47df0: sub             x0, x0, #0xf
    //     0xe47df4: movz            x1, #0xe15c
    //     0xe47df8: movk            x1, #0x3, lsl #16
    //     0xe47dfc: stur            x1, [x0, #-1]
    // 0xe47e00: StoreField: r0->field_7 = d1
    //     0xe47e00: stur            d1, [x0, #7]
    // 0xe47e04: str             x0, [SP]
    // 0xe47e08: ldur            x1, [fp, #-0x20]
    // 0xe47e0c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe47e0c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe47e10: r0 = scale()
    //     0xe47e10: bl              #0x645258  ; [package:vector_math/vector_math_64.dart] Matrix4::scale
    // 0xe47e14: ldur            x1, [fp, #-0x18]
    // 0xe47e18: ldur            x2, [fp, #-0x20]
    // 0xe47e1c: r0 = setTransform()
    //     0xe47e1c: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe47e20: ldur            x1, [fp, #-0x18]
    // 0xe47e24: ldur            x2, [fp, #-8]
    // 0xe47e28: d0 = 0.000000
    //     0xe47e28: eor             v0.16b, v0.16b, v0.16b
    // 0xe47e2c: d1 = 0.000000
    //     0xe47e2c: eor             v1.16b, v1.16b, v1.16b
    // 0xe47e30: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xe47e30: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xe47e34: r0 = drawImage()
    //     0xe47e34: bl              #0xe47e7c  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawImage
    // 0xe47e38: r0 = Null
    //     0xe47e38: mov             x0, NULL
    // 0xe47e3c: LeaveFrame
    //     0xe47e3c: mov             SP, fp
    //     0xe47e40: ldp             fp, lr, [SP], #0x10
    // 0xe47e44: ret
    //     0xe47e44: ret             
    // 0xe47e48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe47e48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe47e4c: b               #0xe47ce8
    // 0xe47e50: stp             q0, q1, [SP, #-0x20]!
    // 0xe47e54: r0 = AllocateDouble()
    //     0xe47e54: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe47e58: mov             x2, x0
    // 0xe47e5c: ldp             q0, q1, [SP], #0x20
    // 0xe47e60: b               #0xe47dd8
    // 0xe47e64: SaveReg d1
    //     0xe47e64: str             q1, [SP, #-0x10]!
    // 0xe47e68: SaveReg r2
    //     0xe47e68: str             x2, [SP, #-8]!
    // 0xe47e6c: r0 = AllocateDouble()
    //     0xe47e6c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe47e70: RestoreReg r2
    //     0xe47e70: ldr             x2, [SP], #8
    // 0xe47e74: RestoreReg d1
    //     0xe47e74: ldr             q1, [SP], #0x10
    // 0xe47e78: b               #0xe47e00
  }
  factory _ SvgImg.fromXml(/* No info */) {
    // ** addr: 0xe72504, size: 0x3f4
    // 0xe72504: EnterFrame
    //     0xe72504: stp             fp, lr, [SP, #-0x10]!
    //     0xe72508: mov             fp, SP
    // 0xe7250c: AllocStack(0x78)
    //     0xe7250c: sub             SP, SP, #0x78
    // 0xe72510: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3 */)
    //     0xe72510: mov             x4, x2
    //     0xe72514: mov             x0, x3
    //     0xe72518: stur            x3, [fp, #-0x10]
    //     0xe7251c: mov             x3, x5
    //     0xe72520: stur            x2, [fp, #-8]
    // 0xe72524: CheckStackOverflow
    //     0xe72524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe72528: cmp             SP, x16
    //     0xe7252c: b.ls            #0xe728ec
    // 0xe72530: mov             x2, x4
    // 0xe72534: mov             x5, x0
    // 0xe72538: r1 = Null
    //     0xe72538: mov             x1, NULL
    // 0xe7253c: r0 = SvgBrush.fromXml()
    //     0xe7253c: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe72540: stur            x0, [fp, #-0x18]
    // 0xe72544: r16 = 0.000000
    //     0xe72544: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe72548: str             x16, [SP]
    // 0xe7254c: ldur            x1, [fp, #-8]
    // 0xe72550: mov             x3, x0
    // 0xe72554: r2 = "width"
    //     0xe72554: ldr             x2, [PP, #0x7198]  ; [pp+0x7198] "width"
    // 0xe72558: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe72558: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe7255c: ldr             x4, [x4, #0xaf0]
    // 0xe72560: r0 = getNumeric()
    //     0xe72560: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe72564: mov             x1, x0
    // 0xe72568: r0 = sizeValue()
    //     0xe72568: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe7256c: stur            d0, [fp, #-0x30]
    // 0xe72570: r16 = 0.000000
    //     0xe72570: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe72574: str             x16, [SP]
    // 0xe72578: ldur            x1, [fp, #-8]
    // 0xe7257c: ldur            x3, [fp, #-0x18]
    // 0xe72580: r2 = "height"
    //     0xe72580: ldr             x2, [PP, #0x4778]  ; [pp+0x4778] "height"
    // 0xe72584: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe72584: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe72588: ldr             x4, [x4, #0xaf0]
    // 0xe7258c: r0 = getNumeric()
    //     0xe7258c: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe72590: mov             x1, x0
    // 0xe72594: r0 = sizeValue()
    //     0xe72594: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe72598: stur            d0, [fp, #-0x38]
    // 0xe7259c: r16 = 0.000000
    //     0xe7259c: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe725a0: str             x16, [SP]
    // 0xe725a4: ldur            x1, [fp, #-8]
    // 0xe725a8: ldur            x3, [fp, #-0x18]
    // 0xe725ac: r2 = "x"
    //     0xe725ac: ldr             x2, [PP, #0x71a0]  ; [pp+0x71a0] "x"
    // 0xe725b0: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe725b0: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe725b4: ldr             x4, [x4, #0xaf0]
    // 0xe725b8: r0 = getNumeric()
    //     0xe725b8: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe725bc: mov             x1, x0
    // 0xe725c0: r0 = sizeValue()
    //     0xe725c0: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe725c4: stur            d0, [fp, #-0x40]
    // 0xe725c8: r16 = 0.000000
    //     0xe725c8: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe725cc: str             x16, [SP]
    // 0xe725d0: ldur            x1, [fp, #-8]
    // 0xe725d4: ldur            x3, [fp, #-0x18]
    // 0xe725d8: r2 = "y"
    //     0xe725d8: ldr             x2, [PP, #0x71a8]  ; [pp+0x71a8] "y"
    // 0xe725dc: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe725dc: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe725e0: ldr             x4, [x4, #0xaf0]
    // 0xe725e4: r0 = getNumeric()
    //     0xe725e4: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe725e8: mov             x1, x0
    // 0xe725ec: r0 = sizeValue()
    //     0xe725ec: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe725f0: ldur            x1, [fp, #-8]
    // 0xe725f4: r2 = "href"
    //     0xe725f4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xe725f8: ldr             x2, [x2, #0x28]
    // 0xe725fc: stur            d0, [fp, #-0x48]
    // 0xe72600: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe72600: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe72604: r0 = getAttribute()
    //     0xe72604: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe72608: cmp             w0, NULL
    // 0xe7260c: b.ne            #0xe72634
    // 0xe72610: r16 = "http://www.w3.org/1999/xlink"
    //     0xe72610: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eaf8] "http://www.w3.org/1999/xlink"
    //     0xe72614: ldr             x16, [x16, #0xaf8]
    // 0xe72618: str             x16, [SP]
    // 0xe7261c: ldur            x1, [fp, #-8]
    // 0xe72620: r2 = "href"
    //     0xe72620: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xe72624: ldr             x2, [x2, #0x28]
    // 0xe72628: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xe72628: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xe7262c: ldr             x4, [x4, #0x388]
    // 0xe72630: r0 = getAttribute()
    //     0xe72630: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe72634: stur            x0, [fp, #-0x20]
    // 0xe72638: cmp             w0, NULL
    // 0xe7263c: b.eq            #0xe7284c
    // 0xe72640: mov             x1, x0
    // 0xe72644: r2 = "data:"
    //     0xe72644: add             x2, PP, #0xc, lsl #12  ; [pp+0xccc0] "data:"
    //     0xe72648: ldr             x2, [x2, #0xcc0]
    // 0xe7264c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe7264c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe72650: r0 = startsWith()
    //     0xe72650: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe72654: tbnz            w0, #4, #0xe72840
    // 0xe72658: ldur            x3, [fp, #-0x20]
    // 0xe7265c: r0 = LoadClassIdInstr(r3)
    //     0xe7265c: ldur            x0, [x3, #-1]
    //     0xe72660: ubfx            x0, x0, #0xc, #0x14
    // 0xe72664: mov             x1, x3
    // 0xe72668: r2 = ";"
    //     0xe72668: add             x2, PP, #0x10, lsl #12  ; [pp+0x107f8] ";"
    //     0xe7266c: ldr             x2, [x2, #0x7f8]
    // 0xe72670: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe72670: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe72674: r0 = GDT[cid_x0 + -0xffa]()
    //     0xe72674: sub             lr, x0, #0xffa
    //     0xe72678: ldr             lr, [x21, lr, lsl #3]
    //     0xe7267c: blr             lr
    // 0xe72680: add             x2, x0, #1
    // 0xe72684: ldur            x1, [fp, #-0x20]
    // 0xe72688: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe72688: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe7268c: r0 = substring()
    //     0xe7268c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe72690: mov             x1, x0
    // 0xe72694: r2 = "base64,"
    //     0xe72694: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3eb58] "base64,"
    //     0xe72698: ldr             x2, [x2, #0xb58]
    // 0xe7269c: stur            x0, [fp, #-0x20]
    // 0xe726a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe726a0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe726a4: r0 = startsWith()
    //     0xe726a4: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe726a8: tbnz            w0, #4, #0xe72838
    // 0xe726ac: ldur            x3, [fp, #-0x10]
    // 0xe726b0: ldur            x1, [fp, #-0x20]
    // 0xe726b4: r2 = 7
    //     0xe726b4: movz            x2, #0x7
    // 0xe726b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe726b8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe726bc: r0 = substring()
    //     0xe726bc: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe726c0: stur            x0, [fp, #-0x20]
    // 0xe726c4: r16 = "\\s"
    //     0xe726c4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26020] "\\s"
    //     0xe726c8: ldr             x16, [x16, #0x20]
    // 0xe726cc: stp             x16, NULL, [SP, #0x20]
    // 0xe726d0: r16 = false
    //     0xe726d0: add             x16, NULL, #0x30  ; false
    // 0xe726d4: r30 = true
    //     0xe726d4: add             lr, NULL, #0x20  ; true
    // 0xe726d8: stp             lr, x16, [SP, #0x10]
    // 0xe726dc: r16 = false
    //     0xe726dc: add             x16, NULL, #0x30  ; false
    // 0xe726e0: r30 = false
    //     0xe726e0: add             lr, NULL, #0x30  ; false
    // 0xe726e4: stp             lr, x16, [SP]
    // 0xe726e8: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xe726e8: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xe726ec: r0 = _RegExp()
    //     0xe726ec: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xe726f0: ldur            x1, [fp, #-0x20]
    // 0xe726f4: mov             x2, x0
    // 0xe726f8: r3 = ""
    //     0xe726f8: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe726fc: r0 = replaceAll()
    //     0xe726fc: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xe72700: mov             x2, x0
    // 0xe72704: r1 = Instance_Base64Codec
    //     0xe72704: ldr             x1, [PP, #0xfc0]  ; [pp+0xfc0] Obj!Base64Codec@e2cce1
    // 0xe72708: r0 = decode()
    //     0xe72708: bl              #0x8e936c  ; [dart:convert] Base64Codec::decode
    // 0xe7270c: mov             x1, x0
    // 0xe72710: r0 = decodeImage()
    //     0xe72710: bl              #0xe69e30  ; [package:image/src/formats/formats.dart] ::decodeImage
    // 0xe72714: mov             x2, x0
    // 0xe72718: stur            x2, [fp, #-0x28]
    // 0xe7271c: cmp             w2, NULL
    // 0xe72720: b.eq            #0xe728f4
    // 0xe72724: ldur            x3, [fp, #-0x10]
    // 0xe72728: LoadField: r4 = r3->field_f
    //     0xe72728: ldur            w4, [x3, #0xf]
    // 0xe7272c: DecompressPointer r4
    //     0xe7272c: add             x4, x4, HEAP, lsl #32
    // 0xe72730: stur            x4, [fp, #-0x20]
    // 0xe72734: LoadField: r1 = r2->field_b
    //     0xe72734: ldur            w1, [x2, #0xb]
    // 0xe72738: DecompressPointer r1
    //     0xe72738: add             x1, x1, HEAP, lsl #32
    // 0xe7273c: cmp             w1, NULL
    // 0xe72740: b.ne            #0xe7274c
    // 0xe72744: r0 = Null
    //     0xe72744: mov             x0, NULL
    // 0xe72748: b               #0xe7276c
    // 0xe7274c: r0 = LoadClassIdInstr(r1)
    //     0xe7274c: ldur            x0, [x1, #-1]
    //     0xe72750: ubfx            x0, x0, #0xc, #0x14
    // 0xe72754: r0 = GDT[cid_x0 + 0x5fb]()
    //     0xe72754: add             lr, x0, #0x5fb
    //     0xe72758: ldr             lr, [x21, lr, lsl #3]
    //     0xe7275c: blr             lr
    // 0xe72760: mov             x1, x0
    // 0xe72764: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe72764: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe72768: r0 = asUint8List()
    //     0xe72768: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0xe7276c: cmp             w0, NULL
    // 0xe72770: b.ne            #0xe72784
    // 0xe72774: r4 = 0
    //     0xe72774: movz            x4, #0
    // 0xe72778: r0 = AllocateUint8Array()
    //     0xe72778: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe7277c: mov             x5, x0
    // 0xe72780: b               #0xe72788
    // 0xe72784: mov             x5, x0
    // 0xe72788: ldur            x0, [fp, #-0x28]
    // 0xe7278c: LoadField: r2 = r0->field_b
    //     0xe7278c: ldur            w2, [x0, #0xb]
    // 0xe72790: DecompressPointer r2
    //     0xe72790: add             x2, x2, HEAP, lsl #32
    // 0xe72794: cmp             w2, NULL
    // 0xe72798: b.ne            #0xe727a4
    // 0xe7279c: r0 = Null
    //     0xe7279c: mov             x0, NULL
    // 0xe727a0: b               #0xe727bc
    // 0xe727a4: LoadField: r3 = r2->field_b
    //     0xe727a4: ldur            x3, [x2, #0xb]
    // 0xe727a8: r0 = BoxInt64Instr(r3)
    //     0xe727a8: sbfiz           x0, x3, #1, #0x1f
    //     0xe727ac: cmp             x3, x0, asr #1
    //     0xe727b0: b.eq            #0xe727bc
    //     0xe727b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe727b8: stur            x3, [x0, #7]
    // 0xe727bc: cmp             w0, NULL
    // 0xe727c0: b.ne            #0xe727cc
    // 0xe727c4: r6 = 0
    //     0xe727c4: movz            x6, #0
    // 0xe727c8: b               #0xe727dc
    // 0xe727cc: r1 = LoadInt32Instr(r0)
    //     0xe727cc: sbfx            x1, x0, #1, #0x1f
    //     0xe727d0: tbz             w0, #0, #0xe727d8
    //     0xe727d4: ldur            x1, [x0, #7]
    // 0xe727d8: mov             x6, x1
    // 0xe727dc: cmp             w2, NULL
    // 0xe727e0: b.ne            #0xe727ec
    // 0xe727e4: r0 = Null
    //     0xe727e4: mov             x0, NULL
    // 0xe727e8: b               #0xe72804
    // 0xe727ec: LoadField: r3 = r2->field_13
    //     0xe727ec: ldur            x3, [x2, #0x13]
    // 0xe727f0: r0 = BoxInt64Instr(r3)
    //     0xe727f0: sbfiz           x0, x3, #1, #0x1f
    //     0xe727f4: cmp             x3, x0, asr #1
    //     0xe727f8: b.eq            #0xe72804
    //     0xe727fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe72800: stur            x3, [x0, #7]
    // 0xe72804: cmp             w0, NULL
    // 0xe72808: b.ne            #0xe72814
    // 0xe7280c: r3 = 0
    //     0xe7280c: movz            x3, #0
    // 0xe72810: b               #0xe72824
    // 0xe72814: r1 = LoadInt32Instr(r0)
    //     0xe72814: sbfx            x1, x0, #1, #0x1f
    //     0xe72818: tbz             w0, #0, #0xe72820
    //     0xe7281c: ldur            x1, [x0, #7]
    // 0xe72820: mov             x3, x1
    // 0xe72824: ldur            x2, [fp, #-0x20]
    // 0xe72828: r1 = <PdfDict<PdfDataType>>
    //     0xe72828: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe7282c: ldr             x1, [x1, #0x758]
    // 0xe72830: r0 = PdfImage()
    //     0xe72830: bl              #0xe69530  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::PdfImage
    // 0xe72834: b               #0xe72844
    // 0xe72838: r0 = Null
    //     0xe72838: mov             x0, NULL
    // 0xe7283c: b               #0xe72844
    // 0xe72840: r0 = Null
    //     0xe72840: mov             x0, NULL
    // 0xe72844: mov             x6, x0
    // 0xe72848: b               #0xe72850
    // 0xe7284c: r6 = Null
    //     0xe7284c: mov             x6, NULL
    // 0xe72850: ldur            x0, [fp, #-0x10]
    // 0xe72854: ldur            x4, [fp, #-0x18]
    // 0xe72858: ldur            d3, [fp, #-0x30]
    // 0xe7285c: ldur            d2, [fp, #-0x38]
    // 0xe72860: ldur            d1, [fp, #-0x40]
    // 0xe72864: ldur            d0, [fp, #-0x48]
    // 0xe72868: ldur            x2, [fp, #-8]
    // 0xe7286c: mov             x3, x0
    // 0xe72870: mov             x5, x4
    // 0xe72874: stur            x6, [fp, #-0x20]
    // 0xe72878: r1 = Null
    //     0xe72878: mov             x1, NULL
    // 0xe7287c: r0 = SvgClipPath.fromXml()
    //     0xe7287c: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe72880: ldur            x2, [fp, #-8]
    // 0xe72884: r1 = Null
    //     0xe72884: mov             x1, NULL
    // 0xe72888: stur            x0, [fp, #-8]
    // 0xe7288c: r0 = SvgTransform.fromXml()
    //     0xe7288c: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe72890: stur            x0, [fp, #-0x28]
    // 0xe72894: r0 = SvgImg()
    //     0xe72894: bl              #0xe728f8  ; AllocateSvgImgStub -> SvgImg (size=0x3c)
    // 0xe72898: ldur            d0, [fp, #-0x40]
    // 0xe7289c: ArrayStore: r0[0] = d0  ; List_8
    //     0xe7289c: stur            d0, [x0, #0x17]
    // 0xe728a0: ldur            d0, [fp, #-0x48]
    // 0xe728a4: StoreField: r0->field_1f = d0
    //     0xe728a4: stur            d0, [x0, #0x1f]
    // 0xe728a8: ldur            d0, [fp, #-0x30]
    // 0xe728ac: StoreField: r0->field_27 = d0
    //     0xe728ac: stur            d0, [x0, #0x27]
    // 0xe728b0: ldur            d0, [fp, #-0x38]
    // 0xe728b4: StoreField: r0->field_2f = d0
    //     0xe728b4: stur            d0, [x0, #0x2f]
    // 0xe728b8: ldur            x1, [fp, #-0x20]
    // 0xe728bc: StoreField: r0->field_37 = r1
    //     0xe728bc: stur            w1, [x0, #0x37]
    // 0xe728c0: ldur            x1, [fp, #-0x18]
    // 0xe728c4: StoreField: r0->field_7 = r1
    //     0xe728c4: stur            w1, [x0, #7]
    // 0xe728c8: ldur            x1, [fp, #-8]
    // 0xe728cc: StoreField: r0->field_b = r1
    //     0xe728cc: stur            w1, [x0, #0xb]
    // 0xe728d0: ldur            x1, [fp, #-0x28]
    // 0xe728d4: StoreField: r0->field_f = r1
    //     0xe728d4: stur            w1, [x0, #0xf]
    // 0xe728d8: ldur            x1, [fp, #-0x10]
    // 0xe728dc: StoreField: r0->field_13 = r1
    //     0xe728dc: stur            w1, [x0, #0x13]
    // 0xe728e0: LeaveFrame
    //     0xe728e0: mov             SP, fp
    //     0xe728e4: ldp             fp, lr, [SP], #0x10
    // 0xe728e8: ret
    //     0xe728e8: ret             
    // 0xe728ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe728ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe728f0: b               #0xe72530
    // 0xe728f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe728f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
