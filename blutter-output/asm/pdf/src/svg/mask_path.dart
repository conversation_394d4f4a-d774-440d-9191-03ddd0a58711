// lib: , url: package:pdf/src/svg/mask_path.dart

// class id: 1050833, size: 0x8
class :: {
}

// class id: 841, size: 0x10, field offset: 0x8
//   const constructor, 
class SvgMaskPath extends Object {

  _ apply(/* No info */) {
    // ** addr: 0xe468f4, size: 0x20c
    // 0xe468f4: EnterFrame
    //     0xe468f4: stp             fp, lr, [SP, #-0x10]!
    //     0xe468f8: mov             fp, SP
    // 0xe468fc: AllocStack(0x50)
    //     0xe468fc: sub             SP, SP, #0x50
    // 0xe46900: SetupParameters(SvgMaskPath this /* r1 => r0, fp-0x18 */, dynamic _ /* r2 => r1, fp-0x20 */)
    //     0xe46900: mov             x0, x1
    //     0xe46904: stur            x1, [fp, #-0x18]
    //     0xe46908: mov             x1, x2
    //     0xe4690c: stur            x2, [fp, #-0x20]
    // 0xe46910: CheckStackOverflow
    //     0xe46910: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe46914: cmp             SP, x16
    //     0xe46918: b.ls            #0xe46ae8
    // 0xe4691c: LoadField: r2 = r0->field_b
    //     0xe4691c: ldur            w2, [x0, #0xb]
    // 0xe46920: DecompressPointer r2
    //     0xe46920: add             x2, x2, HEAP, lsl #32
    // 0xe46924: LoadField: r3 = r2->field_f
    //     0xe46924: ldur            w3, [x2, #0xf]
    // 0xe46928: DecompressPointer r3
    //     0xe46928: add             x3, x3, HEAP, lsl #32
    // 0xe4692c: stur            x3, [fp, #-0x10]
    // 0xe46930: LoadField: r4 = r2->field_13
    //     0xe46930: ldur            w4, [x2, #0x13]
    // 0xe46934: DecompressPointer r4
    //     0xe46934: add             x4, x4, HEAP, lsl #32
    // 0xe46938: stur            x4, [fp, #-8]
    // 0xe4693c: r0 = PdfSoftMask()
    //     0xe4693c: bl              #0xe472f8  ; AllocatePdfSoftMaskStub -> PdfSoftMask (size=0x18)
    // 0xe46940: mov             x1, x0
    // 0xe46944: ldur            x2, [fp, #-0x10]
    // 0xe46948: ldur            x3, [fp, #-8]
    // 0xe4694c: stur            x0, [fp, #-8]
    // 0xe46950: r0 = PdfSoftMask()
    //     0xe46950: bl              #0xe46b00  ; [package:pdf/src/pdf/obj/smask.dart] PdfSoftMask::PdfSoftMask
    // 0xe46954: ldur            x0, [fp, #-8]
    // 0xe46958: LoadField: r2 = r0->field_f
    //     0xe46958: ldur            w2, [x0, #0xf]
    // 0xe4695c: DecompressPointer r2
    //     0xe4695c: add             x2, x2, HEAP, lsl #32
    // 0xe46960: ldur            x1, [fp, #-0x18]
    // 0xe46964: stur            x2, [fp, #-0x10]
    // 0xe46968: LoadField: r3 = r1->field_7
    //     0xe46968: ldur            w3, [x1, #7]
    // 0xe4696c: DecompressPointer r3
    //     0xe4696c: add             x3, x3, HEAP, lsl #32
    // 0xe46970: mov             x1, x3
    // 0xe46974: r0 = iterator()
    //     0xe46974: bl              #0x887d48  ; [dart:_internal] MappedIterable::iterator
    // 0xe46978: mov             x2, x0
    // 0xe4697c: stur            x2, [fp, #-0x38]
    // 0xe46980: LoadField: r3 = r2->field_f
    //     0xe46980: ldur            w3, [x2, #0xf]
    // 0xe46984: DecompressPointer r3
    //     0xe46984: add             x3, x3, HEAP, lsl #32
    // 0xe46988: stur            x3, [fp, #-0x30]
    // 0xe4698c: LoadField: r4 = r2->field_13
    //     0xe4698c: ldur            w4, [x2, #0x13]
    // 0xe46990: DecompressPointer r4
    //     0xe46990: add             x4, x4, HEAP, lsl #32
    // 0xe46994: stur            x4, [fp, #-0x28]
    // 0xe46998: LoadField: r5 = r2->field_7
    //     0xe46998: ldur            w5, [x2, #7]
    // 0xe4699c: DecompressPointer r5
    //     0xe4699c: add             x5, x5, HEAP, lsl #32
    // 0xe469a0: stur            x5, [fp, #-0x18]
    // 0xe469a4: ldur            x6, [fp, #-0x10]
    // 0xe469a8: CheckStackOverflow
    //     0xe469a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe469ac: cmp             SP, x16
    //     0xe469b0: b.ls            #0xe46af0
    // 0xe469b4: r0 = LoadClassIdInstr(r3)
    //     0xe469b4: ldur            x0, [x3, #-1]
    //     0xe469b8: ubfx            x0, x0, #0xc, #0x14
    // 0xe469bc: mov             x1, x3
    // 0xe469c0: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe469c0: movz            x17, #0x292d
    //     0xe469c4: movk            x17, #0x1, lsl #16
    //     0xe469c8: add             lr, x0, x17
    //     0xe469cc: ldr             lr, [x21, lr, lsl #3]
    //     0xe469d0: blr             lr
    // 0xe469d4: tbnz            w0, #4, #0xe46ab0
    // 0xe469d8: ldur            x2, [fp, #-0x38]
    // 0xe469dc: ldur            x3, [fp, #-0x30]
    // 0xe469e0: r0 = LoadClassIdInstr(r3)
    //     0xe469e0: ldur            x0, [x3, #-1]
    //     0xe469e4: ubfx            x0, x0, #0xc, #0x14
    // 0xe469e8: mov             x1, x3
    // 0xe469ec: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe469ec: movz            x17, #0x384d
    //     0xe469f0: movk            x17, #0x1, lsl #16
    //     0xe469f4: add             lr, x0, x17
    //     0xe469f8: ldr             lr, [x21, lr, lsl #3]
    //     0xe469fc: blr             lr
    // 0xe46a00: ldur            x16, [fp, #-0x28]
    // 0xe46a04: stp             x0, x16, [SP]
    // 0xe46a08: ldur            x0, [fp, #-0x28]
    // 0xe46a0c: ClosureCall
    //     0xe46a0c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe46a10: ldur            x2, [x0, #0x1f]
    //     0xe46a14: blr             x2
    // 0xe46a18: mov             x4, x0
    // 0xe46a1c: ldur            x3, [fp, #-0x38]
    // 0xe46a20: stur            x4, [fp, #-0x40]
    // 0xe46a24: StoreField: r3->field_b = r0
    //     0xe46a24: stur            w0, [x3, #0xb]
    //     0xe46a28: tbz             w0, #0, #0xe46a44
    //     0xe46a2c: ldurb           w16, [x3, #-1]
    //     0xe46a30: ldurb           w17, [x0, #-1]
    //     0xe46a34: and             x16, x17, x16, lsr #2
    //     0xe46a38: tst             x16, HEAP, lsr #32
    //     0xe46a3c: b.eq            #0xe46a44
    //     0xe46a40: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe46a44: cmp             w4, NULL
    // 0xe46a48: b.ne            #0xe46a7c
    // 0xe46a4c: mov             x0, x4
    // 0xe46a50: ldur            x2, [fp, #-0x18]
    // 0xe46a54: r1 = Null
    //     0xe46a54: mov             x1, NULL
    // 0xe46a58: cmp             w2, NULL
    // 0xe46a5c: b.eq            #0xe46a7c
    // 0xe46a60: LoadField: r4 = r2->field_1b
    //     0xe46a60: ldur            w4, [x2, #0x1b]
    // 0xe46a64: DecompressPointer r4
    //     0xe46a64: add             x4, x4, HEAP, lsl #32
    // 0xe46a68: r8 = X1
    //     0xe46a68: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xe46a6c: LoadField: r9 = r4->field_7
    //     0xe46a6c: ldur            x9, [x4, #7]
    // 0xe46a70: r3 = Null
    //     0xe46a70: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ea28] Null
    //     0xe46a74: ldr             x3, [x3, #0xa28]
    // 0xe46a78: blr             x9
    // 0xe46a7c: ldur            x1, [fp, #-0x40]
    // 0xe46a80: ldur            x0, [fp, #-0x10]
    // 0xe46a84: cmp             w1, NULL
    // 0xe46a88: b.eq            #0xe46af8
    // 0xe46a8c: cmp             w0, NULL
    // 0xe46a90: b.eq            #0xe46afc
    // 0xe46a94: mov             x2, x0
    // 0xe46a98: r0 = paint()
    //     0xe46a98: bl              #0xe46704  ; [package:pdf/src/svg/operation.dart] SvgOperation::paint
    // 0xe46a9c: ldur            x2, [fp, #-0x38]
    // 0xe46aa0: ldur            x5, [fp, #-0x18]
    // 0xe46aa4: ldur            x3, [fp, #-0x30]
    // 0xe46aa8: ldur            x4, [fp, #-0x28]
    // 0xe46aac: b               #0xe469a4
    // 0xe46ab0: ldur            x1, [fp, #-8]
    // 0xe46ab4: ldur            x0, [fp, #-0x38]
    // 0xe46ab8: StoreField: r0->field_b = rNULL
    //     0xe46ab8: stur            NULL, [x0, #0xb]
    // 0xe46abc: r0 = PdfGraphicState()
    //     0xe46abc: bl              #0xe473a8  ; AllocatePdfGraphicStateStub -> PdfGraphicState (size=0x1c)
    // 0xe46ac0: mov             x1, x0
    // 0xe46ac4: ldur            x0, [fp, #-8]
    // 0xe46ac8: StoreField: r1->field_13 = r0
    //     0xe46ac8: stur            w0, [x1, #0x13]
    // 0xe46acc: mov             x2, x1
    // 0xe46ad0: ldur            x1, [fp, #-0x20]
    // 0xe46ad4: r0 = setGraphicState()
    //     0xe46ad4: bl              #0xe47304  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setGraphicState
    // 0xe46ad8: r0 = Null
    //     0xe46ad8: mov             x0, NULL
    // 0xe46adc: LeaveFrame
    //     0xe46adc: mov             SP, fp
    //     0xe46ae0: ldp             fp, lr, [SP], #0x10
    // 0xe46ae4: ret
    //     0xe46ae4: ret             
    // 0xe46ae8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe46ae8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe46aec: b               #0xe4691c
    // 0xe46af0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe46af0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe46af4: b               #0xe469b4
    // 0xe46af8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe46af8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe46afc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe46afc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ fromXml(/* No info */) {
    // ** addr: 0xe739ac, size: 0x1d0
    // 0xe739ac: EnterFrame
    //     0xe739ac: stp             fp, lr, [SP, #-0x10]!
    //     0xe739b0: mov             fp, SP
    // 0xe739b4: AllocStack(0x38)
    //     0xe739b4: sub             SP, SP, #0x38
    // 0xe739b8: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xe739b8: stur            x1, [fp, #-8]
    //     0xe739bc: stur            x2, [fp, #-0x10]
    //     0xe739c0: stur            x3, [fp, #-0x18]
    // 0xe739c4: CheckStackOverflow
    //     0xe739c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe739c8: cmp             SP, x16
    //     0xe739cc: b.ls            #0xe73b74
    // 0xe739d0: r1 = 2
    //     0xe739d0: movz            x1, #0x2
    // 0xe739d4: r0 = AllocateContext()
    //     0xe739d4: bl              #0xec126c  ; AllocateContextStub
    // 0xe739d8: mov             x3, x0
    // 0xe739dc: ldur            x0, [fp, #-0x10]
    // 0xe739e0: stur            x3, [fp, #-0x20]
    // 0xe739e4: StoreField: r3->field_f = r0
    //     0xe739e4: stur            w0, [x3, #0xf]
    // 0xe739e8: ldur            x1, [fp, #-8]
    // 0xe739ec: r2 = "mask"
    //     0xe739ec: add             x2, PP, #0x25, lsl #12  ; [pp+0x25ae8] "mask"
    //     0xe739f0: ldr             x2, [x2, #0xae8]
    // 0xe739f4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe739f4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe739f8: r0 = getAttribute()
    //     0xe739f8: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe739fc: stur            x0, [fp, #-8]
    // 0xe73a00: cmp             w0, NULL
    // 0xe73a04: b.ne            #0xe73a18
    // 0xe73a08: r0 = Null
    //     0xe73a08: mov             x0, NULL
    // 0xe73a0c: LeaveFrame
    //     0xe73a0c: mov             SP, fp
    //     0xe73a10: ldp             fp, lr, [SP], #0x10
    // 0xe73a14: ret
    //     0xe73a14: ret             
    // 0xe73a18: mov             x1, x0
    // 0xe73a1c: r2 = "url(#"
    //     0xe73a1c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25fa8] "url(#"
    //     0xe73a20: ldr             x2, [x2, #0xfa8]
    // 0xe73a24: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe73a24: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe73a28: r0 = startsWith()
    //     0xe73a28: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xe73a2c: tbnz            w0, #4, #0xe73b64
    // 0xe73a30: ldur            x0, [fp, #-0x20]
    // 0xe73a34: ldur            x1, [fp, #-8]
    // 0xe73a38: r2 = ")"
    //     0xe73a38: ldr             x2, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xe73a3c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe73a3c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe73a40: r0 = lastIndexOf()
    //     0xe73a40: bl              #0x642150  ; [dart:core] _StringBase::lastIndexOf
    // 0xe73a44: mov             x2, x0
    // 0xe73a48: r0 = BoxInt64Instr(r2)
    //     0xe73a48: sbfiz           x0, x2, #1, #0x1f
    //     0xe73a4c: cmp             x2, x0, asr #1
    //     0xe73a50: b.eq            #0xe73a5c
    //     0xe73a54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe73a58: stur            x2, [x0, #7]
    // 0xe73a5c: str             x0, [SP]
    // 0xe73a60: ldur            x1, [fp, #-8]
    // 0xe73a64: r2 = 5
    //     0xe73a64: movz            x2, #0x5
    // 0xe73a68: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe73a68: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe73a6c: r0 = substring()
    //     0xe73a6c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe73a70: mov             x1, x0
    // 0xe73a74: ldur            x0, [fp, #-0x20]
    // 0xe73a78: LoadField: r2 = r0->field_f
    //     0xe73a78: ldur            w2, [x0, #0xf]
    // 0xe73a7c: DecompressPointer r2
    //     0xe73a7c: add             x2, x2, HEAP, lsl #32
    // 0xe73a80: LoadField: r3 = r2->field_7
    //     0xe73a80: ldur            w3, [x2, #7]
    // 0xe73a84: DecompressPointer r3
    //     0xe73a84: add             x3, x3, HEAP, lsl #32
    // 0xe73a88: mov             x2, x1
    // 0xe73a8c: mov             x1, x3
    // 0xe73a90: r0 = findById()
    //     0xe73a90: bl              #0xe6f138  ; [package:pdf/src/svg/parser.dart] SvgParser::findById
    // 0xe73a94: stur            x0, [fp, #-8]
    // 0xe73a98: cmp             w0, NULL
    // 0xe73a9c: b.eq            #0xe73b64
    // 0xe73aa0: ldur            x4, [fp, #-0x20]
    // 0xe73aa4: LoadField: r5 = r4->field_f
    //     0xe73aa4: ldur            w5, [x4, #0xf]
    // 0xe73aa8: DecompressPointer r5
    //     0xe73aa8: add             x5, x5, HEAP, lsl #32
    // 0xe73aac: mov             x2, x0
    // 0xe73ab0: ldur            x3, [fp, #-0x18]
    // 0xe73ab4: r1 = Null
    //     0xe73ab4: mov             x1, NULL
    // 0xe73ab8: r0 = SvgBrush.fromXml()
    //     0xe73ab8: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe73abc: ldur            x2, [fp, #-0x20]
    // 0xe73ac0: StoreField: r2->field_13 = r0
    //     0xe73ac0: stur            w0, [x2, #0x13]
    //     0xe73ac4: ldurb           w16, [x2, #-1]
    //     0xe73ac8: ldurb           w17, [x0, #-1]
    //     0xe73acc: and             x16, x17, x16, lsr #2
    //     0xe73ad0: tst             x16, HEAP, lsr #32
    //     0xe73ad4: b.eq            #0xe73adc
    //     0xe73ad8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe73adc: ldur            x0, [fp, #-8]
    // 0xe73ae0: LoadField: r1 = r0->field_f
    //     0xe73ae0: ldur            w1, [x0, #0xf]
    // 0xe73ae4: DecompressPointer r1
    //     0xe73ae4: add             x1, x1, HEAP, lsl #32
    // 0xe73ae8: r16 = <XmlElement>
    //     0xe73ae8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea98] TypeArguments: <XmlElement>
    //     0xe73aec: ldr             x16, [x16, #0xa98]
    // 0xe73af0: stp             x1, x16, [SP]
    // 0xe73af4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe73af4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe73af8: r0 = whereType()
    //     0xe73af8: bl              #0x7b5364  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::whereType
    // 0xe73afc: ldur            x2, [fp, #-0x20]
    // 0xe73b00: r1 = Function '<anonymous closure>': static.
    //     0xe73b00: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ece0] AnonymousClosure: static (0xe6f2bc), in [package:pdf/src/svg/group.dart] SvgGroup::SvgGroup.fromXml (0xe6dfc0)
    //     0xe73b04: ldr             x1, [x1, #0xce0]
    // 0xe73b08: stur            x0, [fp, #-8]
    // 0xe73b0c: r0 = AllocateClosure()
    //     0xe73b0c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe73b10: r16 = <SvgOperation?>
    //     0xe73b10: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eab0] TypeArguments: <SvgOperation?>
    //     0xe73b14: ldr             x16, [x16, #0xab0]
    // 0xe73b18: ldur            lr, [fp, #-8]
    // 0xe73b1c: stp             lr, x16, [SP, #8]
    // 0xe73b20: str             x0, [SP]
    // 0xe73b24: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe73b24: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe73b28: r0 = map()
    //     0xe73b28: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xe73b2c: mov             x1, x0
    // 0xe73b30: ldur            x0, [fp, #-0x20]
    // 0xe73b34: stur            x1, [fp, #-0x10]
    // 0xe73b38: LoadField: r2 = r0->field_f
    //     0xe73b38: ldur            w2, [x0, #0xf]
    // 0xe73b3c: DecompressPointer r2
    //     0xe73b3c: add             x2, x2, HEAP, lsl #32
    // 0xe73b40: stur            x2, [fp, #-8]
    // 0xe73b44: r0 = SvgMaskPath()
    //     0xe73b44: bl              #0xe73b7c  ; AllocateSvgMaskPathStub -> SvgMaskPath (size=0x10)
    // 0xe73b48: ldur            x1, [fp, #-0x10]
    // 0xe73b4c: StoreField: r0->field_7 = r1
    //     0xe73b4c: stur            w1, [x0, #7]
    // 0xe73b50: ldur            x1, [fp, #-8]
    // 0xe73b54: StoreField: r0->field_b = r1
    //     0xe73b54: stur            w1, [x0, #0xb]
    // 0xe73b58: LeaveFrame
    //     0xe73b58: mov             SP, fp
    //     0xe73b5c: ldp             fp, lr, [SP], #0x10
    // 0xe73b60: ret
    //     0xe73b60: ret             
    // 0xe73b64: r0 = Null
    //     0xe73b64: mov             x0, NULL
    // 0xe73b68: LeaveFrame
    //     0xe73b68: mov             SP, fp
    //     0xe73b6c: ldp             fp, lr, [SP], #0x10
    // 0xe73b70: ret
    //     0xe73b70: ret             
    // 0xe73b74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe73b74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe73b78: b               #0xe739d0
  }
}
