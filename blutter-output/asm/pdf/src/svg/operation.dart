// lib: , url: package:pdf/src/svg/operation.dart

// class id: 1050834, size: 0x8
class :: {
}

// class id: 842, size: 0x18, field offset: 0x8
abstract class SvgOperation extends Object {

  _ paint(/* No info */) {
    // ** addr: 0xe46704, size: 0x160
    // 0xe46704: EnterFrame
    //     0xe46704: stp             fp, lr, [SP, #-0x10]!
    //     0xe46708: mov             fp, SP
    // 0xe4670c: AllocStack(0x38)
    //     0xe4670c: sub             SP, SP, #0x38
    // 0xe46710: SetupParameters(SvgOperation this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe46710: mov             x0, x2
    //     0xe46714: stur            x2, [fp, #-0x10]
    //     0xe46718: mov             x2, x1
    //     0xe4671c: stur            x1, [fp, #-8]
    // 0xe46720: CheckStackOverflow
    //     0xe46720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe46724: cmp             SP, x16
    //     0xe46728: b.ls            #0xe46858
    // 0xe4672c: mov             x1, x0
    // 0xe46730: r0 = saveContext()
    //     0xe46730: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe46734: ldur            x0, [fp, #-8]
    // 0xe46738: LoadField: r1 = r0->field_b
    //     0xe46738: ldur            w1, [x0, #0xb]
    // 0xe4673c: DecompressPointer r1
    //     0xe4673c: add             x1, x1, HEAP, lsl #32
    // 0xe46740: ldur            x2, [fp, #-0x10]
    // 0xe46744: r0 = apply()
    //     0xe46744: bl              #0xe47724  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::apply
    // 0xe46748: ldur            x0, [fp, #-8]
    // 0xe4674c: LoadField: r1 = r0->field_f
    //     0xe4674c: ldur            w1, [x0, #0xf]
    // 0xe46750: DecompressPointer r1
    //     0xe46750: add             x1, x1, HEAP, lsl #32
    // 0xe46754: LoadField: r2 = r1->field_7
    //     0xe46754: ldur            w2, [x1, #7]
    // 0xe46758: DecompressPointer r2
    //     0xe46758: add             x2, x2, HEAP, lsl #32
    // 0xe4675c: cmp             w2, NULL
    // 0xe46760: b.eq            #0xe4676c
    // 0xe46764: ldur            x1, [fp, #-0x10]
    // 0xe46768: r0 = setTransform()
    //     0xe46768: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe4676c: ldur            x1, [fp, #-8]
    // 0xe46770: d0 = 1.000000
    //     0xe46770: fmov            d0, #1.00000000
    // 0xe46774: LoadField: r0 = r1->field_7
    //     0xe46774: ldur            w0, [x1, #7]
    // 0xe46778: DecompressPointer r0
    //     0xe46778: add             x0, x0, HEAP, lsl #32
    // 0xe4677c: stur            x0, [fp, #-0x20]
    // 0xe46780: LoadField: r2 = r0->field_7
    //     0xe46780: ldur            w2, [x0, #7]
    // 0xe46784: DecompressPointer r2
    //     0xe46784: add             x2, x2, HEAP, lsl #32
    // 0xe46788: stur            x2, [fp, #-0x18]
    // 0xe4678c: cmp             w2, NULL
    // 0xe46790: b.eq            #0xe46860
    // 0xe46794: LoadField: d1 = r2->field_7
    //     0xe46794: ldur            d1, [x2, #7]
    // 0xe46798: fcmp            d0, d1
    // 0xe4679c: b.gt            #0xe467b0
    // 0xe467a0: LoadField: r3 = r0->field_4b
    //     0xe467a0: ldur            w3, [x0, #0x4b]
    // 0xe467a4: DecompressPointer r3
    //     0xe467a4: add             x3, x3, HEAP, lsl #32
    // 0xe467a8: cmp             w3, NULL
    // 0xe467ac: b.eq            #0xe46808
    // 0xe467b0: r16 = 2
    //     0xe467b0: movz            x16, #0x2
    // 0xe467b4: stp             x16, x2, [SP]
    // 0xe467b8: r0 = ==()
    //     0xe467b8: bl              #0xd81600  ; [dart:core] _Double::==
    // 0xe467bc: tbnz            w0, #4, #0xe467c8
    // 0xe467c0: r1 = Null
    //     0xe467c0: mov             x1, NULL
    // 0xe467c4: b               #0xe467cc
    // 0xe467c8: ldur            x1, [fp, #-0x18]
    // 0xe467cc: ldur            x0, [fp, #-0x20]
    // 0xe467d0: stur            x1, [fp, #-0x28]
    // 0xe467d4: LoadField: r2 = r0->field_4b
    //     0xe467d4: ldur            w2, [x0, #0x4b]
    // 0xe467d8: DecompressPointer r2
    //     0xe467d8: add             x2, x2, HEAP, lsl #32
    // 0xe467dc: stur            x2, [fp, #-0x18]
    // 0xe467e0: r0 = PdfGraphicState()
    //     0xe467e0: bl              #0xe473a8  ; AllocatePdfGraphicStateStub -> PdfGraphicState (size=0x1c)
    // 0xe467e4: mov             x1, x0
    // 0xe467e8: ldur            x0, [fp, #-0x18]
    // 0xe467ec: StoreField: r1->field_f = r0
    //     0xe467ec: stur            w0, [x1, #0xf]
    // 0xe467f0: ldur            x0, [fp, #-0x28]
    // 0xe467f4: StoreField: r1->field_7 = r0
    //     0xe467f4: stur            w0, [x1, #7]
    // 0xe467f8: StoreField: r1->field_b = r0
    //     0xe467f8: stur            w0, [x1, #0xb]
    // 0xe467fc: mov             x2, x1
    // 0xe46800: ldur            x1, [fp, #-0x10]
    // 0xe46804: r0 = setGraphicState()
    //     0xe46804: bl              #0xe47304  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setGraphicState
    // 0xe46808: ldur            x0, [fp, #-0x20]
    // 0xe4680c: LoadField: r1 = r0->field_4f
    //     0xe4680c: ldur            w1, [x0, #0x4f]
    // 0xe46810: DecompressPointer r1
    //     0xe46810: add             x1, x1, HEAP, lsl #32
    // 0xe46814: cmp             w1, NULL
    // 0xe46818: b.eq            #0xe46824
    // 0xe4681c: ldur            x2, [fp, #-0x10]
    // 0xe46820: r0 = apply()
    //     0xe46820: bl              #0xe468f4  ; [package:pdf/src/svg/mask_path.dart] SvgMaskPath::apply
    // 0xe46824: ldur            x1, [fp, #-8]
    // 0xe46828: r0 = LoadClassIdInstr(r1)
    //     0xe46828: ldur            x0, [x1, #-1]
    //     0xe4682c: ubfx            x0, x0, #0xc, #0x14
    // 0xe46830: ldur            x2, [fp, #-0x10]
    // 0xe46834: r0 = GDT[cid_x0 + -0xced]()
    //     0xe46834: sub             lr, x0, #0xced
    //     0xe46838: ldr             lr, [x21, lr, lsl #3]
    //     0xe4683c: blr             lr
    // 0xe46840: ldur            x1, [fp, #-0x10]
    // 0xe46844: r0 = restoreContext()
    //     0xe46844: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe46848: r0 = Null
    //     0xe46848: mov             x0, NULL
    // 0xe4684c: LeaveFrame
    //     0xe4684c: mov             SP, fp
    //     0xe46850: ldp             fp, lr, [SP], #0x10
    // 0xe46854: ret
    //     0xe46854: ret             
    // 0xe46858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe46858: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4685c: b               #0xe4672c
    // 0xe46860: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe46860: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ draw(/* No info */) {
    // ** addr: 0xe47920, size: 0x90
    // 0xe47920: EnterFrame
    //     0xe47920: stp             fp, lr, [SP, #-0x10]!
    //     0xe47924: mov             fp, SP
    // 0xe47928: AllocStack(0x10)
    //     0xe47928: sub             SP, SP, #0x10
    // 0xe4792c: SetupParameters(SvgOperation this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe4792c: mov             x0, x2
    //     0xe47930: stur            x2, [fp, #-0x10]
    //     0xe47934: mov             x2, x1
    //     0xe47938: stur            x1, [fp, #-8]
    // 0xe4793c: CheckStackOverflow
    //     0xe4793c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe47940: cmp             SP, x16
    //     0xe47944: b.ls            #0xe479a8
    // 0xe47948: mov             x1, x0
    // 0xe4794c: r0 = saveContext()
    //     0xe4794c: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe47950: ldur            x0, [fp, #-8]
    // 0xe47954: LoadField: r1 = r0->field_f
    //     0xe47954: ldur            w1, [x0, #0xf]
    // 0xe47958: DecompressPointer r1
    //     0xe47958: add             x1, x1, HEAP, lsl #32
    // 0xe4795c: LoadField: r2 = r1->field_7
    //     0xe4795c: ldur            w2, [x1, #7]
    // 0xe47960: DecompressPointer r2
    //     0xe47960: add             x2, x2, HEAP, lsl #32
    // 0xe47964: cmp             w2, NULL
    // 0xe47968: b.eq            #0xe47974
    // 0xe4796c: ldur            x1, [fp, #-0x10]
    // 0xe47970: r0 = setTransform()
    //     0xe47970: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe47974: ldur            x1, [fp, #-8]
    // 0xe47978: r0 = LoadClassIdInstr(r1)
    //     0xe47978: ldur            x0, [x1, #-1]
    //     0xe4797c: ubfx            x0, x0, #0xc, #0x14
    // 0xe47980: ldur            x2, [fp, #-0x10]
    // 0xe47984: r0 = GDT[cid_x0 + -0xded]()
    //     0xe47984: sub             lr, x0, #0xded
    //     0xe47988: ldr             lr, [x21, lr, lsl #3]
    //     0xe4798c: blr             lr
    // 0xe47990: ldur            x1, [fp, #-0x10]
    // 0xe47994: r0 = restoreContext()
    //     0xe47994: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe47998: r0 = Null
    //     0xe47998: mov             x0, NULL
    // 0xe4799c: LeaveFrame
    //     0xe4799c: mov             SP, fp
    //     0xe479a0: ldp             fp, lr, [SP], #0x10
    // 0xe479a4: ret
    //     0xe479a4: ret             
    // 0xe479a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe479a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe479ac: b               #0xe47948
  }
  static _ fromXml(/* No info */) {
    // ** addr: 0xe6f308, size: 0x3b0
    // 0xe6f308: EnterFrame
    //     0xe6f308: stp             fp, lr, [SP, #-0x10]!
    //     0xe6f30c: mov             fp, SP
    // 0xe6f310: AllocStack(0x30)
    //     0xe6f310: sub             SP, SP, #0x30
    // 0xe6f314: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0xe6f314: mov             x0, x1
    //     0xe6f318: mov             x5, x3
    //     0xe6f31c: stur            x3, [fp, #-0x18]
    //     0xe6f320: mov             x3, x2
    //     0xe6f324: stur            x1, [fp, #-8]
    //     0xe6f328: stur            x2, [fp, #-0x10]
    // 0xe6f32c: CheckStackOverflow
    //     0xe6f32c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6f330: cmp             SP, x16
    //     0xe6f334: b.ls            #0xe6f6b0
    // 0xe6f338: mov             x1, x0
    // 0xe6f33c: r2 = "visibility"
    //     0xe6f33c: add             x2, PP, #8, lsl #12  ; [pp+0x8950] "visibility"
    //     0xe6f340: ldr             x2, [x2, #0x950]
    // 0xe6f344: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6f344: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6f348: r0 = getAttribute()
    //     0xe6f348: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe6f34c: r1 = LoadClassIdInstr(r0)
    //     0xe6f34c: ldur            x1, [x0, #-1]
    //     0xe6f350: ubfx            x1, x1, #0xc, #0x14
    // 0xe6f354: r16 = "hidden"
    //     0xe6f354: add             x16, PP, #0x26, lsl #12  ; [pp+0x26598] "hidden"
    //     0xe6f358: ldr             x16, [x16, #0x598]
    // 0xe6f35c: stp             x16, x0, [SP]
    // 0xe6f360: mov             x0, x1
    // 0xe6f364: mov             lr, x0
    // 0xe6f368: ldr             lr, [x21, lr, lsl #3]
    // 0xe6f36c: blr             lr
    // 0xe6f370: tbnz            w0, #4, #0xe6f384
    // 0xe6f374: r0 = Null
    //     0xe6f374: mov             x0, NULL
    // 0xe6f378: LeaveFrame
    //     0xe6f378: mov             SP, fp
    //     0xe6f37c: ldp             fp, lr, [SP], #0x10
    // 0xe6f380: ret
    //     0xe6f380: ret             
    // 0xe6f384: ldur            x1, [fp, #-8]
    // 0xe6f388: r2 = "display"
    //     0xe6f388: add             x2, PP, #0x24, lsl #12  ; [pp+0x24ad8] "display"
    //     0xe6f38c: ldr             x2, [x2, #0xad8]
    // 0xe6f390: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6f390: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6f394: r0 = getAttribute()
    //     0xe6f394: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe6f398: r1 = LoadClassIdInstr(r0)
    //     0xe6f398: ldur            x1, [x0, #-1]
    //     0xe6f39c: ubfx            x1, x1, #0xc, #0x14
    // 0xe6f3a0: r16 = "none"
    //     0xe6f3a0: add             x16, PP, #0x25, lsl #12  ; [pp+0x25b58] "none"
    //     0xe6f3a4: ldr             x16, [x16, #0xb58]
    // 0xe6f3a8: stp             x16, x0, [SP]
    // 0xe6f3ac: mov             x0, x1
    // 0xe6f3b0: mov             lr, x0
    // 0xe6f3b4: ldr             lr, [x21, lr, lsl #3]
    // 0xe6f3b8: blr             lr
    // 0xe6f3bc: tbnz            w0, #4, #0xe6f3d0
    // 0xe6f3c0: r0 = Null
    //     0xe6f3c0: mov             x0, NULL
    // 0xe6f3c4: LeaveFrame
    //     0xe6f3c4: mov             SP, fp
    //     0xe6f3c8: ldp             fp, lr, [SP], #0x10
    // 0xe6f3cc: ret
    //     0xe6f3cc: ret             
    // 0xe6f3d0: ldur            x2, [fp, #-8]
    // 0xe6f3d4: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xe6f3d4: ldur            w0, [x2, #0x17]
    // 0xe6f3d8: DecompressPointer r0
    //     0xe6f3d8: add             x0, x0, HEAP, lsl #32
    // 0xe6f3dc: r1 = LoadClassIdInstr(r0)
    //     0xe6f3dc: ldur            x1, [x0, #-1]
    //     0xe6f3e0: ubfx            x1, x1, #0xc, #0x14
    // 0xe6f3e4: cmp             x1, #0xe2
    // 0xe6f3e8: b.ne            #0xe6f3fc
    // 0xe6f3ec: LoadField: r1 = r0->field_b
    //     0xe6f3ec: ldur            w1, [x0, #0xb]
    // 0xe6f3f0: DecompressPointer r1
    //     0xe6f3f0: add             x1, x1, HEAP, lsl #32
    // 0xe6f3f4: mov             x0, x1
    // 0xe6f3f8: b               #0xe6f408
    // 0xe6f3fc: LoadField: r1 = r0->field_f
    //     0xe6f3fc: ldur            w1, [x0, #0xf]
    // 0xe6f400: DecompressPointer r1
    //     0xe6f400: add             x1, x1, HEAP, lsl #32
    // 0xe6f404: mov             x0, x1
    // 0xe6f408: stur            x0, [fp, #-0x20]
    // 0xe6f40c: r16 = "circle"
    //     0xe6f40c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eac0] "circle"
    //     0xe6f410: ldr             x16, [x16, #0xac0]
    // 0xe6f414: stp             x0, x16, [SP]
    // 0xe6f418: r0 = ==()
    //     0xe6f418: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f41c: tbnz            w0, #4, #0xe6f440
    // 0xe6f420: ldur            x2, [fp, #-8]
    // 0xe6f424: ldur            x3, [fp, #-0x10]
    // 0xe6f428: ldur            x5, [fp, #-0x18]
    // 0xe6f42c: r1 = Null
    //     0xe6f42c: mov             x1, NULL
    // 0xe6f430: r0 = SvgPath.fromCircleXml()
    //     0xe6f430: bl              #0xe72e60  ; [package:pdf/src/svg/path.dart] SvgPath::SvgPath.fromCircleXml
    // 0xe6f434: LeaveFrame
    //     0xe6f434: mov             SP, fp
    //     0xe6f438: ldp             fp, lr, [SP], #0x10
    // 0xe6f43c: ret
    //     0xe6f43c: ret             
    // 0xe6f440: r16 = "ellipse"
    //     0xe6f440: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eac8] "ellipse"
    //     0xe6f444: ldr             x16, [x16, #0xac8]
    // 0xe6f448: ldur            lr, [fp, #-0x20]
    // 0xe6f44c: stp             lr, x16, [SP]
    // 0xe6f450: r0 = ==()
    //     0xe6f450: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f454: tbnz            w0, #4, #0xe6f478
    // 0xe6f458: ldur            x2, [fp, #-8]
    // 0xe6f45c: ldur            x3, [fp, #-0x10]
    // 0xe6f460: ldur            x5, [fp, #-0x18]
    // 0xe6f464: r1 = Null
    //     0xe6f464: mov             x1, NULL
    // 0xe6f468: r0 = SvgPath.fromEllipseXml()
    //     0xe6f468: bl              #0xe72904  ; [package:pdf/src/svg/path.dart] SvgPath::SvgPath.fromEllipseXml
    // 0xe6f46c: LeaveFrame
    //     0xe6f46c: mov             SP, fp
    //     0xe6f470: ldp             fp, lr, [SP], #0x10
    // 0xe6f474: ret
    //     0xe6f474: ret             
    // 0xe6f478: r16 = "g"
    //     0xe6f478: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ead0] "g"
    //     0xe6f47c: ldr             x16, [x16, #0xad0]
    // 0xe6f480: ldur            lr, [fp, #-0x20]
    // 0xe6f484: stp             lr, x16, [SP]
    // 0xe6f488: r0 = ==()
    //     0xe6f488: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f48c: tbnz            w0, #4, #0xe6f4b0
    // 0xe6f490: ldur            x2, [fp, #-8]
    // 0xe6f494: ldur            x3, [fp, #-0x10]
    // 0xe6f498: ldur            x5, [fp, #-0x18]
    // 0xe6f49c: r1 = Null
    //     0xe6f49c: mov             x1, NULL
    // 0xe6f4a0: r0 = SvgGroup.fromXml()
    //     0xe6f4a0: bl              #0xe6dfc0  ; [package:pdf/src/svg/group.dart] SvgGroup::SvgGroup.fromXml
    // 0xe6f4a4: LeaveFrame
    //     0xe6f4a4: mov             SP, fp
    //     0xe6f4a8: ldp             fp, lr, [SP], #0x10
    // 0xe6f4ac: ret
    //     0xe6f4ac: ret             
    // 0xe6f4b0: r16 = "image"
    //     0xe6f4b0: add             x16, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xe6f4b4: ldr             x16, [x16, #0x520]
    // 0xe6f4b8: ldur            lr, [fp, #-0x20]
    // 0xe6f4bc: stp             lr, x16, [SP]
    // 0xe6f4c0: r0 = ==()
    //     0xe6f4c0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f4c4: tbnz            w0, #4, #0xe6f4e8
    // 0xe6f4c8: ldur            x2, [fp, #-8]
    // 0xe6f4cc: ldur            x3, [fp, #-0x10]
    // 0xe6f4d0: ldur            x5, [fp, #-0x18]
    // 0xe6f4d4: r1 = Null
    //     0xe6f4d4: mov             x1, NULL
    // 0xe6f4d8: r0 = SvgImg.fromXml()
    //     0xe6f4d8: bl              #0xe72504  ; [package:pdf/src/svg/image.dart] SvgImg::SvgImg.fromXml
    // 0xe6f4dc: LeaveFrame
    //     0xe6f4dc: mov             SP, fp
    //     0xe6f4e0: ldp             fp, lr, [SP], #0x10
    // 0xe6f4e4: ret
    //     0xe6f4e4: ret             
    // 0xe6f4e8: r16 = "line"
    //     0xe6f4e8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd658] "line"
    //     0xe6f4ec: ldr             x16, [x16, #0x658]
    // 0xe6f4f0: ldur            lr, [fp, #-0x20]
    // 0xe6f4f4: stp             lr, x16, [SP]
    // 0xe6f4f8: r0 = ==()
    //     0xe6f4f8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f4fc: tbnz            w0, #4, #0xe6f520
    // 0xe6f500: ldur            x2, [fp, #-8]
    // 0xe6f504: ldur            x3, [fp, #-0x10]
    // 0xe6f508: ldur            x5, [fp, #-0x18]
    // 0xe6f50c: r1 = Null
    //     0xe6f50c: mov             x1, NULL
    // 0xe6f510: r0 = SvgPath.fromLineXml()
    //     0xe6f510: bl              #0xe72230  ; [package:pdf/src/svg/path.dart] SvgPath::SvgPath.fromLineXml
    // 0xe6f514: LeaveFrame
    //     0xe6f514: mov             SP, fp
    //     0xe6f518: ldp             fp, lr, [SP], #0x10
    // 0xe6f51c: ret
    //     0xe6f51c: ret             
    // 0xe6f520: r16 = "path"
    //     0xe6f520: ldr             x16, [PP, #0x3638]  ; [pp+0x3638] "path"
    // 0xe6f524: ldur            lr, [fp, #-0x20]
    // 0xe6f528: stp             lr, x16, [SP]
    // 0xe6f52c: r0 = ==()
    //     0xe6f52c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f530: tbnz            w0, #4, #0xe6f554
    // 0xe6f534: ldur            x2, [fp, #-8]
    // 0xe6f538: ldur            x3, [fp, #-0x10]
    // 0xe6f53c: ldur            x5, [fp, #-0x18]
    // 0xe6f540: r1 = Null
    //     0xe6f540: mov             x1, NULL
    // 0xe6f544: r0 = SvgPath.fromXml()
    //     0xe6f544: bl              #0xe72130  ; [package:pdf/src/svg/path.dart] SvgPath::SvgPath.fromXml
    // 0xe6f548: LeaveFrame
    //     0xe6f548: mov             SP, fp
    //     0xe6f54c: ldp             fp, lr, [SP], #0x10
    // 0xe6f550: ret
    //     0xe6f550: ret             
    // 0xe6f554: r16 = "polygon"
    //     0xe6f554: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ead8] "polygon"
    //     0xe6f558: ldr             x16, [x16, #0xad8]
    // 0xe6f55c: ldur            lr, [fp, #-0x20]
    // 0xe6f560: stp             lr, x16, [SP]
    // 0xe6f564: r0 = ==()
    //     0xe6f564: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f568: tbnz            w0, #4, #0xe6f58c
    // 0xe6f56c: ldur            x2, [fp, #-8]
    // 0xe6f570: ldur            x3, [fp, #-0x10]
    // 0xe6f574: ldur            x5, [fp, #-0x18]
    // 0xe6f578: r1 = Null
    //     0xe6f578: mov             x1, NULL
    // 0xe6f57c: r0 = SvgPath.fromPolygonXml()
    //     0xe6f57c: bl              #0xe7202c  ; [package:pdf/src/svg/path.dart] SvgPath::SvgPath.fromPolygonXml
    // 0xe6f580: LeaveFrame
    //     0xe6f580: mov             SP, fp
    //     0xe6f584: ldp             fp, lr, [SP], #0x10
    // 0xe6f588: ret
    //     0xe6f588: ret             
    // 0xe6f58c: r16 = "polyline"
    //     0xe6f58c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eae0] "polyline"
    //     0xe6f590: ldr             x16, [x16, #0xae0]
    // 0xe6f594: ldur            lr, [fp, #-0x20]
    // 0xe6f598: stp             lr, x16, [SP]
    // 0xe6f59c: r0 = ==()
    //     0xe6f59c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f5a0: tbnz            w0, #4, #0xe6f5c4
    // 0xe6f5a4: ldur            x2, [fp, #-8]
    // 0xe6f5a8: ldur            x3, [fp, #-0x10]
    // 0xe6f5ac: ldur            x5, [fp, #-0x18]
    // 0xe6f5b0: r1 = Null
    //     0xe6f5b0: mov             x1, NULL
    // 0xe6f5b4: r0 = SvgPath.fromPolylineXml()
    //     0xe6f5b4: bl              #0xe71f34  ; [package:pdf/src/svg/path.dart] SvgPath::SvgPath.fromPolylineXml
    // 0xe6f5b8: LeaveFrame
    //     0xe6f5b8: mov             SP, fp
    //     0xe6f5bc: ldp             fp, lr, [SP], #0x10
    // 0xe6f5c0: ret
    //     0xe6f5c0: ret             
    // 0xe6f5c4: r16 = "rect"
    //     0xe6f5c4: ldr             x16, [PP, #0x5098]  ; [pp+0x5098] "rect"
    // 0xe6f5c8: ldur            lr, [fp, #-0x20]
    // 0xe6f5cc: stp             lr, x16, [SP]
    // 0xe6f5d0: r0 = ==()
    //     0xe6f5d0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f5d4: tbnz            w0, #4, #0xe6f5f8
    // 0xe6f5d8: ldur            x2, [fp, #-8]
    // 0xe6f5dc: ldur            x3, [fp, #-0x10]
    // 0xe6f5e0: ldur            x5, [fp, #-0x18]
    // 0xe6f5e4: r1 = Null
    //     0xe6f5e4: mov             x1, NULL
    // 0xe6f5e8: r0 = SvgPath.fromRectXml()
    //     0xe6f5e8: bl              #0xe7136c  ; [package:pdf/src/svg/path.dart] SvgPath::SvgPath.fromRectXml
    // 0xe6f5ec: LeaveFrame
    //     0xe6f5ec: mov             SP, fp
    //     0xe6f5f0: ldp             fp, lr, [SP], #0x10
    // 0xe6f5f4: ret
    //     0xe6f5f4: ret             
    // 0xe6f5f8: r16 = "symbol"
    //     0xe6f5f8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eae8] "symbol"
    //     0xe6f5fc: ldr             x16, [x16, #0xae8]
    // 0xe6f600: ldur            lr, [fp, #-0x20]
    // 0xe6f604: stp             lr, x16, [SP]
    // 0xe6f608: r0 = ==()
    //     0xe6f608: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f60c: tbnz            w0, #4, #0xe6f630
    // 0xe6f610: ldur            x2, [fp, #-8]
    // 0xe6f614: ldur            x3, [fp, #-0x10]
    // 0xe6f618: ldur            x5, [fp, #-0x18]
    // 0xe6f61c: r1 = Null
    //     0xe6f61c: mov             x1, NULL
    // 0xe6f620: r0 = SvgSymbol.fromXml()
    //     0xe6f620: bl              #0xe711f4  ; [package:pdf/src/svg/symbol.dart] SvgSymbol::SvgSymbol.fromXml
    // 0xe6f624: LeaveFrame
    //     0xe6f624: mov             SP, fp
    //     0xe6f628: ldp             fp, lr, [SP], #0x10
    // 0xe6f62c: ret
    //     0xe6f62c: ret             
    // 0xe6f630: r16 = "text"
    //     0xe6f630: ldr             x16, [PP, #0x7060]  ; [pp+0x7060] "text"
    // 0xe6f634: ldur            lr, [fp, #-0x20]
    // 0xe6f638: stp             lr, x16, [SP]
    // 0xe6f63c: r0 = ==()
    //     0xe6f63c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f640: tbnz            w0, #4, #0xe6f668
    // 0xe6f644: ldur            x2, [fp, #-8]
    // 0xe6f648: ldur            x3, [fp, #-0x10]
    // 0xe6f64c: ldur            x5, [fp, #-0x18]
    // 0xe6f650: r1 = Null
    //     0xe6f650: mov             x1, NULL
    // 0xe6f654: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xe6f654: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xe6f658: r0 = SvgText.fromXml()
    //     0xe6f658: bl              #0xe6f8e0  ; [package:pdf/src/svg/text.dart] SvgText::SvgText.fromXml
    // 0xe6f65c: LeaveFrame
    //     0xe6f65c: mov             SP, fp
    //     0xe6f660: ldp             fp, lr, [SP], #0x10
    // 0xe6f664: ret
    //     0xe6f664: ret             
    // 0xe6f668: r16 = "use"
    //     0xe6f668: add             x16, PP, #0x26, lsl #12  ; [pp+0x26088] "use"
    //     0xe6f66c: ldr             x16, [x16, #0x88]
    // 0xe6f670: ldur            lr, [fp, #-0x20]
    // 0xe6f674: stp             lr, x16, [SP]
    // 0xe6f678: r0 = ==()
    //     0xe6f678: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6f67c: tbnz            w0, #4, #0xe6f6a0
    // 0xe6f680: ldur            x2, [fp, #-8]
    // 0xe6f684: ldur            x3, [fp, #-0x10]
    // 0xe6f688: ldur            x5, [fp, #-0x18]
    // 0xe6f68c: r1 = Null
    //     0xe6f68c: mov             x1, NULL
    // 0xe6f690: r0 = SvgUse.fromXml()
    //     0xe6f690: bl              #0xe6f6b8  ; [package:pdf/src/svg/use.dart] SvgUse::SvgUse.fromXml
    // 0xe6f694: LeaveFrame
    //     0xe6f694: mov             SP, fp
    //     0xe6f698: ldp             fp, lr, [SP], #0x10
    // 0xe6f69c: ret
    //     0xe6f69c: ret             
    // 0xe6f6a0: r0 = Null
    //     0xe6f6a0: mov             x0, NULL
    // 0xe6f6a4: LeaveFrame
    //     0xe6f6a4: mov             SP, fp
    //     0xe6f6a8: ldp             fp, lr, [SP], #0x10
    // 0xe6f6ac: ret
    //     0xe6f6ac: ret             
    // 0xe6f6b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6f6b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6f6b4: b               #0xe6f338
  }
}
