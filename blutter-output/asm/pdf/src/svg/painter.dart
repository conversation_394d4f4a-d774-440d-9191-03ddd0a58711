// lib: , url: package:pdf/src/svg/painter.dart

// class id: 1050835, size: 0x8
class :: {
}

// class id: 840, size: 0x20, field offset: 0x8
class SvgPainter extends Object {

  _ paint(/* No info */) {
    // ** addr: 0xe6df50, size: 0x70
    // 0xe6df50: EnterFrame
    //     0xe6df50: stp             fp, lr, [SP, #-0x10]!
    //     0xe6df54: mov             fp, SP
    // 0xe6df58: AllocStack(0x8)
    //     0xe6df58: sub             SP, SP, #8
    // 0xe6df5c: SetupParameters(SvgPainter this /* r1 => r0, fp-0x8 */)
    //     0xe6df5c: mov             x0, x1
    //     0xe6df60: stur            x1, [fp, #-8]
    // 0xe6df64: CheckStackOverflow
    //     0xe6df64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6df68: cmp             SP, x16
    //     0xe6df6c: b.ls            #0xe6dfb8
    // 0xe6df70: LoadField: r1 = r0->field_7
    //     0xe6df70: ldur            w1, [x0, #7]
    // 0xe6df74: DecompressPointer r1
    //     0xe6df74: add             x1, x1, HEAP, lsl #32
    // 0xe6df78: LoadField: r2 = r1->field_b
    //     0xe6df78: ldur            w2, [x1, #0xb]
    // 0xe6df7c: DecompressPointer r2
    //     0xe6df7c: add             x2, x2, HEAP, lsl #32
    // 0xe6df80: mov             x3, x0
    // 0xe6df84: r1 = Null
    //     0xe6df84: mov             x1, NULL
    // 0xe6df88: r5 = Instance_SvgBrush
    //     0xe6df88: add             x5, PP, #0x3e, lsl #12  ; [pp+0x3ea20] Obj!SvgBrush@e0c661
    //     0xe6df8c: ldr             x5, [x5, #0xa20]
    // 0xe6df90: r0 = SvgGroup.fromXml()
    //     0xe6df90: bl              #0xe6dfc0  ; [package:pdf/src/svg/group.dart] SvgGroup::SvgGroup.fromXml
    // 0xe6df94: mov             x1, x0
    // 0xe6df98: ldur            x0, [fp, #-8]
    // 0xe6df9c: LoadField: r2 = r0->field_b
    //     0xe6df9c: ldur            w2, [x0, #0xb]
    // 0xe6dfa0: DecompressPointer r2
    //     0xe6dfa0: add             x2, x2, HEAP, lsl #32
    // 0xe6dfa4: r0 = paint()
    //     0xe6dfa4: bl              #0xe46704  ; [package:pdf/src/svg/operation.dart] SvgOperation::paint
    // 0xe6dfa8: r0 = Null
    //     0xe6dfa8: mov             x0, NULL
    // 0xe6dfac: LeaveFrame
    //     0xe6dfac: mov             SP, fp
    //     0xe6dfb0: ldp             fp, lr, [SP], #0x10
    // 0xe6dfb4: ret
    //     0xe6dfb4: ret             
    // 0xe6dfb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6dfb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6dfbc: b               #0xe6df70
  }
  _ getFontCache(/* No info */) {
    // ** addr: 0xe709ec, size: 0xf4
    // 0xe709ec: EnterFrame
    //     0xe709ec: stp             fp, lr, [SP, #-0x10]!
    //     0xe709f0: mov             fp, SP
    // 0xe709f4: AllocStack(0x38)
    //     0xe709f4: sub             SP, SP, #0x38
    // 0xe709f8: SetupParameters(SvgPainter this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xe709f8: mov             x4, x1
    //     0xe709fc: mov             x0, x2
    //     0xe70a00: stur            x1, [fp, #-8]
    //     0xe70a04: stur            x2, [fp, #-0x10]
    //     0xe70a08: stur            x3, [fp, #-0x18]
    //     0xe70a0c: stur            x5, [fp, #-0x20]
    // 0xe70a10: CheckStackOverflow
    //     0xe70a10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe70a14: cmp             SP, x16
    //     0xe70a18: b.ls            #0xe70ad8
    // 0xe70a1c: r1 = Null
    //     0xe70a1c: mov             x1, NULL
    // 0xe70a20: r2 = 10
    //     0xe70a20: movz            x2, #0xa
    // 0xe70a24: r0 = AllocateArray()
    //     0xe70a24: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe70a28: ldur            x2, [fp, #-0x10]
    // 0xe70a2c: StoreField: r0->field_f = r2
    //     0xe70a2c: stur            w2, [x0, #0xf]
    // 0xe70a30: r16 = "-"
    //     0xe70a30: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xe70a34: StoreField: r0->field_13 = r16
    //     0xe70a34: stur            w16, [x0, #0x13]
    // 0xe70a38: ldur            x3, [fp, #-0x18]
    // 0xe70a3c: ArrayStore: r0[0] = r3  ; List_4
    //     0xe70a3c: stur            w3, [x0, #0x17]
    // 0xe70a40: r16 = "-"
    //     0xe70a40: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xe70a44: StoreField: r0->field_1b = r16
    //     0xe70a44: stur            w16, [x0, #0x1b]
    // 0xe70a48: ldur            x5, [fp, #-0x20]
    // 0xe70a4c: StoreField: r0->field_1f = r5
    //     0xe70a4c: stur            w5, [x0, #0x1f]
    // 0xe70a50: str             x0, [SP]
    // 0xe70a54: r0 = _interpolate()
    //     0xe70a54: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe70a58: mov             x3, x0
    // 0xe70a5c: ldur            x0, [fp, #-8]
    // 0xe70a60: stur            x3, [fp, #-0x30]
    // 0xe70a64: LoadField: r4 = r0->field_1b
    //     0xe70a64: ldur            w4, [x0, #0x1b]
    // 0xe70a68: DecompressPointer r4
    //     0xe70a68: add             x4, x4, HEAP, lsl #32
    // 0xe70a6c: mov             x1, x4
    // 0xe70a70: mov             x2, x3
    // 0xe70a74: stur            x4, [fp, #-0x28]
    // 0xe70a78: r0 = containsKey()
    //     0xe70a78: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xe70a7c: tbz             w0, #4, #0xe70aa4
    // 0xe70a80: ldur            x1, [fp, #-8]
    // 0xe70a84: ldur            x2, [fp, #-0x10]
    // 0xe70a88: ldur            x3, [fp, #-0x18]
    // 0xe70a8c: ldur            x5, [fp, #-0x20]
    // 0xe70a90: r0 = getFont()
    //     0xe70a90: bl              #0xe70ae0  ; [package:pdf/src/svg/painter.dart] SvgPainter::getFont
    // 0xe70a94: ldur            x1, [fp, #-0x28]
    // 0xe70a98: ldur            x2, [fp, #-0x30]
    // 0xe70a9c: mov             x3, x0
    // 0xe70aa0: r0 = []=()
    //     0xe70aa0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe70aa4: ldur            x0, [fp, #-0x28]
    // 0xe70aa8: mov             x1, x0
    // 0xe70aac: ldur            x2, [fp, #-0x30]
    // 0xe70ab0: r0 = _getValueOrData()
    //     0xe70ab0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xe70ab4: ldur            x1, [fp, #-0x28]
    // 0xe70ab8: LoadField: r2 = r1->field_f
    //     0xe70ab8: ldur            w2, [x1, #0xf]
    // 0xe70abc: DecompressPointer r2
    //     0xe70abc: add             x2, x2, HEAP, lsl #32
    // 0xe70ac0: cmp             w2, w0
    // 0xe70ac4: b.ne            #0xe70acc
    // 0xe70ac8: r0 = Null
    //     0xe70ac8: mov             x0, NULL
    // 0xe70acc: LeaveFrame
    //     0xe70acc: mov             SP, fp
    //     0xe70ad0: ldp             fp, lr, [SP], #0x10
    // 0xe70ad4: ret
    //     0xe70ad4: ret             
    // 0xe70ad8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe70ad8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe70adc: b               #0xe70a1c
  }
  _ getFont(/* No info */) {
    // ** addr: 0xe70ae0, size: 0x2e8
    // 0xe70ae0: EnterFrame
    //     0xe70ae0: stp             fp, lr, [SP, #-0x10]!
    //     0xe70ae4: mov             fp, SP
    // 0xe70ae8: AllocStack(0x28)
    //     0xe70ae8: sub             SP, SP, #0x28
    // 0xe70aec: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0xe70aec: stur            x2, [fp, #-8]
    //     0xe70af0: stur            x3, [fp, #-0x10]
    //     0xe70af4: stur            x5, [fp, #-0x18]
    // 0xe70af8: CheckStackOverflow
    //     0xe70af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe70afc: cmp             SP, x16
    //     0xe70b00: b.ls            #0xe70dc0
    // 0xe70b04: r16 = "serif"
    //     0xe70b04: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb28] "serif"
    //     0xe70b08: ldr             x16, [x16, #0xb28]
    // 0xe70b0c: stp             x2, x16, [SP]
    // 0xe70b10: r0 = ==()
    //     0xe70b10: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70b14: tbnz            w0, #4, #0xe70be0
    // 0xe70b18: r16 = "normal"
    //     0xe70b18: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c50] "normal"
    //     0xe70b1c: ldr             x16, [x16, #0xc50]
    // 0xe70b20: ldur            lr, [fp, #-0x10]
    // 0xe70b24: stp             lr, x16, [SP]
    // 0xe70b28: r0 = ==()
    //     0xe70b28: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70b2c: tbnz            w0, #4, #0xe70b88
    // 0xe70b30: r16 = "normal"
    //     0xe70b30: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c50] "normal"
    //     0xe70b34: ldr             x16, [x16, #0xc50]
    // 0xe70b38: ldur            lr, [fp, #-0x18]
    // 0xe70b3c: stp             lr, x16, [SP]
    // 0xe70b40: r0 = ==()
    //     0xe70b40: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70b44: tbz             w0, #4, #0xe70b60
    // 0xe70b48: r16 = "lighter"
    //     0xe70b48: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb30] "lighter"
    //     0xe70b4c: ldr             x16, [x16, #0xb30]
    // 0xe70b50: ldur            lr, [fp, #-0x18]
    // 0xe70b54: stp             lr, x16, [SP]
    // 0xe70b58: r0 = ==()
    //     0xe70b58: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70b5c: tbnz            w0, #4, #0xe70b74
    // 0xe70b60: r1 = Null
    //     0xe70b60: mov             x1, NULL
    // 0xe70b64: r0 = Font.times()
    //     0xe70b64: bl              #0xe70ec4  ; [package:pdf/src/widgets/font.dart] Font::Font.times
    // 0xe70b68: LeaveFrame
    //     0xe70b68: mov             SP, fp
    //     0xe70b6c: ldp             fp, lr, [SP], #0x10
    // 0xe70b70: ret
    //     0xe70b70: ret             
    // 0xe70b74: r1 = Null
    //     0xe70b74: mov             x1, NULL
    // 0xe70b78: r0 = Font.timesBold()
    //     0xe70b78: bl              #0xe70ea0  ; [package:pdf/src/widgets/font.dart] Font::Font.timesBold
    // 0xe70b7c: LeaveFrame
    //     0xe70b7c: mov             SP, fp
    //     0xe70b80: ldp             fp, lr, [SP], #0x10
    // 0xe70b84: ret
    //     0xe70b84: ret             
    // 0xe70b88: r16 = "normal"
    //     0xe70b88: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c50] "normal"
    //     0xe70b8c: ldr             x16, [x16, #0xc50]
    // 0xe70b90: ldur            lr, [fp, #-0x18]
    // 0xe70b94: stp             lr, x16, [SP]
    // 0xe70b98: r0 = ==()
    //     0xe70b98: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70b9c: tbz             w0, #4, #0xe70bb8
    // 0xe70ba0: r16 = "lighter"
    //     0xe70ba0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb30] "lighter"
    //     0xe70ba4: ldr             x16, [x16, #0xb30]
    // 0xe70ba8: ldur            lr, [fp, #-0x18]
    // 0xe70bac: stp             lr, x16, [SP]
    // 0xe70bb0: r0 = ==()
    //     0xe70bb0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70bb4: tbnz            w0, #4, #0xe70bcc
    // 0xe70bb8: r1 = Null
    //     0xe70bb8: mov             x1, NULL
    // 0xe70bbc: r0 = Font.timesItalic()
    //     0xe70bbc: bl              #0xe70e7c  ; [package:pdf/src/widgets/font.dart] Font::Font.timesItalic
    // 0xe70bc0: LeaveFrame
    //     0xe70bc0: mov             SP, fp
    //     0xe70bc4: ldp             fp, lr, [SP], #0x10
    // 0xe70bc8: ret
    //     0xe70bc8: ret             
    // 0xe70bcc: r1 = Null
    //     0xe70bcc: mov             x1, NULL
    // 0xe70bd0: r0 = Font.timesBoldItalic()
    //     0xe70bd0: bl              #0xe70e58  ; [package:pdf/src/widgets/font.dart] Font::Font.timesBoldItalic
    // 0xe70bd4: LeaveFrame
    //     0xe70bd4: mov             SP, fp
    //     0xe70bd8: ldp             fp, lr, [SP], #0x10
    // 0xe70bdc: ret
    //     0xe70bdc: ret             
    // 0xe70be0: r16 = "monospace"
    //     0xe70be0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27e10] "monospace"
    //     0xe70be4: ldr             x16, [x16, #0xe10]
    // 0xe70be8: ldur            lr, [fp, #-8]
    // 0xe70bec: stp             lr, x16, [SP]
    // 0xe70bf0: r0 = ==()
    //     0xe70bf0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70bf4: tbnz            w0, #4, #0xe70cc0
    // 0xe70bf8: r16 = "normal"
    //     0xe70bf8: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c50] "normal"
    //     0xe70bfc: ldr             x16, [x16, #0xc50]
    // 0xe70c00: ldur            lr, [fp, #-0x10]
    // 0xe70c04: stp             lr, x16, [SP]
    // 0xe70c08: r0 = ==()
    //     0xe70c08: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70c0c: tbnz            w0, #4, #0xe70c68
    // 0xe70c10: r16 = "normal"
    //     0xe70c10: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c50] "normal"
    //     0xe70c14: ldr             x16, [x16, #0xc50]
    // 0xe70c18: ldur            lr, [fp, #-0x18]
    // 0xe70c1c: stp             lr, x16, [SP]
    // 0xe70c20: r0 = ==()
    //     0xe70c20: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70c24: tbz             w0, #4, #0xe70c40
    // 0xe70c28: r16 = "lighter"
    //     0xe70c28: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb30] "lighter"
    //     0xe70c2c: ldr             x16, [x16, #0xb30]
    // 0xe70c30: ldur            lr, [fp, #-0x18]
    // 0xe70c34: stp             lr, x16, [SP]
    // 0xe70c38: r0 = ==()
    //     0xe70c38: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70c3c: tbnz            w0, #4, #0xe70c54
    // 0xe70c40: r1 = Null
    //     0xe70c40: mov             x1, NULL
    // 0xe70c44: r0 = Font.courier()
    //     0xe70c44: bl              #0xe70e34  ; [package:pdf/src/widgets/font.dart] Font::Font.courier
    // 0xe70c48: LeaveFrame
    //     0xe70c48: mov             SP, fp
    //     0xe70c4c: ldp             fp, lr, [SP], #0x10
    // 0xe70c50: ret
    //     0xe70c50: ret             
    // 0xe70c54: r1 = Null
    //     0xe70c54: mov             x1, NULL
    // 0xe70c58: r0 = Font.courierBold()
    //     0xe70c58: bl              #0xe70e10  ; [package:pdf/src/widgets/font.dart] Font::Font.courierBold
    // 0xe70c5c: LeaveFrame
    //     0xe70c5c: mov             SP, fp
    //     0xe70c60: ldp             fp, lr, [SP], #0x10
    // 0xe70c64: ret
    //     0xe70c64: ret             
    // 0xe70c68: r16 = "normal"
    //     0xe70c68: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c50] "normal"
    //     0xe70c6c: ldr             x16, [x16, #0xc50]
    // 0xe70c70: ldur            lr, [fp, #-0x18]
    // 0xe70c74: stp             lr, x16, [SP]
    // 0xe70c78: r0 = ==()
    //     0xe70c78: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70c7c: tbz             w0, #4, #0xe70c98
    // 0xe70c80: r16 = "lighter"
    //     0xe70c80: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb30] "lighter"
    //     0xe70c84: ldr             x16, [x16, #0xb30]
    // 0xe70c88: ldur            lr, [fp, #-0x18]
    // 0xe70c8c: stp             lr, x16, [SP]
    // 0xe70c90: r0 = ==()
    //     0xe70c90: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70c94: tbnz            w0, #4, #0xe70cac
    // 0xe70c98: r1 = Null
    //     0xe70c98: mov             x1, NULL
    // 0xe70c9c: r0 = Font.courierOblique()
    //     0xe70c9c: bl              #0xe70dec  ; [package:pdf/src/widgets/font.dart] Font::Font.courierOblique
    // 0xe70ca0: LeaveFrame
    //     0xe70ca0: mov             SP, fp
    //     0xe70ca4: ldp             fp, lr, [SP], #0x10
    // 0xe70ca8: ret
    //     0xe70ca8: ret             
    // 0xe70cac: r1 = Null
    //     0xe70cac: mov             x1, NULL
    // 0xe70cb0: r0 = Font.courierBoldOblique()
    //     0xe70cb0: bl              #0xe70dc8  ; [package:pdf/src/widgets/font.dart] Font::Font.courierBoldOblique
    // 0xe70cb4: LeaveFrame
    //     0xe70cb4: mov             SP, fp
    //     0xe70cb8: ldp             fp, lr, [SP], #0x10
    // 0xe70cbc: ret
    //     0xe70cbc: ret             
    // 0xe70cc0: r16 = "normal"
    //     0xe70cc0: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c50] "normal"
    //     0xe70cc4: ldr             x16, [x16, #0xc50]
    // 0xe70cc8: ldur            lr, [fp, #-0x10]
    // 0xe70ccc: stp             lr, x16, [SP]
    // 0xe70cd0: r0 = ==()
    //     0xe70cd0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70cd4: tbnz            w0, #4, #0xe70d50
    // 0xe70cd8: r16 = "normal"
    //     0xe70cd8: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c50] "normal"
    //     0xe70cdc: ldr             x16, [x16, #0xc50]
    // 0xe70ce0: ldur            lr, [fp, #-0x18]
    // 0xe70ce4: stp             lr, x16, [SP]
    // 0xe70ce8: r0 = ==()
    //     0xe70ce8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70cec: tbz             w0, #4, #0xe70d08
    // 0xe70cf0: r16 = "lighter"
    //     0xe70cf0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb30] "lighter"
    //     0xe70cf4: ldr             x16, [x16, #0xb30]
    // 0xe70cf8: ldur            lr, [fp, #-0x18]
    // 0xe70cfc: stp             lr, x16, [SP]
    // 0xe70d00: r0 = ==()
    //     0xe70d00: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70d04: tbnz            w0, #4, #0xe70d2c
    // 0xe70d08: r0 = Font()
    //     0xe70d08: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70d0c: mov             x1, x0
    // 0xe70d10: r0 = Instance_Type1Fonts
    //     0xe70d10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e590] Obj!Type1Fonts@e2e721
    //     0xe70d14: ldr             x0, [x0, #0x590]
    // 0xe70d18: StoreField: r1->field_7 = r0
    //     0xe70d18: stur            w0, [x1, #7]
    // 0xe70d1c: mov             x0, x1
    // 0xe70d20: LeaveFrame
    //     0xe70d20: mov             SP, fp
    //     0xe70d24: ldp             fp, lr, [SP], #0x10
    // 0xe70d28: ret
    //     0xe70d28: ret             
    // 0xe70d2c: r0 = Font()
    //     0xe70d2c: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70d30: mov             x1, x0
    // 0xe70d34: r0 = Instance_Type1Fonts
    //     0xe70d34: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e588] Obj!Type1Fonts@e2e701
    //     0xe70d38: ldr             x0, [x0, #0x588]
    // 0xe70d3c: StoreField: r1->field_7 = r0
    //     0xe70d3c: stur            w0, [x1, #7]
    // 0xe70d40: mov             x0, x1
    // 0xe70d44: LeaveFrame
    //     0xe70d44: mov             SP, fp
    //     0xe70d48: ldp             fp, lr, [SP], #0x10
    // 0xe70d4c: ret
    //     0xe70d4c: ret             
    // 0xe70d50: r16 = "normal"
    //     0xe70d50: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c50] "normal"
    //     0xe70d54: ldr             x16, [x16, #0xc50]
    // 0xe70d58: ldur            lr, [fp, #-0x18]
    // 0xe70d5c: stp             lr, x16, [SP]
    // 0xe70d60: r0 = ==()
    //     0xe70d60: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70d64: tbz             w0, #4, #0xe70d80
    // 0xe70d68: r16 = "lighter"
    //     0xe70d68: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb30] "lighter"
    //     0xe70d6c: ldr             x16, [x16, #0xb30]
    // 0xe70d70: ldur            lr, [fp, #-0x18]
    // 0xe70d74: stp             lr, x16, [SP]
    // 0xe70d78: r0 = ==()
    //     0xe70d78: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe70d7c: tbnz            w0, #4, #0xe70da4
    // 0xe70d80: r0 = Font()
    //     0xe70d80: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70d84: mov             x1, x0
    // 0xe70d88: r0 = Instance_Type1Fonts
    //     0xe70d88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e580] Obj!Type1Fonts@e2e6e1
    //     0xe70d8c: ldr             x0, [x0, #0x580]
    // 0xe70d90: StoreField: r1->field_7 = r0
    //     0xe70d90: stur            w0, [x1, #7]
    // 0xe70d94: mov             x0, x1
    // 0xe70d98: LeaveFrame
    //     0xe70d98: mov             SP, fp
    //     0xe70d9c: ldp             fp, lr, [SP], #0x10
    // 0xe70da0: ret
    //     0xe70da0: ret             
    // 0xe70da4: r0 = Font()
    //     0xe70da4: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70da8: r1 = Instance_Type1Fonts
    //     0xe70da8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e578] Obj!Type1Fonts@e2e6c1
    //     0xe70dac: ldr             x1, [x1, #0x578]
    // 0xe70db0: StoreField: r0->field_7 = r1
    //     0xe70db0: stur            w1, [x0, #7]
    // 0xe70db4: LeaveFrame
    //     0xe70db4: mov             SP, fp
    //     0xe70db8: ldp             fp, lr, [SP], #0x10
    // 0xe70dbc: ret
    //     0xe70dbc: ret             
    // 0xe70dc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe70dc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe70dc4: b               #0xe70b04
  }
  _ SvgPainter(/* No info */) {
    // ** addr: 0xe76ea8, size: 0x110
    // 0xe76ea8: EnterFrame
    //     0xe76ea8: stp             fp, lr, [SP, #-0x10]!
    //     0xe76eac: mov             fp, SP
    // 0xe76eb0: AllocStack(0x38)
    //     0xe76eb0: sub             SP, SP, #0x38
    // 0xe76eb4: SetupParameters(SvgPainter this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */, dynamic _ /* r6 => r0, fp-0x28 */)
    //     0xe76eb4: mov             x4, x1
    //     0xe76eb8: stur            x2, [fp, #-0x10]
    //     0xe76ebc: mov             x16, x3
    //     0xe76ec0: mov             x3, x2
    //     0xe76ec4: mov             x2, x16
    //     0xe76ec8: stur            x1, [fp, #-8]
    //     0xe76ecc: mov             x1, x5
    //     0xe76ed0: mov             x0, x6
    //     0xe76ed4: stur            x2, [fp, #-0x18]
    //     0xe76ed8: stur            x5, [fp, #-0x20]
    //     0xe76edc: stur            x6, [fp, #-0x28]
    // 0xe76ee0: CheckStackOverflow
    //     0xe76ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe76ee4: cmp             SP, x16
    //     0xe76ee8: b.ls            #0xe76fb0
    // 0xe76eec: r16 = <String, Font>
    //     0xe76eec: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed90] TypeArguments: <String, Font>
    //     0xe76ef0: ldr             x16, [x16, #0xd90]
    // 0xe76ef4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe76ef8: stp             lr, x16, [SP]
    // 0xe76efc: r0 = Map._fromLiteral()
    //     0xe76efc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe76f00: ldur            x1, [fp, #-8]
    // 0xe76f04: StoreField: r1->field_1b = r0
    //     0xe76f04: stur            w0, [x1, #0x1b]
    //     0xe76f08: ldurb           w16, [x1, #-1]
    //     0xe76f0c: ldurb           w17, [x0, #-1]
    //     0xe76f10: and             x16, x17, x16, lsr #2
    //     0xe76f14: tst             x16, HEAP, lsr #32
    //     0xe76f18: b.eq            #0xe76f20
    //     0xe76f1c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe76f20: ldur            x0, [fp, #-0x10]
    // 0xe76f24: StoreField: r1->field_7 = r0
    //     0xe76f24: stur            w0, [x1, #7]
    //     0xe76f28: ldurb           w16, [x1, #-1]
    //     0xe76f2c: ldurb           w17, [x0, #-1]
    //     0xe76f30: and             x16, x17, x16, lsr #2
    //     0xe76f34: tst             x16, HEAP, lsr #32
    //     0xe76f38: b.eq            #0xe76f40
    //     0xe76f3c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe76f40: ldur            x0, [fp, #-0x18]
    // 0xe76f44: StoreField: r1->field_b = r0
    //     0xe76f44: stur            w0, [x1, #0xb]
    //     0xe76f48: ldurb           w16, [x1, #-1]
    //     0xe76f4c: ldurb           w17, [x0, #-1]
    //     0xe76f50: and             x16, x17, x16, lsr #2
    //     0xe76f54: tst             x16, HEAP, lsr #32
    //     0xe76f58: b.eq            #0xe76f60
    //     0xe76f5c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe76f60: ldur            x0, [fp, #-0x20]
    // 0xe76f64: StoreField: r1->field_f = r0
    //     0xe76f64: stur            w0, [x1, #0xf]
    //     0xe76f68: ldurb           w16, [x1, #-1]
    //     0xe76f6c: ldurb           w17, [x0, #-1]
    //     0xe76f70: and             x16, x17, x16, lsr #2
    //     0xe76f74: tst             x16, HEAP, lsr #32
    //     0xe76f78: b.eq            #0xe76f80
    //     0xe76f7c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe76f80: ldur            x0, [fp, #-0x28]
    // 0xe76f84: StoreField: r1->field_13 = r0
    //     0xe76f84: stur            w0, [x1, #0x13]
    //     0xe76f88: ldurb           w16, [x1, #-1]
    //     0xe76f8c: ldurb           w17, [x0, #-1]
    //     0xe76f90: and             x16, x17, x16, lsr #2
    //     0xe76f94: tst             x16, HEAP, lsr #32
    //     0xe76f98: b.eq            #0xe76fa0
    //     0xe76f9c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe76fa0: r0 = Null
    //     0xe76fa0: mov             x0, NULL
    // 0xe76fa4: LeaveFrame
    //     0xe76fa4: mov             SP, fp
    //     0xe76fa8: ldp             fp, lr, [SP], #0x10
    // 0xe76fac: ret
    //     0xe76fac: ret             
    // 0xe76fb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe76fb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76fb4: b               #0xe76eec
  }
}
