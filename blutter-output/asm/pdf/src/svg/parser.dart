// lib: , url: package:pdf/src/svg/parser.dart

// class id: 1050836, size: 0x8
class :: {
}

// class id: 838, size: 0x18, field offset: 0x8
//   const constructor, 
class SvgNumeric extends Object {

  _Double field_8;
  SvgUnit field_10;

  get _ sizeValue(/* No info */) {
    // ** addr: 0xb144a0, size: 0x130
    // 0xb144a0: EnterFrame
    //     0xb144a0: stp             fp, lr, [SP, #-0x10]!
    //     0xb144a4: mov             fp, SP
    // 0xb144a8: AllocStack(0x8)
    //     0xb144a8: sub             SP, SP, #8
    // 0xb144ac: CheckStackOverflow
    //     0xb144ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb144b0: cmp             SP, x16
    //     0xb144b4: b.ls            #0xb145c0
    // 0xb144b8: LoadField: r0 = r1->field_f
    //     0xb144b8: ldur            w0, [x1, #0xf]
    // 0xb144bc: DecompressPointer r0
    //     0xb144bc: add             x0, x0, HEAP, lsl #32
    // 0xb144c0: LoadField: r2 = r0->field_7
    //     0xb144c0: ldur            x2, [x0, #7]
    // 0xb144c4: cmp             x2, #3
    // 0xb144c8: b.gt            #0xb14544
    // 0xb144cc: cmp             x2, #1
    // 0xb144d0: b.gt            #0xb144fc
    // 0xb144d4: cmp             x2, #0
    // 0xb144d8: b.le            #0xb145b0
    // 0xb144dc: d0 = 2.834646
    //     0xb144dc: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e368] IMM: double(2.834645669291339) from 0x4006ad5ab56ad5ac
    //     0xb144e0: ldr             d0, [x17, #0x368]
    // 0xb144e4: LoadField: d1 = r1->field_7
    //     0xb144e4: ldur            d1, [x1, #7]
    // 0xb144e8: fmul            d2, d1, d0
    // 0xb144ec: mov             v0.16b, v2.16b
    // 0xb144f0: LeaveFrame
    //     0xb144f0: mov             SP, fp
    //     0xb144f4: ldp             fp, lr, [SP], #0x10
    // 0xb144f8: ret
    //     0xb144f8: ret             
    // 0xb144fc: cmp             x2, #2
    // 0xb14500: b.gt            #0xb14524
    // 0xb14504: d0 = 28.346457
    //     0xb14504: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e370] IMM: double(28.346456692913385) from 0x403c58b162c58b16
    //     0xb14508: ldr             d0, [x17, #0x370]
    // 0xb1450c: LoadField: d1 = r1->field_7
    //     0xb1450c: ldur            d1, [x1, #7]
    // 0xb14510: fmul            d2, d1, d0
    // 0xb14514: mov             v0.16b, v2.16b
    // 0xb14518: LeaveFrame
    //     0xb14518: mov             SP, fp
    //     0xb1451c: ldp             fp, lr, [SP], #0x10
    // 0xb14520: ret
    //     0xb14520: ret             
    // 0xb14524: d0 = 72.000000
    //     0xb14524: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e378] IMM: double(72) from 0x4052000000000000
    //     0xb14528: ldr             d0, [x17, #0x378]
    // 0xb1452c: LoadField: d1 = r1->field_7
    //     0xb1452c: ldur            d1, [x1, #7]
    // 0xb14530: fmul            d2, d1, d0
    // 0xb14534: mov             v0.16b, v2.16b
    // 0xb14538: LeaveFrame
    //     0xb14538: mov             SP, fp
    //     0xb1453c: ldp             fp, lr, [SP], #0x10
    // 0xb14540: ret
    //     0xb14540: ret             
    // 0xb14544: cmp             x2, #5
    // 0xb14548: b.gt            #0xb145b0
    // 0xb1454c: cmp             x2, #4
    // 0xb14550: b.gt            #0xb14598
    // 0xb14554: LoadField: d0 = r1->field_7
    //     0xb14554: ldur            d0, [x1, #7]
    // 0xb14558: stur            d0, [fp, #-8]
    // 0xb1455c: LoadField: r0 = r1->field_13
    //     0xb1455c: ldur            w0, [x1, #0x13]
    // 0xb14560: DecompressPointer r0
    //     0xb14560: add             x0, x0, HEAP, lsl #32
    // 0xb14564: cmp             w0, NULL
    // 0xb14568: b.eq            #0xb145c8
    // 0xb1456c: LoadField: r1 = r0->field_37
    //     0xb1456c: ldur            w1, [x0, #0x37]
    // 0xb14570: DecompressPointer r1
    //     0xb14570: add             x1, x1, HEAP, lsl #32
    // 0xb14574: cmp             w1, NULL
    // 0xb14578: b.eq            #0xb145cc
    // 0xb1457c: r0 = sizeValue()
    //     0xb1457c: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xb14580: ldur            d1, [fp, #-8]
    // 0xb14584: fmul            d2, d1, d0
    // 0xb14588: mov             v0.16b, v2.16b
    // 0xb1458c: LeaveFrame
    //     0xb1458c: mov             SP, fp
    //     0xb14590: ldp             fp, lr, [SP], #0x10
    // 0xb14594: ret
    //     0xb14594: ret             
    // 0xb14598: d1 = 100.000000
    //     0xb14598: ldr             d1, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xb1459c: LoadField: d2 = r1->field_7
    //     0xb1459c: ldur            d2, [x1, #7]
    // 0xb145a0: fdiv            d0, d2, d1
    // 0xb145a4: LeaveFrame
    //     0xb145a4: mov             SP, fp
    //     0xb145a8: ldp             fp, lr, [SP], #0x10
    // 0xb145ac: ret
    //     0xb145ac: ret             
    // 0xb145b0: LoadField: d0 = r1->field_7
    //     0xb145b0: ldur            d0, [x1, #7]
    // 0xb145b4: LeaveFrame
    //     0xb145b4: mov             SP, fp
    //     0xb145b8: ldp             fp, lr, [SP], #0x10
    // 0xb145bc: ret
    //     0xb145bc: ret             
    // 0xb145c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb145c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb145c4: b               #0xb144b8
    // 0xb145c8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb145c8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xb145cc: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb145cc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  factory _ SvgNumeric(/* No info */) {
    // ** addr: 0xb146a4, size: 0xe8
    // 0xb146a4: EnterFrame
    //     0xb146a4: stp             fp, lr, [SP, #-0x10]!
    //     0xb146a8: mov             fp, SP
    // 0xb146ac: AllocStack(0x48)
    //     0xb146ac: sub             SP, SP, #0x48
    // 0xb146b0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xb146b0: stur            x2, [fp, #-8]
    //     0xb146b4: stur            x3, [fp, #-0x10]
    // 0xb146b8: CheckStackOverflow
    //     0xb146b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb146bc: cmp             SP, x16
    //     0xb146c0: b.ls            #0xb1477c
    // 0xb146c4: r16 = "([-+]\?[\\d\\.]+)\\s*(px|pt|em|cm|mm|in|%|)"
    //     0xb146c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e398] "([-+]\?[\\d\\.]+)\\s*(px|pt|em|cm|mm|in|%|)"
    //     0xb146c8: ldr             x16, [x16, #0x398]
    // 0xb146cc: stp             x16, NULL, [SP, #0x20]
    // 0xb146d0: r16 = false
    //     0xb146d0: add             x16, NULL, #0x30  ; false
    // 0xb146d4: r30 = true
    //     0xb146d4: add             lr, NULL, #0x20  ; true
    // 0xb146d8: stp             lr, x16, [SP, #0x10]
    // 0xb146dc: r16 = false
    //     0xb146dc: add             x16, NULL, #0x30  ; false
    // 0xb146e0: r30 = false
    //     0xb146e0: add             lr, NULL, #0x30  ; false
    // 0xb146e4: stp             lr, x16, [SP]
    // 0xb146e8: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb146e8: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb146ec: r0 = _RegExp()
    //     0xb146ec: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xb146f0: mov             x1, x0
    // 0xb146f4: ldur            x2, [fp, #-8]
    // 0xb146f8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb146f8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb146fc: r0 = allMatches()
    //     0xb146fc: bl              #0xebde88  ; [dart:core] _RegExp::allMatches
    // 0xb14700: mov             x1, x0
    // 0xb14704: r0 = first()
    //     0xb14704: bl              #0x8939e0  ; [dart:core] Iterable::first
    // 0xb14708: mov             x1, x0
    // 0xb1470c: r2 = 1
    //     0xb1470c: movz            x2, #0x1
    // 0xb14710: stur            x0, [fp, #-8]
    // 0xb14714: r0 = group()
    //     0xb14714: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0xb14718: cmp             w0, NULL
    // 0xb1471c: b.eq            #0xb14784
    // 0xb14720: mov             x1, x0
    // 0xb14724: r0 = parse()
    //     0xb14724: bl              #0x61ca34  ; [dart:core] double::parse
    // 0xb14728: ldur            x1, [fp, #-8]
    // 0xb1472c: r2 = 2
    //     0xb1472c: movz            x2, #0x2
    // 0xb14730: stur            d0, [fp, #-0x18]
    // 0xb14734: r0 = group()
    //     0xb14734: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0xb14738: mov             x2, x0
    // 0xb1473c: r1 = _ConstMap len:8
    //     0xb1473c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e3a0] Map<String, SvgUnit>(8)
    //     0xb14740: ldr             x1, [x1, #0x3a0]
    // 0xb14744: r0 = []()
    //     0xb14744: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb14748: stur            x0, [fp, #-8]
    // 0xb1474c: cmp             w0, NULL
    // 0xb14750: b.eq            #0xb14788
    // 0xb14754: r0 = SvgNumeric()
    //     0xb14754: bl              #0xb1478c  ; AllocateSvgNumericStub -> SvgNumeric (size=0x18)
    // 0xb14758: ldur            d0, [fp, #-0x18]
    // 0xb1475c: StoreField: r0->field_7 = d0
    //     0xb1475c: stur            d0, [x0, #7]
    // 0xb14760: ldur            x1, [fp, #-0x10]
    // 0xb14764: StoreField: r0->field_13 = r1
    //     0xb14764: stur            w1, [x0, #0x13]
    // 0xb14768: ldur            x1, [fp, #-8]
    // 0xb1476c: StoreField: r0->field_f = r1
    //     0xb1476c: stur            w1, [x0, #0xf]
    // 0xb14770: LeaveFrame
    //     0xb14770: mov             SP, fp
    //     0xb14774: ldp             fp, lr, [SP], #0x10
    // 0xb14778: ret
    //     0xb14778: ret             
    // 0xb1477c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1477c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14780: b               #0xb146c4
    // 0xb14784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14784: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14788: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14788: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ colorValue(/* No info */) {
    // ** addr: 0xe765b0, size: 0x160
    // 0xe765b0: EnterFrame
    //     0xe765b0: stp             fp, lr, [SP, #-0x10]!
    //     0xe765b4: mov             fp, SP
    // 0xe765b8: AllocStack(0x18)
    //     0xe765b8: sub             SP, SP, #0x18
    // 0xe765bc: SetupParameters(SvgNumeric this /* r1 => r3, fp-0x10 */)
    //     0xe765bc: mov             x3, x1
    //     0xe765c0: stur            x1, [fp, #-0x10]
    // 0xe765c4: CheckStackOverflow
    //     0xe765c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe765c8: cmp             SP, x16
    //     0xe765cc: b.ls            #0xe766ec
    // 0xe765d0: LoadField: r4 = r3->field_f
    //     0xe765d0: ldur            w4, [x3, #0xf]
    // 0xe765d4: DecompressPointer r4
    //     0xe765d4: add             x4, x4, HEAP, lsl #32
    // 0xe765d8: stur            x4, [fp, #-8]
    // 0xe765dc: LoadField: r2 = r4->field_7
    //     0xe765dc: ldur            x2, [x4, #7]
    // 0xe765e0: cmp             x2, #5
    // 0xe765e4: b.gt            #0xe76620
    // 0xe765e8: r0 = BoxInt64Instr(r2)
    //     0xe765e8: sbfiz           x0, x2, #1, #0x1f
    //     0xe765ec: cmp             x2, x0, asr #1
    //     0xe765f0: b.eq            #0xe765fc
    //     0xe765f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe765f8: stur            x2, [x0, #7]
    // 0xe765fc: cmp             w0, #0xa
    // 0xe76600: b.ne            #0xe76660
    // 0xe76604: d0 = 100.000000
    //     0xe76604: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0xe76608: LoadField: d1 = r3->field_7
    //     0xe76608: ldur            d1, [x3, #7]
    // 0xe7660c: fdiv            d2, d1, d0
    // 0xe76610: mov             v0.16b, v2.16b
    // 0xe76614: LeaveFrame
    //     0xe76614: mov             SP, fp
    //     0xe76618: ldp             fp, lr, [SP], #0x10
    // 0xe7661c: ret
    //     0xe7661c: ret             
    // 0xe76620: cmp             x2, #7
    // 0xe76624: b.lt            #0xe76660
    // 0xe76628: r0 = BoxInt64Instr(r2)
    //     0xe76628: sbfiz           x0, x2, #1, #0x1f
    //     0xe7662c: cmp             x2, x0, asr #1
    //     0xe76630: b.eq            #0xe7663c
    //     0xe76634: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe76638: stur            x2, [x0, #7]
    // 0xe7663c: cmp             w0, #0xe
    // 0xe76640: b.ne            #0xe76660
    // 0xe76644: d0 = 255.000000
    //     0xe76644: ldr             d0, [PP, #0x2b20]  ; [pp+0x2b20] IMM: double(255) from 0x406fe00000000000
    // 0xe76648: LoadField: d1 = r3->field_7
    //     0xe76648: ldur            d1, [x3, #7]
    // 0xe7664c: fdiv            d2, d1, d0
    // 0xe76650: mov             v0.16b, v2.16b
    // 0xe76654: LeaveFrame
    //     0xe76654: mov             SP, fp
    //     0xe76658: ldp             fp, lr, [SP], #0x10
    // 0xe7665c: ret
    //     0xe7665c: ret             
    // 0xe76660: r1 = Null
    //     0xe76660: mov             x1, NULL
    // 0xe76664: r2 = 10
    //     0xe76664: movz            x2, #0xa
    // 0xe76668: r0 = AllocateArray()
    //     0xe76668: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7666c: r16 = "Invalid color value "
    //     0xe7666c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed68] "Invalid color value "
    //     0xe76670: ldr             x16, [x16, #0xd68]
    // 0xe76674: StoreField: r0->field_f = r16
    //     0xe76674: stur            w16, [x0, #0xf]
    // 0xe76678: ldur            x1, [fp, #-0x10]
    // 0xe7667c: LoadField: d0 = r1->field_7
    //     0xe7667c: ldur            d0, [x1, #7]
    // 0xe76680: r1 = inline_Allocate_Double()
    //     0xe76680: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe76684: add             x1, x1, #0x10
    //     0xe76688: cmp             x2, x1
    //     0xe7668c: b.ls            #0xe766f4
    //     0xe76690: str             x1, [THR, #0x50]  ; THR::top
    //     0xe76694: sub             x1, x1, #0xf
    //     0xe76698: movz            x2, #0xe15c
    //     0xe7669c: movk            x2, #0x3, lsl #16
    //     0xe766a0: stur            x2, [x1, #-1]
    // 0xe766a4: StoreField: r1->field_7 = d0
    //     0xe766a4: stur            d0, [x1, #7]
    // 0xe766a8: StoreField: r0->field_13 = r1
    //     0xe766a8: stur            w1, [x0, #0x13]
    // 0xe766ac: r16 = " ("
    //     0xe766ac: ldr             x16, [PP, #0x980]  ; [pp+0x980] " ("
    // 0xe766b0: ArrayStore: r0[0] = r16  ; List_4
    //     0xe766b0: stur            w16, [x0, #0x17]
    // 0xe766b4: ldur            x1, [fp, #-8]
    // 0xe766b8: StoreField: r0->field_1b = r1
    //     0xe766b8: stur            w1, [x0, #0x1b]
    // 0xe766bc: r16 = ")"
    //     0xe766bc: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xe766c0: StoreField: r0->field_1f = r16
    //     0xe766c0: stur            w16, [x0, #0x1f]
    // 0xe766c4: str             x0, [SP]
    // 0xe766c8: r0 = _interpolate()
    //     0xe766c8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe766cc: stur            x0, [fp, #-8]
    // 0xe766d0: r0 = _Exception()
    //     0xe766d0: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xe766d4: mov             x1, x0
    // 0xe766d8: ldur            x0, [fp, #-8]
    // 0xe766dc: StoreField: r1->field_7 = r0
    //     0xe766dc: stur            w0, [x1, #7]
    // 0xe766e0: mov             x0, x1
    // 0xe766e4: r0 = Throw()
    //     0xe766e4: bl              #0xec04b8  ; ThrowStub
    // 0xe766e8: brk             #0
    // 0xe766ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe766ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe766f0: b               #0xe765d0
    // 0xe766f4: SaveReg d0
    //     0xe766f4: str             q0, [SP, #-0x10]!
    // 0xe766f8: SaveReg r0
    //     0xe766f8: str             x0, [SP, #-8]!
    // 0xe766fc: r0 = AllocateDouble()
    //     0xe766fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe76700: mov             x1, x0
    // 0xe76704: RestoreReg r0
    //     0xe76704: ldr             x0, [SP], #8
    // 0xe76708: RestoreReg d0
    //     0xe76708: ldr             q0, [SP], #0x10
    // 0xe7670c: b               #0xe766a4
  }
}

// class id: 839, size: 0x14, field offset: 0x8
class SvgParser extends Object {

  static late final RegExp _transformParameterRegExp; // offset: 0x16ec

  factory _ SvgParser(/* No info */) {
    // ** addr: 0xb13ebc, size: 0x464
    // 0xb13ebc: EnterFrame
    //     0xb13ebc: stp             fp, lr, [SP, #-0x10]!
    //     0xb13ec0: mov             fp, SP
    // 0xb13ec4: AllocStack(0x48)
    //     0xb13ec4: sub             SP, SP, #0x48
    // 0xb13ec8: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0xb13ec8: mov             x0, x1
    //     0xb13ecc: mov             x1, x2
    // 0xb13ed0: CheckStackOverflow
    //     0xb13ed0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb13ed4: cmp             SP, x16
    //     0xb13ed8: b.ls            #0xb142a8
    // 0xb13edc: r0 = rootElement()
    //     0xb13edc: bl              #0xb15058  ; [package:xml/src/xml/nodes/document.dart] XmlDocument::rootElement
    // 0xb13ee0: mov             x1, x0
    // 0xb13ee4: r2 = "viewBox"
    //     0xb13ee4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26480] "viewBox"
    //     0xb13ee8: ldr             x2, [x2, #0x480]
    // 0xb13eec: stur            x0, [fp, #-8]
    // 0xb13ef0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb13ef0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb13ef4: r0 = getAttribute()
    //     0xb13ef4: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xb13ef8: ldur            x1, [fp, #-8]
    // 0xb13efc: r2 = "width"
    //     0xb13efc: ldr             x2, [PP, #0x7198]  ; [pp+0x7198] "width"
    // 0xb13f00: r3 = Null
    //     0xb13f00: mov             x3, NULL
    // 0xb13f04: stur            x0, [fp, #-0x10]
    // 0xb13f08: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb13f08: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb13f0c: r0 = getNumeric()
    //     0xb13f0c: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xb13f10: cmp             w0, NULL
    // 0xb13f14: b.ne            #0xb13f20
    // 0xb13f18: r0 = Null
    //     0xb13f18: mov             x0, NULL
    // 0xb13f1c: b               #0xb13f50
    // 0xb13f20: mov             x1, x0
    // 0xb13f24: r0 = sizeValue()
    //     0xb13f24: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xb13f28: r0 = inline_Allocate_Double()
    //     0xb13f28: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb13f2c: add             x0, x0, #0x10
    //     0xb13f30: cmp             x1, x0
    //     0xb13f34: b.ls            #0xb142b0
    //     0xb13f38: str             x0, [THR, #0x50]  ; THR::top
    //     0xb13f3c: sub             x0, x0, #0xf
    //     0xb13f40: movz            x1, #0xe15c
    //     0xb13f44: movk            x1, #0x3, lsl #16
    //     0xb13f48: stur            x1, [x0, #-1]
    // 0xb13f4c: StoreField: r0->field_7 = d0
    //     0xb13f4c: stur            d0, [x0, #7]
    // 0xb13f50: ldur            x1, [fp, #-8]
    // 0xb13f54: stur            x0, [fp, #-0x18]
    // 0xb13f58: r2 = "height"
    //     0xb13f58: ldr             x2, [PP, #0x4778]  ; [pp+0x4778] "height"
    // 0xb13f5c: r3 = Null
    //     0xb13f5c: mov             x3, NULL
    // 0xb13f60: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb13f60: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb13f64: r0 = getNumeric()
    //     0xb13f64: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xb13f68: cmp             w0, NULL
    // 0xb13f6c: b.ne            #0xb13f78
    // 0xb13f70: r0 = Null
    //     0xb13f70: mov             x0, NULL
    // 0xb13f74: b               #0xb13fa8
    // 0xb13f78: mov             x1, x0
    // 0xb13f7c: r0 = sizeValue()
    //     0xb13f7c: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xb13f80: r0 = inline_Allocate_Double()
    //     0xb13f80: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb13f84: add             x0, x0, #0x10
    //     0xb13f88: cmp             x1, x0
    //     0xb13f8c: b.ls            #0xb142c0
    //     0xb13f90: str             x0, [THR, #0x50]  ; THR::top
    //     0xb13f94: sub             x0, x0, #0xf
    //     0xb13f98: movz            x1, #0xe15c
    //     0xb13f9c: movk            x1, #0x3, lsl #16
    //     0xb13fa0: stur            x1, [x0, #-1]
    // 0xb13fa4: StoreField: r0->field_7 = d0
    //     0xb13fa4: stur            d0, [x0, #7]
    // 0xb13fa8: ldur            x1, [fp, #-0x10]
    // 0xb13fac: cmp             w1, NULL
    // 0xb13fb0: b.ne            #0xb14098
    // 0xb13fb4: ldur            x1, [fp, #-0x18]
    // 0xb13fb8: cmp             w1, NULL
    // 0xb13fbc: b.ne            #0xb13fcc
    // 0xb13fc0: d0 = 1000.000000
    //     0xb13fc0: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0xb13fc4: ldr             d0, [x17, #0x238]
    // 0xb13fc8: b               #0xb13fd0
    // 0xb13fcc: LoadField: d0 = r1->field_7
    //     0xb13fcc: ldur            d0, [x1, #7]
    // 0xb13fd0: stur            d0, [fp, #-0x40]
    // 0xb13fd4: cmp             w0, NULL
    // 0xb13fd8: b.ne            #0xb13fe8
    // 0xb13fdc: d1 = 1000.000000
    //     0xb13fdc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0xb13fe0: ldr             d1, [x17, #0x238]
    // 0xb13fe4: b               #0xb13fec
    // 0xb13fe8: LoadField: d1 = r0->field_7
    //     0xb13fe8: ldur            d1, [x0, #7]
    // 0xb13fec: r0 = 8
    //     0xb13fec: movz            x0, #0x8
    // 0xb13ff0: mov             x2, x0
    // 0xb13ff4: stur            d1, [fp, #-0x38]
    // 0xb13ff8: r1 = Null
    //     0xb13ff8: mov             x1, NULL
    // 0xb13ffc: r0 = AllocateArray()
    //     0xb13ffc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb14000: stur            x0, [fp, #-0x18]
    // 0xb14004: r16 = 0.000000
    //     0xb14004: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb14008: StoreField: r0->field_f = r16
    //     0xb14008: stur            w16, [x0, #0xf]
    // 0xb1400c: r16 = 0.000000
    //     0xb1400c: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb14010: StoreField: r0->field_13 = r16
    //     0xb14010: stur            w16, [x0, #0x13]
    // 0xb14014: ldur            d0, [fp, #-0x40]
    // 0xb14018: r1 = inline_Allocate_Double()
    //     0xb14018: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb1401c: add             x1, x1, #0x10
    //     0xb14020: cmp             x2, x1
    //     0xb14024: b.ls            #0xb142d0
    //     0xb14028: str             x1, [THR, #0x50]  ; THR::top
    //     0xb1402c: sub             x1, x1, #0xf
    //     0xb14030: movz            x2, #0xe15c
    //     0xb14034: movk            x2, #0x3, lsl #16
    //     0xb14038: stur            x2, [x1, #-1]
    // 0xb1403c: StoreField: r1->field_7 = d0
    //     0xb1403c: stur            d0, [x1, #7]
    // 0xb14040: ArrayStore: r0[0] = r1  ; List_4
    //     0xb14040: stur            w1, [x0, #0x17]
    // 0xb14044: ldur            d0, [fp, #-0x38]
    // 0xb14048: r1 = inline_Allocate_Double()
    //     0xb14048: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb1404c: add             x1, x1, #0x10
    //     0xb14050: cmp             x2, x1
    //     0xb14054: b.ls            #0xb142ec
    //     0xb14058: str             x1, [THR, #0x50]  ; THR::top
    //     0xb1405c: sub             x1, x1, #0xf
    //     0xb14060: movz            x2, #0xe15c
    //     0xb14064: movk            x2, #0x3, lsl #16
    //     0xb14068: stur            x2, [x1, #-1]
    // 0xb1406c: StoreField: r1->field_7 = d0
    //     0xb1406c: stur            d0, [x1, #7]
    // 0xb14070: StoreField: r0->field_1b = r1
    //     0xb14070: stur            w1, [x0, #0x1b]
    // 0xb14074: r1 = <double>
    //     0xb14074: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xb14078: r0 = AllocateGrowableArray()
    //     0xb14078: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb1407c: mov             x1, x0
    // 0xb14080: ldur            x0, [fp, #-0x18]
    // 0xb14084: StoreField: r1->field_f = r0
    //     0xb14084: stur            w0, [x1, #0xf]
    // 0xb14088: r0 = 8
    //     0xb14088: movz            x0, #0x8
    // 0xb1408c: StoreField: r1->field_b = r0
    //     0xb1408c: stur            w0, [x1, #0xb]
    // 0xb14090: mov             x2, x1
    // 0xb14094: b               #0xb140a0
    // 0xb14098: r0 = splitDoubles()
    //     0xb14098: bl              #0xb14338  ; [package:pdf/src/svg/parser.dart] SvgParser::splitDoubles
    // 0xb1409c: mov             x2, x0
    // 0xb140a0: stur            x2, [fp, #-0x10]
    // 0xb140a4: r0 = LoadClassIdInstr(r2)
    //     0xb140a4: ldur            x0, [x2, #-1]
    //     0xb140a8: ubfx            x0, x0, #0xc, #0x14
    // 0xb140ac: mov             x1, x2
    // 0xb140b0: r0 = GDT[cid_x0 + 0xe879]()
    //     0xb140b0: movz            x17, #0xe879
    //     0xb140b4: add             lr, x0, x17
    //     0xb140b8: ldr             lr, [x21, lr, lsl #3]
    //     0xb140bc: blr             lr
    // 0xb140c0: tbz             w0, #4, #0xb14288
    // 0xb140c4: ldur            x2, [fp, #-0x10]
    // 0xb140c8: r0 = LoadClassIdInstr(r2)
    //     0xb140c8: ldur            x0, [x2, #-1]
    //     0xb140cc: ubfx            x0, x0, #0xc, #0x14
    // 0xb140d0: str             x2, [SP]
    // 0xb140d4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb140d4: movz            x17, #0xc834
    //     0xb140d8: add             lr, x0, x17
    //     0xb140dc: ldr             lr, [x21, lr, lsl #3]
    //     0xb140e0: blr             lr
    // 0xb140e4: r1 = LoadInt32Instr(r0)
    //     0xb140e4: sbfx            x1, x0, #1, #0x1f
    //     0xb140e8: tbz             w0, #0, #0xb140f0
    //     0xb140ec: ldur            x1, [x0, #7]
    // 0xb140f0: cmp             x1, #4
    // 0xb140f4: b.gt            #0xb14288
    // 0xb140f8: ldur            x2, [fp, #-0x10]
    // 0xb140fc: r0 = LoadClassIdInstr(r2)
    //     0xb140fc: ldur            x0, [x2, #-1]
    //     0xb14100: ubfx            x0, x0, #0xc, #0x14
    // 0xb14104: str             x2, [SP]
    // 0xb14108: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb14108: movz            x17, #0xc834
    //     0xb1410c: add             lr, x0, x17
    //     0xb14110: ldr             lr, [x21, lr, lsl #3]
    //     0xb14114: blr             lr
    // 0xb14118: r1 = LoadInt32Instr(r0)
    //     0xb14118: sbfx            x1, x0, #1, #0x1f
    //     0xb1411c: tbz             w0, #0, #0xb14124
    //     0xb14120: ldur            x1, [x0, #7]
    // 0xb14124: r0 = 4
    //     0xb14124: movz            x0, #0x4
    // 0xb14128: sub             x3, x0, x1
    // 0xb1412c: stur            x3, [fp, #-0x20]
    // 0xb14130: r0 = BoxInt64Instr(r3)
    //     0xb14130: sbfiz           x0, x3, #1, #0x1f
    //     0xb14134: cmp             x3, x0, asr #1
    //     0xb14138: b.eq            #0xb14144
    //     0xb1413c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb14140: stur            x3, [x0, #7]
    // 0xb14144: mov             x2, x0
    // 0xb14148: r1 = <double>
    //     0xb14148: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xb1414c: r0 = AllocateArray()
    //     0xb1414c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb14150: ldur            x1, [fp, #-0x20]
    // 0xb14154: r2 = 0
    //     0xb14154: movz            x2, #0
    // 0xb14158: CheckStackOverflow
    //     0xb14158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1415c: cmp             SP, x16
    //     0xb14160: b.ls            #0xb14308
    // 0xb14164: cmp             x2, x1
    // 0xb14168: b.ge            #0xb14184
    // 0xb1416c: add             x3, x0, x2, lsl #2
    // 0xb14170: r16 = 0.000000
    //     0xb14170: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb14174: StoreField: r3->field_f = r16
    //     0xb14174: stur            w16, [x3, #0xf]
    // 0xb14178: add             x3, x2, #1
    // 0xb1417c: mov             x2, x3
    // 0xb14180: b               #0xb14158
    // 0xb14184: ldur            x3, [fp, #-8]
    // 0xb14188: mov             x2, x0
    // 0xb1418c: r1 = <double>
    //     0xb1418c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xb14190: r0 = _GrowableList._ofArray()
    //     0xb14190: bl              #0x60ba94  ; [dart:core] _GrowableList::_GrowableList._ofArray
    // 0xb14194: mov             x1, x0
    // 0xb14198: ldur            x2, [fp, #-0x10]
    // 0xb1419c: stur            x0, [fp, #-0x10]
    // 0xb141a0: r0 = addAll()
    //     0xb141a0: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb141a4: ldur            x2, [fp, #-0x10]
    // 0xb141a8: LoadField: r0 = r2->field_b
    //     0xb141a8: ldur            w0, [x2, #0xb]
    // 0xb141ac: r3 = LoadInt32Instr(r0)
    //     0xb141ac: sbfx            x3, x0, #1, #0x1f
    // 0xb141b0: mov             x0, x3
    // 0xb141b4: r1 = 0
    //     0xb141b4: movz            x1, #0
    // 0xb141b8: cmp             x1, x0
    // 0xb141bc: b.hs            #0xb14310
    // 0xb141c0: LoadField: r4 = r2->field_f
    //     0xb141c0: ldur            w4, [x2, #0xf]
    // 0xb141c4: DecompressPointer r4
    //     0xb141c4: add             x4, x4, HEAP, lsl #32
    // 0xb141c8: LoadField: r2 = r4->field_f
    //     0xb141c8: ldur            w2, [x4, #0xf]
    // 0xb141cc: DecompressPointer r2
    //     0xb141cc: add             x2, x2, HEAP, lsl #32
    // 0xb141d0: mov             x0, x3
    // 0xb141d4: r1 = 1
    //     0xb141d4: movz            x1, #0x1
    // 0xb141d8: cmp             x1, x0
    // 0xb141dc: b.hs            #0xb14314
    // 0xb141e0: LoadField: r5 = r4->field_13
    //     0xb141e0: ldur            w5, [x4, #0x13]
    // 0xb141e4: DecompressPointer r5
    //     0xb141e4: add             x5, x5, HEAP, lsl #32
    // 0xb141e8: mov             x0, x3
    // 0xb141ec: stur            x5, [fp, #-0x28]
    // 0xb141f0: r1 = 2
    //     0xb141f0: movz            x1, #0x2
    // 0xb141f4: cmp             x1, x0
    // 0xb141f8: b.hs            #0xb14318
    // 0xb141fc: ArrayLoad: r6 = r4[0]  ; List_4
    //     0xb141fc: ldur            w6, [x4, #0x17]
    // 0xb14200: DecompressPointer r6
    //     0xb14200: add             x6, x6, HEAP, lsl #32
    // 0xb14204: mov             x0, x3
    // 0xb14208: stur            x6, [fp, #-0x18]
    // 0xb1420c: r1 = 3
    //     0xb1420c: movz            x1, #0x3
    // 0xb14210: cmp             x1, x0
    // 0xb14214: b.hs            #0xb1431c
    // 0xb14218: LoadField: r0 = r4->field_1b
    //     0xb14218: ldur            w0, [x4, #0x1b]
    // 0xb1421c: DecompressPointer r0
    //     0xb1421c: add             x0, x0, HEAP, lsl #32
    // 0xb14220: stur            x0, [fp, #-0x10]
    // 0xb14224: LoadField: d0 = r2->field_7
    //     0xb14224: ldur            d0, [x2, #7]
    // 0xb14228: stur            d0, [fp, #-0x38]
    // 0xb1422c: r0 = PdfRect()
    //     0xb1422c: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xb14230: ldur            d0, [fp, #-0x38]
    // 0xb14234: stur            x0, [fp, #-0x30]
    // 0xb14238: StoreField: r0->field_7 = d0
    //     0xb14238: stur            d0, [x0, #7]
    // 0xb1423c: ldur            x1, [fp, #-0x28]
    // 0xb14240: LoadField: d0 = r1->field_7
    //     0xb14240: ldur            d0, [x1, #7]
    // 0xb14244: StoreField: r0->field_f = d0
    //     0xb14244: stur            d0, [x0, #0xf]
    // 0xb14248: ldur            x1, [fp, #-0x18]
    // 0xb1424c: LoadField: d0 = r1->field_7
    //     0xb1424c: ldur            d0, [x1, #7]
    // 0xb14250: ArrayStore: r0[0] = d0  ; List_8
    //     0xb14250: stur            d0, [x0, #0x17]
    // 0xb14254: ldur            x1, [fp, #-0x10]
    // 0xb14258: LoadField: d0 = r1->field_7
    //     0xb14258: ldur            d0, [x1, #7]
    // 0xb1425c: StoreField: r0->field_1f = d0
    //     0xb1425c: stur            d0, [x0, #0x1f]
    // 0xb14260: r0 = SvgParser()
    //     0xb14260: bl              #0xb14320  ; AllocateSvgParserStub -> SvgParser (size=0x14)
    // 0xb14264: mov             x1, x0
    // 0xb14268: ldur            x0, [fp, #-0x30]
    // 0xb1426c: StoreField: r1->field_7 = r0
    //     0xb1426c: stur            w0, [x1, #7]
    // 0xb14270: ldur            x0, [fp, #-8]
    // 0xb14274: StoreField: r1->field_b = r0
    //     0xb14274: stur            w0, [x1, #0xb]
    // 0xb14278: mov             x0, x1
    // 0xb1427c: LeaveFrame
    //     0xb1427c: mov             SP, fp
    //     0xb14280: ldp             fp, lr, [SP], #0x10
    // 0xb14284: ret
    //     0xb14284: ret             
    // 0xb14288: r0 = _Exception()
    //     0xb14288: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xb1428c: mov             x1, x0
    // 0xb14290: r0 = "viewBox must contain 1..4 parameters"
    //     0xb14290: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e348] "viewBox must contain 1..4 parameters"
    //     0xb14294: ldr             x0, [x0, #0x348]
    // 0xb14298: StoreField: r1->field_7 = r0
    //     0xb14298: stur            w0, [x1, #7]
    // 0xb1429c: mov             x0, x1
    // 0xb142a0: r0 = Throw()
    //     0xb142a0: bl              #0xec04b8  ; ThrowStub
    // 0xb142a4: brk             #0
    // 0xb142a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb142a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb142ac: b               #0xb13edc
    // 0xb142b0: SaveReg d0
    //     0xb142b0: str             q0, [SP, #-0x10]!
    // 0xb142b4: r0 = AllocateDouble()
    //     0xb142b4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb142b8: RestoreReg d0
    //     0xb142b8: ldr             q0, [SP], #0x10
    // 0xb142bc: b               #0xb13f4c
    // 0xb142c0: SaveReg d0
    //     0xb142c0: str             q0, [SP, #-0x10]!
    // 0xb142c4: r0 = AllocateDouble()
    //     0xb142c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb142c8: RestoreReg d0
    //     0xb142c8: ldr             q0, [SP], #0x10
    // 0xb142cc: b               #0xb13fa4
    // 0xb142d0: SaveReg d0
    //     0xb142d0: str             q0, [SP, #-0x10]!
    // 0xb142d4: SaveReg r0
    //     0xb142d4: str             x0, [SP, #-8]!
    // 0xb142d8: r0 = AllocateDouble()
    //     0xb142d8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb142dc: mov             x1, x0
    // 0xb142e0: RestoreReg r0
    //     0xb142e0: ldr             x0, [SP], #8
    // 0xb142e4: RestoreReg d0
    //     0xb142e4: ldr             q0, [SP], #0x10
    // 0xb142e8: b               #0xb1403c
    // 0xb142ec: SaveReg d0
    //     0xb142ec: str             q0, [SP, #-0x10]!
    // 0xb142f0: SaveReg r0
    //     0xb142f0: str             x0, [SP, #-8]!
    // 0xb142f4: r0 = AllocateDouble()
    //     0xb142f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb142f8: mov             x1, x0
    // 0xb142fc: RestoreReg r0
    //     0xb142fc: ldr             x0, [SP], #8
    // 0xb14300: RestoreReg d0
    //     0xb14300: ldr             q0, [SP], #0x10
    // 0xb14304: b               #0xb1406c
    // 0xb14308: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14308: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1430c: b               #0xb14164
    // 0xb14310: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb14310: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb14314: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb14314: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb14318: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb14318: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb1431c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb1431c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ splitDoubles(/* No info */) {
    // ** addr: 0xb14338, size: 0x90
    // 0xb14338: EnterFrame
    //     0xb14338: stp             fp, lr, [SP, #-0x10]!
    //     0xb1433c: mov             fp, SP
    // 0xb14340: AllocStack(0x20)
    //     0xb14340: sub             SP, SP, #0x20
    // 0xb14344: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xb14344: mov             x2, x1
    //     0xb14348: stur            x1, [fp, #-8]
    // 0xb1434c: CheckStackOverflow
    //     0xb1434c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14350: cmp             SP, x16
    //     0xb14354: b.ls            #0xb143c0
    // 0xb14358: r0 = InitLateStaticField(0x16ec) // [package:pdf/src/svg/parser.dart] SvgParser::_transformParameterRegExp
    //     0xb14358: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1435c: ldr             x0, [x0, #0x2dd8]
    //     0xb14360: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb14364: cmp             w0, w16
    //     0xb14368: b.ne            #0xb14378
    //     0xb1436c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e350] Field <SvgParser._transformParameterRegExp@2593390522>: static late final (offset: 0x16ec)
    //     0xb14370: ldr             x2, [x2, #0x350]
    //     0xb14374: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb14378: mov             x1, x0
    // 0xb1437c: ldur            x2, [fp, #-8]
    // 0xb14380: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb14380: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb14384: r0 = allMatches()
    //     0xb14384: bl              #0xebde88  ; [dart:core] _RegExp::allMatches
    // 0xb14388: r1 = Function '<anonymous closure>': static.
    //     0xb14388: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e358] AnonymousClosure: static (0xb143c8), in [package:pdf/src/svg/parser.dart] SvgParser::splitDoubles (0xb14338)
    //     0xb1438c: ldr             x1, [x1, #0x358]
    // 0xb14390: r2 = Null
    //     0xb14390: mov             x2, NULL
    // 0xb14394: stur            x0, [fp, #-8]
    // 0xb14398: r0 = AllocateClosure()
    //     0xb14398: bl              #0xec1630  ; AllocateClosureStub
    // 0xb1439c: r16 = <double>
    //     0xb1439c: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xb143a0: ldur            lr, [fp, #-8]
    // 0xb143a4: stp             lr, x16, [SP, #8]
    // 0xb143a8: str             x0, [SP]
    // 0xb143ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb143ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb143b0: r0 = map()
    //     0xb143b0: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xb143b4: LeaveFrame
    //     0xb143b4: mov             SP, fp
    //     0xb143b8: ldp             fp, lr, [SP], #0x10
    // 0xb143bc: ret
    //     0xb143bc: ret             
    // 0xb143c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb143c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb143c4: b               #0xb14358
  }
  [closure] static double <anonymous closure>(dynamic, RegExpMatch) {
    // ** addr: 0xb143c8, size: 0x80
    // 0xb143c8: EnterFrame
    //     0xb143c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb143cc: mov             fp, SP
    // 0xb143d0: CheckStackOverflow
    //     0xb143d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb143d4: cmp             SP, x16
    //     0xb143d8: b.ls            #0xb1442c
    // 0xb143dc: ldr             x1, [fp, #0x10]
    // 0xb143e0: r2 = 0
    //     0xb143e0: movz            x2, #0
    // 0xb143e4: r0 = group()
    //     0xb143e4: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0xb143e8: cmp             w0, NULL
    // 0xb143ec: b.eq            #0xb14434
    // 0xb143f0: mov             x1, x0
    // 0xb143f4: r0 = parse()
    //     0xb143f4: bl              #0x61ca34  ; [dart:core] double::parse
    // 0xb143f8: r0 = inline_Allocate_Double()
    //     0xb143f8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb143fc: add             x0, x0, #0x10
    //     0xb14400: cmp             x1, x0
    //     0xb14404: b.ls            #0xb14438
    //     0xb14408: str             x0, [THR, #0x50]  ; THR::top
    //     0xb1440c: sub             x0, x0, #0xf
    //     0xb14410: movz            x1, #0xe15c
    //     0xb14414: movk            x1, #0x3, lsl #16
    //     0xb14418: stur            x1, [x0, #-1]
    // 0xb1441c: StoreField: r0->field_7 = d0
    //     0xb1441c: stur            d0, [x0, #7]
    // 0xb14420: LeaveFrame
    //     0xb14420: mov             SP, fp
    //     0xb14424: ldp             fp, lr, [SP], #0x10
    // 0xb14428: ret
    //     0xb14428: ret             
    // 0xb1442c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1442c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14430: b               #0xb143dc
    // 0xb14434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb14434: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb14438: SaveReg d0
    //     0xb14438: str             q0, [SP, #-0x10]!
    // 0xb1443c: r0 = AllocateDouble()
    //     0xb1443c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb14440: RestoreReg d0
    //     0xb14440: ldr             q0, [SP], #0x10
    // 0xb14444: b               #0xb1441c
  }
  static RegExp _transformParameterRegExp() {
    // ** addr: 0xb14448, size: 0x58
    // 0xb14448: EnterFrame
    //     0xb14448: stp             fp, lr, [SP, #-0x10]!
    //     0xb1444c: mov             fp, SP
    // 0xb14450: AllocStack(0x30)
    //     0xb14450: sub             SP, SP, #0x30
    // 0xb14454: CheckStackOverflow
    //     0xb14454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14458: cmp             SP, x16
    //     0xb1445c: b.ls            #0xb14498
    // 0xb14460: r16 = "[\\w.-]+(px|pt|em|cm|mm|in|%|)"
    //     0xb14460: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e360] "[\\w.-]+(px|pt|em|cm|mm|in|%|)"
    //     0xb14464: ldr             x16, [x16, #0x360]
    // 0xb14468: stp             x16, NULL, [SP, #0x20]
    // 0xb1446c: r16 = false
    //     0xb1446c: add             x16, NULL, #0x30  ; false
    // 0xb14470: r30 = true
    //     0xb14470: add             lr, NULL, #0x20  ; true
    // 0xb14474: stp             lr, x16, [SP, #0x10]
    // 0xb14478: r16 = false
    //     0xb14478: add             x16, NULL, #0x30  ; false
    // 0xb1447c: r30 = false
    //     0xb1447c: add             lr, NULL, #0x30  ; false
    // 0xb14480: stp             lr, x16, [SP]
    // 0xb14484: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb14484: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb14488: r0 = _RegExp()
    //     0xb14488: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xb1448c: LeaveFrame
    //     0xb1448c: mov             SP, fp
    //     0xb14490: ldp             fp, lr, [SP], #0x10
    // 0xb14494: ret
    //     0xb14494: ret             
    // 0xb14498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14498: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1449c: b               #0xb14460
  }
  static _ getNumeric(/* No info */) {
    // ** addr: 0xb145d0, size: 0xd4
    // 0xb145d0: EnterFrame
    //     0xb145d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb145d4: mov             fp, SP
    // 0xb145d8: AllocStack(0x20)
    //     0xb145d8: sub             SP, SP, #0x20
    // 0xb145dc: SetupParameters(dynamic _ /* r3 => r3, fp-0x10 */, {dynamic defaultValue = Null /* r0, fp-0x8 */})
    //     0xb145dc: stur            x3, [fp, #-0x10]
    //     0xb145e0: ldur            w0, [x4, #0x13]
    //     0xb145e4: ldur            w5, [x4, #0x1f]
    //     0xb145e8: add             x5, x5, HEAP, lsl #32
    //     0xb145ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e380] "defaultValue"
    //     0xb145f0: ldr             x16, [x16, #0x380]
    //     0xb145f4: cmp             w5, w16
    //     0xb145f8: b.ne            #0xb14614
    //     0xb145fc: ldur            w5, [x4, #0x23]
    //     0xb14600: add             x5, x5, HEAP, lsl #32
    //     0xb14604: sub             w4, w0, w5
    //     0xb14608: add             x0, fp, w4, sxtw #2
    //     0xb1460c: ldr             x0, [x0, #8]
    //     0xb14610: b               #0xb14618
    //     0xb14614: mov             x0, NULL
    //     0xb14618: stur            x0, [fp, #-8]
    // 0xb1461c: CheckStackOverflow
    //     0xb1461c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14620: cmp             SP, x16
    //     0xb14624: b.ls            #0xb1469c
    // 0xb14628: str             NULL, [SP]
    // 0xb1462c: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xb1462c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xb14630: ldr             x4, [x4, #0x388]
    // 0xb14634: r0 = getAttribute()
    //     0xb14634: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xb14638: cmp             w0, NULL
    // 0xb1463c: b.ne            #0xb14680
    // 0xb14640: ldur            x0, [fp, #-8]
    // 0xb14644: cmp             w0, NULL
    // 0xb14648: b.ne            #0xb14654
    // 0xb1464c: r0 = Null
    //     0xb1464c: mov             x0, NULL
    // 0xb14650: b               #0xb14674
    // 0xb14654: LoadField: d0 = r0->field_7
    //     0xb14654: ldur            d0, [x0, #7]
    // 0xb14658: stur            d0, [fp, #-0x18]
    // 0xb1465c: r0 = SvgNumeric()
    //     0xb1465c: bl              #0xb1478c  ; AllocateSvgNumericStub -> SvgNumeric (size=0x18)
    // 0xb14660: ldur            d0, [fp, #-0x18]
    // 0xb14664: StoreField: r0->field_7 = d0
    //     0xb14664: stur            d0, [x0, #7]
    // 0xb14668: r1 = Instance_SvgUnit
    //     0xb14668: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e390] Obj!SvgUnit@e2eb01
    //     0xb1466c: ldr             x1, [x1, #0x390]
    // 0xb14670: StoreField: r0->field_f = r1
    //     0xb14670: stur            w1, [x0, #0xf]
    // 0xb14674: LeaveFrame
    //     0xb14674: mov             SP, fp
    //     0xb14678: ldp             fp, lr, [SP], #0x10
    // 0xb1467c: ret
    //     0xb1467c: ret             
    // 0xb14680: mov             x2, x0
    // 0xb14684: ldur            x3, [fp, #-0x10]
    // 0xb14688: r1 = Null
    //     0xb14688: mov             x1, NULL
    // 0xb1468c: r0 = SvgNumeric()
    //     0xb1468c: bl              #0xb146a4  ; [package:pdf/src/svg/parser.dart] SvgNumeric::SvgNumeric
    // 0xb14690: LeaveFrame
    //     0xb14690: mov             SP, fp
    //     0xb14694: ldp             fp, lr, [SP], #0x10
    // 0xb14698: ret
    //     0xb14698: ret             
    // 0xb1469c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1469c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb146a0: b               #0xb14628
  }
  _ findById(/* No info */) {
    // ** addr: 0xe6f138, size: 0xcc
    // 0xe6f138: EnterFrame
    //     0xe6f138: stp             fp, lr, [SP, #-0x10]!
    //     0xe6f13c: mov             fp, SP
    // 0xe6f140: AllocStack(0x60)
    //     0xe6f140: sub             SP, SP, #0x60
    // 0xe6f144: SetupParameters(SvgParser this /* r1 => r1, fp-0x40 */, dynamic _ /* r2 => r2, fp-0x48 */)
    //     0xe6f144: stur            x1, [fp, #-0x40]
    //     0xe6f148: stur            x2, [fp, #-0x48]
    // 0xe6f14c: CheckStackOverflow
    //     0xe6f14c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6f150: cmp             SP, x16
    //     0xe6f154: b.ls            #0xe6f1fc
    // 0xe6f158: r1 = 1
    //     0xe6f158: movz            x1, #0x1
    // 0xe6f15c: r0 = AllocateContext()
    //     0xe6f15c: bl              #0xec126c  ; AllocateContextStub
    // 0xe6f160: mov             x2, x0
    // 0xe6f164: ldur            x0, [fp, #-0x48]
    // 0xe6f168: stur            x2, [fp, #-0x50]
    // 0xe6f16c: StoreField: r2->field_f = r0
    //     0xe6f16c: stur            w0, [x2, #0xf]
    // 0xe6f170: ldur            x0, [fp, #-0x40]
    // 0xe6f174: LoadField: r1 = r0->field_b
    //     0xe6f174: ldur            w1, [x0, #0xb]
    // 0xe6f178: DecompressPointer r1
    //     0xe6f178: add             x1, x1, HEAP, lsl #32
    // 0xe6f17c: r0 = XmlDescendantsExtension.descendants()
    //     0xe6f17c: bl              #0xe6f204  ; [package:xml/src/xml/extensions/descendants.dart] ::XmlDescendantsExtension.descendants
    // 0xe6f180: r16 = <XmlElement>
    //     0xe6f180: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea98] TypeArguments: <XmlElement>
    //     0xe6f184: ldr             x16, [x16, #0xa98]
    // 0xe6f188: stp             x0, x16, [SP]
    // 0xe6f18c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe6f18c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe6f190: r0 = whereType()
    //     0xe6f190: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0xe6f194: ldur            x2, [fp, #-0x50]
    // 0xe6f198: r1 = Function '<anonymous closure>':.
    //     0xe6f198: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb00] AnonymousClosure: (0xe6f244), in [package:pdf/src/svg/parser.dart] SvgParser::findById (0xe6f138)
    //     0xe6f19c: ldr             x1, [x1, #0xb00]
    // 0xe6f1a0: stur            x0, [fp, #-0x40]
    // 0xe6f1a4: r0 = AllocateClosure()
    //     0xe6f1a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe6f1a8: ldur            x1, [fp, #-0x40]
    // 0xe6f1ac: mov             x2, x0
    // 0xe6f1b0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6f1b0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6f1b4: r0 = firstWhere()
    //     0xe6f1b4: bl              #0x7f0038  ; [dart:core] Iterable::firstWhere
    // 0xe6f1b8: LeaveFrame
    //     0xe6f1b8: mov             SP, fp
    //     0xe6f1bc: ldp             fp, lr, [SP], #0x10
    // 0xe6f1c0: ret
    //     0xe6f1c0: ret             
    // 0xe6f1c4: sub             SP, fp, #0x60
    // 0xe6f1c8: r2 = 60
    //     0xe6f1c8: movz            x2, #0x3c
    // 0xe6f1cc: branchIfSmi(r0, 0xe6f1d8)
    //     0xe6f1cc: tbz             w0, #0, #0xe6f1d8
    // 0xe6f1d0: r2 = LoadClassIdInstr(r0)
    //     0xe6f1d0: ldur            x2, [x0, #-1]
    //     0xe6f1d4: ubfx            x2, x2, #0xc, #0x14
    // 0xe6f1d8: r17 = 7356
    //     0xe6f1d8: movz            x17, #0x1cbc
    // 0xe6f1dc: cmp             x2, x17
    // 0xe6f1e0: b.ne            #0xe6f1f4
    // 0xe6f1e4: r0 = Null
    //     0xe6f1e4: mov             x0, NULL
    // 0xe6f1e8: LeaveFrame
    //     0xe6f1e8: mov             SP, fp
    //     0xe6f1ec: ldp             fp, lr, [SP], #0x10
    // 0xe6f1f0: ret
    //     0xe6f1f0: ret             
    // 0xe6f1f4: r0 = ReThrow()
    //     0xe6f1f4: bl              #0xec048c  ; ReThrowStub
    // 0xe6f1f8: brk             #0
    // 0xe6f1fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6f1fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6f200: b               #0xe6f158
  }
  [closure] bool <anonymous closure>(dynamic, XmlElement) {
    // ** addr: 0xe6f244, size: 0x78
    // 0xe6f244: EnterFrame
    //     0xe6f244: stp             fp, lr, [SP, #-0x10]!
    //     0xe6f248: mov             fp, SP
    // 0xe6f24c: AllocStack(0x18)
    //     0xe6f24c: sub             SP, SP, #0x18
    // 0xe6f250: SetupParameters()
    //     0xe6f250: ldr             x0, [fp, #0x18]
    //     0xe6f254: ldur            w3, [x0, #0x17]
    //     0xe6f258: add             x3, x3, HEAP, lsl #32
    //     0xe6f25c: stur            x3, [fp, #-8]
    // 0xe6f260: CheckStackOverflow
    //     0xe6f260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6f264: cmp             SP, x16
    //     0xe6f268: b.ls            #0xe6f2b4
    // 0xe6f26c: ldr             x1, [fp, #0x10]
    // 0xe6f270: r2 = "id"
    //     0xe6f270: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe6f274: ldr             x2, [x2, #0x740]
    // 0xe6f278: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6f278: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6f27c: r0 = getAttribute()
    //     0xe6f27c: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe6f280: mov             x1, x0
    // 0xe6f284: ldur            x0, [fp, #-8]
    // 0xe6f288: LoadField: r2 = r0->field_f
    //     0xe6f288: ldur            w2, [x0, #0xf]
    // 0xe6f28c: DecompressPointer r2
    //     0xe6f28c: add             x2, x2, HEAP, lsl #32
    // 0xe6f290: r0 = LoadClassIdInstr(r1)
    //     0xe6f290: ldur            x0, [x1, #-1]
    //     0xe6f294: ubfx            x0, x0, #0xc, #0x14
    // 0xe6f298: stp             x2, x1, [SP]
    // 0xe6f29c: mov             lr, x0
    // 0xe6f2a0: ldr             lr, [x21, lr, lsl #3]
    // 0xe6f2a4: blr             lr
    // 0xe6f2a8: LeaveFrame
    //     0xe6f2a8: mov             SP, fp
    //     0xe6f2ac: ldp             fp, lr, [SP], #0x10
    // 0xe6f2b0: ret
    //     0xe6f2b0: ret             
    // 0xe6f2b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6f2b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6f2b8: b               #0xe6f26c
  }
  static _ splitNumeric(/* No info */) {
    // ** addr: 0xe73f7c, size: 0xb0
    // 0xe73f7c: EnterFrame
    //     0xe73f7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe73f80: mov             fp, SP
    // 0xe73f84: AllocStack(0x30)
    //     0xe73f84: sub             SP, SP, #0x30
    // 0xe73f88: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe73f88: mov             x0, x1
    //     0xe73f8c: stur            x1, [fp, #-8]
    //     0xe73f90: stur            x2, [fp, #-0x10]
    // 0xe73f94: CheckStackOverflow
    //     0xe73f94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe73f98: cmp             SP, x16
    //     0xe73f9c: b.ls            #0xe74024
    // 0xe73fa0: r1 = 1
    //     0xe73fa0: movz            x1, #0x1
    // 0xe73fa4: r0 = AllocateContext()
    //     0xe73fa4: bl              #0xec126c  ; AllocateContextStub
    // 0xe73fa8: mov             x1, x0
    // 0xe73fac: ldur            x0, [fp, #-0x10]
    // 0xe73fb0: stur            x1, [fp, #-0x18]
    // 0xe73fb4: StoreField: r1->field_f = r0
    //     0xe73fb4: stur            w0, [x1, #0xf]
    // 0xe73fb8: r0 = InitLateStaticField(0x16ec) // [package:pdf/src/svg/parser.dart] SvgParser::_transformParameterRegExp
    //     0xe73fb8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe73fbc: ldr             x0, [x0, #0x2dd8]
    //     0xe73fc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe73fc4: cmp             w0, w16
    //     0xe73fc8: b.ne            #0xe73fd8
    //     0xe73fcc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e350] Field <SvgParser._transformParameterRegExp@2593390522>: static late final (offset: 0x16ec)
    //     0xe73fd0: ldr             x2, [x2, #0x350]
    //     0xe73fd4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe73fd8: mov             x1, x0
    // 0xe73fdc: ldur            x2, [fp, #-8]
    // 0xe73fe0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe73fe0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe73fe4: r0 = allMatches()
    //     0xe73fe4: bl              #0xebde88  ; [dart:core] _RegExp::allMatches
    // 0xe73fe8: ldur            x2, [fp, #-0x18]
    // 0xe73fec: r1 = Function '<anonymous closure>': static.
    //     0xe73fec: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ece8] AnonymousClosure: static (0xe7402c), in [package:pdf/src/svg/parser.dart] SvgParser::splitNumeric (0xe73f7c)
    //     0xe73ff0: ldr             x1, [x1, #0xce8]
    // 0xe73ff4: stur            x0, [fp, #-8]
    // 0xe73ff8: r0 = AllocateClosure()
    //     0xe73ff8: bl              #0xec1630  ; AllocateClosureStub
    // 0xe73ffc: r16 = <SvgNumeric>
    //     0xe73ffc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ecf0] TypeArguments: <SvgNumeric>
    //     0xe74000: ldr             x16, [x16, #0xcf0]
    // 0xe74004: ldur            lr, [fp, #-8]
    // 0xe74008: stp             lr, x16, [SP, #8]
    // 0xe7400c: str             x0, [SP]
    // 0xe74010: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe74010: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe74014: r0 = map()
    //     0xe74014: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xe74018: LeaveFrame
    //     0xe74018: mov             SP, fp
    //     0xe7401c: ldp             fp, lr, [SP], #0x10
    // 0xe74020: ret
    //     0xe74020: ret             
    // 0xe74024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe74024: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe74028: b               #0xe73fa0
  }
  [closure] static SvgNumeric <anonymous closure>(dynamic, RegExpMatch) {
    // ** addr: 0xe7402c, size: 0x6c
    // 0xe7402c: EnterFrame
    //     0xe7402c: stp             fp, lr, [SP, #-0x10]!
    //     0xe74030: mov             fp, SP
    // 0xe74034: AllocStack(0x8)
    //     0xe74034: sub             SP, SP, #8
    // 0xe74038: SetupParameters()
    //     0xe74038: ldr             x0, [fp, #0x18]
    //     0xe7403c: ldur            w3, [x0, #0x17]
    //     0xe74040: add             x3, x3, HEAP, lsl #32
    //     0xe74044: stur            x3, [fp, #-8]
    // 0xe74048: CheckStackOverflow
    //     0xe74048: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7404c: cmp             SP, x16
    //     0xe74050: b.ls            #0xe7408c
    // 0xe74054: ldr             x1, [fp, #0x10]
    // 0xe74058: r2 = 0
    //     0xe74058: movz            x2, #0
    // 0xe7405c: r0 = group()
    //     0xe7405c: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0xe74060: cmp             w0, NULL
    // 0xe74064: b.eq            #0xe74094
    // 0xe74068: ldur            x1, [fp, #-8]
    // 0xe7406c: LoadField: r3 = r1->field_f
    //     0xe7406c: ldur            w3, [x1, #0xf]
    // 0xe74070: DecompressPointer r3
    //     0xe74070: add             x3, x3, HEAP, lsl #32
    // 0xe74074: mov             x2, x0
    // 0xe74078: r1 = Null
    //     0xe74078: mov             x1, NULL
    // 0xe7407c: r0 = SvgNumeric()
    //     0xe7407c: bl              #0xb146a4  ; [package:pdf/src/svg/parser.dart] SvgNumeric::SvgNumeric
    // 0xe74080: LeaveFrame
    //     0xe74080: mov             SP, fp
    //     0xe74084: ldp             fp, lr, [SP], #0x10
    // 0xe74088: ret
    //     0xe74088: ret             
    // 0xe7408c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7408c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe74090: b               #0xe74054
    // 0xe74094: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe74094: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ getDouble(/* No info */) {
    // ** addr: 0xe76710, size: 0x9c
    // 0xe76710: EnterFrame
    //     0xe76710: stp             fp, lr, [SP, #-0x10]!
    //     0xe76714: mov             fp, SP
    // 0xe76718: AllocStack(0x10)
    //     0xe76718: sub             SP, SP, #0x10
    // 0xe7671c: SetupParameters(dynamic _ /* r3 => r0, fp-0x8 */)
    //     0xe7671c: mov             x0, x3
    //     0xe76720: stur            x3, [fp, #-8]
    // 0xe76724: CheckStackOverflow
    //     0xe76724: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe76728: cmp             SP, x16
    //     0xe7672c: b.ls            #0xe76794
    // 0xe76730: str             NULL, [SP]
    // 0xe76734: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xe76734: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xe76738: ldr             x4, [x4, #0x388]
    // 0xe7673c: r0 = getAttribute()
    //     0xe7673c: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe76740: cmp             w0, NULL
    // 0xe76744: b.ne            #0xe76758
    // 0xe76748: ldur            x0, [fp, #-8]
    // 0xe7674c: LeaveFrame
    //     0xe7674c: mov             SP, fp
    //     0xe76750: ldp             fp, lr, [SP], #0x10
    // 0xe76754: ret
    //     0xe76754: ret             
    // 0xe76758: mov             x1, x0
    // 0xe7675c: r0 = parse()
    //     0xe7675c: bl              #0x61ca34  ; [dart:core] double::parse
    // 0xe76760: r0 = inline_Allocate_Double()
    //     0xe76760: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe76764: add             x0, x0, #0x10
    //     0xe76768: cmp             x1, x0
    //     0xe7676c: b.ls            #0xe7679c
    //     0xe76770: str             x0, [THR, #0x50]  ; THR::top
    //     0xe76774: sub             x0, x0, #0xf
    //     0xe76778: movz            x1, #0xe15c
    //     0xe7677c: movk            x1, #0x3, lsl #16
    //     0xe76780: stur            x1, [x0, #-1]
    // 0xe76784: StoreField: r0->field_7 = d0
    //     0xe76784: stur            d0, [x0, #7]
    // 0xe76788: LeaveFrame
    //     0xe76788: mov             SP, fp
    //     0xe7678c: ldp             fp, lr, [SP], #0x10
    // 0xe76790: ret
    //     0xe76790: ret             
    // 0xe76794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe76794: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76798: b               #0xe76730
    // 0xe7679c: SaveReg d0
    //     0xe7679c: str             q0, [SP, #-0x10]!
    // 0xe767a0: r0 = AllocateDouble()
    //     0xe767a0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe767a4: RestoreReg d0
    //     0xe767a4: ldr             q0, [SP], #0x10
    // 0xe767a8: b               #0xe76784
  }
  static _ convertStyle(/* No info */) {
    // ** addr: 0xe767ac, size: 0x3e8
    // 0xe767ac: EnterFrame
    //     0xe767ac: stp             fp, lr, [SP, #-0x10]!
    //     0xe767b0: mov             fp, SP
    // 0xe767b4: AllocStack(0x80)
    //     0xe767b4: sub             SP, SP, #0x80
    // 0xe767b8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xe767b8: mov             x0, x1
    //     0xe767bc: stur            x1, [fp, #-8]
    // 0xe767c0: CheckStackOverflow
    //     0xe767c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe767c4: cmp             SP, x16
    //     0xe767c8: b.ls            #0xe76b7c
    // 0xe767cc: mov             x1, x0
    // 0xe767d0: r2 = "style"
    //     0xe767d0: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xe767d4: ldr             x2, [x2, #0x9b8]
    // 0xe767d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe767d8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe767dc: r0 = getAttribute()
    //     0xe767dc: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe767e0: cmp             w0, NULL
    // 0xe767e4: b.ne            #0xe767f0
    // 0xe767e8: r1 = Null
    //     0xe767e8: mov             x1, NULL
    // 0xe767ec: b               #0xe767fc
    // 0xe767f0: mov             x1, x0
    // 0xe767f4: r0 = trim()
    //     0xe767f4: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0xe767f8: mov             x1, x0
    // 0xe767fc: cmp             w1, NULL
    // 0xe76800: b.eq            #0xe76abc
    // 0xe76804: LoadField: r0 = r1->field_7
    //     0xe76804: ldur            w0, [x1, #7]
    // 0xe76808: cbz             w0, #0xe76abc
    // 0xe7680c: r0 = LoadClassIdInstr(r1)
    //     0xe7680c: ldur            x0, [x1, #-1]
    //     0xe76810: ubfx            x0, x0, #0xc, #0x14
    // 0xe76814: r2 = ";"
    //     0xe76814: add             x2, PP, #0x10, lsl #12  ; [pp+0x107f8] ";"
    //     0xe76818: ldr             x2, [x2, #0x7f8]
    // 0xe7681c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe7681c: sub             lr, x0, #1, lsl #12
    //     0xe76820: ldr             lr, [x21, lr, lsl #3]
    //     0xe76824: blr             lr
    // 0xe76828: stur            x0, [fp, #-0x30]
    // 0xe7682c: LoadField: r1 = r0->field_b
    //     0xe7682c: ldur            w1, [x0, #0xb]
    // 0xe76830: r2 = LoadInt32Instr(r1)
    //     0xe76830: sbfx            x2, x1, #1, #0x1f
    // 0xe76834: stur            x2, [fp, #-0x28]
    // 0xe76838: r1 = 0
    //     0xe76838: movz            x1, #0
    // 0xe7683c: CheckStackOverflow
    //     0xe7683c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe76840: cmp             SP, x16
    //     0xe76844: b.ls            #0xe76b84
    // 0xe76848: LoadField: r3 = r0->field_b
    //     0xe76848: ldur            w3, [x0, #0xb]
    // 0xe7684c: r4 = LoadInt32Instr(r3)
    //     0xe7684c: sbfx            x4, x3, #1, #0x1f
    // 0xe76850: cmp             x2, x4
    // 0xe76854: b.ne            #0xe76b60
    // 0xe76858: cmp             x1, x4
    // 0xe7685c: b.ge            #0xe76abc
    // 0xe76860: LoadField: r3 = r0->field_f
    //     0xe76860: ldur            w3, [x0, #0xf]
    // 0xe76864: DecompressPointer r3
    //     0xe76864: add             x3, x3, HEAP, lsl #32
    // 0xe76868: ArrayLoad: r4 = r3[r1]  ; Unknown_4
    //     0xe76868: add             x16, x3, x1, lsl #2
    //     0xe7686c: ldur            w4, [x16, #0xf]
    // 0xe76870: DecompressPointer r4
    //     0xe76870: add             x4, x4, HEAP, lsl #32
    // 0xe76874: stur            x4, [fp, #-0x20]
    // 0xe76878: add             x3, x1, #1
    // 0xe7687c: stur            x3, [fp, #-0x18]
    // 0xe76880: LoadField: r5 = r4->field_7
    //     0xe76880: ldur            w5, [x4, #7]
    // 0xe76884: mov             x1, x4
    // 0xe76888: stur            x5, [fp, #-0x10]
    // 0xe7688c: r0 = _firstNonWhitespace()
    //     0xe7688c: bl              #0x6072c0  ; [dart:core] _StringBase::_firstNonWhitespace
    // 0xe76890: mov             x2, x0
    // 0xe76894: ldur            x0, [fp, #-0x10]
    // 0xe76898: stur            x2, [fp, #-0x40]
    // 0xe7689c: r3 = LoadInt32Instr(r0)
    //     0xe7689c: sbfx            x3, x0, #1, #0x1f
    // 0xe768a0: stur            x3, [fp, #-0x38]
    // 0xe768a4: cmp             x3, x2
    // 0xe768a8: b.ne            #0xe768b4
    // 0xe768ac: r0 = ""
    //     0xe768ac: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe768b0: b               #0xe768e8
    // 0xe768b4: ldur            x1, [fp, #-0x20]
    // 0xe768b8: r0 = _lastNonWhitespace()
    //     0xe768b8: bl              #0x60741c  ; [dart:core] _StringBase::_lastNonWhitespace
    // 0xe768bc: add             x3, x0, #1
    // 0xe768c0: ldur            x2, [fp, #-0x40]
    // 0xe768c4: cbnz            x2, #0xe768dc
    // 0xe768c8: ldur            x0, [fp, #-0x38]
    // 0xe768cc: cmp             x3, x0
    // 0xe768d0: b.ne            #0xe768e0
    // 0xe768d4: ldur            x0, [fp, #-0x20]
    // 0xe768d8: b               #0xe768e8
    // 0xe768dc: ldur            x0, [fp, #-0x38]
    // 0xe768e0: ldur            x1, [fp, #-0x20]
    // 0xe768e4: r0 = _substringUnchecked()
    //     0xe768e4: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xe768e8: LoadField: r1 = r0->field_7
    //     0xe768e8: ldur            w1, [x0, #7]
    // 0xe768ec: cbz             w1, #0xe76aac
    // 0xe768f0: ldur            x0, [fp, #-0x38]
    // 0xe768f4: r16 = "([\\w-]+)\\s*:\\s*(.*)"
    //     0xe768f4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed70] "([\\w-]+)\\s*:\\s*(.*)"
    //     0xe768f8: ldr             x16, [x16, #0xd70]
    // 0xe768fc: stp             x16, NULL, [SP, #0x20]
    // 0xe76900: r16 = false
    //     0xe76900: add             x16, NULL, #0x30  ; false
    // 0xe76904: r30 = true
    //     0xe76904: add             lr, NULL, #0x20  ; true
    // 0xe76908: stp             lr, x16, [SP, #0x10]
    // 0xe7690c: r16 = false
    //     0xe7690c: add             x16, NULL, #0x30  ; false
    // 0xe76910: r30 = false
    //     0xe76910: add             lr, NULL, #0x30  ; false
    // 0xe76914: stp             lr, x16, [SP]
    // 0xe76918: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xe76918: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xe7691c: r0 = _RegExp()
    //     0xe7691c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xe76920: mov             x1, x0
    // 0xe76924: ldur            x0, [fp, #-0x38]
    // 0xe76928: stur            x1, [fp, #-0x48]
    // 0xe7692c: tbnz            x0, #0x3f, #0xe76b38
    // 0xe76930: ldur            x0, [fp, #-0x20]
    // 0xe76934: r0 = _AllMatchesIterator()
    //     0xe76934: bl              #0x88787c  ; Allocate_AllMatchesIteratorStub -> _AllMatchesIterator (size=0x1c)
    // 0xe76938: mov             x2, x0
    // 0xe7693c: ldur            x0, [fp, #-0x48]
    // 0xe76940: stur            x2, [fp, #-0x50]
    // 0xe76944: StoreField: r2->field_13 = r0
    //     0xe76944: stur            w0, [x2, #0x13]
    // 0xe76948: ldur            x0, [fp, #-0x20]
    // 0xe7694c: StoreField: r2->field_7 = r0
    //     0xe7694c: stur            w0, [x2, #7]
    // 0xe76950: StoreField: r2->field_b = rZR
    //     0xe76950: stur            xzr, [x2, #0xb]
    // 0xe76954: mov             x1, x2
    // 0xe76958: r0 = moveNext()
    //     0xe76958: bl              #0x675d28  ; [dart:core] _AllMatchesIterator::moveNext
    // 0xe7695c: tbnz            w0, #4, #0xe76b2c
    // 0xe76960: ldur            x0, [fp, #-0x50]
    // 0xe76964: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xe76964: ldur            w3, [x0, #0x17]
    // 0xe76968: DecompressPointer r3
    //     0xe76968: add             x3, x3, HEAP, lsl #32
    // 0xe7696c: stur            x3, [fp, #-0x20]
    // 0xe76970: cmp             w3, NULL
    // 0xe76974: b.ne            #0xe769ac
    // 0xe76978: mov             x0, x3
    // 0xe7697c: r2 = Null
    //     0xe7697c: mov             x2, NULL
    // 0xe76980: r1 = Null
    //     0xe76980: mov             x1, NULL
    // 0xe76984: r4 = LoadClassIdInstr(r0)
    //     0xe76984: ldur            x4, [x0, #-1]
    //     0xe76988: ubfx            x4, x4, #0xc, #0x14
    // 0xe7698c: r17 = 7313
    //     0xe7698c: movz            x17, #0x1c91
    // 0xe76990: cmp             x4, x17
    // 0xe76994: b.eq            #0xe769ac
    // 0xe76998: r8 = RegExpMatch
    //     0xe76998: add             x8, PP, #0x10, lsl #12  ; [pp+0x108c8] Type: RegExpMatch
    //     0xe7699c: ldr             x8, [x8, #0x8c8]
    // 0xe769a0: r3 = Null
    //     0xe769a0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ed78] Null
    //     0xe769a4: ldr             x3, [x3, #0xd78]
    // 0xe769a8: r0 = RegExpMatch()
    //     0xe769a8: bl              #0x644470  ; IsType_RegExpMatch_Stub
    // 0xe769ac: ldur            x1, [fp, #-0x20]
    // 0xe769b0: LoadField: r0 = r1->field_7
    //     0xe769b0: ldur            w0, [x1, #7]
    // 0xe769b4: DecompressPointer r0
    //     0xe769b4: add             x0, x0, HEAP, lsl #32
    // 0xe769b8: stur            x0, [fp, #-0x48]
    // 0xe769bc: str             x0, [SP]
    // 0xe769c0: r0 = _groupCount()
    //     0xe769c0: bl              #0xd5fd08  ; [dart:core] _RegExp::_groupCount
    // 0xe769c4: r1 = LoadInt32Instr(r0)
    //     0xe769c4: sbfx            x1, x0, #1, #0x1f
    //     0xe769c8: tbz             w0, #0, #0xe769d0
    //     0xe769cc: ldur            x1, [x0, #7]
    // 0xe769d0: cmp             x1, #1
    // 0xe769d4: b.lt            #0xe76af8
    // 0xe769d8: ldur            x1, [fp, #-0x20]
    // 0xe769dc: r2 = 1
    //     0xe769dc: movz            x2, #0x1
    // 0xe769e0: r0 = _start()
    //     0xe769e0: bl              #0x65cba0  ; [dart:core] _RegExpMatch::_start
    // 0xe769e4: ldur            x1, [fp, #-0x20]
    // 0xe769e8: r2 = 1
    //     0xe769e8: movz            x2, #0x1
    // 0xe769ec: stur            x0, [fp, #-0x38]
    // 0xe769f0: r0 = _end()
    //     0xe769f0: bl              #0x65cb1c  ; [dart:core] _RegExpMatch::_end
    // 0xe769f4: ldur            x2, [fp, #-0x38]
    // 0xe769f8: cmn             x2, #1
    // 0xe769fc: b.ne            #0xe76a08
    // 0xe76a00: r2 = Null
    //     0xe76a00: mov             x2, NULL
    // 0xe76a04: b               #0xe76a20
    // 0xe76a08: ldur            x4, [fp, #-0x20]
    // 0xe76a0c: LoadField: r1 = r4->field_b
    //     0xe76a0c: ldur            w1, [x4, #0xb]
    // 0xe76a10: DecompressPointer r1
    //     0xe76a10: add             x1, x1, HEAP, lsl #32
    // 0xe76a14: mov             x3, x0
    // 0xe76a18: r0 = _substringUnchecked()
    //     0xe76a18: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xe76a1c: mov             x2, x0
    // 0xe76a20: stur            x2, [fp, #-0x50]
    // 0xe76a24: cmp             w2, NULL
    // 0xe76a28: b.eq            #0xe76b8c
    // 0xe76a2c: ldur            x16, [fp, #-0x48]
    // 0xe76a30: str             x16, [SP]
    // 0xe76a34: r0 = _groupCount()
    //     0xe76a34: bl              #0xd5fd08  ; [dart:core] _RegExp::_groupCount
    // 0xe76a38: r1 = LoadInt32Instr(r0)
    //     0xe76a38: sbfx            x1, x0, #1, #0x1f
    //     0xe76a3c: tbz             w0, #0, #0xe76a44
    //     0xe76a40: ldur            x1, [x0, #7]
    // 0xe76a44: cmp             x1, #2
    // 0xe76a48: b.lt            #0xe76acc
    // 0xe76a4c: ldur            x1, [fp, #-0x20]
    // 0xe76a50: r2 = 2
    //     0xe76a50: movz            x2, #0x2
    // 0xe76a54: r0 = _start()
    //     0xe76a54: bl              #0x65cba0  ; [dart:core] _RegExpMatch::_start
    // 0xe76a58: ldur            x1, [fp, #-0x20]
    // 0xe76a5c: r2 = 2
    //     0xe76a5c: movz            x2, #0x2
    // 0xe76a60: stur            x0, [fp, #-0x38]
    // 0xe76a64: r0 = _end()
    //     0xe76a64: bl              #0x65cb1c  ; [dart:core] _RegExpMatch::_end
    // 0xe76a68: ldur            x2, [fp, #-0x38]
    // 0xe76a6c: cmn             x2, #1
    // 0xe76a70: b.ne            #0xe76a7c
    // 0xe76a74: r3 = Null
    //     0xe76a74: mov             x3, NULL
    // 0xe76a78: b               #0xe76a98
    // 0xe76a7c: ldur            x1, [fp, #-0x20]
    // 0xe76a80: LoadField: r3 = r1->field_b
    //     0xe76a80: ldur            w3, [x1, #0xb]
    // 0xe76a84: DecompressPointer r3
    //     0xe76a84: add             x3, x3, HEAP, lsl #32
    // 0xe76a88: mov             x1, x3
    // 0xe76a8c: mov             x3, x0
    // 0xe76a90: r0 = _substringUnchecked()
    //     0xe76a90: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xe76a94: mov             x3, x0
    // 0xe76a98: cmp             w3, NULL
    // 0xe76a9c: b.eq            #0xe76b90
    // 0xe76aa0: ldur            x1, [fp, #-8]
    // 0xe76aa4: ldur            x2, [fp, #-0x50]
    // 0xe76aa8: r0 = setAttribute()
    //     0xe76aa8: bl              #0xe76b94  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::setAttribute
    // 0xe76aac: ldur            x1, [fp, #-0x18]
    // 0xe76ab0: ldur            x0, [fp, #-0x30]
    // 0xe76ab4: ldur            x2, [fp, #-0x28]
    // 0xe76ab8: b               #0xe7683c
    // 0xe76abc: r0 = Null
    //     0xe76abc: mov             x0, NULL
    // 0xe76ac0: LeaveFrame
    //     0xe76ac0: mov             SP, fp
    //     0xe76ac4: ldp             fp, lr, [SP], #0x10
    // 0xe76ac8: ret
    //     0xe76ac8: ret             
    // 0xe76acc: r0 = RangeError()
    //     0xe76acc: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xe76ad0: mov             x1, x0
    // 0xe76ad4: r0 = "Value not in range"
    //     0xe76ad4: ldr             x0, [PP, #0x588]  ; [pp+0x588] "Value not in range"
    // 0xe76ad8: ArrayStore: r1[0] = r0  ; List_4
    //     0xe76ad8: stur            w0, [x1, #0x17]
    // 0xe76adc: r0 = 4
    //     0xe76adc: movz            x0, #0x4
    // 0xe76ae0: StoreField: r1->field_f = r0
    //     0xe76ae0: stur            w0, [x1, #0xf]
    // 0xe76ae4: r2 = true
    //     0xe76ae4: add             x2, NULL, #0x20  ; true
    // 0xe76ae8: StoreField: r1->field_b = r2
    //     0xe76ae8: stur            w2, [x1, #0xb]
    // 0xe76aec: mov             x0, x1
    // 0xe76af0: r0 = Throw()
    //     0xe76af0: bl              #0xec04b8  ; ThrowStub
    // 0xe76af4: brk             #0
    // 0xe76af8: r2 = true
    //     0xe76af8: add             x2, NULL, #0x20  ; true
    // 0xe76afc: r0 = "Value not in range"
    //     0xe76afc: ldr             x0, [PP, #0x588]  ; [pp+0x588] "Value not in range"
    // 0xe76b00: r0 = RangeError()
    //     0xe76b00: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xe76b04: mov             x1, x0
    // 0xe76b08: r0 = "Value not in range"
    //     0xe76b08: ldr             x0, [PP, #0x588]  ; [pp+0x588] "Value not in range"
    // 0xe76b0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xe76b0c: stur            w0, [x1, #0x17]
    // 0xe76b10: r0 = 2
    //     0xe76b10: movz            x0, #0x2
    // 0xe76b14: StoreField: r1->field_f = r0
    //     0xe76b14: stur            w0, [x1, #0xf]
    // 0xe76b18: r0 = true
    //     0xe76b18: add             x0, NULL, #0x20  ; true
    // 0xe76b1c: StoreField: r1->field_b = r0
    //     0xe76b1c: stur            w0, [x1, #0xb]
    // 0xe76b20: mov             x0, x1
    // 0xe76b24: r0 = Throw()
    //     0xe76b24: bl              #0xec04b8  ; ThrowStub
    // 0xe76b28: brk             #0
    // 0xe76b2c: r0 = noElement()
    //     0xe76b2c: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xe76b30: r0 = Throw()
    //     0xe76b30: bl              #0xec04b8  ; ThrowStub
    // 0xe76b34: brk             #0
    // 0xe76b38: r0 = RangeError()
    //     0xe76b38: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xe76b3c: stur            x0, [fp, #-8]
    // 0xe76b40: stp             xzr, x0, [SP, #0x10]
    // 0xe76b44: ldur            x16, [fp, #-0x10]
    // 0xe76b48: stp             x16, xzr, [SP]
    // 0xe76b4c: r4 = const [0, 0x4, 0x4, 0x4, null]
    //     0xe76b4c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    // 0xe76b50: r0 = RangeError.range()
    //     0xe76b50: bl              #0x600404  ; [dart:core] RangeError::RangeError.range
    // 0xe76b54: ldur            x0, [fp, #-8]
    // 0xe76b58: r0 = Throw()
    //     0xe76b58: bl              #0xec04b8  ; ThrowStub
    // 0xe76b5c: brk             #0
    // 0xe76b60: r0 = ConcurrentModificationError()
    //     0xe76b60: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe76b64: mov             x1, x0
    // 0xe76b68: ldur            x0, [fp, #-0x30]
    // 0xe76b6c: StoreField: r1->field_b = r0
    //     0xe76b6c: stur            w0, [x1, #0xb]
    // 0xe76b70: mov             x0, x1
    // 0xe76b74: r0 = Throw()
    //     0xe76b74: bl              #0xec04b8  ; ThrowStub
    // 0xe76b78: brk             #0
    // 0xe76b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe76b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76b80: b               #0xe767cc
    // 0xe76b84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe76b84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76b88: b               #0xe76848
    // 0xe76b8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe76b8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe76b90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe76b90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 6806, size: 0x14, field offset: 0x14
enum SvgUnit extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4dd34, size: 0x64
    // 0xc4dd34: EnterFrame
    //     0xc4dd34: stp             fp, lr, [SP, #-0x10]!
    //     0xc4dd38: mov             fp, SP
    // 0xc4dd3c: AllocStack(0x10)
    //     0xc4dd3c: sub             SP, SP, #0x10
    // 0xc4dd40: SetupParameters(SvgUnit this /* r1 => r0, fp-0x8 */)
    //     0xc4dd40: mov             x0, x1
    //     0xc4dd44: stur            x1, [fp, #-8]
    // 0xc4dd48: CheckStackOverflow
    //     0xc4dd48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4dd4c: cmp             SP, x16
    //     0xc4dd50: b.ls            #0xc4dd90
    // 0xc4dd54: r1 = Null
    //     0xc4dd54: mov             x1, NULL
    // 0xc4dd58: r2 = 4
    //     0xc4dd58: movz            x2, #0x4
    // 0xc4dd5c: r0 = AllocateArray()
    //     0xc4dd5c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4dd60: r16 = "SvgUnit."
    //     0xc4dd60: add             x16, PP, #0x31, lsl #12  ; [pp+0x314c0] "SvgUnit."
    //     0xc4dd64: ldr             x16, [x16, #0x4c0]
    // 0xc4dd68: StoreField: r0->field_f = r16
    //     0xc4dd68: stur            w16, [x0, #0xf]
    // 0xc4dd6c: ldur            x1, [fp, #-8]
    // 0xc4dd70: LoadField: r2 = r1->field_f
    //     0xc4dd70: ldur            w2, [x1, #0xf]
    // 0xc4dd74: DecompressPointer r2
    //     0xc4dd74: add             x2, x2, HEAP, lsl #32
    // 0xc4dd78: StoreField: r0->field_13 = r2
    //     0xc4dd78: stur            w2, [x0, #0x13]
    // 0xc4dd7c: str             x0, [SP]
    // 0xc4dd80: r0 = _interpolate()
    //     0xc4dd80: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4dd84: LeaveFrame
    //     0xc4dd84: mov             SP, fp
    //     0xc4dd88: ldp             fp, lr, [SP], #0x10
    // 0xc4dd8c: ret
    //     0xc4dd8c: ret             
    // 0xc4dd90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4dd90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4dd94: b               #0xc4dd54
  }
}
