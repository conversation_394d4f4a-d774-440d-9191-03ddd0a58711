// lib: , url: package:pdf/src/svg/path.dart

// class id: 1050837, size: 0x8
class :: {
}

// class id: 845, size: 0x1c, field offset: 0x18
class SvgPath extends SvgOperation {

  _ paintShape(/* No info */) {
    // ** addr: 0xe48f9c, size: 0x34c
    // 0xe48f9c: EnterFrame
    //     0xe48f9c: stp             fp, lr, [SP, #-0x10]!
    //     0xe48fa0: mov             fp, SP
    // 0xe48fa4: AllocStack(0x30)
    //     0xe48fa4: sub             SP, SP, #0x30
    // 0xe48fa8: SetupParameters(SvgPath this /* r1 => r2, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */)
    //     0xe48fa8: mov             x3, x2
    //     0xe48fac: stur            x2, [fp, #-0x20]
    //     0xe48fb0: mov             x2, x1
    //     0xe48fb4: stur            x1, [fp, #-0x18]
    // 0xe48fb8: CheckStackOverflow
    //     0xe48fb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48fbc: cmp             SP, x16
    //     0xe48fc0: b.ls            #0xe49294
    // 0xe48fc4: LoadField: r0 = r2->field_7
    //     0xe48fc4: ldur            w0, [x2, #7]
    // 0xe48fc8: DecompressPointer r0
    //     0xe48fc8: add             x0, x0, HEAP, lsl #32
    // 0xe48fcc: stur            x0, [fp, #-0x10]
    // 0xe48fd0: LoadField: r4 = r0->field_b
    //     0xe48fd0: ldur            w4, [x0, #0xb]
    // 0xe48fd4: DecompressPointer r4
    //     0xe48fd4: add             x4, x4, HEAP, lsl #32
    // 0xe48fd8: mov             x1, x4
    // 0xe48fdc: stur            x4, [fp, #-8]
    // 0xe48fe0: r0 = isNotEmpty()
    //     0xe48fe0: bl              #0xe4998c  ; [package:pdf/src/svg/color.dart] SvgColor::isNotEmpty
    // 0xe48fe4: tbnz            w0, #4, #0xe490bc
    // 0xe48fe8: ldur            x4, [fp, #-0x10]
    // 0xe48fec: ldur            x1, [fp, #-8]
    // 0xe48ff0: r0 = LoadClassIdInstr(r1)
    //     0xe48ff0: ldur            x0, [x1, #-1]
    //     0xe48ff4: ubfx            x0, x0, #0xc, #0x14
    // 0xe48ff8: ldur            x2, [fp, #-0x18]
    // 0xe48ffc: ldur            x3, [fp, #-0x20]
    // 0xe49000: r0 = GDT[cid_x0 + -0xfef]()
    //     0xe49000: sub             lr, x0, #0xfef
    //     0xe49004: ldr             lr, [x21, lr, lsl #3]
    //     0xe49008: blr             lr
    // 0xe4900c: ldur            x0, [fp, #-0x10]
    // 0xe49010: LoadField: r2 = r0->field_13
    //     0xe49010: ldur            w2, [x0, #0x13]
    // 0xe49014: DecompressPointer r2
    //     0xe49014: add             x2, x2, HEAP, lsl #32
    // 0xe49018: stur            x2, [fp, #-8]
    // 0xe4901c: cmp             w2, NULL
    // 0xe49020: b.eq            #0xe4929c
    // 0xe49024: LoadField: d0 = r2->field_7
    //     0xe49024: ldur            d0, [x2, #7]
    // 0xe49028: stur            d0, [fp, #-0x28]
    // 0xe4902c: d1 = 1.000000
    //     0xe4902c: fmov            d1, #1.00000000
    // 0xe49030: fcmp            d1, d0
    // 0xe49034: b.le            #0xe49060
    // 0xe49038: ldur            x1, [fp, #-0x20]
    // 0xe4903c: r0 = saveContext()
    //     0xe4903c: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe49040: r0 = PdfGraphicState()
    //     0xe49040: bl              #0xe473a8  ; AllocatePdfGraphicStateStub -> PdfGraphicState (size=0x1c)
    // 0xe49044: mov             x1, x0
    // 0xe49048: ldur            x0, [fp, #-8]
    // 0xe4904c: StoreField: r1->field_7 = r0
    //     0xe4904c: stur            w0, [x1, #7]
    // 0xe49050: StoreField: r1->field_b = r0
    //     0xe49050: stur            w0, [x1, #0xb]
    // 0xe49054: mov             x2, x1
    // 0xe49058: ldur            x1, [fp, #-0x20]
    // 0xe4905c: r0 = setGraphicState()
    //     0xe4905c: bl              #0xe47304  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setGraphicState
    // 0xe49060: ldur            x3, [fp, #-0x18]
    // 0xe49064: ldur            x0, [fp, #-0x10]
    // 0xe49068: ldur            d0, [fp, #-0x28]
    // 0xe4906c: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xe4906c: ldur            w2, [x3, #0x17]
    // 0xe49070: DecompressPointer r2
    //     0xe49070: add             x2, x2, HEAP, lsl #32
    // 0xe49074: ldur            x1, [fp, #-0x20]
    // 0xe49078: r0 = drawShape()
    //     0xe49078: bl              #0xe497d0  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawShape
    // 0xe4907c: ldur            x0, [fp, #-0x10]
    // 0xe49080: LoadField: r1 = r0->field_f
    //     0xe49080: ldur            w1, [x0, #0xf]
    // 0xe49084: DecompressPointer r1
    //     0xe49084: add             x1, x1, HEAP, lsl #32
    // 0xe49088: cmp             w1, NULL
    // 0xe4908c: b.eq            #0xe492a0
    // 0xe49090: str             x1, [SP]
    // 0xe49094: ldur            x1, [fp, #-0x20]
    // 0xe49098: r4 = const [0, 0x2, 0x1, 0x1, evenOdd, 0x1, null]
    //     0xe49098: add             x4, PP, #0x46, lsl #12  ; [pp+0x46e00] List(7) [0, 0x2, 0x1, 0x1, "evenOdd", 0x1, Null]
    //     0xe4909c: ldr             x4, [x4, #0xe00]
    // 0xe490a0: r0 = fillPath()
    //     0xe490a0: bl              #0xe496d4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::fillPath
    // 0xe490a4: ldur            d0, [fp, #-0x28]
    // 0xe490a8: d1 = 1.000000
    //     0xe490a8: fmov            d1, #1.00000000
    // 0xe490ac: fcmp            d1, d0
    // 0xe490b0: b.le            #0xe490bc
    // 0xe490b4: ldur            x1, [fp, #-0x20]
    // 0xe490b8: r0 = restoreContext()
    //     0xe490b8: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe490bc: ldur            x0, [fp, #-0x10]
    // 0xe490c0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xe490c0: ldur            w2, [x0, #0x17]
    // 0xe490c4: DecompressPointer r2
    //     0xe490c4: add             x2, x2, HEAP, lsl #32
    // 0xe490c8: mov             x1, x2
    // 0xe490cc: stur            x2, [fp, #-8]
    // 0xe490d0: r0 = isNotEmpty()
    //     0xe490d0: bl              #0xe4998c  ; [package:pdf/src/svg/color.dart] SvgColor::isNotEmpty
    // 0xe490d4: tbnz            w0, #4, #0xe49284
    // 0xe490d8: ldur            x4, [fp, #-0x10]
    // 0xe490dc: ldur            x1, [fp, #-8]
    // 0xe490e0: r0 = LoadClassIdInstr(r1)
    //     0xe490e0: ldur            x0, [x1, #-1]
    //     0xe490e4: ubfx            x0, x0, #0xc, #0x14
    // 0xe490e8: ldur            x2, [fp, #-0x18]
    // 0xe490ec: ldur            x3, [fp, #-0x20]
    // 0xe490f0: r0 = GDT[cid_x0 + -0xfeb]()
    //     0xe490f0: sub             lr, x0, #0xfeb
    //     0xe490f4: ldr             lr, [x21, lr, lsl #3]
    //     0xe490f8: blr             lr
    // 0xe490fc: ldur            x0, [fp, #-0x10]
    // 0xe49100: LoadField: r1 = r0->field_1b
    //     0xe49100: ldur            w1, [x0, #0x1b]
    // 0xe49104: DecompressPointer r1
    //     0xe49104: add             x1, x1, HEAP, lsl #32
    // 0xe49108: stur            x1, [fp, #-8]
    // 0xe4910c: cmp             w1, NULL
    // 0xe49110: b.eq            #0xe492a4
    // 0xe49114: LoadField: d0 = r1->field_7
    //     0xe49114: ldur            d0, [x1, #7]
    // 0xe49118: d1 = 1.000000
    //     0xe49118: fmov            d1, #1.00000000
    // 0xe4911c: fcmp            d1, d0
    // 0xe49120: b.le            #0xe49144
    // 0xe49124: r0 = PdfGraphicState()
    //     0xe49124: bl              #0xe473a8  ; AllocatePdfGraphicStateStub -> PdfGraphicState (size=0x1c)
    // 0xe49128: mov             x1, x0
    // 0xe4912c: ldur            x0, [fp, #-8]
    // 0xe49130: StoreField: r1->field_7 = r0
    //     0xe49130: stur            w0, [x1, #7]
    // 0xe49134: StoreField: r1->field_b = r0
    //     0xe49134: stur            w0, [x1, #0xb]
    // 0xe49138: mov             x2, x1
    // 0xe4913c: ldur            x1, [fp, #-0x20]
    // 0xe49140: r0 = setGraphicState()
    //     0xe49140: bl              #0xe47304  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setGraphicState
    // 0xe49144: ldur            x1, [fp, #-0x18]
    // 0xe49148: ldur            x0, [fp, #-0x10]
    // 0xe4914c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xe4914c: ldur            w2, [x1, #0x17]
    // 0xe49150: DecompressPointer r2
    //     0xe49150: add             x2, x2, HEAP, lsl #32
    // 0xe49154: ldur            x1, [fp, #-0x20]
    // 0xe49158: r0 = drawShape()
    //     0xe49158: bl              #0xe497d0  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawShape
    // 0xe4915c: ldur            x0, [fp, #-0x10]
    // 0xe49160: LoadField: r2 = r0->field_2b
    //     0xe49160: ldur            w2, [x0, #0x2b]
    // 0xe49164: DecompressPointer r2
    //     0xe49164: add             x2, x2, HEAP, lsl #32
    // 0xe49168: cmp             w2, NULL
    // 0xe4916c: b.eq            #0xe492a8
    // 0xe49170: ldur            x1, [fp, #-0x20]
    // 0xe49174: r0 = setLineCap()
    //     0xe49174: bl              #0xe49644  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setLineCap
    // 0xe49178: ldur            x0, [fp, #-0x10]
    // 0xe4917c: LoadField: r2 = r0->field_2f
    //     0xe4917c: ldur            w2, [x0, #0x2f]
    // 0xe49180: DecompressPointer r2
    //     0xe49180: add             x2, x2, HEAP, lsl #32
    // 0xe49184: cmp             w2, NULL
    // 0xe49188: b.eq            #0xe492ac
    // 0xe4918c: ldur            x1, [fp, #-0x20]
    // 0xe49190: r0 = setLineJoin()
    //     0xe49190: bl              #0xe495b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setLineJoin
    // 0xe49194: ldur            x0, [fp, #-0x10]
    // 0xe49198: LoadField: r1 = r0->field_33
    //     0xe49198: ldur            w1, [x0, #0x33]
    // 0xe4919c: DecompressPointer r1
    //     0xe4919c: add             x1, x1, HEAP, lsl #32
    // 0xe491a0: cmp             w1, NULL
    // 0xe491a4: b.eq            #0xe492b0
    // 0xe491a8: LoadField: d0 = r1->field_7
    //     0xe491a8: ldur            d0, [x1, #7]
    // 0xe491ac: d1 = 1.000000
    //     0xe491ac: fmov            d1, #1.00000000
    // 0xe491b0: fcmp            d1, d0
    // 0xe491b4: b.le            #0xe491c0
    // 0xe491b8: d0 = 1.000000
    //     0xe491b8: fmov            d0, #1.00000000
    // 0xe491bc: b               #0xe49200
    // 0xe491c0: fcmp            d0, d1
    // 0xe491c4: b.le            #0xe491d0
    // 0xe491c8: LoadField: d0 = r1->field_7
    //     0xe491c8: ldur            d0, [x1, #7]
    // 0xe491cc: b               #0xe49200
    // 0xe491d0: d2 = 0.000000
    //     0xe491d0: eor             v2.16b, v2.16b, v2.16b
    // 0xe491d4: fcmp            d1, d2
    // 0xe491d8: b.ne            #0xe491e8
    // 0xe491dc: fadd            d2, d0, d1
    // 0xe491e0: mov             v0.16b, v2.16b
    // 0xe491e4: b               #0xe49200
    // 0xe491e8: LoadField: d0 = r1->field_7
    //     0xe491e8: ldur            d0, [x1, #7]
    // 0xe491ec: fcmp            d0, d0
    // 0xe491f0: b.vc            #0xe491fc
    // 0xe491f4: LoadField: d0 = r1->field_7
    //     0xe491f4: ldur            d0, [x1, #7]
    // 0xe491f8: b               #0xe49200
    // 0xe491fc: d0 = 1.000000
    //     0xe491fc: fmov            d0, #1.00000000
    // 0xe49200: ldur            x1, [fp, #-0x20]
    // 0xe49204: r0 = setMiterLimit()
    //     0xe49204: bl              #0xe494fc  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setMiterLimit
    // 0xe49208: ldur            x0, [fp, #-0x10]
    // 0xe4920c: LoadField: r2 = r0->field_23
    //     0xe4920c: ldur            w2, [x0, #0x23]
    // 0xe49210: DecompressPointer r2
    //     0xe49210: add             x2, x2, HEAP, lsl #32
    // 0xe49214: cmp             w2, NULL
    // 0xe49218: b.eq            #0xe492b4
    // 0xe4921c: LoadField: r1 = r0->field_27
    //     0xe4921c: ldur            w1, [x0, #0x27]
    // 0xe49220: DecompressPointer r1
    //     0xe49220: add             x1, x1, HEAP, lsl #32
    // 0xe49224: cmp             w1, NULL
    // 0xe49228: b.eq            #0xe492b8
    // 0xe4922c: LoadField: d0 = r1->field_7
    //     0xe4922c: ldur            d0, [x1, #7]
    // 0xe49230: fcmp            d0, d0
    // 0xe49234: b.vs            #0xe492bc
    // 0xe49238: fcvtzs          x1, d0
    // 0xe4923c: asr             x16, x1, #0x1e
    // 0xe49240: cmp             x16, x1, asr #63
    // 0xe49244: b.ne            #0xe492bc
    // 0xe49248: lsl             x1, x1, #1
    // 0xe4924c: str             x1, [SP]
    // 0xe49250: ldur            x1, [fp, #-0x20]
    // 0xe49254: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe49254: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe49258: r0 = setLineDashPattern()
    //     0xe49258: bl              #0xe49410  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setLineDashPattern
    // 0xe4925c: ldur            x0, [fp, #-0x10]
    // 0xe49260: LoadField: r1 = r0->field_1f
    //     0xe49260: ldur            w1, [x0, #0x1f]
    // 0xe49264: DecompressPointer r1
    //     0xe49264: add             x1, x1, HEAP, lsl #32
    // 0xe49268: cmp             w1, NULL
    // 0xe4926c: b.eq            #0xe492e4
    // 0xe49270: r0 = sizeValue()
    //     0xe49270: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe49274: ldur            x1, [fp, #-0x20]
    // 0xe49278: r0 = setLineWidth()
    //     0xe49278: bl              #0xe49358  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setLineWidth
    // 0xe4927c: ldur            x1, [fp, #-0x20]
    // 0xe49280: r0 = strokePath()
    //     0xe49280: bl              #0xe492e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::strokePath
    // 0xe49284: r0 = Null
    //     0xe49284: mov             x0, NULL
    // 0xe49288: LeaveFrame
    //     0xe49288: mov             SP, fp
    //     0xe4928c: ldp             fp, lr, [SP], #0x10
    // 0xe49290: ret
    //     0xe49290: ret             
    // 0xe49294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49294: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49298: b               #0xe48fc4
    // 0xe4929c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4929c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe492a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe492a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe492a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe492a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe492a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe492a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe492ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe492ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe492b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe492b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe492b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe492b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe492b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe492b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe492bc: SaveReg d0
    //     0xe492bc: str             q0, [SP, #-0x10]!
    // 0xe492c0: stp             x0, x2, [SP, #-0x10]!
    // 0xe492c4: r0 = 74
    //     0xe492c4: movz            x0, #0x4a
    // 0xe492c8: r30 = DoubleToIntegerStub
    //     0xe492c8: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xe492cc: LoadField: r30 = r30->field_7
    //     0xe492cc: ldur            lr, [lr, #7]
    // 0xe492d0: blr             lr
    // 0xe492d4: mov             x1, x0
    // 0xe492d8: ldp             x0, x2, [SP], #0x10
    // 0xe492dc: RestoreReg d0
    //     0xe492dc: ldr             q0, [SP], #0x10
    // 0xe492e0: b               #0xe4924c
    // 0xe492e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe492e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ drawShape(/* No info */) {
    // ** addr: 0xe540b0, size: 0x40
    // 0xe540b0: EnterFrame
    //     0xe540b0: stp             fp, lr, [SP, #-0x10]!
    //     0xe540b4: mov             fp, SP
    // 0xe540b8: mov             x0, x1
    // 0xe540bc: mov             x1, x2
    // 0xe540c0: CheckStackOverflow
    //     0xe540c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe540c4: cmp             SP, x16
    //     0xe540c8: b.ls            #0xe540e8
    // 0xe540cc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xe540cc: ldur            w2, [x0, #0x17]
    // 0xe540d0: DecompressPointer r2
    //     0xe540d0: add             x2, x2, HEAP, lsl #32
    // 0xe540d4: r0 = drawShape()
    //     0xe540d4: bl              #0xe497d0  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawShape
    // 0xe540d8: r0 = Null
    //     0xe540d8: mov             x0, NULL
    // 0xe540dc: LeaveFrame
    //     0xe540dc: mov             SP, fp
    //     0xe540e0: ldp             fp, lr, [SP], #0x10
    // 0xe540e4: ret
    //     0xe540e4: ret             
    // 0xe540e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe540e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe540ec: b               #0xe540cc
  }
  factory _ SvgPath.fromRectXml(/* No info */) {
    // ** addr: 0xe7136c, size: 0xbbc
    // 0xe7136c: EnterFrame
    //     0xe7136c: stp             fp, lr, [SP, #-0x10]!
    //     0xe71370: mov             fp, SP
    // 0xe71374: AllocStack(0x70)
    //     0xe71374: sub             SP, SP, #0x70
    // 0xe71378: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3 */)
    //     0xe71378: mov             x4, x2
    //     0xe7137c: mov             x0, x3
    //     0xe71380: stur            x3, [fp, #-0x10]
    //     0xe71384: mov             x3, x5
    //     0xe71388: stur            x2, [fp, #-8]
    // 0xe7138c: CheckStackOverflow
    //     0xe7138c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe71390: cmp             SP, x16
    //     0xe71394: b.ls            #0xe71d00
    // 0xe71398: mov             x2, x4
    // 0xe7139c: mov             x5, x0
    // 0xe713a0: r1 = Null
    //     0xe713a0: mov             x1, NULL
    // 0xe713a4: r0 = SvgBrush.fromXml()
    //     0xe713a4: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe713a8: stur            x0, [fp, #-0x18]
    // 0xe713ac: r16 = 0.000000
    //     0xe713ac: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe713b0: str             x16, [SP]
    // 0xe713b4: ldur            x1, [fp, #-8]
    // 0xe713b8: mov             x3, x0
    // 0xe713bc: r2 = "x"
    //     0xe713bc: ldr             x2, [PP, #0x71a0]  ; [pp+0x71a0] "x"
    // 0xe713c0: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe713c0: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe713c4: ldr             x4, [x4, #0xaf0]
    // 0xe713c8: r0 = getNumeric()
    //     0xe713c8: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe713cc: mov             x1, x0
    // 0xe713d0: r0 = sizeValue()
    //     0xe713d0: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe713d4: stur            d0, [fp, #-0x40]
    // 0xe713d8: r16 = 0.000000
    //     0xe713d8: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe713dc: str             x16, [SP]
    // 0xe713e0: ldur            x1, [fp, #-8]
    // 0xe713e4: ldur            x3, [fp, #-0x18]
    // 0xe713e8: r2 = "y"
    //     0xe713e8: ldr             x2, [PP, #0x71a8]  ; [pp+0x71a8] "y"
    // 0xe713ec: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe713ec: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe713f0: ldr             x4, [x4, #0xaf0]
    // 0xe713f4: r0 = getNumeric()
    //     0xe713f4: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe713f8: mov             x1, x0
    // 0xe713fc: r0 = sizeValue()
    //     0xe713fc: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe71400: stur            d0, [fp, #-0x48]
    // 0xe71404: r16 = 0.000000
    //     0xe71404: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe71408: str             x16, [SP]
    // 0xe7140c: ldur            x1, [fp, #-8]
    // 0xe71410: ldur            x3, [fp, #-0x18]
    // 0xe71414: r2 = "width"
    //     0xe71414: ldr             x2, [PP, #0x7198]  ; [pp+0x7198] "width"
    // 0xe71418: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe71418: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe7141c: ldr             x4, [x4, #0xaf0]
    // 0xe71420: r0 = getNumeric()
    //     0xe71420: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe71424: mov             x1, x0
    // 0xe71428: r0 = sizeValue()
    //     0xe71428: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe7142c: stur            d0, [fp, #-0x50]
    // 0xe71430: r16 = 0.000000
    //     0xe71430: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe71434: str             x16, [SP]
    // 0xe71438: ldur            x1, [fp, #-8]
    // 0xe7143c: ldur            x3, [fp, #-0x18]
    // 0xe71440: r2 = "height"
    //     0xe71440: ldr             x2, [PP, #0x4778]  ; [pp+0x4778] "height"
    // 0xe71444: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe71444: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe71448: ldr             x4, [x4, #0xaf0]
    // 0xe7144c: r0 = getNumeric()
    //     0xe7144c: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe71450: mov             x1, x0
    // 0xe71454: r0 = sizeValue()
    //     0xe71454: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe71458: ldur            x1, [fp, #-8]
    // 0xe7145c: ldur            x3, [fp, #-0x18]
    // 0xe71460: r2 = "rx"
    //     0xe71460: add             x2, PP, #0x26, lsl #12  ; [pp+0x26128] "rx"
    //     0xe71464: ldr             x2, [x2, #0x128]
    // 0xe71468: stur            d0, [fp, #-0x58]
    // 0xe7146c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe7146c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe71470: r0 = getNumeric()
    //     0xe71470: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe71474: cmp             w0, NULL
    // 0xe71478: b.ne            #0xe71484
    // 0xe7147c: r0 = Null
    //     0xe7147c: mov             x0, NULL
    // 0xe71480: b               #0xe714b4
    // 0xe71484: mov             x1, x0
    // 0xe71488: r0 = sizeValue()
    //     0xe71488: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe7148c: r0 = inline_Allocate_Double()
    //     0xe7148c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe71490: add             x0, x0, #0x10
    //     0xe71494: cmp             x1, x0
    //     0xe71498: b.ls            #0xe71d08
    //     0xe7149c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe714a0: sub             x0, x0, #0xf
    //     0xe714a4: movz            x1, #0xe15c
    //     0xe714a8: movk            x1, #0x3, lsl #16
    //     0xe714ac: stur            x1, [x0, #-1]
    // 0xe714b0: StoreField: r0->field_7 = d0
    //     0xe714b0: stur            d0, [x0, #7]
    // 0xe714b4: ldur            x1, [fp, #-8]
    // 0xe714b8: ldur            x3, [fp, #-0x18]
    // 0xe714bc: stur            x0, [fp, #-0x20]
    // 0xe714c0: r2 = "ry"
    //     0xe714c0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26130] "ry"
    //     0xe714c4: ldr             x2, [x2, #0x130]
    // 0xe714c8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe714c8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe714cc: r0 = getNumeric()
    //     0xe714cc: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe714d0: cmp             w0, NULL
    // 0xe714d4: b.ne            #0xe714e0
    // 0xe714d8: r0 = Null
    //     0xe714d8: mov             x0, NULL
    // 0xe714dc: b               #0xe71510
    // 0xe714e0: mov             x1, x0
    // 0xe714e4: r0 = sizeValue()
    //     0xe714e4: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe714e8: r0 = inline_Allocate_Double()
    //     0xe714e8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe714ec: add             x0, x0, #0x10
    //     0xe714f0: cmp             x1, x0
    //     0xe714f4: b.ls            #0xe71d18
    //     0xe714f8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe714fc: sub             x0, x0, #0xf
    //     0xe71500: movz            x1, #0xe15c
    //     0xe71504: movk            x1, #0x3, lsl #16
    //     0xe71508: stur            x1, [x0, #-1]
    // 0xe7150c: StoreField: r0->field_7 = d0
    //     0xe7150c: stur            d0, [x0, #7]
    // 0xe71510: cmp             w0, NULL
    // 0xe71514: b.ne            #0xe71534
    // 0xe71518: ldur            x1, [fp, #-0x20]
    // 0xe7151c: cmp             w1, NULL
    // 0xe71520: b.ne            #0xe7152c
    // 0xe71524: d0 = 0.000000
    //     0xe71524: eor             v0.16b, v0.16b, v0.16b
    // 0xe71528: b               #0xe7153c
    // 0xe7152c: LoadField: d0 = r1->field_7
    //     0xe7152c: ldur            d0, [x1, #7]
    // 0xe71530: b               #0xe7153c
    // 0xe71534: ldur            x1, [fp, #-0x20]
    // 0xe71538: LoadField: d0 = r0->field_7
    //     0xe71538: ldur            d0, [x0, #7]
    // 0xe7153c: stur            d0, [fp, #-0x68]
    // 0xe71540: cmp             w1, NULL
    // 0xe71544: b.ne            #0xe71550
    // 0xe71548: mov             v2.16b, v0.16b
    // 0xe7154c: b               #0xe71558
    // 0xe71550: LoadField: d1 = r1->field_7
    //     0xe71550: ldur            d1, [x1, #7]
    // 0xe71554: mov             v2.16b, v1.16b
    // 0xe71558: d1 = 0.000000
    //     0xe71558: eor             v1.16b, v1.16b, v1.16b
    // 0xe7155c: stur            d2, [fp, #-0x60]
    // 0xe71560: fcmp            d2, d1
    // 0xe71564: b.ne            #0xe71570
    // 0xe71568: fcmp            d0, d1
    // 0xe7156c: b.eq            #0xe71618
    // 0xe71570: r1 = Null
    //     0xe71570: mov             x1, NULL
    // 0xe71574: r2 = 16
    //     0xe71574: movz            x2, #0x10
    // 0xe71578: r0 = AllocateArray()
    //     0xe71578: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7157c: r16 = "a "
    //     0xe7157c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb40] "a "
    //     0xe71580: ldr             x16, [x16, #0xb40]
    // 0xe71584: StoreField: r0->field_f = r16
    //     0xe71584: stur            w16, [x0, #0xf]
    // 0xe71588: ldur            d0, [fp, #-0x60]
    // 0xe7158c: r1 = inline_Allocate_Double()
    //     0xe7158c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe71590: add             x1, x1, #0x10
    //     0xe71594: cmp             x2, x1
    //     0xe71598: b.ls            #0xe71d28
    //     0xe7159c: str             x1, [THR, #0x50]  ; THR::top
    //     0xe715a0: sub             x1, x1, #0xf
    //     0xe715a4: movz            x2, #0xe15c
    //     0xe715a8: movk            x2, #0x3, lsl #16
    //     0xe715ac: stur            x2, [x1, #-1]
    // 0xe715b0: StoreField: r1->field_7 = d0
    //     0xe715b0: stur            d0, [x1, #7]
    // 0xe715b4: StoreField: r0->field_13 = r1
    //     0xe715b4: stur            w1, [x0, #0x13]
    // 0xe715b8: r16 = " "
    //     0xe715b8: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe715bc: ArrayStore: r0[0] = r16  ; List_4
    //     0xe715bc: stur            w16, [x0, #0x17]
    // 0xe715c0: ldur            d1, [fp, #-0x68]
    // 0xe715c4: r2 = inline_Allocate_Double()
    //     0xe715c4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xe715c8: add             x2, x2, #0x10
    //     0xe715cc: cmp             x3, x2
    //     0xe715d0: b.ls            #0xe71d44
    //     0xe715d4: str             x2, [THR, #0x50]  ; THR::top
    //     0xe715d8: sub             x2, x2, #0xf
    //     0xe715dc: movz            x3, #0xe15c
    //     0xe715e0: movk            x3, #0x3, lsl #16
    //     0xe715e4: stur            x3, [x2, #-1]
    // 0xe715e8: StoreField: r2->field_7 = d1
    //     0xe715e8: stur            d1, [x2, #7]
    // 0xe715ec: StoreField: r0->field_1b = r2
    //     0xe715ec: stur            w2, [x0, #0x1b]
    // 0xe715f0: r16 = " 0 0 1 "
    //     0xe715f0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb48] " 0 0 1 "
    //     0xe715f4: ldr             x16, [x16, #0xb48]
    // 0xe715f8: StoreField: r0->field_1f = r16
    //     0xe715f8: stur            w16, [x0, #0x1f]
    // 0xe715fc: StoreField: r0->field_23 = r1
    //     0xe715fc: stur            w1, [x0, #0x23]
    // 0xe71600: r16 = " "
    //     0xe71600: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe71604: StoreField: r0->field_27 = r16
    //     0xe71604: stur            w16, [x0, #0x27]
    // 0xe71608: StoreField: r0->field_2b = r2
    //     0xe71608: stur            w2, [x0, #0x2b]
    // 0xe7160c: str             x0, [SP]
    // 0xe71610: r0 = _interpolate()
    //     0xe71610: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe71614: b               #0xe7161c
    // 0xe71618: r0 = ""
    //     0xe71618: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe7161c: ldur            d0, [fp, #-0x60]
    // 0xe71620: d1 = 0.000000
    //     0xe71620: eor             v1.16b, v1.16b, v1.16b
    // 0xe71624: stur            x0, [fp, #-0x20]
    // 0xe71628: fcmp            d0, d1
    // 0xe7162c: b.eq            #0xe71638
    // 0xe71630: ldur            d2, [fp, #-0x68]
    // 0xe71634: b               #0xe71644
    // 0xe71638: ldur            d2, [fp, #-0x68]
    // 0xe7163c: fcmp            d2, d1
    // 0xe71640: b.eq            #0xe71718
    // 0xe71644: r1 = Null
    //     0xe71644: mov             x1, NULL
    // 0xe71648: r2 = 16
    //     0xe71648: movz            x2, #0x10
    // 0xe7164c: r0 = AllocateArray()
    //     0xe7164c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe71650: r16 = "a "
    //     0xe71650: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb40] "a "
    //     0xe71654: ldr             x16, [x16, #0xb40]
    // 0xe71658: StoreField: r0->field_f = r16
    //     0xe71658: stur            w16, [x0, #0xf]
    // 0xe7165c: ldur            d0, [fp, #-0x60]
    // 0xe71660: r1 = inline_Allocate_Double()
    //     0xe71660: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe71664: add             x1, x1, #0x10
    //     0xe71668: cmp             x2, x1
    //     0xe7166c: b.ls            #0xe71d60
    //     0xe71670: str             x1, [THR, #0x50]  ; THR::top
    //     0xe71674: sub             x1, x1, #0xf
    //     0xe71678: movz            x2, #0xe15c
    //     0xe7167c: movk            x2, #0x3, lsl #16
    //     0xe71680: stur            x2, [x1, #-1]
    // 0xe71684: StoreField: r1->field_7 = d0
    //     0xe71684: stur            d0, [x1, #7]
    // 0xe71688: StoreField: r0->field_13 = r1
    //     0xe71688: stur            w1, [x0, #0x13]
    // 0xe7168c: r16 = " "
    //     0xe7168c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe71690: ArrayStore: r0[0] = r16  ; List_4
    //     0xe71690: stur            w16, [x0, #0x17]
    // 0xe71694: ldur            d1, [fp, #-0x68]
    // 0xe71698: r1 = inline_Allocate_Double()
    //     0xe71698: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe7169c: add             x1, x1, #0x10
    //     0xe716a0: cmp             x2, x1
    //     0xe716a4: b.ls            #0xe71d7c
    //     0xe716a8: str             x1, [THR, #0x50]  ; THR::top
    //     0xe716ac: sub             x1, x1, #0xf
    //     0xe716b0: movz            x2, #0xe15c
    //     0xe716b4: movk            x2, #0x3, lsl #16
    //     0xe716b8: stur            x2, [x1, #-1]
    // 0xe716bc: StoreField: r1->field_7 = d1
    //     0xe716bc: stur            d1, [x1, #7]
    // 0xe716c0: StoreField: r0->field_1b = r1
    //     0xe716c0: stur            w1, [x0, #0x1b]
    // 0xe716c4: r16 = " 0 0 1 "
    //     0xe716c4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb48] " 0 0 1 "
    //     0xe716c8: ldr             x16, [x16, #0xb48]
    // 0xe716cc: StoreField: r0->field_1f = r16
    //     0xe716cc: stur            w16, [x0, #0x1f]
    // 0xe716d0: fneg            d2, d0
    // 0xe716d4: r2 = inline_Allocate_Double()
    //     0xe716d4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xe716d8: add             x2, x2, #0x10
    //     0xe716dc: cmp             x3, x2
    //     0xe716e0: b.ls            #0xe71d98
    //     0xe716e4: str             x2, [THR, #0x50]  ; THR::top
    //     0xe716e8: sub             x2, x2, #0xf
    //     0xe716ec: movz            x3, #0xe15c
    //     0xe716f0: movk            x3, #0x3, lsl #16
    //     0xe716f4: stur            x3, [x2, #-1]
    // 0xe716f8: StoreField: r2->field_7 = d2
    //     0xe716f8: stur            d2, [x2, #7]
    // 0xe716fc: StoreField: r0->field_23 = r2
    //     0xe716fc: stur            w2, [x0, #0x23]
    // 0xe71700: r16 = " "
    //     0xe71700: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe71704: StoreField: r0->field_27 = r16
    //     0xe71704: stur            w16, [x0, #0x27]
    // 0xe71708: StoreField: r0->field_2b = r1
    //     0xe71708: stur            w1, [x0, #0x2b]
    // 0xe7170c: str             x0, [SP]
    // 0xe71710: r0 = _interpolate()
    //     0xe71710: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe71714: b               #0xe7171c
    // 0xe71718: r0 = ""
    //     0xe71718: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe7171c: ldur            d0, [fp, #-0x60]
    // 0xe71720: d1 = 0.000000
    //     0xe71720: eor             v1.16b, v1.16b, v1.16b
    // 0xe71724: stur            x0, [fp, #-0x28]
    // 0xe71728: fcmp            d0, d1
    // 0xe7172c: b.eq            #0xe71738
    // 0xe71730: ldur            d2, [fp, #-0x68]
    // 0xe71734: b               #0xe71744
    // 0xe71738: ldur            d2, [fp, #-0x68]
    // 0xe7173c: fcmp            d2, d1
    // 0xe71740: b.eq            #0xe71844
    // 0xe71744: r1 = Null
    //     0xe71744: mov             x1, NULL
    // 0xe71748: r2 = 16
    //     0xe71748: movz            x2, #0x10
    // 0xe7174c: r0 = AllocateArray()
    //     0xe7174c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe71750: r16 = "a "
    //     0xe71750: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb40] "a "
    //     0xe71754: ldr             x16, [x16, #0xb40]
    // 0xe71758: StoreField: r0->field_f = r16
    //     0xe71758: stur            w16, [x0, #0xf]
    // 0xe7175c: ldur            d0, [fp, #-0x60]
    // 0xe71760: r1 = inline_Allocate_Double()
    //     0xe71760: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe71764: add             x1, x1, #0x10
    //     0xe71768: cmp             x2, x1
    //     0xe7176c: b.ls            #0xe71dbc
    //     0xe71770: str             x1, [THR, #0x50]  ; THR::top
    //     0xe71774: sub             x1, x1, #0xf
    //     0xe71778: movz            x2, #0xe15c
    //     0xe7177c: movk            x2, #0x3, lsl #16
    //     0xe71780: stur            x2, [x1, #-1]
    // 0xe71784: StoreField: r1->field_7 = d0
    //     0xe71784: stur            d0, [x1, #7]
    // 0xe71788: StoreField: r0->field_13 = r1
    //     0xe71788: stur            w1, [x0, #0x13]
    // 0xe7178c: r16 = " "
    //     0xe7178c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe71790: ArrayStore: r0[0] = r16  ; List_4
    //     0xe71790: stur            w16, [x0, #0x17]
    // 0xe71794: ldur            d1, [fp, #-0x68]
    // 0xe71798: r1 = inline_Allocate_Double()
    //     0xe71798: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe7179c: add             x1, x1, #0x10
    //     0xe717a0: cmp             x2, x1
    //     0xe717a4: b.ls            #0xe71dd8
    //     0xe717a8: str             x1, [THR, #0x50]  ; THR::top
    //     0xe717ac: sub             x1, x1, #0xf
    //     0xe717b0: movz            x2, #0xe15c
    //     0xe717b4: movk            x2, #0x3, lsl #16
    //     0xe717b8: stur            x2, [x1, #-1]
    // 0xe717bc: StoreField: r1->field_7 = d1
    //     0xe717bc: stur            d1, [x1, #7]
    // 0xe717c0: StoreField: r0->field_1b = r1
    //     0xe717c0: stur            w1, [x0, #0x1b]
    // 0xe717c4: r16 = " 0 0 1 "
    //     0xe717c4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb48] " 0 0 1 "
    //     0xe717c8: ldr             x16, [x16, #0xb48]
    // 0xe717cc: StoreField: r0->field_1f = r16
    //     0xe717cc: stur            w16, [x0, #0x1f]
    // 0xe717d0: fneg            d2, d0
    // 0xe717d4: r1 = inline_Allocate_Double()
    //     0xe717d4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe717d8: add             x1, x1, #0x10
    //     0xe717dc: cmp             x2, x1
    //     0xe717e0: b.ls            #0xe71df4
    //     0xe717e4: str             x1, [THR, #0x50]  ; THR::top
    //     0xe717e8: sub             x1, x1, #0xf
    //     0xe717ec: movz            x2, #0xe15c
    //     0xe717f0: movk            x2, #0x3, lsl #16
    //     0xe717f4: stur            x2, [x1, #-1]
    // 0xe717f8: StoreField: r1->field_7 = d2
    //     0xe717f8: stur            d2, [x1, #7]
    // 0xe717fc: StoreField: r0->field_23 = r1
    //     0xe717fc: stur            w1, [x0, #0x23]
    // 0xe71800: r16 = " "
    //     0xe71800: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe71804: StoreField: r0->field_27 = r16
    //     0xe71804: stur            w16, [x0, #0x27]
    // 0xe71808: fneg            d2, d1
    // 0xe7180c: r1 = inline_Allocate_Double()
    //     0xe7180c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe71810: add             x1, x1, #0x10
    //     0xe71814: cmp             x2, x1
    //     0xe71818: b.ls            #0xe71e18
    //     0xe7181c: str             x1, [THR, #0x50]  ; THR::top
    //     0xe71820: sub             x1, x1, #0xf
    //     0xe71824: movz            x2, #0xe15c
    //     0xe71828: movk            x2, #0x3, lsl #16
    //     0xe7182c: stur            x2, [x1, #-1]
    // 0xe71830: StoreField: r1->field_7 = d2
    //     0xe71830: stur            d2, [x1, #7]
    // 0xe71834: StoreField: r0->field_2b = r1
    //     0xe71834: stur            w1, [x0, #0x2b]
    // 0xe71838: str             x0, [SP]
    // 0xe7183c: r0 = _interpolate()
    //     0xe7183c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe71840: b               #0xe71848
    // 0xe71844: r0 = ""
    //     0xe71844: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe71848: ldur            d0, [fp, #-0x60]
    // 0xe7184c: d1 = 0.000000
    //     0xe7184c: eor             v1.16b, v1.16b, v1.16b
    // 0xe71850: stur            x0, [fp, #-0x30]
    // 0xe71854: fcmp            d0, d1
    // 0xe71858: b.eq            #0xe71864
    // 0xe7185c: ldur            d2, [fp, #-0x68]
    // 0xe71860: b               #0xe71870
    // 0xe71864: ldur            d2, [fp, #-0x68]
    // 0xe71868: fcmp            d2, d1
    // 0xe7186c: b.eq            #0xe71944
    // 0xe71870: r1 = Null
    //     0xe71870: mov             x1, NULL
    // 0xe71874: r2 = 16
    //     0xe71874: movz            x2, #0x10
    // 0xe71878: r0 = AllocateArray()
    //     0xe71878: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7187c: r16 = "a "
    //     0xe7187c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb40] "a "
    //     0xe71880: ldr             x16, [x16, #0xb40]
    // 0xe71884: StoreField: r0->field_f = r16
    //     0xe71884: stur            w16, [x0, #0xf]
    // 0xe71888: ldur            d0, [fp, #-0x60]
    // 0xe7188c: r1 = inline_Allocate_Double()
    //     0xe7188c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe71890: add             x1, x1, #0x10
    //     0xe71894: cmp             x2, x1
    //     0xe71898: b.ls            #0xe71e3c
    //     0xe7189c: str             x1, [THR, #0x50]  ; THR::top
    //     0xe718a0: sub             x1, x1, #0xf
    //     0xe718a4: movz            x2, #0xe15c
    //     0xe718a8: movk            x2, #0x3, lsl #16
    //     0xe718ac: stur            x2, [x1, #-1]
    // 0xe718b0: StoreField: r1->field_7 = d0
    //     0xe718b0: stur            d0, [x1, #7]
    // 0xe718b4: StoreField: r0->field_13 = r1
    //     0xe718b4: stur            w1, [x0, #0x13]
    // 0xe718b8: r16 = " "
    //     0xe718b8: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe718bc: ArrayStore: r0[0] = r16  ; List_4
    //     0xe718bc: stur            w16, [x0, #0x17]
    // 0xe718c0: ldur            d1, [fp, #-0x68]
    // 0xe718c4: r2 = inline_Allocate_Double()
    //     0xe718c4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xe718c8: add             x2, x2, #0x10
    //     0xe718cc: cmp             x3, x2
    //     0xe718d0: b.ls            #0xe71e58
    //     0xe718d4: str             x2, [THR, #0x50]  ; THR::top
    //     0xe718d8: sub             x2, x2, #0xf
    //     0xe718dc: movz            x3, #0xe15c
    //     0xe718e0: movk            x3, #0x3, lsl #16
    //     0xe718e4: stur            x3, [x2, #-1]
    // 0xe718e8: StoreField: r2->field_7 = d1
    //     0xe718e8: stur            d1, [x2, #7]
    // 0xe718ec: StoreField: r0->field_1b = r2
    //     0xe718ec: stur            w2, [x0, #0x1b]
    // 0xe718f0: r16 = " 0 0 1 "
    //     0xe718f0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb48] " 0 0 1 "
    //     0xe718f4: ldr             x16, [x16, #0xb48]
    // 0xe718f8: StoreField: r0->field_1f = r16
    //     0xe718f8: stur            w16, [x0, #0x1f]
    // 0xe718fc: StoreField: r0->field_23 = r1
    //     0xe718fc: stur            w1, [x0, #0x23]
    // 0xe71900: r16 = " "
    //     0xe71900: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe71904: StoreField: r0->field_27 = r16
    //     0xe71904: stur            w16, [x0, #0x27]
    // 0xe71908: fneg            d2, d1
    // 0xe7190c: r1 = inline_Allocate_Double()
    //     0xe7190c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe71910: add             x1, x1, #0x10
    //     0xe71914: cmp             x2, x1
    //     0xe71918: b.ls            #0xe71e74
    //     0xe7191c: str             x1, [THR, #0x50]  ; THR::top
    //     0xe71920: sub             x1, x1, #0xf
    //     0xe71924: movz            x2, #0xe15c
    //     0xe71928: movk            x2, #0x3, lsl #16
    //     0xe7192c: stur            x2, [x1, #-1]
    // 0xe71930: StoreField: r1->field_7 = d2
    //     0xe71930: stur            d2, [x1, #7]
    // 0xe71934: StoreField: r0->field_2b = r1
    //     0xe71934: stur            w1, [x0, #0x2b]
    // 0xe71938: str             x0, [SP]
    // 0xe7193c: r0 = _interpolate()
    //     0xe7193c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe71940: b               #0xe71948
    // 0xe71944: r0 = ""
    //     0xe71944: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe71948: ldur            x3, [fp, #-0x10]
    // 0xe7194c: ldur            x5, [fp, #-0x18]
    // 0xe71950: ldur            d5, [fp, #-0x40]
    // 0xe71954: ldur            d4, [fp, #-0x48]
    // 0xe71958: ldur            d3, [fp, #-0x50]
    // 0xe7195c: ldur            d2, [fp, #-0x58]
    // 0xe71960: ldur            d1, [fp, #-0x68]
    // 0xe71964: ldur            d0, [fp, #-0x60]
    // 0xe71968: stur            x0, [fp, #-0x38]
    // 0xe7196c: r1 = Null
    //     0xe7196c: mov             x1, NULL
    // 0xe71970: r2 = 34
    //     0xe71970: movz            x2, #0x22
    // 0xe71974: r0 = AllocateArray()
    //     0xe71974: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe71978: mov             x2, x0
    // 0xe7197c: r16 = "M"
    //     0xe7197c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b808] "M"
    //     0xe71980: ldr             x16, [x16, #0x808]
    // 0xe71984: StoreField: r2->field_f = r16
    //     0xe71984: stur            w16, [x2, #0xf]
    // 0xe71988: ldur            d1, [fp, #-0x40]
    // 0xe7198c: ldur            d0, [fp, #-0x60]
    // 0xe71990: fadd            d2, d1, d0
    // 0xe71994: r0 = inline_Allocate_Double()
    //     0xe71994: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe71998: add             x0, x0, #0x10
    //     0xe7199c: cmp             x1, x0
    //     0xe719a0: b.ls            #0xe71e98
    //     0xe719a4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe719a8: sub             x0, x0, #0xf
    //     0xe719ac: movz            x1, #0xe15c
    //     0xe719b0: movk            x1, #0x3, lsl #16
    //     0xe719b4: stur            x1, [x0, #-1]
    // 0xe719b8: StoreField: r0->field_7 = d2
    //     0xe719b8: stur            d2, [x0, #7]
    // 0xe719bc: mov             x1, x2
    // 0xe719c0: ArrayStore: r1[1] = r0  ; List_4
    //     0xe719c0: add             x25, x1, #0x13
    //     0xe719c4: str             w0, [x25]
    //     0xe719c8: tbz             w0, #0, #0xe719e4
    //     0xe719cc: ldurb           w16, [x1, #-1]
    //     0xe719d0: ldurb           w17, [x0, #-1]
    //     0xe719d4: and             x16, x17, x16, lsr #2
    //     0xe719d8: tst             x16, HEAP, lsr #32
    //     0xe719dc: b.eq            #0xe719e4
    //     0xe719e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe719e4: r16 = " "
    //     0xe719e4: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe719e8: ArrayStore: r2[0] = r16  ; List_4
    //     0xe719e8: stur            w16, [x2, #0x17]
    // 0xe719ec: ldur            d1, [fp, #-0x48]
    // 0xe719f0: r0 = inline_Allocate_Double()
    //     0xe719f0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe719f4: add             x0, x0, #0x10
    //     0xe719f8: cmp             x1, x0
    //     0xe719fc: b.ls            #0xe71eb0
    //     0xe71a00: str             x0, [THR, #0x50]  ; THR::top
    //     0xe71a04: sub             x0, x0, #0xf
    //     0xe71a08: movz            x1, #0xe15c
    //     0xe71a0c: movk            x1, #0x3, lsl #16
    //     0xe71a10: stur            x1, [x0, #-1]
    // 0xe71a14: StoreField: r0->field_7 = d1
    //     0xe71a14: stur            d1, [x0, #7]
    // 0xe71a18: mov             x1, x2
    // 0xe71a1c: ArrayStore: r1[3] = r0  ; List_4
    //     0xe71a1c: add             x25, x1, #0x1b
    //     0xe71a20: str             w0, [x25]
    //     0xe71a24: tbz             w0, #0, #0xe71a40
    //     0xe71a28: ldurb           w16, [x1, #-1]
    //     0xe71a2c: ldurb           w17, [x0, #-1]
    //     0xe71a30: and             x16, x17, x16, lsr #2
    //     0xe71a34: tst             x16, HEAP, lsr #32
    //     0xe71a38: b.eq            #0xe71a40
    //     0xe71a3c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71a40: r16 = "h"
    //     0xe71a40: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b7e0] "h"
    //     0xe71a44: ldr             x16, [x16, #0x7e0]
    // 0xe71a48: StoreField: r2->field_1f = r16
    //     0xe71a48: stur            w16, [x2, #0x1f]
    // 0xe71a4c: d1 = 2.000000
    //     0xe71a4c: fmov            d1, #2.00000000
    // 0xe71a50: fmul            d2, d0, d1
    // 0xe71a54: ldur            d0, [fp, #-0x50]
    // 0xe71a58: fsub            d3, d0, d2
    // 0xe71a5c: r0 = inline_Allocate_Double()
    //     0xe71a5c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe71a60: add             x0, x0, #0x10
    //     0xe71a64: cmp             x1, x0
    //     0xe71a68: b.ls            #0xe71ec8
    //     0xe71a6c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe71a70: sub             x0, x0, #0xf
    //     0xe71a74: movz            x1, #0xe15c
    //     0xe71a78: movk            x1, #0x3, lsl #16
    //     0xe71a7c: stur            x1, [x0, #-1]
    // 0xe71a80: StoreField: r0->field_7 = d3
    //     0xe71a80: stur            d3, [x0, #7]
    // 0xe71a84: mov             x1, x2
    // 0xe71a88: ArrayStore: r1[5] = r0  ; List_4
    //     0xe71a88: add             x25, x1, #0x23
    //     0xe71a8c: str             w0, [x25]
    //     0xe71a90: tbz             w0, #0, #0xe71aac
    //     0xe71a94: ldurb           w16, [x1, #-1]
    //     0xe71a98: ldurb           w17, [x0, #-1]
    //     0xe71a9c: and             x16, x17, x16, lsr #2
    //     0xe71aa0: tst             x16, HEAP, lsr #32
    //     0xe71aa4: b.eq            #0xe71aac
    //     0xe71aa8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71aac: mov             x1, x2
    // 0xe71ab0: ldur            x0, [fp, #-0x20]
    // 0xe71ab4: ArrayStore: r1[6] = r0  ; List_4
    //     0xe71ab4: add             x25, x1, #0x27
    //     0xe71ab8: str             w0, [x25]
    //     0xe71abc: tbz             w0, #0, #0xe71ad8
    //     0xe71ac0: ldurb           w16, [x1, #-1]
    //     0xe71ac4: ldurb           w17, [x0, #-1]
    //     0xe71ac8: and             x16, x17, x16, lsr #2
    //     0xe71acc: tst             x16, HEAP, lsr #32
    //     0xe71ad0: b.eq            #0xe71ad8
    //     0xe71ad4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71ad8: r16 = "v"
    //     0xe71ad8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b820] "v"
    //     0xe71adc: ldr             x16, [x16, #0x820]
    // 0xe71ae0: StoreField: r2->field_2b = r16
    //     0xe71ae0: stur            w16, [x2, #0x2b]
    // 0xe71ae4: ldur            d0, [fp, #-0x68]
    // 0xe71ae8: fmul            d2, d0, d1
    // 0xe71aec: ldur            d0, [fp, #-0x58]
    // 0xe71af0: fsub            d1, d0, d2
    // 0xe71af4: r0 = inline_Allocate_Double()
    //     0xe71af4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe71af8: add             x0, x0, #0x10
    //     0xe71afc: cmp             x1, x0
    //     0xe71b00: b.ls            #0xe71ee0
    //     0xe71b04: str             x0, [THR, #0x50]  ; THR::top
    //     0xe71b08: sub             x0, x0, #0xf
    //     0xe71b0c: movz            x1, #0xe15c
    //     0xe71b10: movk            x1, #0x3, lsl #16
    //     0xe71b14: stur            x1, [x0, #-1]
    // 0xe71b18: StoreField: r0->field_7 = d1
    //     0xe71b18: stur            d1, [x0, #7]
    // 0xe71b1c: mov             x1, x2
    // 0xe71b20: ArrayStore: r1[8] = r0  ; List_4
    //     0xe71b20: add             x25, x1, #0x2f
    //     0xe71b24: str             w0, [x25]
    //     0xe71b28: tbz             w0, #0, #0xe71b44
    //     0xe71b2c: ldurb           w16, [x1, #-1]
    //     0xe71b30: ldurb           w17, [x0, #-1]
    //     0xe71b34: and             x16, x17, x16, lsr #2
    //     0xe71b38: tst             x16, HEAP, lsr #32
    //     0xe71b3c: b.eq            #0xe71b44
    //     0xe71b40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71b44: mov             x1, x2
    // 0xe71b48: ldur            x0, [fp, #-0x28]
    // 0xe71b4c: ArrayStore: r1[9] = r0  ; List_4
    //     0xe71b4c: add             x25, x1, #0x33
    //     0xe71b50: str             w0, [x25]
    //     0xe71b54: tbz             w0, #0, #0xe71b70
    //     0xe71b58: ldurb           w16, [x1, #-1]
    //     0xe71b5c: ldurb           w17, [x0, #-1]
    //     0xe71b60: and             x16, x17, x16, lsr #2
    //     0xe71b64: tst             x16, HEAP, lsr #32
    //     0xe71b68: b.eq            #0xe71b70
    //     0xe71b6c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71b70: r16 = "h"
    //     0xe71b70: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b7e0] "h"
    //     0xe71b74: ldr             x16, [x16, #0x7e0]
    // 0xe71b78: StoreField: r2->field_37 = r16
    //     0xe71b78: stur            w16, [x2, #0x37]
    // 0xe71b7c: fneg            d0, d3
    // 0xe71b80: r0 = inline_Allocate_Double()
    //     0xe71b80: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe71b84: add             x0, x0, #0x10
    //     0xe71b88: cmp             x1, x0
    //     0xe71b8c: b.ls            #0xe71ef8
    //     0xe71b90: str             x0, [THR, #0x50]  ; THR::top
    //     0xe71b94: sub             x0, x0, #0xf
    //     0xe71b98: movz            x1, #0xe15c
    //     0xe71b9c: movk            x1, #0x3, lsl #16
    //     0xe71ba0: stur            x1, [x0, #-1]
    // 0xe71ba4: StoreField: r0->field_7 = d0
    //     0xe71ba4: stur            d0, [x0, #7]
    // 0xe71ba8: mov             x1, x2
    // 0xe71bac: ArrayStore: r1[11] = r0  ; List_4
    //     0xe71bac: add             x25, x1, #0x3b
    //     0xe71bb0: str             w0, [x25]
    //     0xe71bb4: tbz             w0, #0, #0xe71bd0
    //     0xe71bb8: ldurb           w16, [x1, #-1]
    //     0xe71bbc: ldurb           w17, [x0, #-1]
    //     0xe71bc0: and             x16, x17, x16, lsr #2
    //     0xe71bc4: tst             x16, HEAP, lsr #32
    //     0xe71bc8: b.eq            #0xe71bd0
    //     0xe71bcc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71bd0: mov             x1, x2
    // 0xe71bd4: ldur            x0, [fp, #-0x30]
    // 0xe71bd8: ArrayStore: r1[12] = r0  ; List_4
    //     0xe71bd8: add             x25, x1, #0x3f
    //     0xe71bdc: str             w0, [x25]
    //     0xe71be0: tbz             w0, #0, #0xe71bfc
    //     0xe71be4: ldurb           w16, [x1, #-1]
    //     0xe71be8: ldurb           w17, [x0, #-1]
    //     0xe71bec: and             x16, x17, x16, lsr #2
    //     0xe71bf0: tst             x16, HEAP, lsr #32
    //     0xe71bf4: b.eq            #0xe71bfc
    //     0xe71bf8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71bfc: r16 = "v"
    //     0xe71bfc: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b820] "v"
    //     0xe71c00: ldr             x16, [x16, #0x820]
    // 0xe71c04: StoreField: r2->field_43 = r16
    //     0xe71c04: stur            w16, [x2, #0x43]
    // 0xe71c08: fneg            d0, d1
    // 0xe71c0c: r0 = inline_Allocate_Double()
    //     0xe71c0c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe71c10: add             x0, x0, #0x10
    //     0xe71c14: cmp             x1, x0
    //     0xe71c18: b.ls            #0xe71f10
    //     0xe71c1c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe71c20: sub             x0, x0, #0xf
    //     0xe71c24: movz            x1, #0xe15c
    //     0xe71c28: movk            x1, #0x3, lsl #16
    //     0xe71c2c: stur            x1, [x0, #-1]
    // 0xe71c30: StoreField: r0->field_7 = d0
    //     0xe71c30: stur            d0, [x0, #7]
    // 0xe71c34: mov             x1, x2
    // 0xe71c38: ArrayStore: r1[14] = r0  ; List_4
    //     0xe71c38: add             x25, x1, #0x47
    //     0xe71c3c: str             w0, [x25]
    //     0xe71c40: tbz             w0, #0, #0xe71c5c
    //     0xe71c44: ldurb           w16, [x1, #-1]
    //     0xe71c48: ldurb           w17, [x0, #-1]
    //     0xe71c4c: and             x16, x17, x16, lsr #2
    //     0xe71c50: tst             x16, HEAP, lsr #32
    //     0xe71c54: b.eq            #0xe71c5c
    //     0xe71c58: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71c5c: mov             x1, x2
    // 0xe71c60: ldur            x0, [fp, #-0x38]
    // 0xe71c64: ArrayStore: r1[15] = r0  ; List_4
    //     0xe71c64: add             x25, x1, #0x4b
    //     0xe71c68: str             w0, [x25]
    //     0xe71c6c: tbz             w0, #0, #0xe71c88
    //     0xe71c70: ldurb           w16, [x1, #-1]
    //     0xe71c74: ldurb           w17, [x0, #-1]
    //     0xe71c78: and             x16, x17, x16, lsr #2
    //     0xe71c7c: tst             x16, HEAP, lsr #32
    //     0xe71c80: b.eq            #0xe71c88
    //     0xe71c84: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe71c88: r16 = "z"
    //     0xe71c88: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b828] "z"
    //     0xe71c8c: ldr             x16, [x16, #0x828]
    // 0xe71c90: StoreField: r2->field_4f = r16
    //     0xe71c90: stur            w16, [x2, #0x4f]
    // 0xe71c94: str             x2, [SP]
    // 0xe71c98: r0 = _interpolate()
    //     0xe71c98: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe71c9c: ldur            x2, [fp, #-8]
    // 0xe71ca0: ldur            x3, [fp, #-0x10]
    // 0xe71ca4: ldur            x5, [fp, #-0x18]
    // 0xe71ca8: r1 = Null
    //     0xe71ca8: mov             x1, NULL
    // 0xe71cac: stur            x0, [fp, #-0x20]
    // 0xe71cb0: r0 = SvgClipPath.fromXml()
    //     0xe71cb0: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe71cb4: ldur            x2, [fp, #-8]
    // 0xe71cb8: r1 = Null
    //     0xe71cb8: mov             x1, NULL
    // 0xe71cbc: stur            x0, [fp, #-8]
    // 0xe71cc0: r0 = SvgTransform.fromXml()
    //     0xe71cc0: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe71cc4: stur            x0, [fp, #-0x28]
    // 0xe71cc8: r0 = SvgPath()
    //     0xe71cc8: bl              #0xe71f28  ; AllocateSvgPathStub -> SvgPath (size=0x1c)
    // 0xe71ccc: ldur            x1, [fp, #-0x20]
    // 0xe71cd0: ArrayStore: r0[0] = r1  ; List_4
    //     0xe71cd0: stur            w1, [x0, #0x17]
    // 0xe71cd4: ldur            x1, [fp, #-0x18]
    // 0xe71cd8: StoreField: r0->field_7 = r1
    //     0xe71cd8: stur            w1, [x0, #7]
    // 0xe71cdc: ldur            x1, [fp, #-8]
    // 0xe71ce0: StoreField: r0->field_b = r1
    //     0xe71ce0: stur            w1, [x0, #0xb]
    // 0xe71ce4: ldur            x1, [fp, #-0x28]
    // 0xe71ce8: StoreField: r0->field_f = r1
    //     0xe71ce8: stur            w1, [x0, #0xf]
    // 0xe71cec: ldur            x1, [fp, #-0x10]
    // 0xe71cf0: StoreField: r0->field_13 = r1
    //     0xe71cf0: stur            w1, [x0, #0x13]
    // 0xe71cf4: LeaveFrame
    //     0xe71cf4: mov             SP, fp
    //     0xe71cf8: ldp             fp, lr, [SP], #0x10
    // 0xe71cfc: ret
    //     0xe71cfc: ret             
    // 0xe71d00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe71d00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe71d04: b               #0xe71398
    // 0xe71d08: SaveReg d0
    //     0xe71d08: str             q0, [SP, #-0x10]!
    // 0xe71d0c: r0 = AllocateDouble()
    //     0xe71d0c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71d10: RestoreReg d0
    //     0xe71d10: ldr             q0, [SP], #0x10
    // 0xe71d14: b               #0xe714b0
    // 0xe71d18: SaveReg d0
    //     0xe71d18: str             q0, [SP, #-0x10]!
    // 0xe71d1c: r0 = AllocateDouble()
    //     0xe71d1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71d20: RestoreReg d0
    //     0xe71d20: ldr             q0, [SP], #0x10
    // 0xe71d24: b               #0xe7150c
    // 0xe71d28: SaveReg d0
    //     0xe71d28: str             q0, [SP, #-0x10]!
    // 0xe71d2c: SaveReg r0
    //     0xe71d2c: str             x0, [SP, #-8]!
    // 0xe71d30: r0 = AllocateDouble()
    //     0xe71d30: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71d34: mov             x1, x0
    // 0xe71d38: RestoreReg r0
    //     0xe71d38: ldr             x0, [SP], #8
    // 0xe71d3c: RestoreReg d0
    //     0xe71d3c: ldr             q0, [SP], #0x10
    // 0xe71d40: b               #0xe715b0
    // 0xe71d44: stp             q0, q1, [SP, #-0x20]!
    // 0xe71d48: stp             x0, x1, [SP, #-0x10]!
    // 0xe71d4c: r0 = AllocateDouble()
    //     0xe71d4c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71d50: mov             x2, x0
    // 0xe71d54: ldp             x0, x1, [SP], #0x10
    // 0xe71d58: ldp             q0, q1, [SP], #0x20
    // 0xe71d5c: b               #0xe715e8
    // 0xe71d60: SaveReg d0
    //     0xe71d60: str             q0, [SP, #-0x10]!
    // 0xe71d64: SaveReg r0
    //     0xe71d64: str             x0, [SP, #-8]!
    // 0xe71d68: r0 = AllocateDouble()
    //     0xe71d68: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71d6c: mov             x1, x0
    // 0xe71d70: RestoreReg r0
    //     0xe71d70: ldr             x0, [SP], #8
    // 0xe71d74: RestoreReg d0
    //     0xe71d74: ldr             q0, [SP], #0x10
    // 0xe71d78: b               #0xe71684
    // 0xe71d7c: stp             q0, q1, [SP, #-0x20]!
    // 0xe71d80: SaveReg r0
    //     0xe71d80: str             x0, [SP, #-8]!
    // 0xe71d84: r0 = AllocateDouble()
    //     0xe71d84: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71d88: mov             x1, x0
    // 0xe71d8c: RestoreReg r0
    //     0xe71d8c: ldr             x0, [SP], #8
    // 0xe71d90: ldp             q0, q1, [SP], #0x20
    // 0xe71d94: b               #0xe716bc
    // 0xe71d98: stp             q1, q2, [SP, #-0x20]!
    // 0xe71d9c: SaveReg d0
    //     0xe71d9c: str             q0, [SP, #-0x10]!
    // 0xe71da0: stp             x0, x1, [SP, #-0x10]!
    // 0xe71da4: r0 = AllocateDouble()
    //     0xe71da4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71da8: mov             x2, x0
    // 0xe71dac: ldp             x0, x1, [SP], #0x10
    // 0xe71db0: RestoreReg d0
    //     0xe71db0: ldr             q0, [SP], #0x10
    // 0xe71db4: ldp             q1, q2, [SP], #0x20
    // 0xe71db8: b               #0xe716f8
    // 0xe71dbc: SaveReg d0
    //     0xe71dbc: str             q0, [SP, #-0x10]!
    // 0xe71dc0: SaveReg r0
    //     0xe71dc0: str             x0, [SP, #-8]!
    // 0xe71dc4: r0 = AllocateDouble()
    //     0xe71dc4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71dc8: mov             x1, x0
    // 0xe71dcc: RestoreReg r0
    //     0xe71dcc: ldr             x0, [SP], #8
    // 0xe71dd0: RestoreReg d0
    //     0xe71dd0: ldr             q0, [SP], #0x10
    // 0xe71dd4: b               #0xe71784
    // 0xe71dd8: stp             q0, q1, [SP, #-0x20]!
    // 0xe71ddc: SaveReg r0
    //     0xe71ddc: str             x0, [SP, #-8]!
    // 0xe71de0: r0 = AllocateDouble()
    //     0xe71de0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71de4: mov             x1, x0
    // 0xe71de8: RestoreReg r0
    //     0xe71de8: ldr             x0, [SP], #8
    // 0xe71dec: ldp             q0, q1, [SP], #0x20
    // 0xe71df0: b               #0xe717bc
    // 0xe71df4: stp             q1, q2, [SP, #-0x20]!
    // 0xe71df8: SaveReg d0
    //     0xe71df8: str             q0, [SP, #-0x10]!
    // 0xe71dfc: SaveReg r0
    //     0xe71dfc: str             x0, [SP, #-8]!
    // 0xe71e00: r0 = AllocateDouble()
    //     0xe71e00: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71e04: mov             x1, x0
    // 0xe71e08: RestoreReg r0
    //     0xe71e08: ldr             x0, [SP], #8
    // 0xe71e0c: RestoreReg d0
    //     0xe71e0c: ldr             q0, [SP], #0x10
    // 0xe71e10: ldp             q1, q2, [SP], #0x20
    // 0xe71e14: b               #0xe717f8
    // 0xe71e18: stp             q1, q2, [SP, #-0x20]!
    // 0xe71e1c: SaveReg d0
    //     0xe71e1c: str             q0, [SP, #-0x10]!
    // 0xe71e20: SaveReg r0
    //     0xe71e20: str             x0, [SP, #-8]!
    // 0xe71e24: r0 = AllocateDouble()
    //     0xe71e24: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71e28: mov             x1, x0
    // 0xe71e2c: RestoreReg r0
    //     0xe71e2c: ldr             x0, [SP], #8
    // 0xe71e30: RestoreReg d0
    //     0xe71e30: ldr             q0, [SP], #0x10
    // 0xe71e34: ldp             q1, q2, [SP], #0x20
    // 0xe71e38: b               #0xe71830
    // 0xe71e3c: SaveReg d0
    //     0xe71e3c: str             q0, [SP, #-0x10]!
    // 0xe71e40: SaveReg r0
    //     0xe71e40: str             x0, [SP, #-8]!
    // 0xe71e44: r0 = AllocateDouble()
    //     0xe71e44: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71e48: mov             x1, x0
    // 0xe71e4c: RestoreReg r0
    //     0xe71e4c: ldr             x0, [SP], #8
    // 0xe71e50: RestoreReg d0
    //     0xe71e50: ldr             q0, [SP], #0x10
    // 0xe71e54: b               #0xe718b0
    // 0xe71e58: stp             q0, q1, [SP, #-0x20]!
    // 0xe71e5c: stp             x0, x1, [SP, #-0x10]!
    // 0xe71e60: r0 = AllocateDouble()
    //     0xe71e60: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71e64: mov             x2, x0
    // 0xe71e68: ldp             x0, x1, [SP], #0x10
    // 0xe71e6c: ldp             q0, q1, [SP], #0x20
    // 0xe71e70: b               #0xe718e8
    // 0xe71e74: stp             q1, q2, [SP, #-0x20]!
    // 0xe71e78: SaveReg d0
    //     0xe71e78: str             q0, [SP, #-0x10]!
    // 0xe71e7c: SaveReg r0
    //     0xe71e7c: str             x0, [SP, #-8]!
    // 0xe71e80: r0 = AllocateDouble()
    //     0xe71e80: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71e84: mov             x1, x0
    // 0xe71e88: RestoreReg r0
    //     0xe71e88: ldr             x0, [SP], #8
    // 0xe71e8c: RestoreReg d0
    //     0xe71e8c: ldr             q0, [SP], #0x10
    // 0xe71e90: ldp             q1, q2, [SP], #0x20
    // 0xe71e94: b               #0xe71930
    // 0xe71e98: stp             q0, q2, [SP, #-0x20]!
    // 0xe71e9c: SaveReg r2
    //     0xe71e9c: str             x2, [SP, #-8]!
    // 0xe71ea0: r0 = AllocateDouble()
    //     0xe71ea0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71ea4: RestoreReg r2
    //     0xe71ea4: ldr             x2, [SP], #8
    // 0xe71ea8: ldp             q0, q2, [SP], #0x20
    // 0xe71eac: b               #0xe719b8
    // 0xe71eb0: stp             q0, q1, [SP, #-0x20]!
    // 0xe71eb4: SaveReg r2
    //     0xe71eb4: str             x2, [SP, #-8]!
    // 0xe71eb8: r0 = AllocateDouble()
    //     0xe71eb8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71ebc: RestoreReg r2
    //     0xe71ebc: ldr             x2, [SP], #8
    // 0xe71ec0: ldp             q0, q1, [SP], #0x20
    // 0xe71ec4: b               #0xe71a14
    // 0xe71ec8: stp             q1, q3, [SP, #-0x20]!
    // 0xe71ecc: SaveReg r2
    //     0xe71ecc: str             x2, [SP, #-8]!
    // 0xe71ed0: r0 = AllocateDouble()
    //     0xe71ed0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71ed4: RestoreReg r2
    //     0xe71ed4: ldr             x2, [SP], #8
    // 0xe71ed8: ldp             q1, q3, [SP], #0x20
    // 0xe71edc: b               #0xe71a80
    // 0xe71ee0: stp             q1, q3, [SP, #-0x20]!
    // 0xe71ee4: SaveReg r2
    //     0xe71ee4: str             x2, [SP, #-8]!
    // 0xe71ee8: r0 = AllocateDouble()
    //     0xe71ee8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71eec: RestoreReg r2
    //     0xe71eec: ldr             x2, [SP], #8
    // 0xe71ef0: ldp             q1, q3, [SP], #0x20
    // 0xe71ef4: b               #0xe71b18
    // 0xe71ef8: stp             q0, q1, [SP, #-0x20]!
    // 0xe71efc: SaveReg r2
    //     0xe71efc: str             x2, [SP, #-8]!
    // 0xe71f00: r0 = AllocateDouble()
    //     0xe71f00: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71f04: RestoreReg r2
    //     0xe71f04: ldr             x2, [SP], #8
    // 0xe71f08: ldp             q0, q1, [SP], #0x20
    // 0xe71f0c: b               #0xe71ba4
    // 0xe71f10: SaveReg d0
    //     0xe71f10: str             q0, [SP, #-0x10]!
    // 0xe71f14: SaveReg r2
    //     0xe71f14: str             x2, [SP, #-8]!
    // 0xe71f18: r0 = AllocateDouble()
    //     0xe71f18: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe71f1c: RestoreReg r2
    //     0xe71f1c: ldr             x2, [SP], #8
    // 0xe71f20: RestoreReg d0
    //     0xe71f20: ldr             q0, [SP], #0x10
    // 0xe71f24: b               #0xe71c30
  }
  factory _ SvgPath.fromPolylineXml(/* No info */) {
    // ** addr: 0xe71f34, size: 0xf8
    // 0xe71f34: EnterFrame
    //     0xe71f34: stp             fp, lr, [SP, #-0x10]!
    //     0xe71f38: mov             fp, SP
    // 0xe71f3c: AllocStack(0x30)
    //     0xe71f3c: sub             SP, SP, #0x30
    // 0xe71f40: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r5, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0xe71f40: mov             x0, x2
    //     0xe71f44: stur            x3, [fp, #-0x10]
    //     0xe71f48: mov             x16, x5
    //     0xe71f4c: mov             x5, x3
    //     0xe71f50: mov             x3, x16
    //     0xe71f54: stur            x2, [fp, #-8]
    //     0xe71f58: stur            x3, [fp, #-0x18]
    // 0xe71f5c: CheckStackOverflow
    //     0xe71f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe71f60: cmp             SP, x16
    //     0xe71f64: b.ls            #0xe72024
    // 0xe71f68: mov             x1, x0
    // 0xe71f6c: r2 = "points"
    //     0xe71f6c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26148] "points"
    //     0xe71f70: ldr             x2, [x2, #0x148]
    // 0xe71f74: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe71f74: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe71f78: r0 = getAttribute()
    //     0xe71f78: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe71f7c: r1 = Null
    //     0xe71f7c: mov             x1, NULL
    // 0xe71f80: r2 = 4
    //     0xe71f80: movz            x2, #0x4
    // 0xe71f84: stur            x0, [fp, #-0x20]
    // 0xe71f88: r0 = AllocateArray()
    //     0xe71f88: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe71f8c: r16 = "M"
    //     0xe71f8c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b808] "M"
    //     0xe71f90: ldr             x16, [x16, #0x808]
    // 0xe71f94: StoreField: r0->field_f = r16
    //     0xe71f94: stur            w16, [x0, #0xf]
    // 0xe71f98: ldur            x1, [fp, #-0x20]
    // 0xe71f9c: StoreField: r0->field_13 = r1
    //     0xe71f9c: stur            w1, [x0, #0x13]
    // 0xe71fa0: str             x0, [SP]
    // 0xe71fa4: r0 = _interpolate()
    //     0xe71fa4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe71fa8: ldur            x2, [fp, #-8]
    // 0xe71fac: ldur            x3, [fp, #-0x18]
    // 0xe71fb0: ldur            x5, [fp, #-0x10]
    // 0xe71fb4: r1 = Null
    //     0xe71fb4: mov             x1, NULL
    // 0xe71fb8: stur            x0, [fp, #-0x18]
    // 0xe71fbc: r0 = SvgBrush.fromXml()
    //     0xe71fbc: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe71fc0: ldur            x2, [fp, #-8]
    // 0xe71fc4: ldur            x3, [fp, #-0x10]
    // 0xe71fc8: mov             x5, x0
    // 0xe71fcc: r1 = Null
    //     0xe71fcc: mov             x1, NULL
    // 0xe71fd0: stur            x0, [fp, #-0x20]
    // 0xe71fd4: r0 = SvgClipPath.fromXml()
    //     0xe71fd4: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe71fd8: ldur            x2, [fp, #-8]
    // 0xe71fdc: r1 = Null
    //     0xe71fdc: mov             x1, NULL
    // 0xe71fe0: stur            x0, [fp, #-8]
    // 0xe71fe4: r0 = SvgTransform.fromXml()
    //     0xe71fe4: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe71fe8: stur            x0, [fp, #-0x28]
    // 0xe71fec: r0 = SvgPath()
    //     0xe71fec: bl              #0xe71f28  ; AllocateSvgPathStub -> SvgPath (size=0x1c)
    // 0xe71ff0: ldur            x1, [fp, #-0x18]
    // 0xe71ff4: ArrayStore: r0[0] = r1  ; List_4
    //     0xe71ff4: stur            w1, [x0, #0x17]
    // 0xe71ff8: ldur            x1, [fp, #-0x20]
    // 0xe71ffc: StoreField: r0->field_7 = r1
    //     0xe71ffc: stur            w1, [x0, #7]
    // 0xe72000: ldur            x1, [fp, #-8]
    // 0xe72004: StoreField: r0->field_b = r1
    //     0xe72004: stur            w1, [x0, #0xb]
    // 0xe72008: ldur            x1, [fp, #-0x28]
    // 0xe7200c: StoreField: r0->field_f = r1
    //     0xe7200c: stur            w1, [x0, #0xf]
    // 0xe72010: ldur            x1, [fp, #-0x10]
    // 0xe72014: StoreField: r0->field_13 = r1
    //     0xe72014: stur            w1, [x0, #0x13]
    // 0xe72018: LeaveFrame
    //     0xe72018: mov             SP, fp
    //     0xe7201c: ldp             fp, lr, [SP], #0x10
    // 0xe72020: ret
    //     0xe72020: ret             
    // 0xe72024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe72024: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe72028: b               #0xe71f68
  }
  factory _ SvgPath.fromPolygonXml(/* No info */) {
    // ** addr: 0xe7202c, size: 0x104
    // 0xe7202c: EnterFrame
    //     0xe7202c: stp             fp, lr, [SP, #-0x10]!
    //     0xe72030: mov             fp, SP
    // 0xe72034: AllocStack(0x30)
    //     0xe72034: sub             SP, SP, #0x30
    // 0xe72038: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r5, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0xe72038: mov             x0, x2
    //     0xe7203c: stur            x3, [fp, #-0x10]
    //     0xe72040: mov             x16, x5
    //     0xe72044: mov             x5, x3
    //     0xe72048: mov             x3, x16
    //     0xe7204c: stur            x2, [fp, #-8]
    //     0xe72050: stur            x3, [fp, #-0x18]
    // 0xe72054: CheckStackOverflow
    //     0xe72054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe72058: cmp             SP, x16
    //     0xe7205c: b.ls            #0xe72128
    // 0xe72060: mov             x1, x0
    // 0xe72064: r2 = "points"
    //     0xe72064: add             x2, PP, #0x26, lsl #12  ; [pp+0x26148] "points"
    //     0xe72068: ldr             x2, [x2, #0x148]
    // 0xe7206c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe7206c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe72070: r0 = getAttribute()
    //     0xe72070: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe72074: r1 = Null
    //     0xe72074: mov             x1, NULL
    // 0xe72078: r2 = 6
    //     0xe72078: movz            x2, #0x6
    // 0xe7207c: stur            x0, [fp, #-0x20]
    // 0xe72080: r0 = AllocateArray()
    //     0xe72080: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe72084: r16 = "M"
    //     0xe72084: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b808] "M"
    //     0xe72088: ldr             x16, [x16, #0x808]
    // 0xe7208c: StoreField: r0->field_f = r16
    //     0xe7208c: stur            w16, [x0, #0xf]
    // 0xe72090: ldur            x1, [fp, #-0x20]
    // 0xe72094: StoreField: r0->field_13 = r1
    //     0xe72094: stur            w1, [x0, #0x13]
    // 0xe72098: r16 = "z"
    //     0xe72098: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b828] "z"
    //     0xe7209c: ldr             x16, [x16, #0x828]
    // 0xe720a0: ArrayStore: r0[0] = r16  ; List_4
    //     0xe720a0: stur            w16, [x0, #0x17]
    // 0xe720a4: str             x0, [SP]
    // 0xe720a8: r0 = _interpolate()
    //     0xe720a8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe720ac: ldur            x2, [fp, #-8]
    // 0xe720b0: ldur            x3, [fp, #-0x18]
    // 0xe720b4: ldur            x5, [fp, #-0x10]
    // 0xe720b8: r1 = Null
    //     0xe720b8: mov             x1, NULL
    // 0xe720bc: stur            x0, [fp, #-0x18]
    // 0xe720c0: r0 = SvgBrush.fromXml()
    //     0xe720c0: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe720c4: ldur            x2, [fp, #-8]
    // 0xe720c8: ldur            x3, [fp, #-0x10]
    // 0xe720cc: mov             x5, x0
    // 0xe720d0: r1 = Null
    //     0xe720d0: mov             x1, NULL
    // 0xe720d4: stur            x0, [fp, #-0x20]
    // 0xe720d8: r0 = SvgClipPath.fromXml()
    //     0xe720d8: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe720dc: ldur            x2, [fp, #-8]
    // 0xe720e0: r1 = Null
    //     0xe720e0: mov             x1, NULL
    // 0xe720e4: stur            x0, [fp, #-8]
    // 0xe720e8: r0 = SvgTransform.fromXml()
    //     0xe720e8: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe720ec: stur            x0, [fp, #-0x28]
    // 0xe720f0: r0 = SvgPath()
    //     0xe720f0: bl              #0xe71f28  ; AllocateSvgPathStub -> SvgPath (size=0x1c)
    // 0xe720f4: ldur            x1, [fp, #-0x18]
    // 0xe720f8: ArrayStore: r0[0] = r1  ; List_4
    //     0xe720f8: stur            w1, [x0, #0x17]
    // 0xe720fc: ldur            x1, [fp, #-0x20]
    // 0xe72100: StoreField: r0->field_7 = r1
    //     0xe72100: stur            w1, [x0, #7]
    // 0xe72104: ldur            x1, [fp, #-8]
    // 0xe72108: StoreField: r0->field_b = r1
    //     0xe72108: stur            w1, [x0, #0xb]
    // 0xe7210c: ldur            x1, [fp, #-0x28]
    // 0xe72110: StoreField: r0->field_f = r1
    //     0xe72110: stur            w1, [x0, #0xf]
    // 0xe72114: ldur            x1, [fp, #-0x10]
    // 0xe72118: StoreField: r0->field_13 = r1
    //     0xe72118: stur            w1, [x0, #0x13]
    // 0xe7211c: LeaveFrame
    //     0xe7211c: mov             SP, fp
    //     0xe72120: ldp             fp, lr, [SP], #0x10
    // 0xe72124: ret
    //     0xe72124: ret             
    // 0xe72128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe72128: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7212c: b               #0xe72060
  }
  factory _ SvgPath.fromXml(/* No info */) {
    // ** addr: 0xe72130, size: 0x100
    // 0xe72130: EnterFrame
    //     0xe72130: stp             fp, lr, [SP, #-0x10]!
    //     0xe72134: mov             fp, SP
    // 0xe72138: AllocStack(0x28)
    //     0xe72138: sub             SP, SP, #0x28
    // 0xe7213c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r5, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0xe7213c: mov             x0, x2
    //     0xe72140: stur            x3, [fp, #-0x10]
    //     0xe72144: mov             x16, x5
    //     0xe72148: mov             x5, x3
    //     0xe7214c: mov             x3, x16
    //     0xe72150: stur            x2, [fp, #-8]
    //     0xe72154: stur            x3, [fp, #-0x18]
    // 0xe72158: CheckStackOverflow
    //     0xe72158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7215c: cmp             SP, x16
    //     0xe72160: b.ls            #0xe72228
    // 0xe72164: mov             x1, x0
    // 0xe72168: r2 = "d"
    //     0xe72168: add             x2, PP, #8, lsl #12  ; [pp+0x8e90] "d"
    //     0xe7216c: ldr             x2, [x2, #0xe90]
    // 0xe72170: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe72170: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe72174: r0 = getAttribute()
    //     0xe72174: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe72178: stur            x0, [fp, #-0x20]
    // 0xe7217c: cmp             w0, NULL
    // 0xe72180: b.eq            #0xe72208
    // 0xe72184: ldur            x4, [fp, #-0x10]
    // 0xe72188: ldur            x2, [fp, #-8]
    // 0xe7218c: ldur            x3, [fp, #-0x18]
    // 0xe72190: mov             x5, x4
    // 0xe72194: r1 = Null
    //     0xe72194: mov             x1, NULL
    // 0xe72198: r0 = SvgBrush.fromXml()
    //     0xe72198: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe7219c: ldur            x2, [fp, #-8]
    // 0xe721a0: ldur            x3, [fp, #-0x10]
    // 0xe721a4: mov             x5, x0
    // 0xe721a8: r1 = Null
    //     0xe721a8: mov             x1, NULL
    // 0xe721ac: stur            x0, [fp, #-0x18]
    // 0xe721b0: r0 = SvgClipPath.fromXml()
    //     0xe721b0: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe721b4: ldur            x2, [fp, #-8]
    // 0xe721b8: r1 = Null
    //     0xe721b8: mov             x1, NULL
    // 0xe721bc: stur            x0, [fp, #-8]
    // 0xe721c0: r0 = SvgTransform.fromXml()
    //     0xe721c0: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe721c4: stur            x0, [fp, #-0x28]
    // 0xe721c8: r0 = SvgPath()
    //     0xe721c8: bl              #0xe71f28  ; AllocateSvgPathStub -> SvgPath (size=0x1c)
    // 0xe721cc: mov             x1, x0
    // 0xe721d0: ldur            x0, [fp, #-0x20]
    // 0xe721d4: ArrayStore: r1[0] = r0  ; List_4
    //     0xe721d4: stur            w0, [x1, #0x17]
    // 0xe721d8: ldur            x0, [fp, #-0x18]
    // 0xe721dc: StoreField: r1->field_7 = r0
    //     0xe721dc: stur            w0, [x1, #7]
    // 0xe721e0: ldur            x0, [fp, #-8]
    // 0xe721e4: StoreField: r1->field_b = r0
    //     0xe721e4: stur            w0, [x1, #0xb]
    // 0xe721e8: ldur            x0, [fp, #-0x28]
    // 0xe721ec: StoreField: r1->field_f = r0
    //     0xe721ec: stur            w0, [x1, #0xf]
    // 0xe721f0: ldur            x0, [fp, #-0x10]
    // 0xe721f4: StoreField: r1->field_13 = r0
    //     0xe721f4: stur            w0, [x1, #0x13]
    // 0xe721f8: mov             x0, x1
    // 0xe721fc: LeaveFrame
    //     0xe721fc: mov             SP, fp
    //     0xe72200: ldp             fp, lr, [SP], #0x10
    // 0xe72204: ret
    //     0xe72204: ret             
    // 0xe72208: r0 = _Exception()
    //     0xe72208: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xe7220c: mov             x1, x0
    // 0xe72210: r0 = "Path element must contain \"d\" attribute"
    //     0xe72210: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eb50] "Path element must contain \"d\" attribute"
    //     0xe72214: ldr             x0, [x0, #0xb50]
    // 0xe72218: StoreField: r1->field_7 = r0
    //     0xe72218: stur            w0, [x1, #7]
    // 0xe7221c: mov             x0, x1
    // 0xe72220: r0 = Throw()
    //     0xe72220: bl              #0xec04b8  ; ThrowStub
    // 0xe72224: brk             #0
    // 0xe72228: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe72228: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7222c: b               #0xe72164
  }
  factory _ SvgPath.fromLineXml(/* No info */) {
    // ** addr: 0xe72230, size: 0x2d4
    // 0xe72230: EnterFrame
    //     0xe72230: stp             fp, lr, [SP, #-0x10]!
    //     0xe72234: mov             fp, SP
    // 0xe72238: AllocStack(0x50)
    //     0xe72238: sub             SP, SP, #0x50
    // 0xe7223c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3 */)
    //     0xe7223c: mov             x4, x2
    //     0xe72240: mov             x0, x3
    //     0xe72244: stur            x3, [fp, #-0x10]
    //     0xe72248: mov             x3, x5
    //     0xe7224c: stur            x2, [fp, #-8]
    // 0xe72250: CheckStackOverflow
    //     0xe72250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe72254: cmp             SP, x16
    //     0xe72258: b.ls            #0xe7247c
    // 0xe7225c: mov             x2, x4
    // 0xe72260: mov             x5, x0
    // 0xe72264: r1 = Null
    //     0xe72264: mov             x1, NULL
    // 0xe72268: r0 = SvgBrush.fromXml()
    //     0xe72268: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe7226c: ldur            x1, [fp, #-8]
    // 0xe72270: mov             x3, x0
    // 0xe72274: r2 = "x1"
    //     0xe72274: add             x2, PP, #0x26, lsl #12  ; [pp+0x260d0] "x1"
    //     0xe72278: ldr             x2, [x2, #0xd0]
    // 0xe7227c: stur            x0, [fp, #-0x18]
    // 0xe72280: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe72280: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe72284: r0 = getNumeric()
    //     0xe72284: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe72288: cmp             w0, NULL
    // 0xe7228c: b.eq            #0xe72484
    // 0xe72290: mov             x1, x0
    // 0xe72294: r0 = sizeValue()
    //     0xe72294: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe72298: ldur            x1, [fp, #-8]
    // 0xe7229c: ldur            x3, [fp, #-0x18]
    // 0xe722a0: r2 = "y1"
    //     0xe722a0: add             x2, PP, #0x26, lsl #12  ; [pp+0x260e0] "y1"
    //     0xe722a4: ldr             x2, [x2, #0xe0]
    // 0xe722a8: stur            d0, [fp, #-0x30]
    // 0xe722ac: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe722ac: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe722b0: r0 = getNumeric()
    //     0xe722b0: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe722b4: cmp             w0, NULL
    // 0xe722b8: b.eq            #0xe72488
    // 0xe722bc: mov             x1, x0
    // 0xe722c0: r0 = sizeValue()
    //     0xe722c0: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe722c4: ldur            x1, [fp, #-8]
    // 0xe722c8: ldur            x3, [fp, #-0x18]
    // 0xe722cc: r2 = "x2"
    //     0xe722cc: add             x2, PP, #0x26, lsl #12  ; [pp+0x260d8] "x2"
    //     0xe722d0: ldr             x2, [x2, #0xd8]
    // 0xe722d4: stur            d0, [fp, #-0x38]
    // 0xe722d8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe722d8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe722dc: r0 = getNumeric()
    //     0xe722dc: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe722e0: cmp             w0, NULL
    // 0xe722e4: b.eq            #0xe7248c
    // 0xe722e8: mov             x1, x0
    // 0xe722ec: r0 = sizeValue()
    //     0xe722ec: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe722f0: ldur            x1, [fp, #-8]
    // 0xe722f4: ldur            x3, [fp, #-0x18]
    // 0xe722f8: r2 = "y2"
    //     0xe722f8: add             x2, PP, #0x26, lsl #12  ; [pp+0x260e8] "y2"
    //     0xe722fc: ldr             x2, [x2, #0xe8]
    // 0xe72300: stur            d0, [fp, #-0x40]
    // 0xe72304: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe72304: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe72308: r0 = getNumeric()
    //     0xe72308: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe7230c: cmp             w0, NULL
    // 0xe72310: b.eq            #0xe72490
    // 0xe72314: mov             x1, x0
    // 0xe72318: r0 = sizeValue()
    //     0xe72318: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe7231c: r1 = Null
    //     0xe7231c: mov             x1, NULL
    // 0xe72320: r2 = 16
    //     0xe72320: movz            x2, #0x10
    // 0xe72324: stur            d0, [fp, #-0x48]
    // 0xe72328: r0 = AllocateArray()
    //     0xe72328: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7232c: r16 = "M"
    //     0xe7232c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b808] "M"
    //     0xe72330: ldr             x16, [x16, #0x808]
    // 0xe72334: StoreField: r0->field_f = r16
    //     0xe72334: stur            w16, [x0, #0xf]
    // 0xe72338: ldur            d0, [fp, #-0x30]
    // 0xe7233c: r1 = inline_Allocate_Double()
    //     0xe7233c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe72340: add             x1, x1, #0x10
    //     0xe72344: cmp             x2, x1
    //     0xe72348: b.ls            #0xe72494
    //     0xe7234c: str             x1, [THR, #0x50]  ; THR::top
    //     0xe72350: sub             x1, x1, #0xf
    //     0xe72354: movz            x2, #0xe15c
    //     0xe72358: movk            x2, #0x3, lsl #16
    //     0xe7235c: stur            x2, [x1, #-1]
    // 0xe72360: StoreField: r1->field_7 = d0
    //     0xe72360: stur            d0, [x1, #7]
    // 0xe72364: StoreField: r0->field_13 = r1
    //     0xe72364: stur            w1, [x0, #0x13]
    // 0xe72368: r16 = " "
    //     0xe72368: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe7236c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe7236c: stur            w16, [x0, #0x17]
    // 0xe72370: ldur            d0, [fp, #-0x38]
    // 0xe72374: r1 = inline_Allocate_Double()
    //     0xe72374: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe72378: add             x1, x1, #0x10
    //     0xe7237c: cmp             x2, x1
    //     0xe72380: b.ls            #0xe724b0
    //     0xe72384: str             x1, [THR, #0x50]  ; THR::top
    //     0xe72388: sub             x1, x1, #0xf
    //     0xe7238c: movz            x2, #0xe15c
    //     0xe72390: movk            x2, #0x3, lsl #16
    //     0xe72394: stur            x2, [x1, #-1]
    // 0xe72398: StoreField: r1->field_7 = d0
    //     0xe72398: stur            d0, [x1, #7]
    // 0xe7239c: StoreField: r0->field_1b = r1
    //     0xe7239c: stur            w1, [x0, #0x1b]
    // 0xe723a0: r16 = " "
    //     0xe723a0: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe723a4: StoreField: r0->field_1f = r16
    //     0xe723a4: stur            w16, [x0, #0x1f]
    // 0xe723a8: ldur            d0, [fp, #-0x40]
    // 0xe723ac: r1 = inline_Allocate_Double()
    //     0xe723ac: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe723b0: add             x1, x1, #0x10
    //     0xe723b4: cmp             x2, x1
    //     0xe723b8: b.ls            #0xe724cc
    //     0xe723bc: str             x1, [THR, #0x50]  ; THR::top
    //     0xe723c0: sub             x1, x1, #0xf
    //     0xe723c4: movz            x2, #0xe15c
    //     0xe723c8: movk            x2, #0x3, lsl #16
    //     0xe723cc: stur            x2, [x1, #-1]
    // 0xe723d0: StoreField: r1->field_7 = d0
    //     0xe723d0: stur            d0, [x1, #7]
    // 0xe723d4: StoreField: r0->field_23 = r1
    //     0xe723d4: stur            w1, [x0, #0x23]
    // 0xe723d8: r16 = " "
    //     0xe723d8: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe723dc: StoreField: r0->field_27 = r16
    //     0xe723dc: stur            w16, [x0, #0x27]
    // 0xe723e0: ldur            d0, [fp, #-0x48]
    // 0xe723e4: r1 = inline_Allocate_Double()
    //     0xe723e4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe723e8: add             x1, x1, #0x10
    //     0xe723ec: cmp             x2, x1
    //     0xe723f0: b.ls            #0xe724e8
    //     0xe723f4: str             x1, [THR, #0x50]  ; THR::top
    //     0xe723f8: sub             x1, x1, #0xf
    //     0xe723fc: movz            x2, #0xe15c
    //     0xe72400: movk            x2, #0x3, lsl #16
    //     0xe72404: stur            x2, [x1, #-1]
    // 0xe72408: StoreField: r1->field_7 = d0
    //     0xe72408: stur            d0, [x1, #7]
    // 0xe7240c: StoreField: r0->field_2b = r1
    //     0xe7240c: stur            w1, [x0, #0x2b]
    // 0xe72410: str             x0, [SP]
    // 0xe72414: r0 = _interpolate()
    //     0xe72414: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe72418: ldur            x2, [fp, #-8]
    // 0xe7241c: ldur            x3, [fp, #-0x10]
    // 0xe72420: ldur            x5, [fp, #-0x18]
    // 0xe72424: r1 = Null
    //     0xe72424: mov             x1, NULL
    // 0xe72428: stur            x0, [fp, #-0x20]
    // 0xe7242c: r0 = SvgClipPath.fromXml()
    //     0xe7242c: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe72430: ldur            x2, [fp, #-8]
    // 0xe72434: r1 = Null
    //     0xe72434: mov             x1, NULL
    // 0xe72438: stur            x0, [fp, #-8]
    // 0xe7243c: r0 = SvgTransform.fromXml()
    //     0xe7243c: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe72440: stur            x0, [fp, #-0x28]
    // 0xe72444: r0 = SvgPath()
    //     0xe72444: bl              #0xe71f28  ; AllocateSvgPathStub -> SvgPath (size=0x1c)
    // 0xe72448: ldur            x1, [fp, #-0x20]
    // 0xe7244c: ArrayStore: r0[0] = r1  ; List_4
    //     0xe7244c: stur            w1, [x0, #0x17]
    // 0xe72450: ldur            x1, [fp, #-0x18]
    // 0xe72454: StoreField: r0->field_7 = r1
    //     0xe72454: stur            w1, [x0, #7]
    // 0xe72458: ldur            x1, [fp, #-8]
    // 0xe7245c: StoreField: r0->field_b = r1
    //     0xe7245c: stur            w1, [x0, #0xb]
    // 0xe72460: ldur            x1, [fp, #-0x28]
    // 0xe72464: StoreField: r0->field_f = r1
    //     0xe72464: stur            w1, [x0, #0xf]
    // 0xe72468: ldur            x1, [fp, #-0x10]
    // 0xe7246c: StoreField: r0->field_13 = r1
    //     0xe7246c: stur            w1, [x0, #0x13]
    // 0xe72470: LeaveFrame
    //     0xe72470: mov             SP, fp
    //     0xe72474: ldp             fp, lr, [SP], #0x10
    // 0xe72478: ret
    //     0xe72478: ret             
    // 0xe7247c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7247c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe72480: b               #0xe7225c
    // 0xe72484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe72484: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe72488: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe72488: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe7248c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe7248c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe72490: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe72490: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe72494: SaveReg d0
    //     0xe72494: str             q0, [SP, #-0x10]!
    // 0xe72498: SaveReg r0
    //     0xe72498: str             x0, [SP, #-8]!
    // 0xe7249c: r0 = AllocateDouble()
    //     0xe7249c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe724a0: mov             x1, x0
    // 0xe724a4: RestoreReg r0
    //     0xe724a4: ldr             x0, [SP], #8
    // 0xe724a8: RestoreReg d0
    //     0xe724a8: ldr             q0, [SP], #0x10
    // 0xe724ac: b               #0xe72360
    // 0xe724b0: SaveReg d0
    //     0xe724b0: str             q0, [SP, #-0x10]!
    // 0xe724b4: SaveReg r0
    //     0xe724b4: str             x0, [SP, #-8]!
    // 0xe724b8: r0 = AllocateDouble()
    //     0xe724b8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe724bc: mov             x1, x0
    // 0xe724c0: RestoreReg r0
    //     0xe724c0: ldr             x0, [SP], #8
    // 0xe724c4: RestoreReg d0
    //     0xe724c4: ldr             q0, [SP], #0x10
    // 0xe724c8: b               #0xe72398
    // 0xe724cc: SaveReg d0
    //     0xe724cc: str             q0, [SP, #-0x10]!
    // 0xe724d0: SaveReg r0
    //     0xe724d0: str             x0, [SP, #-8]!
    // 0xe724d4: r0 = AllocateDouble()
    //     0xe724d4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe724d8: mov             x1, x0
    // 0xe724dc: RestoreReg r0
    //     0xe724dc: ldr             x0, [SP], #8
    // 0xe724e0: RestoreReg d0
    //     0xe724e0: ldr             q0, [SP], #0x10
    // 0xe724e4: b               #0xe723d0
    // 0xe724e8: SaveReg d0
    //     0xe724e8: str             q0, [SP, #-0x10]!
    // 0xe724ec: SaveReg r0
    //     0xe724ec: str             x0, [SP, #-8]!
    // 0xe724f0: r0 = AllocateDouble()
    //     0xe724f0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe724f4: mov             x1, x0
    // 0xe724f8: RestoreReg r0
    //     0xe724f8: ldr             x0, [SP], #8
    // 0xe724fc: RestoreReg d0
    //     0xe724fc: ldr             q0, [SP], #0x10
    // 0xe72500: b               #0xe72408
  }
  factory _ SvgPath.fromEllipseXml(/* No info */) {
    // ** addr: 0xe72904, size: 0x55c
    // 0xe72904: EnterFrame
    //     0xe72904: stp             fp, lr, [SP, #-0x10]!
    //     0xe72908: mov             fp, SP
    // 0xe7290c: AllocStack(0x50)
    //     0xe7290c: sub             SP, SP, #0x50
    // 0xe72910: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3 */)
    //     0xe72910: mov             x4, x2
    //     0xe72914: mov             x0, x3
    //     0xe72918: stur            x3, [fp, #-0x10]
    //     0xe7291c: mov             x3, x5
    //     0xe72920: stur            x2, [fp, #-8]
    // 0xe72924: CheckStackOverflow
    //     0xe72924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe72928: cmp             SP, x16
    //     0xe7292c: b.ls            #0xe72d88
    // 0xe72930: mov             x2, x4
    // 0xe72934: mov             x5, x0
    // 0xe72938: r1 = Null
    //     0xe72938: mov             x1, NULL
    // 0xe7293c: r0 = SvgBrush.fromXml()
    //     0xe7293c: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe72940: ldur            x1, [fp, #-8]
    // 0xe72944: mov             x3, x0
    // 0xe72948: r2 = "cx"
    //     0xe72948: add             x2, PP, #0x26, lsl #12  ; [pp+0x26120] "cx"
    //     0xe7294c: ldr             x2, [x2, #0x120]
    // 0xe72950: stur            x0, [fp, #-0x18]
    // 0xe72954: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe72954: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe72958: r0 = getNumeric()
    //     0xe72958: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe7295c: cmp             w0, NULL
    // 0xe72960: b.eq            #0xe72d90
    // 0xe72964: mov             x1, x0
    // 0xe72968: r0 = sizeValue()
    //     0xe72968: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe7296c: ldur            x1, [fp, #-8]
    // 0xe72970: ldur            x3, [fp, #-0x18]
    // 0xe72974: r2 = "cy"
    //     0xe72974: add             x2, PP, #9, lsl #12  ; [pp+0x9560] "cy"
    //     0xe72978: ldr             x2, [x2, #0x560]
    // 0xe7297c: stur            d0, [fp, #-0x30]
    // 0xe72980: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe72980: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe72984: r0 = getNumeric()
    //     0xe72984: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe72988: cmp             w0, NULL
    // 0xe7298c: b.eq            #0xe72d94
    // 0xe72990: mov             x1, x0
    // 0xe72994: r0 = sizeValue()
    //     0xe72994: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe72998: ldur            x1, [fp, #-8]
    // 0xe7299c: ldur            x3, [fp, #-0x18]
    // 0xe729a0: r2 = "rx"
    //     0xe729a0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26128] "rx"
    //     0xe729a4: ldr             x2, [x2, #0x128]
    // 0xe729a8: stur            d0, [fp, #-0x38]
    // 0xe729ac: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe729ac: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe729b0: r0 = getNumeric()
    //     0xe729b0: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe729b4: cmp             w0, NULL
    // 0xe729b8: b.eq            #0xe72d98
    // 0xe729bc: mov             x1, x0
    // 0xe729c0: r0 = sizeValue()
    //     0xe729c0: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe729c4: ldur            x1, [fp, #-8]
    // 0xe729c8: ldur            x3, [fp, #-0x18]
    // 0xe729cc: r2 = "ry"
    //     0xe729cc: add             x2, PP, #0x26, lsl #12  ; [pp+0x26130] "ry"
    //     0xe729d0: ldr             x2, [x2, #0x130]
    // 0xe729d4: stur            d0, [fp, #-0x40]
    // 0xe729d8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe729d8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe729dc: r0 = getNumeric()
    //     0xe729dc: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe729e0: cmp             w0, NULL
    // 0xe729e4: b.eq            #0xe72d9c
    // 0xe729e8: mov             x1, x0
    // 0xe729ec: r0 = sizeValue()
    //     0xe729ec: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe729f0: r1 = Null
    //     0xe729f0: mov             x1, NULL
    // 0xe729f4: r2 = 42
    //     0xe729f4: movz            x2, #0x2a
    // 0xe729f8: stur            d0, [fp, #-0x48]
    // 0xe729fc: r0 = AllocateArray()
    //     0xe729fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe72a00: mov             x2, x0
    // 0xe72a04: r16 = "M"
    //     0xe72a04: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b808] "M"
    //     0xe72a08: ldr             x16, [x16, #0x808]
    // 0xe72a0c: StoreField: r2->field_f = r16
    //     0xe72a0c: stur            w16, [x2, #0xf]
    // 0xe72a10: ldur            d1, [fp, #-0x30]
    // 0xe72a14: ldur            d0, [fp, #-0x40]
    // 0xe72a18: fsub            d2, d1, d0
    // 0xe72a1c: r3 = inline_Allocate_Double()
    //     0xe72a1c: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xe72a20: add             x3, x3, #0x10
    //     0xe72a24: cmp             x0, x3
    //     0xe72a28: b.ls            #0xe72da0
    //     0xe72a2c: str             x3, [THR, #0x50]  ; THR::top
    //     0xe72a30: sub             x3, x3, #0xf
    //     0xe72a34: movz            x0, #0xe15c
    //     0xe72a38: movk            x0, #0x3, lsl #16
    //     0xe72a3c: stur            x0, [x3, #-1]
    // 0xe72a40: StoreField: r3->field_7 = d2
    //     0xe72a40: stur            d2, [x3, #7]
    // 0xe72a44: mov             x1, x2
    // 0xe72a48: mov             x0, x3
    // 0xe72a4c: ArrayStore: r1[1] = r0  ; List_4
    //     0xe72a4c: add             x25, x1, #0x13
    //     0xe72a50: str             w0, [x25]
    //     0xe72a54: tbz             w0, #0, #0xe72a70
    //     0xe72a58: ldurb           w16, [x1, #-1]
    //     0xe72a5c: ldurb           w17, [x0, #-1]
    //     0xe72a60: and             x16, x17, x16, lsr #2
    //     0xe72a64: tst             x16, HEAP, lsr #32
    //     0xe72a68: b.eq            #0xe72a70
    //     0xe72a6c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72a70: r16 = ","
    //     0xe72a70: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe72a74: ldr             x16, [x16, #0x5f8]
    // 0xe72a78: ArrayStore: r2[0] = r16  ; List_4
    //     0xe72a78: stur            w16, [x2, #0x17]
    // 0xe72a7c: ldur            d2, [fp, #-0x38]
    // 0xe72a80: r4 = inline_Allocate_Double()
    //     0xe72a80: ldp             x4, x0, [THR, #0x50]  ; THR::top
    //     0xe72a84: add             x4, x4, #0x10
    //     0xe72a88: cmp             x0, x4
    //     0xe72a8c: b.ls            #0xe72dc4
    //     0xe72a90: str             x4, [THR, #0x50]  ; THR::top
    //     0xe72a94: sub             x4, x4, #0xf
    //     0xe72a98: movz            x0, #0xe15c
    //     0xe72a9c: movk            x0, #0x3, lsl #16
    //     0xe72aa0: stur            x0, [x4, #-1]
    // 0xe72aa4: StoreField: r4->field_7 = d2
    //     0xe72aa4: stur            d2, [x4, #7]
    // 0xe72aa8: mov             x1, x2
    // 0xe72aac: mov             x0, x4
    // 0xe72ab0: ArrayStore: r1[3] = r0  ; List_4
    //     0xe72ab0: add             x25, x1, #0x1b
    //     0xe72ab4: str             w0, [x25]
    //     0xe72ab8: tbz             w0, #0, #0xe72ad4
    //     0xe72abc: ldurb           w16, [x1, #-1]
    //     0xe72ac0: ldurb           w17, [x0, #-1]
    //     0xe72ac4: and             x16, x17, x16, lsr #2
    //     0xe72ac8: tst             x16, HEAP, lsr #32
    //     0xe72acc: b.eq            #0xe72ad4
    //     0xe72ad0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72ad4: r16 = "A"
    //     0xe72ad4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35420] "A"
    //     0xe72ad8: ldr             x16, [x16, #0x420]
    // 0xe72adc: StoreField: r2->field_1f = r16
    //     0xe72adc: stur            w16, [x2, #0x1f]
    // 0xe72ae0: r5 = inline_Allocate_Double()
    //     0xe72ae0: ldp             x5, x0, [THR, #0x50]  ; THR::top
    //     0xe72ae4: add             x5, x5, #0x10
    //     0xe72ae8: cmp             x0, x5
    //     0xe72aec: b.ls            #0xe72de8
    //     0xe72af0: str             x5, [THR, #0x50]  ; THR::top
    //     0xe72af4: sub             x5, x5, #0xf
    //     0xe72af8: movz            x0, #0xe15c
    //     0xe72afc: movk            x0, #0x3, lsl #16
    //     0xe72b00: stur            x0, [x5, #-1]
    // 0xe72b04: StoreField: r5->field_7 = d0
    //     0xe72b04: stur            d0, [x5, #7]
    // 0xe72b08: mov             x1, x2
    // 0xe72b0c: mov             x0, x5
    // 0xe72b10: ArrayStore: r1[5] = r0  ; List_4
    //     0xe72b10: add             x25, x1, #0x23
    //     0xe72b14: str             w0, [x25]
    //     0xe72b18: tbz             w0, #0, #0xe72b34
    //     0xe72b1c: ldurb           w16, [x1, #-1]
    //     0xe72b20: ldurb           w17, [x0, #-1]
    //     0xe72b24: and             x16, x17, x16, lsr #2
    //     0xe72b28: tst             x16, HEAP, lsr #32
    //     0xe72b2c: b.eq            #0xe72b34
    //     0xe72b30: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72b34: r16 = ","
    //     0xe72b34: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe72b38: ldr             x16, [x16, #0x5f8]
    // 0xe72b3c: StoreField: r2->field_27 = r16
    //     0xe72b3c: stur            w16, [x2, #0x27]
    // 0xe72b40: ldur            d2, [fp, #-0x48]
    // 0xe72b44: r6 = inline_Allocate_Double()
    //     0xe72b44: ldp             x6, x0, [THR, #0x50]  ; THR::top
    //     0xe72b48: add             x6, x6, #0x10
    //     0xe72b4c: cmp             x0, x6
    //     0xe72b50: b.ls            #0xe72e0c
    //     0xe72b54: str             x6, [THR, #0x50]  ; THR::top
    //     0xe72b58: sub             x6, x6, #0xf
    //     0xe72b5c: movz            x0, #0xe15c
    //     0xe72b60: movk            x0, #0x3, lsl #16
    //     0xe72b64: stur            x0, [x6, #-1]
    // 0xe72b68: StoreField: r6->field_7 = d2
    //     0xe72b68: stur            d2, [x6, #7]
    // 0xe72b6c: mov             x1, x2
    // 0xe72b70: mov             x0, x6
    // 0xe72b74: ArrayStore: r1[7] = r0  ; List_4
    //     0xe72b74: add             x25, x1, #0x2b
    //     0xe72b78: str             w0, [x25]
    //     0xe72b7c: tbz             w0, #0, #0xe72b98
    //     0xe72b80: ldurb           w16, [x1, #-1]
    //     0xe72b84: ldurb           w17, [x0, #-1]
    //     0xe72b88: and             x16, x17, x16, lsr #2
    //     0xe72b8c: tst             x16, HEAP, lsr #32
    //     0xe72b90: b.eq            #0xe72b98
    //     0xe72b94: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72b98: r16 = " 0,0,0 "
    //     0xe72b98: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec50] " 0,0,0 "
    //     0xe72b9c: ldr             x16, [x16, #0xc50]
    // 0xe72ba0: StoreField: r2->field_2f = r16
    //     0xe72ba0: stur            w16, [x2, #0x2f]
    // 0xe72ba4: fadd            d2, d1, d0
    // 0xe72ba8: r0 = inline_Allocate_Double()
    //     0xe72ba8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe72bac: add             x0, x0, #0x10
    //     0xe72bb0: cmp             x1, x0
    //     0xe72bb4: b.ls            #0xe72e38
    //     0xe72bb8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe72bbc: sub             x0, x0, #0xf
    //     0xe72bc0: movz            x1, #0xe15c
    //     0xe72bc4: movk            x1, #0x3, lsl #16
    //     0xe72bc8: stur            x1, [x0, #-1]
    // 0xe72bcc: StoreField: r0->field_7 = d2
    //     0xe72bcc: stur            d2, [x0, #7]
    // 0xe72bd0: mov             x1, x2
    // 0xe72bd4: ArrayStore: r1[9] = r0  ; List_4
    //     0xe72bd4: add             x25, x1, #0x33
    //     0xe72bd8: str             w0, [x25]
    //     0xe72bdc: tbz             w0, #0, #0xe72bf8
    //     0xe72be0: ldurb           w16, [x1, #-1]
    //     0xe72be4: ldurb           w17, [x0, #-1]
    //     0xe72be8: and             x16, x17, x16, lsr #2
    //     0xe72bec: tst             x16, HEAP, lsr #32
    //     0xe72bf0: b.eq            #0xe72bf8
    //     0xe72bf4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72bf8: r16 = ","
    //     0xe72bf8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe72bfc: ldr             x16, [x16, #0x5f8]
    // 0xe72c00: StoreField: r2->field_37 = r16
    //     0xe72c00: stur            w16, [x2, #0x37]
    // 0xe72c04: mov             x1, x2
    // 0xe72c08: mov             x0, x4
    // 0xe72c0c: ArrayStore: r1[11] = r0  ; List_4
    //     0xe72c0c: add             x25, x1, #0x3b
    //     0xe72c10: str             w0, [x25]
    //     0xe72c14: tbz             w0, #0, #0xe72c30
    //     0xe72c18: ldurb           w16, [x1, #-1]
    //     0xe72c1c: ldurb           w17, [x0, #-1]
    //     0xe72c20: and             x16, x17, x16, lsr #2
    //     0xe72c24: tst             x16, HEAP, lsr #32
    //     0xe72c28: b.eq            #0xe72c30
    //     0xe72c2c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72c30: r16 = "A"
    //     0xe72c30: add             x16, PP, #0x35, lsl #12  ; [pp+0x35420] "A"
    //     0xe72c34: ldr             x16, [x16, #0x420]
    // 0xe72c38: StoreField: r2->field_3f = r16
    //     0xe72c38: stur            w16, [x2, #0x3f]
    // 0xe72c3c: mov             x1, x2
    // 0xe72c40: mov             x0, x5
    // 0xe72c44: ArrayStore: r1[13] = r0  ; List_4
    //     0xe72c44: add             x25, x1, #0x43
    //     0xe72c48: str             w0, [x25]
    //     0xe72c4c: tbz             w0, #0, #0xe72c68
    //     0xe72c50: ldurb           w16, [x1, #-1]
    //     0xe72c54: ldurb           w17, [x0, #-1]
    //     0xe72c58: and             x16, x17, x16, lsr #2
    //     0xe72c5c: tst             x16, HEAP, lsr #32
    //     0xe72c60: b.eq            #0xe72c68
    //     0xe72c64: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72c68: r16 = ","
    //     0xe72c68: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe72c6c: ldr             x16, [x16, #0x5f8]
    // 0xe72c70: StoreField: r2->field_47 = r16
    //     0xe72c70: stur            w16, [x2, #0x47]
    // 0xe72c74: mov             x1, x2
    // 0xe72c78: mov             x0, x6
    // 0xe72c7c: ArrayStore: r1[15] = r0  ; List_4
    //     0xe72c7c: add             x25, x1, #0x4b
    //     0xe72c80: str             w0, [x25]
    //     0xe72c84: tbz             w0, #0, #0xe72ca0
    //     0xe72c88: ldurb           w16, [x1, #-1]
    //     0xe72c8c: ldurb           w17, [x0, #-1]
    //     0xe72c90: and             x16, x17, x16, lsr #2
    //     0xe72c94: tst             x16, HEAP, lsr #32
    //     0xe72c98: b.eq            #0xe72ca0
    //     0xe72c9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72ca0: r16 = " 0,0,0 "
    //     0xe72ca0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec50] " 0,0,0 "
    //     0xe72ca4: ldr             x16, [x16, #0xc50]
    // 0xe72ca8: StoreField: r2->field_4f = r16
    //     0xe72ca8: stur            w16, [x2, #0x4f]
    // 0xe72cac: mov             x1, x2
    // 0xe72cb0: mov             x0, x3
    // 0xe72cb4: ArrayStore: r1[17] = r0  ; List_4
    //     0xe72cb4: add             x25, x1, #0x53
    //     0xe72cb8: str             w0, [x25]
    //     0xe72cbc: tbz             w0, #0, #0xe72cd8
    //     0xe72cc0: ldurb           w16, [x1, #-1]
    //     0xe72cc4: ldurb           w17, [x0, #-1]
    //     0xe72cc8: and             x16, x17, x16, lsr #2
    //     0xe72ccc: tst             x16, HEAP, lsr #32
    //     0xe72cd0: b.eq            #0xe72cd8
    //     0xe72cd4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72cd8: r16 = ","
    //     0xe72cd8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe72cdc: ldr             x16, [x16, #0x5f8]
    // 0xe72ce0: StoreField: r2->field_57 = r16
    //     0xe72ce0: stur            w16, [x2, #0x57]
    // 0xe72ce4: mov             x1, x2
    // 0xe72ce8: mov             x0, x4
    // 0xe72cec: ArrayStore: r1[19] = r0  ; List_4
    //     0xe72cec: add             x25, x1, #0x5b
    //     0xe72cf0: str             w0, [x25]
    //     0xe72cf4: tbz             w0, #0, #0xe72d10
    //     0xe72cf8: ldurb           w16, [x1, #-1]
    //     0xe72cfc: ldurb           w17, [x0, #-1]
    //     0xe72d00: and             x16, x17, x16, lsr #2
    //     0xe72d04: tst             x16, HEAP, lsr #32
    //     0xe72d08: b.eq            #0xe72d10
    //     0xe72d0c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72d10: r16 = "z"
    //     0xe72d10: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b828] "z"
    //     0xe72d14: ldr             x16, [x16, #0x828]
    // 0xe72d18: StoreField: r2->field_5f = r16
    //     0xe72d18: stur            w16, [x2, #0x5f]
    // 0xe72d1c: str             x2, [SP]
    // 0xe72d20: r0 = _interpolate()
    //     0xe72d20: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe72d24: ldur            x2, [fp, #-8]
    // 0xe72d28: ldur            x3, [fp, #-0x10]
    // 0xe72d2c: ldur            x5, [fp, #-0x18]
    // 0xe72d30: r1 = Null
    //     0xe72d30: mov             x1, NULL
    // 0xe72d34: stur            x0, [fp, #-0x20]
    // 0xe72d38: r0 = SvgClipPath.fromXml()
    //     0xe72d38: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe72d3c: ldur            x2, [fp, #-8]
    // 0xe72d40: r1 = Null
    //     0xe72d40: mov             x1, NULL
    // 0xe72d44: stur            x0, [fp, #-8]
    // 0xe72d48: r0 = SvgTransform.fromXml()
    //     0xe72d48: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe72d4c: stur            x0, [fp, #-0x28]
    // 0xe72d50: r0 = SvgPath()
    //     0xe72d50: bl              #0xe71f28  ; AllocateSvgPathStub -> SvgPath (size=0x1c)
    // 0xe72d54: ldur            x1, [fp, #-0x20]
    // 0xe72d58: ArrayStore: r0[0] = r1  ; List_4
    //     0xe72d58: stur            w1, [x0, #0x17]
    // 0xe72d5c: ldur            x1, [fp, #-0x18]
    // 0xe72d60: StoreField: r0->field_7 = r1
    //     0xe72d60: stur            w1, [x0, #7]
    // 0xe72d64: ldur            x1, [fp, #-8]
    // 0xe72d68: StoreField: r0->field_b = r1
    //     0xe72d68: stur            w1, [x0, #0xb]
    // 0xe72d6c: ldur            x1, [fp, #-0x28]
    // 0xe72d70: StoreField: r0->field_f = r1
    //     0xe72d70: stur            w1, [x0, #0xf]
    // 0xe72d74: ldur            x1, [fp, #-0x10]
    // 0xe72d78: StoreField: r0->field_13 = r1
    //     0xe72d78: stur            w1, [x0, #0x13]
    // 0xe72d7c: LeaveFrame
    //     0xe72d7c: mov             SP, fp
    //     0xe72d80: ldp             fp, lr, [SP], #0x10
    // 0xe72d84: ret
    //     0xe72d84: ret             
    // 0xe72d88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe72d88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe72d8c: b               #0xe72930
    // 0xe72d90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe72d90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe72d94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe72d94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe72d98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe72d98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe72d9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe72d9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe72da0: stp             q1, q2, [SP, #-0x20]!
    // 0xe72da4: SaveReg d0
    //     0xe72da4: str             q0, [SP, #-0x10]!
    // 0xe72da8: SaveReg r2
    //     0xe72da8: str             x2, [SP, #-8]!
    // 0xe72dac: r0 = AllocateDouble()
    //     0xe72dac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe72db0: mov             x3, x0
    // 0xe72db4: RestoreReg r2
    //     0xe72db4: ldr             x2, [SP], #8
    // 0xe72db8: RestoreReg d0
    //     0xe72db8: ldr             q0, [SP], #0x10
    // 0xe72dbc: ldp             q1, q2, [SP], #0x20
    // 0xe72dc0: b               #0xe72a40
    // 0xe72dc4: stp             q1, q2, [SP, #-0x20]!
    // 0xe72dc8: SaveReg d0
    //     0xe72dc8: str             q0, [SP, #-0x10]!
    // 0xe72dcc: stp             x2, x3, [SP, #-0x10]!
    // 0xe72dd0: r0 = AllocateDouble()
    //     0xe72dd0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe72dd4: mov             x4, x0
    // 0xe72dd8: ldp             x2, x3, [SP], #0x10
    // 0xe72ddc: RestoreReg d0
    //     0xe72ddc: ldr             q0, [SP], #0x10
    // 0xe72de0: ldp             q1, q2, [SP], #0x20
    // 0xe72de4: b               #0xe72aa4
    // 0xe72de8: stp             q0, q1, [SP, #-0x20]!
    // 0xe72dec: stp             x3, x4, [SP, #-0x10]!
    // 0xe72df0: SaveReg r2
    //     0xe72df0: str             x2, [SP, #-8]!
    // 0xe72df4: r0 = AllocateDouble()
    //     0xe72df4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe72df8: mov             x5, x0
    // 0xe72dfc: RestoreReg r2
    //     0xe72dfc: ldr             x2, [SP], #8
    // 0xe72e00: ldp             x3, x4, [SP], #0x10
    // 0xe72e04: ldp             q0, q1, [SP], #0x20
    // 0xe72e08: b               #0xe72b04
    // 0xe72e0c: stp             q1, q2, [SP, #-0x20]!
    // 0xe72e10: SaveReg d0
    //     0xe72e10: str             q0, [SP, #-0x10]!
    // 0xe72e14: stp             x4, x5, [SP, #-0x10]!
    // 0xe72e18: stp             x2, x3, [SP, #-0x10]!
    // 0xe72e1c: r0 = AllocateDouble()
    //     0xe72e1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe72e20: mov             x6, x0
    // 0xe72e24: ldp             x2, x3, [SP], #0x10
    // 0xe72e28: ldp             x4, x5, [SP], #0x10
    // 0xe72e2c: RestoreReg d0
    //     0xe72e2c: ldr             q0, [SP], #0x10
    // 0xe72e30: ldp             q1, q2, [SP], #0x20
    // 0xe72e34: b               #0xe72b68
    // 0xe72e38: SaveReg d2
    //     0xe72e38: str             q2, [SP, #-0x10]!
    // 0xe72e3c: stp             x5, x6, [SP, #-0x10]!
    // 0xe72e40: stp             x3, x4, [SP, #-0x10]!
    // 0xe72e44: SaveReg r2
    //     0xe72e44: str             x2, [SP, #-8]!
    // 0xe72e48: r0 = AllocateDouble()
    //     0xe72e48: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe72e4c: RestoreReg r2
    //     0xe72e4c: ldr             x2, [SP], #8
    // 0xe72e50: ldp             x3, x4, [SP], #0x10
    // 0xe72e54: ldp             x5, x6, [SP], #0x10
    // 0xe72e58: RestoreReg d2
    //     0xe72e58: ldr             q2, [SP], #0x10
    // 0xe72e5c: b               #0xe72bcc
  }
  factory _ SvgPath.fromCircleXml(/* No info */) {
    // ** addr: 0xe72e60, size: 0x4cc
    // 0xe72e60: EnterFrame
    //     0xe72e60: stp             fp, lr, [SP, #-0x10]!
    //     0xe72e64: mov             fp, SP
    // 0xe72e68: AllocStack(0x48)
    //     0xe72e68: sub             SP, SP, #0x48
    // 0xe72e6c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3 */)
    //     0xe72e6c: mov             x4, x2
    //     0xe72e70: mov             x0, x3
    //     0xe72e74: stur            x3, [fp, #-0x10]
    //     0xe72e78: mov             x3, x5
    //     0xe72e7c: stur            x2, [fp, #-8]
    // 0xe72e80: CheckStackOverflow
    //     0xe72e80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe72e84: cmp             SP, x16
    //     0xe72e88: b.ls            #0xe7328c
    // 0xe72e8c: mov             x2, x4
    // 0xe72e90: mov             x5, x0
    // 0xe72e94: r1 = Null
    //     0xe72e94: mov             x1, NULL
    // 0xe72e98: r0 = SvgBrush.fromXml()
    //     0xe72e98: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe72e9c: ldur            x1, [fp, #-8]
    // 0xe72ea0: mov             x3, x0
    // 0xe72ea4: r2 = "cx"
    //     0xe72ea4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26120] "cx"
    //     0xe72ea8: ldr             x2, [x2, #0x120]
    // 0xe72eac: stur            x0, [fp, #-0x18]
    // 0xe72eb0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe72eb0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe72eb4: r0 = getNumeric()
    //     0xe72eb4: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe72eb8: cmp             w0, NULL
    // 0xe72ebc: b.eq            #0xe73294
    // 0xe72ec0: mov             x1, x0
    // 0xe72ec4: r0 = sizeValue()
    //     0xe72ec4: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe72ec8: ldur            x1, [fp, #-8]
    // 0xe72ecc: ldur            x3, [fp, #-0x18]
    // 0xe72ed0: r2 = "cy"
    //     0xe72ed0: add             x2, PP, #9, lsl #12  ; [pp+0x9560] "cy"
    //     0xe72ed4: ldr             x2, [x2, #0x560]
    // 0xe72ed8: stur            d0, [fp, #-0x30]
    // 0xe72edc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe72edc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe72ee0: r0 = getNumeric()
    //     0xe72ee0: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe72ee4: cmp             w0, NULL
    // 0xe72ee8: b.eq            #0xe73298
    // 0xe72eec: mov             x1, x0
    // 0xe72ef0: r0 = sizeValue()
    //     0xe72ef0: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe72ef4: ldur            x1, [fp, #-8]
    // 0xe72ef8: ldur            x3, [fp, #-0x18]
    // 0xe72efc: r2 = "r"
    //     0xe72efc: add             x2, PP, #0x26, lsl #12  ; [pp+0x262f0] "r"
    //     0xe72f00: ldr             x2, [x2, #0x2f0]
    // 0xe72f04: stur            d0, [fp, #-0x38]
    // 0xe72f08: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe72f08: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe72f0c: r0 = getNumeric()
    //     0xe72f0c: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe72f10: cmp             w0, NULL
    // 0xe72f14: b.eq            #0xe7329c
    // 0xe72f18: mov             x1, x0
    // 0xe72f1c: r0 = sizeValue()
    //     0xe72f1c: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe72f20: r1 = Null
    //     0xe72f20: mov             x1, NULL
    // 0xe72f24: r2 = 42
    //     0xe72f24: movz            x2, #0x2a
    // 0xe72f28: stur            d0, [fp, #-0x40]
    // 0xe72f2c: r0 = AllocateArray()
    //     0xe72f2c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe72f30: mov             x2, x0
    // 0xe72f34: r16 = "M"
    //     0xe72f34: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b808] "M"
    //     0xe72f38: ldr             x16, [x16, #0x808]
    // 0xe72f3c: StoreField: r2->field_f = r16
    //     0xe72f3c: stur            w16, [x2, #0xf]
    // 0xe72f40: ldur            d1, [fp, #-0x30]
    // 0xe72f44: ldur            d0, [fp, #-0x40]
    // 0xe72f48: fsub            d2, d1, d0
    // 0xe72f4c: r3 = inline_Allocate_Double()
    //     0xe72f4c: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xe72f50: add             x3, x3, #0x10
    //     0xe72f54: cmp             x0, x3
    //     0xe72f58: b.ls            #0xe732a0
    //     0xe72f5c: str             x3, [THR, #0x50]  ; THR::top
    //     0xe72f60: sub             x3, x3, #0xf
    //     0xe72f64: movz            x0, #0xe15c
    //     0xe72f68: movk            x0, #0x3, lsl #16
    //     0xe72f6c: stur            x0, [x3, #-1]
    // 0xe72f70: StoreField: r3->field_7 = d2
    //     0xe72f70: stur            d2, [x3, #7]
    // 0xe72f74: mov             x1, x2
    // 0xe72f78: mov             x0, x3
    // 0xe72f7c: ArrayStore: r1[1] = r0  ; List_4
    //     0xe72f7c: add             x25, x1, #0x13
    //     0xe72f80: str             w0, [x25]
    //     0xe72f84: tbz             w0, #0, #0xe72fa0
    //     0xe72f88: ldurb           w16, [x1, #-1]
    //     0xe72f8c: ldurb           w17, [x0, #-1]
    //     0xe72f90: and             x16, x17, x16, lsr #2
    //     0xe72f94: tst             x16, HEAP, lsr #32
    //     0xe72f98: b.eq            #0xe72fa0
    //     0xe72f9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe72fa0: r16 = ","
    //     0xe72fa0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe72fa4: ldr             x16, [x16, #0x5f8]
    // 0xe72fa8: ArrayStore: r2[0] = r16  ; List_4
    //     0xe72fa8: stur            w16, [x2, #0x17]
    // 0xe72fac: ldur            d2, [fp, #-0x38]
    // 0xe72fb0: r4 = inline_Allocate_Double()
    //     0xe72fb0: ldp             x4, x0, [THR, #0x50]  ; THR::top
    //     0xe72fb4: add             x4, x4, #0x10
    //     0xe72fb8: cmp             x0, x4
    //     0xe72fbc: b.ls            #0xe732c4
    //     0xe72fc0: str             x4, [THR, #0x50]  ; THR::top
    //     0xe72fc4: sub             x4, x4, #0xf
    //     0xe72fc8: movz            x0, #0xe15c
    //     0xe72fcc: movk            x0, #0x3, lsl #16
    //     0xe72fd0: stur            x0, [x4, #-1]
    // 0xe72fd4: StoreField: r4->field_7 = d2
    //     0xe72fd4: stur            d2, [x4, #7]
    // 0xe72fd8: mov             x1, x2
    // 0xe72fdc: mov             x0, x4
    // 0xe72fe0: ArrayStore: r1[3] = r0  ; List_4
    //     0xe72fe0: add             x25, x1, #0x1b
    //     0xe72fe4: str             w0, [x25]
    //     0xe72fe8: tbz             w0, #0, #0xe73004
    //     0xe72fec: ldurb           w16, [x1, #-1]
    //     0xe72ff0: ldurb           w17, [x0, #-1]
    //     0xe72ff4: and             x16, x17, x16, lsr #2
    //     0xe72ff8: tst             x16, HEAP, lsr #32
    //     0xe72ffc: b.eq            #0xe73004
    //     0xe73000: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe73004: r16 = "A"
    //     0xe73004: add             x16, PP, #0x35, lsl #12  ; [pp+0x35420] "A"
    //     0xe73008: ldr             x16, [x16, #0x420]
    // 0xe7300c: StoreField: r2->field_1f = r16
    //     0xe7300c: stur            w16, [x2, #0x1f]
    // 0xe73010: r5 = inline_Allocate_Double()
    //     0xe73010: ldp             x5, x0, [THR, #0x50]  ; THR::top
    //     0xe73014: add             x5, x5, #0x10
    //     0xe73018: cmp             x0, x5
    //     0xe7301c: b.ls            #0xe732e8
    //     0xe73020: str             x5, [THR, #0x50]  ; THR::top
    //     0xe73024: sub             x5, x5, #0xf
    //     0xe73028: movz            x0, #0xe15c
    //     0xe7302c: movk            x0, #0x3, lsl #16
    //     0xe73030: stur            x0, [x5, #-1]
    // 0xe73034: StoreField: r5->field_7 = d0
    //     0xe73034: stur            d0, [x5, #7]
    // 0xe73038: mov             x1, x2
    // 0xe7303c: mov             x0, x5
    // 0xe73040: ArrayStore: r1[5] = r0  ; List_4
    //     0xe73040: add             x25, x1, #0x23
    //     0xe73044: str             w0, [x25]
    //     0xe73048: tbz             w0, #0, #0xe73064
    //     0xe7304c: ldurb           w16, [x1, #-1]
    //     0xe73050: ldurb           w17, [x0, #-1]
    //     0xe73054: and             x16, x17, x16, lsr #2
    //     0xe73058: tst             x16, HEAP, lsr #32
    //     0xe7305c: b.eq            #0xe73064
    //     0xe73060: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe73064: r16 = ","
    //     0xe73064: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe73068: ldr             x16, [x16, #0x5f8]
    // 0xe7306c: StoreField: r2->field_27 = r16
    //     0xe7306c: stur            w16, [x2, #0x27]
    // 0xe73070: mov             x1, x2
    // 0xe73074: mov             x0, x5
    // 0xe73078: ArrayStore: r1[7] = r0  ; List_4
    //     0xe73078: add             x25, x1, #0x2b
    //     0xe7307c: str             w0, [x25]
    //     0xe73080: tbz             w0, #0, #0xe7309c
    //     0xe73084: ldurb           w16, [x1, #-1]
    //     0xe73088: ldurb           w17, [x0, #-1]
    //     0xe7308c: and             x16, x17, x16, lsr #2
    //     0xe73090: tst             x16, HEAP, lsr #32
    //     0xe73094: b.eq            #0xe7309c
    //     0xe73098: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7309c: r16 = " 0,0,0 "
    //     0xe7309c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec50] " 0,0,0 "
    //     0xe730a0: ldr             x16, [x16, #0xc50]
    // 0xe730a4: StoreField: r2->field_2f = r16
    //     0xe730a4: stur            w16, [x2, #0x2f]
    // 0xe730a8: fadd            d2, d1, d0
    // 0xe730ac: r0 = inline_Allocate_Double()
    //     0xe730ac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe730b0: add             x0, x0, #0x10
    //     0xe730b4: cmp             x1, x0
    //     0xe730b8: b.ls            #0xe7330c
    //     0xe730bc: str             x0, [THR, #0x50]  ; THR::top
    //     0xe730c0: sub             x0, x0, #0xf
    //     0xe730c4: movz            x1, #0xe15c
    //     0xe730c8: movk            x1, #0x3, lsl #16
    //     0xe730cc: stur            x1, [x0, #-1]
    // 0xe730d0: StoreField: r0->field_7 = d2
    //     0xe730d0: stur            d2, [x0, #7]
    // 0xe730d4: mov             x1, x2
    // 0xe730d8: ArrayStore: r1[9] = r0  ; List_4
    //     0xe730d8: add             x25, x1, #0x33
    //     0xe730dc: str             w0, [x25]
    //     0xe730e0: tbz             w0, #0, #0xe730fc
    //     0xe730e4: ldurb           w16, [x1, #-1]
    //     0xe730e8: ldurb           w17, [x0, #-1]
    //     0xe730ec: and             x16, x17, x16, lsr #2
    //     0xe730f0: tst             x16, HEAP, lsr #32
    //     0xe730f4: b.eq            #0xe730fc
    //     0xe730f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe730fc: r16 = ","
    //     0xe730fc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe73100: ldr             x16, [x16, #0x5f8]
    // 0xe73104: StoreField: r2->field_37 = r16
    //     0xe73104: stur            w16, [x2, #0x37]
    // 0xe73108: mov             x1, x2
    // 0xe7310c: mov             x0, x4
    // 0xe73110: ArrayStore: r1[11] = r0  ; List_4
    //     0xe73110: add             x25, x1, #0x3b
    //     0xe73114: str             w0, [x25]
    //     0xe73118: tbz             w0, #0, #0xe73134
    //     0xe7311c: ldurb           w16, [x1, #-1]
    //     0xe73120: ldurb           w17, [x0, #-1]
    //     0xe73124: and             x16, x17, x16, lsr #2
    //     0xe73128: tst             x16, HEAP, lsr #32
    //     0xe7312c: b.eq            #0xe73134
    //     0xe73130: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe73134: r16 = "A"
    //     0xe73134: add             x16, PP, #0x35, lsl #12  ; [pp+0x35420] "A"
    //     0xe73138: ldr             x16, [x16, #0x420]
    // 0xe7313c: StoreField: r2->field_3f = r16
    //     0xe7313c: stur            w16, [x2, #0x3f]
    // 0xe73140: mov             x1, x2
    // 0xe73144: mov             x0, x5
    // 0xe73148: ArrayStore: r1[13] = r0  ; List_4
    //     0xe73148: add             x25, x1, #0x43
    //     0xe7314c: str             w0, [x25]
    //     0xe73150: tbz             w0, #0, #0xe7316c
    //     0xe73154: ldurb           w16, [x1, #-1]
    //     0xe73158: ldurb           w17, [x0, #-1]
    //     0xe7315c: and             x16, x17, x16, lsr #2
    //     0xe73160: tst             x16, HEAP, lsr #32
    //     0xe73164: b.eq            #0xe7316c
    //     0xe73168: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7316c: r16 = ","
    //     0xe7316c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe73170: ldr             x16, [x16, #0x5f8]
    // 0xe73174: StoreField: r2->field_47 = r16
    //     0xe73174: stur            w16, [x2, #0x47]
    // 0xe73178: mov             x1, x2
    // 0xe7317c: mov             x0, x5
    // 0xe73180: ArrayStore: r1[15] = r0  ; List_4
    //     0xe73180: add             x25, x1, #0x4b
    //     0xe73184: str             w0, [x25]
    //     0xe73188: tbz             w0, #0, #0xe731a4
    //     0xe7318c: ldurb           w16, [x1, #-1]
    //     0xe73190: ldurb           w17, [x0, #-1]
    //     0xe73194: and             x16, x17, x16, lsr #2
    //     0xe73198: tst             x16, HEAP, lsr #32
    //     0xe7319c: b.eq            #0xe731a4
    //     0xe731a0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe731a4: r16 = " 0,0,0 "
    //     0xe731a4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec50] " 0,0,0 "
    //     0xe731a8: ldr             x16, [x16, #0xc50]
    // 0xe731ac: StoreField: r2->field_4f = r16
    //     0xe731ac: stur            w16, [x2, #0x4f]
    // 0xe731b0: mov             x1, x2
    // 0xe731b4: mov             x0, x3
    // 0xe731b8: ArrayStore: r1[17] = r0  ; List_4
    //     0xe731b8: add             x25, x1, #0x53
    //     0xe731bc: str             w0, [x25]
    //     0xe731c0: tbz             w0, #0, #0xe731dc
    //     0xe731c4: ldurb           w16, [x1, #-1]
    //     0xe731c8: ldurb           w17, [x0, #-1]
    //     0xe731cc: and             x16, x17, x16, lsr #2
    //     0xe731d0: tst             x16, HEAP, lsr #32
    //     0xe731d4: b.eq            #0xe731dc
    //     0xe731d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe731dc: r16 = ","
    //     0xe731dc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xe731e0: ldr             x16, [x16, #0x5f8]
    // 0xe731e4: StoreField: r2->field_57 = r16
    //     0xe731e4: stur            w16, [x2, #0x57]
    // 0xe731e8: mov             x1, x2
    // 0xe731ec: mov             x0, x4
    // 0xe731f0: ArrayStore: r1[19] = r0  ; List_4
    //     0xe731f0: add             x25, x1, #0x5b
    //     0xe731f4: str             w0, [x25]
    //     0xe731f8: tbz             w0, #0, #0xe73214
    //     0xe731fc: ldurb           w16, [x1, #-1]
    //     0xe73200: ldurb           w17, [x0, #-1]
    //     0xe73204: and             x16, x17, x16, lsr #2
    //     0xe73208: tst             x16, HEAP, lsr #32
    //     0xe7320c: b.eq            #0xe73214
    //     0xe73210: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe73214: r16 = "z"
    //     0xe73214: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b828] "z"
    //     0xe73218: ldr             x16, [x16, #0x828]
    // 0xe7321c: StoreField: r2->field_5f = r16
    //     0xe7321c: stur            w16, [x2, #0x5f]
    // 0xe73220: str             x2, [SP]
    // 0xe73224: r0 = _interpolate()
    //     0xe73224: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe73228: ldur            x2, [fp, #-8]
    // 0xe7322c: ldur            x3, [fp, #-0x10]
    // 0xe73230: ldur            x5, [fp, #-0x18]
    // 0xe73234: r1 = Null
    //     0xe73234: mov             x1, NULL
    // 0xe73238: stur            x0, [fp, #-0x20]
    // 0xe7323c: r0 = SvgClipPath.fromXml()
    //     0xe7323c: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe73240: ldur            x2, [fp, #-8]
    // 0xe73244: r1 = Null
    //     0xe73244: mov             x1, NULL
    // 0xe73248: stur            x0, [fp, #-8]
    // 0xe7324c: r0 = SvgTransform.fromXml()
    //     0xe7324c: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe73250: stur            x0, [fp, #-0x28]
    // 0xe73254: r0 = SvgPath()
    //     0xe73254: bl              #0xe71f28  ; AllocateSvgPathStub -> SvgPath (size=0x1c)
    // 0xe73258: ldur            x1, [fp, #-0x20]
    // 0xe7325c: ArrayStore: r0[0] = r1  ; List_4
    //     0xe7325c: stur            w1, [x0, #0x17]
    // 0xe73260: ldur            x1, [fp, #-0x18]
    // 0xe73264: StoreField: r0->field_7 = r1
    //     0xe73264: stur            w1, [x0, #7]
    // 0xe73268: ldur            x1, [fp, #-8]
    // 0xe7326c: StoreField: r0->field_b = r1
    //     0xe7326c: stur            w1, [x0, #0xb]
    // 0xe73270: ldur            x1, [fp, #-0x28]
    // 0xe73274: StoreField: r0->field_f = r1
    //     0xe73274: stur            w1, [x0, #0xf]
    // 0xe73278: ldur            x1, [fp, #-0x10]
    // 0xe7327c: StoreField: r0->field_13 = r1
    //     0xe7327c: stur            w1, [x0, #0x13]
    // 0xe73280: LeaveFrame
    //     0xe73280: mov             SP, fp
    //     0xe73284: ldp             fp, lr, [SP], #0x10
    // 0xe73288: ret
    //     0xe73288: ret             
    // 0xe7328c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7328c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe73290: b               #0xe72e8c
    // 0xe73294: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe73294: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe73298: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe73298: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe7329c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe7329c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe732a0: stp             q1, q2, [SP, #-0x20]!
    // 0xe732a4: SaveReg d0
    //     0xe732a4: str             q0, [SP, #-0x10]!
    // 0xe732a8: SaveReg r2
    //     0xe732a8: str             x2, [SP, #-8]!
    // 0xe732ac: r0 = AllocateDouble()
    //     0xe732ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe732b0: mov             x3, x0
    // 0xe732b4: RestoreReg r2
    //     0xe732b4: ldr             x2, [SP], #8
    // 0xe732b8: RestoreReg d0
    //     0xe732b8: ldr             q0, [SP], #0x10
    // 0xe732bc: ldp             q1, q2, [SP], #0x20
    // 0xe732c0: b               #0xe72f70
    // 0xe732c4: stp             q1, q2, [SP, #-0x20]!
    // 0xe732c8: SaveReg d0
    //     0xe732c8: str             q0, [SP, #-0x10]!
    // 0xe732cc: stp             x2, x3, [SP, #-0x10]!
    // 0xe732d0: r0 = AllocateDouble()
    //     0xe732d0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe732d4: mov             x4, x0
    // 0xe732d8: ldp             x2, x3, [SP], #0x10
    // 0xe732dc: RestoreReg d0
    //     0xe732dc: ldr             q0, [SP], #0x10
    // 0xe732e0: ldp             q1, q2, [SP], #0x20
    // 0xe732e4: b               #0xe72fd4
    // 0xe732e8: stp             q0, q1, [SP, #-0x20]!
    // 0xe732ec: stp             x3, x4, [SP, #-0x10]!
    // 0xe732f0: SaveReg r2
    //     0xe732f0: str             x2, [SP, #-8]!
    // 0xe732f4: r0 = AllocateDouble()
    //     0xe732f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe732f8: mov             x5, x0
    // 0xe732fc: RestoreReg r2
    //     0xe732fc: ldr             x2, [SP], #8
    // 0xe73300: ldp             x3, x4, [SP], #0x10
    // 0xe73304: ldp             q0, q1, [SP], #0x20
    // 0xe73308: b               #0xe73034
    // 0xe7330c: SaveReg d2
    //     0xe7330c: str             q2, [SP, #-0x10]!
    // 0xe73310: stp             x4, x5, [SP, #-0x10]!
    // 0xe73314: stp             x2, x3, [SP, #-0x10]!
    // 0xe73318: r0 = AllocateDouble()
    //     0xe73318: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7331c: ldp             x2, x3, [SP], #0x10
    // 0xe73320: ldp             x4, x5, [SP], #0x10
    // 0xe73324: RestoreReg d2
    //     0xe73324: ldr             q2, [SP], #0x10
    // 0xe73328: b               #0xe730d0
  }
  _ boundingBox(/* No info */) {
    // ** addr: 0xeab3c8, size: 0x38
    // 0xeab3c8: EnterFrame
    //     0xeab3c8: stp             fp, lr, [SP, #-0x10]!
    //     0xeab3cc: mov             fp, SP
    // 0xeab3d0: CheckStackOverflow
    //     0xeab3d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeab3d4: cmp             SP, x16
    //     0xeab3d8: b.ls            #0xeab3f8
    // 0xeab3dc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xeab3dc: ldur            w0, [x1, #0x17]
    // 0xeab3e0: DecompressPointer r0
    //     0xeab3e0: add             x0, x0, HEAP, lsl #32
    // 0xeab3e4: mov             x1, x0
    // 0xeab3e8: r0 = shapeBoundingBox()
    //     0xeab3e8: bl              #0xeab400  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::shapeBoundingBox
    // 0xeab3ec: LeaveFrame
    //     0xeab3ec: mov             SP, fp
    //     0xeab3f0: ldp             fp, lr, [SP], #0x10
    // 0xeab3f4: ret
    //     0xeab3f4: ret             
    // 0xeab3f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeab3f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeab3fc: b               #0xeab3dc
  }
}
