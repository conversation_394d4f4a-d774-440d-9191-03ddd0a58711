// lib: , url: package:pdf/src/svg/symbol.dart

// class id: 1050838, size: 0x8
class :: {
}

// class id: 848, size: 0x1c, field offset: 0x1c
class SvgSymbol extends SvgGroup {

  _ paintShape(/* No info */) {
    // ** addr: 0xe46510, size: 0x1f4
    // 0xe46510: EnterFrame
    //     0xe46510: stp             fp, lr, [SP, #-0x10]!
    //     0xe46514: mov             fp, SP
    // 0xe46518: AllocStack(0x20)
    //     0xe46518: sub             SP, SP, #0x20
    // 0xe4651c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe4651c: stur            x2, [fp, #-8]
    // 0xe46520: CheckStackOverflow
    //     0xe46520: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe46524: cmp             SP, x16
    //     0xe46528: b.ls            #0xe466ec
    // 0xe4652c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xe4652c: ldur            w0, [x1, #0x17]
    // 0xe46530: DecompressPointer r0
    //     0xe46530: add             x0, x0, HEAP, lsl #32
    // 0xe46534: mov             x1, x0
    // 0xe46538: r0 = iterator()
    //     0xe46538: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0xe4653c: LoadField: r2 = r0->field_b
    //     0xe4653c: ldur            w2, [x0, #0xb]
    // 0xe46540: DecompressPointer r2
    //     0xe46540: add             x2, x2, HEAP, lsl #32
    // 0xe46544: stur            x2, [fp, #-0x18]
    // 0xe46548: LoadField: r3 = r0->field_7
    //     0xe46548: ldur            w3, [x0, #7]
    // 0xe4654c: DecompressPointer r3
    //     0xe4654c: add             x3, x3, HEAP, lsl #32
    // 0xe46550: stur            x3, [fp, #-0x10]
    // 0xe46554: CheckStackOverflow
    //     0xe46554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe46558: cmp             SP, x16
    //     0xe4655c: b.ls            #0xe466f4
    // 0xe46560: CheckStackOverflow
    //     0xe46560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe46564: cmp             SP, x16
    //     0xe46568: b.ls            #0xe466fc
    // 0xe4656c: r0 = LoadClassIdInstr(r2)
    //     0xe4656c: ldur            x0, [x2, #-1]
    //     0xe46570: ubfx            x0, x0, #0xc, #0x14
    // 0xe46574: mov             x1, x2
    // 0xe46578: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe46578: movz            x17, #0x292d
    //     0xe4657c: movk            x17, #0x1, lsl #16
    //     0xe46580: add             lr, x0, x17
    //     0xe46584: ldr             lr, [x21, lr, lsl #3]
    //     0xe46588: blr             lr
    // 0xe4658c: tbnz            w0, #4, #0xe466dc
    // 0xe46590: ldur            x2, [fp, #-0x18]
    // 0xe46594: r0 = LoadClassIdInstr(r2)
    //     0xe46594: ldur            x0, [x2, #-1]
    //     0xe46598: ubfx            x0, x0, #0xc, #0x14
    // 0xe4659c: mov             x1, x2
    // 0xe465a0: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe465a0: movz            x17, #0x384d
    //     0xe465a4: movk            x17, #0x1, lsl #16
    //     0xe465a8: add             lr, x0, x17
    //     0xe465ac: ldr             lr, [x21, lr, lsl #3]
    //     0xe465b0: blr             lr
    // 0xe465b4: ldur            x2, [fp, #-0x10]
    // 0xe465b8: r1 = Null
    //     0xe465b8: mov             x1, NULL
    // 0xe465bc: cmp             w2, NULL
    // 0xe465c0: b.eq            #0xe46658
    // 0xe465c4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe465c4: ldur            w3, [x2, #0x17]
    // 0xe465c8: DecompressPointer r3
    //     0xe465c8: add             x3, x3, HEAP, lsl #32
    // 0xe465cc: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xe465d0: cmp             w3, w16
    // 0xe465d4: b.eq            #0xe46658
    // 0xe465d8: r16 = Object?
    //     0xe465d8: ldr             x16, [PP, #0x1648]  ; [pp+0x1648] Type: Object?
    // 0xe465dc: cmp             w3, w16
    // 0xe465e0: b.eq            #0xe46658
    // 0xe465e4: r16 = void?
    //     0xe465e4: ldr             x16, [PP, #0x1650]  ; [pp+0x1650] Type: void?
    // 0xe465e8: cmp             w3, w16
    // 0xe465ec: b.eq            #0xe46658
    // 0xe465f0: tbnz            w0, #0, #0xe4660c
    // 0xe465f4: r16 = int
    //     0xe465f4: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe465f8: cmp             w3, w16
    // 0xe465fc: b.eq            #0xe46658
    // 0xe46600: r16 = num
    //     0xe46600: ldr             x16, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xe46604: cmp             w3, w16
    // 0xe46608: b.eq            #0xe46658
    // 0xe4660c: r3 = SubtypeTestCache
    //     0xe4660c: add             x3, PP, #0x46, lsl #12  ; [pp+0x46dc8] SubtypeTestCache
    //     0xe46610: ldr             x3, [x3, #0xdc8]
    // 0xe46614: r30 = Subtype6TestCacheStub
    //     0xe46614: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27cc)
    // 0xe46618: LoadField: r30 = r30->field_7
    //     0xe46618: ldur            lr, [lr, #7]
    // 0xe4661c: blr             lr
    // 0xe46620: cmp             w7, NULL
    // 0xe46624: b.eq            #0xe46630
    // 0xe46628: tbnz            w7, #4, #0xe46650
    // 0xe4662c: b               #0xe46658
    // 0xe46630: r8 = X0
    //     0xe46630: add             x8, PP, #0x46, lsl #12  ; [pp+0x46dd0] TypeParameter: X0
    //     0xe46634: ldr             x8, [x8, #0xdd0]
    // 0xe46638: r3 = SubtypeTestCache
    //     0xe46638: add             x3, PP, #0x46, lsl #12  ; [pp+0x46dd8] SubtypeTestCache
    //     0xe4663c: ldr             x3, [x3, #0xdd8]
    // 0xe46640: r30 = InstanceOfStub
    //     0xe46640: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe46644: LoadField: r30 = r30->field_7
    //     0xe46644: ldur            lr, [lr, #7]
    // 0xe46648: blr             lr
    // 0xe4664c: b               #0xe4665c
    // 0xe46650: r0 = false
    //     0xe46650: add             x0, NULL, #0x30  ; false
    // 0xe46654: b               #0xe4665c
    // 0xe46658: r0 = true
    //     0xe46658: add             x0, NULL, #0x20  ; true
    // 0xe4665c: tbz             w0, #4, #0xe4666c
    // 0xe46660: ldur            x2, [fp, #-0x18]
    // 0xe46664: ldur            x3, [fp, #-0x10]
    // 0xe46668: b               #0xe46560
    // 0xe4666c: ldur            x2, [fp, #-0x18]
    // 0xe46670: r0 = LoadClassIdInstr(r2)
    //     0xe46670: ldur            x0, [x2, #-1]
    //     0xe46674: ubfx            x0, x0, #0xc, #0x14
    // 0xe46678: mov             x1, x2
    // 0xe4667c: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe4667c: movz            x17, #0x384d
    //     0xe46680: movk            x17, #0x1, lsl #16
    //     0xe46684: add             lr, x0, x17
    //     0xe46688: ldr             lr, [x21, lr, lsl #3]
    //     0xe4668c: blr             lr
    // 0xe46690: ldur            x2, [fp, #-0x10]
    // 0xe46694: mov             x3, x0
    // 0xe46698: r1 = Null
    //     0xe46698: mov             x1, NULL
    // 0xe4669c: stur            x3, [fp, #-0x20]
    // 0xe466a0: cmp             w2, NULL
    // 0xe466a4: b.eq            #0xe466c4
    // 0xe466a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe466a8: ldur            w4, [x2, #0x17]
    // 0xe466ac: DecompressPointer r4
    //     0xe466ac: add             x4, x4, HEAP, lsl #32
    // 0xe466b0: r8 = X0
    //     0xe466b0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe466b4: LoadField: r9 = r4->field_7
    //     0xe466b4: ldur            x9, [x4, #7]
    // 0xe466b8: r3 = Null
    //     0xe466b8: add             x3, PP, #0x46, lsl #12  ; [pp+0x46de0] Null
    //     0xe466bc: ldr             x3, [x3, #0xde0]
    // 0xe466c0: blr             x9
    // 0xe466c4: ldur            x1, [fp, #-0x20]
    // 0xe466c8: ldur            x2, [fp, #-8]
    // 0xe466cc: r0 = paint()
    //     0xe466cc: bl              #0xe46704  ; [package:pdf/src/svg/operation.dart] SvgOperation::paint
    // 0xe466d0: ldur            x2, [fp, #-0x18]
    // 0xe466d4: ldur            x3, [fp, #-0x10]
    // 0xe466d8: b               #0xe46554
    // 0xe466dc: r0 = Null
    //     0xe466dc: mov             x0, NULL
    // 0xe466e0: LeaveFrame
    //     0xe466e0: mov             SP, fp
    //     0xe466e4: ldp             fp, lr, [SP], #0x10
    // 0xe466e8: ret
    //     0xe466e8: ret             
    // 0xe466ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe466ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe466f0: b               #0xe4652c
    // 0xe466f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe466f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe466f8: b               #0xe46560
    // 0xe466fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe466fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe46700: b               #0xe4656c
  }
  factory _ SvgSymbol.fromXml(/* No info */) {
    // ** addr: 0xe711f4, size: 0x16c
    // 0xe711f4: EnterFrame
    //     0xe711f4: stp             fp, lr, [SP, #-0x10]!
    //     0xe711f8: mov             fp, SP
    // 0xe711fc: AllocStack(0x48)
    //     0xe711fc: sub             SP, SP, #0x48
    // 0xe71200: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r5, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0xe71200: stur            x3, [fp, #-0x10]
    //     0xe71204: mov             x16, x5
    //     0xe71208: mov             x5, x3
    //     0xe7120c: mov             x3, x16
    //     0xe71210: stur            x2, [fp, #-8]
    //     0xe71214: stur            x3, [fp, #-0x18]
    // 0xe71218: CheckStackOverflow
    //     0xe71218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7121c: cmp             SP, x16
    //     0xe71220: b.ls            #0xe71358
    // 0xe71224: r1 = 2
    //     0xe71224: movz            x1, #0x2
    // 0xe71228: r0 = AllocateContext()
    //     0xe71228: bl              #0xec126c  ; AllocateContextStub
    // 0xe7122c: ldur            x5, [fp, #-0x10]
    // 0xe71230: stur            x0, [fp, #-0x20]
    // 0xe71234: StoreField: r0->field_f = r5
    //     0xe71234: stur            w5, [x0, #0xf]
    // 0xe71238: ldur            x2, [fp, #-8]
    // 0xe7123c: ldur            x3, [fp, #-0x18]
    // 0xe71240: r1 = Null
    //     0xe71240: mov             x1, NULL
    // 0xe71244: r0 = SvgBrush.fromXml()
    //     0xe71244: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe71248: mov             x1, x0
    // 0xe7124c: ldur            x2, [fp, #-0x20]
    // 0xe71250: stur            x1, [fp, #-0x10]
    // 0xe71254: StoreField: r2->field_13 = r0
    //     0xe71254: stur            w0, [x2, #0x13]
    //     0xe71258: ldurb           w16, [x2, #-1]
    //     0xe7125c: ldurb           w17, [x0, #-1]
    //     0xe71260: and             x16, x17, x16, lsr #2
    //     0xe71264: tst             x16, HEAP, lsr #32
    //     0xe71268: b.eq            #0xe71270
    //     0xe7126c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe71270: ldur            x0, [fp, #-8]
    // 0xe71274: LoadField: r3 = r0->field_f
    //     0xe71274: ldur            w3, [x0, #0xf]
    // 0xe71278: DecompressPointer r3
    //     0xe71278: add             x3, x3, HEAP, lsl #32
    // 0xe7127c: r16 = <XmlElement>
    //     0xe7127c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea98] TypeArguments: <XmlElement>
    //     0xe71280: ldr             x16, [x16, #0xa98]
    // 0xe71284: stp             x3, x16, [SP]
    // 0xe71288: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe71288: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe7128c: r0 = whereType()
    //     0xe7128c: bl              #0x7b5364  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::whereType
    // 0xe71290: ldur            x2, [fp, #-0x20]
    // 0xe71294: r1 = Function '<anonymous closure>': static.
    //     0xe71294: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb38] AnonymousClosure: static (0xe6f2bc), in [package:pdf/src/svg/group.dart] SvgGroup::SvgGroup.fromXml (0xe6dfc0)
    //     0xe71298: ldr             x1, [x1, #0xb38]
    // 0xe7129c: stur            x0, [fp, #-0x18]
    // 0xe712a0: r0 = AllocateClosure()
    //     0xe712a0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe712a4: r16 = <SvgOperation?>
    //     0xe712a4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eab0] TypeArguments: <SvgOperation?>
    //     0xe712a8: ldr             x16, [x16, #0xab0]
    // 0xe712ac: ldur            lr, [fp, #-0x18]
    // 0xe712b0: stp             lr, x16, [SP, #8]
    // 0xe712b4: str             x0, [SP]
    // 0xe712b8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe712b8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe712bc: r0 = map()
    //     0xe712bc: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xe712c0: r16 = <SvgOperation>
    //     0xe712c0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eab8] TypeArguments: <SvgOperation>
    //     0xe712c4: ldr             x16, [x16, #0xab8]
    // 0xe712c8: stp             x0, x16, [SP]
    // 0xe712cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe712cc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe712d0: r0 = whereType()
    //     0xe712d0: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0xe712d4: mov             x4, x0
    // 0xe712d8: ldur            x0, [fp, #-0x20]
    // 0xe712dc: stur            x4, [fp, #-0x18]
    // 0xe712e0: LoadField: r3 = r0->field_f
    //     0xe712e0: ldur            w3, [x0, #0xf]
    // 0xe712e4: DecompressPointer r3
    //     0xe712e4: add             x3, x3, HEAP, lsl #32
    // 0xe712e8: ldur            x2, [fp, #-8]
    // 0xe712ec: ldur            x5, [fp, #-0x10]
    // 0xe712f0: r1 = Null
    //     0xe712f0: mov             x1, NULL
    // 0xe712f4: r0 = SvgClipPath.fromXml()
    //     0xe712f4: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe712f8: ldur            x2, [fp, #-8]
    // 0xe712fc: r1 = Null
    //     0xe712fc: mov             x1, NULL
    // 0xe71300: stur            x0, [fp, #-8]
    // 0xe71304: r0 = SvgTransform.fromXml()
    //     0xe71304: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe71308: mov             x1, x0
    // 0xe7130c: ldur            x0, [fp, #-0x20]
    // 0xe71310: stur            x1, [fp, #-0x30]
    // 0xe71314: LoadField: r2 = r0->field_f
    //     0xe71314: ldur            w2, [x0, #0xf]
    // 0xe71318: DecompressPointer r2
    //     0xe71318: add             x2, x2, HEAP, lsl #32
    // 0xe7131c: stur            x2, [fp, #-0x28]
    // 0xe71320: r0 = SvgSymbol()
    //     0xe71320: bl              #0xe71360  ; AllocateSvgSymbolStub -> SvgSymbol (size=0x1c)
    // 0xe71324: ldur            x1, [fp, #-0x18]
    // 0xe71328: ArrayStore: r0[0] = r1  ; List_4
    //     0xe71328: stur            w1, [x0, #0x17]
    // 0xe7132c: ldur            x1, [fp, #-0x10]
    // 0xe71330: StoreField: r0->field_7 = r1
    //     0xe71330: stur            w1, [x0, #7]
    // 0xe71334: ldur            x1, [fp, #-8]
    // 0xe71338: StoreField: r0->field_b = r1
    //     0xe71338: stur            w1, [x0, #0xb]
    // 0xe7133c: ldur            x1, [fp, #-0x30]
    // 0xe71340: StoreField: r0->field_f = r1
    //     0xe71340: stur            w1, [x0, #0xf]
    // 0xe71344: ldur            x1, [fp, #-0x28]
    // 0xe71348: StoreField: r0->field_13 = r1
    //     0xe71348: stur            w1, [x0, #0x13]
    // 0xe7134c: LeaveFrame
    //     0xe7134c: mov             SP, fp
    //     0xe71350: ldp             fp, lr, [SP], #0x10
    // 0xe71354: ret
    //     0xe71354: ret             
    // 0xe71358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe71358: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7135c: b               #0xe71224
  }
}
