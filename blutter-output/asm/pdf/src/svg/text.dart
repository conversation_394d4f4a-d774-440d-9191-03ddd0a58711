// lib: , url: package:pdf/src/svg/text.dart

// class id: 1050839, size: 0x8
class :: {
}

// class id: 844, size: 0x40, field offset: 0x18
class SvgText extends SvgOperation {

  _ paintShape(/* No info */) {
    // ** addr: 0xe499e4, size: 0x47c
    // 0xe499e4: EnterFrame
    //     0xe499e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe499e8: mov             fp, SP
    // 0xe499ec: AllocStack(0x48)
    //     0xe499ec: sub             SP, SP, #0x48
    // 0xe499f0: SetupParameters(SvgText this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe499f0: mov             x0, x2
    //     0xe499f4: stur            x2, [fp, #-0x10]
    //     0xe499f8: mov             x2, x1
    //     0xe499fc: stur            x1, [fp, #-8]
    // 0xe49a00: CheckStackOverflow
    //     0xe49a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49a04: cmp             SP, x16
    //     0xe49a08: b.ls            #0xe49e40
    // 0xe49a0c: mov             x1, x0
    // 0xe49a10: r0 = saveContext()
    //     0xe49a10: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe49a14: r0 = Matrix4()
    //     0xe49a14: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe49a18: r4 = 32
    //     0xe49a18: movz            x4, #0x20
    // 0xe49a1c: stur            x0, [fp, #-0x18]
    // 0xe49a20: r0 = AllocateFloat64Array()
    //     0xe49a20: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe49a24: mov             x1, x0
    // 0xe49a28: ldur            x0, [fp, #-0x18]
    // 0xe49a2c: StoreField: r0->field_7 = r1
    //     0xe49a2c: stur            w1, [x0, #7]
    // 0xe49a30: mov             x1, x0
    // 0xe49a34: r0 = setIdentity()
    //     0xe49a34: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe49a38: r16 = -1.000000
    //     0xe49a38: ldr             x16, [PP, #0x5bd8]  ; [pp+0x5bd8] -1
    // 0xe49a3c: str             x16, [SP]
    // 0xe49a40: ldur            x1, [fp, #-0x18]
    // 0xe49a44: r2 = 1.000000
    //     0xe49a44: ldr             x2, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xe49a48: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe49a48: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe49a4c: r0 = scale()
    //     0xe49a4c: bl              #0x645258  ; [package:vector_math/vector_math_64.dart] Matrix4::scale
    // 0xe49a50: ldur            x2, [fp, #-8]
    // 0xe49a54: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xe49a54: ldur            d0, [x2, #0x17]
    // 0xe49a58: LoadField: d1 = r2->field_1f
    //     0xe49a58: ldur            d1, [x2, #0x1f]
    // 0xe49a5c: fneg            d2, d1
    // 0xe49a60: ldur            x1, [fp, #-0x18]
    // 0xe49a64: mov             v1.16b, v2.16b
    // 0xe49a68: r0 = translate()
    //     0xe49a68: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe49a6c: ldur            x1, [fp, #-0x10]
    // 0xe49a70: ldur            x2, [fp, #-0x18]
    // 0xe49a74: r0 = setTransform()
    //     0xe49a74: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe49a78: ldur            x4, [fp, #-8]
    // 0xe49a7c: LoadField: r5 = r4->field_7
    //     0xe49a7c: ldur            w5, [x4, #7]
    // 0xe49a80: DecompressPointer r5
    //     0xe49a80: add             x5, x5, HEAP, lsl #32
    // 0xe49a84: stur            x5, [fp, #-0x18]
    // 0xe49a88: LoadField: r1 = r5->field_b
    //     0xe49a88: ldur            w1, [x5, #0xb]
    // 0xe49a8c: DecompressPointer r1
    //     0xe49a8c: add             x1, x1, HEAP, lsl #32
    // 0xe49a90: r0 = LoadClassIdInstr(r1)
    //     0xe49a90: ldur            x0, [x1, #-1]
    //     0xe49a94: ubfx            x0, x0, #0xc, #0x14
    // 0xe49a98: sub             x16, x0, #0x353
    // 0xe49a9c: cmp             x16, #1
    // 0xe49aa0: b.hi            #0xe49ac4
    // 0xe49aa4: LoadField: r0 = r1->field_1b
    //     0xe49aa4: ldur            w0, [x1, #0x1b]
    // 0xe49aa8: DecompressPointer r0
    //     0xe49aa8: add             x0, x0, HEAP, lsl #32
    // 0xe49aac: LoadField: r2 = r0->field_b
    //     0xe49aac: ldur            w2, [x0, #0xb]
    // 0xe49ab0: cbz             w2, #0xe49abc
    // 0xe49ab4: r0 = false
    //     0xe49ab4: add             x0, NULL, #0x30  ; false
    // 0xe49ab8: b               #0xe49ac0
    // 0xe49abc: r0 = true
    //     0xe49abc: add             x0, NULL, #0x20  ; true
    // 0xe49ac0: b               #0xe49ae0
    // 0xe49ac4: LoadField: r0 = r1->field_7
    //     0xe49ac4: ldur            w0, [x1, #7]
    // 0xe49ac8: DecompressPointer r0
    //     0xe49ac8: add             x0, x0, HEAP, lsl #32
    // 0xe49acc: cmp             w0, NULL
    // 0xe49ad0: r16 = true
    //     0xe49ad0: add             x16, NULL, #0x20  ; true
    // 0xe49ad4: r17 = false
    //     0xe49ad4: add             x17, NULL, #0x30  ; false
    // 0xe49ad8: csel            x2, x16, x17, eq
    // 0xe49adc: mov             x0, x2
    // 0xe49ae0: eor             x2, x0, #0x10
    // 0xe49ae4: tbnz            w2, #4, #0xe49bc0
    // 0xe49ae8: r0 = LoadClassIdInstr(r1)
    //     0xe49ae8: ldur            x0, [x1, #-1]
    //     0xe49aec: ubfx            x0, x0, #0xc, #0x14
    // 0xe49af0: mov             x2, x4
    // 0xe49af4: ldur            x3, [fp, #-0x10]
    // 0xe49af8: r0 = GDT[cid_x0 + -0xfef]()
    //     0xe49af8: sub             lr, x0, #0xfef
    //     0xe49afc: ldr             lr, [x21, lr, lsl #3]
    //     0xe49b00: blr             lr
    // 0xe49b04: ldur            x0, [fp, #-0x18]
    // 0xe49b08: LoadField: r2 = r0->field_13
    //     0xe49b08: ldur            w2, [x0, #0x13]
    // 0xe49b0c: DecompressPointer r2
    //     0xe49b0c: add             x2, x2, HEAP, lsl #32
    // 0xe49b10: stur            x2, [fp, #-0x20]
    // 0xe49b14: cmp             w2, NULL
    // 0xe49b18: b.eq            #0xe49e48
    // 0xe49b1c: LoadField: d0 = r2->field_7
    //     0xe49b1c: ldur            d0, [x2, #7]
    // 0xe49b20: stur            d0, [fp, #-0x38]
    // 0xe49b24: d1 = 1.000000
    //     0xe49b24: fmov            d1, #1.00000000
    // 0xe49b28: fcmp            d1, d0
    // 0xe49b2c: b.le            #0xe49b58
    // 0xe49b30: ldur            x1, [fp, #-0x10]
    // 0xe49b34: r0 = saveContext()
    //     0xe49b34: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe49b38: r0 = PdfGraphicState()
    //     0xe49b38: bl              #0xe473a8  ; AllocatePdfGraphicStateStub -> PdfGraphicState (size=0x1c)
    // 0xe49b3c: mov             x1, x0
    // 0xe49b40: ldur            x0, [fp, #-0x20]
    // 0xe49b44: StoreField: r1->field_7 = r0
    //     0xe49b44: stur            w0, [x1, #7]
    // 0xe49b48: StoreField: r1->field_b = r0
    //     0xe49b48: stur            w0, [x1, #0xb]
    // 0xe49b4c: mov             x2, x1
    // 0xe49b50: ldur            x1, [fp, #-0x10]
    // 0xe49b54: r0 = setGraphicState()
    //     0xe49b54: bl              #0xe47304  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setGraphicState
    // 0xe49b58: ldur            x2, [fp, #-8]
    // 0xe49b5c: ldur            x0, [fp, #-0x18]
    // 0xe49b60: ldur            d0, [fp, #-0x38]
    // 0xe49b64: LoadField: r3 = r2->field_33
    //     0xe49b64: ldur            w3, [x2, #0x33]
    // 0xe49b68: DecompressPointer r3
    //     0xe49b68: add             x3, x3, HEAP, lsl #32
    // 0xe49b6c: stur            x3, [fp, #-0x20]
    // 0xe49b70: LoadField: r1 = r0->field_37
    //     0xe49b70: ldur            w1, [x0, #0x37]
    // 0xe49b74: DecompressPointer r1
    //     0xe49b74: add             x1, x1, HEAP, lsl #32
    // 0xe49b78: cmp             w1, NULL
    // 0xe49b7c: b.eq            #0xe49e4c
    // 0xe49b80: r0 = sizeValue()
    //     0xe49b80: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe49b84: ldur            x0, [fp, #-8]
    // 0xe49b88: LoadField: r3 = r0->field_2f
    //     0xe49b88: ldur            w3, [x0, #0x2f]
    // 0xe49b8c: DecompressPointer r3
    //     0xe49b8c: add             x3, x3, HEAP, lsl #32
    // 0xe49b90: ldur            x1, [fp, #-0x10]
    // 0xe49b94: ldur            x2, [fp, #-0x20]
    // 0xe49b98: d1 = 0.000000
    //     0xe49b98: eor             v1.16b, v1.16b, v1.16b
    // 0xe49b9c: d2 = 0.000000
    //     0xe49b9c: eor             v2.16b, v2.16b, v2.16b
    // 0xe49ba0: r4 = const [0, 0x6, 0, 0x6, null]
    //     0xe49ba0: ldr             x4, [PP, #0x7508]  ; [pp+0x7508] List(5) [0, 0x6, 0, 0x6, Null]
    // 0xe49ba4: r0 = drawString()
    //     0xe49ba4: bl              #0xe49e60  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawString
    // 0xe49ba8: ldur            d0, [fp, #-0x38]
    // 0xe49bac: d1 = 1.000000
    //     0xe49bac: fmov            d1, #1.00000000
    // 0xe49bb0: fcmp            d1, d0
    // 0xe49bb4: b.le            #0xe49bc0
    // 0xe49bb8: ldur            x1, [fp, #-0x10]
    // 0xe49bbc: r0 = restoreContext()
    //     0xe49bbc: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe49bc0: ldur            x0, [fp, #-0x18]
    // 0xe49bc4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xe49bc4: ldur            w2, [x0, #0x17]
    // 0xe49bc8: DecompressPointer r2
    //     0xe49bc8: add             x2, x2, HEAP, lsl #32
    // 0xe49bcc: mov             x1, x2
    // 0xe49bd0: stur            x2, [fp, #-0x20]
    // 0xe49bd4: r0 = isNotEmpty()
    //     0xe49bd4: bl              #0xe4998c  ; [package:pdf/src/svg/color.dart] SvgColor::isNotEmpty
    // 0xe49bd8: tbnz            w0, #4, #0xe49cec
    // 0xe49bdc: ldur            x0, [fp, #-0x18]
    // 0xe49be0: LoadField: r1 = r0->field_1f
    //     0xe49be0: ldur            w1, [x0, #0x1f]
    // 0xe49be4: DecompressPointer r1
    //     0xe49be4: add             x1, x1, HEAP, lsl #32
    // 0xe49be8: cmp             w1, NULL
    // 0xe49bec: b.eq            #0xe49bfc
    // 0xe49bf0: r0 = sizeValue()
    //     0xe49bf0: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe49bf4: ldur            x1, [fp, #-0x10]
    // 0xe49bf8: r0 = setLineWidth()
    //     0xe49bf8: bl              #0xe49358  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setLineWidth
    // 0xe49bfc: ldur            x0, [fp, #-0x18]
    // 0xe49c00: LoadField: r2 = r0->field_23
    //     0xe49c00: ldur            w2, [x0, #0x23]
    // 0xe49c04: DecompressPointer r2
    //     0xe49c04: add             x2, x2, HEAP, lsl #32
    // 0xe49c08: cmp             w2, NULL
    // 0xe49c0c: b.eq            #0xe49c1c
    // 0xe49c10: ldur            x1, [fp, #-0x10]
    // 0xe49c14: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe49c14: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe49c18: r0 = setLineDashPattern()
    //     0xe49c18: bl              #0xe49410  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setLineDashPattern
    // 0xe49c1c: ldur            x0, [fp, #-0x18]
    // 0xe49c20: d0 = 1.000000
    //     0xe49c20: fmov            d0, #1.00000000
    // 0xe49c24: LoadField: r1 = r0->field_1b
    //     0xe49c24: ldur            w1, [x0, #0x1b]
    // 0xe49c28: DecompressPointer r1
    //     0xe49c28: add             x1, x1, HEAP, lsl #32
    // 0xe49c2c: stur            x1, [fp, #-0x28]
    // 0xe49c30: cmp             w1, NULL
    // 0xe49c34: b.eq            #0xe49e50
    // 0xe49c38: LoadField: d1 = r1->field_7
    //     0xe49c38: ldur            d1, [x1, #7]
    // 0xe49c3c: fcmp            d0, d1
    // 0xe49c40: b.le            #0xe49c64
    // 0xe49c44: r0 = PdfGraphicState()
    //     0xe49c44: bl              #0xe473a8  ; AllocatePdfGraphicStateStub -> PdfGraphicState (size=0x1c)
    // 0xe49c48: mov             x1, x0
    // 0xe49c4c: ldur            x0, [fp, #-0x28]
    // 0xe49c50: StoreField: r1->field_7 = r0
    //     0xe49c50: stur            w0, [x1, #7]
    // 0xe49c54: StoreField: r1->field_b = r0
    //     0xe49c54: stur            w0, [x1, #0xb]
    // 0xe49c58: mov             x2, x1
    // 0xe49c5c: ldur            x1, [fp, #-0x10]
    // 0xe49c60: r0 = setGraphicState()
    //     0xe49c60: bl              #0xe47304  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setGraphicState
    // 0xe49c64: ldur            x5, [fp, #-8]
    // 0xe49c68: ldur            x4, [fp, #-0x18]
    // 0xe49c6c: ldur            x1, [fp, #-0x20]
    // 0xe49c70: r0 = LoadClassIdInstr(r1)
    //     0xe49c70: ldur            x0, [x1, #-1]
    //     0xe49c74: ubfx            x0, x0, #0xc, #0x14
    // 0xe49c78: mov             x2, x5
    // 0xe49c7c: ldur            x3, [fp, #-0x10]
    // 0xe49c80: r0 = GDT[cid_x0 + -0xfeb]()
    //     0xe49c80: sub             lr, x0, #0xfeb
    //     0xe49c84: ldr             lr, [x21, lr, lsl #3]
    //     0xe49c88: blr             lr
    // 0xe49c8c: ldur            x0, [fp, #-8]
    // 0xe49c90: LoadField: r2 = r0->field_33
    //     0xe49c90: ldur            w2, [x0, #0x33]
    // 0xe49c94: DecompressPointer r2
    //     0xe49c94: add             x2, x2, HEAP, lsl #32
    // 0xe49c98: ldur            x1, [fp, #-0x18]
    // 0xe49c9c: stur            x2, [fp, #-0x20]
    // 0xe49ca0: LoadField: r3 = r1->field_37
    //     0xe49ca0: ldur            w3, [x1, #0x37]
    // 0xe49ca4: DecompressPointer r3
    //     0xe49ca4: add             x3, x3, HEAP, lsl #32
    // 0xe49ca8: cmp             w3, NULL
    // 0xe49cac: b.eq            #0xe49e54
    // 0xe49cb0: mov             x1, x3
    // 0xe49cb4: r0 = sizeValue()
    //     0xe49cb4: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe49cb8: ldur            x0, [fp, #-8]
    // 0xe49cbc: LoadField: r3 = r0->field_2f
    //     0xe49cbc: ldur            w3, [x0, #0x2f]
    // 0xe49cc0: DecompressPointer r3
    //     0xe49cc0: add             x3, x3, HEAP, lsl #32
    // 0xe49cc4: r16 = Instance_PdfTextRenderingMode
    //     0xe49cc4: add             x16, PP, #0x46, lsl #12  ; [pp+0x46da8] Obj!PdfTextRenderingMode@e2ed41
    //     0xe49cc8: ldr             x16, [x16, #0xda8]
    // 0xe49ccc: str             x16, [SP]
    // 0xe49cd0: ldur            x1, [fp, #-0x10]
    // 0xe49cd4: ldur            x2, [fp, #-0x20]
    // 0xe49cd8: d1 = 0.000000
    //     0xe49cd8: eor             v1.16b, v1.16b, v1.16b
    // 0xe49cdc: d2 = 0.000000
    //     0xe49cdc: eor             v2.16b, v2.16b, v2.16b
    // 0xe49ce0: r4 = const [0, 0x7, 0x1, 0x6, mode, 0x6, null]
    //     0xe49ce0: add             x4, PP, #0x46, lsl #12  ; [pp+0x46d40] List(7) [0, 0x7, 0x1, 0x6, "mode", 0x6, Null]
    //     0xe49ce4: ldr             x4, [x4, #0xd40]
    // 0xe49ce8: r0 = drawString()
    //     0xe49ce8: bl              #0xe49e60  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawString
    // 0xe49cec: ldur            x0, [fp, #-8]
    // 0xe49cf0: ldur            x1, [fp, #-0x10]
    // 0xe49cf4: r0 = restoreContext()
    //     0xe49cf4: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe49cf8: ldur            x0, [fp, #-8]
    // 0xe49cfc: LoadField: r1 = r0->field_3b
    //     0xe49cfc: ldur            w1, [x0, #0x3b]
    // 0xe49d00: DecompressPointer r1
    //     0xe49d00: add             x1, x1, HEAP, lsl #32
    // 0xe49d04: r0 = iterator()
    //     0xe49d04: bl              #0x887d48  ; [dart:_internal] MappedIterable::iterator
    // 0xe49d08: mov             x2, x0
    // 0xe49d0c: stur            x2, [fp, #-0x28]
    // 0xe49d10: LoadField: r3 = r2->field_f
    //     0xe49d10: ldur            w3, [x2, #0xf]
    // 0xe49d14: DecompressPointer r3
    //     0xe49d14: add             x3, x3, HEAP, lsl #32
    // 0xe49d18: stur            x3, [fp, #-0x20]
    // 0xe49d1c: LoadField: r4 = r2->field_13
    //     0xe49d1c: ldur            w4, [x2, #0x13]
    // 0xe49d20: DecompressPointer r4
    //     0xe49d20: add             x4, x4, HEAP, lsl #32
    // 0xe49d24: stur            x4, [fp, #-0x18]
    // 0xe49d28: LoadField: r5 = r2->field_7
    //     0xe49d28: ldur            w5, [x2, #7]
    // 0xe49d2c: DecompressPointer r5
    //     0xe49d2c: add             x5, x5, HEAP, lsl #32
    // 0xe49d30: stur            x5, [fp, #-8]
    // 0xe49d34: CheckStackOverflow
    //     0xe49d34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49d38: cmp             SP, x16
    //     0xe49d3c: b.ls            #0xe49e58
    // 0xe49d40: r0 = LoadClassIdInstr(r3)
    //     0xe49d40: ldur            x0, [x3, #-1]
    //     0xe49d44: ubfx            x0, x0, #0xc, #0x14
    // 0xe49d48: mov             x1, x3
    // 0xe49d4c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe49d4c: movz            x17, #0x292d
    //     0xe49d50: movk            x17, #0x1, lsl #16
    //     0xe49d54: add             lr, x0, x17
    //     0xe49d58: ldr             lr, [x21, lr, lsl #3]
    //     0xe49d5c: blr             lr
    // 0xe49d60: tbnz            w0, #4, #0xe49e28
    // 0xe49d64: ldur            x2, [fp, #-0x28]
    // 0xe49d68: ldur            x3, [fp, #-0x20]
    // 0xe49d6c: r0 = LoadClassIdInstr(r3)
    //     0xe49d6c: ldur            x0, [x3, #-1]
    //     0xe49d70: ubfx            x0, x0, #0xc, #0x14
    // 0xe49d74: mov             x1, x3
    // 0xe49d78: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe49d78: movz            x17, #0x384d
    //     0xe49d7c: movk            x17, #0x1, lsl #16
    //     0xe49d80: add             lr, x0, x17
    //     0xe49d84: ldr             lr, [x21, lr, lsl #3]
    //     0xe49d88: blr             lr
    // 0xe49d8c: ldur            x16, [fp, #-0x18]
    // 0xe49d90: stp             x0, x16, [SP]
    // 0xe49d94: ldur            x0, [fp, #-0x18]
    // 0xe49d98: ClosureCall
    //     0xe49d98: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe49d9c: ldur            x2, [x0, #0x1f]
    //     0xe49da0: blr             x2
    // 0xe49da4: mov             x4, x0
    // 0xe49da8: ldur            x3, [fp, #-0x28]
    // 0xe49dac: stur            x4, [fp, #-0x30]
    // 0xe49db0: StoreField: r3->field_b = r0
    //     0xe49db0: stur            w0, [x3, #0xb]
    //     0xe49db4: tbz             w0, #0, #0xe49dd0
    //     0xe49db8: ldurb           w16, [x3, #-1]
    //     0xe49dbc: ldurb           w17, [x0, #-1]
    //     0xe49dc0: and             x16, x17, x16, lsr #2
    //     0xe49dc4: tst             x16, HEAP, lsr #32
    //     0xe49dc8: b.eq            #0xe49dd0
    //     0xe49dcc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe49dd0: cmp             w4, NULL
    // 0xe49dd4: b.ne            #0xe49e08
    // 0xe49dd8: mov             x0, x4
    // 0xe49ddc: ldur            x2, [fp, #-8]
    // 0xe49de0: r1 = Null
    //     0xe49de0: mov             x1, NULL
    // 0xe49de4: cmp             w2, NULL
    // 0xe49de8: b.eq            #0xe49e08
    // 0xe49dec: LoadField: r4 = r2->field_1b
    //     0xe49dec: ldur            w4, [x2, #0x1b]
    // 0xe49df0: DecompressPointer r4
    //     0xe49df0: add             x4, x4, HEAP, lsl #32
    // 0xe49df4: r8 = X1
    //     0xe49df4: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xe49df8: LoadField: r9 = r4->field_7
    //     0xe49df8: ldur            x9, [x4, #7]
    // 0xe49dfc: r3 = Null
    //     0xe49dfc: add             x3, PP, #0x46, lsl #12  ; [pp+0x46db0] Null
    //     0xe49e00: ldr             x3, [x3, #0xdb0]
    // 0xe49e04: blr             x9
    // 0xe49e08: ldur            x1, [fp, #-0x30]
    // 0xe49e0c: ldur            x2, [fp, #-0x10]
    // 0xe49e10: r0 = paint()
    //     0xe49e10: bl              #0xe46704  ; [package:pdf/src/svg/operation.dart] SvgOperation::paint
    // 0xe49e14: ldur            x2, [fp, #-0x28]
    // 0xe49e18: ldur            x5, [fp, #-8]
    // 0xe49e1c: ldur            x3, [fp, #-0x20]
    // 0xe49e20: ldur            x4, [fp, #-0x18]
    // 0xe49e24: b               #0xe49d34
    // 0xe49e28: ldur            x1, [fp, #-0x28]
    // 0xe49e2c: StoreField: r1->field_b = rNULL
    //     0xe49e2c: stur            NULL, [x1, #0xb]
    // 0xe49e30: r0 = Null
    //     0xe49e30: mov             x0, NULL
    // 0xe49e34: LeaveFrame
    //     0xe49e34: mov             SP, fp
    //     0xe49e38: ldp             fp, lr, [SP], #0x10
    // 0xe49e3c: ret
    //     0xe49e3c: ret             
    // 0xe49e40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49e40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49e44: b               #0xe49a0c
    // 0xe49e48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe49e48: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe49e4c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe49e4c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe49e50: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe49e50: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe49e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe49e54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe49e58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49e58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49e5c: b               #0xe49d40
  }
  _ drawShape(/* No info */) {
    // ** addr: 0xe540f0, size: 0x294
    // 0xe540f0: EnterFrame
    //     0xe540f0: stp             fp, lr, [SP, #-0x10]!
    //     0xe540f4: mov             fp, SP
    // 0xe540f8: AllocStack(0x40)
    //     0xe540f8: sub             SP, SP, #0x40
    // 0xe540fc: SetupParameters(SvgText this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe540fc: mov             x0, x2
    //     0xe54100: stur            x2, [fp, #-0x10]
    //     0xe54104: mov             x2, x1
    //     0xe54108: stur            x1, [fp, #-8]
    // 0xe5410c: CheckStackOverflow
    //     0xe5410c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe54110: cmp             SP, x16
    //     0xe54114: b.ls            #0xe54370
    // 0xe54118: mov             x1, x0
    // 0xe5411c: r0 = saveContext()
    //     0xe5411c: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe54120: r0 = Matrix4()
    //     0xe54120: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe54124: r4 = 32
    //     0xe54124: movz            x4, #0x20
    // 0xe54128: stur            x0, [fp, #-0x18]
    // 0xe5412c: r0 = AllocateFloat64Array()
    //     0xe5412c: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe54130: mov             x1, x0
    // 0xe54134: ldur            x0, [fp, #-0x18]
    // 0xe54138: StoreField: r0->field_7 = r1
    //     0xe54138: stur            w1, [x0, #7]
    // 0xe5413c: mov             x1, x0
    // 0xe54140: r0 = setIdentity()
    //     0xe54140: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe54144: r16 = -1.000000
    //     0xe54144: ldr             x16, [PP, #0x5bd8]  ; [pp+0x5bd8] -1
    // 0xe54148: str             x16, [SP]
    // 0xe5414c: ldur            x1, [fp, #-0x18]
    // 0xe54150: r2 = 1.000000
    //     0xe54150: ldr             x2, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xe54154: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe54154: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe54158: r0 = scale()
    //     0xe54158: bl              #0x645258  ; [package:vector_math/vector_math_64.dart] Matrix4::scale
    // 0xe5415c: ldur            x0, [fp, #-8]
    // 0xe54160: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe54160: ldur            d0, [x0, #0x17]
    // 0xe54164: LoadField: d1 = r0->field_1f
    //     0xe54164: ldur            d1, [x0, #0x1f]
    // 0xe54168: fneg            d2, d1
    // 0xe5416c: ldur            x1, [fp, #-0x18]
    // 0xe54170: mov             v1.16b, v2.16b
    // 0xe54174: r0 = translate()
    //     0xe54174: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe54178: ldur            x1, [fp, #-0x10]
    // 0xe5417c: ldur            x2, [fp, #-0x18]
    // 0xe54180: r0 = setTransform()
    //     0xe54180: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe54184: ldur            x0, [fp, #-8]
    // 0xe54188: LoadField: r2 = r0->field_33
    //     0xe54188: ldur            w2, [x0, #0x33]
    // 0xe5418c: DecompressPointer r2
    //     0xe5418c: add             x2, x2, HEAP, lsl #32
    // 0xe54190: stur            x2, [fp, #-0x18]
    // 0xe54194: LoadField: r1 = r0->field_7
    //     0xe54194: ldur            w1, [x0, #7]
    // 0xe54198: DecompressPointer r1
    //     0xe54198: add             x1, x1, HEAP, lsl #32
    // 0xe5419c: LoadField: r3 = r1->field_37
    //     0xe5419c: ldur            w3, [x1, #0x37]
    // 0xe541a0: DecompressPointer r3
    //     0xe541a0: add             x3, x3, HEAP, lsl #32
    // 0xe541a4: cmp             w3, NULL
    // 0xe541a8: b.eq            #0xe54378
    // 0xe541ac: mov             x1, x3
    // 0xe541b0: r0 = sizeValue()
    //     0xe541b0: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe541b4: ldur            x0, [fp, #-8]
    // 0xe541b8: LoadField: r3 = r0->field_2f
    //     0xe541b8: ldur            w3, [x0, #0x2f]
    // 0xe541bc: DecompressPointer r3
    //     0xe541bc: add             x3, x3, HEAP, lsl #32
    // 0xe541c0: r16 = Instance_PdfTextRenderingMode
    //     0xe541c0: add             x16, PP, #0x46, lsl #12  ; [pp+0x46d38] Obj!PdfTextRenderingMode@e2ed61
    //     0xe541c4: ldr             x16, [x16, #0xd38]
    // 0xe541c8: str             x16, [SP]
    // 0xe541cc: ldur            x1, [fp, #-0x10]
    // 0xe541d0: ldur            x2, [fp, #-0x18]
    // 0xe541d4: d1 = 0.000000
    //     0xe541d4: eor             v1.16b, v1.16b, v1.16b
    // 0xe541d8: d2 = 0.000000
    //     0xe541d8: eor             v2.16b, v2.16b, v2.16b
    // 0xe541dc: r4 = const [0, 0x7, 0x1, 0x6, mode, 0x6, null]
    //     0xe541dc: add             x4, PP, #0x46, lsl #12  ; [pp+0x46d40] List(7) [0, 0x7, 0x1, 0x6, "mode", 0x6, Null]
    //     0xe541e0: ldr             x4, [x4, #0xd40]
    // 0xe541e4: r0 = drawString()
    //     0xe541e4: bl              #0xe49e60  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawString
    // 0xe541e8: ldur            x1, [fp, #-0x10]
    // 0xe541ec: r0 = restoreContext()
    //     0xe541ec: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe541f0: ldur            x0, [fp, #-8]
    // 0xe541f4: LoadField: r1 = r0->field_3b
    //     0xe541f4: ldur            w1, [x0, #0x3b]
    // 0xe541f8: DecompressPointer r1
    //     0xe541f8: add             x1, x1, HEAP, lsl #32
    // 0xe541fc: r0 = iterator()
    //     0xe541fc: bl              #0x887d48  ; [dart:_internal] MappedIterable::iterator
    // 0xe54200: mov             x2, x0
    // 0xe54204: stur            x2, [fp, #-0x28]
    // 0xe54208: LoadField: r3 = r2->field_f
    //     0xe54208: ldur            w3, [x2, #0xf]
    // 0xe5420c: DecompressPointer r3
    //     0xe5420c: add             x3, x3, HEAP, lsl #32
    // 0xe54210: stur            x3, [fp, #-0x20]
    // 0xe54214: LoadField: r4 = r2->field_13
    //     0xe54214: ldur            w4, [x2, #0x13]
    // 0xe54218: DecompressPointer r4
    //     0xe54218: add             x4, x4, HEAP, lsl #32
    // 0xe5421c: stur            x4, [fp, #-0x18]
    // 0xe54220: LoadField: r5 = r2->field_7
    //     0xe54220: ldur            w5, [x2, #7]
    // 0xe54224: DecompressPointer r5
    //     0xe54224: add             x5, x5, HEAP, lsl #32
    // 0xe54228: stur            x5, [fp, #-8]
    // 0xe5422c: CheckStackOverflow
    //     0xe5422c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe54230: cmp             SP, x16
    //     0xe54234: b.ls            #0xe5437c
    // 0xe54238: r0 = LoadClassIdInstr(r3)
    //     0xe54238: ldur            x0, [x3, #-1]
    //     0xe5423c: ubfx            x0, x0, #0xc, #0x14
    // 0xe54240: mov             x1, x3
    // 0xe54244: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe54244: movz            x17, #0x292d
    //     0xe54248: movk            x17, #0x1, lsl #16
    //     0xe5424c: add             lr, x0, x17
    //     0xe54250: ldr             lr, [x21, lr, lsl #3]
    //     0xe54254: blr             lr
    // 0xe54258: tbnz            w0, #4, #0xe54358
    // 0xe5425c: ldur            x2, [fp, #-0x28]
    // 0xe54260: ldur            x3, [fp, #-0x20]
    // 0xe54264: r0 = LoadClassIdInstr(r3)
    //     0xe54264: ldur            x0, [x3, #-1]
    //     0xe54268: ubfx            x0, x0, #0xc, #0x14
    // 0xe5426c: mov             x1, x3
    // 0xe54270: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe54270: movz            x17, #0x384d
    //     0xe54274: movk            x17, #0x1, lsl #16
    //     0xe54278: add             lr, x0, x17
    //     0xe5427c: ldr             lr, [x21, lr, lsl #3]
    //     0xe54280: blr             lr
    // 0xe54284: ldur            x16, [fp, #-0x18]
    // 0xe54288: stp             x0, x16, [SP]
    // 0xe5428c: ldur            x0, [fp, #-0x18]
    // 0xe54290: ClosureCall
    //     0xe54290: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe54294: ldur            x2, [x0, #0x1f]
    //     0xe54298: blr             x2
    // 0xe5429c: mov             x4, x0
    // 0xe542a0: ldur            x3, [fp, #-0x28]
    // 0xe542a4: stur            x4, [fp, #-0x30]
    // 0xe542a8: StoreField: r3->field_b = r0
    //     0xe542a8: stur            w0, [x3, #0xb]
    //     0xe542ac: tbz             w0, #0, #0xe542c8
    //     0xe542b0: ldurb           w16, [x3, #-1]
    //     0xe542b4: ldurb           w17, [x0, #-1]
    //     0xe542b8: and             x16, x17, x16, lsr #2
    //     0xe542bc: tst             x16, HEAP, lsr #32
    //     0xe542c0: b.eq            #0xe542c8
    //     0xe542c4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe542c8: cmp             w4, NULL
    // 0xe542cc: b.ne            #0xe54300
    // 0xe542d0: mov             x0, x4
    // 0xe542d4: ldur            x2, [fp, #-8]
    // 0xe542d8: r1 = Null
    //     0xe542d8: mov             x1, NULL
    // 0xe542dc: cmp             w2, NULL
    // 0xe542e0: b.eq            #0xe54300
    // 0xe542e4: LoadField: r4 = r2->field_1b
    //     0xe542e4: ldur            w4, [x2, #0x1b]
    // 0xe542e8: DecompressPointer r4
    //     0xe542e8: add             x4, x4, HEAP, lsl #32
    // 0xe542ec: r8 = X1
    //     0xe542ec: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xe542f0: LoadField: r9 = r4->field_7
    //     0xe542f0: ldur            x9, [x4, #7]
    // 0xe542f4: r3 = Null
    //     0xe542f4: add             x3, PP, #0x46, lsl #12  ; [pp+0x46d48] Null
    //     0xe542f8: ldr             x3, [x3, #0xd48]
    // 0xe542fc: blr             x9
    // 0xe54300: ldur            x0, [fp, #-0x30]
    // 0xe54304: ldur            x1, [fp, #-0x10]
    // 0xe54308: r0 = saveContext()
    //     0xe54308: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe5430c: ldur            x0, [fp, #-0x30]
    // 0xe54310: LoadField: r1 = r0->field_f
    //     0xe54310: ldur            w1, [x0, #0xf]
    // 0xe54314: DecompressPointer r1
    //     0xe54314: add             x1, x1, HEAP, lsl #32
    // 0xe54318: LoadField: r2 = r1->field_7
    //     0xe54318: ldur            w2, [x1, #7]
    // 0xe5431c: DecompressPointer r2
    //     0xe5431c: add             x2, x2, HEAP, lsl #32
    // 0xe54320: cmp             w2, NULL
    // 0xe54324: b.eq            #0xe54330
    // 0xe54328: ldur            x1, [fp, #-0x10]
    // 0xe5432c: r0 = setTransform()
    //     0xe5432c: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe54330: ldur            x1, [fp, #-0x30]
    // 0xe54334: ldur            x2, [fp, #-0x10]
    // 0xe54338: r0 = drawShape()
    //     0xe54338: bl              #0xe540f0  ; [package:pdf/src/svg/text.dart] SvgText::drawShape
    // 0xe5433c: ldur            x1, [fp, #-0x10]
    // 0xe54340: r0 = restoreContext()
    //     0xe54340: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe54344: ldur            x2, [fp, #-0x28]
    // 0xe54348: ldur            x5, [fp, #-8]
    // 0xe5434c: ldur            x3, [fp, #-0x20]
    // 0xe54350: ldur            x4, [fp, #-0x18]
    // 0xe54354: b               #0xe5422c
    // 0xe54358: ldur            x1, [fp, #-0x28]
    // 0xe5435c: StoreField: r1->field_b = rNULL
    //     0xe5435c: stur            NULL, [x1, #0xb]
    // 0xe54360: r0 = Null
    //     0xe54360: mov             x0, NULL
    // 0xe54364: LeaveFrame
    //     0xe54364: mov             SP, fp
    //     0xe54368: ldp             fp, lr, [SP], #0x10
    // 0xe5436c: ret
    //     0xe5436c: ret             
    // 0xe54370: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe54370: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe54374: b               #0xe54118
    // 0xe54378: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe54378: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe5437c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5437c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe54380: b               #0xe54238
  }
  factory _ SvgText.fromXml(/* No info */) {
    // ** addr: 0xe6f8e0, size: 0x5c4
    // 0xe6f8e0: EnterFrame
    //     0xe6f8e0: stp             fp, lr, [SP, #-0x10]!
    //     0xe6f8e4: mov             fp, SP
    // 0xe6f8e8: AllocStack(0x88)
    //     0xe6f8e8: sub             SP, SP, #0x88
    // 0xe6f8ec: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */, dynamic _ /* r5 => r3, fp-0x20 */, [dynamic _ = Instance_PdfPoint /* r0, fp-0x8 */])
    //     0xe6f8ec: stur            x3, [fp, #-0x18]
    //     0xe6f8f0: mov             x16, x5
    //     0xe6f8f4: mov             x5, x3
    //     0xe6f8f8: mov             x3, x16
    //     0xe6f8fc: stur            x2, [fp, #-0x10]
    //     0xe6f900: stur            x3, [fp, #-0x20]
    //     0xe6f904: ldur            w0, [x4, #0x13]
    //     0xe6f908: sub             x1, x0, #8
    //     0xe6f90c: cmp             w1, #2
    //     0xe6f910: b.lt            #0xe6f920
    //     0xe6f914: add             x0, fp, w1, sxtw #2
    //     0xe6f918: ldr             x0, [x0, #8]
    //     0xe6f91c: b               #0xe6f928
    //     0xe6f920: add             x0, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xe6f924: ldr             x0, [x0, #0x730]
    //     0xe6f928: stur            x0, [fp, #-8]
    // 0xe6f92c: CheckStackOverflow
    //     0xe6f92c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6f930: cmp             SP, x16
    //     0xe6f934: b.ls            #0xe6fe64
    // 0xe6f938: r1 = 3
    //     0xe6f938: movz            x1, #0x3
    // 0xe6f93c: r0 = AllocateContext()
    //     0xe6f93c: bl              #0xec126c  ; AllocateContextStub
    // 0xe6f940: ldur            x5, [fp, #-0x18]
    // 0xe6f944: stur            x0, [fp, #-0x28]
    // 0xe6f948: StoreField: r0->field_f = r5
    //     0xe6f948: stur            w5, [x0, #0xf]
    // 0xe6f94c: ldur            x2, [fp, #-0x10]
    // 0xe6f950: ldur            x3, [fp, #-0x20]
    // 0xe6f954: r1 = Null
    //     0xe6f954: mov             x1, NULL
    // 0xe6f958: r0 = SvgBrush.fromXml()
    //     0xe6f958: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe6f95c: mov             x5, x0
    // 0xe6f960: ldur            x4, [fp, #-0x28]
    // 0xe6f964: stur            x5, [fp, #-0x18]
    // 0xe6f968: StoreField: r4->field_13 = r0
    //     0xe6f968: stur            w0, [x4, #0x13]
    //     0xe6f96c: ldurb           w16, [x4, #-1]
    //     0xe6f970: ldurb           w17, [x0, #-1]
    //     0xe6f974: and             x16, x17, x16, lsr #2
    //     0xe6f978: tst             x16, HEAP, lsr #32
    //     0xe6f97c: b.eq            #0xe6f984
    //     0xe6f980: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe6f984: r16 = 0.000000
    //     0xe6f984: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe6f988: str             x16, [SP]
    // 0xe6f98c: ldur            x1, [fp, #-0x10]
    // 0xe6f990: mov             x3, x5
    // 0xe6f994: r2 = "dx"
    //     0xe6f994: add             x2, PP, #0x25, lsl #12  ; [pp+0x25f40] "dx"
    //     0xe6f998: ldr             x2, [x2, #0xf40]
    // 0xe6f99c: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe6f99c: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe6f9a0: ldr             x4, [x4, #0xaf0]
    // 0xe6f9a4: r0 = getNumeric()
    //     0xe6f9a4: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe6f9a8: mov             x1, x0
    // 0xe6f9ac: r0 = sizeValue()
    //     0xe6f9ac: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe6f9b0: stur            d0, [fp, #-0x58]
    // 0xe6f9b4: r16 = 0.000000
    //     0xe6f9b4: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe6f9b8: str             x16, [SP]
    // 0xe6f9bc: ldur            x1, [fp, #-0x10]
    // 0xe6f9c0: ldur            x3, [fp, #-0x18]
    // 0xe6f9c4: r2 = "dy"
    //     0xe6f9c4: add             x2, PP, #0x25, lsl #12  ; [pp+0x25f48] "dy"
    //     0xe6f9c8: ldr             x2, [x2, #0xf48]
    // 0xe6f9cc: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe6f9cc: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe6f9d0: ldr             x4, [x4, #0xaf0]
    // 0xe6f9d4: r0 = getNumeric()
    //     0xe6f9d4: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe6f9d8: mov             x1, x0
    // 0xe6f9dc: r0 = sizeValue()
    //     0xe6f9dc: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe6f9e0: ldur            x1, [fp, #-0x10]
    // 0xe6f9e4: ldur            x3, [fp, #-0x18]
    // 0xe6f9e8: r2 = "x"
    //     0xe6f9e8: ldr             x2, [PP, #0x71a0]  ; [pp+0x71a0] "x"
    // 0xe6f9ec: stur            d0, [fp, #-0x60]
    // 0xe6f9f0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe6f9f0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe6f9f4: r0 = getNumeric()
    //     0xe6f9f4: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe6f9f8: cmp             w0, NULL
    // 0xe6f9fc: b.ne            #0xe6fa08
    // 0xe6fa00: r0 = Null
    //     0xe6fa00: mov             x0, NULL
    // 0xe6fa04: b               #0xe6fa38
    // 0xe6fa08: mov             x1, x0
    // 0xe6fa0c: r0 = sizeValue()
    //     0xe6fa0c: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe6fa10: r0 = inline_Allocate_Double()
    //     0xe6fa10: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6fa14: add             x0, x0, #0x10
    //     0xe6fa18: cmp             x1, x0
    //     0xe6fa1c: b.ls            #0xe6fe6c
    //     0xe6fa20: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6fa24: sub             x0, x0, #0xf
    //     0xe6fa28: movz            x1, #0xe15c
    //     0xe6fa2c: movk            x1, #0x3, lsl #16
    //     0xe6fa30: stur            x1, [x0, #-1]
    // 0xe6fa34: StoreField: r0->field_7 = d0
    //     0xe6fa34: stur            d0, [x0, #7]
    // 0xe6fa38: ldur            x1, [fp, #-0x10]
    // 0xe6fa3c: ldur            x3, [fp, #-0x18]
    // 0xe6fa40: stur            x0, [fp, #-0x20]
    // 0xe6fa44: r2 = "y"
    //     0xe6fa44: ldr             x2, [PP, #0x71a8]  ; [pp+0x71a8] "y"
    // 0xe6fa48: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe6fa48: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe6fa4c: r0 = getNumeric()
    //     0xe6fa4c: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe6fa50: cmp             w0, NULL
    // 0xe6fa54: b.ne            #0xe6fa60
    // 0xe6fa58: r4 = Null
    //     0xe6fa58: mov             x4, NULL
    // 0xe6fa5c: b               #0xe6fa94
    // 0xe6fa60: mov             x1, x0
    // 0xe6fa64: r0 = sizeValue()
    //     0xe6fa64: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe6fa68: r0 = inline_Allocate_Double()
    //     0xe6fa68: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6fa6c: add             x0, x0, #0x10
    //     0xe6fa70: cmp             x1, x0
    //     0xe6fa74: b.ls            #0xe6fe7c
    //     0xe6fa78: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6fa7c: sub             x0, x0, #0xf
    //     0xe6fa80: movz            x1, #0xe15c
    //     0xe6fa84: movk            x1, #0x3, lsl #16
    //     0xe6fa88: stur            x1, [x0, #-1]
    // 0xe6fa8c: StoreField: r0->field_7 = d0
    //     0xe6fa8c: stur            d0, [x0, #7]
    // 0xe6fa90: mov             x4, x0
    // 0xe6fa94: ldur            x3, [fp, #-0x10]
    // 0xe6fa98: ldur            x0, [fp, #-0x28]
    // 0xe6fa9c: ldur            x5, [fp, #-0x18]
    // 0xe6faa0: stur            x4, [fp, #-0x38]
    // 0xe6faa4: LoadField: r6 = r3->field_f
    //     0xe6faa4: ldur            w6, [x3, #0xf]
    // 0xe6faa8: DecompressPointer r6
    //     0xe6faa8: add             x6, x6, HEAP, lsl #32
    // 0xe6faac: stur            x6, [fp, #-0x30]
    // 0xe6fab0: r1 = Function '<anonymous closure>': static.
    //     0xe6fab0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb08] AnonymousClosure: static (0xe70fe4), in [package:pdf/src/svg/text.dart] SvgText::SvgText.fromXml (0xe6f8e0)
    //     0xe6fab4: ldr             x1, [x1, #0xb08]
    // 0xe6fab8: r2 = Null
    //     0xe6fab8: mov             x2, NULL
    // 0xe6fabc: r0 = AllocateClosure()
    //     0xe6fabc: bl              #0xec1630  ; AllocateClosureStub
    // 0xe6fac0: ldur            x1, [fp, #-0x30]
    // 0xe6fac4: mov             x2, x0
    // 0xe6fac8: r0 = where()
    //     0xe6fac8: bl              #0x8004f4  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::where
    // 0xe6facc: r1 = Function '<anonymous closure>': static.
    //     0xe6facc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb10] AnonymousClosure: static (0xe70fa4), in [package:pdf/src/svg/text.dart] SvgText::SvgText.fromXml (0xe6f8e0)
    //     0xe6fad0: ldr             x1, [x1, #0xb10]
    // 0xe6fad4: r2 = Null
    //     0xe6fad4: mov             x2, NULL
    // 0xe6fad8: stur            x0, [fp, #-0x40]
    // 0xe6fadc: r0 = AllocateClosure()
    //     0xe6fadc: bl              #0xec1630  ; AllocateClosureStub
    // 0xe6fae0: r16 = <String?>
    //     0xe6fae0: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xe6fae4: ldur            lr, [fp, #-0x40]
    // 0xe6fae8: stp             lr, x16, [SP, #8]
    // 0xe6faec: str             x0, [SP]
    // 0xe6faf0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe6faf0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe6faf4: r0 = map()
    //     0xe6faf4: bl              #0x7abfa0  ; [dart:_internal] WhereIterable::map
    // 0xe6faf8: mov             x1, x0
    // 0xe6fafc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe6fafc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe6fb00: r0 = join()
    //     0xe6fb00: bl              #0x7ae598  ; [dart:core] Iterable::join
    // 0xe6fb04: mov             x1, x0
    // 0xe6fb08: r0 = trim()
    //     0xe6fb08: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0xe6fb0c: mov             x4, x0
    // 0xe6fb10: ldur            x0, [fp, #-0x28]
    // 0xe6fb14: stur            x4, [fp, #-0x40]
    // 0xe6fb18: LoadField: r1 = r0->field_f
    //     0xe6fb18: ldur            w1, [x0, #0xf]
    // 0xe6fb1c: DecompressPointer r1
    //     0xe6fb1c: add             x1, x1, HEAP, lsl #32
    // 0xe6fb20: ldur            x6, [fp, #-0x18]
    // 0xe6fb24: LoadField: r2 = r6->field_3b
    //     0xe6fb24: ldur            w2, [x6, #0x3b]
    // 0xe6fb28: DecompressPointer r2
    //     0xe6fb28: add             x2, x2, HEAP, lsl #32
    // 0xe6fb2c: cmp             w2, NULL
    // 0xe6fb30: b.eq            #0xe6fe8c
    // 0xe6fb34: LoadField: r3 = r6->field_3f
    //     0xe6fb34: ldur            w3, [x6, #0x3f]
    // 0xe6fb38: DecompressPointer r3
    //     0xe6fb38: add             x3, x3, HEAP, lsl #32
    // 0xe6fb3c: cmp             w3, NULL
    // 0xe6fb40: b.eq            #0xe6fe90
    // 0xe6fb44: LoadField: r5 = r6->field_43
    //     0xe6fb44: ldur            w5, [x6, #0x43]
    // 0xe6fb48: DecompressPointer r5
    //     0xe6fb48: add             x5, x5, HEAP, lsl #32
    // 0xe6fb4c: cmp             w5, NULL
    // 0xe6fb50: b.eq            #0xe6fe94
    // 0xe6fb54: r0 = getFontCache()
    //     0xe6fb54: bl              #0xe709ec  ; [package:pdf/src/svg/painter.dart] SvgPainter::getFontCache
    // 0xe6fb58: stur            x0, [fp, #-0x48]
    // 0xe6fb5c: cmp             w0, NULL
    // 0xe6fb60: b.eq            #0xe6fe98
    // 0xe6fb64: ldur            x3, [fp, #-0x28]
    // 0xe6fb68: LoadField: r1 = r3->field_f
    //     0xe6fb68: ldur            w1, [x3, #0xf]
    // 0xe6fb6c: DecompressPointer r1
    //     0xe6fb6c: add             x1, x1, HEAP, lsl #32
    // 0xe6fb70: LoadField: r2 = r1->field_f
    //     0xe6fb70: ldur            w2, [x1, #0xf]
    // 0xe6fb74: DecompressPointer r2
    //     0xe6fb74: add             x2, x2, HEAP, lsl #32
    // 0xe6fb78: r1 = Null
    //     0xe6fb78: mov             x1, NULL
    // 0xe6fb7c: r0 = Context()
    //     0xe6fb7c: bl              #0xe7097c  ; [package:pdf/src/widgets/widget.dart] Context::Context
    // 0xe6fb80: ldur            x1, [fp, #-0x48]
    // 0xe6fb84: mov             x2, x0
    // 0xe6fb88: r0 = getFont()
    //     0xe6fb88: bl              #0xe65b4c  ; [package:pdf/src/widgets/font.dart] Font::getFont
    // 0xe6fb8c: stur            x0, [fp, #-0x48]
    // 0xe6fb90: r1 = LoadClassIdInstr(r0)
    //     0xe6fb90: ldur            x1, [x0, #-1]
    //     0xe6fb94: ubfx            x1, x1, #0xc, #0x14
    // 0xe6fb98: cmp             x1, #0x37f
    // 0xe6fb9c: b.ne            #0xe6fc04
    // 0xe6fba0: ldur            x3, [fp, #-0x40]
    // 0xe6fba4: LoadField: r1 = r3->field_7
    //     0xe6fba4: ldur            w1, [x3, #7]
    // 0xe6fba8: cbnz            w1, #0xe6fbb8
    // 0xe6fbac: r2 = Instance_PdfFontMetrics
    //     0xe6fbac: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e120] Obj!PdfFontMetrics@e0c991
    //     0xe6fbb0: ldr             x2, [x2, #0x120]
    // 0xe6fbb4: b               #0xe6fc2c
    // 0xe6fbb8: mov             x2, x3
    // 0xe6fbbc: r1 = Instance_Latin1Codec
    //     0xe6fbbc: ldr             x1, [PP, #0xdf8]  ; [pp+0xdf8] Obj!Latin1Codec@e2cd01
    // 0xe6fbc0: r0 = encode()
    //     0xe6fbc0: bl              #0xceba10  ; [dart:convert] Latin1Codec::encode
    // 0xe6fbc4: ldur            x2, [fp, #-0x48]
    // 0xe6fbc8: r1 = Function 'glyphMetrics':.
    //     0xe6fbc8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e108] AnonymousClosure: (0xe71018), in [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::glyphMetrics (0xe71054)
    //     0xe6fbcc: ldr             x1, [x1, #0x108]
    // 0xe6fbd0: stur            x0, [fp, #-0x50]
    // 0xe6fbd4: r0 = AllocateClosure()
    //     0xe6fbd4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe6fbd8: ldur            x2, [fp, #-0x50]
    // 0xe6fbdc: mov             x3, x0
    // 0xe6fbe0: r1 = <PdfFontMetrics, int, PdfFontMetrics>
    //     0xe6fbe0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e110] TypeArguments: <PdfFontMetrics, int, PdfFontMetrics>
    //     0xe6fbe4: ldr             x1, [x1, #0x110]
    // 0xe6fbe8: r0 = MappedIterable()
    //     0xe6fbe8: bl              #0x7ac0ac  ; [dart:_internal] MappedIterable::MappedIterable
    // 0xe6fbec: mov             x2, x0
    // 0xe6fbf0: r1 = Null
    //     0xe6fbf0: mov             x1, NULL
    // 0xe6fbf4: d0 = 0.000000
    //     0xe6fbf4: eor             v0.16b, v0.16b, v0.16b
    // 0xe6fbf8: r0 = PdfFontMetrics.append()
    //     0xe6fbf8: bl              #0xe701bc  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::PdfFontMetrics.append
    // 0xe6fbfc: mov             x2, x0
    // 0xe6fc00: b               #0xe6fc2c
    // 0xe6fc04: mov             x3, x0
    // 0xe6fc08: r0 = LoadClassIdInstr(r3)
    //     0xe6fc08: ldur            x0, [x3, #-1]
    //     0xe6fc0c: ubfx            x0, x0, #0xc, #0x14
    // 0xe6fc10: mov             x1, x3
    // 0xe6fc14: ldur            x2, [fp, #-0x40]
    // 0xe6fc18: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6fc18: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6fc1c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe6fc1c: sub             lr, x0, #1, lsl #12
    //     0xe6fc20: ldr             lr, [x21, lr, lsl #3]
    //     0xe6fc24: blr             lr
    // 0xe6fc28: mov             x2, x0
    // 0xe6fc2c: ldur            x5, [fp, #-0x18]
    // 0xe6fc30: ldur            x0, [fp, #-0x20]
    // 0xe6fc34: stur            x2, [fp, #-0x50]
    // 0xe6fc38: LoadField: r1 = r5->field_37
    //     0xe6fc38: ldur            w1, [x5, #0x37]
    // 0xe6fc3c: DecompressPointer r1
    //     0xe6fc3c: add             x1, x1, HEAP, lsl #32
    // 0xe6fc40: cmp             w1, NULL
    // 0xe6fc44: b.eq            #0xe6fe9c
    // 0xe6fc48: r0 = sizeValue()
    //     0xe6fc48: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe6fc4c: ldur            x1, [fp, #-0x50]
    // 0xe6fc50: r0 = *()
    //     0xe6fc50: bl              #0xe6feb0  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::*
    // 0xe6fc54: mov             x1, x0
    // 0xe6fc58: ldur            x0, [fp, #-0x20]
    // 0xe6fc5c: stur            x1, [fp, #-0x50]
    // 0xe6fc60: cmp             w0, NULL
    // 0xe6fc64: b.ne            #0xe6fc78
    // 0xe6fc68: ldur            x2, [fp, #-8]
    // 0xe6fc6c: LoadField: d0 = r2->field_7
    //     0xe6fc6c: ldur            d0, [x2, #7]
    // 0xe6fc70: mov             v1.16b, v0.16b
    // 0xe6fc74: b               #0xe6fc84
    // 0xe6fc78: ldur            x2, [fp, #-8]
    // 0xe6fc7c: LoadField: d0 = r0->field_7
    //     0xe6fc7c: ldur            d0, [x0, #7]
    // 0xe6fc80: mov             v1.16b, v0.16b
    // 0xe6fc84: ldur            d0, [fp, #-0x58]
    // 0xe6fc88: ldur            x0, [fp, #-0x38]
    // 0xe6fc8c: fadd            d2, d1, d0
    // 0xe6fc90: cmp             w0, NULL
    // 0xe6fc94: b.ne            #0xe6fca4
    // 0xe6fc98: LoadField: d0 = r2->field_f
    //     0xe6fc98: ldur            d0, [x2, #0xf]
    // 0xe6fc9c: mov             v1.16b, v0.16b
    // 0xe6fca0: b               #0xe6fcac
    // 0xe6fca4: LoadField: d0 = r0->field_7
    //     0xe6fca4: ldur            d0, [x0, #7]
    // 0xe6fca8: mov             v1.16b, v0.16b
    // 0xe6fcac: ldur            x5, [fp, #-0x18]
    // 0xe6fcb0: ldur            d0, [fp, #-0x60]
    // 0xe6fcb4: fadd            d3, d1, d0
    // 0xe6fcb8: stur            d3, [fp, #-0x70]
    // 0xe6fcbc: LoadField: r0 = r5->field_47
    //     0xe6fcbc: ldur            w0, [x5, #0x47]
    // 0xe6fcc0: DecompressPointer r0
    //     0xe6fcc0: add             x0, x0, HEAP, lsl #32
    // 0xe6fcc4: cmp             w0, NULL
    // 0xe6fcc8: b.eq            #0xe6fea0
    // 0xe6fccc: LoadField: r2 = r0->field_7
    //     0xe6fccc: ldur            x2, [x0, #7]
    // 0xe6fcd0: cmp             x2, #1
    // 0xe6fcd4: b.gt            #0xe6fd04
    // 0xe6fcd8: cmp             x2, #0
    // 0xe6fcdc: b.gt            #0xe6fce8
    // 0xe6fce0: mov             v0.16b, v2.16b
    // 0xe6fce4: b               #0xe6fd14
    // 0xe6fce8: d0 = 2.000000
    //     0xe6fce8: fmov            d0, #2.00000000
    // 0xe6fcec: LoadField: d1 = r1->field_1f
    //     0xe6fcec: ldur            d1, [x1, #0x1f]
    // 0xe6fcf0: LoadField: d4 = r1->field_7
    //     0xe6fcf0: ldur            d4, [x1, #7]
    // 0xe6fcf4: fsub            d5, d1, d4
    // 0xe6fcf8: fdiv            d1, d5, d0
    // 0xe6fcfc: fsub            d0, d2, d1
    // 0xe6fd00: b               #0xe6fd14
    // 0xe6fd04: LoadField: d0 = r1->field_1f
    //     0xe6fd04: ldur            d0, [x1, #0x1f]
    // 0xe6fd08: LoadField: d1 = r1->field_7
    //     0xe6fd08: ldur            d1, [x1, #7]
    // 0xe6fd0c: fsub            d4, d0, d1
    // 0xe6fd10: fsub            d0, d2, d4
    // 0xe6fd14: ldur            x3, [fp, #-0x28]
    // 0xe6fd18: ldur            x2, [fp, #-0x40]
    // 0xe6fd1c: ldur            x0, [fp, #-0x48]
    // 0xe6fd20: stur            d0, [fp, #-0x68]
    // 0xe6fd24: LoadField: d1 = r1->field_37
    //     0xe6fd24: ldur            d1, [x1, #0x37]
    // 0xe6fd28: stur            d1, [fp, #-0x60]
    // 0xe6fd2c: fadd            d2, d0, d1
    // 0xe6fd30: stur            d2, [fp, #-0x58]
    // 0xe6fd34: r0 = PdfPoint()
    //     0xe6fd34: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe6fd38: ldur            d0, [fp, #-0x58]
    // 0xe6fd3c: StoreField: r0->field_7 = d0
    //     0xe6fd3c: stur            d0, [x0, #7]
    // 0xe6fd40: ldur            d0, [fp, #-0x70]
    // 0xe6fd44: StoreField: r0->field_f = d0
    //     0xe6fd44: stur            d0, [x0, #0xf]
    // 0xe6fd48: ldur            x2, [fp, #-0x28]
    // 0xe6fd4c: ArrayStore: r2[0] = r0  ; List_4
    //     0xe6fd4c: stur            w0, [x2, #0x17]
    //     0xe6fd50: ldurb           w16, [x2, #-1]
    //     0xe6fd54: ldurb           w17, [x0, #-1]
    //     0xe6fd58: and             x16, x17, x16, lsr #2
    //     0xe6fd5c: tst             x16, HEAP, lsr #32
    //     0xe6fd60: b.eq            #0xe6fd68
    //     0xe6fd64: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe6fd68: r16 = <XmlElement>
    //     0xe6fd68: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ea98] TypeArguments: <XmlElement>
    //     0xe6fd6c: ldr             x16, [x16, #0xa98]
    // 0xe6fd70: ldur            lr, [fp, #-0x30]
    // 0xe6fd74: stp             lr, x16, [SP]
    // 0xe6fd78: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe6fd78: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe6fd7c: r0 = whereType()
    //     0xe6fd7c: bl              #0x7b5364  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::whereType
    // 0xe6fd80: ldur            x2, [fp, #-0x28]
    // 0xe6fd84: r1 = Function '<anonymous closure>': static.
    //     0xe6fd84: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb18] AnonymousClosure: static (0xe70ee8), in [package:pdf/src/svg/text.dart] SvgText::SvgText.fromXml (0xe6f8e0)
    //     0xe6fd88: ldr             x1, [x1, #0xb18]
    // 0xe6fd8c: stur            x0, [fp, #-8]
    // 0xe6fd90: r0 = AllocateClosure()
    //     0xe6fd90: bl              #0xec1630  ; AllocateClosureStub
    // 0xe6fd94: r16 = <SvgText>
    //     0xe6fd94: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eb20] TypeArguments: <SvgText>
    //     0xe6fd98: ldr             x16, [x16, #0xb20]
    // 0xe6fd9c: ldur            lr, [fp, #-8]
    // 0xe6fda0: stp             lr, x16, [SP, #8]
    // 0xe6fda4: str             x0, [SP]
    // 0xe6fda8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe6fda8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe6fdac: r0 = map()
    //     0xe6fdac: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xe6fdb0: mov             x4, x0
    // 0xe6fdb4: ldur            x0, [fp, #-0x28]
    // 0xe6fdb8: stur            x4, [fp, #-8]
    // 0xe6fdbc: LoadField: r3 = r0->field_f
    //     0xe6fdbc: ldur            w3, [x0, #0xf]
    // 0xe6fdc0: DecompressPointer r3
    //     0xe6fdc0: add             x3, x3, HEAP, lsl #32
    // 0xe6fdc4: ldur            x2, [fp, #-0x10]
    // 0xe6fdc8: ldur            x5, [fp, #-0x18]
    // 0xe6fdcc: r1 = Null
    //     0xe6fdcc: mov             x1, NULL
    // 0xe6fdd0: r0 = SvgClipPath.fromXml()
    //     0xe6fdd0: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe6fdd4: ldur            x2, [fp, #-0x10]
    // 0xe6fdd8: r1 = Null
    //     0xe6fdd8: mov             x1, NULL
    // 0xe6fddc: stur            x0, [fp, #-0x10]
    // 0xe6fde0: r0 = SvgTransform.fromXml()
    //     0xe6fde0: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe6fde4: mov             x1, x0
    // 0xe6fde8: ldur            x0, [fp, #-0x28]
    // 0xe6fdec: stur            x1, [fp, #-0x30]
    // 0xe6fdf0: LoadField: r2 = r0->field_f
    //     0xe6fdf0: ldur            w2, [x0, #0xf]
    // 0xe6fdf4: DecompressPointer r2
    //     0xe6fdf4: add             x2, x2, HEAP, lsl #32
    // 0xe6fdf8: stur            x2, [fp, #-0x20]
    // 0xe6fdfc: r0 = SvgText()
    //     0xe6fdfc: bl              #0xe6fea4  ; AllocateSvgTextStub -> SvgText (size=0x40)
    // 0xe6fe00: ldur            d0, [fp, #-0x68]
    // 0xe6fe04: ArrayStore: r0[0] = d0  ; List_8
    //     0xe6fe04: stur            d0, [x0, #0x17]
    // 0xe6fe08: ldur            d0, [fp, #-0x70]
    // 0xe6fe0c: StoreField: r0->field_1f = d0
    //     0xe6fe0c: stur            d0, [x0, #0x1f]
    // 0xe6fe10: ldur            d0, [fp, #-0x60]
    // 0xe6fe14: StoreField: r0->field_27 = d0
    //     0xe6fe14: stur            d0, [x0, #0x27]
    // 0xe6fe18: ldur            x1, [fp, #-0x40]
    // 0xe6fe1c: StoreField: r0->field_2f = r1
    //     0xe6fe1c: stur            w1, [x0, #0x2f]
    // 0xe6fe20: ldur            x1, [fp, #-0x48]
    // 0xe6fe24: StoreField: r0->field_33 = r1
    //     0xe6fe24: stur            w1, [x0, #0x33]
    // 0xe6fe28: ldur            x1, [fp, #-8]
    // 0xe6fe2c: StoreField: r0->field_3b = r1
    //     0xe6fe2c: stur            w1, [x0, #0x3b]
    // 0xe6fe30: ldur            x1, [fp, #-0x50]
    // 0xe6fe34: StoreField: r0->field_37 = r1
    //     0xe6fe34: stur            w1, [x0, #0x37]
    // 0xe6fe38: ldur            x1, [fp, #-0x18]
    // 0xe6fe3c: StoreField: r0->field_7 = r1
    //     0xe6fe3c: stur            w1, [x0, #7]
    // 0xe6fe40: ldur            x1, [fp, #-0x10]
    // 0xe6fe44: StoreField: r0->field_b = r1
    //     0xe6fe44: stur            w1, [x0, #0xb]
    // 0xe6fe48: ldur            x1, [fp, #-0x30]
    // 0xe6fe4c: StoreField: r0->field_f = r1
    //     0xe6fe4c: stur            w1, [x0, #0xf]
    // 0xe6fe50: ldur            x1, [fp, #-0x20]
    // 0xe6fe54: StoreField: r0->field_13 = r1
    //     0xe6fe54: stur            w1, [x0, #0x13]
    // 0xe6fe58: LeaveFrame
    //     0xe6fe58: mov             SP, fp
    //     0xe6fe5c: ldp             fp, lr, [SP], #0x10
    // 0xe6fe60: ret
    //     0xe6fe60: ret             
    // 0xe6fe64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6fe64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6fe68: b               #0xe6f938
    // 0xe6fe6c: SaveReg d0
    //     0xe6fe6c: str             q0, [SP, #-0x10]!
    // 0xe6fe70: r0 = AllocateDouble()
    //     0xe6fe70: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6fe74: RestoreReg d0
    //     0xe6fe74: ldr             q0, [SP], #0x10
    // 0xe6fe78: b               #0xe6fa34
    // 0xe6fe7c: SaveReg d0
    //     0xe6fe7c: str             q0, [SP, #-0x10]!
    // 0xe6fe80: r0 = AllocateDouble()
    //     0xe6fe80: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6fe84: RestoreReg d0
    //     0xe6fe84: ldr             q0, [SP], #0x10
    // 0xe6fe88: b               #0xe6fa8c
    // 0xe6fe8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6fe8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6fe90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6fe90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6fe94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6fe94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6fe98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6fe98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6fe9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6fe9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6fea0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe6fea0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  [closure] static SvgText <anonymous closure>(dynamic, XmlElement) {
    // ** addr: 0xe70ee8, size: 0xbc
    // 0xe70ee8: EnterFrame
    //     0xe70ee8: stp             fp, lr, [SP, #-0x10]!
    //     0xe70eec: mov             fp, SP
    // 0xe70ef0: AllocStack(0x28)
    //     0xe70ef0: sub             SP, SP, #0x28
    // 0xe70ef4: SetupParameters()
    //     0xe70ef4: ldr             x0, [fp, #0x18]
    //     0xe70ef8: ldur            w4, [x0, #0x17]
    //     0xe70efc: add             x4, x4, HEAP, lsl #32
    //     0xe70f00: stur            x4, [fp, #-8]
    // 0xe70f04: CheckStackOverflow
    //     0xe70f04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe70f08: cmp             SP, x16
    //     0xe70f0c: b.ls            #0xe70f9c
    // 0xe70f10: LoadField: r3 = r4->field_f
    //     0xe70f10: ldur            w3, [x4, #0xf]
    // 0xe70f14: DecompressPointer r3
    //     0xe70f14: add             x3, x3, HEAP, lsl #32
    // 0xe70f18: LoadField: r5 = r4->field_13
    //     0xe70f18: ldur            w5, [x4, #0x13]
    // 0xe70f1c: DecompressPointer r5
    //     0xe70f1c: add             x5, x5, HEAP, lsl #32
    // 0xe70f20: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xe70f20: ldur            w0, [x4, #0x17]
    // 0xe70f24: DecompressPointer r0
    //     0xe70f24: add             x0, x0, HEAP, lsl #32
    // 0xe70f28: str             x0, [SP]
    // 0xe70f2c: ldr             x2, [fp, #0x10]
    // 0xe70f30: r1 = Null
    //     0xe70f30: mov             x1, NULL
    // 0xe70f34: r4 = const [0, 0x5, 0x1, 0x5, null]
    //     0xe70f34: ldr             x4, [PP, #0x718]  ; [pp+0x718] List(5) [0, 0x5, 0x1, 0x5, Null]
    // 0xe70f38: r0 = SvgText.fromXml()
    //     0xe70f38: bl              #0xe6f8e0  ; [package:pdf/src/svg/text.dart] SvgText::SvgText.fromXml
    // 0xe70f3c: stur            x0, [fp, #-0x10]
    // 0xe70f40: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe70f40: ldur            d0, [x0, #0x17]
    // 0xe70f44: LoadField: d1 = r0->field_27
    //     0xe70f44: ldur            d1, [x0, #0x27]
    // 0xe70f48: fadd            d2, d0, d1
    // 0xe70f4c: stur            d2, [fp, #-0x20]
    // 0xe70f50: LoadField: d0 = r0->field_1f
    //     0xe70f50: ldur            d0, [x0, #0x1f]
    // 0xe70f54: stur            d0, [fp, #-0x18]
    // 0xe70f58: r0 = PdfPoint()
    //     0xe70f58: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe70f5c: ldur            d0, [fp, #-0x20]
    // 0xe70f60: StoreField: r0->field_7 = d0
    //     0xe70f60: stur            d0, [x0, #7]
    // 0xe70f64: ldur            d0, [fp, #-0x18]
    // 0xe70f68: StoreField: r0->field_f = d0
    //     0xe70f68: stur            d0, [x0, #0xf]
    // 0xe70f6c: ldur            x1, [fp, #-8]
    // 0xe70f70: ArrayStore: r1[0] = r0  ; List_4
    //     0xe70f70: stur            w0, [x1, #0x17]
    //     0xe70f74: ldurb           w16, [x1, #-1]
    //     0xe70f78: ldurb           w17, [x0, #-1]
    //     0xe70f7c: and             x16, x17, x16, lsr #2
    //     0xe70f80: tst             x16, HEAP, lsr #32
    //     0xe70f84: b.eq            #0xe70f8c
    //     0xe70f88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe70f8c: ldur            x0, [fp, #-0x10]
    // 0xe70f90: LeaveFrame
    //     0xe70f90: mov             SP, fp
    //     0xe70f94: ldp             fp, lr, [SP], #0x10
    // 0xe70f98: ret
    //     0xe70f98: ret             
    // 0xe70f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe70f9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe70fa0: b               #0xe70f10
  }
  [closure] static String? <anonymous closure>(dynamic, XmlNode) {
    // ** addr: 0xe70fa4, size: 0x40
    // 0xe70fa4: EnterFrame
    //     0xe70fa4: stp             fp, lr, [SP, #-0x10]!
    //     0xe70fa8: mov             fp, SP
    // 0xe70fac: CheckStackOverflow
    //     0xe70fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe70fb0: cmp             SP, x16
    //     0xe70fb4: b.ls            #0xe70fdc
    // 0xe70fb8: ldr             x1, [fp, #0x10]
    // 0xe70fbc: r0 = LoadClassIdInstr(r1)
    //     0xe70fbc: ldur            x0, [x1, #-1]
    //     0xe70fc0: ubfx            x0, x0, #0xc, #0x14
    // 0xe70fc4: r0 = GDT[cid_x0 + -0x78f]()
    //     0xe70fc4: sub             lr, x0, #0x78f
    //     0xe70fc8: ldr             lr, [x21, lr, lsl #3]
    //     0xe70fcc: blr             lr
    // 0xe70fd0: LeaveFrame
    //     0xe70fd0: mov             SP, fp
    //     0xe70fd4: ldp             fp, lr, [SP], #0x10
    // 0xe70fd8: ret
    //     0xe70fd8: ret             
    // 0xe70fdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe70fdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe70fe0: b               #0xe70fb8
  }
  [closure] static bool <anonymous closure>(dynamic, XmlNode) {
    // ** addr: 0xe70fe4, size: 0x34
    // 0xe70fe4: ldr             x1, [SP]
    // 0xe70fe8: r2 = LoadClassIdInstr(r1)
    //     0xe70fe8: ldur            x2, [x1, #-1]
    //     0xe70fec: ubfx            x2, x2, #0xc, #0x14
    // 0xe70ff0: cmp             x2, #0xf3
    // 0xe70ff4: b.ne            #0xe71000
    // 0xe70ff8: r0 = true
    //     0xe70ff8: add             x0, NULL, #0x20  ; true
    // 0xe70ffc: b               #0xe71014
    // 0xe71000: cmp             x2, #0xf6
    // 0xe71004: r16 = true
    //     0xe71004: add             x16, NULL, #0x20  ; true
    // 0xe71008: r17 = false
    //     0xe71008: add             x17, NULL, #0x30  ; false
    // 0xe7100c: csel            x1, x16, x17, eq
    // 0xe71010: mov             x0, x1
    // 0xe71014: ret
    //     0xe71014: ret             
  }
  _ boundingBox(/* No info */) {
    // ** addr: 0xeab500, size: 0x878
    // 0xeab500: EnterFrame
    //     0xeab500: stp             fp, lr, [SP, #-0x10]!
    //     0xeab504: mov             fp, SP
    // 0xeab508: AllocStack(0x90)
    //     0xeab508: sub             SP, SP, #0x90
    // 0xeab50c: SetupParameters(SvgText this /* r1 => r0, fp-0x8 */)
    //     0xeab50c: mov             x0, x1
    //     0xeab510: stur            x1, [fp, #-8]
    // 0xeab514: CheckStackOverflow
    //     0xeab514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeab518: cmp             SP, x16
    //     0xeab51c: b.ls            #0xeabc20
    // 0xeab520: LoadField: r1 = r0->field_37
    //     0xeab520: ldur            w1, [x0, #0x37]
    // 0xeab524: DecompressPointer r1
    //     0xeab524: add             x1, x1, HEAP, lsl #32
    // 0xeab528: r0 = toPdfRect()
    //     0xeab528: bl              #0xeabd78  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::toPdfRect
    // 0xeab52c: LoadField: d0 = r0->field_7
    //     0xeab52c: ldur            d0, [x0, #7]
    // 0xeab530: stur            d0, [fp, #-0x80]
    // 0xeab534: LoadField: d1 = r0->field_f
    //     0xeab534: ldur            d1, [x0, #0xf]
    // 0xeab538: stur            d1, [fp, #-0x78]
    // 0xeab53c: ArrayLoad: d2 = r0[0]  ; List_8
    //     0xeab53c: ldur            d2, [x0, #0x17]
    // 0xeab540: stur            d2, [fp, #-0x70]
    // 0xeab544: LoadField: d3 = r0->field_1f
    //     0xeab544: ldur            d3, [x0, #0x1f]
    // 0xeab548: ldur            x0, [fp, #-8]
    // 0xeab54c: stur            d3, [fp, #-0x68]
    // 0xeab550: LoadField: r1 = r0->field_3b
    //     0xeab550: ldur            w1, [x0, #0x3b]
    // 0xeab554: DecompressPointer r1
    //     0xeab554: add             x1, x1, HEAP, lsl #32
    // 0xeab558: r0 = iterator()
    //     0xeab558: bl              #0x887d48  ; [dart:_internal] MappedIterable::iterator
    // 0xeab55c: mov             x2, x0
    // 0xeab560: ldur            d0, [fp, #-0x80]
    // 0xeab564: stur            x2, [fp, #-0x40]
    // 0xeab568: r0 = inline_Allocate_Double()
    //     0xeab568: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xeab56c: add             x0, x0, #0x10
    //     0xeab570: cmp             x1, x0
    //     0xeab574: b.ls            #0xeabc28
    //     0xeab578: str             x0, [THR, #0x50]  ; THR::top
    //     0xeab57c: sub             x0, x0, #0xf
    //     0xeab580: movz            x1, #0xe15c
    //     0xeab584: movk            x1, #0x3, lsl #16
    //     0xeab588: stur            x1, [x0, #-1]
    // 0xeab58c: StoreField: r0->field_7 = d0
    //     0xeab58c: stur            d0, [x0, #7]
    // 0xeab590: ldur            d0, [fp, #-0x78]
    // 0xeab594: r1 = inline_Allocate_Double()
    //     0xeab594: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xeab598: add             x1, x1, #0x10
    //     0xeab59c: cmp             x3, x1
    //     0xeab5a0: b.ls            #0xeabc40
    //     0xeab5a4: str             x1, [THR, #0x50]  ; THR::top
    //     0xeab5a8: sub             x1, x1, #0xf
    //     0xeab5ac: movz            x3, #0xe15c
    //     0xeab5b0: movk            x3, #0x3, lsl #16
    //     0xeab5b4: stur            x3, [x1, #-1]
    // 0xeab5b8: StoreField: r1->field_7 = d0
    //     0xeab5b8: stur            d0, [x1, #7]
    // 0xeab5bc: ldur            d0, [fp, #-0x70]
    // 0xeab5c0: r3 = inline_Allocate_Double()
    //     0xeab5c0: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0xeab5c4: add             x3, x3, #0x10
    //     0xeab5c8: cmp             x4, x3
    //     0xeab5cc: b.ls            #0xeabc5c
    //     0xeab5d0: str             x3, [THR, #0x50]  ; THR::top
    //     0xeab5d4: sub             x3, x3, #0xf
    //     0xeab5d8: movz            x4, #0xe15c
    //     0xeab5dc: movk            x4, #0x3, lsl #16
    //     0xeab5e0: stur            x4, [x3, #-1]
    // 0xeab5e4: StoreField: r3->field_7 = d0
    //     0xeab5e4: stur            d0, [x3, #7]
    // 0xeab5e8: ldur            d0, [fp, #-0x68]
    // 0xeab5ec: r4 = inline_Allocate_Double()
    //     0xeab5ec: ldp             x4, x5, [THR, #0x50]  ; THR::top
    //     0xeab5f0: add             x4, x4, #0x10
    //     0xeab5f4: cmp             x5, x4
    //     0xeab5f8: b.ls            #0xeabc80
    //     0xeab5fc: str             x4, [THR, #0x50]  ; THR::top
    //     0xeab600: sub             x4, x4, #0xf
    //     0xeab604: movz            x5, #0xe15c
    //     0xeab608: movk            x5, #0x3, lsl #16
    //     0xeab60c: stur            x5, [x4, #-1]
    // 0xeab610: StoreField: r4->field_7 = d0
    //     0xeab610: stur            d0, [x4, #7]
    // 0xeab614: LoadField: r5 = r2->field_f
    //     0xeab614: ldur            w5, [x2, #0xf]
    // 0xeab618: DecompressPointer r5
    //     0xeab618: add             x5, x5, HEAP, lsl #32
    // 0xeab61c: stur            x5, [fp, #-0x38]
    // 0xeab620: LoadField: r6 = r2->field_13
    //     0xeab620: ldur            w6, [x2, #0x13]
    // 0xeab624: DecompressPointer r6
    //     0xeab624: add             x6, x6, HEAP, lsl #32
    // 0xeab628: stur            x6, [fp, #-0x30]
    // 0xeab62c: LoadField: r7 = r2->field_7
    //     0xeab62c: ldur            w7, [x2, #7]
    // 0xeab630: DecompressPointer r7
    //     0xeab630: add             x7, x7, HEAP, lsl #32
    // 0xeab634: stur            x7, [fp, #-0x28]
    // 0xeab638: mov             x9, x0
    // 0xeab63c: mov             x8, x1
    // 0xeab640: mov             x16, x4
    // 0xeab644: mov             x4, x3
    // 0xeab648: mov             x3, x16
    // 0xeab64c: stur            x9, [fp, #-8]
    // 0xeab650: stur            x8, [fp, #-0x10]
    // 0xeab654: stur            x4, [fp, #-0x18]
    // 0xeab658: stur            x3, [fp, #-0x20]
    // 0xeab65c: CheckStackOverflow
    //     0xeab65c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeab660: cmp             SP, x16
    //     0xeab664: b.ls            #0xeabca4
    // 0xeab668: r0 = LoadClassIdInstr(r5)
    //     0xeab668: ldur            x0, [x5, #-1]
    //     0xeab66c: ubfx            x0, x0, #0xc, #0x14
    // 0xeab670: mov             x1, x5
    // 0xeab674: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xeab674: movz            x17, #0x292d
    //     0xeab678: movk            x17, #0x1, lsl #16
    //     0xeab67c: add             lr, x0, x17
    //     0xeab680: ldr             lr, [x21, lr, lsl #3]
    //     0xeab684: blr             lr
    // 0xeab688: tbnz            w0, #4, #0xeabbc4
    // 0xeab68c: ldur            x2, [fp, #-0x40]
    // 0xeab690: ldur            x3, [fp, #-0x38]
    // 0xeab694: r0 = LoadClassIdInstr(r3)
    //     0xeab694: ldur            x0, [x3, #-1]
    //     0xeab698: ubfx            x0, x0, #0xc, #0x14
    // 0xeab69c: mov             x1, x3
    // 0xeab6a0: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xeab6a0: movz            x17, #0x384d
    //     0xeab6a4: movk            x17, #0x1, lsl #16
    //     0xeab6a8: add             lr, x0, x17
    //     0xeab6ac: ldr             lr, [x21, lr, lsl #3]
    //     0xeab6b0: blr             lr
    // 0xeab6b4: ldur            x16, [fp, #-0x30]
    // 0xeab6b8: stp             x0, x16, [SP]
    // 0xeab6bc: ldur            x0, [fp, #-0x30]
    // 0xeab6c0: ClosureCall
    //     0xeab6c0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeab6c4: ldur            x2, [x0, #0x1f]
    //     0xeab6c8: blr             x2
    // 0xeab6cc: mov             x4, x0
    // 0xeab6d0: ldur            x3, [fp, #-0x40]
    // 0xeab6d4: stur            x4, [fp, #-0x48]
    // 0xeab6d8: StoreField: r3->field_b = r0
    //     0xeab6d8: stur            w0, [x3, #0xb]
    //     0xeab6dc: tbz             w0, #0, #0xeab6f8
    //     0xeab6e0: ldurb           w16, [x3, #-1]
    //     0xeab6e4: ldurb           w17, [x0, #-1]
    //     0xeab6e8: and             x16, x17, x16, lsr #2
    //     0xeab6ec: tst             x16, HEAP, lsr #32
    //     0xeab6f0: b.eq            #0xeab6f8
    //     0xeab6f4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xeab6f8: cmp             w4, NULL
    // 0xeab6fc: b.ne            #0xeab730
    // 0xeab700: mov             x0, x4
    // 0xeab704: ldur            x2, [fp, #-0x28]
    // 0xeab708: r1 = Null
    //     0xeab708: mov             x1, NULL
    // 0xeab70c: cmp             w2, NULL
    // 0xeab710: b.eq            #0xeab730
    // 0xeab714: LoadField: r4 = r2->field_1b
    //     0xeab714: ldur            w4, [x2, #0x1b]
    // 0xeab718: DecompressPointer r4
    //     0xeab718: add             x4, x4, HEAP, lsl #32
    // 0xeab71c: r8 = X1
    //     0xeab71c: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xeab720: LoadField: r9 = r4->field_7
    //     0xeab720: ldur            x9, [x4, #7]
    // 0xeab724: r3 = Null
    //     0xeab724: add             x3, PP, #0x57, lsl #12  ; [pp+0x579d0] Null
    //     0xeab728: ldr             x3, [x3, #0x9d0]
    // 0xeab72c: blr             x9
    // 0xeab730: ldur            x1, [fp, #-0x48]
    // 0xeab734: r0 = boundingBox()
    //     0xeab734: bl              #0xeab500  ; [package:pdf/src/svg/text.dart] SvgText::boundingBox
    // 0xeab738: stur            x0, [fp, #-0x50]
    // 0xeab73c: LoadField: d0 = r0->field_7
    //     0xeab73c: ldur            d0, [x0, #7]
    // 0xeab740: stur            d0, [fp, #-0x68]
    // 0xeab744: r1 = inline_Allocate_Double()
    //     0xeab744: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xeab748: add             x1, x1, #0x10
    //     0xeab74c: cmp             x2, x1
    //     0xeab750: b.ls            #0xeabcac
    //     0xeab754: str             x1, [THR, #0x50]  ; THR::top
    //     0xeab758: sub             x1, x1, #0xf
    //     0xeab75c: movz            x2, #0xe15c
    //     0xeab760: movk            x2, #0x3, lsl #16
    //     0xeab764: stur            x2, [x1, #-1]
    // 0xeab768: StoreField: r1->field_7 = d0
    //     0xeab768: stur            d0, [x1, #7]
    // 0xeab76c: stur            x1, [fp, #-0x48]
    // 0xeab770: ldur            x16, [fp, #-8]
    // 0xeab774: stp             x16, x1, [SP]
    // 0xeab778: r0 = >()
    //     0xeab778: bl              #0xebf1c4  ; [dart:core] _Double::>
    // 0xeab77c: tbnz            w0, #4, #0xeab78c
    // 0xeab780: ldur            x9, [fp, #-8]
    // 0xeab784: d1 = 0.000000
    //     0xeab784: eor             v1.16b, v1.16b, v1.16b
    // 0xeab788: b               #0xeab860
    // 0xeab78c: ldur            x16, [fp, #-0x48]
    // 0xeab790: ldur            lr, [fp, #-8]
    // 0xeab794: stp             lr, x16, [SP]
    // 0xeab798: r0 = <()
    //     0xeab798: bl              #0xebdf2c  ; [dart:core] _Double::<
    // 0xeab79c: tbnz            w0, #4, #0xeab7ac
    // 0xeab7a0: ldur            x9, [fp, #-0x48]
    // 0xeab7a4: d1 = 0.000000
    //     0xeab7a4: eor             v1.16b, v1.16b, v1.16b
    // 0xeab7a8: b               #0xeab860
    // 0xeab7ac: ldur            x0, [fp, #-8]
    // 0xeab7b0: r1 = 60
    //     0xeab7b0: movz            x1, #0x3c
    // 0xeab7b4: branchIfSmi(r0, 0xeab7c0)
    //     0xeab7b4: tbz             w0, #0, #0xeab7c0
    // 0xeab7b8: r1 = LoadClassIdInstr(r0)
    //     0xeab7b8: ldur            x1, [x0, #-1]
    //     0xeab7bc: ubfx            x1, x1, #0xc, #0x14
    // 0xeab7c0: cmp             x1, #0x3e
    // 0xeab7c4: b.ne            #0xeab858
    // 0xeab7c8: ldur            d0, [fp, #-0x68]
    // 0xeab7cc: d1 = 0.000000
    //     0xeab7cc: eor             v1.16b, v1.16b, v1.16b
    // 0xeab7d0: fcmp            d0, d1
    // 0xeab7d4: b.ne            #0xeab818
    // 0xeab7d8: LoadField: d2 = r0->field_7
    //     0xeab7d8: ldur            d2, [x0, #7]
    // 0xeab7dc: fadd            d3, d0, d2
    // 0xeab7e0: fmul            d4, d3, d0
    // 0xeab7e4: fmul            d0, d4, d2
    // 0xeab7e8: r0 = inline_Allocate_Double()
    //     0xeab7e8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xeab7ec: add             x0, x0, #0x10
    //     0xeab7f0: cmp             x1, x0
    //     0xeab7f4: b.ls            #0xeabcc8
    //     0xeab7f8: str             x0, [THR, #0x50]  ; THR::top
    //     0xeab7fc: sub             x0, x0, #0xf
    //     0xeab800: movz            x1, #0xe15c
    //     0xeab804: movk            x1, #0x3, lsl #16
    //     0xeab808: stur            x1, [x0, #-1]
    // 0xeab80c: StoreField: r0->field_7 = d0
    //     0xeab80c: stur            d0, [x0, #7]
    // 0xeab810: mov             x9, x0
    // 0xeab814: b               #0xeab860
    // 0xeab818: fcmp            d0, d1
    // 0xeab81c: b.ne            #0xeab83c
    // 0xeab820: LoadField: d0 = r0->field_7
    //     0xeab820: ldur            d0, [x0, #7]
    // 0xeab824: fcmp            d0, #0.0
    // 0xeab828: b.vs            #0xeab83c
    // 0xeab82c: b.ne            #0xeab838
    // 0xeab830: r1 = 0.000000
    //     0xeab830: fmov            x1, d0
    // 0xeab834: cmp             x1, #0
    // 0xeab838: b.lt            #0xeab848
    // 0xeab83c: LoadField: d0 = r0->field_7
    //     0xeab83c: ldur            d0, [x0, #7]
    // 0xeab840: fcmp            d0, d0
    // 0xeab844: b.vc            #0xeab850
    // 0xeab848: mov             x9, x0
    // 0xeab84c: b               #0xeab860
    // 0xeab850: ldur            x9, [fp, #-0x48]
    // 0xeab854: b               #0xeab860
    // 0xeab858: d1 = 0.000000
    //     0xeab858: eor             v1.16b, v1.16b, v1.16b
    // 0xeab85c: ldur            x9, [fp, #-0x48]
    // 0xeab860: ldur            x0, [fp, #-0x50]
    // 0xeab864: stur            x9, [fp, #-0x58]
    // 0xeab868: LoadField: d0 = r0->field_f
    //     0xeab868: ldur            d0, [x0, #0xf]
    // 0xeab86c: stur            d0, [fp, #-0x68]
    // 0xeab870: r1 = inline_Allocate_Double()
    //     0xeab870: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xeab874: add             x1, x1, #0x10
    //     0xeab878: cmp             x2, x1
    //     0xeab87c: b.ls            #0xeabcd8
    //     0xeab880: str             x1, [THR, #0x50]  ; THR::top
    //     0xeab884: sub             x1, x1, #0xf
    //     0xeab888: movz            x2, #0xe15c
    //     0xeab88c: movk            x2, #0x3, lsl #16
    //     0xeab890: stur            x2, [x1, #-1]
    // 0xeab894: StoreField: r1->field_7 = d0
    //     0xeab894: stur            d0, [x1, #7]
    // 0xeab898: stur            x1, [fp, #-0x48]
    // 0xeab89c: ldur            x16, [fp, #-0x10]
    // 0xeab8a0: stp             x16, x1, [SP]
    // 0xeab8a4: r0 = >()
    //     0xeab8a4: bl              #0xebf1c4  ; [dart:core] _Double::>
    // 0xeab8a8: tbnz            w0, #4, #0xeab8b8
    // 0xeab8ac: ldur            x8, [fp, #-0x10]
    // 0xeab8b0: d0 = 0.000000
    //     0xeab8b0: eor             v0.16b, v0.16b, v0.16b
    // 0xeab8b4: b               #0xeab98c
    // 0xeab8b8: ldur            x16, [fp, #-0x48]
    // 0xeab8bc: ldur            lr, [fp, #-0x10]
    // 0xeab8c0: stp             lr, x16, [SP]
    // 0xeab8c4: r0 = <()
    //     0xeab8c4: bl              #0xebdf2c  ; [dart:core] _Double::<
    // 0xeab8c8: tbnz            w0, #4, #0xeab8d8
    // 0xeab8cc: ldur            x8, [fp, #-0x48]
    // 0xeab8d0: d0 = 0.000000
    //     0xeab8d0: eor             v0.16b, v0.16b, v0.16b
    // 0xeab8d4: b               #0xeab98c
    // 0xeab8d8: ldur            x1, [fp, #-0x10]
    // 0xeab8dc: r0 = 60
    //     0xeab8dc: movz            x0, #0x3c
    // 0xeab8e0: branchIfSmi(r1, 0xeab8ec)
    //     0xeab8e0: tbz             w1, #0, #0xeab8ec
    // 0xeab8e4: r0 = LoadClassIdInstr(r1)
    //     0xeab8e4: ldur            x0, [x1, #-1]
    //     0xeab8e8: ubfx            x0, x0, #0xc, #0x14
    // 0xeab8ec: cmp             x0, #0x3e
    // 0xeab8f0: b.ne            #0xeab984
    // 0xeab8f4: ldur            d1, [fp, #-0x68]
    // 0xeab8f8: d0 = 0.000000
    //     0xeab8f8: eor             v0.16b, v0.16b, v0.16b
    // 0xeab8fc: fcmp            d1, d0
    // 0xeab900: b.ne            #0xeab944
    // 0xeab904: LoadField: d2 = r1->field_7
    //     0xeab904: ldur            d2, [x1, #7]
    // 0xeab908: fadd            d3, d1, d2
    // 0xeab90c: fmul            d4, d3, d1
    // 0xeab910: fmul            d1, d4, d2
    // 0xeab914: r0 = inline_Allocate_Double()
    //     0xeab914: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xeab918: add             x0, x0, #0x10
    //     0xeab91c: cmp             x1, x0
    //     0xeab920: b.ls            #0xeabcf4
    //     0xeab924: str             x0, [THR, #0x50]  ; THR::top
    //     0xeab928: sub             x0, x0, #0xf
    //     0xeab92c: movz            x1, #0xe15c
    //     0xeab930: movk            x1, #0x3, lsl #16
    //     0xeab934: stur            x1, [x0, #-1]
    // 0xeab938: StoreField: r0->field_7 = d1
    //     0xeab938: stur            d1, [x0, #7]
    // 0xeab93c: mov             x8, x0
    // 0xeab940: b               #0xeab98c
    // 0xeab944: fcmp            d1, d0
    // 0xeab948: b.ne            #0xeab968
    // 0xeab94c: LoadField: d1 = r1->field_7
    //     0xeab94c: ldur            d1, [x1, #7]
    // 0xeab950: fcmp            d1, #0.0
    // 0xeab954: b.vs            #0xeab968
    // 0xeab958: b.ne            #0xeab964
    // 0xeab95c: r0 = 0.000000
    //     0xeab95c: fmov            x0, d1
    // 0xeab960: cmp             x0, #0
    // 0xeab964: b.lt            #0xeab974
    // 0xeab968: LoadField: d1 = r1->field_7
    //     0xeab968: ldur            d1, [x1, #7]
    // 0xeab96c: fcmp            d1, d1
    // 0xeab970: b.vc            #0xeab97c
    // 0xeab974: mov             x8, x1
    // 0xeab978: b               #0xeab98c
    // 0xeab97c: ldur            x8, [fp, #-0x48]
    // 0xeab980: b               #0xeab98c
    // 0xeab984: d0 = 0.000000
    //     0xeab984: eor             v0.16b, v0.16b, v0.16b
    // 0xeab988: ldur            x8, [fp, #-0x48]
    // 0xeab98c: ldur            x0, [fp, #-0x50]
    // 0xeab990: stur            x8, [fp, #-0x60]
    // 0xeab994: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xeab994: ldur            d1, [x0, #0x17]
    // 0xeab998: stur            d1, [fp, #-0x68]
    // 0xeab99c: r1 = inline_Allocate_Double()
    //     0xeab99c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xeab9a0: add             x1, x1, #0x10
    //     0xeab9a4: cmp             x2, x1
    //     0xeab9a8: b.ls            #0xeabd04
    //     0xeab9ac: str             x1, [THR, #0x50]  ; THR::top
    //     0xeab9b0: sub             x1, x1, #0xf
    //     0xeab9b4: movz            x2, #0xe15c
    //     0xeab9b8: movk            x2, #0x3, lsl #16
    //     0xeab9bc: stur            x2, [x1, #-1]
    // 0xeab9c0: StoreField: r1->field_7 = d1
    //     0xeab9c0: stur            d1, [x1, #7]
    // 0xeab9c4: stur            x1, [fp, #-0x48]
    // 0xeab9c8: ldur            x16, [fp, #-0x18]
    // 0xeab9cc: stp             x16, x1, [SP]
    // 0xeab9d0: r0 = >()
    //     0xeab9d0: bl              #0xebf1c4  ; [dart:core] _Double::>
    // 0xeab9d4: tbnz            w0, #4, #0xeab9e0
    // 0xeab9d8: ldur            x1, [fp, #-0x48]
    // 0xeab9dc: b               #0xeabac8
    // 0xeab9e0: ldur            x16, [fp, #-0x48]
    // 0xeab9e4: ldur            lr, [fp, #-0x18]
    // 0xeab9e8: stp             lr, x16, [SP]
    // 0xeab9ec: r0 = <()
    //     0xeab9ec: bl              #0xebdf2c  ; [dart:core] _Double::<
    // 0xeab9f0: tbnz            w0, #4, #0xeab9fc
    // 0xeab9f4: ldur            x1, [fp, #-0x18]
    // 0xeab9f8: b               #0xeabac8
    // 0xeab9fc: ldur            x1, [fp, #-0x18]
    // 0xeaba00: r0 = 60
    //     0xeaba00: movz            x0, #0x3c
    // 0xeaba04: branchIfSmi(r1, 0xeaba10)
    //     0xeaba04: tbz             w1, #0, #0xeaba10
    // 0xeaba08: r0 = LoadClassIdInstr(r1)
    //     0xeaba08: ldur            x0, [x1, #-1]
    //     0xeaba0c: ubfx            x0, x0, #0xc, #0x14
    // 0xeaba10: cmp             x0, #0x3e
    // 0xeaba14: b.ne            #0xeaba74
    // 0xeaba18: ldur            d1, [fp, #-0x68]
    // 0xeaba1c: d0 = 0.000000
    //     0xeaba1c: eor             v0.16b, v0.16b, v0.16b
    // 0xeaba20: fcmp            d1, d0
    // 0xeaba24: b.ne            #0xeaba60
    // 0xeaba28: LoadField: d2 = r1->field_7
    //     0xeaba28: ldur            d2, [x1, #7]
    // 0xeaba2c: fadd            d3, d1, d2
    // 0xeaba30: r0 = inline_Allocate_Double()
    //     0xeaba30: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xeaba34: add             x0, x0, #0x10
    //     0xeaba38: cmp             x1, x0
    //     0xeaba3c: b.ls            #0xeabd20
    //     0xeaba40: str             x0, [THR, #0x50]  ; THR::top
    //     0xeaba44: sub             x0, x0, #0xf
    //     0xeaba48: movz            x1, #0xe15c
    //     0xeaba4c: movk            x1, #0x3, lsl #16
    //     0xeaba50: stur            x1, [x0, #-1]
    // 0xeaba54: StoreField: r0->field_7 = d3
    //     0xeaba54: stur            d3, [x0, #7]
    // 0xeaba58: mov             x1, x0
    // 0xeaba5c: b               #0xeabac8
    // 0xeaba60: LoadField: d1 = r1->field_7
    //     0xeaba60: ldur            d1, [x1, #7]
    // 0xeaba64: fcmp            d1, d1
    // 0xeaba68: b.vs            #0xeabac8
    // 0xeaba6c: ldur            x1, [fp, #-0x48]
    // 0xeaba70: b               #0xeabac8
    // 0xeaba74: ldur            d1, [fp, #-0x68]
    // 0xeaba78: d0 = 0.000000
    //     0xeaba78: eor             v0.16b, v0.16b, v0.16b
    // 0xeaba7c: r0 = 60
    //     0xeaba7c: movz            x0, #0x3c
    // 0xeaba80: branchIfSmi(r1, 0xeaba8c)
    //     0xeaba80: tbz             w1, #0, #0xeaba8c
    // 0xeaba84: r0 = LoadClassIdInstr(r1)
    //     0xeaba84: ldur            x0, [x1, #-1]
    //     0xeaba88: ubfx            x0, x0, #0xc, #0x14
    // 0xeaba8c: stp             xzr, x1, [SP]
    // 0xeaba90: mov             lr, x0
    // 0xeaba94: ldr             lr, [x21, lr, lsl #3]
    // 0xeaba98: blr             lr
    // 0xeaba9c: tbnz            w0, #4, #0xeabac4
    // 0xeabaa0: ldur            d0, [fp, #-0x68]
    // 0xeabaa4: fcmp            d0, #0.0
    // 0xeabaa8: b.vs            #0xeabac4
    // 0xeabaac: b.ne            #0xeabab8
    // 0xeabab0: r0 = 0.000000
    //     0xeabab0: fmov            x0, d0
    // 0xeabab4: cmp             x0, #0
    // 0xeabab8: b.ge            #0xeabac4
    // 0xeababc: ldur            x1, [fp, #-0x18]
    // 0xeabac0: b               #0xeabac8
    // 0xeabac4: ldur            x1, [fp, #-0x48]
    // 0xeabac8: ldur            x0, [fp, #-0x50]
    // 0xeabacc: LoadField: d0 = r0->field_1f
    //     0xeabacc: ldur            d0, [x0, #0x1f]
    // 0xeabad0: LoadField: d1 = r1->field_7
    //     0xeabad0: ldur            d1, [x1, #7]
    // 0xeabad4: fcmp            d0, d1
    // 0xeabad8: b.le            #0xeabb10
    // 0xeabadc: r0 = inline_Allocate_Double()
    //     0xeabadc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xeabae0: add             x0, x0, #0x10
    //     0xeabae4: cmp             x2, x0
    //     0xeabae8: b.ls            #0xeabd30
    //     0xeabaec: str             x0, [THR, #0x50]  ; THR::top
    //     0xeabaf0: sub             x0, x0, #0xf
    //     0xeabaf4: movz            x2, #0xe15c
    //     0xeabaf8: movk            x2, #0x3, lsl #16
    //     0xeabafc: stur            x2, [x0, #-1]
    // 0xeabb00: StoreField: r0->field_7 = d0
    //     0xeabb00: stur            d0, [x0, #7]
    // 0xeabb04: mov             x3, x0
    // 0xeabb08: d2 = 0.000000
    //     0xeabb08: eor             v2.16b, v2.16b, v2.16b
    // 0xeabb0c: b               #0xeabba4
    // 0xeabb10: fcmp            d1, d0
    // 0xeabb14: b.le            #0xeabb24
    // 0xeabb18: mov             x3, x1
    // 0xeabb1c: d2 = 0.000000
    //     0xeabb1c: eor             v2.16b, v2.16b, v2.16b
    // 0xeabb20: b               #0xeabba4
    // 0xeabb24: d2 = 0.000000
    //     0xeabb24: eor             v2.16b, v2.16b, v2.16b
    // 0xeabb28: fcmp            d0, d2
    // 0xeabb2c: b.ne            #0xeabb64
    // 0xeabb30: fadd            d3, d0, d1
    // 0xeabb34: r0 = inline_Allocate_Double()
    //     0xeabb34: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xeabb38: add             x0, x0, #0x10
    //     0xeabb3c: cmp             x2, x0
    //     0xeabb40: b.ls            #0xeabd48
    //     0xeabb44: str             x0, [THR, #0x50]  ; THR::top
    //     0xeabb48: sub             x0, x0, #0xf
    //     0xeabb4c: movz            x2, #0xe15c
    //     0xeabb50: movk            x2, #0x3, lsl #16
    //     0xeabb54: stur            x2, [x0, #-1]
    // 0xeabb58: StoreField: r0->field_7 = d3
    //     0xeabb58: stur            d3, [x0, #7]
    // 0xeabb5c: mov             x3, x0
    // 0xeabb60: b               #0xeabba4
    // 0xeabb64: LoadField: d1 = r1->field_7
    //     0xeabb64: ldur            d1, [x1, #7]
    // 0xeabb68: fcmp            d1, d1
    // 0xeabb6c: b.vc            #0xeabb78
    // 0xeabb70: mov             x3, x1
    // 0xeabb74: b               #0xeabba4
    // 0xeabb78: r0 = inline_Allocate_Double()
    //     0xeabb78: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xeabb7c: add             x0, x0, #0x10
    //     0xeabb80: cmp             x2, x0
    //     0xeabb84: b.ls            #0xeabd60
    //     0xeabb88: str             x0, [THR, #0x50]  ; THR::top
    //     0xeabb8c: sub             x0, x0, #0xf
    //     0xeabb90: movz            x2, #0xe15c
    //     0xeabb94: movk            x2, #0x3, lsl #16
    //     0xeabb98: stur            x2, [x0, #-1]
    // 0xeabb9c: StoreField: r0->field_7 = d0
    //     0xeabb9c: stur            d0, [x0, #7]
    // 0xeabba0: mov             x3, x0
    // 0xeabba4: ldur            x9, [fp, #-0x58]
    // 0xeabba8: ldur            x8, [fp, #-0x60]
    // 0xeabbac: mov             x4, x1
    // 0xeabbb0: ldur            x2, [fp, #-0x40]
    // 0xeabbb4: ldur            x7, [fp, #-0x28]
    // 0xeabbb8: ldur            x5, [fp, #-0x38]
    // 0xeabbbc: ldur            x6, [fp, #-0x30]
    // 0xeabbc0: b               #0xeab64c
    // 0xeabbc4: ldur            x3, [fp, #-0x40]
    // 0xeabbc8: ldur            x0, [fp, #-8]
    // 0xeabbcc: ldur            x1, [fp, #-0x10]
    // 0xeabbd0: ldur            x2, [fp, #-0x18]
    // 0xeabbd4: ldur            x4, [fp, #-0x20]
    // 0xeabbd8: StoreField: r3->field_b = rNULL
    //     0xeabbd8: stur            NULL, [x3, #0xb]
    // 0xeabbdc: LoadField: d0 = r0->field_7
    //     0xeabbdc: ldur            d0, [x0, #7]
    // 0xeabbe0: stur            d0, [fp, #-0x68]
    // 0xeabbe4: r0 = PdfRect()
    //     0xeabbe4: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xeabbe8: ldur            d0, [fp, #-0x68]
    // 0xeabbec: StoreField: r0->field_7 = d0
    //     0xeabbec: stur            d0, [x0, #7]
    // 0xeabbf0: ldur            x1, [fp, #-0x10]
    // 0xeabbf4: LoadField: d0 = r1->field_7
    //     0xeabbf4: ldur            d0, [x1, #7]
    // 0xeabbf8: StoreField: r0->field_f = d0
    //     0xeabbf8: stur            d0, [x0, #0xf]
    // 0xeabbfc: ldur            x1, [fp, #-0x18]
    // 0xeabc00: LoadField: d0 = r1->field_7
    //     0xeabc00: ldur            d0, [x1, #7]
    // 0xeabc04: ArrayStore: r0[0] = d0  ; List_8
    //     0xeabc04: stur            d0, [x0, #0x17]
    // 0xeabc08: ldur            x1, [fp, #-0x20]
    // 0xeabc0c: LoadField: d0 = r1->field_7
    //     0xeabc0c: ldur            d0, [x1, #7]
    // 0xeabc10: StoreField: r0->field_1f = d0
    //     0xeabc10: stur            d0, [x0, #0x1f]
    // 0xeabc14: LeaveFrame
    //     0xeabc14: mov             SP, fp
    //     0xeabc18: ldp             fp, lr, [SP], #0x10
    // 0xeabc1c: ret
    //     0xeabc1c: ret             
    // 0xeabc20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeabc20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeabc24: b               #0xeab520
    // 0xeabc28: SaveReg d0
    //     0xeabc28: str             q0, [SP, #-0x10]!
    // 0xeabc2c: SaveReg r2
    //     0xeabc2c: str             x2, [SP, #-8]!
    // 0xeabc30: r0 = AllocateDouble()
    //     0xeabc30: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabc34: RestoreReg r2
    //     0xeabc34: ldr             x2, [SP], #8
    // 0xeabc38: RestoreReg d0
    //     0xeabc38: ldr             q0, [SP], #0x10
    // 0xeabc3c: b               #0xeab58c
    // 0xeabc40: SaveReg d0
    //     0xeabc40: str             q0, [SP, #-0x10]!
    // 0xeabc44: stp             x0, x2, [SP, #-0x10]!
    // 0xeabc48: r0 = AllocateDouble()
    //     0xeabc48: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabc4c: mov             x1, x0
    // 0xeabc50: ldp             x0, x2, [SP], #0x10
    // 0xeabc54: RestoreReg d0
    //     0xeabc54: ldr             q0, [SP], #0x10
    // 0xeabc58: b               #0xeab5b8
    // 0xeabc5c: SaveReg d0
    //     0xeabc5c: str             q0, [SP, #-0x10]!
    // 0xeabc60: stp             x1, x2, [SP, #-0x10]!
    // 0xeabc64: SaveReg r0
    //     0xeabc64: str             x0, [SP, #-8]!
    // 0xeabc68: r0 = AllocateDouble()
    //     0xeabc68: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabc6c: mov             x3, x0
    // 0xeabc70: RestoreReg r0
    //     0xeabc70: ldr             x0, [SP], #8
    // 0xeabc74: ldp             x1, x2, [SP], #0x10
    // 0xeabc78: RestoreReg d0
    //     0xeabc78: ldr             q0, [SP], #0x10
    // 0xeabc7c: b               #0xeab5e4
    // 0xeabc80: SaveReg d0
    //     0xeabc80: str             q0, [SP, #-0x10]!
    // 0xeabc84: stp             x2, x3, [SP, #-0x10]!
    // 0xeabc88: stp             x0, x1, [SP, #-0x10]!
    // 0xeabc8c: r0 = AllocateDouble()
    //     0xeabc8c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabc90: mov             x4, x0
    // 0xeabc94: ldp             x0, x1, [SP], #0x10
    // 0xeabc98: ldp             x2, x3, [SP], #0x10
    // 0xeabc9c: RestoreReg d0
    //     0xeabc9c: ldr             q0, [SP], #0x10
    // 0xeabca0: b               #0xeab610
    // 0xeabca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeabca4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeabca8: b               #0xeab668
    // 0xeabcac: SaveReg d0
    //     0xeabcac: str             q0, [SP, #-0x10]!
    // 0xeabcb0: SaveReg r0
    //     0xeabcb0: str             x0, [SP, #-8]!
    // 0xeabcb4: r0 = AllocateDouble()
    //     0xeabcb4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabcb8: mov             x1, x0
    // 0xeabcbc: RestoreReg r0
    //     0xeabcbc: ldr             x0, [SP], #8
    // 0xeabcc0: RestoreReg d0
    //     0xeabcc0: ldr             q0, [SP], #0x10
    // 0xeabcc4: b               #0xeab768
    // 0xeabcc8: stp             q0, q1, [SP, #-0x20]!
    // 0xeabccc: r0 = AllocateDouble()
    //     0xeabccc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabcd0: ldp             q0, q1, [SP], #0x20
    // 0xeabcd4: b               #0xeab80c
    // 0xeabcd8: stp             q0, q1, [SP, #-0x20]!
    // 0xeabcdc: stp             x0, x9, [SP, #-0x10]!
    // 0xeabce0: r0 = AllocateDouble()
    //     0xeabce0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabce4: mov             x1, x0
    // 0xeabce8: ldp             x0, x9, [SP], #0x10
    // 0xeabcec: ldp             q0, q1, [SP], #0x20
    // 0xeabcf0: b               #0xeab894
    // 0xeabcf4: stp             q0, q1, [SP, #-0x20]!
    // 0xeabcf8: r0 = AllocateDouble()
    //     0xeabcf8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabcfc: ldp             q0, q1, [SP], #0x20
    // 0xeabd00: b               #0xeab938
    // 0xeabd04: stp             q0, q1, [SP, #-0x20]!
    // 0xeabd08: stp             x0, x8, [SP, #-0x10]!
    // 0xeabd0c: r0 = AllocateDouble()
    //     0xeabd0c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabd10: mov             x1, x0
    // 0xeabd14: ldp             x0, x8, [SP], #0x10
    // 0xeabd18: ldp             q0, q1, [SP], #0x20
    // 0xeabd1c: b               #0xeab9c0
    // 0xeabd20: stp             q0, q3, [SP, #-0x20]!
    // 0xeabd24: r0 = AllocateDouble()
    //     0xeabd24: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabd28: ldp             q0, q3, [SP], #0x20
    // 0xeabd2c: b               #0xeaba54
    // 0xeabd30: SaveReg d0
    //     0xeabd30: str             q0, [SP, #-0x10]!
    // 0xeabd34: SaveReg r1
    //     0xeabd34: str             x1, [SP, #-8]!
    // 0xeabd38: r0 = AllocateDouble()
    //     0xeabd38: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabd3c: RestoreReg r1
    //     0xeabd3c: ldr             x1, [SP], #8
    // 0xeabd40: RestoreReg d0
    //     0xeabd40: ldr             q0, [SP], #0x10
    // 0xeabd44: b               #0xeabb00
    // 0xeabd48: stp             q2, q3, [SP, #-0x20]!
    // 0xeabd4c: SaveReg r1
    //     0xeabd4c: str             x1, [SP, #-8]!
    // 0xeabd50: r0 = AllocateDouble()
    //     0xeabd50: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabd54: RestoreReg r1
    //     0xeabd54: ldr             x1, [SP], #8
    // 0xeabd58: ldp             q2, q3, [SP], #0x20
    // 0xeabd5c: b               #0xeabb58
    // 0xeabd60: stp             q0, q2, [SP, #-0x20]!
    // 0xeabd64: SaveReg r1
    //     0xeabd64: str             x1, [SP, #-8]!
    // 0xeabd68: r0 = AllocateDouble()
    //     0xeabd68: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeabd6c: RestoreReg r1
    //     0xeabd6c: ldr             x1, [SP], #8
    // 0xeabd70: ldp             q0, q2, [SP], #0x20
    // 0xeabd74: b               #0xeabb9c
  }
}
