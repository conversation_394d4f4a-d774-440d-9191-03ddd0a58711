// lib: , url: package:pdf/src/svg/transform.dart

// class id: 1050840, size: 0x8
class :: {
}

// class id: 837, size: 0xc, field offset: 0x8
//   const constructor, 
class SvgTransform extends Object {

  static late final RegExp _transformRegExp; // offset: 0x16f8

  factory _ SvgTransform.fromXml(/* No info */) {
    // ** addr: 0xe6e158, size: 0x48
    // 0xe6e158: EnterFrame
    //     0xe6e158: stp             fp, lr, [SP, #-0x10]!
    //     0xe6e15c: mov             fp, SP
    // 0xe6e160: mov             x0, x1
    // 0xe6e164: mov             x1, x2
    // 0xe6e168: CheckStackOverflow
    //     0xe6e168: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6e16c: cmp             SP, x16
    //     0xe6e170: b.ls            #0xe6e198
    // 0xe6e174: r2 = "transform"
    //     0xe6e174: ldr             x2, [PP, #0x71e0]  ; [pp+0x71e0] "transform"
    // 0xe6e178: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6e178: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6e17c: r0 = getAttribute()
    //     0xe6e17c: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe6e180: mov             x2, x0
    // 0xe6e184: r1 = Null
    //     0xe6e184: mov             x1, NULL
    // 0xe6e188: r0 = SvgTransform.fromString()
    //     0xe6e188: bl              #0xe6e1a0  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromString
    // 0xe6e18c: LeaveFrame
    //     0xe6e18c: mov             SP, fp
    //     0xe6e190: ldp             fp, lr, [SP], #0x10
    // 0xe6e194: ret
    //     0xe6e194: ret             
    // 0xe6e198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6e198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6e19c: b               #0xe6e174
  }
  factory _ SvgTransform.fromString(/* No info */) {
    // ** addr: 0xe6e1a0, size: 0xd78
    // 0xe6e1a0: EnterFrame
    //     0xe6e1a0: stp             fp, lr, [SP, #-0x10]!
    //     0xe6e1a4: mov             fp, SP
    // 0xe6e1a8: AllocStack(0x98)
    //     0xe6e1a8: sub             SP, SP, #0x98
    // 0xe6e1ac: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe6e1ac: stur            x2, [fp, #-8]
    // 0xe6e1b0: CheckStackOverflow
    //     0xe6e1b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6e1b4: cmp             SP, x16
    //     0xe6e1b8: b.ls            #0xe6eec0
    // 0xe6e1bc: cmp             w2, NULL
    // 0xe6e1c0: b.ne            #0xe6e1d8
    // 0xe6e1c4: r0 = Instance_SvgTransform
    //     0xe6e1c4: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3ec58] Obj!SvgTransform@e0c591
    //     0xe6e1c8: ldr             x0, [x0, #0xc58]
    // 0xe6e1cc: LeaveFrame
    //     0xe6e1cc: mov             SP, fp
    //     0xe6e1d0: ldp             fp, lr, [SP], #0x10
    // 0xe6e1d4: ret
    //     0xe6e1d4: ret             
    // 0xe6e1d8: r0 = Matrix4()
    //     0xe6e1d8: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe6e1dc: r4 = 32
    //     0xe6e1dc: movz            x4, #0x20
    // 0xe6e1e0: stur            x0, [fp, #-0x10]
    // 0xe6e1e4: r0 = AllocateFloat64Array()
    //     0xe6e1e4: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6e1e8: mov             x2, x0
    // 0xe6e1ec: ldur            x0, [fp, #-0x10]
    // 0xe6e1f0: stur            x2, [fp, #-0x18]
    // 0xe6e1f4: StoreField: r0->field_7 = r2
    //     0xe6e1f4: stur            w2, [x0, #7]
    // 0xe6e1f8: mov             x1, x0
    // 0xe6e1fc: r0 = setIdentity()
    //     0xe6e1fc: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe6e200: r0 = InitLateStaticField(0x16f8) // [package:pdf/src/svg/transform.dart] SvgTransform::_transformRegExp
    //     0xe6e200: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6e204: ldr             x0, [x0, #0x2df0]
    //     0xe6e208: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6e20c: cmp             w0, w16
    //     0xe6e210: b.ne            #0xe6e220
    //     0xe6e214: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3ec60] Field <SvgTransform._transformRegExp@2597061189>: static late final (offset: 0x16f8)
    //     0xe6e218: ldr             x2, [x2, #0xc60]
    //     0xe6e21c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6e220: mov             x1, x0
    // 0xe6e224: ldur            x2, [fp, #-8]
    // 0xe6e228: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6e228: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6e22c: r0 = allMatches()
    //     0xe6e22c: bl              #0xebde88  ; [dart:core] _RegExp::allMatches
    // 0xe6e230: LoadField: r1 = r0->field_b
    //     0xe6e230: ldur            w1, [x0, #0xb]
    // 0xe6e234: DecompressPointer r1
    //     0xe6e234: add             x1, x1, HEAP, lsl #32
    // 0xe6e238: stur            x1, [fp, #-0x20]
    // 0xe6e23c: LoadField: r2 = r0->field_f
    //     0xe6e23c: ldur            w2, [x0, #0xf]
    // 0xe6e240: DecompressPointer r2
    //     0xe6e240: add             x2, x2, HEAP, lsl #32
    // 0xe6e244: stur            x2, [fp, #-8]
    // 0xe6e248: r0 = _AllMatchesIterator()
    //     0xe6e248: bl              #0x88787c  ; Allocate_AllMatchesIteratorStub -> _AllMatchesIterator (size=0x1c)
    // 0xe6e24c: mov             x2, x0
    // 0xe6e250: ldur            x0, [fp, #-0x20]
    // 0xe6e254: stur            x2, [fp, #-0x28]
    // 0xe6e258: StoreField: r2->field_13 = r0
    //     0xe6e258: stur            w0, [x2, #0x13]
    // 0xe6e25c: ldur            x0, [fp, #-8]
    // 0xe6e260: StoreField: r2->field_7 = r0
    //     0xe6e260: stur            w0, [x2, #7]
    // 0xe6e264: StoreField: r2->field_b = rZR
    //     0xe6e264: stur            xzr, [x2, #0xb]
    // 0xe6e268: ldur            x0, [fp, #-0x18]
    // 0xe6e26c: CheckStackOverflow
    //     0xe6e26c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6e270: cmp             SP, x16
    //     0xe6e274: b.ls            #0xe6eec8
    // 0xe6e278: mov             x1, x2
    // 0xe6e27c: r0 = moveNext()
    //     0xe6e27c: bl              #0x675d28  ; [dart:core] _AllMatchesIterator::moveNext
    // 0xe6e280: tbnz            w0, #4, #0xe6ee3c
    // 0xe6e284: ldur            x3, [fp, #-0x28]
    // 0xe6e288: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xe6e288: ldur            w4, [x3, #0x17]
    // 0xe6e28c: DecompressPointer r4
    //     0xe6e28c: add             x4, x4, HEAP, lsl #32
    // 0xe6e290: stur            x4, [fp, #-8]
    // 0xe6e294: cmp             w4, NULL
    // 0xe6e298: b.ne            #0xe6e2d0
    // 0xe6e29c: mov             x0, x4
    // 0xe6e2a0: r2 = Null
    //     0xe6e2a0: mov             x2, NULL
    // 0xe6e2a4: r1 = Null
    //     0xe6e2a4: mov             x1, NULL
    // 0xe6e2a8: r4 = LoadClassIdInstr(r0)
    //     0xe6e2a8: ldur            x4, [x0, #-1]
    //     0xe6e2ac: ubfx            x4, x4, #0xc, #0x14
    // 0xe6e2b0: r17 = 7313
    //     0xe6e2b0: movz            x17, #0x1c91
    // 0xe6e2b4: cmp             x4, x17
    // 0xe6e2b8: b.eq            #0xe6e2d0
    // 0xe6e2bc: r8 = RegExpMatch
    //     0xe6e2bc: add             x8, PP, #0x10, lsl #12  ; [pp+0x108c8] Type: RegExpMatch
    //     0xe6e2c0: ldr             x8, [x8, #0x8c8]
    // 0xe6e2c4: r3 = Null
    //     0xe6e2c4: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ec68] Null
    //     0xe6e2c8: ldr             x3, [x3, #0xc68]
    // 0xe6e2cc: r0 = RegExpMatch()
    //     0xe6e2cc: bl              #0x644470  ; IsType_RegExpMatch_Stub
    // 0xe6e2d0: ldur            x1, [fp, #-8]
    // 0xe6e2d4: LoadField: r0 = r1->field_7
    //     0xe6e2d4: ldur            w0, [x1, #7]
    // 0xe6e2d8: DecompressPointer r0
    //     0xe6e2d8: add             x0, x0, HEAP, lsl #32
    // 0xe6e2dc: stur            x0, [fp, #-0x20]
    // 0xe6e2e0: str             x0, [SP]
    // 0xe6e2e4: r0 = _groupCount()
    //     0xe6e2e4: bl              #0xd5fd08  ; [dart:core] _RegExp::_groupCount
    // 0xe6e2e8: r1 = LoadInt32Instr(r0)
    //     0xe6e2e8: sbfx            x1, x0, #1, #0x1f
    //     0xe6e2ec: tbz             w0, #0, #0xe6e2f4
    //     0xe6e2f0: ldur            x1, [x0, #7]
    // 0xe6e2f4: cmp             x1, #1
    // 0xe6e2f8: b.lt            #0xe6ee8c
    // 0xe6e2fc: ldur            x1, [fp, #-8]
    // 0xe6e300: r2 = 1
    //     0xe6e300: movz            x2, #0x1
    // 0xe6e304: r0 = _start()
    //     0xe6e304: bl              #0x65cba0  ; [dart:core] _RegExpMatch::_start
    // 0xe6e308: ldur            x1, [fp, #-8]
    // 0xe6e30c: r2 = 1
    //     0xe6e30c: movz            x2, #0x1
    // 0xe6e310: stur            x0, [fp, #-0x30]
    // 0xe6e314: r0 = _end()
    //     0xe6e314: bl              #0x65cb1c  ; [dart:core] _RegExpMatch::_end
    // 0xe6e318: ldur            x2, [fp, #-0x30]
    // 0xe6e31c: cmn             x2, #1
    // 0xe6e320: b.ne            #0xe6e32c
    // 0xe6e324: r0 = Null
    //     0xe6e324: mov             x0, NULL
    // 0xe6e328: b               #0xe6e340
    // 0xe6e32c: ldur            x4, [fp, #-8]
    // 0xe6e330: LoadField: r1 = r4->field_b
    //     0xe6e330: ldur            w1, [x4, #0xb]
    // 0xe6e334: DecompressPointer r1
    //     0xe6e334: add             x1, x1, HEAP, lsl #32
    // 0xe6e338: mov             x3, x0
    // 0xe6e33c: r0 = _substringUnchecked()
    //     0xe6e33c: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xe6e340: stur            x0, [fp, #-0x38]
    // 0xe6e344: ldur            x16, [fp, #-0x20]
    // 0xe6e348: str             x16, [SP]
    // 0xe6e34c: r0 = _groupCount()
    //     0xe6e34c: bl              #0xd5fd08  ; [dart:core] _RegExp::_groupCount
    // 0xe6e350: r1 = LoadInt32Instr(r0)
    //     0xe6e350: sbfx            x1, x0, #1, #0x1f
    //     0xe6e354: tbz             w0, #0, #0xe6e35c
    //     0xe6e358: ldur            x1, [x0, #7]
    // 0xe6e35c: cmp             x1, #2
    // 0xe6e360: b.lt            #0xe6ee60
    // 0xe6e364: ldur            x1, [fp, #-8]
    // 0xe6e368: r2 = 2
    //     0xe6e368: movz            x2, #0x2
    // 0xe6e36c: r0 = _start()
    //     0xe6e36c: bl              #0x65cba0  ; [dart:core] _RegExpMatch::_start
    // 0xe6e370: ldur            x1, [fp, #-8]
    // 0xe6e374: r2 = 2
    //     0xe6e374: movz            x2, #0x2
    // 0xe6e378: stur            x0, [fp, #-0x30]
    // 0xe6e37c: r0 = _end()
    //     0xe6e37c: bl              #0x65cb1c  ; [dart:core] _RegExpMatch::_end
    // 0xe6e380: ldur            x2, [fp, #-0x30]
    // 0xe6e384: cmn             x2, #1
    // 0xe6e388: b.ne            #0xe6e394
    // 0xe6e38c: r2 = Null
    //     0xe6e38c: mov             x2, NULL
    // 0xe6e390: b               #0xe6e3b0
    // 0xe6e394: ldur            x1, [fp, #-8]
    // 0xe6e398: LoadField: r3 = r1->field_b
    //     0xe6e398: ldur            w3, [x1, #0xb]
    // 0xe6e39c: DecompressPointer r3
    //     0xe6e39c: add             x3, x3, HEAP, lsl #32
    // 0xe6e3a0: mov             x1, x3
    // 0xe6e3a4: mov             x3, x0
    // 0xe6e3a8: r0 = _substringUnchecked()
    //     0xe6e3a8: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xe6e3ac: mov             x2, x0
    // 0xe6e3b0: stur            x2, [fp, #-8]
    // 0xe6e3b4: cmp             w2, NULL
    // 0xe6e3b8: b.eq            #0xe6eed0
    // 0xe6e3bc: r0 = InitLateStaticField(0x16ec) // [package:pdf/src/svg/parser.dart] SvgParser::_transformParameterRegExp
    //     0xe6e3bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe6e3c0: ldr             x0, [x0, #0x2dd8]
    //     0xe6e3c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe6e3c8: cmp             w0, w16
    //     0xe6e3cc: b.ne            #0xe6e3dc
    //     0xe6e3d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e350] Field <SvgParser._transformParameterRegExp@2593390522>: static late final (offset: 0x16ec)
    //     0xe6e3d4: ldr             x2, [x2, #0x350]
    //     0xe6e3d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe6e3dc: mov             x1, x0
    // 0xe6e3e0: ldur            x2, [fp, #-8]
    // 0xe6e3e4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6e3e4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6e3e8: r0 = allMatches()
    //     0xe6e3e8: bl              #0xebde88  ; [dart:core] _RegExp::allMatches
    // 0xe6e3ec: r1 = Function '<anonymous closure>': static.
    //     0xe6e3ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e358] AnonymousClosure: static (0xb143c8), in [package:pdf/src/svg/parser.dart] SvgParser::splitDoubles (0xb14338)
    //     0xe6e3f0: ldr             x1, [x1, #0x358]
    // 0xe6e3f4: r2 = Null
    //     0xe6e3f4: mov             x2, NULL
    // 0xe6e3f8: stur            x0, [fp, #-8]
    // 0xe6e3fc: r0 = AllocateClosure()
    //     0xe6e3fc: bl              #0xec1630  ; AllocateClosureStub
    // 0xe6e400: r16 = <double>
    //     0xe6e400: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe6e404: ldur            lr, [fp, #-8]
    // 0xe6e408: stp             lr, x16, [SP, #8]
    // 0xe6e40c: str             x0, [SP]
    // 0xe6e410: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe6e410: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe6e414: r0 = map()
    //     0xe6e414: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xe6e418: LoadField: r1 = r0->field_7
    //     0xe6e418: ldur            w1, [x0, #7]
    // 0xe6e41c: DecompressPointer r1
    //     0xe6e41c: add             x1, x1, HEAP, lsl #32
    // 0xe6e420: mov             x2, x0
    // 0xe6e424: r0 = _GrowableList.of()
    //     0xe6e424: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe6e428: stur            x0, [fp, #-8]
    // 0xe6e42c: r16 = "matrix"
    //     0xe6e42c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] "matrix"
    //     0xe6e430: ldr             x16, [x16, #0xc78]
    // 0xe6e434: ldur            lr, [fp, #-0x38]
    // 0xe6e438: stp             lr, x16, [SP]
    // 0xe6e43c: r0 = ==()
    //     0xe6e43c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6e440: tbnz            w0, #4, #0xe6e5e0
    // 0xe6e444: ldur            x0, [fp, #-8]
    // 0xe6e448: mov             x2, x0
    // 0xe6e44c: r1 = <double>
    //     0xe6e44c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe6e450: r0 = _GrowableList._ofGrowableList()
    //     0xe6e450: bl              #0x60bbec  ; [dart:core] _GrowableList::_GrowableList._ofGrowableList
    // 0xe6e454: ldur            x2, [fp, #-8]
    // 0xe6e458: stur            x0, [fp, #-0x20]
    // 0xe6e45c: LoadField: r1 = r2->field_b
    //     0xe6e45c: ldur            w1, [x2, #0xb]
    // 0xe6e460: r2 = LoadInt32Instr(r1)
    //     0xe6e460: sbfx            x2, x1, #1, #0x1f
    // 0xe6e464: r3 = 6
    //     0xe6e464: movz            x3, #0x6
    // 0xe6e468: sub             x4, x3, x2
    // 0xe6e46c: stur            x4, [fp, #-0x30]
    // 0xe6e470: lsl             x2, x4, #1
    // 0xe6e474: r1 = <double>
    //     0xe6e474: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe6e478: r0 = AllocateArray()
    //     0xe6e478: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6e47c: ldur            x1, [fp, #-0x30]
    // 0xe6e480: r2 = 0
    //     0xe6e480: movz            x2, #0
    // 0xe6e484: CheckStackOverflow
    //     0xe6e484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6e488: cmp             SP, x16
    //     0xe6e48c: b.ls            #0xe6eed4
    // 0xe6e490: cmp             x2, x1
    // 0xe6e494: b.ge            #0xe6e4b0
    // 0xe6e498: add             x3, x0, x2, lsl #2
    // 0xe6e49c: r16 = 0.000000
    //     0xe6e49c: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe6e4a0: StoreField: r3->field_f = r16
    //     0xe6e4a0: stur            w16, [x3, #0xf]
    // 0xe6e4a4: add             x3, x2, #1
    // 0xe6e4a8: mov             x2, x3
    // 0xe6e4ac: b               #0xe6e484
    // 0xe6e4b0: ldur            x3, [fp, #-0x20]
    // 0xe6e4b4: mov             x1, x3
    // 0xe6e4b8: mov             x2, x0
    // 0xe6e4bc: r0 = addAll()
    //     0xe6e4bc: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xe6e4c0: ldur            x2, [fp, #-0x20]
    // 0xe6e4c4: LoadField: r0 = r2->field_b
    //     0xe6e4c4: ldur            w0, [x2, #0xb]
    // 0xe6e4c8: r3 = LoadInt32Instr(r0)
    //     0xe6e4c8: sbfx            x3, x0, #1, #0x1f
    // 0xe6e4cc: mov             x0, x3
    // 0xe6e4d0: r1 = 0
    //     0xe6e4d0: movz            x1, #0
    // 0xe6e4d4: cmp             x1, x0
    // 0xe6e4d8: b.hs            #0xe6eedc
    // 0xe6e4dc: LoadField: r4 = r2->field_f
    //     0xe6e4dc: ldur            w4, [x2, #0xf]
    // 0xe6e4e0: DecompressPointer r4
    //     0xe6e4e0: add             x4, x4, HEAP, lsl #32
    // 0xe6e4e4: LoadField: r2 = r4->field_f
    //     0xe6e4e4: ldur            w2, [x4, #0xf]
    // 0xe6e4e8: DecompressPointer r2
    //     0xe6e4e8: add             x2, x2, HEAP, lsl #32
    // 0xe6e4ec: mov             x0, x3
    // 0xe6e4f0: stur            x2, [fp, #-0x60]
    // 0xe6e4f4: r1 = 1
    //     0xe6e4f4: movz            x1, #0x1
    // 0xe6e4f8: cmp             x1, x0
    // 0xe6e4fc: b.hs            #0xe6eee0
    // 0xe6e500: LoadField: r5 = r4->field_13
    //     0xe6e500: ldur            w5, [x4, #0x13]
    // 0xe6e504: DecompressPointer r5
    //     0xe6e504: add             x5, x5, HEAP, lsl #32
    // 0xe6e508: mov             x0, x3
    // 0xe6e50c: stur            x5, [fp, #-0x58]
    // 0xe6e510: r1 = 2
    //     0xe6e510: movz            x1, #0x2
    // 0xe6e514: cmp             x1, x0
    // 0xe6e518: b.hs            #0xe6eee4
    // 0xe6e51c: ArrayLoad: r6 = r4[0]  ; List_4
    //     0xe6e51c: ldur            w6, [x4, #0x17]
    // 0xe6e520: DecompressPointer r6
    //     0xe6e520: add             x6, x6, HEAP, lsl #32
    // 0xe6e524: mov             x0, x3
    // 0xe6e528: stur            x6, [fp, #-0x50]
    // 0xe6e52c: r1 = 3
    //     0xe6e52c: movz            x1, #0x3
    // 0xe6e530: cmp             x1, x0
    // 0xe6e534: b.hs            #0xe6eee8
    // 0xe6e538: LoadField: r7 = r4->field_1b
    //     0xe6e538: ldur            w7, [x4, #0x1b]
    // 0xe6e53c: DecompressPointer r7
    //     0xe6e53c: add             x7, x7, HEAP, lsl #32
    // 0xe6e540: mov             x0, x3
    // 0xe6e544: stur            x7, [fp, #-0x48]
    // 0xe6e548: r1 = 4
    //     0xe6e548: movz            x1, #0x4
    // 0xe6e54c: cmp             x1, x0
    // 0xe6e550: b.hs            #0xe6eeec
    // 0xe6e554: LoadField: r8 = r4->field_1f
    //     0xe6e554: ldur            w8, [x4, #0x1f]
    // 0xe6e558: DecompressPointer r8
    //     0xe6e558: add             x8, x8, HEAP, lsl #32
    // 0xe6e55c: mov             x0, x3
    // 0xe6e560: stur            x8, [fp, #-0x40]
    // 0xe6e564: r1 = 5
    //     0xe6e564: movz            x1, #0x5
    // 0xe6e568: cmp             x1, x0
    // 0xe6e56c: b.hs            #0xe6eef0
    // 0xe6e570: LoadField: r0 = r4->field_23
    //     0xe6e570: ldur            w0, [x4, #0x23]
    // 0xe6e574: DecompressPointer r0
    //     0xe6e574: add             x0, x0, HEAP, lsl #32
    // 0xe6e578: stur            x0, [fp, #-0x20]
    // 0xe6e57c: r0 = Matrix4()
    //     0xe6e57c: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe6e580: r4 = 32
    //     0xe6e580: movz            x4, #0x20
    // 0xe6e584: stur            x0, [fp, #-0x68]
    // 0xe6e588: r0 = AllocateFloat64Array()
    //     0xe6e588: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6e58c: mov             x1, x0
    // 0xe6e590: ldur            x0, [fp, #-0x68]
    // 0xe6e594: StoreField: r0->field_7 = r1
    //     0xe6e594: stur            w1, [x0, #7]
    // 0xe6e598: ldur            x1, [fp, #-0x60]
    // 0xe6e59c: LoadField: d0 = r1->field_7
    //     0xe6e59c: ldur            d0, [x1, #7]
    // 0xe6e5a0: ldur            x1, [fp, #-0x58]
    // 0xe6e5a4: LoadField: d1 = r1->field_7
    //     0xe6e5a4: ldur            d1, [x1, #7]
    // 0xe6e5a8: ldur            x1, [fp, #-0x50]
    // 0xe6e5ac: LoadField: d2 = r1->field_7
    //     0xe6e5ac: ldur            d2, [x1, #7]
    // 0xe6e5b0: ldur            x1, [fp, #-0x48]
    // 0xe6e5b4: LoadField: d3 = r1->field_7
    //     0xe6e5b4: ldur            d3, [x1, #7]
    // 0xe6e5b8: ldur            x1, [fp, #-0x40]
    // 0xe6e5bc: LoadField: d4 = r1->field_7
    //     0xe6e5bc: ldur            d4, [x1, #7]
    // 0xe6e5c0: ldur            x1, [fp, #-0x20]
    // 0xe6e5c4: LoadField: d5 = r1->field_7
    //     0xe6e5c4: ldur            d5, [x1, #7]
    // 0xe6e5c8: mov             x1, x0
    // 0xe6e5cc: r0 = setValues()
    //     0xe6e5cc: bl              #0xabd40c  ; [package:vector_math/vector_math_64.dart] Matrix4::setValues
    // 0xe6e5d0: ldur            x1, [fp, #-0x10]
    // 0xe6e5d4: ldur            x2, [fp, #-0x68]
    // 0xe6e5d8: r0 = multiply()
    //     0xe6e5d8: bl              #0x680524  ; [package:vector_math/vector_math_64.dart] Matrix4::multiply
    // 0xe6e5dc: b               #0xe6ee34
    // 0xe6e5e0: ldur            x2, [fp, #-8]
    // 0xe6e5e4: r16 = "translate"
    //     0xe6e5e4: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e40] "translate"
    //     0xe6e5e8: ldr             x16, [x16, #0xe40]
    // 0xe6e5ec: ldur            lr, [fp, #-0x38]
    // 0xe6e5f0: stp             lr, x16, [SP]
    // 0xe6e5f4: r0 = ==()
    //     0xe6e5f4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6e5f8: tbnz            w0, #4, #0xe6e7ac
    // 0xe6e5fc: ldur            x2, [fp, #-8]
    // 0xe6e600: LoadField: r0 = r2->field_b
    //     0xe6e600: ldur            w0, [x2, #0xb]
    // 0xe6e604: r1 = LoadInt32Instr(r0)
    //     0xe6e604: sbfx            x1, x0, #1, #0x1f
    // 0xe6e608: mov             x0, x1
    // 0xe6e60c: r1 = 0
    //     0xe6e60c: movz            x1, #0
    // 0xe6e610: cmp             x1, x0
    // 0xe6e614: b.hs            #0xe6eef4
    // 0xe6e618: LoadField: r0 = r2->field_f
    //     0xe6e618: ldur            w0, [x2, #0xf]
    // 0xe6e61c: DecompressPointer r0
    //     0xe6e61c: add             x0, x0, HEAP, lsl #32
    // 0xe6e620: LoadField: r3 = r0->field_f
    //     0xe6e620: ldur            w3, [x0, #0xf]
    // 0xe6e624: DecompressPointer r3
    //     0xe6e624: add             x3, x3, HEAP, lsl #32
    // 0xe6e628: stur            x3, [fp, #-0x20]
    // 0xe6e62c: r1 = <double>
    //     0xe6e62c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe6e630: r0 = _GrowableList._ofGrowableList()
    //     0xe6e630: bl              #0x60bbec  ; [dart:core] _GrowableList::_GrowableList._ofGrowableList
    // 0xe6e634: stur            x0, [fp, #-0x40]
    // 0xe6e638: LoadField: r1 = r0->field_b
    //     0xe6e638: ldur            w1, [x0, #0xb]
    // 0xe6e63c: LoadField: r2 = r0->field_f
    //     0xe6e63c: ldur            w2, [x0, #0xf]
    // 0xe6e640: DecompressPointer r2
    //     0xe6e640: add             x2, x2, HEAP, lsl #32
    // 0xe6e644: LoadField: r3 = r2->field_b
    //     0xe6e644: ldur            w3, [x2, #0xb]
    // 0xe6e648: r2 = LoadInt32Instr(r1)
    //     0xe6e648: sbfx            x2, x1, #1, #0x1f
    // 0xe6e64c: stur            x2, [fp, #-0x30]
    // 0xe6e650: r1 = LoadInt32Instr(r3)
    //     0xe6e650: sbfx            x1, x3, #1, #0x1f
    // 0xe6e654: cmp             x2, x1
    // 0xe6e658: b.ne            #0xe6e664
    // 0xe6e65c: mov             x1, x0
    // 0xe6e660: r0 = _growToNextCapacity()
    //     0xe6e660: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe6e664: ldur            x0, [fp, #-0x40]
    // 0xe6e668: ldur            x1, [fp, #-0x30]
    // 0xe6e66c: ldur            x2, [fp, #-0x20]
    // 0xe6e670: add             x3, x1, #1
    // 0xe6e674: lsl             x4, x3, #1
    // 0xe6e678: StoreField: r0->field_b = r4
    //     0xe6e678: stur            w4, [x0, #0xb]
    // 0xe6e67c: LoadField: r4 = r0->field_f
    //     0xe6e67c: ldur            w4, [x0, #0xf]
    // 0xe6e680: DecompressPointer r4
    //     0xe6e680: add             x4, x4, HEAP, lsl #32
    // 0xe6e684: add             x0, x4, x1, lsl #2
    // 0xe6e688: r16 = 0.000000
    //     0xe6e688: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe6e68c: StoreField: r0->field_f = r16
    //     0xe6e68c: stur            w16, [x0, #0xf]
    // 0xe6e690: mov             x0, x3
    // 0xe6e694: r1 = 1
    //     0xe6e694: movz            x1, #0x1
    // 0xe6e698: cmp             x1, x0
    // 0xe6e69c: b.hs            #0xe6eef8
    // 0xe6e6a0: LoadField: r0 = r4->field_13
    //     0xe6e6a0: ldur            w0, [x4, #0x13]
    // 0xe6e6a4: DecompressPointer r0
    //     0xe6e6a4: add             x0, x0, HEAP, lsl #32
    // 0xe6e6a8: stur            x0, [fp, #-0x40]
    // 0xe6e6ac: r0 = Matrix4()
    //     0xe6e6ac: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe6e6b0: r4 = 32
    //     0xe6e6b0: movz            x4, #0x20
    // 0xe6e6b4: stur            x0, [fp, #-0x48]
    // 0xe6e6b8: r0 = AllocateFloat64Array()
    //     0xe6e6b8: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6e6bc: mov             x2, x0
    // 0xe6e6c0: ldur            x0, [fp, #-0x48]
    // 0xe6e6c4: stur            x2, [fp, #-0x50]
    // 0xe6e6c8: StoreField: r0->field_7 = r2
    //     0xe6e6c8: stur            w2, [x0, #7]
    // 0xe6e6cc: mov             x1, x0
    // 0xe6e6d0: r0 = setIdentity()
    //     0xe6e6d0: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe6e6d4: ldur            x0, [fp, #-0x50]
    // 0xe6e6d8: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe6e6d8: ldur            d0, [x0, #0x17]
    // 0xe6e6dc: ldur            x1, [fp, #-0x20]
    // 0xe6e6e0: LoadField: d1 = r1->field_7
    //     0xe6e6e0: ldur            d1, [x1, #7]
    // 0xe6e6e4: fmul            d2, d0, d1
    // 0xe6e6e8: LoadField: d0 = r0->field_37
    //     0xe6e6e8: ldur            d0, [x0, #0x37]
    // 0xe6e6ec: ldur            x1, [fp, #-0x40]
    // 0xe6e6f0: LoadField: d3 = r1->field_7
    //     0xe6e6f0: ldur            d3, [x1, #7]
    // 0xe6e6f4: fmul            d4, d0, d3
    // 0xe6e6f8: fadd            d0, d2, d4
    // 0xe6e6fc: LoadField: d2 = r0->field_57
    //     0xe6e6fc: ldur            d2, [x0, #0x57]
    // 0xe6e700: d4 = 0.000000
    //     0xe6e700: eor             v4.16b, v4.16b, v4.16b
    // 0xe6e704: fmul            d5, d2, d4
    // 0xe6e708: fadd            d2, d0, d5
    // 0xe6e70c: LoadField: d0 = r0->field_77
    //     0xe6e70c: ldur            d0, [x0, #0x77]
    // 0xe6e710: fadd            d5, d2, d0
    // 0xe6e714: LoadField: d0 = r0->field_1f
    //     0xe6e714: ldur            d0, [x0, #0x1f]
    // 0xe6e718: fmul            d2, d0, d1
    // 0xe6e71c: LoadField: d0 = r0->field_3f
    //     0xe6e71c: ldur            d0, [x0, #0x3f]
    // 0xe6e720: fmul            d6, d0, d3
    // 0xe6e724: fadd            d0, d2, d6
    // 0xe6e728: LoadField: d2 = r0->field_5f
    //     0xe6e728: ldur            d2, [x0, #0x5f]
    // 0xe6e72c: fmul            d6, d2, d4
    // 0xe6e730: fadd            d2, d0, d6
    // 0xe6e734: LoadField: d0 = r0->field_7f
    //     0xe6e734: ldur            d0, [x0, #0x7f]
    // 0xe6e738: fadd            d6, d2, d0
    // 0xe6e73c: LoadField: d0 = r0->field_27
    //     0xe6e73c: ldur            d0, [x0, #0x27]
    // 0xe6e740: fmul            d2, d0, d1
    // 0xe6e744: LoadField: d0 = r0->field_47
    //     0xe6e744: ldur            d0, [x0, #0x47]
    // 0xe6e748: fmul            d7, d0, d3
    // 0xe6e74c: fadd            d0, d2, d7
    // 0xe6e750: LoadField: d2 = r0->field_67
    //     0xe6e750: ldur            d2, [x0, #0x67]
    // 0xe6e754: fmul            d7, d2, d4
    // 0xe6e758: fadd            d2, d0, d7
    // 0xe6e75c: LoadField: d0 = r0->field_87
    //     0xe6e75c: ldur            d0, [x0, #0x87]
    // 0xe6e760: fadd            d7, d2, d0
    // 0xe6e764: LoadField: d0 = r0->field_2f
    //     0xe6e764: ldur            d0, [x0, #0x2f]
    // 0xe6e768: fmul            d2, d0, d1
    // 0xe6e76c: LoadField: d0 = r0->field_4f
    //     0xe6e76c: ldur            d0, [x0, #0x4f]
    // 0xe6e770: fmul            d1, d0, d3
    // 0xe6e774: fadd            d0, d2, d1
    // 0xe6e778: LoadField: d1 = r0->field_6f
    //     0xe6e778: ldur            d1, [x0, #0x6f]
    // 0xe6e77c: fmul            d2, d1, d4
    // 0xe6e780: fadd            d1, d0, d2
    // 0xe6e784: LoadField: d0 = r0->field_8f
    //     0xe6e784: ldur            d0, [x0, #0x8f]
    // 0xe6e788: fadd            d2, d1, d0
    // 0xe6e78c: StoreField: r0->field_77 = d5
    //     0xe6e78c: stur            d5, [x0, #0x77]
    // 0xe6e790: StoreField: r0->field_7f = d6
    //     0xe6e790: stur            d6, [x0, #0x7f]
    // 0xe6e794: StoreField: r0->field_87 = d7
    //     0xe6e794: stur            d7, [x0, #0x87]
    // 0xe6e798: StoreField: r0->field_8f = d2
    //     0xe6e798: stur            d2, [x0, #0x8f]
    // 0xe6e79c: ldur            x1, [fp, #-0x10]
    // 0xe6e7a0: ldur            x2, [fp, #-0x48]
    // 0xe6e7a4: r0 = multiply()
    //     0xe6e7a4: bl              #0x680524  ; [package:vector_math/vector_math_64.dart] Matrix4::multiply
    // 0xe6e7a8: b               #0xe6ee34
    // 0xe6e7ac: ldur            x2, [fp, #-8]
    // 0xe6e7b0: r16 = "scale"
    //     0xe6e7b0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec80] "scale"
    //     0xe6e7b4: ldr             x16, [x16, #0xc80]
    // 0xe6e7b8: ldur            lr, [fp, #-0x38]
    // 0xe6e7bc: stp             lr, x16, [SP]
    // 0xe6e7c0: r0 = ==()
    //     0xe6e7c0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6e7c4: tbnz            w0, #4, #0xe6e978
    // 0xe6e7c8: ldur            x2, [fp, #-8]
    // 0xe6e7cc: LoadField: r0 = r2->field_b
    //     0xe6e7cc: ldur            w0, [x2, #0xb]
    // 0xe6e7d0: r1 = LoadInt32Instr(r0)
    //     0xe6e7d0: sbfx            x1, x0, #1, #0x1f
    // 0xe6e7d4: mov             x0, x1
    // 0xe6e7d8: r1 = 0
    //     0xe6e7d8: movz            x1, #0
    // 0xe6e7dc: cmp             x1, x0
    // 0xe6e7e0: b.hs            #0xe6eefc
    // 0xe6e7e4: LoadField: r0 = r2->field_f
    //     0xe6e7e4: ldur            w0, [x2, #0xf]
    // 0xe6e7e8: DecompressPointer r0
    //     0xe6e7e8: add             x0, x0, HEAP, lsl #32
    // 0xe6e7ec: LoadField: r3 = r0->field_f
    //     0xe6e7ec: ldur            w3, [x0, #0xf]
    // 0xe6e7f0: DecompressPointer r3
    //     0xe6e7f0: add             x3, x3, HEAP, lsl #32
    // 0xe6e7f4: stur            x3, [fp, #-0x20]
    // 0xe6e7f8: r1 = <double>
    //     0xe6e7f8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe6e7fc: r0 = _GrowableList._ofGrowableList()
    //     0xe6e7fc: bl              #0x60bbec  ; [dart:core] _GrowableList::_GrowableList._ofGrowableList
    // 0xe6e800: stur            x0, [fp, #-0x40]
    // 0xe6e804: LoadField: r1 = r0->field_b
    //     0xe6e804: ldur            w1, [x0, #0xb]
    // 0xe6e808: LoadField: r2 = r0->field_f
    //     0xe6e808: ldur            w2, [x0, #0xf]
    // 0xe6e80c: DecompressPointer r2
    //     0xe6e80c: add             x2, x2, HEAP, lsl #32
    // 0xe6e810: LoadField: r3 = r2->field_b
    //     0xe6e810: ldur            w3, [x2, #0xb]
    // 0xe6e814: r2 = LoadInt32Instr(r1)
    //     0xe6e814: sbfx            x2, x1, #1, #0x1f
    // 0xe6e818: stur            x2, [fp, #-0x30]
    // 0xe6e81c: r1 = LoadInt32Instr(r3)
    //     0xe6e81c: sbfx            x1, x3, #1, #0x1f
    // 0xe6e820: cmp             x2, x1
    // 0xe6e824: b.ne            #0xe6e830
    // 0xe6e828: mov             x1, x0
    // 0xe6e82c: r0 = _growToNextCapacity()
    //     0xe6e82c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe6e830: ldur            x0, [fp, #-0x40]
    // 0xe6e834: ldur            x2, [fp, #-0x30]
    // 0xe6e838: ldur            x3, [fp, #-0x20]
    // 0xe6e83c: add             x4, x2, #1
    // 0xe6e840: lsl             x1, x4, #1
    // 0xe6e844: StoreField: r0->field_b = r1
    //     0xe6e844: stur            w1, [x0, #0xb]
    // 0xe6e848: LoadField: r5 = r0->field_f
    //     0xe6e848: ldur            w5, [x0, #0xf]
    // 0xe6e84c: DecompressPointer r5
    //     0xe6e84c: add             x5, x5, HEAP, lsl #32
    // 0xe6e850: mov             x1, x5
    // 0xe6e854: mov             x0, x3
    // 0xe6e858: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe6e858: add             x25, x1, x2, lsl #2
    //     0xe6e85c: add             x25, x25, #0xf
    //     0xe6e860: str             w0, [x25]
    //     0xe6e864: tbz             w0, #0, #0xe6e880
    //     0xe6e868: ldurb           w16, [x1, #-1]
    //     0xe6e86c: ldurb           w17, [x0, #-1]
    //     0xe6e870: and             x16, x17, x16, lsr #2
    //     0xe6e874: tst             x16, HEAP, lsr #32
    //     0xe6e878: b.eq            #0xe6e880
    //     0xe6e87c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe6e880: mov             x0, x4
    // 0xe6e884: r1 = 1
    //     0xe6e884: movz            x1, #0x1
    // 0xe6e888: cmp             x1, x0
    // 0xe6e88c: b.hs            #0xe6ef00
    // 0xe6e890: LoadField: r0 = r5->field_13
    //     0xe6e890: ldur            w0, [x5, #0x13]
    // 0xe6e894: DecompressPointer r0
    //     0xe6e894: add             x0, x0, HEAP, lsl #32
    // 0xe6e898: stur            x0, [fp, #-0x40]
    // 0xe6e89c: r0 = Matrix4()
    //     0xe6e89c: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe6e8a0: r4 = 32
    //     0xe6e8a0: movz            x4, #0x20
    // 0xe6e8a4: stur            x0, [fp, #-0x48]
    // 0xe6e8a8: r0 = AllocateFloat64Array()
    //     0xe6e8a8: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6e8ac: mov             x2, x0
    // 0xe6e8b0: ldur            x0, [fp, #-0x48]
    // 0xe6e8b4: stur            x2, [fp, #-0x50]
    // 0xe6e8b8: StoreField: r0->field_7 = r2
    //     0xe6e8b8: stur            w2, [x0, #7]
    // 0xe6e8bc: mov             x1, x0
    // 0xe6e8c0: r0 = setIdentity()
    //     0xe6e8c0: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe6e8c4: ldur            x0, [fp, #-0x50]
    // 0xe6e8c8: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe6e8c8: ldur            d0, [x0, #0x17]
    // 0xe6e8cc: ldur            x1, [fp, #-0x20]
    // 0xe6e8d0: LoadField: d1 = r1->field_7
    //     0xe6e8d0: ldur            d1, [x1, #7]
    // 0xe6e8d4: fmul            d2, d0, d1
    // 0xe6e8d8: ArrayStore: r0[0] = d2  ; List_8
    //     0xe6e8d8: stur            d2, [x0, #0x17]
    // 0xe6e8dc: LoadField: d0 = r0->field_1f
    //     0xe6e8dc: ldur            d0, [x0, #0x1f]
    // 0xe6e8e0: fmul            d2, d0, d1
    // 0xe6e8e4: StoreField: r0->field_1f = d2
    //     0xe6e8e4: stur            d2, [x0, #0x1f]
    // 0xe6e8e8: LoadField: d0 = r0->field_27
    //     0xe6e8e8: ldur            d0, [x0, #0x27]
    // 0xe6e8ec: fmul            d2, d0, d1
    // 0xe6e8f0: StoreField: r0->field_27 = d2
    //     0xe6e8f0: stur            d2, [x0, #0x27]
    // 0xe6e8f4: LoadField: d0 = r0->field_2f
    //     0xe6e8f4: ldur            d0, [x0, #0x2f]
    // 0xe6e8f8: fmul            d2, d0, d1
    // 0xe6e8fc: StoreField: r0->field_2f = d2
    //     0xe6e8fc: stur            d2, [x0, #0x2f]
    // 0xe6e900: LoadField: d0 = r0->field_37
    //     0xe6e900: ldur            d0, [x0, #0x37]
    // 0xe6e904: ldur            x1, [fp, #-0x40]
    // 0xe6e908: LoadField: d2 = r1->field_7
    //     0xe6e908: ldur            d2, [x1, #7]
    // 0xe6e90c: fmul            d3, d0, d2
    // 0xe6e910: StoreField: r0->field_37 = d3
    //     0xe6e910: stur            d3, [x0, #0x37]
    // 0xe6e914: LoadField: d0 = r0->field_3f
    //     0xe6e914: ldur            d0, [x0, #0x3f]
    // 0xe6e918: fmul            d3, d0, d2
    // 0xe6e91c: StoreField: r0->field_3f = d3
    //     0xe6e91c: stur            d3, [x0, #0x3f]
    // 0xe6e920: LoadField: d0 = r0->field_47
    //     0xe6e920: ldur            d0, [x0, #0x47]
    // 0xe6e924: fmul            d3, d0, d2
    // 0xe6e928: StoreField: r0->field_47 = d3
    //     0xe6e928: stur            d3, [x0, #0x47]
    // 0xe6e92c: LoadField: d0 = r0->field_4f
    //     0xe6e92c: ldur            d0, [x0, #0x4f]
    // 0xe6e930: fmul            d3, d0, d2
    // 0xe6e934: StoreField: r0->field_4f = d3
    //     0xe6e934: stur            d3, [x0, #0x4f]
    // 0xe6e938: LoadField: d0 = r0->field_57
    //     0xe6e938: ldur            d0, [x0, #0x57]
    // 0xe6e93c: fmul            d2, d0, d1
    // 0xe6e940: StoreField: r0->field_57 = d2
    //     0xe6e940: stur            d2, [x0, #0x57]
    // 0xe6e944: LoadField: d0 = r0->field_5f
    //     0xe6e944: ldur            d0, [x0, #0x5f]
    // 0xe6e948: fmul            d2, d0, d1
    // 0xe6e94c: StoreField: r0->field_5f = d2
    //     0xe6e94c: stur            d2, [x0, #0x5f]
    // 0xe6e950: LoadField: d0 = r0->field_67
    //     0xe6e950: ldur            d0, [x0, #0x67]
    // 0xe6e954: fmul            d2, d0, d1
    // 0xe6e958: StoreField: r0->field_67 = d2
    //     0xe6e958: stur            d2, [x0, #0x67]
    // 0xe6e95c: LoadField: d0 = r0->field_6f
    //     0xe6e95c: ldur            d0, [x0, #0x6f]
    // 0xe6e960: fmul            d2, d0, d1
    // 0xe6e964: StoreField: r0->field_6f = d2
    //     0xe6e964: stur            d2, [x0, #0x6f]
    // 0xe6e968: ldur            x1, [fp, #-0x10]
    // 0xe6e96c: ldur            x2, [fp, #-0x48]
    // 0xe6e970: r0 = multiply()
    //     0xe6e970: bl              #0x680524  ; [package:vector_math/vector_math_64.dart] Matrix4::multiply
    // 0xe6e974: b               #0xe6ee34
    // 0xe6e978: ldur            x2, [fp, #-8]
    // 0xe6e97c: r16 = "rotate"
    //     0xe6e97c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec88] "rotate"
    //     0xe6e980: ldr             x16, [x16, #0xc88]
    // 0xe6e984: ldur            lr, [fp, #-0x38]
    // 0xe6e988: stp             lr, x16, [SP]
    // 0xe6e98c: r0 = ==()
    //     0xe6e98c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6e990: tbnz            w0, #4, #0xe6ec88
    // 0xe6e994: ldur            x2, [fp, #-8]
    // 0xe6e998: LoadField: r0 = r2->field_b
    //     0xe6e998: ldur            w0, [x2, #0xb]
    // 0xe6e99c: r3 = LoadInt32Instr(r0)
    //     0xe6e99c: sbfx            x3, x0, #1, #0x1f
    // 0xe6e9a0: mov             x0, x3
    // 0xe6e9a4: r1 = 0
    //     0xe6e9a4: movz            x1, #0
    // 0xe6e9a8: cmp             x1, x0
    // 0xe6e9ac: b.hs            #0xe6ef04
    // 0xe6e9b0: LoadField: r4 = r2->field_f
    //     0xe6e9b0: ldur            w4, [x2, #0xf]
    // 0xe6e9b4: DecompressPointer r4
    //     0xe6e9b4: add             x4, x4, HEAP, lsl #32
    // 0xe6e9b8: LoadField: r5 = r4->field_f
    //     0xe6e9b8: ldur            w5, [x4, #0xf]
    // 0xe6e9bc: DecompressPointer r5
    //     0xe6e9bc: add             x5, x5, HEAP, lsl #32
    // 0xe6e9c0: stur            x5, [fp, #-0x40]
    // 0xe6e9c4: cmp             x3, #1
    // 0xe6e9c8: b.le            #0xe6eb30
    // 0xe6e9cc: mov             x0, x3
    // 0xe6e9d0: r1 = 1
    //     0xe6e9d0: movz            x1, #0x1
    // 0xe6e9d4: cmp             x1, x0
    // 0xe6e9d8: b.hs            #0xe6ef08
    // 0xe6e9dc: LoadField: r0 = r4->field_13
    //     0xe6e9dc: ldur            w0, [x4, #0x13]
    // 0xe6e9e0: DecompressPointer r0
    //     0xe6e9e0: add             x0, x0, HEAP, lsl #32
    // 0xe6e9e4: stur            x0, [fp, #-0x20]
    // 0xe6e9e8: r1 = <double>
    //     0xe6e9e8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe6e9ec: r0 = _GrowableList._ofGrowableList()
    //     0xe6e9ec: bl              #0x60bbec  ; [dart:core] _GrowableList::_GrowableList._ofGrowableList
    // 0xe6e9f0: stur            x0, [fp, #-0x48]
    // 0xe6e9f4: LoadField: r1 = r0->field_b
    //     0xe6e9f4: ldur            w1, [x0, #0xb]
    // 0xe6e9f8: LoadField: r2 = r0->field_f
    //     0xe6e9f8: ldur            w2, [x0, #0xf]
    // 0xe6e9fc: DecompressPointer r2
    //     0xe6e9fc: add             x2, x2, HEAP, lsl #32
    // 0xe6ea00: LoadField: r3 = r2->field_b
    //     0xe6ea00: ldur            w3, [x2, #0xb]
    // 0xe6ea04: r2 = LoadInt32Instr(r1)
    //     0xe6ea04: sbfx            x2, x1, #1, #0x1f
    // 0xe6ea08: stur            x2, [fp, #-0x30]
    // 0xe6ea0c: r1 = LoadInt32Instr(r3)
    //     0xe6ea0c: sbfx            x1, x3, #1, #0x1f
    // 0xe6ea10: cmp             x2, x1
    // 0xe6ea14: b.ne            #0xe6ea20
    // 0xe6ea18: mov             x1, x0
    // 0xe6ea1c: r0 = _growToNextCapacity()
    //     0xe6ea1c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe6ea20: ldur            x0, [fp, #-0x48]
    // 0xe6ea24: ldur            x3, [fp, #-0x18]
    // 0xe6ea28: ldur            x1, [fp, #-0x30]
    // 0xe6ea2c: ldur            x2, [fp, #-0x20]
    // 0xe6ea30: d0 = 0.000000
    //     0xe6ea30: eor             v0.16b, v0.16b, v0.16b
    // 0xe6ea34: add             x4, x1, #1
    // 0xe6ea38: lsl             x5, x4, #1
    // 0xe6ea3c: StoreField: r0->field_b = r5
    //     0xe6ea3c: stur            w5, [x0, #0xb]
    // 0xe6ea40: LoadField: r5 = r0->field_f
    //     0xe6ea40: ldur            w5, [x0, #0xf]
    // 0xe6ea44: DecompressPointer r5
    //     0xe6ea44: add             x5, x5, HEAP, lsl #32
    // 0xe6ea48: add             x0, x5, x1, lsl #2
    // 0xe6ea4c: r16 = 0.000000
    //     0xe6ea4c: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe6ea50: StoreField: r0->field_f = r16
    //     0xe6ea50: stur            w16, [x0, #0xf]
    // 0xe6ea54: mov             x0, x4
    // 0xe6ea58: r1 = 2
    //     0xe6ea58: movz            x1, #0x2
    // 0xe6ea5c: cmp             x1, x0
    // 0xe6ea60: b.hs            #0xe6ef0c
    // 0xe6ea64: ArrayLoad: r0 = r5[0]  ; List_4
    //     0xe6ea64: ldur            w0, [x5, #0x17]
    // 0xe6ea68: DecompressPointer r0
    //     0xe6ea68: add             x0, x0, HEAP, lsl #32
    // 0xe6ea6c: ArrayLoad: d1 = r3[0]  ; List_8
    //     0xe6ea6c: ldur            d1, [x3, #0x17]
    // 0xe6ea70: LoadField: d2 = r2->field_7
    //     0xe6ea70: ldur            d2, [x2, #7]
    // 0xe6ea74: fmul            d3, d1, d2
    // 0xe6ea78: LoadField: d1 = r3->field_37
    //     0xe6ea78: ldur            d1, [x3, #0x37]
    // 0xe6ea7c: LoadField: d4 = r0->field_7
    //     0xe6ea7c: ldur            d4, [x0, #7]
    // 0xe6ea80: fmul            d5, d1, d4
    // 0xe6ea84: fadd            d1, d3, d5
    // 0xe6ea88: LoadField: d3 = r3->field_57
    //     0xe6ea88: ldur            d3, [x3, #0x57]
    // 0xe6ea8c: fmul            d5, d3, d0
    // 0xe6ea90: fadd            d3, d1, d5
    // 0xe6ea94: LoadField: d1 = r3->field_77
    //     0xe6ea94: ldur            d1, [x3, #0x77]
    // 0xe6ea98: fadd            d5, d3, d1
    // 0xe6ea9c: LoadField: d1 = r3->field_1f
    //     0xe6ea9c: ldur            d1, [x3, #0x1f]
    // 0xe6eaa0: fmul            d3, d1, d2
    // 0xe6eaa4: LoadField: d1 = r3->field_3f
    //     0xe6eaa4: ldur            d1, [x3, #0x3f]
    // 0xe6eaa8: fmul            d6, d1, d4
    // 0xe6eaac: fadd            d1, d3, d6
    // 0xe6eab0: LoadField: d3 = r3->field_5f
    //     0xe6eab0: ldur            d3, [x3, #0x5f]
    // 0xe6eab4: fmul            d6, d3, d0
    // 0xe6eab8: fadd            d3, d1, d6
    // 0xe6eabc: LoadField: d1 = r3->field_7f
    //     0xe6eabc: ldur            d1, [x3, #0x7f]
    // 0xe6eac0: fadd            d6, d3, d1
    // 0xe6eac4: LoadField: d1 = r3->field_27
    //     0xe6eac4: ldur            d1, [x3, #0x27]
    // 0xe6eac8: fmul            d3, d1, d2
    // 0xe6eacc: LoadField: d1 = r3->field_47
    //     0xe6eacc: ldur            d1, [x3, #0x47]
    // 0xe6ead0: fmul            d7, d1, d4
    // 0xe6ead4: fadd            d1, d3, d7
    // 0xe6ead8: LoadField: d3 = r3->field_67
    //     0xe6ead8: ldur            d3, [x3, #0x67]
    // 0xe6eadc: fmul            d7, d3, d0
    // 0xe6eae0: fadd            d3, d1, d7
    // 0xe6eae4: LoadField: d1 = r3->field_87
    //     0xe6eae4: ldur            d1, [x3, #0x87]
    // 0xe6eae8: fadd            d7, d3, d1
    // 0xe6eaec: LoadField: d1 = r3->field_2f
    //     0xe6eaec: ldur            d1, [x3, #0x2f]
    // 0xe6eaf0: fmul            d3, d1, d2
    // 0xe6eaf4: LoadField: d1 = r3->field_4f
    //     0xe6eaf4: ldur            d1, [x3, #0x4f]
    // 0xe6eaf8: fmul            d8, d1, d4
    // 0xe6eafc: fadd            d1, d3, d8
    // 0xe6eb00: LoadField: d3 = r3->field_6f
    //     0xe6eb00: ldur            d3, [x3, #0x6f]
    // 0xe6eb04: fmul            d8, d3, d0
    // 0xe6eb08: fadd            d3, d1, d8
    // 0xe6eb0c: LoadField: d1 = r3->field_8f
    //     0xe6eb0c: ldur            d1, [x3, #0x8f]
    // 0xe6eb10: fadd            d8, d3, d1
    // 0xe6eb14: StoreField: r3->field_77 = d5
    //     0xe6eb14: stur            d5, [x3, #0x77]
    // 0xe6eb18: StoreField: r3->field_7f = d6
    //     0xe6eb18: stur            d6, [x3, #0x7f]
    // 0xe6eb1c: StoreField: r3->field_87 = d7
    //     0xe6eb1c: stur            d7, [x3, #0x87]
    // 0xe6eb20: StoreField: r3->field_8f = d8
    //     0xe6eb20: stur            d8, [x3, #0x8f]
    // 0xe6eb24: mov             v3.16b, v2.16b
    // 0xe6eb28: mov             v2.16b, v4.16b
    // 0xe6eb2c: b               #0xe6eb40
    // 0xe6eb30: ldur            x3, [fp, #-0x18]
    // 0xe6eb34: d0 = 0.000000
    //     0xe6eb34: eor             v0.16b, v0.16b, v0.16b
    // 0xe6eb38: d3 = 0.000000
    //     0xe6eb38: eor             v3.16b, v3.16b, v3.16b
    // 0xe6eb3c: d2 = 0.000000
    //     0xe6eb3c: eor             v2.16b, v2.16b, v2.16b
    // 0xe6eb40: ldur            x0, [fp, #-0x40]
    // 0xe6eb44: d1 = 0.017453
    //     0xe6eb44: add             x17, PP, #0xb, lsl #12  ; [pp+0xb6e8] IMM: double(0.017453292519943295) from 0x3f91df46a2529d39
    //     0xe6eb48: ldr             d1, [x17, #0x6e8]
    // 0xe6eb4c: stur            d3, [fp, #-0x78]
    // 0xe6eb50: stur            d2, [fp, #-0x80]
    // 0xe6eb54: LoadField: d4 = r0->field_7
    //     0xe6eb54: ldur            d4, [x0, #7]
    // 0xe6eb58: fmul            d5, d4, d1
    // 0xe6eb5c: stur            d5, [fp, #-0x70]
    // 0xe6eb60: r0 = Matrix4()
    //     0xe6eb60: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe6eb64: r4 = 32
    //     0xe6eb64: movz            x4, #0x20
    // 0xe6eb68: stur            x0, [fp, #-0x20]
    // 0xe6eb6c: r0 = AllocateFloat64Array()
    //     0xe6eb6c: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6eb70: mov             x1, x0
    // 0xe6eb74: ldur            x0, [fp, #-0x20]
    // 0xe6eb78: StoreField: r0->field_7 = r1
    //     0xe6eb78: stur            w1, [x0, #7]
    // 0xe6eb7c: d1 = 1.000000
    //     0xe6eb7c: fmov            d1, #1.00000000
    // 0xe6eb80: StoreField: r1->field_8f = d1
    //     0xe6eb80: stur            d1, [x1, #0x8f]
    // 0xe6eb84: mov             x1, x0
    // 0xe6eb88: ldur            d0, [fp, #-0x70]
    // 0xe6eb8c: r0 = setRotationZ()
    //     0xe6eb8c: bl              #0x9f0cf8  ; [package:vector_math/vector_math_64.dart] Matrix4::setRotationZ
    // 0xe6eb90: ldur            x1, [fp, #-0x10]
    // 0xe6eb94: ldur            x2, [fp, #-0x20]
    // 0xe6eb98: r0 = multiply()
    //     0xe6eb98: bl              #0x680524  ; [package:vector_math/vector_math_64.dart] Matrix4::multiply
    // 0xe6eb9c: ldur            d1, [fp, #-0x78]
    // 0xe6eba0: d0 = 0.000000
    //     0xe6eba0: eor             v0.16b, v0.16b, v0.16b
    // 0xe6eba4: fcmp            d1, d0
    // 0xe6eba8: b.eq            #0xe6ebb4
    // 0xe6ebac: ldur            d2, [fp, #-0x80]
    // 0xe6ebb0: b               #0xe6ebc0
    // 0xe6ebb4: ldur            d2, [fp, #-0x80]
    // 0xe6ebb8: fcmp            d2, d0
    // 0xe6ebbc: b.eq            #0xe6ec80
    // 0xe6ebc0: ldur            x0, [fp, #-0x18]
    // 0xe6ebc4: fneg            d3, d1
    // 0xe6ebc8: fneg            d1, d2
    // 0xe6ebcc: ArrayLoad: d2 = r0[0]  ; List_8
    //     0xe6ebcc: ldur            d2, [x0, #0x17]
    // 0xe6ebd0: fmul            d4, d2, d3
    // 0xe6ebd4: LoadField: d2 = r0->field_37
    //     0xe6ebd4: ldur            d2, [x0, #0x37]
    // 0xe6ebd8: fmul            d5, d2, d1
    // 0xe6ebdc: fadd            d2, d4, d5
    // 0xe6ebe0: LoadField: d4 = r0->field_57
    //     0xe6ebe0: ldur            d4, [x0, #0x57]
    // 0xe6ebe4: fmul            d5, d4, d0
    // 0xe6ebe8: fadd            d4, d2, d5
    // 0xe6ebec: LoadField: d2 = r0->field_77
    //     0xe6ebec: ldur            d2, [x0, #0x77]
    // 0xe6ebf0: fadd            d5, d4, d2
    // 0xe6ebf4: LoadField: d2 = r0->field_1f
    //     0xe6ebf4: ldur            d2, [x0, #0x1f]
    // 0xe6ebf8: fmul            d4, d2, d3
    // 0xe6ebfc: LoadField: d2 = r0->field_3f
    //     0xe6ebfc: ldur            d2, [x0, #0x3f]
    // 0xe6ec00: fmul            d6, d2, d1
    // 0xe6ec04: fadd            d2, d4, d6
    // 0xe6ec08: LoadField: d4 = r0->field_5f
    //     0xe6ec08: ldur            d4, [x0, #0x5f]
    // 0xe6ec0c: fmul            d6, d4, d0
    // 0xe6ec10: fadd            d4, d2, d6
    // 0xe6ec14: LoadField: d2 = r0->field_7f
    //     0xe6ec14: ldur            d2, [x0, #0x7f]
    // 0xe6ec18: fadd            d6, d4, d2
    // 0xe6ec1c: LoadField: d2 = r0->field_27
    //     0xe6ec1c: ldur            d2, [x0, #0x27]
    // 0xe6ec20: fmul            d4, d2, d3
    // 0xe6ec24: LoadField: d2 = r0->field_47
    //     0xe6ec24: ldur            d2, [x0, #0x47]
    // 0xe6ec28: fmul            d7, d2, d1
    // 0xe6ec2c: fadd            d2, d4, d7
    // 0xe6ec30: LoadField: d4 = r0->field_67
    //     0xe6ec30: ldur            d4, [x0, #0x67]
    // 0xe6ec34: fmul            d7, d4, d0
    // 0xe6ec38: fadd            d4, d2, d7
    // 0xe6ec3c: LoadField: d2 = r0->field_87
    //     0xe6ec3c: ldur            d2, [x0, #0x87]
    // 0xe6ec40: fadd            d7, d4, d2
    // 0xe6ec44: LoadField: d2 = r0->field_2f
    //     0xe6ec44: ldur            d2, [x0, #0x2f]
    // 0xe6ec48: fmul            d4, d2, d3
    // 0xe6ec4c: LoadField: d2 = r0->field_4f
    //     0xe6ec4c: ldur            d2, [x0, #0x4f]
    // 0xe6ec50: fmul            d3, d2, d1
    // 0xe6ec54: fadd            d1, d4, d3
    // 0xe6ec58: LoadField: d2 = r0->field_6f
    //     0xe6ec58: ldur            d2, [x0, #0x6f]
    // 0xe6ec5c: fmul            d3, d2, d0
    // 0xe6ec60: fadd            d2, d1, d3
    // 0xe6ec64: LoadField: d1 = r0->field_8f
    //     0xe6ec64: ldur            d1, [x0, #0x8f]
    // 0xe6ec68: fadd            d3, d2, d1
    // 0xe6ec6c: StoreField: r0->field_77 = d5
    //     0xe6ec6c: stur            d5, [x0, #0x77]
    // 0xe6ec70: StoreField: r0->field_7f = d6
    //     0xe6ec70: stur            d6, [x0, #0x7f]
    // 0xe6ec74: StoreField: r0->field_87 = d7
    //     0xe6ec74: stur            d7, [x0, #0x87]
    // 0xe6ec78: StoreField: r0->field_8f = d3
    //     0xe6ec78: stur            d3, [x0, #0x8f]
    // 0xe6ec7c: b               #0xe6ee34
    // 0xe6ec80: ldur            x0, [fp, #-0x18]
    // 0xe6ec84: b               #0xe6ee34
    // 0xe6ec88: ldur            x2, [fp, #-8]
    // 0xe6ec8c: ldur            x0, [fp, #-0x18]
    // 0xe6ec90: d0 = 0.000000
    //     0xe6ec90: eor             v0.16b, v0.16b, v0.16b
    // 0xe6ec94: r16 = "skewX"
    //     0xe6ec94: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec90] "skewX"
    //     0xe6ec98: ldr             x16, [x16, #0xc90]
    // 0xe6ec9c: ldur            lr, [fp, #-0x38]
    // 0xe6eca0: stp             lr, x16, [SP]
    // 0xe6eca4: r0 = ==()
    //     0xe6eca4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6eca8: tbnz            w0, #4, #0xe6ed64
    // 0xe6ecac: ldur            x2, [fp, #-8]
    // 0xe6ecb0: d0 = 0.017453
    //     0xe6ecb0: add             x17, PP, #0xb, lsl #12  ; [pp+0xb6e8] IMM: double(0.017453292519943295) from 0x3f91df46a2529d39
    //     0xe6ecb4: ldr             d0, [x17, #0x6e8]
    // 0xe6ecb8: LoadField: r0 = r2->field_b
    //     0xe6ecb8: ldur            w0, [x2, #0xb]
    // 0xe6ecbc: r1 = LoadInt32Instr(r0)
    //     0xe6ecbc: sbfx            x1, x0, #1, #0x1f
    // 0xe6ecc0: mov             x0, x1
    // 0xe6ecc4: r1 = 0
    //     0xe6ecc4: movz            x1, #0
    // 0xe6ecc8: cmp             x1, x0
    // 0xe6eccc: b.hs            #0xe6ef10
    // 0xe6ecd0: LoadField: r0 = r2->field_f
    //     0xe6ecd0: ldur            w0, [x2, #0xf]
    // 0xe6ecd4: DecompressPointer r0
    //     0xe6ecd4: add             x0, x0, HEAP, lsl #32
    // 0xe6ecd8: LoadField: r1 = r0->field_f
    //     0xe6ecd8: ldur            w1, [x0, #0xf]
    // 0xe6ecdc: DecompressPointer r1
    //     0xe6ecdc: add             x1, x1, HEAP, lsl #32
    // 0xe6ece0: LoadField: d1 = r1->field_7
    //     0xe6ece0: ldur            d1, [x1, #7]
    // 0xe6ece4: fmul            d2, d1, d0
    // 0xe6ece8: stur            d2, [fp, #-0x70]
    // 0xe6ecec: r0 = Matrix4()
    //     0xe6ecec: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe6ecf0: r4 = 32
    //     0xe6ecf0: movz            x4, #0x20
    // 0xe6ecf4: stur            x0, [fp, #-0x20]
    // 0xe6ecf8: r0 = AllocateFloat64Array()
    //     0xe6ecf8: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6ecfc: mov             x2, x0
    // 0xe6ed00: ldur            x0, [fp, #-0x20]
    // 0xe6ed04: stur            x2, [fp, #-0x40]
    // 0xe6ed08: StoreField: r0->field_7 = r2
    //     0xe6ed08: stur            w2, [x0, #7]
    // 0xe6ed0c: mov             x1, x0
    // 0xe6ed10: r0 = setIdentity()
    //     0xe6ed10: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe6ed14: ldur            d0, [fp, #-0x70]
    // 0xe6ed18: stp             fp, lr, [SP, #-0x10]!
    // 0xe6ed1c: mov             fp, SP
    // 0xe6ed20: CallRuntime_LibcTan(double) -> double
    //     0xe6ed20: and             SP, SP, #0xfffffffffffffff0
    //     0xe6ed24: mov             sp, SP
    //     0xe6ed28: ldr             x16, [THR, #0x5b0]  ; THR::LibcTan
    //     0xe6ed2c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe6ed30: blr             x16
    //     0xe6ed34: movz            x16, #0x8
    //     0xe6ed38: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe6ed3c: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xe6ed40: sub             sp, x16, #1, lsl #12
    //     0xe6ed44: mov             SP, fp
    //     0xe6ed48: ldp             fp, lr, [SP], #0x10
    // 0xe6ed4c: ldur            x0, [fp, #-0x40]
    // 0xe6ed50: StoreField: r0->field_37 = d0
    //     0xe6ed50: stur            d0, [x0, #0x37]
    // 0xe6ed54: ldur            x1, [fp, #-0x10]
    // 0xe6ed58: ldur            x2, [fp, #-0x20]
    // 0xe6ed5c: r0 = multiply()
    //     0xe6ed5c: bl              #0x680524  ; [package:vector_math/vector_math_64.dart] Matrix4::multiply
    // 0xe6ed60: b               #0xe6ee34
    // 0xe6ed64: ldur            x2, [fp, #-8]
    // 0xe6ed68: r16 = "skewY"
    //     0xe6ed68: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec98] "skewY"
    //     0xe6ed6c: ldr             x16, [x16, #0xc98]
    // 0xe6ed70: ldur            lr, [fp, #-0x38]
    // 0xe6ed74: stp             lr, x16, [SP]
    // 0xe6ed78: r0 = ==()
    //     0xe6ed78: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe6ed7c: tbnz            w0, #4, #0xe6ee34
    // 0xe6ed80: ldur            x2, [fp, #-8]
    // 0xe6ed84: d0 = 0.017453
    //     0xe6ed84: add             x17, PP, #0xb, lsl #12  ; [pp+0xb6e8] IMM: double(0.017453292519943295) from 0x3f91df46a2529d39
    //     0xe6ed88: ldr             d0, [x17, #0x6e8]
    // 0xe6ed8c: LoadField: r0 = r2->field_b
    //     0xe6ed8c: ldur            w0, [x2, #0xb]
    // 0xe6ed90: r1 = LoadInt32Instr(r0)
    //     0xe6ed90: sbfx            x1, x0, #1, #0x1f
    // 0xe6ed94: mov             x0, x1
    // 0xe6ed98: r1 = 0
    //     0xe6ed98: movz            x1, #0
    // 0xe6ed9c: cmp             x1, x0
    // 0xe6eda0: b.hs            #0xe6ef14
    // 0xe6eda4: LoadField: r0 = r2->field_f
    //     0xe6eda4: ldur            w0, [x2, #0xf]
    // 0xe6eda8: DecompressPointer r0
    //     0xe6eda8: add             x0, x0, HEAP, lsl #32
    // 0xe6edac: LoadField: r1 = r0->field_f
    //     0xe6edac: ldur            w1, [x0, #0xf]
    // 0xe6edb0: DecompressPointer r1
    //     0xe6edb0: add             x1, x1, HEAP, lsl #32
    // 0xe6edb4: LoadField: d1 = r1->field_7
    //     0xe6edb4: ldur            d1, [x1, #7]
    // 0xe6edb8: fmul            d2, d1, d0
    // 0xe6edbc: stur            d2, [fp, #-0x70]
    // 0xe6edc0: r0 = Matrix4()
    //     0xe6edc0: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe6edc4: r4 = 32
    //     0xe6edc4: movz            x4, #0x20
    // 0xe6edc8: stur            x0, [fp, #-8]
    // 0xe6edcc: r0 = AllocateFloat64Array()
    //     0xe6edcc: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6edd0: mov             x2, x0
    // 0xe6edd4: ldur            x0, [fp, #-8]
    // 0xe6edd8: stur            x2, [fp, #-0x20]
    // 0xe6eddc: StoreField: r0->field_7 = r2
    //     0xe6eddc: stur            w2, [x0, #7]
    // 0xe6ede0: mov             x1, x0
    // 0xe6ede4: r0 = setIdentity()
    //     0xe6ede4: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe6ede8: ldur            d0, [fp, #-0x70]
    // 0xe6edec: stp             fp, lr, [SP, #-0x10]!
    // 0xe6edf0: mov             fp, SP
    // 0xe6edf4: CallRuntime_LibcTan(double) -> double
    //     0xe6edf4: and             SP, SP, #0xfffffffffffffff0
    //     0xe6edf8: mov             sp, SP
    //     0xe6edfc: ldr             x16, [THR, #0x5b0]  ; THR::LibcTan
    //     0xe6ee00: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe6ee04: blr             x16
    //     0xe6ee08: movz            x16, #0x8
    //     0xe6ee0c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe6ee10: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xe6ee14: sub             sp, x16, #1, lsl #12
    //     0xe6ee18: mov             SP, fp
    //     0xe6ee1c: ldp             fp, lr, [SP], #0x10
    // 0xe6ee20: ldur            x0, [fp, #-0x20]
    // 0xe6ee24: StoreField: r0->field_1f = d0
    //     0xe6ee24: stur            d0, [x0, #0x1f]
    // 0xe6ee28: ldur            x1, [fp, #-0x10]
    // 0xe6ee2c: ldur            x2, [fp, #-8]
    // 0xe6ee30: r0 = multiply()
    //     0xe6ee30: bl              #0x680524  ; [package:vector_math/vector_math_64.dart] Matrix4::multiply
    // 0xe6ee34: ldur            x2, [fp, #-0x28]
    // 0xe6ee38: b               #0xe6e268
    // 0xe6ee3c: ldur            x0, [fp, #-0x10]
    // 0xe6ee40: r0 = SvgTransform()
    //     0xe6ee40: bl              #0xe6ef18  ; AllocateSvgTransformStub -> SvgTransform (size=0xc)
    // 0xe6ee44: mov             x1, x0
    // 0xe6ee48: ldur            x0, [fp, #-0x10]
    // 0xe6ee4c: StoreField: r1->field_7 = r0
    //     0xe6ee4c: stur            w0, [x1, #7]
    // 0xe6ee50: mov             x0, x1
    // 0xe6ee54: LeaveFrame
    //     0xe6ee54: mov             SP, fp
    //     0xe6ee58: ldp             fp, lr, [SP], #0x10
    // 0xe6ee5c: ret
    //     0xe6ee5c: ret             
    // 0xe6ee60: r0 = RangeError()
    //     0xe6ee60: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xe6ee64: mov             x1, x0
    // 0xe6ee68: r0 = "Value not in range"
    //     0xe6ee68: ldr             x0, [PP, #0x588]  ; [pp+0x588] "Value not in range"
    // 0xe6ee6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xe6ee6c: stur            w0, [x1, #0x17]
    // 0xe6ee70: r0 = 4
    //     0xe6ee70: movz            x0, #0x4
    // 0xe6ee74: StoreField: r1->field_f = r0
    //     0xe6ee74: stur            w0, [x1, #0xf]
    // 0xe6ee78: r2 = true
    //     0xe6ee78: add             x2, NULL, #0x20  ; true
    // 0xe6ee7c: StoreField: r1->field_b = r2
    //     0xe6ee7c: stur            w2, [x1, #0xb]
    // 0xe6ee80: mov             x0, x1
    // 0xe6ee84: r0 = Throw()
    //     0xe6ee84: bl              #0xec04b8  ; ThrowStub
    // 0xe6ee88: brk             #0
    // 0xe6ee8c: r2 = true
    //     0xe6ee8c: add             x2, NULL, #0x20  ; true
    // 0xe6ee90: r0 = "Value not in range"
    //     0xe6ee90: ldr             x0, [PP, #0x588]  ; [pp+0x588] "Value not in range"
    // 0xe6ee94: r0 = RangeError()
    //     0xe6ee94: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xe6ee98: mov             x1, x0
    // 0xe6ee9c: r0 = "Value not in range"
    //     0xe6ee9c: ldr             x0, [PP, #0x588]  ; [pp+0x588] "Value not in range"
    // 0xe6eea0: ArrayStore: r1[0] = r0  ; List_4
    //     0xe6eea0: stur            w0, [x1, #0x17]
    // 0xe6eea4: r0 = 2
    //     0xe6eea4: movz            x0, #0x2
    // 0xe6eea8: StoreField: r1->field_f = r0
    //     0xe6eea8: stur            w0, [x1, #0xf]
    // 0xe6eeac: r0 = true
    //     0xe6eeac: add             x0, NULL, #0x20  ; true
    // 0xe6eeb0: StoreField: r1->field_b = r0
    //     0xe6eeb0: stur            w0, [x1, #0xb]
    // 0xe6eeb4: mov             x0, x1
    // 0xe6eeb8: r0 = Throw()
    //     0xe6eeb8: bl              #0xec04b8  ; ThrowStub
    // 0xe6eebc: brk             #0
    // 0xe6eec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6eec0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6eec4: b               #0xe6e1bc
    // 0xe6eec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6eec8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6eecc: b               #0xe6e278
    // 0xe6eed0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6eed0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6eed4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6eed4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6eed8: b               #0xe6e490
    // 0xe6eedc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6eedc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6eee0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6eee0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6eee4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6eee4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6eee8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6eee8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6eeec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6eeec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6eef0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6eef0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6eef4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6eef4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6eef8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6eef8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6eefc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6eefc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6ef00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6ef00: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6ef04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6ef04: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6ef08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6ef08: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6ef0c: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6ef0c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe6ef10: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6ef10: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe6ef14: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6ef14: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  static RegExp _transformRegExp() {
    // ** addr: 0xe6ef24, size: 0x58
    // 0xe6ef24: EnterFrame
    //     0xe6ef24: stp             fp, lr, [SP, #-0x10]!
    //     0xe6ef28: mov             fp, SP
    // 0xe6ef2c: AllocStack(0x30)
    //     0xe6ef2c: sub             SP, SP, #0x30
    // 0xe6ef30: CheckStackOverflow
    //     0xe6ef30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6ef34: cmp             SP, x16
    //     0xe6ef38: b.ls            #0xe6ef74
    // 0xe6ef3c: r16 = "(matrix|translate|scale|rotate|skewX|skewY)\\s*\\(([^)]*)\\)\\s*"
    //     0xe6ef3c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eca0] "(matrix|translate|scale|rotate|skewX|skewY)\\s*\\(([^)]*)\\)\\s*"
    //     0xe6ef40: ldr             x16, [x16, #0xca0]
    // 0xe6ef44: stp             x16, NULL, [SP, #0x20]
    // 0xe6ef48: r16 = false
    //     0xe6ef48: add             x16, NULL, #0x30  ; false
    // 0xe6ef4c: r30 = true
    //     0xe6ef4c: add             lr, NULL, #0x20  ; true
    // 0xe6ef50: stp             lr, x16, [SP, #0x10]
    // 0xe6ef54: r16 = false
    //     0xe6ef54: add             x16, NULL, #0x30  ; false
    // 0xe6ef58: r30 = false
    //     0xe6ef58: add             lr, NULL, #0x30  ; false
    // 0xe6ef5c: stp             lr, x16, [SP]
    // 0xe6ef60: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xe6ef60: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xe6ef64: r0 = _RegExp()
    //     0xe6ef64: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xe6ef68: LeaveFrame
    //     0xe6ef68: mov             SP, fp
    //     0xe6ef6c: ldp             fp, lr, [SP], #0x10
    // 0xe6ef70: ret
    //     0xe6ef70: ret             
    // 0xe6ef74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6ef74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6ef78: b               #0xe6ef3c
  }
}
