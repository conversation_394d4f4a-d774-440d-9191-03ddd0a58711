// lib: , url: package:pdf/src/svg/use.dart

// class id: 1050841, size: 0x8
class :: {
}

// class id: 843, size: 0x2c, field offset: 0x18
class SvgUse extends SvgOperation {

  _ paintShape(/* No info */) {
    // ** addr: 0xe4a318, size: 0x9c
    // 0xe4a318: EnterFrame
    //     0xe4a318: stp             fp, lr, [SP, #-0x10]!
    //     0xe4a31c: mov             fp, SP
    // 0xe4a320: AllocStack(0x10)
    //     0xe4a320: sub             SP, SP, #0x10
    // 0xe4a324: d0 = 0.000000
    //     0xe4a324: eor             v0.16b, v0.16b, v0.16b
    // 0xe4a328: mov             x0, x2
    // 0xe4a32c: stur            x2, [fp, #-0x10]
    // 0xe4a330: mov             x2, x1
    // 0xe4a334: stur            x1, [fp, #-8]
    // 0xe4a338: CheckStackOverflow
    //     0xe4a338: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4a33c: cmp             SP, x16
    //     0xe4a340: b.ls            #0xe4a3ac
    // 0xe4a344: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xe4a344: ldur            d1, [x2, #0x17]
    // 0xe4a348: fcmp            d1, d0
    // 0xe4a34c: b.ne            #0xe4a35c
    // 0xe4a350: LoadField: d2 = r2->field_1f
    //     0xe4a350: ldur            d2, [x2, #0x1f]
    // 0xe4a354: fcmp            d2, d0
    // 0xe4a358: b.eq            #0xe4a380
    // 0xe4a35c: LoadField: d0 = r2->field_1f
    //     0xe4a35c: ldur            d0, [x2, #0x1f]
    // 0xe4a360: mov             v31.16b, v0.16b
    // 0xe4a364: mov             v0.16b, v1.16b
    // 0xe4a368: mov             v1.16b, v31.16b
    // 0xe4a36c: r1 = Null
    //     0xe4a36c: mov             x1, NULL
    // 0xe4a370: r0 = Matrix4.translationValues()
    //     0xe4a370: bl              #0x78ff30  ; [package:vector_math/vector_math_64.dart] Matrix4::Matrix4.translationValues
    // 0xe4a374: ldur            x1, [fp, #-0x10]
    // 0xe4a378: mov             x2, x0
    // 0xe4a37c: r0 = setTransform()
    //     0xe4a37c: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe4a380: ldur            x0, [fp, #-8]
    // 0xe4a384: LoadField: r1 = r0->field_27
    //     0xe4a384: ldur            w1, [x0, #0x27]
    // 0xe4a388: DecompressPointer r1
    //     0xe4a388: add             x1, x1, HEAP, lsl #32
    // 0xe4a38c: cmp             w1, NULL
    // 0xe4a390: b.eq            #0xe4a39c
    // 0xe4a394: ldur            x2, [fp, #-0x10]
    // 0xe4a398: r0 = paint()
    //     0xe4a398: bl              #0xe46704  ; [package:pdf/src/svg/operation.dart] SvgOperation::paint
    // 0xe4a39c: r0 = Null
    //     0xe4a39c: mov             x0, NULL
    // 0xe4a3a0: LeaveFrame
    //     0xe4a3a0: mov             SP, fp
    //     0xe4a3a4: ldp             fp, lr, [SP], #0x10
    // 0xe4a3a8: ret
    //     0xe4a3a8: ret             
    // 0xe4a3ac: r0 = StackOverflowSharedWithFPURegs()
    //     0xe4a3ac: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe4a3b0: b               #0xe4a344
  }
  _ drawShape(/* No info */) {
    // ** addr: 0xe54384, size: 0x9c
    // 0xe54384: EnterFrame
    //     0xe54384: stp             fp, lr, [SP, #-0x10]!
    //     0xe54388: mov             fp, SP
    // 0xe5438c: AllocStack(0x10)
    //     0xe5438c: sub             SP, SP, #0x10
    // 0xe54390: d0 = 0.000000
    //     0xe54390: eor             v0.16b, v0.16b, v0.16b
    // 0xe54394: mov             x0, x2
    // 0xe54398: stur            x2, [fp, #-0x10]
    // 0xe5439c: mov             x2, x1
    // 0xe543a0: stur            x1, [fp, #-8]
    // 0xe543a4: CheckStackOverflow
    //     0xe543a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe543a8: cmp             SP, x16
    //     0xe543ac: b.ls            #0xe54418
    // 0xe543b0: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xe543b0: ldur            d1, [x2, #0x17]
    // 0xe543b4: fcmp            d1, d0
    // 0xe543b8: b.ne            #0xe543c8
    // 0xe543bc: LoadField: d2 = r2->field_1f
    //     0xe543bc: ldur            d2, [x2, #0x1f]
    // 0xe543c0: fcmp            d2, d0
    // 0xe543c4: b.eq            #0xe543ec
    // 0xe543c8: LoadField: d0 = r2->field_1f
    //     0xe543c8: ldur            d0, [x2, #0x1f]
    // 0xe543cc: mov             v31.16b, v0.16b
    // 0xe543d0: mov             v0.16b, v1.16b
    // 0xe543d4: mov             v1.16b, v31.16b
    // 0xe543d8: r1 = Null
    //     0xe543d8: mov             x1, NULL
    // 0xe543dc: r0 = Matrix4.translationValues()
    //     0xe543dc: bl              #0x78ff30  ; [package:vector_math/vector_math_64.dart] Matrix4::Matrix4.translationValues
    // 0xe543e0: ldur            x1, [fp, #-0x10]
    // 0xe543e4: mov             x2, x0
    // 0xe543e8: r0 = setTransform()
    //     0xe543e8: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe543ec: ldur            x0, [fp, #-8]
    // 0xe543f0: LoadField: r1 = r0->field_27
    //     0xe543f0: ldur            w1, [x0, #0x27]
    // 0xe543f4: DecompressPointer r1
    //     0xe543f4: add             x1, x1, HEAP, lsl #32
    // 0xe543f8: cmp             w1, NULL
    // 0xe543fc: b.eq            #0xe54408
    // 0xe54400: ldur            x2, [fp, #-0x10]
    // 0xe54404: r0 = draw()
    //     0xe54404: bl              #0xe47920  ; [package:pdf/src/svg/operation.dart] SvgOperation::draw
    // 0xe54408: r0 = Null
    //     0xe54408: mov             x0, NULL
    // 0xe5440c: LeaveFrame
    //     0xe5440c: mov             SP, fp
    //     0xe54410: ldp             fp, lr, [SP], #0x10
    // 0xe54414: ret
    //     0xe54414: ret             
    // 0xe54418: r0 = StackOverflowSharedWithFPURegs()
    //     0xe54418: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe5441c: b               #0xe543b0
  }
  factory _ SvgUse.fromXml(/* No info */) {
    // ** addr: 0xe6f6b8, size: 0x21c
    // 0xe6f6b8: EnterFrame
    //     0xe6f6b8: stp             fp, lr, [SP, #-0x10]!
    //     0xe6f6bc: mov             fp, SP
    // 0xe6f6c0: AllocStack(0x40)
    //     0xe6f6c0: sub             SP, SP, #0x40
    // 0xe6f6c4: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3 */)
    //     0xe6f6c4: mov             x4, x2
    //     0xe6f6c8: mov             x0, x3
    //     0xe6f6cc: stur            x3, [fp, #-0x10]
    //     0xe6f6d0: mov             x3, x5
    //     0xe6f6d4: stur            x2, [fp, #-8]
    // 0xe6f6d8: CheckStackOverflow
    //     0xe6f6d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6f6dc: cmp             SP, x16
    //     0xe6f6e0: b.ls            #0xe6f8cc
    // 0xe6f6e4: mov             x2, x4
    // 0xe6f6e8: mov             x5, x0
    // 0xe6f6ec: r1 = Null
    //     0xe6f6ec: mov             x1, NULL
    // 0xe6f6f0: r0 = SvgBrush.fromXml()
    //     0xe6f6f0: bl              #0xe7332c  ; [package:pdf/src/svg/brush.dart] SvgBrush::SvgBrush.fromXml
    // 0xe6f6f4: stur            x0, [fp, #-0x18]
    // 0xe6f6f8: r16 = 0.000000
    //     0xe6f6f8: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe6f6fc: str             x16, [SP]
    // 0xe6f700: ldur            x1, [fp, #-8]
    // 0xe6f704: mov             x3, x0
    // 0xe6f708: r2 = "width"
    //     0xe6f708: ldr             x2, [PP, #0x7198]  ; [pp+0x7198] "width"
    // 0xe6f70c: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe6f70c: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe6f710: ldr             x4, [x4, #0xaf0]
    // 0xe6f714: r0 = getNumeric()
    //     0xe6f714: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe6f718: mov             x1, x0
    // 0xe6f71c: r0 = sizeValue()
    //     0xe6f71c: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe6f720: r16 = 0.000000
    //     0xe6f720: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe6f724: str             x16, [SP]
    // 0xe6f728: ldur            x1, [fp, #-8]
    // 0xe6f72c: ldur            x3, [fp, #-0x18]
    // 0xe6f730: r2 = "height"
    //     0xe6f730: ldr             x2, [PP, #0x4778]  ; [pp+0x4778] "height"
    // 0xe6f734: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe6f734: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe6f738: ldr             x4, [x4, #0xaf0]
    // 0xe6f73c: r0 = getNumeric()
    //     0xe6f73c: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe6f740: mov             x1, x0
    // 0xe6f744: r0 = sizeValue()
    //     0xe6f744: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe6f748: r16 = 0.000000
    //     0xe6f748: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe6f74c: str             x16, [SP]
    // 0xe6f750: ldur            x1, [fp, #-8]
    // 0xe6f754: ldur            x3, [fp, #-0x18]
    // 0xe6f758: r2 = "x"
    //     0xe6f758: ldr             x2, [PP, #0x71a0]  ; [pp+0x71a0] "x"
    // 0xe6f75c: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe6f75c: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe6f760: ldr             x4, [x4, #0xaf0]
    // 0xe6f764: r0 = getNumeric()
    //     0xe6f764: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe6f768: mov             x1, x0
    // 0xe6f76c: r0 = sizeValue()
    //     0xe6f76c: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe6f770: stur            d0, [fp, #-0x30]
    // 0xe6f774: r16 = 0.000000
    //     0xe6f774: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe6f778: str             x16, [SP]
    // 0xe6f77c: ldur            x1, [fp, #-8]
    // 0xe6f780: ldur            x3, [fp, #-0x18]
    // 0xe6f784: r2 = "y"
    //     0xe6f784: ldr             x2, [PP, #0x71a8]  ; [pp+0x71a8] "y"
    // 0xe6f788: r4 = const [0, 0x4, 0x1, 0x3, defaultValue, 0x3, null]
    //     0xe6f788: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] List(7) [0, 0x4, 0x1, 0x3, "defaultValue", 0x3, Null]
    //     0xe6f78c: ldr             x4, [x4, #0xaf0]
    // 0xe6f790: r0 = getNumeric()
    //     0xe6f790: bl              #0xb145d0  ; [package:pdf/src/svg/parser.dart] SvgParser::getNumeric
    // 0xe6f794: mov             x1, x0
    // 0xe6f798: r0 = sizeValue()
    //     0xe6f798: bl              #0xb144a0  ; [package:pdf/src/svg/parser.dart] SvgNumeric::sizeValue
    // 0xe6f79c: ldur            x1, [fp, #-8]
    // 0xe6f7a0: r2 = "href"
    //     0xe6f7a0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xe6f7a4: ldr             x2, [x2, #0x28]
    // 0xe6f7a8: stur            d0, [fp, #-0x38]
    // 0xe6f7ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6f7ac: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6f7b0: r0 = getAttribute()
    //     0xe6f7b0: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe6f7b4: cmp             w0, NULL
    // 0xe6f7b8: b.ne            #0xe6f7e8
    // 0xe6f7bc: r16 = "http://www.w3.org/1999/xlink"
    //     0xe6f7bc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eaf8] "http://www.w3.org/1999/xlink"
    //     0xe6f7c0: ldr             x16, [x16, #0xaf8]
    // 0xe6f7c4: str             x16, [SP]
    // 0xe6f7c8: ldur            x1, [fp, #-8]
    // 0xe6f7cc: r2 = "href"
    //     0xe6f7cc: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xe6f7d0: ldr             x2, [x2, #0x28]
    // 0xe6f7d4: r4 = const [0, 0x3, 0x1, 0x2, namespace, 0x2, null]
    //     0xe6f7d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e388] List(7) [0, 0x3, 0x1, 0x2, "namespace", 0x2, Null]
    //     0xe6f7d8: ldr             x4, [x4, #0x388]
    // 0xe6f7dc: r0 = getAttribute()
    //     0xe6f7dc: bl              #0xb14798  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0xe6f7e0: mov             x1, x0
    // 0xe6f7e4: b               #0xe6f7ec
    // 0xe6f7e8: mov             x1, x0
    // 0xe6f7ec: cmp             w1, NULL
    // 0xe6f7f0: b.eq            #0xe6f844
    // 0xe6f7f4: ldur            x0, [fp, #-0x10]
    // 0xe6f7f8: LoadField: r3 = r0->field_7
    //     0xe6f7f8: ldur            w3, [x0, #7]
    // 0xe6f7fc: DecompressPointer r3
    //     0xe6f7fc: add             x3, x3, HEAP, lsl #32
    // 0xe6f800: stur            x3, [fp, #-0x20]
    // 0xe6f804: r2 = 1
    //     0xe6f804: movz            x2, #0x1
    // 0xe6f808: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe6f808: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe6f80c: r0 = substring()
    //     0xe6f80c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe6f810: ldur            x1, [fp, #-0x20]
    // 0xe6f814: mov             x2, x0
    // 0xe6f818: r0 = findById()
    //     0xe6f818: bl              #0xe6f138  ; [package:pdf/src/svg/parser.dart] SvgParser::findById
    // 0xe6f81c: cmp             w0, NULL
    // 0xe6f820: b.eq            #0xe6f838
    // 0xe6f824: mov             x1, x0
    // 0xe6f828: ldur            x2, [fp, #-0x10]
    // 0xe6f82c: ldur            x3, [fp, #-0x18]
    // 0xe6f830: r0 = fromXml()
    //     0xe6f830: bl              #0xe6f308  ; [package:pdf/src/svg/operation.dart] SvgOperation::fromXml
    // 0xe6f834: b               #0xe6f83c
    // 0xe6f838: r0 = Null
    //     0xe6f838: mov             x0, NULL
    // 0xe6f83c: mov             x6, x0
    // 0xe6f840: b               #0xe6f848
    // 0xe6f844: r6 = Null
    //     0xe6f844: mov             x6, NULL
    // 0xe6f848: ldur            x0, [fp, #-0x10]
    // 0xe6f84c: ldur            x4, [fp, #-0x18]
    // 0xe6f850: ldur            d1, [fp, #-0x30]
    // 0xe6f854: ldur            d0, [fp, #-0x38]
    // 0xe6f858: ldur            x2, [fp, #-8]
    // 0xe6f85c: mov             x3, x0
    // 0xe6f860: mov             x5, x4
    // 0xe6f864: stur            x6, [fp, #-0x20]
    // 0xe6f868: r1 = Null
    //     0xe6f868: mov             x1, NULL
    // 0xe6f86c: r0 = SvgClipPath.fromXml()
    //     0xe6f86c: bl              #0xe6ef7c  ; [package:pdf/src/svg/clip_path.dart] SvgClipPath::SvgClipPath.fromXml
    // 0xe6f870: ldur            x2, [fp, #-8]
    // 0xe6f874: r1 = Null
    //     0xe6f874: mov             x1, NULL
    // 0xe6f878: stur            x0, [fp, #-8]
    // 0xe6f87c: r0 = SvgTransform.fromXml()
    //     0xe6f87c: bl              #0xe6e158  ; [package:pdf/src/svg/transform.dart] SvgTransform::SvgTransform.fromXml
    // 0xe6f880: stur            x0, [fp, #-0x28]
    // 0xe6f884: r0 = SvgUse()
    //     0xe6f884: bl              #0xe6f8d4  ; AllocateSvgUseStub -> SvgUse (size=0x2c)
    // 0xe6f888: ldur            d0, [fp, #-0x30]
    // 0xe6f88c: ArrayStore: r0[0] = d0  ; List_8
    //     0xe6f88c: stur            d0, [x0, #0x17]
    // 0xe6f890: ldur            d0, [fp, #-0x38]
    // 0xe6f894: StoreField: r0->field_1f = d0
    //     0xe6f894: stur            d0, [x0, #0x1f]
    // 0xe6f898: ldur            x1, [fp, #-0x20]
    // 0xe6f89c: StoreField: r0->field_27 = r1
    //     0xe6f89c: stur            w1, [x0, #0x27]
    // 0xe6f8a0: ldur            x1, [fp, #-0x18]
    // 0xe6f8a4: StoreField: r0->field_7 = r1
    //     0xe6f8a4: stur            w1, [x0, #7]
    // 0xe6f8a8: ldur            x1, [fp, #-8]
    // 0xe6f8ac: StoreField: r0->field_b = r1
    //     0xe6f8ac: stur            w1, [x0, #0xb]
    // 0xe6f8b0: ldur            x1, [fp, #-0x28]
    // 0xe6f8b4: StoreField: r0->field_f = r1
    //     0xe6f8b4: stur            w1, [x0, #0xf]
    // 0xe6f8b8: ldur            x1, [fp, #-0x10]
    // 0xe6f8bc: StoreField: r0->field_13 = r1
    //     0xe6f8bc: stur            w1, [x0, #0x13]
    // 0xe6f8c0: LeaveFrame
    //     0xe6f8c0: mov             SP, fp
    //     0xe6f8c4: ldp             fp, lr, [SP], #0x10
    // 0xe6f8c8: ret
    //     0xe6f8c8: ret             
    // 0xe6f8cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6f8cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6f8d0: b               #0xe6f6e4
  }
}
