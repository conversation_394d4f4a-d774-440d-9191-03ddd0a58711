// lib: , url: package:pdf/src/widgets/basic.dart

// class id: 1050843, size: 0x8
class :: {
}

// class id: 802, size: 0x1c, field offset: 0x10
class SizedBox extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xeaf474, size: 0xcc
    // 0xeaf474: EnterFrame
    //     0xeaf474: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf478: mov             fp, SP
    // 0xeaf47c: AllocStack(0x28)
    //     0xeaf47c: sub             SP, SP, #0x28
    // 0xeaf480: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xeaf480: ldur            w0, [x1, #0x17]
    // 0xeaf484: DecompressPointer r0
    //     0xeaf484: add             x0, x0, HEAP, lsl #32
    // 0xeaf488: stur            x0, [fp, #-0x18]
    // 0xeaf48c: LoadField: r2 = r1->field_f
    //     0xeaf48c: ldur            w2, [x1, #0xf]
    // 0xeaf490: DecompressPointer r2
    //     0xeaf490: add             x2, x2, HEAP, lsl #32
    // 0xeaf494: stur            x2, [fp, #-0x10]
    // 0xeaf498: LoadField: r3 = r1->field_13
    //     0xeaf498: ldur            w3, [x1, #0x13]
    // 0xeaf49c: DecompressPointer r3
    //     0xeaf49c: add             x3, x3, HEAP, lsl #32
    // 0xeaf4a0: stur            x3, [fp, #-8]
    // 0xeaf4a4: cmp             w2, NULL
    // 0xeaf4a8: b.ne            #0xeaf4b4
    // 0xeaf4ac: d0 = 0.000000
    //     0xeaf4ac: eor             v0.16b, v0.16b, v0.16b
    // 0xeaf4b0: b               #0xeaf4b8
    // 0xeaf4b4: LoadField: d0 = r2->field_7
    //     0xeaf4b4: ldur            d0, [x2, #7]
    // 0xeaf4b8: stur            d0, [fp, #-0x28]
    // 0xeaf4bc: r0 = BoxConstraints()
    //     0xeaf4bc: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xeaf4c0: ldur            d0, [fp, #-0x28]
    // 0xeaf4c4: stur            x0, [fp, #-0x20]
    // 0xeaf4c8: StoreField: r0->field_7 = d0
    //     0xeaf4c8: stur            d0, [x0, #7]
    // 0xeaf4cc: ldur            x1, [fp, #-0x10]
    // 0xeaf4d0: cmp             w1, NULL
    // 0xeaf4d4: b.ne            #0xeaf4e0
    // 0xeaf4d8: d0 = inf
    //     0xeaf4d8: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xeaf4dc: b               #0xeaf4e4
    // 0xeaf4e0: LoadField: d0 = r1->field_7
    //     0xeaf4e0: ldur            d0, [x1, #7]
    // 0xeaf4e4: ldur            x1, [fp, #-8]
    // 0xeaf4e8: StoreField: r0->field_f = d0
    //     0xeaf4e8: stur            d0, [x0, #0xf]
    // 0xeaf4ec: cmp             w1, NULL
    // 0xeaf4f0: b.ne            #0xeaf4fc
    // 0xeaf4f4: d0 = 0.000000
    //     0xeaf4f4: eor             v0.16b, v0.16b, v0.16b
    // 0xeaf4f8: b               #0xeaf500
    // 0xeaf4fc: LoadField: d0 = r1->field_7
    //     0xeaf4fc: ldur            d0, [x1, #7]
    // 0xeaf500: ArrayStore: r0[0] = d0  ; List_8
    //     0xeaf500: stur            d0, [x0, #0x17]
    // 0xeaf504: cmp             w1, NULL
    // 0xeaf508: b.ne            #0xeaf514
    // 0xeaf50c: d0 = inf
    //     0xeaf50c: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xeaf510: b               #0xeaf518
    // 0xeaf514: LoadField: d0 = r1->field_7
    //     0xeaf514: ldur            d0, [x1, #7]
    // 0xeaf518: ldur            x1, [fp, #-0x18]
    // 0xeaf51c: StoreField: r0->field_1f = d0
    //     0xeaf51c: stur            d0, [x0, #0x1f]
    // 0xeaf520: r0 = ConstrainedBox()
    //     0xeaf520: bl              #0xe8f454  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xeaf524: ldur            x1, [fp, #-0x20]
    // 0xeaf528: StoreField: r0->field_f = r1
    //     0xeaf528: stur            w1, [x0, #0xf]
    // 0xeaf52c: ldur            x1, [fp, #-0x18]
    // 0xeaf530: StoreField: r0->field_b = r1
    //     0xeaf530: stur            w1, [x0, #0xb]
    // 0xeaf534: LeaveFrame
    //     0xeaf534: mov             SP, fp
    //     0xeaf538: ldp             fp, lr, [SP], #0x10
    // 0xeaf53c: ret
    //     0xeaf53c: ret             
  }
}

// class id: 805, size: 0x14, field offset: 0x10
class ConstrainedBox extends SingleChildWidget {

  _ paint(/* No info */) {
    // ** addr: 0xe63828, size: 0x30
    // 0xe63828: EnterFrame
    //     0xe63828: stp             fp, lr, [SP, #-0x10]!
    //     0xe6382c: mov             fp, SP
    // 0xe63830: CheckStackOverflow
    //     0xe63830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe63834: cmp             SP, x16
    //     0xe63838: b.ls            #0xe63850
    // 0xe6383c: r0 = paintChild()
    //     0xe6383c: bl              #0xe63858  ; [package:pdf/src/widgets/widget.dart] SingleChildWidget::paintChild
    // 0xe63840: r0 = Null
    //     0xe63840: mov             x0, NULL
    // 0xe63844: LeaveFrame
    //     0xe63844: mov             SP, fp
    //     0xe63848: ldp             fp, lr, [SP], #0x10
    // 0xe6384c: ret
    //     0xe6384c: ret             
    // 0xe63850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe63850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe63854: b               #0xe6383c
  }
  _ layout(/* No info */) {
    // ** addr: 0xe8ece4, size: 0x108
    // 0xe8ece4: EnterFrame
    //     0xe8ece4: stp             fp, lr, [SP, #-0x10]!
    //     0xe8ece8: mov             fp, SP
    // 0xe8ecec: AllocStack(0x18)
    //     0xe8ecec: sub             SP, SP, #0x18
    // 0xe8ecf0: SetupParameters(ConstrainedBox this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r2 */)
    //     0xe8ecf0: mov             x0, x2
    //     0xe8ecf4: stur            x2, [fp, #-0x18]
    //     0xe8ecf8: mov             x2, x3
    //     0xe8ecfc: mov             x3, x1
    //     0xe8ed00: stur            x1, [fp, #-0x10]
    // 0xe8ed04: CheckStackOverflow
    //     0xe8ed04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ed08: cmp             SP, x16
    //     0xe8ed0c: b.ls            #0xe8ede4
    // 0xe8ed10: LoadField: r4 = r3->field_b
    //     0xe8ed10: ldur            w4, [x3, #0xb]
    // 0xe8ed14: DecompressPointer r4
    //     0xe8ed14: add             x4, x4, HEAP, lsl #32
    // 0xe8ed18: stur            x4, [fp, #-8]
    // 0xe8ed1c: cmp             w4, NULL
    // 0xe8ed20: b.eq            #0xe8ed8c
    // 0xe8ed24: LoadField: r1 = r3->field_f
    //     0xe8ed24: ldur            w1, [x3, #0xf]
    // 0xe8ed28: DecompressPointer r1
    //     0xe8ed28: add             x1, x1, HEAP, lsl #32
    // 0xe8ed2c: r0 = enforce()
    //     0xe8ed2c: bl              #0xe8ee64  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::enforce
    // 0xe8ed30: ldur            x4, [fp, #-8]
    // 0xe8ed34: r1 = LoadClassIdInstr(r4)
    //     0xe8ed34: ldur            x1, [x4, #-1]
    //     0xe8ed38: ubfx            x1, x1, #0xc, #0x14
    // 0xe8ed3c: mov             x3, x0
    // 0xe8ed40: mov             x0, x1
    // 0xe8ed44: mov             x1, x4
    // 0xe8ed48: ldur            x2, [fp, #-0x18]
    // 0xe8ed4c: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe8ed4c: sub             lr, x0, #0xf89
    //     0xe8ed50: ldr             lr, [x21, lr, lsl #3]
    //     0xe8ed54: blr             lr
    // 0xe8ed58: ldur            x0, [fp, #-8]
    // 0xe8ed5c: LoadField: r1 = r0->field_7
    //     0xe8ed5c: ldur            w1, [x0, #7]
    // 0xe8ed60: DecompressPointer r1
    //     0xe8ed60: add             x1, x1, HEAP, lsl #32
    // 0xe8ed64: mov             x0, x1
    // 0xe8ed68: ldur            x3, [fp, #-0x10]
    // 0xe8ed6c: StoreField: r3->field_7 = r0
    //     0xe8ed6c: stur            w0, [x3, #7]
    //     0xe8ed70: ldurb           w16, [x3, #-1]
    //     0xe8ed74: ldurb           w17, [x0, #-1]
    //     0xe8ed78: and             x16, x17, x16, lsr #2
    //     0xe8ed7c: tst             x16, HEAP, lsr #32
    //     0xe8ed80: b.eq            #0xe8ed88
    //     0xe8ed84: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe8ed88: b               #0xe8edd4
    // 0xe8ed8c: LoadField: r1 = r3->field_f
    //     0xe8ed8c: ldur            w1, [x3, #0xf]
    // 0xe8ed90: DecompressPointer r1
    //     0xe8ed90: add             x1, x1, HEAP, lsl #32
    // 0xe8ed94: r0 = enforce()
    //     0xe8ed94: bl              #0xe8ee64  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::enforce
    // 0xe8ed98: mov             x1, x0
    // 0xe8ed9c: r0 = smallest()
    //     0xe8ed9c: bl              #0xe8edec  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::smallest
    // 0xe8eda0: mov             x3, x0
    // 0xe8eda4: r1 = Null
    //     0xe8eda4: mov             x1, NULL
    // 0xe8eda8: r2 = Instance_PdfPoint
    //     0xe8eda8: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xe8edac: ldr             x2, [x2, #0x730]
    // 0xe8edb0: r0 = PdfRect.fromPoints()
    //     0xe8edb0: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xe8edb4: ldur            x1, [fp, #-0x10]
    // 0xe8edb8: StoreField: r1->field_7 = r0
    //     0xe8edb8: stur            w0, [x1, #7]
    //     0xe8edbc: ldurb           w16, [x1, #-1]
    //     0xe8edc0: ldurb           w17, [x0, #-1]
    //     0xe8edc4: and             x16, x17, x16, lsr #2
    //     0xe8edc8: tst             x16, HEAP, lsr #32
    //     0xe8edcc: b.eq            #0xe8edd4
    //     0xe8edd0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8edd4: r0 = Null
    //     0xe8edd4: mov             x0, NULL
    // 0xe8edd8: LeaveFrame
    //     0xe8edd8: mov             SP, fp
    //     0xe8eddc: ldp             fp, lr, [SP], #0x10
    // 0xe8ede0: ret
    //     0xe8ede0: ret             
    // 0xe8ede4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8ede4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8ede8: b               #0xe8ed10
  }
}

// class id: 806, size: 0x1c, field offset: 0x10
class Align extends SingleChildWidget {

  _ layout(/* No info */) {
    // ** addr: 0xe8ea70, size: 0x230
    // 0xe8ea70: EnterFrame
    //     0xe8ea70: stp             fp, lr, [SP, #-0x10]!
    //     0xe8ea74: mov             fp, SP
    // 0xe8ea78: AllocStack(0x48)
    //     0xe8ea78: sub             SP, SP, #0x48
    // 0xe8ea7c: d0 = inf
    //     0xe8ea7c: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8ea80: mov             x0, x3
    // 0xe8ea84: stur            x3, [fp, #-0x30]
    // 0xe8ea88: mov             x3, x1
    // 0xe8ea8c: stur            x1, [fp, #-0x20]
    // 0xe8ea90: stur            x2, [fp, #-0x28]
    // 0xe8ea94: CheckStackOverflow
    //     0xe8ea94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ea98: cmp             SP, x16
    //     0xe8ea9c: b.ls            #0xe8ec88
    // 0xe8eaa0: LoadField: d1 = r0->field_f
    //     0xe8eaa0: ldur            d1, [x0, #0xf]
    // 0xe8eaa4: fcmp            d1, d0
    // 0xe8eaa8: r16 = true
    //     0xe8eaa8: add             x16, NULL, #0x20  ; true
    // 0xe8eaac: r17 = false
    //     0xe8eaac: add             x17, NULL, #0x30  ; false
    // 0xe8eab0: csel            x4, x16, x17, eq
    // 0xe8eab4: stur            x4, [fp, #-0x18]
    // 0xe8eab8: LoadField: d1 = r0->field_1f
    //     0xe8eab8: ldur            d1, [x0, #0x1f]
    // 0xe8eabc: fcmp            d1, d0
    // 0xe8eac0: r16 = true
    //     0xe8eac0: add             x16, NULL, #0x20  ; true
    // 0xe8eac4: r17 = false
    //     0xe8eac4: add             x17, NULL, #0x30  ; false
    // 0xe8eac8: csel            x5, x16, x17, eq
    // 0xe8eacc: stur            x5, [fp, #-0x10]
    // 0xe8ead0: LoadField: r6 = r3->field_b
    //     0xe8ead0: ldur            w6, [x3, #0xb]
    // 0xe8ead4: DecompressPointer r6
    //     0xe8ead4: add             x6, x6, HEAP, lsl #32
    // 0xe8ead8: stur            x6, [fp, #-8]
    // 0xe8eadc: cmp             w6, NULL
    // 0xe8eae0: b.eq            #0xe8ec24
    // 0xe8eae4: mov             x1, x0
    // 0xe8eae8: r0 = loosen()
    //     0xe8eae8: bl              #0xe8eca0  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::loosen
    // 0xe8eaec: ldur            x4, [fp, #-8]
    // 0xe8eaf0: r1 = LoadClassIdInstr(r4)
    //     0xe8eaf0: ldur            x1, [x4, #-1]
    //     0xe8eaf4: ubfx            x1, x1, #0xc, #0x14
    // 0xe8eaf8: mov             x3, x0
    // 0xe8eafc: mov             x0, x1
    // 0xe8eb00: mov             x1, x4
    // 0xe8eb04: ldur            x2, [fp, #-0x28]
    // 0xe8eb08: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe8eb08: sub             lr, x0, #0xf89
    //     0xe8eb0c: ldr             lr, [x21, lr, lsl #3]
    //     0xe8eb10: blr             lr
    // 0xe8eb14: ldur            x0, [fp, #-0x18]
    // 0xe8eb18: tbnz            w0, #4, #0xe8eb3c
    // 0xe8eb1c: ldur            x0, [fp, #-8]
    // 0xe8eb20: LoadField: r1 = r0->field_7
    //     0xe8eb20: ldur            w1, [x0, #7]
    // 0xe8eb24: DecompressPointer r1
    //     0xe8eb24: add             x1, x1, HEAP, lsl #32
    // 0xe8eb28: cmp             w1, NULL
    // 0xe8eb2c: b.eq            #0xe8ec90
    // 0xe8eb30: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe8eb30: ldur            d0, [x1, #0x17]
    // 0xe8eb34: mov             v1.16b, v0.16b
    // 0xe8eb38: b               #0xe8eb44
    // 0xe8eb3c: ldur            x0, [fp, #-8]
    // 0xe8eb40: d1 = inf
    //     0xe8eb40: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8eb44: ldur            x1, [fp, #-0x10]
    // 0xe8eb48: tbnz            w1, #4, #0xe8eb64
    // 0xe8eb4c: LoadField: r1 = r0->field_7
    //     0xe8eb4c: ldur            w1, [x0, #7]
    // 0xe8eb50: DecompressPointer r1
    //     0xe8eb50: add             x1, x1, HEAP, lsl #32
    // 0xe8eb54: cmp             w1, NULL
    // 0xe8eb58: b.eq            #0xe8ec94
    // 0xe8eb5c: LoadField: d0 = r1->field_1f
    //     0xe8eb5c: ldur            d0, [x1, #0x1f]
    // 0xe8eb60: b               #0xe8eb68
    // 0xe8eb64: d0 = inf
    //     0xe8eb64: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8eb68: ldur            x2, [fp, #-0x20]
    // 0xe8eb6c: ldur            x1, [fp, #-0x30]
    // 0xe8eb70: r0 = constrainRect()
    //     0xe8eb70: bl              #0xe8e560  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainRect
    // 0xe8eb74: ldur            x2, [fp, #-0x20]
    // 0xe8eb78: StoreField: r2->field_7 = r0
    //     0xe8eb78: stur            w0, [x2, #7]
    //     0xe8eb7c: ldurb           w16, [x2, #-1]
    //     0xe8eb80: ldurb           w17, [x0, #-1]
    //     0xe8eb84: and             x16, x17, x16, lsr #2
    //     0xe8eb88: tst             x16, HEAP, lsr #32
    //     0xe8eb8c: b.eq            #0xe8eb94
    //     0xe8eb90: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe8eb94: LoadField: r0 = r2->field_f
    //     0xe8eb94: ldur            w0, [x2, #0xf]
    // 0xe8eb98: DecompressPointer r0
    //     0xe8eb98: add             x0, x0, HEAP, lsl #32
    // 0xe8eb9c: ldur            x1, [fp, #-0x28]
    // 0xe8eba0: stur            x0, [fp, #-0x38]
    // 0xe8eba4: r0 = of()
    //     0xe8eba4: bl              #0xb137c8  ; [package:pdf/src/widgets/text_style.dart] Directionality::of
    // 0xe8eba8: ldur            x0, [fp, #-8]
    // 0xe8ebac: LoadField: r1 = r0->field_7
    //     0xe8ebac: ldur            w1, [x0, #7]
    // 0xe8ebb0: DecompressPointer r1
    //     0xe8ebb0: add             x1, x1, HEAP, lsl #32
    // 0xe8ebb4: cmp             w1, NULL
    // 0xe8ebb8: b.eq            #0xe8ec98
    // 0xe8ebbc: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe8ebbc: ldur            d0, [x1, #0x17]
    // 0xe8ebc0: stur            d0, [fp, #-0x48]
    // 0xe8ebc4: LoadField: d1 = r1->field_1f
    //     0xe8ebc4: ldur            d1, [x1, #0x1f]
    // 0xe8ebc8: stur            d1, [fp, #-0x40]
    // 0xe8ebcc: r0 = PdfPoint()
    //     0xe8ebcc: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe8ebd0: ldur            d0, [fp, #-0x48]
    // 0xe8ebd4: StoreField: r0->field_7 = d0
    //     0xe8ebd4: stur            d0, [x0, #7]
    // 0xe8ebd8: ldur            d0, [fp, #-0x40]
    // 0xe8ebdc: StoreField: r0->field_f = d0
    //     0xe8ebdc: stur            d0, [x0, #0xf]
    // 0xe8ebe0: ldur            x2, [fp, #-0x20]
    // 0xe8ebe4: LoadField: r3 = r2->field_7
    //     0xe8ebe4: ldur            w3, [x2, #7]
    // 0xe8ebe8: DecompressPointer r3
    //     0xe8ebe8: add             x3, x3, HEAP, lsl #32
    // 0xe8ebec: cmp             w3, NULL
    // 0xe8ebf0: b.eq            #0xe8ec9c
    // 0xe8ebf4: ldur            x1, [fp, #-0x38]
    // 0xe8ebf8: mov             x2, x0
    // 0xe8ebfc: r0 = inscribe()
    //     0xe8ebfc: bl              #0xe68de0  ; [package:pdf/src/widgets/geometry.dart] Alignment::inscribe
    // 0xe8ec00: ldur            x1, [fp, #-8]
    // 0xe8ec04: StoreField: r1->field_7 = r0
    //     0xe8ec04: stur            w0, [x1, #7]
    //     0xe8ec08: ldurb           w16, [x1, #-1]
    //     0xe8ec0c: ldurb           w17, [x0, #-1]
    //     0xe8ec10: and             x16, x17, x16, lsr #2
    //     0xe8ec14: tst             x16, HEAP, lsr #32
    //     0xe8ec18: b.eq            #0xe8ec20
    //     0xe8ec1c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8ec20: b               #0xe8ec78
    // 0xe8ec24: mov             x2, x3
    // 0xe8ec28: mov             x0, x4
    // 0xe8ec2c: mov             x1, x5
    // 0xe8ec30: tbnz            w0, #4, #0xe8ec3c
    // 0xe8ec34: d1 = 0.000000
    //     0xe8ec34: eor             v1.16b, v1.16b, v1.16b
    // 0xe8ec38: b               #0xe8ec40
    // 0xe8ec3c: d1 = inf
    //     0xe8ec3c: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8ec40: tbnz            w1, #4, #0xe8ec4c
    // 0xe8ec44: d0 = 0.000000
    //     0xe8ec44: eor             v0.16b, v0.16b, v0.16b
    // 0xe8ec48: b               #0xe8ec50
    // 0xe8ec4c: d0 = inf
    //     0xe8ec4c: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8ec50: ldur            x1, [fp, #-0x30]
    // 0xe8ec54: r0 = constrainRect()
    //     0xe8ec54: bl              #0xe8e560  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainRect
    // 0xe8ec58: ldur            x1, [fp, #-0x20]
    // 0xe8ec5c: StoreField: r1->field_7 = r0
    //     0xe8ec5c: stur            w0, [x1, #7]
    //     0xe8ec60: ldurb           w16, [x1, #-1]
    //     0xe8ec64: ldurb           w17, [x0, #-1]
    //     0xe8ec68: and             x16, x17, x16, lsr #2
    //     0xe8ec6c: tst             x16, HEAP, lsr #32
    //     0xe8ec70: b.eq            #0xe8ec78
    //     0xe8ec74: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8ec78: r0 = Null
    //     0xe8ec78: mov             x0, NULL
    // 0xe8ec7c: LeaveFrame
    //     0xe8ec7c: mov             SP, fp
    //     0xe8ec80: ldp             fp, lr, [SP], #0x10
    // 0xe8ec84: ret
    //     0xe8ec84: ret             
    // 0xe8ec88: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8ec88: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8ec8c: b               #0xe8eaa0
    // 0xe8ec90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe8ec90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe8ec94: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe8ec94: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe8ec98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe8ec98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe8ec9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe8ec9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 808, size: 0x14, field offset: 0x10
class Padding extends SingleChildWidget {

  _ paint(/* No info */) {
    // ** addr: 0xe636f8, size: 0x130
    // 0xe636f8: EnterFrame
    //     0xe636f8: stp             fp, lr, [SP, #-0x10]!
    //     0xe636fc: mov             fp, SP
    // 0xe63700: AllocStack(0x28)
    //     0xe63700: sub             SP, SP, #0x28
    // 0xe63704: SetupParameters(Padding this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xe63704: mov             x0, x2
    //     0xe63708: stur            x2, [fp, #-0x18]
    //     0xe6370c: mov             x2, x1
    //     0xe63710: stur            x1, [fp, #-0x10]
    // 0xe63714: CheckStackOverflow
    //     0xe63714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe63718: cmp             SP, x16
    //     0xe6371c: b.ls            #0xe63818
    // 0xe63720: LoadField: r3 = r2->field_f
    //     0xe63720: ldur            w3, [x2, #0xf]
    // 0xe63724: DecompressPointer r3
    //     0xe63724: add             x3, x3, HEAP, lsl #32
    // 0xe63728: mov             x1, x0
    // 0xe6372c: stur            x3, [fp, #-8]
    // 0xe63730: r0 = of()
    //     0xe63730: bl              #0xb137c8  ; [package:pdf/src/widgets/text_style.dart] Directionality::of
    // 0xe63734: ldur            x0, [fp, #-0x10]
    // 0xe63738: LoadField: r1 = r0->field_b
    //     0xe63738: ldur            w1, [x0, #0xb]
    // 0xe6373c: DecompressPointer r1
    //     0xe6373c: add             x1, x1, HEAP, lsl #32
    // 0xe63740: stur            x1, [fp, #-0x20]
    // 0xe63744: cmp             w1, NULL
    // 0xe63748: b.eq            #0xe63808
    // 0xe6374c: ldur            x2, [fp, #-0x18]
    // 0xe63750: ldur            x3, [fp, #-8]
    // 0xe63754: r0 = Matrix4()
    //     0xe63754: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe63758: r4 = 32
    //     0xe63758: movz            x4, #0x20
    // 0xe6375c: stur            x0, [fp, #-0x28]
    // 0xe63760: r0 = AllocateFloat64Array()
    //     0xe63760: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe63764: mov             x1, x0
    // 0xe63768: ldur            x0, [fp, #-0x28]
    // 0xe6376c: StoreField: r0->field_7 = r1
    //     0xe6376c: stur            w1, [x0, #7]
    // 0xe63770: mov             x1, x0
    // 0xe63774: r0 = setIdentity()
    //     0xe63774: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe63778: ldur            x0, [fp, #-0x10]
    // 0xe6377c: LoadField: r1 = r0->field_7
    //     0xe6377c: ldur            w1, [x0, #7]
    // 0xe63780: DecompressPointer r1
    //     0xe63780: add             x1, x1, HEAP, lsl #32
    // 0xe63784: cmp             w1, NULL
    // 0xe63788: b.eq            #0xe63820
    // 0xe6378c: LoadField: d0 = r1->field_7
    //     0xe6378c: ldur            d0, [x1, #7]
    // 0xe63790: ldur            x0, [fp, #-8]
    // 0xe63794: LoadField: d1 = r0->field_7
    //     0xe63794: ldur            d1, [x0, #7]
    // 0xe63798: fadd            d2, d0, d1
    // 0xe6379c: LoadField: d0 = r1->field_f
    //     0xe6379c: ldur            d0, [x1, #0xf]
    // 0xe637a0: LoadField: d1 = r0->field_1f
    //     0xe637a0: ldur            d1, [x0, #0x1f]
    // 0xe637a4: fadd            d3, d0, d1
    // 0xe637a8: ldur            x1, [fp, #-0x28]
    // 0xe637ac: mov             v0.16b, v2.16b
    // 0xe637b0: mov             v1.16b, v3.16b
    // 0xe637b4: r0 = translate()
    //     0xe637b4: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe637b8: ldur            x2, [fp, #-0x18]
    // 0xe637bc: LoadField: r0 = r2->field_b
    //     0xe637bc: ldur            w0, [x2, #0xb]
    // 0xe637c0: DecompressPointer r0
    //     0xe637c0: add             x0, x0, HEAP, lsl #32
    // 0xe637c4: stur            x0, [fp, #-8]
    // 0xe637c8: cmp             w0, NULL
    // 0xe637cc: b.eq            #0xe63824
    // 0xe637d0: mov             x1, x0
    // 0xe637d4: r0 = saveContext()
    //     0xe637d4: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe637d8: ldur            x1, [fp, #-8]
    // 0xe637dc: ldur            x2, [fp, #-0x28]
    // 0xe637e0: r0 = setTransform()
    //     0xe637e0: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe637e4: ldur            x1, [fp, #-0x20]
    // 0xe637e8: r0 = LoadClassIdInstr(r1)
    //     0xe637e8: ldur            x0, [x1, #-1]
    //     0xe637ec: ubfx            x0, x0, #0xc, #0x14
    // 0xe637f0: ldur            x2, [fp, #-0x18]
    // 0xe637f4: r0 = GDT[cid_x0 + -0xe8b]()
    //     0xe637f4: sub             lr, x0, #0xe8b
    //     0xe637f8: ldr             lr, [x21, lr, lsl #3]
    //     0xe637fc: blr             lr
    // 0xe63800: ldur            x1, [fp, #-8]
    // 0xe63804: r0 = restoreContext()
    //     0xe63804: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe63808: r0 = Null
    //     0xe63808: mov             x0, NULL
    // 0xe6380c: LeaveFrame
    //     0xe6380c: mov             SP, fp
    //     0xe63810: ldp             fp, lr, [SP], #0x10
    // 0xe63814: ret
    //     0xe63814: ret             
    // 0xe63818: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe63818: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6381c: b               #0xe63720
    // 0xe63820: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe63820: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe63824: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe63824: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ layout(/* No info */) {
    // ** addr: 0xe8e3d8, size: 0x188
    // 0xe8e3d8: EnterFrame
    //     0xe8e3d8: stp             fp, lr, [SP, #-0x10]!
    //     0xe8e3dc: mov             fp, SP
    // 0xe8e3e0: AllocStack(0x30)
    //     0xe8e3e0: sub             SP, SP, #0x30
    // 0xe8e3e4: SetupParameters(Padding this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0xe8e3e4: mov             x0, x3
    //     0xe8e3e8: stur            x3, [fp, #-0x20]
    //     0xe8e3ec: mov             x3, x1
    //     0xe8e3f0: stur            x1, [fp, #-0x10]
    //     0xe8e3f4: stur            x2, [fp, #-0x18]
    // 0xe8e3f8: CheckStackOverflow
    //     0xe8e3f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8e3fc: cmp             SP, x16
    //     0xe8e400: b.ls            #0xe8e550
    // 0xe8e404: LoadField: r4 = r3->field_f
    //     0xe8e404: ldur            w4, [x3, #0xf]
    // 0xe8e408: DecompressPointer r4
    //     0xe8e408: add             x4, x4, HEAP, lsl #32
    // 0xe8e40c: mov             x1, x2
    // 0xe8e410: stur            x4, [fp, #-8]
    // 0xe8e414: r0 = of()
    //     0xe8e414: bl              #0xb137c8  ; [package:pdf/src/widgets/text_style.dart] Directionality::of
    // 0xe8e418: ldur            x0, [fp, #-0x10]
    // 0xe8e41c: LoadField: r3 = r0->field_b
    //     0xe8e41c: ldur            w3, [x0, #0xb]
    // 0xe8e420: DecompressPointer r3
    //     0xe8e420: add             x3, x3, HEAP, lsl #32
    // 0xe8e424: stur            x3, [fp, #-0x28]
    // 0xe8e428: cmp             w3, NULL
    // 0xe8e42c: b.eq            #0xe8e4f8
    // 0xe8e430: ldur            x4, [fp, #-8]
    // 0xe8e434: ldur            x1, [fp, #-0x20]
    // 0xe8e438: mov             x2, x4
    // 0xe8e43c: r0 = deflate()
    //     0xe8e43c: bl              #0xe8e8bc  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::deflate
    // 0xe8e440: ldur            x4, [fp, #-0x28]
    // 0xe8e444: r1 = LoadClassIdInstr(r4)
    //     0xe8e444: ldur            x1, [x4, #-1]
    //     0xe8e448: ubfx            x1, x1, #0xc, #0x14
    // 0xe8e44c: mov             x3, x0
    // 0xe8e450: mov             x0, x1
    // 0xe8e454: mov             x1, x4
    // 0xe8e458: ldur            x2, [fp, #-0x18]
    // 0xe8e45c: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe8e45c: sub             lr, x0, #0xf89
    //     0xe8e460: ldr             lr, [x21, lr, lsl #3]
    //     0xe8e464: blr             lr
    // 0xe8e468: ldur            x0, [fp, #-0x28]
    // 0xe8e46c: LoadField: r1 = r0->field_7
    //     0xe8e46c: ldur            w1, [x0, #7]
    // 0xe8e470: DecompressPointer r1
    //     0xe8e470: add             x1, x1, HEAP, lsl #32
    // 0xe8e474: cmp             w1, NULL
    // 0xe8e478: b.eq            #0xe8e558
    // 0xe8e47c: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe8e47c: ldur            d0, [x1, #0x17]
    // 0xe8e480: ldur            x1, [fp, #-8]
    // 0xe8e484: stur            d0, [fp, #-0x30]
    // 0xe8e488: r0 = horizontal()
    //     0xe8e488: bl              #0xe89a80  ; [package:pdf/src/widgets/geometry.dart] EdgeInsetsGeometry::horizontal
    // 0xe8e48c: mov             v1.16b, v0.16b
    // 0xe8e490: ldur            d0, [fp, #-0x30]
    // 0xe8e494: fadd            d2, d0, d1
    // 0xe8e498: ldur            x0, [fp, #-0x28]
    // 0xe8e49c: LoadField: r1 = r0->field_7
    //     0xe8e49c: ldur            w1, [x0, #7]
    // 0xe8e4a0: DecompressPointer r1
    //     0xe8e4a0: add             x1, x1, HEAP, lsl #32
    // 0xe8e4a4: cmp             w1, NULL
    // 0xe8e4a8: b.eq            #0xe8e55c
    // 0xe8e4ac: LoadField: d0 = r1->field_1f
    //     0xe8e4ac: ldur            d0, [x1, #0x1f]
    // 0xe8e4b0: ldur            x0, [fp, #-8]
    // 0xe8e4b4: LoadField: d1 = r0->field_f
    //     0xe8e4b4: ldur            d1, [x0, #0xf]
    // 0xe8e4b8: LoadField: d3 = r0->field_1f
    //     0xe8e4b8: ldur            d3, [x0, #0x1f]
    // 0xe8e4bc: fadd            d4, d1, d3
    // 0xe8e4c0: fadd            d1, d0, d4
    // 0xe8e4c4: ldur            x1, [fp, #-0x20]
    // 0xe8e4c8: mov             v0.16b, v1.16b
    // 0xe8e4cc: mov             v1.16b, v2.16b
    // 0xe8e4d0: r0 = constrainRect()
    //     0xe8e4d0: bl              #0xe8e560  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainRect
    // 0xe8e4d4: ldur            x2, [fp, #-0x10]
    // 0xe8e4d8: StoreField: r2->field_7 = r0
    //     0xe8e4d8: stur            w0, [x2, #7]
    //     0xe8e4dc: ldurb           w16, [x2, #-1]
    //     0xe8e4e0: ldurb           w17, [x0, #-1]
    //     0xe8e4e4: and             x16, x17, x16, lsr #2
    //     0xe8e4e8: tst             x16, HEAP, lsr #32
    //     0xe8e4ec: b.eq            #0xe8e4f4
    //     0xe8e4f0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe8e4f4: b               #0xe8e540
    // 0xe8e4f8: mov             x2, x0
    // 0xe8e4fc: ldur            x0, [fp, #-8]
    // 0xe8e500: mov             x1, x0
    // 0xe8e504: r0 = horizontal()
    //     0xe8e504: bl              #0xe89a80  ; [package:pdf/src/widgets/geometry.dart] EdgeInsetsGeometry::horizontal
    // 0xe8e508: ldur            x1, [fp, #-8]
    // 0xe8e50c: stur            d0, [fp, #-0x30]
    // 0xe8e510: r0 = vertical()
    //     0xe8e510: bl              #0xe89a70  ; [package:pdf/src/widgets/geometry.dart] EdgeInsetsGeometry::vertical
    // 0xe8e514: ldur            x1, [fp, #-0x20]
    // 0xe8e518: ldur            d1, [fp, #-0x30]
    // 0xe8e51c: r0 = constrainRect()
    //     0xe8e51c: bl              #0xe8e560  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainRect
    // 0xe8e520: ldur            x1, [fp, #-0x10]
    // 0xe8e524: StoreField: r1->field_7 = r0
    //     0xe8e524: stur            w0, [x1, #7]
    //     0xe8e528: ldurb           w16, [x1, #-1]
    //     0xe8e52c: ldurb           w17, [x0, #-1]
    //     0xe8e530: and             x16, x17, x16, lsr #2
    //     0xe8e534: tst             x16, HEAP, lsr #32
    //     0xe8e538: b.eq            #0xe8e540
    //     0xe8e53c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8e540: r0 = Null
    //     0xe8e540: mov             x0, NULL
    // 0xe8e544: LeaveFrame
    //     0xe8e544: mov             SP, fp
    //     0xe8e548: ldp             fp, lr, [SP], #0x10
    // 0xe8e54c: ret
    //     0xe8e54c: ret             
    // 0xe8e550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8e550: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8e554: b               #0xe8e404
    // 0xe8e558: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe8e558: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe8e55c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe8e55c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
}

// class id: 6805, size: 0x14, field offset: 0x14
enum BoxFit extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4dd98, size: 0x64
    // 0xc4dd98: EnterFrame
    //     0xc4dd98: stp             fp, lr, [SP, #-0x10]!
    //     0xc4dd9c: mov             fp, SP
    // 0xc4dda0: AllocStack(0x10)
    //     0xc4dda0: sub             SP, SP, #0x10
    // 0xc4dda4: SetupParameters(BoxFit this /* r1 => r0, fp-0x8 */)
    //     0xc4dda4: mov             x0, x1
    //     0xc4dda8: stur            x1, [fp, #-8]
    // 0xc4ddac: CheckStackOverflow
    //     0xc4ddac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ddb0: cmp             SP, x16
    //     0xc4ddb4: b.ls            #0xc4ddf4
    // 0xc4ddb8: r1 = Null
    //     0xc4ddb8: mov             x1, NULL
    // 0xc4ddbc: r2 = 4
    //     0xc4ddbc: movz            x2, #0x4
    // 0xc4ddc0: r0 = AllocateArray()
    //     0xc4ddc0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ddc4: r16 = "BoxFit."
    //     0xc4ddc4: add             x16, PP, #0x33, lsl #12  ; [pp+0x338e8] "BoxFit."
    //     0xc4ddc8: ldr             x16, [x16, #0x8e8]
    // 0xc4ddcc: StoreField: r0->field_f = r16
    //     0xc4ddcc: stur            w16, [x0, #0xf]
    // 0xc4ddd0: ldur            x1, [fp, #-8]
    // 0xc4ddd4: LoadField: r2 = r1->field_f
    //     0xc4ddd4: ldur            w2, [x1, #0xf]
    // 0xc4ddd8: DecompressPointer r2
    //     0xc4ddd8: add             x2, x2, HEAP, lsl #32
    // 0xc4dddc: StoreField: r0->field_13 = r2
    //     0xc4dddc: stur            w2, [x0, #0x13]
    // 0xc4dde0: str             x0, [SP]
    // 0xc4dde4: r0 = _interpolate()
    //     0xc4dde4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4dde8: LeaveFrame
    //     0xc4dde8: mov             SP, fp
    //     0xc4ddec: ldp             fp, lr, [SP], #0x10
    // 0xc4ddf0: ret
    //     0xc4ddf0: ret             
    // 0xc4ddf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ddf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ddf8: b               #0xc4ddb8
  }
}
