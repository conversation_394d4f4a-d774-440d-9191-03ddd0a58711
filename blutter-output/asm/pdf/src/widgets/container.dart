// lib: , url: package:pdf/src/widgets/container.dart

// class id: 1050846, size: 0x8
class :: {
}

// class id: 801, size: 0x30, field offset: 0x10
class Container extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xeaf540, size: 0x94
    // 0xeaf540: EnterFrame
    //     0xeaf540: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf544: mov             fp, SP
    // 0xeaf548: AllocStack(0x20)
    //     0xeaf548: sub             SP, SP, #0x20
    // 0xeaf54c: SetupParameters(Container this /* r1 => r1, fp-0x18 */)
    //     0xeaf54c: stur            x1, [fp, #-0x18]
    // 0xeaf550: LoadField: r0 = r1->field_f
    //     0xeaf550: ldur            w0, [x1, #0xf]
    // 0xeaf554: DecompressPointer r0
    //     0xeaf554: add             x0, x0, HEAP, lsl #32
    // 0xeaf558: stur            x0, [fp, #-0x10]
    // 0xeaf55c: LoadField: r2 = r1->field_13
    //     0xeaf55c: ldur            w2, [x1, #0x13]
    // 0xeaf560: DecompressPointer r2
    //     0xeaf560: add             x2, x2, HEAP, lsl #32
    // 0xeaf564: stur            x2, [fp, #-8]
    // 0xeaf568: r0 = Align()
    //     0xeaf568: bl              #0xeaf5d4  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xeaf56c: mov             x1, x0
    // 0xeaf570: ldur            x0, [fp, #-8]
    // 0xeaf574: stur            x1, [fp, #-0x20]
    // 0xeaf578: StoreField: r1->field_f = r0
    //     0xeaf578: stur            w0, [x1, #0xf]
    // 0xeaf57c: ldur            x0, [fp, #-0x10]
    // 0xeaf580: StoreField: r1->field_b = r0
    //     0xeaf580: stur            w0, [x1, #0xb]
    // 0xeaf584: r0 = Padding()
    //     0xeaf584: bl              #0xb12350  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xeaf588: mov             x1, x0
    // 0xeaf58c: r0 = Instance_EdgeInsets
    //     0xeaf58c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e2a0] Obj!EdgeInsets@e0c501
    //     0xeaf590: ldr             x0, [x0, #0x2a0]
    // 0xeaf594: stur            x1, [fp, #-0x10]
    // 0xeaf598: StoreField: r1->field_f = r0
    //     0xeaf598: stur            w0, [x1, #0xf]
    // 0xeaf59c: ldur            x0, [fp, #-0x20]
    // 0xeaf5a0: StoreField: r1->field_b = r0
    //     0xeaf5a0: stur            w0, [x1, #0xb]
    // 0xeaf5a4: ldur            x0, [fp, #-0x18]
    // 0xeaf5a8: LoadField: r2 = r0->field_23
    //     0xeaf5a8: ldur            w2, [x0, #0x23]
    // 0xeaf5ac: DecompressPointer r2
    //     0xeaf5ac: add             x2, x2, HEAP, lsl #32
    // 0xeaf5b0: stur            x2, [fp, #-8]
    // 0xeaf5b4: r0 = ConstrainedBox()
    //     0xeaf5b4: bl              #0xe8f454  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xeaf5b8: ldur            x1, [fp, #-8]
    // 0xeaf5bc: StoreField: r0->field_f = r1
    //     0xeaf5bc: stur            w1, [x0, #0xf]
    // 0xeaf5c0: ldur            x1, [fp, #-0x10]
    // 0xeaf5c4: StoreField: r0->field_b = r1
    //     0xeaf5c4: stur            w1, [x0, #0xb]
    // 0xeaf5c8: LeaveFrame
    //     0xeaf5c8: mov             SP, fp
    //     0xeaf5cc: ldp             fp, lr, [SP], #0x10
    // 0xeaf5d0: ret
    //     0xeaf5d0: ret             
  }
}
