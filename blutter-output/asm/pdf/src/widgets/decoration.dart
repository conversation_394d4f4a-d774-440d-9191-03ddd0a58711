// lib: , url: package:pdf/src/widgets/decoration.dart

// class id: 1050847, size: 0x8
class :: {
}

// class id: 825, size: 0x24, field offset: 0x8
//   const constructor, 
class BoxDecoration extends Object {

  _ paint(/* No info */) {
    // ** addr: 0xe649ec, size: 0x140
    // 0xe649ec: EnterFrame
    //     0xe649ec: stp             fp, lr, [SP, #-0x10]!
    //     0xe649f0: mov             fp, SP
    // 0xe649f4: AllocStack(0x18)
    //     0xe649f4: sub             SP, SP, #0x18
    // 0xe649f8: SetupParameters(BoxDecoration this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0xe649f8: mov             x0, x2
    //     0xe649fc: mov             x2, x3
    //     0xe64a00: mov             x3, x1
    //     0xe64a04: stur            x1, [fp, #-0x10]
    //     0xe64a08: stur            x5, [fp, #-0x18]
    // 0xe64a0c: CheckStackOverflow
    //     0xe64a0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe64a10: cmp             SP, x16
    //     0xe64a14: b.ls            #0xe64b1c
    // 0xe64a18: r16 = Instance_PaintPhase
    //     0xe64a18: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e9a8] Obj!PaintPhase@e2e981
    //     0xe64a1c: ldr             x16, [x16, #0x9a8]
    // 0xe64a20: cmp             w5, w16
    // 0xe64a24: b.eq            #0xe64a38
    // 0xe64a28: r16 = Instance_PaintPhase
    //     0xe64a28: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e998] Obj!PaintPhase@e2e9c1
    //     0xe64a2c: ldr             x16, [x16, #0x998]
    // 0xe64a30: cmp             w5, w16
    // 0xe64a34: b.ne            #0xe64ae8
    // 0xe64a38: LoadField: r1 = r3->field_13
    //     0xe64a38: ldur            w1, [x3, #0x13]
    // 0xe64a3c: DecompressPointer r1
    //     0xe64a3c: add             x1, x1, HEAP, lsl #32
    // 0xe64a40: LoadField: r4 = r1->field_7
    //     0xe64a40: ldur            x4, [x1, #7]
    // 0xe64a44: cmp             x4, #0
    // 0xe64a48: b.gt            #0xe64aa4
    // 0xe64a4c: d0 = 2.000000
    //     0xe64a4c: fmov            d0, #2.00000000
    // 0xe64a50: LoadField: r4 = r0->field_b
    //     0xe64a50: ldur            w4, [x0, #0xb]
    // 0xe64a54: DecompressPointer r4
    //     0xe64a54: add             x4, x4, HEAP, lsl #32
    // 0xe64a58: stur            x4, [fp, #-8]
    // 0xe64a5c: cmp             w4, NULL
    // 0xe64a60: b.eq            #0xe64b24
    // 0xe64a64: LoadField: d1 = r2->field_7
    //     0xe64a64: ldur            d1, [x2, #7]
    // 0xe64a68: ArrayLoad: d2 = r2[0]  ; List_8
    //     0xe64a68: ldur            d2, [x2, #0x17]
    // 0xe64a6c: fdiv            d3, d2, d0
    // 0xe64a70: fadd            d2, d1, d3
    // 0xe64a74: LoadField: d1 = r2->field_f
    //     0xe64a74: ldur            d1, [x2, #0xf]
    // 0xe64a78: LoadField: d4 = r2->field_1f
    //     0xe64a78: ldur            d4, [x2, #0x1f]
    // 0xe64a7c: fdiv            d5, d4, d0
    // 0xe64a80: fadd            d0, d1, d5
    // 0xe64a84: mov             x1, x4
    // 0xe64a88: mov             v1.16b, v0.16b
    // 0xe64a8c: mov             v0.16b, v2.16b
    // 0xe64a90: mov             v2.16b, v3.16b
    // 0xe64a94: mov             v3.16b, v5.16b
    // 0xe64a98: r0 = drawEllipse()
    //     0xe64a98: bl              #0xe64d24  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawEllipse
    // 0xe64a9c: ldur            x3, [fp, #-8]
    // 0xe64aa0: b               #0xe64ac4
    // 0xe64aa4: LoadField: r3 = r0->field_b
    //     0xe64aa4: ldur            w3, [x0, #0xb]
    // 0xe64aa8: DecompressPointer r3
    //     0xe64aa8: add             x3, x3, HEAP, lsl #32
    // 0xe64aac: stur            x3, [fp, #-8]
    // 0xe64ab0: cmp             w3, NULL
    // 0xe64ab4: b.eq            #0xe64b28
    // 0xe64ab8: mov             x1, x3
    // 0xe64abc: r0 = drawBox()
    //     0xe64abc: bl              #0xe64ce4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawBox
    // 0xe64ac0: ldur            x3, [fp, #-8]
    // 0xe64ac4: ldur            x0, [fp, #-0x10]
    // 0xe64ac8: stur            x3, [fp, #-8]
    // 0xe64acc: LoadField: r2 = r0->field_7
    //     0xe64acc: ldur            w2, [x0, #7]
    // 0xe64ad0: DecompressPointer r2
    //     0xe64ad0: add             x2, x2, HEAP, lsl #32
    // 0xe64ad4: mov             x1, x3
    // 0xe64ad8: r0 = setFillColor()
    //     0xe64ad8: bl              #0xe64b2c  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setFillColor
    // 0xe64adc: ldur            x1, [fp, #-8]
    // 0xe64ae0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe64ae0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe64ae4: r0 = fillPath()
    //     0xe64ae4: bl              #0xe496d4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::fillPath
    // 0xe64ae8: ldur            x1, [fp, #-0x18]
    // 0xe64aec: r16 = Instance_PaintPhase
    //     0xe64aec: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e9a8] Obj!PaintPhase@e2e981
    //     0xe64af0: ldr             x16, [x16, #0x9a8]
    // 0xe64af4: cmp             w1, w16
    // 0xe64af8: b.eq            #0xe64b0c
    // 0xe64afc: r16 = Instance_PaintPhase
    //     0xe64afc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e9a0] Obj!PaintPhase@e2e9a1
    //     0xe64b00: ldr             x16, [x16, #0x9a0]
    // 0xe64b04: cmp             w1, w16
    // 0xe64b08: b.eq            #0xe64b0c
    // 0xe64b0c: r0 = Null
    //     0xe64b0c: mov             x0, NULL
    // 0xe64b10: LeaveFrame
    //     0xe64b10: mov             SP, fp
    //     0xe64b14: ldp             fp, lr, [SP], #0x10
    // 0xe64b18: ret
    //     0xe64b18: ret             
    // 0xe64b1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe64b1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe64b20: b               #0xe64a18
    // 0xe64b24: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe64b24: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe64b28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe64b28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 6802, size: 0x14, field offset: 0x14
enum PaintPhase extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4de60, size: 0x64
    // 0xc4de60: EnterFrame
    //     0xc4de60: stp             fp, lr, [SP, #-0x10]!
    //     0xc4de64: mov             fp, SP
    // 0xc4de68: AllocStack(0x10)
    //     0xc4de68: sub             SP, SP, #0x10
    // 0xc4de6c: SetupParameters(PaintPhase this /* r1 => r0, fp-0x8 */)
    //     0xc4de6c: mov             x0, x1
    //     0xc4de70: stur            x1, [fp, #-8]
    // 0xc4de74: CheckStackOverflow
    //     0xc4de74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4de78: cmp             SP, x16
    //     0xc4de7c: b.ls            #0xc4debc
    // 0xc4de80: r1 = Null
    //     0xc4de80: mov             x1, NULL
    // 0xc4de84: r2 = 4
    //     0xc4de84: movz            x2, #0x4
    // 0xc4de88: r0 = AllocateArray()
    //     0xc4de88: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4de8c: r16 = "PaintPhase."
    //     0xc4de8c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47518] "PaintPhase."
    //     0xc4de90: ldr             x16, [x16, #0x518]
    // 0xc4de94: StoreField: r0->field_f = r16
    //     0xc4de94: stur            w16, [x0, #0xf]
    // 0xc4de98: ldur            x1, [fp, #-8]
    // 0xc4de9c: LoadField: r2 = r1->field_f
    //     0xc4de9c: ldur            w2, [x1, #0xf]
    // 0xc4dea0: DecompressPointer r2
    //     0xc4dea0: add             x2, x2, HEAP, lsl #32
    // 0xc4dea4: StoreField: r0->field_13 = r2
    //     0xc4dea4: stur            w2, [x0, #0x13]
    // 0xc4dea8: str             x0, [SP]
    // 0xc4deac: r0 = _interpolate()
    //     0xc4deac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4deb0: LeaveFrame
    //     0xc4deb0: mov             SP, fp
    //     0xc4deb4: ldp             fp, lr, [SP], #0x10
    // 0xc4deb8: ret
    //     0xc4deb8: ret             
    // 0xc4debc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4debc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4dec0: b               #0xc4de80
  }
}

// class id: 6803, size: 0x14, field offset: 0x14
enum BoxShape extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ddfc, size: 0x64
    // 0xc4ddfc: EnterFrame
    //     0xc4ddfc: stp             fp, lr, [SP, #-0x10]!
    //     0xc4de00: mov             fp, SP
    // 0xc4de04: AllocStack(0x10)
    //     0xc4de04: sub             SP, SP, #0x10
    // 0xc4de08: SetupParameters(BoxShape this /* r1 => r0, fp-0x8 */)
    //     0xc4de08: mov             x0, x1
    //     0xc4de0c: stur            x1, [fp, #-8]
    // 0xc4de10: CheckStackOverflow
    //     0xc4de10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4de14: cmp             SP, x16
    //     0xc4de18: b.ls            #0xc4de58
    // 0xc4de1c: r1 = Null
    //     0xc4de1c: mov             x1, NULL
    // 0xc4de20: r2 = 4
    //     0xc4de20: movz            x2, #0x4
    // 0xc4de24: r0 = AllocateArray()
    //     0xc4de24: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4de28: r16 = "BoxShape."
    //     0xc4de28: add             x16, PP, #0x30, lsl #12  ; [pp+0x30d60] "BoxShape."
    //     0xc4de2c: ldr             x16, [x16, #0xd60]
    // 0xc4de30: StoreField: r0->field_f = r16
    //     0xc4de30: stur            w16, [x0, #0xf]
    // 0xc4de34: ldur            x1, [fp, #-8]
    // 0xc4de38: LoadField: r2 = r1->field_f
    //     0xc4de38: ldur            w2, [x1, #0xf]
    // 0xc4de3c: DecompressPointer r2
    //     0xc4de3c: add             x2, x2, HEAP, lsl #32
    // 0xc4de40: StoreField: r0->field_13 = r2
    //     0xc4de40: stur            w2, [x0, #0x13]
    // 0xc4de44: str             x0, [SP]
    // 0xc4de48: r0 = _interpolate()
    //     0xc4de48: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4de4c: LeaveFrame
    //     0xc4de4c: mov             SP, fp
    //     0xc4de50: ldp             fp, lr, [SP], #0x10
    // 0xc4de54: ret
    //     0xc4de54: ret             
    // 0xc4de58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4de58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4de5c: b               #0xc4de1c
  }
}
