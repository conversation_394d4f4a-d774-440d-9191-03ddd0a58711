// lib: , url: package:pdf/src/widgets/document.dart

// class id: 1050848, size: 0x8
class :: {
}

// class id: 824, size: 0x14, field offset: 0x8
class Document extends Object {

  _ save(/* No info */) async {
    // ** addr: 0xe88654, size: 0x104
    // 0xe88654: EnterFrame
    //     0xe88654: stp             fp, lr, [SP, #-0x10]!
    //     0xe88658: mov             fp, SP
    // 0xe8865c: AllocStack(0x28)
    //     0xe8865c: sub             SP, SP, #0x28
    // 0xe88660: SetupParameters(Document this /* r1 => r1, fp-0x10 */)
    //     0xe88660: stur            NULL, [fp, #-8]
    //     0xe88664: stur            x1, [fp, #-0x10]
    // 0xe88668: CheckStackOverflow
    //     0xe88668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8866c: cmp             SP, x16
    //     0xe88670: b.ls            #0xe88748
    // 0xe88674: InitAsync() -> Future<Uint8List>
    //     0xe88674: ldr             x0, [PP, #0xf80]  ; [pp+0xf80] TypeArguments: <Uint8List>
    //     0xe88678: bl              #0x661298  ; InitAsyncStub
    // 0xe8867c: ldur            x0, [fp, #-0x10]
    // 0xe88680: LoadField: r1 = r0->field_f
    //     0xe88680: ldur            w1, [x0, #0xf]
    // 0xe88684: DecompressPointer r1
    //     0xe88684: add             x1, x1, HEAP, lsl #32
    // 0xe88688: tbz             w1, #4, #0xe8870c
    // 0xe8868c: LoadField: r2 = r0->field_b
    //     0xe8868c: ldur            w2, [x0, #0xb]
    // 0xe88690: DecompressPointer r2
    //     0xe88690: add             x2, x2, HEAP, lsl #32
    // 0xe88694: stur            x2, [fp, #-0x28]
    // 0xe88698: LoadField: r1 = r2->field_b
    //     0xe88698: ldur            w1, [x2, #0xb]
    // 0xe8869c: r3 = LoadInt32Instr(r1)
    //     0xe8869c: sbfx            x3, x1, #1, #0x1f
    // 0xe886a0: stur            x3, [fp, #-0x20]
    // 0xe886a4: r1 = 0
    //     0xe886a4: movz            x1, #0
    // 0xe886a8: CheckStackOverflow
    //     0xe886a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe886ac: cmp             SP, x16
    //     0xe886b0: b.ls            #0xe88750
    // 0xe886b4: LoadField: r4 = r2->field_b
    //     0xe886b4: ldur            w4, [x2, #0xb]
    // 0xe886b8: r5 = LoadInt32Instr(r4)
    //     0xe886b8: sbfx            x5, x4, #1, #0x1f
    // 0xe886bc: cmp             x3, x5
    // 0xe886c0: b.ne            #0xe88728
    // 0xe886c4: cmp             x1, x5
    // 0xe886c8: b.ge            #0xe88704
    // 0xe886cc: LoadField: r4 = r2->field_f
    //     0xe886cc: ldur            w4, [x2, #0xf]
    // 0xe886d0: DecompressPointer r4
    //     0xe886d0: add             x4, x4, HEAP, lsl #32
    // 0xe886d4: ArrayLoad: r5 = r4[r1]  ; Unknown_4
    //     0xe886d4: add             x16, x4, x1, lsl #2
    //     0xe886d8: ldur            w5, [x16, #0xf]
    // 0xe886dc: DecompressPointer r5
    //     0xe886dc: add             x5, x5, HEAP, lsl #32
    // 0xe886e0: add             x4, x1, #1
    // 0xe886e4: mov             x1, x5
    // 0xe886e8: stur            x4, [fp, #-0x18]
    // 0xe886ec: r0 = postProcess()
    //     0xe886ec: bl              #0xe89028  ; [package:pdf/src/widgets/multi_page.dart] MultiPage::postProcess
    // 0xe886f0: ldur            x1, [fp, #-0x18]
    // 0xe886f4: ldur            x0, [fp, #-0x10]
    // 0xe886f8: ldur            x2, [fp, #-0x28]
    // 0xe886fc: ldur            x3, [fp, #-0x20]
    // 0xe88700: b               #0xe886a8
    // 0xe88704: r1 = true
    //     0xe88704: add             x1, NULL, #0x20  ; true
    // 0xe88708: StoreField: r0->field_f = r1
    //     0xe88708: stur            w1, [x0, #0xf]
    // 0xe8870c: LoadField: r1 = r0->field_7
    //     0xe8870c: ldur            w1, [x0, #7]
    // 0xe88710: DecompressPointer r1
    //     0xe88710: add             x1, x1, HEAP, lsl #32
    // 0xe88714: r0 = save()
    //     0xe88714: bl              #0xe88758  ; [package:pdf/src/pdf/document.dart] PdfDocument::save
    // 0xe88718: mov             x1, x0
    // 0xe8871c: stur            x1, [fp, #-0x10]
    // 0xe88720: r0 = Await()
    //     0xe88720: bl              #0x661044  ; AwaitStub
    // 0xe88724: r0 = ReturnAsync()
    //     0xe88724: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe88728: mov             x0, x2
    // 0xe8872c: r0 = ConcurrentModificationError()
    //     0xe8872c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe88730: mov             x1, x0
    // 0xe88734: ldur            x0, [fp, #-0x28]
    // 0xe88738: StoreField: r1->field_b = r0
    //     0xe88738: stur            w0, [x1, #0xb]
    // 0xe8873c: mov             x0, x1
    // 0xe88740: r0 = Throw()
    //     0xe88740: bl              #0xec04b8  ; ThrowStub
    // 0xe88744: brk             #0
    // 0xe88748: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88748: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8874c: b               #0xe88674
    // 0xe88750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88750: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88754: b               #0xe886b4
  }
  _ addPage(/* No info */) {
    // ** addr: 0xe89b0c, size: 0x10c
    // 0xe89b0c: EnterFrame
    //     0xe89b0c: stp             fp, lr, [SP, #-0x10]!
    //     0xe89b10: mov             fp, SP
    // 0xe89b14: AllocStack(0x20)
    //     0xe89b14: sub             SP, SP, #0x20
    // 0xe89b18: SetupParameters(Document this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe89b18: mov             x4, x1
    //     0xe89b1c: mov             x0, x2
    //     0xe89b20: stur            x1, [fp, #-8]
    //     0xe89b24: stur            x2, [fp, #-0x10]
    // 0xe89b28: CheckStackOverflow
    //     0xe89b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe89b2c: cmp             SP, x16
    //     0xe89b30: b.ls            #0xe89c10
    // 0xe89b34: mov             x1, x0
    // 0xe89b38: mov             x2, x4
    // 0xe89b3c: r3 = Null
    //     0xe89b3c: mov             x3, NULL
    // 0xe89b40: r0 = generate()
    //     0xe89b40: bl              #0xe89c18  ; [package:pdf/src/widgets/multi_page.dart] MultiPage::generate
    // 0xe89b44: ldur            x0, [fp, #-8]
    // 0xe89b48: LoadField: r3 = r0->field_b
    //     0xe89b48: ldur            w3, [x0, #0xb]
    // 0xe89b4c: DecompressPointer r3
    //     0xe89b4c: add             x3, x3, HEAP, lsl #32
    // 0xe89b50: stur            x3, [fp, #-0x18]
    // 0xe89b54: LoadField: r2 = r3->field_7
    //     0xe89b54: ldur            w2, [x3, #7]
    // 0xe89b58: DecompressPointer r2
    //     0xe89b58: add             x2, x2, HEAP, lsl #32
    // 0xe89b5c: ldur            x0, [fp, #-0x10]
    // 0xe89b60: r1 = Null
    //     0xe89b60: mov             x1, NULL
    // 0xe89b64: cmp             w2, NULL
    // 0xe89b68: b.eq            #0xe89b88
    // 0xe89b6c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe89b6c: ldur            w4, [x2, #0x17]
    // 0xe89b70: DecompressPointer r4
    //     0xe89b70: add             x4, x4, HEAP, lsl #32
    // 0xe89b74: r8 = X0
    //     0xe89b74: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe89b78: LoadField: r9 = r4->field_7
    //     0xe89b78: ldur            x9, [x4, #7]
    // 0xe89b7c: r3 = Null
    //     0xe89b7c: add             x3, PP, #0x36, lsl #12  ; [pp+0x36738] Null
    //     0xe89b80: ldr             x3, [x3, #0x738]
    // 0xe89b84: blr             x9
    // 0xe89b88: ldur            x0, [fp, #-0x18]
    // 0xe89b8c: LoadField: r1 = r0->field_b
    //     0xe89b8c: ldur            w1, [x0, #0xb]
    // 0xe89b90: LoadField: r2 = r0->field_f
    //     0xe89b90: ldur            w2, [x0, #0xf]
    // 0xe89b94: DecompressPointer r2
    //     0xe89b94: add             x2, x2, HEAP, lsl #32
    // 0xe89b98: LoadField: r3 = r2->field_b
    //     0xe89b98: ldur            w3, [x2, #0xb]
    // 0xe89b9c: r2 = LoadInt32Instr(r1)
    //     0xe89b9c: sbfx            x2, x1, #1, #0x1f
    // 0xe89ba0: stur            x2, [fp, #-0x20]
    // 0xe89ba4: r1 = LoadInt32Instr(r3)
    //     0xe89ba4: sbfx            x1, x3, #1, #0x1f
    // 0xe89ba8: cmp             x2, x1
    // 0xe89bac: b.ne            #0xe89bb8
    // 0xe89bb0: mov             x1, x0
    // 0xe89bb4: r0 = _growToNextCapacity()
    //     0xe89bb4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe89bb8: ldur            x2, [fp, #-0x18]
    // 0xe89bbc: ldur            x3, [fp, #-0x20]
    // 0xe89bc0: add             x4, x3, #1
    // 0xe89bc4: lsl             x5, x4, #1
    // 0xe89bc8: StoreField: r2->field_b = r5
    //     0xe89bc8: stur            w5, [x2, #0xb]
    // 0xe89bcc: LoadField: r1 = r2->field_f
    //     0xe89bcc: ldur            w1, [x2, #0xf]
    // 0xe89bd0: DecompressPointer r1
    //     0xe89bd0: add             x1, x1, HEAP, lsl #32
    // 0xe89bd4: ldur            x0, [fp, #-0x10]
    // 0xe89bd8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe89bd8: add             x25, x1, x3, lsl #2
    //     0xe89bdc: add             x25, x25, #0xf
    //     0xe89be0: str             w0, [x25]
    //     0xe89be4: tbz             w0, #0, #0xe89c00
    //     0xe89be8: ldurb           w16, [x1, #-1]
    //     0xe89bec: ldurb           w17, [x0, #-1]
    //     0xe89bf0: and             x16, x17, x16, lsr #2
    //     0xe89bf4: tst             x16, HEAP, lsr #32
    //     0xe89bf8: b.eq            #0xe89c00
    //     0xe89bfc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe89c00: r0 = Null
    //     0xe89c00: mov             x0, NULL
    // 0xe89c04: LeaveFrame
    //     0xe89c04: mov             SP, fp
    //     0xe89c08: ldp             fp, lr, [SP], #0x10
    // 0xe89c0c: ret
    //     0xe89c0c: ret             
    // 0xe89c10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe89c10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe89c14: b               #0xe89b34
  }
  _ Document(/* No info */) {
    // ** addr: 0xe8b00c, size: 0xa4
    // 0xe8b00c: EnterFrame
    //     0xe8b00c: stp             fp, lr, [SP, #-0x10]!
    //     0xe8b010: mov             fp, SP
    // 0xe8b014: AllocStack(0x10)
    //     0xe8b014: sub             SP, SP, #0x10
    // 0xe8b018: r0 = false
    //     0xe8b018: add             x0, NULL, #0x30  ; false
    // 0xe8b01c: mov             x3, x1
    // 0xe8b020: stur            x1, [fp, #-8]
    // 0xe8b024: CheckStackOverflow
    //     0xe8b024: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b028: cmp             SP, x16
    //     0xe8b02c: b.ls            #0xe8b0a8
    // 0xe8b030: StoreField: r3->field_f = r0
    //     0xe8b030: stur            w0, [x3, #0xf]
    // 0xe8b034: r1 = <Page>
    //     0xe8b034: add             x1, PP, #0x36, lsl #12  ; [pp+0x368b8] TypeArguments: <Page>
    //     0xe8b038: ldr             x1, [x1, #0x8b8]
    // 0xe8b03c: r2 = 0
    //     0xe8b03c: movz            x2, #0
    // 0xe8b040: r0 = _GrowableList()
    //     0xe8b040: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe8b044: ldur            x1, [fp, #-8]
    // 0xe8b048: StoreField: r1->field_b = r0
    //     0xe8b048: stur            w0, [x1, #0xb]
    //     0xe8b04c: ldurb           w16, [x1, #-1]
    //     0xe8b050: ldurb           w17, [x0, #-1]
    //     0xe8b054: and             x16, x17, x16, lsr #2
    //     0xe8b058: tst             x16, HEAP, lsr #32
    //     0xe8b05c: b.eq            #0xe8b064
    //     0xe8b060: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8b064: r0 = PdfDocument()
    //     0xe8b064: bl              #0xe8b55c  ; AllocatePdfDocumentStub -> PdfDocument (size=0x38)
    // 0xe8b068: mov             x1, x0
    // 0xe8b06c: stur            x0, [fp, #-0x10]
    // 0xe8b070: r0 = PdfDocument()
    //     0xe8b070: bl              #0xe8b0b0  ; [package:pdf/src/pdf/document.dart] PdfDocument::PdfDocument
    // 0xe8b074: ldur            x0, [fp, #-0x10]
    // 0xe8b078: ldur            x1, [fp, #-8]
    // 0xe8b07c: StoreField: r1->field_7 = r0
    //     0xe8b07c: stur            w0, [x1, #7]
    //     0xe8b080: ldurb           w16, [x1, #-1]
    //     0xe8b084: ldurb           w17, [x0, #-1]
    //     0xe8b088: and             x16, x17, x16, lsr #2
    //     0xe8b08c: tst             x16, HEAP, lsr #32
    //     0xe8b090: b.eq            #0xe8b098
    //     0xe8b094: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8b098: r0 = Null
    //     0xe8b098: mov             x0, NULL
    // 0xe8b09c: LeaveFrame
    //     0xe8b09c: mov             SP, fp
    //     0xe8b0a0: ldp             fp, lr, [SP], #0x10
    // 0xe8b0a4: ret
    //     0xe8b0a4: ret             
    // 0xe8b0a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b0a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b0ac: b               #0xe8b030
  }
}
