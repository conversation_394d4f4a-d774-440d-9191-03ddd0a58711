// lib: , url: package:pdf/src/widgets/flex.dart

// class id: 1050849, size: 0x8
class :: {
}

// class id: 792, size: 0x10, field offset: 0x10
//   transformed mixin,
abstract class _Flex&MultiChildWidget&SpanningWidget extends MultiChildWidget
     with SpanningWidget {

  _ cloneContext(/* No info */) {
    // ** addr: 0xe3ff3c, size: 0x38
    // 0xe3ff3c: EnterFrame
    //     0xe3ff3c: stp             fp, lr, [SP, #-0x10]!
    //     0xe3ff40: mov             fp, SP
    // 0xe3ff44: CheckStackOverflow
    //     0xe3ff44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3ff48: cmp             SP, x16
    //     0xe3ff4c: b.ls            #0xe3ff6c
    // 0xe3ff50: LoadField: r0 = r1->field_23
    //     0xe3ff50: ldur            w0, [x1, #0x23]
    // 0xe3ff54: DecompressPointer r0
    //     0xe3ff54: add             x0, x0, HEAP, lsl #32
    // 0xe3ff58: mov             x1, x0
    // 0xe3ff5c: r0 = clone()
    //     0xe3ff5c: bl              #0xeabfe0  ; [package:pdf/src/widgets/flex.dart] FlexContext::clone
    // 0xe3ff60: LeaveFrame
    //     0xe3ff60: mov             SP, fp
    //     0xe3ff64: ldp             fp, lr, [SP], #0x10
    // 0xe3ff68: ret
    //     0xe3ff68: ret             
    // 0xe3ff6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3ff6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3ff70: b               #0xe3ff50
  }
  _ applyContext(/* No info */) {
    // ** addr: 0xe4c3f8, size: 0x3c
    // 0xe4c3f8: EnterFrame
    //     0xe4c3f8: stp             fp, lr, [SP, #-0x10]!
    //     0xe4c3fc: mov             fp, SP
    // 0xe4c400: CheckStackOverflow
    //     0xe4c400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4c404: cmp             SP, x16
    //     0xe4c408: b.ls            #0xe4c42c
    // 0xe4c40c: LoadField: r0 = r1->field_23
    //     0xe4c40c: ldur            w0, [x1, #0x23]
    // 0xe4c410: DecompressPointer r0
    //     0xe4c410: add             x0, x0, HEAP, lsl #32
    // 0xe4c414: mov             x1, x0
    // 0xe4c418: r0 = apply()
    //     0xe4c418: bl              #0xeabe5c  ; [package:pdf/src/widgets/flex.dart] FlexContext::apply
    // 0xe4c41c: r0 = Null
    //     0xe4c41c: mov             x0, NULL
    // 0xe4c420: LeaveFrame
    //     0xe4c420: mov             SP, fp
    //     0xe4c424: ldp             fp, lr, [SP], #0x10
    // 0xe4c428: ret
    //     0xe4c428: ret             
    // 0xe4c42c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4c42c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4c430: b               #0xe4c40c
  }
}

// class id: 793, size: 0x28, field offset: 0x10
abstract class Flex extends _Flex&MultiChildWidget&SpanningWidget {

  _ Flex(/* No info */) {
    // ** addr: 0xb13bdc, size: 0xec
    // 0xb13bdc: EnterFrame
    //     0xb13bdc: stp             fp, lr, [SP, #-0x10]!
    //     0xb13be0: mov             fp, SP
    // 0xb13be4: AllocStack(0x20)
    //     0xb13be4: sub             SP, SP, #0x20
    // 0xb13be8: SetupParameters(Flex this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0xb13be8: stur            x1, [fp, #-8]
    //     0xb13bec: mov             x16, x3
    //     0xb13bf0: mov             x3, x1
    //     0xb13bf4: mov             x1, x16
    //     0xb13bf8: mov             x0, x5
    //     0xb13bfc: stur            x2, [fp, #-0x10]
    //     0xb13c00: stur            x1, [fp, #-0x18]
    //     0xb13c04: stur            x5, [fp, #-0x20]
    // 0xb13c08: r0 = FlexContext()
    //     0xb13c08: bl              #0xb13cc8  ; AllocateFlexContextStub -> FlexContext (size=0x18)
    // 0xb13c0c: StoreField: r0->field_7 = rZR
    //     0xb13c0c: stur            xzr, [x0, #7]
    // 0xb13c10: StoreField: r0->field_f = rZR
    //     0xb13c10: stur            xzr, [x0, #0xf]
    // 0xb13c14: ldur            x1, [fp, #-8]
    // 0xb13c18: StoreField: r1->field_23 = r0
    //     0xb13c18: stur            w0, [x1, #0x23]
    //     0xb13c1c: ldurb           w16, [x1, #-1]
    //     0xb13c20: ldurb           w17, [x0, #-1]
    //     0xb13c24: and             x16, x17, x16, lsr #2
    //     0xb13c28: tst             x16, HEAP, lsr #32
    //     0xb13c2c: b.eq            #0xb13c34
    //     0xb13c30: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb13c34: ldur            x0, [fp, #-0x20]
    // 0xb13c38: StoreField: r1->field_f = r0
    //     0xb13c38: stur            w0, [x1, #0xf]
    //     0xb13c3c: ldurb           w16, [x1, #-1]
    //     0xb13c40: ldurb           w17, [x0, #-1]
    //     0xb13c44: and             x16, x17, x16, lsr #2
    //     0xb13c48: tst             x16, HEAP, lsr #32
    //     0xb13c4c: b.eq            #0xb13c54
    //     0xb13c50: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb13c54: r2 = Instance_MainAxisAlignment
    //     0xb13c54: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e310] Obj!MainAxisAlignment@e2e901
    //     0xb13c58: ldr             x2, [x2, #0x310]
    // 0xb13c5c: StoreField: r1->field_13 = r2
    //     0xb13c5c: stur            w2, [x1, #0x13]
    // 0xb13c60: r2 = Instance_MainAxisSize
    //     0xb13c60: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e318] Obj!MainAxisSize@e2e921
    //     0xb13c64: ldr             x2, [x2, #0x318]
    // 0xb13c68: ArrayStore: r1[0] = r2  ; List_4
    //     0xb13c68: stur            w2, [x1, #0x17]
    // 0xb13c6c: ldur            x0, [fp, #-0x18]
    // 0xb13c70: StoreField: r1->field_1b = r0
    //     0xb13c70: stur            w0, [x1, #0x1b]
    //     0xb13c74: ldurb           w16, [x1, #-1]
    //     0xb13c78: ldurb           w17, [x0, #-1]
    //     0xb13c7c: and             x16, x17, x16, lsr #2
    //     0xb13c80: tst             x16, HEAP, lsr #32
    //     0xb13c84: b.eq            #0xb13c8c
    //     0xb13c88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb13c8c: r2 = Instance_VerticalDirection
    //     0xb13c8c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e320] Obj!VerticalDirection@e2e881
    //     0xb13c90: ldr             x2, [x2, #0x320]
    // 0xb13c94: StoreField: r1->field_1f = r2
    //     0xb13c94: stur            w2, [x1, #0x1f]
    // 0xb13c98: ldur            x0, [fp, #-0x10]
    // 0xb13c9c: StoreField: r1->field_b = r0
    //     0xb13c9c: stur            w0, [x1, #0xb]
    //     0xb13ca0: ldurb           w16, [x1, #-1]
    //     0xb13ca4: ldurb           w17, [x0, #-1]
    //     0xb13ca8: and             x16, x17, x16, lsr #2
    //     0xb13cac: tst             x16, HEAP, lsr #32
    //     0xb13cb0: b.eq            #0xb13cb8
    //     0xb13cb4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb13cb8: r0 = Null
    //     0xb13cb8: mov             x0, NULL
    // 0xb13cbc: LeaveFrame
    //     0xb13cbc: mov             SP, fp
    //     0xb13cc0: ldp             fp, lr, [SP], #0x10
    // 0xb13cc4: ret
    //     0xb13cc4: ret             
  }
  _ restoreContext(/* No info */) {
    // ** addr: 0xe54c04, size: 0x7c
    // 0xe54c04: EnterFrame
    //     0xe54c04: stp             fp, lr, [SP, #-0x10]!
    //     0xe54c08: mov             fp, SP
    // 0xe54c0c: AllocStack(0x10)
    //     0xe54c0c: sub             SP, SP, #0x10
    // 0xe54c10: SetupParameters(Flex this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe54c10: mov             x0, x2
    //     0xe54c14: mov             x4, x1
    //     0xe54c18: mov             x3, x2
    //     0xe54c1c: stur            x1, [fp, #-8]
    //     0xe54c20: stur            x2, [fp, #-0x10]
    // 0xe54c24: r2 = Null
    //     0xe54c24: mov             x2, NULL
    // 0xe54c28: r1 = Null
    //     0xe54c28: mov             x1, NULL
    // 0xe54c2c: r4 = 60
    //     0xe54c2c: movz            x4, #0x3c
    // 0xe54c30: branchIfSmi(r0, 0xe54c3c)
    //     0xe54c30: tbz             w0, #0, #0xe54c3c
    // 0xe54c34: r4 = LoadClassIdInstr(r0)
    //     0xe54c34: ldur            x4, [x0, #-1]
    //     0xe54c38: ubfx            x4, x4, #0xc, #0x14
    // 0xe54c3c: cmp             x4, #0x337
    // 0xe54c40: b.eq            #0xe54c58
    // 0xe54c44: r8 = FlexContext
    //     0xe54c44: add             x8, PP, #0x33, lsl #12  ; [pp+0x338d0] Type: FlexContext
    //     0xe54c48: ldr             x8, [x8, #0x8d0]
    // 0xe54c4c: r3 = Null
    //     0xe54c4c: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3edb0] Null
    //     0xe54c50: ldr             x3, [x3, #0xdb0]
    // 0xe54c54: r0 = DefaultTypeTest()
    //     0xe54c54: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe54c58: ldur            x1, [fp, #-8]
    // 0xe54c5c: LoadField: r2 = r1->field_23
    //     0xe54c5c: ldur            w2, [x1, #0x23]
    // 0xe54c60: DecompressPointer r2
    //     0xe54c60: add             x2, x2, HEAP, lsl #32
    // 0xe54c64: ldur            x1, [fp, #-0x10]
    // 0xe54c68: LoadField: r3 = r1->field_f
    //     0xe54c68: ldur            x3, [x1, #0xf]
    // 0xe54c6c: StoreField: r2->field_7 = r3
    //     0xe54c6c: stur            x3, [x2, #7]
    // 0xe54c70: r0 = Null
    //     0xe54c70: mov             x0, NULL
    // 0xe54c74: LeaveFrame
    //     0xe54c74: mov             SP, fp
    //     0xe54c78: ldp             fp, lr, [SP], #0x10
    // 0xe54c7c: ret
    //     0xe54c7c: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0xe68658, size: 0x1f8
    // 0xe68658: EnterFrame
    //     0xe68658: stp             fp, lr, [SP, #-0x10]!
    //     0xe6865c: mov             fp, SP
    // 0xe68660: AllocStack(0x40)
    //     0xe68660: sub             SP, SP, #0x40
    // 0xe68664: SetupParameters(Flex this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe68664: stur            x1, [fp, #-8]
    //     0xe68668: stur            x2, [fp, #-0x10]
    // 0xe6866c: CheckStackOverflow
    //     0xe6866c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe68670: cmp             SP, x16
    //     0xe68674: b.ls            #0xe68838
    // 0xe68678: r0 = Matrix4()
    //     0xe68678: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe6867c: r4 = 32
    //     0xe6867c: movz            x4, #0x20
    // 0xe68680: stur            x0, [fp, #-0x18]
    // 0xe68684: r0 = AllocateFloat64Array()
    //     0xe68684: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe68688: mov             x1, x0
    // 0xe6868c: ldur            x0, [fp, #-0x18]
    // 0xe68690: StoreField: r0->field_7 = r1
    //     0xe68690: stur            w1, [x0, #7]
    // 0xe68694: mov             x1, x0
    // 0xe68698: r0 = setIdentity()
    //     0xe68698: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe6869c: ldur            x0, [fp, #-8]
    // 0xe686a0: LoadField: r1 = r0->field_7
    //     0xe686a0: ldur            w1, [x0, #7]
    // 0xe686a4: DecompressPointer r1
    //     0xe686a4: add             x1, x1, HEAP, lsl #32
    // 0xe686a8: cmp             w1, NULL
    // 0xe686ac: b.eq            #0xe68840
    // 0xe686b0: LoadField: d0 = r1->field_7
    //     0xe686b0: ldur            d0, [x1, #7]
    // 0xe686b4: LoadField: d1 = r1->field_f
    //     0xe686b4: ldur            d1, [x1, #0xf]
    // 0xe686b8: ldur            x1, [fp, #-0x18]
    // 0xe686bc: r0 = translate()
    //     0xe686bc: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe686c0: ldur            x2, [fp, #-0x10]
    // 0xe686c4: LoadField: r0 = r2->field_b
    //     0xe686c4: ldur            w0, [x2, #0xb]
    // 0xe686c8: DecompressPointer r0
    //     0xe686c8: add             x0, x0, HEAP, lsl #32
    // 0xe686cc: stur            x0, [fp, #-0x20]
    // 0xe686d0: cmp             w0, NULL
    // 0xe686d4: b.eq            #0xe68844
    // 0xe686d8: mov             x1, x0
    // 0xe686dc: r0 = saveContext()
    //     0xe686dc: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe686e0: ldur            x1, [fp, #-0x20]
    // 0xe686e4: ldur            x2, [fp, #-0x18]
    // 0xe686e8: r0 = setTransform()
    //     0xe686e8: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe686ec: ldur            x0, [fp, #-8]
    // 0xe686f0: LoadField: r2 = r0->field_b
    //     0xe686f0: ldur            w2, [x0, #0xb]
    // 0xe686f4: DecompressPointer r2
    //     0xe686f4: add             x2, x2, HEAP, lsl #32
    // 0xe686f8: LoadField: r1 = r0->field_23
    //     0xe686f8: ldur            w1, [x0, #0x23]
    // 0xe686fc: DecompressPointer r1
    //     0xe686fc: add             x1, x1, HEAP, lsl #32
    // 0xe68700: LoadField: r3 = r1->field_7
    //     0xe68700: ldur            x3, [x1, #7]
    // 0xe68704: LoadField: r4 = r1->field_f
    //     0xe68704: ldur            x4, [x1, #0xf]
    // 0xe68708: r0 = BoxInt64Instr(r4)
    //     0xe68708: sbfiz           x0, x4, #1, #0x1f
    //     0xe6870c: cmp             x4, x0, asr #1
    //     0xe68710: b.eq            #0xe6871c
    //     0xe68714: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe68718: stur            x4, [x0, #7]
    // 0xe6871c: str             x0, [SP]
    // 0xe68720: mov             x1, x2
    // 0xe68724: mov             x2, x3
    // 0xe68728: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe68728: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe6872c: r0 = sublist()
    //     0xe6872c: bl              #0x6eabd0  ; [dart:core] _GrowableList::sublist
    // 0xe68730: mov             x3, x0
    // 0xe68734: stur            x3, [fp, #-0x38]
    // 0xe68738: LoadField: r4 = r3->field_7
    //     0xe68738: ldur            w4, [x3, #7]
    // 0xe6873c: DecompressPointer r4
    //     0xe6873c: add             x4, x4, HEAP, lsl #32
    // 0xe68740: stur            x4, [fp, #-0x18]
    // 0xe68744: LoadField: r0 = r3->field_b
    //     0xe68744: ldur            w0, [x3, #0xb]
    // 0xe68748: r5 = LoadInt32Instr(r0)
    //     0xe68748: sbfx            x5, x0, #1, #0x1f
    // 0xe6874c: stur            x5, [fp, #-0x30]
    // 0xe68750: r0 = 0
    //     0xe68750: movz            x0, #0
    // 0xe68754: CheckStackOverflow
    //     0xe68754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe68758: cmp             SP, x16
    //     0xe6875c: b.ls            #0xe68848
    // 0xe68760: LoadField: r1 = r3->field_b
    //     0xe68760: ldur            w1, [x3, #0xb]
    // 0xe68764: r2 = LoadInt32Instr(r1)
    //     0xe68764: sbfx            x2, x1, #1, #0x1f
    // 0xe68768: cmp             x5, x2
    // 0xe6876c: b.ne            #0xe68818
    // 0xe68770: cmp             x0, x2
    // 0xe68774: b.ge            #0xe68800
    // 0xe68778: LoadField: r1 = r3->field_f
    //     0xe68778: ldur            w1, [x3, #0xf]
    // 0xe6877c: DecompressPointer r1
    //     0xe6877c: add             x1, x1, HEAP, lsl #32
    // 0xe68780: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0xe68780: add             x16, x1, x0, lsl #2
    //     0xe68784: ldur            w6, [x16, #0xf]
    // 0xe68788: DecompressPointer r6
    //     0xe68788: add             x6, x6, HEAP, lsl #32
    // 0xe6878c: stur            x6, [fp, #-8]
    // 0xe68790: add             x7, x0, #1
    // 0xe68794: stur            x7, [fp, #-0x28]
    // 0xe68798: cmp             w6, NULL
    // 0xe6879c: b.ne            #0xe687d0
    // 0xe687a0: mov             x0, x6
    // 0xe687a4: mov             x2, x4
    // 0xe687a8: r1 = Null
    //     0xe687a8: mov             x1, NULL
    // 0xe687ac: cmp             w2, NULL
    // 0xe687b0: b.eq            #0xe687d0
    // 0xe687b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe687b4: ldur            w4, [x2, #0x17]
    // 0xe687b8: DecompressPointer r4
    //     0xe687b8: add             x4, x4, HEAP, lsl #32
    // 0xe687bc: r8 = X0
    //     0xe687bc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe687c0: LoadField: r9 = r4->field_7
    //     0xe687c0: ldur            x9, [x4, #7]
    // 0xe687c4: r3 = Null
    //     0xe687c4: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3edc0] Null
    //     0xe687c8: ldr             x3, [x3, #0xdc0]
    // 0xe687cc: blr             x9
    // 0xe687d0: ldur            x1, [fp, #-8]
    // 0xe687d4: r0 = LoadClassIdInstr(r1)
    //     0xe687d4: ldur            x0, [x1, #-1]
    //     0xe687d8: ubfx            x0, x0, #0xc, #0x14
    // 0xe687dc: ldur            x2, [fp, #-0x10]
    // 0xe687e0: r0 = GDT[cid_x0 + -0xe8b]()
    //     0xe687e0: sub             lr, x0, #0xe8b
    //     0xe687e4: ldr             lr, [x21, lr, lsl #3]
    //     0xe687e8: blr             lr
    // 0xe687ec: ldur            x0, [fp, #-0x28]
    // 0xe687f0: ldur            x3, [fp, #-0x38]
    // 0xe687f4: ldur            x4, [fp, #-0x18]
    // 0xe687f8: ldur            x5, [fp, #-0x30]
    // 0xe687fc: b               #0xe68754
    // 0xe68800: ldur            x1, [fp, #-0x20]
    // 0xe68804: r0 = restoreContext()
    //     0xe68804: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe68808: r0 = Null
    //     0xe68808: mov             x0, NULL
    // 0xe6880c: LeaveFrame
    //     0xe6880c: mov             SP, fp
    //     0xe68810: ldp             fp, lr, [SP], #0x10
    // 0xe68814: ret
    //     0xe68814: ret             
    // 0xe68818: mov             x0, x3
    // 0xe6881c: r0 = ConcurrentModificationError()
    //     0xe6881c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe68820: mov             x1, x0
    // 0xe68824: ldur            x0, [fp, #-0x38]
    // 0xe68828: StoreField: r1->field_b = r0
    //     0xe68828: stur            w0, [x1, #0xb]
    // 0xe6882c: mov             x0, x1
    // 0xe68830: r0 = Throw()
    //     0xe68830: bl              #0xec04b8  ; ThrowStub
    // 0xe68834: brk             #0
    // 0xe68838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe68838: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6883c: b               #0xe68678
    // 0xe68840: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe68840: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe68844: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe68844: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe68848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe68848: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6884c: b               #0xe68760
  }
  get _ canSpan(/* No info */) {
    // ** addr: 0xe7db68, size: 0x24
    // 0xe7db68: LoadField: r2 = r1->field_f
    //     0xe7db68: ldur            w2, [x1, #0xf]
    // 0xe7db6c: DecompressPointer r2
    //     0xe7db6c: add             x2, x2, HEAP, lsl #32
    // 0xe7db70: r16 = Instance_Axis
    //     0xe7db70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e2f0] Obj!Axis@e2e961
    //     0xe7db74: ldr             x16, [x16, #0x2f0]
    // 0xe7db78: cmp             w2, w16
    // 0xe7db7c: r16 = true
    //     0xe7db7c: add             x16, NULL, #0x20  ; true
    // 0xe7db80: r17 = false
    //     0xe7db80: add             x17, NULL, #0x30  ; false
    // 0xe7db84: csel            x0, x16, x17, eq
    // 0xe7db88: ret
    //     0xe7db88: ret             
  }
  _ layout(/* No info */) {
    // ** addr: 0xe9f1ec, size: 0xc74
    // 0xe9f1ec: EnterFrame
    //     0xe9f1ec: stp             fp, lr, [SP, #-0x10]!
    //     0xe9f1f0: mov             fp, SP
    // 0xe9f1f4: AllocStack(0xd8)
    //     0xe9f1f4: sub             SP, SP, #0xd8
    // 0xe9f1f8: SetupParameters(Flex this /* r1 => r4, fp-0x30 */, dynamic _ /* r2 => r3, fp-0x38 */, dynamic _ /* r3 => r0, fp-0x40 */)
    //     0xe9f1f8: mov             x4, x1
    //     0xe9f1fc: mov             x0, x3
    //     0xe9f200: stur            x3, [fp, #-0x40]
    //     0xe9f204: mov             x3, x2
    //     0xe9f208: stur            x1, [fp, #-0x30]
    //     0xe9f20c: stur            x2, [fp, #-0x38]
    // 0xe9f210: CheckStackOverflow
    //     0xe9f210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9f214: cmp             SP, x16
    //     0xe9f218: b.ls            #0xe9fdec
    // 0xe9f21c: LoadField: r5 = r4->field_f
    //     0xe9f21c: ldur            w5, [x4, #0xf]
    // 0xe9f220: DecompressPointer r5
    //     0xe9f220: add             x5, x5, HEAP, lsl #32
    // 0xe9f224: stur            x5, [fp, #-0x28]
    // 0xe9f228: r16 = Instance_Axis
    //     0xe9f228: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e300] Obj!Axis@e2e941
    //     0xe9f22c: ldr             x16, [x16, #0x300]
    // 0xe9f230: cmp             w5, w16
    // 0xe9f234: b.ne            #0xe9f244
    // 0xe9f238: LoadField: d0 = r0->field_f
    //     0xe9f238: ldur            d0, [x0, #0xf]
    // 0xe9f23c: mov             v1.16b, v0.16b
    // 0xe9f240: b               #0xe9f24c
    // 0xe9f244: LoadField: d0 = r0->field_1f
    //     0xe9f244: ldur            d0, [x0, #0x1f]
    // 0xe9f248: mov             v1.16b, v0.16b
    // 0xe9f24c: d0 = inf
    //     0xe9f24c: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe9f250: stur            d1, [fp, #-0x88]
    // 0xe9f254: fcmp            d0, d1
    // 0xe9f258: r16 = true
    //     0xe9f258: add             x16, NULL, #0x20  ; true
    // 0xe9f25c: r17 = false
    //     0xe9f25c: add             x17, NULL, #0x30  ; false
    // 0xe9f260: csel            x6, x16, x17, gt
    // 0xe9f264: stur            x6, [fp, #-0x20]
    // 0xe9f268: LoadField: r7 = r4->field_23
    //     0xe9f268: ldur            w7, [x4, #0x23]
    // 0xe9f26c: DecompressPointer r7
    //     0xe9f26c: add             x7, x7, HEAP, lsl #32
    // 0xe9f270: stur            x7, [fp, #-0x18]
    // 0xe9f274: LoadField: r8 = r7->field_7
    //     0xe9f274: ldur            x8, [x7, #7]
    // 0xe9f278: stur            x8, [fp, #-0x10]
    // 0xe9f27c: LoadField: r9 = r4->field_b
    //     0xe9f27c: ldur            w9, [x4, #0xb]
    // 0xe9f280: DecompressPointer r9
    //     0xe9f280: add             x9, x9, HEAP, lsl #32
    // 0xe9f284: mov             x1, x9
    // 0xe9f288: mov             x2, x8
    // 0xe9f28c: stur            x9, [fp, #-8]
    // 0xe9f290: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe9f290: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe9f294: r0 = sublist()
    //     0xe9f294: bl              #0x6eabd0  ; [dart:core] _GrowableList::sublist
    // 0xe9f298: mov             x3, x0
    // 0xe9f29c: stur            x3, [fp, #-0x80]
    // 0xe9f2a0: LoadField: r4 = r3->field_7
    //     0xe9f2a0: ldur            w4, [x3, #7]
    // 0xe9f2a4: DecompressPointer r4
    //     0xe9f2a4: add             x4, x4, HEAP, lsl #32
    // 0xe9f2a8: stur            x4, [fp, #-0x78]
    // 0xe9f2ac: LoadField: r0 = r3->field_b
    //     0xe9f2ac: ldur            w0, [x3, #0xb]
    // 0xe9f2b0: r5 = LoadInt32Instr(r0)
    //     0xe9f2b0: sbfx            x5, x0, #1, #0x1f
    // 0xe9f2b4: ldur            x6, [fp, #-0x30]
    // 0xe9f2b8: stur            x5, [fp, #-0x70]
    // 0xe9f2bc: LoadField: r7 = r6->field_1b
    //     0xe9f2bc: ldur            w7, [x6, #0x1b]
    // 0xe9f2c0: DecompressPointer r7
    //     0xe9f2c0: add             x7, x7, HEAP, lsl #32
    // 0xe9f2c4: ldur            x8, [fp, #-0x28]
    // 0xe9f2c8: stur            x7, [fp, #-0x68]
    // 0xe9f2cc: LoadField: r9 = r8->field_7
    //     0xe9f2cc: ldur            x9, [x8, #7]
    // 0xe9f2d0: ldur            x10, [fp, #-0x40]
    // 0xe9f2d4: stur            x9, [fp, #-0x60]
    // 0xe9f2d8: LoadField: d0 = r10->field_f
    //     0xe9f2d8: ldur            d0, [x10, #0xf]
    // 0xe9f2dc: stur            d0, [fp, #-0xa0]
    // 0xe9f2e0: LoadField: d1 = r10->field_1f
    //     0xe9f2e0: ldur            d1, [x10, #0x1f]
    // 0xe9f2e4: stur            d1, [fp, #-0x98]
    // 0xe9f2e8: ldur            x12, [fp, #-0x10]
    // 0xe9f2ec: r11 = 0.000000
    //     0xe9f2ec: ldr             x11, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe9f2f0: d2 = 0.000000
    //     0xe9f2f0: eor             v2.16b, v2.16b, v2.16b
    // 0xe9f2f4: r0 = 0
    //     0xe9f2f4: movz            x0, #0
    // 0xe9f2f8: stur            x12, [fp, #-0x50]
    // 0xe9f2fc: stur            x11, [fp, #-0x58]
    // 0xe9f300: stur            d2, [fp, #-0x90]
    // 0xe9f304: CheckStackOverflow
    //     0xe9f304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9f308: cmp             SP, x16
    //     0xe9f30c: b.ls            #0xe9fdf4
    // 0xe9f310: LoadField: r1 = r3->field_b
    //     0xe9f310: ldur            w1, [x3, #0xb]
    // 0xe9f314: r2 = LoadInt32Instr(r1)
    //     0xe9f314: sbfx            x2, x1, #1, #0x1f
    // 0xe9f318: cmp             x5, x2
    // 0xe9f31c: b.ne            #0xe9fdcc
    // 0xe9f320: cmp             x0, x2
    // 0xe9f324: b.ge            #0xe9f69c
    // 0xe9f328: LoadField: r1 = r3->field_f
    //     0xe9f328: ldur            w1, [x3, #0xf]
    // 0xe9f32c: DecompressPointer r1
    //     0xe9f32c: add             x1, x1, HEAP, lsl #32
    // 0xe9f330: ArrayLoad: r13 = r1[r0]  ; Unknown_4
    //     0xe9f330: add             x16, x1, x0, lsl #2
    //     0xe9f334: ldur            w13, [x16, #0xf]
    // 0xe9f338: DecompressPointer r13
    //     0xe9f338: add             x13, x13, HEAP, lsl #32
    // 0xe9f33c: stur            x13, [fp, #-0x48]
    // 0xe9f340: add             x14, x0, #1
    // 0xe9f344: stur            x14, [fp, #-0x10]
    // 0xe9f348: cmp             w13, NULL
    // 0xe9f34c: b.ne            #0xe9f380
    // 0xe9f350: mov             x0, x13
    // 0xe9f354: mov             x2, x4
    // 0xe9f358: r1 = Null
    //     0xe9f358: mov             x1, NULL
    // 0xe9f35c: cmp             w2, NULL
    // 0xe9f360: b.eq            #0xe9f380
    // 0xe9f364: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe9f364: ldur            w4, [x2, #0x17]
    // 0xe9f368: DecompressPointer r4
    //     0xe9f368: add             x4, x4, HEAP, lsl #32
    // 0xe9f36c: r8 = X0
    //     0xe9f36c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe9f370: LoadField: r9 = r4->field_7
    //     0xe9f370: ldur            x9, [x4, #7]
    // 0xe9f374: r3 = Null
    //     0xe9f374: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3edd0] Null
    //     0xe9f378: ldr             x3, [x3, #0xdd0]
    // 0xe9f37c: blr             x9
    // 0xe9f380: ldur            x0, [fp, #-0x68]
    // 0xe9f384: r16 = Instance_CrossAxisAlignment
    //     0xe9f384: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e2e8] Obj!CrossAxisAlignment@e2e8e1
    //     0xe9f388: ldr             x16, [x16, #0x2e8]
    // 0xe9f38c: cmp             w0, w16
    // 0xe9f390: b.ne            #0xe9f404
    // 0xe9f394: ldur            x1, [fp, #-0x60]
    // 0xe9f398: cmp             x1, #0
    // 0xe9f39c: b.gt            #0xe9f3d0
    // 0xe9f3a0: ldur            d0, [fp, #-0x98]
    // 0xe9f3a4: r0 = BoxConstraints()
    //     0xe9f3a4: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe9f3a8: mov             x1, x0
    // 0xe9f3ac: StoreField: r1->field_7 = rZR
    //     0xe9f3ac: stur            xzr, [x1, #7]
    // 0xe9f3b0: d0 = inf
    //     0xe9f3b0: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe9f3b4: StoreField: r1->field_f = d0
    //     0xe9f3b4: stur            d0, [x1, #0xf]
    // 0xe9f3b8: ldur            d1, [fp, #-0x98]
    // 0xe9f3bc: ArrayStore: r1[0] = d1  ; List_8
    //     0xe9f3bc: stur            d1, [x1, #0x17]
    // 0xe9f3c0: StoreField: r1->field_1f = d1
    //     0xe9f3c0: stur            d1, [x1, #0x1f]
    // 0xe9f3c4: mov             v1.16b, v0.16b
    // 0xe9f3c8: ldur            d0, [fp, #-0xa0]
    // 0xe9f3cc: b               #0xe9f3fc
    // 0xe9f3d0: ldur            d2, [fp, #-0xa0]
    // 0xe9f3d4: ldur            d1, [fp, #-0x98]
    // 0xe9f3d8: d0 = inf
    //     0xe9f3d8: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe9f3dc: r0 = BoxConstraints()
    //     0xe9f3dc: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe9f3e0: mov             x1, x0
    // 0xe9f3e4: ldur            d0, [fp, #-0xa0]
    // 0xe9f3e8: StoreField: r1->field_7 = d0
    //     0xe9f3e8: stur            d0, [x1, #7]
    // 0xe9f3ec: StoreField: r1->field_f = d0
    //     0xe9f3ec: stur            d0, [x1, #0xf]
    // 0xe9f3f0: ArrayStore: r1[0] = rZR  ; List_8
    //     0xe9f3f0: stur            xzr, [x1, #0x17]
    // 0xe9f3f4: d1 = inf
    //     0xe9f3f4: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe9f3f8: StoreField: r1->field_1f = d1
    //     0xe9f3f8: stur            d1, [x1, #0x1f]
    // 0xe9f3fc: mov             x3, x1
    // 0xe9f400: b               #0xe9f478
    // 0xe9f404: ldur            x0, [fp, #-0x60]
    // 0xe9f408: ldur            d0, [fp, #-0xa0]
    // 0xe9f40c: d1 = inf
    //     0xe9f40c: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe9f410: cmp             x0, #0
    // 0xe9f414: b.gt            #0xe9f448
    // 0xe9f418: ldur            d2, [fp, #-0x98]
    // 0xe9f41c: r0 = BoxConstraints()
    //     0xe9f41c: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe9f420: mov             x1, x0
    // 0xe9f424: StoreField: r1->field_7 = rZR
    //     0xe9f424: stur            xzr, [x1, #7]
    // 0xe9f428: d0 = inf
    //     0xe9f428: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe9f42c: StoreField: r1->field_f = d0
    //     0xe9f42c: stur            d0, [x1, #0xf]
    // 0xe9f430: ArrayStore: r1[0] = rZR  ; List_8
    //     0xe9f430: stur            xzr, [x1, #0x17]
    // 0xe9f434: ldur            d1, [fp, #-0x98]
    // 0xe9f438: StoreField: r1->field_1f = d1
    //     0xe9f438: stur            d1, [x1, #0x1f]
    // 0xe9f43c: mov             v1.16b, v0.16b
    // 0xe9f440: ldur            d0, [fp, #-0xa0]
    // 0xe9f444: b               #0xe9f474
    // 0xe9f448: mov             v2.16b, v0.16b
    // 0xe9f44c: mov             v0.16b, v1.16b
    // 0xe9f450: ldur            d1, [fp, #-0x98]
    // 0xe9f454: r0 = BoxConstraints()
    //     0xe9f454: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe9f458: mov             x1, x0
    // 0xe9f45c: StoreField: r1->field_7 = rZR
    //     0xe9f45c: stur            xzr, [x1, #7]
    // 0xe9f460: ldur            d0, [fp, #-0xa0]
    // 0xe9f464: StoreField: r1->field_f = d0
    //     0xe9f464: stur            d0, [x1, #0xf]
    // 0xe9f468: ArrayStore: r1[0] = rZR  ; List_8
    //     0xe9f468: stur            xzr, [x1, #0x17]
    // 0xe9f46c: d1 = inf
    //     0xe9f46c: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe9f470: StoreField: r1->field_1f = d1
    //     0xe9f470: stur            d1, [x1, #0x1f]
    // 0xe9f474: mov             x3, x1
    // 0xe9f478: ldur            x4, [fp, #-0x60]
    // 0xe9f47c: ldur            x5, [fp, #-0x48]
    // 0xe9f480: r0 = LoadClassIdInstr(r5)
    //     0xe9f480: ldur            x0, [x5, #-1]
    //     0xe9f484: ubfx            x0, x0, #0xc, #0x14
    // 0xe9f488: mov             x1, x5
    // 0xe9f48c: ldur            x2, [fp, #-0x38]
    // 0xe9f490: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe9f490: sub             lr, x0, #0xf89
    //     0xe9f494: ldr             lr, [x21, lr, lsl #3]
    //     0xe9f498: blr             lr
    // 0xe9f49c: ldur            x1, [fp, #-0x60]
    // 0xe9f4a0: cmp             x1, #0
    // 0xe9f4a4: b.gt            #0xe9f4cc
    // 0xe9f4a8: ldur            x0, [fp, #-0x48]
    // 0xe9f4ac: LoadField: r2 = r0->field_7
    //     0xe9f4ac: ldur            w2, [x0, #7]
    // 0xe9f4b0: DecompressPointer r2
    //     0xe9f4b0: add             x2, x2, HEAP, lsl #32
    // 0xe9f4b4: cmp             w2, NULL
    // 0xe9f4b8: b.eq            #0xe9fdfc
    // 0xe9f4bc: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xe9f4bc: ldur            d0, [x2, #0x17]
    // 0xe9f4c0: mov             v1.16b, v0.16b
    // 0xe9f4c4: mov             x0, x2
    // 0xe9f4c8: b               #0xe9f4ec
    // 0xe9f4cc: ldur            x0, [fp, #-0x48]
    // 0xe9f4d0: LoadField: r2 = r0->field_7
    //     0xe9f4d0: ldur            w2, [x0, #7]
    // 0xe9f4d4: DecompressPointer r2
    //     0xe9f4d4: add             x2, x2, HEAP, lsl #32
    // 0xe9f4d8: cmp             w2, NULL
    // 0xe9f4dc: b.eq            #0xe9fe00
    // 0xe9f4e0: LoadField: d0 = r2->field_1f
    //     0xe9f4e0: ldur            d0, [x2, #0x1f]
    // 0xe9f4e4: mov             v1.16b, v0.16b
    // 0xe9f4e8: mov             x0, x2
    // 0xe9f4ec: ldur            d0, [fp, #-0x90]
    // 0xe9f4f0: fadd            d2, d0, d1
    // 0xe9f4f4: stur            d2, [fp, #-0xb0]
    // 0xe9f4f8: cmp             x1, #0
    // 0xe9f4fc: b.gt            #0xe9f508
    // 0xe9f500: LoadField: d0 = r0->field_1f
    //     0xe9f500: ldur            d0, [x0, #0x1f]
    // 0xe9f504: b               #0xe9f50c
    // 0xe9f508: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe9f508: ldur            d0, [x0, #0x17]
    // 0xe9f50c: ldur            x2, [fp, #-0x58]
    // 0xe9f510: stur            d0, [fp, #-0xa8]
    // 0xe9f514: r3 = inline_Allocate_Double()
    //     0xe9f514: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xe9f518: add             x3, x3, #0x10
    //     0xe9f51c: cmp             x0, x3
    //     0xe9f520: b.ls            #0xe9fe04
    //     0xe9f524: str             x3, [THR, #0x50]  ; THR::top
    //     0xe9f528: sub             x3, x3, #0xf
    //     0xe9f52c: movz            x0, #0xe15c
    //     0xe9f530: movk            x0, #0x3, lsl #16
    //     0xe9f534: stur            x0, [x3, #-1]
    // 0xe9f538: StoreField: r3->field_7 = d0
    //     0xe9f538: stur            d0, [x3, #7]
    // 0xe9f53c: stur            x3, [fp, #-0x48]
    // 0xe9f540: r0 = 60
    //     0xe9f540: movz            x0, #0x3c
    // 0xe9f544: branchIfSmi(r2, 0xe9f550)
    //     0xe9f544: tbz             w2, #0, #0xe9f550
    // 0xe9f548: r0 = LoadClassIdInstr(r2)
    //     0xe9f548: ldur            x0, [x2, #-1]
    //     0xe9f54c: ubfx            x0, x0, #0xc, #0x14
    // 0xe9f550: stp             x3, x2, [SP]
    // 0xe9f554: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xe9f554: sub             lr, x0, #0xfe3
    //     0xe9f558: ldr             lr, [x21, lr, lsl #3]
    //     0xe9f55c: blr             lr
    // 0xe9f560: tbnz            w0, #4, #0xe9f570
    // 0xe9f564: ldur            x1, [fp, #-0x58]
    // 0xe9f568: d1 = 0.000000
    //     0xe9f568: eor             v1.16b, v1.16b, v1.16b
    // 0xe9f56c: b               #0xe9f624
    // 0xe9f570: ldur            x1, [fp, #-0x58]
    // 0xe9f574: r0 = 60
    //     0xe9f574: movz            x0, #0x3c
    // 0xe9f578: branchIfSmi(r1, 0xe9f584)
    //     0xe9f578: tbz             w1, #0, #0xe9f584
    // 0xe9f57c: r0 = LoadClassIdInstr(r1)
    //     0xe9f57c: ldur            x0, [x1, #-1]
    //     0xe9f580: ubfx            x0, x0, #0xc, #0x14
    // 0xe9f584: ldur            x16, [fp, #-0x48]
    // 0xe9f588: stp             x16, x1, [SP]
    // 0xe9f58c: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xe9f58c: sub             lr, x0, #0xfd2
    //     0xe9f590: ldr             lr, [x21, lr, lsl #3]
    //     0xe9f594: blr             lr
    // 0xe9f598: tbnz            w0, #4, #0xe9f5a8
    // 0xe9f59c: ldur            x1, [fp, #-0x48]
    // 0xe9f5a0: d1 = 0.000000
    //     0xe9f5a0: eor             v1.16b, v1.16b, v1.16b
    // 0xe9f5a4: b               #0xe9f624
    // 0xe9f5a8: ldur            x1, [fp, #-0x58]
    // 0xe9f5ac: r0 = 60
    //     0xe9f5ac: movz            x0, #0x3c
    // 0xe9f5b0: branchIfSmi(r1, 0xe9f5bc)
    //     0xe9f5b0: tbz             w1, #0, #0xe9f5bc
    // 0xe9f5b4: r0 = LoadClassIdInstr(r1)
    //     0xe9f5b4: ldur            x0, [x1, #-1]
    //     0xe9f5b8: ubfx            x0, x0, #0xc, #0x14
    // 0xe9f5bc: cmp             x0, #0x3e
    // 0xe9f5c0: b.ne            #0xe9f610
    // 0xe9f5c4: d1 = 0.000000
    //     0xe9f5c4: eor             v1.16b, v1.16b, v1.16b
    // 0xe9f5c8: LoadField: d0 = r1->field_7
    //     0xe9f5c8: ldur            d0, [x1, #7]
    // 0xe9f5cc: fcmp            d0, d1
    // 0xe9f5d0: b.ne            #0xe9f608
    // 0xe9f5d4: ldur            d2, [fp, #-0xa8]
    // 0xe9f5d8: fadd            d3, d0, d2
    // 0xe9f5dc: r1 = inline_Allocate_Double()
    //     0xe9f5dc: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe9f5e0: add             x1, x1, #0x10
    //     0xe9f5e4: cmp             x0, x1
    //     0xe9f5e8: b.ls            #0xe9fe20
    //     0xe9f5ec: str             x1, [THR, #0x50]  ; THR::top
    //     0xe9f5f0: sub             x1, x1, #0xf
    //     0xe9f5f4: movz            x0, #0xe15c
    //     0xe9f5f8: movk            x0, #0x3, lsl #16
    //     0xe9f5fc: stur            x0, [x1, #-1]
    // 0xe9f600: StoreField: r1->field_7 = d3
    //     0xe9f600: stur            d3, [x1, #7]
    // 0xe9f604: b               #0xe9f624
    // 0xe9f608: ldur            d2, [fp, #-0xa8]
    // 0xe9f60c: b               #0xe9f618
    // 0xe9f610: ldur            d2, [fp, #-0xa8]
    // 0xe9f614: d1 = 0.000000
    //     0xe9f614: eor             v1.16b, v1.16b, v1.16b
    // 0xe9f618: fcmp            d2, d2
    // 0xe9f61c: b.vc            #0xe9f624
    // 0xe9f620: ldur            x1, [fp, #-0x48]
    // 0xe9f624: ldur            x2, [fp, #-0x28]
    // 0xe9f628: r16 = Instance_Axis
    //     0xe9f628: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e2f0] Obj!Axis@e2e961
    //     0xe9f62c: ldr             x16, [x16, #0x2f0]
    // 0xe9f630: cmp             w2, w16
    // 0xe9f634: b.ne            #0xe9f654
    // 0xe9f638: ldur            d0, [fp, #-0xb0]
    // 0xe9f63c: ldur            d3, [fp, #-0x98]
    // 0xe9f640: fcmp            d0, d3
    // 0xe9f644: b.le            #0xe9f65c
    // 0xe9f648: mov             x4, x1
    // 0xe9f64c: ldur            x0, [fp, #-0x50]
    // 0xe9f650: b               #0xe9f6b4
    // 0xe9f654: ldur            d0, [fp, #-0xb0]
    // 0xe9f658: ldur            d3, [fp, #-0x98]
    // 0xe9f65c: ldur            x0, [fp, #-0x50]
    // 0xe9f660: add             x12, x0, #1
    // 0xe9f664: mov             x11, x1
    // 0xe9f668: mov             v2.16b, v0.16b
    // 0xe9f66c: ldur            x0, [fp, #-0x10]
    // 0xe9f670: ldur            x6, [fp, #-0x30]
    // 0xe9f674: ldur            x10, [fp, #-0x40]
    // 0xe9f678: mov             x8, x2
    // 0xe9f67c: ldur            x3, [fp, #-0x80]
    // 0xe9f680: ldur            x7, [fp, #-0x68]
    // 0xe9f684: ldur            x9, [fp, #-0x60]
    // 0xe9f688: ldur            d0, [fp, #-0xa0]
    // 0xe9f68c: mov             v1.16b, v3.16b
    // 0xe9f690: ldur            x4, [fp, #-0x78]
    // 0xe9f694: ldur            x5, [fp, #-0x70]
    // 0xe9f698: b               #0xe9f2f8
    // 0xe9f69c: mov             x2, x8
    // 0xe9f6a0: mov             x1, x11
    // 0xe9f6a4: mov             v0.16b, v2.16b
    // 0xe9f6a8: mov             x0, x12
    // 0xe9f6ac: d1 = 0.000000
    //     0xe9f6ac: eor             v1.16b, v1.16b, v1.16b
    // 0xe9f6b0: mov             x4, x1
    // 0xe9f6b4: ldur            x1, [fp, #-0x20]
    // 0xe9f6b8: ldur            x3, [fp, #-0x18]
    // 0xe9f6bc: stur            x4, [fp, #-0x48]
    // 0xe9f6c0: stur            d0, [fp, #-0x90]
    // 0xe9f6c4: StoreField: r3->field_f = r0
    //     0xe9f6c4: stur            x0, [x3, #0xf]
    // 0xe9f6c8: LoadField: r5 = r3->field_7
    //     0xe9f6c8: ldur            x5, [x3, #7]
    // 0xe9f6cc: sub             x6, x0, x5
    // 0xe9f6d0: stur            x6, [fp, #-0x10]
    // 0xe9f6d4: tbz             w1, #4, #0xe9f6d8
    // 0xe9f6d8: tbnz            w1, #4, #0xe9f6e4
    // 0xe9f6dc: ldur            d2, [fp, #-0x88]
    // 0xe9f6e0: b               #0xe9f6e8
    // 0xe9f6e4: mov             v2.16b, v0.16b
    // 0xe9f6e8: ldur            x0, [fp, #-0x60]
    // 0xe9f6ec: stur            d2, [fp, #-0x88]
    // 0xe9f6f0: cmp             x0, #0
    // 0xe9f6f4: b.gt            #0xe9f734
    // 0xe9f6f8: r0 = PdfPoint()
    //     0xe9f6f8: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe9f6fc: ldur            d0, [fp, #-0x88]
    // 0xe9f700: StoreField: r0->field_7 = d0
    //     0xe9f700: stur            d0, [x0, #7]
    // 0xe9f704: ldur            x1, [fp, #-0x48]
    // 0xe9f708: LoadField: d0 = r1->field_7
    //     0xe9f708: ldur            d0, [x1, #7]
    // 0xe9f70c: StoreField: r0->field_f = d0
    //     0xe9f70c: stur            d0, [x0, #0xf]
    // 0xe9f710: ldur            x1, [fp, #-0x40]
    // 0xe9f714: mov             x2, x0
    // 0xe9f718: r0 = constrain()
    //     0xe9f718: bl              #0xe9fe7c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrain
    // 0xe9f71c: LoadField: d0 = r0->field_7
    //     0xe9f71c: ldur            d0, [x0, #7]
    // 0xe9f720: LoadField: d1 = r0->field_f
    //     0xe9f720: ldur            d1, [x0, #0xf]
    // 0xe9f724: mov             v2.16b, v1.16b
    // 0xe9f728: mov             v1.16b, v0.16b
    // 0xe9f72c: mov             x3, x0
    // 0xe9f730: b               #0xe9f778
    // 0xe9f734: mov             x1, x4
    // 0xe9f738: mov             v0.16b, v2.16b
    // 0xe9f73c: LoadField: d1 = r1->field_7
    //     0xe9f73c: ldur            d1, [x1, #7]
    // 0xe9f740: stur            d1, [fp, #-0x98]
    // 0xe9f744: r0 = PdfPoint()
    //     0xe9f744: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe9f748: ldur            d0, [fp, #-0x98]
    // 0xe9f74c: StoreField: r0->field_7 = d0
    //     0xe9f74c: stur            d0, [x0, #7]
    // 0xe9f750: ldur            d0, [fp, #-0x88]
    // 0xe9f754: StoreField: r0->field_f = d0
    //     0xe9f754: stur            d0, [x0, #0xf]
    // 0xe9f758: ldur            x1, [fp, #-0x40]
    // 0xe9f75c: mov             x2, x0
    // 0xe9f760: r0 = constrain()
    //     0xe9f760: bl              #0xe9fe7c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrain
    // 0xe9f764: LoadField: d0 = r0->field_f
    //     0xe9f764: ldur            d0, [x0, #0xf]
    // 0xe9f768: LoadField: d1 = r0->field_7
    //     0xe9f768: ldur            d1, [x0, #7]
    // 0xe9f76c: mov             v2.16b, v1.16b
    // 0xe9f770: mov             v1.16b, v0.16b
    // 0xe9f774: mov             x3, x0
    // 0xe9f778: ldur            x0, [fp, #-0x30]
    // 0xe9f77c: ldur            d0, [fp, #-0x90]
    // 0xe9f780: stur            d2, [fp, #-0x88]
    // 0xe9f784: stur            d1, [fp, #-0x98]
    // 0xe9f788: r1 = Null
    //     0xe9f788: mov             x1, NULL
    // 0xe9f78c: r2 = Instance_PdfPoint
    //     0xe9f78c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xe9f790: ldr             x2, [x2, #0x730]
    // 0xe9f794: r0 = PdfRect.fromPoints()
    //     0xe9f794: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xe9f798: ldur            x2, [fp, #-0x30]
    // 0xe9f79c: StoreField: r2->field_7 = r0
    //     0xe9f79c: stur            w0, [x2, #7]
    //     0xe9f7a0: ldurb           w16, [x2, #-1]
    //     0xe9f7a4: ldurb           w17, [x0, #-1]
    //     0xe9f7a8: and             x16, x17, x16, lsr #2
    //     0xe9f7ac: tst             x16, HEAP, lsr #32
    //     0xe9f7b0: b.eq            #0xe9f7b8
    //     0xe9f7b4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe9f7b8: ldur            d0, [fp, #-0x90]
    // 0xe9f7bc: ldur            d1, [fp, #-0x98]
    // 0xe9f7c0: fsub            d2, d1, d0
    // 0xe9f7c4: d0 = 0.000000
    //     0xe9f7c4: eor             v0.16b, v0.16b, v0.16b
    // 0xe9f7c8: fcmp            d0, d2
    // 0xe9f7cc: b.le            #0xe9f7d8
    // 0xe9f7d0: d0 = 0.000000
    //     0xe9f7d0: eor             v0.16b, v0.16b, v0.16b
    // 0xe9f7d4: b               #0xe9f810
    // 0xe9f7d8: fcmp            d2, d0
    // 0xe9f7dc: b.le            #0xe9f7e8
    // 0xe9f7e0: mov             v0.16b, v2.16b
    // 0xe9f7e4: b               #0xe9f810
    // 0xe9f7e8: fcmp            d0, d0
    // 0xe9f7ec: b.ne            #0xe9f7fc
    // 0xe9f7f0: fadd            d3, d2, d0
    // 0xe9f7f4: mov             v0.16b, v3.16b
    // 0xe9f7f8: b               #0xe9f810
    // 0xe9f7fc: fcmp            d2, d2
    // 0xe9f800: b.vc            #0xe9f80c
    // 0xe9f804: mov             v0.16b, v2.16b
    // 0xe9f808: b               #0xe9f810
    // 0xe9f80c: d0 = 0.000000
    //     0xe9f80c: eor             v0.16b, v0.16b, v0.16b
    // 0xe9f810: ldur            x1, [fp, #-0x38]
    // 0xe9f814: stur            d0, [fp, #-0x90]
    // 0xe9f818: r0 = of()
    //     0xe9f818: bl              #0xb137c8  ; [package:pdf/src/widgets/text_style.dart] Directionality::of
    // 0xe9f81c: ldur            x1, [fp, #-0x30]
    // 0xe9f820: ldur            x2, [fp, #-0x28]
    // 0xe9f824: r0 = _startIsTopLeft()
    //     0xe9f824: bl              #0xe9fe60  ; [package:pdf/src/widgets/flex.dart] Flex::_startIsTopLeft
    // 0xe9f828: cmp             w0, NULL
    // 0xe9f82c: b.ne            #0xe9f834
    // 0xe9f830: r0 = true
    //     0xe9f830: add             x0, NULL, #0x20  ; true
    // 0xe9f834: ldur            x3, [fp, #-0x30]
    // 0xe9f838: eor             x4, x0, #0x10
    // 0xe9f83c: stur            x4, [fp, #-0x20]
    // 0xe9f840: LoadField: r0 = r3->field_13
    //     0xe9f840: ldur            w0, [x3, #0x13]
    // 0xe9f844: DecompressPointer r0
    //     0xe9f844: add             x0, x0, HEAP, lsl #32
    // 0xe9f848: LoadField: r1 = r0->field_7
    //     0xe9f848: ldur            x1, [x0, #7]
    // 0xe9f84c: cmp             x1, #2
    // 0xe9f850: b.gt            #0xe9f898
    // 0xe9f854: cmp             x1, #1
    // 0xe9f858: b.gt            #0xe9f884
    // 0xe9f85c: cmp             x1, #0
    // 0xe9f860: b.gt            #0xe9f874
    // 0xe9f864: d2 = 0.000000
    //     0xe9f864: eor             v2.16b, v2.16b, v2.16b
    // 0xe9f868: d0 = 0.000000
    //     0xe9f868: eor             v0.16b, v0.16b, v0.16b
    // 0xe9f86c: d1 = 2.000000
    //     0xe9f86c: fmov            d1, #2.00000000
    // 0xe9f870: b               #0xe9f92c
    // 0xe9f874: ldur            d2, [fp, #-0x90]
    // 0xe9f878: d0 = 0.000000
    //     0xe9f878: eor             v0.16b, v0.16b, v0.16b
    // 0xe9f87c: d1 = 2.000000
    //     0xe9f87c: fmov            d1, #2.00000000
    // 0xe9f880: b               #0xe9f92c
    // 0xe9f884: ldur            d0, [fp, #-0x90]
    // 0xe9f888: d1 = 2.000000
    //     0xe9f888: fmov            d1, #2.00000000
    // 0xe9f88c: fdiv            d2, d0, d1
    // 0xe9f890: d0 = 0.000000
    //     0xe9f890: eor             v0.16b, v0.16b, v0.16b
    // 0xe9f894: b               #0xe9f92c
    // 0xe9f898: ldur            d0, [fp, #-0x90]
    // 0xe9f89c: d1 = 2.000000
    //     0xe9f89c: fmov            d1, #2.00000000
    // 0xe9f8a0: cmp             x1, #4
    // 0xe9f8a4: b.gt            #0xe9f904
    // 0xe9f8a8: cmp             x1, #3
    // 0xe9f8ac: b.gt            #0xe9f8dc
    // 0xe9f8b0: ldur            x0, [fp, #-0x10]
    // 0xe9f8b4: cmp             x0, #1
    // 0xe9f8b8: b.le            #0xe9f8d0
    // 0xe9f8bc: sub             x1, x0, #1
    // 0xe9f8c0: scvtf           d2, x1
    // 0xe9f8c4: fdiv            d3, d0, d2
    // 0xe9f8c8: mov             v0.16b, v3.16b
    // 0xe9f8cc: b               #0xe9f8d4
    // 0xe9f8d0: d0 = 0.000000
    //     0xe9f8d0: eor             v0.16b, v0.16b, v0.16b
    // 0xe9f8d4: d2 = 0.000000
    //     0xe9f8d4: eor             v2.16b, v2.16b, v2.16b
    // 0xe9f8d8: b               #0xe9f92c
    // 0xe9f8dc: ldur            x0, [fp, #-0x10]
    // 0xe9f8e0: cmp             x0, #0
    // 0xe9f8e4: b.le            #0xe9f8f8
    // 0xe9f8e8: scvtf           d2, x0
    // 0xe9f8ec: fdiv            d3, d0, d2
    // 0xe9f8f0: mov             v0.16b, v3.16b
    // 0xe9f8f4: b               #0xe9f8fc
    // 0xe9f8f8: d0 = 0.000000
    //     0xe9f8f8: eor             v0.16b, v0.16b, v0.16b
    // 0xe9f8fc: fdiv            d2, d0, d1
    // 0xe9f900: b               #0xe9f92c
    // 0xe9f904: ldur            x0, [fp, #-0x10]
    // 0xe9f908: cmp             x0, #0
    // 0xe9f90c: b.le            #0xe9f924
    // 0xe9f910: add             x1, x0, #1
    // 0xe9f914: scvtf           d2, x1
    // 0xe9f918: fdiv            d3, d0, d2
    // 0xe9f91c: mov             v0.16b, v3.16b
    // 0xe9f920: b               #0xe9f928
    // 0xe9f924: d0 = 0.000000
    //     0xe9f924: eor             v0.16b, v0.16b, v0.16b
    // 0xe9f928: mov             v2.16b, v0.16b
    // 0xe9f92c: stur            d0, [fp, #-0xa0]
    // 0xe9f930: tbnz            w4, #4, #0xe9f944
    // 0xe9f934: ldur            d3, [fp, #-0x98]
    // 0xe9f938: fsub            d4, d3, d2
    // 0xe9f93c: mov             v3.16b, v4.16b
    // 0xe9f940: b               #0xe9f948
    // 0xe9f944: mov             v3.16b, v2.16b
    // 0xe9f948: ldur            x0, [fp, #-0x18]
    // 0xe9f94c: ldur            d2, [fp, #-0x88]
    // 0xe9f950: ldur            x5, [fp, #-0x68]
    // 0xe9f954: stur            d3, [fp, #-0x90]
    // 0xe9f958: LoadField: r2 = r0->field_7
    //     0xe9f958: ldur            x2, [x0, #7]
    // 0xe9f95c: LoadField: r6 = r0->field_f
    //     0xe9f95c: ldur            x6, [x0, #0xf]
    // 0xe9f960: r0 = BoxInt64Instr(r6)
    //     0xe9f960: sbfiz           x0, x6, #1, #0x1f
    //     0xe9f964: cmp             x6, x0, asr #1
    //     0xe9f968: b.eq            #0xe9f974
    //     0xe9f96c: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe9f970: stur            x6, [x0, #7]
    // 0xe9f974: str             x0, [SP]
    // 0xe9f978: ldur            x1, [fp, #-8]
    // 0xe9f97c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe9f97c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe9f980: r0 = sublist()
    //     0xe9f980: bl              #0x6eabd0  ; [dart:core] _GrowableList::sublist
    // 0xe9f984: mov             x3, x0
    // 0xe9f988: stur            x3, [fp, #-0x38]
    // 0xe9f98c: LoadField: r4 = r3->field_7
    //     0xe9f98c: ldur            w4, [x3, #7]
    // 0xe9f990: DecompressPointer r4
    //     0xe9f990: add             x4, x4, HEAP, lsl #32
    // 0xe9f994: stur            x4, [fp, #-0x28]
    // 0xe9f998: LoadField: r0 = r3->field_b
    //     0xe9f998: ldur            w0, [x3, #0xb]
    // 0xe9f99c: r5 = LoadInt32Instr(r0)
    //     0xe9f99c: sbfx            x5, x0, #1, #0x1f
    // 0xe9f9a0: ldur            x0, [fp, #-0x68]
    // 0xe9f9a4: stur            x5, [fp, #-0x70]
    // 0xe9f9a8: LoadField: r6 = r0->field_7
    //     0xe9f9a8: ldur            x6, [x0, #7]
    // 0xe9f9ac: ldur            d1, [fp, #-0x88]
    // 0xe9f9b0: stur            x6, [fp, #-0x50]
    // 0xe9f9b4: d0 = 2.000000
    //     0xe9f9b4: fmov            d0, #2.00000000
    // 0xe9f9b8: fdiv            d2, d1, d0
    // 0xe9f9bc: stur            d2, [fp, #-0x98]
    // 0xe9f9c0: r16 = Instance_CrossAxisAlignment
    //     0xe9f9c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e4e0] Obj!CrossAxisAlignment@e2e8a1
    //     0xe9f9c4: ldr             x16, [x16, #0x4e0]
    // 0xe9f9c8: cmp             w0, w16
    // 0xe9f9cc: r16 = true
    //     0xe9f9cc: add             x16, NULL, #0x20  ; true
    // 0xe9f9d0: r17 = false
    //     0xe9f9d0: add             x17, NULL, #0x30  ; false
    // 0xe9f9d4: csel            x7, x16, x17, eq
    // 0xe9f9d8: stur            x7, [fp, #-0x18]
    // 0xe9f9dc: ldur            d4, [fp, #-0x90]
    // 0xe9f9e0: ldur            x9, [fp, #-0x20]
    // 0xe9f9e4: ldur            d3, [fp, #-0xa0]
    // 0xe9f9e8: ldur            x10, [fp, #-0x60]
    // 0xe9f9ec: r0 = 0
    //     0xe9f9ec: movz            x0, #0
    // 0xe9f9f0: ldur            x8, [fp, #-0x30]
    // 0xe9f9f4: stur            d4, [fp, #-0x90]
    // 0xe9f9f8: CheckStackOverflow
    //     0xe9f9f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9f9fc: cmp             SP, x16
    //     0xe9fa00: b.ls            #0xe9fe34
    // 0xe9fa04: LoadField: r1 = r3->field_b
    //     0xe9fa04: ldur            w1, [x3, #0xb]
    // 0xe9fa08: r2 = LoadInt32Instr(r1)
    //     0xe9fa08: sbfx            x2, x1, #1, #0x1f
    // 0xe9fa0c: cmp             x5, x2
    // 0xe9fa10: b.ne            #0xe9fdac
    // 0xe9fa14: cmp             x0, x2
    // 0xe9fa18: b.ge            #0xe9fd9c
    // 0xe9fa1c: LoadField: r1 = r3->field_f
    //     0xe9fa1c: ldur            w1, [x3, #0xf]
    // 0xe9fa20: DecompressPointer r1
    //     0xe9fa20: add             x1, x1, HEAP, lsl #32
    // 0xe9fa24: ArrayLoad: r11 = r1[r0]  ; Unknown_4
    //     0xe9fa24: add             x16, x1, x0, lsl #2
    //     0xe9fa28: ldur            w11, [x16, #0xf]
    // 0xe9fa2c: DecompressPointer r11
    //     0xe9fa2c: add             x11, x11, HEAP, lsl #32
    // 0xe9fa30: stur            x11, [fp, #-8]
    // 0xe9fa34: add             x12, x0, #1
    // 0xe9fa38: stur            x12, [fp, #-0x10]
    // 0xe9fa3c: cmp             w11, NULL
    // 0xe9fa40: b.ne            #0xe9fa74
    // 0xe9fa44: mov             x0, x11
    // 0xe9fa48: mov             x2, x4
    // 0xe9fa4c: r1 = Null
    //     0xe9fa4c: mov             x1, NULL
    // 0xe9fa50: cmp             w2, NULL
    // 0xe9fa54: b.eq            #0xe9fa74
    // 0xe9fa58: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe9fa58: ldur            w4, [x2, #0x17]
    // 0xe9fa5c: DecompressPointer r4
    //     0xe9fa5c: add             x4, x4, HEAP, lsl #32
    // 0xe9fa60: r8 = X0
    //     0xe9fa60: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe9fa64: LoadField: r9 = r4->field_7
    //     0xe9fa64: ldur            x9, [x4, #7]
    // 0xe9fa68: r3 = Null
    //     0xe9fa68: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ede0] Null
    //     0xe9fa6c: ldr             x3, [x3, #0xde0]
    // 0xe9fa70: blr             x9
    // 0xe9fa74: ldur            x0, [fp, #-0x50]
    // 0xe9fa78: cmp             x0, #1
    // 0xe9fa7c: b.gt            #0xe9fb38
    // 0xe9fa80: ldur            x3, [fp, #-0x60]
    // 0xe9fa84: cmp             x3, #0
    // 0xe9fa88: b.gt            #0xe9fa98
    // 0xe9fa8c: r2 = Instance_Axis
    //     0xe9fa8c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e2f0] Obj!Axis@e2e961
    //     0xe9fa90: ldr             x2, [x2, #0x2f0]
    // 0xe9fa94: b               #0xe9faa0
    // 0xe9fa98: r2 = Instance_Axis
    //     0xe9fa98: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e300] Obj!Axis@e2e941
    //     0xe9fa9c: ldr             x2, [x2, #0x300]
    // 0xe9faa0: ldur            x4, [fp, #-0x18]
    // 0xe9faa4: ldur            x1, [fp, #-0x30]
    // 0xe9faa8: r0 = _startIsTopLeft()
    //     0xe9faa8: bl              #0xe9fe60  ; [package:pdf/src/widgets/flex.dart] Flex::_startIsTopLeft
    // 0xe9faac: mov             x1, x0
    // 0xe9fab0: ldur            x0, [fp, #-0x18]
    // 0xe9fab4: cmp             w1, w0
    // 0xe9fab8: b.ne            #0xe9fad0
    // 0xe9fabc: ldur            d0, [fp, #-0x88]
    // 0xe9fac0: ldur            x1, [fp, #-0x60]
    // 0xe9fac4: ldur            x2, [fp, #-8]
    // 0xe9fac8: d1 = 0.000000
    //     0xe9fac8: eor             v1.16b, v1.16b, v1.16b
    // 0xe9facc: b               #0xe9fb24
    // 0xe9fad0: ldur            x1, [fp, #-0x60]
    // 0xe9fad4: cmp             x1, #0
    // 0xe9fad8: b.gt            #0xe9fafc
    // 0xe9fadc: ldur            x2, [fp, #-8]
    // 0xe9fae0: LoadField: r3 = r2->field_7
    //     0xe9fae0: ldur            w3, [x2, #7]
    // 0xe9fae4: DecompressPointer r3
    //     0xe9fae4: add             x3, x3, HEAP, lsl #32
    // 0xe9fae8: cmp             w3, NULL
    // 0xe9faec: b.eq            #0xe9fe3c
    // 0xe9faf0: LoadField: d0 = r3->field_1f
    //     0xe9faf0: ldur            d0, [x3, #0x1f]
    // 0xe9faf4: mov             v1.16b, v0.16b
    // 0xe9faf8: b               #0xe9fb18
    // 0xe9fafc: ldur            x2, [fp, #-8]
    // 0xe9fb00: LoadField: r3 = r2->field_7
    //     0xe9fb00: ldur            w3, [x2, #7]
    // 0xe9fb04: DecompressPointer r3
    //     0xe9fb04: add             x3, x3, HEAP, lsl #32
    // 0xe9fb08: cmp             w3, NULL
    // 0xe9fb0c: b.eq            #0xe9fe40
    // 0xe9fb10: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xe9fb10: ldur            d0, [x3, #0x17]
    // 0xe9fb14: mov             v1.16b, v0.16b
    // 0xe9fb18: ldur            d0, [fp, #-0x88]
    // 0xe9fb1c: fsub            d2, d0, d1
    // 0xe9fb20: mov             v1.16b, v2.16b
    // 0xe9fb24: mov             v3.16b, v1.16b
    // 0xe9fb28: ldur            x3, [fp, #-0x50]
    // 0xe9fb2c: ldur            d2, [fp, #-0x98]
    // 0xe9fb30: d1 = 2.000000
    //     0xe9fb30: fmov            d1, #2.00000000
    // 0xe9fb34: b               #0xe9fbb0
    // 0xe9fb38: ldur            d0, [fp, #-0x88]
    // 0xe9fb3c: mov             x3, x0
    // 0xe9fb40: ldur            x1, [fp, #-0x60]
    // 0xe9fb44: ldur            x0, [fp, #-0x18]
    // 0xe9fb48: ldur            x2, [fp, #-8]
    // 0xe9fb4c: cmp             x3, #2
    // 0xe9fb50: b.gt            #0xe9fba4
    // 0xe9fb54: cmp             x1, #0
    // 0xe9fb58: b.gt            #0xe9fb78
    // 0xe9fb5c: LoadField: r4 = r2->field_7
    //     0xe9fb5c: ldur            w4, [x2, #7]
    // 0xe9fb60: DecompressPointer r4
    //     0xe9fb60: add             x4, x4, HEAP, lsl #32
    // 0xe9fb64: cmp             w4, NULL
    // 0xe9fb68: b.eq            #0xe9fe44
    // 0xe9fb6c: LoadField: d1 = r4->field_1f
    //     0xe9fb6c: ldur            d1, [x4, #0x1f]
    // 0xe9fb70: mov             v3.16b, v1.16b
    // 0xe9fb74: b               #0xe9fb90
    // 0xe9fb78: LoadField: r4 = r2->field_7
    //     0xe9fb78: ldur            w4, [x2, #7]
    // 0xe9fb7c: DecompressPointer r4
    //     0xe9fb7c: add             x4, x4, HEAP, lsl #32
    // 0xe9fb80: cmp             w4, NULL
    // 0xe9fb84: b.eq            #0xe9fe48
    // 0xe9fb88: ArrayLoad: d1 = r4[0]  ; List_8
    //     0xe9fb88: ldur            d1, [x4, #0x17]
    // 0xe9fb8c: mov             v3.16b, v1.16b
    // 0xe9fb90: ldur            d2, [fp, #-0x98]
    // 0xe9fb94: d1 = 2.000000
    //     0xe9fb94: fmov            d1, #2.00000000
    // 0xe9fb98: fdiv            d4, d3, d1
    // 0xe9fb9c: fsub            d3, d2, d4
    // 0xe9fba0: b               #0xe9fbb0
    // 0xe9fba4: ldur            d2, [fp, #-0x98]
    // 0xe9fba8: d1 = 2.000000
    //     0xe9fba8: fmov            d1, #2.00000000
    // 0xe9fbac: d3 = 0.000000
    //     0xe9fbac: eor             v3.16b, v3.16b, v3.16b
    // 0xe9fbb0: ldur            x4, [fp, #-0x20]
    // 0xe9fbb4: stur            d3, [fp, #-0xc8]
    // 0xe9fbb8: tbnz            w4, #4, #0xe9fc08
    // 0xe9fbbc: cmp             x1, #0
    // 0xe9fbc0: b.gt            #0xe9fbe0
    // 0xe9fbc4: LoadField: r5 = r2->field_7
    //     0xe9fbc4: ldur            w5, [x2, #7]
    // 0xe9fbc8: DecompressPointer r5
    //     0xe9fbc8: add             x5, x5, HEAP, lsl #32
    // 0xe9fbcc: cmp             w5, NULL
    // 0xe9fbd0: b.eq            #0xe9fe4c
    // 0xe9fbd4: ArrayLoad: d5 = r5[0]  ; List_8
    //     0xe9fbd4: ldur            d5, [x5, #0x17]
    // 0xe9fbd8: mov             v4.16b, v5.16b
    // 0xe9fbdc: b               #0xe9fbf8
    // 0xe9fbe0: LoadField: r5 = r2->field_7
    //     0xe9fbe0: ldur            w5, [x2, #7]
    // 0xe9fbe4: DecompressPointer r5
    //     0xe9fbe4: add             x5, x5, HEAP, lsl #32
    // 0xe9fbe8: cmp             w5, NULL
    // 0xe9fbec: b.eq            #0xe9fe50
    // 0xe9fbf0: LoadField: d5 = r5->field_1f
    //     0xe9fbf0: ldur            d5, [x5, #0x1f]
    // 0xe9fbf4: mov             v4.16b, v5.16b
    // 0xe9fbf8: ldur            d5, [fp, #-0x90]
    // 0xe9fbfc: fsub            d6, d5, d4
    // 0xe9fc00: mov             v4.16b, v6.16b
    // 0xe9fc04: b               #0xe9fc10
    // 0xe9fc08: ldur            d5, [fp, #-0x90]
    // 0xe9fc0c: mov             v4.16b, v5.16b
    // 0xe9fc10: stur            d4, [fp, #-0xc0]
    // 0xe9fc14: cmp             x1, #0
    // 0xe9fc18: b.gt            #0xe9fcb8
    // 0xe9fc1c: ldur            x5, [fp, #-0x30]
    // 0xe9fc20: LoadField: r6 = r5->field_7
    //     0xe9fc20: ldur            w6, [x5, #7]
    // 0xe9fc24: DecompressPointer r6
    //     0xe9fc24: add             x6, x6, HEAP, lsl #32
    // 0xe9fc28: cmp             w6, NULL
    // 0xe9fc2c: b.eq            #0xe9fe54
    // 0xe9fc30: LoadField: d5 = r6->field_7
    //     0xe9fc30: ldur            d5, [x6, #7]
    // 0xe9fc34: fadd            d6, d5, d4
    // 0xe9fc38: stur            d6, [fp, #-0xb8]
    // 0xe9fc3c: LoadField: d5 = r6->field_f
    //     0xe9fc3c: ldur            d5, [x6, #0xf]
    // 0xe9fc40: fadd            d7, d5, d3
    // 0xe9fc44: stur            d7, [fp, #-0xb0]
    // 0xe9fc48: LoadField: r6 = r2->field_7
    //     0xe9fc48: ldur            w6, [x2, #7]
    // 0xe9fc4c: DecompressPointer r6
    //     0xe9fc4c: add             x6, x6, HEAP, lsl #32
    // 0xe9fc50: cmp             w6, NULL
    // 0xe9fc54: b.eq            #0xe9fe58
    // 0xe9fc58: ArrayLoad: d3 = r6[0]  ; List_8
    //     0xe9fc58: ldur            d3, [x6, #0x17]
    // 0xe9fc5c: stur            d3, [fp, #-0xa8]
    // 0xe9fc60: LoadField: d5 = r6->field_1f
    //     0xe9fc60: ldur            d5, [x6, #0x1f]
    // 0xe9fc64: stur            d5, [fp, #-0x90]
    // 0xe9fc68: r0 = PdfRect()
    //     0xe9fc68: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe9fc6c: ldur            d0, [fp, #-0xb8]
    // 0xe9fc70: StoreField: r0->field_7 = d0
    //     0xe9fc70: stur            d0, [x0, #7]
    // 0xe9fc74: ldur            d0, [fp, #-0xb0]
    // 0xe9fc78: StoreField: r0->field_f = d0
    //     0xe9fc78: stur            d0, [x0, #0xf]
    // 0xe9fc7c: ldur            d0, [fp, #-0xa8]
    // 0xe9fc80: ArrayStore: r0[0] = d0  ; List_8
    //     0xe9fc80: stur            d0, [x0, #0x17]
    // 0xe9fc84: ldur            d1, [fp, #-0x90]
    // 0xe9fc88: StoreField: r0->field_1f = d1
    //     0xe9fc88: stur            d1, [x0, #0x1f]
    // 0xe9fc8c: ldur            x1, [fp, #-8]
    // 0xe9fc90: StoreField: r1->field_7 = r0
    //     0xe9fc90: stur            w0, [x1, #7]
    //     0xe9fc94: ldurb           w16, [x1, #-1]
    //     0xe9fc98: ldurb           w17, [x0, #-1]
    //     0xe9fc9c: and             x16, x17, x16, lsr #2
    //     0xe9fca0: tst             x16, HEAP, lsr #32
    //     0xe9fca4: b.eq            #0xe9fcac
    //     0xe9fca8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe9fcac: mov             v2.16b, v0.16b
    // 0xe9fcb0: ldur            d0, [fp, #-0xc0]
    // 0xe9fcb4: b               #0xe9fd30
    // 0xe9fcb8: mov             v0.16b, v4.16b
    // 0xe9fcbc: mov             x1, x2
    // 0xe9fcc0: LoadField: r0 = r1->field_7
    //     0xe9fcc0: ldur            w0, [x1, #7]
    // 0xe9fcc4: DecompressPointer r0
    //     0xe9fcc4: add             x0, x0, HEAP, lsl #32
    // 0xe9fcc8: cmp             w0, NULL
    // 0xe9fccc: b.eq            #0xe9fe5c
    // 0xe9fcd0: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xe9fcd0: ldur            d1, [x0, #0x17]
    // 0xe9fcd4: stur            d1, [fp, #-0xa8]
    // 0xe9fcd8: LoadField: d2 = r0->field_1f
    //     0xe9fcd8: ldur            d2, [x0, #0x1f]
    // 0xe9fcdc: stur            d2, [fp, #-0x90]
    // 0xe9fce0: r0 = PdfRect()
    //     0xe9fce0: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe9fce4: ldur            d0, [fp, #-0xc8]
    // 0xe9fce8: StoreField: r0->field_7 = d0
    //     0xe9fce8: stur            d0, [x0, #7]
    // 0xe9fcec: ldur            d0, [fp, #-0xc0]
    // 0xe9fcf0: StoreField: r0->field_f = d0
    //     0xe9fcf0: stur            d0, [x0, #0xf]
    // 0xe9fcf4: ldur            d1, [fp, #-0xa8]
    // 0xe9fcf8: ArrayStore: r0[0] = d1  ; List_8
    //     0xe9fcf8: stur            d1, [x0, #0x17]
    // 0xe9fcfc: ldur            d2, [fp, #-0x90]
    // 0xe9fd00: StoreField: r0->field_1f = d2
    //     0xe9fd00: stur            d2, [x0, #0x1f]
    // 0xe9fd04: ldur            x1, [fp, #-8]
    // 0xe9fd08: StoreField: r1->field_7 = r0
    //     0xe9fd08: stur            w0, [x1, #7]
    //     0xe9fd0c: ldurb           w16, [x1, #-1]
    //     0xe9fd10: ldurb           w17, [x0, #-1]
    //     0xe9fd14: and             x16, x17, x16, lsr #2
    //     0xe9fd18: tst             x16, HEAP, lsr #32
    //     0xe9fd1c: b.eq            #0xe9fd24
    //     0xe9fd20: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe9fd24: mov             v31.16b, v2.16b
    // 0xe9fd28: mov             v2.16b, v1.16b
    // 0xe9fd2c: mov             v1.16b, v31.16b
    // 0xe9fd30: ldur            x1, [fp, #-0x20]
    // 0xe9fd34: tbnz            w1, #4, #0xe9fd4c
    // 0xe9fd38: ldur            d3, [fp, #-0xa0]
    // 0xe9fd3c: fsub            d1, d0, d3
    // 0xe9fd40: mov             v4.16b, v1.16b
    // 0xe9fd44: ldur            x2, [fp, #-0x60]
    // 0xe9fd48: b               #0xe9fd6c
    // 0xe9fd4c: ldur            d3, [fp, #-0xa0]
    // 0xe9fd50: ldur            x2, [fp, #-0x60]
    // 0xe9fd54: cmp             x2, #0
    // 0xe9fd58: b.gt            #0xe9fd60
    // 0xe9fd5c: mov             v1.16b, v2.16b
    // 0xe9fd60: fadd            d2, d1, d3
    // 0xe9fd64: fadd            d1, d0, d2
    // 0xe9fd68: mov             v4.16b, v1.16b
    // 0xe9fd6c: ldur            x0, [fp, #-0x10]
    // 0xe9fd70: ldur            d1, [fp, #-0x88]
    // 0xe9fd74: mov             x9, x1
    // 0xe9fd78: ldur            x3, [fp, #-0x38]
    // 0xe9fd7c: ldur            x6, [fp, #-0x50]
    // 0xe9fd80: ldur            d2, [fp, #-0x98]
    // 0xe9fd84: mov             x10, x2
    // 0xe9fd88: ldur            x7, [fp, #-0x18]
    // 0xe9fd8c: ldur            x4, [fp, #-0x28]
    // 0xe9fd90: ldur            x5, [fp, #-0x70]
    // 0xe9fd94: d0 = 2.000000
    //     0xe9fd94: fmov            d0, #2.00000000
    // 0xe9fd98: b               #0xe9f9f0
    // 0xe9fd9c: r0 = Null
    //     0xe9fd9c: mov             x0, NULL
    // 0xe9fda0: LeaveFrame
    //     0xe9fda0: mov             SP, fp
    //     0xe9fda4: ldp             fp, lr, [SP], #0x10
    // 0xe9fda8: ret
    //     0xe9fda8: ret             
    // 0xe9fdac: mov             x0, x3
    // 0xe9fdb0: r0 = ConcurrentModificationError()
    //     0xe9fdb0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe9fdb4: mov             x1, x0
    // 0xe9fdb8: ldur            x0, [fp, #-0x38]
    // 0xe9fdbc: StoreField: r1->field_b = r0
    //     0xe9fdbc: stur            w0, [x1, #0xb]
    // 0xe9fdc0: mov             x0, x1
    // 0xe9fdc4: r0 = Throw()
    //     0xe9fdc4: bl              #0xec04b8  ; ThrowStub
    // 0xe9fdc8: brk             #0
    // 0xe9fdcc: mov             x0, x3
    // 0xe9fdd0: r0 = ConcurrentModificationError()
    //     0xe9fdd0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe9fdd4: mov             x1, x0
    // 0xe9fdd8: ldur            x0, [fp, #-0x80]
    // 0xe9fddc: StoreField: r1->field_b = r0
    //     0xe9fddc: stur            w0, [x1, #0xb]
    // 0xe9fde0: mov             x0, x1
    // 0xe9fde4: r0 = Throw()
    //     0xe9fde4: bl              #0xec04b8  ; ThrowStub
    // 0xe9fde8: brk             #0
    // 0xe9fdec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe9fdec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe9fdf0: b               #0xe9f21c
    // 0xe9fdf4: r0 = StackOverflowSharedWithFPURegs()
    //     0xe9fdf4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe9fdf8: b               #0xe9f310
    // 0xe9fdfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe9fdfc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe9fe00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe9fe00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe9fe04: stp             q0, q2, [SP, #-0x20]!
    // 0xe9fe08: stp             x1, x2, [SP, #-0x10]!
    // 0xe9fe0c: r0 = AllocateDouble()
    //     0xe9fe0c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe9fe10: mov             x3, x0
    // 0xe9fe14: ldp             x1, x2, [SP], #0x10
    // 0xe9fe18: ldp             q0, q2, [SP], #0x20
    // 0xe9fe1c: b               #0xe9f538
    // 0xe9fe20: stp             q1, q3, [SP, #-0x20]!
    // 0xe9fe24: r0 = AllocateDouble()
    //     0xe9fe24: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe9fe28: mov             x1, x0
    // 0xe9fe2c: ldp             q1, q3, [SP], #0x20
    // 0xe9fe30: b               #0xe9f600
    // 0xe9fe34: r0 = StackOverflowSharedWithFPURegs()
    //     0xe9fe34: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe9fe38: b               #0xe9fa04
    // 0xe9fe3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe9fe3c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe9fe40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe9fe40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe9fe44: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe9fe44: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe9fe48: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe9fe48: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe9fe4c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe9fe4c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe9fe50: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe9fe50: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe9fe54: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe9fe54: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe9fe58: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe9fe58: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe9fe5c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe9fe5c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _startIsTopLeft(/* No info */) {
    // ** addr: 0xe9fe60, size: 0x1c
    // 0xe9fe60: LoadField: r1 = r2->field_7
    //     0xe9fe60: ldur            x1, [x2, #7]
    // 0xe9fe64: cmp             x1, #0
    // 0xe9fe68: b.gt            #0xe9fe74
    // 0xe9fe6c: r0 = true
    //     0xe9fe6c: add             x0, NULL, #0x20  ; true
    // 0xe9fe70: ret
    //     0xe9fe70: ret             
    // 0xe9fe74: r0 = false
    //     0xe9fe74: add             x0, NULL, #0x30  ; false
    // 0xe9fe78: ret
    //     0xe9fe78: ret             
  }
}

// class id: 794, size: 0x28, field offset: 0x28
class Column extends Flex {
}

// class id: 795, size: 0x28, field offset: 0x28
class Row extends Flex {
}

// class id: 823, size: 0x18, field offset: 0x8
class FlexContext extends WidgetContext {

  _ toString(/* No info */) {
    // ** addr: 0xc3661c, size: 0xa4
    // 0xc3661c: EnterFrame
    //     0xc3661c: stp             fp, lr, [SP, #-0x10]!
    //     0xc36620: mov             fp, SP
    // 0xc36624: AllocStack(0x8)
    //     0xc36624: sub             SP, SP, #8
    // 0xc36628: CheckStackOverflow
    //     0xc36628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3662c: cmp             SP, x16
    //     0xc36630: b.ls            #0xc366b8
    // 0xc36634: r1 = Null
    //     0xc36634: mov             x1, NULL
    // 0xc36638: r2 = 10
    //     0xc36638: movz            x2, #0xa
    // 0xc3663c: r0 = AllocateArray()
    //     0xc3663c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc36640: mov             x2, x0
    // 0xc36644: r16 = FlexContext
    //     0xc36644: add             x16, PP, #0x33, lsl #12  ; [pp+0x338d0] Type: FlexContext
    //     0xc36648: ldr             x16, [x16, #0x8d0]
    // 0xc3664c: StoreField: r2->field_f = r16
    //     0xc3664c: stur            w16, [x2, #0xf]
    // 0xc36650: r16 = " first:"
    //     0xc36650: add             x16, PP, #0x33, lsl #12  ; [pp+0x338d8] " first:"
    //     0xc36654: ldr             x16, [x16, #0x8d8]
    // 0xc36658: StoreField: r2->field_13 = r16
    //     0xc36658: stur            w16, [x2, #0x13]
    // 0xc3665c: ldr             x3, [fp, #0x10]
    // 0xc36660: LoadField: r4 = r3->field_7
    //     0xc36660: ldur            x4, [x3, #7]
    // 0xc36664: r0 = BoxInt64Instr(r4)
    //     0xc36664: sbfiz           x0, x4, #1, #0x1f
    //     0xc36668: cmp             x4, x0, asr #1
    //     0xc3666c: b.eq            #0xc36678
    //     0xc36670: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc36674: stur            x4, [x0, #7]
    // 0xc36678: ArrayStore: r2[0] = r0  ; List_4
    //     0xc36678: stur            w0, [x2, #0x17]
    // 0xc3667c: r16 = " last:"
    //     0xc3667c: add             x16, PP, #0x33, lsl #12  ; [pp+0x338e0] " last:"
    //     0xc36680: ldr             x16, [x16, #0x8e0]
    // 0xc36684: StoreField: r2->field_1b = r16
    //     0xc36684: stur            w16, [x2, #0x1b]
    // 0xc36688: LoadField: r4 = r3->field_f
    //     0xc36688: ldur            x4, [x3, #0xf]
    // 0xc3668c: r0 = BoxInt64Instr(r4)
    //     0xc3668c: sbfiz           x0, x4, #1, #0x1f
    //     0xc36690: cmp             x4, x0, asr #1
    //     0xc36694: b.eq            #0xc366a0
    //     0xc36698: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3669c: stur            x4, [x0, #7]
    // 0xc366a0: StoreField: r2->field_1f = r0
    //     0xc366a0: stur            w0, [x2, #0x1f]
    // 0xc366a4: str             x2, [SP]
    // 0xc366a8: r0 = _interpolate()
    //     0xc366a8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc366ac: LeaveFrame
    //     0xc366ac: mov             SP, fp
    //     0xc366b0: ldp             fp, lr, [SP], #0x10
    // 0xc366b4: ret
    //     0xc366b4: ret             
    // 0xc366b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc366b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc366bc: b               #0xc36634
  }
  _ apply(/* No info */) {
    // ** addr: 0xeabe5c, size: 0x7c
    // 0xeabe5c: EnterFrame
    //     0xeabe5c: stp             fp, lr, [SP, #-0x10]!
    //     0xeabe60: mov             fp, SP
    // 0xeabe64: AllocStack(0x10)
    //     0xeabe64: sub             SP, SP, #0x10
    // 0xeabe68: SetupParameters(FlexContext this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xeabe68: mov             x0, x2
    //     0xeabe6c: mov             x4, x1
    //     0xeabe70: mov             x3, x2
    //     0xeabe74: stur            x1, [fp, #-8]
    //     0xeabe78: stur            x2, [fp, #-0x10]
    // 0xeabe7c: r2 = Null
    //     0xeabe7c: mov             x2, NULL
    // 0xeabe80: r1 = Null
    //     0xeabe80: mov             x1, NULL
    // 0xeabe84: r4 = 60
    //     0xeabe84: movz            x4, #0x3c
    // 0xeabe88: branchIfSmi(r0, 0xeabe94)
    //     0xeabe88: tbz             w0, #0, #0xeabe94
    // 0xeabe8c: r4 = LoadClassIdInstr(r0)
    //     0xeabe8c: ldur            x4, [x0, #-1]
    //     0xeabe90: ubfx            x4, x4, #0xc, #0x14
    // 0xeabe94: cmp             x4, #0x337
    // 0xeabe98: b.eq            #0xeabeb0
    // 0xeabe9c: r8 = FlexContext
    //     0xeabe9c: add             x8, PP, #0x33, lsl #12  ; [pp+0x338d0] Type: FlexContext
    //     0xeabea0: ldr             x8, [x8, #0x8d0]
    // 0xeabea4: r3 = Null
    //     0xeabea4: add             x3, PP, #0x36, lsl #12  ; [pp+0x367e0] Null
    //     0xeabea8: ldr             x3, [x3, #0x7e0]
    // 0xeabeac: r0 = DefaultTypeTest()
    //     0xeabeac: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xeabeb0: ldur            x1, [fp, #-0x10]
    // 0xeabeb4: LoadField: r2 = r1->field_7
    //     0xeabeb4: ldur            x2, [x1, #7]
    // 0xeabeb8: ldur            x3, [fp, #-8]
    // 0xeabebc: StoreField: r3->field_7 = r2
    //     0xeabebc: stur            x2, [x3, #7]
    // 0xeabec0: LoadField: r2 = r1->field_f
    //     0xeabec0: ldur            x2, [x1, #0xf]
    // 0xeabec4: StoreField: r3->field_f = r2
    //     0xeabec4: stur            x2, [x3, #0xf]
    // 0xeabec8: r0 = Null
    //     0xeabec8: mov             x0, NULL
    // 0xeabecc: LeaveFrame
    //     0xeabecc: mov             SP, fp
    //     0xeabed0: ldp             fp, lr, [SP], #0x10
    // 0xeabed4: ret
    //     0xeabed4: ret             
  }
  _ clone(/* No info */) {
    // ** addr: 0xeabfe0, size: 0x54
    // 0xeabfe0: EnterFrame
    //     0xeabfe0: stp             fp, lr, [SP, #-0x10]!
    //     0xeabfe4: mov             fp, SP
    // 0xeabfe8: AllocStack(0x10)
    //     0xeabfe8: sub             SP, SP, #0x10
    // 0xeabfec: SetupParameters(FlexContext this /* r1 => r2, fp-0x8 */)
    //     0xeabfec: mov             x2, x1
    //     0xeabff0: stur            x1, [fp, #-8]
    // 0xeabff4: CheckStackOverflow
    //     0xeabff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeabff8: cmp             SP, x16
    //     0xeabffc: b.ls            #0xeac02c
    // 0xeac000: r0 = FlexContext()
    //     0xeac000: bl              #0xb13cc8  ; AllocateFlexContextStub -> FlexContext (size=0x18)
    // 0xeac004: stur            x0, [fp, #-0x10]
    // 0xeac008: StoreField: r0->field_7 = rZR
    //     0xeac008: stur            xzr, [x0, #7]
    // 0xeac00c: StoreField: r0->field_f = rZR
    //     0xeac00c: stur            xzr, [x0, #0xf]
    // 0xeac010: mov             x1, x0
    // 0xeac014: ldur            x2, [fp, #-8]
    // 0xeac018: r0 = apply()
    //     0xeac018: bl              #0xeabe5c  ; [package:pdf/src/widgets/flex.dart] FlexContext::apply
    // 0xeac01c: ldur            x0, [fp, #-0x10]
    // 0xeac020: LeaveFrame
    //     0xeac020: mov             SP, fp
    //     0xeac024: ldp             fp, lr, [SP], #0x10
    // 0xeac028: ret
    //     0xeac028: ret             
    // 0xeac02c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeac02c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeac030: b               #0xeac000
  }
}

// class id: 6796, size: 0x14, field offset: 0x14
enum VerticalDirection extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e054, size: 0x64
    // 0xc4e054: EnterFrame
    //     0xc4e054: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e058: mov             fp, SP
    // 0xc4e05c: AllocStack(0x10)
    //     0xc4e05c: sub             SP, SP, #0x10
    // 0xc4e060: SetupParameters(VerticalDirection this /* r1 => r0, fp-0x8 */)
    //     0xc4e060: mov             x0, x1
    //     0xc4e064: stur            x1, [fp, #-8]
    // 0xc4e068: CheckStackOverflow
    //     0xc4e068: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e06c: cmp             SP, x16
    //     0xc4e070: b.ls            #0xc4e0b0
    // 0xc4e074: r1 = Null
    //     0xc4e074: mov             x1, NULL
    // 0xc4e078: r2 = 4
    //     0xc4e078: movz            x2, #0x4
    // 0xc4e07c: r0 = AllocateArray()
    //     0xc4e07c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e080: r16 = "VerticalDirection."
    //     0xc4e080: add             x16, PP, #0x33, lsl #12  ; [pp+0x338b8] "VerticalDirection."
    //     0xc4e084: ldr             x16, [x16, #0x8b8]
    // 0xc4e088: StoreField: r0->field_f = r16
    //     0xc4e088: stur            w16, [x0, #0xf]
    // 0xc4e08c: ldur            x1, [fp, #-8]
    // 0xc4e090: LoadField: r2 = r1->field_f
    //     0xc4e090: ldur            w2, [x1, #0xf]
    // 0xc4e094: DecompressPointer r2
    //     0xc4e094: add             x2, x2, HEAP, lsl #32
    // 0xc4e098: StoreField: r0->field_13 = r2
    //     0xc4e098: stur            w2, [x0, #0x13]
    // 0xc4e09c: str             x0, [SP]
    // 0xc4e0a0: r0 = _interpolate()
    //     0xc4e0a0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e0a4: LeaveFrame
    //     0xc4e0a4: mov             SP, fp
    //     0xc4e0a8: ldp             fp, lr, [SP], #0x10
    // 0xc4e0ac: ret
    //     0xc4e0ac: ret             
    // 0xc4e0b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e0b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e0b4: b               #0xc4e074
  }
}

// class id: 6797, size: 0x14, field offset: 0x14
enum CrossAxisAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4dff0, size: 0x64
    // 0xc4dff0: EnterFrame
    //     0xc4dff0: stp             fp, lr, [SP, #-0x10]!
    //     0xc4dff4: mov             fp, SP
    // 0xc4dff8: AllocStack(0x10)
    //     0xc4dff8: sub             SP, SP, #0x10
    // 0xc4dffc: SetupParameters(CrossAxisAlignment this /* r1 => r0, fp-0x8 */)
    //     0xc4dffc: mov             x0, x1
    //     0xc4e000: stur            x1, [fp, #-8]
    // 0xc4e004: CheckStackOverflow
    //     0xc4e004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e008: cmp             SP, x16
    //     0xc4e00c: b.ls            #0xc4e04c
    // 0xc4e010: r1 = Null
    //     0xc4e010: mov             x1, NULL
    // 0xc4e014: r2 = 4
    //     0xc4e014: movz            x2, #0x4
    // 0xc4e018: r0 = AllocateArray()
    //     0xc4e018: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e01c: r16 = "CrossAxisAlignment."
    //     0xc4e01c: add             x16, PP, #0x33, lsl #12  ; [pp+0x338c8] "CrossAxisAlignment."
    //     0xc4e020: ldr             x16, [x16, #0x8c8]
    // 0xc4e024: StoreField: r0->field_f = r16
    //     0xc4e024: stur            w16, [x0, #0xf]
    // 0xc4e028: ldur            x1, [fp, #-8]
    // 0xc4e02c: LoadField: r2 = r1->field_f
    //     0xc4e02c: ldur            w2, [x1, #0xf]
    // 0xc4e030: DecompressPointer r2
    //     0xc4e030: add             x2, x2, HEAP, lsl #32
    // 0xc4e034: StoreField: r0->field_13 = r2
    //     0xc4e034: stur            w2, [x0, #0x13]
    // 0xc4e038: str             x0, [SP]
    // 0xc4e03c: r0 = _interpolate()
    //     0xc4e03c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e040: LeaveFrame
    //     0xc4e040: mov             SP, fp
    //     0xc4e044: ldp             fp, lr, [SP], #0x10
    // 0xc4e048: ret
    //     0xc4e048: ret             
    // 0xc4e04c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e04c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e050: b               #0xc4e010
  }
}

// class id: 6798, size: 0x14, field offset: 0x14
enum MainAxisAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4df8c, size: 0x64
    // 0xc4df8c: EnterFrame
    //     0xc4df8c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4df90: mov             fp, SP
    // 0xc4df94: AllocStack(0x10)
    //     0xc4df94: sub             SP, SP, #0x10
    // 0xc4df98: SetupParameters(MainAxisAlignment this /* r1 => r0, fp-0x8 */)
    //     0xc4df98: mov             x0, x1
    //     0xc4df9c: stur            x1, [fp, #-8]
    // 0xc4dfa0: CheckStackOverflow
    //     0xc4dfa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4dfa4: cmp             SP, x16
    //     0xc4dfa8: b.ls            #0xc4dfe8
    // 0xc4dfac: r1 = Null
    //     0xc4dfac: mov             x1, NULL
    // 0xc4dfb0: r2 = 4
    //     0xc4dfb0: movz            x2, #0x4
    // 0xc4dfb4: r0 = AllocateArray()
    //     0xc4dfb4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4dfb8: r16 = "MainAxisAlignment."
    //     0xc4dfb8: add             x16, PP, #0x33, lsl #12  ; [pp+0x338c0] "MainAxisAlignment."
    //     0xc4dfbc: ldr             x16, [x16, #0x8c0]
    // 0xc4dfc0: StoreField: r0->field_f = r16
    //     0xc4dfc0: stur            w16, [x0, #0xf]
    // 0xc4dfc4: ldur            x1, [fp, #-8]
    // 0xc4dfc8: LoadField: r2 = r1->field_f
    //     0xc4dfc8: ldur            w2, [x1, #0xf]
    // 0xc4dfcc: DecompressPointer r2
    //     0xc4dfcc: add             x2, x2, HEAP, lsl #32
    // 0xc4dfd0: StoreField: r0->field_13 = r2
    //     0xc4dfd0: stur            w2, [x0, #0x13]
    // 0xc4dfd4: str             x0, [SP]
    // 0xc4dfd8: r0 = _interpolate()
    //     0xc4dfd8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4dfdc: LeaveFrame
    //     0xc4dfdc: mov             SP, fp
    //     0xc4dfe0: ldp             fp, lr, [SP], #0x10
    // 0xc4dfe4: ret
    //     0xc4dfe4: ret             
    // 0xc4dfe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4dfe8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4dfec: b               #0xc4dfac
  }
}

// class id: 6799, size: 0x14, field offset: 0x14
enum MainAxisSize extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4df28, size: 0x64
    // 0xc4df28: EnterFrame
    //     0xc4df28: stp             fp, lr, [SP, #-0x10]!
    //     0xc4df2c: mov             fp, SP
    // 0xc4df30: AllocStack(0x10)
    //     0xc4df30: sub             SP, SP, #0x10
    // 0xc4df34: SetupParameters(MainAxisSize this /* r1 => r0, fp-0x8 */)
    //     0xc4df34: mov             x0, x1
    //     0xc4df38: stur            x1, [fp, #-8]
    // 0xc4df3c: CheckStackOverflow
    //     0xc4df3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4df40: cmp             SP, x16
    //     0xc4df44: b.ls            #0xc4df84
    // 0xc4df48: r1 = Null
    //     0xc4df48: mov             x1, NULL
    // 0xc4df4c: r2 = 4
    //     0xc4df4c: movz            x2, #0x4
    // 0xc4df50: r0 = AllocateArray()
    //     0xc4df50: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4df54: r16 = "MainAxisSize."
    //     0xc4df54: add             x16, PP, #0x33, lsl #12  ; [pp+0x338b0] "MainAxisSize."
    //     0xc4df58: ldr             x16, [x16, #0x8b0]
    // 0xc4df5c: StoreField: r0->field_f = r16
    //     0xc4df5c: stur            w16, [x0, #0xf]
    // 0xc4df60: ldur            x1, [fp, #-8]
    // 0xc4df64: LoadField: r2 = r1->field_f
    //     0xc4df64: ldur            w2, [x1, #0xf]
    // 0xc4df68: DecompressPointer r2
    //     0xc4df68: add             x2, x2, HEAP, lsl #32
    // 0xc4df6c: StoreField: r0->field_13 = r2
    //     0xc4df6c: stur            w2, [x0, #0x13]
    // 0xc4df70: str             x0, [SP]
    // 0xc4df74: r0 = _interpolate()
    //     0xc4df74: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4df78: LeaveFrame
    //     0xc4df78: mov             SP, fp
    //     0xc4df7c: ldp             fp, lr, [SP], #0x10
    // 0xc4df80: ret
    //     0xc4df80: ret             
    // 0xc4df84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4df84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4df88: b               #0xc4df48
  }
}

// class id: 6800, size: 0x14, field offset: 0x14
enum Axis extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4dec4, size: 0x64
    // 0xc4dec4: EnterFrame
    //     0xc4dec4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4dec8: mov             fp, SP
    // 0xc4decc: AllocStack(0x10)
    //     0xc4decc: sub             SP, SP, #0x10
    // 0xc4ded0: SetupParameters(Axis this /* r1 => r0, fp-0x8 */)
    //     0xc4ded0: mov             x0, x1
    //     0xc4ded4: stur            x1, [fp, #-8]
    // 0xc4ded8: CheckStackOverflow
    //     0xc4ded8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4dedc: cmp             SP, x16
    //     0xc4dee0: b.ls            #0xc4df20
    // 0xc4dee4: r1 = Null
    //     0xc4dee4: mov             x1, NULL
    // 0xc4dee8: r2 = 4
    //     0xc4dee8: movz            x2, #0x4
    // 0xc4deec: r0 = AllocateArray()
    //     0xc4deec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4def0: r16 = "Axis."
    //     0xc4def0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22098] "Axis."
    //     0xc4def4: ldr             x16, [x16, #0x98]
    // 0xc4def8: StoreField: r0->field_f = r16
    //     0xc4def8: stur            w16, [x0, #0xf]
    // 0xc4defc: ldur            x1, [fp, #-8]
    // 0xc4df00: LoadField: r2 = r1->field_f
    //     0xc4df00: ldur            w2, [x1, #0xf]
    // 0xc4df04: DecompressPointer r2
    //     0xc4df04: add             x2, x2, HEAP, lsl #32
    // 0xc4df08: StoreField: r0->field_13 = r2
    //     0xc4df08: stur            w2, [x0, #0x13]
    // 0xc4df0c: str             x0, [SP]
    // 0xc4df10: r0 = _interpolate()
    //     0xc4df10: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4df14: LeaveFrame
    //     0xc4df14: mov             SP, fp
    //     0xc4df18: ldp             fp, lr, [SP], #0x10
    // 0xc4df1c: ret
    //     0xc4df1c: ret             
    // 0xc4df20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4df20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4df24: b               #0xc4dee4
  }
}
