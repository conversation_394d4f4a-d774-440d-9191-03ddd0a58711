// lib: , url: package:pdf/src/widgets/font.dart

// class id: 1050850, size: 0x8
class :: {
}

// class id: 818, size: 0x10, field offset: 0x8
class Font extends Object {

  factory _ Font.helveticaBoldOblique(/* No info */) {
    // ** addr: 0xb103dc, size: 0x24
    // 0xb103dc: EnterFrame
    //     0xb103dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb103e0: mov             fp, SP
    // 0xb103e4: r0 = Font()
    //     0xb103e4: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xb103e8: r1 = Instance_Type1Fonts
    //     0xb103e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e578] Obj!Type1Fonts@e2e6c1
    //     0xb103ec: ldr             x1, [x1, #0x578]
    // 0xb103f0: StoreField: r0->field_7 = r1
    //     0xb103f0: stur            w1, [x0, #7]
    // 0xb103f4: LeaveFrame
    //     0xb103f4: mov             SP, fp
    //     0xb103f8: ldp             fp, lr, [SP], #0x10
    // 0xb103fc: ret
    //     0xb103fc: ret             
  }
  factory _ Font.helveticaOblique(/* No info */) {
    // ** addr: 0xb1040c, size: 0x24
    // 0xb1040c: EnterFrame
    //     0xb1040c: stp             fp, lr, [SP, #-0x10]!
    //     0xb10410: mov             fp, SP
    // 0xb10414: r0 = Font()
    //     0xb10414: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xb10418: r1 = Instance_Type1Fonts
    //     0xb10418: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e580] Obj!Type1Fonts@e2e6e1
    //     0xb1041c: ldr             x1, [x1, #0x580]
    // 0xb10420: StoreField: r0->field_7 = r1
    //     0xb10420: stur            w1, [x0, #7]
    // 0xb10424: LeaveFrame
    //     0xb10424: mov             SP, fp
    //     0xb10428: ldp             fp, lr, [SP], #0x10
    // 0xb1042c: ret
    //     0xb1042c: ret             
  }
  factory _ Font.helveticaBold(/* No info */) {
    // ** addr: 0xb10430, size: 0x24
    // 0xb10430: EnterFrame
    //     0xb10430: stp             fp, lr, [SP, #-0x10]!
    //     0xb10434: mov             fp, SP
    // 0xb10438: r0 = Font()
    //     0xb10438: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xb1043c: r1 = Instance_Type1Fonts
    //     0xb1043c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e588] Obj!Type1Fonts@e2e701
    //     0xb10440: ldr             x1, [x1, #0x588]
    // 0xb10444: StoreField: r0->field_7 = r1
    //     0xb10444: stur            w1, [x0, #7]
    // 0xb10448: LeaveFrame
    //     0xb10448: mov             SP, fp
    //     0xb1044c: ldp             fp, lr, [SP], #0x10
    // 0xb10450: ret
    //     0xb10450: ret             
  }
  factory _ Font.helvetica(/* No info */) {
    // ** addr: 0xb10454, size: 0x24
    // 0xb10454: EnterFrame
    //     0xb10454: stp             fp, lr, [SP, #-0x10]!
    //     0xb10458: mov             fp, SP
    // 0xb1045c: r0 = Font()
    //     0xb1045c: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xb10460: r1 = Instance_Type1Fonts
    //     0xb10460: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e590] Obj!Type1Fonts@e2e721
    //     0xb10464: ldr             x1, [x1, #0x590]
    // 0xb10468: StoreField: r0->field_7 = r1
    //     0xb10468: stur            w1, [x0, #7]
    // 0xb1046c: LeaveFrame
    //     0xb1046c: mov             SP, fp
    //     0xb10470: ldp             fp, lr, [SP], #0x10
    // 0xb10474: ret
    //     0xb10474: ret             
  }
  factory _ Font.ttf(/* No info */) {
    // ** addr: 0xb121e0, size: 0x30
    // 0xb121e0: EnterFrame
    //     0xb121e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb121e4: mov             fp, SP
    // 0xb121e8: AllocStack(0x8)
    //     0xb121e8: sub             SP, SP, #8
    // 0xb121ec: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xb121ec: stur            x2, [fp, #-8]
    // 0xb121f0: r0 = TtfFont()
    //     0xb121f0: bl              #0xb12210  ; AllocateTtfFontStub -> TtfFont (size=0x18)
    // 0xb121f4: ldur            x1, [fp, #-8]
    // 0xb121f8: StoreField: r0->field_f = r1
    //     0xb121f8: stur            w1, [x0, #0xf]
    // 0xb121fc: r1 = false
    //     0xb121fc: add             x1, NULL, #0x30  ; false
    // 0xb12200: StoreField: r0->field_13 = r1
    //     0xb12200: stur            w1, [x0, #0x13]
    // 0xb12204: LeaveFrame
    //     0xb12204: mov             SP, fp
    //     0xb12208: ldp             fp, lr, [SP], #0x10
    // 0xb1220c: ret
    //     0xb1220c: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc39dc4, size: 0x128
    // 0xc39dc4: EnterFrame
    //     0xc39dc4: stp             fp, lr, [SP, #-0x10]!
    //     0xc39dc8: mov             fp, SP
    // 0xc39dcc: AllocStack(0x18)
    //     0xc39dcc: sub             SP, SP, #0x18
    // 0xc39dd0: CheckStackOverflow
    //     0xc39dd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc39dd4: cmp             SP, x16
    //     0xc39dd8: b.ls            #0xc39ee0
    // 0xc39ddc: r1 = Null
    //     0xc39ddc: mov             x1, NULL
    // 0xc39de0: r2 = 6
    //     0xc39de0: movz            x2, #0x6
    // 0xc39de4: r0 = AllocateArray()
    //     0xc39de4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc39de8: stur            x0, [fp, #-8]
    // 0xc39dec: r16 = "<Type1 Font \""
    //     0xc39dec: add             x16, PP, #0x33, lsl #12  ; [pp+0x33838] "<Type1 Font \""
    //     0xc39df0: ldr             x16, [x16, #0x838]
    // 0xc39df4: StoreField: r0->field_f = r16
    //     0xc39df4: stur            w16, [x0, #0xf]
    // 0xc39df8: ldr             x1, [fp, #0x10]
    // 0xc39dfc: r2 = LoadClassIdInstr(r1)
    //     0xc39dfc: ldur            x2, [x1, #-1]
    //     0xc39e00: ubfx            x2, x2, #0xc, #0x14
    // 0xc39e04: cmp             x2, #0x332
    // 0xc39e08: b.ne            #0xc39e2c
    // 0xc39e0c: LoadField: r2 = r1->field_7
    //     0xc39e0c: ldur            w2, [x1, #7]
    // 0xc39e10: DecompressPointer r2
    //     0xc39e10: add             x2, x2, HEAP, lsl #32
    // 0xc39e14: r1 = _ConstMap len:14
    //     0xc39e14: add             x1, PP, #0x33, lsl #12  ; [pp+0x33840] Map<Type1Fonts, String>(14)
    //     0xc39e18: ldr             x1, [x1, #0x840]
    // 0xc39e1c: r0 = []()
    //     0xc39e1c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xc39e20: cmp             w0, NULL
    // 0xc39e24: b.eq            #0xc39ee8
    // 0xc39e28: b               #0xc39e94
    // 0xc39e2c: LoadField: r0 = r1->field_b
    //     0xc39e2c: ldur            w0, [x1, #0xb]
    // 0xc39e30: DecompressPointer r0
    //     0xc39e30: add             x0, x0, HEAP, lsl #32
    // 0xc39e34: cmp             w0, NULL
    // 0xc39e38: b.eq            #0xc39e6c
    // 0xc39e3c: r1 = LoadClassIdInstr(r0)
    //     0xc39e3c: ldur            x1, [x0, #-1]
    //     0xc39e40: ubfx            x1, x1, #0xc, #0x14
    // 0xc39e44: cmp             x1, #0x37f
    // 0xc39e48: b.ne            #0xc39e5c
    // 0xc39e4c: LoadField: r1 = r0->field_2f
    //     0xc39e4c: ldur            w1, [x0, #0x2f]
    // 0xc39e50: DecompressPointer r1
    //     0xc39e50: add             x1, x1, HEAP, lsl #32
    // 0xc39e54: mov             x0, x1
    // 0xc39e58: b               #0xc39e94
    // 0xc39e5c: LoadField: r1 = r0->field_3f
    //     0xc39e5c: ldur            w1, [x0, #0x3f]
    // 0xc39e60: DecompressPointer r1
    //     0xc39e60: add             x1, x1, HEAP, lsl #32
    // 0xc39e64: r0 = fontName()
    //     0xc39e64: bl              #0x7b73dc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::fontName
    // 0xc39e68: b               #0xc39e94
    // 0xc39e6c: LoadField: r2 = r1->field_f
    //     0xc39e6c: ldur            w2, [x1, #0xf]
    // 0xc39e70: DecompressPointer r2
    //     0xc39e70: add             x2, x2, HEAP, lsl #32
    // 0xc39e74: stur            x2, [fp, #-0x10]
    // 0xc39e78: r0 = TtfParser()
    //     0xc39e78: bl              #0xc39db8  ; AllocateTtfParserStub -> TtfParser (size=0x28)
    // 0xc39e7c: mov             x1, x0
    // 0xc39e80: ldur            x2, [fp, #-0x10]
    // 0xc39e84: stur            x0, [fp, #-0x10]
    // 0xc39e88: r0 = TtfParser()
    //     0xc39e88: bl              #0xc369f4  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::TtfParser
    // 0xc39e8c: ldur            x1, [fp, #-0x10]
    // 0xc39e90: r0 = fontName()
    //     0xc39e90: bl              #0x7b73dc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::fontName
    // 0xc39e94: ldur            x2, [fp, #-8]
    // 0xc39e98: mov             x1, x2
    // 0xc39e9c: ArrayStore: r1[1] = r0  ; List_4
    //     0xc39e9c: add             x25, x1, #0x13
    //     0xc39ea0: str             w0, [x25]
    //     0xc39ea4: tbz             w0, #0, #0xc39ec0
    //     0xc39ea8: ldurb           w16, [x1, #-1]
    //     0xc39eac: ldurb           w17, [x0, #-1]
    //     0xc39eb0: and             x16, x17, x16, lsr #2
    //     0xc39eb4: tst             x16, HEAP, lsr #32
    //     0xc39eb8: b.eq            #0xc39ec0
    //     0xc39ebc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc39ec0: r16 = "\">"
    //     0xc39ec0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c530] "\">"
    //     0xc39ec4: ldr             x16, [x16, #0x530]
    // 0xc39ec8: ArrayStore: r2[0] = r16  ; List_4
    //     0xc39ec8: stur            w16, [x2, #0x17]
    // 0xc39ecc: str             x2, [SP]
    // 0xc39ed0: r0 = _interpolate()
    //     0xc39ed0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc39ed4: LeaveFrame
    //     0xc39ed4: mov             SP, fp
    //     0xc39ed8: ldp             fp, lr, [SP], #0x10
    // 0xc39edc: ret
    //     0xc39edc: ret             
    // 0xc39ee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc39ee0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc39ee4: b               #0xc39ddc
    // 0xc39ee8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc39ee8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ getFont(/* No info */) {
    // ** addr: 0xe65b4c, size: 0x144
    // 0xe65b4c: EnterFrame
    //     0xe65b4c: stp             fp, lr, [SP, #-0x10]!
    //     0xe65b50: mov             fp, SP
    // 0xe65b54: AllocStack(0x28)
    //     0xe65b54: sub             SP, SP, #0x28
    // 0xe65b58: SetupParameters(Font this /* r1 => r1, fp-0x10 */)
    //     0xe65b58: stur            x1, [fp, #-0x10]
    // 0xe65b5c: CheckStackOverflow
    //     0xe65b5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe65b60: cmp             SP, x16
    //     0xe65b64: b.ls            #0xe65c88
    // 0xe65b68: LoadField: r0 = r1->field_b
    //     0xe65b68: ldur            w0, [x1, #0xb]
    // 0xe65b6c: DecompressPointer r0
    //     0xe65b6c: add             x0, x0, HEAP, lsl #32
    // 0xe65b70: cmp             w0, NULL
    // 0xe65b74: b.eq            #0xe65b90
    // 0xe65b78: LoadField: r3 = r0->field_23
    //     0xe65b78: ldur            w3, [x0, #0x23]
    // 0xe65b7c: DecompressPointer r3
    //     0xe65b7c: add             x3, x3, HEAP, lsl #32
    // 0xe65b80: LoadField: r4 = r2->field_13
    //     0xe65b80: ldur            w4, [x2, #0x13]
    // 0xe65b84: DecompressPointer r4
    //     0xe65b84: add             x4, x4, HEAP, lsl #32
    // 0xe65b88: cmp             w3, w4
    // 0xe65b8c: b.eq            #0xe65c7c
    // 0xe65b90: LoadField: r0 = r2->field_13
    //     0xe65b90: ldur            w0, [x2, #0x13]
    // 0xe65b94: DecompressPointer r0
    //     0xe65b94: add             x0, x0, HEAP, lsl #32
    // 0xe65b98: stur            x0, [fp, #-8]
    // 0xe65b9c: r2 = LoadClassIdInstr(r1)
    //     0xe65b9c: ldur            x2, [x1, #-1]
    //     0xe65ba0: ubfx            x2, x2, #0xc, #0x14
    // 0xe65ba4: cmp             x2, #0x332
    // 0xe65ba8: b.ne            #0xe65c1c
    // 0xe65bac: r1 = 2
    //     0xe65bac: movz            x1, #0x2
    // 0xe65bb0: r0 = AllocateContext()
    //     0xe65bb0: bl              #0xec126c  ; AllocateContextStub
    // 0xe65bb4: mov             x3, x0
    // 0xe65bb8: ldur            x0, [fp, #-0x10]
    // 0xe65bbc: stur            x3, [fp, #-0x20]
    // 0xe65bc0: StoreField: r3->field_f = r0
    //     0xe65bc0: stur            w0, [x3, #0xf]
    // 0xe65bc4: ldur            x2, [fp, #-8]
    // 0xe65bc8: StoreField: r3->field_13 = r2
    //     0xe65bc8: stur            w2, [x3, #0x13]
    // 0xe65bcc: LoadField: r4 = r2->field_2f
    //     0xe65bcc: ldur            w4, [x2, #0x2f]
    // 0xe65bd0: DecompressPointer r4
    //     0xe65bd0: add             x4, x4, HEAP, lsl #32
    // 0xe65bd4: mov             x2, x3
    // 0xe65bd8: stur            x4, [fp, #-0x18]
    // 0xe65bdc: r1 = Function '<anonymous closure>':.
    //     0xe65bdc: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dda8] AnonymousClosure: (0xe67bf4), of [package:pdf/src/widgets/font.dart] Font
    //     0xe65be0: ldr             x1, [x1, #0xda8]
    // 0xe65be4: r0 = AllocateClosure()
    //     0xe65be4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe65be8: ldur            x2, [fp, #-0x20]
    // 0xe65bec: r1 = Function '<anonymous closure>':.
    //     0xe65bec: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3ddb0] AnonymousClosure: (0xe6612c), of [package:pdf/src/widgets/font.dart] Font
    //     0xe65bf0: ldr             x1, [x1, #0xdb0]
    // 0xe65bf4: stur            x0, [fp, #-0x20]
    // 0xe65bf8: r0 = AllocateClosure()
    //     0xe65bf8: bl              #0xec1630  ; AllocateClosureStub
    // 0xe65bfc: str             x0, [SP]
    // 0xe65c00: ldur            x1, [fp, #-0x18]
    // 0xe65c04: ldur            x2, [fp, #-0x20]
    // 0xe65c08: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xe65c08: add             x4, PP, #0x25, lsl #12  ; [pp+0x25ea0] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xe65c0c: ldr             x4, [x4, #0xea0]
    // 0xe65c10: r0 = firstWhere()
    //     0xe65c10: bl              #0x8a019c  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::firstWhere
    // 0xe65c14: mov             x2, x0
    // 0xe65c18: b               #0xe65c54
    // 0xe65c1c: mov             x2, x0
    // 0xe65c20: mov             x0, x1
    // 0xe65c24: LoadField: r3 = r0->field_f
    //     0xe65c24: ldur            w3, [x0, #0xf]
    // 0xe65c28: DecompressPointer r3
    //     0xe65c28: add             x3, x3, HEAP, lsl #32
    // 0xe65c2c: stur            x3, [fp, #-0x18]
    // 0xe65c30: r1 = <PdfDict<PdfDataType>>
    //     0xe65c30: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe65c34: ldr             x1, [x1, #0x758]
    // 0xe65c38: r0 = PdfTtfFont()
    //     0xe65c38: bl              #0xe66120  ; AllocatePdfTtfFontStub -> PdfTtfFont (size=0x44)
    // 0xe65c3c: mov             x1, x0
    // 0xe65c40: ldur            x2, [fp, #-8]
    // 0xe65c44: ldur            x3, [fp, #-0x18]
    // 0xe65c48: stur            x0, [fp, #-8]
    // 0xe65c4c: r0 = PdfTtfFont()
    //     0xe65c4c: bl              #0xe65c90  ; [package:pdf/src/pdf/obj/ttffont.dart] PdfTtfFont::PdfTtfFont
    // 0xe65c50: ldur            x2, [fp, #-8]
    // 0xe65c54: ldur            x1, [fp, #-0x10]
    // 0xe65c58: mov             x0, x2
    // 0xe65c5c: StoreField: r1->field_b = r0
    //     0xe65c5c: stur            w0, [x1, #0xb]
    //     0xe65c60: ldurb           w16, [x1, #-1]
    //     0xe65c64: ldurb           w17, [x0, #-1]
    //     0xe65c68: and             x16, x17, x16, lsr #2
    //     0xe65c6c: tst             x16, HEAP, lsr #32
    //     0xe65c70: b.eq            #0xe65c78
    //     0xe65c74: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe65c78: mov             x0, x2
    // 0xe65c7c: LeaveFrame
    //     0xe65c7c: mov             SP, fp
    //     0xe65c80: ldp             fp, lr, [SP], #0x10
    // 0xe65c84: ret
    //     0xe65c84: ret             
    // 0xe65c88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe65c88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe65c8c: b               #0xe65b68
  }
  [closure] PdfFont <anonymous closure>(dynamic) {
    // ** addr: 0xe6612c, size: 0x310
    // 0xe6612c: EnterFrame
    //     0xe6612c: stp             fp, lr, [SP, #-0x10]!
    //     0xe66130: mov             fp, SP
    // 0xe66134: ldr             x0, [fp, #0x10]
    // 0xe66138: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe66138: ldur            w1, [x0, #0x17]
    // 0xe6613c: DecompressPointer r1
    //     0xe6613c: add             x1, x1, HEAP, lsl #32
    // 0xe66140: CheckStackOverflow
    //     0xe66140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe66144: cmp             SP, x16
    //     0xe66148: b.ls            #0xe66434
    // 0xe6614c: LoadField: r0 = r1->field_f
    //     0xe6614c: ldur            w0, [x1, #0xf]
    // 0xe66150: DecompressPointer r0
    //     0xe66150: add             x0, x0, HEAP, lsl #32
    // 0xe66154: LoadField: r2 = r0->field_7
    //     0xe66154: ldur            w2, [x0, #7]
    // 0xe66158: DecompressPointer r2
    //     0xe66158: add             x2, x2, HEAP, lsl #32
    // 0xe6615c: r16 = Instance_Type1Fonts
    //     0xe6615c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ddb8] Obj!Type1Fonts@e2e861
    //     0xe66160: ldr             x16, [x16, #0xdb8]
    // 0xe66164: cmp             w2, w16
    // 0xe66168: b.ne            #0xe6618c
    // 0xe6616c: LoadField: r2 = r1->field_13
    //     0xe6616c: ldur            w2, [x1, #0x13]
    // 0xe66170: DecompressPointer r2
    //     0xe66170: add             x2, x2, HEAP, lsl #32
    // 0xe66174: r1 = <PdfDict<PdfDataType>>
    //     0xe66174: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66178: ldr             x1, [x1, #0x758]
    // 0xe6617c: r0 = PdfFont.courier()
    //     0xe6617c: bl              #0xe67b10  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.courier
    // 0xe66180: LeaveFrame
    //     0xe66180: mov             SP, fp
    //     0xe66184: ldp             fp, lr, [SP], #0x10
    // 0xe66188: ret
    //     0xe66188: ret             
    // 0xe6618c: r16 = Instance_Type1Fonts
    //     0xe6618c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ddc0] Obj!Type1Fonts@e2e841
    //     0xe66190: ldr             x16, [x16, #0xdc0]
    // 0xe66194: cmp             w2, w16
    // 0xe66198: b.ne            #0xe661bc
    // 0xe6619c: LoadField: r2 = r1->field_13
    //     0xe6619c: ldur            w2, [x1, #0x13]
    // 0xe661a0: DecompressPointer r2
    //     0xe661a0: add             x2, x2, HEAP, lsl #32
    // 0xe661a4: r1 = <PdfDict<PdfDataType>>
    //     0xe661a4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe661a8: ldr             x1, [x1, #0x758]
    // 0xe661ac: r0 = PdfFont.courierBold()
    //     0xe661ac: bl              #0xe67a34  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.courierBold
    // 0xe661b0: LeaveFrame
    //     0xe661b0: mov             SP, fp
    //     0xe661b4: ldp             fp, lr, [SP], #0x10
    // 0xe661b8: ret
    //     0xe661b8: ret             
    // 0xe661bc: r16 = Instance_Type1Fonts
    //     0xe661bc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ddc8] Obj!Type1Fonts@e2e821
    //     0xe661c0: ldr             x16, [x16, #0xdc8]
    // 0xe661c4: cmp             w2, w16
    // 0xe661c8: b.ne            #0xe661ec
    // 0xe661cc: LoadField: r2 = r1->field_13
    //     0xe661cc: ldur            w2, [x1, #0x13]
    // 0xe661d0: DecompressPointer r2
    //     0xe661d0: add             x2, x2, HEAP, lsl #32
    // 0xe661d4: r1 = <PdfDict<PdfDataType>>
    //     0xe661d4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe661d8: ldr             x1, [x1, #0x758]
    // 0xe661dc: r0 = PdfFont.courierBoldOblique()
    //     0xe661dc: bl              #0xe67944  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.courierBoldOblique
    // 0xe661e0: LeaveFrame
    //     0xe661e0: mov             SP, fp
    //     0xe661e4: ldp             fp, lr, [SP], #0x10
    // 0xe661e8: ret
    //     0xe661e8: ret             
    // 0xe661ec: r16 = Instance_Type1Fonts
    //     0xe661ec: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ddd0] Obj!Type1Fonts@e2e801
    //     0xe661f0: ldr             x16, [x16, #0xdd0]
    // 0xe661f4: cmp             w2, w16
    // 0xe661f8: b.ne            #0xe6621c
    // 0xe661fc: LoadField: r2 = r1->field_13
    //     0xe661fc: ldur            w2, [x1, #0x13]
    // 0xe66200: DecompressPointer r2
    //     0xe66200: add             x2, x2, HEAP, lsl #32
    // 0xe66204: r1 = <PdfDict<PdfDataType>>
    //     0xe66204: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66208: ldr             x1, [x1, #0x758]
    // 0xe6620c: r0 = PdfFont.courierOblique()
    //     0xe6620c: bl              #0xe6785c  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.courierOblique
    // 0xe66210: LeaveFrame
    //     0xe66210: mov             SP, fp
    //     0xe66214: ldp             fp, lr, [SP], #0x10
    // 0xe66218: ret
    //     0xe66218: ret             
    // 0xe6621c: r16 = Instance_Type1Fonts
    //     0xe6621c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e590] Obj!Type1Fonts@e2e721
    //     0xe66220: ldr             x16, [x16, #0x590]
    // 0xe66224: cmp             w2, w16
    // 0xe66228: b.ne            #0xe6624c
    // 0xe6622c: LoadField: r2 = r1->field_13
    //     0xe6622c: ldur            w2, [x1, #0x13]
    // 0xe66230: DecompressPointer r2
    //     0xe66230: add             x2, x2, HEAP, lsl #32
    // 0xe66234: r1 = <PdfDict<PdfDataType>>
    //     0xe66234: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66238: ldr             x1, [x1, #0x758]
    // 0xe6623c: r0 = PdfFont.helvetica()
    //     0xe6623c: bl              #0xe67774  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.helvetica
    // 0xe66240: LeaveFrame
    //     0xe66240: mov             SP, fp
    //     0xe66244: ldp             fp, lr, [SP], #0x10
    // 0xe66248: ret
    //     0xe66248: ret             
    // 0xe6624c: r16 = Instance_Type1Fonts
    //     0xe6624c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e588] Obj!Type1Fonts@e2e701
    //     0xe66250: ldr             x16, [x16, #0x588]
    // 0xe66254: cmp             w2, w16
    // 0xe66258: b.ne            #0xe6627c
    // 0xe6625c: LoadField: r2 = r1->field_13
    //     0xe6625c: ldur            w2, [x1, #0x13]
    // 0xe66260: DecompressPointer r2
    //     0xe66260: add             x2, x2, HEAP, lsl #32
    // 0xe66264: r1 = <PdfDict<PdfDataType>>
    //     0xe66264: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66268: ldr             x1, [x1, #0x758]
    // 0xe6626c: r0 = PdfFont.helveticaBold()
    //     0xe6626c: bl              #0xe6768c  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.helveticaBold
    // 0xe66270: LeaveFrame
    //     0xe66270: mov             SP, fp
    //     0xe66274: ldp             fp, lr, [SP], #0x10
    // 0xe66278: ret
    //     0xe66278: ret             
    // 0xe6627c: r16 = Instance_Type1Fonts
    //     0xe6627c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e578] Obj!Type1Fonts@e2e6c1
    //     0xe66280: ldr             x16, [x16, #0x578]
    // 0xe66284: cmp             w2, w16
    // 0xe66288: b.ne            #0xe662ac
    // 0xe6628c: LoadField: r2 = r1->field_13
    //     0xe6628c: ldur            w2, [x1, #0x13]
    // 0xe66290: DecompressPointer r2
    //     0xe66290: add             x2, x2, HEAP, lsl #32
    // 0xe66294: r1 = <PdfDict<PdfDataType>>
    //     0xe66294: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66298: ldr             x1, [x1, #0x758]
    // 0xe6629c: r0 = PdfFont.helveticaBoldOblique()
    //     0xe6629c: bl              #0xe67598  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.helveticaBoldOblique
    // 0xe662a0: LeaveFrame
    //     0xe662a0: mov             SP, fp
    //     0xe662a4: ldp             fp, lr, [SP], #0x10
    // 0xe662a8: ret
    //     0xe662a8: ret             
    // 0xe662ac: r16 = Instance_Type1Fonts
    //     0xe662ac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e580] Obj!Type1Fonts@e2e6e1
    //     0xe662b0: ldr             x16, [x16, #0x580]
    // 0xe662b4: cmp             w2, w16
    // 0xe662b8: b.ne            #0xe662dc
    // 0xe662bc: LoadField: r2 = r1->field_13
    //     0xe662bc: ldur            w2, [x1, #0x13]
    // 0xe662c0: DecompressPointer r2
    //     0xe662c0: add             x2, x2, HEAP, lsl #32
    // 0xe662c4: r1 = <PdfDict<PdfDataType>>
    //     0xe662c4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe662c8: ldr             x1, [x1, #0x758]
    // 0xe662cc: r0 = PdfFont.helveticaOblique()
    //     0xe662cc: bl              #0xe674a4  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.helveticaOblique
    // 0xe662d0: LeaveFrame
    //     0xe662d0: mov             SP, fp
    //     0xe662d4: ldp             fp, lr, [SP], #0x10
    // 0xe662d8: ret
    //     0xe662d8: ret             
    // 0xe662dc: r16 = Instance_Type1Fonts
    //     0xe662dc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ddd8] Obj!Type1Fonts@e2e7e1
    //     0xe662e0: ldr             x16, [x16, #0xdd8]
    // 0xe662e4: cmp             w2, w16
    // 0xe662e8: b.ne            #0xe6630c
    // 0xe662ec: LoadField: r2 = r1->field_13
    //     0xe662ec: ldur            w2, [x1, #0x13]
    // 0xe662f0: DecompressPointer r2
    //     0xe662f0: add             x2, x2, HEAP, lsl #32
    // 0xe662f4: r1 = <PdfDict<PdfDataType>>
    //     0xe662f4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe662f8: ldr             x1, [x1, #0x758]
    // 0xe662fc: r0 = PdfFont.times()
    //     0xe662fc: bl              #0xe673bc  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.times
    // 0xe66300: LeaveFrame
    //     0xe66300: mov             SP, fp
    //     0xe66304: ldp             fp, lr, [SP], #0x10
    // 0xe66308: ret
    //     0xe66308: ret             
    // 0xe6630c: r16 = Instance_Type1Fonts
    //     0xe6630c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dde0] Obj!Type1Fonts@e2e7c1
    //     0xe66310: ldr             x16, [x16, #0xde0]
    // 0xe66314: cmp             w2, w16
    // 0xe66318: b.ne            #0xe6633c
    // 0xe6631c: LoadField: r2 = r1->field_13
    //     0xe6631c: ldur            w2, [x1, #0x13]
    // 0xe66320: DecompressPointer r2
    //     0xe66320: add             x2, x2, HEAP, lsl #32
    // 0xe66324: r1 = <PdfDict<PdfDataType>>
    //     0xe66324: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66328: ldr             x1, [x1, #0x758]
    // 0xe6632c: r0 = PdfFont.timesBold()
    //     0xe6632c: bl              #0xe672d4  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.timesBold
    // 0xe66330: LeaveFrame
    //     0xe66330: mov             SP, fp
    //     0xe66334: ldp             fp, lr, [SP], #0x10
    // 0xe66338: ret
    //     0xe66338: ret             
    // 0xe6633c: r16 = Instance_Type1Fonts
    //     0xe6633c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dde8] Obj!Type1Fonts@e2e7a1
    //     0xe66340: ldr             x16, [x16, #0xde8]
    // 0xe66344: cmp             w2, w16
    // 0xe66348: b.ne            #0xe6636c
    // 0xe6634c: LoadField: r2 = r1->field_13
    //     0xe6634c: ldur            w2, [x1, #0x13]
    // 0xe66350: DecompressPointer r2
    //     0xe66350: add             x2, x2, HEAP, lsl #32
    // 0xe66354: r1 = <PdfDict<PdfDataType>>
    //     0xe66354: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66358: ldr             x1, [x1, #0x758]
    // 0xe6635c: r0 = PdfFont.timesBoldItalic()
    //     0xe6635c: bl              #0xe671e0  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.timesBoldItalic
    // 0xe66360: LeaveFrame
    //     0xe66360: mov             SP, fp
    //     0xe66364: ldp             fp, lr, [SP], #0x10
    // 0xe66368: ret
    //     0xe66368: ret             
    // 0xe6636c: r16 = Instance_Type1Fonts
    //     0xe6636c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ddf0] Obj!Type1Fonts@e2e781
    //     0xe66370: ldr             x16, [x16, #0xdf0]
    // 0xe66374: cmp             w2, w16
    // 0xe66378: b.ne            #0xe6639c
    // 0xe6637c: LoadField: r2 = r1->field_13
    //     0xe6637c: ldur            w2, [x1, #0x13]
    // 0xe66380: DecompressPointer r2
    //     0xe66380: add             x2, x2, HEAP, lsl #32
    // 0xe66384: r1 = <PdfDict<PdfDataType>>
    //     0xe66384: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66388: ldr             x1, [x1, #0x758]
    // 0xe6638c: r0 = PdfFont.timesItalic()
    //     0xe6638c: bl              #0xe670ec  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.timesItalic
    // 0xe66390: LeaveFrame
    //     0xe66390: mov             SP, fp
    //     0xe66394: ldp             fp, lr, [SP], #0x10
    // 0xe66398: ret
    //     0xe66398: ret             
    // 0xe6639c: r16 = Instance_Type1Fonts
    //     0xe6639c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3ddf8] Obj!Type1Fonts@e2e761
    //     0xe663a0: ldr             x16, [x16, #0xdf8]
    // 0xe663a4: cmp             w2, w16
    // 0xe663a8: b.ne            #0xe663cc
    // 0xe663ac: LoadField: r2 = r1->field_13
    //     0xe663ac: ldur            w2, [x1, #0x13]
    // 0xe663b0: DecompressPointer r2
    //     0xe663b0: add             x2, x2, HEAP, lsl #32
    // 0xe663b4: r1 = <PdfDict<PdfDataType>>
    //     0xe663b4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe663b8: ldr             x1, [x1, #0x758]
    // 0xe663bc: r0 = PdfFont.symbol()
    //     0xe663bc: bl              #0xe67004  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.symbol
    // 0xe663c0: LeaveFrame
    //     0xe663c0: mov             SP, fp
    //     0xe663c4: ldp             fp, lr, [SP], #0x10
    // 0xe663c8: ret
    //     0xe663c8: ret             
    // 0xe663cc: r16 = Instance_Type1Fonts
    //     0xe663cc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3de00] Obj!Type1Fonts@e2e741
    //     0xe663d0: ldr             x16, [x16, #0xe00]
    // 0xe663d4: cmp             w2, w16
    // 0xe663d8: b.ne            #0xe663fc
    // 0xe663dc: LoadField: r2 = r1->field_13
    //     0xe663dc: ldur            w2, [x1, #0x13]
    // 0xe663e0: DecompressPointer r2
    //     0xe663e0: add             x2, x2, HEAP, lsl #32
    // 0xe663e4: r1 = <PdfDict<PdfDataType>>
    //     0xe663e4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe663e8: ldr             x1, [x1, #0x758]
    // 0xe663ec: r0 = PdfFont.zapfDingbats()
    //     0xe663ec: bl              #0xe6643c  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.zapfDingbats
    // 0xe663f0: LeaveFrame
    //     0xe663f0: mov             SP, fp
    //     0xe663f4: ldp             fp, lr, [SP], #0x10
    // 0xe663f8: ret
    //     0xe663f8: ret             
    // 0xe663fc: cmp             w2, NULL
    // 0xe66400: b.ne            #0xe66424
    // 0xe66404: LoadField: r2 = r1->field_13
    //     0xe66404: ldur            w2, [x1, #0x13]
    // 0xe66408: DecompressPointer r2
    //     0xe66408: add             x2, x2, HEAP, lsl #32
    // 0xe6640c: r1 = <PdfDict<PdfDataType>>
    //     0xe6640c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe66410: ldr             x1, [x1, #0x758]
    // 0xe66414: r0 = PdfFont.helvetica()
    //     0xe66414: bl              #0xe67774  ; [package:pdf/src/pdf/obj/font.dart] PdfFont::PdfFont.helvetica
    // 0xe66418: LeaveFrame
    //     0xe66418: mov             SP, fp
    //     0xe6641c: ldp             fp, lr, [SP], #0x10
    // 0xe66420: ret
    //     0xe66420: ret             
    // 0xe66424: r0 = Null
    //     0xe66424: mov             x0, NULL
    // 0xe66428: LeaveFrame
    //     0xe66428: mov             SP, fp
    //     0xe6642c: ldp             fp, lr, [SP], #0x10
    // 0xe66430: ret
    //     0xe66430: ret             
    // 0xe66434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe66434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe66438: b               #0xe6614c
  }
  [closure] bool <anonymous closure>(dynamic, PdfFont) {
    // ** addr: 0xe67bf4, size: 0x1c0
    // 0xe67bf4: EnterFrame
    //     0xe67bf4: stp             fp, lr, [SP, #-0x10]!
    //     0xe67bf8: mov             fp, SP
    // 0xe67bfc: AllocStack(0x28)
    //     0xe67bfc: sub             SP, SP, #0x28
    // 0xe67c00: SetupParameters()
    //     0xe67c00: ldr             x0, [fp, #0x18]
    //     0xe67c04: ldur            w2, [x0, #0x17]
    //     0xe67c08: add             x2, x2, HEAP, lsl #32
    //     0xe67c0c: stur            x2, [fp, #-0x10]
    // 0xe67c10: CheckStackOverflow
    //     0xe67c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe67c14: cmp             SP, x16
    //     0xe67c18: b.ls            #0xe67da4
    // 0xe67c1c: ldr             x3, [fp, #0x10]
    // 0xe67c20: r4 = LoadClassIdInstr(r3)
    //     0xe67c20: ldur            x4, [x3, #-1]
    //     0xe67c24: ubfx            x4, x4, #0xc, #0x14
    // 0xe67c28: stur            x4, [fp, #-8]
    // 0xe67c2c: cmp             x4, #0x37f
    // 0xe67c30: b.ne            #0xe67c40
    // 0xe67c34: LoadField: r0 = r3->field_2b
    //     0xe67c34: ldur            w0, [x3, #0x2b]
    // 0xe67c38: DecompressPointer r0
    //     0xe67c38: add             x0, x0, HEAP, lsl #32
    // 0xe67c3c: b               #0xe67cfc
    // 0xe67c40: r8 = 4278255360
    //     0xe67c40: movz            x8, #0xff00
    //     0xe67c44: movk            x8, #0xff00, lsl #16
    // 0xe67c48: r7 = 16711935
    //     0xe67c48: movz            x7, #0xff
    //     0xe67c4c: movk            x7, #0xff, lsl #16
    // 0xe67c50: r6 = 4294901760
    //     0xe67c50: orr             x6, xzr, #0xffff0000
    // 0xe67c54: r5 = 65535
    //     0xe67c54: orr             x5, xzr, #0xffff
    // 0xe67c58: LoadField: r0 = r3->field_3f
    //     0xe67c58: ldur            w0, [x3, #0x3f]
    // 0xe67c5c: DecompressPointer r0
    //     0xe67c5c: add             x0, x0, HEAP, lsl #32
    // 0xe67c60: LoadField: r9 = r0->field_7
    //     0xe67c60: ldur            w9, [x0, #7]
    // 0xe67c64: DecompressPointer r9
    //     0xe67c64: add             x9, x9, HEAP, lsl #32
    // 0xe67c68: LoadField: r0 = r9->field_13
    //     0xe67c68: ldur            w0, [x9, #0x13]
    // 0xe67c6c: r1 = LoadInt32Instr(r0)
    //     0xe67c6c: sbfx            x1, x0, #1, #0x1f
    // 0xe67c70: sub             x0, x1, #3
    // 0xe67c74: r1 = 0
    //     0xe67c74: movz            x1, #0
    // 0xe67c78: cmp             x1, x0
    // 0xe67c7c: b.hs            #0xe67dac
    // 0xe67c80: ArrayLoad: r0 = r9[0]  ; List_4
    //     0xe67c80: ldur            w0, [x9, #0x17]
    // 0xe67c84: DecompressPointer r0
    //     0xe67c84: add             x0, x0, HEAP, lsl #32
    // 0xe67c88: LoadField: r1 = r9->field_1b
    //     0xe67c88: ldur            w1, [x9, #0x1b]
    // 0xe67c8c: LoadField: r9 = r0->field_7
    //     0xe67c8c: ldur            x9, [x0, #7]
    // 0xe67c90: asr             w16, w1, #1
    // 0xe67c94: add             x16, x9, w16, sxtw
    // 0xe67c98: ldr             w0, [x16]
    // 0xe67c9c: and             x1, x0, x8
    // 0xe67ca0: ubfx            x1, x1, #0, #0x20
    // 0xe67ca4: asr             x8, x1, #8
    // 0xe67ca8: and             x1, x0, x7
    // 0xe67cac: ubfx            x1, x1, #0, #0x20
    // 0xe67cb0: lsl             x0, x1, #8
    // 0xe67cb4: orr             x1, x8, x0
    // 0xe67cb8: mov             x0, x1
    // 0xe67cbc: ubfx            x0, x0, #0, #0x20
    // 0xe67cc0: and             x7, x0, x6
    // 0xe67cc4: ubfx            x7, x7, #0, #0x20
    // 0xe67cc8: asr             x0, x7, #0x10
    // 0xe67ccc: ubfx            x1, x1, #0, #0x20
    // 0xe67cd0: and             x6, x1, x5
    // 0xe67cd4: ubfx            x6, x6, #0, #0x20
    // 0xe67cd8: lsl             x1, x6, #0x10
    // 0xe67cdc: orr             x5, x0, x1
    // 0xe67ce0: cmp             x5, #0x10, lsl #12
    // 0xe67ce4: b.ne            #0xe67cf4
    // 0xe67ce8: r0 = "/Type0"
    //     0xe67ce8: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e098] "/Type0"
    //     0xe67cec: ldr             x0, [x0, #0x98]
    // 0xe67cf0: b               #0xe67cfc
    // 0xe67cf4: LoadField: r0 = r3->field_2b
    //     0xe67cf4: ldur            w0, [x3, #0x2b]
    // 0xe67cf8: DecompressPointer r0
    //     0xe67cf8: add             x0, x0, HEAP, lsl #32
    // 0xe67cfc: r16 = "/Type1"
    //     0xe67cfc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3de48] "/Type1"
    //     0xe67d00: ldr             x16, [x16, #0xe48]
    // 0xe67d04: stp             x16, x0, [SP]
    // 0xe67d08: r0 = ==()
    //     0xe67d08: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xe67d0c: tbnz            w0, #4, #0xe67d94
    // 0xe67d10: ldur            x0, [fp, #-8]
    // 0xe67d14: cmp             x0, #0x37f
    // 0xe67d18: b.ne            #0xe67d30
    // 0xe67d1c: ldr             x0, [fp, #0x10]
    // 0xe67d20: LoadField: r1 = r0->field_2f
    //     0xe67d20: ldur            w1, [x0, #0x2f]
    // 0xe67d24: DecompressPointer r1
    //     0xe67d24: add             x1, x1, HEAP, lsl #32
    // 0xe67d28: mov             x3, x1
    // 0xe67d2c: b               #0xe67d44
    // 0xe67d30: ldr             x0, [fp, #0x10]
    // 0xe67d34: LoadField: r1 = r0->field_3f
    //     0xe67d34: ldur            w1, [x0, #0x3f]
    // 0xe67d38: DecompressPointer r1
    //     0xe67d38: add             x1, x1, HEAP, lsl #32
    // 0xe67d3c: r0 = fontName()
    //     0xe67d3c: bl              #0x7b73dc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::fontName
    // 0xe67d40: mov             x3, x0
    // 0xe67d44: ldur            x0, [fp, #-0x10]
    // 0xe67d48: stur            x3, [fp, #-0x18]
    // 0xe67d4c: LoadField: r1 = r0->field_f
    //     0xe67d4c: ldur            w1, [x0, #0xf]
    // 0xe67d50: DecompressPointer r1
    //     0xe67d50: add             x1, x1, HEAP, lsl #32
    // 0xe67d54: LoadField: r2 = r1->field_7
    //     0xe67d54: ldur            w2, [x1, #7]
    // 0xe67d58: DecompressPointer r2
    //     0xe67d58: add             x2, x2, HEAP, lsl #32
    // 0xe67d5c: r1 = _ConstMap len:14
    //     0xe67d5c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33840] Map<Type1Fonts, String>(14)
    //     0xe67d60: ldr             x1, [x1, #0x840]
    // 0xe67d64: r0 = []()
    //     0xe67d64: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xe67d68: cmp             w0, NULL
    // 0xe67d6c: b.eq            #0xe67db0
    // 0xe67d70: ldur            x1, [fp, #-0x18]
    // 0xe67d74: r2 = LoadClassIdInstr(r1)
    //     0xe67d74: ldur            x2, [x1, #-1]
    //     0xe67d78: ubfx            x2, x2, #0xc, #0x14
    // 0xe67d7c: stp             x0, x1, [SP]
    // 0xe67d80: mov             x0, x2
    // 0xe67d84: mov             lr, x0
    // 0xe67d88: ldr             lr, [x21, lr, lsl #3]
    // 0xe67d8c: blr             lr
    // 0xe67d90: b               #0xe67d98
    // 0xe67d94: r0 = false
    //     0xe67d94: add             x0, NULL, #0x30  ; false
    // 0xe67d98: LeaveFrame
    //     0xe67d98: mov             SP, fp
    //     0xe67d9c: ldp             fp, lr, [SP], #0x10
    // 0xe67da0: ret
    //     0xe67da0: ret             
    // 0xe67da4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe67da4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe67da8: b               #0xe67c1c
    // 0xe67dac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe67dac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe67db0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe67db0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  factory _ Font.courierBoldOblique(/* No info */) {
    // ** addr: 0xe70dc8, size: 0x24
    // 0xe70dc8: EnterFrame
    //     0xe70dc8: stp             fp, lr, [SP, #-0x10]!
    //     0xe70dcc: mov             fp, SP
    // 0xe70dd0: r0 = Font()
    //     0xe70dd0: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70dd4: r1 = Instance_Type1Fonts
    //     0xe70dd4: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3ddc8] Obj!Type1Fonts@e2e821
    //     0xe70dd8: ldr             x1, [x1, #0xdc8]
    // 0xe70ddc: StoreField: r0->field_7 = r1
    //     0xe70ddc: stur            w1, [x0, #7]
    // 0xe70de0: LeaveFrame
    //     0xe70de0: mov             SP, fp
    //     0xe70de4: ldp             fp, lr, [SP], #0x10
    // 0xe70de8: ret
    //     0xe70de8: ret             
  }
  factory _ Font.courierOblique(/* No info */) {
    // ** addr: 0xe70dec, size: 0x24
    // 0xe70dec: EnterFrame
    //     0xe70dec: stp             fp, lr, [SP, #-0x10]!
    //     0xe70df0: mov             fp, SP
    // 0xe70df4: r0 = Font()
    //     0xe70df4: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70df8: r1 = Instance_Type1Fonts
    //     0xe70df8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3ddd0] Obj!Type1Fonts@e2e801
    //     0xe70dfc: ldr             x1, [x1, #0xdd0]
    // 0xe70e00: StoreField: r0->field_7 = r1
    //     0xe70e00: stur            w1, [x0, #7]
    // 0xe70e04: LeaveFrame
    //     0xe70e04: mov             SP, fp
    //     0xe70e08: ldp             fp, lr, [SP], #0x10
    // 0xe70e0c: ret
    //     0xe70e0c: ret             
  }
  factory _ Font.courierBold(/* No info */) {
    // ** addr: 0xe70e10, size: 0x24
    // 0xe70e10: EnterFrame
    //     0xe70e10: stp             fp, lr, [SP, #-0x10]!
    //     0xe70e14: mov             fp, SP
    // 0xe70e18: r0 = Font()
    //     0xe70e18: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70e1c: r1 = Instance_Type1Fonts
    //     0xe70e1c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3ddc0] Obj!Type1Fonts@e2e841
    //     0xe70e20: ldr             x1, [x1, #0xdc0]
    // 0xe70e24: StoreField: r0->field_7 = r1
    //     0xe70e24: stur            w1, [x0, #7]
    // 0xe70e28: LeaveFrame
    //     0xe70e28: mov             SP, fp
    //     0xe70e2c: ldp             fp, lr, [SP], #0x10
    // 0xe70e30: ret
    //     0xe70e30: ret             
  }
  factory _ Font.courier(/* No info */) {
    // ** addr: 0xe70e34, size: 0x24
    // 0xe70e34: EnterFrame
    //     0xe70e34: stp             fp, lr, [SP, #-0x10]!
    //     0xe70e38: mov             fp, SP
    // 0xe70e3c: r0 = Font()
    //     0xe70e3c: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70e40: r1 = Instance_Type1Fonts
    //     0xe70e40: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3ddb8] Obj!Type1Fonts@e2e861
    //     0xe70e44: ldr             x1, [x1, #0xdb8]
    // 0xe70e48: StoreField: r0->field_7 = r1
    //     0xe70e48: stur            w1, [x0, #7]
    // 0xe70e4c: LeaveFrame
    //     0xe70e4c: mov             SP, fp
    //     0xe70e50: ldp             fp, lr, [SP], #0x10
    // 0xe70e54: ret
    //     0xe70e54: ret             
  }
  factory _ Font.timesBoldItalic(/* No info */) {
    // ** addr: 0xe70e58, size: 0x24
    // 0xe70e58: EnterFrame
    //     0xe70e58: stp             fp, lr, [SP, #-0x10]!
    //     0xe70e5c: mov             fp, SP
    // 0xe70e60: r0 = Font()
    //     0xe70e60: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70e64: r1 = Instance_Type1Fonts
    //     0xe70e64: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dde8] Obj!Type1Fonts@e2e7a1
    //     0xe70e68: ldr             x1, [x1, #0xde8]
    // 0xe70e6c: StoreField: r0->field_7 = r1
    //     0xe70e6c: stur            w1, [x0, #7]
    // 0xe70e70: LeaveFrame
    //     0xe70e70: mov             SP, fp
    //     0xe70e74: ldp             fp, lr, [SP], #0x10
    // 0xe70e78: ret
    //     0xe70e78: ret             
  }
  factory _ Font.timesItalic(/* No info */) {
    // ** addr: 0xe70e7c, size: 0x24
    // 0xe70e7c: EnterFrame
    //     0xe70e7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe70e80: mov             fp, SP
    // 0xe70e84: r0 = Font()
    //     0xe70e84: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70e88: r1 = Instance_Type1Fonts
    //     0xe70e88: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3ddf0] Obj!Type1Fonts@e2e781
    //     0xe70e8c: ldr             x1, [x1, #0xdf0]
    // 0xe70e90: StoreField: r0->field_7 = r1
    //     0xe70e90: stur            w1, [x0, #7]
    // 0xe70e94: LeaveFrame
    //     0xe70e94: mov             SP, fp
    //     0xe70e98: ldp             fp, lr, [SP], #0x10
    // 0xe70e9c: ret
    //     0xe70e9c: ret             
  }
  factory _ Font.timesBold(/* No info */) {
    // ** addr: 0xe70ea0, size: 0x24
    // 0xe70ea0: EnterFrame
    //     0xe70ea0: stp             fp, lr, [SP, #-0x10]!
    //     0xe70ea4: mov             fp, SP
    // 0xe70ea8: r0 = Font()
    //     0xe70ea8: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70eac: r1 = Instance_Type1Fonts
    //     0xe70eac: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dde0] Obj!Type1Fonts@e2e7c1
    //     0xe70eb0: ldr             x1, [x1, #0xde0]
    // 0xe70eb4: StoreField: r0->field_7 = r1
    //     0xe70eb4: stur            w1, [x0, #7]
    // 0xe70eb8: LeaveFrame
    //     0xe70eb8: mov             SP, fp
    //     0xe70ebc: ldp             fp, lr, [SP], #0x10
    // 0xe70ec0: ret
    //     0xe70ec0: ret             
  }
  factory _ Font.times(/* No info */) {
    // ** addr: 0xe70ec4, size: 0x24
    // 0xe70ec4: EnterFrame
    //     0xe70ec4: stp             fp, lr, [SP, #-0x10]!
    //     0xe70ec8: mov             fp, SP
    // 0xe70ecc: r0 = Font()
    //     0xe70ecc: bl              #0xb10400  ; AllocateFontStub -> Font (size=0x10)
    // 0xe70ed0: r1 = Instance_Type1Fonts
    //     0xe70ed0: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3ddd8] Obj!Type1Fonts@e2e7e1
    //     0xe70ed4: ldr             x1, [x1, #0xdd8]
    // 0xe70ed8: StoreField: r0->field_7 = r1
    //     0xe70ed8: stur            w1, [x0, #7]
    // 0xe70edc: LeaveFrame
    //     0xe70edc: mov             SP, fp
    //     0xe70ee0: ldp             fp, lr, [SP], #0x10
    // 0xe70ee4: ret
    //     0xe70ee4: ret             
  }
}

// class id: 819, size: 0x18, field offset: 0x10
class TtfFont extends Font {

  _ toString(/* No info */) {
    // ** addr: 0xc36940, size: 0xb4
    // 0xc36940: EnterFrame
    //     0xc36940: stp             fp, lr, [SP, #-0x10]!
    //     0xc36944: mov             fp, SP
    // 0xc36948: AllocStack(0x18)
    //     0xc36948: sub             SP, SP, #0x18
    // 0xc3694c: CheckStackOverflow
    //     0xc3694c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc36950: cmp             SP, x16
    //     0xc36954: b.ls            #0xc369ec
    // 0xc36958: ldr             x0, [fp, #0x10]
    // 0xc3695c: LoadField: r2 = r0->field_f
    //     0xc3695c: ldur            w2, [x0, #0xf]
    // 0xc36960: DecompressPointer r2
    //     0xc36960: add             x2, x2, HEAP, lsl #32
    // 0xc36964: stur            x2, [fp, #-8]
    // 0xc36968: r0 = TtfParser()
    //     0xc36968: bl              #0xc39db8  ; AllocateTtfParserStub -> TtfParser (size=0x28)
    // 0xc3696c: mov             x1, x0
    // 0xc36970: ldur            x2, [fp, #-8]
    // 0xc36974: stur            x0, [fp, #-8]
    // 0xc36978: r0 = TtfParser()
    //     0xc36978: bl              #0xc369f4  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::TtfParser
    // 0xc3697c: r1 = Null
    //     0xc3697c: mov             x1, NULL
    // 0xc36980: r2 = 6
    //     0xc36980: movz            x2, #0x6
    // 0xc36984: r0 = AllocateArray()
    //     0xc36984: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc36988: stur            x0, [fp, #-0x10]
    // 0xc3698c: r16 = "<TrueType Font \""
    //     0xc3698c: add             x16, PP, #0x33, lsl #12  ; [pp+0x338a8] "<TrueType Font \""
    //     0xc36990: ldr             x16, [x16, #0x8a8]
    // 0xc36994: StoreField: r0->field_f = r16
    //     0xc36994: stur            w16, [x0, #0xf]
    // 0xc36998: ldur            x1, [fp, #-8]
    // 0xc3699c: r0 = fontName()
    //     0xc3699c: bl              #0x7b73dc  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::fontName
    // 0xc369a0: ldur            x1, [fp, #-0x10]
    // 0xc369a4: ArrayStore: r1[1] = r0  ; List_4
    //     0xc369a4: add             x25, x1, #0x13
    //     0xc369a8: str             w0, [x25]
    //     0xc369ac: tbz             w0, #0, #0xc369c8
    //     0xc369b0: ldurb           w16, [x1, #-1]
    //     0xc369b4: ldurb           w17, [x0, #-1]
    //     0xc369b8: and             x16, x17, x16, lsr #2
    //     0xc369bc: tst             x16, HEAP, lsr #32
    //     0xc369c0: b.eq            #0xc369c8
    //     0xc369c4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc369c8: ldur            x0, [fp, #-0x10]
    // 0xc369cc: r16 = "\">"
    //     0xc369cc: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c530] "\">"
    //     0xc369d0: ldr             x16, [x16, #0x530]
    // 0xc369d4: ArrayStore: r0[0] = r16  ; List_4
    //     0xc369d4: stur            w16, [x0, #0x17]
    // 0xc369d8: str             x0, [SP]
    // 0xc369dc: r0 = _interpolate()
    //     0xc369dc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc369e0: LeaveFrame
    //     0xc369e0: mov             SP, fp
    //     0xc369e4: ldp             fp, lr, [SP], #0x10
    // 0xc369e8: ret
    //     0xc369e8: ret             
    // 0xc369ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc369ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc369f0: b               #0xc36958
  }
}

// class id: 6795, size: 0x14, field offset: 0x14
enum Type1Fonts extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e0b8, size: 0x64
    // 0xc4e0b8: EnterFrame
    //     0xc4e0b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e0bc: mov             fp, SP
    // 0xc4e0c0: AllocStack(0x10)
    //     0xc4e0c0: sub             SP, SP, #0x10
    // 0xc4e0c4: SetupParameters(Type1Fonts this /* r1 => r0, fp-0x8 */)
    //     0xc4e0c4: mov             x0, x1
    //     0xc4e0c8: stur            x1, [fp, #-8]
    // 0xc4e0cc: CheckStackOverflow
    //     0xc4e0cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e0d0: cmp             SP, x16
    //     0xc4e0d4: b.ls            #0xc4e114
    // 0xc4e0d8: r1 = Null
    //     0xc4e0d8: mov             x1, NULL
    // 0xc4e0dc: r2 = 4
    //     0xc4e0dc: movz            x2, #0x4
    // 0xc4e0e0: r0 = AllocateArray()
    //     0xc4e0e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e0e4: r16 = "Type1Fonts."
    //     0xc4e0e4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33830] "Type1Fonts."
    //     0xc4e0e8: ldr             x16, [x16, #0x830]
    // 0xc4e0ec: StoreField: r0->field_f = r16
    //     0xc4e0ec: stur            w16, [x0, #0xf]
    // 0xc4e0f0: ldur            x1, [fp, #-8]
    // 0xc4e0f4: LoadField: r2 = r1->field_f
    //     0xc4e0f4: ldur            w2, [x1, #0xf]
    // 0xc4e0f8: DecompressPointer r2
    //     0xc4e0f8: add             x2, x2, HEAP, lsl #32
    // 0xc4e0fc: StoreField: r0->field_13 = r2
    //     0xc4e0fc: stur            w2, [x0, #0x13]
    // 0xc4e100: str             x0, [SP]
    // 0xc4e104: r0 = _interpolate()
    //     0xc4e104: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e108: LeaveFrame
    //     0xc4e108: mov             SP, fp
    //     0xc4e10c: ldp             fp, lr, [SP], #0x10
    // 0xc4e110: ret
    //     0xc4e110: ret             
    // 0xc4e114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e118: b               #0xc4e0d8
  }
}
