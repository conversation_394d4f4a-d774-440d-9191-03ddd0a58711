// lib: , url: package:pdf/src/widgets/geometry.dart

// class id: 1050851, size: 0x8
class :: {

  static _ applyBoxFit(/* No info */) {
    // ** addr: 0xe68f18, size: 0x390
    // 0xe68f18: EnterFrame
    //     0xe68f18: stp             fp, lr, [SP, #-0x10]!
    //     0xe68f1c: mov             fp, SP
    // 0xe68f20: AllocStack(0x40)
    //     0xe68f20: sub             SP, SP, #0x40
    // 0xe68f24: d0 = 0.000000
    //     0xe68f24: eor             v0.16b, v0.16b, v0.16b
    // 0xe68f28: stur            x2, [fp, #-8]
    // 0xe68f2c: stur            x3, [fp, #-0x10]
    // 0xe68f30: LoadField: d1 = r2->field_f
    //     0xe68f30: ldur            d1, [x2, #0xf]
    // 0xe68f34: stur            d1, [fp, #-0x38]
    // 0xe68f38: fcmp            d0, d1
    // 0xe68f3c: b.ge            #0xe68f70
    // 0xe68f40: LoadField: d2 = r2->field_7
    //     0xe68f40: ldur            d2, [x2, #7]
    // 0xe68f44: stur            d2, [fp, #-0x30]
    // 0xe68f48: fcmp            d0, d2
    // 0xe68f4c: b.ge            #0xe68f70
    // 0xe68f50: LoadField: d3 = r3->field_f
    //     0xe68f50: ldur            d3, [x3, #0xf]
    // 0xe68f54: stur            d3, [fp, #-0x20]
    // 0xe68f58: fcmp            d0, d3
    // 0xe68f5c: b.ge            #0xe68f70
    // 0xe68f60: LoadField: d4 = r3->field_7
    //     0xe68f60: ldur            d4, [x3, #7]
    // 0xe68f64: stur            d4, [fp, #-0x28]
    // 0xe68f68: fcmp            d0, d4
    // 0xe68f6c: b.lt            #0xe68f84
    // 0xe68f70: r0 = Instance_FittedSizes
    //     0xe68f70: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3eda8] Obj!FittedSizes@e0c471
    //     0xe68f74: ldr             x0, [x0, #0xda8]
    // 0xe68f78: LeaveFrame
    //     0xe68f78: mov             SP, fp
    //     0xe68f7c: ldp             fp, lr, [SP], #0x10
    // 0xe68f80: ret
    //     0xe68f80: ret             
    // 0xe68f84: LoadField: r0 = r1->field_7
    //     0xe68f84: ldur            x0, [x1, #7]
    // 0xe68f88: cmp             x0, #3
    // 0xe68f8c: b.gt            #0xe690c8
    // 0xe68f90: cmp             x0, #1
    // 0xe68f94: b.gt            #0xe69008
    // 0xe68f98: cmp             x0, #0
    // 0xe68f9c: b.gt            #0xe68fac
    // 0xe68fa0: mov             x1, x2
    // 0xe68fa4: mov             x0, x3
    // 0xe68fa8: b               #0xe69280
    // 0xe68fac: fdiv            d0, d4, d3
    // 0xe68fb0: fdiv            d5, d2, d1
    // 0xe68fb4: fcmp            d0, d5
    // 0xe68fb8: b.le            #0xe68fe0
    // 0xe68fbc: fmul            d0, d2, d3
    // 0xe68fc0: fdiv            d2, d0, d1
    // 0xe68fc4: stur            d2, [fp, #-0x18]
    // 0xe68fc8: r0 = PdfPoint()
    //     0xe68fc8: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe68fcc: ldur            d0, [fp, #-0x18]
    // 0xe68fd0: StoreField: r0->field_7 = d0
    //     0xe68fd0: stur            d0, [x0, #7]
    // 0xe68fd4: ldur            d3, [fp, #-0x20]
    // 0xe68fd8: StoreField: r0->field_f = d3
    //     0xe68fd8: stur            d3, [x0, #0xf]
    // 0xe68fdc: b               #0xe69000
    // 0xe68fe0: fmul            d0, d1, d4
    // 0xe68fe4: fdiv            d1, d0, d2
    // 0xe68fe8: stur            d1, [fp, #-0x18]
    // 0xe68fec: r0 = PdfPoint()
    //     0xe68fec: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe68ff0: ldur            d0, [fp, #-0x28]
    // 0xe68ff4: StoreField: r0->field_7 = d0
    //     0xe68ff4: stur            d0, [x0, #7]
    // 0xe68ff8: ldur            d0, [fp, #-0x18]
    // 0xe68ffc: StoreField: r0->field_f = d0
    //     0xe68ffc: stur            d0, [x0, #0xf]
    // 0xe69000: ldur            x1, [fp, #-8]
    // 0xe69004: b               #0xe69280
    // 0xe69008: mov             v0.16b, v4.16b
    // 0xe6900c: cmp             x0, #2
    // 0xe69010: b.gt            #0xe69074
    // 0xe69014: fdiv            d4, d0, d3
    // 0xe69018: fdiv            d5, d2, d1
    // 0xe6901c: fcmp            d4, d5
    // 0xe69020: b.le            #0xe69048
    // 0xe69024: fmul            d1, d2, d3
    // 0xe69028: fdiv            d3, d1, d0
    // 0xe6902c: stur            d3, [fp, #-0x18]
    // 0xe69030: r0 = PdfPoint()
    //     0xe69030: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe69034: ldur            d1, [fp, #-0x30]
    // 0xe69038: StoreField: r0->field_7 = d1
    //     0xe69038: stur            d1, [x0, #7]
    // 0xe6903c: ldur            d0, [fp, #-0x18]
    // 0xe69040: StoreField: r0->field_f = d0
    //     0xe69040: stur            d0, [x0, #0xf]
    // 0xe69044: b               #0xe69068
    // 0xe69048: fmul            d2, d1, d0
    // 0xe6904c: fdiv            d0, d2, d3
    // 0xe69050: stur            d0, [fp, #-0x18]
    // 0xe69054: r0 = PdfPoint()
    //     0xe69054: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe69058: ldur            d0, [fp, #-0x18]
    // 0xe6905c: StoreField: r0->field_7 = d0
    //     0xe6905c: stur            d0, [x0, #7]
    // 0xe69060: ldur            d1, [fp, #-0x38]
    // 0xe69064: StoreField: r0->field_f = d1
    //     0xe69064: stur            d1, [x0, #0xf]
    // 0xe69068: mov             x1, x0
    // 0xe6906c: ldur            x0, [fp, #-0x10]
    // 0xe69070: b               #0xe69280
    // 0xe69074: mov             v1.16b, v2.16b
    // 0xe69078: fmul            d2, d1, d3
    // 0xe6907c: fdiv            d3, d2, d0
    // 0xe69080: stur            d3, [fp, #-0x18]
    // 0xe69084: r0 = PdfPoint()
    //     0xe69084: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe69088: ldur            d2, [fp, #-0x30]
    // 0xe6908c: stur            x0, [fp, #-0x10]
    // 0xe69090: StoreField: r0->field_7 = d2
    //     0xe69090: stur            d2, [x0, #7]
    // 0xe69094: ldur            d0, [fp, #-0x18]
    // 0xe69098: StoreField: r0->field_f = d0
    //     0xe69098: stur            d0, [x0, #0xf]
    // 0xe6909c: ldur            d1, [fp, #-0x28]
    // 0xe690a0: fmul            d3, d0, d1
    // 0xe690a4: fdiv            d0, d3, d2
    // 0xe690a8: stur            d0, [fp, #-0x18]
    // 0xe690ac: r0 = PdfPoint()
    //     0xe690ac: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe690b0: ldur            d4, [fp, #-0x28]
    // 0xe690b4: StoreField: r0->field_7 = d4
    //     0xe690b4: stur            d4, [x0, #7]
    // 0xe690b8: ldur            d0, [fp, #-0x18]
    // 0xe690bc: StoreField: r0->field_f = d0
    //     0xe690bc: stur            d0, [x0, #0xf]
    // 0xe690c0: ldur            x1, [fp, #-0x10]
    // 0xe690c4: b               #0xe69280
    // 0xe690c8: cmp             x0, #5
    // 0xe690cc: b.gt            #0xe69214
    // 0xe690d0: cmp             x0, #4
    // 0xe690d4: b.gt            #0xe69128
    // 0xe690d8: fmul            d0, d1, d4
    // 0xe690dc: fdiv            d2, d0, d3
    // 0xe690e0: stur            d2, [fp, #-0x18]
    // 0xe690e4: r0 = PdfPoint()
    //     0xe690e4: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe690e8: ldur            d0, [fp, #-0x18]
    // 0xe690ec: stur            x0, [fp, #-0x10]
    // 0xe690f0: StoreField: r0->field_7 = d0
    //     0xe690f0: stur            d0, [x0, #7]
    // 0xe690f4: ldur            d1, [fp, #-0x38]
    // 0xe690f8: StoreField: r0->field_f = d1
    //     0xe690f8: stur            d1, [x0, #0xf]
    // 0xe690fc: ldur            d2, [fp, #-0x20]
    // 0xe69100: fmul            d3, d0, d2
    // 0xe69104: fdiv            d0, d3, d1
    // 0xe69108: stur            d0, [fp, #-0x18]
    // 0xe6910c: r0 = PdfPoint()
    //     0xe6910c: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe69110: ldur            d0, [fp, #-0x18]
    // 0xe69114: StoreField: r0->field_7 = d0
    //     0xe69114: stur            d0, [x0, #7]
    // 0xe69118: ldur            d3, [fp, #-0x20]
    // 0xe6911c: StoreField: r0->field_f = d3
    //     0xe6911c: stur            d3, [x0, #0xf]
    // 0xe69120: ldur            x1, [fp, #-0x10]
    // 0xe69124: b               #0xe69280
    // 0xe69128: fcmp            d2, d4
    // 0xe6912c: b.le            #0xe69138
    // 0xe69130: mov             v2.16b, v4.16b
    // 0xe69134: b               #0xe69184
    // 0xe69138: fcmp            d4, d2
    // 0xe6913c: b.gt            #0xe69184
    // 0xe69140: fcmp            d2, d0
    // 0xe69144: b.ne            #0xe69158
    // 0xe69148: fadd            d5, d2, d4
    // 0xe6914c: fmul            d6, d5, d2
    // 0xe69150: fmul            d2, d6, d4
    // 0xe69154: b               #0xe69184
    // 0xe69158: fcmp            d2, d0
    // 0xe6915c: b.ne            #0xe69178
    // 0xe69160: fcmp            d4, #0.0
    // 0xe69164: b.vs            #0xe69178
    // 0xe69168: b.ne            #0xe69174
    // 0xe6916c: r0 = 0.000000
    //     0xe6916c: fmov            x0, d4
    // 0xe69170: cmp             x0, #0
    // 0xe69174: b.lt            #0xe69180
    // 0xe69178: fcmp            d4, d4
    // 0xe6917c: b.vc            #0xe69184
    // 0xe69180: mov             v2.16b, v4.16b
    // 0xe69184: stur            d2, [fp, #-0x40]
    // 0xe69188: fcmp            d1, d3
    // 0xe6918c: b.le            #0xe69198
    // 0xe69190: mov             v0.16b, v3.16b
    // 0xe69194: b               #0xe691f4
    // 0xe69198: fcmp            d3, d1
    // 0xe6919c: b.le            #0xe691a8
    // 0xe691a0: mov             v0.16b, v1.16b
    // 0xe691a4: b               #0xe691f4
    // 0xe691a8: fcmp            d1, d0
    // 0xe691ac: b.ne            #0xe691c0
    // 0xe691b0: fadd            d0, d1, d3
    // 0xe691b4: fmul            d4, d0, d1
    // 0xe691b8: fmul            d0, d4, d3
    // 0xe691bc: b               #0xe691f4
    // 0xe691c0: fcmp            d1, d0
    // 0xe691c4: b.ne            #0xe691e0
    // 0xe691c8: fcmp            d3, #0.0
    // 0xe691cc: b.vs            #0xe691e0
    // 0xe691d0: b.ne            #0xe691dc
    // 0xe691d4: r0 = 0.000000
    //     0xe691d4: fmov            x0, d3
    // 0xe691d8: cmp             x0, #0
    // 0xe691dc: b.lt            #0xe691e8
    // 0xe691e0: fcmp            d3, d3
    // 0xe691e4: b.vc            #0xe691f0
    // 0xe691e8: mov             v0.16b, v3.16b
    // 0xe691ec: b               #0xe691f4
    // 0xe691f0: mov             v0.16b, v1.16b
    // 0xe691f4: stur            d0, [fp, #-0x18]
    // 0xe691f8: r0 = PdfPoint()
    //     0xe691f8: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe691fc: ldur            d0, [fp, #-0x40]
    // 0xe69200: StoreField: r0->field_7 = d0
    //     0xe69200: stur            d0, [x0, #7]
    // 0xe69204: ldur            d0, [fp, #-0x18]
    // 0xe69208: StoreField: r0->field_f = d0
    //     0xe69208: stur            d0, [x0, #0xf]
    // 0xe6920c: mov             x1, x0
    // 0xe69210: b               #0xe69280
    // 0xe69214: fdiv            d0, d2, d1
    // 0xe69218: stur            d0, [fp, #-0x40]
    // 0xe6921c: fcmp            d1, d3
    // 0xe69220: b.le            #0xe69248
    // 0xe69224: fmul            d1, d3, d0
    // 0xe69228: stur            d1, [fp, #-0x18]
    // 0xe6922c: r0 = PdfPoint()
    //     0xe6922c: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe69230: ldur            d0, [fp, #-0x18]
    // 0xe69234: StoreField: r0->field_7 = d0
    //     0xe69234: stur            d0, [x0, #7]
    // 0xe69238: ldur            d1, [fp, #-0x20]
    // 0xe6923c: StoreField: r0->field_f = d1
    //     0xe6923c: stur            d1, [x0, #0xf]
    // 0xe69240: mov             v1.16b, v0.16b
    // 0xe69244: b               #0xe69250
    // 0xe69248: ldur            x0, [fp, #-8]
    // 0xe6924c: mov             v1.16b, v2.16b
    // 0xe69250: ldur            d0, [fp, #-0x28]
    // 0xe69254: fcmp            d1, d0
    // 0xe69258: b.le            #0xe6927c
    // 0xe6925c: ldur            d1, [fp, #-0x40]
    // 0xe69260: fdiv            d2, d0, d1
    // 0xe69264: stur            d2, [fp, #-0x18]
    // 0xe69268: r0 = PdfPoint()
    //     0xe69268: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe6926c: ldur            d0, [fp, #-0x28]
    // 0xe69270: StoreField: r0->field_7 = d0
    //     0xe69270: stur            d0, [x0, #7]
    // 0xe69274: ldur            d0, [fp, #-0x18]
    // 0xe69278: StoreField: r0->field_f = d0
    //     0xe69278: stur            d0, [x0, #0xf]
    // 0xe6927c: ldur            x1, [fp, #-8]
    // 0xe69280: stur            x1, [fp, #-8]
    // 0xe69284: stur            x0, [fp, #-0x10]
    // 0xe69288: r0 = FittedSizes()
    //     0xe69288: bl              #0xe692a8  ; AllocateFittedSizesStub -> FittedSizes (size=0x10)
    // 0xe6928c: ldur            x1, [fp, #-8]
    // 0xe69290: StoreField: r0->field_7 = r1
    //     0xe69290: stur            w1, [x0, #7]
    // 0xe69294: ldur            x1, [fp, #-0x10]
    // 0xe69298: StoreField: r0->field_b = r1
    //     0xe69298: stur            w1, [x0, #0xb]
    // 0xe6929c: LeaveFrame
    //     0xe6929c: mov             SP, fp
    //     0xe692a0: ldp             fp, lr, [SP], #0x10
    // 0xe692a4: ret
    //     0xe692a4: ret             
  }
}

// class id: 812, size: 0x10, field offset: 0x8
//   const constructor, 
class FittedSizes extends Object {

  PdfPoint field_8;
  PdfPoint field_c;
}

// class id: 813, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class AlignmentGeometry extends Object {
}

// class id: 814, size: 0x18, field offset: 0x8
//   const constructor, 
class Alignment extends AlignmentGeometry {

  _Mint field_8;
  _Mint field_10;

  _ toString(/* No info */) {
    // ** addr: 0xc3a978, size: 0x38
    // 0xc3a978: EnterFrame
    //     0xc3a978: stp             fp, lr, [SP, #-0x10]!
    //     0xc3a97c: mov             fp, SP
    // 0xc3a980: CheckStackOverflow
    //     0xc3a980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3a984: cmp             SP, x16
    //     0xc3a988: b.ls            #0xc3a9a8
    // 0xc3a98c: ldr             x0, [fp, #0x10]
    // 0xc3a990: LoadField: d0 = r0->field_7
    //     0xc3a990: ldur            d0, [x0, #7]
    // 0xc3a994: LoadField: d1 = r0->field_f
    //     0xc3a994: ldur            d1, [x0, #0xf]
    // 0xc3a998: r0 = _stringify()
    //     0xc3a998: bl              #0xc3a9b0  ; [package:pdf/src/widgets/geometry.dart] Alignment::_stringify
    // 0xc3a99c: LeaveFrame
    //     0xc3a99c: mov             SP, fp
    //     0xc3a9a0: ldp             fp, lr, [SP], #0x10
    // 0xc3a9a4: ret
    //     0xc3a9a4: ret             
    // 0xc3a9a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3a9a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3a9ac: b               #0xc3a98c
  }
  static _ _stringify(/* No info */) {
    // ** addr: 0xc3a9b0, size: 0x2b0
    // 0xc3a9b0: EnterFrame
    //     0xc3a9b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc3a9b4: mov             fp, SP
    // 0xc3a9b8: AllocStack(0x20)
    //     0xc3a9b8: sub             SP, SP, #0x20
    // 0xc3a9bc: d2 = -1.000000
    //     0xc3a9bc: fmov            d2, #-1.00000000
    // 0xc3a9c0: stur            d0, [fp, #-0x10]
    // 0xc3a9c4: stur            d1, [fp, #-0x18]
    // 0xc3a9c8: CheckStackOverflow
    //     0xc3a9c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3a9cc: cmp             SP, x16
    //     0xc3a9d0: b.ls            #0xc3ac20
    // 0xc3a9d4: fcmp            d0, d2
    // 0xc3a9d8: b.ne            #0xc3a9f8
    // 0xc3a9dc: fcmp            d1, d2
    // 0xc3a9e0: b.ne            #0xc3a9f8
    // 0xc3a9e4: r0 = "Alignment.topLeft"
    //     0xc3a9e4: add             x0, PP, #0x33, lsl #12  ; [pp+0x337a8] "Alignment.topLeft"
    //     0xc3a9e8: ldr             x0, [x0, #0x7a8]
    // 0xc3a9ec: LeaveFrame
    //     0xc3a9ec: mov             SP, fp
    //     0xc3a9f0: ldp             fp, lr, [SP], #0x10
    // 0xc3a9f4: ret
    //     0xc3a9f4: ret             
    // 0xc3a9f8: d3 = 0.000000
    //     0xc3a9f8: eor             v3.16b, v3.16b, v3.16b
    // 0xc3a9fc: fcmp            d0, d3
    // 0xc3aa00: b.ne            #0xc3aa20
    // 0xc3aa04: fcmp            d1, d2
    // 0xc3aa08: b.ne            #0xc3aa20
    // 0xc3aa0c: r0 = "Alignment.topCenter"
    //     0xc3aa0c: add             x0, PP, #0x33, lsl #12  ; [pp+0x337b0] "Alignment.topCenter"
    //     0xc3aa10: ldr             x0, [x0, #0x7b0]
    // 0xc3aa14: LeaveFrame
    //     0xc3aa14: mov             SP, fp
    //     0xc3aa18: ldp             fp, lr, [SP], #0x10
    // 0xc3aa1c: ret
    //     0xc3aa1c: ret             
    // 0xc3aa20: d4 = 1.000000
    //     0xc3aa20: fmov            d4, #1.00000000
    // 0xc3aa24: fcmp            d0, d4
    // 0xc3aa28: b.ne            #0xc3aa48
    // 0xc3aa2c: fcmp            d1, d2
    // 0xc3aa30: b.ne            #0xc3aa48
    // 0xc3aa34: r0 = "Alignment.topRight"
    //     0xc3aa34: add             x0, PP, #0x33, lsl #12  ; [pp+0x337b8] "Alignment.topRight"
    //     0xc3aa38: ldr             x0, [x0, #0x7b8]
    // 0xc3aa3c: LeaveFrame
    //     0xc3aa3c: mov             SP, fp
    //     0xc3aa40: ldp             fp, lr, [SP], #0x10
    // 0xc3aa44: ret
    //     0xc3aa44: ret             
    // 0xc3aa48: fcmp            d0, d2
    // 0xc3aa4c: b.ne            #0xc3aa6c
    // 0xc3aa50: fcmp            d1, d3
    // 0xc3aa54: b.ne            #0xc3aa6c
    // 0xc3aa58: r0 = "Alignment.centerLeft"
    //     0xc3aa58: add             x0, PP, #0x33, lsl #12  ; [pp+0x337c0] "Alignment.centerLeft"
    //     0xc3aa5c: ldr             x0, [x0, #0x7c0]
    // 0xc3aa60: LeaveFrame
    //     0xc3aa60: mov             SP, fp
    //     0xc3aa64: ldp             fp, lr, [SP], #0x10
    // 0xc3aa68: ret
    //     0xc3aa68: ret             
    // 0xc3aa6c: fcmp            d0, d3
    // 0xc3aa70: b.ne            #0xc3aa90
    // 0xc3aa74: fcmp            d1, d3
    // 0xc3aa78: b.ne            #0xc3aa90
    // 0xc3aa7c: r0 = "Alignment.center"
    //     0xc3aa7c: add             x0, PP, #0x33, lsl #12  ; [pp+0x337c8] "Alignment.center"
    //     0xc3aa80: ldr             x0, [x0, #0x7c8]
    // 0xc3aa84: LeaveFrame
    //     0xc3aa84: mov             SP, fp
    //     0xc3aa88: ldp             fp, lr, [SP], #0x10
    // 0xc3aa8c: ret
    //     0xc3aa8c: ret             
    // 0xc3aa90: fcmp            d0, d4
    // 0xc3aa94: b.ne            #0xc3aab4
    // 0xc3aa98: fcmp            d1, d3
    // 0xc3aa9c: b.ne            #0xc3aab4
    // 0xc3aaa0: r0 = "Alignment.centerRight"
    //     0xc3aaa0: add             x0, PP, #0x33, lsl #12  ; [pp+0x337d0] "Alignment.centerRight"
    //     0xc3aaa4: ldr             x0, [x0, #0x7d0]
    // 0xc3aaa8: LeaveFrame
    //     0xc3aaa8: mov             SP, fp
    //     0xc3aaac: ldp             fp, lr, [SP], #0x10
    // 0xc3aab0: ret
    //     0xc3aab0: ret             
    // 0xc3aab4: fcmp            d0, d2
    // 0xc3aab8: b.ne            #0xc3aad8
    // 0xc3aabc: fcmp            d1, d4
    // 0xc3aac0: b.ne            #0xc3aad8
    // 0xc3aac4: r0 = "Alignment.bottomLeft"
    //     0xc3aac4: add             x0, PP, #0x33, lsl #12  ; [pp+0x337d8] "Alignment.bottomLeft"
    //     0xc3aac8: ldr             x0, [x0, #0x7d8]
    // 0xc3aacc: LeaveFrame
    //     0xc3aacc: mov             SP, fp
    //     0xc3aad0: ldp             fp, lr, [SP], #0x10
    // 0xc3aad4: ret
    //     0xc3aad4: ret             
    // 0xc3aad8: fcmp            d0, d3
    // 0xc3aadc: b.ne            #0xc3aafc
    // 0xc3aae0: fcmp            d1, d4
    // 0xc3aae4: b.ne            #0xc3aafc
    // 0xc3aae8: r0 = "Alignment.bottomCenter"
    //     0xc3aae8: add             x0, PP, #0x33, lsl #12  ; [pp+0x337e0] "Alignment.bottomCenter"
    //     0xc3aaec: ldr             x0, [x0, #0x7e0]
    // 0xc3aaf0: LeaveFrame
    //     0xc3aaf0: mov             SP, fp
    //     0xc3aaf4: ldp             fp, lr, [SP], #0x10
    // 0xc3aaf8: ret
    //     0xc3aaf8: ret             
    // 0xc3aafc: fcmp            d0, d4
    // 0xc3ab00: b.ne            #0xc3ab20
    // 0xc3ab04: fcmp            d1, d4
    // 0xc3ab08: b.ne            #0xc3ab20
    // 0xc3ab0c: r0 = "Alignment.bottomRight"
    //     0xc3ab0c: add             x0, PP, #0x33, lsl #12  ; [pp+0x337e8] "Alignment.bottomRight"
    //     0xc3ab10: ldr             x0, [x0, #0x7e8]
    // 0xc3ab14: LeaveFrame
    //     0xc3ab14: mov             SP, fp
    //     0xc3ab18: ldp             fp, lr, [SP], #0x10
    // 0xc3ab1c: ret
    //     0xc3ab1c: ret             
    // 0xc3ab20: r1 = Null
    //     0xc3ab20: mov             x1, NULL
    // 0xc3ab24: r2 = 10
    //     0xc3ab24: movz            x2, #0xa
    // 0xc3ab28: r0 = AllocateArray()
    //     0xc3ab28: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3ab2c: stur            x0, [fp, #-8]
    // 0xc3ab30: r16 = "Alignment("
    //     0xc3ab30: add             x16, PP, #0x33, lsl #12  ; [pp+0x337f0] "Alignment("
    //     0xc3ab34: ldr             x16, [x16, #0x7f0]
    // 0xc3ab38: StoreField: r0->field_f = r16
    //     0xc3ab38: stur            w16, [x0, #0xf]
    // 0xc3ab3c: ldur            d0, [fp, #-0x10]
    // 0xc3ab40: r1 = inline_Allocate_Double()
    //     0xc3ab40: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3ab44: add             x1, x1, #0x10
    //     0xc3ab48: cmp             x2, x1
    //     0xc3ab4c: b.ls            #0xc3ac28
    //     0xc3ab50: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3ab54: sub             x1, x1, #0xf
    //     0xc3ab58: movz            x2, #0xe15c
    //     0xc3ab5c: movk            x2, #0x3, lsl #16
    //     0xc3ab60: stur            x2, [x1, #-1]
    // 0xc3ab64: StoreField: r1->field_7 = d0
    //     0xc3ab64: stur            d0, [x1, #7]
    // 0xc3ab68: r2 = 1
    //     0xc3ab68: movz            x2, #0x1
    // 0xc3ab6c: r0 = toStringAsFixed()
    //     0xc3ab6c: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3ab70: ldur            x1, [fp, #-8]
    // 0xc3ab74: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3ab74: add             x25, x1, #0x13
    //     0xc3ab78: str             w0, [x25]
    //     0xc3ab7c: tbz             w0, #0, #0xc3ab98
    //     0xc3ab80: ldurb           w16, [x1, #-1]
    //     0xc3ab84: ldurb           w17, [x0, #-1]
    //     0xc3ab88: and             x16, x17, x16, lsr #2
    //     0xc3ab8c: tst             x16, HEAP, lsr #32
    //     0xc3ab90: b.eq            #0xc3ab98
    //     0xc3ab94: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3ab98: ldur            x0, [fp, #-8]
    // 0xc3ab9c: r16 = ", "
    //     0xc3ab9c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3aba0: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3aba0: stur            w16, [x0, #0x17]
    // 0xc3aba4: ldur            d0, [fp, #-0x18]
    // 0xc3aba8: r1 = inline_Allocate_Double()
    //     0xc3aba8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3abac: add             x1, x1, #0x10
    //     0xc3abb0: cmp             x2, x1
    //     0xc3abb4: b.ls            #0xc3ac44
    //     0xc3abb8: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3abbc: sub             x1, x1, #0xf
    //     0xc3abc0: movz            x2, #0xe15c
    //     0xc3abc4: movk            x2, #0x3, lsl #16
    //     0xc3abc8: stur            x2, [x1, #-1]
    // 0xc3abcc: StoreField: r1->field_7 = d0
    //     0xc3abcc: stur            d0, [x1, #7]
    // 0xc3abd0: r2 = 1
    //     0xc3abd0: movz            x2, #0x1
    // 0xc3abd4: r0 = toStringAsFixed()
    //     0xc3abd4: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3abd8: ldur            x1, [fp, #-8]
    // 0xc3abdc: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3abdc: add             x25, x1, #0x1b
    //     0xc3abe0: str             w0, [x25]
    //     0xc3abe4: tbz             w0, #0, #0xc3ac00
    //     0xc3abe8: ldurb           w16, [x1, #-1]
    //     0xc3abec: ldurb           w17, [x0, #-1]
    //     0xc3abf0: and             x16, x17, x16, lsr #2
    //     0xc3abf4: tst             x16, HEAP, lsr #32
    //     0xc3abf8: b.eq            #0xc3ac00
    //     0xc3abfc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3ac00: ldur            x0, [fp, #-8]
    // 0xc3ac04: r16 = ")"
    //     0xc3ac04: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3ac08: StoreField: r0->field_1f = r16
    //     0xc3ac08: stur            w16, [x0, #0x1f]
    // 0xc3ac0c: str             x0, [SP]
    // 0xc3ac10: r0 = _interpolate()
    //     0xc3ac10: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3ac14: LeaveFrame
    //     0xc3ac14: mov             SP, fp
    //     0xc3ac18: ldp             fp, lr, [SP], #0x10
    // 0xc3ac1c: ret
    //     0xc3ac1c: ret             
    // 0xc3ac20: r0 = StackOverflowSharedWithFPURegs()
    //     0xc3ac20: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc3ac24: b               #0xc3a9d4
    // 0xc3ac28: SaveReg d0
    //     0xc3ac28: str             q0, [SP, #-0x10]!
    // 0xc3ac2c: SaveReg r0
    //     0xc3ac2c: str             x0, [SP, #-8]!
    // 0xc3ac30: r0 = AllocateDouble()
    //     0xc3ac30: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3ac34: mov             x1, x0
    // 0xc3ac38: RestoreReg r0
    //     0xc3ac38: ldr             x0, [SP], #8
    // 0xc3ac3c: RestoreReg d0
    //     0xc3ac3c: ldr             q0, [SP], #0x10
    // 0xc3ac40: b               #0xc3ab64
    // 0xc3ac44: SaveReg d0
    //     0xc3ac44: str             q0, [SP, #-0x10]!
    // 0xc3ac48: SaveReg r0
    //     0xc3ac48: str             x0, [SP, #-8]!
    // 0xc3ac4c: r0 = AllocateDouble()
    //     0xc3ac4c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3ac50: mov             x1, x0
    // 0xc3ac54: RestoreReg r0
    //     0xc3ac54: ldr             x0, [SP], #8
    // 0xc3ac58: RestoreReg d0
    //     0xc3ac58: ldr             q0, [SP], #0x10
    // 0xc3ac5c: b               #0xc3abcc
  }
  _ inscribe(/* No info */) {
    // ** addr: 0xe68de0, size: 0x98
    // 0xe68de0: EnterFrame
    //     0xe68de0: stp             fp, lr, [SP, #-0x10]!
    //     0xe68de4: mov             fp, SP
    // 0xe68de8: AllocStack(0x20)
    //     0xe68de8: sub             SP, SP, #0x20
    // 0xe68dec: d0 = 2.000000
    //     0xe68dec: fmov            d0, #2.00000000
    // 0xe68df0: ArrayLoad: d1 = r3[0]  ; List_8
    //     0xe68df0: ldur            d1, [x3, #0x17]
    // 0xe68df4: LoadField: d2 = r2->field_7
    //     0xe68df4: ldur            d2, [x2, #7]
    // 0xe68df8: stur            d2, [fp, #-0x20]
    // 0xe68dfc: fsub            d3, d1, d2
    // 0xe68e00: fdiv            d1, d3, d0
    // 0xe68e04: LoadField: d3 = r3->field_1f
    //     0xe68e04: ldur            d3, [x3, #0x1f]
    // 0xe68e08: LoadField: d4 = r2->field_f
    //     0xe68e08: ldur            d4, [x2, #0xf]
    // 0xe68e0c: stur            d4, [fp, #-0x18]
    // 0xe68e10: fsub            d5, d3, d4
    // 0xe68e14: fdiv            d3, d5, d0
    // 0xe68e18: LoadField: d0 = r3->field_7
    //     0xe68e18: ldur            d0, [x3, #7]
    // 0xe68e1c: fadd            d5, d0, d1
    // 0xe68e20: LoadField: d0 = r1->field_7
    //     0xe68e20: ldur            d0, [x1, #7]
    // 0xe68e24: fmul            d6, d0, d1
    // 0xe68e28: fadd            d0, d5, d6
    // 0xe68e2c: stur            d0, [fp, #-0x10]
    // 0xe68e30: LoadField: d1 = r3->field_f
    //     0xe68e30: ldur            d1, [x3, #0xf]
    // 0xe68e34: fadd            d5, d1, d3
    // 0xe68e38: LoadField: d1 = r1->field_f
    //     0xe68e38: ldur            d1, [x1, #0xf]
    // 0xe68e3c: fmul            d6, d1, d3
    // 0xe68e40: fadd            d1, d5, d6
    // 0xe68e44: stur            d1, [fp, #-8]
    // 0xe68e48: r0 = PdfRect()
    //     0xe68e48: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe68e4c: ldur            d0, [fp, #-0x10]
    // 0xe68e50: StoreField: r0->field_7 = d0
    //     0xe68e50: stur            d0, [x0, #7]
    // 0xe68e54: ldur            d0, [fp, #-8]
    // 0xe68e58: StoreField: r0->field_f = d0
    //     0xe68e58: stur            d0, [x0, #0xf]
    // 0xe68e5c: ldur            d0, [fp, #-0x20]
    // 0xe68e60: ArrayStore: r0[0] = d0  ; List_8
    //     0xe68e60: stur            d0, [x0, #0x17]
    // 0xe68e64: ldur            d0, [fp, #-0x18]
    // 0xe68e68: StoreField: r0->field_1f = d0
    //     0xe68e68: stur            d0, [x0, #0x1f]
    // 0xe68e6c: LeaveFrame
    //     0xe68e6c: mov             SP, fp
    //     0xe68e70: ldp             fp, lr, [SP], #0x10
    // 0xe68e74: ret
    //     0xe68e74: ret             
  }
}

// class id: 815, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class EdgeInsetsGeometry extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc3a114, size: 0x864
    // 0xc3a114: EnterFrame
    //     0xc3a114: stp             fp, lr, [SP, #-0x10]!
    //     0xc3a118: mov             fp, SP
    // 0xc3a11c: AllocStack(0x20)
    //     0xc3a11c: sub             SP, SP, #0x20
    // 0xc3a120: d0 = 0.000000
    //     0xc3a120: eor             v0.16b, v0.16b, v0.16b
    // 0xc3a124: CheckStackOverflow
    //     0xc3a124: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3a128: cmp             SP, x16
    //     0xc3a12c: b.ls            #0xc3a83c
    // 0xc3a130: fcmp            d0, d0
    // 0xc3a134: b.ne            #0xc3a42c
    // 0xc3a138: fcmp            d0, d0
    // 0xc3a13c: b.ne            #0xc3a424
    // 0xc3a140: ldr             x0, [fp, #0x10]
    // 0xc3a144: LoadField: d1 = r0->field_7
    //     0xc3a144: ldur            d1, [x0, #7]
    // 0xc3a148: stur            d1, [fp, #-0x10]
    // 0xc3a14c: fcmp            d1, d0
    // 0xc3a150: b.ne            #0xc3a18c
    // 0xc3a154: ArrayLoad: d2 = r0[0]  ; List_8
    //     0xc3a154: ldur            d2, [x0, #0x17]
    // 0xc3a158: fcmp            d2, d0
    // 0xc3a15c: b.ne            #0xc3a18c
    // 0xc3a160: LoadField: d2 = r0->field_f
    //     0xc3a160: ldur            d2, [x0, #0xf]
    // 0xc3a164: fcmp            d2, d0
    // 0xc3a168: b.ne            #0xc3a18c
    // 0xc3a16c: LoadField: d2 = r0->field_1f
    //     0xc3a16c: ldur            d2, [x0, #0x1f]
    // 0xc3a170: fcmp            d2, d0
    // 0xc3a174: b.ne            #0xc3a18c
    // 0xc3a178: r0 = "EdgeInsets.zero"
    //     0xc3a178: add             x0, PP, #0x33, lsl #12  ; [pp+0x337f8] "EdgeInsets.zero"
    //     0xc3a17c: ldr             x0, [x0, #0x7f8]
    // 0xc3a180: LeaveFrame
    //     0xc3a180: mov             SP, fp
    //     0xc3a184: ldp             fp, lr, [SP], #0x10
    // 0xc3a188: ret
    //     0xc3a188: ret             
    // 0xc3a18c: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xc3a18c: ldur            d0, [x0, #0x17]
    // 0xc3a190: stur            d0, [fp, #-0x18]
    // 0xc3a194: fcmp            d1, d0
    // 0xc3a198: b.ne            #0xc3a24c
    // 0xc3a19c: LoadField: d2 = r0->field_f
    //     0xc3a19c: ldur            d2, [x0, #0xf]
    // 0xc3a1a0: fcmp            d0, d2
    // 0xc3a1a4: b.ne            #0xc3a24c
    // 0xc3a1a8: LoadField: d3 = r0->field_1f
    //     0xc3a1a8: ldur            d3, [x0, #0x1f]
    // 0xc3a1ac: fcmp            d2, d3
    // 0xc3a1b0: b.ne            #0xc3a24c
    // 0xc3a1b4: r1 = Null
    //     0xc3a1b4: mov             x1, NULL
    // 0xc3a1b8: r2 = 6
    //     0xc3a1b8: movz            x2, #0x6
    // 0xc3a1bc: r0 = AllocateArray()
    //     0xc3a1bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3a1c0: stur            x0, [fp, #-8]
    // 0xc3a1c4: r16 = "EdgeInsets.all("
    //     0xc3a1c4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33800] "EdgeInsets.all("
    //     0xc3a1c8: ldr             x16, [x16, #0x800]
    // 0xc3a1cc: StoreField: r0->field_f = r16
    //     0xc3a1cc: stur            w16, [x0, #0xf]
    // 0xc3a1d0: ldur            d1, [fp, #-0x10]
    // 0xc3a1d4: r1 = inline_Allocate_Double()
    //     0xc3a1d4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a1d8: add             x1, x1, #0x10
    //     0xc3a1dc: cmp             x2, x1
    //     0xc3a1e0: b.ls            #0xc3a844
    //     0xc3a1e4: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a1e8: sub             x1, x1, #0xf
    //     0xc3a1ec: movz            x2, #0xe15c
    //     0xc3a1f0: movk            x2, #0x3, lsl #16
    //     0xc3a1f4: stur            x2, [x1, #-1]
    // 0xc3a1f8: StoreField: r1->field_7 = d1
    //     0xc3a1f8: stur            d1, [x1, #7]
    // 0xc3a1fc: r2 = 1
    //     0xc3a1fc: movz            x2, #0x1
    // 0xc3a200: r0 = toStringAsFixed()
    //     0xc3a200: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a204: ldur            x1, [fp, #-8]
    // 0xc3a208: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3a208: add             x25, x1, #0x13
    //     0xc3a20c: str             w0, [x25]
    //     0xc3a210: tbz             w0, #0, #0xc3a22c
    //     0xc3a214: ldurb           w16, [x1, #-1]
    //     0xc3a218: ldurb           w17, [x0, #-1]
    //     0xc3a21c: and             x16, x17, x16, lsr #2
    //     0xc3a220: tst             x16, HEAP, lsr #32
    //     0xc3a224: b.eq            #0xc3a22c
    //     0xc3a228: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a22c: ldur            x0, [fp, #-8]
    // 0xc3a230: r16 = ")"
    //     0xc3a230: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3a234: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3a234: stur            w16, [x0, #0x17]
    // 0xc3a238: str             x0, [SP]
    // 0xc3a23c: r0 = _interpolate()
    //     0xc3a23c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3a240: LeaveFrame
    //     0xc3a240: mov             SP, fp
    //     0xc3a244: ldp             fp, lr, [SP], #0x10
    // 0xc3a248: ret
    //     0xc3a248: ret             
    // 0xc3a24c: r1 = Null
    //     0xc3a24c: mov             x1, NULL
    // 0xc3a250: r2 = 18
    //     0xc3a250: movz            x2, #0x12
    // 0xc3a254: r0 = AllocateArray()
    //     0xc3a254: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3a258: stur            x0, [fp, #-8]
    // 0xc3a25c: r16 = "EdgeInsets("
    //     0xc3a25c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33808] "EdgeInsets("
    //     0xc3a260: ldr             x16, [x16, #0x808]
    // 0xc3a264: StoreField: r0->field_f = r16
    //     0xc3a264: stur            w16, [x0, #0xf]
    // 0xc3a268: ldur            d0, [fp, #-0x10]
    // 0xc3a26c: r1 = inline_Allocate_Double()
    //     0xc3a26c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a270: add             x1, x1, #0x10
    //     0xc3a274: cmp             x2, x1
    //     0xc3a278: b.ls            #0xc3a860
    //     0xc3a27c: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a280: sub             x1, x1, #0xf
    //     0xc3a284: movz            x2, #0xe15c
    //     0xc3a288: movk            x2, #0x3, lsl #16
    //     0xc3a28c: stur            x2, [x1, #-1]
    // 0xc3a290: StoreField: r1->field_7 = d0
    //     0xc3a290: stur            d0, [x1, #7]
    // 0xc3a294: r2 = 1
    //     0xc3a294: movz            x2, #0x1
    // 0xc3a298: r0 = toStringAsFixed()
    //     0xc3a298: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a29c: ldur            x1, [fp, #-8]
    // 0xc3a2a0: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3a2a0: add             x25, x1, #0x13
    //     0xc3a2a4: str             w0, [x25]
    //     0xc3a2a8: tbz             w0, #0, #0xc3a2c4
    //     0xc3a2ac: ldurb           w16, [x1, #-1]
    //     0xc3a2b0: ldurb           w17, [x0, #-1]
    //     0xc3a2b4: and             x16, x17, x16, lsr #2
    //     0xc3a2b8: tst             x16, HEAP, lsr #32
    //     0xc3a2bc: b.eq            #0xc3a2c4
    //     0xc3a2c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a2c4: ldur            x0, [fp, #-8]
    // 0xc3a2c8: r16 = ", "
    //     0xc3a2c8: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a2cc: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3a2cc: stur            w16, [x0, #0x17]
    // 0xc3a2d0: ldr             x3, [fp, #0x10]
    // 0xc3a2d4: LoadField: d0 = r3->field_f
    //     0xc3a2d4: ldur            d0, [x3, #0xf]
    // 0xc3a2d8: r1 = inline_Allocate_Double()
    //     0xc3a2d8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a2dc: add             x1, x1, #0x10
    //     0xc3a2e0: cmp             x2, x1
    //     0xc3a2e4: b.ls            #0xc3a87c
    //     0xc3a2e8: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a2ec: sub             x1, x1, #0xf
    //     0xc3a2f0: movz            x2, #0xe15c
    //     0xc3a2f4: movk            x2, #0x3, lsl #16
    //     0xc3a2f8: stur            x2, [x1, #-1]
    // 0xc3a2fc: StoreField: r1->field_7 = d0
    //     0xc3a2fc: stur            d0, [x1, #7]
    // 0xc3a300: r2 = 1
    //     0xc3a300: movz            x2, #0x1
    // 0xc3a304: r0 = toStringAsFixed()
    //     0xc3a304: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a308: ldur            x1, [fp, #-8]
    // 0xc3a30c: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3a30c: add             x25, x1, #0x1b
    //     0xc3a310: str             w0, [x25]
    //     0xc3a314: tbz             w0, #0, #0xc3a330
    //     0xc3a318: ldurb           w16, [x1, #-1]
    //     0xc3a31c: ldurb           w17, [x0, #-1]
    //     0xc3a320: and             x16, x17, x16, lsr #2
    //     0xc3a324: tst             x16, HEAP, lsr #32
    //     0xc3a328: b.eq            #0xc3a330
    //     0xc3a32c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a330: ldur            x0, [fp, #-8]
    // 0xc3a334: r16 = ", "
    //     0xc3a334: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a338: StoreField: r0->field_1f = r16
    //     0xc3a338: stur            w16, [x0, #0x1f]
    // 0xc3a33c: ldur            d0, [fp, #-0x18]
    // 0xc3a340: r1 = inline_Allocate_Double()
    //     0xc3a340: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a344: add             x1, x1, #0x10
    //     0xc3a348: cmp             x2, x1
    //     0xc3a34c: b.ls            #0xc3a898
    //     0xc3a350: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a354: sub             x1, x1, #0xf
    //     0xc3a358: movz            x2, #0xe15c
    //     0xc3a35c: movk            x2, #0x3, lsl #16
    //     0xc3a360: stur            x2, [x1, #-1]
    // 0xc3a364: StoreField: r1->field_7 = d0
    //     0xc3a364: stur            d0, [x1, #7]
    // 0xc3a368: r2 = 1
    //     0xc3a368: movz            x2, #0x1
    // 0xc3a36c: r0 = toStringAsFixed()
    //     0xc3a36c: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a370: ldur            x1, [fp, #-8]
    // 0xc3a374: ArrayStore: r1[5] = r0  ; List_4
    //     0xc3a374: add             x25, x1, #0x23
    //     0xc3a378: str             w0, [x25]
    //     0xc3a37c: tbz             w0, #0, #0xc3a398
    //     0xc3a380: ldurb           w16, [x1, #-1]
    //     0xc3a384: ldurb           w17, [x0, #-1]
    //     0xc3a388: and             x16, x17, x16, lsr #2
    //     0xc3a38c: tst             x16, HEAP, lsr #32
    //     0xc3a390: b.eq            #0xc3a398
    //     0xc3a394: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a398: ldur            x0, [fp, #-8]
    // 0xc3a39c: r16 = ", "
    //     0xc3a39c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a3a0: StoreField: r0->field_27 = r16
    //     0xc3a3a0: stur            w16, [x0, #0x27]
    // 0xc3a3a4: ldr             x3, [fp, #0x10]
    // 0xc3a3a8: LoadField: d0 = r3->field_1f
    //     0xc3a3a8: ldur            d0, [x3, #0x1f]
    // 0xc3a3ac: r1 = inline_Allocate_Double()
    //     0xc3a3ac: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a3b0: add             x1, x1, #0x10
    //     0xc3a3b4: cmp             x2, x1
    //     0xc3a3b8: b.ls            #0xc3a8b4
    //     0xc3a3bc: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a3c0: sub             x1, x1, #0xf
    //     0xc3a3c4: movz            x2, #0xe15c
    //     0xc3a3c8: movk            x2, #0x3, lsl #16
    //     0xc3a3cc: stur            x2, [x1, #-1]
    // 0xc3a3d0: StoreField: r1->field_7 = d0
    //     0xc3a3d0: stur            d0, [x1, #7]
    // 0xc3a3d4: r2 = 1
    //     0xc3a3d4: movz            x2, #0x1
    // 0xc3a3d8: r0 = toStringAsFixed()
    //     0xc3a3d8: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a3dc: ldur            x1, [fp, #-8]
    // 0xc3a3e0: ArrayStore: r1[7] = r0  ; List_4
    //     0xc3a3e0: add             x25, x1, #0x2b
    //     0xc3a3e4: str             w0, [x25]
    //     0xc3a3e8: tbz             w0, #0, #0xc3a404
    //     0xc3a3ec: ldurb           w16, [x1, #-1]
    //     0xc3a3f0: ldurb           w17, [x0, #-1]
    //     0xc3a3f4: and             x16, x17, x16, lsr #2
    //     0xc3a3f8: tst             x16, HEAP, lsr #32
    //     0xc3a3fc: b.eq            #0xc3a404
    //     0xc3a400: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a404: ldur            x0, [fp, #-8]
    // 0xc3a408: r16 = ")"
    //     0xc3a408: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3a40c: StoreField: r0->field_2f = r16
    //     0xc3a40c: stur            w16, [x0, #0x2f]
    // 0xc3a410: str             x0, [SP]
    // 0xc3a414: r0 = _interpolate()
    //     0xc3a414: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3a418: LeaveFrame
    //     0xc3a418: mov             SP, fp
    //     0xc3a41c: ldp             fp, lr, [SP], #0x10
    // 0xc3a420: ret
    //     0xc3a420: ret             
    // 0xc3a424: ldr             x3, [fp, #0x10]
    // 0xc3a428: b               #0xc3a430
    // 0xc3a42c: ldr             x3, [fp, #0x10]
    // 0xc3a430: LoadField: d1 = r3->field_7
    //     0xc3a430: ldur            d1, [x3, #7]
    // 0xc3a434: stur            d1, [fp, #-0x10]
    // 0xc3a438: fcmp            d1, d0
    // 0xc3a43c: b.ne            #0xc3a5d4
    // 0xc3a440: ArrayLoad: d2 = r3[0]  ; List_8
    //     0xc3a440: ldur            d2, [x3, #0x17]
    // 0xc3a444: fcmp            d2, d0
    // 0xc3a448: b.ne            #0xc3a5d4
    // 0xc3a44c: r1 = Null
    //     0xc3a44c: mov             x1, NULL
    // 0xc3a450: r2 = 18
    //     0xc3a450: movz            x2, #0x12
    // 0xc3a454: r0 = AllocateArray()
    //     0xc3a454: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3a458: stur            x0, [fp, #-8]
    // 0xc3a45c: r16 = "EdgeInsetsDirectional("
    //     0xc3a45c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33810] "EdgeInsetsDirectional("
    //     0xc3a460: ldr             x16, [x16, #0x810]
    // 0xc3a464: StoreField: r0->field_f = r16
    //     0xc3a464: stur            w16, [x0, #0xf]
    // 0xc3a468: r1 = 0.000000
    //     0xc3a468: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xc3a46c: r2 = 1
    //     0xc3a46c: movz            x2, #0x1
    // 0xc3a470: r0 = toStringAsFixed()
    //     0xc3a470: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a474: ldur            x1, [fp, #-8]
    // 0xc3a478: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3a478: add             x25, x1, #0x13
    //     0xc3a47c: str             w0, [x25]
    //     0xc3a480: tbz             w0, #0, #0xc3a49c
    //     0xc3a484: ldurb           w16, [x1, #-1]
    //     0xc3a488: ldurb           w17, [x0, #-1]
    //     0xc3a48c: and             x16, x17, x16, lsr #2
    //     0xc3a490: tst             x16, HEAP, lsr #32
    //     0xc3a494: b.eq            #0xc3a49c
    //     0xc3a498: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a49c: ldur            x0, [fp, #-8]
    // 0xc3a4a0: r16 = ", "
    //     0xc3a4a0: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a4a4: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3a4a4: stur            w16, [x0, #0x17]
    // 0xc3a4a8: ldr             x3, [fp, #0x10]
    // 0xc3a4ac: LoadField: d0 = r3->field_f
    //     0xc3a4ac: ldur            d0, [x3, #0xf]
    // 0xc3a4b0: r1 = inline_Allocate_Double()
    //     0xc3a4b0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a4b4: add             x1, x1, #0x10
    //     0xc3a4b8: cmp             x2, x1
    //     0xc3a4bc: b.ls            #0xc3a8d0
    //     0xc3a4c0: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a4c4: sub             x1, x1, #0xf
    //     0xc3a4c8: movz            x2, #0xe15c
    //     0xc3a4cc: movk            x2, #0x3, lsl #16
    //     0xc3a4d0: stur            x2, [x1, #-1]
    // 0xc3a4d4: StoreField: r1->field_7 = d0
    //     0xc3a4d4: stur            d0, [x1, #7]
    // 0xc3a4d8: r2 = 1
    //     0xc3a4d8: movz            x2, #0x1
    // 0xc3a4dc: r0 = toStringAsFixed()
    //     0xc3a4dc: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a4e0: ldur            x1, [fp, #-8]
    // 0xc3a4e4: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3a4e4: add             x25, x1, #0x1b
    //     0xc3a4e8: str             w0, [x25]
    //     0xc3a4ec: tbz             w0, #0, #0xc3a508
    //     0xc3a4f0: ldurb           w16, [x1, #-1]
    //     0xc3a4f4: ldurb           w17, [x0, #-1]
    //     0xc3a4f8: and             x16, x17, x16, lsr #2
    //     0xc3a4fc: tst             x16, HEAP, lsr #32
    //     0xc3a500: b.eq            #0xc3a508
    //     0xc3a504: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a508: ldur            x0, [fp, #-8]
    // 0xc3a50c: r16 = ", "
    //     0xc3a50c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a510: StoreField: r0->field_1f = r16
    //     0xc3a510: stur            w16, [x0, #0x1f]
    // 0xc3a514: r1 = 0.000000
    //     0xc3a514: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xc3a518: r2 = 1
    //     0xc3a518: movz            x2, #0x1
    // 0xc3a51c: r0 = toStringAsFixed()
    //     0xc3a51c: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a520: ldur            x1, [fp, #-8]
    // 0xc3a524: ArrayStore: r1[5] = r0  ; List_4
    //     0xc3a524: add             x25, x1, #0x23
    //     0xc3a528: str             w0, [x25]
    //     0xc3a52c: tbz             w0, #0, #0xc3a548
    //     0xc3a530: ldurb           w16, [x1, #-1]
    //     0xc3a534: ldurb           w17, [x0, #-1]
    //     0xc3a538: and             x16, x17, x16, lsr #2
    //     0xc3a53c: tst             x16, HEAP, lsr #32
    //     0xc3a540: b.eq            #0xc3a548
    //     0xc3a544: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a548: ldur            x0, [fp, #-8]
    // 0xc3a54c: r16 = ", "
    //     0xc3a54c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a550: StoreField: r0->field_27 = r16
    //     0xc3a550: stur            w16, [x0, #0x27]
    // 0xc3a554: ldr             x3, [fp, #0x10]
    // 0xc3a558: LoadField: d0 = r3->field_1f
    //     0xc3a558: ldur            d0, [x3, #0x1f]
    // 0xc3a55c: r1 = inline_Allocate_Double()
    //     0xc3a55c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a560: add             x1, x1, #0x10
    //     0xc3a564: cmp             x2, x1
    //     0xc3a568: b.ls            #0xc3a8ec
    //     0xc3a56c: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a570: sub             x1, x1, #0xf
    //     0xc3a574: movz            x2, #0xe15c
    //     0xc3a578: movk            x2, #0x3, lsl #16
    //     0xc3a57c: stur            x2, [x1, #-1]
    // 0xc3a580: StoreField: r1->field_7 = d0
    //     0xc3a580: stur            d0, [x1, #7]
    // 0xc3a584: r2 = 1
    //     0xc3a584: movz            x2, #0x1
    // 0xc3a588: r0 = toStringAsFixed()
    //     0xc3a588: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a58c: ldur            x1, [fp, #-8]
    // 0xc3a590: ArrayStore: r1[7] = r0  ; List_4
    //     0xc3a590: add             x25, x1, #0x2b
    //     0xc3a594: str             w0, [x25]
    //     0xc3a598: tbz             w0, #0, #0xc3a5b4
    //     0xc3a59c: ldurb           w16, [x1, #-1]
    //     0xc3a5a0: ldurb           w17, [x0, #-1]
    //     0xc3a5a4: and             x16, x17, x16, lsr #2
    //     0xc3a5a8: tst             x16, HEAP, lsr #32
    //     0xc3a5ac: b.eq            #0xc3a5b4
    //     0xc3a5b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a5b4: ldur            x0, [fp, #-8]
    // 0xc3a5b8: r16 = ")"
    //     0xc3a5b8: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3a5bc: StoreField: r0->field_2f = r16
    //     0xc3a5bc: stur            w16, [x0, #0x2f]
    // 0xc3a5c0: str             x0, [SP]
    // 0xc3a5c4: r0 = _interpolate()
    //     0xc3a5c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3a5c8: LeaveFrame
    //     0xc3a5c8: mov             SP, fp
    //     0xc3a5cc: ldp             fp, lr, [SP], #0x10
    // 0xc3a5d0: ret
    //     0xc3a5d0: ret             
    // 0xc3a5d4: r1 = Null
    //     0xc3a5d4: mov             x1, NULL
    // 0xc3a5d8: r2 = 26
    //     0xc3a5d8: movz            x2, #0x1a
    // 0xc3a5dc: r0 = AllocateArray()
    //     0xc3a5dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3a5e0: stur            x0, [fp, #-8]
    // 0xc3a5e4: r16 = "EdgeInsets("
    //     0xc3a5e4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33808] "EdgeInsets("
    //     0xc3a5e8: ldr             x16, [x16, #0x808]
    // 0xc3a5ec: StoreField: r0->field_f = r16
    //     0xc3a5ec: stur            w16, [x0, #0xf]
    // 0xc3a5f0: ldur            d0, [fp, #-0x10]
    // 0xc3a5f4: r1 = inline_Allocate_Double()
    //     0xc3a5f4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a5f8: add             x1, x1, #0x10
    //     0xc3a5fc: cmp             x2, x1
    //     0xc3a600: b.ls            #0xc3a908
    //     0xc3a604: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a608: sub             x1, x1, #0xf
    //     0xc3a60c: movz            x2, #0xe15c
    //     0xc3a610: movk            x2, #0x3, lsl #16
    //     0xc3a614: stur            x2, [x1, #-1]
    // 0xc3a618: StoreField: r1->field_7 = d0
    //     0xc3a618: stur            d0, [x1, #7]
    // 0xc3a61c: r2 = 1
    //     0xc3a61c: movz            x2, #0x1
    // 0xc3a620: r0 = toStringAsFixed()
    //     0xc3a620: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a624: ldur            x1, [fp, #-8]
    // 0xc3a628: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3a628: add             x25, x1, #0x13
    //     0xc3a62c: str             w0, [x25]
    //     0xc3a630: tbz             w0, #0, #0xc3a64c
    //     0xc3a634: ldurb           w16, [x1, #-1]
    //     0xc3a638: ldurb           w17, [x0, #-1]
    //     0xc3a63c: and             x16, x17, x16, lsr #2
    //     0xc3a640: tst             x16, HEAP, lsr #32
    //     0xc3a644: b.eq            #0xc3a64c
    //     0xc3a648: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a64c: ldur            x0, [fp, #-8]
    // 0xc3a650: r16 = ", "
    //     0xc3a650: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a654: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3a654: stur            w16, [x0, #0x17]
    // 0xc3a658: ldr             x3, [fp, #0x10]
    // 0xc3a65c: LoadField: d0 = r3->field_f
    //     0xc3a65c: ldur            d0, [x3, #0xf]
    // 0xc3a660: r1 = inline_Allocate_Double()
    //     0xc3a660: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a664: add             x1, x1, #0x10
    //     0xc3a668: cmp             x2, x1
    //     0xc3a66c: b.ls            #0xc3a924
    //     0xc3a670: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a674: sub             x1, x1, #0xf
    //     0xc3a678: movz            x2, #0xe15c
    //     0xc3a67c: movk            x2, #0x3, lsl #16
    //     0xc3a680: stur            x2, [x1, #-1]
    // 0xc3a684: StoreField: r1->field_7 = d0
    //     0xc3a684: stur            d0, [x1, #7]
    // 0xc3a688: r2 = 1
    //     0xc3a688: movz            x2, #0x1
    // 0xc3a68c: r0 = toStringAsFixed()
    //     0xc3a68c: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a690: ldur            x1, [fp, #-8]
    // 0xc3a694: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3a694: add             x25, x1, #0x1b
    //     0xc3a698: str             w0, [x25]
    //     0xc3a69c: tbz             w0, #0, #0xc3a6b8
    //     0xc3a6a0: ldurb           w16, [x1, #-1]
    //     0xc3a6a4: ldurb           w17, [x0, #-1]
    //     0xc3a6a8: and             x16, x17, x16, lsr #2
    //     0xc3a6ac: tst             x16, HEAP, lsr #32
    //     0xc3a6b0: b.eq            #0xc3a6b8
    //     0xc3a6b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a6b8: ldur            x0, [fp, #-8]
    // 0xc3a6bc: r16 = ", "
    //     0xc3a6bc: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a6c0: StoreField: r0->field_1f = r16
    //     0xc3a6c0: stur            w16, [x0, #0x1f]
    // 0xc3a6c4: ldr             x3, [fp, #0x10]
    // 0xc3a6c8: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc3a6c8: ldur            d0, [x3, #0x17]
    // 0xc3a6cc: r1 = inline_Allocate_Double()
    //     0xc3a6cc: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a6d0: add             x1, x1, #0x10
    //     0xc3a6d4: cmp             x2, x1
    //     0xc3a6d8: b.ls            #0xc3a940
    //     0xc3a6dc: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a6e0: sub             x1, x1, #0xf
    //     0xc3a6e4: movz            x2, #0xe15c
    //     0xc3a6e8: movk            x2, #0x3, lsl #16
    //     0xc3a6ec: stur            x2, [x1, #-1]
    // 0xc3a6f0: StoreField: r1->field_7 = d0
    //     0xc3a6f0: stur            d0, [x1, #7]
    // 0xc3a6f4: r2 = 1
    //     0xc3a6f4: movz            x2, #0x1
    // 0xc3a6f8: r0 = toStringAsFixed()
    //     0xc3a6f8: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a6fc: ldur            x1, [fp, #-8]
    // 0xc3a700: ArrayStore: r1[5] = r0  ; List_4
    //     0xc3a700: add             x25, x1, #0x23
    //     0xc3a704: str             w0, [x25]
    //     0xc3a708: tbz             w0, #0, #0xc3a724
    //     0xc3a70c: ldurb           w16, [x1, #-1]
    //     0xc3a710: ldurb           w17, [x0, #-1]
    //     0xc3a714: and             x16, x17, x16, lsr #2
    //     0xc3a718: tst             x16, HEAP, lsr #32
    //     0xc3a71c: b.eq            #0xc3a724
    //     0xc3a720: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a724: ldur            x0, [fp, #-8]
    // 0xc3a728: r16 = ", "
    //     0xc3a728: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a72c: StoreField: r0->field_27 = r16
    //     0xc3a72c: stur            w16, [x0, #0x27]
    // 0xc3a730: ldr             x1, [fp, #0x10]
    // 0xc3a734: LoadField: d0 = r1->field_1f
    //     0xc3a734: ldur            d0, [x1, #0x1f]
    // 0xc3a738: r1 = inline_Allocate_Double()
    //     0xc3a738: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc3a73c: add             x1, x1, #0x10
    //     0xc3a740: cmp             x2, x1
    //     0xc3a744: b.ls            #0xc3a95c
    //     0xc3a748: str             x1, [THR, #0x50]  ; THR::top
    //     0xc3a74c: sub             x1, x1, #0xf
    //     0xc3a750: movz            x2, #0xe15c
    //     0xc3a754: movk            x2, #0x3, lsl #16
    //     0xc3a758: stur            x2, [x1, #-1]
    // 0xc3a75c: StoreField: r1->field_7 = d0
    //     0xc3a75c: stur            d0, [x1, #7]
    // 0xc3a760: r2 = 1
    //     0xc3a760: movz            x2, #0x1
    // 0xc3a764: r0 = toStringAsFixed()
    //     0xc3a764: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a768: ldur            x1, [fp, #-8]
    // 0xc3a76c: ArrayStore: r1[7] = r0  ; List_4
    //     0xc3a76c: add             x25, x1, #0x2b
    //     0xc3a770: str             w0, [x25]
    //     0xc3a774: tbz             w0, #0, #0xc3a790
    //     0xc3a778: ldurb           w16, [x1, #-1]
    //     0xc3a77c: ldurb           w17, [x0, #-1]
    //     0xc3a780: and             x16, x17, x16, lsr #2
    //     0xc3a784: tst             x16, HEAP, lsr #32
    //     0xc3a788: b.eq            #0xc3a790
    //     0xc3a78c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a790: ldur            x0, [fp, #-8]
    // 0xc3a794: r16 = ") + EdgeInsetsDirectional("
    //     0xc3a794: add             x16, PP, #0x33, lsl #12  ; [pp+0x33818] ") + EdgeInsetsDirectional("
    //     0xc3a798: ldr             x16, [x16, #0x818]
    // 0xc3a79c: StoreField: r0->field_2f = r16
    //     0xc3a79c: stur            w16, [x0, #0x2f]
    // 0xc3a7a0: r1 = 0.000000
    //     0xc3a7a0: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xc3a7a4: r2 = 1
    //     0xc3a7a4: movz            x2, #0x1
    // 0xc3a7a8: r0 = toStringAsFixed()
    //     0xc3a7a8: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a7ac: ldur            x1, [fp, #-8]
    // 0xc3a7b0: ArrayStore: r1[9] = r0  ; List_4
    //     0xc3a7b0: add             x25, x1, #0x33
    //     0xc3a7b4: str             w0, [x25]
    //     0xc3a7b8: tbz             w0, #0, #0xc3a7d4
    //     0xc3a7bc: ldurb           w16, [x1, #-1]
    //     0xc3a7c0: ldurb           w17, [x0, #-1]
    //     0xc3a7c4: and             x16, x17, x16, lsr #2
    //     0xc3a7c8: tst             x16, HEAP, lsr #32
    //     0xc3a7cc: b.eq            #0xc3a7d4
    //     0xc3a7d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a7d4: ldur            x0, [fp, #-8]
    // 0xc3a7d8: r16 = ", 0.0, "
    //     0xc3a7d8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33820] ", 0.0, "
    //     0xc3a7dc: ldr             x16, [x16, #0x820]
    // 0xc3a7e0: StoreField: r0->field_37 = r16
    //     0xc3a7e0: stur            w16, [x0, #0x37]
    // 0xc3a7e4: r1 = 0.000000
    //     0xc3a7e4: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xc3a7e8: r2 = 1
    //     0xc3a7e8: movz            x2, #0x1
    // 0xc3a7ec: r0 = toStringAsFixed()
    //     0xc3a7ec: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc3a7f0: ldur            x1, [fp, #-8]
    // 0xc3a7f4: ArrayStore: r1[11] = r0  ; List_4
    //     0xc3a7f4: add             x25, x1, #0x3b
    //     0xc3a7f8: str             w0, [x25]
    //     0xc3a7fc: tbz             w0, #0, #0xc3a818
    //     0xc3a800: ldurb           w16, [x1, #-1]
    //     0xc3a804: ldurb           w17, [x0, #-1]
    //     0xc3a808: and             x16, x17, x16, lsr #2
    //     0xc3a80c: tst             x16, HEAP, lsr #32
    //     0xc3a810: b.eq            #0xc3a818
    //     0xc3a814: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a818: ldur            x0, [fp, #-8]
    // 0xc3a81c: r16 = ", 0.0)"
    //     0xc3a81c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33828] ", 0.0)"
    //     0xc3a820: ldr             x16, [x16, #0x828]
    // 0xc3a824: StoreField: r0->field_3f = r16
    //     0xc3a824: stur            w16, [x0, #0x3f]
    // 0xc3a828: str             x0, [SP]
    // 0xc3a82c: r0 = _interpolate()
    //     0xc3a82c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3a830: LeaveFrame
    //     0xc3a830: mov             SP, fp
    //     0xc3a834: ldp             fp, lr, [SP], #0x10
    // 0xc3a838: ret
    //     0xc3a838: ret             
    // 0xc3a83c: r0 = StackOverflowSharedWithFPURegs()
    //     0xc3a83c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc3a840: b               #0xc3a130
    // 0xc3a844: SaveReg d1
    //     0xc3a844: str             q1, [SP, #-0x10]!
    // 0xc3a848: SaveReg r0
    //     0xc3a848: str             x0, [SP, #-8]!
    // 0xc3a84c: r0 = AllocateDouble()
    //     0xc3a84c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a850: mov             x1, x0
    // 0xc3a854: RestoreReg r0
    //     0xc3a854: ldr             x0, [SP], #8
    // 0xc3a858: RestoreReg d1
    //     0xc3a858: ldr             q1, [SP], #0x10
    // 0xc3a85c: b               #0xc3a1f8
    // 0xc3a860: SaveReg d0
    //     0xc3a860: str             q0, [SP, #-0x10]!
    // 0xc3a864: SaveReg r0
    //     0xc3a864: str             x0, [SP, #-8]!
    // 0xc3a868: r0 = AllocateDouble()
    //     0xc3a868: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a86c: mov             x1, x0
    // 0xc3a870: RestoreReg r0
    //     0xc3a870: ldr             x0, [SP], #8
    // 0xc3a874: RestoreReg d0
    //     0xc3a874: ldr             q0, [SP], #0x10
    // 0xc3a878: b               #0xc3a290
    // 0xc3a87c: SaveReg d0
    //     0xc3a87c: str             q0, [SP, #-0x10]!
    // 0xc3a880: stp             x0, x3, [SP, #-0x10]!
    // 0xc3a884: r0 = AllocateDouble()
    //     0xc3a884: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a888: mov             x1, x0
    // 0xc3a88c: ldp             x0, x3, [SP], #0x10
    // 0xc3a890: RestoreReg d0
    //     0xc3a890: ldr             q0, [SP], #0x10
    // 0xc3a894: b               #0xc3a2fc
    // 0xc3a898: SaveReg d0
    //     0xc3a898: str             q0, [SP, #-0x10]!
    // 0xc3a89c: SaveReg r0
    //     0xc3a89c: str             x0, [SP, #-8]!
    // 0xc3a8a0: r0 = AllocateDouble()
    //     0xc3a8a0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a8a4: mov             x1, x0
    // 0xc3a8a8: RestoreReg r0
    //     0xc3a8a8: ldr             x0, [SP], #8
    // 0xc3a8ac: RestoreReg d0
    //     0xc3a8ac: ldr             q0, [SP], #0x10
    // 0xc3a8b0: b               #0xc3a364
    // 0xc3a8b4: SaveReg d0
    //     0xc3a8b4: str             q0, [SP, #-0x10]!
    // 0xc3a8b8: SaveReg r0
    //     0xc3a8b8: str             x0, [SP, #-8]!
    // 0xc3a8bc: r0 = AllocateDouble()
    //     0xc3a8bc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a8c0: mov             x1, x0
    // 0xc3a8c4: RestoreReg r0
    //     0xc3a8c4: ldr             x0, [SP], #8
    // 0xc3a8c8: RestoreReg d0
    //     0xc3a8c8: ldr             q0, [SP], #0x10
    // 0xc3a8cc: b               #0xc3a3d0
    // 0xc3a8d0: SaveReg d0
    //     0xc3a8d0: str             q0, [SP, #-0x10]!
    // 0xc3a8d4: stp             x0, x3, [SP, #-0x10]!
    // 0xc3a8d8: r0 = AllocateDouble()
    //     0xc3a8d8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a8dc: mov             x1, x0
    // 0xc3a8e0: ldp             x0, x3, [SP], #0x10
    // 0xc3a8e4: RestoreReg d0
    //     0xc3a8e4: ldr             q0, [SP], #0x10
    // 0xc3a8e8: b               #0xc3a4d4
    // 0xc3a8ec: SaveReg d0
    //     0xc3a8ec: str             q0, [SP, #-0x10]!
    // 0xc3a8f0: SaveReg r0
    //     0xc3a8f0: str             x0, [SP, #-8]!
    // 0xc3a8f4: r0 = AllocateDouble()
    //     0xc3a8f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a8f8: mov             x1, x0
    // 0xc3a8fc: RestoreReg r0
    //     0xc3a8fc: ldr             x0, [SP], #8
    // 0xc3a900: RestoreReg d0
    //     0xc3a900: ldr             q0, [SP], #0x10
    // 0xc3a904: b               #0xc3a580
    // 0xc3a908: SaveReg d0
    //     0xc3a908: str             q0, [SP, #-0x10]!
    // 0xc3a90c: SaveReg r0
    //     0xc3a90c: str             x0, [SP, #-8]!
    // 0xc3a910: r0 = AllocateDouble()
    //     0xc3a910: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a914: mov             x1, x0
    // 0xc3a918: RestoreReg r0
    //     0xc3a918: ldr             x0, [SP], #8
    // 0xc3a91c: RestoreReg d0
    //     0xc3a91c: ldr             q0, [SP], #0x10
    // 0xc3a920: b               #0xc3a618
    // 0xc3a924: SaveReg d0
    //     0xc3a924: str             q0, [SP, #-0x10]!
    // 0xc3a928: stp             x0, x3, [SP, #-0x10]!
    // 0xc3a92c: r0 = AllocateDouble()
    //     0xc3a92c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a930: mov             x1, x0
    // 0xc3a934: ldp             x0, x3, [SP], #0x10
    // 0xc3a938: RestoreReg d0
    //     0xc3a938: ldr             q0, [SP], #0x10
    // 0xc3a93c: b               #0xc3a684
    // 0xc3a940: SaveReg d0
    //     0xc3a940: str             q0, [SP, #-0x10]!
    // 0xc3a944: stp             x0, x3, [SP, #-0x10]!
    // 0xc3a948: r0 = AllocateDouble()
    //     0xc3a948: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a94c: mov             x1, x0
    // 0xc3a950: ldp             x0, x3, [SP], #0x10
    // 0xc3a954: RestoreReg d0
    //     0xc3a954: ldr             q0, [SP], #0x10
    // 0xc3a958: b               #0xc3a6f0
    // 0xc3a95c: SaveReg d0
    //     0xc3a95c: str             q0, [SP, #-0x10]!
    // 0xc3a960: SaveReg r0
    //     0xc3a960: str             x0, [SP, #-8]!
    // 0xc3a964: r0 = AllocateDouble()
    //     0xc3a964: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a968: mov             x1, x0
    // 0xc3a96c: RestoreReg r0
    //     0xc3a96c: ldr             x0, [SP], #8
    // 0xc3a970: RestoreReg d0
    //     0xc3a970: ldr             q0, [SP], #0x10
    // 0xc3a974: b               #0xc3a75c
  }
  get _ vertical(/* No info */) {
    // ** addr: 0xe89a70, size: 0x10
    // 0xe89a70: LoadField: d1 = r1->field_f
    //     0xe89a70: ldur            d1, [x1, #0xf]
    // 0xe89a74: LoadField: d2 = r1->field_1f
    //     0xe89a74: ldur            d2, [x1, #0x1f]
    // 0xe89a78: fadd            d0, d1, d2
    // 0xe89a7c: ret
    //     0xe89a7c: ret             
  }
  get _ horizontal(/* No info */) {
    // ** addr: 0xe89a80, size: 0x1c
    // 0xe89a80: d1 = 0.000000
    //     0xe89a80: eor             v1.16b, v1.16b, v1.16b
    // 0xe89a84: LoadField: d2 = r1->field_7
    //     0xe89a84: ldur            d2, [x1, #7]
    // 0xe89a88: ArrayLoad: d3 = r1[0]  ; List_8
    //     0xe89a88: ldur            d3, [x1, #0x17]
    // 0xe89a8c: fadd            d4, d2, d3
    // 0xe89a90: fadd            d2, d4, d1
    // 0xe89a94: fadd            d0, d2, d1
    // 0xe89a98: ret
    //     0xe89a98: ret             
  }
}

// class id: 816, size: 0x28, field offset: 0x8
//   const constructor, 
class EdgeInsets extends EdgeInsetsGeometry {

  _Mint field_8;
  _Double field_10;
  _Mint field_18;
  _Mint field_20;

  EdgeInsets +(EdgeInsets, EdgeInsets) {
    // ** addr: 0xb0f64c, size: 0x64
    // 0xb0f64c: EnterFrame
    //     0xb0f64c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0f650: mov             fp, SP
    // 0xb0f654: ldr             x0, [fp, #0x10]
    // 0xb0f658: r2 = Null
    //     0xb0f658: mov             x2, NULL
    // 0xb0f65c: r1 = Null
    //     0xb0f65c: mov             x1, NULL
    // 0xb0f660: r4 = 60
    //     0xb0f660: movz            x4, #0x3c
    // 0xb0f664: branchIfSmi(r0, 0xb0f670)
    //     0xb0f664: tbz             w0, #0, #0xb0f670
    // 0xb0f668: r4 = LoadClassIdInstr(r0)
    //     0xb0f668: ldur            x4, [x0, #-1]
    //     0xb0f66c: ubfx            x4, x4, #0xc, #0x14
    // 0xb0f670: cmp             x4, #0x330
    // 0xb0f674: b.eq            #0xb0f68c
    // 0xb0f678: r8 = EdgeInsets
    //     0xb0f678: add             x8, PP, #0x33, lsl #12  ; [pp+0x33790] Type: EdgeInsets
    //     0xb0f67c: ldr             x8, [x8, #0x790]
    // 0xb0f680: r3 = Null
    //     0xb0f680: add             x3, PP, #0x33, lsl #12  ; [pp+0x33798] Null
    //     0xb0f684: ldr             x3, [x3, #0x798]
    // 0xb0f688: r0 = DefaultTypeTest()
    //     0xb0f688: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xb0f68c: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xb0f68c: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xb0f690: r0 = Throw()
    //     0xb0f690: bl              #0xec04b8  ; ThrowStub
    // 0xb0f694: brk             #0
  }
}

// class id: 817, size: 0x28, field offset: 0x8
//   const constructor, 
class BoxConstraints extends Object {

  _Mint field_8;
  _Double field_10;
  _Mint field_18;
  _Double field_20;

  _ toString(/* No info */) {
    // ** addr: 0xc39eec, size: 0x228
    // 0xc39eec: EnterFrame
    //     0xc39eec: stp             fp, lr, [SP, #-0x10]!
    //     0xc39ef0: mov             fp, SP
    // 0xc39ef4: AllocStack(0x8)
    //     0xc39ef4: sub             SP, SP, #8
    // 0xc39ef8: CheckStackOverflow
    //     0xc39ef8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc39efc: cmp             SP, x16
    //     0xc39f00: b.ls            #0xc3a0ac
    // 0xc39f04: r1 = Null
    //     0xc39f04: mov             x1, NULL
    // 0xc39f08: r2 = 18
    //     0xc39f08: movz            x2, #0x12
    // 0xc39f0c: r0 = AllocateArray()
    //     0xc39f0c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc39f10: mov             x2, x0
    // 0xc39f14: r16 = "BoxConstraint <"
    //     0xc39f14: add             x16, PP, #0x33, lsl #12  ; [pp+0x33780] "BoxConstraint <"
    //     0xc39f18: ldr             x16, [x16, #0x780]
    // 0xc39f1c: StoreField: r2->field_f = r16
    //     0xc39f1c: stur            w16, [x2, #0xf]
    // 0xc39f20: ldr             x3, [fp, #0x10]
    // 0xc39f24: LoadField: d0 = r3->field_7
    //     0xc39f24: ldur            d0, [x3, #7]
    // 0xc39f28: r0 = inline_Allocate_Double()
    //     0xc39f28: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc39f2c: add             x0, x0, #0x10
    //     0xc39f30: cmp             x1, x0
    //     0xc39f34: b.ls            #0xc3a0b4
    //     0xc39f38: str             x0, [THR, #0x50]  ; THR::top
    //     0xc39f3c: sub             x0, x0, #0xf
    //     0xc39f40: movz            x1, #0xe15c
    //     0xc39f44: movk            x1, #0x3, lsl #16
    //     0xc39f48: stur            x1, [x0, #-1]
    // 0xc39f4c: StoreField: r0->field_7 = d0
    //     0xc39f4c: stur            d0, [x0, #7]
    // 0xc39f50: mov             x1, x2
    // 0xc39f54: ArrayStore: r1[1] = r0  ; List_4
    //     0xc39f54: add             x25, x1, #0x13
    //     0xc39f58: str             w0, [x25]
    //     0xc39f5c: tbz             w0, #0, #0xc39f78
    //     0xc39f60: ldurb           w16, [x1, #-1]
    //     0xc39f64: ldurb           w17, [x0, #-1]
    //     0xc39f68: and             x16, x17, x16, lsr #2
    //     0xc39f6c: tst             x16, HEAP, lsr #32
    //     0xc39f70: b.eq            #0xc39f78
    //     0xc39f74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc39f78: r16 = ", "
    //     0xc39f78: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc39f7c: ArrayStore: r2[0] = r16  ; List_4
    //     0xc39f7c: stur            w16, [x2, #0x17]
    // 0xc39f80: LoadField: d0 = r3->field_f
    //     0xc39f80: ldur            d0, [x3, #0xf]
    // 0xc39f84: r0 = inline_Allocate_Double()
    //     0xc39f84: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc39f88: add             x0, x0, #0x10
    //     0xc39f8c: cmp             x1, x0
    //     0xc39f90: b.ls            #0xc3a0cc
    //     0xc39f94: str             x0, [THR, #0x50]  ; THR::top
    //     0xc39f98: sub             x0, x0, #0xf
    //     0xc39f9c: movz            x1, #0xe15c
    //     0xc39fa0: movk            x1, #0x3, lsl #16
    //     0xc39fa4: stur            x1, [x0, #-1]
    // 0xc39fa8: StoreField: r0->field_7 = d0
    //     0xc39fa8: stur            d0, [x0, #7]
    // 0xc39fac: mov             x1, x2
    // 0xc39fb0: ArrayStore: r1[3] = r0  ; List_4
    //     0xc39fb0: add             x25, x1, #0x1b
    //     0xc39fb4: str             w0, [x25]
    //     0xc39fb8: tbz             w0, #0, #0xc39fd4
    //     0xc39fbc: ldurb           w16, [x1, #-1]
    //     0xc39fc0: ldurb           w17, [x0, #-1]
    //     0xc39fc4: and             x16, x17, x16, lsr #2
    //     0xc39fc8: tst             x16, HEAP, lsr #32
    //     0xc39fcc: b.eq            #0xc39fd4
    //     0xc39fd0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc39fd4: r16 = "> <"
    //     0xc39fd4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33788] "> <"
    //     0xc39fd8: ldr             x16, [x16, #0x788]
    // 0xc39fdc: StoreField: r2->field_1f = r16
    //     0xc39fdc: stur            w16, [x2, #0x1f]
    // 0xc39fe0: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc39fe0: ldur            d0, [x3, #0x17]
    // 0xc39fe4: r0 = inline_Allocate_Double()
    //     0xc39fe4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc39fe8: add             x0, x0, #0x10
    //     0xc39fec: cmp             x1, x0
    //     0xc39ff0: b.ls            #0xc3a0e4
    //     0xc39ff4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc39ff8: sub             x0, x0, #0xf
    //     0xc39ffc: movz            x1, #0xe15c
    //     0xc3a000: movk            x1, #0x3, lsl #16
    //     0xc3a004: stur            x1, [x0, #-1]
    // 0xc3a008: StoreField: r0->field_7 = d0
    //     0xc3a008: stur            d0, [x0, #7]
    // 0xc3a00c: mov             x1, x2
    // 0xc3a010: ArrayStore: r1[5] = r0  ; List_4
    //     0xc3a010: add             x25, x1, #0x23
    //     0xc3a014: str             w0, [x25]
    //     0xc3a018: tbz             w0, #0, #0xc3a034
    //     0xc3a01c: ldurb           w16, [x1, #-1]
    //     0xc3a020: ldurb           w17, [x0, #-1]
    //     0xc3a024: and             x16, x17, x16, lsr #2
    //     0xc3a028: tst             x16, HEAP, lsr #32
    //     0xc3a02c: b.eq            #0xc3a034
    //     0xc3a030: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a034: r16 = ", "
    //     0xc3a034: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3a038: StoreField: r2->field_27 = r16
    //     0xc3a038: stur            w16, [x2, #0x27]
    // 0xc3a03c: LoadField: d0 = r3->field_1f
    //     0xc3a03c: ldur            d0, [x3, #0x1f]
    // 0xc3a040: r0 = inline_Allocate_Double()
    //     0xc3a040: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc3a044: add             x0, x0, #0x10
    //     0xc3a048: cmp             x1, x0
    //     0xc3a04c: b.ls            #0xc3a0fc
    //     0xc3a050: str             x0, [THR, #0x50]  ; THR::top
    //     0xc3a054: sub             x0, x0, #0xf
    //     0xc3a058: movz            x1, #0xe15c
    //     0xc3a05c: movk            x1, #0x3, lsl #16
    //     0xc3a060: stur            x1, [x0, #-1]
    // 0xc3a064: StoreField: r0->field_7 = d0
    //     0xc3a064: stur            d0, [x0, #7]
    // 0xc3a068: mov             x1, x2
    // 0xc3a06c: ArrayStore: r1[7] = r0  ; List_4
    //     0xc3a06c: add             x25, x1, #0x2b
    //     0xc3a070: str             w0, [x25]
    //     0xc3a074: tbz             w0, #0, #0xc3a090
    //     0xc3a078: ldurb           w16, [x1, #-1]
    //     0xc3a07c: ldurb           w17, [x0, #-1]
    //     0xc3a080: and             x16, x17, x16, lsr #2
    //     0xc3a084: tst             x16, HEAP, lsr #32
    //     0xc3a088: b.eq            #0xc3a090
    //     0xc3a08c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3a090: r16 = ">"
    //     0xc3a090: ldr             x16, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xc3a094: StoreField: r2->field_2f = r16
    //     0xc3a094: stur            w16, [x2, #0x2f]
    // 0xc3a098: str             x2, [SP]
    // 0xc3a09c: r0 = _interpolate()
    //     0xc3a09c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3a0a0: LeaveFrame
    //     0xc3a0a0: mov             SP, fp
    //     0xc3a0a4: ldp             fp, lr, [SP], #0x10
    // 0xc3a0a8: ret
    //     0xc3a0a8: ret             
    // 0xc3a0ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3a0ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3a0b0: b               #0xc39f04
    // 0xc3a0b4: SaveReg d0
    //     0xc3a0b4: str             q0, [SP, #-0x10]!
    // 0xc3a0b8: stp             x2, x3, [SP, #-0x10]!
    // 0xc3a0bc: r0 = AllocateDouble()
    //     0xc3a0bc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a0c0: ldp             x2, x3, [SP], #0x10
    // 0xc3a0c4: RestoreReg d0
    //     0xc3a0c4: ldr             q0, [SP], #0x10
    // 0xc3a0c8: b               #0xc39f4c
    // 0xc3a0cc: SaveReg d0
    //     0xc3a0cc: str             q0, [SP, #-0x10]!
    // 0xc3a0d0: stp             x2, x3, [SP, #-0x10]!
    // 0xc3a0d4: r0 = AllocateDouble()
    //     0xc3a0d4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a0d8: ldp             x2, x3, [SP], #0x10
    // 0xc3a0dc: RestoreReg d0
    //     0xc3a0dc: ldr             q0, [SP], #0x10
    // 0xc3a0e0: b               #0xc39fa8
    // 0xc3a0e4: SaveReg d0
    //     0xc3a0e4: str             q0, [SP, #-0x10]!
    // 0xc3a0e8: stp             x2, x3, [SP, #-0x10]!
    // 0xc3a0ec: r0 = AllocateDouble()
    //     0xc3a0ec: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a0f0: ldp             x2, x3, [SP], #0x10
    // 0xc3a0f4: RestoreReg d0
    //     0xc3a0f4: ldr             q0, [SP], #0x10
    // 0xc3a0f8: b               #0xc3a008
    // 0xc3a0fc: SaveReg d0
    //     0xc3a0fc: str             q0, [SP, #-0x10]!
    // 0xc3a100: SaveReg r2
    //     0xc3a100: str             x2, [SP, #-8]!
    // 0xc3a104: r0 = AllocateDouble()
    //     0xc3a104: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3a108: RestoreReg r2
    //     0xc3a108: ldr             x2, [SP], #8
    // 0xc3a10c: RestoreReg d0
    //     0xc3a10c: ldr             q0, [SP], #0x10
    // 0xc3a110: b               #0xc3a064
  }
  _ constrainRect(/* No info */) {
    // ** addr: 0xe8e560, size: 0x10c
    // 0xe8e560: EnterFrame
    //     0xe8e560: stp             fp, lr, [SP, #-0x10]!
    //     0xe8e564: mov             fp, SP
    // 0xe8e568: AllocStack(0x20)
    //     0xe8e568: sub             SP, SP, #0x20
    // 0xe8e56c: SetupParameters(BoxConstraints this /* r1 => r0, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x10 */)
    //     0xe8e56c: mov             x0, x1
    //     0xe8e570: stur            x1, [fp, #-8]
    //     0xe8e574: stur            d0, [fp, #-0x10]
    // 0xe8e578: CheckStackOverflow
    //     0xe8e578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8e57c: cmp             SP, x16
    //     0xe8e580: b.ls            #0xe8e638
    // 0xe8e584: r1 = inline_Allocate_Double()
    //     0xe8e584: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe8e588: add             x1, x1, #0x10
    //     0xe8e58c: cmp             x2, x1
    //     0xe8e590: b.ls            #0xe8e640
    //     0xe8e594: str             x1, [THR, #0x50]  ; THR::top
    //     0xe8e598: sub             x1, x1, #0xf
    //     0xe8e59c: movz            x2, #0xe15c
    //     0xe8e5a0: movk            x2, #0x3, lsl #16
    //     0xe8e5a4: stur            x2, [x1, #-1]
    // 0xe8e5a8: StoreField: r1->field_7 = d1
    //     0xe8e5a8: stur            d1, [x1, #7]
    // 0xe8e5ac: str             x1, [SP]
    // 0xe8e5b0: mov             x1, x0
    // 0xe8e5b4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe8e5b4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe8e5b8: r0 = constrainWidth()
    //     0xe8e5b8: bl              #0xe8e794  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainWidth
    // 0xe8e5bc: mov             v1.16b, v0.16b
    // 0xe8e5c0: ldur            d0, [fp, #-0x10]
    // 0xe8e5c4: stur            d1, [fp, #-0x18]
    // 0xe8e5c8: r0 = inline_Allocate_Double()
    //     0xe8e5c8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe8e5cc: add             x0, x0, #0x10
    //     0xe8e5d0: cmp             x1, x0
    //     0xe8e5d4: b.ls            #0xe8e65c
    //     0xe8e5d8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8e5dc: sub             x0, x0, #0xf
    //     0xe8e5e0: movz            x1, #0xe15c
    //     0xe8e5e4: movk            x1, #0x3, lsl #16
    //     0xe8e5e8: stur            x1, [x0, #-1]
    // 0xe8e5ec: StoreField: r0->field_7 = d0
    //     0xe8e5ec: stur            d0, [x0, #7]
    // 0xe8e5f0: str             x0, [SP]
    // 0xe8e5f4: ldur            x1, [fp, #-8]
    // 0xe8e5f8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe8e5f8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe8e5fc: r0 = constrainHeight()
    //     0xe8e5fc: bl              #0xe8e66c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainHeight
    // 0xe8e600: stur            d0, [fp, #-0x10]
    // 0xe8e604: r0 = PdfPoint()
    //     0xe8e604: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe8e608: ldur            d0, [fp, #-0x18]
    // 0xe8e60c: StoreField: r0->field_7 = d0
    //     0xe8e60c: stur            d0, [x0, #7]
    // 0xe8e610: ldur            d0, [fp, #-0x10]
    // 0xe8e614: StoreField: r0->field_f = d0
    //     0xe8e614: stur            d0, [x0, #0xf]
    // 0xe8e618: mov             x3, x0
    // 0xe8e61c: r1 = Null
    //     0xe8e61c: mov             x1, NULL
    // 0xe8e620: r2 = Instance_PdfPoint
    //     0xe8e620: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xe8e624: ldr             x2, [x2, #0x730]
    // 0xe8e628: r0 = PdfRect.fromPoints()
    //     0xe8e628: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xe8e62c: LeaveFrame
    //     0xe8e62c: mov             SP, fp
    //     0xe8e630: ldp             fp, lr, [SP], #0x10
    // 0xe8e634: ret
    //     0xe8e634: ret             
    // 0xe8e638: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8e638: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8e63c: b               #0xe8e584
    // 0xe8e640: stp             q0, q1, [SP, #-0x20]!
    // 0xe8e644: SaveReg r0
    //     0xe8e644: str             x0, [SP, #-8]!
    // 0xe8e648: r0 = AllocateDouble()
    //     0xe8e648: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e64c: mov             x1, x0
    // 0xe8e650: RestoreReg r0
    //     0xe8e650: ldr             x0, [SP], #8
    // 0xe8e654: ldp             q0, q1, [SP], #0x20
    // 0xe8e658: b               #0xe8e5a8
    // 0xe8e65c: stp             q0, q1, [SP, #-0x20]!
    // 0xe8e660: r0 = AllocateDouble()
    //     0xe8e660: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e664: ldp             q0, q1, [SP], #0x20
    // 0xe8e668: b               #0xe8e5ec
  }
  _ constrainHeight(/* No info */) {
    // ** addr: 0xe8e66c, size: 0x128
    // 0xe8e66c: EnterFrame
    //     0xe8e66c: stp             fp, lr, [SP, #-0x10]!
    //     0xe8e670: mov             fp, SP
    // 0xe8e674: LoadField: r0 = r4->field_13
    //     0xe8e674: ldur            w0, [x4, #0x13]
    // 0xe8e678: sub             x2, x0, #2
    // 0xe8e67c: cmp             w2, #2
    // 0xe8e680: b.lt            #0xe8e694
    // 0xe8e684: add             x0, fp, w2, sxtw #2
    // 0xe8e688: ldr             x0, [x0, #8]
    // 0xe8e68c: LoadField: d0 = r0->field_7
    //     0xe8e68c: ldur            d0, [x0, #7]
    // 0xe8e690: b               #0xe8e698
    // 0xe8e694: d0 = inf
    //     0xe8e694: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8e698: CheckStackOverflow
    //     0xe8e698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8e69c: cmp             SP, x16
    //     0xe8e6a0: b.ls            #0xe8e738
    // 0xe8e6a4: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xe8e6a4: ldur            d1, [x1, #0x17]
    // 0xe8e6a8: LoadField: d2 = r1->field_1f
    //     0xe8e6a8: ldur            d2, [x1, #0x1f]
    // 0xe8e6ac: r1 = inline_Allocate_Double()
    //     0xe8e6ac: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe8e6b0: add             x1, x1, #0x10
    //     0xe8e6b4: cmp             x0, x1
    //     0xe8e6b8: b.ls            #0xe8e740
    //     0xe8e6bc: str             x1, [THR, #0x50]  ; THR::top
    //     0xe8e6c0: sub             x1, x1, #0xf
    //     0xe8e6c4: movz            x0, #0xe15c
    //     0xe8e6c8: movk            x0, #0x3, lsl #16
    //     0xe8e6cc: stur            x0, [x1, #-1]
    // 0xe8e6d0: StoreField: r1->field_7 = d0
    //     0xe8e6d0: stur            d0, [x1, #7]
    // 0xe8e6d4: r2 = inline_Allocate_Double()
    //     0xe8e6d4: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xe8e6d8: add             x2, x2, #0x10
    //     0xe8e6dc: cmp             x0, x2
    //     0xe8e6e0: b.ls            #0xe8e75c
    //     0xe8e6e4: str             x2, [THR, #0x50]  ; THR::top
    //     0xe8e6e8: sub             x2, x2, #0xf
    //     0xe8e6ec: movz            x0, #0xe15c
    //     0xe8e6f0: movk            x0, #0x3, lsl #16
    //     0xe8e6f4: stur            x0, [x2, #-1]
    // 0xe8e6f8: StoreField: r2->field_7 = d1
    //     0xe8e6f8: stur            d1, [x2, #7]
    // 0xe8e6fc: r3 = inline_Allocate_Double()
    //     0xe8e6fc: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xe8e700: add             x3, x3, #0x10
    //     0xe8e704: cmp             x0, x3
    //     0xe8e708: b.ls            #0xe8e778
    //     0xe8e70c: str             x3, [THR, #0x50]  ; THR::top
    //     0xe8e710: sub             x3, x3, #0xf
    //     0xe8e714: movz            x0, #0xe15c
    //     0xe8e718: movk            x0, #0x3, lsl #16
    //     0xe8e71c: stur            x0, [x3, #-1]
    // 0xe8e720: StoreField: r3->field_7 = d2
    //     0xe8e720: stur            d2, [x3, #7]
    // 0xe8e724: r0 = clamp()
    //     0xe8e724: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xe8e728: LoadField: d0 = r0->field_7
    //     0xe8e728: ldur            d0, [x0, #7]
    // 0xe8e72c: LeaveFrame
    //     0xe8e72c: mov             SP, fp
    //     0xe8e730: ldp             fp, lr, [SP], #0x10
    // 0xe8e734: ret
    //     0xe8e734: ret             
    // 0xe8e738: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8e738: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8e73c: b               #0xe8e6a4
    // 0xe8e740: stp             q1, q2, [SP, #-0x20]!
    // 0xe8e744: SaveReg d0
    //     0xe8e744: str             q0, [SP, #-0x10]!
    // 0xe8e748: r0 = AllocateDouble()
    //     0xe8e748: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e74c: mov             x1, x0
    // 0xe8e750: RestoreReg d0
    //     0xe8e750: ldr             q0, [SP], #0x10
    // 0xe8e754: ldp             q1, q2, [SP], #0x20
    // 0xe8e758: b               #0xe8e6d0
    // 0xe8e75c: stp             q1, q2, [SP, #-0x20]!
    // 0xe8e760: SaveReg r1
    //     0xe8e760: str             x1, [SP, #-8]!
    // 0xe8e764: r0 = AllocateDouble()
    //     0xe8e764: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e768: mov             x2, x0
    // 0xe8e76c: RestoreReg r1
    //     0xe8e76c: ldr             x1, [SP], #8
    // 0xe8e770: ldp             q1, q2, [SP], #0x20
    // 0xe8e774: b               #0xe8e6f8
    // 0xe8e778: SaveReg d2
    //     0xe8e778: str             q2, [SP, #-0x10]!
    // 0xe8e77c: stp             x1, x2, [SP, #-0x10]!
    // 0xe8e780: r0 = AllocateDouble()
    //     0xe8e780: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e784: mov             x3, x0
    // 0xe8e788: ldp             x1, x2, [SP], #0x10
    // 0xe8e78c: RestoreReg d2
    //     0xe8e78c: ldr             q2, [SP], #0x10
    // 0xe8e790: b               #0xe8e720
  }
  _ constrainWidth(/* No info */) {
    // ** addr: 0xe8e794, size: 0x128
    // 0xe8e794: EnterFrame
    //     0xe8e794: stp             fp, lr, [SP, #-0x10]!
    //     0xe8e798: mov             fp, SP
    // 0xe8e79c: LoadField: r0 = r4->field_13
    //     0xe8e79c: ldur            w0, [x4, #0x13]
    // 0xe8e7a0: sub             x2, x0, #2
    // 0xe8e7a4: cmp             w2, #2
    // 0xe8e7a8: b.lt            #0xe8e7bc
    // 0xe8e7ac: add             x0, fp, w2, sxtw #2
    // 0xe8e7b0: ldr             x0, [x0, #8]
    // 0xe8e7b4: LoadField: d0 = r0->field_7
    //     0xe8e7b4: ldur            d0, [x0, #7]
    // 0xe8e7b8: b               #0xe8e7c0
    // 0xe8e7bc: d0 = inf
    //     0xe8e7bc: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8e7c0: CheckStackOverflow
    //     0xe8e7c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8e7c4: cmp             SP, x16
    //     0xe8e7c8: b.ls            #0xe8e860
    // 0xe8e7cc: LoadField: d1 = r1->field_7
    //     0xe8e7cc: ldur            d1, [x1, #7]
    // 0xe8e7d0: LoadField: d2 = r1->field_f
    //     0xe8e7d0: ldur            d2, [x1, #0xf]
    // 0xe8e7d4: r1 = inline_Allocate_Double()
    //     0xe8e7d4: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe8e7d8: add             x1, x1, #0x10
    //     0xe8e7dc: cmp             x0, x1
    //     0xe8e7e0: b.ls            #0xe8e868
    //     0xe8e7e4: str             x1, [THR, #0x50]  ; THR::top
    //     0xe8e7e8: sub             x1, x1, #0xf
    //     0xe8e7ec: movz            x0, #0xe15c
    //     0xe8e7f0: movk            x0, #0x3, lsl #16
    //     0xe8e7f4: stur            x0, [x1, #-1]
    // 0xe8e7f8: StoreField: r1->field_7 = d0
    //     0xe8e7f8: stur            d0, [x1, #7]
    // 0xe8e7fc: r2 = inline_Allocate_Double()
    //     0xe8e7fc: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xe8e800: add             x2, x2, #0x10
    //     0xe8e804: cmp             x0, x2
    //     0xe8e808: b.ls            #0xe8e884
    //     0xe8e80c: str             x2, [THR, #0x50]  ; THR::top
    //     0xe8e810: sub             x2, x2, #0xf
    //     0xe8e814: movz            x0, #0xe15c
    //     0xe8e818: movk            x0, #0x3, lsl #16
    //     0xe8e81c: stur            x0, [x2, #-1]
    // 0xe8e820: StoreField: r2->field_7 = d1
    //     0xe8e820: stur            d1, [x2, #7]
    // 0xe8e824: r3 = inline_Allocate_Double()
    //     0xe8e824: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xe8e828: add             x3, x3, #0x10
    //     0xe8e82c: cmp             x0, x3
    //     0xe8e830: b.ls            #0xe8e8a0
    //     0xe8e834: str             x3, [THR, #0x50]  ; THR::top
    //     0xe8e838: sub             x3, x3, #0xf
    //     0xe8e83c: movz            x0, #0xe15c
    //     0xe8e840: movk            x0, #0x3, lsl #16
    //     0xe8e844: stur            x0, [x3, #-1]
    // 0xe8e848: StoreField: r3->field_7 = d2
    //     0xe8e848: stur            d2, [x3, #7]
    // 0xe8e84c: r0 = clamp()
    //     0xe8e84c: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xe8e850: LoadField: d0 = r0->field_7
    //     0xe8e850: ldur            d0, [x0, #7]
    // 0xe8e854: LeaveFrame
    //     0xe8e854: mov             SP, fp
    //     0xe8e858: ldp             fp, lr, [SP], #0x10
    // 0xe8e85c: ret
    //     0xe8e85c: ret             
    // 0xe8e860: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8e860: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8e864: b               #0xe8e7cc
    // 0xe8e868: stp             q1, q2, [SP, #-0x20]!
    // 0xe8e86c: SaveReg d0
    //     0xe8e86c: str             q0, [SP, #-0x10]!
    // 0xe8e870: r0 = AllocateDouble()
    //     0xe8e870: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e874: mov             x1, x0
    // 0xe8e878: RestoreReg d0
    //     0xe8e878: ldr             q0, [SP], #0x10
    // 0xe8e87c: ldp             q1, q2, [SP], #0x20
    // 0xe8e880: b               #0xe8e7f8
    // 0xe8e884: stp             q1, q2, [SP, #-0x20]!
    // 0xe8e888: SaveReg r1
    //     0xe8e888: str             x1, [SP, #-8]!
    // 0xe8e88c: r0 = AllocateDouble()
    //     0xe8e88c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e890: mov             x2, x0
    // 0xe8e894: RestoreReg r1
    //     0xe8e894: ldr             x1, [SP], #8
    // 0xe8e898: ldp             q1, q2, [SP], #0x20
    // 0xe8e89c: b               #0xe8e820
    // 0xe8e8a0: SaveReg d2
    //     0xe8e8a0: str             q2, [SP, #-0x10]!
    // 0xe8e8a4: stp             x1, x2, [SP, #-0x10]!
    // 0xe8e8a8: r0 = AllocateDouble()
    //     0xe8e8a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8e8ac: mov             x3, x0
    // 0xe8e8b0: ldp             x1, x2, [SP], #0x10
    // 0xe8e8b4: RestoreReg d2
    //     0xe8e8b4: ldr             q2, [SP], #0x10
    // 0xe8e8b8: b               #0xe8e848
  }
  _ deflate(/* No info */) {
    // ** addr: 0xe8e8bc, size: 0x1b4
    // 0xe8e8bc: EnterFrame
    //     0xe8e8bc: stp             fp, lr, [SP, #-0x10]!
    //     0xe8e8c0: mov             fp, SP
    // 0xe8e8c4: AllocStack(0x30)
    //     0xe8e8c4: sub             SP, SP, #0x30
    // 0xe8e8c8: SetupParameters(BoxConstraints this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe8e8c8: mov             x0, x2
    //     0xe8e8cc: stur            x2, [fp, #-0x10]
    //     0xe8e8d0: mov             x2, x1
    //     0xe8e8d4: stur            x1, [fp, #-8]
    // 0xe8e8d8: CheckStackOverflow
    //     0xe8e8d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8e8dc: cmp             SP, x16
    //     0xe8e8e0: b.ls            #0xe8ea68
    // 0xe8e8e4: mov             x1, x0
    // 0xe8e8e8: r0 = horizontal()
    //     0xe8e8e8: bl              #0xe89a80  ; [package:pdf/src/widgets/geometry.dart] EdgeInsetsGeometry::horizontal
    // 0xe8e8ec: ldur            x0, [fp, #-0x10]
    // 0xe8e8f0: LoadField: d1 = r0->field_f
    //     0xe8e8f0: ldur            d1, [x0, #0xf]
    // 0xe8e8f4: LoadField: d2 = r0->field_1f
    //     0xe8e8f4: ldur            d2, [x0, #0x1f]
    // 0xe8e8f8: fadd            d3, d1, d2
    // 0xe8e8fc: ldur            x0, [fp, #-8]
    // 0xe8e900: LoadField: d1 = r0->field_7
    //     0xe8e900: ldur            d1, [x0, #7]
    // 0xe8e904: fsub            d2, d1, d0
    // 0xe8e908: d1 = 0.000000
    //     0xe8e908: eor             v1.16b, v1.16b, v1.16b
    // 0xe8e90c: fcmp            d1, d2
    // 0xe8e910: b.le            #0xe8e91c
    // 0xe8e914: d2 = 0.000000
    //     0xe8e914: eor             v2.16b, v2.16b, v2.16b
    // 0xe8e918: b               #0xe8e944
    // 0xe8e91c: fcmp            d2, d1
    // 0xe8e920: b.gt            #0xe8e944
    // 0xe8e924: fcmp            d1, d1
    // 0xe8e928: b.ne            #0xe8e938
    // 0xe8e92c: fadd            d4, d2, d1
    // 0xe8e930: mov             v2.16b, v4.16b
    // 0xe8e934: b               #0xe8e944
    // 0xe8e938: fcmp            d2, d2
    // 0xe8e93c: b.vs            #0xe8e944
    // 0xe8e940: d2 = 0.000000
    //     0xe8e940: eor             v2.16b, v2.16b, v2.16b
    // 0xe8e944: stur            d2, [fp, #-0x30]
    // 0xe8e948: ArrayLoad: d4 = r0[0]  ; List_8
    //     0xe8e948: ldur            d4, [x0, #0x17]
    // 0xe8e94c: fsub            d5, d4, d3
    // 0xe8e950: fcmp            d1, d5
    // 0xe8e954: b.le            #0xe8e960
    // 0xe8e958: d4 = 0.000000
    //     0xe8e958: eor             v4.16b, v4.16b, v4.16b
    // 0xe8e95c: b               #0xe8e994
    // 0xe8e960: fcmp            d5, d1
    // 0xe8e964: b.le            #0xe8e970
    // 0xe8e968: mov             v4.16b, v5.16b
    // 0xe8e96c: b               #0xe8e994
    // 0xe8e970: fcmp            d1, d1
    // 0xe8e974: b.ne            #0xe8e980
    // 0xe8e978: fadd            d4, d5, d1
    // 0xe8e97c: b               #0xe8e994
    // 0xe8e980: fcmp            d5, d5
    // 0xe8e984: b.vc            #0xe8e990
    // 0xe8e988: mov             v4.16b, v5.16b
    // 0xe8e98c: b               #0xe8e994
    // 0xe8e990: d4 = 0.000000
    //     0xe8e990: eor             v4.16b, v4.16b, v4.16b
    // 0xe8e994: stur            d4, [fp, #-0x28]
    // 0xe8e998: LoadField: d5 = r0->field_f
    //     0xe8e998: ldur            d5, [x0, #0xf]
    // 0xe8e99c: fsub            d6, d5, d0
    // 0xe8e9a0: fcmp            d2, d6
    // 0xe8e9a4: b.le            #0xe8e9b0
    // 0xe8e9a8: mov             v0.16b, v2.16b
    // 0xe8e9ac: b               #0xe8e9e4
    // 0xe8e9b0: fcmp            d6, d2
    // 0xe8e9b4: b.le            #0xe8e9c0
    // 0xe8e9b8: mov             v0.16b, v6.16b
    // 0xe8e9bc: b               #0xe8e9e4
    // 0xe8e9c0: fcmp            d2, d1
    // 0xe8e9c4: b.ne            #0xe8e9d0
    // 0xe8e9c8: fadd            d0, d2, d6
    // 0xe8e9cc: b               #0xe8e9e4
    // 0xe8e9d0: fcmp            d6, d6
    // 0xe8e9d4: b.vc            #0xe8e9e0
    // 0xe8e9d8: mov             v0.16b, v6.16b
    // 0xe8e9dc: b               #0xe8e9e4
    // 0xe8e9e0: mov             v0.16b, v2.16b
    // 0xe8e9e4: stur            d0, [fp, #-0x20]
    // 0xe8e9e8: LoadField: d5 = r0->field_1f
    //     0xe8e9e8: ldur            d5, [x0, #0x1f]
    // 0xe8e9ec: fsub            d6, d5, d3
    // 0xe8e9f0: fcmp            d4, d6
    // 0xe8e9f4: b.le            #0xe8ea00
    // 0xe8e9f8: mov             v1.16b, v4.16b
    // 0xe8e9fc: b               #0xe8ea34
    // 0xe8ea00: fcmp            d6, d4
    // 0xe8ea04: b.le            #0xe8ea10
    // 0xe8ea08: mov             v1.16b, v6.16b
    // 0xe8ea0c: b               #0xe8ea34
    // 0xe8ea10: fcmp            d4, d1
    // 0xe8ea14: b.ne            #0xe8ea20
    // 0xe8ea18: fadd            d1, d4, d6
    // 0xe8ea1c: b               #0xe8ea34
    // 0xe8ea20: fcmp            d6, d6
    // 0xe8ea24: b.vc            #0xe8ea30
    // 0xe8ea28: mov             v1.16b, v6.16b
    // 0xe8ea2c: b               #0xe8ea34
    // 0xe8ea30: mov             v1.16b, v4.16b
    // 0xe8ea34: stur            d1, [fp, #-0x18]
    // 0xe8ea38: r0 = BoxConstraints()
    //     0xe8ea38: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe8ea3c: ldur            d0, [fp, #-0x30]
    // 0xe8ea40: StoreField: r0->field_7 = d0
    //     0xe8ea40: stur            d0, [x0, #7]
    // 0xe8ea44: ldur            d0, [fp, #-0x20]
    // 0xe8ea48: StoreField: r0->field_f = d0
    //     0xe8ea48: stur            d0, [x0, #0xf]
    // 0xe8ea4c: ldur            d0, [fp, #-0x28]
    // 0xe8ea50: ArrayStore: r0[0] = d0  ; List_8
    //     0xe8ea50: stur            d0, [x0, #0x17]
    // 0xe8ea54: ldur            d0, [fp, #-0x18]
    // 0xe8ea58: StoreField: r0->field_1f = d0
    //     0xe8ea58: stur            d0, [x0, #0x1f]
    // 0xe8ea5c: LeaveFrame
    //     0xe8ea5c: mov             SP, fp
    //     0xe8ea60: ldp             fp, lr, [SP], #0x10
    // 0xe8ea64: ret
    //     0xe8ea64: ret             
    // 0xe8ea68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8ea68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8ea6c: b               #0xe8e8e4
  }
  _ loosen(/* No info */) {
    // ** addr: 0xe8eca0, size: 0x44
    // 0xe8eca0: EnterFrame
    //     0xe8eca0: stp             fp, lr, [SP, #-0x10]!
    //     0xe8eca4: mov             fp, SP
    // 0xe8eca8: AllocStack(0x10)
    //     0xe8eca8: sub             SP, SP, #0x10
    // 0xe8ecac: LoadField: d0 = r1->field_f
    //     0xe8ecac: ldur            d0, [x1, #0xf]
    // 0xe8ecb0: stur            d0, [fp, #-0x10]
    // 0xe8ecb4: LoadField: d1 = r1->field_1f
    //     0xe8ecb4: ldur            d1, [x1, #0x1f]
    // 0xe8ecb8: stur            d1, [fp, #-8]
    // 0xe8ecbc: r0 = BoxConstraints()
    //     0xe8ecbc: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe8ecc0: StoreField: r0->field_7 = rZR
    //     0xe8ecc0: stur            xzr, [x0, #7]
    // 0xe8ecc4: ldur            d0, [fp, #-0x10]
    // 0xe8ecc8: StoreField: r0->field_f = d0
    //     0xe8ecc8: stur            d0, [x0, #0xf]
    // 0xe8eccc: ArrayStore: r0[0] = rZR  ; List_8
    //     0xe8eccc: stur            xzr, [x0, #0x17]
    // 0xe8ecd0: ldur            d0, [fp, #-8]
    // 0xe8ecd4: StoreField: r0->field_1f = d0
    //     0xe8ecd4: stur            d0, [x0, #0x1f]
    // 0xe8ecd8: LeaveFrame
    //     0xe8ecd8: mov             SP, fp
    //     0xe8ecdc: ldp             fp, lr, [SP], #0x10
    // 0xe8ece0: ret
    //     0xe8ece0: ret             
  }
  get _ smallest(/* No info */) {
    // ** addr: 0xe8edec, size: 0x78
    // 0xe8edec: EnterFrame
    //     0xe8edec: stp             fp, lr, [SP, #-0x10]!
    //     0xe8edf0: mov             fp, SP
    // 0xe8edf4: AllocStack(0x20)
    //     0xe8edf4: sub             SP, SP, #0x20
    // 0xe8edf8: SetupParameters(BoxConstraints this /* r1 => r0, fp-0x8 */)
    //     0xe8edf8: mov             x0, x1
    //     0xe8edfc: stur            x1, [fp, #-8]
    // 0xe8ee00: CheckStackOverflow
    //     0xe8ee00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ee04: cmp             SP, x16
    //     0xe8ee08: b.ls            #0xe8ee5c
    // 0xe8ee0c: r16 = 0.000000
    //     0xe8ee0c: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe8ee10: str             x16, [SP]
    // 0xe8ee14: mov             x1, x0
    // 0xe8ee18: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe8ee18: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe8ee1c: r0 = constrainWidth()
    //     0xe8ee1c: bl              #0xe8e794  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainWidth
    // 0xe8ee20: stur            d0, [fp, #-0x10]
    // 0xe8ee24: r16 = 0.000000
    //     0xe8ee24: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe8ee28: str             x16, [SP]
    // 0xe8ee2c: ldur            x1, [fp, #-8]
    // 0xe8ee30: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe8ee30: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe8ee34: r0 = constrainHeight()
    //     0xe8ee34: bl              #0xe8e66c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainHeight
    // 0xe8ee38: stur            d0, [fp, #-0x18]
    // 0xe8ee3c: r0 = PdfPoint()
    //     0xe8ee3c: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe8ee40: ldur            d0, [fp, #-0x10]
    // 0xe8ee44: StoreField: r0->field_7 = d0
    //     0xe8ee44: stur            d0, [x0, #7]
    // 0xe8ee48: ldur            d0, [fp, #-0x18]
    // 0xe8ee4c: StoreField: r0->field_f = d0
    //     0xe8ee4c: stur            d0, [x0, #0xf]
    // 0xe8ee50: LeaveFrame
    //     0xe8ee50: mov             SP, fp
    //     0xe8ee54: ldp             fp, lr, [SP], #0x10
    // 0xe8ee58: ret
    //     0xe8ee58: ret             
    // 0xe8ee5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8ee5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8ee60: b               #0xe8ee0c
  }
  _ enforce(/* No info */) {
    // ** addr: 0xe8ee64, size: 0x358
    // 0xe8ee64: EnterFrame
    //     0xe8ee64: stp             fp, lr, [SP, #-0x10]!
    //     0xe8ee68: mov             fp, SP
    // 0xe8ee6c: AllocStack(0x38)
    //     0xe8ee6c: sub             SP, SP, #0x38
    // 0xe8ee70: SetupParameters(BoxConstraints this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0xe8ee70: mov             x4, x1
    //     0xe8ee74: mov             x0, x2
    //     0xe8ee78: stur            x1, [fp, #-0x18]
    //     0xe8ee7c: stur            x2, [fp, #-0x20]
    // 0xe8ee80: CheckStackOverflow
    //     0xe8ee80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ee84: cmp             SP, x16
    //     0xe8ee88: b.ls            #0xe8f0a4
    // 0xe8ee8c: LoadField: d0 = r4->field_7
    //     0xe8ee8c: ldur            d0, [x4, #7]
    // 0xe8ee90: LoadField: d1 = r0->field_7
    //     0xe8ee90: ldur            d1, [x0, #7]
    // 0xe8ee94: LoadField: d2 = r0->field_f
    //     0xe8ee94: ldur            d2, [x0, #0xf]
    // 0xe8ee98: r1 = inline_Allocate_Double()
    //     0xe8ee98: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe8ee9c: add             x1, x1, #0x10
    //     0xe8eea0: cmp             x2, x1
    //     0xe8eea4: b.ls            #0xe8f0ac
    //     0xe8eea8: str             x1, [THR, #0x50]  ; THR::top
    //     0xe8eeac: sub             x1, x1, #0xf
    //     0xe8eeb0: movz            x2, #0xe15c
    //     0xe8eeb4: movk            x2, #0x3, lsl #16
    //     0xe8eeb8: stur            x2, [x1, #-1]
    // 0xe8eebc: StoreField: r1->field_7 = d0
    //     0xe8eebc: stur            d0, [x1, #7]
    // 0xe8eec0: r5 = inline_Allocate_Double()
    //     0xe8eec0: ldp             x5, x2, [THR, #0x50]  ; THR::top
    //     0xe8eec4: add             x5, x5, #0x10
    //     0xe8eec8: cmp             x2, x5
    //     0xe8eecc: b.ls            #0xe8f0d0
    //     0xe8eed0: str             x5, [THR, #0x50]  ; THR::top
    //     0xe8eed4: sub             x5, x5, #0xf
    //     0xe8eed8: movz            x2, #0xe15c
    //     0xe8eedc: movk            x2, #0x3, lsl #16
    //     0xe8eee0: stur            x2, [x5, #-1]
    // 0xe8eee4: StoreField: r5->field_7 = d1
    //     0xe8eee4: stur            d1, [x5, #7]
    // 0xe8eee8: stur            x5, [fp, #-0x10]
    // 0xe8eeec: r6 = inline_Allocate_Double()
    //     0xe8eeec: ldp             x6, x2, [THR, #0x50]  ; THR::top
    //     0xe8eef0: add             x6, x6, #0x10
    //     0xe8eef4: cmp             x2, x6
    //     0xe8eef8: b.ls            #0xe8f0f4
    //     0xe8eefc: str             x6, [THR, #0x50]  ; THR::top
    //     0xe8ef00: sub             x6, x6, #0xf
    //     0xe8ef04: movz            x2, #0xe15c
    //     0xe8ef08: movk            x2, #0x3, lsl #16
    //     0xe8ef0c: stur            x2, [x6, #-1]
    // 0xe8ef10: StoreField: r6->field_7 = d2
    //     0xe8ef10: stur            d2, [x6, #7]
    // 0xe8ef14: mov             x2, x5
    // 0xe8ef18: mov             x3, x6
    // 0xe8ef1c: stur            x6, [fp, #-8]
    // 0xe8ef20: r0 = clamp()
    //     0xe8ef20: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xe8ef24: mov             x4, x0
    // 0xe8ef28: ldur            x0, [fp, #-0x18]
    // 0xe8ef2c: stur            x4, [fp, #-0x28]
    // 0xe8ef30: LoadField: d0 = r0->field_f
    //     0xe8ef30: ldur            d0, [x0, #0xf]
    // 0xe8ef34: r1 = inline_Allocate_Double()
    //     0xe8ef34: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe8ef38: add             x1, x1, #0x10
    //     0xe8ef3c: cmp             x2, x1
    //     0xe8ef40: b.ls            #0xe8f118
    //     0xe8ef44: str             x1, [THR, #0x50]  ; THR::top
    //     0xe8ef48: sub             x1, x1, #0xf
    //     0xe8ef4c: movz            x2, #0xe15c
    //     0xe8ef50: movk            x2, #0x3, lsl #16
    //     0xe8ef54: stur            x2, [x1, #-1]
    // 0xe8ef58: StoreField: r1->field_7 = d0
    //     0xe8ef58: stur            d0, [x1, #7]
    // 0xe8ef5c: ldur            x2, [fp, #-0x10]
    // 0xe8ef60: ldur            x3, [fp, #-8]
    // 0xe8ef64: r0 = clamp()
    //     0xe8ef64: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xe8ef68: mov             x4, x0
    // 0xe8ef6c: ldur            x0, [fp, #-0x18]
    // 0xe8ef70: stur            x4, [fp, #-0x30]
    // 0xe8ef74: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe8ef74: ldur            d0, [x0, #0x17]
    // 0xe8ef78: ldur            x1, [fp, #-0x20]
    // 0xe8ef7c: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xe8ef7c: ldur            d1, [x1, #0x17]
    // 0xe8ef80: LoadField: d2 = r1->field_1f
    //     0xe8ef80: ldur            d2, [x1, #0x1f]
    // 0xe8ef84: r1 = inline_Allocate_Double()
    //     0xe8ef84: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe8ef88: add             x1, x1, #0x10
    //     0xe8ef8c: cmp             x2, x1
    //     0xe8ef90: b.ls            #0xe8f134
    //     0xe8ef94: str             x1, [THR, #0x50]  ; THR::top
    //     0xe8ef98: sub             x1, x1, #0xf
    //     0xe8ef9c: movz            x2, #0xe15c
    //     0xe8efa0: movk            x2, #0x3, lsl #16
    //     0xe8efa4: stur            x2, [x1, #-1]
    // 0xe8efa8: StoreField: r1->field_7 = d0
    //     0xe8efa8: stur            d0, [x1, #7]
    // 0xe8efac: r5 = inline_Allocate_Double()
    //     0xe8efac: ldp             x5, x2, [THR, #0x50]  ; THR::top
    //     0xe8efb0: add             x5, x5, #0x10
    //     0xe8efb4: cmp             x2, x5
    //     0xe8efb8: b.ls            #0xe8f158
    //     0xe8efbc: str             x5, [THR, #0x50]  ; THR::top
    //     0xe8efc0: sub             x5, x5, #0xf
    //     0xe8efc4: movz            x2, #0xe15c
    //     0xe8efc8: movk            x2, #0x3, lsl #16
    //     0xe8efcc: stur            x2, [x5, #-1]
    // 0xe8efd0: StoreField: r5->field_7 = d1
    //     0xe8efd0: stur            d1, [x5, #7]
    // 0xe8efd4: stur            x5, [fp, #-0x10]
    // 0xe8efd8: r6 = inline_Allocate_Double()
    //     0xe8efd8: ldp             x6, x2, [THR, #0x50]  ; THR::top
    //     0xe8efdc: add             x6, x6, #0x10
    //     0xe8efe0: cmp             x2, x6
    //     0xe8efe4: b.ls            #0xe8f17c
    //     0xe8efe8: str             x6, [THR, #0x50]  ; THR::top
    //     0xe8efec: sub             x6, x6, #0xf
    //     0xe8eff0: movz            x2, #0xe15c
    //     0xe8eff4: movk            x2, #0x3, lsl #16
    //     0xe8eff8: stur            x2, [x6, #-1]
    // 0xe8effc: StoreField: r6->field_7 = d2
    //     0xe8effc: stur            d2, [x6, #7]
    // 0xe8f000: mov             x2, x5
    // 0xe8f004: mov             x3, x6
    // 0xe8f008: stur            x6, [fp, #-8]
    // 0xe8f00c: r0 = clamp()
    //     0xe8f00c: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xe8f010: mov             x4, x0
    // 0xe8f014: ldur            x0, [fp, #-0x18]
    // 0xe8f018: stur            x4, [fp, #-0x20]
    // 0xe8f01c: LoadField: d0 = r0->field_1f
    //     0xe8f01c: ldur            d0, [x0, #0x1f]
    // 0xe8f020: r1 = inline_Allocate_Double()
    //     0xe8f020: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe8f024: add             x1, x1, #0x10
    //     0xe8f028: cmp             x0, x1
    //     0xe8f02c: b.ls            #0xe8f1a0
    //     0xe8f030: str             x1, [THR, #0x50]  ; THR::top
    //     0xe8f034: sub             x1, x1, #0xf
    //     0xe8f038: movz            x0, #0xe15c
    //     0xe8f03c: movk            x0, #0x3, lsl #16
    //     0xe8f040: stur            x0, [x1, #-1]
    // 0xe8f044: StoreField: r1->field_7 = d0
    //     0xe8f044: stur            d0, [x1, #7]
    // 0xe8f048: ldur            x2, [fp, #-0x10]
    // 0xe8f04c: ldur            x3, [fp, #-8]
    // 0xe8f050: r0 = clamp()
    //     0xe8f050: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xe8f054: mov             x1, x0
    // 0xe8f058: ldur            x0, [fp, #-0x28]
    // 0xe8f05c: stur            x1, [fp, #-8]
    // 0xe8f060: LoadField: d0 = r0->field_7
    //     0xe8f060: ldur            d0, [x0, #7]
    // 0xe8f064: stur            d0, [fp, #-0x38]
    // 0xe8f068: r0 = BoxConstraints()
    //     0xe8f068: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe8f06c: ldur            d0, [fp, #-0x38]
    // 0xe8f070: StoreField: r0->field_7 = d0
    //     0xe8f070: stur            d0, [x0, #7]
    // 0xe8f074: ldur            x1, [fp, #-0x30]
    // 0xe8f078: LoadField: d0 = r1->field_7
    //     0xe8f078: ldur            d0, [x1, #7]
    // 0xe8f07c: StoreField: r0->field_f = d0
    //     0xe8f07c: stur            d0, [x0, #0xf]
    // 0xe8f080: ldur            x1, [fp, #-0x20]
    // 0xe8f084: LoadField: d0 = r1->field_7
    //     0xe8f084: ldur            d0, [x1, #7]
    // 0xe8f088: ArrayStore: r0[0] = d0  ; List_8
    //     0xe8f088: stur            d0, [x0, #0x17]
    // 0xe8f08c: ldur            x1, [fp, #-8]
    // 0xe8f090: LoadField: d0 = r1->field_7
    //     0xe8f090: ldur            d0, [x1, #7]
    // 0xe8f094: StoreField: r0->field_1f = d0
    //     0xe8f094: stur            d0, [x0, #0x1f]
    // 0xe8f098: LeaveFrame
    //     0xe8f098: mov             SP, fp
    //     0xe8f09c: ldp             fp, lr, [SP], #0x10
    // 0xe8f0a0: ret
    //     0xe8f0a0: ret             
    // 0xe8f0a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8f0a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8f0a8: b               #0xe8ee8c
    // 0xe8f0ac: stp             q1, q2, [SP, #-0x20]!
    // 0xe8f0b0: SaveReg d0
    //     0xe8f0b0: str             q0, [SP, #-0x10]!
    // 0xe8f0b4: stp             x0, x4, [SP, #-0x10]!
    // 0xe8f0b8: r0 = AllocateDouble()
    //     0xe8f0b8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8f0bc: mov             x1, x0
    // 0xe8f0c0: ldp             x0, x4, [SP], #0x10
    // 0xe8f0c4: RestoreReg d0
    //     0xe8f0c4: ldr             q0, [SP], #0x10
    // 0xe8f0c8: ldp             q1, q2, [SP], #0x20
    // 0xe8f0cc: b               #0xe8eebc
    // 0xe8f0d0: stp             q1, q2, [SP, #-0x20]!
    // 0xe8f0d4: stp             x1, x4, [SP, #-0x10]!
    // 0xe8f0d8: SaveReg r0
    //     0xe8f0d8: str             x0, [SP, #-8]!
    // 0xe8f0dc: r0 = AllocateDouble()
    //     0xe8f0dc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8f0e0: mov             x5, x0
    // 0xe8f0e4: RestoreReg r0
    //     0xe8f0e4: ldr             x0, [SP], #8
    // 0xe8f0e8: ldp             x1, x4, [SP], #0x10
    // 0xe8f0ec: ldp             q1, q2, [SP], #0x20
    // 0xe8f0f0: b               #0xe8eee4
    // 0xe8f0f4: SaveReg d2
    //     0xe8f0f4: str             q2, [SP, #-0x10]!
    // 0xe8f0f8: stp             x4, x5, [SP, #-0x10]!
    // 0xe8f0fc: stp             x0, x1, [SP, #-0x10]!
    // 0xe8f100: r0 = AllocateDouble()
    //     0xe8f100: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8f104: mov             x6, x0
    // 0xe8f108: ldp             x0, x1, [SP], #0x10
    // 0xe8f10c: ldp             x4, x5, [SP], #0x10
    // 0xe8f110: RestoreReg d2
    //     0xe8f110: ldr             q2, [SP], #0x10
    // 0xe8f114: b               #0xe8ef10
    // 0xe8f118: SaveReg d0
    //     0xe8f118: str             q0, [SP, #-0x10]!
    // 0xe8f11c: stp             x0, x4, [SP, #-0x10]!
    // 0xe8f120: r0 = AllocateDouble()
    //     0xe8f120: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8f124: mov             x1, x0
    // 0xe8f128: ldp             x0, x4, [SP], #0x10
    // 0xe8f12c: RestoreReg d0
    //     0xe8f12c: ldr             q0, [SP], #0x10
    // 0xe8f130: b               #0xe8ef58
    // 0xe8f134: stp             q1, q2, [SP, #-0x20]!
    // 0xe8f138: SaveReg d0
    //     0xe8f138: str             q0, [SP, #-0x10]!
    // 0xe8f13c: stp             x0, x4, [SP, #-0x10]!
    // 0xe8f140: r0 = AllocateDouble()
    //     0xe8f140: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8f144: mov             x1, x0
    // 0xe8f148: ldp             x0, x4, [SP], #0x10
    // 0xe8f14c: RestoreReg d0
    //     0xe8f14c: ldr             q0, [SP], #0x10
    // 0xe8f150: ldp             q1, q2, [SP], #0x20
    // 0xe8f154: b               #0xe8efa8
    // 0xe8f158: stp             q1, q2, [SP, #-0x20]!
    // 0xe8f15c: stp             x1, x4, [SP, #-0x10]!
    // 0xe8f160: SaveReg r0
    //     0xe8f160: str             x0, [SP, #-8]!
    // 0xe8f164: r0 = AllocateDouble()
    //     0xe8f164: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8f168: mov             x5, x0
    // 0xe8f16c: RestoreReg r0
    //     0xe8f16c: ldr             x0, [SP], #8
    // 0xe8f170: ldp             x1, x4, [SP], #0x10
    // 0xe8f174: ldp             q1, q2, [SP], #0x20
    // 0xe8f178: b               #0xe8efd0
    // 0xe8f17c: SaveReg d2
    //     0xe8f17c: str             q2, [SP, #-0x10]!
    // 0xe8f180: stp             x4, x5, [SP, #-0x10]!
    // 0xe8f184: stp             x0, x1, [SP, #-0x10]!
    // 0xe8f188: r0 = AllocateDouble()
    //     0xe8f188: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8f18c: mov             x6, x0
    // 0xe8f190: ldp             x0, x1, [SP], #0x10
    // 0xe8f194: ldp             x4, x5, [SP], #0x10
    // 0xe8f198: RestoreReg d2
    //     0xe8f198: ldr             q2, [SP], #0x10
    // 0xe8f19c: b               #0xe8effc
    // 0xe8f1a0: SaveReg d0
    //     0xe8f1a0: str             q0, [SP, #-0x10]!
    // 0xe8f1a4: SaveReg r4
    //     0xe8f1a4: str             x4, [SP, #-8]!
    // 0xe8f1a8: r0 = AllocateDouble()
    //     0xe8f1a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8f1ac: mov             x1, x0
    // 0xe8f1b0: RestoreReg r4
    //     0xe8f1b0: ldr             x4, [SP], #8
    // 0xe8f1b4: RestoreReg d0
    //     0xe8f1b4: ldr             q0, [SP], #0x10
    // 0xe8f1b8: b               #0xe8f044
  }
  _ constrain(/* No info */) {
    // ** addr: 0xe9fe7c, size: 0xfc
    // 0xe9fe7c: EnterFrame
    //     0xe9fe7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe9fe80: mov             fp, SP
    // 0xe9fe84: AllocStack(0x28)
    //     0xe9fe84: sub             SP, SP, #0x28
    // 0xe9fe88: SetupParameters(BoxConstraints this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe9fe88: mov             x0, x1
    //     0xe9fe8c: stur            x1, [fp, #-8]
    //     0xe9fe90: stur            x2, [fp, #-0x10]
    // 0xe9fe94: CheckStackOverflow
    //     0xe9fe94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9fe98: cmp             SP, x16
    //     0xe9fe9c: b.ls            #0xe9ff44
    // 0xe9fea0: LoadField: d0 = r2->field_7
    //     0xe9fea0: ldur            d0, [x2, #7]
    // 0xe9fea4: r1 = inline_Allocate_Double()
    //     0xe9fea4: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xe9fea8: add             x1, x1, #0x10
    //     0xe9feac: cmp             x3, x1
    //     0xe9feb0: b.ls            #0xe9ff4c
    //     0xe9feb4: str             x1, [THR, #0x50]  ; THR::top
    //     0xe9feb8: sub             x1, x1, #0xf
    //     0xe9febc: movz            x3, #0xe15c
    //     0xe9fec0: movk            x3, #0x3, lsl #16
    //     0xe9fec4: stur            x3, [x1, #-1]
    // 0xe9fec8: StoreField: r1->field_7 = d0
    //     0xe9fec8: stur            d0, [x1, #7]
    // 0xe9fecc: str             x1, [SP]
    // 0xe9fed0: mov             x1, x0
    // 0xe9fed4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe9fed4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe9fed8: r0 = constrainWidth()
    //     0xe9fed8: bl              #0xe8e794  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainWidth
    // 0xe9fedc: ldur            x0, [fp, #-0x10]
    // 0xe9fee0: stur            d0, [fp, #-0x18]
    // 0xe9fee4: LoadField: d1 = r0->field_f
    //     0xe9fee4: ldur            d1, [x0, #0xf]
    // 0xe9fee8: r0 = inline_Allocate_Double()
    //     0xe9fee8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe9feec: add             x0, x0, #0x10
    //     0xe9fef0: cmp             x1, x0
    //     0xe9fef4: b.ls            #0xe9ff68
    //     0xe9fef8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe9fefc: sub             x0, x0, #0xf
    //     0xe9ff00: movz            x1, #0xe15c
    //     0xe9ff04: movk            x1, #0x3, lsl #16
    //     0xe9ff08: stur            x1, [x0, #-1]
    // 0xe9ff0c: StoreField: r0->field_7 = d1
    //     0xe9ff0c: stur            d1, [x0, #7]
    // 0xe9ff10: str             x0, [SP]
    // 0xe9ff14: ldur            x1, [fp, #-8]
    // 0xe9ff18: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe9ff18: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe9ff1c: r0 = constrainHeight()
    //     0xe9ff1c: bl              #0xe8e66c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainHeight
    // 0xe9ff20: stur            d0, [fp, #-0x20]
    // 0xe9ff24: r0 = PdfPoint()
    //     0xe9ff24: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe9ff28: ldur            d0, [fp, #-0x18]
    // 0xe9ff2c: StoreField: r0->field_7 = d0
    //     0xe9ff2c: stur            d0, [x0, #7]
    // 0xe9ff30: ldur            d0, [fp, #-0x20]
    // 0xe9ff34: StoreField: r0->field_f = d0
    //     0xe9ff34: stur            d0, [x0, #0xf]
    // 0xe9ff38: LeaveFrame
    //     0xe9ff38: mov             SP, fp
    //     0xe9ff3c: ldp             fp, lr, [SP], #0x10
    // 0xe9ff40: ret
    //     0xe9ff40: ret             
    // 0xe9ff44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe9ff44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe9ff48: b               #0xe9fea0
    // 0xe9ff4c: SaveReg d0
    //     0xe9ff4c: str             q0, [SP, #-0x10]!
    // 0xe9ff50: stp             x0, x2, [SP, #-0x10]!
    // 0xe9ff54: r0 = AllocateDouble()
    //     0xe9ff54: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe9ff58: mov             x1, x0
    // 0xe9ff5c: ldp             x0, x2, [SP], #0x10
    // 0xe9ff60: RestoreReg d0
    //     0xe9ff60: ldr             q0, [SP], #0x10
    // 0xe9ff64: b               #0xe9fec8
    // 0xe9ff68: stp             q0, q1, [SP, #-0x20]!
    // 0xe9ff6c: r0 = AllocateDouble()
    //     0xe9ff6c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe9ff70: ldp             q0, q1, [SP], #0x20
    // 0xe9ff74: b               #0xe9ff0c
  }
}
