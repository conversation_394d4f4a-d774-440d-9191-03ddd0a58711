// lib: , url: package:pdf/src/widgets/image.dart

// class id: 1050853, size: 0x8
class :: {

  static _ _paintImage(/* No info */) {
    // ** addr: 0xe68900, size: 0x28c
    // 0xe68900: EnterFrame
    //     0xe68900: stp             fp, lr, [SP, #-0x10]!
    //     0xe68904: mov             fp, SP
    // 0xe68908: AllocStack(0x70)
    //     0xe68908: sub             SP, SP, #0x70
    // 0xe6890c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xe6890c: mov             x0, x1
    //     0xe68910: stur            x1, [fp, #-8]
    //     0xe68914: mov             x1, x3
    //     0xe68918: stur            x2, [fp, #-0x10]
    //     0xe6891c: stur            x3, [fp, #-0x18]
    // 0xe68920: CheckStackOverflow
    //     0xe68920: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe68924: cmp             SP, x16
    //     0xe68928: b.ls            #0xe68b7c
    // 0xe6892c: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe6892c: ldur            d0, [x1, #0x17]
    // 0xe68930: stur            d0, [fp, #-0x50]
    // 0xe68934: LoadField: d1 = r1->field_1f
    //     0xe68934: ldur            d1, [x1, #0x1f]
    // 0xe68938: stur            d1, [fp, #-0x48]
    // 0xe6893c: r0 = PdfPoint()
    //     0xe6893c: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe68940: mov             x2, x0
    // 0xe68944: ldur            d0, [fp, #-0x50]
    // 0xe68948: stur            x2, [fp, #-0x28]
    // 0xe6894c: StoreField: r2->field_7 = d0
    //     0xe6894c: stur            d0, [x2, #7]
    // 0xe68950: ldur            d1, [fp, #-0x48]
    // 0xe68954: StoreField: r2->field_f = d1
    //     0xe68954: stur            d1, [x2, #0xf]
    // 0xe68958: ldur            x3, [fp, #-0x10]
    // 0xe6895c: LoadField: r0 = r3->field_43
    //     0xe6895c: ldur            w0, [x3, #0x43]
    // 0xe68960: DecompressPointer r0
    //     0xe68960: add             x0, x0, HEAP, lsl #32
    // 0xe68964: LoadField: r4 = r0->field_7
    //     0xe68964: ldur            x4, [x0, #7]
    // 0xe68968: stur            x4, [fp, #-0x20]
    // 0xe6896c: cmp             x4, #4
    // 0xe68970: b.lt            #0xe68980
    // 0xe68974: LoadField: r0 = r3->field_3b
    //     0xe68974: ldur            x0, [x3, #0x3b]
    // 0xe68978: mov             x5, x0
    // 0xe6897c: b               #0xe68988
    // 0xe68980: LoadField: r0 = r3->field_33
    //     0xe68980: ldur            x0, [x3, #0x33]
    // 0xe68984: mov             x5, x0
    // 0xe68988: r0 = BoxInt64Instr(r5)
    //     0xe68988: sbfiz           x0, x5, #1, #0x1f
    //     0xe6898c: cmp             x5, x0, asr #1
    //     0xe68990: b.eq            #0xe6899c
    //     0xe68994: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe68998: stur            x5, [x0, #7]
    // 0xe6899c: stp             x0, NULL, [SP]
    // 0xe689a0: r0 = _Double.fromInteger()
    //     0xe689a0: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe689a4: mov             x2, x0
    // 0xe689a8: ldur            x0, [fp, #-0x20]
    // 0xe689ac: stur            x2, [fp, #-0x30]
    // 0xe689b0: cmp             x0, #4
    // 0xe689b4: b.ge            #0xe689c8
    // 0xe689b8: ldur            x3, [fp, #-0x10]
    // 0xe689bc: LoadField: r0 = r3->field_3b
    //     0xe689bc: ldur            x0, [x3, #0x3b]
    // 0xe689c0: mov             x4, x0
    // 0xe689c4: b               #0xe689d4
    // 0xe689c8: ldur            x3, [fp, #-0x10]
    // 0xe689cc: LoadField: r0 = r3->field_33
    //     0xe689cc: ldur            x0, [x3, #0x33]
    // 0xe689d0: mov             x4, x0
    // 0xe689d4: ldur            d0, [fp, #-0x50]
    // 0xe689d8: ldur            d1, [fp, #-0x48]
    // 0xe689dc: r0 = BoxInt64Instr(r4)
    //     0xe689dc: sbfiz           x0, x4, #1, #0x1f
    //     0xe689e0: cmp             x4, x0, asr #1
    //     0xe689e4: b.eq            #0xe689f0
    //     0xe689e8: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe689ec: stur            x4, [x0, #7]
    // 0xe689f0: stp             x0, NULL, [SP]
    // 0xe689f4: r0 = _Double.fromInteger()
    //     0xe689f4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe689f8: mov             x1, x0
    // 0xe689fc: ldur            x0, [fp, #-0x30]
    // 0xe68a00: stur            x1, [fp, #-0x38]
    // 0xe68a04: LoadField: d0 = r0->field_7
    //     0xe68a04: ldur            d0, [x0, #7]
    // 0xe68a08: stur            d0, [fp, #-0x58]
    // 0xe68a0c: r0 = PdfPoint()
    //     0xe68a0c: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe68a10: ldur            d0, [fp, #-0x58]
    // 0xe68a14: stur            x0, [fp, #-0x30]
    // 0xe68a18: StoreField: r0->field_7 = d0
    //     0xe68a18: stur            d0, [x0, #7]
    // 0xe68a1c: ldur            x1, [fp, #-0x38]
    // 0xe68a20: LoadField: d1 = r1->field_7
    //     0xe68a20: ldur            d1, [x1, #7]
    // 0xe68a24: StoreField: r0->field_f = d1
    //     0xe68a24: stur            d1, [x0, #0xf]
    // 0xe68a28: d2 = 1.000000
    //     0xe68a28: fmov            d2, #1.00000000
    // 0xe68a2c: fdiv            d3, d0, d2
    // 0xe68a30: stur            d3, [fp, #-0x60]
    // 0xe68a34: fdiv            d0, d1, d2
    // 0xe68a38: stur            d0, [fp, #-0x58]
    // 0xe68a3c: r0 = PdfPoint()
    //     0xe68a3c: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe68a40: ldur            d0, [fp, #-0x60]
    // 0xe68a44: StoreField: r0->field_7 = d0
    //     0xe68a44: stur            d0, [x0, #7]
    // 0xe68a48: ldur            d0, [fp, #-0x58]
    // 0xe68a4c: StoreField: r0->field_f = d0
    //     0xe68a4c: stur            d0, [x0, #0xf]
    // 0xe68a50: mov             x2, x0
    // 0xe68a54: ldur            x3, [fp, #-0x28]
    // 0xe68a58: r1 = Instance_BoxFit
    //     0xe68a58: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e340] Obj!BoxFit@e2ea01
    //     0xe68a5c: ldr             x1, [x1, #0x340]
    // 0xe68a60: r0 = applyBoxFit()
    //     0xe68a60: bl              #0xe68f18  ; [package:pdf/src/widgets/geometry.dart] ::applyBoxFit
    // 0xe68a64: stur            x0, [fp, #-0x28]
    // 0xe68a68: LoadField: r1 = r0->field_7
    //     0xe68a68: ldur            w1, [x0, #7]
    // 0xe68a6c: DecompressPointer r1
    //     0xe68a6c: add             x1, x1, HEAP, lsl #32
    // 0xe68a70: cmp             w1, NULL
    // 0xe68a74: b.eq            #0xe68b84
    // 0xe68a78: LoadField: d0 = r1->field_7
    //     0xe68a78: ldur            d0, [x1, #7]
    // 0xe68a7c: stur            d0, [fp, #-0x60]
    // 0xe68a80: LoadField: d1 = r1->field_f
    //     0xe68a80: ldur            d1, [x1, #0xf]
    // 0xe68a84: stur            d1, [fp, #-0x58]
    // 0xe68a88: r0 = PdfPoint()
    //     0xe68a88: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe68a8c: ldur            d0, [fp, #-0x60]
    // 0xe68a90: stur            x0, [fp, #-0x40]
    // 0xe68a94: StoreField: r0->field_7 = d0
    //     0xe68a94: stur            d0, [x0, #7]
    // 0xe68a98: ldur            d0, [fp, #-0x58]
    // 0xe68a9c: StoreField: r0->field_f = d0
    //     0xe68a9c: stur            d0, [x0, #0xf]
    // 0xe68aa0: ldur            x1, [fp, #-0x28]
    // 0xe68aa4: LoadField: r3 = r1->field_b
    //     0xe68aa4: ldur            w3, [x1, #0xb]
    // 0xe68aa8: DecompressPointer r3
    //     0xe68aa8: add             x3, x3, HEAP, lsl #32
    // 0xe68aac: stur            x3, [fp, #-0x38]
    // 0xe68ab0: cmp             w3, NULL
    // 0xe68ab4: b.eq            #0xe68b88
    // 0xe68ab8: LoadField: d0 = r3->field_7
    //     0xe68ab8: ldur            d0, [x3, #7]
    // 0xe68abc: ldur            d1, [fp, #-0x50]
    // 0xe68ac0: fsub            d2, d1, d0
    // 0xe68ac4: d0 = 2.000000
    //     0xe68ac4: fmov            d0, #2.00000000
    // 0xe68ac8: fdiv            d1, d2, d0
    // 0xe68acc: LoadField: d2 = r3->field_f
    //     0xe68acc: ldur            d2, [x3, #0xf]
    // 0xe68ad0: ldur            d3, [fp, #-0x48]
    // 0xe68ad4: fsub            d4, d3, d2
    // 0xe68ad8: fdiv            d2, d4, d0
    // 0xe68adc: r2 = Instance_Alignment
    //     0xe68adc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e290] Obj!Alignment@e0c481
    //     0xe68ae0: ldr             x2, [x2, #0x290]
    // 0xe68ae4: LoadField: d0 = r2->field_7
    //     0xe68ae4: ldur            d0, [x2, #7]
    // 0xe68ae8: fmul            d3, d0, d1
    // 0xe68aec: fadd            d0, d1, d3
    // 0xe68af0: stur            d0, [fp, #-0x50]
    // 0xe68af4: LoadField: d1 = r2->field_f
    //     0xe68af4: ldur            d1, [x2, #0xf]
    // 0xe68af8: fmul            d3, d1, d2
    // 0xe68afc: fadd            d1, d2, d3
    // 0xe68b00: ldur            x1, [fp, #-0x18]
    // 0xe68b04: stur            d1, [fp, #-0x48]
    // 0xe68b08: r0 = offset()
    //     0xe68b08: bl              #0xc3adb0  ; [package:pdf/src/pdf/rect.dart] PdfRect::offset
    // 0xe68b0c: mov             x1, x0
    // 0xe68b10: ldur            d0, [fp, #-0x50]
    // 0xe68b14: ldur            d1, [fp, #-0x48]
    // 0xe68b18: r0 = translate()
    //     0xe68b18: bl              #0xe68ed4  ; [package:pdf/src/pdf/point.dart] PdfPoint::translate
    // 0xe68b1c: mov             x2, x0
    // 0xe68b20: ldur            x3, [fp, #-0x38]
    // 0xe68b24: r1 = Null
    //     0xe68b24: mov             x1, NULL
    // 0xe68b28: r0 = PdfRect.fromPoints()
    //     0xe68b28: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xe68b2c: ldur            x3, [fp, #-0x30]
    // 0xe68b30: r1 = Null
    //     0xe68b30: mov             x1, NULL
    // 0xe68b34: r2 = Instance_PdfPoint
    //     0xe68b34: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xe68b38: ldr             x2, [x2, #0x730]
    // 0xe68b3c: stur            x0, [fp, #-0x18]
    // 0xe68b40: r0 = PdfRect.fromPoints()
    //     0xe68b40: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xe68b44: ldur            x2, [fp, #-0x40]
    // 0xe68b48: mov             x3, x0
    // 0xe68b4c: r1 = Instance_Alignment
    //     0xe68b4c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e290] Obj!Alignment@e0c481
    //     0xe68b50: ldr             x1, [x1, #0x290]
    // 0xe68b54: r0 = inscribe()
    //     0xe68b54: bl              #0xe68de0  ; [package:pdf/src/widgets/geometry.dart] Alignment::inscribe
    // 0xe68b58: ldur            x1, [fp, #-8]
    // 0xe68b5c: ldur            x2, [fp, #-0x10]
    // 0xe68b60: mov             x3, x0
    // 0xe68b64: ldur            x5, [fp, #-0x18]
    // 0xe68b68: r0 = _drawImageRect()
    //     0xe68b68: bl              #0xe68bc8  ; [package:pdf/src/widgets/image.dart] ::_drawImageRect
    // 0xe68b6c: r0 = Null
    //     0xe68b6c: mov             x0, NULL
    // 0xe68b70: LeaveFrame
    //     0xe68b70: mov             SP, fp
    //     0xe68b74: ldp             fp, lr, [SP], #0x10
    // 0xe68b78: ret
    //     0xe68b78: ret             
    // 0xe68b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe68b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe68b80: b               #0xe6892c
    // 0xe68b84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe68b84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe68b88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe68b88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _drawImageRect(/* No info */) {
    // ** addr: 0xe68bc8, size: 0x218
    // 0xe68bc8: EnterFrame
    //     0xe68bc8: stp             fp, lr, [SP, #-0x10]!
    //     0xe68bcc: mov             fp, SP
    // 0xe68bd0: AllocStack(0x60)
    //     0xe68bd0: sub             SP, SP, #0x60
    // 0xe68bd4: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xe68bd4: mov             x4, x1
    //     0xe68bd8: mov             x0, x2
    //     0xe68bdc: stur            x2, [fp, #-0x10]
    //     0xe68be0: mov             x2, x5
    //     0xe68be4: stur            x1, [fp, #-8]
    //     0xe68be8: stur            x3, [fp, #-0x18]
    //     0xe68bec: stur            x5, [fp, #-0x20]
    // 0xe68bf0: CheckStackOverflow
    //     0xe68bf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe68bf4: cmp             SP, x16
    //     0xe68bf8: b.ls            #0xe68dac
    // 0xe68bfc: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xe68bfc: ldur            d0, [x2, #0x17]
    // 0xe68c00: ArrayLoad: d1 = r3[0]  ; List_8
    //     0xe68c00: ldur            d1, [x3, #0x17]
    // 0xe68c04: fdiv            d2, d0, d1
    // 0xe68c08: stur            d2, [fp, #-0x38]
    // 0xe68c0c: LoadField: d0 = r2->field_1f
    //     0xe68c0c: ldur            d0, [x2, #0x1f]
    // 0xe68c10: LoadField: d1 = r3->field_1f
    //     0xe68c10: ldur            d1, [x3, #0x1f]
    // 0xe68c14: fdiv            d3, d0, d1
    // 0xe68c18: mov             x1, x4
    // 0xe68c1c: stur            d3, [fp, #-0x30]
    // 0xe68c20: r0 = saveContext()
    //     0xe68c20: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe68c24: ldur            x1, [fp, #-8]
    // 0xe68c28: ldur            x2, [fp, #-0x20]
    // 0xe68c2c: r0 = drawBox()
    //     0xe68c2c: bl              #0xe64ce4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawBox
    // 0xe68c30: ldur            x1, [fp, #-8]
    // 0xe68c34: r0 = clipPath()
    //     0xe68c34: bl              #0xe479b0  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::clipPath
    // 0xe68c38: ldur            x0, [fp, #-0x20]
    // 0xe68c3c: LoadField: d0 = r0->field_7
    //     0xe68c3c: ldur            d0, [x0, #7]
    // 0xe68c40: ldur            x1, [fp, #-0x18]
    // 0xe68c44: LoadField: d1 = r1->field_7
    //     0xe68c44: ldur            d1, [x1, #7]
    // 0xe68c48: ldur            d2, [fp, #-0x38]
    // 0xe68c4c: fmul            d3, d1, d2
    // 0xe68c50: fsub            d1, d0, d3
    // 0xe68c54: stur            d1, [fp, #-0x48]
    // 0xe68c58: LoadField: d0 = r0->field_f
    //     0xe68c58: ldur            d0, [x0, #0xf]
    // 0xe68c5c: LoadField: d3 = r1->field_f
    //     0xe68c5c: ldur            d3, [x1, #0xf]
    // 0xe68c60: ldur            d4, [fp, #-0x30]
    // 0xe68c64: fmul            d5, d3, d4
    // 0xe68c68: fsub            d3, d0, d5
    // 0xe68c6c: ldur            x2, [fp, #-0x10]
    // 0xe68c70: stur            d3, [fp, #-0x40]
    // 0xe68c74: LoadField: r0 = r2->field_43
    //     0xe68c74: ldur            w0, [x2, #0x43]
    // 0xe68c78: DecompressPointer r0
    //     0xe68c78: add             x0, x0, HEAP, lsl #32
    // 0xe68c7c: LoadField: r3 = r0->field_7
    //     0xe68c7c: ldur            x3, [x0, #7]
    // 0xe68c80: stur            x3, [fp, #-0x28]
    // 0xe68c84: cmp             x3, #4
    // 0xe68c88: b.lt            #0xe68c98
    // 0xe68c8c: LoadField: r0 = r2->field_3b
    //     0xe68c8c: ldur            x0, [x2, #0x3b]
    // 0xe68c90: mov             x4, x0
    // 0xe68c94: b               #0xe68ca0
    // 0xe68c98: LoadField: r0 = r2->field_33
    //     0xe68c98: ldur            x0, [x2, #0x33]
    // 0xe68c9c: mov             x4, x0
    // 0xe68ca0: r0 = BoxInt64Instr(r4)
    //     0xe68ca0: sbfiz           x0, x4, #1, #0x1f
    //     0xe68ca4: cmp             x4, x0, asr #1
    //     0xe68ca8: b.eq            #0xe68cb4
    //     0xe68cac: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe68cb0: stur            x4, [x0, #7]
    // 0xe68cb4: stp             x0, NULL, [SP]
    // 0xe68cb8: r0 = _Double.fromInteger()
    //     0xe68cb8: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe68cbc: LoadField: d0 = r0->field_7
    //     0xe68cbc: ldur            d0, [x0, #7]
    // 0xe68cc0: ldur            d1, [fp, #-0x38]
    // 0xe68cc4: fmul            d2, d0, d1
    // 0xe68cc8: ldur            x0, [fp, #-0x28]
    // 0xe68ccc: stur            d2, [fp, #-0x50]
    // 0xe68cd0: cmp             x0, #4
    // 0xe68cd4: b.ge            #0xe68ce8
    // 0xe68cd8: ldur            x2, [fp, #-0x10]
    // 0xe68cdc: LoadField: r0 = r2->field_3b
    //     0xe68cdc: ldur            x0, [x2, #0x3b]
    // 0xe68ce0: mov             x3, x0
    // 0xe68ce4: b               #0xe68cf4
    // 0xe68ce8: ldur            x2, [fp, #-0x10]
    // 0xe68cec: LoadField: r0 = r2->field_33
    //     0xe68cec: ldur            x0, [x2, #0x33]
    // 0xe68cf0: mov             x3, x0
    // 0xe68cf4: ldur            d0, [fp, #-0x30]
    // 0xe68cf8: r0 = BoxInt64Instr(r3)
    //     0xe68cf8: sbfiz           x0, x3, #1, #0x1f
    //     0xe68cfc: cmp             x3, x0, asr #1
    //     0xe68d00: b.eq            #0xe68d0c
    //     0xe68d04: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe68d08: stur            x3, [x0, #7]
    // 0xe68d0c: stp             x0, NULL, [SP]
    // 0xe68d10: r0 = _Double.fromInteger()
    //     0xe68d10: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe68d14: LoadField: d0 = r0->field_7
    //     0xe68d14: ldur            d0, [x0, #7]
    // 0xe68d18: ldur            d1, [fp, #-0x30]
    // 0xe68d1c: fmul            d2, d0, d1
    // 0xe68d20: ldur            d0, [fp, #-0x50]
    // 0xe68d24: r0 = inline_Allocate_Double()
    //     0xe68d24: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe68d28: add             x0, x0, #0x10
    //     0xe68d2c: cmp             x1, x0
    //     0xe68d30: b.ls            #0xe68db4
    //     0xe68d34: str             x0, [THR, #0x50]  ; THR::top
    //     0xe68d38: sub             x0, x0, #0xf
    //     0xe68d3c: movz            x1, #0xe15c
    //     0xe68d40: movk            x1, #0x3, lsl #16
    //     0xe68d44: stur            x1, [x0, #-1]
    // 0xe68d48: StoreField: r0->field_7 = d0
    //     0xe68d48: stur            d0, [x0, #7]
    // 0xe68d4c: r1 = inline_Allocate_Double()
    //     0xe68d4c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe68d50: add             x1, x1, #0x10
    //     0xe68d54: cmp             x2, x1
    //     0xe68d58: b.ls            #0xe68dc4
    //     0xe68d5c: str             x1, [THR, #0x50]  ; THR::top
    //     0xe68d60: sub             x1, x1, #0xf
    //     0xe68d64: movz            x2, #0xe15c
    //     0xe68d68: movk            x2, #0x3, lsl #16
    //     0xe68d6c: stur            x2, [x1, #-1]
    // 0xe68d70: StoreField: r1->field_7 = d2
    //     0xe68d70: stur            d2, [x1, #7]
    // 0xe68d74: stp             x1, x0, [SP]
    // 0xe68d78: ldur            x1, [fp, #-8]
    // 0xe68d7c: ldur            x2, [fp, #-0x10]
    // 0xe68d80: ldur            d0, [fp, #-0x48]
    // 0xe68d84: ldur            d1, [fp, #-0x40]
    // 0xe68d88: r4 = const [0, 0x6, 0x2, 0x6, null]
    //     0xe68d88: add             x4, PP, #0x39, lsl #12  ; [pp+0x391a0] List(5) [0, 0x6, 0x2, 0x6, Null]
    //     0xe68d8c: ldr             x4, [x4, #0x1a0]
    // 0xe68d90: r0 = drawImage()
    //     0xe68d90: bl              #0xe47e7c  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawImage
    // 0xe68d94: ldur            x1, [fp, #-8]
    // 0xe68d98: r0 = restoreContext()
    //     0xe68d98: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe68d9c: r0 = Null
    //     0xe68d9c: mov             x0, NULL
    // 0xe68da0: LeaveFrame
    //     0xe68da0: mov             SP, fp
    //     0xe68da4: ldp             fp, lr, [SP], #0x10
    // 0xe68da8: ret
    //     0xe68da8: ret             
    // 0xe68dac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe68dac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe68db0: b               #0xe68bfc
    // 0xe68db4: stp             q0, q2, [SP, #-0x20]!
    // 0xe68db8: r0 = AllocateDouble()
    //     0xe68db8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe68dbc: ldp             q0, q2, [SP], #0x20
    // 0xe68dc0: b               #0xe68d48
    // 0xe68dc4: SaveReg d2
    //     0xe68dc4: str             q2, [SP, #-0x10]!
    // 0xe68dc8: SaveReg r0
    //     0xe68dc8: str             x0, [SP, #-8]!
    // 0xe68dcc: r0 = AllocateDouble()
    //     0xe68dcc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe68dd0: mov             x1, x0
    // 0xe68dd4: RestoreReg r0
    //     0xe68dd4: ldr             x0, [SP], #8
    // 0xe68dd8: RestoreReg d2
    //     0xe68dd8: ldr             q2, [SP], #0x10
    // 0xe68ddc: b               #0xe68d70
  }
}

// class id: 790, size: 0x24, field offset: 0xc
class Image extends Widget {

  _ paint(/* No info */) {
    // ** addr: 0xe68850, size: 0xb0
    // 0xe68850: EnterFrame
    //     0xe68850: stp             fp, lr, [SP, #-0x10]!
    //     0xe68854: mov             fp, SP
    // 0xe68858: AllocStack(0x18)
    //     0xe68858: sub             SP, SP, #0x18
    // 0xe6885c: SetupParameters(Image this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe6885c: mov             x3, x1
    //     0xe68860: mov             x0, x2
    //     0xe68864: stur            x1, [fp, #-8]
    //     0xe68868: stur            x2, [fp, #-0x10]
    // 0xe6886c: CheckStackOverflow
    //     0xe6886c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe68870: cmp             SP, x16
    //     0xe68874: b.ls            #0xe688ec
    // 0xe68878: LoadField: r2 = r3->field_7
    //     0xe68878: ldur            w2, [x3, #7]
    // 0xe6887c: DecompressPointer r2
    //     0xe6887c: add             x2, x2, HEAP, lsl #32
    // 0xe68880: cmp             w2, NULL
    // 0xe68884: b.eq            #0xe688f4
    // 0xe68888: mov             x1, x0
    // 0xe6888c: r0 = localToGlobal()
    //     0xe6888c: bl              #0xe6d458  ; [package:pdf/src/widgets/widget.dart] Context::localToGlobal
    // 0xe68890: ldur            x2, [fp, #-0x10]
    // 0xe68894: LoadField: r0 = r2->field_b
    //     0xe68894: ldur            w0, [x2, #0xb]
    // 0xe68898: DecompressPointer r0
    //     0xe68898: add             x0, x0, HEAP, lsl #32
    // 0xe6889c: stur            x0, [fp, #-0x18]
    // 0xe688a0: cmp             w0, NULL
    // 0xe688a4: b.eq            #0xe688f8
    // 0xe688a8: ldur            x3, [fp, #-8]
    // 0xe688ac: LoadField: r1 = r3->field_b
    //     0xe688ac: ldur            w1, [x3, #0xb]
    // 0xe688b0: DecompressPointer r1
    //     0xe688b0: add             x1, x1, HEAP, lsl #32
    // 0xe688b4: r0 = resolve()
    //     0xe688b4: bl              #0xe692b4  ; [package:pdf/src/widgets/image_provider.dart] ImageProvider::resolve
    // 0xe688b8: mov             x1, x0
    // 0xe688bc: ldur            x0, [fp, #-8]
    // 0xe688c0: LoadField: r3 = r0->field_7
    //     0xe688c0: ldur            w3, [x0, #7]
    // 0xe688c4: DecompressPointer r3
    //     0xe688c4: add             x3, x3, HEAP, lsl #32
    // 0xe688c8: cmp             w3, NULL
    // 0xe688cc: b.eq            #0xe688fc
    // 0xe688d0: mov             x2, x1
    // 0xe688d4: ldur            x1, [fp, #-0x18]
    // 0xe688d8: r0 = _paintImage()
    //     0xe688d8: bl              #0xe68900  ; [package:pdf/src/widgets/image.dart] ::_paintImage
    // 0xe688dc: r0 = Null
    //     0xe688dc: mov             x0, NULL
    // 0xe688e0: LeaveFrame
    //     0xe688e0: mov             SP, fp
    //     0xe688e4: ldp             fp, lr, [SP], #0x10
    // 0xe688e8: ret
    //     0xe688e8: ret             
    // 0xe688ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe688ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe688f0: b               #0xe68878
    // 0xe688f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe688f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe688f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe688f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe688fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe688fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ layout(/* No info */) {
    // ** addr: 0xea0468, size: 0x2b0
    // 0xea0468: EnterFrame
    //     0xea0468: stp             fp, lr, [SP, #-0x10]!
    //     0xea046c: mov             fp, SP
    // 0xea0470: AllocStack(0x48)
    //     0xea0470: sub             SP, SP, #0x48
    // 0xea0474: d0 = inf
    //     0xea0474: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xea0478: mov             x0, x2
    // 0xea047c: mov             x2, x3
    // 0xea0480: stur            x3, [fp, #-0x10]
    // 0xea0484: mov             x3, x1
    // 0xea0488: stur            x1, [fp, #-8]
    // 0xea048c: CheckStackOverflow
    //     0xea048c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea0490: cmp             SP, x16
    //     0xea0494: b.ls            #0xea06fc
    // 0xea0498: LoadField: d1 = r2->field_f
    //     0xea0498: ldur            d1, [x2, #0xf]
    // 0xea049c: fcmp            d0, d1
    // 0xea04a0: b.gt            #0xea0514
    // 0xea04a4: LoadField: r0 = r3->field_b
    //     0xea04a4: ldur            w0, [x3, #0xb]
    // 0xea04a8: DecompressPointer r0
    //     0xea04a8: add             x0, x0, HEAP, lsl #32
    // 0xea04ac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xea04ac: ldur            w1, [x0, #0x17]
    // 0xea04b0: DecompressPointer r1
    //     0xea04b0: add             x1, x1, HEAP, lsl #32
    // 0xea04b4: LoadField: r4 = r1->field_7
    //     0xea04b4: ldur            x4, [x1, #7]
    // 0xea04b8: cmp             x4, #4
    // 0xea04bc: b.lt            #0xea04dc
    // 0xea04c0: LoadField: r4 = r0->field_f
    //     0xea04c0: ldur            x4, [x0, #0xf]
    // 0xea04c4: r0 = BoxInt64Instr(r4)
    //     0xea04c4: sbfiz           x0, x4, #1, #0x1f
    //     0xea04c8: cmp             x4, x0, asr #1
    //     0xea04cc: b.eq            #0xea04d8
    //     0xea04d0: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xea04d4: stur            x4, [x0, #7]
    // 0xea04d8: b               #0xea04e8
    // 0xea04dc: LoadField: r1 = r0->field_b
    //     0xea04dc: ldur            w1, [x0, #0xb]
    // 0xea04e0: DecompressPointer r1
    //     0xea04e0: add             x1, x1, HEAP, lsl #32
    // 0xea04e4: mov             x0, x1
    // 0xea04e8: cmp             w0, NULL
    // 0xea04ec: b.eq            #0xea0704
    // 0xea04f0: stp             x0, NULL, [SP]
    // 0xea04f4: r0 = _Double.fromInteger()
    //     0xea04f4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xea04f8: str             x0, [SP]
    // 0xea04fc: ldur            x1, [fp, #-0x10]
    // 0xea0500: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xea0500: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xea0504: r0 = constrainWidth()
    //     0xea0504: bl              #0xe8e794  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainWidth
    // 0xea0508: mov             v1.16b, v0.16b
    // 0xea050c: ldur            x2, [fp, #-0x10]
    // 0xea0510: d0 = inf
    //     0xea0510: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xea0514: stur            d1, [fp, #-0x28]
    // 0xea0518: LoadField: d2 = r2->field_1f
    //     0xea0518: ldur            d2, [x2, #0x1f]
    // 0xea051c: fcmp            d0, d2
    // 0xea0520: b.le            #0xea052c
    // 0xea0524: mov             v0.16b, v2.16b
    // 0xea0528: b               #0xea0594
    // 0xea052c: ldur            x3, [fp, #-8]
    // 0xea0530: LoadField: r0 = r3->field_b
    //     0xea0530: ldur            w0, [x3, #0xb]
    // 0xea0534: DecompressPointer r0
    //     0xea0534: add             x0, x0, HEAP, lsl #32
    // 0xea0538: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xea0538: ldur            w1, [x0, #0x17]
    // 0xea053c: DecompressPointer r1
    //     0xea053c: add             x1, x1, HEAP, lsl #32
    // 0xea0540: LoadField: r4 = r1->field_7
    //     0xea0540: ldur            x4, [x1, #7]
    // 0xea0544: cmp             x4, #4
    // 0xea0548: b.ge            #0xea0568
    // 0xea054c: LoadField: r4 = r0->field_f
    //     0xea054c: ldur            x4, [x0, #0xf]
    // 0xea0550: r0 = BoxInt64Instr(r4)
    //     0xea0550: sbfiz           x0, x4, #1, #0x1f
    //     0xea0554: cmp             x4, x0, asr #1
    //     0xea0558: b.eq            #0xea0564
    //     0xea055c: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xea0560: stur            x4, [x0, #7]
    // 0xea0564: b               #0xea0574
    // 0xea0568: LoadField: r1 = r0->field_b
    //     0xea0568: ldur            w1, [x0, #0xb]
    // 0xea056c: DecompressPointer r1
    //     0xea056c: add             x1, x1, HEAP, lsl #32
    // 0xea0570: mov             x0, x1
    // 0xea0574: cmp             w0, NULL
    // 0xea0578: b.eq            #0xea0708
    // 0xea057c: stp             x0, NULL, [SP]
    // 0xea0580: r0 = _Double.fromInteger()
    //     0xea0580: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xea0584: str             x0, [SP]
    // 0xea0588: ldur            x1, [fp, #-0x10]
    // 0xea058c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xea058c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xea0590: r0 = constrainHeight()
    //     0xea0590: bl              #0xe8e66c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainHeight
    // 0xea0594: ldur            x2, [fp, #-8]
    // 0xea0598: stur            d0, [fp, #-0x30]
    // 0xea059c: LoadField: r3 = r2->field_b
    //     0xea059c: ldur            w3, [x2, #0xb]
    // 0xea05a0: DecompressPointer r3
    //     0xea05a0: add             x3, x3, HEAP, lsl #32
    // 0xea05a4: stur            x3, [fp, #-0x10]
    // 0xea05a8: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xea05a8: ldur            w0, [x3, #0x17]
    // 0xea05ac: DecompressPointer r0
    //     0xea05ac: add             x0, x0, HEAP, lsl #32
    // 0xea05b0: LoadField: r4 = r0->field_7
    //     0xea05b0: ldur            x4, [x0, #7]
    // 0xea05b4: stur            x4, [fp, #-0x18]
    // 0xea05b8: cmp             x4, #4
    // 0xea05bc: b.lt            #0xea05dc
    // 0xea05c0: LoadField: r5 = r3->field_f
    //     0xea05c0: ldur            x5, [x3, #0xf]
    // 0xea05c4: r0 = BoxInt64Instr(r5)
    //     0xea05c4: sbfiz           x0, x5, #1, #0x1f
    //     0xea05c8: cmp             x5, x0, asr #1
    //     0xea05cc: b.eq            #0xea05d8
    //     0xea05d0: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xea05d4: stur            x5, [x0, #7]
    // 0xea05d8: b               #0xea05e4
    // 0xea05dc: LoadField: r0 = r3->field_b
    //     0xea05dc: ldur            w0, [x3, #0xb]
    // 0xea05e0: DecompressPointer r0
    //     0xea05e0: add             x0, x0, HEAP, lsl #32
    // 0xea05e4: cmp             w0, NULL
    // 0xea05e8: b.eq            #0xea070c
    // 0xea05ec: stp             x0, NULL, [SP]
    // 0xea05f0: r0 = _Double.fromInteger()
    //     0xea05f0: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xea05f4: mov             x2, x0
    // 0xea05f8: ldur            x0, [fp, #-0x18]
    // 0xea05fc: stur            x2, [fp, #-0x20]
    // 0xea0600: cmp             x0, #4
    // 0xea0604: b.ge            #0xea062c
    // 0xea0608: ldur            x0, [fp, #-0x10]
    // 0xea060c: LoadField: r3 = r0->field_f
    //     0xea060c: ldur            x3, [x0, #0xf]
    // 0xea0610: r0 = BoxInt64Instr(r3)
    //     0xea0610: sbfiz           x0, x3, #1, #0x1f
    //     0xea0614: cmp             x3, x0, asr #1
    //     0xea0618: b.eq            #0xea0624
    //     0xea061c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea0620: stur            x3, [x0, #7]
    // 0xea0624: mov             x1, x0
    // 0xea0628: b               #0xea0638
    // 0xea062c: ldur            x0, [fp, #-0x10]
    // 0xea0630: LoadField: r1 = r0->field_b
    //     0xea0630: ldur            w1, [x0, #0xb]
    // 0xea0634: DecompressPointer r1
    //     0xea0634: add             x1, x1, HEAP, lsl #32
    // 0xea0638: ldur            x0, [fp, #-8]
    // 0xea063c: ldur            d0, [fp, #-0x30]
    // 0xea0640: ldur            d1, [fp, #-0x28]
    // 0xea0644: cmp             w1, NULL
    // 0xea0648: b.eq            #0xea0710
    // 0xea064c: stp             x1, NULL, [SP]
    // 0xea0650: r0 = _Double.fromInteger()
    //     0xea0650: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xea0654: mov             x1, x0
    // 0xea0658: ldur            x0, [fp, #-0x20]
    // 0xea065c: stur            x1, [fp, #-0x10]
    // 0xea0660: LoadField: d0 = r0->field_7
    //     0xea0660: ldur            d0, [x0, #7]
    // 0xea0664: stur            d0, [fp, #-0x38]
    // 0xea0668: r0 = PdfPoint()
    //     0xea0668: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xea066c: ldur            d0, [fp, #-0x38]
    // 0xea0670: stur            x0, [fp, #-0x20]
    // 0xea0674: StoreField: r0->field_7 = d0
    //     0xea0674: stur            d0, [x0, #7]
    // 0xea0678: ldur            x1, [fp, #-0x10]
    // 0xea067c: LoadField: d0 = r1->field_7
    //     0xea067c: ldur            d0, [x1, #7]
    // 0xea0680: StoreField: r0->field_f = d0
    //     0xea0680: stur            d0, [x0, #0xf]
    // 0xea0684: r0 = PdfPoint()
    //     0xea0684: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xea0688: ldur            d0, [fp, #-0x28]
    // 0xea068c: StoreField: r0->field_7 = d0
    //     0xea068c: stur            d0, [x0, #7]
    // 0xea0690: ldur            d0, [fp, #-0x30]
    // 0xea0694: StoreField: r0->field_f = d0
    //     0xea0694: stur            d0, [x0, #0xf]
    // 0xea0698: ldur            x2, [fp, #-0x20]
    // 0xea069c: mov             x3, x0
    // 0xea06a0: r1 = Instance_BoxFit
    //     0xea06a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e340] Obj!BoxFit@e2ea01
    //     0xea06a4: ldr             x1, [x1, #0x340]
    // 0xea06a8: r0 = applyBoxFit()
    //     0xea06a8: bl              #0xe68f18  ; [package:pdf/src/widgets/geometry.dart] ::applyBoxFit
    // 0xea06ac: LoadField: r3 = r0->field_b
    //     0xea06ac: ldur            w3, [x0, #0xb]
    // 0xea06b0: DecompressPointer r3
    //     0xea06b0: add             x3, x3, HEAP, lsl #32
    // 0xea06b4: cmp             w3, NULL
    // 0xea06b8: b.eq            #0xea0714
    // 0xea06bc: r1 = Null
    //     0xea06bc: mov             x1, NULL
    // 0xea06c0: r2 = Instance_PdfPoint
    //     0xea06c0: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xea06c4: ldr             x2, [x2, #0x730]
    // 0xea06c8: r0 = PdfRect.fromPoints()
    //     0xea06c8: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xea06cc: ldur            x1, [fp, #-8]
    // 0xea06d0: StoreField: r1->field_7 = r0
    //     0xea06d0: stur            w0, [x1, #7]
    //     0xea06d4: ldurb           w16, [x1, #-1]
    //     0xea06d8: ldurb           w17, [x0, #-1]
    //     0xea06dc: and             x16, x17, x16, lsr #2
    //     0xea06e0: tst             x16, HEAP, lsr #32
    //     0xea06e4: b.eq            #0xea06ec
    //     0xea06e8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xea06ec: r0 = Null
    //     0xea06ec: mov             x0, NULL
    // 0xea06f0: LeaveFrame
    //     0xea06f0: mov             SP, fp
    //     0xea06f4: ldp             fp, lr, [SP], #0x10
    // 0xea06f8: ret
    //     0xea06f8: ret             
    // 0xea06fc: r0 = StackOverflowSharedWithFPURegs()
    //     0xea06fc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xea0700: b               #0xea0498
    // 0xea0704: r0 = NullCastErrorSharedWithFPURegs()
    //     0xea0704: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xea0708: r0 = NullCastErrorSharedWithFPURegs()
    //     0xea0708: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xea070c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xea070c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xea0710: r0 = NullCastErrorSharedWithFPURegs()
    //     0xea0710: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xea0714: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea0714: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
