// lib: , url: package:pdf/src/widgets/image_provider.dart

// class id: 1050854, size: 0x8
class :: {
}

// class id: 785, size: 0x20, field offset: 0x8
abstract class ImageProvider extends Object {

  _ resolve(/* No info */) {
    // ** addr: 0xe692b4, size: 0x130
    // 0xe692b4: EnterFrame
    //     0xe692b4: stp             fp, lr, [SP, #-0x10]!
    //     0xe692b8: mov             fp, SP
    // 0xe692bc: AllocStack(0x18)
    //     0xe692bc: sub             SP, SP, #0x18
    // 0xe692c0: SetupParameters(ImageProvider this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xe692c0: mov             x3, x1
    //     0xe692c4: mov             x0, x2
    //     0xe692c8: stur            x1, [fp, #-0x10]
    //     0xe692cc: stur            x2, [fp, #-0x18]
    // 0xe692d0: CheckStackOverflow
    //     0xe692d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe692d4: cmp             SP, x16
    //     0xe692d8: b.ls            #0xe693d4
    // 0xe692dc: LoadField: r4 = r3->field_1b
    //     0xe692dc: ldur            w4, [x3, #0x1b]
    // 0xe692e0: DecompressPointer r4
    //     0xe692e0: add             x4, x4, HEAP, lsl #32
    // 0xe692e4: mov             x1, x4
    // 0xe692e8: stur            x4, [fp, #-8]
    // 0xe692ec: r2 = 0
    //     0xe692ec: movz            x2, #0
    // 0xe692f0: r0 = _getValueOrData()
    //     0xe692f0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xe692f4: mov             x1, x0
    // 0xe692f8: ldur            x0, [fp, #-8]
    // 0xe692fc: LoadField: r2 = r0->field_f
    //     0xe692fc: ldur            w2, [x0, #0xf]
    // 0xe69300: DecompressPointer r2
    //     0xe69300: add             x2, x2, HEAP, lsl #32
    // 0xe69304: cmp             w2, w1
    // 0xe69308: b.eq            #0xe69314
    // 0xe6930c: cmp             w1, NULL
    // 0xe69310: b.ne            #0xe69330
    // 0xe69314: ldur            x1, [fp, #-0x10]
    // 0xe69318: ldur            x2, [fp, #-0x18]
    // 0xe6931c: r0 = buildImage()
    //     0xe6931c: bl              #0xe693e4  ; [package:pdf/src/widgets/image_provider.dart] MemoryImage::buildImage
    // 0xe69320: ldur            x1, [fp, #-8]
    // 0xe69324: mov             x3, x0
    // 0xe69328: r2 = 0
    //     0xe69328: movz            x2, #0
    // 0xe6932c: r0 = []=()
    //     0xe6932c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe69330: ldur            x0, [fp, #-8]
    // 0xe69334: mov             x1, x0
    // 0xe69338: r2 = 0
    //     0xe69338: movz            x2, #0
    // 0xe6933c: r0 = _getValueOrData()
    //     0xe6933c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xe69340: mov             x1, x0
    // 0xe69344: ldur            x0, [fp, #-8]
    // 0xe69348: LoadField: r2 = r0->field_f
    //     0xe69348: ldur            w2, [x0, #0xf]
    // 0xe6934c: DecompressPointer r2
    //     0xe6934c: add             x2, x2, HEAP, lsl #32
    // 0xe69350: cmp             w2, w1
    // 0xe69354: b.ne            #0xe6935c
    // 0xe69358: r1 = Null
    //     0xe69358: mov             x1, NULL
    // 0xe6935c: ldur            x2, [fp, #-0x18]
    // 0xe69360: cmp             w1, NULL
    // 0xe69364: b.eq            #0xe693dc
    // 0xe69368: LoadField: r3 = r1->field_23
    //     0xe69368: ldur            w3, [x1, #0x23]
    // 0xe6936c: DecompressPointer r3
    //     0xe6936c: add             x3, x3, HEAP, lsl #32
    // 0xe69370: LoadField: r1 = r2->field_13
    //     0xe69370: ldur            w1, [x2, #0x13]
    // 0xe69374: DecompressPointer r1
    //     0xe69374: add             x1, x1, HEAP, lsl #32
    // 0xe69378: cmp             w3, w1
    // 0xe6937c: b.eq            #0xe69398
    // 0xe69380: ldur            x1, [fp, #-0x10]
    // 0xe69384: r0 = buildImage()
    //     0xe69384: bl              #0xe693e4  ; [package:pdf/src/widgets/image_provider.dart] MemoryImage::buildImage
    // 0xe69388: ldur            x1, [fp, #-8]
    // 0xe6938c: mov             x3, x0
    // 0xe69390: r2 = 0
    //     0xe69390: movz            x2, #0
    // 0xe69394: r0 = []=()
    //     0xe69394: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe69398: ldur            x0, [fp, #-8]
    // 0xe6939c: mov             x1, x0
    // 0xe693a0: r2 = 0
    //     0xe693a0: movz            x2, #0
    // 0xe693a4: r0 = _getValueOrData()
    //     0xe693a4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xe693a8: ldur            x1, [fp, #-8]
    // 0xe693ac: LoadField: r2 = r1->field_f
    //     0xe693ac: ldur            w2, [x1, #0xf]
    // 0xe693b0: DecompressPointer r2
    //     0xe693b0: add             x2, x2, HEAP, lsl #32
    // 0xe693b4: cmp             w2, w0
    // 0xe693b8: b.ne            #0xe693c0
    // 0xe693bc: r0 = Null
    //     0xe693bc: mov             x0, NULL
    // 0xe693c0: cmp             w0, NULL
    // 0xe693c4: b.eq            #0xe693e0
    // 0xe693c8: LeaveFrame
    //     0xe693c8: mov             SP, fp
    //     0xe693cc: ldp             fp, lr, [SP], #0x10
    // 0xe693d0: ret
    //     0xe693d0: ret             
    // 0xe693d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe693d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe693d8: b               #0xe692dc
    // 0xe693dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe693dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe693e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe693e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ width(/* No info */) {
    // ** addr: 0xea0718, size: 0x50
    // 0xea0718: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xea0718: ldur            w2, [x1, #0x17]
    // 0xea071c: DecompressPointer r2
    //     0xea071c: add             x2, x2, HEAP, lsl #32
    // 0xea0720: LoadField: r3 = r2->field_7
    //     0xea0720: ldur            x3, [x2, #7]
    // 0xea0724: cmp             x3, #4
    // 0xea0728: b.lt            #0xea0758
    // 0xea072c: LoadField: r2 = r1->field_f
    //     0xea072c: ldur            x2, [x1, #0xf]
    // 0xea0730: r0 = BoxInt64Instr(r2)
    //     0xea0730: sbfiz           x0, x2, #1, #0x1f
    //     0xea0734: cmp             x2, x0, asr #1
    //     0xea0738: b.eq            #0xea0754
    //     0xea073c: stp             fp, lr, [SP, #-0x10]!
    //     0xea0740: mov             fp, SP
    //     0xea0744: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea0748: mov             SP, fp
    //     0xea074c: ldp             fp, lr, [SP], #0x10
    //     0xea0750: stur            x2, [x0, #7]
    // 0xea0754: b               #0xea0764
    // 0xea0758: LoadField: r2 = r1->field_b
    //     0xea0758: ldur            w2, [x1, #0xb]
    // 0xea075c: DecompressPointer r2
    //     0xea075c: add             x2, x2, HEAP, lsl #32
    // 0xea0760: mov             x0, x2
    // 0xea0764: ret
    //     0xea0764: ret             
  }
}

// class id: 786, size: 0x24, field offset: 0x20
class MemoryImage extends ImageProvider {

  _ buildImage(/* No info */) {
    // ** addr: 0xe693e4, size: 0x48
    // 0xe693e4: EnterFrame
    //     0xe693e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe693e8: mov             fp, SP
    // 0xe693ec: CheckStackOverflow
    //     0xe693ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe693f0: cmp             SP, x16
    //     0xe693f4: b.ls            #0xe69424
    // 0xe693f8: LoadField: r0 = r2->field_13
    //     0xe693f8: ldur            w0, [x2, #0x13]
    // 0xe693fc: DecompressPointer r0
    //     0xe693fc: add             x0, x0, HEAP, lsl #32
    // 0xe69400: LoadField: r3 = r1->field_1f
    //     0xe69400: ldur            w3, [x1, #0x1f]
    // 0xe69404: DecompressPointer r3
    //     0xe69404: add             x3, x3, HEAP, lsl #32
    // 0xe69408: mov             x2, x0
    // 0xe6940c: r1 = <PdfDict<PdfDataType>>
    //     0xe6940c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe69410: ldr             x1, [x1, #0x758]
    // 0xe69414: r0 = PdfImage.file()
    //     0xe69414: bl              #0xe6942c  ; [package:pdf/src/pdf/obj/image.dart] PdfImage::PdfImage.file
    // 0xe69418: LeaveFrame
    //     0xe69418: mov             SP, fp
    //     0xe6941c: ldp             fp, lr, [SP], #0x10
    // 0xe69420: ret
    //     0xe69420: ret             
    // 0xe69424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe69424: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe69428: b               #0xe693f8
  }
  factory _ MemoryImage(/* No info */) {
    // ** addr: 0xe92cac, size: 0x2c4
    // 0xe92cac: EnterFrame
    //     0xe92cac: stp             fp, lr, [SP, #-0x10]!
    //     0xe92cb0: mov             fp, SP
    // 0xe92cb4: AllocStack(0x40)
    //     0xe92cb4: sub             SP, SP, #0x40
    // 0xe92cb8: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xe92cb8: mov             x0, x2
    //     0xe92cbc: stur            x2, [fp, #-8]
    // 0xe92cc0: CheckStackOverflow
    //     0xe92cc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe92cc4: cmp             SP, x16
    //     0xe92cc8: b.ls            #0xe92f68
    // 0xe92ccc: mov             x1, x0
    // 0xe92cd0: r0 = findDecoderForData()
    //     0xe92cd0: bl              #0xe69eb0  ; [package:image/src/formats/formats.dart] ::findDecoderForData
    // 0xe92cd4: cmp             w0, NULL
    // 0xe92cd8: b.eq            #0xe92eec
    // 0xe92cdc: r1 = LoadClassIdInstr(r0)
    //     0xe92cdc: ldur            x1, [x0, #-1]
    //     0xe92ce0: ubfx            x1, x1, #0xc, #0x14
    // 0xe92ce4: cmp             x1, #0x5c7
    // 0xe92ce8: b.ne            #0xe92dbc
    // 0xe92cec: ldur            x0, [fp, #-8]
    // 0xe92cf0: mov             x2, x0
    // 0xe92cf4: r1 = Null
    //     0xe92cf4: mov             x1, NULL
    // 0xe92cf8: r0 = PdfJpegInfo()
    //     0xe92cf8: bl              #0xe6ab28  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::PdfJpegInfo
    // 0xe92cfc: LoadField: r2 = r0->field_7
    //     0xe92cfc: ldur            w2, [x0, #7]
    // 0xe92d00: DecompressPointer r2
    //     0xe92d00: add             x2, x2, HEAP, lsl #32
    // 0xe92d04: stur            x2, [fp, #-0x18]
    // 0xe92d08: LoadField: r3 = r0->field_b
    //     0xe92d08: ldur            x3, [x0, #0xb]
    // 0xe92d0c: mov             x1, x0
    // 0xe92d10: stur            x3, [fp, #-0x10]
    // 0xe92d14: r0 = orientation()
    //     0xe92d14: bl              #0xc341e4  ; [package:pdf/src/pdf/exif.dart] PdfJpegInfo::orientation
    // 0xe92d18: stur            x0, [fp, #-0x20]
    // 0xe92d1c: r0 = MemoryImage()
    //     0xe92d1c: bl              #0xe92f70  ; AllocateMemoryImageStub -> MemoryImage (size=0x24)
    // 0xe92d20: ldur            x3, [fp, #-8]
    // 0xe92d24: stur            x0, [fp, #-0x28]
    // 0xe92d28: StoreField: r0->field_1f = r3
    //     0xe92d28: stur            w3, [x0, #0x1f]
    // 0xe92d2c: r16 = <int, PdfImage>
    //     0xe92d2c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e580] TypeArguments: <int, PdfImage>
    //     0xe92d30: ldr             x16, [x16, #0x580]
    // 0xe92d34: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe92d38: stp             lr, x16, [SP]
    // 0xe92d3c: r0 = Map._fromLiteral()
    //     0xe92d3c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe92d40: ldur            x1, [fp, #-0x28]
    // 0xe92d44: StoreField: r1->field_1b = r0
    //     0xe92d44: stur            w0, [x1, #0x1b]
    //     0xe92d48: ldurb           w16, [x1, #-1]
    //     0xe92d4c: ldurb           w17, [x0, #-1]
    //     0xe92d50: and             x16, x17, x16, lsr #2
    //     0xe92d54: tst             x16, HEAP, lsr #32
    //     0xe92d58: b.eq            #0xe92d60
    //     0xe92d5c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe92d60: ldur            x0, [fp, #-0x18]
    // 0xe92d64: StoreField: r1->field_b = r0
    //     0xe92d64: stur            w0, [x1, #0xb]
    //     0xe92d68: tbz             w0, #0, #0xe92d84
    //     0xe92d6c: ldurb           w16, [x1, #-1]
    //     0xe92d70: ldurb           w17, [x0, #-1]
    //     0xe92d74: and             x16, x17, x16, lsr #2
    //     0xe92d78: tst             x16, HEAP, lsr #32
    //     0xe92d7c: b.eq            #0xe92d84
    //     0xe92d80: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe92d84: ldur            x0, [fp, #-0x10]
    // 0xe92d88: StoreField: r1->field_f = r0
    //     0xe92d88: stur            x0, [x1, #0xf]
    // 0xe92d8c: ldur            x0, [fp, #-0x20]
    // 0xe92d90: ArrayStore: r1[0] = r0  ; List_4
    //     0xe92d90: stur            w0, [x1, #0x17]
    //     0xe92d94: ldurb           w16, [x1, #-1]
    //     0xe92d98: ldurb           w17, [x0, #-1]
    //     0xe92d9c: and             x16, x17, x16, lsr #2
    //     0xe92da0: tst             x16, HEAP, lsr #32
    //     0xe92da4: b.eq            #0xe92dac
    //     0xe92da8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe92dac: mov             x0, x1
    // 0xe92db0: LeaveFrame
    //     0xe92db0: mov             SP, fp
    //     0xe92db4: ldp             fp, lr, [SP], #0x10
    // 0xe92db8: ret
    //     0xe92db8: ret             
    // 0xe92dbc: ldur            x3, [fp, #-8]
    // 0xe92dc0: r1 = LoadClassIdInstr(r0)
    //     0xe92dc0: ldur            x1, [x0, #-1]
    //     0xe92dc4: ubfx            x1, x1, #0xc, #0x14
    // 0xe92dc8: mov             x16, x0
    // 0xe92dcc: mov             x0, x1
    // 0xe92dd0: mov             x1, x16
    // 0xe92dd4: mov             x2, x3
    // 0xe92dd8: r0 = GDT[cid_x0 + 0x2383]()
    //     0xe92dd8: movz            x17, #0x2383
    //     0xe92ddc: add             lr, x0, x17
    //     0xe92de0: ldr             lr, [x21, lr, lsl #3]
    //     0xe92de4: blr             lr
    // 0xe92de8: mov             x2, x0
    // 0xe92dec: stur            x2, [fp, #-0x18]
    // 0xe92df0: cmp             w2, NULL
    // 0xe92df4: b.eq            #0xe92f48
    // 0xe92df8: ldur            x3, [fp, #-8]
    // 0xe92dfc: r0 = LoadClassIdInstr(r2)
    //     0xe92dfc: ldur            x0, [x2, #-1]
    //     0xe92e00: ubfx            x0, x0, #0xc, #0x14
    // 0xe92e04: mov             x1, x2
    // 0xe92e08: r0 = GDT[cid_x0 + 0x12914]()
    //     0xe92e08: movz            x17, #0x2914
    //     0xe92e0c: movk            x17, #0x1, lsl #16
    //     0xe92e10: add             lr, x0, x17
    //     0xe92e14: ldr             lr, [x21, lr, lsl #3]
    //     0xe92e18: blr             lr
    // 0xe92e1c: mov             x2, x0
    // 0xe92e20: ldur            x1, [fp, #-0x18]
    // 0xe92e24: stur            x2, [fp, #-0x10]
    // 0xe92e28: r0 = LoadClassIdInstr(r1)
    //     0xe92e28: ldur            x0, [x1, #-1]
    //     0xe92e2c: ubfx            x0, x0, #0xc, #0x14
    // 0xe92e30: r0 = GDT[cid_x0 + 0x12a07]()
    //     0xe92e30: movz            x17, #0x2a07
    //     0xe92e34: movk            x17, #0x1, lsl #16
    //     0xe92e38: add             lr, x0, x17
    //     0xe92e3c: ldr             lr, [x21, lr, lsl #3]
    //     0xe92e40: blr             lr
    // 0xe92e44: stur            x0, [fp, #-0x30]
    // 0xe92e48: r0 = MemoryImage()
    //     0xe92e48: bl              #0xe92f70  ; AllocateMemoryImageStub -> MemoryImage (size=0x24)
    // 0xe92e4c: mov             x1, x0
    // 0xe92e50: ldur            x0, [fp, #-8]
    // 0xe92e54: stur            x1, [fp, #-0x18]
    // 0xe92e58: StoreField: r1->field_1f = r0
    //     0xe92e58: stur            w0, [x1, #0x1f]
    // 0xe92e5c: r16 = <int, PdfImage>
    //     0xe92e5c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e580] TypeArguments: <int, PdfImage>
    //     0xe92e60: ldr             x16, [x16, #0x580]
    // 0xe92e64: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe92e68: stp             lr, x16, [SP]
    // 0xe92e6c: r0 = Map._fromLiteral()
    //     0xe92e6c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe92e70: ldur            x2, [fp, #-0x18]
    // 0xe92e74: StoreField: r2->field_1b = r0
    //     0xe92e74: stur            w0, [x2, #0x1b]
    //     0xe92e78: ldurb           w16, [x2, #-1]
    //     0xe92e7c: ldurb           w17, [x0, #-1]
    //     0xe92e80: and             x16, x17, x16, lsr #2
    //     0xe92e84: tst             x16, HEAP, lsr #32
    //     0xe92e88: b.eq            #0xe92e90
    //     0xe92e8c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe92e90: ldur            x3, [fp, #-0x10]
    // 0xe92e94: r0 = BoxInt64Instr(r3)
    //     0xe92e94: sbfiz           x0, x3, #1, #0x1f
    //     0xe92e98: cmp             x3, x0, asr #1
    //     0xe92e9c: b.eq            #0xe92ea8
    //     0xe92ea0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe92ea4: stur            x3, [x0, #7]
    // 0xe92ea8: StoreField: r2->field_b = r0
    //     0xe92ea8: stur            w0, [x2, #0xb]
    //     0xe92eac: tbz             w0, #0, #0xe92ec8
    //     0xe92eb0: ldurb           w16, [x2, #-1]
    //     0xe92eb4: ldurb           w17, [x0, #-1]
    //     0xe92eb8: and             x16, x17, x16, lsr #2
    //     0xe92ebc: tst             x16, HEAP, lsr #32
    //     0xe92ec0: b.eq            #0xe92ec8
    //     0xe92ec4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe92ec8: ldur            x0, [fp, #-0x30]
    // 0xe92ecc: StoreField: r2->field_f = r0
    //     0xe92ecc: stur            x0, [x2, #0xf]
    // 0xe92ed0: r0 = Instance_PdfImageOrientation
    //     0xe92ed0: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e588] Obj!PdfImageOrientation@e2ed01
    //     0xe92ed4: ldr             x0, [x0, #0x588]
    // 0xe92ed8: ArrayStore: r2[0] = r0  ; List_4
    //     0xe92ed8: stur            w0, [x2, #0x17]
    // 0xe92edc: mov             x0, x2
    // 0xe92ee0: LeaveFrame
    //     0xe92ee0: mov             SP, fp
    //     0xe92ee4: ldp             fp, lr, [SP], #0x10
    // 0xe92ee8: ret
    //     0xe92ee8: ret             
    // 0xe92eec: ldur            x0, [fp, #-8]
    // 0xe92ef0: r1 = Null
    //     0xe92ef0: mov             x1, NULL
    // 0xe92ef4: r2 = 6
    //     0xe92ef4: movz            x2, #0x6
    // 0xe92ef8: r0 = AllocateArray()
    //     0xe92ef8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe92efc: r16 = "Unable to guess the image type "
    //     0xe92efc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e590] "Unable to guess the image type "
    //     0xe92f00: ldr             x16, [x16, #0x590]
    // 0xe92f04: StoreField: r0->field_f = r16
    //     0xe92f04: stur            w16, [x0, #0xf]
    // 0xe92f08: ldur            x1, [fp, #-8]
    // 0xe92f0c: LoadField: r2 = r1->field_13
    //     0xe92f0c: ldur            w2, [x1, #0x13]
    // 0xe92f10: StoreField: r0->field_13 = r2
    //     0xe92f10: stur            w2, [x0, #0x13]
    // 0xe92f14: r16 = " bytes"
    //     0xe92f14: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e598] " bytes"
    //     0xe92f18: ldr             x16, [x16, #0x598]
    // 0xe92f1c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe92f1c: stur            w16, [x0, #0x17]
    // 0xe92f20: str             x0, [SP]
    // 0xe92f24: r0 = _interpolate()
    //     0xe92f24: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe92f28: stur            x0, [fp, #-8]
    // 0xe92f2c: r0 = _Exception()
    //     0xe92f2c: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xe92f30: mov             x1, x0
    // 0xe92f34: ldur            x0, [fp, #-8]
    // 0xe92f38: StoreField: r1->field_7 = r0
    //     0xe92f38: stur            w0, [x1, #7]
    // 0xe92f3c: mov             x0, x1
    // 0xe92f40: r0 = Throw()
    //     0xe92f40: bl              #0xec04b8  ; ThrowStub
    // 0xe92f44: brk             #0
    // 0xe92f48: r0 = _Exception()
    //     0xe92f48: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xe92f4c: mov             x1, x0
    // 0xe92f50: r0 = "Unable decode the image"
    //     0xe92f50: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e5a0] "Unable decode the image"
    //     0xe92f54: ldr             x0, [x0, #0x5a0]
    // 0xe92f58: StoreField: r1->field_7 = r0
    //     0xe92f58: stur            w0, [x1, #7]
    // 0xe92f5c: mov             x0, x1
    // 0xe92f60: r0 = Throw()
    //     0xe92f60: bl              #0xec04b8  ; ThrowStub
    // 0xe92f64: brk             #0
    // 0xe92f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe92f68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe92f6c: b               #0xe92ccc
  }
}
