// lib: , url: package:pdf/src/widgets/multi_page.dart

// class id: 1050855, size: 0x8
class :: {
}

// class id: 782, size: 0x28, field offset: 0x10
class MultiPage extends Page {

  _ MultiPage(/* No info */) {
    // ** addr: 0xb0f4c0, size: 0x148
    // 0xb0f4c0: EnterFrame
    //     0xb0f4c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb0f4c4: mov             fp, SP
    // 0xb0f4c8: AllocStack(0x28)
    //     0xb0f4c8: sub             SP, SP, #0x28
    // 0xb0f4cc: SetupParameters(MultiPage this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0xb0f4cc: mov             x0, x5
    //     0xb0f4d0: stur            x5, [fp, #-0x20]
    //     0xb0f4d4: mov             x5, x1
    //     0xb0f4d8: mov             x4, x2
    //     0xb0f4dc: stur            x1, [fp, #-8]
    //     0xb0f4e0: stur            x2, [fp, #-0x10]
    //     0xb0f4e4: stur            x3, [fp, #-0x18]
    //     0xb0f4e8: stur            x6, [fp, #-0x28]
    // 0xb0f4ec: CheckStackOverflow
    //     0xb0f4ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0f4f0: cmp             SP, x16
    //     0xb0f4f4: b.ls            #0xb0f600
    // 0xb0f4f8: r1 = <_MultiPageInstance>
    //     0xb0f4f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4d8] TypeArguments: <_MultiPageInstance>
    //     0xb0f4fc: ldr             x1, [x1, #0x4d8]
    // 0xb0f500: r2 = 0
    //     0xb0f500: movz            x2, #0
    // 0xb0f504: r0 = _GrowableList()
    //     0xb0f504: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb0f508: ldur            x1, [fp, #-8]
    // 0xb0f50c: StoreField: r1->field_23 = r0
    //     0xb0f50c: stur            w0, [x1, #0x23]
    //     0xb0f510: ldurb           w16, [x1, #-1]
    //     0xb0f514: ldurb           w17, [x0, #-1]
    //     0xb0f518: and             x16, x17, x16, lsr #2
    //     0xb0f51c: tst             x16, HEAP, lsr #32
    //     0xb0f520: b.eq            #0xb0f528
    //     0xb0f524: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb0f528: r0 = Instance_MainAxisAlignment
    //     0xb0f528: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e310] Obj!MainAxisAlignment@e2e901
    //     0xb0f52c: ldr             x0, [x0, #0x310]
    // 0xb0f530: StoreField: r1->field_1f = r0
    //     0xb0f530: stur            w0, [x1, #0x1f]
    // 0xb0f534: r0 = Instance_CrossAxisAlignment
    //     0xb0f534: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e4e0] Obj!CrossAxisAlignment@e2e8a1
    //     0xb0f538: ldr             x0, [x0, #0x4e0]
    // 0xb0f53c: StoreField: r1->field_13 = r0
    //     0xb0f53c: stur            w0, [x1, #0x13]
    // 0xb0f540: ldur            x0, [fp, #-0x20]
    // 0xb0f544: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0f544: stur            w0, [x1, #0x17]
    //     0xb0f548: ldurb           w16, [x1, #-1]
    //     0xb0f54c: ldurb           w17, [x0, #-1]
    //     0xb0f550: and             x16, x17, x16, lsr #2
    //     0xb0f554: tst             x16, HEAP, lsr #32
    //     0xb0f558: b.eq            #0xb0f560
    //     0xb0f55c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb0f560: ldur            x0, [fp, #-0x18]
    // 0xb0f564: StoreField: r1->field_1b = r0
    //     0xb0f564: stur            w0, [x1, #0x1b]
    //     0xb0f568: ldurb           w16, [x1, #-1]
    //     0xb0f56c: ldurb           w17, [x0, #-1]
    //     0xb0f570: and             x16, x17, x16, lsr #2
    //     0xb0f574: tst             x16, HEAP, lsr #32
    //     0xb0f578: b.eq            #0xb0f580
    //     0xb0f57c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb0f580: ldur            x0, [fp, #-0x10]
    // 0xb0f584: StoreField: r1->field_f = r0
    //     0xb0f584: stur            w0, [x1, #0xf]
    //     0xb0f588: ldurb           w16, [x1, #-1]
    //     0xb0f58c: ldurb           w17, [x0, #-1]
    //     0xb0f590: and             x16, x17, x16, lsr #2
    //     0xb0f594: tst             x16, HEAP, lsr #32
    //     0xb0f598: b.eq            #0xb0f5a0
    //     0xb0f59c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb0f5a0: r0 = PageTheme()
    //     0xb0f5a0: bl              #0xb0f628  ; AllocatePageThemeStub -> PageTheme (size=0x24)
    // 0xb0f5a4: ldur            x1, [fp, #-0x28]
    // 0xb0f5a8: StoreField: r0->field_1b = r1
    //     0xb0f5a8: stur            w1, [x0, #0x1b]
    // 0xb0f5ac: r1 = Instance_PdfPageFormat
    //     0xb0f5ac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4e8] Obj!PdfPageFormat@e0c711
    //     0xb0f5b0: ldr             x1, [x1, #0x4e8]
    // 0xb0f5b4: StoreField: r0->field_7 = r1
    //     0xb0f5b4: stur            w1, [x0, #7]
    // 0xb0f5b8: r1 = Instance_PageOrientation
    //     0xb0f5b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4f0] Obj!PageOrientation@e2e6a1
    //     0xb0f5bc: ldr             x1, [x1, #0x4f0]
    // 0xb0f5c0: StoreField: r0->field_b = r1
    //     0xb0f5c0: stur            w1, [x0, #0xb]
    // 0xb0f5c4: r1 = Instance_EdgeInsets
    //     0xb0f5c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4f8] Obj!EdgeInsets@e0c4a1
    //     0xb0f5c8: ldr             x1, [x1, #0x4f8]
    // 0xb0f5cc: StoreField: r0->field_f = r1
    //     0xb0f5cc: stur            w1, [x0, #0xf]
    // 0xb0f5d0: ldur            x1, [fp, #-8]
    // 0xb0f5d4: StoreField: r1->field_7 = r0
    //     0xb0f5d4: stur            w0, [x1, #7]
    //     0xb0f5d8: ldurb           w16, [x1, #-1]
    //     0xb0f5dc: ldurb           w17, [x0, #-1]
    //     0xb0f5e0: and             x16, x17, x16, lsr #2
    //     0xb0f5e4: tst             x16, HEAP, lsr #32
    //     0xb0f5e8: b.eq            #0xb0f5f0
    //     0xb0f5ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb0f5f0: r0 = Null
    //     0xb0f5f0: mov             x0, NULL
    // 0xb0f5f4: LeaveFrame
    //     0xb0f5f4: mov             SP, fp
    //     0xb0f5f8: ldp             fp, lr, [SP], #0x10
    // 0xb0f5fc: ret
    //     0xb0f5fc: ret             
    // 0xb0f600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0f600: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0f604: b               #0xb0f4f8
  }
  _ postProcess(/* No info */) {
    // ** addr: 0xe89028, size: 0x84c
    // 0xe89028: EnterFrame
    //     0xe89028: stp             fp, lr, [SP, #-0x10]!
    //     0xe8902c: mov             fp, SP
    // 0xe89030: AllocStack(0xe8)
    //     0xe89030: sub             SP, SP, #0xe8
    // 0xe89034: SetupParameters(MultiPage this /* r1 => r0, fp-0x8 */)
    //     0xe89034: mov             x0, x1
    //     0xe89038: stur            x1, [fp, #-8]
    // 0xe8903c: CheckStackOverflow
    //     0xe8903c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe89040: cmp             SP, x16
    //     0xe89044: b.ls            #0xe8982c
    // 0xe89048: mov             x1, x0
    // 0xe8904c: r0 = resolvedMargin()
    //     0xe8904c: bl              #0xe89a9c  ; [package:pdf/src/widgets/page.dart] Page::resolvedMargin
    // 0xe89050: ldur            x1, [fp, #-8]
    // 0xe89054: stur            x0, [fp, #-0x10]
    // 0xe89058: r0 = _simpleInstanceOfFalse()
    //     0xe89058: bl              #0xebd578  ; [dart:core] Object::_simpleInstanceOfFalse
    // 0xe8905c: stur            x0, [fp, #-0x18]
    // 0xe89060: tbnz            w0, #4, #0xe89070
    // 0xe89064: d0 = 595.275591
    //     0xe89064: add             x17, PP, #0x33, lsl #12  ; [pp+0x338f8] IMM: double(595.275590551181) from 0x40829a3468d1a346
    //     0xe89068: ldr             d0, [x17, #0x8f8]
    // 0xe8906c: b               #0xe89078
    // 0xe89070: d0 = 841.889764
    //     0xe89070: add             x17, PP, #0x33, lsl #12  ; [pp+0x33900] IMM: double(841.8897637795275) from 0x408a4f1e3c78f1e3
    //     0xe89074: ldr             d0, [x17, #0x900]
    // 0xe89078: stur            d0, [fp, #-0x90]
    // 0xe8907c: tbnz            w0, #4, #0xe8908c
    // 0xe89080: d1 = 841.889764
    //     0xe89080: add             x17, PP, #0x33, lsl #12  ; [pp+0x33900] IMM: double(841.8897637795275) from 0x408a4f1e3c78f1e3
    //     0xe89084: ldr             d1, [x17, #0x900]
    // 0xe89088: b               #0xe89094
    // 0xe8908c: d1 = 595.275591
    //     0xe8908c: add             x17, PP, #0x33, lsl #12  ; [pp+0x338f8] IMM: double(595.275590551181) from 0x40829a3468d1a346
    //     0xe89090: ldr             d1, [x17, #0x8f8]
    // 0xe89094: stur            d1, [fp, #-0x88]
    // 0xe89098: tbnz            w0, #4, #0xe890a8
    // 0xe8909c: ldur            x1, [fp, #-0x10]
    // 0xe890a0: r0 = horizontal()
    //     0xe890a0: bl              #0xe89a80  ; [package:pdf/src/widgets/geometry.dart] EdgeInsetsGeometry::horizontal
    // 0xe890a4: b               #0xe890b0
    // 0xe890a8: ldur            x1, [fp, #-0x10]
    // 0xe890ac: r0 = vertical()
    //     0xe890ac: bl              #0xe89a70  ; [package:pdf/src/widgets/geometry.dart] EdgeInsetsGeometry::vertical
    // 0xe890b0: ldur            x0, [fp, #-0x18]
    // 0xe890b4: stur            d0, [fp, #-0x98]
    // 0xe890b8: tbnz            w0, #4, #0xe890cc
    // 0xe890bc: ldur            x1, [fp, #-0x10]
    // 0xe890c0: r0 = vertical()
    //     0xe890c0: bl              #0xe89a70  ; [package:pdf/src/widgets/geometry.dart] EdgeInsetsGeometry::vertical
    // 0xe890c4: mov             v2.16b, v0.16b
    // 0xe890c8: b               #0xe890d8
    // 0xe890cc: ldur            x1, [fp, #-0x10]
    // 0xe890d0: r0 = horizontal()
    //     0xe890d0: bl              #0xe89a80  ; [package:pdf/src/widgets/geometry.dart] EdgeInsetsGeometry::horizontal
    // 0xe890d4: mov             v2.16b, v0.16b
    // 0xe890d8: ldur            x2, [fp, #-8]
    // 0xe890dc: ldur            x0, [fp, #-0x10]
    // 0xe890e0: ldur            d0, [fp, #-0x88]
    // 0xe890e4: d1 = 2.000000
    //     0xe890e4: fmov            d1, #2.00000000
    // 0xe890e8: fsub            d3, d0, d2
    // 0xe890ec: stur            d3, [fp, #-0xd0]
    // 0xe890f0: LoadField: r3 = r2->field_23
    //     0xe890f0: ldur            w3, [x2, #0x23]
    // 0xe890f4: DecompressPointer r3
    //     0xe890f4: add             x3, x3, HEAP, lsl #32
    // 0xe890f8: stur            x3, [fp, #-0x80]
    // 0xe890fc: LoadField: r1 = r3->field_b
    //     0xe890fc: ldur            w1, [x3, #0xb]
    // 0xe89100: r4 = LoadInt32Instr(r1)
    //     0xe89100: sbfx            x4, x1, #1, #0x1f
    // 0xe89104: stur            x4, [fp, #-0x78]
    // 0xe89108: LoadField: d0 = r0->field_f
    //     0xe89108: ldur            d0, [x0, #0xf]
    // 0xe8910c: stur            d0, [fp, #-0xc8]
    // 0xe89110: LoadField: d2 = r0->field_1f
    //     0xe89110: ldur            d2, [x0, #0x1f]
    // 0xe89114: stur            d2, [fp, #-0xc0]
    // 0xe89118: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xe89118: ldur            w5, [x2, #0x17]
    // 0xe8911c: DecompressPointer r5
    //     0xe8911c: add             x5, x5, HEAP, lsl #32
    // 0xe89120: stur            x5, [fp, #-0x70]
    // 0xe89124: LoadField: d4 = r0->field_7
    //     0xe89124: ldur            d4, [x0, #7]
    // 0xe89128: stur            d4, [fp, #-0xb8]
    // 0xe8912c: LoadField: r6 = r2->field_1b
    //     0xe8912c: ldur            w6, [x2, #0x1b]
    // 0xe89130: DecompressPointer r6
    //     0xe89130: add             x6, x6, HEAP, lsl #32
    // 0xe89134: stur            x6, [fp, #-0x68]
    // 0xe89138: LoadField: r0 = r2->field_1f
    //     0xe89138: ldur            w0, [x2, #0x1f]
    // 0xe8913c: DecompressPointer r0
    //     0xe8913c: add             x0, x0, HEAP, lsl #32
    // 0xe89140: LoadField: r7 = r0->field_7
    //     0xe89140: ldur            x7, [x0, #7]
    // 0xe89144: stur            x7, [fp, #-0x60]
    // 0xe89148: LoadField: r0 = r2->field_13
    //     0xe89148: ldur            w0, [x2, #0x13]
    // 0xe8914c: DecompressPointer r0
    //     0xe8914c: add             x0, x0, HEAP, lsl #32
    // 0xe89150: LoadField: r8 = r0->field_7
    //     0xe89150: ldur            x8, [x0, #7]
    // 0xe89154: stur            x8, [fp, #-0x58]
    // 0xe89158: fdiv            d5, d3, d1
    // 0xe8915c: stur            d5, [fp, #-0xb0]
    // 0xe89160: r0 = 0
    //     0xe89160: movz            x0, #0
    // 0xe89164: ldur            x9, [fp, #-0x18]
    // 0xe89168: ldur            d7, [fp, #-0x90]
    // 0xe8916c: ldur            d6, [fp, #-0x98]
    // 0xe89170: CheckStackOverflow
    //     0xe89170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe89174: cmp             SP, x16
    //     0xe89178: b.ls            #0xe89834
    // 0xe8917c: LoadField: r1 = r3->field_b
    //     0xe8917c: ldur            w1, [x3, #0xb]
    // 0xe89180: r10 = LoadInt32Instr(r1)
    //     0xe89180: sbfx            x10, x1, #1, #0x1f
    // 0xe89184: cmp             x4, x10
    // 0xe89188: b.ne            #0xe8980c
    // 0xe8918c: cmp             x0, x10
    // 0xe89190: b.ge            #0xe897bc
    // 0xe89194: LoadField: r1 = r3->field_f
    //     0xe89194: ldur            w1, [x3, #0xf]
    // 0xe89198: DecompressPointer r1
    //     0xe89198: add             x1, x1, HEAP, lsl #32
    // 0xe8919c: ArrayLoad: r10 = r1[r0]  ; Unknown_4
    //     0xe8919c: add             x16, x1, x0, lsl #2
    //     0xe891a0: ldur            w10, [x16, #0xf]
    // 0xe891a4: DecompressPointer r10
    //     0xe891a4: add             x10, x10, HEAP, lsl #32
    // 0xe891a8: stur            x10, [fp, #-0x50]
    // 0xe891ac: add             x11, x0, #1
    // 0xe891b0: stur            x11, [fp, #-0x48]
    // 0xe891b4: tbnz            w9, #4, #0xe891c0
    // 0xe891b8: fsub            d8, d6, d2
    // 0xe891bc: b               #0xe891c4
    // 0xe891c0: mov             v8.16b, v0.16b
    // 0xe891c4: fsub            d9, d7, d8
    // 0xe891c8: stur            d9, [fp, #-0xa8]
    // 0xe891cc: tbnz            w9, #4, #0xe891d8
    // 0xe891d0: fsub            d8, d6, d4
    // 0xe891d4: b               #0xe891dc
    // 0xe891d8: mov             v8.16b, v2.16b
    // 0xe891dc: stur            d8, [fp, #-0xa0]
    // 0xe891e0: ArrayLoad: r12 = r10[0]  ; List_4
    //     0xe891e0: ldur            w12, [x10, #0x17]
    // 0xe891e4: DecompressPointer r12
    //     0xe891e4: add             x12, x12, HEAP, lsl #32
    // 0xe891e8: stur            x12, [fp, #-0x40]
    // 0xe891ec: LoadField: r0 = r12->field_b
    //     0xe891ec: ldur            w0, [x12, #0xb]
    // 0xe891f0: r13 = LoadInt32Instr(r0)
    //     0xe891f0: sbfx            x13, x0, #1, #0x1f
    // 0xe891f4: stur            x13, [fp, #-0x38]
    // 0xe891f8: LoadField: r14 = r10->field_7
    //     0xe891f8: ldur            w14, [x10, #7]
    // 0xe891fc: DecompressPointer r14
    //     0xe891fc: add             x14, x14, HEAP, lsl #32
    // 0xe89200: stur            x14, [fp, #-0x30]
    // 0xe89204: d10 = 0.000000
    //     0xe89204: eor             v10.16b, v10.16b, v10.16b
    // 0xe89208: r0 = 0
    //     0xe89208: movz            x0, #0
    // 0xe8920c: stur            d10, [fp, #-0x88]
    // 0xe89210: CheckStackOverflow
    //     0xe89210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe89214: cmp             SP, x16
    //     0xe89218: b.ls            #0xe8983c
    // 0xe8921c: LoadField: r1 = r12->field_b
    //     0xe8921c: ldur            w1, [x12, #0xb]
    // 0xe89220: r19 = LoadInt32Instr(r1)
    //     0xe89220: sbfx            x19, x1, #1, #0x1f
    // 0xe89224: cmp             x13, x19
    // 0xe89228: b.ne            #0xe897ec
    // 0xe8922c: cmp             x0, x19
    // 0xe89230: b.ge            #0xe89374
    // 0xe89234: LoadField: r1 = r12->field_f
    //     0xe89234: ldur            w1, [x12, #0xf]
    // 0xe89238: DecompressPointer r1
    //     0xe89238: add             x1, x1, HEAP, lsl #32
    // 0xe8923c: ArrayLoad: r19 = r1[r0]  ; Unknown_4
    //     0xe8923c: add             x16, x1, x0, lsl #2
    //     0xe89240: ldur            w19, [x16, #0xf]
    // 0xe89244: DecompressPointer r19
    //     0xe89244: add             x19, x19, HEAP, lsl #32
    // 0xe89248: stur            x19, [fp, #-0x28]
    // 0xe8924c: add             x20, x0, #1
    // 0xe89250: stur            x20, [fp, #-0x20]
    // 0xe89254: LoadField: r23 = r19->field_7
    //     0xe89254: ldur            w23, [x19, #7]
    // 0xe89258: DecompressPointer r23
    //     0xe89258: add             x23, x23, HEAP, lsl #32
    // 0xe8925c: stur            x23, [fp, #-0x10]
    // 0xe89260: r0 = LoadClassIdInstr(r23)
    //     0xe89260: ldur            x0, [x23, #-1]
    //     0xe89264: ubfx            x0, x0, #0xc, #0x14
    // 0xe89268: sub             x16, x0, #0x31a
    // 0xe8926c: cmp             x16, #0xf
    // 0xe89270: b.hi            #0xe892c0
    // 0xe89274: r0 = LoadClassIdInstr(r23)
    //     0xe89274: ldur            x0, [x23, #-1]
    //     0xe89278: ubfx            x0, x0, #0xc, #0x14
    // 0xe8927c: mov             x1, x23
    // 0xe89280: r0 = GDT[cid_x0 + -0xf00]()
    //     0xe89280: sub             lr, x0, #0xf00
    //     0xe89284: ldr             lr, [x21, lr, lsl #3]
    //     0xe89288: blr             lr
    // 0xe8928c: tbnz            w0, #4, #0xe892c0
    // 0xe89290: ldur            x4, [fp, #-0x10]
    // 0xe89294: ldur            x3, [fp, #-0x28]
    // 0xe89298: LoadField: r2 = r3->field_f
    //     0xe89298: ldur            w2, [x3, #0xf]
    // 0xe8929c: DecompressPointer r2
    //     0xe8929c: add             x2, x2, HEAP, lsl #32
    // 0xe892a0: cmp             w2, NULL
    // 0xe892a4: b.eq            #0xe89844
    // 0xe892a8: r0 = LoadClassIdInstr(r4)
    //     0xe892a8: ldur            x0, [x4, #-1]
    //     0xe892ac: ubfx            x0, x0, #0xc, #0x14
    // 0xe892b0: mov             x1, x4
    // 0xe892b4: r0 = GDT[cid_x0 + -0xd5b]()
    //     0xe892b4: sub             lr, x0, #0xd5b
    //     0xe892b8: ldr             lr, [x21, lr, lsl #3]
    //     0xe892bc: blr             lr
    // 0xe892c0: ldur            d0, [fp, #-0x88]
    // 0xe892c4: ldur            x4, [fp, #-0x10]
    // 0xe892c8: ldur            x0, [fp, #-0x28]
    // 0xe892cc: LoadField: r3 = r0->field_b
    //     0xe892cc: ldur            w3, [x0, #0xb]
    // 0xe892d0: DecompressPointer r3
    //     0xe892d0: add             x3, x3, HEAP, lsl #32
    // 0xe892d4: r0 = LoadClassIdInstr(r4)
    //     0xe892d4: ldur            x0, [x4, #-1]
    //     0xe892d8: ubfx            x0, x0, #0xc, #0x14
    // 0xe892dc: mov             x1, x4
    // 0xe892e0: ldur            x2, [fp, #-0x30]
    // 0xe892e4: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe892e4: sub             lr, x0, #0xf89
    //     0xe892e8: ldr             lr, [x21, lr, lsl #3]
    //     0xe892ec: blr             lr
    // 0xe892f0: ldur            x0, [fp, #-0x10]
    // 0xe892f4: LoadField: r1 = r0->field_7
    //     0xe892f4: ldur            w1, [x0, #7]
    // 0xe892f8: DecompressPointer r1
    //     0xe892f8: add             x1, x1, HEAP, lsl #32
    // 0xe892fc: cmp             w1, NULL
    // 0xe89300: b.eq            #0xe89848
    // 0xe89304: LoadField: d0 = r1->field_1f
    //     0xe89304: ldur            d0, [x1, #0x1f]
    // 0xe89308: ldur            d1, [fp, #-0x88]
    // 0xe8930c: fadd            d10, d1, d0
    // 0xe89310: ldur            x0, [fp, #-0x20]
    // 0xe89314: ldur            x2, [fp, #-8]
    // 0xe89318: ldur            x9, [fp, #-0x18]
    // 0xe8931c: ldur            d7, [fp, #-0x90]
    // 0xe89320: ldur            d6, [fp, #-0x98]
    // 0xe89324: ldur            d3, [fp, #-0xd0]
    // 0xe89328: ldur            x3, [fp, #-0x80]
    // 0xe8932c: ldur            d0, [fp, #-0xc8]
    // 0xe89330: ldur            d9, [fp, #-0xa8]
    // 0xe89334: ldur            d2, [fp, #-0xc0]
    // 0xe89338: ldur            d8, [fp, #-0xa0]
    // 0xe8933c: ldur            x12, [fp, #-0x40]
    // 0xe89340: ldur            x5, [fp, #-0x70]
    // 0xe89344: ldur            d4, [fp, #-0xb8]
    // 0xe89348: ldur            x6, [fp, #-0x68]
    // 0xe8934c: ldur            x8, [fp, #-0x58]
    // 0xe89350: ldur            d5, [fp, #-0xb0]
    // 0xe89354: ldur            x7, [fp, #-0x60]
    // 0xe89358: ldur            x14, [fp, #-0x30]
    // 0xe8935c: ldur            x11, [fp, #-0x48]
    // 0xe89360: ldur            x4, [fp, #-0x78]
    // 0xe89364: ldur            x13, [fp, #-0x38]
    // 0xe89368: ldur            x10, [fp, #-0x50]
    // 0xe8936c: d1 = 2.000000
    //     0xe8936c: fmov            d1, #2.00000000
    // 0xe89370: b               #0xe8920c
    // 0xe89374: mov             v0.16b, v9.16b
    // 0xe89378: mov             v2.16b, v8.16b
    // 0xe8937c: mov             v1.16b, v10.16b
    // 0xe89380: mov             x1, x5
    // 0xe89384: mov             x2, x6
    // 0xe89388: mov             x3, x10
    // 0xe8938c: cmp             w1, NULL
    // 0xe89390: b.eq            #0xe8984c
    // 0xe89394: ldur            x16, [fp, #-0x30]
    // 0xe89398: stp             x16, x1, [SP]
    // 0xe8939c: mov             x0, x1
    // 0xe893a0: ClosureCall
    //     0xe893a0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe893a4: ldur            x2, [x0, #0x1f]
    //     0xe893a8: blr             x2
    // 0xe893ac: mov             x4, x0
    // 0xe893b0: ldur            x0, [fp, #-0x50]
    // 0xe893b4: stur            x4, [fp, #-0x28]
    // 0xe893b8: LoadField: r5 = r0->field_b
    //     0xe893b8: ldur            w5, [x0, #0xb]
    // 0xe893bc: DecompressPointer r5
    //     0xe893bc: add             x5, x5, HEAP, lsl #32
    // 0xe893c0: mov             x1, x4
    // 0xe893c4: ldur            x2, [fp, #-0x30]
    // 0xe893c8: mov             x3, x5
    // 0xe893cc: stur            x5, [fp, #-0x10]
    // 0xe893d0: r0 = layout()
    //     0xe893d0: bl              #0xe8e3d8  ; [package:pdf/src/widgets/basic.dart] Padding::layout
    // 0xe893d4: ldur            x3, [fp, #-0x28]
    // 0xe893d8: LoadField: r0 = r3->field_7
    //     0xe893d8: ldur            w0, [x3, #7]
    // 0xe893dc: DecompressPointer r0
    //     0xe893dc: add             x0, x0, HEAP, lsl #32
    // 0xe893e0: cmp             w0, NULL
    // 0xe893e4: b.eq            #0xe89850
    // 0xe893e8: LoadField: d0 = r0->field_1f
    //     0xe893e8: ldur            d0, [x0, #0x1f]
    // 0xe893ec: ldur            d1, [fp, #-0xa8]
    // 0xe893f0: fsub            d2, d1, d0
    // 0xe893f4: ldur            x0, [fp, #-0x50]
    // 0xe893f8: stur            d2, [fp, #-0xd8]
    // 0xe893fc: LoadField: d1 = r0->field_f
    //     0xe893fc: ldur            d1, [x0, #0xf]
    // 0xe89400: fsub            d3, d1, d0
    // 0xe89404: ldur            x1, [fp, #-8]
    // 0xe89408: ldur            x2, [fp, #-0x30]
    // 0xe8940c: ldur            d0, [fp, #-0xb8]
    // 0xe89410: mov             v1.16b, v3.16b
    // 0xe89414: r0 = _paintChild()
    //     0xe89414: bl              #0xe89874  ; [package:pdf/src/widgets/multi_page.dart] MultiPage::_paintChild
    // 0xe89418: ldur            x1, [fp, #-0x68]
    // 0xe8941c: cmp             w1, NULL
    // 0xe89420: b.eq            #0xe89854
    // 0xe89424: ldur            x16, [fp, #-0x30]
    // 0xe89428: stp             x16, x1, [SP]
    // 0xe8942c: mov             x0, x1
    // 0xe89430: ClosureCall
    //     0xe89430: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe89434: ldur            x2, [x0, #0x1f]
    //     0xe89438: blr             x2
    // 0xe8943c: mov             x1, x0
    // 0xe89440: ldur            x2, [fp, #-0x30]
    // 0xe89444: ldur            x3, [fp, #-0x10]
    // 0xe89448: stur            x0, [fp, #-0x10]
    // 0xe8944c: r0 = layout()
    //     0xe8944c: bl              #0xe8e3d8  ; [package:pdf/src/widgets/basic.dart] Padding::layout
    // 0xe89450: ldur            x3, [fp, #-0x10]
    // 0xe89454: LoadField: r0 = r3->field_7
    //     0xe89454: ldur            w0, [x3, #7]
    // 0xe89458: DecompressPointer r0
    //     0xe89458: add             x0, x0, HEAP, lsl #32
    // 0xe8945c: cmp             w0, NULL
    // 0xe89460: b.eq            #0xe89858
    // 0xe89464: LoadField: d0 = r0->field_1f
    //     0xe89464: ldur            d0, [x0, #0x1f]
    // 0xe89468: ldur            d1, [fp, #-0xa0]
    // 0xe8946c: fadd            d2, d1, d0
    // 0xe89470: ldur            x1, [fp, #-8]
    // 0xe89474: ldur            x2, [fp, #-0x30]
    // 0xe89478: ldur            d0, [fp, #-0xb8]
    // 0xe8947c: ldur            d1, [fp, #-0xc0]
    // 0xe89480: stur            d2, [fp, #-0xa8]
    // 0xe89484: r0 = _paintChild()
    //     0xe89484: bl              #0xe89874  ; [package:pdf/src/widgets/multi_page.dart] MultiPage::_paintChild
    // 0xe89488: ldur            d1, [fp, #-0xd8]
    // 0xe8948c: ldur            d0, [fp, #-0xa8]
    // 0xe89490: fsub            d2, d1, d0
    // 0xe89494: ldur            d0, [fp, #-0x88]
    // 0xe89498: fsub            d3, d2, d0
    // 0xe8949c: d0 = 0.000000
    //     0xe8949c: eor             v0.16b, v0.16b, v0.16b
    // 0xe894a0: fcmp            d0, d3
    // 0xe894a4: b.le            #0xe894b0
    // 0xe894a8: d2 = 0.000000
    //     0xe894a8: eor             v2.16b, v2.16b, v2.16b
    // 0xe894ac: b               #0xe894e4
    // 0xe894b0: fcmp            d3, d0
    // 0xe894b4: b.le            #0xe894c0
    // 0xe894b8: mov             v2.16b, v3.16b
    // 0xe894bc: b               #0xe894e4
    // 0xe894c0: fcmp            d0, d0
    // 0xe894c4: b.ne            #0xe894d0
    // 0xe894c8: fadd            d2, d3, d0
    // 0xe894cc: b               #0xe894e4
    // 0xe894d0: fcmp            d3, d3
    // 0xe894d4: b.vc            #0xe894e0
    // 0xe894d8: mov             v2.16b, v3.16b
    // 0xe894dc: b               #0xe894e4
    // 0xe894e0: d2 = 0.000000
    //     0xe894e0: eor             v2.16b, v2.16b, v2.16b
    // 0xe894e4: ldur            x3, [fp, #-0x40]
    // 0xe894e8: ldur            x2, [fp, #-0x60]
    // 0xe894ec: LoadField: r0 = r3->field_b
    //     0xe894ec: ldur            w0, [x3, #0xb]
    // 0xe894f0: cmp             x2, #2
    // 0xe894f4: b.gt            #0xe89538
    // 0xe894f8: cmp             x2, #1
    // 0xe894fc: b.gt            #0xe89528
    // 0xe89500: cmp             x2, #0
    // 0xe89504: b.gt            #0xe89518
    // 0xe89508: d4 = 0.000000
    //     0xe89508: eor             v4.16b, v4.16b, v4.16b
    // 0xe8950c: d2 = 0.000000
    //     0xe8950c: eor             v2.16b, v2.16b, v2.16b
    // 0xe89510: d3 = 2.000000
    //     0xe89510: fmov            d3, #2.00000000
    // 0xe89514: b               #0xe895cc
    // 0xe89518: mov             v4.16b, v2.16b
    // 0xe8951c: d2 = 0.000000
    //     0xe8951c: eor             v2.16b, v2.16b, v2.16b
    // 0xe89520: d3 = 2.000000
    //     0xe89520: fmov            d3, #2.00000000
    // 0xe89524: b               #0xe895cc
    // 0xe89528: d3 = 2.000000
    //     0xe89528: fmov            d3, #2.00000000
    // 0xe8952c: fdiv            d4, d2, d3
    // 0xe89530: d2 = 0.000000
    //     0xe89530: eor             v2.16b, v2.16b, v2.16b
    // 0xe89534: b               #0xe895cc
    // 0xe89538: d3 = 2.000000
    //     0xe89538: fmov            d3, #2.00000000
    // 0xe8953c: cmp             x2, #4
    // 0xe89540: b.gt            #0xe895a4
    // 0xe89544: cmp             x2, #3
    // 0xe89548: b.gt            #0xe89578
    // 0xe8954c: r1 = LoadInt32Instr(r0)
    //     0xe8954c: sbfx            x1, x0, #1, #0x1f
    // 0xe89550: cmp             x1, #1
    // 0xe89554: b.le            #0xe8956c
    // 0xe89558: sub             x4, x1, #1
    // 0xe8955c: scvtf           d4, x4
    // 0xe89560: fdiv            d5, d2, d4
    // 0xe89564: mov             v2.16b, v5.16b
    // 0xe89568: b               #0xe89570
    // 0xe8956c: d2 = 0.000000
    //     0xe8956c: eor             v2.16b, v2.16b, v2.16b
    // 0xe89570: d4 = 0.000000
    //     0xe89570: eor             v4.16b, v4.16b, v4.16b
    // 0xe89574: b               #0xe895cc
    // 0xe89578: r1 = LoadInt32Instr(r0)
    //     0xe89578: sbfx            x1, x0, #1, #0x1f
    // 0xe8957c: cmp             x1, #0
    // 0xe89580: b.le            #0xe89598
    // 0xe89584: r16 = LoadInt32Instr(r0)
    //     0xe89584: sbfx            x16, x0, #1, #0x1f
    // 0xe89588: scvtf           d4, w16
    // 0xe8958c: fdiv            d5, d2, d4
    // 0xe89590: mov             v2.16b, v5.16b
    // 0xe89594: b               #0xe8959c
    // 0xe89598: d2 = 0.000000
    //     0xe89598: eor             v2.16b, v2.16b, v2.16b
    // 0xe8959c: fdiv            d4, d2, d3
    // 0xe895a0: b               #0xe895cc
    // 0xe895a4: r1 = LoadInt32Instr(r0)
    //     0xe895a4: sbfx            x1, x0, #1, #0x1f
    // 0xe895a8: cmp             x1, #0
    // 0xe895ac: b.le            #0xe895c4
    // 0xe895b0: add             x4, x1, #1
    // 0xe895b4: scvtf           d4, x4
    // 0xe895b8: fdiv            d5, d2, d4
    // 0xe895bc: mov             v2.16b, v5.16b
    // 0xe895c0: b               #0xe895c8
    // 0xe895c4: d2 = 0.000000
    //     0xe895c4: eor             v2.16b, v2.16b, v2.16b
    // 0xe895c8: mov             v4.16b, v2.16b
    // 0xe895cc: stur            d2, [fp, #-0xa8]
    // 0xe895d0: r4 = LoadInt32Instr(r0)
    //     0xe895d0: sbfx            x4, x0, #1, #0x1f
    // 0xe895d4: stur            x4, [fp, #-0x38]
    // 0xe895d8: r0 = 0
    //     0xe895d8: movz            x0, #0
    // 0xe895dc: CheckStackOverflow
    //     0xe895dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe895e0: cmp             SP, x16
    //     0xe895e4: b.ls            #0xe8985c
    // 0xe895e8: cmp             x0, x4
    // 0xe895ec: b.ge            #0xe895fc
    // 0xe895f0: add             x1, x0, #1
    // 0xe895f4: mov             x0, x1
    // 0xe895f8: b               #0xe895dc
    // 0xe895fc: fsub            d5, d1, d4
    // 0xe89600: mov             v6.16b, v5.16b
    // 0xe89604: r0 = 0
    //     0xe89604: movz            x0, #0
    // 0xe89608: ldur            d1, [fp, #-0xd0]
    // 0xe8960c: ldur            d4, [fp, #-0xb8]
    // 0xe89610: ldur            x5, [fp, #-0x58]
    // 0xe89614: ldur            d5, [fp, #-0xb0]
    // 0xe89618: CheckStackOverflow
    //     0xe89618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8961c: cmp             SP, x16
    //     0xe89620: b.ls            #0xe89864
    // 0xe89624: LoadField: r1 = r3->field_b
    //     0xe89624: ldur            w1, [x3, #0xb]
    // 0xe89628: r6 = LoadInt32Instr(r1)
    //     0xe89628: sbfx            x6, x1, #1, #0x1f
    // 0xe8962c: cmp             x4, x6
    // 0xe89630: b.ne            #0xe897cc
    // 0xe89634: cmp             x0, x6
    // 0xe89638: b.ge            #0xe89780
    // 0xe8963c: LoadField: r1 = r3->field_f
    //     0xe8963c: ldur            w1, [x3, #0xf]
    // 0xe89640: DecompressPointer r1
    //     0xe89640: add             x1, x1, HEAP, lsl #32
    // 0xe89644: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0xe89644: add             x16, x1, x0, lsl #2
    //     0xe89648: ldur            w6, [x16, #0xf]
    // 0xe8964c: DecompressPointer r6
    //     0xe8964c: add             x6, x6, HEAP, lsl #32
    // 0xe89650: stur            x6, [fp, #-0x28]
    // 0xe89654: add             x7, x0, #1
    // 0xe89658: stur            x7, [fp, #-0x20]
    // 0xe8965c: LoadField: r8 = r6->field_7
    //     0xe8965c: ldur            w8, [x6, #7]
    // 0xe89660: DecompressPointer r8
    //     0xe89660: add             x8, x8, HEAP, lsl #32
    // 0xe89664: stur            x8, [fp, #-0x10]
    // 0xe89668: LoadField: r0 = r8->field_7
    //     0xe89668: ldur            w0, [x8, #7]
    // 0xe8966c: DecompressPointer r0
    //     0xe8966c: add             x0, x0, HEAP, lsl #32
    // 0xe89670: cmp             w0, NULL
    // 0xe89674: b.eq            #0xe8986c
    // 0xe89678: LoadField: d7 = r0->field_1f
    //     0xe89678: ldur            d7, [x0, #0x1f]
    // 0xe8967c: fsub            d8, d6, d7
    // 0xe89680: stur            d8, [fp, #-0xa0]
    // 0xe89684: cmp             x5, #1
    // 0xe89688: b.gt            #0xe896a4
    // 0xe8968c: cmp             x5, #0
    // 0xe89690: b.le            #0xe896c0
    // 0xe89694: ArrayLoad: d6 = r0[0]  ; List_8
    //     0xe89694: ldur            d6, [x0, #0x17]
    // 0xe89698: fsub            d7, d1, d6
    // 0xe8969c: mov             v6.16b, v7.16b
    // 0xe896a0: b               #0xe896c4
    // 0xe896a4: cmp             x5, #2
    // 0xe896a8: b.gt            #0xe896c0
    // 0xe896ac: ArrayLoad: d6 = r0[0]  ; List_8
    //     0xe896ac: ldur            d6, [x0, #0x17]
    // 0xe896b0: fdiv            d7, d6, d3
    // 0xe896b4: fsub            d9, d5, d7
    // 0xe896b8: mov             v6.16b, v9.16b
    // 0xe896bc: b               #0xe896c4
    // 0xe896c0: d6 = 0.000000
    //     0xe896c0: eor             v6.16b, v6.16b, v6.16b
    // 0xe896c4: stur            d6, [fp, #-0x88]
    // 0xe896c8: r0 = LoadClassIdInstr(r8)
    //     0xe896c8: ldur            x0, [x8, #-1]
    //     0xe896cc: ubfx            x0, x0, #0xc, #0x14
    // 0xe896d0: sub             x16, x0, #0x31a
    // 0xe896d4: cmp             x16, #0xf
    // 0xe896d8: b.hi            #0xe89728
    // 0xe896dc: r0 = LoadClassIdInstr(r8)
    //     0xe896dc: ldur            x0, [x8, #-1]
    //     0xe896e0: ubfx            x0, x0, #0xc, #0x14
    // 0xe896e4: mov             x1, x8
    // 0xe896e8: r0 = GDT[cid_x0 + -0xf00]()
    //     0xe896e8: sub             lr, x0, #0xf00
    //     0xe896ec: ldr             lr, [x21, lr, lsl #3]
    //     0xe896f0: blr             lr
    // 0xe896f4: tbnz            w0, #4, #0xe89728
    // 0xe896f8: ldur            x3, [fp, #-0x10]
    // 0xe896fc: ldur            x0, [fp, #-0x28]
    // 0xe89700: LoadField: r2 = r0->field_f
    //     0xe89700: ldur            w2, [x0, #0xf]
    // 0xe89704: DecompressPointer r2
    //     0xe89704: add             x2, x2, HEAP, lsl #32
    // 0xe89708: cmp             w2, NULL
    // 0xe8970c: b.eq            #0xe89870
    // 0xe89710: r0 = LoadClassIdInstr(r3)
    //     0xe89710: ldur            x0, [x3, #-1]
    //     0xe89714: ubfx            x0, x0, #0xc, #0x14
    // 0xe89718: mov             x1, x3
    // 0xe8971c: r0 = GDT[cid_x0 + -0xd5b]()
    //     0xe8971c: sub             lr, x0, #0xd5b
    //     0xe89720: ldr             lr, [x21, lr, lsl #3]
    //     0xe89724: blr             lr
    // 0xe89728: ldur            d3, [fp, #-0xb8]
    // 0xe8972c: ldur            d4, [fp, #-0xa0]
    // 0xe89730: ldur            d0, [fp, #-0x88]
    // 0xe89734: ldur            d2, [fp, #-0xa8]
    // 0xe89738: fadd            d1, d3, d0
    // 0xe8973c: ldur            x1, [fp, #-8]
    // 0xe89740: ldur            x2, [fp, #-0x30]
    // 0xe89744: ldur            x3, [fp, #-0x10]
    // 0xe89748: mov             v0.16b, v1.16b
    // 0xe8974c: mov             v1.16b, v4.16b
    // 0xe89750: r0 = _paintChild()
    //     0xe89750: bl              #0xe89874  ; [package:pdf/src/widgets/multi_page.dart] MultiPage::_paintChild
    // 0xe89754: ldur            d1, [fp, #-0xa0]
    // 0xe89758: ldur            d0, [fp, #-0xa8]
    // 0xe8975c: fsub            d6, d1, d0
    // 0xe89760: ldur            x0, [fp, #-0x20]
    // 0xe89764: ldur            x3, [fp, #-0x40]
    // 0xe89768: ldur            x2, [fp, #-0x60]
    // 0xe8976c: mov             v2.16b, v0.16b
    // 0xe89770: ldur            x4, [fp, #-0x38]
    // 0xe89774: d3 = 2.000000
    //     0xe89774: fmov            d3, #2.00000000
    // 0xe89778: d0 = 0.000000
    //     0xe89778: eor             v0.16b, v0.16b, v0.16b
    // 0xe8977c: b               #0xe89608
    // 0xe89780: ldur            x0, [fp, #-0x48]
    // 0xe89784: ldur            x2, [fp, #-8]
    // 0xe89788: ldur            d3, [fp, #-0xd0]
    // 0xe8978c: ldur            x3, [fp, #-0x80]
    // 0xe89790: ldur            d0, [fp, #-0xc8]
    // 0xe89794: ldur            d2, [fp, #-0xc0]
    // 0xe89798: ldur            x5, [fp, #-0x70]
    // 0xe8979c: ldur            d4, [fp, #-0xb8]
    // 0xe897a0: ldur            x6, [fp, #-0x68]
    // 0xe897a4: ldur            x8, [fp, #-0x58]
    // 0xe897a8: ldur            d5, [fp, #-0xb0]
    // 0xe897ac: ldur            x7, [fp, #-0x60]
    // 0xe897b0: ldur            x4, [fp, #-0x78]
    // 0xe897b4: d1 = 2.000000
    //     0xe897b4: fmov            d1, #2.00000000
    // 0xe897b8: b               #0xe89164
    // 0xe897bc: r0 = Null
    //     0xe897bc: mov             x0, NULL
    // 0xe897c0: LeaveFrame
    //     0xe897c0: mov             SP, fp
    //     0xe897c4: ldp             fp, lr, [SP], #0x10
    // 0xe897c8: ret
    //     0xe897c8: ret             
    // 0xe897cc: mov             x0, x3
    // 0xe897d0: r0 = ConcurrentModificationError()
    //     0xe897d0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe897d4: mov             x1, x0
    // 0xe897d8: ldur            x0, [fp, #-0x40]
    // 0xe897dc: StoreField: r1->field_b = r0
    //     0xe897dc: stur            w0, [x1, #0xb]
    // 0xe897e0: mov             x0, x1
    // 0xe897e4: r0 = Throw()
    //     0xe897e4: bl              #0xec04b8  ; ThrowStub
    // 0xe897e8: brk             #0
    // 0xe897ec: mov             x0, x12
    // 0xe897f0: r0 = ConcurrentModificationError()
    //     0xe897f0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe897f4: mov             x1, x0
    // 0xe897f8: ldur            x0, [fp, #-0x40]
    // 0xe897fc: StoreField: r1->field_b = r0
    //     0xe897fc: stur            w0, [x1, #0xb]
    // 0xe89800: mov             x0, x1
    // 0xe89804: r0 = Throw()
    //     0xe89804: bl              #0xec04b8  ; ThrowStub
    // 0xe89808: brk             #0
    // 0xe8980c: mov             x0, x3
    // 0xe89810: r0 = ConcurrentModificationError()
    //     0xe89810: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe89814: mov             x1, x0
    // 0xe89818: ldur            x0, [fp, #-0x80]
    // 0xe8981c: StoreField: r1->field_b = r0
    //     0xe8981c: stur            w0, [x1, #0xb]
    // 0xe89820: mov             x0, x1
    // 0xe89824: r0 = Throw()
    //     0xe89824: bl              #0xec04b8  ; ThrowStub
    // 0xe89828: brk             #0
    // 0xe8982c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8982c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe89830: b               #0xe89048
    // 0xe89834: r0 = StackOverflowSharedWithFPURegs()
    //     0xe89834: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe89838: b               #0xe8917c
    // 0xe8983c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8983c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe89840: b               #0xe8921c
    // 0xe89844: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe89844: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe89848: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe89848: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe8984c: r0 = NullErrorSharedWithFPURegs()
    //     0xe8984c: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xe89850: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe89850: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe89854: r0 = NullErrorSharedWithoutFPURegs()
    //     0xe89854: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xe89858: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe89858: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe8985c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8985c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe89860: b               #0xe895e8
    // 0xe89864: r0 = StackOverflowSharedWithFPURegs()
    //     0xe89864: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe89868: b               #0xe89624
    // 0xe8986c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe8986c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe89870: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe89870: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _paintChild(/* No info */) {
    // ** addr: 0xe89874, size: 0x1a8
    // 0xe89874: EnterFrame
    //     0xe89874: stp             fp, lr, [SP, #-0x10]!
    //     0xe89878: mov             fp, SP
    // 0xe8987c: AllocStack(0x38)
    //     0xe8987c: sub             SP, SP, #0x38
    // 0xe89880: SetupParameters(MultiPage this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x30 */, dynamic _ /* d1 => d1, fp-0x38 */)
    //     0xe89880: mov             x0, x3
    //     0xe89884: stur            x3, [fp, #-0x18]
    //     0xe89888: mov             x3, x1
    //     0xe8988c: stur            x1, [fp, #-8]
    //     0xe89890: stur            x2, [fp, #-0x10]
    //     0xe89894: stur            d0, [fp, #-0x30]
    //     0xe89898: stur            d1, [fp, #-0x38]
    // 0xe8989c: CheckStackOverflow
    //     0xe8989c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe898a0: cmp             SP, x16
    //     0xe898a4: b.ls            #0xe89a0c
    // 0xe898a8: mov             x1, x3
    // 0xe898ac: r0 = _simpleInstanceOfFalse()
    //     0xe898ac: bl              #0xebd578  ; [dart:core] Object::_simpleInstanceOfFalse
    // 0xe898b0: tbnz            w0, #4, #0xe899a4
    // 0xe898b4: ldur            x2, [fp, #-0x10]
    // 0xe898b8: ldur            x0, [fp, #-0x18]
    // 0xe898bc: ldur            d0, [fp, #-0x30]
    // 0xe898c0: ldur            d1, [fp, #-0x38]
    // 0xe898c4: ldur            x1, [fp, #-8]
    // 0xe898c8: r0 = resolvedMargin()
    //     0xe898c8: bl              #0xe89a9c  ; [package:pdf/src/widgets/page.dart] Page::resolvedMargin
    // 0xe898cc: ldur            x2, [fp, #-0x10]
    // 0xe898d0: stur            x0, [fp, #-0x20]
    // 0xe898d4: LoadField: r3 = r2->field_b
    //     0xe898d4: ldur            w3, [x2, #0xb]
    // 0xe898d8: DecompressPointer r3
    //     0xe898d8: add             x3, x3, HEAP, lsl #32
    // 0xe898dc: stur            x3, [fp, #-8]
    // 0xe898e0: cmp             w3, NULL
    // 0xe898e4: b.eq            #0xe89a14
    // 0xe898e8: mov             x1, x3
    // 0xe898ec: r0 = saveContext()
    //     0xe898ec: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe898f0: r0 = Matrix4()
    //     0xe898f0: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe898f4: r4 = 32
    //     0xe898f4: movz            x4, #0x20
    // 0xe898f8: stur            x0, [fp, #-0x28]
    // 0xe898fc: r0 = AllocateFloat64Array()
    //     0xe898fc: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe89900: mov             x1, x0
    // 0xe89904: ldur            x0, [fp, #-0x28]
    // 0xe89908: StoreField: r0->field_7 = r1
    //     0xe89908: stur            w1, [x0, #7]
    // 0xe8990c: mov             x1, x0
    // 0xe89910: r0 = setIdentity()
    //     0xe89910: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe89914: ldur            x1, [fp, #-0x28]
    // 0xe89918: d0 = -1.570796
    //     0xe89918: add             x17, PP, #0x36, lsl #12  ; [pp+0x36708] IMM: double(-1.5707963267948966) from 0xbff921fb54442d18
    //     0xe8991c: ldr             d0, [x17, #0x708]
    // 0xe89920: r0 = rotateZ()
    //     0xe89920: bl              #0x7cfea8  ; [package:vector_math/vector_math_64.dart] Matrix4::rotateZ
    // 0xe89924: ldur            d0, [fp, #-0x30]
    // 0xe89928: d1 = 841.889764
    //     0xe89928: add             x17, PP, #0x33, lsl #12  ; [pp+0x33900] IMM: double(841.8897637795275) from 0x408a4f1e3c78f1e3
    //     0xe8992c: ldr             d1, [x17, #0x900]
    // 0xe89930: fsub            d2, d0, d1
    // 0xe89934: ldur            x0, [fp, #-0x20]
    // 0xe89938: LoadField: d0 = r0->field_f
    //     0xe89938: ldur            d0, [x0, #0xf]
    // 0xe8993c: fadd            d1, d2, d0
    // 0xe89940: LoadField: d0 = r0->field_7
    //     0xe89940: ldur            d0, [x0, #7]
    // 0xe89944: fsub            d2, d1, d0
    // 0xe89948: ldur            d1, [fp, #-0x38]
    // 0xe8994c: fadd            d3, d1, d0
    // 0xe89950: LoadField: d0 = r0->field_1f
    //     0xe89950: ldur            d0, [x0, #0x1f]
    // 0xe89954: fsub            d1, d3, d0
    // 0xe89958: ldur            x1, [fp, #-0x28]
    // 0xe8995c: mov             v0.16b, v2.16b
    // 0xe89960: r0 = translate()
    //     0xe89960: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe89964: ldur            x1, [fp, #-8]
    // 0xe89968: ldur            x2, [fp, #-0x28]
    // 0xe8996c: r0 = setTransform()
    //     0xe8996c: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe89970: ldur            x0, [fp, #-0x18]
    // 0xe89974: r1 = LoadClassIdInstr(r0)
    //     0xe89974: ldur            x1, [x0, #-1]
    //     0xe89978: ubfx            x1, x1, #0xc, #0x14
    // 0xe8997c: mov             x16, x0
    // 0xe89980: mov             x0, x1
    // 0xe89984: mov             x1, x16
    // 0xe89988: ldur            x2, [fp, #-0x10]
    // 0xe8998c: r0 = GDT[cid_x0 + -0xe8b]()
    //     0xe8998c: sub             lr, x0, #0xe8b
    //     0xe89990: ldr             lr, [x21, lr, lsl #3]
    //     0xe89994: blr             lr
    // 0xe89998: ldur            x1, [fp, #-8]
    // 0xe8999c: r0 = restoreContext()
    //     0xe8999c: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe899a0: b               #0xe899fc
    // 0xe899a4: ldur            x0, [fp, #-0x18]
    // 0xe899a8: ldur            d0, [fp, #-0x30]
    // 0xe899ac: ldur            d1, [fp, #-0x38]
    // 0xe899b0: LoadField: r1 = r0->field_7
    //     0xe899b0: ldur            w1, [x0, #7]
    // 0xe899b4: DecompressPointer r1
    //     0xe899b4: add             x1, x1, HEAP, lsl #32
    // 0xe899b8: cmp             w1, NULL
    // 0xe899bc: b.eq            #0xe89a18
    // 0xe899c0: r0 = copyWith()
    //     0xe899c0: bl              #0xe89a1c  ; [package:pdf/src/pdf/rect.dart] PdfRect::copyWith
    // 0xe899c4: ldur            x1, [fp, #-0x18]
    // 0xe899c8: StoreField: r1->field_7 = r0
    //     0xe899c8: stur            w0, [x1, #7]
    //     0xe899cc: ldurb           w16, [x1, #-1]
    //     0xe899d0: ldurb           w17, [x0, #-1]
    //     0xe899d4: and             x16, x17, x16, lsr #2
    //     0xe899d8: tst             x16, HEAP, lsr #32
    //     0xe899dc: b.eq            #0xe899e4
    //     0xe899e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe899e4: r0 = LoadClassIdInstr(r1)
    //     0xe899e4: ldur            x0, [x1, #-1]
    //     0xe899e8: ubfx            x0, x0, #0xc, #0x14
    // 0xe899ec: ldur            x2, [fp, #-0x10]
    // 0xe899f0: r0 = GDT[cid_x0 + -0xe8b]()
    //     0xe899f0: sub             lr, x0, #0xe8b
    //     0xe899f4: ldr             lr, [x21, lr, lsl #3]
    //     0xe899f8: blr             lr
    // 0xe899fc: r0 = Null
    //     0xe899fc: mov             x0, NULL
    // 0xe89a00: LeaveFrame
    //     0xe89a00: mov             SP, fp
    //     0xe89a04: ldp             fp, lr, [SP], #0x10
    // 0xe89a08: ret
    //     0xe89a08: ret             
    // 0xe89a0c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe89a0c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe89a10: b               #0xe898a8
    // 0xe89a14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe89a14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe89a18: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe89a18: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ generate(/* No info */) {
    // ** addr: 0xe89c18, size: 0xca4
    // 0xe89c18: EnterFrame
    //     0xe89c18: stp             fp, lr, [SP, #-0x10]!
    //     0xe89c1c: mov             fp, SP
    // 0xe89c20: AllocStack(0xd8)
    //     0xe89c20: sub             SP, SP, #0xd8
    // 0xe89c24: SetupParameters(MultiPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe89c24: stur            x1, [fp, #-8]
    //     0xe89c28: stur            x2, [fp, #-0x10]
    // 0xe89c2c: CheckStackOverflow
    //     0xe89c2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe89c30: cmp             SP, x16
    //     0xe89c34: b.ls            #0xe8a830
    // 0xe89c38: r0 = BoxConstraints()
    //     0xe89c38: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe89c3c: stur            x0, [fp, #-0x28]
    // 0xe89c40: StoreField: r0->field_7 = rZR
    //     0xe89c40: stur            xzr, [x0, #7]
    // 0xe89c44: d0 = 555.275591
    //     0xe89c44: add             x17, PP, #0x36, lsl #12  ; [pp+0x36748] IMM: double(555.275590551181) from 0x40815a3468d1a346
    //     0xe89c48: ldr             d0, [x17, #0x748]
    // 0xe89c4c: StoreField: r0->field_f = d0
    //     0xe89c4c: stur            d0, [x0, #0xf]
    // 0xe89c50: ArrayStore: r0[0] = rZR  ; List_8
    //     0xe89c50: stur            xzr, [x0, #0x17]
    // 0xe89c54: d1 = inf
    //     0xe89c54: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe89c58: StoreField: r0->field_1f = d1
    //     0xe89c58: stur            d1, [x0, #0x1f]
    // 0xe89c5c: ldur            x3, [fp, #-8]
    // 0xe89c60: LoadField: r1 = r3->field_7
    //     0xe89c60: ldur            w1, [x3, #7]
    // 0xe89c64: DecompressPointer r1
    //     0xe89c64: add             x1, x1, HEAP, lsl #32
    // 0xe89c68: LoadField: r4 = r1->field_1b
    //     0xe89c68: ldur            w4, [x1, #0x1b]
    // 0xe89c6c: DecompressPointer r4
    //     0xe89c6c: add             x4, x4, HEAP, lsl #32
    // 0xe89c70: ldur            x1, [fp, #-0x10]
    // 0xe89c74: stur            x4, [fp, #-0x20]
    // 0xe89c78: LoadField: r5 = r1->field_7
    //     0xe89c78: ldur            w5, [x1, #7]
    // 0xe89c7c: DecompressPointer r5
    //     0xe89c7c: add             x5, x5, HEAP, lsl #32
    // 0xe89c80: mov             x2, x5
    // 0xe89c84: stur            x5, [fp, #-0x18]
    // 0xe89c88: r1 = Null
    //     0xe89c88: mov             x1, NULL
    // 0xe89c8c: r0 = Context()
    //     0xe89c8c: bl              #0xe7097c  ; [package:pdf/src/widgets/widget.dart] Context::Context
    // 0xe89c90: r1 = Null
    //     0xe89c90: mov             x1, NULL
    // 0xe89c94: r2 = 2
    //     0xe89c94: movz            x2, #0x2
    // 0xe89c98: stur            x0, [fp, #-0x10]
    // 0xe89c9c: r0 = AllocateArray()
    //     0xe89c9c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe89ca0: mov             x2, x0
    // 0xe89ca4: ldur            x0, [fp, #-0x20]
    // 0xe89ca8: stur            x2, [fp, #-0x30]
    // 0xe89cac: StoreField: r2->field_f = r0
    //     0xe89cac: stur            w0, [x2, #0xf]
    // 0xe89cb0: r1 = <Inherited>
    //     0xe89cb0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36750] TypeArguments: <Inherited>
    //     0xe89cb4: ldr             x1, [x1, #0x750]
    // 0xe89cb8: r0 = AllocateGrowableArray()
    //     0xe89cb8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe89cbc: mov             x1, x0
    // 0xe89cc0: ldur            x0, [fp, #-0x30]
    // 0xe89cc4: StoreField: r1->field_f = r0
    //     0xe89cc4: stur            w0, [x1, #0xf]
    // 0xe89cc8: r0 = 2
    //     0xe89cc8: movz            x0, #0x2
    // 0xe89ccc: StoreField: r1->field_b = r0
    //     0xe89ccc: stur            w0, [x1, #0xb]
    // 0xe89cd0: mov             x2, x1
    // 0xe89cd4: ldur            x1, [fp, #-0x10]
    // 0xe89cd8: r0 = inheritFromAll()
    //     0xe89cd8: bl              #0xe8ae3c  ; [package:pdf/src/widgets/widget.dart] Context::inheritFromAll
    // 0xe89cdc: mov             x2, x0
    // 0xe89ce0: ldur            x1, [fp, #-8]
    // 0xe89ce4: stur            x2, [fp, #-0x10]
    // 0xe89ce8: LoadField: r0 = r1->field_f
    //     0xe89ce8: ldur            w0, [x1, #0xf]
    // 0xe89cec: DecompressPointer r0
    //     0xe89cec: add             x0, x0, HEAP, lsl #32
    // 0xe89cf0: stp             x2, x0, [SP]
    // 0xe89cf4: ClosureCall
    //     0xe89cf4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe89cf8: ldur            x2, [x0, #0x1f]
    //     0xe89cfc: blr             x2
    // 0xe89d00: mov             x2, x0
    // 0xe89d04: ldur            x0, [fp, #-8]
    // 0xe89d08: stur            x2, [fp, #-0x60]
    // 0xe89d0c: LoadField: r3 = r0->field_23
    //     0xe89d0c: ldur            w3, [x0, #0x23]
    // 0xe89d10: DecompressPointer r3
    //     0xe89d10: add             x3, x3, HEAP, lsl #32
    // 0xe89d14: ldur            x1, [fp, #-0x10]
    // 0xe89d18: stur            x3, [fp, #-0x58]
    // 0xe89d1c: LoadField: r4 = r1->field_13
    //     0xe89d1c: ldur            w4, [x1, #0x13]
    // 0xe89d20: DecompressPointer r4
    //     0xe89d20: add             x4, x4, HEAP, lsl #32
    // 0xe89d24: stur            x4, [fp, #-0x50]
    // 0xe89d28: LoadField: r5 = r1->field_f
    //     0xe89d28: ldur            w5, [x1, #0xf]
    // 0xe89d2c: DecompressPointer r5
    //     0xe89d2c: add             x5, x5, HEAP, lsl #32
    // 0xe89d30: stur            x5, [fp, #-0x48]
    // 0xe89d34: r1 = Instance_EdgeInsets
    //     0xe89d34: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4f8] Obj!EdgeInsets@e0c4a1
    //     0xe89d38: ldr             x1, [x1, #0x4f8]
    // 0xe89d3c: LoadField: d0 = r1->field_1f
    //     0xe89d3c: ldur            d0, [x1, #0x1f]
    // 0xe89d40: stur            d0, [fp, #-0xa8]
    // 0xe89d44: LoadField: r6 = r3->field_7
    //     0xe89d44: ldur            w6, [x3, #7]
    // 0xe89d48: DecompressPointer r6
    //     0xe89d48: add             x6, x6, HEAP, lsl #32
    // 0xe89d4c: stur            x6, [fp, #-0x40]
    // 0xe89d50: ArrayLoad: r7 = r0[0]  ; List_4
    //     0xe89d50: ldur            w7, [x0, #0x17]
    // 0xe89d54: DecompressPointer r7
    //     0xe89d54: add             x7, x7, HEAP, lsl #32
    // 0xe89d58: stur            x7, [fp, #-0x30]
    // 0xe89d5c: LoadField: r8 = r0->field_1b
    //     0xe89d5c: ldur            w8, [x0, #0x1b]
    // 0xe89d60: DecompressPointer r8
    //     0xe89d60: add             x8, x8, HEAP, lsl #32
    // 0xe89d64: stur            x8, [fp, #-0x20]
    // 0xe89d68: r11 = Null
    //     0xe89d68: mov             x11, NULL
    // 0xe89d6c: r10 = Null
    //     0xe89d6c: mov             x10, NULL
    // 0xe89d70: d1 = 0.000000
    //     0xe89d70: eor             v1.16b, v1.16b, v1.16b
    // 0xe89d74: r1 = Null
    //     0xe89d74: mov             x1, NULL
    // 0xe89d78: r9 = 0
    //     0xe89d78: movz            x9, #0
    // 0xe89d7c: ldur            x0, [fp, #-0x28]
    // 0xe89d80: stur            x11, [fp, #-0x10]
    // 0xe89d84: stur            x9, [fp, #-0x38]
    // 0xe89d88: CheckStackOverflow
    //     0xe89d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe89d8c: cmp             SP, x16
    //     0xe89d90: b.ls            #0xe8a838
    // 0xe89d94: LoadField: r12 = r2->field_b
    //     0xe89d94: ldur            w12, [x2, #0xb]
    // 0xe89d98: r13 = LoadInt32Instr(r12)
    //     0xe89d98: sbfx            x13, x12, #1, #0x1f
    // 0xe89d9c: cmp             x9, x13
    // 0xe89da0: b.ge            #0xe8a774
    // 0xe89da4: LoadField: r12 = r2->field_f
    //     0xe89da4: ldur            w12, [x2, #0xf]
    // 0xe89da8: DecompressPointer r12
    //     0xe89da8: add             x12, x12, HEAP, lsl #32
    // 0xe89dac: ArrayLoad: r13 = r12[r9]  ; Unknown_4
    //     0xe89dac: add             x16, x12, x9, lsl #2
    //     0xe89db0: ldur            w13, [x16, #0xf]
    // 0xe89db4: DecompressPointer r13
    //     0xe89db4: add             x13, x13, HEAP, lsl #32
    // 0xe89db8: stur            x13, [fp, #-8]
    // 0xe89dbc: cmp             w10, NULL
    // 0xe89dc0: b.ne            #0xe8a14c
    // 0xe89dc4: r1 = <PdfDict<PdfDataType>>
    //     0xe89dc4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe89dc8: ldr             x1, [x1, #0x758]
    // 0xe89dcc: r0 = PdfPage()
    //     0xe89dcc: bl              #0xe8ae30  ; AllocatePdfPageStub -> PdfPage (size=0x5c)
    // 0xe89dd0: mov             x1, x0
    // 0xe89dd4: ldur            x2, [fp, #-0x18]
    // 0xe89dd8: r3 = Null
    //     0xe89dd8: mov             x3, NULL
    // 0xe89ddc: stur            x0, [fp, #-0x68]
    // 0xe89de0: r0 = PdfPage()
    //     0xe89de0: bl              #0xe8aa70  ; [package:pdf/src/pdf/obj/page.dart] PdfPage::PdfPage
    // 0xe89de4: ldur            x2, [fp, #-0x68]
    // 0xe89de8: LoadField: r0 = r2->field_23
    //     0xe89de8: ldur            w0, [x2, #0x23]
    // 0xe89dec: DecompressPointer r0
    //     0xe89dec: add             x0, x0, HEAP, lsl #32
    // 0xe89df0: stur            x0, [fp, #-0x70]
    // 0xe89df4: r1 = <PdfDict<PdfDataType>>
    //     0xe89df4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36758] TypeArguments: <PdfDict<PdfDataType>>
    //     0xe89df8: ldr             x1, [x1, #0x758]
    // 0xe89dfc: r0 = PdfObjectStream()
    //     0xe89dfc: bl              #0xe66040  ; AllocatePdfObjectStreamStub -> PdfObjectStream (size=0x34)
    // 0xe89e00: mov             x1, x0
    // 0xe89e04: ldur            x2, [fp, #-0x70]
    // 0xe89e08: stur            x0, [fp, #-0x70]
    // 0xe89e0c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe89e0c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe89e10: r0 = PdfObjectStream()
    //     0xe89e10: bl              #0xe4714c  ; [package:pdf/src/pdf/obj/object_stream.dart] PdfObjectStream::PdfObjectStream
    // 0xe89e14: ldur            x2, [fp, #-0x70]
    // 0xe89e18: LoadField: r3 = r2->field_2b
    //     0xe89e18: ldur            w3, [x2, #0x2b]
    // 0xe89e1c: DecompressPointer r3
    //     0xe89e1c: add             x3, x3, HEAP, lsl #32
    // 0xe89e20: stur            x3, [fp, #-0x78]
    // 0xe89e24: r0 = PdfGraphics()
    //     0xe89e24: bl              #0xe46f40  ; AllocatePdfGraphicsStub -> PdfGraphics (size=0x18)
    // 0xe89e28: mov             x1, x0
    // 0xe89e2c: ldur            x2, [fp, #-0x68]
    // 0xe89e30: ldur            x3, [fp, #-0x78]
    // 0xe89e34: stur            x0, [fp, #-0x78]
    // 0xe89e38: r0 = PdfGraphics()
    //     0xe89e38: bl              #0xe46df0  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::PdfGraphics
    // 0xe89e3c: ldur            x0, [fp, #-0x68]
    // 0xe89e40: LoadField: r1 = r0->field_57
    //     0xe89e40: ldur            w1, [x0, #0x57]
    // 0xe89e44: DecompressPointer r1
    //     0xe89e44: add             x1, x1, HEAP, lsl #32
    // 0xe89e48: ldur            x2, [fp, #-0x70]
    // 0xe89e4c: ldur            x3, [fp, #-0x78]
    // 0xe89e50: r0 = []=()
    //     0xe89e50: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe89e54: ldur            x3, [fp, #-0x68]
    // 0xe89e58: LoadField: r4 = r3->field_4f
    //     0xe89e58: ldur            w4, [x3, #0x4f]
    // 0xe89e5c: DecompressPointer r4
    //     0xe89e5c: add             x4, x4, HEAP, lsl #32
    // 0xe89e60: stur            x4, [fp, #-0x80]
    // 0xe89e64: LoadField: r2 = r4->field_7
    //     0xe89e64: ldur            w2, [x4, #7]
    // 0xe89e68: DecompressPointer r2
    //     0xe89e68: add             x2, x2, HEAP, lsl #32
    // 0xe89e6c: ldur            x0, [fp, #-0x70]
    // 0xe89e70: r1 = Null
    //     0xe89e70: mov             x1, NULL
    // 0xe89e74: cmp             w2, NULL
    // 0xe89e78: b.eq            #0xe89e98
    // 0xe89e7c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe89e7c: ldur            w4, [x2, #0x17]
    // 0xe89e80: DecompressPointer r4
    //     0xe89e80: add             x4, x4, HEAP, lsl #32
    // 0xe89e84: r8 = X0
    //     0xe89e84: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe89e88: LoadField: r9 = r4->field_7
    //     0xe89e88: ldur            x9, [x4, #7]
    // 0xe89e8c: r3 = Null
    //     0xe89e8c: add             x3, PP, #0x36, lsl #12  ; [pp+0x36760] Null
    //     0xe89e90: ldr             x3, [x3, #0x760]
    // 0xe89e94: blr             x9
    // 0xe89e98: ldur            x0, [fp, #-0x80]
    // 0xe89e9c: LoadField: r1 = r0->field_b
    //     0xe89e9c: ldur            w1, [x0, #0xb]
    // 0xe89ea0: LoadField: r2 = r0->field_f
    //     0xe89ea0: ldur            w2, [x0, #0xf]
    // 0xe89ea4: DecompressPointer r2
    //     0xe89ea4: add             x2, x2, HEAP, lsl #32
    // 0xe89ea8: LoadField: r3 = r2->field_b
    //     0xe89ea8: ldur            w3, [x2, #0xb]
    // 0xe89eac: r2 = LoadInt32Instr(r1)
    //     0xe89eac: sbfx            x2, x1, #1, #0x1f
    // 0xe89eb0: stur            x2, [fp, #-0x88]
    // 0xe89eb4: r1 = LoadInt32Instr(r3)
    //     0xe89eb4: sbfx            x1, x3, #1, #0x1f
    // 0xe89eb8: cmp             x2, x1
    // 0xe89ebc: b.ne            #0xe89ec8
    // 0xe89ec0: mov             x1, x0
    // 0xe89ec4: r0 = _growToNextCapacity()
    //     0xe89ec4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe89ec8: ldur            x8, [fp, #-0x28]
    // 0xe89ecc: ldur            x5, [fp, #-0x58]
    // 0xe89ed0: ldur            x3, [fp, #-0x68]
    // 0xe89ed4: ldur            x4, [fp, #-0x78]
    // 0xe89ed8: ldur            x0, [fp, #-0x80]
    // 0xe89edc: ldur            x6, [fp, #-0x50]
    // 0xe89ee0: ldur            x7, [fp, #-0x48]
    // 0xe89ee4: ldur            x2, [fp, #-0x88]
    // 0xe89ee8: add             x1, x2, #1
    // 0xe89eec: lsl             x9, x1, #1
    // 0xe89ef0: StoreField: r0->field_b = r9
    //     0xe89ef0: stur            w9, [x0, #0xb]
    // 0xe89ef4: LoadField: r1 = r0->field_f
    //     0xe89ef4: ldur            w1, [x0, #0xf]
    // 0xe89ef8: DecompressPointer r1
    //     0xe89ef8: add             x1, x1, HEAP, lsl #32
    // 0xe89efc: ldur            x0, [fp, #-0x70]
    // 0xe89f00: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe89f00: add             x25, x1, x2, lsl #2
    //     0xe89f04: add             x25, x25, #0xf
    //     0xe89f08: str             w0, [x25]
    //     0xe89f0c: tbz             w0, #0, #0xe89f28
    //     0xe89f10: ldurb           w16, [x1, #-1]
    //     0xe89f14: ldurb           w17, [x0, #-1]
    //     0xe89f18: and             x16, x17, x16, lsr #2
    //     0xe89f1c: tst             x16, HEAP, lsr #32
    //     0xe89f20: b.eq            #0xe89f28
    //     0xe89f24: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe89f28: LoadField: r1 = r4->field_13
    //     0xe89f28: ldur            w1, [x4, #0x13]
    // 0xe89f2c: DecompressPointer r1
    //     0xe89f2c: add             x1, x1, HEAP, lsl #32
    // 0xe89f30: r2 = "0 Tr "
    //     0xe89f30: add             x2, PP, #0x36, lsl #12  ; [pp+0x36770] "0 Tr "
    //     0xe89f34: ldr             x2, [x2, #0x770]
    // 0xe89f38: r0 = putString()
    //     0xe89f38: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe89f3c: r0 = Context()
    //     0xe89f3c: bl              #0xe709e0  ; AllocateContextStub -> Context (size=0x18)
    // 0xe89f40: mov             x3, x0
    // 0xe89f44: ldur            x0, [fp, #-0x50]
    // 0xe89f48: stur            x3, [fp, #-0x70]
    // 0xe89f4c: StoreField: r3->field_13 = r0
    //     0xe89f4c: stur            w0, [x3, #0x13]
    // 0xe89f50: ldur            x1, [fp, #-0x68]
    // 0xe89f54: StoreField: r3->field_7 = r1
    //     0xe89f54: stur            w1, [x3, #7]
    // 0xe89f58: ldur            x1, [fp, #-0x78]
    // 0xe89f5c: StoreField: r3->field_b = r1
    //     0xe89f5c: stur            w1, [x3, #0xb]
    // 0xe89f60: ldur            x4, [fp, #-0x48]
    // 0xe89f64: StoreField: r3->field_f = r4
    //     0xe89f64: stur            w4, [x3, #0xf]
    // 0xe89f68: r1 = <_MultiPageWidget>
    //     0xe89f68: add             x1, PP, #0x36, lsl #12  ; [pp+0x36778] TypeArguments: <_MultiPageWidget>
    //     0xe89f6c: ldr             x1, [x1, #0x778]
    // 0xe89f70: r2 = 0
    //     0xe89f70: movz            x2, #0
    // 0xe89f74: r0 = _GrowableList()
    //     0xe89f74: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe89f78: stur            x0, [fp, #-0x68]
    // 0xe89f7c: r0 = _MultiPageInstance()
    //     0xe89f7c: bl              #0xe8aa64  ; Allocate_MultiPageInstanceStub -> _MultiPageInstance (size=0x1c)
    // 0xe89f80: mov             x3, x0
    // 0xe89f84: ldur            x0, [fp, #-0x68]
    // 0xe89f88: stur            x3, [fp, #-0x78]
    // 0xe89f8c: ArrayStore: r3[0] = r0  ; List_4
    //     0xe89f8c: stur            w0, [x3, #0x17]
    // 0xe89f90: ldur            x4, [fp, #-0x70]
    // 0xe89f94: StoreField: r3->field_7 = r4
    //     0xe89f94: stur            w4, [x3, #7]
    // 0xe89f98: ldur            x5, [fp, #-0x28]
    // 0xe89f9c: StoreField: r3->field_b = r5
    //     0xe89f9c: stur            w5, [x3, #0xb]
    // 0xe89fa0: d0 = 821.889764
    //     0xe89fa0: add             x17, PP, #0x36, lsl #12  ; [pp+0x36780] IMM: double(821.8897637795275) from 0x4089af1e3c78f1e3
    //     0xe89fa4: ldr             d0, [x17, #0x780]
    // 0xe89fa8: StoreField: r3->field_f = d0
    //     0xe89fa8: stur            d0, [x3, #0xf]
    // 0xe89fac: mov             x0, x3
    // 0xe89fb0: ldur            x2, [fp, #-0x40]
    // 0xe89fb4: r1 = Null
    //     0xe89fb4: mov             x1, NULL
    // 0xe89fb8: cmp             w2, NULL
    // 0xe89fbc: b.eq            #0xe89fdc
    // 0xe89fc0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe89fc0: ldur            w4, [x2, #0x17]
    // 0xe89fc4: DecompressPointer r4
    //     0xe89fc4: add             x4, x4, HEAP, lsl #32
    // 0xe89fc8: r8 = X0
    //     0xe89fc8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe89fcc: LoadField: r9 = r4->field_7
    //     0xe89fcc: ldur            x9, [x4, #7]
    // 0xe89fd0: r3 = Null
    //     0xe89fd0: add             x3, PP, #0x36, lsl #12  ; [pp+0x36788] Null
    //     0xe89fd4: ldr             x3, [x3, #0x788]
    // 0xe89fd8: blr             x9
    // 0xe89fdc: ldur            x0, [fp, #-0x58]
    // 0xe89fe0: LoadField: r1 = r0->field_b
    //     0xe89fe0: ldur            w1, [x0, #0xb]
    // 0xe89fe4: LoadField: r2 = r0->field_f
    //     0xe89fe4: ldur            w2, [x0, #0xf]
    // 0xe89fe8: DecompressPointer r2
    //     0xe89fe8: add             x2, x2, HEAP, lsl #32
    // 0xe89fec: LoadField: r3 = r2->field_b
    //     0xe89fec: ldur            w3, [x2, #0xb]
    // 0xe89ff0: r2 = LoadInt32Instr(r1)
    //     0xe89ff0: sbfx            x2, x1, #1, #0x1f
    // 0xe89ff4: stur            x2, [fp, #-0x88]
    // 0xe89ff8: r1 = LoadInt32Instr(r3)
    //     0xe89ff8: sbfx            x1, x3, #1, #0x1f
    // 0xe89ffc: cmp             x2, x1
    // 0xe8a000: b.ne            #0xe8a00c
    // 0xe8a004: mov             x1, x0
    // 0xe8a008: r0 = _growToNextCapacity()
    //     0xe8a008: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8a00c: ldur            x2, [fp, #-0x58]
    // 0xe8a010: ldur            d0, [fp, #-0xa8]
    // 0xe8a014: ldur            x4, [fp, #-0x30]
    // 0xe8a018: ldur            x5, [fp, #-0x20]
    // 0xe8a01c: ldur            x3, [fp, #-0x88]
    // 0xe8a020: add             x0, x3, #1
    // 0xe8a024: lsl             x1, x0, #1
    // 0xe8a028: StoreField: r2->field_b = r1
    //     0xe8a028: stur            w1, [x2, #0xb]
    // 0xe8a02c: LoadField: r1 = r2->field_f
    //     0xe8a02c: ldur            w1, [x2, #0xf]
    // 0xe8a030: DecompressPointer r1
    //     0xe8a030: add             x1, x1, HEAP, lsl #32
    // 0xe8a034: ldur            x0, [fp, #-0x78]
    // 0xe8a038: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe8a038: add             x25, x1, x3, lsl #2
    //     0xe8a03c: add             x25, x25, #0xf
    //     0xe8a040: str             w0, [x25]
    //     0xe8a044: tbz             w0, #0, #0xe8a060
    //     0xe8a048: ldurb           w16, [x1, #-1]
    //     0xe8a04c: ldurb           w17, [x0, #-1]
    //     0xe8a050: and             x16, x17, x16, lsr #2
    //     0xe8a054: tst             x16, HEAP, lsr #32
    //     0xe8a058: b.eq            #0xe8a060
    //     0xe8a05c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8a060: cmp             w4, NULL
    // 0xe8a064: b.eq            #0xe8a840
    // 0xe8a068: ldur            x16, [fp, #-0x70]
    // 0xe8a06c: stp             x16, x4, [SP]
    // 0xe8a070: mov             x0, x4
    // 0xe8a074: ClosureCall
    //     0xe8a074: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe8a078: ldur            x2, [x0, #0x1f]
    //     0xe8a07c: blr             x2
    // 0xe8a080: mov             x1, x0
    // 0xe8a084: ldur            x2, [fp, #-0x70]
    // 0xe8a088: ldur            x3, [fp, #-0x28]
    // 0xe8a08c: stur            x0, [fp, #-0x68]
    // 0xe8a090: r0 = layout()
    //     0xe8a090: bl              #0xe8e3d8  ; [package:pdf/src/widgets/basic.dart] Padding::layout
    // 0xe8a094: ldur            x0, [fp, #-0x68]
    // 0xe8a098: LoadField: r1 = r0->field_7
    //     0xe8a098: ldur            w1, [x0, #7]
    // 0xe8a09c: DecompressPointer r1
    //     0xe8a09c: add             x1, x1, HEAP, lsl #32
    // 0xe8a0a0: cmp             w1, NULL
    // 0xe8a0a4: b.eq            #0xe8a844
    // 0xe8a0a8: LoadField: d0 = r1->field_1f
    //     0xe8a0a8: ldur            d0, [x1, #0x1f]
    // 0xe8a0ac: d1 = 821.889764
    //     0xe8a0ac: add             x17, PP, #0x36, lsl #12  ; [pp+0x36780] IMM: double(821.8897637795275) from 0x4089af1e3c78f1e3
    //     0xe8a0b0: ldr             d1, [x17, #0x780]
    // 0xe8a0b4: fsub            d2, d1, d0
    // 0xe8a0b8: ldur            x1, [fp, #-0x20]
    // 0xe8a0bc: stur            d2, [fp, #-0xb0]
    // 0xe8a0c0: cmp             w1, NULL
    // 0xe8a0c4: b.eq            #0xe8a848
    // 0xe8a0c8: ldur            x16, [fp, #-0x70]
    // 0xe8a0cc: stp             x16, x1, [SP]
    // 0xe8a0d0: mov             x0, x1
    // 0xe8a0d4: ClosureCall
    //     0xe8a0d4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe8a0d8: ldur            x2, [x0, #0x1f]
    //     0xe8a0dc: blr             x2
    // 0xe8a0e0: mov             x1, x0
    // 0xe8a0e4: ldur            x2, [fp, #-0x70]
    // 0xe8a0e8: ldur            x3, [fp, #-0x28]
    // 0xe8a0ec: stur            x0, [fp, #-0x68]
    // 0xe8a0f0: r0 = layout()
    //     0xe8a0f0: bl              #0xe8e3d8  ; [package:pdf/src/widgets/basic.dart] Padding::layout
    // 0xe8a0f4: ldur            x0, [fp, #-0x68]
    // 0xe8a0f8: LoadField: r1 = r0->field_7
    //     0xe8a0f8: ldur            w1, [x0, #7]
    // 0xe8a0fc: DecompressPointer r1
    //     0xe8a0fc: add             x1, x1, HEAP, lsl #32
    // 0xe8a100: cmp             w1, NULL
    // 0xe8a104: b.eq            #0xe8a84c
    // 0xe8a108: LoadField: d0 = r1->field_1f
    //     0xe8a108: ldur            d0, [x1, #0x1f]
    // 0xe8a10c: ldur            d2, [fp, #-0xa8]
    // 0xe8a110: fadd            d1, d2, d0
    // 0xe8a114: ldur            d0, [fp, #-0xb0]
    // 0xe8a118: r0 = inline_Allocate_Double()
    //     0xe8a118: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe8a11c: add             x0, x0, #0x10
    //     0xe8a120: cmp             x1, x0
    //     0xe8a124: b.ls            #0xe8a850
    //     0xe8a128: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8a12c: sub             x0, x0, #0xf
    //     0xe8a130: movz            x1, #0xe15c
    //     0xe8a134: movk            x1, #0x3, lsl #16
    //     0xe8a138: stur            x1, [x0, #-1]
    // 0xe8a13c: StoreField: r0->field_7 = d0
    //     0xe8a13c: stur            d0, [x0, #7]
    // 0xe8a140: mov             x4, x0
    // 0xe8a144: ldur            x3, [fp, #-0x70]
    // 0xe8a148: b               #0xe8a158
    // 0xe8a14c: mov             v2.16b, v0.16b
    // 0xe8a150: mov             x4, x1
    // 0xe8a154: mov             x3, x10
    // 0xe8a158: ldur            x2, [fp, #-8]
    // 0xe8a15c: stur            x4, [fp, #-0x68]
    // 0xe8a160: stur            x3, [fp, #-0x70]
    // 0xe8a164: stur            d1, [fp, #-0xb0]
    // 0xe8a168: r5 = 60
    //     0xe8a168: movz            x5, #0x3c
    // 0xe8a16c: branchIfSmi(r2, 0xe8a178)
    //     0xe8a16c: tbz             w2, #0, #0xe8a178
    // 0xe8a170: r5 = LoadClassIdInstr(r2)
    //     0xe8a170: ldur            x5, [x2, #-1]
    //     0xe8a174: ubfx            x5, x5, #0xc, #0x14
    // 0xe8a178: stur            x5, [fp, #-0x88]
    // 0xe8a17c: sub             x16, x5, #0x31a
    // 0xe8a180: cmp             x16, #0xf
    // 0xe8a184: b.hi            #0xe8a1fc
    // 0xe8a188: r0 = LoadClassIdInstr(r2)
    //     0xe8a188: ldur            x0, [x2, #-1]
    //     0xe8a18c: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a190: mov             x1, x2
    // 0xe8a194: r0 = GDT[cid_x0 + -0xf00]()
    //     0xe8a194: sub             lr, x0, #0xf00
    //     0xe8a198: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a19c: blr             lr
    // 0xe8a1a0: tbnz            w0, #4, #0xe8a1f4
    // 0xe8a1a4: ldur            x2, [fp, #-0x10]
    // 0xe8a1a8: cmp             w2, NULL
    // 0xe8a1ac: b.eq            #0xe8a1cc
    // 0xe8a1b0: ldur            x3, [fp, #-8]
    // 0xe8a1b4: r0 = LoadClassIdInstr(r3)
    //     0xe8a1b4: ldur            x0, [x3, #-1]
    //     0xe8a1b8: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a1bc: mov             x1, x3
    // 0xe8a1c0: r0 = GDT[cid_x0 + -0xdd8]()
    //     0xe8a1c0: sub             lr, x0, #0xdd8
    //     0xe8a1c4: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a1c8: blr             lr
    // 0xe8a1cc: ldur            x2, [fp, #-8]
    // 0xe8a1d0: r0 = LoadClassIdInstr(r2)
    //     0xe8a1d0: ldur            x0, [x2, #-1]
    //     0xe8a1d4: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a1d8: mov             x1, x2
    // 0xe8a1dc: r0 = GDT[cid_x0 + -0xc4b]()
    //     0xe8a1dc: sub             lr, x0, #0xc4b
    //     0xe8a1e0: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a1e4: blr             lr
    // 0xe8a1e8: mov             x6, x0
    // 0xe8a1ec: r7 = Null
    //     0xe8a1ec: mov             x7, NULL
    // 0xe8a1f0: b               #0xe8a208
    // 0xe8a1f4: ldur            x2, [fp, #-0x10]
    // 0xe8a1f8: b               #0xe8a200
    // 0xe8a1fc: ldur            x2, [fp, #-0x10]
    // 0xe8a200: mov             x7, x2
    // 0xe8a204: r6 = Null
    //     0xe8a204: mov             x6, NULL
    // 0xe8a208: ldur            x5, [fp, #-0x88]
    // 0xe8a20c: ldur            x4, [fp, #-8]
    // 0xe8a210: stur            x7, [fp, #-0x10]
    // 0xe8a214: stur            x6, [fp, #-0x78]
    // 0xe8a218: r0 = LoadClassIdInstr(r4)
    //     0xe8a218: ldur            x0, [x4, #-1]
    //     0xe8a21c: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a220: mov             x1, x4
    // 0xe8a224: ldur            x2, [fp, #-0x70]
    // 0xe8a228: ldur            x3, [fp, #-0x28]
    // 0xe8a22c: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe8a22c: sub             lr, x0, #0xf89
    //     0xe8a230: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a234: blr             lr
    // 0xe8a238: ldur            x2, [fp, #-0x88]
    // 0xe8a23c: sub             x16, x2, #0x31a
    // 0xe8a240: cmp             x16, #0xf
    // 0xe8a244: b.hi            #0xe8a26c
    // 0xe8a248: ldur            x3, [fp, #-8]
    // 0xe8a24c: r0 = LoadClassIdInstr(r3)
    //     0xe8a24c: ldur            x0, [x3, #-1]
    //     0xe8a250: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a254: mov             x1, x3
    // 0xe8a258: r0 = GDT[cid_x0 + -0xf00]()
    //     0xe8a258: sub             lr, x0, #0xf00
    //     0xe8a25c: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a260: blr             lr
    // 0xe8a264: mov             x2, x0
    // 0xe8a268: b               #0xe8a270
    // 0xe8a26c: r2 = false
    //     0xe8a26c: add             x2, NULL, #0x30  ; false
    // 0xe8a270: ldur            d1, [fp, #-0xb0]
    // 0xe8a274: ldur            x0, [fp, #-0x68]
    // 0xe8a278: ldur            x3, [fp, #-8]
    // 0xe8a27c: cmp             w0, NULL
    // 0xe8a280: b.eq            #0xe8a868
    // 0xe8a284: LoadField: r1 = r3->field_7
    //     0xe8a284: ldur            w1, [x3, #7]
    // 0xe8a288: DecompressPointer r1
    //     0xe8a288: add             x1, x1, HEAP, lsl #32
    // 0xe8a28c: cmp             w1, NULL
    // 0xe8a290: b.eq            #0xe8a86c
    // 0xe8a294: LoadField: d0 = r1->field_1f
    //     0xe8a294: ldur            d0, [x1, #0x1f]
    // 0xe8a298: stur            d0, [fp, #-0xc8]
    // 0xe8a29c: LoadField: d2 = r0->field_7
    //     0xe8a29c: ldur            d2, [x0, #7]
    // 0xe8a2a0: stur            d2, [fp, #-0xb8]
    // 0xe8a2a4: fsub            d3, d2, d0
    // 0xe8a2a8: fcmp            d1, d3
    // 0xe8a2ac: b.le            #0xe8a584
    // 0xe8a2b0: d3 = 801.889764
    //     0xe8a2b0: add             x17, PP, #0x36, lsl #12  ; [pp+0x36798] IMM: double(801.8897637795275) from 0x40890f1e3c78f1e3
    //     0xe8a2b4: ldr             d3, [x17, #0x798]
    // 0xe8a2b8: fcmp            d3, d0
    // 0xe8a2bc: b.lt            #0xe8a2d8
    // 0xe8a2c0: tbz             w2, #4, #0xe8a2d8
    // 0xe8a2c4: mov             v0.16b, v2.16b
    // 0xe8a2c8: ldur            x9, [fp, #-0x38]
    // 0xe8a2cc: ldur            x11, [fp, #-0x10]
    // 0xe8a2d0: r10 = Null
    //     0xe8a2d0: mov             x10, NULL
    // 0xe8a2d4: b               #0xe8a724
    // 0xe8a2d8: tbnz            w2, #4, #0xe8a790
    // 0xe8a2dc: ldur            x2, [fp, #-0x78]
    // 0xe8a2e0: cmp             w2, NULL
    // 0xe8a2e4: b.eq            #0xe8a300
    // 0xe8a2e8: r0 = LoadClassIdInstr(r3)
    //     0xe8a2e8: ldur            x0, [x3, #-1]
    //     0xe8a2ec: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a2f0: mov             x1, x3
    // 0xe8a2f4: r0 = GDT[cid_x0 + -0xd5b]()
    //     0xe8a2f4: sub             lr, x0, #0xd5b
    //     0xe8a2f8: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a2fc: blr             lr
    // 0xe8a300: ldur            d1, [fp, #-0xb0]
    // 0xe8a304: ldur            x0, [fp, #-0x58]
    // 0xe8a308: ldur            d0, [fp, #-0xb8]
    // 0xe8a30c: ldur            x1, [fp, #-8]
    // 0xe8a310: fsub            d2, d0, d1
    // 0xe8a314: stur            d2, [fp, #-0xc0]
    // 0xe8a318: r0 = BoxConstraints()
    //     0xe8a318: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe8a31c: mov             x4, x0
    // 0xe8a320: stur            x4, [fp, #-0x68]
    // 0xe8a324: StoreField: r4->field_7 = rZR
    //     0xe8a324: stur            xzr, [x4, #7]
    // 0xe8a328: d0 = 555.275591
    //     0xe8a328: add             x17, PP, #0x36, lsl #12  ; [pp+0x36748] IMM: double(555.275590551181) from 0x40815a3468d1a346
    //     0xe8a32c: ldr             d0, [x17, #0x748]
    // 0xe8a330: StoreField: r4->field_f = d0
    //     0xe8a330: stur            d0, [x4, #0xf]
    // 0xe8a334: ArrayStore: r4[0] = rZR  ; List_8
    //     0xe8a334: stur            xzr, [x4, #0x17]
    // 0xe8a338: ldur            d1, [fp, #-0xc0]
    // 0xe8a33c: StoreField: r4->field_1f = d1
    //     0xe8a33c: stur            d1, [x4, #0x1f]
    // 0xe8a340: ldur            x5, [fp, #-8]
    // 0xe8a344: r0 = LoadClassIdInstr(r5)
    //     0xe8a344: ldur            x0, [x5, #-1]
    //     0xe8a348: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a34c: mov             x1, x5
    // 0xe8a350: ldur            x2, [fp, #-0x70]
    // 0xe8a354: mov             x3, x4
    // 0xe8a358: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe8a358: sub             lr, x0, #0xf89
    //     0xe8a35c: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a360: blr             lr
    // 0xe8a364: ldur            x2, [fp, #-8]
    // 0xe8a368: r0 = LoadClassIdInstr(r2)
    //     0xe8a368: ldur            x0, [x2, #-1]
    //     0xe8a36c: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a370: mov             x1, x2
    // 0xe8a374: r0 = GDT[cid_x0 + -0xe7b]()
    //     0xe8a374: sub             lr, x0, #0xe7b
    //     0xe8a378: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a37c: blr             lr
    // 0xe8a380: mov             x3, x0
    // 0xe8a384: ldur            x2, [fp, #-0x58]
    // 0xe8a388: stur            x3, [fp, #-0x80]
    // 0xe8a38c: LoadField: r0 = r2->field_b
    //     0xe8a38c: ldur            w0, [x2, #0xb]
    // 0xe8a390: r1 = LoadInt32Instr(r0)
    //     0xe8a390: sbfx            x1, x0, #1, #0x1f
    // 0xe8a394: cmp             x1, #0
    // 0xe8a398: b.le            #0xe8a784
    // 0xe8a39c: sub             x4, x1, #1
    // 0xe8a3a0: mov             x0, x1
    // 0xe8a3a4: mov             x1, x4
    // 0xe8a3a8: cmp             x1, x0
    // 0xe8a3ac: b.hs            #0xe8a870
    // 0xe8a3b0: LoadField: r0 = r2->field_f
    //     0xe8a3b0: ldur            w0, [x2, #0xf]
    // 0xe8a3b4: DecompressPointer r0
    //     0xe8a3b4: add             x0, x0, HEAP, lsl #32
    // 0xe8a3b8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xe8a3b8: add             x16, x0, x4, lsl #2
    //     0xe8a3bc: ldur            w1, [x16, #0xf]
    // 0xe8a3c0: DecompressPointer r1
    //     0xe8a3c0: add             x1, x1, HEAP, lsl #32
    // 0xe8a3c4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xe8a3c4: ldur            w0, [x1, #0x17]
    // 0xe8a3c8: DecompressPointer r0
    //     0xe8a3c8: add             x0, x0, HEAP, lsl #32
    // 0xe8a3cc: stur            x0, [fp, #-0x78]
    // 0xe8a3d0: r1 = LoadClassIdInstr(r3)
    //     0xe8a3d0: ldur            x1, [x3, #-1]
    //     0xe8a3d4: ubfx            x1, x1, #0xc, #0x14
    // 0xe8a3d8: cmp             x1, #0x335
    // 0xe8a3dc: b.ne            #0xe8a40c
    // 0xe8a3e0: r0 = RichTextContext()
    //     0xe8a3e0: bl              #0xb1254c  ; AllocateRichTextContextStub -> RichTextContext (size=0x28)
    // 0xe8a3e4: stur            x0, [fp, #-0x90]
    // 0xe8a3e8: StoreField: r0->field_7 = rZR
    //     0xe8a3e8: stur            xzr, [x0, #7]
    // 0xe8a3ec: StoreField: r0->field_f = rZR
    //     0xe8a3ec: stur            xzr, [x0, #0xf]
    // 0xe8a3f0: ArrayStore: r0[0] = rZR  ; List_8
    //     0xe8a3f0: stur            xzr, [x0, #0x17]
    // 0xe8a3f4: StoreField: r0->field_1f = rZR
    //     0xe8a3f4: stur            xzr, [x0, #0x1f]
    // 0xe8a3f8: mov             x1, x0
    // 0xe8a3fc: ldur            x2, [fp, #-0x80]
    // 0xe8a400: r0 = apply()
    //     0xe8a400: bl              #0xeabf54  ; [package:pdf/src/widgets/text.dart] RichTextContext::apply
    // 0xe8a404: ldur            x3, [fp, #-0x90]
    // 0xe8a408: b               #0xe8a458
    // 0xe8a40c: cmp             x1, #0x336
    // 0xe8a410: b.ne            #0xe8a438
    // 0xe8a414: r0 = TableContext()
    //     0xe8a414: bl              #0xb137b0  ; AllocateTableContextStub -> TableContext (size=0x18)
    // 0xe8a418: stur            x0, [fp, #-0x90]
    // 0xe8a41c: StoreField: r0->field_7 = rZR
    //     0xe8a41c: stur            xzr, [x0, #7]
    // 0xe8a420: StoreField: r0->field_f = rZR
    //     0xe8a420: stur            xzr, [x0, #0xf]
    // 0xe8a424: mov             x1, x0
    // 0xe8a428: ldur            x2, [fp, #-0x80]
    // 0xe8a42c: r0 = apply()
    //     0xe8a42c: bl              #0xeabed8  ; [package:pdf/src/widgets/table.dart] TableContext::apply
    // 0xe8a430: ldur            x3, [fp, #-0x90]
    // 0xe8a434: b               #0xe8a458
    // 0xe8a438: r0 = FlexContext()
    //     0xe8a438: bl              #0xb13cc8  ; AllocateFlexContextStub -> FlexContext (size=0x18)
    // 0xe8a43c: stur            x0, [fp, #-0x90]
    // 0xe8a440: StoreField: r0->field_7 = rZR
    //     0xe8a440: stur            xzr, [x0, #7]
    // 0xe8a444: StoreField: r0->field_f = rZR
    //     0xe8a444: stur            xzr, [x0, #0xf]
    // 0xe8a448: mov             x1, x0
    // 0xe8a44c: ldur            x2, [fp, #-0x80]
    // 0xe8a450: r0 = apply()
    //     0xe8a450: bl              #0xeabe5c  ; [package:pdf/src/widgets/flex.dart] FlexContext::apply
    // 0xe8a454: ldur            x3, [fp, #-0x90]
    // 0xe8a458: ldur            x1, [fp, #-0x78]
    // 0xe8a45c: ldur            x2, [fp, #-0x68]
    // 0xe8a460: ldur            x0, [fp, #-8]
    // 0xe8a464: stur            x3, [fp, #-0x90]
    // 0xe8a468: r0 = _MultiPageWidget()
    //     0xe8a468: bl              #0xe8aa38  ; Allocate_MultiPageWidgetStub -> _MultiPageWidget (size=0x14)
    // 0xe8a46c: mov             x4, x0
    // 0xe8a470: ldur            x3, [fp, #-8]
    // 0xe8a474: stur            x4, [fp, #-0x98]
    // 0xe8a478: StoreField: r4->field_7 = r3
    //     0xe8a478: stur            w3, [x4, #7]
    // 0xe8a47c: ldur            x0, [fp, #-0x68]
    // 0xe8a480: StoreField: r4->field_b = r0
    //     0xe8a480: stur            w0, [x4, #0xb]
    // 0xe8a484: ldur            x0, [fp, #-0x90]
    // 0xe8a488: StoreField: r4->field_f = r0
    //     0xe8a488: stur            w0, [x4, #0xf]
    // 0xe8a48c: ldur            x5, [fp, #-0x78]
    // 0xe8a490: LoadField: r2 = r5->field_7
    //     0xe8a490: ldur            w2, [x5, #7]
    // 0xe8a494: DecompressPointer r2
    //     0xe8a494: add             x2, x2, HEAP, lsl #32
    // 0xe8a498: mov             x0, x4
    // 0xe8a49c: r1 = Null
    //     0xe8a49c: mov             x1, NULL
    // 0xe8a4a0: cmp             w2, NULL
    // 0xe8a4a4: b.eq            #0xe8a4c4
    // 0xe8a4a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe8a4a8: ldur            w4, [x2, #0x17]
    // 0xe8a4ac: DecompressPointer r4
    //     0xe8a4ac: add             x4, x4, HEAP, lsl #32
    // 0xe8a4b0: r8 = X0
    //     0xe8a4b0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe8a4b4: LoadField: r9 = r4->field_7
    //     0xe8a4b4: ldur            x9, [x4, #7]
    // 0xe8a4b8: r3 = Null
    //     0xe8a4b8: add             x3, PP, #0x36, lsl #12  ; [pp+0x367a0] Null
    //     0xe8a4bc: ldr             x3, [x3, #0x7a0]
    // 0xe8a4c0: blr             x9
    // 0xe8a4c4: ldur            x0, [fp, #-0x78]
    // 0xe8a4c8: LoadField: r1 = r0->field_b
    //     0xe8a4c8: ldur            w1, [x0, #0xb]
    // 0xe8a4cc: LoadField: r2 = r0->field_f
    //     0xe8a4cc: ldur            w2, [x0, #0xf]
    // 0xe8a4d0: DecompressPointer r2
    //     0xe8a4d0: add             x2, x2, HEAP, lsl #32
    // 0xe8a4d4: LoadField: r3 = r2->field_b
    //     0xe8a4d4: ldur            w3, [x2, #0xb]
    // 0xe8a4d8: r2 = LoadInt32Instr(r1)
    //     0xe8a4d8: sbfx            x2, x1, #1, #0x1f
    // 0xe8a4dc: stur            x2, [fp, #-0xa0]
    // 0xe8a4e0: r1 = LoadInt32Instr(r3)
    //     0xe8a4e0: sbfx            x1, x3, #1, #0x1f
    // 0xe8a4e4: cmp             x2, x1
    // 0xe8a4e8: b.ne            #0xe8a4f4
    // 0xe8a4ec: mov             x1, x0
    // 0xe8a4f0: r0 = _growToNextCapacity()
    //     0xe8a4f0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8a4f4: ldur            x0, [fp, #-0x78]
    // 0xe8a4f8: ldur            x2, [fp, #-0xa0]
    // 0xe8a4fc: ldur            x3, [fp, #-8]
    // 0xe8a500: add             x1, x2, #1
    // 0xe8a504: lsl             x4, x1, #1
    // 0xe8a508: StoreField: r0->field_b = r4
    //     0xe8a508: stur            w4, [x0, #0xb]
    // 0xe8a50c: LoadField: r1 = r0->field_f
    //     0xe8a50c: ldur            w1, [x0, #0xf]
    // 0xe8a510: DecompressPointer r1
    //     0xe8a510: add             x1, x1, HEAP, lsl #32
    // 0xe8a514: ldur            x0, [fp, #-0x98]
    // 0xe8a518: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe8a518: add             x25, x1, x2, lsl #2
    //     0xe8a51c: add             x25, x25, #0xf
    //     0xe8a520: str             w0, [x25]
    //     0xe8a524: tbz             w0, #0, #0xe8a540
    //     0xe8a528: ldurb           w16, [x1, #-1]
    //     0xe8a52c: ldurb           w17, [x0, #-1]
    //     0xe8a530: and             x16, x17, x16, lsr #2
    //     0xe8a534: tst             x16, HEAP, lsr #32
    //     0xe8a538: b.eq            #0xe8a540
    //     0xe8a53c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8a540: r0 = LoadClassIdInstr(r3)
    //     0xe8a540: ldur            x0, [x3, #-1]
    //     0xe8a544: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a548: mov             x1, x3
    // 0xe8a54c: r0 = GDT[cid_x0 + -0xe3d]()
    //     0xe8a54c: sub             lr, x0, #0xe3d
    //     0xe8a550: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a554: blr             lr
    // 0xe8a558: tbz             w0, #4, #0xe8a568
    // 0xe8a55c: ldur            x4, [fp, #-0x38]
    // 0xe8a560: add             x0, x4, #1
    // 0xe8a564: b               #0xe8a570
    // 0xe8a568: ldur            x4, [fp, #-0x38]
    // 0xe8a56c: mov             x0, x4
    // 0xe8a570: ldur            d0, [fp, #-0xb8]
    // 0xe8a574: mov             x9, x0
    // 0xe8a578: ldur            x11, [fp, #-0x80]
    // 0xe8a57c: r10 = Null
    //     0xe8a57c: mov             x10, NULL
    // 0xe8a580: b               #0xe8a724
    // 0xe8a584: ldur            x4, [fp, #-0x38]
    // 0xe8a588: ldur            x5, [fp, #-0x58]
    // 0xe8a58c: LoadField: r0 = r5->field_b
    //     0xe8a58c: ldur            w0, [x5, #0xb]
    // 0xe8a590: r1 = LoadInt32Instr(r0)
    //     0xe8a590: sbfx            x1, x0, #1, #0x1f
    // 0xe8a594: cmp             x1, #0
    // 0xe8a598: b.le            #0xe8a824
    // 0xe8a59c: ldur            x6, [fp, #-0x88]
    // 0xe8a5a0: sub             x7, x1, #1
    // 0xe8a5a4: mov             x0, x1
    // 0xe8a5a8: mov             x1, x7
    // 0xe8a5ac: cmp             x1, x0
    // 0xe8a5b0: b.hs            #0xe8a874
    // 0xe8a5b4: LoadField: r0 = r5->field_f
    //     0xe8a5b4: ldur            w0, [x5, #0xf]
    // 0xe8a5b8: DecompressPointer r0
    //     0xe8a5b8: add             x0, x0, HEAP, lsl #32
    // 0xe8a5bc: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xe8a5bc: add             x16, x0, x7, lsl #2
    //     0xe8a5c0: ldur            w1, [x16, #0xf]
    // 0xe8a5c4: DecompressPointer r1
    //     0xe8a5c4: add             x1, x1, HEAP, lsl #32
    // 0xe8a5c8: ArrayLoad: r7 = r1[0]  ; List_4
    //     0xe8a5c8: ldur            w7, [x1, #0x17]
    // 0xe8a5cc: DecompressPointer r7
    //     0xe8a5cc: add             x7, x7, HEAP, lsl #32
    // 0xe8a5d0: stur            x7, [fp, #-0x68]
    // 0xe8a5d4: sub             x16, x6, #0x31a
    // 0xe8a5d8: cmp             x16, #0xf
    // 0xe8a5dc: b.hi            #0xe8a604
    // 0xe8a5e0: tbnz            w2, #4, #0xe8a604
    // 0xe8a5e4: r0 = LoadClassIdInstr(r3)
    //     0xe8a5e4: ldur            x0, [x3, #-1]
    //     0xe8a5e8: ubfx            x0, x0, #0xc, #0x14
    // 0xe8a5ec: mov             x1, x3
    // 0xe8a5f0: r0 = GDT[cid_x0 + -0xc4b]()
    //     0xe8a5f0: sub             lr, x0, #0xc4b
    //     0xe8a5f4: ldr             lr, [x21, lr, lsl #3]
    //     0xe8a5f8: blr             lr
    // 0xe8a5fc: mov             x3, x0
    // 0xe8a600: b               #0xe8a608
    // 0xe8a604: r3 = Null
    //     0xe8a604: mov             x3, NULL
    // 0xe8a608: ldur            x2, [fp, #-0x28]
    // 0xe8a60c: ldur            x1, [fp, #-0x68]
    // 0xe8a610: ldur            x0, [fp, #-8]
    // 0xe8a614: stur            x3, [fp, #-0x78]
    // 0xe8a618: r0 = _MultiPageWidget()
    //     0xe8a618: bl              #0xe8aa38  ; Allocate_MultiPageWidgetStub -> _MultiPageWidget (size=0x14)
    // 0xe8a61c: mov             x4, x0
    // 0xe8a620: ldur            x3, [fp, #-8]
    // 0xe8a624: stur            x4, [fp, #-0x80]
    // 0xe8a628: StoreField: r4->field_7 = r3
    //     0xe8a628: stur            w3, [x4, #7]
    // 0xe8a62c: ldur            x5, [fp, #-0x28]
    // 0xe8a630: StoreField: r4->field_b = r5
    //     0xe8a630: stur            w5, [x4, #0xb]
    // 0xe8a634: ldur            x0, [fp, #-0x78]
    // 0xe8a638: StoreField: r4->field_f = r0
    //     0xe8a638: stur            w0, [x4, #0xf]
    // 0xe8a63c: ldur            x6, [fp, #-0x68]
    // 0xe8a640: LoadField: r2 = r6->field_7
    //     0xe8a640: ldur            w2, [x6, #7]
    // 0xe8a644: DecompressPointer r2
    //     0xe8a644: add             x2, x2, HEAP, lsl #32
    // 0xe8a648: mov             x0, x4
    // 0xe8a64c: r1 = Null
    //     0xe8a64c: mov             x1, NULL
    // 0xe8a650: cmp             w2, NULL
    // 0xe8a654: b.eq            #0xe8a674
    // 0xe8a658: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe8a658: ldur            w4, [x2, #0x17]
    // 0xe8a65c: DecompressPointer r4
    //     0xe8a65c: add             x4, x4, HEAP, lsl #32
    // 0xe8a660: r8 = X0
    //     0xe8a660: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe8a664: LoadField: r9 = r4->field_7
    //     0xe8a664: ldur            x9, [x4, #7]
    // 0xe8a668: r3 = Null
    //     0xe8a668: add             x3, PP, #0x36, lsl #12  ; [pp+0x367b0] Null
    //     0xe8a66c: ldr             x3, [x3, #0x7b0]
    // 0xe8a670: blr             x9
    // 0xe8a674: ldur            x0, [fp, #-0x68]
    // 0xe8a678: LoadField: r1 = r0->field_b
    //     0xe8a678: ldur            w1, [x0, #0xb]
    // 0xe8a67c: LoadField: r2 = r0->field_f
    //     0xe8a67c: ldur            w2, [x0, #0xf]
    // 0xe8a680: DecompressPointer r2
    //     0xe8a680: add             x2, x2, HEAP, lsl #32
    // 0xe8a684: LoadField: r3 = r2->field_b
    //     0xe8a684: ldur            w3, [x2, #0xb]
    // 0xe8a688: r2 = LoadInt32Instr(r1)
    //     0xe8a688: sbfx            x2, x1, #1, #0x1f
    // 0xe8a68c: stur            x2, [fp, #-0x88]
    // 0xe8a690: r1 = LoadInt32Instr(r3)
    //     0xe8a690: sbfx            x1, x3, #1, #0x1f
    // 0xe8a694: cmp             x2, x1
    // 0xe8a698: b.ne            #0xe8a6a4
    // 0xe8a69c: mov             x1, x0
    // 0xe8a6a0: r0 = _growToNextCapacity()
    //     0xe8a6a0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8a6a4: ldur            x4, [fp, #-0x38]
    // 0xe8a6a8: ldur            x0, [fp, #-0x68]
    // 0xe8a6ac: ldur            d0, [fp, #-0xb8]
    // 0xe8a6b0: ldur            x2, [fp, #-0x88]
    // 0xe8a6b4: ldur            x3, [fp, #-8]
    // 0xe8a6b8: add             x1, x2, #1
    // 0xe8a6bc: lsl             x5, x1, #1
    // 0xe8a6c0: StoreField: r0->field_b = r5
    //     0xe8a6c0: stur            w5, [x0, #0xb]
    // 0xe8a6c4: LoadField: r1 = r0->field_f
    //     0xe8a6c4: ldur            w1, [x0, #0xf]
    // 0xe8a6c8: DecompressPointer r1
    //     0xe8a6c8: add             x1, x1, HEAP, lsl #32
    // 0xe8a6cc: ldur            x0, [fp, #-0x80]
    // 0xe8a6d0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe8a6d0: add             x25, x1, x2, lsl #2
    //     0xe8a6d4: add             x25, x25, #0xf
    //     0xe8a6d8: str             w0, [x25]
    //     0xe8a6dc: tbz             w0, #0, #0xe8a6f8
    //     0xe8a6e0: ldurb           w16, [x1, #-1]
    //     0xe8a6e4: ldurb           w17, [x0, #-1]
    //     0xe8a6e8: and             x16, x17, x16, lsr #2
    //     0xe8a6ec: tst             x16, HEAP, lsr #32
    //     0xe8a6f0: b.eq            #0xe8a6f8
    //     0xe8a6f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8a6f8: LoadField: r0 = r3->field_7
    //     0xe8a6f8: ldur            w0, [x3, #7]
    // 0xe8a6fc: DecompressPointer r0
    //     0xe8a6fc: add             x0, x0, HEAP, lsl #32
    // 0xe8a700: cmp             w0, NULL
    // 0xe8a704: b.eq            #0xe8a878
    // 0xe8a708: LoadField: d1 = r0->field_1f
    //     0xe8a708: ldur            d1, [x0, #0x1f]
    // 0xe8a70c: fsub            d2, d0, d1
    // 0xe8a710: add             x0, x4, #1
    // 0xe8a714: ldur            x10, [fp, #-0x70]
    // 0xe8a718: mov             v0.16b, v2.16b
    // 0xe8a71c: mov             x9, x0
    // 0xe8a720: ldur            x11, [fp, #-0x10]
    // 0xe8a724: r1 = inline_Allocate_Double()
    //     0xe8a724: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe8a728: add             x1, x1, #0x10
    //     0xe8a72c: cmp             x0, x1
    //     0xe8a730: b.ls            #0xe8a87c
    //     0xe8a734: str             x1, [THR, #0x50]  ; THR::top
    //     0xe8a738: sub             x1, x1, #0xf
    //     0xe8a73c: movz            x0, #0xe15c
    //     0xe8a740: movk            x0, #0x3, lsl #16
    //     0xe8a744: stur            x0, [x1, #-1]
    // 0xe8a748: StoreField: r1->field_7 = d0
    //     0xe8a748: stur            d0, [x1, #7]
    // 0xe8a74c: ldur            d1, [fp, #-0xb0]
    // 0xe8a750: ldur            x2, [fp, #-0x60]
    // 0xe8a754: ldur            x3, [fp, #-0x58]
    // 0xe8a758: ldur            d0, [fp, #-0xa8]
    // 0xe8a75c: ldur            x7, [fp, #-0x30]
    // 0xe8a760: ldur            x8, [fp, #-0x20]
    // 0xe8a764: ldur            x6, [fp, #-0x40]
    // 0xe8a768: ldur            x4, [fp, #-0x50]
    // 0xe8a76c: ldur            x5, [fp, #-0x48]
    // 0xe8a770: b               #0xe89d7c
    // 0xe8a774: r0 = Null
    //     0xe8a774: mov             x0, NULL
    // 0xe8a778: LeaveFrame
    //     0xe8a778: mov             SP, fp
    //     0xe8a77c: ldp             fp, lr, [SP], #0x10
    // 0xe8a780: ret
    //     0xe8a780: ret             
    // 0xe8a784: r0 = noElement()
    //     0xe8a784: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xe8a788: r0 = Throw()
    //     0xe8a788: bl              #0xec04b8  ; ThrowStub
    // 0xe8a78c: brk             #0
    // 0xe8a790: r1 = Null
    //     0xe8a790: mov             x1, NULL
    // 0xe8a794: r2 = 10
    //     0xe8a794: movz            x2, #0xa
    // 0xe8a798: r0 = AllocateArray()
    //     0xe8a798: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8a79c: r16 = "Widget won\'t fit into the page as its height ("
    //     0xe8a79c: add             x16, PP, #0x36, lsl #12  ; [pp+0x367c0] "Widget won\'t fit into the page as its height ("
    //     0xe8a7a0: ldr             x16, [x16, #0x7c0]
    // 0xe8a7a4: StoreField: r0->field_f = r16
    //     0xe8a7a4: stur            w16, [x0, #0xf]
    // 0xe8a7a8: ldur            d0, [fp, #-0xc8]
    // 0xe8a7ac: r1 = inline_Allocate_Double()
    //     0xe8a7ac: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xe8a7b0: add             x1, x1, #0x10
    //     0xe8a7b4: cmp             x2, x1
    //     0xe8a7b8: b.ls            #0xe8a8a0
    //     0xe8a7bc: str             x1, [THR, #0x50]  ; THR::top
    //     0xe8a7c0: sub             x1, x1, #0xf
    //     0xe8a7c4: movz            x2, #0xe15c
    //     0xe8a7c8: movk            x2, #0x3, lsl #16
    //     0xe8a7cc: stur            x2, [x1, #-1]
    // 0xe8a7d0: StoreField: r1->field_7 = d0
    //     0xe8a7d0: stur            d0, [x1, #7]
    // 0xe8a7d4: StoreField: r0->field_13 = r1
    //     0xe8a7d4: stur            w1, [x0, #0x13]
    // 0xe8a7d8: r16 = ") exceed a page height ("
    //     0xe8a7d8: add             x16, PP, #0x36, lsl #12  ; [pp+0x367c8] ") exceed a page height ("
    //     0xe8a7dc: ldr             x16, [x16, #0x7c8]
    // 0xe8a7e0: ArrayStore: r0[0] = r16  ; List_4
    //     0xe8a7e0: stur            w16, [x0, #0x17]
    // 0xe8a7e4: r16 = 801.889764
    //     0xe8a7e4: add             x16, PP, #0x36, lsl #12  ; [pp+0x367d0] 801.8897637795275
    //     0xe8a7e8: ldr             x16, [x16, #0x7d0]
    // 0xe8a7ec: StoreField: r0->field_1b = r16
    //     0xe8a7ec: stur            w16, [x0, #0x1b]
    // 0xe8a7f0: r16 = "). You probably need a SpanningWidget or use a single page layout"
    //     0xe8a7f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x367d8] "). You probably need a SpanningWidget or use a single page layout"
    //     0xe8a7f4: ldr             x16, [x16, #0x7d8]
    // 0xe8a7f8: StoreField: r0->field_1f = r16
    //     0xe8a7f8: stur            w16, [x0, #0x1f]
    // 0xe8a7fc: str             x0, [SP]
    // 0xe8a800: r0 = _interpolate()
    //     0xe8a800: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe8a804: stur            x0, [fp, #-8]
    // 0xe8a808: r0 = _Exception()
    //     0xe8a808: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xe8a80c: mov             x1, x0
    // 0xe8a810: ldur            x0, [fp, #-8]
    // 0xe8a814: StoreField: r1->field_7 = r0
    //     0xe8a814: stur            w0, [x1, #7]
    // 0xe8a818: mov             x0, x1
    // 0xe8a81c: r0 = Throw()
    //     0xe8a81c: bl              #0xec04b8  ; ThrowStub
    // 0xe8a820: brk             #0
    // 0xe8a824: r0 = noElement()
    //     0xe8a824: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xe8a828: r0 = Throw()
    //     0xe8a828: bl              #0xec04b8  ; ThrowStub
    // 0xe8a82c: brk             #0
    // 0xe8a830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8a830: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8a834: b               #0xe89c38
    // 0xe8a838: r0 = StackOverflowSharedWithFPURegs()
    //     0xe8a838: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe8a83c: b               #0xe89d94
    // 0xe8a840: r0 = NullErrorSharedWithFPURegs()
    //     0xe8a840: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xe8a844: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe8a844: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe8a848: r0 = NullErrorSharedWithFPURegs()
    //     0xe8a848: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xe8a84c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe8a84c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe8a850: stp             q1, q2, [SP, #-0x20]!
    // 0xe8a854: SaveReg d0
    //     0xe8a854: str             q0, [SP, #-0x10]!
    // 0xe8a858: r0 = AllocateDouble()
    //     0xe8a858: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8a85c: RestoreReg d0
    //     0xe8a85c: ldr             q0, [SP], #0x10
    // 0xe8a860: ldp             q1, q2, [SP], #0x20
    // 0xe8a864: b               #0xe8a13c
    // 0xe8a868: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe8a868: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe8a86c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe8a86c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe8a870: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8a870: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8a874: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8a874: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8a878: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe8a878: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe8a87c: SaveReg d0
    //     0xe8a87c: str             q0, [SP, #-0x10]!
    // 0xe8a880: stp             x10, x11, [SP, #-0x10]!
    // 0xe8a884: SaveReg r9
    //     0xe8a884: str             x9, [SP, #-8]!
    // 0xe8a888: r0 = AllocateDouble()
    //     0xe8a888: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8a88c: mov             x1, x0
    // 0xe8a890: RestoreReg r9
    //     0xe8a890: ldr             x9, [SP], #8
    // 0xe8a894: ldp             x10, x11, [SP], #0x10
    // 0xe8a898: RestoreReg d0
    //     0xe8a898: ldr             q0, [SP], #0x10
    // 0xe8a89c: b               #0xe8a748
    // 0xe8a8a0: SaveReg d0
    //     0xe8a8a0: str             q0, [SP, #-0x10]!
    // 0xe8a8a4: SaveReg r0
    //     0xe8a8a4: str             x0, [SP, #-8]!
    // 0xe8a8a8: r0 = AllocateDouble()
    //     0xe8a8a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe8a8ac: mov             x1, x0
    // 0xe8a8b0: RestoreReg r0
    //     0xe8a8b0: ldr             x0, [SP], #8
    // 0xe8a8b4: RestoreReg d0
    //     0xe8a8b4: ldr             q0, [SP], #0x10
    // 0xe8a8b8: b               #0xe8a7d0
  }
}

// class id: 783, size: 0x1c, field offset: 0x8
class _MultiPageInstance extends Object {
}

// class id: 784, size: 0x14, field offset: 0x8
//   const constructor, 
class _MultiPageWidget extends Object {
}

// class id: 810, size: 0xc, field offset: 0xc
abstract class SpanningWidget extends Widget {
}

// class id: 820, size: 0x8, field offset: 0x8
abstract class WidgetContext extends Object {
}
