// lib: , url: package:pdf/src/widgets/page.dart

// class id: 1050856, size: 0x8
class :: {
}

// class id: 781, size: 0x10, field offset: 0x8
abstract class Page extends Object {

  get _ resolvedMargin(/* No info */) {
    // ** addr: 0xe89a9c, size: 0x2c
    // 0xe89a9c: EnterFrame
    //     0xe89a9c: stp             fp, lr, [SP, #-0x10]!
    //     0xe89aa0: mov             fp, SP
    // 0xe89aa4: CheckStackOverflow
    //     0xe89aa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe89aa8: cmp             SP, x16
    //     0xe89aac: b.ls            #0xe89ac0
    // 0xe89ab0: r0 = margin()
    //     0xe89ab0: bl              #0xe89ac8  ; [package:pdf/src/widgets/page.dart] Page::margin
    // 0xe89ab4: LeaveFrame
    //     0xe89ab4: mov             SP, fp
    //     0xe89ab8: ldp             fp, lr, [SP], #0x10
    // 0xe89abc: ret
    //     0xe89abc: ret             
    // 0xe89ac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe89ac0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe89ac4: b               #0xe89ab0
  }
  get _ margin(/* No info */) {
    // ** addr: 0xe89ac8, size: 0x38
    // 0xe89ac8: EnterFrame
    //     0xe89ac8: stp             fp, lr, [SP, #-0x10]!
    //     0xe89acc: mov             fp, SP
    // 0xe89ad0: CheckStackOverflow
    //     0xe89ad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe89ad4: cmp             SP, x16
    //     0xe89ad8: b.ls            #0xe89af8
    // 0xe89adc: LoadField: r0 = r1->field_7
    //     0xe89adc: ldur            w0, [x1, #7]
    // 0xe89ae0: DecompressPointer r0
    //     0xe89ae0: add             x0, x0, HEAP, lsl #32
    // 0xe89ae4: mov             x1, x0
    // 0xe89ae8: r0 = margin()
    //     0xe89ae8: bl              #0xe89b00  ; [package:pdf/src/widgets/page_theme.dart] PageTheme::margin
    // 0xe89aec: LeaveFrame
    //     0xe89aec: mov             SP, fp
    //     0xe89af0: ldp             fp, lr, [SP], #0x10
    // 0xe89af4: ret
    //     0xe89af4: ret             
    // 0xe89af8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe89af8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe89afc: b               #0xe89adc
  }
}

// class id: 6794, size: 0x14, field offset: 0x14
enum PageOrientation extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e11c, size: 0x64
    // 0xc4e11c: EnterFrame
    //     0xc4e11c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e120: mov             fp, SP
    // 0xc4e124: AllocStack(0x10)
    //     0xc4e124: sub             SP, SP, #0x10
    // 0xc4e128: SetupParameters(PageOrientation this /* r1 => r0, fp-0x8 */)
    //     0xc4e128: mov             x0, x1
    //     0xc4e12c: stur            x1, [fp, #-8]
    // 0xc4e130: CheckStackOverflow
    //     0xc4e130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e134: cmp             SP, x16
    //     0xc4e138: b.ls            #0xc4e178
    // 0xc4e13c: r1 = Null
    //     0xc4e13c: mov             x1, NULL
    // 0xc4e140: r2 = 4
    //     0xc4e140: movz            x2, #0x4
    // 0xc4e144: r0 = AllocateArray()
    //     0xc4e144: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e148: r16 = "PageOrientation."
    //     0xc4e148: add             x16, PP, #0x33, lsl #12  ; [pp+0x33778] "PageOrientation."
    //     0xc4e14c: ldr             x16, [x16, #0x778]
    // 0xc4e150: StoreField: r0->field_f = r16
    //     0xc4e150: stur            w16, [x0, #0xf]
    // 0xc4e154: ldur            x1, [fp, #-8]
    // 0xc4e158: LoadField: r2 = r1->field_f
    //     0xc4e158: ldur            w2, [x1, #0xf]
    // 0xc4e15c: DecompressPointer r2
    //     0xc4e15c: add             x2, x2, HEAP, lsl #32
    // 0xc4e160: StoreField: r0->field_13 = r2
    //     0xc4e160: stur            w2, [x0, #0x13]
    // 0xc4e164: str             x0, [SP]
    // 0xc4e168: r0 = _interpolate()
    //     0xc4e168: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e16c: LeaveFrame
    //     0xc4e16c: mov             SP, fp
    //     0xc4e170: ldp             fp, lr, [SP], #0x10
    // 0xc4e174: ret
    //     0xc4e174: ret             
    // 0xc4e178: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e178: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e17c: b               #0xc4e13c
  }
}
