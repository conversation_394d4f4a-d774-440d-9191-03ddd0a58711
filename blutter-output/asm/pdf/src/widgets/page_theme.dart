// lib: , url: package:pdf/src/widgets/page_theme.dart

// class id: 1050857, size: 0x8
class :: {
}

// class id: 780, size: 0x24, field offset: 0x8
//   const constructor, 
class PageTheme extends Object {

  get _ margin(/* No info */) {
    // ** addr: 0xe89b00, size: 0xc
    // 0xe89b00: r0 = Instance_EdgeInsets
    //     0xe89b00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e4f8] Obj!EdgeInsets@e0c4a1
    //     0xe89b04: ldr             x0, [x0, #0x4f8]
    // 0xe89b08: ret
    //     0xe89b08: ret             
  }
}
