// lib: , url: package:pdf/src/widgets/placeholders.dart

// class id: 1050858, size: 0x8
class :: {
}

// class id: 789, size: 0x28, field offset: 0xc
class Placeholder extends Widget {

  _ paint(/* No info */) {
    // ** addr: 0xe6daf8, size: 0x164
    // 0xe6daf8: EnterFrame
    //     0xe6daf8: stp             fp, lr, [SP, #-0x10]!
    //     0xe6dafc: mov             fp, SP
    // 0xe6db00: AllocStack(0x10)
    //     0xe6db00: sub             SP, SP, #0x10
    // 0xe6db04: SetupParameters(Placeholder this /* r1 => r0, fp-0x10 */)
    //     0xe6db04: mov             x0, x1
    //     0xe6db08: stur            x1, [fp, #-0x10]
    // 0xe6db0c: CheckStackOverflow
    //     0xe6db0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6db10: cmp             SP, x16
    //     0xe6db14: b.ls            #0xe6dc3c
    // 0xe6db18: LoadField: r3 = r2->field_b
    //     0xe6db18: ldur            w3, [x2, #0xb]
    // 0xe6db1c: DecompressPointer r3
    //     0xe6db1c: add             x3, x3, HEAP, lsl #32
    // 0xe6db20: stur            x3, [fp, #-8]
    // 0xe6db24: cmp             w3, NULL
    // 0xe6db28: b.eq            #0xe6dc44
    // 0xe6db2c: LoadField: r2 = r0->field_b
    //     0xe6db2c: ldur            w2, [x0, #0xb]
    // 0xe6db30: DecompressPointer r2
    //     0xe6db30: add             x2, x2, HEAP, lsl #32
    // 0xe6db34: mov             x1, x3
    // 0xe6db38: r0 = setStrokeColor()
    //     0xe6db38: bl              #0xe65994  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setStrokeColor
    // 0xe6db3c: ldur            x0, [fp, #-0x10]
    // 0xe6db40: LoadField: r1 = r0->field_7
    //     0xe6db40: ldur            w1, [x0, #7]
    // 0xe6db44: DecompressPointer r1
    //     0xe6db44: add             x1, x1, HEAP, lsl #32
    // 0xe6db48: cmp             w1, NULL
    // 0xe6db4c: b.eq            #0xe6dc48
    // 0xe6db50: LoadField: d0 = r1->field_7
    //     0xe6db50: ldur            d0, [x1, #7]
    // 0xe6db54: LoadField: d1 = r1->field_f
    //     0xe6db54: ldur            d1, [x1, #0xf]
    // 0xe6db58: ldur            x1, [fp, #-8]
    // 0xe6db5c: r0 = moveTo()
    //     0xe6db5c: bl              #0xac6914  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::moveTo
    // 0xe6db60: ldur            x0, [fp, #-0x10]
    // 0xe6db64: LoadField: r1 = r0->field_7
    //     0xe6db64: ldur            w1, [x0, #7]
    // 0xe6db68: DecompressPointer r1
    //     0xe6db68: add             x1, x1, HEAP, lsl #32
    // 0xe6db6c: cmp             w1, NULL
    // 0xe6db70: b.eq            #0xe6dc4c
    // 0xe6db74: LoadField: d0 = r1->field_7
    //     0xe6db74: ldur            d0, [x1, #7]
    // 0xe6db78: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xe6db78: ldur            d1, [x1, #0x17]
    // 0xe6db7c: fadd            d2, d0, d1
    // 0xe6db80: LoadField: d0 = r1->field_f
    //     0xe6db80: ldur            d0, [x1, #0xf]
    // 0xe6db84: LoadField: d1 = r1->field_1f
    //     0xe6db84: ldur            d1, [x1, #0x1f]
    // 0xe6db88: fadd            d3, d0, d1
    // 0xe6db8c: ldur            x1, [fp, #-8]
    // 0xe6db90: mov             v0.16b, v2.16b
    // 0xe6db94: mov             v1.16b, v3.16b
    // 0xe6db98: r0 = lineTo()
    //     0xe6db98: bl              #0xac46e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::lineTo
    // 0xe6db9c: ldur            x0, [fp, #-0x10]
    // 0xe6dba0: LoadField: r1 = r0->field_7
    //     0xe6dba0: ldur            w1, [x0, #7]
    // 0xe6dba4: DecompressPointer r1
    //     0xe6dba4: add             x1, x1, HEAP, lsl #32
    // 0xe6dba8: cmp             w1, NULL
    // 0xe6dbac: b.eq            #0xe6dc50
    // 0xe6dbb0: LoadField: d0 = r1->field_7
    //     0xe6dbb0: ldur            d0, [x1, #7]
    // 0xe6dbb4: LoadField: d1 = r1->field_f
    //     0xe6dbb4: ldur            d1, [x1, #0xf]
    // 0xe6dbb8: LoadField: d2 = r1->field_1f
    //     0xe6dbb8: ldur            d2, [x1, #0x1f]
    // 0xe6dbbc: fadd            d3, d1, d2
    // 0xe6dbc0: ldur            x1, [fp, #-8]
    // 0xe6dbc4: mov             v1.16b, v3.16b
    // 0xe6dbc8: r0 = moveTo()
    //     0xe6dbc8: bl              #0xac6914  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::moveTo
    // 0xe6dbcc: ldur            x0, [fp, #-0x10]
    // 0xe6dbd0: LoadField: r1 = r0->field_7
    //     0xe6dbd0: ldur            w1, [x0, #7]
    // 0xe6dbd4: DecompressPointer r1
    //     0xe6dbd4: add             x1, x1, HEAP, lsl #32
    // 0xe6dbd8: cmp             w1, NULL
    // 0xe6dbdc: b.eq            #0xe6dc54
    // 0xe6dbe0: LoadField: d0 = r1->field_7
    //     0xe6dbe0: ldur            d0, [x1, #7]
    // 0xe6dbe4: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xe6dbe4: ldur            d1, [x1, #0x17]
    // 0xe6dbe8: fadd            d2, d0, d1
    // 0xe6dbec: LoadField: d1 = r1->field_f
    //     0xe6dbec: ldur            d1, [x1, #0xf]
    // 0xe6dbf0: ldur            x1, [fp, #-8]
    // 0xe6dbf4: mov             v0.16b, v2.16b
    // 0xe6dbf8: r0 = lineTo()
    //     0xe6dbf8: bl              #0xac46e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::lineTo
    // 0xe6dbfc: ldur            x0, [fp, #-0x10]
    // 0xe6dc00: LoadField: r2 = r0->field_7
    //     0xe6dc00: ldur            w2, [x0, #7]
    // 0xe6dc04: DecompressPointer r2
    //     0xe6dc04: add             x2, x2, HEAP, lsl #32
    // 0xe6dc08: cmp             w2, NULL
    // 0xe6dc0c: b.eq            #0xe6dc58
    // 0xe6dc10: ldur            x1, [fp, #-8]
    // 0xe6dc14: r0 = drawBox()
    //     0xe6dc14: bl              #0xe64ce4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawBox
    // 0xe6dc18: ldur            x1, [fp, #-8]
    // 0xe6dc1c: d0 = 1.000000
    //     0xe6dc1c: fmov            d0, #1.00000000
    // 0xe6dc20: r0 = setLineWidth()
    //     0xe6dc20: bl              #0xe49358  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setLineWidth
    // 0xe6dc24: ldur            x1, [fp, #-8]
    // 0xe6dc28: r0 = strokePath()
    //     0xe6dc28: bl              #0xe492e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::strokePath
    // 0xe6dc2c: r0 = Null
    //     0xe6dc2c: mov             x0, NULL
    // 0xe6dc30: LeaveFrame
    //     0xe6dc30: mov             SP, fp
    //     0xe6dc34: ldp             fp, lr, [SP], #0x10
    // 0xe6dc38: ret
    //     0xe6dc38: ret             
    // 0xe6dc3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6dc3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6dc40: b               #0xe6db18
    // 0xe6dc44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6dc44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6dc48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6dc48: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6dc4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6dc4c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6dc50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6dc50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6dc54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6dc54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6dc58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6dc58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ layout(/* No info */) {
    // ** addr: 0xea0768, size: 0x158
    // 0xea0768: EnterFrame
    //     0xea0768: stp             fp, lr, [SP, #-0x10]!
    //     0xea076c: mov             fp, SP
    // 0xea0770: AllocStack(0x28)
    //     0xea0770: sub             SP, SP, #0x28
    // 0xea0774: d0 = inf
    //     0xea0774: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xea0778: mov             x0, x3
    // 0xea077c: stur            x3, [fp, #-0x10]
    // 0xea0780: mov             x3, x1
    // 0xea0784: stur            x1, [fp, #-8]
    // 0xea0788: CheckStackOverflow
    //     0xea0788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea078c: cmp             SP, x16
    //     0xea0790: b.ls            #0xea0880
    // 0xea0794: LoadField: d1 = r0->field_f
    //     0xea0794: ldur            d1, [x0, #0xf]
    // 0xea0798: fcmp            d0, d1
    // 0xea079c: b.gt            #0xea07a4
    // 0xea07a0: d1 = 400.000000
    //     0xea07a0: ldr             d1, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(400) from 0x4079000000000000
    // 0xea07a4: r1 = inline_Allocate_Double()
    //     0xea07a4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xea07a8: add             x1, x1, #0x10
    //     0xea07ac: cmp             x2, x1
    //     0xea07b0: b.ls            #0xea0888
    //     0xea07b4: str             x1, [THR, #0x50]  ; THR::top
    //     0xea07b8: sub             x1, x1, #0xf
    //     0xea07bc: movz            x2, #0xe15c
    //     0xea07c0: movk            x2, #0x3, lsl #16
    //     0xea07c4: stur            x2, [x1, #-1]
    // 0xea07c8: StoreField: r1->field_7 = d1
    //     0xea07c8: stur            d1, [x1, #7]
    // 0xea07cc: str             x1, [SP]
    // 0xea07d0: mov             x1, x0
    // 0xea07d4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xea07d4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xea07d8: r0 = constrainWidth()
    //     0xea07d8: bl              #0xe8e794  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainWidth
    // 0xea07dc: ldur            x1, [fp, #-0x10]
    // 0xea07e0: stur            d0, [fp, #-0x18]
    // 0xea07e4: LoadField: d1 = r1->field_1f
    //     0xea07e4: ldur            d1, [x1, #0x1f]
    // 0xea07e8: d2 = inf
    //     0xea07e8: ldr             d2, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xea07ec: fcmp            d2, d1
    // 0xea07f0: b.gt            #0xea07f8
    // 0xea07f4: d1 = 400.000000
    //     0xea07f4: ldr             d1, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(400) from 0x4079000000000000
    // 0xea07f8: ldur            x0, [fp, #-8]
    // 0xea07fc: r2 = inline_Allocate_Double()
    //     0xea07fc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xea0800: add             x2, x2, #0x10
    //     0xea0804: cmp             x3, x2
    //     0xea0808: b.ls            #0xea08a4
    //     0xea080c: str             x2, [THR, #0x50]  ; THR::top
    //     0xea0810: sub             x2, x2, #0xf
    //     0xea0814: movz            x3, #0xe15c
    //     0xea0818: movk            x3, #0x3, lsl #16
    //     0xea081c: stur            x3, [x2, #-1]
    // 0xea0820: StoreField: r2->field_7 = d1
    //     0xea0820: stur            d1, [x2, #7]
    // 0xea0824: str             x2, [SP]
    // 0xea0828: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xea0828: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xea082c: r0 = constrainHeight()
    //     0xea082c: bl              #0xe8e66c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainHeight
    // 0xea0830: stur            d0, [fp, #-0x20]
    // 0xea0834: r0 = PdfRect()
    //     0xea0834: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xea0838: StoreField: r0->field_7 = rZR
    //     0xea0838: stur            xzr, [x0, #7]
    // 0xea083c: StoreField: r0->field_f = rZR
    //     0xea083c: stur            xzr, [x0, #0xf]
    // 0xea0840: ldur            d0, [fp, #-0x18]
    // 0xea0844: ArrayStore: r0[0] = d0  ; List_8
    //     0xea0844: stur            d0, [x0, #0x17]
    // 0xea0848: ldur            d0, [fp, #-0x20]
    // 0xea084c: StoreField: r0->field_1f = d0
    //     0xea084c: stur            d0, [x0, #0x1f]
    // 0xea0850: ldur            x1, [fp, #-8]
    // 0xea0854: StoreField: r1->field_7 = r0
    //     0xea0854: stur            w0, [x1, #7]
    //     0xea0858: ldurb           w16, [x1, #-1]
    //     0xea085c: ldurb           w17, [x0, #-1]
    //     0xea0860: and             x16, x17, x16, lsr #2
    //     0xea0864: tst             x16, HEAP, lsr #32
    //     0xea0868: b.eq            #0xea0870
    //     0xea086c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xea0870: r0 = Null
    //     0xea0870: mov             x0, NULL
    // 0xea0874: LeaveFrame
    //     0xea0874: mov             SP, fp
    //     0xea0878: ldp             fp, lr, [SP], #0x10
    // 0xea087c: ret
    //     0xea087c: ret             
    // 0xea0880: r0 = StackOverflowSharedWithFPURegs()
    //     0xea0880: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xea0884: b               #0xea0794
    // 0xea0888: stp             q0, q1, [SP, #-0x20]!
    // 0xea088c: stp             x0, x3, [SP, #-0x10]!
    // 0xea0890: r0 = AllocateDouble()
    //     0xea0890: bl              #0xec2254  ; AllocateDoubleStub
    // 0xea0894: mov             x1, x0
    // 0xea0898: ldp             x0, x3, [SP], #0x10
    // 0xea089c: ldp             q0, q1, [SP], #0x20
    // 0xea08a0: b               #0xea07c8
    // 0xea08a4: stp             q0, q1, [SP, #-0x20]!
    // 0xea08a8: stp             x0, x1, [SP, #-0x10]!
    // 0xea08ac: r0 = AllocateDouble()
    //     0xea08ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xea08b0: mov             x2, x0
    // 0xea08b4: ldp             x0, x1, [SP], #0x10
    // 0xea08b8: ldp             q0, q1, [SP], #0x20
    // 0xea08bc: b               #0xea0820
  }
}
