// lib: , url: package:pdf/src/widgets/svg.dart

// class id: 1050859, size: 0x8
class :: {
}

// class id: 788, size: 0x34, field offset: 0xc
class SvgImage extends Widget {

  late FittedSizes sizes; // offset: 0x30

  factory _ SvgImage(/* No info */) {
    // ** addr: 0xb13e24, size: 0x8c
    // 0xb13e24: EnterFrame
    //     0xb13e24: stp             fp, lr, [SP, #-0x10]!
    //     0xb13e28: mov             fp, SP
    // 0xb13e2c: AllocStack(0x8)
    //     0xb13e2c: sub             SP, SP, #8
    // 0xb13e30: CheckStackOverflow
    //     0xb13e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb13e34: cmp             SP, x16
    //     0xb13e38: b.ls            #0xb13ea8
    // 0xb13e3c: r1 = Null
    //     0xb13e3c: mov             x1, NULL
    // 0xb13e40: r0 = XmlDocument.parse()
    //     0xb13e40: bl              #0xb15164  ; [package:xml/src/xml/nodes/document.dart] XmlDocument::XmlDocument.parse
    // 0xb13e44: mov             x2, x0
    // 0xb13e48: r1 = Null
    //     0xb13e48: mov             x1, NULL
    // 0xb13e4c: r0 = SvgParser()
    //     0xb13e4c: bl              #0xb13ebc  ; [package:pdf/src/svg/parser.dart] SvgParser::SvgParser
    // 0xb13e50: stur            x0, [fp, #-8]
    // 0xb13e54: r0 = SvgImage()
    //     0xb13e54: bl              #0xb13eb0  ; AllocateSvgImageStub -> SvgImage (size=0x34)
    // 0xb13e58: r1 = Sentinel
    //     0xb13e58: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb13e5c: StoreField: r0->field_2f = r1
    //     0xb13e5c: stur            w1, [x0, #0x2f]
    // 0xb13e60: ldur            x1, [fp, #-8]
    // 0xb13e64: StoreField: r0->field_b = r1
    //     0xb13e64: stur            w1, [x0, #0xb]
    // 0xb13e68: r1 = Instance_BoxFit
    //     0xb13e68: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e340] Obj!BoxFit@e2ea01
    //     0xb13e6c: ldr             x1, [x1, #0x340]
    // 0xb13e70: StoreField: r0->field_f = r1
    //     0xb13e70: stur            w1, [x0, #0xf]
    // 0xb13e74: r1 = Instance_Alignment
    //     0xb13e74: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e290] Obj!Alignment@e0c481
    //     0xb13e78: ldr             x1, [x1, #0x290]
    // 0xb13e7c: StoreField: r0->field_13 = r1
    //     0xb13e7c: stur            w1, [x0, #0x13]
    // 0xb13e80: r1 = true
    //     0xb13e80: add             x1, NULL, #0x20  ; true
    // 0xb13e84: ArrayStore: r0[0] = r1  ; List_4
    //     0xb13e84: stur            w1, [x0, #0x17]
    // 0xb13e88: d0 = 90.000000
    //     0xb13e88: ldr             d0, [PP, #0x6520]  ; [pp+0x6520] IMM: double(90) from 0x4056800000000000
    // 0xb13e8c: StoreField: r0->field_1b = d0
    //     0xb13e8c: stur            d0, [x0, #0x1b]
    // 0xb13e90: d0 = 45.000000
    //     0xb13e90: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2b558] IMM: double(45) from 0x4046800000000000
    //     0xb13e94: ldr             d0, [x17, #0x558]
    // 0xb13e98: StoreField: r0->field_23 = d0
    //     0xb13e98: stur            d0, [x0, #0x23]
    // 0xb13e9c: LeaveFrame
    //     0xb13e9c: mov             SP, fp
    //     0xb13ea0: ldp             fp, lr, [SP], #0x10
    // 0xb13ea4: ret
    //     0xb13ea4: ret             
    // 0xb13ea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb13ea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb13eac: b               #0xb13e3c
  }
  _ paint(/* No info */) {
    // ** addr: 0xe6dc5c, size: 0x2f4
    // 0xe6dc5c: EnterFrame
    //     0xe6dc5c: stp             fp, lr, [SP, #-0x10]!
    //     0xe6dc60: mov             fp, SP
    // 0xe6dc64: AllocStack(0x50)
    //     0xe6dc64: sub             SP, SP, #0x50
    // 0xe6dc68: r0 = Instance_Alignment
    //     0xe6dc68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e290] Obj!Alignment@e0c481
    //     0xe6dc6c: ldr             x0, [x0, #0x290]
    // 0xe6dc70: stur            x1, [fp, #-8]
    // 0xe6dc74: stur            x2, [fp, #-0x10]
    // 0xe6dc78: CheckStackOverflow
    //     0xe6dc78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6dc7c: cmp             SP, x16
    //     0xe6dc80: b.ls            #0xe6def4
    // 0xe6dc84: LoadField: d0 = r0->field_7
    //     0xe6dc84: ldur            d0, [x0, #7]
    // 0xe6dc88: stur            d0, [fp, #-0x30]
    // 0xe6dc8c: r0 = Alignment()
    //     0xe6dc8c: bl              #0xe76fc4  ; AllocateAlignmentStub -> Alignment (size=0x18)
    // 0xe6dc90: ldur            d0, [fp, #-0x30]
    // 0xe6dc94: StoreField: r0->field_7 = d0
    //     0xe6dc94: stur            d0, [x0, #7]
    // 0xe6dc98: d0 = -0.000000
    //     0xe6dc98: ldr             d0, [PP, #0x1368]  ; [pp+0x1368] IMM: double(-0) from 0x8000000000000000
    // 0xe6dc9c: StoreField: r0->field_f = d0
    //     0xe6dc9c: stur            d0, [x0, #0xf]
    // 0xe6dca0: ldur            x4, [fp, #-8]
    // 0xe6dca4: LoadField: r1 = r4->field_2f
    //     0xe6dca4: ldur            w1, [x4, #0x2f]
    // 0xe6dca8: DecompressPointer r1
    //     0xe6dca8: add             x1, x1, HEAP, lsl #32
    // 0xe6dcac: r16 = Sentinel
    //     0xe6dcac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe6dcb0: cmp             w1, w16
    // 0xe6dcb4: b.eq            #0xe6defc
    // 0xe6dcb8: LoadField: r2 = r1->field_7
    //     0xe6dcb8: ldur            w2, [x1, #7]
    // 0xe6dcbc: DecompressPointer r2
    //     0xe6dcbc: add             x2, x2, HEAP, lsl #32
    // 0xe6dcc0: cmp             w2, NULL
    // 0xe6dcc4: b.eq            #0xe6df08
    // 0xe6dcc8: LoadField: r5 = r4->field_b
    //     0xe6dcc8: ldur            w5, [x4, #0xb]
    // 0xe6dccc: DecompressPointer r5
    //     0xe6dccc: add             x5, x5, HEAP, lsl #32
    // 0xe6dcd0: stur            x5, [fp, #-0x18]
    // 0xe6dcd4: LoadField: r3 = r5->field_7
    //     0xe6dcd4: ldur            w3, [x5, #7]
    // 0xe6dcd8: DecompressPointer r3
    //     0xe6dcd8: add             x3, x3, HEAP, lsl #32
    // 0xe6dcdc: mov             x1, x0
    // 0xe6dce0: r0 = inscribe()
    //     0xe6dce0: bl              #0xe68de0  ; [package:pdf/src/widgets/geometry.dart] Alignment::inscribe
    // 0xe6dce4: mov             x1, x0
    // 0xe6dce8: ldur            x0, [fp, #-8]
    // 0xe6dcec: LoadField: r2 = r0->field_2f
    //     0xe6dcec: ldur            w2, [x0, #0x2f]
    // 0xe6dcf0: DecompressPointer r2
    //     0xe6dcf0: add             x2, x2, HEAP, lsl #32
    // 0xe6dcf4: LoadField: r3 = r2->field_b
    //     0xe6dcf4: ldur            w3, [x2, #0xb]
    // 0xe6dcf8: DecompressPointer r3
    //     0xe6dcf8: add             x3, x3, HEAP, lsl #32
    // 0xe6dcfc: cmp             w3, NULL
    // 0xe6dd00: b.eq            #0xe6df0c
    // 0xe6dd04: LoadField: d0 = r3->field_7
    //     0xe6dd04: ldur            d0, [x3, #7]
    // 0xe6dd08: LoadField: r4 = r2->field_7
    //     0xe6dd08: ldur            w4, [x2, #7]
    // 0xe6dd0c: DecompressPointer r4
    //     0xe6dd0c: add             x4, x4, HEAP, lsl #32
    // 0xe6dd10: cmp             w4, NULL
    // 0xe6dd14: b.eq            #0xe6df10
    // 0xe6dd18: LoadField: d1 = r4->field_7
    //     0xe6dd18: ldur            d1, [x4, #7]
    // 0xe6dd1c: fdiv            d2, d0, d1
    // 0xe6dd20: stur            d2, [fp, #-0x48]
    // 0xe6dd24: LoadField: d0 = r3->field_f
    //     0xe6dd24: ldur            d0, [x3, #0xf]
    // 0xe6dd28: LoadField: d1 = r4->field_f
    //     0xe6dd28: ldur            d1, [x4, #0xf]
    // 0xe6dd2c: fdiv            d3, d0, d1
    // 0xe6dd30: stur            d3, [fp, #-0x40]
    // 0xe6dd34: LoadField: d0 = r1->field_7
    //     0xe6dd34: ldur            d0, [x1, #7]
    // 0xe6dd38: fmul            d1, d0, d2
    // 0xe6dd3c: stur            d1, [fp, #-0x38]
    // 0xe6dd40: LoadField: d0 = r1->field_f
    //     0xe6dd40: ldur            d0, [x1, #0xf]
    // 0xe6dd44: fmul            d4, d0, d3
    // 0xe6dd48: stur            d4, [fp, #-0x30]
    // 0xe6dd4c: r0 = Matrix4()
    //     0xe6dd4c: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe6dd50: r4 = 32
    //     0xe6dd50: movz            x4, #0x20
    // 0xe6dd54: stur            x0, [fp, #-0x20]
    // 0xe6dd58: r0 = AllocateFloat64Array()
    //     0xe6dd58: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6dd5c: mov             x1, x0
    // 0xe6dd60: ldur            x0, [fp, #-0x20]
    // 0xe6dd64: StoreField: r0->field_7 = r1
    //     0xe6dd64: stur            w1, [x0, #7]
    // 0xe6dd68: mov             x1, x0
    // 0xe6dd6c: r0 = setIdentity()
    //     0xe6dd6c: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe6dd70: ldur            x0, [fp, #-8]
    // 0xe6dd74: LoadField: r1 = r0->field_7
    //     0xe6dd74: ldur            w1, [x0, #7]
    // 0xe6dd78: DecompressPointer r1
    //     0xe6dd78: add             x1, x1, HEAP, lsl #32
    // 0xe6dd7c: cmp             w1, NULL
    // 0xe6dd80: b.eq            #0xe6df14
    // 0xe6dd84: LoadField: d0 = r1->field_7
    //     0xe6dd84: ldur            d0, [x1, #7]
    // 0xe6dd88: ldur            d1, [fp, #-0x38]
    // 0xe6dd8c: fsub            d2, d0, d1
    // 0xe6dd90: LoadField: d0 = r1->field_f
    //     0xe6dd90: ldur            d0, [x1, #0xf]
    // 0xe6dd94: ldur            d1, [fp, #-0x30]
    // 0xe6dd98: fadd            d3, d0, d1
    // 0xe6dd9c: LoadField: d0 = r1->field_1f
    //     0xe6dd9c: ldur            d0, [x1, #0x1f]
    // 0xe6dda0: fadd            d1, d3, d0
    // 0xe6dda4: ldur            x1, [fp, #-0x20]
    // 0xe6dda8: mov             v0.16b, v2.16b
    // 0xe6ddac: r0 = translate()
    //     0xe6ddac: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe6ddb0: ldur            d0, [fp, #-0x40]
    // 0xe6ddb4: fneg            d1, d0
    // 0xe6ddb8: ldur            d0, [fp, #-0x48]
    // 0xe6ddbc: r2 = inline_Allocate_Double()
    //     0xe6ddbc: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xe6ddc0: add             x2, x2, #0x10
    //     0xe6ddc4: cmp             x0, x2
    //     0xe6ddc8: b.ls            #0xe6df18
    //     0xe6ddcc: str             x2, [THR, #0x50]  ; THR::top
    //     0xe6ddd0: sub             x2, x2, #0xf
    //     0xe6ddd4: movz            x0, #0xe15c
    //     0xe6ddd8: movk            x0, #0x3, lsl #16
    //     0xe6dddc: stur            x0, [x2, #-1]
    // 0xe6dde0: StoreField: r2->field_7 = d0
    //     0xe6dde0: stur            d0, [x2, #7]
    // 0xe6dde4: r0 = inline_Allocate_Double()
    //     0xe6dde4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6dde8: add             x0, x0, #0x10
    //     0xe6ddec: cmp             x1, x0
    //     0xe6ddf0: b.ls            #0xe6df2c
    //     0xe6ddf4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6ddf8: sub             x0, x0, #0xf
    //     0xe6ddfc: movz            x1, #0xe15c
    //     0xe6de00: movk            x1, #0x3, lsl #16
    //     0xe6de04: stur            x1, [x0, #-1]
    // 0xe6de08: StoreField: r0->field_7 = d1
    //     0xe6de08: stur            d1, [x0, #7]
    // 0xe6de0c: str             x0, [SP]
    // 0xe6de10: ldur            x1, [fp, #-0x20]
    // 0xe6de14: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe6de14: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe6de18: r0 = scale()
    //     0xe6de18: bl              #0x645258  ; [package:vector_math/vector_math_64.dart] Matrix4::scale
    // 0xe6de1c: ldur            x0, [fp, #-0x10]
    // 0xe6de20: LoadField: r2 = r0->field_b
    //     0xe6de20: ldur            w2, [x0, #0xb]
    // 0xe6de24: DecompressPointer r2
    //     0xe6de24: add             x2, x2, HEAP, lsl #32
    // 0xe6de28: stur            x2, [fp, #-0x28]
    // 0xe6de2c: cmp             w2, NULL
    // 0xe6de30: b.eq            #0xe6df44
    // 0xe6de34: mov             x1, x2
    // 0xe6de38: r0 = saveContext()
    //     0xe6de38: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe6de3c: ldur            x0, [fp, #-8]
    // 0xe6de40: LoadField: r2 = r0->field_7
    //     0xe6de40: ldur            w2, [x0, #7]
    // 0xe6de44: DecompressPointer r2
    //     0xe6de44: add             x2, x2, HEAP, lsl #32
    // 0xe6de48: cmp             w2, NULL
    // 0xe6de4c: b.eq            #0xe6df48
    // 0xe6de50: ldur            x1, [fp, #-0x28]
    // 0xe6de54: r0 = drawBox()
    //     0xe6de54: bl              #0xe64ce4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawBox
    // 0xe6de58: ldur            x1, [fp, #-0x28]
    // 0xe6de5c: r0 = clipPath()
    //     0xe6de5c: bl              #0xe479b0  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::clipPath
    // 0xe6de60: ldur            x1, [fp, #-0x28]
    // 0xe6de64: ldur            x2, [fp, #-0x20]
    // 0xe6de68: r0 = setTransform()
    //     0xe6de68: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe6de6c: ldur            x0, [fp, #-0x10]
    // 0xe6de70: LoadField: r5 = r0->field_13
    //     0xe6de70: ldur            w5, [x0, #0x13]
    // 0xe6de74: DecompressPointer r5
    //     0xe6de74: add             x5, x5, HEAP, lsl #32
    // 0xe6de78: stur            x5, [fp, #-8]
    // 0xe6de7c: LoadField: r1 = r0->field_7
    //     0xe6de7c: ldur            w1, [x0, #7]
    // 0xe6de80: DecompressPointer r1
    //     0xe6de80: add             x1, x1, HEAP, lsl #32
    // 0xe6de84: cmp             w1, NULL
    // 0xe6de88: b.eq            #0xe6df4c
    // 0xe6de8c: r0 = PdfRect()
    //     0xe6de8c: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe6de90: stur            x0, [fp, #-0x10]
    // 0xe6de94: StoreField: r0->field_7 = rZR
    //     0xe6de94: stur            xzr, [x0, #7]
    // 0xe6de98: StoreField: r0->field_f = rZR
    //     0xe6de98: stur            xzr, [x0, #0xf]
    // 0xe6de9c: d0 = 595.275591
    //     0xe6de9c: add             x17, PP, #0x33, lsl #12  ; [pp+0x338f8] IMM: double(595.275590551181) from 0x40829a3468d1a346
    //     0xe6dea0: ldr             d0, [x17, #0x8f8]
    // 0xe6dea4: ArrayStore: r0[0] = d0  ; List_8
    //     0xe6dea4: stur            d0, [x0, #0x17]
    // 0xe6dea8: d0 = 841.889764
    //     0xe6dea8: add             x17, PP, #0x33, lsl #12  ; [pp+0x33900] IMM: double(841.8897637795275) from 0x408a4f1e3c78f1e3
    //     0xe6deac: ldr             d0, [x17, #0x900]
    // 0xe6deb0: StoreField: r0->field_1f = d0
    //     0xe6deb0: stur            d0, [x0, #0x1f]
    // 0xe6deb4: r0 = SvgPainter()
    //     0xe6deb4: bl              #0xe76fb8  ; AllocateSvgPainterStub -> SvgPainter (size=0x20)
    // 0xe6deb8: mov             x1, x0
    // 0xe6debc: ldur            x2, [fp, #-0x18]
    // 0xe6dec0: ldur            x3, [fp, #-0x28]
    // 0xe6dec4: ldur            x5, [fp, #-8]
    // 0xe6dec8: ldur            x6, [fp, #-0x10]
    // 0xe6decc: stur            x0, [fp, #-8]
    // 0xe6ded0: r0 = SvgPainter()
    //     0xe6ded0: bl              #0xe76ea8  ; [package:pdf/src/svg/painter.dart] SvgPainter::SvgPainter
    // 0xe6ded4: ldur            x1, [fp, #-8]
    // 0xe6ded8: r0 = paint()
    //     0xe6ded8: bl              #0xe6df50  ; [package:pdf/src/svg/painter.dart] SvgPainter::paint
    // 0xe6dedc: ldur            x1, [fp, #-0x28]
    // 0xe6dee0: r0 = restoreContext()
    //     0xe6dee0: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe6dee4: r0 = Null
    //     0xe6dee4: mov             x0, NULL
    // 0xe6dee8: LeaveFrame
    //     0xe6dee8: mov             SP, fp
    //     0xe6deec: ldp             fp, lr, [SP], #0x10
    // 0xe6def0: ret
    //     0xe6def0: ret             
    // 0xe6def4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6def4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6def8: b               #0xe6dc84
    // 0xe6defc: r9 = sizes
    //     0xe6defc: add             x9, PP, #0x3e, lsl #12  ; [pp+0x3ea18] Field <SvgImage.sizes>: late (offset: 0x30)
    //     0xe6df00: ldr             x9, [x9, #0xa18]
    // 0xe6df04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe6df04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe6df08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6df08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6df0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6df0c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6df10: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe6df10: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe6df14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6df14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6df18: stp             q0, q1, [SP, #-0x20]!
    // 0xe6df1c: r0 = AllocateDouble()
    //     0xe6df1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6df20: mov             x2, x0
    // 0xe6df24: ldp             q0, q1, [SP], #0x20
    // 0xe6df28: b               #0xe6dde0
    // 0xe6df2c: SaveReg d1
    //     0xe6df2c: str             q1, [SP, #-0x10]!
    // 0xe6df30: SaveReg r2
    //     0xe6df30: str             x2, [SP, #-8]!
    // 0xe6df34: r0 = AllocateDouble()
    //     0xe6df34: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6df38: RestoreReg r2
    //     0xe6df38: ldr             x2, [SP], #8
    // 0xe6df3c: RestoreReg d1
    //     0xe6df3c: ldr             q1, [SP], #0x10
    // 0xe6df40: b               #0xe6de08
    // 0xe6df44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6df44: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6df48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6df48: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6df4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6df4c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ layout(/* No info */) {
    // ** addr: 0xea08c0, size: 0x144
    // 0xea08c0: EnterFrame
    //     0xea08c0: stp             fp, lr, [SP, #-0x10]!
    //     0xea08c4: mov             fp, SP
    // 0xea08c8: AllocStack(0x38)
    //     0xea08c8: sub             SP, SP, #0x38
    // 0xea08cc: SetupParameters(SvgImage this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xea08cc: mov             x0, x3
    //     0xea08d0: stur            x3, [fp, #-0x10]
    //     0xea08d4: mov             x3, x1
    //     0xea08d8: stur            x1, [fp, #-8]
    // 0xea08dc: CheckStackOverflow
    //     0xea08dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea08e0: cmp             SP, x16
    //     0xea08e4: b.ls            #0xea09f8
    // 0xea08e8: r16 = 90.000000
    //     0xea08e8: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ed98] 90
    //     0xea08ec: ldr             x16, [x16, #0xd98]
    // 0xea08f0: str             x16, [SP]
    // 0xea08f4: mov             x1, x0
    // 0xea08f8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xea08f8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xea08fc: r0 = constrainWidth()
    //     0xea08fc: bl              #0xe8e794  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainWidth
    // 0xea0900: stur            d0, [fp, #-0x18]
    // 0xea0904: r16 = 45.000000
    //     0xea0904: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eda0] 45
    //     0xea0908: ldr             x16, [x16, #0xda0]
    // 0xea090c: str             x16, [SP]
    // 0xea0910: ldur            x1, [fp, #-0x10]
    // 0xea0914: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xea0914: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xea0918: r0 = constrainHeight()
    //     0xea0918: bl              #0xe8e66c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainHeight
    // 0xea091c: ldur            x0, [fp, #-8]
    // 0xea0920: stur            d0, [fp, #-0x30]
    // 0xea0924: LoadField: r1 = r0->field_b
    //     0xea0924: ldur            w1, [x0, #0xb]
    // 0xea0928: DecompressPointer r1
    //     0xea0928: add             x1, x1, HEAP, lsl #32
    // 0xea092c: LoadField: r2 = r1->field_7
    //     0xea092c: ldur            w2, [x1, #7]
    // 0xea0930: DecompressPointer r2
    //     0xea0930: add             x2, x2, HEAP, lsl #32
    // 0xea0934: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xea0934: ldur            d1, [x2, #0x17]
    // 0xea0938: stur            d1, [fp, #-0x28]
    // 0xea093c: LoadField: d2 = r2->field_1f
    //     0xea093c: ldur            d2, [x2, #0x1f]
    // 0xea0940: stur            d2, [fp, #-0x20]
    // 0xea0944: r0 = PdfPoint()
    //     0xea0944: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xea0948: ldur            d0, [fp, #-0x28]
    // 0xea094c: stur            x0, [fp, #-0x10]
    // 0xea0950: StoreField: r0->field_7 = d0
    //     0xea0950: stur            d0, [x0, #7]
    // 0xea0954: ldur            d0, [fp, #-0x20]
    // 0xea0958: StoreField: r0->field_f = d0
    //     0xea0958: stur            d0, [x0, #0xf]
    // 0xea095c: r0 = PdfPoint()
    //     0xea095c: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xea0960: ldur            d0, [fp, #-0x18]
    // 0xea0964: StoreField: r0->field_7 = d0
    //     0xea0964: stur            d0, [x0, #7]
    // 0xea0968: ldur            d0, [fp, #-0x30]
    // 0xea096c: StoreField: r0->field_f = d0
    //     0xea096c: stur            d0, [x0, #0xf]
    // 0xea0970: ldur            x2, [fp, #-0x10]
    // 0xea0974: mov             x3, x0
    // 0xea0978: r1 = Instance_BoxFit
    //     0xea0978: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e340] Obj!BoxFit@e2ea01
    //     0xea097c: ldr             x1, [x1, #0x340]
    // 0xea0980: r0 = applyBoxFit()
    //     0xea0980: bl              #0xe68f18  ; [package:pdf/src/widgets/geometry.dart] ::applyBoxFit
    // 0xea0984: mov             x1, x0
    // 0xea0988: ldur            x4, [fp, #-8]
    // 0xea098c: StoreField: r4->field_2f = r0
    //     0xea098c: stur            w0, [x4, #0x2f]
    //     0xea0990: ldurb           w16, [x4, #-1]
    //     0xea0994: ldurb           w17, [x0, #-1]
    //     0xea0998: and             x16, x17, x16, lsr #2
    //     0xea099c: tst             x16, HEAP, lsr #32
    //     0xea09a0: b.eq            #0xea09a8
    //     0xea09a4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xea09a8: LoadField: r3 = r1->field_b
    //     0xea09a8: ldur            w3, [x1, #0xb]
    // 0xea09ac: DecompressPointer r3
    //     0xea09ac: add             x3, x3, HEAP, lsl #32
    // 0xea09b0: cmp             w3, NULL
    // 0xea09b4: b.eq            #0xea0a00
    // 0xea09b8: r1 = Null
    //     0xea09b8: mov             x1, NULL
    // 0xea09bc: r2 = Instance_PdfPoint
    //     0xea09bc: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xea09c0: ldr             x2, [x2, #0x730]
    // 0xea09c4: r0 = PdfRect.fromPoints()
    //     0xea09c4: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xea09c8: ldur            x1, [fp, #-8]
    // 0xea09cc: StoreField: r1->field_7 = r0
    //     0xea09cc: stur            w0, [x1, #7]
    //     0xea09d0: ldurb           w16, [x1, #-1]
    //     0xea09d4: ldurb           w17, [x0, #-1]
    //     0xea09d8: and             x16, x17, x16, lsr #2
    //     0xea09dc: tst             x16, HEAP, lsr #32
    //     0xea09e0: b.eq            #0xea09e8
    //     0xea09e4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xea09e8: r0 = Null
    //     0xea09e8: mov             x0, NULL
    // 0xea09ec: LeaveFrame
    //     0xea09ec: mov             SP, fp
    //     0xea09f0: ldp             fp, lr, [SP], #0x10
    // 0xea09f4: ret
    //     0xea09f4: ret             
    // 0xea09f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea09f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea09fc: b               #0xea08e8
    // 0xea0a00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea0a00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
