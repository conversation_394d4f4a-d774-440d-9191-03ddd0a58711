// lib: , url: package:pdf/src/widgets/table.dart

// class id: 1050860, size: 0x8
class :: {
}

// class id: 776, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class TableColumnWidth extends Object {
}

// class id: 777, size: 0xc, field offset: 0x8
//   const constructor, 
class IntrinsicColumnWidth extends TableColumnWidth {
}

// class id: 779, size: 0x18, field offset: 0x8
//   const constructor, 
class TableRow extends Object {
}

// class id: 799, size: 0x30, field offset: 0xc
class Table extends _SingleChildWidget&Widget&SpanningWidget {

  [closure] double <anonymous closure>(dynamic, double, double) {
    // ** addr: 0x756934, size: 0x60
    // 0x756934: EnterFrame
    //     0x756934: stp             fp, lr, [SP, #-0x10]!
    //     0x756938: mov             fp, SP
    // 0x75693c: ldr             x1, [fp, #0x18]
    // 0x756940: LoadField: d0 = r1->field_7
    //     0x756940: ldur            d0, [x1, #7]
    // 0x756944: ldr             x1, [fp, #0x10]
    // 0x756948: LoadField: d1 = r1->field_7
    //     0x756948: ldur            d1, [x1, #7]
    // 0x75694c: fadd            d2, d0, d1
    // 0x756950: r0 = inline_Allocate_Double()
    //     0x756950: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x756954: add             x0, x0, #0x10
    //     0x756958: cmp             x1, x0
    //     0x75695c: b.ls            #0x756984
    //     0x756960: str             x0, [THR, #0x50]  ; THR::top
    //     0x756964: sub             x0, x0, #0xf
    //     0x756968: movz            x1, #0xe15c
    //     0x75696c: movk            x1, #0x3, lsl #16
    //     0x756970: stur            x1, [x0, #-1]
    // 0x756974: StoreField: r0->field_7 = d2
    //     0x756974: stur            d2, [x0, #7]
    // 0x756978: LeaveFrame
    //     0x756978: mov             SP, fp
    //     0x75697c: ldp             fp, lr, [SP], #0x10
    // 0x756980: ret
    //     0x756980: ret             
    // 0x756984: SaveReg d2
    //     0x756984: str             q2, [SP, #-0x10]!
    // 0x756988: r0 = AllocateDouble()
    //     0x756988: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75698c: RestoreReg d2
    //     0x75698c: ldr             q2, [SP], #0x10
    // 0x756990: b               #0x756974
  }
  _ Table(/* No info */) {
    // ** addr: 0xb136a8, size: 0x108
    // 0xb136a8: EnterFrame
    //     0xb136a8: stp             fp, lr, [SP, #-0x10]!
    //     0xb136ac: mov             fp, SP
    // 0xb136b0: AllocStack(0x10)
    //     0xb136b0: sub             SP, SP, #0x10
    // 0xb136b4: SetupParameters(Table this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb136b4: mov             x3, x1
    //     0xb136b8: mov             x0, x2
    //     0xb136bc: stur            x1, [fp, #-8]
    //     0xb136c0: stur            x2, [fp, #-0x10]
    // 0xb136c4: CheckStackOverflow
    //     0xb136c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb136c8: cmp             SP, x16
    //     0xb136cc: b.ls            #0xb137a8
    // 0xb136d0: r1 = <double>
    //     0xb136d0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xb136d4: r2 = 0
    //     0xb136d4: movz            x2, #0
    // 0xb136d8: r0 = _GrowableList()
    //     0xb136d8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb136dc: ldur            x3, [fp, #-8]
    // 0xb136e0: StoreField: r3->field_1b = r0
    //     0xb136e0: stur            w0, [x3, #0x1b]
    //     0xb136e4: ldurb           w16, [x3, #-1]
    //     0xb136e8: ldurb           w17, [x0, #-1]
    //     0xb136ec: and             x16, x17, x16, lsr #2
    //     0xb136f0: tst             x16, HEAP, lsr #32
    //     0xb136f4: b.eq            #0xb136fc
    //     0xb136f8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xb136fc: r1 = <double>
    //     0xb136fc: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xb13700: r2 = 0
    //     0xb13700: movz            x2, #0
    // 0xb13704: r0 = _GrowableList()
    //     0xb13704: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb13708: ldur            x1, [fp, #-8]
    // 0xb1370c: StoreField: r1->field_1f = r0
    //     0xb1370c: stur            w0, [x1, #0x1f]
    //     0xb13710: ldurb           w16, [x1, #-1]
    //     0xb13714: ldurb           w17, [x0, #-1]
    //     0xb13718: and             x16, x17, x16, lsr #2
    //     0xb1371c: tst             x16, HEAP, lsr #32
    //     0xb13720: b.eq            #0xb13728
    //     0xb13724: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb13728: r0 = TableContext()
    //     0xb13728: bl              #0xb137b0  ; AllocateTableContextStub -> TableContext (size=0x18)
    // 0xb1372c: StoreField: r0->field_7 = rZR
    //     0xb1372c: stur            xzr, [x0, #7]
    // 0xb13730: StoreField: r0->field_f = rZR
    //     0xb13730: stur            xzr, [x0, #0xf]
    // 0xb13734: ldur            x1, [fp, #-8]
    // 0xb13738: StoreField: r1->field_23 = r0
    //     0xb13738: stur            w0, [x1, #0x23]
    //     0xb1373c: ldurb           w16, [x1, #-1]
    //     0xb13740: ldurb           w17, [x0, #-1]
    //     0xb13744: and             x16, x17, x16, lsr #2
    //     0xb13748: tst             x16, HEAP, lsr #32
    //     0xb1374c: b.eq            #0xb13754
    //     0xb13750: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb13754: ldur            x0, [fp, #-0x10]
    // 0xb13758: StoreField: r1->field_b = r0
    //     0xb13758: stur            w0, [x1, #0xb]
    //     0xb1375c: ldurb           w16, [x1, #-1]
    //     0xb13760: ldurb           w17, [x0, #-1]
    //     0xb13764: and             x16, x17, x16, lsr #2
    //     0xb13768: tst             x16, HEAP, lsr #32
    //     0xb1376c: b.eq            #0xb13774
    //     0xb13770: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb13774: r2 = Instance_TableCellVerticalAlignment
    //     0xb13774: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e2c0] Obj!TableCellVerticalAlignment@e2e681
    //     0xb13778: ldr             x2, [x2, #0x2c0]
    // 0xb1377c: StoreField: r1->field_13 = r2
    //     0xb1377c: stur            w2, [x1, #0x13]
    // 0xb13780: r2 = Instance_IntrinsicColumnWidth
    //     0xb13780: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e2c8] Obj!IntrinsicColumnWidth@e0c461
    //     0xb13784: ldr             x2, [x2, #0x2c8]
    // 0xb13788: StoreField: r1->field_27 = r2
    //     0xb13788: stur            w2, [x1, #0x27]
    // 0xb1378c: r2 = Instance_TableWidth
    //     0xb1378c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e2d0] Obj!TableWidth@e2e661
    //     0xb13790: ldr             x2, [x2, #0x2d0]
    // 0xb13794: ArrayStore: r1[0] = r2  ; List_4
    //     0xb13794: stur            w2, [x1, #0x17]
    // 0xb13798: r0 = Null
    //     0xb13798: mov             x0, NULL
    // 0xb1379c: LeaveFrame
    //     0xb1379c: mov             SP, fp
    //     0xb137a0: ldp             fp, lr, [SP], #0x10
    // 0xb137a4: ret
    //     0xb137a4: ret             
    // 0xb137a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb137a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb137ac: b               #0xb136d0
  }
  _ restoreContext(/* No info */) {
    // ** addr: 0xe54ac8, size: 0xa0
    // 0xe54ac8: EnterFrame
    //     0xe54ac8: stp             fp, lr, [SP, #-0x10]!
    //     0xe54acc: mov             fp, SP
    // 0xe54ad0: AllocStack(0x18)
    //     0xe54ad0: sub             SP, SP, #0x18
    // 0xe54ad4: SetupParameters(Table this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xe54ad4: mov             x4, x1
    //     0xe54ad8: mov             x3, x2
    //     0xe54adc: stur            x1, [fp, #-8]
    //     0xe54ae0: stur            x2, [fp, #-0x10]
    // 0xe54ae4: CheckStackOverflow
    //     0xe54ae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe54ae8: cmp             SP, x16
    //     0xe54aec: b.ls            #0xe54b60
    // 0xe54af0: mov             x0, x3
    // 0xe54af4: r2 = Null
    //     0xe54af4: mov             x2, NULL
    // 0xe54af8: r1 = Null
    //     0xe54af8: mov             x1, NULL
    // 0xe54afc: r4 = 60
    //     0xe54afc: movz            x4, #0x3c
    // 0xe54b00: branchIfSmi(r0, 0xe54b0c)
    //     0xe54b00: tbz             w0, #0, #0xe54b0c
    // 0xe54b04: r4 = LoadClassIdInstr(r0)
    //     0xe54b04: ldur            x4, [x0, #-1]
    //     0xe54b08: ubfx            x4, x4, #0xc, #0x14
    // 0xe54b0c: cmp             x4, #0x336
    // 0xe54b10: b.eq            #0xe54b28
    // 0xe54b14: r8 = TableContext
    //     0xe54b14: add             x8, PP, #0x33, lsl #12  ; [pp+0x33758] Type: TableContext
    //     0xe54b18: ldr             x8, [x8, #0x758]
    // 0xe54b1c: r3 = Null
    //     0xe54b1c: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ea08] Null
    //     0xe54b20: ldr             x3, [x3, #0xa08]
    // 0xe54b24: r0 = DefaultTypeTest()
    //     0xe54b24: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe54b28: ldur            x0, [fp, #-8]
    // 0xe54b2c: LoadField: r3 = r0->field_23
    //     0xe54b2c: ldur            w3, [x0, #0x23]
    // 0xe54b30: DecompressPointer r3
    //     0xe54b30: add             x3, x3, HEAP, lsl #32
    // 0xe54b34: mov             x1, x3
    // 0xe54b38: ldur            x2, [fp, #-0x10]
    // 0xe54b3c: stur            x3, [fp, #-0x18]
    // 0xe54b40: r0 = apply()
    //     0xe54b40: bl              #0xeabed8  ; [package:pdf/src/widgets/table.dart] TableContext::apply
    // 0xe54b44: ldur            x1, [fp, #-0x18]
    // 0xe54b48: LoadField: r2 = r1->field_f
    //     0xe54b48: ldur            x2, [x1, #0xf]
    // 0xe54b4c: StoreField: r1->field_7 = r2
    //     0xe54b4c: stur            x2, [x1, #7]
    // 0xe54b50: r0 = Null
    //     0xe54b50: mov             x0, NULL
    // 0xe54b54: LeaveFrame
    //     0xe54b54: mov             SP, fp
    //     0xe54b58: ldp             fp, lr, [SP], #0x10
    // 0xe54b5c: ret
    //     0xe54b5c: ret             
    // 0xe54b60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe54b60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe54b64: b               #0xe54af0
  }
  _ paint(/* No info */) {
    // ** addr: 0xe63a40, size: 0xdc0
    // 0xe63a40: EnterFrame
    //     0xe63a40: stp             fp, lr, [SP, #-0x10]!
    //     0xe63a44: mov             fp, SP
    // 0xe63a48: AllocStack(0xb0)
    //     0xe63a48: sub             SP, SP, #0xb0
    // 0xe63a4c: SetupParameters(Table this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe63a4c: mov             x3, x1
    //     0xe63a50: mov             x0, x2
    //     0xe63a54: stur            x1, [fp, #-8]
    //     0xe63a58: stur            x2, [fp, #-0x10]
    // 0xe63a5c: CheckStackOverflow
    //     0xe63a5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe63a60: cmp             SP, x16
    //     0xe63a64: b.ls            #0xe64678
    // 0xe63a68: mov             x1, x3
    // 0xe63a6c: mov             x2, x0
    // 0xe63a70: r0 = forceCompileTimeTreeShaking()
    //     0xe63a70: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xe63a74: ldur            x0, [fp, #-8]
    // 0xe63a78: LoadField: r1 = r0->field_23
    //     0xe63a78: ldur            w1, [x0, #0x23]
    // 0xe63a7c: DecompressPointer r1
    //     0xe63a7c: add             x1, x1, HEAP, lsl #32
    // 0xe63a80: stur            x1, [fp, #-0x18]
    // 0xe63a84: LoadField: r2 = r1->field_f
    //     0xe63a84: ldur            x2, [x1, #0xf]
    // 0xe63a88: cbnz            x2, #0xe63a9c
    // 0xe63a8c: r0 = Null
    //     0xe63a8c: mov             x0, NULL
    // 0xe63a90: LeaveFrame
    //     0xe63a90: mov             SP, fp
    //     0xe63a94: ldp             fp, lr, [SP], #0x10
    // 0xe63a98: ret
    //     0xe63a98: ret             
    // 0xe63a9c: ldur            x2, [fp, #-0x10]
    // 0xe63aa0: r0 = Matrix4()
    //     0xe63aa0: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe63aa4: r4 = 32
    //     0xe63aa4: movz            x4, #0x20
    // 0xe63aa8: stur            x0, [fp, #-0x20]
    // 0xe63aac: r0 = AllocateFloat64Array()
    //     0xe63aac: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe63ab0: mov             x1, x0
    // 0xe63ab4: ldur            x0, [fp, #-0x20]
    // 0xe63ab8: StoreField: r0->field_7 = r1
    //     0xe63ab8: stur            w1, [x0, #7]
    // 0xe63abc: mov             x1, x0
    // 0xe63ac0: r0 = setIdentity()
    //     0xe63ac0: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe63ac4: ldur            x0, [fp, #-8]
    // 0xe63ac8: LoadField: r1 = r0->field_7
    //     0xe63ac8: ldur            w1, [x0, #7]
    // 0xe63acc: DecompressPointer r1
    //     0xe63acc: add             x1, x1, HEAP, lsl #32
    // 0xe63ad0: cmp             w1, NULL
    // 0xe63ad4: b.eq            #0xe64680
    // 0xe63ad8: LoadField: d0 = r1->field_7
    //     0xe63ad8: ldur            d0, [x1, #7]
    // 0xe63adc: LoadField: d1 = r1->field_f
    //     0xe63adc: ldur            d1, [x1, #0xf]
    // 0xe63ae0: ldur            x1, [fp, #-0x20]
    // 0xe63ae4: r0 = translate()
    //     0xe63ae4: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe63ae8: ldur            x2, [fp, #-0x10]
    // 0xe63aec: LoadField: r0 = r2->field_b
    //     0xe63aec: ldur            w0, [x2, #0xb]
    // 0xe63af0: DecompressPointer r0
    //     0xe63af0: add             x0, x0, HEAP, lsl #32
    // 0xe63af4: stur            x0, [fp, #-0x28]
    // 0xe63af8: cmp             w0, NULL
    // 0xe63afc: b.eq            #0xe64684
    // 0xe63b00: mov             x1, x0
    // 0xe63b04: r0 = saveContext()
    //     0xe63b04: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe63b08: ldur            x1, [fp, #-0x28]
    // 0xe63b0c: ldur            x2, [fp, #-0x20]
    // 0xe63b10: r0 = setTransform()
    //     0xe63b10: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe63b14: ldur            x1, [fp, #-8]
    // 0xe63b18: LoadField: r2 = r1->field_b
    //     0xe63b18: ldur            w2, [x1, #0xb]
    // 0xe63b1c: DecompressPointer r2
    //     0xe63b1c: add             x2, x2, HEAP, lsl #32
    // 0xe63b20: stur            x2, [fp, #-0x90]
    // 0xe63b24: LoadField: r0 = r2->field_b
    //     0xe63b24: ldur            w0, [x2, #0xb]
    // 0xe63b28: r3 = LoadInt32Instr(r0)
    //     0xe63b28: sbfx            x3, x0, #1, #0x1f
    // 0xe63b2c: ldur            x4, [fp, #-0x28]
    // 0xe63b30: stur            x3, [fp, #-0x88]
    // 0xe63b34: LoadField: r5 = r4->field_13
    //     0xe63b34: ldur            w5, [x4, #0x13]
    // 0xe63b38: DecompressPointer r5
    //     0xe63b38: add             x5, x5, HEAP, lsl #32
    // 0xe63b3c: stur            x5, [fp, #-0x80]
    // 0xe63b40: LoadField: r6 = r4->field_b
    //     0xe63b40: ldur            w6, [x4, #0xb]
    // 0xe63b44: DecompressPointer r6
    //     0xe63b44: add             x6, x6, HEAP, lsl #32
    // 0xe63b48: stur            x6, [fp, #-0x78]
    // 0xe63b4c: ldur            x7, [fp, #-0x18]
    // 0xe63b50: r0 = 0
    //     0xe63b50: movz            x0, #0
    // 0xe63b54: CheckStackOverflow
    //     0xe63b54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe63b58: cmp             SP, x16
    //     0xe63b5c: b.ls            #0xe64688
    // 0xe63b60: LoadField: r8 = r2->field_b
    //     0xe63b60: ldur            w8, [x2, #0xb]
    // 0xe63b64: r9 = LoadInt32Instr(r8)
    //     0xe63b64: sbfx            x9, x8, #1, #0x1f
    // 0xe63b68: cmp             x3, x9
    // 0xe63b6c: b.ne            #0xe64658
    // 0xe63b70: cmp             x0, x9
    // 0xe63b74: b.ge            #0xe64144
    // 0xe63b78: LoadField: r8 = r2->field_f
    //     0xe63b78: ldur            w8, [x2, #0xf]
    // 0xe63b7c: DecompressPointer r8
    //     0xe63b7c: add             x8, x8, HEAP, lsl #32
    // 0xe63b80: ArrayLoad: r9 = r8[r0]  ; Unknown_4
    //     0xe63b80: add             x16, x8, x0, lsl #2
    //     0xe63b84: ldur            w9, [x16, #0xf]
    // 0xe63b88: DecompressPointer r9
    //     0xe63b88: add             x9, x9, HEAP, lsl #32
    // 0xe63b8c: stur            x9, [fp, #-0x70]
    // 0xe63b90: add             x8, x0, #1
    // 0xe63b94: stur            x8, [fp, #-0x68]
    // 0xe63b98: LoadField: r10 = r7->field_7
    //     0xe63b98: ldur            x10, [x7, #7]
    // 0xe63b9c: cmp             x0, x10
    // 0xe63ba0: b.ge            #0xe63bc0
    // 0xe63ba4: LoadField: r0 = r9->field_b
    //     0xe63ba4: ldur            w0, [x9, #0xb]
    // 0xe63ba8: DecompressPointer r0
    //     0xe63ba8: add             x0, x0, HEAP, lsl #32
    // 0xe63bac: tbz             w0, #4, #0xe63bc0
    // 0xe63bb0: mov             x1, x7
    // 0xe63bb4: mov             x0, x8
    // 0xe63bb8: mov             x2, x4
    // 0xe63bbc: b               #0xe64124
    // 0xe63bc0: LoadField: r10 = r9->field_f
    //     0xe63bc0: ldur            w10, [x9, #0xf]
    // 0xe63bc4: DecompressPointer r10
    //     0xe63bc4: add             x10, x10, HEAP, lsl #32
    // 0xe63bc8: stur            x10, [fp, #-0x60]
    // 0xe63bcc: cmp             w10, NULL
    // 0xe63bd0: b.eq            #0xe63fa4
    // 0xe63bd4: LoadField: r11 = r9->field_7
    //     0xe63bd4: ldur            w11, [x9, #7]
    // 0xe63bd8: DecompressPointer r11
    //     0xe63bd8: add             x11, x11, HEAP, lsl #32
    // 0xe63bdc: stur            x11, [fp, #-0x58]
    // 0xe63be0: LoadField: r0 = r11->field_b
    //     0xe63be0: ldur            w0, [x11, #0xb]
    // 0xe63be4: r12 = LoadInt32Instr(r0)
    //     0xe63be4: sbfx            x12, x0, #1, #0x1f
    // 0xe63be8: stur            x12, [fp, #-0x50]
    // 0xe63bec: r14 = inf
    //     0xe63bec: ldr             x14, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0xe63bf0: r13 = 0.000000
    //     0xe63bf0: ldr             x13, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe63bf4: r0 = 0
    //     0xe63bf4: movz            x0, #0
    // 0xe63bf8: stur            x14, [fp, #-0x40]
    // 0xe63bfc: stur            x13, [fp, #-0x48]
    // 0xe63c00: CheckStackOverflow
    //     0xe63c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe63c04: cmp             SP, x16
    //     0xe63c08: b.ls            #0xe64690
    // 0xe63c0c: LoadField: r19 = r11->field_b
    //     0xe63c0c: ldur            w19, [x11, #0xb]
    // 0xe63c10: r20 = LoadInt32Instr(r19)
    //     0xe63c10: sbfx            x20, x19, #1, #0x1f
    // 0xe63c14: cmp             x12, x20
    // 0xe63c18: b.ne            #0xe645d8
    // 0xe63c1c: cmp             x0, x20
    // 0xe63c20: b.ge            #0xe63f3c
    // 0xe63c24: LoadField: r19 = r11->field_f
    //     0xe63c24: ldur            w19, [x11, #0xf]
    // 0xe63c28: DecompressPointer r19
    //     0xe63c28: add             x19, x19, HEAP, lsl #32
    // 0xe63c2c: ArrayLoad: r20 = r19[r0]  ; Unknown_4
    //     0xe63c2c: add             x16, x19, x0, lsl #2
    //     0xe63c30: ldur            w20, [x16, #0xf]
    // 0xe63c34: DecompressPointer r20
    //     0xe63c34: add             x20, x20, HEAP, lsl #32
    // 0xe63c38: stur            x20, [fp, #-0x38]
    // 0xe63c3c: add             x19, x0, #1
    // 0xe63c40: stur            x19, [fp, #-0x30]
    // 0xe63c44: LoadField: r0 = r20->field_7
    //     0xe63c44: ldur            w0, [x20, #7]
    // 0xe63c48: DecompressPointer r0
    //     0xe63c48: add             x0, x0, HEAP, lsl #32
    // 0xe63c4c: cmp             w0, NULL
    // 0xe63c50: b.eq            #0xe64698
    // 0xe63c54: LoadField: d0 = r0->field_f
    //     0xe63c54: ldur            d0, [x0, #0xf]
    // 0xe63c58: stur            d0, [fp, #-0xa0]
    // 0xe63c5c: r23 = inline_Allocate_Double()
    //     0xe63c5c: ldp             x23, x0, [THR, #0x50]  ; THR::top
    //     0xe63c60: add             x23, x23, #0x10
    //     0xe63c64: cmp             x0, x23
    //     0xe63c68: b.ls            #0xe6469c
    //     0xe63c6c: str             x23, [THR, #0x50]  ; THR::top
    //     0xe63c70: sub             x23, x23, #0xf
    //     0xe63c74: movz            x0, #0xe15c
    //     0xe63c78: movk            x0, #0x3, lsl #16
    //     0xe63c7c: stur            x0, [x23, #-1]
    // 0xe63c80: StoreField: r23->field_7 = d0
    //     0xe63c80: stur            d0, [x23, #7]
    // 0xe63c84: stur            x23, [fp, #-0x20]
    // 0xe63c88: r0 = 60
    //     0xe63c88: movz            x0, #0x3c
    // 0xe63c8c: branchIfSmi(r14, 0xe63c98)
    //     0xe63c8c: tbz             w14, #0, #0xe63c98
    // 0xe63c90: r0 = LoadClassIdInstr(r14)
    //     0xe63c90: ldur            x0, [x14, #-1]
    //     0xe63c94: ubfx            x0, x0, #0xc, #0x14
    // 0xe63c98: stp             x23, x14, [SP]
    // 0xe63c9c: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xe63c9c: sub             lr, x0, #0xfe3
    //     0xe63ca0: ldr             lr, [x21, lr, lsl #3]
    //     0xe63ca4: blr             lr
    // 0xe63ca8: tbnz            w0, #4, #0xe63cb4
    // 0xe63cac: ldur            x14, [fp, #-0x20]
    // 0xe63cb0: b               #0xe63dc0
    // 0xe63cb4: ldur            x1, [fp, #-0x40]
    // 0xe63cb8: r0 = 60
    //     0xe63cb8: movz            x0, #0x3c
    // 0xe63cbc: branchIfSmi(r1, 0xe63cc8)
    //     0xe63cbc: tbz             w1, #0, #0xe63cc8
    // 0xe63cc0: r0 = LoadClassIdInstr(r1)
    //     0xe63cc0: ldur            x0, [x1, #-1]
    //     0xe63cc4: ubfx            x0, x0, #0xc, #0x14
    // 0xe63cc8: ldur            x16, [fp, #-0x20]
    // 0xe63ccc: stp             x16, x1, [SP]
    // 0xe63cd0: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xe63cd0: sub             lr, x0, #0xfd2
    //     0xe63cd4: ldr             lr, [x21, lr, lsl #3]
    //     0xe63cd8: blr             lr
    // 0xe63cdc: tbnz            w0, #4, #0xe63ce8
    // 0xe63ce0: ldur            x14, [fp, #-0x40]
    // 0xe63ce4: b               #0xe63dc0
    // 0xe63ce8: ldur            x1, [fp, #-0x40]
    // 0xe63cec: r0 = 60
    //     0xe63cec: movz            x0, #0x3c
    // 0xe63cf0: branchIfSmi(r1, 0xe63cfc)
    //     0xe63cf0: tbz             w1, #0, #0xe63cfc
    // 0xe63cf4: r0 = LoadClassIdInstr(r1)
    //     0xe63cf4: ldur            x0, [x1, #-1]
    //     0xe63cf8: ubfx            x0, x0, #0xc, #0x14
    // 0xe63cfc: cmp             x0, #0x3e
    // 0xe63d00: b.ne            #0xe63d5c
    // 0xe63d04: d0 = 0.000000
    //     0xe63d04: eor             v0.16b, v0.16b, v0.16b
    // 0xe63d08: LoadField: d1 = r1->field_7
    //     0xe63d08: ldur            d1, [x1, #7]
    // 0xe63d0c: fcmp            d1, d0
    // 0xe63d10: b.ne            #0xe63d54
    // 0xe63d14: ldur            d2, [fp, #-0xa0]
    // 0xe63d18: fadd            d3, d1, d2
    // 0xe63d1c: fmul            d4, d3, d1
    // 0xe63d20: fmul            d1, d4, d2
    // 0xe63d24: r1 = inline_Allocate_Double()
    //     0xe63d24: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe63d28: add             x1, x1, #0x10
    //     0xe63d2c: cmp             x0, x1
    //     0xe63d30: b.ls            #0xe646f0
    //     0xe63d34: str             x1, [THR, #0x50]  ; THR::top
    //     0xe63d38: sub             x1, x1, #0xf
    //     0xe63d3c: movz            x0, #0xe15c
    //     0xe63d40: movk            x0, #0x3, lsl #16
    //     0xe63d44: stur            x0, [x1, #-1]
    // 0xe63d48: StoreField: r1->field_7 = d1
    //     0xe63d48: stur            d1, [x1, #7]
    // 0xe63d4c: mov             x14, x1
    // 0xe63d50: b               #0xe63dc0
    // 0xe63d54: ldur            d2, [fp, #-0xa0]
    // 0xe63d58: b               #0xe63d64
    // 0xe63d5c: ldur            d2, [fp, #-0xa0]
    // 0xe63d60: d0 = 0.000000
    //     0xe63d60: eor             v0.16b, v0.16b, v0.16b
    // 0xe63d64: r0 = 60
    //     0xe63d64: movz            x0, #0x3c
    // 0xe63d68: branchIfSmi(r1, 0xe63d74)
    //     0xe63d68: tbz             w1, #0, #0xe63d74
    // 0xe63d6c: r0 = LoadClassIdInstr(r1)
    //     0xe63d6c: ldur            x0, [x1, #-1]
    //     0xe63d70: ubfx            x0, x0, #0xc, #0x14
    // 0xe63d74: stp             xzr, x1, [SP]
    // 0xe63d78: mov             lr, x0
    // 0xe63d7c: ldr             lr, [x21, lr, lsl #3]
    // 0xe63d80: blr             lr
    // 0xe63d84: tbnz            w0, #4, #0xe63da8
    // 0xe63d88: ldur            d0, [fp, #-0xa0]
    // 0xe63d8c: fcmp            d0, #0.0
    // 0xe63d90: b.vs            #0xe63dac
    // 0xe63d94: b.ne            #0xe63da0
    // 0xe63d98: r0 = 0.000000
    //     0xe63d98: fmov            x0, d0
    // 0xe63d9c: cmp             x0, #0
    // 0xe63da0: b.ge            #0xe63dac
    // 0xe63da4: b               #0xe63db4
    // 0xe63da8: ldur            d0, [fp, #-0xa0]
    // 0xe63dac: fcmp            d0, d0
    // 0xe63db0: b.vc            #0xe63dbc
    // 0xe63db4: ldur            x14, [fp, #-0x20]
    // 0xe63db8: b               #0xe63dc0
    // 0xe63dbc: ldur            x14, [fp, #-0x40]
    // 0xe63dc0: ldur            x1, [fp, #-0x48]
    // 0xe63dc4: ldur            x0, [fp, #-0x38]
    // 0xe63dc8: stur            x14, [fp, #-0x98]
    // 0xe63dcc: LoadField: r2 = r0->field_7
    //     0xe63dcc: ldur            w2, [x0, #7]
    // 0xe63dd0: DecompressPointer r2
    //     0xe63dd0: add             x2, x2, HEAP, lsl #32
    // 0xe63dd4: cmp             w2, NULL
    // 0xe63dd8: b.eq            #0xe64704
    // 0xe63ddc: LoadField: d0 = r2->field_1f
    //     0xe63ddc: ldur            d0, [x2, #0x1f]
    // 0xe63de0: stur            d0, [fp, #-0xa0]
    // 0xe63de4: r2 = inline_Allocate_Double()
    //     0xe63de4: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xe63de8: add             x2, x2, #0x10
    //     0xe63dec: cmp             x0, x2
    //     0xe63df0: b.ls            #0xe64708
    //     0xe63df4: str             x2, [THR, #0x50]  ; THR::top
    //     0xe63df8: sub             x2, x2, #0xf
    //     0xe63dfc: movz            x0, #0xe15c
    //     0xe63e00: movk            x0, #0x3, lsl #16
    //     0xe63e04: stur            x0, [x2, #-1]
    // 0xe63e08: StoreField: r2->field_7 = d0
    //     0xe63e08: stur            d0, [x2, #7]
    // 0xe63e0c: stur            x2, [fp, #-0x20]
    // 0xe63e10: r0 = 60
    //     0xe63e10: movz            x0, #0x3c
    // 0xe63e14: branchIfSmi(r1, 0xe63e20)
    //     0xe63e14: tbz             w1, #0, #0xe63e20
    // 0xe63e18: r0 = LoadClassIdInstr(r1)
    //     0xe63e18: ldur            x0, [x1, #-1]
    //     0xe63e1c: ubfx            x0, x0, #0xc, #0x14
    // 0xe63e20: stp             x2, x1, [SP]
    // 0xe63e24: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xe63e24: sub             lr, x0, #0xfe3
    //     0xe63e28: ldr             lr, [x21, lr, lsl #3]
    //     0xe63e2c: blr             lr
    // 0xe63e30: tbnz            w0, #4, #0xe63e40
    // 0xe63e34: ldur            x13, [fp, #-0x48]
    // 0xe63e38: d0 = 0.000000
    //     0xe63e38: eor             v0.16b, v0.16b, v0.16b
    // 0xe63e3c: b               #0xe63f00
    // 0xe63e40: ldur            x1, [fp, #-0x48]
    // 0xe63e44: r0 = 60
    //     0xe63e44: movz            x0, #0x3c
    // 0xe63e48: branchIfSmi(r1, 0xe63e54)
    //     0xe63e48: tbz             w1, #0, #0xe63e54
    // 0xe63e4c: r0 = LoadClassIdInstr(r1)
    //     0xe63e4c: ldur            x0, [x1, #-1]
    //     0xe63e50: ubfx            x0, x0, #0xc, #0x14
    // 0xe63e54: ldur            x16, [fp, #-0x20]
    // 0xe63e58: stp             x16, x1, [SP]
    // 0xe63e5c: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xe63e5c: sub             lr, x0, #0xfd2
    //     0xe63e60: ldr             lr, [x21, lr, lsl #3]
    //     0xe63e64: blr             lr
    // 0xe63e68: tbnz            w0, #4, #0xe63e78
    // 0xe63e6c: ldur            x13, [fp, #-0x20]
    // 0xe63e70: d0 = 0.000000
    //     0xe63e70: eor             v0.16b, v0.16b, v0.16b
    // 0xe63e74: b               #0xe63f00
    // 0xe63e78: ldur            x1, [fp, #-0x48]
    // 0xe63e7c: r0 = 60
    //     0xe63e7c: movz            x0, #0x3c
    // 0xe63e80: branchIfSmi(r1, 0xe63e8c)
    //     0xe63e80: tbz             w1, #0, #0xe63e8c
    // 0xe63e84: r0 = LoadClassIdInstr(r1)
    //     0xe63e84: ldur            x0, [x1, #-1]
    //     0xe63e88: ubfx            x0, x0, #0xc, #0x14
    // 0xe63e8c: cmp             x0, #0x3e
    // 0xe63e90: b.ne            #0xe63ee4
    // 0xe63e94: d0 = 0.000000
    //     0xe63e94: eor             v0.16b, v0.16b, v0.16b
    // 0xe63e98: LoadField: d1 = r1->field_7
    //     0xe63e98: ldur            d1, [x1, #7]
    // 0xe63e9c: fcmp            d1, d0
    // 0xe63ea0: b.ne            #0xe63edc
    // 0xe63ea4: ldur            d2, [fp, #-0xa0]
    // 0xe63ea8: fadd            d3, d1, d2
    // 0xe63eac: r1 = inline_Allocate_Double()
    //     0xe63eac: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe63eb0: add             x1, x1, #0x10
    //     0xe63eb4: cmp             x0, x1
    //     0xe63eb8: b.ls            #0xe64724
    //     0xe63ebc: str             x1, [THR, #0x50]  ; THR::top
    //     0xe63ec0: sub             x1, x1, #0xf
    //     0xe63ec4: movz            x0, #0xe15c
    //     0xe63ec8: movk            x0, #0x3, lsl #16
    //     0xe63ecc: stur            x0, [x1, #-1]
    // 0xe63ed0: StoreField: r1->field_7 = d3
    //     0xe63ed0: stur            d3, [x1, #7]
    // 0xe63ed4: mov             x13, x1
    // 0xe63ed8: b               #0xe63f00
    // 0xe63edc: ldur            d2, [fp, #-0xa0]
    // 0xe63ee0: b               #0xe63eec
    // 0xe63ee4: ldur            d2, [fp, #-0xa0]
    // 0xe63ee8: d0 = 0.000000
    //     0xe63ee8: eor             v0.16b, v0.16b, v0.16b
    // 0xe63eec: fcmp            d2, d2
    // 0xe63ef0: b.vc            #0xe63efc
    // 0xe63ef4: ldur            x13, [fp, #-0x20]
    // 0xe63ef8: b               #0xe63f00
    // 0xe63efc: mov             x13, x1
    // 0xe63f00: ldur            x14, [fp, #-0x98]
    // 0xe63f04: ldur            x0, [fp, #-0x30]
    // 0xe63f08: ldur            x1, [fp, #-8]
    // 0xe63f0c: ldur            x7, [fp, #-0x18]
    // 0xe63f10: ldur            x2, [fp, #-0x90]
    // 0xe63f14: ldur            x10, [fp, #-0x60]
    // 0xe63f18: ldur            x11, [fp, #-0x58]
    // 0xe63f1c: ldur            x5, [fp, #-0x80]
    // 0xe63f20: ldur            x6, [fp, #-0x78]
    // 0xe63f24: ldur            x8, [fp, #-0x68]
    // 0xe63f28: ldur            x4, [fp, #-0x28]
    // 0xe63f2c: ldur            x3, [fp, #-0x88]
    // 0xe63f30: ldur            x12, [fp, #-0x50]
    // 0xe63f34: ldur            x9, [fp, #-0x70]
    // 0xe63f38: b               #0xe63bf8
    // 0xe63f3c: mov             x2, x1
    // 0xe63f40: mov             x0, x14
    // 0xe63f44: mov             x1, x13
    // 0xe63f48: d0 = 0.000000
    //     0xe63f48: eor             v0.16b, v0.16b, v0.16b
    // 0xe63f4c: LoadField: r3 = r2->field_7
    //     0xe63f4c: ldur            w3, [x2, #7]
    // 0xe63f50: DecompressPointer r3
    //     0xe63f50: add             x3, x3, HEAP, lsl #32
    // 0xe63f54: cmp             w3, NULL
    // 0xe63f58: b.eq            #0xe64738
    // 0xe63f5c: ArrayLoad: d1 = r3[0]  ; List_8
    //     0xe63f5c: ldur            d1, [x3, #0x17]
    // 0xe63f60: stur            d1, [fp, #-0xa0]
    // 0xe63f64: r0 = PdfRect()
    //     0xe63f64: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe63f68: StoreField: r0->field_7 = rZR
    //     0xe63f68: stur            xzr, [x0, #7]
    // 0xe63f6c: ldur            x1, [fp, #-0x40]
    // 0xe63f70: LoadField: d0 = r1->field_7
    //     0xe63f70: ldur            d0, [x1, #7]
    // 0xe63f74: StoreField: r0->field_f = d0
    //     0xe63f74: stur            d0, [x0, #0xf]
    // 0xe63f78: ldur            d0, [fp, #-0xa0]
    // 0xe63f7c: ArrayStore: r0[0] = d0  ; List_8
    //     0xe63f7c: stur            d0, [x0, #0x17]
    // 0xe63f80: ldur            x1, [fp, #-0x48]
    // 0xe63f84: LoadField: d0 = r1->field_7
    //     0xe63f84: ldur            d0, [x1, #7]
    // 0xe63f88: StoreField: r0->field_1f = d0
    //     0xe63f88: stur            d0, [x0, #0x1f]
    // 0xe63f8c: ldur            x1, [fp, #-0x60]
    // 0xe63f90: ldur            x2, [fp, #-0x10]
    // 0xe63f94: mov             x3, x0
    // 0xe63f98: r5 = Instance_PaintPhase
    //     0xe63f98: add             x5, PP, #0x3e, lsl #12  ; [pp+0x3e998] Obj!PaintPhase@e2e9c1
    //     0xe63f9c: ldr             x5, [x5, #0x998]
    // 0xe63fa0: r0 = paint()
    //     0xe63fa0: bl              #0xe649ec  ; [package:pdf/src/widgets/decoration.dart] BoxDecoration::paint
    // 0xe63fa4: ldur            x0, [fp, #-0x70]
    // 0xe63fa8: LoadField: r3 = r0->field_7
    //     0xe63fa8: ldur            w3, [x0, #7]
    // 0xe63fac: DecompressPointer r3
    //     0xe63fac: add             x3, x3, HEAP, lsl #32
    // 0xe63fb0: stur            x3, [fp, #-0x38]
    // 0xe63fb4: LoadField: r0 = r3->field_b
    //     0xe63fb4: ldur            w0, [x3, #0xb]
    // 0xe63fb8: r4 = LoadInt32Instr(r0)
    //     0xe63fb8: sbfx            x4, x0, #1, #0x1f
    // 0xe63fbc: stur            x4, [fp, #-0x50]
    // 0xe63fc0: ldur            x0, [fp, #-0x28]
    // 0xe63fc4: r1 = 0
    //     0xe63fc4: movz            x1, #0
    // 0xe63fc8: ldur            x5, [fp, #-0x78]
    // 0xe63fcc: CheckStackOverflow
    //     0xe63fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe63fd0: cmp             SP, x16
    //     0xe63fd4: b.ls            #0xe6473c
    // 0xe63fd8: LoadField: r2 = r3->field_b
    //     0xe63fd8: ldur            w2, [x3, #0xb]
    // 0xe63fdc: r6 = LoadInt32Instr(r2)
    //     0xe63fdc: sbfx            x6, x2, #1, #0x1f
    // 0xe63fe0: cmp             x4, x6
    // 0xe63fe4: b.ne            #0xe645f8
    // 0xe63fe8: cmp             x1, x6
    // 0xe63fec: b.ge            #0xe6410c
    // 0xe63ff0: LoadField: r2 = r3->field_f
    //     0xe63ff0: ldur            w2, [x3, #0xf]
    // 0xe63ff4: DecompressPointer r2
    //     0xe63ff4: add             x2, x2, HEAP, lsl #32
    // 0xe63ff8: ArrayLoad: r6 = r2[r1]  ; Unknown_4
    //     0xe63ff8: add             x16, x2, x1, lsl #2
    //     0xe63ffc: ldur            w6, [x16, #0xf]
    // 0xe64000: DecompressPointer r6
    //     0xe64000: add             x6, x6, HEAP, lsl #32
    // 0xe64004: stur            x6, [fp, #-0x20]
    // 0xe64008: add             x7, x1, #1
    // 0xe6400c: ldur            x1, [fp, #-0x80]
    // 0xe64010: stur            x7, [fp, #-0x30]
    // 0xe64014: r2 = "q "
    //     0xe64014: add             x2, PP, #0x36, lsl #12  ; [pp+0x36728] "q "
    //     0xe64018: ldr             x2, [x2, #0x728]
    // 0xe6401c: r0 = putString()
    //     0xe6401c: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe64020: ldur            x0, [fp, #-0x28]
    // 0xe64024: LoadField: r1 = r0->field_7
    //     0xe64024: ldur            w1, [x0, #7]
    // 0xe64028: DecompressPointer r1
    //     0xe64028: add             x1, x1, HEAP, lsl #32
    // 0xe6402c: r16 = Sentinel
    //     0xe6402c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe64030: cmp             w1, w16
    // 0xe64034: b.eq            #0xe64744
    // 0xe64038: r0 = copy()
    //     0xe64038: bl              #0xe47a80  ; [package:pdf/src/pdf/graphics.dart] _PdfGraphicsContext::copy
    // 0xe6403c: ldur            x1, [fp, #-0x78]
    // 0xe64040: mov             x2, x0
    // 0xe64044: r0 = _add()
    //     0xe64044: bl              #0x61a57c  ; [dart:collection] ListQueue::_add
    // 0xe64048: ldur            x0, [fp, #-0x20]
    // 0xe6404c: LoadField: r1 = r0->field_7
    //     0xe6404c: ldur            w1, [x0, #7]
    // 0xe64050: DecompressPointer r1
    //     0xe64050: add             x1, x1, HEAP, lsl #32
    // 0xe64054: cmp             w1, NULL
    // 0xe64058: b.eq            #0xe64750
    // 0xe6405c: LoadField: d0 = r1->field_7
    //     0xe6405c: ldur            d0, [x1, #7]
    // 0xe64060: LoadField: d1 = r1->field_f
    //     0xe64060: ldur            d1, [x1, #0xf]
    // 0xe64064: ArrayLoad: d2 = r1[0]  ; List_8
    //     0xe64064: ldur            d2, [x1, #0x17]
    // 0xe64068: LoadField: d3 = r1->field_1f
    //     0xe64068: ldur            d3, [x1, #0x1f]
    // 0xe6406c: ldur            x1, [fp, #-0x28]
    // 0xe64070: r0 = drawRect()
    //     0xe64070: bl              #0xe64800  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawRect
    // 0xe64074: ldur            x1, [fp, #-0x80]
    // 0xe64078: r2 = "W n "
    //     0xe64078: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e0b8] "W n "
    //     0xe6407c: ldr             x2, [x2, #0xb8]
    // 0xe64080: r0 = putString()
    //     0xe64080: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe64084: ldur            x1, [fp, #-0x20]
    // 0xe64088: r0 = LoadClassIdInstr(r1)
    //     0xe64088: ldur            x0, [x1, #-1]
    //     0xe6408c: ubfx            x0, x0, #0xc, #0x14
    // 0xe64090: ldur            x2, [fp, #-0x10]
    // 0xe64094: r0 = GDT[cid_x0 + -0xe8b]()
    //     0xe64094: sub             lr, x0, #0xe8b
    //     0xe64098: ldr             lr, [x21, lr, lsl #3]
    //     0xe6409c: blr             lr
    // 0xe640a0: ldur            x0, [fp, #-0x78]
    // 0xe640a4: LoadField: r1 = r0->field_f
    //     0xe640a4: ldur            x1, [x0, #0xf]
    // 0xe640a8: ArrayLoad: r2 = r0[0]  ; List_8
    //     0xe640a8: ldur            x2, [x0, #0x17]
    // 0xe640ac: cmp             x1, x2
    // 0xe640b0: b.eq            #0xe640f4
    // 0xe640b4: ldur            x3, [fp, #-0x28]
    // 0xe640b8: ldur            x1, [fp, #-0x80]
    // 0xe640bc: r2 = "Q "
    //     0xe640bc: add             x2, PP, #0x36, lsl #12  ; [pp+0x36710] "Q "
    //     0xe640c0: ldr             x2, [x2, #0x710]
    // 0xe640c4: r0 = putString()
    //     0xe640c4: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe640c8: ldur            x1, [fp, #-0x78]
    // 0xe640cc: r0 = removeLast()
    //     0xe640cc: bl              #0x64f180  ; [dart:collection] ListQueue::removeLast
    // 0xe640d0: ldur            x2, [fp, #-0x28]
    // 0xe640d4: StoreField: r2->field_7 = r0
    //     0xe640d4: stur            w0, [x2, #7]
    //     0xe640d8: ldurb           w16, [x2, #-1]
    //     0xe640dc: ldurb           w17, [x0, #-1]
    //     0xe640e0: and             x16, x17, x16, lsr #2
    //     0xe640e4: tst             x16, HEAP, lsr #32
    //     0xe640e8: b.eq            #0xe640f0
    //     0xe640ec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe640f0: b               #0xe640f8
    // 0xe640f4: ldur            x2, [fp, #-0x28]
    // 0xe640f8: ldur            x1, [fp, #-0x30]
    // 0xe640fc: ldur            x3, [fp, #-0x38]
    // 0xe64100: mov             x0, x2
    // 0xe64104: ldur            x4, [fp, #-0x50]
    // 0xe64108: b               #0xe63fc8
    // 0xe6410c: ldur            x1, [fp, #-0x18]
    // 0xe64110: mov             x2, x0
    // 0xe64114: ldur            x0, [fp, #-0x68]
    // 0xe64118: LoadField: r3 = r1->field_f
    //     0xe64118: ldur            x3, [x1, #0xf]
    // 0xe6411c: cmp             x0, x3
    // 0xe64120: b.ge            #0xe6414c
    // 0xe64124: mov             x7, x1
    // 0xe64128: ldur            x1, [fp, #-8]
    // 0xe6412c: mov             x4, x2
    // 0xe64130: ldur            x2, [fp, #-0x90]
    // 0xe64134: ldur            x5, [fp, #-0x80]
    // 0xe64138: ldur            x6, [fp, #-0x78]
    // 0xe6413c: ldur            x3, [fp, #-0x88]
    // 0xe64140: b               #0xe63b54
    // 0xe64144: mov             x1, x7
    // 0xe64148: mov             x2, x4
    // 0xe6414c: ldur            x3, [fp, #-0x90]
    // 0xe64150: LoadField: r0 = r3->field_b
    //     0xe64150: ldur            w0, [x3, #0xb]
    // 0xe64154: r4 = LoadInt32Instr(r0)
    //     0xe64154: sbfx            x4, x0, #1, #0x1f
    // 0xe64158: stur            x4, [fp, #-0x88]
    // 0xe6415c: r0 = 0
    //     0xe6415c: movz            x0, #0
    // 0xe64160: ldur            x5, [fp, #-8]
    // 0xe64164: CheckStackOverflow
    //     0xe64164: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe64168: cmp             SP, x16
    //     0xe6416c: b.ls            #0xe64754
    // 0xe64170: LoadField: r6 = r3->field_b
    //     0xe64170: ldur            w6, [x3, #0xb]
    // 0xe64174: r7 = LoadInt32Instr(r6)
    //     0xe64174: sbfx            x7, x6, #1, #0x1f
    // 0xe64178: cmp             x4, x7
    // 0xe6417c: b.ne            #0xe64638
    // 0xe64180: cmp             x0, x7
    // 0xe64184: b.ge            #0xe645c0
    // 0xe64188: LoadField: r6 = r3->field_f
    //     0xe64188: ldur            w6, [x3, #0xf]
    // 0xe6418c: DecompressPointer r6
    //     0xe6418c: add             x6, x6, HEAP, lsl #32
    // 0xe64190: ArrayLoad: r7 = r6[r0]  ; Unknown_4
    //     0xe64190: add             x16, x6, x0, lsl #2
    //     0xe64194: ldur            w7, [x16, #0xf]
    // 0xe64198: DecompressPointer r7
    //     0xe64198: add             x7, x7, HEAP, lsl #32
    // 0xe6419c: add             x6, x0, #1
    // 0xe641a0: stur            x6, [fp, #-0x68]
    // 0xe641a4: LoadField: r8 = r1->field_7
    //     0xe641a4: ldur            x8, [x1, #7]
    // 0xe641a8: cmp             x0, x8
    // 0xe641ac: b.ge            #0xe641c4
    // 0xe641b0: LoadField: r0 = r7->field_b
    //     0xe641b0: ldur            w0, [x7, #0xb]
    // 0xe641b4: DecompressPointer r0
    //     0xe641b4: add             x0, x0, HEAP, lsl #32
    // 0xe641b8: tbz             w0, #4, #0xe641c4
    // 0xe641bc: mov             x0, x6
    // 0xe641c0: b               #0xe645b0
    // 0xe641c4: LoadField: r8 = r7->field_f
    //     0xe641c4: ldur            w8, [x7, #0xf]
    // 0xe641c8: DecompressPointer r8
    //     0xe641c8: add             x8, x8, HEAP, lsl #32
    // 0xe641cc: stur            x8, [fp, #-0x78]
    // 0xe641d0: cmp             w8, NULL
    // 0xe641d4: b.eq            #0xe6459c
    // 0xe641d8: LoadField: r9 = r7->field_7
    //     0xe641d8: ldur            w9, [x7, #7]
    // 0xe641dc: DecompressPointer r9
    //     0xe641dc: add             x9, x9, HEAP, lsl #32
    // 0xe641e0: stur            x9, [fp, #-0x70]
    // 0xe641e4: LoadField: r0 = r9->field_b
    //     0xe641e4: ldur            w0, [x9, #0xb]
    // 0xe641e8: r7 = LoadInt32Instr(r0)
    //     0xe641e8: sbfx            x7, x0, #1, #0x1f
    // 0xe641ec: stur            x7, [fp, #-0x50]
    // 0xe641f0: r11 = inf
    //     0xe641f0: ldr             x11, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0xe641f4: r10 = 0.000000
    //     0xe641f4: ldr             x10, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe641f8: r0 = 0
    //     0xe641f8: movz            x0, #0
    // 0xe641fc: stur            x11, [fp, #-0x48]
    // 0xe64200: stur            x10, [fp, #-0x60]
    // 0xe64204: CheckStackOverflow
    //     0xe64204: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe64208: cmp             SP, x16
    //     0xe6420c: b.ls            #0xe6475c
    // 0xe64210: LoadField: r12 = r9->field_b
    //     0xe64210: ldur            w12, [x9, #0xb]
    // 0xe64214: r13 = LoadInt32Instr(r12)
    //     0xe64214: sbfx            x13, x12, #1, #0x1f
    // 0xe64218: cmp             x7, x13
    // 0xe6421c: b.ne            #0xe64618
    // 0xe64220: cmp             x0, x13
    // 0xe64224: b.ge            #0xe64534
    // 0xe64228: LoadField: r12 = r9->field_f
    //     0xe64228: ldur            w12, [x9, #0xf]
    // 0xe6422c: DecompressPointer r12
    //     0xe6422c: add             x12, x12, HEAP, lsl #32
    // 0xe64230: ArrayLoad: r13 = r12[r0]  ; Unknown_4
    //     0xe64230: add             x16, x12, x0, lsl #2
    //     0xe64234: ldur            w13, [x16, #0xf]
    // 0xe64238: DecompressPointer r13
    //     0xe64238: add             x13, x13, HEAP, lsl #32
    // 0xe6423c: stur            x13, [fp, #-0x40]
    // 0xe64240: add             x12, x0, #1
    // 0xe64244: stur            x12, [fp, #-0x30]
    // 0xe64248: LoadField: r0 = r13->field_7
    //     0xe64248: ldur            w0, [x13, #7]
    // 0xe6424c: DecompressPointer r0
    //     0xe6424c: add             x0, x0, HEAP, lsl #32
    // 0xe64250: cmp             w0, NULL
    // 0xe64254: b.eq            #0xe64764
    // 0xe64258: LoadField: d0 = r0->field_f
    //     0xe64258: ldur            d0, [x0, #0xf]
    // 0xe6425c: stur            d0, [fp, #-0xa0]
    // 0xe64260: r14 = inline_Allocate_Double()
    //     0xe64260: ldp             x14, x0, [THR, #0x50]  ; THR::top
    //     0xe64264: add             x14, x14, #0x10
    //     0xe64268: cmp             x0, x14
    //     0xe6426c: b.ls            #0xe64768
    //     0xe64270: str             x14, [THR, #0x50]  ; THR::top
    //     0xe64274: sub             x14, x14, #0xf
    //     0xe64278: movz            x0, #0xe15c
    //     0xe6427c: movk            x0, #0x3, lsl #16
    //     0xe64280: stur            x0, [x14, #-1]
    // 0xe64284: StoreField: r14->field_7 = d0
    //     0xe64284: stur            d0, [x14, #7]
    // 0xe64288: stur            x14, [fp, #-0x20]
    // 0xe6428c: r0 = 60
    //     0xe6428c: movz            x0, #0x3c
    // 0xe64290: branchIfSmi(r11, 0xe6429c)
    //     0xe64290: tbz             w11, #0, #0xe6429c
    // 0xe64294: r0 = LoadClassIdInstr(r11)
    //     0xe64294: ldur            x0, [x11, #-1]
    //     0xe64298: ubfx            x0, x0, #0xc, #0x14
    // 0xe6429c: stp             x14, x11, [SP]
    // 0xe642a0: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xe642a0: sub             lr, x0, #0xfe3
    //     0xe642a4: ldr             lr, [x21, lr, lsl #3]
    //     0xe642a8: blr             lr
    // 0xe642ac: tbnz            w0, #4, #0xe642b8
    // 0xe642b0: ldur            x11, [fp, #-0x20]
    // 0xe642b4: b               #0xe643c4
    // 0xe642b8: ldur            x1, [fp, #-0x48]
    // 0xe642bc: r0 = 60
    //     0xe642bc: movz            x0, #0x3c
    // 0xe642c0: branchIfSmi(r1, 0xe642cc)
    //     0xe642c0: tbz             w1, #0, #0xe642cc
    // 0xe642c4: r0 = LoadClassIdInstr(r1)
    //     0xe642c4: ldur            x0, [x1, #-1]
    //     0xe642c8: ubfx            x0, x0, #0xc, #0x14
    // 0xe642cc: ldur            x16, [fp, #-0x20]
    // 0xe642d0: stp             x16, x1, [SP]
    // 0xe642d4: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xe642d4: sub             lr, x0, #0xfd2
    //     0xe642d8: ldr             lr, [x21, lr, lsl #3]
    //     0xe642dc: blr             lr
    // 0xe642e0: tbnz            w0, #4, #0xe642ec
    // 0xe642e4: ldur            x11, [fp, #-0x48]
    // 0xe642e8: b               #0xe643c4
    // 0xe642ec: ldur            x1, [fp, #-0x48]
    // 0xe642f0: r0 = 60
    //     0xe642f0: movz            x0, #0x3c
    // 0xe642f4: branchIfSmi(r1, 0xe64300)
    //     0xe642f4: tbz             w1, #0, #0xe64300
    // 0xe642f8: r0 = LoadClassIdInstr(r1)
    //     0xe642f8: ldur            x0, [x1, #-1]
    //     0xe642fc: ubfx            x0, x0, #0xc, #0x14
    // 0xe64300: cmp             x0, #0x3e
    // 0xe64304: b.ne            #0xe64360
    // 0xe64308: d0 = 0.000000
    //     0xe64308: eor             v0.16b, v0.16b, v0.16b
    // 0xe6430c: LoadField: d1 = r1->field_7
    //     0xe6430c: ldur            d1, [x1, #7]
    // 0xe64310: fcmp            d1, d0
    // 0xe64314: b.ne            #0xe64358
    // 0xe64318: ldur            d2, [fp, #-0xa0]
    // 0xe6431c: fadd            d3, d1, d2
    // 0xe64320: fmul            d4, d3, d1
    // 0xe64324: fmul            d1, d4, d2
    // 0xe64328: r1 = inline_Allocate_Double()
    //     0xe64328: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe6432c: add             x1, x1, #0x10
    //     0xe64330: cmp             x0, x1
    //     0xe64334: b.ls            #0xe647b4
    //     0xe64338: str             x1, [THR, #0x50]  ; THR::top
    //     0xe6433c: sub             x1, x1, #0xf
    //     0xe64340: movz            x0, #0xe15c
    //     0xe64344: movk            x0, #0x3, lsl #16
    //     0xe64348: stur            x0, [x1, #-1]
    // 0xe6434c: StoreField: r1->field_7 = d1
    //     0xe6434c: stur            d1, [x1, #7]
    // 0xe64350: mov             x11, x1
    // 0xe64354: b               #0xe643c4
    // 0xe64358: ldur            d2, [fp, #-0xa0]
    // 0xe6435c: b               #0xe64368
    // 0xe64360: ldur            d2, [fp, #-0xa0]
    // 0xe64364: d0 = 0.000000
    //     0xe64364: eor             v0.16b, v0.16b, v0.16b
    // 0xe64368: r0 = 60
    //     0xe64368: movz            x0, #0x3c
    // 0xe6436c: branchIfSmi(r1, 0xe64378)
    //     0xe6436c: tbz             w1, #0, #0xe64378
    // 0xe64370: r0 = LoadClassIdInstr(r1)
    //     0xe64370: ldur            x0, [x1, #-1]
    //     0xe64374: ubfx            x0, x0, #0xc, #0x14
    // 0xe64378: stp             xzr, x1, [SP]
    // 0xe6437c: mov             lr, x0
    // 0xe64380: ldr             lr, [x21, lr, lsl #3]
    // 0xe64384: blr             lr
    // 0xe64388: tbnz            w0, #4, #0xe643ac
    // 0xe6438c: ldur            d0, [fp, #-0xa0]
    // 0xe64390: fcmp            d0, #0.0
    // 0xe64394: b.vs            #0xe643b0
    // 0xe64398: b.ne            #0xe643a4
    // 0xe6439c: r0 = 0.000000
    //     0xe6439c: fmov            x0, d0
    // 0xe643a0: cmp             x0, #0
    // 0xe643a4: b.ge            #0xe643b0
    // 0xe643a8: b               #0xe643b8
    // 0xe643ac: ldur            d0, [fp, #-0xa0]
    // 0xe643b0: fcmp            d0, d0
    // 0xe643b4: b.vc            #0xe643c0
    // 0xe643b8: ldur            x11, [fp, #-0x20]
    // 0xe643bc: b               #0xe643c4
    // 0xe643c0: ldur            x11, [fp, #-0x48]
    // 0xe643c4: ldur            x1, [fp, #-0x60]
    // 0xe643c8: ldur            x0, [fp, #-0x40]
    // 0xe643cc: stur            x11, [fp, #-0x80]
    // 0xe643d0: LoadField: r2 = r0->field_7
    //     0xe643d0: ldur            w2, [x0, #7]
    // 0xe643d4: DecompressPointer r2
    //     0xe643d4: add             x2, x2, HEAP, lsl #32
    // 0xe643d8: cmp             w2, NULL
    // 0xe643dc: b.eq            #0xe647c8
    // 0xe643e0: LoadField: d0 = r2->field_1f
    //     0xe643e0: ldur            d0, [x2, #0x1f]
    // 0xe643e4: stur            d0, [fp, #-0xa0]
    // 0xe643e8: r2 = inline_Allocate_Double()
    //     0xe643e8: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xe643ec: add             x2, x2, #0x10
    //     0xe643f0: cmp             x0, x2
    //     0xe643f4: b.ls            #0xe647cc
    //     0xe643f8: str             x2, [THR, #0x50]  ; THR::top
    //     0xe643fc: sub             x2, x2, #0xf
    //     0xe64400: movz            x0, #0xe15c
    //     0xe64404: movk            x0, #0x3, lsl #16
    //     0xe64408: stur            x0, [x2, #-1]
    // 0xe6440c: StoreField: r2->field_7 = d0
    //     0xe6440c: stur            d0, [x2, #7]
    // 0xe64410: stur            x2, [fp, #-0x20]
    // 0xe64414: r0 = 60
    //     0xe64414: movz            x0, #0x3c
    // 0xe64418: branchIfSmi(r1, 0xe64424)
    //     0xe64418: tbz             w1, #0, #0xe64424
    // 0xe6441c: r0 = LoadClassIdInstr(r1)
    //     0xe6441c: ldur            x0, [x1, #-1]
    //     0xe64420: ubfx            x0, x0, #0xc, #0x14
    // 0xe64424: stp             x2, x1, [SP]
    // 0xe64428: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xe64428: sub             lr, x0, #0xfe3
    //     0xe6442c: ldr             lr, [x21, lr, lsl #3]
    //     0xe64430: blr             lr
    // 0xe64434: tbnz            w0, #4, #0xe64444
    // 0xe64438: ldur            x10, [fp, #-0x60]
    // 0xe6443c: d0 = 0.000000
    //     0xe6443c: eor             v0.16b, v0.16b, v0.16b
    // 0xe64440: b               #0xe64504
    // 0xe64444: ldur            x1, [fp, #-0x60]
    // 0xe64448: r0 = 60
    //     0xe64448: movz            x0, #0x3c
    // 0xe6444c: branchIfSmi(r1, 0xe64458)
    //     0xe6444c: tbz             w1, #0, #0xe64458
    // 0xe64450: r0 = LoadClassIdInstr(r1)
    //     0xe64450: ldur            x0, [x1, #-1]
    //     0xe64454: ubfx            x0, x0, #0xc, #0x14
    // 0xe64458: ldur            x16, [fp, #-0x20]
    // 0xe6445c: stp             x16, x1, [SP]
    // 0xe64460: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xe64460: sub             lr, x0, #0xfd2
    //     0xe64464: ldr             lr, [x21, lr, lsl #3]
    //     0xe64468: blr             lr
    // 0xe6446c: tbnz            w0, #4, #0xe6447c
    // 0xe64470: ldur            x10, [fp, #-0x20]
    // 0xe64474: d0 = 0.000000
    //     0xe64474: eor             v0.16b, v0.16b, v0.16b
    // 0xe64478: b               #0xe64504
    // 0xe6447c: ldur            x1, [fp, #-0x60]
    // 0xe64480: r0 = 60
    //     0xe64480: movz            x0, #0x3c
    // 0xe64484: branchIfSmi(r1, 0xe64490)
    //     0xe64484: tbz             w1, #0, #0xe64490
    // 0xe64488: r0 = LoadClassIdInstr(r1)
    //     0xe64488: ldur            x0, [x1, #-1]
    //     0xe6448c: ubfx            x0, x0, #0xc, #0x14
    // 0xe64490: cmp             x0, #0x3e
    // 0xe64494: b.ne            #0xe644e8
    // 0xe64498: d0 = 0.000000
    //     0xe64498: eor             v0.16b, v0.16b, v0.16b
    // 0xe6449c: LoadField: d1 = r1->field_7
    //     0xe6449c: ldur            d1, [x1, #7]
    // 0xe644a0: fcmp            d1, d0
    // 0xe644a4: b.ne            #0xe644e0
    // 0xe644a8: ldur            d2, [fp, #-0xa0]
    // 0xe644ac: fadd            d3, d1, d2
    // 0xe644b0: r1 = inline_Allocate_Double()
    //     0xe644b0: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe644b4: add             x1, x1, #0x10
    //     0xe644b8: cmp             x0, x1
    //     0xe644bc: b.ls            #0xe647e8
    //     0xe644c0: str             x1, [THR, #0x50]  ; THR::top
    //     0xe644c4: sub             x1, x1, #0xf
    //     0xe644c8: movz            x0, #0xe15c
    //     0xe644cc: movk            x0, #0x3, lsl #16
    //     0xe644d0: stur            x0, [x1, #-1]
    // 0xe644d4: StoreField: r1->field_7 = d3
    //     0xe644d4: stur            d3, [x1, #7]
    // 0xe644d8: mov             x10, x1
    // 0xe644dc: b               #0xe64504
    // 0xe644e0: ldur            d2, [fp, #-0xa0]
    // 0xe644e4: b               #0xe644f0
    // 0xe644e8: ldur            d2, [fp, #-0xa0]
    // 0xe644ec: d0 = 0.000000
    //     0xe644ec: eor             v0.16b, v0.16b, v0.16b
    // 0xe644f0: fcmp            d2, d2
    // 0xe644f4: b.vc            #0xe64500
    // 0xe644f8: ldur            x10, [fp, #-0x20]
    // 0xe644fc: b               #0xe64504
    // 0xe64500: mov             x10, x1
    // 0xe64504: ldur            x11, [fp, #-0x80]
    // 0xe64508: ldur            x0, [fp, #-0x30]
    // 0xe6450c: ldur            x5, [fp, #-8]
    // 0xe64510: ldur            x1, [fp, #-0x18]
    // 0xe64514: ldur            x3, [fp, #-0x90]
    // 0xe64518: ldur            x8, [fp, #-0x78]
    // 0xe6451c: ldur            x9, [fp, #-0x70]
    // 0xe64520: ldur            x6, [fp, #-0x68]
    // 0xe64524: ldur            x2, [fp, #-0x28]
    // 0xe64528: ldur            x4, [fp, #-0x88]
    // 0xe6452c: ldur            x7, [fp, #-0x50]
    // 0xe64530: b               #0xe641fc
    // 0xe64534: mov             x2, x5
    // 0xe64538: mov             x0, x11
    // 0xe6453c: mov             x1, x10
    // 0xe64540: d0 = 0.000000
    //     0xe64540: eor             v0.16b, v0.16b, v0.16b
    // 0xe64544: LoadField: r3 = r2->field_7
    //     0xe64544: ldur            w3, [x2, #7]
    // 0xe64548: DecompressPointer r3
    //     0xe64548: add             x3, x3, HEAP, lsl #32
    // 0xe6454c: cmp             w3, NULL
    // 0xe64550: b.eq            #0xe647fc
    // 0xe64554: ArrayLoad: d1 = r3[0]  ; List_8
    //     0xe64554: ldur            d1, [x3, #0x17]
    // 0xe64558: stur            d1, [fp, #-0xa0]
    // 0xe6455c: r0 = PdfRect()
    //     0xe6455c: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe64560: StoreField: r0->field_7 = rZR
    //     0xe64560: stur            xzr, [x0, #7]
    // 0xe64564: ldur            x1, [fp, #-0x48]
    // 0xe64568: LoadField: d0 = r1->field_7
    //     0xe64568: ldur            d0, [x1, #7]
    // 0xe6456c: StoreField: r0->field_f = d0
    //     0xe6456c: stur            d0, [x0, #0xf]
    // 0xe64570: ldur            d0, [fp, #-0xa0]
    // 0xe64574: ArrayStore: r0[0] = d0  ; List_8
    //     0xe64574: stur            d0, [x0, #0x17]
    // 0xe64578: ldur            x1, [fp, #-0x60]
    // 0xe6457c: LoadField: d0 = r1->field_7
    //     0xe6457c: ldur            d0, [x1, #7]
    // 0xe64580: StoreField: r0->field_1f = d0
    //     0xe64580: stur            d0, [x0, #0x1f]
    // 0xe64584: ldur            x1, [fp, #-0x78]
    // 0xe64588: ldur            x2, [fp, #-0x10]
    // 0xe6458c: mov             x3, x0
    // 0xe64590: r5 = Instance_PaintPhase
    //     0xe64590: add             x5, PP, #0x3e, lsl #12  ; [pp+0x3e9a0] Obj!PaintPhase@e2e9a1
    //     0xe64594: ldr             x5, [x5, #0x9a0]
    // 0xe64598: r0 = paint()
    //     0xe64598: bl              #0xe649ec  ; [package:pdf/src/widgets/decoration.dart] BoxDecoration::paint
    // 0xe6459c: ldur            x1, [fp, #-0x18]
    // 0xe645a0: ldur            x0, [fp, #-0x68]
    // 0xe645a4: LoadField: r2 = r1->field_f
    //     0xe645a4: ldur            x2, [x1, #0xf]
    // 0xe645a8: cmp             x0, x2
    // 0xe645ac: b.ge            #0xe645c0
    // 0xe645b0: ldur            x3, [fp, #-0x90]
    // 0xe645b4: ldur            x2, [fp, #-0x28]
    // 0xe645b8: ldur            x4, [fp, #-0x88]
    // 0xe645bc: b               #0xe64160
    // 0xe645c0: ldur            x1, [fp, #-0x28]
    // 0xe645c4: r0 = restoreContext()
    //     0xe645c4: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe645c8: r0 = Null
    //     0xe645c8: mov             x0, NULL
    // 0xe645cc: LeaveFrame
    //     0xe645cc: mov             SP, fp
    //     0xe645d0: ldp             fp, lr, [SP], #0x10
    // 0xe645d4: ret
    //     0xe645d4: ret             
    // 0xe645d8: mov             x0, x11
    // 0xe645dc: r0 = ConcurrentModificationError()
    //     0xe645dc: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe645e0: mov             x1, x0
    // 0xe645e4: ldur            x0, [fp, #-0x58]
    // 0xe645e8: StoreField: r1->field_b = r0
    //     0xe645e8: stur            w0, [x1, #0xb]
    // 0xe645ec: mov             x0, x1
    // 0xe645f0: r0 = Throw()
    //     0xe645f0: bl              #0xec04b8  ; ThrowStub
    // 0xe645f4: brk             #0
    // 0xe645f8: mov             x0, x3
    // 0xe645fc: r0 = ConcurrentModificationError()
    //     0xe645fc: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe64600: mov             x1, x0
    // 0xe64604: ldur            x0, [fp, #-0x38]
    // 0xe64608: StoreField: r1->field_b = r0
    //     0xe64608: stur            w0, [x1, #0xb]
    // 0xe6460c: mov             x0, x1
    // 0xe64610: r0 = Throw()
    //     0xe64610: bl              #0xec04b8  ; ThrowStub
    // 0xe64614: brk             #0
    // 0xe64618: mov             x0, x9
    // 0xe6461c: r0 = ConcurrentModificationError()
    //     0xe6461c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe64620: mov             x1, x0
    // 0xe64624: ldur            x0, [fp, #-0x70]
    // 0xe64628: StoreField: r1->field_b = r0
    //     0xe64628: stur            w0, [x1, #0xb]
    // 0xe6462c: mov             x0, x1
    // 0xe64630: r0 = Throw()
    //     0xe64630: bl              #0xec04b8  ; ThrowStub
    // 0xe64634: brk             #0
    // 0xe64638: mov             x0, x3
    // 0xe6463c: r0 = ConcurrentModificationError()
    //     0xe6463c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe64640: mov             x1, x0
    // 0xe64644: ldur            x0, [fp, #-0x90]
    // 0xe64648: StoreField: r1->field_b = r0
    //     0xe64648: stur            w0, [x1, #0xb]
    // 0xe6464c: mov             x0, x1
    // 0xe64650: r0 = Throw()
    //     0xe64650: bl              #0xec04b8  ; ThrowStub
    // 0xe64654: brk             #0
    // 0xe64658: mov             x0, x2
    // 0xe6465c: r0 = ConcurrentModificationError()
    //     0xe6465c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe64660: mov             x1, x0
    // 0xe64664: ldur            x0, [fp, #-0x90]
    // 0xe64668: StoreField: r1->field_b = r0
    //     0xe64668: stur            w0, [x1, #0xb]
    // 0xe6466c: mov             x0, x1
    // 0xe64670: r0 = Throw()
    //     0xe64670: bl              #0xec04b8  ; ThrowStub
    // 0xe64674: brk             #0
    // 0xe64678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe64678: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6467c: b               #0xe63a68
    // 0xe64680: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe64680: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe64684: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe64684: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe64688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe64688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6468c: b               #0xe63b60
    // 0xe64690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe64690: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe64694: b               #0xe63c0c
    // 0xe64698: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe64698: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6469c: SaveReg d0
    //     0xe6469c: str             q0, [SP, #-0x10]!
    // 0xe646a0: stp             x19, x20, [SP, #-0x10]!
    // 0xe646a4: stp             x13, x14, [SP, #-0x10]!
    // 0xe646a8: stp             x11, x12, [SP, #-0x10]!
    // 0xe646ac: stp             x9, x10, [SP, #-0x10]!
    // 0xe646b0: stp             x7, x8, [SP, #-0x10]!
    // 0xe646b4: stp             x5, x6, [SP, #-0x10]!
    // 0xe646b8: stp             x3, x4, [SP, #-0x10]!
    // 0xe646bc: stp             x1, x2, [SP, #-0x10]!
    // 0xe646c0: r0 = AllocateDouble()
    //     0xe646c0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe646c4: mov             x23, x0
    // 0xe646c8: ldp             x1, x2, [SP], #0x10
    // 0xe646cc: ldp             x3, x4, [SP], #0x10
    // 0xe646d0: ldp             x5, x6, [SP], #0x10
    // 0xe646d4: ldp             x7, x8, [SP], #0x10
    // 0xe646d8: ldp             x9, x10, [SP], #0x10
    // 0xe646dc: ldp             x11, x12, [SP], #0x10
    // 0xe646e0: ldp             x13, x14, [SP], #0x10
    // 0xe646e4: ldp             x19, x20, [SP], #0x10
    // 0xe646e8: RestoreReg d0
    //     0xe646e8: ldr             q0, [SP], #0x10
    // 0xe646ec: b               #0xe63c80
    // 0xe646f0: stp             q0, q1, [SP, #-0x20]!
    // 0xe646f4: r0 = AllocateDouble()
    //     0xe646f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe646f8: mov             x1, x0
    // 0xe646fc: ldp             q0, q1, [SP], #0x20
    // 0xe64700: b               #0xe63d48
    // 0xe64704: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe64704: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe64708: SaveReg d0
    //     0xe64708: str             q0, [SP, #-0x10]!
    // 0xe6470c: stp             x1, x14, [SP, #-0x10]!
    // 0xe64710: r0 = AllocateDouble()
    //     0xe64710: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe64714: mov             x2, x0
    // 0xe64718: ldp             x1, x14, [SP], #0x10
    // 0xe6471c: RestoreReg d0
    //     0xe6471c: ldr             q0, [SP], #0x10
    // 0xe64720: b               #0xe63e08
    // 0xe64724: stp             q0, q3, [SP, #-0x20]!
    // 0xe64728: r0 = AllocateDouble()
    //     0xe64728: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6472c: mov             x1, x0
    // 0xe64730: ldp             q0, q3, [SP], #0x20
    // 0xe64734: b               #0xe63ed0
    // 0xe64738: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe64738: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe6473c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6473c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe64740: b               #0xe63fd8
    // 0xe64744: r9 = _context
    //     0xe64744: add             x9, PP, #0x36, lsl #12  ; [pp+0x36720] Field <PdfGraphics._context@2225251352>: late (offset: 0x8)
    //     0xe64748: ldr             x9, [x9, #0x720]
    // 0xe6474c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe6474c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe64750: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe64750: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe64754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe64754: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe64758: b               #0xe64170
    // 0xe6475c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6475c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe64760: b               #0xe64210
    // 0xe64764: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe64764: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe64768: SaveReg d0
    //     0xe64768: str             q0, [SP, #-0x10]!
    // 0xe6476c: stp             x12, x13, [SP, #-0x10]!
    // 0xe64770: stp             x10, x11, [SP, #-0x10]!
    // 0xe64774: stp             x8, x9, [SP, #-0x10]!
    // 0xe64778: stp             x6, x7, [SP, #-0x10]!
    // 0xe6477c: stp             x4, x5, [SP, #-0x10]!
    // 0xe64780: stp             x2, x3, [SP, #-0x10]!
    // 0xe64784: SaveReg r1
    //     0xe64784: str             x1, [SP, #-8]!
    // 0xe64788: r0 = AllocateDouble()
    //     0xe64788: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6478c: mov             x14, x0
    // 0xe64790: RestoreReg r1
    //     0xe64790: ldr             x1, [SP], #8
    // 0xe64794: ldp             x2, x3, [SP], #0x10
    // 0xe64798: ldp             x4, x5, [SP], #0x10
    // 0xe6479c: ldp             x6, x7, [SP], #0x10
    // 0xe647a0: ldp             x8, x9, [SP], #0x10
    // 0xe647a4: ldp             x10, x11, [SP], #0x10
    // 0xe647a8: ldp             x12, x13, [SP], #0x10
    // 0xe647ac: RestoreReg d0
    //     0xe647ac: ldr             q0, [SP], #0x10
    // 0xe647b0: b               #0xe64284
    // 0xe647b4: stp             q0, q1, [SP, #-0x20]!
    // 0xe647b8: r0 = AllocateDouble()
    //     0xe647b8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe647bc: mov             x1, x0
    // 0xe647c0: ldp             q0, q1, [SP], #0x20
    // 0xe647c4: b               #0xe6434c
    // 0xe647c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe647c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe647cc: SaveReg d0
    //     0xe647cc: str             q0, [SP, #-0x10]!
    // 0xe647d0: stp             x1, x11, [SP, #-0x10]!
    // 0xe647d4: r0 = AllocateDouble()
    //     0xe647d4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe647d8: mov             x2, x0
    // 0xe647dc: ldp             x1, x11, [SP], #0x10
    // 0xe647e0: RestoreReg d0
    //     0xe647e0: ldr             q0, [SP], #0x10
    // 0xe647e4: b               #0xe6440c
    // 0xe647e8: stp             q0, q3, [SP, #-0x20]!
    // 0xe647ec: r0 = AllocateDouble()
    //     0xe647ec: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe647f0: mov             x1, x0
    // 0xe647f4: ldp             q0, q3, [SP], #0x20
    // 0xe647f8: b               #0xe644d4
    // 0xe647fc: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe647fc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ layout(/* No info */) {
    // ** addr: 0xe8f460, size: 0x15e8
    // 0xe8f460: EnterFrame
    //     0xe8f460: stp             fp, lr, [SP, #-0x10]!
    //     0xe8f464: mov             fp, SP
    // 0xe8f468: AllocStack(0x108)
    //     0xe8f468: sub             SP, SP, #0x108
    // 0xe8f46c: SetupParameters(Table this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xe8f46c: mov             x4, x1
    //     0xe8f470: mov             x0, x3
    //     0xe8f474: stur            x3, [fp, #-0x18]
    //     0xe8f478: mov             x3, x2
    //     0xe8f47c: stur            x1, [fp, #-8]
    //     0xe8f480: stur            x2, [fp, #-0x10]
    // 0xe8f484: CheckStackOverflow
    //     0xe8f484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8f488: cmp             SP, x16
    //     0xe8f48c: b.ls            #0xe90880
    // 0xe8f490: r1 = <double>
    //     0xe8f490: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe8f494: r2 = 0
    //     0xe8f494: movz            x2, #0
    // 0xe8f498: r0 = _GrowableList()
    //     0xe8f498: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe8f49c: mov             x2, x0
    // 0xe8f4a0: ldur            x0, [fp, #-8]
    // 0xe8f4a4: stur            x2, [fp, #-0x28]
    // 0xe8f4a8: LoadField: r3 = r0->field_1b
    //     0xe8f4a8: ldur            w3, [x0, #0x1b]
    // 0xe8f4ac: DecompressPointer r3
    //     0xe8f4ac: add             x3, x3, HEAP, lsl #32
    // 0xe8f4b0: mov             x1, x3
    // 0xe8f4b4: stur            x3, [fp, #-0x20]
    // 0xe8f4b8: r0 = clear()
    //     0xe8f4b8: bl              #0xbd9e80  ; [dart:core] _GrowableList::clear
    // 0xe8f4bc: ldur            x0, [fp, #-8]
    // 0xe8f4c0: LoadField: r2 = r0->field_1f
    //     0xe8f4c0: ldur            w2, [x0, #0x1f]
    // 0xe8f4c4: DecompressPointer r2
    //     0xe8f4c4: add             x2, x2, HEAP, lsl #32
    // 0xe8f4c8: mov             x1, x2
    // 0xe8f4cc: stur            x2, [fp, #-0x30]
    // 0xe8f4d0: r0 = clear()
    //     0xe8f4d0: bl              #0xbd9e80  ; [dart:core] _GrowableList::clear
    // 0xe8f4d4: ldur            x0, [fp, #-8]
    // 0xe8f4d8: LoadField: r4 = r0->field_b
    //     0xe8f4d8: ldur            w4, [x0, #0xb]
    // 0xe8f4dc: DecompressPointer r4
    //     0xe8f4dc: add             x4, x4, HEAP, lsl #32
    // 0xe8f4e0: stur            x4, [fp, #-0x58]
    // 0xe8f4e4: LoadField: r1 = r4->field_b
    //     0xe8f4e4: ldur            w1, [x4, #0xb]
    // 0xe8f4e8: r5 = LoadInt32Instr(r1)
    //     0xe8f4e8: sbfx            x5, x1, #1, #0x1f
    // 0xe8f4ec: ldur            x6, [fp, #-0x20]
    // 0xe8f4f0: stur            x5, [fp, #-0x50]
    // 0xe8f4f4: LoadField: r7 = r6->field_7
    //     0xe8f4f4: ldur            w7, [x6, #7]
    // 0xe8f4f8: DecompressPointer r7
    //     0xe8f4f8: add             x7, x7, HEAP, lsl #32
    // 0xe8f4fc: stur            x7, [fp, #-0x48]
    // 0xe8f500: r1 = 0
    //     0xe8f500: movz            x1, #0
    // 0xe8f504: ldur            x8, [fp, #-0x28]
    // 0xe8f508: CheckStackOverflow
    //     0xe8f508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8f50c: cmp             SP, x16
    //     0xe8f510: b.ls            #0xe90888
    // 0xe8f514: LoadField: r2 = r4->field_b
    //     0xe8f514: ldur            w2, [x4, #0xb]
    // 0xe8f518: r3 = LoadInt32Instr(r2)
    //     0xe8f518: sbfx            x3, x2, #1, #0x1f
    // 0xe8f51c: cmp             x5, x3
    // 0xe8f520: b.ne            #0xe90860
    // 0xe8f524: cmp             x1, x3
    // 0xe8f528: b.ge            #0xe8fb6c
    // 0xe8f52c: LoadField: r2 = r4->field_f
    //     0xe8f52c: ldur            w2, [x4, #0xf]
    // 0xe8f530: DecompressPointer r2
    //     0xe8f530: add             x2, x2, HEAP, lsl #32
    // 0xe8f534: ArrayLoad: r3 = r2[r1]  ; Unknown_4
    //     0xe8f534: add             x16, x2, x1, lsl #2
    //     0xe8f538: ldur            w3, [x16, #0xf]
    // 0xe8f53c: DecompressPointer r3
    //     0xe8f53c: add             x3, x3, HEAP, lsl #32
    // 0xe8f540: add             x9, x1, #1
    // 0xe8f544: stur            x9, [fp, #-0x40]
    // 0xe8f548: LoadField: r10 = r3->field_7
    //     0xe8f548: ldur            w10, [x3, #7]
    // 0xe8f54c: DecompressPointer r10
    //     0xe8f54c: add             x10, x10, HEAP, lsl #32
    // 0xe8f550: stur            x10, [fp, #-0x38]
    // 0xe8f554: LoadField: r2 = r10->field_7
    //     0xe8f554: ldur            w2, [x10, #7]
    // 0xe8f558: DecompressPointer r2
    //     0xe8f558: add             x2, x2, HEAP, lsl #32
    // 0xe8f55c: r1 = Null
    //     0xe8f55c: mov             x1, NULL
    // 0xe8f560: r3 = <int, X0>
    //     0xe8f560: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db00] TypeArguments: <int, X0>
    //     0xe8f564: ldr             x3, [x3, #0xb00]
    // 0xe8f568: r30 = InstantiateTypeArgumentsStub
    //     0xe8f568: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe8f56c: LoadField: r30 = r30->field_7
    //     0xe8f56c: ldur            lr, [lr, #7]
    // 0xe8f570: blr             lr
    // 0xe8f574: mov             x1, x0
    // 0xe8f578: stur            x0, [fp, #-0x60]
    // 0xe8f57c: r0 = ListMapView()
    //     0xe8f57c: bl              #0x666568  ; AllocateListMapViewStub -> ListMapView<C1X0> (size=0x10)
    // 0xe8f580: mov             x1, x0
    // 0xe8f584: ldur            x0, [fp, #-0x38]
    // 0xe8f588: stur            x1, [fp, #-0x68]
    // 0xe8f58c: StoreField: r1->field_b = r0
    //     0xe8f58c: stur            w0, [x1, #0xb]
    // 0xe8f590: r1 = 1
    //     0xe8f590: movz            x1, #0x1
    // 0xe8f594: r0 = AllocateContext()
    //     0xe8f594: bl              #0xec126c  ; AllocateContextStub
    // 0xe8f598: mov             x4, x0
    // 0xe8f59c: ldur            x0, [fp, #-0x68]
    // 0xe8f5a0: stur            x4, [fp, #-0x38]
    // 0xe8f5a4: StoreField: r4->field_f = r0
    //     0xe8f5a4: stur            w0, [x4, #0xf]
    // 0xe8f5a8: ldur            x2, [fp, #-0x60]
    // 0xe8f5ac: r1 = Null
    //     0xe8f5ac: mov             x1, NULL
    // 0xe8f5b0: r3 = <MapEntry<X0, X1>>
    //     0xe8f5b0: ldr             x3, [PP, #0xa40]  ; [pp+0xa40] TypeArguments: <MapEntry<X0, X1>>
    // 0xe8f5b4: r30 = InstantiateTypeArgumentsStub
    //     0xe8f5b4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe8f5b8: LoadField: r30 = r30->field_7
    //     0xe8f5b8: ldur            lr, [lr, #7]
    // 0xe8f5bc: blr             lr
    // 0xe8f5c0: ldur            x1, [fp, #-0x68]
    // 0xe8f5c4: stur            x0, [fp, #-0x68]
    // 0xe8f5c8: r0 = keys()
    //     0xe8f5c8: bl              #0xd12db0  ; [dart:_internal] ListMapView::keys
    // 0xe8f5cc: ldur            x2, [fp, #-0x38]
    // 0xe8f5d0: ldur            x3, [fp, #-0x60]
    // 0xe8f5d4: r1 = Function '<anonymous closure>':.
    //     0xe8f5d4: add             x1, PP, #0x23, lsl #12  ; [pp+0x232d8] AnonymousClosure: (0x7f8224), in [dart:collection] MapBase::entries (0x7f814c)
    //     0xe8f5d8: ldr             x1, [x1, #0x2d8]
    // 0xe8f5dc: stur            x0, [fp, #-0x38]
    // 0xe8f5e0: r0 = AllocateClosureTA()
    //     0xe8f5e0: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xe8f5e4: mov             x1, x0
    // 0xe8f5e8: ldur            x0, [fp, #-0x38]
    // 0xe8f5ec: r2 = LoadClassIdInstr(r0)
    //     0xe8f5ec: ldur            x2, [x0, #-1]
    //     0xe8f5f0: ubfx            x2, x2, #0xc, #0x14
    // 0xe8f5f4: ldur            x16, [fp, #-0x68]
    // 0xe8f5f8: stp             x0, x16, [SP, #8]
    // 0xe8f5fc: str             x1, [SP]
    // 0xe8f600: mov             x0, x2
    // 0xe8f604: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe8f604: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe8f608: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe8f608: movz            x17, #0xf28c
    //     0xe8f60c: add             lr, x0, x17
    //     0xe8f610: ldr             lr, [x21, lr, lsl #3]
    //     0xe8f614: blr             lr
    // 0xe8f618: mov             x1, x0
    // 0xe8f61c: stur            x1, [fp, #-0x60]
    // 0xe8f620: LoadField: r2 = r1->field_7
    //     0xe8f620: ldur            w2, [x1, #7]
    // 0xe8f624: DecompressPointer r2
    //     0xe8f624: add             x2, x2, HEAP, lsl #32
    // 0xe8f628: stur            x2, [fp, #-0x38]
    // 0xe8f62c: LoadField: r0 = r1->field_b
    //     0xe8f62c: ldur            w0, [x1, #0xb]
    // 0xe8f630: DecompressPointer r0
    //     0xe8f630: add             x0, x0, HEAP, lsl #32
    // 0xe8f634: r3 = LoadClassIdInstr(r0)
    //     0xe8f634: ldur            x3, [x0, #-1]
    //     0xe8f638: ubfx            x3, x3, #0xc, #0x14
    // 0xe8f63c: str             x0, [SP]
    // 0xe8f640: mov             x0, x3
    // 0xe8f644: r0 = GDT[cid_x0 + 0xc834]()
    //     0xe8f644: movz            x17, #0xc834
    //     0xe8f648: add             lr, x0, x17
    //     0xe8f64c: ldr             lr, [x21, lr, lsl #3]
    //     0xe8f650: blr             lr
    // 0xe8f654: r1 = LoadInt32Instr(r0)
    //     0xe8f654: sbfx            x1, x0, #1, #0x1f
    //     0xe8f658: tbz             w0, #0, #0xe8f660
    //     0xe8f65c: ldur            x1, [x0, #7]
    // 0xe8f660: stur            x1, [fp, #-0x78]
    // 0xe8f664: r5 = 0
    //     0xe8f664: movz            x5, #0
    // 0xe8f668: ldur            x4, [fp, #-0x28]
    // 0xe8f66c: ldur            x3, [fp, #-0x20]
    // 0xe8f670: ldur            x2, [fp, #-0x60]
    // 0xe8f674: stur            x5, [fp, #-0x70]
    // 0xe8f678: CheckStackOverflow
    //     0xe8f678: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8f67c: cmp             SP, x16
    //     0xe8f680: b.ls            #0xe90890
    // 0xe8f684: r0 = LoadClassIdInstr(r2)
    //     0xe8f684: ldur            x0, [x2, #-1]
    //     0xe8f688: ubfx            x0, x0, #0xc, #0x14
    // 0xe8f68c: str             x2, [SP]
    // 0xe8f690: r0 = GDT[cid_x0 + 0xc834]()
    //     0xe8f690: movz            x17, #0xc834
    //     0xe8f694: add             lr, x0, x17
    //     0xe8f698: ldr             lr, [x21, lr, lsl #3]
    //     0xe8f69c: blr             lr
    // 0xe8f6a0: r1 = LoadInt32Instr(r0)
    //     0xe8f6a0: sbfx            x1, x0, #1, #0x1f
    //     0xe8f6a4: tbz             w0, #0, #0xe8f6ac
    //     0xe8f6a8: ldur            x1, [x0, #7]
    // 0xe8f6ac: ldur            x3, [fp, #-0x78]
    // 0xe8f6b0: cmp             x3, x1
    // 0xe8f6b4: b.ne            #0xe907e0
    // 0xe8f6b8: ldur            x4, [fp, #-0x70]
    // 0xe8f6bc: cmp             x4, x1
    // 0xe8f6c0: b.ge            #0xe8fb50
    // 0xe8f6c4: ldur            x5, [fp, #-0x60]
    // 0xe8f6c8: r0 = LoadClassIdInstr(r5)
    //     0xe8f6c8: ldur            x0, [x5, #-1]
    //     0xe8f6cc: ubfx            x0, x0, #0xc, #0x14
    // 0xe8f6d0: mov             x1, x5
    // 0xe8f6d4: mov             x2, x4
    // 0xe8f6d8: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xe8f6d8: movz            x17, #0xd28f
    //     0xe8f6dc: add             lr, x0, x17
    //     0xe8f6e0: ldr             lr, [x21, lr, lsl #3]
    //     0xe8f6e4: blr             lr
    // 0xe8f6e8: mov             x3, x0
    // 0xe8f6ec: ldur            x0, [fp, #-0x70]
    // 0xe8f6f0: stur            x3, [fp, #-0x68]
    // 0xe8f6f4: add             x5, x0, #1
    // 0xe8f6f8: stur            x5, [fp, #-0x80]
    // 0xe8f6fc: cmp             w3, NULL
    // 0xe8f700: b.ne            #0xe8f734
    // 0xe8f704: mov             x0, x3
    // 0xe8f708: ldur            x2, [fp, #-0x38]
    // 0xe8f70c: r1 = Null
    //     0xe8f70c: mov             x1, NULL
    // 0xe8f710: cmp             w2, NULL
    // 0xe8f714: b.eq            #0xe8f734
    // 0xe8f718: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe8f718: ldur            w4, [x2, #0x17]
    // 0xe8f71c: DecompressPointer r4
    //     0xe8f71c: add             x4, x4, HEAP, lsl #32
    // 0xe8f720: r8 = X0
    //     0xe8f720: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe8f724: LoadField: r9 = r4->field_7
    //     0xe8f724: ldur            x9, [x4, #7]
    // 0xe8f728: r3 = Null
    //     0xe8f728: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e9c8] Null
    //     0xe8f72c: ldr             x3, [x3, #0x9c8]
    // 0xe8f730: blr             x9
    // 0xe8f734: ldur            x0, [fp, #-0x68]
    // 0xe8f738: LoadField: r4 = r0->field_b
    //     0xe8f738: ldur            w4, [x0, #0xb]
    // 0xe8f73c: DecompressPointer r4
    //     0xe8f73c: add             x4, x4, HEAP, lsl #32
    // 0xe8f740: stur            x4, [fp, #-0x90]
    // 0xe8f744: LoadField: r5 = r0->field_f
    //     0xe8f744: ldur            w5, [x0, #0xf]
    // 0xe8f748: DecompressPointer r5
    //     0xe8f748: add             x5, x5, HEAP, lsl #32
    // 0xe8f74c: stur            x5, [fp, #-0x88]
    // 0xe8f750: r0 = LoadClassIdInstr(r5)
    //     0xe8f750: ldur            x0, [x5, #-1]
    //     0xe8f754: ubfx            x0, x0, #0xc, #0x14
    // 0xe8f758: mov             x1, x5
    // 0xe8f75c: ldur            x2, [fp, #-0x10]
    // 0xe8f760: r3 = Instance_BoxConstraints
    //     0xe8f760: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e9d8] Obj!BoxConstraints@e0c561
    //     0xe8f764: ldr             x3, [x3, #0x9d8]
    // 0xe8f768: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe8f768: sub             lr, x0, #0xf89
    //     0xe8f76c: ldr             lr, [x21, lr, lsl #3]
    //     0xe8f770: blr             lr
    // 0xe8f774: ldur            x0, [fp, #-0x88]
    // 0xe8f778: LoadField: r1 = r0->field_7
    //     0xe8f778: ldur            w1, [x0, #7]
    // 0xe8f77c: DecompressPointer r1
    //     0xe8f77c: add             x1, x1, HEAP, lsl #32
    // 0xe8f780: cmp             w1, NULL
    // 0xe8f784: b.eq            #0xe90898
    // 0xe8f788: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe8f788: ldur            d0, [x1, #0x17]
    // 0xe8f78c: d1 = inf
    //     0xe8f78c: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8f790: fcmp            d0, d1
    // 0xe8f794: b.ne            #0xe8f7a0
    // 0xe8f798: d2 = 0.000000
    //     0xe8f798: eor             v2.16b, v2.16b, v2.16b
    // 0xe8f79c: b               #0xe8f7a4
    // 0xe8f7a0: mov             v2.16b, v0.16b
    // 0xe8f7a4: stur            d2, [fp, #-0xc8]
    // 0xe8f7a8: fcmp            d0, d1
    // 0xe8f7ac: b.ne            #0xe8f7b8
    // 0xe8f7b0: d0 = 1.000000
    //     0xe8f7b0: fmov            d0, #1.00000000
    // 0xe8f7b4: b               #0xe8f7bc
    // 0xe8f7b8: d0 = 0.000000
    //     0xe8f7b8: eor             v0.16b, v0.16b, v0.16b
    // 0xe8f7bc: ldur            x2, [fp, #-0x28]
    // 0xe8f7c0: ldur            x1, [fp, #-0x90]
    // 0xe8f7c4: stur            d0, [fp, #-0xc0]
    // 0xe8f7c8: LoadField: r0 = r2->field_b
    //     0xe8f7c8: ldur            w0, [x2, #0xb]
    // 0xe8f7cc: r3 = 60
    //     0xe8f7cc: movz            x3, #0x3c
    // 0xe8f7d0: branchIfSmi(r1, 0xe8f7dc)
    //     0xe8f7d0: tbz             w1, #0, #0xe8f7dc
    // 0xe8f7d4: r3 = LoadClassIdInstr(r1)
    //     0xe8f7d4: ldur            x3, [x1, #-1]
    //     0xe8f7d8: ubfx            x3, x3, #0xc, #0x14
    // 0xe8f7dc: stp             x0, x1, [SP]
    // 0xe8f7e0: mov             x0, x3
    // 0xe8f7e4: r0 = GDT[cid_x0 + -0xe7f]()
    //     0xe8f7e4: sub             lr, x0, #0xe7f
    //     0xe8f7e8: ldr             lr, [x21, lr, lsl #3]
    //     0xe8f7ec: blr             lr
    // 0xe8f7f0: tbnz            w0, #4, #0xe8f938
    // 0xe8f7f4: ldur            x0, [fp, #-0x28]
    // 0xe8f7f8: LoadField: r1 = r0->field_b
    //     0xe8f7f8: ldur            w1, [x0, #0xb]
    // 0xe8f7fc: LoadField: r2 = r0->field_f
    //     0xe8f7fc: ldur            w2, [x0, #0xf]
    // 0xe8f800: DecompressPointer r2
    //     0xe8f800: add             x2, x2, HEAP, lsl #32
    // 0xe8f804: LoadField: r3 = r2->field_b
    //     0xe8f804: ldur            w3, [x2, #0xb]
    // 0xe8f808: r2 = LoadInt32Instr(r1)
    //     0xe8f808: sbfx            x2, x1, #1, #0x1f
    // 0xe8f80c: stur            x2, [fp, #-0x70]
    // 0xe8f810: r1 = LoadInt32Instr(r3)
    //     0xe8f810: sbfx            x1, x3, #1, #0x1f
    // 0xe8f814: cmp             x2, x1
    // 0xe8f818: b.ne            #0xe8f824
    // 0xe8f81c: mov             x1, x0
    // 0xe8f820: r0 = _growToNextCapacity()
    //     0xe8f820: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8f824: ldur            x2, [fp, #-0x28]
    // 0xe8f828: ldur            x4, [fp, #-0x20]
    // 0xe8f82c: ldur            d0, [fp, #-0xc0]
    // 0xe8f830: ldur            x3, [fp, #-0x70]
    // 0xe8f834: add             x0, x3, #1
    // 0xe8f838: lsl             x1, x0, #1
    // 0xe8f83c: StoreField: r2->field_b = r1
    //     0xe8f83c: stur            w1, [x2, #0xb]
    // 0xe8f840: LoadField: r1 = r2->field_f
    //     0xe8f840: ldur            w1, [x2, #0xf]
    // 0xe8f844: DecompressPointer r1
    //     0xe8f844: add             x1, x1, HEAP, lsl #32
    // 0xe8f848: r0 = inline_Allocate_Double()
    //     0xe8f848: ldp             x0, x5, [THR, #0x50]  ; THR::top
    //     0xe8f84c: add             x0, x0, #0x10
    //     0xe8f850: cmp             x5, x0
    //     0xe8f854: b.ls            #0xe9089c
    //     0xe8f858: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8f85c: sub             x0, x0, #0xf
    //     0xe8f860: movz            x5, #0xe15c
    //     0xe8f864: movk            x5, #0x3, lsl #16
    //     0xe8f868: stur            x5, [x0, #-1]
    // 0xe8f86c: StoreField: r0->field_7 = d0
    //     0xe8f86c: stur            d0, [x0, #7]
    // 0xe8f870: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe8f870: add             x25, x1, x3, lsl #2
    //     0xe8f874: add             x25, x25, #0xf
    //     0xe8f878: str             w0, [x25]
    //     0xe8f87c: tbz             w0, #0, #0xe8f898
    //     0xe8f880: ldurb           w16, [x1, #-1]
    //     0xe8f884: ldurb           w17, [x0, #-1]
    //     0xe8f888: and             x16, x17, x16, lsr #2
    //     0xe8f88c: tst             x16, HEAP, lsr #32
    //     0xe8f890: b.eq            #0xe8f898
    //     0xe8f894: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8f898: LoadField: r0 = r4->field_b
    //     0xe8f898: ldur            w0, [x4, #0xb]
    // 0xe8f89c: LoadField: r1 = r4->field_f
    //     0xe8f89c: ldur            w1, [x4, #0xf]
    // 0xe8f8a0: DecompressPointer r1
    //     0xe8f8a0: add             x1, x1, HEAP, lsl #32
    // 0xe8f8a4: LoadField: r3 = r1->field_b
    //     0xe8f8a4: ldur            w3, [x1, #0xb]
    // 0xe8f8a8: r5 = LoadInt32Instr(r0)
    //     0xe8f8a8: sbfx            x5, x0, #1, #0x1f
    // 0xe8f8ac: stur            x5, [fp, #-0x70]
    // 0xe8f8b0: r0 = LoadInt32Instr(r3)
    //     0xe8f8b0: sbfx            x0, x3, #1, #0x1f
    // 0xe8f8b4: cmp             x5, x0
    // 0xe8f8b8: b.ne            #0xe8f8c4
    // 0xe8f8bc: mov             x1, x4
    // 0xe8f8c0: r0 = _growToNextCapacity()
    //     0xe8f8c0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe8f8c4: ldur            x3, [fp, #-0x20]
    // 0xe8f8c8: ldur            d1, [fp, #-0xc8]
    // 0xe8f8cc: ldur            x2, [fp, #-0x70]
    // 0xe8f8d0: add             x0, x2, #1
    // 0xe8f8d4: lsl             x1, x0, #1
    // 0xe8f8d8: StoreField: r3->field_b = r1
    //     0xe8f8d8: stur            w1, [x3, #0xb]
    // 0xe8f8dc: LoadField: r1 = r3->field_f
    //     0xe8f8dc: ldur            w1, [x3, #0xf]
    // 0xe8f8e0: DecompressPointer r1
    //     0xe8f8e0: add             x1, x1, HEAP, lsl #32
    // 0xe8f8e4: r0 = inline_Allocate_Double()
    //     0xe8f8e4: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xe8f8e8: add             x0, x0, #0x10
    //     0xe8f8ec: cmp             x4, x0
    //     0xe8f8f0: b.ls            #0xe908bc
    //     0xe8f8f4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8f8f8: sub             x0, x0, #0xf
    //     0xe8f8fc: movz            x4, #0xe15c
    //     0xe8f900: movk            x4, #0x3, lsl #16
    //     0xe8f904: stur            x4, [x0, #-1]
    // 0xe8f908: StoreField: r0->field_7 = d1
    //     0xe8f908: stur            d1, [x0, #7]
    // 0xe8f90c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe8f90c: add             x25, x1, x2, lsl #2
    //     0xe8f910: add             x25, x25, #0xf
    //     0xe8f914: str             w0, [x25]
    //     0xe8f918: tbz             w0, #0, #0xe8f934
    //     0xe8f91c: ldurb           w16, [x1, #-1]
    //     0xe8f920: ldurb           w17, [x0, #-1]
    //     0xe8f924: and             x16, x17, x16, lsr #2
    //     0xe8f928: tst             x16, HEAP, lsr #32
    //     0xe8f92c: b.eq            #0xe8f934
    //     0xe8f930: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8f934: b               #0xe8fb44
    // 0xe8f938: ldur            x3, [fp, #-0x20]
    // 0xe8f93c: ldur            d1, [fp, #-0xc8]
    // 0xe8f940: ldur            d0, [fp, #-0xc0]
    // 0xe8f944: d2 = 0.000000
    //     0xe8f944: eor             v2.16b, v2.16b, v2.16b
    // 0xe8f948: fcmp            d0, d2
    // 0xe8f94c: b.le            #0xe8fa20
    // 0xe8f950: ldur            x4, [fp, #-0x28]
    // 0xe8f954: ldur            x2, [fp, #-0x90]
    // 0xe8f958: LoadField: r0 = r4->field_b
    //     0xe8f958: ldur            w0, [x4, #0xb]
    // 0xe8f95c: r5 = LoadInt32Instr(r2)
    //     0xe8f95c: sbfx            x5, x2, #1, #0x1f
    //     0xe8f960: tbz             w2, #0, #0xe8f968
    //     0xe8f964: ldur            x5, [x2, #7]
    // 0xe8f968: r1 = LoadInt32Instr(r0)
    //     0xe8f968: sbfx            x1, x0, #1, #0x1f
    // 0xe8f96c: mov             x0, x1
    // 0xe8f970: mov             x1, x5
    // 0xe8f974: cmp             x1, x0
    // 0xe8f978: b.hs            #0xe908dc
    // 0xe8f97c: LoadField: r1 = r4->field_f
    //     0xe8f97c: ldur            w1, [x4, #0xf]
    // 0xe8f980: DecompressPointer r1
    //     0xe8f980: add             x1, x1, HEAP, lsl #32
    // 0xe8f984: ArrayLoad: r0 = r1[r5]  ; Unknown_4
    //     0xe8f984: add             x16, x1, x5, lsl #2
    //     0xe8f988: ldur            w0, [x16, #0xf]
    // 0xe8f98c: DecompressPointer r0
    //     0xe8f98c: add             x0, x0, HEAP, lsl #32
    // 0xe8f990: LoadField: d3 = r0->field_7
    //     0xe8f990: ldur            d3, [x0, #7]
    // 0xe8f994: fcmp            d3, d0
    // 0xe8f998: b.le            #0xe8f9a4
    // 0xe8f99c: LoadField: d0 = r0->field_7
    //     0xe8f99c: ldur            d0, [x0, #7]
    // 0xe8f9a0: b               #0xe8f9cc
    // 0xe8f9a4: fcmp            d0, d3
    // 0xe8f9a8: b.gt            #0xe8f9cc
    // 0xe8f9ac: fcmp            d3, d2
    // 0xe8f9b0: b.ne            #0xe8f9c0
    // 0xe8f9b4: fadd            d4, d3, d0
    // 0xe8f9b8: mov             v0.16b, v4.16b
    // 0xe8f9bc: b               #0xe8f9cc
    // 0xe8f9c0: fcmp            d0, d0
    // 0xe8f9c4: b.vs            #0xe8f9cc
    // 0xe8f9c8: LoadField: d0 = r0->field_7
    //     0xe8f9c8: ldur            d0, [x0, #7]
    // 0xe8f9cc: r0 = inline_Allocate_Double()
    //     0xe8f9cc: ldp             x0, x6, [THR, #0x50]  ; THR::top
    //     0xe8f9d0: add             x0, x0, #0x10
    //     0xe8f9d4: cmp             x6, x0
    //     0xe8f9d8: b.ls            #0xe908e0
    //     0xe8f9dc: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8f9e0: sub             x0, x0, #0xf
    //     0xe8f9e4: movz            x6, #0xe15c
    //     0xe8f9e8: movk            x6, #0x3, lsl #16
    //     0xe8f9ec: stur            x6, [x0, #-1]
    // 0xe8f9f0: StoreField: r0->field_7 = d0
    //     0xe8f9f0: stur            d0, [x0, #7]
    // 0xe8f9f4: ArrayStore: r1[r5] = r0  ; List_4
    //     0xe8f9f4: add             x25, x1, x5, lsl #2
    //     0xe8f9f8: add             x25, x25, #0xf
    //     0xe8f9fc: str             w0, [x25]
    //     0xe8fa00: tbz             w0, #0, #0xe8fa1c
    //     0xe8fa04: ldurb           w16, [x1, #-1]
    //     0xe8fa08: ldurb           w17, [x0, #-1]
    //     0xe8fa0c: and             x16, x17, x16, lsr #2
    //     0xe8fa10: tst             x16, HEAP, lsr #32
    //     0xe8fa14: b.eq            #0xe8fa1c
    //     0xe8fa18: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8fa1c: b               #0xe8fa28
    // 0xe8fa20: ldur            x4, [fp, #-0x28]
    // 0xe8fa24: ldur            x2, [fp, #-0x90]
    // 0xe8fa28: LoadField: r0 = r3->field_b
    //     0xe8fa28: ldur            w0, [x3, #0xb]
    // 0xe8fa2c: r5 = LoadInt32Instr(r2)
    //     0xe8fa2c: sbfx            x5, x2, #1, #0x1f
    //     0xe8fa30: tbz             w2, #0, #0xe8fa38
    //     0xe8fa34: ldur            x5, [x2, #7]
    // 0xe8fa38: stur            x5, [fp, #-0x70]
    // 0xe8fa3c: r1 = LoadInt32Instr(r0)
    //     0xe8fa3c: sbfx            x1, x0, #1, #0x1f
    // 0xe8fa40: mov             x0, x1
    // 0xe8fa44: mov             x1, x5
    // 0xe8fa48: cmp             x1, x0
    // 0xe8fa4c: b.hs            #0xe90910
    // 0xe8fa50: LoadField: r6 = r3->field_f
    //     0xe8fa50: ldur            w6, [x3, #0xf]
    // 0xe8fa54: DecompressPointer r6
    //     0xe8fa54: add             x6, x6, HEAP, lsl #32
    // 0xe8fa58: stur            x6, [fp, #-0x88]
    // 0xe8fa5c: ArrayLoad: r0 = r6[r5]  ; Unknown_4
    //     0xe8fa5c: add             x16, x6, x5, lsl #2
    //     0xe8fa60: ldur            w0, [x16, #0xf]
    // 0xe8fa64: DecompressPointer r0
    //     0xe8fa64: add             x0, x0, HEAP, lsl #32
    // 0xe8fa68: LoadField: d0 = r0->field_7
    //     0xe8fa68: ldur            d0, [x0, #7]
    // 0xe8fa6c: fcmp            d0, d1
    // 0xe8fa70: b.le            #0xe8fa7c
    // 0xe8fa74: LoadField: d0 = r0->field_7
    //     0xe8fa74: ldur            d0, [x0, #7]
    // 0xe8fa78: b               #0xe8fab4
    // 0xe8fa7c: fcmp            d1, d0
    // 0xe8fa80: b.le            #0xe8fa8c
    // 0xe8fa84: mov             v0.16b, v1.16b
    // 0xe8fa88: b               #0xe8fab4
    // 0xe8fa8c: fcmp            d0, d2
    // 0xe8fa90: b.ne            #0xe8faa0
    // 0xe8fa94: fadd            d3, d0, d1
    // 0xe8fa98: mov             v0.16b, v3.16b
    // 0xe8fa9c: b               #0xe8fab4
    // 0xe8faa0: fcmp            d1, d1
    // 0xe8faa4: b.vc            #0xe8fab0
    // 0xe8faa8: mov             v0.16b, v1.16b
    // 0xe8faac: b               #0xe8fab4
    // 0xe8fab0: LoadField: d0 = r0->field_7
    //     0xe8fab0: ldur            d0, [x0, #7]
    // 0xe8fab4: r7 = inline_Allocate_Double()
    //     0xe8fab4: ldp             x7, x0, [THR, #0x50]  ; THR::top
    //     0xe8fab8: add             x7, x7, #0x10
    //     0xe8fabc: cmp             x0, x7
    //     0xe8fac0: b.ls            #0xe90914
    //     0xe8fac4: str             x7, [THR, #0x50]  ; THR::top
    //     0xe8fac8: sub             x7, x7, #0xf
    //     0xe8facc: movz            x0, #0xe15c
    //     0xe8fad0: movk            x0, #0x3, lsl #16
    //     0xe8fad4: stur            x0, [x7, #-1]
    // 0xe8fad8: StoreField: r7->field_7 = d0
    //     0xe8fad8: stur            d0, [x7, #7]
    // 0xe8fadc: mov             x0, x7
    // 0xe8fae0: ldur            x2, [fp, #-0x48]
    // 0xe8fae4: stur            x7, [fp, #-0x68]
    // 0xe8fae8: r1 = Null
    //     0xe8fae8: mov             x1, NULL
    // 0xe8faec: cmp             w2, NULL
    // 0xe8faf0: b.eq            #0xe8fb10
    // 0xe8faf4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe8faf4: ldur            w4, [x2, #0x17]
    // 0xe8faf8: DecompressPointer r4
    //     0xe8faf8: add             x4, x4, HEAP, lsl #32
    // 0xe8fafc: r8 = X0
    //     0xe8fafc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe8fb00: LoadField: r9 = r4->field_7
    //     0xe8fb00: ldur            x9, [x4, #7]
    // 0xe8fb04: r3 = Null
    //     0xe8fb04: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e9e0] Null
    //     0xe8fb08: ldr             x3, [x3, #0x9e0]
    // 0xe8fb0c: blr             x9
    // 0xe8fb10: ldur            x1, [fp, #-0x88]
    // 0xe8fb14: ldur            x0, [fp, #-0x68]
    // 0xe8fb18: ldur            x2, [fp, #-0x70]
    // 0xe8fb1c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe8fb1c: add             x25, x1, x2, lsl #2
    //     0xe8fb20: add             x25, x25, #0xf
    //     0xe8fb24: str             w0, [x25]
    //     0xe8fb28: tbz             w0, #0, #0xe8fb44
    //     0xe8fb2c: ldurb           w16, [x1, #-1]
    //     0xe8fb30: ldurb           w17, [x0, #-1]
    //     0xe8fb34: and             x16, x17, x16, lsr #2
    //     0xe8fb38: tst             x16, HEAP, lsr #32
    //     0xe8fb3c: b.eq            #0xe8fb44
    //     0xe8fb40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8fb44: ldur            x5, [fp, #-0x80]
    // 0xe8fb48: ldur            x1, [fp, #-0x78]
    // 0xe8fb4c: b               #0xe8f668
    // 0xe8fb50: ldur            x1, [fp, #-0x40]
    // 0xe8fb54: ldur            x0, [fp, #-8]
    // 0xe8fb58: ldur            x6, [fp, #-0x20]
    // 0xe8fb5c: ldur            x4, [fp, #-0x58]
    // 0xe8fb60: ldur            x7, [fp, #-0x48]
    // 0xe8fb64: ldur            x5, [fp, #-0x50]
    // 0xe8fb68: b               #0xe8f504
    // 0xe8fb6c: mov             x0, x6
    // 0xe8fb70: LoadField: r1 = r0->field_b
    //     0xe8fb70: ldur            w1, [x0, #0xb]
    // 0xe8fb74: cbnz            w1, #0xe8fbc8
    // 0xe8fb78: ldur            x0, [fp, #-8]
    // 0xe8fb7c: ldur            x1, [fp, #-0x18]
    // 0xe8fb80: r0 = smallest()
    //     0xe8fb80: bl              #0xe8edec  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::smallest
    // 0xe8fb84: mov             x3, x0
    // 0xe8fb88: r1 = Null
    //     0xe8fb88: mov             x1, NULL
    // 0xe8fb8c: r2 = Instance_PdfPoint
    //     0xe8fb8c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xe8fb90: ldr             x2, [x2, #0x730]
    // 0xe8fb94: r0 = PdfRect.fromPoints()
    //     0xe8fb94: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xe8fb98: ldur            x3, [fp, #-8]
    // 0xe8fb9c: StoreField: r3->field_7 = r0
    //     0xe8fb9c: stur            w0, [x3, #7]
    //     0xe8fba0: ldurb           w16, [x3, #-1]
    //     0xe8fba4: ldurb           w17, [x0, #-1]
    //     0xe8fba8: and             x16, x17, x16, lsr #2
    //     0xe8fbac: tst             x16, HEAP, lsr #32
    //     0xe8fbb0: b.eq            #0xe8fbb8
    //     0xe8fbb4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe8fbb8: r0 = Null
    //     0xe8fbb8: mov             x0, NULL
    // 0xe8fbbc: LeaveFrame
    //     0xe8fbbc: mov             SP, fp
    //     0xe8fbc0: ldp             fp, lr, [SP], #0x10
    // 0xe8fbc4: ret
    //     0xe8fbc4: ret             
    // 0xe8fbc8: ldur            x3, [fp, #-8]
    // 0xe8fbcc: ldur            x4, [fp, #-0x18]
    // 0xe8fbd0: r1 = Function '<anonymous closure>':.
    //     0xe8fbd0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9f0] AnonymousClosure: (0x756934), in [package:pdf/src/widgets/table.dart] Table::layout (0xe8f460)
    //     0xe8fbd4: ldr             x1, [x1, #0x9f0]
    // 0xe8fbd8: r2 = Null
    //     0xe8fbd8: mov             x2, NULL
    // 0xe8fbdc: r0 = AllocateClosure()
    //     0xe8fbdc: bl              #0xec1630  ; AllocateClosureStub
    // 0xe8fbe0: r16 = <double>
    //     0xe8fbe0: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe8fbe4: ldur            lr, [fp, #-0x20]
    // 0xe8fbe8: stp             lr, x16, [SP, #0x10]
    // 0xe8fbec: r16 = 0.000000
    //     0xe8fbec: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe8fbf0: stp             x0, x16, [SP]
    // 0xe8fbf4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe8fbf4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe8fbf8: r0 = fold()
    //     0xe8fbf8: bl              #0x895b28  ; [dart:collection] ListBase::fold
    // 0xe8fbfc: mov             x3, x0
    // 0xe8fc00: ldur            x0, [fp, #-0x18]
    // 0xe8fc04: stur            x3, [fp, #-0x38]
    // 0xe8fc08: LoadField: d0 = r0->field_f
    //     0xe8fc08: ldur            d0, [x0, #0xf]
    // 0xe8fc0c: stur            d0, [fp, #-0xc0]
    // 0xe8fc10: d1 = inf
    //     0xe8fc10: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8fc14: fcmp            d1, d0
    // 0xe8fc18: b.le            #0xe8fe28
    // 0xe8fc1c: ldur            x5, [fp, #-0x28]
    // 0xe8fc20: ldur            x4, [fp, #-0x20]
    // 0xe8fc24: r1 = Function '<anonymous closure>':.
    //     0xe8fc24: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9f8] AnonymousClosure: (0xe90a48), in [package:pdf/src/widgets/table.dart] Table::layout (0xe8f460)
    //     0xe8fc28: ldr             x1, [x1, #0x9f8]
    // 0xe8fc2c: r2 = Null
    //     0xe8fc2c: mov             x2, NULL
    // 0xe8fc30: r0 = AllocateClosure()
    //     0xe8fc30: bl              #0xec1630  ; AllocateClosureStub
    // 0xe8fc34: ldur            x1, [fp, #-0x28]
    // 0xe8fc38: mov             x2, x0
    // 0xe8fc3c: r0 = reduce()
    //     0xe8fc3c: bl              #0x8a5ec4  ; [dart:collection] ListBase::reduce
    // 0xe8fc40: mov             x2, x0
    // 0xe8fc44: ldur            x3, [fp, #-0x20]
    // 0xe8fc48: LoadField: r0 = r3->field_b
    //     0xe8fc48: ldur            w0, [x3, #0xb]
    // 0xe8fc4c: r4 = LoadInt32Instr(r0)
    //     0xe8fc4c: sbfx            x4, x0, #1, #0x1f
    // 0xe8fc50: ldur            x0, [fp, #-0x28]
    // 0xe8fc54: LoadField: r5 = r0->field_b
    //     0xe8fc54: ldur            w5, [x0, #0xb]
    // 0xe8fc58: r6 = LoadInt32Instr(r5)
    //     0xe8fc58: sbfx            x6, x5, #1, #0x1f
    // 0xe8fc5c: LoadField: r7 = r0->field_f
    //     0xe8fc5c: ldur            w7, [x0, #0xf]
    // 0xe8fc60: DecompressPointer r7
    //     0xe8fc60: add             x7, x7, HEAP, lsl #32
    // 0xe8fc64: LoadField: r8 = r3->field_f
    //     0xe8fc64: ldur            w8, [x3, #0xf]
    // 0xe8fc68: DecompressPointer r8
    //     0xe8fc68: add             x8, x8, HEAP, lsl #32
    // 0xe8fc6c: ldur            x0, [fp, #-0x38]
    // 0xe8fc70: LoadField: d0 = r0->field_7
    //     0xe8fc70: ldur            d0, [x0, #7]
    // 0xe8fc74: LoadField: d1 = r2->field_7
    //     0xe8fc74: ldur            d1, [x2, #7]
    // 0xe8fc78: ldur            d2, [fp, #-0xc0]
    // 0xe8fc7c: d4 = 0.000000
    //     0xe8fc7c: eor             v4.16b, v4.16b, v4.16b
    // 0xe8fc80: r9 = 0
    //     0xe8fc80: movz            x9, #0
    // 0xe8fc84: d3 = 0.000000
    //     0xe8fc84: eor             v3.16b, v3.16b, v3.16b
    // 0xe8fc88: CheckStackOverflow
    //     0xe8fc88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8fc8c: cmp             SP, x16
    //     0xe8fc90: b.ls            #0xe90938
    // 0xe8fc94: cmp             x9, x4
    // 0xe8fc98: b.ge            #0xe8fd60
    // 0xe8fc9c: mov             x0, x6
    // 0xe8fca0: mov             x1, x9
    // 0xe8fca4: cmp             x1, x0
    // 0xe8fca8: b.hs            #0xe90940
    // 0xe8fcac: ArrayLoad: r0 = r7[r9]  ; Unknown_4
    //     0xe8fcac: add             x16, x7, x9, lsl #2
    //     0xe8fcb0: ldur            w0, [x16, #0xf]
    // 0xe8fcb4: DecompressPointer r0
    //     0xe8fcb4: add             x0, x0, HEAP, lsl #32
    // 0xe8fcb8: LoadField: d5 = r0->field_7
    //     0xe8fcb8: ldur            d5, [x0, #7]
    // 0xe8fcbc: fcmp            d5, d3
    // 0xe8fcc0: b.ne            #0xe8fd54
    // 0xe8fcc4: ArrayLoad: r0 = r8[r9]  ; Unknown_4
    //     0xe8fcc4: add             x16, x8, x9, lsl #2
    //     0xe8fcc8: ldur            w0, [x16, #0xf]
    // 0xe8fccc: DecompressPointer r0
    //     0xe8fccc: add             x0, x0, HEAP, lsl #32
    // 0xe8fcd0: LoadField: d5 = r0->field_7
    //     0xe8fcd0: ldur            d5, [x0, #7]
    // 0xe8fcd4: fdiv            d6, d5, d0
    // 0xe8fcd8: fmul            d5, d6, d2
    // 0xe8fcdc: fcmp            d1, d3
    // 0xe8fce0: b.eq            #0xe8fcf0
    // 0xe8fce4: LoadField: d6 = r0->field_7
    //     0xe8fce4: ldur            d6, [x0, #7]
    // 0xe8fce8: fcmp            d6, d5
    // 0xe8fcec: b.le            #0xe8fd48
    // 0xe8fcf0: r0 = inline_Allocate_Double()
    //     0xe8fcf0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe8fcf4: add             x0, x0, #0x10
    //     0xe8fcf8: cmp             x1, x0
    //     0xe8fcfc: b.ls            #0xe90944
    //     0xe8fd00: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8fd04: sub             x0, x0, #0xf
    //     0xe8fd08: movz            x1, #0xe15c
    //     0xe8fd0c: movk            x1, #0x3, lsl #16
    //     0xe8fd10: stur            x1, [x0, #-1]
    // 0xe8fd14: StoreField: r0->field_7 = d5
    //     0xe8fd14: stur            d5, [x0, #7]
    // 0xe8fd18: mov             x1, x8
    // 0xe8fd1c: ArrayStore: r1[r9] = r0  ; List_4
    //     0xe8fd1c: add             x25, x1, x9, lsl #2
    //     0xe8fd20: add             x25, x25, #0xf
    //     0xe8fd24: str             w0, [x25]
    //     0xe8fd28: tbz             w0, #0, #0xe8fd44
    //     0xe8fd2c: ldurb           w16, [x1, #-1]
    //     0xe8fd30: ldurb           w17, [x0, #-1]
    //     0xe8fd34: and             x16, x17, x16, lsr #2
    //     0xe8fd38: tst             x16, HEAP, lsr #32
    //     0xe8fd3c: b.eq            #0xe8fd44
    //     0xe8fd40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8fd44: b               #0xe8fd4c
    // 0xe8fd48: LoadField: d5 = r0->field_7
    //     0xe8fd48: ldur            d5, [x0, #7]
    // 0xe8fd4c: fadd            d6, d4, d5
    // 0xe8fd50: mov             v4.16b, v6.16b
    // 0xe8fd54: add             x0, x9, #1
    // 0xe8fd58: mov             x9, x0
    // 0xe8fd5c: b               #0xe8fc88
    // 0xe8fd60: LoadField: d0 = r2->field_7
    //     0xe8fd60: ldur            d0, [x2, #7]
    // 0xe8fd64: fcmp            d0, d3
    // 0xe8fd68: b.le            #0xe8fd7c
    // 0xe8fd6c: fsub            d1, d2, d4
    // 0xe8fd70: fdiv            d2, d1, d0
    // 0xe8fd74: mov             v0.16b, v2.16b
    // 0xe8fd78: b               #0xe8fd80
    // 0xe8fd7c: d0 = -nan
    //     0xe8fd7c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(-nan) from 0xfff8000000000000
    // 0xe8fd80: r2 = LoadInt32Instr(r5)
    //     0xe8fd80: sbfx            x2, x5, #1, #0x1f
    // 0xe8fd84: r5 = 0
    //     0xe8fd84: movz            x5, #0
    // 0xe8fd88: CheckStackOverflow
    //     0xe8fd88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8fd8c: cmp             SP, x16
    //     0xe8fd90: b.ls            #0xe90984
    // 0xe8fd94: cmp             x5, x4
    // 0xe8fd98: b.ge            #0xe8fe30
    // 0xe8fd9c: mov             x0, x2
    // 0xe8fda0: mov             x1, x5
    // 0xe8fda4: cmp             x1, x0
    // 0xe8fda8: b.hs            #0xe9098c
    // 0xe8fdac: ArrayLoad: r0 = r7[r5]  ; Unknown_4
    //     0xe8fdac: add             x16, x7, x5, lsl #2
    //     0xe8fdb0: ldur            w0, [x16, #0xf]
    // 0xe8fdb4: DecompressPointer r0
    //     0xe8fdb4: add             x0, x0, HEAP, lsl #32
    // 0xe8fdb8: LoadField: d1 = r0->field_7
    //     0xe8fdb8: ldur            d1, [x0, #7]
    // 0xe8fdbc: fcmp            d1, d3
    // 0xe8fdc0: b.le            #0xe8fe1c
    // 0xe8fdc4: fmul            d2, d0, d1
    // 0xe8fdc8: r0 = inline_Allocate_Double()
    //     0xe8fdc8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe8fdcc: add             x0, x0, #0x10
    //     0xe8fdd0: cmp             x1, x0
    //     0xe8fdd4: b.ls            #0xe90990
    //     0xe8fdd8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe8fddc: sub             x0, x0, #0xf
    //     0xe8fde0: movz            x1, #0xe15c
    //     0xe8fde4: movk            x1, #0x3, lsl #16
    //     0xe8fde8: stur            x1, [x0, #-1]
    // 0xe8fdec: StoreField: r0->field_7 = d2
    //     0xe8fdec: stur            d2, [x0, #7]
    // 0xe8fdf0: mov             x1, x8
    // 0xe8fdf4: ArrayStore: r1[r5] = r0  ; List_4
    //     0xe8fdf4: add             x25, x1, x5, lsl #2
    //     0xe8fdf8: add             x25, x25, #0xf
    //     0xe8fdfc: str             w0, [x25]
    //     0xe8fe00: tbz             w0, #0, #0xe8fe1c
    //     0xe8fe04: ldurb           w16, [x1, #-1]
    //     0xe8fe08: ldurb           w17, [x0, #-1]
    //     0xe8fe0c: and             x16, x17, x16, lsr #2
    //     0xe8fe10: tst             x16, HEAP, lsr #32
    //     0xe8fe14: b.eq            #0xe8fe1c
    //     0xe8fe18: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8fe1c: add             x0, x5, #1
    // 0xe8fe20: mov             x5, x0
    // 0xe8fe24: b               #0xe8fd88
    // 0xe8fe28: ldur            x3, [fp, #-0x20]
    // 0xe8fe2c: d3 = 0.000000
    //     0xe8fe2c: eor             v3.16b, v3.16b, v3.16b
    // 0xe8fe30: ldur            x4, [fp, #-8]
    // 0xe8fe34: ldur            x0, [fp, #-0x18]
    // 0xe8fe38: ldur            x5, [fp, #-0x58]
    // 0xe8fe3c: r1 = Function '<anonymous closure>':.
    //     0xe8fe3c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea00] AnonymousClosure: (0x756934), in [package:pdf/src/widgets/table.dart] Table::layout (0xe8f460)
    //     0xe8fe40: ldr             x1, [x1, #0xa00]
    // 0xe8fe44: r2 = Null
    //     0xe8fe44: mov             x2, NULL
    // 0xe8fe48: r0 = AllocateClosure()
    //     0xe8fe48: bl              #0xec1630  ; AllocateClosureStub
    // 0xe8fe4c: r16 = <double>
    //     0xe8fe4c: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe8fe50: ldur            lr, [fp, #-0x20]
    // 0xe8fe54: stp             lr, x16, [SP, #0x10]
    // 0xe8fe58: r16 = 0.000000
    //     0xe8fe58: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe8fe5c: stp             x0, x16, [SP]
    // 0xe8fe60: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe8fe60: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe8fe64: r0 = fold()
    //     0xe8fe64: bl              #0x895b28  ; [dart:collection] ListBase::fold
    // 0xe8fe68: mov             x3, x0
    // 0xe8fe6c: ldur            x2, [fp, #-0x58]
    // 0xe8fe70: stur            x3, [fp, #-0x68]
    // 0xe8fe74: LoadField: r0 = r2->field_b
    //     0xe8fe74: ldur            w0, [x2, #0xb]
    // 0xe8fe78: r4 = LoadInt32Instr(r0)
    //     0xe8fe78: sbfx            x4, x0, #1, #0x1f
    // 0xe8fe7c: ldur            x5, [fp, #-8]
    // 0xe8fe80: stur            x4, [fp, #-0x80]
    // 0xe8fe84: LoadField: r6 = r5->field_23
    //     0xe8fe84: ldur            w6, [x5, #0x23]
    // 0xe8fe88: DecompressPointer r6
    //     0xe8fe88: add             x6, x6, HEAP, lsl #32
    // 0xe8fe8c: ldur            x0, [fp, #-0x18]
    // 0xe8fe90: stur            x6, [fp, #-0x48]
    // 0xe8fe94: LoadField: d0 = r0->field_1f
    //     0xe8fe94: ldur            d0, [x0, #0x1f]
    // 0xe8fe98: stur            d0, [fp, #-0xd8]
    // 0xe8fe9c: ldur            x8, [fp, #-0x30]
    // 0xe8fea0: r0 = 0
    //     0xe8fea0: movz            x0, #0
    // 0xe8fea4: d1 = 0.000000
    //     0xe8fea4: eor             v1.16b, v1.16b, v1.16b
    // 0xe8fea8: ldur            x7, [fp, #-0x20]
    // 0xe8feac: stur            d1, [fp, #-0xd0]
    // 0xe8feb0: CheckStackOverflow
    //     0xe8feb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8feb4: cmp             SP, x16
    //     0xe8feb8: b.ls            #0xe909c0
    // 0xe8febc: LoadField: r1 = r2->field_b
    //     0xe8febc: ldur            w1, [x2, #0xb]
    // 0xe8fec0: r9 = LoadInt32Instr(r1)
    //     0xe8fec0: sbfx            x9, x1, #1, #0x1f
    // 0xe8fec4: cmp             x4, x9
    // 0xe8fec8: b.ne            #0xe90840
    // 0xe8fecc: cmp             x0, x9
    // 0xe8fed0: b.ge            #0xe90488
    // 0xe8fed4: LoadField: r1 = r2->field_f
    //     0xe8fed4: ldur            w1, [x2, #0xf]
    // 0xe8fed8: DecompressPointer r1
    //     0xe8fed8: add             x1, x1, HEAP, lsl #32
    // 0xe8fedc: ArrayLoad: r9 = r1[r0]  ; Unknown_4
    //     0xe8fedc: add             x16, x1, x0, lsl #2
    //     0xe8fee0: ldur            w9, [x16, #0xf]
    // 0xe8fee4: DecompressPointer r9
    //     0xe8fee4: add             x9, x9, HEAP, lsl #32
    // 0xe8fee8: add             x10, x0, #1
    // 0xe8feec: stur            x10, [fp, #-0x78]
    // 0xe8fef0: LoadField: r1 = r6->field_7
    //     0xe8fef0: ldur            x1, [x6, #7]
    // 0xe8fef4: cmp             x0, x1
    // 0xe8fef8: b.ge            #0xe8ff10
    // 0xe8fefc: LoadField: r0 = r9->field_b
    //     0xe8fefc: ldur            w0, [x9, #0xb]
    // 0xe8ff00: DecompressPointer r0
    //     0xe8ff00: add             x0, x0, HEAP, lsl #32
    // 0xe8ff04: tbz             w0, #4, #0xe8ff10
    // 0xe8ff08: mov             x2, x8
    // 0xe8ff0c: b               #0xe90450
    // 0xe8ff10: LoadField: r11 = r9->field_7
    //     0xe8ff10: ldur            w11, [x9, #7]
    // 0xe8ff14: DecompressPointer r11
    //     0xe8ff14: add             x11, x11, HEAP, lsl #32
    // 0xe8ff18: stur            x11, [fp, #-0x38]
    // 0xe8ff1c: LoadField: r0 = r11->field_b
    //     0xe8ff1c: ldur            w0, [x11, #0xb]
    // 0xe8ff20: r9 = LoadInt32Instr(r0)
    //     0xe8ff20: sbfx            x9, x0, #1, #0x1f
    // 0xe8ff24: stur            x9, [fp, #-0x70]
    // 0xe8ff28: r13 = 0
    //     0xe8ff28: movz            x13, #0
    // 0xe8ff2c: d2 = 0.000000
    //     0xe8ff2c: eor             v2.16b, v2.16b, v2.16b
    // 0xe8ff30: r12 = 0.000000
    //     0xe8ff30: ldr             x12, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe8ff34: stur            x13, [fp, #-0x50]
    // 0xe8ff38: stur            x12, [fp, #-0x28]
    // 0xe8ff3c: stur            d2, [fp, #-0xc8]
    // 0xe8ff40: CheckStackOverflow
    //     0xe8ff40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ff44: cmp             SP, x16
    //     0xe8ff48: b.ls            #0xe909c8
    // 0xe8ff4c: LoadField: r0 = r11->field_b
    //     0xe8ff4c: ldur            w0, [x11, #0xb]
    // 0xe8ff50: r14 = LoadInt32Instr(r0)
    //     0xe8ff50: sbfx            x14, x0, #1, #0x1f
    // 0xe8ff54: stur            x14, [fp, #-0x98]
    // 0xe8ff58: cmp             x9, x14
    // 0xe8ff5c: b.ne            #0xe90820
    // 0xe8ff60: cmp             x13, x14
    // 0xe8ff64: b.ge            #0xe90204
    // 0xe8ff68: LoadField: r0 = r11->field_f
    //     0xe8ff68: ldur            w0, [x11, #0xf]
    // 0xe8ff6c: DecompressPointer r0
    //     0xe8ff6c: add             x0, x0, HEAP, lsl #32
    // 0xe8ff70: ArrayLoad: r14 = r0[r13]  ; Unknown_4
    //     0xe8ff70: add             x16, x0, x13, lsl #2
    //     0xe8ff74: ldur            w14, [x16, #0xf]
    // 0xe8ff78: DecompressPointer r14
    //     0xe8ff78: add             x14, x14, HEAP, lsl #32
    // 0xe8ff7c: stur            x14, [fp, #-0x18]
    // 0xe8ff80: add             x19, x13, #1
    // 0xe8ff84: stur            x19, [fp, #-0x40]
    // 0xe8ff88: LoadField: r0 = r7->field_b
    //     0xe8ff88: ldur            w0, [x7, #0xb]
    // 0xe8ff8c: r1 = LoadInt32Instr(r0)
    //     0xe8ff8c: sbfx            x1, x0, #1, #0x1f
    // 0xe8ff90: mov             x0, x1
    // 0xe8ff94: mov             x1, x13
    // 0xe8ff98: cmp             x1, x0
    // 0xe8ff9c: b.hs            #0xe909d0
    // 0xe8ffa0: LoadField: r0 = r7->field_f
    //     0xe8ffa0: ldur            w0, [x7, #0xf]
    // 0xe8ffa4: DecompressPointer r0
    //     0xe8ffa4: add             x0, x0, HEAP, lsl #32
    // 0xe8ffa8: ArrayLoad: r1 = r0[r13]  ; Unknown_4
    //     0xe8ffa8: add             x16, x0, x13, lsl #2
    //     0xe8ffac: ldur            w1, [x16, #0xf]
    // 0xe8ffb0: DecompressPointer r1
    //     0xe8ffb0: add             x1, x1, HEAP, lsl #32
    // 0xe8ffb4: LoadField: d3 = r1->field_7
    //     0xe8ffb4: ldur            d3, [x1, #7]
    // 0xe8ffb8: stur            d3, [fp, #-0xc0]
    // 0xe8ffbc: r0 = BoxConstraints()
    //     0xe8ffbc: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe8ffc0: ldur            d0, [fp, #-0xc0]
    // 0xe8ffc4: StoreField: r0->field_7 = d0
    //     0xe8ffc4: stur            d0, [x0, #7]
    // 0xe8ffc8: StoreField: r0->field_f = d0
    //     0xe8ffc8: stur            d0, [x0, #0xf]
    // 0xe8ffcc: ArrayStore: r0[0] = rZR  ; List_8
    //     0xe8ffcc: stur            xzr, [x0, #0x17]
    // 0xe8ffd0: d0 = inf
    //     0xe8ffd0: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8ffd4: StoreField: r0->field_1f = d0
    //     0xe8ffd4: stur            d0, [x0, #0x1f]
    // 0xe8ffd8: ldur            x4, [fp, #-0x18]
    // 0xe8ffdc: r1 = LoadClassIdInstr(r4)
    //     0xe8ffdc: ldur            x1, [x4, #-1]
    //     0xe8ffe0: ubfx            x1, x1, #0xc, #0x14
    // 0xe8ffe4: mov             x3, x0
    // 0xe8ffe8: mov             x0, x1
    // 0xe8ffec: mov             x1, x4
    // 0xe8fff0: ldur            x2, [fp, #-0x10]
    // 0xe8fff4: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe8fff4: sub             lr, x0, #0xf89
    //     0xe8fff8: ldr             lr, [x21, lr, lsl #3]
    //     0xe8fffc: blr             lr
    // 0xe90000: ldur            x0, [fp, #-0x18]
    // 0xe90004: LoadField: r1 = r0->field_7
    //     0xe90004: ldur            w1, [x0, #7]
    // 0xe90008: DecompressPointer r1
    //     0xe90008: add             x1, x1, HEAP, lsl #32
    // 0xe9000c: cmp             w1, NULL
    // 0xe90010: b.eq            #0xe909d4
    // 0xe90014: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe90014: ldur            d0, [x1, #0x17]
    // 0xe90018: stur            d0, [fp, #-0xe0]
    // 0xe9001c: LoadField: d1 = r1->field_1f
    //     0xe9001c: ldur            d1, [x1, #0x1f]
    // 0xe90020: stur            d1, [fp, #-0xc0]
    // 0xe90024: r0 = PdfRect()
    //     0xe90024: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe90028: ldur            d0, [fp, #-0xc8]
    // 0xe9002c: StoreField: r0->field_7 = d0
    //     0xe9002c: stur            d0, [x0, #7]
    // 0xe90030: ldur            d1, [fp, #-0xd0]
    // 0xe90034: StoreField: r0->field_f = d1
    //     0xe90034: stur            d1, [x0, #0xf]
    // 0xe90038: ldur            d2, [fp, #-0xe0]
    // 0xe9003c: ArrayStore: r0[0] = d2  ; List_8
    //     0xe9003c: stur            d2, [x0, #0x17]
    // 0xe90040: ldur            d2, [fp, #-0xc0]
    // 0xe90044: StoreField: r0->field_1f = d2
    //     0xe90044: stur            d2, [x0, #0x1f]
    // 0xe90048: ldur            x1, [fp, #-0x18]
    // 0xe9004c: StoreField: r1->field_7 = r0
    //     0xe9004c: stur            w0, [x1, #7]
    //     0xe90050: ldurb           w16, [x1, #-1]
    //     0xe90054: ldurb           w17, [x0, #-1]
    //     0xe90058: and             x16, x17, x16, lsr #2
    //     0xe9005c: tst             x16, HEAP, lsr #32
    //     0xe90060: b.eq            #0xe90068
    //     0xe90064: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe90068: ldur            x2, [fp, #-0x20]
    // 0xe9006c: LoadField: r0 = r2->field_b
    //     0xe9006c: ldur            w0, [x2, #0xb]
    // 0xe90070: r1 = LoadInt32Instr(r0)
    //     0xe90070: sbfx            x1, x0, #1, #0x1f
    // 0xe90074: mov             x0, x1
    // 0xe90078: ldur            x1, [fp, #-0x50]
    // 0xe9007c: cmp             x1, x0
    // 0xe90080: b.hs            #0xe909d8
    // 0xe90084: LoadField: r0 = r2->field_f
    //     0xe90084: ldur            w0, [x2, #0xf]
    // 0xe90088: DecompressPointer r0
    //     0xe90088: add             x0, x0, HEAP, lsl #32
    // 0xe9008c: ldur            x1, [fp, #-0x50]
    // 0xe90090: ArrayLoad: r3 = r0[r1]  ; Unknown_4
    //     0xe90090: add             x16, x0, x1, lsl #2
    //     0xe90094: ldur            w3, [x16, #0xf]
    // 0xe90098: DecompressPointer r3
    //     0xe90098: add             x3, x3, HEAP, lsl #32
    // 0xe9009c: LoadField: d3 = r3->field_7
    //     0xe9009c: ldur            d3, [x3, #7]
    // 0xe900a0: fadd            d4, d0, d3
    // 0xe900a4: stur            d4, [fp, #-0xe0]
    // 0xe900a8: r1 = inline_Allocate_Double()
    //     0xe900a8: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe900ac: add             x1, x1, #0x10
    //     0xe900b0: cmp             x0, x1
    //     0xe900b4: b.ls            #0xe909dc
    //     0xe900b8: str             x1, [THR, #0x50]  ; THR::top
    //     0xe900bc: sub             x1, x1, #0xf
    //     0xe900c0: movz            x0, #0xe15c
    //     0xe900c4: movk            x0, #0x3, lsl #16
    //     0xe900c8: stur            x0, [x1, #-1]
    // 0xe900cc: StoreField: r1->field_7 = d2
    //     0xe900cc: stur            d2, [x1, #7]
    // 0xe900d0: ldur            x3, [fp, #-0x28]
    // 0xe900d4: stur            x1, [fp, #-0x18]
    // 0xe900d8: r0 = 60
    //     0xe900d8: movz            x0, #0x3c
    // 0xe900dc: branchIfSmi(r3, 0xe900e8)
    //     0xe900dc: tbz             w3, #0, #0xe900e8
    // 0xe900e0: r0 = LoadClassIdInstr(r3)
    //     0xe900e0: ldur            x0, [x3, #-1]
    //     0xe900e4: ubfx            x0, x0, #0xc, #0x14
    // 0xe900e8: stp             x1, x3, [SP]
    // 0xe900ec: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xe900ec: sub             lr, x0, #0xfe3
    //     0xe900f0: ldr             lr, [x21, lr, lsl #3]
    //     0xe900f4: blr             lr
    // 0xe900f8: tbnz            w0, #4, #0xe90108
    // 0xe900fc: ldur            x12, [fp, #-0x28]
    // 0xe90100: d0 = 0.000000
    //     0xe90100: eor             v0.16b, v0.16b, v0.16b
    // 0xe90104: b               #0xe901c8
    // 0xe90108: ldur            x1, [fp, #-0x28]
    // 0xe9010c: r0 = 60
    //     0xe9010c: movz            x0, #0x3c
    // 0xe90110: branchIfSmi(r1, 0xe9011c)
    //     0xe90110: tbz             w1, #0, #0xe9011c
    // 0xe90114: r0 = LoadClassIdInstr(r1)
    //     0xe90114: ldur            x0, [x1, #-1]
    //     0xe90118: ubfx            x0, x0, #0xc, #0x14
    // 0xe9011c: ldur            x16, [fp, #-0x18]
    // 0xe90120: stp             x16, x1, [SP]
    // 0xe90124: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xe90124: sub             lr, x0, #0xfd2
    //     0xe90128: ldr             lr, [x21, lr, lsl #3]
    //     0xe9012c: blr             lr
    // 0xe90130: tbnz            w0, #4, #0xe90140
    // 0xe90134: ldur            x12, [fp, #-0x18]
    // 0xe90138: d0 = 0.000000
    //     0xe90138: eor             v0.16b, v0.16b, v0.16b
    // 0xe9013c: b               #0xe901c8
    // 0xe90140: ldur            x2, [fp, #-0x28]
    // 0xe90144: r0 = 60
    //     0xe90144: movz            x0, #0x3c
    // 0xe90148: branchIfSmi(r2, 0xe90154)
    //     0xe90148: tbz             w2, #0, #0xe90154
    // 0xe9014c: r0 = LoadClassIdInstr(r2)
    //     0xe9014c: ldur            x0, [x2, #-1]
    //     0xe90150: ubfx            x0, x0, #0xc, #0x14
    // 0xe90154: cmp             x0, #0x3e
    // 0xe90158: b.ne            #0xe901ac
    // 0xe9015c: d0 = 0.000000
    //     0xe9015c: eor             v0.16b, v0.16b, v0.16b
    // 0xe90160: LoadField: d1 = r2->field_7
    //     0xe90160: ldur            d1, [x2, #7]
    // 0xe90164: fcmp            d1, d0
    // 0xe90168: b.ne            #0xe901a4
    // 0xe9016c: ldur            d2, [fp, #-0xc0]
    // 0xe90170: fadd            d3, d1, d2
    // 0xe90174: r0 = inline_Allocate_Double()
    //     0xe90174: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe90178: add             x0, x0, #0x10
    //     0xe9017c: cmp             x1, x0
    //     0xe90180: b.ls            #0xe90a00
    //     0xe90184: str             x0, [THR, #0x50]  ; THR::top
    //     0xe90188: sub             x0, x0, #0xf
    //     0xe9018c: movz            x1, #0xe15c
    //     0xe90190: movk            x1, #0x3, lsl #16
    //     0xe90194: stur            x1, [x0, #-1]
    // 0xe90198: StoreField: r0->field_7 = d3
    //     0xe90198: stur            d3, [x0, #7]
    // 0xe9019c: mov             x12, x0
    // 0xe901a0: b               #0xe901c8
    // 0xe901a4: ldur            d2, [fp, #-0xc0]
    // 0xe901a8: b               #0xe901b4
    // 0xe901ac: ldur            d2, [fp, #-0xc0]
    // 0xe901b0: d0 = 0.000000
    //     0xe901b0: eor             v0.16b, v0.16b, v0.16b
    // 0xe901b4: fcmp            d2, d2
    // 0xe901b8: b.vc            #0xe901c4
    // 0xe901bc: ldur            x12, [fp, #-0x18]
    // 0xe901c0: b               #0xe901c8
    // 0xe901c4: mov             x12, x2
    // 0xe901c8: ldur            x13, [fp, #-0x40]
    // 0xe901cc: ldur            d2, [fp, #-0xe0]
    // 0xe901d0: ldur            x5, [fp, #-8]
    // 0xe901d4: ldur            x7, [fp, #-0x20]
    // 0xe901d8: ldur            x8, [fp, #-0x30]
    // 0xe901dc: ldur            x2, [fp, #-0x58]
    // 0xe901e0: ldur            x3, [fp, #-0x68]
    // 0xe901e4: ldur            d1, [fp, #-0xd0]
    // 0xe901e8: ldur            x6, [fp, #-0x48]
    // 0xe901ec: ldur            x11, [fp, #-0x38]
    // 0xe901f0: ldur            d0, [fp, #-0xd8]
    // 0xe901f4: ldur            x10, [fp, #-0x78]
    // 0xe901f8: ldur            x4, [fp, #-0x80]
    // 0xe901fc: ldur            x9, [fp, #-0x70]
    // 0xe90200: b               #0xe8ff34
    // 0xe90204: mov             x2, x12
    // 0xe90208: d0 = 0.000000
    //     0xe90208: eor             v0.16b, v0.16b, v0.16b
    // 0xe9020c: LoadField: d1 = r2->field_7
    //     0xe9020c: ldur            d1, [x2, #7]
    // 0xe90210: stur            d1, [fp, #-0xe0]
    // 0xe90214: ldur            x3, [fp, #-0x20]
    // 0xe90218: ldur            d2, [fp, #-0xd0]
    // 0xe9021c: r5 = 0
    //     0xe9021c: movz            x5, #0
    // 0xe90220: d3 = 0.000000
    //     0xe90220: eor             v3.16b, v3.16b, v3.16b
    // 0xe90224: ldur            x4, [fp, #-0x38]
    // 0xe90228: stur            x5, [fp, #-0x50]
    // 0xe9022c: stur            d3, [fp, #-0xc8]
    // 0xe90230: CheckStackOverflow
    //     0xe90230: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe90234: cmp             SP, x16
    //     0xe90238: b.ls            #0xe90a10
    // 0xe9023c: LoadField: r0 = r4->field_b
    //     0xe9023c: ldur            w0, [x4, #0xb]
    // 0xe90240: r1 = LoadInt32Instr(r0)
    //     0xe90240: sbfx            x1, x0, #1, #0x1f
    // 0xe90244: cmp             x14, x1
    // 0xe90248: b.ne            #0xe90800
    // 0xe9024c: cmp             x5, x1
    // 0xe90250: b.ge            #0xe903b0
    // 0xe90254: LoadField: r0 = r4->field_f
    //     0xe90254: ldur            w0, [x4, #0xf]
    // 0xe90258: DecompressPointer r0
    //     0xe90258: add             x0, x0, HEAP, lsl #32
    // 0xe9025c: ArrayLoad: r6 = r0[r5]  ; Unknown_4
    //     0xe9025c: add             x16, x0, x5, lsl #2
    //     0xe90260: ldur            w6, [x16, #0xf]
    // 0xe90264: DecompressPointer r6
    //     0xe90264: add             x6, x6, HEAP, lsl #32
    // 0xe90268: stur            x6, [fp, #-0x18]
    // 0xe9026c: add             x7, x5, #1
    // 0xe90270: stur            x7, [fp, #-0x40]
    // 0xe90274: LoadField: r0 = r3->field_b
    //     0xe90274: ldur            w0, [x3, #0xb]
    // 0xe90278: r1 = LoadInt32Instr(r0)
    //     0xe90278: sbfx            x1, x0, #1, #0x1f
    // 0xe9027c: mov             x0, x1
    // 0xe90280: mov             x1, x5
    // 0xe90284: cmp             x1, x0
    // 0xe90288: b.hs            #0xe90a18
    // 0xe9028c: LoadField: r0 = r3->field_f
    //     0xe9028c: ldur            w0, [x3, #0xf]
    // 0xe90290: DecompressPointer r0
    //     0xe90290: add             x0, x0, HEAP, lsl #32
    // 0xe90294: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xe90294: add             x16, x0, x5, lsl #2
    //     0xe90298: ldur            w1, [x16, #0xf]
    // 0xe9029c: DecompressPointer r1
    //     0xe9029c: add             x1, x1, HEAP, lsl #32
    // 0xe902a0: LoadField: d4 = r1->field_7
    //     0xe902a0: ldur            d4, [x1, #7]
    // 0xe902a4: stur            d4, [fp, #-0xc0]
    // 0xe902a8: r0 = BoxConstraints()
    //     0xe902a8: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe902ac: ldur            d0, [fp, #-0xc0]
    // 0xe902b0: StoreField: r0->field_7 = d0
    //     0xe902b0: stur            d0, [x0, #7]
    // 0xe902b4: StoreField: r0->field_f = d0
    //     0xe902b4: stur            d0, [x0, #0xf]
    // 0xe902b8: ldur            d0, [fp, #-0xe0]
    // 0xe902bc: ArrayStore: r0[0] = d0  ; List_8
    //     0xe902bc: stur            d0, [x0, #0x17]
    // 0xe902c0: StoreField: r0->field_1f = d0
    //     0xe902c0: stur            d0, [x0, #0x1f]
    // 0xe902c4: ldur            x4, [fp, #-0x18]
    // 0xe902c8: r1 = LoadClassIdInstr(r4)
    //     0xe902c8: ldur            x1, [x4, #-1]
    //     0xe902cc: ubfx            x1, x1, #0xc, #0x14
    // 0xe902d0: mov             x3, x0
    // 0xe902d4: mov             x0, x1
    // 0xe902d8: mov             x1, x4
    // 0xe902dc: ldur            x2, [fp, #-0x10]
    // 0xe902e0: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe902e0: sub             lr, x0, #0xf89
    //     0xe902e4: ldr             lr, [x21, lr, lsl #3]
    //     0xe902e8: blr             lr
    // 0xe902ec: ldur            x0, [fp, #-0x18]
    // 0xe902f0: LoadField: r1 = r0->field_7
    //     0xe902f0: ldur            w1, [x0, #7]
    // 0xe902f4: DecompressPointer r1
    //     0xe902f4: add             x1, x1, HEAP, lsl #32
    // 0xe902f8: cmp             w1, NULL
    // 0xe902fc: b.eq            #0xe90a1c
    // 0xe90300: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe90300: ldur            d0, [x1, #0x17]
    // 0xe90304: stur            d0, [fp, #-0xe8]
    // 0xe90308: LoadField: d1 = r1->field_1f
    //     0xe90308: ldur            d1, [x1, #0x1f]
    // 0xe9030c: stur            d1, [fp, #-0xc0]
    // 0xe90310: r0 = PdfRect()
    //     0xe90310: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe90314: ldur            d0, [fp, #-0xc8]
    // 0xe90318: StoreField: r0->field_7 = d0
    //     0xe90318: stur            d0, [x0, #7]
    // 0xe9031c: ldur            d1, [fp, #-0xd0]
    // 0xe90320: StoreField: r0->field_f = d1
    //     0xe90320: stur            d1, [x0, #0xf]
    // 0xe90324: ldur            d2, [fp, #-0xe8]
    // 0xe90328: ArrayStore: r0[0] = d2  ; List_8
    //     0xe90328: stur            d2, [x0, #0x17]
    // 0xe9032c: ldur            d2, [fp, #-0xc0]
    // 0xe90330: StoreField: r0->field_1f = d2
    //     0xe90330: stur            d2, [x0, #0x1f]
    // 0xe90334: ldur            x1, [fp, #-0x18]
    // 0xe90338: StoreField: r1->field_7 = r0
    //     0xe90338: stur            w0, [x1, #7]
    //     0xe9033c: ldurb           w16, [x1, #-1]
    //     0xe90340: ldurb           w17, [x0, #-1]
    //     0xe90344: and             x16, x17, x16, lsr #2
    //     0xe90348: tst             x16, HEAP, lsr #32
    //     0xe9034c: b.eq            #0xe90354
    //     0xe90350: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe90354: ldur            x2, [fp, #-0x20]
    // 0xe90358: LoadField: r0 = r2->field_b
    //     0xe90358: ldur            w0, [x2, #0xb]
    // 0xe9035c: r1 = LoadInt32Instr(r0)
    //     0xe9035c: sbfx            x1, x0, #1, #0x1f
    // 0xe90360: mov             x0, x1
    // 0xe90364: ldur            x1, [fp, #-0x50]
    // 0xe90368: cmp             x1, x0
    // 0xe9036c: b.hs            #0xe90a20
    // 0xe90370: LoadField: r0 = r2->field_f
    //     0xe90370: ldur            w0, [x2, #0xf]
    // 0xe90374: DecompressPointer r0
    //     0xe90374: add             x0, x0, HEAP, lsl #32
    // 0xe90378: ldur            x1, [fp, #-0x50]
    // 0xe9037c: ArrayLoad: r3 = r0[r1]  ; Unknown_4
    //     0xe9037c: add             x16, x0, x1, lsl #2
    //     0xe90380: ldur            w3, [x16, #0xf]
    // 0xe90384: DecompressPointer r3
    //     0xe90384: add             x3, x3, HEAP, lsl #32
    // 0xe90388: LoadField: d2 = r3->field_7
    //     0xe90388: ldur            d2, [x3, #7]
    // 0xe9038c: fadd            d3, d0, d2
    // 0xe90390: ldur            x5, [fp, #-0x40]
    // 0xe90394: mov             x3, x2
    // 0xe90398: mov             v2.16b, v1.16b
    // 0xe9039c: ldur            x2, [fp, #-0x28]
    // 0xe903a0: ldur            d1, [fp, #-0xe0]
    // 0xe903a4: ldur            x14, [fp, #-0x98]
    // 0xe903a8: d0 = 0.000000
    //     0xe903a8: eor             v0.16b, v0.16b, v0.16b
    // 0xe903ac: b               #0xe90224
    // 0xe903b0: mov             x0, x2
    // 0xe903b4: mov             x2, x3
    // 0xe903b8: mov             v1.16b, v2.16b
    // 0xe903bc: ldur            d0, [fp, #-0xd8]
    // 0xe903c0: LoadField: d2 = r0->field_7
    //     0xe903c0: ldur            d2, [x0, #7]
    // 0xe903c4: fadd            d3, d1, d2
    // 0xe903c8: stur            d3, [fp, #-0xc0]
    // 0xe903cc: fcmp            d3, d0
    // 0xe903d0: b.gt            #0xe90474
    // 0xe903d4: ldur            x3, [fp, #-0x30]
    // 0xe903d8: LoadField: r1 = r3->field_b
    //     0xe903d8: ldur            w1, [x3, #0xb]
    // 0xe903dc: LoadField: r4 = r3->field_f
    //     0xe903dc: ldur            w4, [x3, #0xf]
    // 0xe903e0: DecompressPointer r4
    //     0xe903e0: add             x4, x4, HEAP, lsl #32
    // 0xe903e4: LoadField: r5 = r4->field_b
    //     0xe903e4: ldur            w5, [x4, #0xb]
    // 0xe903e8: r4 = LoadInt32Instr(r1)
    //     0xe903e8: sbfx            x4, x1, #1, #0x1f
    // 0xe903ec: stur            x4, [fp, #-0x40]
    // 0xe903f0: r1 = LoadInt32Instr(r5)
    //     0xe903f0: sbfx            x1, x5, #1, #0x1f
    // 0xe903f4: cmp             x4, x1
    // 0xe903f8: b.ne            #0xe90404
    // 0xe903fc: mov             x1, x3
    // 0xe90400: r0 = _growToNextCapacity()
    //     0xe90400: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe90404: ldur            x2, [fp, #-0x30]
    // 0xe90408: ldur            x3, [fp, #-0x40]
    // 0xe9040c: add             x0, x3, #1
    // 0xe90410: lsl             x1, x0, #1
    // 0xe90414: StoreField: r2->field_b = r1
    //     0xe90414: stur            w1, [x2, #0xb]
    // 0xe90418: LoadField: r1 = r2->field_f
    //     0xe90418: ldur            w1, [x2, #0xf]
    // 0xe9041c: DecompressPointer r1
    //     0xe9041c: add             x1, x1, HEAP, lsl #32
    // 0xe90420: ldur            x0, [fp, #-0x28]
    // 0xe90424: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe90424: add             x25, x1, x3, lsl #2
    //     0xe90428: add             x25, x25, #0xf
    //     0xe9042c: str             w0, [x25]
    //     0xe90430: tbz             w0, #0, #0xe9044c
    //     0xe90434: ldurb           w16, [x1, #-1]
    //     0xe90438: ldurb           w17, [x0, #-1]
    //     0xe9043c: and             x16, x17, x16, lsr #2
    //     0xe90440: tst             x16, HEAP, lsr #32
    //     0xe90444: b.eq            #0xe9044c
    //     0xe90448: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe9044c: ldur            d1, [fp, #-0xc0]
    // 0xe90450: ldur            x0, [fp, #-0x78]
    // 0xe90454: ldur            x5, [fp, #-8]
    // 0xe90458: mov             x8, x2
    // 0xe9045c: ldur            x2, [fp, #-0x58]
    // 0xe90460: ldur            x3, [fp, #-0x68]
    // 0xe90464: ldur            x6, [fp, #-0x48]
    // 0xe90468: ldur            d0, [fp, #-0xd8]
    // 0xe9046c: ldur            x4, [fp, #-0x80]
    // 0xe90470: b               #0xe8fea8
    // 0xe90474: ldur            x2, [fp, #-0x30]
    // 0xe90478: ldur            x0, [fp, #-0x78]
    // 0xe9047c: sub             x1, x0, #1
    // 0xe90480: mov             x4, x1
    // 0xe90484: b               #0xe90490
    // 0xe90488: mov             x2, x8
    // 0xe9048c: mov             x4, x0
    // 0xe90490: ldur            x3, [fp, #-8]
    // 0xe90494: ldur            x0, [fp, #-0x58]
    // 0xe90498: ldur            x1, [fp, #-0x48]
    // 0xe9049c: stur            x4, [fp, #-0xb8]
    // 0xe904a0: StoreField: r1->field_f = r4
    //     0xe904a0: stur            x4, [x1, #0xf]
    // 0xe904a4: LoadField: r5 = r0->field_b
    //     0xe904a4: ldur            w5, [x0, #0xb]
    // 0xe904a8: r6 = LoadInt32Instr(r5)
    //     0xe904a8: sbfx            x6, x5, #1, #0x1f
    // 0xe904ac: stur            x6, [fp, #-0xb0]
    // 0xe904b0: LoadField: r5 = r0->field_f
    //     0xe904b0: ldur            w5, [x0, #0xf]
    // 0xe904b4: DecompressPointer r5
    //     0xe904b4: add             x5, x5, HEAP, lsl #32
    // 0xe904b8: stur            x5, [fp, #-0x28]
    // 0xe904bc: LoadField: r7 = r1->field_7
    //     0xe904bc: ldur            x7, [x1, #7]
    // 0xe904c0: stur            x7, [fp, #-0xa8]
    // 0xe904c4: LoadField: r0 = r3->field_13
    //     0xe904c4: ldur            w0, [x3, #0x13]
    // 0xe904c8: DecompressPointer r0
    //     0xe904c8: add             x0, x0, HEAP, lsl #32
    // 0xe904cc: LoadField: r8 = r0->field_7
    //     0xe904cc: ldur            x8, [x0, #7]
    // 0xe904d0: stur            x8, [fp, #-0xa0]
    // 0xe904d4: LoadField: r0 = r2->field_b
    //     0xe904d4: ldur            w0, [x2, #0xb]
    // 0xe904d8: r9 = LoadInt32Instr(r0)
    //     0xe904d8: sbfx            x9, x0, #1, #0x1f
    // 0xe904dc: stur            x9, [fp, #-0x98]
    // 0xe904e0: LoadField: r10 = r2->field_f
    //     0xe904e0: ldur            w10, [x2, #0xf]
    // 0xe904e4: DecompressPointer r10
    //     0xe904e4: add             x10, x10, HEAP, lsl #32
    // 0xe904e8: stur            x10, [fp, #-0x20]
    // 0xe904ec: r2 = LoadInt32Instr(r0)
    //     0xe904ec: sbfx            x2, x0, #1, #0x1f
    // 0xe904f0: stur            x2, [fp, #-0x80]
    // 0xe904f4: r0 = 0
    //     0xe904f4: movz            x0, #0
    // 0xe904f8: r11 = 0
    //     0xe904f8: movz            x11, #0
    // 0xe904fc: d0 = 2.000000
    //     0xe904fc: fmov            d0, #2.00000000
    // 0xe90500: stur            x11, [fp, #-0x78]
    // 0xe90504: CheckStackOverflow
    //     0xe90504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe90508: cmp             SP, x16
    //     0xe9050c: b.ls            #0xe90a24
    // 0xe90510: cmp             x0, x6
    // 0xe90514: b.ge            #0xe90784
    // 0xe90518: ArrayLoad: r1 = r5[r0]  ; Unknown_4
    //     0xe90518: add             x16, x5, x0, lsl #2
    //     0xe9051c: ldur            w1, [x16, #0xf]
    // 0xe90520: DecompressPointer r1
    //     0xe90520: add             x1, x1, HEAP, lsl #32
    // 0xe90524: add             x12, x0, #1
    // 0xe90528: stur            x12, [fp, #-0x70]
    // 0xe9052c: cmp             x0, x7
    // 0xe90530: b.ge            #0xe9054c
    // 0xe90534: LoadField: r0 = r1->field_b
    //     0xe90534: ldur            w0, [x1, #0xb]
    // 0xe90538: DecompressPointer r0
    //     0xe90538: add             x0, x0, HEAP, lsl #32
    // 0xe9053c: tbz             w0, #4, #0xe9054c
    // 0xe90540: mov             x1, x4
    // 0xe90544: mov             x0, x12
    // 0xe90548: b               #0xe90758
    // 0xe9054c: LoadField: r0 = r1->field_7
    //     0xe9054c: ldur            w0, [x1, #7]
    // 0xe90550: DecompressPointer r0
    //     0xe90550: add             x0, x0, HEAP, lsl #32
    // 0xe90554: LoadField: r1 = r0->field_b
    //     0xe90554: ldur            w1, [x0, #0xb]
    // 0xe90558: r13 = LoadInt32Instr(r1)
    //     0xe90558: sbfx            x13, x1, #1, #0x1f
    // 0xe9055c: stur            x13, [fp, #-0x50]
    // 0xe90560: LoadField: r14 = r0->field_f
    //     0xe90560: ldur            w14, [x0, #0xf]
    // 0xe90564: DecompressPointer r14
    //     0xe90564: add             x14, x14, HEAP, lsl #32
    // 0xe90568: stur            x14, [fp, #-0x18]
    // 0xe9056c: r0 = 0
    //     0xe9056c: movz            x0, #0
    // 0xe90570: CheckStackOverflow
    //     0xe90570: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe90574: cmp             SP, x16
    //     0xe90578: b.ls            #0xe90a2c
    // 0xe9057c: cmp             x0, x13
    // 0xe90580: b.ge            #0xe9073c
    // 0xe90584: ArrayLoad: r19 = r14[r0]  ; Unknown_4
    //     0xe90584: add             x16, x14, x0, lsl #2
    //     0xe90588: ldur            w19, [x16, #0xf]
    // 0xe9058c: DecompressPointer r19
    //     0xe9058c: add             x19, x19, HEAP, lsl #32
    // 0xe90590: stur            x19, [fp, #-0x10]
    // 0xe90594: add             x20, x0, #1
    // 0xe90598: stur            x20, [fp, #-0x40]
    // 0xe9059c: cmp             x8, #1
    // 0xe905a0: b.gt            #0xe90670
    // 0xe905a4: cmp             x8, #0
    // 0xe905a8: b.gt            #0xe90608
    // 0xe905ac: LoadField: r23 = r19->field_7
    //     0xe905ac: ldur            w23, [x19, #7]
    // 0xe905b0: DecompressPointer r23
    //     0xe905b0: add             x23, x23, HEAP, lsl #32
    // 0xe905b4: cmp             w23, NULL
    // 0xe905b8: b.eq            #0xe90a34
    // 0xe905bc: LoadField: d2 = r23->field_f
    //     0xe905bc: ldur            d2, [x23, #0xf]
    // 0xe905c0: fsub            d3, d1, d2
    // 0xe905c4: tbnz            x11, #0x3f, #0xe905f4
    // 0xe905c8: cmp             x11, x2
    // 0xe905cc: b.ge            #0xe905f4
    // 0xe905d0: mov             x0, x2
    // 0xe905d4: mov             x1, x11
    // 0xe905d8: cmp             x1, x0
    // 0xe905dc: b.hs            #0xe90a38
    // 0xe905e0: ArrayLoad: r0 = r10[r11]  ; Unknown_4
    //     0xe905e0: add             x16, x10, x11, lsl #2
    //     0xe905e4: ldur            w0, [x16, #0xf]
    // 0xe905e8: DecompressPointer r0
    //     0xe905e8: add             x0, x0, HEAP, lsl #32
    // 0xe905ec: LoadField: d2 = r0->field_7
    //     0xe905ec: ldur            d2, [x0, #7]
    // 0xe905f0: b               #0xe905f8
    // 0xe905f4: d2 = 0.000000
    //     0xe905f4: eor             v2.16b, v2.16b, v2.16b
    // 0xe905f8: fsub            d4, d3, d2
    // 0xe905fc: mov             v2.16b, v4.16b
    // 0xe90600: mov             x0, x23
    // 0xe90604: b               #0xe90698
    // 0xe90608: LoadField: r23 = r19->field_7
    //     0xe90608: ldur            w23, [x19, #7]
    // 0xe9060c: DecompressPointer r23
    //     0xe9060c: add             x23, x23, HEAP, lsl #32
    // 0xe90610: cmp             w23, NULL
    // 0xe90614: b.eq            #0xe90a3c
    // 0xe90618: LoadField: d2 = r23->field_f
    //     0xe90618: ldur            d2, [x23, #0xf]
    // 0xe9061c: fsub            d3, d1, d2
    // 0xe90620: tbnz            x11, #0x3f, #0xe90650
    // 0xe90624: cmp             x11, x9
    // 0xe90628: b.ge            #0xe90650
    // 0xe9062c: mov             x0, x9
    // 0xe90630: mov             x1, x11
    // 0xe90634: cmp             x1, x0
    // 0xe90638: b.hs            #0xe90a40
    // 0xe9063c: ArrayLoad: r0 = r10[r11]  ; Unknown_4
    //     0xe9063c: add             x16, x10, x11, lsl #2
    //     0xe90640: ldur            w0, [x16, #0xf]
    // 0xe90644: DecompressPointer r0
    //     0xe90644: add             x0, x0, HEAP, lsl #32
    // 0xe90648: LoadField: d2 = r0->field_7
    //     0xe90648: ldur            d2, [x0, #7]
    // 0xe9064c: b               #0xe90654
    // 0xe90650: d2 = 0.000000
    //     0xe90650: eor             v2.16b, v2.16b, v2.16b
    // 0xe90654: LoadField: d4 = r23->field_1f
    //     0xe90654: ldur            d4, [x23, #0x1f]
    // 0xe90658: fadd            d5, d2, d4
    // 0xe9065c: fdiv            d2, d5, d0
    // 0xe90660: fsub            d4, d3, d2
    // 0xe90664: mov             v2.16b, v4.16b
    // 0xe90668: mov             x0, x23
    // 0xe9066c: b               #0xe90698
    // 0xe90670: LoadField: r1 = r19->field_7
    //     0xe90670: ldur            w1, [x19, #7]
    // 0xe90674: DecompressPointer r1
    //     0xe90674: add             x1, x1, HEAP, lsl #32
    // 0xe90678: cmp             w1, NULL
    // 0xe9067c: b.eq            #0xe90a44
    // 0xe90680: LoadField: d2 = r1->field_f
    //     0xe90680: ldur            d2, [x1, #0xf]
    // 0xe90684: fsub            d3, d1, d2
    // 0xe90688: LoadField: d2 = r1->field_1f
    //     0xe90688: ldur            d2, [x1, #0x1f]
    // 0xe9068c: fsub            d4, d3, d2
    // 0xe90690: mov             v2.16b, v4.16b
    // 0xe90694: mov             x0, x1
    // 0xe90698: stur            d2, [fp, #-0xe0]
    // 0xe9069c: LoadField: d3 = r0->field_7
    //     0xe9069c: ldur            d3, [x0, #7]
    // 0xe906a0: stur            d3, [fp, #-0xd8]
    // 0xe906a4: ArrayLoad: d4 = r0[0]  ; List_8
    //     0xe906a4: ldur            d4, [x0, #0x17]
    // 0xe906a8: stur            d4, [fp, #-0xc8]
    // 0xe906ac: LoadField: d5 = r0->field_1f
    //     0xe906ac: ldur            d5, [x0, #0x1f]
    // 0xe906b0: stur            d5, [fp, #-0xc0]
    // 0xe906b4: r0 = PdfRect()
    //     0xe906b4: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe906b8: ldur            d0, [fp, #-0xd8]
    // 0xe906bc: StoreField: r0->field_7 = d0
    //     0xe906bc: stur            d0, [x0, #7]
    // 0xe906c0: ldur            d0, [fp, #-0xe0]
    // 0xe906c4: StoreField: r0->field_f = d0
    //     0xe906c4: stur            d0, [x0, #0xf]
    // 0xe906c8: ldur            d0, [fp, #-0xc8]
    // 0xe906cc: ArrayStore: r0[0] = d0  ; List_8
    //     0xe906cc: stur            d0, [x0, #0x17]
    // 0xe906d0: ldur            d0, [fp, #-0xc0]
    // 0xe906d4: StoreField: r0->field_1f = d0
    //     0xe906d4: stur            d0, [x0, #0x1f]
    // 0xe906d8: ldur            x1, [fp, #-0x10]
    // 0xe906dc: StoreField: r1->field_7 = r0
    //     0xe906dc: stur            w0, [x1, #7]
    //     0xe906e0: ldurb           w16, [x1, #-1]
    //     0xe906e4: ldurb           w17, [x0, #-1]
    //     0xe906e8: and             x16, x17, x16, lsr #2
    //     0xe906ec: tst             x16, HEAP, lsr #32
    //     0xe906f0: b.eq            #0xe906f8
    //     0xe906f4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe906f8: ldur            x0, [fp, #-0x40]
    // 0xe906fc: ldur            x3, [fp, #-8]
    // 0xe90700: ldur            d1, [fp, #-0xd0]
    // 0xe90704: ldur            x4, [fp, #-0xb8]
    // 0xe90708: ldur            x11, [fp, #-0x78]
    // 0xe9070c: ldur            x7, [fp, #-0xa8]
    // 0xe90710: ldur            x8, [fp, #-0xa0]
    // 0xe90714: ldur            x10, [fp, #-0x20]
    // 0xe90718: ldur            x12, [fp, #-0x70]
    // 0xe9071c: ldur            x5, [fp, #-0x28]
    // 0xe90720: ldur            x14, [fp, #-0x18]
    // 0xe90724: ldur            x6, [fp, #-0xb0]
    // 0xe90728: ldur            x13, [fp, #-0x50]
    // 0xe9072c: ldur            x2, [fp, #-0x80]
    // 0xe90730: ldur            x9, [fp, #-0x98]
    // 0xe90734: d0 = 2.000000
    //     0xe90734: fmov            d0, #2.00000000
    // 0xe90738: b               #0xe90570
    // 0xe9073c: mov             x1, x4
    // 0xe90740: mov             x0, x12
    // 0xe90744: cmp             x0, x1
    // 0xe90748: b.ge            #0xe90784
    // 0xe9074c: ldur            x2, [fp, #-0x78]
    // 0xe90750: add             x3, x2, #1
    // 0xe90754: mov             x11, x3
    // 0xe90758: ldur            x3, [fp, #-8]
    // 0xe9075c: ldur            d1, [fp, #-0xd0]
    // 0xe90760: mov             x4, x1
    // 0xe90764: ldur            x7, [fp, #-0xa8]
    // 0xe90768: ldur            x8, [fp, #-0xa0]
    // 0xe9076c: ldur            x10, [fp, #-0x20]
    // 0xe90770: ldur            x5, [fp, #-0x28]
    // 0xe90774: ldur            x6, [fp, #-0xb0]
    // 0xe90778: ldur            x2, [fp, #-0x80]
    // 0xe9077c: ldur            x9, [fp, #-0x98]
    // 0xe90780: b               #0xe904fc
    // 0xe90784: ldur            x0, [fp, #-8]
    // 0xe90788: ldur            x1, [fp, #-0x68]
    // 0xe9078c: ldur            d0, [fp, #-0xd0]
    // 0xe90790: r0 = PdfRect()
    //     0xe90790: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe90794: StoreField: r0->field_7 = rZR
    //     0xe90794: stur            xzr, [x0, #7]
    // 0xe90798: StoreField: r0->field_f = rZR
    //     0xe90798: stur            xzr, [x0, #0xf]
    // 0xe9079c: ldur            x1, [fp, #-0x68]
    // 0xe907a0: LoadField: d0 = r1->field_7
    //     0xe907a0: ldur            d0, [x1, #7]
    // 0xe907a4: ArrayStore: r0[0] = d0  ; List_8
    //     0xe907a4: stur            d0, [x0, #0x17]
    // 0xe907a8: ldur            d0, [fp, #-0xd0]
    // 0xe907ac: StoreField: r0->field_1f = d0
    //     0xe907ac: stur            d0, [x0, #0x1f]
    // 0xe907b0: ldur            x1, [fp, #-8]
    // 0xe907b4: StoreField: r1->field_7 = r0
    //     0xe907b4: stur            w0, [x1, #7]
    //     0xe907b8: ldurb           w16, [x1, #-1]
    //     0xe907bc: ldurb           w17, [x0, #-1]
    //     0xe907c0: and             x16, x17, x16, lsr #2
    //     0xe907c4: tst             x16, HEAP, lsr #32
    //     0xe907c8: b.eq            #0xe907d0
    //     0xe907cc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe907d0: r0 = Null
    //     0xe907d0: mov             x0, NULL
    // 0xe907d4: LeaveFrame
    //     0xe907d4: mov             SP, fp
    //     0xe907d8: ldp             fp, lr, [SP], #0x10
    // 0xe907dc: ret
    //     0xe907dc: ret             
    // 0xe907e0: ldur            x0, [fp, #-0x60]
    // 0xe907e4: r0 = ConcurrentModificationError()
    //     0xe907e4: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe907e8: mov             x1, x0
    // 0xe907ec: ldur            x0, [fp, #-0x60]
    // 0xe907f0: StoreField: r1->field_b = r0
    //     0xe907f0: stur            w0, [x1, #0xb]
    // 0xe907f4: mov             x0, x1
    // 0xe907f8: r0 = Throw()
    //     0xe907f8: bl              #0xec04b8  ; ThrowStub
    // 0xe907fc: brk             #0
    // 0xe90800: mov             x0, x4
    // 0xe90804: r0 = ConcurrentModificationError()
    //     0xe90804: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe90808: mov             x1, x0
    // 0xe9080c: ldur            x0, [fp, #-0x38]
    // 0xe90810: StoreField: r1->field_b = r0
    //     0xe90810: stur            w0, [x1, #0xb]
    // 0xe90814: mov             x0, x1
    // 0xe90818: r0 = Throw()
    //     0xe90818: bl              #0xec04b8  ; ThrowStub
    // 0xe9081c: brk             #0
    // 0xe90820: mov             x0, x11
    // 0xe90824: r0 = ConcurrentModificationError()
    //     0xe90824: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe90828: mov             x1, x0
    // 0xe9082c: ldur            x0, [fp, #-0x38]
    // 0xe90830: StoreField: r1->field_b = r0
    //     0xe90830: stur            w0, [x1, #0xb]
    // 0xe90834: mov             x0, x1
    // 0xe90838: r0 = Throw()
    //     0xe90838: bl              #0xec04b8  ; ThrowStub
    // 0xe9083c: brk             #0
    // 0xe90840: mov             x0, x2
    // 0xe90844: r0 = ConcurrentModificationError()
    //     0xe90844: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe90848: mov             x1, x0
    // 0xe9084c: ldur            x0, [fp, #-0x58]
    // 0xe90850: StoreField: r1->field_b = r0
    //     0xe90850: stur            w0, [x1, #0xb]
    // 0xe90854: mov             x0, x1
    // 0xe90858: r0 = Throw()
    //     0xe90858: bl              #0xec04b8  ; ThrowStub
    // 0xe9085c: brk             #0
    // 0xe90860: mov             x0, x4
    // 0xe90864: r0 = ConcurrentModificationError()
    //     0xe90864: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe90868: mov             x1, x0
    // 0xe9086c: ldur            x0, [fp, #-0x58]
    // 0xe90870: StoreField: r1->field_b = r0
    //     0xe90870: stur            w0, [x1, #0xb]
    // 0xe90874: mov             x0, x1
    // 0xe90878: r0 = Throw()
    //     0xe90878: bl              #0xec04b8  ; ThrowStub
    // 0xe9087c: brk             #0
    // 0xe90880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe90880: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe90884: b               #0xe8f490
    // 0xe90888: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe90888: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe9088c: b               #0xe8f514
    // 0xe90890: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe90890: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe90894: b               #0xe8f684
    // 0xe90898: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe90898: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe9089c: SaveReg d0
    //     0xe9089c: str             q0, [SP, #-0x10]!
    // 0xe908a0: stp             x3, x4, [SP, #-0x10]!
    // 0xe908a4: stp             x1, x2, [SP, #-0x10]!
    // 0xe908a8: r0 = AllocateDouble()
    //     0xe908a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe908ac: ldp             x1, x2, [SP], #0x10
    // 0xe908b0: ldp             x3, x4, [SP], #0x10
    // 0xe908b4: RestoreReg d0
    //     0xe908b4: ldr             q0, [SP], #0x10
    // 0xe908b8: b               #0xe8f86c
    // 0xe908bc: SaveReg d1
    //     0xe908bc: str             q1, [SP, #-0x10]!
    // 0xe908c0: stp             x2, x3, [SP, #-0x10]!
    // 0xe908c4: SaveReg r1
    //     0xe908c4: str             x1, [SP, #-8]!
    // 0xe908c8: r0 = AllocateDouble()
    //     0xe908c8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe908cc: RestoreReg r1
    //     0xe908cc: ldr             x1, [SP], #8
    // 0xe908d0: ldp             x2, x3, [SP], #0x10
    // 0xe908d4: RestoreReg d1
    //     0xe908d4: ldr             q1, [SP], #0x10
    // 0xe908d8: b               #0xe8f908
    // 0xe908dc: r0 = RangeErrorSharedWithFPURegs()
    //     0xe908dc: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe908e0: stp             q1, q2, [SP, #-0x20]!
    // 0xe908e4: SaveReg d0
    //     0xe908e4: str             q0, [SP, #-0x10]!
    // 0xe908e8: stp             x4, x5, [SP, #-0x10]!
    // 0xe908ec: stp             x2, x3, [SP, #-0x10]!
    // 0xe908f0: SaveReg r1
    //     0xe908f0: str             x1, [SP, #-8]!
    // 0xe908f4: r0 = AllocateDouble()
    //     0xe908f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe908f8: RestoreReg r1
    //     0xe908f8: ldr             x1, [SP], #8
    // 0xe908fc: ldp             x2, x3, [SP], #0x10
    // 0xe90900: ldp             x4, x5, [SP], #0x10
    // 0xe90904: RestoreReg d0
    //     0xe90904: ldr             q0, [SP], #0x10
    // 0xe90908: ldp             q1, q2, [SP], #0x20
    // 0xe9090c: b               #0xe8f9f0
    // 0xe90910: r0 = RangeErrorSharedWithFPURegs()
    //     0xe90910: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe90914: stp             q0, q2, [SP, #-0x20]!
    // 0xe90918: stp             x5, x6, [SP, #-0x10]!
    // 0xe9091c: stp             x3, x4, [SP, #-0x10]!
    // 0xe90920: r0 = AllocateDouble()
    //     0xe90920: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe90924: mov             x7, x0
    // 0xe90928: ldp             x3, x4, [SP], #0x10
    // 0xe9092c: ldp             x5, x6, [SP], #0x10
    // 0xe90930: ldp             q0, q2, [SP], #0x20
    // 0xe90934: b               #0xe8fad8
    // 0xe90938: r0 = StackOverflowSharedWithFPURegs()
    //     0xe90938: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe9093c: b               #0xe8fc94
    // 0xe90940: r0 = RangeErrorSharedWithFPURegs()
    //     0xe90940: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe90944: stp             q4, q5, [SP, #-0x20]!
    // 0xe90948: stp             q2, q3, [SP, #-0x20]!
    // 0xe9094c: stp             q0, q1, [SP, #-0x20]!
    // 0xe90950: stp             x8, x9, [SP, #-0x10]!
    // 0xe90954: stp             x6, x7, [SP, #-0x10]!
    // 0xe90958: stp             x4, x5, [SP, #-0x10]!
    // 0xe9095c: stp             x2, x3, [SP, #-0x10]!
    // 0xe90960: r0 = AllocateDouble()
    //     0xe90960: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe90964: ldp             x2, x3, [SP], #0x10
    // 0xe90968: ldp             x4, x5, [SP], #0x10
    // 0xe9096c: ldp             x6, x7, [SP], #0x10
    // 0xe90970: ldp             x8, x9, [SP], #0x10
    // 0xe90974: ldp             q0, q1, [SP], #0x20
    // 0xe90978: ldp             q2, q3, [SP], #0x20
    // 0xe9097c: ldp             q4, q5, [SP], #0x20
    // 0xe90980: b               #0xe8fd14
    // 0xe90984: r0 = StackOverflowSharedWithFPURegs()
    //     0xe90984: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe90988: b               #0xe8fd94
    // 0xe9098c: r0 = RangeErrorSharedWithFPURegs()
    //     0xe9098c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe90990: stp             q2, q3, [SP, #-0x20]!
    // 0xe90994: SaveReg d0
    //     0xe90994: str             q0, [SP, #-0x10]!
    // 0xe90998: stp             x7, x8, [SP, #-0x10]!
    // 0xe9099c: stp             x4, x5, [SP, #-0x10]!
    // 0xe909a0: stp             x2, x3, [SP, #-0x10]!
    // 0xe909a4: r0 = AllocateDouble()
    //     0xe909a4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe909a8: ldp             x2, x3, [SP], #0x10
    // 0xe909ac: ldp             x4, x5, [SP], #0x10
    // 0xe909b0: ldp             x7, x8, [SP], #0x10
    // 0xe909b4: RestoreReg d0
    //     0xe909b4: ldr             q0, [SP], #0x10
    // 0xe909b8: ldp             q2, q3, [SP], #0x20
    // 0xe909bc: b               #0xe8fdec
    // 0xe909c0: r0 = StackOverflowSharedWithFPURegs()
    //     0xe909c0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe909c4: b               #0xe8febc
    // 0xe909c8: r0 = StackOverflowSharedWithFPURegs()
    //     0xe909c8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe909cc: b               #0xe8ff4c
    // 0xe909d0: r0 = RangeErrorSharedWithFPURegs()
    //     0xe909d0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe909d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe909d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe909d8: r0 = RangeErrorSharedWithFPURegs()
    //     0xe909d8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe909dc: stp             q2, q4, [SP, #-0x20]!
    // 0xe909e0: SaveReg d1
    //     0xe909e0: str             q1, [SP, #-0x10]!
    // 0xe909e4: SaveReg r2
    //     0xe909e4: str             x2, [SP, #-8]!
    // 0xe909e8: r0 = AllocateDouble()
    //     0xe909e8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe909ec: mov             x1, x0
    // 0xe909f0: RestoreReg r2
    //     0xe909f0: ldr             x2, [SP], #8
    // 0xe909f4: RestoreReg d1
    //     0xe909f4: ldr             q1, [SP], #0x10
    // 0xe909f8: ldp             q2, q4, [SP], #0x20
    // 0xe909fc: b               #0xe900cc
    // 0xe90a00: stp             q0, q3, [SP, #-0x20]!
    // 0xe90a04: r0 = AllocateDouble()
    //     0xe90a04: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe90a08: ldp             q0, q3, [SP], #0x20
    // 0xe90a0c: b               #0xe90198
    // 0xe90a10: r0 = StackOverflowSharedWithFPURegs()
    //     0xe90a10: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe90a14: b               #0xe9023c
    // 0xe90a18: r0 = RangeErrorSharedWithFPURegs()
    //     0xe90a18: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe90a1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe90a1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe90a20: r0 = RangeErrorSharedWithFPURegs()
    //     0xe90a20: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe90a24: r0 = StackOverflowSharedWithFPURegs()
    //     0xe90a24: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe90a28: b               #0xe90510
    // 0xe90a2c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe90a2c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe90a30: b               #0xe9057c
    // 0xe90a34: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe90a34: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe90a38: r0 = RangeErrorSharedWithFPURegs()
    //     0xe90a38: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe90a3c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe90a3c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe90a40: r0 = RangeErrorSharedWithFPURegs()
    //     0xe90a40: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe90a44: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe90a44: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  [closure] double <anonymous closure>(dynamic, double?, double?) {
    // ** addr: 0xe90a48, size: 0x78
    // 0xe90a48: EnterFrame
    //     0xe90a48: stp             fp, lr, [SP, #-0x10]!
    //     0xe90a4c: mov             fp, SP
    // 0xe90a50: ldr             x1, [fp, #0x18]
    // 0xe90a54: cmp             w1, NULL
    // 0xe90a58: b.eq            #0xe90aa8
    // 0xe90a5c: ldr             x2, [fp, #0x10]
    // 0xe90a60: cmp             w2, NULL
    // 0xe90a64: b.eq            #0xe90aac
    // 0xe90a68: LoadField: d0 = r1->field_7
    //     0xe90a68: ldur            d0, [x1, #7]
    // 0xe90a6c: LoadField: d1 = r2->field_7
    //     0xe90a6c: ldur            d1, [x2, #7]
    // 0xe90a70: fadd            d2, d0, d1
    // 0xe90a74: r0 = inline_Allocate_Double()
    //     0xe90a74: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe90a78: add             x0, x0, #0x10
    //     0xe90a7c: cmp             x1, x0
    //     0xe90a80: b.ls            #0xe90ab0
    //     0xe90a84: str             x0, [THR, #0x50]  ; THR::top
    //     0xe90a88: sub             x0, x0, #0xf
    //     0xe90a8c: movz            x1, #0xe15c
    //     0xe90a90: movk            x1, #0x3, lsl #16
    //     0xe90a94: stur            x1, [x0, #-1]
    // 0xe90a98: StoreField: r0->field_7 = d2
    //     0xe90a98: stur            d2, [x0, #7]
    // 0xe90a9c: LeaveFrame
    //     0xe90a9c: mov             SP, fp
    //     0xe90aa0: ldp             fp, lr, [SP], #0x10
    // 0xe90aa4: ret
    //     0xe90aa4: ret             
    // 0xe90aa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe90aa8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe90aac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe90aac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe90ab0: SaveReg d2
    //     0xe90ab0: str             q2, [SP, #-0x10]!
    // 0xe90ab4: r0 = AllocateDouble()
    //     0xe90ab4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe90ab8: RestoreReg d2
    //     0xe90ab8: ldr             q2, [SP], #0x10
    // 0xe90abc: b               #0xe90a98
  }
}

// class id: 822, size: 0x18, field offset: 0x8
class TableContext extends WidgetContext {

  _ toString(/* No info */) {
    // ** addr: 0xc366c0, size: 0xa4
    // 0xc366c0: EnterFrame
    //     0xc366c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc366c4: mov             fp, SP
    // 0xc366c8: AllocStack(0x8)
    //     0xc366c8: sub             SP, SP, #8
    // 0xc366cc: CheckStackOverflow
    //     0xc366cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc366d0: cmp             SP, x16
    //     0xc366d4: b.ls            #0xc3675c
    // 0xc366d8: r1 = Null
    //     0xc366d8: mov             x1, NULL
    // 0xc366dc: r2 = 10
    //     0xc366dc: movz            x2, #0xa
    // 0xc366e0: r0 = AllocateArray()
    //     0xc366e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc366e4: mov             x2, x0
    // 0xc366e8: r16 = TableContext
    //     0xc366e8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33758] Type: TableContext
    //     0xc366ec: ldr             x16, [x16, #0x758]
    // 0xc366f0: StoreField: r2->field_f = r16
    //     0xc366f0: stur            w16, [x2, #0xf]
    // 0xc366f4: r16 = " firstLine: "
    //     0xc366f4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " firstLine: "
    //     0xc366f8: ldr             x16, [x16, #0x760]
    // 0xc366fc: StoreField: r2->field_13 = r16
    //     0xc366fc: stur            w16, [x2, #0x13]
    // 0xc36700: ldr             x3, [fp, #0x10]
    // 0xc36704: LoadField: r4 = r3->field_7
    //     0xc36704: ldur            x4, [x3, #7]
    // 0xc36708: r0 = BoxInt64Instr(r4)
    //     0xc36708: sbfiz           x0, x4, #1, #0x1f
    //     0xc3670c: cmp             x4, x0, asr #1
    //     0xc36710: b.eq            #0xc3671c
    //     0xc36714: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc36718: stur            x4, [x0, #7]
    // 0xc3671c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc3671c: stur            w0, [x2, #0x17]
    // 0xc36720: r16 = " lastLine: "
    //     0xc36720: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] " lastLine: "
    //     0xc36724: ldr             x16, [x16, #0x768]
    // 0xc36728: StoreField: r2->field_1b = r16
    //     0xc36728: stur            w16, [x2, #0x1b]
    // 0xc3672c: LoadField: r4 = r3->field_f
    //     0xc3672c: ldur            x4, [x3, #0xf]
    // 0xc36730: r0 = BoxInt64Instr(r4)
    //     0xc36730: sbfiz           x0, x4, #1, #0x1f
    //     0xc36734: cmp             x4, x0, asr #1
    //     0xc36738: b.eq            #0xc36744
    //     0xc3673c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc36740: stur            x4, [x0, #7]
    // 0xc36744: StoreField: r2->field_1f = r0
    //     0xc36744: stur            w0, [x2, #0x1f]
    // 0xc36748: str             x2, [SP]
    // 0xc3674c: r0 = _interpolate()
    //     0xc3674c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc36750: LeaveFrame
    //     0xc36750: mov             SP, fp
    //     0xc36754: ldp             fp, lr, [SP], #0x10
    // 0xc36758: ret
    //     0xc36758: ret             
    // 0xc3675c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3675c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc36760: b               #0xc366d8
  }
  _ apply(/* No info */) {
    // ** addr: 0xeabed8, size: 0x7c
    // 0xeabed8: EnterFrame
    //     0xeabed8: stp             fp, lr, [SP, #-0x10]!
    //     0xeabedc: mov             fp, SP
    // 0xeabee0: AllocStack(0x10)
    //     0xeabee0: sub             SP, SP, #0x10
    // 0xeabee4: SetupParameters(TableContext this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xeabee4: mov             x0, x2
    //     0xeabee8: mov             x4, x1
    //     0xeabeec: mov             x3, x2
    //     0xeabef0: stur            x1, [fp, #-8]
    //     0xeabef4: stur            x2, [fp, #-0x10]
    // 0xeabef8: r2 = Null
    //     0xeabef8: mov             x2, NULL
    // 0xeabefc: r1 = Null
    //     0xeabefc: mov             x1, NULL
    // 0xeabf00: r4 = 60
    //     0xeabf00: movz            x4, #0x3c
    // 0xeabf04: branchIfSmi(r0, 0xeabf10)
    //     0xeabf04: tbz             w0, #0, #0xeabf10
    // 0xeabf08: r4 = LoadClassIdInstr(r0)
    //     0xeabf08: ldur            x4, [x0, #-1]
    //     0xeabf0c: ubfx            x4, x4, #0xc, #0x14
    // 0xeabf10: cmp             x4, #0x336
    // 0xeabf14: b.eq            #0xeabf2c
    // 0xeabf18: r8 = TableContext
    //     0xeabf18: add             x8, PP, #0x33, lsl #12  ; [pp+0x33758] Type: TableContext
    //     0xeabf1c: ldr             x8, [x8, #0x758]
    // 0xeabf20: r3 = Null
    //     0xeabf20: add             x3, PP, #0x36, lsl #12  ; [pp+0x367f0] Null
    //     0xeabf24: ldr             x3, [x3, #0x7f0]
    // 0xeabf28: r0 = DefaultTypeTest()
    //     0xeabf28: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xeabf2c: ldur            x1, [fp, #-0x10]
    // 0xeabf30: LoadField: r2 = r1->field_7
    //     0xeabf30: ldur            x2, [x1, #7]
    // 0xeabf34: ldur            x3, [fp, #-8]
    // 0xeabf38: StoreField: r3->field_7 = r2
    //     0xeabf38: stur            x2, [x3, #7]
    // 0xeabf3c: LoadField: r2 = r1->field_f
    //     0xeabf3c: ldur            x2, [x1, #0xf]
    // 0xeabf40: StoreField: r3->field_f = r2
    //     0xeabf40: stur            x2, [x3, #0xf]
    // 0xeabf44: r0 = Null
    //     0xeabf44: mov             x0, NULL
    // 0xeabf48: LeaveFrame
    //     0xeabf48: mov             SP, fp
    //     0xeabf4c: ldp             fp, lr, [SP], #0x10
    // 0xeabf50: ret
    //     0xeabf50: ret             
  }
}

// class id: 6792, size: 0x14, field offset: 0x14
enum TableWidth extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e1e4, size: 0x64
    // 0xc4e1e4: EnterFrame
    //     0xc4e1e4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e1e8: mov             fp, SP
    // 0xc4e1ec: AllocStack(0x10)
    //     0xc4e1ec: sub             SP, SP, #0x10
    // 0xc4e1f0: SetupParameters(TableWidth this /* r1 => r0, fp-0x8 */)
    //     0xc4e1f0: mov             x0, x1
    //     0xc4e1f4: stur            x1, [fp, #-8]
    // 0xc4e1f8: CheckStackOverflow
    //     0xc4e1f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e1fc: cmp             SP, x16
    //     0xc4e200: b.ls            #0xc4e240
    // 0xc4e204: r1 = Null
    //     0xc4e204: mov             x1, NULL
    // 0xc4e208: r2 = 4
    //     0xc4e208: movz            x2, #0x4
    // 0xc4e20c: r0 = AllocateArray()
    //     0xc4e20c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e210: r16 = "TableWidth."
    //     0xc4e210: add             x16, PP, #0x33, lsl #12  ; [pp+0x33750] "TableWidth."
    //     0xc4e214: ldr             x16, [x16, #0x750]
    // 0xc4e218: StoreField: r0->field_f = r16
    //     0xc4e218: stur            w16, [x0, #0xf]
    // 0xc4e21c: ldur            x1, [fp, #-8]
    // 0xc4e220: LoadField: r2 = r1->field_f
    //     0xc4e220: ldur            w2, [x1, #0xf]
    // 0xc4e224: DecompressPointer r2
    //     0xc4e224: add             x2, x2, HEAP, lsl #32
    // 0xc4e228: StoreField: r0->field_13 = r2
    //     0xc4e228: stur            w2, [x0, #0x13]
    // 0xc4e22c: str             x0, [SP]
    // 0xc4e230: r0 = _interpolate()
    //     0xc4e230: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e234: LeaveFrame
    //     0xc4e234: mov             SP, fp
    //     0xc4e238: ldp             fp, lr, [SP], #0x10
    // 0xc4e23c: ret
    //     0xc4e23c: ret             
    // 0xc4e240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e244: b               #0xc4e204
  }
}

// class id: 6793, size: 0x14, field offset: 0x14
enum TableCellVerticalAlignment extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e180, size: 0x64
    // 0xc4e180: EnterFrame
    //     0xc4e180: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e184: mov             fp, SP
    // 0xc4e188: AllocStack(0x10)
    //     0xc4e188: sub             SP, SP, #0x10
    // 0xc4e18c: SetupParameters(TableCellVerticalAlignment this /* r1 => r0, fp-0x8 */)
    //     0xc4e18c: mov             x0, x1
    //     0xc4e190: stur            x1, [fp, #-8]
    // 0xc4e194: CheckStackOverflow
    //     0xc4e194: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e198: cmp             SP, x16
    //     0xc4e19c: b.ls            #0xc4e1dc
    // 0xc4e1a0: r1 = Null
    //     0xc4e1a0: mov             x1, NULL
    // 0xc4e1a4: r2 = 4
    //     0xc4e1a4: movz            x2, #0x4
    // 0xc4e1a8: r0 = AllocateArray()
    //     0xc4e1a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e1ac: r16 = "TableCellVerticalAlignment."
    //     0xc4e1ac: add             x16, PP, #0x33, lsl #12  ; [pp+0x33770] "TableCellVerticalAlignment."
    //     0xc4e1b0: ldr             x16, [x16, #0x770]
    // 0xc4e1b4: StoreField: r0->field_f = r16
    //     0xc4e1b4: stur            w16, [x0, #0xf]
    // 0xc4e1b8: ldur            x1, [fp, #-8]
    // 0xc4e1bc: LoadField: r2 = r1->field_f
    //     0xc4e1bc: ldur            w2, [x1, #0xf]
    // 0xc4e1c0: DecompressPointer r2
    //     0xc4e1c0: add             x2, x2, HEAP, lsl #32
    // 0xc4e1c4: StoreField: r0->field_13 = r2
    //     0xc4e1c4: stur            w2, [x0, #0x13]
    // 0xc4e1c8: str             x0, [SP]
    // 0xc4e1cc: r0 = _interpolate()
    //     0xc4e1cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e1d0: LeaveFrame
    //     0xc4e1d0: mov             SP, fp
    //     0xc4e1d4: ldp             fp, lr, [SP], #0x10
    // 0xc4e1d8: ret
    //     0xc4e1d8: ret             
    // 0xc4e1dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e1dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e1e0: b               #0xc4e1a0
  }
}
