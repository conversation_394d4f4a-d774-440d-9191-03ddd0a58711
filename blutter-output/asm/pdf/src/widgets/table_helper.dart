// lib: , url: package:pdf/src/widgets/table_helper.dart

// class id: 1050861, size: 0x8
class :: {
}

// class id: 775, size: 0x8, field offset: 0x8
abstract class TableHelper extends Object {

  static _ fromTextArray(/* No info */) {
    // ** addr: 0xb129b0, size: 0xc4c
    // 0xb129b0: EnterFrame
    //     0xb129b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb129b4: mov             fp, SP
    // 0xb129b8: AllocStack(0xc0)
    //     0xb129b8: sub             SP, SP, #0xc0
    // 0xb129bc: SetupParameters(dynamic _ /* r1 => r2, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */, dynamic _ /* r3 => r3, fp-0x28 */, dynamic _ /* r5 => r5, fp-0x30 */, dynamic _ /* r6 => r6, fp-0x38 */, dynamic _ /* r7 => r7, fp-0x40 */, dynamic _ /* r9, fp-0x10 */, dynamic _ /* r10, fp-0x8 */, {dynamic cellAlignments, dynamic headerAlignments, dynamic headerHeight})
    //     0xb129bc: mov             x0, x2
    //     0xb129c0: stur            x2, [fp, #-0x20]
    //     0xb129c4: mov             x2, x1
    //     0xb129c8: stur            x1, [fp, #-0x18]
    //     0xb129cc: stur            x3, [fp, #-0x28]
    //     0xb129d0: stur            x5, [fp, #-0x30]
    //     0xb129d4: stur            x6, [fp, #-0x38]
    //     0xb129d8: stur            x7, [fp, #-0x40]
    //     0xb129dc: ldur            w1, [x4, #0x13]
    //     0xb129e0: sub             x8, x1, #0x10
    //     0xb129e4: add             x9, fp, w8, sxtw #2
    //     0xb129e8: ldr             x9, [x9, #0x18]
    //     0xb129ec: stur            x9, [fp, #-0x10]
    //     0xb129f0: add             x10, fp, w8, sxtw #2
    //     0xb129f4: ldr             x10, [x10, #0x10]
    //     0xb129f8: stur            x10, [fp, #-8]
    //     0xb129fc: ldur            w1, [x4, #0x1f]
    //     0xb12a00: add             x1, x1, HEAP, lsl #32
    //     0xb12a04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e258] "cellAlignments"
    //     0xb12a08: ldr             x16, [x16, #0x258]
    //     0xb12a0c: cmp             w1, w16
    //     0xb12a10: b.ne            #0xb12a1c
    //     0xb12a14: movz            x1, #0x1
    //     0xb12a18: b               #0xb12a20
    //     0xb12a1c: movz            x1, #0
    //     0xb12a20: lsl             x8, x1, #1
    //     0xb12a24: lsl             w11, w8, #1
    //     0xb12a28: add             w12, w11, #8
    //     0xb12a2c: add             x16, x4, w12, sxtw #1
    //     0xb12a30: ldur            w11, [x16, #0xf]
    //     0xb12a34: add             x11, x11, HEAP, lsl #32
    //     0xb12a38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e260] "headerAlignments"
    //     0xb12a3c: ldr             x16, [x16, #0x260]
    //     0xb12a40: cmp             w11, w16
    //     0xb12a44: b.ne            #0xb12a54
    //     0xb12a48: add             w1, w8, #2
    //     0xb12a4c: sbfx            x8, x1, #1, #0x1f
    //     0xb12a50: mov             x1, x8
    //     0xb12a54: lsl             x8, x1, #1
    //     0xb12a58: lsl             w1, w8, #1
    //     0xb12a5c: add             w8, w1, #8
    //     0xb12a60: add             x16, x4, w8, sxtw #1
    //     0xb12a64: ldur            w1, [x16, #0xf]
    //     0xb12a68: add             x1, x1, HEAP, lsl #32
    //     0xb12a6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e268] "headerHeight"
    //     0xb12a70: ldr             x16, [x16, #0x268]
    //     0xb12a74: cmp             w1, w16
    //     0xb12a78: b.eq            #0xb12a7c
    // 0xb12a7c: CheckStackOverflow
    //     0xb12a7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12a80: cmp             SP, x16
    //     0xb12a84: b.ls            #0xb135d4
    // 0xb12a88: mov             x1, x0
    // 0xb12a8c: r0 = of()
    //     0xb12a8c: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xb12a90: r1 = <TableRow>
    //     0xb12a90: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e270] TypeArguments: <TableRow>
    //     0xb12a94: ldr             x1, [x1, #0x270]
    // 0xb12a98: r2 = 0
    //     0xb12a98: movz            x2, #0
    // 0xb12a9c: r0 = _GrowableList()
    //     0xb12a9c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb12aa0: r1 = <Widget>
    //     0xb12aa0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e250] TypeArguments: <Widget>
    //     0xb12aa4: ldr             x1, [x1, #0x250]
    // 0xb12aa8: r2 = 0
    //     0xb12aa8: movz            x2, #0
    // 0xb12aac: stur            x0, [fp, #-0x48]
    // 0xb12ab0: r0 = _GrowableList()
    //     0xb12ab0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb12ab4: mov             x4, x0
    // 0xb12ab8: ldur            x3, [fp, #-0x40]
    // 0xb12abc: stur            x4, [fp, #-0x78]
    // 0xb12ac0: LoadField: r5 = r3->field_7
    //     0xb12ac0: ldur            w5, [x3, #7]
    // 0xb12ac4: DecompressPointer r5
    //     0xb12ac4: add             x5, x5, HEAP, lsl #32
    // 0xb12ac8: stur            x5, [fp, #-0x70]
    // 0xb12acc: LoadField: r0 = r3->field_b
    //     0xb12acc: ldur            w0, [x3, #0xb]
    // 0xb12ad0: r6 = LoadInt32Instr(r0)
    //     0xb12ad0: sbfx            x6, x0, #1, #0x1f
    // 0xb12ad4: stur            x6, [fp, #-0x68]
    // 0xb12ad8: r7 = _ConstMap len:0
    //     0xb12ad8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e278] Map<int, Alignment>(0)
    //     0xb12adc: ldr             x7, [x7, #0x278]
    // 0xb12ae0: LoadField: r8 = r7->field_f
    //     0xb12ae0: ldur            w8, [x7, #0xf]
    // 0xb12ae4: DecompressPointer r8
    //     0xb12ae4: add             x8, x8, HEAP, lsl #32
    // 0xb12ae8: stur            x8, [fp, #-0x60]
    // 0xb12aec: r0 = 0
    //     0xb12aec: movz            x0, #0
    // 0xb12af0: ldur            x9, [fp, #-0x38]
    // 0xb12af4: CheckStackOverflow
    //     0xb12af4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12af8: cmp             SP, x16
    //     0xb12afc: b.ls            #0xb135dc
    // 0xb12b00: LoadField: r1 = r3->field_b
    //     0xb12b00: ldur            w1, [x3, #0xb]
    // 0xb12b04: r2 = LoadInt32Instr(r1)
    //     0xb12b04: sbfx            x2, x1, #1, #0x1f
    // 0xb12b08: cmp             x6, x2
    // 0xb12b0c: b.ne            #0xb135b4
    // 0xb12b10: cmp             x0, x2
    // 0xb12b14: b.ge            #0xb12d18
    // 0xb12b18: LoadField: r1 = r3->field_f
    //     0xb12b18: ldur            w1, [x3, #0xf]
    // 0xb12b1c: DecompressPointer r1
    //     0xb12b1c: add             x1, x1, HEAP, lsl #32
    // 0xb12b20: ArrayLoad: r10 = r1[r0]  ; Unknown_4
    //     0xb12b20: add             x16, x1, x0, lsl #2
    //     0xb12b24: ldur            w10, [x16, #0xf]
    // 0xb12b28: DecompressPointer r10
    //     0xb12b28: add             x10, x10, HEAP, lsl #32
    // 0xb12b2c: stur            x10, [fp, #-0x58]
    // 0xb12b30: add             x11, x0, #1
    // 0xb12b34: stur            x11, [fp, #-0x50]
    // 0xb12b38: cmp             w10, NULL
    // 0xb12b3c: b.ne            #0xb12b70
    // 0xb12b40: mov             x0, x10
    // 0xb12b44: mov             x2, x5
    // 0xb12b48: r1 = Null
    //     0xb12b48: mov             x1, NULL
    // 0xb12b4c: cmp             w2, NULL
    // 0xb12b50: b.eq            #0xb12b70
    // 0xb12b54: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb12b54: ldur            w4, [x2, #0x17]
    // 0xb12b58: DecompressPointer r4
    //     0xb12b58: add             x4, x4, HEAP, lsl #32
    // 0xb12b5c: r8 = X0
    //     0xb12b5c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb12b60: LoadField: r9 = r4->field_7
    //     0xb12b60: ldur            x9, [x4, #7]
    // 0xb12b64: r3 = Null
    //     0xb12b64: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e280] Null
    //     0xb12b68: ldr             x3, [x3, #0x280]
    // 0xb12b6c: blr             x9
    // 0xb12b70: ldur            x0, [fp, #-0x78]
    // 0xb12b74: r2 = _ConstMap len:0
    //     0xb12b74: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e278] Map<int, Alignment>(0)
    //     0xb12b78: ldr             x2, [x2, #0x278]
    // 0xb12b7c: LoadField: r3 = r0->field_b
    //     0xb12b7c: ldur            w3, [x0, #0xb]
    // 0xb12b80: stur            x3, [fp, #-0x80]
    // 0xb12b84: LoadField: r1 = r2->field_1b
    //     0xb12b84: ldur            w1, [x2, #0x1b]
    // 0xb12b88: DecompressPointer r1
    //     0xb12b88: add             x1, x1, HEAP, lsl #32
    // 0xb12b8c: cmp             w1, NULL
    // 0xb12b90: b.ne            #0xb12b9c
    // 0xb12b94: mov             x1, x2
    // 0xb12b98: r0 = _createIndex()
    //     0xb12b98: bl              #0x7667a0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::_createIndex
    // 0xb12b9c: ldur            x0, [fp, #-0x60]
    // 0xb12ba0: ldur            x2, [fp, #-0x80]
    // 0xb12ba4: r1 = _ConstMap len:0
    //     0xb12ba4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e278] Map<int, Alignment>(0)
    //     0xb12ba8: ldr             x1, [x1, #0x278]
    // 0xb12bac: r0 = _getValueOrData()
    //     0xb12bac: bl              #0xeb9fb0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb12bb0: mov             x1, x0
    // 0xb12bb4: ldur            x0, [fp, #-0x60]
    // 0xb12bb8: cmp             w0, w1
    // 0xb12bbc: b.ne            #0xb12bc4
    // 0xb12bc0: r1 = Null
    //     0xb12bc0: mov             x1, NULL
    // 0xb12bc4: cmp             w1, NULL
    // 0xb12bc8: b.ne            #0xb12bd8
    // 0xb12bcc: r4 = Instance_Alignment
    //     0xb12bcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e290] Obj!Alignment@e0c481
    //     0xb12bd0: ldr             x4, [x4, #0x290]
    // 0xb12bd4: b               #0xb12bdc
    // 0xb12bd8: mov             x4, x1
    // 0xb12bdc: ldur            x2, [fp, #-0x38]
    // 0xb12be0: ldur            x1, [fp, #-0x78]
    // 0xb12be4: ldur            x3, [fp, #-0x58]
    // 0xb12be8: stur            x4, [fp, #-0x80]
    // 0xb12bec: r0 = BoxConstraints()
    //     0xb12bec: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb12bf0: stur            x0, [fp, #-0x88]
    // 0xb12bf4: StoreField: r0->field_7 = rZR
    //     0xb12bf4: stur            xzr, [x0, #7]
    // 0xb12bf8: d0 = inf
    //     0xb12bf8: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xb12bfc: StoreField: r0->field_f = d0
    //     0xb12bfc: stur            d0, [x0, #0xf]
    // 0xb12c00: d1 = 1.000000
    //     0xb12c00: fmov            d1, #1.00000000
    // 0xb12c04: ArrayStore: r0[0] = d1  ; List_8
    //     0xb12c04: stur            d1, [x0, #0x17]
    // 0xb12c08: StoreField: r0->field_1f = d0
    //     0xb12c08: stur            d0, [x0, #0x1f]
    // 0xb12c0c: r0 = TextSpan()
    //     0xb12c0c: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xb12c10: mov             x1, x0
    // 0xb12c14: ldur            x0, [fp, #-0x58]
    // 0xb12c18: stur            x1, [fp, #-0x90]
    // 0xb12c1c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb12c1c: stur            w0, [x1, #0x17]
    // 0xb12c20: ldur            x0, [fp, #-0x38]
    // 0xb12c24: StoreField: r1->field_7 = r0
    //     0xb12c24: stur            w0, [x1, #7]
    // 0xb12c28: StoreField: r1->field_b = rZR
    //     0xb12c28: stur            xzr, [x1, #0xb]
    // 0xb12c2c: r0 = Text()
    //     0xb12c2c: bl              #0xb13848  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb12c30: stur            x0, [fp, #-0x58]
    // 0xb12c34: stp             NULL, NULL, [SP]
    // 0xb12c38: mov             x1, x0
    // 0xb12c3c: ldur            x2, [fp, #-0x90]
    // 0xb12c40: r4 = const [0, 0x4, 0x2, 0x2, overflow, 0x3, textAlign, 0x2, null]
    //     0xb12c40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e298] List(9) [0, 0x4, 0x2, 0x2, "overflow", 0x3, "textAlign", 0x2, Null]
    //     0xb12c44: ldr             x4, [x4, #0x298]
    // 0xb12c48: r0 = RichText()
    //     0xb12c48: bl              #0xb1235c  ; [package:pdf/src/widgets/text.dart] RichText::RichText
    // 0xb12c4c: r0 = Container()
    //     0xb12c4c: bl              #0xb1383c  ; AllocateContainerStub -> Container (size=0x30)
    // 0xb12c50: mov             x2, x0
    // 0xb12c54: ldur            x0, [fp, #-0x80]
    // 0xb12c58: stur            x2, [fp, #-0x90]
    // 0xb12c5c: StoreField: r2->field_13 = r0
    //     0xb12c5c: stur            w0, [x2, #0x13]
    // 0xb12c60: r0 = Instance_EdgeInsets
    //     0xb12c60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e2a0] Obj!EdgeInsets@e0c501
    //     0xb12c64: ldr             x0, [x0, #0x2a0]
    // 0xb12c68: ArrayStore: r2[0] = r0  ; List_4
    //     0xb12c68: stur            w0, [x2, #0x17]
    // 0xb12c6c: ldur            x1, [fp, #-0x58]
    // 0xb12c70: StoreField: r2->field_f = r1
    //     0xb12c70: stur            w1, [x2, #0xf]
    // 0xb12c74: ldur            x1, [fp, #-0x88]
    // 0xb12c78: StoreField: r2->field_23 = r1
    //     0xb12c78: stur            w1, [x2, #0x23]
    // 0xb12c7c: ldur            x3, [fp, #-0x78]
    // 0xb12c80: LoadField: r1 = r3->field_b
    //     0xb12c80: ldur            w1, [x3, #0xb]
    // 0xb12c84: LoadField: r4 = r3->field_f
    //     0xb12c84: ldur            w4, [x3, #0xf]
    // 0xb12c88: DecompressPointer r4
    //     0xb12c88: add             x4, x4, HEAP, lsl #32
    // 0xb12c8c: LoadField: r5 = r4->field_b
    //     0xb12c8c: ldur            w5, [x4, #0xb]
    // 0xb12c90: r4 = LoadInt32Instr(r1)
    //     0xb12c90: sbfx            x4, x1, #1, #0x1f
    // 0xb12c94: stur            x4, [fp, #-0x98]
    // 0xb12c98: r1 = LoadInt32Instr(r5)
    //     0xb12c98: sbfx            x1, x5, #1, #0x1f
    // 0xb12c9c: cmp             x4, x1
    // 0xb12ca0: b.ne            #0xb12cac
    // 0xb12ca4: mov             x1, x3
    // 0xb12ca8: r0 = _growToNextCapacity()
    //     0xb12ca8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb12cac: ldur            x2, [fp, #-0x78]
    // 0xb12cb0: ldur            x3, [fp, #-0x98]
    // 0xb12cb4: add             x0, x3, #1
    // 0xb12cb8: lsl             x1, x0, #1
    // 0xb12cbc: StoreField: r2->field_b = r1
    //     0xb12cbc: stur            w1, [x2, #0xb]
    // 0xb12cc0: LoadField: r1 = r2->field_f
    //     0xb12cc0: ldur            w1, [x2, #0xf]
    // 0xb12cc4: DecompressPointer r1
    //     0xb12cc4: add             x1, x1, HEAP, lsl #32
    // 0xb12cc8: ldur            x0, [fp, #-0x90]
    // 0xb12ccc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb12ccc: add             x25, x1, x3, lsl #2
    //     0xb12cd0: add             x25, x25, #0xf
    //     0xb12cd4: str             w0, [x25]
    //     0xb12cd8: tbz             w0, #0, #0xb12cf4
    //     0xb12cdc: ldurb           w16, [x1, #-1]
    //     0xb12ce0: ldurb           w17, [x0, #-1]
    //     0xb12ce4: and             x16, x17, x16, lsr #2
    //     0xb12ce8: tst             x16, HEAP, lsr #32
    //     0xb12cec: b.eq            #0xb12cf4
    //     0xb12cf0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb12cf4: ldur            x0, [fp, #-0x50]
    // 0xb12cf8: ldur            x3, [fp, #-0x40]
    // 0xb12cfc: mov             x4, x2
    // 0xb12d00: ldur            x5, [fp, #-0x70]
    // 0xb12d04: ldur            x8, [fp, #-0x60]
    // 0xb12d08: ldur            x6, [fp, #-0x68]
    // 0xb12d0c: r7 = _ConstMap len:0
    //     0xb12d0c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e278] Map<int, Alignment>(0)
    //     0xb12d10: ldr             x7, [x7, #0x278]
    // 0xb12d14: b               #0xb12af0
    // 0xb12d18: ldur            x0, [fp, #-0x30]
    // 0xb12d1c: ldur            x1, [fp, #-0x48]
    // 0xb12d20: mov             x2, x4
    // 0xb12d24: r0 = TableRow()
    //     0xb12d24: bl              #0xb13810  ; AllocateTableRowStub -> TableRow (size=0x18)
    // 0xb12d28: mov             x2, x0
    // 0xb12d2c: ldur            x0, [fp, #-0x78]
    // 0xb12d30: stur            x2, [fp, #-0x58]
    // 0xb12d34: StoreField: r2->field_7 = r0
    //     0xb12d34: stur            w0, [x2, #7]
    // 0xb12d38: r0 = true
    //     0xb12d38: add             x0, NULL, #0x20  ; true
    // 0xb12d3c: StoreField: r2->field_b = r0
    //     0xb12d3c: stur            w0, [x2, #0xb]
    // 0xb12d40: ldur            x0, [fp, #-0x30]
    // 0xb12d44: StoreField: r2->field_f = r0
    //     0xb12d44: stur            w0, [x2, #0xf]
    // 0xb12d48: ldur            x3, [fp, #-0x48]
    // 0xb12d4c: LoadField: r1 = r3->field_b
    //     0xb12d4c: ldur            w1, [x3, #0xb]
    // 0xb12d50: LoadField: r4 = r3->field_f
    //     0xb12d50: ldur            w4, [x3, #0xf]
    // 0xb12d54: DecompressPointer r4
    //     0xb12d54: add             x4, x4, HEAP, lsl #32
    // 0xb12d58: LoadField: r5 = r4->field_b
    //     0xb12d58: ldur            w5, [x4, #0xb]
    // 0xb12d5c: r4 = LoadInt32Instr(r1)
    //     0xb12d5c: sbfx            x4, x1, #1, #0x1f
    // 0xb12d60: stur            x4, [fp, #-0x50]
    // 0xb12d64: r1 = LoadInt32Instr(r5)
    //     0xb12d64: sbfx            x1, x5, #1, #0x1f
    // 0xb12d68: cmp             x4, x1
    // 0xb12d6c: b.ne            #0xb12d78
    // 0xb12d70: mov             x1, x3
    // 0xb12d74: r0 = _growToNextCapacity()
    //     0xb12d74: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb12d78: ldur            x4, [fp, #-0x28]
    // 0xb12d7c: ldur            x2, [fp, #-0x48]
    // 0xb12d80: ldur            x3, [fp, #-0x50]
    // 0xb12d84: add             x0, x3, #1
    // 0xb12d88: lsl             x1, x0, #1
    // 0xb12d8c: StoreField: r2->field_b = r1
    //     0xb12d8c: stur            w1, [x2, #0xb]
    // 0xb12d90: LoadField: r1 = r2->field_f
    //     0xb12d90: ldur            w1, [x2, #0xf]
    // 0xb12d94: DecompressPointer r1
    //     0xb12d94: add             x1, x1, HEAP, lsl #32
    // 0xb12d98: ldur            x0, [fp, #-0x58]
    // 0xb12d9c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb12d9c: add             x25, x1, x3, lsl #2
    //     0xb12da0: add             x25, x25, #0xf
    //     0xb12da4: str             w0, [x25]
    //     0xb12da8: tbz             w0, #0, #0xb12dc4
    //     0xb12dac: ldurb           w16, [x1, #-1]
    //     0xb12db0: ldurb           w17, [x0, #-1]
    //     0xb12db4: and             x16, x17, x16, lsr #2
    //     0xb12db8: tst             x16, HEAP, lsr #32
    //     0xb12dbc: b.eq            #0xb12dc4
    //     0xb12dc0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb12dc4: ldur            x1, [fp, #-0x20]
    // 0xb12dc8: r0 = of()
    //     0xb12dc8: bl              #0xb137c8  ; [package:pdf/src/widgets/text_style.dart] Directionality::of
    // 0xb12dcc: ldur            x0, [fp, #-0x28]
    // 0xb12dd0: LoadField: r1 = r0->field_b
    //     0xb12dd0: ldur            w1, [x0, #0xb]
    // 0xb12dd4: r2 = LoadInt32Instr(r1)
    //     0xb12dd4: sbfx            x2, x1, #1, #0x1f
    // 0xb12dd8: stur            x2, [fp, #-0x98]
    // 0xb12ddc: ldur            x1, [fp, #-0x48]
    // 0xb12de0: r6 = 1
    //     0xb12de0: movz            x6, #0x1
    // 0xb12de4: r5 = 0
    //     0xb12de4: movz            x5, #0
    // 0xb12de8: ldur            x3, [fp, #-0x38]
    // 0xb12dec: ldur            x4, [fp, #-0x60]
    // 0xb12df0: stur            x6, [fp, #-0x68]
    // 0xb12df4: CheckStackOverflow
    //     0xb12df4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12df8: cmp             SP, x16
    //     0xb12dfc: b.ls            #0xb135e4
    // 0xb12e00: LoadField: r7 = r0->field_b
    //     0xb12e00: ldur            w7, [x0, #0xb]
    // 0xb12e04: r8 = LoadInt32Instr(r7)
    //     0xb12e04: sbfx            x8, x7, #1, #0x1f
    // 0xb12e08: cmp             x2, x8
    // 0xb12e0c: b.ne            #0xb13598
    // 0xb12e10: cmp             x5, x8
    // 0xb12e14: b.ge            #0xb13570
    // 0xb12e18: LoadField: r7 = r0->field_f
    //     0xb12e18: ldur            w7, [x0, #0xf]
    // 0xb12e1c: DecompressPointer r7
    //     0xb12e1c: add             x7, x7, HEAP, lsl #32
    // 0xb12e20: ArrayLoad: r8 = r7[r5]  ; Unknown_4
    //     0xb12e20: add             x16, x7, x5, lsl #2
    //     0xb12e24: ldur            w8, [x16, #0xf]
    // 0xb12e28: DecompressPointer r8
    //     0xb12e28: add             x8, x8, HEAP, lsl #32
    // 0xb12e2c: stur            x8, [fp, #-0x20]
    // 0xb12e30: add             x7, x5, #1
    // 0xb12e34: stur            x7, [fp, #-0x50]
    // 0xb12e38: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xb12e38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb12e3c: ldr             x0, [x0]
    //     0xb12e40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb12e44: cmp             w0, w16
    //     0xb12e48: b.ne            #0xb12e54
    //     0xb12e4c: ldr             x2, [PP, #0x528]  ; [pp+0x528] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xb12e50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb12e54: r1 = <Widget>
    //     0xb12e54: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e250] TypeArguments: <Widget>
    //     0xb12e58: ldr             x1, [x1, #0x250]
    // 0xb12e5c: stur            x0, [fp, #-0x58]
    // 0xb12e60: r0 = AllocateGrowableArray()
    //     0xb12e60: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb12e64: mov             x2, x0
    // 0xb12e68: ldur            x0, [fp, #-0x58]
    // 0xb12e6c: stur            x2, [fp, #-0x70]
    // 0xb12e70: StoreField: r2->field_f = r0
    //     0xb12e70: stur            w0, [x2, #0xf]
    // 0xb12e74: StoreField: r2->field_b = rZR
    //     0xb12e74: stur            wzr, [x2, #0xb]
    // 0xb12e78: ldur            x0, [fp, #-0x68]
    // 0xb12e7c: ubfx            x0, x0, #0, #0x20
    // 0xb12e80: r3 = 1
    //     0xb12e80: movz            x3, #0x1
    // 0xb12e84: sub             w1, w0, w3
    // 0xb12e88: and             x0, x1, x3
    // 0xb12e8c: ubfx            x0, x0, #0, #0x20
    // 0xb12e90: cbnz            x0, #0xb12e9c
    // 0xb12e94: r4 = false
    //     0xb12e94: add             x4, NULL, #0x30  ; false
    // 0xb12e98: b               #0xb12ea0
    // 0xb12e9c: r4 = true
    //     0xb12e9c: add             x4, NULL, #0x20  ; true
    // 0xb12ea0: ldur            x5, [fp, #-0x68]
    // 0xb12ea4: stur            x4, [fp, #-0x58]
    // 0xb12ea8: cmp             x5, #1
    // 0xb12eac: b.ge            #0xb13180
    // 0xb12eb0: ldur            x1, [fp, #-0x20]
    // 0xb12eb4: r0 = LoadClassIdInstr(r1)
    //     0xb12eb4: ldur            x0, [x1, #-1]
    //     0xb12eb8: ubfx            x0, x0, #0xc, #0x14
    // 0xb12ebc: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xb12ebc: movz            x17, #0xd35d
    //     0xb12ec0: add             lr, x0, x17
    //     0xb12ec4: ldr             lr, [x21, lr, lsl #3]
    //     0xb12ec8: blr             lr
    // 0xb12ecc: mov             x2, x0
    // 0xb12ed0: stur            x2, [fp, #-0x78]
    // 0xb12ed4: ldur            x3, [fp, #-0x70]
    // 0xb12ed8: ldur            x4, [fp, #-0x38]
    // 0xb12edc: ldur            x5, [fp, #-0x60]
    // 0xb12ee0: CheckStackOverflow
    //     0xb12ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12ee4: cmp             SP, x16
    //     0xb12ee8: b.ls            #0xb135ec
    // 0xb12eec: r0 = LoadClassIdInstr(r2)
    //     0xb12eec: ldur            x0, [x2, #-1]
    //     0xb12ef0: ubfx            x0, x0, #0xc, #0x14
    // 0xb12ef4: mov             x1, x2
    // 0xb12ef8: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xb12ef8: movz            x17, #0x292d
    //     0xb12efc: movk            x17, #0x1, lsl #16
    //     0xb12f00: add             lr, x0, x17
    //     0xb12f04: ldr             lr, [x21, lr, lsl #3]
    //     0xb12f08: blr             lr
    // 0xb12f0c: tbnz            w0, #4, #0xb13178
    // 0xb12f10: ldur            x2, [fp, #-0x78]
    // 0xb12f14: ldur            x3, [fp, #-0x70]
    // 0xb12f18: r0 = LoadClassIdInstr(r2)
    //     0xb12f18: ldur            x0, [x2, #-1]
    //     0xb12f1c: ubfx            x0, x0, #0xc, #0x14
    // 0xb12f20: mov             x1, x2
    // 0xb12f24: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xb12f24: movz            x17, #0x384d
    //     0xb12f28: movk            x17, #0x1, lsl #16
    //     0xb12f2c: add             lr, x0, x17
    //     0xb12f30: ldr             lr, [x21, lr, lsl #3]
    //     0xb12f34: blr             lr
    // 0xb12f38: mov             x2, x0
    // 0xb12f3c: ldur            x0, [fp, #-0x70]
    // 0xb12f40: stur            x2, [fp, #-0x88]
    // 0xb12f44: LoadField: r3 = r0->field_b
    //     0xb12f44: ldur            w3, [x0, #0xb]
    // 0xb12f48: stur            x3, [fp, #-0x80]
    // 0xb12f4c: r4 = _ConstMap len:0
    //     0xb12f4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e278] Map<int, Alignment>(0)
    //     0xb12f50: ldr             x4, [x4, #0x278]
    // 0xb12f54: LoadField: r1 = r4->field_1b
    //     0xb12f54: ldur            w1, [x4, #0x1b]
    // 0xb12f58: DecompressPointer r1
    //     0xb12f58: add             x1, x1, HEAP, lsl #32
    // 0xb12f5c: cmp             w1, NULL
    // 0xb12f60: b.ne            #0xb12f6c
    // 0xb12f64: mov             x1, x4
    // 0xb12f68: r0 = _createIndex()
    //     0xb12f68: bl              #0x7667a0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::_createIndex
    // 0xb12f6c: ldur            x0, [fp, #-0x60]
    // 0xb12f70: ldur            x2, [fp, #-0x80]
    // 0xb12f74: r1 = _ConstMap len:0
    //     0xb12f74: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e278] Map<int, Alignment>(0)
    //     0xb12f78: ldr             x1, [x1, #0x278]
    // 0xb12f7c: r0 = _getValueOrData()
    //     0xb12f7c: bl              #0xeb9fb0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb12f80: mov             x1, x0
    // 0xb12f84: ldur            x0, [fp, #-0x60]
    // 0xb12f88: cmp             w0, w1
    // 0xb12f8c: b.ne            #0xb12f94
    // 0xb12f90: r1 = Null
    //     0xb12f90: mov             x1, NULL
    // 0xb12f94: cmp             w1, NULL
    // 0xb12f98: b.ne            #0xb12fa4
    // 0xb12f9c: r1 = Instance_Alignment
    //     0xb12f9c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e290] Obj!Alignment@e0c481
    //     0xb12fa0: ldr             x1, [x1, #0x290]
    // 0xb12fa4: d0 = 0.000000
    //     0xb12fa4: eor             v0.16b, v0.16b, v0.16b
    // 0xb12fa8: stur            x1, [fp, #-0x90]
    // 0xb12fac: LoadField: d1 = r1->field_7
    //     0xb12fac: ldur            d1, [x1, #7]
    // 0xb12fb0: fcmp            d1, d0
    // 0xb12fb4: b.ne            #0xb12fc4
    // 0xb12fb8: r3 = Instance_TextAlign
    //     0xb12fb8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e2a8] Obj!TextAlign@e2e621
    //     0xb12fbc: ldr             x3, [x3, #0x2a8]
    // 0xb12fc0: b               #0xb12fe0
    // 0xb12fc4: fcmp            d0, d1
    // 0xb12fc8: b.le            #0xb12fd8
    // 0xb12fcc: r3 = Instance_TextAlign
    //     0xb12fcc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e2b0] Obj!TextAlign@e2e601
    //     0xb12fd0: ldr             x3, [x3, #0x2b0]
    // 0xb12fd4: b               #0xb12fe0
    // 0xb12fd8: r3 = Instance_TextAlign
    //     0xb12fd8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e2b8] Obj!TextAlign@e2e5e1
    //     0xb12fdc: ldr             x3, [x3, #0x2b8]
    // 0xb12fe0: ldur            x2, [fp, #-0x88]
    // 0xb12fe4: stur            x3, [fp, #-0x80]
    // 0xb12fe8: r0 = BoxConstraints()
    //     0xb12fe8: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb12fec: mov             x1, x0
    // 0xb12ff0: stur            x1, [fp, #-0xa0]
    // 0xb12ff4: StoreField: r1->field_7 = rZR
    //     0xb12ff4: stur            xzr, [x1, #7]
    // 0xb12ff8: d0 = inf
    //     0xb12ff8: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xb12ffc: StoreField: r1->field_f = d0
    //     0xb12ffc: stur            d0, [x1, #0xf]
    // 0xb13000: d1 = 1.000000
    //     0xb13000: fmov            d1, #1.00000000
    // 0xb13004: ArrayStore: r1[0] = d1  ; List_8
    //     0xb13004: stur            d1, [x1, #0x17]
    // 0xb13008: StoreField: r1->field_1f = d0
    //     0xb13008: stur            d0, [x1, #0x1f]
    // 0xb1300c: ldur            x0, [fp, #-0x88]
    // 0xb13010: r2 = 60
    //     0xb13010: movz            x2, #0x3c
    // 0xb13014: branchIfSmi(r0, 0xb13020)
    //     0xb13014: tbz             w0, #0, #0xb13020
    // 0xb13018: r2 = LoadClassIdInstr(r0)
    //     0xb13018: ldur            x2, [x0, #-1]
    //     0xb1301c: ubfx            x2, x2, #0xc, #0x14
    // 0xb13020: sub             x16, x2, #0x314
    // 0xb13024: cmp             x16, #0x15
    // 0xb13028: b.hi            #0xb13038
    // 0xb1302c: mov             x3, x0
    // 0xb13030: mov             x0, x1
    // 0xb13034: b               #0xb130b8
    // 0xb13038: ldur            x2, [fp, #-0x38]
    // 0xb1303c: r3 = 60
    //     0xb1303c: movz            x3, #0x3c
    // 0xb13040: branchIfSmi(r0, 0xb1304c)
    //     0xb13040: tbz             w0, #0, #0xb1304c
    // 0xb13044: r3 = LoadClassIdInstr(r0)
    //     0xb13044: ldur            x3, [x0, #-1]
    //     0xb13048: ubfx            x3, x3, #0xc, #0x14
    // 0xb1304c: str             x0, [SP]
    // 0xb13050: mov             x0, x3
    // 0xb13054: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb13054: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb13058: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb13058: movz            x17, #0x2b03
    //     0xb1305c: add             lr, x0, x17
    //     0xb13060: ldr             lr, [x21, lr, lsl #3]
    //     0xb13064: blr             lr
    // 0xb13068: stur            x0, [fp, #-0x88]
    // 0xb1306c: r0 = TextSpan()
    //     0xb1306c: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xb13070: mov             x1, x0
    // 0xb13074: ldur            x0, [fp, #-0x88]
    // 0xb13078: stur            x1, [fp, #-0xa8]
    // 0xb1307c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1307c: stur            w0, [x1, #0x17]
    // 0xb13080: ldur            x0, [fp, #-0x38]
    // 0xb13084: StoreField: r1->field_7 = r0
    //     0xb13084: stur            w0, [x1, #7]
    // 0xb13088: StoreField: r1->field_b = rZR
    //     0xb13088: stur            xzr, [x1, #0xb]
    // 0xb1308c: r0 = Text()
    //     0xb1308c: bl              #0xb13848  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb13090: stur            x0, [fp, #-0x88]
    // 0xb13094: ldur            x16, [fp, #-0x80]
    // 0xb13098: stp             NULL, x16, [SP]
    // 0xb1309c: mov             x1, x0
    // 0xb130a0: ldur            x2, [fp, #-0xa8]
    // 0xb130a4: r4 = const [0, 0x4, 0x2, 0x2, overflow, 0x3, textAlign, 0x2, null]
    //     0xb130a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e298] List(9) [0, 0x4, 0x2, 0x2, "overflow", 0x3, "textAlign", 0x2, Null]
    //     0xb130a8: ldr             x4, [x4, #0x298]
    // 0xb130ac: r0 = RichText()
    //     0xb130ac: bl              #0xb1235c  ; [package:pdf/src/widgets/text.dart] RichText::RichText
    // 0xb130b0: ldur            x3, [fp, #-0x88]
    // 0xb130b4: ldur            x0, [fp, #-0xa0]
    // 0xb130b8: ldur            x1, [fp, #-0x90]
    // 0xb130bc: ldur            x2, [fp, #-0x70]
    // 0xb130c0: stur            x3, [fp, #-0x80]
    // 0xb130c4: r0 = Container()
    //     0xb130c4: bl              #0xb1383c  ; AllocateContainerStub -> Container (size=0x30)
    // 0xb130c8: mov             x2, x0
    // 0xb130cc: ldur            x0, [fp, #-0x90]
    // 0xb130d0: stur            x2, [fp, #-0x88]
    // 0xb130d4: StoreField: r2->field_13 = r0
    //     0xb130d4: stur            w0, [x2, #0x13]
    // 0xb130d8: r0 = Instance_EdgeInsets
    //     0xb130d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e2a0] Obj!EdgeInsets@e0c501
    //     0xb130dc: ldr             x0, [x0, #0x2a0]
    // 0xb130e0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb130e0: stur            w0, [x2, #0x17]
    // 0xb130e4: ldur            x1, [fp, #-0x80]
    // 0xb130e8: StoreField: r2->field_f = r1
    //     0xb130e8: stur            w1, [x2, #0xf]
    // 0xb130ec: ldur            x1, [fp, #-0xa0]
    // 0xb130f0: StoreField: r2->field_23 = r1
    //     0xb130f0: stur            w1, [x2, #0x23]
    // 0xb130f4: ldur            x3, [fp, #-0x70]
    // 0xb130f8: LoadField: r1 = r3->field_b
    //     0xb130f8: ldur            w1, [x3, #0xb]
    // 0xb130fc: LoadField: r4 = r3->field_f
    //     0xb130fc: ldur            w4, [x3, #0xf]
    // 0xb13100: DecompressPointer r4
    //     0xb13100: add             x4, x4, HEAP, lsl #32
    // 0xb13104: LoadField: r5 = r4->field_b
    //     0xb13104: ldur            w5, [x4, #0xb]
    // 0xb13108: r4 = LoadInt32Instr(r1)
    //     0xb13108: sbfx            x4, x1, #1, #0x1f
    // 0xb1310c: stur            x4, [fp, #-0xb0]
    // 0xb13110: r1 = LoadInt32Instr(r5)
    //     0xb13110: sbfx            x1, x5, #1, #0x1f
    // 0xb13114: cmp             x4, x1
    // 0xb13118: b.ne            #0xb13124
    // 0xb1311c: mov             x1, x3
    // 0xb13120: r0 = _growToNextCapacity()
    //     0xb13120: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb13124: ldur            x2, [fp, #-0x70]
    // 0xb13128: ldur            x3, [fp, #-0xb0]
    // 0xb1312c: add             x0, x3, #1
    // 0xb13130: lsl             x1, x0, #1
    // 0xb13134: StoreField: r2->field_b = r1
    //     0xb13134: stur            w1, [x2, #0xb]
    // 0xb13138: LoadField: r1 = r2->field_f
    //     0xb13138: ldur            w1, [x2, #0xf]
    // 0xb1313c: DecompressPointer r1
    //     0xb1313c: add             x1, x1, HEAP, lsl #32
    // 0xb13140: ldur            x0, [fp, #-0x88]
    // 0xb13144: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb13144: add             x25, x1, x3, lsl #2
    //     0xb13148: add             x25, x25, #0xf
    //     0xb1314c: str             w0, [x25]
    //     0xb13150: tbz             w0, #0, #0xb1316c
    //     0xb13154: ldurb           w16, [x1, #-1]
    //     0xb13158: ldurb           w17, [x0, #-1]
    //     0xb1315c: and             x16, x17, x16, lsr #2
    //     0xb13160: tst             x16, HEAP, lsr #32
    //     0xb13164: b.eq            #0xb1316c
    //     0xb13168: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb1316c: mov             x3, x2
    // 0xb13170: ldur            x2, [fp, #-0x78]
    // 0xb13174: b               #0xb12ed8
    // 0xb13178: ldur            x2, [fp, #-0x70]
    // 0xb1317c: b               #0xb13470
    // 0xb13180: ldur            x1, [fp, #-0x20]
    // 0xb13184: r0 = LoadClassIdInstr(r1)
    //     0xb13184: ldur            x0, [x1, #-1]
    //     0xb13188: ubfx            x0, x0, #0xc, #0x14
    // 0xb1318c: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xb1318c: movz            x17, #0xd35d
    //     0xb13190: add             lr, x0, x17
    //     0xb13194: ldr             lr, [x21, lr, lsl #3]
    //     0xb13198: blr             lr
    // 0xb1319c: mov             x2, x0
    // 0xb131a0: stur            x2, [fp, #-0x20]
    // 0xb131a4: ldur            x3, [fp, #-0x70]
    // 0xb131a8: ldur            x5, [fp, #-0x58]
    // 0xb131ac: ldur            x4, [fp, #-0x60]
    // 0xb131b0: CheckStackOverflow
    //     0xb131b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb131b4: cmp             SP, x16
    //     0xb131b8: b.ls            #0xb135f4
    // 0xb131bc: r0 = LoadClassIdInstr(r2)
    //     0xb131bc: ldur            x0, [x2, #-1]
    //     0xb131c0: ubfx            x0, x0, #0xc, #0x14
    // 0xb131c4: mov             x1, x2
    // 0xb131c8: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xb131c8: movz            x17, #0x292d
    //     0xb131cc: movk            x17, #0x1, lsl #16
    //     0xb131d0: add             lr, x0, x17
    //     0xb131d4: ldr             lr, [x21, lr, lsl #3]
    //     0xb131d8: blr             lr
    // 0xb131dc: tbnz            w0, #4, #0xb1346c
    // 0xb131e0: ldur            x2, [fp, #-0x20]
    // 0xb131e4: ldur            x3, [fp, #-0x70]
    // 0xb131e8: r0 = LoadClassIdInstr(r2)
    //     0xb131e8: ldur            x0, [x2, #-1]
    //     0xb131ec: ubfx            x0, x0, #0xc, #0x14
    // 0xb131f0: mov             x1, x2
    // 0xb131f4: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xb131f4: movz            x17, #0x384d
    //     0xb131f8: movk            x17, #0x1, lsl #16
    //     0xb131fc: add             lr, x0, x17
    //     0xb13200: ldr             lr, [x21, lr, lsl #3]
    //     0xb13204: blr             lr
    // 0xb13208: mov             x2, x0
    // 0xb1320c: ldur            x0, [fp, #-0x70]
    // 0xb13210: stur            x2, [fp, #-0x80]
    // 0xb13214: LoadField: r3 = r0->field_b
    //     0xb13214: ldur            w3, [x0, #0xb]
    // 0xb13218: stur            x3, [fp, #-0x78]
    // 0xb1321c: r4 = _ConstMap len:0
    //     0xb1321c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e278] Map<int, Alignment>(0)
    //     0xb13220: ldr             x4, [x4, #0x278]
    // 0xb13224: LoadField: r1 = r4->field_1b
    //     0xb13224: ldur            w1, [x4, #0x1b]
    // 0xb13228: DecompressPointer r1
    //     0xb13228: add             x1, x1, HEAP, lsl #32
    // 0xb1322c: cmp             w1, NULL
    // 0xb13230: b.ne            #0xb1323c
    // 0xb13234: mov             x1, x4
    // 0xb13238: r0 = _createIndex()
    //     0xb13238: bl              #0x7667a0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::_createIndex
    // 0xb1323c: ldur            x0, [fp, #-0x60]
    // 0xb13240: ldur            x2, [fp, #-0x78]
    // 0xb13244: r1 = _ConstMap len:0
    //     0xb13244: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e278] Map<int, Alignment>(0)
    //     0xb13248: ldr             x1, [x1, #0x278]
    // 0xb1324c: r0 = _getValueOrData()
    //     0xb1324c: bl              #0xeb9fb0  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb13250: mov             x1, x0
    // 0xb13254: ldur            x0, [fp, #-0x60]
    // 0xb13258: cmp             w0, w1
    // 0xb1325c: b.ne            #0xb13264
    // 0xb13260: r1 = Null
    //     0xb13260: mov             x1, NULL
    // 0xb13264: cmp             w1, NULL
    // 0xb13268: b.ne            #0xb13278
    // 0xb1326c: r2 = Instance_Alignment
    //     0xb1326c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e290] Obj!Alignment@e0c481
    //     0xb13270: ldr             x2, [x2, #0x290]
    // 0xb13274: b               #0xb1327c
    // 0xb13278: mov             x2, x1
    // 0xb1327c: ldur            x1, [fp, #-0x80]
    // 0xb13280: stur            x2, [fp, #-0x78]
    // 0xb13284: r0 = BoxConstraints()
    //     0xb13284: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb13288: mov             x1, x0
    // 0xb1328c: stur            x1, [fp, #-0x88]
    // 0xb13290: StoreField: r1->field_7 = rZR
    //     0xb13290: stur            xzr, [x1, #7]
    // 0xb13294: d0 = inf
    //     0xb13294: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xb13298: StoreField: r1->field_f = d0
    //     0xb13298: stur            d0, [x1, #0xf]
    // 0xb1329c: d1 = 1.000000
    //     0xb1329c: fmov            d1, #1.00000000
    // 0xb132a0: ArrayStore: r1[0] = d1  ; List_8
    //     0xb132a0: stur            d1, [x1, #0x17]
    // 0xb132a4: StoreField: r1->field_1f = d0
    //     0xb132a4: stur            d0, [x1, #0x1f]
    // 0xb132a8: ldur            x0, [fp, #-0x80]
    // 0xb132ac: r2 = 60
    //     0xb132ac: movz            x2, #0x3c
    // 0xb132b0: branchIfSmi(r0, 0xb132bc)
    //     0xb132b0: tbz             w0, #0, #0xb132bc
    // 0xb132b4: r2 = LoadClassIdInstr(r0)
    //     0xb132b4: ldur            x2, [x0, #-1]
    //     0xb132b8: ubfx            x2, x2, #0xc, #0x14
    // 0xb132bc: sub             x16, x2, #0x314
    // 0xb132c0: cmp             x16, #0x15
    // 0xb132c4: b.hi            #0xb132d0
    // 0xb132c8: mov             x3, x0
    // 0xb132cc: b               #0xb133ac
    // 0xb132d0: ldur            x2, [fp, #-0x58]
    // 0xb132d4: r3 = 60
    //     0xb132d4: movz            x3, #0x3c
    // 0xb132d8: branchIfSmi(r0, 0xb132e4)
    //     0xb132d8: tbz             w0, #0, #0xb132e4
    // 0xb132dc: r3 = LoadClassIdInstr(r0)
    //     0xb132dc: ldur            x3, [x0, #-1]
    //     0xb132e0: ubfx            x3, x3, #0xc, #0x14
    // 0xb132e4: str             x0, [SP]
    // 0xb132e8: mov             x0, x3
    // 0xb132ec: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb132ec: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb132f0: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb132f0: movz            x17, #0x2b03
    //     0xb132f4: add             lr, x0, x17
    //     0xb132f8: ldr             lr, [x21, lr, lsl #3]
    //     0xb132fc: blr             lr
    // 0xb13300: mov             x1, x0
    // 0xb13304: ldur            x0, [fp, #-0x58]
    // 0xb13308: stur            x1, [fp, #-0xa0]
    // 0xb1330c: tbnz            w0, #4, #0xb13318
    // 0xb13310: ldur            x3, [fp, #-0x10]
    // 0xb13314: b               #0xb1331c
    // 0xb13318: ldur            x3, [fp, #-0x18]
    // 0xb1331c: ldur            x2, [fp, #-0x78]
    // 0xb13320: d0 = 0.000000
    //     0xb13320: eor             v0.16b, v0.16b, v0.16b
    // 0xb13324: stur            x3, [fp, #-0x90]
    // 0xb13328: LoadField: d1 = r2->field_7
    //     0xb13328: ldur            d1, [x2, #7]
    // 0xb1332c: fcmp            d1, d0
    // 0xb13330: b.ne            #0xb13340
    // 0xb13334: r4 = Instance_TextAlign
    //     0xb13334: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e2a8] Obj!TextAlign@e2e621
    //     0xb13338: ldr             x4, [x4, #0x2a8]
    // 0xb1333c: b               #0xb1335c
    // 0xb13340: fcmp            d0, d1
    // 0xb13344: b.le            #0xb13354
    // 0xb13348: r4 = Instance_TextAlign
    //     0xb13348: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e2b0] Obj!TextAlign@e2e601
    //     0xb1334c: ldr             x4, [x4, #0x2b0]
    // 0xb13350: b               #0xb1335c
    // 0xb13354: r4 = Instance_TextAlign
    //     0xb13354: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e2b8] Obj!TextAlign@e2e5e1
    //     0xb13358: ldr             x4, [x4, #0x2b8]
    // 0xb1335c: stur            x4, [fp, #-0x80]
    // 0xb13360: r0 = TextSpan()
    //     0xb13360: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xb13364: mov             x1, x0
    // 0xb13368: ldur            x0, [fp, #-0xa0]
    // 0xb1336c: stur            x1, [fp, #-0xa8]
    // 0xb13370: ArrayStore: r1[0] = r0  ; List_4
    //     0xb13370: stur            w0, [x1, #0x17]
    // 0xb13374: ldur            x0, [fp, #-0x90]
    // 0xb13378: StoreField: r1->field_7 = r0
    //     0xb13378: stur            w0, [x1, #7]
    // 0xb1337c: StoreField: r1->field_b = rZR
    //     0xb1337c: stur            xzr, [x1, #0xb]
    // 0xb13380: r0 = Text()
    //     0xb13380: bl              #0xb13848  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb13384: stur            x0, [fp, #-0x90]
    // 0xb13388: ldur            x16, [fp, #-0x80]
    // 0xb1338c: stp             NULL, x16, [SP]
    // 0xb13390: mov             x1, x0
    // 0xb13394: ldur            x2, [fp, #-0xa8]
    // 0xb13398: r4 = const [0, 0x4, 0x2, 0x2, overflow, 0x3, textAlign, 0x2, null]
    //     0xb13398: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e298] List(9) [0, 0x4, 0x2, 0x2, "overflow", 0x3, "textAlign", 0x2, Null]
    //     0xb1339c: ldr             x4, [x4, #0x298]
    // 0xb133a0: r0 = RichText()
    //     0xb133a0: bl              #0xb1235c  ; [package:pdf/src/widgets/text.dart] RichText::RichText
    // 0xb133a4: ldur            x3, [fp, #-0x90]
    // 0xb133a8: ldur            x1, [fp, #-0x88]
    // 0xb133ac: ldur            x0, [fp, #-0x78]
    // 0xb133b0: ldur            x2, [fp, #-0x70]
    // 0xb133b4: stur            x3, [fp, #-0x80]
    // 0xb133b8: r0 = Container()
    //     0xb133b8: bl              #0xb1383c  ; AllocateContainerStub -> Container (size=0x30)
    // 0xb133bc: mov             x2, x0
    // 0xb133c0: ldur            x0, [fp, #-0x78]
    // 0xb133c4: stur            x2, [fp, #-0x90]
    // 0xb133c8: StoreField: r2->field_13 = r0
    //     0xb133c8: stur            w0, [x2, #0x13]
    // 0xb133cc: r0 = Instance_EdgeInsets
    //     0xb133cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e2a0] Obj!EdgeInsets@e0c501
    //     0xb133d0: ldr             x0, [x0, #0x2a0]
    // 0xb133d4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb133d4: stur            w0, [x2, #0x17]
    // 0xb133d8: ldur            x1, [fp, #-0x80]
    // 0xb133dc: StoreField: r2->field_f = r1
    //     0xb133dc: stur            w1, [x2, #0xf]
    // 0xb133e0: ldur            x1, [fp, #-0x88]
    // 0xb133e4: StoreField: r2->field_23 = r1
    //     0xb133e4: stur            w1, [x2, #0x23]
    // 0xb133e8: ldur            x3, [fp, #-0x70]
    // 0xb133ec: LoadField: r1 = r3->field_b
    //     0xb133ec: ldur            w1, [x3, #0xb]
    // 0xb133f0: LoadField: r4 = r3->field_f
    //     0xb133f0: ldur            w4, [x3, #0xf]
    // 0xb133f4: DecompressPointer r4
    //     0xb133f4: add             x4, x4, HEAP, lsl #32
    // 0xb133f8: LoadField: r5 = r4->field_b
    //     0xb133f8: ldur            w5, [x4, #0xb]
    // 0xb133fc: r4 = LoadInt32Instr(r1)
    //     0xb133fc: sbfx            x4, x1, #1, #0x1f
    // 0xb13400: stur            x4, [fp, #-0xb0]
    // 0xb13404: r1 = LoadInt32Instr(r5)
    //     0xb13404: sbfx            x1, x5, #1, #0x1f
    // 0xb13408: cmp             x4, x1
    // 0xb1340c: b.ne            #0xb13418
    // 0xb13410: mov             x1, x3
    // 0xb13414: r0 = _growToNextCapacity()
    //     0xb13414: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb13418: ldur            x2, [fp, #-0x70]
    // 0xb1341c: ldur            x3, [fp, #-0xb0]
    // 0xb13420: add             x0, x3, #1
    // 0xb13424: lsl             x1, x0, #1
    // 0xb13428: StoreField: r2->field_b = r1
    //     0xb13428: stur            w1, [x2, #0xb]
    // 0xb1342c: LoadField: r1 = r2->field_f
    //     0xb1342c: ldur            w1, [x2, #0xf]
    // 0xb13430: DecompressPointer r1
    //     0xb13430: add             x1, x1, HEAP, lsl #32
    // 0xb13434: ldur            x0, [fp, #-0x90]
    // 0xb13438: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb13438: add             x25, x1, x3, lsl #2
    //     0xb1343c: add             x25, x25, #0xf
    //     0xb13440: str             w0, [x25]
    //     0xb13444: tbz             w0, #0, #0xb13460
    //     0xb13448: ldurb           w16, [x1, #-1]
    //     0xb1344c: ldurb           w17, [x0, #-1]
    //     0xb13450: and             x16, x17, x16, lsr #2
    //     0xb13454: tst             x16, HEAP, lsr #32
    //     0xb13458: b.eq            #0xb13460
    //     0xb1345c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb13460: mov             x3, x2
    // 0xb13464: ldur            x2, [fp, #-0x20]
    // 0xb13468: b               #0xb131a8
    // 0xb1346c: ldur            x2, [fp, #-0x70]
    // 0xb13470: ldur            x0, [fp, #-0x58]
    // 0xb13474: tbnz            w0, #4, #0xb13480
    // 0xb13478: ldur            x1, [fp, #-8]
    // 0xb1347c: b               #0xb13484
    // 0xb13480: r1 = Null
    //     0xb13480: mov             x1, NULL
    // 0xb13484: ldur            x0, [fp, #-0x68]
    // 0xb13488: cmp             x0, #1
    // 0xb1348c: b.ge            #0xb13498
    // 0xb13490: ldur            x3, [fp, #-0x30]
    // 0xb13494: b               #0xb1349c
    // 0xb13498: mov             x3, x1
    // 0xb1349c: ldur            x1, [fp, #-0x48]
    // 0xb134a0: stur            x3, [fp, #-0x58]
    // 0xb134a4: cmp             x0, #1
    // 0xb134a8: r16 = true
    //     0xb134a8: add             x16, NULL, #0x20  ; true
    // 0xb134ac: r17 = false
    //     0xb134ac: add             x17, NULL, #0x30  ; false
    // 0xb134b0: csel            x4, x16, x17, lt
    // 0xb134b4: stur            x4, [fp, #-0x20]
    // 0xb134b8: r0 = TableRow()
    //     0xb134b8: bl              #0xb13810  ; AllocateTableRowStub -> TableRow (size=0x18)
    // 0xb134bc: mov             x2, x0
    // 0xb134c0: ldur            x0, [fp, #-0x70]
    // 0xb134c4: stur            x2, [fp, #-0x78]
    // 0xb134c8: StoreField: r2->field_7 = r0
    //     0xb134c8: stur            w0, [x2, #7]
    // 0xb134cc: ldur            x0, [fp, #-0x20]
    // 0xb134d0: StoreField: r2->field_b = r0
    //     0xb134d0: stur            w0, [x2, #0xb]
    // 0xb134d4: ldur            x0, [fp, #-0x58]
    // 0xb134d8: StoreField: r2->field_f = r0
    //     0xb134d8: stur            w0, [x2, #0xf]
    // 0xb134dc: ldur            x0, [fp, #-0x48]
    // 0xb134e0: LoadField: r1 = r0->field_b
    //     0xb134e0: ldur            w1, [x0, #0xb]
    // 0xb134e4: LoadField: r3 = r0->field_f
    //     0xb134e4: ldur            w3, [x0, #0xf]
    // 0xb134e8: DecompressPointer r3
    //     0xb134e8: add             x3, x3, HEAP, lsl #32
    // 0xb134ec: LoadField: r4 = r3->field_b
    //     0xb134ec: ldur            w4, [x3, #0xb]
    // 0xb134f0: r3 = LoadInt32Instr(r1)
    //     0xb134f0: sbfx            x3, x1, #1, #0x1f
    // 0xb134f4: stur            x3, [fp, #-0xb0]
    // 0xb134f8: r1 = LoadInt32Instr(r4)
    //     0xb134f8: sbfx            x1, x4, #1, #0x1f
    // 0xb134fc: cmp             x3, x1
    // 0xb13500: b.ne            #0xb1350c
    // 0xb13504: mov             x1, x0
    // 0xb13508: r0 = _growToNextCapacity()
    //     0xb13508: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1350c: ldur            x2, [fp, #-0x48]
    // 0xb13510: ldur            x4, [fp, #-0x68]
    // 0xb13514: ldur            x3, [fp, #-0xb0]
    // 0xb13518: add             x0, x3, #1
    // 0xb1351c: lsl             x1, x0, #1
    // 0xb13520: StoreField: r2->field_b = r1
    //     0xb13520: stur            w1, [x2, #0xb]
    // 0xb13524: LoadField: r1 = r2->field_f
    //     0xb13524: ldur            w1, [x2, #0xf]
    // 0xb13528: DecompressPointer r1
    //     0xb13528: add             x1, x1, HEAP, lsl #32
    // 0xb1352c: ldur            x0, [fp, #-0x78]
    // 0xb13530: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb13530: add             x25, x1, x3, lsl #2
    //     0xb13534: add             x25, x25, #0xf
    //     0xb13538: str             w0, [x25]
    //     0xb1353c: tbz             w0, #0, #0xb13558
    //     0xb13540: ldurb           w16, [x1, #-1]
    //     0xb13544: ldurb           w17, [x0, #-1]
    //     0xb13548: and             x16, x17, x16, lsr #2
    //     0xb1354c: tst             x16, HEAP, lsr #32
    //     0xb13550: b.eq            #0xb13558
    //     0xb13554: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb13558: add             x6, x4, #1
    // 0xb1355c: ldur            x5, [fp, #-0x50]
    // 0xb13560: ldur            x0, [fp, #-0x28]
    // 0xb13564: mov             x1, x2
    // 0xb13568: ldur            x2, [fp, #-0x98]
    // 0xb1356c: b               #0xb12de8
    // 0xb13570: mov             x2, x1
    // 0xb13574: r0 = Table()
    //     0xb13574: bl              #0xb137bc  ; AllocateTableStub -> Table (size=0x30)
    // 0xb13578: mov             x1, x0
    // 0xb1357c: ldur            x2, [fp, #-0x48]
    // 0xb13580: stur            x0, [fp, #-8]
    // 0xb13584: r0 = Table()
    //     0xb13584: bl              #0xb136a8  ; [package:pdf/src/widgets/table.dart] Table::Table
    // 0xb13588: ldur            x0, [fp, #-8]
    // 0xb1358c: LeaveFrame
    //     0xb1358c: mov             SP, fp
    //     0xb13590: ldp             fp, lr, [SP], #0x10
    // 0xb13594: ret
    //     0xb13594: ret             
    // 0xb13598: r0 = ConcurrentModificationError()
    //     0xb13598: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xb1359c: mov             x1, x0
    // 0xb135a0: ldur            x0, [fp, #-0x28]
    // 0xb135a4: StoreField: r1->field_b = r0
    //     0xb135a4: stur            w0, [x1, #0xb]
    // 0xb135a8: mov             x0, x1
    // 0xb135ac: r0 = Throw()
    //     0xb135ac: bl              #0xec04b8  ; ThrowStub
    // 0xb135b0: brk             #0
    // 0xb135b4: mov             x0, x3
    // 0xb135b8: r0 = ConcurrentModificationError()
    //     0xb135b8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xb135bc: mov             x1, x0
    // 0xb135c0: ldur            x0, [fp, #-0x40]
    // 0xb135c4: StoreField: r1->field_b = r0
    //     0xb135c4: stur            w0, [x1, #0xb]
    // 0xb135c8: mov             x0, x1
    // 0xb135cc: r0 = Throw()
    //     0xb135cc: bl              #0xec04b8  ; ThrowStub
    // 0xb135d0: brk             #0
    // 0xb135d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb135d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb135d8: b               #0xb12a88
    // 0xb135dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb135dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb135e0: b               #0xb12b00
    // 0xb135e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb135e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb135e8: b               #0xb12e00
    // 0xb135ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb135ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb135f0: b               #0xb12eec
    // 0xb135f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb135f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb135f8: b               #0xb131bc
  }
}
