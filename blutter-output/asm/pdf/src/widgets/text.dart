// lib: , url: package:pdf/src/widgets/text.dart

// class id: 1050862, size: 0x8
class :: {
}

// class id: 767, size: 0x34, field offset: 0x8
//   const constructor, 
class _Line extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc3adf8, size: 0x1d8
    // 0xc3adf8: EnterFrame
    //     0xc3adf8: stp             fp, lr, [SP, #-0x10]!
    //     0xc3adfc: mov             fp, SP
    // 0xc3ae00: AllocStack(0x8)
    //     0xc3ae00: sub             SP, SP, #8
    // 0xc3ae04: CheckStackOverflow
    //     0xc3ae04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3ae08: cmp             SP, x16
    //     0xc3ae0c: b.ls            #0xc3af98
    // 0xc3ae10: r1 = Null
    //     0xc3ae10: mov             x1, NULL
    // 0xc3ae14: r2 = 18
    //     0xc3ae14: movz            x2, #0x12
    // 0xc3ae18: r0 = AllocateArray()
    //     0xc3ae18: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3ae1c: mov             x2, x0
    // 0xc3ae20: r16 = _Line
    //     0xc3ae20: add             x16, PP, #0x47, lsl #12  ; [pp+0x470c0] Type: _Line
    //     0xc3ae24: ldr             x16, [x16, #0xc0]
    // 0xc3ae28: StoreField: r2->field_f = r16
    //     0xc3ae28: stur            w16, [x2, #0xf]
    // 0xc3ae2c: r16 = " "
    //     0xc3ae2c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc3ae30: StoreField: r2->field_13 = r16
    //     0xc3ae30: stur            w16, [x2, #0x13]
    // 0xc3ae34: ldr             x3, [fp, #0x10]
    // 0xc3ae38: LoadField: r4 = r3->field_b
    //     0xc3ae38: ldur            x4, [x3, #0xb]
    // 0xc3ae3c: r0 = BoxInt64Instr(r4)
    //     0xc3ae3c: sbfiz           x0, x4, #1, #0x1f
    //     0xc3ae40: cmp             x4, x0, asr #1
    //     0xc3ae44: b.eq            #0xc3ae50
    //     0xc3ae48: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3ae4c: stur            x4, [x0, #7]
    // 0xc3ae50: mov             x1, x2
    // 0xc3ae54: ArrayStore: r1[2] = r0  ; List_4
    //     0xc3ae54: add             x25, x1, #0x17
    //     0xc3ae58: str             w0, [x25]
    //     0xc3ae5c: tbz             w0, #0, #0xc3ae78
    //     0xc3ae60: ldurb           w16, [x1, #-1]
    //     0xc3ae64: ldurb           w17, [x0, #-1]
    //     0xc3ae68: and             x16, x17, x16, lsr #2
    //     0xc3ae6c: tst             x16, HEAP, lsr #32
    //     0xc3ae70: b.eq            #0xc3ae78
    //     0xc3ae74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3ae78: r16 = "-"
    //     0xc3ae78: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xc3ae7c: StoreField: r2->field_1b = r16
    //     0xc3ae7c: stur            w16, [x2, #0x1b]
    // 0xc3ae80: LoadField: r0 = r3->field_13
    //     0xc3ae80: ldur            x0, [x3, #0x13]
    // 0xc3ae84: add             x5, x4, x0
    // 0xc3ae88: r0 = BoxInt64Instr(r5)
    //     0xc3ae88: sbfiz           x0, x5, #1, #0x1f
    //     0xc3ae8c: cmp             x5, x0, asr #1
    //     0xc3ae90: b.eq            #0xc3ae9c
    //     0xc3ae94: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3ae98: stur            x5, [x0, #7]
    // 0xc3ae9c: mov             x1, x2
    // 0xc3aea0: ArrayStore: r1[4] = r0  ; List_4
    //     0xc3aea0: add             x25, x1, #0x1f
    //     0xc3aea4: str             w0, [x25]
    //     0xc3aea8: tbz             w0, #0, #0xc3aec4
    //     0xc3aeac: ldurb           w16, [x1, #-1]
    //     0xc3aeb0: ldurb           w17, [x0, #-1]
    //     0xc3aeb4: and             x16, x17, x16, lsr #2
    //     0xc3aeb8: tst             x16, HEAP, lsr #32
    //     0xc3aebc: b.eq            #0xc3aec4
    //     0xc3aec0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3aec4: r16 = " baseline: "
    //     0xc3aec4: add             x16, PP, #0x47, lsl #12  ; [pp+0x470c8] " baseline: "
    //     0xc3aec8: ldr             x16, [x16, #0xc8]
    // 0xc3aecc: StoreField: r2->field_23 = r16
    //     0xc3aecc: stur            w16, [x2, #0x23]
    // 0xc3aed0: LoadField: d0 = r3->field_1b
    //     0xc3aed0: ldur            d0, [x3, #0x1b]
    // 0xc3aed4: r0 = inline_Allocate_Double()
    //     0xc3aed4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc3aed8: add             x0, x0, #0x10
    //     0xc3aedc: cmp             x1, x0
    //     0xc3aee0: b.ls            #0xc3afa0
    //     0xc3aee4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc3aee8: sub             x0, x0, #0xf
    //     0xc3aeec: movz            x1, #0xe15c
    //     0xc3aef0: movk            x1, #0x3, lsl #16
    //     0xc3aef4: stur            x1, [x0, #-1]
    // 0xc3aef8: StoreField: r0->field_7 = d0
    //     0xc3aef8: stur            d0, [x0, #7]
    // 0xc3aefc: mov             x1, x2
    // 0xc3af00: ArrayStore: r1[6] = r0  ; List_4
    //     0xc3af00: add             x25, x1, #0x27
    //     0xc3af04: str             w0, [x25]
    //     0xc3af08: tbz             w0, #0, #0xc3af24
    //     0xc3af0c: ldurb           w16, [x1, #-1]
    //     0xc3af10: ldurb           w17, [x0, #-1]
    //     0xc3af14: and             x16, x17, x16, lsr #2
    //     0xc3af18: tst             x16, HEAP, lsr #32
    //     0xc3af1c: b.eq            #0xc3af24
    //     0xc3af20: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3af24: r16 = " width:"
    //     0xc3af24: add             x16, PP, #0x47, lsl #12  ; [pp+0x470d0] " width:"
    //     0xc3af28: ldr             x16, [x16, #0xd0]
    // 0xc3af2c: StoreField: r2->field_2b = r16
    //     0xc3af2c: stur            w16, [x2, #0x2b]
    // 0xc3af30: LoadField: d0 = r3->field_23
    //     0xc3af30: ldur            d0, [x3, #0x23]
    // 0xc3af34: r0 = inline_Allocate_Double()
    //     0xc3af34: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc3af38: add             x0, x0, #0x10
    //     0xc3af3c: cmp             x1, x0
    //     0xc3af40: b.ls            #0xc3afb8
    //     0xc3af44: str             x0, [THR, #0x50]  ; THR::top
    //     0xc3af48: sub             x0, x0, #0xf
    //     0xc3af4c: movz            x1, #0xe15c
    //     0xc3af50: movk            x1, #0x3, lsl #16
    //     0xc3af54: stur            x1, [x0, #-1]
    // 0xc3af58: StoreField: r0->field_7 = d0
    //     0xc3af58: stur            d0, [x0, #7]
    // 0xc3af5c: mov             x1, x2
    // 0xc3af60: ArrayStore: r1[8] = r0  ; List_4
    //     0xc3af60: add             x25, x1, #0x2f
    //     0xc3af64: str             w0, [x25]
    //     0xc3af68: tbz             w0, #0, #0xc3af84
    //     0xc3af6c: ldurb           w16, [x1, #-1]
    //     0xc3af70: ldurb           w17, [x0, #-1]
    //     0xc3af74: and             x16, x17, x16, lsr #2
    //     0xc3af78: tst             x16, HEAP, lsr #32
    //     0xc3af7c: b.eq            #0xc3af84
    //     0xc3af80: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3af84: str             x2, [SP]
    // 0xc3af88: r0 = _interpolate()
    //     0xc3af88: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3af8c: LeaveFrame
    //     0xc3af8c: mov             SP, fp
    //     0xc3af90: ldp             fp, lr, [SP], #0x10
    // 0xc3af94: ret
    //     0xc3af94: ret             
    // 0xc3af98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3af98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3af9c: b               #0xc3ae10
    // 0xc3afa0: SaveReg d0
    //     0xc3afa0: str             q0, [SP, #-0x10]!
    // 0xc3afa4: stp             x2, x3, [SP, #-0x10]!
    // 0xc3afa8: r0 = AllocateDouble()
    //     0xc3afa8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3afac: ldp             x2, x3, [SP], #0x10
    // 0xc3afb0: RestoreReg d0
    //     0xc3afb0: ldr             q0, [SP], #0x10
    // 0xc3afb4: b               #0xc3aef8
    // 0xc3afb8: SaveReg d0
    //     0xc3afb8: str             q0, [SP, #-0x10]!
    // 0xc3afbc: SaveReg r2
    //     0xc3afbc: str             x2, [SP, #-8]!
    // 0xc3afc0: r0 = AllocateDouble()
    //     0xc3afc0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3afc4: RestoreReg r2
    //     0xc3afc4: ldr             x2, [SP], #8
    // 0xc3afc8: RestoreReg d0
    //     0xc3afc8: ldr             q0, [SP], #0x10
    // 0xc3afcc: b               #0xc3af58
  }
  get _ height(/* No info */) {
    // ** addr: 0xe917e0, size: 0xc0
    // 0xe917e0: EnterFrame
    //     0xe917e0: stp             fp, lr, [SP, #-0x10]!
    //     0xe917e4: mov             fp, SP
    // 0xe917e8: AllocStack(0x10)
    //     0xe917e8: sub             SP, SP, #0x10
    // 0xe917ec: CheckStackOverflow
    //     0xe917ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe917f0: cmp             SP, x16
    //     0xe917f4: b.ls            #0xe91898
    // 0xe917f8: LoadField: r0 = r1->field_7
    //     0xe917f8: ldur            w0, [x1, #7]
    // 0xe917fc: DecompressPointer r0
    //     0xe917fc: add             x0, x0, HEAP, lsl #32
    // 0xe91800: LoadField: r2 = r0->field_2f
    //     0xe91800: ldur            w2, [x0, #0x2f]
    // 0xe91804: DecompressPointer r2
    //     0xe91804: add             x2, x2, HEAP, lsl #32
    // 0xe91808: LoadField: r3 = r1->field_b
    //     0xe91808: ldur            x3, [x1, #0xb]
    // 0xe9180c: LoadField: r0 = r1->field_13
    //     0xe9180c: ldur            x0, [x1, #0x13]
    // 0xe91810: add             x4, x3, x0
    // 0xe91814: r0 = BoxInt64Instr(r4)
    //     0xe91814: sbfiz           x0, x4, #1, #0x1f
    //     0xe91818: cmp             x4, x0, asr #1
    //     0xe9181c: b.eq            #0xe91828
    //     0xe91820: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe91824: stur            x4, [x0, #7]
    // 0xe91828: str             x0, [SP]
    // 0xe9182c: mov             x1, x2
    // 0xe91830: mov             x2, x3
    // 0xe91834: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe91834: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe91838: r0 = sublist()
    //     0xe91838: bl              #0x6eabd0  ; [dart:core] _GrowableList::sublist
    // 0xe9183c: stur            x0, [fp, #-8]
    // 0xe91840: LoadField: r1 = r0->field_b
    //     0xe91840: ldur            w1, [x0, #0xb]
    // 0xe91844: cbnz            w1, #0xe91850
    // 0xe91848: d0 = 0.000000
    //     0xe91848: eor             v0.16b, v0.16b, v0.16b
    // 0xe9184c: b               #0xe9188c
    // 0xe91850: r1 = Function '<anonymous closure>':.
    //     0xe91850: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e4d8] AnonymousClosure: (0xe918a0), in [package:pdf/src/widgets/text.dart] _Line::height (0xe917e0)
    //     0xe91854: ldr             x1, [x1, #0x4d8]
    // 0xe91858: r2 = Null
    //     0xe91858: mov             x2, NULL
    // 0xe9185c: r0 = AllocateClosure()
    //     0xe9185c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe91860: ldur            x1, [fp, #-8]
    // 0xe91864: mov             x2, x0
    // 0xe91868: r0 = reduce()
    //     0xe91868: bl              #0x8a5ec4  ; [dart:collection] ListBase::reduce
    // 0xe9186c: r1 = LoadClassIdInstr(r0)
    //     0xe9186c: ldur            x1, [x0, #-1]
    //     0xe91870: ubfx            x1, x1, #0xc, #0x14
    // 0xe91874: mov             x16, x0
    // 0xe91878: mov             x0, x1
    // 0xe9187c: mov             x1, x16
    // 0xe91880: r0 = GDT[cid_x0 + -0xffe]()
    //     0xe91880: sub             lr, x0, #0xffe
    //     0xe91884: ldr             lr, [x21, lr, lsl #3]
    //     0xe91888: blr             lr
    // 0xe9188c: LeaveFrame
    //     0xe9188c: mov             SP, fp
    //     0xe91890: ldp             fp, lr, [SP], #0x10
    // 0xe91894: ret
    //     0xe91894: ret             
    // 0xe91898: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe91898: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe9189c: b               #0xe917f8
  }
  [closure] _Span <anonymous closure>(dynamic, _Span, _Span) {
    // ** addr: 0xe918a0, size: 0xc8
    // 0xe918a0: EnterFrame
    //     0xe918a0: stp             fp, lr, [SP, #-0x10]!
    //     0xe918a4: mov             fp, SP
    // 0xe918a8: ldr             x1, [fp, #0x18]
    // 0xe918ac: r2 = LoadClassIdInstr(r1)
    //     0xe918ac: ldur            x2, [x1, #-1]
    //     0xe918b0: ubfx            x2, x2, #0xc, #0x14
    // 0xe918b4: cmp             x2, #0x305
    // 0xe918b8: b.ne            #0xe918dc
    // 0xe918bc: LoadField: r2 = r1->field_f
    //     0xe918bc: ldur            w2, [x1, #0xf]
    // 0xe918c0: DecompressPointer r2
    //     0xe918c0: add             x2, x2, HEAP, lsl #32
    // 0xe918c4: LoadField: r3 = r2->field_7
    //     0xe918c4: ldur            w3, [x2, #7]
    // 0xe918c8: DecompressPointer r3
    //     0xe918c8: add             x3, x3, HEAP, lsl #32
    // 0xe918cc: cmp             w3, NULL
    // 0xe918d0: b.eq            #0xe91960
    // 0xe918d4: LoadField: d0 = r3->field_1f
    //     0xe918d4: ldur            d0, [x3, #0x1f]
    // 0xe918d8: b               #0xe918f4
    // 0xe918dc: LoadField: r2 = r1->field_13
    //     0xe918dc: ldur            w2, [x1, #0x13]
    // 0xe918e0: DecompressPointer r2
    //     0xe918e0: add             x2, x2, HEAP, lsl #32
    // 0xe918e4: LoadField: d0 = r2->field_27
    //     0xe918e4: ldur            d0, [x2, #0x27]
    // 0xe918e8: LoadField: d1 = r2->field_2f
    //     0xe918e8: ldur            d1, [x2, #0x2f]
    // 0xe918ec: fsub            d2, d0, d1
    // 0xe918f0: mov             v0.16b, v2.16b
    // 0xe918f4: ldr             x2, [fp, #0x10]
    // 0xe918f8: r3 = LoadClassIdInstr(r2)
    //     0xe918f8: ldur            x3, [x2, #-1]
    //     0xe918fc: ubfx            x3, x3, #0xc, #0x14
    // 0xe91900: cmp             x3, #0x305
    // 0xe91904: b.ne            #0xe91928
    // 0xe91908: LoadField: r3 = r2->field_f
    //     0xe91908: ldur            w3, [x2, #0xf]
    // 0xe9190c: DecompressPointer r3
    //     0xe9190c: add             x3, x3, HEAP, lsl #32
    // 0xe91910: LoadField: r4 = r3->field_7
    //     0xe91910: ldur            w4, [x3, #7]
    // 0xe91914: DecompressPointer r4
    //     0xe91914: add             x4, x4, HEAP, lsl #32
    // 0xe91918: cmp             w4, NULL
    // 0xe9191c: b.eq            #0xe91964
    // 0xe91920: LoadField: d1 = r4->field_1f
    //     0xe91920: ldur            d1, [x4, #0x1f]
    // 0xe91924: b               #0xe91940
    // 0xe91928: LoadField: r3 = r2->field_13
    //     0xe91928: ldur            w3, [x2, #0x13]
    // 0xe9192c: DecompressPointer r3
    //     0xe9192c: add             x3, x3, HEAP, lsl #32
    // 0xe91930: LoadField: d1 = r3->field_27
    //     0xe91930: ldur            d1, [x3, #0x27]
    // 0xe91934: LoadField: d2 = r3->field_2f
    //     0xe91934: ldur            d2, [x3, #0x2f]
    // 0xe91938: fsub            d3, d1, d2
    // 0xe9193c: mov             v1.16b, v3.16b
    // 0xe91940: fcmp            d0, d1
    // 0xe91944: b.le            #0xe91950
    // 0xe91948: mov             x0, x1
    // 0xe9194c: b               #0xe91954
    // 0xe91950: mov             x0, x2
    // 0xe91954: LeaveFrame
    //     0xe91954: mov             SP, fp
    //     0xe91958: ldp             fp, lr, [SP], #0x10
    // 0xe9195c: ret
    //     0xe9195c: ret             
    // 0xe91960: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe91960: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe91964: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe91964: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ realign(/* No info */) {
    // ** addr: 0xe91968, size: 0x44c
    // 0xe91968: EnterFrame
    //     0xe91968: stp             fp, lr, [SP, #-0x10]!
    //     0xe9196c: mov             fp, SP
    // 0xe91970: AllocStack(0x60)
    //     0xe91970: sub             SP, SP, #0x60
    // 0xe91974: SetupParameters(_Line this /* r1 => r3, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x38 */)
    //     0xe91974: mov             x3, x1
    //     0xe91978: stur            x1, [fp, #-8]
    //     0xe9197c: stur            d0, [fp, #-0x38]
    // 0xe91980: CheckStackOverflow
    //     0xe91980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe91984: cmp             SP, x16
    //     0xe91988: b.ls            #0xe91d9c
    // 0xe9198c: LoadField: r0 = r3->field_7
    //     0xe9198c: ldur            w0, [x3, #7]
    // 0xe91990: DecompressPointer r0
    //     0xe91990: add             x0, x0, HEAP, lsl #32
    // 0xe91994: LoadField: r2 = r0->field_2f
    //     0xe91994: ldur            w2, [x0, #0x2f]
    // 0xe91998: DecompressPointer r2
    //     0xe91998: add             x2, x2, HEAP, lsl #32
    // 0xe9199c: LoadField: r4 = r3->field_b
    //     0xe9199c: ldur            x4, [x3, #0xb]
    // 0xe919a0: LoadField: r0 = r3->field_13
    //     0xe919a0: ldur            x0, [x3, #0x13]
    // 0xe919a4: add             x5, x4, x0
    // 0xe919a8: r0 = BoxInt64Instr(r5)
    //     0xe919a8: sbfiz           x0, x5, #1, #0x1f
    //     0xe919ac: cmp             x5, x0, asr #1
    //     0xe919b0: b.eq            #0xe919bc
    //     0xe919b4: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe919b8: stur            x5, [x0, #7]
    // 0xe919bc: str             x0, [SP]
    // 0xe919c0: mov             x1, x2
    // 0xe919c4: mov             x2, x4
    // 0xe919c8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe919c8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe919cc: r0 = sublist()
    //     0xe919cc: bl              #0x6eabd0  ; [dart:core] _GrowableList::sublist
    // 0xe919d0: ldur            x1, [fp, #-8]
    // 0xe919d4: stur            x0, [fp, #-0x10]
    // 0xe919d8: r0 = textAlign()
    //     0xe919d8: bl              #0xe91db4  ; [package:pdf/src/widgets/text.dart] _Line::textAlign
    // 0xe919dc: LoadField: r1 = r0->field_7
    //     0xe919dc: ldur            x1, [x0, #7]
    // 0xe919e0: cmp             x1, #2
    // 0xe919e4: b.gt            #0xe91a28
    // 0xe919e8: cmp             x1, #1
    // 0xe919ec: b.gt            #0xe91a1c
    // 0xe919f0: cmp             x1, #0
    // 0xe919f4: b.gt            #0xe91a04
    // 0xe919f8: ldur            x0, [fp, #-8]
    // 0xe919fc: d0 = 0.000000
    //     0xe919fc: eor             v0.16b, v0.16b, v0.16b
    // 0xe91a00: b               #0xe91a74
    // 0xe91a04: ldur            x0, [fp, #-8]
    // 0xe91a08: ldur            d0, [fp, #-0x38]
    // 0xe91a0c: LoadField: d1 = r0->field_23
    //     0xe91a0c: ldur            d1, [x0, #0x23]
    // 0xe91a10: fsub            d2, d0, d1
    // 0xe91a14: mov             v0.16b, v2.16b
    // 0xe91a18: b               #0xe91a74
    // 0xe91a1c: ldur            x0, [fp, #-8]
    // 0xe91a20: d0 = 0.000000
    //     0xe91a20: eor             v0.16b, v0.16b, v0.16b
    // 0xe91a24: b               #0xe91a74
    // 0xe91a28: ldur            x0, [fp, #-8]
    // 0xe91a2c: ldur            d0, [fp, #-0x38]
    // 0xe91a30: cmp             x1, #4
    // 0xe91a34: b.gt            #0xe91a64
    // 0xe91a38: cmp             x1, #3
    // 0xe91a3c: b.gt            #0xe91a50
    // 0xe91a40: LoadField: d1 = r0->field_23
    //     0xe91a40: ldur            d1, [x0, #0x23]
    // 0xe91a44: fsub            d2, d0, d1
    // 0xe91a48: mov             v0.16b, v2.16b
    // 0xe91a4c: b               #0xe91a74
    // 0xe91a50: d1 = 2.000000
    //     0xe91a50: fmov            d1, #2.00000000
    // 0xe91a54: LoadField: d2 = r0->field_23
    //     0xe91a54: ldur            d2, [x0, #0x23]
    // 0xe91a58: fsub            d3, d0, d2
    // 0xe91a5c: fdiv            d0, d3, d1
    // 0xe91a60: b               #0xe91a74
    // 0xe91a64: LoadField: r1 = r0->field_2f
    //     0xe91a64: ldur            w1, [x0, #0x2f]
    // 0xe91a68: DecompressPointer r1
    //     0xe91a68: add             x1, x1, HEAP, lsl #32
    // 0xe91a6c: tbz             w1, #4, #0xe91bc8
    // 0xe91a70: d0 = 0.000000
    //     0xe91a70: eor             v0.16b, v0.16b, v0.16b
    // 0xe91a74: ldur            x3, [fp, #-0x10]
    // 0xe91a78: stur            d0, [fp, #-0x48]
    // 0xe91a7c: LoadField: r4 = r3->field_7
    //     0xe91a7c: ldur            w4, [x3, #7]
    // 0xe91a80: DecompressPointer r4
    //     0xe91a80: add             x4, x4, HEAP, lsl #32
    // 0xe91a84: stur            x4, [fp, #-0x30]
    // 0xe91a88: LoadField: r1 = r3->field_b
    //     0xe91a88: ldur            w1, [x3, #0xb]
    // 0xe91a8c: r5 = LoadInt32Instr(r1)
    //     0xe91a8c: sbfx            x5, x1, #1, #0x1f
    // 0xe91a90: stur            x5, [fp, #-0x28]
    // 0xe91a94: LoadField: d1 = r0->field_1b
    //     0xe91a94: ldur            d1, [x0, #0x1b]
    // 0xe91a98: fneg            d2, d1
    // 0xe91a9c: stur            d2, [fp, #-0x40]
    // 0xe91aa0: r0 = 0
    //     0xe91aa0: movz            x0, #0
    // 0xe91aa4: CheckStackOverflow
    //     0xe91aa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe91aa8: cmp             SP, x16
    //     0xe91aac: b.ls            #0xe91da4
    // 0xe91ab0: LoadField: r1 = r3->field_b
    //     0xe91ab0: ldur            w1, [x3, #0xb]
    // 0xe91ab4: r2 = LoadInt32Instr(r1)
    //     0xe91ab4: sbfx            x2, x1, #1, #0x1f
    // 0xe91ab8: cmp             x5, x2
    // 0xe91abc: b.ne            #0xe91d5c
    // 0xe91ac0: cmp             x0, x2
    // 0xe91ac4: b.ge            #0xe91bb8
    // 0xe91ac8: LoadField: r1 = r3->field_f
    //     0xe91ac8: ldur            w1, [x3, #0xf]
    // 0xe91acc: DecompressPointer r1
    //     0xe91acc: add             x1, x1, HEAP, lsl #32
    // 0xe91ad0: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0xe91ad0: add             x16, x1, x0, lsl #2
    //     0xe91ad4: ldur            w6, [x16, #0xf]
    // 0xe91ad8: DecompressPointer r6
    //     0xe91ad8: add             x6, x6, HEAP, lsl #32
    // 0xe91adc: stur            x6, [fp, #-0x20]
    // 0xe91ae0: add             x7, x0, #1
    // 0xe91ae4: stur            x7, [fp, #-0x18]
    // 0xe91ae8: cmp             w6, NULL
    // 0xe91aec: b.ne            #0xe91b20
    // 0xe91af0: mov             x0, x6
    // 0xe91af4: mov             x2, x4
    // 0xe91af8: r1 = Null
    //     0xe91af8: mov             x1, NULL
    // 0xe91afc: cmp             w2, NULL
    // 0xe91b00: b.eq            #0xe91b20
    // 0xe91b04: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe91b04: ldur            w4, [x2, #0x17]
    // 0xe91b08: DecompressPointer r4
    //     0xe91b08: add             x4, x4, HEAP, lsl #32
    // 0xe91b0c: r8 = X0
    //     0xe91b0c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe91b10: LoadField: r9 = r4->field_7
    //     0xe91b10: ldur            x9, [x4, #7]
    // 0xe91b14: r3 = Null
    //     0xe91b14: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e4e0] Null
    //     0xe91b18: ldr             x3, [x3, #0x4e0]
    // 0xe91b1c: blr             x9
    // 0xe91b20: ldur            d0, [fp, #-0x48]
    // 0xe91b24: ldur            d1, [fp, #-0x40]
    // 0xe91b28: ldur            x2, [fp, #-0x20]
    // 0xe91b2c: r0 = LoadClassIdInstr(r2)
    //     0xe91b2c: ldur            x0, [x2, #-1]
    //     0xe91b30: ubfx            x0, x0, #0xc, #0x14
    // 0xe91b34: mov             x1, x2
    // 0xe91b38: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe91b38: sub             lr, x0, #1, lsl #12
    //     0xe91b3c: ldr             lr, [x21, lr, lsl #3]
    //     0xe91b40: blr             lr
    // 0xe91b44: LoadField: d0 = r0->field_7
    //     0xe91b44: ldur            d0, [x0, #7]
    // 0xe91b48: ldur            d1, [fp, #-0x48]
    // 0xe91b4c: fadd            d2, d0, d1
    // 0xe91b50: stur            d2, [fp, #-0x58]
    // 0xe91b54: LoadField: d0 = r0->field_f
    //     0xe91b54: ldur            d0, [x0, #0xf]
    // 0xe91b58: ldur            d3, [fp, #-0x40]
    // 0xe91b5c: fadd            d4, d0, d3
    // 0xe91b60: stur            d4, [fp, #-0x50]
    // 0xe91b64: r0 = PdfPoint()
    //     0xe91b64: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe91b68: ldur            d0, [fp, #-0x58]
    // 0xe91b6c: StoreField: r0->field_7 = d0
    //     0xe91b6c: stur            d0, [x0, #7]
    // 0xe91b70: ldur            d0, [fp, #-0x50]
    // 0xe91b74: StoreField: r0->field_f = d0
    //     0xe91b74: stur            d0, [x0, #0xf]
    // 0xe91b78: ldur            x1, [fp, #-0x20]
    // 0xe91b7c: r2 = LoadClassIdInstr(r1)
    //     0xe91b7c: ldur            x2, [x1, #-1]
    //     0xe91b80: ubfx            x2, x2, #0xc, #0x14
    // 0xe91b84: mov             x16, x0
    // 0xe91b88: mov             x0, x2
    // 0xe91b8c: mov             x2, x16
    // 0xe91b90: r0 = GDT[cid_x0 + -0xfee]()
    //     0xe91b90: sub             lr, x0, #0xfee
    //     0xe91b94: ldr             lr, [x21, lr, lsl #3]
    //     0xe91b98: blr             lr
    // 0xe91b9c: ldur            x0, [fp, #-0x18]
    // 0xe91ba0: ldur            x3, [fp, #-0x10]
    // 0xe91ba4: ldur            d0, [fp, #-0x48]
    // 0xe91ba8: ldur            d2, [fp, #-0x40]
    // 0xe91bac: ldur            x4, [fp, #-0x30]
    // 0xe91bb0: ldur            x5, [fp, #-0x28]
    // 0xe91bb4: b               #0xe91aa4
    // 0xe91bb8: r0 = Null
    //     0xe91bb8: mov             x0, NULL
    // 0xe91bbc: LeaveFrame
    //     0xe91bbc: mov             SP, fp
    //     0xe91bc0: ldp             fp, lr, [SP], #0x10
    // 0xe91bc4: ret
    //     0xe91bc4: ret             
    // 0xe91bc8: ldur            x3, [fp, #-0x10]
    // 0xe91bcc: LoadField: d1 = r0->field_23
    //     0xe91bcc: ldur            d1, [x0, #0x23]
    // 0xe91bd0: fsub            d2, d0, d1
    // 0xe91bd4: LoadField: r1 = r3->field_b
    //     0xe91bd4: ldur            w1, [x3, #0xb]
    // 0xe91bd8: r4 = LoadInt32Instr(r1)
    //     0xe91bd8: sbfx            x4, x1, #1, #0x1f
    // 0xe91bdc: stur            x4, [fp, #-0x28]
    // 0xe91be0: sub             x1, x4, #1
    // 0xe91be4: scvtf           d0, x1
    // 0xe91be8: fdiv            d1, d2, d0
    // 0xe91bec: stur            d1, [fp, #-0x48]
    // 0xe91bf0: LoadField: r5 = r3->field_7
    //     0xe91bf0: ldur            w5, [x3, #7]
    // 0xe91bf4: DecompressPointer r5
    //     0xe91bf4: add             x5, x5, HEAP, lsl #32
    // 0xe91bf8: stur            x5, [fp, #-0x20]
    // 0xe91bfc: LoadField: d0 = r0->field_1b
    //     0xe91bfc: ldur            d0, [x0, #0x1b]
    // 0xe91c00: stur            d0, [fp, #-0x40]
    // 0xe91c04: d2 = 0.000000
    //     0xe91c04: eor             v2.16b, v2.16b, v2.16b
    // 0xe91c08: r0 = 0
    //     0xe91c08: movz            x0, #0
    // 0xe91c0c: stur            d2, [fp, #-0x38]
    // 0xe91c10: CheckStackOverflow
    //     0xe91c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe91c14: cmp             SP, x16
    //     0xe91c18: b.ls            #0xe91dac
    // 0xe91c1c: LoadField: r1 = r3->field_b
    //     0xe91c1c: ldur            w1, [x3, #0xb]
    // 0xe91c20: r2 = LoadInt32Instr(r1)
    //     0xe91c20: sbfx            x2, x1, #1, #0x1f
    // 0xe91c24: cmp             x4, x2
    // 0xe91c28: b.ne            #0xe91d7c
    // 0xe91c2c: cmp             x0, x2
    // 0xe91c30: b.ge            #0xe91d4c
    // 0xe91c34: LoadField: r1 = r3->field_f
    //     0xe91c34: ldur            w1, [x3, #0xf]
    // 0xe91c38: DecompressPointer r1
    //     0xe91c38: add             x1, x1, HEAP, lsl #32
    // 0xe91c3c: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0xe91c3c: add             x16, x1, x0, lsl #2
    //     0xe91c40: ldur            w6, [x16, #0xf]
    // 0xe91c44: DecompressPointer r6
    //     0xe91c44: add             x6, x6, HEAP, lsl #32
    // 0xe91c48: stur            x6, [fp, #-8]
    // 0xe91c4c: add             x7, x0, #1
    // 0xe91c50: stur            x7, [fp, #-0x18]
    // 0xe91c54: cmp             w6, NULL
    // 0xe91c58: b.ne            #0xe91c8c
    // 0xe91c5c: mov             x0, x6
    // 0xe91c60: mov             x2, x5
    // 0xe91c64: r1 = Null
    //     0xe91c64: mov             x1, NULL
    // 0xe91c68: cmp             w2, NULL
    // 0xe91c6c: b.eq            #0xe91c8c
    // 0xe91c70: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe91c70: ldur            w4, [x2, #0x17]
    // 0xe91c74: DecompressPointer r4
    //     0xe91c74: add             x4, x4, HEAP, lsl #32
    // 0xe91c78: r8 = X0
    //     0xe91c78: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe91c7c: LoadField: r9 = r4->field_7
    //     0xe91c7c: ldur            x9, [x4, #7]
    // 0xe91c80: r3 = Null
    //     0xe91c80: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e4f0] Null
    //     0xe91c84: ldr             x3, [x3, #0x4f0]
    // 0xe91c88: blr             x9
    // 0xe91c8c: ldur            d0, [fp, #-0x48]
    // 0xe91c90: ldur            d2, [fp, #-0x38]
    // 0xe91c94: ldur            d1, [fp, #-0x40]
    // 0xe91c98: ldur            x2, [fp, #-8]
    // 0xe91c9c: r0 = LoadClassIdInstr(r2)
    //     0xe91c9c: ldur            x0, [x2, #-1]
    //     0xe91ca0: ubfx            x0, x0, #0xc, #0x14
    // 0xe91ca4: mov             x1, x2
    // 0xe91ca8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe91ca8: sub             lr, x0, #1, lsl #12
    //     0xe91cac: ldr             lr, [x21, lr, lsl #3]
    //     0xe91cb0: blr             lr
    // 0xe91cb4: LoadField: d0 = r0->field_7
    //     0xe91cb4: ldur            d0, [x0, #7]
    // 0xe91cb8: ldur            d1, [fp, #-0x38]
    // 0xe91cbc: fadd            d2, d0, d1
    // 0xe91cc0: ldur            x2, [fp, #-8]
    // 0xe91cc4: stur            d2, [fp, #-0x50]
    // 0xe91cc8: r0 = LoadClassIdInstr(r2)
    //     0xe91cc8: ldur            x0, [x2, #-1]
    //     0xe91ccc: ubfx            x0, x0, #0xc, #0x14
    // 0xe91cd0: mov             x1, x2
    // 0xe91cd4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe91cd4: sub             lr, x0, #1, lsl #12
    //     0xe91cd8: ldr             lr, [x21, lr, lsl #3]
    //     0xe91cdc: blr             lr
    // 0xe91ce0: LoadField: d0 = r0->field_f
    //     0xe91ce0: ldur            d0, [x0, #0xf]
    // 0xe91ce4: ldur            d1, [fp, #-0x40]
    // 0xe91ce8: fsub            d2, d0, d1
    // 0xe91cec: stur            d2, [fp, #-0x58]
    // 0xe91cf0: r0 = PdfPoint()
    //     0xe91cf0: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe91cf4: ldur            d0, [fp, #-0x50]
    // 0xe91cf8: StoreField: r0->field_7 = d0
    //     0xe91cf8: stur            d0, [x0, #7]
    // 0xe91cfc: ldur            d0, [fp, #-0x58]
    // 0xe91d00: StoreField: r0->field_f = d0
    //     0xe91d00: stur            d0, [x0, #0xf]
    // 0xe91d04: ldur            x1, [fp, #-8]
    // 0xe91d08: r2 = LoadClassIdInstr(r1)
    //     0xe91d08: ldur            x2, [x1, #-1]
    //     0xe91d0c: ubfx            x2, x2, #0xc, #0x14
    // 0xe91d10: mov             x16, x0
    // 0xe91d14: mov             x0, x2
    // 0xe91d18: mov             x2, x16
    // 0xe91d1c: r0 = GDT[cid_x0 + -0xfee]()
    //     0xe91d1c: sub             lr, x0, #0xfee
    //     0xe91d20: ldr             lr, [x21, lr, lsl #3]
    //     0xe91d24: blr             lr
    // 0xe91d28: ldur            d1, [fp, #-0x48]
    // 0xe91d2c: ldur            d0, [fp, #-0x38]
    // 0xe91d30: fadd            d2, d0, d1
    // 0xe91d34: ldur            x0, [fp, #-0x18]
    // 0xe91d38: ldur            x3, [fp, #-0x10]
    // 0xe91d3c: ldur            d0, [fp, #-0x40]
    // 0xe91d40: ldur            x5, [fp, #-0x20]
    // 0xe91d44: ldur            x4, [fp, #-0x28]
    // 0xe91d48: b               #0xe91c0c
    // 0xe91d4c: r0 = Null
    //     0xe91d4c: mov             x0, NULL
    // 0xe91d50: LeaveFrame
    //     0xe91d50: mov             SP, fp
    //     0xe91d54: ldp             fp, lr, [SP], #0x10
    // 0xe91d58: ret
    //     0xe91d58: ret             
    // 0xe91d5c: mov             x0, x3
    // 0xe91d60: r0 = ConcurrentModificationError()
    //     0xe91d60: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe91d64: mov             x1, x0
    // 0xe91d68: ldur            x0, [fp, #-0x10]
    // 0xe91d6c: StoreField: r1->field_b = r0
    //     0xe91d6c: stur            w0, [x1, #0xb]
    // 0xe91d70: mov             x0, x1
    // 0xe91d74: r0 = Throw()
    //     0xe91d74: bl              #0xec04b8  ; ThrowStub
    // 0xe91d78: brk             #0
    // 0xe91d7c: mov             x0, x3
    // 0xe91d80: r0 = ConcurrentModificationError()
    //     0xe91d80: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe91d84: mov             x1, x0
    // 0xe91d88: ldur            x0, [fp, #-0x10]
    // 0xe91d8c: StoreField: r1->field_b = r0
    //     0xe91d8c: stur            w0, [x1, #0xb]
    // 0xe91d90: mov             x0, x1
    // 0xe91d94: r0 = Throw()
    //     0xe91d94: bl              #0xec04b8  ; ThrowStub
    // 0xe91d98: brk             #0
    // 0xe91d9c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe91d9c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe91da0: b               #0xe9198c
    // 0xe91da4: r0 = StackOverflowSharedWithFPURegs()
    //     0xe91da4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe91da8: b               #0xe91ab0
    // 0xe91dac: r0 = StackOverflowSharedWithFPURegs()
    //     0xe91dac: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe91db0: b               #0xe91c1c
  }
  get _ textAlign(/* No info */) {
    // ** addr: 0xe91db4, size: 0x34
    // 0xe91db4: LoadField: r2 = r1->field_7
    //     0xe91db4: ldur            w2, [x1, #7]
    // 0xe91db8: DecompressPointer r2
    //     0xe91db8: add             x2, x2, HEAP, lsl #32
    // 0xe91dbc: LoadField: r0 = r2->field_13
    //     0xe91dbc: ldur            w0, [x2, #0x13]
    // 0xe91dc0: DecompressPointer r0
    //     0xe91dc0: add             x0, x0, HEAP, lsl #32
    // 0xe91dc4: r16 = Sentinel
    //     0xe91dc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe91dc8: cmp             w0, w16
    // 0xe91dcc: b.eq            #0xe91dd4
    // 0xe91dd0: ret
    //     0xe91dd0: ret             
    // 0xe91dd4: EnterFrame
    //     0xe91dd4: stp             fp, lr, [SP, #-0x10]!
    //     0xe91dd8: mov             fp, SP
    // 0xe91ddc: r9 = _textAlign
    //     0xe91ddc: add             x9, PP, #0x3e, lsl #12  ; [pp+0x3e500] Field <RichText._textAlign@2271243954>: late (offset: 0x14)
    //     0xe91de0: ldr             x9, [x9, #0x500]
    // 0xe91de4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe91de4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 768, size: 0x18, field offset: 0x8
//   const constructor, 
abstract class InlineSpan extends Object {
}

// class id: 769, size: 0x20, field offset: 0x18
//   const constructor, 
class TextSpan extends InlineSpan {

  _Mint field_c;
  _OneByteString field_18;

  _ visitChildren(/* No info */) {
    // ** addr: 0xeb0d68, size: 0x164
    // 0xeb0d68: EnterFrame
    //     0xeb0d68: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0d6c: mov             fp, SP
    // 0xeb0d70: AllocStack(0x50)
    //     0xeb0d70: sub             SP, SP, #0x50
    // 0xeb0d74: SetupParameters(TextSpan this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r1 */)
    //     0xeb0d74: stur            x1, [fp, #-8]
    //     0xeb0d78: mov             x16, x3
    //     0xeb0d7c: mov             x3, x1
    //     0xeb0d80: mov             x1, x16
    //     0xeb0d84: mov             x0, x2
    //     0xeb0d88: stur            x2, [fp, #-0x10]
    // 0xeb0d8c: CheckStackOverflow
    //     0xeb0d8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0d90: cmp             SP, x16
    //     0xeb0d94: b.ls            #0xeb0ebc
    // 0xeb0d98: LoadField: r2 = r3->field_7
    //     0xeb0d98: ldur            w2, [x3, #7]
    // 0xeb0d9c: DecompressPointer r2
    //     0xeb0d9c: add             x2, x2, HEAP, lsl #32
    // 0xeb0da0: r0 = merge()
    //     0xeb0da0: bl              #0xb0faa8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::merge
    // 0xeb0da4: mov             x2, x0
    // 0xeb0da8: ldur            x1, [fp, #-8]
    // 0xeb0dac: stur            x2, [fp, #-0x18]
    // 0xeb0db0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xeb0db0: ldur            w0, [x1, #0x17]
    // 0xeb0db4: DecompressPointer r0
    //     0xeb0db4: add             x0, x0, HEAP, lsl #32
    // 0xeb0db8: cmp             w0, NULL
    // 0xeb0dbc: b.eq            #0xeb0ddc
    // 0xeb0dc0: ldur            x16, [fp, #-0x10]
    // 0xeb0dc4: stp             x1, x16, [SP, #0x10]
    // 0xeb0dc8: stp             NULL, x2, [SP]
    // 0xeb0dcc: ldur            x0, [fp, #-0x10]
    // 0xeb0dd0: ClosureCall
    //     0xeb0dd0: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xeb0dd4: ldur            x2, [x0, #0x1f]
    //     0xeb0dd8: blr             x2
    // 0xeb0ddc: ldur            x0, [fp, #-8]
    // 0xeb0de0: LoadField: r4 = r0->field_1b
    //     0xeb0de0: ldur            w4, [x0, #0x1b]
    // 0xeb0de4: DecompressPointer r4
    //     0xeb0de4: add             x4, x4, HEAP, lsl #32
    // 0xeb0de8: stur            x4, [fp, #-0x30]
    // 0xeb0dec: cmp             w4, NULL
    // 0xeb0df0: b.eq            #0xeb0e8c
    // 0xeb0df4: LoadField: r0 = r4->field_b
    //     0xeb0df4: ldur            w0, [x4, #0xb]
    // 0xeb0df8: r6 = LoadInt32Instr(r0)
    //     0xeb0df8: sbfx            x6, x0, #1, #0x1f
    // 0xeb0dfc: stur            x6, [fp, #-0x28]
    // 0xeb0e00: r0 = 0
    //     0xeb0e00: movz            x0, #0
    // 0xeb0e04: CheckStackOverflow
    //     0xeb0e04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0e08: cmp             SP, x16
    //     0xeb0e0c: b.ls            #0xeb0ec4
    // 0xeb0e10: LoadField: r1 = r4->field_b
    //     0xeb0e10: ldur            w1, [x4, #0xb]
    // 0xeb0e14: r2 = LoadInt32Instr(r1)
    //     0xeb0e14: sbfx            x2, x1, #1, #0x1f
    // 0xeb0e18: cmp             x6, x2
    // 0xeb0e1c: b.ne            #0xeb0e9c
    // 0xeb0e20: cmp             x0, x2
    // 0xeb0e24: b.ge            #0xeb0e8c
    // 0xeb0e28: LoadField: r1 = r4->field_f
    //     0xeb0e28: ldur            w1, [x4, #0xf]
    // 0xeb0e2c: DecompressPointer r1
    //     0xeb0e2c: add             x1, x1, HEAP, lsl #32
    // 0xeb0e30: ArrayLoad: r2 = r1[r0]  ; Unknown_4
    //     0xeb0e30: add             x16, x1, x0, lsl #2
    //     0xeb0e34: ldur            w2, [x16, #0xf]
    // 0xeb0e38: DecompressPointer r2
    //     0xeb0e38: add             x2, x2, HEAP, lsl #32
    // 0xeb0e3c: add             x7, x0, #1
    // 0xeb0e40: stur            x7, [fp, #-0x20]
    // 0xeb0e44: r0 = LoadClassIdInstr(r2)
    //     0xeb0e44: ldur            x0, [x2, #-1]
    //     0xeb0e48: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0e4c: mov             x1, x2
    // 0xeb0e50: ldur            x2, [fp, #-0x10]
    // 0xeb0e54: ldur            x3, [fp, #-0x18]
    // 0xeb0e58: r5 = Null
    //     0xeb0e58: mov             x5, NULL
    // 0xeb0e5c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb0e5c: sub             lr, x0, #1, lsl #12
    //     0xeb0e60: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0e64: blr             lr
    // 0xeb0e68: tbnz            w0, #4, #0xeb0e7c
    // 0xeb0e6c: ldur            x0, [fp, #-0x20]
    // 0xeb0e70: ldur            x4, [fp, #-0x30]
    // 0xeb0e74: ldur            x6, [fp, #-0x28]
    // 0xeb0e78: b               #0xeb0e04
    // 0xeb0e7c: r0 = false
    //     0xeb0e7c: add             x0, NULL, #0x30  ; false
    // 0xeb0e80: LeaveFrame
    //     0xeb0e80: mov             SP, fp
    //     0xeb0e84: ldp             fp, lr, [SP], #0x10
    // 0xeb0e88: ret
    //     0xeb0e88: ret             
    // 0xeb0e8c: r0 = true
    //     0xeb0e8c: add             x0, NULL, #0x20  ; true
    // 0xeb0e90: LeaveFrame
    //     0xeb0e90: mov             SP, fp
    //     0xeb0e94: ldp             fp, lr, [SP], #0x10
    // 0xeb0e98: ret
    //     0xeb0e98: ret             
    // 0xeb0e9c: mov             x0, x4
    // 0xeb0ea0: r0 = ConcurrentModificationError()
    //     0xeb0ea0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xeb0ea4: mov             x1, x0
    // 0xeb0ea8: ldur            x0, [fp, #-0x30]
    // 0xeb0eac: StoreField: r1->field_b = r0
    //     0xeb0eac: stur            w0, [x1, #0xb]
    // 0xeb0eb0: mov             x0, x1
    // 0xeb0eb4: r0 = Throw()
    //     0xeb0eb4: bl              #0xec04b8  ; ThrowStub
    // 0xeb0eb8: brk             #0
    // 0xeb0ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0ebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0ec0: b               #0xeb0d98
    // 0xeb0ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0ec4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0ec8: b               #0xeb0e10
  }
}

// class id: 770, size: 0x1c, field offset: 0x18
//   const constructor, 
class WidgetSpan extends InlineSpan {

  _ visitChildren(/* No info */) {
    // ** addr: 0xeb0cf4, size: 0x74
    // 0xeb0cf4: EnterFrame
    //     0xeb0cf4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0cf8: mov             fp, SP
    // 0xeb0cfc: AllocStack(0x30)
    //     0xeb0cfc: sub             SP, SP, #0x30
    // 0xeb0d00: SetupParameters(WidgetSpan this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r1 */)
    //     0xeb0d00: stur            x1, [fp, #-8]
    //     0xeb0d04: mov             x16, x3
    //     0xeb0d08: mov             x3, x1
    //     0xeb0d0c: mov             x1, x16
    //     0xeb0d10: mov             x0, x2
    //     0xeb0d14: stur            x2, [fp, #-0x10]
    // 0xeb0d18: CheckStackOverflow
    //     0xeb0d18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0d1c: cmp             SP, x16
    //     0xeb0d20: b.ls            #0xeb0d60
    // 0xeb0d24: LoadField: r2 = r3->field_7
    //     0xeb0d24: ldur            w2, [x3, #7]
    // 0xeb0d28: DecompressPointer r2
    //     0xeb0d28: add             x2, x2, HEAP, lsl #32
    // 0xeb0d2c: r0 = merge()
    //     0xeb0d2c: bl              #0xb0faa8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::merge
    // 0xeb0d30: ldur            x16, [fp, #-0x10]
    // 0xeb0d34: ldur            lr, [fp, #-8]
    // 0xeb0d38: stp             lr, x16, [SP, #0x10]
    // 0xeb0d3c: stp             NULL, x0, [SP]
    // 0xeb0d40: ldur            x0, [fp, #-0x10]
    // 0xeb0d44: ClosureCall
    //     0xeb0d44: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xeb0d48: ldur            x2, [x0, #0x1f]
    //     0xeb0d4c: blr             x2
    // 0xeb0d50: r0 = true
    //     0xeb0d50: add             x0, NULL, #0x20  ; true
    // 0xeb0d54: LeaveFrame
    //     0xeb0d54: mov             SP, fp
    //     0xeb0d58: ldp             fp, lr, [SP], #0x10
    // 0xeb0d5c: ret
    //     0xeb0d5c: ret             
    // 0xeb0d60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0d60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0d64: b               #0xeb0d24
  }
}

// class id: 771, size: 0x24, field offset: 0x8
class _TextDecoration extends Object {

  _ foregroundPaint(/* No info */) {
    // ** addr: 0xe65474, size: 0x4bc
    // 0xe65474: EnterFrame
    //     0xe65474: stp             fp, lr, [SP, #-0x10]!
    //     0xe65478: mov             fp, SP
    // 0xe6547c: AllocStack(0x78)
    //     0xe6547c: sub             SP, SP, #0x78
    // 0xe65480: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r2 */)
    //     0xe65480: mov             x0, x2
    //     0xe65484: stur            x2, [fp, #-0x18]
    //     0xe65488: mov             x2, x5
    //     0xe6548c: stur            x3, [fp, #-0x20]
    // 0xe65490: CheckStackOverflow
    //     0xe65490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe65494: cmp             SP, x16
    //     0xe65498: b.ls            #0xe65900
    // 0xe6549c: LoadField: r4 = r1->field_7
    //     0xe6549c: ldur            w4, [x1, #7]
    // 0xe654a0: DecompressPointer r4
    //     0xe654a0: add             x4, x4, HEAP, lsl #32
    // 0xe654a4: stur            x4, [fp, #-0x10]
    // 0xe654a8: LoadField: r5 = r4->field_43
    //     0xe654a8: ldur            w5, [x4, #0x43]
    // 0xe654ac: DecompressPointer r5
    //     0xe654ac: add             x5, x5, HEAP, lsl #32
    // 0xe654b0: stur            x5, [fp, #-8]
    // 0xe654b4: cmp             w5, NULL
    // 0xe654b8: b.ne            #0xe654cc
    // 0xe654bc: r0 = Null
    //     0xe654bc: mov             x0, NULL
    // 0xe654c0: LeaveFrame
    //     0xe654c0: mov             SP, fp
    //     0xe654c4: ldp             fp, lr, [SP], #0x10
    // 0xe654c8: ret
    //     0xe654c8: ret             
    // 0xe654cc: r0 = _getBox()
    //     0xe654cc: bl              #0xe67db4  ; [package:pdf/src/widgets/text.dart] _TextDecoration::_getBox
    // 0xe654d0: ldur            x1, [fp, #-0x10]
    // 0xe654d4: stur            x0, [fp, #-0x28]
    // 0xe654d8: r0 = font()
    //     0xe654d8: bl              #0xb0fcfc  ; [package:pdf/src/widgets/text_style.dart] TextStyle::font
    // 0xe654dc: cmp             w0, NULL
    // 0xe654e0: b.eq            #0xe65908
    // 0xe654e4: mov             x1, x0
    // 0xe654e8: ldur            x2, [fp, #-0x18]
    // 0xe654ec: r0 = getFont()
    //     0xe654ec: bl              #0xe65b4c  ; [package:pdf/src/widgets/font.dart] Font::getFont
    // 0xe654f0: mov             x3, x0
    // 0xe654f4: ldur            x0, [fp, #-0x10]
    // 0xe654f8: stur            x3, [fp, #-0x38]
    // 0xe654fc: LoadField: r1 = r0->field_23
    //     0xe654fc: ldur            w1, [x0, #0x23]
    // 0xe65500: DecompressPointer r1
    //     0xe65500: add             x1, x1, HEAP, lsl #32
    // 0xe65504: cmp             w1, NULL
    // 0xe65508: b.eq            #0xe6590c
    // 0xe6550c: LoadField: d0 = r1->field_7
    //     0xe6550c: ldur            d0, [x1, #7]
    // 0xe65510: stur            d0, [fp, #-0x58]
    // 0xe65514: d1 = -0.150000
    //     0xe65514: add             x17, PP, #0x3d, lsl #12  ; [pp+0x3dd80] IMM: double(-0.15) from 0xbfc3333333333333
    //     0xe65518: ldr             d1, [x17, #0xd80]
    // 0xe6551c: fmul            d2, d0, d1
    // 0xe65520: LoadField: r1 = r0->field_4f
    //     0xe65520: ldur            w1, [x0, #0x4f]
    // 0xe65524: DecompressPointer r1
    //     0xe65524: add             x1, x1, HEAP, lsl #32
    // 0xe65528: cmp             w1, NULL
    // 0xe6552c: b.eq            #0xe65910
    // 0xe65530: LoadField: d1 = r1->field_7
    //     0xe65530: ldur            d1, [x1, #7]
    // 0xe65534: stur            d1, [fp, #-0x50]
    // 0xe65538: fmul            d3, d2, d1
    // 0xe6553c: ldur            x1, [fp, #-0x18]
    // 0xe65540: stur            d3, [fp, #-0x48]
    // 0xe65544: LoadField: r4 = r1->field_b
    //     0xe65544: ldur            w4, [x1, #0xb]
    // 0xe65548: DecompressPointer r4
    //     0xe65548: add             x4, x4, HEAP, lsl #32
    // 0xe6554c: stur            x4, [fp, #-0x30]
    // 0xe65550: cmp             w4, NULL
    // 0xe65554: b.eq            #0xe65914
    // 0xe65558: LoadField: r2 = r0->field_b
    //     0xe65558: ldur            w2, [x0, #0xb]
    // 0xe6555c: DecompressPointer r2
    //     0xe6555c: add             x2, x2, HEAP, lsl #32
    // 0xe65560: mov             x1, x4
    // 0xe65564: r0 = setStrokeColor()
    //     0xe65564: bl              #0xe65994  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setStrokeColor
    // 0xe65568: ldur            d1, [fp, #-0x58]
    // 0xe6556c: ldur            d0, [fp, #-0x50]
    // 0xe65570: fmul            d2, d0, d1
    // 0xe65574: d0 = 0.050000
    //     0xe65574: ldr             d0, [PP, #0x5790]  ; [pp+0x5790] IMM: double(0.05) from 0x3fa999999999999a
    // 0xe65578: fmul            d3, d2, d0
    // 0xe6557c: ldur            x1, [fp, #-0x30]
    // 0xe65580: mov             v0.16b, v3.16b
    // 0xe65584: r0 = setLineWidth()
    //     0xe65584: bl              #0xe49358  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setLineWidth
    // 0xe65588: ldur            x0, [fp, #-8]
    // 0xe6558c: LoadField: r2 = r0->field_7
    //     0xe6558c: ldur            x2, [x0, #7]
    // 0xe65590: stur            x2, [fp, #-0x40]
    // 0xe65594: orr             x0, x2, #1
    // 0xe65598: cmp             x0, x2
    // 0xe6559c: b.ne            #0xe656d8
    // 0xe655a0: ldur            x0, [fp, #-0x38]
    // 0xe655a4: r1 = LoadClassIdInstr(r0)
    //     0xe655a4: ldur            x1, [x0, #-1]
    //     0xe655a8: ubfx            x1, x1, #0xc, #0x14
    // 0xe655ac: cmp             x1, #0x37f
    // 0xe655b0: b.ne            #0xe655bc
    // 0xe655b4: LoadField: d0 = r0->field_3b
    //     0xe655b4: ldur            d0, [x0, #0x3b]
    // 0xe655b8: b               #0xe65610
    // 0xe655bc: LoadField: r3 = r0->field_3f
    //     0xe655bc: ldur            w3, [x0, #0x3f]
    // 0xe655c0: DecompressPointer r3
    //     0xe655c0: add             x3, x3, HEAP, lsl #32
    // 0xe655c4: mov             x1, x3
    // 0xe655c8: stur            x3, [fp, #-8]
    // 0xe655cc: r0 = descent()
    //     0xe655cc: bl              #0xc3820c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::descent
    // 0xe655d0: mov             x2, x0
    // 0xe655d4: r0 = BoxInt64Instr(r2)
    //     0xe655d4: sbfiz           x0, x2, #1, #0x1f
    //     0xe655d8: cmp             x2, x0, asr #1
    //     0xe655dc: b.eq            #0xe655e8
    //     0xe655e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe655e4: stur            x2, [x0, #7]
    // 0xe655e8: stp             x0, NULL, [SP]
    // 0xe655ec: r0 = _Double.fromInteger()
    //     0xe655ec: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe655f0: ldur            x1, [fp, #-8]
    // 0xe655f4: stur            x0, [fp, #-8]
    // 0xe655f8: r0 = unitsPerEm()
    //     0xe655f8: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0xe655fc: scvtf           d0, x0
    // 0xe65600: ldur            x0, [fp, #-8]
    // 0xe65604: LoadField: d1 = r0->field_7
    //     0xe65604: ldur            d1, [x0, #7]
    // 0xe65608: fdiv            d2, d1, d0
    // 0xe6560c: mov             v0.16b, v2.16b
    // 0xe65610: ldur            x3, [fp, #-0x20]
    // 0xe65614: ldur            x0, [fp, #-0x10]
    // 0xe65618: ldur            x2, [fp, #-0x28]
    // 0xe6561c: ldur            d4, [fp, #-0x58]
    // 0xe65620: d5 = 2.000000
    //     0xe65620: fmov            d5, #2.00000000
    // 0xe65624: fneg            d1, d0
    // 0xe65628: fmul            d0, d1, d4
    // 0xe6562c: fdiv            d1, d0, d5
    // 0xe65630: cmp             w2, NULL
    // 0xe65634: b.eq            #0xe65918
    // 0xe65638: LoadField: d0 = r2->field_7
    //     0xe65638: ldur            d0, [x2, #7]
    // 0xe6563c: ArrayLoad: d2 = r2[0]  ; List_8
    //     0xe6563c: ldur            d2, [x2, #0x17]
    // 0xe65640: fadd            d3, d0, d2
    // 0xe65644: cmp             w3, NULL
    // 0xe65648: b.eq            #0xe6591c
    // 0xe6564c: LoadField: d2 = r3->field_7
    //     0xe6564c: ldur            d2, [x3, #7]
    // 0xe65650: fadd            d6, d2, d0
    // 0xe65654: stur            d6, [fp, #-0x68]
    // 0xe65658: LoadField: d0 = r3->field_f
    //     0xe65658: ldur            d0, [x3, #0xf]
    // 0xe6565c: LoadField: d7 = r3->field_1f
    //     0xe6565c: ldur            d7, [x3, #0x1f]
    // 0xe65660: fadd            d8, d0, d7
    // 0xe65664: LoadField: d0 = r2->field_f
    //     0xe65664: ldur            d0, [x2, #0xf]
    // 0xe65668: fadd            d7, d8, d0
    // 0xe6566c: fadd            d8, d7, d1
    // 0xe65670: stur            d8, [fp, #-0x60]
    // 0xe65674: fadd            d7, d2, d3
    // 0xe65678: ldur            x1, [fp, #-0x30]
    // 0xe6567c: mov             v0.16b, v6.16b
    // 0xe65680: mov             v1.16b, v8.16b
    // 0xe65684: mov             v2.16b, v7.16b
    // 0xe65688: mov             v3.16b, v8.16b
    // 0xe6568c: stur            d7, [fp, #-0x50]
    // 0xe65690: r0 = drawLine()
    //     0xe65690: bl              #0xe65930  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawLine
    // 0xe65694: ldur            x0, [fp, #-0x10]
    // 0xe65698: LoadField: r1 = r0->field_4b
    //     0xe65698: ldur            w1, [x0, #0x4b]
    // 0xe6569c: DecompressPointer r1
    //     0xe6569c: add             x1, x1, HEAP, lsl #32
    // 0xe656a0: r16 = Instance_TextDecorationStyle
    //     0xe656a0: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dd88] Obj!TextDecorationStyle@e2e4c1
    //     0xe656a4: ldr             x16, [x16, #0xd88]
    // 0xe656a8: cmp             w1, w16
    // 0xe656ac: b.ne            #0xe656d0
    // 0xe656b0: ldur            d4, [fp, #-0x48]
    // 0xe656b4: ldur            d0, [fp, #-0x60]
    // 0xe656b8: fadd            d3, d0, d4
    // 0xe656bc: ldur            x1, [fp, #-0x30]
    // 0xe656c0: ldur            d0, [fp, #-0x68]
    // 0xe656c4: mov             v1.16b, v3.16b
    // 0xe656c8: ldur            d2, [fp, #-0x50]
    // 0xe656cc: r0 = drawLine()
    //     0xe656cc: bl              #0xe65930  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawLine
    // 0xe656d0: ldur            x1, [fp, #-0x30]
    // 0xe656d4: r0 = strokePath()
    //     0xe656d4: bl              #0xe492e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::strokePath
    // 0xe656d8: ldur            x0, [fp, #-0x40]
    // 0xe656dc: orr             x1, x0, #2
    // 0xe656e0: cmp             x1, x0
    // 0xe656e4: b.ne            #0xe657a0
    // 0xe656e8: ldur            x4, [fp, #-0x20]
    // 0xe656ec: ldur            x2, [fp, #-0x10]
    // 0xe656f0: ldur            x3, [fp, #-0x28]
    // 0xe656f4: ldur            d4, [fp, #-0x58]
    // 0xe656f8: cmp             w4, NULL
    // 0xe656fc: b.eq            #0xe65920
    // 0xe65700: LoadField: d0 = r4->field_7
    //     0xe65700: ldur            d0, [x4, #7]
    // 0xe65704: cmp             w3, NULL
    // 0xe65708: b.eq            #0xe65924
    // 0xe6570c: LoadField: d1 = r3->field_7
    //     0xe6570c: ldur            d1, [x3, #7]
    // 0xe65710: fadd            d5, d0, d1
    // 0xe65714: stur            d5, [fp, #-0x68]
    // 0xe65718: LoadField: d2 = r4->field_f
    //     0xe65718: ldur            d2, [x4, #0xf]
    // 0xe6571c: LoadField: d3 = r4->field_1f
    //     0xe6571c: ldur            d3, [x4, #0x1f]
    // 0xe65720: fadd            d6, d2, d3
    // 0xe65724: LoadField: d2 = r3->field_f
    //     0xe65724: ldur            d2, [x3, #0xf]
    // 0xe65728: fadd            d3, d6, d2
    // 0xe6572c: fadd            d6, d3, d4
    // 0xe65730: stur            d6, [fp, #-0x60]
    // 0xe65734: ArrayLoad: d2 = r3[0]  ; List_8
    //     0xe65734: ldur            d2, [x3, #0x17]
    // 0xe65738: fadd            d3, d1, d2
    // 0xe6573c: fadd            d7, d0, d3
    // 0xe65740: ldur            x1, [fp, #-0x30]
    // 0xe65744: mov             v0.16b, v5.16b
    // 0xe65748: mov             v1.16b, v6.16b
    // 0xe6574c: mov             v2.16b, v7.16b
    // 0xe65750: mov             v3.16b, v6.16b
    // 0xe65754: stur            d7, [fp, #-0x50]
    // 0xe65758: r0 = drawLine()
    //     0xe65758: bl              #0xe65930  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawLine
    // 0xe6575c: ldur            x0, [fp, #-0x10]
    // 0xe65760: LoadField: r1 = r0->field_4b
    //     0xe65760: ldur            w1, [x0, #0x4b]
    // 0xe65764: DecompressPointer r1
    //     0xe65764: add             x1, x1, HEAP, lsl #32
    // 0xe65768: r16 = Instance_TextDecorationStyle
    //     0xe65768: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dd88] Obj!TextDecorationStyle@e2e4c1
    //     0xe6576c: ldr             x16, [x16, #0xd88]
    // 0xe65770: cmp             w1, w16
    // 0xe65774: b.ne            #0xe65798
    // 0xe65778: ldur            d4, [fp, #-0x48]
    // 0xe6577c: ldur            d0, [fp, #-0x60]
    // 0xe65780: fsub            d3, d0, d4
    // 0xe65784: ldur            x1, [fp, #-0x30]
    // 0xe65788: ldur            d0, [fp, #-0x68]
    // 0xe6578c: mov             v1.16b, v3.16b
    // 0xe65790: ldur            d2, [fp, #-0x50]
    // 0xe65794: r0 = drawLine()
    //     0xe65794: bl              #0xe65930  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawLine
    // 0xe65798: ldur            x1, [fp, #-0x30]
    // 0xe6579c: r0 = strokePath()
    //     0xe6579c: bl              #0xe492e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::strokePath
    // 0xe657a0: ldur            x0, [fp, #-0x40]
    // 0xe657a4: orr             x1, x0, #4
    // 0xe657a8: cmp             x1, x0
    // 0xe657ac: b.ne            #0xe658f0
    // 0xe657b0: ldur            x0, [fp, #-0x38]
    // 0xe657b4: r1 = LoadClassIdInstr(r0)
    //     0xe657b4: ldur            x1, [x0, #-1]
    //     0xe657b8: ubfx            x1, x1, #0xc, #0x14
    // 0xe657bc: cmp             x1, #0x37f
    // 0xe657c0: b.ne            #0xe657d0
    // 0xe657c4: LoadField: d0 = r0->field_3b
    //     0xe657c4: ldur            d0, [x0, #0x3b]
    // 0xe657c8: mov             v3.16b, v0.16b
    // 0xe657cc: b               #0xe65824
    // 0xe657d0: LoadField: r2 = r0->field_3f
    //     0xe657d0: ldur            w2, [x0, #0x3f]
    // 0xe657d4: DecompressPointer r2
    //     0xe657d4: add             x2, x2, HEAP, lsl #32
    // 0xe657d8: mov             x1, x2
    // 0xe657dc: stur            x2, [fp, #-8]
    // 0xe657e0: r0 = descent()
    //     0xe657e0: bl              #0xc3820c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::descent
    // 0xe657e4: mov             x2, x0
    // 0xe657e8: r0 = BoxInt64Instr(r2)
    //     0xe657e8: sbfiz           x0, x2, #1, #0x1f
    //     0xe657ec: cmp             x2, x0, asr #1
    //     0xe657f0: b.eq            #0xe657fc
    //     0xe657f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe657f8: stur            x2, [x0, #7]
    // 0xe657fc: stp             x0, NULL, [SP]
    // 0xe65800: r0 = _Double.fromInteger()
    //     0xe65800: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe65804: ldur            x1, [fp, #-8]
    // 0xe65808: stur            x0, [fp, #-8]
    // 0xe6580c: r0 = unitsPerEm()
    //     0xe6580c: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0xe65810: scvtf           d0, x0
    // 0xe65814: ldur            x0, [fp, #-8]
    // 0xe65818: LoadField: d1 = r0->field_7
    //     0xe65818: ldur            d1, [x0, #7]
    // 0xe6581c: fdiv            d2, d1, d0
    // 0xe65820: mov             v3.16b, v2.16b
    // 0xe65824: ldur            x2, [fp, #-0x20]
    // 0xe65828: ldur            x0, [fp, #-0x10]
    // 0xe6582c: ldur            x1, [fp, #-0x28]
    // 0xe65830: ldur            d0, [fp, #-0x58]
    // 0xe65834: d1 = 2.000000
    //     0xe65834: fmov            d1, #2.00000000
    // 0xe65838: d2 = 1.000000
    //     0xe65838: fmov            d2, #1.00000000
    // 0xe6583c: fsub            d4, d2, d3
    // 0xe65840: fmul            d2, d4, d0
    // 0xe65844: fdiv            d0, d2, d1
    // 0xe65848: cmp             w2, NULL
    // 0xe6584c: b.eq            #0xe65928
    // 0xe65850: LoadField: d1 = r2->field_7
    //     0xe65850: ldur            d1, [x2, #7]
    // 0xe65854: cmp             w1, NULL
    // 0xe65858: b.eq            #0xe6592c
    // 0xe6585c: LoadField: d2 = r1->field_7
    //     0xe6585c: ldur            d2, [x1, #7]
    // 0xe65860: fadd            d4, d1, d2
    // 0xe65864: stur            d4, [fp, #-0x60]
    // 0xe65868: LoadField: d3 = r2->field_f
    //     0xe65868: ldur            d3, [x2, #0xf]
    // 0xe6586c: LoadField: d5 = r2->field_1f
    //     0xe6586c: ldur            d5, [x2, #0x1f]
    // 0xe65870: fadd            d6, d3, d5
    // 0xe65874: LoadField: d3 = r1->field_f
    //     0xe65874: ldur            d3, [x1, #0xf]
    // 0xe65878: fadd            d5, d6, d3
    // 0xe6587c: fadd            d6, d5, d0
    // 0xe65880: stur            d6, [fp, #-0x58]
    // 0xe65884: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe65884: ldur            d0, [x1, #0x17]
    // 0xe65888: fadd            d3, d2, d0
    // 0xe6588c: fadd            d5, d1, d3
    // 0xe65890: ldur            x1, [fp, #-0x30]
    // 0xe65894: mov             v0.16b, v4.16b
    // 0xe65898: mov             v1.16b, v6.16b
    // 0xe6589c: mov             v2.16b, v5.16b
    // 0xe658a0: mov             v3.16b, v6.16b
    // 0xe658a4: stur            d5, [fp, #-0x50]
    // 0xe658a8: r0 = drawLine()
    //     0xe658a8: bl              #0xe65930  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawLine
    // 0xe658ac: ldur            x0, [fp, #-0x10]
    // 0xe658b0: LoadField: r1 = r0->field_4b
    //     0xe658b0: ldur            w1, [x0, #0x4b]
    // 0xe658b4: DecompressPointer r1
    //     0xe658b4: add             x1, x1, HEAP, lsl #32
    // 0xe658b8: r16 = Instance_TextDecorationStyle
    //     0xe658b8: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dd88] Obj!TextDecorationStyle@e2e4c1
    //     0xe658bc: ldr             x16, [x16, #0xd88]
    // 0xe658c0: cmp             w1, w16
    // 0xe658c4: b.ne            #0xe658e8
    // 0xe658c8: ldur            d1, [fp, #-0x48]
    // 0xe658cc: ldur            d0, [fp, #-0x58]
    // 0xe658d0: fadd            d3, d0, d1
    // 0xe658d4: ldur            x1, [fp, #-0x30]
    // 0xe658d8: ldur            d0, [fp, #-0x60]
    // 0xe658dc: mov             v1.16b, v3.16b
    // 0xe658e0: ldur            d2, [fp, #-0x50]
    // 0xe658e4: r0 = drawLine()
    //     0xe658e4: bl              #0xe65930  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawLine
    // 0xe658e8: ldur            x1, [fp, #-0x30]
    // 0xe658ec: r0 = strokePath()
    //     0xe658ec: bl              #0xe492e8  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::strokePath
    // 0xe658f0: r0 = Null
    //     0xe658f0: mov             x0, NULL
    // 0xe658f4: LeaveFrame
    //     0xe658f4: mov             SP, fp
    //     0xe658f8: ldp             fp, lr, [SP], #0x10
    // 0xe658fc: ret
    //     0xe658fc: ret             
    // 0xe65900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe65900: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe65904: b               #0xe6549c
    // 0xe65908: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe65908: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6590c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6590c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe65910: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe65910: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe65914: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe65914: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe65918: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe65918: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe6591c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe6591c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe65920: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe65920: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe65924: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe65924: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe65928: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe65928: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe6592c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe6592c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _getBox(/* No info */) {
    // ** addr: 0xe67db4, size: 0x850
    // 0xe67db4: EnterFrame
    //     0xe67db4: stp             fp, lr, [SP, #-0x10]!
    //     0xe67db8: mov             fp, SP
    // 0xe67dbc: AllocStack(0x80)
    //     0xe67dbc: sub             SP, SP, #0x80
    // 0xe67dc0: SetupParameters(_TextDecoration this /* r1 => r3, fp-0x30 */, dynamic _ /* r2 => r2, fp-0x38 */)
    //     0xe67dc0: mov             x3, x1
    //     0xe67dc4: stur            x1, [fp, #-0x30]
    //     0xe67dc8: stur            x2, [fp, #-0x38]
    // 0xe67dcc: CheckStackOverflow
    //     0xe67dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe67dd0: cmp             SP, x16
    //     0xe67dd4: b.ls            #0xe68534
    // 0xe67dd8: LoadField: r0 = r3->field_1f
    //     0xe67dd8: ldur            w0, [x3, #0x1f]
    // 0xe67ddc: DecompressPointer r0
    //     0xe67ddc: add             x0, x0, HEAP, lsl #32
    // 0xe67de0: cmp             w0, NULL
    // 0xe67de4: b.eq            #0xe67df4
    // 0xe67de8: LeaveFrame
    //     0xe67de8: mov             SP, fp
    //     0xe67dec: ldp             fp, lr, [SP], #0x10
    // 0xe67df0: ret
    //     0xe67df0: ret             
    // 0xe67df4: LoadField: r4 = r3->field_f
    //     0xe67df4: ldur            x4, [x3, #0xf]
    // 0xe67df8: stur            x4, [fp, #-0x28]
    // 0xe67dfc: LoadField: r0 = r2->field_b
    //     0xe67dfc: ldur            w0, [x2, #0xb]
    // 0xe67e00: r5 = LoadInt32Instr(r0)
    //     0xe67e00: sbfx            x5, x0, #1, #0x1f
    // 0xe67e04: mov             x0, x5
    // 0xe67e08: mov             x1, x4
    // 0xe67e0c: stur            x5, [fp, #-0x20]
    // 0xe67e10: cmp             x1, x0
    // 0xe67e14: b.hs            #0xe6853c
    // 0xe67e18: LoadField: r0 = r2->field_f
    //     0xe67e18: ldur            w0, [x2, #0xf]
    // 0xe67e1c: DecompressPointer r0
    //     0xe67e1c: add             x0, x0, HEAP, lsl #32
    // 0xe67e20: stur            x0, [fp, #-0x18]
    // 0xe67e24: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xe67e24: add             x16, x0, x4, lsl #2
    //     0xe67e28: ldur            w1, [x16, #0xf]
    // 0xe67e2c: DecompressPointer r1
    //     0xe67e2c: add             x1, x1, HEAP, lsl #32
    // 0xe67e30: stur            x1, [fp, #-0x10]
    // 0xe67e34: r6 = LoadClassIdInstr(r1)
    //     0xe67e34: ldur            x6, [x1, #-1]
    //     0xe67e38: ubfx            x6, x6, #0xc, #0x14
    // 0xe67e3c: stur            x6, [fp, #-8]
    // 0xe67e40: cmp             x6, #0x305
    // 0xe67e44: b.ne            #0xe67e8c
    // 0xe67e48: LoadField: r7 = r1->field_f
    //     0xe67e48: ldur            w7, [x1, #0xf]
    // 0xe67e4c: DecompressPointer r7
    //     0xe67e4c: add             x7, x7, HEAP, lsl #32
    // 0xe67e50: LoadField: r8 = r7->field_7
    //     0xe67e50: ldur            w8, [x7, #7]
    // 0xe67e54: DecompressPointer r8
    //     0xe67e54: add             x8, x8, HEAP, lsl #32
    // 0xe67e58: cmp             w8, NULL
    // 0xe67e5c: b.eq            #0xe68540
    // 0xe67e60: LoadField: d0 = r8->field_7
    //     0xe67e60: ldur            d0, [x8, #7]
    // 0xe67e64: stur            d0, [fp, #-0x60]
    // 0xe67e68: LoadField: d1 = r8->field_f
    //     0xe67e68: ldur            d1, [x8, #0xf]
    // 0xe67e6c: stur            d1, [fp, #-0x58]
    // 0xe67e70: r0 = PdfPoint()
    //     0xe67e70: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe67e74: ldur            d0, [fp, #-0x60]
    // 0xe67e78: StoreField: r0->field_7 = d0
    //     0xe67e78: stur            d0, [x0, #7]
    // 0xe67e7c: ldur            d0, [fp, #-0x58]
    // 0xe67e80: StoreField: r0->field_f = d0
    //     0xe67e80: stur            d0, [x0, #0xf]
    // 0xe67e84: ldur            x2, [fp, #-0x10]
    // 0xe67e88: b               #0xe67e98
    // 0xe67e8c: mov             x2, x1
    // 0xe67e90: LoadField: r0 = r2->field_b
    //     0xe67e90: ldur            w0, [x2, #0xb]
    // 0xe67e94: DecompressPointer r0
    //     0xe67e94: add             x0, x0, HEAP, lsl #32
    // 0xe67e98: ldur            x3, [fp, #-8]
    // 0xe67e9c: LoadField: d0 = r0->field_7
    //     0xe67e9c: ldur            d0, [x0, #7]
    // 0xe67ea0: cmp             x3, #0x305
    // 0xe67ea4: b.ne            #0xe67eb0
    // 0xe67ea8: d1 = 0.000000
    //     0xe67ea8: eor             v1.16b, v1.16b, v1.16b
    // 0xe67eac: b               #0xe67ebc
    // 0xe67eb0: LoadField: r0 = r2->field_13
    //     0xe67eb0: ldur            w0, [x2, #0x13]
    // 0xe67eb4: DecompressPointer r0
    //     0xe67eb4: add             x0, x0, HEAP, lsl #32
    // 0xe67eb8: LoadField: d1 = r0->field_7
    //     0xe67eb8: ldur            d1, [x0, #7]
    // 0xe67ebc: ldur            x4, [fp, #-0x30]
    // 0xe67ec0: ldur            x5, [fp, #-0x18]
    // 0xe67ec4: fadd            d2, d0, d1
    // 0xe67ec8: stur            d2, [fp, #-0x68]
    // 0xe67ecc: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xe67ecc: ldur            x6, [x4, #0x17]
    // 0xe67ed0: ldur            x0, [fp, #-0x20]
    // 0xe67ed4: mov             x1, x6
    // 0xe67ed8: stur            x6, [fp, #-0x48]
    // 0xe67edc: cmp             x1, x0
    // 0xe67ee0: b.hs            #0xe68544
    // 0xe67ee4: ArrayLoad: r0 = r5[r6]  ; Unknown_4
    //     0xe67ee4: add             x16, x5, x6, lsl #2
    //     0xe67ee8: ldur            w0, [x16, #0xf]
    // 0xe67eec: DecompressPointer r0
    //     0xe67eec: add             x0, x0, HEAP, lsl #32
    // 0xe67ef0: stur            x0, [fp, #-0x40]
    // 0xe67ef4: r1 = LoadClassIdInstr(r0)
    //     0xe67ef4: ldur            x1, [x0, #-1]
    //     0xe67ef8: ubfx            x1, x1, #0xc, #0x14
    // 0xe67efc: stur            x1, [fp, #-0x20]
    // 0xe67f00: cmp             x1, #0x305
    // 0xe67f04: b.ne            #0xe67f50
    // 0xe67f08: LoadField: r5 = r0->field_f
    //     0xe67f08: ldur            w5, [x0, #0xf]
    // 0xe67f0c: DecompressPointer r5
    //     0xe67f0c: add             x5, x5, HEAP, lsl #32
    // 0xe67f10: LoadField: r7 = r5->field_7
    //     0xe67f10: ldur            w7, [x5, #7]
    // 0xe67f14: DecompressPointer r7
    //     0xe67f14: add             x7, x7, HEAP, lsl #32
    // 0xe67f18: cmp             w7, NULL
    // 0xe67f1c: b.eq            #0xe68548
    // 0xe67f20: LoadField: d0 = r7->field_7
    //     0xe67f20: ldur            d0, [x7, #7]
    // 0xe67f24: stur            d0, [fp, #-0x60]
    // 0xe67f28: LoadField: d1 = r7->field_f
    //     0xe67f28: ldur            d1, [x7, #0xf]
    // 0xe67f2c: stur            d1, [fp, #-0x58]
    // 0xe67f30: r0 = PdfPoint()
    //     0xe67f30: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe67f34: ldur            d0, [fp, #-0x60]
    // 0xe67f38: StoreField: r0->field_7 = d0
    //     0xe67f38: stur            d0, [x0, #7]
    // 0xe67f3c: ldur            d0, [fp, #-0x58]
    // 0xe67f40: StoreField: r0->field_f = d0
    //     0xe67f40: stur            d0, [x0, #0xf]
    // 0xe67f44: mov             x2, x0
    // 0xe67f48: ldur            x0, [fp, #-0x40]
    // 0xe67f4c: b               #0xe67f5c
    // 0xe67f50: LoadField: r1 = r0->field_b
    //     0xe67f50: ldur            w1, [x0, #0xb]
    // 0xe67f54: DecompressPointer r1
    //     0xe67f54: add             x1, x1, HEAP, lsl #32
    // 0xe67f58: mov             x2, x1
    // 0xe67f5c: ldur            x1, [fp, #-0x20]
    // 0xe67f60: LoadField: d0 = r2->field_7
    //     0xe67f60: ldur            d0, [x2, #7]
    // 0xe67f64: cmp             x1, #0x305
    // 0xe67f68: b.ne            #0xe67f74
    // 0xe67f6c: d1 = 0.000000
    //     0xe67f6c: eor             v1.16b, v1.16b, v1.16b
    // 0xe67f70: b               #0xe67f80
    // 0xe67f74: LoadField: r2 = r0->field_13
    //     0xe67f74: ldur            w2, [x0, #0x13]
    // 0xe67f78: DecompressPointer r2
    //     0xe67f78: add             x2, x2, HEAP, lsl #32
    // 0xe67f7c: LoadField: d1 = r2->field_7
    //     0xe67f7c: ldur            d1, [x2, #7]
    // 0xe67f80: fadd            d2, d0, d1
    // 0xe67f84: cmp             x1, #0x305
    // 0xe67f88: b.ne            #0xe67fac
    // 0xe67f8c: LoadField: r1 = r0->field_f
    //     0xe67f8c: ldur            w1, [x0, #0xf]
    // 0xe67f90: DecompressPointer r1
    //     0xe67f90: add             x1, x1, HEAP, lsl #32
    // 0xe67f94: LoadField: r0 = r1->field_7
    //     0xe67f94: ldur            w0, [x1, #7]
    // 0xe67f98: DecompressPointer r0
    //     0xe67f98: add             x0, x0, HEAP, lsl #32
    // 0xe67f9c: cmp             w0, NULL
    // 0xe67fa0: b.eq            #0xe6854c
    // 0xe67fa4: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe67fa4: ldur            d0, [x0, #0x17]
    // 0xe67fa8: b               #0xe67fc4
    // 0xe67fac: LoadField: r1 = r0->field_13
    //     0xe67fac: ldur            w1, [x0, #0x13]
    // 0xe67fb0: DecompressPointer r1
    //     0xe67fb0: add             x1, x1, HEAP, lsl #32
    // 0xe67fb4: LoadField: d0 = r1->field_1f
    //     0xe67fb4: ldur            d0, [x1, #0x1f]
    // 0xe67fb8: LoadField: d1 = r1->field_7
    //     0xe67fb8: ldur            d1, [x1, #7]
    // 0xe67fbc: fsub            d3, d0, d1
    // 0xe67fc0: mov             v0.16b, v3.16b
    // 0xe67fc4: ldur            x0, [fp, #-8]
    // 0xe67fc8: fadd            d1, d2, d0
    // 0xe67fcc: stur            d1, [fp, #-0x70]
    // 0xe67fd0: cmp             x0, #0x305
    // 0xe67fd4: b.ne            #0xe68024
    // 0xe67fd8: ldur            x1, [fp, #-0x10]
    // 0xe67fdc: LoadField: r2 = r1->field_f
    //     0xe67fdc: ldur            w2, [x1, #0xf]
    // 0xe67fe0: DecompressPointer r2
    //     0xe67fe0: add             x2, x2, HEAP, lsl #32
    // 0xe67fe4: LoadField: r3 = r2->field_7
    //     0xe67fe4: ldur            w3, [x2, #7]
    // 0xe67fe8: DecompressPointer r3
    //     0xe67fe8: add             x3, x3, HEAP, lsl #32
    // 0xe67fec: cmp             w3, NULL
    // 0xe67ff0: b.eq            #0xe68550
    // 0xe67ff4: LoadField: d0 = r3->field_7
    //     0xe67ff4: ldur            d0, [x3, #7]
    // 0xe67ff8: stur            d0, [fp, #-0x60]
    // 0xe67ffc: LoadField: d2 = r3->field_f
    //     0xe67ffc: ldur            d2, [x3, #0xf]
    // 0xe68000: stur            d2, [fp, #-0x58]
    // 0xe68004: r0 = PdfPoint()
    //     0xe68004: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe68008: ldur            d0, [fp, #-0x60]
    // 0xe6800c: StoreField: r0->field_7 = d0
    //     0xe6800c: stur            d0, [x0, #7]
    // 0xe68010: ldur            d0, [fp, #-0x58]
    // 0xe68014: StoreField: r0->field_f = d0
    //     0xe68014: stur            d0, [x0, #0xf]
    // 0xe68018: mov             x2, x0
    // 0xe6801c: ldur            x0, [fp, #-0x10]
    // 0xe68020: b               #0xe68034
    // 0xe68024: ldur            x0, [fp, #-0x10]
    // 0xe68028: LoadField: r1 = r0->field_b
    //     0xe68028: ldur            w1, [x0, #0xb]
    // 0xe6802c: DecompressPointer r1
    //     0xe6802c: add             x1, x1, HEAP, lsl #32
    // 0xe68030: mov             x2, x1
    // 0xe68034: ldur            x1, [fp, #-8]
    // 0xe68038: LoadField: d0 = r2->field_f
    //     0xe68038: ldur            d0, [x2, #0xf]
    // 0xe6803c: cmp             x1, #0x305
    // 0xe68040: b.ne            #0xe6804c
    // 0xe68044: d1 = 0.000000
    //     0xe68044: eor             v1.16b, v1.16b, v1.16b
    // 0xe68048: b               #0xe68058
    // 0xe6804c: LoadField: r2 = r0->field_13
    //     0xe6804c: ldur            w2, [x0, #0x13]
    // 0xe68050: DecompressPointer r2
    //     0xe68050: add             x2, x2, HEAP, lsl #32
    // 0xe68054: LoadField: d1 = r2->field_2f
    //     0xe68054: ldur            d1, [x2, #0x2f]
    // 0xe68058: fadd            d2, d0, d1
    // 0xe6805c: cmp             x1, #0x305
    // 0xe68060: b.ne            #0xe68084
    // 0xe68064: LoadField: r1 = r0->field_f
    //     0xe68064: ldur            w1, [x0, #0xf]
    // 0xe68068: DecompressPointer r1
    //     0xe68068: add             x1, x1, HEAP, lsl #32
    // 0xe6806c: LoadField: r0 = r1->field_7
    //     0xe6806c: ldur            w0, [x1, #7]
    // 0xe68070: DecompressPointer r0
    //     0xe68070: add             x0, x0, HEAP, lsl #32
    // 0xe68074: cmp             w0, NULL
    // 0xe68078: b.eq            #0xe68554
    // 0xe6807c: LoadField: d0 = r0->field_1f
    //     0xe6807c: ldur            d0, [x0, #0x1f]
    // 0xe68080: b               #0xe6809c
    // 0xe68084: LoadField: r1 = r0->field_13
    //     0xe68084: ldur            w1, [x0, #0x13]
    // 0xe68088: DecompressPointer r1
    //     0xe68088: add             x1, x1, HEAP, lsl #32
    // 0xe6808c: LoadField: d0 = r1->field_27
    //     0xe6808c: ldur            d0, [x1, #0x27]
    // 0xe68090: LoadField: d1 = r1->field_2f
    //     0xe68090: ldur            d1, [x1, #0x2f]
    // 0xe68094: fsub            d3, d0, d1
    // 0xe68098: mov             v0.16b, v3.16b
    // 0xe6809c: ldur            x0, [fp, #-0x28]
    // 0xe680a0: fadd            d1, d2, d0
    // 0xe680a4: add             x1, x0, #1
    // 0xe680a8: r0 = inline_Allocate_Double()
    //     0xe680a8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe680ac: add             x0, x0, #0x10
    //     0xe680b0: cmp             x2, x0
    //     0xe680b4: b.ls            #0xe68558
    //     0xe680b8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe680bc: sub             x0, x0, #0xf
    //     0xe680c0: movz            x2, #0xe15c
    //     0xe680c4: movk            x2, #0x3, lsl #16
    //     0xe680c8: stur            x2, [x0, #-1]
    // 0xe680cc: StoreField: r0->field_7 = d2
    //     0xe680cc: stur            d2, [x0, #7]
    // 0xe680d0: r2 = inline_Allocate_Double()
    //     0xe680d0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xe680d4: add             x2, x2, #0x10
    //     0xe680d8: cmp             x3, x2
    //     0xe680dc: b.ls            #0xe68570
    //     0xe680e0: str             x2, [THR, #0x50]  ; THR::top
    //     0xe680e4: sub             x2, x2, #0xf
    //     0xe680e8: movz            x3, #0xe15c
    //     0xe680ec: movk            x3, #0x3, lsl #16
    //     0xe680f0: stur            x3, [x2, #-1]
    // 0xe680f4: StoreField: r2->field_7 = d1
    //     0xe680f4: stur            d1, [x2, #7]
    // 0xe680f8: mov             x6, x0
    // 0xe680fc: mov             x5, x2
    // 0xe68100: mov             x4, x1
    // 0xe68104: ldur            x3, [fp, #-0x38]
    // 0xe68108: ldur            x2, [fp, #-0x48]
    // 0xe6810c: stur            x6, [fp, #-0x10]
    // 0xe68110: stur            x5, [fp, #-0x18]
    // 0xe68114: stur            x4, [fp, #-8]
    // 0xe68118: CheckStackOverflow
    //     0xe68118: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6811c: cmp             SP, x16
    //     0xe68120: b.ls            #0xe6858c
    // 0xe68124: cmp             x4, x2
    // 0xe68128: b.gt            #0xe684dc
    // 0xe6812c: LoadField: r0 = r3->field_b
    //     0xe6812c: ldur            w0, [x3, #0xb]
    // 0xe68130: r1 = LoadInt32Instr(r0)
    //     0xe68130: sbfx            x1, x0, #1, #0x1f
    // 0xe68134: mov             x0, x1
    // 0xe68138: mov             x1, x4
    // 0xe6813c: cmp             x1, x0
    // 0xe68140: b.hs            #0xe68594
    // 0xe68144: LoadField: r0 = r3->field_f
    //     0xe68144: ldur            w0, [x3, #0xf]
    // 0xe68148: DecompressPointer r0
    //     0xe68148: add             x0, x0, HEAP, lsl #32
    // 0xe6814c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xe6814c: add             x16, x0, x4, lsl #2
    //     0xe68150: ldur            w1, [x16, #0xf]
    // 0xe68154: DecompressPointer r1
    //     0xe68154: add             x1, x1, HEAP, lsl #32
    // 0xe68158: r0 = LoadClassIdInstr(r1)
    //     0xe68158: ldur            x0, [x1, #-1]
    //     0xe6815c: ubfx            x0, x0, #0xc, #0x14
    // 0xe68160: cmp             x0, #0x305
    // 0xe68164: b.ne            #0xe68188
    // 0xe68168: LoadField: r0 = r1->field_f
    //     0xe68168: ldur            w0, [x1, #0xf]
    // 0xe6816c: DecompressPointer r0
    //     0xe6816c: add             x0, x0, HEAP, lsl #32
    // 0xe68170: LoadField: r1 = r0->field_7
    //     0xe68170: ldur            w1, [x0, #7]
    // 0xe68174: DecompressPointer r1
    //     0xe68174: add             x1, x1, HEAP, lsl #32
    // 0xe68178: cmp             w1, NULL
    // 0xe6817c: b.eq            #0xe68598
    // 0xe68180: r0 = offset()
    //     0xe68180: bl              #0xc3adb0  ; [package:pdf/src/pdf/rect.dart] PdfRect::offset
    // 0xe68184: b               #0xe68190
    // 0xe68188: LoadField: r0 = r1->field_b
    //     0xe68188: ldur            w0, [x1, #0xb]
    // 0xe6818c: DecompressPointer r0
    //     0xe6818c: add             x0, x0, HEAP, lsl #32
    // 0xe68190: ldur            x2, [fp, #-0x38]
    // 0xe68194: ldur            x3, [fp, #-8]
    // 0xe68198: LoadField: d0 = r0->field_f
    //     0xe68198: ldur            d0, [x0, #0xf]
    // 0xe6819c: LoadField: r0 = r2->field_b
    //     0xe6819c: ldur            w0, [x2, #0xb]
    // 0xe681a0: r1 = LoadInt32Instr(r0)
    //     0xe681a0: sbfx            x1, x0, #1, #0x1f
    // 0xe681a4: mov             x0, x1
    // 0xe681a8: mov             x1, x3
    // 0xe681ac: cmp             x1, x0
    // 0xe681b0: b.hs            #0xe6859c
    // 0xe681b4: LoadField: r0 = r2->field_f
    //     0xe681b4: ldur            w0, [x2, #0xf]
    // 0xe681b8: DecompressPointer r0
    //     0xe681b8: add             x0, x0, HEAP, lsl #32
    // 0xe681bc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xe681bc: add             x16, x0, x3, lsl #2
    //     0xe681c0: ldur            w1, [x16, #0xf]
    // 0xe681c4: DecompressPointer r1
    //     0xe681c4: add             x1, x1, HEAP, lsl #32
    // 0xe681c8: r0 = LoadClassIdInstr(r1)
    //     0xe681c8: ldur            x0, [x1, #-1]
    //     0xe681cc: ubfx            x0, x0, #0xc, #0x14
    // 0xe681d0: cmp             x0, #0x305
    // 0xe681d4: b.ne            #0xe681e0
    // 0xe681d8: d1 = 0.000000
    //     0xe681d8: eor             v1.16b, v1.16b, v1.16b
    // 0xe681dc: b               #0xe681ec
    // 0xe681e0: LoadField: r4 = r1->field_13
    //     0xe681e0: ldur            w4, [x1, #0x13]
    // 0xe681e4: DecompressPointer r4
    //     0xe681e4: add             x4, x4, HEAP, lsl #32
    // 0xe681e8: LoadField: d1 = r4->field_2f
    //     0xe681e8: ldur            d1, [x4, #0x2f]
    // 0xe681ec: fadd            d2, d0, d1
    // 0xe681f0: stur            d2, [fp, #-0x60]
    // 0xe681f4: cmp             x0, #0x305
    // 0xe681f8: b.ne            #0xe6821c
    // 0xe681fc: LoadField: r0 = r1->field_f
    //     0xe681fc: ldur            w0, [x1, #0xf]
    // 0xe68200: DecompressPointer r0
    //     0xe68200: add             x0, x0, HEAP, lsl #32
    // 0xe68204: LoadField: r1 = r0->field_7
    //     0xe68204: ldur            w1, [x0, #7]
    // 0xe68208: DecompressPointer r1
    //     0xe68208: add             x1, x1, HEAP, lsl #32
    // 0xe6820c: cmp             w1, NULL
    // 0xe68210: b.eq            #0xe685a0
    // 0xe68214: LoadField: d0 = r1->field_1f
    //     0xe68214: ldur            d0, [x1, #0x1f]
    // 0xe68218: b               #0xe68234
    // 0xe6821c: LoadField: r0 = r1->field_13
    //     0xe6821c: ldur            w0, [x1, #0x13]
    // 0xe68220: DecompressPointer r0
    //     0xe68220: add             x0, x0, HEAP, lsl #32
    // 0xe68224: LoadField: d0 = r0->field_27
    //     0xe68224: ldur            d0, [x0, #0x27]
    // 0xe68228: LoadField: d1 = r0->field_2f
    //     0xe68228: ldur            d1, [x0, #0x2f]
    // 0xe6822c: fsub            d3, d0, d1
    // 0xe68230: mov             v0.16b, v3.16b
    // 0xe68234: ldur            x1, [fp, #-0x10]
    // 0xe68238: fadd            d1, d2, d0
    // 0xe6823c: stur            d1, [fp, #-0x58]
    // 0xe68240: r4 = inline_Allocate_Double()
    //     0xe68240: ldp             x4, x0, [THR, #0x50]  ; THR::top
    //     0xe68244: add             x4, x4, #0x10
    //     0xe68248: cmp             x0, x4
    //     0xe6824c: b.ls            #0xe685a4
    //     0xe68250: str             x4, [THR, #0x50]  ; THR::top
    //     0xe68254: sub             x4, x4, #0xf
    //     0xe68258: movz            x0, #0xe15c
    //     0xe6825c: movk            x0, #0x3, lsl #16
    //     0xe68260: stur            x0, [x4, #-1]
    // 0xe68264: StoreField: r4->field_7 = d2
    //     0xe68264: stur            d2, [x4, #7]
    // 0xe68268: stur            x4, [fp, #-0x40]
    // 0xe6826c: r0 = 60
    //     0xe6826c: movz            x0, #0x3c
    // 0xe68270: branchIfSmi(r1, 0xe6827c)
    //     0xe68270: tbz             w1, #0, #0xe6827c
    // 0xe68274: r0 = LoadClassIdInstr(r1)
    //     0xe68274: ldur            x0, [x1, #-1]
    //     0xe68278: ubfx            x0, x0, #0xc, #0x14
    // 0xe6827c: stp             x4, x1, [SP]
    // 0xe68280: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xe68280: sub             lr, x0, #0xfe3
    //     0xe68284: ldr             lr, [x21, lr, lsl #3]
    //     0xe68288: blr             lr
    // 0xe6828c: tbnz            w0, #4, #0xe68298
    // 0xe68290: ldur            x6, [fp, #-0x40]
    // 0xe68294: b               #0xe683a4
    // 0xe68298: ldur            x1, [fp, #-0x10]
    // 0xe6829c: r0 = 60
    //     0xe6829c: movz            x0, #0x3c
    // 0xe682a0: branchIfSmi(r1, 0xe682ac)
    //     0xe682a0: tbz             w1, #0, #0xe682ac
    // 0xe682a4: r0 = LoadClassIdInstr(r1)
    //     0xe682a4: ldur            x0, [x1, #-1]
    //     0xe682a8: ubfx            x0, x0, #0xc, #0x14
    // 0xe682ac: ldur            x16, [fp, #-0x40]
    // 0xe682b0: stp             x16, x1, [SP]
    // 0xe682b4: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xe682b4: sub             lr, x0, #0xfd2
    //     0xe682b8: ldr             lr, [x21, lr, lsl #3]
    //     0xe682bc: blr             lr
    // 0xe682c0: tbnz            w0, #4, #0xe682cc
    // 0xe682c4: ldur            x6, [fp, #-0x10]
    // 0xe682c8: b               #0xe683a4
    // 0xe682cc: ldur            x1, [fp, #-0x10]
    // 0xe682d0: r0 = 60
    //     0xe682d0: movz            x0, #0x3c
    // 0xe682d4: branchIfSmi(r1, 0xe682e0)
    //     0xe682d4: tbz             w1, #0, #0xe682e0
    // 0xe682d8: r0 = LoadClassIdInstr(r1)
    //     0xe682d8: ldur            x0, [x1, #-1]
    //     0xe682dc: ubfx            x0, x0, #0xc, #0x14
    // 0xe682e0: cmp             x0, #0x3e
    // 0xe682e4: b.ne            #0xe68340
    // 0xe682e8: d0 = 0.000000
    //     0xe682e8: eor             v0.16b, v0.16b, v0.16b
    // 0xe682ec: LoadField: d1 = r1->field_7
    //     0xe682ec: ldur            d1, [x1, #7]
    // 0xe682f0: fcmp            d1, d0
    // 0xe682f4: b.ne            #0xe68338
    // 0xe682f8: ldur            d2, [fp, #-0x60]
    // 0xe682fc: fadd            d3, d1, d2
    // 0xe68300: fmul            d4, d3, d1
    // 0xe68304: fmul            d1, d4, d2
    // 0xe68308: r0 = inline_Allocate_Double()
    //     0xe68308: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6830c: add             x0, x0, #0x10
    //     0xe68310: cmp             x1, x0
    //     0xe68314: b.ls            #0xe685c8
    //     0xe68318: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6831c: sub             x0, x0, #0xf
    //     0xe68320: movz            x1, #0xe15c
    //     0xe68324: movk            x1, #0x3, lsl #16
    //     0xe68328: stur            x1, [x0, #-1]
    // 0xe6832c: StoreField: r0->field_7 = d1
    //     0xe6832c: stur            d1, [x0, #7]
    // 0xe68330: mov             x6, x0
    // 0xe68334: b               #0xe683a4
    // 0xe68338: ldur            d2, [fp, #-0x60]
    // 0xe6833c: b               #0xe68348
    // 0xe68340: ldur            d2, [fp, #-0x60]
    // 0xe68344: d0 = 0.000000
    //     0xe68344: eor             v0.16b, v0.16b, v0.16b
    // 0xe68348: r0 = 60
    //     0xe68348: movz            x0, #0x3c
    // 0xe6834c: branchIfSmi(r1, 0xe68358)
    //     0xe6834c: tbz             w1, #0, #0xe68358
    // 0xe68350: r0 = LoadClassIdInstr(r1)
    //     0xe68350: ldur            x0, [x1, #-1]
    //     0xe68354: ubfx            x0, x0, #0xc, #0x14
    // 0xe68358: stp             xzr, x1, [SP]
    // 0xe6835c: mov             lr, x0
    // 0xe68360: ldr             lr, [x21, lr, lsl #3]
    // 0xe68364: blr             lr
    // 0xe68368: tbnz            w0, #4, #0xe6838c
    // 0xe6836c: ldur            d0, [fp, #-0x60]
    // 0xe68370: fcmp            d0, #0.0
    // 0xe68374: b.vs            #0xe68390
    // 0xe68378: b.ne            #0xe68384
    // 0xe6837c: r0 = 0.000000
    //     0xe6837c: fmov            x0, d0
    // 0xe68380: cmp             x0, #0
    // 0xe68384: b.ge            #0xe68390
    // 0xe68388: b               #0xe68398
    // 0xe6838c: ldur            d0, [fp, #-0x60]
    // 0xe68390: fcmp            d0, d0
    // 0xe68394: b.vc            #0xe683a0
    // 0xe68398: ldur            x6, [fp, #-0x40]
    // 0xe6839c: b               #0xe683a4
    // 0xe683a0: ldur            x6, [fp, #-0x10]
    // 0xe683a4: ldur            x1, [fp, #-0x18]
    // 0xe683a8: ldur            d0, [fp, #-0x58]
    // 0xe683ac: stur            x6, [fp, #-0x50]
    // 0xe683b0: r2 = inline_Allocate_Double()
    //     0xe683b0: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xe683b4: add             x2, x2, #0x10
    //     0xe683b8: cmp             x0, x2
    //     0xe683bc: b.ls            #0xe685d8
    //     0xe683c0: str             x2, [THR, #0x50]  ; THR::top
    //     0xe683c4: sub             x2, x2, #0xf
    //     0xe683c8: movz            x0, #0xe15c
    //     0xe683cc: movk            x0, #0x3, lsl #16
    //     0xe683d0: stur            x0, [x2, #-1]
    // 0xe683d4: StoreField: r2->field_7 = d0
    //     0xe683d4: stur            d0, [x2, #7]
    // 0xe683d8: stur            x2, [fp, #-0x40]
    // 0xe683dc: r0 = 60
    //     0xe683dc: movz            x0, #0x3c
    // 0xe683e0: branchIfSmi(r1, 0xe683ec)
    //     0xe683e0: tbz             w1, #0, #0xe683ec
    // 0xe683e4: r0 = LoadClassIdInstr(r1)
    //     0xe683e4: ldur            x0, [x1, #-1]
    //     0xe683e8: ubfx            x0, x0, #0xc, #0x14
    // 0xe683ec: stp             x2, x1, [SP]
    // 0xe683f0: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xe683f0: sub             lr, x0, #0xfe3
    //     0xe683f4: ldr             lr, [x21, lr, lsl #3]
    //     0xe683f8: blr             lr
    // 0xe683fc: tbnz            w0, #4, #0xe6840c
    // 0xe68400: ldur            x5, [fp, #-0x18]
    // 0xe68404: d0 = 0.000000
    //     0xe68404: eor             v0.16b, v0.16b, v0.16b
    // 0xe68408: b               #0xe684cc
    // 0xe6840c: ldur            x1, [fp, #-0x18]
    // 0xe68410: r0 = 60
    //     0xe68410: movz            x0, #0x3c
    // 0xe68414: branchIfSmi(r1, 0xe68420)
    //     0xe68414: tbz             w1, #0, #0xe68420
    // 0xe68418: r0 = LoadClassIdInstr(r1)
    //     0xe68418: ldur            x0, [x1, #-1]
    //     0xe6841c: ubfx            x0, x0, #0xc, #0x14
    // 0xe68420: ldur            x16, [fp, #-0x40]
    // 0xe68424: stp             x16, x1, [SP]
    // 0xe68428: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xe68428: sub             lr, x0, #0xfd2
    //     0xe6842c: ldr             lr, [x21, lr, lsl #3]
    //     0xe68430: blr             lr
    // 0xe68434: tbnz            w0, #4, #0xe68444
    // 0xe68438: ldur            x5, [fp, #-0x40]
    // 0xe6843c: d0 = 0.000000
    //     0xe6843c: eor             v0.16b, v0.16b, v0.16b
    // 0xe68440: b               #0xe684cc
    // 0xe68444: ldur            x0, [fp, #-0x18]
    // 0xe68448: r1 = 60
    //     0xe68448: movz            x1, #0x3c
    // 0xe6844c: branchIfSmi(r0, 0xe68458)
    //     0xe6844c: tbz             w0, #0, #0xe68458
    // 0xe68450: r1 = LoadClassIdInstr(r0)
    //     0xe68450: ldur            x1, [x0, #-1]
    //     0xe68454: ubfx            x1, x1, #0xc, #0x14
    // 0xe68458: cmp             x1, #0x3e
    // 0xe6845c: b.ne            #0xe684b0
    // 0xe68460: d0 = 0.000000
    //     0xe68460: eor             v0.16b, v0.16b, v0.16b
    // 0xe68464: LoadField: d1 = r0->field_7
    //     0xe68464: ldur            d1, [x0, #7]
    // 0xe68468: fcmp            d1, d0
    // 0xe6846c: b.ne            #0xe684a8
    // 0xe68470: ldur            d2, [fp, #-0x58]
    // 0xe68474: fadd            d3, d1, d2
    // 0xe68478: r0 = inline_Allocate_Double()
    //     0xe68478: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6847c: add             x0, x0, #0x10
    //     0xe68480: cmp             x1, x0
    //     0xe68484: b.ls            #0xe685f4
    //     0xe68488: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6848c: sub             x0, x0, #0xf
    //     0xe68490: movz            x1, #0xe15c
    //     0xe68494: movk            x1, #0x3, lsl #16
    //     0xe68498: stur            x1, [x0, #-1]
    // 0xe6849c: StoreField: r0->field_7 = d3
    //     0xe6849c: stur            d3, [x0, #7]
    // 0xe684a0: mov             x5, x0
    // 0xe684a4: b               #0xe684cc
    // 0xe684a8: ldur            d2, [fp, #-0x58]
    // 0xe684ac: b               #0xe684b8
    // 0xe684b0: ldur            d2, [fp, #-0x58]
    // 0xe684b4: d0 = 0.000000
    //     0xe684b4: eor             v0.16b, v0.16b, v0.16b
    // 0xe684b8: fcmp            d2, d2
    // 0xe684bc: b.vc            #0xe684c8
    // 0xe684c0: ldur            x5, [fp, #-0x40]
    // 0xe684c4: b               #0xe684cc
    // 0xe684c8: mov             x5, x0
    // 0xe684cc: ldur            x0, [fp, #-8]
    // 0xe684d0: add             x4, x0, #1
    // 0xe684d4: ldur            x6, [fp, #-0x50]
    // 0xe684d8: b               #0xe68104
    // 0xe684dc: ldur            x2, [fp, #-0x30]
    // 0xe684e0: mov             x1, x6
    // 0xe684e4: mov             x0, x5
    // 0xe684e8: LoadField: d1 = r1->field_7
    //     0xe684e8: ldur            d1, [x1, #7]
    // 0xe684ec: LoadField: d3 = r0->field_7
    //     0xe684ec: ldur            d3, [x0, #7]
    // 0xe684f0: ldur            d0, [fp, #-0x68]
    // 0xe684f4: ldur            d2, [fp, #-0x70]
    // 0xe684f8: r1 = Null
    //     0xe684f8: mov             x1, NULL
    // 0xe684fc: r0 = PdfRect.fromLTRB()
    //     0xe684fc: bl              #0xe68604  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromLTRB
    // 0xe68500: mov             x2, x0
    // 0xe68504: ldur            x1, [fp, #-0x30]
    // 0xe68508: StoreField: r1->field_1f = r0
    //     0xe68508: stur            w0, [x1, #0x1f]
    //     0xe6850c: ldurb           w16, [x1, #-1]
    //     0xe68510: ldurb           w17, [x0, #-1]
    //     0xe68514: and             x16, x17, x16, lsr #2
    //     0xe68518: tst             x16, HEAP, lsr #32
    //     0xe6851c: b.eq            #0xe68524
    //     0xe68520: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe68524: mov             x0, x2
    // 0xe68528: LeaveFrame
    //     0xe68528: mov             SP, fp
    //     0xe6852c: ldp             fp, lr, [SP], #0x10
    // 0xe68530: ret
    //     0xe68530: ret             
    // 0xe68534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe68534: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe68538: b               #0xe67dd8
    // 0xe6853c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6853c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe68540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe68540: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe68544: r0 = RangeErrorSharedWithFPURegs()
    //     0xe68544: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe68548: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe68548: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe6854c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe6854c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe68550: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe68550: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe68554: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe68554: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe68558: stp             q1, q2, [SP, #-0x20]!
    // 0xe6855c: SaveReg r1
    //     0xe6855c: str             x1, [SP, #-8]!
    // 0xe68560: r0 = AllocateDouble()
    //     0xe68560: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe68564: RestoreReg r1
    //     0xe68564: ldr             x1, [SP], #8
    // 0xe68568: ldp             q1, q2, [SP], #0x20
    // 0xe6856c: b               #0xe680cc
    // 0xe68570: SaveReg d1
    //     0xe68570: str             q1, [SP, #-0x10]!
    // 0xe68574: stp             x0, x1, [SP, #-0x10]!
    // 0xe68578: r0 = AllocateDouble()
    //     0xe68578: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6857c: mov             x2, x0
    // 0xe68580: ldp             x0, x1, [SP], #0x10
    // 0xe68584: RestoreReg d1
    //     0xe68584: ldr             q1, [SP], #0x10
    // 0xe68588: b               #0xe680f4
    // 0xe6858c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6858c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe68590: b               #0xe68124
    // 0xe68594: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe68594: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe68598: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe68598: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6859c: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6859c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe685a0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe685a0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe685a4: stp             q1, q2, [SP, #-0x20]!
    // 0xe685a8: stp             x2, x3, [SP, #-0x10]!
    // 0xe685ac: SaveReg r1
    //     0xe685ac: str             x1, [SP, #-8]!
    // 0xe685b0: r0 = AllocateDouble()
    //     0xe685b0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe685b4: mov             x4, x0
    // 0xe685b8: RestoreReg r1
    //     0xe685b8: ldr             x1, [SP], #8
    // 0xe685bc: ldp             x2, x3, [SP], #0x10
    // 0xe685c0: ldp             q1, q2, [SP], #0x20
    // 0xe685c4: b               #0xe68264
    // 0xe685c8: stp             q0, q1, [SP, #-0x20]!
    // 0xe685cc: r0 = AllocateDouble()
    //     0xe685cc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe685d0: ldp             q0, q1, [SP], #0x20
    // 0xe685d4: b               #0xe6832c
    // 0xe685d8: SaveReg d0
    //     0xe685d8: str             q0, [SP, #-0x10]!
    // 0xe685dc: stp             x1, x6, [SP, #-0x10]!
    // 0xe685e0: r0 = AllocateDouble()
    //     0xe685e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe685e4: mov             x2, x0
    // 0xe685e8: ldp             x1, x6, [SP], #0x10
    // 0xe685ec: RestoreReg d0
    //     0xe685ec: ldr             q0, [SP], #0x10
    // 0xe685f0: b               #0xe683d4
    // 0xe685f4: stp             q0, q3, [SP, #-0x20]!
    // 0xe685f8: r0 = AllocateDouble()
    //     0xe685f8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe685fc: ldp             q0, q3, [SP], #0x20
    // 0xe68600: b               #0xe6849c
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xe95490, size: 0x4c
    // 0xe95490: EnterFrame
    //     0xe95490: stp             fp, lr, [SP, #-0x10]!
    //     0xe95494: mov             fp, SP
    // 0xe95498: AllocStack(0x18)
    //     0xe95498: sub             SP, SP, #0x18
    // 0xe9549c: SetupParameters(dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe9549c: stur            x2, [fp, #-0x18]
    // 0xe954a0: LoadField: r0 = r1->field_7
    //     0xe954a0: ldur            w0, [x1, #7]
    // 0xe954a4: DecompressPointer r0
    //     0xe954a4: add             x0, x0, HEAP, lsl #32
    // 0xe954a8: stur            x0, [fp, #-0x10]
    // 0xe954ac: LoadField: r3 = r1->field_f
    //     0xe954ac: ldur            x3, [x1, #0xf]
    // 0xe954b0: stur            x3, [fp, #-8]
    // 0xe954b4: r0 = _TextDecoration()
    //     0xe954b4: bl              #0xe954dc  ; Allocate_TextDecorationStub -> _TextDecoration (size=0x24)
    // 0xe954b8: ldur            x1, [fp, #-0x10]
    // 0xe954bc: StoreField: r0->field_7 = r1
    //     0xe954bc: stur            w1, [x0, #7]
    // 0xe954c0: ldur            x1, [fp, #-8]
    // 0xe954c4: StoreField: r0->field_f = r1
    //     0xe954c4: stur            x1, [x0, #0xf]
    // 0xe954c8: ldur            x1, [fp, #-0x18]
    // 0xe954cc: ArrayStore: r0[0] = r1  ; List_8
    //     0xe954cc: stur            x1, [x0, #0x17]
    // 0xe954d0: LeaveFrame
    //     0xe954d0: mov             SP, fp
    //     0xe954d4: ldp             fp, lr, [SP], #0x10
    // 0xe954d8: ret
    //     0xe954d8: ret             
  }
}

// class id: 772, size: 0x10, field offset: 0x8
abstract class _Span extends Object {

  set _ offset=(/* No info */) {
    // ** addr: 0xeafdb0, size: 0x30
    // 0xeafdb0: mov             x0, x2
    // 0xeafdb4: StoreField: r1->field_b = r0
    //     0xeafdb4: stur            w0, [x1, #0xb]
    //     0xeafdb8: ldurb           w16, [x1, #-1]
    //     0xeafdbc: ldurb           w17, [x0, #-1]
    //     0xeafdc0: and             x16, x17, x16, lsr #2
    //     0xeafdc4: tst             x16, HEAP, lsr #32
    //     0xeafdc8: b.eq            #0xeafdd8
    //     0xeafdcc: str             lr, [SP, #-8]!
    //     0xeafdd0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    //     0xeafdd4: ldr             lr, [SP], #8
    // 0xeafdd8: r0 = Null
    //     0xeafdd8: mov             x0, NULL
    // 0xeafddc: ret
    //     0xeafddc: ret             
  }
}

// class id: 773, size: 0x14, field offset: 0x10
class _WidgetSpan extends _Span {

  _ toString(/* No info */) {
    // ** addr: 0xc3ad04, size: 0xac
    // 0xc3ad04: EnterFrame
    //     0xc3ad04: stp             fp, lr, [SP, #-0x10]!
    //     0xc3ad08: mov             fp, SP
    // 0xc3ad0c: AllocStack(0x20)
    //     0xc3ad0c: sub             SP, SP, #0x20
    // 0xc3ad10: CheckStackOverflow
    //     0xc3ad10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3ad14: cmp             SP, x16
    //     0xc3ad18: b.ls            #0xc3ada4
    // 0xc3ad1c: r1 = Null
    //     0xc3ad1c: mov             x1, NULL
    // 0xc3ad20: r2 = 8
    //     0xc3ad20: movz            x2, #0x8
    // 0xc3ad24: r0 = AllocateArray()
    //     0xc3ad24: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3ad28: stur            x0, [fp, #-8]
    // 0xc3ad2c: r16 = "Widget \""
    //     0xc3ad2c: add             x16, PP, #0x47, lsl #12  ; [pp+0x470f8] "Widget \""
    //     0xc3ad30: ldr             x16, [x16, #0xf8]
    // 0xc3ad34: StoreField: r0->field_f = r16
    //     0xc3ad34: stur            w16, [x0, #0xf]
    // 0xc3ad38: ldr             x1, [fp, #0x10]
    // 0xc3ad3c: LoadField: r2 = r1->field_f
    //     0xc3ad3c: ldur            w2, [x1, #0xf]
    // 0xc3ad40: DecompressPointer r2
    //     0xc3ad40: add             x2, x2, HEAP, lsl #32
    // 0xc3ad44: StoreField: r0->field_13 = r2
    //     0xc3ad44: stur            w2, [x0, #0x13]
    // 0xc3ad48: r16 = "\" offset:"
    //     0xc3ad48: add             x16, PP, #0x47, lsl #12  ; [pp+0x470e8] "\" offset:"
    //     0xc3ad4c: ldr             x16, [x16, #0xe8]
    // 0xc3ad50: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3ad50: stur            w16, [x0, #0x17]
    // 0xc3ad54: LoadField: r1 = r2->field_7
    //     0xc3ad54: ldur            w1, [x2, #7]
    // 0xc3ad58: DecompressPointer r1
    //     0xc3ad58: add             x1, x1, HEAP, lsl #32
    // 0xc3ad5c: cmp             w1, NULL
    // 0xc3ad60: b.eq            #0xc3adac
    // 0xc3ad64: LoadField: d0 = r1->field_7
    //     0xc3ad64: ldur            d0, [x1, #7]
    // 0xc3ad68: stur            d0, [fp, #-0x18]
    // 0xc3ad6c: LoadField: d1 = r1->field_f
    //     0xc3ad6c: ldur            d1, [x1, #0xf]
    // 0xc3ad70: stur            d1, [fp, #-0x10]
    // 0xc3ad74: r0 = PdfPoint()
    //     0xc3ad74: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xc3ad78: ldur            d0, [fp, #-0x18]
    // 0xc3ad7c: StoreField: r0->field_7 = d0
    //     0xc3ad7c: stur            d0, [x0, #7]
    // 0xc3ad80: ldur            d0, [fp, #-0x10]
    // 0xc3ad84: StoreField: r0->field_f = d0
    //     0xc3ad84: stur            d0, [x0, #0xf]
    // 0xc3ad88: ldur            x1, [fp, #-8]
    // 0xc3ad8c: StoreField: r1->field_1b = r0
    //     0xc3ad8c: stur            w0, [x1, #0x1b]
    // 0xc3ad90: str             x1, [SP]
    // 0xc3ad94: r0 = _interpolate()
    //     0xc3ad94: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3ad98: LeaveFrame
    //     0xc3ad98: mov             SP, fp
    //     0xc3ad9c: ldp             fp, lr, [SP], #0x10
    // 0xc3ada0: ret
    //     0xc3ada0: ret             
    // 0xc3ada4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3ada4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3ada8: b               #0xc3ad1c
    // 0xc3adac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc3adac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ paint(/* No info */) {
    // ** addr: 0xeaf754, size: 0xf0
    // 0xeaf754: EnterFrame
    //     0xeaf754: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf758: mov             fp, SP
    // 0xeaf75c: AllocStack(0x30)
    //     0xeaf75c: sub             SP, SP, #0x30
    // 0xeaf760: SetupParameters(dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xeaf760: stur            x2, [fp, #-0x18]
    // 0xeaf764: CheckStackOverflow
    //     0xeaf764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf768: cmp             SP, x16
    //     0xeaf76c: b.ls            #0xeaf838
    // 0xeaf770: LoadField: r0 = r1->field_f
    //     0xeaf770: ldur            w0, [x1, #0xf]
    // 0xeaf774: DecompressPointer r0
    //     0xeaf774: add             x0, x0, HEAP, lsl #32
    // 0xeaf778: stur            x0, [fp, #-0x10]
    // 0xeaf77c: LoadField: d0 = r5->field_7
    //     0xeaf77c: ldur            d0, [x5, #7]
    // 0xeaf780: LoadField: r1 = r0->field_7
    //     0xeaf780: ldur            w1, [x0, #7]
    // 0xeaf784: DecompressPointer r1
    //     0xeaf784: add             x1, x1, HEAP, lsl #32
    // 0xeaf788: stur            x1, [fp, #-8]
    // 0xeaf78c: cmp             w1, NULL
    // 0xeaf790: b.eq            #0xeaf840
    // 0xeaf794: LoadField: d1 = r1->field_7
    //     0xeaf794: ldur            d1, [x1, #7]
    // 0xeaf798: LoadField: d2 = r1->field_f
    //     0xeaf798: ldur            d2, [x1, #0xf]
    // 0xeaf79c: fadd            d3, d0, d1
    // 0xeaf7a0: stur            d3, [fp, #-0x30]
    // 0xeaf7a4: LoadField: d0 = r5->field_f
    //     0xeaf7a4: ldur            d0, [x5, #0xf]
    // 0xeaf7a8: fadd            d1, d0, d2
    // 0xeaf7ac: stur            d1, [fp, #-0x28]
    // 0xeaf7b0: r0 = PdfPoint()
    //     0xeaf7b0: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xeaf7b4: ldur            d0, [fp, #-0x30]
    // 0xeaf7b8: stur            x0, [fp, #-0x20]
    // 0xeaf7bc: StoreField: r0->field_7 = d0
    //     0xeaf7bc: stur            d0, [x0, #7]
    // 0xeaf7c0: ldur            d0, [fp, #-0x28]
    // 0xeaf7c4: StoreField: r0->field_f = d0
    //     0xeaf7c4: stur            d0, [x0, #0xf]
    // 0xeaf7c8: ldur            x1, [fp, #-8]
    // 0xeaf7cc: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xeaf7cc: ldur            d0, [x1, #0x17]
    // 0xeaf7d0: stur            d0, [fp, #-0x30]
    // 0xeaf7d4: LoadField: d1 = r1->field_1f
    //     0xeaf7d4: ldur            d1, [x1, #0x1f]
    // 0xeaf7d8: stur            d1, [fp, #-0x28]
    // 0xeaf7dc: r0 = PdfPoint()
    //     0xeaf7dc: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xeaf7e0: ldur            d0, [fp, #-0x30]
    // 0xeaf7e4: StoreField: r0->field_7 = d0
    //     0xeaf7e4: stur            d0, [x0, #7]
    // 0xeaf7e8: ldur            d0, [fp, #-0x28]
    // 0xeaf7ec: StoreField: r0->field_f = d0
    //     0xeaf7ec: stur            d0, [x0, #0xf]
    // 0xeaf7f0: ldur            x2, [fp, #-0x20]
    // 0xeaf7f4: mov             x3, x0
    // 0xeaf7f8: r1 = Null
    //     0xeaf7f8: mov             x1, NULL
    // 0xeaf7fc: r0 = PdfRect.fromPoints()
    //     0xeaf7fc: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xeaf800: ldur            x1, [fp, #-0x10]
    // 0xeaf804: StoreField: r1->field_7 = r0
    //     0xeaf804: stur            w0, [x1, #7]
    //     0xeaf808: ldurb           w16, [x1, #-1]
    //     0xeaf80c: ldurb           w17, [x0, #-1]
    //     0xeaf810: and             x16, x17, x16, lsr #2
    //     0xeaf814: tst             x16, HEAP, lsr #32
    //     0xeaf818: b.eq            #0xeaf820
    //     0xeaf81c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xeaf820: ldur            x2, [fp, #-0x18]
    // 0xeaf824: r0 = paint()
    //     0xeaf824: bl              #0xe63944  ; [package:pdf/src/widgets/widget.dart] StatelessWidget::paint
    // 0xeaf828: r0 = Null
    //     0xeaf828: mov             x0, NULL
    // 0xeaf82c: LeaveFrame
    //     0xeaf82c: mov             SP, fp
    //     0xeaf830: ldp             fp, lr, [SP], #0x10
    // 0xeaf834: ret
    //     0xeaf834: ret             
    // 0xeaf838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf838: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf83c: b               #0xeaf770
    // 0xeaf840: r0 = NullCastErrorSharedWithFPURegs()
    //     0xeaf840: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  get _ width(/* No info */) {
    // ** addr: 0xeaf85c, size: 0x2c
    // 0xeaf85c: LoadField: r0 = r1->field_f
    //     0xeaf85c: ldur            w0, [x1, #0xf]
    // 0xeaf860: DecompressPointer r0
    //     0xeaf860: add             x0, x0, HEAP, lsl #32
    // 0xeaf864: LoadField: r1 = r0->field_7
    //     0xeaf864: ldur            w1, [x0, #7]
    // 0xeaf868: DecompressPointer r1
    //     0xeaf868: add             x1, x1, HEAP, lsl #32
    // 0xeaf86c: cmp             w1, NULL
    // 0xeaf870: b.eq            #0xeaf87c
    // 0xeaf874: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xeaf874: ldur            d0, [x1, #0x17]
    // 0xeaf878: ret
    //     0xeaf878: ret             
    // 0xeaf87c: EnterFrame
    //     0xeaf87c: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf880: mov             fp, SP
    // 0xeaf884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeaf884: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  set _ offset=(/* No info */) {
    // ** addr: 0xeafde0, size: 0x88
    // 0xeafde0: EnterFrame
    //     0xeafde0: stp             fp, lr, [SP, #-0x10]!
    //     0xeafde4: mov             fp, SP
    // 0xeafde8: AllocStack(0x10)
    //     0xeafde8: sub             SP, SP, #0x10
    // 0xeafdec: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xeafdec: stur            x2, [fp, #-0x10]
    // 0xeafdf0: CheckStackOverflow
    //     0xeafdf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeafdf4: cmp             SP, x16
    //     0xeafdf8: b.ls            #0xeafe5c
    // 0xeafdfc: LoadField: r0 = r1->field_f
    //     0xeafdfc: ldur            w0, [x1, #0xf]
    // 0xeafe00: DecompressPointer r0
    //     0xeafe00: add             x0, x0, HEAP, lsl #32
    // 0xeafe04: stur            x0, [fp, #-8]
    // 0xeafe08: LoadField: r1 = r0->field_7
    //     0xeafe08: ldur            w1, [x0, #7]
    // 0xeafe0c: DecompressPointer r1
    //     0xeafe0c: add             x1, x1, HEAP, lsl #32
    // 0xeafe10: cmp             w1, NULL
    // 0xeafe14: b.eq            #0xeafe64
    // 0xeafe18: r0 = size()
    //     0xeafe18: bl              #0xe68b8c  ; [package:pdf/src/pdf/rect.dart] PdfRect::size
    // 0xeafe1c: ldur            x2, [fp, #-0x10]
    // 0xeafe20: mov             x3, x0
    // 0xeafe24: r1 = Null
    //     0xeafe24: mov             x1, NULL
    // 0xeafe28: r0 = PdfRect.fromPoints()
    //     0xeafe28: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xeafe2c: ldur            x1, [fp, #-8]
    // 0xeafe30: StoreField: r1->field_7 = r0
    //     0xeafe30: stur            w0, [x1, #7]
    //     0xeafe34: ldurb           w16, [x1, #-1]
    //     0xeafe38: ldurb           w17, [x0, #-1]
    //     0xeafe3c: and             x16, x17, x16, lsr #2
    //     0xeafe40: tst             x16, HEAP, lsr #32
    //     0xeafe44: b.eq            #0xeafe4c
    //     0xeafe48: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xeafe4c: r0 = Null
    //     0xeafe4c: mov             x0, NULL
    // 0xeafe50: LeaveFrame
    //     0xeafe50: mov             SP, fp
    //     0xeafe54: ldp             fp, lr, [SP], #0x10
    // 0xeafe58: ret
    //     0xeafe58: ret             
    // 0xeafe5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeafe5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeafe60: b               #0xeafdfc
    // 0xeafe64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeafe64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ height(/* No info */) {
    // ** addr: 0xeb0c70, size: 0x2c
    // 0xeb0c70: LoadField: r0 = r1->field_f
    //     0xeb0c70: ldur            w0, [x1, #0xf]
    // 0xeb0c74: DecompressPointer r0
    //     0xeb0c74: add             x0, x0, HEAP, lsl #32
    // 0xeb0c78: LoadField: r1 = r0->field_7
    //     0xeb0c78: ldur            w1, [x0, #7]
    // 0xeb0c7c: DecompressPointer r1
    //     0xeb0c7c: add             x1, x1, HEAP, lsl #32
    // 0xeb0c80: cmp             w1, NULL
    // 0xeb0c84: b.eq            #0xeb0c90
    // 0xeb0c88: LoadField: d0 = r1->field_1f
    //     0xeb0c88: ldur            d0, [x1, #0x1f]
    // 0xeb0c8c: ret
    //     0xeb0c8c: ret             
    // 0xeb0c90: EnterFrame
    //     0xeb0c90: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0c94: mov             fp, SP
    // 0xeb0c98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb0c98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ offset(/* No info */) {
    // ** addr: 0xeb0c9c, size: 0x58
    // 0xeb0c9c: EnterFrame
    //     0xeb0c9c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0ca0: mov             fp, SP
    // 0xeb0ca4: AllocStack(0x10)
    //     0xeb0ca4: sub             SP, SP, #0x10
    // 0xeb0ca8: LoadField: r0 = r1->field_f
    //     0xeb0ca8: ldur            w0, [x1, #0xf]
    // 0xeb0cac: DecompressPointer r0
    //     0xeb0cac: add             x0, x0, HEAP, lsl #32
    // 0xeb0cb0: LoadField: r1 = r0->field_7
    //     0xeb0cb0: ldur            w1, [x0, #7]
    // 0xeb0cb4: DecompressPointer r1
    //     0xeb0cb4: add             x1, x1, HEAP, lsl #32
    // 0xeb0cb8: cmp             w1, NULL
    // 0xeb0cbc: b.eq            #0xeb0cf0
    // 0xeb0cc0: LoadField: d0 = r1->field_7
    //     0xeb0cc0: ldur            d0, [x1, #7]
    // 0xeb0cc4: stur            d0, [fp, #-0x10]
    // 0xeb0cc8: LoadField: d1 = r1->field_f
    //     0xeb0cc8: ldur            d1, [x1, #0xf]
    // 0xeb0ccc: stur            d1, [fp, #-8]
    // 0xeb0cd0: r0 = PdfPoint()
    //     0xeb0cd0: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xeb0cd4: ldur            d0, [fp, #-0x10]
    // 0xeb0cd8: StoreField: r0->field_7 = d0
    //     0xeb0cd8: stur            d0, [x0, #7]
    // 0xeb0cdc: ldur            d0, [fp, #-8]
    // 0xeb0ce0: StoreField: r0->field_f = d0
    //     0xeb0ce0: stur            d0, [x0, #0xf]
    // 0xeb0ce4: LeaveFrame
    //     0xeb0ce4: mov             SP, fp
    //     0xeb0ce8: ldp             fp, lr, [SP], #0x10
    // 0xeb0cec: ret
    //     0xeb0cec: ret             
    // 0xeb0cf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb0cf0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 774, size: 0x18, field offset: 0x10
class _Word extends _Span {

  _ toString(/* No info */) {
    // ** addr: 0xc3ac60, size: 0xa4
    // 0xc3ac60: EnterFrame
    //     0xc3ac60: stp             fp, lr, [SP, #-0x10]!
    //     0xc3ac64: mov             fp, SP
    // 0xc3ac68: AllocStack(0x8)
    //     0xc3ac68: sub             SP, SP, #8
    // 0xc3ac6c: CheckStackOverflow
    //     0xc3ac6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3ac70: cmp             SP, x16
    //     0xc3ac74: b.ls            #0xc3acfc
    // 0xc3ac78: r1 = Null
    //     0xc3ac78: mov             x1, NULL
    // 0xc3ac7c: r2 = 16
    //     0xc3ac7c: movz            x2, #0x10
    // 0xc3ac80: r0 = AllocateArray()
    //     0xc3ac80: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3ac84: r16 = "Word \""
    //     0xc3ac84: add             x16, PP, #0x47, lsl #12  ; [pp+0x470e0] "Word \""
    //     0xc3ac88: ldr             x16, [x16, #0xe0]
    // 0xc3ac8c: StoreField: r0->field_f = r16
    //     0xc3ac8c: stur            w16, [x0, #0xf]
    // 0xc3ac90: ldr             x1, [fp, #0x10]
    // 0xc3ac94: LoadField: r2 = r1->field_f
    //     0xc3ac94: ldur            w2, [x1, #0xf]
    // 0xc3ac98: DecompressPointer r2
    //     0xc3ac98: add             x2, x2, HEAP, lsl #32
    // 0xc3ac9c: StoreField: r0->field_13 = r2
    //     0xc3ac9c: stur            w2, [x0, #0x13]
    // 0xc3aca0: r16 = "\" offset:"
    //     0xc3aca0: add             x16, PP, #0x47, lsl #12  ; [pp+0x470e8] "\" offset:"
    //     0xc3aca4: ldr             x16, [x16, #0xe8]
    // 0xc3aca8: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3aca8: stur            w16, [x0, #0x17]
    // 0xc3acac: LoadField: r2 = r1->field_b
    //     0xc3acac: ldur            w2, [x1, #0xb]
    // 0xc3acb0: DecompressPointer r2
    //     0xc3acb0: add             x2, x2, HEAP, lsl #32
    // 0xc3acb4: StoreField: r0->field_1b = r2
    //     0xc3acb4: stur            w2, [x0, #0x1b]
    // 0xc3acb8: r16 = " metrics:"
    //     0xc3acb8: add             x16, PP, #0x47, lsl #12  ; [pp+0x470f0] " metrics:"
    //     0xc3acbc: ldr             x16, [x16, #0xf0]
    // 0xc3acc0: StoreField: r0->field_1f = r16
    //     0xc3acc0: stur            w16, [x0, #0x1f]
    // 0xc3acc4: LoadField: r2 = r1->field_13
    //     0xc3acc4: ldur            w2, [x1, #0x13]
    // 0xc3acc8: DecompressPointer r2
    //     0xc3acc8: add             x2, x2, HEAP, lsl #32
    // 0xc3accc: StoreField: r0->field_23 = r2
    //     0xc3accc: stur            w2, [x0, #0x23]
    // 0xc3acd0: r16 = " style:"
    //     0xc3acd0: add             x16, PP, #0x33, lsl #12  ; [pp+0x336a0] " style:"
    //     0xc3acd4: ldr             x16, [x16, #0x6a0]
    // 0xc3acd8: StoreField: r0->field_27 = r16
    //     0xc3acd8: stur            w16, [x0, #0x27]
    // 0xc3acdc: LoadField: r2 = r1->field_7
    //     0xc3acdc: ldur            w2, [x1, #7]
    // 0xc3ace0: DecompressPointer r2
    //     0xc3ace0: add             x2, x2, HEAP, lsl #32
    // 0xc3ace4: StoreField: r0->field_2b = r2
    //     0xc3ace4: stur            w2, [x0, #0x2b]
    // 0xc3ace8: str             x0, [SP]
    // 0xc3acec: r0 = _interpolate()
    //     0xc3acec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3acf0: LeaveFrame
    //     0xc3acf0: mov             SP, fp
    //     0xc3acf4: ldp             fp, lr, [SP], #0x10
    // 0xc3acf8: ret
    //     0xc3acf8: ret             
    // 0xc3acfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3acfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3ad00: b               #0xc3ac78
  }
  _ paint(/* No info */) {
    // ** addr: 0xeaf5e0, size: 0x174
    // 0xeaf5e0: EnterFrame
    //     0xeaf5e0: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf5e4: mov             fp, SP
    // 0xeaf5e8: AllocStack(0x38)
    //     0xeaf5e8: sub             SP, SP, #0x38
    // 0xeaf5ec: SetupParameters(_Word this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */)
    //     0xeaf5ec: mov             x0, x3
    //     0xeaf5f0: stur            x3, [fp, #-0x20]
    //     0xeaf5f4: mov             x3, x1
    //     0xeaf5f8: stur            x1, [fp, #-0x10]
    //     0xeaf5fc: stur            x2, [fp, #-0x18]
    //     0xeaf600: stur            x5, [fp, #-0x28]
    // 0xeaf604: CheckStackOverflow
    //     0xeaf604: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf608: cmp             SP, x16
    //     0xeaf60c: b.ls            #0xeaf718
    // 0xeaf610: LoadField: r4 = r2->field_b
    //     0xeaf610: ldur            w4, [x2, #0xb]
    // 0xeaf614: DecompressPointer r4
    //     0xeaf614: add             x4, x4, HEAP, lsl #32
    // 0xeaf618: stur            x4, [fp, #-8]
    // 0xeaf61c: cmp             w4, NULL
    // 0xeaf620: b.eq            #0xeaf720
    // 0xeaf624: mov             x1, x0
    // 0xeaf628: r0 = font()
    //     0xeaf628: bl              #0xb0fcfc  ; [package:pdf/src/widgets/text_style.dart] TextStyle::font
    // 0xeaf62c: cmp             w0, NULL
    // 0xeaf630: b.eq            #0xeaf724
    // 0xeaf634: mov             x1, x0
    // 0xeaf638: ldur            x2, [fp, #-0x18]
    // 0xeaf63c: r0 = getFont()
    //     0xeaf63c: bl              #0xe65b4c  ; [package:pdf/src/widgets/font.dart] Font::getFont
    // 0xeaf640: mov             x1, x0
    // 0xeaf644: ldur            x0, [fp, #-0x20]
    // 0xeaf648: LoadField: r2 = r0->field_23
    //     0xeaf648: ldur            w2, [x0, #0x23]
    // 0xeaf64c: DecompressPointer r2
    //     0xeaf64c: add             x2, x2, HEAP, lsl #32
    // 0xeaf650: cmp             w2, NULL
    // 0xeaf654: b.eq            #0xeaf728
    // 0xeaf658: LoadField: d0 = r2->field_7
    //     0xeaf658: ldur            d0, [x2, #7]
    // 0xeaf65c: ldur            x2, [fp, #-0x10]
    // 0xeaf660: LoadField: r3 = r2->field_f
    //     0xeaf660: ldur            w3, [x2, #0xf]
    // 0xeaf664: DecompressPointer r3
    //     0xeaf664: add             x3, x3, HEAP, lsl #32
    // 0xeaf668: ldur            x4, [fp, #-0x28]
    // 0xeaf66c: LoadField: d1 = r4->field_7
    //     0xeaf66c: ldur            d1, [x4, #7]
    // 0xeaf670: LoadField: r5 = r2->field_b
    //     0xeaf670: ldur            w5, [x2, #0xb]
    // 0xeaf674: DecompressPointer r5
    //     0xeaf674: add             x5, x5, HEAP, lsl #32
    // 0xeaf678: LoadField: d2 = r5->field_7
    //     0xeaf678: ldur            d2, [x5, #7]
    // 0xeaf67c: fadd            d3, d1, d2
    // 0xeaf680: LoadField: d1 = r4->field_f
    //     0xeaf680: ldur            d1, [x4, #0xf]
    // 0xeaf684: LoadField: d2 = r5->field_f
    //     0xeaf684: ldur            d2, [x5, #0xf]
    // 0xeaf688: fadd            d4, d1, d2
    // 0xeaf68c: LoadField: r2 = r0->field_53
    //     0xeaf68c: ldur            w2, [x0, #0x53]
    // 0xeaf690: DecompressPointer r2
    //     0xeaf690: add             x2, x2, HEAP, lsl #32
    // 0xeaf694: cmp             w2, NULL
    // 0xeaf698: b.ne            #0xeaf6a4
    // 0xeaf69c: r2 = Instance_PdfTextRenderingMode
    //     0xeaf69c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e570] Obj!PdfTextRenderingMode@e2ed21
    //     0xeaf6a0: ldr             x2, [x2, #0x570]
    // 0xeaf6a4: LoadField: r4 = r0->field_2f
    //     0xeaf6a4: ldur            w4, [x0, #0x2f]
    // 0xeaf6a8: DecompressPointer r4
    //     0xeaf6a8: add             x4, x4, HEAP, lsl #32
    // 0xeaf6ac: cmp             w4, NULL
    // 0xeaf6b0: b.ne            #0xeaf6bc
    // 0xeaf6b4: d1 = 0.000000
    //     0xeaf6b4: eor             v1.16b, v1.16b, v1.16b
    // 0xeaf6b8: b               #0xeaf6c0
    // 0xeaf6bc: LoadField: d1 = r4->field_7
    //     0xeaf6bc: ldur            d1, [x4, #7]
    // 0xeaf6c0: r0 = inline_Allocate_Double()
    //     0xeaf6c0: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xeaf6c4: add             x0, x0, #0x10
    //     0xeaf6c8: cmp             x4, x0
    //     0xeaf6cc: b.ls            #0xeaf72c
    //     0xeaf6d0: str             x0, [THR, #0x50]  ; THR::top
    //     0xeaf6d4: sub             x0, x0, #0xf
    //     0xeaf6d8: movz            x4, #0xe15c
    //     0xeaf6dc: movk            x4, #0x3, lsl #16
    //     0xeaf6e0: stur            x4, [x0, #-1]
    // 0xeaf6e4: StoreField: r0->field_7 = d1
    //     0xeaf6e4: stur            d1, [x0, #7]
    // 0xeaf6e8: stp             x0, x2, [SP]
    // 0xeaf6ec: mov             x2, x1
    // 0xeaf6f0: ldur            x1, [fp, #-8]
    // 0xeaf6f4: mov             v1.16b, v3.16b
    // 0xeaf6f8: mov             v2.16b, v4.16b
    // 0xeaf6fc: r4 = const [0, 0x8, 0x2, 0x6, charSpace, 0x7, mode, 0x6, null]
    //     0xeaf6fc: add             x4, PP, #0x47, lsl #12  ; [pp+0x470d8] List(9) [0, 0x8, 0x2, 0x6, "charSpace", 0x7, "mode", 0x6, Null]
    //     0xeaf700: ldr             x4, [x4, #0xd8]
    // 0xeaf704: r0 = drawString()
    //     0xeaf704: bl              #0xe49e60  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawString
    // 0xeaf708: r0 = Null
    //     0xeaf708: mov             x0, NULL
    // 0xeaf70c: LeaveFrame
    //     0xeaf70c: mov             SP, fp
    //     0xeaf710: ldp             fp, lr, [SP], #0x10
    // 0xeaf714: ret
    //     0xeaf714: ret             
    // 0xeaf718: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf718: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf71c: b               #0xeaf610
    // 0xeaf720: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeaf720: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeaf724: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeaf724: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeaf728: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeaf728: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeaf72c: stp             q3, q4, [SP, #-0x20]!
    // 0xeaf730: stp             q0, q1, [SP, #-0x20]!
    // 0xeaf734: stp             x2, x3, [SP, #-0x10]!
    // 0xeaf738: SaveReg r1
    //     0xeaf738: str             x1, [SP, #-8]!
    // 0xeaf73c: r0 = AllocateDouble()
    //     0xeaf73c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xeaf740: RestoreReg r1
    //     0xeaf740: ldr             x1, [SP], #8
    // 0xeaf744: ldp             x2, x3, [SP], #0x10
    // 0xeaf748: ldp             q0, q1, [SP], #0x20
    // 0xeaf74c: ldp             q3, q4, [SP], #0x20
    // 0xeaf750: b               #0xeaf6e4
  }
  get _ width(/* No info */) {
    // ** addr: 0xeaf844, size: 0x18
    // 0xeaf844: LoadField: r0 = r1->field_13
    //     0xeaf844: ldur            w0, [x1, #0x13]
    // 0xeaf848: DecompressPointer r0
    //     0xeaf848: add             x0, x0, HEAP, lsl #32
    // 0xeaf84c: LoadField: d1 = r0->field_1f
    //     0xeaf84c: ldur            d1, [x0, #0x1f]
    // 0xeaf850: LoadField: d2 = r0->field_7
    //     0xeaf850: ldur            d2, [x0, #7]
    // 0xeaf854: fsub            d0, d1, d2
    // 0xeaf858: ret
    //     0xeaf858: ret             
  }
  get _ height(/* No info */) {
    // ** addr: 0xeb0c58, size: 0x18
    // 0xeb0c58: LoadField: r0 = r1->field_13
    //     0xeb0c58: ldur            w0, [x1, #0x13]
    // 0xeb0c5c: DecompressPointer r0
    //     0xeb0c5c: add             x0, x0, HEAP, lsl #32
    // 0xeb0c60: LoadField: d1 = r0->field_27
    //     0xeb0c60: ldur            d1, [x0, #0x27]
    // 0xeb0c64: LoadField: d2 = r0->field_2f
    //     0xeb0c64: ldur            d2, [x0, #0x2f]
    // 0xeb0c68: fsub            d0, d1, d2
    // 0xeb0c6c: ret
    //     0xeb0c6c: ret             
  }
}

// class id: 797, size: 0x4c, field offset: 0xc
class RichText extends _SingleChildWidget&Widget&SpanningWidget {

  late TextAlign _textAlign; // offset: 0x14

  _ RichText(/* No info */) {
    // ** addr: 0xb1235c, size: 0x1f0
    // 0xb1235c: EnterFrame
    //     0xb1235c: stp             fp, lr, [SP, #-0x10]!
    //     0xb12360: mov             fp, SP
    // 0xb12364: AllocStack(0x20)
    //     0xb12364: sub             SP, SP, #0x20
    // 0xb12368: SetupParameters(RichText this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */, {dynamic overflow = Instance_TextOverflow /* r5, fp-0x10 */, dynamic textAlign = Null /* r6, fp-0x8 */})
    //     0xb12368: mov             x3, x1
    //     0xb1236c: mov             x0, x2
    //     0xb12370: stur            x1, [fp, #-0x18]
    //     0xb12374: stur            x2, [fp, #-0x20]
    //     0xb12378: ldur            w1, [x4, #0x13]
    //     0xb1237c: ldur            w2, [x4, #0x1f]
    //     0xb12380: add             x2, x2, HEAP, lsl #32
    //     0xb12384: add             x16, PP, #0xc, lsl #12  ; [pp+0xc9a0] "overflow"
    //     0xb12388: ldr             x16, [x16, #0x9a0]
    //     0xb1238c: cmp             w2, w16
    //     0xb12390: b.ne            #0xb123b4
    //     0xb12394: ldur            w2, [x4, #0x23]
    //     0xb12398: add             x2, x2, HEAP, lsl #32
    //     0xb1239c: sub             w5, w1, w2
    //     0xb123a0: add             x2, fp, w5, sxtw #2
    //     0xb123a4: ldr             x2, [x2, #8]
    //     0xb123a8: mov             x5, x2
    //     0xb123ac: movz            x2, #0x1
    //     0xb123b0: b               #0xb123c0
    //     0xb123b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e1f8] Obj!TextOverflow@e2e561
    //     0xb123b8: ldr             x5, [x5, #0x1f8]
    //     0xb123bc: movz            x2, #0
    //     0xb123c0: stur            x5, [fp, #-0x10]
    //     0xb123c4: lsl             x6, x2, #1
    //     0xb123c8: lsl             w2, w6, #1
    //     0xb123cc: add             w6, w2, #8
    //     0xb123d0: add             x16, x4, w6, sxtw #1
    //     0xb123d4: ldur            w7, [x16, #0xf]
    //     0xb123d8: add             x7, x7, HEAP, lsl #32
    //     0xb123dc: ldr             x16, [PP, #0x4828]  ; [pp+0x4828] "textAlign"
    //     0xb123e0: cmp             w7, w16
    //     0xb123e4: b.ne            #0xb1240c
    //     0xb123e8: add             w6, w2, #0xa
    //     0xb123ec: add             x16, x4, w6, sxtw #1
    //     0xb123f0: ldur            w2, [x16, #0xf]
    //     0xb123f4: add             x2, x2, HEAP, lsl #32
    //     0xb123f8: sub             w4, w1, w2
    //     0xb123fc: add             x1, fp, w4, sxtw #2
    //     0xb12400: ldr             x1, [x1, #8]
    //     0xb12404: mov             x6, x1
    //     0xb12408: b               #0xb12410
    //     0xb1240c: mov             x6, NULL
    //     0xb12410: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb12414: add             x4, NULL, #0x30  ; false
    //     0xb12418: stur            x6, [fp, #-8]
    // 0xb12410: r1 = Sentinel
    // 0xb12414: r4 = false
    // 0xb1241c: CheckStackOverflow
    //     0xb1241c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12420: cmp             SP, x16
    //     0xb12424: b.ls            #0xb12544
    // 0xb12428: StoreField: r3->field_13 = r1
    //     0xb12428: stur            w1, [x3, #0x13]
    // 0xb1242c: StoreField: r3->field_3f = r4
    //     0xb1242c: stur            w4, [x3, #0x3f]
    // 0xb12430: r1 = <_Span>
    //     0xb12430: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e200] TypeArguments: <_Span>
    //     0xb12434: ldr             x1, [x1, #0x200]
    // 0xb12438: r2 = 0
    //     0xb12438: movz            x2, #0
    // 0xb1243c: r0 = _GrowableList()
    //     0xb1243c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb12440: ldur            x3, [fp, #-0x18]
    // 0xb12444: StoreField: r3->field_2f = r0
    //     0xb12444: stur            w0, [x3, #0x2f]
    //     0xb12448: ldurb           w16, [x3, #-1]
    //     0xb1244c: ldurb           w17, [x0, #-1]
    //     0xb12450: and             x16, x17, x16, lsr #2
    //     0xb12454: tst             x16, HEAP, lsr #32
    //     0xb12458: b.eq            #0xb12460
    //     0xb1245c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xb12460: r1 = <_TextDecoration>
    //     0xb12460: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e208] TypeArguments: <_TextDecoration>
    //     0xb12464: ldr             x1, [x1, #0x208]
    // 0xb12468: r2 = 0
    //     0xb12468: movz            x2, #0
    // 0xb1246c: r0 = _GrowableList()
    //     0xb1246c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb12470: ldur            x1, [fp, #-0x18]
    // 0xb12474: StoreField: r1->field_33 = r0
    //     0xb12474: stur            w0, [x1, #0x33]
    //     0xb12478: ldurb           w16, [x1, #-1]
    //     0xb1247c: ldurb           w17, [x0, #-1]
    //     0xb12480: and             x16, x17, x16, lsr #2
    //     0xb12484: tst             x16, HEAP, lsr #32
    //     0xb12488: b.eq            #0xb12490
    //     0xb1248c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb12490: r0 = RichTextContext()
    //     0xb12490: bl              #0xb1254c  ; AllocateRichTextContextStub -> RichTextContext (size=0x28)
    // 0xb12494: StoreField: r0->field_7 = rZR
    //     0xb12494: stur            xzr, [x0, #7]
    // 0xb12498: StoreField: r0->field_f = rZR
    //     0xb12498: stur            xzr, [x0, #0xf]
    // 0xb1249c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb1249c: stur            xzr, [x0, #0x17]
    // 0xb124a0: StoreField: r0->field_1f = rZR
    //     0xb124a0: stur            xzr, [x0, #0x1f]
    // 0xb124a4: ldur            x1, [fp, #-0x18]
    // 0xb124a8: StoreField: r1->field_37 = r0
    //     0xb124a8: stur            w0, [x1, #0x37]
    //     0xb124ac: ldurb           w16, [x1, #-1]
    //     0xb124b0: ldurb           w17, [x0, #-1]
    //     0xb124b4: and             x16, x17, x16, lsr #2
    //     0xb124b8: tst             x16, HEAP, lsr #32
    //     0xb124bc: b.eq            #0xb124c4
    //     0xb124c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb124c4: ldur            x0, [fp, #-0x20]
    // 0xb124c8: StoreField: r1->field_b = r0
    //     0xb124c8: stur            w0, [x1, #0xb]
    //     0xb124cc: ldurb           w16, [x1, #-1]
    //     0xb124d0: ldurb           w17, [x0, #-1]
    //     0xb124d4: and             x16, x17, x16, lsr #2
    //     0xb124d8: tst             x16, HEAP, lsr #32
    //     0xb124dc: b.eq            #0xb124e4
    //     0xb124e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb124e4: ldur            x0, [fp, #-8]
    // 0xb124e8: StoreField: r1->field_f = r0
    //     0xb124e8: stur            w0, [x1, #0xf]
    //     0xb124ec: ldurb           w16, [x1, #-1]
    //     0xb124f0: ldurb           w17, [x0, #-1]
    //     0xb124f4: and             x16, x17, x16, lsr #2
    //     0xb124f8: tst             x16, HEAP, lsr #32
    //     0xb124fc: b.eq            #0xb12504
    //     0xb12500: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb12504: r2 = false
    //     0xb12504: add             x2, NULL, #0x30  ; false
    // 0xb12508: StoreField: r1->field_27 = r2
    //     0xb12508: stur            w2, [x1, #0x27]
    // 0xb1250c: d0 = 1.000000
    //     0xb1250c: fmov            d0, #1.00000000
    // 0xb12510: StoreField: r1->field_1b = d0
    //     0xb12510: stur            d0, [x1, #0x1b]
    // 0xb12514: ldur            x0, [fp, #-0x10]
    // 0xb12518: StoreField: r1->field_3b = r0
    //     0xb12518: stur            w0, [x1, #0x3b]
    //     0xb1251c: ldurb           w16, [x1, #-1]
    //     0xb12520: ldurb           w17, [x0, #-1]
    //     0xb12524: and             x16, x17, x16, lsr #2
    //     0xb12528: tst             x16, HEAP, lsr #32
    //     0xb1252c: b.eq            #0xb12534
    //     0xb12530: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb12534: r0 = Null
    //     0xb12534: mov             x0, NULL
    // 0xb12538: LeaveFrame
    //     0xb12538: mov             SP, fp
    //     0xb1253c: ldp             fp, lr, [SP], #0x10
    // 0xb12540: ret
    //     0xb12540: ret             
    // 0xb12544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb12544: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb12548: b               #0xb12428
  }
  _ restoreContext(/* No info */) {
    // ** addr: 0xe54b68, size: 0x88
    // 0xe54b68: EnterFrame
    //     0xe54b68: stp             fp, lr, [SP, #-0x10]!
    //     0xe54b6c: mov             fp, SP
    // 0xe54b70: AllocStack(0x10)
    //     0xe54b70: sub             SP, SP, #0x10
    // 0xe54b74: SetupParameters(RichText this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe54b74: mov             x0, x2
    //     0xe54b78: mov             x4, x1
    //     0xe54b7c: mov             x3, x2
    //     0xe54b80: stur            x1, [fp, #-8]
    //     0xe54b84: stur            x2, [fp, #-0x10]
    // 0xe54b88: r2 = Null
    //     0xe54b88: mov             x2, NULL
    // 0xe54b8c: r1 = Null
    //     0xe54b8c: mov             x1, NULL
    // 0xe54b90: r4 = 60
    //     0xe54b90: movz            x4, #0x3c
    // 0xe54b94: branchIfSmi(r0, 0xe54ba0)
    //     0xe54b94: tbz             w0, #0, #0xe54ba0
    // 0xe54b98: r4 = LoadClassIdInstr(r0)
    //     0xe54b98: ldur            x4, [x0, #-1]
    //     0xe54b9c: ubfx            x4, x4, #0xc, #0x14
    // 0xe54ba0: cmp             x4, #0x335
    // 0xe54ba4: b.eq            #0xe54bbc
    // 0xe54ba8: r8 = RichTextContext
    //     0xe54ba8: add             x8, PP, #0x33, lsl #12  ; [pp+0x33728] Type: RichTextContext
    //     0xe54bac: ldr             x8, [x8, #0x728]
    // 0xe54bb0: r3 = Null
    //     0xe54bb0: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dd50] Null
    //     0xe54bb4: ldr             x3, [x3, #0xd50]
    // 0xe54bb8: r0 = DefaultTypeTest()
    //     0xe54bb8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe54bbc: ldur            x1, [fp, #-8]
    // 0xe54bc0: LoadField: r2 = r1->field_37
    //     0xe54bc0: ldur            w2, [x1, #0x37]
    // 0xe54bc4: DecompressPointer r2
    //     0xe54bc4: add             x2, x2, HEAP, lsl #32
    // 0xe54bc8: ldur            x1, [fp, #-0x10]
    // 0xe54bcc: LoadField: r3 = r1->field_1f
    //     0xe54bcc: ldur            x3, [x1, #0x1f]
    // 0xe54bd0: ArrayStore: r2[0] = r3  ; List_8
    //     0xe54bd0: stur            x3, [x2, #0x17]
    // 0xe54bd4: LoadField: d0 = r1->field_f
    //     0xe54bd4: ldur            d0, [x1, #0xf]
    // 0xe54bd8: fneg            d1, d0
    // 0xe54bdc: StoreField: r2->field_7 = d1
    //     0xe54bdc: stur            d1, [x2, #7]
    // 0xe54be0: r0 = Null
    //     0xe54be0: mov             x0, NULL
    // 0xe54be4: LeaveFrame
    //     0xe54be4: mov             SP, fp
    //     0xe54be8: ldp             fp, lr, [SP], #0x10
    // 0xe54bec: ret
    //     0xe54bec: ret             
  }
  _ saveContext(/* No info */) {
    // ** addr: 0xe636ec, size: 0xc
    // 0xe636ec: LoadField: r0 = r1->field_37
    //     0xe636ec: ldur            w0, [x1, #0x37]
    // 0xe636f0: DecompressPointer r0
    //     0xe636f0: add             x0, x0, HEAP, lsl #32
    // 0xe636f4: ret
    //     0xe636f4: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0xe64e84, size: 0x5f0
    // 0xe64e84: EnterFrame
    //     0xe64e84: stp             fp, lr, [SP, #-0x10]!
    //     0xe64e88: mov             fp, SP
    // 0xe64e8c: AllocStack(0xa0)
    //     0xe64e8c: sub             SP, SP, #0xa0
    // 0xe64e90: SetupParameters(RichText this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe64e90: mov             x0, x1
    //     0xe64e94: stur            x1, [fp, #-0x10]
    //     0xe64e98: stur            x2, [fp, #-0x18]
    // 0xe64e9c: CheckStackOverflow
    //     0xe64e9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe64ea0: cmp             SP, x16
    //     0xe64ea4: b.ls            #0xe653dc
    // 0xe64ea8: LoadField: r1 = r0->field_3f
    //     0xe64ea8: ldur            w1, [x0, #0x3f]
    // 0xe64eac: DecompressPointer r1
    //     0xe64eac: add             x1, x1, HEAP, lsl #32
    // 0xe64eb0: tbnz            w1, #4, #0xe64ef4
    // 0xe64eb4: LoadField: r3 = r2->field_b
    //     0xe64eb4: ldur            w3, [x2, #0xb]
    // 0xe64eb8: DecompressPointer r3
    //     0xe64eb8: add             x3, x3, HEAP, lsl #32
    // 0xe64ebc: stur            x3, [fp, #-8]
    // 0xe64ec0: cmp             w3, NULL
    // 0xe64ec4: b.eq            #0xe653e4
    // 0xe64ec8: mov             x1, x3
    // 0xe64ecc: r0 = saveContext()
    //     0xe64ecc: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe64ed0: ldur            x0, [fp, #-0x10]
    // 0xe64ed4: LoadField: r2 = r0->field_7
    //     0xe64ed4: ldur            w2, [x0, #7]
    // 0xe64ed8: DecompressPointer r2
    //     0xe64ed8: add             x2, x2, HEAP, lsl #32
    // 0xe64edc: cmp             w2, NULL
    // 0xe64ee0: b.eq            #0xe653e8
    // 0xe64ee4: ldur            x1, [fp, #-8]
    // 0xe64ee8: r0 = drawBox()
    //     0xe64ee8: bl              #0xe64ce4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::drawBox
    // 0xe64eec: ldur            x1, [fp, #-8]
    // 0xe64ef0: r0 = clipPath()
    //     0xe64ef0: bl              #0xe479b0  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::clipPath
    // 0xe64ef4: ldur            x0, [fp, #-0x10]
    // 0xe64ef8: LoadField: r3 = r0->field_33
    //     0xe64ef8: ldur            w3, [x0, #0x33]
    // 0xe64efc: DecompressPointer r3
    //     0xe64efc: add             x3, x3, HEAP, lsl #32
    // 0xe64f00: stur            x3, [fp, #-0x30]
    // 0xe64f04: LoadField: r1 = r3->field_b
    //     0xe64f04: ldur            w1, [x3, #0xb]
    // 0xe64f08: r4 = LoadInt32Instr(r1)
    //     0xe64f08: sbfx            x4, x1, #1, #0x1f
    // 0xe64f0c: stur            x4, [fp, #-0x28]
    // 0xe64f10: LoadField: r5 = r0->field_2f
    //     0xe64f10: ldur            w5, [x0, #0x2f]
    // 0xe64f14: DecompressPointer r5
    //     0xe64f14: add             x5, x5, HEAP, lsl #32
    // 0xe64f18: stur            x5, [fp, #-8]
    // 0xe64f1c: r1 = 0
    //     0xe64f1c: movz            x1, #0
    // 0xe64f20: CheckStackOverflow
    //     0xe64f20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe64f24: cmp             SP, x16
    //     0xe64f28: b.ls            #0xe653ec
    // 0xe64f2c: LoadField: r2 = r3->field_b
    //     0xe64f2c: ldur            w2, [x3, #0xb]
    // 0xe64f30: r6 = LoadInt32Instr(r2)
    //     0xe64f30: sbfx            x6, x2, #1, #0x1f
    // 0xe64f34: cmp             x4, x6
    // 0xe64f38: b.ne            #0xe653bc
    // 0xe64f3c: cmp             x1, x6
    // 0xe64f40: b.ge            #0xe64f84
    // 0xe64f44: LoadField: r2 = r3->field_f
    //     0xe64f44: ldur            w2, [x3, #0xf]
    // 0xe64f48: DecompressPointer r2
    //     0xe64f48: add             x2, x2, HEAP, lsl #32
    // 0xe64f4c: ArrayLoad: r6 = r2[r1]  ; Unknown_4
    //     0xe64f4c: add             x16, x2, x1, lsl #2
    //     0xe64f50: ldur            w6, [x16, #0xf]
    // 0xe64f54: DecompressPointer r6
    //     0xe64f54: add             x6, x6, HEAP, lsl #32
    // 0xe64f58: add             x7, x1, #1
    // 0xe64f5c: mov             x1, x6
    // 0xe64f60: mov             x2, x5
    // 0xe64f64: stur            x7, [fp, #-0x20]
    // 0xe64f68: r0 = _getBox()
    //     0xe64f68: bl              #0xe67db4  ; [package:pdf/src/widgets/text.dart] _TextDecoration::_getBox
    // 0xe64f6c: ldur            x1, [fp, #-0x20]
    // 0xe64f70: ldur            x0, [fp, #-0x10]
    // 0xe64f74: ldur            x3, [fp, #-0x30]
    // 0xe64f78: ldur            x5, [fp, #-8]
    // 0xe64f7c: ldur            x4, [fp, #-0x28]
    // 0xe64f80: b               #0xe64f20
    // 0xe64f84: mov             x3, x0
    // 0xe64f88: ldur            x4, [fp, #-0x18]
    // 0xe64f8c: LoadField: r0 = r3->field_37
    //     0xe64f8c: ldur            w0, [x3, #0x37]
    // 0xe64f90: DecompressPointer r0
    //     0xe64f90: add             x0, x0, HEAP, lsl #32
    // 0xe64f94: ArrayLoad: r2 = r0[0]  ; List_8
    //     0xe64f94: ldur            x2, [x0, #0x17]
    // 0xe64f98: LoadField: r5 = r0->field_1f
    //     0xe64f98: ldur            x5, [x0, #0x1f]
    // 0xe64f9c: r0 = BoxInt64Instr(r5)
    //     0xe64f9c: sbfiz           x0, x5, #1, #0x1f
    //     0xe64fa0: cmp             x5, x0, asr #1
    //     0xe64fa4: b.eq            #0xe64fb0
    //     0xe64fa8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe64fac: stur            x5, [x0, #7]
    // 0xe64fb0: str             x0, [SP]
    // 0xe64fb4: ldur            x1, [fp, #-8]
    // 0xe64fb8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe64fb8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe64fbc: r0 = sublist()
    //     0xe64fbc: bl              #0x6eabd0  ; [dart:core] _GrowableList::sublist
    // 0xe64fc0: mov             x3, x0
    // 0xe64fc4: stur            x3, [fp, #-0x60]
    // 0xe64fc8: LoadField: r4 = r3->field_7
    //     0xe64fc8: ldur            w4, [x3, #7]
    // 0xe64fcc: DecompressPointer r4
    //     0xe64fcc: add             x4, x4, HEAP, lsl #32
    // 0xe64fd0: stur            x4, [fp, #-0x58]
    // 0xe64fd4: LoadField: r0 = r3->field_b
    //     0xe64fd4: ldur            w0, [x3, #0xb]
    // 0xe64fd8: r5 = LoadInt32Instr(r0)
    //     0xe64fd8: sbfx            x5, x0, #1, #0x1f
    // 0xe64fdc: ldur            x6, [fp, #-0x18]
    // 0xe64fe0: stur            x5, [fp, #-0x28]
    // 0xe64fe4: LoadField: r7 = r6->field_b
    //     0xe64fe4: ldur            w7, [x6, #0xb]
    // 0xe64fe8: DecompressPointer r7
    //     0xe64fe8: add             x7, x7, HEAP, lsl #32
    // 0xe64fec: stur            x7, [fp, #-0x50]
    // 0xe64ff0: r10 = Null
    //     0xe64ff0: mov             x10, NULL
    // 0xe64ff4: r9 = Null
    //     0xe64ff4: mov             x9, NULL
    // 0xe64ff8: r0 = 0
    //     0xe64ff8: movz            x0, #0
    // 0xe64ffc: ldur            x8, [fp, #-0x10]
    // 0xe65000: stur            x10, [fp, #-0x40]
    // 0xe65004: stur            x9, [fp, #-0x48]
    // 0xe65008: CheckStackOverflow
    //     0xe65008: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6500c: cmp             SP, x16
    //     0xe65010: b.ls            #0xe653f4
    // 0xe65014: LoadField: r1 = r3->field_b
    //     0xe65014: ldur            w1, [x3, #0xb]
    // 0xe65018: r2 = LoadInt32Instr(r1)
    //     0xe65018: sbfx            x2, x1, #1, #0x1f
    // 0xe6501c: cmp             x5, x2
    // 0xe65020: b.ne            #0xe6539c
    // 0xe65024: cmp             x0, x2
    // 0xe65028: b.ge            #0xe652c8
    // 0xe6502c: LoadField: r1 = r3->field_f
    //     0xe6502c: ldur            w1, [x3, #0xf]
    // 0xe65030: DecompressPointer r1
    //     0xe65030: add             x1, x1, HEAP, lsl #32
    // 0xe65034: ArrayLoad: r11 = r1[r0]  ; Unknown_4
    //     0xe65034: add             x16, x1, x0, lsl #2
    //     0xe65038: ldur            w11, [x16, #0xf]
    // 0xe6503c: DecompressPointer r11
    //     0xe6503c: add             x11, x11, HEAP, lsl #32
    // 0xe65040: stur            x11, [fp, #-0x38]
    // 0xe65044: add             x12, x0, #1
    // 0xe65048: stur            x12, [fp, #-0x20]
    // 0xe6504c: cmp             w11, NULL
    // 0xe65050: b.ne            #0xe65084
    // 0xe65054: mov             x0, x11
    // 0xe65058: mov             x2, x4
    // 0xe6505c: r1 = Null
    //     0xe6505c: mov             x1, NULL
    // 0xe65060: cmp             w2, NULL
    // 0xe65064: b.eq            #0xe65084
    // 0xe65068: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe65068: ldur            w4, [x2, #0x17]
    // 0xe6506c: DecompressPointer r4
    //     0xe6506c: add             x4, x4, HEAP, lsl #32
    // 0xe65070: r8 = X0
    //     0xe65070: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe65074: LoadField: r9 = r4->field_7
    //     0xe65074: ldur            x9, [x4, #7]
    // 0xe65078: r3 = Null
    //     0xe65078: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dd68] Null
    //     0xe6507c: ldr             x3, [x3, #0xd68]
    // 0xe65080: blr             x9
    // 0xe65084: ldur            x1, [fp, #-0x40]
    // 0xe65088: ldur            x2, [fp, #-0x38]
    // 0xe6508c: LoadField: r3 = r2->field_7
    //     0xe6508c: ldur            w3, [x2, #7]
    // 0xe65090: DecompressPointer r3
    //     0xe65090: add             x3, x3, HEAP, lsl #32
    // 0xe65094: stur            x3, [fp, #-0x70]
    // 0xe65098: cmp             w3, w1
    // 0xe6509c: b.eq            #0xe6521c
    // 0xe650a0: LoadField: r1 = r3->field_b
    //     0xe650a0: ldur            w1, [x3, #0xb]
    // 0xe650a4: DecompressPointer r1
    //     0xe650a4: add             x1, x1, HEAP, lsl #32
    // 0xe650a8: stur            x1, [fp, #-0x68]
    // 0xe650ac: r0 = LoadClassIdInstr(r1)
    //     0xe650ac: ldur            x0, [x1, #-1]
    //     0xe650b0: ubfx            x0, x0, #0xc, #0x14
    // 0xe650b4: ldur            x16, [fp, #-0x48]
    // 0xe650b8: stp             x16, x1, [SP]
    // 0xe650bc: mov             lr, x0
    // 0xe650c0: ldr             lr, [x21, lr, lsl #3]
    // 0xe650c4: blr             lr
    // 0xe650c8: tbz             w0, #4, #0xe6520c
    // 0xe650cc: ldur            x0, [fp, #-0x68]
    // 0xe650d0: ldur            x3, [fp, #-0x50]
    // 0xe650d4: r4 = 6
    //     0xe650d4: movz            x4, #0x6
    // 0xe650d8: cmp             w3, NULL
    // 0xe650dc: b.eq            #0xe653fc
    // 0xe650e0: cmp             w0, NULL
    // 0xe650e4: b.eq            #0xe65400
    // 0xe650e8: LoadField: d0 = r0->field_f
    //     0xe650e8: ldur            d0, [x0, #0xf]
    // 0xe650ec: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xe650ec: ldur            d1, [x0, #0x17]
    // 0xe650f0: stur            d1, [fp, #-0x90]
    // 0xe650f4: LoadField: d2 = r0->field_1f
    //     0xe650f4: ldur            d2, [x0, #0x1f]
    // 0xe650f8: stur            d2, [fp, #-0x88]
    // 0xe650fc: r5 = inline_Allocate_Double()
    //     0xe650fc: ldp             x5, x1, [THR, #0x50]  ; THR::top
    //     0xe65100: add             x5, x5, #0x10
    //     0xe65104: cmp             x1, x5
    //     0xe65108: b.ls            #0xe65404
    //     0xe6510c: str             x5, [THR, #0x50]  ; THR::top
    //     0xe65110: sub             x5, x5, #0xf
    //     0xe65114: movz            x1, #0xe15c
    //     0xe65118: movk            x1, #0x3, lsl #16
    //     0xe6511c: stur            x1, [x5, #-1]
    // 0xe65120: StoreField: r5->field_7 = d0
    //     0xe65120: stur            d0, [x5, #7]
    // 0xe65124: mov             x2, x4
    // 0xe65128: stur            x5, [fp, #-0x78]
    // 0xe6512c: r1 = Null
    //     0xe6512c: mov             x1, NULL
    // 0xe65130: r0 = AllocateArray()
    //     0xe65130: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe65134: mov             x2, x0
    // 0xe65138: ldur            x0, [fp, #-0x78]
    // 0xe6513c: stur            x2, [fp, #-0x80]
    // 0xe65140: StoreField: r2->field_f = r0
    //     0xe65140: stur            w0, [x2, #0xf]
    // 0xe65144: ldur            d0, [fp, #-0x90]
    // 0xe65148: r0 = inline_Allocate_Double()
    //     0xe65148: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6514c: add             x0, x0, #0x10
    //     0xe65150: cmp             x1, x0
    //     0xe65154: b.ls            #0xe65430
    //     0xe65158: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6515c: sub             x0, x0, #0xf
    //     0xe65160: movz            x1, #0xe15c
    //     0xe65164: movk            x1, #0x3, lsl #16
    //     0xe65168: stur            x1, [x0, #-1]
    // 0xe6516c: StoreField: r0->field_7 = d0
    //     0xe6516c: stur            d0, [x0, #7]
    // 0xe65170: StoreField: r2->field_13 = r0
    //     0xe65170: stur            w0, [x2, #0x13]
    // 0xe65174: ldur            d0, [fp, #-0x88]
    // 0xe65178: r0 = inline_Allocate_Double()
    //     0xe65178: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6517c: add             x0, x0, #0x10
    //     0xe65180: cmp             x1, x0
    //     0xe65184: b.ls            #0xe65448
    //     0xe65188: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6518c: sub             x0, x0, #0xf
    //     0xe65190: movz            x1, #0xe15c
    //     0xe65194: movk            x1, #0x3, lsl #16
    //     0xe65198: stur            x1, [x0, #-1]
    // 0xe6519c: StoreField: r0->field_7 = d0
    //     0xe6519c: stur            d0, [x0, #7]
    // 0xe651a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xe651a0: stur            w0, [x2, #0x17]
    // 0xe651a4: r1 = <double>
    //     0xe651a4: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe651a8: r0 = AllocateGrowableArray()
    //     0xe651a8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe651ac: mov             x1, x0
    // 0xe651b0: ldur            x0, [fp, #-0x80]
    // 0xe651b4: stur            x1, [fp, #-0x78]
    // 0xe651b8: StoreField: r1->field_f = r0
    //     0xe651b8: stur            w0, [x1, #0xf]
    // 0xe651bc: r0 = 6
    //     0xe651bc: movz            x0, #0x6
    // 0xe651c0: StoreField: r1->field_b = r0
    //     0xe651c0: stur            w0, [x1, #0xb]
    // 0xe651c4: r0 = PdfNumList()
    //     0xe651c4: bl              #0xac4684  ; AllocatePdfNumListStub -> PdfNumList (size=0xc)
    // 0xe651c8: mov             x1, x0
    // 0xe651cc: ldur            x0, [fp, #-0x78]
    // 0xe651d0: StoreField: r1->field_7 = r0
    //     0xe651d0: stur            w0, [x1, #7]
    // 0xe651d4: ldur            x0, [fp, #-0x50]
    // 0xe651d8: LoadField: r2 = r0->field_f
    //     0xe651d8: ldur            w2, [x0, #0xf]
    // 0xe651dc: DecompressPointer r2
    //     0xe651dc: add             x2, x2, HEAP, lsl #32
    // 0xe651e0: LoadField: r4 = r0->field_13
    //     0xe651e0: ldur            w4, [x0, #0x13]
    // 0xe651e4: DecompressPointer r4
    //     0xe651e4: add             x4, x4, HEAP, lsl #32
    // 0xe651e8: mov             x3, x4
    // 0xe651ec: stur            x4, [fp, #-0x78]
    // 0xe651f0: r0 = output()
    //     0xe651f0: bl              #0xe7f3f0  ; [package:pdf/src/pdf/format/num.dart] PdfNumList::output
    // 0xe651f4: ldur            x1, [fp, #-0x78]
    // 0xe651f8: r2 = " rg "
    //     0xe651f8: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dd78] " rg "
    //     0xe651fc: ldr             x2, [x2, #0xd78]
    // 0xe65200: r0 = putString()
    //     0xe65200: bl              #0x7cb8d4  ; [package:pdf/src/pdf/format/stream.dart] PdfStream::putString
    // 0xe65204: ldur            x1, [fp, #-0x68]
    // 0xe65208: b               #0xe65210
    // 0xe6520c: ldur            x1, [fp, #-0x48]
    // 0xe65210: ldur            x3, [fp, #-0x70]
    // 0xe65214: mov             x9, x1
    // 0xe65218: b               #0xe65224
    // 0xe6521c: mov             x3, x1
    // 0xe65220: ldur            x9, [fp, #-0x48]
    // 0xe65224: ldur            x0, [fp, #-0x10]
    // 0xe65228: ldur            x1, [fp, #-0x38]
    // 0xe6522c: stur            x3, [fp, #-0x40]
    // 0xe65230: stur            x9, [fp, #-0x48]
    // 0xe65234: cmp             w3, NULL
    // 0xe65238: b.eq            #0xe65460
    // 0xe6523c: LoadField: r2 = r0->field_7
    //     0xe6523c: ldur            w2, [x0, #7]
    // 0xe65240: DecompressPointer r2
    //     0xe65240: add             x2, x2, HEAP, lsl #32
    // 0xe65244: cmp             w2, NULL
    // 0xe65248: b.eq            #0xe65464
    // 0xe6524c: LoadField: d0 = r2->field_7
    //     0xe6524c: ldur            d0, [x2, #7]
    // 0xe65250: stur            d0, [fp, #-0x90]
    // 0xe65254: LoadField: d1 = r2->field_f
    //     0xe65254: ldur            d1, [x2, #0xf]
    // 0xe65258: LoadField: d2 = r2->field_1f
    //     0xe65258: ldur            d2, [x2, #0x1f]
    // 0xe6525c: fadd            d3, d1, d2
    // 0xe65260: stur            d3, [fp, #-0x88]
    // 0xe65264: r0 = PdfPoint()
    //     0xe65264: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe65268: ldur            d0, [fp, #-0x90]
    // 0xe6526c: StoreField: r0->field_7 = d0
    //     0xe6526c: stur            d0, [x0, #7]
    // 0xe65270: ldur            d0, [fp, #-0x88]
    // 0xe65274: StoreField: r0->field_f = d0
    //     0xe65274: stur            d0, [x0, #0xf]
    // 0xe65278: ldur            x1, [fp, #-0x38]
    // 0xe6527c: r2 = LoadClassIdInstr(r1)
    //     0xe6527c: ldur            x2, [x1, #-1]
    //     0xe65280: ubfx            x2, x2, #0xc, #0x14
    // 0xe65284: mov             x5, x0
    // 0xe65288: mov             x0, x2
    // 0xe6528c: ldur            x2, [fp, #-0x18]
    // 0xe65290: ldur            x3, [fp, #-0x40]
    // 0xe65294: d0 = 1.000000
    //     0xe65294: fmov            d0, #1.00000000
    // 0xe65298: r0 = GDT[cid_x0 + -0xfdd]()
    //     0xe65298: sub             lr, x0, #0xfdd
    //     0xe6529c: ldr             lr, [x21, lr, lsl #3]
    //     0xe652a0: blr             lr
    // 0xe652a4: ldur            x10, [fp, #-0x40]
    // 0xe652a8: ldur            x9, [fp, #-0x48]
    // 0xe652ac: ldur            x0, [fp, #-0x20]
    // 0xe652b0: ldur            x6, [fp, #-0x18]
    // 0xe652b4: ldur            x3, [fp, #-0x60]
    // 0xe652b8: ldur            x7, [fp, #-0x50]
    // 0xe652bc: ldur            x4, [fp, #-0x58]
    // 0xe652c0: ldur            x5, [fp, #-0x28]
    // 0xe652c4: b               #0xe64ffc
    // 0xe652c8: ldur            x0, [fp, #-0x30]
    // 0xe652cc: LoadField: r1 = r0->field_b
    //     0xe652cc: ldur            w1, [x0, #0xb]
    // 0xe652d0: r4 = LoadInt32Instr(r1)
    //     0xe652d0: sbfx            x4, x1, #1, #0x1f
    // 0xe652d4: stur            x4, [fp, #-0x28]
    // 0xe652d8: r1 = 0
    //     0xe652d8: movz            x1, #0
    // 0xe652dc: ldur            x6, [fp, #-0x10]
    // 0xe652e0: CheckStackOverflow
    //     0xe652e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe652e4: cmp             SP, x16
    //     0xe652e8: b.ls            #0xe65468
    // 0xe652ec: LoadField: r2 = r0->field_b
    //     0xe652ec: ldur            w2, [x0, #0xb]
    // 0xe652f0: r3 = LoadInt32Instr(r2)
    //     0xe652f0: sbfx            x3, x2, #1, #0x1f
    // 0xe652f4: cmp             x4, x3
    // 0xe652f8: b.ne            #0xe65380
    // 0xe652fc: cmp             x1, x3
    // 0xe65300: b.ge            #0xe65350
    // 0xe65304: LoadField: r2 = r0->field_f
    //     0xe65304: ldur            w2, [x0, #0xf]
    // 0xe65308: DecompressPointer r2
    //     0xe65308: add             x2, x2, HEAP, lsl #32
    // 0xe6530c: ArrayLoad: r3 = r2[r1]  ; Unknown_4
    //     0xe6530c: add             x16, x2, x1, lsl #2
    //     0xe65310: ldur            w3, [x16, #0xf]
    // 0xe65314: DecompressPointer r3
    //     0xe65314: add             x3, x3, HEAP, lsl #32
    // 0xe65318: add             x7, x1, #1
    // 0xe6531c: stur            x7, [fp, #-0x20]
    // 0xe65320: LoadField: r1 = r6->field_7
    //     0xe65320: ldur            w1, [x6, #7]
    // 0xe65324: DecompressPointer r1
    //     0xe65324: add             x1, x1, HEAP, lsl #32
    // 0xe65328: mov             x16, x1
    // 0xe6532c: mov             x1, x3
    // 0xe65330: mov             x3, x16
    // 0xe65334: ldur            x2, [fp, #-0x18]
    // 0xe65338: ldur            x5, [fp, #-8]
    // 0xe6533c: r0 = foregroundPaint()
    //     0xe6533c: bl              #0xe65474  ; [package:pdf/src/widgets/text.dart] _TextDecoration::foregroundPaint
    // 0xe65340: ldur            x1, [fp, #-0x20]
    // 0xe65344: ldur            x0, [fp, #-0x30]
    // 0xe65348: ldur            x4, [fp, #-0x28]
    // 0xe6534c: b               #0xe652dc
    // 0xe65350: mov             x0, x6
    // 0xe65354: LoadField: r1 = r0->field_3f
    //     0xe65354: ldur            w1, [x0, #0x3f]
    // 0xe65358: DecompressPointer r1
    //     0xe65358: add             x1, x1, HEAP, lsl #32
    // 0xe6535c: tbnz            w1, #4, #0xe65370
    // 0xe65360: ldur            x1, [fp, #-0x50]
    // 0xe65364: cmp             w1, NULL
    // 0xe65368: b.eq            #0xe65470
    // 0xe6536c: r0 = restoreContext()
    //     0xe6536c: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe65370: r0 = Null
    //     0xe65370: mov             x0, NULL
    // 0xe65374: LeaveFrame
    //     0xe65374: mov             SP, fp
    //     0xe65378: ldp             fp, lr, [SP], #0x10
    // 0xe6537c: ret
    //     0xe6537c: ret             
    // 0xe65380: r0 = ConcurrentModificationError()
    //     0xe65380: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe65384: mov             x1, x0
    // 0xe65388: ldur            x0, [fp, #-0x30]
    // 0xe6538c: StoreField: r1->field_b = r0
    //     0xe6538c: stur            w0, [x1, #0xb]
    // 0xe65390: mov             x0, x1
    // 0xe65394: r0 = Throw()
    //     0xe65394: bl              #0xec04b8  ; ThrowStub
    // 0xe65398: brk             #0
    // 0xe6539c: mov             x0, x3
    // 0xe653a0: r0 = ConcurrentModificationError()
    //     0xe653a0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe653a4: mov             x1, x0
    // 0xe653a8: ldur            x0, [fp, #-0x60]
    // 0xe653ac: StoreField: r1->field_b = r0
    //     0xe653ac: stur            w0, [x1, #0xb]
    // 0xe653b0: mov             x0, x1
    // 0xe653b4: r0 = Throw()
    //     0xe653b4: bl              #0xec04b8  ; ThrowStub
    // 0xe653b8: brk             #0
    // 0xe653bc: mov             x0, x3
    // 0xe653c0: r0 = ConcurrentModificationError()
    //     0xe653c0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe653c4: mov             x1, x0
    // 0xe653c8: ldur            x0, [fp, #-0x30]
    // 0xe653cc: StoreField: r1->field_b = r0
    //     0xe653cc: stur            w0, [x1, #0xb]
    // 0xe653d0: mov             x0, x1
    // 0xe653d4: r0 = Throw()
    //     0xe653d4: bl              #0xec04b8  ; ThrowStub
    // 0xe653d8: brk             #0
    // 0xe653dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe653dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe653e0: b               #0xe64ea8
    // 0xe653e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe653e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe653e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe653e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe653ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe653ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe653f0: b               #0xe64f2c
    // 0xe653f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe653f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe653f8: b               #0xe65014
    // 0xe653fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe653fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe65400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe65400: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe65404: stp             q1, q2, [SP, #-0x20]!
    // 0xe65408: SaveReg d0
    //     0xe65408: str             q0, [SP, #-0x10]!
    // 0xe6540c: stp             x3, x4, [SP, #-0x10]!
    // 0xe65410: SaveReg r0
    //     0xe65410: str             x0, [SP, #-8]!
    // 0xe65414: r0 = AllocateDouble()
    //     0xe65414: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe65418: mov             x5, x0
    // 0xe6541c: RestoreReg r0
    //     0xe6541c: ldr             x0, [SP], #8
    // 0xe65420: ldp             x3, x4, [SP], #0x10
    // 0xe65424: RestoreReg d0
    //     0xe65424: ldr             q0, [SP], #0x10
    // 0xe65428: ldp             q1, q2, [SP], #0x20
    // 0xe6542c: b               #0xe65120
    // 0xe65430: SaveReg d0
    //     0xe65430: str             q0, [SP, #-0x10]!
    // 0xe65434: SaveReg r2
    //     0xe65434: str             x2, [SP, #-8]!
    // 0xe65438: r0 = AllocateDouble()
    //     0xe65438: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6543c: RestoreReg r2
    //     0xe6543c: ldr             x2, [SP], #8
    // 0xe65440: RestoreReg d0
    //     0xe65440: ldr             q0, [SP], #0x10
    // 0xe65444: b               #0xe6516c
    // 0xe65448: SaveReg d0
    //     0xe65448: str             q0, [SP, #-0x10]!
    // 0xe6544c: SaveReg r2
    //     0xe6544c: str             x2, [SP, #-8]!
    // 0xe65450: r0 = AllocateDouble()
    //     0xe65450: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe65454: RestoreReg r2
    //     0xe65454: ldr             x2, [SP], #8
    // 0xe65458: RestoreReg d0
    //     0xe65458: ldr             q0, [SP], #0x10
    // 0xe6545c: b               #0xe6519c
    // 0xe65460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe65460: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe65464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe65464: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe65468: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe65468: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6546c: b               #0xe652ec
    // 0xe65470: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe65470: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ canSpan(/* No info */) {
    // ** addr: 0xe7db44, size: 0x24
    // 0xe7db44: LoadField: r2 = r1->field_3b
    //     0xe7db44: ldur            w2, [x1, #0x3b]
    // 0xe7db48: DecompressPointer r2
    //     0xe7db48: add             x2, x2, HEAP, lsl #32
    // 0xe7db4c: r16 = Instance_TextOverflow
    //     0xe7db4c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dd60] Obj!TextOverflow@e2e581
    //     0xe7db50: ldr             x16, [x16, #0xd60]
    // 0xe7db54: cmp             w2, w16
    // 0xe7db58: r16 = true
    //     0xe7db58: add             x16, NULL, #0x20  ; true
    // 0xe7db5c: r17 = false
    //     0xe7db5c: add             x17, NULL, #0x30  ; false
    // 0xe7db60: csel            x0, x16, x17, eq
    // 0xe7db64: ret
    //     0xe7db64: ret             
  }
  _ layout(/* No info */) {
    // ** addr: 0xe90ac0, size: 0xd20
    // 0xe90ac0: EnterFrame
    //     0xe90ac0: stp             fp, lr, [SP, #-0x10]!
    //     0xe90ac4: mov             fp, SP
    // 0xe90ac8: AllocStack(0xc0)
    //     0xe90ac8: sub             SP, SP, #0xc0
    // 0xe90acc: SetupParameters(RichText this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xe90acc: mov             x0, x1
    //     0xe90ad0: stur            x1, [fp, #-8]
    //     0xe90ad4: mov             x1, x3
    //     0xe90ad8: stur            x2, [fp, #-0x10]
    //     0xe90adc: stur            x3, [fp, #-0x18]
    // 0xe90ae0: CheckStackOverflow
    //     0xe90ae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe90ae4: cmp             SP, x16
    //     0xe90ae8: b.ls            #0xe916a8
    // 0xe90aec: r1 = 14
    //     0xe90aec: movz            x1, #0xe
    // 0xe90af0: r0 = AllocateContext()
    //     0xe90af0: bl              #0xec126c  ; AllocateContextStub
    // 0xe90af4: mov             x2, x0
    // 0xe90af8: ldur            x0, [fp, #-8]
    // 0xe90afc: stur            x2, [fp, #-0x20]
    // 0xe90b00: StoreField: r2->field_f = r0
    //     0xe90b00: stur            w0, [x2, #0xf]
    // 0xe90b04: ldur            x1, [fp, #-0x10]
    // 0xe90b08: StoreField: r2->field_13 = r1
    //     0xe90b08: stur            w1, [x2, #0x13]
    // 0xe90b0c: LoadField: r3 = r0->field_2f
    //     0xe90b0c: ldur            w3, [x0, #0x2f]
    // 0xe90b10: DecompressPointer r3
    //     0xe90b10: add             x3, x3, HEAP, lsl #32
    // 0xe90b14: mov             x1, x3
    // 0xe90b18: stur            x3, [fp, #-0x10]
    // 0xe90b1c: r0 = clear()
    //     0xe90b1c: bl              #0xbd9e80  ; [dart:core] _GrowableList::clear
    // 0xe90b20: ldur            x0, [fp, #-8]
    // 0xe90b24: LoadField: r2 = r0->field_33
    //     0xe90b24: ldur            w2, [x0, #0x33]
    // 0xe90b28: DecompressPointer r2
    //     0xe90b28: add             x2, x2, HEAP, lsl #32
    // 0xe90b2c: mov             x1, x2
    // 0xe90b30: stur            x2, [fp, #-0x28]
    // 0xe90b34: r0 = clear()
    //     0xe90b34: bl              #0xbd9e80  ; [dart:core] _GrowableList::clear
    // 0xe90b38: ldur            x2, [fp, #-0x20]
    // 0xe90b3c: LoadField: r1 = r2->field_13
    //     0xe90b3c: ldur            w1, [x2, #0x13]
    // 0xe90b40: DecompressPointer r1
    //     0xe90b40: add             x1, x1, HEAP, lsl #32
    // 0xe90b44: r0 = of()
    //     0xe90b44: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xe90b48: ldur            x2, [fp, #-0x20]
    // 0xe90b4c: r0 = true
    //     0xe90b4c: add             x0, NULL, #0x20  ; true
    // 0xe90b50: ArrayStore: r2[0] = r0  ; List_4
    //     0xe90b50: stur            w0, [x2, #0x17]
    // 0xe90b54: LoadField: r1 = r2->field_13
    //     0xe90b54: ldur            w1, [x2, #0x13]
    // 0xe90b58: DecompressPointer r1
    //     0xe90b58: add             x1, x1, HEAP, lsl #32
    // 0xe90b5c: r0 = of()
    //     0xe90b5c: bl              #0xb137c8  ; [package:pdf/src/widgets/text_style.dart] Directionality::of
    // 0xe90b60: ldur            x2, [fp, #-0x20]
    // 0xe90b64: r3 = Instance_TextDirection
    //     0xe90b64: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e2e0] Obj!TextDirection@e2e5a1
    //     0xe90b68: ldr             x3, [x3, #0x2e0]
    // 0xe90b6c: StoreField: r2->field_1b = r3
    //     0xe90b6c: stur            w3, [x2, #0x1b]
    // 0xe90b70: ldur            x4, [fp, #-8]
    // 0xe90b74: LoadField: r0 = r4->field_f
    //     0xe90b74: ldur            w0, [x4, #0xf]
    // 0xe90b78: DecompressPointer r0
    //     0xe90b78: add             x0, x0, HEAP, lsl #32
    // 0xe90b7c: cmp             w0, NULL
    // 0xe90b80: b.ne            #0xe90b88
    // 0xe90b84: r0 = Null
    //     0xe90b84: mov             x0, NULL
    // 0xe90b88: cmp             w0, NULL
    // 0xe90b8c: b.ne            #0xe90b98
    // 0xe90b90: r0 = Instance_TextAlign
    //     0xe90b90: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e0c8] Obj!TextAlign@e2e641
    //     0xe90b94: ldr             x0, [x0, #0xc8]
    // 0xe90b98: StoreField: r4->field_13 = r0
    //     0xe90b98: stur            w0, [x4, #0x13]
    //     0xe90b9c: ldurb           w16, [x4, #-1]
    //     0xe90ba0: ldurb           w17, [x0, #-1]
    //     0xe90ba4: and             x16, x17, x16, lsr #2
    //     0xe90ba8: tst             x16, HEAP, lsr #32
    //     0xe90bac: b.eq            #0xe90bb4
    //     0xe90bb0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe90bb4: LoadField: r0 = r4->field_3b
    //     0xe90bb4: ldur            w0, [x4, #0x3b]
    // 0xe90bb8: DecompressPointer r0
    //     0xe90bb8: add             x0, x0, HEAP, lsl #32
    // 0xe90bbc: cmp             w0, NULL
    // 0xe90bc0: b.ne            #0xe90bd0
    // 0xe90bc4: r5 = Instance_TextOverflow
    //     0xe90bc4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e1f8] Obj!TextOverflow@e2e561
    //     0xe90bc8: ldr             x5, [x5, #0x1f8]
    // 0xe90bcc: b               #0xe90bd4
    // 0xe90bd0: mov             x5, x0
    // 0xe90bd4: ldur            x0, [fp, #-0x18]
    // 0xe90bd8: d0 = inf
    //     0xe90bd8: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe90bdc: stur            x5, [fp, #-0x30]
    // 0xe90be0: LoadField: d1 = r0->field_f
    //     0xe90be0: ldur            d1, [x0, #0xf]
    // 0xe90be4: fcmp            d0, d1
    // 0xe90be8: b.le            #0xe90bf4
    // 0xe90bec: mov             x3, x0
    // 0xe90bf0: b               #0xe90c10
    // 0xe90bf4: mov             x1, x0
    // 0xe90bf8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe90bf8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe90bfc: r0 = constrainWidth()
    //     0xe90bfc: bl              #0xe8e794  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainWidth
    // 0xe90c00: mov             v1.16b, v0.16b
    // 0xe90c04: ldur            x3, [fp, #-0x18]
    // 0xe90c08: ldur            x2, [fp, #-0x20]
    // 0xe90c0c: d0 = inf
    //     0xe90c0c: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe90c10: stur            d1, [fp, #-0xa0]
    // 0xe90c14: r0 = inline_Allocate_Double()
    //     0xe90c14: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe90c18: add             x0, x0, #0x10
    //     0xe90c1c: cmp             x1, x0
    //     0xe90c20: b.ls            #0xe916b0
    //     0xe90c24: str             x0, [THR, #0x50]  ; THR::top
    //     0xe90c28: sub             x0, x0, #0xf
    //     0xe90c2c: movz            x1, #0xe15c
    //     0xe90c30: movk            x1, #0x3, lsl #16
    //     0xe90c34: stur            x1, [x0, #-1]
    // 0xe90c38: StoreField: r0->field_7 = d1
    //     0xe90c38: stur            d1, [x0, #7]
    // 0xe90c3c: StoreField: r2->field_1f = r0
    //     0xe90c3c: stur            w0, [x2, #0x1f]
    //     0xe90c40: ldurb           w16, [x2, #-1]
    //     0xe90c44: ldurb           w17, [x0, #-1]
    //     0xe90c48: and             x16, x17, x16, lsr #2
    //     0xe90c4c: tst             x16, HEAP, lsr #32
    //     0xe90c50: b.eq            #0xe90c58
    //     0xe90c54: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe90c58: LoadField: d2 = r3->field_1f
    //     0xe90c58: ldur            d2, [x3, #0x1f]
    // 0xe90c5c: fcmp            d0, d2
    // 0xe90c60: b.le            #0xe90c70
    // 0xe90c64: mov             v0.16b, v2.16b
    // 0xe90c68: mov             x3, x2
    // 0xe90c6c: b               #0xe90c80
    // 0xe90c70: mov             x1, x3
    // 0xe90c74: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe90c74: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe90c78: r0 = constrainHeight()
    //     0xe90c78: bl              #0xe8e66c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainHeight
    // 0xe90c7c: ldur            x3, [fp, #-0x20]
    // 0xe90c80: ldur            x4, [fp, #-8]
    // 0xe90c84: r1 = 0.000000
    //     0xe90c84: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe90c88: stur            d0, [fp, #-0xa8]
    // 0xe90c8c: r0 = inline_Allocate_Double()
    //     0xe90c8c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe90c90: add             x0, x0, #0x10
    //     0xe90c94: cmp             x2, x0
    //     0xe90c98: b.ls            #0xe916c8
    //     0xe90c9c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe90ca0: sub             x0, x0, #0xf
    //     0xe90ca4: movz            x2, #0xe15c
    //     0xe90ca8: movk            x2, #0x3, lsl #16
    //     0xe90cac: stur            x2, [x0, #-1]
    // 0xe90cb0: StoreField: r0->field_7 = d0
    //     0xe90cb0: stur            d0, [x0, #7]
    // 0xe90cb4: StoreField: r3->field_23 = r0
    //     0xe90cb4: stur            w0, [x3, #0x23]
    //     0xe90cb8: ldurb           w16, [x3, #-1]
    //     0xe90cbc: ldurb           w17, [x0, #-1]
    //     0xe90cc0: and             x16, x17, x16, lsr #2
    //     0xe90cc4: tst             x16, HEAP, lsr #32
    //     0xe90cc8: b.eq            #0xe90cd0
    //     0xe90ccc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe90cd0: StoreField: r3->field_27 = r1
    //     0xe90cd0: stur            w1, [x3, #0x27]
    // 0xe90cd4: LoadField: r5 = r4->field_37
    //     0xe90cd4: ldur            w5, [x4, #0x37]
    // 0xe90cd8: DecompressPointer r5
    //     0xe90cd8: add             x5, x5, HEAP, lsl #32
    // 0xe90cdc: stur            x5, [fp, #-0x38]
    // 0xe90ce0: LoadField: d1 = r5->field_7
    //     0xe90ce0: ldur            d1, [x5, #7]
    // 0xe90ce4: r0 = inline_Allocate_Double()
    //     0xe90ce4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe90ce8: add             x0, x0, #0x10
    //     0xe90cec: cmp             x2, x0
    //     0xe90cf0: b.ls            #0xe916e8
    //     0xe90cf4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe90cf8: sub             x0, x0, #0xf
    //     0xe90cfc: movz            x2, #0xe15c
    //     0xe90d00: movk            x2, #0x3, lsl #16
    //     0xe90d04: stur            x2, [x0, #-1]
    // 0xe90d08: StoreField: r0->field_7 = d1
    //     0xe90d08: stur            d1, [x0, #7]
    // 0xe90d0c: StoreField: r3->field_2b = r0
    //     0xe90d0c: stur            w0, [x3, #0x2b]
    //     0xe90d10: ldurb           w16, [x3, #-1]
    //     0xe90d14: ldurb           w17, [x0, #-1]
    //     0xe90d18: and             x16, x17, x16, lsr #2
    //     0xe90d1c: tst             x16, HEAP, lsr #32
    //     0xe90d20: b.eq            #0xe90d28
    //     0xe90d24: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe90d28: StoreField: r3->field_2f = r1
    //     0xe90d28: stur            w1, [x3, #0x2f]
    // 0xe90d2c: StoreField: r3->field_33 = r1
    //     0xe90d2c: stur            w1, [x3, #0x33]
    // 0xe90d30: r1 = <_Line>
    //     0xe90d30: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e0d0] TypeArguments: <_Line>
    //     0xe90d34: ldr             x1, [x1, #0xd0]
    // 0xe90d38: r2 = 0
    //     0xe90d38: movz            x2, #0
    // 0xe90d3c: r0 = _GrowableList()
    //     0xe90d3c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe90d40: mov             x4, x0
    // 0xe90d44: ldur            x3, [fp, #-0x20]
    // 0xe90d48: stur            x4, [fp, #-0x40]
    // 0xe90d4c: StoreField: r3->field_37 = r0
    //     0xe90d4c: stur            w0, [x3, #0x37]
    //     0xe90d50: ldurb           w16, [x3, #-1]
    //     0xe90d54: ldurb           w17, [x0, #-1]
    //     0xe90d58: and             x16, x17, x16, lsr #2
    //     0xe90d5c: tst             x16, HEAP, lsr #32
    //     0xe90d60: b.eq            #0xe90d68
    //     0xe90d64: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe90d68: StoreField: r3->field_3b = rZR
    //     0xe90d68: stur            wzr, [x3, #0x3b]
    // 0xe90d6c: StoreField: r3->field_3f = rZR
    //     0xe90d6c: stur            wzr, [x3, #0x3f]
    // 0xe90d70: r0 = false
    //     0xe90d70: add             x0, NULL, #0x30  ; false
    // 0xe90d74: StoreField: r3->field_43 = r0
    //     0xe90d74: stur            w0, [x3, #0x43]
    // 0xe90d78: ldur            x5, [fp, #-8]
    // 0xe90d7c: LoadField: r1 = r5->field_43
    //     0xe90d7c: ldur            w1, [x5, #0x43]
    // 0xe90d80: DecompressPointer r1
    //     0xe90d80: add             x1, x1, HEAP, lsl #32
    // 0xe90d84: cmp             w1, NULL
    // 0xe90d88: b.ne            #0xe90dc0
    // 0xe90d8c: LoadField: r2 = r3->field_13
    //     0xe90d8c: ldur            w2, [x3, #0x13]
    // 0xe90d90: DecompressPointer r2
    //     0xe90d90: add             x2, x2, HEAP, lsl #32
    // 0xe90d94: mov             x1, x5
    // 0xe90d98: r0 = _preProcessSpans()
    //     0xe90d98: bl              #0xe91df4  ; [package:pdf/src/widgets/text.dart] RichText::_preProcessSpans
    // 0xe90d9c: ldur            x3, [fp, #-8]
    // 0xe90da0: StoreField: r3->field_43 = r0
    //     0xe90da0: stur            w0, [x3, #0x43]
    //     0xe90da4: ldurb           w16, [x3, #-1]
    //     0xe90da8: ldurb           w17, [x0, #-1]
    //     0xe90dac: and             x16, x17, x16, lsr #2
    //     0xe90db0: tst             x16, HEAP, lsr #32
    //     0xe90db4: b.eq            #0xe90dbc
    //     0xe90db8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe90dbc: b               #0xe90dc4
    // 0xe90dc0: mov             x3, x5
    // 0xe90dc4: ldur            x0, [fp, #-0x20]
    // 0xe90dc8: mov             x2, x0
    // 0xe90dcc: r1 = Function '_buildLines':.
    //     0xe90dcc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e0d8] AnonymousClosure: (0xe93034), in [package:pdf/src/widgets/text.dart] RichText::layout (0xe90ac0)
    //     0xe90dd0: ldr             x1, [x1, #0xd8]
    // 0xe90dd4: r0 = AllocateClosure()
    //     0xe90dd4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe90dd8: str             x0, [SP]
    // 0xe90ddc: ClosureCall
    //     0xe90ddc: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xe90de0: ldur            x2, [x0, #0x1f]
    //     0xe90de4: blr             x2
    // 0xe90de8: ldur            x0, [fp, #-0x20]
    // 0xe90dec: LoadField: r1 = r0->field_3b
    //     0xe90dec: ldur            w1, [x0, #0x3b]
    // 0xe90df0: DecompressPointer r1
    //     0xe90df0: add             x1, x1, HEAP, lsl #32
    // 0xe90df4: r2 = LoadInt32Instr(r1)
    //     0xe90df4: sbfx            x2, x1, #1, #0x1f
    //     0xe90df8: tbz             w1, #0, #0xe90e00
    //     0xe90dfc: ldur            x2, [x1, #7]
    // 0xe90e00: stur            x2, [fp, #-0x60]
    // 0xe90e04: cmp             x2, #0
    // 0xe90e08: b.le            #0xe90f84
    // 0xe90e0c: ldur            x1, [fp, #-8]
    // 0xe90e10: ldur            x3, [fp, #-0x40]
    // 0xe90e14: LoadField: r4 = r0->field_3f
    //     0xe90e14: ldur            w4, [x0, #0x3f]
    // 0xe90e18: DecompressPointer r4
    //     0xe90e18: add             x4, x4, HEAP, lsl #32
    // 0xe90e1c: stur            x4, [fp, #-0x58]
    // 0xe90e20: LoadField: r5 = r0->field_33
    //     0xe90e20: ldur            w5, [x0, #0x33]
    // 0xe90e24: DecompressPointer r5
    //     0xe90e24: add             x5, x5, HEAP, lsl #32
    // 0xe90e28: stur            x5, [fp, #-0x50]
    // 0xe90e2c: LoadField: r6 = r0->field_27
    //     0xe90e2c: ldur            w6, [x0, #0x27]
    // 0xe90e30: DecompressPointer r6
    //     0xe90e30: add             x6, x6, HEAP, lsl #32
    // 0xe90e34: stur            x6, [fp, #-0x48]
    // 0xe90e38: r0 = _Line()
    //     0xe90e38: bl              #0xe91de8  ; Allocate_LineStub -> _Line (size=0x34)
    // 0xe90e3c: mov             x2, x0
    // 0xe90e40: ldur            x0, [fp, #-8]
    // 0xe90e44: stur            x2, [fp, #-0x68]
    // 0xe90e48: StoreField: r2->field_7 = r0
    //     0xe90e48: stur            w0, [x2, #7]
    // 0xe90e4c: ldur            x1, [fp, #-0x58]
    // 0xe90e50: r3 = LoadInt32Instr(r1)
    //     0xe90e50: sbfx            x3, x1, #1, #0x1f
    //     0xe90e54: tbz             w1, #0, #0xe90e5c
    //     0xe90e58: ldur            x3, [x1, #7]
    // 0xe90e5c: StoreField: r2->field_b = r3
    //     0xe90e5c: stur            x3, [x2, #0xb]
    // 0xe90e60: ldur            x1, [fp, #-0x60]
    // 0xe90e64: StoreField: r2->field_13 = r1
    //     0xe90e64: stur            x1, [x2, #0x13]
    // 0xe90e68: ldur            x1, [fp, #-0x50]
    // 0xe90e6c: LoadField: d0 = r1->field_7
    //     0xe90e6c: ldur            d0, [x1, #7]
    // 0xe90e70: StoreField: r2->field_1b = d0
    //     0xe90e70: stur            d0, [x2, #0x1b]
    // 0xe90e74: ldur            x1, [fp, #-0x48]
    // 0xe90e78: LoadField: d0 = r1->field_7
    //     0xe90e78: ldur            d0, [x1, #7]
    // 0xe90e7c: StoreField: r2->field_23 = d0
    //     0xe90e7c: stur            d0, [x2, #0x23]
    // 0xe90e80: r1 = Instance_TextDirection
    //     0xe90e80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e2e0] Obj!TextDirection@e2e5a1
    //     0xe90e84: ldr             x1, [x1, #0x2e0]
    // 0xe90e88: StoreField: r2->field_2b = r1
    //     0xe90e88: stur            w1, [x2, #0x2b]
    // 0xe90e8c: r1 = false
    //     0xe90e8c: add             x1, NULL, #0x30  ; false
    // 0xe90e90: StoreField: r2->field_2f = r1
    //     0xe90e90: stur            w1, [x2, #0x2f]
    // 0xe90e94: ldur            x3, [fp, #-0x40]
    // 0xe90e98: LoadField: r1 = r3->field_b
    //     0xe90e98: ldur            w1, [x3, #0xb]
    // 0xe90e9c: LoadField: r4 = r3->field_f
    //     0xe90e9c: ldur            w4, [x3, #0xf]
    // 0xe90ea0: DecompressPointer r4
    //     0xe90ea0: add             x4, x4, HEAP, lsl #32
    // 0xe90ea4: LoadField: r5 = r4->field_b
    //     0xe90ea4: ldur            w5, [x4, #0xb]
    // 0xe90ea8: r4 = LoadInt32Instr(r1)
    //     0xe90ea8: sbfx            x4, x1, #1, #0x1f
    // 0xe90eac: stur            x4, [fp, #-0x60]
    // 0xe90eb0: r1 = LoadInt32Instr(r5)
    //     0xe90eb0: sbfx            x1, x5, #1, #0x1f
    // 0xe90eb4: cmp             x4, x1
    // 0xe90eb8: b.ne            #0xe90ec4
    // 0xe90ebc: mov             x1, x3
    // 0xe90ec0: r0 = _growToNextCapacity()
    //     0xe90ec0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe90ec4: ldur            x4, [fp, #-0x20]
    // 0xe90ec8: ldur            x2, [fp, #-0x40]
    // 0xe90ecc: ldur            x3, [fp, #-0x60]
    // 0xe90ed0: add             x0, x3, #1
    // 0xe90ed4: lsl             x1, x0, #1
    // 0xe90ed8: StoreField: r2->field_b = r1
    //     0xe90ed8: stur            w1, [x2, #0xb]
    // 0xe90edc: LoadField: r1 = r2->field_f
    //     0xe90edc: ldur            w1, [x2, #0xf]
    // 0xe90ee0: DecompressPointer r1
    //     0xe90ee0: add             x1, x1, HEAP, lsl #32
    // 0xe90ee4: ldur            x0, [fp, #-0x68]
    // 0xe90ee8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe90ee8: add             x25, x1, x3, lsl #2
    //     0xe90eec: add             x25, x25, #0xf
    //     0xe90ef0: str             w0, [x25]
    //     0xe90ef4: tbz             w0, #0, #0xe90f10
    //     0xe90ef8: ldurb           w16, [x1, #-1]
    //     0xe90efc: ldurb           w17, [x0, #-1]
    //     0xe90f00: and             x16, x17, x16, lsr #2
    //     0xe90f04: tst             x16, HEAP, lsr #32
    //     0xe90f08: b.eq            #0xe90f10
    //     0xe90f0c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe90f10: LoadField: r0 = r4->field_2b
    //     0xe90f10: ldur            w0, [x4, #0x2b]
    // 0xe90f14: DecompressPointer r0
    //     0xe90f14: add             x0, x0, HEAP, lsl #32
    // 0xe90f18: LoadField: r1 = r4->field_33
    //     0xe90f18: ldur            w1, [x4, #0x33]
    // 0xe90f1c: DecompressPointer r1
    //     0xe90f1c: add             x1, x1, HEAP, lsl #32
    // 0xe90f20: LoadField: r3 = r4->field_2f
    //     0xe90f20: ldur            w3, [x4, #0x2f]
    // 0xe90f24: DecompressPointer r3
    //     0xe90f24: add             x3, x3, HEAP, lsl #32
    // 0xe90f28: LoadField: d0 = r1->field_7
    //     0xe90f28: ldur            d0, [x1, #7]
    // 0xe90f2c: LoadField: d1 = r3->field_7
    //     0xe90f2c: ldur            d1, [x3, #7]
    // 0xe90f30: fsub            d2, d0, d1
    // 0xe90f34: LoadField: d0 = r0->field_7
    //     0xe90f34: ldur            d0, [x0, #7]
    // 0xe90f38: fadd            d1, d0, d2
    // 0xe90f3c: r0 = inline_Allocate_Double()
    //     0xe90f3c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe90f40: add             x0, x0, #0x10
    //     0xe90f44: cmp             x1, x0
    //     0xe90f48: b.ls            #0xe91708
    //     0xe90f4c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe90f50: sub             x0, x0, #0xf
    //     0xe90f54: movz            x1, #0xe15c
    //     0xe90f58: movk            x1, #0x3, lsl #16
    //     0xe90f5c: stur            x1, [x0, #-1]
    // 0xe90f60: StoreField: r0->field_7 = d1
    //     0xe90f60: stur            d1, [x0, #7]
    // 0xe90f64: StoreField: r4->field_2b = r0
    //     0xe90f64: stur            w0, [x4, #0x2b]
    //     0xe90f68: ldurb           w16, [x4, #-1]
    //     0xe90f6c: ldurb           w17, [x0, #-1]
    //     0xe90f70: and             x16, x17, x16, lsr #2
    //     0xe90f74: tst             x16, HEAP, lsr #32
    //     0xe90f78: b.eq            #0xe90f80
    //     0xe90f7c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe90f80: b               #0xe90f8c
    // 0xe90f84: mov             x4, x0
    // 0xe90f88: ldur            x2, [fp, #-0x40]
    // 0xe90f8c: LoadField: r0 = r4->field_43
    //     0xe90f8c: ldur            w0, [x4, #0x43]
    // 0xe90f90: DecompressPointer r0
    //     0xe90f90: add             x0, x0, HEAP, lsl #32
    // 0xe90f94: tbnz            w0, #4, #0xe90fa4
    // 0xe90f98: ldur            d0, [fp, #-0xa0]
    // 0xe90f9c: ldur            x1, [fp, #-0x18]
    // 0xe90fa0: b               #0xe90fac
    // 0xe90fa4: ldur            x1, [fp, #-0x18]
    // 0xe90fa8: LoadField: d0 = r1->field_7
    //     0xe90fa8: ldur            d0, [x1, #7]
    // 0xe90fac: LoadField: r3 = r2->field_b
    //     0xe90fac: ldur            w3, [x2, #0xb]
    // 0xe90fb0: r5 = LoadInt32Instr(r3)
    //     0xe90fb0: sbfx            x5, x3, #1, #0x1f
    // 0xe90fb4: stur            x5, [fp, #-0x70]
    // 0xe90fb8: cbz             w3, #0xe91234
    // 0xe90fbc: tbz             w0, #4, #0xe91180
    // 0xe90fc0: r0 = inline_Allocate_Double()
    //     0xe90fc0: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xe90fc4: add             x0, x0, #0x10
    //     0xe90fc8: cmp             x3, x0
    //     0xe90fcc: b.ls            #0xe91720
    //     0xe90fd0: str             x0, [THR, #0x50]  ; THR::top
    //     0xe90fd4: sub             x0, x0, #0xf
    //     0xe90fd8: movz            x3, #0xe15c
    //     0xe90fdc: movk            x3, #0x3, lsl #16
    //     0xe90fe0: stur            x3, [x0, #-1]
    // 0xe90fe4: StoreField: r0->field_7 = d0
    //     0xe90fe4: stur            d0, [x0, #7]
    // 0xe90fe8: mov             x3, x0
    // 0xe90fec: r0 = 0
    //     0xe90fec: movz            x0, #0
    // 0xe90ff0: stur            x3, [fp, #-0x50]
    // 0xe90ff4: CheckStackOverflow
    //     0xe90ff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe90ff8: cmp             SP, x16
    //     0xe90ffc: b.ls            #0xe91740
    // 0xe91000: LoadField: r6 = r2->field_b
    //     0xe91000: ldur            w6, [x2, #0xb]
    // 0xe91004: r7 = LoadInt32Instr(r6)
    //     0xe91004: sbfx            x7, x6, #1, #0x1f
    // 0xe91008: cmp             x5, x7
    // 0xe9100c: b.ne            #0xe91668
    // 0xe91010: cmp             x0, x7
    // 0xe91014: b.ge            #0xe91170
    // 0xe91018: LoadField: r6 = r2->field_f
    //     0xe91018: ldur            w6, [x2, #0xf]
    // 0xe9101c: DecompressPointer r6
    //     0xe9101c: add             x6, x6, HEAP, lsl #32
    // 0xe91020: ArrayLoad: r7 = r6[r0]  ; Unknown_4
    //     0xe91020: add             x16, x6, x0, lsl #2
    //     0xe91024: ldur            w7, [x16, #0xf]
    // 0xe91028: DecompressPointer r7
    //     0xe91028: add             x7, x7, HEAP, lsl #32
    // 0xe9102c: add             x6, x0, #1
    // 0xe91030: stur            x6, [fp, #-0x60]
    // 0xe91034: LoadField: d0 = r7->field_23
    //     0xe91034: ldur            d0, [x7, #0x23]
    // 0xe91038: stur            d0, [fp, #-0xa0]
    // 0xe9103c: r7 = inline_Allocate_Double()
    //     0xe9103c: ldp             x7, x0, [THR, #0x50]  ; THR::top
    //     0xe91040: add             x7, x7, #0x10
    //     0xe91044: cmp             x0, x7
    //     0xe91048: b.ls            #0xe91748
    //     0xe9104c: str             x7, [THR, #0x50]  ; THR::top
    //     0xe91050: sub             x7, x7, #0xf
    //     0xe91054: movz            x0, #0xe15c
    //     0xe91058: movk            x0, #0x3, lsl #16
    //     0xe9105c: stur            x0, [x7, #-1]
    // 0xe91060: StoreField: r7->field_7 = d0
    //     0xe91060: stur            d0, [x7, #7]
    // 0xe91064: stur            x7, [fp, #-0x48]
    // 0xe91068: r0 = 60
    //     0xe91068: movz            x0, #0x3c
    // 0xe9106c: branchIfSmi(r3, 0xe91078)
    //     0xe9106c: tbz             w3, #0, #0xe91078
    // 0xe91070: r0 = LoadClassIdInstr(r3)
    //     0xe91070: ldur            x0, [x3, #-1]
    //     0xe91074: ubfx            x0, x0, #0xc, #0x14
    // 0xe91078: stp             x7, x3, [SP]
    // 0xe9107c: r0 = GDT[cid_x0 + -0xfe3]()
    //     0xe9107c: sub             lr, x0, #0xfe3
    //     0xe91080: ldr             lr, [x21, lr, lsl #3]
    //     0xe91084: blr             lr
    // 0xe91088: tbnz            w0, #4, #0xe91098
    // 0xe9108c: ldur            x3, [fp, #-0x50]
    // 0xe91090: d0 = 0.000000
    //     0xe91090: eor             v0.16b, v0.16b, v0.16b
    // 0xe91094: b               #0xe91158
    // 0xe91098: ldur            x1, [fp, #-0x50]
    // 0xe9109c: r0 = 60
    //     0xe9109c: movz            x0, #0x3c
    // 0xe910a0: branchIfSmi(r1, 0xe910ac)
    //     0xe910a0: tbz             w1, #0, #0xe910ac
    // 0xe910a4: r0 = LoadClassIdInstr(r1)
    //     0xe910a4: ldur            x0, [x1, #-1]
    //     0xe910a8: ubfx            x0, x0, #0xc, #0x14
    // 0xe910ac: ldur            x16, [fp, #-0x48]
    // 0xe910b0: stp             x16, x1, [SP]
    // 0xe910b4: r0 = GDT[cid_x0 + -0xfd2]()
    //     0xe910b4: sub             lr, x0, #0xfd2
    //     0xe910b8: ldr             lr, [x21, lr, lsl #3]
    //     0xe910bc: blr             lr
    // 0xe910c0: tbnz            w0, #4, #0xe910d0
    // 0xe910c4: ldur            x3, [fp, #-0x48]
    // 0xe910c8: d0 = 0.000000
    //     0xe910c8: eor             v0.16b, v0.16b, v0.16b
    // 0xe910cc: b               #0xe91158
    // 0xe910d0: ldur            x1, [fp, #-0x50]
    // 0xe910d4: r0 = 60
    //     0xe910d4: movz            x0, #0x3c
    // 0xe910d8: branchIfSmi(r1, 0xe910e4)
    //     0xe910d8: tbz             w1, #0, #0xe910e4
    // 0xe910dc: r0 = LoadClassIdInstr(r1)
    //     0xe910dc: ldur            x0, [x1, #-1]
    //     0xe910e0: ubfx            x0, x0, #0xc, #0x14
    // 0xe910e4: cmp             x0, #0x3e
    // 0xe910e8: b.ne            #0xe9113c
    // 0xe910ec: d0 = 0.000000
    //     0xe910ec: eor             v0.16b, v0.16b, v0.16b
    // 0xe910f0: LoadField: d1 = r1->field_7
    //     0xe910f0: ldur            d1, [x1, #7]
    // 0xe910f4: fcmp            d1, d0
    // 0xe910f8: b.ne            #0xe91134
    // 0xe910fc: ldur            d2, [fp, #-0xa0]
    // 0xe91100: fadd            d3, d1, d2
    // 0xe91104: r1 = inline_Allocate_Double()
    //     0xe91104: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xe91108: add             x1, x1, #0x10
    //     0xe9110c: cmp             x0, x1
    //     0xe91110: b.ls            #0xe91774
    //     0xe91114: str             x1, [THR, #0x50]  ; THR::top
    //     0xe91118: sub             x1, x1, #0xf
    //     0xe9111c: movz            x0, #0xe15c
    //     0xe91120: movk            x0, #0x3, lsl #16
    //     0xe91124: stur            x0, [x1, #-1]
    // 0xe91128: StoreField: r1->field_7 = d3
    //     0xe91128: stur            d3, [x1, #7]
    // 0xe9112c: mov             x3, x1
    // 0xe91130: b               #0xe91158
    // 0xe91134: ldur            d2, [fp, #-0xa0]
    // 0xe91138: b               #0xe91144
    // 0xe9113c: ldur            d2, [fp, #-0xa0]
    // 0xe91140: d0 = 0.000000
    //     0xe91140: eor             v0.16b, v0.16b, v0.16b
    // 0xe91144: fcmp            d2, d2
    // 0xe91148: b.vc            #0xe91154
    // 0xe9114c: ldur            x3, [fp, #-0x48]
    // 0xe91150: b               #0xe91158
    // 0xe91154: mov             x3, x1
    // 0xe91158: ldur            x0, [fp, #-0x60]
    // 0xe9115c: ldur            x1, [fp, #-0x18]
    // 0xe91160: ldur            x4, [fp, #-0x20]
    // 0xe91164: ldur            x2, [fp, #-0x40]
    // 0xe91168: ldur            x5, [fp, #-0x70]
    // 0xe9116c: b               #0xe90ff0
    // 0xe91170: mov             x1, x3
    // 0xe91174: r0 = LoadInt32Instr(r6)
    //     0xe91174: sbfx            x0, x6, #1, #0x1f
    // 0xe91178: mov             x2, x1
    // 0xe9117c: b               #0xe911b4
    // 0xe91180: r0 = inline_Allocate_Double()
    //     0xe91180: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe91184: add             x0, x0, #0x10
    //     0xe91188: cmp             x1, x0
    //     0xe9118c: b.ls            #0xe91788
    //     0xe91190: str             x0, [THR, #0x50]  ; THR::top
    //     0xe91194: sub             x0, x0, #0xf
    //     0xe91198: movz            x1, #0xe15c
    //     0xe9119c: movk            x1, #0x3, lsl #16
    //     0xe911a0: stur            x1, [x0, #-1]
    // 0xe911a4: StoreField: r0->field_7 = d0
    //     0xe911a4: stur            d0, [x0, #7]
    // 0xe911a8: r1 = LoadInt32Instr(r3)
    //     0xe911a8: sbfx            x1, x3, #1, #0x1f
    // 0xe911ac: mov             x2, x0
    // 0xe911b0: mov             x0, x1
    // 0xe911b4: stur            x2, [fp, #-0x48]
    // 0xe911b8: stur            x0, [fp, #-0x70]
    // 0xe911bc: LoadField: d1 = r2->field_7
    //     0xe911bc: ldur            d1, [x2, #7]
    // 0xe911c0: stur            d1, [fp, #-0xa0]
    // 0xe911c4: r1 = 0
    //     0xe911c4: movz            x1, #0
    // 0xe911c8: ldur            x3, [fp, #-0x40]
    // 0xe911cc: CheckStackOverflow
    //     0xe911cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe911d0: cmp             SP, x16
    //     0xe911d4: b.ls            #0xe917a0
    // 0xe911d8: LoadField: r4 = r3->field_b
    //     0xe911d8: ldur            w4, [x3, #0xb]
    // 0xe911dc: r5 = LoadInt32Instr(r4)
    //     0xe911dc: sbfx            x5, x4, #1, #0x1f
    // 0xe911e0: cmp             x0, x5
    // 0xe911e4: b.ne            #0xe91688
    // 0xe911e8: cmp             x1, x5
    // 0xe911ec: b.ge            #0xe9122c
    // 0xe911f0: LoadField: r4 = r3->field_f
    //     0xe911f0: ldur            w4, [x3, #0xf]
    // 0xe911f4: DecompressPointer r4
    //     0xe911f4: add             x4, x4, HEAP, lsl #32
    // 0xe911f8: ArrayLoad: r5 = r4[r1]  ; Unknown_4
    //     0xe911f8: add             x16, x4, x1, lsl #2
    //     0xe911fc: ldur            w5, [x16, #0xf]
    // 0xe91200: DecompressPointer r5
    //     0xe91200: add             x5, x5, HEAP, lsl #32
    // 0xe91204: add             x4, x1, #1
    // 0xe91208: mov             x1, x5
    // 0xe9120c: mov             v0.16b, v1.16b
    // 0xe91210: stur            x4, [fp, #-0x60]
    // 0xe91214: r0 = realign()
    //     0xe91214: bl              #0xe91968  ; [package:pdf/src/widgets/text.dart] _Line::realign
    // 0xe91218: ldur            x1, [fp, #-0x60]
    // 0xe9121c: ldur            x2, [fp, #-0x48]
    // 0xe91220: ldur            d1, [fp, #-0xa0]
    // 0xe91224: ldur            x0, [fp, #-0x70]
    // 0xe91228: b               #0xe911c8
    // 0xe9122c: ldur            x1, [fp, #-0x48]
    // 0xe91230: b               #0xe91260
    // 0xe91234: r0 = inline_Allocate_Double()
    //     0xe91234: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe91238: add             x0, x0, #0x10
    //     0xe9123c: cmp             x1, x0
    //     0xe91240: b.ls            #0xe917a8
    //     0xe91244: str             x0, [THR, #0x50]  ; THR::top
    //     0xe91248: sub             x0, x0, #0xf
    //     0xe9124c: movz            x1, #0xe15c
    //     0xe91250: movk            x1, #0x3, lsl #16
    //     0xe91254: stur            x1, [x0, #-1]
    // 0xe91258: StoreField: r0->field_7 = d0
    //     0xe91258: stur            d0, [x0, #7]
    // 0xe9125c: mov             x1, x0
    // 0xe91260: ldur            x2, [fp, #-8]
    // 0xe91264: ldur            x0, [fp, #-0x20]
    // 0xe91268: ldur            x5, [fp, #-0x10]
    // 0xe9126c: ldur            x4, [fp, #-0x30]
    // 0xe91270: ldur            x3, [fp, #-0x38]
    // 0xe91274: str             x1, [SP]
    // 0xe91278: ldur            x1, [fp, #-0x18]
    // 0xe9127c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe9127c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe91280: r0 = constrainWidth()
    //     0xe91280: bl              #0xe8e794  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainWidth
    // 0xe91284: ldur            x0, [fp, #-0x20]
    // 0xe91288: stur            d0, [fp, #-0xa0]
    // 0xe9128c: LoadField: r1 = r0->field_2b
    //     0xe9128c: ldur            w1, [x0, #0x2b]
    // 0xe91290: DecompressPointer r1
    //     0xe91290: add             x1, x1, HEAP, lsl #32
    // 0xe91294: str             x1, [SP]
    // 0xe91298: ldur            x1, [fp, #-0x18]
    // 0xe9129c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe9129c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe912a0: r0 = constrainHeight()
    //     0xe912a0: bl              #0xe8e66c  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::constrainHeight
    // 0xe912a4: stur            d0, [fp, #-0xb0]
    // 0xe912a8: r0 = PdfRect()
    //     0xe912a8: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe912ac: StoreField: r0->field_7 = rZR
    //     0xe912ac: stur            xzr, [x0, #7]
    // 0xe912b0: StoreField: r0->field_f = rZR
    //     0xe912b0: stur            xzr, [x0, #0xf]
    // 0xe912b4: ldur            d0, [fp, #-0xa0]
    // 0xe912b8: ArrayStore: r0[0] = d0  ; List_8
    //     0xe912b8: stur            d0, [x0, #0x17]
    // 0xe912bc: ldur            d0, [fp, #-0xb0]
    // 0xe912c0: StoreField: r0->field_1f = d0
    //     0xe912c0: stur            d0, [x0, #0x1f]
    // 0xe912c4: ldur            x1, [fp, #-8]
    // 0xe912c8: StoreField: r1->field_7 = r0
    //     0xe912c8: stur            w0, [x1, #7]
    //     0xe912cc: ldurb           w16, [x1, #-1]
    //     0xe912d0: ldurb           w17, [x0, #-1]
    //     0xe912d4: and             x16, x17, x16, lsr #2
    //     0xe912d8: tst             x16, HEAP, lsr #32
    //     0xe912dc: b.eq            #0xe912e4
    //     0xe912e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe912e4: ldur            x0, [fp, #-0x20]
    // 0xe912e8: LoadField: r2 = r0->field_2b
    //     0xe912e8: ldur            w2, [x0, #0x2b]
    // 0xe912ec: DecompressPointer r2
    //     0xe912ec: add             x2, x2, HEAP, lsl #32
    // 0xe912f0: ldur            x0, [fp, #-0x38]
    // 0xe912f4: LoadField: d0 = r0->field_7
    //     0xe912f4: ldur            d0, [x0, #7]
    // 0xe912f8: LoadField: d1 = r2->field_7
    //     0xe912f8: ldur            d1, [x2, #7]
    // 0xe912fc: fsub            d2, d1, d0
    // 0xe91300: StoreField: r0->field_f = d2
    //     0xe91300: stur            d2, [x0, #0xf]
    // 0xe91304: ldur            x2, [fp, #-0x10]
    // 0xe91308: LoadField: r3 = r2->field_b
    //     0xe91308: ldur            w3, [x2, #0xb]
    // 0xe9130c: r2 = LoadInt32Instr(r3)
    //     0xe9130c: sbfx            x2, x3, #1, #0x1f
    // 0xe91310: stur            x2, [fp, #-0x60]
    // 0xe91314: StoreField: r0->field_1f = r2
    //     0xe91314: stur            x2, [x0, #0x1f]
    // 0xe91318: ldur            x3, [fp, #-0x30]
    // 0xe9131c: r16 = Instance_TextOverflow
    //     0xe9131c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dd60] Obj!TextOverflow@e2e581
    //     0xe91320: ldr             x16, [x16, #0xd60]
    // 0xe91324: cmp             w3, w16
    // 0xe91328: b.eq            #0xe91354
    // 0xe9132c: r16 = Instance_TextOverflow
    //     0xe9132c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e1f8] Obj!TextOverflow@e2e561
    //     0xe91330: ldr             x16, [x16, #0x1f8]
    // 0xe91334: cmp             w3, w16
    // 0xe91338: b.eq            #0xe91344
    // 0xe9133c: r0 = true
    //     0xe9133c: add             x0, NULL, #0x20  ; true
    // 0xe91340: StoreField: r1->field_3f = r0
    //     0xe91340: stur            w0, [x1, #0x3f]
    // 0xe91344: r0 = Null
    //     0xe91344: mov             x0, NULL
    // 0xe91348: LeaveFrame
    //     0xe91348: mov             SP, fp
    //     0xe9134c: ldp             fp, lr, [SP], #0x10
    // 0xe91350: ret
    //     0xe91350: ret             
    // 0xe91354: ldur            d0, [fp, #-0xa8]
    // 0xe91358: d2 = 0.000100
    //     0xe91358: ldr             d2, [PP, #0x5b50]  ; [pp+0x5b50] IMM: double(1e-04) from 0x3f1a36e2eb1c432d
    // 0xe9135c: fadd            d3, d0, d2
    // 0xe91360: fcmp            d1, d3
    // 0xe91364: b.le            #0xe913b4
    // 0xe91368: ldur            x1, [fp, #-0x40]
    // 0xe9136c: r0 = last()
    //     0xe9136c: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xe91370: LoadField: r1 = r0->field_13
    //     0xe91370: ldur            x1, [x0, #0x13]
    // 0xe91374: ldur            x0, [fp, #-0x60]
    // 0xe91378: sub             x2, x0, x1
    // 0xe9137c: ldur            x0, [fp, #-0x38]
    // 0xe91380: StoreField: r0->field_1f = r2
    //     0xe91380: stur            x2, [x0, #0x1f]
    // 0xe91384: LoadField: d0 = r0->field_f
    //     0xe91384: ldur            d0, [x0, #0xf]
    // 0xe91388: ldur            x1, [fp, #-0x40]
    // 0xe9138c: stur            d0, [fp, #-0xa0]
    // 0xe91390: r0 = last()
    //     0xe91390: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xe91394: mov             x1, x0
    // 0xe91398: r0 = height()
    //     0xe91398: bl              #0xe917e0  ; [package:pdf/src/widgets/text.dart] _Line::height
    // 0xe9139c: mov             v1.16b, v0.16b
    // 0xe913a0: ldur            d0, [fp, #-0xa0]
    // 0xe913a4: fsub            d2, d0, d1
    // 0xe913a8: ldur            x3, [fp, #-0x38]
    // 0xe913ac: StoreField: r3->field_f = d2
    //     0xe913ac: stur            d2, [x3, #0xf]
    // 0xe913b0: b               #0xe913b8
    // 0xe913b4: mov             x3, x0
    // 0xe913b8: ldur            x4, [fp, #-0x28]
    // 0xe913bc: LoadField: r5 = r4->field_7
    //     0xe913bc: ldur            w5, [x4, #7]
    // 0xe913c0: DecompressPointer r5
    //     0xe913c0: add             x5, x5, HEAP, lsl #32
    // 0xe913c4: stur            x5, [fp, #-0x18]
    // 0xe913c8: r6 = 0
    //     0xe913c8: movz            x6, #0
    // 0xe913cc: stur            x6, [fp, #-0x90]
    // 0xe913d0: CheckStackOverflow
    //     0xe913d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe913d4: cmp             SP, x16
    //     0xe913d8: b.ls            #0xe917b8
    // 0xe913dc: LoadField: r0 = r4->field_b
    //     0xe913dc: ldur            w0, [x4, #0xb]
    // 0xe913e0: r7 = LoadInt32Instr(r0)
    //     0xe913e0: sbfx            x7, x0, #1, #0x1f
    // 0xe913e4: stur            x7, [fp, #-0x88]
    // 0xe913e8: cmp             x6, x7
    // 0xe913ec: b.ge            #0xe91658
    // 0xe913f0: LoadField: r8 = r4->field_f
    //     0xe913f0: ldur            w8, [x4, #0xf]
    // 0xe913f4: DecompressPointer r8
    //     0xe913f4: add             x8, x8, HEAP, lsl #32
    // 0xe913f8: stur            x8, [fp, #-0x10]
    // 0xe913fc: ArrayLoad: r0 = r8[r6]  ; Unknown_4
    //     0xe913fc: add             x16, x8, x6, lsl #2
    //     0xe91400: ldur            w0, [x16, #0xf]
    // 0xe91404: DecompressPointer r0
    //     0xe91404: add             x0, x0, HEAP, lsl #32
    // 0xe91408: LoadField: r1 = r0->field_f
    //     0xe91408: ldur            x1, [x0, #0xf]
    // 0xe9140c: LoadField: r2 = r3->field_1f
    //     0xe9140c: ldur            x2, [x3, #0x1f]
    // 0xe91410: cmp             x1, x2
    // 0xe91414: b.ge            #0xe91428
    // 0xe91418: ArrayLoad: r1 = r0[0]  ; List_8
    //     0xe91418: ldur            x1, [x0, #0x17]
    // 0xe9141c: ArrayLoad: r0 = r3[0]  ; List_8
    //     0xe9141c: ldur            x0, [x3, #0x17]
    // 0xe91420: cmp             x1, x0
    // 0xe91424: b.ge            #0xe91640
    // 0xe91428: sub             x9, x7, #1
    // 0xe9142c: stur            x9, [fp, #-0x80]
    // 0xe91430: cmp             x6, x9
    // 0xe91434: b.ge            #0xe91620
    // 0xe91438: add             x10, x6, #1
    // 0xe9143c: stur            x10, [fp, #-0x78]
    // 0xe91440: sub             x0, x9, x6
    // 0xe91444: cmp             x10, x6
    // 0xe91448: b.ge            #0xe91544
    // 0xe9144c: add             x1, x10, x0
    // 0xe91450: sub             x2, x1, #1
    // 0xe91454: add             x1, x6, x0
    // 0xe91458: sub             x0, x1, #1
    // 0xe9145c: mov             x12, x2
    // 0xe91460: mov             x11, x0
    // 0xe91464: stur            x12, [fp, #-0x60]
    // 0xe91468: stur            x11, [fp, #-0x70]
    // 0xe9146c: CheckStackOverflow
    //     0xe9146c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe91470: cmp             SP, x16
    //     0xe91474: b.ls            #0xe917c0
    // 0xe91478: cmp             x12, x10
    // 0xe9147c: b.lt            #0xe91620
    // 0xe91480: mov             x0, x7
    // 0xe91484: mov             x1, x12
    // 0xe91488: cmp             x1, x0
    // 0xe9148c: b.hs            #0xe917c8
    // 0xe91490: ArrayLoad: r13 = r8[r12]  ; Unknown_4
    //     0xe91490: add             x16, x8, x12, lsl #2
    //     0xe91494: ldur            w13, [x16, #0xf]
    // 0xe91498: DecompressPointer r13
    //     0xe91498: add             x13, x13, HEAP, lsl #32
    // 0xe9149c: mov             x0, x13
    // 0xe914a0: mov             x2, x5
    // 0xe914a4: stur            x13, [fp, #-8]
    // 0xe914a8: r1 = Null
    //     0xe914a8: mov             x1, NULL
    // 0xe914ac: cmp             w2, NULL
    // 0xe914b0: b.eq            #0xe914d0
    // 0xe914b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe914b4: ldur            w4, [x2, #0x17]
    // 0xe914b8: DecompressPointer r4
    //     0xe914b8: add             x4, x4, HEAP, lsl #32
    // 0xe914bc: r8 = X0
    //     0xe914bc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe914c0: LoadField: r9 = r4->field_7
    //     0xe914c0: ldur            x9, [x4, #7]
    // 0xe914c4: r3 = Null
    //     0xe914c4: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e0e0] Null
    //     0xe914c8: ldr             x3, [x3, #0xe0]
    // 0xe914cc: blr             x9
    // 0xe914d0: ldur            x0, [fp, #-0x88]
    // 0xe914d4: ldur            x1, [fp, #-0x70]
    // 0xe914d8: cmp             x1, x0
    // 0xe914dc: b.hs            #0xe917cc
    // 0xe914e0: ldur            x1, [fp, #-0x10]
    // 0xe914e4: ldur            x0, [fp, #-8]
    // 0xe914e8: ldur            x2, [fp, #-0x70]
    // 0xe914ec: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe914ec: add             x25, x1, x2, lsl #2
    //     0xe914f0: add             x25, x25, #0xf
    //     0xe914f4: str             w0, [x25]
    //     0xe914f8: tbz             w0, #0, #0xe91514
    //     0xe914fc: ldurb           w16, [x1, #-1]
    //     0xe91500: ldurb           w17, [x0, #-1]
    //     0xe91504: and             x16, x17, x16, lsr #2
    //     0xe91508: tst             x16, HEAP, lsr #32
    //     0xe9150c: b.eq            #0xe91514
    //     0xe91510: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe91514: ldur            x0, [fp, #-0x60]
    // 0xe91518: sub             x12, x0, #1
    // 0xe9151c: sub             x11, x2, #1
    // 0xe91520: ldur            x4, [fp, #-0x28]
    // 0xe91524: ldur            x3, [fp, #-0x38]
    // 0xe91528: ldur            x6, [fp, #-0x90]
    // 0xe9152c: ldur            x8, [fp, #-0x10]
    // 0xe91530: ldur            x9, [fp, #-0x80]
    // 0xe91534: ldur            x10, [fp, #-0x78]
    // 0xe91538: ldur            x5, [fp, #-0x18]
    // 0xe9153c: ldur            x7, [fp, #-0x88]
    // 0xe91540: b               #0xe91464
    // 0xe91544: mov             x1, x10
    // 0xe91548: add             x3, x1, x0
    // 0xe9154c: stur            x3, [fp, #-0x98]
    // 0xe91550: mov             x6, x1
    // 0xe91554: ldur            x5, [fp, #-0x90]
    // 0xe91558: ldur            x4, [fp, #-0x10]
    // 0xe9155c: stur            x6, [fp, #-0x60]
    // 0xe91560: stur            x5, [fp, #-0x70]
    // 0xe91564: CheckStackOverflow
    //     0xe91564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe91568: cmp             SP, x16
    //     0xe9156c: b.ls            #0xe917d0
    // 0xe91570: cmp             x6, x3
    // 0xe91574: b.ge            #0xe91620
    // 0xe91578: ldur            x0, [fp, #-0x88]
    // 0xe9157c: mov             x1, x6
    // 0xe91580: cmp             x1, x0
    // 0xe91584: b.hs            #0xe917d8
    // 0xe91588: ArrayLoad: r7 = r4[r6]  ; Unknown_4
    //     0xe91588: add             x16, x4, x6, lsl #2
    //     0xe9158c: ldur            w7, [x16, #0xf]
    // 0xe91590: DecompressPointer r7
    //     0xe91590: add             x7, x7, HEAP, lsl #32
    // 0xe91594: mov             x0, x7
    // 0xe91598: ldur            x2, [fp, #-0x18]
    // 0xe9159c: stur            x7, [fp, #-8]
    // 0xe915a0: r1 = Null
    //     0xe915a0: mov             x1, NULL
    // 0xe915a4: cmp             w2, NULL
    // 0xe915a8: b.eq            #0xe915c8
    // 0xe915ac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe915ac: ldur            w4, [x2, #0x17]
    // 0xe915b0: DecompressPointer r4
    //     0xe915b0: add             x4, x4, HEAP, lsl #32
    // 0xe915b4: r8 = X0
    //     0xe915b4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe915b8: LoadField: r9 = r4->field_7
    //     0xe915b8: ldur            x9, [x4, #7]
    // 0xe915bc: r3 = Null
    //     0xe915bc: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e0f0] Null
    //     0xe915c0: ldr             x3, [x3, #0xf0]
    // 0xe915c4: blr             x9
    // 0xe915c8: ldur            x0, [fp, #-0x88]
    // 0xe915cc: ldur            x1, [fp, #-0x70]
    // 0xe915d0: cmp             x1, x0
    // 0xe915d4: b.hs            #0xe917dc
    // 0xe915d8: ldur            x1, [fp, #-0x10]
    // 0xe915dc: ldur            x0, [fp, #-8]
    // 0xe915e0: ldur            x2, [fp, #-0x70]
    // 0xe915e4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe915e4: add             x25, x1, x2, lsl #2
    //     0xe915e8: add             x25, x25, #0xf
    //     0xe915ec: str             w0, [x25]
    //     0xe915f0: tbz             w0, #0, #0xe9160c
    //     0xe915f4: ldurb           w16, [x1, #-1]
    //     0xe915f8: ldurb           w17, [x0, #-1]
    //     0xe915fc: and             x16, x17, x16, lsr #2
    //     0xe91600: tst             x16, HEAP, lsr #32
    //     0xe91604: b.eq            #0xe9160c
    //     0xe91608: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe9160c: ldur            x0, [fp, #-0x60]
    // 0xe91610: add             x6, x0, #1
    // 0xe91614: add             x5, x2, #1
    // 0xe91618: ldur            x3, [fp, #-0x98]
    // 0xe9161c: b               #0xe91558
    // 0xe91620: ldur            x0, [fp, #-0x90]
    // 0xe91624: ldur            x1, [fp, #-0x28]
    // 0xe91628: ldur            x2, [fp, #-0x80]
    // 0xe9162c: r0 = length=()
    //     0xe9162c: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xe91630: ldur            x0, [fp, #-0x90]
    // 0xe91634: sub             x1, x0, #1
    // 0xe91638: mov             x0, x1
    // 0xe9163c: b               #0xe91644
    // 0xe91640: mov             x0, x6
    // 0xe91644: add             x6, x0, #1
    // 0xe91648: ldur            x4, [fp, #-0x28]
    // 0xe9164c: ldur            x3, [fp, #-0x38]
    // 0xe91650: ldur            x5, [fp, #-0x18]
    // 0xe91654: b               #0xe913cc
    // 0xe91658: r0 = Null
    //     0xe91658: mov             x0, NULL
    // 0xe9165c: LeaveFrame
    //     0xe9165c: mov             SP, fp
    //     0xe91660: ldp             fp, lr, [SP], #0x10
    // 0xe91664: ret
    //     0xe91664: ret             
    // 0xe91668: mov             x0, x2
    // 0xe9166c: r0 = ConcurrentModificationError()
    //     0xe9166c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe91670: mov             x1, x0
    // 0xe91674: ldur            x0, [fp, #-0x40]
    // 0xe91678: StoreField: r1->field_b = r0
    //     0xe91678: stur            w0, [x1, #0xb]
    // 0xe9167c: mov             x0, x1
    // 0xe91680: r0 = Throw()
    //     0xe91680: bl              #0xec04b8  ; ThrowStub
    // 0xe91684: brk             #0
    // 0xe91688: mov             x0, x3
    // 0xe9168c: r0 = ConcurrentModificationError()
    //     0xe9168c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe91690: mov             x1, x0
    // 0xe91694: ldur            x0, [fp, #-0x40]
    // 0xe91698: StoreField: r1->field_b = r0
    //     0xe91698: stur            w0, [x1, #0xb]
    // 0xe9169c: mov             x0, x1
    // 0xe916a0: r0 = Throw()
    //     0xe916a0: bl              #0xec04b8  ; ThrowStub
    // 0xe916a4: brk             #0
    // 0xe916a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe916a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe916ac: b               #0xe90aec
    // 0xe916b0: stp             q0, q1, [SP, #-0x20]!
    // 0xe916b4: stp             x2, x3, [SP, #-0x10]!
    // 0xe916b8: r0 = AllocateDouble()
    //     0xe916b8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe916bc: ldp             x2, x3, [SP], #0x10
    // 0xe916c0: ldp             q0, q1, [SP], #0x20
    // 0xe916c4: b               #0xe90c38
    // 0xe916c8: SaveReg d0
    //     0xe916c8: str             q0, [SP, #-0x10]!
    // 0xe916cc: stp             x3, x4, [SP, #-0x10]!
    // 0xe916d0: SaveReg r1
    //     0xe916d0: str             x1, [SP, #-8]!
    // 0xe916d4: r0 = AllocateDouble()
    //     0xe916d4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe916d8: RestoreReg r1
    //     0xe916d8: ldr             x1, [SP], #8
    // 0xe916dc: ldp             x3, x4, [SP], #0x10
    // 0xe916e0: RestoreReg d0
    //     0xe916e0: ldr             q0, [SP], #0x10
    // 0xe916e4: b               #0xe90cb0
    // 0xe916e8: stp             q0, q1, [SP, #-0x20]!
    // 0xe916ec: stp             x4, x5, [SP, #-0x10]!
    // 0xe916f0: stp             x1, x3, [SP, #-0x10]!
    // 0xe916f4: r0 = AllocateDouble()
    //     0xe916f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe916f8: ldp             x1, x3, [SP], #0x10
    // 0xe916fc: ldp             x4, x5, [SP], #0x10
    // 0xe91700: ldp             q0, q1, [SP], #0x20
    // 0xe91704: b               #0xe90d08
    // 0xe91708: SaveReg d1
    //     0xe91708: str             q1, [SP, #-0x10]!
    // 0xe9170c: stp             x2, x4, [SP, #-0x10]!
    // 0xe91710: r0 = AllocateDouble()
    //     0xe91710: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe91714: ldp             x2, x4, [SP], #0x10
    // 0xe91718: RestoreReg d1
    //     0xe91718: ldr             q1, [SP], #0x10
    // 0xe9171c: b               #0xe90f60
    // 0xe91720: SaveReg d0
    //     0xe91720: str             q0, [SP, #-0x10]!
    // 0xe91724: stp             x4, x5, [SP, #-0x10]!
    // 0xe91728: stp             x1, x2, [SP, #-0x10]!
    // 0xe9172c: r0 = AllocateDouble()
    //     0xe9172c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe91730: ldp             x1, x2, [SP], #0x10
    // 0xe91734: ldp             x4, x5, [SP], #0x10
    // 0xe91738: RestoreReg d0
    //     0xe91738: ldr             q0, [SP], #0x10
    // 0xe9173c: b               #0xe90fe4
    // 0xe91740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe91740: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe91744: b               #0xe91000
    // 0xe91748: SaveReg d0
    //     0xe91748: str             q0, [SP, #-0x10]!
    // 0xe9174c: stp             x5, x6, [SP, #-0x10]!
    // 0xe91750: stp             x3, x4, [SP, #-0x10]!
    // 0xe91754: stp             x1, x2, [SP, #-0x10]!
    // 0xe91758: r0 = AllocateDouble()
    //     0xe91758: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe9175c: mov             x7, x0
    // 0xe91760: ldp             x1, x2, [SP], #0x10
    // 0xe91764: ldp             x3, x4, [SP], #0x10
    // 0xe91768: ldp             x5, x6, [SP], #0x10
    // 0xe9176c: RestoreReg d0
    //     0xe9176c: ldr             q0, [SP], #0x10
    // 0xe91770: b               #0xe91060
    // 0xe91774: stp             q0, q3, [SP, #-0x20]!
    // 0xe91778: r0 = AllocateDouble()
    //     0xe91778: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe9177c: mov             x1, x0
    // 0xe91780: ldp             q0, q3, [SP], #0x20
    // 0xe91784: b               #0xe91128
    // 0xe91788: SaveReg d0
    //     0xe91788: str             q0, [SP, #-0x10]!
    // 0xe9178c: SaveReg r3
    //     0xe9178c: str             x3, [SP, #-8]!
    // 0xe91790: r0 = AllocateDouble()
    //     0xe91790: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe91794: RestoreReg r3
    //     0xe91794: ldr             x3, [SP], #8
    // 0xe91798: RestoreReg d0
    //     0xe91798: ldr             q0, [SP], #0x10
    // 0xe9179c: b               #0xe911a4
    // 0xe917a0: r0 = StackOverflowSharedWithFPURegs()
    //     0xe917a0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe917a4: b               #0xe911d8
    // 0xe917a8: SaveReg d0
    //     0xe917a8: str             q0, [SP, #-0x10]!
    // 0xe917ac: r0 = AllocateDouble()
    //     0xe917ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe917b0: RestoreReg d0
    //     0xe917b0: ldr             q0, [SP], #0x10
    // 0xe917b4: b               #0xe91258
    // 0xe917b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe917b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe917bc: b               #0xe913dc
    // 0xe917c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe917c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe917c4: b               #0xe91478
    // 0xe917c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe917c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe917cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe917cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe917d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe917d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe917d4: b               #0xe91570
    // 0xe917d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe917d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe917dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe917dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _preProcessSpans(/* No info */) {
    // ** addr: 0xe91df4, size: 0xd8
    // 0xe91df4: EnterFrame
    //     0xe91df4: stp             fp, lr, [SP, #-0x10]!
    //     0xe91df8: mov             fp, SP
    // 0xe91dfc: AllocStack(0x28)
    //     0xe91dfc: sub             SP, SP, #0x28
    // 0xe91e00: SetupParameters(RichText this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xe91e00: mov             x0, x1
    //     0xe91e04: stur            x1, [fp, #-8]
    //     0xe91e08: mov             x1, x2
    //     0xe91e0c: stur            x2, [fp, #-0x10]
    // 0xe91e10: CheckStackOverflow
    //     0xe91e10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe91e14: cmp             SP, x16
    //     0xe91e18: b.ls            #0xe91ec4
    // 0xe91e1c: r1 = 3
    //     0xe91e1c: movz            x1, #0x3
    // 0xe91e20: r0 = AllocateContext()
    //     0xe91e20: bl              #0xec126c  ; AllocateContextStub
    // 0xe91e24: mov             x2, x0
    // 0xe91e28: ldur            x0, [fp, #-8]
    // 0xe91e2c: stur            x2, [fp, #-0x18]
    // 0xe91e30: StoreField: r2->field_f = r0
    //     0xe91e30: stur            w0, [x2, #0xf]
    // 0xe91e34: ldur            x1, [fp, #-0x10]
    // 0xe91e38: StoreField: r2->field_13 = r1
    //     0xe91e38: stur            w1, [x2, #0x13]
    // 0xe91e3c: r0 = of()
    //     0xe91e3c: bl              #0xb125d8  ; [package:pdf/src/widgets/theme.dart] Theme::of
    // 0xe91e40: LoadField: r3 = r0->field_7
    //     0xe91e40: ldur            w3, [x0, #7]
    // 0xe91e44: DecompressPointer r3
    //     0xe91e44: add             x3, x3, HEAP, lsl #32
    // 0xe91e48: stur            x3, [fp, #-0x10]
    // 0xe91e4c: r1 = <InlineSpan>
    //     0xe91e4c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e1e8] TypeArguments: <InlineSpan>
    //     0xe91e50: ldr             x1, [x1, #0x1e8]
    // 0xe91e54: r2 = 0
    //     0xe91e54: movz            x2, #0
    // 0xe91e58: r0 = _GrowableList()
    //     0xe91e58: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe91e5c: mov             x3, x0
    // 0xe91e60: ldur            x2, [fp, #-0x18]
    // 0xe91e64: stur            x3, [fp, #-0x28]
    // 0xe91e68: ArrayStore: r2[0] = r0  ; List_4
    //     0xe91e68: stur            w0, [x2, #0x17]
    //     0xe91e6c: ldurb           w16, [x2, #-1]
    //     0xe91e70: ldurb           w17, [x0, #-1]
    //     0xe91e74: and             x16, x17, x16, lsr #2
    //     0xe91e78: tst             x16, HEAP, lsr #32
    //     0xe91e7c: b.eq            #0xe91e84
    //     0xe91e80: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe91e84: ldur            x0, [fp, #-8]
    // 0xe91e88: LoadField: r4 = r0->field_b
    //     0xe91e88: ldur            w4, [x0, #0xb]
    // 0xe91e8c: DecompressPointer r4
    //     0xe91e8c: add             x4, x4, HEAP, lsl #32
    // 0xe91e90: stur            x4, [fp, #-0x20]
    // 0xe91e94: r1 = Function '<anonymous closure>':.
    //     0xe91e94: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e508] AnonymousClosure: (0xe91ecc), in [package:pdf/src/widgets/text.dart] RichText::_preProcessSpans (0xe91df4)
    //     0xe91e98: ldr             x1, [x1, #0x508]
    // 0xe91e9c: r0 = AllocateClosure()
    //     0xe91e9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe91ea0: ldur            x1, [fp, #-0x20]
    // 0xe91ea4: mov             x2, x0
    // 0xe91ea8: ldur            x3, [fp, #-0x10]
    // 0xe91eac: r5 = Null
    //     0xe91eac: mov             x5, NULL
    // 0xe91eb0: r0 = visitChildren()
    //     0xe91eb0: bl              #0xeb0d68  ; [package:pdf/src/widgets/text.dart] TextSpan::visitChildren
    // 0xe91eb4: ldur            x0, [fp, #-0x28]
    // 0xe91eb8: LeaveFrame
    //     0xe91eb8: mov             SP, fp
    //     0xe91ebc: ldp             fp, lr, [SP], #0x10
    // 0xe91ec0: ret
    //     0xe91ec0: ret             
    // 0xe91ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe91ec4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe91ec8: b               #0xe91e1c
  }
  [closure] bool <anonymous closure>(dynamic, InlineSpan, TextStyle?, AnnotationBuilder?) {
    // ** addr: 0xe91ecc, size: 0xc0c
    // 0xe91ecc: EnterFrame
    //     0xe91ecc: stp             fp, lr, [SP, #-0x10]!
    //     0xe91ed0: mov             fp, SP
    // 0xe91ed4: AllocStack(0xb8)
    //     0xe91ed4: sub             SP, SP, #0xb8
    // 0xe91ed8: SetupParameters()
    //     0xe91ed8: ldr             x0, [fp, #0x28]
    //     0xe91edc: ldur            w2, [x0, #0x17]
    //     0xe91ee0: add             x2, x2, HEAP, lsl #32
    //     0xe91ee4: stur            x2, [fp, #-0x30]
    // 0xe91ee8: CheckStackOverflow
    //     0xe91ee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe91eec: cmp             SP, x16
    //     0xe91ef0: b.ls            #0xe92a8c
    // 0xe91ef4: ldr             x0, [fp, #0x20]
    // 0xe91ef8: r1 = LoadClassIdInstr(r0)
    //     0xe91ef8: ldur            x1, [x0, #-1]
    //     0xe91efc: ubfx            x1, x1, #0xc, #0x14
    // 0xe91f00: cmp             x1, #0x301
    // 0xe91f04: b.eq            #0xe920a8
    // 0xe91f08: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe91f08: ldur            w3, [x2, #0x17]
    // 0xe91f0c: DecompressPointer r3
    //     0xe91f0c: add             x3, x3, HEAP, lsl #32
    // 0xe91f10: stur            x3, [fp, #-0x20]
    // 0xe91f14: cmp             x1, #0x301
    // 0xe91f18: b.ne            #0xe91f8c
    // 0xe91f1c: ldr             x4, [fp, #0x18]
    // 0xe91f20: cmp             w4, NULL
    // 0xe91f24: b.ne            #0xe91f34
    // 0xe91f28: LoadField: r1 = r0->field_7
    //     0xe91f28: ldur            w1, [x0, #7]
    // 0xe91f2c: DecompressPointer r1
    //     0xe91f2c: add             x1, x1, HEAP, lsl #32
    // 0xe91f30: b               #0xe91f38
    // 0xe91f34: mov             x1, x4
    // 0xe91f38: stur            x1, [fp, #-0x18]
    // 0xe91f3c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xe91f3c: ldur            w2, [x0, #0x17]
    // 0xe91f40: DecompressPointer r2
    //     0xe91f40: add             x2, x2, HEAP, lsl #32
    // 0xe91f44: stur            x2, [fp, #-0x10]
    // 0xe91f48: LoadField: d0 = r0->field_b
    //     0xe91f48: ldur            d0, [x0, #0xb]
    // 0xe91f4c: stur            d0, [fp, #-0x88]
    // 0xe91f50: LoadField: r4 = r0->field_1b
    //     0xe91f50: ldur            w4, [x0, #0x1b]
    // 0xe91f54: DecompressPointer r4
    //     0xe91f54: add             x4, x4, HEAP, lsl #32
    // 0xe91f58: stur            x4, [fp, #-8]
    // 0xe91f5c: r0 = TextSpan()
    //     0xe91f5c: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xe91f60: mov             x1, x0
    // 0xe91f64: ldur            x0, [fp, #-0x10]
    // 0xe91f68: ArrayStore: r1[0] = r0  ; List_4
    //     0xe91f68: stur            w0, [x1, #0x17]
    // 0xe91f6c: ldur            x0, [fp, #-8]
    // 0xe91f70: StoreField: r1->field_1b = r0
    //     0xe91f70: stur            w0, [x1, #0x1b]
    // 0xe91f74: ldur            x0, [fp, #-0x18]
    // 0xe91f78: StoreField: r1->field_7 = r0
    //     0xe91f78: stur            w0, [x1, #7]
    // 0xe91f7c: ldur            d0, [fp, #-0x88]
    // 0xe91f80: StoreField: r1->field_b = d0
    //     0xe91f80: stur            d0, [x1, #0xb]
    // 0xe91f84: mov             x4, x1
    // 0xe91f88: b               #0xe91fe4
    // 0xe91f8c: ldr             x4, [fp, #0x18]
    // 0xe91f90: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe91f90: ldur            w1, [x0, #0x17]
    // 0xe91f94: DecompressPointer r1
    //     0xe91f94: add             x1, x1, HEAP, lsl #32
    // 0xe91f98: stur            x1, [fp, #-0x10]
    // 0xe91f9c: cmp             w4, NULL
    // 0xe91fa0: b.ne            #0xe91fb0
    // 0xe91fa4: LoadField: r2 = r0->field_7
    //     0xe91fa4: ldur            w2, [x0, #7]
    // 0xe91fa8: DecompressPointer r2
    //     0xe91fa8: add             x2, x2, HEAP, lsl #32
    // 0xe91fac: b               #0xe91fb4
    // 0xe91fb0: mov             x2, x4
    // 0xe91fb4: stur            x2, [fp, #-8]
    // 0xe91fb8: LoadField: d0 = r0->field_b
    //     0xe91fb8: ldur            d0, [x0, #0xb]
    // 0xe91fbc: stur            d0, [fp, #-0x88]
    // 0xe91fc0: r0 = WidgetSpan()
    //     0xe91fc0: bl              #0xe93028  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x1c)
    // 0xe91fc4: mov             x1, x0
    // 0xe91fc8: ldur            x0, [fp, #-0x10]
    // 0xe91fcc: ArrayStore: r1[0] = r0  ; List_4
    //     0xe91fcc: stur            w0, [x1, #0x17]
    // 0xe91fd0: ldur            x0, [fp, #-8]
    // 0xe91fd4: StoreField: r1->field_7 = r0
    //     0xe91fd4: stur            w0, [x1, #7]
    // 0xe91fd8: ldur            d0, [fp, #-0x88]
    // 0xe91fdc: StoreField: r1->field_b = d0
    //     0xe91fdc: stur            d0, [x1, #0xb]
    // 0xe91fe0: mov             x4, x1
    // 0xe91fe4: ldur            x3, [fp, #-0x20]
    // 0xe91fe8: stur            x4, [fp, #-8]
    // 0xe91fec: LoadField: r2 = r3->field_7
    //     0xe91fec: ldur            w2, [x3, #7]
    // 0xe91ff0: DecompressPointer r2
    //     0xe91ff0: add             x2, x2, HEAP, lsl #32
    // 0xe91ff4: mov             x0, x4
    // 0xe91ff8: r1 = Null
    //     0xe91ff8: mov             x1, NULL
    // 0xe91ffc: cmp             w2, NULL
    // 0xe92000: b.eq            #0xe92020
    // 0xe92004: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe92004: ldur            w4, [x2, #0x17]
    // 0xe92008: DecompressPointer r4
    //     0xe92008: add             x4, x4, HEAP, lsl #32
    // 0xe9200c: r8 = X0
    //     0xe9200c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe92010: LoadField: r9 = r4->field_7
    //     0xe92010: ldur            x9, [x4, #7]
    // 0xe92014: r3 = Null
    //     0xe92014: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e510] Null
    //     0xe92018: ldr             x3, [x3, #0x510]
    // 0xe9201c: blr             x9
    // 0xe92020: ldur            x0, [fp, #-0x20]
    // 0xe92024: LoadField: r1 = r0->field_b
    //     0xe92024: ldur            w1, [x0, #0xb]
    // 0xe92028: LoadField: r2 = r0->field_f
    //     0xe92028: ldur            w2, [x0, #0xf]
    // 0xe9202c: DecompressPointer r2
    //     0xe9202c: add             x2, x2, HEAP, lsl #32
    // 0xe92030: LoadField: r3 = r2->field_b
    //     0xe92030: ldur            w3, [x2, #0xb]
    // 0xe92034: r2 = LoadInt32Instr(r1)
    //     0xe92034: sbfx            x2, x1, #1, #0x1f
    // 0xe92038: stur            x2, [fp, #-0x28]
    // 0xe9203c: r1 = LoadInt32Instr(r3)
    //     0xe9203c: sbfx            x1, x3, #1, #0x1f
    // 0xe92040: cmp             x2, x1
    // 0xe92044: b.ne            #0xe92050
    // 0xe92048: mov             x1, x0
    // 0xe9204c: r0 = _growToNextCapacity()
    //     0xe9204c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe92050: ldur            x0, [fp, #-0x20]
    // 0xe92054: ldur            x2, [fp, #-0x28]
    // 0xe92058: add             x1, x2, #1
    // 0xe9205c: lsl             x3, x1, #1
    // 0xe92060: StoreField: r0->field_b = r3
    //     0xe92060: stur            w3, [x0, #0xb]
    // 0xe92064: LoadField: r1 = r0->field_f
    //     0xe92064: ldur            w1, [x0, #0xf]
    // 0xe92068: DecompressPointer r1
    //     0xe92068: add             x1, x1, HEAP, lsl #32
    // 0xe9206c: ldur            x0, [fp, #-8]
    // 0xe92070: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe92070: add             x25, x1, x2, lsl #2
    //     0xe92074: add             x25, x25, #0xf
    //     0xe92078: str             w0, [x25]
    //     0xe9207c: tbz             w0, #0, #0xe92098
    //     0xe92080: ldurb           w16, [x1, #-1]
    //     0xe92084: ldurb           w17, [x0, #-1]
    //     0xe92088: and             x16, x17, x16, lsr #2
    //     0xe9208c: tst             x16, HEAP, lsr #32
    //     0xe92090: b.eq            #0xe92098
    //     0xe92094: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe92098: r0 = true
    //     0xe92098: add             x0, NULL, #0x20  ; true
    // 0xe9209c: LeaveFrame
    //     0xe9209c: mov             SP, fp
    //     0xe920a0: ldp             fp, lr, [SP], #0x10
    // 0xe920a4: ret
    //     0xe920a4: ret             
    // 0xe920a8: ldr             x4, [fp, #0x18]
    // 0xe920ac: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xe920ac: ldur            w3, [x0, #0x17]
    // 0xe920b0: DecompressPointer r3
    //     0xe920b0: add             x3, x3, HEAP, lsl #32
    // 0xe920b4: stur            x3, [fp, #-8]
    // 0xe920b8: cmp             w3, NULL
    // 0xe920bc: b.ne            #0xe920d0
    // 0xe920c0: r0 = true
    //     0xe920c0: add             x0, NULL, #0x20  ; true
    // 0xe920c4: LeaveFrame
    //     0xe920c4: mov             SP, fp
    //     0xe920c8: ldp             fp, lr, [SP], #0x10
    // 0xe920cc: ret
    //     0xe920cc: ret             
    // 0xe920d0: cmp             w4, NULL
    // 0xe920d4: b.eq            #0xe92a94
    // 0xe920d8: mov             x1, x4
    // 0xe920dc: r0 = font()
    //     0xe920dc: bl              #0xb0fcfc  ; [package:pdf/src/widgets/text_style.dart] TextStyle::font
    // 0xe920e0: cmp             w0, NULL
    // 0xe920e4: b.eq            #0xe92a98
    // 0xe920e8: ldur            x3, [fp, #-0x30]
    // 0xe920ec: LoadField: r2 = r3->field_13
    //     0xe920ec: ldur            w2, [x3, #0x13]
    // 0xe920f0: DecompressPointer r2
    //     0xe920f0: add             x2, x2, HEAP, lsl #32
    // 0xe920f4: mov             x1, x0
    // 0xe920f8: r0 = getFont()
    //     0xe920f8: bl              #0xe65b4c  ; [package:pdf/src/widgets/font.dart] Font::getFont
    // 0xe920fc: r1 = <int>
    //     0xe920fc: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe92100: stur            x0, [fp, #-0x10]
    // 0xe92104: r0 = Runes()
    //     0xe92104: bl              #0x7bc644  ; AllocateRunesStub -> Runes (size=0x10)
    // 0xe92108: mov             x1, x0
    // 0xe9210c: ldur            x0, [fp, #-8]
    // 0xe92110: StoreField: r1->field_b = r0
    //     0xe92110: stur            w0, [x1, #0xb]
    // 0xe92114: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe92114: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe92118: r0 = toList()
    //     0xe92118: bl              #0xa532a8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::toList
    // 0xe9211c: ldur            x1, [fp, #-0x10]
    // 0xe92120: r2 = LoadClassIdInstr(r1)
    //     0xe92120: ldur            x2, [x1, #-1]
    //     0xe92124: ubfx            x2, x2, #0xc, #0x14
    // 0xe92128: ldr             x3, [fp, #0x18]
    // 0xe9212c: stur            x2, [fp, #-0x50]
    // 0xe92130: LoadField: r4 = r3->field_1f
    //     0xe92130: ldur            w4, [x3, #0x1f]
    // 0xe92134: DecompressPointer r4
    //     0xe92134: add             x4, x4, HEAP, lsl #32
    // 0xe92138: ldur            x5, [fp, #-0x30]
    // 0xe9213c: stur            x4, [fp, #-0x48]
    // 0xe92140: ArrayLoad: r6 = r5[0]  ; List_4
    //     0xe92140: ldur            w6, [x5, #0x17]
    // 0xe92144: DecompressPointer r6
    //     0xe92144: add             x6, x6, HEAP, lsl #32
    // 0xe92148: ldr             x7, [fp, #0x20]
    // 0xe9214c: stur            x6, [fp, #-0x40]
    // 0xe92150: LoadField: d0 = r7->field_b
    //     0xe92150: ldur            d0, [x7, #0xb]
    // 0xe92154: stur            d0, [fp, #-0x88]
    // 0xe92158: LoadField: r7 = r3->field_23
    //     0xe92158: ldur            w7, [x3, #0x23]
    // 0xe9215c: DecompressPointer r7
    //     0xe9215c: add             x7, x7, HEAP, lsl #32
    // 0xe92160: stur            x7, [fp, #-0x38]
    // 0xe92164: LoadField: r8 = r3->field_b
    //     0xe92164: ldur            w8, [x3, #0xb]
    // 0xe92168: DecompressPointer r8
    //     0xe92168: add             x8, x8, HEAP, lsl #32
    // 0xe9216c: stur            x8, [fp, #-0x20]
    // 0xe92170: LoadField: r9 = r6->field_7
    //     0xe92170: ldur            w9, [x6, #7]
    // 0xe92174: DecompressPointer r9
    //     0xe92174: add             x9, x9, HEAP, lsl #32
    // 0xe92178: stur            x9, [fp, #-0x18]
    // 0xe9217c: mov             x11, x0
    // 0xe92180: r10 = 0
    //     0xe92180: movz            x10, #0
    // 0xe92184: stur            x11, [fp, #-8]
    // 0xe92188: stur            x10, [fp, #-0x28]
    // 0xe9218c: CheckStackOverflow
    //     0xe9218c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe92190: cmp             SP, x16
    //     0xe92194: b.ls            #0xe92a9c
    // 0xe92198: r0 = LoadClassIdInstr(r11)
    //     0xe92198: ldur            x0, [x11, #-1]
    //     0xe9219c: ubfx            x0, x0, #0xc, #0x14
    // 0xe921a0: str             x11, [SP]
    // 0xe921a4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xe921a4: movz            x17, #0xc834
    //     0xe921a8: add             lr, x0, x17
    //     0xe921ac: ldr             lr, [x21, lr, lsl #3]
    //     0xe921b0: blr             lr
    // 0xe921b4: r1 = LoadInt32Instr(r0)
    //     0xe921b4: sbfx            x1, x0, #1, #0x1f
    //     0xe921b8: tbz             w0, #0, #0xe921c0
    //     0xe921bc: ldur            x1, [x0, #7]
    // 0xe921c0: ldur            x2, [fp, #-0x28]
    // 0xe921c4: cmp             x2, x1
    // 0xe921c8: b.ge            #0xe92994
    // 0xe921cc: ldur            x3, [fp, #-8]
    // 0xe921d0: r0 = BoxInt64Instr(r2)
    //     0xe921d0: sbfiz           x0, x2, #1, #0x1f
    //     0xe921d4: cmp             x2, x0, asr #1
    //     0xe921d8: b.eq            #0xe921e4
    //     0xe921dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe921e0: stur            x2, [x0, #7]
    // 0xe921e4: mov             x1, x0
    // 0xe921e8: stur            x1, [fp, #-0x58]
    // 0xe921ec: r0 = LoadClassIdInstr(r3)
    //     0xe921ec: ldur            x0, [x3, #-1]
    //     0xe921f0: ubfx            x0, x0, #0xc, #0x14
    // 0xe921f4: stp             x1, x3, [SP]
    // 0xe921f8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xe921f8: movz            x17, #0x3037
    //     0xe921fc: movk            x17, #0x1, lsl #16
    //     0xe92200: add             lr, x0, x17
    //     0xe92204: ldr             lr, [x21, lr, lsl #3]
    //     0xe92208: blr             lr
    // 0xe9220c: mov             x2, x0
    // 0xe92210: r0 = _ConstSet len:18
    //     0xe92210: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e520] Set<int>(18)
    //     0xe92214: ldr             x0, [x0, #0x520]
    // 0xe92218: stur            x2, [fp, #-0x60]
    // 0xe9221c: LoadField: r1 = r0->field_1b
    //     0xe9221c: ldur            w1, [x0, #0x1b]
    // 0xe92220: DecompressPointer r1
    //     0xe92220: add             x1, x1, HEAP, lsl #32
    // 0xe92224: cmp             w1, NULL
    // 0xe92228: b.ne            #0xe92234
    // 0xe9222c: mov             x1, x0
    // 0xe92230: r0 = _createIndex()
    //     0xe92230: bl              #0x86ae38  ; [dart:_compact_hash] __ConstSet&_HashVMImmutableBase&SetMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashSetMixin&_UnmodifiableSetMixin&_ImmutableLinkedHashSetMixin::_createIndex
    // 0xe92234: ldur            x2, [fp, #-0x60]
    // 0xe92238: r1 = _ConstSet len:18
    //     0xe92238: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e520] Set<int>(18)
    //     0xe9223c: ldr             x1, [x1, #0x520]
    // 0xe92240: r0 = contains()
    //     0xe92240: bl              #0x86ab50  ; [dart:_compact_hash] __ConstSet&_HashVMImmutableBase&SetMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashSetMixin::contains
    // 0xe92244: tbnz            w0, #4, #0xe92254
    // 0xe92248: ldur            x11, [fp, #-8]
    // 0xe9224c: ldur            x0, [fp, #-0x28]
    // 0xe92250: b               #0xe92964
    // 0xe92254: ldur            x0, [fp, #-0x50]
    // 0xe92258: cmp             x0, #0x37f
    // 0xe9225c: b.ne            #0xe92288
    // 0xe92260: ldur            x3, [fp, #-0x60]
    // 0xe92264: r1 = LoadInt32Instr(r3)
    //     0xe92264: sbfx            x1, x3, #1, #0x1f
    //     0xe92268: tbz             w3, #0, #0xe92270
    //     0xe9226c: ldur            x1, [x3, #7]
    // 0xe92270: tbnz            x1, #0x3f, #0xe922b0
    // 0xe92274: cmp             x1, #0xff
    // 0xe92278: b.gt            #0xe922b0
    // 0xe9227c: ldur            x1, [fp, #-8]
    // 0xe92280: ldur            x0, [fp, #-0x28]
    // 0xe92284: b               #0xe92960
    // 0xe92288: ldur            x4, [fp, #-0x10]
    // 0xe9228c: ldur            x3, [fp, #-0x60]
    // 0xe92290: LoadField: r1 = r4->field_3f
    //     0xe92290: ldur            w1, [x4, #0x3f]
    // 0xe92294: DecompressPointer r1
    //     0xe92294: add             x1, x1, HEAP, lsl #32
    // 0xe92298: LoadField: r2 = r1->field_13
    //     0xe92298: ldur            w2, [x1, #0x13]
    // 0xe9229c: DecompressPointer r2
    //     0xe9229c: add             x2, x2, HEAP, lsl #32
    // 0xe922a0: mov             x1, x2
    // 0xe922a4: mov             x2, x3
    // 0xe922a8: r0 = containsKey()
    //     0xe922a8: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xe922ac: tbz             w0, #4, #0xe92958
    // 0xe922b0: ldur            x0, [fp, #-0x28]
    // 0xe922b4: cmp             x0, #0
    // 0xe922b8: b.le            #0xe923ac
    // 0xe922bc: ldr             x4, [fp, #0x18]
    // 0xe922c0: ldur            x5, [fp, #-0x40]
    // 0xe922c4: ldur            d0, [fp, #-0x88]
    // 0xe922c8: ldur            x1, [fp, #-8]
    // 0xe922cc: ldur            x3, [fp, #-0x58]
    // 0xe922d0: r2 = 0
    //     0xe922d0: movz            x2, #0
    // 0xe922d4: r0 = createFromCharCodes()
    //     0xe922d4: bl              #0x601670  ; [dart:core] _StringBase::createFromCharCodes
    // 0xe922d8: stur            x0, [fp, #-0x58]
    // 0xe922dc: r0 = TextSpan()
    //     0xe922dc: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xe922e0: mov             x3, x0
    // 0xe922e4: ldur            x0, [fp, #-0x58]
    // 0xe922e8: stur            x3, [fp, #-0x68]
    // 0xe922ec: ArrayStore: r3[0] = r0  ; List_4
    //     0xe922ec: stur            w0, [x3, #0x17]
    // 0xe922f0: ldr             x4, [fp, #0x18]
    // 0xe922f4: StoreField: r3->field_7 = r4
    //     0xe922f4: stur            w4, [x3, #7]
    // 0xe922f8: ldur            d0, [fp, #-0x88]
    // 0xe922fc: StoreField: r3->field_b = d0
    //     0xe922fc: stur            d0, [x3, #0xb]
    // 0xe92300: mov             x0, x3
    // 0xe92304: ldur            x2, [fp, #-0x18]
    // 0xe92308: r1 = Null
    //     0xe92308: mov             x1, NULL
    // 0xe9230c: cmp             w2, NULL
    // 0xe92310: b.eq            #0xe92330
    // 0xe92314: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe92314: ldur            w4, [x2, #0x17]
    // 0xe92318: DecompressPointer r4
    //     0xe92318: add             x4, x4, HEAP, lsl #32
    // 0xe9231c: r8 = X0
    //     0xe9231c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe92320: LoadField: r9 = r4->field_7
    //     0xe92320: ldur            x9, [x4, #7]
    // 0xe92324: r3 = Null
    //     0xe92324: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e528] Null
    //     0xe92328: ldr             x3, [x3, #0x528]
    // 0xe9232c: blr             x9
    // 0xe92330: ldur            x0, [fp, #-0x40]
    // 0xe92334: LoadField: r1 = r0->field_b
    //     0xe92334: ldur            w1, [x0, #0xb]
    // 0xe92338: LoadField: r2 = r0->field_f
    //     0xe92338: ldur            w2, [x0, #0xf]
    // 0xe9233c: DecompressPointer r2
    //     0xe9233c: add             x2, x2, HEAP, lsl #32
    // 0xe92340: LoadField: r3 = r2->field_b
    //     0xe92340: ldur            w3, [x2, #0xb]
    // 0xe92344: r2 = LoadInt32Instr(r1)
    //     0xe92344: sbfx            x2, x1, #1, #0x1f
    // 0xe92348: stur            x2, [fp, #-0x70]
    // 0xe9234c: r1 = LoadInt32Instr(r3)
    //     0xe9234c: sbfx            x1, x3, #1, #0x1f
    // 0xe92350: cmp             x2, x1
    // 0xe92354: b.ne            #0xe92360
    // 0xe92358: mov             x1, x0
    // 0xe9235c: r0 = _growToNextCapacity()
    //     0xe9235c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe92360: ldur            x2, [fp, #-0x40]
    // 0xe92364: ldur            x3, [fp, #-0x70]
    // 0xe92368: add             x0, x3, #1
    // 0xe9236c: lsl             x1, x0, #1
    // 0xe92370: StoreField: r2->field_b = r1
    //     0xe92370: stur            w1, [x2, #0xb]
    // 0xe92374: LoadField: r1 = r2->field_f
    //     0xe92374: ldur            w1, [x2, #0xf]
    // 0xe92378: DecompressPointer r1
    //     0xe92378: add             x1, x1, HEAP, lsl #32
    // 0xe9237c: ldur            x0, [fp, #-0x68]
    // 0xe92380: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe92380: add             x25, x1, x3, lsl #2
    //     0xe92384: add             x25, x25, #0xf
    //     0xe92388: str             w0, [x25]
    //     0xe9238c: tbz             w0, #0, #0xe923a8
    //     0xe92390: ldurb           w16, [x1, #-1]
    //     0xe92394: ldurb           w17, [x0, #-1]
    //     0xe92398: and             x16, x17, x16, lsr #2
    //     0xe9239c: tst             x16, HEAP, lsr #32
    //     0xe923a0: b.eq            #0xe923a8
    //     0xe923a4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe923a8: b               #0xe923b0
    // 0xe923ac: ldur            x2, [fp, #-0x40]
    // 0xe923b0: ldur            x3, [fp, #-0x60]
    // 0xe923b4: ldur            x4, [fp, #-0x48]
    // 0xe923b8: r0 = LoadClassIdInstr(r4)
    //     0xe923b8: ldur            x0, [x4, #-1]
    //     0xe923bc: ubfx            x0, x0, #0xc, #0x14
    // 0xe923c0: mov             x1, x4
    // 0xe923c4: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xe923c4: movz            x17, #0xd35d
    //     0xe923c8: add             lr, x0, x17
    //     0xe923cc: ldr             lr, [x21, lr, lsl #3]
    //     0xe923d0: blr             lr
    // 0xe923d4: mov             x3, x0
    // 0xe923d8: ldur            x2, [fp, #-0x60]
    // 0xe923dc: stur            x3, [fp, #-0x58]
    // 0xe923e0: r4 = LoadInt32Instr(r2)
    //     0xe923e0: sbfx            x4, x2, #1, #0x1f
    //     0xe923e4: tbz             w2, #0, #0xe923ec
    //     0xe923e8: ldur            x4, [x2, #7]
    // 0xe923ec: stur            x4, [fp, #-0x70]
    // 0xe923f0: ldur            x5, [fp, #-0x30]
    // 0xe923f4: CheckStackOverflow
    //     0xe923f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe923f8: cmp             SP, x16
    //     0xe923fc: b.ls            #0xe92aa4
    // 0xe92400: r0 = LoadClassIdInstr(r3)
    //     0xe92400: ldur            x0, [x3, #-1]
    //     0xe92404: ubfx            x0, x0, #0xc, #0x14
    // 0xe92408: mov             x1, x3
    // 0xe9240c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xe9240c: movz            x17, #0x292d
    //     0xe92410: movk            x17, #0x1, lsl #16
    //     0xe92414: add             lr, x0, x17
    //     0xe92418: ldr             lr, [x21, lr, lsl #3]
    //     0xe9241c: blr             lr
    // 0xe92420: tbnz            w0, #4, #0xe927a4
    // 0xe92424: ldur            x3, [fp, #-0x30]
    // 0xe92428: ldur            x2, [fp, #-0x58]
    // 0xe9242c: r0 = LoadClassIdInstr(r2)
    //     0xe9242c: ldur            x0, [x2, #-1]
    //     0xe92430: ubfx            x0, x0, #0xc, #0x14
    // 0xe92434: mov             x1, x2
    // 0xe92438: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xe92438: movz            x17, #0x384d
    //     0xe9243c: movk            x17, #0x1, lsl #16
    //     0xe92440: add             lr, x0, x17
    //     0xe92444: ldr             lr, [x21, lr, lsl #3]
    //     0xe92448: blr             lr
    // 0xe9244c: mov             x3, x0
    // 0xe92450: ldur            x0, [fp, #-0x30]
    // 0xe92454: stur            x3, [fp, #-0x68]
    // 0xe92458: LoadField: r2 = r0->field_13
    //     0xe92458: ldur            w2, [x0, #0x13]
    // 0xe9245c: DecompressPointer r2
    //     0xe9245c: add             x2, x2, HEAP, lsl #32
    // 0xe92460: mov             x1, x3
    // 0xe92464: r0 = getFont()
    //     0xe92464: bl              #0xe65b4c  ; [package:pdf/src/widgets/font.dart] Font::getFont
    // 0xe92468: stur            x0, [fp, #-0x80]
    // 0xe9246c: r3 = LoadClassIdInstr(r0)
    //     0xe9246c: ldur            x3, [x0, #-1]
    //     0xe92470: ubfx            x3, x3, #0xc, #0x14
    // 0xe92474: stur            x3, [fp, #-0x78]
    // 0xe92478: cmp             x3, #0x37f
    // 0xe9247c: b.ne            #0xe92498
    // 0xe92480: ldur            x4, [fp, #-0x70]
    // 0xe92484: tbnz            x4, #0x3f, #0xe924bc
    // 0xe92488: cmp             x4, #0xff
    // 0xe9248c: b.gt            #0xe924bc
    // 0xe92490: mov             x0, x3
    // 0xe92494: b               #0xe924d0
    // 0xe92498: ldur            x4, [fp, #-0x70]
    // 0xe9249c: LoadField: r1 = r0->field_3f
    //     0xe9249c: ldur            w1, [x0, #0x3f]
    // 0xe924a0: DecompressPointer r1
    //     0xe924a0: add             x1, x1, HEAP, lsl #32
    // 0xe924a4: LoadField: r2 = r1->field_13
    //     0xe924a4: ldur            w2, [x1, #0x13]
    // 0xe924a8: DecompressPointer r2
    //     0xe924a8: add             x2, x2, HEAP, lsl #32
    // 0xe924ac: mov             x1, x2
    // 0xe924b0: ldur            x2, [fp, #-0x60]
    // 0xe924b4: r0 = containsKey()
    //     0xe924b4: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xe924b8: tbz             w0, #4, #0xe924cc
    // 0xe924bc: ldur            x2, [fp, #-0x60]
    // 0xe924c0: ldur            x3, [fp, #-0x58]
    // 0xe924c4: ldur            x4, [fp, #-0x70]
    // 0xe924c8: b               #0xe923f0
    // 0xe924cc: ldur            x0, [fp, #-0x78]
    // 0xe924d0: cmp             x0, #0x380
    // 0xe924d4: b.ne            #0xe9263c
    // 0xe924d8: ldur            x0, [fp, #-0x80]
    // 0xe924dc: LoadField: r1 = r0->field_3f
    //     0xe924dc: ldur            w1, [x0, #0x3f]
    // 0xe924e0: DecompressPointer r1
    //     0xe924e0: add             x1, x1, HEAP, lsl #32
    // 0xe924e4: LoadField: r0 = r1->field_23
    //     0xe924e4: ldur            w0, [x1, #0x23]
    // 0xe924e8: DecompressPointer r0
    //     0xe924e8: add             x0, x0, HEAP, lsl #32
    // 0xe924ec: stur            x0, [fp, #-0x80]
    // 0xe924f0: LoadField: r3 = r1->field_13
    //     0xe924f0: ldur            w3, [x1, #0x13]
    // 0xe924f4: DecompressPointer r3
    //     0xe924f4: add             x3, x3, HEAP, lsl #32
    // 0xe924f8: mov             x1, x3
    // 0xe924fc: ldur            x2, [fp, #-0x60]
    // 0xe92500: stur            x3, [fp, #-0x58]
    // 0xe92504: r0 = _getValueOrData()
    //     0xe92504: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xe92508: mov             x1, x0
    // 0xe9250c: ldur            x0, [fp, #-0x58]
    // 0xe92510: LoadField: r2 = r0->field_f
    //     0xe92510: ldur            w2, [x0, #0xf]
    // 0xe92514: DecompressPointer r2
    //     0xe92514: add             x2, x2, HEAP, lsl #32
    // 0xe92518: cmp             w2, w1
    // 0xe9251c: b.ne            #0xe92528
    // 0xe92520: r2 = Null
    //     0xe92520: mov             x2, NULL
    // 0xe92524: b               #0xe9252c
    // 0xe92528: mov             x2, x1
    // 0xe9252c: ldur            x0, [fp, #-0x80]
    // 0xe92530: mov             x1, x0
    // 0xe92534: r0 = _getValueOrData()
    //     0xe92534: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xe92538: mov             x1, x0
    // 0xe9253c: ldur            x0, [fp, #-0x80]
    // 0xe92540: LoadField: r2 = r0->field_f
    //     0xe92540: ldur            w2, [x0, #0xf]
    // 0xe92544: DecompressPointer r2
    //     0xe92544: add             x2, x2, HEAP, lsl #32
    // 0xe92548: cmp             w2, w1
    // 0xe9254c: b.ne            #0xe92558
    // 0xe92550: r2 = Null
    //     0xe92550: mov             x2, NULL
    // 0xe92554: b               #0xe9255c
    // 0xe92558: mov             x2, x1
    // 0xe9255c: cmp             w2, NULL
    // 0xe92560: b.eq            #0xe92634
    // 0xe92564: ldur            x0, [fp, #-0x30]
    // 0xe92568: ldur            x4, [fp, #-0x40]
    // 0xe9256c: LoadField: r1 = r0->field_f
    //     0xe9256c: ldur            w1, [x0, #0xf]
    // 0xe92570: DecompressPointer r1
    //     0xe92570: add             x1, x1, HEAP, lsl #32
    // 0xe92574: ldur            d0, [fp, #-0x88]
    // 0xe92578: ldr             x3, [fp, #0x18]
    // 0xe9257c: r0 = _addEmoji()
    //     0xe9257c: bl              #0xe92b84  ; [package:pdf/src/widgets/text.dart] RichText::_addEmoji
    // 0xe92580: ldur            x2, [fp, #-0x18]
    // 0xe92584: mov             x3, x0
    // 0xe92588: r1 = Null
    //     0xe92588: mov             x1, NULL
    // 0xe9258c: stur            x3, [fp, #-0x58]
    // 0xe92590: cmp             w2, NULL
    // 0xe92594: b.eq            #0xe925b4
    // 0xe92598: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe92598: ldur            w4, [x2, #0x17]
    // 0xe9259c: DecompressPointer r4
    //     0xe9259c: add             x4, x4, HEAP, lsl #32
    // 0xe925a0: r8 = X0
    //     0xe925a0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe925a4: LoadField: r9 = r4->field_7
    //     0xe925a4: ldur            x9, [x4, #7]
    // 0xe925a8: r3 = Null
    //     0xe925a8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e538] Null
    //     0xe925ac: ldr             x3, [x3, #0x538]
    // 0xe925b0: blr             x9
    // 0xe925b4: ldur            x0, [fp, #-0x40]
    // 0xe925b8: LoadField: r1 = r0->field_b
    //     0xe925b8: ldur            w1, [x0, #0xb]
    // 0xe925bc: LoadField: r2 = r0->field_f
    //     0xe925bc: ldur            w2, [x0, #0xf]
    // 0xe925c0: DecompressPointer r2
    //     0xe925c0: add             x2, x2, HEAP, lsl #32
    // 0xe925c4: LoadField: r3 = r2->field_b
    //     0xe925c4: ldur            w3, [x2, #0xb]
    // 0xe925c8: r2 = LoadInt32Instr(r1)
    //     0xe925c8: sbfx            x2, x1, #1, #0x1f
    // 0xe925cc: stur            x2, [fp, #-0x70]
    // 0xe925d0: r1 = LoadInt32Instr(r3)
    //     0xe925d0: sbfx            x1, x3, #1, #0x1f
    // 0xe925d4: cmp             x2, x1
    // 0xe925d8: b.ne            #0xe925e4
    // 0xe925dc: mov             x1, x0
    // 0xe925e0: r0 = _growToNextCapacity()
    //     0xe925e0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe925e4: ldur            x3, [fp, #-0x40]
    // 0xe925e8: ldur            x2, [fp, #-0x70]
    // 0xe925ec: add             x0, x2, #1
    // 0xe925f0: lsl             x1, x0, #1
    // 0xe925f4: StoreField: r3->field_b = r1
    //     0xe925f4: stur            w1, [x3, #0xb]
    // 0xe925f8: LoadField: r1 = r3->field_f
    //     0xe925f8: ldur            w1, [x3, #0xf]
    // 0xe925fc: DecompressPointer r1
    //     0xe925fc: add             x1, x1, HEAP, lsl #32
    // 0xe92600: ldur            x0, [fp, #-0x58]
    // 0xe92604: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe92604: add             x25, x1, x2, lsl #2
    //     0xe92608: add             x25, x25, #0xf
    //     0xe9260c: str             w0, [x25]
    //     0xe92610: tbz             w0, #0, #0xe9262c
    //     0xe92614: ldurb           w16, [x1, #-1]
    //     0xe92618: ldurb           w17, [x0, #-1]
    //     0xe9261c: and             x16, x17, x16, lsr #2
    //     0xe92620: tst             x16, HEAP, lsr #32
    //     0xe92624: b.eq            #0xe9262c
    //     0xe92628: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe9262c: mov             x2, x3
    // 0xe92630: b               #0xe9279c
    // 0xe92634: ldur            x3, [fp, #-0x40]
    // 0xe92638: b               #0xe92640
    // 0xe9263c: ldur            x3, [fp, #-0x40]
    // 0xe92640: ldur            x0, [fp, #-0x60]
    // 0xe92644: ldur            d0, [fp, #-0x88]
    // 0xe92648: r4 = 2
    //     0xe92648: movz            x4, #0x2
    // 0xe9264c: mov             x2, x4
    // 0xe92650: r1 = Null
    //     0xe92650: mov             x1, NULL
    // 0xe92654: r0 = AllocateArray()
    //     0xe92654: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe92658: mov             x2, x0
    // 0xe9265c: ldur            x0, [fp, #-0x60]
    // 0xe92660: stur            x2, [fp, #-0x58]
    // 0xe92664: StoreField: r2->field_f = r0
    //     0xe92664: stur            w0, [x2, #0xf]
    // 0xe92668: r1 = <int>
    //     0xe92668: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe9266c: r0 = AllocateGrowableArray()
    //     0xe9266c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe92670: mov             x2, x0
    // 0xe92674: ldur            x0, [fp, #-0x58]
    // 0xe92678: stur            x2, [fp, #-0x60]
    // 0xe9267c: StoreField: r2->field_f = r0
    //     0xe9267c: stur            w0, [x2, #0xf]
    // 0xe92680: r0 = 2
    //     0xe92680: movz            x0, #0x2
    // 0xe92684: StoreField: r2->field_b = r0
    //     0xe92684: stur            w0, [x2, #0xb]
    // 0xe92688: ldur            x16, [fp, #-0x68]
    // 0xe9268c: ldur            lr, [fp, #-0x68]
    // 0xe92690: stp             lr, x16, [SP, #0x18]
    // 0xe92694: ldur            x16, [fp, #-0x68]
    // 0xe92698: ldur            lr, [fp, #-0x68]
    // 0xe9269c: stp             lr, x16, [SP, #8]
    // 0xe926a0: ldur            x16, [fp, #-0x68]
    // 0xe926a4: str             x16, [SP]
    // 0xe926a8: ldr             x1, [fp, #0x18]
    // 0xe926ac: r4 = const [0, 0x6, 0x5, 0x1, font, 0x1, fontBold, 0x3, fontBoldItalic, 0x4, fontItalic, 0x5, fontNormal, 0x2, null]
    //     0xe926ac: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e548] List(15) [0, 0x6, 0x5, 0x1, "font", 0x1, "fontBold", 0x3, "fontBoldItalic", 0x4, "fontItalic", 0x5, "fontNormal", 0x2, Null]
    //     0xe926b0: ldr             x4, [x4, #0x548]
    // 0xe926b4: r0 = copyWith()
    //     0xe926b4: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xe926b8: ldur            x1, [fp, #-0x60]
    // 0xe926bc: r2 = 0
    //     0xe926bc: movz            x2, #0
    // 0xe926c0: r3 = Null
    //     0xe926c0: mov             x3, NULL
    // 0xe926c4: stur            x0, [fp, #-0x58]
    // 0xe926c8: r0 = createFromCharCodes()
    //     0xe926c8: bl              #0x601670  ; [dart:core] _StringBase::createFromCharCodes
    // 0xe926cc: stur            x0, [fp, #-0x60]
    // 0xe926d0: r0 = TextSpan()
    //     0xe926d0: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xe926d4: mov             x3, x0
    // 0xe926d8: ldur            x0, [fp, #-0x60]
    // 0xe926dc: stur            x3, [fp, #-0x68]
    // 0xe926e0: ArrayStore: r3[0] = r0  ; List_4
    //     0xe926e0: stur            w0, [x3, #0x17]
    // 0xe926e4: ldur            x0, [fp, #-0x58]
    // 0xe926e8: StoreField: r3->field_7 = r0
    //     0xe926e8: stur            w0, [x3, #7]
    // 0xe926ec: ldur            d0, [fp, #-0x88]
    // 0xe926f0: StoreField: r3->field_b = d0
    //     0xe926f0: stur            d0, [x3, #0xb]
    // 0xe926f4: mov             x0, x3
    // 0xe926f8: ldur            x2, [fp, #-0x18]
    // 0xe926fc: r1 = Null
    //     0xe926fc: mov             x1, NULL
    // 0xe92700: cmp             w2, NULL
    // 0xe92704: b.eq            #0xe92724
    // 0xe92708: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe92708: ldur            w4, [x2, #0x17]
    // 0xe9270c: DecompressPointer r4
    //     0xe9270c: add             x4, x4, HEAP, lsl #32
    // 0xe92710: r8 = X0
    //     0xe92710: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe92714: LoadField: r9 = r4->field_7
    //     0xe92714: ldur            x9, [x4, #7]
    // 0xe92718: r3 = Null
    //     0xe92718: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e550] Null
    //     0xe9271c: ldr             x3, [x3, #0x550]
    // 0xe92720: blr             x9
    // 0xe92724: ldur            x0, [fp, #-0x40]
    // 0xe92728: LoadField: r1 = r0->field_b
    //     0xe92728: ldur            w1, [x0, #0xb]
    // 0xe9272c: LoadField: r2 = r0->field_f
    //     0xe9272c: ldur            w2, [x0, #0xf]
    // 0xe92730: DecompressPointer r2
    //     0xe92730: add             x2, x2, HEAP, lsl #32
    // 0xe92734: LoadField: r3 = r2->field_b
    //     0xe92734: ldur            w3, [x2, #0xb]
    // 0xe92738: r2 = LoadInt32Instr(r1)
    //     0xe92738: sbfx            x2, x1, #1, #0x1f
    // 0xe9273c: stur            x2, [fp, #-0x70]
    // 0xe92740: r1 = LoadInt32Instr(r3)
    //     0xe92740: sbfx            x1, x3, #1, #0x1f
    // 0xe92744: cmp             x2, x1
    // 0xe92748: b.ne            #0xe92754
    // 0xe9274c: mov             x1, x0
    // 0xe92750: r0 = _growToNextCapacity()
    //     0xe92750: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe92754: ldur            x2, [fp, #-0x40]
    // 0xe92758: ldur            x3, [fp, #-0x70]
    // 0xe9275c: add             x0, x3, #1
    // 0xe92760: lsl             x1, x0, #1
    // 0xe92764: StoreField: r2->field_b = r1
    //     0xe92764: stur            w1, [x2, #0xb]
    // 0xe92768: LoadField: r1 = r2->field_f
    //     0xe92768: ldur            w1, [x2, #0xf]
    // 0xe9276c: DecompressPointer r1
    //     0xe9276c: add             x1, x1, HEAP, lsl #32
    // 0xe92770: ldur            x0, [fp, #-0x68]
    // 0xe92774: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe92774: add             x25, x1, x3, lsl #2
    //     0xe92778: add             x25, x25, #0xf
    //     0xe9277c: str             w0, [x25]
    //     0xe92780: tbz             w0, #0, #0xe9279c
    //     0xe92784: ldurb           w16, [x1, #-1]
    //     0xe92788: ldurb           w17, [x0, #-1]
    //     0xe9278c: and             x16, x17, x16, lsr #2
    //     0xe92790: tst             x16, HEAP, lsr #32
    //     0xe92794: b.eq            #0xe9279c
    //     0xe92798: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe9279c: mov             x3, x2
    // 0xe927a0: b               #0xe92920
    // 0xe927a4: ldr             x0, [fp, #0x18]
    // 0xe927a8: ldur            x2, [fp, #-0x40]
    // 0xe927ac: ldur            d0, [fp, #-0x88]
    // 0xe927b0: ldur            x1, [fp, #-0x38]
    // 0xe927b4: ldur            x3, [fp, #-0x20]
    // 0xe927b8: d1 = 2.000000
    //     0xe927b8: fmov            d1, #2.00000000
    // 0xe927bc: cmp             w1, NULL
    // 0xe927c0: b.eq            #0xe92aac
    // 0xe927c4: LoadField: d2 = r1->field_7
    //     0xe927c4: ldur            d2, [x1, #7]
    // 0xe927c8: fdiv            d3, d2, d1
    // 0xe927cc: stur            d3, [fp, #-0x90]
    // 0xe927d0: cmp             w3, NULL
    // 0xe927d4: b.eq            #0xe92ab0
    // 0xe927d8: r0 = Placeholder()
    //     0xe927d8: bl              #0xe92b78  ; AllocatePlaceholderStub -> Placeholder (size=0x28)
    // 0xe927dc: mov             x1, x0
    // 0xe927e0: ldur            x0, [fp, #-0x20]
    // 0xe927e4: stur            x1, [fp, #-0x60]
    // 0xe927e8: StoreField: r1->field_b = r0
    //     0xe927e8: stur            w0, [x1, #0xb]
    // 0xe927ec: d0 = 1.000000
    //     0xe927ec: fmov            d0, #1.00000000
    // 0xe927f0: StoreField: r1->field_f = d0
    //     0xe927f0: stur            d0, [x1, #0xf]
    // 0xe927f4: d1 = 400.000000
    //     0xe927f4: ldr             d1, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(400) from 0x4079000000000000
    // 0xe927f8: ArrayStore: r1[0] = d1  ; List_8
    //     0xe927f8: stur            d1, [x1, #0x17]
    // 0xe927fc: StoreField: r1->field_1f = d1
    //     0xe927fc: stur            d1, [x1, #0x1f]
    // 0xe92800: ldur            d2, [fp, #-0x90]
    // 0xe92804: r2 = inline_Allocate_Double()
    //     0xe92804: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xe92808: add             x2, x2, #0x10
    //     0xe9280c: cmp             x3, x2
    //     0xe92810: b.ls            #0xe92ab4
    //     0xe92814: str             x2, [THR, #0x50]  ; THR::top
    //     0xe92818: sub             x2, x2, #0xf
    //     0xe9281c: movz            x3, #0xe15c
    //     0xe92820: movk            x3, #0x3, lsl #16
    //     0xe92824: stur            x3, [x2, #-1]
    // 0xe92828: StoreField: r2->field_7 = d2
    //     0xe92828: stur            d2, [x2, #7]
    // 0xe9282c: stur            x2, [fp, #-0x58]
    // 0xe92830: r0 = SizedBox()
    //     0xe92830: bl              #0xb13e18  ; AllocateSizedBoxStub -> SizedBox (size=0x1c)
    // 0xe92834: mov             x1, x0
    // 0xe92838: ldur            x0, [fp, #-0x58]
    // 0xe9283c: stur            x1, [fp, #-0x68]
    // 0xe92840: StoreField: r1->field_f = r0
    //     0xe92840: stur            w0, [x1, #0xf]
    // 0xe92844: ldur            x0, [fp, #-0x38]
    // 0xe92848: StoreField: r1->field_13 = r0
    //     0xe92848: stur            w0, [x1, #0x13]
    // 0xe9284c: ldur            x2, [fp, #-0x60]
    // 0xe92850: ArrayStore: r1[0] = r2  ; List_4
    //     0xe92850: stur            w2, [x1, #0x17]
    // 0xe92854: r0 = WidgetSpan()
    //     0xe92854: bl              #0xe93028  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x1c)
    // 0xe92858: mov             x3, x0
    // 0xe9285c: ldur            x0, [fp, #-0x68]
    // 0xe92860: stur            x3, [fp, #-0x58]
    // 0xe92864: ArrayStore: r3[0] = r0  ; List_4
    //     0xe92864: stur            w0, [x3, #0x17]
    // 0xe92868: ldr             x4, [fp, #0x18]
    // 0xe9286c: StoreField: r3->field_7 = r4
    //     0xe9286c: stur            w4, [x3, #7]
    // 0xe92870: ldur            d0, [fp, #-0x88]
    // 0xe92874: StoreField: r3->field_b = d0
    //     0xe92874: stur            d0, [x3, #0xb]
    // 0xe92878: mov             x0, x3
    // 0xe9287c: ldur            x2, [fp, #-0x18]
    // 0xe92880: r1 = Null
    //     0xe92880: mov             x1, NULL
    // 0xe92884: cmp             w2, NULL
    // 0xe92888: b.eq            #0xe928a8
    // 0xe9288c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe9288c: ldur            w4, [x2, #0x17]
    // 0xe92890: DecompressPointer r4
    //     0xe92890: add             x4, x4, HEAP, lsl #32
    // 0xe92894: r8 = X0
    //     0xe92894: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe92898: LoadField: r9 = r4->field_7
    //     0xe92898: ldur            x9, [x4, #7]
    // 0xe9289c: r3 = Null
    //     0xe9289c: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e560] Null
    //     0xe928a0: ldr             x3, [x3, #0x560]
    // 0xe928a4: blr             x9
    // 0xe928a8: ldur            x0, [fp, #-0x40]
    // 0xe928ac: LoadField: r1 = r0->field_b
    //     0xe928ac: ldur            w1, [x0, #0xb]
    // 0xe928b0: LoadField: r2 = r0->field_f
    //     0xe928b0: ldur            w2, [x0, #0xf]
    // 0xe928b4: DecompressPointer r2
    //     0xe928b4: add             x2, x2, HEAP, lsl #32
    // 0xe928b8: LoadField: r3 = r2->field_b
    //     0xe928b8: ldur            w3, [x2, #0xb]
    // 0xe928bc: r2 = LoadInt32Instr(r1)
    //     0xe928bc: sbfx            x2, x1, #1, #0x1f
    // 0xe928c0: stur            x2, [fp, #-0x70]
    // 0xe928c4: r1 = LoadInt32Instr(r3)
    //     0xe928c4: sbfx            x1, x3, #1, #0x1f
    // 0xe928c8: cmp             x2, x1
    // 0xe928cc: b.ne            #0xe928d8
    // 0xe928d0: mov             x1, x0
    // 0xe928d4: r0 = _growToNextCapacity()
    //     0xe928d4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe928d8: ldur            x3, [fp, #-0x40]
    // 0xe928dc: ldur            x2, [fp, #-0x70]
    // 0xe928e0: add             x0, x2, #1
    // 0xe928e4: lsl             x1, x0, #1
    // 0xe928e8: StoreField: r3->field_b = r1
    //     0xe928e8: stur            w1, [x3, #0xb]
    // 0xe928ec: LoadField: r1 = r3->field_f
    //     0xe928ec: ldur            w1, [x3, #0xf]
    // 0xe928f0: DecompressPointer r1
    //     0xe928f0: add             x1, x1, HEAP, lsl #32
    // 0xe928f4: ldur            x0, [fp, #-0x58]
    // 0xe928f8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe928f8: add             x25, x1, x2, lsl #2
    //     0xe928fc: add             x25, x25, #0xf
    //     0xe92900: str             w0, [x25]
    //     0xe92904: tbz             w0, #0, #0xe92920
    //     0xe92908: ldurb           w16, [x1, #-1]
    //     0xe9290c: ldurb           w17, [x0, #-1]
    //     0xe92910: and             x16, x17, x16, lsr #2
    //     0xe92914: tst             x16, HEAP, lsr #32
    //     0xe92918: b.eq            #0xe92920
    //     0xe9291c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe92920: ldur            x1, [fp, #-8]
    // 0xe92924: ldur            x0, [fp, #-0x28]
    // 0xe92928: add             x2, x0, #1
    // 0xe9292c: r0 = LoadClassIdInstr(r1)
    //     0xe9292c: ldur            x0, [x1, #-1]
    //     0xe92930: ubfx            x0, x0, #0xc, #0x14
    // 0xe92934: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe92934: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe92938: r0 = GDT[cid_x0 + 0x13344]()
    //     0xe92938: movz            x17, #0x3344
    //     0xe9293c: movk            x17, #0x1, lsl #16
    //     0xe92940: add             lr, x0, x17
    //     0xe92944: ldr             lr, [x21, lr, lsl #3]
    //     0xe92948: blr             lr
    // 0xe9294c: mov             x1, x0
    // 0xe92950: r0 = -1
    //     0xe92950: movn            x0, #0
    // 0xe92954: b               #0xe92960
    // 0xe92958: ldur            x1, [fp, #-8]
    // 0xe9295c: ldur            x0, [fp, #-0x28]
    // 0xe92960: mov             x11, x1
    // 0xe92964: add             x10, x0, #1
    // 0xe92968: ldr             x3, [fp, #0x18]
    // 0xe9296c: ldur            x5, [fp, #-0x30]
    // 0xe92970: ldur            x1, [fp, #-0x10]
    // 0xe92974: ldur            x4, [fp, #-0x48]
    // 0xe92978: ldur            x6, [fp, #-0x40]
    // 0xe9297c: ldur            d0, [fp, #-0x88]
    // 0xe92980: ldur            x2, [fp, #-0x50]
    // 0xe92984: ldur            x7, [fp, #-0x38]
    // 0xe92988: ldur            x8, [fp, #-0x20]
    // 0xe9298c: ldur            x9, [fp, #-0x18]
    // 0xe92990: b               #0xe92184
    // 0xe92994: ldur            x2, [fp, #-0x30]
    // 0xe92998: ldur            x1, [fp, #-8]
    // 0xe9299c: ldur            x0, [fp, #-0x40]
    // 0xe929a0: LoadField: r3 = r2->field_f
    //     0xe929a0: ldur            w3, [x2, #0xf]
    // 0xe929a4: DecompressPointer r3
    //     0xe929a4: add             x3, x3, HEAP, lsl #32
    // 0xe929a8: mov             x16, x1
    // 0xe929ac: mov             x1, x3
    // 0xe929b0: mov             x3, x16
    // 0xe929b4: ldur            d0, [fp, #-0x88]
    // 0xe929b8: ldr             x2, [fp, #0x18]
    // 0xe929bc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xe929bc: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xe929c0: r0 = _addText()
    //     0xe929c0: bl              #0xe92ad8  ; [package:pdf/src/widgets/text.dart] RichText::_addText
    // 0xe929c4: mov             x4, x0
    // 0xe929c8: ldur            x3, [fp, #-0x40]
    // 0xe929cc: stur            x4, [fp, #-8]
    // 0xe929d0: LoadField: r2 = r3->field_7
    //     0xe929d0: ldur            w2, [x3, #7]
    // 0xe929d4: DecompressPointer r2
    //     0xe929d4: add             x2, x2, HEAP, lsl #32
    // 0xe929d8: mov             x0, x4
    // 0xe929dc: r1 = Null
    //     0xe929dc: mov             x1, NULL
    // 0xe929e0: cmp             w2, NULL
    // 0xe929e4: b.eq            #0xe92a04
    // 0xe929e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe929e8: ldur            w4, [x2, #0x17]
    // 0xe929ec: DecompressPointer r4
    //     0xe929ec: add             x4, x4, HEAP, lsl #32
    // 0xe929f0: r8 = X0
    //     0xe929f0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe929f4: LoadField: r9 = r4->field_7
    //     0xe929f4: ldur            x9, [x4, #7]
    // 0xe929f8: r3 = Null
    //     0xe929f8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e570] Null
    //     0xe929fc: ldr             x3, [x3, #0x570]
    // 0xe92a00: blr             x9
    // 0xe92a04: ldur            x0, [fp, #-0x40]
    // 0xe92a08: LoadField: r1 = r0->field_b
    //     0xe92a08: ldur            w1, [x0, #0xb]
    // 0xe92a0c: LoadField: r2 = r0->field_f
    //     0xe92a0c: ldur            w2, [x0, #0xf]
    // 0xe92a10: DecompressPointer r2
    //     0xe92a10: add             x2, x2, HEAP, lsl #32
    // 0xe92a14: LoadField: r3 = r2->field_b
    //     0xe92a14: ldur            w3, [x2, #0xb]
    // 0xe92a18: r2 = LoadInt32Instr(r1)
    //     0xe92a18: sbfx            x2, x1, #1, #0x1f
    // 0xe92a1c: stur            x2, [fp, #-0x28]
    // 0xe92a20: r1 = LoadInt32Instr(r3)
    //     0xe92a20: sbfx            x1, x3, #1, #0x1f
    // 0xe92a24: cmp             x2, x1
    // 0xe92a28: b.ne            #0xe92a34
    // 0xe92a2c: mov             x1, x0
    // 0xe92a30: r0 = _growToNextCapacity()
    //     0xe92a30: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe92a34: ldur            x2, [fp, #-0x40]
    // 0xe92a38: ldur            x3, [fp, #-0x28]
    // 0xe92a3c: add             x4, x3, #1
    // 0xe92a40: lsl             x5, x4, #1
    // 0xe92a44: StoreField: r2->field_b = r5
    //     0xe92a44: stur            w5, [x2, #0xb]
    // 0xe92a48: LoadField: r1 = r2->field_f
    //     0xe92a48: ldur            w1, [x2, #0xf]
    // 0xe92a4c: DecompressPointer r1
    //     0xe92a4c: add             x1, x1, HEAP, lsl #32
    // 0xe92a50: ldur            x0, [fp, #-8]
    // 0xe92a54: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe92a54: add             x25, x1, x3, lsl #2
    //     0xe92a58: add             x25, x25, #0xf
    //     0xe92a5c: str             w0, [x25]
    //     0xe92a60: tbz             w0, #0, #0xe92a7c
    //     0xe92a64: ldurb           w16, [x1, #-1]
    //     0xe92a68: ldurb           w17, [x0, #-1]
    //     0xe92a6c: and             x16, x17, x16, lsr #2
    //     0xe92a70: tst             x16, HEAP, lsr #32
    //     0xe92a74: b.eq            #0xe92a7c
    //     0xe92a78: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe92a7c: r0 = true
    //     0xe92a7c: add             x0, NULL, #0x20  ; true
    // 0xe92a80: LeaveFrame
    //     0xe92a80: mov             SP, fp
    //     0xe92a84: ldp             fp, lr, [SP], #0x10
    // 0xe92a88: ret
    //     0xe92a88: ret             
    // 0xe92a8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe92a8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe92a90: b               #0xe91ef4
    // 0xe92a94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe92a94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe92a98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe92a98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe92a9c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe92a9c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe92aa0: b               #0xe92198
    // 0xe92aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe92aa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe92aa8: b               #0xe92400
    // 0xe92aac: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe92aac: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe92ab0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe92ab0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe92ab4: stp             q1, q2, [SP, #-0x20]!
    // 0xe92ab8: SaveReg d0
    //     0xe92ab8: str             q0, [SP, #-0x10]!
    // 0xe92abc: stp             x0, x1, [SP, #-0x10]!
    // 0xe92ac0: r0 = AllocateDouble()
    //     0xe92ac0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe92ac4: mov             x2, x0
    // 0xe92ac8: ldp             x0, x1, [SP], #0x10
    // 0xe92acc: RestoreReg d0
    //     0xe92acc: ldr             q0, [SP], #0x10
    // 0xe92ad0: ldp             q1, q2, [SP], #0x20
    // 0xe92ad4: b               #0xe92828
  }
  _ _addText(/* No info */) {
    // ** addr: 0xe92ad8, size: 0xa0
    // 0xe92ad8: EnterFrame
    //     0xe92ad8: stp             fp, lr, [SP, #-0x10]!
    //     0xe92adc: mov             fp, SP
    // 0xe92ae0: AllocStack(0x18)
    //     0xe92ae0: sub             SP, SP, #0x18
    // 0xe92ae4: SetupParameters(RichText this, dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r1 */, dynamic _ /* d0 => d0, fp-0x18 */, {dynamic end = Null /* r3 */})
    //     0xe92ae4: mov             x0, x2
    //     0xe92ae8: stur            x2, [fp, #-8]
    //     0xe92aec: mov             x2, x1
    //     0xe92af0: mov             x1, x3
    //     0xe92af4: stur            d0, [fp, #-0x18]
    //     0xe92af8: ldur            w2, [x4, #0x13]
    //     0xe92afc: ldur            w3, [x4, #0x1f]
    //     0xe92b00: add             x3, x3, HEAP, lsl #32
    //     0xe92b04: ldr             x16, [PP, #0x540]  ; [pp+0x540] "end"
    //     0xe92b08: cmp             w3, w16
    //     0xe92b0c: b.ne            #0xe92b2c
    //     0xe92b10: ldur            w3, [x4, #0x23]
    //     0xe92b14: add             x3, x3, HEAP, lsl #32
    //     0xe92b18: sub             w4, w2, w3
    //     0xe92b1c: add             x2, fp, w4, sxtw #2
    //     0xe92b20: ldr             x2, [x2, #8]
    //     0xe92b24: mov             x3, x2
    //     0xe92b28: b               #0xe92b30
    //     0xe92b2c: mov             x3, NULL
    // 0xe92b30: CheckStackOverflow
    //     0xe92b30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe92b34: cmp             SP, x16
    //     0xe92b38: b.ls            #0xe92b70
    // 0xe92b3c: r2 = 0
    //     0xe92b3c: movz            x2, #0
    // 0xe92b40: r0 = createFromCharCodes()
    //     0xe92b40: bl              #0x601670  ; [dart:core] _StringBase::createFromCharCodes
    // 0xe92b44: stur            x0, [fp, #-0x10]
    // 0xe92b48: r0 = TextSpan()
    //     0xe92b48: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xe92b4c: ldur            x1, [fp, #-0x10]
    // 0xe92b50: ArrayStore: r0[0] = r1  ; List_4
    //     0xe92b50: stur            w1, [x0, #0x17]
    // 0xe92b54: ldur            x1, [fp, #-8]
    // 0xe92b58: StoreField: r0->field_7 = r1
    //     0xe92b58: stur            w1, [x0, #7]
    // 0xe92b5c: ldur            d0, [fp, #-0x18]
    // 0xe92b60: StoreField: r0->field_b = d0
    //     0xe92b60: stur            d0, [x0, #0xb]
    // 0xe92b64: LeaveFrame
    //     0xe92b64: mov             SP, fp
    //     0xe92b68: ldp             fp, lr, [SP], #0x10
    // 0xe92b6c: ret
    //     0xe92b6c: ret             
    // 0xe92b70: r0 = StackOverflowSharedWithFPURegs()
    //     0xe92b70: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe92b74: b               #0xe92b3c
  }
  _ _addEmoji(/* No info */) {
    // ** addr: 0xe92b84, size: 0x11c
    // 0xe92b84: EnterFrame
    //     0xe92b84: stp             fp, lr, [SP, #-0x10]!
    //     0xe92b88: mov             fp, SP
    // 0xe92b8c: AllocStack(0x30)
    //     0xe92b8c: sub             SP, SP, #0x30
    // 0xe92b90: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x30 */)
    //     0xe92b90: mov             x0, x2
    //     0xe92b94: stur            x2, [fp, #-8]
    //     0xe92b98: stur            x3, [fp, #-0x10]
    //     0xe92b9c: stur            d0, [fp, #-0x30]
    // 0xe92ba0: CheckStackOverflow
    //     0xe92ba0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe92ba4: cmp             SP, x16
    //     0xe92ba8: b.ls            #0xe92c94
    // 0xe92bac: mov             x1, x0
    // 0xe92bb0: r0 = metrics()
    //     0xe92bb0: bl              #0xe92f7c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfBitmapInfo::metrics
    // 0xe92bb4: mov             x1, x0
    // 0xe92bb8: ldur            x0, [fp, #-0x10]
    // 0xe92bbc: LoadField: r2 = r0->field_23
    //     0xe92bbc: ldur            w2, [x0, #0x23]
    // 0xe92bc0: DecompressPointer r2
    //     0xe92bc0: add             x2, x2, HEAP, lsl #32
    // 0xe92bc4: stur            x2, [fp, #-0x18]
    // 0xe92bc8: cmp             w2, NULL
    // 0xe92bcc: b.eq            #0xe92c9c
    // 0xe92bd0: LoadField: d0 = r2->field_7
    //     0xe92bd0: ldur            d0, [x2, #7]
    // 0xe92bd4: r0 = *()
    //     0xe92bd4: bl              #0xe6feb0  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::*
    // 0xe92bd8: mov             x3, x0
    // 0xe92bdc: ldur            x0, [fp, #-8]
    // 0xe92be0: stur            x3, [fp, #-0x20]
    // 0xe92be4: LoadField: r2 = r0->field_7
    //     0xe92be4: ldur            w2, [x0, #7]
    // 0xe92be8: DecompressPointer r2
    //     0xe92be8: add             x2, x2, HEAP, lsl #32
    // 0xe92bec: r1 = Null
    //     0xe92bec: mov             x1, NULL
    // 0xe92bf0: r0 = MemoryImage()
    //     0xe92bf0: bl              #0xe92cac  ; [package:pdf/src/widgets/image_provider.dart] MemoryImage::MemoryImage
    // 0xe92bf4: stur            x0, [fp, #-8]
    // 0xe92bf8: r0 = Image()
    //     0xe92bf8: bl              #0xe92ca0  ; AllocateImageStub -> Image (size=0x24)
    // 0xe92bfc: mov             x1, x0
    // 0xe92c00: ldur            x0, [fp, #-8]
    // 0xe92c04: stur            x1, [fp, #-0x28]
    // 0xe92c08: StoreField: r1->field_b = r0
    //     0xe92c08: stur            w0, [x1, #0xb]
    // 0xe92c0c: r0 = Instance_BoxFit
    //     0xe92c0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e340] Obj!BoxFit@e2ea01
    //     0xe92c10: ldr             x0, [x0, #0x340]
    // 0xe92c14: StoreField: r1->field_f = r0
    //     0xe92c14: stur            w0, [x1, #0xf]
    // 0xe92c18: r0 = Instance_Alignment
    //     0xe92c18: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e290] Obj!Alignment@e0c481
    //     0xe92c1c: ldr             x0, [x0, #0x290]
    // 0xe92c20: StoreField: r1->field_13 = r0
    //     0xe92c20: stur            w0, [x1, #0x13]
    // 0xe92c24: r0 = SizedBox()
    //     0xe92c24: bl              #0xb13e18  ; AllocateSizedBoxStub -> SizedBox (size=0x1c)
    // 0xe92c28: mov             x1, x0
    // 0xe92c2c: ldur            x0, [fp, #-0x18]
    // 0xe92c30: stur            x1, [fp, #-8]
    // 0xe92c34: StoreField: r1->field_13 = r0
    //     0xe92c34: stur            w0, [x1, #0x13]
    // 0xe92c38: ldur            x0, [fp, #-0x28]
    // 0xe92c3c: ArrayStore: r1[0] = r0  ; List_4
    //     0xe92c3c: stur            w0, [x1, #0x17]
    // 0xe92c40: ldur            x0, [fp, #-0x20]
    // 0xe92c44: LoadField: d0 = r0->field_27
    //     0xe92c44: ldur            d0, [x0, #0x27]
    // 0xe92c48: ldur            d1, [fp, #-0x30]
    // 0xe92c4c: fadd            d2, d1, d0
    // 0xe92c50: LoadField: d0 = r0->field_2f
    //     0xe92c50: ldur            d0, [x0, #0x2f]
    // 0xe92c54: fadd            d1, d2, d0
    // 0xe92c58: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe92c58: ldur            d0, [x0, #0x17]
    // 0xe92c5c: LoadField: d2 = r0->field_f
    //     0xe92c5c: ldur            d2, [x0, #0xf]
    // 0xe92c60: fsub            d3, d0, d2
    // 0xe92c64: fsub            d0, d1, d3
    // 0xe92c68: stur            d0, [fp, #-0x30]
    // 0xe92c6c: r0 = WidgetSpan()
    //     0xe92c6c: bl              #0xe93028  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x1c)
    // 0xe92c70: ldur            x1, [fp, #-8]
    // 0xe92c74: ArrayStore: r0[0] = r1  ; List_4
    //     0xe92c74: stur            w1, [x0, #0x17]
    // 0xe92c78: ldur            x1, [fp, #-0x10]
    // 0xe92c7c: StoreField: r0->field_7 = r1
    //     0xe92c7c: stur            w1, [x0, #7]
    // 0xe92c80: ldur            d0, [fp, #-0x30]
    // 0xe92c84: StoreField: r0->field_b = d0
    //     0xe92c84: stur            d0, [x0, #0xb]
    // 0xe92c88: LeaveFrame
    //     0xe92c88: mov             SP, fp
    //     0xe92c8c: ldp             fp, lr, [SP], #0x10
    // 0xe92c90: ret
    //     0xe92c90: ret             
    // 0xe92c94: r0 = StackOverflowSharedWithFPURegs()
    //     0xe92c94: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe92c98: b               #0xe92bac
    // 0xe92c9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe92c9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _buildLines(dynamic) {
    // ** addr: 0xe93034, size: 0x2244
    // 0xe93034: EnterFrame
    //     0xe93034: stp             fp, lr, [SP, #-0x10]!
    //     0xe93038: mov             fp, SP
    // 0xe9303c: AllocStack(0x180)
    //     0xe9303c: sub             SP, SP, #0x180
    // 0xe93040: SetupParameters()
    //     0xe93040: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3e100] IMM: double(1e-05) from 0x3ee4f8b588e368f1
    //     0xe93044: ldr             d0, [x17, #0x100]
    //     0xe93048: ldr             x0, [fp, #0x10]
    //     0xe9304c: ldur            w2, [x0, #0x17]
    //     0xe93050: add             x2, x2, HEAP, lsl #32
    //     0xe93054: stur            x2, [fp, #-0x58]
    // 0xe93040: d0 = 0.000010
    // 0xe93058: CheckStackOverflow
    //     0xe93058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9305c: cmp             SP, x16
    //     0xe93060: b.ls            #0xe94e00
    // 0xe93064: LoadField: r0 = r2->field_f
    //     0xe93064: ldur            w0, [x2, #0xf]
    // 0xe93068: DecompressPointer r0
    //     0xe93068: add             x0, x0, HEAP, lsl #32
    // 0xe9306c: LoadField: r3 = r0->field_43
    //     0xe9306c: ldur            w3, [x0, #0x43]
    // 0xe93070: DecompressPointer r3
    //     0xe93070: add             x3, x3, HEAP, lsl #32
    // 0xe93074: stur            x3, [fp, #-0x50]
    // 0xe93078: cmp             w3, NULL
    // 0xe9307c: b.eq            #0xe94e08
    // 0xe93080: LoadField: r0 = r3->field_b
    //     0xe93080: ldur            w0, [x3, #0xb]
    // 0xe93084: r4 = LoadInt32Instr(r0)
    //     0xe93084: sbfx            x4, x0, #1, #0x1f
    // 0xe93088: stur            x4, [fp, #-0x48]
    // 0xe9308c: LoadField: r0 = r2->field_1f
    //     0xe9308c: ldur            w0, [x2, #0x1f]
    // 0xe93090: DecompressPointer r0
    //     0xe93090: add             x0, x0, HEAP, lsl #32
    // 0xe93094: LoadField: r1 = r2->field_23
    //     0xe93094: ldur            w1, [x2, #0x23]
    // 0xe93098: DecompressPointer r1
    //     0xe93098: add             x1, x1, HEAP, lsl #32
    // 0xe9309c: LoadField: d1 = r0->field_7
    //     0xe9309c: ldur            d1, [x0, #7]
    // 0xe930a0: r17 = -280
    //     0xe930a0: movn            x17, #0x117
    // 0xe930a4: str             d1, [fp, x17]
    // 0xe930a8: LoadField: d2 = r1->field_7
    //     0xe930a8: ldur            d2, [x1, #7]
    // 0xe930ac: r17 = -272
    //     0xe930ac: movn            x17, #0x10f
    // 0xe930b0: str             d2, [fp, x17]
    // 0xe930b4: LoadField: r5 = r2->field_37
    //     0xe930b4: ldur            w5, [x2, #0x37]
    // 0xe930b8: DecompressPointer r5
    //     0xe930b8: add             x5, x5, HEAP, lsl #32
    // 0xe930bc: stur            x5, [fp, #-0x40]
    // 0xe930c0: LoadField: r6 = r5->field_7
    //     0xe930c0: ldur            w6, [x5, #7]
    // 0xe930c4: DecompressPointer r6
    //     0xe930c4: add             x6, x6, HEAP, lsl #32
    // 0xe930c8: stur            x6, [fp, #-0x38]
    // 0xe930cc: LoadField: r7 = r2->field_1b
    //     0xe930cc: ldur            w7, [x2, #0x1b]
    // 0xe930d0: DecompressPointer r7
    //     0xe930d0: add             x7, x7, HEAP, lsl #32
    // 0xe930d4: stur            x7, [fp, #-0x30]
    // 0xe930d8: ArrayLoad: r8 = r2[0]  ; List_4
    //     0xe930d8: ldur            w8, [x2, #0x17]
    // 0xe930dc: DecompressPointer r8
    //     0xe930dc: add             x8, x8, HEAP, lsl #32
    // 0xe930e0: stur            x8, [fp, #-0x28]
    // 0xe930e4: LoadField: d3 = r0->field_7
    //     0xe930e4: ldur            d3, [x0, #7]
    // 0xe930e8: r17 = -264
    //     0xe930e8: movn            x17, #0x107
    // 0xe930ec: str             d3, [fp, x17]
    // 0xe930f0: fadd            d4, d3, d0
    // 0xe930f4: stur            d4, [fp, #-0x100]
    // 0xe930f8: LoadField: d0 = r1->field_7
    //     0xe930f8: ldur            d0, [x1, #7]
    // 0xe930fc: stur            d0, [fp, #-0xf8]
    // 0xe93100: LoadField: d5 = r1->field_7
    //     0xe93100: ldur            d5, [x1, #7]
    // 0xe93104: stur            d5, [fp, #-0xf0]
    // 0xe93108: r0 = 0
    //     0xe93108: movz            x0, #0
    // 0xe9310c: CheckStackOverflow
    //     0xe9310c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe93110: cmp             SP, x16
    //     0xe93114: b.ls            #0xe94e0c
    // 0xe93118: LoadField: r1 = r3->field_b
    //     0xe93118: ldur            w1, [x3, #0xb]
    // 0xe9311c: r9 = LoadInt32Instr(r1)
    //     0xe9311c: sbfx            x9, x1, #1, #0x1f
    // 0xe93120: cmp             x4, x9
    // 0xe93124: b.ne            #0xe94de0
    // 0xe93128: cmp             x0, x9
    // 0xe9312c: b.ge            #0xe94dd0
    // 0xe93130: LoadField: r1 = r3->field_f
    //     0xe93130: ldur            w1, [x3, #0xf]
    // 0xe93134: DecompressPointer r1
    //     0xe93134: add             x1, x1, HEAP, lsl #32
    // 0xe93138: ArrayLoad: r9 = r1[r0]  ; Unknown_4
    //     0xe93138: add             x16, x1, x0, lsl #2
    //     0xe9313c: ldur            w9, [x16, #0xf]
    // 0xe93140: DecompressPointer r9
    //     0xe93140: add             x9, x9, HEAP, lsl #32
    // 0xe93144: stur            x9, [fp, #-0x20]
    // 0xe93148: add             x10, x0, #1
    // 0xe9314c: stur            x10, [fp, #-0x18]
    // 0xe93150: LoadField: r0 = r9->field_7
    //     0xe93150: ldur            w0, [x9, #7]
    // 0xe93154: DecompressPointer r0
    //     0xe93154: add             x0, x0, HEAP, lsl #32
    // 0xe93158: stur            x0, [fp, #-0x10]
    // 0xe9315c: r1 = LoadClassIdInstr(r9)
    //     0xe9315c: ldur            x1, [x9, #-1]
    //     0xe93160: ubfx            x1, x1, #0xc, #0x14
    // 0xe93164: cmp             x1, #0x301
    // 0xe93168: b.ne            #0xe945d4
    // 0xe9316c: ArrayLoad: r11 = r9[0]  ; List_4
    //     0xe9316c: ldur            w11, [x9, #0x17]
    // 0xe93170: DecompressPointer r11
    //     0xe93170: add             x11, x11, HEAP, lsl #32
    // 0xe93174: stur            x11, [fp, #-8]
    // 0xe93178: cmp             w11, NULL
    // 0xe9317c: b.ne            #0xe9318c
    // 0xe93180: mov             x1, x2
    // 0xe93184: d1 = 0.000000
    //     0xe93184: eor             v1.16b, v1.16b, v1.16b
    // 0xe93188: b               #0xe94d88
    // 0xe9318c: cmp             w0, NULL
    // 0xe93190: b.eq            #0xe94e14
    // 0xe93194: mov             x1, x0
    // 0xe93198: r0 = font()
    //     0xe93198: bl              #0xb0fcfc  ; [package:pdf/src/widgets/text_style.dart] TextStyle::font
    // 0xe9319c: cmp             w0, NULL
    // 0xe931a0: b.eq            #0xe94e18
    // 0xe931a4: ldur            x3, [fp, #-0x58]
    // 0xe931a8: LoadField: r2 = r3->field_13
    //     0xe931a8: ldur            w2, [x3, #0x13]
    // 0xe931ac: DecompressPointer r2
    //     0xe931ac: add             x2, x2, HEAP, lsl #32
    // 0xe931b0: mov             x1, x0
    // 0xe931b4: r0 = getFont()
    //     0xe931b4: bl              #0xe65b4c  ; [package:pdf/src/widgets/font.dart] Font::getFont
    // 0xe931b8: stur            x0, [fp, #-0x68]
    // 0xe931bc: r3 = LoadClassIdInstr(r0)
    //     0xe931bc: ldur            x3, [x0, #-1]
    //     0xe931c0: ubfx            x3, x3, #0xc, #0x14
    // 0xe931c4: stur            x3, [fp, #-0x60]
    // 0xe931c8: cmp             x3, #0x37f
    // 0xe931cc: b.ne            #0xe9321c
    // 0xe931d0: r1 = Instance_Latin1Codec
    //     0xe931d0: ldr             x1, [PP, #0xdf8]  ; [pp+0xdf8] Obj!Latin1Codec@e2cd01
    // 0xe931d4: r2 = " "
    //     0xe931d4: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe931d8: r0 = encode()
    //     0xe931d8: bl              #0xceba10  ; [dart:convert] Latin1Codec::encode
    // 0xe931dc: ldur            x2, [fp, #-0x68]
    // 0xe931e0: r1 = Function 'glyphMetrics':.
    //     0xe931e0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e108] AnonymousClosure: (0xe71018), in [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::glyphMetrics (0xe71054)
    //     0xe931e4: ldr             x1, [x1, #0x108]
    // 0xe931e8: stur            x0, [fp, #-0x70]
    // 0xe931ec: r0 = AllocateClosure()
    //     0xe931ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xe931f0: ldur            x2, [fp, #-0x70]
    // 0xe931f4: mov             x3, x0
    // 0xe931f8: r1 = <PdfFontMetrics, int, PdfFontMetrics>
    //     0xe931f8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e110] TypeArguments: <PdfFontMetrics, int, PdfFontMetrics>
    //     0xe931fc: ldr             x1, [x1, #0x110]
    // 0xe93200: r0 = MappedIterable()
    //     0xe93200: bl              #0x7ac0ac  ; [dart:_internal] MappedIterable::MappedIterable
    // 0xe93204: mov             x2, x0
    // 0xe93208: r1 = Null
    //     0xe93208: mov             x1, NULL
    // 0xe9320c: d0 = 0.000000
    //     0xe9320c: eor             v0.16b, v0.16b, v0.16b
    // 0xe93210: r0 = PdfFontMetrics.append()
    //     0xe93210: bl              #0xe701bc  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::PdfFontMetrics.append
    // 0xe93214: mov             x1, x0
    // 0xe93218: b               #0xe93244
    // 0xe9321c: mov             x3, x0
    // 0xe93220: r0 = LoadClassIdInstr(r3)
    //     0xe93220: ldur            x0, [x3, #-1]
    //     0xe93224: ubfx            x0, x0, #0xc, #0x14
    // 0xe93228: mov             x1, x3
    // 0xe9322c: r2 = " "
    //     0xe9322c: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe93230: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe93230: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe93234: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe93234: sub             lr, x0, #1, lsl #12
    //     0xe93238: ldr             lr, [x21, lr, lsl #3]
    //     0xe9323c: blr             lr
    // 0xe93240: mov             x1, x0
    // 0xe93244: ldur            x5, [fp, #-0x10]
    // 0xe93248: ldur            x0, [fp, #-0x30]
    // 0xe9324c: LoadField: r2 = r5->field_23
    //     0xe9324c: ldur            w2, [x5, #0x23]
    // 0xe93250: DecompressPointer r2
    //     0xe93250: add             x2, x2, HEAP, lsl #32
    // 0xe93254: cmp             w2, NULL
    // 0xe93258: b.eq            #0xe94e1c
    // 0xe9325c: LoadField: d1 = r2->field_7
    //     0xe9325c: ldur            d1, [x2, #7]
    // 0xe93260: mov             v0.16b, v1.16b
    // 0xe93264: r17 = -288
    //     0xe93264: movn            x17, #0x11f
    // 0xe93268: str             d1, [fp, x17]
    // 0xe9326c: r0 = *()
    //     0xe9326c: bl              #0xe6feb0  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::*
    // 0xe93270: mov             x2, x0
    // 0xe93274: ldur            x0, [fp, #-0x30]
    // 0xe93278: stur            x2, [fp, #-0x70]
    // 0xe9327c: r16 = Instance_TextDirection
    //     0xe9327c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e118] Obj!TextDirection@e2e5c1
    //     0xe93280: ldr             x16, [x16, #0x118]
    // 0xe93284: cmp             w0, w16
    // 0xe93288: b.ne            #0xe9329c
    // 0xe9328c: ldur            x1, [fp, #-8]
    // 0xe93290: r0 = logicalToVisual()
    //     0xe93290: bl              #0xe95aec  ; [package:pdf/src/pdf/font/bidi_utils.dart] ::logicalToVisual
    // 0xe93294: mov             x1, x0
    // 0xe93298: b               #0xe932a0
    // 0xe9329c: ldur            x1, [fp, #-8]
    // 0xe932a0: ldur            x5, [fp, #-0x10]
    // 0xe932a4: ldur            x3, [fp, #-0x70]
    // 0xe932a8: ldur            x4, [fp, #-0x20]
    // 0xe932ac: r0 = LoadClassIdInstr(r1)
    //     0xe932ac: ldur            x0, [x1, #-1]
    //     0xe932b0: ubfx            x0, x0, #0xc, #0x14
    // 0xe932b4: r2 = "\n"
    //     0xe932b4: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xe932b8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe932b8: sub             lr, x0, #1, lsl #12
    //     0xe932bc: ldr             lr, [x21, lr, lsl #3]
    //     0xe932c0: blr             lr
    // 0xe932c4: ldur            x5, [fp, #-0x10]
    // 0xe932c8: stur            x0, [fp, #-0x90]
    // 0xe932cc: LoadField: r1 = r5->field_2f
    //     0xe932cc: ldur            w1, [x5, #0x2f]
    // 0xe932d0: DecompressPointer r1
    //     0xe932d0: add             x1, x1, HEAP, lsl #32
    // 0xe932d4: ldur            x2, [fp, #-0x20]
    // 0xe932d8: stur            x1, [fp, #-0x88]
    // 0xe932dc: LoadField: d0 = r2->field_b
    //     0xe932dc: ldur            d0, [x2, #0xb]
    // 0xe932e0: ldur            x2, [fp, #-0x70]
    // 0xe932e4: r17 = -304
    //     0xe932e4: movn            x17, #0x12f
    // 0xe932e8: str             d0, [fp, x17]
    // 0xe932ec: LoadField: d1 = r2->field_37
    //     0xe932ec: ldur            d1, [x2, #0x37]
    // 0xe932f0: r17 = -296
    //     0xe932f0: movn            x17, #0x127
    // 0xe932f4: str             d1, [fp, x17]
    // 0xe932f8: LoadField: r2 = r5->field_37
    //     0xe932f8: ldur            w2, [x5, #0x37]
    // 0xe932fc: DecompressPointer r2
    //     0xe932fc: add             x2, x2, HEAP, lsl #32
    // 0xe93300: stur            x2, [fp, #-0x80]
    // 0xe93304: LoadField: r3 = r5->field_33
    //     0xe93304: ldur            w3, [x5, #0x33]
    // 0xe93308: DecompressPointer r3
    //     0xe93308: add             x3, x3, HEAP, lsl #32
    // 0xe9330c: stur            x3, [fp, #-0x70]
    // 0xe93310: ldur            x7, [fp, #-0x58]
    // 0xe93314: r17 = -288
    //     0xe93314: movn            x17, #0x11f
    // 0xe93318: ldr             d2, [fp, x17]
    // 0xe9331c: ldur            d6, [fp, #-0xf0]
    // 0xe93320: r10 = 0
    //     0xe93320: movz            x10, #0
    // 0xe93324: ldur            x8, [fp, #-0x40]
    // 0xe93328: ldur            x4, [fp, #-0x68]
    // 0xe9332c: ldur            x9, [fp, #-0x28]
    // 0xe93330: ldur            d4, [fp, #-0x100]
    // 0xe93334: ldur            x6, [fp, #-0x60]
    // 0xe93338: r17 = -264
    //     0xe93338: movn            x17, #0x107
    // 0xe9333c: ldr             d3, [fp, x17]
    // 0xe93340: ldur            d5, [fp, #-0xf8]
    // 0xe93344: stur            x10, [fp, #-0x78]
    // 0xe93348: CheckStackOverflow
    //     0xe93348: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9334c: cmp             SP, x16
    //     0xe93350: b.ls            #0xe94e20
    // 0xe93354: LoadField: r11 = r0->field_b
    //     0xe93354: ldur            w11, [x0, #0xb]
    // 0xe93358: r12 = LoadInt32Instr(r11)
    //     0xe93358: sbfx            x12, x11, #1, #0x1f
    // 0xe9335c: cmp             x10, x12
    // 0xe93360: b.ge            #0xe94544
    // 0xe93364: LoadField: r11 = r0->field_f
    //     0xe93364: ldur            w11, [x0, #0xf]
    // 0xe93368: DecompressPointer r11
    //     0xe93368: add             x11, x11, HEAP, lsl #32
    // 0xe9336c: ArrayLoad: r12 = r11[r10]  ; Unknown_4
    //     0xe9336c: add             x16, x11, x10, lsl #2
    //     0xe93370: ldur            w12, [x16, #0xf]
    // 0xe93374: DecompressPointer r12
    //     0xe93374: add             x12, x12, HEAP, lsl #32
    // 0xe93378: stur            x12, [fp, #-8]
    // 0xe9337c: r16 = "\\s"
    //     0xe9337c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26020] "\\s"
    //     0xe93380: ldr             x16, [x16, #0x20]
    // 0xe93384: stp             x16, NULL, [SP, #0x20]
    // 0xe93388: r16 = false
    //     0xe93388: add             x16, NULL, #0x30  ; false
    // 0xe9338c: r30 = true
    //     0xe9338c: add             lr, NULL, #0x20  ; true
    // 0xe93390: stp             lr, x16, [SP, #0x10]
    // 0xe93394: r16 = false
    //     0xe93394: add             x16, NULL, #0x30  ; false
    // 0xe93398: r30 = false
    //     0xe93398: add             lr, NULL, #0x30  ; false
    // 0xe9339c: stp             lr, x16, [SP]
    // 0xe933a0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xe933a0: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xe933a4: r0 = _RegExp()
    //     0xe933a4: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xe933a8: ldur            x1, [fp, #-8]
    // 0xe933ac: r2 = LoadClassIdInstr(r1)
    //     0xe933ac: ldur            x2, [x1, #-1]
    //     0xe933b0: ubfx            x2, x2, #0xc, #0x14
    // 0xe933b4: mov             x16, x0
    // 0xe933b8: mov             x0, x2
    // 0xe933bc: mov             x2, x16
    // 0xe933c0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe933c0: sub             lr, x0, #1, lsl #12
    //     0xe933c4: ldr             lr, [x21, lr, lsl #3]
    //     0xe933c8: blr             lr
    // 0xe933cc: mov             x3, x0
    // 0xe933d0: stur            x3, [fp, #-0xa8]
    // 0xe933d4: ldur            x10, [fp, #-0x58]
    // 0xe933d8: r17 = -296
    //     0xe933d8: movn            x17, #0x127
    // 0xe933dc: ldr             d1, [fp, x17]
    // 0xe933e0: ldur            x6, [fp, #-0x80]
    // 0xe933e4: r13 = 0
    //     0xe933e4: movz            x13, #0
    // 0xe933e8: ldur            x5, [fp, #-0x10]
    // 0xe933ec: ldur            x11, [fp, #-0x40]
    // 0xe933f0: ldur            x8, [fp, #-0x68]
    // 0xe933f4: ldur            x4, [fp, #-0x88]
    // 0xe933f8: ldur            x12, [fp, #-0x28]
    // 0xe933fc: r17 = -304
    //     0xe933fc: movn            x17, #0x12f
    // 0xe93400: ldr             d0, [fp, x17]
    // 0xe93404: ldur            d4, [fp, #-0x100]
    // 0xe93408: ldur            x7, [fp, #-0x70]
    // 0xe9340c: ldur            x9, [fp, #-0x60]
    // 0xe93410: r17 = -288
    //     0xe93410: movn            x17, #0x11f
    // 0xe93414: ldr             d2, [fp, x17]
    // 0xe93418: r17 = -264
    //     0xe93418: movn            x17, #0x107
    // 0xe9341c: ldr             d3, [fp, x17]
    // 0xe93420: ldur            d5, [fp, #-0xf8]
    // 0xe93424: stur            x13, [fp, #-0xa0]
    // 0xe93428: CheckStackOverflow
    //     0xe93428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9342c: cmp             SP, x16
    //     0xe93430: b.ls            #0xe94e28
    // 0xe93434: LoadField: r0 = r3->field_b
    //     0xe93434: ldur            w0, [x3, #0xb]
    // 0xe93438: r1 = LoadInt32Instr(r0)
    //     0xe93438: sbfx            x1, x0, #1, #0x1f
    // 0xe9343c: cmp             x13, x1
    // 0xe93440: b.ge            #0xe9405c
    // 0xe93444: LoadField: r0 = r3->field_f
    //     0xe93444: ldur            w0, [x3, #0xf]
    // 0xe93448: DecompressPointer r0
    //     0xe93448: add             x0, x0, HEAP, lsl #32
    // 0xe9344c: ArrayLoad: r14 = r0[r13]  ; Unknown_4
    //     0xe9344c: add             x16, x0, x13, lsl #2
    //     0xe93450: ldur            w14, [x16, #0xf]
    // 0xe93454: DecompressPointer r14
    //     0xe93454: add             x14, x14, HEAP, lsl #32
    // 0xe93458: stur            x14, [fp, #-0x98]
    // 0xe9345c: LoadField: r0 = r14->field_7
    //     0xe9345c: ldur            w0, [x14, #7]
    // 0xe93460: stur            x0, [fp, #-8]
    // 0xe93464: cbnz            w0, #0xe934f0
    // 0xe93468: LoadField: r0 = r10->field_27
    //     0xe93468: ldur            w0, [x10, #0x27]
    // 0xe9346c: DecompressPointer r0
    //     0xe9346c: add             x0, x0, HEAP, lsl #32
    // 0xe93470: cmp             w6, NULL
    // 0xe93474: b.eq            #0xe94e30
    // 0xe93478: LoadField: d6 = r6->field_7
    //     0xe93478: ldur            d6, [x6, #7]
    // 0xe9347c: fmul            d7, d1, d6
    // 0xe93480: cmp             w4, NULL
    // 0xe93484: b.eq            #0xe94e34
    // 0xe93488: LoadField: d6 = r4->field_7
    //     0xe93488: ldur            d6, [x4, #7]
    // 0xe9348c: fadd            d8, d7, d6
    // 0xe93490: LoadField: d6 = r0->field_7
    //     0xe93490: ldur            d6, [x0, #7]
    // 0xe93494: fadd            d7, d6, d8
    // 0xe93498: r0 = inline_Allocate_Double()
    //     0xe93498: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe9349c: add             x0, x0, #0x10
    //     0xe934a0: cmp             x1, x0
    //     0xe934a4: b.ls            #0xe94e38
    //     0xe934a8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe934ac: sub             x0, x0, #0xf
    //     0xe934b0: movz            x1, #0xe15c
    //     0xe934b4: movk            x1, #0x3, lsl #16
    //     0xe934b8: stur            x1, [x0, #-1]
    // 0xe934bc: StoreField: r0->field_7 = d7
    //     0xe934bc: stur            d7, [x0, #7]
    // 0xe934c0: StoreField: r10->field_27 = r0
    //     0xe934c0: stur            w0, [x10, #0x27]
    //     0xe934c4: ldurb           w16, [x10, #-1]
    //     0xe934c8: ldurb           w17, [x0, #-1]
    //     0xe934cc: and             x16, x17, x16, lsr #2
    //     0xe934d0: tst             x16, HEAP, lsr #32
    //     0xe934d4: b.eq            #0xe934dc
    //     0xe934d8: bl              #0xec0b48  ; WriteBarrierWrappersStub
    // 0xe934dc: mov             x0, x13
    // 0xe934e0: mov             x1, x10
    // 0xe934e4: mov             v2.16b, v1.16b
    // 0xe934e8: mov             x2, x6
    // 0xe934ec: b               #0xe94044
    // 0xe934f0: cmp             w4, NULL
    // 0xe934f4: b.eq            #0xe94e90
    // 0xe934f8: LoadField: d6 = r4->field_7
    //     0xe934f8: ldur            d6, [x4, #7]
    // 0xe934fc: r17 = -320
    //     0xe934fc: movn            x17, #0x13f
    // 0xe93500: str             d6, [fp, x17]
    // 0xe93504: fdiv            d7, d6, d2
    // 0xe93508: r17 = -312
    //     0xe93508: movn            x17, #0x137
    // 0xe9350c: str             d7, [fp, x17]
    // 0xe93510: cmp             x9, #0x37f
    // 0xe93514: b.ne            #0xe9358c
    // 0xe93518: cbnz            w0, #0xe93530
    // 0xe9351c: mov             x0, x12
    // 0xe93520: mov             v1.16b, v2.16b
    // 0xe93524: r1 = Instance_PdfFontMetrics
    //     0xe93524: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e120] Obj!PdfFontMetrics@e0c991
    //     0xe93528: ldr             x1, [x1, #0x120]
    // 0xe9352c: b               #0xe935f8
    // 0xe93530: mov             x2, x14
    // 0xe93534: r1 = Instance_Latin1Codec
    //     0xe93534: ldr             x1, [PP, #0xdf8]  ; [pp+0xdf8] Obj!Latin1Codec@e2cd01
    // 0xe93538: r0 = encode()
    //     0xe93538: bl              #0xceba10  ; [dart:convert] Latin1Codec::encode
    // 0xe9353c: ldur            x2, [fp, #-0x68]
    // 0xe93540: r1 = Function 'glyphMetrics':.
    //     0xe93540: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e108] AnonymousClosure: (0xe71018), in [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::glyphMetrics (0xe71054)
    //     0xe93544: ldr             x1, [x1, #0x108]
    // 0xe93548: stur            x0, [fp, #-0xb0]
    // 0xe9354c: r0 = AllocateClosure()
    //     0xe9354c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe93550: ldur            x2, [fp, #-0xb0]
    // 0xe93554: mov             x3, x0
    // 0xe93558: r1 = <PdfFontMetrics, int, PdfFontMetrics>
    //     0xe93558: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e110] TypeArguments: <PdfFontMetrics, int, PdfFontMetrics>
    //     0xe9355c: ldr             x1, [x1, #0x110]
    // 0xe93560: r0 = MappedIterable()
    //     0xe93560: bl              #0x7ac0ac  ; [dart:_internal] MappedIterable::MappedIterable
    // 0xe93564: mov             x2, x0
    // 0xe93568: r17 = -312
    //     0xe93568: movn            x17, #0x137
    // 0xe9356c: ldr             d0, [fp, x17]
    // 0xe93570: r1 = Null
    //     0xe93570: mov             x1, NULL
    // 0xe93574: r0 = PdfFontMetrics.append()
    //     0xe93574: bl              #0xe701bc  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::PdfFontMetrics.append
    // 0xe93578: mov             x1, x0
    // 0xe9357c: ldur            x0, [fp, #-0x28]
    // 0xe93580: r17 = -288
    //     0xe93580: movn            x17, #0x11f
    // 0xe93584: ldr             d1, [fp, x17]
    // 0xe93588: b               #0xe935f8
    // 0xe9358c: mov             x3, x8
    // 0xe93590: mov             v0.16b, v7.16b
    // 0xe93594: r0 = inline_Allocate_Double()
    //     0xe93594: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe93598: add             x0, x0, #0x10
    //     0xe9359c: cmp             x1, x0
    //     0xe935a0: b.ls            #0xe94e94
    //     0xe935a4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe935a8: sub             x0, x0, #0xf
    //     0xe935ac: movz            x1, #0xe15c
    //     0xe935b0: movk            x1, #0x3, lsl #16
    //     0xe935b4: stur            x1, [x0, #-1]
    // 0xe935b8: StoreField: r0->field_7 = d0
    //     0xe935b8: stur            d0, [x0, #7]
    // 0xe935bc: r1 = LoadClassIdInstr(r3)
    //     0xe935bc: ldur            x1, [x3, #-1]
    //     0xe935c0: ubfx            x1, x1, #0xc, #0x14
    // 0xe935c4: str             x0, [SP]
    // 0xe935c8: mov             x0, x1
    // 0xe935cc: mov             x1, x3
    // 0xe935d0: ldur            x2, [fp, #-0x98]
    // 0xe935d4: r4 = const [0, 0x3, 0x1, 0x2, letterSpacing, 0x2, null]
    //     0xe935d4: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e128] List(7) [0, 0x3, 0x1, 0x2, "letterSpacing", 0x2, Null]
    //     0xe935d8: ldr             x4, [x4, #0x128]
    // 0xe935dc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe935dc: sub             lr, x0, #1, lsl #12
    //     0xe935e0: ldr             lr, [x21, lr, lsl #3]
    //     0xe935e4: blr             lr
    // 0xe935e8: mov             x1, x0
    // 0xe935ec: ldur            x0, [fp, #-0x28]
    // 0xe935f0: r17 = -288
    //     0xe935f0: movn            x17, #0x11f
    // 0xe935f4: ldr             d1, [fp, x17]
    // 0xe935f8: LoadField: d0 = r1->field_7
    //     0xe935f8: ldur            d0, [x1, #7]
    // 0xe935fc: fmul            d2, d0, d1
    // 0xe93600: LoadField: d0 = r1->field_f
    //     0xe93600: ldur            d0, [x1, #0xf]
    // 0xe93604: fmul            d3, d0, d1
    // 0xe93608: LoadField: d0 = r1->field_1f
    //     0xe93608: ldur            d0, [x1, #0x1f]
    // 0xe9360c: fmul            d4, d0, d1
    // 0xe93610: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xe93610: ldur            d0, [x1, #0x17]
    // 0xe93614: fmul            d5, d0, d1
    // 0xe93618: LoadField: d0 = r1->field_27
    //     0xe93618: ldur            d0, [x1, #0x27]
    // 0xe9361c: fmul            d6, d0, d1
    // 0xe93620: LoadField: d0 = r1->field_2f
    //     0xe93620: ldur            d0, [x1, #0x2f]
    // 0xe93624: fmul            d7, d0, d1
    // 0xe93628: LoadField: d0 = r1->field_37
    //     0xe93628: ldur            d0, [x1, #0x37]
    // 0xe9362c: fmul            d8, d0, d1
    // 0xe93630: LoadField: d0 = r1->field_3f
    //     0xe93630: ldur            d0, [x1, #0x3f]
    // 0xe93634: fmul            d9, d0, d1
    // 0xe93638: r2 = inline_Allocate_Double()
    //     0xe93638: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xe9363c: add             x2, x2, #0x10
    //     0xe93640: cmp             x3, x2
    //     0xe93644: b.ls            #0xe94eac
    //     0xe93648: str             x2, [THR, #0x50]  ; THR::top
    //     0xe9364c: sub             x2, x2, #0xf
    //     0xe93650: movz            x3, #0xe15c
    //     0xe93654: movk            x3, #0x3, lsl #16
    //     0xe93658: stur            x3, [x2, #-1]
    // 0xe9365c: StoreField: r2->field_7 = d2
    //     0xe9365c: stur            d2, [x2, #7]
    // 0xe93660: r3 = inline_Allocate_Double()
    //     0xe93660: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0xe93664: add             x3, x3, #0x10
    //     0xe93668: cmp             x4, x3
    //     0xe9366c: b.ls            #0xe94ee8
    //     0xe93670: str             x3, [THR, #0x50]  ; THR::top
    //     0xe93674: sub             x3, x3, #0xf
    //     0xe93678: movz            x4, #0xe15c
    //     0xe9367c: movk            x4, #0x3, lsl #16
    //     0xe93680: stur            x4, [x3, #-1]
    // 0xe93684: StoreField: r3->field_7 = d3
    //     0xe93684: stur            d3, [x3, #7]
    // 0xe93688: r4 = inline_Allocate_Double()
    //     0xe93688: ldp             x4, x5, [THR, #0x50]  ; THR::top
    //     0xe9368c: add             x4, x4, #0x10
    //     0xe93690: cmp             x5, x4
    //     0xe93694: b.ls            #0xe94f24
    //     0xe93698: str             x4, [THR, #0x50]  ; THR::top
    //     0xe9369c: sub             x4, x4, #0xf
    //     0xe936a0: movz            x5, #0xe15c
    //     0xe936a4: movk            x5, #0x3, lsl #16
    //     0xe936a8: stur            x5, [x4, #-1]
    // 0xe936ac: StoreField: r4->field_7 = d4
    //     0xe936ac: stur            d4, [x4, #7]
    // 0xe936b0: r5 = inline_Allocate_Double()
    //     0xe936b0: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xe936b4: add             x5, x5, #0x10
    //     0xe936b8: cmp             x6, x5
    //     0xe936bc: b.ls            #0xe94f60
    //     0xe936c0: str             x5, [THR, #0x50]  ; THR::top
    //     0xe936c4: sub             x5, x5, #0xf
    //     0xe936c8: movz            x6, #0xe15c
    //     0xe936cc: movk            x6, #0x3, lsl #16
    //     0xe936d0: stur            x6, [x5, #-1]
    // 0xe936d4: StoreField: r5->field_7 = d5
    //     0xe936d4: stur            d5, [x5, #7]
    // 0xe936d8: r6 = inline_Allocate_Double()
    //     0xe936d8: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0xe936dc: add             x6, x6, #0x10
    //     0xe936e0: cmp             x7, x6
    //     0xe936e4: b.ls            #0xe94f9c
    //     0xe936e8: str             x6, [THR, #0x50]  ; THR::top
    //     0xe936ec: sub             x6, x6, #0xf
    //     0xe936f0: movz            x7, #0xe15c
    //     0xe936f4: movk            x7, #0x3, lsl #16
    //     0xe936f8: stur            x7, [x6, #-1]
    // 0xe936fc: StoreField: r6->field_7 = d6
    //     0xe936fc: stur            d6, [x6, #7]
    // 0xe93700: r7 = inline_Allocate_Double()
    //     0xe93700: ldp             x7, x8, [THR, #0x50]  ; THR::top
    //     0xe93704: add             x7, x7, #0x10
    //     0xe93708: cmp             x8, x7
    //     0xe9370c: b.ls            #0xe94fd8
    //     0xe93710: str             x7, [THR, #0x50]  ; THR::top
    //     0xe93714: sub             x7, x7, #0xf
    //     0xe93718: movz            x8, #0xe15c
    //     0xe9371c: movk            x8, #0x3, lsl #16
    //     0xe93720: stur            x8, [x7, #-1]
    // 0xe93724: StoreField: r7->field_7 = d7
    //     0xe93724: stur            d7, [x7, #7]
    // 0xe93728: r8 = inline_Allocate_Double()
    //     0xe93728: ldp             x8, x9, [THR, #0x50]  ; THR::top
    //     0xe9372c: add             x8, x8, #0x10
    //     0xe93730: cmp             x9, x8
    //     0xe93734: b.ls            #0xe95014
    //     0xe93738: str             x8, [THR, #0x50]  ; THR::top
    //     0xe9373c: sub             x8, x8, #0xf
    //     0xe93740: movz            x9, #0xe15c
    //     0xe93744: movk            x9, #0x3, lsl #16
    //     0xe93748: stur            x9, [x8, #-1]
    // 0xe9374c: StoreField: r8->field_7 = d9
    //     0xe9374c: stur            d9, [x8, #7]
    // 0xe93750: stp             x3, x2, [SP, #0x28]
    // 0xe93754: stp             x5, x4, [SP, #0x18]
    // 0xe93758: stp             x7, x6, [SP, #8]
    // 0xe9375c: str             x8, [SP]
    // 0xe93760: mov             v0.16b, v8.16b
    // 0xe93764: r4 = const [0, 0x9, 0x7, 0x2, ascent, 0x6, bottom, 0x5, descent, 0x7, left, 0x2, leftBearing, 0x8, right, 0x4, top, 0x3, null]
    //     0xe93764: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e130] List(19) [0, 0x9, 0x7, 0x2, "ascent", 0x6, "bottom", 0x5, "descent", 0x7, "left", 0x2, "leftBearing", 0x8, "right", 0x4, "top", 0x3, Null]
    //     0xe93768: ldr             x4, [x4, #0x130]
    // 0xe9376c: r0 = copyWith()
    //     0xe9376c: bl              #0x7b6ea4  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::copyWith
    // 0xe93770: mov             x1, x0
    // 0xe93774: ldur            x0, [fp, #-0x28]
    // 0xe93778: stur            x1, [fp, #-0xd0]
    // 0xe9377c: tbnz            w0, #4, #0xe93c14
    // 0xe93780: ldur            x2, [fp, #-0x58]
    // 0xe93784: ldur            d0, [fp, #-0x100]
    // 0xe93788: LoadField: r3 = r2->field_27
    //     0xe93788: ldur            w3, [x2, #0x27]
    // 0xe9378c: DecompressPointer r3
    //     0xe9378c: add             x3, x3, HEAP, lsl #32
    // 0xe93790: LoadField: d1 = r1->field_1f
    //     0xe93790: ldur            d1, [x1, #0x1f]
    // 0xe93794: LoadField: d2 = r1->field_7
    //     0xe93794: ldur            d2, [x1, #7]
    // 0xe93798: fsub            d3, d1, d2
    // 0xe9379c: LoadField: d1 = r3->field_7
    //     0xe9379c: ldur            d1, [x3, #7]
    // 0xe937a0: fadd            d2, d1, d3
    // 0xe937a4: fcmp            d2, d0
    // 0xe937a8: b.le            #0xe93c0c
    // 0xe937ac: LoadField: r3 = r2->field_3b
    //     0xe937ac: ldur            w3, [x2, #0x3b]
    // 0xe937b0: DecompressPointer r3
    //     0xe937b0: add             x3, x3, HEAP, lsl #32
    // 0xe937b4: r4 = LoadInt32Instr(r3)
    //     0xe937b4: sbfx            x4, x3, #1, #0x1f
    //     0xe937b8: tbz             w3, #0, #0xe937c0
    //     0xe937bc: ldur            x4, [x3, #7]
    // 0xe937c0: stur            x4, [fp, #-0xc8]
    // 0xe937c4: cmp             x4, #0
    // 0xe937c8: b.le            #0xe93ac0
    // 0xe937cc: r17 = -264
    //     0xe937cc: movn            x17, #0x107
    // 0xe937d0: ldr             d2, [fp, x17]
    // 0xe937d4: fcmp            d2, d3
    // 0xe937d8: b.lt            #0xe93aa8
    // 0xe937dc: ldur            x5, [fp, #-0x40]
    // 0xe937e0: r17 = -296
    //     0xe937e0: movn            x17, #0x127
    // 0xe937e4: ldr             d3, [fp, x17]
    // 0xe937e8: ldur            x3, [fp, #-0x80]
    // 0xe937ec: r17 = -320
    //     0xe937ec: movn            x17, #0x13f
    // 0xe937f0: ldr             d4, [fp, x17]
    // 0xe937f4: r6 = true
    //     0xe937f4: add             x6, NULL, #0x20  ; true
    // 0xe937f8: StoreField: r2->field_43 = r6
    //     0xe937f8: stur            w6, [x2, #0x43]
    // 0xe937fc: LoadField: r7 = r2->field_f
    //     0xe937fc: ldur            w7, [x2, #0xf]
    // 0xe93800: DecompressPointer r7
    //     0xe93800: add             x7, x7, HEAP, lsl #32
    // 0xe93804: stur            x7, [fp, #-0xc0]
    // 0xe93808: LoadField: r8 = r2->field_3f
    //     0xe93808: ldur            w8, [x2, #0x3f]
    // 0xe9380c: DecompressPointer r8
    //     0xe9380c: add             x8, x8, HEAP, lsl #32
    // 0xe93810: stur            x8, [fp, #-0xb8]
    // 0xe93814: LoadField: r9 = r2->field_33
    //     0xe93814: ldur            w9, [x2, #0x33]
    // 0xe93818: DecompressPointer r9
    //     0xe93818: add             x9, x9, HEAP, lsl #32
    // 0xe9381c: stur            x9, [fp, #-0xb0]
    // 0xe93820: cmp             w3, NULL
    // 0xe93824: b.eq            #0xe95050
    // 0xe93828: LoadField: d5 = r3->field_7
    //     0xe93828: ldur            d5, [x3, #7]
    // 0xe9382c: fmul            d6, d3, d5
    // 0xe93830: fsub            d5, d1, d6
    // 0xe93834: fsub            d1, d5, d4
    // 0xe93838: r17 = -312
    //     0xe93838: movn            x17, #0x137
    // 0xe9383c: str             d1, [fp, x17]
    // 0xe93840: r0 = _Line()
    //     0xe93840: bl              #0xe91de8  ; Allocate_LineStub -> _Line (size=0x34)
    // 0xe93844: mov             x3, x0
    // 0xe93848: ldur            x0, [fp, #-0xc0]
    // 0xe9384c: stur            x3, [fp, #-0xd8]
    // 0xe93850: StoreField: r3->field_7 = r0
    //     0xe93850: stur            w0, [x3, #7]
    // 0xe93854: ldur            x0, [fp, #-0xb8]
    // 0xe93858: r1 = LoadInt32Instr(r0)
    //     0xe93858: sbfx            x1, x0, #1, #0x1f
    //     0xe9385c: tbz             w0, #0, #0xe93864
    //     0xe93860: ldur            x1, [x0, #7]
    // 0xe93864: StoreField: r3->field_b = r1
    //     0xe93864: stur            x1, [x3, #0xb]
    // 0xe93868: ldur            x0, [fp, #-0xc8]
    // 0xe9386c: StoreField: r3->field_13 = r0
    //     0xe9386c: stur            x0, [x3, #0x13]
    // 0xe93870: ldur            x0, [fp, #-0xb0]
    // 0xe93874: LoadField: d0 = r0->field_7
    //     0xe93874: ldur            d0, [x0, #7]
    // 0xe93878: StoreField: r3->field_1b = d0
    //     0xe93878: stur            d0, [x3, #0x1b]
    // 0xe9387c: r17 = -312
    //     0xe9387c: movn            x17, #0x137
    // 0xe93880: ldr             d0, [fp, x17]
    // 0xe93884: StoreField: r3->field_23 = d0
    //     0xe93884: stur            d0, [x3, #0x23]
    // 0xe93888: r4 = Instance_TextDirection
    //     0xe93888: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e2e0] Obj!TextDirection@e2e5a1
    //     0xe9388c: ldr             x4, [x4, #0x2e0]
    // 0xe93890: StoreField: r3->field_2b = r4
    //     0xe93890: stur            w4, [x3, #0x2b]
    // 0xe93894: r5 = true
    //     0xe93894: add             x5, NULL, #0x20  ; true
    // 0xe93898: StoreField: r3->field_2f = r5
    //     0xe93898: stur            w5, [x3, #0x2f]
    // 0xe9389c: mov             x0, x3
    // 0xe938a0: ldur            x2, [fp, #-0x38]
    // 0xe938a4: r1 = Null
    //     0xe938a4: mov             x1, NULL
    // 0xe938a8: cmp             w2, NULL
    // 0xe938ac: b.eq            #0xe938cc
    // 0xe938b0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe938b0: ldur            w4, [x2, #0x17]
    // 0xe938b4: DecompressPointer r4
    //     0xe938b4: add             x4, x4, HEAP, lsl #32
    // 0xe938b8: r8 = X0
    //     0xe938b8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe938bc: LoadField: r9 = r4->field_7
    //     0xe938bc: ldur            x9, [x4, #7]
    // 0xe938c0: r3 = Null
    //     0xe938c0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e138] Null
    //     0xe938c4: ldr             x3, [x3, #0x138]
    // 0xe938c8: blr             x9
    // 0xe938cc: ldur            x0, [fp, #-0x40]
    // 0xe938d0: LoadField: r1 = r0->field_b
    //     0xe938d0: ldur            w1, [x0, #0xb]
    // 0xe938d4: LoadField: r2 = r0->field_f
    //     0xe938d4: ldur            w2, [x0, #0xf]
    // 0xe938d8: DecompressPointer r2
    //     0xe938d8: add             x2, x2, HEAP, lsl #32
    // 0xe938dc: LoadField: r3 = r2->field_b
    //     0xe938dc: ldur            w3, [x2, #0xb]
    // 0xe938e0: r2 = LoadInt32Instr(r1)
    //     0xe938e0: sbfx            x2, x1, #1, #0x1f
    // 0xe938e4: stur            x2, [fp, #-0xc8]
    // 0xe938e8: r1 = LoadInt32Instr(r3)
    //     0xe938e8: sbfx            x1, x3, #1, #0x1f
    // 0xe938ec: cmp             x2, x1
    // 0xe938f0: b.ne            #0xe938fc
    // 0xe938f4: mov             x1, x0
    // 0xe938f8: r0 = _growToNextCapacity()
    //     0xe938f8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe938fc: ldur            x6, [fp, #-0x58]
    // 0xe93900: ldur            x4, [fp, #-0x40]
    // 0xe93904: ldur            x2, [fp, #-0xc8]
    // 0xe93908: ldur            d1, [fp, #-0xf8]
    // 0xe9390c: r7 = 0.000000
    //     0xe9390c: ldr             x7, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe93910: add             x0, x2, #1
    // 0xe93914: lsl             x1, x0, #1
    // 0xe93918: StoreField: r4->field_b = r1
    //     0xe93918: stur            w1, [x4, #0xb]
    // 0xe9391c: LoadField: r1 = r4->field_f
    //     0xe9391c: ldur            w1, [x4, #0xf]
    // 0xe93920: DecompressPointer r1
    //     0xe93920: add             x1, x1, HEAP, lsl #32
    // 0xe93924: ldur            x0, [fp, #-0xd8]
    // 0xe93928: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe93928: add             x25, x1, x2, lsl #2
    //     0xe9392c: add             x25, x25, #0xf
    //     0xe93930: str             w0, [x25]
    //     0xe93934: tbz             w0, #0, #0xe93950
    //     0xe93938: ldurb           w16, [x1, #-1]
    //     0xe9393c: ldurb           w17, [x0, #-1]
    //     0xe93940: and             x16, x17, x16, lsr #2
    //     0xe93944: tst             x16, HEAP, lsr #32
    //     0xe93948: b.eq            #0xe93950
    //     0xe9394c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe93950: LoadField: r0 = r6->field_3f
    //     0xe93950: ldur            w0, [x6, #0x3f]
    // 0xe93954: DecompressPointer r0
    //     0xe93954: add             x0, x0, HEAP, lsl #32
    // 0xe93958: LoadField: r1 = r6->field_3b
    //     0xe93958: ldur            w1, [x6, #0x3b]
    // 0xe9395c: DecompressPointer r1
    //     0xe9395c: add             x1, x1, HEAP, lsl #32
    // 0xe93960: r2 = LoadInt32Instr(r0)
    //     0xe93960: sbfx            x2, x0, #1, #0x1f
    //     0xe93964: tbz             w0, #0, #0xe9396c
    //     0xe93968: ldur            x2, [x0, #7]
    // 0xe9396c: r0 = LoadInt32Instr(r1)
    //     0xe9396c: sbfx            x0, x1, #1, #0x1f
    //     0xe93970: tbz             w1, #0, #0xe93978
    //     0xe93974: ldur            x0, [x1, #7]
    // 0xe93978: add             x3, x2, x0
    // 0xe9397c: r0 = BoxInt64Instr(r3)
    //     0xe9397c: sbfiz           x0, x3, #1, #0x1f
    //     0xe93980: cmp             x3, x0, asr #1
    //     0xe93984: b.eq            #0xe93990
    //     0xe93988: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe9398c: stur            x3, [x0, #7]
    // 0xe93990: StoreField: r6->field_3f = r0
    //     0xe93990: stur            w0, [x6, #0x3f]
    //     0xe93994: tbz             w0, #0, #0xe939b0
    //     0xe93998: ldurb           w16, [x6, #-1]
    //     0xe9399c: ldurb           w17, [x0, #-1]
    //     0xe939a0: and             x16, x17, x16, lsr #2
    //     0xe939a4: tst             x16, HEAP, lsr #32
    //     0xe939a8: b.eq            #0xe939b0
    //     0xe939ac: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xe939b0: StoreField: r6->field_3b = rZR
    //     0xe939b0: stur            wzr, [x6, #0x3b]
    // 0xe939b4: StoreField: r6->field_27 = r7
    //     0xe939b4: stur            w7, [x6, #0x27]
    // 0xe939b8: LoadField: r0 = r6->field_2b
    //     0xe939b8: ldur            w0, [x6, #0x2b]
    // 0xe939bc: DecompressPointer r0
    //     0xe939bc: add             x0, x0, HEAP, lsl #32
    // 0xe939c0: LoadField: r1 = r6->field_33
    //     0xe939c0: ldur            w1, [x6, #0x33]
    // 0xe939c4: DecompressPointer r1
    //     0xe939c4: add             x1, x1, HEAP, lsl #32
    // 0xe939c8: LoadField: r2 = r6->field_2f
    //     0xe939c8: ldur            w2, [x6, #0x2f]
    // 0xe939cc: DecompressPointer r2
    //     0xe939cc: add             x2, x2, HEAP, lsl #32
    // 0xe939d0: LoadField: d0 = r1->field_7
    //     0xe939d0: ldur            d0, [x1, #7]
    // 0xe939d4: LoadField: d2 = r2->field_7
    //     0xe939d4: ldur            d2, [x2, #7]
    // 0xe939d8: fsub            d3, d0, d2
    // 0xe939dc: LoadField: d0 = r0->field_7
    //     0xe939dc: ldur            d0, [x0, #7]
    // 0xe939e0: fadd            d2, d0, d3
    // 0xe939e4: r0 = inline_Allocate_Double()
    //     0xe939e4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe939e8: add             x0, x0, #0x10
    //     0xe939ec: cmp             x1, x0
    //     0xe939f0: b.ls            #0xe95054
    //     0xe939f4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe939f8: sub             x0, x0, #0xf
    //     0xe939fc: movz            x1, #0xe15c
    //     0xe93a00: movk            x1, #0x3, lsl #16
    //     0xe93a04: stur            x1, [x0, #-1]
    // 0xe93a08: StoreField: r0->field_7 = d2
    //     0xe93a08: stur            d2, [x0, #7]
    // 0xe93a0c: StoreField: r6->field_2b = r0
    //     0xe93a0c: stur            w0, [x6, #0x2b]
    //     0xe93a10: ldurb           w16, [x6, #-1]
    //     0xe93a14: ldurb           w17, [x0, #-1]
    //     0xe93a18: and             x16, x17, x16, lsr #2
    //     0xe93a1c: tst             x16, HEAP, lsr #32
    //     0xe93a20: b.eq            #0xe93a28
    //     0xe93a24: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xe93a28: StoreField: r6->field_2f = r7
    //     0xe93a28: stur            w7, [x6, #0x2f]
    // 0xe93a2c: StoreField: r6->field_33 = r7
    //     0xe93a2c: stur            w7, [x6, #0x33]
    // 0xe93a30: fcmp            d2, d1
    // 0xe93a34: b.gt            #0xe93a98
    // 0xe93a38: ldur            x8, [fp, #-0x70]
    // 0xe93a3c: cmp             w8, NULL
    // 0xe93a40: b.eq            #0xe95074
    // 0xe93a44: LoadField: d0 = r8->field_7
    //     0xe93a44: ldur            d0, [x8, #7]
    // 0xe93a48: fadd            d3, d2, d0
    // 0xe93a4c: r0 = inline_Allocate_Double()
    //     0xe93a4c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe93a50: add             x0, x0, #0x10
    //     0xe93a54: cmp             x1, x0
    //     0xe93a58: b.ls            #0xe95078
    //     0xe93a5c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe93a60: sub             x0, x0, #0xf
    //     0xe93a64: movz            x1, #0xe15c
    //     0xe93a68: movk            x1, #0x3, lsl #16
    //     0xe93a6c: stur            x1, [x0, #-1]
    // 0xe93a70: StoreField: r0->field_7 = d3
    //     0xe93a70: stur            d3, [x0, #7]
    // 0xe93a74: StoreField: r6->field_2b = r0
    //     0xe93a74: stur            w0, [x6, #0x2b]
    //     0xe93a78: ldurb           w16, [x6, #-1]
    //     0xe93a7c: ldurb           w17, [x0, #-1]
    //     0xe93a80: and             x16, x17, x16, lsr #2
    //     0xe93a84: tst             x16, HEAP, lsr #32
    //     0xe93a88: b.eq            #0xe93a90
    //     0xe93a8c: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xe93a90: ldur            x1, [fp, #-0xa0]
    // 0xe93a94: b               #0xe93c18
    // 0xe93a98: r0 = Null
    //     0xe93a98: mov             x0, NULL
    // 0xe93a9c: LeaveFrame
    //     0xe93a9c: mov             SP, fp
    //     0xe93aa0: ldp             fp, lr, [SP], #0x10
    // 0xe93aa4: ret
    //     0xe93aa4: ret             
    // 0xe93aa8: mov             x6, x2
    // 0xe93aac: ldur            x4, [fp, #-0x40]
    // 0xe93ab0: ldur            x8, [fp, #-0x70]
    // 0xe93ab4: ldur            d1, [fp, #-0xf8]
    // 0xe93ab8: r7 = 0.000000
    //     0xe93ab8: ldr             x7, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe93abc: b               #0xe93ad4
    // 0xe93ac0: mov             x6, x2
    // 0xe93ac4: ldur            x4, [fp, #-0x40]
    // 0xe93ac8: ldur            x8, [fp, #-0x70]
    // 0xe93acc: ldur            d1, [fp, #-0xf8]
    // 0xe93ad0: r7 = 0.000000
    //     0xe93ad0: ldr             x7, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe93ad4: ldur            x0, [fp, #-8]
    // 0xe93ad8: LoadField: r1 = r6->field_f
    //     0xe93ad8: ldur            w1, [x6, #0xf]
    // 0xe93adc: DecompressPointer r1
    //     0xe93adc: add             x1, x1, HEAP, lsl #32
    // 0xe93ae0: ldur            x2, [fp, #-0x98]
    // 0xe93ae4: ldur            x3, [fp, #-0x68]
    // 0xe93ae8: ldur            x5, [fp, #-0x10]
    // 0xe93aec: r17 = -264
    //     0xe93aec: movn            x17, #0x107
    // 0xe93af0: ldr             d0, [fp, x17]
    // 0xe93af4: r0 = _splitWord()
    //     0xe93af4: bl              #0xe954f4  ; [package:pdf/src/widgets/text.dart] RichText::_splitWord
    // 0xe93af8: mov             x4, x0
    // 0xe93afc: ldur            x0, [fp, #-8]
    // 0xe93b00: stur            x4, [fp, #-0xe0]
    // 0xe93b04: r5 = LoadInt32Instr(r0)
    //     0xe93b04: sbfx            x5, x0, #1, #0x1f
    // 0xe93b08: stur            x5, [fp, #-0xc8]
    // 0xe93b0c: cmp             x4, x5
    // 0xe93b10: b.ge            #0xe93c04
    // 0xe93b14: ldur            x6, [fp, #-0xa8]
    // 0xe93b18: ldur            x7, [fp, #-0xa0]
    // 0xe93b1c: r0 = BoxInt64Instr(r4)
    //     0xe93b1c: sbfiz           x0, x4, #1, #0x1f
    //     0xe93b20: cmp             x4, x0, asr #1
    //     0xe93b24: b.eq            #0xe93b30
    //     0xe93b28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe93b2c: stur            x4, [x0, #7]
    // 0xe93b30: mov             x2, x0
    // 0xe93b34: mov             x3, x5
    // 0xe93b38: r1 = 0
    //     0xe93b38: movz            x1, #0
    // 0xe93b3c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe93b3c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe93b40: r0 = checkValidRange()
    //     0xe93b40: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe93b44: ldur            x1, [fp, #-0x98]
    // 0xe93b48: mov             x3, x0
    // 0xe93b4c: r2 = 0
    //     0xe93b4c: movz            x2, #0
    // 0xe93b50: r0 = _substringUnchecked()
    //     0xe93b50: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xe93b54: mov             x2, x0
    // 0xe93b58: ldur            x4, [fp, #-0xa8]
    // 0xe93b5c: LoadField: r0 = r4->field_b
    //     0xe93b5c: ldur            w0, [x4, #0xb]
    // 0xe93b60: r1 = LoadInt32Instr(r0)
    //     0xe93b60: sbfx            x1, x0, #1, #0x1f
    // 0xe93b64: mov             x0, x1
    // 0xe93b68: ldur            x1, [fp, #-0xa0]
    // 0xe93b6c: cmp             x1, x0
    // 0xe93b70: b.hs            #0xe95098
    // 0xe93b74: LoadField: r1 = r4->field_f
    //     0xe93b74: ldur            w1, [x4, #0xf]
    // 0xe93b78: DecompressPointer r1
    //     0xe93b78: add             x1, x1, HEAP, lsl #32
    // 0xe93b7c: mov             x0, x2
    // 0xe93b80: ldur            x5, [fp, #-0xa0]
    // 0xe93b84: ArrayStore: r1[r5] = r0  ; List_4
    //     0xe93b84: add             x25, x1, x5, lsl #2
    //     0xe93b88: add             x25, x25, #0xf
    //     0xe93b8c: str             w0, [x25]
    //     0xe93b90: tbz             w0, #0, #0xe93bac
    //     0xe93b94: ldurb           w16, [x1, #-1]
    //     0xe93b98: ldurb           w17, [x0, #-1]
    //     0xe93b9c: and             x16, x17, x16, lsr #2
    //     0xe93ba0: tst             x16, HEAP, lsr #32
    //     0xe93ba4: b.eq            #0xe93bac
    //     0xe93ba8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe93bac: add             x0, x5, #1
    // 0xe93bb0: ldur            x1, [fp, #-0xe0]
    // 0xe93bb4: ldur            x3, [fp, #-0xc8]
    // 0xe93bb8: stur            x0, [fp, #-0xe8]
    // 0xe93bbc: r2 = Null
    //     0xe93bbc: mov             x2, NULL
    // 0xe93bc0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe93bc0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe93bc4: r0 = checkValidRange()
    //     0xe93bc4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe93bc8: ldur            x1, [fp, #-0x98]
    // 0xe93bcc: ldur            x2, [fp, #-0xe0]
    // 0xe93bd0: mov             x3, x0
    // 0xe93bd4: r0 = _substringUnchecked()
    //     0xe93bd4: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xe93bd8: ldur            x1, [fp, #-0xa8]
    // 0xe93bdc: ldur            x2, [fp, #-0xe8]
    // 0xe93be0: mov             x3, x0
    // 0xe93be4: r0 = insert()
    //     0xe93be4: bl              #0x6e39fc  ; [dart:core] _GrowableList::insert
    // 0xe93be8: ldur            x1, [fp, #-0xa0]
    // 0xe93bec: sub             x0, x1, #1
    // 0xe93bf0: ldur            x1, [fp, #-0x58]
    // 0xe93bf4: r17 = -296
    //     0xe93bf4: movn            x17, #0x127
    // 0xe93bf8: ldr             d2, [fp, x17]
    // 0xe93bfc: ldur            x2, [fp, #-0x80]
    // 0xe93c00: b               #0xe94044
    // 0xe93c04: ldur            x1, [fp, #-0xa0]
    // 0xe93c08: b               #0xe93c18
    // 0xe93c0c: ldur            x1, [fp, #-0xa0]
    // 0xe93c10: b               #0xe93c18
    // 0xe93c14: ldur            x1, [fp, #-0xa0]
    // 0xe93c18: ldur            x2, [fp, #-0x58]
    // 0xe93c1c: r17 = -304
    //     0xe93c1c: movn            x17, #0x12f
    // 0xe93c20: ldr             d0, [fp, x17]
    // 0xe93c24: ldur            x3, [fp, #-0xd0]
    // 0xe93c28: LoadField: d1 = r3->field_2f
    //     0xe93c28: ldur            d1, [x3, #0x2f]
    // 0xe93c2c: LoadField: d2 = r3->field_27
    //     0xe93c2c: ldur            d2, [x3, #0x27]
    // 0xe93c30: LoadField: r0 = r2->field_2f
    //     0xe93c30: ldur            w0, [x2, #0x2f]
    // 0xe93c34: DecompressPointer r0
    //     0xe93c34: add             x0, x0, HEAP, lsl #32
    // 0xe93c38: fadd            d3, d1, d0
    // 0xe93c3c: LoadField: d1 = r0->field_7
    //     0xe93c3c: ldur            d1, [x0, #7]
    // 0xe93c40: fcmp            d1, d3
    // 0xe93c44: b.le            #0xe93c54
    // 0xe93c48: mov             v1.16b, v3.16b
    // 0xe93c4c: d4 = 0.000000
    //     0xe93c4c: eor             v4.16b, v4.16b, v4.16b
    // 0xe93c50: b               #0xe93cb8
    // 0xe93c54: fcmp            d3, d1
    // 0xe93c58: b.le            #0xe93c68
    // 0xe93c5c: LoadField: d1 = r0->field_7
    //     0xe93c5c: ldur            d1, [x0, #7]
    // 0xe93c60: d4 = 0.000000
    //     0xe93c60: eor             v4.16b, v4.16b, v4.16b
    // 0xe93c64: b               #0xe93cb8
    // 0xe93c68: d4 = 0.000000
    //     0xe93c68: eor             v4.16b, v4.16b, v4.16b
    // 0xe93c6c: fcmp            d1, d4
    // 0xe93c70: b.ne            #0xe93c84
    // 0xe93c74: fadd            d5, d1, d3
    // 0xe93c78: fmul            d6, d5, d1
    // 0xe93c7c: fmul            d1, d6, d3
    // 0xe93c80: b               #0xe93cb8
    // 0xe93c84: fcmp            d1, d4
    // 0xe93c88: b.ne            #0xe93ca4
    // 0xe93c8c: fcmp            d3, #0.0
    // 0xe93c90: b.vs            #0xe93ca4
    // 0xe93c94: b.ne            #0xe93ca0
    // 0xe93c98: r4 = 0.000000
    //     0xe93c98: fmov            x4, d3
    // 0xe93c9c: cmp             x4, #0
    // 0xe93ca0: b.lt            #0xe93cac
    // 0xe93ca4: fcmp            d3, d3
    // 0xe93ca8: b.vc            #0xe93cb4
    // 0xe93cac: mov             v1.16b, v3.16b
    // 0xe93cb0: b               #0xe93cb8
    // 0xe93cb4: LoadField: d1 = r0->field_7
    //     0xe93cb4: ldur            d1, [x0, #7]
    // 0xe93cb8: r0 = inline_Allocate_Double()
    //     0xe93cb8: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xe93cbc: add             x0, x0, #0x10
    //     0xe93cc0: cmp             x4, x0
    //     0xe93cc4: b.ls            #0xe9509c
    //     0xe93cc8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe93ccc: sub             x0, x0, #0xf
    //     0xe93cd0: movz            x4, #0xe15c
    //     0xe93cd4: movk            x4, #0x3, lsl #16
    //     0xe93cd8: stur            x4, [x0, #-1]
    // 0xe93cdc: StoreField: r0->field_7 = d1
    //     0xe93cdc: stur            d1, [x0, #7]
    // 0xe93ce0: StoreField: r2->field_2f = r0
    //     0xe93ce0: stur            w0, [x2, #0x2f]
    //     0xe93ce4: ldurb           w16, [x2, #-1]
    //     0xe93ce8: ldurb           w17, [x0, #-1]
    //     0xe93cec: and             x16, x17, x16, lsr #2
    //     0xe93cf0: tst             x16, HEAP, lsr #32
    //     0xe93cf4: b.eq            #0xe93cfc
    //     0xe93cf8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe93cfc: LoadField: r0 = r2->field_33
    //     0xe93cfc: ldur            w0, [x2, #0x33]
    // 0xe93d00: DecompressPointer r0
    //     0xe93d00: add             x0, x0, HEAP, lsl #32
    // 0xe93d04: fadd            d1, d2, d0
    // 0xe93d08: LoadField: d2 = r0->field_7
    //     0xe93d08: ldur            d2, [x0, #7]
    // 0xe93d0c: fcmp            d2, d1
    // 0xe93d10: b.le            #0xe93d1c
    // 0xe93d14: LoadField: d1 = r0->field_7
    //     0xe93d14: ldur            d1, [x0, #7]
    // 0xe93d18: b               #0xe93d44
    // 0xe93d1c: fcmp            d1, d2
    // 0xe93d20: b.gt            #0xe93d44
    // 0xe93d24: fcmp            d2, d4
    // 0xe93d28: b.ne            #0xe93d38
    // 0xe93d2c: fadd            d3, d2, d1
    // 0xe93d30: mov             v1.16b, v3.16b
    // 0xe93d34: b               #0xe93d44
    // 0xe93d38: fcmp            d1, d1
    // 0xe93d3c: b.vs            #0xe93d44
    // 0xe93d40: LoadField: d1 = r0->field_7
    //     0xe93d40: ldur            d1, [x0, #7]
    // 0xe93d44: ldur            x4, [fp, #-0x10]
    // 0xe93d48: ldur            x5, [fp, #-0x98]
    // 0xe93d4c: r0 = inline_Allocate_Double()
    //     0xe93d4c: ldp             x0, x6, [THR, #0x50]  ; THR::top
    //     0xe93d50: add             x0, x0, #0x10
    //     0xe93d54: cmp             x6, x0
    //     0xe93d58: b.ls            #0xe950c4
    //     0xe93d5c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe93d60: sub             x0, x0, #0xf
    //     0xe93d64: movz            x6, #0xe15c
    //     0xe93d68: movk            x6, #0x3, lsl #16
    //     0xe93d6c: stur            x6, [x0, #-1]
    // 0xe93d70: StoreField: r0->field_7 = d1
    //     0xe93d70: stur            d1, [x0, #7]
    // 0xe93d74: StoreField: r2->field_33 = r0
    //     0xe93d74: stur            w0, [x2, #0x33]
    //     0xe93d78: ldurb           w16, [x2, #-1]
    //     0xe93d7c: ldurb           w17, [x0, #-1]
    //     0xe93d80: and             x16, x17, x16, lsr #2
    //     0xe93d84: tst             x16, HEAP, lsr #32
    //     0xe93d88: b.eq            #0xe93d90
    //     0xe93d8c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe93d90: r0 = _Word()
    //     0xe93d90: bl              #0xe954e8  ; Allocate_WordStub -> _Word (size=0x18)
    // 0xe93d94: mov             x1, x0
    // 0xe93d98: ldur            x0, [fp, #-0x98]
    // 0xe93d9c: stur            x1, [fp, #-8]
    // 0xe93da0: StoreField: r1->field_f = r0
    //     0xe93da0: stur            w0, [x1, #0xf]
    // 0xe93da4: ldur            x0, [fp, #-0xd0]
    // 0xe93da8: StoreField: r1->field_13 = r0
    //     0xe93da8: stur            w0, [x1, #0x13]
    // 0xe93dac: r2 = Instance_PdfPoint
    //     0xe93dac: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xe93db0: ldr             x2, [x2, #0x730]
    // 0xe93db4: StoreField: r1->field_b = r2
    //     0xe93db4: stur            w2, [x1, #0xb]
    // 0xe93db8: ldur            x3, [fp, #-0x10]
    // 0xe93dbc: StoreField: r1->field_7 = r3
    //     0xe93dbc: stur            w3, [x1, #7]
    // 0xe93dc0: ldur            x4, [fp, #-0x58]
    // 0xe93dc4: LoadField: r5 = r4->field_27
    //     0xe93dc4: ldur            w5, [x4, #0x27]
    // 0xe93dc8: DecompressPointer r5
    //     0xe93dc8: add             x5, x5, HEAP, lsl #32
    // 0xe93dcc: LoadField: r6 = r4->field_2b
    //     0xe93dcc: ldur            w6, [x4, #0x2b]
    // 0xe93dd0: DecompressPointer r6
    //     0xe93dd0: add             x6, x6, HEAP, lsl #32
    // 0xe93dd4: LoadField: d0 = r6->field_7
    //     0xe93dd4: ldur            d0, [x6, #7]
    // 0xe93dd8: fneg            d1, d0
    // 0xe93ddc: r17 = -304
    //     0xe93ddc: movn            x17, #0x12f
    // 0xe93de0: ldr             d0, [fp, x17]
    // 0xe93de4: fadd            d2, d1, d0
    // 0xe93de8: r17 = -328
    //     0xe93de8: movn            x17, #0x147
    // 0xe93dec: str             d2, [fp, x17]
    // 0xe93df0: LoadField: d1 = r5->field_7
    //     0xe93df0: ldur            d1, [x5, #7]
    // 0xe93df4: r17 = -312
    //     0xe93df4: movn            x17, #0x137
    // 0xe93df8: str             d1, [fp, x17]
    // 0xe93dfc: r0 = PdfPoint()
    //     0xe93dfc: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe93e00: r17 = -312
    //     0xe93e00: movn            x17, #0x137
    // 0xe93e04: ldr             d0, [fp, x17]
    // 0xe93e08: StoreField: r0->field_7 = d0
    //     0xe93e08: stur            d0, [x0, #7]
    // 0xe93e0c: r17 = -328
    //     0xe93e0c: movn            x17, #0x147
    // 0xe93e10: ldr             d0, [fp, x17]
    // 0xe93e14: StoreField: r0->field_f = d0
    //     0xe93e14: stur            d0, [x0, #0xf]
    // 0xe93e18: ldur            x3, [fp, #-8]
    // 0xe93e1c: StoreField: r3->field_b = r0
    //     0xe93e1c: stur            w0, [x3, #0xb]
    // 0xe93e20: ldur            x4, [fp, #-0x58]
    // 0xe93e24: LoadField: r0 = r4->field_f
    //     0xe93e24: ldur            w0, [x4, #0xf]
    // 0xe93e28: DecompressPointer r0
    //     0xe93e28: add             x0, x0, HEAP, lsl #32
    // 0xe93e2c: LoadField: r5 = r0->field_2f
    //     0xe93e2c: ldur            w5, [x0, #0x2f]
    // 0xe93e30: DecompressPointer r5
    //     0xe93e30: add             x5, x5, HEAP, lsl #32
    // 0xe93e34: stur            x5, [fp, #-0x98]
    // 0xe93e38: LoadField: r2 = r5->field_7
    //     0xe93e38: ldur            w2, [x5, #7]
    // 0xe93e3c: DecompressPointer r2
    //     0xe93e3c: add             x2, x2, HEAP, lsl #32
    // 0xe93e40: mov             x0, x3
    // 0xe93e44: r1 = Null
    //     0xe93e44: mov             x1, NULL
    // 0xe93e48: cmp             w2, NULL
    // 0xe93e4c: b.eq            #0xe93e6c
    // 0xe93e50: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe93e50: ldur            w4, [x2, #0x17]
    // 0xe93e54: DecompressPointer r4
    //     0xe93e54: add             x4, x4, HEAP, lsl #32
    // 0xe93e58: r8 = X0
    //     0xe93e58: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe93e5c: LoadField: r9 = r4->field_7
    //     0xe93e5c: ldur            x9, [x4, #7]
    // 0xe93e60: r3 = Null
    //     0xe93e60: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e148] Null
    //     0xe93e64: ldr             x3, [x3, #0x148]
    // 0xe93e68: blr             x9
    // 0xe93e6c: ldur            x0, [fp, #-0x98]
    // 0xe93e70: LoadField: r1 = r0->field_b
    //     0xe93e70: ldur            w1, [x0, #0xb]
    // 0xe93e74: LoadField: r2 = r0->field_f
    //     0xe93e74: ldur            w2, [x0, #0xf]
    // 0xe93e78: DecompressPointer r2
    //     0xe93e78: add             x2, x2, HEAP, lsl #32
    // 0xe93e7c: LoadField: r3 = r2->field_b
    //     0xe93e7c: ldur            w3, [x2, #0xb]
    // 0xe93e80: r2 = LoadInt32Instr(r1)
    //     0xe93e80: sbfx            x2, x1, #1, #0x1f
    // 0xe93e84: stur            x2, [fp, #-0xc8]
    // 0xe93e88: r1 = LoadInt32Instr(r3)
    //     0xe93e88: sbfx            x1, x3, #1, #0x1f
    // 0xe93e8c: cmp             x2, x1
    // 0xe93e90: b.ne            #0xe93e9c
    // 0xe93e94: mov             x1, x0
    // 0xe93e98: r0 = _growToNextCapacity()
    //     0xe93e98: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe93e9c: ldur            x3, [fp, #-0x58]
    // 0xe93ea0: ldur            x5, [fp, #-0x10]
    // 0xe93ea4: ldur            x0, [fp, #-0x98]
    // 0xe93ea8: r17 = -296
    //     0xe93ea8: movn            x17, #0x127
    // 0xe93eac: ldr             d0, [fp, x17]
    // 0xe93eb0: ldur            x6, [fp, #-0x80]
    // 0xe93eb4: ldur            x4, [fp, #-0xd0]
    // 0xe93eb8: r17 = -320
    //     0xe93eb8: movn            x17, #0x13f
    // 0xe93ebc: ldr             d1, [fp, x17]
    // 0xe93ec0: ldur            x2, [fp, #-0xc8]
    // 0xe93ec4: add             x1, x2, #1
    // 0xe93ec8: lsl             x7, x1, #1
    // 0xe93ecc: StoreField: r0->field_b = r7
    //     0xe93ecc: stur            w7, [x0, #0xb]
    // 0xe93ed0: LoadField: r1 = r0->field_f
    //     0xe93ed0: ldur            w1, [x0, #0xf]
    // 0xe93ed4: DecompressPointer r1
    //     0xe93ed4: add             x1, x1, HEAP, lsl #32
    // 0xe93ed8: ldur            x0, [fp, #-8]
    // 0xe93edc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe93edc: add             x25, x1, x2, lsl #2
    //     0xe93ee0: add             x25, x25, #0xf
    //     0xe93ee4: str             w0, [x25]
    //     0xe93ee8: tbz             w0, #0, #0xe93f04
    //     0xe93eec: ldurb           w16, [x1, #-1]
    //     0xe93ef0: ldurb           w17, [x0, #-1]
    //     0xe93ef4: and             x16, x17, x16, lsr #2
    //     0xe93ef8: tst             x16, HEAP, lsr #32
    //     0xe93efc: b.eq            #0xe93f04
    //     0xe93f00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe93f04: LoadField: r0 = r3->field_3b
    //     0xe93f04: ldur            w0, [x3, #0x3b]
    // 0xe93f08: DecompressPointer r0
    //     0xe93f08: add             x0, x0, HEAP, lsl #32
    // 0xe93f0c: r1 = LoadInt32Instr(r0)
    //     0xe93f0c: sbfx            x1, x0, #1, #0x1f
    //     0xe93f10: tbz             w0, #0, #0xe93f18
    //     0xe93f14: ldur            x1, [x0, #7]
    // 0xe93f18: add             x2, x1, #1
    // 0xe93f1c: r0 = BoxInt64Instr(r2)
    //     0xe93f1c: sbfiz           x0, x2, #1, #0x1f
    //     0xe93f20: cmp             x2, x0, asr #1
    //     0xe93f24: b.eq            #0xe93f30
    //     0xe93f28: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe93f2c: stur            x2, [x0, #7]
    // 0xe93f30: StoreField: r3->field_3b = r0
    //     0xe93f30: stur            w0, [x3, #0x3b]
    //     0xe93f34: tbz             w0, #0, #0xe93f50
    //     0xe93f38: ldurb           w16, [x3, #-1]
    //     0xe93f3c: ldurb           w17, [x0, #-1]
    //     0xe93f40: and             x16, x17, x16, lsr #2
    //     0xe93f44: tst             x16, HEAP, lsr #32
    //     0xe93f48: b.eq            #0xe93f50
    //     0xe93f4c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe93f50: LoadField: r1 = r3->field_f
    //     0xe93f50: ldur            w1, [x3, #0xf]
    // 0xe93f54: DecompressPointer r1
    //     0xe93f54: add             x1, x1, HEAP, lsl #32
    // 0xe93f58: stur            x1, [fp, #-0x98]
    // 0xe93f5c: cmp             x2, #1
    // 0xe93f60: r16 = true
    //     0xe93f60: add             x16, NULL, #0x20  ; true
    // 0xe93f64: r17 = false
    //     0xe93f64: add             x17, NULL, #0x30  ; false
    // 0xe93f68: csel            x0, x16, x17, gt
    // 0xe93f6c: stur            x0, [fp, #-8]
    // 0xe93f70: LoadField: r2 = r1->field_2f
    //     0xe93f70: ldur            w2, [x1, #0x2f]
    // 0xe93f74: DecompressPointer r2
    //     0xe93f74: add             x2, x2, HEAP, lsl #32
    // 0xe93f78: LoadField: r7 = r2->field_b
    //     0xe93f78: ldur            w7, [x2, #0xb]
    // 0xe93f7c: r2 = LoadInt32Instr(r7)
    //     0xe93f7c: sbfx            x2, x7, #1, #0x1f
    // 0xe93f80: sub             x7, x2, #1
    // 0xe93f84: stur            x7, [fp, #-0xc8]
    // 0xe93f88: r0 = _TextDecoration()
    //     0xe93f88: bl              #0xe954dc  ; Allocate_TextDecorationStub -> _TextDecoration (size=0x24)
    // 0xe93f8c: mov             x1, x0
    // 0xe93f90: ldur            x0, [fp, #-0x10]
    // 0xe93f94: StoreField: r1->field_7 = r0
    //     0xe93f94: stur            w0, [x1, #7]
    // 0xe93f98: ldur            x2, [fp, #-0xc8]
    // 0xe93f9c: StoreField: r1->field_f = r2
    //     0xe93f9c: stur            x2, [x1, #0xf]
    // 0xe93fa0: ArrayStore: r1[0] = r2  ; List_8
    //     0xe93fa0: stur            x2, [x1, #0x17]
    // 0xe93fa4: mov             x3, x1
    // 0xe93fa8: ldur            x1, [fp, #-0x98]
    // 0xe93fac: ldur            x2, [fp, #-8]
    // 0xe93fb0: r0 = _appendDecoration()
    //     0xe93fb0: bl              #0xe95284  ; [package:pdf/src/widgets/text.dart] RichText::_appendDecoration
    // 0xe93fb4: ldur            x1, [fp, #-0x58]
    // 0xe93fb8: LoadField: r0 = r1->field_27
    //     0xe93fb8: ldur            w0, [x1, #0x27]
    // 0xe93fbc: DecompressPointer r0
    //     0xe93fbc: add             x0, x0, HEAP, lsl #32
    // 0xe93fc0: ldur            x2, [fp, #-0xd0]
    // 0xe93fc4: LoadField: d0 = r2->field_37
    //     0xe93fc4: ldur            d0, [x2, #0x37]
    // 0xe93fc8: ldur            x2, [fp, #-0x80]
    // 0xe93fcc: cmp             w2, NULL
    // 0xe93fd0: b.eq            #0xe950f4
    // 0xe93fd4: LoadField: d1 = r2->field_7
    //     0xe93fd4: ldur            d1, [x2, #7]
    // 0xe93fd8: r17 = -296
    //     0xe93fd8: movn            x17, #0x127
    // 0xe93fdc: ldr             d2, [fp, x17]
    // 0xe93fe0: fmul            d3, d2, d1
    // 0xe93fe4: fadd            d1, d0, d3
    // 0xe93fe8: r17 = -320
    //     0xe93fe8: movn            x17, #0x13f
    // 0xe93fec: ldr             d0, [fp, x17]
    // 0xe93ff0: fadd            d3, d1, d0
    // 0xe93ff4: LoadField: d0 = r0->field_7
    //     0xe93ff4: ldur            d0, [x0, #7]
    // 0xe93ff8: fadd            d1, d0, d3
    // 0xe93ffc: r0 = inline_Allocate_Double()
    //     0xe93ffc: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xe94000: add             x0, x0, #0x10
    //     0xe94004: cmp             x3, x0
    //     0xe94008: b.ls            #0xe950f8
    //     0xe9400c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe94010: sub             x0, x0, #0xf
    //     0xe94014: movz            x3, #0xe15c
    //     0xe94018: movk            x3, #0x3, lsl #16
    //     0xe9401c: stur            x3, [x0, #-1]
    // 0xe94020: StoreField: r0->field_7 = d1
    //     0xe94020: stur            d1, [x0, #7]
    // 0xe94024: StoreField: r1->field_27 = r0
    //     0xe94024: stur            w0, [x1, #0x27]
    //     0xe94028: ldurb           w16, [x1, #-1]
    //     0xe9402c: ldurb           w17, [x0, #-1]
    //     0xe94030: and             x16, x17, x16, lsr #2
    //     0xe94034: tst             x16, HEAP, lsr #32
    //     0xe94038: b.eq            #0xe94040
    //     0xe9403c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe94040: ldur            x0, [fp, #-0xa0]
    // 0xe94044: add             x13, x0, #1
    // 0xe94048: mov             x10, x1
    // 0xe9404c: ldur            x3, [fp, #-0xa8]
    // 0xe94050: mov             v1.16b, v2.16b
    // 0xe94054: mov             x6, x2
    // 0xe94058: b               #0xe933e8
    // 0xe9405c: mov             x1, x10
    // 0xe94060: ldur            x0, [fp, #-0x90]
    // 0xe94064: ldur            x3, [fp, #-0x78]
    // 0xe94068: mov             v2.16b, v1.16b
    // 0xe9406c: mov             x2, x6
    // 0xe94070: LoadField: r4 = r0->field_b
    //     0xe94070: ldur            w4, [x0, #0xb]
    // 0xe94074: r5 = LoadInt32Instr(r4)
    //     0xe94074: sbfx            x5, x4, #1, #0x1f
    // 0xe94078: sub             x4, x5, #1
    // 0xe9407c: cmp             x3, x4
    // 0xe94080: b.ge            #0xe944f0
    // 0xe94084: ldur            x4, [fp, #-0x40]
    // 0xe94088: ldur            x5, [fp, #-0x88]
    // 0xe9408c: LoadField: r6 = r1->field_f
    //     0xe9408c: ldur            w6, [x1, #0xf]
    // 0xe94090: DecompressPointer r6
    //     0xe94090: add             x6, x6, HEAP, lsl #32
    // 0xe94094: stur            x6, [fp, #-0xb0]
    // 0xe94098: LoadField: r7 = r1->field_3f
    //     0xe94098: ldur            w7, [x1, #0x3f]
    // 0xe9409c: DecompressPointer r7
    //     0xe9409c: add             x7, x7, HEAP, lsl #32
    // 0xe940a0: stur            x7, [fp, #-0xa8]
    // 0xe940a4: LoadField: r8 = r1->field_3b
    //     0xe940a4: ldur            w8, [x1, #0x3b]
    // 0xe940a8: DecompressPointer r8
    //     0xe940a8: add             x8, x8, HEAP, lsl #32
    // 0xe940ac: stur            x8, [fp, #-0x98]
    // 0xe940b0: LoadField: r9 = r1->field_33
    //     0xe940b0: ldur            w9, [x1, #0x33]
    // 0xe940b4: DecompressPointer r9
    //     0xe940b4: add             x9, x9, HEAP, lsl #32
    // 0xe940b8: stur            x9, [fp, #-8]
    // 0xe940bc: LoadField: r10 = r1->field_27
    //     0xe940bc: ldur            w10, [x1, #0x27]
    // 0xe940c0: DecompressPointer r10
    //     0xe940c0: add             x10, x10, HEAP, lsl #32
    // 0xe940c4: cmp             w2, NULL
    // 0xe940c8: b.eq            #0xe95110
    // 0xe940cc: LoadField: d0 = r2->field_7
    //     0xe940cc: ldur            d0, [x2, #7]
    // 0xe940d0: fmul            d1, d2, d0
    // 0xe940d4: LoadField: d0 = r10->field_7
    //     0xe940d4: ldur            d0, [x10, #7]
    // 0xe940d8: fsub            d3, d0, d1
    // 0xe940dc: cmp             w5, NULL
    // 0xe940e0: b.eq            #0xe95114
    // 0xe940e4: LoadField: d0 = r5->field_7
    //     0xe940e4: ldur            d0, [x5, #7]
    // 0xe940e8: fsub            d1, d3, d0
    // 0xe940ec: r17 = -312
    //     0xe940ec: movn            x17, #0x137
    // 0xe940f0: str             d1, [fp, x17]
    // 0xe940f4: r0 = _Line()
    //     0xe940f4: bl              #0xe91de8  ; Allocate_LineStub -> _Line (size=0x34)
    // 0xe940f8: mov             x3, x0
    // 0xe940fc: ldur            x0, [fp, #-0xb0]
    // 0xe94100: stur            x3, [fp, #-0xb8]
    // 0xe94104: StoreField: r3->field_7 = r0
    //     0xe94104: stur            w0, [x3, #7]
    // 0xe94108: ldur            x0, [fp, #-0xa8]
    // 0xe9410c: r1 = LoadInt32Instr(r0)
    //     0xe9410c: sbfx            x1, x0, #1, #0x1f
    //     0xe94110: tbz             w0, #0, #0xe94118
    //     0xe94114: ldur            x1, [x0, #7]
    // 0xe94118: StoreField: r3->field_b = r1
    //     0xe94118: stur            x1, [x3, #0xb]
    // 0xe9411c: ldur            x0, [fp, #-0x98]
    // 0xe94120: r1 = LoadInt32Instr(r0)
    //     0xe94120: sbfx            x1, x0, #1, #0x1f
    //     0xe94124: tbz             w0, #0, #0xe9412c
    //     0xe94128: ldur            x1, [x0, #7]
    // 0xe9412c: StoreField: r3->field_13 = r1
    //     0xe9412c: stur            x1, [x3, #0x13]
    // 0xe94130: ldur            x0, [fp, #-8]
    // 0xe94134: LoadField: d0 = r0->field_7
    //     0xe94134: ldur            d0, [x0, #7]
    // 0xe94138: StoreField: r3->field_1b = d0
    //     0xe94138: stur            d0, [x3, #0x1b]
    // 0xe9413c: r17 = -312
    //     0xe9413c: movn            x17, #0x137
    // 0xe94140: ldr             d0, [fp, x17]
    // 0xe94144: StoreField: r3->field_23 = d0
    //     0xe94144: stur            d0, [x3, #0x23]
    // 0xe94148: r4 = Instance_TextDirection
    //     0xe94148: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e2e0] Obj!TextDirection@e2e5a1
    //     0xe9414c: ldr             x4, [x4, #0x2e0]
    // 0xe94150: StoreField: r3->field_2b = r4
    //     0xe94150: stur            w4, [x3, #0x2b]
    // 0xe94154: r5 = false
    //     0xe94154: add             x5, NULL, #0x30  ; false
    // 0xe94158: StoreField: r3->field_2f = r5
    //     0xe94158: stur            w5, [x3, #0x2f]
    // 0xe9415c: mov             x0, x3
    // 0xe94160: ldur            x2, [fp, #-0x38]
    // 0xe94164: r1 = Null
    //     0xe94164: mov             x1, NULL
    // 0xe94168: cmp             w2, NULL
    // 0xe9416c: b.eq            #0xe9418c
    // 0xe94170: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe94170: ldur            w4, [x2, #0x17]
    // 0xe94174: DecompressPointer r4
    //     0xe94174: add             x4, x4, HEAP, lsl #32
    // 0xe94178: r8 = X0
    //     0xe94178: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe9417c: LoadField: r9 = r4->field_7
    //     0xe9417c: ldur            x9, [x4, #7]
    // 0xe94180: r3 = Null
    //     0xe94180: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e158] Null
    //     0xe94184: ldr             x3, [x3, #0x158]
    // 0xe94188: blr             x9
    // 0xe9418c: ldur            x0, [fp, #-0x40]
    // 0xe94190: LoadField: r1 = r0->field_b
    //     0xe94190: ldur            w1, [x0, #0xb]
    // 0xe94194: LoadField: r2 = r0->field_f
    //     0xe94194: ldur            w2, [x0, #0xf]
    // 0xe94198: DecompressPointer r2
    //     0xe94198: add             x2, x2, HEAP, lsl #32
    // 0xe9419c: LoadField: r3 = r2->field_b
    //     0xe9419c: ldur            w3, [x2, #0xb]
    // 0xe941a0: r2 = LoadInt32Instr(r1)
    //     0xe941a0: sbfx            x2, x1, #1, #0x1f
    // 0xe941a4: stur            x2, [fp, #-0xa0]
    // 0xe941a8: r1 = LoadInt32Instr(r3)
    //     0xe941a8: sbfx            x1, x3, #1, #0x1f
    // 0xe941ac: cmp             x2, x1
    // 0xe941b0: b.ne            #0xe941bc
    // 0xe941b4: mov             x1, x0
    // 0xe941b8: r0 = _growToNextCapacity()
    //     0xe941b8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe941bc: ldur            x4, [fp, #-0x58]
    // 0xe941c0: ldur            x2, [fp, #-0x40]
    // 0xe941c4: ldur            x3, [fp, #-0xa0]
    // 0xe941c8: r5 = 0.000000
    //     0xe941c8: ldr             x5, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe941cc: add             x0, x3, #1
    // 0xe941d0: lsl             x1, x0, #1
    // 0xe941d4: StoreField: r2->field_b = r1
    //     0xe941d4: stur            w1, [x2, #0xb]
    // 0xe941d8: LoadField: r1 = r2->field_f
    //     0xe941d8: ldur            w1, [x2, #0xf]
    // 0xe941dc: DecompressPointer r1
    //     0xe941dc: add             x1, x1, HEAP, lsl #32
    // 0xe941e0: ldur            x0, [fp, #-0xb8]
    // 0xe941e4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe941e4: add             x25, x1, x3, lsl #2
    //     0xe941e8: add             x25, x25, #0xf
    //     0xe941ec: str             w0, [x25]
    //     0xe941f0: tbz             w0, #0, #0xe9420c
    //     0xe941f4: ldurb           w16, [x1, #-1]
    //     0xe941f8: ldurb           w17, [x0, #-1]
    //     0xe941fc: and             x16, x17, x16, lsr #2
    //     0xe94200: tst             x16, HEAP, lsr #32
    //     0xe94204: b.eq            #0xe9420c
    //     0xe94208: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe9420c: LoadField: r0 = r4->field_3f
    //     0xe9420c: ldur            w0, [x4, #0x3f]
    // 0xe94210: DecompressPointer r0
    //     0xe94210: add             x0, x0, HEAP, lsl #32
    // 0xe94214: LoadField: r1 = r4->field_3b
    //     0xe94214: ldur            w1, [x4, #0x3b]
    // 0xe94218: DecompressPointer r1
    //     0xe94218: add             x1, x1, HEAP, lsl #32
    // 0xe9421c: r3 = LoadInt32Instr(r0)
    //     0xe9421c: sbfx            x3, x0, #1, #0x1f
    //     0xe94220: tbz             w0, #0, #0xe94228
    //     0xe94224: ldur            x3, [x0, #7]
    // 0xe94228: r6 = LoadInt32Instr(r1)
    //     0xe94228: sbfx            x6, x1, #1, #0x1f
    //     0xe9422c: tbz             w1, #0, #0xe94234
    //     0xe94230: ldur            x6, [x1, #7]
    // 0xe94234: add             x7, x3, x6
    // 0xe94238: r0 = BoxInt64Instr(r7)
    //     0xe94238: sbfiz           x0, x7, #1, #0x1f
    //     0xe9423c: cmp             x7, x0, asr #1
    //     0xe94240: b.eq            #0xe9424c
    //     0xe94244: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe94248: stur            x7, [x0, #7]
    // 0xe9424c: StoreField: r4->field_3f = r0
    //     0xe9424c: stur            w0, [x4, #0x3f]
    //     0xe94250: tbz             w0, #0, #0xe9426c
    //     0xe94254: ldurb           w16, [x4, #-1]
    //     0xe94258: ldurb           w17, [x0, #-1]
    //     0xe9425c: and             x16, x17, x16, lsr #2
    //     0xe94260: tst             x16, HEAP, lsr #32
    //     0xe94264: b.eq            #0xe9426c
    //     0xe94268: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe9426c: StoreField: r4->field_27 = r5
    //     0xe9426c: stur            w5, [x4, #0x27]
    // 0xe94270: cmp             x6, #0
    // 0xe94274: b.le            #0xe94300
    // 0xe94278: LoadField: r0 = r4->field_2b
    //     0xe94278: ldur            w0, [x4, #0x2b]
    // 0xe9427c: DecompressPointer r0
    //     0xe9427c: add             x0, x0, HEAP, lsl #32
    // 0xe94280: LoadField: r1 = r4->field_33
    //     0xe94280: ldur            w1, [x4, #0x33]
    // 0xe94284: DecompressPointer r1
    //     0xe94284: add             x1, x1, HEAP, lsl #32
    // 0xe94288: LoadField: r3 = r4->field_2f
    //     0xe94288: ldur            w3, [x4, #0x2f]
    // 0xe9428c: DecompressPointer r3
    //     0xe9428c: add             x3, x3, HEAP, lsl #32
    // 0xe94290: LoadField: d0 = r1->field_7
    //     0xe94290: ldur            d0, [x1, #7]
    // 0xe94294: LoadField: d1 = r3->field_7
    //     0xe94294: ldur            d1, [x3, #7]
    // 0xe94298: fsub            d2, d0, d1
    // 0xe9429c: LoadField: d0 = r0->field_7
    //     0xe9429c: ldur            d0, [x0, #7]
    // 0xe942a0: fadd            d1, d0, d2
    // 0xe942a4: r0 = inline_Allocate_Double()
    //     0xe942a4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe942a8: add             x0, x0, #0x10
    //     0xe942ac: cmp             x1, x0
    //     0xe942b0: b.ls            #0xe95118
    //     0xe942b4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe942b8: sub             x0, x0, #0xf
    //     0xe942bc: movz            x1, #0xe15c
    //     0xe942c0: movk            x1, #0x3, lsl #16
    //     0xe942c4: stur            x1, [x0, #-1]
    // 0xe942c8: StoreField: r0->field_7 = d1
    //     0xe942c8: stur            d1, [x0, #7]
    // 0xe942cc: StoreField: r4->field_2b = r0
    //     0xe942cc: stur            w0, [x4, #0x2b]
    //     0xe942d0: ldurb           w16, [x4, #-1]
    //     0xe942d4: ldurb           w17, [x0, #-1]
    //     0xe942d8: and             x16, x17, x16, lsr #2
    //     0xe942dc: tst             x16, HEAP, lsr #32
    //     0xe942e0: b.eq            #0xe942e8
    //     0xe942e4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe942e8: mov             v2.16b, v1.16b
    // 0xe942ec: mov             x3, x4
    // 0xe942f0: mov             x4, x5
    // 0xe942f4: r17 = -288
    //     0xe942f4: movn            x17, #0x11f
    // 0xe942f8: ldr             d1, [fp, x17]
    // 0xe942fc: b               #0xe9446c
    // 0xe94300: ldur            x0, [fp, #-0x60]
    // 0xe94304: LoadField: r3 = r4->field_2b
    //     0xe94304: ldur            w3, [x4, #0x2b]
    // 0xe94308: DecompressPointer r3
    //     0xe94308: add             x3, x3, HEAP, lsl #32
    // 0xe9430c: stur            x3, [fp, #-0x98]
    // 0xe94310: cmp             x0, #0x37f
    // 0xe94314: b.ne            #0xe94324
    // 0xe94318: ldur            x6, [fp, #-0x68]
    // 0xe9431c: LoadField: d0 = r6->field_33
    //     0xe9431c: ldur            d0, [x6, #0x33]
    // 0xe94320: b               #0xe94380
    // 0xe94324: ldur            x6, [fp, #-0x68]
    // 0xe94328: LoadField: r7 = r6->field_3f
    //     0xe94328: ldur            w7, [x6, #0x3f]
    // 0xe9432c: DecompressPointer r7
    //     0xe9432c: add             x7, x7, HEAP, lsl #32
    // 0xe94330: mov             x1, x7
    // 0xe94334: stur            x7, [fp, #-8]
    // 0xe94338: r0 = ascent()
    //     0xe94338: bl              #0xc3831c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::ascent
    // 0xe9433c: mov             x2, x0
    // 0xe94340: r0 = BoxInt64Instr(r2)
    //     0xe94340: sbfiz           x0, x2, #1, #0x1f
    //     0xe94344: cmp             x2, x0, asr #1
    //     0xe94348: b.eq            #0xe94354
    //     0xe9434c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe94350: stur            x2, [x0, #7]
    // 0xe94354: stp             x0, NULL, [SP]
    // 0xe94358: r0 = _Double.fromInteger()
    //     0xe94358: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe9435c: ldur            x1, [fp, #-8]
    // 0xe94360: stur            x0, [fp, #-8]
    // 0xe94364: r0 = unitsPerEm()
    //     0xe94364: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0xe94368: scvtf           d0, x0
    // 0xe9436c: ldur            x0, [fp, #-8]
    // 0xe94370: LoadField: d1 = r0->field_7
    //     0xe94370: ldur            d1, [x0, #7]
    // 0xe94374: fdiv            d2, d1, d0
    // 0xe94378: mov             v0.16b, v2.16b
    // 0xe9437c: ldur            x0, [fp, #-0x60]
    // 0xe94380: r17 = -312
    //     0xe94380: movn            x17, #0x137
    // 0xe94384: str             d0, [fp, x17]
    // 0xe94388: cmp             x0, #0x37f
    // 0xe9438c: b.ne            #0xe943a0
    // 0xe94390: ldur            x2, [fp, #-0x68]
    // 0xe94394: LoadField: d1 = r2->field_3b
    //     0xe94394: ldur            d1, [x2, #0x3b]
    // 0xe94398: mov             v2.16b, v1.16b
    // 0xe9439c: b               #0xe943fc
    // 0xe943a0: ldur            x2, [fp, #-0x68]
    // 0xe943a4: LoadField: r3 = r2->field_3f
    //     0xe943a4: ldur            w3, [x2, #0x3f]
    // 0xe943a8: DecompressPointer r3
    //     0xe943a8: add             x3, x3, HEAP, lsl #32
    // 0xe943ac: mov             x1, x3
    // 0xe943b0: stur            x3, [fp, #-8]
    // 0xe943b4: r0 = descent()
    //     0xe943b4: bl              #0xc3820c  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::descent
    // 0xe943b8: mov             x2, x0
    // 0xe943bc: r0 = BoxInt64Instr(r2)
    //     0xe943bc: sbfiz           x0, x2, #1, #0x1f
    //     0xe943c0: cmp             x2, x0, asr #1
    //     0xe943c4: b.eq            #0xe943d0
    //     0xe943c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe943cc: stur            x2, [x0, #7]
    // 0xe943d0: stp             x0, NULL, [SP]
    // 0xe943d4: r0 = _Double.fromInteger()
    //     0xe943d4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0xe943d8: ldur            x1, [fp, #-8]
    // 0xe943dc: stur            x0, [fp, #-8]
    // 0xe943e0: r0 = unitsPerEm()
    //     0xe943e0: bl              #0x7c9fc0  ; [package:pdf/src/pdf/font/ttf_parser.dart] TtfParser::unitsPerEm
    // 0xe943e4: scvtf           d0, x0
    // 0xe943e8: ldur            x0, [fp, #-8]
    // 0xe943ec: LoadField: d1 = r0->field_7
    //     0xe943ec: ldur            d1, [x0, #7]
    // 0xe943f0: fdiv            d2, d1, d0
    // 0xe943f4: r17 = -312
    //     0xe943f4: movn            x17, #0x137
    // 0xe943f8: ldr             d0, [fp, x17]
    // 0xe943fc: ldur            x3, [fp, #-0x58]
    // 0xe94400: ldur            x0, [fp, #-0x98]
    // 0xe94404: r17 = -288
    //     0xe94404: movn            x17, #0x11f
    // 0xe94408: ldr             d1, [fp, x17]
    // 0xe9440c: fneg            d3, d2
    // 0xe94410: fadd            d2, d0, d3
    // 0xe94414: fmul            d0, d2, d1
    // 0xe94418: LoadField: d2 = r0->field_7
    //     0xe94418: ldur            d2, [x0, #7]
    // 0xe9441c: fadd            d3, d2, d0
    // 0xe94420: r0 = inline_Allocate_Double()
    //     0xe94420: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe94424: add             x0, x0, #0x10
    //     0xe94428: cmp             x1, x0
    //     0xe9442c: b.ls            #0xe95138
    //     0xe94430: str             x0, [THR, #0x50]  ; THR::top
    //     0xe94434: sub             x0, x0, #0xf
    //     0xe94438: movz            x1, #0xe15c
    //     0xe9443c: movk            x1, #0x3, lsl #16
    //     0xe94440: stur            x1, [x0, #-1]
    // 0xe94444: StoreField: r0->field_7 = d3
    //     0xe94444: stur            d3, [x0, #7]
    // 0xe94448: StoreField: r3->field_2b = r0
    //     0xe94448: stur            w0, [x3, #0x2b]
    //     0xe9444c: ldurb           w16, [x3, #-1]
    //     0xe94450: ldurb           w17, [x0, #-1]
    //     0xe94454: and             x16, x17, x16, lsr #2
    //     0xe94458: tst             x16, HEAP, lsr #32
    //     0xe9445c: b.eq            #0xe94464
    //     0xe94460: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe94464: mov             v2.16b, v3.16b
    // 0xe94468: r4 = 0.000000
    //     0xe94468: ldr             x4, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe9446c: ldur            d0, [fp, #-0xf0]
    // 0xe94470: StoreField: r3->field_2f = r4
    //     0xe94470: stur            w4, [x3, #0x2f]
    // 0xe94474: StoreField: r3->field_33 = r4
    //     0xe94474: stur            w4, [x3, #0x33]
    // 0xe94478: StoreField: r3->field_3b = rZR
    //     0xe94478: stur            wzr, [x3, #0x3b]
    // 0xe9447c: fcmp            d2, d0
    // 0xe94480: b.gt            #0xe944e0
    // 0xe94484: ldur            x1, [fp, #-0x70]
    // 0xe94488: cmp             w1, NULL
    // 0xe9448c: b.eq            #0xe95150
    // 0xe94490: LoadField: d3 = r1->field_7
    //     0xe94490: ldur            d3, [x1, #7]
    // 0xe94494: fadd            d4, d2, d3
    // 0xe94498: r0 = inline_Allocate_Double()
    //     0xe94498: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe9449c: add             x0, x0, #0x10
    //     0xe944a0: cmp             x2, x0
    //     0xe944a4: b.ls            #0xe95154
    //     0xe944a8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe944ac: sub             x0, x0, #0xf
    //     0xe944b0: movz            x2, #0xe15c
    //     0xe944b4: movk            x2, #0x3, lsl #16
    //     0xe944b8: stur            x2, [x0, #-1]
    // 0xe944bc: StoreField: r0->field_7 = d4
    //     0xe944bc: stur            d4, [x0, #7]
    // 0xe944c0: StoreField: r3->field_2b = r0
    //     0xe944c0: stur            w0, [x3, #0x2b]
    //     0xe944c4: ldurb           w16, [x3, #-1]
    //     0xe944c8: ldurb           w17, [x0, #-1]
    //     0xe944cc: and             x16, x17, x16, lsr #2
    //     0xe944d0: tst             x16, HEAP, lsr #32
    //     0xe944d4: b.eq            #0xe944dc
    //     0xe944d8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe944dc: b               #0xe94508
    // 0xe944e0: r0 = Null
    //     0xe944e0: mov             x0, NULL
    // 0xe944e4: LeaveFrame
    //     0xe944e4: mov             SP, fp
    //     0xe944e8: ldp             fp, lr, [SP], #0x10
    // 0xe944ec: ret
    //     0xe944ec: ret             
    // 0xe944f0: mov             x3, x1
    // 0xe944f4: ldur            x1, [fp, #-0x70]
    // 0xe944f8: r17 = -288
    //     0xe944f8: movn            x17, #0x11f
    // 0xe944fc: ldr             d1, [fp, x17]
    // 0xe94500: ldur            d0, [fp, #-0xf0]
    // 0xe94504: r4 = 0.000000
    //     0xe94504: ldr             x4, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe94508: ldur            x0, [fp, #-0x78]
    // 0xe9450c: add             x10, x0, #1
    // 0xe94510: mov             x7, x3
    // 0xe94514: ldur            x5, [fp, #-0x10]
    // 0xe94518: ldur            x0, [fp, #-0x90]
    // 0xe9451c: mov             x3, x1
    // 0xe94520: ldur            x1, [fp, #-0x88]
    // 0xe94524: mov             v6.16b, v0.16b
    // 0xe94528: r17 = -304
    //     0xe94528: movn            x17, #0x12f
    // 0xe9452c: ldr             d0, [fp, x17]
    // 0xe94530: mov             v2.16b, v1.16b
    // 0xe94534: r17 = -296
    //     0xe94534: movn            x17, #0x127
    // 0xe94538: ldr             d1, [fp, x17]
    // 0xe9453c: ldur            x2, [fp, #-0x80]
    // 0xe94540: b               #0xe93324
    // 0xe94544: mov             x3, x7
    // 0xe94548: mov             x0, x2
    // 0xe9454c: mov             v0.16b, v6.16b
    // 0xe94550: r4 = 0.000000
    //     0xe94550: ldr             x4, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe94554: LoadField: r2 = r3->field_27
    //     0xe94554: ldur            w2, [x3, #0x27]
    // 0xe94558: DecompressPointer r2
    //     0xe94558: add             x2, x2, HEAP, lsl #32
    // 0xe9455c: cmp             w0, NULL
    // 0xe94560: b.eq            #0xe9517c
    // 0xe94564: LoadField: d2 = r0->field_7
    //     0xe94564: ldur            d2, [x0, #7]
    // 0xe94568: fmul            d3, d1, d2
    // 0xe9456c: cmp             w1, NULL
    // 0xe94570: b.eq            #0xe95180
    // 0xe94574: LoadField: d1 = r1->field_7
    //     0xe94574: ldur            d1, [x1, #7]
    // 0xe94578: fsub            d2, d3, d1
    // 0xe9457c: LoadField: d1 = r2->field_7
    //     0xe9457c: ldur            d1, [x2, #7]
    // 0xe94580: fsub            d3, d1, d2
    // 0xe94584: r0 = inline_Allocate_Double()
    //     0xe94584: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe94588: add             x0, x0, #0x10
    //     0xe9458c: cmp             x1, x0
    //     0xe94590: b.ls            #0xe95184
    //     0xe94594: str             x0, [THR, #0x50]  ; THR::top
    //     0xe94598: sub             x0, x0, #0xf
    //     0xe9459c: movz            x1, #0xe15c
    //     0xe945a0: movk            x1, #0x3, lsl #16
    //     0xe945a4: stur            x1, [x0, #-1]
    // 0xe945a8: StoreField: r0->field_7 = d3
    //     0xe945a8: stur            d3, [x0, #7]
    // 0xe945ac: StoreField: r3->field_27 = r0
    //     0xe945ac: stur            w0, [x3, #0x27]
    //     0xe945b0: ldurb           w16, [x3, #-1]
    //     0xe945b4: ldurb           w17, [x0, #-1]
    //     0xe945b8: and             x16, x17, x16, lsr #2
    //     0xe945bc: tst             x16, HEAP, lsr #32
    //     0xe945c0: b.eq            #0xe945c8
    //     0xe945c4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe945c8: mov             x1, x3
    // 0xe945cc: d1 = 0.000000
    //     0xe945cc: eor             v1.16b, v1.16b, v1.16b
    // 0xe945d0: b               #0xe94d88
    // 0xe945d4: mov             x3, x2
    // 0xe945d8: mov             v0.16b, v5.16b
    // 0xe945dc: mov             x2, x9
    // 0xe945e0: r4 = 0.000000
    //     0xe945e0: ldr             x4, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe945e4: cmp             x1, #0x302
    // 0xe945e8: b.ne            #0xe94d80
    // 0xe945ec: ldur            x0, [fp, #-0x10]
    // 0xe945f0: r17 = -280
    //     0xe945f0: movn            x17, #0x117
    // 0xe945f4: ldr             d1, [fp, x17]
    // 0xe945f8: r17 = -272
    //     0xe945f8: movn            x17, #0x10f
    // 0xe945fc: ldr             d2, [fp, x17]
    // 0xe94600: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xe94600: ldur            w1, [x2, #0x17]
    // 0xe94604: DecompressPointer r1
    //     0xe94604: add             x1, x1, HEAP, lsl #32
    // 0xe94608: stur            x1, [fp, #-0x68]
    // 0xe9460c: LoadField: r5 = r3->field_13
    //     0xe9460c: ldur            w5, [x3, #0x13]
    // 0xe94610: DecompressPointer r5
    //     0xe94610: add             x5, x5, HEAP, lsl #32
    // 0xe94614: stur            x5, [fp, #-8]
    // 0xe94618: r0 = BoxConstraints()
    //     0xe94618: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe9461c: StoreField: r0->field_7 = rZR
    //     0xe9461c: stur            xzr, [x0, #7]
    // 0xe94620: r17 = -280
    //     0xe94620: movn            x17, #0x117
    // 0xe94624: ldr             d0, [fp, x17]
    // 0xe94628: StoreField: r0->field_f = d0
    //     0xe94628: stur            d0, [x0, #0xf]
    // 0xe9462c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xe9462c: stur            xzr, [x0, #0x17]
    // 0xe94630: r17 = -272
    //     0xe94630: movn            x17, #0x10f
    // 0xe94634: ldr             d1, [fp, x17]
    // 0xe94638: StoreField: r0->field_1f = d1
    //     0xe94638: stur            d1, [x0, #0x1f]
    // 0xe9463c: ldur            x1, [fp, #-0x68]
    // 0xe94640: ldur            x2, [fp, #-8]
    // 0xe94644: mov             x3, x0
    // 0xe94648: r0 = layout()
    //     0xe94648: bl              #0xe8f294  ; [package:pdf/src/widgets/widget.dart] StatelessWidget::layout
    // 0xe9464c: ldur            x0, [fp, #-0x10]
    // 0xe94650: cmp             w0, NULL
    // 0xe94654: b.eq            #0xe9519c
    // 0xe94658: r0 = _WidgetSpan()
    //     0xe94658: bl              #0xe95278  ; Allocate_WidgetSpanStub -> _WidgetSpan (size=0x14)
    // 0xe9465c: mov             x1, x0
    // 0xe94660: ldur            x0, [fp, #-0x68]
    // 0xe94664: stur            x1, [fp, #-0x88]
    // 0xe94668: StoreField: r1->field_f = r0
    //     0xe94668: stur            w0, [x1, #0xf]
    // 0xe9466c: r2 = Instance_PdfPoint
    //     0xe9466c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xe94670: ldr             x2, [x2, #0x730]
    // 0xe94674: StoreField: r1->field_b = r2
    //     0xe94674: stur            w2, [x1, #0xb]
    // 0xe94678: ldur            x3, [fp, #-0x10]
    // 0xe9467c: StoreField: r1->field_7 = r3
    //     0xe9467c: stur            w3, [x1, #7]
    // 0xe94680: ldur            x4, [fp, #-0x58]
    // 0xe94684: LoadField: r5 = r4->field_27
    //     0xe94684: ldur            w5, [x4, #0x27]
    // 0xe94688: DecompressPointer r5
    //     0xe94688: add             x5, x5, HEAP, lsl #32
    // 0xe9468c: LoadField: r6 = r0->field_7
    //     0xe9468c: ldur            w6, [x0, #7]
    // 0xe94690: DecompressPointer r6
    //     0xe94690: add             x6, x6, HEAP, lsl #32
    // 0xe94694: cmp             w6, NULL
    // 0xe94698: b.eq            #0xe951a0
    // 0xe9469c: ArrayLoad: d0 = r6[0]  ; List_8
    //     0xe9469c: ldur            d0, [x6, #0x17]
    // 0xe946a0: LoadField: d1 = r5->field_7
    //     0xe946a0: ldur            d1, [x5, #7]
    // 0xe946a4: r17 = -288
    //     0xe946a4: movn            x17, #0x11f
    // 0xe946a8: str             d1, [fp, x17]
    // 0xe946ac: fadd            d2, d1, d0
    // 0xe946b0: r17 = -280
    //     0xe946b0: movn            x17, #0x117
    // 0xe946b4: ldr             d0, [fp, x17]
    // 0xe946b8: fcmp            d2, d0
    // 0xe946bc: b.le            #0xe9499c
    // 0xe946c0: LoadField: r6 = r4->field_3b
    //     0xe946c0: ldur            w6, [x4, #0x3b]
    // 0xe946c4: DecompressPointer r6
    //     0xe946c4: add             x6, x6, HEAP, lsl #32
    // 0xe946c8: r7 = LoadInt32Instr(r6)
    //     0xe946c8: sbfx            x7, x6, #1, #0x1f
    //     0xe946cc: tbz             w6, #0, #0xe946d4
    //     0xe946d0: ldur            x7, [x6, #7]
    // 0xe946d4: stur            x7, [fp, #-0x60]
    // 0xe946d8: cmp             x7, #0
    // 0xe946dc: b.le            #0xe94984
    // 0xe946e0: ldur            x5, [fp, #-0x40]
    // 0xe946e4: r6 = true
    //     0xe946e4: add             x6, NULL, #0x20  ; true
    // 0xe946e8: StoreField: r4->field_43 = r6
    //     0xe946e8: stur            w6, [x4, #0x43]
    // 0xe946ec: LoadField: r8 = r4->field_f
    //     0xe946ec: ldur            w8, [x4, #0xf]
    // 0xe946f0: DecompressPointer r8
    //     0xe946f0: add             x8, x8, HEAP, lsl #32
    // 0xe946f4: stur            x8, [fp, #-0x80]
    // 0xe946f8: LoadField: r9 = r4->field_3f
    //     0xe946f8: ldur            w9, [x4, #0x3f]
    // 0xe946fc: DecompressPointer r9
    //     0xe946fc: add             x9, x9, HEAP, lsl #32
    // 0xe94700: stur            x9, [fp, #-0x70]
    // 0xe94704: LoadField: r10 = r4->field_33
    //     0xe94704: ldur            w10, [x4, #0x33]
    // 0xe94708: DecompressPointer r10
    //     0xe94708: add             x10, x10, HEAP, lsl #32
    // 0xe9470c: stur            x10, [fp, #-8]
    // 0xe94710: r0 = _Line()
    //     0xe94710: bl              #0xe91de8  ; Allocate_LineStub -> _Line (size=0x34)
    // 0xe94714: mov             x3, x0
    // 0xe94718: ldur            x0, [fp, #-0x80]
    // 0xe9471c: stur            x3, [fp, #-0x90]
    // 0xe94720: StoreField: r3->field_7 = r0
    //     0xe94720: stur            w0, [x3, #7]
    // 0xe94724: ldur            x0, [fp, #-0x70]
    // 0xe94728: r1 = LoadInt32Instr(r0)
    //     0xe94728: sbfx            x1, x0, #1, #0x1f
    //     0xe9472c: tbz             w0, #0, #0xe94734
    //     0xe94730: ldur            x1, [x0, #7]
    // 0xe94734: StoreField: r3->field_b = r1
    //     0xe94734: stur            x1, [x3, #0xb]
    // 0xe94738: ldur            x0, [fp, #-0x60]
    // 0xe9473c: StoreField: r3->field_13 = r0
    //     0xe9473c: stur            x0, [x3, #0x13]
    // 0xe94740: ldur            x0, [fp, #-8]
    // 0xe94744: LoadField: d0 = r0->field_7
    //     0xe94744: ldur            d0, [x0, #7]
    // 0xe94748: StoreField: r3->field_1b = d0
    //     0xe94748: stur            d0, [x3, #0x1b]
    // 0xe9474c: r17 = -288
    //     0xe9474c: movn            x17, #0x11f
    // 0xe94750: ldr             d0, [fp, x17]
    // 0xe94754: StoreField: r3->field_23 = d0
    //     0xe94754: stur            d0, [x3, #0x23]
    // 0xe94758: r4 = Instance_TextDirection
    //     0xe94758: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e2e0] Obj!TextDirection@e2e5a1
    //     0xe9475c: ldr             x4, [x4, #0x2e0]
    // 0xe94760: StoreField: r3->field_2b = r4
    //     0xe94760: stur            w4, [x3, #0x2b]
    // 0xe94764: r5 = true
    //     0xe94764: add             x5, NULL, #0x20  ; true
    // 0xe94768: StoreField: r3->field_2f = r5
    //     0xe94768: stur            w5, [x3, #0x2f]
    // 0xe9476c: mov             x0, x3
    // 0xe94770: ldur            x2, [fp, #-0x38]
    // 0xe94774: r1 = Null
    //     0xe94774: mov             x1, NULL
    // 0xe94778: cmp             w2, NULL
    // 0xe9477c: b.eq            #0xe9479c
    // 0xe94780: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe94780: ldur            w4, [x2, #0x17]
    // 0xe94784: DecompressPointer r4
    //     0xe94784: add             x4, x4, HEAP, lsl #32
    // 0xe94788: r8 = X0
    //     0xe94788: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe9478c: LoadField: r9 = r4->field_7
    //     0xe9478c: ldur            x9, [x4, #7]
    // 0xe94790: r3 = Null
    //     0xe94790: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e168] Null
    //     0xe94794: ldr             x3, [x3, #0x168]
    // 0xe94798: blr             x9
    // 0xe9479c: ldur            x0, [fp, #-0x40]
    // 0xe947a0: LoadField: r1 = r0->field_b
    //     0xe947a0: ldur            w1, [x0, #0xb]
    // 0xe947a4: LoadField: r2 = r0->field_f
    //     0xe947a4: ldur            w2, [x0, #0xf]
    // 0xe947a8: DecompressPointer r2
    //     0xe947a8: add             x2, x2, HEAP, lsl #32
    // 0xe947ac: LoadField: r3 = r2->field_b
    //     0xe947ac: ldur            w3, [x2, #0xb]
    // 0xe947b0: r2 = LoadInt32Instr(r1)
    //     0xe947b0: sbfx            x2, x1, #1, #0x1f
    // 0xe947b4: stur            x2, [fp, #-0x60]
    // 0xe947b8: r1 = LoadInt32Instr(r3)
    //     0xe947b8: sbfx            x1, x3, #1, #0x1f
    // 0xe947bc: cmp             x2, x1
    // 0xe947c0: b.ne            #0xe947cc
    // 0xe947c4: mov             x1, x0
    // 0xe947c8: r0 = _growToNextCapacity()
    //     0xe947c8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe947cc: ldur            x4, [fp, #-0x58]
    // 0xe947d0: ldur            x2, [fp, #-0x40]
    // 0xe947d4: r17 = -272
    //     0xe947d4: movn            x17, #0x10f
    // 0xe947d8: ldr             d0, [fp, x17]
    // 0xe947dc: ldur            x3, [fp, #-0x60]
    // 0xe947e0: r6 = 0.000000
    //     0xe947e0: ldr             x6, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe947e4: add             x0, x3, #1
    // 0xe947e8: lsl             x1, x0, #1
    // 0xe947ec: StoreField: r2->field_b = r1
    //     0xe947ec: stur            w1, [x2, #0xb]
    // 0xe947f0: LoadField: r1 = r2->field_f
    //     0xe947f0: ldur            w1, [x2, #0xf]
    // 0xe947f4: DecompressPointer r1
    //     0xe947f4: add             x1, x1, HEAP, lsl #32
    // 0xe947f8: ldur            x0, [fp, #-0x90]
    // 0xe947fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe947fc: add             x25, x1, x3, lsl #2
    //     0xe94800: add             x25, x25, #0xf
    //     0xe94804: str             w0, [x25]
    //     0xe94808: tbz             w0, #0, #0xe94824
    //     0xe9480c: ldurb           w16, [x1, #-1]
    //     0xe94810: ldurb           w17, [x0, #-1]
    //     0xe94814: and             x16, x17, x16, lsr #2
    //     0xe94818: tst             x16, HEAP, lsr #32
    //     0xe9481c: b.eq            #0xe94824
    //     0xe94820: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe94824: LoadField: r0 = r4->field_3f
    //     0xe94824: ldur            w0, [x4, #0x3f]
    // 0xe94828: DecompressPointer r0
    //     0xe94828: add             x0, x0, HEAP, lsl #32
    // 0xe9482c: LoadField: r1 = r4->field_3b
    //     0xe9482c: ldur            w1, [x4, #0x3b]
    // 0xe94830: DecompressPointer r1
    //     0xe94830: add             x1, x1, HEAP, lsl #32
    // 0xe94834: r3 = LoadInt32Instr(r0)
    //     0xe94834: sbfx            x3, x0, #1, #0x1f
    //     0xe94838: tbz             w0, #0, #0xe94840
    //     0xe9483c: ldur            x3, [x0, #7]
    // 0xe94840: r0 = LoadInt32Instr(r1)
    //     0xe94840: sbfx            x0, x1, #1, #0x1f
    //     0xe94844: tbz             w1, #0, #0xe9484c
    //     0xe94848: ldur            x0, [x1, #7]
    // 0xe9484c: add             x5, x3, x0
    // 0xe94850: r0 = BoxInt64Instr(r5)
    //     0xe94850: sbfiz           x0, x5, #1, #0x1f
    //     0xe94854: cmp             x5, x0, asr #1
    //     0xe94858: b.eq            #0xe94864
    //     0xe9485c: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe94860: stur            x5, [x0, #7]
    // 0xe94864: StoreField: r4->field_3f = r0
    //     0xe94864: stur            w0, [x4, #0x3f]
    //     0xe94868: tbz             w0, #0, #0xe94884
    //     0xe9486c: ldurb           w16, [x4, #-1]
    //     0xe94870: ldurb           w17, [x0, #-1]
    //     0xe94874: and             x16, x17, x16, lsr #2
    //     0xe94878: tst             x16, HEAP, lsr #32
    //     0xe9487c: b.eq            #0xe94884
    //     0xe94880: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe94884: StoreField: r4->field_3b = rZR
    //     0xe94884: stur            wzr, [x4, #0x3b]
    // 0xe94888: StoreField: r4->field_27 = r6
    //     0xe94888: stur            w6, [x4, #0x27]
    // 0xe9488c: LoadField: r0 = r4->field_2b
    //     0xe9488c: ldur            w0, [x4, #0x2b]
    // 0xe94890: DecompressPointer r0
    //     0xe94890: add             x0, x0, HEAP, lsl #32
    // 0xe94894: LoadField: r1 = r4->field_33
    //     0xe94894: ldur            w1, [x4, #0x33]
    // 0xe94898: DecompressPointer r1
    //     0xe94898: add             x1, x1, HEAP, lsl #32
    // 0xe9489c: LoadField: r3 = r4->field_2f
    //     0xe9489c: ldur            w3, [x4, #0x2f]
    // 0xe948a0: DecompressPointer r3
    //     0xe948a0: add             x3, x3, HEAP, lsl #32
    // 0xe948a4: LoadField: d1 = r1->field_7
    //     0xe948a4: ldur            d1, [x1, #7]
    // 0xe948a8: LoadField: d2 = r3->field_7
    //     0xe948a8: ldur            d2, [x3, #7]
    // 0xe948ac: fsub            d3, d1, d2
    // 0xe948b0: LoadField: d1 = r0->field_7
    //     0xe948b0: ldur            d1, [x0, #7]
    // 0xe948b4: fadd            d2, d1, d3
    // 0xe948b8: r0 = inline_Allocate_Double()
    //     0xe948b8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe948bc: add             x0, x0, #0x10
    //     0xe948c0: cmp             x1, x0
    //     0xe948c4: b.ls            #0xe951a4
    //     0xe948c8: str             x0, [THR, #0x50]  ; THR::top
    //     0xe948cc: sub             x0, x0, #0xf
    //     0xe948d0: movz            x1, #0xe15c
    //     0xe948d4: movk            x1, #0x3, lsl #16
    //     0xe948d8: stur            x1, [x0, #-1]
    // 0xe948dc: StoreField: r0->field_7 = d2
    //     0xe948dc: stur            d2, [x0, #7]
    // 0xe948e0: StoreField: r4->field_2b = r0
    //     0xe948e0: stur            w0, [x4, #0x2b]
    //     0xe948e4: ldurb           w16, [x4, #-1]
    //     0xe948e8: ldurb           w17, [x0, #-1]
    //     0xe948ec: and             x16, x17, x16, lsr #2
    //     0xe948f0: tst             x16, HEAP, lsr #32
    //     0xe948f4: b.eq            #0xe948fc
    //     0xe948f8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe948fc: StoreField: r4->field_2f = r6
    //     0xe948fc: stur            w6, [x4, #0x2f]
    // 0xe94900: StoreField: r4->field_33 = r6
    //     0xe94900: stur            w6, [x4, #0x33]
    // 0xe94904: fcmp            d2, d0
    // 0xe94908: b.gt            #0xe94974
    // 0xe9490c: ldur            x1, [fp, #-0x10]
    // 0xe94910: LoadField: r0 = r1->field_33
    //     0xe94910: ldur            w0, [x1, #0x33]
    // 0xe94914: DecompressPointer r0
    //     0xe94914: add             x0, x0, HEAP, lsl #32
    // 0xe94918: cmp             w0, NULL
    // 0xe9491c: b.eq            #0xe951c4
    // 0xe94920: LoadField: d1 = r0->field_7
    //     0xe94920: ldur            d1, [x0, #7]
    // 0xe94924: fadd            d3, d2, d1
    // 0xe94928: r0 = inline_Allocate_Double()
    //     0xe94928: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xe9492c: add             x0, x0, #0x10
    //     0xe94930: cmp             x3, x0
    //     0xe94934: b.ls            #0xe951c8
    //     0xe94938: str             x0, [THR, #0x50]  ; THR::top
    //     0xe9493c: sub             x0, x0, #0xf
    //     0xe94940: movz            x3, #0xe15c
    //     0xe94944: movk            x3, #0x3, lsl #16
    //     0xe94948: stur            x3, [x0, #-1]
    // 0xe9494c: StoreField: r0->field_7 = d3
    //     0xe9494c: stur            d3, [x0, #7]
    // 0xe94950: StoreField: r4->field_2b = r0
    //     0xe94950: stur            w0, [x4, #0x2b]
    //     0xe94954: ldurb           w16, [x4, #-1]
    //     0xe94958: ldurb           w17, [x0, #-1]
    //     0xe9495c: and             x16, x17, x16, lsr #2
    //     0xe94960: tst             x16, HEAP, lsr #32
    //     0xe94964: b.eq            #0xe9496c
    //     0xe94968: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe9496c: d1 = 0.000000
    //     0xe9496c: eor             v1.16b, v1.16b, v1.16b
    // 0xe94970: b               #0xe949b4
    // 0xe94974: r0 = Null
    //     0xe94974: mov             x0, NULL
    // 0xe94978: LeaveFrame
    //     0xe94978: mov             SP, fp
    //     0xe9497c: ldp             fp, lr, [SP], #0x10
    // 0xe94980: ret
    //     0xe94980: ret             
    // 0xe94984: mov             x1, x3
    // 0xe94988: ldur            x2, [fp, #-0x40]
    // 0xe9498c: r17 = -272
    //     0xe9498c: movn            x17, #0x10f
    // 0xe94990: ldr             d0, [fp, x17]
    // 0xe94994: r6 = 0.000000
    //     0xe94994: ldr             x6, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe94998: b               #0xe949b0
    // 0xe9499c: mov             x1, x3
    // 0xe949a0: ldur            x2, [fp, #-0x40]
    // 0xe949a4: r17 = -272
    //     0xe949a4: movn            x17, #0x10f
    // 0xe949a8: ldr             d0, [fp, x17]
    // 0xe949ac: r6 = 0.000000
    //     0xe949ac: ldr             x6, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xe949b0: LoadField: d1 = r5->field_7
    //     0xe949b0: ldur            d1, [x5, #7]
    // 0xe949b4: ldur            x0, [fp, #-0x20]
    // 0xe949b8: r17 = -296
    //     0xe949b8: movn            x17, #0x127
    // 0xe949bc: str             d1, [fp, x17]
    // 0xe949c0: LoadField: d2 = r0->field_b
    //     0xe949c0: ldur            d2, [x0, #0xb]
    // 0xe949c4: LoadField: r0 = r4->field_2f
    //     0xe949c4: ldur            w0, [x4, #0x2f]
    // 0xe949c8: DecompressPointer r0
    //     0xe949c8: add             x0, x0, HEAP, lsl #32
    // 0xe949cc: LoadField: d3 = r0->field_7
    //     0xe949cc: ldur            d3, [x0, #7]
    // 0xe949d0: fcmp            d3, d2
    // 0xe949d4: b.le            #0xe949e4
    // 0xe949d8: mov             v3.16b, v2.16b
    // 0xe949dc: d4 = 0.000000
    //     0xe949dc: eor             v4.16b, v4.16b, v4.16b
    // 0xe949e0: b               #0xe94a48
    // 0xe949e4: fcmp            d2, d3
    // 0xe949e8: b.le            #0xe949f8
    // 0xe949ec: LoadField: d3 = r0->field_7
    //     0xe949ec: ldur            d3, [x0, #7]
    // 0xe949f0: d4 = 0.000000
    //     0xe949f0: eor             v4.16b, v4.16b, v4.16b
    // 0xe949f4: b               #0xe94a48
    // 0xe949f8: d4 = 0.000000
    //     0xe949f8: eor             v4.16b, v4.16b, v4.16b
    // 0xe949fc: fcmp            d3, d4
    // 0xe94a00: b.ne            #0xe94a14
    // 0xe94a04: fadd            d5, d3, d2
    // 0xe94a08: fmul            d6, d5, d3
    // 0xe94a0c: fmul            d3, d6, d2
    // 0xe94a10: b               #0xe94a48
    // 0xe94a14: fcmp            d3, d4
    // 0xe94a18: b.ne            #0xe94a34
    // 0xe94a1c: fcmp            d2, #0.0
    // 0xe94a20: b.vs            #0xe94a34
    // 0xe94a24: b.ne            #0xe94a30
    // 0xe94a28: r3 = 0.000000
    //     0xe94a28: fmov            x3, d2
    // 0xe94a2c: cmp             x3, #0
    // 0xe94a30: b.lt            #0xe94a3c
    // 0xe94a34: fcmp            d2, d2
    // 0xe94a38: b.vc            #0xe94a44
    // 0xe94a3c: mov             v3.16b, v2.16b
    // 0xe94a40: b               #0xe94a48
    // 0xe94a44: LoadField: d3 = r0->field_7
    //     0xe94a44: ldur            d3, [x0, #7]
    // 0xe94a48: ldur            x3, [fp, #-0x68]
    // 0xe94a4c: r0 = inline_Allocate_Double()
    //     0xe94a4c: ldp             x0, x5, [THR, #0x50]  ; THR::top
    //     0xe94a50: add             x0, x0, #0x10
    //     0xe94a54: cmp             x5, x0
    //     0xe94a58: b.ls            #0xe951e8
    //     0xe94a5c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe94a60: sub             x0, x0, #0xf
    //     0xe94a64: movz            x5, #0xe15c
    //     0xe94a68: movk            x5, #0x3, lsl #16
    //     0xe94a6c: stur            x5, [x0, #-1]
    // 0xe94a70: StoreField: r0->field_7 = d3
    //     0xe94a70: stur            d3, [x0, #7]
    // 0xe94a74: StoreField: r4->field_2f = r0
    //     0xe94a74: stur            w0, [x4, #0x2f]
    //     0xe94a78: ldurb           w16, [x4, #-1]
    //     0xe94a7c: ldurb           w17, [x0, #-1]
    //     0xe94a80: and             x16, x17, x16, lsr #2
    //     0xe94a84: tst             x16, HEAP, lsr #32
    //     0xe94a88: b.eq            #0xe94a90
    //     0xe94a8c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe94a90: LoadField: r0 = r4->field_33
    //     0xe94a90: ldur            w0, [x4, #0x33]
    // 0xe94a94: DecompressPointer r0
    //     0xe94a94: add             x0, x0, HEAP, lsl #32
    // 0xe94a98: LoadField: r5 = r3->field_7
    //     0xe94a98: ldur            w5, [x3, #7]
    // 0xe94a9c: DecompressPointer r5
    //     0xe94a9c: add             x5, x5, HEAP, lsl #32
    // 0xe94aa0: cmp             w5, NULL
    // 0xe94aa4: b.eq            #0xe95220
    // 0xe94aa8: LoadField: d3 = r5->field_1f
    //     0xe94aa8: ldur            d3, [x5, #0x1f]
    // 0xe94aac: fadd            d5, d3, d2
    // 0xe94ab0: LoadField: d3 = r0->field_7
    //     0xe94ab0: ldur            d3, [x0, #7]
    // 0xe94ab4: fcmp            d3, d5
    // 0xe94ab8: b.le            #0xe94ac4
    // 0xe94abc: LoadField: d3 = r0->field_7
    //     0xe94abc: ldur            d3, [x0, #7]
    // 0xe94ac0: b               #0xe94afc
    // 0xe94ac4: fcmp            d5, d3
    // 0xe94ac8: b.le            #0xe94ad4
    // 0xe94acc: mov             v3.16b, v5.16b
    // 0xe94ad0: b               #0xe94afc
    // 0xe94ad4: fcmp            d3, d4
    // 0xe94ad8: b.ne            #0xe94ae8
    // 0xe94adc: fadd            d6, d3, d5
    // 0xe94ae0: mov             v3.16b, v6.16b
    // 0xe94ae4: b               #0xe94afc
    // 0xe94ae8: fcmp            d5, d5
    // 0xe94aec: b.vc            #0xe94af8
    // 0xe94af0: mov             v3.16b, v5.16b
    // 0xe94af4: b               #0xe94afc
    // 0xe94af8: LoadField: d3 = r0->field_7
    //     0xe94af8: ldur            d3, [x0, #7]
    // 0xe94afc: r0 = inline_Allocate_Double()
    //     0xe94afc: ldp             x0, x5, [THR, #0x50]  ; THR::top
    //     0xe94b00: add             x0, x0, #0x10
    //     0xe94b04: cmp             x5, x0
    //     0xe94b08: b.ls            #0xe95224
    //     0xe94b0c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe94b10: sub             x0, x0, #0xf
    //     0xe94b14: movz            x5, #0xe15c
    //     0xe94b18: movk            x5, #0x3, lsl #16
    //     0xe94b1c: stur            x5, [x0, #-1]
    // 0xe94b20: StoreField: r0->field_7 = d3
    //     0xe94b20: stur            d3, [x0, #7]
    // 0xe94b24: StoreField: r4->field_33 = r0
    //     0xe94b24: stur            w0, [x4, #0x33]
    //     0xe94b28: ldurb           w16, [x4, #-1]
    //     0xe94b2c: ldurb           w17, [x0, #-1]
    //     0xe94b30: and             x16, x17, x16, lsr #2
    //     0xe94b34: tst             x16, HEAP, lsr #32
    //     0xe94b38: b.eq            #0xe94b40
    //     0xe94b3c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe94b40: LoadField: r0 = r4->field_2b
    //     0xe94b40: ldur            w0, [x4, #0x2b]
    // 0xe94b44: DecompressPointer r0
    //     0xe94b44: add             x0, x0, HEAP, lsl #32
    // 0xe94b48: LoadField: d3 = r0->field_7
    //     0xe94b48: ldur            d3, [x0, #7]
    // 0xe94b4c: fneg            d5, d3
    // 0xe94b50: fadd            d3, d5, d2
    // 0xe94b54: r17 = -288
    //     0xe94b54: movn            x17, #0x11f
    // 0xe94b58: str             d3, [fp, x17]
    // 0xe94b5c: r0 = PdfPoint()
    //     0xe94b5c: bl              #0xc3adec  ; AllocatePdfPointStub -> PdfPoint (size=0x18)
    // 0xe94b60: r17 = -296
    //     0xe94b60: movn            x17, #0x127
    // 0xe94b64: ldr             d0, [fp, x17]
    // 0xe94b68: StoreField: r0->field_7 = d0
    //     0xe94b68: stur            d0, [x0, #7]
    // 0xe94b6c: r17 = -288
    //     0xe94b6c: movn            x17, #0x11f
    // 0xe94b70: ldr             d0, [fp, x17]
    // 0xe94b74: StoreField: r0->field_f = d0
    //     0xe94b74: stur            d0, [x0, #0xf]
    // 0xe94b78: ldur            x1, [fp, #-0x88]
    // 0xe94b7c: mov             x2, x0
    // 0xe94b80: r0 = offset=()
    //     0xe94b80: bl              #0xeafde0  ; [package:pdf/src/widgets/text.dart] _WidgetSpan::offset=
    // 0xe94b84: ldur            x3, [fp, #-0x58]
    // 0xe94b88: LoadField: r0 = r3->field_f
    //     0xe94b88: ldur            w0, [x3, #0xf]
    // 0xe94b8c: DecompressPointer r0
    //     0xe94b8c: add             x0, x0, HEAP, lsl #32
    // 0xe94b90: LoadField: r4 = r0->field_2f
    //     0xe94b90: ldur            w4, [x0, #0x2f]
    // 0xe94b94: DecompressPointer r4
    //     0xe94b94: add             x4, x4, HEAP, lsl #32
    // 0xe94b98: stur            x4, [fp, #-8]
    // 0xe94b9c: LoadField: r2 = r4->field_7
    //     0xe94b9c: ldur            w2, [x4, #7]
    // 0xe94ba0: DecompressPointer r2
    //     0xe94ba0: add             x2, x2, HEAP, lsl #32
    // 0xe94ba4: ldur            x0, [fp, #-0x88]
    // 0xe94ba8: r1 = Null
    //     0xe94ba8: mov             x1, NULL
    // 0xe94bac: cmp             w2, NULL
    // 0xe94bb0: b.eq            #0xe94bd0
    // 0xe94bb4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe94bb4: ldur            w4, [x2, #0x17]
    // 0xe94bb8: DecompressPointer r4
    //     0xe94bb8: add             x4, x4, HEAP, lsl #32
    // 0xe94bbc: r8 = X0
    //     0xe94bbc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe94bc0: LoadField: r9 = r4->field_7
    //     0xe94bc0: ldur            x9, [x4, #7]
    // 0xe94bc4: r3 = Null
    //     0xe94bc4: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e178] Null
    //     0xe94bc8: ldr             x3, [x3, #0x178]
    // 0xe94bcc: blr             x9
    // 0xe94bd0: ldur            x0, [fp, #-8]
    // 0xe94bd4: LoadField: r1 = r0->field_b
    //     0xe94bd4: ldur            w1, [x0, #0xb]
    // 0xe94bd8: LoadField: r2 = r0->field_f
    //     0xe94bd8: ldur            w2, [x0, #0xf]
    // 0xe94bdc: DecompressPointer r2
    //     0xe94bdc: add             x2, x2, HEAP, lsl #32
    // 0xe94be0: LoadField: r3 = r2->field_b
    //     0xe94be0: ldur            w3, [x2, #0xb]
    // 0xe94be4: r2 = LoadInt32Instr(r1)
    //     0xe94be4: sbfx            x2, x1, #1, #0x1f
    // 0xe94be8: stur            x2, [fp, #-0x60]
    // 0xe94bec: r1 = LoadInt32Instr(r3)
    //     0xe94bec: sbfx            x1, x3, #1, #0x1f
    // 0xe94bf0: cmp             x2, x1
    // 0xe94bf4: b.ne            #0xe94c00
    // 0xe94bf8: mov             x1, x0
    // 0xe94bfc: r0 = _growToNextCapacity()
    //     0xe94bfc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe94c00: ldur            x3, [fp, #-0x58]
    // 0xe94c04: ldur            x4, [fp, #-0x10]
    // 0xe94c08: ldur            x5, [fp, #-0x68]
    // 0xe94c0c: ldur            x0, [fp, #-8]
    // 0xe94c10: ldur            x2, [fp, #-0x60]
    // 0xe94c14: add             x1, x2, #1
    // 0xe94c18: lsl             x6, x1, #1
    // 0xe94c1c: StoreField: r0->field_b = r6
    //     0xe94c1c: stur            w6, [x0, #0xb]
    // 0xe94c20: LoadField: r1 = r0->field_f
    //     0xe94c20: ldur            w1, [x0, #0xf]
    // 0xe94c24: DecompressPointer r1
    //     0xe94c24: add             x1, x1, HEAP, lsl #32
    // 0xe94c28: ldur            x0, [fp, #-0x88]
    // 0xe94c2c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe94c2c: add             x25, x1, x2, lsl #2
    //     0xe94c30: add             x25, x25, #0xf
    //     0xe94c34: str             w0, [x25]
    //     0xe94c38: tbz             w0, #0, #0xe94c54
    //     0xe94c3c: ldurb           w16, [x1, #-1]
    //     0xe94c40: ldurb           w17, [x0, #-1]
    //     0xe94c44: and             x16, x17, x16, lsr #2
    //     0xe94c48: tst             x16, HEAP, lsr #32
    //     0xe94c4c: b.eq            #0xe94c54
    //     0xe94c50: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe94c54: LoadField: r0 = r3->field_3b
    //     0xe94c54: ldur            w0, [x3, #0x3b]
    // 0xe94c58: DecompressPointer r0
    //     0xe94c58: add             x0, x0, HEAP, lsl #32
    // 0xe94c5c: r1 = LoadInt32Instr(r0)
    //     0xe94c5c: sbfx            x1, x0, #1, #0x1f
    //     0xe94c60: tbz             w0, #0, #0xe94c68
    //     0xe94c64: ldur            x1, [x0, #7]
    // 0xe94c68: add             x2, x1, #1
    // 0xe94c6c: r0 = BoxInt64Instr(r2)
    //     0xe94c6c: sbfiz           x0, x2, #1, #0x1f
    //     0xe94c70: cmp             x2, x0, asr #1
    //     0xe94c74: b.eq            #0xe94c80
    //     0xe94c78: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe94c7c: stur            x2, [x0, #7]
    // 0xe94c80: StoreField: r3->field_3b = r0
    //     0xe94c80: stur            w0, [x3, #0x3b]
    //     0xe94c84: tbz             w0, #0, #0xe94ca0
    //     0xe94c88: ldurb           w16, [x3, #-1]
    //     0xe94c8c: ldurb           w17, [x0, #-1]
    //     0xe94c90: and             x16, x17, x16, lsr #2
    //     0xe94c94: tst             x16, HEAP, lsr #32
    //     0xe94c98: b.eq            #0xe94ca0
    //     0xe94c9c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe94ca0: LoadField: r1 = r3->field_f
    //     0xe94ca0: ldur            w1, [x3, #0xf]
    // 0xe94ca4: DecompressPointer r1
    //     0xe94ca4: add             x1, x1, HEAP, lsl #32
    // 0xe94ca8: stur            x1, [fp, #-0x20]
    // 0xe94cac: cmp             x2, #1
    // 0xe94cb0: r16 = true
    //     0xe94cb0: add             x16, NULL, #0x20  ; true
    // 0xe94cb4: r17 = false
    //     0xe94cb4: add             x17, NULL, #0x30  ; false
    // 0xe94cb8: csel            x0, x16, x17, gt
    // 0xe94cbc: stur            x0, [fp, #-8]
    // 0xe94cc0: LoadField: r2 = r1->field_2f
    //     0xe94cc0: ldur            w2, [x1, #0x2f]
    // 0xe94cc4: DecompressPointer r2
    //     0xe94cc4: add             x2, x2, HEAP, lsl #32
    // 0xe94cc8: LoadField: r6 = r2->field_b
    //     0xe94cc8: ldur            w6, [x2, #0xb]
    // 0xe94ccc: r2 = LoadInt32Instr(r6)
    //     0xe94ccc: sbfx            x2, x6, #1, #0x1f
    // 0xe94cd0: sub             x6, x2, #1
    // 0xe94cd4: stur            x6, [fp, #-0x60]
    // 0xe94cd8: r0 = _TextDecoration()
    //     0xe94cd8: bl              #0xe954dc  ; Allocate_TextDecorationStub -> _TextDecoration (size=0x24)
    // 0xe94cdc: mov             x1, x0
    // 0xe94ce0: ldur            x0, [fp, #-0x10]
    // 0xe94ce4: StoreField: r1->field_7 = r0
    //     0xe94ce4: stur            w0, [x1, #7]
    // 0xe94ce8: ldur            x0, [fp, #-0x60]
    // 0xe94cec: StoreField: r1->field_f = r0
    //     0xe94cec: stur            x0, [x1, #0xf]
    // 0xe94cf0: ArrayStore: r1[0] = r0  ; List_8
    //     0xe94cf0: stur            x0, [x1, #0x17]
    // 0xe94cf4: mov             x3, x1
    // 0xe94cf8: ldur            x1, [fp, #-0x20]
    // 0xe94cfc: ldur            x2, [fp, #-8]
    // 0xe94d00: r0 = _appendDecoration()
    //     0xe94d00: bl              #0xe95284  ; [package:pdf/src/widgets/text.dart] RichText::_appendDecoration
    // 0xe94d04: ldur            x1, [fp, #-0x58]
    // 0xe94d08: LoadField: r0 = r1->field_27
    //     0xe94d08: ldur            w0, [x1, #0x27]
    // 0xe94d0c: DecompressPointer r0
    //     0xe94d0c: add             x0, x0, HEAP, lsl #32
    // 0xe94d10: ldur            x2, [fp, #-0x68]
    // 0xe94d14: LoadField: r3 = r2->field_7
    //     0xe94d14: ldur            w3, [x2, #7]
    // 0xe94d18: DecompressPointer r3
    //     0xe94d18: add             x3, x3, HEAP, lsl #32
    // 0xe94d1c: cmp             w3, NULL
    // 0xe94d20: b.eq            #0xe9525c
    // 0xe94d24: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xe94d24: ldur            d0, [x3, #0x17]
    // 0xe94d28: d1 = 0.000000
    //     0xe94d28: eor             v1.16b, v1.16b, v1.16b
    // 0xe94d2c: fadd            d2, d0, d1
    // 0xe94d30: LoadField: d0 = r0->field_7
    //     0xe94d30: ldur            d0, [x0, #7]
    // 0xe94d34: fadd            d3, d0, d2
    // 0xe94d38: r0 = inline_Allocate_Double()
    //     0xe94d38: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe94d3c: add             x0, x0, #0x10
    //     0xe94d40: cmp             x2, x0
    //     0xe94d44: b.ls            #0xe95260
    //     0xe94d48: str             x0, [THR, #0x50]  ; THR::top
    //     0xe94d4c: sub             x0, x0, #0xf
    //     0xe94d50: movz            x2, #0xe15c
    //     0xe94d54: movk            x2, #0x3, lsl #16
    //     0xe94d58: stur            x2, [x0, #-1]
    // 0xe94d5c: StoreField: r0->field_7 = d3
    //     0xe94d5c: stur            d3, [x0, #7]
    // 0xe94d60: StoreField: r1->field_27 = r0
    //     0xe94d60: stur            w0, [x1, #0x27]
    //     0xe94d64: ldurb           w16, [x1, #-1]
    //     0xe94d68: ldurb           w17, [x0, #-1]
    //     0xe94d6c: and             x16, x17, x16, lsr #2
    //     0xe94d70: tst             x16, HEAP, lsr #32
    //     0xe94d74: b.eq            #0xe94d7c
    //     0xe94d78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe94d7c: b               #0xe94d88
    // 0xe94d80: mov             x1, x3
    // 0xe94d84: d1 = 0.000000
    //     0xe94d84: eor             v1.16b, v1.16b, v1.16b
    // 0xe94d88: ldur            x0, [fp, #-0x18]
    // 0xe94d8c: mov             x2, x1
    // 0xe94d90: ldur            x3, [fp, #-0x50]
    // 0xe94d94: ldur            x5, [fp, #-0x40]
    // 0xe94d98: ldur            x7, [fp, #-0x30]
    // 0xe94d9c: ldur            x8, [fp, #-0x28]
    // 0xe94da0: ldur            d4, [fp, #-0x100]
    // 0xe94da4: ldur            x6, [fp, #-0x38]
    // 0xe94da8: ldur            x4, [fp, #-0x48]
    // 0xe94dac: r17 = -264
    //     0xe94dac: movn            x17, #0x107
    // 0xe94db0: ldr             d3, [fp, x17]
    // 0xe94db4: ldur            d0, [fp, #-0xf8]
    // 0xe94db8: ldur            d5, [fp, #-0xf0]
    // 0xe94dbc: r17 = -280
    //     0xe94dbc: movn            x17, #0x117
    // 0xe94dc0: ldr             d1, [fp, x17]
    // 0xe94dc4: r17 = -272
    //     0xe94dc4: movn            x17, #0x10f
    // 0xe94dc8: ldr             d2, [fp, x17]
    // 0xe94dcc: b               #0xe9310c
    // 0xe94dd0: r0 = Null
    //     0xe94dd0: mov             x0, NULL
    // 0xe94dd4: LeaveFrame
    //     0xe94dd4: mov             SP, fp
    //     0xe94dd8: ldp             fp, lr, [SP], #0x10
    // 0xe94ddc: ret
    //     0xe94ddc: ret             
    // 0xe94de0: mov             x0, x3
    // 0xe94de4: r0 = ConcurrentModificationError()
    //     0xe94de4: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe94de8: mov             x1, x0
    // 0xe94dec: ldur            x0, [fp, #-0x50]
    // 0xe94df0: StoreField: r1->field_b = r0
    //     0xe94df0: stur            w0, [x1, #0xb]
    // 0xe94df4: mov             x0, x1
    // 0xe94df8: r0 = Throw()
    //     0xe94df8: bl              #0xec04b8  ; ThrowStub
    // 0xe94dfc: brk             #0
    // 0xe94e00: r0 = StackOverflowSharedWithFPURegs()
    //     0xe94e00: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe94e04: b               #0xe93064
    // 0xe94e08: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe94e08: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe94e0c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe94e0c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe94e10: b               #0xe93118
    // 0xe94e14: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe94e14: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe94e18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe94e18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe94e1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe94e1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe94e20: r0 = StackOverflowSharedWithFPURegs()
    //     0xe94e20: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe94e24: b               #0xe93354
    // 0xe94e28: r0 = StackOverflowSharedWithFPURegs()
    //     0xe94e28: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe94e2c: b               #0xe93434
    // 0xe94e30: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe94e30: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe94e34: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe94e34: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe94e38: stp             q5, q7, [SP, #-0x20]!
    // 0xe94e3c: stp             q3, q4, [SP, #-0x20]!
    // 0xe94e40: stp             q1, q2, [SP, #-0x20]!
    // 0xe94e44: SaveReg d0
    //     0xe94e44: str             q0, [SP, #-0x10]!
    // 0xe94e48: stp             x12, x13, [SP, #-0x10]!
    // 0xe94e4c: stp             x10, x11, [SP, #-0x10]!
    // 0xe94e50: stp             x8, x9, [SP, #-0x10]!
    // 0xe94e54: stp             x6, x7, [SP, #-0x10]!
    // 0xe94e58: stp             x4, x5, [SP, #-0x10]!
    // 0xe94e5c: SaveReg r3
    //     0xe94e5c: str             x3, [SP, #-8]!
    // 0xe94e60: r0 = AllocateDouble()
    //     0xe94e60: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe94e64: RestoreReg r3
    //     0xe94e64: ldr             x3, [SP], #8
    // 0xe94e68: ldp             x4, x5, [SP], #0x10
    // 0xe94e6c: ldp             x6, x7, [SP], #0x10
    // 0xe94e70: ldp             x8, x9, [SP], #0x10
    // 0xe94e74: ldp             x10, x11, [SP], #0x10
    // 0xe94e78: ldp             x12, x13, [SP], #0x10
    // 0xe94e7c: RestoreReg d0
    //     0xe94e7c: ldr             q0, [SP], #0x10
    // 0xe94e80: ldp             q1, q2, [SP], #0x20
    // 0xe94e84: ldp             q3, q4, [SP], #0x20
    // 0xe94e88: ldp             q5, q7, [SP], #0x20
    // 0xe94e8c: b               #0xe934bc
    // 0xe94e90: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe94e90: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe94e94: SaveReg d0
    //     0xe94e94: str             q0, [SP, #-0x10]!
    // 0xe94e98: SaveReg r3
    //     0xe94e98: str             x3, [SP, #-8]!
    // 0xe94e9c: r0 = AllocateDouble()
    //     0xe94e9c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe94ea0: RestoreReg r3
    //     0xe94ea0: ldr             x3, [SP], #8
    // 0xe94ea4: RestoreReg d0
    //     0xe94ea4: ldr             q0, [SP], #0x10
    // 0xe94ea8: b               #0xe935b8
    // 0xe94eac: stp             q8, q9, [SP, #-0x20]!
    // 0xe94eb0: stp             q6, q7, [SP, #-0x20]!
    // 0xe94eb4: stp             q4, q5, [SP, #-0x20]!
    // 0xe94eb8: stp             q2, q3, [SP, #-0x20]!
    // 0xe94ebc: SaveReg d1
    //     0xe94ebc: str             q1, [SP, #-0x10]!
    // 0xe94ec0: stp             x0, x1, [SP, #-0x10]!
    // 0xe94ec4: r0 = AllocateDouble()
    //     0xe94ec4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe94ec8: mov             x2, x0
    // 0xe94ecc: ldp             x0, x1, [SP], #0x10
    // 0xe94ed0: RestoreReg d1
    //     0xe94ed0: ldr             q1, [SP], #0x10
    // 0xe94ed4: ldp             q2, q3, [SP], #0x20
    // 0xe94ed8: ldp             q4, q5, [SP], #0x20
    // 0xe94edc: ldp             q6, q7, [SP], #0x20
    // 0xe94ee0: ldp             q8, q9, [SP], #0x20
    // 0xe94ee4: b               #0xe9365c
    // 0xe94ee8: stp             q8, q9, [SP, #-0x20]!
    // 0xe94eec: stp             q6, q7, [SP, #-0x20]!
    // 0xe94ef0: stp             q4, q5, [SP, #-0x20]!
    // 0xe94ef4: stp             q1, q3, [SP, #-0x20]!
    // 0xe94ef8: stp             x1, x2, [SP, #-0x10]!
    // 0xe94efc: SaveReg r0
    //     0xe94efc: str             x0, [SP, #-8]!
    // 0xe94f00: r0 = AllocateDouble()
    //     0xe94f00: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe94f04: mov             x3, x0
    // 0xe94f08: RestoreReg r0
    //     0xe94f08: ldr             x0, [SP], #8
    // 0xe94f0c: ldp             x1, x2, [SP], #0x10
    // 0xe94f10: ldp             q1, q3, [SP], #0x20
    // 0xe94f14: ldp             q4, q5, [SP], #0x20
    // 0xe94f18: ldp             q6, q7, [SP], #0x20
    // 0xe94f1c: ldp             q8, q9, [SP], #0x20
    // 0xe94f20: b               #0xe93684
    // 0xe94f24: stp             q8, q9, [SP, #-0x20]!
    // 0xe94f28: stp             q6, q7, [SP, #-0x20]!
    // 0xe94f2c: stp             q4, q5, [SP, #-0x20]!
    // 0xe94f30: SaveReg d1
    //     0xe94f30: str             q1, [SP, #-0x10]!
    // 0xe94f34: stp             x2, x3, [SP, #-0x10]!
    // 0xe94f38: stp             x0, x1, [SP, #-0x10]!
    // 0xe94f3c: r0 = AllocateDouble()
    //     0xe94f3c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe94f40: mov             x4, x0
    // 0xe94f44: ldp             x0, x1, [SP], #0x10
    // 0xe94f48: ldp             x2, x3, [SP], #0x10
    // 0xe94f4c: RestoreReg d1
    //     0xe94f4c: ldr             q1, [SP], #0x10
    // 0xe94f50: ldp             q4, q5, [SP], #0x20
    // 0xe94f54: ldp             q6, q7, [SP], #0x20
    // 0xe94f58: ldp             q8, q9, [SP], #0x20
    // 0xe94f5c: b               #0xe936ac
    // 0xe94f60: stp             q8, q9, [SP, #-0x20]!
    // 0xe94f64: stp             q6, q7, [SP, #-0x20]!
    // 0xe94f68: stp             q1, q5, [SP, #-0x20]!
    // 0xe94f6c: stp             x3, x4, [SP, #-0x10]!
    // 0xe94f70: stp             x1, x2, [SP, #-0x10]!
    // 0xe94f74: SaveReg r0
    //     0xe94f74: str             x0, [SP, #-8]!
    // 0xe94f78: r0 = AllocateDouble()
    //     0xe94f78: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe94f7c: mov             x5, x0
    // 0xe94f80: RestoreReg r0
    //     0xe94f80: ldr             x0, [SP], #8
    // 0xe94f84: ldp             x1, x2, [SP], #0x10
    // 0xe94f88: ldp             x3, x4, [SP], #0x10
    // 0xe94f8c: ldp             q1, q5, [SP], #0x20
    // 0xe94f90: ldp             q6, q7, [SP], #0x20
    // 0xe94f94: ldp             q8, q9, [SP], #0x20
    // 0xe94f98: b               #0xe936d4
    // 0xe94f9c: stp             q8, q9, [SP, #-0x20]!
    // 0xe94fa0: stp             q6, q7, [SP, #-0x20]!
    // 0xe94fa4: SaveReg d1
    //     0xe94fa4: str             q1, [SP, #-0x10]!
    // 0xe94fa8: stp             x4, x5, [SP, #-0x10]!
    // 0xe94fac: stp             x2, x3, [SP, #-0x10]!
    // 0xe94fb0: stp             x0, x1, [SP, #-0x10]!
    // 0xe94fb4: r0 = AllocateDouble()
    //     0xe94fb4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe94fb8: mov             x6, x0
    // 0xe94fbc: ldp             x0, x1, [SP], #0x10
    // 0xe94fc0: ldp             x2, x3, [SP], #0x10
    // 0xe94fc4: ldp             x4, x5, [SP], #0x10
    // 0xe94fc8: RestoreReg d1
    //     0xe94fc8: ldr             q1, [SP], #0x10
    // 0xe94fcc: ldp             q6, q7, [SP], #0x20
    // 0xe94fd0: ldp             q8, q9, [SP], #0x20
    // 0xe94fd4: b               #0xe936fc
    // 0xe94fd8: stp             q8, q9, [SP, #-0x20]!
    // 0xe94fdc: stp             q1, q7, [SP, #-0x20]!
    // 0xe94fe0: stp             x5, x6, [SP, #-0x10]!
    // 0xe94fe4: stp             x3, x4, [SP, #-0x10]!
    // 0xe94fe8: stp             x1, x2, [SP, #-0x10]!
    // 0xe94fec: SaveReg r0
    //     0xe94fec: str             x0, [SP, #-8]!
    // 0xe94ff0: r0 = AllocateDouble()
    //     0xe94ff0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe94ff4: mov             x7, x0
    // 0xe94ff8: RestoreReg r0
    //     0xe94ff8: ldr             x0, [SP], #8
    // 0xe94ffc: ldp             x1, x2, [SP], #0x10
    // 0xe95000: ldp             x3, x4, [SP], #0x10
    // 0xe95004: ldp             x5, x6, [SP], #0x10
    // 0xe95008: ldp             q1, q7, [SP], #0x20
    // 0xe9500c: ldp             q8, q9, [SP], #0x20
    // 0xe95010: b               #0xe93724
    // 0xe95014: stp             q8, q9, [SP, #-0x20]!
    // 0xe95018: SaveReg d1
    //     0xe95018: str             q1, [SP, #-0x10]!
    // 0xe9501c: stp             x6, x7, [SP, #-0x10]!
    // 0xe95020: stp             x4, x5, [SP, #-0x10]!
    // 0xe95024: stp             x2, x3, [SP, #-0x10]!
    // 0xe95028: stp             x0, x1, [SP, #-0x10]!
    // 0xe9502c: r0 = AllocateDouble()
    //     0xe9502c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95030: mov             x8, x0
    // 0xe95034: ldp             x0, x1, [SP], #0x10
    // 0xe95038: ldp             x2, x3, [SP], #0x10
    // 0xe9503c: ldp             x4, x5, [SP], #0x10
    // 0xe95040: ldp             x6, x7, [SP], #0x10
    // 0xe95044: RestoreReg d1
    //     0xe95044: ldr             q1, [SP], #0x10
    // 0xe95048: ldp             q8, q9, [SP], #0x20
    // 0xe9504c: b               #0xe9374c
    // 0xe95050: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe95050: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe95054: stp             q1, q2, [SP, #-0x20]!
    // 0xe95058: stp             x6, x7, [SP, #-0x10]!
    // 0xe9505c: SaveReg r4
    //     0xe9505c: str             x4, [SP, #-8]!
    // 0xe95060: r0 = AllocateDouble()
    //     0xe95060: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95064: RestoreReg r4
    //     0xe95064: ldr             x4, [SP], #8
    // 0xe95068: ldp             x6, x7, [SP], #0x10
    // 0xe9506c: ldp             q1, q2, [SP], #0x20
    // 0xe95070: b               #0xe93a08
    // 0xe95074: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe95074: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe95078: stp             q1, q3, [SP, #-0x20]!
    // 0xe9507c: stp             x7, x8, [SP, #-0x10]!
    // 0xe95080: stp             x4, x6, [SP, #-0x10]!
    // 0xe95084: r0 = AllocateDouble()
    //     0xe95084: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95088: ldp             x4, x6, [SP], #0x10
    // 0xe9508c: ldp             x7, x8, [SP], #0x10
    // 0xe95090: ldp             q1, q3, [SP], #0x20
    // 0xe95094: b               #0xe93a70
    // 0xe95098: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe95098: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe9509c: stp             q2, q4, [SP, #-0x20]!
    // 0xe950a0: stp             q0, q1, [SP, #-0x20]!
    // 0xe950a4: stp             x2, x3, [SP, #-0x10]!
    // 0xe950a8: SaveReg r1
    //     0xe950a8: str             x1, [SP, #-8]!
    // 0xe950ac: r0 = AllocateDouble()
    //     0xe950ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe950b0: RestoreReg r1
    //     0xe950b0: ldr             x1, [SP], #8
    // 0xe950b4: ldp             x2, x3, [SP], #0x10
    // 0xe950b8: ldp             q0, q1, [SP], #0x20
    // 0xe950bc: ldp             q2, q4, [SP], #0x20
    // 0xe950c0: b               #0xe93cdc
    // 0xe950c4: stp             q1, q4, [SP, #-0x20]!
    // 0xe950c8: SaveReg d0
    //     0xe950c8: str             q0, [SP, #-0x10]!
    // 0xe950cc: stp             x4, x5, [SP, #-0x10]!
    // 0xe950d0: stp             x2, x3, [SP, #-0x10]!
    // 0xe950d4: SaveReg r1
    //     0xe950d4: str             x1, [SP, #-8]!
    // 0xe950d8: r0 = AllocateDouble()
    //     0xe950d8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe950dc: RestoreReg r1
    //     0xe950dc: ldr             x1, [SP], #8
    // 0xe950e0: ldp             x2, x3, [SP], #0x10
    // 0xe950e4: ldp             x4, x5, [SP], #0x10
    // 0xe950e8: RestoreReg d0
    //     0xe950e8: ldr             q0, [SP], #0x10
    // 0xe950ec: ldp             q1, q4, [SP], #0x20
    // 0xe950f0: b               #0xe93d70
    // 0xe950f4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe950f4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe950f8: stp             q1, q2, [SP, #-0x20]!
    // 0xe950fc: stp             x1, x2, [SP, #-0x10]!
    // 0xe95100: r0 = AllocateDouble()
    //     0xe95100: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95104: ldp             x1, x2, [SP], #0x10
    // 0xe95108: ldp             q1, q2, [SP], #0x20
    // 0xe9510c: b               #0xe94020
    // 0xe95110: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe95110: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe95114: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe95114: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe95118: SaveReg d1
    //     0xe95118: str             q1, [SP, #-0x10]!
    // 0xe9511c: stp             x4, x5, [SP, #-0x10]!
    // 0xe95120: SaveReg r2
    //     0xe95120: str             x2, [SP, #-8]!
    // 0xe95124: r0 = AllocateDouble()
    //     0xe95124: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95128: RestoreReg r2
    //     0xe95128: ldr             x2, [SP], #8
    // 0xe9512c: ldp             x4, x5, [SP], #0x10
    // 0xe95130: RestoreReg d1
    //     0xe95130: ldr             q1, [SP], #0x10
    // 0xe95134: b               #0xe942c8
    // 0xe95138: stp             q1, q3, [SP, #-0x20]!
    // 0xe9513c: SaveReg r3
    //     0xe9513c: str             x3, [SP, #-8]!
    // 0xe95140: r0 = AllocateDouble()
    //     0xe95140: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95144: RestoreReg r3
    //     0xe95144: ldr             x3, [SP], #8
    // 0xe95148: ldp             q1, q3, [SP], #0x20
    // 0xe9514c: b               #0xe94444
    // 0xe95150: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe95150: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe95154: stp             q1, q4, [SP, #-0x20]!
    // 0xe95158: SaveReg d0
    //     0xe95158: str             q0, [SP, #-0x10]!
    // 0xe9515c: stp             x3, x4, [SP, #-0x10]!
    // 0xe95160: SaveReg r1
    //     0xe95160: str             x1, [SP, #-8]!
    // 0xe95164: r0 = AllocateDouble()
    //     0xe95164: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95168: RestoreReg r1
    //     0xe95168: ldr             x1, [SP], #8
    // 0xe9516c: ldp             x3, x4, [SP], #0x10
    // 0xe95170: RestoreReg d0
    //     0xe95170: ldr             q0, [SP], #0x10
    // 0xe95174: ldp             q1, q4, [SP], #0x20
    // 0xe95178: b               #0xe944bc
    // 0xe9517c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe9517c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe95180: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe95180: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe95184: stp             q0, q3, [SP, #-0x20]!
    // 0xe95188: stp             x3, x4, [SP, #-0x10]!
    // 0xe9518c: r0 = AllocateDouble()
    //     0xe9518c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95190: ldp             x3, x4, [SP], #0x10
    // 0xe95194: ldp             q0, q3, [SP], #0x20
    // 0xe95198: b               #0xe945a8
    // 0xe9519c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe9519c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe951a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe951a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe951a4: stp             q0, q2, [SP, #-0x20]!
    // 0xe951a8: stp             x4, x6, [SP, #-0x10]!
    // 0xe951ac: SaveReg r2
    //     0xe951ac: str             x2, [SP, #-8]!
    // 0xe951b0: r0 = AllocateDouble()
    //     0xe951b0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe951b4: RestoreReg r2
    //     0xe951b4: ldr             x2, [SP], #8
    // 0xe951b8: ldp             x4, x6, [SP], #0x10
    // 0xe951bc: ldp             q0, q2, [SP], #0x20
    // 0xe951c0: b               #0xe948dc
    // 0xe951c4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe951c4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe951c8: stp             q0, q3, [SP, #-0x20]!
    // 0xe951cc: stp             x4, x6, [SP, #-0x10]!
    // 0xe951d0: stp             x1, x2, [SP, #-0x10]!
    // 0xe951d4: r0 = AllocateDouble()
    //     0xe951d4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe951d8: ldp             x1, x2, [SP], #0x10
    // 0xe951dc: ldp             x4, x6, [SP], #0x10
    // 0xe951e0: ldp             q0, q3, [SP], #0x20
    // 0xe951e4: b               #0xe9494c
    // 0xe951e8: stp             q3, q4, [SP, #-0x20]!
    // 0xe951ec: stp             q1, q2, [SP, #-0x20]!
    // 0xe951f0: SaveReg d0
    //     0xe951f0: str             q0, [SP, #-0x10]!
    // 0xe951f4: stp             x4, x6, [SP, #-0x10]!
    // 0xe951f8: stp             x2, x3, [SP, #-0x10]!
    // 0xe951fc: SaveReg r1
    //     0xe951fc: str             x1, [SP, #-8]!
    // 0xe95200: r0 = AllocateDouble()
    //     0xe95200: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95204: RestoreReg r1
    //     0xe95204: ldr             x1, [SP], #8
    // 0xe95208: ldp             x2, x3, [SP], #0x10
    // 0xe9520c: ldp             x4, x6, [SP], #0x10
    // 0xe95210: RestoreReg d0
    //     0xe95210: ldr             q0, [SP], #0x10
    // 0xe95214: ldp             q1, q2, [SP], #0x20
    // 0xe95218: ldp             q3, q4, [SP], #0x20
    // 0xe9521c: b               #0xe94a70
    // 0xe95220: r0 = NullCastErrorSharedWithFPURegs()
    //     0xe95220: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xe95224: stp             q3, q4, [SP, #-0x20]!
    // 0xe95228: stp             q1, q2, [SP, #-0x20]!
    // 0xe9522c: SaveReg d0
    //     0xe9522c: str             q0, [SP, #-0x10]!
    // 0xe95230: stp             x4, x6, [SP, #-0x10]!
    // 0xe95234: stp             x2, x3, [SP, #-0x10]!
    // 0xe95238: SaveReg r1
    //     0xe95238: str             x1, [SP, #-8]!
    // 0xe9523c: r0 = AllocateDouble()
    //     0xe9523c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95240: RestoreReg r1
    //     0xe95240: ldr             x1, [SP], #8
    // 0xe95244: ldp             x2, x3, [SP], #0x10
    // 0xe95248: ldp             x4, x6, [SP], #0x10
    // 0xe9524c: RestoreReg d0
    //     0xe9524c: ldr             q0, [SP], #0x10
    // 0xe95250: ldp             q1, q2, [SP], #0x20
    // 0xe95254: ldp             q3, q4, [SP], #0x20
    // 0xe95258: b               #0xe94b20
    // 0xe9525c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe9525c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe95260: stp             q1, q3, [SP, #-0x20]!
    // 0xe95264: SaveReg r1
    //     0xe95264: str             x1, [SP, #-8]!
    // 0xe95268: r0 = AllocateDouble()
    //     0xe95268: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe9526c: RestoreReg r1
    //     0xe9526c: ldr             x1, [SP], #8
    // 0xe95270: ldp             q1, q3, [SP], #0x20
    // 0xe95274: b               #0xe94d5c
  }
  _ _appendDecoration(/* No info */) {
    // ** addr: 0xe95284, size: 0x20c
    // 0xe95284: EnterFrame
    //     0xe95284: stp             fp, lr, [SP, #-0x10]!
    //     0xe95288: mov             fp, SP
    // 0xe9528c: AllocStack(0x28)
    //     0xe9528c: sub             SP, SP, #0x28
    // 0xe95290: SetupParameters(RichText this /* r1 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xe95290: mov             x0, x3
    //     0xe95294: stur            x3, [fp, #-0x18]
    //     0xe95298: mov             x3, x1
    //     0xe9529c: stur            x1, [fp, #-0x10]
    // 0xe952a0: CheckStackOverflow
    //     0xe952a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe952a4: cmp             SP, x16
    //     0xe952a8: b.ls            #0xe95484
    // 0xe952ac: tbnz            w2, #4, #0xe953b4
    // 0xe952b0: LoadField: r2 = r3->field_33
    //     0xe952b0: ldur            w2, [x3, #0x33]
    // 0xe952b4: DecompressPointer r2
    //     0xe952b4: add             x2, x2, HEAP, lsl #32
    // 0xe952b8: stur            x2, [fp, #-8]
    // 0xe952bc: LoadField: r1 = r2->field_b
    //     0xe952bc: ldur            w1, [x2, #0xb]
    // 0xe952c0: cbz             w1, #0xe953ac
    // 0xe952c4: mov             x1, x2
    // 0xe952c8: r0 = last()
    //     0xe952c8: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xe952cc: LoadField: r1 = r0->field_7
    //     0xe952cc: ldur            w1, [x0, #7]
    // 0xe952d0: DecompressPointer r1
    //     0xe952d0: add             x1, x1, HEAP, lsl #32
    // 0xe952d4: ldur            x3, [fp, #-0x18]
    // 0xe952d8: LoadField: r2 = r3->field_7
    //     0xe952d8: ldur            w2, [x3, #7]
    // 0xe952dc: DecompressPointer r2
    //     0xe952dc: add             x2, x2, HEAP, lsl #32
    // 0xe952e0: cmp             w1, w2
    // 0xe952e4: b.ne            #0xe953b8
    // 0xe952e8: ldur            x4, [fp, #-8]
    // 0xe952ec: LoadField: r1 = r4->field_b
    //     0xe952ec: ldur            w1, [x4, #0xb]
    // 0xe952f0: r2 = LoadInt32Instr(r1)
    //     0xe952f0: sbfx            x2, x1, #1, #0x1f
    // 0xe952f4: sub             x5, x2, #1
    // 0xe952f8: stur            x5, [fp, #-0x20]
    // 0xe952fc: ArrayLoad: r2 = r3[0]  ; List_8
    //     0xe952fc: ldur            x2, [x3, #0x17]
    // 0xe95300: mov             x1, x0
    // 0xe95304: r0 = copyWith()
    //     0xe95304: bl              #0xe95490  ; [package:pdf/src/widgets/text.dart] _TextDecoration::copyWith
    // 0xe95308: mov             x4, x0
    // 0xe9530c: ldur            x3, [fp, #-8]
    // 0xe95310: stur            x4, [fp, #-0x28]
    // 0xe95314: LoadField: r2 = r3->field_7
    //     0xe95314: ldur            w2, [x3, #7]
    // 0xe95318: DecompressPointer r2
    //     0xe95318: add             x2, x2, HEAP, lsl #32
    // 0xe9531c: mov             x0, x4
    // 0xe95320: r1 = Null
    //     0xe95320: mov             x1, NULL
    // 0xe95324: cmp             w2, NULL
    // 0xe95328: b.eq            #0xe95348
    // 0xe9532c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe9532c: ldur            w4, [x2, #0x17]
    // 0xe95330: DecompressPointer r4
    //     0xe95330: add             x4, x4, HEAP, lsl #32
    // 0xe95334: r8 = X0
    //     0xe95334: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe95338: LoadField: r9 = r4->field_7
    //     0xe95338: ldur            x9, [x4, #7]
    // 0xe9533c: r3 = Null
    //     0xe9533c: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e198] Null
    //     0xe95340: ldr             x3, [x3, #0x198]
    // 0xe95344: blr             x9
    // 0xe95348: ldur            x2, [fp, #-8]
    // 0xe9534c: LoadField: r0 = r2->field_b
    //     0xe9534c: ldur            w0, [x2, #0xb]
    // 0xe95350: r1 = LoadInt32Instr(r0)
    //     0xe95350: sbfx            x1, x0, #1, #0x1f
    // 0xe95354: mov             x0, x1
    // 0xe95358: ldur            x1, [fp, #-0x20]
    // 0xe9535c: cmp             x1, x0
    // 0xe95360: b.hs            #0xe9548c
    // 0xe95364: LoadField: r1 = r2->field_f
    //     0xe95364: ldur            w1, [x2, #0xf]
    // 0xe95368: DecompressPointer r1
    //     0xe95368: add             x1, x1, HEAP, lsl #32
    // 0xe9536c: ldur            x0, [fp, #-0x28]
    // 0xe95370: ldur            x2, [fp, #-0x20]
    // 0xe95374: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe95374: add             x25, x1, x2, lsl #2
    //     0xe95378: add             x25, x25, #0xf
    //     0xe9537c: str             w0, [x25]
    //     0xe95380: tbz             w0, #0, #0xe9539c
    //     0xe95384: ldurb           w16, [x1, #-1]
    //     0xe95388: ldurb           w17, [x0, #-1]
    //     0xe9538c: and             x16, x17, x16, lsr #2
    //     0xe95390: tst             x16, HEAP, lsr #32
    //     0xe95394: b.eq            #0xe9539c
    //     0xe95398: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe9539c: r0 = Null
    //     0xe9539c: mov             x0, NULL
    // 0xe953a0: LeaveFrame
    //     0xe953a0: mov             SP, fp
    //     0xe953a4: ldp             fp, lr, [SP], #0x10
    // 0xe953a8: ret
    //     0xe953a8: ret             
    // 0xe953ac: mov             x3, x0
    // 0xe953b0: b               #0xe953b8
    // 0xe953b4: mov             x3, x0
    // 0xe953b8: ldur            x0, [fp, #-0x10]
    // 0xe953bc: LoadField: r4 = r0->field_33
    //     0xe953bc: ldur            w4, [x0, #0x33]
    // 0xe953c0: DecompressPointer r4
    //     0xe953c0: add             x4, x4, HEAP, lsl #32
    // 0xe953c4: stur            x4, [fp, #-8]
    // 0xe953c8: LoadField: r2 = r4->field_7
    //     0xe953c8: ldur            w2, [x4, #7]
    // 0xe953cc: DecompressPointer r2
    //     0xe953cc: add             x2, x2, HEAP, lsl #32
    // 0xe953d0: mov             x0, x3
    // 0xe953d4: r1 = Null
    //     0xe953d4: mov             x1, NULL
    // 0xe953d8: cmp             w2, NULL
    // 0xe953dc: b.eq            #0xe953fc
    // 0xe953e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe953e0: ldur            w4, [x2, #0x17]
    // 0xe953e4: DecompressPointer r4
    //     0xe953e4: add             x4, x4, HEAP, lsl #32
    // 0xe953e8: r8 = X0
    //     0xe953e8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe953ec: LoadField: r9 = r4->field_7
    //     0xe953ec: ldur            x9, [x4, #7]
    // 0xe953f0: r3 = Null
    //     0xe953f0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e1a8] Null
    //     0xe953f4: ldr             x3, [x3, #0x1a8]
    // 0xe953f8: blr             x9
    // 0xe953fc: ldur            x0, [fp, #-8]
    // 0xe95400: LoadField: r1 = r0->field_b
    //     0xe95400: ldur            w1, [x0, #0xb]
    // 0xe95404: LoadField: r2 = r0->field_f
    //     0xe95404: ldur            w2, [x0, #0xf]
    // 0xe95408: DecompressPointer r2
    //     0xe95408: add             x2, x2, HEAP, lsl #32
    // 0xe9540c: LoadField: r3 = r2->field_b
    //     0xe9540c: ldur            w3, [x2, #0xb]
    // 0xe95410: r2 = LoadInt32Instr(r1)
    //     0xe95410: sbfx            x2, x1, #1, #0x1f
    // 0xe95414: stur            x2, [fp, #-0x20]
    // 0xe95418: r1 = LoadInt32Instr(r3)
    //     0xe95418: sbfx            x1, x3, #1, #0x1f
    // 0xe9541c: cmp             x2, x1
    // 0xe95420: b.ne            #0xe9542c
    // 0xe95424: mov             x1, x0
    // 0xe95428: r0 = _growToNextCapacity()
    //     0xe95428: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe9542c: ldur            x2, [fp, #-8]
    // 0xe95430: ldur            x3, [fp, #-0x20]
    // 0xe95434: add             x4, x3, #1
    // 0xe95438: lsl             x5, x4, #1
    // 0xe9543c: StoreField: r2->field_b = r5
    //     0xe9543c: stur            w5, [x2, #0xb]
    // 0xe95440: LoadField: r1 = r2->field_f
    //     0xe95440: ldur            w1, [x2, #0xf]
    // 0xe95444: DecompressPointer r1
    //     0xe95444: add             x1, x1, HEAP, lsl #32
    // 0xe95448: ldur            x0, [fp, #-0x18]
    // 0xe9544c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe9544c: add             x25, x1, x3, lsl #2
    //     0xe95450: add             x25, x25, #0xf
    //     0xe95454: str             w0, [x25]
    //     0xe95458: tbz             w0, #0, #0xe95474
    //     0xe9545c: ldurb           w16, [x1, #-1]
    //     0xe95460: ldurb           w17, [x0, #-1]
    //     0xe95464: and             x16, x17, x16, lsr #2
    //     0xe95468: tst             x16, HEAP, lsr #32
    //     0xe9546c: b.eq            #0xe95474
    //     0xe95470: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe95474: r0 = Null
    //     0xe95474: mov             x0, NULL
    // 0xe95478: LeaveFrame
    //     0xe95478: mov             SP, fp
    //     0xe9547c: ldp             fp, lr, [SP], #0x10
    // 0xe95480: ret
    //     0xe95480: ret             
    // 0xe95484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe95484: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe95488: b               #0xe952ac
    // 0xe9548c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe9548c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _splitWord(/* No info */) {
    // ** addr: 0xe954f4, size: 0x5f8
    // 0xe954f4: EnterFrame
    //     0xe954f4: stp             fp, lr, [SP, #-0x10]!
    //     0xe954f8: mov             fp, SP
    // 0xe954fc: AllocStack(0xa0)
    //     0xe954fc: sub             SP, SP, #0xa0
    // 0xe95500: r4 = 2
    //     0xe95500: movz            x4, #0x2
    // 0xe95504: mov             x7, x2
    // 0xe95508: mov             x6, x3
    // 0xe9550c: stur            x2, [fp, #-0x40]
    // 0xe95510: stur            x3, [fp, #-0x48]
    // 0xe95514: stur            d0, [fp, #-0x58]
    // 0xe95518: CheckStackOverflow
    //     0xe95518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9551c: cmp             SP, x16
    //     0xe95520: b.ls            #0xe95934
    // 0xe95524: LoadField: r0 = r7->field_7
    //     0xe95524: ldur            w0, [x7, #7]
    // 0xe95528: r8 = LoadInt32Instr(r0)
    //     0xe95528: sbfx            x8, x0, #1, #0x1f
    // 0xe9552c: stur            x8, [fp, #-0x38]
    // 0xe95530: sdiv            x0, x8, x4
    // 0xe95534: LoadField: r9 = r5->field_2f
    //     0xe95534: ldur            w9, [x5, #0x2f]
    // 0xe95538: DecompressPointer r9
    //     0xe95538: add             x9, x9, HEAP, lsl #32
    // 0xe9553c: stur            x9, [fp, #-0x30]
    // 0xe95540: LoadField: r10 = r5->field_23
    //     0xe95540: ldur            w10, [x5, #0x23]
    // 0xe95544: DecompressPointer r10
    //     0xe95544: add             x10, x10, HEAP, lsl #32
    // 0xe95548: stur            x10, [fp, #-0x28]
    // 0xe9554c: r5 = LoadClassIdInstr(r6)
    //     0xe9554c: ldur            x5, [x6, #-1]
    //     0xe95550: ubfx            x5, x5, #0xc, #0x14
    // 0xe95554: stur            x5, [fp, #-0x20]
    // 0xe95558: mov             x12, x8
    // 0xe9555c: mov             x11, x0
    // 0xe95560: r13 = 0
    //     0xe95560: movz            x13, #0
    // 0xe95564: stur            x13, [fp, #-8]
    // 0xe95568: stur            x12, [fp, #-0x10]
    // 0xe9556c: stur            x11, [fp, #-0x18]
    // 0xe95570: CheckStackOverflow
    //     0xe95570: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe95574: cmp             SP, x16
    //     0xe95578: b.ls            #0xe9593c
    // 0xe9557c: add             x0, x13, #1
    // 0xe95580: cmp             x0, x12
    // 0xe95584: b.ge            #0xe95898
    // 0xe95588: r0 = BoxInt64Instr(r11)
    //     0xe95588: sbfiz           x0, x11, #1, #0x1f
    //     0xe9558c: cmp             x11, x0, asr #1
    //     0xe95590: b.eq            #0xe9559c
    //     0xe95594: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xe95598: stur            x11, [x0, #7]
    // 0xe9559c: mov             x2, x0
    // 0xe955a0: mov             x3, x8
    // 0xe955a4: r1 = 0
    //     0xe955a4: movz            x1, #0
    // 0xe955a8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe955a8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe955ac: r0 = checkValidRange()
    //     0xe955ac: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe955b0: ldur            x1, [fp, #-0x40]
    // 0xe955b4: mov             x3, x0
    // 0xe955b8: r2 = 0
    //     0xe955b8: movz            x2, #0
    // 0xe955bc: r0 = _substringUnchecked()
    //     0xe955bc: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xe955c0: mov             x1, x0
    // 0xe955c4: ldur            x0, [fp, #-0x30]
    // 0xe955c8: cmp             w0, NULL
    // 0xe955cc: b.eq            #0xe95944
    // 0xe955d0: ldur            x3, [fp, #-0x28]
    // 0xe955d4: cmp             w3, NULL
    // 0xe955d8: b.eq            #0xe95948
    // 0xe955dc: LoadField: d0 = r3->field_7
    //     0xe955dc: ldur            d0, [x3, #7]
    // 0xe955e0: stur            d0, [fp, #-0x68]
    // 0xe955e4: LoadField: d1 = r0->field_7
    //     0xe955e4: ldur            d1, [x0, #7]
    // 0xe955e8: fdiv            d2, d1, d0
    // 0xe955ec: ldur            x4, [fp, #-0x20]
    // 0xe955f0: stur            d2, [fp, #-0x60]
    // 0xe955f4: cmp             x4, #0x37f
    // 0xe955f8: b.ne            #0xe95660
    // 0xe955fc: LoadField: r2 = r1->field_7
    //     0xe955fc: ldur            w2, [x1, #7]
    // 0xe95600: cbnz            w2, #0xe95610
    // 0xe95604: r1 = Instance_PdfFontMetrics
    //     0xe95604: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e120] Obj!PdfFontMetrics@e0c991
    //     0xe95608: ldr             x1, [x1, #0x120]
    // 0xe9560c: b               #0xe956c4
    // 0xe95610: mov             x2, x1
    // 0xe95614: r1 = Instance_Latin1Codec
    //     0xe95614: ldr             x1, [PP, #0xdf8]  ; [pp+0xdf8] Obj!Latin1Codec@e2cd01
    // 0xe95618: r0 = encode()
    //     0xe95618: bl              #0xceba10  ; [dart:convert] Latin1Codec::encode
    // 0xe9561c: ldur            x2, [fp, #-0x48]
    // 0xe95620: r1 = Function 'glyphMetrics':.
    //     0xe95620: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e108] AnonymousClosure: (0xe71018), in [package:pdf/src/pdf/obj/type1_font.dart] PdfType1Font::glyphMetrics (0xe71054)
    //     0xe95624: ldr             x1, [x1, #0x108]
    // 0xe95628: stur            x0, [fp, #-0x50]
    // 0xe9562c: r0 = AllocateClosure()
    //     0xe9562c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe95630: ldur            x2, [fp, #-0x50]
    // 0xe95634: mov             x3, x0
    // 0xe95638: r1 = <PdfFontMetrics, int, PdfFontMetrics>
    //     0xe95638: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e110] TypeArguments: <PdfFontMetrics, int, PdfFontMetrics>
    //     0xe9563c: ldr             x1, [x1, #0x110]
    // 0xe95640: r0 = MappedIterable()
    //     0xe95640: bl              #0x7ac0ac  ; [dart:_internal] MappedIterable::MappedIterable
    // 0xe95644: mov             x2, x0
    // 0xe95648: ldur            d0, [fp, #-0x60]
    // 0xe9564c: r1 = Null
    //     0xe9564c: mov             x1, NULL
    // 0xe95650: r0 = PdfFontMetrics.append()
    //     0xe95650: bl              #0xe701bc  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::PdfFontMetrics.append
    // 0xe95654: mov             x1, x0
    // 0xe95658: ldur            d0, [fp, #-0x68]
    // 0xe9565c: b               #0xe956c4
    // 0xe95660: ldur            x3, [fp, #-0x48]
    // 0xe95664: mov             v0.16b, v2.16b
    // 0xe95668: r0 = inline_Allocate_Double()
    //     0xe95668: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe9566c: add             x0, x0, #0x10
    //     0xe95670: cmp             x2, x0
    //     0xe95674: b.ls            #0xe9594c
    //     0xe95678: str             x0, [THR, #0x50]  ; THR::top
    //     0xe9567c: sub             x0, x0, #0xf
    //     0xe95680: movz            x2, #0xe15c
    //     0xe95684: movk            x2, #0x3, lsl #16
    //     0xe95688: stur            x2, [x0, #-1]
    // 0xe9568c: StoreField: r0->field_7 = d0
    //     0xe9568c: stur            d0, [x0, #7]
    // 0xe95690: r2 = LoadClassIdInstr(r3)
    //     0xe95690: ldur            x2, [x3, #-1]
    //     0xe95694: ubfx            x2, x2, #0xc, #0x14
    // 0xe95698: str             x0, [SP]
    // 0xe9569c: mov             x0, x2
    // 0xe956a0: mov             x2, x1
    // 0xe956a4: mov             x1, x3
    // 0xe956a8: r4 = const [0, 0x3, 0x1, 0x2, letterSpacing, 0x2, null]
    //     0xe956a8: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e128] List(7) [0, 0x3, 0x1, 0x2, "letterSpacing", 0x2, Null]
    //     0xe956ac: ldr             x4, [x4, #0x128]
    // 0xe956b0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe956b0: sub             lr, x0, #1, lsl #12
    //     0xe956b4: ldr             lr, [x21, lr, lsl #3]
    //     0xe956b8: blr             lr
    // 0xe956bc: mov             x1, x0
    // 0xe956c0: ldur            d0, [fp, #-0x68]
    // 0xe956c4: ldur            d1, [fp, #-0x58]
    // 0xe956c8: LoadField: d2 = r1->field_7
    //     0xe956c8: ldur            d2, [x1, #7]
    // 0xe956cc: fmul            d3, d2, d0
    // 0xe956d0: LoadField: d2 = r1->field_f
    //     0xe956d0: ldur            d2, [x1, #0xf]
    // 0xe956d4: fmul            d4, d2, d0
    // 0xe956d8: LoadField: d2 = r1->field_1f
    //     0xe956d8: ldur            d2, [x1, #0x1f]
    // 0xe956dc: fmul            d5, d2, d0
    // 0xe956e0: ArrayLoad: d2 = r1[0]  ; List_8
    //     0xe956e0: ldur            d2, [x1, #0x17]
    // 0xe956e4: fmul            d6, d2, d0
    // 0xe956e8: LoadField: d2 = r1->field_27
    //     0xe956e8: ldur            d2, [x1, #0x27]
    // 0xe956ec: fmul            d7, d2, d0
    // 0xe956f0: LoadField: d2 = r1->field_2f
    //     0xe956f0: ldur            d2, [x1, #0x2f]
    // 0xe956f4: fmul            d8, d2, d0
    // 0xe956f8: LoadField: d2 = r1->field_37
    //     0xe956f8: ldur            d2, [x1, #0x37]
    // 0xe956fc: fmul            d9, d2, d0
    // 0xe95700: LoadField: d2 = r1->field_3f
    //     0xe95700: ldur            d2, [x1, #0x3f]
    // 0xe95704: fmul            d10, d2, d0
    // 0xe95708: r0 = inline_Allocate_Double()
    //     0xe95708: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe9570c: add             x0, x0, #0x10
    //     0xe95710: cmp             x2, x0
    //     0xe95714: b.ls            #0xe95964
    //     0xe95718: str             x0, [THR, #0x50]  ; THR::top
    //     0xe9571c: sub             x0, x0, #0xf
    //     0xe95720: movz            x2, #0xe15c
    //     0xe95724: movk            x2, #0x3, lsl #16
    //     0xe95728: stur            x2, [x0, #-1]
    // 0xe9572c: StoreField: r0->field_7 = d3
    //     0xe9572c: stur            d3, [x0, #7]
    // 0xe95730: r2 = inline_Allocate_Double()
    //     0xe95730: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xe95734: add             x2, x2, #0x10
    //     0xe95738: cmp             x3, x2
    //     0xe9573c: b.ls            #0xe9599c
    //     0xe95740: str             x2, [THR, #0x50]  ; THR::top
    //     0xe95744: sub             x2, x2, #0xf
    //     0xe95748: movz            x3, #0xe15c
    //     0xe9574c: movk            x3, #0x3, lsl #16
    //     0xe95750: stur            x3, [x2, #-1]
    // 0xe95754: StoreField: r2->field_7 = d4
    //     0xe95754: stur            d4, [x2, #7]
    // 0xe95758: r3 = inline_Allocate_Double()
    //     0xe95758: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0xe9575c: add             x3, x3, #0x10
    //     0xe95760: cmp             x4, x3
    //     0xe95764: b.ls            #0xe959d0
    //     0xe95768: str             x3, [THR, #0x50]  ; THR::top
    //     0xe9576c: sub             x3, x3, #0xf
    //     0xe95770: movz            x4, #0xe15c
    //     0xe95774: movk            x4, #0x3, lsl #16
    //     0xe95778: stur            x4, [x3, #-1]
    // 0xe9577c: StoreField: r3->field_7 = d5
    //     0xe9577c: stur            d5, [x3, #7]
    // 0xe95780: r4 = inline_Allocate_Double()
    //     0xe95780: ldp             x4, x5, [THR, #0x50]  ; THR::top
    //     0xe95784: add             x4, x4, #0x10
    //     0xe95788: cmp             x5, x4
    //     0xe9578c: b.ls            #0xe95a0c
    //     0xe95790: str             x4, [THR, #0x50]  ; THR::top
    //     0xe95794: sub             x4, x4, #0xf
    //     0xe95798: movz            x5, #0xe15c
    //     0xe9579c: movk            x5, #0x3, lsl #16
    //     0xe957a0: stur            x5, [x4, #-1]
    // 0xe957a4: StoreField: r4->field_7 = d6
    //     0xe957a4: stur            d6, [x4, #7]
    // 0xe957a8: r5 = inline_Allocate_Double()
    //     0xe957a8: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xe957ac: add             x5, x5, #0x10
    //     0xe957b0: cmp             x6, x5
    //     0xe957b4: b.ls            #0xe95a40
    //     0xe957b8: str             x5, [THR, #0x50]  ; THR::top
    //     0xe957bc: sub             x5, x5, #0xf
    //     0xe957c0: movz            x6, #0xe15c
    //     0xe957c4: movk            x6, #0x3, lsl #16
    //     0xe957c8: stur            x6, [x5, #-1]
    // 0xe957cc: StoreField: r5->field_7 = d7
    //     0xe957cc: stur            d7, [x5, #7]
    // 0xe957d0: r6 = inline_Allocate_Double()
    //     0xe957d0: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0xe957d4: add             x6, x6, #0x10
    //     0xe957d8: cmp             x7, x6
    //     0xe957dc: b.ls            #0xe95a7c
    //     0xe957e0: str             x6, [THR, #0x50]  ; THR::top
    //     0xe957e4: sub             x6, x6, #0xf
    //     0xe957e8: movz            x7, #0xe15c
    //     0xe957ec: movk            x7, #0x3, lsl #16
    //     0xe957f0: stur            x7, [x6, #-1]
    // 0xe957f4: StoreField: r6->field_7 = d8
    //     0xe957f4: stur            d8, [x6, #7]
    // 0xe957f8: r7 = inline_Allocate_Double()
    //     0xe957f8: ldp             x7, x8, [THR, #0x50]  ; THR::top
    //     0xe957fc: add             x7, x7, #0x10
    //     0xe95800: cmp             x8, x7
    //     0xe95804: b.ls            #0xe95ab0
    //     0xe95808: str             x7, [THR, #0x50]  ; THR::top
    //     0xe9580c: sub             x7, x7, #0xf
    //     0xe95810: movz            x8, #0xe15c
    //     0xe95814: movk            x8, #0x3, lsl #16
    //     0xe95818: stur            x8, [x7, #-1]
    // 0xe9581c: StoreField: r7->field_7 = d10
    //     0xe9581c: stur            d10, [x7, #7]
    // 0xe95820: stp             x2, x0, [SP, #0x28]
    // 0xe95824: stp             x4, x3, [SP, #0x18]
    // 0xe95828: stp             x6, x5, [SP, #8]
    // 0xe9582c: str             x7, [SP]
    // 0xe95830: mov             v0.16b, v9.16b
    // 0xe95834: r4 = const [0, 0x9, 0x7, 0x2, ascent, 0x6, bottom, 0x5, descent, 0x7, left, 0x2, leftBearing, 0x8, right, 0x4, top, 0x3, null]
    //     0xe95834: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e130] List(19) [0, 0x9, 0x7, 0x2, "ascent", 0x6, "bottom", 0x5, "descent", 0x7, "left", 0x2, "leftBearing", 0x8, "right", 0x4, "top", 0x3, Null]
    //     0xe95838: ldr             x4, [x4, #0x130]
    // 0xe9583c: r0 = copyWith()
    //     0xe9583c: bl              #0x7b6ea4  ; [package:pdf/src/pdf/font/font_metrics.dart] PdfFontMetrics::copyWith
    // 0xe95840: LoadField: d0 = r0->field_1f
    //     0xe95840: ldur            d0, [x0, #0x1f]
    // 0xe95844: LoadField: d1 = r0->field_7
    //     0xe95844: ldur            d1, [x0, #7]
    // 0xe95848: fsub            d2, d0, d1
    // 0xe9584c: ldur            d0, [fp, #-0x58]
    // 0xe95850: fcmp            d2, d0
    // 0xe95854: b.le            #0xe95864
    // 0xe95858: ldur            x13, [fp, #-8]
    // 0xe9585c: ldur            x12, [fp, #-0x18]
    // 0xe95860: b               #0xe9586c
    // 0xe95864: ldur            x13, [fp, #-0x18]
    // 0xe95868: ldur            x12, [fp, #-0x10]
    // 0xe9586c: r2 = 2
    //     0xe9586c: movz            x2, #0x2
    // 0xe95870: add             x3, x13, x12
    // 0xe95874: sdiv            x11, x3, x2
    // 0xe95878: ldur            x7, [fp, #-0x40]
    // 0xe9587c: ldur            x6, [fp, #-0x48]
    // 0xe95880: ldur            x9, [fp, #-0x30]
    // 0xe95884: ldur            x10, [fp, #-0x28]
    // 0xe95888: ldur            x5, [fp, #-0x20]
    // 0xe9588c: ldur            x8, [fp, #-0x38]
    // 0xe95890: mov             x4, x2
    // 0xe95894: b               #0xe95564
    // 0xe95898: mov             x2, x11
    // 0xe9589c: cmp             x2, #1
    // 0xe958a0: b.ge            #0xe958ac
    // 0xe958a4: r1 = 2
    //     0xe958a4: movz            x1, #0x2
    // 0xe958a8: b               #0xe9591c
    // 0xe958ac: cmp             x2, #1
    // 0xe958b0: b.le            #0xe958d0
    // 0xe958b4: r0 = BoxInt64Instr(r2)
    //     0xe958b4: sbfiz           x0, x2, #1, #0x1f
    //     0xe958b8: cmp             x2, x0, asr #1
    //     0xe958bc: b.eq            #0xe958c8
    //     0xe958c0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe958c4: stur            x2, [x0, #7]
    // 0xe958c8: mov             x1, x0
    // 0xe958cc: b               #0xe9591c
    // 0xe958d0: r0 = BoxInt64Instr(r2)
    //     0xe958d0: sbfiz           x0, x2, #1, #0x1f
    //     0xe958d4: cmp             x2, x0, asr #1
    //     0xe958d8: b.eq            #0xe958e4
    //     0xe958dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe958e0: stur            x2, [x0, #7]
    // 0xe958e4: r1 = 60
    //     0xe958e4: movz            x1, #0x3c
    // 0xe958e8: branchIfSmi(r0, 0xe958f4)
    //     0xe958e8: tbz             w0, #0, #0xe958f4
    // 0xe958ec: r1 = LoadClassIdInstr(r0)
    //     0xe958ec: ldur            x1, [x0, #-1]
    //     0xe958f0: ubfx            x1, x1, #0xc, #0x14
    // 0xe958f4: cmp             x1, #0x3e
    // 0xe958f8: b.ne            #0xe95918
    // 0xe958fc: LoadField: d0 = r0->field_7
    //     0xe958fc: ldur            d0, [x0, #7]
    // 0xe95900: fcmp            d0, d0
    // 0xe95904: b.vc            #0xe95910
    // 0xe95908: mov             x1, x0
    // 0xe9590c: b               #0xe9591c
    // 0xe95910: r1 = 2
    //     0xe95910: movz            x1, #0x2
    // 0xe95914: b               #0xe9591c
    // 0xe95918: r1 = 2
    //     0xe95918: movz            x1, #0x2
    // 0xe9591c: r0 = LoadInt32Instr(r1)
    //     0xe9591c: sbfx            x0, x1, #1, #0x1f
    //     0xe95920: tbz             w1, #0, #0xe95928
    //     0xe95924: ldur            x0, [x1, #7]
    // 0xe95928: LeaveFrame
    //     0xe95928: mov             SP, fp
    //     0xe9592c: ldp             fp, lr, [SP], #0x10
    // 0xe95930: ret
    //     0xe95930: ret             
    // 0xe95934: r0 = StackOverflowSharedWithFPURegs()
    //     0xe95934: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe95938: b               #0xe95524
    // 0xe9593c: r0 = StackOverflowSharedWithFPURegs()
    //     0xe9593c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xe95940: b               #0xe9557c
    // 0xe95944: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe95944: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe95948: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe95948: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe9594c: SaveReg d0
    //     0xe9594c: str             q0, [SP, #-0x10]!
    // 0xe95950: stp             x1, x3, [SP, #-0x10]!
    // 0xe95954: r0 = AllocateDouble()
    //     0xe95954: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95958: ldp             x1, x3, [SP], #0x10
    // 0xe9595c: RestoreReg d0
    //     0xe9595c: ldr             q0, [SP], #0x10
    // 0xe95960: b               #0xe9568c
    // 0xe95964: stp             q9, q10, [SP, #-0x20]!
    // 0xe95968: stp             q7, q8, [SP, #-0x20]!
    // 0xe9596c: stp             q5, q6, [SP, #-0x20]!
    // 0xe95970: stp             q3, q4, [SP, #-0x20]!
    // 0xe95974: SaveReg d1
    //     0xe95974: str             q1, [SP, #-0x10]!
    // 0xe95978: SaveReg r1
    //     0xe95978: str             x1, [SP, #-8]!
    // 0xe9597c: r0 = AllocateDouble()
    //     0xe9597c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95980: RestoreReg r1
    //     0xe95980: ldr             x1, [SP], #8
    // 0xe95984: RestoreReg d1
    //     0xe95984: ldr             q1, [SP], #0x10
    // 0xe95988: ldp             q3, q4, [SP], #0x20
    // 0xe9598c: ldp             q5, q6, [SP], #0x20
    // 0xe95990: ldp             q7, q8, [SP], #0x20
    // 0xe95994: ldp             q9, q10, [SP], #0x20
    // 0xe95998: b               #0xe9572c
    // 0xe9599c: stp             q9, q10, [SP, #-0x20]!
    // 0xe959a0: stp             q7, q8, [SP, #-0x20]!
    // 0xe959a4: stp             q5, q6, [SP, #-0x20]!
    // 0xe959a8: stp             q1, q4, [SP, #-0x20]!
    // 0xe959ac: stp             x0, x1, [SP, #-0x10]!
    // 0xe959b0: r0 = AllocateDouble()
    //     0xe959b0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe959b4: mov             x2, x0
    // 0xe959b8: ldp             x0, x1, [SP], #0x10
    // 0xe959bc: ldp             q1, q4, [SP], #0x20
    // 0xe959c0: ldp             q5, q6, [SP], #0x20
    // 0xe959c4: ldp             q7, q8, [SP], #0x20
    // 0xe959c8: ldp             q9, q10, [SP], #0x20
    // 0xe959cc: b               #0xe95754
    // 0xe959d0: stp             q9, q10, [SP, #-0x20]!
    // 0xe959d4: stp             q7, q8, [SP, #-0x20]!
    // 0xe959d8: stp             q5, q6, [SP, #-0x20]!
    // 0xe959dc: SaveReg d1
    //     0xe959dc: str             q1, [SP, #-0x10]!
    // 0xe959e0: stp             x1, x2, [SP, #-0x10]!
    // 0xe959e4: SaveReg r0
    //     0xe959e4: str             x0, [SP, #-8]!
    // 0xe959e8: r0 = AllocateDouble()
    //     0xe959e8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe959ec: mov             x3, x0
    // 0xe959f0: RestoreReg r0
    //     0xe959f0: ldr             x0, [SP], #8
    // 0xe959f4: ldp             x1, x2, [SP], #0x10
    // 0xe959f8: RestoreReg d1
    //     0xe959f8: ldr             q1, [SP], #0x10
    // 0xe959fc: ldp             q5, q6, [SP], #0x20
    // 0xe95a00: ldp             q7, q8, [SP], #0x20
    // 0xe95a04: ldp             q9, q10, [SP], #0x20
    // 0xe95a08: b               #0xe9577c
    // 0xe95a0c: stp             q9, q10, [SP, #-0x20]!
    // 0xe95a10: stp             q7, q8, [SP, #-0x20]!
    // 0xe95a14: stp             q1, q6, [SP, #-0x20]!
    // 0xe95a18: stp             x2, x3, [SP, #-0x10]!
    // 0xe95a1c: stp             x0, x1, [SP, #-0x10]!
    // 0xe95a20: r0 = AllocateDouble()
    //     0xe95a20: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95a24: mov             x4, x0
    // 0xe95a28: ldp             x0, x1, [SP], #0x10
    // 0xe95a2c: ldp             x2, x3, [SP], #0x10
    // 0xe95a30: ldp             q1, q6, [SP], #0x20
    // 0xe95a34: ldp             q7, q8, [SP], #0x20
    // 0xe95a38: ldp             q9, q10, [SP], #0x20
    // 0xe95a3c: b               #0xe957a4
    // 0xe95a40: stp             q9, q10, [SP, #-0x20]!
    // 0xe95a44: stp             q7, q8, [SP, #-0x20]!
    // 0xe95a48: SaveReg d1
    //     0xe95a48: str             q1, [SP, #-0x10]!
    // 0xe95a4c: stp             x3, x4, [SP, #-0x10]!
    // 0xe95a50: stp             x1, x2, [SP, #-0x10]!
    // 0xe95a54: SaveReg r0
    //     0xe95a54: str             x0, [SP, #-8]!
    // 0xe95a58: r0 = AllocateDouble()
    //     0xe95a58: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95a5c: mov             x5, x0
    // 0xe95a60: RestoreReg r0
    //     0xe95a60: ldr             x0, [SP], #8
    // 0xe95a64: ldp             x1, x2, [SP], #0x10
    // 0xe95a68: ldp             x3, x4, [SP], #0x10
    // 0xe95a6c: RestoreReg d1
    //     0xe95a6c: ldr             q1, [SP], #0x10
    // 0xe95a70: ldp             q7, q8, [SP], #0x20
    // 0xe95a74: ldp             q9, q10, [SP], #0x20
    // 0xe95a78: b               #0xe957cc
    // 0xe95a7c: stp             q9, q10, [SP, #-0x20]!
    // 0xe95a80: stp             q1, q8, [SP, #-0x20]!
    // 0xe95a84: stp             x4, x5, [SP, #-0x10]!
    // 0xe95a88: stp             x2, x3, [SP, #-0x10]!
    // 0xe95a8c: stp             x0, x1, [SP, #-0x10]!
    // 0xe95a90: r0 = AllocateDouble()
    //     0xe95a90: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95a94: mov             x6, x0
    // 0xe95a98: ldp             x0, x1, [SP], #0x10
    // 0xe95a9c: ldp             x2, x3, [SP], #0x10
    // 0xe95aa0: ldp             x4, x5, [SP], #0x10
    // 0xe95aa4: ldp             q1, q8, [SP], #0x20
    // 0xe95aa8: ldp             q9, q10, [SP], #0x20
    // 0xe95aac: b               #0xe957f4
    // 0xe95ab0: stp             q9, q10, [SP, #-0x20]!
    // 0xe95ab4: SaveReg d1
    //     0xe95ab4: str             q1, [SP, #-0x10]!
    // 0xe95ab8: stp             x5, x6, [SP, #-0x10]!
    // 0xe95abc: stp             x3, x4, [SP, #-0x10]!
    // 0xe95ac0: stp             x1, x2, [SP, #-0x10]!
    // 0xe95ac4: SaveReg r0
    //     0xe95ac4: str             x0, [SP, #-8]!
    // 0xe95ac8: r0 = AllocateDouble()
    //     0xe95ac8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe95acc: mov             x7, x0
    // 0xe95ad0: RestoreReg r0
    //     0xe95ad0: ldr             x0, [SP], #8
    // 0xe95ad4: ldp             x1, x2, [SP], #0x10
    // 0xe95ad8: ldp             x3, x4, [SP], #0x10
    // 0xe95adc: ldp             x5, x6, [SP], #0x10
    // 0xe95ae0: RestoreReg d1
    //     0xe95ae0: ldr             q1, [SP], #0x10
    // 0xe95ae4: ldp             q9, q10, [SP], #0x20
    // 0xe95ae8: b               #0xe9581c
  }
}

// class id: 798, size: 0x4c, field offset: 0x4c
class Text extends RichText {

  _ Text(/* No info */) {
    // ** addr: 0xb135fc, size: 0xac
    // 0xb135fc: EnterFrame
    //     0xb135fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb13600: mov             fp, SP
    // 0xb13604: AllocStack(0x30)
    //     0xb13604: sub             SP, SP, #0x30
    // 0xb13608: SetupParameters(Text this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, {dynamic textAlign = Null /* r0, fp-0x8 */})
    //     0xb13608: stur            x1, [fp, #-0x10]
    //     0xb1360c: stur            x2, [fp, #-0x18]
    //     0xb13610: stur            x3, [fp, #-0x20]
    //     0xb13614: ldur            w0, [x4, #0x13]
    //     0xb13618: ldur            w5, [x4, #0x1f]
    //     0xb1361c: add             x5, x5, HEAP, lsl #32
    //     0xb13620: ldr             x16, [PP, #0x4828]  ; [pp+0x4828] "textAlign"
    //     0xb13624: cmp             w5, w16
    //     0xb13628: b.ne            #0xb13644
    //     0xb1362c: ldur            w5, [x4, #0x23]
    //     0xb13630: add             x5, x5, HEAP, lsl #32
    //     0xb13634: sub             w4, w0, w5
    //     0xb13638: add             x0, fp, w4, sxtw #2
    //     0xb1363c: ldr             x0, [x0, #8]
    //     0xb13640: b               #0xb13648
    //     0xb13644: mov             x0, NULL
    //     0xb13648: stur            x0, [fp, #-8]
    // 0xb1364c: CheckStackOverflow
    //     0xb1364c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb13650: cmp             SP, x16
    //     0xb13654: b.ls            #0xb136a0
    // 0xb13658: r0 = TextSpan()
    //     0xb13658: bl              #0xb125a8  ; AllocateTextSpanStub -> TextSpan (size=0x20)
    // 0xb1365c: mov             x1, x0
    // 0xb13660: ldur            x0, [fp, #-0x18]
    // 0xb13664: ArrayStore: r1[0] = r0  ; List_4
    //     0xb13664: stur            w0, [x1, #0x17]
    // 0xb13668: ldur            x0, [fp, #-0x20]
    // 0xb1366c: StoreField: r1->field_7 = r0
    //     0xb1366c: stur            w0, [x1, #7]
    // 0xb13670: StoreField: r1->field_b = rZR
    //     0xb13670: stur            xzr, [x1, #0xb]
    // 0xb13674: ldur            x16, [fp, #-8]
    // 0xb13678: stp             NULL, x16, [SP]
    // 0xb1367c: mov             x2, x1
    // 0xb13680: ldur            x1, [fp, #-0x10]
    // 0xb13684: r4 = const [0, 0x4, 0x2, 0x2, overflow, 0x3, textAlign, 0x2, null]
    //     0xb13684: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e298] List(9) [0, 0x4, 0x2, 0x2, "overflow", 0x3, "textAlign", 0x2, Null]
    //     0xb13688: ldr             x4, [x4, #0x298]
    // 0xb1368c: r0 = RichText()
    //     0xb1368c: bl              #0xb1235c  ; [package:pdf/src/widgets/text.dart] RichText::RichText
    // 0xb13690: r0 = Null
    //     0xb13690: mov             x0, NULL
    // 0xb13694: LeaveFrame
    //     0xb13694: mov             SP, fp
    //     0xb13698: ldp             fp, lr, [SP], #0x10
    // 0xb1369c: ret
    //     0xb1369c: ret             
    // 0xb136a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb136a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb136a4: b               #0xb13658
  }
}

// class id: 821, size: 0x28, field offset: 0x8
class RichTextContext extends WidgetContext {

  _ toString(/* No info */) {
    // ** addr: 0xc36764, size: 0x1dc
    // 0xc36764: EnterFrame
    //     0xc36764: stp             fp, lr, [SP, #-0x10]!
    //     0xc36768: mov             fp, SP
    // 0xc3676c: AllocStack(0x8)
    //     0xc3676c: sub             SP, SP, #8
    // 0xc36770: CheckStackOverflow
    //     0xc36770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc36774: cmp             SP, x16
    //     0xc36778: b.ls            #0xc36908
    // 0xc3677c: r1 = Null
    //     0xc3677c: mov             x1, NULL
    // 0xc36780: r2 = 18
    //     0xc36780: movz            x2, #0x12
    // 0xc36784: r0 = AllocateArray()
    //     0xc36784: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc36788: mov             x2, x0
    // 0xc3678c: r16 = RichTextContext
    //     0xc3678c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33728] Type: RichTextContext
    //     0xc36790: ldr             x16, [x16, #0x728]
    // 0xc36794: StoreField: r2->field_f = r16
    //     0xc36794: stur            w16, [x2, #0xf]
    // 0xc36798: r16 = " Offset: "
    //     0xc36798: add             x16, PP, #0x33, lsl #12  ; [pp+0x33730] " Offset: "
    //     0xc3679c: ldr             x16, [x16, #0x730]
    // 0xc367a0: StoreField: r2->field_13 = r16
    //     0xc367a0: stur            w16, [x2, #0x13]
    // 0xc367a4: ldr             x3, [fp, #0x10]
    // 0xc367a8: LoadField: d0 = r3->field_7
    //     0xc367a8: ldur            d0, [x3, #7]
    // 0xc367ac: r0 = inline_Allocate_Double()
    //     0xc367ac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc367b0: add             x0, x0, #0x10
    //     0xc367b4: cmp             x1, x0
    //     0xc367b8: b.ls            #0xc36910
    //     0xc367bc: str             x0, [THR, #0x50]  ; THR::top
    //     0xc367c0: sub             x0, x0, #0xf
    //     0xc367c4: movz            x1, #0xe15c
    //     0xc367c8: movk            x1, #0x3, lsl #16
    //     0xc367cc: stur            x1, [x0, #-1]
    // 0xc367d0: StoreField: r0->field_7 = d0
    //     0xc367d0: stur            d0, [x0, #7]
    // 0xc367d4: mov             x1, x2
    // 0xc367d8: ArrayStore: r1[2] = r0  ; List_4
    //     0xc367d8: add             x25, x1, #0x17
    //     0xc367dc: str             w0, [x25]
    //     0xc367e0: tbz             w0, #0, #0xc367fc
    //     0xc367e4: ldurb           w16, [x1, #-1]
    //     0xc367e8: ldurb           w17, [x0, #-1]
    //     0xc367ec: and             x16, x17, x16, lsr #2
    //     0xc367f0: tst             x16, HEAP, lsr #32
    //     0xc367f4: b.eq            #0xc367fc
    //     0xc367f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc367fc: r16 = " -> "
    //     0xc367fc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33738] " -> "
    //     0xc36800: ldr             x16, [x16, #0x738]
    // 0xc36804: StoreField: r2->field_1b = r16
    //     0xc36804: stur            w16, [x2, #0x1b]
    // 0xc36808: LoadField: d0 = r3->field_f
    //     0xc36808: ldur            d0, [x3, #0xf]
    // 0xc3680c: r0 = inline_Allocate_Double()
    //     0xc3680c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc36810: add             x0, x0, #0x10
    //     0xc36814: cmp             x1, x0
    //     0xc36818: b.ls            #0xc36928
    //     0xc3681c: str             x0, [THR, #0x50]  ; THR::top
    //     0xc36820: sub             x0, x0, #0xf
    //     0xc36824: movz            x1, #0xe15c
    //     0xc36828: movk            x1, #0x3, lsl #16
    //     0xc3682c: stur            x1, [x0, #-1]
    // 0xc36830: StoreField: r0->field_7 = d0
    //     0xc36830: stur            d0, [x0, #7]
    // 0xc36834: mov             x1, x2
    // 0xc36838: ArrayStore: r1[4] = r0  ; List_4
    //     0xc36838: add             x25, x1, #0x1f
    //     0xc3683c: str             w0, [x25]
    //     0xc36840: tbz             w0, #0, #0xc3685c
    //     0xc36844: ldurb           w16, [x1, #-1]
    //     0xc36848: ldurb           w17, [x0, #-1]
    //     0xc3684c: and             x16, x17, x16, lsr #2
    //     0xc36850: tst             x16, HEAP, lsr #32
    //     0xc36854: b.eq            #0xc3685c
    //     0xc36858: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3685c: r16 = "  Span: "
    //     0xc3685c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33740] "  Span: "
    //     0xc36860: ldr             x16, [x16, #0x740]
    // 0xc36864: StoreField: r2->field_23 = r16
    //     0xc36864: stur            w16, [x2, #0x23]
    // 0xc36868: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xc36868: ldur            x4, [x3, #0x17]
    // 0xc3686c: r0 = BoxInt64Instr(r4)
    //     0xc3686c: sbfiz           x0, x4, #1, #0x1f
    //     0xc36870: cmp             x4, x0, asr #1
    //     0xc36874: b.eq            #0xc36880
    //     0xc36878: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3687c: stur            x4, [x0, #7]
    // 0xc36880: mov             x1, x2
    // 0xc36884: ArrayStore: r1[6] = r0  ; List_4
    //     0xc36884: add             x25, x1, #0x27
    //     0xc36888: str             w0, [x25]
    //     0xc3688c: tbz             w0, #0, #0xc368a8
    //     0xc36890: ldurb           w16, [x1, #-1]
    //     0xc36894: ldurb           w17, [x0, #-1]
    //     0xc36898: and             x16, x17, x16, lsr #2
    //     0xc3689c: tst             x16, HEAP, lsr #32
    //     0xc368a0: b.eq            #0xc368a8
    //     0xc368a4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc368a8: r16 = " -> "
    //     0xc368a8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33738] " -> "
    //     0xc368ac: ldr             x16, [x16, #0x738]
    // 0xc368b0: StoreField: r2->field_2b = r16
    //     0xc368b0: stur            w16, [x2, #0x2b]
    // 0xc368b4: LoadField: r4 = r3->field_1f
    //     0xc368b4: ldur            x4, [x3, #0x1f]
    // 0xc368b8: r0 = BoxInt64Instr(r4)
    //     0xc368b8: sbfiz           x0, x4, #1, #0x1f
    //     0xc368bc: cmp             x4, x0, asr #1
    //     0xc368c0: b.eq            #0xc368cc
    //     0xc368c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc368c8: stur            x4, [x0, #7]
    // 0xc368cc: mov             x1, x2
    // 0xc368d0: ArrayStore: r1[8] = r0  ; List_4
    //     0xc368d0: add             x25, x1, #0x2f
    //     0xc368d4: str             w0, [x25]
    //     0xc368d8: tbz             w0, #0, #0xc368f4
    //     0xc368dc: ldurb           w16, [x1, #-1]
    //     0xc368e0: ldurb           w17, [x0, #-1]
    //     0xc368e4: and             x16, x17, x16, lsr #2
    //     0xc368e8: tst             x16, HEAP, lsr #32
    //     0xc368ec: b.eq            #0xc368f4
    //     0xc368f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc368f4: str             x2, [SP]
    // 0xc368f8: r0 = _interpolate()
    //     0xc368f8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc368fc: LeaveFrame
    //     0xc368fc: mov             SP, fp
    //     0xc36900: ldp             fp, lr, [SP], #0x10
    // 0xc36904: ret
    //     0xc36904: ret             
    // 0xc36908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc36908: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3690c: b               #0xc3677c
    // 0xc36910: SaveReg d0
    //     0xc36910: str             q0, [SP, #-0x10]!
    // 0xc36914: stp             x2, x3, [SP, #-0x10]!
    // 0xc36918: r0 = AllocateDouble()
    //     0xc36918: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3691c: ldp             x2, x3, [SP], #0x10
    // 0xc36920: RestoreReg d0
    //     0xc36920: ldr             q0, [SP], #0x10
    // 0xc36924: b               #0xc367d0
    // 0xc36928: SaveReg d0
    //     0xc36928: str             q0, [SP, #-0x10]!
    // 0xc3692c: stp             x2, x3, [SP, #-0x10]!
    // 0xc36930: r0 = AllocateDouble()
    //     0xc36930: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc36934: ldp             x2, x3, [SP], #0x10
    // 0xc36938: RestoreReg d0
    //     0xc36938: ldr             q0, [SP], #0x10
    // 0xc3693c: b               #0xc36830
  }
  _ apply(/* No info */) {
    // ** addr: 0xeabf54, size: 0x8c
    // 0xeabf54: EnterFrame
    //     0xeabf54: stp             fp, lr, [SP, #-0x10]!
    //     0xeabf58: mov             fp, SP
    // 0xeabf5c: AllocStack(0x10)
    //     0xeabf5c: sub             SP, SP, #0x10
    // 0xeabf60: SetupParameters(RichTextContext this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xeabf60: mov             x0, x2
    //     0xeabf64: mov             x4, x1
    //     0xeabf68: mov             x3, x2
    //     0xeabf6c: stur            x1, [fp, #-8]
    //     0xeabf70: stur            x2, [fp, #-0x10]
    // 0xeabf74: r2 = Null
    //     0xeabf74: mov             x2, NULL
    // 0xeabf78: r1 = Null
    //     0xeabf78: mov             x1, NULL
    // 0xeabf7c: r4 = 60
    //     0xeabf7c: movz            x4, #0x3c
    // 0xeabf80: branchIfSmi(r0, 0xeabf8c)
    //     0xeabf80: tbz             w0, #0, #0xeabf8c
    // 0xeabf84: r4 = LoadClassIdInstr(r0)
    //     0xeabf84: ldur            x4, [x0, #-1]
    //     0xeabf88: ubfx            x4, x4, #0xc, #0x14
    // 0xeabf8c: cmp             x4, #0x335
    // 0xeabf90: b.eq            #0xeabfa8
    // 0xeabf94: r8 = RichTextContext
    //     0xeabf94: add             x8, PP, #0x33, lsl #12  ; [pp+0x33728] Type: RichTextContext
    //     0xeabf98: ldr             x8, [x8, #0x728]
    // 0xeabf9c: r3 = Null
    //     0xeabf9c: add             x3, PP, #0x36, lsl #12  ; [pp+0x36800] Null
    //     0xeabfa0: ldr             x3, [x3, #0x800]
    // 0xeabfa4: r0 = DefaultTypeTest()
    //     0xeabfa4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xeabfa8: ldur            x1, [fp, #-0x10]
    // 0xeabfac: LoadField: d0 = r1->field_7
    //     0xeabfac: ldur            d0, [x1, #7]
    // 0xeabfb0: ldur            x2, [fp, #-8]
    // 0xeabfb4: StoreField: r2->field_7 = d0
    //     0xeabfb4: stur            d0, [x2, #7]
    // 0xeabfb8: LoadField: d0 = r1->field_f
    //     0xeabfb8: ldur            d0, [x1, #0xf]
    // 0xeabfbc: StoreField: r2->field_f = d0
    //     0xeabfbc: stur            d0, [x2, #0xf]
    // 0xeabfc0: ArrayLoad: r3 = r1[0]  ; List_8
    //     0xeabfc0: ldur            x3, [x1, #0x17]
    // 0xeabfc4: ArrayStore: r2[0] = r3  ; List_8
    //     0xeabfc4: stur            x3, [x2, #0x17]
    // 0xeabfc8: LoadField: r3 = r1->field_1f
    //     0xeabfc8: ldur            x3, [x1, #0x1f]
    // 0xeabfcc: StoreField: r2->field_1f = r3
    //     0xeabfcc: stur            x3, [x2, #0x1f]
    // 0xeabfd0: r0 = Null
    //     0xeabfd0: mov             x0, NULL
    // 0xeabfd4: LeaveFrame
    //     0xeabfd4: mov             SP, fp
    //     0xeabfd8: ldp             fp, lr, [SP], #0x10
    // 0xeabfdc: ret
    //     0xeabfdc: ret             
  }
}

// class id: 6789, size: 0x14, field offset: 0x14
enum TextOverflow extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e310, size: 0x64
    // 0xc4e310: EnterFrame
    //     0xc4e310: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e314: mov             fp, SP
    // 0xc4e318: AllocStack(0x10)
    //     0xc4e318: sub             SP, SP, #0x10
    // 0xc4e31c: SetupParameters(TextOverflow this /* r1 => r0, fp-0x8 */)
    //     0xc4e31c: mov             x0, x1
    //     0xc4e320: stur            x1, [fp, #-8]
    // 0xc4e324: CheckStackOverflow
    //     0xc4e324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e328: cmp             SP, x16
    //     0xc4e32c: b.ls            #0xc4e36c
    // 0xc4e330: r1 = Null
    //     0xc4e330: mov             x1, NULL
    // 0xc4e334: r2 = 4
    //     0xc4e334: movz            x2, #0x4
    // 0xc4e338: r0 = AllocateArray()
    //     0xc4e338: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e33c: r16 = "TextOverflow."
    //     0xc4e33c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33748] "TextOverflow."
    //     0xc4e340: ldr             x16, [x16, #0x748]
    // 0xc4e344: StoreField: r0->field_f = r16
    //     0xc4e344: stur            w16, [x0, #0xf]
    // 0xc4e348: ldur            x1, [fp, #-8]
    // 0xc4e34c: LoadField: r2 = r1->field_f
    //     0xc4e34c: ldur            w2, [x1, #0xf]
    // 0xc4e350: DecompressPointer r2
    //     0xc4e350: add             x2, x2, HEAP, lsl #32
    // 0xc4e354: StoreField: r0->field_13 = r2
    //     0xc4e354: stur            w2, [x0, #0x13]
    // 0xc4e358: str             x0, [SP]
    // 0xc4e35c: r0 = _interpolate()
    //     0xc4e35c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e360: LeaveFrame
    //     0xc4e360: mov             SP, fp
    //     0xc4e364: ldp             fp, lr, [SP], #0x10
    // 0xc4e368: ret
    //     0xc4e368: ret             
    // 0xc4e36c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e36c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e370: b               #0xc4e330
  }
}

// class id: 6790, size: 0x14, field offset: 0x14
enum TextDirection extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e2ac, size: 0x64
    // 0xc4e2ac: EnterFrame
    //     0xc4e2ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e2b0: mov             fp, SP
    // 0xc4e2b4: AllocStack(0x10)
    //     0xc4e2b4: sub             SP, SP, #0x10
    // 0xc4e2b8: SetupParameters(TextDirection this /* r1 => r0, fp-0x8 */)
    //     0xc4e2b8: mov             x0, x1
    //     0xc4e2bc: stur            x1, [fp, #-8]
    // 0xc4e2c0: CheckStackOverflow
    //     0xc4e2c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e2c4: cmp             SP, x16
    //     0xc4e2c8: b.ls            #0xc4e308
    // 0xc4e2cc: r1 = Null
    //     0xc4e2cc: mov             x1, NULL
    // 0xc4e2d0: r2 = 4
    //     0xc4e2d0: movz            x2, #0x4
    // 0xc4e2d4: r0 = AllocateArray()
    //     0xc4e2d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e2d8: r16 = "TextDirection."
    //     0xc4e2d8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22850] "TextDirection."
    //     0xc4e2dc: ldr             x16, [x16, #0x850]
    // 0xc4e2e0: StoreField: r0->field_f = r16
    //     0xc4e2e0: stur            w16, [x0, #0xf]
    // 0xc4e2e4: ldur            x1, [fp, #-8]
    // 0xc4e2e8: LoadField: r2 = r1->field_f
    //     0xc4e2e8: ldur            w2, [x1, #0xf]
    // 0xc4e2ec: DecompressPointer r2
    //     0xc4e2ec: add             x2, x2, HEAP, lsl #32
    // 0xc4e2f0: StoreField: r0->field_13 = r2
    //     0xc4e2f0: stur            w2, [x0, #0x13]
    // 0xc4e2f4: str             x0, [SP]
    // 0xc4e2f8: r0 = _interpolate()
    //     0xc4e2f8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e2fc: LeaveFrame
    //     0xc4e2fc: mov             SP, fp
    //     0xc4e300: ldp             fp, lr, [SP], #0x10
    // 0xc4e304: ret
    //     0xc4e304: ret             
    // 0xc4e308: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e308: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e30c: b               #0xc4e2cc
  }
}

// class id: 6791, size: 0x14, field offset: 0x14
enum TextAlign extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e248, size: 0x64
    // 0xc4e248: EnterFrame
    //     0xc4e248: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e24c: mov             fp, SP
    // 0xc4e250: AllocStack(0x10)
    //     0xc4e250: sub             SP, SP, #0x10
    // 0xc4e254: SetupParameters(TextAlign this /* r1 => r0, fp-0x8 */)
    //     0xc4e254: mov             x0, x1
    //     0xc4e258: stur            x1, [fp, #-8]
    // 0xc4e25c: CheckStackOverflow
    //     0xc4e25c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e260: cmp             SP, x16
    //     0xc4e264: b.ls            #0xc4e2a4
    // 0xc4e268: r1 = Null
    //     0xc4e268: mov             x1, NULL
    // 0xc4e26c: r2 = 4
    //     0xc4e26c: movz            x2, #0x4
    // 0xc4e270: r0 = AllocateArray()
    //     0xc4e270: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e274: r16 = "TextAlign."
    //     0xc4e274: add             x16, PP, #0x22, lsl #12  ; [pp+0x22828] "TextAlign."
    //     0xc4e278: ldr             x16, [x16, #0x828]
    // 0xc4e27c: StoreField: r0->field_f = r16
    //     0xc4e27c: stur            w16, [x0, #0xf]
    // 0xc4e280: ldur            x1, [fp, #-8]
    // 0xc4e284: LoadField: r2 = r1->field_f
    //     0xc4e284: ldur            w2, [x1, #0xf]
    // 0xc4e288: DecompressPointer r2
    //     0xc4e288: add             x2, x2, HEAP, lsl #32
    // 0xc4e28c: StoreField: r0->field_13 = r2
    //     0xc4e28c: stur            w2, [x0, #0x13]
    // 0xc4e290: str             x0, [SP]
    // 0xc4e294: r0 = _interpolate()
    //     0xc4e294: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e298: LeaveFrame
    //     0xc4e298: mov             SP, fp
    //     0xc4e29c: ldp             fp, lr, [SP], #0x10
    // 0xc4e2a0: ret
    //     0xc4e2a0: ret             
    // 0xc4e2a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e2a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e2a8: b               #0xc4e268
  }
}
