// lib: , url: package:pdf/src/widgets/text_style.dart

// class id: 1050863, size: 0x8
class :: {
}

// class id: 761, size: 0x8, field offset: 0x8
abstract class Directionality extends Object {

  static _ of(/* No info */) {
    // ** addr: 0xb137c8, size: 0x48
    // 0xb137c8: EnterFrame
    //     0xb137c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb137cc: mov             fp, SP
    // 0xb137d0: AllocStack(0x10)
    //     0xb137d0: sub             SP, SP, #0x10
    // 0xb137d4: CheckStackOverflow
    //     0xb137d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb137d8: cmp             SP, x16
    //     0xb137dc: b.ls            #0xb13808
    // 0xb137e0: r16 = <InheritedDirectionality>
    //     0xb137e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e2d8] TypeArguments: <InheritedDirectionality>
    //     0xb137e4: ldr             x16, [x16, #0x2d8]
    // 0xb137e8: stp             x1, x16, [SP]
    // 0xb137ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb137ec: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb137f0: r0 = dependsOn()
    //     0xb137f0: bl              #0xb12624  ; [package:pdf/src/widgets/widget.dart] Context::dependsOn
    // 0xb137f4: r0 = Instance_TextDirection
    //     0xb137f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e2e0] Obj!TextDirection@e2e5a1
    //     0xb137f8: ldr             x0, [x0, #0x2e0]
    // 0xb137fc: LeaveFrame
    //     0xb137fc: mov             SP, fp
    //     0xb13800: ldp             fp, lr, [SP], #0x10
    // 0xb13804: ret
    //     0xb13804: ret             
    // 0xb13808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb13808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1380c: b               #0xb137e0
  }
}

// class id: 764, size: 0xc, field offset: 0x8
//   const constructor, 
class InheritedDirectionality extends Inherited {
}

// class id: 765, size: 0x58, field offset: 0x8
//   const constructor, 
class TextStyle extends Object {

  _ merge(/* No info */) {
    // ** addr: 0xb0faa8, size: 0x1fc
    // 0xb0faa8: EnterFrame
    //     0xb0faa8: stp             fp, lr, [SP, #-0x10]!
    //     0xb0faac: mov             fp, SP
    // 0xb0fab0: AllocStack(0x110)
    //     0xb0fab0: sub             SP, SP, #0x110
    // 0xb0fab4: SetupParameters(TextStyle this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xb0fab4: mov             x0, x2
    //     0xb0fab8: stur            x2, [fp, #-0x18]
    //     0xb0fabc: mov             x2, x1
    //     0xb0fac0: stur            x1, [fp, #-0x10]
    // 0xb0fac4: CheckStackOverflow
    //     0xb0fac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0fac8: cmp             SP, x16
    //     0xb0facc: b.ls            #0xb0fc9c
    // 0xb0fad0: cmp             w0, NULL
    // 0xb0fad4: b.ne            #0xb0fae8
    // 0xb0fad8: mov             x0, x2
    // 0xb0fadc: LeaveFrame
    //     0xb0fadc: mov             SP, fp
    //     0xb0fae0: ldp             fp, lr, [SP], #0x10
    // 0xb0fae4: ret
    //     0xb0fae4: ret             
    // 0xb0fae8: LoadField: r1 = r0->field_7
    //     0xb0fae8: ldur            w1, [x0, #7]
    // 0xb0faec: DecompressPointer r1
    //     0xb0faec: add             x1, x1, HEAP, lsl #32
    // 0xb0faf0: tbz             w1, #4, #0xb0fb00
    // 0xb0faf4: LeaveFrame
    //     0xb0faf4: mov             SP, fp
    //     0xb0faf8: ldp             fp, lr, [SP], #0x10
    // 0xb0fafc: ret
    //     0xb0fafc: ret             
    // 0xb0fb00: LoadField: r3 = r0->field_b
    //     0xb0fb00: ldur            w3, [x0, #0xb]
    // 0xb0fb04: DecompressPointer r3
    //     0xb0fb04: add             x3, x3, HEAP, lsl #32
    // 0xb0fb08: mov             x1, x0
    // 0xb0fb0c: stur            x3, [fp, #-8]
    // 0xb0fb10: r0 = font()
    //     0xb0fb10: bl              #0xb0fcfc  ; [package:pdf/src/widgets/text_style.dart] TextStyle::font
    // 0xb0fb14: mov             x3, x0
    // 0xb0fb18: ldur            x0, [fp, #-0x18]
    // 0xb0fb1c: stur            x3, [fp, #-0x40]
    // 0xb0fb20: LoadField: r4 = r0->field_f
    //     0xb0fb20: ldur            w4, [x0, #0xf]
    // 0xb0fb24: DecompressPointer r4
    //     0xb0fb24: add             x4, x4, HEAP, lsl #32
    // 0xb0fb28: stur            x4, [fp, #-0x38]
    // 0xb0fb2c: LoadField: r5 = r0->field_13
    //     0xb0fb2c: ldur            w5, [x0, #0x13]
    // 0xb0fb30: DecompressPointer r5
    //     0xb0fb30: add             x5, x5, HEAP, lsl #32
    // 0xb0fb34: stur            x5, [fp, #-0x30]
    // 0xb0fb38: ArrayLoad: r6 = r0[0]  ; List_4
    //     0xb0fb38: ldur            w6, [x0, #0x17]
    // 0xb0fb3c: DecompressPointer r6
    //     0xb0fb3c: add             x6, x6, HEAP, lsl #32
    // 0xb0fb40: stur            x6, [fp, #-0x28]
    // 0xb0fb44: LoadField: r7 = r0->field_1b
    //     0xb0fb44: ldur            w7, [x0, #0x1b]
    // 0xb0fb48: DecompressPointer r7
    //     0xb0fb48: add             x7, x7, HEAP, lsl #32
    // 0xb0fb4c: stur            x7, [fp, #-0x20]
    // 0xb0fb50: LoadField: r2 = r0->field_1f
    //     0xb0fb50: ldur            w2, [x0, #0x1f]
    // 0xb0fb54: DecompressPointer r2
    //     0xb0fb54: add             x2, x2, HEAP, lsl #32
    // 0xb0fb58: r1 = <Font>
    //     0xb0fb58: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e500] TypeArguments: <Font>
    //     0xb0fb5c: ldr             x1, [x1, #0x500]
    // 0xb0fb60: r0 = _GrowableList.of()
    //     0xb0fb60: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb0fb64: mov             x3, x0
    // 0xb0fb68: ldur            x0, [fp, #-0x10]
    // 0xb0fb6c: stur            x3, [fp, #-0x48]
    // 0xb0fb70: LoadField: r2 = r0->field_1f
    //     0xb0fb70: ldur            w2, [x0, #0x1f]
    // 0xb0fb74: DecompressPointer r2
    //     0xb0fb74: add             x2, x2, HEAP, lsl #32
    // 0xb0fb78: mov             x1, x3
    // 0xb0fb7c: r0 = addAll()
    //     0xb0fb7c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb0fb80: ldur            x0, [fp, #-0x18]
    // 0xb0fb84: LoadField: r3 = r0->field_23
    //     0xb0fb84: ldur            w3, [x0, #0x23]
    // 0xb0fb88: DecompressPointer r3
    //     0xb0fb88: add             x3, x3, HEAP, lsl #32
    // 0xb0fb8c: stur            x3, [fp, #-0x80]
    // 0xb0fb90: LoadField: r4 = r0->field_27
    //     0xb0fb90: ldur            w4, [x0, #0x27]
    // 0xb0fb94: DecompressPointer r4
    //     0xb0fb94: add             x4, x4, HEAP, lsl #32
    // 0xb0fb98: stur            x4, [fp, #-0x78]
    // 0xb0fb9c: LoadField: r5 = r0->field_2b
    //     0xb0fb9c: ldur            w5, [x0, #0x2b]
    // 0xb0fba0: DecompressPointer r5
    //     0xb0fba0: add             x5, x5, HEAP, lsl #32
    // 0xb0fba4: stur            x5, [fp, #-0x70]
    // 0xb0fba8: LoadField: r6 = r0->field_2f
    //     0xb0fba8: ldur            w6, [x0, #0x2f]
    // 0xb0fbac: DecompressPointer r6
    //     0xb0fbac: add             x6, x6, HEAP, lsl #32
    // 0xb0fbb0: stur            x6, [fp, #-0x68]
    // 0xb0fbb4: LoadField: r7 = r0->field_37
    //     0xb0fbb4: ldur            w7, [x0, #0x37]
    // 0xb0fbb8: DecompressPointer r7
    //     0xb0fbb8: add             x7, x7, HEAP, lsl #32
    // 0xb0fbbc: stur            x7, [fp, #-0x60]
    // 0xb0fbc0: LoadField: r8 = r0->field_33
    //     0xb0fbc0: ldur            w8, [x0, #0x33]
    // 0xb0fbc4: DecompressPointer r8
    //     0xb0fbc4: add             x8, x8, HEAP, lsl #32
    // 0xb0fbc8: stur            x8, [fp, #-0x58]
    // 0xb0fbcc: LoadField: r9 = r0->field_3b
    //     0xb0fbcc: ldur            w9, [x0, #0x3b]
    // 0xb0fbd0: DecompressPointer r9
    //     0xb0fbd0: add             x9, x9, HEAP, lsl #32
    // 0xb0fbd4: ldur            x10, [fp, #-0x10]
    // 0xb0fbd8: stur            x9, [fp, #-0x50]
    // 0xb0fbdc: LoadField: r1 = r10->field_43
    //     0xb0fbdc: ldur            w1, [x10, #0x43]
    // 0xb0fbe0: DecompressPointer r1
    //     0xb0fbe0: add             x1, x1, HEAP, lsl #32
    // 0xb0fbe4: cmp             w1, NULL
    // 0xb0fbe8: b.ne            #0xb0fbf8
    // 0xb0fbec: LoadField: r1 = r0->field_43
    //     0xb0fbec: ldur            w1, [x0, #0x43]
    // 0xb0fbf0: DecompressPointer r1
    //     0xb0fbf0: add             x1, x1, HEAP, lsl #32
    // 0xb0fbf4: b               #0xb0fc0c
    // 0xb0fbf8: LoadField: r2 = r0->field_43
    //     0xb0fbf8: ldur            w2, [x0, #0x43]
    // 0xb0fbfc: DecompressPointer r2
    //     0xb0fbfc: add             x2, x2, HEAP, lsl #32
    // 0xb0fc00: r0 = merge()
    //     0xb0fc00: bl              #0xb0fca4  ; [package:pdf/src/widgets/text_style.dart] TextDecoration::merge
    // 0xb0fc04: mov             x1, x0
    // 0xb0fc08: ldur            x0, [fp, #-0x18]
    // 0xb0fc0c: LoadField: r2 = r0->field_4b
    //     0xb0fc0c: ldur            w2, [x0, #0x4b]
    // 0xb0fc10: DecompressPointer r2
    //     0xb0fc10: add             x2, x2, HEAP, lsl #32
    // 0xb0fc14: LoadField: r3 = r0->field_4f
    //     0xb0fc14: ldur            w3, [x0, #0x4f]
    // 0xb0fc18: DecompressPointer r3
    //     0xb0fc18: add             x3, x3, HEAP, lsl #32
    // 0xb0fc1c: LoadField: r4 = r0->field_53
    //     0xb0fc1c: ldur            w4, [x0, #0x53]
    // 0xb0fc20: DecompressPointer r4
    //     0xb0fc20: add             x4, x4, HEAP, lsl #32
    // 0xb0fc24: ldur            x16, [fp, #-8]
    // 0xb0fc28: ldur            lr, [fp, #-0x40]
    // 0xb0fc2c: stp             lr, x16, [SP, #0x80]
    // 0xb0fc30: ldur            x16, [fp, #-0x38]
    // 0xb0fc34: ldur            lr, [fp, #-0x30]
    // 0xb0fc38: stp             lr, x16, [SP, #0x70]
    // 0xb0fc3c: ldur            x16, [fp, #-0x28]
    // 0xb0fc40: ldur            lr, [fp, #-0x20]
    // 0xb0fc44: stp             lr, x16, [SP, #0x60]
    // 0xb0fc48: ldur            x16, [fp, #-0x48]
    // 0xb0fc4c: ldur            lr, [fp, #-0x80]
    // 0xb0fc50: stp             lr, x16, [SP, #0x50]
    // 0xb0fc54: ldur            x16, [fp, #-0x78]
    // 0xb0fc58: ldur            lr, [fp, #-0x70]
    // 0xb0fc5c: stp             lr, x16, [SP, #0x40]
    // 0xb0fc60: ldur            x16, [fp, #-0x68]
    // 0xb0fc64: ldur            lr, [fp, #-0x60]
    // 0xb0fc68: stp             lr, x16, [SP, #0x30]
    // 0xb0fc6c: ldur            x16, [fp, #-0x58]
    // 0xb0fc70: ldur            lr, [fp, #-0x50]
    // 0xb0fc74: stp             lr, x16, [SP, #0x20]
    // 0xb0fc78: stp             x2, x1, [SP, #0x10]
    // 0xb0fc7c: stp             x4, x3, [SP]
    // 0xb0fc80: ldur            x1, [fp, #-0x10]
    // 0xb0fc84: r4 = const [0, 0x13, 0x12, 0x1, color, 0x1, decoration, 0xf, decorationStyle, 0x10, decorationThickness, 0x11, font, 0x2, fontBold, 0x4, fontBoldItalic, 0x6, fontFallback, 0x7, fontItalic, 0x5, fontNormal, 0x3, fontSize, 0x8, fontStyle, 0xa, fontWeight, 0x9, height, 0xe, letterSpacing, 0xb, lineSpacing, 0xd, renderingMode, 0x12, wordSpacing, 0xc, null]
    //     0xb0fc84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e508] List(41) [0, 0x13, 0x12, 0x1, "color", 0x1, "decoration", 0xf, "decorationStyle", 0x10, "decorationThickness", 0x11, "font", 0x2, "fontBold", 0x4, "fontBoldItalic", 0x6, "fontFallback", 0x7, "fontItalic", 0x5, "fontNormal", 0x3, "fontSize", 0x8, "fontStyle", 0xa, "fontWeight", 0x9, "height", 0xe, "letterSpacing", 0xb, "lineSpacing", 0xd, "renderingMode", 0x12, "wordSpacing", 0xc, Null]
    //     0xb0fc88: ldr             x4, [x4, #0x508]
    // 0xb0fc8c: r0 = copyWith()
    //     0xb0fc8c: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb0fc90: LeaveFrame
    //     0xb0fc90: mov             SP, fp
    //     0xb0fc94: ldp             fp, lr, [SP], #0x10
    // 0xb0fc98: ret
    //     0xb0fc98: ret             
    // 0xb0fc9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0fc9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0fca0: b               #0xb0fad0
  }
  get _ font(/* No info */) {
    // ** addr: 0xb0fcfc, size: 0x188
    // 0xb0fcfc: LoadField: r2 = r1->field_27
    //     0xb0fcfc: ldur            w2, [x1, #0x27]
    // 0xb0fd00: DecompressPointer r2
    //     0xb0fd00: add             x2, x2, HEAP, lsl #32
    // 0xb0fd04: r16 = Instance_FontWeight
    //     0xb0fd04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e198] Obj!FontWeight@e2e541
    //     0xb0fd08: ldr             x16, [x16, #0x198]
    // 0xb0fd0c: cmp             w2, w16
    // 0xb0fd10: b.eq            #0xb0fdcc
    // 0xb0fd14: LoadField: r2 = r1->field_2b
    //     0xb0fd14: ldur            w2, [x1, #0x2b]
    // 0xb0fd18: DecompressPointer r2
    //     0xb0fd18: add             x2, x2, HEAP, lsl #32
    // 0xb0fd1c: r16 = Instance_FontStyle
    //     0xb0fd1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e510] Obj!FontStyle@e2e4e1
    //     0xb0fd20: ldr             x16, [x16, #0x510]
    // 0xb0fd24: cmp             w2, w16
    // 0xb0fd28: b.eq            #0xb0fd7c
    // 0xb0fd2c: LoadField: r2 = r1->field_f
    //     0xb0fd2c: ldur            w2, [x1, #0xf]
    // 0xb0fd30: DecompressPointer r2
    //     0xb0fd30: add             x2, x2, HEAP, lsl #32
    // 0xb0fd34: cmp             w2, NULL
    // 0xb0fd38: b.ne            #0xb0fd48
    // 0xb0fd3c: LoadField: r3 = r1->field_13
    //     0xb0fd3c: ldur            w3, [x1, #0x13]
    // 0xb0fd40: DecompressPointer r3
    //     0xb0fd40: add             x3, x3, HEAP, lsl #32
    // 0xb0fd44: mov             x2, x3
    // 0xb0fd48: cmp             w2, NULL
    // 0xb0fd4c: b.ne            #0xb0fd5c
    // 0xb0fd50: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb0fd50: ldur            w3, [x1, #0x17]
    // 0xb0fd54: DecompressPointer r3
    //     0xb0fd54: add             x3, x3, HEAP, lsl #32
    // 0xb0fd58: mov             x2, x3
    // 0xb0fd5c: cmp             w2, NULL
    // 0xb0fd60: b.ne            #0xb0fd74
    // 0xb0fd64: LoadField: r3 = r1->field_1b
    //     0xb0fd64: ldur            w3, [x1, #0x1b]
    // 0xb0fd68: DecompressPointer r3
    //     0xb0fd68: add             x3, x3, HEAP, lsl #32
    // 0xb0fd6c: mov             x0, x3
    // 0xb0fd70: b               #0xb0fd78
    // 0xb0fd74: mov             x0, x2
    // 0xb0fd78: ret
    //     0xb0fd78: ret             
    // 0xb0fd7c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb0fd7c: ldur            w2, [x1, #0x17]
    // 0xb0fd80: DecompressPointer r2
    //     0xb0fd80: add             x2, x2, HEAP, lsl #32
    // 0xb0fd84: cmp             w2, NULL
    // 0xb0fd88: b.ne            #0xb0fd98
    // 0xb0fd8c: LoadField: r3 = r1->field_f
    //     0xb0fd8c: ldur            w3, [x1, #0xf]
    // 0xb0fd90: DecompressPointer r3
    //     0xb0fd90: add             x3, x3, HEAP, lsl #32
    // 0xb0fd94: mov             x2, x3
    // 0xb0fd98: cmp             w2, NULL
    // 0xb0fd9c: b.ne            #0xb0fdac
    // 0xb0fda0: LoadField: r3 = r1->field_13
    //     0xb0fda0: ldur            w3, [x1, #0x13]
    // 0xb0fda4: DecompressPointer r3
    //     0xb0fda4: add             x3, x3, HEAP, lsl #32
    // 0xb0fda8: mov             x2, x3
    // 0xb0fdac: cmp             w2, NULL
    // 0xb0fdb0: b.ne            #0xb0fdc4
    // 0xb0fdb4: LoadField: r3 = r1->field_1b
    //     0xb0fdb4: ldur            w3, [x1, #0x1b]
    // 0xb0fdb8: DecompressPointer r3
    //     0xb0fdb8: add             x3, x3, HEAP, lsl #32
    // 0xb0fdbc: mov             x0, x3
    // 0xb0fdc0: b               #0xb0fdc8
    // 0xb0fdc4: mov             x0, x2
    // 0xb0fdc8: ret
    //     0xb0fdc8: ret             
    // 0xb0fdcc: LoadField: r2 = r1->field_2b
    //     0xb0fdcc: ldur            w2, [x1, #0x2b]
    // 0xb0fdd0: DecompressPointer r2
    //     0xb0fdd0: add             x2, x2, HEAP, lsl #32
    // 0xb0fdd4: r16 = Instance_FontStyle
    //     0xb0fdd4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e510] Obj!FontStyle@e2e4e1
    //     0xb0fdd8: ldr             x16, [x16, #0x510]
    // 0xb0fddc: cmp             w2, w16
    // 0xb0fde0: b.eq            #0xb0fe34
    // 0xb0fde4: LoadField: r2 = r1->field_13
    //     0xb0fde4: ldur            w2, [x1, #0x13]
    // 0xb0fde8: DecompressPointer r2
    //     0xb0fde8: add             x2, x2, HEAP, lsl #32
    // 0xb0fdec: cmp             w2, NULL
    // 0xb0fdf0: b.ne            #0xb0fe00
    // 0xb0fdf4: LoadField: r3 = r1->field_f
    //     0xb0fdf4: ldur            w3, [x1, #0xf]
    // 0xb0fdf8: DecompressPointer r3
    //     0xb0fdf8: add             x3, x3, HEAP, lsl #32
    // 0xb0fdfc: mov             x2, x3
    // 0xb0fe00: cmp             w2, NULL
    // 0xb0fe04: b.ne            #0xb0fe14
    // 0xb0fe08: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb0fe08: ldur            w3, [x1, #0x17]
    // 0xb0fe0c: DecompressPointer r3
    //     0xb0fe0c: add             x3, x3, HEAP, lsl #32
    // 0xb0fe10: mov             x2, x3
    // 0xb0fe14: cmp             w2, NULL
    // 0xb0fe18: b.ne            #0xb0fe2c
    // 0xb0fe1c: LoadField: r3 = r1->field_1b
    //     0xb0fe1c: ldur            w3, [x1, #0x1b]
    // 0xb0fe20: DecompressPointer r3
    //     0xb0fe20: add             x3, x3, HEAP, lsl #32
    // 0xb0fe24: mov             x0, x3
    // 0xb0fe28: b               #0xb0fe30
    // 0xb0fe2c: mov             x0, x2
    // 0xb0fe30: ret
    //     0xb0fe30: ret             
    // 0xb0fe34: LoadField: r2 = r1->field_1b
    //     0xb0fe34: ldur            w2, [x1, #0x1b]
    // 0xb0fe38: DecompressPointer r2
    //     0xb0fe38: add             x2, x2, HEAP, lsl #32
    // 0xb0fe3c: cmp             w2, NULL
    // 0xb0fe40: b.ne            #0xb0fe50
    // 0xb0fe44: LoadField: r3 = r1->field_13
    //     0xb0fe44: ldur            w3, [x1, #0x13]
    // 0xb0fe48: DecompressPointer r3
    //     0xb0fe48: add             x3, x3, HEAP, lsl #32
    // 0xb0fe4c: mov             x2, x3
    // 0xb0fe50: cmp             w2, NULL
    // 0xb0fe54: b.ne            #0xb0fe64
    // 0xb0fe58: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb0fe58: ldur            w3, [x1, #0x17]
    // 0xb0fe5c: DecompressPointer r3
    //     0xb0fe5c: add             x3, x3, HEAP, lsl #32
    // 0xb0fe60: mov             x2, x3
    // 0xb0fe64: cmp             w2, NULL
    // 0xb0fe68: b.ne            #0xb0fe7c
    // 0xb0fe6c: LoadField: r3 = r1->field_f
    //     0xb0fe6c: ldur            w3, [x1, #0xf]
    // 0xb0fe70: DecompressPointer r3
    //     0xb0fe70: add             x3, x3, HEAP, lsl #32
    // 0xb0fe74: mov             x0, x3
    // 0xb0fe78: b               #0xb0fe80
    // 0xb0fe7c: mov             x0, x2
    // 0xb0fe80: ret
    //     0xb0fe80: ret             
  }
  factory _ TextStyle.defaultStyle(/* No info */) {
    // ** addr: 0xb102d8, size: 0x104
    // 0xb102d8: EnterFrame
    //     0xb102d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb102dc: mov             fp, SP
    // 0xb102e0: AllocStack(0x20)
    //     0xb102e0: sub             SP, SP, #0x20
    // 0xb102e4: CheckStackOverflow
    //     0xb102e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb102e8: cmp             SP, x16
    //     0xb102ec: b.ls            #0xb103d4
    // 0xb102f0: r1 = Null
    //     0xb102f0: mov             x1, NULL
    // 0xb102f4: r0 = Font.helvetica()
    //     0xb102f4: bl              #0xb10454  ; [package:pdf/src/widgets/font.dart] Font::Font.helvetica
    // 0xb102f8: r1 = Null
    //     0xb102f8: mov             x1, NULL
    // 0xb102fc: stur            x0, [fp, #-8]
    // 0xb10300: r0 = Font.helveticaBold()
    //     0xb10300: bl              #0xb10430  ; [package:pdf/src/widgets/font.dart] Font::Font.helveticaBold
    // 0xb10304: r1 = Null
    //     0xb10304: mov             x1, NULL
    // 0xb10308: stur            x0, [fp, #-0x10]
    // 0xb1030c: r0 = Font.helveticaOblique()
    //     0xb1030c: bl              #0xb1040c  ; [package:pdf/src/widgets/font.dart] Font::Font.helveticaOblique
    // 0xb10310: r1 = Null
    //     0xb10310: mov             x1, NULL
    // 0xb10314: stur            x0, [fp, #-0x18]
    // 0xb10318: r0 = Font.helveticaBoldOblique()
    //     0xb10318: bl              #0xb103dc  ; [package:pdf/src/widgets/font.dart] Font::Font.helveticaBoldOblique
    // 0xb1031c: stur            x0, [fp, #-0x20]
    // 0xb10320: r0 = TextStyle()
    //     0xb10320: bl              #0xb121c8  ; AllocateTextStyleStub -> TextStyle (size=0x58)
    // 0xb10324: r1 = false
    //     0xb10324: add             x1, NULL, #0x30  ; false
    // 0xb10328: StoreField: r0->field_7 = r1
    //     0xb10328: stur            w1, [x0, #7]
    // 0xb1032c: r1 = Instance_PdfColor
    //     0xb1032c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e548] Obj!PdfColor@e0c9e1
    //     0xb10330: ldr             x1, [x1, #0x548]
    // 0xb10334: StoreField: r0->field_b = r1
    //     0xb10334: stur            w1, [x0, #0xb]
    // 0xb10338: r1 = const []
    //     0xb10338: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e128] List<Font>(0)
    //     0xb1033c: ldr             x1, [x1, #0x128]
    // 0xb10340: StoreField: r0->field_1f = r1
    //     0xb10340: stur            w1, [x0, #0x1f]
    // 0xb10344: r1 = 12.000000
    //     0xb10344: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xb10348: ldr             x1, [x1, #0xc60]
    // 0xb1034c: StoreField: r0->field_23 = r1
    //     0xb1034c: stur            w1, [x0, #0x23]
    // 0xb10350: r1 = Instance_FontWeight
    //     0xb10350: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e550] Obj!FontWeight@e2e521
    //     0xb10354: ldr             x1, [x1, #0x550]
    // 0xb10358: StoreField: r0->field_27 = r1
    //     0xb10358: stur            w1, [x0, #0x27]
    // 0xb1035c: r1 = Instance_FontStyle
    //     0xb1035c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e558] Obj!FontStyle@e2e501
    //     0xb10360: ldr             x1, [x1, #0x558]
    // 0xb10364: StoreField: r0->field_2b = r1
    //     0xb10364: stur            w1, [x0, #0x2b]
    // 0xb10368: r1 = 0.000000
    //     0xb10368: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb1036c: StoreField: r0->field_2f = r1
    //     0xb1036c: stur            w1, [x0, #0x2f]
    // 0xb10370: r2 = 1.000000
    //     0xb10370: ldr             x2, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xb10374: StoreField: r0->field_37 = r2
    //     0xb10374: stur            w2, [x0, #0x37]
    // 0xb10378: StoreField: r0->field_33 = r1
    //     0xb10378: stur            w1, [x0, #0x33]
    // 0xb1037c: StoreField: r0->field_3b = r2
    //     0xb1037c: stur            w2, [x0, #0x3b]
    // 0xb10380: r1 = Instance_TextDecoration
    //     0xb10380: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e560] Obj!TextDecoration@e0c411
    //     0xb10384: ldr             x1, [x1, #0x560]
    // 0xb10388: StoreField: r0->field_43 = r1
    //     0xb10388: stur            w1, [x0, #0x43]
    // 0xb1038c: r1 = Instance_TextDecorationStyle
    //     0xb1038c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e568] Obj!TextDecorationStyle@e2e4a1
    //     0xb10390: ldr             x1, [x1, #0x568]
    // 0xb10394: StoreField: r0->field_4b = r1
    //     0xb10394: stur            w1, [x0, #0x4b]
    // 0xb10398: StoreField: r0->field_4f = r2
    //     0xb10398: stur            w2, [x0, #0x4f]
    // 0xb1039c: r1 = Instance_PdfTextRenderingMode
    //     0xb1039c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e570] Obj!PdfTextRenderingMode@e2ed21
    //     0xb103a0: ldr             x1, [x1, #0x570]
    // 0xb103a4: StoreField: r0->field_53 = r1
    //     0xb103a4: stur            w1, [x0, #0x53]
    // 0xb103a8: ldur            x1, [fp, #-8]
    // 0xb103ac: StoreField: r0->field_f = r1
    //     0xb103ac: stur            w1, [x0, #0xf]
    // 0xb103b0: ldur            x1, [fp, #-0x10]
    // 0xb103b4: StoreField: r0->field_13 = r1
    //     0xb103b4: stur            w1, [x0, #0x13]
    // 0xb103b8: ldur            x1, [fp, #-0x18]
    // 0xb103bc: ArrayStore: r0[0] = r1  ; List_4
    //     0xb103bc: stur            w1, [x0, #0x17]
    // 0xb103c0: ldur            x1, [fp, #-0x20]
    // 0xb103c4: StoreField: r0->field_1b = r1
    //     0xb103c4: stur            w1, [x0, #0x1b]
    // 0xb103c8: LeaveFrame
    //     0xb103c8: mov             SP, fp
    //     0xb103cc: ldp             fp, lr, [SP], #0x10
    // 0xb103d0: ret
    //     0xb103d0: ret             
    // 0xb103d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb103d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb103d8: b               #0xb102f0
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xb107b0, size: 0xaa8
    // 0xb107b0: EnterFrame
    //     0xb107b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb107b4: mov             fp, SP
    // 0xb107b8: AllocStack(0xb8)
    //     0xb107b8: sub             SP, SP, #0xb8
    // 0xb107bc: SetupParameters(TextStyle this /* r1 => r0, fp-0x98 */, {dynamic color = Null /* r3 */, dynamic decoration = Null /* fp-0x8 */, dynamic decorationStyle = Null /* r6, fp-0x90 */, dynamic decorationThickness = Null /* r7, fp-0x88 */, dynamic font = Null /* r8 */, dynamic fontBold = Null /* r9, fp-0x80 */, dynamic fontBoldItalic = Null /* r10, fp-0x78 */, dynamic fontFallback = Null /* r11, fp-0x70 */, dynamic fontItalic = Null /* r12, fp-0x68 */, dynamic fontNormal = Null /* r13, fp-0x60 */, dynamic fontSize = Null /* r14, fp-0x58 */, dynamic fontStyle = Null /* r19, fp-0x50 */, dynamic fontWeight = Null /* r20, fp-0x48 */, dynamic height = Null /* fp-0x10 */, dynamic letterSpacing = Null /* fp-0x18 */, dynamic lineSpacing = Null /* fp-0x20 */, dynamic renderingMode = Null /* r5, fp-0x40 */, dynamic wordSpacing = Null /* r2, fp-0x38 */})
    //     0xb107bc: mov             x0, x1
    //     0xb107c0: stur            x1, [fp, #-0x98]
    //     0xb107c4: ldur            w1, [x4, #0x13]
    //     0xb107c8: ldur            w2, [x4, #0x1f]
    //     0xb107cc: add             x2, x2, HEAP, lsl #32
    //     0xb107d0: ldr             x16, [PP, #0x4720]  ; [pp+0x4720] "color"
    //     0xb107d4: cmp             w2, w16
    //     0xb107d8: b.ne            #0xb107fc
    //     0xb107dc: ldur            w2, [x4, #0x23]
    //     0xb107e0: add             x2, x2, HEAP, lsl #32
    //     0xb107e4: sub             w3, w1, w2
    //     0xb107e8: add             x2, fp, w3, sxtw #2
    //     0xb107ec: ldr             x2, [x2, #8]
    //     0xb107f0: mov             x3, x2
    //     0xb107f4: movz            x2, #0x1
    //     0xb107f8: b               #0xb10804
    //     0xb107fc: mov             x3, NULL
    //     0xb10800: movz            x2, #0
    //     0xb10804: lsl             x5, x2, #1
    //     0xb10808: lsl             w6, w5, #1
    //     0xb1080c: add             w7, w6, #8
    //     0xb10810: add             x16, x4, w7, sxtw #1
    //     0xb10814: ldur            w8, [x16, #0xf]
    //     0xb10818: add             x8, x8, HEAP, lsl #32
    //     0xb1081c: ldr             x16, [PP, #0x4728]  ; [pp+0x4728] "decoration"
    //     0xb10820: cmp             w8, w16
    //     0xb10824: b.ne            #0xb10858
    //     0xb10828: add             w2, w6, #0xa
    //     0xb1082c: add             x16, x4, w2, sxtw #1
    //     0xb10830: ldur            w6, [x16, #0xf]
    //     0xb10834: add             x6, x6, HEAP, lsl #32
    //     0xb10838: sub             w2, w1, w6
    //     0xb1083c: add             x6, fp, w2, sxtw #2
    //     0xb10840: ldr             x6, [x6, #8]
    //     0xb10844: add             w2, w5, #2
    //     0xb10848: sbfx            x5, x2, #1, #0x1f
    //     0xb1084c: mov             x2, x5
    //     0xb10850: mov             x5, x6
    //     0xb10854: b               #0xb1085c
    //     0xb10858: mov             x5, NULL
    //     0xb1085c: stur            x5, [fp, #-8]
    //     0xb10860: lsl             x6, x2, #1
    //     0xb10864: lsl             w7, w6, #1
    //     0xb10868: add             w8, w7, #8
    //     0xb1086c: add             x16, x4, w8, sxtw #1
    //     0xb10870: ldur            w9, [x16, #0xf]
    //     0xb10874: add             x9, x9, HEAP, lsl #32
    //     0xb10878: ldr             x16, [PP, #0x4738]  ; [pp+0x4738] "decorationStyle"
    //     0xb1087c: cmp             w9, w16
    //     0xb10880: b.ne            #0xb108b4
    //     0xb10884: add             w2, w7, #0xa
    //     0xb10888: add             x16, x4, w2, sxtw #1
    //     0xb1088c: ldur            w7, [x16, #0xf]
    //     0xb10890: add             x7, x7, HEAP, lsl #32
    //     0xb10894: sub             w2, w1, w7
    //     0xb10898: add             x7, fp, w2, sxtw #2
    //     0xb1089c: ldr             x7, [x7, #8]
    //     0xb108a0: add             w2, w6, #2
    //     0xb108a4: sbfx            x6, x2, #1, #0x1f
    //     0xb108a8: mov             x2, x6
    //     0xb108ac: mov             x6, x7
    //     0xb108b0: b               #0xb108b8
    //     0xb108b4: mov             x6, NULL
    //     0xb108b8: stur            x6, [fp, #-0x90]
    //     0xb108bc: lsl             x7, x2, #1
    //     0xb108c0: lsl             w8, w7, #1
    //     0xb108c4: add             w9, w8, #8
    //     0xb108c8: add             x16, x4, w9, sxtw #1
    //     0xb108cc: ldur            w10, [x16, #0xf]
    //     0xb108d0: add             x10, x10, HEAP, lsl #32
    //     0xb108d4: ldr             x16, [PP, #0x4740]  ; [pp+0x4740] "decorationThickness"
    //     0xb108d8: cmp             w10, w16
    //     0xb108dc: b.ne            #0xb10910
    //     0xb108e0: add             w2, w8, #0xa
    //     0xb108e4: add             x16, x4, w2, sxtw #1
    //     0xb108e8: ldur            w8, [x16, #0xf]
    //     0xb108ec: add             x8, x8, HEAP, lsl #32
    //     0xb108f0: sub             w2, w1, w8
    //     0xb108f4: add             x8, fp, w2, sxtw #2
    //     0xb108f8: ldr             x8, [x8, #8]
    //     0xb108fc: add             w2, w7, #2
    //     0xb10900: sbfx            x7, x2, #1, #0x1f
    //     0xb10904: mov             x2, x7
    //     0xb10908: mov             x7, x8
    //     0xb1090c: b               #0xb10914
    //     0xb10910: mov             x7, NULL
    //     0xb10914: stur            x7, [fp, #-0x88]
    //     0xb10918: lsl             x8, x2, #1
    //     0xb1091c: lsl             w9, w8, #1
    //     0xb10920: add             w10, w9, #8
    //     0xb10924: add             x16, x4, w10, sxtw #1
    //     0xb10928: ldur            w11, [x16, #0xf]
    //     0xb1092c: add             x11, x11, HEAP, lsl #32
    //     0xb10930: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e598] "font"
    //     0xb10934: ldr             x16, [x16, #0x598]
    //     0xb10938: cmp             w11, w16
    //     0xb1093c: b.ne            #0xb10970
    //     0xb10940: add             w2, w9, #0xa
    //     0xb10944: add             x16, x4, w2, sxtw #1
    //     0xb10948: ldur            w9, [x16, #0xf]
    //     0xb1094c: add             x9, x9, HEAP, lsl #32
    //     0xb10950: sub             w2, w1, w9
    //     0xb10954: add             x9, fp, w2, sxtw #2
    //     0xb10958: ldr             x9, [x9, #8]
    //     0xb1095c: add             w2, w8, #2
    //     0xb10960: sbfx            x8, x2, #1, #0x1f
    //     0xb10964: mov             x2, x8
    //     0xb10968: mov             x8, x9
    //     0xb1096c: b               #0xb10974
    //     0xb10970: mov             x8, NULL
    //     0xb10974: lsl             x9, x2, #1
    //     0xb10978: lsl             w10, w9, #1
    //     0xb1097c: add             w11, w10, #8
    //     0xb10980: add             x16, x4, w11, sxtw #1
    //     0xb10984: ldur            w12, [x16, #0xf]
    //     0xb10988: add             x12, x12, HEAP, lsl #32
    //     0xb1098c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e5a0] "fontBold"
    //     0xb10990: ldr             x16, [x16, #0x5a0]
    //     0xb10994: cmp             w12, w16
    //     0xb10998: b.ne            #0xb109cc
    //     0xb1099c: add             w2, w10, #0xa
    //     0xb109a0: add             x16, x4, w2, sxtw #1
    //     0xb109a4: ldur            w10, [x16, #0xf]
    //     0xb109a8: add             x10, x10, HEAP, lsl #32
    //     0xb109ac: sub             w2, w1, w10
    //     0xb109b0: add             x10, fp, w2, sxtw #2
    //     0xb109b4: ldr             x10, [x10, #8]
    //     0xb109b8: add             w2, w9, #2
    //     0xb109bc: sbfx            x9, x2, #1, #0x1f
    //     0xb109c0: mov             x2, x9
    //     0xb109c4: mov             x9, x10
    //     0xb109c8: b               #0xb109d0
    //     0xb109cc: mov             x9, NULL
    //     0xb109d0: stur            x9, [fp, #-0x80]
    //     0xb109d4: lsl             x10, x2, #1
    //     0xb109d8: lsl             w11, w10, #1
    //     0xb109dc: add             w12, w11, #8
    //     0xb109e0: add             x16, x4, w12, sxtw #1
    //     0xb109e4: ldur            w13, [x16, #0xf]
    //     0xb109e8: add             x13, x13, HEAP, lsl #32
    //     0xb109ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e5a8] "fontBoldItalic"
    //     0xb109f0: ldr             x16, [x16, #0x5a8]
    //     0xb109f4: cmp             w13, w16
    //     0xb109f8: b.ne            #0xb10a2c
    //     0xb109fc: add             w2, w11, #0xa
    //     0xb10a00: add             x16, x4, w2, sxtw #1
    //     0xb10a04: ldur            w11, [x16, #0xf]
    //     0xb10a08: add             x11, x11, HEAP, lsl #32
    //     0xb10a0c: sub             w2, w1, w11
    //     0xb10a10: add             x11, fp, w2, sxtw #2
    //     0xb10a14: ldr             x11, [x11, #8]
    //     0xb10a18: add             w2, w10, #2
    //     0xb10a1c: sbfx            x10, x2, #1, #0x1f
    //     0xb10a20: mov             x2, x10
    //     0xb10a24: mov             x10, x11
    //     0xb10a28: b               #0xb10a30
    //     0xb10a2c: mov             x10, NULL
    //     0xb10a30: stur            x10, [fp, #-0x78]
    //     0xb10a34: lsl             x11, x2, #1
    //     0xb10a38: lsl             w12, w11, #1
    //     0xb10a3c: add             w13, w12, #8
    //     0xb10a40: add             x16, x4, w13, sxtw #1
    //     0xb10a44: ldur            w14, [x16, #0xf]
    //     0xb10a48: add             x14, x14, HEAP, lsl #32
    //     0xb10a4c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e5b0] "fontFallback"
    //     0xb10a50: ldr             x16, [x16, #0x5b0]
    //     0xb10a54: cmp             w14, w16
    //     0xb10a58: b.ne            #0xb10a8c
    //     0xb10a5c: add             w2, w12, #0xa
    //     0xb10a60: add             x16, x4, w2, sxtw #1
    //     0xb10a64: ldur            w12, [x16, #0xf]
    //     0xb10a68: add             x12, x12, HEAP, lsl #32
    //     0xb10a6c: sub             w2, w1, w12
    //     0xb10a70: add             x12, fp, w2, sxtw #2
    //     0xb10a74: ldr             x12, [x12, #8]
    //     0xb10a78: add             w2, w11, #2
    //     0xb10a7c: sbfx            x11, x2, #1, #0x1f
    //     0xb10a80: mov             x2, x11
    //     0xb10a84: mov             x11, x12
    //     0xb10a88: b               #0xb10a90
    //     0xb10a8c: mov             x11, NULL
    //     0xb10a90: stur            x11, [fp, #-0x70]
    //     0xb10a94: lsl             x12, x2, #1
    //     0xb10a98: lsl             w13, w12, #1
    //     0xb10a9c: add             w14, w13, #8
    //     0xb10aa0: add             x16, x4, w14, sxtw #1
    //     0xb10aa4: ldur            w19, [x16, #0xf]
    //     0xb10aa8: add             x19, x19, HEAP, lsl #32
    //     0xb10aac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e5b8] "fontItalic"
    //     0xb10ab0: ldr             x16, [x16, #0x5b8]
    //     0xb10ab4: cmp             w19, w16
    //     0xb10ab8: b.ne            #0xb10aec
    //     0xb10abc: add             w2, w13, #0xa
    //     0xb10ac0: add             x16, x4, w2, sxtw #1
    //     0xb10ac4: ldur            w13, [x16, #0xf]
    //     0xb10ac8: add             x13, x13, HEAP, lsl #32
    //     0xb10acc: sub             w2, w1, w13
    //     0xb10ad0: add             x13, fp, w2, sxtw #2
    //     0xb10ad4: ldr             x13, [x13, #8]
    //     0xb10ad8: add             w2, w12, #2
    //     0xb10adc: sbfx            x12, x2, #1, #0x1f
    //     0xb10ae0: mov             x2, x12
    //     0xb10ae4: mov             x12, x13
    //     0xb10ae8: b               #0xb10af0
    //     0xb10aec: mov             x12, NULL
    //     0xb10af0: stur            x12, [fp, #-0x68]
    //     0xb10af4: lsl             x13, x2, #1
    //     0xb10af8: lsl             w14, w13, #1
    //     0xb10afc: add             w19, w14, #8
    //     0xb10b00: add             x16, x4, w19, sxtw #1
    //     0xb10b04: ldur            w20, [x16, #0xf]
    //     0xb10b08: add             x20, x20, HEAP, lsl #32
    //     0xb10b0c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e5c0] "fontNormal"
    //     0xb10b10: ldr             x16, [x16, #0x5c0]
    //     0xb10b14: cmp             w20, w16
    //     0xb10b18: b.ne            #0xb10b4c
    //     0xb10b1c: add             w2, w14, #0xa
    //     0xb10b20: add             x16, x4, w2, sxtw #1
    //     0xb10b24: ldur            w14, [x16, #0xf]
    //     0xb10b28: add             x14, x14, HEAP, lsl #32
    //     0xb10b2c: sub             w2, w1, w14
    //     0xb10b30: add             x14, fp, w2, sxtw #2
    //     0xb10b34: ldr             x14, [x14, #8]
    //     0xb10b38: add             w2, w13, #2
    //     0xb10b3c: sbfx            x13, x2, #1, #0x1f
    //     0xb10b40: mov             x2, x13
    //     0xb10b44: mov             x13, x14
    //     0xb10b48: b               #0xb10b50
    //     0xb10b4c: mov             x13, NULL
    //     0xb10b50: stur            x13, [fp, #-0x60]
    //     0xb10b54: lsl             x14, x2, #1
    //     0xb10b58: lsl             w19, w14, #1
    //     0xb10b5c: add             w20, w19, #8
    //     0xb10b60: add             x16, x4, w20, sxtw #1
    //     0xb10b64: ldur            w23, [x16, #0xf]
    //     0xb10b68: add             x23, x23, HEAP, lsl #32
    //     0xb10b6c: ldr             x16, [PP, #0x4808]  ; [pp+0x4808] "fontSize"
    //     0xb10b70: cmp             w23, w16
    //     0xb10b74: b.ne            #0xb10ba8
    //     0xb10b78: add             w2, w19, #0xa
    //     0xb10b7c: add             x16, x4, w2, sxtw #1
    //     0xb10b80: ldur            w19, [x16, #0xf]
    //     0xb10b84: add             x19, x19, HEAP, lsl #32
    //     0xb10b88: sub             w2, w1, w19
    //     0xb10b8c: add             x19, fp, w2, sxtw #2
    //     0xb10b90: ldr             x19, [x19, #8]
    //     0xb10b94: add             w2, w14, #2
    //     0xb10b98: sbfx            x14, x2, #1, #0x1f
    //     0xb10b9c: mov             x2, x14
    //     0xb10ba0: mov             x14, x19
    //     0xb10ba4: b               #0xb10bac
    //     0xb10ba8: mov             x14, NULL
    //     0xb10bac: stur            x14, [fp, #-0x58]
    //     0xb10bb0: lsl             x19, x2, #1
    //     0xb10bb4: lsl             w20, w19, #1
    //     0xb10bb8: add             w23, w20, #8
    //     0xb10bbc: add             x16, x4, w23, sxtw #1
    //     0xb10bc0: ldur            w24, [x16, #0xf]
    //     0xb10bc4: add             x24, x24, HEAP, lsl #32
    //     0xb10bc8: ldr             x16, [PP, #0x4758]  ; [pp+0x4758] "fontStyle"
    //     0xb10bcc: cmp             w24, w16
    //     0xb10bd0: b.ne            #0xb10c04
    //     0xb10bd4: add             w2, w20, #0xa
    //     0xb10bd8: add             x16, x4, w2, sxtw #1
    //     0xb10bdc: ldur            w20, [x16, #0xf]
    //     0xb10be0: add             x20, x20, HEAP, lsl #32
    //     0xb10be4: sub             w2, w1, w20
    //     0xb10be8: add             x20, fp, w2, sxtw #2
    //     0xb10bec: ldr             x20, [x20, #8]
    //     0xb10bf0: add             w2, w19, #2
    //     0xb10bf4: sbfx            x19, x2, #1, #0x1f
    //     0xb10bf8: mov             x2, x19
    //     0xb10bfc: mov             x19, x20
    //     0xb10c00: b               #0xb10c08
    //     0xb10c04: mov             x19, NULL
    //     0xb10c08: stur            x19, [fp, #-0x50]
    //     0xb10c0c: lsl             x20, x2, #1
    //     0xb10c10: lsl             w23, w20, #1
    //     0xb10c14: add             w24, w23, #8
    //     0xb10c18: add             x16, x4, w24, sxtw #1
    //     0xb10c1c: ldur            w25, [x16, #0xf]
    //     0xb10c20: add             x25, x25, HEAP, lsl #32
    //     0xb10c24: ldr             x16, [PP, #0x4768]  ; [pp+0x4768] "fontWeight"
    //     0xb10c28: cmp             w25, w16
    //     0xb10c2c: b.ne            #0xb10c60
    //     0xb10c30: add             w2, w23, #0xa
    //     0xb10c34: add             x16, x4, w2, sxtw #1
    //     0xb10c38: ldur            w23, [x16, #0xf]
    //     0xb10c3c: add             x23, x23, HEAP, lsl #32
    //     0xb10c40: sub             w2, w1, w23
    //     0xb10c44: add             x23, fp, w2, sxtw #2
    //     0xb10c48: ldr             x23, [x23, #8]
    //     0xb10c4c: add             w2, w20, #2
    //     0xb10c50: sbfx            x20, x2, #1, #0x1f
    //     0xb10c54: mov             x2, x20
    //     0xb10c58: mov             x20, x23
    //     0xb10c5c: b               #0xb10c64
    //     0xb10c60: mov             x20, NULL
    //     0xb10c64: stur            x20, [fp, #-0x48]
    //     0xb10c68: lsl             x23, x2, #1
    //     0xb10c6c: lsl             w24, w23, #1
    //     0xb10c70: add             w25, w24, #8
    //     0xb10c74: add             x16, x4, w25, sxtw #1
    //     0xb10c78: ldur            w5, [x16, #0xf]
    //     0xb10c7c: add             x5, x5, HEAP, lsl #32
    //     0xb10c80: ldr             x16, [PP, #0x4778]  ; [pp+0x4778] "height"
    //     0xb10c84: cmp             w5, w16
    //     0xb10c88: b.ne            #0xb10cb8
    //     0xb10c8c: add             w2, w24, #0xa
    //     0xb10c90: add             x16, x4, w2, sxtw #1
    //     0xb10c94: ldur            w5, [x16, #0xf]
    //     0xb10c98: add             x5, x5, HEAP, lsl #32
    //     0xb10c9c: sub             w2, w1, w5
    //     0xb10ca0: add             x5, fp, w2, sxtw #2
    //     0xb10ca4: ldr             x5, [x5, #8]
    //     0xb10ca8: add             w2, w23, #2
    //     0xb10cac: sbfx            x23, x2, #1, #0x1f
    //     0xb10cb0: mov             x2, x23
    //     0xb10cb4: b               #0xb10cbc
    //     0xb10cb8: mov             x5, NULL
    //     0xb10cbc: stur            x5, [fp, #-0x10]
    //     0xb10cc0: lsl             x23, x2, #1
    //     0xb10cc4: lsl             w24, w23, #1
    //     0xb10cc8: add             w25, w24, #8
    //     0xb10ccc: add             x16, x4, w25, sxtw #1
    //     0xb10cd0: ldur            w5, [x16, #0xf]
    //     0xb10cd4: add             x5, x5, HEAP, lsl #32
    //     0xb10cd8: ldr             x16, [PP, #0x4788]  ; [pp+0x4788] "letterSpacing"
    //     0xb10cdc: cmp             w5, w16
    //     0xb10ce0: b.ne            #0xb10d10
    //     0xb10ce4: add             w2, w24, #0xa
    //     0xb10ce8: add             x16, x4, w2, sxtw #1
    //     0xb10cec: ldur            w5, [x16, #0xf]
    //     0xb10cf0: add             x5, x5, HEAP, lsl #32
    //     0xb10cf4: sub             w2, w1, w5
    //     0xb10cf8: add             x5, fp, w2, sxtw #2
    //     0xb10cfc: ldr             x5, [x5, #8]
    //     0xb10d00: add             w2, w23, #2
    //     0xb10d04: sbfx            x23, x2, #1, #0x1f
    //     0xb10d08: mov             x2, x23
    //     0xb10d0c: b               #0xb10d14
    //     0xb10d10: mov             x5, NULL
    //     0xb10d14: stur            x5, [fp, #-0x18]
    //     0xb10d18: lsl             x23, x2, #1
    //     0xb10d1c: lsl             w24, w23, #1
    //     0xb10d20: add             w25, w24, #8
    //     0xb10d24: add             x16, x4, w25, sxtw #1
    //     0xb10d28: ldur            w5, [x16, #0xf]
    //     0xb10d2c: add             x5, x5, HEAP, lsl #32
    //     0xb10d30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e5c8] "lineSpacing"
    //     0xb10d34: ldr             x16, [x16, #0x5c8]
    //     0xb10d38: cmp             w5, w16
    //     0xb10d3c: b.ne            #0xb10d6c
    //     0xb10d40: add             w2, w24, #0xa
    //     0xb10d44: add             x16, x4, w2, sxtw #1
    //     0xb10d48: ldur            w5, [x16, #0xf]
    //     0xb10d4c: add             x5, x5, HEAP, lsl #32
    //     0xb10d50: sub             w2, w1, w5
    //     0xb10d54: add             x5, fp, w2, sxtw #2
    //     0xb10d58: ldr             x5, [x5, #8]
    //     0xb10d5c: add             w2, w23, #2
    //     0xb10d60: sbfx            x23, x2, #1, #0x1f
    //     0xb10d64: mov             x2, x23
    //     0xb10d68: b               #0xb10d70
    //     0xb10d6c: mov             x5, NULL
    //     0xb10d70: stur            x5, [fp, #-0x20]
    //     0xb10d74: lsl             x23, x2, #1
    //     0xb10d78: lsl             w24, w23, #1
    //     0xb10d7c: add             w25, w24, #8
    //     0xb10d80: add             x16, x4, w25, sxtw #1
    //     0xb10d84: ldur            w5, [x16, #0xf]
    //     0xb10d88: add             x5, x5, HEAP, lsl #32
    //     0xb10d8c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e5d0] "renderingMode"
    //     0xb10d90: ldr             x16, [x16, #0x5d0]
    //     0xb10d94: cmp             w5, w16
    //     0xb10d98: b.ne            #0xb10dc8
    //     0xb10d9c: add             w2, w24, #0xa
    //     0xb10da0: add             x16, x4, w2, sxtw #1
    //     0xb10da4: ldur            w5, [x16, #0xf]
    //     0xb10da8: add             x5, x5, HEAP, lsl #32
    //     0xb10dac: sub             w2, w1, w5
    //     0xb10db0: add             x5, fp, w2, sxtw #2
    //     0xb10db4: ldr             x5, [x5, #8]
    //     0xb10db8: add             w2, w23, #2
    //     0xb10dbc: sbfx            x23, x2, #1, #0x1f
    //     0xb10dc0: mov             x2, x23
    //     0xb10dc4: b               #0xb10dcc
    //     0xb10dc8: mov             x5, NULL
    //     0xb10dcc: stur            x5, [fp, #-0x40]
    //     0xb10dd0: lsl             x23, x2, #1
    //     0xb10dd4: lsl             w2, w23, #1
    //     0xb10dd8: add             w23, w2, #8
    //     0xb10ddc: add             x16, x4, w23, sxtw #1
    //     0xb10de0: ldur            w24, [x16, #0xf]
    //     0xb10de4: add             x24, x24, HEAP, lsl #32
    //     0xb10de8: ldr             x16, [PP, #0x47a0]  ; [pp+0x47a0] "wordSpacing"
    //     0xb10dec: cmp             w24, w16
    //     0xb10df0: b.ne            #0xb10e18
    //     0xb10df4: add             w23, w2, #0xa
    //     0xb10df8: add             x16, x4, w23, sxtw #1
    //     0xb10dfc: ldur            w2, [x16, #0xf]
    //     0xb10e00: add             x2, x2, HEAP, lsl #32
    //     0xb10e04: sub             w4, w1, w2
    //     0xb10e08: add             x1, fp, w4, sxtw #2
    //     0xb10e0c: ldr             x1, [x1, #8]
    //     0xb10e10: mov             x2, x1
    //     0xb10e14: b               #0xb10e1c
    //     0xb10e18: mov             x2, NULL
    //     0xb10e1c: stur            x2, [fp, #-0x38]
    // 0xb10e20: CheckStackOverflow
    //     0xb10e20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb10e24: cmp             SP, x16
    //     0xb10e28: b.ls            #0xb11250
    // 0xb10e2c: LoadField: r4 = r0->field_7
    //     0xb10e2c: ldur            w4, [x0, #7]
    // 0xb10e30: DecompressPointer r4
    //     0xb10e30: add             x4, x4, HEAP, lsl #32
    // 0xb10e34: stur            x4, [fp, #-0x30]
    // 0xb10e38: cmp             w3, NULL
    // 0xb10e3c: b.ne            #0xb10e4c
    // 0xb10e40: LoadField: r1 = r0->field_b
    //     0xb10e40: ldur            w1, [x0, #0xb]
    // 0xb10e44: DecompressPointer r1
    //     0xb10e44: add             x1, x1, HEAP, lsl #32
    // 0xb10e48: mov             x3, x1
    // 0xb10e4c: stur            x3, [fp, #-0x28]
    // 0xb10e50: cmp             w8, NULL
    // 0xb10e54: b.ne            #0xb10e68
    // 0xb10e58: mov             x1, x0
    // 0xb10e5c: r0 = font()
    //     0xb10e5c: bl              #0xb0fcfc  ; [package:pdf/src/widgets/text_style.dart] TextStyle::font
    // 0xb10e60: mov             x1, x0
    // 0xb10e64: b               #0xb10e6c
    // 0xb10e68: mov             x1, x8
    // 0xb10e6c: ldur            x0, [fp, #-0x60]
    // 0xb10e70: stur            x1, [fp, #-0xb8]
    // 0xb10e74: cmp             w0, NULL
    // 0xb10e78: b.ne            #0xb10e90
    // 0xb10e7c: ldur            x2, [fp, #-0x98]
    // 0xb10e80: LoadField: r0 = r2->field_f
    //     0xb10e80: ldur            w0, [x2, #0xf]
    // 0xb10e84: DecompressPointer r0
    //     0xb10e84: add             x0, x0, HEAP, lsl #32
    // 0xb10e88: mov             x3, x0
    // 0xb10e8c: b               #0xb10e98
    // 0xb10e90: ldur            x2, [fp, #-0x98]
    // 0xb10e94: mov             x3, x0
    // 0xb10e98: ldur            x0, [fp, #-0x80]
    // 0xb10e9c: stur            x3, [fp, #-0xb0]
    // 0xb10ea0: cmp             w0, NULL
    // 0xb10ea4: b.ne            #0xb10eb8
    // 0xb10ea8: LoadField: r0 = r2->field_13
    //     0xb10ea8: ldur            w0, [x2, #0x13]
    // 0xb10eac: DecompressPointer r0
    //     0xb10eac: add             x0, x0, HEAP, lsl #32
    // 0xb10eb0: mov             x4, x0
    // 0xb10eb4: b               #0xb10ebc
    // 0xb10eb8: mov             x4, x0
    // 0xb10ebc: ldur            x0, [fp, #-0x68]
    // 0xb10ec0: stur            x4, [fp, #-0xa8]
    // 0xb10ec4: cmp             w0, NULL
    // 0xb10ec8: b.ne            #0xb10edc
    // 0xb10ecc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb10ecc: ldur            w0, [x2, #0x17]
    // 0xb10ed0: DecompressPointer r0
    //     0xb10ed0: add             x0, x0, HEAP, lsl #32
    // 0xb10ed4: mov             x5, x0
    // 0xb10ed8: b               #0xb10ee0
    // 0xb10edc: mov             x5, x0
    // 0xb10ee0: ldur            x0, [fp, #-0x78]
    // 0xb10ee4: stur            x5, [fp, #-0xa0]
    // 0xb10ee8: cmp             w0, NULL
    // 0xb10eec: b.ne            #0xb10f00
    // 0xb10ef0: LoadField: r0 = r2->field_1b
    //     0xb10ef0: ldur            w0, [x2, #0x1b]
    // 0xb10ef4: DecompressPointer r0
    //     0xb10ef4: add             x0, x0, HEAP, lsl #32
    // 0xb10ef8: mov             x6, x0
    // 0xb10efc: b               #0xb10f04
    // 0xb10f00: mov             x6, x0
    // 0xb10f04: ldur            x0, [fp, #-0x70]
    // 0xb10f08: stur            x6, [fp, #-0x80]
    // 0xb10f0c: cmp             w0, NULL
    // 0xb10f10: b.ne            #0xb10f24
    // 0xb10f14: LoadField: r0 = r2->field_1f
    //     0xb10f14: ldur            w0, [x2, #0x1f]
    // 0xb10f18: DecompressPointer r0
    //     0xb10f18: add             x0, x0, HEAP, lsl #32
    // 0xb10f1c: mov             x7, x0
    // 0xb10f20: b               #0xb10f28
    // 0xb10f24: mov             x7, x0
    // 0xb10f28: ldur            x0, [fp, #-0x58]
    // 0xb10f2c: stur            x7, [fp, #-0x78]
    // 0xb10f30: cmp             w0, NULL
    // 0xb10f34: b.ne            #0xb10f48
    // 0xb10f38: LoadField: r0 = r2->field_23
    //     0xb10f38: ldur            w0, [x2, #0x23]
    // 0xb10f3c: DecompressPointer r0
    //     0xb10f3c: add             x0, x0, HEAP, lsl #32
    // 0xb10f40: mov             x8, x0
    // 0xb10f44: b               #0xb10f4c
    // 0xb10f48: mov             x8, x0
    // 0xb10f4c: ldur            x0, [fp, #-0x48]
    // 0xb10f50: stur            x8, [fp, #-0x70]
    // 0xb10f54: cmp             w0, NULL
    // 0xb10f58: b.ne            #0xb10f6c
    // 0xb10f5c: LoadField: r0 = r2->field_27
    //     0xb10f5c: ldur            w0, [x2, #0x27]
    // 0xb10f60: DecompressPointer r0
    //     0xb10f60: add             x0, x0, HEAP, lsl #32
    // 0xb10f64: mov             x9, x0
    // 0xb10f68: b               #0xb10f70
    // 0xb10f6c: mov             x9, x0
    // 0xb10f70: ldur            x0, [fp, #-0x50]
    // 0xb10f74: stur            x9, [fp, #-0x68]
    // 0xb10f78: cmp             w0, NULL
    // 0xb10f7c: b.ne            #0xb10f90
    // 0xb10f80: LoadField: r0 = r2->field_2b
    //     0xb10f80: ldur            w0, [x2, #0x2b]
    // 0xb10f84: DecompressPointer r0
    //     0xb10f84: add             x0, x0, HEAP, lsl #32
    // 0xb10f88: mov             x10, x0
    // 0xb10f8c: b               #0xb10f94
    // 0xb10f90: mov             x10, x0
    // 0xb10f94: ldur            x0, [fp, #-0x18]
    // 0xb10f98: stur            x10, [fp, #-0x60]
    // 0xb10f9c: cmp             w0, NULL
    // 0xb10fa0: b.ne            #0xb10fb4
    // 0xb10fa4: LoadField: r0 = r2->field_2f
    //     0xb10fa4: ldur            w0, [x2, #0x2f]
    // 0xb10fa8: DecompressPointer r0
    //     0xb10fa8: add             x0, x0, HEAP, lsl #32
    // 0xb10fac: mov             x11, x0
    // 0xb10fb0: b               #0xb10fb8
    // 0xb10fb4: mov             x11, x0
    // 0xb10fb8: ldur            x0, [fp, #-0x38]
    // 0xb10fbc: stur            x11, [fp, #-0x58]
    // 0xb10fc0: cmp             w0, NULL
    // 0xb10fc4: b.ne            #0xb10fd8
    // 0xb10fc8: LoadField: r0 = r2->field_37
    //     0xb10fc8: ldur            w0, [x2, #0x37]
    // 0xb10fcc: DecompressPointer r0
    //     0xb10fcc: add             x0, x0, HEAP, lsl #32
    // 0xb10fd0: mov             x12, x0
    // 0xb10fd4: b               #0xb10fdc
    // 0xb10fd8: mov             x12, x0
    // 0xb10fdc: ldur            x0, [fp, #-0x20]
    // 0xb10fe0: stur            x12, [fp, #-0x50]
    // 0xb10fe4: cmp             w0, NULL
    // 0xb10fe8: b.ne            #0xb10ffc
    // 0xb10fec: LoadField: r0 = r2->field_33
    //     0xb10fec: ldur            w0, [x2, #0x33]
    // 0xb10ff0: DecompressPointer r0
    //     0xb10ff0: add             x0, x0, HEAP, lsl #32
    // 0xb10ff4: mov             x13, x0
    // 0xb10ff8: b               #0xb11000
    // 0xb10ffc: mov             x13, x0
    // 0xb11000: ldur            x0, [fp, #-0x10]
    // 0xb11004: stur            x13, [fp, #-0x48]
    // 0xb11008: cmp             w0, NULL
    // 0xb1100c: b.ne            #0xb11020
    // 0xb11010: LoadField: r0 = r2->field_3b
    //     0xb11010: ldur            w0, [x2, #0x3b]
    // 0xb11014: DecompressPointer r0
    //     0xb11014: add             x0, x0, HEAP, lsl #32
    // 0xb11018: mov             x14, x0
    // 0xb1101c: b               #0xb11024
    // 0xb11020: mov             x14, x0
    // 0xb11024: ldur            x0, [fp, #-8]
    // 0xb11028: stur            x14, [fp, #-0x38]
    // 0xb1102c: cmp             w0, NULL
    // 0xb11030: b.ne            #0xb11044
    // 0xb11034: LoadField: r0 = r2->field_43
    //     0xb11034: ldur            w0, [x2, #0x43]
    // 0xb11038: DecompressPointer r0
    //     0xb11038: add             x0, x0, HEAP, lsl #32
    // 0xb1103c: mov             x19, x0
    // 0xb11040: b               #0xb11048
    // 0xb11044: mov             x19, x0
    // 0xb11048: ldur            x0, [fp, #-0x90]
    // 0xb1104c: stur            x19, [fp, #-0x20]
    // 0xb11050: cmp             w0, NULL
    // 0xb11054: b.ne            #0xb11068
    // 0xb11058: LoadField: r0 = r2->field_4b
    //     0xb11058: ldur            w0, [x2, #0x4b]
    // 0xb1105c: DecompressPointer r0
    //     0xb1105c: add             x0, x0, HEAP, lsl #32
    // 0xb11060: mov             x20, x0
    // 0xb11064: b               #0xb1106c
    // 0xb11068: mov             x20, x0
    // 0xb1106c: ldur            x0, [fp, #-0x88]
    // 0xb11070: stur            x20, [fp, #-0x18]
    // 0xb11074: cmp             w0, NULL
    // 0xb11078: b.ne            #0xb1108c
    // 0xb1107c: LoadField: r0 = r2->field_4f
    //     0xb1107c: ldur            w0, [x2, #0x4f]
    // 0xb11080: DecompressPointer r0
    //     0xb11080: add             x0, x0, HEAP, lsl #32
    // 0xb11084: mov             x23, x0
    // 0xb11088: b               #0xb11090
    // 0xb1108c: mov             x23, x0
    // 0xb11090: ldur            x0, [fp, #-0x40]
    // 0xb11094: stur            x23, [fp, #-0x10]
    // 0xb11098: cmp             w0, NULL
    // 0xb1109c: b.ne            #0xb110b0
    // 0xb110a0: LoadField: r0 = r2->field_53
    //     0xb110a0: ldur            w0, [x2, #0x53]
    // 0xb110a4: DecompressPointer r0
    //     0xb110a4: add             x0, x0, HEAP, lsl #32
    // 0xb110a8: mov             x24, x0
    // 0xb110ac: b               #0xb110b4
    // 0xb110b0: mov             x24, x0
    // 0xb110b4: ldur            x0, [fp, #-0x30]
    // 0xb110b8: ldur            x2, [fp, #-0x28]
    // 0xb110bc: stur            x24, [fp, #-8]
    // 0xb110c0: r0 = TextStyle()
    //     0xb110c0: bl              #0xb121c8  ; AllocateTextStyleStub -> TextStyle (size=0x58)
    // 0xb110c4: ldur            x1, [fp, #-0x30]
    // 0xb110c8: StoreField: r0->field_7 = r1
    //     0xb110c8: stur            w1, [x0, #7]
    // 0xb110cc: ldur            x1, [fp, #-0x28]
    // 0xb110d0: StoreField: r0->field_b = r1
    //     0xb110d0: stur            w1, [x0, #0xb]
    // 0xb110d4: ldur            x1, [fp, #-0x78]
    // 0xb110d8: StoreField: r0->field_1f = r1
    //     0xb110d8: stur            w1, [x0, #0x1f]
    // 0xb110dc: ldur            x1, [fp, #-0x70]
    // 0xb110e0: StoreField: r0->field_23 = r1
    //     0xb110e0: stur            w1, [x0, #0x23]
    // 0xb110e4: ldur            x1, [fp, #-0x68]
    // 0xb110e8: StoreField: r0->field_27 = r1
    //     0xb110e8: stur            w1, [x0, #0x27]
    // 0xb110ec: ldur            x2, [fp, #-0x60]
    // 0xb110f0: StoreField: r0->field_2b = r2
    //     0xb110f0: stur            w2, [x0, #0x2b]
    // 0xb110f4: ldur            x3, [fp, #-0x58]
    // 0xb110f8: StoreField: r0->field_2f = r3
    //     0xb110f8: stur            w3, [x0, #0x2f]
    // 0xb110fc: ldur            x3, [fp, #-0x50]
    // 0xb11100: StoreField: r0->field_37 = r3
    //     0xb11100: stur            w3, [x0, #0x37]
    // 0xb11104: ldur            x3, [fp, #-0x48]
    // 0xb11108: StoreField: r0->field_33 = r3
    //     0xb11108: stur            w3, [x0, #0x33]
    // 0xb1110c: ldur            x3, [fp, #-0x38]
    // 0xb11110: StoreField: r0->field_3b = r3
    //     0xb11110: stur            w3, [x0, #0x3b]
    // 0xb11114: ldur            x3, [fp, #-0x20]
    // 0xb11118: StoreField: r0->field_43 = r3
    //     0xb11118: stur            w3, [x0, #0x43]
    // 0xb1111c: ldur            x3, [fp, #-0x18]
    // 0xb11120: StoreField: r0->field_4b = r3
    //     0xb11120: stur            w3, [x0, #0x4b]
    // 0xb11124: ldur            x3, [fp, #-0x10]
    // 0xb11128: StoreField: r0->field_4f = r3
    //     0xb11128: stur            w3, [x0, #0x4f]
    // 0xb1112c: ldur            x3, [fp, #-8]
    // 0xb11130: StoreField: r0->field_53 = r3
    //     0xb11130: stur            w3, [x0, #0x53]
    // 0xb11134: ldur            x3, [fp, #-0xb0]
    // 0xb11138: cmp             w3, NULL
    // 0xb1113c: b.ne            #0xb11170
    // 0xb11140: r16 = Instance_FontStyle
    //     0xb11140: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e510] Obj!FontStyle@e2e4e1
    //     0xb11144: ldr             x16, [x16, #0x510]
    // 0xb11148: cmp             w2, w16
    // 0xb1114c: b.eq            #0xb11168
    // 0xb11150: r16 = Instance_FontWeight
    //     0xb11150: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e198] Obj!FontWeight@e2e541
    //     0xb11154: ldr             x16, [x16, #0x198]
    // 0xb11158: cmp             w1, w16
    // 0xb1115c: b.eq            #0xb11168
    // 0xb11160: ldur            x4, [fp, #-0xb8]
    // 0xb11164: b               #0xb11174
    // 0xb11168: r4 = Null
    //     0xb11168: mov             x4, NULL
    // 0xb1116c: b               #0xb11174
    // 0xb11170: mov             x4, x3
    // 0xb11174: ldur            x3, [fp, #-0xa8]
    // 0xb11178: StoreField: r0->field_f = r4
    //     0xb11178: stur            w4, [x0, #0xf]
    // 0xb1117c: cmp             w3, NULL
    // 0xb11180: b.ne            #0xb111b4
    // 0xb11184: r16 = Instance_FontStyle
    //     0xb11184: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e510] Obj!FontStyle@e2e4e1
    //     0xb11188: ldr             x16, [x16, #0x510]
    // 0xb1118c: cmp             w2, w16
    // 0xb11190: b.eq            #0xb111ac
    // 0xb11194: r16 = Instance_FontWeight
    //     0xb11194: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e198] Obj!FontWeight@e2e541
    //     0xb11198: ldr             x16, [x16, #0x198]
    // 0xb1119c: cmp             w1, w16
    // 0xb111a0: b.ne            #0xb111ac
    // 0xb111a4: ldur            x4, [fp, #-0xb8]
    // 0xb111a8: b               #0xb111b8
    // 0xb111ac: r4 = Null
    //     0xb111ac: mov             x4, NULL
    // 0xb111b0: b               #0xb111b8
    // 0xb111b4: mov             x4, x3
    // 0xb111b8: ldur            x3, [fp, #-0xa0]
    // 0xb111bc: StoreField: r0->field_13 = r4
    //     0xb111bc: stur            w4, [x0, #0x13]
    // 0xb111c0: cmp             w3, NULL
    // 0xb111c4: b.ne            #0xb111f8
    // 0xb111c8: r16 = Instance_FontStyle
    //     0xb111c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e510] Obj!FontStyle@e2e4e1
    //     0xb111cc: ldr             x16, [x16, #0x510]
    // 0xb111d0: cmp             w2, w16
    // 0xb111d4: b.ne            #0xb111f0
    // 0xb111d8: r16 = Instance_FontWeight
    //     0xb111d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e198] Obj!FontWeight@e2e541
    //     0xb111dc: ldr             x16, [x16, #0x198]
    // 0xb111e0: cmp             w1, w16
    // 0xb111e4: b.eq            #0xb111f0
    // 0xb111e8: ldur            x4, [fp, #-0xb8]
    // 0xb111ec: b               #0xb111fc
    // 0xb111f0: r4 = Null
    //     0xb111f0: mov             x4, NULL
    // 0xb111f4: b               #0xb111fc
    // 0xb111f8: mov             x4, x3
    // 0xb111fc: ldur            x3, [fp, #-0x80]
    // 0xb11200: ArrayStore: r0[0] = r4  ; List_4
    //     0xb11200: stur            w4, [x0, #0x17]
    // 0xb11204: cmp             w3, NULL
    // 0xb11208: b.ne            #0xb1123c
    // 0xb1120c: r16 = Instance_FontStyle
    //     0xb1120c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e510] Obj!FontStyle@e2e4e1
    //     0xb11210: ldr             x16, [x16, #0x510]
    // 0xb11214: cmp             w2, w16
    // 0xb11218: b.ne            #0xb11234
    // 0xb1121c: r16 = Instance_FontWeight
    //     0xb1121c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e198] Obj!FontWeight@e2e541
    //     0xb11220: ldr             x16, [x16, #0x198]
    // 0xb11224: cmp             w1, w16
    // 0xb11228: b.ne            #0xb11234
    // 0xb1122c: ldur            x1, [fp, #-0xb8]
    // 0xb11230: b               #0xb11240
    // 0xb11234: r1 = Null
    //     0xb11234: mov             x1, NULL
    // 0xb11238: b               #0xb11240
    // 0xb1123c: mov             x1, x3
    // 0xb11240: StoreField: r0->field_1b = r1
    //     0xb11240: stur            w1, [x0, #0x1b]
    // 0xb11244: LeaveFrame
    //     0xb11244: mov             SP, fp
    //     0xb11248: ldp             fp, lr, [SP], #0x10
    // 0xb1124c: ret
    //     0xb1124c: ret             
    // 0xb11250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb11250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb11254: b               #0xb10e2c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3b25c, size: 0x370
    // 0xc3b25c: EnterFrame
    //     0xc3b25c: stp             fp, lr, [SP, #-0x10]!
    //     0xc3b260: mov             fp, SP
    // 0xc3b264: AllocStack(0x10)
    //     0xc3b264: sub             SP, SP, #0x10
    // 0xc3b268: CheckStackOverflow
    //     0xc3b268: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3b26c: cmp             SP, x16
    //     0xc3b270: b.ls            #0xc3b5c4
    // 0xc3b274: r1 = Null
    //     0xc3b274: mov             x1, NULL
    // 0xc3b278: r2 = 62
    //     0xc3b278: movz            x2, #0x3e
    // 0xc3b27c: r0 = AllocateArray()
    //     0xc3b27c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3b280: stur            x0, [fp, #-8]
    // 0xc3b284: r16 = "TextStyle(color:"
    //     0xc3b284: add             x16, PP, #0x33, lsl #12  ; [pp+0x33680] "TextStyle(color:"
    //     0xc3b288: ldr             x16, [x16, #0x680]
    // 0xc3b28c: StoreField: r0->field_f = r16
    //     0xc3b28c: stur            w16, [x0, #0xf]
    // 0xc3b290: ldr             x2, [fp, #0x10]
    // 0xc3b294: LoadField: r1 = r2->field_b
    //     0xc3b294: ldur            w1, [x2, #0xb]
    // 0xc3b298: DecompressPointer r1
    //     0xc3b298: add             x1, x1, HEAP, lsl #32
    // 0xc3b29c: StoreField: r0->field_13 = r1
    //     0xc3b29c: stur            w1, [x0, #0x13]
    // 0xc3b2a0: r16 = " font:"
    //     0xc3b2a0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33688] " font:"
    //     0xc3b2a4: ldr             x16, [x16, #0x688]
    // 0xc3b2a8: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3b2a8: stur            w16, [x0, #0x17]
    // 0xc3b2ac: mov             x1, x2
    // 0xc3b2b0: r0 = font()
    //     0xc3b2b0: bl              #0xb0fcfc  ; [package:pdf/src/widgets/text_style.dart] TextStyle::font
    // 0xc3b2b4: ldur            x1, [fp, #-8]
    // 0xc3b2b8: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3b2b8: add             x25, x1, #0x1b
    //     0xc3b2bc: str             w0, [x25]
    //     0xc3b2c0: tbz             w0, #0, #0xc3b2dc
    //     0xc3b2c4: ldurb           w16, [x1, #-1]
    //     0xc3b2c8: ldurb           w17, [x0, #-1]
    //     0xc3b2cc: and             x16, x17, x16, lsr #2
    //     0xc3b2d0: tst             x16, HEAP, lsr #32
    //     0xc3b2d4: b.eq            #0xc3b2dc
    //     0xc3b2d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b2dc: ldur            x2, [fp, #-8]
    // 0xc3b2e0: r16 = " size:"
    //     0xc3b2e0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33690] " size:"
    //     0xc3b2e4: ldr             x16, [x16, #0x690]
    // 0xc3b2e8: StoreField: r2->field_1f = r16
    //     0xc3b2e8: stur            w16, [x2, #0x1f]
    // 0xc3b2ec: ldr             x3, [fp, #0x10]
    // 0xc3b2f0: LoadField: r0 = r3->field_23
    //     0xc3b2f0: ldur            w0, [x3, #0x23]
    // 0xc3b2f4: DecompressPointer r0
    //     0xc3b2f4: add             x0, x0, HEAP, lsl #32
    // 0xc3b2f8: mov             x1, x2
    // 0xc3b2fc: ArrayStore: r1[5] = r0  ; List_4
    //     0xc3b2fc: add             x25, x1, #0x23
    //     0xc3b300: str             w0, [x25]
    //     0xc3b304: tbz             w0, #0, #0xc3b320
    //     0xc3b308: ldurb           w16, [x1, #-1]
    //     0xc3b30c: ldurb           w17, [x0, #-1]
    //     0xc3b310: and             x16, x17, x16, lsr #2
    //     0xc3b314: tst             x16, HEAP, lsr #32
    //     0xc3b318: b.eq            #0xc3b320
    //     0xc3b31c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b320: r16 = " weight:"
    //     0xc3b320: add             x16, PP, #0x33, lsl #12  ; [pp+0x33698] " weight:"
    //     0xc3b324: ldr             x16, [x16, #0x698]
    // 0xc3b328: StoreField: r2->field_27 = r16
    //     0xc3b328: stur            w16, [x2, #0x27]
    // 0xc3b32c: LoadField: r0 = r3->field_27
    //     0xc3b32c: ldur            w0, [x3, #0x27]
    // 0xc3b330: DecompressPointer r0
    //     0xc3b330: add             x0, x0, HEAP, lsl #32
    // 0xc3b334: mov             x1, x2
    // 0xc3b338: ArrayStore: r1[7] = r0  ; List_4
    //     0xc3b338: add             x25, x1, #0x2b
    //     0xc3b33c: str             w0, [x25]
    //     0xc3b340: tbz             w0, #0, #0xc3b35c
    //     0xc3b344: ldurb           w16, [x1, #-1]
    //     0xc3b348: ldurb           w17, [x0, #-1]
    //     0xc3b34c: and             x16, x17, x16, lsr #2
    //     0xc3b350: tst             x16, HEAP, lsr #32
    //     0xc3b354: b.eq            #0xc3b35c
    //     0xc3b358: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b35c: r16 = " style:"
    //     0xc3b35c: add             x16, PP, #0x33, lsl #12  ; [pp+0x336a0] " style:"
    //     0xc3b360: ldr             x16, [x16, #0x6a0]
    // 0xc3b364: StoreField: r2->field_2f = r16
    //     0xc3b364: stur            w16, [x2, #0x2f]
    // 0xc3b368: LoadField: r0 = r3->field_2b
    //     0xc3b368: ldur            w0, [x3, #0x2b]
    // 0xc3b36c: DecompressPointer r0
    //     0xc3b36c: add             x0, x0, HEAP, lsl #32
    // 0xc3b370: mov             x1, x2
    // 0xc3b374: ArrayStore: r1[9] = r0  ; List_4
    //     0xc3b374: add             x25, x1, #0x33
    //     0xc3b378: str             w0, [x25]
    //     0xc3b37c: tbz             w0, #0, #0xc3b398
    //     0xc3b380: ldurb           w16, [x1, #-1]
    //     0xc3b384: ldurb           w17, [x0, #-1]
    //     0xc3b388: and             x16, x17, x16, lsr #2
    //     0xc3b38c: tst             x16, HEAP, lsr #32
    //     0xc3b390: b.eq            #0xc3b398
    //     0xc3b394: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b398: r16 = " letterSpacing:"
    //     0xc3b398: add             x16, PP, #0x33, lsl #12  ; [pp+0x336a8] " letterSpacing:"
    //     0xc3b39c: ldr             x16, [x16, #0x6a8]
    // 0xc3b3a0: StoreField: r2->field_37 = r16
    //     0xc3b3a0: stur            w16, [x2, #0x37]
    // 0xc3b3a4: LoadField: r0 = r3->field_2f
    //     0xc3b3a4: ldur            w0, [x3, #0x2f]
    // 0xc3b3a8: DecompressPointer r0
    //     0xc3b3a8: add             x0, x0, HEAP, lsl #32
    // 0xc3b3ac: mov             x1, x2
    // 0xc3b3b0: ArrayStore: r1[11] = r0  ; List_4
    //     0xc3b3b0: add             x25, x1, #0x3b
    //     0xc3b3b4: str             w0, [x25]
    //     0xc3b3b8: tbz             w0, #0, #0xc3b3d4
    //     0xc3b3bc: ldurb           w16, [x1, #-1]
    //     0xc3b3c0: ldurb           w17, [x0, #-1]
    //     0xc3b3c4: and             x16, x17, x16, lsr #2
    //     0xc3b3c8: tst             x16, HEAP, lsr #32
    //     0xc3b3cc: b.eq            #0xc3b3d4
    //     0xc3b3d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b3d4: r16 = " wordSpacing:"
    //     0xc3b3d4: add             x16, PP, #0x33, lsl #12  ; [pp+0x336b0] " wordSpacing:"
    //     0xc3b3d8: ldr             x16, [x16, #0x6b0]
    // 0xc3b3dc: StoreField: r2->field_3f = r16
    //     0xc3b3dc: stur            w16, [x2, #0x3f]
    // 0xc3b3e0: LoadField: r0 = r3->field_37
    //     0xc3b3e0: ldur            w0, [x3, #0x37]
    // 0xc3b3e4: DecompressPointer r0
    //     0xc3b3e4: add             x0, x0, HEAP, lsl #32
    // 0xc3b3e8: mov             x1, x2
    // 0xc3b3ec: ArrayStore: r1[13] = r0  ; List_4
    //     0xc3b3ec: add             x25, x1, #0x43
    //     0xc3b3f0: str             w0, [x25]
    //     0xc3b3f4: tbz             w0, #0, #0xc3b410
    //     0xc3b3f8: ldurb           w16, [x1, #-1]
    //     0xc3b3fc: ldurb           w17, [x0, #-1]
    //     0xc3b400: and             x16, x17, x16, lsr #2
    //     0xc3b404: tst             x16, HEAP, lsr #32
    //     0xc3b408: b.eq            #0xc3b410
    //     0xc3b40c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b410: r16 = " lineSpacing:"
    //     0xc3b410: add             x16, PP, #0x33, lsl #12  ; [pp+0x336b8] " lineSpacing:"
    //     0xc3b414: ldr             x16, [x16, #0x6b8]
    // 0xc3b418: StoreField: r2->field_47 = r16
    //     0xc3b418: stur            w16, [x2, #0x47]
    // 0xc3b41c: LoadField: r0 = r3->field_33
    //     0xc3b41c: ldur            w0, [x3, #0x33]
    // 0xc3b420: DecompressPointer r0
    //     0xc3b420: add             x0, x0, HEAP, lsl #32
    // 0xc3b424: mov             x1, x2
    // 0xc3b428: ArrayStore: r1[15] = r0  ; List_4
    //     0xc3b428: add             x25, x1, #0x4b
    //     0xc3b42c: str             w0, [x25]
    //     0xc3b430: tbz             w0, #0, #0xc3b44c
    //     0xc3b434: ldurb           w16, [x1, #-1]
    //     0xc3b438: ldurb           w17, [x0, #-1]
    //     0xc3b43c: and             x16, x17, x16, lsr #2
    //     0xc3b440: tst             x16, HEAP, lsr #32
    //     0xc3b444: b.eq            #0xc3b44c
    //     0xc3b448: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b44c: r16 = " height:"
    //     0xc3b44c: add             x16, PP, #0x33, lsl #12  ; [pp+0x336c0] " height:"
    //     0xc3b450: ldr             x16, [x16, #0x6c0]
    // 0xc3b454: StoreField: r2->field_4f = r16
    //     0xc3b454: stur            w16, [x2, #0x4f]
    // 0xc3b458: LoadField: r0 = r3->field_3b
    //     0xc3b458: ldur            w0, [x3, #0x3b]
    // 0xc3b45c: DecompressPointer r0
    //     0xc3b45c: add             x0, x0, HEAP, lsl #32
    // 0xc3b460: mov             x1, x2
    // 0xc3b464: ArrayStore: r1[17] = r0  ; List_4
    //     0xc3b464: add             x25, x1, #0x53
    //     0xc3b468: str             w0, [x25]
    //     0xc3b46c: tbz             w0, #0, #0xc3b488
    //     0xc3b470: ldurb           w16, [x1, #-1]
    //     0xc3b474: ldurb           w17, [x0, #-1]
    //     0xc3b478: and             x16, x17, x16, lsr #2
    //     0xc3b47c: tst             x16, HEAP, lsr #32
    //     0xc3b480: b.eq            #0xc3b488
    //     0xc3b484: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b488: r16 = " background:"
    //     0xc3b488: add             x16, PP, #0x33, lsl #12  ; [pp+0x336c8] " background:"
    //     0xc3b48c: ldr             x16, [x16, #0x6c8]
    // 0xc3b490: StoreField: r2->field_57 = r16
    //     0xc3b490: stur            w16, [x2, #0x57]
    // 0xc3b494: LoadField: r0 = r3->field_3f
    //     0xc3b494: ldur            w0, [x3, #0x3f]
    // 0xc3b498: DecompressPointer r0
    //     0xc3b498: add             x0, x0, HEAP, lsl #32
    // 0xc3b49c: StoreField: r2->field_5b = r0
    //     0xc3b49c: stur            w0, [x2, #0x5b]
    // 0xc3b4a0: r16 = " decoration:"
    //     0xc3b4a0: add             x16, PP, #0x33, lsl #12  ; [pp+0x336d0] " decoration:"
    //     0xc3b4a4: ldr             x16, [x16, #0x6d0]
    // 0xc3b4a8: StoreField: r2->field_5f = r16
    //     0xc3b4a8: stur            w16, [x2, #0x5f]
    // 0xc3b4ac: LoadField: r0 = r3->field_43
    //     0xc3b4ac: ldur            w0, [x3, #0x43]
    // 0xc3b4b0: DecompressPointer r0
    //     0xc3b4b0: add             x0, x0, HEAP, lsl #32
    // 0xc3b4b4: mov             x1, x2
    // 0xc3b4b8: ArrayStore: r1[21] = r0  ; List_4
    //     0xc3b4b8: add             x25, x1, #0x63
    //     0xc3b4bc: str             w0, [x25]
    //     0xc3b4c0: tbz             w0, #0, #0xc3b4dc
    //     0xc3b4c4: ldurb           w16, [x1, #-1]
    //     0xc3b4c8: ldurb           w17, [x0, #-1]
    //     0xc3b4cc: and             x16, x17, x16, lsr #2
    //     0xc3b4d0: tst             x16, HEAP, lsr #32
    //     0xc3b4d4: b.eq            #0xc3b4dc
    //     0xc3b4d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b4dc: r16 = " decorationColor:"
    //     0xc3b4dc: add             x16, PP, #0x33, lsl #12  ; [pp+0x336d8] " decorationColor:"
    //     0xc3b4e0: ldr             x16, [x16, #0x6d8]
    // 0xc3b4e4: StoreField: r2->field_67 = r16
    //     0xc3b4e4: stur            w16, [x2, #0x67]
    // 0xc3b4e8: LoadField: r0 = r3->field_47
    //     0xc3b4e8: ldur            w0, [x3, #0x47]
    // 0xc3b4ec: DecompressPointer r0
    //     0xc3b4ec: add             x0, x0, HEAP, lsl #32
    // 0xc3b4f0: StoreField: r2->field_6b = r0
    //     0xc3b4f0: stur            w0, [x2, #0x6b]
    // 0xc3b4f4: r16 = " decorationStyle:"
    //     0xc3b4f4: add             x16, PP, #0x33, lsl #12  ; [pp+0x336e0] " decorationStyle:"
    //     0xc3b4f8: ldr             x16, [x16, #0x6e0]
    // 0xc3b4fc: StoreField: r2->field_6f = r16
    //     0xc3b4fc: stur            w16, [x2, #0x6f]
    // 0xc3b500: LoadField: r0 = r3->field_4b
    //     0xc3b500: ldur            w0, [x3, #0x4b]
    // 0xc3b504: DecompressPointer r0
    //     0xc3b504: add             x0, x0, HEAP, lsl #32
    // 0xc3b508: mov             x1, x2
    // 0xc3b50c: ArrayStore: r1[25] = r0  ; List_4
    //     0xc3b50c: add             x25, x1, #0x73
    //     0xc3b510: str             w0, [x25]
    //     0xc3b514: tbz             w0, #0, #0xc3b530
    //     0xc3b518: ldurb           w16, [x1, #-1]
    //     0xc3b51c: ldurb           w17, [x0, #-1]
    //     0xc3b520: and             x16, x17, x16, lsr #2
    //     0xc3b524: tst             x16, HEAP, lsr #32
    //     0xc3b528: b.eq            #0xc3b530
    //     0xc3b52c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b530: r16 = " decorationThickness:"
    //     0xc3b530: add             x16, PP, #0x33, lsl #12  ; [pp+0x336e8] " decorationThickness:"
    //     0xc3b534: ldr             x16, [x16, #0x6e8]
    // 0xc3b538: StoreField: r2->field_77 = r16
    //     0xc3b538: stur            w16, [x2, #0x77]
    // 0xc3b53c: LoadField: r0 = r3->field_4f
    //     0xc3b53c: ldur            w0, [x3, #0x4f]
    // 0xc3b540: DecompressPointer r0
    //     0xc3b540: add             x0, x0, HEAP, lsl #32
    // 0xc3b544: mov             x1, x2
    // 0xc3b548: ArrayStore: r1[27] = r0  ; List_4
    //     0xc3b548: add             x25, x1, #0x7b
    //     0xc3b54c: str             w0, [x25]
    //     0xc3b550: tbz             w0, #0, #0xc3b56c
    //     0xc3b554: ldurb           w16, [x1, #-1]
    //     0xc3b558: ldurb           w17, [x0, #-1]
    //     0xc3b55c: and             x16, x17, x16, lsr #2
    //     0xc3b560: tst             x16, HEAP, lsr #32
    //     0xc3b564: b.eq            #0xc3b56c
    //     0xc3b568: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b56c: r16 = ", renderingMode:"
    //     0xc3b56c: add             x16, PP, #0x33, lsl #12  ; [pp+0x336f0] ", renderingMode:"
    //     0xc3b570: ldr             x16, [x16, #0x6f0]
    // 0xc3b574: StoreField: r2->field_7f = r16
    //     0xc3b574: stur            w16, [x2, #0x7f]
    // 0xc3b578: LoadField: r0 = r3->field_53
    //     0xc3b578: ldur            w0, [x3, #0x53]
    // 0xc3b57c: DecompressPointer r0
    //     0xc3b57c: add             x0, x0, HEAP, lsl #32
    // 0xc3b580: mov             x1, x2
    // 0xc3b584: ArrayStore: r1[29] = r0  ; List_4
    //     0xc3b584: add             x25, x1, #0x83
    //     0xc3b588: str             w0, [x25]
    //     0xc3b58c: tbz             w0, #0, #0xc3b5a8
    //     0xc3b590: ldurb           w16, [x1, #-1]
    //     0xc3b594: ldurb           w17, [x0, #-1]
    //     0xc3b598: and             x16, x17, x16, lsr #2
    //     0xc3b59c: tst             x16, HEAP, lsr #32
    //     0xc3b5a0: b.eq            #0xc3b5a8
    //     0xc3b5a4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b5a8: r16 = ")"
    //     0xc3b5a8: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3b5ac: StoreField: r2->field_87 = r16
    //     0xc3b5ac: stur            w16, [x2, #0x87]
    // 0xc3b5b0: str             x2, [SP]
    // 0xc3b5b4: r0 = _interpolate()
    //     0xc3b5b4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3b5b8: LeaveFrame
    //     0xc3b5b8: mov             SP, fp
    //     0xc3b5bc: ldp             fp, lr, [SP], #0x10
    // 0xc3b5c0: ret
    //     0xc3b5c0: ret             
    // 0xc3b5c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3b5c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3b5c8: b               #0xc3b274
  }
}

// class id: 766, size: 0x10, field offset: 0x8
//   const constructor, 
class TextDecoration extends Object {

  _Mint field_8;

  _ merge(/* No info */) {
    // ** addr: 0xb0fca4, size: 0x4c
    // 0xb0fca4: EnterFrame
    //     0xb0fca4: stp             fp, lr, [SP, #-0x10]!
    //     0xb0fca8: mov             fp, SP
    // 0xb0fcac: AllocStack(0x8)
    //     0xb0fcac: sub             SP, SP, #8
    // 0xb0fcb0: SetupParameters(TextDecoration this /* r1 => r0 */)
    //     0xb0fcb0: mov             x0, x1
    // 0xb0fcb4: cmp             w2, NULL
    // 0xb0fcb8: b.ne            #0xb0fcc8
    // 0xb0fcbc: LeaveFrame
    //     0xb0fcbc: mov             SP, fp
    //     0xb0fcc0: ldp             fp, lr, [SP], #0x10
    // 0xb0fcc4: ret
    //     0xb0fcc4: ret             
    // 0xb0fcc8: LoadField: r1 = r0->field_7
    //     0xb0fcc8: ldur            x1, [x0, #7]
    // 0xb0fccc: LoadField: r0 = r2->field_7
    //     0xb0fccc: ldur            x0, [x2, #7]
    // 0xb0fcd0: orr             x2, x1, x0
    // 0xb0fcd4: stur            x2, [fp, #-8]
    // 0xb0fcd8: r0 = TextDecoration()
    //     0xb0fcd8: bl              #0xb0fcf0  ; AllocateTextDecorationStub -> TextDecoration (size=0x10)
    // 0xb0fcdc: ldur            x1, [fp, #-8]
    // 0xb0fce0: StoreField: r0->field_7 = r1
    //     0xb0fce0: stur            x1, [x0, #7]
    // 0xb0fce4: LeaveFrame
    //     0xb0fce4: mov             SP, fp
    //     0xb0fce8: ldp             fp, lr, [SP], #0x10
    // 0xb0fcec: ret
    //     0xb0fcec: ret             
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf1bcc, size: 0x70
    // 0xbf1bcc: EnterFrame
    //     0xbf1bcc: stp             fp, lr, [SP, #-0x10]!
    //     0xbf1bd0: mov             fp, SP
    // 0xbf1bd4: AllocStack(0x8)
    //     0xbf1bd4: sub             SP, SP, #8
    // 0xbf1bd8: CheckStackOverflow
    //     0xbf1bd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf1bdc: cmp             SP, x16
    //     0xbf1be0: b.ls            #0xbf1c34
    // 0xbf1be4: ldr             x0, [fp, #0x10]
    // 0xbf1be8: LoadField: r2 = r0->field_7
    //     0xbf1be8: ldur            x2, [x0, #7]
    // 0xbf1bec: r0 = BoxInt64Instr(r2)
    //     0xbf1bec: sbfiz           x0, x2, #1, #0x1f
    //     0xbf1bf0: cmp             x2, x0, asr #1
    //     0xbf1bf4: b.eq            #0xbf1c00
    //     0xbf1bf8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1bfc: stur            x2, [x0, #7]
    // 0xbf1c00: r1 = 60
    //     0xbf1c00: movz            x1, #0x3c
    // 0xbf1c04: branchIfSmi(r0, 0xbf1c10)
    //     0xbf1c04: tbz             w0, #0, #0xbf1c10
    // 0xbf1c08: r1 = LoadClassIdInstr(r0)
    //     0xbf1c08: ldur            x1, [x0, #-1]
    //     0xbf1c0c: ubfx            x1, x1, #0xc, #0x14
    // 0xbf1c10: str             x0, [SP]
    // 0xbf1c14: mov             x0, x1
    // 0xbf1c18: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1c18: movz            x17, #0x64af
    //     0xbf1c1c: add             lr, x0, x17
    //     0xbf1c20: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1c24: blr             lr
    // 0xbf1c28: LeaveFrame
    //     0xbf1c28: mov             SP, fp
    //     0xbf1c2c: ldp             fp, lr, [SP], #0x10
    // 0xbf1c30: ret
    //     0xbf1c30: ret             
    // 0xbf1c34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1c34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1c38: b               #0xbf1be4
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3aff0, size: 0x26c
    // 0xc3aff0: EnterFrame
    //     0xc3aff0: stp             fp, lr, [SP, #-0x10]!
    //     0xc3aff4: mov             fp, SP
    // 0xc3aff8: AllocStack(0x28)
    //     0xc3aff8: sub             SP, SP, #0x28
    // 0xc3affc: CheckStackOverflow
    //     0xc3affc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3b000: cmp             SP, x16
    //     0xc3b004: b.ls            #0xc3b250
    // 0xc3b008: ldr             x0, [fp, #0x10]
    // 0xc3b00c: LoadField: r3 = r0->field_7
    //     0xc3b00c: ldur            x3, [x0, #7]
    // 0xc3b010: stur            x3, [fp, #-8]
    // 0xc3b014: cbnz            x3, #0xc3b02c
    // 0xc3b018: r0 = "TextDecoration.none"
    //     0xc3b018: add             x0, PP, #0x33, lsl #12  ; [pp+0x33700] "TextDecoration.none"
    //     0xc3b01c: ldr             x0, [x0, #0x700]
    // 0xc3b020: LeaveFrame
    //     0xc3b020: mov             SP, fp
    //     0xc3b024: ldp             fp, lr, [SP], #0x10
    // 0xc3b028: ret
    //     0xc3b028: ret             
    // 0xc3b02c: r1 = <String>
    //     0xc3b02c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc3b030: r2 = 0
    //     0xc3b030: movz            x2, #0
    // 0xc3b034: r0 = _GrowableList()
    //     0xc3b034: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc3b038: mov             x2, x0
    // 0xc3b03c: ldur            x0, [fp, #-8]
    // 0xc3b040: stur            x2, [fp, #-0x18]
    // 0xc3b044: branchIfSmi(r0, 0xc3b0a4)
    //     0xc3b044: tbz             w0, #0, #0xc3b0a4
    // 0xc3b048: LoadField: r1 = r2->field_b
    //     0xc3b048: ldur            w1, [x2, #0xb]
    // 0xc3b04c: LoadField: r3 = r2->field_f
    //     0xc3b04c: ldur            w3, [x2, #0xf]
    // 0xc3b050: DecompressPointer r3
    //     0xc3b050: add             x3, x3, HEAP, lsl #32
    // 0xc3b054: LoadField: r4 = r3->field_b
    //     0xc3b054: ldur            w4, [x3, #0xb]
    // 0xc3b058: r3 = LoadInt32Instr(r1)
    //     0xc3b058: sbfx            x3, x1, #1, #0x1f
    // 0xc3b05c: stur            x3, [fp, #-0x10]
    // 0xc3b060: r1 = LoadInt32Instr(r4)
    //     0xc3b060: sbfx            x1, x4, #1, #0x1f
    // 0xc3b064: cmp             x3, x1
    // 0xc3b068: b.ne            #0xc3b074
    // 0xc3b06c: mov             x1, x2
    // 0xc3b070: r0 = _growToNextCapacity()
    //     0xc3b070: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc3b074: ldur            x0, [fp, #-0x18]
    // 0xc3b078: ldur            x1, [fp, #-0x10]
    // 0xc3b07c: add             x2, x1, #1
    // 0xc3b080: lsl             x3, x2, #1
    // 0xc3b084: StoreField: r0->field_b = r3
    //     0xc3b084: stur            w3, [x0, #0xb]
    // 0xc3b088: LoadField: r2 = r0->field_f
    //     0xc3b088: ldur            w2, [x0, #0xf]
    // 0xc3b08c: DecompressPointer r2
    //     0xc3b08c: add             x2, x2, HEAP, lsl #32
    // 0xc3b090: add             x3, x2, x1, lsl #2
    // 0xc3b094: r16 = "underline"
    //     0xc3b094: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c18] "underline"
    //     0xc3b098: ldr             x16, [x16, #0xc18]
    // 0xc3b09c: StoreField: r3->field_f = r16
    //     0xc3b09c: stur            w16, [x3, #0xf]
    // 0xc3b0a0: b               #0xc3b0a8
    // 0xc3b0a4: mov             x0, x2
    // 0xc3b0a8: ldur            x2, [fp, #-8]
    // 0xc3b0ac: tbz             w2, #1, #0xc3b108
    // 0xc3b0b0: LoadField: r1 = r0->field_b
    //     0xc3b0b0: ldur            w1, [x0, #0xb]
    // 0xc3b0b4: LoadField: r3 = r0->field_f
    //     0xc3b0b4: ldur            w3, [x0, #0xf]
    // 0xc3b0b8: DecompressPointer r3
    //     0xc3b0b8: add             x3, x3, HEAP, lsl #32
    // 0xc3b0bc: LoadField: r4 = r3->field_b
    //     0xc3b0bc: ldur            w4, [x3, #0xb]
    // 0xc3b0c0: r3 = LoadInt32Instr(r1)
    //     0xc3b0c0: sbfx            x3, x1, #1, #0x1f
    // 0xc3b0c4: stur            x3, [fp, #-0x10]
    // 0xc3b0c8: r1 = LoadInt32Instr(r4)
    //     0xc3b0c8: sbfx            x1, x4, #1, #0x1f
    // 0xc3b0cc: cmp             x3, x1
    // 0xc3b0d0: b.ne            #0xc3b0dc
    // 0xc3b0d4: mov             x1, x0
    // 0xc3b0d8: r0 = _growToNextCapacity()
    //     0xc3b0d8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc3b0dc: ldur            x0, [fp, #-0x18]
    // 0xc3b0e0: ldur            x1, [fp, #-0x10]
    // 0xc3b0e4: add             x2, x1, #1
    // 0xc3b0e8: lsl             x3, x2, #1
    // 0xc3b0ec: StoreField: r0->field_b = r3
    //     0xc3b0ec: stur            w3, [x0, #0xb]
    // 0xc3b0f0: LoadField: r2 = r0->field_f
    //     0xc3b0f0: ldur            w2, [x0, #0xf]
    // 0xc3b0f4: DecompressPointer r2
    //     0xc3b0f4: add             x2, x2, HEAP, lsl #32
    // 0xc3b0f8: add             x3, x2, x1, lsl #2
    // 0xc3b0fc: r16 = "overline"
    //     0xc3b0fc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25c28] "overline"
    //     0xc3b100: ldr             x16, [x16, #0xc28]
    // 0xc3b104: StoreField: r3->field_f = r16
    //     0xc3b104: stur            w16, [x3, #0xf]
    // 0xc3b108: ldur            x1, [fp, #-8]
    // 0xc3b10c: tbz             w1, #2, #0xc3b168
    // 0xc3b110: LoadField: r1 = r0->field_b
    //     0xc3b110: ldur            w1, [x0, #0xb]
    // 0xc3b114: LoadField: r2 = r0->field_f
    //     0xc3b114: ldur            w2, [x0, #0xf]
    // 0xc3b118: DecompressPointer r2
    //     0xc3b118: add             x2, x2, HEAP, lsl #32
    // 0xc3b11c: LoadField: r3 = r2->field_b
    //     0xc3b11c: ldur            w3, [x2, #0xb]
    // 0xc3b120: r2 = LoadInt32Instr(r1)
    //     0xc3b120: sbfx            x2, x1, #1, #0x1f
    // 0xc3b124: stur            x2, [fp, #-8]
    // 0xc3b128: r1 = LoadInt32Instr(r3)
    //     0xc3b128: sbfx            x1, x3, #1, #0x1f
    // 0xc3b12c: cmp             x2, x1
    // 0xc3b130: b.ne            #0xc3b13c
    // 0xc3b134: mov             x1, x0
    // 0xc3b138: r0 = _growToNextCapacity()
    //     0xc3b138: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc3b13c: ldur            x0, [fp, #-0x18]
    // 0xc3b140: ldur            x1, [fp, #-8]
    // 0xc3b144: add             x2, x1, #1
    // 0xc3b148: lsl             x3, x2, #1
    // 0xc3b14c: StoreField: r0->field_b = r3
    //     0xc3b14c: stur            w3, [x0, #0xb]
    // 0xc3b150: LoadField: r2 = r0->field_f
    //     0xc3b150: ldur            w2, [x0, #0xf]
    // 0xc3b154: DecompressPointer r2
    //     0xc3b154: add             x2, x2, HEAP, lsl #32
    // 0xc3b158: add             x3, x2, x1, lsl #2
    // 0xc3b15c: r16 = "lineThrough"
    //     0xc3b15c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33708] "lineThrough"
    //     0xc3b160: ldr             x16, [x16, #0x708]
    // 0xc3b164: StoreField: r3->field_f = r16
    //     0xc3b164: stur            w16, [x3, #0xf]
    // 0xc3b168: LoadField: r1 = r0->field_b
    //     0xc3b168: ldur            w1, [x0, #0xb]
    // 0xc3b16c: r3 = LoadInt32Instr(r1)
    //     0xc3b16c: sbfx            x3, x1, #1, #0x1f
    // 0xc3b170: stur            x3, [fp, #-8]
    // 0xc3b174: cmp             x3, #1
    // 0xc3b178: b.ne            #0xc3b1d4
    // 0xc3b17c: r1 = Null
    //     0xc3b17c: mov             x1, NULL
    // 0xc3b180: r2 = 4
    //     0xc3b180: movz            x2, #0x4
    // 0xc3b184: r0 = AllocateArray()
    //     0xc3b184: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3b188: mov             x2, x0
    // 0xc3b18c: r16 = "TextDecoration."
    //     0xc3b18c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33710] "TextDecoration."
    //     0xc3b190: ldr             x16, [x16, #0x710]
    // 0xc3b194: StoreField: r2->field_f = r16
    //     0xc3b194: stur            w16, [x2, #0xf]
    // 0xc3b198: ldur            x0, [fp, #-8]
    // 0xc3b19c: r1 = 0
    //     0xc3b19c: movz            x1, #0
    // 0xc3b1a0: cmp             x1, x0
    // 0xc3b1a4: b.hs            #0xc3b258
    // 0xc3b1a8: ldur            x0, [fp, #-0x18]
    // 0xc3b1ac: LoadField: r1 = r0->field_f
    //     0xc3b1ac: ldur            w1, [x0, #0xf]
    // 0xc3b1b0: DecompressPointer r1
    //     0xc3b1b0: add             x1, x1, HEAP, lsl #32
    // 0xc3b1b4: LoadField: r0 = r1->field_f
    //     0xc3b1b4: ldur            w0, [x1, #0xf]
    // 0xc3b1b8: DecompressPointer r0
    //     0xc3b1b8: add             x0, x0, HEAP, lsl #32
    // 0xc3b1bc: StoreField: r2->field_13 = r0
    //     0xc3b1bc: stur            w0, [x2, #0x13]
    // 0xc3b1c0: str             x2, [SP]
    // 0xc3b1c4: r0 = _interpolate()
    //     0xc3b1c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3b1c8: LeaveFrame
    //     0xc3b1c8: mov             SP, fp
    //     0xc3b1cc: ldp             fp, lr, [SP], #0x10
    // 0xc3b1d0: ret
    //     0xc3b1d0: ret             
    // 0xc3b1d4: r1 = Null
    //     0xc3b1d4: mov             x1, NULL
    // 0xc3b1d8: r2 = 6
    //     0xc3b1d8: movz            x2, #0x6
    // 0xc3b1dc: r0 = AllocateArray()
    //     0xc3b1dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3b1e0: stur            x0, [fp, #-0x20]
    // 0xc3b1e4: r16 = "TextDecoration.combine(["
    //     0xc3b1e4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33718] "TextDecoration.combine(["
    //     0xc3b1e8: ldr             x16, [x16, #0x718]
    // 0xc3b1ec: StoreField: r0->field_f = r16
    //     0xc3b1ec: stur            w16, [x0, #0xf]
    // 0xc3b1f0: r16 = ", "
    //     0xc3b1f0: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3b1f4: str             x16, [SP]
    // 0xc3b1f8: ldur            x1, [fp, #-0x18]
    // 0xc3b1fc: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xc3b1fc: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xc3b200: r0 = join()
    //     0xc3b200: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xc3b204: ldur            x1, [fp, #-0x20]
    // 0xc3b208: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3b208: add             x25, x1, #0x13
    //     0xc3b20c: str             w0, [x25]
    //     0xc3b210: tbz             w0, #0, #0xc3b22c
    //     0xc3b214: ldurb           w16, [x1, #-1]
    //     0xc3b218: ldurb           w17, [x0, #-1]
    //     0xc3b21c: and             x16, x17, x16, lsr #2
    //     0xc3b220: tst             x16, HEAP, lsr #32
    //     0xc3b224: b.eq            #0xc3b22c
    //     0xc3b228: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b22c: ldur            x0, [fp, #-0x20]
    // 0xc3b230: r16 = "])"
    //     0xc3b230: add             x16, PP, #0x33, lsl #12  ; [pp+0x33720] "])"
    //     0xc3b234: ldr             x16, [x16, #0x720]
    // 0xc3b238: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3b238: stur            w16, [x0, #0x17]
    // 0xc3b23c: str             x0, [SP]
    // 0xc3b240: r0 = _interpolate()
    //     0xc3b240: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3b244: LeaveFrame
    //     0xc3b244: mov             SP, fp
    //     0xc3b248: ldp             fp, lr, [SP], #0x10
    // 0xc3b24c: ret
    //     0xc3b24c: ret             
    // 0xc3b250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3b250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3b254: b               #0xc3b008
    // 0xc3b258: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3b258: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7ce78, size: 0x54
    // 0xd7ce78: ldr             x1, [SP]
    // 0xd7ce7c: cmp             w1, NULL
    // 0xd7ce80: b.ne            #0xd7ce8c
    // 0xd7ce84: r0 = false
    //     0xd7ce84: add             x0, NULL, #0x30  ; false
    // 0xd7ce88: ret
    //     0xd7ce88: ret             
    // 0xd7ce8c: r2 = 60
    //     0xd7ce8c: movz            x2, #0x3c
    // 0xd7ce90: branchIfSmi(r1, 0xd7ce9c)
    //     0xd7ce90: tbz             w1, #0, #0xd7ce9c
    // 0xd7ce94: r2 = LoadClassIdInstr(r1)
    //     0xd7ce94: ldur            x2, [x1, #-1]
    //     0xd7ce98: ubfx            x2, x2, #0xc, #0x14
    // 0xd7ce9c: cmp             x2, #0x2fe
    // 0xd7cea0: b.eq            #0xd7ceac
    // 0xd7cea4: r0 = false
    //     0xd7cea4: add             x0, NULL, #0x30  ; false
    // 0xd7cea8: ret
    //     0xd7cea8: ret             
    // 0xd7ceac: ldr             x2, [SP, #8]
    // 0xd7ceb0: LoadField: r3 = r2->field_7
    //     0xd7ceb0: ldur            x3, [x2, #7]
    // 0xd7ceb4: LoadField: r2 = r1->field_7
    //     0xd7ceb4: ldur            x2, [x1, #7]
    // 0xd7ceb8: cmp             x3, x2
    // 0xd7cebc: r16 = true
    //     0xd7cebc: add             x16, NULL, #0x20  ; true
    // 0xd7cec0: r17 = false
    //     0xd7cec0: add             x17, NULL, #0x30  ; false
    // 0xd7cec4: csel            x0, x16, x17, eq
    // 0xd7cec8: ret
    //     0xd7cec8: ret             
  }
}

// class id: 6786, size: 0x14, field offset: 0x14
enum TextDecorationStyle extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e43c, size: 0x64
    // 0xc4e43c: EnterFrame
    //     0xc4e43c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e440: mov             fp, SP
    // 0xc4e444: AllocStack(0x10)
    //     0xc4e444: sub             SP, SP, #0x10
    // 0xc4e448: SetupParameters(TextDecorationStyle this /* r1 => r0, fp-0x8 */)
    //     0xc4e448: mov             x0, x1
    //     0xc4e44c: stur            x1, [fp, #-8]
    // 0xc4e450: CheckStackOverflow
    //     0xc4e450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e454: cmp             SP, x16
    //     0xc4e458: b.ls            #0xc4e498
    // 0xc4e45c: r1 = Null
    //     0xc4e45c: mov             x1, NULL
    // 0xc4e460: r2 = 4
    //     0xc4e460: movz            x2, #0x4
    // 0xc4e464: r0 = AllocateArray()
    //     0xc4e464: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e468: r16 = "TextDecorationStyle."
    //     0xc4e468: add             x16, PP, #0x33, lsl #12  ; [pp+0x336f8] "TextDecorationStyle."
    //     0xc4e46c: ldr             x16, [x16, #0x6f8]
    // 0xc4e470: StoreField: r0->field_f = r16
    //     0xc4e470: stur            w16, [x0, #0xf]
    // 0xc4e474: ldur            x1, [fp, #-8]
    // 0xc4e478: LoadField: r2 = r1->field_f
    //     0xc4e478: ldur            w2, [x1, #0xf]
    // 0xc4e47c: DecompressPointer r2
    //     0xc4e47c: add             x2, x2, HEAP, lsl #32
    // 0xc4e480: StoreField: r0->field_13 = r2
    //     0xc4e480: stur            w2, [x0, #0x13]
    // 0xc4e484: str             x0, [SP]
    // 0xc4e488: r0 = _interpolate()
    //     0xc4e488: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e48c: LeaveFrame
    //     0xc4e48c: mov             SP, fp
    //     0xc4e490: ldp             fp, lr, [SP], #0x10
    // 0xc4e494: ret
    //     0xc4e494: ret             
    // 0xc4e498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e498: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e49c: b               #0xc4e45c
  }
}

// class id: 6787, size: 0x14, field offset: 0x14
enum FontStyle extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e3d8, size: 0x64
    // 0xc4e3d8: EnterFrame
    //     0xc4e3d8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e3dc: mov             fp, SP
    // 0xc4e3e0: AllocStack(0x10)
    //     0xc4e3e0: sub             SP, SP, #0x10
    // 0xc4e3e4: SetupParameters(FontStyle this /* r1 => r0, fp-0x8 */)
    //     0xc4e3e4: mov             x0, x1
    //     0xc4e3e8: stur            x1, [fp, #-8]
    // 0xc4e3ec: CheckStackOverflow
    //     0xc4e3ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e3f0: cmp             SP, x16
    //     0xc4e3f4: b.ls            #0xc4e434
    // 0xc4e3f8: r1 = Null
    //     0xc4e3f8: mov             x1, NULL
    // 0xc4e3fc: r2 = 4
    //     0xc4e3fc: movz            x2, #0x4
    // 0xc4e400: r0 = AllocateArray()
    //     0xc4e400: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e404: r16 = "FontStyle."
    //     0xc4e404: add             x16, PP, #0x33, lsl #12  ; [pp+0x33670] "FontStyle."
    //     0xc4e408: ldr             x16, [x16, #0x670]
    // 0xc4e40c: StoreField: r0->field_f = r16
    //     0xc4e40c: stur            w16, [x0, #0xf]
    // 0xc4e410: ldur            x1, [fp, #-8]
    // 0xc4e414: LoadField: r2 = r1->field_f
    //     0xc4e414: ldur            w2, [x1, #0xf]
    // 0xc4e418: DecompressPointer r2
    //     0xc4e418: add             x2, x2, HEAP, lsl #32
    // 0xc4e41c: StoreField: r0->field_13 = r2
    //     0xc4e41c: stur            w2, [x0, #0x13]
    // 0xc4e420: str             x0, [SP]
    // 0xc4e424: r0 = _interpolate()
    //     0xc4e424: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e428: LeaveFrame
    //     0xc4e428: mov             SP, fp
    //     0xc4e42c: ldp             fp, lr, [SP], #0x10
    // 0xc4e430: ret
    //     0xc4e430: ret             
    // 0xc4e434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e438: b               #0xc4e3f8
  }
}

// class id: 6788, size: 0x14, field offset: 0x14
enum FontWeight extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e374, size: 0x64
    // 0xc4e374: EnterFrame
    //     0xc4e374: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e378: mov             fp, SP
    // 0xc4e37c: AllocStack(0x10)
    //     0xc4e37c: sub             SP, SP, #0x10
    // 0xc4e380: SetupParameters(FontWeight this /* r1 => r0, fp-0x8 */)
    //     0xc4e380: mov             x0, x1
    //     0xc4e384: stur            x1, [fp, #-8]
    // 0xc4e388: CheckStackOverflow
    //     0xc4e388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e38c: cmp             SP, x16
    //     0xc4e390: b.ls            #0xc4e3d0
    // 0xc4e394: r1 = Null
    //     0xc4e394: mov             x1, NULL
    // 0xc4e398: r2 = 4
    //     0xc4e398: movz            x2, #0x4
    // 0xc4e39c: r0 = AllocateArray()
    //     0xc4e39c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e3a0: r16 = "FontWeight."
    //     0xc4e3a0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33678] "FontWeight."
    //     0xc4e3a4: ldr             x16, [x16, #0x678]
    // 0xc4e3a8: StoreField: r0->field_f = r16
    //     0xc4e3a8: stur            w16, [x0, #0xf]
    // 0xc4e3ac: ldur            x1, [fp, #-8]
    // 0xc4e3b0: LoadField: r2 = r1->field_f
    //     0xc4e3b0: ldur            w2, [x1, #0xf]
    // 0xc4e3b4: DecompressPointer r2
    //     0xc4e3b4: add             x2, x2, HEAP, lsl #32
    // 0xc4e3b8: StoreField: r0->field_13 = r2
    //     0xc4e3b8: stur            w2, [x0, #0x13]
    // 0xc4e3bc: str             x0, [SP]
    // 0xc4e3c0: r0 = _interpolate()
    //     0xc4e3c0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e3c4: LeaveFrame
    //     0xc4e3c4: mov             SP, fp
    //     0xc4e3c8: ldp             fp, lr, [SP], #0x10
    // 0xc4e3cc: ret
    //     0xc4e3cc: ret             
    // 0xc4e3d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e3d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e3d4: b               #0xc4e394
  }
}
