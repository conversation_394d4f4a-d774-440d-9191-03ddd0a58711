// lib: , url: package:pdf/src/widgets/theme.dart

// class id: 1050864, size: 0x8
class :: {
}

// class id: 760, size: 0x8, field offset: 0x8
abstract class Theme extends Object {

  static _ of(/* No info */) {
    // ** addr: 0xb125d8, size: 0x4c
    // 0xb125d8: EnterFrame
    //     0xb125d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb125dc: mov             fp, SP
    // 0xb125e0: AllocStack(0x10)
    //     0xb125e0: sub             SP, SP, #0x10
    // 0xb125e4: CheckStackOverflow
    //     0xb125e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb125e8: cmp             SP, x16
    //     0xb125ec: b.ls            #0xb12618
    // 0xb125f0: r16 = <ThemeData>
    //     0xb125f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e210] TypeArguments: <ThemeData>
    //     0xb125f4: ldr             x16, [x16, #0x210]
    // 0xb125f8: stp             x1, x16, [SP]
    // 0xb125fc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb125fc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb12600: r0 = dependsOn()
    //     0xb12600: bl              #0xb12624  ; [package:pdf/src/widgets/widget.dart] Context::dependsOn
    // 0xb12604: cmp             w0, NULL
    // 0xb12608: b.eq            #0xb12620
    // 0xb1260c: LeaveFrame
    //     0xb1260c: mov             SP, fp
    //     0xb12610: ldp             fp, lr, [SP], #0x10
    // 0xb12614: ret
    //     0xb12614: ret             
    // 0xb12618: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb12618: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1261c: b               #0xb125f0
    // 0xb12620: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb12620: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 763, size: 0x48, field offset: 0x8
class ThemeData extends Inherited {

  factory _ ThemeData(/* No info */) {
    // ** addr: 0xb0f6c4, size: 0x54
    // 0xb0f6c4: EnterFrame
    //     0xb0f6c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb0f6c8: mov             fp, SP
    // 0xb0f6cc: AllocStack(0x18)
    //     0xb0f6cc: sub             SP, SP, #0x18
    // 0xb0f6d0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0xb0f6d0: stur            x2, [fp, #-8]
    //     0xb0f6d4: stur            x3, [fp, #-0x10]
    //     0xb0f6d8: stur            x5, [fp, #-0x18]
    // 0xb0f6dc: CheckStackOverflow
    //     0xb0f6dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0f6e0: cmp             SP, x16
    //     0xb0f6e4: b.ls            #0xb0f710
    // 0xb0f6e8: r1 = Null
    //     0xb0f6e8: mov             x1, NULL
    // 0xb0f6ec: r0 = ThemeData.withFont()
    //     0xb0f6ec: bl              #0xb0fea8  ; [package:pdf/src/widgets/theme.dart] ThemeData::ThemeData.withFont
    // 0xb0f6f0: mov             x1, x0
    // 0xb0f6f4: ldur            x2, [fp, #-8]
    // 0xb0f6f8: ldur            x3, [fp, #-0x10]
    // 0xb0f6fc: ldur            x5, [fp, #-0x18]
    // 0xb0f700: r0 = copyWith()
    //     0xb0f700: bl              #0xb0f73c  ; [package:pdf/src/widgets/theme.dart] ThemeData::copyWith
    // 0xb0f704: LeaveFrame
    //     0xb0f704: mov             SP, fp
    //     0xb0f708: ldp             fp, lr, [SP], #0x10
    // 0xb0f70c: ret
    //     0xb0f70c: ret             
    // 0xb0f710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0f710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0f714: b               #0xb0f6e8
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xb0f73c, size: 0x190
    // 0xb0f73c: EnterFrame
    //     0xb0f73c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0f740: mov             fp, SP
    // 0xb0f744: AllocStack(0xa0)
    //     0xb0f744: sub             SP, SP, #0xa0
    // 0xb0f748: SetupParameters(ThemeData this /* r1 => r4, fp-0x28 */, dynamic _ /* r3 => r3, fp-0x30 */, dynamic _ /* r5 => r0, fp-0x38 */)
    //     0xb0f748: mov             x4, x1
    //     0xb0f74c: mov             x0, x5
    //     0xb0f750: stur            x1, [fp, #-0x28]
    //     0xb0f754: stur            x3, [fp, #-0x30]
    //     0xb0f758: stur            x5, [fp, #-0x38]
    // 0xb0f75c: CheckStackOverflow
    //     0xb0f75c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0f760: cmp             SP, x16
    //     0xb0f764: b.ls            #0xb0f8c4
    // 0xb0f768: LoadField: r5 = r4->field_7
    //     0xb0f768: ldur            w5, [x4, #7]
    // 0xb0f76c: DecompressPointer r5
    //     0xb0f76c: add             x5, x5, HEAP, lsl #32
    // 0xb0f770: stur            x5, [fp, #-0x20]
    // 0xb0f774: LoadField: r6 = r4->field_b
    //     0xb0f774: ldur            w6, [x4, #0xb]
    // 0xb0f778: DecompressPointer r6
    //     0xb0f778: add             x6, x6, HEAP, lsl #32
    // 0xb0f77c: stur            x6, [fp, #-0x18]
    // 0xb0f780: LoadField: r7 = r4->field_27
    //     0xb0f780: ldur            w7, [x4, #0x27]
    // 0xb0f784: DecompressPointer r7
    //     0xb0f784: add             x7, x7, HEAP, lsl #32
    // 0xb0f788: stur            x7, [fp, #-0x10]
    // 0xb0f78c: LoadField: r8 = r4->field_f
    //     0xb0f78c: ldur            w8, [x4, #0xf]
    // 0xb0f790: DecompressPointer r8
    //     0xb0f790: add             x8, x8, HEAP, lsl #32
    // 0xb0f794: stur            x8, [fp, #-8]
    // 0xb0f798: LoadField: r1 = r4->field_13
    //     0xb0f798: ldur            w1, [x4, #0x13]
    // 0xb0f79c: DecompressPointer r1
    //     0xb0f79c: add             x1, x1, HEAP, lsl #32
    // 0xb0f7a0: r0 = merge()
    //     0xb0f7a0: bl              #0xb0faa8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::merge
    // 0xb0f7a4: mov             x3, x0
    // 0xb0f7a8: ldur            x0, [fp, #-0x28]
    // 0xb0f7ac: stur            x3, [fp, #-0x40]
    // 0xb0f7b0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb0f7b0: ldur            w1, [x0, #0x17]
    // 0xb0f7b4: DecompressPointer r1
    //     0xb0f7b4: add             x1, x1, HEAP, lsl #32
    // 0xb0f7b8: ldur            x2, [fp, #-0x30]
    // 0xb0f7bc: r0 = merge()
    //     0xb0f7bc: bl              #0xb0faa8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::merge
    // 0xb0f7c0: mov             x3, x0
    // 0xb0f7c4: ldur            x0, [fp, #-0x28]
    // 0xb0f7c8: stur            x3, [fp, #-0x30]
    // 0xb0f7cc: LoadField: r1 = r0->field_1b
    //     0xb0f7cc: ldur            w1, [x0, #0x1b]
    // 0xb0f7d0: DecompressPointer r1
    //     0xb0f7d0: add             x1, x1, HEAP, lsl #32
    // 0xb0f7d4: ldur            x2, [fp, #-0x38]
    // 0xb0f7d8: r0 = merge()
    //     0xb0f7d8: bl              #0xb0faa8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::merge
    // 0xb0f7dc: mov             x3, x0
    // 0xb0f7e0: ldur            x0, [fp, #-0x28]
    // 0xb0f7e4: stur            x3, [fp, #-0x38]
    // 0xb0f7e8: LoadField: r1 = r0->field_1f
    //     0xb0f7e8: ldur            w1, [x0, #0x1f]
    // 0xb0f7ec: DecompressPointer r1
    //     0xb0f7ec: add             x1, x1, HEAP, lsl #32
    // 0xb0f7f0: r2 = Null
    //     0xb0f7f0: mov             x2, NULL
    // 0xb0f7f4: r0 = merge()
    //     0xb0f7f4: bl              #0xb0faa8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::merge
    // 0xb0f7f8: mov             x3, x0
    // 0xb0f7fc: ldur            x0, [fp, #-0x28]
    // 0xb0f800: stur            x3, [fp, #-0x48]
    // 0xb0f804: LoadField: r1 = r0->field_23
    //     0xb0f804: ldur            w1, [x0, #0x23]
    // 0xb0f808: DecompressPointer r1
    //     0xb0f808: add             x1, x1, HEAP, lsl #32
    // 0xb0f80c: r2 = Null
    //     0xb0f80c: mov             x2, NULL
    // 0xb0f810: r0 = merge()
    //     0xb0f810: bl              #0xb0faa8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::merge
    // 0xb0f814: mov             x3, x0
    // 0xb0f818: ldur            x0, [fp, #-0x28]
    // 0xb0f81c: stur            x3, [fp, #-0x50]
    // 0xb0f820: LoadField: r1 = r0->field_2b
    //     0xb0f820: ldur            w1, [x0, #0x2b]
    // 0xb0f824: DecompressPointer r1
    //     0xb0f824: add             x1, x1, HEAP, lsl #32
    // 0xb0f828: r2 = Null
    //     0xb0f828: mov             x2, NULL
    // 0xb0f82c: r0 = merge()
    //     0xb0f82c: bl              #0xb0faa8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::merge
    // 0xb0f830: mov             x3, x0
    // 0xb0f834: ldur            x0, [fp, #-0x28]
    // 0xb0f838: stur            x3, [fp, #-0x58]
    // 0xb0f83c: LoadField: r1 = r0->field_2f
    //     0xb0f83c: ldur            w1, [x0, #0x2f]
    // 0xb0f840: DecompressPointer r1
    //     0xb0f840: add             x1, x1, HEAP, lsl #32
    // 0xb0f844: r2 = Null
    //     0xb0f844: mov             x2, NULL
    // 0xb0f848: r0 = merge()
    //     0xb0f848: bl              #0xb0faa8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::merge
    // 0xb0f84c: mov             x1, x0
    // 0xb0f850: ldur            x0, [fp, #-0x28]
    // 0xb0f854: stur            x1, [fp, #-0x68]
    // 0xb0f858: LoadField: r2 = r0->field_43
    //     0xb0f858: ldur            w2, [x0, #0x43]
    // 0xb0f85c: DecompressPointer r2
    //     0xb0f85c: add             x2, x2, HEAP, lsl #32
    // 0xb0f860: stur            x2, [fp, #-0x60]
    // 0xb0f864: r0 = ThemeData()
    //     0xb0f864: bl              #0xb0fa9c  ; AllocateThemeDataStub -> ThemeData (size=0x48)
    // 0xb0f868: stur            x0, [fp, #-0x28]
    // 0xb0f86c: ldur            x16, [fp, #-0x38]
    // 0xb0f870: ldur            lr, [fp, #-0x48]
    // 0xb0f874: stp             lr, x16, [SP, #0x28]
    // 0xb0f878: ldur            x16, [fp, #-0x50]
    // 0xb0f87c: ldur            lr, [fp, #-0x60]
    // 0xb0f880: stp             lr, x16, [SP, #0x18]
    // 0xb0f884: ldur            x16, [fp, #-0x18]
    // 0xb0f888: ldur            lr, [fp, #-0x68]
    // 0xb0f88c: stp             lr, x16, [SP, #8]
    // 0xb0f890: ldur            x16, [fp, #-0x58]
    // 0xb0f894: str             x16, [SP]
    // 0xb0f898: mov             x1, x0
    // 0xb0f89c: ldur            x2, [fp, #-0x10]
    // 0xb0f8a0: ldur            x3, [fp, #-0x20]
    // 0xb0f8a4: ldur            x5, [fp, #-8]
    // 0xb0f8a8: ldur            x6, [fp, #-0x40]
    // 0xb0f8ac: ldur            x7, [fp, #-0x30]
    // 0xb0f8b0: r0 = ThemeData._()
    //     0xb0f8b0: bl              #0xb0f8cc  ; [package:pdf/src/widgets/theme.dart] ThemeData::ThemeData._
    // 0xb0f8b4: ldur            x0, [fp, #-0x28]
    // 0xb0f8b8: LeaveFrame
    //     0xb0f8b8: mov             SP, fp
    //     0xb0f8bc: ldp             fp, lr, [SP], #0x10
    // 0xb0f8c0: ret
    //     0xb0f8c0: ret             
    // 0xb0f8c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0f8c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0f8c8: b               #0xb0f768
  }
  _ ThemeData._(/* No info */) {
    // ** addr: 0xb0f8cc, size: 0x1d0
    // 0xb0f8cc: EnterFrame
    //     0xb0f8cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb0f8d0: mov             fp, SP
    // 0xb0f8d4: r8 = true
    //     0xb0f8d4: add             x8, NULL, #0x20  ; true
    // 0xb0f8d8: r4 = Instance_TextOverflow
    //     0xb0f8d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e1f8] Obj!TextOverflow@e2e561
    //     0xb0f8dc: ldr             x4, [x4, #0x1f8]
    // 0xb0f8e0: mov             x0, x3
    // 0xb0f8e4: mov             x16, x7
    // 0xb0f8e8: mov             x7, x1
    // 0xb0f8ec: mov             x1, x16
    // 0xb0f8f0: mov             x16, x6
    // 0xb0f8f4: mov             x6, x2
    // 0xb0f8f8: mov             x2, x16
    // 0xb0f8fc: mov             x16, x5
    // 0xb0f900: mov             x5, x3
    // 0xb0f904: mov             x3, x16
    // 0xb0f908: StoreField: r7->field_7 = r0
    //     0xb0f908: stur            w0, [x7, #7]
    //     0xb0f90c: ldurb           w16, [x7, #-1]
    //     0xb0f910: ldurb           w17, [x0, #-1]
    //     0xb0f914: and             x16, x17, x16, lsr #2
    //     0xb0f918: tst             x16, HEAP, lsr #32
    //     0xb0f91c: b.eq            #0xb0f924
    //     0xb0f920: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0f924: ldr             x0, [fp, #0x20]
    // 0xb0f928: StoreField: r7->field_b = r0
    //     0xb0f928: stur            w0, [x7, #0xb]
    //     0xb0f92c: ldurb           w16, [x7, #-1]
    //     0xb0f930: ldurb           w17, [x0, #-1]
    //     0xb0f934: and             x16, x17, x16, lsr #2
    //     0xb0f938: tst             x16, HEAP, lsr #32
    //     0xb0f93c: b.eq            #0xb0f944
    //     0xb0f940: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0f944: mov             x0, x3
    // 0xb0f948: StoreField: r7->field_f = r0
    //     0xb0f948: stur            w0, [x7, #0xf]
    //     0xb0f94c: ldurb           w16, [x7, #-1]
    //     0xb0f950: ldurb           w17, [x0, #-1]
    //     0xb0f954: and             x16, x17, x16, lsr #2
    //     0xb0f958: tst             x16, HEAP, lsr #32
    //     0xb0f95c: b.eq            #0xb0f964
    //     0xb0f960: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0f964: mov             x0, x2
    // 0xb0f968: StoreField: r7->field_13 = r0
    //     0xb0f968: stur            w0, [x7, #0x13]
    //     0xb0f96c: ldurb           w16, [x7, #-1]
    //     0xb0f970: ldurb           w17, [x0, #-1]
    //     0xb0f974: and             x16, x17, x16, lsr #2
    //     0xb0f978: tst             x16, HEAP, lsr #32
    //     0xb0f97c: b.eq            #0xb0f984
    //     0xb0f980: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0f984: mov             x0, x1
    // 0xb0f988: ArrayStore: r7[0] = r0  ; List_4
    //     0xb0f988: stur            w0, [x7, #0x17]
    //     0xb0f98c: ldurb           w16, [x7, #-1]
    //     0xb0f990: ldurb           w17, [x0, #-1]
    //     0xb0f994: and             x16, x17, x16, lsr #2
    //     0xb0f998: tst             x16, HEAP, lsr #32
    //     0xb0f99c: b.eq            #0xb0f9a4
    //     0xb0f9a0: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0f9a4: ldr             x0, [fp, #0x40]
    // 0xb0f9a8: StoreField: r7->field_1b = r0
    //     0xb0f9a8: stur            w0, [x7, #0x1b]
    //     0xb0f9ac: ldurb           w16, [x7, #-1]
    //     0xb0f9b0: ldurb           w17, [x0, #-1]
    //     0xb0f9b4: and             x16, x17, x16, lsr #2
    //     0xb0f9b8: tst             x16, HEAP, lsr #32
    //     0xb0f9bc: b.eq            #0xb0f9c4
    //     0xb0f9c0: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0f9c4: ldr             x0, [fp, #0x38]
    // 0xb0f9c8: StoreField: r7->field_1f = r0
    //     0xb0f9c8: stur            w0, [x7, #0x1f]
    //     0xb0f9cc: ldurb           w16, [x7, #-1]
    //     0xb0f9d0: ldurb           w17, [x0, #-1]
    //     0xb0f9d4: and             x16, x17, x16, lsr #2
    //     0xb0f9d8: tst             x16, HEAP, lsr #32
    //     0xb0f9dc: b.eq            #0xb0f9e4
    //     0xb0f9e0: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0f9e4: ldr             x0, [fp, #0x30]
    // 0xb0f9e8: StoreField: r7->field_23 = r0
    //     0xb0f9e8: stur            w0, [x7, #0x23]
    //     0xb0f9ec: ldurb           w16, [x7, #-1]
    //     0xb0f9f0: ldurb           w17, [x0, #-1]
    //     0xb0f9f4: and             x16, x17, x16, lsr #2
    //     0xb0f9f8: tst             x16, HEAP, lsr #32
    //     0xb0f9fc: b.eq            #0xb0fa04
    //     0xb0fa00: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0fa04: mov             x0, x6
    // 0xb0fa08: StoreField: r7->field_27 = r0
    //     0xb0fa08: stur            w0, [x7, #0x27]
    //     0xb0fa0c: ldurb           w16, [x7, #-1]
    //     0xb0fa10: ldurb           w17, [x0, #-1]
    //     0xb0fa14: and             x16, x17, x16, lsr #2
    //     0xb0fa18: tst             x16, HEAP, lsr #32
    //     0xb0fa1c: b.eq            #0xb0fa24
    //     0xb0fa20: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0fa24: ldr             x0, [fp, #0x10]
    // 0xb0fa28: StoreField: r7->field_2b = r0
    //     0xb0fa28: stur            w0, [x7, #0x2b]
    //     0xb0fa2c: ldurb           w16, [x7, #-1]
    //     0xb0fa30: ldurb           w17, [x0, #-1]
    //     0xb0fa34: and             x16, x17, x16, lsr #2
    //     0xb0fa38: tst             x16, HEAP, lsr #32
    //     0xb0fa3c: b.eq            #0xb0fa44
    //     0xb0fa40: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0fa44: ldr             x0, [fp, #0x18]
    // 0xb0fa48: StoreField: r7->field_2f = r0
    //     0xb0fa48: stur            w0, [x7, #0x2f]
    //     0xb0fa4c: ldurb           w16, [x7, #-1]
    //     0xb0fa50: ldurb           w17, [x0, #-1]
    //     0xb0fa54: and             x16, x17, x16, lsr #2
    //     0xb0fa58: tst             x16, HEAP, lsr #32
    //     0xb0fa5c: b.eq            #0xb0fa64
    //     0xb0fa60: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0fa64: StoreField: r7->field_37 = r8
    //     0xb0fa64: stur            w8, [x7, #0x37]
    // 0xb0fa68: StoreField: r7->field_3f = r4
    //     0xb0fa68: stur            w4, [x7, #0x3f]
    // 0xb0fa6c: ldr             x0, [fp, #0x28]
    // 0xb0fa70: StoreField: r7->field_43 = r0
    //     0xb0fa70: stur            w0, [x7, #0x43]
    //     0xb0fa74: ldurb           w16, [x7, #-1]
    //     0xb0fa78: ldurb           w17, [x0, #-1]
    //     0xb0fa7c: and             x16, x17, x16, lsr #2
    //     0xb0fa80: tst             x16, HEAP, lsr #32
    //     0xb0fa84: b.eq            #0xb0fa8c
    //     0xb0fa88: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb0fa8c: r0 = Null
    //     0xb0fa8c: mov             x0, NULL
    // 0xb0fa90: LeaveFrame
    //     0xb0fa90: mov             SP, fp
    //     0xb0fa94: ldp             fp, lr, [SP], #0x10
    // 0xb0fa98: ret
    //     0xb0fa98: ret             
  }
  factory _ ThemeData.withFont(/* No info */) {
    // ** addr: 0xb0fea8, size: 0x424
    // 0xb0fea8: EnterFrame
    //     0xb0fea8: stp             fp, lr, [SP, #-0x10]!
    //     0xb0feac: mov             fp, SP
    // 0xb0feb0: AllocStack(0xa8)
    //     0xb0feb0: sub             SP, SP, #0xa8
    // 0xb0feb4: CheckStackOverflow
    //     0xb0feb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0feb8: cmp             SP, x16
    //     0xb0febc: b.ls            #0xb10200
    // 0xb0fec0: r1 = Null
    //     0xb0fec0: mov             x1, NULL
    // 0xb0fec4: r0 = TextStyle.defaultStyle()
    //     0xb0fec4: bl              #0xb102d8  ; [package:pdf/src/widgets/text_style.dart] TextStyle::TextStyle.defaultStyle
    // 0xb0fec8: stp             NULL, NULL, [SP, #0x20]
    // 0xb0fecc: stp             NULL, NULL, [SP, #0x10]
    // 0xb0fed0: stp             NULL, NULL, [SP]
    // 0xb0fed4: mov             x1, x0
    // 0xb0fed8: r4 = const [0, 0x7, 0x6, 0x1, font, 0x1, fontBold, 0x3, fontBoldItalic, 0x5, fontFallback, 0x6, fontItalic, 0x4, fontNormal, 0x2, null]
    //     0xb0fed8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e518] List(17) [0, 0x7, 0x6, 0x1, "font", 0x1, "fontBold", 0x3, "fontBoldItalic", 0x5, "fontFallback", 0x6, "fontItalic", 0x4, "fontNormal", 0x2, Null]
    //     0xb0fedc: ldr             x4, [x4, #0x518]
    // 0xb0fee0: r0 = copyWith()
    //     0xb0fee0: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb0fee4: stur            x0, [fp, #-0x10]
    // 0xb0fee8: LoadField: r2 = r0->field_23
    //     0xb0fee8: ldur            w2, [x0, #0x23]
    // 0xb0feec: DecompressPointer r2
    //     0xb0feec: add             x2, x2, HEAP, lsl #32
    // 0xb0fef0: stur            x2, [fp, #-8]
    // 0xb0fef4: cmp             w2, NULL
    // 0xb0fef8: b.eq            #0xb10208
    // 0xb0fefc: r16 = 5.000000
    //     0xb0fefc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e520] 5
    //     0xb0ff00: ldr             x16, [x16, #0x520]
    // 0xb0ff04: str             x16, [SP]
    // 0xb0ff08: mov             x1, x0
    // 0xb0ff0c: r4 = const [0, 0x2, 0x1, 0x1, lineSpacing, 0x1, null]
    //     0xb0ff0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e528] List(7) [0, 0x2, 0x1, 0x1, "lineSpacing", 0x1, Null]
    //     0xb0ff10: ldr             x4, [x4, #0x528]
    // 0xb0ff14: r0 = copyWith()
    //     0xb0ff14: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb0ff18: stur            x0, [fp, #-0x18]
    // 0xb0ff1c: r16 = 5.000000
    //     0xb0ff1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e520] 5
    //     0xb0ff20: ldr             x16, [x16, #0x520]
    // 0xb0ff24: str             x16, [SP]
    // 0xb0ff28: ldur            x1, [fp, #-0x10]
    // 0xb0ff2c: r4 = const [0, 0x2, 0x1, 0x1, lineSpacing, 0x1, null]
    //     0xb0ff2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e528] List(7) [0, 0x2, 0x1, 0x1, "lineSpacing", 0x1, Null]
    //     0xb0ff30: ldr             x4, [x4, #0x528]
    // 0xb0ff34: r0 = copyWith()
    //     0xb0ff34: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb0ff38: mov             x2, x0
    // 0xb0ff3c: ldur            x0, [fp, #-8]
    // 0xb0ff40: stur            x2, [fp, #-0x20]
    // 0xb0ff44: LoadField: d0 = r0->field_7
    //     0xb0ff44: ldur            d0, [x0, #7]
    // 0xb0ff48: stur            d0, [fp, #-0x70]
    // 0xb0ff4c: d1 = 2.000000
    //     0xb0ff4c: fmov            d1, #2.00000000
    // 0xb0ff50: fmul            d2, d0, d1
    // 0xb0ff54: r0 = inline_Allocate_Double()
    //     0xb0ff54: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb0ff58: add             x0, x0, #0x10
    //     0xb0ff5c: cmp             x1, x0
    //     0xb0ff60: b.ls            #0xb1020c
    //     0xb0ff64: str             x0, [THR, #0x50]  ; THR::top
    //     0xb0ff68: sub             x0, x0, #0xf
    //     0xb0ff6c: movz            x1, #0xe15c
    //     0xb0ff70: movk            x1, #0x3, lsl #16
    //     0xb0ff74: stur            x1, [x0, #-1]
    // 0xb0ff78: StoreField: r0->field_7 = d2
    //     0xb0ff78: stur            d2, [x0, #7]
    // 0xb0ff7c: str             x0, [SP]
    // 0xb0ff80: ldur            x1, [fp, #-0x10]
    // 0xb0ff84: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb0ff84: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb0ff88: ldr             x4, [x4, #0x88]
    // 0xb0ff8c: r0 = copyWith()
    //     0xb0ff8c: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb0ff90: ldur            d0, [fp, #-0x70]
    // 0xb0ff94: d1 = 1.500000
    //     0xb0ff94: fmov            d1, #1.50000000
    // 0xb0ff98: stur            x0, [fp, #-8]
    // 0xb0ff9c: fmul            d2, d0, d1
    // 0xb0ffa0: r1 = inline_Allocate_Double()
    //     0xb0ffa0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb0ffa4: add             x1, x1, #0x10
    //     0xb0ffa8: cmp             x2, x1
    //     0xb0ffac: b.ls            #0xb10224
    //     0xb0ffb0: str             x1, [THR, #0x50]  ; THR::top
    //     0xb0ffb4: sub             x1, x1, #0xf
    //     0xb0ffb8: movz            x2, #0xe15c
    //     0xb0ffbc: movk            x2, #0x3, lsl #16
    //     0xb0ffc0: stur            x2, [x1, #-1]
    // 0xb0ffc4: StoreField: r1->field_7 = d2
    //     0xb0ffc4: stur            d2, [x1, #7]
    // 0xb0ffc8: str             x1, [SP]
    // 0xb0ffcc: ldur            x1, [fp, #-0x10]
    // 0xb0ffd0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb0ffd0: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb0ffd4: ldr             x4, [x4, #0x88]
    // 0xb0ffd8: r0 = copyWith()
    //     0xb0ffd8: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb0ffdc: ldur            d0, [fp, #-0x70]
    // 0xb0ffe0: d1 = 1.400000
    //     0xb0ffe0: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e530] IMM: double(1.4) from 0x3ff6666666666666
    //     0xb0ffe4: ldr             d1, [x17, #0x530]
    // 0xb0ffe8: stur            x0, [fp, #-0x28]
    // 0xb0ffec: fmul            d2, d0, d1
    // 0xb0fff0: r1 = inline_Allocate_Double()
    //     0xb0fff0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb0fff4: add             x1, x1, #0x10
    //     0xb0fff8: cmp             x2, x1
    //     0xb0fffc: b.ls            #0xb10240
    //     0xb10000: str             x1, [THR, #0x50]  ; THR::top
    //     0xb10004: sub             x1, x1, #0xf
    //     0xb10008: movz            x2, #0xe15c
    //     0xb1000c: movk            x2, #0x3, lsl #16
    //     0xb10010: stur            x2, [x1, #-1]
    // 0xb10014: StoreField: r1->field_7 = d2
    //     0xb10014: stur            d2, [x1, #7]
    // 0xb10018: str             x1, [SP]
    // 0xb1001c: ldur            x1, [fp, #-0x10]
    // 0xb10020: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb10020: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb10024: ldr             x4, [x4, #0x88]
    // 0xb10028: r0 = copyWith()
    //     0xb10028: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb1002c: ldur            d0, [fp, #-0x70]
    // 0xb10030: d1 = 1.300000
    //     0xb10030: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e538] IMM: double(1.3) from 0x3ff4cccccccccccd
    //     0xb10034: ldr             d1, [x17, #0x538]
    // 0xb10038: stur            x0, [fp, #-0x30]
    // 0xb1003c: fmul            d2, d0, d1
    // 0xb10040: r1 = inline_Allocate_Double()
    //     0xb10040: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb10044: add             x1, x1, #0x10
    //     0xb10048: cmp             x2, x1
    //     0xb1004c: b.ls            #0xb1025c
    //     0xb10050: str             x1, [THR, #0x50]  ; THR::top
    //     0xb10054: sub             x1, x1, #0xf
    //     0xb10058: movz            x2, #0xe15c
    //     0xb1005c: movk            x2, #0x3, lsl #16
    //     0xb10060: stur            x2, [x1, #-1]
    // 0xb10064: StoreField: r1->field_7 = d2
    //     0xb10064: stur            d2, [x1, #7]
    // 0xb10068: str             x1, [SP]
    // 0xb1006c: ldur            x1, [fp, #-0x10]
    // 0xb10070: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb10070: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb10074: ldr             x4, [x4, #0x88]
    // 0xb10078: r0 = copyWith()
    //     0xb10078: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb1007c: ldur            d0, [fp, #-0x70]
    // 0xb10080: d1 = 1.200000
    //     0xb10080: add             x17, PP, #0x25, lsl #12  ; [pp+0x25d28] IMM: double(1.2) from 0x3ff3333333333333
    //     0xb10084: ldr             d1, [x17, #0xd28]
    // 0xb10088: stur            x0, [fp, #-0x38]
    // 0xb1008c: fmul            d2, d0, d1
    // 0xb10090: r1 = inline_Allocate_Double()
    //     0xb10090: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb10094: add             x1, x1, #0x10
    //     0xb10098: cmp             x2, x1
    //     0xb1009c: b.ls            #0xb10278
    //     0xb100a0: str             x1, [THR, #0x50]  ; THR::top
    //     0xb100a4: sub             x1, x1, #0xf
    //     0xb100a8: movz            x2, #0xe15c
    //     0xb100ac: movk            x2, #0x3, lsl #16
    //     0xb100b0: stur            x2, [x1, #-1]
    // 0xb100b4: StoreField: r1->field_7 = d2
    //     0xb100b4: stur            d2, [x1, #7]
    // 0xb100b8: str             x1, [SP]
    // 0xb100bc: ldur            x1, [fp, #-0x10]
    // 0xb100c0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb100c0: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb100c4: ldr             x4, [x4, #0x88]
    // 0xb100c8: r0 = copyWith()
    //     0xb100c8: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb100cc: ldur            d0, [fp, #-0x70]
    // 0xb100d0: d1 = 1.100000
    //     0xb100d0: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e540] IMM: double(1.1) from 0x3ff199999999999a
    //     0xb100d4: ldr             d1, [x17, #0x540]
    // 0xb100d8: stur            x0, [fp, #-0x40]
    // 0xb100dc: fmul            d2, d0, d1
    // 0xb100e0: r1 = inline_Allocate_Double()
    //     0xb100e0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb100e4: add             x1, x1, #0x10
    //     0xb100e8: cmp             x2, x1
    //     0xb100ec: b.ls            #0xb10294
    //     0xb100f0: str             x1, [THR, #0x50]  ; THR::top
    //     0xb100f4: sub             x1, x1, #0xf
    //     0xb100f8: movz            x2, #0xe15c
    //     0xb100fc: movk            x2, #0x3, lsl #16
    //     0xb10100: stur            x2, [x1, #-1]
    // 0xb10104: StoreField: r1->field_7 = d2
    //     0xb10104: stur            d2, [x1, #7]
    // 0xb10108: str             x1, [SP]
    // 0xb1010c: ldur            x1, [fp, #-0x10]
    // 0xb10110: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb10110: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb10114: ldr             x4, [x4, #0x88]
    // 0xb10118: r0 = copyWith()
    //     0xb10118: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb1011c: ldur            d0, [fp, #-0x70]
    // 0xb10120: d1 = 0.800000
    //     0xb10120: add             x17, PP, #0x29, lsl #12  ; [pp+0x29048] IMM: double(0.8) from 0x3fe999999999999a
    //     0xb10124: ldr             d1, [x17, #0x48]
    // 0xb10128: stur            x0, [fp, #-0x50]
    // 0xb1012c: fmul            d2, d0, d1
    // 0xb10130: r2 = inline_Allocate_Double()
    //     0xb10130: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0xb10134: add             x2, x2, #0x10
    //     0xb10138: cmp             x1, x2
    //     0xb1013c: b.ls            #0xb102b0
    //     0xb10140: str             x2, [THR, #0x50]  ; THR::top
    //     0xb10144: sub             x2, x2, #0xf
    //     0xb10148: movz            x1, #0xe15c
    //     0xb1014c: movk            x1, #0x3, lsl #16
    //     0xb10150: stur            x1, [x2, #-1]
    // 0xb10154: StoreField: r2->field_7 = d2
    //     0xb10154: stur            d2, [x2, #7]
    // 0xb10158: stur            x2, [fp, #-0x48]
    // 0xb1015c: r16 = Instance_FontWeight
    //     0xb1015c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e198] Obj!FontWeight@e2e541
    //     0xb10160: ldr             x16, [x16, #0x198]
    // 0xb10164: stp             x16, x2, [SP]
    // 0xb10168: ldur            x1, [fp, #-0x10]
    // 0xb1016c: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x1, fontWeight, 0x2, null]
    //     0xb1016c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cdf8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x1, "fontWeight", 0x2, Null]
    //     0xb10170: ldr             x4, [x4, #0xdf8]
    // 0xb10174: r0 = copyWith()
    //     0xb10174: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb10178: stur            x0, [fp, #-0x58]
    // 0xb1017c: ldur            x16, [fp, #-0x48]
    // 0xb10180: str             x16, [SP]
    // 0xb10184: ldur            x1, [fp, #-0x10]
    // 0xb10188: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb10188: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb1018c: ldr             x4, [x4, #0x88]
    // 0xb10190: r0 = copyWith()
    //     0xb10190: bl              #0xb107b0  ; [package:pdf/src/widgets/text_style.dart] TextStyle::copyWith
    // 0xb10194: stur            x0, [fp, #-0x48]
    // 0xb10198: r0 = IconThemeData()
    //     0xb10198: bl              #0xb102cc  ; AllocateIconThemeDataStub -> IconThemeData (size=0x8)
    // 0xb1019c: stur            x0, [fp, #-0x60]
    // 0xb101a0: r0 = ThemeData()
    //     0xb101a0: bl              #0xb0fa9c  ; AllocateThemeDataStub -> ThemeData (size=0x48)
    // 0xb101a4: stur            x0, [fp, #-0x68]
    // 0xb101a8: ldur            x16, [fp, #-0x38]
    // 0xb101ac: ldur            lr, [fp, #-0x40]
    // 0xb101b0: stp             lr, x16, [SP, #0x28]
    // 0xb101b4: ldur            x16, [fp, #-0x50]
    // 0xb101b8: ldur            lr, [fp, #-0x60]
    // 0xb101bc: stp             lr, x16, [SP, #0x18]
    // 0xb101c0: ldur            x16, [fp, #-0x18]
    // 0xb101c4: ldur            lr, [fp, #-0x48]
    // 0xb101c8: stp             lr, x16, [SP, #8]
    // 0xb101cc: ldur            x16, [fp, #-0x58]
    // 0xb101d0: str             x16, [SP]
    // 0xb101d4: mov             x1, x0
    // 0xb101d8: ldur            x2, [fp, #-0x20]
    // 0xb101dc: ldur            x3, [fp, #-0x10]
    // 0xb101e0: ldur            x5, [fp, #-8]
    // 0xb101e4: ldur            x6, [fp, #-0x28]
    // 0xb101e8: ldur            x7, [fp, #-0x30]
    // 0xb101ec: r0 = ThemeData._()
    //     0xb101ec: bl              #0xb0f8cc  ; [package:pdf/src/widgets/theme.dart] ThemeData::ThemeData._
    // 0xb101f0: ldur            x0, [fp, #-0x68]
    // 0xb101f4: LeaveFrame
    //     0xb101f4: mov             SP, fp
    //     0xb101f8: ldp             fp, lr, [SP], #0x10
    // 0xb101fc: ret
    //     0xb101fc: ret             
    // 0xb10200: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb10200: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb10204: b               #0xb0fec0
    // 0xb10208: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb10208: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1020c: stp             q0, q2, [SP, #-0x20]!
    // 0xb10210: SaveReg r2
    //     0xb10210: str             x2, [SP, #-8]!
    // 0xb10214: r0 = AllocateDouble()
    //     0xb10214: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb10218: RestoreReg r2
    //     0xb10218: ldr             x2, [SP], #8
    // 0xb1021c: ldp             q0, q2, [SP], #0x20
    // 0xb10220: b               #0xb0ff78
    // 0xb10224: stp             q0, q2, [SP, #-0x20]!
    // 0xb10228: SaveReg r0
    //     0xb10228: str             x0, [SP, #-8]!
    // 0xb1022c: r0 = AllocateDouble()
    //     0xb1022c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb10230: mov             x1, x0
    // 0xb10234: RestoreReg r0
    //     0xb10234: ldr             x0, [SP], #8
    // 0xb10238: ldp             q0, q2, [SP], #0x20
    // 0xb1023c: b               #0xb0ffc4
    // 0xb10240: stp             q0, q2, [SP, #-0x20]!
    // 0xb10244: SaveReg r0
    //     0xb10244: str             x0, [SP, #-8]!
    // 0xb10248: r0 = AllocateDouble()
    //     0xb10248: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb1024c: mov             x1, x0
    // 0xb10250: RestoreReg r0
    //     0xb10250: ldr             x0, [SP], #8
    // 0xb10254: ldp             q0, q2, [SP], #0x20
    // 0xb10258: b               #0xb10014
    // 0xb1025c: stp             q0, q2, [SP, #-0x20]!
    // 0xb10260: SaveReg r0
    //     0xb10260: str             x0, [SP, #-8]!
    // 0xb10264: r0 = AllocateDouble()
    //     0xb10264: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb10268: mov             x1, x0
    // 0xb1026c: RestoreReg r0
    //     0xb1026c: ldr             x0, [SP], #8
    // 0xb10270: ldp             q0, q2, [SP], #0x20
    // 0xb10274: b               #0xb10064
    // 0xb10278: stp             q0, q2, [SP, #-0x20]!
    // 0xb1027c: SaveReg r0
    //     0xb1027c: str             x0, [SP, #-8]!
    // 0xb10280: r0 = AllocateDouble()
    //     0xb10280: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb10284: mov             x1, x0
    // 0xb10288: RestoreReg r0
    //     0xb10288: ldr             x0, [SP], #8
    // 0xb1028c: ldp             q0, q2, [SP], #0x20
    // 0xb10290: b               #0xb100b4
    // 0xb10294: stp             q0, q2, [SP, #-0x20]!
    // 0xb10298: SaveReg r0
    //     0xb10298: str             x0, [SP, #-8]!
    // 0xb1029c: r0 = AllocateDouble()
    //     0xb1029c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb102a0: mov             x1, x0
    // 0xb102a4: RestoreReg r0
    //     0xb102a4: ldr             x0, [SP], #8
    // 0xb102a8: ldp             q0, q2, [SP], #0x20
    // 0xb102ac: b               #0xb10104
    // 0xb102b0: SaveReg d2
    //     0xb102b0: str             q2, [SP, #-0x10]!
    // 0xb102b4: SaveReg r0
    //     0xb102b4: str             x0, [SP, #-8]!
    // 0xb102b8: r0 = AllocateDouble()
    //     0xb102b8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb102bc: mov             x2, x0
    // 0xb102c0: RestoreReg r0
    //     0xb102c0: ldr             x0, [SP], #8
    // 0xb102c4: RestoreReg d2
    //     0xb102c4: ldr             q2, [SP], #0x10
    // 0xb102c8: b               #0xb10154
  }
}
