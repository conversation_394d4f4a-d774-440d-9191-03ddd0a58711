// lib: , url: package:pdf/src/widgets/widget.dart

// class id: 1050865, size: 0x8
class :: {
}

// class id: 759, size: 0x18, field offset: 0x8
//   const constructor, 
class Context extends Object {

  Y0? dependsOn<Y0>(Context) {
    // ** addr: 0xb12624, size: 0xcc
    // 0xb12624: EnterFrame
    //     0xb12624: stp             fp, lr, [SP, #-0x10]!
    //     0xb12628: mov             fp, SP
    // 0xb1262c: AllocStack(0x10)
    //     0xb1262c: sub             SP, SP, #0x10
    // 0xb12630: SetupParameters()
    //     0xb12630: ldur            w0, [x4, #0xf]
    //     0xb12634: cbnz            w0, #0xb12640
    //     0xb12638: mov             x3, NULL
    //     0xb1263c: b               #0xb12650
    //     0xb12640: ldur            w0, [x4, #0x17]
    //     0xb12644: add             x1, fp, w0, sxtw #2
    //     0xb12648: ldr             x1, [x1, #0x10]
    //     0xb1264c: mov             x3, x1
    //     0xb12650: ldr             x0, [fp, #0x10]
    //     0xb12654: stur            x3, [fp, #-0x10]
    // 0xb12658: CheckStackOverflow
    //     0xb12658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1265c: cmp             SP, x16
    //     0xb12660: b.ls            #0xb126e8
    // 0xb12664: LoadField: r4 = r0->field_f
    //     0xb12664: ldur            w4, [x0, #0xf]
    // 0xb12668: DecompressPointer r4
    //     0xb12668: add             x4, x4, HEAP, lsl #32
    // 0xb1266c: mov             x1, x3
    // 0xb12670: stur            x4, [fp, #-8]
    // 0xb12674: r2 = Null
    //     0xb12674: mov             x2, NULL
    // 0xb12678: r3 = Y0
    //     0xb12678: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e218] TypeParameter: Y0
    //     0xb1267c: ldr             x3, [x3, #0x218]
    // 0xb12680: r30 = InstantiateTypeNonNullableFunctionTypeParameterStub
    //     0xb12680: ldr             lr, [PP, #0x24e0]  ; [pp+0x24e0] Stub: InstantiateTypeNonNullableFunctionTypeParameter (0x5e10cc)
    // 0xb12684: LoadField: r30 = r30->field_7
    //     0xb12684: ldur            lr, [lr, #7]
    // 0xb12688: blr             lr
    // 0xb1268c: ldur            x1, [fp, #-8]
    // 0xb12690: mov             x2, x0
    // 0xb12694: r0 = []()
    //     0xb12694: bl              #0xd36ccc  ; [dart:collection] _HashMap::[]
    // 0xb12698: ldur            x1, [fp, #-0x10]
    // 0xb1269c: mov             x3, x0
    // 0xb126a0: r2 = Null
    //     0xb126a0: mov             x2, NULL
    // 0xb126a4: stur            x3, [fp, #-8]
    // 0xb126a8: cmp             w0, NULL
    // 0xb126ac: b.eq            #0xb126d8
    // 0xb126b0: cmp             w1, NULL
    // 0xb126b4: b.eq            #0xb126d8
    // 0xb126b8: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xb126b8: ldur            w4, [x1, #0x17]
    // 0xb126bc: DecompressPointer r4
    //     0xb126bc: add             x4, x4, HEAP, lsl #32
    // 0xb126c0: r8 = Y0?
    //     0xb126c0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e220] TypeParameter: Y0?
    //     0xb126c4: ldr             x8, [x8, #0x220]
    // 0xb126c8: LoadField: r9 = r4->field_7
    //     0xb126c8: ldur            x9, [x4, #7]
    // 0xb126cc: r3 = Null
    //     0xb126cc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e228] Null
    //     0xb126d0: ldr             x3, [x3, #0x228]
    // 0xb126d4: blr             x9
    // 0xb126d8: ldur            x0, [fp, #-8]
    // 0xb126dc: LeaveFrame
    //     0xb126dc: mov             SP, fp
    //     0xb126e0: ldp             fp, lr, [SP], #0x10
    // 0xb126e4: ret
    //     0xb126e4: ret             
    // 0xb126e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb126e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb126ec: b               #0xb12664
  }
  _ localToGlobal(/* No info */) {
    // ** addr: 0xe6d458, size: 0x64c
    // 0xe6d458: EnterFrame
    //     0xe6d458: stp             fp, lr, [SP, #-0x10]!
    //     0xe6d45c: mov             fp, SP
    // 0xe6d460: AllocStack(0x78)
    //     0xe6d460: sub             SP, SP, #0x78
    // 0xe6d464: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe6d464: stur            x2, [fp, #-8]
    // 0xe6d468: CheckStackOverflow
    //     0xe6d468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6d46c: cmp             SP, x16
    //     0xe6d470: b.ls            #0xe6d990
    // 0xe6d474: LoadField: r0 = r1->field_b
    //     0xe6d474: ldur            w0, [x1, #0xb]
    // 0xe6d478: DecompressPointer r0
    //     0xe6d478: add             x0, x0, HEAP, lsl #32
    // 0xe6d47c: cmp             w0, NULL
    // 0xe6d480: b.eq            #0xe6d998
    // 0xe6d484: mov             x1, x0
    // 0xe6d488: r0 = getTransform()
    //     0xe6d488: bl              #0xe6daa4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::getTransform
    // 0xe6d48c: mov             x1, x0
    // 0xe6d490: ldur            x0, [fp, #-8]
    // 0xe6d494: stur            x1, [fp, #-0x10]
    // 0xe6d498: LoadField: d0 = r0->field_7
    //     0xe6d498: ldur            d0, [x0, #7]
    // 0xe6d49c: stur            d0, [fp, #-0x68]
    // 0xe6d4a0: LoadField: d1 = r0->field_f
    //     0xe6d4a0: ldur            d1, [x0, #0xf]
    // 0xe6d4a4: stur            d1, [fp, #-0x60]
    // 0xe6d4a8: r0 = Vector3()
    //     0xe6d4a8: bl              #0x68e1e4  ; AllocateVector3Stub -> Vector3 (size=0xc)
    // 0xe6d4ac: r4 = 6
    //     0xe6d4ac: movz            x4, #0x6
    // 0xe6d4b0: stur            x0, [fp, #-0x18]
    // 0xe6d4b4: r0 = AllocateFloat64Array()
    //     0xe6d4b4: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6d4b8: ldur            x2, [fp, #-0x18]
    // 0xe6d4bc: StoreField: r2->field_7 = r0
    //     0xe6d4bc: stur            w0, [x2, #7]
    // 0xe6d4c0: ldur            d0, [fp, #-0x68]
    // 0xe6d4c4: ArrayStore: r0[0] = d0  ; List_8
    //     0xe6d4c4: stur            d0, [x0, #0x17]
    // 0xe6d4c8: ldur            d1, [fp, #-0x60]
    // 0xe6d4cc: StoreField: r0->field_1f = d1
    //     0xe6d4cc: stur            d1, [x0, #0x1f]
    // 0xe6d4d0: StoreField: r0->field_27 = rZR
    //     0xe6d4d0: stur            xzr, [x0, #0x27]
    // 0xe6d4d4: ldur            x1, [fp, #-0x10]
    // 0xe6d4d8: r0 = transform3()
    //     0xe6d4d8: bl              #0x6c20e8  ; [package:vector_math/vector_math_64.dart] Matrix4::transform3
    // 0xe6d4dc: mov             x1, x0
    // 0xe6d4e0: ldur            x0, [fp, #-8]
    // 0xe6d4e4: stur            x1, [fp, #-0x18]
    // 0xe6d4e8: LoadField: d0 = r0->field_1f
    //     0xe6d4e8: ldur            d0, [x0, #0x1f]
    // 0xe6d4ec: ldur            d1, [fp, #-0x60]
    // 0xe6d4f0: fadd            d2, d1, d0
    // 0xe6d4f4: stur            d2, [fp, #-0x70]
    // 0xe6d4f8: r0 = Vector3()
    //     0xe6d4f8: bl              #0x68e1e4  ; AllocateVector3Stub -> Vector3 (size=0xc)
    // 0xe6d4fc: r4 = 6
    //     0xe6d4fc: movz            x4, #0x6
    // 0xe6d500: stur            x0, [fp, #-0x20]
    // 0xe6d504: r0 = AllocateFloat64Array()
    //     0xe6d504: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6d508: ldur            x2, [fp, #-0x20]
    // 0xe6d50c: StoreField: r2->field_7 = r0
    //     0xe6d50c: stur            w0, [x2, #7]
    // 0xe6d510: ldur            d0, [fp, #-0x68]
    // 0xe6d514: ArrayStore: r0[0] = d0  ; List_8
    //     0xe6d514: stur            d0, [x0, #0x17]
    // 0xe6d518: ldur            d1, [fp, #-0x70]
    // 0xe6d51c: StoreField: r0->field_1f = d1
    //     0xe6d51c: stur            d1, [x0, #0x1f]
    // 0xe6d520: StoreField: r0->field_27 = rZR
    //     0xe6d520: stur            xzr, [x0, #0x27]
    // 0xe6d524: ldur            x1, [fp, #-0x10]
    // 0xe6d528: r0 = transform3()
    //     0xe6d528: bl              #0x6c20e8  ; [package:vector_math/vector_math_64.dart] Matrix4::transform3
    // 0xe6d52c: mov             x1, x0
    // 0xe6d530: ldur            x0, [fp, #-8]
    // 0xe6d534: stur            x1, [fp, #-0x20]
    // 0xe6d538: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe6d538: ldur            d0, [x0, #0x17]
    // 0xe6d53c: ldur            d1, [fp, #-0x68]
    // 0xe6d540: fadd            d2, d1, d0
    // 0xe6d544: stur            d2, [fp, #-0x78]
    // 0xe6d548: r0 = Vector3()
    //     0xe6d548: bl              #0x68e1e4  ; AllocateVector3Stub -> Vector3 (size=0xc)
    // 0xe6d54c: r4 = 6
    //     0xe6d54c: movz            x4, #0x6
    // 0xe6d550: stur            x0, [fp, #-8]
    // 0xe6d554: r0 = AllocateFloat64Array()
    //     0xe6d554: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6d558: ldur            x2, [fp, #-8]
    // 0xe6d55c: StoreField: r2->field_7 = r0
    //     0xe6d55c: stur            w0, [x2, #7]
    // 0xe6d560: ldur            d0, [fp, #-0x78]
    // 0xe6d564: ArrayStore: r0[0] = d0  ; List_8
    //     0xe6d564: stur            d0, [x0, #0x17]
    // 0xe6d568: ldur            d1, [fp, #-0x60]
    // 0xe6d56c: StoreField: r0->field_1f = d1
    //     0xe6d56c: stur            d1, [x0, #0x1f]
    // 0xe6d570: StoreField: r0->field_27 = rZR
    //     0xe6d570: stur            xzr, [x0, #0x27]
    // 0xe6d574: ldur            x1, [fp, #-0x10]
    // 0xe6d578: r0 = transform3()
    //     0xe6d578: bl              #0x6c20e8  ; [package:vector_math/vector_math_64.dart] Matrix4::transform3
    // 0xe6d57c: stur            x0, [fp, #-8]
    // 0xe6d580: r0 = Vector3()
    //     0xe6d580: bl              #0x68e1e4  ; AllocateVector3Stub -> Vector3 (size=0xc)
    // 0xe6d584: r4 = 6
    //     0xe6d584: movz            x4, #0x6
    // 0xe6d588: stur            x0, [fp, #-0x28]
    // 0xe6d58c: r0 = AllocateFloat64Array()
    //     0xe6d58c: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6d590: ldur            x2, [fp, #-0x28]
    // 0xe6d594: StoreField: r2->field_7 = r0
    //     0xe6d594: stur            w0, [x2, #7]
    // 0xe6d598: ldur            d0, [fp, #-0x78]
    // 0xe6d59c: ArrayStore: r0[0] = d0  ; List_8
    //     0xe6d59c: stur            d0, [x0, #0x17]
    // 0xe6d5a0: ldur            d0, [fp, #-0x70]
    // 0xe6d5a4: StoreField: r0->field_1f = d0
    //     0xe6d5a4: stur            d0, [x0, #0x1f]
    // 0xe6d5a8: StoreField: r0->field_27 = rZR
    //     0xe6d5a8: stur            xzr, [x0, #0x27]
    // 0xe6d5ac: ldur            x1, [fp, #-0x10]
    // 0xe6d5b0: r0 = transform3()
    //     0xe6d5b0: bl              #0x6c20e8  ; [package:vector_math/vector_math_64.dart] Matrix4::transform3
    // 0xe6d5b4: mov             x2, x0
    // 0xe6d5b8: ldur            x0, [fp, #-0x18]
    // 0xe6d5bc: LoadField: r3 = r0->field_7
    //     0xe6d5bc: ldur            w3, [x0, #7]
    // 0xe6d5c0: DecompressPointer r3
    //     0xe6d5c0: add             x3, x3, HEAP, lsl #32
    // 0xe6d5c4: stur            x3, [fp, #-0x50]
    // 0xe6d5c8: LoadField: r0 = r3->field_13
    //     0xe6d5c8: ldur            w0, [x3, #0x13]
    // 0xe6d5cc: r4 = LoadInt32Instr(r0)
    //     0xe6d5cc: sbfx            x4, x0, #1, #0x1f
    // 0xe6d5d0: mov             x0, x4
    // 0xe6d5d4: stur            x4, [fp, #-0x48]
    // 0xe6d5d8: r1 = 0
    //     0xe6d5d8: movz            x1, #0
    // 0xe6d5dc: cmp             x1, x0
    // 0xe6d5e0: b.hs            #0xe6d99c
    // 0xe6d5e4: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xe6d5e4: ldur            d0, [x3, #0x17]
    // 0xe6d5e8: ldur            x0, [fp, #-0x20]
    // 0xe6d5ec: LoadField: r5 = r0->field_7
    //     0xe6d5ec: ldur            w5, [x0, #7]
    // 0xe6d5f0: DecompressPointer r5
    //     0xe6d5f0: add             x5, x5, HEAP, lsl #32
    // 0xe6d5f4: stur            x5, [fp, #-0x28]
    // 0xe6d5f8: LoadField: r0 = r5->field_13
    //     0xe6d5f8: ldur            w0, [x5, #0x13]
    // 0xe6d5fc: r6 = LoadInt32Instr(r0)
    //     0xe6d5fc: sbfx            x6, x0, #1, #0x1f
    // 0xe6d600: mov             x0, x6
    // 0xe6d604: stur            x6, [fp, #-0x40]
    // 0xe6d608: r1 = 0
    //     0xe6d608: movz            x1, #0
    // 0xe6d60c: cmp             x1, x0
    // 0xe6d610: b.hs            #0xe6d9a0
    // 0xe6d614: ArrayLoad: d1 = r5[0]  ; List_8
    //     0xe6d614: ldur            d1, [x5, #0x17]
    // 0xe6d618: ldur            x0, [fp, #-8]
    // 0xe6d61c: stur            d1, [fp, #-0x70]
    // 0xe6d620: LoadField: r7 = r0->field_7
    //     0xe6d620: ldur            w7, [x0, #7]
    // 0xe6d624: DecompressPointer r7
    //     0xe6d624: add             x7, x7, HEAP, lsl #32
    // 0xe6d628: stur            x7, [fp, #-0x18]
    // 0xe6d62c: LoadField: r0 = r7->field_13
    //     0xe6d62c: ldur            w0, [x7, #0x13]
    // 0xe6d630: r8 = LoadInt32Instr(r0)
    //     0xe6d630: sbfx            x8, x0, #1, #0x1f
    // 0xe6d634: mov             x0, x8
    // 0xe6d638: stur            x8, [fp, #-0x38]
    // 0xe6d63c: r1 = 0
    //     0xe6d63c: movz            x1, #0
    // 0xe6d640: cmp             x1, x0
    // 0xe6d644: b.hs            #0xe6d9a4
    // 0xe6d648: ArrayLoad: d2 = r7[0]  ; List_8
    //     0xe6d648: ldur            d2, [x7, #0x17]
    // 0xe6d64c: stur            d2, [fp, #-0x68]
    // 0xe6d650: LoadField: r9 = r2->field_7
    //     0xe6d650: ldur            w9, [x2, #7]
    // 0xe6d654: DecompressPointer r9
    //     0xe6d654: add             x9, x9, HEAP, lsl #32
    // 0xe6d658: stur            x9, [fp, #-0x10]
    // 0xe6d65c: LoadField: r0 = r9->field_13
    //     0xe6d65c: ldur            w0, [x9, #0x13]
    // 0xe6d660: r10 = LoadInt32Instr(r0)
    //     0xe6d660: sbfx            x10, x0, #1, #0x1f
    // 0xe6d664: mov             x0, x10
    // 0xe6d668: stur            x10, [fp, #-0x30]
    // 0xe6d66c: r1 = 0
    //     0xe6d66c: movz            x1, #0
    // 0xe6d670: cmp             x1, x0
    // 0xe6d674: b.hs            #0xe6d9a8
    // 0xe6d678: ArrayLoad: d3 = r9[0]  ; List_8
    //     0xe6d678: ldur            d3, [x9, #0x17]
    // 0xe6d67c: stur            d3, [fp, #-0x60]
    // 0xe6d680: r0 = inline_Allocate_Double()
    //     0xe6d680: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6d684: add             x0, x0, #0x10
    //     0xe6d688: cmp             x1, x0
    //     0xe6d68c: b.ls            #0xe6d9ac
    //     0xe6d690: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6d694: sub             x0, x0, #0xf
    //     0xe6d698: movz            x1, #0xe15c
    //     0xe6d69c: movk            x1, #0x3, lsl #16
    //     0xe6d6a0: stur            x1, [x0, #-1]
    // 0xe6d6a4: StoreField: r0->field_7 = d0
    //     0xe6d6a4: stur            d0, [x0, #7]
    // 0xe6d6a8: stur            x0, [fp, #-8]
    // 0xe6d6ac: r1 = Null
    //     0xe6d6ac: mov             x1, NULL
    // 0xe6d6b0: r2 = 8
    //     0xe6d6b0: movz            x2, #0x8
    // 0xe6d6b4: r0 = AllocateArray()
    //     0xe6d6b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6d6b8: mov             x2, x0
    // 0xe6d6bc: ldur            x0, [fp, #-8]
    // 0xe6d6c0: stur            x2, [fp, #-0x20]
    // 0xe6d6c4: StoreField: r2->field_f = r0
    //     0xe6d6c4: stur            w0, [x2, #0xf]
    // 0xe6d6c8: ldur            d0, [fp, #-0x70]
    // 0xe6d6cc: r0 = inline_Allocate_Double()
    //     0xe6d6cc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6d6d0: add             x0, x0, #0x10
    //     0xe6d6d4: cmp             x1, x0
    //     0xe6d6d8: b.ls            #0xe6d9e4
    //     0xe6d6dc: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6d6e0: sub             x0, x0, #0xf
    //     0xe6d6e4: movz            x1, #0xe15c
    //     0xe6d6e8: movk            x1, #0x3, lsl #16
    //     0xe6d6ec: stur            x1, [x0, #-1]
    // 0xe6d6f0: StoreField: r0->field_7 = d0
    //     0xe6d6f0: stur            d0, [x0, #7]
    // 0xe6d6f4: StoreField: r2->field_13 = r0
    //     0xe6d6f4: stur            w0, [x2, #0x13]
    // 0xe6d6f8: ldur            d0, [fp, #-0x68]
    // 0xe6d6fc: r0 = inline_Allocate_Double()
    //     0xe6d6fc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6d700: add             x0, x0, #0x10
    //     0xe6d704: cmp             x1, x0
    //     0xe6d708: b.ls            #0xe6d9fc
    //     0xe6d70c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6d710: sub             x0, x0, #0xf
    //     0xe6d714: movz            x1, #0xe15c
    //     0xe6d718: movk            x1, #0x3, lsl #16
    //     0xe6d71c: stur            x1, [x0, #-1]
    // 0xe6d720: StoreField: r0->field_7 = d0
    //     0xe6d720: stur            d0, [x0, #7]
    // 0xe6d724: ArrayStore: r2[0] = r0  ; List_4
    //     0xe6d724: stur            w0, [x2, #0x17]
    // 0xe6d728: ldur            d0, [fp, #-0x60]
    // 0xe6d72c: r0 = inline_Allocate_Double()
    //     0xe6d72c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6d730: add             x0, x0, #0x10
    //     0xe6d734: cmp             x1, x0
    //     0xe6d738: b.ls            #0xe6da14
    //     0xe6d73c: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6d740: sub             x0, x0, #0xf
    //     0xe6d744: movz            x1, #0xe15c
    //     0xe6d748: movk            x1, #0x3, lsl #16
    //     0xe6d74c: stur            x1, [x0, #-1]
    // 0xe6d750: StoreField: r0->field_7 = d0
    //     0xe6d750: stur            d0, [x0, #7]
    // 0xe6d754: StoreField: r2->field_1b = r0
    //     0xe6d754: stur            w0, [x2, #0x1b]
    // 0xe6d758: r1 = <double>
    //     0xe6d758: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe6d75c: r0 = AllocateGrowableArray()
    //     0xe6d75c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe6d760: mov             x3, x0
    // 0xe6d764: ldur            x0, [fp, #-0x20]
    // 0xe6d768: stur            x3, [fp, #-0x58]
    // 0xe6d76c: StoreField: r3->field_f = r0
    //     0xe6d76c: stur            w0, [x3, #0xf]
    // 0xe6d770: r4 = 8
    //     0xe6d770: movz            x4, #0x8
    // 0xe6d774: StoreField: r3->field_b = r4
    //     0xe6d774: stur            w4, [x3, #0xb]
    // 0xe6d778: ldur            x0, [fp, #-0x48]
    // 0xe6d77c: r1 = 1
    //     0xe6d77c: movz            x1, #0x1
    // 0xe6d780: cmp             x1, x0
    // 0xe6d784: b.hs            #0xe6da2c
    // 0xe6d788: ldur            x0, [fp, #-0x50]
    // 0xe6d78c: LoadField: d0 = r0->field_1f
    //     0xe6d78c: ldur            d0, [x0, #0x1f]
    // 0xe6d790: ldur            x0, [fp, #-0x40]
    // 0xe6d794: r1 = 1
    //     0xe6d794: movz            x1, #0x1
    // 0xe6d798: cmp             x1, x0
    // 0xe6d79c: b.hs            #0xe6da30
    // 0xe6d7a0: ldur            x0, [fp, #-0x28]
    // 0xe6d7a4: LoadField: d1 = r0->field_1f
    //     0xe6d7a4: ldur            d1, [x0, #0x1f]
    // 0xe6d7a8: ldur            x0, [fp, #-0x38]
    // 0xe6d7ac: stur            d1, [fp, #-0x70]
    // 0xe6d7b0: r1 = 1
    //     0xe6d7b0: movz            x1, #0x1
    // 0xe6d7b4: cmp             x1, x0
    // 0xe6d7b8: b.hs            #0xe6da34
    // 0xe6d7bc: ldur            x0, [fp, #-0x18]
    // 0xe6d7c0: LoadField: d2 = r0->field_1f
    //     0xe6d7c0: ldur            d2, [x0, #0x1f]
    // 0xe6d7c4: ldur            x0, [fp, #-0x30]
    // 0xe6d7c8: stur            d2, [fp, #-0x68]
    // 0xe6d7cc: r1 = 1
    //     0xe6d7cc: movz            x1, #0x1
    // 0xe6d7d0: cmp             x1, x0
    // 0xe6d7d4: b.hs            #0xe6da38
    // 0xe6d7d8: ldur            x0, [fp, #-0x10]
    // 0xe6d7dc: LoadField: d3 = r0->field_1f
    //     0xe6d7dc: ldur            d3, [x0, #0x1f]
    // 0xe6d7e0: stur            d3, [fp, #-0x60]
    // 0xe6d7e4: r0 = inline_Allocate_Double()
    //     0xe6d7e4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6d7e8: add             x0, x0, #0x10
    //     0xe6d7ec: cmp             x1, x0
    //     0xe6d7f0: b.ls            #0xe6da3c
    //     0xe6d7f4: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6d7f8: sub             x0, x0, #0xf
    //     0xe6d7fc: movz            x1, #0xe15c
    //     0xe6d800: movk            x1, #0x3, lsl #16
    //     0xe6d804: stur            x1, [x0, #-1]
    // 0xe6d808: StoreField: r0->field_7 = d0
    //     0xe6d808: stur            d0, [x0, #7]
    // 0xe6d80c: mov             x2, x4
    // 0xe6d810: stur            x0, [fp, #-8]
    // 0xe6d814: r1 = Null
    //     0xe6d814: mov             x1, NULL
    // 0xe6d818: r0 = AllocateArray()
    //     0xe6d818: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe6d81c: mov             x2, x0
    // 0xe6d820: ldur            x0, [fp, #-8]
    // 0xe6d824: stur            x2, [fp, #-0x10]
    // 0xe6d828: StoreField: r2->field_f = r0
    //     0xe6d828: stur            w0, [x2, #0xf]
    // 0xe6d82c: ldur            d0, [fp, #-0x70]
    // 0xe6d830: r0 = inline_Allocate_Double()
    //     0xe6d830: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6d834: add             x0, x0, #0x10
    //     0xe6d838: cmp             x1, x0
    //     0xe6d83c: b.ls            #0xe6da5c
    //     0xe6d840: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6d844: sub             x0, x0, #0xf
    //     0xe6d848: movz            x1, #0xe15c
    //     0xe6d84c: movk            x1, #0x3, lsl #16
    //     0xe6d850: stur            x1, [x0, #-1]
    // 0xe6d854: StoreField: r0->field_7 = d0
    //     0xe6d854: stur            d0, [x0, #7]
    // 0xe6d858: StoreField: r2->field_13 = r0
    //     0xe6d858: stur            w0, [x2, #0x13]
    // 0xe6d85c: ldur            d0, [fp, #-0x68]
    // 0xe6d860: r0 = inline_Allocate_Double()
    //     0xe6d860: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6d864: add             x0, x0, #0x10
    //     0xe6d868: cmp             x1, x0
    //     0xe6d86c: b.ls            #0xe6da74
    //     0xe6d870: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6d874: sub             x0, x0, #0xf
    //     0xe6d878: movz            x1, #0xe15c
    //     0xe6d87c: movk            x1, #0x3, lsl #16
    //     0xe6d880: stur            x1, [x0, #-1]
    // 0xe6d884: StoreField: r0->field_7 = d0
    //     0xe6d884: stur            d0, [x0, #7]
    // 0xe6d888: ArrayStore: r2[0] = r0  ; List_4
    //     0xe6d888: stur            w0, [x2, #0x17]
    // 0xe6d88c: ldur            d0, [fp, #-0x60]
    // 0xe6d890: r0 = inline_Allocate_Double()
    //     0xe6d890: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xe6d894: add             x0, x0, #0x10
    //     0xe6d898: cmp             x1, x0
    //     0xe6d89c: b.ls            #0xe6da8c
    //     0xe6d8a0: str             x0, [THR, #0x50]  ; THR::top
    //     0xe6d8a4: sub             x0, x0, #0xf
    //     0xe6d8a8: movz            x1, #0xe15c
    //     0xe6d8ac: movk            x1, #0x3, lsl #16
    //     0xe6d8b0: stur            x1, [x0, #-1]
    // 0xe6d8b4: StoreField: r0->field_7 = d0
    //     0xe6d8b4: stur            d0, [x0, #7]
    // 0xe6d8b8: StoreField: r2->field_1b = r0
    //     0xe6d8b8: stur            w0, [x2, #0x1b]
    // 0xe6d8bc: r1 = <double>
    //     0xe6d8bc: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xe6d8c0: r0 = AllocateGrowableArray()
    //     0xe6d8c0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe6d8c4: mov             x3, x0
    // 0xe6d8c8: ldur            x0, [fp, #-0x10]
    // 0xe6d8cc: stur            x3, [fp, #-8]
    // 0xe6d8d0: StoreField: r3->field_f = r0
    //     0xe6d8d0: stur            w0, [x3, #0xf]
    // 0xe6d8d4: r0 = 8
    //     0xe6d8d4: movz            x0, #0x8
    // 0xe6d8d8: StoreField: r3->field_b = r0
    //     0xe6d8d8: stur            w0, [x3, #0xb]
    // 0xe6d8dc: ldur            x1, [fp, #-0x58]
    // 0xe6d8e0: r2 = Closure: (double, double) => double from Function 'min': static.
    //     0xe6d8e0: add             x2, PP, #0x47, lsl #12  ; [pp+0x47508] Closure: (double, double) => double from Function 'min': static. (0x7e54fb03482c)
    //     0xe6d8e4: ldr             x2, [x2, #0x508]
    // 0xe6d8e8: r0 = reduce()
    //     0xe6d8e8: bl              #0x8a5ec4  ; [dart:collection] ListBase::reduce
    // 0xe6d8ec: ldur            x1, [fp, #-8]
    // 0xe6d8f0: r2 = Closure: (double, double) => double from Function 'min': static.
    //     0xe6d8f0: add             x2, PP, #0x47, lsl #12  ; [pp+0x47508] Closure: (double, double) => double from Function 'min': static. (0x7e54fb03482c)
    //     0xe6d8f4: ldr             x2, [x2, #0x508]
    // 0xe6d8f8: stur            x0, [fp, #-0x10]
    // 0xe6d8fc: r0 = reduce()
    //     0xe6d8fc: bl              #0x8a5ec4  ; [dart:collection] ListBase::reduce
    // 0xe6d900: ldur            x1, [fp, #-0x58]
    // 0xe6d904: r2 = Closure: (double, double) => double from Function 'max': static.
    //     0xe6d904: add             x2, PP, #0x47, lsl #12  ; [pp+0x47510] Closure: (double, double) => double from Function 'max': static. (0x7e54fb034a64)
    //     0xe6d908: ldr             x2, [x2, #0x510]
    // 0xe6d90c: stur            x0, [fp, #-0x18]
    // 0xe6d910: r0 = reduce()
    //     0xe6d910: bl              #0x8a5ec4  ; [dart:collection] ListBase::reduce
    // 0xe6d914: ldur            x1, [fp, #-8]
    // 0xe6d918: r2 = Closure: (double, double) => double from Function 'max': static.
    //     0xe6d918: add             x2, PP, #0x47, lsl #12  ; [pp+0x47510] Closure: (double, double) => double from Function 'max': static. (0x7e54fb034a64)
    //     0xe6d91c: ldr             x2, [x2, #0x510]
    // 0xe6d920: stur            x0, [fp, #-8]
    // 0xe6d924: r0 = reduce()
    //     0xe6d924: bl              #0x8a5ec4  ; [dart:collection] ListBase::reduce
    // 0xe6d928: mov             x1, x0
    // 0xe6d92c: ldur            x0, [fp, #-0x10]
    // 0xe6d930: LoadField: d0 = r0->field_7
    //     0xe6d930: ldur            d0, [x0, #7]
    // 0xe6d934: ldur            x0, [fp, #-8]
    // 0xe6d938: stur            d0, [fp, #-0x78]
    // 0xe6d93c: LoadField: d1 = r0->field_7
    //     0xe6d93c: ldur            d1, [x0, #7]
    // 0xe6d940: fsub            d2, d1, d0
    // 0xe6d944: ldur            x0, [fp, #-0x18]
    // 0xe6d948: stur            d2, [fp, #-0x70]
    // 0xe6d94c: LoadField: d1 = r0->field_7
    //     0xe6d94c: ldur            d1, [x0, #7]
    // 0xe6d950: stur            d1, [fp, #-0x68]
    // 0xe6d954: LoadField: d3 = r1->field_7
    //     0xe6d954: ldur            d3, [x1, #7]
    // 0xe6d958: fsub            d4, d3, d1
    // 0xe6d95c: stur            d4, [fp, #-0x60]
    // 0xe6d960: r0 = PdfRect()
    //     0xe6d960: bl              #0xb1432c  ; AllocatePdfRectStub -> PdfRect (size=0x28)
    // 0xe6d964: ldur            d0, [fp, #-0x78]
    // 0xe6d968: StoreField: r0->field_7 = d0
    //     0xe6d968: stur            d0, [x0, #7]
    // 0xe6d96c: ldur            d0, [fp, #-0x68]
    // 0xe6d970: StoreField: r0->field_f = d0
    //     0xe6d970: stur            d0, [x0, #0xf]
    // 0xe6d974: ldur            d0, [fp, #-0x70]
    // 0xe6d978: ArrayStore: r0[0] = d0  ; List_8
    //     0xe6d978: stur            d0, [x0, #0x17]
    // 0xe6d97c: ldur            d0, [fp, #-0x60]
    // 0xe6d980: StoreField: r0->field_1f = d0
    //     0xe6d980: stur            d0, [x0, #0x1f]
    // 0xe6d984: LeaveFrame
    //     0xe6d984: mov             SP, fp
    //     0xe6d988: ldp             fp, lr, [SP], #0x10
    // 0xe6d98c: ret
    //     0xe6d98c: ret             
    // 0xe6d990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6d990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6d994: b               #0xe6d474
    // 0xe6d998: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6d998: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe6d99c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6d99c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6d9a0: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6d9a0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe6d9a4: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6d9a4: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe6d9a8: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6d9a8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe6d9ac: stp             q2, q3, [SP, #-0x20]!
    // 0xe6d9b0: stp             q0, q1, [SP, #-0x20]!
    // 0xe6d9b4: stp             x9, x10, [SP, #-0x10]!
    // 0xe6d9b8: stp             x7, x8, [SP, #-0x10]!
    // 0xe6d9bc: stp             x5, x6, [SP, #-0x10]!
    // 0xe6d9c0: stp             x3, x4, [SP, #-0x10]!
    // 0xe6d9c4: r0 = AllocateDouble()
    //     0xe6d9c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6d9c8: ldp             x3, x4, [SP], #0x10
    // 0xe6d9cc: ldp             x5, x6, [SP], #0x10
    // 0xe6d9d0: ldp             x7, x8, [SP], #0x10
    // 0xe6d9d4: ldp             x9, x10, [SP], #0x10
    // 0xe6d9d8: ldp             q0, q1, [SP], #0x20
    // 0xe6d9dc: ldp             q2, q3, [SP], #0x20
    // 0xe6d9e0: b               #0xe6d6a4
    // 0xe6d9e4: SaveReg d0
    //     0xe6d9e4: str             q0, [SP, #-0x10]!
    // 0xe6d9e8: SaveReg r2
    //     0xe6d9e8: str             x2, [SP, #-8]!
    // 0xe6d9ec: r0 = AllocateDouble()
    //     0xe6d9ec: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6d9f0: RestoreReg r2
    //     0xe6d9f0: ldr             x2, [SP], #8
    // 0xe6d9f4: RestoreReg d0
    //     0xe6d9f4: ldr             q0, [SP], #0x10
    // 0xe6d9f8: b               #0xe6d6f0
    // 0xe6d9fc: SaveReg d0
    //     0xe6d9fc: str             q0, [SP, #-0x10]!
    // 0xe6da00: SaveReg r2
    //     0xe6da00: str             x2, [SP, #-8]!
    // 0xe6da04: r0 = AllocateDouble()
    //     0xe6da04: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6da08: RestoreReg r2
    //     0xe6da08: ldr             x2, [SP], #8
    // 0xe6da0c: RestoreReg d0
    //     0xe6da0c: ldr             q0, [SP], #0x10
    // 0xe6da10: b               #0xe6d720
    // 0xe6da14: SaveReg d0
    //     0xe6da14: str             q0, [SP, #-0x10]!
    // 0xe6da18: SaveReg r2
    //     0xe6da18: str             x2, [SP, #-8]!
    // 0xe6da1c: r0 = AllocateDouble()
    //     0xe6da1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6da20: RestoreReg r2
    //     0xe6da20: ldr             x2, [SP], #8
    // 0xe6da24: RestoreReg d0
    //     0xe6da24: ldr             q0, [SP], #0x10
    // 0xe6da28: b               #0xe6d750
    // 0xe6da2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe6da2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe6da30: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6da30: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe6da34: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6da34: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe6da38: r0 = RangeErrorSharedWithFPURegs()
    //     0xe6da38: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xe6da3c: stp             q2, q3, [SP, #-0x20]!
    // 0xe6da40: stp             q0, q1, [SP, #-0x20]!
    // 0xe6da44: stp             x3, x4, [SP, #-0x10]!
    // 0xe6da48: r0 = AllocateDouble()
    //     0xe6da48: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6da4c: ldp             x3, x4, [SP], #0x10
    // 0xe6da50: ldp             q0, q1, [SP], #0x20
    // 0xe6da54: ldp             q2, q3, [SP], #0x20
    // 0xe6da58: b               #0xe6d808
    // 0xe6da5c: SaveReg d0
    //     0xe6da5c: str             q0, [SP, #-0x10]!
    // 0xe6da60: SaveReg r2
    //     0xe6da60: str             x2, [SP, #-8]!
    // 0xe6da64: r0 = AllocateDouble()
    //     0xe6da64: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6da68: RestoreReg r2
    //     0xe6da68: ldr             x2, [SP], #8
    // 0xe6da6c: RestoreReg d0
    //     0xe6da6c: ldr             q0, [SP], #0x10
    // 0xe6da70: b               #0xe6d854
    // 0xe6da74: SaveReg d0
    //     0xe6da74: str             q0, [SP, #-0x10]!
    // 0xe6da78: SaveReg r2
    //     0xe6da78: str             x2, [SP, #-8]!
    // 0xe6da7c: r0 = AllocateDouble()
    //     0xe6da7c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6da80: RestoreReg r2
    //     0xe6da80: ldr             x2, [SP], #8
    // 0xe6da84: RestoreReg d0
    //     0xe6da84: ldr             q0, [SP], #0x10
    // 0xe6da88: b               #0xe6d884
    // 0xe6da8c: SaveReg d0
    //     0xe6da8c: str             q0, [SP, #-0x10]!
    // 0xe6da90: SaveReg r2
    //     0xe6da90: str             x2, [SP, #-8]!
    // 0xe6da94: r0 = AllocateDouble()
    //     0xe6da94: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe6da98: RestoreReg r2
    //     0xe6da98: ldr             x2, [SP], #8
    // 0xe6da9c: RestoreReg d0
    //     0xe6da9c: ldr             q0, [SP], #0x10
    // 0xe6daa0: b               #0xe6d8b4
  }
  factory _ Context(/* No info */) {
    // ** addr: 0xe7097c, size: 0x64
    // 0xe7097c: EnterFrame
    //     0xe7097c: stp             fp, lr, [SP, #-0x10]!
    //     0xe70980: mov             fp, SP
    // 0xe70984: AllocStack(0x10)
    //     0xe70984: sub             SP, SP, #0x10
    // 0xe70988: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe70988: mov             x0, x1
    //     0xe7098c: stur            x2, [fp, #-8]
    // 0xe70990: r1 = <Type, Inherited>
    //     0xe70990: add             x1, PP, #0x36, lsl #12  ; [pp+0x36888] TypeArguments: <Type, Inherited>
    //     0xe70994: ldr             x1, [x1, #0x888]
    // 0xe70998: r0 = _HashMap()
    //     0xe70998: bl              #0x63a3f8  ; Allocate_HashMapStub -> _HashMap<X0, X1> (size=0x20)
    // 0xe7099c: stur            x0, [fp, #-0x10]
    // 0xe709a0: StoreField: r0->field_b = rZR
    //     0xe709a0: stur            xzr, [x0, #0xb]
    // 0xe709a4: ArrayStore: r0[0] = rZR  ; List_8
    //     0xe709a4: stur            xzr, [x0, #0x17]
    // 0xe709a8: r1 = <_HashMapEntry?>
    //     0xe709a8: ldr             x1, [PP, #0x1a10]  ; [pp+0x1a10] TypeArguments: <_HashMapEntry?>
    // 0xe709ac: r2 = 16
    //     0xe709ac: movz            x2, #0x10
    // 0xe709b0: r0 = AllocateArray()
    //     0xe709b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe709b4: mov             x1, x0
    // 0xe709b8: ldur            x0, [fp, #-0x10]
    // 0xe709bc: StoreField: r0->field_13 = r1
    //     0xe709bc: stur            w1, [x0, #0x13]
    // 0xe709c0: r0 = Context()
    //     0xe709c0: bl              #0xe709e0  ; AllocateContextStub -> Context (size=0x18)
    // 0xe709c4: ldur            x1, [fp, #-8]
    // 0xe709c8: StoreField: r0->field_13 = r1
    //     0xe709c8: stur            w1, [x0, #0x13]
    // 0xe709cc: ldur            x1, [fp, #-0x10]
    // 0xe709d0: StoreField: r0->field_f = r1
    //     0xe709d0: stur            w1, [x0, #0xf]
    // 0xe709d4: LeaveFrame
    //     0xe709d4: mov             SP, fp
    //     0xe709d8: ldp             fp, lr, [SP], #0x10
    // 0xe709dc: ret
    //     0xe709dc: ret             
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xe8a8bc, size: 0x17c
    // 0xe8a8bc: EnterFrame
    //     0xe8a8bc: stp             fp, lr, [SP, #-0x10]!
    //     0xe8a8c0: mov             fp, SP
    // 0xe8a8c4: AllocStack(0x20)
    //     0xe8a8c4: sub             SP, SP, #0x20
    // 0xe8a8c8: SetupParameters({dynamic canvas = Null /* r3 */, dynamic inherited = Null /* r5 */, dynamic page = Null /* r0 */})
    //     0xe8a8c8: ldur            w0, [x4, #0x13]
    //     0xe8a8cc: ldur            w2, [x4, #0x1f]
    //     0xe8a8d0: add             x2, x2, HEAP, lsl #32
    //     0xe8a8d4: add             x16, PP, #0x36, lsl #12  ; [pp+0x368a8] "canvas"
    //     0xe8a8d8: ldr             x16, [x16, #0x8a8]
    //     0xe8a8dc: cmp             w2, w16
    //     0xe8a8e0: b.ne            #0xe8a904
    //     0xe8a8e4: ldur            w2, [x4, #0x23]
    //     0xe8a8e8: add             x2, x2, HEAP, lsl #32
    //     0xe8a8ec: sub             w3, w0, w2
    //     0xe8a8f0: add             x2, fp, w3, sxtw #2
    //     0xe8a8f4: ldr             x2, [x2, #8]
    //     0xe8a8f8: mov             x3, x2
    //     0xe8a8fc: movz            x2, #0x1
    //     0xe8a900: b               #0xe8a90c
    //     0xe8a904: mov             x3, NULL
    //     0xe8a908: movz            x2, #0
    //     0xe8a90c: lsl             x5, x2, #1
    //     0xe8a910: lsl             w6, w5, #1
    //     0xe8a914: add             w7, w6, #8
    //     0xe8a918: add             x16, x4, w7, sxtw #1
    //     0xe8a91c: ldur            w8, [x16, #0xf]
    //     0xe8a920: add             x8, x8, HEAP, lsl #32
    //     0xe8a924: add             x16, PP, #0x36, lsl #12  ; [pp+0x368b0] "inherited"
    //     0xe8a928: ldr             x16, [x16, #0x8b0]
    //     0xe8a92c: cmp             w8, w16
    //     0xe8a930: b.ne            #0xe8a964
    //     0xe8a934: add             w2, w6, #0xa
    //     0xe8a938: add             x16, x4, w2, sxtw #1
    //     0xe8a93c: ldur            w6, [x16, #0xf]
    //     0xe8a940: add             x6, x6, HEAP, lsl #32
    //     0xe8a944: sub             w2, w0, w6
    //     0xe8a948: add             x6, fp, w2, sxtw #2
    //     0xe8a94c: ldr             x6, [x6, #8]
    //     0xe8a950: add             w2, w5, #2
    //     0xe8a954: sbfx            x5, x2, #1, #0x1f
    //     0xe8a958: mov             x2, x5
    //     0xe8a95c: mov             x5, x6
    //     0xe8a960: b               #0xe8a968
    //     0xe8a964: mov             x5, NULL
    //     0xe8a968: lsl             x6, x2, #1
    //     0xe8a96c: lsl             w2, w6, #1
    //     0xe8a970: add             w6, w2, #8
    //     0xe8a974: add             x16, x4, w6, sxtw #1
    //     0xe8a978: ldur            w7, [x16, #0xf]
    //     0xe8a97c: add             x7, x7, HEAP, lsl #32
    //     0xe8a980: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0xe8a984: ldr             x16, [x16, #0x300]
    //     0xe8a988: cmp             w7, w16
    //     0xe8a98c: b.ne            #0xe8a9b0
    //     0xe8a990: add             w6, w2, #0xa
    //     0xe8a994: add             x16, x4, w6, sxtw #1
    //     0xe8a998: ldur            w2, [x16, #0xf]
    //     0xe8a99c: add             x2, x2, HEAP, lsl #32
    //     0xe8a9a0: sub             w4, w0, w2
    //     0xe8a9a4: add             x0, fp, w4, sxtw #2
    //     0xe8a9a8: ldr             x0, [x0, #8]
    //     0xe8a9ac: b               #0xe8a9b4
    //     0xe8a9b0: mov             x0, NULL
    // 0xe8a9b4: LoadField: r2 = r1->field_13
    //     0xe8a9b4: ldur            w2, [x1, #0x13]
    // 0xe8a9b8: DecompressPointer r2
    //     0xe8a9b8: add             x2, x2, HEAP, lsl #32
    // 0xe8a9bc: stur            x2, [fp, #-0x20]
    // 0xe8a9c0: cmp             w0, NULL
    // 0xe8a9c4: b.ne            #0xe8a9d0
    // 0xe8a9c8: LoadField: r0 = r1->field_7
    //     0xe8a9c8: ldur            w0, [x1, #7]
    // 0xe8a9cc: DecompressPointer r0
    //     0xe8a9cc: add             x0, x0, HEAP, lsl #32
    // 0xe8a9d0: stur            x0, [fp, #-0x18]
    // 0xe8a9d4: cmp             w3, NULL
    // 0xe8a9d8: b.ne            #0xe8a9e4
    // 0xe8a9dc: LoadField: r3 = r1->field_b
    //     0xe8a9dc: ldur            w3, [x1, #0xb]
    // 0xe8a9e0: DecompressPointer r3
    //     0xe8a9e0: add             x3, x3, HEAP, lsl #32
    // 0xe8a9e4: stur            x3, [fp, #-0x10]
    // 0xe8a9e8: cmp             w5, NULL
    // 0xe8a9ec: b.ne            #0xe8aa00
    // 0xe8a9f0: LoadField: r4 = r1->field_f
    //     0xe8a9f0: ldur            w4, [x1, #0xf]
    // 0xe8a9f4: DecompressPointer r4
    //     0xe8a9f4: add             x4, x4, HEAP, lsl #32
    // 0xe8a9f8: mov             x1, x4
    // 0xe8a9fc: b               #0xe8aa04
    // 0xe8aa00: mov             x1, x5
    // 0xe8aa04: stur            x1, [fp, #-8]
    // 0xe8aa08: r0 = Context()
    //     0xe8aa08: bl              #0xe709e0  ; AllocateContextStub -> Context (size=0x18)
    // 0xe8aa0c: ldur            x1, [fp, #-0x20]
    // 0xe8aa10: StoreField: r0->field_13 = r1
    //     0xe8aa10: stur            w1, [x0, #0x13]
    // 0xe8aa14: ldur            x1, [fp, #-0x18]
    // 0xe8aa18: StoreField: r0->field_7 = r1
    //     0xe8aa18: stur            w1, [x0, #7]
    // 0xe8aa1c: ldur            x1, [fp, #-0x10]
    // 0xe8aa20: StoreField: r0->field_b = r1
    //     0xe8aa20: stur            w1, [x0, #0xb]
    // 0xe8aa24: ldur            x1, [fp, #-8]
    // 0xe8aa28: StoreField: r0->field_f = r1
    //     0xe8aa28: stur            w1, [x0, #0xf]
    // 0xe8aa2c: LeaveFrame
    //     0xe8aa2c: mov             SP, fp
    //     0xe8aa30: ldp             fp, lr, [SP], #0x10
    // 0xe8aa34: ret
    //     0xe8aa34: ret             
  }
  _ inheritFromAll(/* No info */) {
    // ** addr: 0xe8ae3c, size: 0x168
    // 0xe8ae3c: EnterFrame
    //     0xe8ae3c: stp             fp, lr, [SP, #-0x10]!
    //     0xe8ae40: mov             fp, SP
    // 0xe8ae44: AllocStack(0x40)
    //     0xe8ae44: sub             SP, SP, #0x40
    // 0xe8ae48: SetupParameters(Context this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe8ae48: mov             x3, x1
    //     0xe8ae4c: mov             x0, x2
    //     0xe8ae50: stur            x1, [fp, #-8]
    //     0xe8ae54: stur            x2, [fp, #-0x10]
    // 0xe8ae58: CheckStackOverflow
    //     0xe8ae58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ae5c: cmp             SP, x16
    //     0xe8ae60: b.ls            #0xe8af94
    // 0xe8ae64: LoadField: r2 = r3->field_f
    //     0xe8ae64: ldur            w2, [x3, #0xf]
    // 0xe8ae68: DecompressPointer r2
    //     0xe8ae68: add             x2, x2, HEAP, lsl #32
    // 0xe8ae6c: r1 = <Type, Inherited>
    //     0xe8ae6c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36888] TypeArguments: <Type, Inherited>
    //     0xe8ae70: ldr             x1, [x1, #0x888]
    // 0xe8ae74: r0 = HashMap.of()
    //     0xe8ae74: bl              #0xe8afa4  ; [dart:collection] HashMap::HashMap.of
    // 0xe8ae78: mov             x4, x0
    // 0xe8ae7c: ldur            x3, [fp, #-0x10]
    // 0xe8ae80: stur            x4, [fp, #-0x38]
    // 0xe8ae84: LoadField: r5 = r3->field_7
    //     0xe8ae84: ldur            w5, [x3, #7]
    // 0xe8ae88: DecompressPointer r5
    //     0xe8ae88: add             x5, x5, HEAP, lsl #32
    // 0xe8ae8c: stur            x5, [fp, #-0x30]
    // 0xe8ae90: LoadField: r0 = r3->field_b
    //     0xe8ae90: ldur            w0, [x3, #0xb]
    // 0xe8ae94: r6 = LoadInt32Instr(r0)
    //     0xe8ae94: sbfx            x6, x0, #1, #0x1f
    // 0xe8ae98: stur            x6, [fp, #-0x28]
    // 0xe8ae9c: r0 = 0
    //     0xe8ae9c: movz            x0, #0
    // 0xe8aea0: CheckStackOverflow
    //     0xe8aea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8aea4: cmp             SP, x16
    //     0xe8aea8: b.ls            #0xe8af9c
    // 0xe8aeac: LoadField: r1 = r3->field_b
    //     0xe8aeac: ldur            w1, [x3, #0xb]
    // 0xe8aeb0: r2 = LoadInt32Instr(r1)
    //     0xe8aeb0: sbfx            x2, x1, #1, #0x1f
    // 0xe8aeb4: cmp             x6, x2
    // 0xe8aeb8: b.ne            #0xe8af74
    // 0xe8aebc: cmp             x0, x2
    // 0xe8aec0: b.ge            #0xe8af50
    // 0xe8aec4: LoadField: r1 = r3->field_f
    //     0xe8aec4: ldur            w1, [x3, #0xf]
    // 0xe8aec8: DecompressPointer r1
    //     0xe8aec8: add             x1, x1, HEAP, lsl #32
    // 0xe8aecc: ArrayLoad: r7 = r1[r0]  ; Unknown_4
    //     0xe8aecc: add             x16, x1, x0, lsl #2
    //     0xe8aed0: ldur            w7, [x16, #0xf]
    // 0xe8aed4: DecompressPointer r7
    //     0xe8aed4: add             x7, x7, HEAP, lsl #32
    // 0xe8aed8: stur            x7, [fp, #-0x20]
    // 0xe8aedc: add             x8, x0, #1
    // 0xe8aee0: stur            x8, [fp, #-0x18]
    // 0xe8aee4: cmp             w7, NULL
    // 0xe8aee8: b.ne            #0xe8af1c
    // 0xe8aeec: mov             x0, x7
    // 0xe8aef0: mov             x2, x5
    // 0xe8aef4: r1 = Null
    //     0xe8aef4: mov             x1, NULL
    // 0xe8aef8: cmp             w2, NULL
    // 0xe8aefc: b.eq            #0xe8af1c
    // 0xe8af00: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe8af00: ldur            w4, [x2, #0x17]
    // 0xe8af04: DecompressPointer r4
    //     0xe8af04: add             x4, x4, HEAP, lsl #32
    // 0xe8af08: r8 = X0
    //     0xe8af08: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe8af0c: LoadField: r9 = r4->field_7
    //     0xe8af0c: ldur            x9, [x4, #7]
    // 0xe8af10: r3 = Null
    //     0xe8af10: add             x3, PP, #0x36, lsl #12  ; [pp+0x36890] Null
    //     0xe8af14: ldr             x3, [x3, #0x890]
    // 0xe8af18: blr             x9
    // 0xe8af1c: ldur            x16, [fp, #-0x20]
    // 0xe8af20: str             x16, [SP]
    // 0xe8af24: r0 = runtimeType()
    //     0xe8af24: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xe8af28: ldur            x1, [fp, #-0x38]
    // 0xe8af2c: mov             x2, x0
    // 0xe8af30: ldur            x3, [fp, #-0x20]
    // 0xe8af34: r0 = []=()
    //     0xe8af34: bl              #0xd36648  ; [dart:collection] _HashMap::[]=
    // 0xe8af38: ldur            x0, [fp, #-0x18]
    // 0xe8af3c: ldur            x3, [fp, #-0x10]
    // 0xe8af40: ldur            x4, [fp, #-0x38]
    // 0xe8af44: ldur            x5, [fp, #-0x30]
    // 0xe8af48: ldur            x6, [fp, #-0x28]
    // 0xe8af4c: b               #0xe8aea0
    // 0xe8af50: ldur            x16, [fp, #-0x38]
    // 0xe8af54: str             x16, [SP]
    // 0xe8af58: ldur            x1, [fp, #-8]
    // 0xe8af5c: r4 = const [0, 0x2, 0x1, 0x1, inherited, 0x1, null]
    //     0xe8af5c: add             x4, PP, #0x36, lsl #12  ; [pp+0x368a0] List(7) [0, 0x2, 0x1, 0x1, "inherited", 0x1, Null]
    //     0xe8af60: ldr             x4, [x4, #0x8a0]
    // 0xe8af64: r0 = copyWith()
    //     0xe8af64: bl              #0xe8a8bc  ; [package:pdf/src/widgets/widget.dart] Context::copyWith
    // 0xe8af68: LeaveFrame
    //     0xe8af68: mov             SP, fp
    //     0xe8af6c: ldp             fp, lr, [SP], #0x10
    // 0xe8af70: ret
    //     0xe8af70: ret             
    // 0xe8af74: mov             x0, x3
    // 0xe8af78: r0 = ConcurrentModificationError()
    //     0xe8af78: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe8af7c: mov             x1, x0
    // 0xe8af80: ldur            x0, [fp, #-0x10]
    // 0xe8af84: StoreField: r1->field_b = r0
    //     0xe8af84: stur            w0, [x1, #0xb]
    // 0xe8af88: mov             x0, x1
    // 0xe8af8c: r0 = Throw()
    //     0xe8af8c: bl              #0xec04b8  ; ThrowStub
    // 0xe8af90: brk             #0
    // 0xe8af94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8af94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8af98: b               #0xe8ae64
    // 0xe8af9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8af9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8afa0: b               #0xe8aeac
  }
}

// class id: 762, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Inherited extends Object {
}

// class id: 787, size: 0xc, field offset: 0x8
abstract class Widget extends Object {
}

// class id: 791, size: 0x10, field offset: 0xc
abstract class MultiChildWidget extends Widget {
}

// class id: 796, size: 0xc, field offset: 0xc
//   transformed mixin,
abstract class _SingleChildWidget&Widget&SpanningWidget extends Widget
     with SpanningWidget {

  _ cloneContext(/* No info */) {
    // ** addr: 0xe3fe70, size: 0xcc
    // 0xe3fe70: EnterFrame
    //     0xe3fe70: stp             fp, lr, [SP, #-0x10]!
    //     0xe3fe74: mov             fp, SP
    // 0xe3fe78: AllocStack(0x10)
    //     0xe3fe78: sub             SP, SP, #0x10
    // 0xe3fe7c: CheckStackOverflow
    //     0xe3fe7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3fe80: cmp             SP, x16
    //     0xe3fe84: b.ls            #0xe3ff34
    // 0xe3fe88: r0 = LoadClassIdInstr(r1)
    //     0xe3fe88: ldur            x0, [x1, #-1]
    //     0xe3fe8c: ubfx            x0, x0, #0xc, #0x14
    // 0xe3fe90: r0 = GDT[cid_x0 + -0xe7b]()
    //     0xe3fe90: sub             lr, x0, #0xe7b
    //     0xe3fe94: ldr             lr, [x21, lr, lsl #3]
    //     0xe3fe98: blr             lr
    // 0xe3fe9c: stur            x0, [fp, #-8]
    // 0xe3fea0: r1 = LoadClassIdInstr(r0)
    //     0xe3fea0: ldur            x1, [x0, #-1]
    //     0xe3fea4: ubfx            x1, x1, #0xc, #0x14
    // 0xe3fea8: cmp             x1, #0x335
    // 0xe3feac: b.ne            #0xe3fedc
    // 0xe3feb0: r0 = RichTextContext()
    //     0xe3feb0: bl              #0xb1254c  ; AllocateRichTextContextStub -> RichTextContext (size=0x28)
    // 0xe3feb4: stur            x0, [fp, #-0x10]
    // 0xe3feb8: StoreField: r0->field_7 = rZR
    //     0xe3feb8: stur            xzr, [x0, #7]
    // 0xe3febc: StoreField: r0->field_f = rZR
    //     0xe3febc: stur            xzr, [x0, #0xf]
    // 0xe3fec0: ArrayStore: r0[0] = rZR  ; List_8
    //     0xe3fec0: stur            xzr, [x0, #0x17]
    // 0xe3fec4: StoreField: r0->field_1f = rZR
    //     0xe3fec4: stur            xzr, [x0, #0x1f]
    // 0xe3fec8: mov             x1, x0
    // 0xe3fecc: ldur            x2, [fp, #-8]
    // 0xe3fed0: r0 = apply()
    //     0xe3fed0: bl              #0xeabf54  ; [package:pdf/src/widgets/text.dart] RichTextContext::apply
    // 0xe3fed4: ldur            x0, [fp, #-0x10]
    // 0xe3fed8: b               #0xe3ff28
    // 0xe3fedc: cmp             x1, #0x336
    // 0xe3fee0: b.ne            #0xe3ff08
    // 0xe3fee4: r0 = TableContext()
    //     0xe3fee4: bl              #0xb137b0  ; AllocateTableContextStub -> TableContext (size=0x18)
    // 0xe3fee8: stur            x0, [fp, #-0x10]
    // 0xe3feec: StoreField: r0->field_7 = rZR
    //     0xe3feec: stur            xzr, [x0, #7]
    // 0xe3fef0: StoreField: r0->field_f = rZR
    //     0xe3fef0: stur            xzr, [x0, #0xf]
    // 0xe3fef4: mov             x1, x0
    // 0xe3fef8: ldur            x2, [fp, #-8]
    // 0xe3fefc: r0 = apply()
    //     0xe3fefc: bl              #0xeabed8  ; [package:pdf/src/widgets/table.dart] TableContext::apply
    // 0xe3ff00: ldur            x0, [fp, #-0x10]
    // 0xe3ff04: b               #0xe3ff28
    // 0xe3ff08: r0 = FlexContext()
    //     0xe3ff08: bl              #0xb13cc8  ; AllocateFlexContextStub -> FlexContext (size=0x18)
    // 0xe3ff0c: stur            x0, [fp, #-0x10]
    // 0xe3ff10: StoreField: r0->field_7 = rZR
    //     0xe3ff10: stur            xzr, [x0, #7]
    // 0xe3ff14: StoreField: r0->field_f = rZR
    //     0xe3ff14: stur            xzr, [x0, #0xf]
    // 0xe3ff18: mov             x1, x0
    // 0xe3ff1c: ldur            x2, [fp, #-8]
    // 0xe3ff20: r0 = apply()
    //     0xe3ff20: bl              #0xeabe5c  ; [package:pdf/src/widgets/flex.dart] FlexContext::apply
    // 0xe3ff24: ldur            x0, [fp, #-0x10]
    // 0xe3ff28: LeaveFrame
    //     0xe3ff28: mov             SP, fp
    //     0xe3ff2c: ldp             fp, lr, [SP], #0x10
    // 0xe3ff30: ret
    //     0xe3ff30: ret             
    // 0xe3ff34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3ff34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3ff38: b               #0xe3fe88
  }
  _ applyContext(/* No info */) {
    // ** addr: 0xe4c294, size: 0x164
    // 0xe4c294: EnterFrame
    //     0xe4c294: stp             fp, lr, [SP, #-0x10]!
    //     0xe4c298: mov             fp, SP
    // 0xe4c29c: AllocStack(0x10)
    //     0xe4c29c: sub             SP, SP, #0x10
    // 0xe4c2a0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe4c2a0: stur            x2, [fp, #-8]
    // 0xe4c2a4: CheckStackOverflow
    //     0xe4c2a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4c2a8: cmp             SP, x16
    //     0xe4c2ac: b.ls            #0xe4c3f0
    // 0xe4c2b0: r0 = LoadClassIdInstr(r1)
    //     0xe4c2b0: ldur            x0, [x1, #-1]
    //     0xe4c2b4: ubfx            x0, x0, #0xc, #0x14
    // 0xe4c2b8: r0 = GDT[cid_x0 + -0xe7b]()
    //     0xe4c2b8: sub             lr, x0, #0xe7b
    //     0xe4c2bc: ldr             lr, [x21, lr, lsl #3]
    //     0xe4c2c0: blr             lr
    // 0xe4c2c4: mov             x3, x0
    // 0xe4c2c8: stur            x3, [fp, #-0x10]
    // 0xe4c2cc: r0 = LoadClassIdInstr(r3)
    //     0xe4c2cc: ldur            x0, [x3, #-1]
    //     0xe4c2d0: ubfx            x0, x0, #0xc, #0x14
    // 0xe4c2d4: cmp             x0, #0x335
    // 0xe4c2d8: b.ne            #0xe4c33c
    // 0xe4c2dc: ldur            x4, [fp, #-8]
    // 0xe4c2e0: mov             x0, x4
    // 0xe4c2e4: r2 = Null
    //     0xe4c2e4: mov             x2, NULL
    // 0xe4c2e8: r1 = Null
    //     0xe4c2e8: mov             x1, NULL
    // 0xe4c2ec: r4 = LoadClassIdInstr(r0)
    //     0xe4c2ec: ldur            x4, [x0, #-1]
    //     0xe4c2f0: ubfx            x4, x4, #0xc, #0x14
    // 0xe4c2f4: cmp             x4, #0x335
    // 0xe4c2f8: b.eq            #0xe4c310
    // 0xe4c2fc: r8 = RichTextContext
    //     0xe4c2fc: add             x8, PP, #0x33, lsl #12  ; [pp+0x33728] Type: RichTextContext
    //     0xe4c300: ldr             x8, [x8, #0x728]
    // 0xe4c304: r3 = Null
    //     0xe4c304: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dd20] Null
    //     0xe4c308: ldr             x3, [x3, #0xd20]
    // 0xe4c30c: r0 = DefaultTypeTest()
    //     0xe4c30c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe4c310: ldur            x3, [fp, #-8]
    // 0xe4c314: LoadField: d0 = r3->field_7
    //     0xe4c314: ldur            d0, [x3, #7]
    // 0xe4c318: ldur            x4, [fp, #-0x10]
    // 0xe4c31c: StoreField: r4->field_7 = d0
    //     0xe4c31c: stur            d0, [x4, #7]
    // 0xe4c320: LoadField: d0 = r3->field_f
    //     0xe4c320: ldur            d0, [x3, #0xf]
    // 0xe4c324: StoreField: r4->field_f = d0
    //     0xe4c324: stur            d0, [x4, #0xf]
    // 0xe4c328: ArrayLoad: r0 = r3[0]  ; List_8
    //     0xe4c328: ldur            x0, [x3, #0x17]
    // 0xe4c32c: ArrayStore: r4[0] = r0  ; List_8
    //     0xe4c32c: stur            x0, [x4, #0x17]
    // 0xe4c330: LoadField: r0 = r3->field_1f
    //     0xe4c330: ldur            x0, [x3, #0x1f]
    // 0xe4c334: StoreField: r4->field_1f = r0
    //     0xe4c334: stur            x0, [x4, #0x1f]
    // 0xe4c338: b               #0xe4c3e0
    // 0xe4c33c: mov             x4, x3
    // 0xe4c340: ldur            x3, [fp, #-8]
    // 0xe4c344: cmp             x0, #0x336
    // 0xe4c348: b.ne            #0xe4c398
    // 0xe4c34c: mov             x0, x3
    // 0xe4c350: r2 = Null
    //     0xe4c350: mov             x2, NULL
    // 0xe4c354: r1 = Null
    //     0xe4c354: mov             x1, NULL
    // 0xe4c358: r4 = LoadClassIdInstr(r0)
    //     0xe4c358: ldur            x4, [x0, #-1]
    //     0xe4c35c: ubfx            x4, x4, #0xc, #0x14
    // 0xe4c360: cmp             x4, #0x336
    // 0xe4c364: b.eq            #0xe4c37c
    // 0xe4c368: r8 = TableContext
    //     0xe4c368: add             x8, PP, #0x33, lsl #12  ; [pp+0x33758] Type: TableContext
    //     0xe4c36c: ldr             x8, [x8, #0x758]
    // 0xe4c370: r3 = Null
    //     0xe4c370: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dd30] Null
    //     0xe4c374: ldr             x3, [x3, #0xd30]
    // 0xe4c378: r0 = DefaultTypeTest()
    //     0xe4c378: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe4c37c: ldur            x3, [fp, #-8]
    // 0xe4c380: LoadField: r0 = r3->field_7
    //     0xe4c380: ldur            x0, [x3, #7]
    // 0xe4c384: ldur            x4, [fp, #-0x10]
    // 0xe4c388: StoreField: r4->field_7 = r0
    //     0xe4c388: stur            x0, [x4, #7]
    // 0xe4c38c: LoadField: r0 = r3->field_f
    //     0xe4c38c: ldur            x0, [x3, #0xf]
    // 0xe4c390: StoreField: r4->field_f = r0
    //     0xe4c390: stur            x0, [x4, #0xf]
    // 0xe4c394: b               #0xe4c3e0
    // 0xe4c398: mov             x0, x3
    // 0xe4c39c: r2 = Null
    //     0xe4c39c: mov             x2, NULL
    // 0xe4c3a0: r1 = Null
    //     0xe4c3a0: mov             x1, NULL
    // 0xe4c3a4: r4 = LoadClassIdInstr(r0)
    //     0xe4c3a4: ldur            x4, [x0, #-1]
    //     0xe4c3a8: ubfx            x4, x4, #0xc, #0x14
    // 0xe4c3ac: cmp             x4, #0x337
    // 0xe4c3b0: b.eq            #0xe4c3c8
    // 0xe4c3b4: r8 = FlexContext
    //     0xe4c3b4: add             x8, PP, #0x33, lsl #12  ; [pp+0x338d0] Type: FlexContext
    //     0xe4c3b8: ldr             x8, [x8, #0x8d0]
    // 0xe4c3bc: r3 = Null
    //     0xe4c3bc: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dd40] Null
    //     0xe4c3c0: ldr             x3, [x3, #0xd40]
    // 0xe4c3c4: r0 = DefaultTypeTest()
    //     0xe4c3c4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe4c3c8: ldur            x1, [fp, #-8]
    // 0xe4c3cc: LoadField: r2 = r1->field_7
    //     0xe4c3cc: ldur            x2, [x1, #7]
    // 0xe4c3d0: ldur            x3, [fp, #-0x10]
    // 0xe4c3d4: StoreField: r3->field_7 = r2
    //     0xe4c3d4: stur            x2, [x3, #7]
    // 0xe4c3d8: LoadField: r2 = r1->field_f
    //     0xe4c3d8: ldur            x2, [x1, #0xf]
    // 0xe4c3dc: StoreField: r3->field_f = r2
    //     0xe4c3dc: stur            x2, [x3, #0xf]
    // 0xe4c3e0: r0 = Null
    //     0xe4c3e0: mov             x0, NULL
    // 0xe4c3e4: LeaveFrame
    //     0xe4c3e4: mov             SP, fp
    //     0xe4c3e8: ldp             fp, lr, [SP], #0x10
    // 0xe4c3ec: ret
    //     0xe4c3ec: ret             
    // 0xe4c3f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4c3f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4c3f4: b               #0xe4c2b0
  }
}

// class id: 800, size: 0x10, field offset: 0xc
abstract class StatelessWidget extends _SingleChildWidget&Widget&SpanningWidget {

  _ restoreContext(/* No info */) {
    // ** addr: 0xe54a20, size: 0xa8
    // 0xe54a20: EnterFrame
    //     0xe54a20: stp             fp, lr, [SP, #-0x10]!
    //     0xe54a24: mov             fp, SP
    // 0xe54a28: AllocStack(0x10)
    //     0xe54a28: sub             SP, SP, #0x10
    // 0xe54a2c: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xe54a2c: mov             x3, x2
    //     0xe54a30: stur            x2, [fp, #-0x10]
    // 0xe54a34: CheckStackOverflow
    //     0xe54a34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe54a38: cmp             SP, x16
    //     0xe54a3c: b.ls            #0xe54ac0
    // 0xe54a40: LoadField: r4 = r1->field_b
    //     0xe54a40: ldur            w4, [x1, #0xb]
    // 0xe54a44: DecompressPointer r4
    //     0xe54a44: add             x4, x4, HEAP, lsl #32
    // 0xe54a48: stur            x4, [fp, #-8]
    // 0xe54a4c: r0 = LoadClassIdInstr(r4)
    //     0xe54a4c: ldur            x0, [x4, #-1]
    //     0xe54a50: ubfx            x0, x0, #0xc, #0x14
    // 0xe54a54: sub             x16, x0, #0x31a
    // 0xe54a58: cmp             x16, #0xf
    // 0xe54a5c: b.hi            #0xe54ab0
    // 0xe54a60: mov             x0, x4
    // 0xe54a64: r2 = Null
    //     0xe54a64: mov             x2, NULL
    // 0xe54a68: r1 = Null
    //     0xe54a68: mov             x1, NULL
    // 0xe54a6c: r4 = LoadClassIdInstr(r0)
    //     0xe54a6c: ldur            x4, [x0, #-1]
    //     0xe54a70: ubfx            x4, x4, #0xc, #0x14
    // 0xe54a74: sub             x4, x4, #0x31a
    // 0xe54a78: cmp             x4, #0xf
    // 0xe54a7c: b.ls            #0xe54a94
    // 0xe54a80: r8 = SpanningWidget
    //     0xe54a80: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3dc98] Type: SpanningWidget
    //     0xe54a84: ldr             x8, [x8, #0xc98]
    // 0xe54a88: r3 = Null
    //     0xe54a88: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dcf0] Null
    //     0xe54a8c: ldr             x3, [x3, #0xcf0]
    // 0xe54a90: r0 = DefaultTypeTest()
    //     0xe54a90: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe54a94: ldur            x1, [fp, #-8]
    // 0xe54a98: r0 = LoadClassIdInstr(r1)
    //     0xe54a98: ldur            x0, [x1, #-1]
    //     0xe54a9c: ubfx            x0, x0, #0xc, #0x14
    // 0xe54aa0: ldur            x2, [fp, #-0x10]
    // 0xe54aa4: r0 = GDT[cid_x0 + -0xdd8]()
    //     0xe54aa4: sub             lr, x0, #0xdd8
    //     0xe54aa8: ldr             lr, [x21, lr, lsl #3]
    //     0xe54aac: blr             lr
    // 0xe54ab0: r0 = Null
    //     0xe54ab0: mov             x0, NULL
    // 0xe54ab4: LeaveFrame
    //     0xe54ab4: mov             SP, fp
    //     0xe54ab8: ldp             fp, lr, [SP], #0x10
    // 0xe54abc: ret
    //     0xe54abc: ret             
    // 0xe54ac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe54ac0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe54ac4: b               #0xe54a40
  }
  get _ hasMoreWidgets(/* No info */) {
    // ** addr: 0xe61d10, size: 0xa0
    // 0xe61d10: EnterFrame
    //     0xe61d10: stp             fp, lr, [SP, #-0x10]!
    //     0xe61d14: mov             fp, SP
    // 0xe61d18: AllocStack(0x8)
    //     0xe61d18: sub             SP, SP, #8
    // 0xe61d1c: CheckStackOverflow
    //     0xe61d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe61d20: cmp             SP, x16
    //     0xe61d24: b.ls            #0xe61da8
    // 0xe61d28: LoadField: r3 = r1->field_b
    //     0xe61d28: ldur            w3, [x1, #0xb]
    // 0xe61d2c: DecompressPointer r3
    //     0xe61d2c: add             x3, x3, HEAP, lsl #32
    // 0xe61d30: stur            x3, [fp, #-8]
    // 0xe61d34: r0 = LoadClassIdInstr(r3)
    //     0xe61d34: ldur            x0, [x3, #-1]
    //     0xe61d38: ubfx            x0, x0, #0xc, #0x14
    // 0xe61d3c: sub             x16, x0, #0x31a
    // 0xe61d40: cmp             x16, #0xf
    // 0xe61d44: b.hi            #0xe61d98
    // 0xe61d48: mov             x0, x3
    // 0xe61d4c: r2 = Null
    //     0xe61d4c: mov             x2, NULL
    // 0xe61d50: r1 = Null
    //     0xe61d50: mov             x1, NULL
    // 0xe61d54: r4 = LoadClassIdInstr(r0)
    //     0xe61d54: ldur            x4, [x0, #-1]
    //     0xe61d58: ubfx            x4, x4, #0xc, #0x14
    // 0xe61d5c: sub             x4, x4, #0x31a
    // 0xe61d60: cmp             x4, #0xf
    // 0xe61d64: b.ls            #0xe61d7c
    // 0xe61d68: r8 = SpanningWidget
    //     0xe61d68: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3dc98] Type: SpanningWidget
    //     0xe61d6c: ldr             x8, [x8, #0xc98]
    // 0xe61d70: r3 = Null
    //     0xe61d70: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dd00] Null
    //     0xe61d74: ldr             x3, [x3, #0xd00]
    // 0xe61d78: r0 = DefaultTypeTest()
    //     0xe61d78: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe61d7c: ldur            x1, [fp, #-8]
    // 0xe61d80: r0 = LoadClassIdInstr(r1)
    //     0xe61d80: ldur            x0, [x1, #-1]
    //     0xe61d84: ubfx            x0, x0, #0xc, #0x14
    // 0xe61d88: r0 = GDT[cid_x0 + -0xe3d]()
    //     0xe61d88: sub             lr, x0, #0xe3d
    //     0xe61d8c: ldr             lr, [x21, lr, lsl #3]
    //     0xe61d90: blr             lr
    // 0xe61d94: b               #0xe61d9c
    // 0xe61d98: r0 = false
    //     0xe61d98: add             x0, NULL, #0x30  ; false
    // 0xe61d9c: LeaveFrame
    //     0xe61d9c: mov             SP, fp
    //     0xe61da0: ldp             fp, lr, [SP], #0x10
    // 0xe61da4: ret
    //     0xe61da4: ret             
    // 0xe61da8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe61da8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe61dac: b               #0xe61d28
  }
  _ saveContext(/* No info */) {
    // ** addr: 0xe63648, size: 0xa4
    // 0xe63648: EnterFrame
    //     0xe63648: stp             fp, lr, [SP, #-0x10]!
    //     0xe6364c: mov             fp, SP
    // 0xe63650: AllocStack(0x8)
    //     0xe63650: sub             SP, SP, #8
    // 0xe63654: CheckStackOverflow
    //     0xe63654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe63658: cmp             SP, x16
    //     0xe6365c: b.ls            #0xe636e4
    // 0xe63660: LoadField: r3 = r1->field_b
    //     0xe63660: ldur            w3, [x1, #0xb]
    // 0xe63664: DecompressPointer r3
    //     0xe63664: add             x3, x3, HEAP, lsl #32
    // 0xe63668: stur            x3, [fp, #-8]
    // 0xe6366c: r0 = LoadClassIdInstr(r3)
    //     0xe6366c: ldur            x0, [x3, #-1]
    //     0xe63670: ubfx            x0, x0, #0xc, #0x14
    // 0xe63674: sub             x16, x0, #0x31a
    // 0xe63678: cmp             x16, #0xf
    // 0xe6367c: b.hi            #0xe636d8
    // 0xe63680: mov             x0, x3
    // 0xe63684: r2 = Null
    //     0xe63684: mov             x2, NULL
    // 0xe63688: r1 = Null
    //     0xe63688: mov             x1, NULL
    // 0xe6368c: r4 = LoadClassIdInstr(r0)
    //     0xe6368c: ldur            x4, [x0, #-1]
    //     0xe63690: ubfx            x4, x4, #0xc, #0x14
    // 0xe63694: sub             x4, x4, #0x31a
    // 0xe63698: cmp             x4, #0xf
    // 0xe6369c: b.ls            #0xe636b4
    // 0xe636a0: r8 = SpanningWidget
    //     0xe636a0: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3dc98] Type: SpanningWidget
    //     0xe636a4: ldr             x8, [x8, #0xc98]
    // 0xe636a8: r3 = Null
    //     0xe636a8: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dce0] Null
    //     0xe636ac: ldr             x3, [x3, #0xce0]
    // 0xe636b0: r0 = DefaultTypeTest()
    //     0xe636b0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe636b4: ldur            x1, [fp, #-8]
    // 0xe636b8: r0 = LoadClassIdInstr(r1)
    //     0xe636b8: ldur            x0, [x1, #-1]
    //     0xe636bc: ubfx            x0, x0, #0xc, #0x14
    // 0xe636c0: r0 = GDT[cid_x0 + -0xe7b]()
    //     0xe636c0: sub             lr, x0, #0xe7b
    //     0xe636c4: ldr             lr, [x21, lr, lsl #3]
    //     0xe636c8: blr             lr
    // 0xe636cc: LeaveFrame
    //     0xe636cc: mov             SP, fp
    //     0xe636d0: ldp             fp, lr, [SP], #0x10
    // 0xe636d4: ret
    //     0xe636d4: ret             
    // 0xe636d8: r0 = UnimplementedError()
    //     0xe636d8: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xe636dc: r0 = Throw()
    //     0xe636dc: bl              #0xec04b8  ; ThrowStub
    // 0xe636e0: brk             #0
    // 0xe636e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe636e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe636e8: b               #0xe63660
  }
  _ paint(/* No info */) {
    // ** addr: 0xe63944, size: 0xfc
    // 0xe63944: EnterFrame
    //     0xe63944: stp             fp, lr, [SP, #-0x10]!
    //     0xe63948: mov             fp, SP
    // 0xe6394c: AllocStack(0x20)
    //     0xe6394c: sub             SP, SP, #0x20
    // 0xe63950: SetupParameters(StatelessWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe63950: stur            x1, [fp, #-8]
    //     0xe63954: stur            x2, [fp, #-0x10]
    // 0xe63958: CheckStackOverflow
    //     0xe63958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6395c: cmp             SP, x16
    //     0xe63960: b.ls            #0xe63a2c
    // 0xe63964: LoadField: r0 = r1->field_b
    //     0xe63964: ldur            w0, [x1, #0xb]
    // 0xe63968: DecompressPointer r0
    //     0xe63968: add             x0, x0, HEAP, lsl #32
    // 0xe6396c: cmp             w0, NULL
    // 0xe63970: b.eq            #0xe63a1c
    // 0xe63974: r0 = Matrix4()
    //     0xe63974: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe63978: r4 = 32
    //     0xe63978: movz            x4, #0x20
    // 0xe6397c: stur            x0, [fp, #-0x18]
    // 0xe63980: r0 = AllocateFloat64Array()
    //     0xe63980: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe63984: mov             x1, x0
    // 0xe63988: ldur            x0, [fp, #-0x18]
    // 0xe6398c: StoreField: r0->field_7 = r1
    //     0xe6398c: stur            w1, [x0, #7]
    // 0xe63990: mov             x1, x0
    // 0xe63994: r0 = setIdentity()
    //     0xe63994: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe63998: ldur            x0, [fp, #-8]
    // 0xe6399c: LoadField: r1 = r0->field_7
    //     0xe6399c: ldur            w1, [x0, #7]
    // 0xe639a0: DecompressPointer r1
    //     0xe639a0: add             x1, x1, HEAP, lsl #32
    // 0xe639a4: cmp             w1, NULL
    // 0xe639a8: b.eq            #0xe63a34
    // 0xe639ac: LoadField: d0 = r1->field_7
    //     0xe639ac: ldur            d0, [x1, #7]
    // 0xe639b0: LoadField: d1 = r1->field_f
    //     0xe639b0: ldur            d1, [x1, #0xf]
    // 0xe639b4: ldur            x1, [fp, #-0x18]
    // 0xe639b8: r0 = translate()
    //     0xe639b8: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe639bc: ldur            x2, [fp, #-0x10]
    // 0xe639c0: LoadField: r0 = r2->field_b
    //     0xe639c0: ldur            w0, [x2, #0xb]
    // 0xe639c4: DecompressPointer r0
    //     0xe639c4: add             x0, x0, HEAP, lsl #32
    // 0xe639c8: stur            x0, [fp, #-0x20]
    // 0xe639cc: cmp             w0, NULL
    // 0xe639d0: b.eq            #0xe63a38
    // 0xe639d4: mov             x1, x0
    // 0xe639d8: r0 = saveContext()
    //     0xe639d8: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe639dc: ldur            x1, [fp, #-0x20]
    // 0xe639e0: ldur            x2, [fp, #-0x18]
    // 0xe639e4: r0 = setTransform()
    //     0xe639e4: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe639e8: ldur            x0, [fp, #-8]
    // 0xe639ec: LoadField: r1 = r0->field_b
    //     0xe639ec: ldur            w1, [x0, #0xb]
    // 0xe639f0: DecompressPointer r1
    //     0xe639f0: add             x1, x1, HEAP, lsl #32
    // 0xe639f4: cmp             w1, NULL
    // 0xe639f8: b.eq            #0xe63a3c
    // 0xe639fc: r0 = LoadClassIdInstr(r1)
    //     0xe639fc: ldur            x0, [x1, #-1]
    //     0xe63a00: ubfx            x0, x0, #0xc, #0x14
    // 0xe63a04: ldur            x2, [fp, #-0x10]
    // 0xe63a08: r0 = GDT[cid_x0 + -0xe8b]()
    //     0xe63a08: sub             lr, x0, #0xe8b
    //     0xe63a0c: ldr             lr, [x21, lr, lsl #3]
    //     0xe63a10: blr             lr
    // 0xe63a14: ldur            x1, [fp, #-0x20]
    // 0xe63a18: r0 = restoreContext()
    //     0xe63a18: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe63a1c: r0 = Null
    //     0xe63a1c: mov             x0, NULL
    // 0xe63a20: LeaveFrame
    //     0xe63a20: mov             SP, fp
    //     0xe63a24: ldp             fp, lr, [SP], #0x10
    // 0xe63a28: ret
    //     0xe63a28: ret             
    // 0xe63a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe63a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe63a30: b               #0xe63964
    // 0xe63a34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe63a34: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe63a38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe63a38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe63a3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe63a3c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ canSpan(/* No info */) {
    // ** addr: 0xe7da9c, size: 0xa8
    // 0xe7da9c: EnterFrame
    //     0xe7da9c: stp             fp, lr, [SP, #-0x10]!
    //     0xe7daa0: mov             fp, SP
    // 0xe7daa4: AllocStack(0x8)
    //     0xe7daa4: sub             SP, SP, #8
    // 0xe7daa8: CheckStackOverflow
    //     0xe7daa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7daac: cmp             SP, x16
    //     0xe7dab0: b.ls            #0xe7db3c
    // 0xe7dab4: LoadField: r3 = r1->field_b
    //     0xe7dab4: ldur            w3, [x1, #0xb]
    // 0xe7dab8: DecompressPointer r3
    //     0xe7dab8: add             x3, x3, HEAP, lsl #32
    // 0xe7dabc: stur            x3, [fp, #-8]
    // 0xe7dac0: cmp             w3, NULL
    // 0xe7dac4: b.eq            #0xe7db2c
    // 0xe7dac8: r0 = LoadClassIdInstr(r3)
    //     0xe7dac8: ldur            x0, [x3, #-1]
    //     0xe7dacc: ubfx            x0, x0, #0xc, #0x14
    // 0xe7dad0: sub             x16, x0, #0x31a
    // 0xe7dad4: cmp             x16, #0xf
    // 0xe7dad8: b.hi            #0xe7db2c
    // 0xe7dadc: mov             x0, x3
    // 0xe7dae0: r2 = Null
    //     0xe7dae0: mov             x2, NULL
    // 0xe7dae4: r1 = Null
    //     0xe7dae4: mov             x1, NULL
    // 0xe7dae8: r4 = LoadClassIdInstr(r0)
    //     0xe7dae8: ldur            x4, [x0, #-1]
    //     0xe7daec: ubfx            x4, x4, #0xc, #0x14
    // 0xe7daf0: sub             x4, x4, #0x31a
    // 0xe7daf4: cmp             x4, #0xf
    // 0xe7daf8: b.ls            #0xe7db10
    // 0xe7dafc: r8 = SpanningWidget
    //     0xe7dafc: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3dc98] Type: SpanningWidget
    //     0xe7db00: ldr             x8, [x8, #0xc98]
    // 0xe7db04: r3 = Null
    //     0xe7db04: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dd10] Null
    //     0xe7db08: ldr             x3, [x3, #0xd10]
    // 0xe7db0c: r0 = DefaultTypeTest()
    //     0xe7db0c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe7db10: ldur            x1, [fp, #-8]
    // 0xe7db14: r0 = LoadClassIdInstr(r1)
    //     0xe7db14: ldur            x0, [x1, #-1]
    //     0xe7db18: ubfx            x0, x0, #0xc, #0x14
    // 0xe7db1c: r0 = GDT[cid_x0 + -0xf00]()
    //     0xe7db1c: sub             lr, x0, #0xf00
    //     0xe7db20: ldr             lr, [x21, lr, lsl #3]
    //     0xe7db24: blr             lr
    // 0xe7db28: b               #0xe7db30
    // 0xe7db2c: r0 = false
    //     0xe7db2c: add             x0, NULL, #0x30  ; false
    // 0xe7db30: LeaveFrame
    //     0xe7db30: mov             SP, fp
    //     0xe7db34: ldp             fp, lr, [SP], #0x10
    // 0xe7db38: ret
    //     0xe7db38: ret             
    // 0xe7db3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7db3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7db40: b               #0xe7dab4
  }
  _ layout(/* No info */) {
    // ** addr: 0xe8f294, size: 0x1c0
    // 0xe8f294: EnterFrame
    //     0xe8f294: stp             fp, lr, [SP, #-0x10]!
    //     0xe8f298: mov             fp, SP
    // 0xe8f29c: AllocStack(0x40)
    //     0xe8f29c: sub             SP, SP, #0x40
    // 0xe8f2a0: SetupParameters(StatelessWidget this /* r1 => r1, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */, dynamic _ /* r3 => r3, fp-0x30 */)
    //     0xe8f2a0: stur            x1, [fp, #-0x20]
    //     0xe8f2a4: stur            x2, [fp, #-0x28]
    //     0xe8f2a8: stur            x3, [fp, #-0x30]
    // 0xe8f2ac: CheckStackOverflow
    //     0xe8f2ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8f2b0: cmp             SP, x16
    //     0xe8f2b4: b.ls            #0xe8f448
    // 0xe8f2b8: LoadField: r0 = r1->field_b
    //     0xe8f2b8: ldur            w0, [x1, #0xb]
    // 0xe8f2bc: DecompressPointer r0
    //     0xe8f2bc: add             x0, x0, HEAP, lsl #32
    // 0xe8f2c0: cmp             w0, NULL
    // 0xe8f2c4: b.ne            #0xe8f3dc
    // 0xe8f2c8: r0 = LoadClassIdInstr(r1)
    //     0xe8f2c8: ldur            x0, [x1, #-1]
    //     0xe8f2cc: ubfx            x0, x0, #0xc, #0x14
    // 0xe8f2d0: cmp             x0, #0x322
    // 0xe8f2d4: b.ne            #0xe8f394
    // 0xe8f2d8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xe8f2d8: ldur            w0, [x1, #0x17]
    // 0xe8f2dc: DecompressPointer r0
    //     0xe8f2dc: add             x0, x0, HEAP, lsl #32
    // 0xe8f2e0: stur            x0, [fp, #-0x18]
    // 0xe8f2e4: LoadField: r4 = r1->field_f
    //     0xe8f2e4: ldur            w4, [x1, #0xf]
    // 0xe8f2e8: DecompressPointer r4
    //     0xe8f2e8: add             x4, x4, HEAP, lsl #32
    // 0xe8f2ec: stur            x4, [fp, #-0x10]
    // 0xe8f2f0: LoadField: r5 = r1->field_13
    //     0xe8f2f0: ldur            w5, [x1, #0x13]
    // 0xe8f2f4: DecompressPointer r5
    //     0xe8f2f4: add             x5, x5, HEAP, lsl #32
    // 0xe8f2f8: stur            x5, [fp, #-8]
    // 0xe8f2fc: cmp             w4, NULL
    // 0xe8f300: b.ne            #0xe8f30c
    // 0xe8f304: d0 = 0.000000
    //     0xe8f304: eor             v0.16b, v0.16b, v0.16b
    // 0xe8f308: b               #0xe8f310
    // 0xe8f30c: LoadField: d0 = r4->field_7
    //     0xe8f30c: ldur            d0, [x4, #7]
    // 0xe8f310: stur            d0, [fp, #-0x40]
    // 0xe8f314: r0 = BoxConstraints()
    //     0xe8f314: bl              #0xb13854  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xe8f318: ldur            d0, [fp, #-0x40]
    // 0xe8f31c: stur            x0, [fp, #-0x38]
    // 0xe8f320: StoreField: r0->field_7 = d0
    //     0xe8f320: stur            d0, [x0, #7]
    // 0xe8f324: ldur            x1, [fp, #-0x10]
    // 0xe8f328: cmp             w1, NULL
    // 0xe8f32c: b.ne            #0xe8f338
    // 0xe8f330: d0 = inf
    //     0xe8f330: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8f334: b               #0xe8f33c
    // 0xe8f338: LoadField: d0 = r1->field_7
    //     0xe8f338: ldur            d0, [x1, #7]
    // 0xe8f33c: ldur            x1, [fp, #-8]
    // 0xe8f340: StoreField: r0->field_f = d0
    //     0xe8f340: stur            d0, [x0, #0xf]
    // 0xe8f344: cmp             w1, NULL
    // 0xe8f348: b.ne            #0xe8f354
    // 0xe8f34c: d0 = 0.000000
    //     0xe8f34c: eor             v0.16b, v0.16b, v0.16b
    // 0xe8f350: b               #0xe8f358
    // 0xe8f354: LoadField: d0 = r1->field_7
    //     0xe8f354: ldur            d0, [x1, #7]
    // 0xe8f358: ArrayStore: r0[0] = d0  ; List_8
    //     0xe8f358: stur            d0, [x0, #0x17]
    // 0xe8f35c: cmp             w1, NULL
    // 0xe8f360: b.ne            #0xe8f36c
    // 0xe8f364: d0 = inf
    //     0xe8f364: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xe8f368: b               #0xe8f370
    // 0xe8f36c: LoadField: d0 = r1->field_7
    //     0xe8f36c: ldur            d0, [x1, #7]
    // 0xe8f370: ldur            x1, [fp, #-0x18]
    // 0xe8f374: StoreField: r0->field_1f = d0
    //     0xe8f374: stur            d0, [x0, #0x1f]
    // 0xe8f378: r0 = ConstrainedBox()
    //     0xe8f378: bl              #0xe8f454  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xe8f37c: mov             x1, x0
    // 0xe8f380: ldur            x0, [fp, #-0x38]
    // 0xe8f384: StoreField: r1->field_f = r0
    //     0xe8f384: stur            w0, [x1, #0xf]
    // 0xe8f388: ldur            x0, [fp, #-0x18]
    // 0xe8f38c: StoreField: r1->field_b = r0
    //     0xe8f38c: stur            w0, [x1, #0xb]
    // 0xe8f390: b               #0xe8f3b4
    // 0xe8f394: mov             x2, x1
    // 0xe8f398: r0 = LoadClassIdInstr(r2)
    //     0xe8f398: ldur            x0, [x2, #-1]
    //     0xe8f39c: ubfx            x0, x0, #0xc, #0x14
    // 0xe8f3a0: mov             x1, x2
    // 0xe8f3a4: r0 = GDT[cid_x0 + -0xff7]()
    //     0xe8f3a4: sub             lr, x0, #0xff7
    //     0xe8f3a8: ldr             lr, [x21, lr, lsl #3]
    //     0xe8f3ac: blr             lr
    // 0xe8f3b0: mov             x1, x0
    // 0xe8f3b4: ldur            x4, [fp, #-0x20]
    // 0xe8f3b8: mov             x0, x1
    // 0xe8f3bc: StoreField: r4->field_b = r0
    //     0xe8f3bc: stur            w0, [x4, #0xb]
    //     0xe8f3c0: ldurb           w16, [x4, #-1]
    //     0xe8f3c4: ldurb           w17, [x0, #-1]
    //     0xe8f3c8: and             x16, x17, x16, lsr #2
    //     0xe8f3cc: tst             x16, HEAP, lsr #32
    //     0xe8f3d0: b.eq            #0xe8f3d8
    //     0xe8f3d4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe8f3d8: b               #0xe8f3e4
    // 0xe8f3dc: mov             x4, x1
    // 0xe8f3e0: mov             x1, x0
    // 0xe8f3e4: r0 = LoadClassIdInstr(r1)
    //     0xe8f3e4: ldur            x0, [x1, #-1]
    //     0xe8f3e8: ubfx            x0, x0, #0xc, #0x14
    // 0xe8f3ec: ldur            x2, [fp, #-0x28]
    // 0xe8f3f0: ldur            x3, [fp, #-0x30]
    // 0xe8f3f4: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe8f3f4: sub             lr, x0, #0xf89
    //     0xe8f3f8: ldr             lr, [x21, lr, lsl #3]
    //     0xe8f3fc: blr             lr
    // 0xe8f400: ldur            x1, [fp, #-0x20]
    // 0xe8f404: LoadField: r2 = r1->field_b
    //     0xe8f404: ldur            w2, [x1, #0xb]
    // 0xe8f408: DecompressPointer r2
    //     0xe8f408: add             x2, x2, HEAP, lsl #32
    // 0xe8f40c: cmp             w2, NULL
    // 0xe8f410: b.eq            #0xe8f450
    // 0xe8f414: LoadField: r0 = r2->field_7
    //     0xe8f414: ldur            w0, [x2, #7]
    // 0xe8f418: DecompressPointer r0
    //     0xe8f418: add             x0, x0, HEAP, lsl #32
    // 0xe8f41c: StoreField: r1->field_7 = r0
    //     0xe8f41c: stur            w0, [x1, #7]
    //     0xe8f420: ldurb           w16, [x1, #-1]
    //     0xe8f424: ldurb           w17, [x0, #-1]
    //     0xe8f428: and             x16, x17, x16, lsr #2
    //     0xe8f42c: tst             x16, HEAP, lsr #32
    //     0xe8f430: b.eq            #0xe8f438
    //     0xe8f434: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8f438: r0 = Null
    //     0xe8f438: mov             x0, NULL
    // 0xe8f43c: LeaveFrame
    //     0xe8f43c: mov             SP, fp
    //     0xe8f440: ldp             fp, lr, [SP], #0x10
    // 0xe8f444: ret
    //     0xe8f444: ret             
    // 0xe8f448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8f448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8f44c: b               #0xe8f2b8
    // 0xe8f450: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe8f450: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 803, size: 0x10, field offset: 0xc
abstract class SingleChildWidget extends _SingleChildWidget&Widget&SpanningWidget {

  _ restoreContext(/* No info */) {
    // ** addr: 0xe54978, size: 0xa8
    // 0xe54978: EnterFrame
    //     0xe54978: stp             fp, lr, [SP, #-0x10]!
    //     0xe5497c: mov             fp, SP
    // 0xe54980: AllocStack(0x10)
    //     0xe54980: sub             SP, SP, #0x10
    // 0xe54984: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xe54984: mov             x3, x2
    //     0xe54988: stur            x2, [fp, #-0x10]
    // 0xe5498c: CheckStackOverflow
    //     0xe5498c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe54990: cmp             SP, x16
    //     0xe54994: b.ls            #0xe54a18
    // 0xe54998: LoadField: r4 = r1->field_b
    //     0xe54998: ldur            w4, [x1, #0xb]
    // 0xe5499c: DecompressPointer r4
    //     0xe5499c: add             x4, x4, HEAP, lsl #32
    // 0xe549a0: stur            x4, [fp, #-8]
    // 0xe549a4: r0 = LoadClassIdInstr(r4)
    //     0xe549a4: ldur            x0, [x4, #-1]
    //     0xe549a8: ubfx            x0, x0, #0xc, #0x14
    // 0xe549ac: sub             x16, x0, #0x31a
    // 0xe549b0: cmp             x16, #0xf
    // 0xe549b4: b.hi            #0xe54a08
    // 0xe549b8: mov             x0, x4
    // 0xe549bc: r2 = Null
    //     0xe549bc: mov             x2, NULL
    // 0xe549c0: r1 = Null
    //     0xe549c0: mov             x1, NULL
    // 0xe549c4: r4 = LoadClassIdInstr(r0)
    //     0xe549c4: ldur            x4, [x0, #-1]
    //     0xe549c8: ubfx            x4, x4, #0xc, #0x14
    // 0xe549cc: sub             x4, x4, #0x31a
    // 0xe549d0: cmp             x4, #0xf
    // 0xe549d4: b.ls            #0xe549ec
    // 0xe549d8: r8 = SpanningWidget
    //     0xe549d8: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3dc98] Type: SpanningWidget
    //     0xe549dc: ldr             x8, [x8, #0xc98]
    // 0xe549e0: r3 = Null
    //     0xe549e0: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dcb0] Null
    //     0xe549e4: ldr             x3, [x3, #0xcb0]
    // 0xe549e8: r0 = DefaultTypeTest()
    //     0xe549e8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe549ec: ldur            x1, [fp, #-8]
    // 0xe549f0: r0 = LoadClassIdInstr(r1)
    //     0xe549f0: ldur            x0, [x1, #-1]
    //     0xe549f4: ubfx            x0, x0, #0xc, #0x14
    // 0xe549f8: ldur            x2, [fp, #-0x10]
    // 0xe549fc: r0 = GDT[cid_x0 + -0xdd8]()
    //     0xe549fc: sub             lr, x0, #0xdd8
    //     0xe54a00: ldr             lr, [x21, lr, lsl #3]
    //     0xe54a04: blr             lr
    // 0xe54a08: r0 = Null
    //     0xe54a08: mov             x0, NULL
    // 0xe54a0c: LeaveFrame
    //     0xe54a0c: mov             SP, fp
    //     0xe54a10: ldp             fp, lr, [SP], #0x10
    // 0xe54a14: ret
    //     0xe54a14: ret             
    // 0xe54a18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe54a18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe54a1c: b               #0xe54998
  }
  get _ hasMoreWidgets(/* No info */) {
    // ** addr: 0xe61c70, size: 0xa0
    // 0xe61c70: EnterFrame
    //     0xe61c70: stp             fp, lr, [SP, #-0x10]!
    //     0xe61c74: mov             fp, SP
    // 0xe61c78: AllocStack(0x8)
    //     0xe61c78: sub             SP, SP, #8
    // 0xe61c7c: CheckStackOverflow
    //     0xe61c7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe61c80: cmp             SP, x16
    //     0xe61c84: b.ls            #0xe61d08
    // 0xe61c88: LoadField: r3 = r1->field_b
    //     0xe61c88: ldur            w3, [x1, #0xb]
    // 0xe61c8c: DecompressPointer r3
    //     0xe61c8c: add             x3, x3, HEAP, lsl #32
    // 0xe61c90: stur            x3, [fp, #-8]
    // 0xe61c94: r0 = LoadClassIdInstr(r3)
    //     0xe61c94: ldur            x0, [x3, #-1]
    //     0xe61c98: ubfx            x0, x0, #0xc, #0x14
    // 0xe61c9c: sub             x16, x0, #0x31a
    // 0xe61ca0: cmp             x16, #0xf
    // 0xe61ca4: b.hi            #0xe61cf8
    // 0xe61ca8: mov             x0, x3
    // 0xe61cac: r2 = Null
    //     0xe61cac: mov             x2, NULL
    // 0xe61cb0: r1 = Null
    //     0xe61cb0: mov             x1, NULL
    // 0xe61cb4: r4 = LoadClassIdInstr(r0)
    //     0xe61cb4: ldur            x4, [x0, #-1]
    //     0xe61cb8: ubfx            x4, x4, #0xc, #0x14
    // 0xe61cbc: sub             x4, x4, #0x31a
    // 0xe61cc0: cmp             x4, #0xf
    // 0xe61cc4: b.ls            #0xe61cdc
    // 0xe61cc8: r8 = SpanningWidget
    //     0xe61cc8: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3dc98] Type: SpanningWidget
    //     0xe61ccc: ldr             x8, [x8, #0xc98]
    // 0xe61cd0: r3 = Null
    //     0xe61cd0: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dcc0] Null
    //     0xe61cd4: ldr             x3, [x3, #0xcc0]
    // 0xe61cd8: r0 = DefaultTypeTest()
    //     0xe61cd8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe61cdc: ldur            x1, [fp, #-8]
    // 0xe61ce0: r0 = LoadClassIdInstr(r1)
    //     0xe61ce0: ldur            x0, [x1, #-1]
    //     0xe61ce4: ubfx            x0, x0, #0xc, #0x14
    // 0xe61ce8: r0 = GDT[cid_x0 + -0xe3d]()
    //     0xe61ce8: sub             lr, x0, #0xe3d
    //     0xe61cec: ldr             lr, [x21, lr, lsl #3]
    //     0xe61cf0: blr             lr
    // 0xe61cf4: b               #0xe61cfc
    // 0xe61cf8: r0 = false
    //     0xe61cf8: add             x0, NULL, #0x30  ; false
    // 0xe61cfc: LeaveFrame
    //     0xe61cfc: mov             SP, fp
    //     0xe61d00: ldp             fp, lr, [SP], #0x10
    // 0xe61d04: ret
    //     0xe61d04: ret             
    // 0xe61d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe61d08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe61d0c: b               #0xe61c88
  }
  _ saveContext(/* No info */) {
    // ** addr: 0xe635a4, size: 0xa4
    // 0xe635a4: EnterFrame
    //     0xe635a4: stp             fp, lr, [SP, #-0x10]!
    //     0xe635a8: mov             fp, SP
    // 0xe635ac: AllocStack(0x8)
    //     0xe635ac: sub             SP, SP, #8
    // 0xe635b0: CheckStackOverflow
    //     0xe635b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe635b4: cmp             SP, x16
    //     0xe635b8: b.ls            #0xe63640
    // 0xe635bc: LoadField: r3 = r1->field_b
    //     0xe635bc: ldur            w3, [x1, #0xb]
    // 0xe635c0: DecompressPointer r3
    //     0xe635c0: add             x3, x3, HEAP, lsl #32
    // 0xe635c4: stur            x3, [fp, #-8]
    // 0xe635c8: r0 = LoadClassIdInstr(r3)
    //     0xe635c8: ldur            x0, [x3, #-1]
    //     0xe635cc: ubfx            x0, x0, #0xc, #0x14
    // 0xe635d0: sub             x16, x0, #0x31a
    // 0xe635d4: cmp             x16, #0xf
    // 0xe635d8: b.hi            #0xe63634
    // 0xe635dc: mov             x0, x3
    // 0xe635e0: r2 = Null
    //     0xe635e0: mov             x2, NULL
    // 0xe635e4: r1 = Null
    //     0xe635e4: mov             x1, NULL
    // 0xe635e8: r4 = LoadClassIdInstr(r0)
    //     0xe635e8: ldur            x4, [x0, #-1]
    //     0xe635ec: ubfx            x4, x4, #0xc, #0x14
    // 0xe635f0: sub             x4, x4, #0x31a
    // 0xe635f4: cmp             x4, #0xf
    // 0xe635f8: b.ls            #0xe63610
    // 0xe635fc: r8 = SpanningWidget
    //     0xe635fc: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3dc98] Type: SpanningWidget
    //     0xe63600: ldr             x8, [x8, #0xc98]
    // 0xe63604: r3 = Null
    //     0xe63604: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dca0] Null
    //     0xe63608: ldr             x3, [x3, #0xca0]
    // 0xe6360c: r0 = DefaultTypeTest()
    //     0xe6360c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe63610: ldur            x1, [fp, #-8]
    // 0xe63614: r0 = LoadClassIdInstr(r1)
    //     0xe63614: ldur            x0, [x1, #-1]
    //     0xe63618: ubfx            x0, x0, #0xc, #0x14
    // 0xe6361c: r0 = GDT[cid_x0 + -0xe7b]()
    //     0xe6361c: sub             lr, x0, #0xe7b
    //     0xe63620: ldr             lr, [x21, lr, lsl #3]
    //     0xe63624: blr             lr
    // 0xe63628: LeaveFrame
    //     0xe63628: mov             SP, fp
    //     0xe6362c: ldp             fp, lr, [SP], #0x10
    // 0xe63630: ret
    //     0xe63630: ret             
    // 0xe63634: r0 = UnimplementedError()
    //     0xe63634: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xe63638: r0 = Throw()
    //     0xe63638: bl              #0xec04b8  ; ThrowStub
    // 0xe6363c: brk             #0
    // 0xe63640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe63640: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe63644: b               #0xe635bc
  }
  _ paintChild(/* No info */) {
    // ** addr: 0xe63858, size: 0xec
    // 0xe63858: EnterFrame
    //     0xe63858: stp             fp, lr, [SP, #-0x10]!
    //     0xe6385c: mov             fp, SP
    // 0xe63860: AllocStack(0x20)
    //     0xe63860: sub             SP, SP, #0x20
    // 0xe63864: SetupParameters(SingleChildWidget this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe63864: stur            x1, [fp, #-0x10]
    //     0xe63868: stur            x2, [fp, #-0x18]
    // 0xe6386c: CheckStackOverflow
    //     0xe6386c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe63870: cmp             SP, x16
    //     0xe63874: b.ls            #0xe63934
    // 0xe63878: LoadField: r0 = r1->field_b
    //     0xe63878: ldur            w0, [x1, #0xb]
    // 0xe6387c: DecompressPointer r0
    //     0xe6387c: add             x0, x0, HEAP, lsl #32
    // 0xe63880: stur            x0, [fp, #-8]
    // 0xe63884: cmp             w0, NULL
    // 0xe63888: b.eq            #0xe63924
    // 0xe6388c: r0 = Matrix4()
    //     0xe6388c: bl              #0x645164  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xe63890: r4 = 32
    //     0xe63890: movz            x4, #0x20
    // 0xe63894: stur            x0, [fp, #-0x20]
    // 0xe63898: r0 = AllocateFloat64Array()
    //     0xe63898: bl              #0xec193c  ; AllocateFloat64ArrayStub
    // 0xe6389c: mov             x1, x0
    // 0xe638a0: ldur            x0, [fp, #-0x20]
    // 0xe638a4: StoreField: r0->field_7 = r1
    //     0xe638a4: stur            w1, [x0, #7]
    // 0xe638a8: mov             x1, x0
    // 0xe638ac: r0 = setIdentity()
    //     0xe638ac: bl              #0x649400  ; [package:vector_math/vector_math_64.dart] Matrix4::setIdentity
    // 0xe638b0: ldur            x0, [fp, #-0x10]
    // 0xe638b4: LoadField: r1 = r0->field_7
    //     0xe638b4: ldur            w1, [x0, #7]
    // 0xe638b8: DecompressPointer r1
    //     0xe638b8: add             x1, x1, HEAP, lsl #32
    // 0xe638bc: cmp             w1, NULL
    // 0xe638c0: b.eq            #0xe6393c
    // 0xe638c4: LoadField: d0 = r1->field_7
    //     0xe638c4: ldur            d0, [x1, #7]
    // 0xe638c8: LoadField: d1 = r1->field_f
    //     0xe638c8: ldur            d1, [x1, #0xf]
    // 0xe638cc: ldur            x1, [fp, #-0x20]
    // 0xe638d0: r0 = translate()
    //     0xe638d0: bl              #0x78fdc8  ; [package:vector_math/vector_math_64.dart] Matrix4::translate
    // 0xe638d4: ldur            x2, [fp, #-0x18]
    // 0xe638d8: LoadField: r0 = r2->field_b
    //     0xe638d8: ldur            w0, [x2, #0xb]
    // 0xe638dc: DecompressPointer r0
    //     0xe638dc: add             x0, x0, HEAP, lsl #32
    // 0xe638e0: stur            x0, [fp, #-0x10]
    // 0xe638e4: cmp             w0, NULL
    // 0xe638e8: b.eq            #0xe63940
    // 0xe638ec: mov             x1, x0
    // 0xe638f0: r0 = saveContext()
    //     0xe638f0: bl              #0xe479f4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::saveContext
    // 0xe638f4: ldur            x1, [fp, #-0x10]
    // 0xe638f8: ldur            x2, [fp, #-0x20]
    // 0xe638fc: r0 = setTransform()
    //     0xe638fc: bl              #0xe473b4  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::setTransform
    // 0xe63900: ldur            x1, [fp, #-8]
    // 0xe63904: r0 = LoadClassIdInstr(r1)
    //     0xe63904: ldur            x0, [x1, #-1]
    //     0xe63908: ubfx            x0, x0, #0xc, #0x14
    // 0xe6390c: ldur            x2, [fp, #-0x18]
    // 0xe63910: r0 = GDT[cid_x0 + -0xe8b]()
    //     0xe63910: sub             lr, x0, #0xe8b
    //     0xe63914: ldr             lr, [x21, lr, lsl #3]
    //     0xe63918: blr             lr
    // 0xe6391c: ldur            x1, [fp, #-0x10]
    // 0xe63920: r0 = restoreContext()
    //     0xe63920: bl              #0xe46864  ; [package:pdf/src/pdf/graphics.dart] PdfGraphics::restoreContext
    // 0xe63924: r0 = Null
    //     0xe63924: mov             x0, NULL
    // 0xe63928: LeaveFrame
    //     0xe63928: mov             SP, fp
    //     0xe6392c: ldp             fp, lr, [SP], #0x10
    // 0xe63930: ret
    //     0xe63930: ret             
    // 0xe63934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe63934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe63938: b               #0xe63878
    // 0xe6393c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe6393c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe63940: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe63940: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ canSpan(/* No info */) {
    // ** addr: 0xe7d9fc, size: 0xa0
    // 0xe7d9fc: EnterFrame
    //     0xe7d9fc: stp             fp, lr, [SP, #-0x10]!
    //     0xe7da00: mov             fp, SP
    // 0xe7da04: AllocStack(0x8)
    //     0xe7da04: sub             SP, SP, #8
    // 0xe7da08: CheckStackOverflow
    //     0xe7da08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7da0c: cmp             SP, x16
    //     0xe7da10: b.ls            #0xe7da94
    // 0xe7da14: LoadField: r3 = r1->field_b
    //     0xe7da14: ldur            w3, [x1, #0xb]
    // 0xe7da18: DecompressPointer r3
    //     0xe7da18: add             x3, x3, HEAP, lsl #32
    // 0xe7da1c: stur            x3, [fp, #-8]
    // 0xe7da20: r0 = LoadClassIdInstr(r3)
    //     0xe7da20: ldur            x0, [x3, #-1]
    //     0xe7da24: ubfx            x0, x0, #0xc, #0x14
    // 0xe7da28: sub             x16, x0, #0x31a
    // 0xe7da2c: cmp             x16, #0xf
    // 0xe7da30: b.hi            #0xe7da84
    // 0xe7da34: mov             x0, x3
    // 0xe7da38: r2 = Null
    //     0xe7da38: mov             x2, NULL
    // 0xe7da3c: r1 = Null
    //     0xe7da3c: mov             x1, NULL
    // 0xe7da40: r4 = LoadClassIdInstr(r0)
    //     0xe7da40: ldur            x4, [x0, #-1]
    //     0xe7da44: ubfx            x4, x4, #0xc, #0x14
    // 0xe7da48: sub             x4, x4, #0x31a
    // 0xe7da4c: cmp             x4, #0xf
    // 0xe7da50: b.ls            #0xe7da68
    // 0xe7da54: r8 = SpanningWidget
    //     0xe7da54: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3dc98] Type: SpanningWidget
    //     0xe7da58: ldr             x8, [x8, #0xc98]
    // 0xe7da5c: r3 = Null
    //     0xe7da5c: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dcd0] Null
    //     0xe7da60: ldr             x3, [x3, #0xcd0]
    // 0xe7da64: r0 = DefaultTypeTest()
    //     0xe7da64: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe7da68: ldur            x1, [fp, #-8]
    // 0xe7da6c: r0 = LoadClassIdInstr(r1)
    //     0xe7da6c: ldur            x0, [x1, #-1]
    //     0xe7da70: ubfx            x0, x0, #0xc, #0x14
    // 0xe7da74: r0 = GDT[cid_x0 + -0xf00]()
    //     0xe7da74: sub             lr, x0, #0xf00
    //     0xe7da78: ldr             lr, [x21, lr, lsl #3]
    //     0xe7da7c: blr             lr
    // 0xe7da80: b               #0xe7da88
    // 0xe7da84: r0 = false
    //     0xe7da84: add             x0, NULL, #0x30  ; false
    // 0xe7da88: LeaveFrame
    //     0xe7da88: mov             SP, fp
    //     0xe7da8c: ldp             fp, lr, [SP], #0x10
    // 0xe7da90: ret
    //     0xe7da90: ret             
    // 0xe7da94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7da94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7da98: b               #0xe7da14
  }
  _ layout(/* No info */) {
    // ** addr: 0xe8f1bc, size: 0xd8
    // 0xe8f1bc: EnterFrame
    //     0xe8f1bc: stp             fp, lr, [SP, #-0x10]!
    //     0xe8f1c0: mov             fp, SP
    // 0xe8f1c4: AllocStack(0x10)
    //     0xe8f1c4: sub             SP, SP, #0x10
    // 0xe8f1c8: SetupParameters(SingleChildWidget this /* r1 => r4, fp-0x10 */)
    //     0xe8f1c8: mov             x4, x1
    //     0xe8f1cc: stur            x1, [fp, #-0x10]
    // 0xe8f1d0: CheckStackOverflow
    //     0xe8f1d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8f1d4: cmp             SP, x16
    //     0xe8f1d8: b.ls            #0xe8f28c
    // 0xe8f1dc: LoadField: r5 = r4->field_b
    //     0xe8f1dc: ldur            w5, [x4, #0xb]
    // 0xe8f1e0: DecompressPointer r5
    //     0xe8f1e0: add             x5, x5, HEAP, lsl #32
    // 0xe8f1e4: stur            x5, [fp, #-8]
    // 0xe8f1e8: cmp             w5, NULL
    // 0xe8f1ec: b.eq            #0xe8f23c
    // 0xe8f1f0: r0 = LoadClassIdInstr(r5)
    //     0xe8f1f0: ldur            x0, [x5, #-1]
    //     0xe8f1f4: ubfx            x0, x0, #0xc, #0x14
    // 0xe8f1f8: mov             x1, x5
    // 0xe8f1fc: r0 = GDT[cid_x0 + -0xf89]()
    //     0xe8f1fc: sub             lr, x0, #0xf89
    //     0xe8f200: ldr             lr, [x21, lr, lsl #3]
    //     0xe8f204: blr             lr
    // 0xe8f208: ldur            x0, [fp, #-8]
    // 0xe8f20c: LoadField: r1 = r0->field_7
    //     0xe8f20c: ldur            w1, [x0, #7]
    // 0xe8f210: DecompressPointer r1
    //     0xe8f210: add             x1, x1, HEAP, lsl #32
    // 0xe8f214: mov             x0, x1
    // 0xe8f218: ldur            x2, [fp, #-0x10]
    // 0xe8f21c: StoreField: r2->field_7 = r0
    //     0xe8f21c: stur            w0, [x2, #7]
    //     0xe8f220: ldurb           w16, [x2, #-1]
    //     0xe8f224: ldurb           w17, [x0, #-1]
    //     0xe8f228: and             x16, x17, x16, lsr #2
    //     0xe8f22c: tst             x16, HEAP, lsr #32
    //     0xe8f230: b.eq            #0xe8f238
    //     0xe8f234: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe8f238: b               #0xe8f27c
    // 0xe8f23c: mov             x2, x4
    // 0xe8f240: mov             x1, x3
    // 0xe8f244: r0 = smallest()
    //     0xe8f244: bl              #0xe8edec  ; [package:pdf/src/widgets/geometry.dart] BoxConstraints::smallest
    // 0xe8f248: mov             x3, x0
    // 0xe8f24c: r1 = Null
    //     0xe8f24c: mov             x1, NULL
    // 0xe8f250: r2 = Instance_PdfPoint
    //     0xe8f250: add             x2, PP, #0x36, lsl #12  ; [pp+0x36730] Obj!PdfPoint@e0c6f1
    //     0xe8f254: ldr             x2, [x2, #0x730]
    // 0xe8f258: r0 = PdfRect.fromPoints()
    //     0xe8f258: bl              #0xe68e78  ; [package:pdf/src/pdf/rect.dart] PdfRect::PdfRect.fromPoints
    // 0xe8f25c: ldur            x1, [fp, #-0x10]
    // 0xe8f260: StoreField: r1->field_7 = r0
    //     0xe8f260: stur            w0, [x1, #7]
    //     0xe8f264: ldurb           w16, [x1, #-1]
    //     0xe8f268: ldurb           w17, [x0, #-1]
    //     0xe8f26c: and             x16, x17, x16, lsr #2
    //     0xe8f270: tst             x16, HEAP, lsr #32
    //     0xe8f274: b.eq            #0xe8f27c
    //     0xe8f278: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8f27c: r0 = Null
    //     0xe8f27c: mov             x0, NULL
    // 0xe8f280: LeaveFrame
    //     0xe8f280: mov             SP, fp
    //     0xe8f284: ldp             fp, lr, [SP], #0x10
    // 0xe8f288: ret
    //     0xe8f288: ret             
    // 0xe8f28c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8f28c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8f290: b               #0xe8f1dc
  }
}
