// lib: , url: package:percent_indicator/linear_percent_indicator.dart

// class id: 1050866, size: 0x8
class :: {
}

// class id: 4102, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __LinearPercentIndicatorState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ dispose(/* No info */) {
    // ** addr: 0xa83afc, size: 0x94
    // 0xa83afc: EnterFrame
    //     0xa83afc: stp             fp, lr, [SP, #-0x10]!
    //     0xa83b00: mov             fp, SP
    // 0xa83b04: AllocStack(0x10)
    //     0xa83b04: sub             SP, SP, #0x10
    // 0xa83b08: SetupParameters(__LinearPercentIndicatorState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa83b08: mov             x0, x1
    //     0xa83b0c: stur            x1, [fp, #-0x10]
    // 0xa83b10: CheckStackOverflow
    //     0xa83b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83b14: cmp             SP, x16
    //     0xa83b18: b.ls            #0xa83b88
    // 0xa83b1c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa83b1c: ldur            w3, [x0, #0x17]
    // 0xa83b20: DecompressPointer r3
    //     0xa83b20: add             x3, x3, HEAP, lsl #32
    // 0xa83b24: stur            x3, [fp, #-8]
    // 0xa83b28: cmp             w3, NULL
    // 0xa83b2c: b.ne            #0xa83b38
    // 0xa83b30: mov             x1, x0
    // 0xa83b34: b               #0xa83b74
    // 0xa83b38: mov             x2, x0
    // 0xa83b3c: r1 = Function '_updateTicker@364311458':.
    //     0xa83b3c: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a468] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa83b40: ldr             x1, [x1, #0x468]
    // 0xa83b44: r0 = AllocateClosure()
    //     0xa83b44: bl              #0xec1630  ; AllocateClosureStub
    // 0xa83b48: ldur            x1, [fp, #-8]
    // 0xa83b4c: r2 = LoadClassIdInstr(r1)
    //     0xa83b4c: ldur            x2, [x1, #-1]
    //     0xa83b50: ubfx            x2, x2, #0xc, #0x14
    // 0xa83b54: mov             x16, x0
    // 0xa83b58: mov             x0, x2
    // 0xa83b5c: mov             x2, x16
    // 0xa83b60: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa83b60: movz            x17, #0xbf5c
    //     0xa83b64: add             lr, x0, x17
    //     0xa83b68: ldr             lr, [x21, lr, lsl #3]
    //     0xa83b6c: blr             lr
    // 0xa83b70: ldur            x1, [fp, #-0x10]
    // 0xa83b74: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa83b74: stur            NULL, [x1, #0x17]
    // 0xa83b78: r0 = Null
    //     0xa83b78: mov             x0, NULL
    // 0xa83b7c: LeaveFrame
    //     0xa83b7c: mov             SP, fp
    //     0xa83b80: ldp             fp, lr, [SP], #0x10
    // 0xa83b84: ret
    //     0xa83b84: ret             
    // 0xa83b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83b88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83b8c: b               #0xa83b1c
  }
  _ activate(/* No info */) {
    // ** addr: 0xa85ca8, size: 0x30
    // 0xa85ca8: EnterFrame
    //     0xa85ca8: stp             fp, lr, [SP, #-0x10]!
    //     0xa85cac: mov             fp, SP
    // 0xa85cb0: CheckStackOverflow
    //     0xa85cb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85cb4: cmp             SP, x16
    //     0xa85cb8: b.ls            #0xa85cd0
    // 0xa85cbc: r0 = _updateTickerModeNotifier()
    //     0xa85cbc: bl              #0xa85cd8  ; [package:percent_indicator/linear_percent_indicator.dart] __LinearPercentIndicatorState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa85cc0: r0 = Null
    //     0xa85cc0: mov             x0, NULL
    // 0xa85cc4: LeaveFrame
    //     0xa85cc4: mov             SP, fp
    //     0xa85cc8: ldp             fp, lr, [SP], #0x10
    // 0xa85ccc: ret
    //     0xa85ccc: ret             
    // 0xa85cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85cd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85cd4: b               #0xa85cbc
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0xa85cd8, size: 0x124
    // 0xa85cd8: EnterFrame
    //     0xa85cd8: stp             fp, lr, [SP, #-0x10]!
    //     0xa85cdc: mov             fp, SP
    // 0xa85ce0: AllocStack(0x18)
    //     0xa85ce0: sub             SP, SP, #0x18
    // 0xa85ce4: SetupParameters(__LinearPercentIndicatorState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0xa85ce4: mov             x2, x1
    //     0xa85ce8: stur            x1, [fp, #-8]
    // 0xa85cec: CheckStackOverflow
    //     0xa85cec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85cf0: cmp             SP, x16
    //     0xa85cf4: b.ls            #0xa85df0
    // 0xa85cf8: LoadField: r1 = r2->field_f
    //     0xa85cf8: ldur            w1, [x2, #0xf]
    // 0xa85cfc: DecompressPointer r1
    //     0xa85cfc: add             x1, x1, HEAP, lsl #32
    // 0xa85d00: cmp             w1, NULL
    // 0xa85d04: b.eq            #0xa85df8
    // 0xa85d08: r0 = getNotifier()
    //     0xa85d08: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0xa85d0c: mov             x3, x0
    // 0xa85d10: ldur            x0, [fp, #-8]
    // 0xa85d14: stur            x3, [fp, #-0x18]
    // 0xa85d18: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa85d18: ldur            w4, [x0, #0x17]
    // 0xa85d1c: DecompressPointer r4
    //     0xa85d1c: add             x4, x4, HEAP, lsl #32
    // 0xa85d20: stur            x4, [fp, #-0x10]
    // 0xa85d24: cmp             w3, w4
    // 0xa85d28: b.ne            #0xa85d3c
    // 0xa85d2c: r0 = Null
    //     0xa85d2c: mov             x0, NULL
    // 0xa85d30: LeaveFrame
    //     0xa85d30: mov             SP, fp
    //     0xa85d34: ldp             fp, lr, [SP], #0x10
    // 0xa85d38: ret
    //     0xa85d38: ret             
    // 0xa85d3c: cmp             w4, NULL
    // 0xa85d40: b.eq            #0xa85d84
    // 0xa85d44: mov             x2, x0
    // 0xa85d48: r1 = Function '_updateTicker@364311458':.
    //     0xa85d48: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a468] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa85d4c: ldr             x1, [x1, #0x468]
    // 0xa85d50: r0 = AllocateClosure()
    //     0xa85d50: bl              #0xec1630  ; AllocateClosureStub
    // 0xa85d54: ldur            x1, [fp, #-0x10]
    // 0xa85d58: r2 = LoadClassIdInstr(r1)
    //     0xa85d58: ldur            x2, [x1, #-1]
    //     0xa85d5c: ubfx            x2, x2, #0xc, #0x14
    // 0xa85d60: mov             x16, x0
    // 0xa85d64: mov             x0, x2
    // 0xa85d68: mov             x2, x16
    // 0xa85d6c: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa85d6c: movz            x17, #0xbf5c
    //     0xa85d70: add             lr, x0, x17
    //     0xa85d74: ldr             lr, [x21, lr, lsl #3]
    //     0xa85d78: blr             lr
    // 0xa85d7c: ldur            x0, [fp, #-8]
    // 0xa85d80: ldur            x3, [fp, #-0x18]
    // 0xa85d84: mov             x2, x0
    // 0xa85d88: r1 = Function '_updateTicker@364311458':.
    //     0xa85d88: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a468] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa85d8c: ldr             x1, [x1, #0x468]
    // 0xa85d90: r0 = AllocateClosure()
    //     0xa85d90: bl              #0xec1630  ; AllocateClosureStub
    // 0xa85d94: ldur            x3, [fp, #-0x18]
    // 0xa85d98: r1 = LoadClassIdInstr(r3)
    //     0xa85d98: ldur            x1, [x3, #-1]
    //     0xa85d9c: ubfx            x1, x1, #0xc, #0x14
    // 0xa85da0: mov             x2, x0
    // 0xa85da4: mov             x0, x1
    // 0xa85da8: mov             x1, x3
    // 0xa85dac: r0 = GDT[cid_x0 + 0xc407]()
    //     0xa85dac: movz            x17, #0xc407
    //     0xa85db0: add             lr, x0, x17
    //     0xa85db4: ldr             lr, [x21, lr, lsl #3]
    //     0xa85db8: blr             lr
    // 0xa85dbc: ldur            x0, [fp, #-0x18]
    // 0xa85dc0: ldur            x1, [fp, #-8]
    // 0xa85dc4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa85dc4: stur            w0, [x1, #0x17]
    //     0xa85dc8: ldurb           w16, [x1, #-1]
    //     0xa85dcc: ldurb           w17, [x0, #-1]
    //     0xa85dd0: and             x16, x17, x16, lsr #2
    //     0xa85dd4: tst             x16, HEAP, lsr #32
    //     0xa85dd8: b.eq            #0xa85de0
    //     0xa85ddc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa85de0: r0 = Null
    //     0xa85de0: mov             x0, NULL
    // 0xa85de4: LeaveFrame
    //     0xa85de4: mov             SP, fp
    //     0xa85de8: ldp             fp, lr, [SP], #0x10
    // 0xa85dec: ret
    //     0xa85dec: ret             
    // 0xa85df0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85df0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85df4: b               #0xa85cf8
    // 0xa85df8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa85df8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4103, size: 0x20, field offset: 0x1c
//   transformed mixin,
abstract class __LinearPercentIndicatorState&State&SingleTickerProviderStateMixin&AutomaticKeepAliveClientMixin extends __LinearPercentIndicatorState&State&SingleTickerProviderStateMixin
     with AutomaticKeepAliveClientMixin<X0 bound StatefulWidget> {

  _ deactivate(/* No info */) {
    // ** addr: 0x92bb6c, size: 0x40
    // 0x92bb6c: EnterFrame
    //     0x92bb6c: stp             fp, lr, [SP, #-0x10]!
    //     0x92bb70: mov             fp, SP
    // 0x92bb74: CheckStackOverflow
    //     0x92bb74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92bb78: cmp             SP, x16
    //     0x92bb7c: b.ls            #0x92bba4
    // 0x92bb80: LoadField: r0 = r1->field_1b
    //     0x92bb80: ldur            w0, [x1, #0x1b]
    // 0x92bb84: DecompressPointer r0
    //     0x92bb84: add             x0, x0, HEAP, lsl #32
    // 0x92bb88: cmp             w0, NULL
    // 0x92bb8c: b.eq            #0x92bb94
    // 0x92bb90: r0 = _releaseKeepAlive()
    //     0x92bb90: bl              #0x92b5b4  ; [package:flutter/src/widgets/dismissible.dart] __DismissibleState&State&TickerProviderStateMixin&AutomaticKeepAliveClientMixin::_releaseKeepAlive
    // 0x92bb94: r0 = Null
    //     0x92bb94: mov             x0, NULL
    // 0x92bb98: LeaveFrame
    //     0x92bb98: mov             SP, fp
    //     0x92bb9c: ldp             fp, lr, [SP], #0x10
    // 0x92bba0: ret
    //     0x92bba0: ret             
    // 0x92bba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92bba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92bba8: b               #0x92bb80
  }
  _ initState(/* No info */) {
    // ** addr: 0x97e35c, size: 0x44
    // 0x97e35c: EnterFrame
    //     0x97e35c: stp             fp, lr, [SP, #-0x10]!
    //     0x97e360: mov             fp, SP
    // 0x97e364: CheckStackOverflow
    //     0x97e364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e368: cmp             SP, x16
    //     0x97e36c: b.ls            #0x97e394
    // 0x97e370: LoadField: r0 = r1->field_b
    //     0x97e370: ldur            w0, [x1, #0xb]
    // 0x97e374: DecompressPointer r0
    //     0x97e374: add             x0, x0, HEAP, lsl #32
    // 0x97e378: cmp             w0, NULL
    // 0x97e37c: b.eq            #0x97e39c
    // 0x97e380: r0 = _ensureKeepAlive()
    //     0x97e380: bl              #0x93ef98  ; [package:flutter/src/widgets/dismissible.dart] __DismissibleState&State&TickerProviderStateMixin&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0x97e384: r0 = Null
    //     0x97e384: mov             x0, NULL
    // 0x97e388: LeaveFrame
    //     0x97e388: mov             SP, fp
    //     0x97e38c: ldp             fp, lr, [SP], #0x10
    // 0x97e390: ret
    //     0x97e390: ret             
    // 0x97e394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e394: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e398: b               #0x97e370
    // 0x97e39c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97e39c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa44118, size: 0x58
    // 0xa44118: EnterFrame
    //     0xa44118: stp             fp, lr, [SP, #-0x10]!
    //     0xa4411c: mov             fp, SP
    // 0xa44120: CheckStackOverflow
    //     0xa44120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44124: cmp             SP, x16
    //     0xa44128: b.ls            #0xa44164
    // 0xa4412c: LoadField: r0 = r1->field_b
    //     0xa4412c: ldur            w0, [x1, #0xb]
    // 0xa44130: DecompressPointer r0
    //     0xa44130: add             x0, x0, HEAP, lsl #32
    // 0xa44134: cmp             w0, NULL
    // 0xa44138: b.eq            #0xa4416c
    // 0xa4413c: LoadField: r0 = r1->field_1b
    //     0xa4413c: ldur            w0, [x1, #0x1b]
    // 0xa44140: DecompressPointer r0
    //     0xa44140: add             x0, x0, HEAP, lsl #32
    // 0xa44144: cmp             w0, NULL
    // 0xa44148: b.ne            #0xa44150
    // 0xa4414c: r0 = _ensureKeepAlive()
    //     0xa4414c: bl              #0x93ef98  ; [package:flutter/src/widgets/dismissible.dart] __DismissibleState&State&TickerProviderStateMixin&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0xa44150: r0 = Instance__NullWidget
    //     0xa44150: add             x0, PP, #0x40, lsl #12  ; [pp+0x407d8] Obj!_NullWidget@e25381
    //     0xa44154: ldr             x0, [x0, #0x7d8]
    // 0xa44158: LeaveFrame
    //     0xa44158: mov             SP, fp
    //     0xa4415c: ldp             fp, lr, [SP], #0x10
    // 0xa44160: ret
    //     0xa44160: ret             
    // 0xa44164: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44164: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44168: b               #0xa4412c
    // 0xa4416c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4416c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4104, size: 0x48, field offset: 0x20
class _LinearPercentIndicatorState extends __LinearPercentIndicatorState&State&SingleTickerProviderStateMixin&AutomaticKeepAliveClientMixin {

  _ initState(/* No info */) {
    // ** addr: 0x97e204, size: 0x158
    // 0x97e204: EnterFrame
    //     0x97e204: stp             fp, lr, [SP, #-0x10]!
    //     0x97e208: mov             fp, SP
    // 0x97e20c: AllocStack(0x20)
    //     0x97e20c: sub             SP, SP, #0x20
    // 0x97e210: SetupParameters(_LinearPercentIndicatorState this /* r1 => r1, fp-0x8 */)
    //     0x97e210: stur            x1, [fp, #-8]
    // 0x97e214: CheckStackOverflow
    //     0x97e214: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e218: cmp             SP, x16
    //     0x97e21c: b.ls            #0x97e34c
    // 0x97e220: r1 = 1
    //     0x97e220: movz            x1, #0x1
    // 0x97e224: r0 = AllocateContext()
    //     0x97e224: bl              #0xec126c  ; AllocateContextStub
    // 0x97e228: mov             x1, x0
    // 0x97e22c: ldur            x0, [fp, #-8]
    // 0x97e230: StoreField: r1->field_f = r0
    //     0x97e230: stur            w0, [x1, #0xf]
    // 0x97e234: r2 = LoadStaticField(0x7d4)
    //     0x97e234: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x97e238: ldr             x2, [x2, #0xfa8]
    // 0x97e23c: cmp             w2, NULL
    // 0x97e240: b.eq            #0x97e354
    // 0x97e244: LoadField: r3 = r2->field_53
    //     0x97e244: ldur            w3, [x2, #0x53]
    // 0x97e248: DecompressPointer r3
    //     0x97e248: add             x3, x3, HEAP, lsl #32
    // 0x97e24c: stur            x3, [fp, #-0x18]
    // 0x97e250: LoadField: r4 = r3->field_7
    //     0x97e250: ldur            w4, [x3, #7]
    // 0x97e254: DecompressPointer r4
    //     0x97e254: add             x4, x4, HEAP, lsl #32
    // 0x97e258: mov             x2, x1
    // 0x97e25c: stur            x4, [fp, #-0x10]
    // 0x97e260: r1 = Function '<anonymous closure>':.
    //     0x97e260: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a4c0] AnonymousClosure: (0x97e444), in [package:percent_indicator/linear_percent_indicator.dart] _LinearPercentIndicatorState::initState (0x97e204)
    //     0x97e264: ldr             x1, [x1, #0x4c0]
    // 0x97e268: r0 = AllocateClosure()
    //     0x97e268: bl              #0xec1630  ; AllocateClosureStub
    // 0x97e26c: ldur            x2, [fp, #-0x10]
    // 0x97e270: mov             x3, x0
    // 0x97e274: r1 = Null
    //     0x97e274: mov             x1, NULL
    // 0x97e278: stur            x3, [fp, #-0x10]
    // 0x97e27c: cmp             w2, NULL
    // 0x97e280: b.eq            #0x97e2a0
    // 0x97e284: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x97e284: ldur            w4, [x2, #0x17]
    // 0x97e288: DecompressPointer r4
    //     0x97e288: add             x4, x4, HEAP, lsl #32
    // 0x97e28c: r8 = X0
    //     0x97e28c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x97e290: LoadField: r9 = r4->field_7
    //     0x97e290: ldur            x9, [x4, #7]
    // 0x97e294: r3 = Null
    //     0x97e294: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a4c8] Null
    //     0x97e298: ldr             x3, [x3, #0x4c8]
    // 0x97e29c: blr             x9
    // 0x97e2a0: ldur            x0, [fp, #-0x18]
    // 0x97e2a4: LoadField: r1 = r0->field_b
    //     0x97e2a4: ldur            w1, [x0, #0xb]
    // 0x97e2a8: LoadField: r2 = r0->field_f
    //     0x97e2a8: ldur            w2, [x0, #0xf]
    // 0x97e2ac: DecompressPointer r2
    //     0x97e2ac: add             x2, x2, HEAP, lsl #32
    // 0x97e2b0: LoadField: r3 = r2->field_b
    //     0x97e2b0: ldur            w3, [x2, #0xb]
    // 0x97e2b4: r2 = LoadInt32Instr(r1)
    //     0x97e2b4: sbfx            x2, x1, #1, #0x1f
    // 0x97e2b8: stur            x2, [fp, #-0x20]
    // 0x97e2bc: r1 = LoadInt32Instr(r3)
    //     0x97e2bc: sbfx            x1, x3, #1, #0x1f
    // 0x97e2c0: cmp             x2, x1
    // 0x97e2c4: b.ne            #0x97e2d0
    // 0x97e2c8: mov             x1, x0
    // 0x97e2cc: r0 = _growToNextCapacity()
    //     0x97e2cc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x97e2d0: ldur            x3, [fp, #-8]
    // 0x97e2d4: ldur            x0, [fp, #-0x18]
    // 0x97e2d8: ldur            x2, [fp, #-0x20]
    // 0x97e2dc: add             x1, x2, #1
    // 0x97e2e0: lsl             x4, x1, #1
    // 0x97e2e4: StoreField: r0->field_b = r4
    //     0x97e2e4: stur            w4, [x0, #0xb]
    // 0x97e2e8: LoadField: r1 = r0->field_f
    //     0x97e2e8: ldur            w1, [x0, #0xf]
    // 0x97e2ec: DecompressPointer r1
    //     0x97e2ec: add             x1, x1, HEAP, lsl #32
    // 0x97e2f0: ldur            x0, [fp, #-0x10]
    // 0x97e2f4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x97e2f4: add             x25, x1, x2, lsl #2
    //     0x97e2f8: add             x25, x25, #0xf
    //     0x97e2fc: str             w0, [x25]
    //     0x97e300: tbz             w0, #0, #0x97e31c
    //     0x97e304: ldurb           w16, [x1, #-1]
    //     0x97e308: ldurb           w17, [x0, #-1]
    //     0x97e30c: and             x16, x17, x16, lsr #2
    //     0x97e310: tst             x16, HEAP, lsr #32
    //     0x97e314: b.eq            #0x97e31c
    //     0x97e318: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97e31c: LoadField: r0 = r3->field_b
    //     0x97e31c: ldur            w0, [x3, #0xb]
    // 0x97e320: DecompressPointer r0
    //     0x97e320: add             x0, x0, HEAP, lsl #32
    // 0x97e324: cmp             w0, NULL
    // 0x97e328: b.eq            #0x97e358
    // 0x97e32c: mov             x1, x3
    // 0x97e330: r0 = _updateProgress()
    //     0x97e330: bl              #0x97e3a0  ; [package:percent_indicator/linear_percent_indicator.dart] _LinearPercentIndicatorState::_updateProgress
    // 0x97e334: ldur            x1, [fp, #-8]
    // 0x97e338: r0 = initState()
    //     0x97e338: bl              #0x97e35c  ; [package:percent_indicator/linear_percent_indicator.dart] __LinearPercentIndicatorState&State&SingleTickerProviderStateMixin&AutomaticKeepAliveClientMixin::initState
    // 0x97e33c: r0 = Null
    //     0x97e33c: mov             x0, NULL
    // 0x97e340: LeaveFrame
    //     0x97e340: mov             SP, fp
    //     0x97e344: ldp             fp, lr, [SP], #0x10
    // 0x97e348: ret
    //     0x97e348: ret             
    // 0x97e34c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e34c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e350: b               #0x97e220
    // 0x97e354: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97e354: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97e358: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97e358: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateProgress(/* No info */) {
    // ** addr: 0x97e3a0, size: 0x64
    // 0x97e3a0: EnterFrame
    //     0x97e3a0: stp             fp, lr, [SP, #-0x10]!
    //     0x97e3a4: mov             fp, SP
    // 0x97e3a8: AllocStack(0x8)
    //     0x97e3a8: sub             SP, SP, #8
    // 0x97e3ac: SetupParameters(_LinearPercentIndicatorState this /* r1 => r1, fp-0x8 */)
    //     0x97e3ac: stur            x1, [fp, #-8]
    // 0x97e3b0: CheckStackOverflow
    //     0x97e3b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e3b4: cmp             SP, x16
    //     0x97e3b8: b.ls            #0x97e3fc
    // 0x97e3bc: r1 = 1
    //     0x97e3bc: movz            x1, #0x1
    // 0x97e3c0: r0 = AllocateContext()
    //     0x97e3c0: bl              #0xec126c  ; AllocateContextStub
    // 0x97e3c4: mov             x1, x0
    // 0x97e3c8: ldur            x0, [fp, #-8]
    // 0x97e3cc: StoreField: r1->field_f = r0
    //     0x97e3cc: stur            w0, [x1, #0xf]
    // 0x97e3d0: mov             x2, x1
    // 0x97e3d4: r1 = Function '<anonymous closure>':.
    //     0x97e3d4: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a4b8] AnonymousClosure: (0x97e404), in [package:percent_indicator/linear_percent_indicator.dart] _LinearPercentIndicatorState::_updateProgress (0x97e3a0)
    //     0x97e3d8: ldr             x1, [x1, #0x4b8]
    // 0x97e3dc: r0 = AllocateClosure()
    //     0x97e3dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x97e3e0: ldur            x1, [fp, #-8]
    // 0x97e3e4: mov             x2, x0
    // 0x97e3e8: r0 = setState()
    //     0x97e3e8: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x97e3ec: r0 = Null
    //     0x97e3ec: mov             x0, NULL
    // 0x97e3f0: LeaveFrame
    //     0x97e3f0: mov             SP, fp
    //     0x97e3f4: ldp             fp, lr, [SP], #0x10
    // 0x97e3f8: ret
    //     0x97e3f8: ret             
    // 0x97e3fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e3fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e400: b               #0x97e3bc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x97e404, size: 0x40
    // 0x97e404: ldr             x1, [SP]
    // 0x97e408: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x97e408: ldur            w2, [x1, #0x17]
    // 0x97e40c: DecompressPointer r2
    //     0x97e40c: add             x2, x2, HEAP, lsl #32
    // 0x97e410: LoadField: r1 = r2->field_f
    //     0x97e410: ldur            w1, [x2, #0xf]
    // 0x97e414: DecompressPointer r1
    //     0x97e414: add             x1, x1, HEAP, lsl #32
    // 0x97e418: LoadField: r2 = r1->field_b
    //     0x97e418: ldur            w2, [x1, #0xb]
    // 0x97e41c: DecompressPointer r2
    //     0x97e41c: add             x2, x2, HEAP, lsl #32
    // 0x97e420: cmp             w2, NULL
    // 0x97e424: b.eq            #0x97e438
    // 0x97e428: LoadField: d0 = r2->field_b
    //     0x97e428: ldur            d0, [x2, #0xb]
    // 0x97e42c: StoreField: r1->field_27 = d0
    //     0x97e42c: stur            d0, [x1, #0x27]
    // 0x97e430: r0 = Null
    //     0x97e430: mov             x0, NULL
    // 0x97e434: ret
    //     0x97e434: ret             
    // 0x97e438: EnterFrame
    //     0x97e438: stp             fp, lr, [SP, #-0x10]!
    //     0x97e43c: mov             fp, SP
    // 0x97e440: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97e440: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x97e444, size: 0x70
    // 0x97e444: EnterFrame
    //     0x97e444: stp             fp, lr, [SP, #-0x10]!
    //     0x97e448: mov             fp, SP
    // 0x97e44c: AllocStack(0x8)
    //     0x97e44c: sub             SP, SP, #8
    // 0x97e450: SetupParameters()
    //     0x97e450: ldr             x0, [fp, #0x18]
    //     0x97e454: ldur            w2, [x0, #0x17]
    //     0x97e458: add             x2, x2, HEAP, lsl #32
    // 0x97e45c: CheckStackOverflow
    //     0x97e45c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e460: cmp             SP, x16
    //     0x97e464: b.ls            #0x97e4ac
    // 0x97e468: LoadField: r0 = r2->field_f
    //     0x97e468: ldur            w0, [x2, #0xf]
    // 0x97e46c: DecompressPointer r0
    //     0x97e46c: add             x0, x0, HEAP, lsl #32
    // 0x97e470: stur            x0, [fp, #-8]
    // 0x97e474: LoadField: r1 = r0->field_f
    //     0x97e474: ldur            w1, [x0, #0xf]
    // 0x97e478: DecompressPointer r1
    //     0x97e478: add             x1, x1, HEAP, lsl #32
    // 0x97e47c: cmp             w1, NULL
    // 0x97e480: b.eq            #0x97e49c
    // 0x97e484: r1 = Function '<anonymous closure>':.
    //     0x97e484: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a4d8] AnonymousClosure: (0x97e4b4), in [package:percent_indicator/linear_percent_indicator.dart] _LinearPercentIndicatorState::initState (0x97e204)
    //     0x97e488: ldr             x1, [x1, #0x4d8]
    // 0x97e48c: r0 = AllocateClosure()
    //     0x97e48c: bl              #0xec1630  ; AllocateClosureStub
    // 0x97e490: ldur            x1, [fp, #-8]
    // 0x97e494: mov             x2, x0
    // 0x97e498: r0 = setState()
    //     0x97e498: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x97e49c: r0 = Null
    //     0x97e49c: mov             x0, NULL
    // 0x97e4a0: LeaveFrame
    //     0x97e4a0: mov             SP, fp
    //     0x97e4a4: ldp             fp, lr, [SP], #0x10
    // 0x97e4a8: ret
    //     0x97e4a8: ret             
    // 0x97e4ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e4ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e4b0: b               #0x97e468
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x97e4b4, size: 0x294
    // 0x97e4b4: EnterFrame
    //     0x97e4b4: stp             fp, lr, [SP, #-0x10]!
    //     0x97e4b8: mov             fp, SP
    // 0x97e4bc: AllocStack(0x10)
    //     0x97e4bc: sub             SP, SP, #0x10
    // 0x97e4c0: SetupParameters()
    //     0x97e4c0: ldr             x0, [fp, #0x10]
    //     0x97e4c4: ldur            w2, [x0, #0x17]
    //     0x97e4c8: add             x2, x2, HEAP, lsl #32
    //     0x97e4cc: stur            x2, [fp, #-0x10]
    // 0x97e4d0: CheckStackOverflow
    //     0x97e4d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e4d4: cmp             SP, x16
    //     0x97e4d8: b.ls            #0x97e720
    // 0x97e4dc: LoadField: r0 = r2->field_f
    //     0x97e4dc: ldur            w0, [x2, #0xf]
    // 0x97e4e0: DecompressPointer r0
    //     0x97e4e0: add             x0, x0, HEAP, lsl #32
    // 0x97e4e4: stur            x0, [fp, #-8]
    // 0x97e4e8: LoadField: r1 = r0->field_2f
    //     0x97e4e8: ldur            w1, [x0, #0x2f]
    // 0x97e4ec: DecompressPointer r1
    //     0x97e4ec: add             x1, x1, HEAP, lsl #32
    // 0x97e4f0: r0 = _currentElement()
    //     0x97e4f0: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x97e4f4: cmp             w0, NULL
    // 0x97e4f8: b.ne            #0x97e504
    // 0x97e4fc: r0 = Null
    //     0x97e4fc: mov             x0, NULL
    // 0x97e500: b               #0x97e56c
    // 0x97e504: mov             x1, x0
    // 0x97e508: r0 = findRenderObject()
    //     0x97e508: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0x97e50c: r1 = LoadClassIdInstr(r0)
    //     0x97e50c: ldur            x1, [x0, #-1]
    //     0x97e510: ubfx            x1, x1, #0xc, #0x14
    // 0x97e514: sub             x16, x1, #0xbba
    // 0x97e518: cmp             x16, #0x9a
    // 0x97e51c: b.hi            #0x97e52c
    // 0x97e520: mov             x1, x0
    // 0x97e524: r0 = size()
    //     0x97e524: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x97e528: b               #0x97e530
    // 0x97e52c: r0 = Null
    //     0x97e52c: mov             x0, NULL
    // 0x97e530: cmp             w0, NULL
    // 0x97e534: b.ne            #0x97e540
    // 0x97e538: r0 = Null
    //     0x97e538: mov             x0, NULL
    // 0x97e53c: b               #0x97e56c
    // 0x97e540: LoadField: d0 = r0->field_7
    //     0x97e540: ldur            d0, [x0, #7]
    // 0x97e544: r0 = inline_Allocate_Double()
    //     0x97e544: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x97e548: add             x0, x0, #0x10
    //     0x97e54c: cmp             x1, x0
    //     0x97e550: b.ls            #0x97e728
    //     0x97e554: str             x0, [THR, #0x50]  ; THR::top
    //     0x97e558: sub             x0, x0, #0xf
    //     0x97e55c: movz            x1, #0xe15c
    //     0x97e560: movk            x1, #0x3, lsl #16
    //     0x97e564: stur            x1, [x0, #-1]
    // 0x97e568: StoreField: r0->field_7 = d0
    //     0x97e568: stur            d0, [x0, #7]
    // 0x97e56c: cmp             w0, NULL
    // 0x97e570: b.ne            #0x97e57c
    // 0x97e574: d0 = 0.000000
    //     0x97e574: eor             v0.16b, v0.16b, v0.16b
    // 0x97e578: b               #0x97e580
    // 0x97e57c: LoadField: d0 = r0->field_7
    //     0x97e57c: ldur            d0, [x0, #7]
    // 0x97e580: ldur            x0, [fp, #-0x10]
    // 0x97e584: ldur            x1, [fp, #-8]
    // 0x97e588: StoreField: r1->field_37 = d0
    //     0x97e588: stur            d0, [x1, #0x37]
    // 0x97e58c: LoadField: r1 = r0->field_f
    //     0x97e58c: ldur            w1, [x0, #0xf]
    // 0x97e590: DecompressPointer r1
    //     0x97e590: add             x1, x1, HEAP, lsl #32
    // 0x97e594: LoadField: r2 = r1->field_2f
    //     0x97e594: ldur            w2, [x1, #0x2f]
    // 0x97e598: DecompressPointer r2
    //     0x97e598: add             x2, x2, HEAP, lsl #32
    // 0x97e59c: mov             x1, x2
    // 0x97e5a0: r0 = _currentElement()
    //     0x97e5a0: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x97e5a4: cmp             w0, NULL
    // 0x97e5a8: b.eq            #0x97e5e0
    // 0x97e5ac: mov             x1, x0
    // 0x97e5b0: r0 = findRenderObject()
    //     0x97e5b0: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0x97e5b4: r1 = LoadClassIdInstr(r0)
    //     0x97e5b4: ldur            x1, [x0, #-1]
    //     0x97e5b8: ubfx            x1, x1, #0xc, #0x14
    // 0x97e5bc: sub             x16, x1, #0xbba
    // 0x97e5c0: cmp             x16, #0x9a
    // 0x97e5c4: b.hi            #0x97e5d4
    // 0x97e5c8: mov             x1, x0
    // 0x97e5cc: r0 = size()
    //     0x97e5cc: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x97e5d0: b               #0x97e5d8
    // 0x97e5d4: r0 = Null
    //     0x97e5d4: mov             x0, NULL
    // 0x97e5d8: cmp             w0, NULL
    // 0x97e5dc: b.eq            #0x97e5e0
    // 0x97e5e0: ldur            x0, [fp, #-0x10]
    // 0x97e5e4: LoadField: r1 = r0->field_f
    //     0x97e5e4: ldur            w1, [x0, #0xf]
    // 0x97e5e8: DecompressPointer r1
    //     0x97e5e8: add             x1, x1, HEAP, lsl #32
    // 0x97e5ec: LoadField: r2 = r1->field_33
    //     0x97e5ec: ldur            w2, [x1, #0x33]
    // 0x97e5f0: DecompressPointer r2
    //     0x97e5f0: add             x2, x2, HEAP, lsl #32
    // 0x97e5f4: mov             x1, x2
    // 0x97e5f8: r0 = _currentElement()
    //     0x97e5f8: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x97e5fc: cmp             w0, NULL
    // 0x97e600: b.eq            #0x97e710
    // 0x97e604: ldur            x0, [fp, #-0x10]
    // 0x97e608: LoadField: r2 = r0->field_f
    //     0x97e608: ldur            w2, [x0, #0xf]
    // 0x97e60c: DecompressPointer r2
    //     0x97e60c: add             x2, x2, HEAP, lsl #32
    // 0x97e610: stur            x2, [fp, #-8]
    // 0x97e614: LoadField: r1 = r2->field_33
    //     0x97e614: ldur            w1, [x2, #0x33]
    // 0x97e618: DecompressPointer r1
    //     0x97e618: add             x1, x1, HEAP, lsl #32
    // 0x97e61c: r0 = _currentElement()
    //     0x97e61c: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x97e620: cmp             w0, NULL
    // 0x97e624: b.ne            #0x97e630
    // 0x97e628: r0 = Null
    //     0x97e628: mov             x0, NULL
    // 0x97e62c: b               #0x97e698
    // 0x97e630: mov             x1, x0
    // 0x97e634: r0 = findRenderObject()
    //     0x97e634: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0x97e638: r1 = LoadClassIdInstr(r0)
    //     0x97e638: ldur            x1, [x0, #-1]
    //     0x97e63c: ubfx            x1, x1, #0xc, #0x14
    // 0x97e640: sub             x16, x1, #0xbba
    // 0x97e644: cmp             x16, #0x9a
    // 0x97e648: b.hi            #0x97e658
    // 0x97e64c: mov             x1, x0
    // 0x97e650: r0 = size()
    //     0x97e650: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x97e654: b               #0x97e65c
    // 0x97e658: r0 = Null
    //     0x97e658: mov             x0, NULL
    // 0x97e65c: cmp             w0, NULL
    // 0x97e660: b.ne            #0x97e66c
    // 0x97e664: r0 = Null
    //     0x97e664: mov             x0, NULL
    // 0x97e668: b               #0x97e698
    // 0x97e66c: LoadField: d0 = r0->field_7
    //     0x97e66c: ldur            d0, [x0, #7]
    // 0x97e670: r0 = inline_Allocate_Double()
    //     0x97e670: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x97e674: add             x0, x0, #0x10
    //     0x97e678: cmp             x1, x0
    //     0x97e67c: b.ls            #0x97e738
    //     0x97e680: str             x0, [THR, #0x50]  ; THR::top
    //     0x97e684: sub             x0, x0, #0xf
    //     0x97e688: movz            x1, #0xe15c
    //     0x97e68c: movk            x1, #0x3, lsl #16
    //     0x97e690: stur            x1, [x0, #-1]
    // 0x97e694: StoreField: r0->field_7 = d0
    //     0x97e694: stur            d0, [x0, #7]
    // 0x97e698: cmp             w0, NULL
    // 0x97e69c: b.ne            #0x97e6a8
    // 0x97e6a0: d0 = 0.000000
    //     0x97e6a0: eor             v0.16b, v0.16b, v0.16b
    // 0x97e6a4: b               #0x97e6ac
    // 0x97e6a8: LoadField: d0 = r0->field_7
    //     0x97e6a8: ldur            d0, [x0, #7]
    // 0x97e6ac: ldur            x0, [fp, #-0x10]
    // 0x97e6b0: ldur            x1, [fp, #-8]
    // 0x97e6b4: StoreField: r1->field_3f = d0
    //     0x97e6b4: stur            d0, [x1, #0x3f]
    // 0x97e6b8: LoadField: r1 = r0->field_f
    //     0x97e6b8: ldur            w1, [x0, #0xf]
    // 0x97e6bc: DecompressPointer r1
    //     0x97e6bc: add             x1, x1, HEAP, lsl #32
    // 0x97e6c0: LoadField: r0 = r1->field_33
    //     0x97e6c0: ldur            w0, [x1, #0x33]
    // 0x97e6c4: DecompressPointer r0
    //     0x97e6c4: add             x0, x0, HEAP, lsl #32
    // 0x97e6c8: mov             x1, x0
    // 0x97e6cc: r0 = _currentElement()
    //     0x97e6cc: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x97e6d0: cmp             w0, NULL
    // 0x97e6d4: b.eq            #0x97e710
    // 0x97e6d8: mov             x1, x0
    // 0x97e6dc: r0 = findRenderObject()
    //     0x97e6dc: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0x97e6e0: r1 = LoadClassIdInstr(r0)
    //     0x97e6e0: ldur            x1, [x0, #-1]
    //     0x97e6e4: ubfx            x1, x1, #0xc, #0x14
    // 0x97e6e8: sub             x16, x1, #0xbba
    // 0x97e6ec: cmp             x16, #0x9a
    // 0x97e6f0: b.hi            #0x97e704
    // 0x97e6f4: mov             x1, x0
    // 0x97e6f8: r0 = size()
    //     0x97e6f8: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x97e6fc: mov             x1, x0
    // 0x97e700: b               #0x97e708
    // 0x97e704: r1 = Null
    //     0x97e704: mov             x1, NULL
    // 0x97e708: cmp             w1, NULL
    // 0x97e70c: b.eq            #0x97e710
    // 0x97e710: r0 = Null
    //     0x97e710: mov             x0, NULL
    // 0x97e714: LeaveFrame
    //     0x97e714: mov             SP, fp
    //     0x97e718: ldp             fp, lr, [SP], #0x10
    // 0x97e71c: ret
    //     0x97e71c: ret             
    // 0x97e720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e724: b               #0x97e4dc
    // 0x97e728: SaveReg d0
    //     0x97e728: str             q0, [SP, #-0x10]!
    // 0x97e72c: r0 = AllocateDouble()
    //     0x97e72c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x97e730: RestoreReg d0
    //     0x97e730: ldr             q0, [SP], #0x10
    // 0x97e734: b               #0x97e568
    // 0x97e738: SaveReg d0
    //     0x97e738: str             q0, [SP, #-0x10]!
    // 0x97e73c: r0 = AllocateDouble()
    //     0x97e73c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x97e740: RestoreReg d0
    //     0x97e740: ldr             q0, [SP], #0x10
    // 0x97e744: b               #0x97e694
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a111c, size: 0xe8
    // 0x9a111c: EnterFrame
    //     0x9a111c: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1120: mov             fp, SP
    // 0x9a1124: AllocStack(0x10)
    //     0x9a1124: sub             SP, SP, #0x10
    // 0x9a1128: SetupParameters(_LinearPercentIndicatorState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a1128: mov             x4, x1
    //     0x9a112c: mov             x3, x2
    //     0x9a1130: stur            x1, [fp, #-8]
    //     0x9a1134: stur            x2, [fp, #-0x10]
    // 0x9a1138: CheckStackOverflow
    //     0x9a1138: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a113c: cmp             SP, x16
    //     0x9a1140: b.ls            #0x9a11f8
    // 0x9a1144: mov             x0, x3
    // 0x9a1148: r2 = Null
    //     0x9a1148: mov             x2, NULL
    // 0x9a114c: r1 = Null
    //     0x9a114c: mov             x1, NULL
    // 0x9a1150: r4 = 60
    //     0x9a1150: movz            x4, #0x3c
    // 0x9a1154: branchIfSmi(r0, 0x9a1160)
    //     0x9a1154: tbz             w0, #0, #0x9a1160
    // 0x9a1158: r4 = LoadClassIdInstr(r0)
    //     0x9a1158: ldur            x4, [x0, #-1]
    //     0x9a115c: ubfx            x4, x4, #0xc, #0x14
    // 0x9a1160: r17 = 4703
    //     0x9a1160: movz            x17, #0x125f
    // 0x9a1164: cmp             x4, x17
    // 0x9a1168: b.eq            #0x9a1180
    // 0x9a116c: r8 = LinearPercentIndicator
    //     0x9a116c: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a490] Type: LinearPercentIndicator
    //     0x9a1170: ldr             x8, [x8, #0x490]
    // 0x9a1174: r3 = Null
    //     0x9a1174: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a498] Null
    //     0x9a1178: ldr             x3, [x3, #0x498]
    // 0x9a117c: r0 = LinearPercentIndicator()
    //     0x9a117c: bl              #0x92bbac  ; IsType_LinearPercentIndicator_Stub
    // 0x9a1180: ldur            x3, [fp, #-8]
    // 0x9a1184: LoadField: r2 = r3->field_7
    //     0x9a1184: ldur            w2, [x3, #7]
    // 0x9a1188: DecompressPointer r2
    //     0x9a1188: add             x2, x2, HEAP, lsl #32
    // 0x9a118c: ldur            x0, [fp, #-0x10]
    // 0x9a1190: r1 = Null
    //     0x9a1190: mov             x1, NULL
    // 0x9a1194: cmp             w2, NULL
    // 0x9a1198: b.eq            #0x9a11bc
    // 0x9a119c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a119c: ldur            w4, [x2, #0x17]
    // 0x9a11a0: DecompressPointer r4
    //     0x9a11a0: add             x4, x4, HEAP, lsl #32
    // 0x9a11a4: r8 = X0 bound StatefulWidget
    //     0x9a11a4: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a11a8: ldr             x8, [x8, #0x7f8]
    // 0x9a11ac: LoadField: r9 = r4->field_7
    //     0x9a11ac: ldur            x9, [x4, #7]
    // 0x9a11b0: r3 = Null
    //     0x9a11b0: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a4a8] Null
    //     0x9a11b4: ldr             x3, [x3, #0x4a8]
    // 0x9a11b8: blr             x9
    // 0x9a11bc: ldur            x0, [fp, #-0x10]
    // 0x9a11c0: LoadField: d0 = r0->field_b
    //     0x9a11c0: ldur            d0, [x0, #0xb]
    // 0x9a11c4: ldur            x1, [fp, #-8]
    // 0x9a11c8: LoadField: r0 = r1->field_b
    //     0x9a11c8: ldur            w0, [x1, #0xb]
    // 0x9a11cc: DecompressPointer r0
    //     0x9a11cc: add             x0, x0, HEAP, lsl #32
    // 0x9a11d0: cmp             w0, NULL
    // 0x9a11d4: b.eq            #0x9a1200
    // 0x9a11d8: LoadField: d1 = r0->field_b
    //     0x9a11d8: ldur            d1, [x0, #0xb]
    // 0x9a11dc: fcmp            d0, d1
    // 0x9a11e0: b.eq            #0x9a11e8
    // 0x9a11e4: r0 = _updateProgress()
    //     0x9a11e4: bl              #0x97e3a0  ; [package:percent_indicator/linear_percent_indicator.dart] _LinearPercentIndicatorState::_updateProgress
    // 0x9a11e8: r0 = Null
    //     0x9a11e8: mov             x0, NULL
    // 0x9a11ec: LeaveFrame
    //     0x9a11ec: mov             SP, fp
    //     0x9a11f0: ldp             fp, lr, [SP], #0x10
    // 0x9a11f4: ret
    //     0x9a11f4: ret             
    // 0x9a11f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a11f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a11fc: b               #0x9a1144
    // 0x9a1200: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9a1200: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa43ed8, size: 0x240
    // 0xa43ed8: EnterFrame
    //     0xa43ed8: stp             fp, lr, [SP, #-0x10]!
    //     0xa43edc: mov             fp, SP
    // 0xa43ee0: AllocStack(0x38)
    //     0xa43ee0: sub             SP, SP, #0x38
    // 0xa43ee4: SetupParameters(_LinearPercentIndicatorState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa43ee4: stur            x1, [fp, #-8]
    //     0xa43ee8: stur            x2, [fp, #-0x10]
    // 0xa43eec: CheckStackOverflow
    //     0xa43eec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa43ef0: cmp             SP, x16
    //     0xa43ef4: b.ls            #0xa44108
    // 0xa43ef8: r1 = 2
    //     0xa43ef8: movz            x1, #0x2
    // 0xa43efc: r0 = AllocateContext()
    //     0xa43efc: bl              #0xec126c  ; AllocateContextStub
    // 0xa43f00: mov             x3, x0
    // 0xa43f04: ldur            x0, [fp, #-8]
    // 0xa43f08: stur            x3, [fp, #-0x18]
    // 0xa43f0c: StoreField: r3->field_f = r0
    //     0xa43f0c: stur            w0, [x3, #0xf]
    // 0xa43f10: mov             x1, x0
    // 0xa43f14: ldur            x2, [fp, #-0x10]
    // 0xa43f18: r0 = build()
    //     0xa43f18: bl              #0xa44118  ; [package:percent_indicator/linear_percent_indicator.dart] __LinearPercentIndicatorState&State&SingleTickerProviderStateMixin&AutomaticKeepAliveClientMixin::build
    // 0xa43f1c: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xa43f1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa43f20: ldr             x0, [x0]
    //     0xa43f24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa43f28: cmp             w0, w16
    //     0xa43f2c: b.ne            #0xa43f38
    //     0xa43f30: ldr             x2, [PP, #0x528]  ; [pp+0x528] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xa43f34: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa43f38: r1 = <Widget>
    //     0xa43f38: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa43f3c: stur            x0, [fp, #-0x10]
    // 0xa43f40: r0 = AllocateGrowableArray()
    //     0xa43f40: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa43f44: mov             x3, x0
    // 0xa43f48: ldur            x0, [fp, #-0x10]
    // 0xa43f4c: stur            x3, [fp, #-0x20]
    // 0xa43f50: StoreField: r3->field_f = r0
    //     0xa43f50: stur            w0, [x3, #0xf]
    // 0xa43f54: StoreField: r3->field_b = rZR
    //     0xa43f54: stur            wzr, [x3, #0xb]
    // 0xa43f58: ldur            x4, [fp, #-8]
    // 0xa43f5c: LoadField: r1 = r4->field_b
    //     0xa43f5c: ldur            w1, [x4, #0xb]
    // 0xa43f60: DecompressPointer r1
    //     0xa43f60: add             x1, x1, HEAP, lsl #32
    // 0xa43f64: cmp             w1, NULL
    // 0xa43f68: b.eq            #0xa44110
    // 0xa43f6c: ldur            x2, [fp, #-0x18]
    // 0xa43f70: r1 = false
    //     0xa43f70: add             x1, NULL, #0x30  ; false
    // 0xa43f74: StoreField: r2->field_13 = r1
    //     0xa43f74: stur            w1, [x2, #0x13]
    // 0xa43f78: r1 = Function '<anonymous closure>':.
    //     0xa43f78: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a470] AnonymousClosure: (0xa44170), in [package:percent_indicator/linear_percent_indicator.dart] _LinearPercentIndicatorState::build (0xa43ed8)
    //     0xa43f7c: ldr             x1, [x1, #0x470]
    // 0xa43f80: r0 = AllocateClosure()
    //     0xa43f80: bl              #0xec1630  ; AllocateClosureStub
    // 0xa43f84: r1 = <BoxConstraints>
    //     0xa43f84: add             x1, PP, #0x37, lsl #12  ; [pp+0x37fa8] TypeArguments: <BoxConstraints>
    //     0xa43f88: ldr             x1, [x1, #0xfa8]
    // 0xa43f8c: stur            x0, [fp, #-0x18]
    // 0xa43f90: r0 = LayoutBuilder()
    //     0xa43f90: bl              #0x9f1460  ; AllocateLayoutBuilderStub -> LayoutBuilder (size=0x14)
    // 0xa43f94: mov             x2, x0
    // 0xa43f98: ldur            x0, [fp, #-0x18]
    // 0xa43f9c: stur            x2, [fp, #-0x28]
    // 0xa43fa0: StoreField: r2->field_f = r0
    //     0xa43fa0: stur            w0, [x2, #0xf]
    // 0xa43fa4: r1 = <FlexParentData>
    //     0xa43fa4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xa43fa8: ldr             x1, [x1, #0x720]
    // 0xa43fac: r0 = Expanded()
    //     0xa43fac: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa43fb0: mov             x2, x0
    // 0xa43fb4: r0 = 1
    //     0xa43fb4: movz            x0, #0x1
    // 0xa43fb8: stur            x2, [fp, #-0x18]
    // 0xa43fbc: StoreField: r2->field_13 = r0
    //     0xa43fbc: stur            x0, [x2, #0x13]
    // 0xa43fc0: r0 = Instance_FlexFit
    //     0xa43fc0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xa43fc4: ldr             x0, [x0, #0x728]
    // 0xa43fc8: StoreField: r2->field_1b = r0
    //     0xa43fc8: stur            w0, [x2, #0x1b]
    // 0xa43fcc: ldur            x0, [fp, #-0x28]
    // 0xa43fd0: StoreField: r2->field_b = r0
    //     0xa43fd0: stur            w0, [x2, #0xb]
    // 0xa43fd4: ldur            x0, [fp, #-0x10]
    // 0xa43fd8: LoadField: r1 = r0->field_b
    //     0xa43fd8: ldur            w1, [x0, #0xb]
    // 0xa43fdc: cbnz            w1, #0xa43fe8
    // 0xa43fe0: ldur            x1, [fp, #-0x20]
    // 0xa43fe4: r0 = _growToNextCapacity()
    //     0xa43fe4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa43fe8: ldur            x3, [fp, #-8]
    // 0xa43fec: ldur            x2, [fp, #-0x20]
    // 0xa43ff0: r0 = 2
    //     0xa43ff0: movz            x0, #0x2
    // 0xa43ff4: StoreField: r2->field_b = r0
    //     0xa43ff4: stur            w0, [x2, #0xb]
    // 0xa43ff8: LoadField: r1 = r2->field_f
    //     0xa43ff8: ldur            w1, [x2, #0xf]
    // 0xa43ffc: DecompressPointer r1
    //     0xa43ffc: add             x1, x1, HEAP, lsl #32
    // 0xa44000: ldur            x0, [fp, #-0x18]
    // 0xa44004: ArrayStore: r1[0] = r0  ; List_4
    //     0xa44004: add             x25, x1, #0xf
    //     0xa44008: str             w0, [x25]
    //     0xa4400c: tbz             w0, #0, #0xa44028
    //     0xa44010: ldurb           w16, [x1, #-1]
    //     0xa44014: ldurb           w17, [x0, #-1]
    //     0xa44018: and             x16, x17, x16, lsr #2
    //     0xa4401c: tst             x16, HEAP, lsr #32
    //     0xa44020: b.eq            #0xa44028
    //     0xa44024: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa44028: LoadField: r0 = r3->field_b
    //     0xa44028: ldur            w0, [x3, #0xb]
    // 0xa4402c: DecompressPointer r0
    //     0xa4402c: add             x0, x0, HEAP, lsl #32
    // 0xa44030: cmp             w0, NULL
    // 0xa44034: b.eq            #0xa44114
    // 0xa44038: r0 = Row()
    //     0xa44038: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa4403c: mov             x1, x0
    // 0xa44040: r0 = Instance_Axis
    //     0xa44040: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa44044: stur            x1, [fp, #-8]
    // 0xa44048: StoreField: r1->field_f = r0
    //     0xa44048: stur            w0, [x1, #0xf]
    // 0xa4404c: r0 = Instance_MainAxisAlignment
    //     0xa4404c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa44050: ldr             x0, [x0, #0x730]
    // 0xa44054: StoreField: r1->field_13 = r0
    //     0xa44054: stur            w0, [x1, #0x13]
    // 0xa44058: r0 = Instance_MainAxisSize
    //     0xa44058: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa4405c: ldr             x0, [x0, #0x738]
    // 0xa44060: ArrayStore: r1[0] = r0  ; List_4
    //     0xa44060: stur            w0, [x1, #0x17]
    // 0xa44064: r0 = Instance_CrossAxisAlignment
    //     0xa44064: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa44068: ldr             x0, [x0, #0x740]
    // 0xa4406c: StoreField: r1->field_1b = r0
    //     0xa4406c: stur            w0, [x1, #0x1b]
    // 0xa44070: r0 = Instance_VerticalDirection
    //     0xa44070: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa44074: ldr             x0, [x0, #0x748]
    // 0xa44078: StoreField: r1->field_23 = r0
    //     0xa44078: stur            w0, [x1, #0x23]
    // 0xa4407c: r0 = Instance_Clip
    //     0xa4407c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa44080: ldr             x0, [x0, #0x750]
    // 0xa44084: StoreField: r1->field_2b = r0
    //     0xa44084: stur            w0, [x1, #0x2b]
    // 0xa44088: StoreField: r1->field_2f = rZR
    //     0xa44088: stur            xzr, [x1, #0x2f]
    // 0xa4408c: ldur            x2, [fp, #-0x20]
    // 0xa44090: StoreField: r1->field_b = r2
    //     0xa44090: stur            w2, [x1, #0xb]
    // 0xa44094: r0 = Container()
    //     0xa44094: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa44098: stur            x0, [fp, #-0x10]
    // 0xa4409c: r16 = Instance_Color
    //     0xa4409c: ldr             x16, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xa440a0: ldur            lr, [fp, #-8]
    // 0xa440a4: stp             lr, x16, [SP]
    // 0xa440a8: mov             x1, x0
    // 0xa440ac: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, color, 0x1, null]
    //     0xa440ac: add             x4, PP, #0x2a, lsl #12  ; [pp+0x2abf8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "color", 0x1, Null]
    //     0xa440b0: ldr             x4, [x4, #0xbf8]
    // 0xa440b4: r0 = Container()
    //     0xa440b4: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa440b8: r0 = Material()
    //     0xa440b8: bl              #0x9e6a2c  ; AllocateMaterialStub -> Material (size=0x40)
    // 0xa440bc: r1 = Instance_MaterialType
    //     0xa440bc: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2ce18] Obj!MaterialType@e36501
    //     0xa440c0: ldr             x1, [x1, #0xe18]
    // 0xa440c4: StoreField: r0->field_f = r1
    //     0xa440c4: stur            w1, [x0, #0xf]
    // 0xa440c8: StoreField: r0->field_13 = rZR
    //     0xa440c8: stur            xzr, [x0, #0x13]
    // 0xa440cc: r1 = Instance_Color
    //     0xa440cc: ldr             x1, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xa440d0: StoreField: r0->field_1b = r1
    //     0xa440d0: stur            w1, [x0, #0x1b]
    // 0xa440d4: r1 = true
    //     0xa440d4: add             x1, NULL, #0x20  ; true
    // 0xa440d8: StoreField: r0->field_2f = r1
    //     0xa440d8: stur            w1, [x0, #0x2f]
    // 0xa440dc: r1 = Instance_Clip
    //     0xa440dc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa440e0: ldr             x1, [x1, #0x750]
    // 0xa440e4: StoreField: r0->field_33 = r1
    //     0xa440e4: stur            w1, [x0, #0x33]
    // 0xa440e8: r1 = Instance_Duration
    //     0xa440e8: add             x1, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa440ec: ldr             x1, [x1, #0x368]
    // 0xa440f0: StoreField: r0->field_37 = r1
    //     0xa440f0: stur            w1, [x0, #0x37]
    // 0xa440f4: ldur            x1, [fp, #-0x10]
    // 0xa440f8: StoreField: r0->field_b = r1
    //     0xa440f8: stur            w1, [x0, #0xb]
    // 0xa440fc: LeaveFrame
    //     0xa440fc: mov             SP, fp
    //     0xa44100: ldp             fp, lr, [SP], #0x10
    // 0xa44104: ret
    //     0xa44104: ret             
    // 0xa44108: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44108: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4410c: b               #0xa43ef8
    // 0xa44110: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa44110: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa44114: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa44114: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, BoxConstraints) {
    // ** addr: 0xa44170, size: 0x28c
    // 0xa44170: EnterFrame
    //     0xa44170: stp             fp, lr, [SP, #-0x10]!
    //     0xa44174: mov             fp, SP
    // 0xa44178: AllocStack(0x58)
    //     0xa44178: sub             SP, SP, #0x58
    // 0xa4417c: SetupParameters()
    //     0xa4417c: ldr             x0, [fp, #0x20]
    //     0xa44180: ldur            w1, [x0, #0x17]
    //     0xa44184: add             x1, x1, HEAP, lsl #32
    //     0xa44188: stur            x1, [fp, #-0x28]
    // 0xa4418c: CheckStackOverflow
    //     0xa4418c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44190: cmp             SP, x16
    //     0xa44194: b.ls            #0xa443b8
    // 0xa44198: LoadField: r0 = r1->field_f
    //     0xa44198: ldur            w0, [x1, #0xf]
    // 0xa4419c: DecompressPointer r0
    //     0xa4419c: add             x0, x0, HEAP, lsl #32
    // 0xa441a0: ldr             x2, [fp, #0x10]
    // 0xa441a4: LoadField: d0 = r2->field_f
    //     0xa441a4: ldur            d0, [x2, #0xf]
    // 0xa441a8: StoreField: r0->field_37 = d0
    //     0xa441a8: stur            d0, [x0, #0x37]
    // 0xa441ac: LoadField: r2 = r1->field_13
    //     0xa441ac: ldur            w2, [x1, #0x13]
    // 0xa441b0: DecompressPointer r2
    //     0xa441b0: add             x2, x2, HEAP, lsl #32
    // 0xa441b4: tbz             w2, #4, #0xa443ac
    // 0xa441b8: LoadField: r2 = r0->field_b
    //     0xa441b8: ldur            w2, [x0, #0xb]
    // 0xa441bc: DecompressPointer r2
    //     0xa441bc: add             x2, x2, HEAP, lsl #32
    // 0xa441c0: cmp             w2, NULL
    // 0xa441c4: b.eq            #0xa443c0
    // 0xa441c8: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xa441c8: ldur            d0, [x2, #0x17]
    // 0xa441cc: stur            d0, [fp, #-0x38]
    // 0xa441d0: LoadField: r3 = r0->field_2f
    //     0xa441d0: ldur            w3, [x0, #0x2f]
    // 0xa441d4: DecompressPointer r3
    //     0xa441d4: add             x3, x3, HEAP, lsl #32
    // 0xa441d8: stur            x3, [fp, #-0x20]
    // 0xa441dc: LoadField: d1 = r0->field_27
    //     0xa441dc: ldur            d1, [x0, #0x27]
    // 0xa441e0: stur            d1, [fp, #-0x30]
    // 0xa441e4: LoadField: r5 = r2->field_2b
    //     0xa441e4: ldur            w5, [x2, #0x2b]
    // 0xa441e8: DecompressPointer r5
    //     0xa441e8: add             x5, x5, HEAP, lsl #32
    // 0xa441ec: r16 = Sentinel
    //     0xa441ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa441f0: cmp             w5, w16
    // 0xa441f4: b.eq            #0xa443c4
    // 0xa441f8: stur            x5, [fp, #-0x18]
    // 0xa441fc: LoadField: r0 = r2->field_23
    //     0xa441fc: ldur            w0, [x2, #0x23]
    // 0xa44200: DecompressPointer r0
    //     0xa44200: add             x0, x0, HEAP, lsl #32
    // 0xa44204: r16 = Sentinel
    //     0xa44204: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa44208: cmp             w0, w16
    // 0xa4420c: b.eq            #0xa443d0
    // 0xa44210: stur            x0, [fp, #-0x10]
    // 0xa44214: LoadField: r4 = r2->field_3f
    //     0xa44214: ldur            w4, [x2, #0x3f]
    // 0xa44218: DecompressPointer r4
    //     0xa44218: add             x4, x4, HEAP, lsl #32
    // 0xa4421c: stur            x4, [fp, #-8]
    // 0xa44220: r0 = _LinearPainter()
    //     0xa44220: bl              #0xa445b0  ; Allocate_LinearPainterStub -> _LinearPainter (size=0x3c)
    // 0xa44224: mov             x1, x0
    // 0xa44228: ldur            x2, [fp, #-0x10]
    // 0xa4422c: ldur            x3, [fp, #-8]
    // 0xa44230: ldur            d0, [fp, #-0x30]
    // 0xa44234: ldur            x5, [fp, #-0x18]
    // 0xa44238: stur            x0, [fp, #-8]
    // 0xa4423c: r0 = _LinearPainter()
    //     0xa4423c: bl              #0xa443fc  ; [package:percent_indicator/linear_percent_indicator.dart] _LinearPainter::_LinearPainter
    // 0xa44240: ldur            x0, [fp, #-0x28]
    // 0xa44244: LoadField: r1 = r0->field_f
    //     0xa44244: ldur            w1, [x0, #0xf]
    // 0xa44248: DecompressPointer r1
    //     0xa44248: add             x1, x1, HEAP, lsl #32
    // 0xa4424c: LoadField: r2 = r1->field_b
    //     0xa4424c: ldur            w2, [x1, #0xb]
    // 0xa44250: DecompressPointer r2
    //     0xa44250: add             x2, x2, HEAP, lsl #32
    // 0xa44254: cmp             w2, NULL
    // 0xa44258: b.eq            #0xa443dc
    // 0xa4425c: r0 = Container()
    //     0xa4425c: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa44260: mov             x1, x0
    // 0xa44264: stur            x0, [fp, #-0x10]
    // 0xa44268: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa44268: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa4426c: r0 = Container()
    //     0xa4426c: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa44270: r0 = CustomPaint()
    //     0xa44270: bl              #0x9d4558  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0xa44274: mov             x3, x0
    // 0xa44278: ldur            x0, [fp, #-8]
    // 0xa4427c: stur            x3, [fp, #-0x18]
    // 0xa44280: StoreField: r3->field_f = r0
    //     0xa44280: stur            w0, [x3, #0xf]
    // 0xa44284: r0 = Instance_Size
    //     0xa44284: add             x0, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0xa44288: ldr             x0, [x0, #0xa20]
    // 0xa4428c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa4428c: stur            w0, [x3, #0x17]
    // 0xa44290: r0 = false
    //     0xa44290: add             x0, NULL, #0x30  ; false
    // 0xa44294: StoreField: r3->field_1b = r0
    //     0xa44294: stur            w0, [x3, #0x1b]
    // 0xa44298: StoreField: r3->field_1f = r0
    //     0xa44298: stur            w0, [x3, #0x1f]
    // 0xa4429c: ldur            x0, [fp, #-0x10]
    // 0xa442a0: StoreField: r3->field_b = r0
    //     0xa442a0: stur            w0, [x3, #0xb]
    // 0xa442a4: ldur            x0, [fp, #-0x20]
    // 0xa442a8: StoreField: r3->field_7 = r0
    //     0xa442a8: stur            w0, [x3, #7]
    // 0xa442ac: r1 = Null
    //     0xa442ac: mov             x1, NULL
    // 0xa442b0: r2 = 2
    //     0xa442b0: movz            x2, #0x2
    // 0xa442b4: r0 = AllocateArray()
    //     0xa442b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa442b8: mov             x2, x0
    // 0xa442bc: ldur            x0, [fp, #-0x18]
    // 0xa442c0: stur            x2, [fp, #-8]
    // 0xa442c4: StoreField: r2->field_f = r0
    //     0xa442c4: stur            w0, [x2, #0xf]
    // 0xa442c8: r1 = <Widget>
    //     0xa442c8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa442cc: r0 = AllocateGrowableArray()
    //     0xa442cc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa442d0: mov             x1, x0
    // 0xa442d4: ldur            x0, [fp, #-8]
    // 0xa442d8: stur            x1, [fp, #-0x10]
    // 0xa442dc: StoreField: r1->field_f = r0
    //     0xa442dc: stur            w0, [x1, #0xf]
    // 0xa442e0: r0 = 2
    //     0xa442e0: movz            x0, #0x2
    // 0xa442e4: StoreField: r1->field_b = r0
    //     0xa442e4: stur            w0, [x1, #0xb]
    // 0xa442e8: ldur            x0, [fp, #-0x28]
    // 0xa442ec: LoadField: r2 = r0->field_f
    //     0xa442ec: ldur            w2, [x0, #0xf]
    // 0xa442f0: DecompressPointer r2
    //     0xa442f0: add             x2, x2, HEAP, lsl #32
    // 0xa442f4: LoadField: r0 = r2->field_b
    //     0xa442f4: ldur            w0, [x2, #0xb]
    // 0xa442f8: DecompressPointer r0
    //     0xa442f8: add             x0, x0, HEAP, lsl #32
    // 0xa442fc: cmp             w0, NULL
    // 0xa44300: b.eq            #0xa443e0
    // 0xa44304: r0 = Stack()
    //     0xa44304: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa44308: mov             x1, x0
    // 0xa4430c: r0 = Instance_AlignmentDirectional
    //     0xa4430c: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0xa44310: ldr             x0, [x0, #0x7b0]
    // 0xa44314: stur            x1, [fp, #-0x18]
    // 0xa44318: StoreField: r1->field_f = r0
    //     0xa44318: stur            w0, [x1, #0xf]
    // 0xa4431c: r0 = Instance_StackFit
    //     0xa4431c: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xa44320: ldr             x0, [x0, #0x7b8]
    // 0xa44324: ArrayStore: r1[0] = r0  ; List_4
    //     0xa44324: stur            w0, [x1, #0x17]
    // 0xa44328: r0 = Instance_Clip
    //     0xa44328: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa4432c: ldr             x0, [x0, #0x750]
    // 0xa44330: StoreField: r1->field_1b = r0
    //     0xa44330: stur            w0, [x1, #0x1b]
    // 0xa44334: ldur            x0, [fp, #-0x10]
    // 0xa44338: StoreField: r1->field_b = r0
    //     0xa44338: stur            w0, [x1, #0xb]
    // 0xa4433c: ldur            d0, [fp, #-0x38]
    // 0xa44340: r0 = inline_Allocate_Double()
    //     0xa44340: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa44344: add             x0, x0, #0x10
    //     0xa44348: cmp             x2, x0
    //     0xa4434c: b.ls            #0xa443e4
    //     0xa44350: str             x0, [THR, #0x50]  ; THR::top
    //     0xa44354: sub             x0, x0, #0xf
    //     0xa44358: movz            x2, #0xe15c
    //     0xa4435c: movk            x2, #0x3, lsl #16
    //     0xa44360: stur            x2, [x0, #-1]
    // 0xa44364: StoreField: r0->field_7 = d0
    //     0xa44364: stur            d0, [x0, #7]
    // 0xa44368: stur            x0, [fp, #-8]
    // 0xa4436c: r0 = Container()
    //     0xa4436c: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa44370: stur            x0, [fp, #-0x10]
    // 0xa44374: r16 = inf
    //     0xa44374: ldr             x16, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0xa44378: ldur            lr, [fp, #-8]
    // 0xa4437c: stp             lr, x16, [SP, #0x10]
    // 0xa44380: r16 = Instance_EdgeInsets
    //     0xa44380: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa44384: ldur            lr, [fp, #-0x18]
    // 0xa44388: stp             lr, x16, [SP]
    // 0xa4438c: mov             x1, x0
    // 0xa44390: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, height, 0x2, padding, 0x3, width, 0x1, null]
    //     0xa44390: add             x4, PP, #0x4a, lsl #12  ; [pp+0x4a478] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "height", 0x2, "padding", 0x3, "width", 0x1, Null]
    //     0xa44394: ldr             x4, [x4, #0x478]
    // 0xa44398: r0 = Container()
    //     0xa44398: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4439c: ldur            x0, [fp, #-0x10]
    // 0xa443a0: LeaveFrame
    //     0xa443a0: mov             SP, fp
    //     0xa443a4: ldp             fp, lr, [SP], #0x10
    // 0xa443a8: ret
    //     0xa443a8: ret             
    // 0xa443ac: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xa443ac: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xa443b0: r0 = Throw()
    //     0xa443b0: bl              #0xec04b8  ; ThrowStub
    // 0xa443b4: brk             #0
    // 0xa443b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa443b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa443bc: b               #0xa44198
    // 0xa443c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa443c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa443c4: r9 = _progressColor
    //     0xa443c4: add             x9, PP, #0x4a, lsl #12  ; [pp+0x4a480] Field <LinearPercentIndicator._progressColor@1677288463>: late (offset: 0x2c)
    //     0xa443c8: ldr             x9, [x9, #0x480]
    // 0xa443cc: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xa443cc: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xa443d0: r9 = _backgroundColor
    //     0xa443d0: add             x9, PP, #0x4a, lsl #12  ; [pp+0x4a488] Field <LinearPercentIndicator._backgroundColor@1677288463>: late (offset: 0x24)
    //     0xa443d4: ldr             x9, [x9, #0x488]
    // 0xa443d8: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xa443d8: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xa443dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa443dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa443e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa443e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa443e4: SaveReg d0
    //     0xa443e4: str             q0, [SP, #-0x10]!
    // 0xa443e8: SaveReg r1
    //     0xa443e8: str             x1, [SP, #-8]!
    // 0xa443ec: r0 = AllocateDouble()
    //     0xa443ec: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa443f0: RestoreReg r1
    //     0xa443f0: ldr             x1, [SP], #8
    // 0xa443f4: RestoreReg d0
    //     0xa443f4: ldr             q0, [SP], #0x10
    // 0xa443f8: b               #0xa44364
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83acc, size: 0x30
    // 0xa83acc: EnterFrame
    //     0xa83acc: stp             fp, lr, [SP, #-0x10]!
    //     0xa83ad0: mov             fp, SP
    // 0xa83ad4: CheckStackOverflow
    //     0xa83ad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83ad8: cmp             SP, x16
    //     0xa83adc: b.ls            #0xa83af4
    // 0xa83ae0: r0 = dispose()
    //     0xa83ae0: bl              #0xa83afc  ; [package:percent_indicator/linear_percent_indicator.dart] __LinearPercentIndicatorState&State&SingleTickerProviderStateMixin::dispose
    // 0xa83ae4: r0 = Null
    //     0xa83ae4: mov             x0, NULL
    // 0xa83ae8: LeaveFrame
    //     0xa83ae8: mov             SP, fp
    //     0xa83aec: ldp             fp, lr, [SP], #0x10
    // 0xa83af0: ret
    //     0xa83af0: ret             
    // 0xa83af4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83af4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83af8: b               #0xa83ae0
  }
  _ _LinearPercentIndicatorState(/* No info */) {
    // ** addr: 0xa94b28, size: 0x80
    // 0xa94b28: EnterFrame
    //     0xa94b28: stp             fp, lr, [SP, #-0x10]!
    //     0xa94b2c: mov             fp, SP
    // 0xa94b30: AllocStack(0x8)
    //     0xa94b30: sub             SP, SP, #8
    // 0xa94b34: SetupParameters(_LinearPercentIndicatorState this /* r1 => r0, fp-0x8 */)
    //     0xa94b34: mov             x0, x1
    //     0xa94b38: stur            x1, [fp, #-8]
    // 0xa94b3c: StoreField: r0->field_27 = rZR
    //     0xa94b3c: stur            xzr, [x0, #0x27]
    // 0xa94b40: StoreField: r0->field_37 = rZR
    //     0xa94b40: stur            xzr, [x0, #0x37]
    // 0xa94b44: StoreField: r0->field_3f = rZR
    //     0xa94b44: stur            xzr, [x0, #0x3f]
    // 0xa94b48: r1 = <State<StatefulWidget>>
    //     0xa94b48: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa94b4c: r0 = LabeledGlobalKey()
    //     0xa94b4c: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xa94b50: ldur            x2, [fp, #-8]
    // 0xa94b54: StoreField: r2->field_2f = r0
    //     0xa94b54: stur            w0, [x2, #0x2f]
    //     0xa94b58: ldurb           w16, [x2, #-1]
    //     0xa94b5c: ldurb           w17, [x0, #-1]
    //     0xa94b60: and             x16, x17, x16, lsr #2
    //     0xa94b64: tst             x16, HEAP, lsr #32
    //     0xa94b68: b.eq            #0xa94b70
    //     0xa94b6c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa94b70: r1 = <State<StatefulWidget>>
    //     0xa94b70: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa94b74: r0 = LabeledGlobalKey()
    //     0xa94b74: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xa94b78: ldur            x1, [fp, #-8]
    // 0xa94b7c: StoreField: r1->field_33 = r0
    //     0xa94b7c: stur            w0, [x1, #0x33]
    //     0xa94b80: ldurb           w16, [x1, #-1]
    //     0xa94b84: ldurb           w17, [x0, #-1]
    //     0xa94b88: and             x16, x17, x16, lsr #2
    //     0xa94b8c: tst             x16, HEAP, lsr #32
    //     0xa94b90: b.eq            #0xa94b98
    //     0xa94b94: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa94b98: r0 = Null
    //     0xa94b98: mov             x0, NULL
    // 0xa94b9c: LeaveFrame
    //     0xa94b9c: mov             SP, fp
    //     0xa94ba0: ldp             fp, lr, [SP], #0x10
    // 0xa94ba4: ret
    //     0xa94ba4: ret             
  }
}

// class id: 4703, size: 0x70, field offset: 0xc
class LinearPercentIndicator extends StatefulWidget {

  late Color _progressColor; // offset: 0x2c
  late Color _backgroundColor; // offset: 0x24

  _ createState(/* No info */) {
    // ** addr: 0xa94ae0, size: 0x48
    // 0xa94ae0: EnterFrame
    //     0xa94ae0: stp             fp, lr, [SP, #-0x10]!
    //     0xa94ae4: mov             fp, SP
    // 0xa94ae8: AllocStack(0x8)
    //     0xa94ae8: sub             SP, SP, #8
    // 0xa94aec: CheckStackOverflow
    //     0xa94aec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa94af0: cmp             SP, x16
    //     0xa94af4: b.ls            #0xa94b20
    // 0xa94af8: r1 = <LinearPercentIndicator>
    //     0xa94af8: add             x1, PP, #0x41, lsl #12  ; [pp+0x413e8] TypeArguments: <LinearPercentIndicator>
    //     0xa94afc: ldr             x1, [x1, #0x3e8]
    // 0xa94b00: r0 = _LinearPercentIndicatorState()
    //     0xa94b00: bl              #0xa94ba8  ; Allocate_LinearPercentIndicatorStateStub -> _LinearPercentIndicatorState (size=0x48)
    // 0xa94b04: mov             x1, x0
    // 0xa94b08: stur            x0, [fp, #-8]
    // 0xa94b0c: r0 = _LinearPercentIndicatorState()
    //     0xa94b0c: bl              #0xa94b28  ; [package:percent_indicator/linear_percent_indicator.dart] _LinearPercentIndicatorState::_LinearPercentIndicatorState
    // 0xa94b10: ldur            x0, [fp, #-8]
    // 0xa94b14: LeaveFrame
    //     0xa94b14: mov             SP, fp
    //     0xa94b18: ldp             fp, lr, [SP], #0x10
    // 0xa94b1c: ret
    //     0xa94b1c: ret             
    // 0xa94b20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa94b20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa94b24: b               #0xa94af8
  }
  _ LinearPercentIndicator(/* No info */) {
    // ** addr: 0xb75f24, size: 0x1f0
    // 0xb75f24: EnterFrame
    //     0xb75f24: stp             fp, lr, [SP, #-0x10]!
    //     0xb75f28: mov             fp, SP
    // 0xb75f2c: AllocStack(0x18)
    //     0xb75f2c: sub             SP, SP, #0x18
    // 0xb75f30: r9 = Sentinel
    //     0xb75f30: ldr             x9, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb75f34: r8 = Instance_Color
    //     0xb75f34: ldr             x8, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xb75f38: r7 = false
    //     0xb75f38: add             x7, NULL, #0x30  ; false
    // 0xb75f3c: r0 = true
    //     0xb75f3c: add             x0, NULL, #0x20  ; true
    // 0xb75f40: r6 = Instance_EdgeInsets
    //     0xb75f40: ldr             x6, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb75f44: r4 = Instance_MainAxisAlignment
    //     0xb75f44: add             x4, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb75f48: ldr             x4, [x4, #0x730]
    // 0xb75f4c: stur            x1, [fp, #-8]
    // 0xb75f50: mov             x16, x5
    // 0xb75f54: mov             x5, x1
    // 0xb75f58: mov             x1, x16
    // 0xb75f5c: mov             x16, x3
    // 0xb75f60: mov             x3, x2
    // 0xb75f64: mov             x2, x16
    // 0xb75f68: stur            d1, [fp, #-0x10]
    // 0xb75f6c: CheckStackOverflow
    //     0xb75f6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb75f70: cmp             SP, x16
    //     0xb75f74: b.ls            #0xb760f0
    // 0xb75f78: StoreField: r5->field_23 = r9
    //     0xb75f78: stur            w9, [x5, #0x23]
    // 0xb75f7c: StoreField: r5->field_1f = r8
    //     0xb75f7c: stur            w8, [x5, #0x1f]
    // 0xb75f80: StoreField: r5->field_b = d1
    //     0xb75f80: stur            d1, [x5, #0xb]
    // 0xb75f84: ArrayStore: r5[0] = d0  ; List_8
    //     0xb75f84: stur            d0, [x5, #0x17]
    // 0xb75f88: StoreField: r5->field_2f = r7
    //     0xb75f88: stur            w7, [x5, #0x2f]
    // 0xb75f8c: StoreField: r5->field_53 = r7
    //     0xb75f8c: stur            w7, [x5, #0x53]
    // 0xb75f90: StoreField: r5->field_4f = r0
    //     0xb75f90: stur            w0, [x5, #0x4f]
    // 0xb75f94: mov             x0, x2
    // 0xb75f98: StoreField: r5->field_3f = r0
    //     0xb75f98: stur            w0, [x5, #0x3f]
    //     0xb75f9c: ldurb           w16, [x5, #-1]
    //     0xb75fa0: ldurb           w17, [x0, #-1]
    //     0xb75fa4: and             x16, x17, x16, lsr #2
    //     0xb75fa8: tst             x16, HEAP, lsr #32
    //     0xb75fac: b.eq            #0xb75fb4
    //     0xb75fb0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xb75fb4: StoreField: r5->field_47 = r6
    //     0xb75fb4: stur            w6, [x5, #0x47]
    // 0xb75fb8: StoreField: r5->field_43 = r4
    //     0xb75fb8: stur            w4, [x5, #0x43]
    // 0xb75fbc: StoreField: r5->field_5b = r7
    //     0xb75fbc: stur            w7, [x5, #0x5b]
    // 0xb75fc0: StoreField: r5->field_5f = r7
    //     0xb75fc0: stur            w7, [x5, #0x5f]
    // 0xb75fc4: mov             x0, x1
    // 0xb75fc8: StoreField: r5->field_2b = r0
    //     0xb75fc8: stur            w0, [x5, #0x2b]
    //     0xb75fcc: ldurb           w16, [x5, #-1]
    //     0xb75fd0: ldurb           w17, [x0, #-1]
    //     0xb75fd4: and             x16, x17, x16, lsr #2
    //     0xb75fd8: tst             x16, HEAP, lsr #32
    //     0xb75fdc: b.eq            #0xb75fe4
    //     0xb75fe0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xb75fe4: cmp             w3, NULL
    // 0xb75fe8: b.ne            #0xb76030
    // 0xb75fec: r0 = Color()
    //     0xb75fec: bl              #0x62a9c4  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb75ff0: mov             x1, x0
    // 0xb75ff4: r0 = Instance_ColorSpace
    //     0xb75ff4: ldr             x0, [PP, #0x5788]  ; [pp+0x5788] Obj!ColorSpace@e39a61
    // 0xb75ff8: StoreField: r1->field_27 = r0
    //     0xb75ff8: stur            w0, [x1, #0x27]
    // 0xb75ffc: d0 = 1.000000
    //     0xb75ffc: fmov            d0, #1.00000000
    // 0xb76000: StoreField: r1->field_7 = d0
    //     0xb76000: stur            d0, [x1, #7]
    // 0xb76004: d1 = 0.721569
    //     0xb76004: add             x17, PP, #0x38, lsl #12  ; [pp+0x38268] IMM: double(0.7215686274509804) from 0x3fe7171717171717
    //     0xb76008: ldr             d1, [x17, #0x268]
    // 0xb7600c: StoreField: r1->field_f = d1
    //     0xb7600c: stur            d1, [x1, #0xf]
    // 0xb76010: d1 = 0.780392
    //     0xb76010: add             x17, PP, #0x38, lsl #12  ; [pp+0x38270] IMM: double(0.7803921568627451) from 0x3fe8f8f8f8f8f8f9
    //     0xb76014: ldr             d1, [x17, #0x270]
    // 0xb76018: ArrayStore: r1[0] = d1  ; List_8
    //     0xb76018: stur            d1, [x1, #0x17]
    // 0xb7601c: d1 = 0.796078
    //     0xb7601c: add             x17, PP, #0x38, lsl #12  ; [pp+0x38278] IMM: double(0.796078431372549) from 0x3fe9797979797979
    //     0xb76020: ldr             d1, [x17, #0x278]
    // 0xb76024: StoreField: r1->field_1f = d1
    //     0xb76024: stur            d1, [x1, #0x1f]
    // 0xb76028: mov             x0, x1
    // 0xb7602c: b               #0xb76038
    // 0xb76030: d0 = 1.000000
    //     0xb76030: fmov            d0, #1.00000000
    // 0xb76034: mov             x0, x3
    // 0xb76038: ldur            x1, [fp, #-8]
    // 0xb7603c: ldur            d1, [fp, #-0x10]
    // 0xb76040: d2 = 0.000000
    //     0xb76040: eor             v2.16b, v2.16b, v2.16b
    // 0xb76044: StoreField: r1->field_23 = r0
    //     0xb76044: stur            w0, [x1, #0x23]
    //     0xb76048: ldurb           w16, [x1, #-1]
    //     0xb7604c: ldurb           w17, [x0, #-1]
    //     0xb76050: and             x16, x17, x16, lsr #2
    //     0xb76054: tst             x16, HEAP, lsr #32
    //     0xb76058: b.eq            #0xb76060
    //     0xb7605c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb76060: fcmp            d2, d1
    // 0xb76064: b.gt            #0xb76080
    // 0xb76068: fcmp            d1, d0
    // 0xb7606c: b.gt            #0xb76080
    // 0xb76070: r0 = Null
    //     0xb76070: mov             x0, NULL
    // 0xb76074: LeaveFrame
    //     0xb76074: mov             SP, fp
    //     0xb76078: ldp             fp, lr, [SP], #0x10
    // 0xb7607c: ret
    //     0xb7607c: ret             
    // 0xb76080: r1 = Null
    //     0xb76080: mov             x1, NULL
    // 0xb76084: r2 = 4
    //     0xb76084: movz            x2, #0x4
    // 0xb76088: r0 = AllocateArray()
    //     0xb76088: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb7608c: r16 = "Percent value must be a double between 0.0 and 1.0, but it\'s "
    //     0xb7608c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38280] "Percent value must be a double between 0.0 and 1.0, but it\'s "
    //     0xb76090: ldr             x16, [x16, #0x280]
    // 0xb76094: StoreField: r0->field_f = r16
    //     0xb76094: stur            w16, [x0, #0xf]
    // 0xb76098: ldur            d0, [fp, #-0x10]
    // 0xb7609c: r1 = inline_Allocate_Double()
    //     0xb7609c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb760a0: add             x1, x1, #0x10
    //     0xb760a4: cmp             x2, x1
    //     0xb760a8: b.ls            #0xb760f8
    //     0xb760ac: str             x1, [THR, #0x50]  ; THR::top
    //     0xb760b0: sub             x1, x1, #0xf
    //     0xb760b4: movz            x2, #0xe15c
    //     0xb760b8: movk            x2, #0x3, lsl #16
    //     0xb760bc: stur            x2, [x1, #-1]
    // 0xb760c0: StoreField: r1->field_7 = d0
    //     0xb760c0: stur            d0, [x1, #7]
    // 0xb760c4: StoreField: r0->field_13 = r1
    //     0xb760c4: stur            w1, [x0, #0x13]
    // 0xb760c8: str             x0, [SP]
    // 0xb760cc: r0 = _interpolate()
    //     0xb760cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb760d0: stur            x0, [fp, #-8]
    // 0xb760d4: r0 = _Exception()
    //     0xb760d4: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xb760d8: mov             x1, x0
    // 0xb760dc: ldur            x0, [fp, #-8]
    // 0xb760e0: StoreField: r1->field_7 = r0
    //     0xb760e0: stur            w0, [x1, #7]
    // 0xb760e4: mov             x0, x1
    // 0xb760e8: r0 = Throw()
    //     0xb760e8: bl              #0xec04b8  ; ThrowStub
    // 0xb760ec: brk             #0
    // 0xb760f0: r0 = StackOverflowSharedWithFPURegs()
    //     0xb760f0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xb760f4: b               #0xb75f78
    // 0xb760f8: SaveReg d0
    //     0xb760f8: str             q0, [SP, #-0x10]!
    // 0xb760fc: SaveReg r0
    //     0xb760fc: str             x0, [SP, #-8]!
    // 0xb76100: r0 = AllocateDouble()
    //     0xb76100: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb76104: mov             x1, x0
    // 0xb76108: RestoreReg r0
    //     0xb76108: ldr             x0, [SP], #8
    // 0xb7610c: RestoreReg d0
    //     0xb7610c: ldr             q0, [SP], #0x10
    // 0xb76110: b               #0xb760c0
  }
}

// class id: 5455, size: 0x3c, field offset: 0xc
class _LinearPainter extends CustomPainter {

  _ paint(/* No info */) {
    // ** addr: 0x7d413c, size: 0x5ac
    // 0x7d413c: EnterFrame
    //     0x7d413c: stp             fp, lr, [SP, #-0x10]!
    //     0x7d4140: mov             fp, SP
    // 0x7d4144: AllocStack(0x68)
    //     0x7d4144: sub             SP, SP, #0x68
    // 0x7d4148: SetupParameters(_LinearPainter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7d4148: stur            x1, [fp, #-8]
    //     0x7d414c: stur            x2, [fp, #-0x10]
    //     0x7d4150: stur            x3, [fp, #-0x18]
    // 0x7d4154: CheckStackOverflow
    //     0x7d4154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d4158: cmp             SP, x16
    //     0x7d415c: b.ls            #0x7d46c0
    // 0x7d4160: r0 = _NativePath()
    //     0x7d4160: bl              #0x78e3a4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x7d4164: mov             x1, x0
    // 0x7d4168: stur            x0, [fp, #-0x20]
    // 0x7d416c: r0 = __constructor$Method$FfiNative()
    //     0x7d416c: bl              #0x78eb3c  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x7d4170: ldur            x0, [fp, #-0x18]
    // 0x7d4174: LoadField: d0 = r0->field_7
    //     0x7d4174: ldur            d0, [x0, #7]
    // 0x7d4178: stur            d0, [fp, #-0x60]
    // 0x7d417c: LoadField: d1 = r0->field_f
    //     0x7d417c: ldur            d1, [x0, #0xf]
    // 0x7d4180: stur            d1, [fp, #-0x58]
    // 0x7d4184: d2 = 0.000000
    //     0x7d4184: eor             v2.16b, v2.16b, v2.16b
    // 0x7d4188: fadd            d3, d0, d2
    // 0x7d418c: stur            d3, [fp, #-0x50]
    // 0x7d4190: fadd            d4, d1, d2
    // 0x7d4194: stur            d4, [fp, #-0x48]
    // 0x7d4198: r0 = Rect()
    //     0x7d4198: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7d419c: stur            x0, [fp, #-0x28]
    // 0x7d41a0: StoreField: r0->field_7 = rZR
    //     0x7d41a0: stur            xzr, [x0, #7]
    // 0x7d41a4: StoreField: r0->field_f = rZR
    //     0x7d41a4: stur            xzr, [x0, #0xf]
    // 0x7d41a8: ldur            d0, [fp, #-0x50]
    // 0x7d41ac: ArrayStore: r0[0] = d0  ; List_8
    //     0x7d41ac: stur            d0, [x0, #0x17]
    // 0x7d41b0: ldur            d0, [fp, #-0x48]
    // 0x7d41b4: StoreField: r0->field_1f = d0
    //     0x7d41b4: stur            d0, [x0, #0x1f]
    // 0x7d41b8: ldur            x1, [fp, #-8]
    // 0x7d41bc: LoadField: r3 = r1->field_2b
    //     0x7d41bc: ldur            w3, [x1, #0x2b]
    // 0x7d41c0: DecompressPointer r3
    //     0x7d41c0: add             x3, x3, HEAP, lsl #32
    // 0x7d41c4: stur            x3, [fp, #-0x18]
    // 0x7d41c8: r0 = RRect()
    //     0x7d41c8: bl              #0x789ce8  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x7d41cc: mov             x1, x0
    // 0x7d41d0: ldur            x2, [fp, #-0x28]
    // 0x7d41d4: ldur            x3, [fp, #-0x18]
    // 0x7d41d8: stur            x0, [fp, #-0x28]
    // 0x7d41dc: r0 = RRect.fromRectAndRadius()
    //     0x7d41dc: bl              #0x789854  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x7d41e0: ldur            x0, [fp, #-0x28]
    // 0x7d41e4: LoadField: d0 = r0->field_7
    //     0x7d41e4: ldur            d0, [x0, #7]
    // 0x7d41e8: fcvt            s1, d0
    // 0x7d41ec: stur            d1, [fp, #-0x48]
    // 0x7d41f0: r4 = 24
    //     0x7d41f0: movz            x4, #0x18
    // 0x7d41f4: r0 = AllocateFloat32Array()
    //     0x7d41f4: bl              #0xec19f8  ; AllocateFloat32ArrayStub
    // 0x7d41f8: ldur            d0, [fp, #-0x48]
    // 0x7d41fc: stur            x0, [fp, #-0x38]
    // 0x7d4200: ArrayStore: r0[0] = d0  ; List_8
    //     0x7d4200: stur            s0, [x0, #0x17]
    // 0x7d4204: ldur            x1, [fp, #-0x28]
    // 0x7d4208: LoadField: d0 = r1->field_f
    //     0x7d4208: ldur            d0, [x1, #0xf]
    // 0x7d420c: fcvt            s1, d0
    // 0x7d4210: StoreField: r0->field_1b = d1
    //     0x7d4210: stur            s1, [x0, #0x1b]
    // 0x7d4214: ArrayLoad: d0 = r1[0]  ; List_8
    //     0x7d4214: ldur            d0, [x1, #0x17]
    // 0x7d4218: fcvt            s1, d0
    // 0x7d421c: StoreField: r0->field_1f = d1
    //     0x7d421c: stur            s1, [x0, #0x1f]
    // 0x7d4220: LoadField: d0 = r1->field_1f
    //     0x7d4220: ldur            d0, [x1, #0x1f]
    // 0x7d4224: fcvt            s1, d0
    // 0x7d4228: StoreField: r0->field_23 = d1
    //     0x7d4228: stur            s1, [x0, #0x23]
    // 0x7d422c: LoadField: d0 = r1->field_27
    //     0x7d422c: ldur            d0, [x1, #0x27]
    // 0x7d4230: fcvt            s1, d0
    // 0x7d4234: StoreField: r0->field_27 = d1
    //     0x7d4234: stur            s1, [x0, #0x27]
    // 0x7d4238: LoadField: d0 = r1->field_2f
    //     0x7d4238: ldur            d0, [x1, #0x2f]
    // 0x7d423c: fcvt            s1, d0
    // 0x7d4240: StoreField: r0->field_2b = d1
    //     0x7d4240: stur            s1, [x0, #0x2b]
    // 0x7d4244: LoadField: d0 = r1->field_37
    //     0x7d4244: ldur            d0, [x1, #0x37]
    // 0x7d4248: fcvt            s1, d0
    // 0x7d424c: StoreField: r0->field_2f = d1
    //     0x7d424c: stur            s1, [x0, #0x2f]
    // 0x7d4250: LoadField: d0 = r1->field_3f
    //     0x7d4250: ldur            d0, [x1, #0x3f]
    // 0x7d4254: fcvt            s1, d0
    // 0x7d4258: StoreField: r0->field_33 = d1
    //     0x7d4258: stur            s1, [x0, #0x33]
    // 0x7d425c: LoadField: d0 = r1->field_47
    //     0x7d425c: ldur            d0, [x1, #0x47]
    // 0x7d4260: fcvt            s1, d0
    // 0x7d4264: StoreField: r0->field_37 = d1
    //     0x7d4264: stur            s1, [x0, #0x37]
    // 0x7d4268: LoadField: d0 = r1->field_4f
    //     0x7d4268: ldur            d0, [x1, #0x4f]
    // 0x7d426c: fcvt            s1, d0
    // 0x7d4270: StoreField: r0->field_3b = d1
    //     0x7d4270: stur            s1, [x0, #0x3b]
    // 0x7d4274: LoadField: d0 = r1->field_57
    //     0x7d4274: ldur            d0, [x1, #0x57]
    // 0x7d4278: fcvt            s1, d0
    // 0x7d427c: StoreField: r0->field_3f = d1
    //     0x7d427c: stur            s1, [x0, #0x3f]
    // 0x7d4280: LoadField: d0 = r1->field_5f
    //     0x7d4280: ldur            d0, [x1, #0x5f]
    // 0x7d4284: fcvt            s1, d0
    // 0x7d4288: StoreField: r0->field_43 = d1
    //     0x7d4288: stur            s1, [x0, #0x43]
    // 0x7d428c: ldur            x1, [fp, #-0x20]
    // 0x7d4290: LoadField: r2 = r1->field_7
    //     0x7d4290: ldur            w2, [x1, #7]
    // 0x7d4294: DecompressPointer r2
    //     0x7d4294: add             x2, x2, HEAP, lsl #32
    // 0x7d4298: cmp             w2, NULL
    // 0x7d429c: b.eq            #0x7d46c8
    // 0x7d42a0: LoadField: r3 = r2->field_7
    //     0x7d42a0: ldur            x3, [x2, #7]
    // 0x7d42a4: ldr             x2, [x3]
    // 0x7d42a8: stur            x2, [fp, #-0x30]
    // 0x7d42ac: cbnz            x2, #0x7d42bc
    // 0x7d42b0: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7d42b0: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7d42b4: str             x16, [SP]
    // 0x7d42b8: r0 = _throwNew()
    //     0x7d42b8: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7d42bc: ldur            x0, [fp, #-8]
    // 0x7d42c0: ldur            x2, [fp, #-0x10]
    // 0x7d42c4: ldur            x3, [fp, #-0x30]
    // 0x7d42c8: stur            x3, [fp, #-0x30]
    // 0x7d42cc: r1 = <Never>
    //     0x7d42cc: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7d42d0: r0 = Pointer()
    //     0x7d42d0: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7d42d4: mov             x1, x0
    // 0x7d42d8: ldur            x0, [fp, #-0x30]
    // 0x7d42dc: StoreField: r1->field_7 = r0
    //     0x7d42dc: stur            x0, [x1, #7]
    // 0x7d42e0: ldur            x2, [fp, #-0x38]
    // 0x7d42e4: r0 = __addRRect$Method$FfiNative()
    //     0x7d42e4: bl              #0x78e988  ; [dart:ui] _NativePath::__addRRect$Method$FfiNative
    // 0x7d42e8: ldur            x0, [fp, #-8]
    // 0x7d42ec: LoadField: r1 = r0->field_b
    //     0x7d42ec: ldur            w1, [x0, #0xb]
    // 0x7d42f0: DecompressPointer r1
    //     0x7d42f0: add             x1, x1, HEAP, lsl #32
    // 0x7d42f4: LoadField: r3 = r1->field_b
    //     0x7d42f4: ldur            w3, [x1, #0xb]
    // 0x7d42f8: DecompressPointer r3
    //     0x7d42f8: add             x3, x3, HEAP, lsl #32
    // 0x7d42fc: stur            x3, [fp, #-0x38]
    // 0x7d4300: LoadField: r5 = r1->field_7
    //     0x7d4300: ldur            w5, [x1, #7]
    // 0x7d4304: DecompressPointer r5
    //     0x7d4304: add             x5, x5, HEAP, lsl #32
    // 0x7d4308: ldur            x1, [fp, #-0x10]
    // 0x7d430c: stur            x5, [fp, #-0x28]
    // 0x7d4310: LoadField: r2 = r1->field_7
    //     0x7d4310: ldur            w2, [x1, #7]
    // 0x7d4314: DecompressPointer r2
    //     0x7d4314: add             x2, x2, HEAP, lsl #32
    // 0x7d4318: cmp             w2, NULL
    // 0x7d431c: b.eq            #0x7d46cc
    // 0x7d4320: LoadField: r4 = r2->field_7
    //     0x7d4320: ldur            x4, [x2, #7]
    // 0x7d4324: ldr             x2, [x4]
    // 0x7d4328: stur            x2, [fp, #-0x30]
    // 0x7d432c: cbnz            x2, #0x7d433c
    // 0x7d4330: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7d4330: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7d4334: str             x16, [SP]
    // 0x7d4338: r0 = _throwNew()
    //     0x7d4338: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7d433c: ldur            x0, [fp, #-0x10]
    // 0x7d4340: ldur            x2, [fp, #-0x20]
    // 0x7d4344: ldur            x3, [fp, #-0x30]
    // 0x7d4348: stur            x3, [fp, #-0x30]
    // 0x7d434c: r1 = <Never>
    //     0x7d434c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7d4350: r0 = Pointer()
    //     0x7d4350: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7d4354: mov             x2, x0
    // 0x7d4358: ldur            x0, [fp, #-0x30]
    // 0x7d435c: stur            x2, [fp, #-0x40]
    // 0x7d4360: StoreField: r2->field_7 = r0
    //     0x7d4360: stur            x0, [x2, #7]
    // 0x7d4364: ldur            x0, [fp, #-0x20]
    // 0x7d4368: LoadField: r1 = r0->field_7
    //     0x7d4368: ldur            w1, [x0, #7]
    // 0x7d436c: DecompressPointer r1
    //     0x7d436c: add             x1, x1, HEAP, lsl #32
    // 0x7d4370: cmp             w1, NULL
    // 0x7d4374: b.eq            #0x7d46d0
    // 0x7d4378: LoadField: r3 = r1->field_7
    //     0x7d4378: ldur            x3, [x1, #7]
    // 0x7d437c: ldr             x1, [x3]
    // 0x7d4380: mov             x3, x1
    // 0x7d4384: stur            x3, [fp, #-0x30]
    // 0x7d4388: r1 = <Never>
    //     0x7d4388: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7d438c: r0 = Pointer()
    //     0x7d438c: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7d4390: mov             x1, x0
    // 0x7d4394: ldur            x0, [fp, #-0x30]
    // 0x7d4398: StoreField: r1->field_7 = r0
    //     0x7d4398: stur            x0, [x1, #7]
    // 0x7d439c: mov             x2, x1
    // 0x7d43a0: ldur            x1, [fp, #-0x40]
    // 0x7d43a4: ldur            x3, [fp, #-0x38]
    // 0x7d43a8: ldur            x5, [fp, #-0x28]
    // 0x7d43ac: r0 = __drawPath$Method$FfiNative()
    //     0x7d43ac: bl              #0x78f2fc  ; [dart:ui] _NativeCanvas::__drawPath$Method$FfiNative
    // 0x7d43b0: ldur            x0, [fp, #-0x10]
    // 0x7d43b4: LoadField: r1 = r0->field_7
    //     0x7d43b4: ldur            w1, [x0, #7]
    // 0x7d43b8: DecompressPointer r1
    //     0x7d43b8: add             x1, x1, HEAP, lsl #32
    // 0x7d43bc: cmp             w1, NULL
    // 0x7d43c0: b.eq            #0x7d46d4
    // 0x7d43c4: LoadField: r2 = r1->field_7
    //     0x7d43c4: ldur            x2, [x1, #7]
    // 0x7d43c8: ldr             x1, [x2]
    // 0x7d43cc: stur            x1, [fp, #-0x30]
    // 0x7d43d0: cbnz            x1, #0x7d43e0
    // 0x7d43d4: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7d43d4: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7d43d8: str             x16, [SP]
    // 0x7d43dc: r0 = _throwNew()
    //     0x7d43dc: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7d43e0: ldur            x2, [fp, #-8]
    // 0x7d43e4: ldur            x0, [fp, #-0x20]
    // 0x7d43e8: ldur            d0, [fp, #-0x60]
    // 0x7d43ec: ldur            d1, [fp, #-0x58]
    // 0x7d43f0: ldur            x3, [fp, #-0x30]
    // 0x7d43f4: stur            x3, [fp, #-0x30]
    // 0x7d43f8: r1 = <Never>
    //     0x7d43f8: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7d43fc: r0 = Pointer()
    //     0x7d43fc: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7d4400: mov             x2, x0
    // 0x7d4404: ldur            x0, [fp, #-0x30]
    // 0x7d4408: stur            x2, [fp, #-0x28]
    // 0x7d440c: StoreField: r2->field_7 = r0
    //     0x7d440c: stur            x0, [x2, #7]
    // 0x7d4410: ldur            x0, [fp, #-0x20]
    // 0x7d4414: LoadField: r1 = r0->field_7
    //     0x7d4414: ldur            w1, [x0, #7]
    // 0x7d4418: DecompressPointer r1
    //     0x7d4418: add             x1, x1, HEAP, lsl #32
    // 0x7d441c: cmp             w1, NULL
    // 0x7d4420: b.eq            #0x7d46d8
    // 0x7d4424: LoadField: r3 = r1->field_7
    //     0x7d4424: ldur            x3, [x1, #7]
    // 0x7d4428: ldr             x1, [x3]
    // 0x7d442c: mov             x3, x1
    // 0x7d4430: stur            x3, [fp, #-0x30]
    // 0x7d4434: r1 = <Never>
    //     0x7d4434: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7d4438: r0 = Pointer()
    //     0x7d4438: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7d443c: mov             x1, x0
    // 0x7d4440: ldur            x0, [fp, #-0x30]
    // 0x7d4444: StoreField: r1->field_7 = r0
    //     0x7d4444: stur            x0, [x1, #7]
    // 0x7d4448: mov             x2, x1
    // 0x7d444c: ldur            x1, [fp, #-0x28]
    // 0x7d4450: r3 = true
    //     0x7d4450: add             x3, NULL, #0x20  ; true
    // 0x7d4454: r0 = __clipPath$Method$FfiNative()
    //     0x7d4454: bl              #0x78dfd0  ; [dart:ui] _NativeCanvas::__clipPath$Method$FfiNative
    // 0x7d4458: ldur            x0, [fp, #-8]
    // 0x7d445c: LoadField: d0 = r0->field_13
    //     0x7d445c: ldur            d0, [x0, #0x13]
    // 0x7d4460: ldur            d1, [fp, #-0x60]
    // 0x7d4464: fmul            d2, d1, d0
    // 0x7d4468: stur            d2, [fp, #-0x48]
    // 0x7d446c: r0 = _NativePath()
    //     0x7d446c: bl              #0x78e3a4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x7d4470: mov             x1, x0
    // 0x7d4474: stur            x0, [fp, #-0x20]
    // 0x7d4478: r0 = __constructor$Method$FfiNative()
    //     0x7d4478: bl              #0x78eb3c  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x7d447c: r0 = _NativePath()
    //     0x7d447c: bl              #0x78e3a4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x7d4480: mov             x1, x0
    // 0x7d4484: r0 = __constructor$Method$FfiNative()
    //     0x7d4484: bl              #0x78eb3c  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x7d4488: ldur            d0, [fp, #-0x48]
    // 0x7d448c: d1 = 0.000000
    //     0x7d448c: eor             v1.16b, v1.16b, v1.16b
    // 0x7d4490: fsub            d2, d0, d1
    // 0x7d4494: ldur            d0, [fp, #-0x58]
    // 0x7d4498: fsub            d3, d0, d1
    // 0x7d449c: fadd            d0, d2, d1
    // 0x7d44a0: stur            d0, [fp, #-0x50]
    // 0x7d44a4: fadd            d2, d3, d1
    // 0x7d44a8: stur            d2, [fp, #-0x48]
    // 0x7d44ac: r0 = Rect()
    //     0x7d44ac: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7d44b0: stur            x0, [fp, #-0x28]
    // 0x7d44b4: StoreField: r0->field_7 = rZR
    //     0x7d44b4: stur            xzr, [x0, #7]
    // 0x7d44b8: StoreField: r0->field_f = rZR
    //     0x7d44b8: stur            xzr, [x0, #0xf]
    // 0x7d44bc: ldur            d0, [fp, #-0x50]
    // 0x7d44c0: ArrayStore: r0[0] = d0  ; List_8
    //     0x7d44c0: stur            d0, [x0, #0x17]
    // 0x7d44c4: ldur            d0, [fp, #-0x48]
    // 0x7d44c8: StoreField: r0->field_1f = d0
    //     0x7d44c8: stur            d0, [x0, #0x1f]
    // 0x7d44cc: r0 = RRect()
    //     0x7d44cc: bl              #0x789ce8  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x7d44d0: mov             x1, x0
    // 0x7d44d4: ldur            x2, [fp, #-0x28]
    // 0x7d44d8: ldur            x3, [fp, #-0x18]
    // 0x7d44dc: stur            x0, [fp, #-0x18]
    // 0x7d44e0: r0 = RRect.fromRectAndRadius()
    //     0x7d44e0: bl              #0x789854  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x7d44e4: ldur            x0, [fp, #-0x18]
    // 0x7d44e8: LoadField: d0 = r0->field_7
    //     0x7d44e8: ldur            d0, [x0, #7]
    // 0x7d44ec: fcvt            s1, d0
    // 0x7d44f0: stur            d1, [fp, #-0x48]
    // 0x7d44f4: r4 = 24
    //     0x7d44f4: movz            x4, #0x18
    // 0x7d44f8: r0 = AllocateFloat32Array()
    //     0x7d44f8: bl              #0xec19f8  ; AllocateFloat32ArrayStub
    // 0x7d44fc: ldur            d0, [fp, #-0x48]
    // 0x7d4500: stur            x0, [fp, #-0x28]
    // 0x7d4504: ArrayStore: r0[0] = d0  ; List_8
    //     0x7d4504: stur            s0, [x0, #0x17]
    // 0x7d4508: ldur            x1, [fp, #-0x18]
    // 0x7d450c: LoadField: d0 = r1->field_f
    //     0x7d450c: ldur            d0, [x1, #0xf]
    // 0x7d4510: fcvt            s1, d0
    // 0x7d4514: StoreField: r0->field_1b = d1
    //     0x7d4514: stur            s1, [x0, #0x1b]
    // 0x7d4518: ArrayLoad: d0 = r1[0]  ; List_8
    //     0x7d4518: ldur            d0, [x1, #0x17]
    // 0x7d451c: fcvt            s1, d0
    // 0x7d4520: StoreField: r0->field_1f = d1
    //     0x7d4520: stur            s1, [x0, #0x1f]
    // 0x7d4524: LoadField: d0 = r1->field_1f
    //     0x7d4524: ldur            d0, [x1, #0x1f]
    // 0x7d4528: fcvt            s1, d0
    // 0x7d452c: StoreField: r0->field_23 = d1
    //     0x7d452c: stur            s1, [x0, #0x23]
    // 0x7d4530: LoadField: d0 = r1->field_27
    //     0x7d4530: ldur            d0, [x1, #0x27]
    // 0x7d4534: fcvt            s1, d0
    // 0x7d4538: StoreField: r0->field_27 = d1
    //     0x7d4538: stur            s1, [x0, #0x27]
    // 0x7d453c: LoadField: d0 = r1->field_2f
    //     0x7d453c: ldur            d0, [x1, #0x2f]
    // 0x7d4540: fcvt            s1, d0
    // 0x7d4544: StoreField: r0->field_2b = d1
    //     0x7d4544: stur            s1, [x0, #0x2b]
    // 0x7d4548: LoadField: d0 = r1->field_37
    //     0x7d4548: ldur            d0, [x1, #0x37]
    // 0x7d454c: fcvt            s1, d0
    // 0x7d4550: StoreField: r0->field_2f = d1
    //     0x7d4550: stur            s1, [x0, #0x2f]
    // 0x7d4554: LoadField: d0 = r1->field_3f
    //     0x7d4554: ldur            d0, [x1, #0x3f]
    // 0x7d4558: fcvt            s1, d0
    // 0x7d455c: StoreField: r0->field_33 = d1
    //     0x7d455c: stur            s1, [x0, #0x33]
    // 0x7d4560: LoadField: d0 = r1->field_47
    //     0x7d4560: ldur            d0, [x1, #0x47]
    // 0x7d4564: fcvt            s1, d0
    // 0x7d4568: StoreField: r0->field_37 = d1
    //     0x7d4568: stur            s1, [x0, #0x37]
    // 0x7d456c: LoadField: d0 = r1->field_4f
    //     0x7d456c: ldur            d0, [x1, #0x4f]
    // 0x7d4570: fcvt            s1, d0
    // 0x7d4574: StoreField: r0->field_3b = d1
    //     0x7d4574: stur            s1, [x0, #0x3b]
    // 0x7d4578: LoadField: d0 = r1->field_57
    //     0x7d4578: ldur            d0, [x1, #0x57]
    // 0x7d457c: fcvt            s1, d0
    // 0x7d4580: StoreField: r0->field_3f = d1
    //     0x7d4580: stur            s1, [x0, #0x3f]
    // 0x7d4584: LoadField: d0 = r1->field_5f
    //     0x7d4584: ldur            d0, [x1, #0x5f]
    // 0x7d4588: fcvt            s1, d0
    // 0x7d458c: StoreField: r0->field_43 = d1
    //     0x7d458c: stur            s1, [x0, #0x43]
    // 0x7d4590: ldur            x1, [fp, #-0x20]
    // 0x7d4594: LoadField: r2 = r1->field_7
    //     0x7d4594: ldur            w2, [x1, #7]
    // 0x7d4598: DecompressPointer r2
    //     0x7d4598: add             x2, x2, HEAP, lsl #32
    // 0x7d459c: cmp             w2, NULL
    // 0x7d45a0: b.eq            #0x7d46dc
    // 0x7d45a4: LoadField: r3 = r2->field_7
    //     0x7d45a4: ldur            x3, [x2, #7]
    // 0x7d45a8: ldr             x2, [x3]
    // 0x7d45ac: stur            x2, [fp, #-0x30]
    // 0x7d45b0: cbnz            x2, #0x7d45c0
    // 0x7d45b4: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7d45b4: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7d45b8: str             x16, [SP]
    // 0x7d45bc: r0 = _throwNew()
    //     0x7d45bc: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7d45c0: ldur            x0, [fp, #-8]
    // 0x7d45c4: ldur            x2, [fp, #-0x10]
    // 0x7d45c8: ldur            x3, [fp, #-0x30]
    // 0x7d45cc: stur            x3, [fp, #-0x30]
    // 0x7d45d0: r1 = <Never>
    //     0x7d45d0: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7d45d4: r0 = Pointer()
    //     0x7d45d4: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7d45d8: mov             x1, x0
    // 0x7d45dc: ldur            x0, [fp, #-0x30]
    // 0x7d45e0: StoreField: r1->field_7 = r0
    //     0x7d45e0: stur            x0, [x1, #7]
    // 0x7d45e4: ldur            x2, [fp, #-0x28]
    // 0x7d45e8: r0 = __addRRect$Method$FfiNative()
    //     0x7d45e8: bl              #0x78e988  ; [dart:ui] _NativePath::__addRRect$Method$FfiNative
    // 0x7d45ec: ldur            x0, [fp, #-8]
    // 0x7d45f0: LoadField: r1 = r0->field_f
    //     0x7d45f0: ldur            w1, [x0, #0xf]
    // 0x7d45f4: DecompressPointer r1
    //     0x7d45f4: add             x1, x1, HEAP, lsl #32
    // 0x7d45f8: LoadField: r3 = r1->field_b
    //     0x7d45f8: ldur            w3, [x1, #0xb]
    // 0x7d45fc: DecompressPointer r3
    //     0x7d45fc: add             x3, x3, HEAP, lsl #32
    // 0x7d4600: stur            x3, [fp, #-0x18]
    // 0x7d4604: LoadField: r5 = r1->field_7
    //     0x7d4604: ldur            w5, [x1, #7]
    // 0x7d4608: DecompressPointer r5
    //     0x7d4608: add             x5, x5, HEAP, lsl #32
    // 0x7d460c: ldur            x0, [fp, #-0x10]
    // 0x7d4610: stur            x5, [fp, #-8]
    // 0x7d4614: LoadField: r1 = r0->field_7
    //     0x7d4614: ldur            w1, [x0, #7]
    // 0x7d4618: DecompressPointer r1
    //     0x7d4618: add             x1, x1, HEAP, lsl #32
    // 0x7d461c: cmp             w1, NULL
    // 0x7d4620: b.eq            #0x7d46e0
    // 0x7d4624: LoadField: r2 = r1->field_7
    //     0x7d4624: ldur            x2, [x1, #7]
    // 0x7d4628: ldr             x1, [x2]
    // 0x7d462c: stur            x1, [fp, #-0x30]
    // 0x7d4630: cbnz            x1, #0x7d4640
    // 0x7d4634: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7d4634: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7d4638: str             x16, [SP]
    // 0x7d463c: r0 = _throwNew()
    //     0x7d463c: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7d4640: ldur            x0, [fp, #-0x20]
    // 0x7d4644: ldur            x2, [fp, #-0x30]
    // 0x7d4648: stur            x2, [fp, #-0x30]
    // 0x7d464c: r1 = <Never>
    //     0x7d464c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7d4650: r0 = Pointer()
    //     0x7d4650: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7d4654: mov             x2, x0
    // 0x7d4658: ldur            x0, [fp, #-0x30]
    // 0x7d465c: stur            x2, [fp, #-0x28]
    // 0x7d4660: StoreField: r2->field_7 = r0
    //     0x7d4660: stur            x0, [x2, #7]
    // 0x7d4664: ldur            x0, [fp, #-0x20]
    // 0x7d4668: LoadField: r1 = r0->field_7
    //     0x7d4668: ldur            w1, [x0, #7]
    // 0x7d466c: DecompressPointer r1
    //     0x7d466c: add             x1, x1, HEAP, lsl #32
    // 0x7d4670: cmp             w1, NULL
    // 0x7d4674: b.eq            #0x7d46e4
    // 0x7d4678: LoadField: r3 = r1->field_7
    //     0x7d4678: ldur            x3, [x1, #7]
    // 0x7d467c: ldr             x1, [x3]
    // 0x7d4680: mov             x3, x1
    // 0x7d4684: stur            x3, [fp, #-0x30]
    // 0x7d4688: r1 = <Never>
    //     0x7d4688: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7d468c: r0 = Pointer()
    //     0x7d468c: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7d4690: mov             x1, x0
    // 0x7d4694: ldur            x0, [fp, #-0x30]
    // 0x7d4698: StoreField: r1->field_7 = r0
    //     0x7d4698: stur            x0, [x1, #7]
    // 0x7d469c: mov             x2, x1
    // 0x7d46a0: ldur            x1, [fp, #-0x28]
    // 0x7d46a4: ldur            x3, [fp, #-0x18]
    // 0x7d46a8: ldur            x5, [fp, #-8]
    // 0x7d46ac: r0 = __drawPath$Method$FfiNative()
    //     0x7d46ac: bl              #0x78f2fc  ; [dart:ui] _NativeCanvas::__drawPath$Method$FfiNative
    // 0x7d46b0: r0 = Null
    //     0x7d46b0: mov             x0, NULL
    // 0x7d46b4: LeaveFrame
    //     0x7d46b4: mov             SP, fp
    //     0x7d46b8: ldp             fp, lr, [SP], #0x10
    // 0x7d46bc: ret
    //     0x7d46bc: ret             
    // 0x7d46c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d46c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d46c4: b               #0x7d4160
    // 0x7d46c8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7d46c8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7d46cc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7d46cc: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7d46d0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7d46d0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7d46d4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7d46d4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7d46d8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7d46d8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7d46dc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7d46dc: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7d46e0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7d46e0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7d46e4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7d46e4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _LinearPainter(/* No info */) {
    // ** addr: 0xa443fc, size: 0x1b4
    // 0xa443fc: EnterFrame
    //     0xa443fc: stp             fp, lr, [SP, #-0x10]!
    //     0xa44400: mov             fp, SP
    // 0xa44404: AllocStack(0x50)
    //     0xa44404: sub             SP, SP, #0x50
    // 0xa44408: SetupParameters(_LinearPainter this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */, dynamic _ /* d0 => d0, fp-0x40 */)
    //     0xa44408: stur            x1, [fp, #-8]
    //     0xa4440c: mov             x16, x3
    //     0xa44410: mov             x3, x1
    //     0xa44414: mov             x1, x16
    //     0xa44418: mov             x0, x5
    //     0xa4441c: stur            x2, [fp, #-0x10]
    //     0xa44420: stur            x1, [fp, #-0x18]
    //     0xa44424: stur            x5, [fp, #-0x20]
    //     0xa44428: stur            d0, [fp, #-0x40]
    // 0xa4442c: CheckStackOverflow
    //     0xa4442c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44430: cmp             SP, x16
    //     0xa44434: b.ls            #0xa445a8
    // 0xa44438: r16 = 136
    //     0xa44438: movz            x16, #0x88
    // 0xa4443c: stp             x16, NULL, [SP]
    // 0xa44440: r0 = ByteData()
    //     0xa44440: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xa44444: stur            x0, [fp, #-0x28]
    // 0xa44448: r0 = Paint()
    //     0xa44448: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xa4444c: mov             x1, x0
    // 0xa44450: ldur            x0, [fp, #-0x28]
    // 0xa44454: stur            x1, [fp, #-0x30]
    // 0xa44458: StoreField: r1->field_7 = r0
    //     0xa44458: stur            w0, [x1, #7]
    // 0xa4445c: mov             x0, x1
    // 0xa44460: ldur            x2, [fp, #-8]
    // 0xa44464: StoreField: r2->field_b = r0
    //     0xa44464: stur            w0, [x2, #0xb]
    //     0xa44468: ldurb           w16, [x2, #-1]
    //     0xa4446c: ldurb           w17, [x0, #-1]
    //     0xa44470: and             x16, x17, x16, lsr #2
    //     0xa44474: tst             x16, HEAP, lsr #32
    //     0xa44478: b.eq            #0xa44480
    //     0xa4447c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa44480: r16 = 136
    //     0xa44480: movz            x16, #0x88
    // 0xa44484: stp             x16, NULL, [SP]
    // 0xa44488: r0 = ByteData()
    //     0xa44488: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xa4448c: stur            x0, [fp, #-0x28]
    // 0xa44490: r0 = Paint()
    //     0xa44490: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xa44494: mov             x1, x0
    // 0xa44498: ldur            x0, [fp, #-0x28]
    // 0xa4449c: stur            x1, [fp, #-0x38]
    // 0xa444a0: StoreField: r1->field_7 = r0
    //     0xa444a0: stur            w0, [x1, #7]
    // 0xa444a4: mov             x0, x1
    // 0xa444a8: ldur            x2, [fp, #-8]
    // 0xa444ac: StoreField: r2->field_f = r0
    //     0xa444ac: stur            w0, [x2, #0xf]
    //     0xa444b0: ldurb           w16, [x2, #-1]
    //     0xa444b4: ldurb           w17, [x0, #-1]
    //     0xa444b8: and             x16, x17, x16, lsr #2
    //     0xa444bc: tst             x16, HEAP, lsr #32
    //     0xa444c0: b.eq            #0xa444c8
    //     0xa444c4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa444c8: r16 = 136
    //     0xa444c8: movz            x16, #0x88
    // 0xa444cc: stp             x16, NULL, [SP]
    // 0xa444d0: r0 = ByteData()
    //     0xa444d0: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xa444d4: ldur            x1, [fp, #-8]
    // 0xa444d8: ldur            d0, [fp, #-0x40]
    // 0xa444dc: StoreField: r1->field_13 = d0
    //     0xa444dc: stur            d0, [x1, #0x13]
    // 0xa444e0: r0 = false
    //     0xa444e0: add             x0, NULL, #0x30  ; false
    // 0xa444e4: StoreField: r1->field_1b = r0
    //     0xa444e4: stur            w0, [x1, #0x1b]
    // 0xa444e8: ldur            x0, [fp, #-0x20]
    // 0xa444ec: StoreField: r1->field_1f = r0
    //     0xa444ec: stur            w0, [x1, #0x1f]
    //     0xa444f0: ldurb           w16, [x1, #-1]
    //     0xa444f4: ldurb           w17, [x0, #-1]
    //     0xa444f8: and             x16, x17, x16, lsr #2
    //     0xa444fc: tst             x16, HEAP, lsr #32
    //     0xa44500: b.eq            #0xa44508
    //     0xa44504: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa44508: ldur            x0, [fp, #-0x10]
    // 0xa4450c: StoreField: r1->field_27 = r0
    //     0xa4450c: stur            w0, [x1, #0x27]
    //     0xa44510: ldurb           w16, [x1, #-1]
    //     0xa44514: ldurb           w17, [x0, #-1]
    //     0xa44518: and             x16, x17, x16, lsr #2
    //     0xa4451c: tst             x16, HEAP, lsr #32
    //     0xa44520: b.eq            #0xa44528
    //     0xa44524: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa44528: ldur            x0, [fp, #-0x18]
    // 0xa4452c: StoreField: r1->field_2b = r0
    //     0xa4452c: stur            w0, [x1, #0x2b]
    //     0xa44530: ldurb           w16, [x1, #-1]
    //     0xa44534: ldurb           w17, [x0, #-1]
    //     0xa44538: and             x16, x17, x16, lsr #2
    //     0xa4453c: tst             x16, HEAP, lsr #32
    //     0xa44540: b.eq            #0xa44548
    //     0xa44544: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa44548: ldur            x1, [fp, #-0x30]
    // 0xa4454c: ldur            x2, [fp, #-0x10]
    // 0xa44550: r0 = color=()
    //     0xa44550: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0xa44554: ldur            d0, [fp, #-0x40]
    // 0xa44558: d1 = 0.000000
    //     0xa44558: eor             v1.16b, v1.16b, v1.16b
    // 0xa4455c: fcmp            d0, d1
    // 0xa44560: b.ne            #0xa44588
    // 0xa44564: ldur            x1, [fp, #-0x20]
    // 0xa44568: r0 = LoadClassIdInstr(r1)
    //     0xa44568: ldur            x0, [x1, #-1]
    //     0xa4456c: ubfx            x0, x0, #0xc, #0x14
    // 0xa44570: mov             v0.16b, v1.16b
    // 0xa44574: r0 = GDT[cid_x0 + -0x1000]()
    //     0xa44574: sub             lr, x0, #1, lsl #12
    //     0xa44578: ldr             lr, [x21, lr, lsl #3]
    //     0xa4457c: blr             lr
    // 0xa44580: mov             x2, x0
    // 0xa44584: b               #0xa44590
    // 0xa44588: ldur            x1, [fp, #-0x20]
    // 0xa4458c: mov             x2, x1
    // 0xa44590: ldur            x1, [fp, #-0x38]
    // 0xa44594: r0 = color=()
    //     0xa44594: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0xa44598: r0 = Null
    //     0xa44598: mov             x0, NULL
    // 0xa4459c: LeaveFrame
    //     0xa4459c: mov             SP, fp
    //     0xa445a0: ldp             fp, lr, [SP], #0x10
    // 0xa445a4: ret
    //     0xa445a4: ret             
    // 0xa445a8: r0 = StackOverflowSharedWithFPURegs()
    //     0xa445a8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa445ac: b               #0xa44438
  }
}
