// lib: , url: package:permission_handler/permission_handler.dart

// class id: 1050867, size: 0x8
class :: {

  static _ PermissionCheckShortcuts.isGranted(/* No info */) {
    // ** addr: 0x8ffc60, size: 0x34
    // 0x8ffc60: EnterFrame
    //     0x8ffc60: stp             fp, lr, [SP, #-0x10]!
    //     0x8ffc64: mov             fp, SP
    // 0x8ffc68: CheckStackOverflow
    //     0x8ffc68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ffc6c: cmp             SP, x16
    //     0x8ffc70: b.ls            #0x8ffc8c
    // 0x8ffc74: r0 = PermissionActions.status()
    //     0x8ffc74: bl              #0x8ffd10  ; [package:permission_handler/permission_handler.dart] ::PermissionActions.status
    // 0x8ffc78: mov             x1, x0
    // 0x8ffc7c: r0 = FuturePermissionStatusGetters.isGranted()
    //     0x8ffc7c: bl              #0x8ffc94  ; [package:permission_handler_platform_interface/permission_handler_platform_interface.dart] ::FuturePermissionStatusGetters.isGranted
    // 0x8ffc80: LeaveFrame
    //     0x8ffc80: mov             SP, fp
    //     0x8ffc84: ldp             fp, lr, [SP], #0x10
    // 0x8ffc88: ret
    //     0x8ffc88: ret             
    // 0x8ffc8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ffc8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ffc90: b               #0x8ffc74
  }
  static _ PermissionActions.status(/* No info */) {
    // ** addr: 0x8ffd10, size: 0x58
    // 0x8ffd10: EnterFrame
    //     0x8ffd10: stp             fp, lr, [SP, #-0x10]!
    //     0x8ffd14: mov             fp, SP
    // 0x8ffd18: CheckStackOverflow
    //     0x8ffd18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ffd1c: cmp             SP, x16
    //     0x8ffd20: b.ls            #0x8ffd60
    // 0x8ffd24: r0 = InitLateStaticField(0x1704) // [package:permission_handler_platform_interface/permission_handler_platform_interface.dart] PermissionHandlerPlatform::_instance
    //     0x8ffd24: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ffd28: ldr             x0, [x0, #0x2e08]
    //     0x8ffd2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ffd30: cmp             w0, w16
    //     0x8ffd34: b.ne            #0x8ffd44
    //     0x8ffd38: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c798] Field <PermissionHandlerPlatform._instance@2604000480>: static late (offset: 0x1704)
    //     0x8ffd3c: ldr             x2, [x2, #0x798]
    //     0x8ffd40: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8ffd44: mov             x1, x0
    // 0x8ffd48: r2 = Instance_Permission
    //     0x8ffd48: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] Obj!Permission@e0c191
    //     0x8ffd4c: ldr             x2, [x2, #0x7a0]
    // 0x8ffd50: r0 = checkPermissionStatus()
    //     0x8ffd50: bl              #0x8ffd68  ; [package:permission_handler_platform_interface/src/method_channel/method_channel_permission_handler.dart] MethodChannelPermissionHandler::checkPermissionStatus
    // 0x8ffd54: LeaveFrame
    //     0x8ffd54: mov             SP, fp
    //     0x8ffd58: ldp             fp, lr, [SP], #0x10
    // 0x8ffd5c: ret
    //     0x8ffd5c: ret             
    // 0x8ffd60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ffd60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ffd64: b               #0x8ffd24
  }
  static _ PermissionActions.request(/* No info */) async {
    // ** addr: 0x900468, size: 0xb8
    // 0x900468: EnterFrame
    //     0x900468: stp             fp, lr, [SP, #-0x10]!
    //     0x90046c: mov             fp, SP
    // 0x900470: AllocStack(0x10)
    //     0x900470: sub             SP, SP, #0x10
    // 0x900474: SetupParameters()
    //     0x900474: stur            NULL, [fp, #-8]
    // 0x900478: CheckStackOverflow
    //     0x900478: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90047c: cmp             SP, x16
    //     0x900480: b.ls            #0x900518
    // 0x900484: InitAsync() -> Future<PermissionStatus>
    //     0x900484: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c7a8] TypeArguments: <PermissionStatus>
    //     0x900488: ldr             x0, [x0, #0x7a8]
    //     0x90048c: bl              #0x661298  ; InitAsyncStub
    // 0x900490: r1 = Null
    //     0x900490: mov             x1, NULL
    // 0x900494: r2 = 2
    //     0x900494: movz            x2, #0x2
    // 0x900498: r0 = AllocateArray()
    //     0x900498: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90049c: stur            x0, [fp, #-0x10]
    // 0x9004a0: r16 = Instance_Permission
    //     0x9004a0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33610] Obj!Permission@e0c3b1
    //     0x9004a4: ldr             x16, [x16, #0x610]
    // 0x9004a8: StoreField: r0->field_f = r16
    //     0x9004a8: stur            w16, [x0, #0xf]
    // 0x9004ac: r1 = <Permission>
    //     0x9004ac: add             x1, PP, #0x33, lsl #12  ; [pp+0x33618] TypeArguments: <Permission>
    //     0x9004b0: ldr             x1, [x1, #0x618]
    // 0x9004b4: r0 = AllocateGrowableArray()
    //     0x9004b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x9004b8: mov             x1, x0
    // 0x9004bc: ldur            x0, [fp, #-0x10]
    // 0x9004c0: StoreField: r1->field_f = r0
    //     0x9004c0: stur            w0, [x1, #0xf]
    // 0x9004c4: r0 = 2
    //     0x9004c4: movz            x0, #0x2
    // 0x9004c8: StoreField: r1->field_b = r0
    //     0x9004c8: stur            w0, [x1, #0xb]
    // 0x9004cc: r0 = PermissionListActions.request()
    //     0x9004cc: bl              #0x900520  ; [package:permission_handler/permission_handler.dart] ::PermissionListActions.request
    // 0x9004d0: mov             x1, x0
    // 0x9004d4: stur            x1, [fp, #-0x10]
    // 0x9004d8: r0 = Await()
    //     0x9004d8: bl              #0x661044  ; AwaitStub
    // 0x9004dc: r1 = LoadClassIdInstr(r0)
    //     0x9004dc: ldur            x1, [x0, #-1]
    //     0x9004e0: ubfx            x1, x1, #0xc, #0x14
    // 0x9004e4: mov             x16, x0
    // 0x9004e8: mov             x0, x1
    // 0x9004ec: mov             x1, x16
    // 0x9004f0: r2 = Instance_Permission
    //     0x9004f0: add             x2, PP, #0x33, lsl #12  ; [pp+0x33610] Obj!Permission@e0c3b1
    //     0x9004f4: ldr             x2, [x2, #0x610]
    // 0x9004f8: r0 = GDT[cid_x0 + -0x114]()
    //     0x9004f8: sub             lr, x0, #0x114
    //     0x9004fc: ldr             lr, [x21, lr, lsl #3]
    //     0x900500: blr             lr
    // 0x900504: cmp             w0, NULL
    // 0x900508: b.ne            #0x900514
    // 0x90050c: r0 = Instance_PermissionStatus
    //     0x90050c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c7d0] Obj!PermissionStatus@e2e481
    //     0x900510: ldr             x0, [x0, #0x7d0]
    // 0x900514: r0 = ReturnAsyncNotFuture()
    //     0x900514: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x900518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x900518: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90051c: b               #0x900484
  }
  static _ PermissionListActions.request(/* No info */) {
    // ** addr: 0x900520, size: 0x60
    // 0x900520: EnterFrame
    //     0x900520: stp             fp, lr, [SP, #-0x10]!
    //     0x900524: mov             fp, SP
    // 0x900528: AllocStack(0x8)
    //     0x900528: sub             SP, SP, #8
    // 0x90052c: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x90052c: mov             x2, x1
    //     0x900530: stur            x1, [fp, #-8]
    // 0x900534: CheckStackOverflow
    //     0x900534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x900538: cmp             SP, x16
    //     0x90053c: b.ls            #0x900578
    // 0x900540: r0 = InitLateStaticField(0x1704) // [package:permission_handler_platform_interface/permission_handler_platform_interface.dart] PermissionHandlerPlatform::_instance
    //     0x900540: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x900544: ldr             x0, [x0, #0x2e08]
    //     0x900548: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x90054c: cmp             w0, w16
    //     0x900550: b.ne            #0x900560
    //     0x900554: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c798] Field <PermissionHandlerPlatform._instance@2604000480>: static late (offset: 0x1704)
    //     0x900558: ldr             x2, [x2, #0x798]
    //     0x90055c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x900560: mov             x1, x0
    // 0x900564: ldur            x2, [fp, #-8]
    // 0x900568: r0 = requestPermissions()
    //     0x900568: bl              #0x900580  ; [package:permission_handler_platform_interface/src/method_channel/method_channel_permission_handler.dart] MethodChannelPermissionHandler::requestPermissions
    // 0x90056c: LeaveFrame
    //     0x90056c: mov             SP, fp
    //     0x900570: ldp             fp, lr, [SP], #0x10
    // 0x900574: ret
    //     0x900574: ret             
    // 0x900578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x900578: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90057c: b               #0x900540
  }
}
