// lib: , url: package:permission_handler_platform_interface/permission_handler_platform_interface.dart

// class id: 1050868, size: 0x8
class :: {

  static _ FuturePermissionStatusGetters.isGranted(/* No info */) async {
    // ** addr: 0x8ffc94, size: 0x58
    // 0x8ffc94: EnterFrame
    //     0x8ffc94: stp             fp, lr, [SP, #-0x10]!
    //     0x8ffc98: mov             fp, SP
    // 0x8ffc9c: AllocStack(0x10)
    //     0x8ffc9c: sub             SP, SP, #0x10
    // 0x8ffca0: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */)
    //     0x8ffca0: stur            NULL, [fp, #-8]
    //     0x8ffca4: stur            x1, [fp, #-0x10]
    // 0x8ffca8: CheckStackOverflow
    //     0x8ffca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ffcac: cmp             SP, x16
    //     0x8ffcb0: b.ls            #0x8ffce4
    // 0x8ffcb4: InitAsync() -> Future<bool>
    //     0x8ffcb4: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x8ffcb8: bl              #0x661298  ; InitAsyncStub
    // 0x8ffcbc: ldur            x0, [fp, #-0x10]
    // 0x8ffcc0: r0 = Await()
    //     0x8ffcc0: bl              #0x661044  ; AwaitStub
    // 0x8ffcc4: r16 = Instance_PermissionStatus
    //     0x8ffcc4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c790] Obj!PermissionStatus@e2e3e1
    //     0x8ffcc8: ldr             x16, [x16, #0x790]
    // 0x8ffccc: cmp             w0, w16
    // 0x8ffcd0: r16 = true
    //     0x8ffcd0: add             x16, NULL, #0x20  ; true
    // 0x8ffcd4: r17 = false
    //     0x8ffcd4: add             x17, NULL, #0x30  ; false
    // 0x8ffcd8: csel            x1, x16, x17, eq
    // 0x8ffcdc: mov             x0, x1
    // 0x8ffce0: r0 = ReturnAsyncNotFuture()
    //     0x8ffce0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ffce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ffce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ffce8: b               #0x8ffcb4
  }
  static _ PermissionStatusValue.statusByValue(/* No info */) {
    // ** addr: 0x8ffe1c, size: 0x9c
    // 0x8ffe1c: EnterFrame
    //     0x8ffe1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ffe20: mov             fp, SP
    // 0x8ffe24: AllocStack(0x8)
    //     0x8ffe24: sub             SP, SP, #8
    // 0x8ffe28: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x8ffe28: mov             x0, x1
    //     0x8ffe2c: stur            x1, [fp, #-8]
    // 0x8ffe30: r1 = Null
    //     0x8ffe30: mov             x1, NULL
    // 0x8ffe34: r2 = 12
    //     0x8ffe34: movz            x2, #0xc
    // 0x8ffe38: r0 = AllocateArray()
    //     0x8ffe38: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8ffe3c: mov             x2, x0
    // 0x8ffe40: r16 = Instance_PermissionStatus
    //     0x8ffe40: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7d0] Obj!PermissionStatus@e2e481
    //     0x8ffe44: ldr             x16, [x16, #0x7d0]
    // 0x8ffe48: StoreField: r2->field_f = r16
    //     0x8ffe48: stur            w16, [x2, #0xf]
    // 0x8ffe4c: r16 = Instance_PermissionStatus
    //     0x8ffe4c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c790] Obj!PermissionStatus@e2e3e1
    //     0x8ffe50: ldr             x16, [x16, #0x790]
    // 0x8ffe54: StoreField: r2->field_13 = r16
    //     0x8ffe54: stur            w16, [x2, #0x13]
    // 0x8ffe58: r16 = Instance_PermissionStatus
    //     0x8ffe58: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7d8] Obj!PermissionStatus@e2e461
    //     0x8ffe5c: ldr             x16, [x16, #0x7d8]
    // 0x8ffe60: ArrayStore: r2[0] = r16  ; List_4
    //     0x8ffe60: stur            w16, [x2, #0x17]
    // 0x8ffe64: r16 = Instance_PermissionStatus
    //     0x8ffe64: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7e0] Obj!PermissionStatus@e2e441
    //     0x8ffe68: ldr             x16, [x16, #0x7e0]
    // 0x8ffe6c: StoreField: r2->field_1b = r16
    //     0x8ffe6c: stur            w16, [x2, #0x1b]
    // 0x8ffe70: r16 = Instance_PermissionStatus
    //     0x8ffe70: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7e8] Obj!PermissionStatus@e2e421
    //     0x8ffe74: ldr             x16, [x16, #0x7e8]
    // 0x8ffe78: StoreField: r2->field_1f = r16
    //     0x8ffe78: stur            w16, [x2, #0x1f]
    // 0x8ffe7c: r16 = Instance_PermissionStatus
    //     0x8ffe7c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7f0] Obj!PermissionStatus@e2e401
    //     0x8ffe80: ldr             x16, [x16, #0x7f0]
    // 0x8ffe84: StoreField: r2->field_23 = r16
    //     0x8ffe84: stur            w16, [x2, #0x23]
    // 0x8ffe88: ldur            x1, [fp, #-8]
    // 0x8ffe8c: r0 = 6
    //     0x8ffe8c: movz            x0, #0x6
    // 0x8ffe90: cmp             x1, x0
    // 0x8ffe94: b.hs            #0x8ffeb4
    // 0x8ffe98: ldur            x1, [fp, #-8]
    // 0x8ffe9c: ArrayLoad: r0 = r2[r1]  ; Unknown_4
    //     0x8ffe9c: add             x16, x2, x1, lsl #2
    //     0x8ffea0: ldur            w0, [x16, #0xf]
    // 0x8ffea4: DecompressPointer r0
    //     0x8ffea4: add             x0, x0, HEAP, lsl #32
    // 0x8ffea8: LeaveFrame
    //     0x8ffea8: mov             SP, fp
    //     0x8ffeac: ldp             fp, lr, [SP], #0x10
    // 0x8ffeb0: ret
    //     0x8ffeb0: ret             
    // 0x8ffeb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8ffeb4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 757, size: 0x10, field offset: 0x8
//   const constructor, 
class Permission extends Object {

  _Mint field_8;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf1c3c, size: 0x50
    // 0xbf1c3c: ldr             x2, [SP]
    // 0xbf1c40: LoadField: r3 = r2->field_7
    //     0xbf1c40: ldur            x3, [x2, #7]
    // 0xbf1c44: r0 = BoxInt64Instr(r3)
    //     0xbf1c44: sbfiz           x0, x3, #1, #0x1f
    //     0xbf1c48: cmp             x3, x0, asr #1
    //     0xbf1c4c: b.eq            #0xbf1c68
    //     0xbf1c50: stp             fp, lr, [SP, #-0x10]!
    //     0xbf1c54: mov             fp, SP
    //     0xbf1c58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1c5c: mov             SP, fp
    //     0xbf1c60: ldp             fp, lr, [SP], #0x10
    //     0xbf1c64: stur            x3, [x0, #7]
    // 0xbf1c68: r16 = LoadInt32Instr(r0)
    //     0xbf1c68: sbfx            x16, x0, #1, #0x1f
    // 0xbf1c6c: r17 = 11601
    //     0xbf1c6c: movz            x17, #0x2d51
    // 0xbf1c70: mul             x1, x16, x17
    // 0xbf1c74: umulh           x16, x16, x17
    // 0xbf1c78: eor             x1, x1, x16
    // 0xbf1c7c: r1 = 0
    //     0xbf1c7c: eor             x1, x1, x1, lsr #32
    // 0xbf1c80: ubfiz           x1, x1, #1, #0x1e
    // 0xbf1c84: mov             x0, x1
    // 0xbf1c88: ret
    //     0xbf1c88: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3b5cc, size: 0x84
    // 0xc3b5cc: EnterFrame
    //     0xc3b5cc: stp             fp, lr, [SP, #-0x10]!
    //     0xc3b5d0: mov             fp, SP
    // 0xc3b5d4: AllocStack(0x8)
    //     0xc3b5d4: sub             SP, SP, #8
    // 0xc3b5d8: CheckStackOverflow
    //     0xc3b5d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3b5dc: cmp             SP, x16
    //     0xc3b5e0: b.ls            #0xc3b644
    // 0xc3b5e4: r1 = Null
    //     0xc3b5e4: mov             x1, NULL
    // 0xc3b5e8: r2 = 4
    //     0xc3b5e8: movz            x2, #0x4
    // 0xc3b5ec: r0 = AllocateArray()
    //     0xc3b5ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3b5f0: mov             x2, x0
    // 0xc3b5f4: r16 = "Permission."
    //     0xc3b5f4: add             x16, PP, #0x31, lsl #12  ; [pp+0x314a8] "Permission."
    //     0xc3b5f8: ldr             x16, [x16, #0x4a8]
    // 0xc3b5fc: StoreField: r2->field_f = r16
    //     0xc3b5fc: stur            w16, [x2, #0xf]
    // 0xc3b600: ldr             x0, [fp, #0x10]
    // 0xc3b604: LoadField: r3 = r0->field_7
    //     0xc3b604: ldur            x3, [x0, #7]
    // 0xc3b608: mov             x1, x3
    // 0xc3b60c: r0 = 40
    //     0xc3b60c: movz            x0, #0x28
    // 0xc3b610: cmp             x1, x0
    // 0xc3b614: b.hs            #0xc3b64c
    // 0xc3b618: r0 = const [calendar, camera, contacts, location, locationAlways, locationWhenInUse, mediaLibrary, microphone, phone, photos, photosAddOnly, reminders, sensors, sms, speech, storage, ignoreBatteryOptimizations, notification, access_media_location, activity_recognition, unknown, bluetooth, manageExternalStorage, systemAlertWindow, requestInstallPackages, appTrackingTransparency, criticalAlerts, accessNotificationPolicy, bluetoothScan, bluetoothAdvertise, bluetoothConnect, nearbyWifiDevices, videos, audio, scheduleExactAlarm, sensorsAlways, calendarWriteOnly, calendarFullAccess, assistant, backgroundRefresh]
    //     0xc3b618: add             x0, PP, #0x31, lsl #12  ; [pp+0x314b0] List<String>(40)
    //     0xc3b61c: ldr             x0, [x0, #0x4b0]
    // 0xc3b620: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xc3b620: add             x16, x0, x3, lsl #2
    //     0xc3b624: ldur            w1, [x16, #0xf]
    // 0xc3b628: DecompressPointer r1
    //     0xc3b628: add             x1, x1, HEAP, lsl #32
    // 0xc3b62c: StoreField: r2->field_13 = r1
    //     0xc3b62c: stur            w1, [x2, #0x13]
    // 0xc3b630: str             x2, [SP]
    // 0xc3b634: r0 = _interpolate()
    //     0xc3b634: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3b638: LeaveFrame
    //     0xc3b638: mov             SP, fp
    //     0xc3b63c: ldp             fp, lr, [SP], #0x10
    // 0xc3b640: ret
    //     0xc3b640: ret             
    // 0xc3b644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3b644: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3b648: b               #0xc3b5e4
    // 0xc3b64c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3b64c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7cf3c, size: 0xc8
    // 0xd7cf3c: EnterFrame
    //     0xd7cf3c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7cf40: mov             fp, SP
    // 0xd7cf44: AllocStack(0x10)
    //     0xd7cf44: sub             SP, SP, #0x10
    // 0xd7cf48: CheckStackOverflow
    //     0xd7cf48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7cf4c: cmp             SP, x16
    //     0xd7cf50: b.ls            #0xd7cffc
    // 0xd7cf54: ldr             x0, [fp, #0x10]
    // 0xd7cf58: cmp             w0, NULL
    // 0xd7cf5c: b.ne            #0xd7cf70
    // 0xd7cf60: r0 = false
    //     0xd7cf60: add             x0, NULL, #0x30  ; false
    // 0xd7cf64: LeaveFrame
    //     0xd7cf64: mov             SP, fp
    //     0xd7cf68: ldp             fp, lr, [SP], #0x10
    // 0xd7cf6c: ret
    //     0xd7cf6c: ret             
    // 0xd7cf70: ldr             x1, [fp, #0x18]
    // 0xd7cf74: cmp             w1, w0
    // 0xd7cf78: b.ne            #0xd7cf8c
    // 0xd7cf7c: r0 = true
    //     0xd7cf7c: add             x0, NULL, #0x20  ; true
    // 0xd7cf80: LeaveFrame
    //     0xd7cf80: mov             SP, fp
    //     0xd7cf84: ldp             fp, lr, [SP], #0x10
    // 0xd7cf88: ret
    //     0xd7cf88: ret             
    // 0xd7cf8c: stp             x1, x0, [SP]
    // 0xd7cf90: r0 = _haveSameRuntimeType()
    //     0xd7cf90: bl              #0x6c18d0  ; [dart:core] Object::_haveSameRuntimeType
    // 0xd7cf94: tbz             w0, #4, #0xd7cfa8
    // 0xd7cf98: r0 = false
    //     0xd7cf98: add             x0, NULL, #0x30  ; false
    // 0xd7cf9c: LeaveFrame
    //     0xd7cf9c: mov             SP, fp
    //     0xd7cfa0: ldp             fp, lr, [SP], #0x10
    // 0xd7cfa4: ret
    //     0xd7cfa4: ret             
    // 0xd7cfa8: ldr             x1, [fp, #0x10]
    // 0xd7cfac: r2 = 60
    //     0xd7cfac: movz            x2, #0x3c
    // 0xd7cfb0: branchIfSmi(r1, 0xd7cfbc)
    //     0xd7cfb0: tbz             w1, #0, #0xd7cfbc
    // 0xd7cfb4: r2 = LoadClassIdInstr(r1)
    //     0xd7cfb4: ldur            x2, [x1, #-1]
    //     0xd7cfb8: ubfx            x2, x2, #0xc, #0x14
    // 0xd7cfbc: sub             x16, x2, #0x2f5
    // 0xd7cfc0: cmp             x16, #1
    // 0xd7cfc4: b.hi            #0xd7cfec
    // 0xd7cfc8: ldr             x2, [fp, #0x18]
    // 0xd7cfcc: LoadField: r3 = r1->field_7
    //     0xd7cfcc: ldur            x3, [x1, #7]
    // 0xd7cfd0: LoadField: r1 = r2->field_7
    //     0xd7cfd0: ldur            x1, [x2, #7]
    // 0xd7cfd4: cmp             x3, x1
    // 0xd7cfd8: r16 = true
    //     0xd7cfd8: add             x16, NULL, #0x20  ; true
    // 0xd7cfdc: r17 = false
    //     0xd7cfdc: add             x17, NULL, #0x30  ; false
    // 0xd7cfe0: csel            x2, x16, x17, eq
    // 0xd7cfe4: mov             x0, x2
    // 0xd7cfe8: b               #0xd7cff0
    // 0xd7cfec: r0 = false
    //     0xd7cfec: add             x0, NULL, #0x30  ; false
    // 0xd7cff0: LeaveFrame
    //     0xd7cff0: mov             SP, fp
    //     0xd7cff4: ldp             fp, lr, [SP], #0x10
    // 0xd7cff8: ret
    //     0xd7cff8: ret             
    // 0xd7cffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7cffc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7d000: b               #0xd7cf54
  }
}

// class id: 758, size: 0x10, field offset: 0x10
//   const constructor, 
class PermissionWithService extends Permission {

  _Mint field_8;
}

// class id: 5871, size: 0x8, field offset: 0x8
abstract class PermissionHandlerPlatform extends PlatformInterface {

  static late PermissionHandlerPlatform _instance; // offset: 0x1704
  static late final Object _token; // offset: 0x1700

  static PermissionHandlerPlatform _instance() {
    // ** addr: 0x8ffedc, size: 0x8c
    // 0x8ffedc: EnterFrame
    //     0x8ffedc: stp             fp, lr, [SP, #-0x10]!
    //     0x8ffee0: mov             fp, SP
    // 0x8ffee4: AllocStack(0x10)
    //     0x8ffee4: sub             SP, SP, #0x10
    // 0x8ffee8: CheckStackOverflow
    //     0x8ffee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ffeec: cmp             SP, x16
    //     0x8ffef0: b.ls            #0x8fff60
    // 0x8ffef4: r0 = InitLateStaticField(0x1700) // [package:permission_handler_platform_interface/permission_handler_platform_interface.dart] PermissionHandlerPlatform::_token
    //     0x8ffef4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ffef8: ldr             x0, [x0, #0x2e00]
    //     0x8ffefc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8fff00: cmp             w0, w16
    //     0x8fff04: b.ne            #0x8fff14
    //     0x8fff08: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c7f8] Field <PermissionHandlerPlatform._token@2604000480>: static late final (offset: 0x1700)
    //     0x8fff0c: ldr             x2, [x2, #0x7f8]
    //     0x8fff10: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8fff14: stur            x0, [fp, #-8]
    // 0x8fff18: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x8fff18: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8fff1c: ldr             x0, [x0, #0xc08]
    //     0x8fff20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8fff24: cmp             w0, w16
    //     0x8fff28: b.ne            #0x8fff34
    //     0x8fff2c: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x8fff30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8fff34: stur            x0, [fp, #-0x10]
    // 0x8fff38: r0 = MethodChannelPermissionHandler()
    //     0x8fff38: bl              #0x8fff68  ; AllocateMethodChannelPermissionHandlerStub -> MethodChannelPermissionHandler (size=0x8)
    // 0x8fff3c: ldur            x1, [fp, #-0x10]
    // 0x8fff40: mov             x2, x0
    // 0x8fff44: ldur            x3, [fp, #-8]
    // 0x8fff48: stur            x0, [fp, #-8]
    // 0x8fff4c: r0 = []=()
    //     0x8fff4c: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x8fff50: ldur            x0, [fp, #-8]
    // 0x8fff54: LeaveFrame
    //     0x8fff54: mov             SP, fp
    //     0x8fff58: ldp             fp, lr, [SP], #0x10
    // 0x8fff5c: ret
    //     0x8fff5c: ret             
    // 0x8fff60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fff60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fff64: b               #0x8ffef4
  }
}

// class id: 6784, size: 0x14, field offset: 0x14
enum PermissionStatus extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e4a0, size: 0x64
    // 0xc4e4a0: EnterFrame
    //     0xc4e4a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e4a4: mov             fp, SP
    // 0xc4e4a8: AllocStack(0x10)
    //     0xc4e4a8: sub             SP, SP, #0x10
    // 0xc4e4ac: SetupParameters(PermissionStatus this /* r1 => r0, fp-0x8 */)
    //     0xc4e4ac: mov             x0, x1
    //     0xc4e4b0: stur            x1, [fp, #-8]
    // 0xc4e4b4: CheckStackOverflow
    //     0xc4e4b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e4b8: cmp             SP, x16
    //     0xc4e4bc: b.ls            #0xc4e4fc
    // 0xc4e4c0: r1 = Null
    //     0xc4e4c0: mov             x1, NULL
    // 0xc4e4c4: r2 = 4
    //     0xc4e4c4: movz            x2, #0x4
    // 0xc4e4c8: r0 = AllocateArray()
    //     0xc4e4c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e4cc: r16 = "PermissionStatus."
    //     0xc4e4cc: add             x16, PP, #0x31, lsl #12  ; [pp+0x314b8] "PermissionStatus."
    //     0xc4e4d0: ldr             x16, [x16, #0x4b8]
    // 0xc4e4d4: StoreField: r0->field_f = r16
    //     0xc4e4d4: stur            w16, [x0, #0xf]
    // 0xc4e4d8: ldur            x1, [fp, #-8]
    // 0xc4e4dc: LoadField: r2 = r1->field_f
    //     0xc4e4dc: ldur            w2, [x1, #0xf]
    // 0xc4e4e0: DecompressPointer r2
    //     0xc4e4e0: add             x2, x2, HEAP, lsl #32
    // 0xc4e4e4: StoreField: r0->field_13 = r2
    //     0xc4e4e4: stur            w2, [x0, #0x13]
    // 0xc4e4e8: str             x0, [SP]
    // 0xc4e4ec: r0 = _interpolate()
    //     0xc4e4ec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e4f0: LeaveFrame
    //     0xc4e4f0: mov             SP, fp
    //     0xc4e4f4: ldp             fp, lr, [SP], #0x10
    // 0xc4e4f8: ret
    //     0xc4e4f8: ret             
    // 0xc4e4fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e4fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e500: b               #0xc4e4c0
  }
}
