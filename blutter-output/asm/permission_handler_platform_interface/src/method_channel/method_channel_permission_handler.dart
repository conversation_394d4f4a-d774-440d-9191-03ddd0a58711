// lib: , url: package:permission_handler_platform_interface/src/method_channel/method_channel_permission_handler.dart

// class id: 1050869, size: 0x8
class :: {
}

// class id: 5872, size: 0x8, field offset: 0x8
class MethodChannelPermissionHandler extends PermissionHandlerPlatform {

  _ checkPermissionStatus(/* No info */) async {
    // ** addr: 0x8ffd68, size: 0xb4
    // 0x8ffd68: EnterFrame
    //     0x8ffd68: stp             fp, lr, [SP, #-0x10]!
    //     0x8ffd6c: mov             fp, SP
    // 0x8ffd70: AllocStack(0x38)
    //     0x8ffd70: sub             SP, SP, #0x38
    // 0x8ffd74: SetupParameters(MethodChannelPermissionHandler this /* r1 => r1, fp-0x10 */)
    //     0x8ffd74: stur            NULL, [fp, #-8]
    //     0x8ffd78: stur            x1, [fp, #-0x10]
    // 0x8ffd7c: CheckStackOverflow
    //     0x8ffd7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ffd80: cmp             SP, x16
    //     0x8ffd84: b.ls            #0x8ffe14
    // 0x8ffd88: InitAsync() -> Future<PermissionStatus>
    //     0x8ffd88: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c7a8] TypeArguments: <PermissionStatus>
    //     0x8ffd8c: ldr             x0, [x0, #0x7a8]
    //     0x8ffd90: bl              #0x661298  ; InitAsyncStub
    // 0x8ffd94: r16 = Instance_MethodChannel
    //     0x8ffd94: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7b0] Obj!MethodChannel@e111b1
    //     0x8ffd98: ldr             x16, [x16, #0x7b0]
    // 0x8ffd9c: stp             x16, NULL, [SP, #0x10]
    // 0x8ffda0: r16 = "checkPermissionStatus"
    //     0x8ffda0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7b8] "checkPermissionStatus"
    //     0x8ffda4: ldr             x16, [x16, #0x7b8]
    // 0x8ffda8: r30 = 34
    //     0x8ffda8: movz            lr, #0x22
    // 0x8ffdac: stp             lr, x16, [SP]
    // 0x8ffdb0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8ffdb0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8ffdb4: r0 = invokeMethod()
    //     0x8ffdb4: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x8ffdb8: mov             x1, x0
    // 0x8ffdbc: stur            x1, [fp, #-0x18]
    // 0x8ffdc0: r0 = Await()
    //     0x8ffdc0: bl              #0x661044  ; AwaitStub
    // 0x8ffdc4: mov             x3, x0
    // 0x8ffdc8: r2 = Null
    //     0x8ffdc8: mov             x2, NULL
    // 0x8ffdcc: r1 = Null
    //     0x8ffdcc: mov             x1, NULL
    // 0x8ffdd0: stur            x3, [fp, #-0x10]
    // 0x8ffdd4: branchIfSmi(r0, 0x8ffdfc)
    //     0x8ffdd4: tbz             w0, #0, #0x8ffdfc
    // 0x8ffdd8: r4 = LoadClassIdInstr(r0)
    //     0x8ffdd8: ldur            x4, [x0, #-1]
    //     0x8ffddc: ubfx            x4, x4, #0xc, #0x14
    // 0x8ffde0: sub             x4, x4, #0x3c
    // 0x8ffde4: cmp             x4, #1
    // 0x8ffde8: b.ls            #0x8ffdfc
    // 0x8ffdec: r8 = int
    //     0x8ffdec: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ffdf0: r3 = Null
    //     0x8ffdf0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c7c0] Null
    //     0x8ffdf4: ldr             x3, [x3, #0x7c0]
    // 0x8ffdf8: r0 = int()
    //     0x8ffdf8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ffdfc: ldur            x0, [fp, #-0x10]
    // 0x8ffe00: r1 = LoadInt32Instr(r0)
    //     0x8ffe00: sbfx            x1, x0, #1, #0x1f
    //     0x8ffe04: tbz             w0, #0, #0x8ffe0c
    //     0x8ffe08: ldur            x1, [x0, #7]
    // 0x8ffe0c: r0 = PermissionStatusValue.statusByValue()
    //     0x8ffe0c: bl              #0x8ffe1c  ; [package:permission_handler_platform_interface/permission_handler_platform_interface.dart] ::PermissionStatusValue.statusByValue
    // 0x8ffe10: r0 = ReturnAsyncNotFuture()
    //     0x8ffe10: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ffe14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ffe14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ffe18: b               #0x8ffd88
  }
  _ requestPermissions(/* No info */) async {
    // ** addr: 0x900580, size: 0xb0
    // 0x900580: EnterFrame
    //     0x900580: stp             fp, lr, [SP, #-0x10]!
    //     0x900584: mov             fp, SP
    // 0x900588: AllocStack(0x38)
    //     0x900588: sub             SP, SP, #0x38
    // 0x90058c: SetupParameters(MethodChannelPermissionHandler this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x90058c: stur            NULL, [fp, #-8]
    //     0x900590: stur            x1, [fp, #-0x10]
    //     0x900594: mov             x16, x2
    //     0x900598: mov             x2, x1
    //     0x90059c: mov             x1, x16
    //     0x9005a0: stur            x1, [fp, #-0x18]
    // 0x9005a4: CheckStackOverflow
    //     0x9005a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9005a8: cmp             SP, x16
    //     0x9005ac: b.ls            #0x900628
    // 0x9005b0: InitAsync() -> Future<Map<Permission, PermissionStatus>>
    //     0x9005b0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33620] TypeArguments: <Map<Permission, PermissionStatus>>
    //     0x9005b4: ldr             x0, [x0, #0x620]
    //     0x9005b8: bl              #0x661298  ; InitAsyncStub
    // 0x9005bc: ldur            x1, [fp, #-0x18]
    // 0x9005c0: r0 = encodePermissions()
    //     0x9005c0: bl              #0x900730  ; [package:permission_handler_platform_interface/src/method_channel/utils/codec.dart] ::encodePermissions
    // 0x9005c4: r16 = Instance_MethodChannel
    //     0x9005c4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7b0] Obj!MethodChannel@e111b1
    //     0x9005c8: ldr             x16, [x16, #0x7b0]
    // 0x9005cc: stp             x16, NULL, [SP, #0x10]
    // 0x9005d0: r16 = "requestPermissions"
    //     0x9005d0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33628] "requestPermissions"
    //     0x9005d4: ldr             x16, [x16, #0x628]
    // 0x9005d8: stp             x0, x16, [SP]
    // 0x9005dc: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x9005dc: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x9005e0: r0 = invokeMethod()
    //     0x9005e0: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x9005e4: mov             x1, x0
    // 0x9005e8: stur            x1, [fp, #-0x18]
    // 0x9005ec: r0 = Await()
    //     0x9005ec: bl              #0x661044  ; AwaitStub
    // 0x9005f0: mov             x3, x0
    // 0x9005f4: r2 = Null
    //     0x9005f4: mov             x2, NULL
    // 0x9005f8: r1 = Null
    //     0x9005f8: mov             x1, NULL
    // 0x9005fc: stur            x3, [fp, #-0x10]
    // 0x900600: r8 = Map
    //     0x900600: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0x900604: r3 = Null
    //     0x900604: add             x3, PP, #0x33, lsl #12  ; [pp+0x33630] Null
    //     0x900608: ldr             x3, [x3, #0x630]
    // 0x90060c: r0 = Map()
    //     0x90060c: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0x900610: ldur            x2, [fp, #-0x10]
    // 0x900614: r1 = <int, int>
    //     0x900614: ldr             x1, [PP, #0x28b0]  ; [pp+0x28b0] TypeArguments: <int, int>
    // 0x900618: r0 = LinkedHashMap.from()
    //     0x900618: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0x90061c: mov             x1, x0
    // 0x900620: r0 = decodePermissionRequestResult()
    //     0x900620: bl              #0x900630  ; [package:permission_handler_platform_interface/src/method_channel/utils/codec.dart] ::decodePermissionRequestResult
    // 0x900624: r0 = ReturnAsyncNotFuture()
    //     0x900624: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x900628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x900628: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90062c: b               #0x9005b0
  }
}
