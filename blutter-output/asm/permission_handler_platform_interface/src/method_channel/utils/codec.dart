// lib: , url: package:permission_handler_platform_interface/src/method_channel/utils/codec.dart

// class id: 1050870, size: 0x8
class :: {

  static _ decodePermissionRequestResult(/* No info */) {
    // ** addr: 0x900630, size: 0x60
    // 0x900630: EnterFrame
    //     0x900630: stp             fp, lr, [SP, #-0x10]!
    //     0x900634: mov             fp, SP
    // 0x900638: AllocStack(0x20)
    //     0x900638: sub             SP, SP, #0x20
    // 0x90063c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x90063c: mov             x0, x1
    //     0x900640: stur            x1, [fp, #-8]
    // 0x900644: CheckStackOverflow
    //     0x900644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x900648: cmp             SP, x16
    //     0x90064c: b.ls            #0x900688
    // 0x900650: r1 = Function '<anonymous closure>': static.
    //     0x900650: add             x1, PP, #0x33, lsl #12  ; [pp+0x33640] AnonymousClosure: static (0x900690), in [package:permission_handler_platform_interface/src/method_channel/utils/codec.dart] ::decodePermissionRequestResult (0x900630)
    //     0x900654: ldr             x1, [x1, #0x640]
    // 0x900658: r2 = Null
    //     0x900658: mov             x2, NULL
    // 0x90065c: r0 = AllocateClosure()
    //     0x90065c: bl              #0xec1630  ; AllocateClosureStub
    // 0x900660: r16 = <Permission, PermissionStatus>
    //     0x900660: add             x16, PP, #0x33, lsl #12  ; [pp+0x33648] TypeArguments: <Permission, PermissionStatus>
    //     0x900664: ldr             x16, [x16, #0x648]
    // 0x900668: ldur            lr, [fp, #-8]
    // 0x90066c: stp             lr, x16, [SP, #8]
    // 0x900670: str             x0, [SP]
    // 0x900674: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x900674: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x900678: r0 = map()
    //     0x900678: bl              #0x766c60  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::map
    // 0x90067c: LeaveFrame
    //     0x90067c: mov             SP, fp
    //     0x900680: ldp             fp, lr, [SP], #0x10
    // 0x900684: ret
    //     0x900684: ret             
    // 0x900688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x900688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90068c: b               #0x900650
  }
  [closure] static MapEntry<Permission, PermissionStatus> <anonymous closure>(dynamic, int, int) {
    // ** addr: 0x900690, size: 0xa0
    // 0x900690: EnterFrame
    //     0x900690: stp             fp, lr, [SP, #-0x10]!
    //     0x900694: mov             fp, SP
    // 0x900698: AllocStack(0x10)
    //     0x900698: sub             SP, SP, #0x10
    // 0x90069c: SetupParameters()
    //     0x90069c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33650] List<Permission>(40)
    //     0x9006a0: ldr             x2, [x2, #0x650]
    // 0x90069c: r2 = const [Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'PermissionWithService', Instance of 'PermissionWithService', Instance of 'PermissionWithService', Instance of 'Permission', Instance of 'Permission', Instance of 'PermissionWithService', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'PermissionWithService', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission', Instance of 'Permission']
    // 0x9006a4: CheckStackOverflow
    //     0x9006a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9006a8: cmp             SP, x16
    //     0x9006ac: b.ls            #0x900724
    // 0x9006b0: ldr             x0, [fp, #0x18]
    // 0x9006b4: r3 = LoadInt32Instr(r0)
    //     0x9006b4: sbfx            x3, x0, #1, #0x1f
    //     0x9006b8: tbz             w0, #0, #0x9006c0
    //     0x9006bc: ldur            x3, [x0, #7]
    // 0x9006c0: mov             x1, x3
    // 0x9006c4: r0 = 40
    //     0x9006c4: movz            x0, #0x28
    // 0x9006c8: cmp             x1, x0
    // 0x9006cc: b.hs            #0x90072c
    // 0x9006d0: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0x9006d0: add             x16, x2, x3, lsl #2
    //     0x9006d4: ldur            w0, [x16, #0xf]
    // 0x9006d8: DecompressPointer r0
    //     0x9006d8: add             x0, x0, HEAP, lsl #32
    // 0x9006dc: ldr             x1, [fp, #0x10]
    // 0x9006e0: stur            x0, [fp, #-8]
    // 0x9006e4: r2 = LoadInt32Instr(r1)
    //     0x9006e4: sbfx            x2, x1, #1, #0x1f
    //     0x9006e8: tbz             w1, #0, #0x9006f0
    //     0x9006ec: ldur            x2, [x1, #7]
    // 0x9006f0: mov             x1, x2
    // 0x9006f4: r0 = PermissionStatusValue.statusByValue()
    //     0x9006f4: bl              #0x8ffe1c  ; [package:permission_handler_platform_interface/permission_handler_platform_interface.dart] ::PermissionStatusValue.statusByValue
    // 0x9006f8: r1 = <Permission, PermissionStatus>
    //     0x9006f8: add             x1, PP, #0x33, lsl #12  ; [pp+0x33648] TypeArguments: <Permission, PermissionStatus>
    //     0x9006fc: ldr             x1, [x1, #0x648]
    // 0x900700: stur            x0, [fp, #-0x10]
    // 0x900704: r0 = MapEntry()
    //     0x900704: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x900708: ldur            x1, [fp, #-8]
    // 0x90070c: StoreField: r0->field_b = r1
    //     0x90070c: stur            w1, [x0, #0xb]
    // 0x900710: ldur            x1, [fp, #-0x10]
    // 0x900714: StoreField: r0->field_f = r1
    //     0x900714: stur            w1, [x0, #0xf]
    // 0x900718: LeaveFrame
    //     0x900718: mov             SP, fp
    //     0x90071c: ldp             fp, lr, [SP], #0x10
    // 0x900720: ret
    //     0x900720: ret             
    // 0x900724: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x900724: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x900728: b               #0x9006b0
    // 0x90072c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x90072c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ encodePermissions(/* No info */) {
    // ** addr: 0x900730, size: 0x6c
    // 0x900730: EnterFrame
    //     0x900730: stp             fp, lr, [SP, #-0x10]!
    //     0x900734: mov             fp, SP
    // 0x900738: AllocStack(0x20)
    //     0x900738: sub             SP, SP, #0x20
    // 0x90073c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x90073c: mov             x0, x1
    //     0x900740: stur            x1, [fp, #-8]
    // 0x900744: CheckStackOverflow
    //     0x900744: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x900748: cmp             SP, x16
    //     0x90074c: b.ls            #0x900794
    // 0x900750: r1 = Function '<anonymous closure>': static.
    //     0x900750: add             x1, PP, #0x33, lsl #12  ; [pp+0x33658] Function: [dart:io] _ResourceHandleImpl::_handle (0xbee348)
    //     0x900754: ldr             x1, [x1, #0x658]
    // 0x900758: r2 = Null
    //     0x900758: mov             x2, NULL
    // 0x90075c: r0 = AllocateClosure()
    //     0x90075c: bl              #0xec1630  ; AllocateClosureStub
    // 0x900760: r16 = <int>
    //     0x900760: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x900764: ldur            lr, [fp, #-8]
    // 0x900768: stp             lr, x16, [SP, #8]
    // 0x90076c: str             x0, [SP]
    // 0x900770: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x900770: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x900774: r0 = map()
    //     0x900774: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x900778: LoadField: r1 = r0->field_7
    //     0x900778: ldur            w1, [x0, #7]
    // 0x90077c: DecompressPointer r1
    //     0x90077c: add             x1, x1, HEAP, lsl #32
    // 0x900780: mov             x2, x0
    // 0x900784: r0 = _GrowableList.of()
    //     0x900784: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x900788: LeaveFrame
    //     0x900788: mov             SP, fp
    //     0x90078c: ldp             fp, lr, [SP], #0x10
    // 0x900790: ret
    //     0x900790: ret             
    // 0x900794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x900794: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x900798: b               #0x900750
  }
}
