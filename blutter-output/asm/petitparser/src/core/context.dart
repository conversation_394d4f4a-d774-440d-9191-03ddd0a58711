// lib: , url: package:petitparser/src/core/context.dart

// class id: 1050871, size: 0x8
class :: {
}

// class id: 753, size: 0x14, field offset: 0x8
//   const constructor, 
class Context extends Object {

  _ toPositionString(/* No info */) {
    // ** addr: 0xc3b718, size: 0x3c
    // 0xc3b718: EnterFrame
    //     0xc3b718: stp             fp, lr, [SP, #-0x10]!
    //     0xc3b71c: mov             fp, SP
    // 0xc3b720: CheckStackOverflow
    //     0xc3b720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3b724: cmp             SP, x16
    //     0xc3b728: b.ls            #0xc3b74c
    // 0xc3b72c: LoadField: r0 = r1->field_7
    //     0xc3b72c: ldur            w0, [x1, #7]
    // 0xc3b730: DecompressPointer r0
    //     0xc3b730: add             x0, x0, HEAP, lsl #32
    // 0xc3b734: LoadField: r2 = r1->field_b
    //     0xc3b734: ldur            x2, [x1, #0xb]
    // 0xc3b738: mov             x1, x0
    // 0xc3b73c: r0 = positionString()
    //     0xc3b73c: bl              #0xc3b754  ; [package:petitparser/src/core/token.dart] Token::positionString
    // 0xc3b740: LeaveFrame
    //     0xc3b740: mov             SP, fp
    //     0xc3b744: ldp             fp, lr, [SP], #0x10
    // 0xc3b748: ret
    //     0xc3b748: ret             
    // 0xc3b74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3b74c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3b750: b               #0xc3b72c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3bc1c, size: 0x8c
    // 0xc3bc1c: EnterFrame
    //     0xc3bc1c: stp             fp, lr, [SP, #-0x10]!
    //     0xc3bc20: mov             fp, SP
    // 0xc3bc24: AllocStack(0x10)
    //     0xc3bc24: sub             SP, SP, #0x10
    // 0xc3bc28: CheckStackOverflow
    //     0xc3bc28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3bc2c: cmp             SP, x16
    //     0xc3bc30: b.ls            #0xc3bca0
    // 0xc3bc34: r1 = Null
    //     0xc3bc34: mov             x1, NULL
    // 0xc3bc38: r2 = 6
    //     0xc3bc38: movz            x2, #0x6
    // 0xc3bc3c: r0 = AllocateArray()
    //     0xc3bc3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3bc40: stur            x0, [fp, #-8]
    // 0xc3bc44: r16 = "Context["
    //     0xc3bc44: add             x16, PP, #0x31, lsl #12  ; [pp+0x314a0] "Context["
    //     0xc3bc48: ldr             x16, [x16, #0x4a0]
    // 0xc3bc4c: StoreField: r0->field_f = r16
    //     0xc3bc4c: stur            w16, [x0, #0xf]
    // 0xc3bc50: ldr             x1, [fp, #0x10]
    // 0xc3bc54: r0 = toPositionString()
    //     0xc3bc54: bl              #0xc3b718  ; [package:petitparser/src/core/context.dart] Context::toPositionString
    // 0xc3bc58: ldur            x1, [fp, #-8]
    // 0xc3bc5c: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3bc5c: add             x25, x1, #0x13
    //     0xc3bc60: str             w0, [x25]
    //     0xc3bc64: tbz             w0, #0, #0xc3bc80
    //     0xc3bc68: ldurb           w16, [x1, #-1]
    //     0xc3bc6c: ldurb           w17, [x0, #-1]
    //     0xc3bc70: and             x16, x17, x16, lsr #2
    //     0xc3bc74: tst             x16, HEAP, lsr #32
    //     0xc3bc78: b.eq            #0xc3bc80
    //     0xc3bc7c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3bc80: ldur            x0, [fp, #-8]
    // 0xc3bc84: r16 = "]"
    //     0xc3bc84: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc3bc88: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3bc88: stur            w16, [x0, #0x17]
    // 0xc3bc8c: str             x0, [SP]
    // 0xc3bc90: r0 = _interpolate()
    //     0xc3bc90: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3bc94: LeaveFrame
    //     0xc3bc94: mov             SP, fp
    //     0xc3bc98: ldp             fp, lr, [SP], #0x10
    // 0xc3bc9c: ret
    //     0xc3bc9c: ret             
    // 0xc3bca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3bca0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3bca4: b               #0xc3bc34
  }
  _ success(/* No info */) {
    // ** addr: 0xeb1004, size: 0xc0
    // 0xeb1004: EnterFrame
    //     0xeb1004: stp             fp, lr, [SP, #-0x10]!
    //     0xeb1008: mov             fp, SP
    // 0xeb100c: AllocStack(0x18)
    //     0xeb100c: sub             SP, SP, #0x18
    // 0xeb1010: SetupParameters(Context this /* r0 */, dynamic _ /* r2, fp-0x18 */, [dynamic _ = Null /* r1 */])
    //     0xeb1010: ldur            w0, [x4, #0x13]
    //     0xeb1014: sub             x1, x0, #4
    //     0xeb1018: add             x0, fp, w1, sxtw #2
    //     0xeb101c: ldr             x0, [x0, #0x18]
    //     0xeb1020: add             x2, fp, w1, sxtw #2
    //     0xeb1024: ldr             x2, [x2, #0x10]
    //     0xeb1028: stur            x2, [fp, #-0x18]
    //     0xeb102c: cmp             w1, #2
    //     0xeb1030: b.lt            #0xeb1044
    //     0xeb1034: add             x3, fp, w1, sxtw #2
    //     0xeb1038: ldr             x3, [x3, #8]
    //     0xeb103c: mov             x1, x3
    //     0xeb1040: b               #0xeb1048
    //     0xeb1044: mov             x1, NULL
    //     0xeb1048: ldur            w3, [x4, #0xf]
    //     0xeb104c: cbnz            w3, #0xeb1058
    //     0xeb1050: mov             x3, NULL
    //     0xeb1054: b               #0xeb1068
    //     0xeb1058: ldur            w3, [x4, #0x17]
    //     0xeb105c: add             x4, fp, w3, sxtw #2
    //     0xeb1060: ldr             x4, [x4, #0x10]
    //     0xeb1064: mov             x3, x4
    // 0xeb1068: LoadField: r4 = r0->field_7
    //     0xeb1068: ldur            w4, [x0, #7]
    // 0xeb106c: DecompressPointer r4
    //     0xeb106c: add             x4, x4, HEAP, lsl #32
    // 0xeb1070: stur            x4, [fp, #-0x10]
    // 0xeb1074: cmp             w1, NULL
    // 0xeb1078: b.ne            #0xeb1088
    // 0xeb107c: LoadField: r1 = r0->field_b
    //     0xeb107c: ldur            x1, [x0, #0xb]
    // 0xeb1080: mov             x0, x1
    // 0xeb1084: b               #0xeb1094
    // 0xeb1088: r0 = LoadInt32Instr(r1)
    //     0xeb1088: sbfx            x0, x1, #1, #0x1f
    //     0xeb108c: tbz             w1, #0, #0xeb1094
    //     0xeb1090: ldur            x0, [x1, #7]
    // 0xeb1094: mov             x1, x3
    // 0xeb1098: stur            x0, [fp, #-8]
    // 0xeb109c: r0 = Success()
    //     0xeb109c: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb10a0: ldur            x1, [fp, #-0x18]
    // 0xeb10a4: ArrayStore: r0[0] = r1  ; List_4
    //     0xeb10a4: stur            w1, [x0, #0x17]
    // 0xeb10a8: ldur            x1, [fp, #-0x10]
    // 0xeb10ac: StoreField: r0->field_7 = r1
    //     0xeb10ac: stur            w1, [x0, #7]
    // 0xeb10b0: ldur            x1, [fp, #-8]
    // 0xeb10b4: StoreField: r0->field_b = r1
    //     0xeb10b4: stur            x1, [x0, #0xb]
    // 0xeb10b8: LeaveFrame
    //     0xeb10b8: mov             SP, fp
    //     0xeb10bc: ldp             fp, lr, [SP], #0x10
    // 0xeb10c0: ret
    //     0xeb10c0: ret             
  }
}
