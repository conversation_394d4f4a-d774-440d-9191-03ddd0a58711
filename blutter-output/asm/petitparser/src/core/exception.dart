// lib: , url: package:petitparser/src/core/exception.dart

// class id: 1050872, size: 0x8
class :: {
}

// class id: 752, size: 0xc, field offset: 0x8
//   const constructor, 
class ParserException extends Object
    implements FormatException {

  _ toString(/* No info */) {
    // ** addr: 0xc3bca8, size: 0xc4
    // 0xc3bca8: EnterFrame
    //     0xc3bca8: stp             fp, lr, [SP, #-0x10]!
    //     0xc3bcac: mov             fp, SP
    // 0xc3bcb0: AllocStack(0x18)
    //     0xc3bcb0: sub             SP, SP, #0x18
    // 0xc3bcb4: CheckStackOverflow
    //     0xc3bcb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3bcb8: cmp             SP, x16
    //     0xc3bcbc: b.ls            #0xc3bd64
    // 0xc3bcc0: ldr             x16, [fp, #0x10]
    // 0xc3bcc4: str             x16, [SP]
    // 0xc3bcc8: r0 = toString()
    //     0xc3bcc8: bl              #0xc467e8  ; [dart:core] Object::toString
    // 0xc3bccc: r1 = Null
    //     0xc3bccc: mov             x1, NULL
    // 0xc3bcd0: r2 = 12
    //     0xc3bcd0: movz            x2, #0xc
    // 0xc3bcd4: stur            x0, [fp, #-8]
    // 0xc3bcd8: r0 = AllocateArray()
    //     0xc3bcd8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3bcdc: mov             x2, x0
    // 0xc3bce0: ldur            x0, [fp, #-8]
    // 0xc3bce4: stur            x2, [fp, #-0x10]
    // 0xc3bce8: StoreField: r2->field_f = r0
    //     0xc3bce8: stur            w0, [x2, #0xf]
    // 0xc3bcec: r16 = ": "
    //     0xc3bcec: ldr             x16, [PP, #0x7d8]  ; [pp+0x7d8] ": "
    // 0xc3bcf0: StoreField: r2->field_13 = r16
    //     0xc3bcf0: stur            w16, [x2, #0x13]
    // 0xc3bcf4: ldr             x0, [fp, #0x10]
    // 0xc3bcf8: LoadField: r1 = r0->field_7
    //     0xc3bcf8: ldur            w1, [x0, #7]
    // 0xc3bcfc: DecompressPointer r1
    //     0xc3bcfc: add             x1, x1, HEAP, lsl #32
    // 0xc3bd00: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc3bd00: ldur            w0, [x1, #0x17]
    // 0xc3bd04: DecompressPointer r0
    //     0xc3bd04: add             x0, x0, HEAP, lsl #32
    // 0xc3bd08: ArrayStore: r2[0] = r0  ; List_4
    //     0xc3bd08: stur            w0, [x2, #0x17]
    // 0xc3bd0c: r16 = " (at "
    //     0xc3bd0c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31460] " (at "
    //     0xc3bd10: ldr             x16, [x16, #0x460]
    // 0xc3bd14: StoreField: r2->field_1b = r16
    //     0xc3bd14: stur            w16, [x2, #0x1b]
    // 0xc3bd18: r0 = toPositionString()
    //     0xc3bd18: bl              #0xc3b718  ; [package:petitparser/src/core/context.dart] Context::toPositionString
    // 0xc3bd1c: ldur            x1, [fp, #-0x10]
    // 0xc3bd20: ArrayStore: r1[4] = r0  ; List_4
    //     0xc3bd20: add             x25, x1, #0x1f
    //     0xc3bd24: str             w0, [x25]
    //     0xc3bd28: tbz             w0, #0, #0xc3bd44
    //     0xc3bd2c: ldurb           w16, [x1, #-1]
    //     0xc3bd30: ldurb           w17, [x0, #-1]
    //     0xc3bd34: and             x16, x17, x16, lsr #2
    //     0xc3bd38: tst             x16, HEAP, lsr #32
    //     0xc3bd3c: b.eq            #0xc3bd44
    //     0xc3bd40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3bd44: ldur            x0, [fp, #-0x10]
    // 0xc3bd48: r16 = ")"
    //     0xc3bd48: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3bd4c: StoreField: r0->field_23 = r16
    //     0xc3bd4c: stur            w16, [x0, #0x23]
    // 0xc3bd50: str             x0, [SP]
    // 0xc3bd54: r0 = _interpolate()
    //     0xc3bd54: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3bd58: LeaveFrame
    //     0xc3bd58: mov             SP, fp
    //     0xc3bd5c: ldp             fp, lr, [SP], #0x10
    // 0xc3bd60: ret
    //     0xc3bd60: ret             
    // 0xc3bd64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3bd64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3bd68: b               #0xc3bcc0
  }
  get _ offset(/* No info */) {
    // ** addr: 0xe3dd14, size: 0x34
    // 0xe3dd14: LoadField: r2 = r1->field_7
    //     0xe3dd14: ldur            w2, [x1, #7]
    // 0xe3dd18: DecompressPointer r2
    //     0xe3dd18: add             x2, x2, HEAP, lsl #32
    // 0xe3dd1c: LoadField: r3 = r2->field_b
    //     0xe3dd1c: ldur            x3, [x2, #0xb]
    // 0xe3dd20: r0 = BoxInt64Instr(r3)
    //     0xe3dd20: sbfiz           x0, x3, #1, #0x1f
    //     0xe3dd24: cmp             x3, x0, asr #1
    //     0xe3dd28: b.eq            #0xe3dd44
    //     0xe3dd2c: stp             fp, lr, [SP, #-0x10]!
    //     0xe3dd30: mov             fp, SP
    //     0xe3dd34: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe3dd38: mov             SP, fp
    //     0xe3dd3c: ldp             fp, lr, [SP], #0x10
    //     0xe3dd40: stur            x3, [x0, #7]
    // 0xe3dd44: ret
    //     0xe3dd44: ret             
  }
  get _ message(/* No info */) {
    // ** addr: 0xe4c280, size: 0x14
    // 0xe4c280: LoadField: r2 = r1->field_7
    //     0xe4c280: ldur            w2, [x1, #7]
    // 0xe4c284: DecompressPointer r2
    //     0xe4c284: add             x2, x2, HEAP, lsl #32
    // 0xe4c288: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xe4c288: ldur            w0, [x2, #0x17]
    // 0xe4c28c: DecompressPointer r0
    //     0xe4c28c: add             x0, x0, HEAP, lsl #32
    // 0xe4c290: ret
    //     0xe4c290: ret             
  }
  get _ source(/* No info */) {
    // ** addr: 0xe54bf0, size: 0x14
    // 0xe54bf0: LoadField: r2 = r1->field_7
    //     0xe54bf0: ldur            w2, [x1, #7]
    // 0xe54bf4: DecompressPointer r2
    //     0xe54bf4: add             x2, x2, HEAP, lsl #32
    // 0xe54bf8: LoadField: r0 = r2->field_7
    //     0xe54bf8: ldur            w0, [x2, #7]
    // 0xe54bfc: DecompressPointer r0
    //     0xe54bfc: add             x0, x0, HEAP, lsl #32
    // 0xe54c00: ret
    //     0xe54c00: ret             
  }
}
