// lib: , url: package:petitparser/src/core/parser.dart

// class id: 1050873, size: 0x8
class :: {
}

// class id: 726, size: 0xc, field offset: 0x8
abstract class Parser<X0> extends Object {

  _ parse(/* No info */) {
    // ** addr: 0x88d1d4, size: 0x54
    // 0x88d1d4: EnterFrame
    //     0x88d1d4: stp             fp, lr, [SP, #-0x10]!
    //     0x88d1d8: mov             fp, SP
    // 0x88d1dc: AllocStack(0x10)
    //     0x88d1dc: sub             SP, SP, #0x10
    // 0x88d1e0: SetupParameters(Parser<X0> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x88d1e0: stur            x1, [fp, #-8]
    //     0x88d1e4: stur            x2, [fp, #-0x10]
    // 0x88d1e8: CheckStackOverflow
    //     0x88d1e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d1ec: cmp             SP, x16
    //     0x88d1f0: b.ls            #0x88d220
    // 0x88d1f4: r0 = Context()
    //     0x88d1f4: bl              #0x747588  ; AllocateContextStub -> Context (size=0x14)
    // 0x88d1f8: mov             x1, x0
    // 0x88d1fc: ldur            x0, [fp, #-0x10]
    // 0x88d200: StoreField: r1->field_7 = r0
    //     0x88d200: stur            w0, [x1, #7]
    // 0x88d204: StoreField: r1->field_b = rZR
    //     0x88d204: stur            xzr, [x1, #0xb]
    // 0x88d208: mov             x2, x1
    // 0x88d20c: ldur            x1, [fp, #-8]
    // 0x88d210: r0 = parseOn()
    //     0x88d210: bl              #0xeb10d0  ; [package:petitparser/src/parser/action/map.dart] MapParser::parseOn
    // 0x88d214: LeaveFrame
    //     0x88d214: mov             SP, fp
    //     0x88d218: ldp             fp, lr, [SP], #0x10
    // 0x88d21c: ret
    //     0x88d21c: ret             
    // 0x88d220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88d220: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88d224: b               #0x88d1f4
  }
  get _ children(/* No info */) {
    // ** addr: 0x88fcd4, size: 0xc
    // 0x88fcd4: r0 = const []
    //     0x88fcd4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31458] List<Parser>(0)
    //     0x88fcd8: ldr             x0, [x0, #0x458]
    // 0x88fcdc: ret
    //     0x88fcdc: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3c084, size: 0x7c
    // 0xc3c084: EnterFrame
    //     0xc3c084: stp             fp, lr, [SP, #-0x10]!
    //     0xc3c088: mov             fp, SP
    // 0xc3c08c: AllocStack(0x10)
    //     0xc3c08c: sub             SP, SP, #0x10
    // 0xc3c090: CheckStackOverflow
    //     0xc3c090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3c094: cmp             SP, x16
    //     0xc3c098: b.ls            #0xc3c0f8
    // 0xc3c09c: ldr             x16, [fp, #0x10]
    // 0xc3c0a0: str             x16, [SP]
    // 0xc3c0a4: r0 = toString()
    //     0xc3c0a4: bl              #0xc467e8  ; [dart:core] Object::toString
    // 0xc3c0a8: mov             x1, x0
    // 0xc3c0ac: r2 = "Instance of \'"
    //     0xc3c0ac: add             x2, PP, #0x31, lsl #12  ; [pp+0x31450] "Instance of \'"
    //     0xc3c0b0: ldr             x2, [x2, #0x450]
    // 0xc3c0b4: stur            x0, [fp, #-8]
    // 0xc3c0b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc3c0b8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc3c0bc: r0 = startsWith()
    //     0xc3c0bc: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xc3c0c0: tbnz            w0, #4, #0xc3c0e8
    // 0xc3c0c4: ldur            x1, [fp, #-8]
    // 0xc3c0c8: r2 = 13
    //     0xc3c0c8: movz            x2, #0xd
    // 0xc3c0cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc3c0cc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc3c0d0: r0 = substring()
    //     0xc3c0d0: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc3c0d4: mov             x1, x0
    // 0xc3c0d8: r2 = "\'"
    //     0xc3c0d8: ldr             x2, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xc3c0dc: r3 = ""
    //     0xc3c0dc: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc3c0e0: r0 = replaceFirst()
    //     0xc3c0e0: bl              #0x6440c0  ; [dart:core] _StringBase::replaceFirst
    // 0xc3c0e4: b               #0xc3c0ec
    // 0xc3c0e8: ldur            x0, [fp, #-8]
    // 0xc3c0ec: LeaveFrame
    //     0xc3c0ec: mov             SP, fp
    //     0xc3c0f0: ldp             fp, lr, [SP], #0x10
    // 0xc3c0f4: ret
    //     0xc3c0f4: ret             
    // 0xc3c0f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3c0f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3c0fc: b               #0xc3c09c
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeaf888, size: 0x2c
    // 0xeaf888: EnterFrame
    //     0xeaf888: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf88c: mov             fp, SP
    // 0xeaf890: CheckStackOverflow
    //     0xeaf890: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf894: cmp             SP, x16
    //     0xeaf898: b.ls            #0xeaf8ac
    // 0xeaf89c: r0 = _throwUnsupported()
    //     0xeaf89c: bl              #0xeaf8b4  ; [package:petitparser/src/definition/internal/reference.dart] ::_throwUnsupported
    // 0xeaf8a0: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xeaf8a0: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xeaf8a4: r0 = Throw()
    //     0xeaf8a4: bl              #0xec04b8  ; ThrowStub
    // 0xeaf8a8: brk             #0
    // 0xeaf8ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf8ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf8b0: b               #0xeaf89c
  }
}
