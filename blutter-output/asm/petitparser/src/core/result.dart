// lib: , url: package:petitparser/src/core/result.dart

// class id: 1050874, size: 0x8
class :: {
}

// class id: 754, size: 0x18, field offset: 0x14
//   const constructor, 
abstract class Result<X0> extends Context {
}

// class id: 755, size: 0x1c, field offset: 0x18
//   const constructor, 
class Failure extends Result<dynamic> {

  _ toString(/* No info */) {
    // ** addr: 0xc3bb54, size: 0xc8
    // 0xc3bb54: EnterFrame
    //     0xc3bb54: stp             fp, lr, [SP, #-0x10]!
    //     0xc3bb58: mov             fp, SP
    // 0xc3bb5c: AllocStack(0x10)
    //     0xc3bb5c: sub             SP, SP, #0x10
    // 0xc3bb60: CheckStackOverflow
    //     0xc3bb60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3bb64: cmp             SP, x16
    //     0xc3bb68: b.ls            #0xc3bc14
    // 0xc3bb6c: r1 = Null
    //     0xc3bb6c: mov             x1, NULL
    // 0xc3bb70: r2 = 8
    //     0xc3bb70: movz            x2, #0x8
    // 0xc3bb74: r0 = AllocateArray()
    //     0xc3bb74: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3bb78: stur            x0, [fp, #-8]
    // 0xc3bb7c: r16 = "Failure["
    //     0xc3bb7c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31498] "Failure["
    //     0xc3bb80: ldr             x16, [x16, #0x498]
    // 0xc3bb84: StoreField: r0->field_f = r16
    //     0xc3bb84: stur            w16, [x0, #0xf]
    // 0xc3bb88: ldr             x1, [fp, #0x10]
    // 0xc3bb8c: r0 = toPositionString()
    //     0xc3bb8c: bl              #0xc3b718  ; [package:petitparser/src/core/context.dart] Context::toPositionString
    // 0xc3bb90: ldur            x1, [fp, #-8]
    // 0xc3bb94: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3bb94: add             x25, x1, #0x13
    //     0xc3bb98: str             w0, [x25]
    //     0xc3bb9c: tbz             w0, #0, #0xc3bbb8
    //     0xc3bba0: ldurb           w16, [x1, #-1]
    //     0xc3bba4: ldurb           w17, [x0, #-1]
    //     0xc3bba8: and             x16, x17, x16, lsr #2
    //     0xc3bbac: tst             x16, HEAP, lsr #32
    //     0xc3bbb0: b.eq            #0xc3bbb8
    //     0xc3bbb4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3bbb8: ldur            x2, [fp, #-8]
    // 0xc3bbbc: r16 = "]: "
    //     0xc3bbbc: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c4e0] "]: "
    //     0xc3bbc0: ldr             x16, [x16, #0x4e0]
    // 0xc3bbc4: ArrayStore: r2[0] = r16  ; List_4
    //     0xc3bbc4: stur            w16, [x2, #0x17]
    // 0xc3bbc8: ldr             x0, [fp, #0x10]
    // 0xc3bbcc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc3bbcc: ldur            w1, [x0, #0x17]
    // 0xc3bbd0: DecompressPointer r1
    //     0xc3bbd0: add             x1, x1, HEAP, lsl #32
    // 0xc3bbd4: mov             x0, x1
    // 0xc3bbd8: mov             x1, x2
    // 0xc3bbdc: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3bbdc: add             x25, x1, #0x1b
    //     0xc3bbe0: str             w0, [x25]
    //     0xc3bbe4: tbz             w0, #0, #0xc3bc00
    //     0xc3bbe8: ldurb           w16, [x1, #-1]
    //     0xc3bbec: ldurb           w17, [x0, #-1]
    //     0xc3bbf0: and             x16, x17, x16, lsr #2
    //     0xc3bbf4: tst             x16, HEAP, lsr #32
    //     0xc3bbf8: b.eq            #0xc3bc00
    //     0xc3bbfc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3bc00: str             x2, [SP]
    // 0xc3bc04: r0 = _interpolate()
    //     0xc3bc04: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3bc08: LeaveFrame
    //     0xc3bc08: mov             SP, fp
    //     0xc3bc0c: ldp             fp, lr, [SP], #0x10
    // 0xc3bc10: ret
    //     0xc3bc10: ret             
    // 0xc3bc14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3bc14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3bc18: b               #0xc3bb6c
  }
}

// class id: 756, size: 0x1c, field offset: 0x18
//   const constructor, 
class Success<X0> extends Result<X0> {

  _ toString(/* No info */) {
    // ** addr: 0xc3b650, size: 0xc8
    // 0xc3b650: EnterFrame
    //     0xc3b650: stp             fp, lr, [SP, #-0x10]!
    //     0xc3b654: mov             fp, SP
    // 0xc3b658: AllocStack(0x10)
    //     0xc3b658: sub             SP, SP, #0x10
    // 0xc3b65c: CheckStackOverflow
    //     0xc3b65c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3b660: cmp             SP, x16
    //     0xc3b664: b.ls            #0xc3b710
    // 0xc3b668: r1 = Null
    //     0xc3b668: mov             x1, NULL
    // 0xc3b66c: r2 = 8
    //     0xc3b66c: movz            x2, #0x8
    // 0xc3b670: r0 = AllocateArray()
    //     0xc3b670: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3b674: stur            x0, [fp, #-8]
    // 0xc3b678: r16 = "Success["
    //     0xc3b678: add             x16, PP, #0x31, lsl #12  ; [pp+0x31490] "Success["
    //     0xc3b67c: ldr             x16, [x16, #0x490]
    // 0xc3b680: StoreField: r0->field_f = r16
    //     0xc3b680: stur            w16, [x0, #0xf]
    // 0xc3b684: ldr             x1, [fp, #0x10]
    // 0xc3b688: r0 = toPositionString()
    //     0xc3b688: bl              #0xc3b718  ; [package:petitparser/src/core/context.dart] Context::toPositionString
    // 0xc3b68c: ldur            x1, [fp, #-8]
    // 0xc3b690: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3b690: add             x25, x1, #0x13
    //     0xc3b694: str             w0, [x25]
    //     0xc3b698: tbz             w0, #0, #0xc3b6b4
    //     0xc3b69c: ldurb           w16, [x1, #-1]
    //     0xc3b6a0: ldurb           w17, [x0, #-1]
    //     0xc3b6a4: and             x16, x17, x16, lsr #2
    //     0xc3b6a8: tst             x16, HEAP, lsr #32
    //     0xc3b6ac: b.eq            #0xc3b6b4
    //     0xc3b6b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b6b4: ldur            x2, [fp, #-8]
    // 0xc3b6b8: r16 = "]: "
    //     0xc3b6b8: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c4e0] "]: "
    //     0xc3b6bc: ldr             x16, [x16, #0x4e0]
    // 0xc3b6c0: ArrayStore: r2[0] = r16  ; List_4
    //     0xc3b6c0: stur            w16, [x2, #0x17]
    // 0xc3b6c4: ldr             x0, [fp, #0x10]
    // 0xc3b6c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc3b6c8: ldur            w1, [x0, #0x17]
    // 0xc3b6cc: DecompressPointer r1
    //     0xc3b6cc: add             x1, x1, HEAP, lsl #32
    // 0xc3b6d0: mov             x0, x1
    // 0xc3b6d4: mov             x1, x2
    // 0xc3b6d8: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3b6d8: add             x25, x1, #0x1b
    //     0xc3b6dc: str             w0, [x25]
    //     0xc3b6e0: tbz             w0, #0, #0xc3b6fc
    //     0xc3b6e4: ldurb           w16, [x1, #-1]
    //     0xc3b6e8: ldurb           w17, [x0, #-1]
    //     0xc3b6ec: and             x16, x17, x16, lsr #2
    //     0xc3b6f0: tst             x16, HEAP, lsr #32
    //     0xc3b6f4: b.eq            #0xc3b6fc
    //     0xc3b6f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3b6fc: str             x2, [SP]
    // 0xc3b700: r0 = _interpolate()
    //     0xc3b700: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3b704: LeaveFrame
    //     0xc3b704: mov             SP, fp
    //     0xc3b708: ldp             fp, lr, [SP], #0x10
    // 0xc3b70c: ret
    //     0xc3b70c: ret             
    // 0xc3b710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3b710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3b714: b               #0xc3b668
  }
}
