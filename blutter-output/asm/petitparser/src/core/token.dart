// lib: , url: package:petitparser/src/core/token.dart

// class id: 1050875, size: 0x8
class :: {
}

// class id: 725, size: 0x24, field offset: 0x8
//   const constructor, 
class Token<X0> extends Object {

  static late final Parser<String> _newlineParser; // offset: 0x1708

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf1ccc, size: 0x140
    // 0xbf1ccc: EnterFrame
    //     0xbf1ccc: stp             fp, lr, [SP, #-0x10]!
    //     0xbf1cd0: mov             fp, SP
    // 0xbf1cd4: AllocStack(0x18)
    //     0xbf1cd4: sub             SP, SP, #0x18
    // 0xbf1cd8: CheckStackOverflow
    //     0xbf1cd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf1cdc: cmp             SP, x16
    //     0xbf1ce0: b.ls            #0xbf1e04
    // 0xbf1ce4: ldr             x1, [fp, #0x10]
    // 0xbf1ce8: LoadField: r0 = r1->field_b
    //     0xbf1ce8: ldur            w0, [x1, #0xb]
    // 0xbf1cec: DecompressPointer r0
    //     0xbf1cec: add             x0, x0, HEAP, lsl #32
    // 0xbf1cf0: r2 = 60
    //     0xbf1cf0: movz            x2, #0x3c
    // 0xbf1cf4: branchIfSmi(r0, 0xbf1d00)
    //     0xbf1cf4: tbz             w0, #0, #0xbf1d00
    // 0xbf1cf8: r2 = LoadClassIdInstr(r0)
    //     0xbf1cf8: ldur            x2, [x0, #-1]
    //     0xbf1cfc: ubfx            x2, x2, #0xc, #0x14
    // 0xbf1d00: str             x0, [SP]
    // 0xbf1d04: mov             x0, x2
    // 0xbf1d08: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1d08: movz            x17, #0x64af
    //     0xbf1d0c: add             lr, x0, x17
    //     0xbf1d10: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1d14: blr             lr
    // 0xbf1d18: mov             x3, x0
    // 0xbf1d1c: ldr             x2, [fp, #0x10]
    // 0xbf1d20: stur            x3, [fp, #-8]
    // 0xbf1d24: LoadField: r4 = r2->field_13
    //     0xbf1d24: ldur            x4, [x2, #0x13]
    // 0xbf1d28: r0 = BoxInt64Instr(r4)
    //     0xbf1d28: sbfiz           x0, x4, #1, #0x1f
    //     0xbf1d2c: cmp             x4, x0, asr #1
    //     0xbf1d30: b.eq            #0xbf1d3c
    //     0xbf1d34: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1d38: stur            x4, [x0, #7]
    // 0xbf1d3c: r1 = 60
    //     0xbf1d3c: movz            x1, #0x3c
    // 0xbf1d40: branchIfSmi(r0, 0xbf1d4c)
    //     0xbf1d40: tbz             w0, #0, #0xbf1d4c
    // 0xbf1d44: r1 = LoadClassIdInstr(r0)
    //     0xbf1d44: ldur            x1, [x0, #-1]
    //     0xbf1d48: ubfx            x1, x1, #0xc, #0x14
    // 0xbf1d4c: str             x0, [SP]
    // 0xbf1d50: mov             x0, x1
    // 0xbf1d54: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1d54: movz            x17, #0x64af
    //     0xbf1d58: add             lr, x0, x17
    //     0xbf1d5c: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1d60: blr             lr
    // 0xbf1d64: mov             x1, x0
    // 0xbf1d68: ldur            x0, [fp, #-8]
    // 0xbf1d6c: r2 = LoadInt32Instr(r0)
    //     0xbf1d6c: sbfx            x2, x0, #1, #0x1f
    //     0xbf1d70: tbz             w0, #0, #0xbf1d78
    //     0xbf1d74: ldur            x2, [x0, #7]
    // 0xbf1d78: r0 = LoadInt32Instr(r1)
    //     0xbf1d78: sbfx            x0, x1, #1, #0x1f
    //     0xbf1d7c: tbz             w1, #0, #0xbf1d84
    //     0xbf1d80: ldur            x0, [x1, #7]
    // 0xbf1d84: add             x3, x2, x0
    // 0xbf1d88: ldr             x0, [fp, #0x10]
    // 0xbf1d8c: stur            x3, [fp, #-0x10]
    // 0xbf1d90: LoadField: r2 = r0->field_1b
    //     0xbf1d90: ldur            x2, [x0, #0x1b]
    // 0xbf1d94: r0 = BoxInt64Instr(r2)
    //     0xbf1d94: sbfiz           x0, x2, #1, #0x1f
    //     0xbf1d98: cmp             x2, x0, asr #1
    //     0xbf1d9c: b.eq            #0xbf1da8
    //     0xbf1da0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1da4: stur            x2, [x0, #7]
    // 0xbf1da8: r1 = 60
    //     0xbf1da8: movz            x1, #0x3c
    // 0xbf1dac: branchIfSmi(r0, 0xbf1db8)
    //     0xbf1dac: tbz             w0, #0, #0xbf1db8
    // 0xbf1db0: r1 = LoadClassIdInstr(r0)
    //     0xbf1db0: ldur            x1, [x0, #-1]
    //     0xbf1db4: ubfx            x1, x1, #0xc, #0x14
    // 0xbf1db8: str             x0, [SP]
    // 0xbf1dbc: mov             x0, x1
    // 0xbf1dc0: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf1dc0: movz            x17, #0x64af
    //     0xbf1dc4: add             lr, x0, x17
    //     0xbf1dc8: ldr             lr, [x21, lr, lsl #3]
    //     0xbf1dcc: blr             lr
    // 0xbf1dd0: r2 = LoadInt32Instr(r0)
    //     0xbf1dd0: sbfx            x2, x0, #1, #0x1f
    //     0xbf1dd4: tbz             w0, #0, #0xbf1ddc
    //     0xbf1dd8: ldur            x2, [x0, #7]
    // 0xbf1ddc: ldur            x3, [fp, #-0x10]
    // 0xbf1de0: add             x4, x3, x2
    // 0xbf1de4: r0 = BoxInt64Instr(r4)
    //     0xbf1de4: sbfiz           x0, x4, #1, #0x1f
    //     0xbf1de8: cmp             x4, x0, asr #1
    //     0xbf1dec: b.eq            #0xbf1df8
    //     0xbf1df0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf1df4: stur            x4, [x0, #7]
    // 0xbf1df8: LeaveFrame
    //     0xbf1df8: mov             SP, fp
    //     0xbf1dfc: ldp             fp, lr, [SP], #0x10
    // 0xbf1e00: ret
    //     0xbf1e00: ret             
    // 0xbf1e04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1e04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1e08: b               #0xbf1ce4
  }
  static _ positionString(/* No info */) {
    // ** addr: 0xc3b754, size: 0xb8
    // 0xc3b754: EnterFrame
    //     0xc3b754: stp             fp, lr, [SP, #-0x10]!
    //     0xc3b758: mov             fp, SP
    // 0xc3b75c: AllocStack(0x20)
    //     0xc3b75c: sub             SP, SP, #0x20
    // 0xc3b760: CheckStackOverflow
    //     0xc3b760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3b764: cmp             SP, x16
    //     0xc3b768: b.ls            #0xc3b7fc
    // 0xc3b76c: r0 = lineAndColumnOf()
    //     0xc3b76c: bl              #0xc3b80c  ; [package:petitparser/src/core/token.dart] Token::lineAndColumnOf
    // 0xc3b770: mov             x2, x0
    // 0xc3b774: LoadField: r0 = r2->field_b
    //     0xc3b774: ldur            w0, [x2, #0xb]
    // 0xc3b778: r3 = LoadInt32Instr(r0)
    //     0xc3b778: sbfx            x3, x0, #1, #0x1f
    // 0xc3b77c: mov             x0, x3
    // 0xc3b780: stur            x3, [fp, #-0x18]
    // 0xc3b784: r1 = 0
    //     0xc3b784: movz            x1, #0
    // 0xc3b788: cmp             x1, x0
    // 0xc3b78c: b.hs            #0xc3b804
    // 0xc3b790: LoadField: r0 = r2->field_f
    //     0xc3b790: ldur            w0, [x2, #0xf]
    // 0xc3b794: DecompressPointer r0
    //     0xc3b794: add             x0, x0, HEAP, lsl #32
    // 0xc3b798: stur            x0, [fp, #-0x10]
    // 0xc3b79c: LoadField: r4 = r0->field_f
    //     0xc3b79c: ldur            w4, [x0, #0xf]
    // 0xc3b7a0: DecompressPointer r4
    //     0xc3b7a0: add             x4, x4, HEAP, lsl #32
    // 0xc3b7a4: stur            x4, [fp, #-8]
    // 0xc3b7a8: r1 = Null
    //     0xc3b7a8: mov             x1, NULL
    // 0xc3b7ac: r2 = 6
    //     0xc3b7ac: movz            x2, #0x6
    // 0xc3b7b0: r0 = AllocateArray()
    //     0xc3b7b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3b7b4: mov             x2, x0
    // 0xc3b7b8: ldur            x0, [fp, #-8]
    // 0xc3b7bc: StoreField: r2->field_f = r0
    //     0xc3b7bc: stur            w0, [x2, #0xf]
    // 0xc3b7c0: r16 = ":"
    //     0xc3b7c0: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xc3b7c4: StoreField: r2->field_13 = r16
    //     0xc3b7c4: stur            w16, [x2, #0x13]
    // 0xc3b7c8: ldur            x0, [fp, #-0x18]
    // 0xc3b7cc: r1 = 1
    //     0xc3b7cc: movz            x1, #0x1
    // 0xc3b7d0: cmp             x1, x0
    // 0xc3b7d4: b.hs            #0xc3b808
    // 0xc3b7d8: ldur            x0, [fp, #-0x10]
    // 0xc3b7dc: LoadField: r1 = r0->field_13
    //     0xc3b7dc: ldur            w1, [x0, #0x13]
    // 0xc3b7e0: DecompressPointer r1
    //     0xc3b7e0: add             x1, x1, HEAP, lsl #32
    // 0xc3b7e4: ArrayStore: r2[0] = r1  ; List_4
    //     0xc3b7e4: stur            w1, [x2, #0x17]
    // 0xc3b7e8: str             x2, [SP]
    // 0xc3b7ec: r0 = _interpolate()
    //     0xc3b7ec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3b7f0: LeaveFrame
    //     0xc3b7f0: mov             SP, fp
    //     0xc3b7f4: ldp             fp, lr, [SP], #0x10
    // 0xc3b7f8: ret
    //     0xc3b7f8: ret             
    // 0xc3b7fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3b7fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3b800: b               #0xc3b76c
    // 0xc3b804: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3b804: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc3b808: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3b808: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ lineAndColumnOf(/* No info */) {
    // ** addr: 0xc3b80c, size: 0x21c
    // 0xc3b80c: EnterFrame
    //     0xc3b80c: stp             fp, lr, [SP, #-0x10]!
    //     0xc3b810: mov             fp, SP
    // 0xc3b814: AllocStack(0x48)
    //     0xc3b814: sub             SP, SP, #0x48
    // 0xc3b818: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc3b818: stur            x1, [fp, #-8]
    //     0xc3b81c: stur            x2, [fp, #-0x10]
    // 0xc3b820: CheckStackOverflow
    //     0xc3b820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3b824: cmp             SP, x16
    //     0xc3b828: b.ls            #0xc3ba0c
    // 0xc3b82c: r0 = InitLateStaticField(0x1708) // [package:petitparser/src/core/token.dart] Token<X0>::_newlineParser
    //     0xc3b82c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc3b830: ldr             x0, [x0, #0x2e10]
    //     0xc3b834: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc3b838: cmp             w0, w16
    //     0xc3b83c: b.ne            #0xc3b84c
    //     0xc3b840: add             x2, PP, #0x31, lsl #12  ; [pp+0x31468] Field <Token._newlineParser@2609343534>: static late final (offset: 0x1708)
    //     0xc3b844: ldr             x2, [x2, #0x468]
    //     0xc3b848: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc3b84c: r16 = <String>
    //     0xc3b84c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc3b850: stp             x0, x16, [SP]
    // 0xc3b854: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc3b854: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc3b858: r0 = TokenParserExtension.token()
    //     0xc3b858: bl              #0xc3ba8c  ; [package:petitparser/src/parser/action/token.dart] ::TokenParserExtension.token
    // 0xc3b85c: r16 = <Token<String>>
    //     0xc3b85c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31470] TypeArguments: <Token<String>>
    //     0xc3b860: ldr             x16, [x16, #0x470]
    // 0xc3b864: stp             x0, x16, [SP, #8]
    // 0xc3b868: ldur            x16, [fp, #-8]
    // 0xc3b86c: str             x16, [SP]
    // 0xc3b870: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc3b870: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc3b874: r0 = MatchesParserExtension.allMatches()
    //     0xc3b874: bl              #0xc3ba28  ; [package:petitparser/src/matcher/matches.dart] ::MatchesParserExtension.allMatches
    // 0xc3b878: mov             x1, x0
    // 0xc3b87c: r0 = iterator()
    //     0xc3b87c: bl              #0x888d7c  ; [package:petitparser/src/matcher/matches/matches_iterable.dart] MatchesIterable::iterator
    // 0xc3b880: stur            x0, [fp, #-8]
    // 0xc3b884: ldur            x2, [fp, #-0x10]
    // 0xc3b888: r4 = 1
    //     0xc3b888: movz            x4, #0x1
    // 0xc3b88c: r3 = 0
    //     0xc3b88c: movz            x3, #0
    // 0xc3b890: stur            x4, [fp, #-0x18]
    // 0xc3b894: stur            x3, [fp, #-0x20]
    // 0xc3b898: CheckStackOverflow
    //     0xc3b898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3b89c: cmp             SP, x16
    //     0xc3b8a0: b.ls            #0xc3ba14
    // 0xc3b8a4: mov             x1, x0
    // 0xc3b8a8: r0 = moveNext()
    //     0xc3b8a8: bl              #0x747224  ; [package:petitparser/src/matcher/matches/matches_iterator.dart] MatchesIterator::moveNext
    // 0xc3b8ac: tbnz            w0, #4, #0xc3b97c
    // 0xc3b8b0: ldur            x1, [fp, #-0x10]
    // 0xc3b8b4: ldur            x0, [fp, #-8]
    // 0xc3b8b8: LoadField: r2 = r0->field_1f
    //     0xc3b8b8: ldur            w2, [x0, #0x1f]
    // 0xc3b8bc: DecompressPointer r2
    //     0xc3b8bc: add             x2, x2, HEAP, lsl #32
    // 0xc3b8c0: r16 = Sentinel
    //     0xc3b8c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc3b8c4: cmp             w2, w16
    // 0xc3b8c8: b.eq            #0xc3ba1c
    // 0xc3b8cc: LoadField: r3 = r2->field_1b
    //     0xc3b8cc: ldur            x3, [x2, #0x1b]
    // 0xc3b8d0: cmp             x1, x3
    // 0xc3b8d4: b.lt            #0xc3b8e8
    // 0xc3b8d8: ldur            x2, [fp, #-0x18]
    // 0xc3b8dc: add             x4, x2, #1
    // 0xc3b8e0: mov             x2, x1
    // 0xc3b8e4: b               #0xc3b890
    // 0xc3b8e8: ldur            x2, [fp, #-0x18]
    // 0xc3b8ec: ldur            x0, [fp, #-0x20]
    // 0xc3b8f0: r3 = 4
    //     0xc3b8f0: movz            x3, #0x4
    // 0xc3b8f4: sub             x4, x1, x0
    // 0xc3b8f8: add             x5, x4, #1
    // 0xc3b8fc: stur            x5, [fp, #-0x28]
    // 0xc3b900: r0 = BoxInt64Instr(r2)
    //     0xc3b900: sbfiz           x0, x2, #1, #0x1f
    //     0xc3b904: cmp             x2, x0, asr #1
    //     0xc3b908: b.eq            #0xc3b914
    //     0xc3b90c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3b910: stur            x2, [x0, #7]
    // 0xc3b914: mov             x2, x3
    // 0xc3b918: r1 = Null
    //     0xc3b918: mov             x1, NULL
    // 0xc3b91c: stur            x0, [fp, #-8]
    // 0xc3b920: r0 = AllocateArray()
    //     0xc3b920: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3b924: mov             x2, x0
    // 0xc3b928: ldur            x0, [fp, #-8]
    // 0xc3b92c: stur            x2, [fp, #-0x30]
    // 0xc3b930: StoreField: r2->field_f = r0
    //     0xc3b930: stur            w0, [x2, #0xf]
    // 0xc3b934: ldur            x3, [fp, #-0x28]
    // 0xc3b938: r0 = BoxInt64Instr(r3)
    //     0xc3b938: sbfiz           x0, x3, #1, #0x1f
    //     0xc3b93c: cmp             x3, x0, asr #1
    //     0xc3b940: b.eq            #0xc3b94c
    //     0xc3b944: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3b948: stur            x3, [x0, #7]
    // 0xc3b94c: StoreField: r2->field_13 = r0
    //     0xc3b94c: stur            w0, [x2, #0x13]
    // 0xc3b950: r1 = <int>
    //     0xc3b950: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc3b954: r0 = AllocateGrowableArray()
    //     0xc3b954: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xc3b958: mov             x1, x0
    // 0xc3b95c: ldur            x0, [fp, #-0x30]
    // 0xc3b960: StoreField: r1->field_f = r0
    //     0xc3b960: stur            w0, [x1, #0xf]
    // 0xc3b964: r3 = 4
    //     0xc3b964: movz            x3, #0x4
    // 0xc3b968: StoreField: r1->field_b = r3
    //     0xc3b968: stur            w3, [x1, #0xb]
    // 0xc3b96c: mov             x0, x1
    // 0xc3b970: LeaveFrame
    //     0xc3b970: mov             SP, fp
    //     0xc3b974: ldp             fp, lr, [SP], #0x10
    // 0xc3b978: ret
    //     0xc3b978: ret             
    // 0xc3b97c: ldur            x1, [fp, #-0x10]
    // 0xc3b980: ldur            x2, [fp, #-0x18]
    // 0xc3b984: ldur            x0, [fp, #-0x20]
    // 0xc3b988: r3 = 4
    //     0xc3b988: movz            x3, #0x4
    // 0xc3b98c: sub             x4, x1, x0
    // 0xc3b990: add             x5, x4, #1
    // 0xc3b994: stur            x5, [fp, #-0x10]
    // 0xc3b998: r0 = BoxInt64Instr(r2)
    //     0xc3b998: sbfiz           x0, x2, #1, #0x1f
    //     0xc3b99c: cmp             x2, x0, asr #1
    //     0xc3b9a0: b.eq            #0xc3b9ac
    //     0xc3b9a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3b9a8: stur            x2, [x0, #7]
    // 0xc3b9ac: mov             x2, x3
    // 0xc3b9b0: r1 = Null
    //     0xc3b9b0: mov             x1, NULL
    // 0xc3b9b4: stur            x0, [fp, #-8]
    // 0xc3b9b8: r0 = AllocateArray()
    //     0xc3b9b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3b9bc: mov             x2, x0
    // 0xc3b9c0: ldur            x0, [fp, #-8]
    // 0xc3b9c4: stur            x2, [fp, #-0x30]
    // 0xc3b9c8: StoreField: r2->field_f = r0
    //     0xc3b9c8: stur            w0, [x2, #0xf]
    // 0xc3b9cc: ldur            x3, [fp, #-0x10]
    // 0xc3b9d0: r0 = BoxInt64Instr(r3)
    //     0xc3b9d0: sbfiz           x0, x3, #1, #0x1f
    //     0xc3b9d4: cmp             x3, x0, asr #1
    //     0xc3b9d8: b.eq            #0xc3b9e4
    //     0xc3b9dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3b9e0: stur            x3, [x0, #7]
    // 0xc3b9e4: StoreField: r2->field_13 = r0
    //     0xc3b9e4: stur            w0, [x2, #0x13]
    // 0xc3b9e8: r1 = <int>
    //     0xc3b9e8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc3b9ec: r0 = AllocateGrowableArray()
    //     0xc3b9ec: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xc3b9f0: ldur            x1, [fp, #-0x30]
    // 0xc3b9f4: StoreField: r0->field_f = r1
    //     0xc3b9f4: stur            w1, [x0, #0xf]
    // 0xc3b9f8: r1 = 4
    //     0xc3b9f8: movz            x1, #0x4
    // 0xc3b9fc: StoreField: r0->field_b = r1
    //     0xc3b9fc: stur            w1, [x0, #0xb]
    // 0xc3ba00: LeaveFrame
    //     0xc3ba00: mov             SP, fp
    //     0xc3ba04: ldp             fp, lr, [SP], #0x10
    // 0xc3ba08: ret
    //     0xc3ba08: ret             
    // 0xc3ba0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3ba0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3ba10: b               #0xc3b82c
    // 0xc3ba14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3ba14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3ba18: b               #0xc3b8a4
    // 0xc3ba1c: r9 = current
    //     0xc3ba1c: add             x9, PP, #0x31, lsl #12  ; [pp+0x31478] Field <MatchesIterator.current>: late (offset: 0x20)
    //     0xc3ba20: ldr             x9, [x9, #0x478]
    // 0xc3ba24: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc3ba24: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  static Parser<String> _newlineParser() {
    // ** addr: 0xc3baf4, size: 0x2c
    // 0xc3baf4: EnterFrame
    //     0xc3baf4: stp             fp, lr, [SP, #-0x10]!
    //     0xc3baf8: mov             fp, SP
    // 0xc3bafc: CheckStackOverflow
    //     0xc3bafc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3bb00: cmp             SP, x16
    //     0xc3bb04: b.ls            #0xc3bb18
    // 0xc3bb08: r0 = newline()
    //     0xc3bb08: bl              #0xc3bb20  ; [package:petitparser/src/parser/misc/newline.dart] ::newline
    // 0xc3bb0c: LeaveFrame
    //     0xc3bb0c: mov             SP, fp
    //     0xc3bb10: ldp             fp, lr, [SP], #0x10
    // 0xc3bb14: ret
    //     0xc3bb14: ret             
    // 0xc3bb18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3bb18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3bb1c: b               #0xc3bb08
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3c100, size: 0xd4
    // 0xc3c100: EnterFrame
    //     0xc3c100: stp             fp, lr, [SP, #-0x10]!
    //     0xc3c104: mov             fp, SP
    // 0xc3c108: AllocStack(0x10)
    //     0xc3c108: sub             SP, SP, #0x10
    // 0xc3c10c: CheckStackOverflow
    //     0xc3c10c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3c110: cmp             SP, x16
    //     0xc3c114: b.ls            #0xc3c1cc
    // 0xc3c118: r1 = Null
    //     0xc3c118: mov             x1, NULL
    // 0xc3c11c: r2 = 8
    //     0xc3c11c: movz            x2, #0x8
    // 0xc3c120: r0 = AllocateArray()
    //     0xc3c120: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3c124: stur            x0, [fp, #-8]
    // 0xc3c128: r16 = "Token["
    //     0xc3c128: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c018] "Token["
    //     0xc3c12c: ldr             x16, [x16, #0x18]
    // 0xc3c130: StoreField: r0->field_f = r16
    //     0xc3c130: stur            w16, [x0, #0xf]
    // 0xc3c134: ldr             x3, [fp, #0x10]
    // 0xc3c138: LoadField: r1 = r3->field_f
    //     0xc3c138: ldur            w1, [x3, #0xf]
    // 0xc3c13c: DecompressPointer r1
    //     0xc3c13c: add             x1, x1, HEAP, lsl #32
    // 0xc3c140: LoadField: r2 = r3->field_13
    //     0xc3c140: ldur            x2, [x3, #0x13]
    // 0xc3c144: r0 = positionString()
    //     0xc3c144: bl              #0xc3b754  ; [package:petitparser/src/core/token.dart] Token::positionString
    // 0xc3c148: ldur            x1, [fp, #-8]
    // 0xc3c14c: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3c14c: add             x25, x1, #0x13
    //     0xc3c150: str             w0, [x25]
    //     0xc3c154: tbz             w0, #0, #0xc3c170
    //     0xc3c158: ldurb           w16, [x1, #-1]
    //     0xc3c15c: ldurb           w17, [x0, #-1]
    //     0xc3c160: and             x16, x17, x16, lsr #2
    //     0xc3c164: tst             x16, HEAP, lsr #32
    //     0xc3c168: b.eq            #0xc3c170
    //     0xc3c16c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3c170: ldur            x2, [fp, #-8]
    // 0xc3c174: r16 = "]: "
    //     0xc3c174: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c4e0] "]: "
    //     0xc3c178: ldr             x16, [x16, #0x4e0]
    // 0xc3c17c: ArrayStore: r2[0] = r16  ; List_4
    //     0xc3c17c: stur            w16, [x2, #0x17]
    // 0xc3c180: ldr             x0, [fp, #0x10]
    // 0xc3c184: LoadField: r1 = r0->field_b
    //     0xc3c184: ldur            w1, [x0, #0xb]
    // 0xc3c188: DecompressPointer r1
    //     0xc3c188: add             x1, x1, HEAP, lsl #32
    // 0xc3c18c: mov             x0, x1
    // 0xc3c190: mov             x1, x2
    // 0xc3c194: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3c194: add             x25, x1, #0x1b
    //     0xc3c198: str             w0, [x25]
    //     0xc3c19c: tbz             w0, #0, #0xc3c1b8
    //     0xc3c1a0: ldurb           w16, [x1, #-1]
    //     0xc3c1a4: ldurb           w17, [x0, #-1]
    //     0xc3c1a8: and             x16, x17, x16, lsr #2
    //     0xc3c1ac: tst             x16, HEAP, lsr #32
    //     0xc3c1b0: b.eq            #0xc3c1b8
    //     0xc3c1b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3c1b8: str             x2, [SP]
    // 0xc3c1bc: r0 = _interpolate()
    //     0xc3c1bc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3c1c0: LeaveFrame
    //     0xc3c1c0: mov             SP, fp
    //     0xc3c1c4: ldp             fp, lr, [SP], #0x10
    // 0xc3c1c8: ret
    //     0xc3c1c8: ret             
    // 0xc3c1cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3c1cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3c1d0: b               #0xc3c118
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d0bc, size: 0xd8
    // 0xd7d0bc: EnterFrame
    //     0xd7d0bc: stp             fp, lr, [SP, #-0x10]!
    //     0xd7d0c0: mov             fp, SP
    // 0xd7d0c4: AllocStack(0x10)
    //     0xd7d0c4: sub             SP, SP, #0x10
    // 0xd7d0c8: CheckStackOverflow
    //     0xd7d0c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7d0cc: cmp             SP, x16
    //     0xd7d0d0: b.ls            #0xd7d18c
    // 0xd7d0d4: ldr             x1, [fp, #0x10]
    // 0xd7d0d8: cmp             w1, NULL
    // 0xd7d0dc: b.ne            #0xd7d0f0
    // 0xd7d0e0: r0 = false
    //     0xd7d0e0: add             x0, NULL, #0x30  ; false
    // 0xd7d0e4: LeaveFrame
    //     0xd7d0e4: mov             SP, fp
    //     0xd7d0e8: ldp             fp, lr, [SP], #0x10
    // 0xd7d0ec: ret
    //     0xd7d0ec: ret             
    // 0xd7d0f0: r0 = 60
    //     0xd7d0f0: movz            x0, #0x3c
    // 0xd7d0f4: branchIfSmi(r1, 0xd7d100)
    //     0xd7d0f4: tbz             w1, #0, #0xd7d100
    // 0xd7d0f8: r0 = LoadClassIdInstr(r1)
    //     0xd7d0f8: ldur            x0, [x1, #-1]
    //     0xd7d0fc: ubfx            x0, x0, #0xc, #0x14
    // 0xd7d100: cmp             x0, #0x2d5
    // 0xd7d104: b.ne            #0xd7d17c
    // 0xd7d108: ldr             x2, [fp, #0x18]
    // 0xd7d10c: LoadField: r0 = r2->field_b
    //     0xd7d10c: ldur            w0, [x2, #0xb]
    // 0xd7d110: DecompressPointer r0
    //     0xd7d110: add             x0, x0, HEAP, lsl #32
    // 0xd7d114: LoadField: r3 = r1->field_b
    //     0xd7d114: ldur            w3, [x1, #0xb]
    // 0xd7d118: DecompressPointer r3
    //     0xd7d118: add             x3, x3, HEAP, lsl #32
    // 0xd7d11c: r4 = 60
    //     0xd7d11c: movz            x4, #0x3c
    // 0xd7d120: branchIfSmi(r0, 0xd7d12c)
    //     0xd7d120: tbz             w0, #0, #0xd7d12c
    // 0xd7d124: r4 = LoadClassIdInstr(r0)
    //     0xd7d124: ldur            x4, [x0, #-1]
    //     0xd7d128: ubfx            x4, x4, #0xc, #0x14
    // 0xd7d12c: stp             x3, x0, [SP]
    // 0xd7d130: mov             x0, x4
    // 0xd7d134: mov             lr, x0
    // 0xd7d138: ldr             lr, [x21, lr, lsl #3]
    // 0xd7d13c: blr             lr
    // 0xd7d140: tbnz            w0, #4, #0xd7d17c
    // 0xd7d144: ldr             x2, [fp, #0x18]
    // 0xd7d148: ldr             x1, [fp, #0x10]
    // 0xd7d14c: LoadField: r3 = r2->field_13
    //     0xd7d14c: ldur            x3, [x2, #0x13]
    // 0xd7d150: LoadField: r4 = r1->field_13
    //     0xd7d150: ldur            x4, [x1, #0x13]
    // 0xd7d154: cmp             x3, x4
    // 0xd7d158: b.ne            #0xd7d17c
    // 0xd7d15c: LoadField: r3 = r2->field_1b
    //     0xd7d15c: ldur            x3, [x2, #0x1b]
    // 0xd7d160: LoadField: r2 = r1->field_1b
    //     0xd7d160: ldur            x2, [x1, #0x1b]
    // 0xd7d164: cmp             x3, x2
    // 0xd7d168: r16 = true
    //     0xd7d168: add             x16, NULL, #0x20  ; true
    // 0xd7d16c: r17 = false
    //     0xd7d16c: add             x17, NULL, #0x30  ; false
    // 0xd7d170: csel            x1, x16, x17, eq
    // 0xd7d174: mov             x0, x1
    // 0xd7d178: b               #0xd7d180
    // 0xd7d17c: r0 = false
    //     0xd7d17c: add             x0, NULL, #0x30  ; false
    // 0xd7d180: LeaveFrame
    //     0xd7d180: mov             SP, fp
    //     0xd7d184: ldp             fp, lr, [SP], #0x10
    // 0xd7d188: ret
    //     0xd7d188: ret             
    // 0xd7d18c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7d18c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7d190: b               #0xd7d0d4
  }
}
