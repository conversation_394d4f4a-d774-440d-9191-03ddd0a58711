// lib: , url: package:petitparser/src/definition/internal/reference.dart

// class id: 1050876, size: 0x8
class :: {

  static _ _throwUnsupported(/* No info */) {
    // ** addr: 0xeaf8b4, size: 0x28
    // 0xeaf8b4: EnterFrame
    //     0xeaf8b4: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf8b8: mov             fp, SP
    // 0xeaf8bc: r0 = UnsupportedError()
    //     0xeaf8bc: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0xeaf8c0: mov             x1, x0
    // 0xeaf8c4: r0 = "Unsupported operation on parser reference"
    //     0xeaf8c4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31448] "Unsupported operation on parser reference"
    //     0xeaf8c8: ldr             x0, [x0, #0x448]
    // 0xeaf8cc: StoreField: r1->field_b = r0
    //     0xeaf8cc: stur            w0, [x1, #0xb]
    // 0xeaf8d0: mov             x0, x1
    // 0xeaf8d4: r0 = Throw()
    //     0xeaf8d4: bl              #0xec04b8  ; ThrowStub
    // 0xeaf8d8: brk             #0
  }
}

// class id: 751, size: 0x14, field offset: 0xc
class ReferenceParser<X0> extends Parser<X0>
    implements ResolvableParser<X0> {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf1c8c, size: 0x40
    // 0xbf1c8c: EnterFrame
    //     0xbf1c8c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf1c90: mov             fp, SP
    // 0xbf1c94: AllocStack(0x8)
    //     0xbf1c94: sub             SP, SP, #8
    // 0xbf1c98: CheckStackOverflow
    //     0xbf1c98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf1c9c: cmp             SP, x16
    //     0xbf1ca0: b.ls            #0xbf1cc4
    // 0xbf1ca4: ldr             x0, [fp, #0x10]
    // 0xbf1ca8: LoadField: r1 = r0->field_b
    //     0xbf1ca8: ldur            w1, [x0, #0xb]
    // 0xbf1cac: DecompressPointer r1
    //     0xbf1cac: add             x1, x1, HEAP, lsl #32
    // 0xbf1cb0: str             x1, [SP]
    // 0xbf1cb4: r0 = hashCode()
    //     0xbf1cb4: bl              #0xbf4d24  ; [dart:core] _Closure::hashCode
    // 0xbf1cb8: LeaveFrame
    //     0xbf1cb8: mov             SP, fp
    //     0xbf1cbc: ldp             fp, lr, [SP], #0x10
    // 0xbf1cc0: ret
    //     0xbf1cc0: ret             
    // 0xbf1cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf1cc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf1cc8: b               #0xbf1ca4
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d004, size: 0xb8
    // 0xd7d004: EnterFrame
    //     0xd7d004: stp             fp, lr, [SP, #-0x10]!
    //     0xd7d008: mov             fp, SP
    // 0xd7d00c: AllocStack(0x10)
    //     0xd7d00c: sub             SP, SP, #0x10
    // 0xd7d010: CheckStackOverflow
    //     0xd7d010: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7d014: cmp             SP, x16
    //     0xd7d018: b.ls            #0xd7d0ac
    // 0xd7d01c: ldr             x0, [fp, #0x10]
    // 0xd7d020: cmp             w0, NULL
    // 0xd7d024: b.ne            #0xd7d038
    // 0xd7d028: r0 = false
    //     0xd7d028: add             x0, NULL, #0x30  ; false
    // 0xd7d02c: LeaveFrame
    //     0xd7d02c: mov             SP, fp
    //     0xd7d030: ldp             fp, lr, [SP], #0x10
    // 0xd7d034: ret
    //     0xd7d034: ret             
    // 0xd7d038: r1 = 60
    //     0xd7d038: movz            x1, #0x3c
    // 0xd7d03c: branchIfSmi(r0, 0xd7d048)
    //     0xd7d03c: tbz             w0, #0, #0xd7d048
    // 0xd7d040: r1 = LoadClassIdInstr(r0)
    //     0xd7d040: ldur            x1, [x0, #-1]
    //     0xd7d044: ubfx            x1, x1, #0xc, #0x14
    // 0xd7d048: cmp             x1, #0x2ef
    // 0xd7d04c: b.ne            #0xd7d09c
    // 0xd7d050: ldr             x1, [fp, #0x18]
    // 0xd7d054: LoadField: r2 = r1->field_b
    //     0xd7d054: ldur            w2, [x1, #0xb]
    // 0xd7d058: DecompressPointer r2
    //     0xd7d058: add             x2, x2, HEAP, lsl #32
    // 0xd7d05c: LoadField: r1 = r0->field_b
    //     0xd7d05c: ldur            w1, [x0, #0xb]
    // 0xd7d060: DecompressPointer r1
    //     0xd7d060: add             x1, x1, HEAP, lsl #32
    // 0xd7d064: stp             x1, x2, [SP]
    // 0xd7d068: r0 = ==()
    //     0xd7d068: bl              #0xd81860  ; [dart:core] _Closure::==
    // 0xd7d06c: tbz             w0, #4, #0xd7d080
    // 0xd7d070: r0 = false
    //     0xd7d070: add             x0, NULL, #0x30  ; false
    // 0xd7d074: LeaveFrame
    //     0xd7d074: mov             SP, fp
    //     0xd7d078: ldp             fp, lr, [SP], #0x10
    // 0xd7d07c: ret
    //     0xd7d07c: ret             
    // 0xd7d080: CheckStackOverflow
    //     0xd7d080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7d084: cmp             SP, x16
    //     0xd7d088: b.ls            #0xd7d0b4
    // 0xd7d08c: r0 = true
    //     0xd7d08c: add             x0, NULL, #0x20  ; true
    // 0xd7d090: LeaveFrame
    //     0xd7d090: mov             SP, fp
    //     0xd7d094: ldp             fp, lr, [SP], #0x10
    // 0xd7d098: ret
    //     0xd7d098: ret             
    // 0xd7d09c: r0 = false
    //     0xd7d09c: add             x0, NULL, #0x30  ; false
    // 0xd7d0a0: LeaveFrame
    //     0xd7d0a0: mov             SP, fp
    //     0xd7d0a4: ldp             fp, lr, [SP], #0x10
    // 0xd7d0a8: ret
    //     0xd7d0a8: ret             
    // 0xd7d0ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7d0ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7d0b0: b               #0xd7d01c
    // 0xd7d0b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7d0b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7d0b8: b               #0xd7d08c
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb0ecc, size: 0x2c
    // 0xeb0ecc: EnterFrame
    //     0xeb0ecc: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0ed0: mov             fp, SP
    // 0xeb0ed4: CheckStackOverflow
    //     0xeb0ed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0ed8: cmp             SP, x16
    //     0xeb0edc: b.ls            #0xeb0ef0
    // 0xeb0ee0: r0 = _throwUnsupported()
    //     0xeb0ee0: bl              #0xeaf8b4  ; [package:petitparser/src/definition/internal/reference.dart] ::_throwUnsupported
    // 0xeb0ee4: LeaveFrame
    //     0xeb0ee4: mov             SP, fp
    //     0xeb0ee8: ldp             fp, lr, [SP], #0x10
    // 0xeb0eec: ret
    //     0xeb0eec: ret             
    // 0xeb0ef0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0ef0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0ef4: b               #0xeb0ee0
  }
}
