// lib: , url: package:petitparser/src/definition/reference.dart

// class id: 1050877, size: 0x8
class :: {

  static _ ref0(/* No info */) {
    // ** addr: 0x88a29c, size: 0x4c
    // 0x88a29c: EnterFrame
    //     0x88a29c: stp             fp, lr, [SP, #-0x10]!
    //     0x88a2a0: mov             fp, SP
    // 0x88a2a4: LoadField: r0 = r4->field_f
    //     0x88a2a4: ldur            w0, [x4, #0xf]
    // 0x88a2a8: cbnz            w0, #0x88a2b4
    // 0x88a2ac: r1 = Null
    //     0x88a2ac: mov             x1, NULL
    // 0x88a2b0: b               #0x88a2c0
    // 0x88a2b4: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88a2b4: ldur            w0, [x4, #0x17]
    // 0x88a2b8: add             x1, fp, w0, sxtw #2
    // 0x88a2bc: ldr             x1, [x1, #0x10]
    // 0x88a2c0: ldr             x0, [fp, #0x10]
    // 0x88a2c4: r0 = ReferenceParser()
    //     0x88a2c4: bl              #0x88a2e8  ; AllocateReferenceParserStub -> ReferenceParser<X0> (size=0x14)
    // 0x88a2c8: ldr             x1, [fp, #0x10]
    // 0x88a2cc: StoreField: r0->field_b = r1
    //     0x88a2cc: stur            w1, [x0, #0xb]
    // 0x88a2d0: r1 = const []
    //     0x88a2d0: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0d8] List(0) []
    //     0x88a2d4: ldr             x1, [x1, #0xd8]
    // 0x88a2d8: StoreField: r0->field_f = r1
    //     0x88a2d8: stur            w1, [x0, #0xf]
    // 0x88a2dc: LeaveFrame
    //     0x88a2dc: mov             SP, fp
    //     0x88a2e0: ldp             fp, lr, [SP], #0x10
    // 0x88a2e4: ret
    //     0x88a2e4: ret             
  }
}
