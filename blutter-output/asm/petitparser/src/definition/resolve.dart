// lib: , url: package:petitparser/src/definition/resolve.dart

// class id: 1050878, size: 0x8
class :: {

  static _ resolve(/* No info */) {
    // ** addr: 0x8894ec, size: 0x3b4
    // 0x8894ec: EnterFrame
    //     0x8894ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8894f0: mov             fp, SP
    // 0x8894f4: AllocStack(0x60)
    //     0x8894f4: sub             SP, SP, #0x60
    // 0x8894f8: SetupParameters()
    //     0x8894f8: ldur            w0, [x4, #0xf]
    //     0x8894fc: cbnz            w0, #0x889508
    //     0x889500: mov             x0, NULL
    //     0x889504: b               #0x889518
    //     0x889508: ldur            w0, [x4, #0x17]
    //     0x88950c: add             x1, fp, w0, sxtw #2
    //     0x889510: ldr             x1, [x1, #0x10]
    //     0x889514: mov             x0, x1
    //     0x889518: stur            x0, [fp, #-8]
    // 0x88951c: CheckStackOverflow
    //     0x88951c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x889520: cmp             SP, x16
    //     0x889524: b.ls            #0x889884
    // 0x889528: r16 = <ResolvableParser, Parser>
    //     0x889528: add             x16, PP, #0x26, lsl #12  ; [pp+0x26be8] TypeArguments: <ResolvableParser, Parser>
    //     0x88952c: ldr             x16, [x16, #0xbe8]
    // 0x889530: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x889534: stp             lr, x16, [SP]
    // 0x889538: r0 = Map._fromLiteral()
    //     0x889538: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x88953c: stur            x0, [fp, #-0x10]
    // 0x889540: ldur            x16, [fp, #-8]
    // 0x889544: ldr             lr, [fp, #0x10]
    // 0x889548: stp             lr, x16, [SP, #8]
    // 0x88954c: str             x0, [SP]
    // 0x889550: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x889550: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x889554: r0 = _dereference()
    //     0x889554: bl              #0x8898a0  ; [package:petitparser/src/definition/resolve.dart] ::_dereference
    // 0x889558: r1 = Null
    //     0x889558: mov             x1, NULL
    // 0x88955c: r2 = 2
    //     0x88955c: movz            x2, #0x2
    // 0x889560: stur            x0, [fp, #-8]
    // 0x889564: r0 = AllocateArray()
    //     0x889564: bl              #0xec22fc  ; AllocateArrayStub
    // 0x889568: ldur            x2, [fp, #-8]
    // 0x88956c: stur            x0, [fp, #-0x18]
    // 0x889570: StoreField: r0->field_f = r2
    //     0x889570: stur            w2, [x0, #0xf]
    // 0x889574: r1 = <Parser>
    //     0x889574: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x889578: ldr             x1, [x1, #0x6f8]
    // 0x88957c: r0 = AllocateGrowableArray()
    //     0x88957c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x889580: mov             x1, x0
    // 0x889584: ldur            x0, [fp, #-0x18]
    // 0x889588: stur            x1, [fp, #-0x20]
    // 0x88958c: StoreField: r1->field_f = r0
    //     0x88958c: stur            w0, [x1, #0xf]
    // 0x889590: r0 = 2
    //     0x889590: movz            x0, #0x2
    // 0x889594: StoreField: r1->field_b = r0
    //     0x889594: stur            w0, [x1, #0xb]
    // 0x889598: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x889598: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x88959c: ldr             x0, [x0, #0x778]
    //     0x8895a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8895a4: cmp             w0, w16
    //     0x8895a8: b.ne            #0x8895b4
    //     0x8895ac: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x8895b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8895b4: r1 = <Parser>
    //     0x8895b4: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x8895b8: ldr             x1, [x1, #0x6f8]
    // 0x8895bc: stur            x0, [fp, #-0x18]
    // 0x8895c0: r0 = _Set()
    //     0x8895c0: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x8895c4: mov             x1, x0
    // 0x8895c8: ldur            x0, [fp, #-0x18]
    // 0x8895cc: stur            x1, [fp, #-0x28]
    // 0x8895d0: StoreField: r1->field_1b = r0
    //     0x8895d0: stur            w0, [x1, #0x1b]
    // 0x8895d4: StoreField: r1->field_b = rZR
    //     0x8895d4: stur            wzr, [x1, #0xb]
    // 0x8895d8: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x8895d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8895dc: ldr             x0, [x0, #0x780]
    //     0x8895e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8895e4: cmp             w0, w16
    //     0x8895e8: b.ne            #0x8895f4
    //     0x8895ec: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x8895f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8895f4: mov             x1, x0
    // 0x8895f8: ldur            x0, [fp, #-0x28]
    // 0x8895fc: StoreField: r0->field_f = r1
    //     0x8895fc: stur            w1, [x0, #0xf]
    // 0x889600: StoreField: r0->field_13 = rZR
    //     0x889600: stur            wzr, [x0, #0x13]
    // 0x889604: ArrayStore: r0[0] = rZR  ; List_4
    //     0x889604: stur            wzr, [x0, #0x17]
    // 0x889608: mov             x1, x0
    // 0x88960c: ldur            x2, [fp, #-8]
    // 0x889610: r0 = add()
    //     0x889610: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x889614: ldur            x3, [fp, #-0x20]
    // 0x889618: CheckStackOverflow
    //     0x889618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88961c: cmp             SP, x16
    //     0x889620: b.ls            #0x88988c
    // 0x889624: LoadField: r0 = r3->field_b
    //     0x889624: ldur            w0, [x3, #0xb]
    // 0x889628: r1 = LoadInt32Instr(r0)
    //     0x889628: sbfx            x1, x0, #1, #0x1f
    // 0x88962c: cbz             x1, #0x889874
    // 0x889630: sub             x2, x1, #1
    // 0x889634: mov             x0, x1
    // 0x889638: mov             x1, x2
    // 0x88963c: cmp             x1, x0
    // 0x889640: b.hs            #0x889894
    // 0x889644: LoadField: r0 = r3->field_f
    //     0x889644: ldur            w0, [x3, #0xf]
    // 0x889648: DecompressPointer r0
    //     0x889648: add             x0, x0, HEAP, lsl #32
    // 0x88964c: ArrayLoad: r4 = r0[r2]  ; Unknown_4
    //     0x88964c: add             x16, x0, x2, lsl #2
    //     0x889650: ldur            w4, [x16, #0xf]
    // 0x889654: DecompressPointer r4
    //     0x889654: add             x4, x4, HEAP, lsl #32
    // 0x889658: mov             x1, x3
    // 0x88965c: stur            x4, [fp, #-0x18]
    // 0x889660: r0 = length=()
    //     0x889660: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0x889664: ldur            x2, [fp, #-0x18]
    // 0x889668: r0 = LoadClassIdInstr(r2)
    //     0x889668: ldur            x0, [x2, #-1]
    //     0x88966c: ubfx            x0, x0, #0xc, #0x14
    // 0x889670: mov             x1, x2
    // 0x889674: r0 = GDT[cid_x0 + 0xec92]()
    //     0x889674: movz            x17, #0xec92
    //     0x889678: add             lr, x0, x17
    //     0x88967c: ldr             lr, [x21, lr, lsl #3]
    //     0x889680: blr             lr
    // 0x889684: r1 = LoadClassIdInstr(r0)
    //     0x889684: ldur            x1, [x0, #-1]
    //     0x889688: ubfx            x1, x1, #0xc, #0x14
    // 0x88968c: mov             x16, x0
    // 0x889690: mov             x0, x1
    // 0x889694: mov             x1, x16
    // 0x889698: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x889698: movz            x17, #0xd35d
    //     0x88969c: add             lr, x0, x17
    //     0x8896a0: ldr             lr, [x21, lr, lsl #3]
    //     0x8896a4: blr             lr
    // 0x8896a8: mov             x2, x0
    // 0x8896ac: stur            x2, [fp, #-0x30]
    // 0x8896b0: ldur            x4, [fp, #-0x20]
    // 0x8896b4: ldur            x3, [fp, #-0x18]
    // 0x8896b8: CheckStackOverflow
    //     0x8896b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8896bc: cmp             SP, x16
    //     0x8896c0: b.ls            #0x889898
    // 0x8896c4: r0 = LoadClassIdInstr(r2)
    //     0x8896c4: ldur            x0, [x2, #-1]
    //     0x8896c8: ubfx            x0, x0, #0xc, #0x14
    // 0x8896cc: mov             x1, x2
    // 0x8896d0: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x8896d0: movz            x17, #0x292d
    //     0x8896d4: movk            x17, #0x1, lsl #16
    //     0x8896d8: add             lr, x0, x17
    //     0x8896dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8896e0: blr             lr
    // 0x8896e4: tbnz            w0, #4, #0x889868
    // 0x8896e8: ldur            x2, [fp, #-0x30]
    // 0x8896ec: r0 = LoadClassIdInstr(r2)
    //     0x8896ec: ldur            x0, [x2, #-1]
    //     0x8896f0: ubfx            x0, x0, #0xc, #0x14
    // 0x8896f4: mov             x1, x2
    // 0x8896f8: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x8896f8: movz            x17, #0x384d
    //     0x8896fc: movk            x17, #0x1, lsl #16
    //     0x889700: add             lr, x0, x17
    //     0x889704: ldr             lr, [x21, lr, lsl #3]
    //     0x889708: blr             lr
    // 0x88970c: stur            x0, [fp, #-0x38]
    // 0x889710: r1 = 60
    //     0x889710: movz            x1, #0x3c
    // 0x889714: branchIfSmi(r0, 0x889720)
    //     0x889714: tbz             w0, #0, #0x889720
    // 0x889718: r1 = LoadClassIdInstr(r0)
    //     0x889718: ldur            x1, [x0, #-1]
    //     0x88971c: ubfx            x1, x1, #0xc, #0x14
    // 0x889720: cmp             x1, #0x2ef
    // 0x889724: b.ne            #0x889778
    // 0x889728: ldur            x1, [fp, #-0x18]
    // 0x88972c: stp             x0, NULL, [SP, #8]
    // 0x889730: ldur            x16, [fp, #-0x10]
    // 0x889734: str             x16, [SP]
    // 0x889738: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x889738: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88973c: r0 = _dereference()
    //     0x88973c: bl              #0x8898a0  ; [package:petitparser/src/definition/resolve.dart] ::_dereference
    // 0x889740: mov             x5, x0
    // 0x889744: ldur            x4, [fp, #-0x18]
    // 0x889748: stur            x5, [fp, #-0x40]
    // 0x88974c: r0 = LoadClassIdInstr(r4)
    //     0x88974c: ldur            x0, [x4, #-1]
    //     0x889750: ubfx            x0, x0, #0xc, #0x14
    // 0x889754: mov             x1, x4
    // 0x889758: ldur            x2, [fp, #-0x38]
    // 0x88975c: mov             x3, x5
    // 0x889760: r0 = GDT[cid_x0 + 0xeafa]()
    //     0x889760: movz            x17, #0xeafa
    //     0x889764: add             lr, x0, x17
    //     0x889768: ldr             lr, [x21, lr, lsl #3]
    //     0x88976c: blr             lr
    // 0x889770: ldur            x3, [fp, #-0x40]
    // 0x889774: b               #0x88977c
    // 0x889778: ldur            x3, [fp, #-0x38]
    // 0x88977c: mov             x0, x3
    // 0x889780: stur            x3, [fp, #-0x38]
    // 0x889784: r2 = Null
    //     0x889784: mov             x2, NULL
    // 0x889788: r1 = Null
    //     0x889788: mov             x1, NULL
    // 0x88978c: r4 = 60
    //     0x88978c: movz            x4, #0x3c
    // 0x889790: branchIfSmi(r0, 0x88979c)
    //     0x889790: tbz             w0, #0, #0x88979c
    // 0x889794: r4 = LoadClassIdInstr(r0)
    //     0x889794: ldur            x4, [x0, #-1]
    //     0x889798: ubfx            x4, x4, #0xc, #0x14
    // 0x88979c: sub             x4, x4, #0x2d7
    // 0x8897a0: cmp             x4, #0x18
    // 0x8897a4: b.ls            #0x8897bc
    // 0x8897a8: r8 = Parser
    //     0x8897a8: add             x8, PP, #0x26, lsl #12  ; [pp+0x26bf0] Type: Parser
    //     0x8897ac: ldr             x8, [x8, #0xbf0]
    // 0x8897b0: r3 = Null
    //     0x8897b0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26bf8] Null
    //     0x8897b4: ldr             x3, [x3, #0xbf8]
    // 0x8897b8: r0 = Parser()
    //     0x8897b8: bl              #0x88a258  ; IsType_Parser_Stub
    // 0x8897bc: ldur            x1, [fp, #-0x28]
    // 0x8897c0: ldur            x2, [fp, #-0x38]
    // 0x8897c4: r0 = _hashCode()
    //     0x8897c4: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x8897c8: ldur            x1, [fp, #-0x28]
    // 0x8897cc: ldur            x2, [fp, #-0x38]
    // 0x8897d0: mov             x3, x0
    // 0x8897d4: r0 = _add()
    //     0x8897d4: bl              #0x69b44c  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_add
    // 0x8897d8: tbnz            w0, #4, #0x889858
    // 0x8897dc: ldur            x0, [fp, #-0x20]
    // 0x8897e0: LoadField: r1 = r0->field_b
    //     0x8897e0: ldur            w1, [x0, #0xb]
    // 0x8897e4: LoadField: r2 = r0->field_f
    //     0x8897e4: ldur            w2, [x0, #0xf]
    // 0x8897e8: DecompressPointer r2
    //     0x8897e8: add             x2, x2, HEAP, lsl #32
    // 0x8897ec: LoadField: r3 = r2->field_b
    //     0x8897ec: ldur            w3, [x2, #0xb]
    // 0x8897f0: r2 = LoadInt32Instr(r1)
    //     0x8897f0: sbfx            x2, x1, #1, #0x1f
    // 0x8897f4: stur            x2, [fp, #-0x48]
    // 0x8897f8: r1 = LoadInt32Instr(r3)
    //     0x8897f8: sbfx            x1, x3, #1, #0x1f
    // 0x8897fc: cmp             x2, x1
    // 0x889800: b.ne            #0x88980c
    // 0x889804: mov             x1, x0
    // 0x889808: r0 = _growToNextCapacity()
    //     0x889808: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x88980c: ldur            x2, [fp, #-0x20]
    // 0x889810: ldur            x3, [fp, #-0x48]
    // 0x889814: add             x4, x3, #1
    // 0x889818: lsl             x5, x4, #1
    // 0x88981c: StoreField: r2->field_b = r5
    //     0x88981c: stur            w5, [x2, #0xb]
    // 0x889820: LoadField: r1 = r2->field_f
    //     0x889820: ldur            w1, [x2, #0xf]
    // 0x889824: DecompressPointer r1
    //     0x889824: add             x1, x1, HEAP, lsl #32
    // 0x889828: ldur            x0, [fp, #-0x38]
    // 0x88982c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x88982c: add             x25, x1, x3, lsl #2
    //     0x889830: add             x25, x25, #0xf
    //     0x889834: str             w0, [x25]
    //     0x889838: tbz             w0, #0, #0x889854
    //     0x88983c: ldurb           w16, [x1, #-1]
    //     0x889840: ldurb           w17, [x0, #-1]
    //     0x889844: and             x16, x17, x16, lsr #2
    //     0x889848: tst             x16, HEAP, lsr #32
    //     0x88984c: b.eq            #0x889854
    //     0x889850: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x889854: b               #0x88985c
    // 0x889858: ldur            x2, [fp, #-0x20]
    // 0x88985c: mov             x4, x2
    // 0x889860: ldur            x2, [fp, #-0x30]
    // 0x889864: b               #0x8896b4
    // 0x889868: ldur            x2, [fp, #-0x20]
    // 0x88986c: mov             x3, x2
    // 0x889870: b               #0x889618
    // 0x889874: ldur            x0, [fp, #-8]
    // 0x889878: LeaveFrame
    //     0x889878: mov             SP, fp
    //     0x88987c: ldp             fp, lr, [SP], #0x10
    // 0x889880: ret
    //     0x889880: ret             
    // 0x889884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889884: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889888: b               #0x889528
    // 0x88988c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88988c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889890: b               #0x889624
    // 0x889894: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x889894: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x889898: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889898: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88989c: b               #0x8896c4
  }
  static Parser<Y0> _dereference<Y0>(Parser<Y0>, Map<Parser<dynamic>, Parser<dynamic>>) {
    // ** addr: 0x8898a0, size: 0x438
    // 0x8898a0: EnterFrame
    //     0x8898a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8898a4: mov             fp, SP
    // 0x8898a8: AllocStack(0x38)
    //     0x8898a8: sub             SP, SP, #0x38
    // 0x8898ac: SetupParameters()
    //     0x8898ac: ldur            w0, [x4, #0xf]
    //     0x8898b0: cbnz            w0, #0x8898bc
    //     0x8898b4: mov             x4, NULL
    //     0x8898b8: b               #0x8898cc
    //     0x8898bc: ldur            w0, [x4, #0x17]
    //     0x8898c0: add             x1, fp, w0, sxtw #2
    //     0x8898c4: ldr             x1, [x1, #0x10]
    //     0x8898c8: mov             x4, x1
    //     0x8898cc: ldr             x0, [fp, #0x18]
    //     0x8898d0: stur            x4, [fp, #-8]
    // 0x8898d4: CheckStackOverflow
    //     0x8898d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8898d8: cmp             SP, x16
    //     0x8898dc: b.ls            #0x889cc0
    // 0x8898e0: mov             x1, x4
    // 0x8898e4: r2 = Null
    //     0x8898e4: mov             x2, NULL
    // 0x8898e8: r3 = <ResolvableParser<Y0>>
    //     0x8898e8: add             x3, PP, #0x26, lsl #12  ; [pp+0x26c08] TypeArguments: <ResolvableParser<Y0>>
    //     0x8898ec: ldr             x3, [x3, #0xc08]
    // 0x8898f0: r30 = InstantiateTypeArgumentsStub
    //     0x8898f0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8898f4: LoadField: r30 = r30->field_7
    //     0x8898f4: ldur            lr, [lr, #7]
    // 0x8898f8: blr             lr
    // 0x8898fc: stur            x0, [fp, #-0x10]
    // 0x889900: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x889900: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x889904: ldr             x0, [x0, #0x778]
    //     0x889908: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x88990c: cmp             w0, w16
    //     0x889910: b.ne            #0x88991c
    //     0x889914: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x889918: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x88991c: ldur            x1, [fp, #-0x10]
    // 0x889920: stur            x0, [fp, #-0x18]
    // 0x889924: r0 = _Set()
    //     0x889924: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x889928: mov             x1, x0
    // 0x88992c: ldur            x0, [fp, #-0x18]
    // 0x889930: stur            x1, [fp, #-0x20]
    // 0x889934: StoreField: r1->field_1b = r0
    //     0x889934: stur            w0, [x1, #0x1b]
    // 0x889938: StoreField: r1->field_b = rZR
    //     0x889938: stur            wzr, [x1, #0xb]
    // 0x88993c: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x88993c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x889940: ldr             x0, [x0, #0x780]
    //     0x889944: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x889948: cmp             w0, w16
    //     0x88994c: b.ne            #0x889958
    //     0x889950: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x889954: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x889958: ldur            x3, [fp, #-0x20]
    // 0x88995c: StoreField: r3->field_f = r0
    //     0x88995c: stur            w0, [x3, #0xf]
    // 0x889960: StoreField: r3->field_13 = rZR
    //     0x889960: stur            wzr, [x3, #0x13]
    // 0x889964: ArrayStore: r3[0] = rZR  ; List_4
    //     0x889964: stur            wzr, [x3, #0x17]
    // 0x889968: ldr             x5, [fp, #0x18]
    // 0x88996c: ldr             x4, [fp, #0x10]
    // 0x889970: stur            x5, [fp, #-0x18]
    // 0x889974: CheckStackOverflow
    //     0x889974: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x889978: cmp             SP, x16
    //     0x88997c: b.ls            #0x889cc8
    // 0x889980: mov             x0, x5
    // 0x889984: ldur            x1, [fp, #-8]
    // 0x889988: r2 = Null
    //     0x889988: mov             x2, NULL
    // 0x88998c: cmp             w0, NULL
    // 0x889990: b.eq            #0x8899dc
    // 0x889994: branchIfSmi(r0, 0x8899dc)
    //     0x889994: tbz             w0, #0, #0x8899dc
    // 0x889998: r3 = SubtypeTestCache
    //     0x889998: add             x3, PP, #0x26, lsl #12  ; [pp+0x26c10] SubtypeTestCache
    //     0x88999c: ldr             x3, [x3, #0xc10]
    // 0x8899a0: r30 = Subtype4TestCacheStub
    //     0x8899a0: ldr             lr, [PP, #0x20]  ; [pp+0x20] Stub: Subtype4TestCache (0x5f2a74)
    // 0x8899a4: LoadField: r30 = r30->field_7
    //     0x8899a4: ldur            lr, [lr, #7]
    // 0x8899a8: blr             lr
    // 0x8899ac: cmp             w7, NULL
    // 0x8899b0: b.eq            #0x8899bc
    // 0x8899b4: tbnz            w7, #4, #0x8899dc
    // 0x8899b8: b               #0x8899e4
    // 0x8899bc: r8 = ResolvableParser<Y0>
    //     0x8899bc: add             x8, PP, #0x26, lsl #12  ; [pp+0x26c18] Type: ResolvableParser<Y0>
    //     0x8899c0: ldr             x8, [x8, #0xc18]
    // 0x8899c4: r3 = SubtypeTestCache
    //     0x8899c4: add             x3, PP, #0x26, lsl #12  ; [pp+0x26c20] SubtypeTestCache
    //     0x8899c8: ldr             x3, [x3, #0xc20]
    // 0x8899cc: r30 = InstanceOfStub
    //     0x8899cc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8899d0: LoadField: r30 = r30->field_7
    //     0x8899d0: ldur            lr, [lr, #7]
    // 0x8899d4: blr             lr
    // 0x8899d8: b               #0x8899e8
    // 0x8899dc: r0 = false
    //     0x8899dc: add             x0, NULL, #0x30  ; false
    // 0x8899e0: b               #0x8899e8
    // 0x8899e4: r0 = true
    //     0x8899e4: add             x0, NULL, #0x20  ; true
    // 0x8899e8: tbnz            w0, #4, #0x889b34
    // 0x8899ec: ldr             x0, [fp, #0x10]
    // 0x8899f0: LoadField: r3 = r0->field_f
    //     0x8899f0: ldur            w3, [x0, #0xf]
    // 0x8899f4: DecompressPointer r3
    //     0x8899f4: add             x3, x3, HEAP, lsl #32
    // 0x8899f8: mov             x1, x0
    // 0x8899fc: ldur            x2, [fp, #-0x18]
    // 0x889a00: stur            x3, [fp, #-0x28]
    // 0x889a04: r0 = _getValueOrData()
    //     0x889a04: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x889a08: mov             x1, x0
    // 0x889a0c: ldur            x0, [fp, #-0x28]
    // 0x889a10: cmp             w0, w1
    // 0x889a14: b.ne            #0x889ac8
    // 0x889a18: ldur            x0, [fp, #-0x18]
    // 0x889a1c: ldur            x2, [fp, #-0x10]
    // 0x889a20: r1 = Null
    //     0x889a20: mov             x1, NULL
    // 0x889a24: cmp             w2, NULL
    // 0x889a28: b.eq            #0x889a48
    // 0x889a2c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x889a2c: ldur            w4, [x2, #0x17]
    // 0x889a30: DecompressPointer r4
    //     0x889a30: add             x4, x4, HEAP, lsl #32
    // 0x889a34: r8 = X0
    //     0x889a34: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x889a38: LoadField: r9 = r4->field_7
    //     0x889a38: ldur            x9, [x4, #7]
    // 0x889a3c: r3 = Null
    //     0x889a3c: add             x3, PP, #0x26, lsl #12  ; [pp+0x26c28] Null
    //     0x889a40: ldr             x3, [x3, #0xc28]
    // 0x889a44: blr             x9
    // 0x889a48: ldur            x1, [fp, #-0x20]
    // 0x889a4c: ldur            x2, [fp, #-0x18]
    // 0x889a50: r0 = _hashCode()
    //     0x889a50: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x889a54: ldur            x1, [fp, #-0x20]
    // 0x889a58: ldur            x2, [fp, #-0x18]
    // 0x889a5c: mov             x3, x0
    // 0x889a60: r0 = _add()
    //     0x889a60: bl              #0x69b44c  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_add
    // 0x889a64: tbnz            w0, #4, #0x889c74
    // 0x889a68: ldur            x0, [fp, #-0x18]
    // 0x889a6c: LoadField: r1 = r0->field_b
    //     0x889a6c: ldur            w1, [x0, #0xb]
    // 0x889a70: DecompressPointer r1
    //     0x889a70: add             x1, x1, HEAP, lsl #32
    // 0x889a74: r2 = const []
    //     0x889a74: add             x2, PP, #0xf, lsl #12  ; [pp+0xf0d8] List(0) []
    //     0x889a78: ldr             x2, [x2, #0xd8]
    // 0x889a7c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x889a7c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x889a80: r0 = apply()
    //     0x889a80: bl              #0x889cd8  ; [dart:core] Function::apply
    // 0x889a84: mov             x3, x0
    // 0x889a88: ldur            x2, [fp, #-0x18]
    // 0x889a8c: stur            x3, [fp, #-0x28]
    // 0x889a90: LoadField: r0 = r2->field_7
    //     0x889a90: ldur            w0, [x2, #7]
    // 0x889a94: DecompressPointer r0
    //     0x889a94: add             x0, x0, HEAP, lsl #32
    // 0x889a98: mov             x2, x0
    // 0x889a9c: mov             x0, x3
    // 0x889aa0: r1 = Null
    //     0x889aa0: mov             x1, NULL
    // 0x889aa4: r8 = Parser<X0>
    //     0x889aa4: add             x8, PP, #0x26, lsl #12  ; [pp+0x26c38] Type: Parser<X0>
    //     0x889aa8: ldr             x8, [x8, #0xc38]
    // 0x889aac: LoadField: r9 = r8->field_7
    //     0x889aac: ldur            x9, [x8, #7]
    // 0x889ab0: r3 = Null
    //     0x889ab0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26c40] Null
    //     0x889ab4: ldr             x3, [x3, #0xc40]
    // 0x889ab8: blr             x9
    // 0x889abc: ldur            x5, [fp, #-0x28]
    // 0x889ac0: ldur            x3, [fp, #-0x20]
    // 0x889ac4: b               #0x88996c
    // 0x889ac8: ldr             x0, [fp, #0x10]
    // 0x889acc: ldur            x2, [fp, #-0x18]
    // 0x889ad0: mov             x1, x0
    // 0x889ad4: r0 = _getValueOrData()
    //     0x889ad4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x889ad8: mov             x1, x0
    // 0x889adc: ldr             x0, [fp, #0x10]
    // 0x889ae0: LoadField: r2 = r0->field_f
    //     0x889ae0: ldur            w2, [x0, #0xf]
    // 0x889ae4: DecompressPointer r2
    //     0x889ae4: add             x2, x2, HEAP, lsl #32
    // 0x889ae8: cmp             w2, w1
    // 0x889aec: b.ne            #0x889af8
    // 0x889af0: r3 = Null
    //     0x889af0: mov             x3, NULL
    // 0x889af4: b               #0x889afc
    // 0x889af8: mov             x3, x1
    // 0x889afc: mov             x0, x3
    // 0x889b00: ldur            x1, [fp, #-8]
    // 0x889b04: stur            x3, [fp, #-0x10]
    // 0x889b08: r2 = Null
    //     0x889b08: mov             x2, NULL
    // 0x889b0c: r8 = Parser<Y0>
    //     0x889b0c: add             x8, PP, #0x26, lsl #12  ; [pp+0x26c50] Type: Parser<Y0>
    //     0x889b10: ldr             x8, [x8, #0xc50]
    // 0x889b14: LoadField: r9 = r8->field_7
    //     0x889b14: ldur            x9, [x8, #7]
    // 0x889b18: r3 = Null
    //     0x889b18: add             x3, PP, #0x26, lsl #12  ; [pp+0x26c58] Null
    //     0x889b1c: ldr             x3, [x3, #0xc58]
    // 0x889b20: blr             x9
    // 0x889b24: ldur            x0, [fp, #-0x10]
    // 0x889b28: LeaveFrame
    //     0x889b28: mov             SP, fp
    //     0x889b2c: ldp             fp, lr, [SP], #0x10
    // 0x889b30: ret
    //     0x889b30: ret             
    // 0x889b34: ldr             x0, [fp, #0x10]
    // 0x889b38: ldur            x2, [fp, #-0x18]
    // 0x889b3c: ldur            x1, [fp, #-0x20]
    // 0x889b40: r0 = iterator()
    //     0x889b40: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x889b44: mov             x2, x0
    // 0x889b48: ldr             x0, [fp, #0x10]
    // 0x889b4c: stur            x2, [fp, #-0x28]
    // 0x889b50: LoadField: r3 = r0->field_7
    //     0x889b50: ldur            w3, [x0, #7]
    // 0x889b54: DecompressPointer r3
    //     0x889b54: add             x3, x3, HEAP, lsl #32
    // 0x889b58: stur            x3, [fp, #-0x10]
    // 0x889b5c: LoadField: r4 = r2->field_7
    //     0x889b5c: ldur            w4, [x2, #7]
    // 0x889b60: DecompressPointer r4
    //     0x889b60: add             x4, x4, HEAP, lsl #32
    // 0x889b64: stur            x4, [fp, #-8]
    // 0x889b68: CheckStackOverflow
    //     0x889b68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x889b6c: cmp             SP, x16
    //     0x889b70: b.ls            #0x889cd0
    // 0x889b74: mov             x1, x2
    // 0x889b78: r0 = moveNext()
    //     0x889b78: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x889b7c: tbnz            w0, #4, #0x889c64
    // 0x889b80: ldur            x3, [fp, #-0x28]
    // 0x889b84: LoadField: r4 = r3->field_33
    //     0x889b84: ldur            w4, [x3, #0x33]
    // 0x889b88: DecompressPointer r4
    //     0x889b88: add             x4, x4, HEAP, lsl #32
    // 0x889b8c: stur            x4, [fp, #-0x30]
    // 0x889b90: cmp             w4, NULL
    // 0x889b94: b.ne            #0x889bc8
    // 0x889b98: mov             x0, x4
    // 0x889b9c: ldur            x2, [fp, #-8]
    // 0x889ba0: r1 = Null
    //     0x889ba0: mov             x1, NULL
    // 0x889ba4: cmp             w2, NULL
    // 0x889ba8: b.eq            #0x889bc8
    // 0x889bac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x889bac: ldur            w4, [x2, #0x17]
    // 0x889bb0: DecompressPointer r4
    //     0x889bb0: add             x4, x4, HEAP, lsl #32
    // 0x889bb4: r8 = X0
    //     0x889bb4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x889bb8: LoadField: r9 = r4->field_7
    //     0x889bb8: ldur            x9, [x4, #7]
    // 0x889bbc: r3 = Null
    //     0x889bbc: add             x3, PP, #0x26, lsl #12  ; [pp+0x26c68] Null
    //     0x889bc0: ldr             x3, [x3, #0xc68]
    // 0x889bc4: blr             x9
    // 0x889bc8: ldur            x0, [fp, #-0x30]
    // 0x889bcc: ldur            x2, [fp, #-0x10]
    // 0x889bd0: r1 = Null
    //     0x889bd0: mov             x1, NULL
    // 0x889bd4: cmp             w2, NULL
    // 0x889bd8: b.eq            #0x889bf8
    // 0x889bdc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x889bdc: ldur            w4, [x2, #0x17]
    // 0x889be0: DecompressPointer r4
    //     0x889be0: add             x4, x4, HEAP, lsl #32
    // 0x889be4: r8 = X0
    //     0x889be4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x889be8: LoadField: r9 = r4->field_7
    //     0x889be8: ldur            x9, [x4, #7]
    // 0x889bec: r3 = Null
    //     0x889bec: add             x3, PP, #0x26, lsl #12  ; [pp+0x26c78] Null
    //     0x889bf0: ldr             x3, [x3, #0xc78]
    // 0x889bf4: blr             x9
    // 0x889bf8: ldur            x0, [fp, #-0x18]
    // 0x889bfc: ldur            x2, [fp, #-0x10]
    // 0x889c00: r1 = Null
    //     0x889c00: mov             x1, NULL
    // 0x889c04: cmp             w2, NULL
    // 0x889c08: b.eq            #0x889c28
    // 0x889c0c: LoadField: r4 = r2->field_1b
    //     0x889c0c: ldur            w4, [x2, #0x1b]
    // 0x889c10: DecompressPointer r4
    //     0x889c10: add             x4, x4, HEAP, lsl #32
    // 0x889c14: r8 = X1
    //     0x889c14: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x889c18: LoadField: r9 = r4->field_7
    //     0x889c18: ldur            x9, [x4, #7]
    // 0x889c1c: r3 = Null
    //     0x889c1c: add             x3, PP, #0x26, lsl #12  ; [pp+0x26c88] Null
    //     0x889c20: ldr             x3, [x3, #0xc88]
    // 0x889c24: blr             x9
    // 0x889c28: ldur            x16, [fp, #-0x30]
    // 0x889c2c: str             x16, [SP]
    // 0x889c30: r0 = hashCode()
    //     0x889c30: bl              #0xbf1c8c  ; [package:petitparser/src/definition/internal/reference.dart] ReferenceParser::hashCode
    // 0x889c34: r5 = LoadInt32Instr(r0)
    //     0x889c34: sbfx            x5, x0, #1, #0x1f
    //     0x889c38: tbz             w0, #0, #0x889c40
    //     0x889c3c: ldur            x5, [x0, #7]
    // 0x889c40: ldr             x1, [fp, #0x10]
    // 0x889c44: ldur            x2, [fp, #-0x30]
    // 0x889c48: ldur            x3, [fp, #-0x18]
    // 0x889c4c: r0 = _set()
    //     0x889c4c: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x889c50: ldr             x0, [fp, #0x10]
    // 0x889c54: ldur            x2, [fp, #-0x28]
    // 0x889c58: ldur            x4, [fp, #-8]
    // 0x889c5c: ldur            x3, [fp, #-0x10]
    // 0x889c60: b               #0x889b68
    // 0x889c64: ldur            x0, [fp, #-0x18]
    // 0x889c68: LeaveFrame
    //     0x889c68: mov             SP, fp
    //     0x889c6c: ldp             fp, lr, [SP], #0x10
    // 0x889c70: ret
    //     0x889c70: ret             
    // 0x889c74: ldur            x0, [fp, #-0x20]
    // 0x889c78: r1 = Null
    //     0x889c78: mov             x1, NULL
    // 0x889c7c: r2 = 4
    //     0x889c7c: movz            x2, #0x4
    // 0x889c80: r0 = AllocateArray()
    //     0x889c80: bl              #0xec22fc  ; AllocateArrayStub
    // 0x889c84: r16 = "Recursive references detected: "
    //     0x889c84: add             x16, PP, #0x26, lsl #12  ; [pp+0x26c98] "Recursive references detected: "
    //     0x889c88: ldr             x16, [x16, #0xc98]
    // 0x889c8c: StoreField: r0->field_f = r16
    //     0x889c8c: stur            w16, [x0, #0xf]
    // 0x889c90: ldur            x1, [fp, #-0x20]
    // 0x889c94: StoreField: r0->field_13 = r1
    //     0x889c94: stur            w1, [x0, #0x13]
    // 0x889c98: str             x0, [SP]
    // 0x889c9c: r0 = _interpolate()
    //     0x889c9c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x889ca0: stur            x0, [fp, #-8]
    // 0x889ca4: r0 = StateError()
    //     0x889ca4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x889ca8: mov             x1, x0
    // 0x889cac: ldur            x0, [fp, #-8]
    // 0x889cb0: StoreField: r1->field_b = r0
    //     0x889cb0: stur            w0, [x1, #0xb]
    // 0x889cb4: mov             x0, x1
    // 0x889cb8: r0 = Throw()
    //     0x889cb8: bl              #0xec04b8  ; ThrowStub
    // 0x889cbc: brk             #0
    // 0x889cc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889cc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889cc4: b               #0x8898e0
    // 0x889cc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889cc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889ccc: b               #0x889980
    // 0x889cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889cd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889cd4: b               #0x889b74
  }
}
