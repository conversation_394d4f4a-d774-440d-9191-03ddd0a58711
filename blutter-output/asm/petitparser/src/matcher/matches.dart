// lib: , url: package:petitparser/src/matcher/matches.dart

// class id: 1050879, size: 0x8
class :: {

  static _ MatchesParserExtension.allMatches(/* No info */) {
    // ** addr: 0xc3ba28, size: 0x58
    // 0xc3ba28: EnterFrame
    //     0xc3ba28: stp             fp, lr, [SP, #-0x10]!
    //     0xc3ba2c: mov             fp, SP
    // 0xc3ba30: LoadField: r0 = r4->field_f
    //     0xc3ba30: ldur            w0, [x4, #0xf]
    // 0xc3ba34: cbnz            w0, #0xc3ba40
    // 0xc3ba38: r1 = Null
    //     0xc3ba38: mov             x1, NULL
    // 0xc3ba3c: b               #0xc3ba4c
    // 0xc3ba40: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xc3ba40: ldur            w0, [x4, #0x17]
    // 0xc3ba44: add             x1, fp, w0, sxtw #2
    // 0xc3ba48: ldr             x1, [x1, #0x10]
    // 0xc3ba4c: ldr             x2, [fp, #0x18]
    // 0xc3ba50: ldr             x0, [fp, #0x10]
    // 0xc3ba54: r0 = MatchesIterable()
    //     0xc3ba54: bl              #0xc3ba80  ; AllocateMatchesIterableStub -> MatchesIterable<X0> (size=0x20)
    // 0xc3ba58: ldr             x1, [fp, #0x18]
    // 0xc3ba5c: StoreField: r0->field_b = r1
    //     0xc3ba5c: stur            w1, [x0, #0xb]
    // 0xc3ba60: ldr             x1, [fp, #0x10]
    // 0xc3ba64: StoreField: r0->field_f = r1
    //     0xc3ba64: stur            w1, [x0, #0xf]
    // 0xc3ba68: StoreField: r0->field_13 = rZR
    //     0xc3ba68: stur            xzr, [x0, #0x13]
    // 0xc3ba6c: r1 = false
    //     0xc3ba6c: add             x1, NULL, #0x30  ; false
    // 0xc3ba70: StoreField: r0->field_1b = r1
    //     0xc3ba70: stur            w1, [x0, #0x1b]
    // 0xc3ba74: LeaveFrame
    //     0xc3ba74: mov             SP, fp
    //     0xc3ba78: ldp             fp, lr, [SP], #0x10
    // 0xc3ba7c: ret
    //     0xc3ba7c: ret             
  }
}
