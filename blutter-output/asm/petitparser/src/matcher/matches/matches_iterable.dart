// lib: , url: package:petitparser/src/matcher/matches/matches_iterable.dart

// class id: 1050880, size: 0x8
class :: {
}

// class id: 7210, size: 0x20, field offset: 0xc
//   const constructor, 
class MatchesIterable<X0> extends Iterable<X0> {

  get _ iterator(/* No info */) {
    // ** addr: 0x888d7c, size: 0x64
    // 0x888d7c: EnterFrame
    //     0x888d7c: stp             fp, lr, [SP, #-0x10]!
    //     0x888d80: mov             fp, SP
    // 0x888d84: AllocStack(0x10)
    //     0x888d84: sub             SP, SP, #0x10
    // 0x888d88: LoadField: r0 = r1->field_b
    //     0x888d88: ldur            w0, [x1, #0xb]
    // 0x888d8c: DecompressPointer r0
    //     0x888d8c: add             x0, x0, HEAP, lsl #32
    // 0x888d90: stur            x0, [fp, #-0x10]
    // 0x888d94: LoadField: r2 = r1->field_f
    //     0x888d94: ldur            w2, [x1, #0xf]
    // 0x888d98: DecompressPointer r2
    //     0x888d98: add             x2, x2, HEAP, lsl #32
    // 0x888d9c: stur            x2, [fp, #-8]
    // 0x888da0: LoadField: r3 = r1->field_7
    //     0x888da0: ldur            w3, [x1, #7]
    // 0x888da4: DecompressPointer r3
    //     0x888da4: add             x3, x3, HEAP, lsl #32
    // 0x888da8: mov             x1, x3
    // 0x888dac: r0 = MatchesIterator()
    //     0x888dac: bl              #0x888de0  ; AllocateMatchesIteratorStub -> MatchesIterator<X0> (size=0x24)
    // 0x888db0: r1 = Sentinel
    //     0x888db0: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x888db4: StoreField: r0->field_1f = r1
    //     0x888db4: stur            w1, [x0, #0x1f]
    // 0x888db8: ldur            x1, [fp, #-0x10]
    // 0x888dbc: StoreField: r0->field_b = r1
    //     0x888dbc: stur            w1, [x0, #0xb]
    // 0x888dc0: ldur            x1, [fp, #-8]
    // 0x888dc4: StoreField: r0->field_f = r1
    //     0x888dc4: stur            w1, [x0, #0xf]
    // 0x888dc8: ArrayStore: r0[0] = rZR  ; List_8
    //     0x888dc8: stur            xzr, [x0, #0x17]
    // 0x888dcc: r1 = false
    //     0x888dcc: add             x1, NULL, #0x30  ; false
    // 0x888dd0: StoreField: r0->field_13 = r1
    //     0x888dd0: stur            w1, [x0, #0x13]
    // 0x888dd4: LeaveFrame
    //     0x888dd4: mov             SP, fp
    //     0x888dd8: ldp             fp, lr, [SP], #0x10
    // 0x888ddc: ret
    //     0x888ddc: ret             
  }
}
