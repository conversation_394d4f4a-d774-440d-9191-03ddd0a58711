// lib: , url: package:petitparser/src/matcher/matches/matches_iterator.dart

// class id: 1050881, size: 0x8
class :: {
}

// class id: 723, size: 0x24, field offset: 0x8
class MatchesIterator<X0> extends Object
    implements Iterator<X0> {

  late X0 current; // offset: 0x20

  get _ current(/* No info */) {
    // ** addr: 0x6d5a54, size: 0x2c
    // 0x6d5a54: LoadField: r0 = r1->field_1f
    //     0x6d5a54: ldur            w0, [x1, #0x1f]
    // 0x6d5a58: DecompressPointer r0
    //     0x6d5a58: add             x0, x0, HEAP, lsl #32
    // 0x6d5a5c: r16 = Sentinel
    //     0x6d5a5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6d5a60: cmp             w0, w16
    // 0x6d5a64: b.eq            #0x6d5a6c
    // 0x6d5a68: ret
    //     0x6d5a68: ret             
    // 0x6d5a6c: EnterFrame
    //     0x6d5a6c: stp             fp, lr, [SP, #-0x10]!
    //     0x6d5a70: mov             fp, SP
    // 0x6d5a74: r9 = current
    //     0x6d5a74: add             x9, PP, #0x31, lsl #12  ; [pp+0x31478] Field <MatchesIterator.current>: late (offset: 0x20)
    //     0x6d5a78: ldr             x9, [x9, #0x478]
    // 0x6d5a7c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x6d5a7c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ moveNext(/* No info */) {
    // ** addr: 0x747224, size: 0x198
    // 0x747224: EnterFrame
    //     0x747224: stp             fp, lr, [SP, #-0x10]!
    //     0x747228: mov             fp, SP
    // 0x74722c: AllocStack(0x28)
    //     0x74722c: sub             SP, SP, #0x28
    // 0x747230: SetupParameters(MatchesIterator<X0> this /* r1 => r4, fp-0x20 */)
    //     0x747230: mov             x4, x1
    //     0x747234: stur            x1, [fp, #-0x20]
    // 0x747238: CheckStackOverflow
    //     0x747238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74723c: cmp             SP, x16
    //     0x747240: b.ls            #0x7473ac
    // 0x747244: LoadField: r5 = r4->field_f
    //     0x747244: ldur            w5, [x4, #0xf]
    // 0x747248: DecompressPointer r5
    //     0x747248: add             x5, x5, HEAP, lsl #32
    // 0x74724c: stur            x5, [fp, #-0x18]
    // 0x747250: LoadField: r0 = r5->field_7
    //     0x747250: ldur            w0, [x5, #7]
    // 0x747254: r6 = LoadInt32Instr(r0)
    //     0x747254: sbfx            x6, x0, #1, #0x1f
    // 0x747258: stur            x6, [fp, #-0x10]
    // 0x74725c: LoadField: r7 = r4->field_b
    //     0x74725c: ldur            w7, [x4, #0xb]
    // 0x747260: DecompressPointer r7
    //     0x747260: add             x7, x7, HEAP, lsl #32
    // 0x747264: stur            x7, [fp, #-8]
    // 0x747268: CheckStackOverflow
    //     0x747268: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74726c: cmp             SP, x16
    //     0x747270: b.ls            #0x7473b4
    // 0x747274: ArrayLoad: r3 = r4[0]  ; List_8
    //     0x747274: ldur            x3, [x4, #0x17]
    // 0x747278: cmp             x3, x6
    // 0x74727c: b.gt            #0x747380
    // 0x747280: LoadField: r1 = r7->field_b
    //     0x747280: ldur            w1, [x7, #0xb]
    // 0x747284: DecompressPointer r1
    //     0x747284: add             x1, x1, HEAP, lsl #32
    // 0x747288: r0 = LoadClassIdInstr(r1)
    //     0x747288: ldur            x0, [x1, #-1]
    //     0x74728c: ubfx            x0, x0, #0xc, #0x14
    // 0x747290: mov             x2, x5
    // 0x747294: r0 = GDT[cid_x0 + -0xfce]()
    //     0x747294: sub             lr, x0, #0xfce
    //     0x747298: ldr             lr, [x21, lr, lsl #3]
    //     0x74729c: blr             lr
    // 0x7472a0: r1 = LoadInt32Instr(r0)
    //     0x7472a0: sbfx            x1, x0, #1, #0x1f
    //     0x7472a4: tbz             w0, #0, #0x7472ac
    //     0x7472a8: ldur            x1, [x0, #7]
    // 0x7472ac: stur            x1, [fp, #-0x28]
    // 0x7472b0: tbz             x1, #0x3f, #0x7472d8
    // 0x7472b4: ldur            x0, [fp, #-0x20]
    // 0x7472b8: ArrayLoad: r1 = r0[0]  ; List_8
    //     0x7472b8: ldur            x1, [x0, #0x17]
    // 0x7472bc: add             x2, x1, #1
    // 0x7472c0: ArrayStore: r0[0] = r2  ; List_8
    //     0x7472c0: stur            x2, [x0, #0x17]
    // 0x7472c4: mov             x4, x0
    // 0x7472c8: ldur            x5, [fp, #-0x18]
    // 0x7472cc: ldur            x7, [fp, #-8]
    // 0x7472d0: ldur            x6, [fp, #-0x10]
    // 0x7472d4: b               #0x747268
    // 0x7472d8: ldur            x0, [fp, #-0x20]
    // 0x7472dc: ldur            x2, [fp, #-0x18]
    // 0x7472e0: ArrayLoad: r3 = r0[0]  ; List_8
    //     0x7472e0: ldur            x3, [x0, #0x17]
    // 0x7472e4: stur            x3, [fp, #-0x10]
    // 0x7472e8: r0 = Context()
    //     0x7472e8: bl              #0x747588  ; AllocateContextStub -> Context (size=0x14)
    // 0x7472ec: mov             x1, x0
    // 0x7472f0: ldur            x0, [fp, #-0x18]
    // 0x7472f4: StoreField: r1->field_7 = r0
    //     0x7472f4: stur            w0, [x1, #7]
    // 0x7472f8: ldur            x0, [fp, #-0x10]
    // 0x7472fc: StoreField: r1->field_b = r0
    //     0x7472fc: stur            x0, [x1, #0xb]
    // 0x747300: mov             x2, x1
    // 0x747304: ldur            x1, [fp, #-8]
    // 0x747308: r0 = parseOn()
    //     0x747308: bl              #0xeb11ec  ; [package:petitparser/src/parser/action/token.dart] TokenParser::parseOn
    // 0x74730c: stur            x0, [fp, #-8]
    // 0x747310: r1 = LoadClassIdInstr(r0)
    //     0x747310: ldur            x1, [x0, #-1]
    //     0x747314: ubfx            x1, x1, #0xc, #0x14
    // 0x747318: cmp             x1, #0x2f3
    // 0x74731c: b.eq            #0x747390
    // 0x747320: ldur            x2, [fp, #-0x20]
    // 0x747324: ldur            x1, [fp, #-0x28]
    // 0x747328: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x747328: ldur            w3, [x0, #0x17]
    // 0x74732c: DecompressPointer r3
    //     0x74732c: add             x3, x3, HEAP, lsl #32
    // 0x747330: mov             x0, x3
    // 0x747334: StoreField: r2->field_1f = r0
    //     0x747334: stur            w0, [x2, #0x1f]
    //     0x747338: tbz             w0, #0, #0x747354
    //     0x74733c: ldurb           w16, [x2, #-1]
    //     0x747340: ldurb           w17, [x0, #-1]
    //     0x747344: and             x16, x17, x16, lsr #2
    //     0x747348: tst             x16, HEAP, lsr #32
    //     0x74734c: b.eq            #0x747354
    //     0x747350: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x747354: ArrayLoad: r0 = r2[0]  ; List_8
    //     0x747354: ldur            x0, [x2, #0x17]
    // 0x747358: cmp             x0, x1
    // 0x74735c: b.ne            #0x74736c
    // 0x747360: add             x1, x0, #1
    // 0x747364: ArrayStore: r2[0] = r1  ; List_8
    //     0x747364: stur            x1, [x2, #0x17]
    // 0x747368: b               #0x747370
    // 0x74736c: ArrayStore: r2[0] = r1  ; List_8
    //     0x74736c: stur            x1, [x2, #0x17]
    // 0x747370: r0 = true
    //     0x747370: add             x0, NULL, #0x20  ; true
    // 0x747374: LeaveFrame
    //     0x747374: mov             SP, fp
    //     0x747378: ldp             fp, lr, [SP], #0x10
    // 0x74737c: ret
    //     0x74737c: ret             
    // 0x747380: r0 = false
    //     0x747380: add             x0, NULL, #0x30  ; false
    // 0x747384: LeaveFrame
    //     0x747384: mov             SP, fp
    //     0x747388: ldp             fp, lr, [SP], #0x10
    // 0x74738c: ret
    //     0x74738c: ret             
    // 0x747390: r0 = ParserException()
    //     0x747390: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0x747394: mov             x1, x0
    // 0x747398: ldur            x0, [fp, #-8]
    // 0x74739c: StoreField: r1->field_7 = r0
    //     0x74739c: stur            w0, [x1, #7]
    // 0x7473a0: mov             x0, x1
    // 0x7473a4: r0 = Throw()
    //     0x7473a4: bl              #0xec04b8  ; ThrowStub
    // 0x7473a8: brk             #0
    // 0x7473ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7473ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7473b0: b               #0x747244
    // 0x7473b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7473b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7473b8: b               #0x747274
  }
}
