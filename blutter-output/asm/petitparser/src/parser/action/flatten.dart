// lib: , url: package:petitparser/src/parser/action/flatten.dart

// class id: 1050882, size: 0x8
class :: {

  static Parser<String> FlattenParserExtension.flatten<Y0>(Parser<Y0>, String?) {
    // ** addr: 0x88b93c, size: 0x68
    // 0x88b93c: EnterFrame
    //     0x88b93c: stp             fp, lr, [SP, #-0x10]!
    //     0x88b940: mov             fp, SP
    // 0x88b944: LoadField: r0 = r4->field_f
    //     0x88b944: ldur            w0, [x4, #0xf]
    // 0x88b948: cbnz            w0, #0x88b954
    // 0x88b94c: r1 = Null
    //     0x88b94c: mov             x1, NULL
    // 0x88b950: b               #0x88b960
    // 0x88b954: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88b954: ldur            w0, [x4, #0x17]
    // 0x88b958: add             x1, fp, w0, sxtw #2
    // 0x88b95c: ldr             x1, [x1, #0x10]
    // 0x88b960: ldr             x4, [fp, #0x18]
    // 0x88b964: ldr             x0, [fp, #0x10]
    // 0x88b968: r2 = Null
    //     0x88b968: mov             x2, NULL
    // 0x88b96c: r3 = <String, Y0, String, Y0>
    //     0x88b96c: add             x3, PP, #0x26, lsl #12  ; [pp+0x267d8] TypeArguments: <String, Y0, String, Y0>
    //     0x88b970: ldr             x3, [x3, #0x7d8]
    // 0x88b974: r30 = InstantiateTypeArgumentsStub
    //     0x88b974: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88b978: LoadField: r30 = r30->field_7
    //     0x88b978: ldur            lr, [lr, #7]
    // 0x88b97c: blr             lr
    // 0x88b980: mov             x1, x0
    // 0x88b984: r0 = FlattenParser()
    //     0x88b984: bl              #0x88b9a4  ; AllocateFlattenParserStub -> FlattenParser<C3X0> (size=0x14)
    // 0x88b988: ldr             x1, [fp, #0x10]
    // 0x88b98c: StoreField: r0->field_f = r1
    //     0x88b98c: stur            w1, [x0, #0xf]
    // 0x88b990: ldr             x1, [fp, #0x18]
    // 0x88b994: StoreField: r0->field_b = r1
    //     0x88b994: stur            w1, [x0, #0xb]
    // 0x88b998: LeaveFrame
    //     0x88b998: mov             SP, fp
    //     0x88b99c: ldp             fp, lr, [SP], #0x10
    // 0x88b9a0: ret
    //     0x88b9a0: ret             
  }
}

// class id: 750, size: 0x14, field offset: 0x10
class FlattenParser<C3X0> extends DelegateParser<C3X0, dynamic> {

  _ parseOn(/* No info */) {
    // ** addr: 0xeb0ef8, size: 0x10c
    // 0xeb0ef8: EnterFrame
    //     0xeb0ef8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0efc: mov             fp, SP
    // 0xeb0f00: AllocStack(0x50)
    //     0xeb0f00: sub             SP, SP, #0x50
    // 0xeb0f04: SetupParameters(FlattenParser<C3X0> this /* r1 => r5, fp-0x18 */, dynamic _ /* r2 => r4, fp-0x20 */)
    //     0xeb0f04: mov             x5, x1
    //     0xeb0f08: mov             x4, x2
    //     0xeb0f0c: stur            x1, [fp, #-0x18]
    //     0xeb0f10: stur            x2, [fp, #-0x20]
    // 0xeb0f14: CheckStackOverflow
    //     0xeb0f14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0f18: cmp             SP, x16
    //     0xeb0f1c: b.ls            #0xeb0ffc
    // 0xeb0f20: LoadField: r1 = r5->field_b
    //     0xeb0f20: ldur            w1, [x5, #0xb]
    // 0xeb0f24: DecompressPointer r1
    //     0xeb0f24: add             x1, x1, HEAP, lsl #32
    // 0xeb0f28: LoadField: r6 = r4->field_7
    //     0xeb0f28: ldur            w6, [x4, #7]
    // 0xeb0f2c: DecompressPointer r6
    //     0xeb0f2c: add             x6, x6, HEAP, lsl #32
    // 0xeb0f30: stur            x6, [fp, #-0x10]
    // 0xeb0f34: LoadField: r7 = r4->field_b
    //     0xeb0f34: ldur            x7, [x4, #0xb]
    // 0xeb0f38: stur            x7, [fp, #-8]
    // 0xeb0f3c: r0 = LoadClassIdInstr(r1)
    //     0xeb0f3c: ldur            x0, [x1, #-1]
    //     0xeb0f40: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0f44: mov             x2, x6
    // 0xeb0f48: mov             x3, x7
    // 0xeb0f4c: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb0f4c: sub             lr, x0, #0xfce
    //     0xeb0f50: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0f54: blr             lr
    // 0xeb0f58: stur            x0, [fp, #-0x30]
    // 0xeb0f5c: r1 = LoadInt32Instr(r0)
    //     0xeb0f5c: sbfx            x1, x0, #1, #0x1f
    //     0xeb0f60: tbz             w0, #0, #0xeb0f68
    //     0xeb0f64: ldur            x1, [x0, #7]
    // 0xeb0f68: tbz             x1, #0x3f, #0xeb0fb8
    // 0xeb0f6c: ldur            x0, [fp, #-0x18]
    // 0xeb0f70: ldur            x2, [fp, #-0x10]
    // 0xeb0f74: ldur            x3, [fp, #-8]
    // 0xeb0f78: LoadField: r4 = r0->field_f
    //     0xeb0f78: ldur            w4, [x0, #0xf]
    // 0xeb0f7c: DecompressPointer r4
    //     0xeb0f7c: add             x4, x4, HEAP, lsl #32
    // 0xeb0f80: stur            x4, [fp, #-0x28]
    // 0xeb0f84: r1 = <Never>
    //     0xeb0f84: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xeb0f88: r0 = Failure()
    //     0xeb0f88: bl              #0x75149c  ; AllocateFailureStub -> Failure (size=0x1c)
    // 0xeb0f8c: mov             x1, x0
    // 0xeb0f90: ldur            x0, [fp, #-0x28]
    // 0xeb0f94: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb0f94: stur            w0, [x1, #0x17]
    // 0xeb0f98: ldur            x2, [fp, #-0x10]
    // 0xeb0f9c: StoreField: r1->field_7 = r2
    //     0xeb0f9c: stur            w2, [x1, #7]
    // 0xeb0fa0: ldur            x3, [fp, #-8]
    // 0xeb0fa4: StoreField: r1->field_b = r3
    //     0xeb0fa4: stur            x3, [x1, #0xb]
    // 0xeb0fa8: mov             x0, x1
    // 0xeb0fac: LeaveFrame
    //     0xeb0fac: mov             SP, fp
    //     0xeb0fb0: ldp             fp, lr, [SP], #0x10
    // 0xeb0fb4: ret
    //     0xeb0fb4: ret             
    // 0xeb0fb8: ldur            x2, [fp, #-0x10]
    // 0xeb0fbc: ldur            x3, [fp, #-8]
    // 0xeb0fc0: str             x0, [SP]
    // 0xeb0fc4: mov             x1, x2
    // 0xeb0fc8: mov             x2, x3
    // 0xeb0fcc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb0fcc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb0fd0: r0 = substring()
    //     0xeb0fd0: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xeb0fd4: r16 = <String>
    //     0xeb0fd4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xeb0fd8: ldur            lr, [fp, #-0x20]
    // 0xeb0fdc: stp             lr, x16, [SP, #0x10]
    // 0xeb0fe0: ldur            x16, [fp, #-0x30]
    // 0xeb0fe4: stp             x16, x0, [SP]
    // 0xeb0fe8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xeb0fe8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xeb0fec: r0 = success()
    //     0xeb0fec: bl              #0xeb1004  ; [package:petitparser/src/core/context.dart] Context::success
    // 0xeb0ff0: LeaveFrame
    //     0xeb0ff0: mov             SP, fp
    //     0xeb0ff4: ldp             fp, lr, [SP], #0x10
    // 0xeb0ff8: ret
    //     0xeb0ff8: ret             
    // 0xeb0ffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0ffc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb1000: b               #0xeb0f20
  }
}
