// lib: , url: package:petitparser/src/parser/action/map.dart

// class id: 1050883, size: 0x8
class :: {

  static Parser<Y1> MapParserExtension.map<Y0, Y1>(Parser<Y0>, (dynamic, Y0) => Y1) {
    // ** addr: 0x88ab3c, size: 0x84
    // 0x88ab3c: EnterFrame
    //     0x88ab3c: stp             fp, lr, [SP, #-0x10]!
    //     0x88ab40: mov             fp, SP
    // 0x88ab44: LoadField: r0 = r4->field_f
    //     0x88ab44: ldur            w0, [x4, #0xf]
    // 0x88ab48: cbnz            w0, #0x88ab54
    // 0x88ab4c: r1 = Null
    //     0x88ab4c: mov             x1, NULL
    // 0x88ab50: b               #0x88ab60
    // 0x88ab54: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88ab54: ldur            w0, [x4, #0x17]
    // 0x88ab58: add             x1, fp, w0, sxtw #2
    // 0x88ab5c: ldr             x1, [x1, #0x10]
    // 0x88ab60: ldr             x4, [fp, #0x18]
    // 0x88ab64: ldr             x0, [fp, #0x10]
    // 0x88ab68: r2 = Null
    //     0x88ab68: mov             x2, NULL
    // 0x88ab6c: r3 = <Y1, Y0, Y1>
    //     0x88ab6c: add             x3, PP, #0x26, lsl #12  ; [pp+0x26780] TypeArguments: <Y1, Y0, Y1>
    //     0x88ab70: ldr             x3, [x3, #0x780]
    // 0x88ab74: r0 = Null
    //     0x88ab74: mov             x0, NULL
    // 0x88ab78: cmp             x2, x0
    // 0x88ab7c: b.ne            #0x88ab88
    // 0x88ab80: cmp             x1, x0
    // 0x88ab84: b.eq            #0x88ab94
    // 0x88ab88: r30 = InstantiateTypeArgumentsStub
    //     0x88ab88: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88ab8c: LoadField: r30 = r30->field_7
    //     0x88ab8c: ldur            lr, [lr, #7]
    // 0x88ab90: blr             lr
    // 0x88ab94: mov             x1, x0
    // 0x88ab98: r0 = MapParser()
    //     0x88ab98: bl              #0x88abc0  ; AllocateMapParserStub -> MapParser<C1X0, C1X1> (size=0x18)
    // 0x88ab9c: ldr             x1, [fp, #0x10]
    // 0x88aba0: StoreField: r0->field_f = r1
    //     0x88aba0: stur            w1, [x0, #0xf]
    // 0x88aba4: r1 = false
    //     0x88aba4: add             x1, NULL, #0x30  ; false
    // 0x88aba8: StoreField: r0->field_13 = r1
    //     0x88aba8: stur            w1, [x0, #0x13]
    // 0x88abac: ldr             x1, [fp, #0x18]
    // 0x88abb0: StoreField: r0->field_b = r1
    //     0x88abb0: stur            w1, [x0, #0xb]
    // 0x88abb4: LeaveFrame
    //     0x88abb4: mov             SP, fp
    //     0x88abb8: ldp             fp, lr, [SP], #0x10
    // 0x88abbc: ret
    //     0x88abbc: ret             
  }
}

// class id: 749, size: 0x18, field offset: 0x10
class MapParser<C1X0, C1X1> extends DelegateParser<C1X0, C1X1> {

  _ parseOn(/* No info */) {
    // ** addr: 0xeb10d0, size: 0x11c
    // 0xeb10d0: EnterFrame
    //     0xeb10d0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb10d4: mov             fp, SP
    // 0xeb10d8: AllocStack(0x38)
    //     0xeb10d8: sub             SP, SP, #0x38
    // 0xeb10dc: SetupParameters(MapParser<C1X0, C1X1> this /* r1 => r3, fp-0x8 */)
    //     0xeb10dc: mov             x3, x1
    //     0xeb10e0: stur            x1, [fp, #-8]
    // 0xeb10e4: CheckStackOverflow
    //     0xeb10e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb10e8: cmp             SP, x16
    //     0xeb10ec: b.ls            #0xeb11e4
    // 0xeb10f0: LoadField: r1 = r3->field_b
    //     0xeb10f0: ldur            w1, [x3, #0xb]
    // 0xeb10f4: DecompressPointer r1
    //     0xeb10f4: add             x1, x1, HEAP, lsl #32
    // 0xeb10f8: r0 = LoadClassIdInstr(r1)
    //     0xeb10f8: ldur            x0, [x1, #-1]
    //     0xeb10fc: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1100: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1100: sub             lr, x0, #1, lsl #12
    //     0xeb1104: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1108: blr             lr
    // 0xeb110c: mov             x1, x0
    // 0xeb1110: stur            x1, [fp, #-0x18]
    // 0xeb1114: r0 = LoadClassIdInstr(r1)
    //     0xeb1114: ldur            x0, [x1, #-1]
    //     0xeb1118: ubfx            x0, x0, #0xc, #0x14
    // 0xeb111c: cmp             x0, #0x2f3
    // 0xeb1120: b.ne            #0xeb1134
    // 0xeb1124: mov             x0, x1
    // 0xeb1128: LeaveFrame
    //     0xeb1128: mov             SP, fp
    //     0xeb112c: ldp             fp, lr, [SP], #0x10
    // 0xeb1130: ret
    //     0xeb1130: ret             
    // 0xeb1134: ldur            x2, [fp, #-8]
    // 0xeb1138: LoadField: r3 = r2->field_7
    //     0xeb1138: ldur            w3, [x2, #7]
    // 0xeb113c: DecompressPointer r3
    //     0xeb113c: add             x3, x3, HEAP, lsl #32
    // 0xeb1140: stur            x3, [fp, #-0x10]
    // 0xeb1144: cmp             x0, #0x2f3
    // 0xeb1148: b.eq            #0xeb11c4
    // 0xeb114c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xeb114c: ldur            w0, [x1, #0x17]
    // 0xeb1150: DecompressPointer r0
    //     0xeb1150: add             x0, x0, HEAP, lsl #32
    // 0xeb1154: LoadField: r4 = r2->field_f
    //     0xeb1154: ldur            w4, [x2, #0xf]
    // 0xeb1158: DecompressPointer r4
    //     0xeb1158: add             x4, x4, HEAP, lsl #32
    // 0xeb115c: stp             x0, x4, [SP]
    // 0xeb1160: mov             x0, x4
    // 0xeb1164: ClosureCall
    //     0xeb1164: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeb1168: ldur            x2, [x0, #0x1f]
    //     0xeb116c: blr             x2
    // 0xeb1170: mov             x2, x0
    // 0xeb1174: ldur            x0, [fp, #-0x18]
    // 0xeb1178: stur            x2, [fp, #-0x28]
    // 0xeb117c: LoadField: r3 = r0->field_7
    //     0xeb117c: ldur            w3, [x0, #7]
    // 0xeb1180: DecompressPointer r3
    //     0xeb1180: add             x3, x3, HEAP, lsl #32
    // 0xeb1184: stur            x3, [fp, #-8]
    // 0xeb1188: LoadField: r4 = r0->field_b
    //     0xeb1188: ldur            x4, [x0, #0xb]
    // 0xeb118c: ldur            x1, [fp, #-0x10]
    // 0xeb1190: stur            x4, [fp, #-0x20]
    // 0xeb1194: r0 = Success()
    //     0xeb1194: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb1198: mov             x1, x0
    // 0xeb119c: ldur            x0, [fp, #-0x28]
    // 0xeb11a0: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb11a0: stur            w0, [x1, #0x17]
    // 0xeb11a4: ldur            x0, [fp, #-8]
    // 0xeb11a8: StoreField: r1->field_7 = r0
    //     0xeb11a8: stur            w0, [x1, #7]
    // 0xeb11ac: ldur            x0, [fp, #-0x20]
    // 0xeb11b0: StoreField: r1->field_b = r0
    //     0xeb11b0: stur            x0, [x1, #0xb]
    // 0xeb11b4: mov             x0, x1
    // 0xeb11b8: LeaveFrame
    //     0xeb11b8: mov             SP, fp
    //     0xeb11bc: ldp             fp, lr, [SP], #0x10
    // 0xeb11c0: ret
    //     0xeb11c0: ret             
    // 0xeb11c4: mov             x0, x1
    // 0xeb11c8: r0 = ParserException()
    //     0xeb11c8: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb11cc: mov             x1, x0
    // 0xeb11d0: ldur            x0, [fp, #-0x18]
    // 0xeb11d4: StoreField: r1->field_7 = r0
    //     0xeb11d4: stur            w0, [x1, #7]
    // 0xeb11d8: mov             x0, x1
    // 0xeb11dc: r0 = Throw()
    //     0xeb11dc: bl              #0xec04b8  ; ThrowStub
    // 0xeb11e0: brk             #0
    // 0xeb11e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb11e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb11e8: b               #0xeb10f0
  }
}
