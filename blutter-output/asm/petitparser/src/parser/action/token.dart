// lib: , url: package:petitparser/src/parser/action/token.dart

// class id: 1050884, size: 0x8
class :: {

  static Parser<Token<Y0>> TokenParserExtension.token<Y0>(Parser<Y0>) {
    // ** addr: 0xc3ba8c, size: 0x5c
    // 0xc3ba8c: EnterFrame
    //     0xc3ba8c: stp             fp, lr, [SP, #-0x10]!
    //     0xc3ba90: mov             fp, SP
    // 0xc3ba94: LoadField: r0 = r4->field_f
    //     0xc3ba94: ldur            w0, [x4, #0xf]
    // 0xc3ba98: cbnz            w0, #0xc3baa4
    // 0xc3ba9c: r1 = Null
    //     0xc3ba9c: mov             x1, NULL
    // 0xc3baa0: b               #0xc3bab0
    // 0xc3baa4: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xc3baa4: ldur            w0, [x4, #0x17]
    // 0xc3baa8: add             x1, fp, w0, sxtw #2
    // 0xc3baac: ldr             x1, [x1, #0x10]
    // 0xc3bab0: ldr             x0, [fp, #0x10]
    // 0xc3bab4: r2 = Null
    //     0xc3bab4: mov             x2, NULL
    // 0xc3bab8: r3 = <Token<Y0>, Y0, Token<Y0>, Y0>
    //     0xc3bab8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31480] TypeArguments: <Token<Y0>, Y0, Token<Y0>, Y0>
    //     0xc3babc: ldr             x3, [x3, #0x480]
    // 0xc3bac0: r30 = InstantiateTypeArgumentsStub
    //     0xc3bac0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xc3bac4: LoadField: r30 = r30->field_7
    //     0xc3bac4: ldur            lr, [lr, #7]
    // 0xc3bac8: blr             lr
    // 0xc3bacc: mov             x1, x0
    // 0xc3bad0: r0 = TokenParser()
    //     0xc3bad0: bl              #0xc3bae8  ; AllocateTokenParserStub -> TokenParser<C3X0> (size=0x10)
    // 0xc3bad4: ldr             x1, [fp, #0x10]
    // 0xc3bad8: StoreField: r0->field_b = r1
    //     0xc3bad8: stur            w1, [x0, #0xb]
    // 0xc3badc: LeaveFrame
    //     0xc3badc: mov             SP, fp
    //     0xc3bae0: ldp             fp, lr, [SP], #0x10
    // 0xc3bae4: ret
    //     0xc3bae4: ret             
  }
}

// class id: 748, size: 0x10, field offset: 0x10
class TokenParser<C3X0> extends DelegateParser<C3X0, dynamic> {

  _ fastParseOn(/* No info */) {
    // ** addr: 0xeaf8dc, size: 0x50
    // 0xeaf8dc: EnterFrame
    //     0xeaf8dc: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf8e0: mov             fp, SP
    // 0xeaf8e4: CheckStackOverflow
    //     0xeaf8e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf8e8: cmp             SP, x16
    //     0xeaf8ec: b.ls            #0xeaf924
    // 0xeaf8f0: LoadField: r0 = r1->field_b
    //     0xeaf8f0: ldur            w0, [x1, #0xb]
    // 0xeaf8f4: DecompressPointer r0
    //     0xeaf8f4: add             x0, x0, HEAP, lsl #32
    // 0xeaf8f8: r1 = LoadClassIdInstr(r0)
    //     0xeaf8f8: ldur            x1, [x0, #-1]
    //     0xeaf8fc: ubfx            x1, x1, #0xc, #0x14
    // 0xeaf900: mov             x16, x0
    // 0xeaf904: mov             x0, x1
    // 0xeaf908: mov             x1, x16
    // 0xeaf90c: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeaf90c: sub             lr, x0, #0xfce
    //     0xeaf910: ldr             lr, [x21, lr, lsl #3]
    //     0xeaf914: blr             lr
    // 0xeaf918: LeaveFrame
    //     0xeaf918: mov             SP, fp
    //     0xeaf91c: ldp             fp, lr, [SP], #0x10
    // 0xeaf920: ret
    //     0xeaf920: ret             
    // 0xeaf924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf928: b               #0xeaf8f0
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb11ec, size: 0x17c
    // 0xeb11ec: EnterFrame
    //     0xeb11ec: stp             fp, lr, [SP, #-0x10]!
    //     0xeb11f0: mov             fp, SP
    // 0xeb11f4: AllocStack(0x38)
    //     0xeb11f4: sub             SP, SP, #0x38
    // 0xeb11f8: SetupParameters(TokenParser<C3X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xeb11f8: mov             x4, x1
    //     0xeb11fc: mov             x3, x2
    //     0xeb1200: stur            x1, [fp, #-8]
    //     0xeb1204: stur            x2, [fp, #-0x10]
    // 0xeb1208: CheckStackOverflow
    //     0xeb1208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb120c: cmp             SP, x16
    //     0xeb1210: b.ls            #0xeb1360
    // 0xeb1214: LoadField: r1 = r4->field_b
    //     0xeb1214: ldur            w1, [x4, #0xb]
    // 0xeb1218: DecompressPointer r1
    //     0xeb1218: add             x1, x1, HEAP, lsl #32
    // 0xeb121c: r0 = LoadClassIdInstr(r1)
    //     0xeb121c: ldur            x0, [x1, #-1]
    //     0xeb1220: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1224: mov             x2, x3
    // 0xeb1228: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1228: sub             lr, x0, #1, lsl #12
    //     0xeb122c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1230: blr             lr
    // 0xeb1234: stur            x0, [fp, #-0x28]
    // 0xeb1238: r4 = LoadClassIdInstr(r0)
    //     0xeb1238: ldur            x4, [x0, #-1]
    //     0xeb123c: ubfx            x4, x4, #0xc, #0x14
    // 0xeb1240: stur            x4, [fp, #-0x20]
    // 0xeb1244: cmp             x4, #0x2f3
    // 0xeb1248: b.ne            #0xeb1258
    // 0xeb124c: LeaveFrame
    //     0xeb124c: mov             SP, fp
    //     0xeb1250: ldp             fp, lr, [SP], #0x10
    // 0xeb1254: ret
    //     0xeb1254: ret             
    // 0xeb1258: ldur            x1, [fp, #-8]
    // 0xeb125c: LoadField: r5 = r1->field_7
    //     0xeb125c: ldur            w5, [x1, #7]
    // 0xeb1260: DecompressPointer r5
    //     0xeb1260: add             x5, x5, HEAP, lsl #32
    // 0xeb1264: mov             x2, x5
    // 0xeb1268: stur            x5, [fp, #-0x18]
    // 0xeb126c: r1 = Null
    //     0xeb126c: mov             x1, NULL
    // 0xeb1270: r3 = <C3X0>
    //     0xeb1270: add             x3, PP, #0x31, lsl #12  ; [pp+0x31230] TypeArguments: <C3X0>
    //     0xeb1274: ldr             x3, [x3, #0x230]
    // 0xeb1278: r0 = Null
    //     0xeb1278: mov             x0, NULL
    // 0xeb127c: cmp             x2, x0
    // 0xeb1280: b.eq            #0xeb1290
    // 0xeb1284: r30 = InstantiateTypeArgumentsStub
    //     0xeb1284: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xeb1288: LoadField: r30 = r30->field_7
    //     0xeb1288: ldur            lr, [lr, #7]
    // 0xeb128c: blr             lr
    // 0xeb1290: mov             x1, x0
    // 0xeb1294: ldur            x0, [fp, #-0x20]
    // 0xeb1298: cmp             x0, #0x2f3
    // 0xeb129c: b.eq            #0xeb1340
    // 0xeb12a0: ldur            x2, [fp, #-0x10]
    // 0xeb12a4: ldur            x0, [fp, #-0x28]
    // 0xeb12a8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xeb12a8: ldur            w3, [x0, #0x17]
    // 0xeb12ac: DecompressPointer r3
    //     0xeb12ac: add             x3, x3, HEAP, lsl #32
    // 0xeb12b0: stur            x3, [fp, #-0x38]
    // 0xeb12b4: LoadField: r4 = r2->field_7
    //     0xeb12b4: ldur            w4, [x2, #7]
    // 0xeb12b8: DecompressPointer r4
    //     0xeb12b8: add             x4, x4, HEAP, lsl #32
    // 0xeb12bc: stur            x4, [fp, #-8]
    // 0xeb12c0: LoadField: r5 = r2->field_b
    //     0xeb12c0: ldur            x5, [x2, #0xb]
    // 0xeb12c4: stur            x5, [fp, #-0x30]
    // 0xeb12c8: LoadField: r2 = r0->field_b
    //     0xeb12c8: ldur            x2, [x0, #0xb]
    // 0xeb12cc: stur            x2, [fp, #-0x20]
    // 0xeb12d0: r0 = Token()
    //     0xeb12d0: bl              #0xeb1368  ; AllocateTokenStub -> Token<X0> (size=0x24)
    // 0xeb12d4: mov             x2, x0
    // 0xeb12d8: ldur            x0, [fp, #-0x38]
    // 0xeb12dc: stur            x2, [fp, #-0x10]
    // 0xeb12e0: StoreField: r2->field_b = r0
    //     0xeb12e0: stur            w0, [x2, #0xb]
    // 0xeb12e4: ldur            x0, [fp, #-8]
    // 0xeb12e8: StoreField: r2->field_f = r0
    //     0xeb12e8: stur            w0, [x2, #0xf]
    // 0xeb12ec: ldur            x0, [fp, #-0x30]
    // 0xeb12f0: StoreField: r2->field_13 = r0
    //     0xeb12f0: stur            x0, [x2, #0x13]
    // 0xeb12f4: ldur            x0, [fp, #-0x20]
    // 0xeb12f8: StoreField: r2->field_1b = r0
    //     0xeb12f8: stur            x0, [x2, #0x1b]
    // 0xeb12fc: ldur            x1, [fp, #-0x28]
    // 0xeb1300: LoadField: r3 = r1->field_7
    //     0xeb1300: ldur            w3, [x1, #7]
    // 0xeb1304: DecompressPointer r3
    //     0xeb1304: add             x3, x3, HEAP, lsl #32
    // 0xeb1308: ldur            x1, [fp, #-0x18]
    // 0xeb130c: stur            x3, [fp, #-8]
    // 0xeb1310: r0 = Success()
    //     0xeb1310: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb1314: mov             x1, x0
    // 0xeb1318: ldur            x0, [fp, #-0x10]
    // 0xeb131c: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb131c: stur            w0, [x1, #0x17]
    // 0xeb1320: ldur            x0, [fp, #-8]
    // 0xeb1324: StoreField: r1->field_7 = r0
    //     0xeb1324: stur            w0, [x1, #7]
    // 0xeb1328: ldur            x0, [fp, #-0x20]
    // 0xeb132c: StoreField: r1->field_b = r0
    //     0xeb132c: stur            x0, [x1, #0xb]
    // 0xeb1330: mov             x0, x1
    // 0xeb1334: LeaveFrame
    //     0xeb1334: mov             SP, fp
    //     0xeb1338: ldp             fp, lr, [SP], #0x10
    // 0xeb133c: ret
    //     0xeb133c: ret             
    // 0xeb1340: ldur            x1, [fp, #-0x28]
    // 0xeb1344: r0 = ParserException()
    //     0xeb1344: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb1348: mov             x1, x0
    // 0xeb134c: ldur            x0, [fp, #-0x28]
    // 0xeb1350: StoreField: r1->field_7 = r0
    //     0xeb1350: stur            w0, [x1, #7]
    // 0xeb1354: mov             x0, x1
    // 0xeb1358: r0 = Throw()
    //     0xeb1358: bl              #0xec04b8  ; ThrowStub
    // 0xeb135c: brk             #0
    // 0xeb1360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb1360: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb1364: b               #0xeb1214
  }
}
