// lib: , url: package:petitparser/src/parser/character/char.dart

// class id: 1050885, size: 0x8
class :: {

  static _ char(/* No info */) {
    // ** addr: 0x88b140, size: 0xe8
    // 0x88b140: EnterFrame
    //     0x88b140: stp             fp, lr, [SP, #-0x10]!
    //     0x88b144: mov             fp, SP
    // 0x88b148: AllocStack(0x28)
    //     0x88b148: sub             SP, SP, #0x28
    // 0x88b14c: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */)
    //     0x88b14c: mov             x2, x1
    //     0x88b150: stur            x1, [fp, #-0x10]
    // 0x88b154: CheckStackOverflow
    //     0x88b154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b158: cmp             SP, x16
    //     0x88b15c: b.ls            #0x88b21c
    // 0x88b160: LoadField: r0 = r2->field_7
    //     0x88b160: ldur            w0, [x2, #7]
    // 0x88b164: r1 = LoadInt32Instr(r0)
    //     0x88b164: sbfx            x1, x0, #1, #0x1f
    // 0x88b168: mov             x0, x1
    // 0x88b16c: r1 = 0
    //     0x88b16c: movz            x1, #0
    // 0x88b170: cmp             x1, x0
    // 0x88b174: b.hs            #0x88b224
    // 0x88b178: ArrayLoad: r0 = r2[-8]  ; TypedUnsigned_1
    //     0x88b178: ldrb            w0, [x2, #0xf]
    // 0x88b17c: stur            x0, [fp, #-8]
    // 0x88b180: r0 = SingleCharPredicate()
    //     0x88b180: bl              #0x88b4b8  ; AllocateSingleCharPredicateStub -> SingleCharPredicate (size=0x10)
    // 0x88b184: mov             x3, x0
    // 0x88b188: ldur            x0, [fp, #-8]
    // 0x88b18c: stur            x3, [fp, #-0x18]
    // 0x88b190: StoreField: r3->field_7 = r0
    //     0x88b190: stur            x0, [x3, #7]
    // 0x88b194: r1 = Null
    //     0x88b194: mov             x1, NULL
    // 0x88b198: r2 = 6
    //     0x88b198: movz            x2, #0x6
    // 0x88b19c: r0 = AllocateArray()
    //     0x88b19c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88b1a0: stur            x0, [fp, #-0x20]
    // 0x88b1a4: r16 = "\""
    //     0x88b1a4: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x88b1a8: StoreField: r0->field_f = r16
    //     0x88b1a8: stur            w16, [x0, #0xf]
    // 0x88b1ac: ldur            x1, [fp, #-0x10]
    // 0x88b1b0: r0 = toReadableString()
    //     0x88b1b0: bl              #0x88b234  ; [package:petitparser/src/parser/character/code.dart] ::toReadableString
    // 0x88b1b4: ldur            x1, [fp, #-0x20]
    // 0x88b1b8: ArrayStore: r1[1] = r0  ; List_4
    //     0x88b1b8: add             x25, x1, #0x13
    //     0x88b1bc: str             w0, [x25]
    //     0x88b1c0: tbz             w0, #0, #0x88b1dc
    //     0x88b1c4: ldurb           w16, [x1, #-1]
    //     0x88b1c8: ldurb           w17, [x0, #-1]
    //     0x88b1cc: and             x16, x17, x16, lsr #2
    //     0x88b1d0: tst             x16, HEAP, lsr #32
    //     0x88b1d4: b.eq            #0x88b1dc
    //     0x88b1d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x88b1dc: ldur            x0, [fp, #-0x20]
    // 0x88b1e0: r16 = "\" expected"
    //     0x88b1e0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26938] "\" expected"
    //     0x88b1e4: ldr             x16, [x16, #0x938]
    // 0x88b1e8: ArrayStore: r0[0] = r16  ; List_4
    //     0x88b1e8: stur            w16, [x0, #0x17]
    // 0x88b1ec: str             x0, [SP]
    // 0x88b1f0: r0 = _interpolate()
    //     0x88b1f0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x88b1f4: r1 = <String>
    //     0x88b1f4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88b1f8: stur            x0, [fp, #-0x10]
    // 0x88b1fc: r0 = SingleCharacterParser()
    //     0x88b1fc: bl              #0x88b228  ; AllocateSingleCharacterParserStub -> SingleCharacterParser (size=0x14)
    // 0x88b200: ldur            x1, [fp, #-0x18]
    // 0x88b204: StoreField: r0->field_b = r1
    //     0x88b204: stur            w1, [x0, #0xb]
    // 0x88b208: ldur            x1, [fp, #-0x10]
    // 0x88b20c: StoreField: r0->field_f = r1
    //     0x88b20c: stur            w1, [x0, #0xf]
    // 0x88b210: LeaveFrame
    //     0x88b210: mov             SP, fp
    //     0x88b214: ldp             fp, lr, [SP], #0x10
    // 0x88b218: ret
    //     0x88b218: ret             
    // 0x88b21c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b21c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b220: b               #0x88b160
    // 0x88b224: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x88b224: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 722, size: 0x10, field offset: 0x8
//   const constructor, 
class SingleCharPredicate extends CharacterPredicate {

  _ test(/* No info */) {
    // ** addr: 0xeb37cc, size: 0x44
    // 0xeb37cc: LoadField: r3 = r1->field_7
    //     0xeb37cc: ldur            x3, [x1, #7]
    // 0xeb37d0: lsl             x4, x2, #1
    // 0xeb37d4: r0 = BoxInt64Instr(r3)
    //     0xeb37d4: sbfiz           x0, x3, #1, #0x1f
    //     0xeb37d8: cmp             x3, x0, asr #1
    //     0xeb37dc: b.eq            #0xeb37f8
    //     0xeb37e0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb37e4: mov             fp, SP
    //     0xeb37e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb37ec: mov             SP, fp
    //     0xeb37f0: ldp             fp, lr, [SP], #0x10
    //     0xeb37f4: stur            x3, [x0, #7]
    // 0xeb37f8: cmp             w0, w4
    // 0xeb37fc: r16 = true
    //     0xeb37fc: add             x16, NULL, #0x20  ; true
    // 0xeb3800: r17 = false
    //     0xeb3800: add             x17, NULL, #0x30  ; false
    // 0xeb3804: csel            x1, x16, x17, eq
    // 0xeb3808: mov             x0, x1
    // 0xeb380c: ret
    //     0xeb380c: ret             
  }
}
