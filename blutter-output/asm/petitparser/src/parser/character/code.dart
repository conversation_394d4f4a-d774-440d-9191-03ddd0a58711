// lib: , url: package:petitparser/src/parser/character/code.dart

// class id: 1050886, size: 0x8
class :: {

  static _ toReadableString(/* No info */) {
    // ** addr: 0x88b234, size: 0x70
    // 0x88b234: EnterFrame
    //     0x88b234: stp             fp, lr, [SP, #-0x10]!
    //     0x88b238: mov             fp, SP
    // 0x88b23c: AllocStack(0x20)
    //     0x88b23c: sub             SP, SP, #0x20
    // 0x88b240: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x88b240: mov             x0, x1
    //     0x88b244: stur            x1, [fp, #-8]
    // 0x88b248: CheckStackOverflow
    //     0x88b248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b24c: cmp             SP, x16
    //     0x88b250: b.ls            #0x88b29c
    // 0x88b254: r1 = <int>
    //     0x88b254: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x88b258: r0 = CodeUnits()
    //     0x88b258: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0x88b25c: mov             x1, x0
    // 0x88b260: ldur            x0, [fp, #-8]
    // 0x88b264: StoreField: r1->field_b = r0
    //     0x88b264: stur            w0, [x1, #0xb]
    // 0x88b268: r16 = <String>
    //     0x88b268: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88b26c: stp             x1, x16, [SP, #8]
    // 0x88b270: r16 = Closure: (int) => String from Function '_toFormattedChar@2626124338': static.
    //     0x88b270: add             x16, PP, #0x26, lsl #12  ; [pp+0x268b8] Closure: (int) => String from Function '_toFormattedChar@2626124338': static. (0x7e54fb28b2a4)
    //     0x88b274: ldr             x16, [x16, #0x8b8]
    // 0x88b278: str             x16, [SP]
    // 0x88b27c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88b27c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88b280: r0 = map()
    //     0x88b280: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x88b284: mov             x1, x0
    // 0x88b288: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x88b288: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x88b28c: r0 = join()
    //     0x88b28c: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0x88b290: LeaveFrame
    //     0x88b290: mov             SP, fp
    //     0x88b294: ldp             fp, lr, [SP], #0x10
    // 0x88b298: ret
    //     0x88b298: ret             
    // 0x88b29c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b29c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b2a0: b               #0x88b254
  }
  [closure] static String _toFormattedChar(dynamic, int) {
    // ** addr: 0x88b2a4, size: 0x30
    // 0x88b2a4: EnterFrame
    //     0x88b2a4: stp             fp, lr, [SP, #-0x10]!
    //     0x88b2a8: mov             fp, SP
    // 0x88b2ac: CheckStackOverflow
    //     0x88b2ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b2b0: cmp             SP, x16
    //     0x88b2b4: b.ls            #0x88b2cc
    // 0x88b2b8: ldr             x1, [fp, #0x10]
    // 0x88b2bc: r0 = _toFormattedChar()
    //     0x88b2bc: bl              #0x88b2d4  ; [package:petitparser/src/parser/character/code.dart] ::_toFormattedChar
    // 0x88b2c0: LeaveFrame
    //     0x88b2c0: mov             SP, fp
    //     0x88b2c4: ldp             fp, lr, [SP], #0x10
    // 0x88b2c8: ret
    //     0x88b2c8: ret             
    // 0x88b2cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b2cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b2d0: b               #0x88b2b8
  }
  static String _toFormattedChar(int) {
    // ** addr: 0x88b2d4, size: 0x1e4
    // 0x88b2d4: EnterFrame
    //     0x88b2d4: stp             fp, lr, [SP, #-0x10]!
    //     0x88b2d8: mov             fp, SP
    // 0x88b2dc: AllocStack(0x18)
    //     0x88b2dc: sub             SP, SP, #0x18
    // 0x88b2e0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x88b2e0: mov             x0, x1
    //     0x88b2e4: stur            x1, [fp, #-8]
    // 0x88b2e8: CheckStackOverflow
    //     0x88b2e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b2ec: cmp             SP, x16
    //     0x88b2f0: b.ls            #0x88b4b0
    // 0x88b2f4: r1 = LoadInt32Instr(r0)
    //     0x88b2f4: sbfx            x1, x0, #1, #0x1f
    //     0x88b2f8: tbz             w0, #0, #0x88b300
    //     0x88b2fc: ldur            x1, [x0, #7]
    // 0x88b300: cmp             x1, #0xc
    // 0x88b304: b.gt            #0x88b394
    // 0x88b308: cmp             x1, #0xa
    // 0x88b30c: b.gt            #0x88b364
    // 0x88b310: cmp             x1, #9
    // 0x88b314: b.gt            #0x88b350
    // 0x88b318: cmp             x1, #8
    // 0x88b31c: b.gt            #0x88b33c
    // 0x88b320: cmp             w0, #0x10
    // 0x88b324: b.ne            #0x88b41c
    // 0x88b328: r0 = "\\b"
    //     0x88b328: add             x0, PP, #0x26, lsl #12  ; [pp+0x268c0] "\\b"
    //     0x88b32c: ldr             x0, [x0, #0x8c0]
    // 0x88b330: LeaveFrame
    //     0x88b330: mov             SP, fp
    //     0x88b334: ldp             fp, lr, [SP], #0x10
    // 0x88b338: ret
    //     0x88b338: ret             
    // 0x88b33c: r0 = "\\t"
    //     0x88b33c: add             x0, PP, #0x26, lsl #12  ; [pp+0x268c8] "\\t"
    //     0x88b340: ldr             x0, [x0, #0x8c8]
    // 0x88b344: LeaveFrame
    //     0x88b344: mov             SP, fp
    //     0x88b348: ldp             fp, lr, [SP], #0x10
    // 0x88b34c: ret
    //     0x88b34c: ret             
    // 0x88b350: r0 = "\\n"
    //     0x88b350: add             x0, PP, #0x26, lsl #12  ; [pp+0x268d0] "\\n"
    //     0x88b354: ldr             x0, [x0, #0x8d0]
    // 0x88b358: LeaveFrame
    //     0x88b358: mov             SP, fp
    //     0x88b35c: ldp             fp, lr, [SP], #0x10
    // 0x88b360: ret
    //     0x88b360: ret             
    // 0x88b364: cmp             x1, #0xb
    // 0x88b368: b.gt            #0x88b380
    // 0x88b36c: r0 = "\\v"
    //     0x88b36c: add             x0, PP, #0x26, lsl #12  ; [pp+0x268d8] "\\v"
    //     0x88b370: ldr             x0, [x0, #0x8d8]
    // 0x88b374: LeaveFrame
    //     0x88b374: mov             SP, fp
    //     0x88b378: ldp             fp, lr, [SP], #0x10
    // 0x88b37c: ret
    //     0x88b37c: ret             
    // 0x88b380: r0 = "\\f"
    //     0x88b380: add             x0, PP, #0x26, lsl #12  ; [pp+0x268e0] "\\f"
    //     0x88b384: ldr             x0, [x0, #0x8e0]
    // 0x88b388: LeaveFrame
    //     0x88b388: mov             SP, fp
    //     0x88b38c: ldp             fp, lr, [SP], #0x10
    // 0x88b390: ret
    //     0x88b390: ret             
    // 0x88b394: cmp             x1, #0x22
    // 0x88b398: b.gt            #0x88b3d4
    // 0x88b39c: cmp             x1, #0xd
    // 0x88b3a0: b.gt            #0x88b3b8
    // 0x88b3a4: r0 = "\\r"
    //     0x88b3a4: add             x0, PP, #0x26, lsl #12  ; [pp+0x268e8] "\\r"
    //     0x88b3a8: ldr             x0, [x0, #0x8e8]
    // 0x88b3ac: LeaveFrame
    //     0x88b3ac: mov             SP, fp
    //     0x88b3b0: ldp             fp, lr, [SP], #0x10
    // 0x88b3b4: ret
    //     0x88b3b4: ret             
    // 0x88b3b8: cmp             x1, #0x22
    // 0x88b3bc: b.lt            #0x88b41c
    // 0x88b3c0: r0 = "\\\""
    //     0x88b3c0: add             x0, PP, #0x10, lsl #12  ; [pp+0x10808] "\\\""
    //     0x88b3c4: ldr             x0, [x0, #0x808]
    // 0x88b3c8: LeaveFrame
    //     0x88b3c8: mov             SP, fp
    //     0x88b3cc: ldp             fp, lr, [SP], #0x10
    // 0x88b3d0: ret
    //     0x88b3d0: ret             
    // 0x88b3d4: cmp             x1, #0x27
    // 0x88b3d8: b.lt            #0x88b41c
    // 0x88b3dc: cmp             x1, #0x27
    // 0x88b3e0: b.gt            #0x88b3f8
    // 0x88b3e4: r0 = "\\\'"
    //     0x88b3e4: add             x0, PP, #0x26, lsl #12  ; [pp+0x268f0] "\\\'"
    //     0x88b3e8: ldr             x0, [x0, #0x8f0]
    // 0x88b3ec: LeaveFrame
    //     0x88b3ec: mov             SP, fp
    //     0x88b3f0: ldp             fp, lr, [SP], #0x10
    // 0x88b3f4: ret
    //     0x88b3f4: ret             
    // 0x88b3f8: cmp             x1, #0x5c
    // 0x88b3fc: b.lt            #0x88b41c
    // 0x88b400: cmp             w0, #0xb8
    // 0x88b404: b.ne            #0x88b41c
    // 0x88b408: r0 = "\\\\"
    //     0x88b408: add             x0, PP, #0x10, lsl #12  ; [pp+0x10800] "\\\\"
    //     0x88b40c: ldr             x0, [x0, #0x800]
    // 0x88b410: LeaveFrame
    //     0x88b410: mov             SP, fp
    //     0x88b414: ldp             fp, lr, [SP], #0x10
    // 0x88b418: ret
    //     0x88b418: ret             
    // 0x88b41c: cmp             x1, #0x20
    // 0x88b420: b.ge            #0x88b498
    // 0x88b424: r1 = Null
    //     0x88b424: mov             x1, NULL
    // 0x88b428: r2 = 4
    //     0x88b428: movz            x2, #0x4
    // 0x88b42c: r0 = AllocateArray()
    //     0x88b42c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88b430: stur            x0, [fp, #-0x10]
    // 0x88b434: r16 = "\\x"
    //     0x88b434: add             x16, PP, #0x26, lsl #12  ; [pp+0x268f8] "\\x"
    //     0x88b438: ldr             x16, [x16, #0x8f8]
    // 0x88b43c: StoreField: r0->field_f = r16
    //     0x88b43c: stur            w16, [x0, #0xf]
    // 0x88b440: ldur            x1, [fp, #-8]
    // 0x88b444: r0 = _toPow2String()
    //     0x88b444: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0x88b448: mov             x1, x0
    // 0x88b44c: r2 = 2
    //     0x88b44c: movz            x2, #0x2
    // 0x88b450: r3 = "0"
    //     0x88b450: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x88b454: r0 = padLeft()
    //     0x88b454: bl              #0xebe370  ; [dart:core] _OneByteString::padLeft
    // 0x88b458: ldur            x1, [fp, #-0x10]
    // 0x88b45c: ArrayStore: r1[1] = r0  ; List_4
    //     0x88b45c: add             x25, x1, #0x13
    //     0x88b460: str             w0, [x25]
    //     0x88b464: tbz             w0, #0, #0x88b480
    //     0x88b468: ldurb           w16, [x1, #-1]
    //     0x88b46c: ldurb           w17, [x0, #-1]
    //     0x88b470: and             x16, x17, x16, lsr #2
    //     0x88b474: tst             x16, HEAP, lsr #32
    //     0x88b478: b.eq            #0x88b480
    //     0x88b47c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x88b480: ldur            x16, [fp, #-0x10]
    // 0x88b484: str             x16, [SP]
    // 0x88b488: r0 = _interpolate()
    //     0x88b488: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x88b48c: LeaveFrame
    //     0x88b48c: mov             SP, fp
    //     0x88b490: ldp             fp, lr, [SP], #0x10
    // 0x88b494: ret
    //     0x88b494: ret             
    // 0x88b498: ldur            x2, [fp, #-8]
    // 0x88b49c: r1 = Null
    //     0x88b49c: mov             x1, NULL
    // 0x88b4a0: r0 = String.fromCharCode()
    //     0x88b4a0: bl              #0x602bac  ; [dart:core] String::String.fromCharCode
    // 0x88b4a4: LeaveFrame
    //     0x88b4a4: mov             SP, fp
    //     0x88b4a8: ldp             fp, lr, [SP], #0x10
    // 0x88b4ac: ret
    //     0x88b4ac: ret             
    // 0x88b4b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b4b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b4b4: b               #0x88b2f4
  }
}
