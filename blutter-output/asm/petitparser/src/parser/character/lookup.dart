// lib: , url: package:petitparser/src/parser/character/lookup.dart

// class id: 1050888, size: 0x8
class :: {
}

// class id: 718, size: 0x1c, field offset: 0x8
class LookupCharPredicate extends Object
    implements CharacterPredicate {

  _ LookupCharPredicate(/* No info */) {
    // ** addr: 0x88da8c, size: 0x1d4
    // 0x88da8c: EnterFrame
    //     0x88da8c: stp             fp, lr, [SP, #-0x10]!
    //     0x88da90: mov             fp, SP
    // 0x88da94: AllocStack(0x20)
    //     0x88da94: sub             SP, SP, #0x20
    // 0x88da98: SetupParameters(LookupCharPredicate this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x88da98: mov             x0, x2
    //     0x88da9c: stur            x2, [fp, #-0x10]
    //     0x88daa0: mov             x2, x1
    //     0x88daa4: stur            x1, [fp, #-8]
    // 0x88daa8: CheckStackOverflow
    //     0x88daa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88daac: cmp             SP, x16
    //     0x88dab0: b.ls            #0x88dc44
    // 0x88dab4: mov             x1, x0
    // 0x88dab8: r0 = first()
    //     0x88dab8: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x88dabc: LoadField: r2 = r0->field_7
    //     0x88dabc: ldur            x2, [x0, #7]
    // 0x88dac0: ldur            x0, [fp, #-8]
    // 0x88dac4: stur            x2, [fp, #-0x18]
    // 0x88dac8: StoreField: r0->field_7 = r2
    //     0x88dac8: stur            x2, [x0, #7]
    // 0x88dacc: ldur            x1, [fp, #-0x10]
    // 0x88dad0: r0 = last()
    //     0x88dad0: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x88dad4: LoadField: r1 = r0->field_f
    //     0x88dad4: ldur            x1, [x0, #0xf]
    // 0x88dad8: ldur            x0, [fp, #-8]
    // 0x88dadc: StoreField: r0->field_f = r1
    //     0x88dadc: stur            x1, [x0, #0xf]
    // 0x88dae0: ldur            x1, [fp, #-0x10]
    // 0x88dae4: r0 = last()
    //     0x88dae4: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x88dae8: LoadField: r2 = r0->field_f
    //     0x88dae8: ldur            x2, [x0, #0xf]
    // 0x88daec: ldur            x1, [fp, #-0x10]
    // 0x88daf0: stur            x2, [fp, #-0x20]
    // 0x88daf4: r0 = first()
    //     0x88daf4: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x88daf8: LoadField: r1 = r0->field_7
    //     0x88daf8: ldur            x1, [x0, #7]
    // 0x88dafc: ldur            x0, [fp, #-0x20]
    // 0x88db00: sub             x2, x0, x1
    // 0x88db04: add             x0, x2, #1
    // 0x88db08: add             x1, x0, #0x1f
    // 0x88db0c: asr             x2, x1, #5
    // 0x88db10: stur            x2, [fp, #-0x20]
    // 0x88db14: r0 = BoxInt64Instr(r2)
    //     0x88db14: sbfiz           x0, x2, #1, #0x1f
    //     0x88db18: cmp             x2, x0, asr #1
    //     0x88db1c: b.eq            #0x88db28
    //     0x88db20: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x88db24: stur            x2, [x0, #7]
    // 0x88db28: mov             x4, x0
    // 0x88db2c: r0 = AllocateUint32Array()
    //     0x88db2c: bl              #0xec1c2c  ; AllocateUint32ArrayStub
    // 0x88db30: mov             x3, x0
    // 0x88db34: ldur            x2, [fp, #-8]
    // 0x88db38: ArrayStore: r2[0] = r0  ; List_4
    //     0x88db38: stur            w0, [x2, #0x17]
    //     0x88db3c: ldurb           w16, [x2, #-1]
    //     0x88db40: ldurb           w17, [x0, #-1]
    //     0x88db44: and             x16, x17, x16, lsr #2
    //     0x88db48: tst             x16, HEAP, lsr #32
    //     0x88db4c: b.eq            #0x88db54
    //     0x88db50: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x88db54: ldur            x2, [fp, #-0x10]
    // 0x88db58: LoadField: r4 = r2->field_b
    //     0x88db58: ldur            w4, [x2, #0xb]
    // 0x88db5c: r5 = LoadInt32Instr(r4)
    //     0x88db5c: sbfx            x5, x4, #1, #0x1f
    // 0x88db60: LoadField: r4 = r2->field_f
    //     0x88db60: ldur            w4, [x2, #0xf]
    // 0x88db64: DecompressPointer r4
    //     0x88db64: add             x4, x4, HEAP, lsl #32
    // 0x88db68: ldur            x2, [fp, #-0x18]
    // 0x88db6c: r8 = 0
    //     0x88db6c: movz            x8, #0
    // 0x88db70: r7 = const [0x1, 0x2, 0x4, 0x8, 0x10, 0x20, 0x40, 0x80, 0x100, 0x200, 0x400, 0x800, 0x1000, 0x2000, 0x4000, 0x8000, 0x10000, 0x20000, 0x40000, 0x80000, 0x100000, 0x200000, 0x400000, 0x800000, 0x1000000, 0x2000000, 0x4000000, 0x8000000, 0x10000000, 0x20000000, 1073741824, 2147483648]
    //     0x88db70: add             x7, PP, #0x26, lsl #12  ; [pp+0x269c8] List<int>(32)
    //     0x88db74: ldr             x7, [x7, #0x9c8]
    // 0x88db78: r6 = 31
    //     0x88db78: movz            x6, #0x1f
    // 0x88db7c: CheckStackOverflow
    //     0x88db7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88db80: cmp             SP, x16
    //     0x88db84: b.ls            #0x88dc4c
    // 0x88db88: cmp             x8, x5
    // 0x88db8c: b.ge            #0x88dc34
    // 0x88db90: ArrayLoad: r9 = r4[r8]  ; Unknown_4
    //     0x88db90: add             x16, x4, x8, lsl #2
    //     0x88db94: ldur            w9, [x16, #0xf]
    // 0x88db98: DecompressPointer r9
    //     0x88db98: add             x9, x9, HEAP, lsl #32
    // 0x88db9c: add             x10, x8, #1
    // 0x88dba0: LoadField: r8 = r9->field_7
    //     0x88dba0: ldur            x8, [x9, #7]
    // 0x88dba4: sub             x11, x8, x2
    // 0x88dba8: LoadField: r8 = r9->field_f
    //     0x88dba8: ldur            x8, [x9, #0xf]
    // 0x88dbac: sub             x9, x8, x2
    // 0x88dbb0: mov             x8, x11
    // 0x88dbb4: CheckStackOverflow
    //     0x88dbb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88dbb8: cmp             SP, x16
    //     0x88dbbc: b.ls            #0x88dc54
    // 0x88dbc0: cmp             x8, x9
    // 0x88dbc4: b.gt            #0x88dc2c
    // 0x88dbc8: asr             x11, x8, #5
    // 0x88dbcc: ldur            x0, [fp, #-0x20]
    // 0x88dbd0: mov             x1, x11
    // 0x88dbd4: cmp             x1, x0
    // 0x88dbd8: b.hs            #0x88dc5c
    // 0x88dbdc: ArrayLoad: r1 = r3[r11]  ; List_4
    //     0x88dbdc: add             x16, x3, x11, lsl #2
    //     0x88dbe0: ldur            w1, [x16, #0x17]
    // 0x88dbe4: mov             x12, x8
    // 0x88dbe8: ubfx            x12, x12, #0, #0x20
    // 0x88dbec: and             x13, x12, x6
    // 0x88dbf0: ubfx            x13, x13, #0, #0x20
    // 0x88dbf4: ArrayLoad: r12 = r7[r13]  ; Unknown_4
    //     0x88dbf4: add             x16, x7, x13, lsl #2
    //     0x88dbf8: ldur            w12, [x16, #0xf]
    // 0x88dbfc: DecompressPointer r12
    //     0x88dbfc: add             x12, x12, HEAP, lsl #32
    // 0x88dc00: ubfx            x1, x1, #0, #0x20
    // 0x88dc04: r13 = LoadInt32Instr(r12)
    //     0x88dc04: sbfx            x13, x12, #1, #0x1f
    //     0x88dc08: tbz             w12, #0, #0x88dc10
    //     0x88dc0c: ldur            x13, [x12, #7]
    // 0x88dc10: orr             x12, x1, x13
    // 0x88dc14: ubfx            x12, x12, #0, #0x20
    // 0x88dc18: ArrayStore: r3[r11] = r12  ; List_4
    //     0x88dc18: add             x1, x3, x11, lsl #2
    //     0x88dc1c: stur            w12, [x1, #0x17]
    // 0x88dc20: add             x0, x8, #1
    // 0x88dc24: mov             x8, x0
    // 0x88dc28: b               #0x88dbb4
    // 0x88dc2c: mov             x8, x10
    // 0x88dc30: b               #0x88db7c
    // 0x88dc34: r0 = Null
    //     0x88dc34: mov             x0, NULL
    // 0x88dc38: LeaveFrame
    //     0x88dc38: mov             SP, fp
    //     0x88dc3c: ldp             fp, lr, [SP], #0x10
    // 0x88dc40: ret
    //     0x88dc40: ret             
    // 0x88dc44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88dc44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88dc48: b               #0x88dab4
    // 0x88dc4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88dc4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88dc50: b               #0x88db88
    // 0x88dc54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88dc54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88dc58: b               #0x88dbc0
    // 0x88dc5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x88dc5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ test(/* No info */) {
    // ** addr: 0xeb3868, size: 0xb0
    // 0xeb3868: EnterFrame
    //     0xeb3868: stp             fp, lr, [SP, #-0x10]!
    //     0xeb386c: mov             fp, SP
    // 0xeb3870: LoadField: r3 = r1->field_7
    //     0xeb3870: ldur            x3, [x1, #7]
    // 0xeb3874: cmp             x3, x2
    // 0xeb3878: b.gt            #0xeb3904
    // 0xeb387c: LoadField: r4 = r1->field_f
    //     0xeb387c: ldur            x4, [x1, #0xf]
    // 0xeb3880: cmp             x2, x4
    // 0xeb3884: b.gt            #0xeb3904
    // 0xeb3888: r5 = const [0x1, 0x2, 0x4, 0x8, 0x10, 0x20, 0x40, 0x80, 0x100, 0x200, 0x400, 0x800, 0x1000, 0x2000, 0x4000, 0x8000, 0x10000, 0x20000, 0x40000, 0x80000, 0x100000, 0x200000, 0x400000, 0x800000, 0x1000000, 0x2000000, 0x4000000, 0x8000000, 0x10000000, 0x20000000, 1073741824, 2147483648]
    //     0xeb3888: add             x5, PP, #0x26, lsl #12  ; [pp+0x269c8] List<int>(32)
    //     0xeb388c: ldr             x5, [x5, #0x9c8]
    // 0xeb3890: r4 = 31
    //     0xeb3890: movz            x4, #0x1f
    // 0xeb3894: sub             x6, x2, x3
    // 0xeb3898: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xeb3898: ldur            w2, [x1, #0x17]
    // 0xeb389c: DecompressPointer r2
    //     0xeb389c: add             x2, x2, HEAP, lsl #32
    // 0xeb38a0: asr             x3, x6, #5
    // 0xeb38a4: LoadField: r7 = r2->field_13
    //     0xeb38a4: ldur            w7, [x2, #0x13]
    // 0xeb38a8: r0 = LoadInt32Instr(r7)
    //     0xeb38a8: sbfx            x0, x7, #1, #0x1f
    // 0xeb38ac: mov             x1, x3
    // 0xeb38b0: cmp             x1, x0
    // 0xeb38b4: b.hs            #0xeb3914
    // 0xeb38b8: ArrayLoad: r1 = r2[r3]  ; List_4
    //     0xeb38b8: add             x16, x2, x3, lsl #2
    //     0xeb38bc: ldur            w1, [x16, #0x17]
    // 0xeb38c0: ubfx            x6, x6, #0, #0x20
    // 0xeb38c4: and             x2, x6, x4
    // 0xeb38c8: ubfx            x2, x2, #0, #0x20
    // 0xeb38cc: ArrayLoad: r3 = r5[r2]  ; Unknown_4
    //     0xeb38cc: add             x16, x5, x2, lsl #2
    //     0xeb38d0: ldur            w3, [x16, #0xf]
    // 0xeb38d4: DecompressPointer r3
    //     0xeb38d4: add             x3, x3, HEAP, lsl #32
    // 0xeb38d8: r2 = LoadInt32Instr(r3)
    //     0xeb38d8: sbfx            x2, x3, #1, #0x1f
    //     0xeb38dc: tbz             w3, #0, #0xeb38e4
    //     0xeb38e0: ldur            x2, [x3, #7]
    // 0xeb38e4: and             x3, x1, x2
    // 0xeb38e8: ubfx            x3, x3, #0, #0x20
    // 0xeb38ec: cbnz            x3, #0xeb38f8
    // 0xeb38f0: r1 = false
    //     0xeb38f0: add             x1, NULL, #0x30  ; false
    // 0xeb38f4: b               #0xeb38fc
    // 0xeb38f8: r1 = true
    //     0xeb38f8: add             x1, NULL, #0x20  ; true
    // 0xeb38fc: mov             x0, x1
    // 0xeb3900: b               #0xeb3908
    // 0xeb3904: r0 = false
    //     0xeb3904: add             x0, NULL, #0x30  ; false
    // 0xeb3908: LeaveFrame
    //     0xeb3908: mov             SP, fp
    //     0xeb390c: ldp             fp, lr, [SP], #0x10
    // 0xeb3910: ret
    //     0xeb3910: ret             
    // 0xeb3914: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb3914: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
