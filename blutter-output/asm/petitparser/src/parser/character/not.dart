// lib: , url: package:petitparser/src/parser/character/not.dart

// class id: 1050889, size: 0x8
class :: {
}

// class id: 720, size: 0xc, field offset: 0x8
//   const constructor, 
class NotCharacterPredicate extends CharacterPredicate {

  _ test(/* No info */) {
    // ** addr: 0xeb3810, size: 0x58
    // 0xeb3810: EnterFrame
    //     0xeb3810: stp             fp, lr, [SP, #-0x10]!
    //     0xeb3814: mov             fp, SP
    // 0xeb3818: CheckStackOverflow
    //     0xeb3818: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb381c: cmp             SP, x16
    //     0xeb3820: b.ls            #0xeb3860
    // 0xeb3824: LoadField: r0 = r1->field_7
    //     0xeb3824: ldur            w0, [x1, #7]
    // 0xeb3828: DecompressPointer r0
    //     0xeb3828: add             x0, x0, HEAP, lsl #32
    // 0xeb382c: r1 = LoadClassIdInstr(r0)
    //     0xeb382c: ldur            x1, [x0, #-1]
    //     0xeb3830: ubfx            x1, x1, #0xc, #0x14
    // 0xeb3834: mov             x16, x0
    // 0xeb3838: mov             x0, x1
    // 0xeb383c: mov             x1, x16
    // 0xeb3840: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb3840: sub             lr, x0, #1, lsl #12
    //     0xeb3844: ldr             lr, [x21, lr, lsl #3]
    //     0xeb3848: blr             lr
    // 0xeb384c: eor             x1, x0, #0x10
    // 0xeb3850: mov             x0, x1
    // 0xeb3854: LeaveFrame
    //     0xeb3854: mov             SP, fp
    //     0xeb3858: ldp             fp, lr, [SP], #0x10
    // 0xeb385c: ret
    //     0xeb385c: ret             
    // 0xeb3860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb3860: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb3864: b               #0xeb3824
  }
}
