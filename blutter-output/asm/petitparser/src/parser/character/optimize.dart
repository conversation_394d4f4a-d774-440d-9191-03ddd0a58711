// lib: , url: package:petitparser/src/parser/character/optimize.dart

// class id: 1050890, size: 0x8
class :: {

  [closure] static CharacterPredicate optimizedRanges(dynamic, Iterable<RangeCharPredicate>) {
    // ** addr: 0x88d61c, size: 0x30
    // 0x88d61c: EnterFrame
    //     0x88d61c: stp             fp, lr, [SP, #-0x10]!
    //     0x88d620: mov             fp, SP
    // 0x88d624: CheckStackOverflow
    //     0x88d624: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d628: cmp             SP, x16
    //     0x88d62c: b.ls            #0x88d644
    // 0x88d630: ldr             x1, [fp, #0x10]
    // 0x88d634: r0 = optimizedRanges()
    //     0x88d634: bl              #0x88d64c  ; [package:petitparser/src/parser/character/optimize.dart] ::optimizedRanges
    // 0x88d638: LeaveFrame
    //     0x88d638: mov             SP, fp
    //     0x88d63c: ldp             fp, lr, [SP], #0x10
    // 0x88d640: ret
    //     0x88d640: ret             
    // 0x88d644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88d644: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88d648: b               #0x88d630
  }
  static _ optimizedRanges(/* No info */) {
    // ** addr: 0x88d64c, size: 0x440
    // 0x88d64c: EnterFrame
    //     0x88d64c: stp             fp, lr, [SP, #-0x10]!
    //     0x88d650: mov             fp, SP
    // 0x88d654: AllocStack(0x78)
    //     0x88d654: sub             SP, SP, #0x78
    // 0x88d658: SetupParameters(dynamic _ /* r1 => r2 */)
    //     0x88d658: mov             x2, x1
    // 0x88d65c: CheckStackOverflow
    //     0x88d65c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d660: cmp             SP, x16
    //     0x88d664: b.ls            #0x88da74
    // 0x88d668: r1 = <RangeCharPredicate>
    //     0x88d668: add             x1, PP, #0x26, lsl #12  ; [pp+0x26958] TypeArguments: <RangeCharPredicate>
    //     0x88d66c: ldr             x1, [x1, #0x958]
    // 0x88d670: r0 = _List.of()
    //     0x88d670: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x88d674: r1 = Function '<anonymous closure>': static.
    //     0x88d674: add             x1, PP, #0x26, lsl #12  ; [pp+0x26970] AnonymousClosure: static (0x88dce8), in [package:petitparser/src/parser/character/optimize.dart] ::optimizedRanges (0x88d64c)
    //     0x88d678: ldr             x1, [x1, #0x970]
    // 0x88d67c: r2 = Null
    //     0x88d67c: mov             x2, NULL
    // 0x88d680: stur            x0, [fp, #-8]
    // 0x88d684: r0 = AllocateClosure()
    //     0x88d684: bl              #0xec1630  ; AllocateClosureStub
    // 0x88d688: str             x0, [SP]
    // 0x88d68c: ldur            x1, [fp, #-8]
    // 0x88d690: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x88d690: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x88d694: r0 = sort()
    //     0x88d694: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x88d698: r1 = <RangeCharPredicate>
    //     0x88d698: add             x1, PP, #0x26, lsl #12  ; [pp+0x26958] TypeArguments: <RangeCharPredicate>
    //     0x88d69c: ldr             x1, [x1, #0x958]
    // 0x88d6a0: r2 = 0
    //     0x88d6a0: movz            x2, #0
    // 0x88d6a4: r0 = _GrowableList()
    //     0x88d6a4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x88d6a8: mov             x4, x0
    // 0x88d6ac: ldur            x3, [fp, #-8]
    // 0x88d6b0: stur            x4, [fp, #-0x30]
    // 0x88d6b4: LoadField: r5 = r3->field_7
    //     0x88d6b4: ldur            w5, [x3, #7]
    // 0x88d6b8: DecompressPointer r5
    //     0x88d6b8: add             x5, x5, HEAP, lsl #32
    // 0x88d6bc: stur            x5, [fp, #-0x28]
    // 0x88d6c0: LoadField: r0 = r3->field_b
    //     0x88d6c0: ldur            w0, [x3, #0xb]
    // 0x88d6c4: r6 = LoadInt32Instr(r0)
    //     0x88d6c4: sbfx            x6, x0, #1, #0x1f
    // 0x88d6c8: stur            x6, [fp, #-0x20]
    // 0x88d6cc: r0 = 0
    //     0x88d6cc: movz            x0, #0
    // 0x88d6d0: CheckStackOverflow
    //     0x88d6d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d6d4: cmp             SP, x16
    //     0x88d6d8: b.ls            #0x88da7c
    // 0x88d6dc: cmp             x0, x6
    // 0x88d6e0: b.ge            #0x88d960
    // 0x88d6e4: ArrayLoad: r7 = r3[r0]  ; Unknown_4
    //     0x88d6e4: add             x16, x3, x0, lsl #2
    //     0x88d6e8: ldur            w7, [x16, #0xf]
    // 0x88d6ec: DecompressPointer r7
    //     0x88d6ec: add             x7, x7, HEAP, lsl #32
    // 0x88d6f0: stur            x7, [fp, #-0x18]
    // 0x88d6f4: add             x8, x0, #1
    // 0x88d6f8: stur            x8, [fp, #-0x10]
    // 0x88d6fc: cmp             w7, NULL
    // 0x88d700: b.ne            #0x88d734
    // 0x88d704: mov             x0, x7
    // 0x88d708: mov             x2, x5
    // 0x88d70c: r1 = Null
    //     0x88d70c: mov             x1, NULL
    // 0x88d710: cmp             w2, NULL
    // 0x88d714: b.eq            #0x88d734
    // 0x88d718: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x88d718: ldur            w4, [x2, #0x17]
    // 0x88d71c: DecompressPointer r4
    //     0x88d71c: add             x4, x4, HEAP, lsl #32
    // 0x88d720: r8 = X0
    //     0x88d720: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x88d724: LoadField: r9 = r4->field_7
    //     0x88d724: ldur            x9, [x4, #7]
    // 0x88d728: r3 = Null
    //     0x88d728: add             x3, PP, #0x26, lsl #12  ; [pp+0x26978] Null
    //     0x88d72c: ldr             x3, [x3, #0x978]
    // 0x88d730: blr             x9
    // 0x88d734: ldur            x3, [fp, #-0x30]
    // 0x88d738: LoadField: r0 = r3->field_b
    //     0x88d738: ldur            w0, [x3, #0xb]
    // 0x88d73c: r4 = LoadInt32Instr(r0)
    //     0x88d73c: sbfx            x4, x0, #1, #0x1f
    // 0x88d740: stur            x4, [fp, #-0x38]
    // 0x88d744: cbnz            x4, #0x88d7ec
    // 0x88d748: ldur            x0, [fp, #-0x18]
    // 0x88d74c: r2 = Null
    //     0x88d74c: mov             x2, NULL
    // 0x88d750: r1 = Null
    //     0x88d750: mov             x1, NULL
    // 0x88d754: r4 = LoadClassIdInstr(r0)
    //     0x88d754: ldur            x4, [x0, #-1]
    //     0x88d758: ubfx            x4, x4, #0xc, #0x14
    // 0x88d75c: cmp             x4, #0x2cd
    // 0x88d760: b.eq            #0x88d778
    // 0x88d764: r8 = RangeCharPredicate
    //     0x88d764: add             x8, PP, #0x26, lsl #12  ; [pp+0x26988] Type: RangeCharPredicate
    //     0x88d768: ldr             x8, [x8, #0x988]
    // 0x88d76c: r3 = Null
    //     0x88d76c: add             x3, PP, #0x26, lsl #12  ; [pp+0x26990] Null
    //     0x88d770: ldr             x3, [x3, #0x990]
    // 0x88d774: r0 = RangeCharPredicate()
    //     0x88d774: bl              #0x88dcc8  ; IsType_RangeCharPredicate_Stub
    // 0x88d778: ldur            x0, [fp, #-0x30]
    // 0x88d77c: LoadField: r1 = r0->field_f
    //     0x88d77c: ldur            w1, [x0, #0xf]
    // 0x88d780: DecompressPointer r1
    //     0x88d780: add             x1, x1, HEAP, lsl #32
    // 0x88d784: LoadField: r2 = r1->field_b
    //     0x88d784: ldur            w2, [x1, #0xb]
    // 0x88d788: r1 = LoadInt32Instr(r2)
    //     0x88d788: sbfx            x1, x2, #1, #0x1f
    // 0x88d78c: ldur            x2, [fp, #-0x38]
    // 0x88d790: cmp             x2, x1
    // 0x88d794: b.ne            #0x88d7a0
    // 0x88d798: mov             x1, x0
    // 0x88d79c: r0 = _growToNextCapacity()
    //     0x88d79c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x88d7a0: ldur            x2, [fp, #-0x30]
    // 0x88d7a4: ldur            x3, [fp, #-0x38]
    // 0x88d7a8: r4 = 2
    //     0x88d7a8: movz            x4, #0x2
    // 0x88d7ac: StoreField: r2->field_b = r4
    //     0x88d7ac: stur            w4, [x2, #0xb]
    // 0x88d7b0: LoadField: r1 = r2->field_f
    //     0x88d7b0: ldur            w1, [x2, #0xf]
    // 0x88d7b4: DecompressPointer r1
    //     0x88d7b4: add             x1, x1, HEAP, lsl #32
    // 0x88d7b8: ldur            x0, [fp, #-0x18]
    // 0x88d7bc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x88d7bc: add             x25, x1, x3, lsl #2
    //     0x88d7c0: add             x25, x25, #0xf
    //     0x88d7c4: str             w0, [x25]
    //     0x88d7c8: tbz             w0, #0, #0x88d7e4
    //     0x88d7cc: ldurb           w16, [x1, #-1]
    //     0x88d7d0: ldurb           w17, [x0, #-1]
    //     0x88d7d4: and             x16, x17, x16, lsr #2
    //     0x88d7d8: tst             x16, HEAP, lsr #32
    //     0x88d7dc: b.eq            #0x88d7e4
    //     0x88d7e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x88d7e4: mov             x3, x2
    // 0x88d7e8: b               #0x88d948
    // 0x88d7ec: mov             x2, x3
    // 0x88d7f0: mov             x3, x4
    // 0x88d7f4: r4 = 2
    //     0x88d7f4: movz            x4, #0x2
    // 0x88d7f8: cmp             x3, #0
    // 0x88d7fc: b.le            #0x88da68
    // 0x88d800: ldur            x5, [fp, #-0x18]
    // 0x88d804: sub             x6, x3, #1
    // 0x88d808: mov             x0, x3
    // 0x88d80c: mov             x1, x6
    // 0x88d810: stur            x6, [fp, #-0x58]
    // 0x88d814: cmp             x1, x0
    // 0x88d818: b.hs            #0x88da84
    // 0x88d81c: LoadField: r1 = r2->field_f
    //     0x88d81c: ldur            w1, [x2, #0xf]
    // 0x88d820: DecompressPointer r1
    //     0x88d820: add             x1, x1, HEAP, lsl #32
    // 0x88d824: stur            x1, [fp, #-0x50]
    // 0x88d828: ArrayLoad: r0 = r1[r6]  ; Unknown_4
    //     0x88d828: add             x16, x1, x6, lsl #2
    //     0x88d82c: ldur            w0, [x16, #0xf]
    // 0x88d830: DecompressPointer r0
    //     0x88d830: add             x0, x0, HEAP, lsl #32
    // 0x88d834: LoadField: r7 = r0->field_f
    //     0x88d834: ldur            x7, [x0, #0xf]
    // 0x88d838: add             x8, x7, #1
    // 0x88d83c: LoadField: r7 = r5->field_7
    //     0x88d83c: ldur            x7, [x5, #7]
    // 0x88d840: cmp             x8, x7
    // 0x88d844: b.lt            #0x88d8ac
    // 0x88d848: LoadField: r3 = r0->field_7
    //     0x88d848: ldur            x3, [x0, #7]
    // 0x88d84c: stur            x3, [fp, #-0x48]
    // 0x88d850: LoadField: r0 = r5->field_f
    //     0x88d850: ldur            x0, [x5, #0xf]
    // 0x88d854: stur            x0, [fp, #-0x40]
    // 0x88d858: r0 = RangeCharPredicate()
    //     0x88d858: bl              #0x88dc6c  ; AllocateRangeCharPredicateStub -> RangeCharPredicate (size=0x18)
    // 0x88d85c: mov             x1, x0
    // 0x88d860: ldur            x0, [fp, #-0x48]
    // 0x88d864: StoreField: r1->field_7 = r0
    //     0x88d864: stur            x0, [x1, #7]
    // 0x88d868: ldur            x0, [fp, #-0x40]
    // 0x88d86c: StoreField: r1->field_f = r0
    //     0x88d86c: stur            x0, [x1, #0xf]
    // 0x88d870: mov             x0, x1
    // 0x88d874: ldur            x1, [fp, #-0x50]
    // 0x88d878: ldur            x2, [fp, #-0x58]
    // 0x88d87c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x88d87c: add             x25, x1, x2, lsl #2
    //     0x88d880: add             x25, x25, #0xf
    //     0x88d884: str             w0, [x25]
    //     0x88d888: tbz             w0, #0, #0x88d8a4
    //     0x88d88c: ldurb           w16, [x1, #-1]
    //     0x88d890: ldurb           w17, [x0, #-1]
    //     0x88d894: and             x16, x17, x16, lsr #2
    //     0x88d898: tst             x16, HEAP, lsr #32
    //     0x88d89c: b.eq            #0x88d8a4
    //     0x88d8a0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x88d8a4: ldur            x3, [fp, #-0x30]
    // 0x88d8a8: b               #0x88d948
    // 0x88d8ac: mov             x4, x1
    // 0x88d8b0: mov             x0, x5
    // 0x88d8b4: r2 = Null
    //     0x88d8b4: mov             x2, NULL
    // 0x88d8b8: r1 = Null
    //     0x88d8b8: mov             x1, NULL
    // 0x88d8bc: r4 = LoadClassIdInstr(r0)
    //     0x88d8bc: ldur            x4, [x0, #-1]
    //     0x88d8c0: ubfx            x4, x4, #0xc, #0x14
    // 0x88d8c4: cmp             x4, #0x2cd
    // 0x88d8c8: b.eq            #0x88d8e0
    // 0x88d8cc: r8 = RangeCharPredicate
    //     0x88d8cc: add             x8, PP, #0x26, lsl #12  ; [pp+0x26988] Type: RangeCharPredicate
    //     0x88d8d0: ldr             x8, [x8, #0x988]
    // 0x88d8d4: r3 = Null
    //     0x88d8d4: add             x3, PP, #0x26, lsl #12  ; [pp+0x269a0] Null
    //     0x88d8d8: ldr             x3, [x3, #0x9a0]
    // 0x88d8dc: r0 = RangeCharPredicate()
    //     0x88d8dc: bl              #0x88dcc8  ; IsType_RangeCharPredicate_Stub
    // 0x88d8e0: ldur            x0, [fp, #-0x50]
    // 0x88d8e4: LoadField: r1 = r0->field_b
    //     0x88d8e4: ldur            w1, [x0, #0xb]
    // 0x88d8e8: r0 = LoadInt32Instr(r1)
    //     0x88d8e8: sbfx            x0, x1, #1, #0x1f
    // 0x88d8ec: ldur            x2, [fp, #-0x38]
    // 0x88d8f0: cmp             x2, x0
    // 0x88d8f4: b.ne            #0x88d900
    // 0x88d8f8: ldur            x1, [fp, #-0x30]
    // 0x88d8fc: r0 = _growToNextCapacity()
    //     0x88d8fc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x88d900: ldur            x3, [fp, #-0x30]
    // 0x88d904: ldur            x2, [fp, #-0x38]
    // 0x88d908: add             x0, x2, #1
    // 0x88d90c: lsl             x1, x0, #1
    // 0x88d910: StoreField: r3->field_b = r1
    //     0x88d910: stur            w1, [x3, #0xb]
    // 0x88d914: LoadField: r1 = r3->field_f
    //     0x88d914: ldur            w1, [x3, #0xf]
    // 0x88d918: DecompressPointer r1
    //     0x88d918: add             x1, x1, HEAP, lsl #32
    // 0x88d91c: ldur            x0, [fp, #-0x18]
    // 0x88d920: ArrayStore: r1[r2] = r0  ; List_4
    //     0x88d920: add             x25, x1, x2, lsl #2
    //     0x88d924: add             x25, x25, #0xf
    //     0x88d928: str             w0, [x25]
    //     0x88d92c: tbz             w0, #0, #0x88d948
    //     0x88d930: ldurb           w16, [x1, #-1]
    //     0x88d934: ldurb           w17, [x0, #-1]
    //     0x88d938: and             x16, x17, x16, lsr #2
    //     0x88d93c: tst             x16, HEAP, lsr #32
    //     0x88d940: b.eq            #0x88d948
    //     0x88d944: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x88d948: ldur            x0, [fp, #-0x10]
    // 0x88d94c: mov             x4, x3
    // 0x88d950: ldur            x3, [fp, #-8]
    // 0x88d954: ldur            x5, [fp, #-0x28]
    // 0x88d958: ldur            x6, [fp, #-0x20]
    // 0x88d95c: b               #0x88d6d0
    // 0x88d960: mov             x3, x4
    // 0x88d964: r1 = Function '<anonymous closure>': static.
    //     0x88d964: add             x1, PP, #0x26, lsl #12  ; [pp+0x269b0] AnonymousClosure: static (0x88dc78), in [package:petitparser/src/parser/character/optimize.dart] ::optimizedRanges (0x88d64c)
    //     0x88d968: ldr             x1, [x1, #0x9b0]
    // 0x88d96c: r2 = Null
    //     0x88d96c: mov             x2, NULL
    // 0x88d970: r0 = AllocateClosure()
    //     0x88d970: bl              #0xec1630  ; AllocateClosureStub
    // 0x88d974: r16 = <int>
    //     0x88d974: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x88d978: ldur            lr, [fp, #-0x30]
    // 0x88d97c: stp             lr, x16, [SP, #0x10]
    // 0x88d980: stp             x0, xzr, [SP]
    // 0x88d984: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x88d984: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x88d988: r0 = fold()
    //     0x88d988: bl              #0x895b28  ; [dart:collection] ListBase::fold
    // 0x88d98c: r1 = LoadInt32Instr(r0)
    //     0x88d98c: sbfx            x1, x0, #1, #0x1f
    //     0x88d990: tbz             w0, #0, #0x88d998
    //     0x88d994: ldur            x1, [x0, #7]
    // 0x88d998: cbnz            x1, #0x88d9b0
    // 0x88d99c: r0 = Instance_ConstantCharPredicate
    //     0x88d99c: add             x0, PP, #0x26, lsl #12  ; [pp+0x269b8] Obj!ConstantCharPredicate@e0c181
    //     0x88d9a0: ldr             x0, [x0, #0x9b8]
    // 0x88d9a4: LeaveFrame
    //     0x88d9a4: mov             SP, fp
    //     0x88d9a8: ldp             fp, lr, [SP], #0x10
    // 0x88d9ac: ret
    //     0x88d9ac: ret             
    // 0x88d9b0: sub             x0, x1, #1
    // 0x88d9b4: r17 = 65535
    //     0x88d9b4: orr             x17, xzr, #0xffff
    // 0x88d9b8: cmp             x0, x17
    // 0x88d9bc: b.ne            #0x88d9d4
    // 0x88d9c0: r0 = Instance_ConstantCharPredicate
    //     0x88d9c0: add             x0, PP, #0x26, lsl #12  ; [pp+0x269c0] Obj!ConstantCharPredicate@e0c171
    //     0x88d9c4: ldr             x0, [x0, #0x9c0]
    // 0x88d9c8: LeaveFrame
    //     0x88d9c8: mov             SP, fp
    //     0x88d9cc: ldp             fp, lr, [SP], #0x10
    // 0x88d9d0: ret
    //     0x88d9d0: ret             
    // 0x88d9d4: ldur            x2, [fp, #-0x30]
    // 0x88d9d8: LoadField: r0 = r2->field_b
    //     0x88d9d8: ldur            w0, [x2, #0xb]
    // 0x88d9dc: r1 = LoadInt32Instr(r0)
    //     0x88d9dc: sbfx            x1, x0, #1, #0x1f
    // 0x88d9e0: cmp             x1, #1
    // 0x88d9e4: b.ne            #0x88da44
    // 0x88d9e8: mov             x0, x1
    // 0x88d9ec: r1 = 0
    //     0x88d9ec: movz            x1, #0
    // 0x88d9f0: cmp             x1, x0
    // 0x88d9f4: b.hs            #0x88da88
    // 0x88d9f8: LoadField: r0 = r2->field_f
    //     0x88d9f8: ldur            w0, [x2, #0xf]
    // 0x88d9fc: DecompressPointer r0
    //     0x88d9fc: add             x0, x0, HEAP, lsl #32
    // 0x88da00: LoadField: r1 = r0->field_f
    //     0x88da00: ldur            w1, [x0, #0xf]
    // 0x88da04: DecompressPointer r1
    //     0x88da04: add             x1, x1, HEAP, lsl #32
    // 0x88da08: LoadField: r0 = r1->field_7
    //     0x88da08: ldur            x0, [x1, #7]
    // 0x88da0c: stur            x0, [fp, #-0x10]
    // 0x88da10: LoadField: r2 = r1->field_f
    //     0x88da10: ldur            x2, [x1, #0xf]
    // 0x88da14: cmp             x0, x2
    // 0x88da18: b.ne            #0x88da34
    // 0x88da1c: r0 = SingleCharPredicate()
    //     0x88da1c: bl              #0x88b4b8  ; AllocateSingleCharPredicateStub -> SingleCharPredicate (size=0x10)
    // 0x88da20: mov             x1, x0
    // 0x88da24: ldur            x0, [fp, #-0x10]
    // 0x88da28: StoreField: r1->field_7 = r0
    //     0x88da28: stur            x0, [x1, #7]
    // 0x88da2c: mov             x0, x1
    // 0x88da30: b               #0x88da38
    // 0x88da34: mov             x0, x1
    // 0x88da38: LeaveFrame
    //     0x88da38: mov             SP, fp
    //     0x88da3c: ldp             fp, lr, [SP], #0x10
    // 0x88da40: ret
    //     0x88da40: ret             
    // 0x88da44: r0 = LookupCharPredicate()
    //     0x88da44: bl              #0x88dc60  ; AllocateLookupCharPredicateStub -> LookupCharPredicate (size=0x1c)
    // 0x88da48: mov             x1, x0
    // 0x88da4c: ldur            x2, [fp, #-0x30]
    // 0x88da50: stur            x0, [fp, #-8]
    // 0x88da54: r0 = LookupCharPredicate()
    //     0x88da54: bl              #0x88da8c  ; [package:petitparser/src/parser/character/lookup.dart] LookupCharPredicate::LookupCharPredicate
    // 0x88da58: ldur            x0, [fp, #-8]
    // 0x88da5c: LeaveFrame
    //     0x88da5c: mov             SP, fp
    //     0x88da60: ldp             fp, lr, [SP], #0x10
    // 0x88da64: ret
    //     0x88da64: ret             
    // 0x88da68: r0 = noElement()
    //     0x88da68: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0x88da6c: r0 = Throw()
    //     0x88da6c: bl              #0xec04b8  ; ThrowStub
    // 0x88da70: brk             #0
    // 0x88da74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88da74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88da78: b               #0x88d668
    // 0x88da7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88da7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88da80: b               #0x88d6dc
    // 0x88da84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x88da84: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x88da88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x88da88: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static int <anonymous closure>(dynamic, int, RangeCharPredicate) {
    // ** addr: 0x88dc78, size: 0x50
    // 0x88dc78: ldr             x2, [SP]
    // 0x88dc7c: LoadField: r3 = r2->field_f
    //     0x88dc7c: ldur            x3, [x2, #0xf]
    // 0x88dc80: LoadField: r4 = r2->field_7
    //     0x88dc80: ldur            x4, [x2, #7]
    // 0x88dc84: sub             x2, x3, x4
    // 0x88dc88: add             x3, x2, #1
    // 0x88dc8c: ldr             x2, [SP, #8]
    // 0x88dc90: r4 = LoadInt32Instr(r2)
    //     0x88dc90: sbfx            x4, x2, #1, #0x1f
    //     0x88dc94: tbz             w2, #0, #0x88dc9c
    //     0x88dc98: ldur            x4, [x2, #7]
    // 0x88dc9c: add             x2, x4, x3
    // 0x88dca0: r0 = BoxInt64Instr(r2)
    //     0x88dca0: sbfiz           x0, x2, #1, #0x1f
    //     0x88dca4: cmp             x2, x0, asr #1
    //     0x88dca8: b.eq            #0x88dcc4
    //     0x88dcac: stp             fp, lr, [SP, #-0x10]!
    //     0x88dcb0: mov             fp, SP
    //     0x88dcb4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x88dcb8: mov             SP, fp
    //     0x88dcbc: ldp             fp, lr, [SP], #0x10
    //     0x88dcc0: stur            x2, [x0, #7]
    // 0x88dcc4: ret
    //     0x88dcc4: ret             
  }
  [closure] static int <anonymous closure>(dynamic, RangeCharPredicate, RangeCharPredicate) {
    // ** addr: 0x88dce8, size: 0x5c
    // 0x88dce8: ldr             x2, [SP, #8]
    // 0x88dcec: LoadField: r3 = r2->field_7
    //     0x88dcec: ldur            x3, [x2, #7]
    // 0x88dcf0: ldr             x4, [SP]
    // 0x88dcf4: LoadField: r5 = r4->field_7
    //     0x88dcf4: ldur            x5, [x4, #7]
    // 0x88dcf8: cmp             x3, x5
    // 0x88dcfc: b.eq            #0x88dd0c
    // 0x88dd00: sub             x6, x3, x5
    // 0x88dd04: mov             x2, x6
    // 0x88dd08: b               #0x88dd1c
    // 0x88dd0c: LoadField: r3 = r2->field_f
    //     0x88dd0c: ldur            x3, [x2, #0xf]
    // 0x88dd10: LoadField: r2 = r4->field_f
    //     0x88dd10: ldur            x2, [x4, #0xf]
    // 0x88dd14: sub             x4, x3, x2
    // 0x88dd18: mov             x2, x4
    // 0x88dd1c: r0 = BoxInt64Instr(r2)
    //     0x88dd1c: sbfiz           x0, x2, #1, #0x1f
    //     0x88dd20: cmp             x2, x0, asr #1
    //     0x88dd24: b.eq            #0x88dd40
    //     0x88dd28: stp             fp, lr, [SP, #-0x10]!
    //     0x88dd2c: mov             fp, SP
    //     0x88dd30: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x88dd34: mov             SP, fp
    //     0x88dd38: ldp             fp, lr, [SP], #0x10
    //     0x88dd3c: stur            x2, [x0, #7]
    // 0x88dd40: ret
    //     0x88dd40: ret             
  }
}
