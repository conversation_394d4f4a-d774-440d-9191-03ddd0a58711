// lib: , url: package:petitparser/src/parser/character/pattern.dart

// class id: 1050891, size: 0x8
class :: {

  static late final Parser<CharacterPredicate> _pattern; // offset: 0x1718
  static late final Parser<CharacterPredicate> _sequence; // offset: 0x1714
  static late final Parser<RangeCharPredicate> _range; // offset: 0x1710
  static late final Parser<RangeCharPredicate> _single; // offset: 0x170c

  static _ pattern(/* No info */) {
    // ** addr: 0x88d0b4, size: 0x120
    // 0x88d0b4: EnterFrame
    //     0x88d0b4: stp             fp, lr, [SP, #-0x10]!
    //     0x88d0b8: mov             fp, SP
    // 0x88d0bc: AllocStack(0x28)
    //     0x88d0bc: sub             SP, SP, #0x28
    // 0x88d0c0: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x88d0c0: mov             x2, x1
    //     0x88d0c4: stur            x1, [fp, #-8]
    // 0x88d0c8: CheckStackOverflow
    //     0x88d0c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d0cc: cmp             SP, x16
    //     0x88d0d0: b.ls            #0x88d1cc
    // 0x88d0d4: r0 = InitLateStaticField(0x1718) // [package:petitparser/src/parser/character/pattern.dart] ::_pattern
    //     0x88d0d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x88d0d8: ldr             x0, [x0, #0x2e30]
    //     0x88d0dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x88d0e0: cmp             w0, w16
    //     0x88d0e4: b.ne            #0x88d0f4
    //     0x88d0e8: add             x2, PP, #0x26, lsl #12  ; [pp+0x268a8] Field <::._pattern@2633113086>: static late final (offset: 0x1718)
    //     0x88d0ec: ldr             x2, [x2, #0x8a8]
    //     0x88d0f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x88d0f4: mov             x1, x0
    // 0x88d0f8: ldur            x2, [fp, #-8]
    // 0x88d0fc: r0 = parse()
    //     0x88d0fc: bl              #0x88d1d4  ; [package:petitparser/src/core/parser.dart] Parser::parse
    // 0x88d100: stur            x0, [fp, #-0x20]
    // 0x88d104: r1 = LoadClassIdInstr(r0)
    //     0x88d104: ldur            x1, [x0, #-1]
    //     0x88d108: ubfx            x1, x1, #0xc, #0x14
    // 0x88d10c: cmp             x1, #0x2f3
    // 0x88d110: b.eq            #0x88d1b0
    // 0x88d114: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x88d114: ldur            w3, [x0, #0x17]
    // 0x88d118: DecompressPointer r3
    //     0x88d118: add             x3, x3, HEAP, lsl #32
    // 0x88d11c: stur            x3, [fp, #-0x10]
    // 0x88d120: r1 = Null
    //     0x88d120: mov             x1, NULL
    // 0x88d124: r2 = 6
    //     0x88d124: movz            x2, #0x6
    // 0x88d128: r0 = AllocateArray()
    //     0x88d128: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88d12c: stur            x0, [fp, #-0x18]
    // 0x88d130: r16 = "["
    //     0x88d130: ldr             x16, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0x88d134: StoreField: r0->field_f = r16
    //     0x88d134: stur            w16, [x0, #0xf]
    // 0x88d138: ldur            x1, [fp, #-8]
    // 0x88d13c: r0 = toReadableString()
    //     0x88d13c: bl              #0x88b234  ; [package:petitparser/src/parser/character/code.dart] ::toReadableString
    // 0x88d140: ldur            x1, [fp, #-0x18]
    // 0x88d144: ArrayStore: r1[1] = r0  ; List_4
    //     0x88d144: add             x25, x1, #0x13
    //     0x88d148: str             w0, [x25]
    //     0x88d14c: tbz             w0, #0, #0x88d168
    //     0x88d150: ldurb           w16, [x1, #-1]
    //     0x88d154: ldurb           w17, [x0, #-1]
    //     0x88d158: and             x16, x17, x16, lsr #2
    //     0x88d15c: tst             x16, HEAP, lsr #32
    //     0x88d160: b.eq            #0x88d168
    //     0x88d164: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x88d168: ldur            x0, [fp, #-0x18]
    // 0x88d16c: r16 = "] expected"
    //     0x88d16c: add             x16, PP, #0x26, lsl #12  ; [pp+0x268b0] "] expected"
    //     0x88d170: ldr             x16, [x16, #0x8b0]
    // 0x88d174: ArrayStore: r0[0] = r16  ; List_4
    //     0x88d174: stur            w16, [x0, #0x17]
    // 0x88d178: str             x0, [SP]
    // 0x88d17c: r0 = _interpolate()
    //     0x88d17c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x88d180: r1 = <String>
    //     0x88d180: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88d184: stur            x0, [fp, #-8]
    // 0x88d188: r0 = SingleCharacterParser()
    //     0x88d188: bl              #0x88b228  ; AllocateSingleCharacterParserStub -> SingleCharacterParser (size=0x14)
    // 0x88d18c: mov             x1, x0
    // 0x88d190: ldur            x0, [fp, #-0x10]
    // 0x88d194: StoreField: r1->field_b = r0
    //     0x88d194: stur            w0, [x1, #0xb]
    // 0x88d198: ldur            x0, [fp, #-8]
    // 0x88d19c: StoreField: r1->field_f = r0
    //     0x88d19c: stur            w0, [x1, #0xf]
    // 0x88d1a0: mov             x0, x1
    // 0x88d1a4: LeaveFrame
    //     0x88d1a4: mov             SP, fp
    //     0x88d1a8: ldp             fp, lr, [SP], #0x10
    // 0x88d1ac: ret
    //     0x88d1ac: ret             
    // 0x88d1b0: r0 = ParserException()
    //     0x88d1b0: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0x88d1b4: mov             x1, x0
    // 0x88d1b8: ldur            x0, [fp, #-0x20]
    // 0x88d1bc: StoreField: r1->field_7 = r0
    //     0x88d1bc: stur            w0, [x1, #7]
    // 0x88d1c0: mov             x0, x1
    // 0x88d1c4: r0 = Throw()
    //     0x88d1c4: bl              #0xec04b8  ; ThrowStub
    // 0x88d1c8: brk             #0
    // 0x88d1cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88d1cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88d1d0: b               #0x88d0d4
  }
  static Parser<CharacterPredicate> _pattern() {
    // ** addr: 0x88d228, size: 0xbc
    // 0x88d228: EnterFrame
    //     0x88d228: stp             fp, lr, [SP, #-0x10]!
    //     0x88d22c: mov             fp, SP
    // 0x88d230: AllocStack(0x20)
    //     0x88d230: sub             SP, SP, #0x20
    // 0x88d234: CheckStackOverflow
    //     0x88d234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d238: cmp             SP, x16
    //     0x88d23c: b.ls            #0x88d2dc
    // 0x88d240: r1 = "^"
    //     0x88d240: add             x1, PP, #0x18, lsl #12  ; [pp+0x18118] "^"
    //     0x88d244: ldr             x1, [x1, #0x118]
    // 0x88d248: r0 = char()
    //     0x88d248: bl              #0x88b140  ; [package:petitparser/src/parser/character/char.dart] ::char
    // 0x88d24c: r16 = <String>
    //     0x88d24c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88d250: stp             x0, x16, [SP]
    // 0x88d254: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88d254: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88d258: r0 = OptionalParserExtension.optional()
    //     0x88d258: bl              #0x88ad30  ; [package:petitparser/src/parser/combinator/optional.dart] ::OptionalParserExtension.optional
    // 0x88d25c: stur            x0, [fp, #-8]
    // 0x88d260: r0 = InitLateStaticField(0x1714) // [package:petitparser/src/parser/character/pattern.dart] ::_sequence
    //     0x88d260: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x88d264: ldr             x0, [x0, #0x2e28]
    //     0x88d268: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x88d26c: cmp             w0, w16
    //     0x88d270: b.ne            #0x88d280
    //     0x88d274: add             x2, PP, #0x26, lsl #12  ; [pp+0x26900] Field <::._sequence@2633113086>: static late final (offset: 0x1714)
    //     0x88d278: ldr             x2, [x2, #0x900]
    //     0x88d27c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x88d280: ldur            x2, [fp, #-8]
    // 0x88d284: mov             x3, x0
    // 0x88d288: r0 = AllocateRecord2()
    //     0x88d288: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x88d28c: r16 = <String?, CharacterPredicate>
    //     0x88d28c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26908] TypeArguments: <String?, CharacterPredicate>
    //     0x88d290: ldr             x16, [x16, #0x908]
    // 0x88d294: stp             x0, x16, [SP]
    // 0x88d298: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x88d298: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x88d29c: r0 = RecordOfParsersExtension2.toSequenceParser()
    //     0x88d29c: bl              #0x88d408  ; [package:petitparser/src/parser/combinator/generated/sequence_2.dart] ::RecordOfParsersExtension2.toSequenceParser
    // 0x88d2a0: r1 = Function '<anonymous closure>': static.
    //     0x88d2a0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26910] AnonymousClosure: static (0x88d48c), in [package:petitparser/src/parser/character/pattern.dart] ::_pattern (0x88d228)
    //     0x88d2a4: ldr             x1, [x1, #0x910]
    // 0x88d2a8: r2 = Null
    //     0x88d2a8: mov             x2, NULL
    // 0x88d2ac: stur            x0, [fp, #-8]
    // 0x88d2b0: r0 = AllocateClosure()
    //     0x88d2b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x88d2b4: r16 = <String?, CharacterPredicate, CharacterPredicate>
    //     0x88d2b4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26918] TypeArguments: <String?, CharacterPredicate, CharacterPredicate>
    //     0x88d2b8: ldr             x16, [x16, #0x918]
    // 0x88d2bc: ldur            lr, [fp, #-8]
    // 0x88d2c0: stp             lr, x16, [SP, #8]
    // 0x88d2c4: str             x0, [SP]
    // 0x88d2c8: r4 = const [0x3, 0x2, 0x2, 0x2, null]
    //     0x88d2c8: ldr             x4, [PP, #0x1930]  ; [pp+0x1930] List(5) [0x3, 0x2, 0x2, 0x2, Null]
    // 0x88d2cc: r0 = RecordParserExtension2.map2()
    //     0x88d2cc: bl              #0x88d2e4  ; [package:petitparser/src/parser/combinator/generated/sequence_2.dart] ::RecordParserExtension2.map2
    // 0x88d2d0: LeaveFrame
    //     0x88d2d0: mov             SP, fp
    //     0x88d2d4: ldp             fp, lr, [SP], #0x10
    // 0x88d2d8: ret
    //     0x88d2d8: ret             
    // 0x88d2dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88d2dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88d2e0: b               #0x88d240
  }
  [closure] static CharacterPredicate <anonymous closure>(dynamic, String?, CharacterPredicate) {
    // ** addr: 0x88d48c, size: 0x78
    // 0x88d48c: EnterFrame
    //     0x88d48c: stp             fp, lr, [SP, #-0x10]!
    //     0x88d490: mov             fp, SP
    // 0x88d494: AllocStack(0x8)
    //     0x88d494: sub             SP, SP, #8
    // 0x88d498: ldr             x0, [fp, #0x18]
    // 0x88d49c: cmp             w0, NULL
    // 0x88d4a0: b.ne            #0x88d4ac
    // 0x88d4a4: ldr             x0, [fp, #0x10]
    // 0x88d4a8: b               #0x88d4f8
    // 0x88d4ac: ldr             x0, [fp, #0x10]
    // 0x88d4b0: r1 = LoadClassIdInstr(r0)
    //     0x88d4b0: ldur            x1, [x0, #-1]
    //     0x88d4b4: ubfx            x1, x1, #0xc, #0x14
    // 0x88d4b8: cmp             x1, #0x2d1
    // 0x88d4bc: b.ne            #0x88d4e4
    // 0x88d4c0: LoadField: r1 = r0->field_7
    //     0x88d4c0: ldur            w1, [x0, #7]
    // 0x88d4c4: DecompressPointer r1
    //     0x88d4c4: add             x1, x1, HEAP, lsl #32
    // 0x88d4c8: eor             x0, x1, #0x10
    // 0x88d4cc: stur            x0, [fp, #-8]
    // 0x88d4d0: r0 = ConstantCharPredicate()
    //     0x88d4d0: bl              #0x88d510  ; AllocateConstantCharPredicateStub -> ConstantCharPredicate (size=0xc)
    // 0x88d4d4: mov             x1, x0
    // 0x88d4d8: ldur            x0, [fp, #-8]
    // 0x88d4dc: StoreField: r1->field_7 = r0
    //     0x88d4dc: stur            w0, [x1, #7]
    // 0x88d4e0: b               #0x88d4f4
    // 0x88d4e4: r0 = NotCharacterPredicate()
    //     0x88d4e4: bl              #0x88d504  ; AllocateNotCharacterPredicateStub -> NotCharacterPredicate (size=0xc)
    // 0x88d4e8: ldr             x1, [fp, #0x10]
    // 0x88d4ec: StoreField: r0->field_7 = r1
    //     0x88d4ec: stur            w1, [x0, #7]
    // 0x88d4f0: mov             x1, x0
    // 0x88d4f4: mov             x0, x1
    // 0x88d4f8: LeaveFrame
    //     0x88d4f8: mov             SP, fp
    //     0x88d4fc: ldp             fp, lr, [SP], #0x10
    // 0x88d500: ret
    //     0x88d500: ret             
  }
  static Parser<CharacterPredicate> _sequence() {
    // ** addr: 0x88d51c, size: 0x100
    // 0x88d51c: EnterFrame
    //     0x88d51c: stp             fp, lr, [SP, #-0x10]!
    //     0x88d520: mov             fp, SP
    // 0x88d524: AllocStack(0x30)
    //     0x88d524: sub             SP, SP, #0x30
    // 0x88d528: CheckStackOverflow
    //     0x88d528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d52c: cmp             SP, x16
    //     0x88d530: b.ls            #0x88d614
    // 0x88d534: r0 = InitLateStaticField(0x1710) // [package:petitparser/src/parser/character/pattern.dart] ::_range
    //     0x88d534: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x88d538: ldr             x0, [x0, #0x2e20]
    //     0x88d53c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x88d540: cmp             w0, w16
    //     0x88d544: b.ne            #0x88d554
    //     0x88d548: add             x2, PP, #0x26, lsl #12  ; [pp+0x26940] Field <::._range@2633113086>: static late final (offset: 0x1710)
    //     0x88d54c: ldr             x2, [x2, #0x940]
    //     0x88d550: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x88d554: stur            x0, [fp, #-8]
    // 0x88d558: r0 = InitLateStaticField(0x170c) // [package:petitparser/src/parser/character/pattern.dart] ::_single
    //     0x88d558: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x88d55c: ldr             x0, [x0, #0x2e18]
    //     0x88d560: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x88d564: cmp             w0, w16
    //     0x88d568: b.ne            #0x88d578
    //     0x88d56c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26948] Field <::._single@2633113086>: static late final (offset: 0x170c)
    //     0x88d570: ldr             x2, [x2, #0x948]
    //     0x88d574: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x88d578: r1 = Null
    //     0x88d578: mov             x1, NULL
    // 0x88d57c: r2 = 4
    //     0x88d57c: movz            x2, #0x4
    // 0x88d580: stur            x0, [fp, #-0x10]
    // 0x88d584: r0 = AllocateArray()
    //     0x88d584: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88d588: mov             x2, x0
    // 0x88d58c: ldur            x0, [fp, #-8]
    // 0x88d590: stur            x2, [fp, #-0x18]
    // 0x88d594: StoreField: r2->field_f = r0
    //     0x88d594: stur            w0, [x2, #0xf]
    // 0x88d598: ldur            x0, [fp, #-0x10]
    // 0x88d59c: StoreField: r2->field_13 = r0
    //     0x88d59c: stur            w0, [x2, #0x13]
    // 0x88d5a0: r1 = <Parser<RangeCharPredicate>>
    //     0x88d5a0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26950] TypeArguments: <Parser<RangeCharPredicate>>
    //     0x88d5a4: ldr             x1, [x1, #0x950]
    // 0x88d5a8: r0 = AllocateGrowableArray()
    //     0x88d5a8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88d5ac: mov             x1, x0
    // 0x88d5b0: ldur            x0, [fp, #-0x18]
    // 0x88d5b4: StoreField: r1->field_f = r0
    //     0x88d5b4: stur            w0, [x1, #0xf]
    // 0x88d5b8: r0 = 4
    //     0x88d5b8: movz            x0, #0x4
    // 0x88d5bc: StoreField: r1->field_b = r0
    //     0x88d5bc: stur            w0, [x1, #0xb]
    // 0x88d5c0: r16 = <RangeCharPredicate>
    //     0x88d5c0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26958] TypeArguments: <RangeCharPredicate>
    //     0x88d5c4: ldr             x16, [x16, #0x958]
    // 0x88d5c8: stp             x1, x16, [SP]
    // 0x88d5cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88d5cc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88d5d0: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88d5d0: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88d5d4: r16 = <RangeCharPredicate>
    //     0x88d5d4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26958] TypeArguments: <RangeCharPredicate>
    //     0x88d5d8: ldr             x16, [x16, #0x958]
    // 0x88d5dc: stp             x0, x16, [SP]
    // 0x88d5e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88d5e0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88d5e4: r0 = PossessiveRepeatingParserExtension.star()
    //     0x88d5e4: bl              #0x88cfa0  ; [package:petitparser/src/parser/repeater/possessive.dart] ::PossessiveRepeatingParserExtension.star
    // 0x88d5e8: r16 = <List<RangeCharPredicate>, CharacterPredicate>
    //     0x88d5e8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26960] TypeArguments: <List<RangeCharPredicate>, CharacterPredicate>
    //     0x88d5ec: ldr             x16, [x16, #0x960]
    // 0x88d5f0: stp             x0, x16, [SP, #8]
    // 0x88d5f4: r16 = Closure: (Iterable<RangeCharPredicate>) => CharacterPredicate from Function 'optimizedRanges': static.
    //     0x88d5f4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26968] Closure: (Iterable<RangeCharPredicate>) => CharacterPredicate from Function 'optimizedRanges': static. (0x7e54fb28d61c)
    //     0x88d5f8: ldr             x16, [x16, #0x968]
    // 0x88d5fc: str             x16, [SP]
    // 0x88d600: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88d600: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88d604: r0 = MapParserExtension.map()
    //     0x88d604: bl              #0x88ab3c  ; [package:petitparser/src/parser/action/map.dart] ::MapParserExtension.map
    // 0x88d608: LeaveFrame
    //     0x88d608: mov             SP, fp
    //     0x88d60c: ldp             fp, lr, [SP], #0x10
    // 0x88d610: ret
    //     0x88d610: ret             
    // 0x88d614: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88d614: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88d618: b               #0x88d534
  }
  static Parser<RangeCharPredicate> _single() {
    // ** addr: 0x88ddb8, size: 0x60
    // 0x88ddb8: EnterFrame
    //     0x88ddb8: stp             fp, lr, [SP, #-0x10]!
    //     0x88ddbc: mov             fp, SP
    // 0x88ddc0: AllocStack(0x20)
    //     0x88ddc0: sub             SP, SP, #0x20
    // 0x88ddc4: CheckStackOverflow
    //     0x88ddc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88ddc8: cmp             SP, x16
    //     0x88ddcc: b.ls            #0x88de10
    // 0x88ddd0: r0 = any()
    //     0x88ddd0: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88ddd4: r1 = Function '<anonymous closure>': static.
    //     0x88ddd4: add             x1, PP, #0x26, lsl #12  ; [pp+0x269d0] AnonymousClosure: static (0x88de18), in [package:petitparser/src/parser/character/pattern.dart] ::_single (0x88ddb8)
    //     0x88ddd8: ldr             x1, [x1, #0x9d0]
    // 0x88dddc: r2 = Null
    //     0x88dddc: mov             x2, NULL
    // 0x88dde0: stur            x0, [fp, #-8]
    // 0x88dde4: r0 = AllocateClosure()
    //     0x88dde4: bl              #0xec1630  ; AllocateClosureStub
    // 0x88dde8: r16 = <String, RangeCharPredicate>
    //     0x88dde8: add             x16, PP, #0x26, lsl #12  ; [pp+0x269d8] TypeArguments: <String, RangeCharPredicate>
    //     0x88ddec: ldr             x16, [x16, #0x9d8]
    // 0x88ddf0: ldur            lr, [fp, #-8]
    // 0x88ddf4: stp             lr, x16, [SP, #8]
    // 0x88ddf8: str             x0, [SP]
    // 0x88ddfc: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88ddfc: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88de00: r0 = MapParserExtension.map()
    //     0x88de00: bl              #0x88ab3c  ; [package:petitparser/src/parser/action/map.dart] ::MapParserExtension.map
    // 0x88de04: LeaveFrame
    //     0x88de04: mov             SP, fp
    //     0x88de08: ldp             fp, lr, [SP], #0x10
    // 0x88de0c: ret
    //     0x88de0c: ret             
    // 0x88de10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88de10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88de14: b               #0x88ddd0
  }
  [closure] static RangeCharPredicate <anonymous closure>(dynamic, String) {
    // ** addr: 0x88de18, size: 0x88
    // 0x88de18: EnterFrame
    //     0x88de18: stp             fp, lr, [SP, #-0x10]!
    //     0x88de1c: mov             fp, SP
    // 0x88de20: AllocStack(0x10)
    //     0x88de20: sub             SP, SP, #0x10
    // 0x88de24: ldr             x2, [fp, #0x10]
    // 0x88de28: LoadField: r0 = r2->field_7
    //     0x88de28: ldur            w0, [x2, #7]
    // 0x88de2c: r1 = LoadInt32Instr(r0)
    //     0x88de2c: sbfx            x1, x0, #1, #0x1f
    // 0x88de30: mov             x0, x1
    // 0x88de34: r1 = 0
    //     0x88de34: movz            x1, #0
    // 0x88de38: cmp             x1, x0
    // 0x88de3c: b.hs            #0x88de9c
    // 0x88de40: r0 = LoadClassIdInstr(r2)
    //     0x88de40: ldur            x0, [x2, #-1]
    //     0x88de44: ubfx            x0, x0, #0xc, #0x14
    // 0x88de48: lsl             x0, x0, #1
    // 0x88de4c: cmp             w0, #0xbc
    // 0x88de50: b.ne            #0x88de5c
    // 0x88de54: ArrayLoad: r1 = r2[-8]  ; TypedUnsigned_1
    //     0x88de54: ldrb            w1, [x2, #0xf]
    // 0x88de58: b               #0x88de60
    // 0x88de5c: ldurh           w1, [x2, #0xf]
    // 0x88de60: stur            x1, [fp, #-0x10]
    // 0x88de64: cmp             w0, #0xbc
    // 0x88de68: b.ne            #0x88de74
    // 0x88de6c: ArrayLoad: r0 = r2[-8]  ; TypedUnsigned_1
    //     0x88de6c: ldrb            w0, [x2, #0xf]
    // 0x88de70: b               #0x88de78
    // 0x88de74: ldurh           w0, [x2, #0xf]
    // 0x88de78: stur            x0, [fp, #-8]
    // 0x88de7c: r0 = RangeCharPredicate()
    //     0x88de7c: bl              #0x88dc6c  ; AllocateRangeCharPredicateStub -> RangeCharPredicate (size=0x18)
    // 0x88de80: ldur            x1, [fp, #-0x10]
    // 0x88de84: StoreField: r0->field_7 = r1
    //     0x88de84: stur            x1, [x0, #7]
    // 0x88de88: ldur            x1, [fp, #-8]
    // 0x88de8c: StoreField: r0->field_f = r1
    //     0x88de8c: stur            x1, [x0, #0xf]
    // 0x88de90: LeaveFrame
    //     0x88de90: mov             SP, fp
    //     0x88de94: ldp             fp, lr, [SP], #0x10
    // 0x88de98: ret
    //     0x88de98: ret             
    // 0x88de9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x88de9c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static Parser<RangeCharPredicate> _range() {
    // ** addr: 0x88dea0, size: 0xa0
    // 0x88dea0: EnterFrame
    //     0x88dea0: stp             fp, lr, [SP, #-0x10]!
    //     0x88dea4: mov             fp, SP
    // 0x88dea8: AllocStack(0x28)
    //     0x88dea8: sub             SP, SP, #0x28
    // 0x88deac: CheckStackOverflow
    //     0x88deac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88deb0: cmp             SP, x16
    //     0x88deb4: b.ls            #0x88df38
    // 0x88deb8: r0 = any()
    //     0x88deb8: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88debc: r1 = "-"
    //     0x88debc: ldr             x1, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0x88dec0: stur            x0, [fp, #-8]
    // 0x88dec4: r0 = char()
    //     0x88dec4: bl              #0x88b140  ; [package:petitparser/src/parser/character/char.dart] ::char
    // 0x88dec8: stur            x0, [fp, #-0x10]
    // 0x88decc: r0 = any()
    //     0x88decc: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88ded0: ldur            x2, [fp, #-8]
    // 0x88ded4: ldur            x3, [fp, #-0x10]
    // 0x88ded8: mov             x4, x0
    // 0x88dedc: r0 = AllocateRecord3()
    //     0x88dedc: bl              #0xec0e44  ; AllocateRecord3Stub
    // 0x88dee0: r16 = <String, String, String>
    //     0x88dee0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10810] TypeArguments: <String, String, String>
    //     0x88dee4: ldr             x16, [x16, #0x810]
    // 0x88dee8: stp             x0, x16, [SP]
    // 0x88deec: r4 = const [0x3, 0x1, 0x1, 0x1, null]
    //     0x88deec: add             x4, PP, #0x26, lsl #12  ; [pp+0x269e0] List(5) [0x3, 0x1, 0x1, 0x1, Null]
    //     0x88def0: ldr             x4, [x4, #0x9e0]
    // 0x88def4: r0 = RecordOfParsersExtension3.toSequenceParser()
    //     0x88def4: bl              #0x88df40  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordOfParsersExtension3.toSequenceParser
    // 0x88def8: r1 = Function '<anonymous closure>': static.
    //     0x88def8: add             x1, PP, #0x26, lsl #12  ; [pp+0x269e8] AnonymousClosure: static (0x88dfd8), in [package:petitparser/src/parser/character/pattern.dart] ::_range (0x88dea0)
    //     0x88defc: ldr             x1, [x1, #0x9e8]
    // 0x88df00: r2 = Null
    //     0x88df00: mov             x2, NULL
    // 0x88df04: stur            x0, [fp, #-8]
    // 0x88df08: r0 = AllocateClosure()
    //     0x88df08: bl              #0xec1630  ; AllocateClosureStub
    // 0x88df0c: r16 = <String, String, String, RangeCharPredicate>
    //     0x88df0c: add             x16, PP, #0x26, lsl #12  ; [pp+0x269f0] TypeArguments: <String, String, String, RangeCharPredicate>
    //     0x88df10: ldr             x16, [x16, #0x9f0]
    // 0x88df14: ldur            lr, [fp, #-8]
    // 0x88df18: stp             lr, x16, [SP, #8]
    // 0x88df1c: str             x0, [SP]
    // 0x88df20: r4 = const [0x4, 0x2, 0x2, 0x2, null]
    //     0x88df20: add             x4, PP, #0x26, lsl #12  ; [pp+0x26718] List(5) [0x4, 0x2, 0x2, 0x2, Null]
    //     0x88df24: ldr             x4, [x4, #0x718]
    // 0x88df28: r0 = RecordParserExtension3.map3()
    //     0x88df28: bl              #0x88b790  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordParserExtension3.map3
    // 0x88df2c: LeaveFrame
    //     0x88df2c: mov             SP, fp
    //     0x88df30: ldp             fp, lr, [SP], #0x10
    // 0x88df34: ret
    //     0x88df34: ret             
    // 0x88df38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88df38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88df3c: b               #0x88deb8
  }
  [closure] static RangeCharPredicate <anonymous closure>(dynamic, String, String, String) {
    // ** addr: 0x88dfd8, size: 0xbc
    // 0x88dfd8: EnterFrame
    //     0x88dfd8: stp             fp, lr, [SP, #-0x10]!
    //     0x88dfdc: mov             fp, SP
    // 0x88dfe0: AllocStack(0x10)
    //     0x88dfe0: sub             SP, SP, #0x10
    // 0x88dfe4: ldr             x2, [fp, #0x20]
    // 0x88dfe8: LoadField: r0 = r2->field_7
    //     0x88dfe8: ldur            w0, [x2, #7]
    // 0x88dfec: r1 = LoadInt32Instr(r0)
    //     0x88dfec: sbfx            x1, x0, #1, #0x1f
    // 0x88dff0: mov             x0, x1
    // 0x88dff4: r1 = 0
    //     0x88dff4: movz            x1, #0
    // 0x88dff8: cmp             x1, x0
    // 0x88dffc: b.hs            #0x88e08c
    // 0x88e000: r0 = LoadClassIdInstr(r2)
    //     0x88e000: ldur            x0, [x2, #-1]
    //     0x88e004: ubfx            x0, x0, #0xc, #0x14
    // 0x88e008: lsl             x0, x0, #1
    // 0x88e00c: cmp             w0, #0xbc
    // 0x88e010: b.ne            #0x88e020
    // 0x88e014: ArrayLoad: r0 = r2[-8]  ; TypedUnsigned_1
    //     0x88e014: ldrb            w0, [x2, #0xf]
    // 0x88e018: mov             x3, x0
    // 0x88e01c: b               #0x88e028
    // 0x88e020: ldurh           w0, [x2, #0xf]
    // 0x88e024: mov             x3, x0
    // 0x88e028: ldr             x2, [fp, #0x10]
    // 0x88e02c: stur            x3, [fp, #-0x10]
    // 0x88e030: LoadField: r0 = r2->field_7
    //     0x88e030: ldur            w0, [x2, #7]
    // 0x88e034: r1 = LoadInt32Instr(r0)
    //     0x88e034: sbfx            x1, x0, #1, #0x1f
    // 0x88e038: mov             x0, x1
    // 0x88e03c: r1 = 0
    //     0x88e03c: movz            x1, #0
    // 0x88e040: cmp             x1, x0
    // 0x88e044: b.hs            #0x88e090
    // 0x88e048: r0 = LoadClassIdInstr(r2)
    //     0x88e048: ldur            x0, [x2, #-1]
    //     0x88e04c: ubfx            x0, x0, #0xc, #0x14
    // 0x88e050: lsl             x0, x0, #1
    // 0x88e054: cmp             w0, #0xbc
    // 0x88e058: b.ne            #0x88e064
    // 0x88e05c: ArrayLoad: r0 = r2[-8]  ; TypedUnsigned_1
    //     0x88e05c: ldrb            w0, [x2, #0xf]
    // 0x88e060: b               #0x88e068
    // 0x88e064: ldurh           w0, [x2, #0xf]
    // 0x88e068: stur            x0, [fp, #-8]
    // 0x88e06c: r0 = RangeCharPredicate()
    //     0x88e06c: bl              #0x88dc6c  ; AllocateRangeCharPredicateStub -> RangeCharPredicate (size=0x18)
    // 0x88e070: ldur            x1, [fp, #-0x10]
    // 0x88e074: StoreField: r0->field_7 = r1
    //     0x88e074: stur            x1, [x0, #7]
    // 0x88e078: ldur            x1, [fp, #-8]
    // 0x88e07c: StoreField: r0->field_f = r1
    //     0x88e07c: stur            x1, [x0, #0xf]
    // 0x88e080: LeaveFrame
    //     0x88e080: mov             SP, fp
    //     0x88e084: ldp             fp, lr, [SP], #0x10
    // 0x88e088: ret
    //     0x88e088: ret             
    // 0x88e08c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x88e08c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x88e090: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x88e090: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
