// lib: , url: package:petitparser/src/parser/character/range.dart

// class id: 1050893, size: 0x8
class :: {
}

// class id: 717, size: 0x18, field offset: 0x8
class RangeCharPredicate extends Object
    implements CharacterPredicate {

  _ test(/* No info */) {
    // ** addr: 0xeb3918, size: 0x30
    // 0xeb3918: LoadField: r3 = r1->field_7
    //     0xeb3918: ldur            x3, [x1, #7]
    // 0xeb391c: cmp             x3, x2
    // 0xeb3920: b.gt            #0xeb3940
    // 0xeb3924: LoadField: r3 = r1->field_f
    //     0xeb3924: ldur            x3, [x1, #0xf]
    // 0xeb3928: cmp             x2, x3
    // 0xeb392c: r16 = true
    //     0xeb392c: add             x16, NULL, #0x20  ; true
    // 0xeb3930: r17 = false
    //     0xeb3930: add             x17, NULL, #0x30  ; false
    // 0xeb3934: csel            x1, x16, x17, le
    // 0xeb3938: mov             x0, x1
    // 0xeb393c: b               #0xeb3944
    // 0xeb3940: r0 = false
    //     0xeb3940: add             x0, NULL, #0x30  ; false
    // 0xeb3944: ret
    //     0xeb3944: ret             
  }
}
