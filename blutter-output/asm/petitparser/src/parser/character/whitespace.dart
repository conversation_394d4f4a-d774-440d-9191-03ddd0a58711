// lib: , url: package:petitparser/src/parser/character/whitespace.dart

// class id: 1050894, size: 0x8
class :: {

  static Parser<String> whitespace() {
    // ** addr: 0x88c754, size: 0x34
    // 0x88c754: EnterFrame
    //     0x88c754: stp             fp, lr, [SP, #-0x10]!
    //     0x88c758: mov             fp, SP
    // 0x88c75c: r1 = <String>
    //     0x88c75c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88c760: r0 = SingleCharacterParser()
    //     0x88c760: bl              #0x88b228  ; AllocateSingleCharacterParserStub -> SingleCharacterParser (size=0x14)
    // 0x88c764: r1 = Instance_WhitespaceCharPredicate
    //     0x88c764: add             x1, PP, #0x26, lsl #12  ; [pp+0x267f8] Obj!WhitespaceCharPredicate@e0c161
    //     0x88c768: ldr             x1, [x1, #0x7f8]
    // 0x88c76c: StoreField: r0->field_b = r1
    //     0x88c76c: stur            w1, [x0, #0xb]
    // 0x88c770: r1 = "whitespace expected"
    //     0x88c770: add             x1, PP, #0x26, lsl #12  ; [pp+0x267f0] "whitespace expected"
    //     0x88c774: ldr             x1, [x1, #0x7f0]
    // 0x88c778: StoreField: r0->field_f = r1
    //     0x88c778: stur            w1, [x0, #0xf]
    // 0x88c77c: LeaveFrame
    //     0x88c77c: mov             SP, fp
    //     0x88c780: ldp             fp, lr, [SP], #0x10
    // 0x88c784: ret
    //     0x88c784: ret             
  }
}

// class id: 716, size: 0x8, field offset: 0x8
//   const constructor, 
class WhitespaceCharPredicate extends Object
    implements CharacterPredicate {

  _ test(/* No info */) {
    // ** addr: 0xeb3948, size: 0x174
    // 0xeb3948: cmp             x2, #0x100
    // 0xeb394c: b.ge            #0xeb39c8
    // 0xeb3950: cmp             x2, #0xc
    // 0xeb3954: b.gt            #0xeb3978
    // 0xeb3958: cmp             x2, #0xa
    // 0xeb395c: b.gt            #0xeb39b8
    // 0xeb3960: cmp             x2, #9
    // 0xeb3964: b.gt            #0xeb39b8
    // 0xeb3968: lsl             x1, x2, #1
    // 0xeb396c: cmp             w1, #0x12
    // 0xeb3970: b.ne            #0xeb39c0
    // 0xeb3974: b               #0xeb39b8
    // 0xeb3978: cmp             x2, #0x20
    // 0xeb397c: b.gt            #0xeb3994
    // 0xeb3980: cmp             x2, #0xd
    // 0xeb3984: b.le            #0xeb39b8
    // 0xeb3988: cmp             x2, #0x20
    // 0xeb398c: b.lt            #0xeb39c0
    // 0xeb3990: b               #0xeb39b8
    // 0xeb3994: cmp             x2, #0x85
    // 0xeb3998: b.lt            #0xeb39c0
    // 0xeb399c: cmp             x2, #0x85
    // 0xeb39a0: b.le            #0xeb39b8
    // 0xeb39a4: cmp             x2, #0xa0
    // 0xeb39a8: b.lt            #0xeb39c0
    // 0xeb39ac: lsl             x1, x2, #1
    // 0xeb39b0: cmp             w1, #0x140
    // 0xeb39b4: b.ne            #0xeb39c0
    // 0xeb39b8: r0 = true
    //     0xeb39b8: add             x0, NULL, #0x20  ; true
    // 0xeb39bc: ret
    //     0xeb39bc: ret             
    // 0xeb39c0: r0 = false
    //     0xeb39c0: add             x0, NULL, #0x30  ; false
    // 0xeb39c4: ret
    //     0xeb39c4: ret             
    // 0xeb39c8: r17 = 8199
    //     0xeb39c8: movz            x17, #0x2007
    // 0xeb39cc: cmp             x2, x17
    // 0xeb39d0: b.gt            #0xeb3a20
    // 0xeb39d4: r17 = 8195
    //     0xeb39d4: movz            x17, #0x2003
    // 0xeb39d8: cmp             x2, x17
    // 0xeb39dc: b.gt            #0xeb3aac
    // 0xeb39e0: r17 = 8193
    //     0xeb39e0: movz            x17, #0x2001
    // 0xeb39e4: cmp             x2, x17
    // 0xeb39e8: b.gt            #0xeb3aac
    // 0xeb39ec: cmp             x2, #2, lsl #12
    // 0xeb39f0: b.gt            #0xeb3aac
    // 0xeb39f4: r17 = 5760
    //     0xeb39f4: movz            x17, #0x1680
    // 0xeb39f8: cmp             x2, x17
    // 0xeb39fc: b.gt            #0xeb3a14
    // 0xeb3a00: lsl             x1, x2, #1
    // 0xeb3a04: r17 = 11520
    //     0xeb3a04: movz            x17, #0x2d00
    // 0xeb3a08: cmp             w1, w17
    // 0xeb3a0c: b.ne            #0xeb3ab4
    // 0xeb3a10: b               #0xeb3aac
    // 0xeb3a14: cmp             x2, #2, lsl #12
    // 0xeb3a18: b.lt            #0xeb3ab4
    // 0xeb3a1c: b               #0xeb3aac
    // 0xeb3a20: r17 = 8233
    //     0xeb3a20: movz            x17, #0x2029
    // 0xeb3a24: cmp             x2, x17
    // 0xeb3a28: b.gt            #0xeb3a48
    // 0xeb3a2c: r17 = 8202
    //     0xeb3a2c: movz            x17, #0x200a
    // 0xeb3a30: cmp             x2, x17
    // 0xeb3a34: b.le            #0xeb3aac
    // 0xeb3a38: r17 = 8232
    //     0xeb3a38: movz            x17, #0x2028
    // 0xeb3a3c: cmp             x2, x17
    // 0xeb3a40: b.lt            #0xeb3ab4
    // 0xeb3a44: b               #0xeb3aac
    // 0xeb3a48: r17 = 8239
    //     0xeb3a48: movz            x17, #0x202f
    // 0xeb3a4c: cmp             x2, x17
    // 0xeb3a50: b.lt            #0xeb3ab4
    // 0xeb3a54: r17 = 8287
    //     0xeb3a54: movz            x17, #0x205f
    // 0xeb3a58: cmp             x2, x17
    // 0xeb3a5c: b.gt            #0xeb3a7c
    // 0xeb3a60: r17 = 8239
    //     0xeb3a60: movz            x17, #0x202f
    // 0xeb3a64: cmp             x2, x17
    // 0xeb3a68: b.le            #0xeb3aac
    // 0xeb3a6c: r17 = 8287
    //     0xeb3a6c: movz            x17, #0x205f
    // 0xeb3a70: cmp             x2, x17
    // 0xeb3a74: b.lt            #0xeb3ab4
    // 0xeb3a78: b               #0xeb3aac
    // 0xeb3a7c: cmp             x2, #3, lsl #12
    // 0xeb3a80: b.lt            #0xeb3ab4
    // 0xeb3a84: cmp             x2, #3, lsl #12
    // 0xeb3a88: b.le            #0xeb3aac
    // 0xeb3a8c: r17 = 65279
    //     0xeb3a8c: movz            x17, #0xfeff
    // 0xeb3a90: cmp             x2, x17
    // 0xeb3a94: b.lt            #0xeb3ab4
    // 0xeb3a98: lsl             x1, x2, #1
    // 0xeb3a9c: r17 = 130558
    //     0xeb3a9c: movz            x17, #0xfdfe
    //     0xeb3aa0: movk            x17, #0x1, lsl #16
    // 0xeb3aa4: cmp             w1, w17
    // 0xeb3aa8: b.ne            #0xeb3ab4
    // 0xeb3aac: r0 = true
    //     0xeb3aac: add             x0, NULL, #0x20  ; true
    // 0xeb3ab0: ret
    //     0xeb3ab0: ret             
    // 0xeb3ab4: r0 = false
    //     0xeb3ab4: add             x0, NULL, #0x30  ; false
    // 0xeb3ab8: ret
    //     0xeb3ab8: ret             
  }
}
