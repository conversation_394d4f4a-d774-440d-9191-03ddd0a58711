// lib: , url: package:petitparser/src/parser/combinator/choice.dart

// class id: 1050895, size: 0x8
class :: {

  static ChoiceParser<Y0> ChoiceIterableExtension.toChoiceParser<Y0>(Iterable<Parser<Y0>>, {((dynamic, Failure, Failure) => Failure)? failureJoiner}) {
    // ** addr: 0x88a538, size: 0x104
    // 0x88a538: EnterFrame
    //     0x88a538: stp             fp, lr, [SP, #-0x10]!
    //     0x88a53c: mov             fp, SP
    // 0x88a540: AllocStack(0x18)
    //     0x88a540: sub             SP, SP, #0x18
    // 0x88a544: SetupParameters(dynamic _ /* r5, fp-0x10 */, {dynamic failureJoiner = Null /* r0, fp-0x8 */})
    //     0x88a544: ldur            w0, [x4, #0x13]
    //     0x88a548: sub             x1, x0, #2
    //     0x88a54c: add             x5, fp, w1, sxtw #2
    //     0x88a550: ldr             x5, [x5, #0x10]
    //     0x88a554: stur            x5, [fp, #-0x10]
    //     0x88a558: ldur            w1, [x4, #0x1f]
    //     0x88a55c: add             x1, x1, HEAP, lsl #32
    //     0x88a560: add             x16, PP, #0x26, lsl #12  ; [pp+0x26bc8] "failureJoiner"
    //     0x88a564: ldr             x16, [x16, #0xbc8]
    //     0x88a568: cmp             w1, w16
    //     0x88a56c: b.ne            #0x88a588
    //     0x88a570: ldur            w1, [x4, #0x23]
    //     0x88a574: add             x1, x1, HEAP, lsl #32
    //     0x88a578: sub             w2, w0, w1
    //     0x88a57c: add             x0, fp, w2, sxtw #2
    //     0x88a580: ldr             x0, [x0, #8]
    //     0x88a584: b               #0x88a58c
    //     0x88a588: mov             x0, NULL
    //     0x88a58c: stur            x0, [fp, #-8]
    //     0x88a590: ldur            w1, [x4, #0xf]
    //     0x88a594: cbnz            w1, #0x88a5a0
    //     0x88a598: mov             x1, NULL
    //     0x88a59c: b               #0x88a5b0
    //     0x88a5a0: ldur            w1, [x4, #0x17]
    //     0x88a5a4: add             x2, fp, w1, sxtw #2
    //     0x88a5a8: ldr             x2, [x2, #0x10]
    //     0x88a5ac: mov             x1, x2
    // 0x88a5b0: CheckStackOverflow
    //     0x88a5b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88a5b4: cmp             SP, x16
    //     0x88a5b8: b.ls            #0x88a634
    // 0x88a5bc: r2 = Null
    //     0x88a5bc: mov             x2, NULL
    // 0x88a5c0: r3 = <Y0, Y0, Y0>
    //     0x88a5c0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26bd0] TypeArguments: <Y0, Y0, Y0>
    //     0x88a5c4: ldr             x3, [x3, #0xbd0]
    // 0x88a5c8: r0 = Null
    //     0x88a5c8: mov             x0, NULL
    // 0x88a5cc: cmp             x2, x0
    // 0x88a5d0: b.ne            #0x88a5dc
    // 0x88a5d4: cmp             x1, x0
    // 0x88a5d8: b.eq            #0x88a5e8
    // 0x88a5dc: r30 = InstantiateTypeArgumentsStub
    //     0x88a5dc: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88a5e0: LoadField: r30 = r30->field_7
    //     0x88a5e0: ldur            lr, [lr, #7]
    // 0x88a5e4: blr             lr
    // 0x88a5e8: mov             x1, x0
    // 0x88a5ec: ldur            x0, [fp, #-8]
    // 0x88a5f0: cmp             w0, NULL
    // 0x88a5f4: b.ne            #0x88a600
    // 0x88a5f8: r0 = Closure: (Failure, Failure) => Failure from Function 'selectLast': static.
    //     0x88a5f8: add             x0, PP, #0x26, lsl #12  ; [pp+0x26bd8] Closure: (Failure, Failure) => Failure from Function 'selectLast': static. (0x7e54fb8bd554)
    //     0x88a5fc: ldr             x0, [x0, #0xbd8]
    // 0x88a600: stur            x0, [fp, #-8]
    // 0x88a604: r0 = ChoiceParser()
    //     0x88a604: bl              #0x88a7a8  ; AllocateChoiceParserStub -> ChoiceParser<C2X0> (size=0x14)
    // 0x88a608: mov             x3, x0
    // 0x88a60c: ldur            x0, [fp, #-8]
    // 0x88a610: stur            x3, [fp, #-0x18]
    // 0x88a614: StoreField: r3->field_f = r0
    //     0x88a614: stur            w0, [x3, #0xf]
    // 0x88a618: mov             x1, x3
    // 0x88a61c: ldur            x2, [fp, #-0x10]
    // 0x88a620: r0 = ListParser()
    //     0x88a620: bl              #0x88a63c  ; [package:petitparser/src/parser/combinator/list.dart] ListParser::ListParser
    // 0x88a624: ldur            x0, [fp, #-0x18]
    // 0x88a628: LeaveFrame
    //     0x88a628: mov             SP, fp
    //     0x88a62c: ldp             fp, lr, [SP], #0x10
    // 0x88a630: ret
    //     0x88a630: ret             
    // 0x88a634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88a634: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88a638: b               #0x88a5bc
  }
}

// class id: 740, size: 0x14, field offset: 0x10
class ChoiceParser<C2X0> extends ListParser<C2X0, dynamic> {

  _ fastParseOn(/* No info */) {
    // ** addr: 0xeafe68, size: 0xf0
    // 0xeafe68: EnterFrame
    //     0xeafe68: stp             fp, lr, [SP, #-0x10]!
    //     0xeafe6c: mov             fp, SP
    // 0xeafe70: AllocStack(0x28)
    //     0xeafe70: sub             SP, SP, #0x28
    // 0xeafe74: SetupParameters(dynamic _ /* r2 => r5, fp-0x20 */, dynamic _ /* r3 => r4, fp-0x28 */)
    //     0xeafe74: mov             x5, x2
    //     0xeafe78: mov             x4, x3
    //     0xeafe7c: stur            x2, [fp, #-0x20]
    //     0xeafe80: stur            x3, [fp, #-0x28]
    // 0xeafe84: CheckStackOverflow
    //     0xeafe84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeafe88: cmp             SP, x16
    //     0xeafe8c: b.ls            #0xeaff48
    // 0xeafe90: LoadField: r6 = r1->field_b
    //     0xeafe90: ldur            w6, [x1, #0xb]
    // 0xeafe94: DecompressPointer r6
    //     0xeafe94: add             x6, x6, HEAP, lsl #32
    // 0xeafe98: stur            x6, [fp, #-0x18]
    // 0xeafe9c: LoadField: r0 = r6->field_b
    //     0xeafe9c: ldur            w0, [x6, #0xb]
    // 0xeafea0: r7 = LoadInt32Instr(r0)
    //     0xeafea0: sbfx            x7, x0, #1, #0x1f
    // 0xeafea4: stur            x7, [fp, #-0x10]
    // 0xeafea8: r2 = -1
    //     0xeafea8: movn            x2, #0
    // 0xeafeac: r8 = 0
    //     0xeafeac: movz            x8, #0
    // 0xeafeb0: stur            x8, [fp, #-8]
    // 0xeafeb4: CheckStackOverflow
    //     0xeafeb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeafeb8: cmp             SP, x16
    //     0xeafebc: b.ls            #0xeaff50
    // 0xeafec0: cmp             x8, x7
    // 0xeafec4: b.ge            #0xeaff28
    // 0xeafec8: ArrayLoad: r1 = r6[r8]  ; Unknown_4
    //     0xeafec8: add             x16, x6, x8, lsl #2
    //     0xeafecc: ldur            w1, [x16, #0xf]
    // 0xeafed0: DecompressPointer r1
    //     0xeafed0: add             x1, x1, HEAP, lsl #32
    // 0xeafed4: r0 = LoadClassIdInstr(r1)
    //     0xeafed4: ldur            x0, [x1, #-1]
    //     0xeafed8: ubfx            x0, x0, #0xc, #0x14
    // 0xeafedc: mov             x2, x5
    // 0xeafee0: mov             x3, x4
    // 0xeafee4: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeafee4: sub             lr, x0, #0xfce
    //     0xeafee8: ldr             lr, [x21, lr, lsl #3]
    //     0xeafeec: blr             lr
    // 0xeafef0: r2 = LoadInt32Instr(r0)
    //     0xeafef0: sbfx            x2, x0, #1, #0x1f
    //     0xeafef4: tbz             w0, #0, #0xeafefc
    //     0xeafef8: ldur            x2, [x0, #7]
    // 0xeafefc: tbz             x2, #0x3f, #0xeaff1c
    // 0xeaff00: ldur            x3, [fp, #-8]
    // 0xeaff04: add             x8, x3, #1
    // 0xeaff08: ldur            x5, [fp, #-0x20]
    // 0xeaff0c: ldur            x4, [fp, #-0x28]
    // 0xeaff10: ldur            x6, [fp, #-0x18]
    // 0xeaff14: ldur            x7, [fp, #-0x10]
    // 0xeaff18: b               #0xeafeb0
    // 0xeaff1c: LeaveFrame
    //     0xeaff1c: mov             SP, fp
    //     0xeaff20: ldp             fp, lr, [SP], #0x10
    // 0xeaff24: ret
    //     0xeaff24: ret             
    // 0xeaff28: r0 = BoxInt64Instr(r2)
    //     0xeaff28: sbfiz           x0, x2, #1, #0x1f
    //     0xeaff2c: cmp             x2, x0, asr #1
    //     0xeaff30: b.eq            #0xeaff3c
    //     0xeaff34: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaff38: stur            x2, [x0, #7]
    // 0xeaff3c: LeaveFrame
    //     0xeaff3c: mov             SP, fp
    //     0xeaff40: ldp             fp, lr, [SP], #0x10
    // 0xeaff44: ret
    //     0xeaff44: ret             
    // 0xeaff48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaff48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaff4c: b               #0xeafe90
    // 0xeaff50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaff50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaff54: b               #0xeafec0
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb1d7c, size: 0x164
    // 0xeb1d7c: EnterFrame
    //     0xeb1d7c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb1d80: mov             fp, SP
    // 0xeb1d84: AllocStack(0x48)
    //     0xeb1d84: sub             SP, SP, #0x48
    // 0xeb1d88: SetupParameters(ChoiceParser<C2X0> this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */)
    //     0xeb1d88: mov             x4, x1
    //     0xeb1d8c: mov             x3, x2
    //     0xeb1d90: stur            x1, [fp, #-0x18]
    //     0xeb1d94: stur            x2, [fp, #-0x20]
    // 0xeb1d98: CheckStackOverflow
    //     0xeb1d98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb1d9c: cmp             SP, x16
    //     0xeb1da0: b.ls            #0xeb1ecc
    // 0xeb1da4: LoadField: r5 = r4->field_b
    //     0xeb1da4: ldur            w5, [x4, #0xb]
    // 0xeb1da8: DecompressPointer r5
    //     0xeb1da8: add             x5, x5, HEAP, lsl #32
    // 0xeb1dac: stur            x5, [fp, #-0x10]
    // 0xeb1db0: LoadField: r0 = r5->field_b
    //     0xeb1db0: ldur            w0, [x5, #0xb]
    // 0xeb1db4: r6 = LoadInt32Instr(r0)
    //     0xeb1db4: sbfx            x6, x0, #1, #0x1f
    // 0xeb1db8: mov             x0, x6
    // 0xeb1dbc: stur            x6, [fp, #-8]
    // 0xeb1dc0: r1 = 0
    //     0xeb1dc0: movz            x1, #0
    // 0xeb1dc4: cmp             x1, x0
    // 0xeb1dc8: b.hs            #0xeb1ed4
    // 0xeb1dcc: LoadField: r1 = r5->field_f
    //     0xeb1dcc: ldur            w1, [x5, #0xf]
    // 0xeb1dd0: DecompressPointer r1
    //     0xeb1dd0: add             x1, x1, HEAP, lsl #32
    // 0xeb1dd4: r0 = LoadClassIdInstr(r1)
    //     0xeb1dd4: ldur            x0, [x1, #-1]
    //     0xeb1dd8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1ddc: mov             x2, x3
    // 0xeb1de0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1de0: sub             lr, x0, #1, lsl #12
    //     0xeb1de4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1de8: blr             lr
    // 0xeb1dec: r1 = LoadClassIdInstr(r0)
    //     0xeb1dec: ldur            x1, [x0, #-1]
    //     0xeb1df0: ubfx            x1, x1, #0xc, #0x14
    // 0xeb1df4: cmp             x1, #0x2f3
    // 0xeb1df8: b.eq            #0xeb1e08
    // 0xeb1dfc: LeaveFrame
    //     0xeb1dfc: mov             SP, fp
    //     0xeb1e00: ldp             fp, lr, [SP], #0x10
    // 0xeb1e04: ret
    //     0xeb1e04: ret             
    // 0xeb1e08: ldur            x1, [fp, #-0x18]
    // 0xeb1e0c: LoadField: r3 = r1->field_f
    //     0xeb1e0c: ldur            w3, [x1, #0xf]
    // 0xeb1e10: DecompressPointer r3
    //     0xeb1e10: add             x3, x3, HEAP, lsl #32
    // 0xeb1e14: stur            x3, [fp, #-0x30]
    // 0xeb1e18: mov             x7, x0
    // 0xeb1e1c: r6 = 1
    //     0xeb1e1c: movz            x6, #0x1
    // 0xeb1e20: ldur            x4, [fp, #-0x10]
    // 0xeb1e24: ldur            x5, [fp, #-8]
    // 0xeb1e28: stur            x7, [fp, #-0x18]
    // 0xeb1e2c: stur            x6, [fp, #-0x28]
    // 0xeb1e30: CheckStackOverflow
    //     0xeb1e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb1e34: cmp             SP, x16
    //     0xeb1e38: b.ls            #0xeb1ed8
    // 0xeb1e3c: cmp             x6, x5
    // 0xeb1e40: b.ge            #0xeb1ebc
    // 0xeb1e44: ArrayLoad: r1 = r4[r6]  ; Unknown_4
    //     0xeb1e44: add             x16, x4, x6, lsl #2
    //     0xeb1e48: ldur            w1, [x16, #0xf]
    // 0xeb1e4c: DecompressPointer r1
    //     0xeb1e4c: add             x1, x1, HEAP, lsl #32
    // 0xeb1e50: r0 = LoadClassIdInstr(r1)
    //     0xeb1e50: ldur            x0, [x1, #-1]
    //     0xeb1e54: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1e58: ldur            x2, [fp, #-0x20]
    // 0xeb1e5c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1e5c: sub             lr, x0, #1, lsl #12
    //     0xeb1e60: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1e64: blr             lr
    // 0xeb1e68: r1 = LoadClassIdInstr(r0)
    //     0xeb1e68: ldur            x1, [x0, #-1]
    //     0xeb1e6c: ubfx            x1, x1, #0xc, #0x14
    // 0xeb1e70: cmp             x1, #0x2f3
    // 0xeb1e74: b.ne            #0xeb1eb0
    // 0xeb1e78: ldur            x1, [fp, #-0x28]
    // 0xeb1e7c: ldur            x16, [fp, #-0x30]
    // 0xeb1e80: ldur            lr, [fp, #-0x18]
    // 0xeb1e84: stp             lr, x16, [SP, #8]
    // 0xeb1e88: str             x0, [SP]
    // 0xeb1e8c: ldur            x0, [fp, #-0x30]
    // 0xeb1e90: ClosureCall
    //     0xeb1e90: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xeb1e94: ldur            x2, [x0, #0x1f]
    //     0xeb1e98: blr             x2
    // 0xeb1e9c: ldur            x1, [fp, #-0x28]
    // 0xeb1ea0: add             x6, x1, #1
    // 0xeb1ea4: mov             x7, x0
    // 0xeb1ea8: ldur            x3, [fp, #-0x30]
    // 0xeb1eac: b               #0xeb1e20
    // 0xeb1eb0: LeaveFrame
    //     0xeb1eb0: mov             SP, fp
    //     0xeb1eb4: ldp             fp, lr, [SP], #0x10
    // 0xeb1eb8: ret
    //     0xeb1eb8: ret             
    // 0xeb1ebc: ldur            x0, [fp, #-0x18]
    // 0xeb1ec0: LeaveFrame
    //     0xeb1ec0: mov             SP, fp
    //     0xeb1ec4: ldp             fp, lr, [SP], #0x10
    // 0xeb1ec8: ret
    //     0xeb1ec8: ret             
    // 0xeb1ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb1ecc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb1ed0: b               #0xeb1da4
    // 0xeb1ed4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb1ed4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeb1ed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb1ed8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb1edc: b               #0xeb1e3c
  }
}
