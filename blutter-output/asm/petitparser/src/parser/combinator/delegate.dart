// lib: , url: package:petitparser/src/parser/combinator/delegate.dart

// class id: 1050896, size: 0x8
class :: {
}

// class id: 741, size: 0x10, field offset: 0xc
abstract class DelegateParser<C1X0, C1X1> extends Parser<C1X0> {

  get _ children(/* No info */) {
    // ** addr: 0x88f940, size: 0x60
    // 0x88f940: EnterFrame
    //     0x88f940: stp             fp, lr, [SP, #-0x10]!
    //     0x88f944: mov             fp, SP
    // 0x88f948: AllocStack(0x10)
    //     0x88f948: sub             SP, SP, #0x10
    // 0x88f94c: r0 = 2
    //     0x88f94c: movz            x0, #0x2
    // 0x88f950: LoadField: r3 = r1->field_b
    //     0x88f950: ldur            w3, [x1, #0xb]
    // 0x88f954: DecompressPointer r3
    //     0x88f954: add             x3, x3, HEAP, lsl #32
    // 0x88f958: mov             x2, x0
    // 0x88f95c: stur            x3, [fp, #-8]
    // 0x88f960: r1 = Null
    //     0x88f960: mov             x1, NULL
    // 0x88f964: r0 = AllocateArray()
    //     0x88f964: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88f968: mov             x2, x0
    // 0x88f96c: ldur            x0, [fp, #-8]
    // 0x88f970: stur            x2, [fp, #-0x10]
    // 0x88f974: StoreField: r2->field_f = r0
    //     0x88f974: stur            w0, [x2, #0xf]
    // 0x88f978: r1 = <Parser>
    //     0x88f978: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x88f97c: ldr             x1, [x1, #0x6f8]
    // 0x88f980: r0 = AllocateGrowableArray()
    //     0x88f980: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88f984: ldur            x1, [fp, #-0x10]
    // 0x88f988: StoreField: r0->field_f = r1
    //     0x88f988: stur            w1, [x0, #0xf]
    // 0x88f98c: r1 = 2
    //     0x88f98c: movz            x1, #0x2
    // 0x88f990: StoreField: r0->field_b = r1
    //     0x88f990: stur            w1, [x0, #0xb]
    // 0x88f994: LeaveFrame
    //     0x88f994: mov             SP, fp
    //     0x88f998: ldp             fp, lr, [SP], #0x10
    // 0x88f99c: ret
    //     0x88f99c: ret             
  }
  _ replace(/* No info */) {
    // ** addr: 0x893dc0, size: 0xbc
    // 0x893dc0: EnterFrame
    //     0x893dc0: stp             fp, lr, [SP, #-0x10]!
    //     0x893dc4: mov             fp, SP
    // 0x893dc8: AllocStack(0x20)
    //     0x893dc8: sub             SP, SP, #0x20
    // 0x893dcc: SetupParameters(DelegateParser<C1X0, C1X1> this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r1, fp-0x10 */)
    //     0x893dcc: stur            x1, [fp, #-8]
    //     0x893dd0: mov             x16, x3
    //     0x893dd4: mov             x3, x1
    //     0x893dd8: mov             x1, x16
    //     0x893ddc: stur            x1, [fp, #-0x10]
    // 0x893de0: CheckStackOverflow
    //     0x893de0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x893de4: cmp             SP, x16
    //     0x893de8: b.ls            #0x893e74
    // 0x893dec: LoadField: r0 = r3->field_b
    //     0x893dec: ldur            w0, [x3, #0xb]
    // 0x893df0: DecompressPointer r0
    //     0x893df0: add             x0, x0, HEAP, lsl #32
    // 0x893df4: r4 = LoadClassIdInstr(r0)
    //     0x893df4: ldur            x4, [x0, #-1]
    //     0x893df8: ubfx            x4, x4, #0xc, #0x14
    // 0x893dfc: stp             x2, x0, [SP]
    // 0x893e00: mov             x0, x4
    // 0x893e04: mov             lr, x0
    // 0x893e08: ldr             lr, [x21, lr, lsl #3]
    // 0x893e0c: blr             lr
    // 0x893e10: tbnz            w0, #4, #0x893e64
    // 0x893e14: ldur            x3, [fp, #-8]
    // 0x893e18: LoadField: r2 = r3->field_7
    //     0x893e18: ldur            w2, [x3, #7]
    // 0x893e1c: DecompressPointer r2
    //     0x893e1c: add             x2, x2, HEAP, lsl #32
    // 0x893e20: ldur            x0, [fp, #-0x10]
    // 0x893e24: r1 = Null
    //     0x893e24: mov             x1, NULL
    // 0x893e28: r8 = Parser<C1X0>
    //     0x893e28: add             x8, PP, #0x31, lsl #12  ; [pp+0x31258] Type: Parser<C1X0>
    //     0x893e2c: ldr             x8, [x8, #0x258]
    // 0x893e30: LoadField: r9 = r8->field_7
    //     0x893e30: ldur            x9, [x8, #7]
    // 0x893e34: r3 = Null
    //     0x893e34: add             x3, PP, #0x31, lsl #12  ; [pp+0x31438] Null
    //     0x893e38: ldr             x3, [x3, #0x438]
    // 0x893e3c: blr             x9
    // 0x893e40: ldur            x0, [fp, #-0x10]
    // 0x893e44: ldur            x1, [fp, #-8]
    // 0x893e48: StoreField: r1->field_b = r0
    //     0x893e48: stur            w0, [x1, #0xb]
    //     0x893e4c: ldurb           w16, [x1, #-1]
    //     0x893e50: ldurb           w17, [x0, #-1]
    //     0x893e54: and             x16, x17, x16, lsr #2
    //     0x893e58: tst             x16, HEAP, lsr #32
    //     0x893e5c: b.eq            #0x893e64
    //     0x893e60: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x893e64: r0 = Null
    //     0x893e64: mov             x0, NULL
    // 0x893e68: LeaveFrame
    //     0x893e68: mov             SP, fp
    //     0x893e6c: ldp             fp, lr, [SP], #0x10
    // 0x893e70: ret
    //     0x893e70: ret             
    // 0x893e74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x893e74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x893e78: b               #0x893dec
  }
}
