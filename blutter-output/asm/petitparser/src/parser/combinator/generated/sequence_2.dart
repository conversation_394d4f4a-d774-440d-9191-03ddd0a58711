// lib: , url: package:petitparser/src/parser/combinator/generated/sequence_2.dart

// class id: 1050897, size: 0x8
class :: {

  static Parser<(Y0, Y1)> seq2<Y0, Y1>(Parser<Y0>, Parser<Y1>) {
    // ** addr: 0x88cf2c, size: 0x68
    // 0x88cf2c: EnterFrame
    //     0x88cf2c: stp             fp, lr, [SP, #-0x10]!
    //     0x88cf30: mov             fp, SP
    // 0x88cf34: LoadField: r0 = r4->field_f
    //     0x88cf34: ldur            w0, [x4, #0xf]
    // 0x88cf38: cbnz            w0, #0x88cf44
    // 0x88cf3c: r1 = Null
    //     0x88cf3c: mov             x1, NULL
    // 0x88cf40: b               #0x88cf50
    // 0x88cf44: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88cf44: ldur            w0, [x4, #0x17]
    // 0x88cf48: add             x1, fp, w0, sxtw #2
    // 0x88cf4c: ldr             x1, [x1, #0x10]
    // 0x88cf50: ldr             x4, [fp, #0x18]
    // 0x88cf54: ldr             x0, [fp, #0x10]
    // 0x88cf58: r2 = Null
    //     0x88cf58: mov             x2, NULL
    // 0x88cf5c: r3 = <(Y0, Y1), Y0, Y1>
    //     0x88cf5c: add             x3, PP, #0x26, lsl #12  ; [pp+0x26a08] TypeArguments: <(Y0, Y1), Y0, Y1>
    //     0x88cf60: ldr             x3, [x3, #0xa08]
    // 0x88cf64: r30 = InstantiateTypeArgumentsStub
    //     0x88cf64: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88cf68: LoadField: r30 = r30->field_7
    //     0x88cf68: ldur            lr, [lr, #7]
    // 0x88cf6c: blr             lr
    // 0x88cf70: mov             x1, x0
    // 0x88cf74: r0 = SequenceParser2()
    //     0x88cf74: bl              #0x88cf94  ; AllocateSequenceParser2Stub -> SequenceParser2<C1X0, C1X1> (size=0x14)
    // 0x88cf78: ldr             x1, [fp, #0x18]
    // 0x88cf7c: StoreField: r0->field_b = r1
    //     0x88cf7c: stur            w1, [x0, #0xb]
    // 0x88cf80: ldr             x1, [fp, #0x10]
    // 0x88cf84: StoreField: r0->field_f = r1
    //     0x88cf84: stur            w1, [x0, #0xf]
    // 0x88cf88: LeaveFrame
    //     0x88cf88: mov             SP, fp
    //     0x88cf8c: ldp             fp, lr, [SP], #0x10
    // 0x88cf90: ret
    //     0x88cf90: ret             
  }
  static Parser<Y2> RecordParserExtension2.map2<Y0, Y1, Y2>(Parser<(Y0, Y1)>, (dynamic, Y0, Y1) => Y2) {
    // ** addr: 0x88d2e4, size: 0xbc
    // 0x88d2e4: EnterFrame
    //     0x88d2e4: stp             fp, lr, [SP, #-0x10]!
    //     0x88d2e8: mov             fp, SP
    // 0x88d2ec: AllocStack(0x28)
    //     0x88d2ec: sub             SP, SP, #0x28
    // 0x88d2f0: SetupParameters()
    //     0x88d2f0: ldur            w0, [x4, #0xf]
    //     0x88d2f4: cbnz            w0, #0x88d300
    //     0x88d2f8: mov             x1, NULL
    //     0x88d2fc: b               #0x88d30c
    //     0x88d300: ldur            w0, [x4, #0x17]
    //     0x88d304: add             x1, fp, w0, sxtw #2
    //     0x88d308: ldr             x1, [x1, #0x10]
    //     0x88d30c: ldr             x0, [fp, #0x10]
    //     0x88d310: stur            x1, [fp, #-8]
    // 0x88d314: CheckStackOverflow
    //     0x88d314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d318: cmp             SP, x16
    //     0x88d31c: b.ls            #0x88d398
    // 0x88d320: r1 = 1
    //     0x88d320: movz            x1, #0x1
    // 0x88d324: r0 = AllocateContext()
    //     0x88d324: bl              #0xec126c  ; AllocateContextStub
    // 0x88d328: mov             x4, x0
    // 0x88d32c: ldr             x0, [fp, #0x10]
    // 0x88d330: stur            x4, [fp, #-0x10]
    // 0x88d334: StoreField: r4->field_f = r0
    //     0x88d334: stur            w0, [x4, #0xf]
    // 0x88d338: ldur            x1, [fp, #-8]
    // 0x88d33c: r2 = Null
    //     0x88d33c: mov             x2, NULL
    // 0x88d340: r3 = <(Y0, Y1), Y2>
    //     0x88d340: add             x3, PP, #0x26, lsl #12  ; [pp+0x26920] TypeArguments: <(Y0, Y1), Y2>
    //     0x88d344: ldr             x3, [x3, #0x920]
    // 0x88d348: r30 = InstantiateTypeArgumentsStub
    //     0x88d348: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88d34c: LoadField: r30 = r30->field_7
    //     0x88d34c: ldur            lr, [lr, #7]
    // 0x88d350: blr             lr
    // 0x88d354: ldur            x2, [fp, #-0x10]
    // 0x88d358: r1 = Function '<anonymous closure>': static.
    //     0x88d358: add             x1, PP, #0x26, lsl #12  ; [pp+0x26928] AnonymousClosure: static (0x88d3a0), in [package:petitparser/src/parser/combinator/generated/sequence_2.dart] ::RecordParserExtension2.map2 (0x88d2e4)
    //     0x88d35c: ldr             x1, [x1, #0x928]
    // 0x88d360: stur            x0, [fp, #-0x10]
    // 0x88d364: r0 = AllocateClosure()
    //     0x88d364: bl              #0xec1630  ; AllocateClosureStub
    // 0x88d368: mov             x1, x0
    // 0x88d36c: ldur            x0, [fp, #-8]
    // 0x88d370: StoreField: r1->field_b = r0
    //     0x88d370: stur            w0, [x1, #0xb]
    // 0x88d374: ldur            x16, [fp, #-0x10]
    // 0x88d378: ldr             lr, [fp, #0x18]
    // 0x88d37c: stp             lr, x16, [SP, #8]
    // 0x88d380: str             x1, [SP]
    // 0x88d384: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88d384: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88d388: r0 = MapParserExtension.map()
    //     0x88d388: bl              #0x88ab3c  ; [package:petitparser/src/parser/action/map.dart] ::MapParserExtension.map
    // 0x88d38c: LeaveFrame
    //     0x88d38c: mov             SP, fp
    //     0x88d390: ldp             fp, lr, [SP], #0x10
    // 0x88d394: ret
    //     0x88d394: ret             
    // 0x88d398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88d398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88d39c: b               #0x88d320
  }
  [closure] static Y2 <anonymous closure>(dynamic, (Y0, Y1)) {
    // ** addr: 0x88d3a0, size: 0x68
    // 0x88d3a0: EnterFrame
    //     0x88d3a0: stp             fp, lr, [SP, #-0x10]!
    //     0x88d3a4: mov             fp, SP
    // 0x88d3a8: AllocStack(0x18)
    //     0x88d3a8: sub             SP, SP, #0x18
    // 0x88d3ac: SetupParameters()
    //     0x88d3ac: ldr             x0, [fp, #0x18]
    //     0x88d3b0: ldur            w1, [x0, #0x17]
    //     0x88d3b4: add             x1, x1, HEAP, lsl #32
    // 0x88d3b8: CheckStackOverflow
    //     0x88d3b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d3bc: cmp             SP, x16
    //     0x88d3c0: b.ls            #0x88d400
    // 0x88d3c4: LoadField: r0 = r1->field_f
    //     0x88d3c4: ldur            w0, [x1, #0xf]
    // 0x88d3c8: DecompressPointer r0
    //     0x88d3c8: add             x0, x0, HEAP, lsl #32
    // 0x88d3cc: ldr             x1, [fp, #0x10]
    // 0x88d3d0: LoadField: r2 = r1->field_f
    //     0x88d3d0: ldur            w2, [x1, #0xf]
    // 0x88d3d4: DecompressPointer r2
    //     0x88d3d4: add             x2, x2, HEAP, lsl #32
    // 0x88d3d8: LoadField: r3 = r1->field_13
    //     0x88d3d8: ldur            w3, [x1, #0x13]
    // 0x88d3dc: DecompressPointer r3
    //     0x88d3dc: add             x3, x3, HEAP, lsl #32
    // 0x88d3e0: stp             x2, x0, [SP, #8]
    // 0x88d3e4: str             x3, [SP]
    // 0x88d3e8: ClosureCall
    //     0x88d3e8: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x88d3ec: ldur            x2, [x0, #0x1f]
    //     0x88d3f0: blr             x2
    // 0x88d3f4: LeaveFrame
    //     0x88d3f4: mov             SP, fp
    //     0x88d3f8: ldp             fp, lr, [SP], #0x10
    // 0x88d3fc: ret
    //     0x88d3fc: ret             
    // 0x88d400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88d400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88d404: b               #0x88d3c4
  }
  static Parser<(Y0, Y1)> RecordOfParsersExtension2.toSequenceParser<Y0, Y1>((Parser<Y0>, Parser<Y1>)) {
    // ** addr: 0x88d408, size: 0x84
    // 0x88d408: EnterFrame
    //     0x88d408: stp             fp, lr, [SP, #-0x10]!
    //     0x88d40c: mov             fp, SP
    // 0x88d410: AllocStack(0x10)
    //     0x88d410: sub             SP, SP, #0x10
    // 0x88d414: SetupParameters()
    //     0x88d414: ldur            w0, [x4, #0xf]
    //     0x88d418: cbnz            w0, #0x88d424
    //     0x88d41c: mov             x1, NULL
    //     0x88d420: b               #0x88d430
    //     0x88d424: ldur            w0, [x4, #0x17]
    //     0x88d428: add             x1, fp, w0, sxtw #2
    //     0x88d42c: ldr             x1, [x1, #0x10]
    // 0x88d430: ldr             x0, [fp, #0x10]
    // 0x88d434: r2 = Null
    //     0x88d434: mov             x2, NULL
    // 0x88d438: r3 = <(Y0, Y1), Y0, Y1>
    //     0x88d438: add             x3, PP, #0x26, lsl #12  ; [pp+0x26930] TypeArguments: <(Y0, Y1), Y0, Y1>
    //     0x88d43c: ldr             x3, [x3, #0x930]
    // 0x88d440: r30 = InstantiateTypeArgumentsStub
    //     0x88d440: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88d444: LoadField: r30 = r30->field_7
    //     0x88d444: ldur            lr, [lr, #7]
    // 0x88d448: blr             lr
    // 0x88d44c: mov             x1, x0
    // 0x88d450: ldr             x0, [fp, #0x10]
    // 0x88d454: LoadField: r2 = r0->field_f
    //     0x88d454: ldur            w2, [x0, #0xf]
    // 0x88d458: DecompressPointer r2
    //     0x88d458: add             x2, x2, HEAP, lsl #32
    // 0x88d45c: stur            x2, [fp, #-0x10]
    // 0x88d460: LoadField: r3 = r0->field_13
    //     0x88d460: ldur            w3, [x0, #0x13]
    // 0x88d464: DecompressPointer r3
    //     0x88d464: add             x3, x3, HEAP, lsl #32
    // 0x88d468: stur            x3, [fp, #-8]
    // 0x88d46c: r0 = SequenceParser2()
    //     0x88d46c: bl              #0x88cf94  ; AllocateSequenceParser2Stub -> SequenceParser2<C1X0, C1X1> (size=0x14)
    // 0x88d470: ldur            x1, [fp, #-0x10]
    // 0x88d474: StoreField: r0->field_b = r1
    //     0x88d474: stur            w1, [x0, #0xb]
    // 0x88d478: ldur            x1, [fp, #-8]
    // 0x88d47c: StoreField: r0->field_f = r1
    //     0x88d47c: stur            w1, [x0, #0xf]
    // 0x88d480: LeaveFrame
    //     0x88d480: mov             SP, fp
    //     0x88d484: ldp             fp, lr, [SP], #0x10
    // 0x88d488: ret
    //     0x88d488: ret             
  }
}

// class id: 738, size: 0x14, field offset: 0xc
class SequenceParser2<C1X0, C1X1> extends Parser<C1X0>
    implements SequentialParser {

  get _ children(/* No info */) {
    // ** addr: 0x88f9a0, size: 0x74
    // 0x88f9a0: EnterFrame
    //     0x88f9a0: stp             fp, lr, [SP, #-0x10]!
    //     0x88f9a4: mov             fp, SP
    // 0x88f9a8: AllocStack(0x18)
    //     0x88f9a8: sub             SP, SP, #0x18
    // 0x88f9ac: r0 = 4
    //     0x88f9ac: movz            x0, #0x4
    // 0x88f9b0: LoadField: r3 = r1->field_b
    //     0x88f9b0: ldur            w3, [x1, #0xb]
    // 0x88f9b4: DecompressPointer r3
    //     0x88f9b4: add             x3, x3, HEAP, lsl #32
    // 0x88f9b8: stur            x3, [fp, #-0x10]
    // 0x88f9bc: LoadField: r4 = r1->field_f
    //     0x88f9bc: ldur            w4, [x1, #0xf]
    // 0x88f9c0: DecompressPointer r4
    //     0x88f9c0: add             x4, x4, HEAP, lsl #32
    // 0x88f9c4: mov             x2, x0
    // 0x88f9c8: stur            x4, [fp, #-8]
    // 0x88f9cc: r1 = Null
    //     0x88f9cc: mov             x1, NULL
    // 0x88f9d0: r0 = AllocateArray()
    //     0x88f9d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88f9d4: mov             x2, x0
    // 0x88f9d8: ldur            x0, [fp, #-0x10]
    // 0x88f9dc: stur            x2, [fp, #-0x18]
    // 0x88f9e0: StoreField: r2->field_f = r0
    //     0x88f9e0: stur            w0, [x2, #0xf]
    // 0x88f9e4: ldur            x0, [fp, #-8]
    // 0x88f9e8: StoreField: r2->field_13 = r0
    //     0x88f9e8: stur            w0, [x2, #0x13]
    // 0x88f9ec: r1 = <Parser>
    //     0x88f9ec: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x88f9f0: ldr             x1, [x1, #0x6f8]
    // 0x88f9f4: r0 = AllocateGrowableArray()
    //     0x88f9f4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88f9f8: ldur            x1, [fp, #-0x18]
    // 0x88f9fc: StoreField: r0->field_f = r1
    //     0x88f9fc: stur            w1, [x0, #0xf]
    // 0x88fa00: r1 = 4
    //     0x88fa00: movz            x1, #0x4
    // 0x88fa04: StoreField: r0->field_b = r1
    //     0x88fa04: stur            w1, [x0, #0xb]
    // 0x88fa08: LeaveFrame
    //     0x88fa08: mov             SP, fp
    //     0x88fa0c: ldp             fp, lr, [SP], #0x10
    // 0x88fa10: ret
    //     0x88fa10: ret             
  }
  _ replace(/* No info */) {
    // ** addr: 0x893ff0, size: 0x144
    // 0x893ff0: EnterFrame
    //     0x893ff0: stp             fp, lr, [SP, #-0x10]!
    //     0x893ff4: mov             fp, SP
    // 0x893ff8: AllocStack(0x28)
    //     0x893ff8: sub             SP, SP, #0x28
    // 0x893ffc: SetupParameters(SequenceParser2<C1X0, C1X1> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0x893ffc: stur            x1, [fp, #-8]
    //     0x894000: mov             x16, x3
    //     0x894004: mov             x3, x1
    //     0x894008: mov             x1, x16
    //     0x89400c: stur            x2, [fp, #-0x10]
    //     0x894010: stur            x1, [fp, #-0x18]
    // 0x894014: CheckStackOverflow
    //     0x894014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x894018: cmp             SP, x16
    //     0x89401c: b.ls            #0x89412c
    // 0x894020: LoadField: r0 = r3->field_b
    //     0x894020: ldur            w0, [x3, #0xb]
    // 0x894024: DecompressPointer r0
    //     0x894024: add             x0, x0, HEAP, lsl #32
    // 0x894028: r4 = LoadClassIdInstr(r0)
    //     0x894028: ldur            x4, [x0, #-1]
    //     0x89402c: ubfx            x4, x4, #0xc, #0x14
    // 0x894030: stp             x2, x0, [SP]
    // 0x894034: mov             x0, x4
    // 0x894038: mov             lr, x0
    // 0x89403c: ldr             lr, [x21, lr, lsl #3]
    // 0x894040: blr             lr
    // 0x894044: tbnz            w0, #4, #0x89409c
    // 0x894048: ldur            x3, [fp, #-8]
    // 0x89404c: LoadField: r2 = r3->field_7
    //     0x89404c: ldur            w2, [x3, #7]
    // 0x894050: DecompressPointer r2
    //     0x894050: add             x2, x2, HEAP, lsl #32
    // 0x894054: ldur            x0, [fp, #-0x18]
    // 0x894058: r1 = Null
    //     0x894058: mov             x1, NULL
    // 0x89405c: r8 = Parser<C1X0>
    //     0x89405c: add             x8, PP, #0x31, lsl #12  ; [pp+0x31258] Type: Parser<C1X0>
    //     0x894060: ldr             x8, [x8, #0x258]
    // 0x894064: LoadField: r9 = r8->field_7
    //     0x894064: ldur            x9, [x8, #7]
    // 0x894068: r3 = Null
    //     0x894068: add             x3, PP, #0x31, lsl #12  ; [pp+0x313d8] Null
    //     0x89406c: ldr             x3, [x3, #0x3d8]
    // 0x894070: blr             x9
    // 0x894074: ldur            x0, [fp, #-0x18]
    // 0x894078: ldur            x1, [fp, #-8]
    // 0x89407c: StoreField: r1->field_b = r0
    //     0x89407c: stur            w0, [x1, #0xb]
    //     0x894080: ldurb           w16, [x1, #-1]
    //     0x894084: ldurb           w17, [x0, #-1]
    //     0x894088: and             x16, x17, x16, lsr #2
    //     0x89408c: tst             x16, HEAP, lsr #32
    //     0x894090: b.eq            #0x894098
    //     0x894094: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894098: b               #0x8940a0
    // 0x89409c: ldur            x1, [fp, #-8]
    // 0x8940a0: LoadField: r0 = r1->field_f
    //     0x8940a0: ldur            w0, [x1, #0xf]
    // 0x8940a4: DecompressPointer r0
    //     0x8940a4: add             x0, x0, HEAP, lsl #32
    // 0x8940a8: r2 = LoadClassIdInstr(r0)
    //     0x8940a8: ldur            x2, [x0, #-1]
    //     0x8940ac: ubfx            x2, x2, #0xc, #0x14
    // 0x8940b0: ldur            x16, [fp, #-0x10]
    // 0x8940b4: stp             x16, x0, [SP]
    // 0x8940b8: mov             x0, x2
    // 0x8940bc: mov             lr, x0
    // 0x8940c0: ldr             lr, [x21, lr, lsl #3]
    // 0x8940c4: blr             lr
    // 0x8940c8: tbnz            w0, #4, #0x89411c
    // 0x8940cc: ldur            x3, [fp, #-8]
    // 0x8940d0: LoadField: r2 = r3->field_7
    //     0x8940d0: ldur            w2, [x3, #7]
    // 0x8940d4: DecompressPointer r2
    //     0x8940d4: add             x2, x2, HEAP, lsl #32
    // 0x8940d8: ldur            x0, [fp, #-0x18]
    // 0x8940dc: r1 = Null
    //     0x8940dc: mov             x1, NULL
    // 0x8940e0: r8 = Parser<C1X1>
    //     0x8940e0: add             x8, PP, #0x31, lsl #12  ; [pp+0x31270] Type: Parser<C1X1>
    //     0x8940e4: ldr             x8, [x8, #0x270]
    // 0x8940e8: LoadField: r9 = r8->field_7
    //     0x8940e8: ldur            x9, [x8, #7]
    // 0x8940ec: r3 = Null
    //     0x8940ec: add             x3, PP, #0x31, lsl #12  ; [pp+0x313e8] Null
    //     0x8940f0: ldr             x3, [x3, #0x3e8]
    // 0x8940f4: blr             x9
    // 0x8940f8: ldur            x0, [fp, #-0x18]
    // 0x8940fc: ldur            x1, [fp, #-8]
    // 0x894100: StoreField: r1->field_f = r0
    //     0x894100: stur            w0, [x1, #0xf]
    //     0x894104: ldurb           w16, [x1, #-1]
    //     0x894108: ldurb           w17, [x0, #-1]
    //     0x89410c: and             x16, x17, x16, lsr #2
    //     0x894110: tst             x16, HEAP, lsr #32
    //     0x894114: b.eq            #0x89411c
    //     0x894118: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x89411c: r0 = Null
    //     0x89411c: mov             x0, NULL
    // 0x894120: LeaveFrame
    //     0x894120: mov             SP, fp
    //     0x894124: ldp             fp, lr, [SP], #0x10
    // 0x894128: ret
    //     0x894128: ret             
    // 0x89412c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89412c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x894130: b               #0x894020
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeaff58, size: 0xc0
    // 0xeaff58: EnterFrame
    //     0xeaff58: stp             fp, lr, [SP, #-0x10]!
    //     0xeaff5c: mov             fp, SP
    // 0xeaff60: AllocStack(0x10)
    //     0xeaff60: sub             SP, SP, #0x10
    // 0xeaff64: SetupParameters(SequenceParser2<C1X0, C1X1> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */)
    //     0xeaff64: mov             x5, x1
    //     0xeaff68: mov             x4, x2
    //     0xeaff6c: stur            x1, [fp, #-8]
    //     0xeaff70: stur            x2, [fp, #-0x10]
    // 0xeaff74: CheckStackOverflow
    //     0xeaff74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaff78: cmp             SP, x16
    //     0xeaff7c: b.ls            #0xeb0010
    // 0xeaff80: LoadField: r1 = r5->field_b
    //     0xeaff80: ldur            w1, [x5, #0xb]
    // 0xeaff84: DecompressPointer r1
    //     0xeaff84: add             x1, x1, HEAP, lsl #32
    // 0xeaff88: r0 = LoadClassIdInstr(r1)
    //     0xeaff88: ldur            x0, [x1, #-1]
    //     0xeaff8c: ubfx            x0, x0, #0xc, #0x14
    // 0xeaff90: mov             x2, x4
    // 0xeaff94: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeaff94: sub             lr, x0, #0xfce
    //     0xeaff98: ldr             lr, [x21, lr, lsl #3]
    //     0xeaff9c: blr             lr
    // 0xeaffa0: r3 = LoadInt32Instr(r0)
    //     0xeaffa0: sbfx            x3, x0, #1, #0x1f
    //     0xeaffa4: tbz             w0, #0, #0xeaffac
    //     0xeaffa8: ldur            x3, [x0, #7]
    // 0xeaffac: tbz             x3, #0x3f, #0xeaffc0
    // 0xeaffb0: r0 = -2
    //     0xeaffb0: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeaffb4: LeaveFrame
    //     0xeaffb4: mov             SP, fp
    //     0xeaffb8: ldp             fp, lr, [SP], #0x10
    // 0xeaffbc: ret
    //     0xeaffbc: ret             
    // 0xeaffc0: ldur            x0, [fp, #-8]
    // 0xeaffc4: LoadField: r1 = r0->field_f
    //     0xeaffc4: ldur            w1, [x0, #0xf]
    // 0xeaffc8: DecompressPointer r1
    //     0xeaffc8: add             x1, x1, HEAP, lsl #32
    // 0xeaffcc: r0 = LoadClassIdInstr(r1)
    //     0xeaffcc: ldur            x0, [x1, #-1]
    //     0xeaffd0: ubfx            x0, x0, #0xc, #0x14
    // 0xeaffd4: ldur            x2, [fp, #-0x10]
    // 0xeaffd8: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeaffd8: sub             lr, x0, #0xfce
    //     0xeaffdc: ldr             lr, [x21, lr, lsl #3]
    //     0xeaffe0: blr             lr
    // 0xeaffe4: r1 = LoadInt32Instr(r0)
    //     0xeaffe4: sbfx            x1, x0, #1, #0x1f
    //     0xeaffe8: tbz             w0, #0, #0xeafff0
    //     0xeaffec: ldur            x1, [x0, #7]
    // 0xeafff0: tbz             x1, #0x3f, #0xeb0004
    // 0xeafff4: r0 = -2
    //     0xeafff4: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeafff8: LeaveFrame
    //     0xeafff8: mov             SP, fp
    //     0xeafffc: ldp             fp, lr, [SP], #0x10
    // 0xeb0000: ret
    //     0xeb0000: ret             
    // 0xeb0004: LeaveFrame
    //     0xeb0004: mov             SP, fp
    //     0xeb0008: ldp             fp, lr, [SP], #0x10
    // 0xeb000c: ret
    //     0xeb000c: ret             
    // 0xeb0010: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0010: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0014: b               #0xeaff80
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb1f1c, size: 0x180
    // 0xeb1f1c: EnterFrame
    //     0xeb1f1c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb1f20: mov             fp, SP
    // 0xeb1f24: AllocStack(0x28)
    //     0xeb1f24: sub             SP, SP, #0x28
    // 0xeb1f28: SetupParameters(SequenceParser2<C1X0, C1X1> this /* r1 => r3, fp-0x8 */)
    //     0xeb1f28: mov             x3, x1
    //     0xeb1f2c: stur            x1, [fp, #-8]
    // 0xeb1f30: CheckStackOverflow
    //     0xeb1f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb1f34: cmp             SP, x16
    //     0xeb1f38: b.ls            #0xeb2094
    // 0xeb1f3c: LoadField: r1 = r3->field_b
    //     0xeb1f3c: ldur            w1, [x3, #0xb]
    // 0xeb1f40: DecompressPointer r1
    //     0xeb1f40: add             x1, x1, HEAP, lsl #32
    // 0xeb1f44: r0 = LoadClassIdInstr(r1)
    //     0xeb1f44: ldur            x0, [x1, #-1]
    //     0xeb1f48: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1f4c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1f4c: sub             lr, x0, #1, lsl #12
    //     0xeb1f50: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1f54: blr             lr
    // 0xeb1f58: mov             x3, x0
    // 0xeb1f5c: stur            x3, [fp, #-0x18]
    // 0xeb1f60: r4 = LoadClassIdInstr(r3)
    //     0xeb1f60: ldur            x4, [x3, #-1]
    //     0xeb1f64: ubfx            x4, x4, #0xc, #0x14
    // 0xeb1f68: stur            x4, [fp, #-0x10]
    // 0xeb1f6c: cmp             x4, #0x2f3
    // 0xeb1f70: b.ne            #0xeb1f84
    // 0xeb1f74: mov             x0, x3
    // 0xeb1f78: LeaveFrame
    //     0xeb1f78: mov             SP, fp
    //     0xeb1f7c: ldp             fp, lr, [SP], #0x10
    // 0xeb1f80: ret
    //     0xeb1f80: ret             
    // 0xeb1f84: ldur            x5, [fp, #-8]
    // 0xeb1f88: LoadField: r1 = r5->field_f
    //     0xeb1f88: ldur            w1, [x5, #0xf]
    // 0xeb1f8c: DecompressPointer r1
    //     0xeb1f8c: add             x1, x1, HEAP, lsl #32
    // 0xeb1f90: r0 = LoadClassIdInstr(r1)
    //     0xeb1f90: ldur            x0, [x1, #-1]
    //     0xeb1f94: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1f98: mov             x2, x3
    // 0xeb1f9c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1f9c: sub             lr, x0, #1, lsl #12
    //     0xeb1fa0: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1fa4: blr             lr
    // 0xeb1fa8: stur            x0, [fp, #-0x28]
    // 0xeb1fac: r1 = LoadClassIdInstr(r0)
    //     0xeb1fac: ldur            x1, [x0, #-1]
    //     0xeb1fb0: ubfx            x1, x1, #0xc, #0x14
    // 0xeb1fb4: cmp             x1, #0x2f3
    // 0xeb1fb8: b.ne            #0xeb1fc8
    // 0xeb1fbc: LeaveFrame
    //     0xeb1fbc: mov             SP, fp
    //     0xeb1fc0: ldp             fp, lr, [SP], #0x10
    // 0xeb1fc4: ret
    //     0xeb1fc4: ret             
    // 0xeb1fc8: ldur            x3, [fp, #-8]
    // 0xeb1fcc: ldur            x2, [fp, #-0x10]
    // 0xeb1fd0: LoadField: r4 = r3->field_7
    //     0xeb1fd0: ldur            w4, [x3, #7]
    // 0xeb1fd4: DecompressPointer r4
    //     0xeb1fd4: add             x4, x4, HEAP, lsl #32
    // 0xeb1fd8: stur            x4, [fp, #-0x20]
    // 0xeb1fdc: cmp             x2, #0x2f3
    // 0xeb1fe0: b.eq            #0xeb2058
    // 0xeb1fe4: ldur            x2, [fp, #-0x18]
    // 0xeb1fe8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xeb1fe8: ldur            w3, [x2, #0x17]
    // 0xeb1fec: DecompressPointer r3
    //     0xeb1fec: add             x3, x3, HEAP, lsl #32
    // 0xeb1ff0: cmp             x1, #0x2f3
    // 0xeb1ff4: b.eq            #0xeb2078
    // 0xeb1ff8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb1ff8: ldur            w1, [x0, #0x17]
    // 0xeb1ffc: DecompressPointer r1
    //     0xeb1ffc: add             x1, x1, HEAP, lsl #32
    // 0xeb2000: LoadField: r5 = r0->field_7
    //     0xeb2000: ldur            w5, [x0, #7]
    // 0xeb2004: DecompressPointer r5
    //     0xeb2004: add             x5, x5, HEAP, lsl #32
    // 0xeb2008: stur            x5, [fp, #-8]
    // 0xeb200c: LoadField: r6 = r0->field_b
    //     0xeb200c: ldur            x6, [x0, #0xb]
    // 0xeb2010: mov             x2, x3
    // 0xeb2014: mov             x3, x1
    // 0xeb2018: stur            x6, [fp, #-0x10]
    // 0xeb201c: r0 = AllocateRecord2()
    //     0xeb201c: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0xeb2020: ldur            x1, [fp, #-0x20]
    // 0xeb2024: stur            x0, [fp, #-0x20]
    // 0xeb2028: r0 = Success()
    //     0xeb2028: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb202c: mov             x1, x0
    // 0xeb2030: ldur            x0, [fp, #-0x20]
    // 0xeb2034: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb2034: stur            w0, [x1, #0x17]
    // 0xeb2038: ldur            x0, [fp, #-8]
    // 0xeb203c: StoreField: r1->field_7 = r0
    //     0xeb203c: stur            w0, [x1, #7]
    // 0xeb2040: ldur            x0, [fp, #-0x10]
    // 0xeb2044: StoreField: r1->field_b = r0
    //     0xeb2044: stur            x0, [x1, #0xb]
    // 0xeb2048: mov             x0, x1
    // 0xeb204c: LeaveFrame
    //     0xeb204c: mov             SP, fp
    //     0xeb2050: ldp             fp, lr, [SP], #0x10
    // 0xeb2054: ret
    //     0xeb2054: ret             
    // 0xeb2058: ldur            x2, [fp, #-0x18]
    // 0xeb205c: r0 = ParserException()
    //     0xeb205c: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2060: mov             x1, x0
    // 0xeb2064: ldur            x0, [fp, #-0x18]
    // 0xeb2068: StoreField: r1->field_7 = r0
    //     0xeb2068: stur            w0, [x1, #7]
    // 0xeb206c: mov             x0, x1
    // 0xeb2070: r0 = Throw()
    //     0xeb2070: bl              #0xec04b8  ; ThrowStub
    // 0xeb2074: brk             #0
    // 0xeb2078: r0 = ParserException()
    //     0xeb2078: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb207c: mov             x1, x0
    // 0xeb2080: ldur            x0, [fp, #-0x28]
    // 0xeb2084: StoreField: r1->field_7 = r0
    //     0xeb2084: stur            w0, [x1, #7]
    // 0xeb2088: mov             x0, x1
    // 0xeb208c: r0 = Throw()
    //     0xeb208c: bl              #0xec04b8  ; ThrowStub
    // 0xeb2090: brk             #0
    // 0xeb2094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb2094: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb2098: b               #0xeb1f3c
  }
}
