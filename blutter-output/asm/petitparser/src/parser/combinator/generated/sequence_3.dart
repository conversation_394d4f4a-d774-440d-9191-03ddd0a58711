// lib: , url: package:petitparser/src/parser/combinator/generated/sequence_3.dart

// class id: 1050898, size: 0x8
class :: {

  static Parser<Y3> RecordParserExtension3.map3<Y0, Y1, Y2, Y3>(Parser<(Y0, Y1, Y2)>, (dynamic, Y0, Y1, Y2) => Y3) {
    // ** addr: 0x88b790, size: 0xbc
    // 0x88b790: EnterFrame
    //     0x88b790: stp             fp, lr, [SP, #-0x10]!
    //     0x88b794: mov             fp, SP
    // 0x88b798: AllocStack(0x28)
    //     0x88b798: sub             SP, SP, #0x28
    // 0x88b79c: SetupParameters()
    //     0x88b79c: ldur            w0, [x4, #0xf]
    //     0x88b7a0: cbnz            w0, #0x88b7ac
    //     0x88b7a4: mov             x1, NULL
    //     0x88b7a8: b               #0x88b7b8
    //     0x88b7ac: ldur            w0, [x4, #0x17]
    //     0x88b7b0: add             x1, fp, w0, sxtw #2
    //     0x88b7b4: ldr             x1, [x1, #0x10]
    //     0x88b7b8: ldr             x0, [fp, #0x10]
    //     0x88b7bc: stur            x1, [fp, #-8]
    // 0x88b7c0: CheckStackOverflow
    //     0x88b7c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b7c4: cmp             SP, x16
    //     0x88b7c8: b.ls            #0x88b844
    // 0x88b7cc: r1 = 1
    //     0x88b7cc: movz            x1, #0x1
    // 0x88b7d0: r0 = AllocateContext()
    //     0x88b7d0: bl              #0xec126c  ; AllocateContextStub
    // 0x88b7d4: mov             x4, x0
    // 0x88b7d8: ldr             x0, [fp, #0x10]
    // 0x88b7dc: stur            x4, [fp, #-0x10]
    // 0x88b7e0: StoreField: r4->field_f = r0
    //     0x88b7e0: stur            w0, [x4, #0xf]
    // 0x88b7e4: ldur            x1, [fp, #-8]
    // 0x88b7e8: r2 = Null
    //     0x88b7e8: mov             x2, NULL
    // 0x88b7ec: r3 = <(Y0, Y1, Y2), Y3>
    //     0x88b7ec: add             x3, PP, #0x26, lsl #12  ; [pp+0x267c0] TypeArguments: <(Y0, Y1, Y2), Y3>
    //     0x88b7f0: ldr             x3, [x3, #0x7c0]
    // 0x88b7f4: r30 = InstantiateTypeArgumentsStub
    //     0x88b7f4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88b7f8: LoadField: r30 = r30->field_7
    //     0x88b7f8: ldur            lr, [lr, #7]
    // 0x88b7fc: blr             lr
    // 0x88b800: ldur            x2, [fp, #-0x10]
    // 0x88b804: r1 = Function '<anonymous closure>': static.
    //     0x88b804: add             x1, PP, #0x26, lsl #12  ; [pp+0x267c8] AnonymousClosure: static (0x88b84c), in [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordParserExtension3.map3 (0x88b790)
    //     0x88b808: ldr             x1, [x1, #0x7c8]
    // 0x88b80c: stur            x0, [fp, #-0x10]
    // 0x88b810: r0 = AllocateClosure()
    //     0x88b810: bl              #0xec1630  ; AllocateClosureStub
    // 0x88b814: mov             x1, x0
    // 0x88b818: ldur            x0, [fp, #-8]
    // 0x88b81c: StoreField: r1->field_b = r0
    //     0x88b81c: stur            w0, [x1, #0xb]
    // 0x88b820: ldur            x16, [fp, #-0x10]
    // 0x88b824: ldr             lr, [fp, #0x18]
    // 0x88b828: stp             lr, x16, [SP, #8]
    // 0x88b82c: str             x1, [SP]
    // 0x88b830: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88b830: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88b834: r0 = MapParserExtension.map()
    //     0x88b834: bl              #0x88ab3c  ; [package:petitparser/src/parser/action/map.dart] ::MapParserExtension.map
    // 0x88b838: LeaveFrame
    //     0x88b838: mov             SP, fp
    //     0x88b83c: ldp             fp, lr, [SP], #0x10
    // 0x88b840: ret
    //     0x88b840: ret             
    // 0x88b844: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b844: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b848: b               #0x88b7cc
  }
  [closure] static Y3 <anonymous closure>(dynamic, (Y0, Y1, Y2)) {
    // ** addr: 0x88b84c, size: 0x70
    // 0x88b84c: EnterFrame
    //     0x88b84c: stp             fp, lr, [SP, #-0x10]!
    //     0x88b850: mov             fp, SP
    // 0x88b854: AllocStack(0x20)
    //     0x88b854: sub             SP, SP, #0x20
    // 0x88b858: SetupParameters()
    //     0x88b858: ldr             x0, [fp, #0x18]
    //     0x88b85c: ldur            w1, [x0, #0x17]
    //     0x88b860: add             x1, x1, HEAP, lsl #32
    // 0x88b864: CheckStackOverflow
    //     0x88b864: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b868: cmp             SP, x16
    //     0x88b86c: b.ls            #0x88b8b4
    // 0x88b870: LoadField: r0 = r1->field_f
    //     0x88b870: ldur            w0, [x1, #0xf]
    // 0x88b874: DecompressPointer r0
    //     0x88b874: add             x0, x0, HEAP, lsl #32
    // 0x88b878: ldr             x1, [fp, #0x10]
    // 0x88b87c: LoadField: r2 = r1->field_f
    //     0x88b87c: ldur            w2, [x1, #0xf]
    // 0x88b880: DecompressPointer r2
    //     0x88b880: add             x2, x2, HEAP, lsl #32
    // 0x88b884: LoadField: r3 = r1->field_13
    //     0x88b884: ldur            w3, [x1, #0x13]
    // 0x88b888: DecompressPointer r3
    //     0x88b888: add             x3, x3, HEAP, lsl #32
    // 0x88b88c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x88b88c: ldur            w4, [x1, #0x17]
    // 0x88b890: DecompressPointer r4
    //     0x88b890: add             x4, x4, HEAP, lsl #32
    // 0x88b894: stp             x2, x0, [SP, #0x10]
    // 0x88b898: stp             x4, x3, [SP]
    // 0x88b89c: ClosureCall
    //     0x88b89c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0x88b8a0: ldur            x2, [x0, #0x1f]
    //     0x88b8a4: blr             x2
    // 0x88b8a8: LeaveFrame
    //     0x88b8a8: mov             SP, fp
    //     0x88b8ac: ldp             fp, lr, [SP], #0x10
    // 0x88b8b0: ret
    //     0x88b8b0: ret             
    // 0x88b8b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b8b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b8b8: b               #0x88b870
  }
  static Parser<(Y0, Y1, Y2)> seq3<Y0, Y1, Y2>(Parser<Y0>, Parser<Y1>, Parser<Y2>) {
    // ** addr: 0x88b8bc, size: 0x74
    // 0x88b8bc: EnterFrame
    //     0x88b8bc: stp             fp, lr, [SP, #-0x10]!
    //     0x88b8c0: mov             fp, SP
    // 0x88b8c4: LoadField: r0 = r4->field_f
    //     0x88b8c4: ldur            w0, [x4, #0xf]
    // 0x88b8c8: cbnz            w0, #0x88b8d4
    // 0x88b8cc: r1 = Null
    //     0x88b8cc: mov             x1, NULL
    // 0x88b8d0: b               #0x88b8e0
    // 0x88b8d4: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88b8d4: ldur            w0, [x4, #0x17]
    // 0x88b8d8: add             x1, fp, w0, sxtw #2
    // 0x88b8dc: ldr             x1, [x1, #0x10]
    // 0x88b8e0: ldr             x5, [fp, #0x20]
    // 0x88b8e4: ldr             x4, [fp, #0x18]
    // 0x88b8e8: ldr             x0, [fp, #0x10]
    // 0x88b8ec: r2 = Null
    //     0x88b8ec: mov             x2, NULL
    // 0x88b8f0: r3 = <(Y0, Y1, Y2), Y0, Y1, Y2>
    //     0x88b8f0: add             x3, PP, #0x26, lsl #12  ; [pp+0x267d0] TypeArguments: <(Y0, Y1, Y2), Y0, Y1, Y2>
    //     0x88b8f4: ldr             x3, [x3, #0x7d0]
    // 0x88b8f8: r30 = InstantiateTypeArgumentsStub
    //     0x88b8f8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88b8fc: LoadField: r30 = r30->field_7
    //     0x88b8fc: ldur            lr, [lr, #7]
    // 0x88b900: blr             lr
    // 0x88b904: mov             x1, x0
    // 0x88b908: r0 = SequenceParser3()
    //     0x88b908: bl              #0x88b930  ; AllocateSequenceParser3Stub -> SequenceParser3<C1X0, C1X1, C1X2> (size=0x18)
    // 0x88b90c: ldr             x1, [fp, #0x20]
    // 0x88b910: StoreField: r0->field_b = r1
    //     0x88b910: stur            w1, [x0, #0xb]
    // 0x88b914: ldr             x1, [fp, #0x18]
    // 0x88b918: StoreField: r0->field_f = r1
    //     0x88b918: stur            w1, [x0, #0xf]
    // 0x88b91c: ldr             x1, [fp, #0x10]
    // 0x88b920: StoreField: r0->field_13 = r1
    //     0x88b920: stur            w1, [x0, #0x13]
    // 0x88b924: LeaveFrame
    //     0x88b924: mov             SP, fp
    //     0x88b928: ldp             fp, lr, [SP], #0x10
    // 0x88b92c: ret
    //     0x88b92c: ret             
  }
  static Parser<(Y0, Y1, Y2)> RecordOfParsersExtension3.toSequenceParser<Y0, Y1, Y2>((Parser<Y0>, Parser<Y1>, Parser<Y2>)) {
    // ** addr: 0x88df40, size: 0x98
    // 0x88df40: EnterFrame
    //     0x88df40: stp             fp, lr, [SP, #-0x10]!
    //     0x88df44: mov             fp, SP
    // 0x88df48: AllocStack(0x18)
    //     0x88df48: sub             SP, SP, #0x18
    // 0x88df4c: SetupParameters()
    //     0x88df4c: ldur            w0, [x4, #0xf]
    //     0x88df50: cbnz            w0, #0x88df5c
    //     0x88df54: mov             x1, NULL
    //     0x88df58: b               #0x88df68
    //     0x88df5c: ldur            w0, [x4, #0x17]
    //     0x88df60: add             x1, fp, w0, sxtw #2
    //     0x88df64: ldr             x1, [x1, #0x10]
    // 0x88df68: ldr             x0, [fp, #0x10]
    // 0x88df6c: r2 = Null
    //     0x88df6c: mov             x2, NULL
    // 0x88df70: r3 = <(Y0, Y1, Y2), Y0, Y1, Y2>
    //     0x88df70: add             x3, PP, #0x26, lsl #12  ; [pp+0x269f8] TypeArguments: <(Y0, Y1, Y2), Y0, Y1, Y2>
    //     0x88df74: ldr             x3, [x3, #0x9f8]
    // 0x88df78: r30 = InstantiateTypeArgumentsStub
    //     0x88df78: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88df7c: LoadField: r30 = r30->field_7
    //     0x88df7c: ldur            lr, [lr, #7]
    // 0x88df80: blr             lr
    // 0x88df84: mov             x1, x0
    // 0x88df88: ldr             x0, [fp, #0x10]
    // 0x88df8c: LoadField: r2 = r0->field_f
    //     0x88df8c: ldur            w2, [x0, #0xf]
    // 0x88df90: DecompressPointer r2
    //     0x88df90: add             x2, x2, HEAP, lsl #32
    // 0x88df94: stur            x2, [fp, #-0x18]
    // 0x88df98: LoadField: r3 = r0->field_13
    //     0x88df98: ldur            w3, [x0, #0x13]
    // 0x88df9c: DecompressPointer r3
    //     0x88df9c: add             x3, x3, HEAP, lsl #32
    // 0x88dfa0: stur            x3, [fp, #-0x10]
    // 0x88dfa4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x88dfa4: ldur            w4, [x0, #0x17]
    // 0x88dfa8: DecompressPointer r4
    //     0x88dfa8: add             x4, x4, HEAP, lsl #32
    // 0x88dfac: stur            x4, [fp, #-8]
    // 0x88dfb0: r0 = SequenceParser3()
    //     0x88dfb0: bl              #0x88b930  ; AllocateSequenceParser3Stub -> SequenceParser3<C1X0, C1X1, C1X2> (size=0x18)
    // 0x88dfb4: ldur            x1, [fp, #-0x18]
    // 0x88dfb8: StoreField: r0->field_b = r1
    //     0x88dfb8: stur            w1, [x0, #0xb]
    // 0x88dfbc: ldur            x1, [fp, #-0x10]
    // 0x88dfc0: StoreField: r0->field_f = r1
    //     0x88dfc0: stur            w1, [x0, #0xf]
    // 0x88dfc4: ldur            x1, [fp, #-8]
    // 0x88dfc8: StoreField: r0->field_13 = r1
    //     0x88dfc8: stur            w1, [x0, #0x13]
    // 0x88dfcc: LeaveFrame
    //     0x88dfcc: mov             SP, fp
    //     0x88dfd0: ldp             fp, lr, [SP], #0x10
    // 0x88dfd4: ret
    //     0x88dfd4: ret             
  }
}

// class id: 737, size: 0x18, field offset: 0xc
class SequenceParser3<C1X0, C1X1, C1X2> extends Parser<C1X0>
    implements SequentialParser {

  get _ children(/* No info */) {
    // ** addr: 0x88fa14, size: 0x88
    // 0x88fa14: EnterFrame
    //     0x88fa14: stp             fp, lr, [SP, #-0x10]!
    //     0x88fa18: mov             fp, SP
    // 0x88fa1c: AllocStack(0x20)
    //     0x88fa1c: sub             SP, SP, #0x20
    // 0x88fa20: r0 = 6
    //     0x88fa20: movz            x0, #0x6
    // 0x88fa24: LoadField: r3 = r1->field_b
    //     0x88fa24: ldur            w3, [x1, #0xb]
    // 0x88fa28: DecompressPointer r3
    //     0x88fa28: add             x3, x3, HEAP, lsl #32
    // 0x88fa2c: stur            x3, [fp, #-0x18]
    // 0x88fa30: LoadField: r4 = r1->field_f
    //     0x88fa30: ldur            w4, [x1, #0xf]
    // 0x88fa34: DecompressPointer r4
    //     0x88fa34: add             x4, x4, HEAP, lsl #32
    // 0x88fa38: stur            x4, [fp, #-0x10]
    // 0x88fa3c: LoadField: r5 = r1->field_13
    //     0x88fa3c: ldur            w5, [x1, #0x13]
    // 0x88fa40: DecompressPointer r5
    //     0x88fa40: add             x5, x5, HEAP, lsl #32
    // 0x88fa44: mov             x2, x0
    // 0x88fa48: stur            x5, [fp, #-8]
    // 0x88fa4c: r1 = Null
    //     0x88fa4c: mov             x1, NULL
    // 0x88fa50: r0 = AllocateArray()
    //     0x88fa50: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88fa54: mov             x2, x0
    // 0x88fa58: ldur            x0, [fp, #-0x18]
    // 0x88fa5c: stur            x2, [fp, #-0x20]
    // 0x88fa60: StoreField: r2->field_f = r0
    //     0x88fa60: stur            w0, [x2, #0xf]
    // 0x88fa64: ldur            x0, [fp, #-0x10]
    // 0x88fa68: StoreField: r2->field_13 = r0
    //     0x88fa68: stur            w0, [x2, #0x13]
    // 0x88fa6c: ldur            x0, [fp, #-8]
    // 0x88fa70: ArrayStore: r2[0] = r0  ; List_4
    //     0x88fa70: stur            w0, [x2, #0x17]
    // 0x88fa74: r1 = <Parser>
    //     0x88fa74: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x88fa78: ldr             x1, [x1, #0x6f8]
    // 0x88fa7c: r0 = AllocateGrowableArray()
    //     0x88fa7c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88fa80: ldur            x1, [fp, #-0x20]
    // 0x88fa84: StoreField: r0->field_f = r1
    //     0x88fa84: stur            w1, [x0, #0xf]
    // 0x88fa88: r1 = 6
    //     0x88fa88: movz            x1, #0x6
    // 0x88fa8c: StoreField: r0->field_b = r1
    //     0x88fa8c: stur            w1, [x0, #0xb]
    // 0x88fa90: LeaveFrame
    //     0x88fa90: mov             SP, fp
    //     0x88fa94: ldp             fp, lr, [SP], #0x10
    // 0x88fa98: ret
    //     0x88fa98: ret             
  }
  _ replace(/* No info */) {
    // ** addr: 0x894134, size: 0x1c8
    // 0x894134: EnterFrame
    //     0x894134: stp             fp, lr, [SP, #-0x10]!
    //     0x894138: mov             fp, SP
    // 0x89413c: AllocStack(0x28)
    //     0x89413c: sub             SP, SP, #0x28
    // 0x894140: SetupParameters(SequenceParser3<C1X0, C1X1, C1X2> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0x894140: stur            x1, [fp, #-8]
    //     0x894144: mov             x16, x3
    //     0x894148: mov             x3, x1
    //     0x89414c: mov             x1, x16
    //     0x894150: stur            x2, [fp, #-0x10]
    //     0x894154: stur            x1, [fp, #-0x18]
    // 0x894158: CheckStackOverflow
    //     0x894158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89415c: cmp             SP, x16
    //     0x894160: b.ls            #0x8942f4
    // 0x894164: LoadField: r0 = r3->field_b
    //     0x894164: ldur            w0, [x3, #0xb]
    // 0x894168: DecompressPointer r0
    //     0x894168: add             x0, x0, HEAP, lsl #32
    // 0x89416c: r4 = LoadClassIdInstr(r0)
    //     0x89416c: ldur            x4, [x0, #-1]
    //     0x894170: ubfx            x4, x4, #0xc, #0x14
    // 0x894174: stp             x2, x0, [SP]
    // 0x894178: mov             x0, x4
    // 0x89417c: mov             lr, x0
    // 0x894180: ldr             lr, [x21, lr, lsl #3]
    // 0x894184: blr             lr
    // 0x894188: tbnz            w0, #4, #0x8941e0
    // 0x89418c: ldur            x3, [fp, #-8]
    // 0x894190: LoadField: r2 = r3->field_7
    //     0x894190: ldur            w2, [x3, #7]
    // 0x894194: DecompressPointer r2
    //     0x894194: add             x2, x2, HEAP, lsl #32
    // 0x894198: ldur            x0, [fp, #-0x18]
    // 0x89419c: r1 = Null
    //     0x89419c: mov             x1, NULL
    // 0x8941a0: r8 = Parser<C1X0>
    //     0x8941a0: add             x8, PP, #0x31, lsl #12  ; [pp+0x31258] Type: Parser<C1X0>
    //     0x8941a4: ldr             x8, [x8, #0x258]
    // 0x8941a8: LoadField: r9 = r8->field_7
    //     0x8941a8: ldur            x9, [x8, #7]
    // 0x8941ac: r3 = Null
    //     0x8941ac: add             x3, PP, #0x31, lsl #12  ; [pp+0x313a8] Null
    //     0x8941b0: ldr             x3, [x3, #0x3a8]
    // 0x8941b4: blr             x9
    // 0x8941b8: ldur            x0, [fp, #-0x18]
    // 0x8941bc: ldur            x1, [fp, #-8]
    // 0x8941c0: StoreField: r1->field_b = r0
    //     0x8941c0: stur            w0, [x1, #0xb]
    //     0x8941c4: ldurb           w16, [x1, #-1]
    //     0x8941c8: ldurb           w17, [x0, #-1]
    //     0x8941cc: and             x16, x17, x16, lsr #2
    //     0x8941d0: tst             x16, HEAP, lsr #32
    //     0x8941d4: b.eq            #0x8941dc
    //     0x8941d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8941dc: b               #0x8941e4
    // 0x8941e0: ldur            x1, [fp, #-8]
    // 0x8941e4: LoadField: r0 = r1->field_f
    //     0x8941e4: ldur            w0, [x1, #0xf]
    // 0x8941e8: DecompressPointer r0
    //     0x8941e8: add             x0, x0, HEAP, lsl #32
    // 0x8941ec: r2 = LoadClassIdInstr(r0)
    //     0x8941ec: ldur            x2, [x0, #-1]
    //     0x8941f0: ubfx            x2, x2, #0xc, #0x14
    // 0x8941f4: ldur            x16, [fp, #-0x10]
    // 0x8941f8: stp             x16, x0, [SP]
    // 0x8941fc: mov             x0, x2
    // 0x894200: mov             lr, x0
    // 0x894204: ldr             lr, [x21, lr, lsl #3]
    // 0x894208: blr             lr
    // 0x89420c: tbnz            w0, #4, #0x894264
    // 0x894210: ldur            x3, [fp, #-8]
    // 0x894214: LoadField: r2 = r3->field_7
    //     0x894214: ldur            w2, [x3, #7]
    // 0x894218: DecompressPointer r2
    //     0x894218: add             x2, x2, HEAP, lsl #32
    // 0x89421c: ldur            x0, [fp, #-0x18]
    // 0x894220: r1 = Null
    //     0x894220: mov             x1, NULL
    // 0x894224: r8 = Parser<C1X1>
    //     0x894224: add             x8, PP, #0x31, lsl #12  ; [pp+0x31270] Type: Parser<C1X1>
    //     0x894228: ldr             x8, [x8, #0x270]
    // 0x89422c: LoadField: r9 = r8->field_7
    //     0x89422c: ldur            x9, [x8, #7]
    // 0x894230: r3 = Null
    //     0x894230: add             x3, PP, #0x31, lsl #12  ; [pp+0x313b8] Null
    //     0x894234: ldr             x3, [x3, #0x3b8]
    // 0x894238: blr             x9
    // 0x89423c: ldur            x0, [fp, #-0x18]
    // 0x894240: ldur            x1, [fp, #-8]
    // 0x894244: StoreField: r1->field_f = r0
    //     0x894244: stur            w0, [x1, #0xf]
    //     0x894248: ldurb           w16, [x1, #-1]
    //     0x89424c: ldurb           w17, [x0, #-1]
    //     0x894250: and             x16, x17, x16, lsr #2
    //     0x894254: tst             x16, HEAP, lsr #32
    //     0x894258: b.eq            #0x894260
    //     0x89425c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894260: b               #0x894268
    // 0x894264: ldur            x1, [fp, #-8]
    // 0x894268: LoadField: r0 = r1->field_13
    //     0x894268: ldur            w0, [x1, #0x13]
    // 0x89426c: DecompressPointer r0
    //     0x89426c: add             x0, x0, HEAP, lsl #32
    // 0x894270: r2 = LoadClassIdInstr(r0)
    //     0x894270: ldur            x2, [x0, #-1]
    //     0x894274: ubfx            x2, x2, #0xc, #0x14
    // 0x894278: ldur            x16, [fp, #-0x10]
    // 0x89427c: stp             x16, x0, [SP]
    // 0x894280: mov             x0, x2
    // 0x894284: mov             lr, x0
    // 0x894288: ldr             lr, [x21, lr, lsl #3]
    // 0x89428c: blr             lr
    // 0x894290: tbnz            w0, #4, #0x8942e4
    // 0x894294: ldur            x3, [fp, #-8]
    // 0x894298: LoadField: r2 = r3->field_7
    //     0x894298: ldur            w2, [x3, #7]
    // 0x89429c: DecompressPointer r2
    //     0x89429c: add             x2, x2, HEAP, lsl #32
    // 0x8942a0: ldur            x0, [fp, #-0x18]
    // 0x8942a4: r1 = Null
    //     0x8942a4: mov             x1, NULL
    // 0x8942a8: r8 = Parser<C1X2>
    //     0x8942a8: add             x8, PP, #0x31, lsl #12  ; [pp+0x31288] Type: Parser<C1X2>
    //     0x8942ac: ldr             x8, [x8, #0x288]
    // 0x8942b0: LoadField: r9 = r8->field_7
    //     0x8942b0: ldur            x9, [x8, #7]
    // 0x8942b4: r3 = Null
    //     0x8942b4: add             x3, PP, #0x31, lsl #12  ; [pp+0x313c8] Null
    //     0x8942b8: ldr             x3, [x3, #0x3c8]
    // 0x8942bc: blr             x9
    // 0x8942c0: ldur            x0, [fp, #-0x18]
    // 0x8942c4: ldur            x1, [fp, #-8]
    // 0x8942c8: StoreField: r1->field_13 = r0
    //     0x8942c8: stur            w0, [x1, #0x13]
    //     0x8942cc: ldurb           w16, [x1, #-1]
    //     0x8942d0: ldurb           w17, [x0, #-1]
    //     0x8942d4: and             x16, x17, x16, lsr #2
    //     0x8942d8: tst             x16, HEAP, lsr #32
    //     0x8942dc: b.eq            #0x8942e4
    //     0x8942e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8942e4: r0 = Null
    //     0x8942e4: mov             x0, NULL
    // 0x8942e8: LeaveFrame
    //     0x8942e8: mov             SP, fp
    //     0x8942ec: ldp             fp, lr, [SP], #0x10
    // 0x8942f0: ret
    //     0x8942f0: ret             
    // 0x8942f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8942f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8942f8: b               #0x894164
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb0018, size: 0x104
    // 0xeb0018: EnterFrame
    //     0xeb0018: stp             fp, lr, [SP, #-0x10]!
    //     0xeb001c: mov             fp, SP
    // 0xeb0020: AllocStack(0x10)
    //     0xeb0020: sub             SP, SP, #0x10
    // 0xeb0024: SetupParameters(SequenceParser3<C1X0, C1X1, C1X2> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */)
    //     0xeb0024: mov             x5, x1
    //     0xeb0028: mov             x4, x2
    //     0xeb002c: stur            x1, [fp, #-8]
    //     0xeb0030: stur            x2, [fp, #-0x10]
    // 0xeb0034: CheckStackOverflow
    //     0xeb0034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0038: cmp             SP, x16
    //     0xeb003c: b.ls            #0xeb0114
    // 0xeb0040: LoadField: r1 = r5->field_b
    //     0xeb0040: ldur            w1, [x5, #0xb]
    // 0xeb0044: DecompressPointer r1
    //     0xeb0044: add             x1, x1, HEAP, lsl #32
    // 0xeb0048: r0 = LoadClassIdInstr(r1)
    //     0xeb0048: ldur            x0, [x1, #-1]
    //     0xeb004c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0050: mov             x2, x4
    // 0xeb0054: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb0054: sub             lr, x0, #0xfce
    //     0xeb0058: ldr             lr, [x21, lr, lsl #3]
    //     0xeb005c: blr             lr
    // 0xeb0060: r3 = LoadInt32Instr(r0)
    //     0xeb0060: sbfx            x3, x0, #1, #0x1f
    //     0xeb0064: tbz             w0, #0, #0xeb006c
    //     0xeb0068: ldur            x3, [x0, #7]
    // 0xeb006c: tbz             x3, #0x3f, #0xeb0080
    // 0xeb0070: r0 = -2
    //     0xeb0070: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0074: LeaveFrame
    //     0xeb0074: mov             SP, fp
    //     0xeb0078: ldp             fp, lr, [SP], #0x10
    // 0xeb007c: ret
    //     0xeb007c: ret             
    // 0xeb0080: ldur            x4, [fp, #-8]
    // 0xeb0084: LoadField: r1 = r4->field_f
    //     0xeb0084: ldur            w1, [x4, #0xf]
    // 0xeb0088: DecompressPointer r1
    //     0xeb0088: add             x1, x1, HEAP, lsl #32
    // 0xeb008c: r0 = LoadClassIdInstr(r1)
    //     0xeb008c: ldur            x0, [x1, #-1]
    //     0xeb0090: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0094: ldur            x2, [fp, #-0x10]
    // 0xeb0098: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb0098: sub             lr, x0, #0xfce
    //     0xeb009c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb00a0: blr             lr
    // 0xeb00a4: r3 = LoadInt32Instr(r0)
    //     0xeb00a4: sbfx            x3, x0, #1, #0x1f
    //     0xeb00a8: tbz             w0, #0, #0xeb00b0
    //     0xeb00ac: ldur            x3, [x0, #7]
    // 0xeb00b0: tbz             x3, #0x3f, #0xeb00c4
    // 0xeb00b4: r0 = -2
    //     0xeb00b4: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb00b8: LeaveFrame
    //     0xeb00b8: mov             SP, fp
    //     0xeb00bc: ldp             fp, lr, [SP], #0x10
    // 0xeb00c0: ret
    //     0xeb00c0: ret             
    // 0xeb00c4: ldur            x0, [fp, #-8]
    // 0xeb00c8: LoadField: r1 = r0->field_13
    //     0xeb00c8: ldur            w1, [x0, #0x13]
    // 0xeb00cc: DecompressPointer r1
    //     0xeb00cc: add             x1, x1, HEAP, lsl #32
    // 0xeb00d0: r0 = LoadClassIdInstr(r1)
    //     0xeb00d0: ldur            x0, [x1, #-1]
    //     0xeb00d4: ubfx            x0, x0, #0xc, #0x14
    // 0xeb00d8: ldur            x2, [fp, #-0x10]
    // 0xeb00dc: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb00dc: sub             lr, x0, #0xfce
    //     0xeb00e0: ldr             lr, [x21, lr, lsl #3]
    //     0xeb00e4: blr             lr
    // 0xeb00e8: r1 = LoadInt32Instr(r0)
    //     0xeb00e8: sbfx            x1, x0, #1, #0x1f
    //     0xeb00ec: tbz             w0, #0, #0xeb00f4
    //     0xeb00f0: ldur            x1, [x0, #7]
    // 0xeb00f4: tbz             x1, #0x3f, #0xeb0108
    // 0xeb00f8: r0 = -2
    //     0xeb00f8: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb00fc: LeaveFrame
    //     0xeb00fc: mov             SP, fp
    //     0xeb0100: ldp             fp, lr, [SP], #0x10
    // 0xeb0104: ret
    //     0xeb0104: ret             
    // 0xeb0108: LeaveFrame
    //     0xeb0108: mov             SP, fp
    //     0xeb010c: ldp             fp, lr, [SP], #0x10
    // 0xeb0110: ret
    //     0xeb0110: ret             
    // 0xeb0114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0118: b               #0xeb0040
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb209c, size: 0x208
    // 0xeb209c: EnterFrame
    //     0xeb209c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb20a0: mov             fp, SP
    // 0xeb20a4: AllocStack(0x38)
    //     0xeb20a4: sub             SP, SP, #0x38
    // 0xeb20a8: SetupParameters(SequenceParser3<C1X0, C1X1, C1X2> this /* r1 => r3, fp-0x8 */)
    //     0xeb20a8: mov             x3, x1
    //     0xeb20ac: stur            x1, [fp, #-8]
    // 0xeb20b0: CheckStackOverflow
    //     0xeb20b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb20b4: cmp             SP, x16
    //     0xeb20b8: b.ls            #0xeb229c
    // 0xeb20bc: LoadField: r1 = r3->field_b
    //     0xeb20bc: ldur            w1, [x3, #0xb]
    // 0xeb20c0: DecompressPointer r1
    //     0xeb20c0: add             x1, x1, HEAP, lsl #32
    // 0xeb20c4: r0 = LoadClassIdInstr(r1)
    //     0xeb20c4: ldur            x0, [x1, #-1]
    //     0xeb20c8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb20cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb20cc: sub             lr, x0, #1, lsl #12
    //     0xeb20d0: ldr             lr, [x21, lr, lsl #3]
    //     0xeb20d4: blr             lr
    // 0xeb20d8: mov             x3, x0
    // 0xeb20dc: stur            x3, [fp, #-0x18]
    // 0xeb20e0: r4 = LoadClassIdInstr(r3)
    //     0xeb20e0: ldur            x4, [x3, #-1]
    //     0xeb20e4: ubfx            x4, x4, #0xc, #0x14
    // 0xeb20e8: stur            x4, [fp, #-0x10]
    // 0xeb20ec: cmp             x4, #0x2f3
    // 0xeb20f0: b.ne            #0xeb2104
    // 0xeb20f4: mov             x0, x3
    // 0xeb20f8: LeaveFrame
    //     0xeb20f8: mov             SP, fp
    //     0xeb20fc: ldp             fp, lr, [SP], #0x10
    // 0xeb2100: ret
    //     0xeb2100: ret             
    // 0xeb2104: ldur            x5, [fp, #-8]
    // 0xeb2108: LoadField: r1 = r5->field_f
    //     0xeb2108: ldur            w1, [x5, #0xf]
    // 0xeb210c: DecompressPointer r1
    //     0xeb210c: add             x1, x1, HEAP, lsl #32
    // 0xeb2110: r0 = LoadClassIdInstr(r1)
    //     0xeb2110: ldur            x0, [x1, #-1]
    //     0xeb2114: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2118: mov             x2, x3
    // 0xeb211c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb211c: sub             lr, x0, #1, lsl #12
    //     0xeb2120: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2124: blr             lr
    // 0xeb2128: mov             x3, x0
    // 0xeb212c: stur            x3, [fp, #-0x28]
    // 0xeb2130: r4 = LoadClassIdInstr(r3)
    //     0xeb2130: ldur            x4, [x3, #-1]
    //     0xeb2134: ubfx            x4, x4, #0xc, #0x14
    // 0xeb2138: stur            x4, [fp, #-0x20]
    // 0xeb213c: cmp             x4, #0x2f3
    // 0xeb2140: b.ne            #0xeb2154
    // 0xeb2144: mov             x0, x3
    // 0xeb2148: LeaveFrame
    //     0xeb2148: mov             SP, fp
    //     0xeb214c: ldp             fp, lr, [SP], #0x10
    // 0xeb2150: ret
    //     0xeb2150: ret             
    // 0xeb2154: ldur            x5, [fp, #-8]
    // 0xeb2158: LoadField: r1 = r5->field_13
    //     0xeb2158: ldur            w1, [x5, #0x13]
    // 0xeb215c: DecompressPointer r1
    //     0xeb215c: add             x1, x1, HEAP, lsl #32
    // 0xeb2160: r0 = LoadClassIdInstr(r1)
    //     0xeb2160: ldur            x0, [x1, #-1]
    //     0xeb2164: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2168: mov             x2, x3
    // 0xeb216c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb216c: sub             lr, x0, #1, lsl #12
    //     0xeb2170: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2174: blr             lr
    // 0xeb2178: stur            x0, [fp, #-0x38]
    // 0xeb217c: r1 = LoadClassIdInstr(r0)
    //     0xeb217c: ldur            x1, [x0, #-1]
    //     0xeb2180: ubfx            x1, x1, #0xc, #0x14
    // 0xeb2184: cmp             x1, #0x2f3
    // 0xeb2188: b.ne            #0xeb2198
    // 0xeb218c: LeaveFrame
    //     0xeb218c: mov             SP, fp
    //     0xeb2190: ldp             fp, lr, [SP], #0x10
    // 0xeb2194: ret
    //     0xeb2194: ret             
    // 0xeb2198: ldur            x2, [fp, #-8]
    // 0xeb219c: ldur            x3, [fp, #-0x10]
    // 0xeb21a0: LoadField: r5 = r2->field_7
    //     0xeb21a0: ldur            w5, [x2, #7]
    // 0xeb21a4: DecompressPointer r5
    //     0xeb21a4: add             x5, x5, HEAP, lsl #32
    // 0xeb21a8: stur            x5, [fp, #-0x30]
    // 0xeb21ac: cmp             x3, #0x2f3
    // 0xeb21b0: b.eq            #0xeb2240
    // 0xeb21b4: ldur            x3, [fp, #-0x18]
    // 0xeb21b8: ldur            x2, [fp, #-0x20]
    // 0xeb21bc: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xeb21bc: ldur            w4, [x3, #0x17]
    // 0xeb21c0: DecompressPointer r4
    //     0xeb21c0: add             x4, x4, HEAP, lsl #32
    // 0xeb21c4: cmp             x2, #0x2f3
    // 0xeb21c8: b.eq            #0xeb2260
    // 0xeb21cc: ldur            x2, [fp, #-0x28]
    // 0xeb21d0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xeb21d0: ldur            w3, [x2, #0x17]
    // 0xeb21d4: DecompressPointer r3
    //     0xeb21d4: add             x3, x3, HEAP, lsl #32
    // 0xeb21d8: cmp             x1, #0x2f3
    // 0xeb21dc: b.eq            #0xeb2280
    // 0xeb21e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb21e0: ldur            w1, [x0, #0x17]
    // 0xeb21e4: DecompressPointer r1
    //     0xeb21e4: add             x1, x1, HEAP, lsl #32
    // 0xeb21e8: LoadField: r6 = r0->field_7
    //     0xeb21e8: ldur            w6, [x0, #7]
    // 0xeb21ec: DecompressPointer r6
    //     0xeb21ec: add             x6, x6, HEAP, lsl #32
    // 0xeb21f0: stur            x6, [fp, #-8]
    // 0xeb21f4: LoadField: r7 = r0->field_b
    //     0xeb21f4: ldur            x7, [x0, #0xb]
    // 0xeb21f8: mov             x2, x4
    // 0xeb21fc: mov             x4, x1
    // 0xeb2200: stur            x7, [fp, #-0x10]
    // 0xeb2204: r0 = AllocateRecord3()
    //     0xeb2204: bl              #0xec0e44  ; AllocateRecord3Stub
    // 0xeb2208: ldur            x1, [fp, #-0x30]
    // 0xeb220c: stur            x0, [fp, #-0x30]
    // 0xeb2210: r0 = Success()
    //     0xeb2210: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb2214: mov             x1, x0
    // 0xeb2218: ldur            x0, [fp, #-0x30]
    // 0xeb221c: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb221c: stur            w0, [x1, #0x17]
    // 0xeb2220: ldur            x0, [fp, #-8]
    // 0xeb2224: StoreField: r1->field_7 = r0
    //     0xeb2224: stur            w0, [x1, #7]
    // 0xeb2228: ldur            x0, [fp, #-0x10]
    // 0xeb222c: StoreField: r1->field_b = r0
    //     0xeb222c: stur            x0, [x1, #0xb]
    // 0xeb2230: mov             x0, x1
    // 0xeb2234: LeaveFrame
    //     0xeb2234: mov             SP, fp
    //     0xeb2238: ldp             fp, lr, [SP], #0x10
    // 0xeb223c: ret
    //     0xeb223c: ret             
    // 0xeb2240: ldur            x3, [fp, #-0x18]
    // 0xeb2244: r0 = ParserException()
    //     0xeb2244: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2248: mov             x1, x0
    // 0xeb224c: ldur            x0, [fp, #-0x18]
    // 0xeb2250: StoreField: r1->field_7 = r0
    //     0xeb2250: stur            w0, [x1, #7]
    // 0xeb2254: mov             x0, x1
    // 0xeb2258: r0 = Throw()
    //     0xeb2258: bl              #0xec04b8  ; ThrowStub
    // 0xeb225c: brk             #0
    // 0xeb2260: ldur            x2, [fp, #-0x28]
    // 0xeb2264: r0 = ParserException()
    //     0xeb2264: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2268: mov             x1, x0
    // 0xeb226c: ldur            x0, [fp, #-0x28]
    // 0xeb2270: StoreField: r1->field_7 = r0
    //     0xeb2270: stur            w0, [x1, #7]
    // 0xeb2274: mov             x0, x1
    // 0xeb2278: r0 = Throw()
    //     0xeb2278: bl              #0xec04b8  ; ThrowStub
    // 0xeb227c: brk             #0
    // 0xeb2280: r0 = ParserException()
    //     0xeb2280: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2284: mov             x1, x0
    // 0xeb2288: ldur            x0, [fp, #-0x38]
    // 0xeb228c: StoreField: r1->field_7 = r0
    //     0xeb228c: stur            w0, [x1, #7]
    // 0xeb2290: mov             x0, x1
    // 0xeb2294: r0 = Throw()
    //     0xeb2294: bl              #0xec04b8  ; ThrowStub
    // 0xeb2298: brk             #0
    // 0xeb229c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb229c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb22a0: b               #0xeb20bc
  }
}
