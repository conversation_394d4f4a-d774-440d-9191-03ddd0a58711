// lib: , url: package:petitparser/src/parser/combinator/generated/sequence_4.dart

// class id: 1050899, size: 0x8
class :: {

  static Parser<Y4> RecordParserExtension4.map4<Y0, Y1, Y2, Y3, Y4>(Parser<(Y0, Y1, Y2, Y3)>, (dynamic, Y0, Y1, Y2, Y3) => Y4) {
    // ** addr: 0x88e34c, size: 0xbc
    // 0x88e34c: EnterFrame
    //     0x88e34c: stp             fp, lr, [SP, #-0x10]!
    //     0x88e350: mov             fp, SP
    // 0x88e354: AllocStack(0x28)
    //     0x88e354: sub             SP, SP, #0x28
    // 0x88e358: SetupParameters()
    //     0x88e358: ldur            w0, [x4, #0xf]
    //     0x88e35c: cbnz            w0, #0x88e368
    //     0x88e360: mov             x1, NULL
    //     0x88e364: b               #0x88e374
    //     0x88e368: ldur            w0, [x4, #0x17]
    //     0x88e36c: add             x1, fp, w0, sxtw #2
    //     0x88e370: ldr             x1, [x1, #0x10]
    //     0x88e374: ldr             x0, [fp, #0x10]
    //     0x88e378: stur            x1, [fp, #-8]
    // 0x88e37c: CheckStackOverflow
    //     0x88e37c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e380: cmp             SP, x16
    //     0x88e384: b.ls            #0x88e400
    // 0x88e388: r1 = 1
    //     0x88e388: movz            x1, #0x1
    // 0x88e38c: r0 = AllocateContext()
    //     0x88e38c: bl              #0xec126c  ; AllocateContextStub
    // 0x88e390: mov             x4, x0
    // 0x88e394: ldr             x0, [fp, #0x10]
    // 0x88e398: stur            x4, [fp, #-0x10]
    // 0x88e39c: StoreField: r4->field_f = r0
    //     0x88e39c: stur            w0, [x4, #0xf]
    // 0x88e3a0: ldur            x1, [fp, #-8]
    // 0x88e3a4: r2 = Null
    //     0x88e3a4: mov             x2, NULL
    // 0x88e3a8: r3 = <(Y0, Y1, Y2, Y3), Y4>
    //     0x88e3a8: add             x3, PP, #0x26, lsl #12  ; [pp+0x26a90] TypeArguments: <(Y0, Y1, Y2, Y3), Y4>
    //     0x88e3ac: ldr             x3, [x3, #0xa90]
    // 0x88e3b0: r30 = InstantiateTypeArgumentsStub
    //     0x88e3b0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88e3b4: LoadField: r30 = r30->field_7
    //     0x88e3b4: ldur            lr, [lr, #7]
    // 0x88e3b8: blr             lr
    // 0x88e3bc: ldur            x2, [fp, #-0x10]
    // 0x88e3c0: r1 = Function '<anonymous closure>': static.
    //     0x88e3c0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a98] AnonymousClosure: static (0x88e408), in [package:petitparser/src/parser/combinator/generated/sequence_4.dart] ::RecordParserExtension4.map4 (0x88e34c)
    //     0x88e3c4: ldr             x1, [x1, #0xa98]
    // 0x88e3c8: stur            x0, [fp, #-0x10]
    // 0x88e3cc: r0 = AllocateClosure()
    //     0x88e3cc: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e3d0: mov             x1, x0
    // 0x88e3d4: ldur            x0, [fp, #-8]
    // 0x88e3d8: StoreField: r1->field_b = r0
    //     0x88e3d8: stur            w0, [x1, #0xb]
    // 0x88e3dc: ldur            x16, [fp, #-0x10]
    // 0x88e3e0: ldr             lr, [fp, #0x18]
    // 0x88e3e4: stp             lr, x16, [SP, #8]
    // 0x88e3e8: str             x1, [SP]
    // 0x88e3ec: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88e3ec: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88e3f0: r0 = MapParserExtension.map()
    //     0x88e3f0: bl              #0x88ab3c  ; [package:petitparser/src/parser/action/map.dart] ::MapParserExtension.map
    // 0x88e3f4: LeaveFrame
    //     0x88e3f4: mov             SP, fp
    //     0x88e3f8: ldp             fp, lr, [SP], #0x10
    // 0x88e3fc: ret
    //     0x88e3fc: ret             
    // 0x88e400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e404: b               #0x88e388
  }
  [closure] static Y4 <anonymous closure>(dynamic, (Y0, Y1, Y2, Y3)) {
    // ** addr: 0x88e408, size: 0x7c
    // 0x88e408: EnterFrame
    //     0x88e408: stp             fp, lr, [SP, #-0x10]!
    //     0x88e40c: mov             fp, SP
    // 0x88e410: AllocStack(0x28)
    //     0x88e410: sub             SP, SP, #0x28
    // 0x88e414: SetupParameters()
    //     0x88e414: ldr             x0, [fp, #0x18]
    //     0x88e418: ldur            w1, [x0, #0x17]
    //     0x88e41c: add             x1, x1, HEAP, lsl #32
    // 0x88e420: CheckStackOverflow
    //     0x88e420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e424: cmp             SP, x16
    //     0x88e428: b.ls            #0x88e47c
    // 0x88e42c: LoadField: r0 = r1->field_f
    //     0x88e42c: ldur            w0, [x1, #0xf]
    // 0x88e430: DecompressPointer r0
    //     0x88e430: add             x0, x0, HEAP, lsl #32
    // 0x88e434: ldr             x1, [fp, #0x10]
    // 0x88e438: LoadField: r2 = r1->field_f
    //     0x88e438: ldur            w2, [x1, #0xf]
    // 0x88e43c: DecompressPointer r2
    //     0x88e43c: add             x2, x2, HEAP, lsl #32
    // 0x88e440: LoadField: r3 = r1->field_13
    //     0x88e440: ldur            w3, [x1, #0x13]
    // 0x88e444: DecompressPointer r3
    //     0x88e444: add             x3, x3, HEAP, lsl #32
    // 0x88e448: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x88e448: ldur            w4, [x1, #0x17]
    // 0x88e44c: DecompressPointer r4
    //     0x88e44c: add             x4, x4, HEAP, lsl #32
    // 0x88e450: LoadField: r5 = r1->field_1b
    //     0x88e450: ldur            w5, [x1, #0x1b]
    // 0x88e454: DecompressPointer r5
    //     0x88e454: add             x5, x5, HEAP, lsl #32
    // 0x88e458: stp             x2, x0, [SP, #0x18]
    // 0x88e45c: stp             x4, x3, [SP, #8]
    // 0x88e460: str             x5, [SP]
    // 0x88e464: ClosureCall
    //     0x88e464: ldr             x4, [PP, #0xce0]  ; [pp+0xce0] List(5) [0, 0x5, 0x5, 0x5, Null]
    //     0x88e468: ldur            x2, [x0, #0x1f]
    //     0x88e46c: blr             x2
    // 0x88e470: LeaveFrame
    //     0x88e470: mov             SP, fp
    //     0x88e474: ldp             fp, lr, [SP], #0x10
    // 0x88e478: ret
    //     0x88e478: ret             
    // 0x88e47c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e47c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e480: b               #0x88e42c
  }
  static Parser<(Y0, Y1, Y2, Y3)> seq4<Y0, Y1, Y2, Y3>(Parser<Y0>, Parser<Y1>, Parser<Y2>, Parser<Y3>) {
    // ** addr: 0x88e484, size: 0x80
    // 0x88e484: EnterFrame
    //     0x88e484: stp             fp, lr, [SP, #-0x10]!
    //     0x88e488: mov             fp, SP
    // 0x88e48c: LoadField: r0 = r4->field_f
    //     0x88e48c: ldur            w0, [x4, #0xf]
    // 0x88e490: cbnz            w0, #0x88e49c
    // 0x88e494: r1 = Null
    //     0x88e494: mov             x1, NULL
    // 0x88e498: b               #0x88e4a8
    // 0x88e49c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88e49c: ldur            w0, [x4, #0x17]
    // 0x88e4a0: add             x1, fp, w0, sxtw #2
    // 0x88e4a4: ldr             x1, [x1, #0x10]
    // 0x88e4a8: ldr             x6, [fp, #0x28]
    // 0x88e4ac: ldr             x5, [fp, #0x20]
    // 0x88e4b0: ldr             x4, [fp, #0x18]
    // 0x88e4b4: ldr             x0, [fp, #0x10]
    // 0x88e4b8: r2 = Null
    //     0x88e4b8: mov             x2, NULL
    // 0x88e4bc: r3 = <(Y0, Y1, Y2, Y3), Y0, Y1, Y2, Y3>
    //     0x88e4bc: add             x3, PP, #0x26, lsl #12  ; [pp+0x26aa0] TypeArguments: <(Y0, Y1, Y2, Y3), Y0, Y1, Y2, Y3>
    //     0x88e4c0: ldr             x3, [x3, #0xaa0]
    // 0x88e4c4: r30 = InstantiateTypeArgumentsStub
    //     0x88e4c4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88e4c8: LoadField: r30 = r30->field_7
    //     0x88e4c8: ldur            lr, [lr, #7]
    // 0x88e4cc: blr             lr
    // 0x88e4d0: mov             x1, x0
    // 0x88e4d4: r0 = SequenceParser4()
    //     0x88e4d4: bl              #0x88e504  ; AllocateSequenceParser4Stub -> SequenceParser4<C1X0, C1X1, C1X2, C1X3> (size=0x1c)
    // 0x88e4d8: ldr             x1, [fp, #0x28]
    // 0x88e4dc: StoreField: r0->field_b = r1
    //     0x88e4dc: stur            w1, [x0, #0xb]
    // 0x88e4e0: ldr             x1, [fp, #0x20]
    // 0x88e4e4: StoreField: r0->field_f = r1
    //     0x88e4e4: stur            w1, [x0, #0xf]
    // 0x88e4e8: ldr             x1, [fp, #0x18]
    // 0x88e4ec: StoreField: r0->field_13 = r1
    //     0x88e4ec: stur            w1, [x0, #0x13]
    // 0x88e4f0: ldr             x1, [fp, #0x10]
    // 0x88e4f4: ArrayStore: r0[0] = r1  ; List_4
    //     0x88e4f4: stur            w1, [x0, #0x17]
    // 0x88e4f8: LeaveFrame
    //     0x88e4f8: mov             SP, fp
    //     0x88e4fc: ldp             fp, lr, [SP], #0x10
    // 0x88e500: ret
    //     0x88e500: ret             
  }
}

// class id: 736, size: 0x1c, field offset: 0xc
class SequenceParser4<C1X0, C1X1, C1X2, C1X3> extends Parser<C1X0>
    implements SequentialParser {

  get _ children(/* No info */) {
    // ** addr: 0x88fa9c, size: 0x9c
    // 0x88fa9c: EnterFrame
    //     0x88fa9c: stp             fp, lr, [SP, #-0x10]!
    //     0x88faa0: mov             fp, SP
    // 0x88faa4: AllocStack(0x28)
    //     0x88faa4: sub             SP, SP, #0x28
    // 0x88faa8: r0 = 8
    //     0x88faa8: movz            x0, #0x8
    // 0x88faac: LoadField: r3 = r1->field_b
    //     0x88faac: ldur            w3, [x1, #0xb]
    // 0x88fab0: DecompressPointer r3
    //     0x88fab0: add             x3, x3, HEAP, lsl #32
    // 0x88fab4: stur            x3, [fp, #-0x20]
    // 0x88fab8: LoadField: r4 = r1->field_f
    //     0x88fab8: ldur            w4, [x1, #0xf]
    // 0x88fabc: DecompressPointer r4
    //     0x88fabc: add             x4, x4, HEAP, lsl #32
    // 0x88fac0: stur            x4, [fp, #-0x18]
    // 0x88fac4: LoadField: r5 = r1->field_13
    //     0x88fac4: ldur            w5, [x1, #0x13]
    // 0x88fac8: DecompressPointer r5
    //     0x88fac8: add             x5, x5, HEAP, lsl #32
    // 0x88facc: stur            x5, [fp, #-0x10]
    // 0x88fad0: ArrayLoad: r6 = r1[0]  ; List_4
    //     0x88fad0: ldur            w6, [x1, #0x17]
    // 0x88fad4: DecompressPointer r6
    //     0x88fad4: add             x6, x6, HEAP, lsl #32
    // 0x88fad8: mov             x2, x0
    // 0x88fadc: stur            x6, [fp, #-8]
    // 0x88fae0: r1 = Null
    //     0x88fae0: mov             x1, NULL
    // 0x88fae4: r0 = AllocateArray()
    //     0x88fae4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88fae8: mov             x2, x0
    // 0x88faec: ldur            x0, [fp, #-0x20]
    // 0x88faf0: stur            x2, [fp, #-0x28]
    // 0x88faf4: StoreField: r2->field_f = r0
    //     0x88faf4: stur            w0, [x2, #0xf]
    // 0x88faf8: ldur            x0, [fp, #-0x18]
    // 0x88fafc: StoreField: r2->field_13 = r0
    //     0x88fafc: stur            w0, [x2, #0x13]
    // 0x88fb00: ldur            x0, [fp, #-0x10]
    // 0x88fb04: ArrayStore: r2[0] = r0  ; List_4
    //     0x88fb04: stur            w0, [x2, #0x17]
    // 0x88fb08: ldur            x0, [fp, #-8]
    // 0x88fb0c: StoreField: r2->field_1b = r0
    //     0x88fb0c: stur            w0, [x2, #0x1b]
    // 0x88fb10: r1 = <Parser>
    //     0x88fb10: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x88fb14: ldr             x1, [x1, #0x6f8]
    // 0x88fb18: r0 = AllocateGrowableArray()
    //     0x88fb18: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88fb1c: ldur            x1, [fp, #-0x28]
    // 0x88fb20: StoreField: r0->field_f = r1
    //     0x88fb20: stur            w1, [x0, #0xf]
    // 0x88fb24: r1 = 8
    //     0x88fb24: movz            x1, #0x8
    // 0x88fb28: StoreField: r0->field_b = r1
    //     0x88fb28: stur            w1, [x0, #0xb]
    // 0x88fb2c: LeaveFrame
    //     0x88fb2c: mov             SP, fp
    //     0x88fb30: ldp             fp, lr, [SP], #0x10
    // 0x88fb34: ret
    //     0x88fb34: ret             
  }
  _ replace(/* No info */) {
    // ** addr: 0x8943dc, size: 0x24c
    // 0x8943dc: EnterFrame
    //     0x8943dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8943e0: mov             fp, SP
    // 0x8943e4: AllocStack(0x28)
    //     0x8943e4: sub             SP, SP, #0x28
    // 0x8943e8: SetupParameters(SequenceParser4<C1X0, C1X1, C1X2, C1X3> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0x8943e8: stur            x1, [fp, #-8]
    //     0x8943ec: mov             x16, x3
    //     0x8943f0: mov             x3, x1
    //     0x8943f4: mov             x1, x16
    //     0x8943f8: stur            x2, [fp, #-0x10]
    //     0x8943fc: stur            x1, [fp, #-0x18]
    // 0x894400: CheckStackOverflow
    //     0x894400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x894404: cmp             SP, x16
    //     0x894408: b.ls            #0x894620
    // 0x89440c: LoadField: r0 = r3->field_b
    //     0x89440c: ldur            w0, [x3, #0xb]
    // 0x894410: DecompressPointer r0
    //     0x894410: add             x0, x0, HEAP, lsl #32
    // 0x894414: r4 = LoadClassIdInstr(r0)
    //     0x894414: ldur            x4, [x0, #-1]
    //     0x894418: ubfx            x4, x4, #0xc, #0x14
    // 0x89441c: stp             x2, x0, [SP]
    // 0x894420: mov             x0, x4
    // 0x894424: mov             lr, x0
    // 0x894428: ldr             lr, [x21, lr, lsl #3]
    // 0x89442c: blr             lr
    // 0x894430: tbnz            w0, #4, #0x894488
    // 0x894434: ldur            x3, [fp, #-8]
    // 0x894438: LoadField: r2 = r3->field_7
    //     0x894438: ldur            w2, [x3, #7]
    // 0x89443c: DecompressPointer r2
    //     0x89443c: add             x2, x2, HEAP, lsl #32
    // 0x894440: ldur            x0, [fp, #-0x18]
    // 0x894444: r1 = Null
    //     0x894444: mov             x1, NULL
    // 0x894448: r8 = Parser<C1X0>
    //     0x894448: add             x8, PP, #0x31, lsl #12  ; [pp+0x31258] Type: Parser<C1X0>
    //     0x89444c: ldr             x8, [x8, #0x258]
    // 0x894450: LoadField: r9 = r8->field_7
    //     0x894450: ldur            x9, [x8, #7]
    // 0x894454: r3 = Null
    //     0x894454: add             x3, PP, #0x31, lsl #12  ; [pp+0x31368] Null
    //     0x894458: ldr             x3, [x3, #0x368]
    // 0x89445c: blr             x9
    // 0x894460: ldur            x0, [fp, #-0x18]
    // 0x894464: ldur            x1, [fp, #-8]
    // 0x894468: StoreField: r1->field_b = r0
    //     0x894468: stur            w0, [x1, #0xb]
    //     0x89446c: ldurb           w16, [x1, #-1]
    //     0x894470: ldurb           w17, [x0, #-1]
    //     0x894474: and             x16, x17, x16, lsr #2
    //     0x894478: tst             x16, HEAP, lsr #32
    //     0x89447c: b.eq            #0x894484
    //     0x894480: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894484: b               #0x89448c
    // 0x894488: ldur            x1, [fp, #-8]
    // 0x89448c: LoadField: r0 = r1->field_f
    //     0x89448c: ldur            w0, [x1, #0xf]
    // 0x894490: DecompressPointer r0
    //     0x894490: add             x0, x0, HEAP, lsl #32
    // 0x894494: r2 = LoadClassIdInstr(r0)
    //     0x894494: ldur            x2, [x0, #-1]
    //     0x894498: ubfx            x2, x2, #0xc, #0x14
    // 0x89449c: ldur            x16, [fp, #-0x10]
    // 0x8944a0: stp             x16, x0, [SP]
    // 0x8944a4: mov             x0, x2
    // 0x8944a8: mov             lr, x0
    // 0x8944ac: ldr             lr, [x21, lr, lsl #3]
    // 0x8944b0: blr             lr
    // 0x8944b4: tbnz            w0, #4, #0x89450c
    // 0x8944b8: ldur            x3, [fp, #-8]
    // 0x8944bc: LoadField: r2 = r3->field_7
    //     0x8944bc: ldur            w2, [x3, #7]
    // 0x8944c0: DecompressPointer r2
    //     0x8944c0: add             x2, x2, HEAP, lsl #32
    // 0x8944c4: ldur            x0, [fp, #-0x18]
    // 0x8944c8: r1 = Null
    //     0x8944c8: mov             x1, NULL
    // 0x8944cc: r8 = Parser<C1X1>
    //     0x8944cc: add             x8, PP, #0x31, lsl #12  ; [pp+0x31270] Type: Parser<C1X1>
    //     0x8944d0: ldr             x8, [x8, #0x270]
    // 0x8944d4: LoadField: r9 = r8->field_7
    //     0x8944d4: ldur            x9, [x8, #7]
    // 0x8944d8: r3 = Null
    //     0x8944d8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31378] Null
    //     0x8944dc: ldr             x3, [x3, #0x378]
    // 0x8944e0: blr             x9
    // 0x8944e4: ldur            x0, [fp, #-0x18]
    // 0x8944e8: ldur            x1, [fp, #-8]
    // 0x8944ec: StoreField: r1->field_f = r0
    //     0x8944ec: stur            w0, [x1, #0xf]
    //     0x8944f0: ldurb           w16, [x1, #-1]
    //     0x8944f4: ldurb           w17, [x0, #-1]
    //     0x8944f8: and             x16, x17, x16, lsr #2
    //     0x8944fc: tst             x16, HEAP, lsr #32
    //     0x894500: b.eq            #0x894508
    //     0x894504: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894508: b               #0x894510
    // 0x89450c: ldur            x1, [fp, #-8]
    // 0x894510: LoadField: r0 = r1->field_13
    //     0x894510: ldur            w0, [x1, #0x13]
    // 0x894514: DecompressPointer r0
    //     0x894514: add             x0, x0, HEAP, lsl #32
    // 0x894518: r2 = LoadClassIdInstr(r0)
    //     0x894518: ldur            x2, [x0, #-1]
    //     0x89451c: ubfx            x2, x2, #0xc, #0x14
    // 0x894520: ldur            x16, [fp, #-0x10]
    // 0x894524: stp             x16, x0, [SP]
    // 0x894528: mov             x0, x2
    // 0x89452c: mov             lr, x0
    // 0x894530: ldr             lr, [x21, lr, lsl #3]
    // 0x894534: blr             lr
    // 0x894538: tbnz            w0, #4, #0x894590
    // 0x89453c: ldur            x3, [fp, #-8]
    // 0x894540: LoadField: r2 = r3->field_7
    //     0x894540: ldur            w2, [x3, #7]
    // 0x894544: DecompressPointer r2
    //     0x894544: add             x2, x2, HEAP, lsl #32
    // 0x894548: ldur            x0, [fp, #-0x18]
    // 0x89454c: r1 = Null
    //     0x89454c: mov             x1, NULL
    // 0x894550: r8 = Parser<C1X2>
    //     0x894550: add             x8, PP, #0x31, lsl #12  ; [pp+0x31288] Type: Parser<C1X2>
    //     0x894554: ldr             x8, [x8, #0x288]
    // 0x894558: LoadField: r9 = r8->field_7
    //     0x894558: ldur            x9, [x8, #7]
    // 0x89455c: r3 = Null
    //     0x89455c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31388] Null
    //     0x894560: ldr             x3, [x3, #0x388]
    // 0x894564: blr             x9
    // 0x894568: ldur            x0, [fp, #-0x18]
    // 0x89456c: ldur            x1, [fp, #-8]
    // 0x894570: StoreField: r1->field_13 = r0
    //     0x894570: stur            w0, [x1, #0x13]
    //     0x894574: ldurb           w16, [x1, #-1]
    //     0x894578: ldurb           w17, [x0, #-1]
    //     0x89457c: and             x16, x17, x16, lsr #2
    //     0x894580: tst             x16, HEAP, lsr #32
    //     0x894584: b.eq            #0x89458c
    //     0x894588: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x89458c: b               #0x894594
    // 0x894590: ldur            x1, [fp, #-8]
    // 0x894594: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x894594: ldur            w0, [x1, #0x17]
    // 0x894598: DecompressPointer r0
    //     0x894598: add             x0, x0, HEAP, lsl #32
    // 0x89459c: r2 = LoadClassIdInstr(r0)
    //     0x89459c: ldur            x2, [x0, #-1]
    //     0x8945a0: ubfx            x2, x2, #0xc, #0x14
    // 0x8945a4: ldur            x16, [fp, #-0x10]
    // 0x8945a8: stp             x16, x0, [SP]
    // 0x8945ac: mov             x0, x2
    // 0x8945b0: mov             lr, x0
    // 0x8945b4: ldr             lr, [x21, lr, lsl #3]
    // 0x8945b8: blr             lr
    // 0x8945bc: tbnz            w0, #4, #0x894610
    // 0x8945c0: ldur            x3, [fp, #-8]
    // 0x8945c4: LoadField: r2 = r3->field_7
    //     0x8945c4: ldur            w2, [x3, #7]
    // 0x8945c8: DecompressPointer r2
    //     0x8945c8: add             x2, x2, HEAP, lsl #32
    // 0x8945cc: ldur            x0, [fp, #-0x18]
    // 0x8945d0: r1 = Null
    //     0x8945d0: mov             x1, NULL
    // 0x8945d4: r8 = Parser<C1X3>
    //     0x8945d4: add             x8, PP, #0x31, lsl #12  ; [pp+0x312a0] Type: Parser<C1X3>
    //     0x8945d8: ldr             x8, [x8, #0x2a0]
    // 0x8945dc: LoadField: r9 = r8->field_7
    //     0x8945dc: ldur            x9, [x8, #7]
    // 0x8945e0: r3 = Null
    //     0x8945e0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31398] Null
    //     0x8945e4: ldr             x3, [x3, #0x398]
    // 0x8945e8: blr             x9
    // 0x8945ec: ldur            x0, [fp, #-0x18]
    // 0x8945f0: ldur            x1, [fp, #-8]
    // 0x8945f4: ArrayStore: r1[0] = r0  ; List_4
    //     0x8945f4: stur            w0, [x1, #0x17]
    //     0x8945f8: ldurb           w16, [x1, #-1]
    //     0x8945fc: ldurb           w17, [x0, #-1]
    //     0x894600: and             x16, x17, x16, lsr #2
    //     0x894604: tst             x16, HEAP, lsr #32
    //     0x894608: b.eq            #0x894610
    //     0x89460c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894610: r0 = Null
    //     0x894610: mov             x0, NULL
    // 0x894614: LeaveFrame
    //     0x894614: mov             SP, fp
    //     0x894618: ldp             fp, lr, [SP], #0x10
    // 0x89461c: ret
    //     0x89461c: ret             
    // 0x894620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x894620: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x894624: b               #0x89440c
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb011c, size: 0x148
    // 0xeb011c: EnterFrame
    //     0xeb011c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0120: mov             fp, SP
    // 0xeb0124: AllocStack(0x10)
    //     0xeb0124: sub             SP, SP, #0x10
    // 0xeb0128: SetupParameters(SequenceParser4<C1X0, C1X1, C1X2, C1X3> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */)
    //     0xeb0128: mov             x5, x1
    //     0xeb012c: mov             x4, x2
    //     0xeb0130: stur            x1, [fp, #-8]
    //     0xeb0134: stur            x2, [fp, #-0x10]
    // 0xeb0138: CheckStackOverflow
    //     0xeb0138: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb013c: cmp             SP, x16
    //     0xeb0140: b.ls            #0xeb025c
    // 0xeb0144: LoadField: r1 = r5->field_b
    //     0xeb0144: ldur            w1, [x5, #0xb]
    // 0xeb0148: DecompressPointer r1
    //     0xeb0148: add             x1, x1, HEAP, lsl #32
    // 0xeb014c: r0 = LoadClassIdInstr(r1)
    //     0xeb014c: ldur            x0, [x1, #-1]
    //     0xeb0150: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0154: mov             x2, x4
    // 0xeb0158: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb0158: sub             lr, x0, #0xfce
    //     0xeb015c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0160: blr             lr
    // 0xeb0164: r3 = LoadInt32Instr(r0)
    //     0xeb0164: sbfx            x3, x0, #1, #0x1f
    //     0xeb0168: tbz             w0, #0, #0xeb0170
    //     0xeb016c: ldur            x3, [x0, #7]
    // 0xeb0170: tbz             x3, #0x3f, #0xeb0184
    // 0xeb0174: r0 = -2
    //     0xeb0174: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0178: LeaveFrame
    //     0xeb0178: mov             SP, fp
    //     0xeb017c: ldp             fp, lr, [SP], #0x10
    // 0xeb0180: ret
    //     0xeb0180: ret             
    // 0xeb0184: ldur            x4, [fp, #-8]
    // 0xeb0188: LoadField: r1 = r4->field_f
    //     0xeb0188: ldur            w1, [x4, #0xf]
    // 0xeb018c: DecompressPointer r1
    //     0xeb018c: add             x1, x1, HEAP, lsl #32
    // 0xeb0190: r0 = LoadClassIdInstr(r1)
    //     0xeb0190: ldur            x0, [x1, #-1]
    //     0xeb0194: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0198: ldur            x2, [fp, #-0x10]
    // 0xeb019c: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb019c: sub             lr, x0, #0xfce
    //     0xeb01a0: ldr             lr, [x21, lr, lsl #3]
    //     0xeb01a4: blr             lr
    // 0xeb01a8: r3 = LoadInt32Instr(r0)
    //     0xeb01a8: sbfx            x3, x0, #1, #0x1f
    //     0xeb01ac: tbz             w0, #0, #0xeb01b4
    //     0xeb01b0: ldur            x3, [x0, #7]
    // 0xeb01b4: tbz             x3, #0x3f, #0xeb01c8
    // 0xeb01b8: r0 = -2
    //     0xeb01b8: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb01bc: LeaveFrame
    //     0xeb01bc: mov             SP, fp
    //     0xeb01c0: ldp             fp, lr, [SP], #0x10
    // 0xeb01c4: ret
    //     0xeb01c4: ret             
    // 0xeb01c8: ldur            x4, [fp, #-8]
    // 0xeb01cc: LoadField: r1 = r4->field_13
    //     0xeb01cc: ldur            w1, [x4, #0x13]
    // 0xeb01d0: DecompressPointer r1
    //     0xeb01d0: add             x1, x1, HEAP, lsl #32
    // 0xeb01d4: r0 = LoadClassIdInstr(r1)
    //     0xeb01d4: ldur            x0, [x1, #-1]
    //     0xeb01d8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb01dc: ldur            x2, [fp, #-0x10]
    // 0xeb01e0: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb01e0: sub             lr, x0, #0xfce
    //     0xeb01e4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb01e8: blr             lr
    // 0xeb01ec: r3 = LoadInt32Instr(r0)
    //     0xeb01ec: sbfx            x3, x0, #1, #0x1f
    //     0xeb01f0: tbz             w0, #0, #0xeb01f8
    //     0xeb01f4: ldur            x3, [x0, #7]
    // 0xeb01f8: tbz             x3, #0x3f, #0xeb020c
    // 0xeb01fc: r0 = -2
    //     0xeb01fc: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0200: LeaveFrame
    //     0xeb0200: mov             SP, fp
    //     0xeb0204: ldp             fp, lr, [SP], #0x10
    // 0xeb0208: ret
    //     0xeb0208: ret             
    // 0xeb020c: ldur            x0, [fp, #-8]
    // 0xeb0210: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb0210: ldur            w1, [x0, #0x17]
    // 0xeb0214: DecompressPointer r1
    //     0xeb0214: add             x1, x1, HEAP, lsl #32
    // 0xeb0218: r0 = LoadClassIdInstr(r1)
    //     0xeb0218: ldur            x0, [x1, #-1]
    //     0xeb021c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0220: ldur            x2, [fp, #-0x10]
    // 0xeb0224: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb0224: sub             lr, x0, #0xfce
    //     0xeb0228: ldr             lr, [x21, lr, lsl #3]
    //     0xeb022c: blr             lr
    // 0xeb0230: r1 = LoadInt32Instr(r0)
    //     0xeb0230: sbfx            x1, x0, #1, #0x1f
    //     0xeb0234: tbz             w0, #0, #0xeb023c
    //     0xeb0238: ldur            x1, [x0, #7]
    // 0xeb023c: tbz             x1, #0x3f, #0xeb0250
    // 0xeb0240: r0 = -2
    //     0xeb0240: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0244: LeaveFrame
    //     0xeb0244: mov             SP, fp
    //     0xeb0248: ldp             fp, lr, [SP], #0x10
    // 0xeb024c: ret
    //     0xeb024c: ret             
    // 0xeb0250: LeaveFrame
    //     0xeb0250: mov             SP, fp
    //     0xeb0254: ldp             fp, lr, [SP], #0x10
    // 0xeb0258: ret
    //     0xeb0258: ret             
    // 0xeb025c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb025c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0260: b               #0xeb0144
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb22a4, size: 0x2bc
    // 0xeb22a4: EnterFrame
    //     0xeb22a4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb22a8: mov             fp, SP
    // 0xeb22ac: AllocStack(0x58)
    //     0xeb22ac: sub             SP, SP, #0x58
    // 0xeb22b0: SetupParameters(SequenceParser4<C1X0, C1X1, C1X2, C1X3> this /* r1 => r3, fp-0x8 */)
    //     0xeb22b0: mov             x3, x1
    //     0xeb22b4: stur            x1, [fp, #-8]
    // 0xeb22b8: CheckStackOverflow
    //     0xeb22b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb22bc: cmp             SP, x16
    //     0xeb22c0: b.ls            #0xeb2558
    // 0xeb22c4: LoadField: r1 = r3->field_b
    //     0xeb22c4: ldur            w1, [x3, #0xb]
    // 0xeb22c8: DecompressPointer r1
    //     0xeb22c8: add             x1, x1, HEAP, lsl #32
    // 0xeb22cc: r0 = LoadClassIdInstr(r1)
    //     0xeb22cc: ldur            x0, [x1, #-1]
    //     0xeb22d0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb22d4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb22d4: sub             lr, x0, #1, lsl #12
    //     0xeb22d8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb22dc: blr             lr
    // 0xeb22e0: mov             x3, x0
    // 0xeb22e4: stur            x3, [fp, #-0x18]
    // 0xeb22e8: r4 = LoadClassIdInstr(r3)
    //     0xeb22e8: ldur            x4, [x3, #-1]
    //     0xeb22ec: ubfx            x4, x4, #0xc, #0x14
    // 0xeb22f0: stur            x4, [fp, #-0x10]
    // 0xeb22f4: cmp             x4, #0x2f3
    // 0xeb22f8: b.ne            #0xeb230c
    // 0xeb22fc: mov             x0, x3
    // 0xeb2300: LeaveFrame
    //     0xeb2300: mov             SP, fp
    //     0xeb2304: ldp             fp, lr, [SP], #0x10
    // 0xeb2308: ret
    //     0xeb2308: ret             
    // 0xeb230c: ldur            x5, [fp, #-8]
    // 0xeb2310: LoadField: r1 = r5->field_f
    //     0xeb2310: ldur            w1, [x5, #0xf]
    // 0xeb2314: DecompressPointer r1
    //     0xeb2314: add             x1, x1, HEAP, lsl #32
    // 0xeb2318: r0 = LoadClassIdInstr(r1)
    //     0xeb2318: ldur            x0, [x1, #-1]
    //     0xeb231c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2320: mov             x2, x3
    // 0xeb2324: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2324: sub             lr, x0, #1, lsl #12
    //     0xeb2328: ldr             lr, [x21, lr, lsl #3]
    //     0xeb232c: blr             lr
    // 0xeb2330: mov             x3, x0
    // 0xeb2334: stur            x3, [fp, #-0x28]
    // 0xeb2338: r4 = LoadClassIdInstr(r3)
    //     0xeb2338: ldur            x4, [x3, #-1]
    //     0xeb233c: ubfx            x4, x4, #0xc, #0x14
    // 0xeb2340: stur            x4, [fp, #-0x20]
    // 0xeb2344: cmp             x4, #0x2f3
    // 0xeb2348: b.ne            #0xeb235c
    // 0xeb234c: mov             x0, x3
    // 0xeb2350: LeaveFrame
    //     0xeb2350: mov             SP, fp
    //     0xeb2354: ldp             fp, lr, [SP], #0x10
    // 0xeb2358: ret
    //     0xeb2358: ret             
    // 0xeb235c: ldur            x5, [fp, #-8]
    // 0xeb2360: LoadField: r1 = r5->field_13
    //     0xeb2360: ldur            w1, [x5, #0x13]
    // 0xeb2364: DecompressPointer r1
    //     0xeb2364: add             x1, x1, HEAP, lsl #32
    // 0xeb2368: r0 = LoadClassIdInstr(r1)
    //     0xeb2368: ldur            x0, [x1, #-1]
    //     0xeb236c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2370: mov             x2, x3
    // 0xeb2374: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2374: sub             lr, x0, #1, lsl #12
    //     0xeb2378: ldr             lr, [x21, lr, lsl #3]
    //     0xeb237c: blr             lr
    // 0xeb2380: mov             x3, x0
    // 0xeb2384: stur            x3, [fp, #-0x38]
    // 0xeb2388: r4 = LoadClassIdInstr(r3)
    //     0xeb2388: ldur            x4, [x3, #-1]
    //     0xeb238c: ubfx            x4, x4, #0xc, #0x14
    // 0xeb2390: stur            x4, [fp, #-0x30]
    // 0xeb2394: cmp             x4, #0x2f3
    // 0xeb2398: b.ne            #0xeb23ac
    // 0xeb239c: mov             x0, x3
    // 0xeb23a0: LeaveFrame
    //     0xeb23a0: mov             SP, fp
    //     0xeb23a4: ldp             fp, lr, [SP], #0x10
    // 0xeb23a8: ret
    //     0xeb23a8: ret             
    // 0xeb23ac: ldur            x5, [fp, #-8]
    // 0xeb23b0: ArrayLoad: r1 = r5[0]  ; List_4
    //     0xeb23b0: ldur            w1, [x5, #0x17]
    // 0xeb23b4: DecompressPointer r1
    //     0xeb23b4: add             x1, x1, HEAP, lsl #32
    // 0xeb23b8: r0 = LoadClassIdInstr(r1)
    //     0xeb23b8: ldur            x0, [x1, #-1]
    //     0xeb23bc: ubfx            x0, x0, #0xc, #0x14
    // 0xeb23c0: mov             x2, x3
    // 0xeb23c4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb23c4: sub             lr, x0, #1, lsl #12
    //     0xeb23c8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb23cc: blr             lr
    // 0xeb23d0: stur            x0, [fp, #-0x50]
    // 0xeb23d4: r1 = LoadClassIdInstr(r0)
    //     0xeb23d4: ldur            x1, [x0, #-1]
    //     0xeb23d8: ubfx            x1, x1, #0xc, #0x14
    // 0xeb23dc: stur            x1, [fp, #-0x48]
    // 0xeb23e0: cmp             x1, #0x2f3
    // 0xeb23e4: b.ne            #0xeb23f4
    // 0xeb23e8: LeaveFrame
    //     0xeb23e8: mov             SP, fp
    //     0xeb23ec: ldp             fp, lr, [SP], #0x10
    // 0xeb23f0: ret
    //     0xeb23f0: ret             
    // 0xeb23f4: ldur            x2, [fp, #-8]
    // 0xeb23f8: ldur            x3, [fp, #-0x10]
    // 0xeb23fc: LoadField: r4 = r2->field_7
    //     0xeb23fc: ldur            w4, [x2, #7]
    // 0xeb2400: DecompressPointer r4
    //     0xeb2400: add             x4, x4, HEAP, lsl #32
    // 0xeb2404: stur            x4, [fp, #-0x40]
    // 0xeb2408: cmp             x3, #0x2f3
    // 0xeb240c: b.eq            #0xeb24d8
    // 0xeb2410: ldur            x3, [fp, #-0x18]
    // 0xeb2414: ldur            x2, [fp, #-0x20]
    // 0xeb2418: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xeb2418: ldur            w5, [x3, #0x17]
    // 0xeb241c: DecompressPointer r5
    //     0xeb241c: add             x5, x5, HEAP, lsl #32
    // 0xeb2420: stur            x5, [fp, #-8]
    // 0xeb2424: r1 = 8
    //     0xeb2424: movz            x1, #0x8
    // 0xeb2428: r0 = AllocateRecord()
    //     0xeb2428: bl              #0xec1080  ; AllocateRecordStub
    // 0xeb242c: mov             x2, x0
    // 0xeb2430: ldur            x0, [fp, #-8]
    // 0xeb2434: stur            x2, [fp, #-0x58]
    // 0xeb2438: StoreField: r2->field_f = r0
    //     0xeb2438: stur            w0, [x2, #0xf]
    // 0xeb243c: ldur            x0, [fp, #-0x20]
    // 0xeb2440: cmp             x0, #0x2f3
    // 0xeb2444: b.eq            #0xeb24f8
    // 0xeb2448: ldur            x1, [fp, #-0x28]
    // 0xeb244c: ldur            x0, [fp, #-0x30]
    // 0xeb2450: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb2450: ldur            w3, [x1, #0x17]
    // 0xeb2454: DecompressPointer r3
    //     0xeb2454: add             x3, x3, HEAP, lsl #32
    // 0xeb2458: StoreField: r2->field_13 = r3
    //     0xeb2458: stur            w3, [x2, #0x13]
    // 0xeb245c: cmp             x0, #0x2f3
    // 0xeb2460: b.eq            #0xeb2518
    // 0xeb2464: ldur            x1, [fp, #-0x38]
    // 0xeb2468: ldur            x0, [fp, #-0x48]
    // 0xeb246c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb246c: ldur            w3, [x1, #0x17]
    // 0xeb2470: DecompressPointer r3
    //     0xeb2470: add             x3, x3, HEAP, lsl #32
    // 0xeb2474: ArrayStore: r2[0] = r3  ; List_4
    //     0xeb2474: stur            w3, [x2, #0x17]
    // 0xeb2478: cmp             x0, #0x2f3
    // 0xeb247c: b.eq            #0xeb2538
    // 0xeb2480: ldur            x0, [fp, #-0x50]
    // 0xeb2484: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb2484: ldur            w1, [x0, #0x17]
    // 0xeb2488: DecompressPointer r1
    //     0xeb2488: add             x1, x1, HEAP, lsl #32
    // 0xeb248c: StoreField: r2->field_1b = r1
    //     0xeb248c: stur            w1, [x2, #0x1b]
    // 0xeb2490: LoadField: r3 = r0->field_7
    //     0xeb2490: ldur            w3, [x0, #7]
    // 0xeb2494: DecompressPointer r3
    //     0xeb2494: add             x3, x3, HEAP, lsl #32
    // 0xeb2498: stur            x3, [fp, #-8]
    // 0xeb249c: LoadField: r4 = r0->field_b
    //     0xeb249c: ldur            x4, [x0, #0xb]
    // 0xeb24a0: ldur            x1, [fp, #-0x40]
    // 0xeb24a4: stur            x4, [fp, #-0x10]
    // 0xeb24a8: r0 = Success()
    //     0xeb24a8: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb24ac: mov             x1, x0
    // 0xeb24b0: ldur            x0, [fp, #-0x58]
    // 0xeb24b4: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb24b4: stur            w0, [x1, #0x17]
    // 0xeb24b8: ldur            x0, [fp, #-8]
    // 0xeb24bc: StoreField: r1->field_7 = r0
    //     0xeb24bc: stur            w0, [x1, #7]
    // 0xeb24c0: ldur            x0, [fp, #-0x10]
    // 0xeb24c4: StoreField: r1->field_b = r0
    //     0xeb24c4: stur            x0, [x1, #0xb]
    // 0xeb24c8: mov             x0, x1
    // 0xeb24cc: LeaveFrame
    //     0xeb24cc: mov             SP, fp
    //     0xeb24d0: ldp             fp, lr, [SP], #0x10
    // 0xeb24d4: ret
    //     0xeb24d4: ret             
    // 0xeb24d8: ldur            x3, [fp, #-0x18]
    // 0xeb24dc: r0 = ParserException()
    //     0xeb24dc: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb24e0: mov             x1, x0
    // 0xeb24e4: ldur            x0, [fp, #-0x18]
    // 0xeb24e8: StoreField: r1->field_7 = r0
    //     0xeb24e8: stur            w0, [x1, #7]
    // 0xeb24ec: mov             x0, x1
    // 0xeb24f0: r0 = Throw()
    //     0xeb24f0: bl              #0xec04b8  ; ThrowStub
    // 0xeb24f4: brk             #0
    // 0xeb24f8: ldur            x1, [fp, #-0x28]
    // 0xeb24fc: r0 = ParserException()
    //     0xeb24fc: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2500: mov             x1, x0
    // 0xeb2504: ldur            x0, [fp, #-0x28]
    // 0xeb2508: StoreField: r1->field_7 = r0
    //     0xeb2508: stur            w0, [x1, #7]
    // 0xeb250c: mov             x0, x1
    // 0xeb2510: r0 = Throw()
    //     0xeb2510: bl              #0xec04b8  ; ThrowStub
    // 0xeb2514: brk             #0
    // 0xeb2518: ldur            x1, [fp, #-0x38]
    // 0xeb251c: r0 = ParserException()
    //     0xeb251c: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2520: mov             x1, x0
    // 0xeb2524: ldur            x0, [fp, #-0x38]
    // 0xeb2528: StoreField: r1->field_7 = r0
    //     0xeb2528: stur            w0, [x1, #7]
    // 0xeb252c: mov             x0, x1
    // 0xeb2530: r0 = Throw()
    //     0xeb2530: bl              #0xec04b8  ; ThrowStub
    // 0xeb2534: brk             #0
    // 0xeb2538: ldur            x0, [fp, #-0x50]
    // 0xeb253c: r0 = ParserException()
    //     0xeb253c: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2540: mov             x1, x0
    // 0xeb2544: ldur            x0, [fp, #-0x50]
    // 0xeb2548: StoreField: r1->field_7 = r0
    //     0xeb2548: stur            w0, [x1, #7]
    // 0xeb254c: mov             x0, x1
    // 0xeb2550: r0 = Throw()
    //     0xeb2550: bl              #0xec04b8  ; ThrowStub
    // 0xeb2554: brk             #0
    // 0xeb2558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb2558: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb255c: b               #0xeb22c4
  }
}
