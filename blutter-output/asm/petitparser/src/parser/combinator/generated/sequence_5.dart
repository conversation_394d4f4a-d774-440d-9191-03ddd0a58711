// lib: , url: package:petitparser/src/parser/combinator/generated/sequence_5.dart

// class id: 1050900, size: 0x8
class :: {

  static Parser<Y5> RecordParserExtension5.map5<Y0, Y1, Y2, Y3, Y4, Y5>(Parser<(Y0, Y1, Y2, Y3, Y4)>, (dynamic, Y0, Y1, Y2, Y3, Y4) => Y5) {
    // ** addr: 0x88ca74, size: 0xbc
    // 0x88ca74: EnterFrame
    //     0x88ca74: stp             fp, lr, [SP, #-0x10]!
    //     0x88ca78: mov             fp, SP
    // 0x88ca7c: AllocStack(0x28)
    //     0x88ca7c: sub             SP, SP, #0x28
    // 0x88ca80: SetupParameters()
    //     0x88ca80: ldur            w0, [x4, #0xf]
    //     0x88ca84: cbnz            w0, #0x88ca90
    //     0x88ca88: mov             x1, NULL
    //     0x88ca8c: b               #0x88ca9c
    //     0x88ca90: ldur            w0, [x4, #0x17]
    //     0x88ca94: add             x1, fp, w0, sxtw #2
    //     0x88ca98: ldr             x1, [x1, #0x10]
    //     0x88ca9c: ldr             x0, [fp, #0x10]
    //     0x88caa0: stur            x1, [fp, #-8]
    // 0x88caa4: CheckStackOverflow
    //     0x88caa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88caa8: cmp             SP, x16
    //     0x88caac: b.ls            #0x88cb28
    // 0x88cab0: r1 = 1
    //     0x88cab0: movz            x1, #0x1
    // 0x88cab4: r0 = AllocateContext()
    //     0x88cab4: bl              #0xec126c  ; AllocateContextStub
    // 0x88cab8: mov             x4, x0
    // 0x88cabc: ldr             x0, [fp, #0x10]
    // 0x88cac0: stur            x4, [fp, #-0x10]
    // 0x88cac4: StoreField: r4->field_f = r0
    //     0x88cac4: stur            w0, [x4, #0xf]
    // 0x88cac8: ldur            x1, [fp, #-8]
    // 0x88cacc: r2 = Null
    //     0x88cacc: mov             x2, NULL
    // 0x88cad0: r3 = <(Y0, Y1, Y2, Y3, Y4), Y5>
    //     0x88cad0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26848] TypeArguments: <(Y0, Y1, Y2, Y3, Y4), Y5>
    //     0x88cad4: ldr             x3, [x3, #0x848]
    // 0x88cad8: r30 = InstantiateTypeArgumentsStub
    //     0x88cad8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88cadc: LoadField: r30 = r30->field_7
    //     0x88cadc: ldur            lr, [lr, #7]
    // 0x88cae0: blr             lr
    // 0x88cae4: ldur            x2, [fp, #-0x10]
    // 0x88cae8: r1 = Function '<anonymous closure>': static.
    //     0x88cae8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26850] AnonymousClosure: static (0x88cb30), in [package:petitparser/src/parser/combinator/generated/sequence_5.dart] ::RecordParserExtension5.map5 (0x88ca74)
    //     0x88caec: ldr             x1, [x1, #0x850]
    // 0x88caf0: stur            x0, [fp, #-0x10]
    // 0x88caf4: r0 = AllocateClosure()
    //     0x88caf4: bl              #0xec1630  ; AllocateClosureStub
    // 0x88caf8: mov             x1, x0
    // 0x88cafc: ldur            x0, [fp, #-8]
    // 0x88cb00: StoreField: r1->field_b = r0
    //     0x88cb00: stur            w0, [x1, #0xb]
    // 0x88cb04: ldur            x16, [fp, #-0x10]
    // 0x88cb08: ldr             lr, [fp, #0x18]
    // 0x88cb0c: stp             lr, x16, [SP, #8]
    // 0x88cb10: str             x1, [SP]
    // 0x88cb14: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88cb14: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88cb18: r0 = MapParserExtension.map()
    //     0x88cb18: bl              #0x88ab3c  ; [package:petitparser/src/parser/action/map.dart] ::MapParserExtension.map
    // 0x88cb1c: LeaveFrame
    //     0x88cb1c: mov             SP, fp
    //     0x88cb20: ldp             fp, lr, [SP], #0x10
    // 0x88cb24: ret
    //     0x88cb24: ret             
    // 0x88cb28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88cb28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88cb2c: b               #0x88cab0
  }
  [closure] static Y5 <anonymous closure>(dynamic, (Y0, Y1, Y2, Y3, Y4)) {
    // ** addr: 0x88cb30, size: 0x84
    // 0x88cb30: EnterFrame
    //     0x88cb30: stp             fp, lr, [SP, #-0x10]!
    //     0x88cb34: mov             fp, SP
    // 0x88cb38: AllocStack(0x30)
    //     0x88cb38: sub             SP, SP, #0x30
    // 0x88cb3c: SetupParameters()
    //     0x88cb3c: ldr             x0, [fp, #0x18]
    //     0x88cb40: ldur            w1, [x0, #0x17]
    //     0x88cb44: add             x1, x1, HEAP, lsl #32
    // 0x88cb48: CheckStackOverflow
    //     0x88cb48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88cb4c: cmp             SP, x16
    //     0x88cb50: b.ls            #0x88cbac
    // 0x88cb54: LoadField: r0 = r1->field_f
    //     0x88cb54: ldur            w0, [x1, #0xf]
    // 0x88cb58: DecompressPointer r0
    //     0x88cb58: add             x0, x0, HEAP, lsl #32
    // 0x88cb5c: ldr             x1, [fp, #0x10]
    // 0x88cb60: LoadField: r2 = r1->field_f
    //     0x88cb60: ldur            w2, [x1, #0xf]
    // 0x88cb64: DecompressPointer r2
    //     0x88cb64: add             x2, x2, HEAP, lsl #32
    // 0x88cb68: LoadField: r3 = r1->field_13
    //     0x88cb68: ldur            w3, [x1, #0x13]
    // 0x88cb6c: DecompressPointer r3
    //     0x88cb6c: add             x3, x3, HEAP, lsl #32
    // 0x88cb70: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x88cb70: ldur            w4, [x1, #0x17]
    // 0x88cb74: DecompressPointer r4
    //     0x88cb74: add             x4, x4, HEAP, lsl #32
    // 0x88cb78: LoadField: r5 = r1->field_1b
    //     0x88cb78: ldur            w5, [x1, #0x1b]
    // 0x88cb7c: DecompressPointer r5
    //     0x88cb7c: add             x5, x5, HEAP, lsl #32
    // 0x88cb80: LoadField: r6 = r1->field_1f
    //     0x88cb80: ldur            w6, [x1, #0x1f]
    // 0x88cb84: DecompressPointer r6
    //     0x88cb84: add             x6, x6, HEAP, lsl #32
    // 0x88cb88: stp             x2, x0, [SP, #0x20]
    // 0x88cb8c: stp             x4, x3, [SP, #0x10]
    // 0x88cb90: stp             x6, x5, [SP]
    // 0x88cb94: ClosureCall
    //     0x88cb94: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x6, 0x6, 0x6, Null]
    //     0x88cb98: ldur            x2, [x0, #0x1f]
    //     0x88cb9c: blr             x2
    // 0x88cba0: LeaveFrame
    //     0x88cba0: mov             SP, fp
    //     0x88cba4: ldp             fp, lr, [SP], #0x10
    // 0x88cba8: ret
    //     0x88cba8: ret             
    // 0x88cbac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88cbac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88cbb0: b               #0x88cb54
  }
  static Parser<(Y0, Y1, Y2, Y3, Y4)> seq5<Y0, Y1, Y2, Y3, Y4>(Parser<Y0>, Parser<Y1>, Parser<Y2>, Parser<Y3>, Parser<Y4>) {
    // ** addr: 0x88cbb4, size: 0x8c
    // 0x88cbb4: EnterFrame
    //     0x88cbb4: stp             fp, lr, [SP, #-0x10]!
    //     0x88cbb8: mov             fp, SP
    // 0x88cbbc: LoadField: r0 = r4->field_f
    //     0x88cbbc: ldur            w0, [x4, #0xf]
    // 0x88cbc0: cbnz            w0, #0x88cbcc
    // 0x88cbc4: r1 = Null
    //     0x88cbc4: mov             x1, NULL
    // 0x88cbc8: b               #0x88cbd8
    // 0x88cbcc: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88cbcc: ldur            w0, [x4, #0x17]
    // 0x88cbd0: add             x1, fp, w0, sxtw #2
    // 0x88cbd4: ldr             x1, [x1, #0x10]
    // 0x88cbd8: ldr             x7, [fp, #0x30]
    // 0x88cbdc: ldr             x6, [fp, #0x28]
    // 0x88cbe0: ldr             x5, [fp, #0x20]
    // 0x88cbe4: ldr             x4, [fp, #0x18]
    // 0x88cbe8: ldr             x0, [fp, #0x10]
    // 0x88cbec: r2 = Null
    //     0x88cbec: mov             x2, NULL
    // 0x88cbf0: r3 = <(Y0, Y1, Y2, Y3, Y4), Y0, Y1, Y2, Y3, Y4>
    //     0x88cbf0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26858] TypeArguments: <(Y0, Y1, Y2, Y3, Y4), Y0, Y1, Y2, Y3, Y4>
    //     0x88cbf4: ldr             x3, [x3, #0x858]
    // 0x88cbf8: r30 = InstantiateTypeArgumentsStub
    //     0x88cbf8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88cbfc: LoadField: r30 = r30->field_7
    //     0x88cbfc: ldur            lr, [lr, #7]
    // 0x88cc00: blr             lr
    // 0x88cc04: mov             x1, x0
    // 0x88cc08: r0 = SequenceParser5()
    //     0x88cc08: bl              #0x88cc40  ; AllocateSequenceParser5Stub -> SequenceParser5<C1X0, C1X1, C1X2, C1X3, C1X4> (size=0x20)
    // 0x88cc0c: ldr             x1, [fp, #0x30]
    // 0x88cc10: StoreField: r0->field_b = r1
    //     0x88cc10: stur            w1, [x0, #0xb]
    // 0x88cc14: ldr             x1, [fp, #0x28]
    // 0x88cc18: StoreField: r0->field_f = r1
    //     0x88cc18: stur            w1, [x0, #0xf]
    // 0x88cc1c: ldr             x1, [fp, #0x20]
    // 0x88cc20: StoreField: r0->field_13 = r1
    //     0x88cc20: stur            w1, [x0, #0x13]
    // 0x88cc24: ldr             x1, [fp, #0x18]
    // 0x88cc28: ArrayStore: r0[0] = r1  ; List_4
    //     0x88cc28: stur            w1, [x0, #0x17]
    // 0x88cc2c: ldr             x1, [fp, #0x10]
    // 0x88cc30: StoreField: r0->field_1b = r1
    //     0x88cc30: stur            w1, [x0, #0x1b]
    // 0x88cc34: LeaveFrame
    //     0x88cc34: mov             SP, fp
    //     0x88cc38: ldp             fp, lr, [SP], #0x10
    // 0x88cc3c: ret
    //     0x88cc3c: ret             
  }
}

// class id: 735, size: 0x20, field offset: 0xc
class SequenceParser5<C1X0, C1X1, C1X2, C1X3, C1X4> extends Parser<C1X0>
    implements SequentialParser {

  get _ children(/* No info */) {
    // ** addr: 0x88fb38, size: 0xb0
    // 0x88fb38: EnterFrame
    //     0x88fb38: stp             fp, lr, [SP, #-0x10]!
    //     0x88fb3c: mov             fp, SP
    // 0x88fb40: AllocStack(0x30)
    //     0x88fb40: sub             SP, SP, #0x30
    // 0x88fb44: r0 = 10
    //     0x88fb44: movz            x0, #0xa
    // 0x88fb48: LoadField: r3 = r1->field_b
    //     0x88fb48: ldur            w3, [x1, #0xb]
    // 0x88fb4c: DecompressPointer r3
    //     0x88fb4c: add             x3, x3, HEAP, lsl #32
    // 0x88fb50: stur            x3, [fp, #-0x28]
    // 0x88fb54: LoadField: r4 = r1->field_f
    //     0x88fb54: ldur            w4, [x1, #0xf]
    // 0x88fb58: DecompressPointer r4
    //     0x88fb58: add             x4, x4, HEAP, lsl #32
    // 0x88fb5c: stur            x4, [fp, #-0x20]
    // 0x88fb60: LoadField: r5 = r1->field_13
    //     0x88fb60: ldur            w5, [x1, #0x13]
    // 0x88fb64: DecompressPointer r5
    //     0x88fb64: add             x5, x5, HEAP, lsl #32
    // 0x88fb68: stur            x5, [fp, #-0x18]
    // 0x88fb6c: ArrayLoad: r6 = r1[0]  ; List_4
    //     0x88fb6c: ldur            w6, [x1, #0x17]
    // 0x88fb70: DecompressPointer r6
    //     0x88fb70: add             x6, x6, HEAP, lsl #32
    // 0x88fb74: stur            x6, [fp, #-0x10]
    // 0x88fb78: LoadField: r7 = r1->field_1b
    //     0x88fb78: ldur            w7, [x1, #0x1b]
    // 0x88fb7c: DecompressPointer r7
    //     0x88fb7c: add             x7, x7, HEAP, lsl #32
    // 0x88fb80: mov             x2, x0
    // 0x88fb84: stur            x7, [fp, #-8]
    // 0x88fb88: r1 = Null
    //     0x88fb88: mov             x1, NULL
    // 0x88fb8c: r0 = AllocateArray()
    //     0x88fb8c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88fb90: mov             x2, x0
    // 0x88fb94: ldur            x0, [fp, #-0x28]
    // 0x88fb98: stur            x2, [fp, #-0x30]
    // 0x88fb9c: StoreField: r2->field_f = r0
    //     0x88fb9c: stur            w0, [x2, #0xf]
    // 0x88fba0: ldur            x0, [fp, #-0x20]
    // 0x88fba4: StoreField: r2->field_13 = r0
    //     0x88fba4: stur            w0, [x2, #0x13]
    // 0x88fba8: ldur            x0, [fp, #-0x18]
    // 0x88fbac: ArrayStore: r2[0] = r0  ; List_4
    //     0x88fbac: stur            w0, [x2, #0x17]
    // 0x88fbb0: ldur            x0, [fp, #-0x10]
    // 0x88fbb4: StoreField: r2->field_1b = r0
    //     0x88fbb4: stur            w0, [x2, #0x1b]
    // 0x88fbb8: ldur            x0, [fp, #-8]
    // 0x88fbbc: StoreField: r2->field_1f = r0
    //     0x88fbbc: stur            w0, [x2, #0x1f]
    // 0x88fbc0: r1 = <Parser>
    //     0x88fbc0: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x88fbc4: ldr             x1, [x1, #0x6f8]
    // 0x88fbc8: r0 = AllocateGrowableArray()
    //     0x88fbc8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88fbcc: ldur            x1, [fp, #-0x30]
    // 0x88fbd0: StoreField: r0->field_f = r1
    //     0x88fbd0: stur            w1, [x0, #0xf]
    // 0x88fbd4: r1 = 10
    //     0x88fbd4: movz            x1, #0xa
    // 0x88fbd8: StoreField: r0->field_b = r1
    //     0x88fbd8: stur            w1, [x0, #0xb]
    // 0x88fbdc: LeaveFrame
    //     0x88fbdc: mov             SP, fp
    //     0x88fbe0: ldp             fp, lr, [SP], #0x10
    // 0x88fbe4: ret
    //     0x88fbe4: ret             
  }
  _ replace(/* No info */) {
    // ** addr: 0x894708, size: 0x2d0
    // 0x894708: EnterFrame
    //     0x894708: stp             fp, lr, [SP, #-0x10]!
    //     0x89470c: mov             fp, SP
    // 0x894710: AllocStack(0x28)
    //     0x894710: sub             SP, SP, #0x28
    // 0x894714: SetupParameters(SequenceParser5<C1X0, C1X1, C1X2, C1X3, C1X4> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0x894714: stur            x1, [fp, #-8]
    //     0x894718: mov             x16, x3
    //     0x89471c: mov             x3, x1
    //     0x894720: mov             x1, x16
    //     0x894724: stur            x2, [fp, #-0x10]
    //     0x894728: stur            x1, [fp, #-0x18]
    // 0x89472c: CheckStackOverflow
    //     0x89472c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x894730: cmp             SP, x16
    //     0x894734: b.ls            #0x8949d0
    // 0x894738: LoadField: r0 = r3->field_b
    //     0x894738: ldur            w0, [x3, #0xb]
    // 0x89473c: DecompressPointer r0
    //     0x89473c: add             x0, x0, HEAP, lsl #32
    // 0x894740: r4 = LoadClassIdInstr(r0)
    //     0x894740: ldur            x4, [x0, #-1]
    //     0x894744: ubfx            x4, x4, #0xc, #0x14
    // 0x894748: stp             x2, x0, [SP]
    // 0x89474c: mov             x0, x4
    // 0x894750: mov             lr, x0
    // 0x894754: ldr             lr, [x21, lr, lsl #3]
    // 0x894758: blr             lr
    // 0x89475c: tbnz            w0, #4, #0x8947b4
    // 0x894760: ldur            x3, [fp, #-8]
    // 0x894764: LoadField: r2 = r3->field_7
    //     0x894764: ldur            w2, [x3, #7]
    // 0x894768: DecompressPointer r2
    //     0x894768: add             x2, x2, HEAP, lsl #32
    // 0x89476c: ldur            x0, [fp, #-0x18]
    // 0x894770: r1 = Null
    //     0x894770: mov             x1, NULL
    // 0x894774: r8 = Parser<C1X0>
    //     0x894774: add             x8, PP, #0x31, lsl #12  ; [pp+0x31258] Type: Parser<C1X0>
    //     0x894778: ldr             x8, [x8, #0x258]
    // 0x89477c: LoadField: r9 = r8->field_7
    //     0x89477c: ldur            x9, [x8, #7]
    // 0x894780: r3 = Null
    //     0x894780: add             x3, PP, #0x31, lsl #12  ; [pp+0x31318] Null
    //     0x894784: ldr             x3, [x3, #0x318]
    // 0x894788: blr             x9
    // 0x89478c: ldur            x0, [fp, #-0x18]
    // 0x894790: ldur            x1, [fp, #-8]
    // 0x894794: StoreField: r1->field_b = r0
    //     0x894794: stur            w0, [x1, #0xb]
    //     0x894798: ldurb           w16, [x1, #-1]
    //     0x89479c: ldurb           w17, [x0, #-1]
    //     0x8947a0: and             x16, x17, x16, lsr #2
    //     0x8947a4: tst             x16, HEAP, lsr #32
    //     0x8947a8: b.eq            #0x8947b0
    //     0x8947ac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8947b0: b               #0x8947b8
    // 0x8947b4: ldur            x1, [fp, #-8]
    // 0x8947b8: LoadField: r0 = r1->field_f
    //     0x8947b8: ldur            w0, [x1, #0xf]
    // 0x8947bc: DecompressPointer r0
    //     0x8947bc: add             x0, x0, HEAP, lsl #32
    // 0x8947c0: r2 = LoadClassIdInstr(r0)
    //     0x8947c0: ldur            x2, [x0, #-1]
    //     0x8947c4: ubfx            x2, x2, #0xc, #0x14
    // 0x8947c8: ldur            x16, [fp, #-0x10]
    // 0x8947cc: stp             x16, x0, [SP]
    // 0x8947d0: mov             x0, x2
    // 0x8947d4: mov             lr, x0
    // 0x8947d8: ldr             lr, [x21, lr, lsl #3]
    // 0x8947dc: blr             lr
    // 0x8947e0: tbnz            w0, #4, #0x894838
    // 0x8947e4: ldur            x3, [fp, #-8]
    // 0x8947e8: LoadField: r2 = r3->field_7
    //     0x8947e8: ldur            w2, [x3, #7]
    // 0x8947ec: DecompressPointer r2
    //     0x8947ec: add             x2, x2, HEAP, lsl #32
    // 0x8947f0: ldur            x0, [fp, #-0x18]
    // 0x8947f4: r1 = Null
    //     0x8947f4: mov             x1, NULL
    // 0x8947f8: r8 = Parser<C1X1>
    //     0x8947f8: add             x8, PP, #0x31, lsl #12  ; [pp+0x31270] Type: Parser<C1X1>
    //     0x8947fc: ldr             x8, [x8, #0x270]
    // 0x894800: LoadField: r9 = r8->field_7
    //     0x894800: ldur            x9, [x8, #7]
    // 0x894804: r3 = Null
    //     0x894804: add             x3, PP, #0x31, lsl #12  ; [pp+0x31328] Null
    //     0x894808: ldr             x3, [x3, #0x328]
    // 0x89480c: blr             x9
    // 0x894810: ldur            x0, [fp, #-0x18]
    // 0x894814: ldur            x1, [fp, #-8]
    // 0x894818: StoreField: r1->field_f = r0
    //     0x894818: stur            w0, [x1, #0xf]
    //     0x89481c: ldurb           w16, [x1, #-1]
    //     0x894820: ldurb           w17, [x0, #-1]
    //     0x894824: and             x16, x17, x16, lsr #2
    //     0x894828: tst             x16, HEAP, lsr #32
    //     0x89482c: b.eq            #0x894834
    //     0x894830: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894834: b               #0x89483c
    // 0x894838: ldur            x1, [fp, #-8]
    // 0x89483c: LoadField: r0 = r1->field_13
    //     0x89483c: ldur            w0, [x1, #0x13]
    // 0x894840: DecompressPointer r0
    //     0x894840: add             x0, x0, HEAP, lsl #32
    // 0x894844: r2 = LoadClassIdInstr(r0)
    //     0x894844: ldur            x2, [x0, #-1]
    //     0x894848: ubfx            x2, x2, #0xc, #0x14
    // 0x89484c: ldur            x16, [fp, #-0x10]
    // 0x894850: stp             x16, x0, [SP]
    // 0x894854: mov             x0, x2
    // 0x894858: mov             lr, x0
    // 0x89485c: ldr             lr, [x21, lr, lsl #3]
    // 0x894860: blr             lr
    // 0x894864: tbnz            w0, #4, #0x8948bc
    // 0x894868: ldur            x3, [fp, #-8]
    // 0x89486c: LoadField: r2 = r3->field_7
    //     0x89486c: ldur            w2, [x3, #7]
    // 0x894870: DecompressPointer r2
    //     0x894870: add             x2, x2, HEAP, lsl #32
    // 0x894874: ldur            x0, [fp, #-0x18]
    // 0x894878: r1 = Null
    //     0x894878: mov             x1, NULL
    // 0x89487c: r8 = Parser<C1X2>
    //     0x89487c: add             x8, PP, #0x31, lsl #12  ; [pp+0x31288] Type: Parser<C1X2>
    //     0x894880: ldr             x8, [x8, #0x288]
    // 0x894884: LoadField: r9 = r8->field_7
    //     0x894884: ldur            x9, [x8, #7]
    // 0x894888: r3 = Null
    //     0x894888: add             x3, PP, #0x31, lsl #12  ; [pp+0x31338] Null
    //     0x89488c: ldr             x3, [x3, #0x338]
    // 0x894890: blr             x9
    // 0x894894: ldur            x0, [fp, #-0x18]
    // 0x894898: ldur            x1, [fp, #-8]
    // 0x89489c: StoreField: r1->field_13 = r0
    //     0x89489c: stur            w0, [x1, #0x13]
    //     0x8948a0: ldurb           w16, [x1, #-1]
    //     0x8948a4: ldurb           w17, [x0, #-1]
    //     0x8948a8: and             x16, x17, x16, lsr #2
    //     0x8948ac: tst             x16, HEAP, lsr #32
    //     0x8948b0: b.eq            #0x8948b8
    //     0x8948b4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8948b8: b               #0x8948c0
    // 0x8948bc: ldur            x1, [fp, #-8]
    // 0x8948c0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8948c0: ldur            w0, [x1, #0x17]
    // 0x8948c4: DecompressPointer r0
    //     0x8948c4: add             x0, x0, HEAP, lsl #32
    // 0x8948c8: r2 = LoadClassIdInstr(r0)
    //     0x8948c8: ldur            x2, [x0, #-1]
    //     0x8948cc: ubfx            x2, x2, #0xc, #0x14
    // 0x8948d0: ldur            x16, [fp, #-0x10]
    // 0x8948d4: stp             x16, x0, [SP]
    // 0x8948d8: mov             x0, x2
    // 0x8948dc: mov             lr, x0
    // 0x8948e0: ldr             lr, [x21, lr, lsl #3]
    // 0x8948e4: blr             lr
    // 0x8948e8: tbnz            w0, #4, #0x894940
    // 0x8948ec: ldur            x3, [fp, #-8]
    // 0x8948f0: LoadField: r2 = r3->field_7
    //     0x8948f0: ldur            w2, [x3, #7]
    // 0x8948f4: DecompressPointer r2
    //     0x8948f4: add             x2, x2, HEAP, lsl #32
    // 0x8948f8: ldur            x0, [fp, #-0x18]
    // 0x8948fc: r1 = Null
    //     0x8948fc: mov             x1, NULL
    // 0x894900: r8 = Parser<C1X3>
    //     0x894900: add             x8, PP, #0x31, lsl #12  ; [pp+0x312a0] Type: Parser<C1X3>
    //     0x894904: ldr             x8, [x8, #0x2a0]
    // 0x894908: LoadField: r9 = r8->field_7
    //     0x894908: ldur            x9, [x8, #7]
    // 0x89490c: r3 = Null
    //     0x89490c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31348] Null
    //     0x894910: ldr             x3, [x3, #0x348]
    // 0x894914: blr             x9
    // 0x894918: ldur            x0, [fp, #-0x18]
    // 0x89491c: ldur            x1, [fp, #-8]
    // 0x894920: ArrayStore: r1[0] = r0  ; List_4
    //     0x894920: stur            w0, [x1, #0x17]
    //     0x894924: ldurb           w16, [x1, #-1]
    //     0x894928: ldurb           w17, [x0, #-1]
    //     0x89492c: and             x16, x17, x16, lsr #2
    //     0x894930: tst             x16, HEAP, lsr #32
    //     0x894934: b.eq            #0x89493c
    //     0x894938: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x89493c: b               #0x894944
    // 0x894940: ldur            x1, [fp, #-8]
    // 0x894944: LoadField: r0 = r1->field_1b
    //     0x894944: ldur            w0, [x1, #0x1b]
    // 0x894948: DecompressPointer r0
    //     0x894948: add             x0, x0, HEAP, lsl #32
    // 0x89494c: r2 = LoadClassIdInstr(r0)
    //     0x89494c: ldur            x2, [x0, #-1]
    //     0x894950: ubfx            x2, x2, #0xc, #0x14
    // 0x894954: ldur            x16, [fp, #-0x10]
    // 0x894958: stp             x16, x0, [SP]
    // 0x89495c: mov             x0, x2
    // 0x894960: mov             lr, x0
    // 0x894964: ldr             lr, [x21, lr, lsl #3]
    // 0x894968: blr             lr
    // 0x89496c: tbnz            w0, #4, #0x8949c0
    // 0x894970: ldur            x3, [fp, #-8]
    // 0x894974: LoadField: r2 = r3->field_7
    //     0x894974: ldur            w2, [x3, #7]
    // 0x894978: DecompressPointer r2
    //     0x894978: add             x2, x2, HEAP, lsl #32
    // 0x89497c: ldur            x0, [fp, #-0x18]
    // 0x894980: r1 = Null
    //     0x894980: mov             x1, NULL
    // 0x894984: r8 = Parser<C1X4>
    //     0x894984: add             x8, PP, #0x31, lsl #12  ; [pp+0x312b8] Type: Parser<C1X4>
    //     0x894988: ldr             x8, [x8, #0x2b8]
    // 0x89498c: LoadField: r9 = r8->field_7
    //     0x89498c: ldur            x9, [x8, #7]
    // 0x894990: r3 = Null
    //     0x894990: add             x3, PP, #0x31, lsl #12  ; [pp+0x31358] Null
    //     0x894994: ldr             x3, [x3, #0x358]
    // 0x894998: blr             x9
    // 0x89499c: ldur            x0, [fp, #-0x18]
    // 0x8949a0: ldur            x1, [fp, #-8]
    // 0x8949a4: StoreField: r1->field_1b = r0
    //     0x8949a4: stur            w0, [x1, #0x1b]
    //     0x8949a8: ldurb           w16, [x1, #-1]
    //     0x8949ac: ldurb           w17, [x0, #-1]
    //     0x8949b0: and             x16, x17, x16, lsr #2
    //     0x8949b4: tst             x16, HEAP, lsr #32
    //     0x8949b8: b.eq            #0x8949c0
    //     0x8949bc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8949c0: r0 = Null
    //     0x8949c0: mov             x0, NULL
    // 0x8949c4: LeaveFrame
    //     0x8949c4: mov             SP, fp
    //     0x8949c8: ldp             fp, lr, [SP], #0x10
    // 0x8949cc: ret
    //     0x8949cc: ret             
    // 0x8949d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8949d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8949d4: b               #0x894738
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb0264, size: 0x18c
    // 0xeb0264: EnterFrame
    //     0xeb0264: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0268: mov             fp, SP
    // 0xeb026c: AllocStack(0x10)
    //     0xeb026c: sub             SP, SP, #0x10
    // 0xeb0270: SetupParameters(SequenceParser5<C1X0, C1X1, C1X2, C1X3, C1X4> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */)
    //     0xeb0270: mov             x5, x1
    //     0xeb0274: mov             x4, x2
    //     0xeb0278: stur            x1, [fp, #-8]
    //     0xeb027c: stur            x2, [fp, #-0x10]
    // 0xeb0280: CheckStackOverflow
    //     0xeb0280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0284: cmp             SP, x16
    //     0xeb0288: b.ls            #0xeb03e8
    // 0xeb028c: LoadField: r1 = r5->field_b
    //     0xeb028c: ldur            w1, [x5, #0xb]
    // 0xeb0290: DecompressPointer r1
    //     0xeb0290: add             x1, x1, HEAP, lsl #32
    // 0xeb0294: r0 = LoadClassIdInstr(r1)
    //     0xeb0294: ldur            x0, [x1, #-1]
    //     0xeb0298: ubfx            x0, x0, #0xc, #0x14
    // 0xeb029c: mov             x2, x4
    // 0xeb02a0: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb02a0: sub             lr, x0, #0xfce
    //     0xeb02a4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb02a8: blr             lr
    // 0xeb02ac: r3 = LoadInt32Instr(r0)
    //     0xeb02ac: sbfx            x3, x0, #1, #0x1f
    //     0xeb02b0: tbz             w0, #0, #0xeb02b8
    //     0xeb02b4: ldur            x3, [x0, #7]
    // 0xeb02b8: tbz             x3, #0x3f, #0xeb02cc
    // 0xeb02bc: r0 = -2
    //     0xeb02bc: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb02c0: LeaveFrame
    //     0xeb02c0: mov             SP, fp
    //     0xeb02c4: ldp             fp, lr, [SP], #0x10
    // 0xeb02c8: ret
    //     0xeb02c8: ret             
    // 0xeb02cc: ldur            x4, [fp, #-8]
    // 0xeb02d0: LoadField: r1 = r4->field_f
    //     0xeb02d0: ldur            w1, [x4, #0xf]
    // 0xeb02d4: DecompressPointer r1
    //     0xeb02d4: add             x1, x1, HEAP, lsl #32
    // 0xeb02d8: r0 = LoadClassIdInstr(r1)
    //     0xeb02d8: ldur            x0, [x1, #-1]
    //     0xeb02dc: ubfx            x0, x0, #0xc, #0x14
    // 0xeb02e0: ldur            x2, [fp, #-0x10]
    // 0xeb02e4: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb02e4: sub             lr, x0, #0xfce
    //     0xeb02e8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb02ec: blr             lr
    // 0xeb02f0: r3 = LoadInt32Instr(r0)
    //     0xeb02f0: sbfx            x3, x0, #1, #0x1f
    //     0xeb02f4: tbz             w0, #0, #0xeb02fc
    //     0xeb02f8: ldur            x3, [x0, #7]
    // 0xeb02fc: tbz             x3, #0x3f, #0xeb0310
    // 0xeb0300: r0 = -2
    //     0xeb0300: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0304: LeaveFrame
    //     0xeb0304: mov             SP, fp
    //     0xeb0308: ldp             fp, lr, [SP], #0x10
    // 0xeb030c: ret
    //     0xeb030c: ret             
    // 0xeb0310: ldur            x4, [fp, #-8]
    // 0xeb0314: LoadField: r1 = r4->field_13
    //     0xeb0314: ldur            w1, [x4, #0x13]
    // 0xeb0318: DecompressPointer r1
    //     0xeb0318: add             x1, x1, HEAP, lsl #32
    // 0xeb031c: r0 = LoadClassIdInstr(r1)
    //     0xeb031c: ldur            x0, [x1, #-1]
    //     0xeb0320: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0324: ldur            x2, [fp, #-0x10]
    // 0xeb0328: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb0328: sub             lr, x0, #0xfce
    //     0xeb032c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0330: blr             lr
    // 0xeb0334: r3 = LoadInt32Instr(r0)
    //     0xeb0334: sbfx            x3, x0, #1, #0x1f
    //     0xeb0338: tbz             w0, #0, #0xeb0340
    //     0xeb033c: ldur            x3, [x0, #7]
    // 0xeb0340: tbz             x3, #0x3f, #0xeb0354
    // 0xeb0344: r0 = -2
    //     0xeb0344: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0348: LeaveFrame
    //     0xeb0348: mov             SP, fp
    //     0xeb034c: ldp             fp, lr, [SP], #0x10
    // 0xeb0350: ret
    //     0xeb0350: ret             
    // 0xeb0354: ldur            x4, [fp, #-8]
    // 0xeb0358: ArrayLoad: r1 = r4[0]  ; List_4
    //     0xeb0358: ldur            w1, [x4, #0x17]
    // 0xeb035c: DecompressPointer r1
    //     0xeb035c: add             x1, x1, HEAP, lsl #32
    // 0xeb0360: r0 = LoadClassIdInstr(r1)
    //     0xeb0360: ldur            x0, [x1, #-1]
    //     0xeb0364: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0368: ldur            x2, [fp, #-0x10]
    // 0xeb036c: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb036c: sub             lr, x0, #0xfce
    //     0xeb0370: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0374: blr             lr
    // 0xeb0378: r3 = LoadInt32Instr(r0)
    //     0xeb0378: sbfx            x3, x0, #1, #0x1f
    //     0xeb037c: tbz             w0, #0, #0xeb0384
    //     0xeb0380: ldur            x3, [x0, #7]
    // 0xeb0384: tbz             x3, #0x3f, #0xeb0398
    // 0xeb0388: r0 = -2
    //     0xeb0388: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb038c: LeaveFrame
    //     0xeb038c: mov             SP, fp
    //     0xeb0390: ldp             fp, lr, [SP], #0x10
    // 0xeb0394: ret
    //     0xeb0394: ret             
    // 0xeb0398: ldur            x0, [fp, #-8]
    // 0xeb039c: LoadField: r1 = r0->field_1b
    //     0xeb039c: ldur            w1, [x0, #0x1b]
    // 0xeb03a0: DecompressPointer r1
    //     0xeb03a0: add             x1, x1, HEAP, lsl #32
    // 0xeb03a4: r0 = LoadClassIdInstr(r1)
    //     0xeb03a4: ldur            x0, [x1, #-1]
    //     0xeb03a8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb03ac: ldur            x2, [fp, #-0x10]
    // 0xeb03b0: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb03b0: sub             lr, x0, #0xfce
    //     0xeb03b4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb03b8: blr             lr
    // 0xeb03bc: r1 = LoadInt32Instr(r0)
    //     0xeb03bc: sbfx            x1, x0, #1, #0x1f
    //     0xeb03c0: tbz             w0, #0, #0xeb03c8
    //     0xeb03c4: ldur            x1, [x0, #7]
    // 0xeb03c8: tbz             x1, #0x3f, #0xeb03dc
    // 0xeb03cc: r0 = -2
    //     0xeb03cc: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb03d0: LeaveFrame
    //     0xeb03d0: mov             SP, fp
    //     0xeb03d4: ldp             fp, lr, [SP], #0x10
    // 0xeb03d8: ret
    //     0xeb03d8: ret             
    // 0xeb03dc: LeaveFrame
    //     0xeb03dc: mov             SP, fp
    //     0xeb03e0: ldp             fp, lr, [SP], #0x10
    // 0xeb03e4: ret
    //     0xeb03e4: ret             
    // 0xeb03e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb03e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb03ec: b               #0xeb028c
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb2560, size: 0x348
    // 0xeb2560: EnterFrame
    //     0xeb2560: stp             fp, lr, [SP, #-0x10]!
    //     0xeb2564: mov             fp, SP
    // 0xeb2568: AllocStack(0x68)
    //     0xeb2568: sub             SP, SP, #0x68
    // 0xeb256c: SetupParameters(SequenceParser5<C1X0, C1X1, C1X2, C1X3, C1X4> this /* r1 => r3, fp-0x8 */)
    //     0xeb256c: mov             x3, x1
    //     0xeb2570: stur            x1, [fp, #-8]
    // 0xeb2574: CheckStackOverflow
    //     0xeb2574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb2578: cmp             SP, x16
    //     0xeb257c: b.ls            #0xeb28a0
    // 0xeb2580: LoadField: r1 = r3->field_b
    //     0xeb2580: ldur            w1, [x3, #0xb]
    // 0xeb2584: DecompressPointer r1
    //     0xeb2584: add             x1, x1, HEAP, lsl #32
    // 0xeb2588: r0 = LoadClassIdInstr(r1)
    //     0xeb2588: ldur            x0, [x1, #-1]
    //     0xeb258c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2590: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2590: sub             lr, x0, #1, lsl #12
    //     0xeb2594: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2598: blr             lr
    // 0xeb259c: mov             x3, x0
    // 0xeb25a0: stur            x3, [fp, #-0x18]
    // 0xeb25a4: r4 = LoadClassIdInstr(r3)
    //     0xeb25a4: ldur            x4, [x3, #-1]
    //     0xeb25a8: ubfx            x4, x4, #0xc, #0x14
    // 0xeb25ac: stur            x4, [fp, #-0x10]
    // 0xeb25b0: cmp             x4, #0x2f3
    // 0xeb25b4: b.ne            #0xeb25c8
    // 0xeb25b8: mov             x0, x3
    // 0xeb25bc: LeaveFrame
    //     0xeb25bc: mov             SP, fp
    //     0xeb25c0: ldp             fp, lr, [SP], #0x10
    // 0xeb25c4: ret
    //     0xeb25c4: ret             
    // 0xeb25c8: ldur            x5, [fp, #-8]
    // 0xeb25cc: LoadField: r1 = r5->field_f
    //     0xeb25cc: ldur            w1, [x5, #0xf]
    // 0xeb25d0: DecompressPointer r1
    //     0xeb25d0: add             x1, x1, HEAP, lsl #32
    // 0xeb25d4: r0 = LoadClassIdInstr(r1)
    //     0xeb25d4: ldur            x0, [x1, #-1]
    //     0xeb25d8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb25dc: mov             x2, x3
    // 0xeb25e0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb25e0: sub             lr, x0, #1, lsl #12
    //     0xeb25e4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb25e8: blr             lr
    // 0xeb25ec: mov             x3, x0
    // 0xeb25f0: stur            x3, [fp, #-0x28]
    // 0xeb25f4: r4 = LoadClassIdInstr(r3)
    //     0xeb25f4: ldur            x4, [x3, #-1]
    //     0xeb25f8: ubfx            x4, x4, #0xc, #0x14
    // 0xeb25fc: stur            x4, [fp, #-0x20]
    // 0xeb2600: cmp             x4, #0x2f3
    // 0xeb2604: b.ne            #0xeb2618
    // 0xeb2608: mov             x0, x3
    // 0xeb260c: LeaveFrame
    //     0xeb260c: mov             SP, fp
    //     0xeb2610: ldp             fp, lr, [SP], #0x10
    // 0xeb2614: ret
    //     0xeb2614: ret             
    // 0xeb2618: ldur            x5, [fp, #-8]
    // 0xeb261c: LoadField: r1 = r5->field_13
    //     0xeb261c: ldur            w1, [x5, #0x13]
    // 0xeb2620: DecompressPointer r1
    //     0xeb2620: add             x1, x1, HEAP, lsl #32
    // 0xeb2624: r0 = LoadClassIdInstr(r1)
    //     0xeb2624: ldur            x0, [x1, #-1]
    //     0xeb2628: ubfx            x0, x0, #0xc, #0x14
    // 0xeb262c: mov             x2, x3
    // 0xeb2630: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2630: sub             lr, x0, #1, lsl #12
    //     0xeb2634: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2638: blr             lr
    // 0xeb263c: mov             x3, x0
    // 0xeb2640: stur            x3, [fp, #-0x38]
    // 0xeb2644: r4 = LoadClassIdInstr(r3)
    //     0xeb2644: ldur            x4, [x3, #-1]
    //     0xeb2648: ubfx            x4, x4, #0xc, #0x14
    // 0xeb264c: stur            x4, [fp, #-0x30]
    // 0xeb2650: cmp             x4, #0x2f3
    // 0xeb2654: b.ne            #0xeb2668
    // 0xeb2658: mov             x0, x3
    // 0xeb265c: LeaveFrame
    //     0xeb265c: mov             SP, fp
    //     0xeb2660: ldp             fp, lr, [SP], #0x10
    // 0xeb2664: ret
    //     0xeb2664: ret             
    // 0xeb2668: ldur            x5, [fp, #-8]
    // 0xeb266c: ArrayLoad: r1 = r5[0]  ; List_4
    //     0xeb266c: ldur            w1, [x5, #0x17]
    // 0xeb2670: DecompressPointer r1
    //     0xeb2670: add             x1, x1, HEAP, lsl #32
    // 0xeb2674: r0 = LoadClassIdInstr(r1)
    //     0xeb2674: ldur            x0, [x1, #-1]
    //     0xeb2678: ubfx            x0, x0, #0xc, #0x14
    // 0xeb267c: mov             x2, x3
    // 0xeb2680: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2680: sub             lr, x0, #1, lsl #12
    //     0xeb2684: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2688: blr             lr
    // 0xeb268c: mov             x3, x0
    // 0xeb2690: stur            x3, [fp, #-0x48]
    // 0xeb2694: r4 = LoadClassIdInstr(r3)
    //     0xeb2694: ldur            x4, [x3, #-1]
    //     0xeb2698: ubfx            x4, x4, #0xc, #0x14
    // 0xeb269c: stur            x4, [fp, #-0x40]
    // 0xeb26a0: cmp             x4, #0x2f3
    // 0xeb26a4: b.ne            #0xeb26b8
    // 0xeb26a8: mov             x0, x3
    // 0xeb26ac: LeaveFrame
    //     0xeb26ac: mov             SP, fp
    //     0xeb26b0: ldp             fp, lr, [SP], #0x10
    // 0xeb26b4: ret
    //     0xeb26b4: ret             
    // 0xeb26b8: ldur            x5, [fp, #-8]
    // 0xeb26bc: LoadField: r1 = r5->field_1b
    //     0xeb26bc: ldur            w1, [x5, #0x1b]
    // 0xeb26c0: DecompressPointer r1
    //     0xeb26c0: add             x1, x1, HEAP, lsl #32
    // 0xeb26c4: r0 = LoadClassIdInstr(r1)
    //     0xeb26c4: ldur            x0, [x1, #-1]
    //     0xeb26c8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb26cc: mov             x2, x3
    // 0xeb26d0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb26d0: sub             lr, x0, #1, lsl #12
    //     0xeb26d4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb26d8: blr             lr
    // 0xeb26dc: stur            x0, [fp, #-0x60]
    // 0xeb26e0: r1 = LoadClassIdInstr(r0)
    //     0xeb26e0: ldur            x1, [x0, #-1]
    //     0xeb26e4: ubfx            x1, x1, #0xc, #0x14
    // 0xeb26e8: stur            x1, [fp, #-0x58]
    // 0xeb26ec: cmp             x1, #0x2f3
    // 0xeb26f0: b.ne            #0xeb2700
    // 0xeb26f4: LeaveFrame
    //     0xeb26f4: mov             SP, fp
    //     0xeb26f8: ldp             fp, lr, [SP], #0x10
    // 0xeb26fc: ret
    //     0xeb26fc: ret             
    // 0xeb2700: ldur            x2, [fp, #-8]
    // 0xeb2704: ldur            x3, [fp, #-0x10]
    // 0xeb2708: LoadField: r4 = r2->field_7
    //     0xeb2708: ldur            w4, [x2, #7]
    // 0xeb270c: DecompressPointer r4
    //     0xeb270c: add             x4, x4, HEAP, lsl #32
    // 0xeb2710: stur            x4, [fp, #-0x50]
    // 0xeb2714: cmp             x3, #0x2f3
    // 0xeb2718: b.eq            #0xeb2800
    // 0xeb271c: ldur            x3, [fp, #-0x18]
    // 0xeb2720: ldur            x2, [fp, #-0x20]
    // 0xeb2724: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xeb2724: ldur            w5, [x3, #0x17]
    // 0xeb2728: DecompressPointer r5
    //     0xeb2728: add             x5, x5, HEAP, lsl #32
    // 0xeb272c: stur            x5, [fp, #-8]
    // 0xeb2730: r1 = 10
    //     0xeb2730: movz            x1, #0xa
    // 0xeb2734: r0 = AllocateRecord()
    //     0xeb2734: bl              #0xec1080  ; AllocateRecordStub
    // 0xeb2738: mov             x2, x0
    // 0xeb273c: ldur            x0, [fp, #-8]
    // 0xeb2740: stur            x2, [fp, #-0x68]
    // 0xeb2744: StoreField: r2->field_f = r0
    //     0xeb2744: stur            w0, [x2, #0xf]
    // 0xeb2748: ldur            x0, [fp, #-0x20]
    // 0xeb274c: cmp             x0, #0x2f3
    // 0xeb2750: b.eq            #0xeb2820
    // 0xeb2754: ldur            x1, [fp, #-0x28]
    // 0xeb2758: ldur            x0, [fp, #-0x30]
    // 0xeb275c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb275c: ldur            w3, [x1, #0x17]
    // 0xeb2760: DecompressPointer r3
    //     0xeb2760: add             x3, x3, HEAP, lsl #32
    // 0xeb2764: StoreField: r2->field_13 = r3
    //     0xeb2764: stur            w3, [x2, #0x13]
    // 0xeb2768: cmp             x0, #0x2f3
    // 0xeb276c: b.eq            #0xeb2840
    // 0xeb2770: ldur            x1, [fp, #-0x38]
    // 0xeb2774: ldur            x0, [fp, #-0x40]
    // 0xeb2778: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb2778: ldur            w3, [x1, #0x17]
    // 0xeb277c: DecompressPointer r3
    //     0xeb277c: add             x3, x3, HEAP, lsl #32
    // 0xeb2780: ArrayStore: r2[0] = r3  ; List_4
    //     0xeb2780: stur            w3, [x2, #0x17]
    // 0xeb2784: cmp             x0, #0x2f3
    // 0xeb2788: b.eq            #0xeb2860
    // 0xeb278c: ldur            x1, [fp, #-0x48]
    // 0xeb2790: ldur            x0, [fp, #-0x58]
    // 0xeb2794: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb2794: ldur            w3, [x1, #0x17]
    // 0xeb2798: DecompressPointer r3
    //     0xeb2798: add             x3, x3, HEAP, lsl #32
    // 0xeb279c: StoreField: r2->field_1b = r3
    //     0xeb279c: stur            w3, [x2, #0x1b]
    // 0xeb27a0: cmp             x0, #0x2f3
    // 0xeb27a4: b.eq            #0xeb2880
    // 0xeb27a8: ldur            x0, [fp, #-0x60]
    // 0xeb27ac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb27ac: ldur            w1, [x0, #0x17]
    // 0xeb27b0: DecompressPointer r1
    //     0xeb27b0: add             x1, x1, HEAP, lsl #32
    // 0xeb27b4: StoreField: r2->field_1f = r1
    //     0xeb27b4: stur            w1, [x2, #0x1f]
    // 0xeb27b8: LoadField: r3 = r0->field_7
    //     0xeb27b8: ldur            w3, [x0, #7]
    // 0xeb27bc: DecompressPointer r3
    //     0xeb27bc: add             x3, x3, HEAP, lsl #32
    // 0xeb27c0: stur            x3, [fp, #-8]
    // 0xeb27c4: LoadField: r4 = r0->field_b
    //     0xeb27c4: ldur            x4, [x0, #0xb]
    // 0xeb27c8: ldur            x1, [fp, #-0x50]
    // 0xeb27cc: stur            x4, [fp, #-0x10]
    // 0xeb27d0: r0 = Success()
    //     0xeb27d0: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb27d4: mov             x1, x0
    // 0xeb27d8: ldur            x0, [fp, #-0x68]
    // 0xeb27dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb27dc: stur            w0, [x1, #0x17]
    // 0xeb27e0: ldur            x0, [fp, #-8]
    // 0xeb27e4: StoreField: r1->field_7 = r0
    //     0xeb27e4: stur            w0, [x1, #7]
    // 0xeb27e8: ldur            x0, [fp, #-0x10]
    // 0xeb27ec: StoreField: r1->field_b = r0
    //     0xeb27ec: stur            x0, [x1, #0xb]
    // 0xeb27f0: mov             x0, x1
    // 0xeb27f4: LeaveFrame
    //     0xeb27f4: mov             SP, fp
    //     0xeb27f8: ldp             fp, lr, [SP], #0x10
    // 0xeb27fc: ret
    //     0xeb27fc: ret             
    // 0xeb2800: ldur            x3, [fp, #-0x18]
    // 0xeb2804: r0 = ParserException()
    //     0xeb2804: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2808: mov             x1, x0
    // 0xeb280c: ldur            x0, [fp, #-0x18]
    // 0xeb2810: StoreField: r1->field_7 = r0
    //     0xeb2810: stur            w0, [x1, #7]
    // 0xeb2814: mov             x0, x1
    // 0xeb2818: r0 = Throw()
    //     0xeb2818: bl              #0xec04b8  ; ThrowStub
    // 0xeb281c: brk             #0
    // 0xeb2820: ldur            x1, [fp, #-0x28]
    // 0xeb2824: r0 = ParserException()
    //     0xeb2824: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2828: mov             x1, x0
    // 0xeb282c: ldur            x0, [fp, #-0x28]
    // 0xeb2830: StoreField: r1->field_7 = r0
    //     0xeb2830: stur            w0, [x1, #7]
    // 0xeb2834: mov             x0, x1
    // 0xeb2838: r0 = Throw()
    //     0xeb2838: bl              #0xec04b8  ; ThrowStub
    // 0xeb283c: brk             #0
    // 0xeb2840: ldur            x1, [fp, #-0x38]
    // 0xeb2844: r0 = ParserException()
    //     0xeb2844: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2848: mov             x1, x0
    // 0xeb284c: ldur            x0, [fp, #-0x38]
    // 0xeb2850: StoreField: r1->field_7 = r0
    //     0xeb2850: stur            w0, [x1, #7]
    // 0xeb2854: mov             x0, x1
    // 0xeb2858: r0 = Throw()
    //     0xeb2858: bl              #0xec04b8  ; ThrowStub
    // 0xeb285c: brk             #0
    // 0xeb2860: ldur            x1, [fp, #-0x48]
    // 0xeb2864: r0 = ParserException()
    //     0xeb2864: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2868: mov             x1, x0
    // 0xeb286c: ldur            x0, [fp, #-0x48]
    // 0xeb2870: StoreField: r1->field_7 = r0
    //     0xeb2870: stur            w0, [x1, #7]
    // 0xeb2874: mov             x0, x1
    // 0xeb2878: r0 = Throw()
    //     0xeb2878: bl              #0xec04b8  ; ThrowStub
    // 0xeb287c: brk             #0
    // 0xeb2880: ldur            x0, [fp, #-0x60]
    // 0xeb2884: r0 = ParserException()
    //     0xeb2884: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2888: mov             x1, x0
    // 0xeb288c: ldur            x0, [fp, #-0x60]
    // 0xeb2890: StoreField: r1->field_7 = r0
    //     0xeb2890: stur            w0, [x1, #7]
    // 0xeb2894: mov             x0, x1
    // 0xeb2898: r0 = Throw()
    //     0xeb2898: bl              #0xec04b8  ; ThrowStub
    // 0xeb289c: brk             #0
    // 0xeb28a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb28a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb28a4: b               #0xeb2580
  }
}
