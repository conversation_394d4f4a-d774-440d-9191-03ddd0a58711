// lib: , url: package:petitparser/src/parser/combinator/generated/sequence_8.dart

// class id: 1050901, size: 0x8
class :: {

  static Parser<Y8> RecordParserExtension8.map8<Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7, Y8>(Parser<(Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7)>, (dynamic, Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7) => Y8) {
    // ** addr: 0x88aa80, size: 0xbc
    // 0x88aa80: EnterFrame
    //     0x88aa80: stp             fp, lr, [SP, #-0x10]!
    //     0x88aa84: mov             fp, SP
    // 0x88aa88: AllocStack(0x28)
    //     0x88aa88: sub             SP, SP, #0x28
    // 0x88aa8c: SetupParameters()
    //     0x88aa8c: ldur            w0, [x4, #0xf]
    //     0x88aa90: cbnz            w0, #0x88aa9c
    //     0x88aa94: mov             x1, NULL
    //     0x88aa98: b               #0x88aaa8
    //     0x88aa9c: ldur            w0, [x4, #0x17]
    //     0x88aaa0: add             x1, fp, w0, sxtw #2
    //     0x88aaa4: ldr             x1, [x1, #0x10]
    //     0x88aaa8: ldr             x0, [fp, #0x10]
    //     0x88aaac: stur            x1, [fp, #-8]
    // 0x88aab0: CheckStackOverflow
    //     0x88aab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88aab4: cmp             SP, x16
    //     0x88aab8: b.ls            #0x88ab34
    // 0x88aabc: r1 = 1
    //     0x88aabc: movz            x1, #0x1
    // 0x88aac0: r0 = AllocateContext()
    //     0x88aac0: bl              #0xec126c  ; AllocateContextStub
    // 0x88aac4: mov             x4, x0
    // 0x88aac8: ldr             x0, [fp, #0x10]
    // 0x88aacc: stur            x4, [fp, #-0x10]
    // 0x88aad0: StoreField: r4->field_f = r0
    //     0x88aad0: stur            w0, [x4, #0xf]
    // 0x88aad4: ldur            x1, [fp, #-8]
    // 0x88aad8: r2 = Null
    //     0x88aad8: mov             x2, NULL
    // 0x88aadc: r3 = <(Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7), Y8>
    //     0x88aadc: add             x3, PP, #0x26, lsl #12  ; [pp+0x26a18] TypeArguments: <(Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7), Y8>
    //     0x88aae0: ldr             x3, [x3, #0xa18]
    // 0x88aae4: r30 = InstantiateTypeArgumentsStub
    //     0x88aae4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88aae8: LoadField: r30 = r30->field_7
    //     0x88aae8: ldur            lr, [lr, #7]
    // 0x88aaec: blr             lr
    // 0x88aaf0: ldur            x2, [fp, #-0x10]
    // 0x88aaf4: r1 = Function '<anonymous closure>': static.
    //     0x88aaf4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a20] AnonymousClosure: static (0x88abcc), in [package:petitparser/src/parser/combinator/generated/sequence_8.dart] ::RecordParserExtension8.map8 (0x88aa80)
    //     0x88aaf8: ldr             x1, [x1, #0xa20]
    // 0x88aafc: stur            x0, [fp, #-0x10]
    // 0x88ab00: r0 = AllocateClosure()
    //     0x88ab00: bl              #0xec1630  ; AllocateClosureStub
    // 0x88ab04: mov             x1, x0
    // 0x88ab08: ldur            x0, [fp, #-8]
    // 0x88ab0c: StoreField: r1->field_b = r0
    //     0x88ab0c: stur            w0, [x1, #0xb]
    // 0x88ab10: ldur            x16, [fp, #-0x10]
    // 0x88ab14: ldr             lr, [fp, #0x18]
    // 0x88ab18: stp             lr, x16, [SP, #8]
    // 0x88ab1c: str             x1, [SP]
    // 0x88ab20: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88ab20: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88ab24: r0 = MapParserExtension.map()
    //     0x88ab24: bl              #0x88ab3c  ; [package:petitparser/src/parser/action/map.dart] ::MapParserExtension.map
    // 0x88ab28: LeaveFrame
    //     0x88ab28: mov             SP, fp
    //     0x88ab2c: ldp             fp, lr, [SP], #0x10
    // 0x88ab30: ret
    //     0x88ab30: ret             
    // 0x88ab34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ab34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88ab38: b               #0x88aabc
  }
  [closure] static Y8 <anonymous closure>(dynamic, (Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7)) {
    // ** addr: 0x88abcc, size: 0xa8
    // 0x88abcc: EnterFrame
    //     0x88abcc: stp             fp, lr, [SP, #-0x10]!
    //     0x88abd0: mov             fp, SP
    // 0x88abd4: AllocStack(0x48)
    //     0x88abd4: sub             SP, SP, #0x48
    // 0x88abd8: SetupParameters()
    //     0x88abd8: ldr             x0, [fp, #0x18]
    //     0x88abdc: ldur            w1, [x0, #0x17]
    //     0x88abe0: add             x1, x1, HEAP, lsl #32
    // 0x88abe4: CheckStackOverflow
    //     0x88abe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88abe8: cmp             SP, x16
    //     0x88abec: b.ls            #0x88ac6c
    // 0x88abf0: LoadField: r0 = r1->field_f
    //     0x88abf0: ldur            w0, [x1, #0xf]
    // 0x88abf4: DecompressPointer r0
    //     0x88abf4: add             x0, x0, HEAP, lsl #32
    // 0x88abf8: ldr             x1, [fp, #0x10]
    // 0x88abfc: LoadField: r2 = r1->field_f
    //     0x88abfc: ldur            w2, [x1, #0xf]
    // 0x88ac00: DecompressPointer r2
    //     0x88ac00: add             x2, x2, HEAP, lsl #32
    // 0x88ac04: LoadField: r3 = r1->field_13
    //     0x88ac04: ldur            w3, [x1, #0x13]
    // 0x88ac08: DecompressPointer r3
    //     0x88ac08: add             x3, x3, HEAP, lsl #32
    // 0x88ac0c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x88ac0c: ldur            w4, [x1, #0x17]
    // 0x88ac10: DecompressPointer r4
    //     0x88ac10: add             x4, x4, HEAP, lsl #32
    // 0x88ac14: LoadField: r5 = r1->field_1b
    //     0x88ac14: ldur            w5, [x1, #0x1b]
    // 0x88ac18: DecompressPointer r5
    //     0x88ac18: add             x5, x5, HEAP, lsl #32
    // 0x88ac1c: LoadField: r6 = r1->field_1f
    //     0x88ac1c: ldur            w6, [x1, #0x1f]
    // 0x88ac20: DecompressPointer r6
    //     0x88ac20: add             x6, x6, HEAP, lsl #32
    // 0x88ac24: LoadField: r7 = r1->field_23
    //     0x88ac24: ldur            w7, [x1, #0x23]
    // 0x88ac28: DecompressPointer r7
    //     0x88ac28: add             x7, x7, HEAP, lsl #32
    // 0x88ac2c: LoadField: r8 = r1->field_27
    //     0x88ac2c: ldur            w8, [x1, #0x27]
    // 0x88ac30: DecompressPointer r8
    //     0x88ac30: add             x8, x8, HEAP, lsl #32
    // 0x88ac34: LoadField: r9 = r1->field_2b
    //     0x88ac34: ldur            w9, [x1, #0x2b]
    // 0x88ac38: DecompressPointer r9
    //     0x88ac38: add             x9, x9, HEAP, lsl #32
    // 0x88ac3c: stp             x2, x0, [SP, #0x38]
    // 0x88ac40: stp             x4, x3, [SP, #0x28]
    // 0x88ac44: stp             x6, x5, [SP, #0x18]
    // 0x88ac48: stp             x8, x7, [SP, #8]
    // 0x88ac4c: str             x9, [SP]
    // 0x88ac50: ClosureCall
    //     0x88ac50: add             x4, PP, #0x26, lsl #12  ; [pp+0x26a28] List(5) [0, 0x9, 0x9, 0x9, Null]
    //     0x88ac54: ldr             x4, [x4, #0xa28]
    //     0x88ac58: ldur            x2, [x0, #0x1f]
    //     0x88ac5c: blr             x2
    // 0x88ac60: LeaveFrame
    //     0x88ac60: mov             SP, fp
    //     0x88ac64: ldp             fp, lr, [SP], #0x10
    // 0x88ac68: ret
    //     0x88ac68: ret             
    // 0x88ac6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ac6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88ac70: b               #0x88abf0
  }
  static Parser<(Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7)> seq8<Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7>(Parser<Y0>, Parser<Y1>, Parser<Y2>, Parser<Y3>, Parser<Y4>, Parser<Y5>, Parser<Y6>, Parser<Y7>) {
    // ** addr: 0x88ac74, size: 0xb0
    // 0x88ac74: EnterFrame
    //     0x88ac74: stp             fp, lr, [SP, #-0x10]!
    //     0x88ac78: mov             fp, SP
    // 0x88ac7c: LoadField: r0 = r4->field_f
    //     0x88ac7c: ldur            w0, [x4, #0xf]
    // 0x88ac80: cbnz            w0, #0x88ac8c
    // 0x88ac84: r1 = Null
    //     0x88ac84: mov             x1, NULL
    // 0x88ac88: b               #0x88ac98
    // 0x88ac8c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88ac8c: ldur            w0, [x4, #0x17]
    // 0x88ac90: add             x1, fp, w0, sxtw #2
    // 0x88ac94: ldr             x1, [x1, #0x10]
    // 0x88ac98: ldr             x10, [fp, #0x48]
    // 0x88ac9c: ldr             x9, [fp, #0x40]
    // 0x88aca0: ldr             x8, [fp, #0x38]
    // 0x88aca4: ldr             x7, [fp, #0x30]
    // 0x88aca8: ldr             x6, [fp, #0x28]
    // 0x88acac: ldr             x5, [fp, #0x20]
    // 0x88acb0: ldr             x4, [fp, #0x18]
    // 0x88acb4: ldr             x0, [fp, #0x10]
    // 0x88acb8: r2 = Null
    //     0x88acb8: mov             x2, NULL
    // 0x88acbc: r3 = <(Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7), Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7>
    //     0x88acbc: add             x3, PP, #0x26, lsl #12  ; [pp+0x26a30] TypeArguments: <(Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7), Y0, Y1, Y2, Y3, Y4, Y5, Y6, Y7>
    //     0x88acc0: ldr             x3, [x3, #0xa30]
    // 0x88acc4: r30 = InstantiateTypeArgumentsStub
    //     0x88acc4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88acc8: LoadField: r30 = r30->field_7
    //     0x88acc8: ldur            lr, [lr, #7]
    // 0x88accc: blr             lr
    // 0x88acd0: mov             x1, x0
    // 0x88acd4: r0 = SequenceParser8()
    //     0x88acd4: bl              #0x88ad24  ; AllocateSequenceParser8Stub -> SequenceParser8<C1X0, C1X1, C1X2, C1X3, C1X4, C1X5, C1X6, C1X7> (size=0x2c)
    // 0x88acd8: ldr             x1, [fp, #0x48]
    // 0x88acdc: StoreField: r0->field_b = r1
    //     0x88acdc: stur            w1, [x0, #0xb]
    // 0x88ace0: ldr             x1, [fp, #0x40]
    // 0x88ace4: StoreField: r0->field_f = r1
    //     0x88ace4: stur            w1, [x0, #0xf]
    // 0x88ace8: ldr             x1, [fp, #0x38]
    // 0x88acec: StoreField: r0->field_13 = r1
    //     0x88acec: stur            w1, [x0, #0x13]
    // 0x88acf0: ldr             x1, [fp, #0x30]
    // 0x88acf4: ArrayStore: r0[0] = r1  ; List_4
    //     0x88acf4: stur            w1, [x0, #0x17]
    // 0x88acf8: ldr             x1, [fp, #0x28]
    // 0x88acfc: StoreField: r0->field_1b = r1
    //     0x88acfc: stur            w1, [x0, #0x1b]
    // 0x88ad00: ldr             x1, [fp, #0x20]
    // 0x88ad04: StoreField: r0->field_1f = r1
    //     0x88ad04: stur            w1, [x0, #0x1f]
    // 0x88ad08: ldr             x1, [fp, #0x18]
    // 0x88ad0c: StoreField: r0->field_23 = r1
    //     0x88ad0c: stur            w1, [x0, #0x23]
    // 0x88ad10: ldr             x1, [fp, #0x10]
    // 0x88ad14: StoreField: r0->field_27 = r1
    //     0x88ad14: stur            w1, [x0, #0x27]
    // 0x88ad18: LeaveFrame
    //     0x88ad18: mov             SP, fp
    //     0x88ad1c: ldp             fp, lr, [SP], #0x10
    // 0x88ad20: ret
    //     0x88ad20: ret             
  }
}

// class id: 734, size: 0x2c, field offset: 0xc
class SequenceParser8<C1X0, C1X1, C1X2, C1X3, C1X4, C1X5, C1X6, C1X7> extends Parser<C1X0>
    implements SequentialParser {

  get _ children(/* No info */) {
    // ** addr: 0x88fbe8, size: 0xec
    // 0x88fbe8: EnterFrame
    //     0x88fbe8: stp             fp, lr, [SP, #-0x10]!
    //     0x88fbec: mov             fp, SP
    // 0x88fbf0: AllocStack(0x48)
    //     0x88fbf0: sub             SP, SP, #0x48
    // 0x88fbf4: r0 = 16
    //     0x88fbf4: movz            x0, #0x10
    // 0x88fbf8: LoadField: r3 = r1->field_b
    //     0x88fbf8: ldur            w3, [x1, #0xb]
    // 0x88fbfc: DecompressPointer r3
    //     0x88fbfc: add             x3, x3, HEAP, lsl #32
    // 0x88fc00: stur            x3, [fp, #-0x40]
    // 0x88fc04: LoadField: r4 = r1->field_f
    //     0x88fc04: ldur            w4, [x1, #0xf]
    // 0x88fc08: DecompressPointer r4
    //     0x88fc08: add             x4, x4, HEAP, lsl #32
    // 0x88fc0c: stur            x4, [fp, #-0x38]
    // 0x88fc10: LoadField: r5 = r1->field_13
    //     0x88fc10: ldur            w5, [x1, #0x13]
    // 0x88fc14: DecompressPointer r5
    //     0x88fc14: add             x5, x5, HEAP, lsl #32
    // 0x88fc18: stur            x5, [fp, #-0x30]
    // 0x88fc1c: ArrayLoad: r6 = r1[0]  ; List_4
    //     0x88fc1c: ldur            w6, [x1, #0x17]
    // 0x88fc20: DecompressPointer r6
    //     0x88fc20: add             x6, x6, HEAP, lsl #32
    // 0x88fc24: stur            x6, [fp, #-0x28]
    // 0x88fc28: LoadField: r7 = r1->field_1b
    //     0x88fc28: ldur            w7, [x1, #0x1b]
    // 0x88fc2c: DecompressPointer r7
    //     0x88fc2c: add             x7, x7, HEAP, lsl #32
    // 0x88fc30: stur            x7, [fp, #-0x20]
    // 0x88fc34: LoadField: r8 = r1->field_1f
    //     0x88fc34: ldur            w8, [x1, #0x1f]
    // 0x88fc38: DecompressPointer r8
    //     0x88fc38: add             x8, x8, HEAP, lsl #32
    // 0x88fc3c: stur            x8, [fp, #-0x18]
    // 0x88fc40: LoadField: r9 = r1->field_23
    //     0x88fc40: ldur            w9, [x1, #0x23]
    // 0x88fc44: DecompressPointer r9
    //     0x88fc44: add             x9, x9, HEAP, lsl #32
    // 0x88fc48: stur            x9, [fp, #-0x10]
    // 0x88fc4c: LoadField: r10 = r1->field_27
    //     0x88fc4c: ldur            w10, [x1, #0x27]
    // 0x88fc50: DecompressPointer r10
    //     0x88fc50: add             x10, x10, HEAP, lsl #32
    // 0x88fc54: mov             x2, x0
    // 0x88fc58: stur            x10, [fp, #-8]
    // 0x88fc5c: r1 = Null
    //     0x88fc5c: mov             x1, NULL
    // 0x88fc60: r0 = AllocateArray()
    //     0x88fc60: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88fc64: mov             x2, x0
    // 0x88fc68: ldur            x0, [fp, #-0x40]
    // 0x88fc6c: stur            x2, [fp, #-0x48]
    // 0x88fc70: StoreField: r2->field_f = r0
    //     0x88fc70: stur            w0, [x2, #0xf]
    // 0x88fc74: ldur            x0, [fp, #-0x38]
    // 0x88fc78: StoreField: r2->field_13 = r0
    //     0x88fc78: stur            w0, [x2, #0x13]
    // 0x88fc7c: ldur            x0, [fp, #-0x30]
    // 0x88fc80: ArrayStore: r2[0] = r0  ; List_4
    //     0x88fc80: stur            w0, [x2, #0x17]
    // 0x88fc84: ldur            x0, [fp, #-0x28]
    // 0x88fc88: StoreField: r2->field_1b = r0
    //     0x88fc88: stur            w0, [x2, #0x1b]
    // 0x88fc8c: ldur            x0, [fp, #-0x20]
    // 0x88fc90: StoreField: r2->field_1f = r0
    //     0x88fc90: stur            w0, [x2, #0x1f]
    // 0x88fc94: ldur            x0, [fp, #-0x18]
    // 0x88fc98: StoreField: r2->field_23 = r0
    //     0x88fc98: stur            w0, [x2, #0x23]
    // 0x88fc9c: ldur            x0, [fp, #-0x10]
    // 0x88fca0: StoreField: r2->field_27 = r0
    //     0x88fca0: stur            w0, [x2, #0x27]
    // 0x88fca4: ldur            x0, [fp, #-8]
    // 0x88fca8: StoreField: r2->field_2b = r0
    //     0x88fca8: stur            w0, [x2, #0x2b]
    // 0x88fcac: r1 = <Parser>
    //     0x88fcac: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x88fcb0: ldr             x1, [x1, #0x6f8]
    // 0x88fcb4: r0 = AllocateGrowableArray()
    //     0x88fcb4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88fcb8: ldur            x1, [fp, #-0x48]
    // 0x88fcbc: StoreField: r0->field_f = r1
    //     0x88fcbc: stur            w1, [x0, #0xf]
    // 0x88fcc0: r1 = 16
    //     0x88fcc0: movz            x1, #0x10
    // 0x88fcc4: StoreField: r0->field_b = r1
    //     0x88fcc4: stur            w1, [x0, #0xb]
    // 0x88fcc8: LeaveFrame
    //     0x88fcc8: mov             SP, fp
    //     0x88fccc: ldp             fp, lr, [SP], #0x10
    // 0x88fcd0: ret
    //     0x88fcd0: ret             
  }
  _ replace(/* No info */) {
    // ** addr: 0x894ab8, size: 0x45c
    // 0x894ab8: EnterFrame
    //     0x894ab8: stp             fp, lr, [SP, #-0x10]!
    //     0x894abc: mov             fp, SP
    // 0x894ac0: AllocStack(0x28)
    //     0x894ac0: sub             SP, SP, #0x28
    // 0x894ac4: SetupParameters(SequenceParser8<C1X0, C1X1, C1X2, C1X3, C1X4, C1X5, C1X6, C1X7> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0x894ac4: stur            x1, [fp, #-8]
    //     0x894ac8: mov             x16, x3
    //     0x894acc: mov             x3, x1
    //     0x894ad0: mov             x1, x16
    //     0x894ad4: stur            x2, [fp, #-0x10]
    //     0x894ad8: stur            x1, [fp, #-0x18]
    // 0x894adc: CheckStackOverflow
    //     0x894adc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x894ae0: cmp             SP, x16
    //     0x894ae4: b.ls            #0x894f0c
    // 0x894ae8: LoadField: r0 = r3->field_b
    //     0x894ae8: ldur            w0, [x3, #0xb]
    // 0x894aec: DecompressPointer r0
    //     0x894aec: add             x0, x0, HEAP, lsl #32
    // 0x894af0: r4 = LoadClassIdInstr(r0)
    //     0x894af0: ldur            x4, [x0, #-1]
    //     0x894af4: ubfx            x4, x4, #0xc, #0x14
    // 0x894af8: stp             x2, x0, [SP]
    // 0x894afc: mov             x0, x4
    // 0x894b00: mov             lr, x0
    // 0x894b04: ldr             lr, [x21, lr, lsl #3]
    // 0x894b08: blr             lr
    // 0x894b0c: tbnz            w0, #4, #0x894b64
    // 0x894b10: ldur            x3, [fp, #-8]
    // 0x894b14: LoadField: r2 = r3->field_7
    //     0x894b14: ldur            w2, [x3, #7]
    // 0x894b18: DecompressPointer r2
    //     0x894b18: add             x2, x2, HEAP, lsl #32
    // 0x894b1c: ldur            x0, [fp, #-0x18]
    // 0x894b20: r1 = Null
    //     0x894b20: mov             x1, NULL
    // 0x894b24: r8 = Parser<C1X0>
    //     0x894b24: add             x8, PP, #0x31, lsl #12  ; [pp+0x31258] Type: Parser<C1X0>
    //     0x894b28: ldr             x8, [x8, #0x258]
    // 0x894b2c: LoadField: r9 = r8->field_7
    //     0x894b2c: ldur            x9, [x8, #7]
    // 0x894b30: r3 = Null
    //     0x894b30: add             x3, PP, #0x31, lsl #12  ; [pp+0x31260] Null
    //     0x894b34: ldr             x3, [x3, #0x260]
    // 0x894b38: blr             x9
    // 0x894b3c: ldur            x0, [fp, #-0x18]
    // 0x894b40: ldur            x1, [fp, #-8]
    // 0x894b44: StoreField: r1->field_b = r0
    //     0x894b44: stur            w0, [x1, #0xb]
    //     0x894b48: ldurb           w16, [x1, #-1]
    //     0x894b4c: ldurb           w17, [x0, #-1]
    //     0x894b50: and             x16, x17, x16, lsr #2
    //     0x894b54: tst             x16, HEAP, lsr #32
    //     0x894b58: b.eq            #0x894b60
    //     0x894b5c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894b60: b               #0x894b68
    // 0x894b64: ldur            x1, [fp, #-8]
    // 0x894b68: LoadField: r0 = r1->field_f
    //     0x894b68: ldur            w0, [x1, #0xf]
    // 0x894b6c: DecompressPointer r0
    //     0x894b6c: add             x0, x0, HEAP, lsl #32
    // 0x894b70: r2 = LoadClassIdInstr(r0)
    //     0x894b70: ldur            x2, [x0, #-1]
    //     0x894b74: ubfx            x2, x2, #0xc, #0x14
    // 0x894b78: ldur            x16, [fp, #-0x10]
    // 0x894b7c: stp             x16, x0, [SP]
    // 0x894b80: mov             x0, x2
    // 0x894b84: mov             lr, x0
    // 0x894b88: ldr             lr, [x21, lr, lsl #3]
    // 0x894b8c: blr             lr
    // 0x894b90: tbnz            w0, #4, #0x894be8
    // 0x894b94: ldur            x3, [fp, #-8]
    // 0x894b98: LoadField: r2 = r3->field_7
    //     0x894b98: ldur            w2, [x3, #7]
    // 0x894b9c: DecompressPointer r2
    //     0x894b9c: add             x2, x2, HEAP, lsl #32
    // 0x894ba0: ldur            x0, [fp, #-0x18]
    // 0x894ba4: r1 = Null
    //     0x894ba4: mov             x1, NULL
    // 0x894ba8: r8 = Parser<C1X1>
    //     0x894ba8: add             x8, PP, #0x31, lsl #12  ; [pp+0x31270] Type: Parser<C1X1>
    //     0x894bac: ldr             x8, [x8, #0x270]
    // 0x894bb0: LoadField: r9 = r8->field_7
    //     0x894bb0: ldur            x9, [x8, #7]
    // 0x894bb4: r3 = Null
    //     0x894bb4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31278] Null
    //     0x894bb8: ldr             x3, [x3, #0x278]
    // 0x894bbc: blr             x9
    // 0x894bc0: ldur            x0, [fp, #-0x18]
    // 0x894bc4: ldur            x1, [fp, #-8]
    // 0x894bc8: StoreField: r1->field_f = r0
    //     0x894bc8: stur            w0, [x1, #0xf]
    //     0x894bcc: ldurb           w16, [x1, #-1]
    //     0x894bd0: ldurb           w17, [x0, #-1]
    //     0x894bd4: and             x16, x17, x16, lsr #2
    //     0x894bd8: tst             x16, HEAP, lsr #32
    //     0x894bdc: b.eq            #0x894be4
    //     0x894be0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894be4: b               #0x894bec
    // 0x894be8: ldur            x1, [fp, #-8]
    // 0x894bec: LoadField: r0 = r1->field_13
    //     0x894bec: ldur            w0, [x1, #0x13]
    // 0x894bf0: DecompressPointer r0
    //     0x894bf0: add             x0, x0, HEAP, lsl #32
    // 0x894bf4: r2 = LoadClassIdInstr(r0)
    //     0x894bf4: ldur            x2, [x0, #-1]
    //     0x894bf8: ubfx            x2, x2, #0xc, #0x14
    // 0x894bfc: ldur            x16, [fp, #-0x10]
    // 0x894c00: stp             x16, x0, [SP]
    // 0x894c04: mov             x0, x2
    // 0x894c08: mov             lr, x0
    // 0x894c0c: ldr             lr, [x21, lr, lsl #3]
    // 0x894c10: blr             lr
    // 0x894c14: tbnz            w0, #4, #0x894c6c
    // 0x894c18: ldur            x3, [fp, #-8]
    // 0x894c1c: LoadField: r2 = r3->field_7
    //     0x894c1c: ldur            w2, [x3, #7]
    // 0x894c20: DecompressPointer r2
    //     0x894c20: add             x2, x2, HEAP, lsl #32
    // 0x894c24: ldur            x0, [fp, #-0x18]
    // 0x894c28: r1 = Null
    //     0x894c28: mov             x1, NULL
    // 0x894c2c: r8 = Parser<C1X2>
    //     0x894c2c: add             x8, PP, #0x31, lsl #12  ; [pp+0x31288] Type: Parser<C1X2>
    //     0x894c30: ldr             x8, [x8, #0x288]
    // 0x894c34: LoadField: r9 = r8->field_7
    //     0x894c34: ldur            x9, [x8, #7]
    // 0x894c38: r3 = Null
    //     0x894c38: add             x3, PP, #0x31, lsl #12  ; [pp+0x31290] Null
    //     0x894c3c: ldr             x3, [x3, #0x290]
    // 0x894c40: blr             x9
    // 0x894c44: ldur            x0, [fp, #-0x18]
    // 0x894c48: ldur            x1, [fp, #-8]
    // 0x894c4c: StoreField: r1->field_13 = r0
    //     0x894c4c: stur            w0, [x1, #0x13]
    //     0x894c50: ldurb           w16, [x1, #-1]
    //     0x894c54: ldurb           w17, [x0, #-1]
    //     0x894c58: and             x16, x17, x16, lsr #2
    //     0x894c5c: tst             x16, HEAP, lsr #32
    //     0x894c60: b.eq            #0x894c68
    //     0x894c64: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894c68: b               #0x894c70
    // 0x894c6c: ldur            x1, [fp, #-8]
    // 0x894c70: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x894c70: ldur            w0, [x1, #0x17]
    // 0x894c74: DecompressPointer r0
    //     0x894c74: add             x0, x0, HEAP, lsl #32
    // 0x894c78: r2 = LoadClassIdInstr(r0)
    //     0x894c78: ldur            x2, [x0, #-1]
    //     0x894c7c: ubfx            x2, x2, #0xc, #0x14
    // 0x894c80: ldur            x16, [fp, #-0x10]
    // 0x894c84: stp             x16, x0, [SP]
    // 0x894c88: mov             x0, x2
    // 0x894c8c: mov             lr, x0
    // 0x894c90: ldr             lr, [x21, lr, lsl #3]
    // 0x894c94: blr             lr
    // 0x894c98: tbnz            w0, #4, #0x894cf0
    // 0x894c9c: ldur            x3, [fp, #-8]
    // 0x894ca0: LoadField: r2 = r3->field_7
    //     0x894ca0: ldur            w2, [x3, #7]
    // 0x894ca4: DecompressPointer r2
    //     0x894ca4: add             x2, x2, HEAP, lsl #32
    // 0x894ca8: ldur            x0, [fp, #-0x18]
    // 0x894cac: r1 = Null
    //     0x894cac: mov             x1, NULL
    // 0x894cb0: r8 = Parser<C1X3>
    //     0x894cb0: add             x8, PP, #0x31, lsl #12  ; [pp+0x312a0] Type: Parser<C1X3>
    //     0x894cb4: ldr             x8, [x8, #0x2a0]
    // 0x894cb8: LoadField: r9 = r8->field_7
    //     0x894cb8: ldur            x9, [x8, #7]
    // 0x894cbc: r3 = Null
    //     0x894cbc: add             x3, PP, #0x31, lsl #12  ; [pp+0x312a8] Null
    //     0x894cc0: ldr             x3, [x3, #0x2a8]
    // 0x894cc4: blr             x9
    // 0x894cc8: ldur            x0, [fp, #-0x18]
    // 0x894ccc: ldur            x1, [fp, #-8]
    // 0x894cd0: ArrayStore: r1[0] = r0  ; List_4
    //     0x894cd0: stur            w0, [x1, #0x17]
    //     0x894cd4: ldurb           w16, [x1, #-1]
    //     0x894cd8: ldurb           w17, [x0, #-1]
    //     0x894cdc: and             x16, x17, x16, lsr #2
    //     0x894ce0: tst             x16, HEAP, lsr #32
    //     0x894ce4: b.eq            #0x894cec
    //     0x894ce8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894cec: b               #0x894cf4
    // 0x894cf0: ldur            x1, [fp, #-8]
    // 0x894cf4: LoadField: r0 = r1->field_1b
    //     0x894cf4: ldur            w0, [x1, #0x1b]
    // 0x894cf8: DecompressPointer r0
    //     0x894cf8: add             x0, x0, HEAP, lsl #32
    // 0x894cfc: r2 = LoadClassIdInstr(r0)
    //     0x894cfc: ldur            x2, [x0, #-1]
    //     0x894d00: ubfx            x2, x2, #0xc, #0x14
    // 0x894d04: ldur            x16, [fp, #-0x10]
    // 0x894d08: stp             x16, x0, [SP]
    // 0x894d0c: mov             x0, x2
    // 0x894d10: mov             lr, x0
    // 0x894d14: ldr             lr, [x21, lr, lsl #3]
    // 0x894d18: blr             lr
    // 0x894d1c: tbnz            w0, #4, #0x894d74
    // 0x894d20: ldur            x3, [fp, #-8]
    // 0x894d24: LoadField: r2 = r3->field_7
    //     0x894d24: ldur            w2, [x3, #7]
    // 0x894d28: DecompressPointer r2
    //     0x894d28: add             x2, x2, HEAP, lsl #32
    // 0x894d2c: ldur            x0, [fp, #-0x18]
    // 0x894d30: r1 = Null
    //     0x894d30: mov             x1, NULL
    // 0x894d34: r8 = Parser<C1X4>
    //     0x894d34: add             x8, PP, #0x31, lsl #12  ; [pp+0x312b8] Type: Parser<C1X4>
    //     0x894d38: ldr             x8, [x8, #0x2b8]
    // 0x894d3c: LoadField: r9 = r8->field_7
    //     0x894d3c: ldur            x9, [x8, #7]
    // 0x894d40: r3 = Null
    //     0x894d40: add             x3, PP, #0x31, lsl #12  ; [pp+0x312c0] Null
    //     0x894d44: ldr             x3, [x3, #0x2c0]
    // 0x894d48: blr             x9
    // 0x894d4c: ldur            x0, [fp, #-0x18]
    // 0x894d50: ldur            x1, [fp, #-8]
    // 0x894d54: StoreField: r1->field_1b = r0
    //     0x894d54: stur            w0, [x1, #0x1b]
    //     0x894d58: ldurb           w16, [x1, #-1]
    //     0x894d5c: ldurb           w17, [x0, #-1]
    //     0x894d60: and             x16, x17, x16, lsr #2
    //     0x894d64: tst             x16, HEAP, lsr #32
    //     0x894d68: b.eq            #0x894d70
    //     0x894d6c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894d70: b               #0x894d78
    // 0x894d74: ldur            x1, [fp, #-8]
    // 0x894d78: LoadField: r0 = r1->field_1f
    //     0x894d78: ldur            w0, [x1, #0x1f]
    // 0x894d7c: DecompressPointer r0
    //     0x894d7c: add             x0, x0, HEAP, lsl #32
    // 0x894d80: r2 = LoadClassIdInstr(r0)
    //     0x894d80: ldur            x2, [x0, #-1]
    //     0x894d84: ubfx            x2, x2, #0xc, #0x14
    // 0x894d88: ldur            x16, [fp, #-0x10]
    // 0x894d8c: stp             x16, x0, [SP]
    // 0x894d90: mov             x0, x2
    // 0x894d94: mov             lr, x0
    // 0x894d98: ldr             lr, [x21, lr, lsl #3]
    // 0x894d9c: blr             lr
    // 0x894da0: tbnz            w0, #4, #0x894df8
    // 0x894da4: ldur            x3, [fp, #-8]
    // 0x894da8: LoadField: r2 = r3->field_7
    //     0x894da8: ldur            w2, [x3, #7]
    // 0x894dac: DecompressPointer r2
    //     0x894dac: add             x2, x2, HEAP, lsl #32
    // 0x894db0: ldur            x0, [fp, #-0x18]
    // 0x894db4: r1 = Null
    //     0x894db4: mov             x1, NULL
    // 0x894db8: r8 = Parser<C1X5>
    //     0x894db8: add             x8, PP, #0x31, lsl #12  ; [pp+0x312d0] Type: Parser<C1X5>
    //     0x894dbc: ldr             x8, [x8, #0x2d0]
    // 0x894dc0: LoadField: r9 = r8->field_7
    //     0x894dc0: ldur            x9, [x8, #7]
    // 0x894dc4: r3 = Null
    //     0x894dc4: add             x3, PP, #0x31, lsl #12  ; [pp+0x312d8] Null
    //     0x894dc8: ldr             x3, [x3, #0x2d8]
    // 0x894dcc: blr             x9
    // 0x894dd0: ldur            x0, [fp, #-0x18]
    // 0x894dd4: ldur            x1, [fp, #-8]
    // 0x894dd8: StoreField: r1->field_1f = r0
    //     0x894dd8: stur            w0, [x1, #0x1f]
    //     0x894ddc: ldurb           w16, [x1, #-1]
    //     0x894de0: ldurb           w17, [x0, #-1]
    //     0x894de4: and             x16, x17, x16, lsr #2
    //     0x894de8: tst             x16, HEAP, lsr #32
    //     0x894dec: b.eq            #0x894df4
    //     0x894df0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894df4: b               #0x894dfc
    // 0x894df8: ldur            x1, [fp, #-8]
    // 0x894dfc: LoadField: r0 = r1->field_23
    //     0x894dfc: ldur            w0, [x1, #0x23]
    // 0x894e00: DecompressPointer r0
    //     0x894e00: add             x0, x0, HEAP, lsl #32
    // 0x894e04: r2 = LoadClassIdInstr(r0)
    //     0x894e04: ldur            x2, [x0, #-1]
    //     0x894e08: ubfx            x2, x2, #0xc, #0x14
    // 0x894e0c: ldur            x16, [fp, #-0x10]
    // 0x894e10: stp             x16, x0, [SP]
    // 0x894e14: mov             x0, x2
    // 0x894e18: mov             lr, x0
    // 0x894e1c: ldr             lr, [x21, lr, lsl #3]
    // 0x894e20: blr             lr
    // 0x894e24: tbnz            w0, #4, #0x894e7c
    // 0x894e28: ldur            x3, [fp, #-8]
    // 0x894e2c: LoadField: r2 = r3->field_7
    //     0x894e2c: ldur            w2, [x3, #7]
    // 0x894e30: DecompressPointer r2
    //     0x894e30: add             x2, x2, HEAP, lsl #32
    // 0x894e34: ldur            x0, [fp, #-0x18]
    // 0x894e38: r1 = Null
    //     0x894e38: mov             x1, NULL
    // 0x894e3c: r8 = Parser<C1X6>
    //     0x894e3c: add             x8, PP, #0x31, lsl #12  ; [pp+0x312e8] Type: Parser<C1X6>
    //     0x894e40: ldr             x8, [x8, #0x2e8]
    // 0x894e44: LoadField: r9 = r8->field_7
    //     0x894e44: ldur            x9, [x8, #7]
    // 0x894e48: r3 = Null
    //     0x894e48: add             x3, PP, #0x31, lsl #12  ; [pp+0x312f0] Null
    //     0x894e4c: ldr             x3, [x3, #0x2f0]
    // 0x894e50: blr             x9
    // 0x894e54: ldur            x0, [fp, #-0x18]
    // 0x894e58: ldur            x1, [fp, #-8]
    // 0x894e5c: StoreField: r1->field_23 = r0
    //     0x894e5c: stur            w0, [x1, #0x23]
    //     0x894e60: ldurb           w16, [x1, #-1]
    //     0x894e64: ldurb           w17, [x0, #-1]
    //     0x894e68: and             x16, x17, x16, lsr #2
    //     0x894e6c: tst             x16, HEAP, lsr #32
    //     0x894e70: b.eq            #0x894e78
    //     0x894e74: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894e78: b               #0x894e80
    // 0x894e7c: ldur            x1, [fp, #-8]
    // 0x894e80: LoadField: r0 = r1->field_27
    //     0x894e80: ldur            w0, [x1, #0x27]
    // 0x894e84: DecompressPointer r0
    //     0x894e84: add             x0, x0, HEAP, lsl #32
    // 0x894e88: r2 = LoadClassIdInstr(r0)
    //     0x894e88: ldur            x2, [x0, #-1]
    //     0x894e8c: ubfx            x2, x2, #0xc, #0x14
    // 0x894e90: ldur            x16, [fp, #-0x10]
    // 0x894e94: stp             x16, x0, [SP]
    // 0x894e98: mov             x0, x2
    // 0x894e9c: mov             lr, x0
    // 0x894ea0: ldr             lr, [x21, lr, lsl #3]
    // 0x894ea4: blr             lr
    // 0x894ea8: tbnz            w0, #4, #0x894efc
    // 0x894eac: ldur            x3, [fp, #-8]
    // 0x894eb0: LoadField: r2 = r3->field_7
    //     0x894eb0: ldur            w2, [x3, #7]
    // 0x894eb4: DecompressPointer r2
    //     0x894eb4: add             x2, x2, HEAP, lsl #32
    // 0x894eb8: ldur            x0, [fp, #-0x18]
    // 0x894ebc: r1 = Null
    //     0x894ebc: mov             x1, NULL
    // 0x894ec0: r8 = Parser<C1X7>
    //     0x894ec0: add             x8, PP, #0x31, lsl #12  ; [pp+0x31300] Type: Parser<C1X7>
    //     0x894ec4: ldr             x8, [x8, #0x300]
    // 0x894ec8: LoadField: r9 = r8->field_7
    //     0x894ec8: ldur            x9, [x8, #7]
    // 0x894ecc: r3 = Null
    //     0x894ecc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31308] Null
    //     0x894ed0: ldr             x3, [x3, #0x308]
    // 0x894ed4: blr             x9
    // 0x894ed8: ldur            x0, [fp, #-0x18]
    // 0x894edc: ldur            x1, [fp, #-8]
    // 0x894ee0: StoreField: r1->field_27 = r0
    //     0x894ee0: stur            w0, [x1, #0x27]
    //     0x894ee4: ldurb           w16, [x1, #-1]
    //     0x894ee8: ldurb           w17, [x0, #-1]
    //     0x894eec: and             x16, x17, x16, lsr #2
    //     0x894ef0: tst             x16, HEAP, lsr #32
    //     0x894ef4: b.eq            #0x894efc
    //     0x894ef8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x894efc: r0 = Null
    //     0x894efc: mov             x0, NULL
    // 0x894f00: LeaveFrame
    //     0x894f00: mov             SP, fp
    //     0x894f04: ldp             fp, lr, [SP], #0x10
    // 0x894f08: ret
    //     0x894f08: ret             
    // 0x894f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x894f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x894f10: b               #0x894ae8
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb03f0, size: 0x258
    // 0xeb03f0: EnterFrame
    //     0xeb03f0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb03f4: mov             fp, SP
    // 0xeb03f8: AllocStack(0x10)
    //     0xeb03f8: sub             SP, SP, #0x10
    // 0xeb03fc: SetupParameters(SequenceParser8<C1X0, C1X1, C1X2, C1X3, C1X4, C1X5, C1X6, C1X7> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */)
    //     0xeb03fc: mov             x5, x1
    //     0xeb0400: mov             x4, x2
    //     0xeb0404: stur            x1, [fp, #-8]
    //     0xeb0408: stur            x2, [fp, #-0x10]
    // 0xeb040c: CheckStackOverflow
    //     0xeb040c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0410: cmp             SP, x16
    //     0xeb0414: b.ls            #0xeb0640
    // 0xeb0418: LoadField: r1 = r5->field_b
    //     0xeb0418: ldur            w1, [x5, #0xb]
    // 0xeb041c: DecompressPointer r1
    //     0xeb041c: add             x1, x1, HEAP, lsl #32
    // 0xeb0420: r0 = LoadClassIdInstr(r1)
    //     0xeb0420: ldur            x0, [x1, #-1]
    //     0xeb0424: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0428: mov             x2, x4
    // 0xeb042c: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb042c: sub             lr, x0, #0xfce
    //     0xeb0430: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0434: blr             lr
    // 0xeb0438: r3 = LoadInt32Instr(r0)
    //     0xeb0438: sbfx            x3, x0, #1, #0x1f
    //     0xeb043c: tbz             w0, #0, #0xeb0444
    //     0xeb0440: ldur            x3, [x0, #7]
    // 0xeb0444: tbz             x3, #0x3f, #0xeb0458
    // 0xeb0448: r0 = -2
    //     0xeb0448: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb044c: LeaveFrame
    //     0xeb044c: mov             SP, fp
    //     0xeb0450: ldp             fp, lr, [SP], #0x10
    // 0xeb0454: ret
    //     0xeb0454: ret             
    // 0xeb0458: ldur            x4, [fp, #-8]
    // 0xeb045c: LoadField: r1 = r4->field_f
    //     0xeb045c: ldur            w1, [x4, #0xf]
    // 0xeb0460: DecompressPointer r1
    //     0xeb0460: add             x1, x1, HEAP, lsl #32
    // 0xeb0464: r0 = LoadClassIdInstr(r1)
    //     0xeb0464: ldur            x0, [x1, #-1]
    //     0xeb0468: ubfx            x0, x0, #0xc, #0x14
    // 0xeb046c: ldur            x2, [fp, #-0x10]
    // 0xeb0470: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb0470: sub             lr, x0, #0xfce
    //     0xeb0474: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0478: blr             lr
    // 0xeb047c: r3 = LoadInt32Instr(r0)
    //     0xeb047c: sbfx            x3, x0, #1, #0x1f
    //     0xeb0480: tbz             w0, #0, #0xeb0488
    //     0xeb0484: ldur            x3, [x0, #7]
    // 0xeb0488: tbz             x3, #0x3f, #0xeb049c
    // 0xeb048c: r0 = -2
    //     0xeb048c: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0490: LeaveFrame
    //     0xeb0490: mov             SP, fp
    //     0xeb0494: ldp             fp, lr, [SP], #0x10
    // 0xeb0498: ret
    //     0xeb0498: ret             
    // 0xeb049c: ldur            x4, [fp, #-8]
    // 0xeb04a0: LoadField: r1 = r4->field_13
    //     0xeb04a0: ldur            w1, [x4, #0x13]
    // 0xeb04a4: DecompressPointer r1
    //     0xeb04a4: add             x1, x1, HEAP, lsl #32
    // 0xeb04a8: r0 = LoadClassIdInstr(r1)
    //     0xeb04a8: ldur            x0, [x1, #-1]
    //     0xeb04ac: ubfx            x0, x0, #0xc, #0x14
    // 0xeb04b0: ldur            x2, [fp, #-0x10]
    // 0xeb04b4: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb04b4: sub             lr, x0, #0xfce
    //     0xeb04b8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb04bc: blr             lr
    // 0xeb04c0: r3 = LoadInt32Instr(r0)
    //     0xeb04c0: sbfx            x3, x0, #1, #0x1f
    //     0xeb04c4: tbz             w0, #0, #0xeb04cc
    //     0xeb04c8: ldur            x3, [x0, #7]
    // 0xeb04cc: tbz             x3, #0x3f, #0xeb04e0
    // 0xeb04d0: r0 = -2
    //     0xeb04d0: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb04d4: LeaveFrame
    //     0xeb04d4: mov             SP, fp
    //     0xeb04d8: ldp             fp, lr, [SP], #0x10
    // 0xeb04dc: ret
    //     0xeb04dc: ret             
    // 0xeb04e0: ldur            x4, [fp, #-8]
    // 0xeb04e4: ArrayLoad: r1 = r4[0]  ; List_4
    //     0xeb04e4: ldur            w1, [x4, #0x17]
    // 0xeb04e8: DecompressPointer r1
    //     0xeb04e8: add             x1, x1, HEAP, lsl #32
    // 0xeb04ec: r0 = LoadClassIdInstr(r1)
    //     0xeb04ec: ldur            x0, [x1, #-1]
    //     0xeb04f0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb04f4: ldur            x2, [fp, #-0x10]
    // 0xeb04f8: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb04f8: sub             lr, x0, #0xfce
    //     0xeb04fc: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0500: blr             lr
    // 0xeb0504: r3 = LoadInt32Instr(r0)
    //     0xeb0504: sbfx            x3, x0, #1, #0x1f
    //     0xeb0508: tbz             w0, #0, #0xeb0510
    //     0xeb050c: ldur            x3, [x0, #7]
    // 0xeb0510: tbz             x3, #0x3f, #0xeb0524
    // 0xeb0514: r0 = -2
    //     0xeb0514: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0518: LeaveFrame
    //     0xeb0518: mov             SP, fp
    //     0xeb051c: ldp             fp, lr, [SP], #0x10
    // 0xeb0520: ret
    //     0xeb0520: ret             
    // 0xeb0524: ldur            x4, [fp, #-8]
    // 0xeb0528: LoadField: r1 = r4->field_1b
    //     0xeb0528: ldur            w1, [x4, #0x1b]
    // 0xeb052c: DecompressPointer r1
    //     0xeb052c: add             x1, x1, HEAP, lsl #32
    // 0xeb0530: r0 = LoadClassIdInstr(r1)
    //     0xeb0530: ldur            x0, [x1, #-1]
    //     0xeb0534: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0538: ldur            x2, [fp, #-0x10]
    // 0xeb053c: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb053c: sub             lr, x0, #0xfce
    //     0xeb0540: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0544: blr             lr
    // 0xeb0548: r3 = LoadInt32Instr(r0)
    //     0xeb0548: sbfx            x3, x0, #1, #0x1f
    //     0xeb054c: tbz             w0, #0, #0xeb0554
    //     0xeb0550: ldur            x3, [x0, #7]
    // 0xeb0554: tbz             x3, #0x3f, #0xeb0568
    // 0xeb0558: r0 = -2
    //     0xeb0558: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb055c: LeaveFrame
    //     0xeb055c: mov             SP, fp
    //     0xeb0560: ldp             fp, lr, [SP], #0x10
    // 0xeb0564: ret
    //     0xeb0564: ret             
    // 0xeb0568: ldur            x4, [fp, #-8]
    // 0xeb056c: LoadField: r1 = r4->field_1f
    //     0xeb056c: ldur            w1, [x4, #0x1f]
    // 0xeb0570: DecompressPointer r1
    //     0xeb0570: add             x1, x1, HEAP, lsl #32
    // 0xeb0574: r0 = LoadClassIdInstr(r1)
    //     0xeb0574: ldur            x0, [x1, #-1]
    //     0xeb0578: ubfx            x0, x0, #0xc, #0x14
    // 0xeb057c: ldur            x2, [fp, #-0x10]
    // 0xeb0580: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb0580: sub             lr, x0, #0xfce
    //     0xeb0584: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0588: blr             lr
    // 0xeb058c: r3 = LoadInt32Instr(r0)
    //     0xeb058c: sbfx            x3, x0, #1, #0x1f
    //     0xeb0590: tbz             w0, #0, #0xeb0598
    //     0xeb0594: ldur            x3, [x0, #7]
    // 0xeb0598: tbz             x3, #0x3f, #0xeb05ac
    // 0xeb059c: r0 = -2
    //     0xeb059c: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb05a0: LeaveFrame
    //     0xeb05a0: mov             SP, fp
    //     0xeb05a4: ldp             fp, lr, [SP], #0x10
    // 0xeb05a8: ret
    //     0xeb05a8: ret             
    // 0xeb05ac: ldur            x4, [fp, #-8]
    // 0xeb05b0: LoadField: r1 = r4->field_23
    //     0xeb05b0: ldur            w1, [x4, #0x23]
    // 0xeb05b4: DecompressPointer r1
    //     0xeb05b4: add             x1, x1, HEAP, lsl #32
    // 0xeb05b8: r0 = LoadClassIdInstr(r1)
    //     0xeb05b8: ldur            x0, [x1, #-1]
    //     0xeb05bc: ubfx            x0, x0, #0xc, #0x14
    // 0xeb05c0: ldur            x2, [fp, #-0x10]
    // 0xeb05c4: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb05c4: sub             lr, x0, #0xfce
    //     0xeb05c8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb05cc: blr             lr
    // 0xeb05d0: r3 = LoadInt32Instr(r0)
    //     0xeb05d0: sbfx            x3, x0, #1, #0x1f
    //     0xeb05d4: tbz             w0, #0, #0xeb05dc
    //     0xeb05d8: ldur            x3, [x0, #7]
    // 0xeb05dc: tbz             x3, #0x3f, #0xeb05f0
    // 0xeb05e0: r0 = -2
    //     0xeb05e0: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb05e4: LeaveFrame
    //     0xeb05e4: mov             SP, fp
    //     0xeb05e8: ldp             fp, lr, [SP], #0x10
    // 0xeb05ec: ret
    //     0xeb05ec: ret             
    // 0xeb05f0: ldur            x0, [fp, #-8]
    // 0xeb05f4: LoadField: r1 = r0->field_27
    //     0xeb05f4: ldur            w1, [x0, #0x27]
    // 0xeb05f8: DecompressPointer r1
    //     0xeb05f8: add             x1, x1, HEAP, lsl #32
    // 0xeb05fc: r0 = LoadClassIdInstr(r1)
    //     0xeb05fc: ldur            x0, [x1, #-1]
    //     0xeb0600: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0604: ldur            x2, [fp, #-0x10]
    // 0xeb0608: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeb0608: sub             lr, x0, #0xfce
    //     0xeb060c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0610: blr             lr
    // 0xeb0614: r1 = LoadInt32Instr(r0)
    //     0xeb0614: sbfx            x1, x0, #1, #0x1f
    //     0xeb0618: tbz             w0, #0, #0xeb0620
    //     0xeb061c: ldur            x1, [x0, #7]
    // 0xeb0620: tbz             x1, #0x3f, #0xeb0634
    // 0xeb0624: r0 = -2
    //     0xeb0624: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0628: LeaveFrame
    //     0xeb0628: mov             SP, fp
    //     0xeb062c: ldp             fp, lr, [SP], #0x10
    // 0xeb0630: ret
    //     0xeb0630: ret             
    // 0xeb0634: LeaveFrame
    //     0xeb0634: mov             SP, fp
    //     0xeb0638: ldp             fp, lr, [SP], #0x10
    // 0xeb063c: ret
    //     0xeb063c: ret             
    // 0xeb0640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0640: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0644: b               #0xeb0418
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb28a8, size: 0x4ec
    // 0xeb28a8: EnterFrame
    //     0xeb28a8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb28ac: mov             fp, SP
    // 0xeb28b0: AllocStack(0x98)
    //     0xeb28b0: sub             SP, SP, #0x98
    // 0xeb28b4: SetupParameters(SequenceParser8<C1X0, C1X1, C1X2, C1X3, C1X4, C1X5, C1X6, C1X7> this /* r1 => r3, fp-0x8 */)
    //     0xeb28b4: mov             x3, x1
    //     0xeb28b8: stur            x1, [fp, #-8]
    // 0xeb28bc: CheckStackOverflow
    //     0xeb28bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb28c0: cmp             SP, x16
    //     0xeb28c4: b.ls            #0xeb2d8c
    // 0xeb28c8: LoadField: r1 = r3->field_b
    //     0xeb28c8: ldur            w1, [x3, #0xb]
    // 0xeb28cc: DecompressPointer r1
    //     0xeb28cc: add             x1, x1, HEAP, lsl #32
    // 0xeb28d0: r0 = LoadClassIdInstr(r1)
    //     0xeb28d0: ldur            x0, [x1, #-1]
    //     0xeb28d4: ubfx            x0, x0, #0xc, #0x14
    // 0xeb28d8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb28d8: sub             lr, x0, #1, lsl #12
    //     0xeb28dc: ldr             lr, [x21, lr, lsl #3]
    //     0xeb28e0: blr             lr
    // 0xeb28e4: mov             x3, x0
    // 0xeb28e8: stur            x3, [fp, #-0x18]
    // 0xeb28ec: r4 = LoadClassIdInstr(r3)
    //     0xeb28ec: ldur            x4, [x3, #-1]
    //     0xeb28f0: ubfx            x4, x4, #0xc, #0x14
    // 0xeb28f4: stur            x4, [fp, #-0x10]
    // 0xeb28f8: cmp             x4, #0x2f3
    // 0xeb28fc: b.ne            #0xeb2910
    // 0xeb2900: mov             x0, x3
    // 0xeb2904: LeaveFrame
    //     0xeb2904: mov             SP, fp
    //     0xeb2908: ldp             fp, lr, [SP], #0x10
    // 0xeb290c: ret
    //     0xeb290c: ret             
    // 0xeb2910: ldur            x5, [fp, #-8]
    // 0xeb2914: LoadField: r1 = r5->field_f
    //     0xeb2914: ldur            w1, [x5, #0xf]
    // 0xeb2918: DecompressPointer r1
    //     0xeb2918: add             x1, x1, HEAP, lsl #32
    // 0xeb291c: r0 = LoadClassIdInstr(r1)
    //     0xeb291c: ldur            x0, [x1, #-1]
    //     0xeb2920: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2924: mov             x2, x3
    // 0xeb2928: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2928: sub             lr, x0, #1, lsl #12
    //     0xeb292c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2930: blr             lr
    // 0xeb2934: mov             x3, x0
    // 0xeb2938: stur            x3, [fp, #-0x28]
    // 0xeb293c: r4 = LoadClassIdInstr(r3)
    //     0xeb293c: ldur            x4, [x3, #-1]
    //     0xeb2940: ubfx            x4, x4, #0xc, #0x14
    // 0xeb2944: stur            x4, [fp, #-0x20]
    // 0xeb2948: cmp             x4, #0x2f3
    // 0xeb294c: b.ne            #0xeb2960
    // 0xeb2950: mov             x0, x3
    // 0xeb2954: LeaveFrame
    //     0xeb2954: mov             SP, fp
    //     0xeb2958: ldp             fp, lr, [SP], #0x10
    // 0xeb295c: ret
    //     0xeb295c: ret             
    // 0xeb2960: ldur            x5, [fp, #-8]
    // 0xeb2964: LoadField: r1 = r5->field_13
    //     0xeb2964: ldur            w1, [x5, #0x13]
    // 0xeb2968: DecompressPointer r1
    //     0xeb2968: add             x1, x1, HEAP, lsl #32
    // 0xeb296c: r0 = LoadClassIdInstr(r1)
    //     0xeb296c: ldur            x0, [x1, #-1]
    //     0xeb2970: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2974: mov             x2, x3
    // 0xeb2978: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2978: sub             lr, x0, #1, lsl #12
    //     0xeb297c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2980: blr             lr
    // 0xeb2984: mov             x3, x0
    // 0xeb2988: stur            x3, [fp, #-0x38]
    // 0xeb298c: r4 = LoadClassIdInstr(r3)
    //     0xeb298c: ldur            x4, [x3, #-1]
    //     0xeb2990: ubfx            x4, x4, #0xc, #0x14
    // 0xeb2994: stur            x4, [fp, #-0x30]
    // 0xeb2998: cmp             x4, #0x2f3
    // 0xeb299c: b.ne            #0xeb29b0
    // 0xeb29a0: mov             x0, x3
    // 0xeb29a4: LeaveFrame
    //     0xeb29a4: mov             SP, fp
    //     0xeb29a8: ldp             fp, lr, [SP], #0x10
    // 0xeb29ac: ret
    //     0xeb29ac: ret             
    // 0xeb29b0: ldur            x5, [fp, #-8]
    // 0xeb29b4: ArrayLoad: r1 = r5[0]  ; List_4
    //     0xeb29b4: ldur            w1, [x5, #0x17]
    // 0xeb29b8: DecompressPointer r1
    //     0xeb29b8: add             x1, x1, HEAP, lsl #32
    // 0xeb29bc: r0 = LoadClassIdInstr(r1)
    //     0xeb29bc: ldur            x0, [x1, #-1]
    //     0xeb29c0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb29c4: mov             x2, x3
    // 0xeb29c8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb29c8: sub             lr, x0, #1, lsl #12
    //     0xeb29cc: ldr             lr, [x21, lr, lsl #3]
    //     0xeb29d0: blr             lr
    // 0xeb29d4: mov             x3, x0
    // 0xeb29d8: stur            x3, [fp, #-0x48]
    // 0xeb29dc: r4 = LoadClassIdInstr(r3)
    //     0xeb29dc: ldur            x4, [x3, #-1]
    //     0xeb29e0: ubfx            x4, x4, #0xc, #0x14
    // 0xeb29e4: stur            x4, [fp, #-0x40]
    // 0xeb29e8: cmp             x4, #0x2f3
    // 0xeb29ec: b.ne            #0xeb2a00
    // 0xeb29f0: mov             x0, x3
    // 0xeb29f4: LeaveFrame
    //     0xeb29f4: mov             SP, fp
    //     0xeb29f8: ldp             fp, lr, [SP], #0x10
    // 0xeb29fc: ret
    //     0xeb29fc: ret             
    // 0xeb2a00: ldur            x5, [fp, #-8]
    // 0xeb2a04: LoadField: r1 = r5->field_1b
    //     0xeb2a04: ldur            w1, [x5, #0x1b]
    // 0xeb2a08: DecompressPointer r1
    //     0xeb2a08: add             x1, x1, HEAP, lsl #32
    // 0xeb2a0c: r0 = LoadClassIdInstr(r1)
    //     0xeb2a0c: ldur            x0, [x1, #-1]
    //     0xeb2a10: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2a14: mov             x2, x3
    // 0xeb2a18: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2a18: sub             lr, x0, #1, lsl #12
    //     0xeb2a1c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2a20: blr             lr
    // 0xeb2a24: mov             x3, x0
    // 0xeb2a28: stur            x3, [fp, #-0x58]
    // 0xeb2a2c: r4 = LoadClassIdInstr(r3)
    //     0xeb2a2c: ldur            x4, [x3, #-1]
    //     0xeb2a30: ubfx            x4, x4, #0xc, #0x14
    // 0xeb2a34: stur            x4, [fp, #-0x50]
    // 0xeb2a38: cmp             x4, #0x2f3
    // 0xeb2a3c: b.ne            #0xeb2a50
    // 0xeb2a40: mov             x0, x3
    // 0xeb2a44: LeaveFrame
    //     0xeb2a44: mov             SP, fp
    //     0xeb2a48: ldp             fp, lr, [SP], #0x10
    // 0xeb2a4c: ret
    //     0xeb2a4c: ret             
    // 0xeb2a50: ldur            x5, [fp, #-8]
    // 0xeb2a54: LoadField: r1 = r5->field_1f
    //     0xeb2a54: ldur            w1, [x5, #0x1f]
    // 0xeb2a58: DecompressPointer r1
    //     0xeb2a58: add             x1, x1, HEAP, lsl #32
    // 0xeb2a5c: r0 = LoadClassIdInstr(r1)
    //     0xeb2a5c: ldur            x0, [x1, #-1]
    //     0xeb2a60: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2a64: mov             x2, x3
    // 0xeb2a68: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2a68: sub             lr, x0, #1, lsl #12
    //     0xeb2a6c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2a70: blr             lr
    // 0xeb2a74: mov             x3, x0
    // 0xeb2a78: stur            x3, [fp, #-0x68]
    // 0xeb2a7c: r4 = LoadClassIdInstr(r3)
    //     0xeb2a7c: ldur            x4, [x3, #-1]
    //     0xeb2a80: ubfx            x4, x4, #0xc, #0x14
    // 0xeb2a84: stur            x4, [fp, #-0x60]
    // 0xeb2a88: cmp             x4, #0x2f3
    // 0xeb2a8c: b.ne            #0xeb2aa0
    // 0xeb2a90: mov             x0, x3
    // 0xeb2a94: LeaveFrame
    //     0xeb2a94: mov             SP, fp
    //     0xeb2a98: ldp             fp, lr, [SP], #0x10
    // 0xeb2a9c: ret
    //     0xeb2a9c: ret             
    // 0xeb2aa0: ldur            x5, [fp, #-8]
    // 0xeb2aa4: LoadField: r1 = r5->field_23
    //     0xeb2aa4: ldur            w1, [x5, #0x23]
    // 0xeb2aa8: DecompressPointer r1
    //     0xeb2aa8: add             x1, x1, HEAP, lsl #32
    // 0xeb2aac: r0 = LoadClassIdInstr(r1)
    //     0xeb2aac: ldur            x0, [x1, #-1]
    //     0xeb2ab0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2ab4: mov             x2, x3
    // 0xeb2ab8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2ab8: sub             lr, x0, #1, lsl #12
    //     0xeb2abc: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2ac0: blr             lr
    // 0xeb2ac4: mov             x3, x0
    // 0xeb2ac8: stur            x3, [fp, #-0x78]
    // 0xeb2acc: r4 = LoadClassIdInstr(r3)
    //     0xeb2acc: ldur            x4, [x3, #-1]
    //     0xeb2ad0: ubfx            x4, x4, #0xc, #0x14
    // 0xeb2ad4: stur            x4, [fp, #-0x70]
    // 0xeb2ad8: cmp             x4, #0x2f3
    // 0xeb2adc: b.ne            #0xeb2af0
    // 0xeb2ae0: mov             x0, x3
    // 0xeb2ae4: LeaveFrame
    //     0xeb2ae4: mov             SP, fp
    //     0xeb2ae8: ldp             fp, lr, [SP], #0x10
    // 0xeb2aec: ret
    //     0xeb2aec: ret             
    // 0xeb2af0: ldur            x5, [fp, #-8]
    // 0xeb2af4: LoadField: r1 = r5->field_27
    //     0xeb2af4: ldur            w1, [x5, #0x27]
    // 0xeb2af8: DecompressPointer r1
    //     0xeb2af8: add             x1, x1, HEAP, lsl #32
    // 0xeb2afc: r0 = LoadClassIdInstr(r1)
    //     0xeb2afc: ldur            x0, [x1, #-1]
    //     0xeb2b00: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2b04: mov             x2, x3
    // 0xeb2b08: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb2b08: sub             lr, x0, #1, lsl #12
    //     0xeb2b0c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb2b10: blr             lr
    // 0xeb2b14: stur            x0, [fp, #-0x90]
    // 0xeb2b18: r1 = LoadClassIdInstr(r0)
    //     0xeb2b18: ldur            x1, [x0, #-1]
    //     0xeb2b1c: ubfx            x1, x1, #0xc, #0x14
    // 0xeb2b20: stur            x1, [fp, #-0x88]
    // 0xeb2b24: cmp             x1, #0x2f3
    // 0xeb2b28: b.ne            #0xeb2b38
    // 0xeb2b2c: LeaveFrame
    //     0xeb2b2c: mov             SP, fp
    //     0xeb2b30: ldp             fp, lr, [SP], #0x10
    // 0xeb2b34: ret
    //     0xeb2b34: ret             
    // 0xeb2b38: ldur            x2, [fp, #-8]
    // 0xeb2b3c: ldur            x3, [fp, #-0x10]
    // 0xeb2b40: LoadField: r4 = r2->field_7
    //     0xeb2b40: ldur            w4, [x2, #7]
    // 0xeb2b44: DecompressPointer r4
    //     0xeb2b44: add             x4, x4, HEAP, lsl #32
    // 0xeb2b48: stur            x4, [fp, #-0x80]
    // 0xeb2b4c: cmp             x3, #0x2f3
    // 0xeb2b50: b.eq            #0xeb2c8c
    // 0xeb2b54: ldur            x3, [fp, #-0x18]
    // 0xeb2b58: ldur            x2, [fp, #-0x20]
    // 0xeb2b5c: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xeb2b5c: ldur            w5, [x3, #0x17]
    // 0xeb2b60: DecompressPointer r5
    //     0xeb2b60: add             x5, x5, HEAP, lsl #32
    // 0xeb2b64: stur            x5, [fp, #-8]
    // 0xeb2b68: r1 = 16
    //     0xeb2b68: movz            x1, #0x10
    // 0xeb2b6c: r0 = AllocateRecord()
    //     0xeb2b6c: bl              #0xec1080  ; AllocateRecordStub
    // 0xeb2b70: mov             x2, x0
    // 0xeb2b74: ldur            x0, [fp, #-8]
    // 0xeb2b78: stur            x2, [fp, #-0x98]
    // 0xeb2b7c: StoreField: r2->field_f = r0
    //     0xeb2b7c: stur            w0, [x2, #0xf]
    // 0xeb2b80: ldur            x0, [fp, #-0x20]
    // 0xeb2b84: cmp             x0, #0x2f3
    // 0xeb2b88: b.eq            #0xeb2cac
    // 0xeb2b8c: ldur            x1, [fp, #-0x28]
    // 0xeb2b90: ldur            x0, [fp, #-0x30]
    // 0xeb2b94: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb2b94: ldur            w3, [x1, #0x17]
    // 0xeb2b98: DecompressPointer r3
    //     0xeb2b98: add             x3, x3, HEAP, lsl #32
    // 0xeb2b9c: StoreField: r2->field_13 = r3
    //     0xeb2b9c: stur            w3, [x2, #0x13]
    // 0xeb2ba0: cmp             x0, #0x2f3
    // 0xeb2ba4: b.eq            #0xeb2ccc
    // 0xeb2ba8: ldur            x1, [fp, #-0x38]
    // 0xeb2bac: ldur            x0, [fp, #-0x40]
    // 0xeb2bb0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb2bb0: ldur            w3, [x1, #0x17]
    // 0xeb2bb4: DecompressPointer r3
    //     0xeb2bb4: add             x3, x3, HEAP, lsl #32
    // 0xeb2bb8: ArrayStore: r2[0] = r3  ; List_4
    //     0xeb2bb8: stur            w3, [x2, #0x17]
    // 0xeb2bbc: cmp             x0, #0x2f3
    // 0xeb2bc0: b.eq            #0xeb2cec
    // 0xeb2bc4: ldur            x1, [fp, #-0x48]
    // 0xeb2bc8: ldur            x0, [fp, #-0x50]
    // 0xeb2bcc: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb2bcc: ldur            w3, [x1, #0x17]
    // 0xeb2bd0: DecompressPointer r3
    //     0xeb2bd0: add             x3, x3, HEAP, lsl #32
    // 0xeb2bd4: StoreField: r2->field_1b = r3
    //     0xeb2bd4: stur            w3, [x2, #0x1b]
    // 0xeb2bd8: cmp             x0, #0x2f3
    // 0xeb2bdc: b.eq            #0xeb2d0c
    // 0xeb2be0: ldur            x1, [fp, #-0x58]
    // 0xeb2be4: ldur            x0, [fp, #-0x60]
    // 0xeb2be8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb2be8: ldur            w3, [x1, #0x17]
    // 0xeb2bec: DecompressPointer r3
    //     0xeb2bec: add             x3, x3, HEAP, lsl #32
    // 0xeb2bf0: StoreField: r2->field_1f = r3
    //     0xeb2bf0: stur            w3, [x2, #0x1f]
    // 0xeb2bf4: cmp             x0, #0x2f3
    // 0xeb2bf8: b.eq            #0xeb2d2c
    // 0xeb2bfc: ldur            x1, [fp, #-0x68]
    // 0xeb2c00: ldur            x0, [fp, #-0x70]
    // 0xeb2c04: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb2c04: ldur            w3, [x1, #0x17]
    // 0xeb2c08: DecompressPointer r3
    //     0xeb2c08: add             x3, x3, HEAP, lsl #32
    // 0xeb2c0c: StoreField: r2->field_23 = r3
    //     0xeb2c0c: stur            w3, [x2, #0x23]
    // 0xeb2c10: cmp             x0, #0x2f3
    // 0xeb2c14: b.eq            #0xeb2d4c
    // 0xeb2c18: ldur            x1, [fp, #-0x78]
    // 0xeb2c1c: ldur            x0, [fp, #-0x88]
    // 0xeb2c20: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xeb2c20: ldur            w3, [x1, #0x17]
    // 0xeb2c24: DecompressPointer r3
    //     0xeb2c24: add             x3, x3, HEAP, lsl #32
    // 0xeb2c28: StoreField: r2->field_27 = r3
    //     0xeb2c28: stur            w3, [x2, #0x27]
    // 0xeb2c2c: cmp             x0, #0x2f3
    // 0xeb2c30: b.eq            #0xeb2d6c
    // 0xeb2c34: ldur            x0, [fp, #-0x90]
    // 0xeb2c38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb2c38: ldur            w1, [x0, #0x17]
    // 0xeb2c3c: DecompressPointer r1
    //     0xeb2c3c: add             x1, x1, HEAP, lsl #32
    // 0xeb2c40: StoreField: r2->field_2b = r1
    //     0xeb2c40: stur            w1, [x2, #0x2b]
    // 0xeb2c44: LoadField: r3 = r0->field_7
    //     0xeb2c44: ldur            w3, [x0, #7]
    // 0xeb2c48: DecompressPointer r3
    //     0xeb2c48: add             x3, x3, HEAP, lsl #32
    // 0xeb2c4c: stur            x3, [fp, #-8]
    // 0xeb2c50: LoadField: r4 = r0->field_b
    //     0xeb2c50: ldur            x4, [x0, #0xb]
    // 0xeb2c54: ldur            x1, [fp, #-0x80]
    // 0xeb2c58: stur            x4, [fp, #-0x10]
    // 0xeb2c5c: r0 = Success()
    //     0xeb2c5c: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb2c60: mov             x1, x0
    // 0xeb2c64: ldur            x0, [fp, #-0x98]
    // 0xeb2c68: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb2c68: stur            w0, [x1, #0x17]
    // 0xeb2c6c: ldur            x0, [fp, #-8]
    // 0xeb2c70: StoreField: r1->field_7 = r0
    //     0xeb2c70: stur            w0, [x1, #7]
    // 0xeb2c74: ldur            x0, [fp, #-0x10]
    // 0xeb2c78: StoreField: r1->field_b = r0
    //     0xeb2c78: stur            x0, [x1, #0xb]
    // 0xeb2c7c: mov             x0, x1
    // 0xeb2c80: LeaveFrame
    //     0xeb2c80: mov             SP, fp
    //     0xeb2c84: ldp             fp, lr, [SP], #0x10
    // 0xeb2c88: ret
    //     0xeb2c88: ret             
    // 0xeb2c8c: ldur            x3, [fp, #-0x18]
    // 0xeb2c90: r0 = ParserException()
    //     0xeb2c90: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2c94: mov             x1, x0
    // 0xeb2c98: ldur            x0, [fp, #-0x18]
    // 0xeb2c9c: StoreField: r1->field_7 = r0
    //     0xeb2c9c: stur            w0, [x1, #7]
    // 0xeb2ca0: mov             x0, x1
    // 0xeb2ca4: r0 = Throw()
    //     0xeb2ca4: bl              #0xec04b8  ; ThrowStub
    // 0xeb2ca8: brk             #0
    // 0xeb2cac: ldur            x1, [fp, #-0x28]
    // 0xeb2cb0: r0 = ParserException()
    //     0xeb2cb0: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2cb4: mov             x1, x0
    // 0xeb2cb8: ldur            x0, [fp, #-0x28]
    // 0xeb2cbc: StoreField: r1->field_7 = r0
    //     0xeb2cbc: stur            w0, [x1, #7]
    // 0xeb2cc0: mov             x0, x1
    // 0xeb2cc4: r0 = Throw()
    //     0xeb2cc4: bl              #0xec04b8  ; ThrowStub
    // 0xeb2cc8: brk             #0
    // 0xeb2ccc: ldur            x1, [fp, #-0x38]
    // 0xeb2cd0: r0 = ParserException()
    //     0xeb2cd0: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2cd4: mov             x1, x0
    // 0xeb2cd8: ldur            x0, [fp, #-0x38]
    // 0xeb2cdc: StoreField: r1->field_7 = r0
    //     0xeb2cdc: stur            w0, [x1, #7]
    // 0xeb2ce0: mov             x0, x1
    // 0xeb2ce4: r0 = Throw()
    //     0xeb2ce4: bl              #0xec04b8  ; ThrowStub
    // 0xeb2ce8: brk             #0
    // 0xeb2cec: ldur            x1, [fp, #-0x48]
    // 0xeb2cf0: r0 = ParserException()
    //     0xeb2cf0: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2cf4: mov             x1, x0
    // 0xeb2cf8: ldur            x0, [fp, #-0x48]
    // 0xeb2cfc: StoreField: r1->field_7 = r0
    //     0xeb2cfc: stur            w0, [x1, #7]
    // 0xeb2d00: mov             x0, x1
    // 0xeb2d04: r0 = Throw()
    //     0xeb2d04: bl              #0xec04b8  ; ThrowStub
    // 0xeb2d08: brk             #0
    // 0xeb2d0c: ldur            x1, [fp, #-0x58]
    // 0xeb2d10: r0 = ParserException()
    //     0xeb2d10: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2d14: mov             x1, x0
    // 0xeb2d18: ldur            x0, [fp, #-0x58]
    // 0xeb2d1c: StoreField: r1->field_7 = r0
    //     0xeb2d1c: stur            w0, [x1, #7]
    // 0xeb2d20: mov             x0, x1
    // 0xeb2d24: r0 = Throw()
    //     0xeb2d24: bl              #0xec04b8  ; ThrowStub
    // 0xeb2d28: brk             #0
    // 0xeb2d2c: ldur            x1, [fp, #-0x68]
    // 0xeb2d30: r0 = ParserException()
    //     0xeb2d30: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2d34: mov             x1, x0
    // 0xeb2d38: ldur            x0, [fp, #-0x68]
    // 0xeb2d3c: StoreField: r1->field_7 = r0
    //     0xeb2d3c: stur            w0, [x1, #7]
    // 0xeb2d40: mov             x0, x1
    // 0xeb2d44: r0 = Throw()
    //     0xeb2d44: bl              #0xec04b8  ; ThrowStub
    // 0xeb2d48: brk             #0
    // 0xeb2d4c: ldur            x1, [fp, #-0x78]
    // 0xeb2d50: r0 = ParserException()
    //     0xeb2d50: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2d54: mov             x1, x0
    // 0xeb2d58: ldur            x0, [fp, #-0x78]
    // 0xeb2d5c: StoreField: r1->field_7 = r0
    //     0xeb2d5c: stur            w0, [x1, #7]
    // 0xeb2d60: mov             x0, x1
    // 0xeb2d64: r0 = Throw()
    //     0xeb2d64: bl              #0xec04b8  ; ThrowStub
    // 0xeb2d68: brk             #0
    // 0xeb2d6c: ldur            x0, [fp, #-0x90]
    // 0xeb2d70: r0 = ParserException()
    //     0xeb2d70: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb2d74: mov             x1, x0
    // 0xeb2d78: ldur            x0, [fp, #-0x90]
    // 0xeb2d7c: StoreField: r1->field_7 = r0
    //     0xeb2d7c: stur            w0, [x1, #7]
    // 0xeb2d80: mov             x0, x1
    // 0xeb2d84: r0 = Throw()
    //     0xeb2d84: bl              #0xec04b8  ; ThrowStub
    // 0xeb2d88: brk             #0
    // 0xeb2d8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb2d8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb2d90: b               #0xeb28c8
  }
}
