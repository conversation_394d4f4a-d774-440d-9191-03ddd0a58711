// lib: , url: package:petitparser/src/parser/combinator/list.dart

// class id: 1050902, size: 0x8
class :: {
}

// class id: 739, size: 0x10, field offset: 0xc
abstract class ListParser<C1X0, C1X1> extends Parser<C1X0> {

  _ ListParser(/* No info */) {
    // ** addr: 0x88a63c, size: 0x8c
    // 0x88a63c: EnterFrame
    //     0x88a63c: stp             fp, lr, [SP, #-0x10]!
    //     0x88a640: mov             fp, SP
    // 0x88a644: AllocStack(0x10)
    //     0x88a644: sub             SP, SP, #0x10
    // 0x88a648: SetupParameters(ListParser<C1X0, C1X1> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x88a648: mov             x4, x1
    //     0x88a64c: mov             x0, x2
    //     0x88a650: stur            x1, [fp, #-8]
    //     0x88a654: stur            x2, [fp, #-0x10]
    // 0x88a658: CheckStackOverflow
    //     0x88a658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88a65c: cmp             SP, x16
    //     0x88a660: b.ls            #0x88a6c0
    // 0x88a664: LoadField: r2 = r4->field_7
    //     0x88a664: ldur            w2, [x4, #7]
    // 0x88a668: DecompressPointer r2
    //     0x88a668: add             x2, x2, HEAP, lsl #32
    // 0x88a66c: r1 = Null
    //     0x88a66c: mov             x1, NULL
    // 0x88a670: r3 = <Parser<C1X0>>
    //     0x88a670: add             x3, PP, #0x26, lsl #12  ; [pp+0x26be0] TypeArguments: <Parser<C1X0>>
    //     0x88a674: ldr             x3, [x3, #0xbe0]
    // 0x88a678: r30 = InstantiateTypeArgumentsStub
    //     0x88a678: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88a67c: LoadField: r30 = r30->field_7
    //     0x88a67c: ldur            lr, [lr, #7]
    // 0x88a680: blr             lr
    // 0x88a684: mov             x1, x0
    // 0x88a688: ldur            x2, [fp, #-0x10]
    // 0x88a68c: r0 = _List.of()
    //     0x88a68c: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x88a690: ldur            x1, [fp, #-8]
    // 0x88a694: StoreField: r1->field_b = r0
    //     0x88a694: stur            w0, [x1, #0xb]
    //     0x88a698: ldurb           w16, [x1, #-1]
    //     0x88a69c: ldurb           w17, [x0, #-1]
    //     0x88a6a0: and             x16, x17, x16, lsr #2
    //     0x88a6a4: tst             x16, HEAP, lsr #32
    //     0x88a6a8: b.eq            #0x88a6b0
    //     0x88a6ac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x88a6b0: r0 = Null
    //     0x88a6b0: mov             x0, NULL
    // 0x88a6b4: LeaveFrame
    //     0x88a6b4: mov             SP, fp
    //     0x88a6b8: ldp             fp, lr, [SP], #0x10
    // 0x88a6bc: ret
    //     0x88a6bc: ret             
    // 0x88a6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88a6c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88a6c4: b               #0x88a664
  }
  _ replace(/* No info */) {
    // ** addr: 0x893e7c, size: 0x174
    // 0x893e7c: EnterFrame
    //     0x893e7c: stp             fp, lr, [SP, #-0x10]!
    //     0x893e80: mov             fp, SP
    // 0x893e84: AllocStack(0x48)
    //     0x893e84: sub             SP, SP, #0x48
    // 0x893e88: SetupParameters(ListParser<C1X0, C1X1> this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x30 */, dynamic _ /* r3 => r1, fp-0x38 */)
    //     0x893e88: mov             x0, x1
    //     0x893e8c: mov             x1, x3
    //     0x893e90: stur            x2, [fp, #-0x30]
    //     0x893e94: stur            x3, [fp, #-0x38]
    // 0x893e98: CheckStackOverflow
    //     0x893e98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x893e9c: cmp             SP, x16
    //     0x893ea0: b.ls            #0x893fe0
    // 0x893ea4: LoadField: r3 = r0->field_b
    //     0x893ea4: ldur            w3, [x0, #0xb]
    // 0x893ea8: DecompressPointer r3
    //     0x893ea8: add             x3, x3, HEAP, lsl #32
    // 0x893eac: stur            x3, [fp, #-0x28]
    // 0x893eb0: LoadField: r4 = r3->field_b
    //     0x893eb0: ldur            w4, [x3, #0xb]
    // 0x893eb4: r5 = LoadInt32Instr(r4)
    //     0x893eb4: sbfx            x5, x4, #1, #0x1f
    // 0x893eb8: stur            x5, [fp, #-0x20]
    // 0x893ebc: LoadField: r4 = r0->field_7
    //     0x893ebc: ldur            w4, [x0, #7]
    // 0x893ec0: DecompressPointer r4
    //     0x893ec0: add             x4, x4, HEAP, lsl #32
    // 0x893ec4: stur            x4, [fp, #-0x18]
    // 0x893ec8: LoadField: r6 = r3->field_7
    //     0x893ec8: ldur            w6, [x3, #7]
    // 0x893ecc: DecompressPointer r6
    //     0x893ecc: add             x6, x6, HEAP, lsl #32
    // 0x893ed0: stur            x6, [fp, #-0x10]
    // 0x893ed4: r7 = 0
    //     0x893ed4: movz            x7, #0
    // 0x893ed8: stur            x7, [fp, #-8]
    // 0x893edc: CheckStackOverflow
    //     0x893edc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x893ee0: cmp             SP, x16
    //     0x893ee4: b.ls            #0x893fe8
    // 0x893ee8: cmp             x7, x5
    // 0x893eec: b.ge            #0x893fd0
    // 0x893ef0: ArrayLoad: r0 = r3[r7]  ; Unknown_4
    //     0x893ef0: add             x16, x3, x7, lsl #2
    //     0x893ef4: ldur            w0, [x16, #0xf]
    // 0x893ef8: DecompressPointer r0
    //     0x893ef8: add             x0, x0, HEAP, lsl #32
    // 0x893efc: r8 = LoadClassIdInstr(r0)
    //     0x893efc: ldur            x8, [x0, #-1]
    //     0x893f00: ubfx            x8, x8, #0xc, #0x14
    // 0x893f04: stp             x2, x0, [SP]
    // 0x893f08: mov             x0, x8
    // 0x893f0c: mov             lr, x0
    // 0x893f10: ldr             lr, [x21, lr, lsl #3]
    // 0x893f14: blr             lr
    // 0x893f18: tbnz            w0, #4, #0x893fac
    // 0x893f1c: ldur            x3, [fp, #-8]
    // 0x893f20: ldur            x0, [fp, #-0x38]
    // 0x893f24: ldur            x2, [fp, #-0x18]
    // 0x893f28: r1 = Null
    //     0x893f28: mov             x1, NULL
    // 0x893f2c: r8 = Parser<C1X0>
    //     0x893f2c: add             x8, PP, #0x31, lsl #12  ; [pp+0x31258] Type: Parser<C1X0>
    //     0x893f30: ldr             x8, [x8, #0x258]
    // 0x893f34: LoadField: r9 = r8->field_7
    //     0x893f34: ldur            x9, [x8, #7]
    // 0x893f38: r3 = Null
    //     0x893f38: add             x3, PP, #0x31, lsl #12  ; [pp+0x313f8] Null
    //     0x893f3c: ldr             x3, [x3, #0x3f8]
    // 0x893f40: blr             x9
    // 0x893f44: ldur            x0, [fp, #-0x38]
    // 0x893f48: ldur            x2, [fp, #-0x10]
    // 0x893f4c: r1 = Null
    //     0x893f4c: mov             x1, NULL
    // 0x893f50: cmp             w2, NULL
    // 0x893f54: b.eq            #0x893f74
    // 0x893f58: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x893f58: ldur            w4, [x2, #0x17]
    // 0x893f5c: DecompressPointer r4
    //     0x893f5c: add             x4, x4, HEAP, lsl #32
    // 0x893f60: r8 = X0
    //     0x893f60: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x893f64: LoadField: r9 = r4->field_7
    //     0x893f64: ldur            x9, [x4, #7]
    // 0x893f68: r3 = Null
    //     0x893f68: add             x3, PP, #0x31, lsl #12  ; [pp+0x31408] Null
    //     0x893f6c: ldr             x3, [x3, #0x408]
    // 0x893f70: blr             x9
    // 0x893f74: ldur            x1, [fp, #-0x28]
    // 0x893f78: ldur            x0, [fp, #-0x38]
    // 0x893f7c: ldur            x2, [fp, #-8]
    // 0x893f80: ArrayStore: r1[r2] = r0  ; List_4
    //     0x893f80: add             x25, x1, x2, lsl #2
    //     0x893f84: add             x25, x25, #0xf
    //     0x893f88: str             w0, [x25]
    //     0x893f8c: tbz             w0, #0, #0x893fa8
    //     0x893f90: ldurb           w16, [x1, #-1]
    //     0x893f94: ldurb           w17, [x0, #-1]
    //     0x893f98: and             x16, x17, x16, lsr #2
    //     0x893f9c: tst             x16, HEAP, lsr #32
    //     0x893fa0: b.eq            #0x893fa8
    //     0x893fa4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x893fa8: b               #0x893fb0
    // 0x893fac: ldur            x2, [fp, #-8]
    // 0x893fb0: add             x7, x2, #1
    // 0x893fb4: ldur            x2, [fp, #-0x30]
    // 0x893fb8: ldur            x1, [fp, #-0x38]
    // 0x893fbc: ldur            x3, [fp, #-0x28]
    // 0x893fc0: ldur            x4, [fp, #-0x18]
    // 0x893fc4: ldur            x6, [fp, #-0x10]
    // 0x893fc8: ldur            x5, [fp, #-0x20]
    // 0x893fcc: b               #0x893ed8
    // 0x893fd0: r0 = Null
    //     0x893fd0: mov             x0, NULL
    // 0x893fd4: LeaveFrame
    //     0x893fd4: mov             SP, fp
    //     0x893fd8: ldp             fp, lr, [SP], #0x10
    // 0x893fdc: ret
    //     0x893fdc: ret             
    // 0x893fe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x893fe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x893fe4: b               #0x893ea4
    // 0x893fe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x893fe8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x893fec: b               #0x893ee8
  }
}
