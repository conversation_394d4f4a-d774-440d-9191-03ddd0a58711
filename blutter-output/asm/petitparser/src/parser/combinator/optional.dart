// lib: , url: package:petitparser/src/parser/combinator/optional.dart

// class id: 1050903, size: 0x8
class :: {

  static Parser<Y0?> OptionalParserExtension.optional<Y0>(Parser<Y0>) {
    // ** addr: 0x88ad30, size: 0x70
    // 0x88ad30: EnterFrame
    //     0x88ad30: stp             fp, lr, [SP, #-0x10]!
    //     0x88ad34: mov             fp, SP
    // 0x88ad38: LoadField: r0 = r4->field_f
    //     0x88ad38: ldur            w0, [x4, #0xf]
    // 0x88ad3c: cbnz            w0, #0x88ad48
    // 0x88ad40: r1 = Null
    //     0x88ad40: mov             x1, NULL
    // 0x88ad44: b               #0x88ad54
    // 0x88ad48: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88ad48: ldur            w0, [x4, #0x17]
    // 0x88ad4c: add             x1, fp, w0, sxtw #2
    // 0x88ad50: ldr             x1, [x1, #0x10]
    // 0x88ad54: ldr             x0, [fp, #0x10]
    // 0x88ad58: r2 = Null
    //     0x88ad58: mov             x2, NULL
    // 0x88ad5c: r3 = <Y0?, Y0?, Y0?>
    //     0x88ad5c: add             x3, PP, #0x26, lsl #12  ; [pp+0x26a38] TypeArguments: <Y0?, Y0?, Y0?>
    //     0x88ad60: ldr             x3, [x3, #0xa38]
    // 0x88ad64: r0 = Null
    //     0x88ad64: mov             x0, NULL
    // 0x88ad68: cmp             x2, x0
    // 0x88ad6c: b.ne            #0x88ad78
    // 0x88ad70: cmp             x1, x0
    // 0x88ad74: b.eq            #0x88ad84
    // 0x88ad78: r30 = InstantiateTypeArgumentsStub
    //     0x88ad78: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88ad7c: LoadField: r30 = r30->field_7
    //     0x88ad7c: ldur            lr, [lr, #7]
    // 0x88ad80: blr             lr
    // 0x88ad84: mov             x1, x0
    // 0x88ad88: r0 = OptionalParser()
    //     0x88ad88: bl              #0x88ada0  ; AllocateOptionalParserStub -> OptionalParser<C2X0> (size=0x14)
    // 0x88ad8c: ldr             x1, [fp, #0x10]
    // 0x88ad90: StoreField: r0->field_b = r1
    //     0x88ad90: stur            w1, [x0, #0xb]
    // 0x88ad94: LeaveFrame
    //     0x88ad94: mov             SP, fp
    //     0x88ad98: ldp             fp, lr, [SP], #0x10
    // 0x88ad9c: ret
    //     0x88ad9c: ret             
  }
  static Parser<Y0> OptionalParserExtension.optionalWith<Y0>(Parser<Y0>, Y0) {
    // ** addr: 0x88e510, size: 0x7c
    // 0x88e510: EnterFrame
    //     0x88e510: stp             fp, lr, [SP, #-0x10]!
    //     0x88e514: mov             fp, SP
    // 0x88e518: LoadField: r0 = r4->field_f
    //     0x88e518: ldur            w0, [x4, #0xf]
    // 0x88e51c: cbnz            w0, #0x88e528
    // 0x88e520: r1 = Null
    //     0x88e520: mov             x1, NULL
    // 0x88e524: b               #0x88e534
    // 0x88e528: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88e528: ldur            w0, [x4, #0x17]
    // 0x88e52c: add             x1, fp, w0, sxtw #2
    // 0x88e530: ldr             x1, [x1, #0x10]
    // 0x88e534: ldr             x4, [fp, #0x18]
    // 0x88e538: ldr             x0, [fp, #0x10]
    // 0x88e53c: r2 = Null
    //     0x88e53c: mov             x2, NULL
    // 0x88e540: r3 = <Y0, Y0, Y0>
    //     0x88e540: add             x3, PP, #0x26, lsl #12  ; [pp+0x26aa8] TypeArguments: <Y0, Y0, Y0>
    //     0x88e544: ldr             x3, [x3, #0xaa8]
    // 0x88e548: r0 = Null
    //     0x88e548: mov             x0, NULL
    // 0x88e54c: cmp             x2, x0
    // 0x88e550: b.ne            #0x88e55c
    // 0x88e554: cmp             x1, x0
    // 0x88e558: b.eq            #0x88e568
    // 0x88e55c: r30 = InstantiateTypeArgumentsStub
    //     0x88e55c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88e560: LoadField: r30 = r30->field_7
    //     0x88e560: ldur            lr, [lr, #7]
    // 0x88e564: blr             lr
    // 0x88e568: mov             x1, x0
    // 0x88e56c: r0 = OptionalParser()
    //     0x88e56c: bl              #0x88ada0  ; AllocateOptionalParserStub -> OptionalParser<C2X0> (size=0x14)
    // 0x88e570: ldr             x1, [fp, #0x10]
    // 0x88e574: StoreField: r0->field_f = r1
    //     0x88e574: stur            w1, [x0, #0xf]
    // 0x88e578: ldr             x1, [fp, #0x18]
    // 0x88e57c: StoreField: r0->field_b = r1
    //     0x88e57c: stur            w1, [x0, #0xb]
    // 0x88e580: LeaveFrame
    //     0x88e580: mov             SP, fp
    //     0x88e584: ldp             fp, lr, [SP], #0x10
    // 0x88e588: ret
    //     0x88e588: ret             
  }
}

// class id: 747, size: 0x14, field offset: 0x10
class OptionalParser<C2X0> extends DelegateParser<C2X0, dynamic> {

  _ fastParseOn(/* No info */) {
    // ** addr: 0xeaf92c, size: 0x88
    // 0xeaf92c: EnterFrame
    //     0xeaf92c: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf930: mov             fp, SP
    // 0xeaf934: AllocStack(0x8)
    //     0xeaf934: sub             SP, SP, #8
    // 0xeaf938: SetupParameters(dynamic _ /* r3 => r4, fp-0x8 */)
    //     0xeaf938: mov             x4, x3
    //     0xeaf93c: stur            x3, [fp, #-8]
    // 0xeaf940: CheckStackOverflow
    //     0xeaf940: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf944: cmp             SP, x16
    //     0xeaf948: b.ls            #0xeaf9ac
    // 0xeaf94c: LoadField: r0 = r1->field_b
    //     0xeaf94c: ldur            w0, [x1, #0xb]
    // 0xeaf950: DecompressPointer r0
    //     0xeaf950: add             x0, x0, HEAP, lsl #32
    // 0xeaf954: r1 = LoadClassIdInstr(r0)
    //     0xeaf954: ldur            x1, [x0, #-1]
    //     0xeaf958: ubfx            x1, x1, #0xc, #0x14
    // 0xeaf95c: mov             x16, x0
    // 0xeaf960: mov             x0, x1
    // 0xeaf964: mov             x1, x16
    // 0xeaf968: mov             x3, x4
    // 0xeaf96c: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeaf96c: sub             lr, x0, #0xfce
    //     0xeaf970: ldr             lr, [x21, lr, lsl #3]
    //     0xeaf974: blr             lr
    // 0xeaf978: r2 = LoadInt32Instr(r0)
    //     0xeaf978: sbfx            x2, x0, #1, #0x1f
    //     0xeaf97c: tbz             w0, #0, #0xeaf984
    //     0xeaf980: ldur            x2, [x0, #7]
    // 0xeaf984: tbz             x2, #0x3f, #0xeaf98c
    // 0xeaf988: ldur            x2, [fp, #-8]
    // 0xeaf98c: r0 = BoxInt64Instr(r2)
    //     0xeaf98c: sbfiz           x0, x2, #1, #0x1f
    //     0xeaf990: cmp             x2, x0, asr #1
    //     0xeaf994: b.eq            #0xeaf9a0
    //     0xeaf998: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaf99c: stur            x2, [x0, #7]
    // 0xeaf9a0: LeaveFrame
    //     0xeaf9a0: mov             SP, fp
    //     0xeaf9a4: ldp             fp, lr, [SP], #0x10
    // 0xeaf9a8: ret
    //     0xeaf9a8: ret             
    // 0xeaf9ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf9ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf9b0: b               #0xeaf94c
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb1374, size: 0xc8
    // 0xeb1374: EnterFrame
    //     0xeb1374: stp             fp, lr, [SP, #-0x10]!
    //     0xeb1378: mov             fp, SP
    // 0xeb137c: AllocStack(0x20)
    //     0xeb137c: sub             SP, SP, #0x20
    // 0xeb1380: SetupParameters(OptionalParser<C2X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xeb1380: mov             x4, x1
    //     0xeb1384: mov             x3, x2
    //     0xeb1388: stur            x1, [fp, #-8]
    //     0xeb138c: stur            x2, [fp, #-0x10]
    // 0xeb1390: CheckStackOverflow
    //     0xeb1390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb1394: cmp             SP, x16
    //     0xeb1398: b.ls            #0xeb1434
    // 0xeb139c: LoadField: r1 = r4->field_b
    //     0xeb139c: ldur            w1, [x4, #0xb]
    // 0xeb13a0: DecompressPointer r1
    //     0xeb13a0: add             x1, x1, HEAP, lsl #32
    // 0xeb13a4: r0 = LoadClassIdInstr(r1)
    //     0xeb13a4: ldur            x0, [x1, #-1]
    //     0xeb13a8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb13ac: mov             x2, x3
    // 0xeb13b0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb13b0: sub             lr, x0, #1, lsl #12
    //     0xeb13b4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb13b8: blr             lr
    // 0xeb13bc: r1 = LoadClassIdInstr(r0)
    //     0xeb13bc: ldur            x1, [x0, #-1]
    //     0xeb13c0: ubfx            x1, x1, #0xc, #0x14
    // 0xeb13c4: cmp             x1, #0x2f3
    // 0xeb13c8: b.eq            #0xeb13d8
    // 0xeb13cc: LeaveFrame
    //     0xeb13cc: mov             SP, fp
    //     0xeb13d0: ldp             fp, lr, [SP], #0x10
    // 0xeb13d4: ret
    //     0xeb13d4: ret             
    // 0xeb13d8: ldur            x1, [fp, #-8]
    // 0xeb13dc: ldur            x0, [fp, #-0x10]
    // 0xeb13e0: LoadField: r2 = r1->field_7
    //     0xeb13e0: ldur            w2, [x1, #7]
    // 0xeb13e4: DecompressPointer r2
    //     0xeb13e4: add             x2, x2, HEAP, lsl #32
    // 0xeb13e8: LoadField: r3 = r1->field_f
    //     0xeb13e8: ldur            w3, [x1, #0xf]
    // 0xeb13ec: DecompressPointer r3
    //     0xeb13ec: add             x3, x3, HEAP, lsl #32
    // 0xeb13f0: stur            x3, [fp, #-0x20]
    // 0xeb13f4: LoadField: r4 = r0->field_7
    //     0xeb13f4: ldur            w4, [x0, #7]
    // 0xeb13f8: DecompressPointer r4
    //     0xeb13f8: add             x4, x4, HEAP, lsl #32
    // 0xeb13fc: stur            x4, [fp, #-8]
    // 0xeb1400: LoadField: r5 = r0->field_b
    //     0xeb1400: ldur            x5, [x0, #0xb]
    // 0xeb1404: mov             x1, x2
    // 0xeb1408: stur            x5, [fp, #-0x18]
    // 0xeb140c: r0 = Success()
    //     0xeb140c: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb1410: ldur            x1, [fp, #-0x20]
    // 0xeb1414: ArrayStore: r0[0] = r1  ; List_4
    //     0xeb1414: stur            w1, [x0, #0x17]
    // 0xeb1418: ldur            x1, [fp, #-8]
    // 0xeb141c: StoreField: r0->field_7 = r1
    //     0xeb141c: stur            w1, [x0, #7]
    // 0xeb1420: ldur            x1, [fp, #-0x18]
    // 0xeb1424: StoreField: r0->field_b = r1
    //     0xeb1424: stur            x1, [x0, #0xb]
    // 0xeb1428: LeaveFrame
    //     0xeb1428: mov             SP, fp
    //     0xeb142c: ldp             fp, lr, [SP], #0x10
    // 0xeb1430: ret
    //     0xeb1430: ret             
    // 0xeb1434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb1434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb1438: b               #0xeb139c
  }
}
