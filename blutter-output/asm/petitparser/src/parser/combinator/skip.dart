// lib: , url: package:petitparser/src/parser/combinator/skip.dart

// class id: 1050904, size: 0x8
class :: {

  static Parser<Y0> SkipParserExtension.skip<Y0>(Parser<Y0>, Parser<void>?) {
    // ** addr: 0x88adac, size: 0xb0
    // 0x88adac: EnterFrame
    //     0x88adac: stp             fp, lr, [SP, #-0x10]!
    //     0x88adb0: mov             fp, SP
    // 0x88adb4: AllocStack(0x8)
    //     0x88adb4: sub             SP, SP, #8
    // 0x88adb8: SetupParameters()
    //     0x88adb8: ldur            w0, [x4, #0xf]
    //     0x88adbc: cbnz            w0, #0x88adc8
    //     0x88adc0: mov             x2, NULL
    //     0x88adc4: b               #0x88add8
    //     0x88adc8: ldur            w0, [x4, #0x17]
    //     0x88adcc: add             x1, fp, w0, sxtw #2
    //     0x88add0: ldr             x1, [x1, #0x10]
    //     0x88add4: mov             x2, x1
    //     0x88add8: ldr             x1, [fp, #0x18]
    //     0x88addc: ldr             x0, [fp, #0x10]
    //     0x88ade0: stur            x2, [fp, #-8]
    // 0x88ade4: CheckStackOverflow
    //     0x88ade4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88ade8: cmp             SP, x16
    //     0x88adec: b.ls            #0x88ae54
    // 0x88adf0: r0 = epsilon()
    //     0x88adf0: bl              #0x88ae68  ; [package:petitparser/src/parser/misc/epsilon.dart] ::epsilon
    // 0x88adf4: ldur            x1, [fp, #-8]
    // 0x88adf8: r2 = Null
    //     0x88adf8: mov             x2, NULL
    // 0x88adfc: r3 = <Y0, Y0, Y0>
    //     0x88adfc: add             x3, PP, #0x26, lsl #12  ; [pp+0x26a40] TypeArguments: <Y0, Y0, Y0>
    //     0x88ae00: ldr             x3, [x3, #0xa40]
    // 0x88ae04: stur            x0, [fp, #-8]
    // 0x88ae08: r0 = Null
    //     0x88ae08: mov             x0, NULL
    // 0x88ae0c: cmp             x2, x0
    // 0x88ae10: b.ne            #0x88ae1c
    // 0x88ae14: cmp             x1, x0
    // 0x88ae18: b.eq            #0x88ae28
    // 0x88ae1c: r30 = InstantiateTypeArgumentsStub
    //     0x88ae1c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88ae20: LoadField: r30 = r30->field_7
    //     0x88ae20: ldur            lr, [lr, #7]
    // 0x88ae24: blr             lr
    // 0x88ae28: mov             x1, x0
    // 0x88ae2c: r0 = SkipParser()
    //     0x88ae2c: bl              #0x88ae5c  ; AllocateSkipParserStub -> SkipParser<C2X0> (size=0x18)
    // 0x88ae30: ldr             x1, [fp, #0x10]
    // 0x88ae34: StoreField: r0->field_f = r1
    //     0x88ae34: stur            w1, [x0, #0xf]
    // 0x88ae38: ldur            x1, [fp, #-8]
    // 0x88ae3c: StoreField: r0->field_13 = r1
    //     0x88ae3c: stur            w1, [x0, #0x13]
    // 0x88ae40: ldr             x1, [fp, #0x18]
    // 0x88ae44: StoreField: r0->field_b = r1
    //     0x88ae44: stur            w1, [x0, #0xb]
    // 0x88ae48: LeaveFrame
    //     0x88ae48: mov             SP, fp
    //     0x88ae4c: ldp             fp, lr, [SP], #0x10
    // 0x88ae50: ret
    //     0x88ae50: ret             
    // 0x88ae54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ae54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88ae58: b               #0x88adf0
  }
}

// class id: 746, size: 0x18, field offset: 0x10
class SkipParser<C2X0> extends DelegateParser<C2X0, dynamic>
    implements SequentialParser {

  get _ children(/* No info */) {
    // ** addr: 0x88f844, size: 0x88
    // 0x88f844: EnterFrame
    //     0x88f844: stp             fp, lr, [SP, #-0x10]!
    //     0x88f848: mov             fp, SP
    // 0x88f84c: AllocStack(0x20)
    //     0x88f84c: sub             SP, SP, #0x20
    // 0x88f850: r0 = 6
    //     0x88f850: movz            x0, #0x6
    // 0x88f854: LoadField: r3 = r1->field_f
    //     0x88f854: ldur            w3, [x1, #0xf]
    // 0x88f858: DecompressPointer r3
    //     0x88f858: add             x3, x3, HEAP, lsl #32
    // 0x88f85c: stur            x3, [fp, #-0x18]
    // 0x88f860: LoadField: r4 = r1->field_b
    //     0x88f860: ldur            w4, [x1, #0xb]
    // 0x88f864: DecompressPointer r4
    //     0x88f864: add             x4, x4, HEAP, lsl #32
    // 0x88f868: stur            x4, [fp, #-0x10]
    // 0x88f86c: LoadField: r5 = r1->field_13
    //     0x88f86c: ldur            w5, [x1, #0x13]
    // 0x88f870: DecompressPointer r5
    //     0x88f870: add             x5, x5, HEAP, lsl #32
    // 0x88f874: mov             x2, x0
    // 0x88f878: stur            x5, [fp, #-8]
    // 0x88f87c: r1 = Null
    //     0x88f87c: mov             x1, NULL
    // 0x88f880: r0 = AllocateArray()
    //     0x88f880: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88f884: mov             x2, x0
    // 0x88f888: ldur            x0, [fp, #-0x18]
    // 0x88f88c: stur            x2, [fp, #-0x20]
    // 0x88f890: StoreField: r2->field_f = r0
    //     0x88f890: stur            w0, [x2, #0xf]
    // 0x88f894: ldur            x0, [fp, #-0x10]
    // 0x88f898: StoreField: r2->field_13 = r0
    //     0x88f898: stur            w0, [x2, #0x13]
    // 0x88f89c: ldur            x0, [fp, #-8]
    // 0x88f8a0: ArrayStore: r2[0] = r0  ; List_4
    //     0x88f8a0: stur            w0, [x2, #0x17]
    // 0x88f8a4: r1 = <Parser>
    //     0x88f8a4: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x88f8a8: ldr             x1, [x1, #0x6f8]
    // 0x88f8ac: r0 = AllocateGrowableArray()
    //     0x88f8ac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88f8b0: ldur            x1, [fp, #-0x20]
    // 0x88f8b4: StoreField: r0->field_f = r1
    //     0x88f8b4: stur            w1, [x0, #0xf]
    // 0x88f8b8: r1 = 6
    //     0x88f8b8: movz            x1, #0x6
    // 0x88f8bc: StoreField: r0->field_b = r1
    //     0x88f8bc: stur            w1, [x0, #0xb]
    // 0x88f8c0: LeaveFrame
    //     0x88f8c0: mov             SP, fp
    //     0x88f8c4: ldp             fp, lr, [SP], #0x10
    // 0x88f8c8: ret
    //     0x88f8c8: ret             
  }
  _ replace(/* No info */) {
    // ** addr: 0x893c10, size: 0x104
    // 0x893c10: EnterFrame
    //     0x893c10: stp             fp, lr, [SP, #-0x10]!
    //     0x893c14: mov             fp, SP
    // 0x893c18: AllocStack(0x28)
    //     0x893c18: sub             SP, SP, #0x28
    // 0x893c1c: SetupParameters(SkipParser<C2X0> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x893c1c: mov             x5, x1
    //     0x893c20: mov             x4, x2
    //     0x893c24: mov             x0, x3
    //     0x893c28: stur            x1, [fp, #-8]
    //     0x893c2c: stur            x2, [fp, #-0x10]
    //     0x893c30: stur            x3, [fp, #-0x18]
    // 0x893c34: CheckStackOverflow
    //     0x893c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x893c38: cmp             SP, x16
    //     0x893c3c: b.ls            #0x893d0c
    // 0x893c40: mov             x1, x5
    // 0x893c44: mov             x2, x4
    // 0x893c48: mov             x3, x0
    // 0x893c4c: r0 = replace()
    //     0x893c4c: bl              #0x893dc0  ; [package:petitparser/src/parser/combinator/delegate.dart] DelegateParser::replace
    // 0x893c50: ldur            x1, [fp, #-8]
    // 0x893c54: LoadField: r0 = r1->field_f
    //     0x893c54: ldur            w0, [x1, #0xf]
    // 0x893c58: DecompressPointer r0
    //     0x893c58: add             x0, x0, HEAP, lsl #32
    // 0x893c5c: r2 = LoadClassIdInstr(r0)
    //     0x893c5c: ldur            x2, [x0, #-1]
    //     0x893c60: ubfx            x2, x2, #0xc, #0x14
    // 0x893c64: ldur            x16, [fp, #-0x10]
    // 0x893c68: stp             x16, x0, [SP]
    // 0x893c6c: mov             x0, x2
    // 0x893c70: mov             lr, x0
    // 0x893c74: ldr             lr, [x21, lr, lsl #3]
    // 0x893c78: blr             lr
    // 0x893c7c: tbnz            w0, #4, #0x893ca8
    // 0x893c80: ldur            x1, [fp, #-8]
    // 0x893c84: ldur            x0, [fp, #-0x18]
    // 0x893c88: StoreField: r1->field_f = r0
    //     0x893c88: stur            w0, [x1, #0xf]
    //     0x893c8c: ldurb           w16, [x1, #-1]
    //     0x893c90: ldurb           w17, [x0, #-1]
    //     0x893c94: and             x16, x17, x16, lsr #2
    //     0x893c98: tst             x16, HEAP, lsr #32
    //     0x893c9c: b.eq            #0x893ca4
    //     0x893ca0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x893ca4: b               #0x893cac
    // 0x893ca8: ldur            x1, [fp, #-8]
    // 0x893cac: LoadField: r0 = r1->field_13
    //     0x893cac: ldur            w0, [x1, #0x13]
    // 0x893cb0: DecompressPointer r0
    //     0x893cb0: add             x0, x0, HEAP, lsl #32
    // 0x893cb4: r2 = LoadClassIdInstr(r0)
    //     0x893cb4: ldur            x2, [x0, #-1]
    //     0x893cb8: ubfx            x2, x2, #0xc, #0x14
    // 0x893cbc: ldur            x16, [fp, #-0x10]
    // 0x893cc0: stp             x16, x0, [SP]
    // 0x893cc4: mov             x0, x2
    // 0x893cc8: mov             lr, x0
    // 0x893ccc: ldr             lr, [x21, lr, lsl #3]
    // 0x893cd0: blr             lr
    // 0x893cd4: tbnz            w0, #4, #0x893cfc
    // 0x893cd8: ldur            x1, [fp, #-8]
    // 0x893cdc: ldur            x0, [fp, #-0x18]
    // 0x893ce0: StoreField: r1->field_13 = r0
    //     0x893ce0: stur            w0, [x1, #0x13]
    //     0x893ce4: ldurb           w16, [x1, #-1]
    //     0x893ce8: ldurb           w17, [x0, #-1]
    //     0x893cec: and             x16, x17, x16, lsr #2
    //     0x893cf0: tst             x16, HEAP, lsr #32
    //     0x893cf4: b.eq            #0x893cfc
    //     0x893cf8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x893cfc: r0 = Null
    //     0x893cfc: mov             x0, NULL
    // 0x893d00: LeaveFrame
    //     0x893d00: mov             SP, fp
    //     0x893d04: ldp             fp, lr, [SP], #0x10
    // 0x893d08: ret
    //     0x893d08: ret             
    // 0x893d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x893d0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x893d10: b               #0x893c40
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeaf9b4, size: 0xe4
    // 0xeaf9b4: EnterFrame
    //     0xeaf9b4: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf9b8: mov             fp, SP
    // 0xeaf9bc: AllocStack(0x10)
    //     0xeaf9bc: sub             SP, SP, #0x10
    // 0xeaf9c0: SetupParameters(SkipParser<C2X0> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */)
    //     0xeaf9c0: mov             x5, x1
    //     0xeaf9c4: mov             x4, x2
    //     0xeaf9c8: stur            x1, [fp, #-8]
    //     0xeaf9cc: stur            x2, [fp, #-0x10]
    // 0xeaf9d0: CheckStackOverflow
    //     0xeaf9d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf9d4: cmp             SP, x16
    //     0xeaf9d8: b.ls            #0xeafa90
    // 0xeaf9dc: LoadField: r1 = r5->field_f
    //     0xeaf9dc: ldur            w1, [x5, #0xf]
    // 0xeaf9e0: DecompressPointer r1
    //     0xeaf9e0: add             x1, x1, HEAP, lsl #32
    // 0xeaf9e4: r0 = LoadClassIdInstr(r1)
    //     0xeaf9e4: ldur            x0, [x1, #-1]
    //     0xeaf9e8: ubfx            x0, x0, #0xc, #0x14
    // 0xeaf9ec: mov             x2, x4
    // 0xeaf9f0: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeaf9f0: sub             lr, x0, #0xfce
    //     0xeaf9f4: ldr             lr, [x21, lr, lsl #3]
    //     0xeaf9f8: blr             lr
    // 0xeaf9fc: r3 = LoadInt32Instr(r0)
    //     0xeaf9fc: sbfx            x3, x0, #1, #0x1f
    //     0xeafa00: tbz             w0, #0, #0xeafa08
    //     0xeafa04: ldur            x3, [x0, #7]
    // 0xeafa08: tbz             x3, #0x3f, #0xeafa1c
    // 0xeafa0c: r0 = -2
    //     0xeafa0c: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeafa10: LeaveFrame
    //     0xeafa10: mov             SP, fp
    //     0xeafa14: ldp             fp, lr, [SP], #0x10
    // 0xeafa18: ret
    //     0xeafa18: ret             
    // 0xeafa1c: ldur            x4, [fp, #-8]
    // 0xeafa20: LoadField: r1 = r4->field_b
    //     0xeafa20: ldur            w1, [x4, #0xb]
    // 0xeafa24: DecompressPointer r1
    //     0xeafa24: add             x1, x1, HEAP, lsl #32
    // 0xeafa28: r0 = LoadClassIdInstr(r1)
    //     0xeafa28: ldur            x0, [x1, #-1]
    //     0xeafa2c: ubfx            x0, x0, #0xc, #0x14
    // 0xeafa30: ldur            x2, [fp, #-0x10]
    // 0xeafa34: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeafa34: sub             lr, x0, #0xfce
    //     0xeafa38: ldr             lr, [x21, lr, lsl #3]
    //     0xeafa3c: blr             lr
    // 0xeafa40: r3 = LoadInt32Instr(r0)
    //     0xeafa40: sbfx            x3, x0, #1, #0x1f
    //     0xeafa44: tbz             w0, #0, #0xeafa4c
    //     0xeafa48: ldur            x3, [x0, #7]
    // 0xeafa4c: tbz             x3, #0x3f, #0xeafa60
    // 0xeafa50: r0 = -2
    //     0xeafa50: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeafa54: LeaveFrame
    //     0xeafa54: mov             SP, fp
    //     0xeafa58: ldp             fp, lr, [SP], #0x10
    // 0xeafa5c: ret
    //     0xeafa5c: ret             
    // 0xeafa60: ldur            x0, [fp, #-8]
    // 0xeafa64: LoadField: r1 = r0->field_13
    //     0xeafa64: ldur            w1, [x0, #0x13]
    // 0xeafa68: DecompressPointer r1
    //     0xeafa68: add             x1, x1, HEAP, lsl #32
    // 0xeafa6c: r0 = LoadClassIdInstr(r1)
    //     0xeafa6c: ldur            x0, [x1, #-1]
    //     0xeafa70: ubfx            x0, x0, #0xc, #0x14
    // 0xeafa74: ldur            x2, [fp, #-0x10]
    // 0xeafa78: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeafa78: sub             lr, x0, #0xfce
    //     0xeafa7c: ldr             lr, [x21, lr, lsl #3]
    //     0xeafa80: blr             lr
    // 0xeafa84: LeaveFrame
    //     0xeafa84: mov             SP, fp
    //     0xeafa88: ldp             fp, lr, [SP], #0x10
    // 0xeafa8c: ret
    //     0xeafa8c: ret             
    // 0xeafa90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeafa90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeafa94: b               #0xeaf9dc
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb143c, size: 0x188
    // 0xeb143c: EnterFrame
    //     0xeb143c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb1440: mov             fp, SP
    // 0xeb1444: AllocStack(0x20)
    //     0xeb1444: sub             SP, SP, #0x20
    // 0xeb1448: SetupParameters(SkipParser<C2X0> this /* r1 => r3, fp-0x8 */)
    //     0xeb1448: mov             x3, x1
    //     0xeb144c: stur            x1, [fp, #-8]
    // 0xeb1450: CheckStackOverflow
    //     0xeb1450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb1454: cmp             SP, x16
    //     0xeb1458: b.ls            #0xeb15bc
    // 0xeb145c: LoadField: r1 = r3->field_f
    //     0xeb145c: ldur            w1, [x3, #0xf]
    // 0xeb1460: DecompressPointer r1
    //     0xeb1460: add             x1, x1, HEAP, lsl #32
    // 0xeb1464: r0 = LoadClassIdInstr(r1)
    //     0xeb1464: ldur            x0, [x1, #-1]
    //     0xeb1468: ubfx            x0, x0, #0xc, #0x14
    // 0xeb146c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb146c: sub             lr, x0, #1, lsl #12
    //     0xeb1470: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1474: blr             lr
    // 0xeb1478: r1 = LoadClassIdInstr(r0)
    //     0xeb1478: ldur            x1, [x0, #-1]
    //     0xeb147c: ubfx            x1, x1, #0xc, #0x14
    // 0xeb1480: cmp             x1, #0x2f3
    // 0xeb1484: b.ne            #0xeb1494
    // 0xeb1488: LeaveFrame
    //     0xeb1488: mov             SP, fp
    //     0xeb148c: ldp             fp, lr, [SP], #0x10
    // 0xeb1490: ret
    //     0xeb1490: ret             
    // 0xeb1494: ldur            x3, [fp, #-8]
    // 0xeb1498: LoadField: r1 = r3->field_b
    //     0xeb1498: ldur            w1, [x3, #0xb]
    // 0xeb149c: DecompressPointer r1
    //     0xeb149c: add             x1, x1, HEAP, lsl #32
    // 0xeb14a0: r2 = LoadClassIdInstr(r1)
    //     0xeb14a0: ldur            x2, [x1, #-1]
    //     0xeb14a4: ubfx            x2, x2, #0xc, #0x14
    // 0xeb14a8: mov             x16, x0
    // 0xeb14ac: mov             x0, x2
    // 0xeb14b0: mov             x2, x16
    // 0xeb14b4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb14b4: sub             lr, x0, #1, lsl #12
    //     0xeb14b8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb14bc: blr             lr
    // 0xeb14c0: mov             x3, x0
    // 0xeb14c4: stur            x3, [fp, #-0x18]
    // 0xeb14c8: r4 = LoadClassIdInstr(r3)
    //     0xeb14c8: ldur            x4, [x3, #-1]
    //     0xeb14cc: ubfx            x4, x4, #0xc, #0x14
    // 0xeb14d0: stur            x4, [fp, #-0x10]
    // 0xeb14d4: cmp             x4, #0x2f3
    // 0xeb14d8: b.ne            #0xeb14ec
    // 0xeb14dc: mov             x0, x3
    // 0xeb14e0: LeaveFrame
    //     0xeb14e0: mov             SP, fp
    //     0xeb14e4: ldp             fp, lr, [SP], #0x10
    // 0xeb14e8: ret
    //     0xeb14e8: ret             
    // 0xeb14ec: ldur            x5, [fp, #-8]
    // 0xeb14f0: LoadField: r1 = r5->field_13
    //     0xeb14f0: ldur            w1, [x5, #0x13]
    // 0xeb14f4: DecompressPointer r1
    //     0xeb14f4: add             x1, x1, HEAP, lsl #32
    // 0xeb14f8: r0 = LoadClassIdInstr(r1)
    //     0xeb14f8: ldur            x0, [x1, #-1]
    //     0xeb14fc: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1500: mov             x2, x3
    // 0xeb1504: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1504: sub             lr, x0, #1, lsl #12
    //     0xeb1508: ldr             lr, [x21, lr, lsl #3]
    //     0xeb150c: blr             lr
    // 0xeb1510: r1 = LoadClassIdInstr(r0)
    //     0xeb1510: ldur            x1, [x0, #-1]
    //     0xeb1514: ubfx            x1, x1, #0xc, #0x14
    // 0xeb1518: cmp             x1, #0x2f3
    // 0xeb151c: b.ne            #0xeb152c
    // 0xeb1520: LeaveFrame
    //     0xeb1520: mov             SP, fp
    //     0xeb1524: ldp             fp, lr, [SP], #0x10
    // 0xeb1528: ret
    //     0xeb1528: ret             
    // 0xeb152c: ldur            x2, [fp, #-8]
    // 0xeb1530: ldur            x1, [fp, #-0x10]
    // 0xeb1534: LoadField: r3 = r2->field_7
    //     0xeb1534: ldur            w3, [x2, #7]
    // 0xeb1538: DecompressPointer r3
    //     0xeb1538: add             x3, x3, HEAP, lsl #32
    // 0xeb153c: cmp             x1, #0x2f3
    // 0xeb1540: b.eq            #0xeb159c
    // 0xeb1544: ldur            x1, [fp, #-0x18]
    // 0xeb1548: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xeb1548: ldur            w2, [x1, #0x17]
    // 0xeb154c: DecompressPointer r2
    //     0xeb154c: add             x2, x2, HEAP, lsl #32
    // 0xeb1550: stur            x2, [fp, #-0x20]
    // 0xeb1554: LoadField: r4 = r0->field_7
    //     0xeb1554: ldur            w4, [x0, #7]
    // 0xeb1558: DecompressPointer r4
    //     0xeb1558: add             x4, x4, HEAP, lsl #32
    // 0xeb155c: stur            x4, [fp, #-8]
    // 0xeb1560: LoadField: r5 = r0->field_b
    //     0xeb1560: ldur            x5, [x0, #0xb]
    // 0xeb1564: mov             x1, x3
    // 0xeb1568: stur            x5, [fp, #-0x10]
    // 0xeb156c: r0 = Success()
    //     0xeb156c: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb1570: mov             x1, x0
    // 0xeb1574: ldur            x0, [fp, #-0x20]
    // 0xeb1578: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb1578: stur            w0, [x1, #0x17]
    // 0xeb157c: ldur            x0, [fp, #-8]
    // 0xeb1580: StoreField: r1->field_7 = r0
    //     0xeb1580: stur            w0, [x1, #7]
    // 0xeb1584: ldur            x0, [fp, #-0x10]
    // 0xeb1588: StoreField: r1->field_b = r0
    //     0xeb1588: stur            x0, [x1, #0xb]
    // 0xeb158c: mov             x0, x1
    // 0xeb1590: LeaveFrame
    //     0xeb1590: mov             SP, fp
    //     0xeb1594: ldp             fp, lr, [SP], #0x10
    // 0xeb1598: ret
    //     0xeb1598: ret             
    // 0xeb159c: ldur            x1, [fp, #-0x18]
    // 0xeb15a0: r0 = ParserException()
    //     0xeb15a0: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb15a4: mov             x1, x0
    // 0xeb15a8: ldur            x0, [fp, #-0x18]
    // 0xeb15ac: StoreField: r1->field_7 = r0
    //     0xeb15ac: stur            w0, [x1, #7]
    // 0xeb15b0: mov             x0, x1
    // 0xeb15b4: r0 = Throw()
    //     0xeb15b4: bl              #0xec04b8  ; ThrowStub
    // 0xeb15b8: brk             #0
    // 0xeb15bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb15bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb15c0: b               #0xeb145c
  }
}
