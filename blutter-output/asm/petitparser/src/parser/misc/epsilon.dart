// lib: , url: package:petitparser/src/parser/misc/epsilon.dart

// class id: 1050905, size: 0x8
class :: {

  static _ epsilon(/* No info */) {
    // ** addr: 0x88ae68, size: 0x3c
    // 0x88ae68: EnterFrame
    //     0x88ae68: stp             fp, lr, [SP, #-0x10]!
    //     0x88ae6c: mov             fp, SP
    // 0x88ae70: AllocStack(0x10)
    //     0x88ae70: sub             SP, SP, #0x10
    // 0x88ae74: CheckStackOverflow
    //     0x88ae74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88ae78: cmp             SP, x16
    //     0x88ae7c: b.ls            #0x88ae9c
    // 0x88ae80: r16 = <void?>
    //     0x88ae80: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x88ae84: stp             NULL, x16, [SP]
    // 0x88ae88: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88ae88: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88ae8c: r0 = epsilonWith()
    //     0x88ae8c: bl              #0x88aea4  ; [package:petitparser/src/parser/misc/epsilon.dart] ::epsilonWith
    // 0x88ae90: LeaveFrame
    //     0x88ae90: mov             SP, fp
    //     0x88ae94: ldp             fp, lr, [SP], #0x10
    // 0x88ae98: ret
    //     0x88ae98: ret             
    // 0x88ae9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ae9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88aea0: b               #0x88ae80
  }
  static _ epsilonWith(/* No info */) {
    // ** addr: 0x88aea4, size: 0x40
    // 0x88aea4: EnterFrame
    //     0x88aea4: stp             fp, lr, [SP, #-0x10]!
    //     0x88aea8: mov             fp, SP
    // 0x88aeac: LoadField: r0 = r4->field_f
    //     0x88aeac: ldur            w0, [x4, #0xf]
    // 0x88aeb0: cbnz            w0, #0x88aebc
    // 0x88aeb4: r1 = Null
    //     0x88aeb4: mov             x1, NULL
    // 0x88aeb8: b               #0x88aec8
    // 0x88aebc: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88aebc: ldur            w0, [x4, #0x17]
    // 0x88aec0: add             x1, fp, w0, sxtw #2
    // 0x88aec4: ldr             x1, [x1, #0x10]
    // 0x88aec8: ldr             x0, [fp, #0x10]
    // 0x88aecc: r0 = EpsilonParser()
    //     0x88aecc: bl              #0x88aee4  ; AllocateEpsilonParserStub -> EpsilonParser<X0> (size=0x10)
    // 0x88aed0: ldr             x1, [fp, #0x10]
    // 0x88aed4: StoreField: r0->field_b = r1
    //     0x88aed4: stur            w1, [x0, #0xb]
    // 0x88aed8: LeaveFrame
    //     0x88aed8: mov             SP, fp
    //     0x88aedc: ldp             fp, lr, [SP], #0x10
    // 0x88aee0: ret
    //     0x88aee0: ret             
  }
}

// class id: 733, size: 0x10, field offset: 0xc
class EpsilonParser<X0> extends Parser<X0> {

  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb0648, size: 0x28
    // 0xeb0648: r0 = BoxInt64Instr(r3)
    //     0xeb0648: sbfiz           x0, x3, #1, #0x1f
    //     0xeb064c: cmp             x3, x0, asr #1
    //     0xeb0650: b.eq            #0xeb066c
    //     0xeb0654: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0658: mov             fp, SP
    //     0xeb065c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb0660: mov             SP, fp
    //     0xeb0664: ldp             fp, lr, [SP], #0x10
    //     0xeb0668: stur            x3, [x0, #7]
    // 0xeb066c: ret
    //     0xeb066c: ret             
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb2d94, size: 0x60
    // 0xeb2d94: EnterFrame
    //     0xeb2d94: stp             fp, lr, [SP, #-0x10]!
    //     0xeb2d98: mov             fp, SP
    // 0xeb2d9c: AllocStack(0x18)
    //     0xeb2d9c: sub             SP, SP, #0x18
    // 0xeb2da0: LoadField: r0 = r1->field_7
    //     0xeb2da0: ldur            w0, [x1, #7]
    // 0xeb2da4: DecompressPointer r0
    //     0xeb2da4: add             x0, x0, HEAP, lsl #32
    // 0xeb2da8: LoadField: r3 = r1->field_b
    //     0xeb2da8: ldur            w3, [x1, #0xb]
    // 0xeb2dac: DecompressPointer r3
    //     0xeb2dac: add             x3, x3, HEAP, lsl #32
    // 0xeb2db0: stur            x3, [fp, #-0x18]
    // 0xeb2db4: LoadField: r4 = r2->field_7
    //     0xeb2db4: ldur            w4, [x2, #7]
    // 0xeb2db8: DecompressPointer r4
    //     0xeb2db8: add             x4, x4, HEAP, lsl #32
    // 0xeb2dbc: stur            x4, [fp, #-0x10]
    // 0xeb2dc0: LoadField: r5 = r2->field_b
    //     0xeb2dc0: ldur            x5, [x2, #0xb]
    // 0xeb2dc4: mov             x1, x0
    // 0xeb2dc8: stur            x5, [fp, #-8]
    // 0xeb2dcc: r0 = Success()
    //     0xeb2dcc: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb2dd0: ldur            x1, [fp, #-0x18]
    // 0xeb2dd4: ArrayStore: r0[0] = r1  ; List_4
    //     0xeb2dd4: stur            w1, [x0, #0x17]
    // 0xeb2dd8: ldur            x1, [fp, #-0x10]
    // 0xeb2ddc: StoreField: r0->field_7 = r1
    //     0xeb2ddc: stur            w1, [x0, #7]
    // 0xeb2de0: ldur            x1, [fp, #-8]
    // 0xeb2de4: StoreField: r0->field_b = r1
    //     0xeb2de4: stur            x1, [x0, #0xb]
    // 0xeb2de8: LeaveFrame
    //     0xeb2de8: mov             SP, fp
    //     0xeb2dec: ldp             fp, lr, [SP], #0x10
    // 0xeb2df0: ret
    //     0xeb2df0: ret             
  }
}
