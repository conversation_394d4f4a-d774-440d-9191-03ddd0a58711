// lib: , url: package:petitparser/src/parser/misc/newline.dart

// class id: 1050906, size: 0x8
class :: {

  static Parser<String> newline() {
    // ** addr: 0xc3bb20, size: 0x28
    // 0xc3bb20: EnterFrame
    //     0xc3bb20: stp             fp, lr, [SP, #-0x10]!
    //     0xc3bb24: mov             fp, SP
    // 0xc3bb28: r1 = <String>
    //     0xc3bb28: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc3bb2c: r0 = NewlineParser()
    //     0xc3bb2c: bl              #0xc3bb48  ; AllocateNewlineParserStub -> NewlineParser (size=0x10)
    // 0xc3bb30: r1 = "newline expected"
    //     0xc3bb30: add             x1, PP, #0x31, lsl #12  ; [pp+0x31488] "newline expected"
    //     0xc3bb34: ldr             x1, [x1, #0x488]
    // 0xc3bb38: StoreField: r0->field_b = r1
    //     0xc3bb38: stur            w1, [x0, #0xb]
    // 0xc3bb3c: LeaveFrame
    //     0xc3bb3c: mov             SP, fp
    //     0xc3bb40: ldp             fp, lr, [SP], #0x10
    // 0xc3bb44: ret
    //     0xc3bb44: ret             
  }
}

// class id: 732, size: 0x10, field offset: 0xc
class NewlineParser extends Parser<dynamic> {

  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb0670, size: 0xe8
    // 0xeb0670: mov             x16, x3
    // 0xeb0674: mov             x3, x2
    // 0xeb0678: mov             x2, x16
    // 0xeb067c: LoadField: r4 = r3->field_7
    //     0xeb067c: ldur            w4, [x3, #7]
    // 0xeb0680: r5 = LoadInt32Instr(r4)
    //     0xeb0680: sbfx            x5, x4, #1, #0x1f
    // 0xeb0684: cmp             x2, x5
    // 0xeb0688: b.ge            #0xeb0744
    // 0xeb068c: mov             x0, x5
    // 0xeb0690: mov             x1, x2
    // 0xeb0694: cmp             x1, x0
    // 0xeb0698: b.hs            #0xeb074c
    // 0xeb069c: r1 = LoadClassIdInstr(r3)
    //     0xeb069c: ldur            x1, [x3, #-1]
    //     0xeb06a0: ubfx            x1, x1, #0xc, #0x14
    // 0xeb06a4: lsl             x1, x1, #1
    // 0xeb06a8: cmp             w1, #0xbc
    // 0xeb06ac: b.ne            #0xeb06bc
    // 0xeb06b0: ArrayLoad: r4 = r3[r2]  ; TypedUnsigned_1
    //     0xeb06b0: add             x16, x3, x2
    //     0xeb06b4: ldrb            w4, [x16, #0xf]
    // 0xeb06b8: b               #0xeb06c4
    // 0xeb06bc: add             x16, x3, x2, lsl #1
    // 0xeb06c0: ldurh           w4, [x16, #0xf]
    // 0xeb06c4: cmp             x4, #0xa
    // 0xeb06c8: b.gt            #0xeb06e4
    // 0xeb06cc: lsl             x6, x4, #1
    // 0xeb06d0: cmp             w6, #0x14
    // 0xeb06d4: b.ne            #0xeb0744
    // 0xeb06d8: add             x6, x2, #1
    // 0xeb06dc: lsl             x0, x6, #1
    // 0xeb06e0: ret
    //     0xeb06e0: ret             
    // 0xeb06e4: cmp             x4, #0xd
    // 0xeb06e8: b.lt            #0xeb0744
    // 0xeb06ec: lsl             x6, x4, #1
    // 0xeb06f0: cmp             w6, #0x1a
    // 0xeb06f4: b.ne            #0xeb0744
    // 0xeb06f8: add             x4, x2, #1
    // 0xeb06fc: cmp             x4, x5
    // 0xeb0700: b.ge            #0xeb0738
    // 0xeb0704: cmp             w1, #0xbc
    // 0xeb0708: b.ne            #0xeb0720
    // 0xeb070c: ArrayLoad: r1 = r3[r4]  ; TypedUnsigned_1
    //     0xeb070c: add             x16, x3, x4
    //     0xeb0710: ldrb            w1, [x16, #0xf]
    // 0xeb0714: cmp             x1, #0xa
    // 0xeb0718: b.ne            #0xeb0738
    // 0xeb071c: b               #0xeb0730
    // 0xeb0720: add             x16, x3, x4, lsl #1
    // 0xeb0724: ldurh           w1, [x16, #0xf]
    // 0xeb0728: cmp             x1, #0xa
    // 0xeb072c: b.ne            #0xeb0738
    // 0xeb0730: add             x1, x2, #2
    // 0xeb0734: b               #0xeb073c
    // 0xeb0738: mov             x1, x4
    // 0xeb073c: lsl             x0, x1, #1
    // 0xeb0740: ret
    //     0xeb0740: ret             
    // 0xeb0744: r0 = -2
    //     0xeb0744: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0748: ret
    //     0xeb0748: ret             
    // 0xeb074c: EnterFrame
    //     0xeb074c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0750: mov             fp, SP
    // 0xeb0754: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb0754: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb2df4, size: 0x198
    // 0xeb2df4: EnterFrame
    //     0xeb2df4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb2df8: mov             fp, SP
    // 0xeb2dfc: AllocStack(0x30)
    //     0xeb2dfc: sub             SP, SP, #0x30
    // 0xeb2e00: CheckStackOverflow
    //     0xeb2e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb2e04: cmp             SP, x16
    //     0xeb2e08: b.ls            #0xeb2f80
    // 0xeb2e0c: LoadField: r3 = r2->field_7
    //     0xeb2e0c: ldur            w3, [x2, #7]
    // 0xeb2e10: DecompressPointer r3
    //     0xeb2e10: add             x3, x3, HEAP, lsl #32
    // 0xeb2e14: stur            x3, [fp, #-0x10]
    // 0xeb2e18: LoadField: r4 = r2->field_b
    //     0xeb2e18: ldur            x4, [x2, #0xb]
    // 0xeb2e1c: stur            x4, [fp, #-8]
    // 0xeb2e20: LoadField: r0 = r3->field_7
    //     0xeb2e20: ldur            w0, [x3, #7]
    // 0xeb2e24: r5 = LoadInt32Instr(r0)
    //     0xeb2e24: sbfx            x5, x0, #1, #0x1f
    // 0xeb2e28: cmp             x4, x5
    // 0xeb2e2c: b.ge            #0xeb2f50
    // 0xeb2e30: mov             x0, x5
    // 0xeb2e34: mov             x1, x4
    // 0xeb2e38: cmp             x1, x0
    // 0xeb2e3c: b.hs            #0xeb2f88
    // 0xeb2e40: r0 = LoadClassIdInstr(r3)
    //     0xeb2e40: ldur            x0, [x3, #-1]
    //     0xeb2e44: ubfx            x0, x0, #0xc, #0x14
    // 0xeb2e48: lsl             x0, x0, #1
    // 0xeb2e4c: cmp             w0, #0xbc
    // 0xeb2e50: b.ne            #0xeb2e60
    // 0xeb2e54: ArrayLoad: r1 = r3[r4]  ; TypedUnsigned_1
    //     0xeb2e54: add             x16, x3, x4
    //     0xeb2e58: ldrb            w1, [x16, #0xf]
    // 0xeb2e5c: b               #0xeb2e68
    // 0xeb2e60: add             x16, x3, x4, lsl #1
    // 0xeb2e64: ldurh           w1, [x16, #0xf]
    // 0xeb2e68: cmp             x1, #0xa
    // 0xeb2e6c: b.gt            #0xeb2ea8
    // 0xeb2e70: lsl             x0, x1, #1
    // 0xeb2e74: cmp             w0, #0x14
    // 0xeb2e78: b.ne            #0xeb2f50
    // 0xeb2e7c: add             x0, x4, #1
    // 0xeb2e80: lsl             x1, x0, #1
    // 0xeb2e84: r16 = <String>
    //     0xeb2e84: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xeb2e88: stp             x2, x16, [SP, #0x10]
    // 0xeb2e8c: r16 = "\n"
    //     0xeb2e8c: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xeb2e90: stp             x1, x16, [SP]
    // 0xeb2e94: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xeb2e94: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xeb2e98: r0 = success()
    //     0xeb2e98: bl              #0xeb1004  ; [package:petitparser/src/core/context.dart] Context::success
    // 0xeb2e9c: LeaveFrame
    //     0xeb2e9c: mov             SP, fp
    //     0xeb2ea0: ldp             fp, lr, [SP], #0x10
    // 0xeb2ea4: ret
    //     0xeb2ea4: ret             
    // 0xeb2ea8: cmp             x1, #0xd
    // 0xeb2eac: b.lt            #0xeb2f50
    // 0xeb2eb0: lsl             x6, x1, #1
    // 0xeb2eb4: cmp             w6, #0x1a
    // 0xeb2eb8: b.ne            #0xeb2f50
    // 0xeb2ebc: add             x1, x4, #1
    // 0xeb2ec0: cmp             x1, x5
    // 0xeb2ec4: b.ge            #0xeb2f24
    // 0xeb2ec8: cmp             w0, #0xbc
    // 0xeb2ecc: b.ne            #0xeb2ee4
    // 0xeb2ed0: ArrayLoad: r0 = r3[r1]  ; TypedUnsigned_1
    //     0xeb2ed0: add             x16, x3, x1
    //     0xeb2ed4: ldrb            w0, [x16, #0xf]
    // 0xeb2ed8: cmp             x0, #0xa
    // 0xeb2edc: b.ne            #0xeb2f24
    // 0xeb2ee0: b               #0xeb2ef4
    // 0xeb2ee4: add             x16, x3, x1, lsl #1
    // 0xeb2ee8: ldurh           w0, [x16, #0xf]
    // 0xeb2eec: cmp             x0, #0xa
    // 0xeb2ef0: b.ne            #0xeb2f24
    // 0xeb2ef4: add             x0, x4, #2
    // 0xeb2ef8: lsl             x1, x0, #1
    // 0xeb2efc: r16 = <String>
    //     0xeb2efc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xeb2f00: stp             x2, x16, [SP, #0x10]
    // 0xeb2f04: r16 = "\r\n"
    //     0xeb2f04: add             x16, PP, #0x12, lsl #12  ; [pp+0x12260] "\r\n"
    //     0xeb2f08: ldr             x16, [x16, #0x260]
    // 0xeb2f0c: stp             x1, x16, [SP]
    // 0xeb2f10: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xeb2f10: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xeb2f14: r0 = success()
    //     0xeb2f14: bl              #0xeb1004  ; [package:petitparser/src/core/context.dart] Context::success
    // 0xeb2f18: LeaveFrame
    //     0xeb2f18: mov             SP, fp
    //     0xeb2f1c: ldp             fp, lr, [SP], #0x10
    // 0xeb2f20: ret
    //     0xeb2f20: ret             
    // 0xeb2f24: lsl             x0, x1, #1
    // 0xeb2f28: r16 = <String>
    //     0xeb2f28: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xeb2f2c: stp             x2, x16, [SP, #0x10]
    // 0xeb2f30: r16 = "\r"
    //     0xeb2f30: add             x16, PP, #0xc, lsl #12  ; [pp+0xc9a8] "\r"
    //     0xeb2f34: ldr             x16, [x16, #0x9a8]
    // 0xeb2f38: stp             x0, x16, [SP]
    // 0xeb2f3c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xeb2f3c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xeb2f40: r0 = success()
    //     0xeb2f40: bl              #0xeb1004  ; [package:petitparser/src/core/context.dart] Context::success
    // 0xeb2f44: LeaveFrame
    //     0xeb2f44: mov             SP, fp
    //     0xeb2f48: ldp             fp, lr, [SP], #0x10
    // 0xeb2f4c: ret
    //     0xeb2f4c: ret             
    // 0xeb2f50: r1 = <Never>
    //     0xeb2f50: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xeb2f54: r0 = Failure()
    //     0xeb2f54: bl              #0x75149c  ; AllocateFailureStub -> Failure (size=0x1c)
    // 0xeb2f58: r1 = "newline expected"
    //     0xeb2f58: add             x1, PP, #0x31, lsl #12  ; [pp+0x31488] "newline expected"
    //     0xeb2f5c: ldr             x1, [x1, #0x488]
    // 0xeb2f60: ArrayStore: r0[0] = r1  ; List_4
    //     0xeb2f60: stur            w1, [x0, #0x17]
    // 0xeb2f64: ldur            x1, [fp, #-0x10]
    // 0xeb2f68: StoreField: r0->field_7 = r1
    //     0xeb2f68: stur            w1, [x0, #7]
    // 0xeb2f6c: ldur            x1, [fp, #-8]
    // 0xeb2f70: StoreField: r0->field_b = r1
    //     0xeb2f70: stur            x1, [x0, #0xb]
    // 0xeb2f74: LeaveFrame
    //     0xeb2f74: mov             SP, fp
    //     0xeb2f78: ldp             fp, lr, [SP], #0x10
    // 0xeb2f7c: ret
    //     0xeb2f7c: ret             
    // 0xeb2f80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb2f80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb2f84: b               #0xeb2e0c
    // 0xeb2f88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb2f88: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
