// lib: , url: package:petitparser/src/parser/predicate/any.dart

// class id: 1050907, size: 0x8
class :: {

  static Parser<String> any() {
    // ** addr: 0x88ba98, size: 0x28
    // 0x88ba98: EnterFrame
    //     0x88ba98: stp             fp, lr, [SP, #-0x10]!
    //     0x88ba9c: mov             fp, SP
    // 0x88baa0: r1 = <String>
    //     0x88baa0: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88baa4: r0 = AnyCharacterParser()
    //     0x88baa4: bl              #0x88bac0  ; AllocateAnyCharacterParserStub -> AnyCharacterParser (size=0x10)
    // 0x88baa8: r1 = "input expected"
    //     0x88baa8: add             x1, PP, #0x26, lsl #12  ; [pp+0x267e8] "input expected"
    //     0x88baac: ldr             x1, [x1, #0x7e8]
    // 0x88bab0: StoreField: r0->field_b = r1
    //     0x88bab0: stur            w1, [x0, #0xb]
    // 0x88bab4: LeaveFrame
    //     0x88bab4: mov             SP, fp
    //     0x88bab8: ldp             fp, lr, [SP], #0x10
    // 0x88babc: ret
    //     0x88babc: ret             
  }
}

// class id: 731, size: 0x10, field offset: 0xc
class AnyCharacterParser extends Parser<dynamic> {

  _ toString(/* No info */) {
    // ** addr: 0xc3be34, size: 0x7c
    // 0xc3be34: EnterFrame
    //     0xc3be34: stp             fp, lr, [SP, #-0x10]!
    //     0xc3be38: mov             fp, SP
    // 0xc3be3c: AllocStack(0x10)
    //     0xc3be3c: sub             SP, SP, #0x10
    // 0xc3be40: CheckStackOverflow
    //     0xc3be40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3be44: cmp             SP, x16
    //     0xc3be48: b.ls            #0xc3bea8
    // 0xc3be4c: ldr             x16, [fp, #0x10]
    // 0xc3be50: str             x16, [SP]
    // 0xc3be54: r0 = toString()
    //     0xc3be54: bl              #0xc3c084  ; [package:petitparser/src/core/parser.dart] Parser::toString
    // 0xc3be58: r1 = Null
    //     0xc3be58: mov             x1, NULL
    // 0xc3be5c: r2 = 8
    //     0xc3be5c: movz            x2, #0x8
    // 0xc3be60: stur            x0, [fp, #-8]
    // 0xc3be64: r0 = AllocateArray()
    //     0xc3be64: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3be68: mov             x1, x0
    // 0xc3be6c: ldur            x0, [fp, #-8]
    // 0xc3be70: StoreField: r1->field_f = r0
    //     0xc3be70: stur            w0, [x1, #0xf]
    // 0xc3be74: r16 = "["
    //     0xc3be74: ldr             x16, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xc3be78: StoreField: r1->field_13 = r16
    //     0xc3be78: stur            w16, [x1, #0x13]
    // 0xc3be7c: ldr             x0, [fp, #0x10]
    // 0xc3be80: LoadField: r2 = r0->field_b
    //     0xc3be80: ldur            w2, [x0, #0xb]
    // 0xc3be84: DecompressPointer r2
    //     0xc3be84: add             x2, x2, HEAP, lsl #32
    // 0xc3be88: ArrayStore: r1[0] = r2  ; List_4
    //     0xc3be88: stur            w2, [x1, #0x17]
    // 0xc3be8c: r16 = "]"
    //     0xc3be8c: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc3be90: StoreField: r1->field_1b = r16
    //     0xc3be90: stur            w16, [x1, #0x1b]
    // 0xc3be94: str             x1, [SP]
    // 0xc3be98: r0 = _interpolate()
    //     0xc3be98: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3be9c: LeaveFrame
    //     0xc3be9c: mov             SP, fp
    //     0xc3bea0: ldp             fp, lr, [SP], #0x10
    // 0xc3bea4: ret
    //     0xc3bea4: ret             
    // 0xc3bea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3bea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3beac: b               #0xc3be4c
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb0758, size: 0x44
    // 0xeb0758: LoadField: r4 = r2->field_7
    //     0xeb0758: ldur            w4, [x2, #7]
    // 0xeb075c: r2 = LoadInt32Instr(r4)
    //     0xeb075c: sbfx            x2, x4, #1, #0x1f
    // 0xeb0760: cmp             x3, x2
    // 0xeb0764: b.ge            #0xeb0770
    // 0xeb0768: add             x2, x3, #1
    // 0xeb076c: b               #0xeb0774
    // 0xeb0770: r2 = -1
    //     0xeb0770: movn            x2, #0
    // 0xeb0774: r0 = BoxInt64Instr(r2)
    //     0xeb0774: sbfiz           x0, x2, #1, #0x1f
    //     0xeb0778: cmp             x2, x0, asr #1
    //     0xeb077c: b.eq            #0xeb0798
    //     0xeb0780: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0784: mov             fp, SP
    //     0xeb0788: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb078c: mov             SP, fp
    //     0xeb0790: ldp             fp, lr, [SP], #0x10
    //     0xeb0794: stur            x2, [x0, #7]
    // 0xeb0798: ret
    //     0xeb0798: ret             
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb2f8c, size: 0xdc
    // 0xeb2f8c: EnterFrame
    //     0xeb2f8c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb2f90: mov             fp, SP
    // 0xeb2f94: AllocStack(0x38)
    //     0xeb2f94: sub             SP, SP, #0x38
    // 0xeb2f98: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xeb2f98: stur            x2, [fp, #-0x10]
    // 0xeb2f9c: CheckStackOverflow
    //     0xeb2f9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb2fa0: cmp             SP, x16
    //     0xeb2fa4: b.ls            #0xeb3060
    // 0xeb2fa8: LoadField: r3 = r2->field_7
    //     0xeb2fa8: ldur            w3, [x2, #7]
    // 0xeb2fac: DecompressPointer r3
    //     0xeb2fac: add             x3, x3, HEAP, lsl #32
    // 0xeb2fb0: stur            x3, [fp, #-0x18]
    // 0xeb2fb4: LoadField: r4 = r2->field_b
    //     0xeb2fb4: ldur            x4, [x2, #0xb]
    // 0xeb2fb8: stur            x4, [fp, #-8]
    // 0xeb2fbc: LoadField: r0 = r3->field_7
    //     0xeb2fbc: ldur            w0, [x3, #7]
    // 0xeb2fc0: r1 = LoadInt32Instr(r0)
    //     0xeb2fc0: sbfx            x1, x0, #1, #0x1f
    // 0xeb2fc4: cmp             x4, x1
    // 0xeb2fc8: b.ge            #0xeb302c
    // 0xeb2fcc: r0 = BoxInt64Instr(r4)
    //     0xeb2fcc: sbfiz           x0, x4, #1, #0x1f
    //     0xeb2fd0: cmp             x4, x0, asr #1
    //     0xeb2fd4: b.eq            #0xeb2fe0
    //     0xeb2fd8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb2fdc: stur            x4, [x0, #7]
    // 0xeb2fe0: stp             x0, x3, [SP]
    // 0xeb2fe4: r0 = []()
    //     0xeb2fe4: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xeb2fe8: mov             x2, x0
    // 0xeb2fec: ldur            x0, [fp, #-8]
    // 0xeb2ff0: add             x3, x0, #1
    // 0xeb2ff4: r0 = BoxInt64Instr(r3)
    //     0xeb2ff4: sbfiz           x0, x3, #1, #0x1f
    //     0xeb2ff8: cmp             x3, x0, asr #1
    //     0xeb2ffc: b.eq            #0xeb3008
    //     0xeb3000: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3004: stur            x3, [x0, #7]
    // 0xeb3008: r16 = <String>
    //     0xeb3008: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xeb300c: ldur            lr, [fp, #-0x10]
    // 0xeb3010: stp             lr, x16, [SP, #0x10]
    // 0xeb3014: stp             x0, x2, [SP]
    // 0xeb3018: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xeb3018: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xeb301c: r0 = success()
    //     0xeb301c: bl              #0xeb1004  ; [package:petitparser/src/core/context.dart] Context::success
    // 0xeb3020: LeaveFrame
    //     0xeb3020: mov             SP, fp
    //     0xeb3024: ldp             fp, lr, [SP], #0x10
    // 0xeb3028: ret
    //     0xeb3028: ret             
    // 0xeb302c: mov             x0, x4
    // 0xeb3030: r1 = <Never>
    //     0xeb3030: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xeb3034: r0 = Failure()
    //     0xeb3034: bl              #0x75149c  ; AllocateFailureStub -> Failure (size=0x1c)
    // 0xeb3038: r1 = "input expected"
    //     0xeb3038: add             x1, PP, #0x26, lsl #12  ; [pp+0x267e8] "input expected"
    //     0xeb303c: ldr             x1, [x1, #0x7e8]
    // 0xeb3040: ArrayStore: r0[0] = r1  ; List_4
    //     0xeb3040: stur            w1, [x0, #0x17]
    // 0xeb3044: ldur            x1, [fp, #-0x18]
    // 0xeb3048: StoreField: r0->field_7 = r1
    //     0xeb3048: stur            w1, [x0, #7]
    // 0xeb304c: ldur            x1, [fp, #-8]
    // 0xeb3050: StoreField: r0->field_b = r1
    //     0xeb3050: stur            x1, [x0, #0xb]
    // 0xeb3054: LeaveFrame
    //     0xeb3054: mov             SP, fp
    //     0xeb3058: ldp             fp, lr, [SP], #0x10
    // 0xeb305c: ret
    //     0xeb305c: ret             
    // 0xeb3060: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb3060: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb3064: b               #0xeb2fa8
  }
}
