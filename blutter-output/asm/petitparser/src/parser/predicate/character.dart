// lib: , url: package:petitparser/src/parser/predicate/character.dart

// class id: 1050908, size: 0x8
class :: {
}

// class id: 730, size: 0x14, field offset: 0xc
class SingleCharacterParser extends Parser<dynamic> {

  _ toString(/* No info */) {
    // ** addr: 0xc3beb0, size: 0x7c
    // 0xc3beb0: EnterFrame
    //     0xc3beb0: stp             fp, lr, [SP, #-0x10]!
    //     0xc3beb4: mov             fp, SP
    // 0xc3beb8: AllocStack(0x10)
    //     0xc3beb8: sub             SP, SP, #0x10
    // 0xc3bebc: CheckStackOverflow
    //     0xc3bebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3bec0: cmp             SP, x16
    //     0xc3bec4: b.ls            #0xc3bf24
    // 0xc3bec8: ldr             x16, [fp, #0x10]
    // 0xc3becc: str             x16, [SP]
    // 0xc3bed0: r0 = toString()
    //     0xc3bed0: bl              #0xc3c084  ; [package:petitparser/src/core/parser.dart] Parser::toString
    // 0xc3bed4: r1 = Null
    //     0xc3bed4: mov             x1, NULL
    // 0xc3bed8: r2 = 8
    //     0xc3bed8: movz            x2, #0x8
    // 0xc3bedc: stur            x0, [fp, #-8]
    // 0xc3bee0: r0 = AllocateArray()
    //     0xc3bee0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3bee4: mov             x1, x0
    // 0xc3bee8: ldur            x0, [fp, #-8]
    // 0xc3beec: StoreField: r1->field_f = r0
    //     0xc3beec: stur            w0, [x1, #0xf]
    // 0xc3bef0: r16 = "["
    //     0xc3bef0: ldr             x16, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xc3bef4: StoreField: r1->field_13 = r16
    //     0xc3bef4: stur            w16, [x1, #0x13]
    // 0xc3bef8: ldr             x0, [fp, #0x10]
    // 0xc3befc: LoadField: r2 = r0->field_f
    //     0xc3befc: ldur            w2, [x0, #0xf]
    // 0xc3bf00: DecompressPointer r2
    //     0xc3bf00: add             x2, x2, HEAP, lsl #32
    // 0xc3bf04: ArrayStore: r1[0] = r2  ; List_4
    //     0xc3bf04: stur            w2, [x1, #0x17]
    // 0xc3bf08: r16 = "]"
    //     0xc3bf08: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc3bf0c: StoreField: r1->field_1b = r16
    //     0xc3bf0c: stur            w16, [x1, #0x1b]
    // 0xc3bf10: str             x1, [SP]
    // 0xc3bf14: r0 = _interpolate()
    //     0xc3bf14: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3bf18: LeaveFrame
    //     0xc3bf18: mov             SP, fp
    //     0xc3bf1c: ldp             fp, lr, [SP], #0x10
    // 0xc3bf20: ret
    //     0xc3bf20: ret             
    // 0xc3bf24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3bf24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3bf28: b               #0xc3bec8
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb079c, size: 0xc0
    // 0xeb079c: EnterFrame
    //     0xeb079c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb07a0: mov             fp, SP
    // 0xeb07a4: AllocStack(0x8)
    //     0xeb07a4: sub             SP, SP, #8
    // 0xeb07a8: SetupParameters(dynamic _ /* r3 => r3, fp-0x8 */)
    //     0xeb07a8: stur            x3, [fp, #-8]
    // 0xeb07ac: CheckStackOverflow
    //     0xeb07ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb07b0: cmp             SP, x16
    //     0xeb07b4: b.ls            #0xeb0850
    // 0xeb07b8: LoadField: r0 = r2->field_7
    //     0xeb07b8: ldur            w0, [x2, #7]
    // 0xeb07bc: r4 = LoadInt32Instr(r0)
    //     0xeb07bc: sbfx            x4, x0, #1, #0x1f
    // 0xeb07c0: cmp             x3, x4
    // 0xeb07c4: b.ge            #0xeb083c
    // 0xeb07c8: LoadField: r5 = r1->field_b
    //     0xeb07c8: ldur            w5, [x1, #0xb]
    // 0xeb07cc: DecompressPointer r5
    //     0xeb07cc: add             x5, x5, HEAP, lsl #32
    // 0xeb07d0: mov             x0, x4
    // 0xeb07d4: mov             x1, x3
    // 0xeb07d8: cmp             x1, x0
    // 0xeb07dc: b.hs            #0xeb0858
    // 0xeb07e0: r0 = LoadClassIdInstr(r2)
    //     0xeb07e0: ldur            x0, [x2, #-1]
    //     0xeb07e4: ubfx            x0, x0, #0xc, #0x14
    // 0xeb07e8: lsl             x0, x0, #1
    // 0xeb07ec: cmp             w0, #0xbc
    // 0xeb07f0: b.ne            #0xeb0804
    // 0xeb07f4: ArrayLoad: r0 = r2[r3]  ; TypedUnsigned_1
    //     0xeb07f4: add             x16, x2, x3
    //     0xeb07f8: ldrb            w0, [x16, #0xf]
    // 0xeb07fc: mov             x2, x0
    // 0xeb0800: b               #0xeb0810
    // 0xeb0804: add             x16, x2, x3, lsl #1
    // 0xeb0808: ldurh           w0, [x16, #0xf]
    // 0xeb080c: mov             x2, x0
    // 0xeb0810: r0 = LoadClassIdInstr(r5)
    //     0xeb0810: ldur            x0, [x5, #-1]
    //     0xeb0814: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0818: mov             x1, x5
    // 0xeb081c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb081c: sub             lr, x0, #1, lsl #12
    //     0xeb0820: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0824: blr             lr
    // 0xeb0828: tbnz            w0, #4, #0xeb083c
    // 0xeb082c: ldur            x1, [fp, #-8]
    // 0xeb0830: add             x2, x1, #1
    // 0xeb0834: mov             x1, x2
    // 0xeb0838: b               #0xeb0840
    // 0xeb083c: r1 = -1
    //     0xeb083c: movn            x1, #0
    // 0xeb0840: lsl             x0, x1, #1
    // 0xeb0844: LeaveFrame
    //     0xeb0844: mov             SP, fp
    //     0xeb0848: ldp             fp, lr, [SP], #0x10
    // 0xeb084c: ret
    //     0xeb084c: ret             
    // 0xeb0850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0854: b               #0xeb07b8
    // 0xeb0858: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb0858: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb3068, size: 0x16c
    // 0xeb3068: EnterFrame
    //     0xeb3068: stp             fp, lr, [SP, #-0x10]!
    //     0xeb306c: mov             fp, SP
    // 0xeb3070: AllocStack(0x40)
    //     0xeb3070: sub             SP, SP, #0x40
    // 0xeb3074: SetupParameters(SingleCharacterParser this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */)
    //     0xeb3074: mov             x4, x1
    //     0xeb3078: mov             x3, x2
    //     0xeb307c: stur            x1, [fp, #-0x18]
    //     0xeb3080: stur            x2, [fp, #-0x20]
    // 0xeb3084: CheckStackOverflow
    //     0xeb3084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb3088: cmp             SP, x16
    //     0xeb308c: b.ls            #0xeb31c8
    // 0xeb3090: LoadField: r5 = r3->field_7
    //     0xeb3090: ldur            w5, [x3, #7]
    // 0xeb3094: DecompressPointer r5
    //     0xeb3094: add             x5, x5, HEAP, lsl #32
    // 0xeb3098: stur            x5, [fp, #-0x10]
    // 0xeb309c: LoadField: r6 = r3->field_b
    //     0xeb309c: ldur            x6, [x3, #0xb]
    // 0xeb30a0: stur            x6, [fp, #-8]
    // 0xeb30a4: LoadField: r0 = r5->field_7
    //     0xeb30a4: ldur            w0, [x5, #7]
    // 0xeb30a8: r1 = LoadInt32Instr(r0)
    //     0xeb30a8: sbfx            x1, x0, #1, #0x1f
    // 0xeb30ac: cmp             x6, x1
    // 0xeb30b0: b.ge            #0xeb3184
    // 0xeb30b4: LoadField: r2 = r4->field_b
    //     0xeb30b4: ldur            w2, [x4, #0xb]
    // 0xeb30b8: DecompressPointer r2
    //     0xeb30b8: add             x2, x2, HEAP, lsl #32
    // 0xeb30bc: mov             x0, x1
    // 0xeb30c0: mov             x1, x6
    // 0xeb30c4: cmp             x1, x0
    // 0xeb30c8: b.hs            #0xeb31d0
    // 0xeb30cc: r0 = LoadClassIdInstr(r5)
    //     0xeb30cc: ldur            x0, [x5, #-1]
    //     0xeb30d0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb30d4: lsl             x0, x0, #1
    // 0xeb30d8: cmp             w0, #0xbc
    // 0xeb30dc: b.ne            #0xeb30ec
    // 0xeb30e0: ArrayLoad: r0 = r5[r6]  ; TypedUnsigned_1
    //     0xeb30e0: add             x16, x5, x6
    //     0xeb30e4: ldrb            w0, [x16, #0xf]
    // 0xeb30e8: b               #0xeb30f4
    // 0xeb30ec: add             x16, x5, x6, lsl #1
    // 0xeb30f0: ldurh           w0, [x16, #0xf]
    // 0xeb30f4: r1 = LoadClassIdInstr(r2)
    //     0xeb30f4: ldur            x1, [x2, #-1]
    //     0xeb30f8: ubfx            x1, x1, #0xc, #0x14
    // 0xeb30fc: mov             x16, x2
    // 0xeb3100: mov             x2, x1
    // 0xeb3104: mov             x1, x16
    // 0xeb3108: mov             x16, x0
    // 0xeb310c: mov             x0, x2
    // 0xeb3110: mov             x2, x16
    // 0xeb3114: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb3114: sub             lr, x0, #1, lsl #12
    //     0xeb3118: ldr             lr, [x21, lr, lsl #3]
    //     0xeb311c: blr             lr
    // 0xeb3120: tbnz            w0, #4, #0xeb317c
    // 0xeb3124: ldur            x2, [fp, #-8]
    // 0xeb3128: r0 = BoxInt64Instr(r2)
    //     0xeb3128: sbfiz           x0, x2, #1, #0x1f
    //     0xeb312c: cmp             x2, x0, asr #1
    //     0xeb3130: b.eq            #0xeb313c
    //     0xeb3134: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3138: stur            x2, [x0, #7]
    // 0xeb313c: ldur            x16, [fp, #-0x10]
    // 0xeb3140: stp             x0, x16, [SP]
    // 0xeb3144: r0 = []()
    //     0xeb3144: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xeb3148: mov             x1, x0
    // 0xeb314c: ldur            x0, [fp, #-8]
    // 0xeb3150: add             x2, x0, #1
    // 0xeb3154: lsl             x0, x2, #1
    // 0xeb3158: r16 = <String>
    //     0xeb3158: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xeb315c: ldur            lr, [fp, #-0x20]
    // 0xeb3160: stp             lr, x16, [SP, #0x10]
    // 0xeb3164: stp             x0, x1, [SP]
    // 0xeb3168: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xeb3168: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xeb316c: r0 = success()
    //     0xeb316c: bl              #0xeb1004  ; [package:petitparser/src/core/context.dart] Context::success
    // 0xeb3170: LeaveFrame
    //     0xeb3170: mov             SP, fp
    //     0xeb3174: ldp             fp, lr, [SP], #0x10
    // 0xeb3178: ret
    //     0xeb3178: ret             
    // 0xeb317c: ldur            x0, [fp, #-8]
    // 0xeb3180: b               #0xeb3188
    // 0xeb3184: mov             x0, x6
    // 0xeb3188: ldur            x1, [fp, #-0x18]
    // 0xeb318c: ldur            x2, [fp, #-0x10]
    // 0xeb3190: LoadField: r3 = r1->field_f
    //     0xeb3190: ldur            w3, [x1, #0xf]
    // 0xeb3194: DecompressPointer r3
    //     0xeb3194: add             x3, x3, HEAP, lsl #32
    // 0xeb3198: stur            x3, [fp, #-0x20]
    // 0xeb319c: r1 = <Never>
    //     0xeb319c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xeb31a0: r0 = Failure()
    //     0xeb31a0: bl              #0x75149c  ; AllocateFailureStub -> Failure (size=0x1c)
    // 0xeb31a4: ldur            x1, [fp, #-0x20]
    // 0xeb31a8: ArrayStore: r0[0] = r1  ; List_4
    //     0xeb31a8: stur            w1, [x0, #0x17]
    // 0xeb31ac: ldur            x1, [fp, #-0x10]
    // 0xeb31b0: StoreField: r0->field_7 = r1
    //     0xeb31b0: stur            w1, [x0, #7]
    // 0xeb31b4: ldur            x1, [fp, #-8]
    // 0xeb31b8: StoreField: r0->field_b = r1
    //     0xeb31b8: stur            x1, [x0, #0xb]
    // 0xeb31bc: LeaveFrame
    //     0xeb31bc: mov             SP, fp
    //     0xeb31c0: ldp             fp, lr, [SP], #0x10
    // 0xeb31c4: ret
    //     0xeb31c4: ret             
    // 0xeb31c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb31c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb31cc: b               #0xeb3090
    // 0xeb31d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb31d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
