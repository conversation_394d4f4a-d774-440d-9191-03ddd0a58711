// lib: , url: package:petitparser/src/parser/predicate/predicate.dart

// class id: 1050909, size: 0x8
class :: {

  static _ predicate(/* No info */) {
    // ** addr: 0x88b010, size: 0x48
    // 0x88b010: EnterFrame
    //     0x88b010: stp             fp, lr, [SP, #-0x10]!
    //     0x88b014: mov             fp, SP
    // 0x88b018: AllocStack(0x18)
    //     0x88b018: sub             SP, SP, #0x18
    // 0x88b01c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x88b01c: mov             x0, x1
    //     0x88b020: stur            x1, [fp, #-8]
    //     0x88b024: stur            x2, [fp, #-0x10]
    //     0x88b028: stur            x3, [fp, #-0x18]
    // 0x88b02c: r1 = <String>
    //     0x88b02c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88b030: r0 = PredicateParser()
    //     0x88b030: bl              #0x88b058  ; AllocatePredicateParserStub -> PredicateParser (size=0x1c)
    // 0x88b034: ldur            x1, [fp, #-8]
    // 0x88b038: StoreField: r0->field_b = r1
    //     0x88b038: stur            x1, [x0, #0xb]
    // 0x88b03c: ldur            x1, [fp, #-0x10]
    // 0x88b040: StoreField: r0->field_13 = r1
    //     0x88b040: stur            w1, [x0, #0x13]
    // 0x88b044: ldur            x1, [fp, #-0x18]
    // 0x88b048: ArrayStore: r0[0] = r1  ; List_4
    //     0x88b048: stur            w1, [x0, #0x17]
    // 0x88b04c: LeaveFrame
    //     0x88b04c: mov             SP, fp
    //     0x88b050: ldp             fp, lr, [SP], #0x10
    // 0x88b054: ret
    //     0x88b054: ret             
  }
}

// class id: 729, size: 0x1c, field offset: 0xc
class PredicateParser extends Parser<dynamic> {

  _ toString(/* No info */) {
    // ** addr: 0xc3bf2c, size: 0x7c
    // 0xc3bf2c: EnterFrame
    //     0xc3bf2c: stp             fp, lr, [SP, #-0x10]!
    //     0xc3bf30: mov             fp, SP
    // 0xc3bf34: AllocStack(0x10)
    //     0xc3bf34: sub             SP, SP, #0x10
    // 0xc3bf38: CheckStackOverflow
    //     0xc3bf38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3bf3c: cmp             SP, x16
    //     0xc3bf40: b.ls            #0xc3bfa0
    // 0xc3bf44: ldr             x16, [fp, #0x10]
    // 0xc3bf48: str             x16, [SP]
    // 0xc3bf4c: r0 = toString()
    //     0xc3bf4c: bl              #0xc3c084  ; [package:petitparser/src/core/parser.dart] Parser::toString
    // 0xc3bf50: r1 = Null
    //     0xc3bf50: mov             x1, NULL
    // 0xc3bf54: r2 = 8
    //     0xc3bf54: movz            x2, #0x8
    // 0xc3bf58: stur            x0, [fp, #-8]
    // 0xc3bf5c: r0 = AllocateArray()
    //     0xc3bf5c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3bf60: mov             x1, x0
    // 0xc3bf64: ldur            x0, [fp, #-8]
    // 0xc3bf68: StoreField: r1->field_f = r0
    //     0xc3bf68: stur            w0, [x1, #0xf]
    // 0xc3bf6c: r16 = "["
    //     0xc3bf6c: ldr             x16, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xc3bf70: StoreField: r1->field_13 = r16
    //     0xc3bf70: stur            w16, [x1, #0x13]
    // 0xc3bf74: ldr             x0, [fp, #0x10]
    // 0xc3bf78: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc3bf78: ldur            w2, [x0, #0x17]
    // 0xc3bf7c: DecompressPointer r2
    //     0xc3bf7c: add             x2, x2, HEAP, lsl #32
    // 0xc3bf80: ArrayStore: r1[0] = r2  ; List_4
    //     0xc3bf80: stur            w2, [x1, #0x17]
    // 0xc3bf84: r16 = "]"
    //     0xc3bf84: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc3bf88: StoreField: r1->field_1b = r16
    //     0xc3bf88: stur            w16, [x1, #0x1b]
    // 0xc3bf8c: str             x1, [SP]
    // 0xc3bf90: r0 = _interpolate()
    //     0xc3bf90: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3bf94: LeaveFrame
    //     0xc3bf94: mov             SP, fp
    //     0xc3bf98: ldp             fp, lr, [SP], #0x10
    // 0xc3bf9c: ret
    //     0xc3bf9c: ret             
    // 0xc3bfa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3bfa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3bfa4: b               #0xc3bf44
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb085c, size: 0xd0
    // 0xeb085c: EnterFrame
    //     0xeb085c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0860: mov             fp, SP
    // 0xeb0864: AllocStack(0x20)
    //     0xeb0864: sub             SP, SP, #0x20
    // 0xeb0868: SetupParameters(PredicateParser this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3 */, dynamic _ /* r3 => r2 */)
    //     0xeb0868: mov             x4, x1
    //     0xeb086c: mov             x16, x3
    //     0xeb0870: mov             x3, x2
    //     0xeb0874: mov             x2, x16
    //     0xeb0878: stur            x1, [fp, #-0x10]
    // 0xeb087c: CheckStackOverflow
    //     0xeb087c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0880: cmp             SP, x16
    //     0xeb0884: b.ls            #0xeb0924
    // 0xeb0888: LoadField: r0 = r4->field_b
    //     0xeb0888: ldur            x0, [x4, #0xb]
    // 0xeb088c: add             x5, x2, x0
    // 0xeb0890: stur            x5, [fp, #-8]
    // 0xeb0894: LoadField: r0 = r3->field_7
    //     0xeb0894: ldur            w0, [x3, #7]
    // 0xeb0898: r1 = LoadInt32Instr(r0)
    //     0xeb0898: sbfx            x1, x0, #1, #0x1f
    // 0xeb089c: cmp             x5, x1
    // 0xeb08a0: b.gt            #0xeb0900
    // 0xeb08a4: r0 = BoxInt64Instr(r5)
    //     0xeb08a4: sbfiz           x0, x5, #1, #0x1f
    //     0xeb08a8: cmp             x5, x0, asr #1
    //     0xeb08ac: b.eq            #0xeb08b8
    //     0xeb08b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb08b4: stur            x5, [x0, #7]
    // 0xeb08b8: str             x0, [SP]
    // 0xeb08bc: mov             x1, x3
    // 0xeb08c0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb08c0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb08c4: r0 = substring()
    //     0xeb08c4: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xeb08c8: mov             x1, x0
    // 0xeb08cc: ldur            x0, [fp, #-0x10]
    // 0xeb08d0: LoadField: r2 = r0->field_13
    //     0xeb08d0: ldur            w2, [x0, #0x13]
    // 0xeb08d4: DecompressPointer r2
    //     0xeb08d4: add             x2, x2, HEAP, lsl #32
    // 0xeb08d8: stp             x1, x2, [SP]
    // 0xeb08dc: mov             x0, x2
    // 0xeb08e0: ClosureCall
    //     0xeb08e0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeb08e4: ldur            x2, [x0, #0x1f]
    //     0xeb08e8: blr             x2
    // 0xeb08ec: r16 = true
    //     0xeb08ec: add             x16, NULL, #0x20  ; true
    // 0xeb08f0: cmp             w0, w16
    // 0xeb08f4: b.ne            #0xeb0900
    // 0xeb08f8: ldur            x2, [fp, #-8]
    // 0xeb08fc: b               #0xeb0904
    // 0xeb0900: r2 = -1
    //     0xeb0900: movn            x2, #0
    // 0xeb0904: r0 = BoxInt64Instr(r2)
    //     0xeb0904: sbfiz           x0, x2, #1, #0x1f
    //     0xeb0908: cmp             x2, x0, asr #1
    //     0xeb090c: b.eq            #0xeb0918
    //     0xeb0910: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb0914: stur            x2, [x0, #7]
    // 0xeb0918: LeaveFrame
    //     0xeb0918: mov             SP, fp
    //     0xeb091c: ldp             fp, lr, [SP], #0x10
    // 0xeb0920: ret
    //     0xeb0920: ret             
    // 0xeb0924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0928: b               #0xeb0888
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb31d4, size: 0x128
    // 0xeb31d4: EnterFrame
    //     0xeb31d4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb31d8: mov             fp, SP
    // 0xeb31dc: AllocStack(0x50)
    //     0xeb31dc: sub             SP, SP, #0x50
    // 0xeb31e0: SetupParameters(PredicateParser this /* r1 => r4, fp-0x20 */, dynamic _ /* r2 => r3, fp-0x28 */)
    //     0xeb31e0: mov             x4, x1
    //     0xeb31e4: mov             x3, x2
    //     0xeb31e8: stur            x1, [fp, #-0x20]
    //     0xeb31ec: stur            x2, [fp, #-0x28]
    // 0xeb31f0: CheckStackOverflow
    //     0xeb31f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb31f4: cmp             SP, x16
    //     0xeb31f8: b.ls            #0xeb32f4
    // 0xeb31fc: LoadField: r5 = r3->field_b
    //     0xeb31fc: ldur            x5, [x3, #0xb]
    // 0xeb3200: stur            x5, [fp, #-0x18]
    // 0xeb3204: LoadField: r0 = r4->field_b
    //     0xeb3204: ldur            x0, [x4, #0xb]
    // 0xeb3208: add             x2, x5, x0
    // 0xeb320c: LoadField: r6 = r3->field_7
    //     0xeb320c: ldur            w6, [x3, #7]
    // 0xeb3210: DecompressPointer r6
    //     0xeb3210: add             x6, x6, HEAP, lsl #32
    // 0xeb3214: stur            x6, [fp, #-0x10]
    // 0xeb3218: LoadField: r0 = r6->field_7
    //     0xeb3218: ldur            w0, [x6, #7]
    // 0xeb321c: r1 = LoadInt32Instr(r0)
    //     0xeb321c: sbfx            x1, x0, #1, #0x1f
    // 0xeb3220: cmp             x2, x1
    // 0xeb3224: b.gt            #0xeb32b0
    // 0xeb3228: r0 = BoxInt64Instr(r2)
    //     0xeb3228: sbfiz           x0, x2, #1, #0x1f
    //     0xeb322c: cmp             x2, x0, asr #1
    //     0xeb3230: b.eq            #0xeb323c
    //     0xeb3234: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3238: stur            x2, [x0, #7]
    // 0xeb323c: stur            x0, [fp, #-8]
    // 0xeb3240: str             x0, [SP]
    // 0xeb3244: mov             x1, x6
    // 0xeb3248: mov             x2, x5
    // 0xeb324c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb324c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb3250: r0 = substring()
    //     0xeb3250: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xeb3254: mov             x2, x0
    // 0xeb3258: ldur            x1, [fp, #-0x20]
    // 0xeb325c: stur            x2, [fp, #-0x30]
    // 0xeb3260: LoadField: r0 = r1->field_13
    //     0xeb3260: ldur            w0, [x1, #0x13]
    // 0xeb3264: DecompressPointer r0
    //     0xeb3264: add             x0, x0, HEAP, lsl #32
    // 0xeb3268: stp             x2, x0, [SP]
    // 0xeb326c: ClosureCall
    //     0xeb326c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeb3270: ldur            x2, [x0, #0x1f]
    //     0xeb3274: blr             x2
    // 0xeb3278: r16 = true
    //     0xeb3278: add             x16, NULL, #0x20  ; true
    // 0xeb327c: cmp             w0, w16
    // 0xeb3280: b.ne            #0xeb32b0
    // 0xeb3284: r16 = <String>
    //     0xeb3284: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xeb3288: ldur            lr, [fp, #-0x28]
    // 0xeb328c: stp             lr, x16, [SP, #0x10]
    // 0xeb3290: ldur            x16, [fp, #-0x30]
    // 0xeb3294: ldur            lr, [fp, #-8]
    // 0xeb3298: stp             lr, x16, [SP]
    // 0xeb329c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xeb329c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xeb32a0: r0 = success()
    //     0xeb32a0: bl              #0xeb1004  ; [package:petitparser/src/core/context.dart] Context::success
    // 0xeb32a4: LeaveFrame
    //     0xeb32a4: mov             SP, fp
    //     0xeb32a8: ldp             fp, lr, [SP], #0x10
    // 0xeb32ac: ret
    //     0xeb32ac: ret             
    // 0xeb32b0: ldur            x0, [fp, #-0x20]
    // 0xeb32b4: ldur            x2, [fp, #-0x18]
    // 0xeb32b8: ldur            x3, [fp, #-0x10]
    // 0xeb32bc: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xeb32bc: ldur            w4, [x0, #0x17]
    // 0xeb32c0: DecompressPointer r4
    //     0xeb32c0: add             x4, x4, HEAP, lsl #32
    // 0xeb32c4: stur            x4, [fp, #-8]
    // 0xeb32c8: r1 = <Never>
    //     0xeb32c8: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xeb32cc: r0 = Failure()
    //     0xeb32cc: bl              #0x75149c  ; AllocateFailureStub -> Failure (size=0x1c)
    // 0xeb32d0: ldur            x1, [fp, #-8]
    // 0xeb32d4: ArrayStore: r0[0] = r1  ; List_4
    //     0xeb32d4: stur            w1, [x0, #0x17]
    // 0xeb32d8: ldur            x1, [fp, #-0x10]
    // 0xeb32dc: StoreField: r0->field_7 = r1
    //     0xeb32dc: stur            w1, [x0, #7]
    // 0xeb32e0: ldur            x1, [fp, #-0x18]
    // 0xeb32e4: StoreField: r0->field_b = r1
    //     0xeb32e4: stur            x1, [x0, #0xb]
    // 0xeb32e8: LeaveFrame
    //     0xeb32e8: mov             SP, fp
    //     0xeb32ec: ldp             fp, lr, [SP], #0x10
    // 0xeb32f0: ret
    //     0xeb32f0: ret             
    // 0xeb32f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb32f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb32f8: b               #0xeb31fc
  }
}
