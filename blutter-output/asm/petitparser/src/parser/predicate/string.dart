// lib: , url: package:petitparser/src/parser/predicate/string.dart

// class id: 1050910, size: 0x8
class :: {

  static _ PredicateStringExtension.toParser(/* No info */) {
    // ** addr: 0x88aef0, size: 0x6c
    // 0x88aef0: EnterFrame
    //     0x88aef0: stp             fp, lr, [SP, #-0x10]!
    //     0x88aef4: mov             fp, SP
    // 0x88aef8: AllocStack(0x10)
    //     0x88aef8: sub             SP, SP, #0x10
    // 0x88aefc: CheckStackOverflow
    //     0x88aefc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88af00: cmp             SP, x16
    //     0x88af04: b.ls            #0x88af54
    // 0x88af08: LoadField: r0 = r1->field_7
    //     0x88af08: ldur            w0, [x1, #7]
    // 0x88af0c: cbnz            w0, #0x88af2c
    // 0x88af10: r16 = <String>
    //     0x88af10: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88af14: stp             x1, x16, [SP]
    // 0x88af18: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88af18: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88af1c: r0 = epsilonWith()
    //     0x88af1c: bl              #0x88aea4  ; [package:petitparser/src/parser/misc/epsilon.dart] ::epsilonWith
    // 0x88af20: LeaveFrame
    //     0x88af20: mov             SP, fp
    //     0x88af24: ldp             fp, lr, [SP], #0x10
    // 0x88af28: ret
    //     0x88af28: ret             
    // 0x88af2c: cmp             w0, #2
    // 0x88af30: b.ne            #0x88af44
    // 0x88af34: r0 = char()
    //     0x88af34: bl              #0x88b140  ; [package:petitparser/src/parser/character/char.dart] ::char
    // 0x88af38: LeaveFrame
    //     0x88af38: mov             SP, fp
    //     0x88af3c: ldp             fp, lr, [SP], #0x10
    // 0x88af40: ret
    //     0x88af40: ret             
    // 0x88af44: r0 = string()
    //     0x88af44: bl              #0x88af5c  ; [package:petitparser/src/parser/predicate/string.dart] ::string
    // 0x88af48: LeaveFrame
    //     0x88af48: mov             SP, fp
    //     0x88af4c: ldp             fp, lr, [SP], #0x10
    // 0x88af50: ret
    //     0x88af50: ret             
    // 0x88af54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88af54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88af58: b               #0x88af08
  }
  static _ string(/* No info */) {
    // ** addr: 0x88af5c, size: 0xb4
    // 0x88af5c: EnterFrame
    //     0x88af5c: stp             fp, lr, [SP, #-0x10]!
    //     0x88af60: mov             fp, SP
    // 0x88af64: AllocStack(0x28)
    //     0x88af64: sub             SP, SP, #0x28
    // 0x88af68: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x88af68: stur            x1, [fp, #-8]
    // 0x88af6c: CheckStackOverflow
    //     0x88af6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88af70: cmp             SP, x16
    //     0x88af74: b.ls            #0x88b008
    // 0x88af78: r1 = 1
    //     0x88af78: movz            x1, #0x1
    // 0x88af7c: r0 = AllocateContext()
    //     0x88af7c: bl              #0xec126c  ; AllocateContextStub
    // 0x88af80: mov             x3, x0
    // 0x88af84: ldur            x0, [fp, #-8]
    // 0x88af88: stur            x3, [fp, #-0x18]
    // 0x88af8c: StoreField: r3->field_f = r0
    //     0x88af8c: stur            w0, [x3, #0xf]
    // 0x88af90: LoadField: r4 = r0->field_7
    //     0x88af90: ldur            w4, [x0, #7]
    // 0x88af94: stur            x4, [fp, #-0x10]
    // 0x88af98: r1 = Null
    //     0x88af98: mov             x1, NULL
    // 0x88af9c: r2 = 6
    //     0x88af9c: movz            x2, #0x6
    // 0x88afa0: r0 = AllocateArray()
    //     0x88afa0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88afa4: r16 = "\""
    //     0x88afa4: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x88afa8: StoreField: r0->field_f = r16
    //     0x88afa8: stur            w16, [x0, #0xf]
    // 0x88afac: ldur            x1, [fp, #-8]
    // 0x88afb0: StoreField: r0->field_13 = r1
    //     0x88afb0: stur            w1, [x0, #0x13]
    // 0x88afb4: r16 = "\" expected"
    //     0x88afb4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26938] "\" expected"
    //     0x88afb8: ldr             x16, [x16, #0x938]
    // 0x88afbc: ArrayStore: r0[0] = r16  ; List_4
    //     0x88afbc: stur            w16, [x0, #0x17]
    // 0x88afc0: str             x0, [SP]
    // 0x88afc4: r0 = _interpolate()
    //     0x88afc4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x88afc8: mov             x3, x0
    // 0x88afcc: ldur            x0, [fp, #-0x10]
    // 0x88afd0: stur            x3, [fp, #-8]
    // 0x88afd4: r4 = LoadInt32Instr(r0)
    //     0x88afd4: sbfx            x4, x0, #1, #0x1f
    // 0x88afd8: ldur            x2, [fp, #-0x18]
    // 0x88afdc: stur            x4, [fp, #-0x20]
    // 0x88afe0: r1 = Function '<anonymous closure>': static.
    //     0x88afe0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a48] AnonymousClosure: static (0x88b0f4), in [package:petitparser/src/parser/predicate/string.dart] ::string (0x88af5c)
    //     0x88afe4: ldr             x1, [x1, #0xa48]
    // 0x88afe8: r0 = AllocateClosure()
    //     0x88afe8: bl              #0xec1630  ; AllocateClosureStub
    // 0x88afec: ldur            x1, [fp, #-0x20]
    // 0x88aff0: mov             x2, x0
    // 0x88aff4: ldur            x3, [fp, #-8]
    // 0x88aff8: r0 = predicate()
    //     0x88aff8: bl              #0x88b010  ; [package:petitparser/src/parser/predicate/predicate.dart] ::predicate
    // 0x88affc: LeaveFrame
    //     0x88affc: mov             SP, fp
    //     0x88b000: ldp             fp, lr, [SP], #0x10
    // 0x88b004: ret
    //     0x88b004: ret             
    // 0x88b008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b008: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b00c: b               #0x88af78
  }
  [closure] static bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x88b0f4, size: 0x4c
    // 0x88b0f4: EnterFrame
    //     0x88b0f4: stp             fp, lr, [SP, #-0x10]!
    //     0x88b0f8: mov             fp, SP
    // 0x88b0fc: AllocStack(0x10)
    //     0x88b0fc: sub             SP, SP, #0x10
    // 0x88b100: SetupParameters()
    //     0x88b100: ldr             x0, [fp, #0x18]
    //     0x88b104: ldur            w1, [x0, #0x17]
    //     0x88b108: add             x1, x1, HEAP, lsl #32
    // 0x88b10c: CheckStackOverflow
    //     0x88b10c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b110: cmp             SP, x16
    //     0x88b114: b.ls            #0x88b138
    // 0x88b118: LoadField: r0 = r1->field_f
    //     0x88b118: ldur            w0, [x1, #0xf]
    // 0x88b11c: DecompressPointer r0
    //     0x88b11c: add             x0, x0, HEAP, lsl #32
    // 0x88b120: ldr             x16, [fp, #0x10]
    // 0x88b124: stp             x16, x0, [SP]
    // 0x88b128: r0 = ==()
    //     0x88b128: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x88b12c: LeaveFrame
    //     0x88b12c: mov             SP, fp
    //     0x88b130: ldp             fp, lr, [SP], #0x10
    // 0x88b134: ret
    //     0x88b134: ret             
    // 0x88b138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b138: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b13c: b               #0x88b118
  }
}
