// lib: , url: package:petitparser/src/parser/repeater/character.dart

// class id: 1050911, size: 0x8
class :: {

  static _ RepeatingCharacterParserExtension.starString(/* No info */) {
    // ** addr: 0x88c6bc, size: 0x34
    // 0x88c6bc: EnterFrame
    //     0x88c6bc: stp             fp, lr, [SP, #-0x10]!
    //     0x88c6c0: mov             fp, SP
    // 0x88c6c4: CheckStackOverflow
    //     0x88c6c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c6c8: cmp             SP, x16
    //     0x88c6cc: b.ls            #0x88c6e8
    // 0x88c6d0: r2 = 0
    //     0x88c6d0: movz            x2, #0
    // 0x88c6d4: r3 = 9007199254740991
    //     0x88c6d4: orr             x3, xzr, #0x1fffffffffffff
    // 0x88c6d8: r0 = RepeatingCharacterParserExtension.repeatString()
    //     0x88c6d8: bl              #0x88c6f0  ; [package:petitparser/src/parser/repeater/character.dart] ::RepeatingCharacterParserExtension.repeatString
    // 0x88c6dc: LeaveFrame
    //     0x88c6dc: mov             SP, fp
    //     0x88c6e0: ldp             fp, lr, [SP], #0x10
    // 0x88c6e4: ret
    //     0x88c6e4: ret             
    // 0x88c6e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c6e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c6ec: b               #0x88c6d0
  }
  static _ RepeatingCharacterParserExtension.repeatString(/* No info */) {
    // ** addr: 0x88c6f0, size: 0x58
    // 0x88c6f0: EnterFrame
    //     0x88c6f0: stp             fp, lr, [SP, #-0x10]!
    //     0x88c6f4: mov             fp, SP
    // 0x88c6f8: AllocStack(0x18)
    //     0x88c6f8: sub             SP, SP, #0x18
    // 0x88c6fc: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x88c6fc: stur            x2, [fp, #-0x10]
    //     0x88c700: stur            x3, [fp, #-0x18]
    // 0x88c704: LoadField: r0 = r1->field_b
    //     0x88c704: ldur            w0, [x1, #0xb]
    // 0x88c708: DecompressPointer r0
    //     0x88c708: add             x0, x0, HEAP, lsl #32
    // 0x88c70c: stur            x0, [fp, #-8]
    // 0x88c710: r1 = <String>
    //     0x88c710: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88c714: r0 = RepeatingCharacterParser()
    //     0x88c714: bl              #0x88c748  ; AllocateRepeatingCharacterParserStub -> RepeatingCharacterParser (size=0x24)
    // 0x88c718: ldur            x1, [fp, #-8]
    // 0x88c71c: StoreField: r0->field_b = r1
    //     0x88c71c: stur            w1, [x0, #0xb]
    // 0x88c720: r1 = "whitespace expected"
    //     0x88c720: add             x1, PP, #0x26, lsl #12  ; [pp+0x267f0] "whitespace expected"
    //     0x88c724: ldr             x1, [x1, #0x7f0]
    // 0x88c728: StoreField: r0->field_f = r1
    //     0x88c728: stur            w1, [x0, #0xf]
    // 0x88c72c: ldur            x1, [fp, #-0x10]
    // 0x88c730: StoreField: r0->field_13 = r1
    //     0x88c730: stur            x1, [x0, #0x13]
    // 0x88c734: ldur            x1, [fp, #-0x18]
    // 0x88c738: StoreField: r0->field_1b = r1
    //     0x88c738: stur            x1, [x0, #0x1b]
    // 0x88c73c: LeaveFrame
    //     0x88c73c: mov             SP, fp
    //     0x88c740: ldp             fp, lr, [SP], #0x10
    // 0x88c744: ret
    //     0x88c744: ret             
  }
  static _ RepeatingCharacterParserExtension.plusString(/* No info */) {
    // ** addr: 0x88e134, size: 0x34
    // 0x88e134: EnterFrame
    //     0x88e134: stp             fp, lr, [SP, #-0x10]!
    //     0x88e138: mov             fp, SP
    // 0x88e13c: CheckStackOverflow
    //     0x88e13c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e140: cmp             SP, x16
    //     0x88e144: b.ls            #0x88e160
    // 0x88e148: r2 = 1
    //     0x88e148: movz            x2, #0x1
    // 0x88e14c: r3 = 9007199254740991
    //     0x88e14c: orr             x3, xzr, #0x1fffffffffffff
    // 0x88e150: r0 = RepeatingCharacterParserExtension.repeatString()
    //     0x88e150: bl              #0x88c6f0  ; [package:petitparser/src/parser/repeater/character.dart] ::RepeatingCharacterParserExtension.repeatString
    // 0x88e154: LeaveFrame
    //     0x88e154: mov             SP, fp
    //     0x88e158: ldp             fp, lr, [SP], #0x10
    // 0x88e15c: ret
    //     0x88e15c: ret             
    // 0x88e160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e160: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e164: b               #0x88e148
  }
}

// class id: 728, size: 0x24, field offset: 0xc
class RepeatingCharacterParser extends Parser<dynamic> {

  _ toString(/* No info */) {
    // ** addr: 0xc3bfa8, size: 0xdc
    // 0xc3bfa8: EnterFrame
    //     0xc3bfa8: stp             fp, lr, [SP, #-0x10]!
    //     0xc3bfac: mov             fp, SP
    // 0xc3bfb0: AllocStack(0x10)
    //     0xc3bfb0: sub             SP, SP, #0x10
    // 0xc3bfb4: CheckStackOverflow
    //     0xc3bfb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3bfb8: cmp             SP, x16
    //     0xc3bfbc: b.ls            #0xc3c07c
    // 0xc3bfc0: ldr             x16, [fp, #0x10]
    // 0xc3bfc4: str             x16, [SP]
    // 0xc3bfc8: r0 = toString()
    //     0xc3bfc8: bl              #0xc3c084  ; [package:petitparser/src/core/parser.dart] Parser::toString
    // 0xc3bfcc: r1 = Null
    //     0xc3bfcc: mov             x1, NULL
    // 0xc3bfd0: r2 = 16
    //     0xc3bfd0: movz            x2, #0x10
    // 0xc3bfd4: stur            x0, [fp, #-8]
    // 0xc3bfd8: r0 = AllocateArray()
    //     0xc3bfd8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3bfdc: mov             x2, x0
    // 0xc3bfe0: ldur            x0, [fp, #-8]
    // 0xc3bfe4: StoreField: r2->field_f = r0
    //     0xc3bfe4: stur            w0, [x2, #0xf]
    // 0xc3bfe8: r16 = "["
    //     0xc3bfe8: ldr             x16, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xc3bfec: StoreField: r2->field_13 = r16
    //     0xc3bfec: stur            w16, [x2, #0x13]
    // 0xc3bff0: ldr             x3, [fp, #0x10]
    // 0xc3bff4: LoadField: r0 = r3->field_f
    //     0xc3bff4: ldur            w0, [x3, #0xf]
    // 0xc3bff8: DecompressPointer r0
    //     0xc3bff8: add             x0, x0, HEAP, lsl #32
    // 0xc3bffc: ArrayStore: r2[0] = r0  ; List_4
    //     0xc3bffc: stur            w0, [x2, #0x17]
    // 0xc3c000: r16 = ", "
    //     0xc3c000: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3c004: StoreField: r2->field_1b = r16
    //     0xc3c004: stur            w16, [x2, #0x1b]
    // 0xc3c008: LoadField: r4 = r3->field_13
    //     0xc3c008: ldur            x4, [x3, #0x13]
    // 0xc3c00c: r0 = BoxInt64Instr(r4)
    //     0xc3c00c: sbfiz           x0, x4, #1, #0x1f
    //     0xc3c010: cmp             x4, x0, asr #1
    //     0xc3c014: b.eq            #0xc3c020
    //     0xc3c018: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3c01c: stur            x4, [x0, #7]
    // 0xc3c020: StoreField: r2->field_1f = r0
    //     0xc3c020: stur            w0, [x2, #0x1f]
    // 0xc3c024: r16 = ".."
    //     0xc3c024: ldr             x16, [PP, #0xc30]  ; [pp+0xc30] ".."
    // 0xc3c028: StoreField: r2->field_23 = r16
    //     0xc3c028: stur            w16, [x2, #0x23]
    // 0xc3c02c: LoadField: r4 = r3->field_1b
    //     0xc3c02c: ldur            x4, [x3, #0x1b]
    // 0xc3c030: r17 = 9007199254740991
    //     0xc3c030: orr             x17, xzr, #0x1fffffffffffff
    // 0xc3c034: cmp             x4, x17
    // 0xc3c038: b.ne            #0xc3c048
    // 0xc3c03c: r0 = "*"
    //     0xc3c03c: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2ad20] "*"
    //     0xc3c040: ldr             x0, [x0, #0xd20]
    // 0xc3c044: b               #0xc3c05c
    // 0xc3c048: r0 = BoxInt64Instr(r4)
    //     0xc3c048: sbfiz           x0, x4, #1, #0x1f
    //     0xc3c04c: cmp             x4, x0, asr #1
    //     0xc3c050: b.eq            #0xc3c05c
    //     0xc3c054: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3c058: stur            x4, [x0, #7]
    // 0xc3c05c: StoreField: r2->field_27 = r0
    //     0xc3c05c: stur            w0, [x2, #0x27]
    // 0xc3c060: r16 = "]"
    //     0xc3c060: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc3c064: StoreField: r2->field_2b = r16
    //     0xc3c064: stur            w16, [x2, #0x2b]
    // 0xc3c068: str             x2, [SP]
    // 0xc3c06c: r0 = _interpolate()
    //     0xc3c06c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3c070: LeaveFrame
    //     0xc3c070: mov             SP, fp
    //     0xc3c074: ldp             fp, lr, [SP], #0x10
    // 0xc3c078: ret
    //     0xc3c078: ret             
    // 0xc3c07c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3c07c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3c080: b               #0xc3bfc0
  }
  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb092c, size: 0x244
    // 0xeb092c: EnterFrame
    //     0xeb092c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0930: mov             fp, SP
    // 0xeb0934: AllocStack(0x48)
    //     0xeb0934: sub             SP, SP, #0x48
    // 0xeb0938: SetupParameters(RepeatingCharacterParser this /* r1 => r5, fp-0x40 */, dynamic _ /* r2 => r4, fp-0x48 */)
    //     0xeb0938: mov             x5, x1
    //     0xeb093c: mov             x4, x2
    //     0xeb0940: stur            x1, [fp, #-0x40]
    //     0xeb0944: stur            x2, [fp, #-0x48]
    // 0xeb0948: CheckStackOverflow
    //     0xeb0948: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb094c: cmp             SP, x16
    //     0xeb0950: b.ls            #0xeb0b50
    // 0xeb0954: LoadField: r6 = r4->field_7
    //     0xeb0954: ldur            w6, [x4, #7]
    // 0xeb0958: stur            x6, [fp, #-0x38]
    // 0xeb095c: LoadField: r7 = r5->field_13
    //     0xeb095c: ldur            x7, [x5, #0x13]
    // 0xeb0960: stur            x7, [fp, #-0x30]
    // 0xeb0964: r8 = LoadInt32Instr(r6)
    //     0xeb0964: sbfx            x8, x6, #1, #0x1f
    // 0xeb0968: stur            x8, [fp, #-0x28]
    // 0xeb096c: LoadField: r9 = r5->field_b
    //     0xeb096c: ldur            w9, [x5, #0xb]
    // 0xeb0970: DecompressPointer r9
    //     0xeb0970: add             x9, x9, HEAP, lsl #32
    // 0xeb0974: stur            x9, [fp, #-0x20]
    // 0xeb0978: r10 = LoadClassIdInstr(r4)
    //     0xeb0978: ldur            x10, [x4, #-1]
    //     0xeb097c: ubfx            x10, x10, #0xc, #0x14
    // 0xeb0980: lsl             x10, x10, #1
    // 0xeb0984: stur            x10, [fp, #-0x18]
    // 0xeb0988: mov             x11, x3
    // 0xeb098c: r3 = 0
    //     0xeb098c: movz            x3, #0
    // 0xeb0990: stur            x11, [fp, #-8]
    // 0xeb0994: stur            x3, [fp, #-0x10]
    // 0xeb0998: CheckStackOverflow
    //     0xeb0998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb099c: cmp             SP, x16
    //     0xeb09a0: b.ls            #0xeb0b58
    // 0xeb09a4: cmp             x3, x7
    // 0xeb09a8: b.ge            #0xeb0a44
    // 0xeb09ac: cmp             x11, x8
    // 0xeb09b0: b.ge            #0xeb0a34
    // 0xeb09b4: mov             x0, x8
    // 0xeb09b8: mov             x1, x11
    // 0xeb09bc: cmp             x1, x0
    // 0xeb09c0: b.hs            #0xeb0b60
    // 0xeb09c4: cmp             w10, #0xbc
    // 0xeb09c8: b.ne            #0xeb09dc
    // 0xeb09cc: ArrayLoad: r0 = r4[r11]  ; TypedUnsigned_1
    //     0xeb09cc: add             x16, x4, x11
    //     0xeb09d0: ldrb            w0, [x16, #0xf]
    // 0xeb09d4: mov             x2, x0
    // 0xeb09d8: b               #0xeb09e8
    // 0xeb09dc: add             x16, x4, x11, lsl #1
    // 0xeb09e0: ldurh           w0, [x16, #0xf]
    // 0xeb09e4: mov             x2, x0
    // 0xeb09e8: r0 = LoadClassIdInstr(r9)
    //     0xeb09e8: ldur            x0, [x9, #-1]
    //     0xeb09ec: ubfx            x0, x0, #0xc, #0x14
    // 0xeb09f0: mov             x1, x9
    // 0xeb09f4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb09f4: sub             lr, x0, #1, lsl #12
    //     0xeb09f8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb09fc: blr             lr
    // 0xeb0a00: tbnz            w0, #4, #0xeb0a34
    // 0xeb0a04: ldur            x1, [fp, #-8]
    // 0xeb0a08: ldur            x0, [fp, #-0x10]
    // 0xeb0a0c: add             x11, x1, #1
    // 0xeb0a10: add             x3, x0, #1
    // 0xeb0a14: ldur            x5, [fp, #-0x40]
    // 0xeb0a18: ldur            x4, [fp, #-0x48]
    // 0xeb0a1c: ldur            x7, [fp, #-0x30]
    // 0xeb0a20: ldur            x9, [fp, #-0x20]
    // 0xeb0a24: ldur            x10, [fp, #-0x18]
    // 0xeb0a28: ldur            x6, [fp, #-0x38]
    // 0xeb0a2c: ldur            x8, [fp, #-0x28]
    // 0xeb0a30: b               #0xeb0990
    // 0xeb0a34: r0 = -2
    //     0xeb0a34: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeb0a38: LeaveFrame
    //     0xeb0a38: mov             SP, fp
    //     0xeb0a3c: ldp             fp, lr, [SP], #0x10
    // 0xeb0a40: ret
    //     0xeb0a40: ret             
    // 0xeb0a44: mov             x2, x5
    // 0xeb0a48: mov             x0, x3
    // 0xeb0a4c: mov             x3, x4
    // 0xeb0a50: mov             x1, x11
    // 0xeb0a54: mov             x4, x6
    // 0xeb0a58: r5 = LoadInt32Instr(r4)
    //     0xeb0a58: sbfx            x5, x4, #1, #0x1f
    // 0xeb0a5c: stur            x5, [fp, #-0x30]
    // 0xeb0a60: LoadField: r4 = r2->field_1b
    //     0xeb0a60: ldur            x4, [x2, #0x1b]
    // 0xeb0a64: stur            x4, [fp, #-0x28]
    // 0xeb0a68: r6 = LoadClassIdInstr(r3)
    //     0xeb0a68: ldur            x6, [x3, #-1]
    //     0xeb0a6c: ubfx            x6, x6, #0xc, #0x14
    // 0xeb0a70: lsl             x6, x6, #1
    // 0xeb0a74: stur            x6, [fp, #-0x18]
    // 0xeb0a78: mov             x9, x1
    // 0xeb0a7c: mov             x8, x0
    // 0xeb0a80: ldur            x7, [fp, #-0x20]
    // 0xeb0a84: stur            x9, [fp, #-8]
    // 0xeb0a88: stur            x8, [fp, #-0x10]
    // 0xeb0a8c: CheckStackOverflow
    //     0xeb0a8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0a90: cmp             SP, x16
    //     0xeb0a94: b.ls            #0xeb0b64
    // 0xeb0a98: cmp             x9, x5
    // 0xeb0a9c: b.ge            #0xeb0b2c
    // 0xeb0aa0: cmp             x8, x4
    // 0xeb0aa4: b.ge            #0xeb0b24
    // 0xeb0aa8: mov             x0, x5
    // 0xeb0aac: mov             x1, x9
    // 0xeb0ab0: cmp             x1, x0
    // 0xeb0ab4: b.hs            #0xeb0b6c
    // 0xeb0ab8: cmp             w6, #0xbc
    // 0xeb0abc: b.ne            #0xeb0ad0
    // 0xeb0ac0: ArrayLoad: r0 = r3[r9]  ; TypedUnsigned_1
    //     0xeb0ac0: add             x16, x3, x9
    //     0xeb0ac4: ldrb            w0, [x16, #0xf]
    // 0xeb0ac8: mov             x2, x0
    // 0xeb0acc: b               #0xeb0adc
    // 0xeb0ad0: add             x16, x3, x9, lsl #1
    // 0xeb0ad4: ldurh           w0, [x16, #0xf]
    // 0xeb0ad8: mov             x2, x0
    // 0xeb0adc: r0 = LoadClassIdInstr(r7)
    //     0xeb0adc: ldur            x0, [x7, #-1]
    //     0xeb0ae0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb0ae4: mov             x1, x7
    // 0xeb0ae8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb0ae8: sub             lr, x0, #1, lsl #12
    //     0xeb0aec: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0af0: blr             lr
    // 0xeb0af4: tbnz            w0, #4, #0xeb0b1c
    // 0xeb0af8: ldur            x3, [fp, #-8]
    // 0xeb0afc: ldur            x2, [fp, #-0x10]
    // 0xeb0b00: add             x9, x3, #1
    // 0xeb0b04: add             x8, x2, #1
    // 0xeb0b08: ldur            x3, [fp, #-0x48]
    // 0xeb0b0c: ldur            x4, [fp, #-0x28]
    // 0xeb0b10: ldur            x6, [fp, #-0x18]
    // 0xeb0b14: ldur            x5, [fp, #-0x30]
    // 0xeb0b18: b               #0xeb0a80
    // 0xeb0b1c: ldur            x3, [fp, #-8]
    // 0xeb0b20: b               #0xeb0b30
    // 0xeb0b24: mov             x3, x9
    // 0xeb0b28: b               #0xeb0b30
    // 0xeb0b2c: mov             x3, x9
    // 0xeb0b30: r0 = BoxInt64Instr(r3)
    //     0xeb0b30: sbfiz           x0, x3, #1, #0x1f
    //     0xeb0b34: cmp             x3, x0, asr #1
    //     0xeb0b38: b.eq            #0xeb0b44
    //     0xeb0b3c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb0b40: stur            x3, [x0, #7]
    // 0xeb0b44: LeaveFrame
    //     0xeb0b44: mov             SP, fp
    //     0xeb0b48: ldp             fp, lr, [SP], #0x10
    // 0xeb0b4c: ret
    //     0xeb0b4c: ret             
    // 0xeb0b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0b54: b               #0xeb0954
    // 0xeb0b58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0b58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0b5c: b               #0xeb09a4
    // 0xeb0b60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb0b60: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeb0b64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0b64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0b68: b               #0xeb0a98
    // 0xeb0b6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb0b6c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb32fc, size: 0x2cc
    // 0xeb32fc: EnterFrame
    //     0xeb32fc: stp             fp, lr, [SP, #-0x10]!
    //     0xeb3300: mov             fp, SP
    // 0xeb3304: AllocStack(0x78)
    //     0xeb3304: sub             SP, SP, #0x78
    // 0xeb3308: SetupParameters(RepeatingCharacterParser this /* r1 => r4, fp-0x50 */, dynamic _ /* r2 => r3, fp-0x58 */)
    //     0xeb3308: mov             x4, x1
    //     0xeb330c: mov             x3, x2
    //     0xeb3310: stur            x1, [fp, #-0x50]
    //     0xeb3314: stur            x2, [fp, #-0x58]
    // 0xeb3318: CheckStackOverflow
    //     0xeb3318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb331c: cmp             SP, x16
    //     0xeb3320: b.ls            #0xeb35a8
    // 0xeb3324: LoadField: r5 = r3->field_7
    //     0xeb3324: ldur            w5, [x3, #7]
    // 0xeb3328: DecompressPointer r5
    //     0xeb3328: add             x5, x5, HEAP, lsl #32
    // 0xeb332c: stur            x5, [fp, #-0x48]
    // 0xeb3330: LoadField: r6 = r3->field_b
    //     0xeb3330: ldur            x6, [x3, #0xb]
    // 0xeb3334: stur            x6, [fp, #-0x40]
    // 0xeb3338: LoadField: r7 = r5->field_7
    //     0xeb3338: ldur            w7, [x5, #7]
    // 0xeb333c: stur            x7, [fp, #-0x38]
    // 0xeb3340: LoadField: r8 = r4->field_13
    //     0xeb3340: ldur            x8, [x4, #0x13]
    // 0xeb3344: stur            x8, [fp, #-0x30]
    // 0xeb3348: r9 = LoadInt32Instr(r7)
    //     0xeb3348: sbfx            x9, x7, #1, #0x1f
    // 0xeb334c: stur            x9, [fp, #-0x28]
    // 0xeb3350: LoadField: r10 = r4->field_b
    //     0xeb3350: ldur            w10, [x4, #0xb]
    // 0xeb3354: DecompressPointer r10
    //     0xeb3354: add             x10, x10, HEAP, lsl #32
    // 0xeb3358: stur            x10, [fp, #-0x20]
    // 0xeb335c: r11 = LoadClassIdInstr(r5)
    //     0xeb335c: ldur            x11, [x5, #-1]
    //     0xeb3360: ubfx            x11, x11, #0xc, #0x14
    // 0xeb3364: lsl             x11, x11, #1
    // 0xeb3368: stur            x11, [fp, #-0x18]
    // 0xeb336c: mov             x13, x6
    // 0xeb3370: r12 = 0
    //     0xeb3370: movz            x12, #0
    // 0xeb3374: stur            x13, [fp, #-8]
    // 0xeb3378: stur            x12, [fp, #-0x10]
    // 0xeb337c: CheckStackOverflow
    //     0xeb337c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb3380: cmp             SP, x16
    //     0xeb3384: b.ls            #0xeb35b0
    // 0xeb3388: cmp             x12, x8
    // 0xeb338c: b.ge            #0xeb3468
    // 0xeb3390: cmp             x13, x9
    // 0xeb3394: b.ge            #0xeb3428
    // 0xeb3398: mov             x0, x9
    // 0xeb339c: mov             x1, x13
    // 0xeb33a0: cmp             x1, x0
    // 0xeb33a4: b.hs            #0xeb35b8
    // 0xeb33a8: cmp             w11, #0xbc
    // 0xeb33ac: b.ne            #0xeb33c0
    // 0xeb33b0: ArrayLoad: r0 = r5[r13]  ; TypedUnsigned_1
    //     0xeb33b0: add             x16, x5, x13
    //     0xeb33b4: ldrb            w0, [x16, #0xf]
    // 0xeb33b8: mov             x2, x0
    // 0xeb33bc: b               #0xeb33cc
    // 0xeb33c0: add             x16, x5, x13, lsl #1
    // 0xeb33c4: ldurh           w0, [x16, #0xf]
    // 0xeb33c8: mov             x2, x0
    // 0xeb33cc: r0 = LoadClassIdInstr(r10)
    //     0xeb33cc: ldur            x0, [x10, #-1]
    //     0xeb33d0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb33d4: mov             x1, x10
    // 0xeb33d8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb33d8: sub             lr, x0, #1, lsl #12
    //     0xeb33dc: ldr             lr, [x21, lr, lsl #3]
    //     0xeb33e0: blr             lr
    // 0xeb33e4: tbnz            w0, #4, #0xeb3420
    // 0xeb33e8: ldur            x2, [fp, #-8]
    // 0xeb33ec: ldur            x0, [fp, #-0x10]
    // 0xeb33f0: add             x13, x2, #1
    // 0xeb33f4: add             x12, x0, #1
    // 0xeb33f8: ldur            x4, [fp, #-0x50]
    // 0xeb33fc: ldur            x3, [fp, #-0x58]
    // 0xeb3400: ldur            x5, [fp, #-0x48]
    // 0xeb3404: ldur            x6, [fp, #-0x40]
    // 0xeb3408: ldur            x8, [fp, #-0x30]
    // 0xeb340c: ldur            x10, [fp, #-0x20]
    // 0xeb3410: ldur            x11, [fp, #-0x18]
    // 0xeb3414: ldur            x7, [fp, #-0x38]
    // 0xeb3418: ldur            x9, [fp, #-0x28]
    // 0xeb341c: b               #0xeb3374
    // 0xeb3420: ldur            x2, [fp, #-8]
    // 0xeb3424: b               #0xeb342c
    // 0xeb3428: mov             x2, x13
    // 0xeb342c: ldur            x0, [fp, #-0x48]
    // 0xeb3430: r1 = <Never>
    //     0xeb3430: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xeb3434: r0 = Failure()
    //     0xeb3434: bl              #0x75149c  ; AllocateFailureStub -> Failure (size=0x1c)
    // 0xeb3438: mov             x1, x0
    // 0xeb343c: r0 = "whitespace expected"
    //     0xeb343c: add             x0, PP, #0x26, lsl #12  ; [pp+0x267f0] "whitespace expected"
    //     0xeb3440: ldr             x0, [x0, #0x7f0]
    // 0xeb3444: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb3444: stur            w0, [x1, #0x17]
    // 0xeb3448: ldur            x3, [fp, #-0x48]
    // 0xeb344c: StoreField: r1->field_7 = r3
    //     0xeb344c: stur            w3, [x1, #7]
    // 0xeb3450: ldur            x2, [fp, #-8]
    // 0xeb3454: StoreField: r1->field_b = r2
    //     0xeb3454: stur            x2, [x1, #0xb]
    // 0xeb3458: mov             x0, x1
    // 0xeb345c: LeaveFrame
    //     0xeb345c: mov             SP, fp
    //     0xeb3460: ldp             fp, lr, [SP], #0x10
    // 0xeb3464: ret
    //     0xeb3464: ret             
    // 0xeb3468: mov             x1, x4
    // 0xeb346c: mov             x3, x5
    // 0xeb3470: mov             x2, x13
    // 0xeb3474: mov             x0, x12
    // 0xeb3478: mov             x4, x7
    // 0xeb347c: r5 = LoadInt32Instr(r4)
    //     0xeb347c: sbfx            x5, x4, #1, #0x1f
    // 0xeb3480: stur            x5, [fp, #-0x30]
    // 0xeb3484: LoadField: r4 = r1->field_1b
    //     0xeb3484: ldur            x4, [x1, #0x1b]
    // 0xeb3488: stur            x4, [fp, #-0x28]
    // 0xeb348c: r6 = LoadClassIdInstr(r3)
    //     0xeb348c: ldur            x6, [x3, #-1]
    //     0xeb3490: ubfx            x6, x6, #0xc, #0x14
    // 0xeb3494: lsl             x6, x6, #1
    // 0xeb3498: stur            x6, [fp, #-0x18]
    // 0xeb349c: mov             x9, x2
    // 0xeb34a0: mov             x8, x0
    // 0xeb34a4: ldur            x7, [fp, #-0x20]
    // 0xeb34a8: stur            x9, [fp, #-8]
    // 0xeb34ac: stur            x8, [fp, #-0x10]
    // 0xeb34b0: CheckStackOverflow
    //     0xeb34b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb34b4: cmp             SP, x16
    //     0xeb34b8: b.ls            #0xeb35bc
    // 0xeb34bc: cmp             x9, x5
    // 0xeb34c0: b.ge            #0xeb3550
    // 0xeb34c4: cmp             x8, x4
    // 0xeb34c8: b.ge            #0xeb3548
    // 0xeb34cc: mov             x0, x5
    // 0xeb34d0: mov             x1, x9
    // 0xeb34d4: cmp             x1, x0
    // 0xeb34d8: b.hs            #0xeb35c4
    // 0xeb34dc: cmp             w6, #0xbc
    // 0xeb34e0: b.ne            #0xeb34f4
    // 0xeb34e4: ArrayLoad: r0 = r3[r9]  ; TypedUnsigned_1
    //     0xeb34e4: add             x16, x3, x9
    //     0xeb34e8: ldrb            w0, [x16, #0xf]
    // 0xeb34ec: mov             x2, x0
    // 0xeb34f0: b               #0xeb3500
    // 0xeb34f4: add             x16, x3, x9, lsl #1
    // 0xeb34f8: ldurh           w0, [x16, #0xf]
    // 0xeb34fc: mov             x2, x0
    // 0xeb3500: r0 = LoadClassIdInstr(r7)
    //     0xeb3500: ldur            x0, [x7, #-1]
    //     0xeb3504: ubfx            x0, x0, #0xc, #0x14
    // 0xeb3508: mov             x1, x7
    // 0xeb350c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb350c: sub             lr, x0, #1, lsl #12
    //     0xeb3510: ldr             lr, [x21, lr, lsl #3]
    //     0xeb3514: blr             lr
    // 0xeb3518: tbnz            w0, #4, #0xeb3540
    // 0xeb351c: ldur            x2, [fp, #-8]
    // 0xeb3520: ldur            x0, [fp, #-0x10]
    // 0xeb3524: add             x9, x2, #1
    // 0xeb3528: add             x8, x0, #1
    // 0xeb352c: ldur            x3, [fp, #-0x48]
    // 0xeb3530: ldur            x4, [fp, #-0x28]
    // 0xeb3534: ldur            x6, [fp, #-0x18]
    // 0xeb3538: ldur            x5, [fp, #-0x30]
    // 0xeb353c: b               #0xeb34a4
    // 0xeb3540: ldur            x2, [fp, #-8]
    // 0xeb3544: b               #0xeb3554
    // 0xeb3548: mov             x2, x9
    // 0xeb354c: b               #0xeb3554
    // 0xeb3550: mov             x2, x9
    // 0xeb3554: r0 = BoxInt64Instr(r2)
    //     0xeb3554: sbfiz           x0, x2, #1, #0x1f
    //     0xeb3558: cmp             x2, x0, asr #1
    //     0xeb355c: b.eq            #0xeb3568
    //     0xeb3560: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3564: stur            x2, [x0, #7]
    // 0xeb3568: stur            x0, [fp, #-0x18]
    // 0xeb356c: str             x0, [SP]
    // 0xeb3570: ldur            x1, [fp, #-0x48]
    // 0xeb3574: ldur            x2, [fp, #-0x40]
    // 0xeb3578: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb3578: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb357c: r0 = substring()
    //     0xeb357c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xeb3580: r16 = <String>
    //     0xeb3580: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xeb3584: ldur            lr, [fp, #-0x58]
    // 0xeb3588: stp             lr, x16, [SP, #0x10]
    // 0xeb358c: ldur            x16, [fp, #-0x18]
    // 0xeb3590: stp             x16, x0, [SP]
    // 0xeb3594: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xeb3594: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xeb3598: r0 = success()
    //     0xeb3598: bl              #0xeb1004  ; [package:petitparser/src/core/context.dart] Context::success
    // 0xeb359c: LeaveFrame
    //     0xeb359c: mov             SP, fp
    //     0xeb35a0: ldp             fp, lr, [SP], #0x10
    // 0xeb35a4: ret
    //     0xeb35a4: ret             
    // 0xeb35a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb35a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb35ac: b               #0xeb3324
    // 0xeb35b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb35b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb35b4: b               #0xeb3388
    // 0xeb35b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb35b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeb35bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb35bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb35c0: b               #0xeb34bc
    // 0xeb35c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb35c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
