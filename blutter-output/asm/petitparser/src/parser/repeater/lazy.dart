// lib: , url: package:petitparser/src/parser/repeater/lazy.dart

// class id: 1050912, size: 0x8
class :: {

  static _ LazyRepeatingParserExtension.starLazy(/* No info */) {
    // ** addr: 0x88b9b0, size: 0x64
    // 0x88b9b0: EnterFrame
    //     0x88b9b0: stp             fp, lr, [SP, #-0x10]!
    //     0x88b9b4: mov             fp, SP
    // 0x88b9b8: AllocStack(0x20)
    //     0x88b9b8: sub             SP, SP, #0x20
    // 0x88b9bc: SetupParameters()
    //     0x88b9bc: ldur            w0, [x4, #0xf]
    //     0x88b9c0: cbnz            w0, #0x88b9cc
    //     0x88b9c4: mov             x1, NULL
    //     0x88b9c8: b               #0x88b9d8
    //     0x88b9cc: ldur            w0, [x4, #0x17]
    //     0x88b9d0: add             x1, fp, w0, sxtw #2
    //     0x88b9d4: ldr             x1, [x1, #0x10]
    //     0x88b9d8: orr             x0, xzr, #0x1fffffffffffff
    // 0x88b9d8: r0 = 9007199254740991
    // 0x88b9dc: CheckStackOverflow
    //     0x88b9dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b9e0: cmp             SP, x16
    //     0x88b9e4: b.ls            #0x88ba0c
    // 0x88b9e8: ldr             x16, [fp, #0x18]
    // 0x88b9ec: stp             x16, x1, [SP, #0x10]
    // 0x88b9f0: ldr             x16, [fp, #0x10]
    // 0x88b9f4: stp             x0, x16, [SP]
    // 0x88b9f8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x88b9f8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x88b9fc: r0 = LazyRepeatingParserExtension.repeatLazy()
    //     0x88b9fc: bl              #0x88ba14  ; [package:petitparser/src/parser/repeater/lazy.dart] ::LazyRepeatingParserExtension.repeatLazy
    // 0x88ba00: LeaveFrame
    //     0x88ba00: mov             SP, fp
    //     0x88ba04: ldp             fp, lr, [SP], #0x10
    // 0x88ba08: ret
    //     0x88ba08: ret             
    // 0x88ba0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ba0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88ba10: b               #0x88b9e8
  }
  static Parser<List<Y0>> LazyRepeatingParserExtension.repeatLazy<Y0>(Parser<Y0>, Parser<void>, int) {
    // ** addr: 0x88ba14, size: 0x78
    // 0x88ba14: EnterFrame
    //     0x88ba14: stp             fp, lr, [SP, #-0x10]!
    //     0x88ba18: mov             fp, SP
    // 0x88ba1c: LoadField: r0 = r4->field_f
    //     0x88ba1c: ldur            w0, [x4, #0xf]
    // 0x88ba20: cbnz            w0, #0x88ba2c
    // 0x88ba24: r1 = Null
    //     0x88ba24: mov             x1, NULL
    // 0x88ba28: b               #0x88ba38
    // 0x88ba2c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88ba2c: ldur            w0, [x4, #0x17]
    // 0x88ba30: add             x1, fp, w0, sxtw #2
    // 0x88ba34: ldr             x1, [x1, #0x10]
    // 0x88ba38: ldr             x5, [fp, #0x20]
    // 0x88ba3c: ldr             x4, [fp, #0x18]
    // 0x88ba40: ldr             x0, [fp, #0x10]
    // 0x88ba44: r2 = Null
    //     0x88ba44: mov             x2, NULL
    // 0x88ba48: r3 = <List<Y0>, Y0, List<Y0>, Y0>
    //     0x88ba48: add             x3, PP, #0x26, lsl #12  ; [pp+0x267e0] TypeArguments: <List<Y0>, Y0, List<Y0>, Y0>
    //     0x88ba4c: ldr             x3, [x3, #0x7e0]
    // 0x88ba50: r30 = InstantiateTypeArgumentsStub
    //     0x88ba50: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88ba54: LoadField: r30 = r30->field_7
    //     0x88ba54: ldur            lr, [lr, #7]
    // 0x88ba58: blr             lr
    // 0x88ba5c: mov             x1, x0
    // 0x88ba60: r0 = LazyRepeatingParser()
    //     0x88ba60: bl              #0x88ba8c  ; AllocateLazyRepeatingParserStub -> LazyRepeatingParser<C3X0> (size=0x24)
    // 0x88ba64: ldr             x1, [fp, #0x18]
    // 0x88ba68: StoreField: r0->field_1f = r1
    //     0x88ba68: stur            w1, [x0, #0x1f]
    // 0x88ba6c: StoreField: r0->field_f = rZR
    //     0x88ba6c: stur            xzr, [x0, #0xf]
    // 0x88ba70: ldr             x1, [fp, #0x10]
    // 0x88ba74: ArrayStore: r0[0] = r1  ; List_8
    //     0x88ba74: stur            x1, [x0, #0x17]
    // 0x88ba78: ldr             x1, [fp, #0x20]
    // 0x88ba7c: StoreField: r0->field_b = r1
    //     0x88ba7c: stur            w1, [x0, #0xb]
    // 0x88ba80: LeaveFrame
    //     0x88ba80: mov             SP, fp
    //     0x88ba84: ldp             fp, lr, [SP], #0x10
    // 0x88ba88: ret
    //     0x88ba88: ret             
  }
}

// class id: 745, size: 0x24, field offset: 0x24
class LazyRepeatingParser<C3X0> extends LimitedRepeatingParser<C3X0> {

  _ fastParseOn(/* No info */) {
    // ** addr: 0xeafa98, size: 0x1bc
    // 0xeafa98: EnterFrame
    //     0xeafa98: stp             fp, lr, [SP, #-0x10]!
    //     0xeafa9c: mov             fp, SP
    // 0xeafaa0: AllocStack(0x28)
    //     0xeafaa0: sub             SP, SP, #0x28
    // 0xeafaa4: SetupParameters(LazyRepeatingParser<C3X0> this /* r1 => r5, fp-0x18 */, dynamic _ /* r2 => r4, fp-0x20 */)
    //     0xeafaa4: mov             x5, x1
    //     0xeafaa8: mov             x4, x2
    //     0xeafaac: stur            x1, [fp, #-0x18]
    //     0xeafab0: stur            x2, [fp, #-0x20]
    // 0xeafab4: CheckStackOverflow
    //     0xeafab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeafab8: cmp             SP, x16
    //     0xeafabc: b.ls            #0xeafc3c
    // 0xeafac0: LoadField: r6 = r5->field_f
    //     0xeafac0: ldur            x6, [x5, #0xf]
    // 0xeafac4: stur            x6, [fp, #-0x10]
    // 0xeafac8: r7 = 0
    //     0xeafac8: movz            x7, #0
    // 0xeafacc: stur            x7, [fp, #-8]
    // 0xeafad0: CheckStackOverflow
    //     0xeafad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeafad4: cmp             SP, x16
    //     0xeafad8: b.ls            #0xeafc44
    // 0xeafadc: cmp             x7, x6
    // 0xeafae0: b.ge            #0xeafb3c
    // 0xeafae4: LoadField: r1 = r5->field_b
    //     0xeafae4: ldur            w1, [x5, #0xb]
    // 0xeafae8: DecompressPointer r1
    //     0xeafae8: add             x1, x1, HEAP, lsl #32
    // 0xeafaec: r0 = LoadClassIdInstr(r1)
    //     0xeafaec: ldur            x0, [x1, #-1]
    //     0xeafaf0: ubfx            x0, x0, #0xc, #0x14
    // 0xeafaf4: mov             x2, x4
    // 0xeafaf8: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeafaf8: sub             lr, x0, #0xfce
    //     0xeafafc: ldr             lr, [x21, lr, lsl #3]
    //     0xeafb00: blr             lr
    // 0xeafb04: r3 = LoadInt32Instr(r0)
    //     0xeafb04: sbfx            x3, x0, #1, #0x1f
    //     0xeafb08: tbz             w0, #0, #0xeafb10
    //     0xeafb0c: ldur            x3, [x0, #7]
    // 0xeafb10: tbnz            x3, #0x3f, #0xeafb2c
    // 0xeafb14: ldur            x0, [fp, #-8]
    // 0xeafb18: add             x7, x0, #1
    // 0xeafb1c: ldur            x5, [fp, #-0x18]
    // 0xeafb20: ldur            x4, [fp, #-0x20]
    // 0xeafb24: ldur            x6, [fp, #-0x10]
    // 0xeafb28: b               #0xeafacc
    // 0xeafb2c: r0 = -2
    //     0xeafb2c: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeafb30: LeaveFrame
    //     0xeafb30: mov             SP, fp
    //     0xeafb34: ldp             fp, lr, [SP], #0x10
    // 0xeafb38: ret
    //     0xeafb38: ret             
    // 0xeafb3c: mov             x4, x5
    // 0xeafb40: mov             x0, x7
    // 0xeafb44: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xeafb44: ldur            x5, [x4, #0x17]
    // 0xeafb48: stur            x5, [fp, #-0x28]
    // 0xeafb4c: mov             x7, x0
    // 0xeafb50: mov             x6, x3
    // 0xeafb54: stur            x7, [fp, #-8]
    // 0xeafb58: stur            x6, [fp, #-0x10]
    // 0xeafb5c: CheckStackOverflow
    //     0xeafb5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeafb60: cmp             SP, x16
    //     0xeafb64: b.ls            #0xeafc4c
    // 0xeafb68: LoadField: r1 = r4->field_1f
    //     0xeafb68: ldur            w1, [x4, #0x1f]
    // 0xeafb6c: DecompressPointer r1
    //     0xeafb6c: add             x1, x1, HEAP, lsl #32
    // 0xeafb70: r0 = LoadClassIdInstr(r1)
    //     0xeafb70: ldur            x0, [x1, #-1]
    //     0xeafb74: ubfx            x0, x0, #0xc, #0x14
    // 0xeafb78: ldur            x2, [fp, #-0x20]
    // 0xeafb7c: mov             x3, x6
    // 0xeafb80: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeafb80: sub             lr, x0, #0xfce
    //     0xeafb84: ldr             lr, [x21, lr, lsl #3]
    //     0xeafb88: blr             lr
    // 0xeafb8c: r1 = LoadInt32Instr(r0)
    //     0xeafb8c: sbfx            x1, x0, #1, #0x1f
    //     0xeafb90: tbz             w0, #0, #0xeafb98
    //     0xeafb94: ldur            x1, [x0, #7]
    // 0xeafb98: tbz             x1, #0x3f, #0xeafc18
    // 0xeafb9c: ldur            x5, [fp, #-8]
    // 0xeafba0: ldur            x4, [fp, #-0x28]
    // 0xeafba4: cmp             x5, x4
    // 0xeafba8: b.ge            #0xeafc08
    // 0xeafbac: ldur            x6, [fp, #-0x18]
    // 0xeafbb0: LoadField: r1 = r6->field_b
    //     0xeafbb0: ldur            w1, [x6, #0xb]
    // 0xeafbb4: DecompressPointer r1
    //     0xeafbb4: add             x1, x1, HEAP, lsl #32
    // 0xeafbb8: r0 = LoadClassIdInstr(r1)
    //     0xeafbb8: ldur            x0, [x1, #-1]
    //     0xeafbbc: ubfx            x0, x0, #0xc, #0x14
    // 0xeafbc0: ldur            x2, [fp, #-0x20]
    // 0xeafbc4: ldur            x3, [fp, #-0x10]
    // 0xeafbc8: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeafbc8: sub             lr, x0, #0xfce
    //     0xeafbcc: ldr             lr, [x21, lr, lsl #3]
    //     0xeafbd0: blr             lr
    // 0xeafbd4: r6 = LoadInt32Instr(r0)
    //     0xeafbd4: sbfx            x6, x0, #1, #0x1f
    //     0xeafbd8: tbz             w0, #0, #0xeafbe0
    //     0xeafbdc: ldur            x6, [x0, #7]
    // 0xeafbe0: tbnz            x6, #0x3f, #0xeafbf8
    // 0xeafbe4: ldur            x2, [fp, #-8]
    // 0xeafbe8: add             x7, x2, #1
    // 0xeafbec: ldur            x4, [fp, #-0x18]
    // 0xeafbf0: ldur            x5, [fp, #-0x28]
    // 0xeafbf4: b               #0xeafb54
    // 0xeafbf8: r0 = -2
    //     0xeafbf8: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeafbfc: LeaveFrame
    //     0xeafbfc: mov             SP, fp
    //     0xeafc00: ldp             fp, lr, [SP], #0x10
    // 0xeafc04: ret
    //     0xeafc04: ret             
    // 0xeafc08: r0 = -2
    //     0xeafc08: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeafc0c: LeaveFrame
    //     0xeafc0c: mov             SP, fp
    //     0xeafc10: ldp             fp, lr, [SP], #0x10
    // 0xeafc14: ret
    //     0xeafc14: ret             
    // 0xeafc18: ldur            x2, [fp, #-0x10]
    // 0xeafc1c: r0 = BoxInt64Instr(r2)
    //     0xeafc1c: sbfiz           x0, x2, #1, #0x1f
    //     0xeafc20: cmp             x2, x0, asr #1
    //     0xeafc24: b.eq            #0xeafc30
    //     0xeafc28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeafc2c: stur            x2, [x0, #7]
    // 0xeafc30: LeaveFrame
    //     0xeafc30: mov             SP, fp
    //     0xeafc34: ldp             fp, lr, [SP], #0x10
    // 0xeafc38: ret
    //     0xeafc38: ret             
    // 0xeafc3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeafc3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeafc40: b               #0xeafac0
    // 0xeafc44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeafc44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeafc48: b               #0xeafadc
    // 0xeafc4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeafc4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeafc50: b               #0xeafb68
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb15c4, size: 0x408
    // 0xeb15c4: EnterFrame
    //     0xeb15c4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb15c8: mov             fp, SP
    // 0xeb15cc: AllocStack(0x58)
    //     0xeb15cc: sub             SP, SP, #0x58
    // 0xeb15d0: SetupParameters(LazyRepeatingParser<C3X0> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xeb15d0: mov             x4, x1
    //     0xeb15d4: mov             x0, x2
    //     0xeb15d8: stur            x1, [fp, #-0x10]
    //     0xeb15dc: stur            x2, [fp, #-0x18]
    // 0xeb15e0: CheckStackOverflow
    //     0xeb15e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb15e4: cmp             SP, x16
    //     0xeb15e8: b.ls            #0xeb19b4
    // 0xeb15ec: LoadField: r5 = r4->field_7
    //     0xeb15ec: ldur            w5, [x4, #7]
    // 0xeb15f0: DecompressPointer r5
    //     0xeb15f0: add             x5, x5, HEAP, lsl #32
    // 0xeb15f4: mov             x2, x5
    // 0xeb15f8: stur            x5, [fp, #-8]
    // 0xeb15fc: r1 = Null
    //     0xeb15fc: mov             x1, NULL
    // 0xeb1600: r3 = <C3X0>
    //     0xeb1600: add             x3, PP, #0x31, lsl #12  ; [pp+0x31230] TypeArguments: <C3X0>
    //     0xeb1604: ldr             x3, [x3, #0x230]
    // 0xeb1608: r0 = Null
    //     0xeb1608: mov             x0, NULL
    // 0xeb160c: cmp             x2, x0
    // 0xeb1610: b.eq            #0xeb1620
    // 0xeb1614: r30 = InstantiateTypeArgumentsStub
    //     0xeb1614: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xeb1618: LoadField: r30 = r30->field_7
    //     0xeb1618: ldur            lr, [lr, #7]
    // 0xeb161c: blr             lr
    // 0xeb1620: mov             x1, x0
    // 0xeb1624: r2 = 0
    //     0xeb1624: movz            x2, #0
    // 0xeb1628: stur            x0, [fp, #-0x20]
    // 0xeb162c: r0 = _GrowableList()
    //     0xeb162c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xeb1630: mov             x4, x0
    // 0xeb1634: ldur            x3, [fp, #-0x10]
    // 0xeb1638: stur            x4, [fp, #-0x30]
    // 0xeb163c: LoadField: r5 = r3->field_f
    //     0xeb163c: ldur            x5, [x3, #0xf]
    // 0xeb1640: stur            x5, [fp, #-0x28]
    // 0xeb1644: ldur            x2, [fp, #-0x18]
    // 0xeb1648: CheckStackOverflow
    //     0xeb1648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb164c: cmp             SP, x16
    //     0xeb1650: b.ls            #0xeb19bc
    // 0xeb1654: LoadField: r0 = r4->field_b
    //     0xeb1654: ldur            w0, [x4, #0xb]
    // 0xeb1658: r1 = LoadInt32Instr(r0)
    //     0xeb1658: sbfx            x1, x0, #1, #0x1f
    // 0xeb165c: cmp             x1, x5
    // 0xeb1660: b.ge            #0xeb177c
    // 0xeb1664: LoadField: r1 = r3->field_b
    //     0xeb1664: ldur            w1, [x3, #0xb]
    // 0xeb1668: DecompressPointer r1
    //     0xeb1668: add             x1, x1, HEAP, lsl #32
    // 0xeb166c: r0 = LoadClassIdInstr(r1)
    //     0xeb166c: ldur            x0, [x1, #-1]
    //     0xeb1670: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1674: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1674: sub             lr, x0, #1, lsl #12
    //     0xeb1678: ldr             lr, [x21, lr, lsl #3]
    //     0xeb167c: blr             lr
    // 0xeb1680: mov             x3, x0
    // 0xeb1684: stur            x3, [fp, #-0x38]
    // 0xeb1688: r0 = LoadClassIdInstr(r3)
    //     0xeb1688: ldur            x0, [x3, #-1]
    //     0xeb168c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1690: cmp             x0, #0x2f3
    // 0xeb1694: b.eq            #0xeb176c
    // 0xeb1698: cmp             x0, #0x2f3
    // 0xeb169c: b.eq            #0xeb1974
    // 0xeb16a0: ldur            x4, [fp, #-0x30]
    // 0xeb16a4: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xeb16a4: ldur            w5, [x3, #0x17]
    // 0xeb16a8: DecompressPointer r5
    //     0xeb16a8: add             x5, x5, HEAP, lsl #32
    // 0xeb16ac: mov             x0, x5
    // 0xeb16b0: ldur            x2, [fp, #-0x20]
    // 0xeb16b4: stur            x5, [fp, #-0x18]
    // 0xeb16b8: r1 = Null
    //     0xeb16b8: mov             x1, NULL
    // 0xeb16bc: cmp             w2, NULL
    // 0xeb16c0: b.eq            #0xeb16e0
    // 0xeb16c4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xeb16c4: ldur            w4, [x2, #0x17]
    // 0xeb16c8: DecompressPointer r4
    //     0xeb16c8: add             x4, x4, HEAP, lsl #32
    // 0xeb16cc: r8 = X0
    //     0xeb16cc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xeb16d0: LoadField: r9 = r4->field_7
    //     0xeb16d0: ldur            x9, [x4, #7]
    // 0xeb16d4: r3 = Null
    //     0xeb16d4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31238] Null
    //     0xeb16d8: ldr             x3, [x3, #0x238]
    // 0xeb16dc: blr             x9
    // 0xeb16e0: ldur            x0, [fp, #-0x30]
    // 0xeb16e4: LoadField: r1 = r0->field_b
    //     0xeb16e4: ldur            w1, [x0, #0xb]
    // 0xeb16e8: LoadField: r2 = r0->field_f
    //     0xeb16e8: ldur            w2, [x0, #0xf]
    // 0xeb16ec: DecompressPointer r2
    //     0xeb16ec: add             x2, x2, HEAP, lsl #32
    // 0xeb16f0: LoadField: r3 = r2->field_b
    //     0xeb16f0: ldur            w3, [x2, #0xb]
    // 0xeb16f4: r2 = LoadInt32Instr(r1)
    //     0xeb16f4: sbfx            x2, x1, #1, #0x1f
    // 0xeb16f8: stur            x2, [fp, #-0x40]
    // 0xeb16fc: r1 = LoadInt32Instr(r3)
    //     0xeb16fc: sbfx            x1, x3, #1, #0x1f
    // 0xeb1700: cmp             x2, x1
    // 0xeb1704: b.ne            #0xeb1710
    // 0xeb1708: mov             x1, x0
    // 0xeb170c: r0 = _growToNextCapacity()
    //     0xeb170c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeb1710: ldur            x3, [fp, #-0x30]
    // 0xeb1714: ldur            x2, [fp, #-0x40]
    // 0xeb1718: add             x0, x2, #1
    // 0xeb171c: lsl             x1, x0, #1
    // 0xeb1720: StoreField: r3->field_b = r1
    //     0xeb1720: stur            w1, [x3, #0xb]
    // 0xeb1724: LoadField: r1 = r3->field_f
    //     0xeb1724: ldur            w1, [x3, #0xf]
    // 0xeb1728: DecompressPointer r1
    //     0xeb1728: add             x1, x1, HEAP, lsl #32
    // 0xeb172c: ldur            x0, [fp, #-0x18]
    // 0xeb1730: ArrayStore: r1[r2] = r0  ; List_4
    //     0xeb1730: add             x25, x1, x2, lsl #2
    //     0xeb1734: add             x25, x25, #0xf
    //     0xeb1738: str             w0, [x25]
    //     0xeb173c: tbz             w0, #0, #0xeb1758
    //     0xeb1740: ldurb           w16, [x1, #-1]
    //     0xeb1744: ldurb           w17, [x0, #-1]
    //     0xeb1748: and             x16, x17, x16, lsr #2
    //     0xeb174c: tst             x16, HEAP, lsr #32
    //     0xeb1750: b.eq            #0xeb1758
    //     0xeb1754: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeb1758: ldur            x2, [fp, #-0x38]
    // 0xeb175c: mov             x4, x3
    // 0xeb1760: ldur            x3, [fp, #-0x10]
    // 0xeb1764: ldur            x5, [fp, #-0x28]
    // 0xeb1768: b               #0xeb1648
    // 0xeb176c: ldur            x0, [fp, #-0x38]
    // 0xeb1770: LeaveFrame
    //     0xeb1770: mov             SP, fp
    //     0xeb1774: ldp             fp, lr, [SP], #0x10
    // 0xeb1778: ret
    //     0xeb1778: ret             
    // 0xeb177c: mov             x16, x4
    // 0xeb1780: mov             x4, x3
    // 0xeb1784: mov             x3, x16
    // 0xeb1788: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xeb1788: ldur            x5, [x4, #0x17]
    // 0xeb178c: stur            x5, [fp, #-0x28]
    // 0xeb1790: mov             x6, x2
    // 0xeb1794: stur            x6, [fp, #-0x18]
    // 0xeb1798: CheckStackOverflow
    //     0xeb1798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb179c: cmp             SP, x16
    //     0xeb17a0: b.ls            #0xeb19c4
    // 0xeb17a4: LoadField: r1 = r4->field_1f
    //     0xeb17a4: ldur            w1, [x4, #0x1f]
    // 0xeb17a8: DecompressPointer r1
    //     0xeb17a8: add             x1, x1, HEAP, lsl #32
    // 0xeb17ac: r0 = LoadClassIdInstr(r1)
    //     0xeb17ac: ldur            x0, [x1, #-1]
    //     0xeb17b0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb17b4: mov             x2, x6
    // 0xeb17b8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb17b8: sub             lr, x0, #1, lsl #12
    //     0xeb17bc: ldr             lr, [x21, lr, lsl #3]
    //     0xeb17c0: blr             lr
    // 0xeb17c4: mov             x3, x0
    // 0xeb17c8: stur            x3, [fp, #-0x48]
    // 0xeb17cc: r0 = LoadClassIdInstr(r3)
    //     0xeb17cc: ldur            x0, [x3, #-1]
    //     0xeb17d0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb17d4: cmp             x0, #0x2f3
    // 0xeb17d8: b.ne            #0xeb1924
    // 0xeb17dc: ldur            x4, [fp, #-0x30]
    // 0xeb17e0: ldur            x5, [fp, #-0x28]
    // 0xeb17e4: LoadField: r0 = r4->field_b
    //     0xeb17e4: ldur            w0, [x4, #0xb]
    // 0xeb17e8: r1 = LoadInt32Instr(r0)
    //     0xeb17e8: sbfx            x1, x0, #1, #0x1f
    // 0xeb17ec: cmp             x1, x5
    // 0xeb17f0: b.ge            #0xeb1914
    // 0xeb17f4: ldur            x6, [fp, #-0x10]
    // 0xeb17f8: LoadField: r1 = r6->field_b
    //     0xeb17f8: ldur            w1, [x6, #0xb]
    // 0xeb17fc: DecompressPointer r1
    //     0xeb17fc: add             x1, x1, HEAP, lsl #32
    // 0xeb1800: r0 = LoadClassIdInstr(r1)
    //     0xeb1800: ldur            x0, [x1, #-1]
    //     0xeb1804: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1808: ldur            x2, [fp, #-0x18]
    // 0xeb180c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb180c: sub             lr, x0, #1, lsl #12
    //     0xeb1810: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1814: blr             lr
    // 0xeb1818: mov             x3, x0
    // 0xeb181c: stur            x3, [fp, #-0x58]
    // 0xeb1820: r0 = LoadClassIdInstr(r3)
    //     0xeb1820: ldur            x0, [x3, #-1]
    //     0xeb1824: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1828: cmp             x0, #0x2f3
    // 0xeb182c: b.eq            #0xeb1904
    // 0xeb1830: cmp             x0, #0x2f3
    // 0xeb1834: b.eq            #0xeb1994
    // 0xeb1838: ldur            x4, [fp, #-0x30]
    // 0xeb183c: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xeb183c: ldur            w5, [x3, #0x17]
    // 0xeb1840: DecompressPointer r5
    //     0xeb1840: add             x5, x5, HEAP, lsl #32
    // 0xeb1844: mov             x0, x5
    // 0xeb1848: ldur            x2, [fp, #-0x20]
    // 0xeb184c: stur            x5, [fp, #-0x50]
    // 0xeb1850: r1 = Null
    //     0xeb1850: mov             x1, NULL
    // 0xeb1854: cmp             w2, NULL
    // 0xeb1858: b.eq            #0xeb1878
    // 0xeb185c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xeb185c: ldur            w4, [x2, #0x17]
    // 0xeb1860: DecompressPointer r4
    //     0xeb1860: add             x4, x4, HEAP, lsl #32
    // 0xeb1864: r8 = X0
    //     0xeb1864: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xeb1868: LoadField: r9 = r4->field_7
    //     0xeb1868: ldur            x9, [x4, #7]
    // 0xeb186c: r3 = Null
    //     0xeb186c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31248] Null
    //     0xeb1870: ldr             x3, [x3, #0x248]
    // 0xeb1874: blr             x9
    // 0xeb1878: ldur            x0, [fp, #-0x30]
    // 0xeb187c: LoadField: r1 = r0->field_b
    //     0xeb187c: ldur            w1, [x0, #0xb]
    // 0xeb1880: LoadField: r2 = r0->field_f
    //     0xeb1880: ldur            w2, [x0, #0xf]
    // 0xeb1884: DecompressPointer r2
    //     0xeb1884: add             x2, x2, HEAP, lsl #32
    // 0xeb1888: LoadField: r3 = r2->field_b
    //     0xeb1888: ldur            w3, [x2, #0xb]
    // 0xeb188c: r2 = LoadInt32Instr(r1)
    //     0xeb188c: sbfx            x2, x1, #1, #0x1f
    // 0xeb1890: stur            x2, [fp, #-0x40]
    // 0xeb1894: r1 = LoadInt32Instr(r3)
    //     0xeb1894: sbfx            x1, x3, #1, #0x1f
    // 0xeb1898: cmp             x2, x1
    // 0xeb189c: b.ne            #0xeb18a8
    // 0xeb18a0: mov             x1, x0
    // 0xeb18a4: r0 = _growToNextCapacity()
    //     0xeb18a4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeb18a8: ldur            x2, [fp, #-0x30]
    // 0xeb18ac: ldur            x3, [fp, #-0x40]
    // 0xeb18b0: add             x0, x3, #1
    // 0xeb18b4: lsl             x1, x0, #1
    // 0xeb18b8: StoreField: r2->field_b = r1
    //     0xeb18b8: stur            w1, [x2, #0xb]
    // 0xeb18bc: LoadField: r1 = r2->field_f
    //     0xeb18bc: ldur            w1, [x2, #0xf]
    // 0xeb18c0: DecompressPointer r1
    //     0xeb18c0: add             x1, x1, HEAP, lsl #32
    // 0xeb18c4: ldur            x0, [fp, #-0x50]
    // 0xeb18c8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xeb18c8: add             x25, x1, x3, lsl #2
    //     0xeb18cc: add             x25, x25, #0xf
    //     0xeb18d0: str             w0, [x25]
    //     0xeb18d4: tbz             w0, #0, #0xeb18f0
    //     0xeb18d8: ldurb           w16, [x1, #-1]
    //     0xeb18dc: ldurb           w17, [x0, #-1]
    //     0xeb18e0: and             x16, x17, x16, lsr #2
    //     0xeb18e4: tst             x16, HEAP, lsr #32
    //     0xeb18e8: b.eq            #0xeb18f0
    //     0xeb18ec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeb18f0: ldur            x6, [fp, #-0x58]
    // 0xeb18f4: ldur            x4, [fp, #-0x10]
    // 0xeb18f8: mov             x3, x2
    // 0xeb18fc: ldur            x5, [fp, #-0x28]
    // 0xeb1900: b               #0xeb1794
    // 0xeb1904: ldur            x0, [fp, #-0x48]
    // 0xeb1908: LeaveFrame
    //     0xeb1908: mov             SP, fp
    //     0xeb190c: ldp             fp, lr, [SP], #0x10
    // 0xeb1910: ret
    //     0xeb1910: ret             
    // 0xeb1914: ldur            x0, [fp, #-0x48]
    // 0xeb1918: LeaveFrame
    //     0xeb1918: mov             SP, fp
    //     0xeb191c: ldp             fp, lr, [SP], #0x10
    // 0xeb1920: ret
    //     0xeb1920: ret             
    // 0xeb1924: ldur            x2, [fp, #-0x30]
    // 0xeb1928: ldur            x0, [fp, #-0x18]
    // 0xeb192c: LoadField: r3 = r0->field_7
    //     0xeb192c: ldur            w3, [x0, #7]
    // 0xeb1930: DecompressPointer r3
    //     0xeb1930: add             x3, x3, HEAP, lsl #32
    // 0xeb1934: stur            x3, [fp, #-0x10]
    // 0xeb1938: LoadField: r4 = r0->field_b
    //     0xeb1938: ldur            x4, [x0, #0xb]
    // 0xeb193c: ldur            x1, [fp, #-8]
    // 0xeb1940: stur            x4, [fp, #-0x28]
    // 0xeb1944: r0 = Success()
    //     0xeb1944: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb1948: mov             x1, x0
    // 0xeb194c: ldur            x0, [fp, #-0x30]
    // 0xeb1950: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb1950: stur            w0, [x1, #0x17]
    // 0xeb1954: ldur            x0, [fp, #-0x10]
    // 0xeb1958: StoreField: r1->field_7 = r0
    //     0xeb1958: stur            w0, [x1, #7]
    // 0xeb195c: ldur            x0, [fp, #-0x28]
    // 0xeb1960: StoreField: r1->field_b = r0
    //     0xeb1960: stur            x0, [x1, #0xb]
    // 0xeb1964: mov             x0, x1
    // 0xeb1968: LeaveFrame
    //     0xeb1968: mov             SP, fp
    //     0xeb196c: ldp             fp, lr, [SP], #0x10
    // 0xeb1970: ret
    //     0xeb1970: ret             
    // 0xeb1974: mov             x0, x3
    // 0xeb1978: r0 = ParserException()
    //     0xeb1978: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb197c: mov             x1, x0
    // 0xeb1980: ldur            x0, [fp, #-0x38]
    // 0xeb1984: StoreField: r1->field_7 = r0
    //     0xeb1984: stur            w0, [x1, #7]
    // 0xeb1988: mov             x0, x1
    // 0xeb198c: r0 = Throw()
    //     0xeb198c: bl              #0xec04b8  ; ThrowStub
    // 0xeb1990: brk             #0
    // 0xeb1994: mov             x0, x3
    // 0xeb1998: r0 = ParserException()
    //     0xeb1998: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb199c: mov             x1, x0
    // 0xeb19a0: ldur            x0, [fp, #-0x58]
    // 0xeb19a4: StoreField: r1->field_7 = r0
    //     0xeb19a4: stur            w0, [x1, #7]
    // 0xeb19a8: mov             x0, x1
    // 0xeb19ac: r0 = Throw()
    //     0xeb19ac: bl              #0xec04b8  ; ThrowStub
    // 0xeb19b0: brk             #0
    // 0xeb19b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb19b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb19b8: b               #0xeb15ec
    // 0xeb19bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb19bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb19c0: b               #0xeb1654
    // 0xeb19c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb19c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb19c8: b               #0xeb17a4
  }
}
