// lib: , url: package:petitparser/src/parser/repeater/limited.dart

// class id: 1050913, size: 0x8
class :: {
}

// class id: 744, size: 0x24, field offset: 0x20
abstract class LimitedRepeatingParser<C3X0> extends RepeatingParser<C3X0, dynamic> {

  get _ children(/* No info */) {
    // ** addr: 0x88f8cc, size: 0x74
    // 0x88f8cc: EnterFrame
    //     0x88f8cc: stp             fp, lr, [SP, #-0x10]!
    //     0x88f8d0: mov             fp, SP
    // 0x88f8d4: AllocStack(0x18)
    //     0x88f8d4: sub             SP, SP, #0x18
    // 0x88f8d8: r0 = 4
    //     0x88f8d8: movz            x0, #0x4
    // 0x88f8dc: LoadField: r3 = r1->field_b
    //     0x88f8dc: ldur            w3, [x1, #0xb]
    // 0x88f8e0: DecompressPointer r3
    //     0x88f8e0: add             x3, x3, HEAP, lsl #32
    // 0x88f8e4: stur            x3, [fp, #-0x10]
    // 0x88f8e8: LoadField: r4 = r1->field_1f
    //     0x88f8e8: ldur            w4, [x1, #0x1f]
    // 0x88f8ec: DecompressPointer r4
    //     0x88f8ec: add             x4, x4, HEAP, lsl #32
    // 0x88f8f0: mov             x2, x0
    // 0x88f8f4: stur            x4, [fp, #-8]
    // 0x88f8f8: r1 = Null
    //     0x88f8f8: mov             x1, NULL
    // 0x88f8fc: r0 = AllocateArray()
    //     0x88f8fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88f900: mov             x2, x0
    // 0x88f904: ldur            x0, [fp, #-0x10]
    // 0x88f908: stur            x2, [fp, #-0x18]
    // 0x88f90c: StoreField: r2->field_f = r0
    //     0x88f90c: stur            w0, [x2, #0xf]
    // 0x88f910: ldur            x0, [fp, #-8]
    // 0x88f914: StoreField: r2->field_13 = r0
    //     0x88f914: stur            w0, [x2, #0x13]
    // 0x88f918: r1 = <Parser>
    //     0x88f918: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x88f91c: ldr             x1, [x1, #0x6f8]
    // 0x88f920: r0 = AllocateGrowableArray()
    //     0x88f920: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88f924: ldur            x1, [fp, #-0x18]
    // 0x88f928: StoreField: r0->field_f = r1
    //     0x88f928: stur            w1, [x0, #0xf]
    // 0x88f92c: r1 = 4
    //     0x88f92c: movz            x1, #0x4
    // 0x88f930: StoreField: r0->field_b = r1
    //     0x88f930: stur            w1, [x0, #0xb]
    // 0x88f934: LeaveFrame
    //     0x88f934: mov             SP, fp
    //     0x88f938: ldp             fp, lr, [SP], #0x10
    // 0x88f93c: ret
    //     0x88f93c: ret             
  }
  _ replace(/* No info */) {
    // ** addr: 0x893d14, size: 0xac
    // 0x893d14: EnterFrame
    //     0x893d14: stp             fp, lr, [SP, #-0x10]!
    //     0x893d18: mov             fp, SP
    // 0x893d1c: AllocStack(0x28)
    //     0x893d1c: sub             SP, SP, #0x28
    // 0x893d20: SetupParameters(LimitedRepeatingParser<C3X0> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x893d20: mov             x5, x1
    //     0x893d24: mov             x4, x2
    //     0x893d28: mov             x0, x3
    //     0x893d2c: stur            x1, [fp, #-8]
    //     0x893d30: stur            x2, [fp, #-0x10]
    //     0x893d34: stur            x3, [fp, #-0x18]
    // 0x893d38: CheckStackOverflow
    //     0x893d38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x893d3c: cmp             SP, x16
    //     0x893d40: b.ls            #0x893db8
    // 0x893d44: mov             x1, x5
    // 0x893d48: mov             x2, x4
    // 0x893d4c: mov             x3, x0
    // 0x893d50: r0 = replace()
    //     0x893d50: bl              #0x893dc0  ; [package:petitparser/src/parser/combinator/delegate.dart] DelegateParser::replace
    // 0x893d54: ldur            x1, [fp, #-8]
    // 0x893d58: LoadField: r0 = r1->field_1f
    //     0x893d58: ldur            w0, [x1, #0x1f]
    // 0x893d5c: DecompressPointer r0
    //     0x893d5c: add             x0, x0, HEAP, lsl #32
    // 0x893d60: r2 = LoadClassIdInstr(r0)
    //     0x893d60: ldur            x2, [x0, #-1]
    //     0x893d64: ubfx            x2, x2, #0xc, #0x14
    // 0x893d68: ldur            x16, [fp, #-0x10]
    // 0x893d6c: stp             x16, x0, [SP]
    // 0x893d70: mov             x0, x2
    // 0x893d74: mov             lr, x0
    // 0x893d78: ldr             lr, [x21, lr, lsl #3]
    // 0x893d7c: blr             lr
    // 0x893d80: tbnz            w0, #4, #0x893da8
    // 0x893d84: ldur            x1, [fp, #-8]
    // 0x893d88: ldur            x0, [fp, #-0x18]
    // 0x893d8c: StoreField: r1->field_1f = r0
    //     0x893d8c: stur            w0, [x1, #0x1f]
    //     0x893d90: ldurb           w16, [x1, #-1]
    //     0x893d94: ldurb           w17, [x0, #-1]
    //     0x893d98: and             x16, x17, x16, lsr #2
    //     0x893d9c: tst             x16, HEAP, lsr #32
    //     0x893da0: b.eq            #0x893da8
    //     0x893da4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x893da8: r0 = Null
    //     0x893da8: mov             x0, NULL
    // 0x893dac: LeaveFrame
    //     0x893dac: mov             SP, fp
    //     0x893db0: ldp             fp, lr, [SP], #0x10
    // 0x893db4: ret
    //     0x893db4: ret             
    // 0x893db8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x893db8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x893dbc: b               #0x893d44
  }
}
