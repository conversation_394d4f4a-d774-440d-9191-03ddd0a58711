// lib: , url: package:petitparser/src/parser/repeater/possessive.dart

// class id: 1050914, size: 0x8
class :: {

  static _ PossessiveRepeatingParserExtension.star(/* No info */) {
    // ** addr: 0x88cfa0, size: 0x60
    // 0x88cfa0: EnterFrame
    //     0x88cfa0: stp             fp, lr, [SP, #-0x10]!
    //     0x88cfa4: mov             fp, SP
    // 0x88cfa8: AllocStack(0x20)
    //     0x88cfa8: sub             SP, SP, #0x20
    // 0x88cfac: SetupParameters()
    //     0x88cfac: ldur            w0, [x4, #0xf]
    //     0x88cfb0: cbnz            w0, #0x88cfbc
    //     0x88cfb4: mov             x1, NULL
    //     0x88cfb8: b               #0x88cfc8
    //     0x88cfbc: ldur            w0, [x4, #0x17]
    //     0x88cfc0: add             x1, fp, w0, sxtw #2
    //     0x88cfc4: ldr             x1, [x1, #0x10]
    //     0x88cfc8: orr             x0, xzr, #0x1fffffffffffff
    // 0x88cfc8: r0 = 9007199254740991
    // 0x88cfcc: CheckStackOverflow
    //     0x88cfcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88cfd0: cmp             SP, x16
    //     0x88cfd4: b.ls            #0x88cff8
    // 0x88cfd8: ldr             x16, [fp, #0x10]
    // 0x88cfdc: stp             x16, x1, [SP, #0x10]
    // 0x88cfe0: stp             x0, xzr, [SP]
    // 0x88cfe4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x88cfe4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x88cfe8: r0 = PossessiveRepeatingParserExtension.repeat()
    //     0x88cfe8: bl              #0x88d000  ; [package:petitparser/src/parser/repeater/possessive.dart] ::PossessiveRepeatingParserExtension.repeat
    // 0x88cfec: LeaveFrame
    //     0x88cfec: mov             SP, fp
    //     0x88cff0: ldp             fp, lr, [SP], #0x10
    // 0x88cff4: ret
    //     0x88cff4: ret             
    // 0x88cff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88cff8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88cffc: b               #0x88cfd8
  }
  static Parser<List<Y0>> PossessiveRepeatingParserExtension.repeat<Y0>(Parser<Y0>, int, int?) {
    // ** addr: 0x88d000, size: 0x74
    // 0x88d000: EnterFrame
    //     0x88d000: stp             fp, lr, [SP, #-0x10]!
    //     0x88d004: mov             fp, SP
    // 0x88d008: LoadField: r0 = r4->field_f
    //     0x88d008: ldur            w0, [x4, #0xf]
    // 0x88d00c: cbnz            w0, #0x88d018
    // 0x88d010: r1 = Null
    //     0x88d010: mov             x1, NULL
    // 0x88d014: b               #0x88d024
    // 0x88d018: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x88d018: ldur            w0, [x4, #0x17]
    // 0x88d01c: add             x1, fp, w0, sxtw #2
    // 0x88d020: ldr             x1, [x1, #0x10]
    // 0x88d024: ldr             x5, [fp, #0x20]
    // 0x88d028: ldr             x4, [fp, #0x18]
    // 0x88d02c: ldr             x0, [fp, #0x10]
    // 0x88d030: r2 = Null
    //     0x88d030: mov             x2, NULL
    // 0x88d034: r3 = <List<Y0>, Y0, List<Y0>, Y0>
    //     0x88d034: add             x3, PP, #0x26, lsl #12  ; [pp+0x26a10] TypeArguments: <List<Y0>, Y0, List<Y0>, Y0>
    //     0x88d038: ldr             x3, [x3, #0xa10]
    // 0x88d03c: r30 = InstantiateTypeArgumentsStub
    //     0x88d03c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x88d040: LoadField: r30 = r30->field_7
    //     0x88d040: ldur            lr, [lr, #7]
    // 0x88d044: blr             lr
    // 0x88d048: mov             x1, x0
    // 0x88d04c: r0 = PossessiveRepeatingParser()
    //     0x88d04c: bl              #0x88d074  ; AllocatePossessiveRepeatingParserStub -> PossessiveRepeatingParser<C3X0> (size=0x20)
    // 0x88d050: ldr             x1, [fp, #0x18]
    // 0x88d054: StoreField: r0->field_f = r1
    //     0x88d054: stur            x1, [x0, #0xf]
    // 0x88d058: ldr             x1, [fp, #0x10]
    // 0x88d05c: ArrayStore: r0[0] = r1  ; List_8
    //     0x88d05c: stur            x1, [x0, #0x17]
    // 0x88d060: ldr             x1, [fp, #0x20]
    // 0x88d064: StoreField: r0->field_b = r1
    //     0x88d064: stur            w1, [x0, #0xb]
    // 0x88d068: LeaveFrame
    //     0x88d068: mov             SP, fp
    //     0x88d06c: ldp             fp, lr, [SP], #0x10
    // 0x88d070: ret
    //     0x88d070: ret             
  }
}

// class id: 743, size: 0x20, field offset: 0x20
class PossessiveRepeatingParser<C3X0> extends RepeatingParser<C3X0, dynamic> {

  _ fastParseOn(/* No info */) {
    // ** addr: 0xeafc54, size: 0x15c
    // 0xeafc54: EnterFrame
    //     0xeafc54: stp             fp, lr, [SP, #-0x10]!
    //     0xeafc58: mov             fp, SP
    // 0xeafc5c: AllocStack(0x28)
    //     0xeafc5c: sub             SP, SP, #0x28
    // 0xeafc60: SetupParameters(PossessiveRepeatingParser<C3X0> this /* r1 => r5, fp-0x18 */, dynamic _ /* r2 => r4, fp-0x20 */)
    //     0xeafc60: mov             x5, x1
    //     0xeafc64: mov             x4, x2
    //     0xeafc68: stur            x1, [fp, #-0x18]
    //     0xeafc6c: stur            x2, [fp, #-0x20]
    // 0xeafc70: CheckStackOverflow
    //     0xeafc70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeafc74: cmp             SP, x16
    //     0xeafc78: b.ls            #0xeafd98
    // 0xeafc7c: LoadField: r6 = r5->field_f
    //     0xeafc7c: ldur            x6, [x5, #0xf]
    // 0xeafc80: stur            x6, [fp, #-0x10]
    // 0xeafc84: r7 = 0
    //     0xeafc84: movz            x7, #0
    // 0xeafc88: stur            x7, [fp, #-8]
    // 0xeafc8c: CheckStackOverflow
    //     0xeafc8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeafc90: cmp             SP, x16
    //     0xeafc94: b.ls            #0xeafda0
    // 0xeafc98: cmp             x7, x6
    // 0xeafc9c: b.ge            #0xeafcf8
    // 0xeafca0: LoadField: r1 = r5->field_b
    //     0xeafca0: ldur            w1, [x5, #0xb]
    // 0xeafca4: DecompressPointer r1
    //     0xeafca4: add             x1, x1, HEAP, lsl #32
    // 0xeafca8: r0 = LoadClassIdInstr(r1)
    //     0xeafca8: ldur            x0, [x1, #-1]
    //     0xeafcac: ubfx            x0, x0, #0xc, #0x14
    // 0xeafcb0: mov             x2, x4
    // 0xeafcb4: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeafcb4: sub             lr, x0, #0xfce
    //     0xeafcb8: ldr             lr, [x21, lr, lsl #3]
    //     0xeafcbc: blr             lr
    // 0xeafcc0: r3 = LoadInt32Instr(r0)
    //     0xeafcc0: sbfx            x3, x0, #1, #0x1f
    //     0xeafcc4: tbz             w0, #0, #0xeafccc
    //     0xeafcc8: ldur            x3, [x0, #7]
    // 0xeafccc: tbnz            x3, #0x3f, #0xeafce8
    // 0xeafcd0: ldur            x0, [fp, #-8]
    // 0xeafcd4: add             x7, x0, #1
    // 0xeafcd8: ldur            x5, [fp, #-0x18]
    // 0xeafcdc: ldur            x4, [fp, #-0x20]
    // 0xeafce0: ldur            x6, [fp, #-0x10]
    // 0xeafce4: b               #0xeafc88
    // 0xeafce8: r0 = -2
    //     0xeafce8: orr             x0, xzr, #0xfffffffffffffffe
    // 0xeafcec: LeaveFrame
    //     0xeafcec: mov             SP, fp
    //     0xeafcf0: ldp             fp, lr, [SP], #0x10
    // 0xeafcf4: ret
    //     0xeafcf4: ret             
    // 0xeafcf8: mov             x4, x5
    // 0xeafcfc: mov             x0, x7
    // 0xeafd00: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xeafd00: ldur            x5, [x4, #0x17]
    // 0xeafd04: stur            x5, [fp, #-0x28]
    // 0xeafd08: mov             x7, x0
    // 0xeafd0c: mov             x6, x3
    // 0xeafd10: stur            x7, [fp, #-8]
    // 0xeafd14: stur            x6, [fp, #-0x10]
    // 0xeafd18: CheckStackOverflow
    //     0xeafd18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeafd1c: cmp             SP, x16
    //     0xeafd20: b.ls            #0xeafda8
    // 0xeafd24: cmp             x7, x5
    // 0xeafd28: b.ge            #0xeafd74
    // 0xeafd2c: LoadField: r1 = r4->field_b
    //     0xeafd2c: ldur            w1, [x4, #0xb]
    // 0xeafd30: DecompressPointer r1
    //     0xeafd30: add             x1, x1, HEAP, lsl #32
    // 0xeafd34: r0 = LoadClassIdInstr(r1)
    //     0xeafd34: ldur            x0, [x1, #-1]
    //     0xeafd38: ubfx            x0, x0, #0xc, #0x14
    // 0xeafd3c: ldur            x2, [fp, #-0x20]
    // 0xeafd40: mov             x3, x6
    // 0xeafd44: r0 = GDT[cid_x0 + -0xfce]()
    //     0xeafd44: sub             lr, x0, #0xfce
    //     0xeafd48: ldr             lr, [x21, lr, lsl #3]
    //     0xeafd4c: blr             lr
    // 0xeafd50: r6 = LoadInt32Instr(r0)
    //     0xeafd50: sbfx            x6, x0, #1, #0x1f
    //     0xeafd54: tbz             w0, #0, #0xeafd5c
    //     0xeafd58: ldur            x6, [x0, #7]
    // 0xeafd5c: tbnz            x6, #0x3f, #0xeafd74
    // 0xeafd60: ldur            x2, [fp, #-8]
    // 0xeafd64: add             x7, x2, #1
    // 0xeafd68: ldur            x4, [fp, #-0x18]
    // 0xeafd6c: ldur            x5, [fp, #-0x28]
    // 0xeafd70: b               #0xeafd10
    // 0xeafd74: ldur            x2, [fp, #-0x10]
    // 0xeafd78: r0 = BoxInt64Instr(r2)
    //     0xeafd78: sbfiz           x0, x2, #1, #0x1f
    //     0xeafd7c: cmp             x2, x0, asr #1
    //     0xeafd80: b.eq            #0xeafd8c
    //     0xeafd84: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeafd88: stur            x2, [x0, #7]
    // 0xeafd8c: LeaveFrame
    //     0xeafd8c: mov             SP, fp
    //     0xeafd90: ldp             fp, lr, [SP], #0x10
    // 0xeafd94: ret
    //     0xeafd94: ret             
    // 0xeafd98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeafd98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeafd9c: b               #0xeafc7c
    // 0xeafda0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeafda0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeafda4: b               #0xeafc98
    // 0xeafda8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeafda8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeafdac: b               #0xeafd24
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb19cc, size: 0x3b0
    // 0xeb19cc: EnterFrame
    //     0xeb19cc: stp             fp, lr, [SP, #-0x10]!
    //     0xeb19d0: mov             fp, SP
    // 0xeb19d4: AllocStack(0x50)
    //     0xeb19d4: sub             SP, SP, #0x50
    // 0xeb19d8: SetupParameters(PossessiveRepeatingParser<C3X0> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xeb19d8: mov             x4, x1
    //     0xeb19dc: mov             x0, x2
    //     0xeb19e0: stur            x1, [fp, #-0x10]
    //     0xeb19e4: stur            x2, [fp, #-0x18]
    // 0xeb19e8: CheckStackOverflow
    //     0xeb19e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb19ec: cmp             SP, x16
    //     0xeb19f0: b.ls            #0xeb1d64
    // 0xeb19f4: LoadField: r5 = r4->field_7
    //     0xeb19f4: ldur            w5, [x4, #7]
    // 0xeb19f8: DecompressPointer r5
    //     0xeb19f8: add             x5, x5, HEAP, lsl #32
    // 0xeb19fc: mov             x2, x5
    // 0xeb1a00: stur            x5, [fp, #-8]
    // 0xeb1a04: r1 = Null
    //     0xeb1a04: mov             x1, NULL
    // 0xeb1a08: r3 = <C3X0>
    //     0xeb1a08: add             x3, PP, #0x31, lsl #12  ; [pp+0x31230] TypeArguments: <C3X0>
    //     0xeb1a0c: ldr             x3, [x3, #0x230]
    // 0xeb1a10: r0 = Null
    //     0xeb1a10: mov             x0, NULL
    // 0xeb1a14: cmp             x2, x0
    // 0xeb1a18: b.eq            #0xeb1a28
    // 0xeb1a1c: r30 = InstantiateTypeArgumentsStub
    //     0xeb1a1c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xeb1a20: LoadField: r30 = r30->field_7
    //     0xeb1a20: ldur            lr, [lr, #7]
    // 0xeb1a24: blr             lr
    // 0xeb1a28: mov             x1, x0
    // 0xeb1a2c: r2 = 0
    //     0xeb1a2c: movz            x2, #0
    // 0xeb1a30: stur            x0, [fp, #-0x20]
    // 0xeb1a34: r0 = _GrowableList()
    //     0xeb1a34: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xeb1a38: mov             x4, x0
    // 0xeb1a3c: ldur            x3, [fp, #-0x10]
    // 0xeb1a40: stur            x4, [fp, #-0x30]
    // 0xeb1a44: LoadField: r5 = r3->field_f
    //     0xeb1a44: ldur            x5, [x3, #0xf]
    // 0xeb1a48: stur            x5, [fp, #-0x28]
    // 0xeb1a4c: ldur            x2, [fp, #-0x18]
    // 0xeb1a50: CheckStackOverflow
    //     0xeb1a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb1a54: cmp             SP, x16
    //     0xeb1a58: b.ls            #0xeb1d6c
    // 0xeb1a5c: LoadField: r0 = r4->field_b
    //     0xeb1a5c: ldur            w0, [x4, #0xb]
    // 0xeb1a60: r1 = LoadInt32Instr(r0)
    //     0xeb1a60: sbfx            x1, x0, #1, #0x1f
    // 0xeb1a64: cmp             x1, x5
    // 0xeb1a68: b.ge            #0xeb1b84
    // 0xeb1a6c: LoadField: r1 = r3->field_b
    //     0xeb1a6c: ldur            w1, [x3, #0xb]
    // 0xeb1a70: DecompressPointer r1
    //     0xeb1a70: add             x1, x1, HEAP, lsl #32
    // 0xeb1a74: r0 = LoadClassIdInstr(r1)
    //     0xeb1a74: ldur            x0, [x1, #-1]
    //     0xeb1a78: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1a7c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1a7c: sub             lr, x0, #1, lsl #12
    //     0xeb1a80: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1a84: blr             lr
    // 0xeb1a88: mov             x3, x0
    // 0xeb1a8c: stur            x3, [fp, #-0x38]
    // 0xeb1a90: r0 = LoadClassIdInstr(r3)
    //     0xeb1a90: ldur            x0, [x3, #-1]
    //     0xeb1a94: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1a98: cmp             x0, #0x2f3
    // 0xeb1a9c: b.eq            #0xeb1b74
    // 0xeb1aa0: cmp             x0, #0x2f3
    // 0xeb1aa4: b.eq            #0xeb1d24
    // 0xeb1aa8: ldur            x4, [fp, #-0x30]
    // 0xeb1aac: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xeb1aac: ldur            w5, [x3, #0x17]
    // 0xeb1ab0: DecompressPointer r5
    //     0xeb1ab0: add             x5, x5, HEAP, lsl #32
    // 0xeb1ab4: mov             x0, x5
    // 0xeb1ab8: ldur            x2, [fp, #-0x20]
    // 0xeb1abc: stur            x5, [fp, #-0x18]
    // 0xeb1ac0: r1 = Null
    //     0xeb1ac0: mov             x1, NULL
    // 0xeb1ac4: cmp             w2, NULL
    // 0xeb1ac8: b.eq            #0xeb1ae8
    // 0xeb1acc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xeb1acc: ldur            w4, [x2, #0x17]
    // 0xeb1ad0: DecompressPointer r4
    //     0xeb1ad0: add             x4, x4, HEAP, lsl #32
    // 0xeb1ad4: r8 = X0
    //     0xeb1ad4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xeb1ad8: LoadField: r9 = r4->field_7
    //     0xeb1ad8: ldur            x9, [x4, #7]
    // 0xeb1adc: r3 = Null
    //     0xeb1adc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31418] Null
    //     0xeb1ae0: ldr             x3, [x3, #0x418]
    // 0xeb1ae4: blr             x9
    // 0xeb1ae8: ldur            x0, [fp, #-0x30]
    // 0xeb1aec: LoadField: r1 = r0->field_b
    //     0xeb1aec: ldur            w1, [x0, #0xb]
    // 0xeb1af0: LoadField: r2 = r0->field_f
    //     0xeb1af0: ldur            w2, [x0, #0xf]
    // 0xeb1af4: DecompressPointer r2
    //     0xeb1af4: add             x2, x2, HEAP, lsl #32
    // 0xeb1af8: LoadField: r3 = r2->field_b
    //     0xeb1af8: ldur            w3, [x2, #0xb]
    // 0xeb1afc: r2 = LoadInt32Instr(r1)
    //     0xeb1afc: sbfx            x2, x1, #1, #0x1f
    // 0xeb1b00: stur            x2, [fp, #-0x40]
    // 0xeb1b04: r1 = LoadInt32Instr(r3)
    //     0xeb1b04: sbfx            x1, x3, #1, #0x1f
    // 0xeb1b08: cmp             x2, x1
    // 0xeb1b0c: b.ne            #0xeb1b18
    // 0xeb1b10: mov             x1, x0
    // 0xeb1b14: r0 = _growToNextCapacity()
    //     0xeb1b14: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeb1b18: ldur            x3, [fp, #-0x30]
    // 0xeb1b1c: ldur            x2, [fp, #-0x40]
    // 0xeb1b20: add             x0, x2, #1
    // 0xeb1b24: lsl             x1, x0, #1
    // 0xeb1b28: StoreField: r3->field_b = r1
    //     0xeb1b28: stur            w1, [x3, #0xb]
    // 0xeb1b2c: LoadField: r1 = r3->field_f
    //     0xeb1b2c: ldur            w1, [x3, #0xf]
    // 0xeb1b30: DecompressPointer r1
    //     0xeb1b30: add             x1, x1, HEAP, lsl #32
    // 0xeb1b34: ldur            x0, [fp, #-0x18]
    // 0xeb1b38: ArrayStore: r1[r2] = r0  ; List_4
    //     0xeb1b38: add             x25, x1, x2, lsl #2
    //     0xeb1b3c: add             x25, x25, #0xf
    //     0xeb1b40: str             w0, [x25]
    //     0xeb1b44: tbz             w0, #0, #0xeb1b60
    //     0xeb1b48: ldurb           w16, [x1, #-1]
    //     0xeb1b4c: ldurb           w17, [x0, #-1]
    //     0xeb1b50: and             x16, x17, x16, lsr #2
    //     0xeb1b54: tst             x16, HEAP, lsr #32
    //     0xeb1b58: b.eq            #0xeb1b60
    //     0xeb1b5c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeb1b60: ldur            x2, [fp, #-0x38]
    // 0xeb1b64: mov             x4, x3
    // 0xeb1b68: ldur            x3, [fp, #-0x10]
    // 0xeb1b6c: ldur            x5, [fp, #-0x28]
    // 0xeb1b70: b               #0xeb1a50
    // 0xeb1b74: ldur            x0, [fp, #-0x38]
    // 0xeb1b78: LeaveFrame
    //     0xeb1b78: mov             SP, fp
    //     0xeb1b7c: ldp             fp, lr, [SP], #0x10
    // 0xeb1b80: ret
    //     0xeb1b80: ret             
    // 0xeb1b84: mov             x16, x4
    // 0xeb1b88: mov             x4, x3
    // 0xeb1b8c: mov             x3, x16
    // 0xeb1b90: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xeb1b90: ldur            x5, [x4, #0x17]
    // 0xeb1b94: stur            x5, [fp, #-0x28]
    // 0xeb1b98: r1 = LoadInt32Instr(r0)
    //     0xeb1b98: sbfx            x1, x0, #1, #0x1f
    // 0xeb1b9c: mov             x6, x2
    // 0xeb1ba0: mov             x0, x1
    // 0xeb1ba4: stur            x6, [fp, #-0x18]
    // 0xeb1ba8: CheckStackOverflow
    //     0xeb1ba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb1bac: cmp             SP, x16
    //     0xeb1bb0: b.ls            #0xeb1d74
    // 0xeb1bb4: cmp             x0, x5
    // 0xeb1bb8: b.ge            #0xeb1cd4
    // 0xeb1bbc: LoadField: r1 = r4->field_b
    //     0xeb1bbc: ldur            w1, [x4, #0xb]
    // 0xeb1bc0: DecompressPointer r1
    //     0xeb1bc0: add             x1, x1, HEAP, lsl #32
    // 0xeb1bc4: r0 = LoadClassIdInstr(r1)
    //     0xeb1bc4: ldur            x0, [x1, #-1]
    //     0xeb1bc8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1bcc: mov             x2, x6
    // 0xeb1bd0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb1bd0: sub             lr, x0, #1, lsl #12
    //     0xeb1bd4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb1bd8: blr             lr
    // 0xeb1bdc: mov             x3, x0
    // 0xeb1be0: stur            x3, [fp, #-0x50]
    // 0xeb1be4: r0 = LoadClassIdInstr(r3)
    //     0xeb1be4: ldur            x0, [x3, #-1]
    //     0xeb1be8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb1bec: cmp             x0, #0x2f3
    // 0xeb1bf0: b.eq            #0xeb1ccc
    // 0xeb1bf4: cmp             x0, #0x2f3
    // 0xeb1bf8: b.eq            #0xeb1d44
    // 0xeb1bfc: ldur            x4, [fp, #-0x30]
    // 0xeb1c00: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xeb1c00: ldur            w5, [x3, #0x17]
    // 0xeb1c04: DecompressPointer r5
    //     0xeb1c04: add             x5, x5, HEAP, lsl #32
    // 0xeb1c08: mov             x0, x5
    // 0xeb1c0c: ldur            x2, [fp, #-0x20]
    // 0xeb1c10: stur            x5, [fp, #-0x48]
    // 0xeb1c14: r1 = Null
    //     0xeb1c14: mov             x1, NULL
    // 0xeb1c18: cmp             w2, NULL
    // 0xeb1c1c: b.eq            #0xeb1c3c
    // 0xeb1c20: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xeb1c20: ldur            w4, [x2, #0x17]
    // 0xeb1c24: DecompressPointer r4
    //     0xeb1c24: add             x4, x4, HEAP, lsl #32
    // 0xeb1c28: r8 = X0
    //     0xeb1c28: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xeb1c2c: LoadField: r9 = r4->field_7
    //     0xeb1c2c: ldur            x9, [x4, #7]
    // 0xeb1c30: r3 = Null
    //     0xeb1c30: add             x3, PP, #0x31, lsl #12  ; [pp+0x31428] Null
    //     0xeb1c34: ldr             x3, [x3, #0x428]
    // 0xeb1c38: blr             x9
    // 0xeb1c3c: ldur            x0, [fp, #-0x30]
    // 0xeb1c40: LoadField: r1 = r0->field_b
    //     0xeb1c40: ldur            w1, [x0, #0xb]
    // 0xeb1c44: LoadField: r2 = r0->field_f
    //     0xeb1c44: ldur            w2, [x0, #0xf]
    // 0xeb1c48: DecompressPointer r2
    //     0xeb1c48: add             x2, x2, HEAP, lsl #32
    // 0xeb1c4c: LoadField: r3 = r2->field_b
    //     0xeb1c4c: ldur            w3, [x2, #0xb]
    // 0xeb1c50: r2 = LoadInt32Instr(r1)
    //     0xeb1c50: sbfx            x2, x1, #1, #0x1f
    // 0xeb1c54: stur            x2, [fp, #-0x40]
    // 0xeb1c58: r1 = LoadInt32Instr(r3)
    //     0xeb1c58: sbfx            x1, x3, #1, #0x1f
    // 0xeb1c5c: cmp             x2, x1
    // 0xeb1c60: b.ne            #0xeb1c6c
    // 0xeb1c64: mov             x1, x0
    // 0xeb1c68: r0 = _growToNextCapacity()
    //     0xeb1c68: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xeb1c6c: ldur            x2, [fp, #-0x30]
    // 0xeb1c70: ldur            x3, [fp, #-0x40]
    // 0xeb1c74: add             x4, x3, #1
    // 0xeb1c78: lsl             x0, x4, #1
    // 0xeb1c7c: StoreField: r2->field_b = r0
    //     0xeb1c7c: stur            w0, [x2, #0xb]
    // 0xeb1c80: LoadField: r1 = r2->field_f
    //     0xeb1c80: ldur            w1, [x2, #0xf]
    // 0xeb1c84: DecompressPointer r1
    //     0xeb1c84: add             x1, x1, HEAP, lsl #32
    // 0xeb1c88: ldur            x0, [fp, #-0x48]
    // 0xeb1c8c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xeb1c8c: add             x25, x1, x3, lsl #2
    //     0xeb1c90: add             x25, x25, #0xf
    //     0xeb1c94: str             w0, [x25]
    //     0xeb1c98: tbz             w0, #0, #0xeb1cb4
    //     0xeb1c9c: ldurb           w16, [x1, #-1]
    //     0xeb1ca0: ldurb           w17, [x0, #-1]
    //     0xeb1ca4: and             x16, x17, x16, lsr #2
    //     0xeb1ca8: tst             x16, HEAP, lsr #32
    //     0xeb1cac: b.eq            #0xeb1cb4
    //     0xeb1cb0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeb1cb4: ldur            x6, [fp, #-0x50]
    // 0xeb1cb8: mov             x0, x4
    // 0xeb1cbc: ldur            x4, [fp, #-0x10]
    // 0xeb1cc0: mov             x3, x2
    // 0xeb1cc4: ldur            x5, [fp, #-0x28]
    // 0xeb1cc8: b               #0xeb1ba4
    // 0xeb1ccc: ldur            x2, [fp, #-0x30]
    // 0xeb1cd0: b               #0xeb1cd8
    // 0xeb1cd4: mov             x2, x3
    // 0xeb1cd8: ldur            x0, [fp, #-0x18]
    // 0xeb1cdc: LoadField: r3 = r0->field_7
    //     0xeb1cdc: ldur            w3, [x0, #7]
    // 0xeb1ce0: DecompressPointer r3
    //     0xeb1ce0: add             x3, x3, HEAP, lsl #32
    // 0xeb1ce4: stur            x3, [fp, #-0x10]
    // 0xeb1ce8: LoadField: r4 = r0->field_b
    //     0xeb1ce8: ldur            x4, [x0, #0xb]
    // 0xeb1cec: ldur            x1, [fp, #-8]
    // 0xeb1cf0: stur            x4, [fp, #-0x28]
    // 0xeb1cf4: r0 = Success()
    //     0xeb1cf4: bl              #0xeb10c4  ; AllocateSuccessStub -> Success<X0> (size=0x1c)
    // 0xeb1cf8: mov             x1, x0
    // 0xeb1cfc: ldur            x0, [fp, #-0x30]
    // 0xeb1d00: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb1d00: stur            w0, [x1, #0x17]
    // 0xeb1d04: ldur            x0, [fp, #-0x10]
    // 0xeb1d08: StoreField: r1->field_7 = r0
    //     0xeb1d08: stur            w0, [x1, #7]
    // 0xeb1d0c: ldur            x0, [fp, #-0x28]
    // 0xeb1d10: StoreField: r1->field_b = r0
    //     0xeb1d10: stur            x0, [x1, #0xb]
    // 0xeb1d14: mov             x0, x1
    // 0xeb1d18: LeaveFrame
    //     0xeb1d18: mov             SP, fp
    //     0xeb1d1c: ldp             fp, lr, [SP], #0x10
    // 0xeb1d20: ret
    //     0xeb1d20: ret             
    // 0xeb1d24: mov             x0, x3
    // 0xeb1d28: r0 = ParserException()
    //     0xeb1d28: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb1d2c: mov             x1, x0
    // 0xeb1d30: ldur            x0, [fp, #-0x38]
    // 0xeb1d34: StoreField: r1->field_7 = r0
    //     0xeb1d34: stur            w0, [x1, #7]
    // 0xeb1d38: mov             x0, x1
    // 0xeb1d3c: r0 = Throw()
    //     0xeb1d3c: bl              #0xec04b8  ; ThrowStub
    // 0xeb1d40: brk             #0
    // 0xeb1d44: mov             x0, x3
    // 0xeb1d48: r0 = ParserException()
    //     0xeb1d48: bl              #0x74757c  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0xeb1d4c: mov             x1, x0
    // 0xeb1d50: ldur            x0, [fp, #-0x50]
    // 0xeb1d54: StoreField: r1->field_7 = r0
    //     0xeb1d54: stur            w0, [x1, #7]
    // 0xeb1d58: mov             x0, x1
    // 0xeb1d5c: r0 = Throw()
    //     0xeb1d5c: bl              #0xec04b8  ; ThrowStub
    // 0xeb1d60: brk             #0
    // 0xeb1d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb1d64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb1d68: b               #0xeb19f4
    // 0xeb1d6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb1d6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb1d70: b               #0xeb1a5c
    // 0xeb1d74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb1d74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb1d78: b               #0xeb1bb4
  }
}
