// lib: , url: package:petitparser/src/parser/repeater/repeating.dart

// class id: 1050915, size: 0x8
class :: {
}

// class id: 742, size: 0x20, field offset: 0x10
abstract class RepeatingParser<C1X0, C1X1> extends DelegateParser<C1X0, C1X1> {

  _ toString(/* No info */) {
    // ** addr: 0xc3bd6c, size: 0xc8
    // 0xc3bd6c: EnterFrame
    //     0xc3bd6c: stp             fp, lr, [SP, #-0x10]!
    //     0xc3bd70: mov             fp, SP
    // 0xc3bd74: AllocStack(0x10)
    //     0xc3bd74: sub             SP, SP, #0x10
    // 0xc3bd78: CheckStackOverflow
    //     0xc3bd78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3bd7c: cmp             SP, x16
    //     0xc3bd80: b.ls            #0xc3be2c
    // 0xc3bd84: ldr             x16, [fp, #0x10]
    // 0xc3bd88: str             x16, [SP]
    // 0xc3bd8c: r0 = toString()
    //     0xc3bd8c: bl              #0xc3c084  ; [package:petitparser/src/core/parser.dart] Parser::toString
    // 0xc3bd90: r1 = Null
    //     0xc3bd90: mov             x1, NULL
    // 0xc3bd94: r2 = 12
    //     0xc3bd94: movz            x2, #0xc
    // 0xc3bd98: stur            x0, [fp, #-8]
    // 0xc3bd9c: r0 = AllocateArray()
    //     0xc3bd9c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3bda0: mov             x2, x0
    // 0xc3bda4: ldur            x0, [fp, #-8]
    // 0xc3bda8: StoreField: r2->field_f = r0
    //     0xc3bda8: stur            w0, [x2, #0xf]
    // 0xc3bdac: r16 = "["
    //     0xc3bdac: ldr             x16, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xc3bdb0: StoreField: r2->field_13 = r16
    //     0xc3bdb0: stur            w16, [x2, #0x13]
    // 0xc3bdb4: ldr             x3, [fp, #0x10]
    // 0xc3bdb8: LoadField: r4 = r3->field_f
    //     0xc3bdb8: ldur            x4, [x3, #0xf]
    // 0xc3bdbc: r0 = BoxInt64Instr(r4)
    //     0xc3bdbc: sbfiz           x0, x4, #1, #0x1f
    //     0xc3bdc0: cmp             x4, x0, asr #1
    //     0xc3bdc4: b.eq            #0xc3bdd0
    //     0xc3bdc8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3bdcc: stur            x4, [x0, #7]
    // 0xc3bdd0: ArrayStore: r2[0] = r0  ; List_4
    //     0xc3bdd0: stur            w0, [x2, #0x17]
    // 0xc3bdd4: r16 = ".."
    //     0xc3bdd4: ldr             x16, [PP, #0xc30]  ; [pp+0xc30] ".."
    // 0xc3bdd8: StoreField: r2->field_1b = r16
    //     0xc3bdd8: stur            w16, [x2, #0x1b]
    // 0xc3bddc: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xc3bddc: ldur            x4, [x3, #0x17]
    // 0xc3bde0: r17 = 9007199254740991
    //     0xc3bde0: orr             x17, xzr, #0x1fffffffffffff
    // 0xc3bde4: cmp             x4, x17
    // 0xc3bde8: b.ne            #0xc3bdf8
    // 0xc3bdec: r0 = "*"
    //     0xc3bdec: add             x0, PP, #0x2a, lsl #12  ; [pp+0x2ad20] "*"
    //     0xc3bdf0: ldr             x0, [x0, #0xd20]
    // 0xc3bdf4: b               #0xc3be0c
    // 0xc3bdf8: r0 = BoxInt64Instr(r4)
    //     0xc3bdf8: sbfiz           x0, x4, #1, #0x1f
    //     0xc3bdfc: cmp             x4, x0, asr #1
    //     0xc3be00: b.eq            #0xc3be0c
    //     0xc3be04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3be08: stur            x4, [x0, #7]
    // 0xc3be0c: StoreField: r2->field_1f = r0
    //     0xc3be0c: stur            w0, [x2, #0x1f]
    // 0xc3be10: r16 = "]"
    //     0xc3be10: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc3be14: StoreField: r2->field_23 = r16
    //     0xc3be14: stur            w16, [x2, #0x23]
    // 0xc3be18: str             x2, [SP]
    // 0xc3be1c: r0 = _interpolate()
    //     0xc3be1c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3be20: LeaveFrame
    //     0xc3be20: mov             SP, fp
    //     0xc3be24: ldp             fp, lr, [SP], #0x10
    // 0xc3be28: ret
    //     0xc3be28: ret             
    // 0xc3be2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3be2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3be30: b               #0xc3bd84
  }
}
