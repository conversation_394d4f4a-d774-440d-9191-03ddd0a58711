// lib: , url: package:petitparser/src/parser/utils/failure_joiner.dart

// class id: 1050916, size: 0x8
class :: {

  [closure] static Failure selectFarthest(dynamic, Failure, Failure) {
    // ** addr: 0x88a7b4, size: 0x28
    // 0x88a7b4: ldr             x1, [SP, #8]
    // 0x88a7b8: LoadField: r2 = r1->field_b
    //     0x88a7b8: ldur            x2, [x1, #0xb]
    // 0x88a7bc: ldr             x3, [SP]
    // 0x88a7c0: LoadField: r4 = r3->field_b
    //     0x88a7c0: ldur            x4, [x3, #0xb]
    // 0x88a7c4: cmp             x2, x4
    // 0x88a7c8: b.gt            #0x88a7d4
    // 0x88a7cc: mov             x0, x3
    // 0x88a7d0: b               #0x88a7d8
    // 0x88a7d4: mov             x0, x1
    // 0x88a7d8: ret
    //     0x88a7d8: ret             
  }
}
