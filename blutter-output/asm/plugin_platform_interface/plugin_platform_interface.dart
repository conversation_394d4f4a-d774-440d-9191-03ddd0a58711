// lib: , url: package:plugin_platform_interface/plugin_platform_interface.dart

// class id: 1050921, size: 0x8
class :: {
}

// class id: 5848, size: 0x8, field offset: 0x8
abstract class PlatformInterface extends Object {

  static late final Expando<Object> _instanceTokens; // offset: 0x604

  _ PlatformInterface(/* No info */) {
    // ** addr: 0x7d9fb8, size: 0x6c
    // 0x7d9fb8: EnterFrame
    //     0x7d9fb8: stp             fp, lr, [SP, #-0x10]!
    //     0x7d9fbc: mov             fp, SP
    // 0x7d9fc0: AllocStack(0x10)
    //     0x7d9fc0: sub             SP, SP, #0x10
    // 0x7d9fc4: SetupParameters(PlatformInterface this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7d9fc4: mov             x3, x2
    //     0x7d9fc8: stur            x2, [fp, #-0x10]
    //     0x7d9fcc: mov             x2, x1
    //     0x7d9fd0: stur            x1, [fp, #-8]
    // 0x7d9fd4: CheckStackOverflow
    //     0x7d9fd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d9fd8: cmp             SP, x16
    //     0x7d9fdc: b.ls            #0x7da01c
    // 0x7d9fe0: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x7d9fe0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7d9fe4: ldr             x0, [x0, #0xc08]
    //     0x7d9fe8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7d9fec: cmp             w0, w16
    //     0x7d9ff0: b.ne            #0x7d9ffc
    //     0x7d9ff4: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x7d9ff8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7d9ffc: mov             x1, x0
    // 0x7da000: ldur            x2, [fp, #-8]
    // 0x7da004: ldur            x3, [fp, #-0x10]
    // 0x7da008: r0 = []=()
    //     0x7da008: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x7da00c: r0 = Null
    //     0x7da00c: mov             x0, NULL
    // 0x7da010: LeaveFrame
    //     0x7da010: mov             SP, fp
    //     0x7da014: ldp             fp, lr, [SP], #0x10
    // 0x7da018: ret
    //     0x7da018: ret             
    // 0x7da01c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7da01c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7da020: b               #0x7d9fe0
  }
  static Expando<Object> _instanceTokens() {
    // ** addr: 0x7da030, size: 0x40
    // 0x7da030: EnterFrame
    //     0x7da030: stp             fp, lr, [SP, #-0x10]!
    //     0x7da034: mov             fp, SP
    // 0x7da038: AllocStack(0x8)
    //     0x7da038: sub             SP, SP, #8
    // 0x7da03c: r1 = <Object>
    //     0x7da03c: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x7da040: r0 = Expando()
    //     0x7da040: bl              #0x7da070  ; AllocateExpandoStub -> Expando<X0> (size=0x1c)
    // 0x7da044: r1 = <_WeakProperty?>
    //     0x7da044: ldr             x1, [PP, #0x1210]  ; [pp+0x1210] TypeArguments: <_WeakProperty?>
    // 0x7da048: r2 = 16
    //     0x7da048: movz            x2, #0x10
    // 0x7da04c: stur            x0, [fp, #-8]
    // 0x7da050: r0 = AllocateArray()
    //     0x7da050: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7da054: mov             x1, x0
    // 0x7da058: ldur            x0, [fp, #-8]
    // 0x7da05c: StoreField: r0->field_b = r1
    //     0x7da05c: stur            w1, [x0, #0xb]
    // 0x7da060: StoreField: r0->field_f = rZR
    //     0x7da060: stur            xzr, [x0, #0xf]
    // 0x7da064: LeaveFrame
    //     0x7da064: mov             SP, fp
    //     0x7da068: ldp             fp, lr, [SP], #0x10
    // 0x7da06c: ret
    //     0x7da06c: ret             
  }
  static _ verify(/* No info */) {
    // ** addr: 0x833b94, size: 0x34
    // 0x833b94: EnterFrame
    //     0x833b94: stp             fp, lr, [SP, #-0x10]!
    //     0x833b98: mov             fp, SP
    // 0x833b9c: CheckStackOverflow
    //     0x833b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833ba0: cmp             SP, x16
    //     0x833ba4: b.ls            #0x833bc0
    // 0x833ba8: r3 = true
    //     0x833ba8: add             x3, NULL, #0x20  ; true
    // 0x833bac: r0 = _verify()
    //     0x833bac: bl              #0x833bc8  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_verify
    // 0x833bb0: r0 = Null
    //     0x833bb0: mov             x0, NULL
    // 0x833bb4: LeaveFrame
    //     0x833bb4: mov             SP, fp
    //     0x833bb8: ldp             fp, lr, [SP], #0x10
    // 0x833bbc: ret
    //     0x833bbc: ret             
    // 0x833bc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833bc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833bc4: b               #0x833ba8
  }
  static _ _verify(/* No info */) {
    // ** addr: 0x833bc8, size: 0xf0
    // 0x833bc8: EnterFrame
    //     0x833bc8: stp             fp, lr, [SP, #-0x10]!
    //     0x833bcc: mov             fp, SP
    // 0x833bd0: AllocStack(0x10)
    //     0x833bd0: sub             SP, SP, #0x10
    // 0x833bd4: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x833bd4: mov             x0, x1
    //     0x833bd8: stur            x1, [fp, #-8]
    //     0x833bdc: stur            x2, [fp, #-0x10]
    // 0x833be0: CheckStackOverflow
    //     0x833be0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x833be4: cmp             SP, x16
    //     0x833be8: b.ls            #0x833cb0
    // 0x833bec: tbnz            w3, #4, #0x833c24
    // 0x833bf0: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x833bf0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x833bf4: ldr             x0, [x0, #0xc08]
    //     0x833bf8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x833bfc: cmp             w0, w16
    //     0x833c00: b.ne            #0x833c0c
    //     0x833c04: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x833c08: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x833c0c: mov             x1, x0
    // 0x833c10: ldur            x2, [fp, #-8]
    // 0x833c14: r0 = []()
    //     0x833c14: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0x833c18: r16 = Instance_Object
    //     0x833c18: ldr             x16, [PP, #0x2e40]  ; [pp+0x2e40] Obj!Object@cb7731
    // 0x833c1c: cmp             w0, w16
    // 0x833c20: b.eq            #0x833c70
    // 0x833c24: ldur            x0, [fp, #-0x10]
    // 0x833c28: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x833c28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x833c2c: ldr             x0, [x0, #0xc08]
    //     0x833c30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x833c34: cmp             w0, w16
    //     0x833c38: b.ne            #0x833c44
    //     0x833c3c: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x833c40: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x833c44: mov             x1, x0
    // 0x833c48: ldur            x2, [fp, #-8]
    // 0x833c4c: r0 = []()
    //     0x833c4c: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0x833c50: mov             x1, x0
    // 0x833c54: ldur            x0, [fp, #-0x10]
    // 0x833c58: cmp             w0, w1
    // 0x833c5c: b.ne            #0x833c90
    // 0x833c60: r0 = Null
    //     0x833c60: mov             x0, NULL
    // 0x833c64: LeaveFrame
    //     0x833c64: mov             SP, fp
    //     0x833c68: ldp             fp, lr, [SP], #0x10
    // 0x833c6c: ret
    //     0x833c6c: ret             
    // 0x833c70: r0 = AssertionError()
    //     0x833c70: bl              #0x6bcfb4  ; AllocateAssertionErrorStub -> AssertionError (size=0x10)
    // 0x833c74: mov             x1, x0
    // 0x833c78: r0 = "`const Object()` cannot be used as the token."
    //     0x833c78: add             x0, PP, #0xb, lsl #12  ; [pp+0xbd90] "`const Object()` cannot be used as the token."
    //     0x833c7c: ldr             x0, [x0, #0xd90]
    // 0x833c80: StoreField: r1->field_b = r0
    //     0x833c80: stur            w0, [x1, #0xb]
    // 0x833c84: mov             x0, x1
    // 0x833c88: r0 = Throw()
    //     0x833c88: bl              #0xec04b8  ; ThrowStub
    // 0x833c8c: brk             #0
    // 0x833c90: r0 = AssertionError()
    //     0x833c90: bl              #0x6bcfb4  ; AllocateAssertionErrorStub -> AssertionError (size=0x10)
    // 0x833c94: mov             x1, x0
    // 0x833c98: r0 = "Platform interfaces must not be implemented with `implements`"
    //     0x833c98: add             x0, PP, #0xb, lsl #12  ; [pp+0xbd98] "Platform interfaces must not be implemented with `implements`"
    //     0x833c9c: ldr             x0, [x0, #0xd98]
    // 0x833ca0: StoreField: r1->field_b = r0
    //     0x833ca0: stur            w0, [x1, #0xb]
    // 0x833ca4: mov             x0, x1
    // 0x833ca8: r0 = Throw()
    //     0x833ca8: bl              #0xec04b8  ; ThrowStub
    // 0x833cac: brk             #0
    // 0x833cb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x833cb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x833cb4: b               #0x833bec
  }
  static _ verifyToken(/* No info */) {
    // ** addr: 0x912f2c, size: 0x34
    // 0x912f2c: EnterFrame
    //     0x912f2c: stp             fp, lr, [SP, #-0x10]!
    //     0x912f30: mov             fp, SP
    // 0x912f34: CheckStackOverflow
    //     0x912f34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x912f38: cmp             SP, x16
    //     0x912f3c: b.ls            #0x912f58
    // 0x912f40: r3 = false
    //     0x912f40: add             x3, NULL, #0x30  ; false
    // 0x912f44: r0 = _verify()
    //     0x912f44: bl              #0x833bc8  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_verify
    // 0x912f48: r0 = Null
    //     0x912f48: mov             x0, NULL
    // 0x912f4c: LeaveFrame
    //     0x912f4c: mov             SP, fp
    //     0x912f50: ldp             fp, lr, [SP], #0x10
    // 0x912f54: ret
    //     0x912f54: ret             
    // 0x912f58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x912f58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x912f5c: b               #0x912f40
  }
}
