// lib: impl.adapters.stream_cipher_as_block_cipher, url: package:pointycastle/adapters/stream_cipher_as_block_cipher.dart

// class id: 1050922, size: 0x8
class :: {
}

// class id: 710, size: 0x14, field offset: 0x8
abstract class StreamCipherAsBlockCipher extends BaseBlockCipher {

  _ reset(/* No info */) {
    // ** addr: 0xe7db8c, size: 0x3c
    // 0xe7db8c: EnterFrame
    //     0xe7db8c: stp             fp, lr, [SP, #-0x10]!
    //     0xe7db90: mov             fp, SP
    // 0xe7db94: CheckStackOverflow
    //     0xe7db94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7db98: cmp             SP, x16
    //     0xe7db9c: b.ls            #0xe7dbc0
    // 0xe7dba0: LoadField: r0 = r1->field_7
    //     0xe7dba0: ldur            w0, [x1, #7]
    // 0xe7dba4: DecompressPointer r0
    //     0xe7dba4: add             x0, x0, HEAP, lsl #32
    // 0xe7dba8: mov             x1, x0
    // 0xe7dbac: r0 = reset()
    //     0xe7dbac: bl              #0xe7dbc8  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::reset
    // 0xe7dbb0: r0 = Null
    //     0xe7dbb0: mov             x0, NULL
    // 0xe7dbb4: LeaveFrame
    //     0xe7dbb4: mov             SP, fp
    //     0xe7dbb8: ldp             fp, lr, [SP], #0x10
    // 0xe7dbbc: ret
    //     0xe7dbbc: ret             
    // 0xe7dbc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7dbc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7dbc4: b               #0xe7dba0
  }
  _ init(/* No info */) {
    // ** addr: 0xe82fbc, size: 0x44
    // 0xe82fbc: EnterFrame
    //     0xe82fbc: stp             fp, lr, [SP, #-0x10]!
    //     0xe82fc0: mov             fp, SP
    // 0xe82fc4: mov             x0, x2
    // 0xe82fc8: mov             x2, x3
    // 0xe82fcc: CheckStackOverflow
    //     0xe82fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe82fd0: cmp             SP, x16
    //     0xe82fd4: b.ls            #0xe82ff8
    // 0xe82fd8: LoadField: r0 = r1->field_7
    //     0xe82fd8: ldur            w0, [x1, #7]
    // 0xe82fdc: DecompressPointer r0
    //     0xe82fdc: add             x0, x0, HEAP, lsl #32
    // 0xe82fe0: mov             x1, x0
    // 0xe82fe4: r0 = init()
    //     0xe82fe4: bl              #0xe83000  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::init
    // 0xe82fe8: r0 = Null
    //     0xe82fe8: mov             x0, NULL
    // 0xe82fec: LeaveFrame
    //     0xe82fec: mov             SP, fp
    //     0xe82ff0: ldp             fp, lr, [SP], #0x10
    // 0xe82ff4: ret
    //     0xe82ff4: ret             
    // 0xe82ff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82ff8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82ffc: b               #0xe82fd8
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xea1b88, size: 0x54
    // 0xea1b88: EnterFrame
    //     0xea1b88: stp             fp, lr, [SP, #-0x10]!
    //     0xea1b8c: mov             fp, SP
    // 0xea1b90: AllocStack(0x8)
    //     0xea1b90: sub             SP, SP, #8
    // 0xea1b94: SetupParameters(dynamic _ /* r5 => r6 */, dynamic _ /* r6 => r7 */)
    //     0xea1b94: mov             x7, x6
    //     0xea1b98: mov             x6, x5
    // 0xea1b9c: CheckStackOverflow
    //     0xea1b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1ba0: cmp             SP, x16
    //     0xea1ba4: b.ls            #0xea1bd4
    // 0xea1ba8: LoadField: r0 = r1->field_7
    //     0xea1ba8: ldur            w0, [x1, #7]
    // 0xea1bac: DecompressPointer r0
    //     0xea1bac: add             x0, x0, HEAP, lsl #32
    // 0xea1bb0: LoadField: r4 = r1->field_b
    //     0xea1bb0: ldur            x4, [x1, #0xb]
    // 0xea1bb4: mov             x1, x0
    // 0xea1bb8: mov             x5, x4
    // 0xea1bbc: stur            x4, [fp, #-8]
    // 0xea1bc0: r0 = processBytes()
    //     0xea1bc0: bl              #0xea1bdc  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::processBytes
    // 0xea1bc4: ldur            x0, [fp, #-8]
    // 0xea1bc8: LeaveFrame
    //     0xea1bc8: mov             SP, fp
    //     0xea1bcc: ldp             fp, lr, [SP], #0x10
    // 0xea1bd0: ret
    //     0xea1bd0: ret             
    // 0xea1bd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1bd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1bd8: b               #0xea1ba8
  }
}
