// lib: , url: package:pointycastle/api.dart

// class id: 1050923, size: 0x8
class :: {
}

// class id: 678, size: 0xc, field offset: 0x8
class RegistryFactoryException extends Object
    implements Exception {

  _ RegistryFactoryException.unknown(/* No info */) {
    // ** addr: 0x8c3738, size: 0xa0
    // 0x8c3738: EnterFrame
    //     0x8c3738: stp             fp, lr, [SP, #-0x10]!
    //     0x8c373c: mov             fp, SP
    // 0x8c3740: AllocStack(0x20)
    //     0x8c3740: sub             SP, SP, #0x20
    // 0x8c3744: SetupParameters(RegistryFactoryException this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x8c3744: mov             x4, x1
    //     0x8c3748: mov             x0, x2
    //     0x8c374c: stur            x1, [fp, #-8]
    //     0x8c3750: stur            x2, [fp, #-0x10]
    //     0x8c3754: stur            x3, [fp, #-0x18]
    // 0x8c3758: CheckStackOverflow
    //     0x8c3758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c375c: cmp             SP, x16
    //     0x8c3760: b.ls            #0x8c37d0
    // 0x8c3764: r1 = Null
    //     0x8c3764: mov             x1, NULL
    // 0x8c3768: r2 = 8
    //     0x8c3768: movz            x2, #0x8
    // 0x8c376c: r0 = AllocateArray()
    //     0x8c376c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c3770: r16 = "No algorithm registered of type "
    //     0x8c3770: add             x16, PP, #0x17, lsl #12  ; [pp+0x17fc8] "No algorithm registered of type "
    //     0x8c3774: ldr             x16, [x16, #0xfc8]
    // 0x8c3778: StoreField: r0->field_f = r16
    //     0x8c3778: stur            w16, [x0, #0xf]
    // 0x8c377c: ldur            x1, [fp, #-0x18]
    // 0x8c3780: StoreField: r0->field_13 = r1
    //     0x8c3780: stur            w1, [x0, #0x13]
    // 0x8c3784: r16 = " with name: "
    //     0x8c3784: add             x16, PP, #0x17, lsl #12  ; [pp+0x17fd0] " with name: "
    //     0x8c3788: ldr             x16, [x16, #0xfd0]
    // 0x8c378c: ArrayStore: r0[0] = r16  ; List_4
    //     0x8c378c: stur            w16, [x0, #0x17]
    // 0x8c3790: ldur            x1, [fp, #-0x10]
    // 0x8c3794: StoreField: r0->field_1b = r1
    //     0x8c3794: stur            w1, [x0, #0x1b]
    // 0x8c3798: str             x0, [SP]
    // 0x8c379c: r0 = _interpolate()
    //     0x8c379c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c37a0: ldur            x1, [fp, #-8]
    // 0x8c37a4: StoreField: r1->field_7 = r0
    //     0x8c37a4: stur            w0, [x1, #7]
    //     0x8c37a8: ldurb           w16, [x1, #-1]
    //     0x8c37ac: ldurb           w17, [x0, #-1]
    //     0x8c37b0: and             x16, x17, x16, lsr #2
    //     0x8c37b4: tst             x16, HEAP, lsr #32
    //     0x8c37b8: b.eq            #0x8c37c0
    //     0x8c37bc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c37c0: r0 = Null
    //     0x8c37c0: mov             x0, NULL
    // 0x8c37c4: LeaveFrame
    //     0x8c37c4: mov             SP, fp
    //     0x8c37c8: ldp             fp, lr, [SP], #0x10
    // 0x8c37cc: ret
    //     0x8c37cc: ret             
    // 0x8c37d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c37d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c37d4: b               #0x8c3764
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3f320, size: 0x5c
    // 0xc3f320: EnterFrame
    //     0xc3f320: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f324: mov             fp, SP
    // 0xc3f328: AllocStack(0x8)
    //     0xc3f328: sub             SP, SP, #8
    // 0xc3f32c: CheckStackOverflow
    //     0xc3f32c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f330: cmp             SP, x16
    //     0xc3f334: b.ls            #0xc3f374
    // 0xc3f338: r1 = Null
    //     0xc3f338: mov             x1, NULL
    // 0xc3f33c: r2 = 4
    //     0xc3f33c: movz            x2, #0x4
    // 0xc3f340: r0 = AllocateArray()
    //     0xc3f340: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3f344: r16 = "RegistryFactoryException: "
    //     0xc3f344: add             x16, PP, #0x21, lsl #12  ; [pp+0x21e20] "RegistryFactoryException: "
    //     0xc3f348: ldr             x16, [x16, #0xe20]
    // 0xc3f34c: StoreField: r0->field_f = r16
    //     0xc3f34c: stur            w16, [x0, #0xf]
    // 0xc3f350: ldr             x1, [fp, #0x10]
    // 0xc3f354: LoadField: r2 = r1->field_7
    //     0xc3f354: ldur            w2, [x1, #7]
    // 0xc3f358: DecompressPointer r2
    //     0xc3f358: add             x2, x2, HEAP, lsl #32
    // 0xc3f35c: StoreField: r0->field_13 = r2
    //     0xc3f35c: stur            w2, [x0, #0x13]
    // 0xc3f360: str             x0, [SP]
    // 0xc3f364: r0 = _interpolate()
    //     0xc3f364: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3f368: LeaveFrame
    //     0xc3f368: mov             SP, fp
    //     0xc3f36c: ldp             fp, lr, [SP], #0x10
    // 0xc3f370: ret
    //     0xc3f370: ret             
    // 0xc3f374: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f374: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f378: b               #0xc3f338
  }
}

// class id: 679, size: 0x8, field offset: 0x8
abstract class PBEParametersGenerator extends Object {
}

// class id: 680, size: 0x14, field offset: 0x8
class ParametersWithIV<X0 bound CipherParameters?> extends Object
    implements CipherParameters {
}

// class id: 681, size: 0x14, field offset: 0x8
class PaddedBlockCipherParameters<X0 bound CipherParameters?, X1 bound CipherParameters?> extends Object
    implements CipherParameters {
}

// class id: 682, size: 0x8, field offset: 0x8
abstract class PaddedBlockCipher extends Object
    implements BlockCipher {

  factory _ PaddedBlockCipher(/* No info */) {
    // ** addr: 0x8c3190, size: 0x6c
    // 0x8c3190: EnterFrame
    //     0x8c3190: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3194: mov             fp, SP
    // 0x8c3198: AllocStack(0x20)
    //     0x8c3198: sub             SP, SP, #0x20
    // 0x8c319c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8c319c: stur            x2, [fp, #-8]
    // 0x8c31a0: CheckStackOverflow
    //     0x8c31a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c31a4: cmp             SP, x16
    //     0x8c31a8: b.ls            #0x8c31f4
    // 0x8c31ac: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c31ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c31b0: ldr             x0, [x0, #0x2e38]
    //     0x8c31b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c31b8: cmp             w0, w16
    //     0x8c31bc: b.ne            #0x8c31cc
    //     0x8c31c0: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c31c4: ldr             x2, [x2, #0xf80]
    //     0x8c31c8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c31cc: r16 = <PaddedBlockCipher>
    //     0x8c31cc: add             x16, PP, #0x17, lsl #12  ; [pp+0x17f88] TypeArguments: <PaddedBlockCipher>
    //     0x8c31d0: ldr             x16, [x16, #0xf88]
    // 0x8c31d4: stp             x0, x16, [SP, #8]
    // 0x8c31d8: ldur            x16, [fp, #-8]
    // 0x8c31dc: str             x16, [SP]
    // 0x8c31e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c31e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c31e4: r0 = create()
    //     0x8c31e4: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c31e8: LeaveFrame
    //     0x8c31e8: mov             SP, fp
    //     0x8c31ec: ldp             fp, lr, [SP], #0x10
    // 0x8c31f0: ret
    //     0x8c31f0: ret             
    // 0x8c31f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c31f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c31f8: b               #0x8c31ac
  }
}

// class id: 683, size: 0x8, field offset: 0x8
abstract class CipherParameters extends Object {
}

// class id: 684, size: 0xc, field offset: 0x8
class KeyParameter extends CipherParameters {

  late Uint8List key; // offset: 0x8
}

// class id: 686, size: 0x8, field offset: 0x8
abstract class Algorithm extends Object {
}

// class id: 687, size: 0x8, field offset: 0x8
abstract class StreamCipher extends Algorithm {
}

// class id: 688, size: 0x8, field offset: 0x8
abstract class Signer extends Algorithm {
}

// class id: 689, size: 0x8, field offset: 0x8
abstract class SecureRandom extends Algorithm {

  factory _ SecureRandom(/* No info */) {
    // ** addr: 0x8d0544, size: 0x68
    // 0x8d0544: EnterFrame
    //     0x8d0544: stp             fp, lr, [SP, #-0x10]!
    //     0x8d0548: mov             fp, SP
    // 0x8d054c: AllocStack(0x18)
    //     0x8d054c: sub             SP, SP, #0x18
    // 0x8d0550: CheckStackOverflow
    //     0x8d0550: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d0554: cmp             SP, x16
    //     0x8d0558: b.ls            #0x8d05a4
    // 0x8d055c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8d055c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d0560: ldr             x0, [x0, #0x2e38]
    //     0x8d0564: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d0568: cmp             w0, w16
    //     0x8d056c: b.ne            #0x8d057c
    //     0x8d0570: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8d0574: ldr             x2, [x2, #0xf80]
    //     0x8d0578: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d057c: r16 = <SecureRandom>
    //     0x8d057c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18880] TypeArguments: <SecureRandom>
    //     0x8d0580: ldr             x16, [x16, #0x880]
    // 0x8d0584: stp             x0, x16, [SP, #8]
    // 0x8d0588: r16 = ""
    //     0x8d0588: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8d058c: str             x16, [SP]
    // 0x8d0590: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8d0590: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8d0594: r0 = create()
    //     0x8d0594: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8d0598: LeaveFrame
    //     0x8d0598: mov             SP, fp
    //     0x8d059c: ldp             fp, lr, [SP], #0x10
    // 0x8d05a0: ret
    //     0x8d05a0: ret             
    // 0x8d05a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d05a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d05a8: b               #0x8d055c
  }
}

// class id: 690, size: 0x8, field offset: 0x8
abstract class Padding extends Algorithm {

  factory _ Padding(/* No info */) {
    // ** addr: 0x8c6c48, size: 0x6c
    // 0x8c6c48: EnterFrame
    //     0x8c6c48: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6c4c: mov             fp, SP
    // 0x8c6c50: AllocStack(0x20)
    //     0x8c6c50: sub             SP, SP, #0x20
    // 0x8c6c54: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8c6c54: stur            x2, [fp, #-8]
    // 0x8c6c58: CheckStackOverflow
    //     0x8c6c58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6c5c: cmp             SP, x16
    //     0x8c6c60: b.ls            #0x8c6cac
    // 0x8c6c64: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c6c64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6c68: ldr             x0, [x0, #0x2e38]
    //     0x8c6c6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6c70: cmp             w0, w16
    //     0x8c6c74: b.ne            #0x8c6c84
    //     0x8c6c78: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c6c7c: ldr             x2, [x2, #0xf80]
    //     0x8c6c80: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6c84: r16 = <Padding>
    //     0x8c6c84: add             x16, PP, #0x18, lsl #12  ; [pp+0x18368] TypeArguments: <Padding>
    //     0x8c6c88: ldr             x16, [x16, #0x368]
    // 0x8c6c8c: stp             x0, x16, [SP, #8]
    // 0x8c6c90: ldur            x16, [fp, #-8]
    // 0x8c6c94: str             x16, [SP]
    // 0x8c6c98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c6c98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c6c9c: r0 = create()
    //     0x8c6c9c: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c6ca0: LeaveFrame
    //     0x8c6ca0: mov             SP, fp
    //     0x8c6ca4: ldp             fp, lr, [SP], #0x10
    // 0x8c6ca8: ret
    //     0x8c6ca8: ret             
    // 0x8c6cac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6cac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6cb0: b               #0x8c6c64
  }
}

// class id: 691, size: 0x8, field offset: 0x8
abstract class Mac extends Algorithm {

  factory _ Mac(/* No info */) {
    // ** addr: 0x8c6064, size: 0x6c
    // 0x8c6064: EnterFrame
    //     0x8c6064: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6068: mov             fp, SP
    // 0x8c606c: AllocStack(0x20)
    //     0x8c606c: sub             SP, SP, #0x20
    // 0x8c6070: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8c6070: stur            x2, [fp, #-8]
    // 0x8c6074: CheckStackOverflow
    //     0x8c6074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6078: cmp             SP, x16
    //     0x8c607c: b.ls            #0x8c60c8
    // 0x8c6080: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c6080: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6084: ldr             x0, [x0, #0x2e38]
    //     0x8c6088: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c608c: cmp             w0, w16
    //     0x8c6090: b.ne            #0x8c60a0
    //     0x8c6094: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c6098: ldr             x2, [x2, #0xf80]
    //     0x8c609c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c60a0: r16 = <Mac>
    //     0x8c60a0: add             x16, PP, #0x18, lsl #12  ; [pp+0x182a8] TypeArguments: <Mac>
    //     0x8c60a4: ldr             x16, [x16, #0x2a8]
    // 0x8c60a8: stp             x0, x16, [SP, #8]
    // 0x8c60ac: ldur            x16, [fp, #-8]
    // 0x8c60b0: str             x16, [SP]
    // 0x8c60b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c60b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c60b8: r0 = create()
    //     0x8c60b8: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c60bc: LeaveFrame
    //     0x8c60bc: mov             SP, fp
    //     0x8c60c0: ldp             fp, lr, [SP], #0x10
    // 0x8c60c4: ret
    //     0x8c60c4: ret             
    // 0x8c60c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c60c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c60cc: b               #0x8c6080
  }
}

// class id: 692, size: 0x8, field offset: 0x8
abstract class KeyGenerator extends Algorithm {
}

// class id: 693, size: 0x8, field offset: 0x8
abstract class KeyDerivator extends Algorithm {
}

// class id: 694, size: 0x8, field offset: 0x8
abstract class Digest extends Algorithm {

  factory _ Digest(/* No info */) {
    // ** addr: 0x8c5a54, size: 0x6c
    // 0x8c5a54: EnterFrame
    //     0x8c5a54: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5a58: mov             fp, SP
    // 0x8c5a5c: AllocStack(0x20)
    //     0x8c5a5c: sub             SP, SP, #0x20
    // 0x8c5a60: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8c5a60: stur            x2, [fp, #-8]
    // 0x8c5a64: CheckStackOverflow
    //     0x8c5a64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5a68: cmp             SP, x16
    //     0x8c5a6c: b.ls            #0x8c5ab8
    // 0x8c5a70: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c5a70: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c5a74: ldr             x0, [x0, #0x2e38]
    //     0x8c5a78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c5a7c: cmp             w0, w16
    //     0x8c5a80: b.ne            #0x8c5a90
    //     0x8c5a84: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c5a88: ldr             x2, [x2, #0xf80]
    //     0x8c5a8c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c5a90: r16 = <Digest>
    //     0x8c5a90: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b8] TypeArguments: <Digest>
    //     0x8c5a94: ldr             x16, [x16, #0x1b8]
    // 0x8c5a98: stp             x0, x16, [SP, #8]
    // 0x8c5a9c: ldur            x16, [fp, #-8]
    // 0x8c5aa0: str             x16, [SP]
    // 0x8c5aa4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c5aa4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c5aa8: r0 = create()
    //     0x8c5aa8: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c5aac: LeaveFrame
    //     0x8c5aac: mov             SP, fp
    //     0x8c5ab0: ldp             fp, lr, [SP], #0x10
    // 0x8c5ab4: ret
    //     0x8c5ab4: ret             
    // 0x8c5ab8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5ab8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5abc: b               #0x8c5a70
  }
}

// class id: 695, size: 0x8, field offset: 0x8
abstract class Xof extends Digest {
}

// class id: 696, size: 0x8, field offset: 0x8
abstract class AsymmetricBlockCipher extends Algorithm {

  factory _ AsymmetricBlockCipher(/* No info */) {
    // ** addr: 0x8e6ccc, size: 0x6c
    // 0x8e6ccc: EnterFrame
    //     0x8e6ccc: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6cd0: mov             fp, SP
    // 0x8e6cd4: AllocStack(0x20)
    //     0x8e6cd4: sub             SP, SP, #0x20
    // 0x8e6cd8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8e6cd8: stur            x2, [fp, #-8]
    // 0x8e6cdc: CheckStackOverflow
    //     0x8e6cdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6ce0: cmp             SP, x16
    //     0x8e6ce4: b.ls            #0x8e6d30
    // 0x8e6ce8: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e6ce8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e6cec: ldr             x0, [x0, #0x2e38]
    //     0x8e6cf0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6cf4: cmp             w0, w16
    //     0x8e6cf8: b.ne            #0x8e6d08
    //     0x8e6cfc: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e6d00: ldr             x2, [x2, #0xf80]
    //     0x8e6d04: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e6d08: r16 = <AsymmetricBlockCipher>
    //     0x8e6d08: add             x16, PP, #0x19, lsl #12  ; [pp+0x19c10] TypeArguments: <AsymmetricBlockCipher>
    //     0x8e6d0c: ldr             x16, [x16, #0xc10]
    // 0x8e6d10: stp             x0, x16, [SP, #8]
    // 0x8e6d14: ldur            x16, [fp, #-8]
    // 0x8e6d18: str             x16, [SP]
    // 0x8e6d1c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e6d1c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e6d20: r0 = create()
    //     0x8e6d20: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e6d24: LeaveFrame
    //     0x8e6d24: mov             SP, fp
    //     0x8e6d28: ldp             fp, lr, [SP], #0x10
    // 0x8e6d2c: ret
    //     0x8e6d2c: ret             
    // 0x8e6d30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6d30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6d34: b               #0x8e6ce8
  }
}

// class id: 697, size: 0x8, field offset: 0x8
abstract class AEADCipher extends Algorithm {
}

// class id: 698, size: 0x8, field offset: 0x8
abstract class BlockCipher extends Algorithm {

  factory _ BlockCipher(/* No info */) {
    // ** addr: 0x8c4ca0, size: 0x6c
    // 0x8c4ca0: EnterFrame
    //     0x8c4ca0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4ca4: mov             fp, SP
    // 0x8c4ca8: AllocStack(0x20)
    //     0x8c4ca8: sub             SP, SP, #0x20
    // 0x8c4cac: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8c4cac: stur            x2, [fp, #-8]
    // 0x8c4cb0: CheckStackOverflow
    //     0x8c4cb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c4cb4: cmp             SP, x16
    //     0x8c4cb8: b.ls            #0x8c4d04
    // 0x8c4cbc: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c4cbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c4cc0: ldr             x0, [x0, #0x2e38]
    //     0x8c4cc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c4cc8: cmp             w0, w16
    //     0x8c4ccc: b.ne            #0x8c4cdc
    //     0x8c4cd0: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c4cd4: ldr             x2, [x2, #0xf80]
    //     0x8c4cd8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c4cdc: r16 = <BlockCipher>
    //     0x8c4cdc: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8c4ce0: ldr             x16, [x16, #0x88]
    // 0x8c4ce4: stp             x0, x16, [SP, #8]
    // 0x8c4ce8: ldur            x16, [fp, #-8]
    // 0x8c4cec: str             x16, [SP]
    // 0x8c4cf0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c4cf0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c4cf4: r0 = create()
    //     0x8c4cf4: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c4cf8: LeaveFrame
    //     0x8c4cf8: mov             SP, fp
    //     0x8c4cfc: ldp             fp, lr, [SP], #0x10
    // 0x8c4d00: ret
    //     0x8c4d00: ret             
    // 0x8c4d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4d04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c4d08: b               #0x8c4cbc
  }
}

// class id: 699, size: 0x8, field offset: 0x8
abstract class AEADBlockCipher extends BlockCipher {
}

// class id: 700, size: 0x8, field offset: 0x8
class InvalidCipherTextException extends Object
    implements Exception {
}
