// lib: impl.asymmetric_block_cipher.oeap, url: package:pointycastle/asymmetric/oaep.dart

// class id: 1050924, size: 0x8
class :: {
}

// class id: 677, size: 0xc, field offset: 0x8
class OAEPEncoding extends BaseAsymmetricBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xd50

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e6d38, size: 0x64
    // 0x8e6d38: EnterFrame
    //     0x8e6d38: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6d3c: mov             fp, SP
    // 0x8e6d40: AllocStack(0x8)
    //     0x8e6d40: sub             SP, SP, #8
    // 0x8e6d44: CheckStackOverflow
    //     0x8e6d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6d48: cmp             SP, x16
    //     0x8e6d4c: b.ls            #0x8e6d94
    // 0x8e6d50: r0 = DynamicFactoryConfig()
    //     0x8e6d50: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e6d54: r1 = Function '<anonymous closure>': static.
    //     0x8e6d54: add             x1, PP, #0x19, lsl #12  ; [pp+0x19c18] AnonymousClosure: static (0x8e6d9c), in [package:pointycastle/asymmetric/oaep.dart] OAEPEncoding::factoryConfig (0x8e6d38)
    //     0x8e6d58: ldr             x1, [x1, #0xc18]
    // 0x8e6d5c: r2 = Null
    //     0x8e6d5c: mov             x2, NULL
    // 0x8e6d60: stur            x0, [fp, #-8]
    // 0x8e6d64: r0 = AllocateClosure()
    //     0x8e6d64: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6d68: ldur            x1, [fp, #-8]
    // 0x8e6d6c: mov             x5, x0
    // 0x8e6d70: r2 = AsymmetricBlockCipher
    //     0x8e6d70: add             x2, PP, #0x19, lsl #12  ; [pp+0x19bf0] Type: AsymmetricBlockCipher
    //     0x8e6d74: ldr             x2, [x2, #0xbf0]
    // 0x8e6d78: r3 = "/OAEP"
    //     0x8e6d78: add             x3, PP, #0x19, lsl #12  ; [pp+0x19c20] "/OAEP"
    //     0x8e6d7c: ldr             x3, [x3, #0xc20]
    // 0x8e6d80: r0 = DynamicFactoryConfig.suffix()
    //     0x8e6d80: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e6d84: ldur            x0, [fp, #-8]
    // 0x8e6d88: LeaveFrame
    //     0x8e6d88: mov             SP, fp
    //     0x8e6d8c: ldp             fp, lr, [SP], #0x10
    // 0x8e6d90: ret
    //     0x8e6d90: ret             
    // 0x8e6d94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6d94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6d98: b               #0x8e6d50
  }
  [closure] static (dynamic) => OAEPEncoding <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e6d9c, size: 0x54
    // 0x8e6d9c: EnterFrame
    //     0x8e6d9c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6da0: mov             fp, SP
    // 0x8e6da4: AllocStack(0x8)
    //     0x8e6da4: sub             SP, SP, #8
    // 0x8e6da8: SetupParameters()
    //     0x8e6da8: ldr             x0, [fp, #0x20]
    //     0x8e6dac: ldur            w1, [x0, #0x17]
    //     0x8e6db0: add             x1, x1, HEAP, lsl #32
    //     0x8e6db4: stur            x1, [fp, #-8]
    // 0x8e6db8: r1 = 1
    //     0x8e6db8: movz            x1, #0x1
    // 0x8e6dbc: r0 = AllocateContext()
    //     0x8e6dbc: bl              #0xec126c  ; AllocateContextStub
    // 0x8e6dc0: mov             x1, x0
    // 0x8e6dc4: ldur            x0, [fp, #-8]
    // 0x8e6dc8: StoreField: r1->field_b = r0
    //     0x8e6dc8: stur            w0, [x1, #0xb]
    // 0x8e6dcc: ldr             x0, [fp, #0x10]
    // 0x8e6dd0: StoreField: r1->field_f = r0
    //     0x8e6dd0: stur            w0, [x1, #0xf]
    // 0x8e6dd4: mov             x2, x1
    // 0x8e6dd8: r1 = Function '<anonymous closure>': static.
    //     0x8e6dd8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19c28] AnonymousClosure: static (0x8e6df0), in [package:pointycastle/asymmetric/oaep.dart] OAEPEncoding::factoryConfig (0x8e6d38)
    //     0x8e6ddc: ldr             x1, [x1, #0xc28]
    // 0x8e6de0: r0 = AllocateClosure()
    //     0x8e6de0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6de4: LeaveFrame
    //     0x8e6de4: mov             SP, fp
    //     0x8e6de8: ldp             fp, lr, [SP], #0x10
    // 0x8e6dec: ret
    //     0x8e6dec: ret             
  }
  [closure] static OAEPEncoding <anonymous closure>(dynamic) {
    // ** addr: 0x8e6df0, size: 0xb8
    // 0x8e6df0: EnterFrame
    //     0x8e6df0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6df4: mov             fp, SP
    // 0x8e6df8: AllocStack(0x20)
    //     0x8e6df8: sub             SP, SP, #0x20
    // 0x8e6dfc: SetupParameters()
    //     0x8e6dfc: ldr             x0, [fp, #0x10]
    //     0x8e6e00: ldur            w1, [x0, #0x17]
    //     0x8e6e04: add             x1, x1, HEAP, lsl #32
    // 0x8e6e08: CheckStackOverflow
    //     0x8e6e08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6e0c: cmp             SP, x16
    //     0x8e6e10: b.ls            #0x8e6e9c
    // 0x8e6e14: LoadField: r0 = r1->field_f
    //     0x8e6e14: ldur            w0, [x1, #0xf]
    // 0x8e6e18: DecompressPointer r0
    //     0x8e6e18: add             x0, x0, HEAP, lsl #32
    // 0x8e6e1c: r1 = LoadClassIdInstr(r0)
    //     0x8e6e1c: ldur            x1, [x0, #-1]
    //     0x8e6e20: ubfx            x1, x1, #0xc, #0x14
    // 0x8e6e24: mov             x16, x0
    // 0x8e6e28: mov             x0, x1
    // 0x8e6e2c: mov             x1, x16
    // 0x8e6e30: r2 = 1
    //     0x8e6e30: movz            x2, #0x1
    // 0x8e6e34: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e6e34: sub             lr, x0, #0xfdd
    //     0x8e6e38: ldr             lr, [x21, lr, lsl #3]
    //     0x8e6e3c: blr             lr
    // 0x8e6e40: stur            x0, [fp, #-8]
    // 0x8e6e44: cmp             w0, NULL
    // 0x8e6e48: b.eq            #0x8e6ea4
    // 0x8e6e4c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e6e4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e6e50: ldr             x0, [x0, #0x2e38]
    //     0x8e6e54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6e58: cmp             w0, w16
    //     0x8e6e5c: b.ne            #0x8e6e6c
    //     0x8e6e60: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e6e64: ldr             x2, [x2, #0xf80]
    //     0x8e6e68: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e6e6c: r16 = <AsymmetricBlockCipher>
    //     0x8e6e6c: add             x16, PP, #0x19, lsl #12  ; [pp+0x19c10] TypeArguments: <AsymmetricBlockCipher>
    //     0x8e6e70: ldr             x16, [x16, #0xc10]
    // 0x8e6e74: stp             x0, x16, [SP, #8]
    // 0x8e6e78: ldur            x16, [fp, #-8]
    // 0x8e6e7c: str             x16, [SP]
    // 0x8e6e80: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e6e80: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e6e84: r0 = create()
    //     0x8e6e84: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e6e88: r1 = Null
    //     0x8e6e88: mov             x1, NULL
    // 0x8e6e8c: r0 = OAEPEncoding.withSHA1()
    //     0x8e6e8c: bl              #0x8e6ea8  ; [package:pointycastle/asymmetric/oaep.dart] OAEPEncoding::OAEPEncoding.withSHA1
    // 0x8e6e90: LeaveFrame
    //     0x8e6e90: mov             SP, fp
    //     0x8e6e94: ldp             fp, lr, [SP], #0x10
    // 0x8e6e98: ret
    //     0x8e6e98: ret             
    // 0x8e6e9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6e9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6ea0: b               #0x8e6e14
    // 0x8e6ea4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e6ea4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  factory OAEPEncoding OAEPEncoding.withSHA1(dynamic) {
    // ** addr: 0x8e6ea8, size: 0x54
    // 0x8e6ea8: EnterFrame
    //     0x8e6ea8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6eac: mov             fp, SP
    // 0x8e6eb0: AllocStack(0x8)
    //     0x8e6eb0: sub             SP, SP, #8
    // 0x8e6eb4: CheckStackOverflow
    //     0x8e6eb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6eb8: cmp             SP, x16
    //     0x8e6ebc: b.ls            #0x8e6ef4
    // 0x8e6ec0: r0 = OAEPEncoding()
    //     0x8e6ec0: bl              #0x8e9218  ; AllocateOAEPEncodingStub -> OAEPEncoding (size=0xc)
    // 0x8e6ec4: r1 = Function '<anonymous closure>': static.
    //     0x8e6ec4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19c30] AnonymousClosure: static (0x8e37ac), in [package:pointycastle/digests/sha1.dart] SHA1Digest::factoryConfig (0x8e3754)
    //     0x8e6ec8: ldr             x1, [x1, #0xc30]
    // 0x8e6ecc: r2 = Null
    //     0x8e6ecc: mov             x2, NULL
    // 0x8e6ed0: stur            x0, [fp, #-8]
    // 0x8e6ed4: r0 = AllocateClosure()
    //     0x8e6ed4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6ed8: ldur            x1, [fp, #-8]
    // 0x8e6edc: mov             x2, x0
    // 0x8e6ee0: r0 = OAEPEncoding._()
    //     0x8e6ee0: bl              #0x8e6efc  ; [package:pointycastle/asymmetric/oaep.dart] OAEPEncoding::OAEPEncoding._
    // 0x8e6ee4: ldur            x0, [fp, #-8]
    // 0x8e6ee8: LeaveFrame
    //     0x8e6ee8: mov             SP, fp
    //     0x8e6eec: ldp             fp, lr, [SP], #0x10
    // 0x8e6ef0: ret
    //     0x8e6ef0: ret             
    // 0x8e6ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6ef4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6ef8: b               #0x8e6ec0
  }
  _ OAEPEncoding._(/* No info */) {
    // ** addr: 0x8e6efc, size: 0xc8
    // 0x8e6efc: EnterFrame
    //     0x8e6efc: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6f00: mov             fp, SP
    // 0x8e6f04: AllocStack(0x18)
    //     0x8e6f04: sub             SP, SP, #0x18
    // 0x8e6f08: SetupParameters(OAEPEncoding this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x8e6f08: stur            x1, [fp, #-8]
    //     0x8e6f0c: mov             x16, x2
    //     0x8e6f10: mov             x2, x1
    //     0x8e6f14: mov             x1, x16
    //     0x8e6f18: stur            x1, [fp, #-0x10]
    // 0x8e6f1c: CheckStackOverflow
    //     0x8e6f1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6f20: cmp             SP, x16
    //     0x8e6f24: b.ls            #0x8e6fbc
    // 0x8e6f28: str             x1, [SP]
    // 0x8e6f2c: mov             x0, x1
    // 0x8e6f30: ClosureCall
    //     0x8e6f30: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x8e6f34: ldur            x2, [x0, #0x1f]
    //     0x8e6f38: blr             x2
    // 0x8e6f3c: ldur            x16, [fp, #-0x10]
    // 0x8e6f40: str             x16, [SP]
    // 0x8e6f44: ldur            x0, [fp, #-0x10]
    // 0x8e6f48: ClosureCall
    //     0x8e6f48: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x8e6f4c: ldur            x2, [x0, #0x1f]
    //     0x8e6f50: blr             x2
    // 0x8e6f54: r4 = 40
    //     0x8e6f54: movz            x4, #0x28
    // 0x8e6f58: r0 = AllocateUint8Array()
    //     0x8e6f58: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e6f5c: ldur            x1, [fp, #-8]
    // 0x8e6f60: StoreField: r1->field_7 = r0
    //     0x8e6f60: stur            w0, [x1, #7]
    //     0x8e6f64: ldurb           w16, [x1, #-1]
    //     0x8e6f68: ldurb           w17, [x0, #-1]
    //     0x8e6f6c: and             x16, x17, x16, lsr #2
    //     0x8e6f70: tst             x16, HEAP, lsr #32
    //     0x8e6f74: b.eq            #0x8e6f7c
    //     0x8e6f78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e6f7c: ldur            x16, [fp, #-0x10]
    // 0x8e6f80: str             x16, [SP]
    // 0x8e6f84: ldur            x0, [fp, #-0x10]
    // 0x8e6f88: ClosureCall
    //     0x8e6f88: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x8e6f8c: ldur            x2, [x0, #0x1f]
    //     0x8e6f90: blr             x2
    // 0x8e6f94: mov             x1, x0
    // 0x8e6f98: ldur            x0, [fp, #-8]
    // 0x8e6f9c: LoadField: r2 = r0->field_7
    //     0x8e6f9c: ldur            w2, [x0, #7]
    // 0x8e6fa0: DecompressPointer r2
    //     0x8e6fa0: add             x2, x2, HEAP, lsl #32
    // 0x8e6fa4: r3 = 0
    //     0x8e6fa4: movz            x3, #0
    // 0x8e6fa8: r0 = doFinal()
    //     0x8e6fa8: bl              #0x8e6fc4  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::doFinal
    // 0x8e6fac: r0 = Null
    //     0x8e6fac: mov             x0, NULL
    // 0x8e6fb0: LeaveFrame
    //     0x8e6fb0: mov             SP, fp
    //     0x8e6fb4: ldp             fp, lr, [SP], #0x10
    // 0x8e6fb8: ret
    //     0x8e6fb8: ret             
    // 0x8e6fbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6fbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6fc0: b               #0x8e6f28
  }
}
