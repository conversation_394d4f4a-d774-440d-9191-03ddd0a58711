// lib: impl.asymmetric_block_cipher.pkcs1, url: package:pointycastle/asymmetric/pkcs1.dart

// class id: 1050925, size: 0x8
class :: {
}

// class id: 676, size: 0x8, field offset: 0x8
class PKCS1Encoding extends BaseAsymmetricBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xd48

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e6b8c, size: 0x64
    // 0x8e6b8c: EnterFrame
    //     0x8e6b8c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6b90: mov             fp, SP
    // 0x8e6b94: AllocStack(0x8)
    //     0x8e6b94: sub             SP, SP, #8
    // 0x8e6b98: CheckStackOverflow
    //     0x8e6b98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6b9c: cmp             SP, x16
    //     0x8e6ba0: b.ls            #0x8e6be8
    // 0x8e6ba4: r0 = DynamicFactoryConfig()
    //     0x8e6ba4: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e6ba8: r1 = Function '<anonymous closure>': static.
    //     0x8e6ba8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19bf8] AnonymousClosure: static (0x8e6bf0), in [package:pointycastle/asymmetric/pkcs1.dart] PKCS1Encoding::factoryConfig (0x8e6b8c)
    //     0x8e6bac: ldr             x1, [x1, #0xbf8]
    // 0x8e6bb0: r2 = Null
    //     0x8e6bb0: mov             x2, NULL
    // 0x8e6bb4: stur            x0, [fp, #-8]
    // 0x8e6bb8: r0 = AllocateClosure()
    //     0x8e6bb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6bbc: ldur            x1, [fp, #-8]
    // 0x8e6bc0: mov             x5, x0
    // 0x8e6bc4: r2 = AsymmetricBlockCipher
    //     0x8e6bc4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19bf0] Type: AsymmetricBlockCipher
    //     0x8e6bc8: ldr             x2, [x2, #0xbf0]
    // 0x8e6bcc: r3 = "/PKCS1"
    //     0x8e6bcc: add             x3, PP, #0x19, lsl #12  ; [pp+0x19c00] "/PKCS1"
    //     0x8e6bd0: ldr             x3, [x3, #0xc00]
    // 0x8e6bd4: r0 = DynamicFactoryConfig.suffix()
    //     0x8e6bd4: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e6bd8: ldur            x0, [fp, #-8]
    // 0x8e6bdc: LeaveFrame
    //     0x8e6bdc: mov             SP, fp
    //     0x8e6be0: ldp             fp, lr, [SP], #0x10
    // 0x8e6be4: ret
    //     0x8e6be4: ret             
    // 0x8e6be8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6be8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6bec: b               #0x8e6ba4
  }
  [closure] static (dynamic) => PKCS1Encoding <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e6bf0, size: 0x54
    // 0x8e6bf0: EnterFrame
    //     0x8e6bf0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6bf4: mov             fp, SP
    // 0x8e6bf8: AllocStack(0x8)
    //     0x8e6bf8: sub             SP, SP, #8
    // 0x8e6bfc: SetupParameters()
    //     0x8e6bfc: ldr             x0, [fp, #0x20]
    //     0x8e6c00: ldur            w1, [x0, #0x17]
    //     0x8e6c04: add             x1, x1, HEAP, lsl #32
    //     0x8e6c08: stur            x1, [fp, #-8]
    // 0x8e6c0c: r1 = 1
    //     0x8e6c0c: movz            x1, #0x1
    // 0x8e6c10: r0 = AllocateContext()
    //     0x8e6c10: bl              #0xec126c  ; AllocateContextStub
    // 0x8e6c14: mov             x1, x0
    // 0x8e6c18: ldur            x0, [fp, #-8]
    // 0x8e6c1c: StoreField: r1->field_b = r0
    //     0x8e6c1c: stur            w0, [x1, #0xb]
    // 0x8e6c20: ldr             x0, [fp, #0x10]
    // 0x8e6c24: StoreField: r1->field_f = r0
    //     0x8e6c24: stur            w0, [x1, #0xf]
    // 0x8e6c28: mov             x2, x1
    // 0x8e6c2c: r1 = Function '<anonymous closure>': static.
    //     0x8e6c2c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19c08] AnonymousClosure: static (0x8e6c44), in [package:pointycastle/asymmetric/pkcs1.dart] PKCS1Encoding::factoryConfig (0x8e6b8c)
    //     0x8e6c30: ldr             x1, [x1, #0xc08]
    // 0x8e6c34: r0 = AllocateClosure()
    //     0x8e6c34: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6c38: LeaveFrame
    //     0x8e6c38: mov             SP, fp
    //     0x8e6c3c: ldp             fp, lr, [SP], #0x10
    // 0x8e6c40: ret
    //     0x8e6c40: ret             
  }
  [closure] static PKCS1Encoding <anonymous closure>(dynamic) {
    // ** addr: 0x8e6c44, size: 0x7c
    // 0x8e6c44: EnterFrame
    //     0x8e6c44: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6c48: mov             fp, SP
    // 0x8e6c4c: ldr             x0, [fp, #0x10]
    // 0x8e6c50: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8e6c50: ldur            w1, [x0, #0x17]
    // 0x8e6c54: DecompressPointer r1
    //     0x8e6c54: add             x1, x1, HEAP, lsl #32
    // 0x8e6c58: CheckStackOverflow
    //     0x8e6c58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6c5c: cmp             SP, x16
    //     0x8e6c60: b.ls            #0x8e6cb4
    // 0x8e6c64: LoadField: r0 = r1->field_f
    //     0x8e6c64: ldur            w0, [x1, #0xf]
    // 0x8e6c68: DecompressPointer r0
    //     0x8e6c68: add             x0, x0, HEAP, lsl #32
    // 0x8e6c6c: r1 = LoadClassIdInstr(r0)
    //     0x8e6c6c: ldur            x1, [x0, #-1]
    //     0x8e6c70: ubfx            x1, x1, #0xc, #0x14
    // 0x8e6c74: mov             x16, x0
    // 0x8e6c78: mov             x0, x1
    // 0x8e6c7c: mov             x1, x16
    // 0x8e6c80: r2 = 1
    //     0x8e6c80: movz            x2, #0x1
    // 0x8e6c84: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e6c84: sub             lr, x0, #0xfdd
    //     0x8e6c88: ldr             lr, [x21, lr, lsl #3]
    //     0x8e6c8c: blr             lr
    // 0x8e6c90: cmp             w0, NULL
    // 0x8e6c94: b.eq            #0x8e6cbc
    // 0x8e6c98: mov             x2, x0
    // 0x8e6c9c: r1 = Null
    //     0x8e6c9c: mov             x1, NULL
    // 0x8e6ca0: r0 = AsymmetricBlockCipher()
    //     0x8e6ca0: bl              #0x8e6ccc  ; [package:pointycastle/api.dart] AsymmetricBlockCipher::AsymmetricBlockCipher
    // 0x8e6ca4: r0 = PKCS1Encoding()
    //     0x8e6ca4: bl              #0x8e6cc0  ; AllocatePKCS1EncodingStub -> PKCS1Encoding (size=0x8)
    // 0x8e6ca8: LeaveFrame
    //     0x8e6ca8: mov             SP, fp
    //     0x8e6cac: ldp             fp, lr, [SP], #0x10
    // 0x8e6cb0: ret
    //     0x8e6cb0: ret             
    // 0x8e6cb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6cb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6cb8: b               #0x8e6c64
    // 0x8e6cbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e6cbc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
