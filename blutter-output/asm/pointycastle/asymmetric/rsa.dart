// lib: impl.asymmetric_block_cipher.rsa, url: package:pointycastle/asymmetric/rsa.dart

// class id: 1050926, size: 0x8
class :: {
}

// class id: 675, size: 0x8, field offset: 0x8
class RSAEngine extends BaseAsymmetricBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xd4c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e6b10, size: 0x58
    // 0x8e6b10: EnterFrame
    //     0x8e6b10: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6b14: mov             fp, SP
    // 0x8e6b18: AllocStack(0x8)
    //     0x8e6b18: sub             SP, SP, #8
    // 0x8e6b1c: r0 = StaticFactoryConfig()
    //     0x8e6b1c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e6b20: mov             x3, x0
    // 0x8e6b24: r0 = "RSA"
    //     0x8e6b24: add             x0, PP, #0x18, lsl #12  ; [pp+0x18450] "RSA"
    //     0x8e6b28: ldr             x0, [x0, #0x450]
    // 0x8e6b2c: stur            x3, [fp, #-8]
    // 0x8e6b30: StoreField: r3->field_b = r0
    //     0x8e6b30: stur            w0, [x3, #0xb]
    // 0x8e6b34: r1 = Function '<anonymous closure>': static.
    //     0x8e6b34: add             x1, PP, #0x19, lsl #12  ; [pp+0x19be8] AnonymousClosure: static (0x8e6b68), in [package:pointycastle/asymmetric/rsa.dart] RSAEngine::factoryConfig (0x8e6b10)
    //     0x8e6b38: ldr             x1, [x1, #0xbe8]
    // 0x8e6b3c: r2 = Null
    //     0x8e6b3c: mov             x2, NULL
    // 0x8e6b40: r0 = AllocateClosure()
    //     0x8e6b40: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6b44: mov             x1, x0
    // 0x8e6b48: ldur            x0, [fp, #-8]
    // 0x8e6b4c: StoreField: r0->field_f = r1
    //     0x8e6b4c: stur            w1, [x0, #0xf]
    // 0x8e6b50: r1 = AsymmetricBlockCipher
    //     0x8e6b50: add             x1, PP, #0x19, lsl #12  ; [pp+0x19bf0] Type: AsymmetricBlockCipher
    //     0x8e6b54: ldr             x1, [x1, #0xbf0]
    // 0x8e6b58: StoreField: r0->field_7 = r1
    //     0x8e6b58: stur            w1, [x0, #7]
    // 0x8e6b5c: LeaveFrame
    //     0x8e6b5c: mov             SP, fp
    //     0x8e6b60: ldp             fp, lr, [SP], #0x10
    // 0x8e6b64: ret
    //     0x8e6b64: ret             
  }
  [closure] static RSAEngine <anonymous closure>(dynamic) {
    // ** addr: 0x8e6b68, size: 0x18
    // 0x8e6b68: EnterFrame
    //     0x8e6b68: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6b6c: mov             fp, SP
    // 0x8e6b70: r0 = RSAEngine()
    //     0x8e6b70: bl              #0x8e6b80  ; AllocateRSAEngineStub -> RSAEngine (size=0x8)
    // 0x8e6b74: LeaveFrame
    //     0x8e6b74: mov             SP, fp
    //     0x8e6b78: ldp             fp, lr, [SP], #0x10
    // 0x8e6b7c: ret
    //     0x8e6b7c: ret             
  }
}
