// lib: , url: package:pointycastle/block/des_base.dart

// class id: 1050928, size: 0x8
class :: {
}

// class id: 672, size: 0x8, field offset: 0x8
abstract class DesBase extends Object {

  static late final Uint8List pc1; // offset: 0xd68
  static late final List<int> bytebit; // offset: 0xd60
  static late final Uint8List totrot; // offset: 0xd6c
  static late final Uint8List pc2; // offset: 0xd70
  static late final List<int> bigbyte; // offset: 0xd64
  static late final List<int> SP7; // offset: 0xd8c
  static late final List<int> SP5; // offset: 0xd84
  static late final List<int> SP3; // offset: 0xd7c
  static late final List<int> SP1; // offset: 0xd74
  static late final List<int> SP8; // offset: 0xd90
  static late final List<int> SP6; // offset: 0xd88
  static late final List<int> SP4; // offset: 0xd80
  static late final List<int> SP2; // offset: 0xd78

  _ generateWorkingKey(/* No info */) {
    // ** addr: 0xe8b9b8, size: 0xd78
    // 0xe8b9b8: EnterFrame
    //     0xe8b9b8: stp             fp, lr, [SP, #-0x10]!
    //     0xe8b9bc: mov             fp, SP
    // 0xe8b9c0: AllocStack(0x78)
    //     0xe8b9c0: sub             SP, SP, #0x78
    // 0xe8b9c4: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xe8b9c4: mov             x0, x2
    //     0xe8b9c8: stur            x2, [fp, #-8]
    //     0xe8b9cc: stur            x3, [fp, #-0x10]
    // 0xe8b9d0: CheckStackOverflow
    //     0xe8b9d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b9d4: cmp             SP, x16
    //     0xe8b9d8: b.ls            #0xe8c588
    // 0xe8b9dc: r1 = <int>
    //     0xe8b9dc: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8b9e0: r2 = 32
    //     0xe8b9e0: movz            x2, #0x20
    // 0xe8b9e4: r0 = _GrowableList()
    //     0xe8b9e4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe8b9e8: stur            x0, [fp, #-0x28]
    // 0xe8b9ec: LoadField: r1 = r0->field_b
    //     0xe8b9ec: ldur            w1, [x0, #0xb]
    // 0xe8b9f0: r3 = LoadInt32Instr(r1)
    //     0xe8b9f0: sbfx            x3, x1, #1, #0x1f
    // 0xe8b9f4: stur            x3, [fp, #-0x20]
    // 0xe8b9f8: LoadField: r4 = r0->field_f
    //     0xe8b9f8: ldur            w4, [x0, #0xf]
    // 0xe8b9fc: DecompressPointer r4
    //     0xe8b9fc: add             x4, x4, HEAP, lsl #32
    // 0xe8ba00: stur            x4, [fp, #-0x18]
    // 0xe8ba04: r1 = 0
    //     0xe8ba04: movz            x1, #0
    // 0xe8ba08: CheckStackOverflow
    //     0xe8ba08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ba0c: cmp             SP, x16
    //     0xe8ba10: b.ls            #0xe8c590
    // 0xe8ba14: cmp             x1, x3
    // 0xe8ba18: b.ge            #0xe8ba30
    // 0xe8ba1c: ArrayStore: r4[r1] = rZR  ; Unknown_4
    //     0xe8ba1c: add             x2, x4, x1, lsl #2
    //     0xe8ba20: stur            wzr, [x2, #0xf]
    // 0xe8ba24: add             x2, x1, #1
    // 0xe8ba28: mov             x1, x2
    // 0xe8ba2c: b               #0xe8ba08
    // 0xe8ba30: r1 = <bool>
    //     0xe8ba30: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xe8ba34: r2 = 56
    //     0xe8ba34: movz            x2, #0x38
    // 0xe8ba38: r0 = _GrowableList()
    //     0xe8ba38: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe8ba3c: LoadField: r1 = r0->field_b
    //     0xe8ba3c: ldur            w1, [x0, #0xb]
    // 0xe8ba40: r3 = LoadInt32Instr(r1)
    //     0xe8ba40: sbfx            x3, x1, #1, #0x1f
    // 0xe8ba44: stur            x3, [fp, #-0x38]
    // 0xe8ba48: LoadField: r4 = r0->field_f
    //     0xe8ba48: ldur            w4, [x0, #0xf]
    // 0xe8ba4c: DecompressPointer r4
    //     0xe8ba4c: add             x4, x4, HEAP, lsl #32
    // 0xe8ba50: stur            x4, [fp, #-0x30]
    // 0xe8ba54: r0 = 0
    //     0xe8ba54: movz            x0, #0
    // 0xe8ba58: CheckStackOverflow
    //     0xe8ba58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ba5c: cmp             SP, x16
    //     0xe8ba60: b.ls            #0xe8c598
    // 0xe8ba64: cmp             x0, x3
    // 0xe8ba68: b.ge            #0xe8ba84
    // 0xe8ba6c: add             x1, x4, x0, lsl #2
    // 0xe8ba70: r16 = false
    //     0xe8ba70: add             x16, NULL, #0x30  ; false
    // 0xe8ba74: StoreField: r1->field_f = r16
    //     0xe8ba74: stur            w16, [x1, #0xf]
    // 0xe8ba78: add             x1, x0, #1
    // 0xe8ba7c: mov             x0, x1
    // 0xe8ba80: b               #0xe8ba58
    // 0xe8ba84: r1 = <bool>
    //     0xe8ba84: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xe8ba88: r2 = 56
    //     0xe8ba88: movz            x2, #0x38
    // 0xe8ba8c: r0 = _GrowableList()
    //     0xe8ba8c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe8ba90: LoadField: r1 = r0->field_b
    //     0xe8ba90: ldur            w1, [x0, #0xb]
    // 0xe8ba94: r2 = LoadInt32Instr(r1)
    //     0xe8ba94: sbfx            x2, x1, #1, #0x1f
    // 0xe8ba98: stur            x2, [fp, #-0x58]
    // 0xe8ba9c: LoadField: r1 = r0->field_f
    //     0xe8ba9c: ldur            w1, [x0, #0xf]
    // 0xe8baa0: DecompressPointer r1
    //     0xe8baa0: add             x1, x1, HEAP, lsl #32
    // 0xe8baa4: stur            x1, [fp, #-0x50]
    // 0xe8baa8: r0 = 0
    //     0xe8baa8: movz            x0, #0
    // 0xe8baac: CheckStackOverflow
    //     0xe8baac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8bab0: cmp             SP, x16
    //     0xe8bab4: b.ls            #0xe8c5a0
    // 0xe8bab8: cmp             x0, x2
    // 0xe8babc: b.ge            #0xe8bad8
    // 0xe8bac0: add             x3, x1, x0, lsl #2
    // 0xe8bac4: r16 = false
    //     0xe8bac4: add             x16, NULL, #0x30  ; false
    // 0xe8bac8: StoreField: r3->field_f = r16
    //     0xe8bac8: stur            w16, [x3, #0xf]
    // 0xe8bacc: add             x3, x0, #1
    // 0xe8bad0: mov             x0, x3
    // 0xe8bad4: b               #0xe8baac
    // 0xe8bad8: ldur            x0, [fp, #-0x10]
    // 0xe8badc: LoadField: r3 = r0->field_13
    //     0xe8badc: ldur            w3, [x0, #0x13]
    // 0xe8bae0: r4 = LoadInt32Instr(r3)
    //     0xe8bae0: sbfx            x4, x3, #1, #0x1f
    // 0xe8bae4: stur            x4, [fp, #-0x48]
    // 0xe8bae8: ldur            x3, [fp, #-0x30]
    // 0xe8baec: r5 = 0
    //     0xe8baec: movz            x5, #0
    // 0xe8baf0: stur            x5, [fp, #-0x40]
    // 0xe8baf4: CheckStackOverflow
    //     0xe8baf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8baf8: cmp             SP, x16
    //     0xe8bafc: b.ls            #0xe8c5a8
    // 0xe8bb00: cmp             x5, #0x38
    // 0xe8bb04: b.ge            #0xe8bc48
    // 0xe8bb08: r0 = InitLateStaticField(0xd68) // [package:pointycastle/block/des_base.dart] DesBase::pc1
    //     0xe8bb08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8bb0c: ldr             x0, [x0, #0x1ad0]
    //     0xe8bb10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8bb14: cmp             w0, w16
    //     0xe8bb18: b.ne            #0xe8bb28
    //     0xe8bb1c: add             x2, PP, #0x21, lsl #12  ; [pp+0x21d90] Field <DesBase.pc1>: static late final (offset: 0xd68)
    //     0xe8bb20: ldr             x2, [x2, #0xd90]
    //     0xe8bb24: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8bb28: mov             x2, x0
    // 0xe8bb2c: LoadField: r0 = r2->field_13
    //     0xe8bb2c: ldur            w0, [x2, #0x13]
    // 0xe8bb30: r1 = LoadInt32Instr(r0)
    //     0xe8bb30: sbfx            x1, x0, #1, #0x1f
    // 0xe8bb34: mov             x0, x1
    // 0xe8bb38: ldur            x1, [fp, #-0x40]
    // 0xe8bb3c: cmp             x1, x0
    // 0xe8bb40: b.hs            #0xe8c5b0
    // 0xe8bb44: ldur            x3, [fp, #-0x40]
    // 0xe8bb48: ArrayLoad: r4 = r2[r3]  ; List_1
    //     0xe8bb48: add             x16, x2, x3
    //     0xe8bb4c: ldrb            w4, [x16, #0x17]
    // 0xe8bb50: stur            x4, [fp, #-0x68]
    // 0xe8bb54: asr             x2, x4, #3
    // 0xe8bb58: ldur            x0, [fp, #-0x48]
    // 0xe8bb5c: mov             x1, x2
    // 0xe8bb60: cmp             x1, x0
    // 0xe8bb64: b.hs            #0xe8c5b4
    // 0xe8bb68: ldur            x0, [fp, #-0x10]
    // 0xe8bb6c: ArrayLoad: r1 = r0[r2]  ; List_1
    //     0xe8bb6c: add             x16, x0, x2
    //     0xe8bb70: ldrb            w1, [x16, #0x17]
    // 0xe8bb74: stur            x1, [fp, #-0x60]
    // 0xe8bb78: r0 = InitLateStaticField(0xd60) // [package:pointycastle/block/des_base.dart] DesBase::bytebit
    //     0xe8bb78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8bb7c: ldr             x0, [x0, #0x1ac0]
    //     0xe8bb80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8bb84: cmp             w0, w16
    //     0xe8bb88: b.ne            #0xe8bb98
    //     0xe8bb8c: add             x2, PP, #0x21, lsl #12  ; [pp+0x21d98] Field <DesBase.bytebit>: static late final (offset: 0xd60)
    //     0xe8bb90: ldr             x2, [x2, #0xd98]
    //     0xe8bb94: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8bb98: mov             x2, x0
    // 0xe8bb9c: ldur            x0, [fp, #-0x68]
    // 0xe8bba0: ubfx            x0, x0, #0, #0x20
    // 0xe8bba4: r3 = 7
    //     0xe8bba4: movz            x3, #0x7
    // 0xe8bba8: and             x1, x0, x3
    // 0xe8bbac: LoadField: r0 = r2->field_b
    //     0xe8bbac: ldur            w0, [x2, #0xb]
    // 0xe8bbb0: r4 = LoadInt32Instr(r0)
    //     0xe8bbb0: sbfx            x4, x0, #1, #0x1f
    // 0xe8bbb4: mov             x5, x1
    // 0xe8bbb8: ubfx            x5, x5, #0, #0x20
    // 0xe8bbbc: mov             x0, x4
    // 0xe8bbc0: mov             x1, x5
    // 0xe8bbc4: cmp             x1, x0
    // 0xe8bbc8: b.hs            #0xe8c5b8
    // 0xe8bbcc: LoadField: r0 = r2->field_f
    //     0xe8bbcc: ldur            w0, [x2, #0xf]
    // 0xe8bbd0: DecompressPointer r0
    //     0xe8bbd0: add             x0, x0, HEAP, lsl #32
    // 0xe8bbd4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xe8bbd4: add             x16, x0, x5, lsl #2
    //     0xe8bbd8: ldur            w1, [x16, #0xf]
    // 0xe8bbdc: DecompressPointer r1
    //     0xe8bbdc: add             x1, x1, HEAP, lsl #32
    // 0xe8bbe0: r0 = LoadInt32Instr(r1)
    //     0xe8bbe0: sbfx            x0, x1, #1, #0x1f
    //     0xe8bbe4: tbz             w1, #0, #0xe8bbec
    //     0xe8bbe8: ldur            x0, [x1, #7]
    // 0xe8bbec: ldur            x1, [fp, #-0x60]
    // 0xe8bbf0: ubfx            x1, x1, #0, #0x20
    // 0xe8bbf4: and             x2, x1, x0
    // 0xe8bbf8: ubfx            x2, x2, #0, #0x20
    // 0xe8bbfc: cbnz            x2, #0xe8bc08
    // 0xe8bc00: r4 = false
    //     0xe8bc00: add             x4, NULL, #0x30  ; false
    // 0xe8bc04: b               #0xe8bc0c
    // 0xe8bc08: r4 = true
    //     0xe8bc08: add             x4, NULL, #0x20  ; true
    // 0xe8bc0c: ldur            x0, [fp, #-0x38]
    // 0xe8bc10: ldur            x1, [fp, #-0x40]
    // 0xe8bc14: cmp             x1, x0
    // 0xe8bc18: b.hs            #0xe8c5bc
    // 0xe8bc1c: ldur            x0, [fp, #-0x40]
    // 0xe8bc20: ldur            x1, [fp, #-0x30]
    // 0xe8bc24: ArrayStore: r1[r0] = r4  ; Unknown_4
    //     0xe8bc24: add             x2, x1, x0, lsl #2
    //     0xe8bc28: stur            w4, [x2, #0xf]
    // 0xe8bc2c: add             x5, x0, #1
    // 0xe8bc30: ldur            x0, [fp, #-0x10]
    // 0xe8bc34: mov             x3, x1
    // 0xe8bc38: ldur            x1, [fp, #-0x50]
    // 0xe8bc3c: ldur            x2, [fp, #-0x58]
    // 0xe8bc40: ldur            x4, [fp, #-0x48]
    // 0xe8bc44: b               #0xe8baf0
    // 0xe8bc48: mov             x1, x3
    // 0xe8bc4c: r4 = 0
    //     0xe8bc4c: movz            x4, #0
    // 0xe8bc50: ldur            x3, [fp, #-8]
    // 0xe8bc54: ldur            x2, [fp, #-0x18]
    // 0xe8bc58: ldur            x0, [fp, #-0x50]
    // 0xe8bc5c: stur            x4, [fp, #-0x40]
    // 0xe8bc60: CheckStackOverflow
    //     0xe8bc60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8bc64: cmp             SP, x16
    //     0xe8bc68: b.ls            #0xe8c5c0
    // 0xe8bc6c: cmp             x4, #0x10
    // 0xe8bc70: b.ge            #0xe8c2b4
    // 0xe8bc74: tbnz            w3, #4, #0xe8bd00
    // 0xe8bc78: r0 = InitLateStaticField(0xf58) // [package:pointycastle/src/ufixnum.dart] ::_MASK32_HI_BITS
    //     0xe8bc78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8bc7c: ldr             x0, [x0, #0x1eb0]
    //     0xe8bc80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8bc84: cmp             w0, w16
    //     0xe8bc88: b.ne            #0xe8bc98
    //     0xe8bc8c: add             x2, PP, #0x19, lsl #12  ; [pp+0x194e0] Field <::._MASK32_HI_BITS@1011143242>: static late final (offset: 0xf58)
    //     0xe8bc90: ldr             x2, [x2, #0x4e0]
    //     0xe8bc94: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8bc98: mov             x2, x0
    // 0xe8bc9c: LoadField: r0 = r2->field_b
    //     0xe8bc9c: ldur            w0, [x2, #0xb]
    // 0xe8bca0: r1 = LoadInt32Instr(r0)
    //     0xe8bca0: sbfx            x1, x0, #1, #0x1f
    // 0xe8bca4: mov             x0, x1
    // 0xe8bca8: r1 = 1
    //     0xe8bca8: movz            x1, #0x1
    // 0xe8bcac: cmp             x1, x0
    // 0xe8bcb0: b.hs            #0xe8c5c8
    // 0xe8bcb4: LoadField: r0 = r2->field_f
    //     0xe8bcb4: ldur            w0, [x2, #0xf]
    // 0xe8bcb8: DecompressPointer r0
    //     0xe8bcb8: add             x0, x0, HEAP, lsl #32
    // 0xe8bcbc: LoadField: r1 = r0->field_13
    //     0xe8bcbc: ldur            w1, [x0, #0x13]
    // 0xe8bcc0: DecompressPointer r1
    //     0xe8bcc0: add             x1, x1, HEAP, lsl #32
    // 0xe8bcc4: r0 = LoadInt32Instr(r1)
    //     0xe8bcc4: sbfx            x0, x1, #1, #0x1f
    //     0xe8bcc8: tbz             w1, #0, #0xe8bcd0
    //     0xe8bccc: ldur            x0, [x1, #7]
    // 0xe8bcd0: ldur            x1, [fp, #-0x40]
    // 0xe8bcd4: ubfx            x1, x1, #0, #0x20
    // 0xe8bcd8: and             x2, x1, x0
    // 0xe8bcdc: r1 = 1
    //     0xe8bcdc: movz            x1, #0x1
    // 0xe8bce0: tbnz            x1, #0x3f, #0xe8c5cc
    // 0xe8bce4: lsl             w0, w2, w1
    // 0xe8bce8: cmp             x1, #0x1f
    // 0xe8bcec: csel            x0, x0, xzr, le
    // 0xe8bcf0: ubfx            x0, x0, #0, #0x20
    // 0xe8bcf4: mov             x4, x0
    // 0xe8bcf8: mov             x3, x1
    // 0xe8bcfc: b               #0xe8bd94
    // 0xe8bd00: mov             x2, x4
    // 0xe8bd04: r1 = 1
    //     0xe8bd04: movz            x1, #0x1
    // 0xe8bd08: r0 = 15
    //     0xe8bd08: movz            x0, #0xf
    // 0xe8bd0c: sub             x3, x0, x2
    // 0xe8bd10: stur            x3, [fp, #-0x48]
    // 0xe8bd14: r0 = InitLateStaticField(0xf58) // [package:pointycastle/src/ufixnum.dart] ::_MASK32_HI_BITS
    //     0xe8bd14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8bd18: ldr             x0, [x0, #0x1eb0]
    //     0xe8bd1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8bd20: cmp             w0, w16
    //     0xe8bd24: b.ne            #0xe8bd34
    //     0xe8bd28: add             x2, PP, #0x19, lsl #12  ; [pp+0x194e0] Field <::._MASK32_HI_BITS@1011143242>: static late final (offset: 0xf58)
    //     0xe8bd2c: ldr             x2, [x2, #0x4e0]
    //     0xe8bd30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8bd34: mov             x2, x0
    // 0xe8bd38: LoadField: r0 = r2->field_b
    //     0xe8bd38: ldur            w0, [x2, #0xb]
    // 0xe8bd3c: r1 = LoadInt32Instr(r0)
    //     0xe8bd3c: sbfx            x1, x0, #1, #0x1f
    // 0xe8bd40: mov             x0, x1
    // 0xe8bd44: r1 = 1
    //     0xe8bd44: movz            x1, #0x1
    // 0xe8bd48: cmp             x1, x0
    // 0xe8bd4c: b.hs            #0xe8c5e8
    // 0xe8bd50: LoadField: r0 = r2->field_f
    //     0xe8bd50: ldur            w0, [x2, #0xf]
    // 0xe8bd54: DecompressPointer r0
    //     0xe8bd54: add             x0, x0, HEAP, lsl #32
    // 0xe8bd58: LoadField: r1 = r0->field_13
    //     0xe8bd58: ldur            w1, [x0, #0x13]
    // 0xe8bd5c: DecompressPointer r1
    //     0xe8bd5c: add             x1, x1, HEAP, lsl #32
    // 0xe8bd60: r0 = LoadInt32Instr(r1)
    //     0xe8bd60: sbfx            x0, x1, #1, #0x1f
    //     0xe8bd64: tbz             w1, #0, #0xe8bd6c
    //     0xe8bd68: ldur            x0, [x1, #7]
    // 0xe8bd6c: ldur            x1, [fp, #-0x48]
    // 0xe8bd70: ubfx            x1, x1, #0, #0x20
    // 0xe8bd74: and             x2, x1, x0
    // 0xe8bd78: r3 = 1
    //     0xe8bd78: movz            x3, #0x1
    // 0xe8bd7c: tbnz            x3, #0x3f, #0xe8c5ec
    // 0xe8bd80: lsl             w0, w2, w3
    // 0xe8bd84: cmp             x3, #0x1f
    // 0xe8bd88: csel            x0, x0, xzr, le
    // 0xe8bd8c: ubfx            x0, x0, #0, #0x20
    // 0xe8bd90: mov             x4, x0
    // 0xe8bd94: ldur            x2, [fp, #-0x18]
    // 0xe8bd98: stur            x4, [fp, #-0x68]
    // 0xe8bd9c: add             x5, x4, #1
    // 0xe8bda0: ldur            x0, [fp, #-0x20]
    // 0xe8bda4: mov             x1, x5
    // 0xe8bda8: stur            x5, [fp, #-0x60]
    // 0xe8bdac: cmp             x1, x0
    // 0xe8bdb0: b.hs            #0xe8c608
    // 0xe8bdb4: ArrayStore: r2[r5] = rZR  ; Unknown_4
    //     0xe8bdb4: add             x0, x2, x5, lsl #2
    //     0xe8bdb8: stur            wzr, [x0, #0xf]
    // 0xe8bdbc: ldur            x0, [fp, #-0x20]
    // 0xe8bdc0: mov             x1, x4
    // 0xe8bdc4: cmp             x1, x0
    // 0xe8bdc8: b.hs            #0xe8c60c
    // 0xe8bdcc: ArrayStore: r2[r4] = rZR  ; Unknown_4
    //     0xe8bdcc: add             x0, x2, x4, lsl #2
    //     0xe8bdd0: stur            wzr, [x0, #0xf]
    // 0xe8bdd4: ldur            x1, [fp, #-0x40]
    // 0xe8bdd8: ldur            x0, [fp, #-0x30]
    // 0xe8bddc: ldur            x6, [fp, #-0x50]
    // 0xe8bde0: r7 = 0
    //     0xe8bde0: movz            x7, #0
    // 0xe8bde4: stur            x7, [fp, #-0x48]
    // 0xe8bde8: CheckStackOverflow
    //     0xe8bde8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8bdec: cmp             SP, x16
    //     0xe8bdf0: b.ls            #0xe8c610
    // 0xe8bdf4: cmp             x7, #0x1c
    // 0xe8bdf8: b.ge            #0xe8bef4
    // 0xe8bdfc: r0 = InitLateStaticField(0xd6c) // [package:pointycastle/block/des_base.dart] DesBase::totrot
    //     0xe8bdfc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8be00: ldr             x0, [x0, #0x1ad8]
    //     0xe8be04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8be08: cmp             w0, w16
    //     0xe8be0c: b.ne            #0xe8be1c
    //     0xe8be10: add             x2, PP, #0x21, lsl #12  ; [pp+0x21da0] Field <DesBase.totrot>: static late final (offset: 0xd6c)
    //     0xe8be14: ldr             x2, [x2, #0xda0]
    //     0xe8be18: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8be1c: mov             x2, x0
    // 0xe8be20: LoadField: r0 = r2->field_13
    //     0xe8be20: ldur            w0, [x2, #0x13]
    // 0xe8be24: r1 = LoadInt32Instr(r0)
    //     0xe8be24: sbfx            x1, x0, #1, #0x1f
    // 0xe8be28: mov             x0, x1
    // 0xe8be2c: ldur            x1, [fp, #-0x40]
    // 0xe8be30: cmp             x1, x0
    // 0xe8be34: b.hs            #0xe8c618
    // 0xe8be38: ldur            x3, [fp, #-0x40]
    // 0xe8be3c: ArrayLoad: r0 = r2[r3]  ; List_1
    //     0xe8be3c: add             x16, x2, x3
    //     0xe8be40: ldrb            w0, [x16, #0x17]
    // 0xe8be44: ldur            x2, [fp, #-0x48]
    // 0xe8be48: add             x4, x2, x0
    // 0xe8be4c: cmp             x4, #0x1c
    // 0xe8be50: b.ge            #0xe8be94
    // 0xe8be54: ldur            x5, [fp, #-0x30]
    // 0xe8be58: ldur            x6, [fp, #-0x50]
    // 0xe8be5c: ldur            x0, [fp, #-0x38]
    // 0xe8be60: mov             x1, x4
    // 0xe8be64: cmp             x1, x0
    // 0xe8be68: b.hs            #0xe8c61c
    // 0xe8be6c: ArrayLoad: r7 = r5[r4]  ; Unknown_4
    //     0xe8be6c: add             x16, x5, x4, lsl #2
    //     0xe8be70: ldur            w7, [x16, #0xf]
    // 0xe8be74: DecompressPointer r7
    //     0xe8be74: add             x7, x7, HEAP, lsl #32
    // 0xe8be78: ldur            x0, [fp, #-0x58]
    // 0xe8be7c: mov             x1, x2
    // 0xe8be80: cmp             x1, x0
    // 0xe8be84: b.hs            #0xe8c620
    // 0xe8be88: ArrayStore: r6[r2] = r7  ; Unknown_4
    //     0xe8be88: add             x0, x6, x2, lsl #2
    //     0xe8be8c: stur            w7, [x0, #0xf]
    // 0xe8be90: b               #0xe8bed4
    // 0xe8be94: ldur            x5, [fp, #-0x30]
    // 0xe8be98: ldur            x6, [fp, #-0x50]
    // 0xe8be9c: sub             x7, x4, #0x1c
    // 0xe8bea0: ldur            x0, [fp, #-0x38]
    // 0xe8bea4: mov             x1, x7
    // 0xe8bea8: cmp             x1, x0
    // 0xe8beac: b.hs            #0xe8c624
    // 0xe8beb0: ArrayLoad: r4 = r5[r7]  ; Unknown_4
    //     0xe8beb0: add             x16, x5, x7, lsl #2
    //     0xe8beb4: ldur            w4, [x16, #0xf]
    // 0xe8beb8: DecompressPointer r4
    //     0xe8beb8: add             x4, x4, HEAP, lsl #32
    // 0xe8bebc: ldur            x0, [fp, #-0x58]
    // 0xe8bec0: mov             x1, x2
    // 0xe8bec4: cmp             x1, x0
    // 0xe8bec8: b.hs            #0xe8c628
    // 0xe8becc: ArrayStore: r6[r2] = r4  ; Unknown_4
    //     0xe8becc: add             x0, x6, x2, lsl #2
    //     0xe8bed0: stur            w4, [x0, #0xf]
    // 0xe8bed4: add             x7, x2, #1
    // 0xe8bed8: mov             x1, x3
    // 0xe8bedc: ldur            x4, [fp, #-0x68]
    // 0xe8bee0: mov             x0, x5
    // 0xe8bee4: ldur            x5, [fp, #-0x60]
    // 0xe8bee8: ldur            x2, [fp, #-0x18]
    // 0xe8beec: r3 = 1
    //     0xe8beec: movz            x3, #0x1
    // 0xe8bef0: b               #0xe8bde4
    // 0xe8bef4: mov             x3, x1
    // 0xe8bef8: mov             x5, x0
    // 0xe8befc: r1 = 28
    //     0xe8befc: movz            x1, #0x1c
    // 0xe8bf00: stur            x1, [fp, #-0x48]
    // 0xe8bf04: CheckStackOverflow
    //     0xe8bf04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8bf08: cmp             SP, x16
    //     0xe8bf0c: b.ls            #0xe8c62c
    // 0xe8bf10: cmp             x1, #0x38
    // 0xe8bf14: b.ge            #0xe8bff8
    // 0xe8bf18: r0 = InitLateStaticField(0xd6c) // [package:pointycastle/block/des_base.dart] DesBase::totrot
    //     0xe8bf18: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8bf1c: ldr             x0, [x0, #0x1ad8]
    //     0xe8bf20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8bf24: cmp             w0, w16
    //     0xe8bf28: b.ne            #0xe8bf38
    //     0xe8bf2c: add             x2, PP, #0x21, lsl #12  ; [pp+0x21da0] Field <DesBase.totrot>: static late final (offset: 0xd6c)
    //     0xe8bf30: ldr             x2, [x2, #0xda0]
    //     0xe8bf34: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8bf38: mov             x2, x0
    // 0xe8bf3c: LoadField: r0 = r2->field_13
    //     0xe8bf3c: ldur            w0, [x2, #0x13]
    // 0xe8bf40: r1 = LoadInt32Instr(r0)
    //     0xe8bf40: sbfx            x1, x0, #1, #0x1f
    // 0xe8bf44: mov             x0, x1
    // 0xe8bf48: ldur            x1, [fp, #-0x40]
    // 0xe8bf4c: cmp             x1, x0
    // 0xe8bf50: b.hs            #0xe8c634
    // 0xe8bf54: ldur            x3, [fp, #-0x40]
    // 0xe8bf58: ArrayLoad: r0 = r2[r3]  ; List_1
    //     0xe8bf58: add             x16, x2, x3
    //     0xe8bf5c: ldrb            w0, [x16, #0x17]
    // 0xe8bf60: ldur            x2, [fp, #-0x48]
    // 0xe8bf64: add             x4, x2, x0
    // 0xe8bf68: cmp             x4, #0x38
    // 0xe8bf6c: b.ge            #0xe8bfb0
    // 0xe8bf70: ldur            x5, [fp, #-0x30]
    // 0xe8bf74: ldur            x6, [fp, #-0x50]
    // 0xe8bf78: ldur            x0, [fp, #-0x38]
    // 0xe8bf7c: mov             x1, x4
    // 0xe8bf80: cmp             x1, x0
    // 0xe8bf84: b.hs            #0xe8c638
    // 0xe8bf88: ArrayLoad: r7 = r5[r4]  ; Unknown_4
    //     0xe8bf88: add             x16, x5, x4, lsl #2
    //     0xe8bf8c: ldur            w7, [x16, #0xf]
    // 0xe8bf90: DecompressPointer r7
    //     0xe8bf90: add             x7, x7, HEAP, lsl #32
    // 0xe8bf94: ldur            x0, [fp, #-0x58]
    // 0xe8bf98: mov             x1, x2
    // 0xe8bf9c: cmp             x1, x0
    // 0xe8bfa0: b.hs            #0xe8c63c
    // 0xe8bfa4: ArrayStore: r6[r2] = r7  ; Unknown_4
    //     0xe8bfa4: add             x0, x6, x2, lsl #2
    //     0xe8bfa8: stur            w7, [x0, #0xf]
    // 0xe8bfac: b               #0xe8bff0
    // 0xe8bfb0: ldur            x5, [fp, #-0x30]
    // 0xe8bfb4: ldur            x6, [fp, #-0x50]
    // 0xe8bfb8: sub             x7, x4, #0x1c
    // 0xe8bfbc: ldur            x0, [fp, #-0x38]
    // 0xe8bfc0: mov             x1, x7
    // 0xe8bfc4: cmp             x1, x0
    // 0xe8bfc8: b.hs            #0xe8c640
    // 0xe8bfcc: ArrayLoad: r4 = r5[r7]  ; Unknown_4
    //     0xe8bfcc: add             x16, x5, x7, lsl #2
    //     0xe8bfd0: ldur            w4, [x16, #0xf]
    // 0xe8bfd4: DecompressPointer r4
    //     0xe8bfd4: add             x4, x4, HEAP, lsl #32
    // 0xe8bfd8: ldur            x0, [fp, #-0x58]
    // 0xe8bfdc: mov             x1, x2
    // 0xe8bfe0: cmp             x1, x0
    // 0xe8bfe4: b.hs            #0xe8c644
    // 0xe8bfe8: ArrayStore: r6[r2] = r4  ; Unknown_4
    //     0xe8bfe8: add             x0, x6, x2, lsl #2
    //     0xe8bfec: stur            w4, [x0, #0xf]
    // 0xe8bff0: add             x1, x2, #1
    // 0xe8bff4: b               #0xe8bf00
    // 0xe8bff8: ldur            x2, [fp, #-0x60]
    // 0xe8bffc: r4 = 0
    //     0xe8bffc: movz            x4, #0
    // 0xe8c000: ldur            x0, [fp, #-0x68]
    // 0xe8c004: ldur            x1, [fp, #-0x18]
    // 0xe8c008: stur            x4, [fp, #-0x48]
    // 0xe8c00c: CheckStackOverflow
    //     0xe8c00c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8c010: cmp             SP, x16
    //     0xe8c014: b.ls            #0xe8c648
    // 0xe8c018: cmp             x4, #0x18
    // 0xe8c01c: b.ge            #0xe8c2a4
    // 0xe8c020: r0 = InitLateStaticField(0xd70) // [package:pointycastle/block/des_base.dart] DesBase::pc2
    //     0xe8c020: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8c024: ldr             x0, [x0, #0x1ae0]
    //     0xe8c028: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8c02c: cmp             w0, w16
    //     0xe8c030: b.ne            #0xe8c040
    //     0xe8c034: add             x2, PP, #0x21, lsl #12  ; [pp+0x21da8] Field <DesBase.pc2>: static late final (offset: 0xd70)
    //     0xe8c038: ldr             x2, [x2, #0xda8]
    //     0xe8c03c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8c040: mov             x2, x0
    // 0xe8c044: stur            x2, [fp, #-0x78]
    // 0xe8c048: LoadField: r0 = r2->field_13
    //     0xe8c048: ldur            w0, [x2, #0x13]
    // 0xe8c04c: r3 = LoadInt32Instr(r0)
    //     0xe8c04c: sbfx            x3, x0, #1, #0x1f
    // 0xe8c050: mov             x0, x3
    // 0xe8c054: ldur            x1, [fp, #-0x48]
    // 0xe8c058: stur            x3, [fp, #-0x70]
    // 0xe8c05c: cmp             x1, x0
    // 0xe8c060: b.hs            #0xe8c650
    // 0xe8c064: ldur            x4, [fp, #-0x48]
    // 0xe8c068: ArrayLoad: r5 = r2[r4]  ; List_1
    //     0xe8c068: add             x16, x2, x4
    //     0xe8c06c: ldrb            w5, [x16, #0x17]
    // 0xe8c070: ldur            x0, [fp, #-0x58]
    // 0xe8c074: mov             x1, x5
    // 0xe8c078: cmp             x1, x0
    // 0xe8c07c: b.hs            #0xe8c654
    // 0xe8c080: ldur            x0, [fp, #-0x50]
    // 0xe8c084: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xe8c084: add             x16, x0, x5, lsl #2
    //     0xe8c088: ldur            w1, [x16, #0xf]
    // 0xe8c08c: DecompressPointer r1
    //     0xe8c08c: add             x1, x1, HEAP, lsl #32
    // 0xe8c090: tbnz            w1, #4, #0xe8c168
    // 0xe8c094: ldur            x5, [fp, #-0x68]
    // 0xe8c098: ldur            x1, [fp, #-0x18]
    // 0xe8c09c: ArrayLoad: r6 = r1[r5]  ; Unknown_4
    //     0xe8c09c: add             x16, x1, x5, lsl #2
    //     0xe8c0a0: ldur            w6, [x16, #0xf]
    // 0xe8c0a4: DecompressPointer r6
    //     0xe8c0a4: add             x6, x6, HEAP, lsl #32
    // 0xe8c0a8: stur            x6, [fp, #-0x10]
    // 0xe8c0ac: r0 = InitLateStaticField(0xd64) // [package:pointycastle/block/des_base.dart] DesBase::bigbyte
    //     0xe8c0ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8c0b0: ldr             x0, [x0, #0x1ac8]
    //     0xe8c0b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8c0b8: cmp             w0, w16
    //     0xe8c0bc: b.ne            #0xe8c0cc
    //     0xe8c0c0: add             x2, PP, #0x21, lsl #12  ; [pp+0x21db0] Field <DesBase.bigbyte>: static late final (offset: 0xd64)
    //     0xe8c0c4: ldr             x2, [x2, #0xdb0]
    //     0xe8c0c8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8c0cc: mov             x2, x0
    // 0xe8c0d0: LoadField: r0 = r2->field_b
    //     0xe8c0d0: ldur            w0, [x2, #0xb]
    // 0xe8c0d4: r1 = LoadInt32Instr(r0)
    //     0xe8c0d4: sbfx            x1, x0, #1, #0x1f
    // 0xe8c0d8: mov             x0, x1
    // 0xe8c0dc: ldur            x1, [fp, #-0x48]
    // 0xe8c0e0: cmp             x1, x0
    // 0xe8c0e4: b.hs            #0xe8c658
    // 0xe8c0e8: LoadField: r0 = r2->field_f
    //     0xe8c0e8: ldur            w0, [x2, #0xf]
    // 0xe8c0ec: DecompressPointer r0
    //     0xe8c0ec: add             x0, x0, HEAP, lsl #32
    // 0xe8c0f0: ldur            x2, [fp, #-0x48]
    // 0xe8c0f4: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xe8c0f4: add             x16, x0, x2, lsl #2
    //     0xe8c0f8: ldur            w1, [x16, #0xf]
    // 0xe8c0fc: DecompressPointer r1
    //     0xe8c0fc: add             x1, x1, HEAP, lsl #32
    // 0xe8c100: ldur            x0, [fp, #-0x10]
    // 0xe8c104: r3 = LoadInt32Instr(r0)
    //     0xe8c104: sbfx            x3, x0, #1, #0x1f
    //     0xe8c108: tbz             w0, #0, #0xe8c110
    //     0xe8c10c: ldur            x3, [x0, #7]
    // 0xe8c110: r0 = LoadInt32Instr(r1)
    //     0xe8c110: sbfx            x0, x1, #1, #0x1f
    //     0xe8c114: tbz             w1, #0, #0xe8c11c
    //     0xe8c118: ldur            x0, [x1, #7]
    // 0xe8c11c: orr             x4, x3, x0
    // 0xe8c120: r0 = BoxInt64Instr(r4)
    //     0xe8c120: sbfiz           x0, x4, #1, #0x1f
    //     0xe8c124: cmp             x4, x0, asr #1
    //     0xe8c128: b.eq            #0xe8c134
    //     0xe8c12c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8c130: stur            x4, [x0, #7]
    // 0xe8c134: ldur            x1, [fp, #-0x18]
    // 0xe8c138: ldur            x3, [fp, #-0x68]
    // 0xe8c13c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe8c13c: add             x25, x1, x3, lsl #2
    //     0xe8c140: add             x25, x25, #0xf
    //     0xe8c144: str             w0, [x25]
    //     0xe8c148: tbz             w0, #0, #0xe8c164
    //     0xe8c14c: ldurb           w16, [x1, #-1]
    //     0xe8c150: ldurb           w17, [x0, #-1]
    //     0xe8c154: and             x16, x17, x16, lsr #2
    //     0xe8c158: tst             x16, HEAP, lsr #32
    //     0xe8c15c: b.eq            #0xe8c164
    //     0xe8c160: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8c164: b               #0xe8c170
    // 0xe8c168: ldur            x3, [fp, #-0x68]
    // 0xe8c16c: mov             x2, x4
    // 0xe8c170: ldur            x4, [fp, #-0x78]
    // 0xe8c174: ldur            x5, [fp, #-0x50]
    // 0xe8c178: add             x6, x2, #0x18
    // 0xe8c17c: ldur            x0, [fp, #-0x70]
    // 0xe8c180: mov             x1, x6
    // 0xe8c184: cmp             x1, x0
    // 0xe8c188: b.hs            #0xe8c65c
    // 0xe8c18c: ArrayLoad: r7 = r4[r6]  ; List_1
    //     0xe8c18c: add             x16, x4, x6
    //     0xe8c190: ldrb            w7, [x16, #0x17]
    // 0xe8c194: ldur            x0, [fp, #-0x58]
    // 0xe8c198: mov             x1, x7
    // 0xe8c19c: cmp             x1, x0
    // 0xe8c1a0: b.hs            #0xe8c660
    // 0xe8c1a4: ArrayLoad: r0 = r5[r7]  ; Unknown_4
    //     0xe8c1a4: add             x16, x5, x7, lsl #2
    //     0xe8c1a8: ldur            w0, [x16, #0xf]
    // 0xe8c1ac: DecompressPointer r0
    //     0xe8c1ac: add             x0, x0, HEAP, lsl #32
    // 0xe8c1b0: tbnz            w0, #4, #0xe8c288
    // 0xe8c1b4: ldur            x0, [fp, #-0x60]
    // 0xe8c1b8: ldur            x1, [fp, #-0x18]
    // 0xe8c1bc: ArrayLoad: r4 = r1[r0]  ; Unknown_4
    //     0xe8c1bc: add             x16, x1, x0, lsl #2
    //     0xe8c1c0: ldur            w4, [x16, #0xf]
    // 0xe8c1c4: DecompressPointer r4
    //     0xe8c1c4: add             x4, x4, HEAP, lsl #32
    // 0xe8c1c8: stur            x4, [fp, #-0x10]
    // 0xe8c1cc: r0 = InitLateStaticField(0xd64) // [package:pointycastle/block/des_base.dart] DesBase::bigbyte
    //     0xe8c1cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8c1d0: ldr             x0, [x0, #0x1ac8]
    //     0xe8c1d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8c1d8: cmp             w0, w16
    //     0xe8c1dc: b.ne            #0xe8c1ec
    //     0xe8c1e0: add             x2, PP, #0x21, lsl #12  ; [pp+0x21db0] Field <DesBase.bigbyte>: static late final (offset: 0xd64)
    //     0xe8c1e4: ldr             x2, [x2, #0xdb0]
    //     0xe8c1e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8c1ec: mov             x2, x0
    // 0xe8c1f0: LoadField: r0 = r2->field_b
    //     0xe8c1f0: ldur            w0, [x2, #0xb]
    // 0xe8c1f4: r1 = LoadInt32Instr(r0)
    //     0xe8c1f4: sbfx            x1, x0, #1, #0x1f
    // 0xe8c1f8: mov             x0, x1
    // 0xe8c1fc: ldur            x1, [fp, #-0x48]
    // 0xe8c200: cmp             x1, x0
    // 0xe8c204: b.hs            #0xe8c664
    // 0xe8c208: LoadField: r0 = r2->field_f
    //     0xe8c208: ldur            w0, [x2, #0xf]
    // 0xe8c20c: DecompressPointer r0
    //     0xe8c20c: add             x0, x0, HEAP, lsl #32
    // 0xe8c210: ldur            x2, [fp, #-0x48]
    // 0xe8c214: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xe8c214: add             x16, x0, x2, lsl #2
    //     0xe8c218: ldur            w1, [x16, #0xf]
    // 0xe8c21c: DecompressPointer r1
    //     0xe8c21c: add             x1, x1, HEAP, lsl #32
    // 0xe8c220: ldur            x0, [fp, #-0x10]
    // 0xe8c224: r3 = LoadInt32Instr(r0)
    //     0xe8c224: sbfx            x3, x0, #1, #0x1f
    //     0xe8c228: tbz             w0, #0, #0xe8c230
    //     0xe8c22c: ldur            x3, [x0, #7]
    // 0xe8c230: r0 = LoadInt32Instr(r1)
    //     0xe8c230: sbfx            x0, x1, #1, #0x1f
    //     0xe8c234: tbz             w1, #0, #0xe8c23c
    //     0xe8c238: ldur            x0, [x1, #7]
    // 0xe8c23c: orr             x4, x3, x0
    // 0xe8c240: r0 = BoxInt64Instr(r4)
    //     0xe8c240: sbfiz           x0, x4, #1, #0x1f
    //     0xe8c244: cmp             x4, x0, asr #1
    //     0xe8c248: b.eq            #0xe8c254
    //     0xe8c24c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8c250: stur            x4, [x0, #7]
    // 0xe8c254: ldur            x1, [fp, #-0x18]
    // 0xe8c258: ldur            x3, [fp, #-0x60]
    // 0xe8c25c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe8c25c: add             x25, x1, x3, lsl #2
    //     0xe8c260: add             x25, x25, #0xf
    //     0xe8c264: str             w0, [x25]
    //     0xe8c268: tbz             w0, #0, #0xe8c284
    //     0xe8c26c: ldurb           w16, [x1, #-1]
    //     0xe8c270: ldurb           w17, [x0, #-1]
    //     0xe8c274: and             x16, x17, x16, lsr #2
    //     0xe8c278: tst             x16, HEAP, lsr #32
    //     0xe8c27c: b.eq            #0xe8c284
    //     0xe8c280: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8c284: b               #0xe8c28c
    // 0xe8c288: ldur            x3, [fp, #-0x60]
    // 0xe8c28c: add             x4, x2, #1
    // 0xe8c290: mov             x2, x3
    // 0xe8c294: ldur            x3, [fp, #-0x40]
    // 0xe8c298: ldur            x5, [fp, #-0x30]
    // 0xe8c29c: ldur            x6, [fp, #-0x50]
    // 0xe8c2a0: b               #0xe8c000
    // 0xe8c2a4: mov             x0, x3
    // 0xe8c2a8: add             x4, x0, #1
    // 0xe8c2ac: ldur            x1, [fp, #-0x30]
    // 0xe8c2b0: b               #0xe8bc50
    // 0xe8c2b4: r4 = 0
    //     0xe8c2b4: movz            x4, #0
    // 0xe8c2b8: r3 = 252
    //     0xe8c2b8: movz            x3, #0xfc, lsl #16
    // 0xe8c2bc: ldur            x2, [fp, #-0x18]
    // 0xe8c2c0: stur            x4, [fp, #-0x58]
    // 0xe8c2c4: CheckStackOverflow
    //     0xe8c2c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8c2c8: cmp             SP, x16
    //     0xe8c2cc: b.ls            #0xe8c668
    // 0xe8c2d0: cmp             x4, #0x20
    // 0xe8c2d4: b.eq            #0xe8c578
    // 0xe8c2d8: ldur            x0, [fp, #-0x20]
    // 0xe8c2dc: mov             x1, x4
    // 0xe8c2e0: cmp             x1, x0
    // 0xe8c2e4: b.hs            #0xe8c670
    // 0xe8c2e8: ArrayLoad: r5 = r2[r4]  ; Unknown_4
    //     0xe8c2e8: add             x16, x2, x4, lsl #2
    //     0xe8c2ec: ldur            w5, [x16, #0xf]
    // 0xe8c2f0: DecompressPointer r5
    //     0xe8c2f0: add             x5, x5, HEAP, lsl #32
    // 0xe8c2f4: add             x6, x4, #1
    // 0xe8c2f8: ldur            x0, [fp, #-0x20]
    // 0xe8c2fc: mov             x1, x6
    // 0xe8c300: stur            x6, [fp, #-0x48]
    // 0xe8c304: cmp             x1, x0
    // 0xe8c308: b.hs            #0xe8c674
    // 0xe8c30c: ArrayLoad: r0 = r2[r6]  ; Unknown_4
    //     0xe8c30c: add             x16, x2, x6, lsl #2
    //     0xe8c310: ldur            w0, [x16, #0xf]
    // 0xe8c314: DecompressPointer r0
    //     0xe8c314: add             x0, x0, HEAP, lsl #32
    // 0xe8c318: stur            x0, [fp, #-8]
    // 0xe8c31c: r1 = LoadInt32Instr(r5)
    //     0xe8c31c: sbfx            x1, x5, #1, #0x1f
    //     0xe8c320: tbz             w5, #0, #0xe8c328
    //     0xe8c324: ldur            x1, [x5, #7]
    // 0xe8c328: stur            x1, [fp, #-0x40]
    // 0xe8c32c: and             x5, x1, x3
    // 0xe8c330: stur            x5, [fp, #-0x38]
    // 0xe8c334: r0 = InitLateStaticField(0xf58) // [package:pointycastle/src/ufixnum.dart] ::_MASK32_HI_BITS
    //     0xe8c334: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8c338: ldr             x0, [x0, #0x1eb0]
    //     0xe8c33c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8c340: cmp             w0, w16
    //     0xe8c344: b.ne            #0xe8c354
    //     0xe8c348: add             x2, PP, #0x19, lsl #12  ; [pp+0x194e0] Field <::._MASK32_HI_BITS@1011143242>: static late final (offset: 0xf58)
    //     0xe8c34c: ldr             x2, [x2, #0x4e0]
    //     0xe8c350: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe8c354: mov             x2, x0
    // 0xe8c358: LoadField: r3 = r2->field_b
    //     0xe8c358: ldur            w3, [x2, #0xb]
    // 0xe8c35c: r4 = LoadInt32Instr(r3)
    //     0xe8c35c: sbfx            x4, x3, #1, #0x1f
    // 0xe8c360: mov             x0, x4
    // 0xe8c364: r1 = 6
    //     0xe8c364: movz            x1, #0x6
    // 0xe8c368: cmp             x1, x0
    // 0xe8c36c: b.hs            #0xe8c678
    // 0xe8c370: LoadField: r3 = r2->field_f
    //     0xe8c370: ldur            w3, [x2, #0xf]
    // 0xe8c374: DecompressPointer r3
    //     0xe8c374: add             x3, x3, HEAP, lsl #32
    // 0xe8c378: LoadField: r2 = r3->field_27
    //     0xe8c378: ldur            w2, [x3, #0x27]
    // 0xe8c37c: DecompressPointer r2
    //     0xe8c37c: add             x2, x2, HEAP, lsl #32
    // 0xe8c380: r5 = LoadInt32Instr(r2)
    //     0xe8c380: sbfx            x5, x2, #1, #0x1f
    //     0xe8c384: tbz             w2, #0, #0xe8c38c
    //     0xe8c388: ldur            x5, [x2, #7]
    // 0xe8c38c: ldur            x2, [fp, #-0x38]
    // 0xe8c390: and             x6, x2, x5
    // 0xe8c394: r2 = 6
    //     0xe8c394: movz            x2, #0x6
    // 0xe8c398: tbnz            x2, #0x3f, #0xe8c67c
    // 0xe8c39c: lsl             w5, w6, w2
    // 0xe8c3a0: cmp             x2, #0x1f
    // 0xe8c3a4: csel            x5, x5, xzr, le
    // 0xe8c3a8: ldur            x7, [fp, #-0x40]
    // 0xe8c3ac: r6 = 4032
    //     0xe8c3ac: movz            x6, #0xfc0
    // 0xe8c3b0: and             x8, x7, x6
    // 0xe8c3b4: mov             x0, x4
    // 0xe8c3b8: r1 = 10
    //     0xe8c3b8: movz            x1, #0xa
    // 0xe8c3bc: cmp             x1, x0
    // 0xe8c3c0: b.hs            #0xe8c69c
    // 0xe8c3c4: LoadField: r9 = r3->field_37
    //     0xe8c3c4: ldur            w9, [x3, #0x37]
    // 0xe8c3c8: DecompressPointer r9
    //     0xe8c3c8: add             x9, x9, HEAP, lsl #32
    // 0xe8c3cc: r10 = LoadInt32Instr(r9)
    //     0xe8c3cc: sbfx            x10, x9, #1, #0x1f
    //     0xe8c3d0: tbz             w9, #0, #0xe8c3d8
    //     0xe8c3d4: ldur            x10, [x9, #7]
    // 0xe8c3d8: and             x9, x8, x10
    // 0xe8c3dc: r8 = 10
    //     0xe8c3dc: movz            x8, #0xa
    // 0xe8c3e0: tbnz            x8, #0x3f, #0xe8c6a0
    // 0xe8c3e4: lsl             w10, w9, w8
    // 0xe8c3e8: cmp             x8, #0x1f
    // 0xe8c3ec: csel            x10, x10, xzr, le
    // 0xe8c3f0: ubfx            x5, x5, #0, #0x20
    // 0xe8c3f4: ubfx            x10, x10, #0, #0x20
    // 0xe8c3f8: orr             x9, x5, x10
    // 0xe8c3fc: ldur            x5, [fp, #-8]
    // 0xe8c400: r10 = LoadInt32Instr(r5)
    //     0xe8c400: sbfx            x10, x5, #1, #0x1f
    //     0xe8c404: tbz             w5, #0, #0xe8c40c
    //     0xe8c408: ldur            x10, [x5, #7]
    // 0xe8c40c: r5 = 252
    //     0xe8c40c: movz            x5, #0xfc, lsl #16
    // 0xe8c410: and             x11, x10, x5
    // 0xe8c414: ubfx            x11, x11, #0, #0x20
    // 0xe8c418: asr             x12, x11, #0xa
    // 0xe8c41c: orr             x11, x9, x12
    // 0xe8c420: and             x9, x10, x6
    // 0xe8c424: ubfx            x9, x9, #0, #0x20
    // 0xe8c428: asr             x12, x9, #6
    // 0xe8c42c: orr             x9, x11, x12
    // 0xe8c430: r0 = BoxInt64Instr(r9)
    //     0xe8c430: sbfiz           x0, x9, #1, #0x1f
    //     0xe8c434: cmp             x9, x0, asr #1
    //     0xe8c438: b.eq            #0xe8c444
    //     0xe8c43c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8c440: stur            x9, [x0, #7]
    // 0xe8c444: ldur            x1, [fp, #-0x18]
    // 0xe8c448: ldur            x9, [fp, #-0x58]
    // 0xe8c44c: ArrayStore: r1[r9] = r0  ; List_4
    //     0xe8c44c: add             x25, x1, x9, lsl #2
    //     0xe8c450: add             x25, x25, #0xf
    //     0xe8c454: str             w0, [x25]
    //     0xe8c458: tbz             w0, #0, #0xe8c474
    //     0xe8c45c: ldurb           w16, [x1, #-1]
    //     0xe8c460: ldurb           w17, [x0, #-1]
    //     0xe8c464: and             x16, x17, x16, lsr #2
    //     0xe8c468: tst             x16, HEAP, lsr #32
    //     0xe8c46c: b.eq            #0xe8c474
    //     0xe8c470: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8c474: r11 = 258048
    //     0xe8c474: movz            x11, #0xf000
    //     0xe8c478: movk            x11, #0x3, lsl #16
    // 0xe8c47c: and             x12, x7, x11
    // 0xe8c480: mov             x0, x4
    // 0xe8c484: r1 = 12
    //     0xe8c484: movz            x1, #0xc
    // 0xe8c488: cmp             x1, x0
    // 0xe8c48c: b.hs            #0xe8c6c8
    // 0xe8c490: LoadField: r13 = r3->field_3f
    //     0xe8c490: ldur            w13, [x3, #0x3f]
    // 0xe8c494: DecompressPointer r13
    //     0xe8c494: add             x13, x13, HEAP, lsl #32
    // 0xe8c498: r14 = LoadInt32Instr(r13)
    //     0xe8c498: sbfx            x14, x13, #1, #0x1f
    //     0xe8c49c: tbz             w13, #0, #0xe8c4a4
    //     0xe8c4a0: ldur            x14, [x13, #7]
    // 0xe8c4a4: and             x13, x12, x14
    // 0xe8c4a8: r12 = 12
    //     0xe8c4a8: movz            x12, #0xc
    // 0xe8c4ac: tbnz            x12, #0x3f, #0xe8c6cc
    // 0xe8c4b0: lsl             w14, w13, w12
    // 0xe8c4b4: cmp             x12, #0x1f
    // 0xe8c4b8: csel            x14, x14, xzr, le
    // 0xe8c4bc: r13 = 63
    //     0xe8c4bc: movz            x13, #0x3f
    // 0xe8c4c0: and             x19, x7, x13
    // 0xe8c4c4: mov             x0, x4
    // 0xe8c4c8: r1 = 16
    //     0xe8c4c8: movz            x1, #0x10
    // 0xe8c4cc: cmp             x1, x0
    // 0xe8c4d0: b.hs            #0xe8c6fc
    // 0xe8c4d4: LoadField: r4 = r3->field_4f
    //     0xe8c4d4: ldur            w4, [x3, #0x4f]
    // 0xe8c4d8: DecompressPointer r4
    //     0xe8c4d8: add             x4, x4, HEAP, lsl #32
    // 0xe8c4dc: r3 = LoadInt32Instr(r4)
    //     0xe8c4dc: sbfx            x3, x4, #1, #0x1f
    //     0xe8c4e0: tbz             w4, #0, #0xe8c4e8
    //     0xe8c4e4: ldur            x3, [x4, #7]
    // 0xe8c4e8: and             x4, x19, x3
    // 0xe8c4ec: r3 = 16
    //     0xe8c4ec: movz            x3, #0x10
    // 0xe8c4f0: tbnz            x3, #0x3f, #0xe8c700
    // 0xe8c4f4: lsl             w7, w4, w3
    // 0xe8c4f8: cmp             x3, #0x1f
    // 0xe8c4fc: csel            x7, x7, xzr, le
    // 0xe8c500: ubfx            x14, x14, #0, #0x20
    // 0xe8c504: ubfx            x7, x7, #0, #0x20
    // 0xe8c508: orr             x4, x14, x7
    // 0xe8c50c: and             x7, x10, x11
    // 0xe8c510: ubfx            x7, x7, #0, #0x20
    // 0xe8c514: asr             x14, x7, #4
    // 0xe8c518: orr             x7, x4, x14
    // 0xe8c51c: and             x4, x10, x13
    // 0xe8c520: ubfx            x4, x4, #0, #0x20
    // 0xe8c524: orr             x10, x7, x4
    // 0xe8c528: r0 = BoxInt64Instr(r10)
    //     0xe8c528: sbfiz           x0, x10, #1, #0x1f
    //     0xe8c52c: cmp             x10, x0, asr #1
    //     0xe8c530: b.eq            #0xe8c53c
    //     0xe8c534: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8c538: stur            x10, [x0, #7]
    // 0xe8c53c: ldur            x1, [fp, #-0x18]
    // 0xe8c540: ldur            x4, [fp, #-0x48]
    // 0xe8c544: ArrayStore: r1[r4] = r0  ; List_4
    //     0xe8c544: add             x25, x1, x4, lsl #2
    //     0xe8c548: add             x25, x25, #0xf
    //     0xe8c54c: str             w0, [x25]
    //     0xe8c550: tbz             w0, #0, #0xe8c56c
    //     0xe8c554: ldurb           w16, [x1, #-1]
    //     0xe8c558: ldurb           w17, [x0, #-1]
    //     0xe8c55c: and             x16, x17, x16, lsr #2
    //     0xe8c560: tst             x16, HEAP, lsr #32
    //     0xe8c564: b.eq            #0xe8c56c
    //     0xe8c568: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe8c56c: add             x4, x9, #2
    // 0xe8c570: mov             x3, x5
    // 0xe8c574: b               #0xe8c2bc
    // 0xe8c578: ldur            x0, [fp, #-0x28]
    // 0xe8c57c: LeaveFrame
    //     0xe8c57c: mov             SP, fp
    //     0xe8c580: ldp             fp, lr, [SP], #0x10
    // 0xe8c584: ret
    //     0xe8c584: ret             
    // 0xe8c588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c588: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c58c: b               #0xe8b9dc
    // 0xe8c590: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c590: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c594: b               #0xe8ba14
    // 0xe8c598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c59c: b               #0xe8ba64
    // 0xe8c5a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c5a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c5a4: b               #0xe8bab8
    // 0xe8c5a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c5a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c5ac: b               #0xe8bb00
    // 0xe8c5b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c5b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c5b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c5b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c5b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c5b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c5bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c5bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c5c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c5c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c5c4: b               #0xe8bc6c
    // 0xe8c5c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c5c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c5cc: str             x1, [THR, #0x7a8]  ; THR::
    // 0xe8c5d0: stp             x1, x2, [SP, #-0x10]!
    // 0xe8c5d4: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe8c5d8: r4 = 0
    //     0xe8c5d8: movz            x4, #0
    // 0xe8c5dc: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe8c5e0: blr             lr
    // 0xe8c5e4: brk             #0
    // 0xe8c5e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c5e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c5ec: str             x3, [THR, #0x7a8]  ; THR::
    // 0xe8c5f0: stp             x2, x3, [SP, #-0x10]!
    // 0xe8c5f4: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe8c5f8: r4 = 0
    //     0xe8c5f8: movz            x4, #0
    // 0xe8c5fc: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe8c600: blr             lr
    // 0xe8c604: brk             #0
    // 0xe8c608: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c608: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c60c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c60c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c610: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c610: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c614: b               #0xe8bdf4
    // 0xe8c618: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c618: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c61c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c61c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c620: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c620: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c624: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c624: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c628: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c628: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c62c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c62c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c630: b               #0xe8bf10
    // 0xe8c634: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c634: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c638: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c638: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c63c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c63c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c640: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c640: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c644: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c644: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c648: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c648: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c64c: b               #0xe8c018
    // 0xe8c650: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c650: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c654: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c654: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c658: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c658: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c65c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c65c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c660: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c660: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c664: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c664: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c668: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8c668: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8c66c: b               #0xe8c2d0
    // 0xe8c670: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c670: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c674: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c674: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c678: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c678: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c67c: str             x2, [THR, #0x7a8]  ; THR::
    // 0xe8c680: stp             x4, x6, [SP, #-0x10]!
    // 0xe8c684: stp             x2, x3, [SP, #-0x10]!
    // 0xe8c688: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe8c68c: r4 = 0
    //     0xe8c68c: movz            x4, #0
    // 0xe8c690: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe8c694: blr             lr
    // 0xe8c698: brk             #0
    // 0xe8c69c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c69c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c6a0: str             x8, [THR, #0x7a8]  ; THR::
    // 0xe8c6a4: stp             x8, x9, [SP, #-0x10]!
    // 0xe8c6a8: stp             x6, x7, [SP, #-0x10]!
    // 0xe8c6ac: stp             x4, x5, [SP, #-0x10]!
    // 0xe8c6b0: stp             x2, x3, [SP, #-0x10]!
    // 0xe8c6b4: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe8c6b8: r4 = 0
    //     0xe8c6b8: movz            x4, #0
    // 0xe8c6bc: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe8c6c0: blr             lr
    // 0xe8c6c4: brk             #0
    // 0xe8c6c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c6c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c6cc: str             x12, [THR, #0x7a8]  ; THR::
    // 0xe8c6d0: stp             x12, x13, [SP, #-0x10]!
    // 0xe8c6d4: stp             x10, x11, [SP, #-0x10]!
    // 0xe8c6d8: stp             x8, x9, [SP, #-0x10]!
    // 0xe8c6dc: stp             x6, x7, [SP, #-0x10]!
    // 0xe8c6e0: stp             x4, x5, [SP, #-0x10]!
    // 0xe8c6e4: stp             x2, x3, [SP, #-0x10]!
    // 0xe8c6e8: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe8c6ec: r4 = 0
    //     0xe8c6ec: movz            x4, #0
    // 0xe8c6f0: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe8c6f4: blr             lr
    // 0xe8c6f8: brk             #0
    // 0xe8c6fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8c6fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8c700: str             x3, [THR, #0x7a8]  ; THR::
    // 0xe8c704: stp             x13, x14, [SP, #-0x10]!
    // 0xe8c708: stp             x11, x12, [SP, #-0x10]!
    // 0xe8c70c: stp             x9, x10, [SP, #-0x10]!
    // 0xe8c710: stp             x6, x8, [SP, #-0x10]!
    // 0xe8c714: stp             x4, x5, [SP, #-0x10]!
    // 0xe8c718: stp             x2, x3, [SP, #-0x10]!
    // 0xe8c71c: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe8c720: r4 = 0
    //     0xe8c720: movz            x4, #0
    // 0xe8c724: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe8c728: blr             lr
    // 0xe8c72c: brk             #0
  }
  static List<int> bigbyte() {
    // ** addr: 0xe8c730, size: 0x104
    // 0xe8c730: EnterFrame
    //     0xe8c730: stp             fp, lr, [SP, #-0x10]!
    //     0xe8c734: mov             fp, SP
    // 0xe8c738: AllocStack(0x8)
    //     0xe8c738: sub             SP, SP, #8
    // 0xe8c73c: r0 = 48
    //     0xe8c73c: movz            x0, #0x30
    // 0xe8c740: mov             x2, x0
    // 0xe8c744: r1 = <int>
    //     0xe8c744: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8c748: r0 = AllocateArray()
    //     0xe8c748: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8c74c: stur            x0, [fp, #-8]
    // 0xe8c750: r16 = 16777216
    //     0xe8c750: orr             x16, xzr, #0x1000000
    // 0xe8c754: StoreField: r0->field_f = r16
    //     0xe8c754: stur            w16, [x0, #0xf]
    // 0xe8c758: r16 = 128
    //     0xe8c758: movz            x16, #0x80, lsl #16
    // 0xe8c75c: StoreField: r0->field_13 = r16
    //     0xe8c75c: stur            w16, [x0, #0x13]
    // 0xe8c760: r16 = 64
    //     0xe8c760: movz            x16, #0x40, lsl #16
    // 0xe8c764: ArrayStore: r0[0] = r16  ; List_4
    //     0xe8c764: stur            w16, [x0, #0x17]
    // 0xe8c768: r16 = 32
    //     0xe8c768: movz            x16, #0x20, lsl #16
    // 0xe8c76c: StoreField: r0->field_1b = r16
    //     0xe8c76c: stur            w16, [x0, #0x1b]
    // 0xe8c770: r16 = 16
    //     0xe8c770: movz            x16, #0x10, lsl #16
    // 0xe8c774: StoreField: r0->field_1f = r16
    //     0xe8c774: stur            w16, [x0, #0x1f]
    // 0xe8c778: r16 = 8
    //     0xe8c778: movz            x16, #0x8, lsl #16
    // 0xe8c77c: StoreField: r0->field_23 = r16
    //     0xe8c77c: stur            w16, [x0, #0x23]
    // 0xe8c780: r16 = 4
    //     0xe8c780: movz            x16, #0x4, lsl #16
    // 0xe8c784: StoreField: r0->field_27 = r16
    //     0xe8c784: stur            w16, [x0, #0x27]
    // 0xe8c788: r16 = 2
    //     0xe8c788: movz            x16, #0x2, lsl #16
    // 0xe8c78c: StoreField: r0->field_2b = r16
    //     0xe8c78c: stur            w16, [x0, #0x2b]
    // 0xe8c790: r16 = 1
    //     0xe8c790: movz            x16, #0x1, lsl #16
    // 0xe8c794: StoreField: r0->field_2f = r16
    //     0xe8c794: stur            w16, [x0, #0x2f]
    // 0xe8c798: r16 = 32768
    //     0xe8c798: movz            x16, #0x8000
    // 0xe8c79c: StoreField: r0->field_33 = r16
    //     0xe8c79c: stur            w16, [x0, #0x33]
    // 0xe8c7a0: r16 = 16384
    //     0xe8c7a0: movz            x16, #0x4000
    // 0xe8c7a4: StoreField: r0->field_37 = r16
    //     0xe8c7a4: stur            w16, [x0, #0x37]
    // 0xe8c7a8: r16 = 8192
    //     0xe8c7a8: movz            x16, #0x2000
    // 0xe8c7ac: StoreField: r0->field_3b = r16
    //     0xe8c7ac: stur            w16, [x0, #0x3b]
    // 0xe8c7b0: r16 = 4096
    //     0xe8c7b0: movz            x16, #0x1000
    // 0xe8c7b4: StoreField: r0->field_3f = r16
    //     0xe8c7b4: stur            w16, [x0, #0x3f]
    // 0xe8c7b8: r16 = 2048
    //     0xe8c7b8: movz            x16, #0x800
    // 0xe8c7bc: StoreField: r0->field_43 = r16
    //     0xe8c7bc: stur            w16, [x0, #0x43]
    // 0xe8c7c0: r16 = 1024
    //     0xe8c7c0: movz            x16, #0x400
    // 0xe8c7c4: StoreField: r0->field_47 = r16
    //     0xe8c7c4: stur            w16, [x0, #0x47]
    // 0xe8c7c8: r16 = 512
    //     0xe8c7c8: movz            x16, #0x200
    // 0xe8c7cc: StoreField: r0->field_4b = r16
    //     0xe8c7cc: stur            w16, [x0, #0x4b]
    // 0xe8c7d0: r16 = 256
    //     0xe8c7d0: movz            x16, #0x100
    // 0xe8c7d4: StoreField: r0->field_4f = r16
    //     0xe8c7d4: stur            w16, [x0, #0x4f]
    // 0xe8c7d8: r16 = 128
    //     0xe8c7d8: movz            x16, #0x80
    // 0xe8c7dc: StoreField: r0->field_53 = r16
    //     0xe8c7dc: stur            w16, [x0, #0x53]
    // 0xe8c7e0: r16 = 64
    //     0xe8c7e0: movz            x16, #0x40
    // 0xe8c7e4: StoreField: r0->field_57 = r16
    //     0xe8c7e4: stur            w16, [x0, #0x57]
    // 0xe8c7e8: r16 = 32
    //     0xe8c7e8: movz            x16, #0x20
    // 0xe8c7ec: StoreField: r0->field_5b = r16
    //     0xe8c7ec: stur            w16, [x0, #0x5b]
    // 0xe8c7f0: r16 = 16
    //     0xe8c7f0: movz            x16, #0x10
    // 0xe8c7f4: StoreField: r0->field_5f = r16
    //     0xe8c7f4: stur            w16, [x0, #0x5f]
    // 0xe8c7f8: r16 = 8
    //     0xe8c7f8: movz            x16, #0x8
    // 0xe8c7fc: StoreField: r0->field_63 = r16
    //     0xe8c7fc: stur            w16, [x0, #0x63]
    // 0xe8c800: r16 = 4
    //     0xe8c800: movz            x16, #0x4
    // 0xe8c804: StoreField: r0->field_67 = r16
    //     0xe8c804: stur            w16, [x0, #0x67]
    // 0xe8c808: r16 = 2
    //     0xe8c808: movz            x16, #0x2
    // 0xe8c80c: StoreField: r0->field_6b = r16
    //     0xe8c80c: stur            w16, [x0, #0x6b]
    // 0xe8c810: r1 = <int>
    //     0xe8c810: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8c814: r0 = AllocateGrowableArray()
    //     0xe8c814: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe8c818: ldur            x1, [fp, #-8]
    // 0xe8c81c: StoreField: r0->field_f = r1
    //     0xe8c81c: stur            w1, [x0, #0xf]
    // 0xe8c820: r1 = 48
    //     0xe8c820: movz            x1, #0x30
    // 0xe8c824: StoreField: r0->field_b = r1
    //     0xe8c824: stur            w1, [x0, #0xb]
    // 0xe8c828: LeaveFrame
    //     0xe8c828: mov             SP, fp
    //     0xe8c82c: ldp             fp, lr, [SP], #0x10
    // 0xe8c830: ret
    //     0xe8c830: ret             
  }
  static Uint8List pc2() {
    // ** addr: 0xe8c834, size: 0x200
    // 0xe8c834: EnterFrame
    //     0xe8c834: stp             fp, lr, [SP, #-0x10]!
    //     0xe8c838: mov             fp, SP
    // 0xe8c83c: AllocStack(0x10)
    //     0xe8c83c: sub             SP, SP, #0x10
    // 0xe8c840: r0 = 96
    //     0xe8c840: movz            x0, #0x60
    // 0xe8c844: CheckStackOverflow
    //     0xe8c844: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8c848: cmp             SP, x16
    //     0xe8c84c: b.ls            #0xe8ca2c
    // 0xe8c850: mov             x2, x0
    // 0xe8c854: r1 = <int>
    //     0xe8c854: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8c858: r0 = AllocateArray()
    //     0xe8c858: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8c85c: stur            x0, [fp, #-8]
    // 0xe8c860: r16 = 26
    //     0xe8c860: movz            x16, #0x1a
    // 0xe8c864: StoreField: r0->field_f = r16
    //     0xe8c864: stur            w16, [x0, #0xf]
    // 0xe8c868: r16 = 32
    //     0xe8c868: movz            x16, #0x20
    // 0xe8c86c: StoreField: r0->field_13 = r16
    //     0xe8c86c: stur            w16, [x0, #0x13]
    // 0xe8c870: r16 = 20
    //     0xe8c870: movz            x16, #0x14
    // 0xe8c874: ArrayStore: r0[0] = r16  ; List_4
    //     0xe8c874: stur            w16, [x0, #0x17]
    // 0xe8c878: r16 = 46
    //     0xe8c878: movz            x16, #0x2e
    // 0xe8c87c: StoreField: r0->field_1b = r16
    //     0xe8c87c: stur            w16, [x0, #0x1b]
    // 0xe8c880: StoreField: r0->field_1f = rZR
    //     0xe8c880: stur            wzr, [x0, #0x1f]
    // 0xe8c884: r16 = 8
    //     0xe8c884: movz            x16, #0x8
    // 0xe8c888: StoreField: r0->field_23 = r16
    //     0xe8c888: stur            w16, [x0, #0x23]
    // 0xe8c88c: r16 = 4
    //     0xe8c88c: movz            x16, #0x4
    // 0xe8c890: StoreField: r0->field_27 = r16
    //     0xe8c890: stur            w16, [x0, #0x27]
    // 0xe8c894: r16 = 54
    //     0xe8c894: movz            x16, #0x36
    // 0xe8c898: StoreField: r0->field_2b = r16
    //     0xe8c898: stur            w16, [x0, #0x2b]
    // 0xe8c89c: r16 = 28
    //     0xe8c89c: movz            x16, #0x1c
    // 0xe8c8a0: StoreField: r0->field_2f = r16
    //     0xe8c8a0: stur            w16, [x0, #0x2f]
    // 0xe8c8a4: r16 = 10
    //     0xe8c8a4: movz            x16, #0xa
    // 0xe8c8a8: StoreField: r0->field_33 = r16
    //     0xe8c8a8: stur            w16, [x0, #0x33]
    // 0xe8c8ac: r16 = 40
    //     0xe8c8ac: movz            x16, #0x28
    // 0xe8c8b0: StoreField: r0->field_37 = r16
    //     0xe8c8b0: stur            w16, [x0, #0x37]
    // 0xe8c8b4: r16 = 18
    //     0xe8c8b4: movz            x16, #0x12
    // 0xe8c8b8: StoreField: r0->field_3b = r16
    //     0xe8c8b8: stur            w16, [x0, #0x3b]
    // 0xe8c8bc: r16 = 44
    //     0xe8c8bc: movz            x16, #0x2c
    // 0xe8c8c0: StoreField: r0->field_3f = r16
    //     0xe8c8c0: stur            w16, [x0, #0x3f]
    // 0xe8c8c4: r16 = 36
    //     0xe8c8c4: movz            x16, #0x24
    // 0xe8c8c8: StoreField: r0->field_43 = r16
    //     0xe8c8c8: stur            w16, [x0, #0x43]
    // 0xe8c8cc: r16 = 22
    //     0xe8c8cc: movz            x16, #0x16
    // 0xe8c8d0: StoreField: r0->field_47 = r16
    //     0xe8c8d0: stur            w16, [x0, #0x47]
    // 0xe8c8d4: r16 = 6
    //     0xe8c8d4: movz            x16, #0x6
    // 0xe8c8d8: StoreField: r0->field_4b = r16
    //     0xe8c8d8: stur            w16, [x0, #0x4b]
    // 0xe8c8dc: r16 = 50
    //     0xe8c8dc: movz            x16, #0x32
    // 0xe8c8e0: StoreField: r0->field_4f = r16
    //     0xe8c8e0: stur            w16, [x0, #0x4f]
    // 0xe8c8e4: r16 = 14
    //     0xe8c8e4: movz            x16, #0xe
    // 0xe8c8e8: StoreField: r0->field_53 = r16
    //     0xe8c8e8: stur            w16, [x0, #0x53]
    // 0xe8c8ec: r16 = 30
    //     0xe8c8ec: movz            x16, #0x1e
    // 0xe8c8f0: StoreField: r0->field_57 = r16
    //     0xe8c8f0: stur            w16, [x0, #0x57]
    // 0xe8c8f4: r16 = 12
    //     0xe8c8f4: movz            x16, #0xc
    // 0xe8c8f8: StoreField: r0->field_5b = r16
    //     0xe8c8f8: stur            w16, [x0, #0x5b]
    // 0xe8c8fc: r16 = 52
    //     0xe8c8fc: movz            x16, #0x34
    // 0xe8c900: StoreField: r0->field_5f = r16
    //     0xe8c900: stur            w16, [x0, #0x5f]
    // 0xe8c904: r16 = 38
    //     0xe8c904: movz            x16, #0x26
    // 0xe8c908: StoreField: r0->field_63 = r16
    //     0xe8c908: stur            w16, [x0, #0x63]
    // 0xe8c90c: r16 = 24
    //     0xe8c90c: movz            x16, #0x18
    // 0xe8c910: StoreField: r0->field_67 = r16
    //     0xe8c910: stur            w16, [x0, #0x67]
    // 0xe8c914: r16 = 2
    //     0xe8c914: movz            x16, #0x2
    // 0xe8c918: StoreField: r0->field_6b = r16
    //     0xe8c918: stur            w16, [x0, #0x6b]
    // 0xe8c91c: r16 = 80
    //     0xe8c91c: movz            x16, #0x50
    // 0xe8c920: StoreField: r0->field_6f = r16
    //     0xe8c920: stur            w16, [x0, #0x6f]
    // 0xe8c924: r16 = 102
    //     0xe8c924: movz            x16, #0x66
    // 0xe8c928: StoreField: r0->field_73 = r16
    //     0xe8c928: stur            w16, [x0, #0x73]
    // 0xe8c92c: r16 = 60
    //     0xe8c92c: movz            x16, #0x3c
    // 0xe8c930: StoreField: r0->field_77 = r16
    //     0xe8c930: stur            w16, [x0, #0x77]
    // 0xe8c934: r16 = 72
    //     0xe8c934: movz            x16, #0x48
    // 0xe8c938: StoreField: r0->field_7b = r16
    //     0xe8c938: stur            w16, [x0, #0x7b]
    // 0xe8c93c: r16 = 92
    //     0xe8c93c: movz            x16, #0x5c
    // 0xe8c940: StoreField: r0->field_7f = r16
    //     0xe8c940: stur            w16, [x0, #0x7f]
    // 0xe8c944: r16 = 108
    //     0xe8c944: movz            x16, #0x6c
    // 0xe8c948: StoreField: r0->field_83 = r16
    //     0xe8c948: stur            w16, [x0, #0x83]
    // 0xe8c94c: r16 = 58
    //     0xe8c94c: movz            x16, #0x3a
    // 0xe8c950: StoreField: r0->field_87 = r16
    //     0xe8c950: stur            w16, [x0, #0x87]
    // 0xe8c954: r16 = 78
    //     0xe8c954: movz            x16, #0x4e
    // 0xe8c958: StoreField: r0->field_8b = r16
    //     0xe8c958: stur            w16, [x0, #0x8b]
    // 0xe8c95c: r16 = 100
    //     0xe8c95c: movz            x16, #0x64
    // 0xe8c960: StoreField: r0->field_8f = r16
    //     0xe8c960: stur            w16, [x0, #0x8f]
    // 0xe8c964: r16 = 88
    //     0xe8c964: movz            x16, #0x58
    // 0xe8c968: StoreField: r0->field_93 = r16
    //     0xe8c968: stur            w16, [x0, #0x93]
    // 0xe8c96c: r16 = 64
    //     0xe8c96c: movz            x16, #0x40
    // 0xe8c970: StoreField: r0->field_97 = r16
    //     0xe8c970: stur            w16, [x0, #0x97]
    // 0xe8c974: r16 = 94
    //     0xe8c974: movz            x16, #0x5e
    // 0xe8c978: StoreField: r0->field_9b = r16
    //     0xe8c978: stur            w16, [x0, #0x9b]
    // 0xe8c97c: r16 = 86
    //     0xe8c97c: movz            x16, #0x56
    // 0xe8c980: StoreField: r0->field_9f = r16
    //     0xe8c980: stur            w16, [x0, #0x9f]
    // 0xe8c984: r16 = 96
    //     0xe8c984: movz            x16, #0x60
    // 0xe8c988: StoreField: r0->field_a3 = r16
    //     0xe8c988: stur            w16, [x0, #0xa3]
    // 0xe8c98c: r16 = 76
    //     0xe8c98c: movz            x16, #0x4c
    // 0xe8c990: StoreField: r0->field_a7 = r16
    //     0xe8c990: stur            w16, [x0, #0xa7]
    // 0xe8c994: r16 = 110
    //     0xe8c994: movz            x16, #0x6e
    // 0xe8c998: StoreField: r0->field_ab = r16
    //     0xe8c998: stur            w16, [x0, #0xab]
    // 0xe8c99c: r16 = 66
    //     0xe8c99c: movz            x16, #0x42
    // 0xe8c9a0: StoreField: r0->field_af = r16
    //     0xe8c9a0: stur            w16, [x0, #0xaf]
    // 0xe8c9a4: r16 = 104
    //     0xe8c9a4: movz            x16, #0x68
    // 0xe8c9a8: StoreField: r0->field_b3 = r16
    //     0xe8c9a8: stur            w16, [x0, #0xb3]
    // 0xe8c9ac: r16 = 90
    //     0xe8c9ac: movz            x16, #0x5a
    // 0xe8c9b0: StoreField: r0->field_b7 = r16
    //     0xe8c9b0: stur            w16, [x0, #0xb7]
    // 0xe8c9b4: r16 = 82
    //     0xe8c9b4: movz            x16, #0x52
    // 0xe8c9b8: StoreField: r0->field_bb = r16
    //     0xe8c9b8: stur            w16, [x0, #0xbb]
    // 0xe8c9bc: r16 = 98
    //     0xe8c9bc: movz            x16, #0x62
    // 0xe8c9c0: StoreField: r0->field_bf = r16
    //     0xe8c9c0: stur            w16, [x0, #0xbf]
    // 0xe8c9c4: r16 = 70
    //     0xe8c9c4: movz            x16, #0x46
    // 0xe8c9c8: StoreField: r0->field_c3 = r16
    //     0xe8c9c8: stur            w16, [x0, #0xc3]
    // 0xe8c9cc: r16 = 56
    //     0xe8c9cc: movz            x16, #0x38
    // 0xe8c9d0: StoreField: r0->field_c7 = r16
    //     0xe8c9d0: stur            w16, [x0, #0xc7]
    // 0xe8c9d4: r16 = 62
    //     0xe8c9d4: movz            x16, #0x3e
    // 0xe8c9d8: StoreField: r0->field_cb = r16
    //     0xe8c9d8: stur            w16, [x0, #0xcb]
    // 0xe8c9dc: r1 = <int>
    //     0xe8c9dc: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8c9e0: r0 = AllocateGrowableArray()
    //     0xe8c9e0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe8c9e4: mov             x1, x0
    // 0xe8c9e8: ldur            x0, [fp, #-8]
    // 0xe8c9ec: stur            x1, [fp, #-0x10]
    // 0xe8c9f0: StoreField: r1->field_f = r0
    //     0xe8c9f0: stur            w0, [x1, #0xf]
    // 0xe8c9f4: r4 = 96
    //     0xe8c9f4: movz            x4, #0x60
    // 0xe8c9f8: StoreField: r1->field_b = r4
    //     0xe8c9f8: stur            w4, [x1, #0xb]
    // 0xe8c9fc: r0 = AllocateUint8Array()
    //     0xe8c9fc: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8ca00: mov             x1, x0
    // 0xe8ca04: ldur            x5, [fp, #-0x10]
    // 0xe8ca08: r2 = 0
    //     0xe8ca08: movz            x2, #0
    // 0xe8ca0c: r3 = 48
    //     0xe8ca0c: movz            x3, #0x30
    // 0xe8ca10: r6 = 0
    //     0xe8ca10: movz            x6, #0
    // 0xe8ca14: stur            x0, [fp, #-8]
    // 0xe8ca18: r0 = _slowSetRange()
    //     0xe8ca18: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xe8ca1c: ldur            x0, [fp, #-8]
    // 0xe8ca20: LeaveFrame
    //     0xe8ca20: mov             SP, fp
    //     0xe8ca24: ldp             fp, lr, [SP], #0x10
    // 0xe8ca28: ret
    //     0xe8ca28: ret             
    // 0xe8ca2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8ca2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8ca30: b               #0xe8c850
  }
  static Uint8List totrot() {
    // ** addr: 0xe8ca34, size: 0x104
    // 0xe8ca34: EnterFrame
    //     0xe8ca34: stp             fp, lr, [SP, #-0x10]!
    //     0xe8ca38: mov             fp, SP
    // 0xe8ca3c: AllocStack(0x10)
    //     0xe8ca3c: sub             SP, SP, #0x10
    // 0xe8ca40: r0 = 32
    //     0xe8ca40: movz            x0, #0x20
    // 0xe8ca44: CheckStackOverflow
    //     0xe8ca44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ca48: cmp             SP, x16
    //     0xe8ca4c: b.ls            #0xe8cb30
    // 0xe8ca50: mov             x2, x0
    // 0xe8ca54: r1 = <int>
    //     0xe8ca54: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8ca58: r0 = AllocateArray()
    //     0xe8ca58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8ca5c: stur            x0, [fp, #-8]
    // 0xe8ca60: r16 = 2
    //     0xe8ca60: movz            x16, #0x2
    // 0xe8ca64: StoreField: r0->field_f = r16
    //     0xe8ca64: stur            w16, [x0, #0xf]
    // 0xe8ca68: r16 = 4
    //     0xe8ca68: movz            x16, #0x4
    // 0xe8ca6c: StoreField: r0->field_13 = r16
    //     0xe8ca6c: stur            w16, [x0, #0x13]
    // 0xe8ca70: r16 = 8
    //     0xe8ca70: movz            x16, #0x8
    // 0xe8ca74: ArrayStore: r0[0] = r16  ; List_4
    //     0xe8ca74: stur            w16, [x0, #0x17]
    // 0xe8ca78: r16 = 12
    //     0xe8ca78: movz            x16, #0xc
    // 0xe8ca7c: StoreField: r0->field_1b = r16
    //     0xe8ca7c: stur            w16, [x0, #0x1b]
    // 0xe8ca80: r16 = 16
    //     0xe8ca80: movz            x16, #0x10
    // 0xe8ca84: StoreField: r0->field_1f = r16
    //     0xe8ca84: stur            w16, [x0, #0x1f]
    // 0xe8ca88: r16 = 20
    //     0xe8ca88: movz            x16, #0x14
    // 0xe8ca8c: StoreField: r0->field_23 = r16
    //     0xe8ca8c: stur            w16, [x0, #0x23]
    // 0xe8ca90: r16 = 24
    //     0xe8ca90: movz            x16, #0x18
    // 0xe8ca94: StoreField: r0->field_27 = r16
    //     0xe8ca94: stur            w16, [x0, #0x27]
    // 0xe8ca98: r16 = 28
    //     0xe8ca98: movz            x16, #0x1c
    // 0xe8ca9c: StoreField: r0->field_2b = r16
    //     0xe8ca9c: stur            w16, [x0, #0x2b]
    // 0xe8caa0: r16 = 30
    //     0xe8caa0: movz            x16, #0x1e
    // 0xe8caa4: StoreField: r0->field_2f = r16
    //     0xe8caa4: stur            w16, [x0, #0x2f]
    // 0xe8caa8: r16 = 34
    //     0xe8caa8: movz            x16, #0x22
    // 0xe8caac: StoreField: r0->field_33 = r16
    //     0xe8caac: stur            w16, [x0, #0x33]
    // 0xe8cab0: r16 = 38
    //     0xe8cab0: movz            x16, #0x26
    // 0xe8cab4: StoreField: r0->field_37 = r16
    //     0xe8cab4: stur            w16, [x0, #0x37]
    // 0xe8cab8: r16 = 42
    //     0xe8cab8: movz            x16, #0x2a
    // 0xe8cabc: StoreField: r0->field_3b = r16
    //     0xe8cabc: stur            w16, [x0, #0x3b]
    // 0xe8cac0: r16 = 46
    //     0xe8cac0: movz            x16, #0x2e
    // 0xe8cac4: StoreField: r0->field_3f = r16
    //     0xe8cac4: stur            w16, [x0, #0x3f]
    // 0xe8cac8: r16 = 50
    //     0xe8cac8: movz            x16, #0x32
    // 0xe8cacc: StoreField: r0->field_43 = r16
    //     0xe8cacc: stur            w16, [x0, #0x43]
    // 0xe8cad0: r16 = 54
    //     0xe8cad0: movz            x16, #0x36
    // 0xe8cad4: StoreField: r0->field_47 = r16
    //     0xe8cad4: stur            w16, [x0, #0x47]
    // 0xe8cad8: r16 = 56
    //     0xe8cad8: movz            x16, #0x38
    // 0xe8cadc: StoreField: r0->field_4b = r16
    //     0xe8cadc: stur            w16, [x0, #0x4b]
    // 0xe8cae0: r1 = <int>
    //     0xe8cae0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8cae4: r0 = AllocateGrowableArray()
    //     0xe8cae4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe8cae8: mov             x1, x0
    // 0xe8caec: ldur            x0, [fp, #-8]
    // 0xe8caf0: stur            x1, [fp, #-0x10]
    // 0xe8caf4: StoreField: r1->field_f = r0
    //     0xe8caf4: stur            w0, [x1, #0xf]
    // 0xe8caf8: r4 = 32
    //     0xe8caf8: movz            x4, #0x20
    // 0xe8cafc: StoreField: r1->field_b = r4
    //     0xe8cafc: stur            w4, [x1, #0xb]
    // 0xe8cb00: r0 = AllocateUint8Array()
    //     0xe8cb00: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8cb04: mov             x1, x0
    // 0xe8cb08: ldur            x5, [fp, #-0x10]
    // 0xe8cb0c: r2 = 0
    //     0xe8cb0c: movz            x2, #0
    // 0xe8cb10: r3 = 16
    //     0xe8cb10: movz            x3, #0x10
    // 0xe8cb14: r6 = 0
    //     0xe8cb14: movz            x6, #0
    // 0xe8cb18: stur            x0, [fp, #-8]
    // 0xe8cb1c: r0 = _slowSetRange()
    //     0xe8cb1c: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xe8cb20: ldur            x0, [fp, #-8]
    // 0xe8cb24: LeaveFrame
    //     0xe8cb24: mov             SP, fp
    //     0xe8cb28: ldp             fp, lr, [SP], #0x10
    // 0xe8cb2c: ret
    //     0xe8cb2c: ret             
    // 0xe8cb30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8cb30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8cb34: b               #0xe8ca50
  }
  static List<int> bytebit() {
    // ** addr: 0xe8cb38, size: 0x84
    // 0xe8cb38: EnterFrame
    //     0xe8cb38: stp             fp, lr, [SP, #-0x10]!
    //     0xe8cb3c: mov             fp, SP
    // 0xe8cb40: AllocStack(0x8)
    //     0xe8cb40: sub             SP, SP, #8
    // 0xe8cb44: r0 = 16
    //     0xe8cb44: movz            x0, #0x10
    // 0xe8cb48: mov             x2, x0
    // 0xe8cb4c: r1 = Null
    //     0xe8cb4c: mov             x1, NULL
    // 0xe8cb50: r0 = AllocateArray()
    //     0xe8cb50: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8cb54: stur            x0, [fp, #-8]
    // 0xe8cb58: r16 = 256
    //     0xe8cb58: movz            x16, #0x100
    // 0xe8cb5c: StoreField: r0->field_f = r16
    //     0xe8cb5c: stur            w16, [x0, #0xf]
    // 0xe8cb60: r16 = 128
    //     0xe8cb60: movz            x16, #0x80
    // 0xe8cb64: StoreField: r0->field_13 = r16
    //     0xe8cb64: stur            w16, [x0, #0x13]
    // 0xe8cb68: r16 = 64
    //     0xe8cb68: movz            x16, #0x40
    // 0xe8cb6c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe8cb6c: stur            w16, [x0, #0x17]
    // 0xe8cb70: r16 = 32
    //     0xe8cb70: movz            x16, #0x20
    // 0xe8cb74: StoreField: r0->field_1b = r16
    //     0xe8cb74: stur            w16, [x0, #0x1b]
    // 0xe8cb78: r16 = 16
    //     0xe8cb78: movz            x16, #0x10
    // 0xe8cb7c: StoreField: r0->field_1f = r16
    //     0xe8cb7c: stur            w16, [x0, #0x1f]
    // 0xe8cb80: r16 = 8
    //     0xe8cb80: movz            x16, #0x8
    // 0xe8cb84: StoreField: r0->field_23 = r16
    //     0xe8cb84: stur            w16, [x0, #0x23]
    // 0xe8cb88: r16 = 4
    //     0xe8cb88: movz            x16, #0x4
    // 0xe8cb8c: StoreField: r0->field_27 = r16
    //     0xe8cb8c: stur            w16, [x0, #0x27]
    // 0xe8cb90: r16 = 2
    //     0xe8cb90: movz            x16, #0x2
    // 0xe8cb94: StoreField: r0->field_2b = r16
    //     0xe8cb94: stur            w16, [x0, #0x2b]
    // 0xe8cb98: r1 = <int>
    //     0xe8cb98: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8cb9c: r0 = AllocateGrowableArray()
    //     0xe8cb9c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe8cba0: ldur            x1, [fp, #-8]
    // 0xe8cba4: StoreField: r0->field_f = r1
    //     0xe8cba4: stur            w1, [x0, #0xf]
    // 0xe8cba8: r1 = 16
    //     0xe8cba8: movz            x1, #0x10
    // 0xe8cbac: StoreField: r0->field_b = r1
    //     0xe8cbac: stur            w1, [x0, #0xb]
    // 0xe8cbb0: LeaveFrame
    //     0xe8cbb0: mov             SP, fp
    //     0xe8cbb4: ldp             fp, lr, [SP], #0x10
    // 0xe8cbb8: ret
    //     0xe8cbb8: ret             
  }
  static Uint8List pc1() {
    // ** addr: 0xe8cbbc, size: 0x240
    // 0xe8cbbc: EnterFrame
    //     0xe8cbbc: stp             fp, lr, [SP, #-0x10]!
    //     0xe8cbc0: mov             fp, SP
    // 0xe8cbc4: AllocStack(0x10)
    //     0xe8cbc4: sub             SP, SP, #0x10
    // 0xe8cbc8: r0 = 112
    //     0xe8cbc8: movz            x0, #0x70
    // 0xe8cbcc: CheckStackOverflow
    //     0xe8cbcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8cbd0: cmp             SP, x16
    //     0xe8cbd4: b.ls            #0xe8cdf4
    // 0xe8cbd8: mov             x2, x0
    // 0xe8cbdc: r1 = <int>
    //     0xe8cbdc: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8cbe0: r0 = AllocateArray()
    //     0xe8cbe0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8cbe4: stur            x0, [fp, #-8]
    // 0xe8cbe8: r16 = 112
    //     0xe8cbe8: movz            x16, #0x70
    // 0xe8cbec: StoreField: r0->field_f = r16
    //     0xe8cbec: stur            w16, [x0, #0xf]
    // 0xe8cbf0: r16 = 96
    //     0xe8cbf0: movz            x16, #0x60
    // 0xe8cbf4: StoreField: r0->field_13 = r16
    //     0xe8cbf4: stur            w16, [x0, #0x13]
    // 0xe8cbf8: r16 = 80
    //     0xe8cbf8: movz            x16, #0x50
    // 0xe8cbfc: ArrayStore: r0[0] = r16  ; List_4
    //     0xe8cbfc: stur            w16, [x0, #0x17]
    // 0xe8cc00: r16 = 64
    //     0xe8cc00: movz            x16, #0x40
    // 0xe8cc04: StoreField: r0->field_1b = r16
    //     0xe8cc04: stur            w16, [x0, #0x1b]
    // 0xe8cc08: r16 = 48
    //     0xe8cc08: movz            x16, #0x30
    // 0xe8cc0c: StoreField: r0->field_1f = r16
    //     0xe8cc0c: stur            w16, [x0, #0x1f]
    // 0xe8cc10: r16 = 32
    //     0xe8cc10: movz            x16, #0x20
    // 0xe8cc14: StoreField: r0->field_23 = r16
    //     0xe8cc14: stur            w16, [x0, #0x23]
    // 0xe8cc18: r16 = 16
    //     0xe8cc18: movz            x16, #0x10
    // 0xe8cc1c: StoreField: r0->field_27 = r16
    //     0xe8cc1c: stur            w16, [x0, #0x27]
    // 0xe8cc20: StoreField: r0->field_2b = rZR
    //     0xe8cc20: stur            wzr, [x0, #0x2b]
    // 0xe8cc24: r16 = 114
    //     0xe8cc24: movz            x16, #0x72
    // 0xe8cc28: StoreField: r0->field_2f = r16
    //     0xe8cc28: stur            w16, [x0, #0x2f]
    // 0xe8cc2c: r16 = 98
    //     0xe8cc2c: movz            x16, #0x62
    // 0xe8cc30: StoreField: r0->field_33 = r16
    //     0xe8cc30: stur            w16, [x0, #0x33]
    // 0xe8cc34: r16 = 82
    //     0xe8cc34: movz            x16, #0x52
    // 0xe8cc38: StoreField: r0->field_37 = r16
    //     0xe8cc38: stur            w16, [x0, #0x37]
    // 0xe8cc3c: r16 = 66
    //     0xe8cc3c: movz            x16, #0x42
    // 0xe8cc40: StoreField: r0->field_3b = r16
    //     0xe8cc40: stur            w16, [x0, #0x3b]
    // 0xe8cc44: r16 = 50
    //     0xe8cc44: movz            x16, #0x32
    // 0xe8cc48: StoreField: r0->field_3f = r16
    //     0xe8cc48: stur            w16, [x0, #0x3f]
    // 0xe8cc4c: r16 = 34
    //     0xe8cc4c: movz            x16, #0x22
    // 0xe8cc50: StoreField: r0->field_43 = r16
    //     0xe8cc50: stur            w16, [x0, #0x43]
    // 0xe8cc54: r16 = 18
    //     0xe8cc54: movz            x16, #0x12
    // 0xe8cc58: StoreField: r0->field_47 = r16
    //     0xe8cc58: stur            w16, [x0, #0x47]
    // 0xe8cc5c: r16 = 2
    //     0xe8cc5c: movz            x16, #0x2
    // 0xe8cc60: StoreField: r0->field_4b = r16
    //     0xe8cc60: stur            w16, [x0, #0x4b]
    // 0xe8cc64: r16 = 116
    //     0xe8cc64: movz            x16, #0x74
    // 0xe8cc68: StoreField: r0->field_4f = r16
    //     0xe8cc68: stur            w16, [x0, #0x4f]
    // 0xe8cc6c: r16 = 100
    //     0xe8cc6c: movz            x16, #0x64
    // 0xe8cc70: StoreField: r0->field_53 = r16
    //     0xe8cc70: stur            w16, [x0, #0x53]
    // 0xe8cc74: r16 = 84
    //     0xe8cc74: movz            x16, #0x54
    // 0xe8cc78: StoreField: r0->field_57 = r16
    //     0xe8cc78: stur            w16, [x0, #0x57]
    // 0xe8cc7c: r16 = 68
    //     0xe8cc7c: movz            x16, #0x44
    // 0xe8cc80: StoreField: r0->field_5b = r16
    //     0xe8cc80: stur            w16, [x0, #0x5b]
    // 0xe8cc84: r16 = 52
    //     0xe8cc84: movz            x16, #0x34
    // 0xe8cc88: StoreField: r0->field_5f = r16
    //     0xe8cc88: stur            w16, [x0, #0x5f]
    // 0xe8cc8c: r16 = 36
    //     0xe8cc8c: movz            x16, #0x24
    // 0xe8cc90: StoreField: r0->field_63 = r16
    //     0xe8cc90: stur            w16, [x0, #0x63]
    // 0xe8cc94: r16 = 20
    //     0xe8cc94: movz            x16, #0x14
    // 0xe8cc98: StoreField: r0->field_67 = r16
    //     0xe8cc98: stur            w16, [x0, #0x67]
    // 0xe8cc9c: r16 = 4
    //     0xe8cc9c: movz            x16, #0x4
    // 0xe8cca0: StoreField: r0->field_6b = r16
    //     0xe8cca0: stur            w16, [x0, #0x6b]
    // 0xe8cca4: r16 = 118
    //     0xe8cca4: movz            x16, #0x76
    // 0xe8cca8: StoreField: r0->field_6f = r16
    //     0xe8cca8: stur            w16, [x0, #0x6f]
    // 0xe8ccac: r16 = 102
    //     0xe8ccac: movz            x16, #0x66
    // 0xe8ccb0: StoreField: r0->field_73 = r16
    //     0xe8ccb0: stur            w16, [x0, #0x73]
    // 0xe8ccb4: r16 = 86
    //     0xe8ccb4: movz            x16, #0x56
    // 0xe8ccb8: StoreField: r0->field_77 = r16
    //     0xe8ccb8: stur            w16, [x0, #0x77]
    // 0xe8ccbc: r16 = 70
    //     0xe8ccbc: movz            x16, #0x46
    // 0xe8ccc0: StoreField: r0->field_7b = r16
    //     0xe8ccc0: stur            w16, [x0, #0x7b]
    // 0xe8ccc4: r16 = 124
    //     0xe8ccc4: movz            x16, #0x7c
    // 0xe8ccc8: StoreField: r0->field_7f = r16
    //     0xe8ccc8: stur            w16, [x0, #0x7f]
    // 0xe8cccc: r16 = 108
    //     0xe8cccc: movz            x16, #0x6c
    // 0xe8ccd0: StoreField: r0->field_83 = r16
    //     0xe8ccd0: stur            w16, [x0, #0x83]
    // 0xe8ccd4: r16 = 92
    //     0xe8ccd4: movz            x16, #0x5c
    // 0xe8ccd8: StoreField: r0->field_87 = r16
    //     0xe8ccd8: stur            w16, [x0, #0x87]
    // 0xe8ccdc: r16 = 76
    //     0xe8ccdc: movz            x16, #0x4c
    // 0xe8cce0: StoreField: r0->field_8b = r16
    //     0xe8cce0: stur            w16, [x0, #0x8b]
    // 0xe8cce4: r16 = 60
    //     0xe8cce4: movz            x16, #0x3c
    // 0xe8cce8: StoreField: r0->field_8f = r16
    //     0xe8cce8: stur            w16, [x0, #0x8f]
    // 0xe8ccec: r16 = 44
    //     0xe8ccec: movz            x16, #0x2c
    // 0xe8ccf0: StoreField: r0->field_93 = r16
    //     0xe8ccf0: stur            w16, [x0, #0x93]
    // 0xe8ccf4: r16 = 28
    //     0xe8ccf4: movz            x16, #0x1c
    // 0xe8ccf8: StoreField: r0->field_97 = r16
    //     0xe8ccf8: stur            w16, [x0, #0x97]
    // 0xe8ccfc: r16 = 12
    //     0xe8ccfc: movz            x16, #0xc
    // 0xe8cd00: StoreField: r0->field_9b = r16
    //     0xe8cd00: stur            w16, [x0, #0x9b]
    // 0xe8cd04: r16 = 122
    //     0xe8cd04: movz            x16, #0x7a
    // 0xe8cd08: StoreField: r0->field_9f = r16
    //     0xe8cd08: stur            w16, [x0, #0x9f]
    // 0xe8cd0c: r16 = 106
    //     0xe8cd0c: movz            x16, #0x6a
    // 0xe8cd10: StoreField: r0->field_a3 = r16
    //     0xe8cd10: stur            w16, [x0, #0xa3]
    // 0xe8cd14: r16 = 90
    //     0xe8cd14: movz            x16, #0x5a
    // 0xe8cd18: StoreField: r0->field_a7 = r16
    //     0xe8cd18: stur            w16, [x0, #0xa7]
    // 0xe8cd1c: r16 = 74
    //     0xe8cd1c: movz            x16, #0x4a
    // 0xe8cd20: StoreField: r0->field_ab = r16
    //     0xe8cd20: stur            w16, [x0, #0xab]
    // 0xe8cd24: r16 = 58
    //     0xe8cd24: movz            x16, #0x3a
    // 0xe8cd28: StoreField: r0->field_af = r16
    //     0xe8cd28: stur            w16, [x0, #0xaf]
    // 0xe8cd2c: r16 = 42
    //     0xe8cd2c: movz            x16, #0x2a
    // 0xe8cd30: StoreField: r0->field_b3 = r16
    //     0xe8cd30: stur            w16, [x0, #0xb3]
    // 0xe8cd34: r16 = 26
    //     0xe8cd34: movz            x16, #0x1a
    // 0xe8cd38: StoreField: r0->field_b7 = r16
    //     0xe8cd38: stur            w16, [x0, #0xb7]
    // 0xe8cd3c: r16 = 10
    //     0xe8cd3c: movz            x16, #0xa
    // 0xe8cd40: StoreField: r0->field_bb = r16
    //     0xe8cd40: stur            w16, [x0, #0xbb]
    // 0xe8cd44: r16 = 120
    //     0xe8cd44: movz            x16, #0x78
    // 0xe8cd48: StoreField: r0->field_bf = r16
    //     0xe8cd48: stur            w16, [x0, #0xbf]
    // 0xe8cd4c: r16 = 104
    //     0xe8cd4c: movz            x16, #0x68
    // 0xe8cd50: StoreField: r0->field_c3 = r16
    //     0xe8cd50: stur            w16, [x0, #0xc3]
    // 0xe8cd54: r16 = 88
    //     0xe8cd54: movz            x16, #0x58
    // 0xe8cd58: StoreField: r0->field_c7 = r16
    //     0xe8cd58: stur            w16, [x0, #0xc7]
    // 0xe8cd5c: r16 = 72
    //     0xe8cd5c: movz            x16, #0x48
    // 0xe8cd60: StoreField: r0->field_cb = r16
    //     0xe8cd60: stur            w16, [x0, #0xcb]
    // 0xe8cd64: r16 = 56
    //     0xe8cd64: movz            x16, #0x38
    // 0xe8cd68: StoreField: r0->field_cf = r16
    //     0xe8cd68: stur            w16, [x0, #0xcf]
    // 0xe8cd6c: r16 = 40
    //     0xe8cd6c: movz            x16, #0x28
    // 0xe8cd70: StoreField: r0->field_d3 = r16
    //     0xe8cd70: stur            w16, [x0, #0xd3]
    // 0xe8cd74: r16 = 24
    //     0xe8cd74: movz            x16, #0x18
    // 0xe8cd78: StoreField: r0->field_d7 = r16
    //     0xe8cd78: stur            w16, [x0, #0xd7]
    // 0xe8cd7c: r16 = 8
    //     0xe8cd7c: movz            x16, #0x8
    // 0xe8cd80: StoreField: r0->field_db = r16
    //     0xe8cd80: stur            w16, [x0, #0xdb]
    // 0xe8cd84: r16 = 54
    //     0xe8cd84: movz            x16, #0x36
    // 0xe8cd88: StoreField: r0->field_df = r16
    //     0xe8cd88: stur            w16, [x0, #0xdf]
    // 0xe8cd8c: r16 = 38
    //     0xe8cd8c: movz            x16, #0x26
    // 0xe8cd90: StoreField: r0->field_e3 = r16
    //     0xe8cd90: stur            w16, [x0, #0xe3]
    // 0xe8cd94: r16 = 22
    //     0xe8cd94: movz            x16, #0x16
    // 0xe8cd98: StoreField: r0->field_e7 = r16
    //     0xe8cd98: stur            w16, [x0, #0xe7]
    // 0xe8cd9c: r16 = 6
    //     0xe8cd9c: movz            x16, #0x6
    // 0xe8cda0: StoreField: r0->field_eb = r16
    //     0xe8cda0: stur            w16, [x0, #0xeb]
    // 0xe8cda4: r1 = <int>
    //     0xe8cda4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8cda8: r0 = AllocateGrowableArray()
    //     0xe8cda8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe8cdac: mov             x1, x0
    // 0xe8cdb0: ldur            x0, [fp, #-8]
    // 0xe8cdb4: stur            x1, [fp, #-0x10]
    // 0xe8cdb8: StoreField: r1->field_f = r0
    //     0xe8cdb8: stur            w0, [x1, #0xf]
    // 0xe8cdbc: r4 = 112
    //     0xe8cdbc: movz            x4, #0x70
    // 0xe8cdc0: StoreField: r1->field_b = r4
    //     0xe8cdc0: stur            w4, [x1, #0xb]
    // 0xe8cdc4: r0 = AllocateUint8Array()
    //     0xe8cdc4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8cdc8: mov             x1, x0
    // 0xe8cdcc: ldur            x5, [fp, #-0x10]
    // 0xe8cdd0: r2 = 0
    //     0xe8cdd0: movz            x2, #0
    // 0xe8cdd4: r3 = 56
    //     0xe8cdd4: movz            x3, #0x38
    // 0xe8cdd8: r6 = 0
    //     0xe8cdd8: movz            x6, #0
    // 0xe8cddc: stur            x0, [fp, #-8]
    // 0xe8cde0: r0 = _slowSetRange()
    //     0xe8cde0: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xe8cde4: ldur            x0, [fp, #-8]
    // 0xe8cde8: LeaveFrame
    //     0xe8cde8: mov             SP, fp
    //     0xe8cdec: ldp             fp, lr, [SP], #0x10
    // 0xe8cdf0: ret
    //     0xe8cdf0: ret             
    // 0xe8cdf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8cdf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8cdf8: b               #0xe8cbd8
  }
  _ desFunc(/* No info */) {
    // ** addr: 0xeac280, size: 0xde0
    // 0xeac280: EnterFrame
    //     0xeac280: stp             fp, lr, [SP, #-0x10]!
    //     0xeac284: mov             fp, SP
    // 0xeac288: AllocStack(0x98)
    //     0xeac288: sub             SP, SP, #0x98
    // 0xeac28c: SetupParameters(DesBase this /* r1 => r8, fp-0x8 */, dynamic _ /* r2 => r7, fp-0x10 */, dynamic _ /* r3 => r6, fp-0x18 */, dynamic _ /* r5 => r4, fp-0x20 */, dynamic _ /* r6 => r0, fp-0x28 */, dynamic _ /* r7 => r5, fp-0x30 */)
    //     0xeac28c: mov             x8, x1
    //     0xeac290: mov             x4, x5
    //     0xeac294: stur            x5, [fp, #-0x20]
    //     0xeac298: mov             x5, x7
    //     0xeac29c: stur            x7, [fp, #-0x30]
    //     0xeac2a0: mov             x7, x2
    //     0xeac2a4: mov             x0, x6
    //     0xeac2a8: stur            x6, [fp, #-0x28]
    //     0xeac2ac: mov             x6, x3
    //     0xeac2b0: stur            x1, [fp, #-8]
    //     0xeac2b4: stur            x2, [fp, #-0x10]
    //     0xeac2b8: stur            x3, [fp, #-0x18]
    // 0xeac2bc: CheckStackOverflow
    //     0xeac2bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeac2c0: cmp             SP, x16
    //     0xeac2c4: b.ls            #0xeacfb4
    // 0xeac2c8: mov             x1, x8
    // 0xeac2cc: mov             x2, x6
    // 0xeac2d0: mov             x3, x4
    // 0xeac2d4: r0 = _bigEndianToInt()
    //     0xeac2d4: bl              #0xead110  ; [package:pointycastle/block/des_base.dart] DesBase::_bigEndianToInt
    // 0xeac2d8: mov             x4, x0
    // 0xeac2dc: ldur            x0, [fp, #-0x20]
    // 0xeac2e0: stur            x4, [fp, #-0x38]
    // 0xeac2e4: add             x3, x0, #4
    // 0xeac2e8: ldur            x1, [fp, #-8]
    // 0xeac2ec: ldur            x2, [fp, #-0x18]
    // 0xeac2f0: r0 = _bigEndianToInt()
    //     0xeac2f0: bl              #0xead110  ; [package:pointycastle/block/des_base.dart] DesBase::_bigEndianToInt
    // 0xeac2f4: mov             x1, x0
    // 0xeac2f8: ldur            x0, [fp, #-0x38]
    // 0xeac2fc: asr             x2, x0, #4
    // 0xeac300: mov             x3, x1
    // 0xeac304: ubfx            x3, x3, #0, #0x20
    // 0xeac308: ubfx            x2, x2, #0, #0x20
    // 0xeac30c: eor             x4, x2, x3
    // 0xeac310: r3 = 252645135
    //     0xeac310: movz            x3, #0xf0f
    //     0xeac314: movk            x3, #0xf0f, lsl #16
    // 0xeac318: and             x2, x4, x3
    // 0xeac31c: mov             x4, x2
    // 0xeac320: ubfx            x4, x4, #0, #0x20
    // 0xeac324: eor             x5, x1, x4
    // 0xeac328: stur            x5, [fp, #-0x20]
    // 0xeac32c: ubfx            x2, x2, #0, #0x20
    // 0xeac330: mov             x1, x2
    // 0xeac334: r2 = 4
    //     0xeac334: movz            x2, #0x4
    // 0xeac338: r0 = shiftl32()
    //     0xeac338: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeac33c: mov             x1, x0
    // 0xeac340: ldur            x0, [fp, #-0x38]
    // 0xeac344: eor             x3, x0, x1
    // 0xeac348: stur            x3, [fp, #-0x40]
    // 0xeac34c: asr             x0, x3, #0x10
    // 0xeac350: ldur            x1, [fp, #-0x20]
    // 0xeac354: ubfx            x1, x1, #0, #0x20
    // 0xeac358: ubfx            x0, x0, #0, #0x20
    // 0xeac35c: eor             x2, x0, x1
    // 0xeac360: r0 = 65535
    //     0xeac360: orr             x0, xzr, #0xffff
    // 0xeac364: and             x1, x2, x0
    // 0xeac368: mov             x2, x1
    // 0xeac36c: ubfx            x2, x2, #0, #0x20
    // 0xeac370: ldur            x4, [fp, #-0x20]
    // 0xeac374: eor             x5, x4, x2
    // 0xeac378: stur            x5, [fp, #-0x38]
    // 0xeac37c: ubfx            x1, x1, #0, #0x20
    // 0xeac380: r2 = 16
    //     0xeac380: movz            x2, #0x10
    // 0xeac384: r0 = shiftl32()
    //     0xeac384: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeac388: mov             x1, x0
    // 0xeac38c: ldur            x0, [fp, #-0x40]
    // 0xeac390: eor             x2, x0, x1
    // 0xeac394: ldur            x0, [fp, #-0x38]
    // 0xeac398: asr             x1, x0, #2
    // 0xeac39c: mov             x3, x2
    // 0xeac3a0: ubfx            x3, x3, #0, #0x20
    // 0xeac3a4: ubfx            x1, x1, #0, #0x20
    // 0xeac3a8: eor             x4, x1, x3
    // 0xeac3ac: r3 = 858993459
    //     0xeac3ac: movz            x3, #0x3333
    //     0xeac3b0: movk            x3, #0x3333, lsl #16
    // 0xeac3b4: and             x1, x4, x3
    // 0xeac3b8: mov             x4, x1
    // 0xeac3bc: ubfx            x4, x4, #0, #0x20
    // 0xeac3c0: eor             x5, x2, x4
    // 0xeac3c4: stur            x5, [fp, #-0x20]
    // 0xeac3c8: ubfx            x1, x1, #0, #0x20
    // 0xeac3cc: r2 = 2
    //     0xeac3cc: movz            x2, #0x2
    // 0xeac3d0: r0 = shiftl32()
    //     0xeac3d0: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeac3d4: mov             x1, x0
    // 0xeac3d8: ldur            x0, [fp, #-0x38]
    // 0xeac3dc: eor             x3, x0, x1
    // 0xeac3e0: stur            x3, [fp, #-0x40]
    // 0xeac3e4: asr             x0, x3, #8
    // 0xeac3e8: ldur            x1, [fp, #-0x20]
    // 0xeac3ec: ubfx            x1, x1, #0, #0x20
    // 0xeac3f0: ubfx            x0, x0, #0, #0x20
    // 0xeac3f4: eor             x2, x0, x1
    // 0xeac3f8: r0 = 16711935
    //     0xeac3f8: movz            x0, #0xff
    //     0xeac3fc: movk            x0, #0xff, lsl #16
    // 0xeac400: and             x1, x2, x0
    // 0xeac404: mov             x2, x1
    // 0xeac408: ubfx            x2, x2, #0, #0x20
    // 0xeac40c: ldur            x4, [fp, #-0x20]
    // 0xeac410: eor             x5, x4, x2
    // 0xeac414: stur            x5, [fp, #-0x38]
    // 0xeac418: ubfx            x1, x1, #0, #0x20
    // 0xeac41c: r2 = 8
    //     0xeac41c: movz            x2, #0x8
    // 0xeac420: r0 = shiftl32()
    //     0xeac420: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeac424: mov             x1, x0
    // 0xeac428: ldur            x0, [fp, #-0x40]
    // 0xeac42c: eor             x3, x0, x1
    // 0xeac430: mov             x1, x3
    // 0xeac434: stur            x3, [fp, #-0x20]
    // 0xeac438: r2 = 1
    //     0xeac438: movz            x2, #0x1
    // 0xeac43c: r0 = shiftl32()
    //     0xeac43c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeac440: mov             x1, x0
    // 0xeac444: ldur            x0, [fp, #-0x20]
    // 0xeac448: asr             x2, x0, #0x1f
    // 0xeac44c: orr             x0, x1, x2
    // 0xeac450: ldur            x1, [fp, #-0x38]
    // 0xeac454: ubfx            x1, x1, #0, #0x20
    // 0xeac458: mov             x2, x0
    // 0xeac45c: ubfx            x2, x2, #0, #0x20
    // 0xeac460: eor             x3, x1, x2
    // 0xeac464: r4 = 2863311530
    //     0xeac464: movz            x4, #0xaaaa
    //     0xeac468: movk            x4, #0xaaaa, lsl #16
    // 0xeac46c: and             x1, x3, x4
    // 0xeac470: mov             x2, x1
    // 0xeac474: ubfx            x2, x2, #0, #0x20
    // 0xeac478: ldur            x3, [fp, #-0x38]
    // 0xeac47c: eor             x5, x3, x2
    // 0xeac480: stur            x5, [fp, #-0x40]
    // 0xeac484: ubfx            x1, x1, #0, #0x20
    // 0xeac488: eor             x3, x0, x1
    // 0xeac48c: mov             x1, x5
    // 0xeac490: stur            x3, [fp, #-0x20]
    // 0xeac494: r2 = 1
    //     0xeac494: movz            x2, #0x1
    // 0xeac498: r0 = shiftl32()
    //     0xeac498: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeac49c: mov             x1, x0
    // 0xeac4a0: ldur            x0, [fp, #-0x40]
    // 0xeac4a4: asr             x2, x0, #0x1f
    // 0xeac4a8: orr             x0, x1, x2
    // 0xeac4ac: ldur            x3, [fp, #-0x20]
    // 0xeac4b0: mov             x2, x0
    // 0xeac4b4: ldur            x0, [fp, #-0x10]
    // 0xeac4b8: r1 = 0
    //     0xeac4b8: movz            x1, #0
    // 0xeac4bc: stur            x3, [fp, #-0x20]
    // 0xeac4c0: stur            x2, [fp, #-0x38]
    // 0xeac4c4: stur            x1, [fp, #-0x40]
    // 0xeac4c8: CheckStackOverflow
    //     0xeac4c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeac4cc: cmp             SP, x16
    //     0xeac4d0: b.ls            #0xeacfbc
    // 0xeac4d4: cmp             x1, #8
    // 0xeac4d8: b.ge            #0xeacdbc
    // 0xeac4dc: r0 = InitLateStaticField(0xf58) // [package:pointycastle/src/ufixnum.dart] ::_MASK32_HI_BITS
    //     0xeac4dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeac4e0: ldr             x0, [x0, #0x1eb0]
    //     0xeac4e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeac4e8: cmp             w0, w16
    //     0xeac4ec: b.ne            #0xeac4fc
    //     0xeac4f0: add             x2, PP, #0x19, lsl #12  ; [pp+0x194e0] Field <::._MASK32_HI_BITS@1011143242>: static late final (offset: 0xf58)
    //     0xeac4f4: ldr             x2, [x2, #0x4e0]
    //     0xeac4f8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeac4fc: mov             x2, x0
    // 0xeac500: stur            x2, [fp, #-0x18]
    // 0xeac504: LoadField: r0 = r2->field_b
    //     0xeac504: ldur            w0, [x2, #0xb]
    // 0xeac508: r1 = LoadInt32Instr(r0)
    //     0xeac508: sbfx            x1, x0, #1, #0x1f
    // 0xeac50c: mov             x0, x1
    // 0xeac510: r1 = 28
    //     0xeac510: movz            x1, #0x1c
    // 0xeac514: cmp             x1, x0
    // 0xeac518: b.hs            #0xeacfc4
    // 0xeac51c: LoadField: r0 = r2->field_f
    //     0xeac51c: ldur            w0, [x2, #0xf]
    // 0xeac520: DecompressPointer r0
    //     0xeac520: add             x0, x0, HEAP, lsl #32
    // 0xeac524: LoadField: r1 = r0->field_7f
    //     0xeac524: ldur            w1, [x0, #0x7f]
    // 0xeac528: DecompressPointer r1
    //     0xeac528: add             x1, x1, HEAP, lsl #32
    // 0xeac52c: r0 = LoadInt32Instr(r1)
    //     0xeac52c: sbfx            x0, x1, #1, #0x1f
    //     0xeac530: tbz             w1, #0, #0xeac538
    //     0xeac534: ldur            x0, [x1, #7]
    // 0xeac538: ldur            x1, [fp, #-0x20]
    // 0xeac53c: ubfx            x1, x1, #0, #0x20
    // 0xeac540: and             x3, x1, x0
    // 0xeac544: r4 = 28
    //     0xeac544: movz            x4, #0x1c
    // 0xeac548: tbnz            x4, #0x3f, #0xeacfc8
    // 0xeac54c: lsl             w0, w3, w4
    // 0xeac550: cmp             x4, #0x1f
    // 0xeac554: csel            x0, x0, xzr, le
    // 0xeac558: ldur            x3, [fp, #-0x20]
    // 0xeac55c: asr             x1, x3, #4
    // 0xeac560: ubfx            x0, x0, #0, #0x20
    // 0xeac564: orr             x5, x0, x1
    // 0xeac568: ldur            x6, [fp, #-0x40]
    // 0xeac56c: lsl             x7, x6, #2
    // 0xeac570: ldur            x8, [fp, #-0x10]
    // 0xeac574: stur            x7, [fp, #-0x50]
    // 0xeac578: LoadField: r0 = r8->field_b
    //     0xeac578: ldur            w0, [x8, #0xb]
    // 0xeac57c: r1 = LoadInt32Instr(r0)
    //     0xeac57c: sbfx            x1, x0, #1, #0x1f
    // 0xeac580: mov             x0, x1
    // 0xeac584: mov             x1, x7
    // 0xeac588: cmp             x1, x0
    // 0xeac58c: b.hs            #0xeacfe8
    // 0xeac590: LoadField: r0 = r8->field_f
    //     0xeac590: ldur            w0, [x8, #0xf]
    // 0xeac594: DecompressPointer r0
    //     0xeac594: add             x0, x0, HEAP, lsl #32
    // 0xeac598: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xeac598: add             x16, x0, x7, lsl #2
    //     0xeac59c: ldur            w1, [x16, #0xf]
    // 0xeac5a0: DecompressPointer r1
    //     0xeac5a0: add             x1, x1, HEAP, lsl #32
    // 0xeac5a4: r0 = LoadInt32Instr(r1)
    //     0xeac5a4: sbfx            x0, x1, #1, #0x1f
    //     0xeac5a8: tbz             w1, #0, #0xeac5b0
    //     0xeac5ac: ldur            x0, [x1, #7]
    // 0xeac5b0: eor             x1, x5, x0
    // 0xeac5b4: stur            x1, [fp, #-0x48]
    // 0xeac5b8: r0 = InitLateStaticField(0xd8c) // [package:pointycastle/block/des_base.dart] DesBase::SP7
    //     0xeac5b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeac5bc: ldr             x0, [x0, #0x1b18]
    //     0xeac5c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeac5c4: cmp             w0, w16
    //     0xeac5c8: b.ne            #0xeac5d8
    //     0xeac5cc: add             x2, PP, #0x23, lsl #12  ; [pp+0x236c8] Field <DesBase.SP7>: static late final (offset: 0xd8c)
    //     0xeac5d0: ldr             x2, [x2, #0x6c8]
    //     0xeac5d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeac5d8: mov             x2, x0
    // 0xeac5dc: ldur            x0, [fp, #-0x48]
    // 0xeac5e0: stur            x2, [fp, #-0x60]
    // 0xeac5e4: ubfx            x0, x0, #0, #0x20
    // 0xeac5e8: r3 = 63
    //     0xeac5e8: movz            x3, #0x3f
    // 0xeac5ec: and             x1, x0, x3
    // 0xeac5f0: LoadField: r0 = r2->field_b
    //     0xeac5f0: ldur            w0, [x2, #0xb]
    // 0xeac5f4: r4 = LoadInt32Instr(r0)
    //     0xeac5f4: sbfx            x4, x0, #1, #0x1f
    // 0xeac5f8: mov             x5, x1
    // 0xeac5fc: ubfx            x5, x5, #0, #0x20
    // 0xeac600: mov             x0, x4
    // 0xeac604: mov             x1, x5
    // 0xeac608: cmp             x1, x0
    // 0xeac60c: b.hs            #0xeacfec
    // 0xeac610: LoadField: r0 = r2->field_f
    //     0xeac610: ldur            w0, [x2, #0xf]
    // 0xeac614: DecompressPointer r0
    //     0xeac614: add             x0, x0, HEAP, lsl #32
    // 0xeac618: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xeac618: add             x16, x0, x5, lsl #2
    //     0xeac61c: ldur            w1, [x16, #0xf]
    // 0xeac620: DecompressPointer r1
    //     0xeac620: add             x1, x1, HEAP, lsl #32
    // 0xeac624: stur            x1, [fp, #-0x58]
    // 0xeac628: r0 = InitLateStaticField(0xd84) // [package:pointycastle/block/des_base.dart] DesBase::SP5
    //     0xeac628: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeac62c: ldr             x0, [x0, #0x1b08]
    //     0xeac630: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeac634: cmp             w0, w16
    //     0xeac638: b.ne            #0xeac648
    //     0xeac63c: add             x2, PP, #0x23, lsl #12  ; [pp+0x236d0] Field <DesBase.SP5>: static late final (offset: 0xd84)
    //     0xeac640: ldr             x2, [x2, #0x6d0]
    //     0xeac644: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeac648: mov             x3, x0
    // 0xeac64c: ldur            x2, [fp, #-0x48]
    // 0xeac650: stur            x3, [fp, #-0x70]
    // 0xeac654: asr             x0, x2, #8
    // 0xeac658: ubfx            x0, x0, #0, #0x20
    // 0xeac65c: r4 = 63
    //     0xeac65c: movz            x4, #0x3f
    // 0xeac660: and             x1, x0, x4
    // 0xeac664: LoadField: r0 = r3->field_b
    //     0xeac664: ldur            w0, [x3, #0xb]
    // 0xeac668: r5 = LoadInt32Instr(r0)
    //     0xeac668: sbfx            x5, x0, #1, #0x1f
    // 0xeac66c: mov             x6, x1
    // 0xeac670: ubfx            x6, x6, #0, #0x20
    // 0xeac674: mov             x0, x5
    // 0xeac678: mov             x1, x6
    // 0xeac67c: cmp             x1, x0
    // 0xeac680: b.hs            #0xeacff0
    // 0xeac684: LoadField: r0 = r3->field_f
    //     0xeac684: ldur            w0, [x3, #0xf]
    // 0xeac688: DecompressPointer r0
    //     0xeac688: add             x0, x0, HEAP, lsl #32
    // 0xeac68c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xeac68c: add             x16, x0, x6, lsl #2
    //     0xeac690: ldur            w1, [x16, #0xf]
    // 0xeac694: DecompressPointer r1
    //     0xeac694: add             x1, x1, HEAP, lsl #32
    // 0xeac698: ldur            x0, [fp, #-0x58]
    // 0xeac69c: r5 = LoadInt32Instr(r0)
    //     0xeac69c: sbfx            x5, x0, #1, #0x1f
    //     0xeac6a0: tbz             w0, #0, #0xeac6a8
    //     0xeac6a4: ldur            x5, [x0, #7]
    // 0xeac6a8: r0 = LoadInt32Instr(r1)
    //     0xeac6a8: sbfx            x0, x1, #1, #0x1f
    //     0xeac6ac: tbz             w1, #0, #0xeac6b4
    //     0xeac6b0: ldur            x0, [x1, #7]
    // 0xeac6b4: orr             x1, x5, x0
    // 0xeac6b8: stur            x1, [fp, #-0x68]
    // 0xeac6bc: r0 = InitLateStaticField(0xd7c) // [package:pointycastle/block/des_base.dart] DesBase::SP3
    //     0xeac6bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeac6c0: ldr             x0, [x0, #0x1af8]
    //     0xeac6c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeac6c8: cmp             w0, w16
    //     0xeac6cc: b.ne            #0xeac6dc
    //     0xeac6d0: add             x2, PP, #0x23, lsl #12  ; [pp+0x236d8] Field <DesBase.SP3>: static late final (offset: 0xd7c)
    //     0xeac6d4: ldr             x2, [x2, #0x6d8]
    //     0xeac6d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeac6dc: mov             x3, x0
    // 0xeac6e0: ldur            x2, [fp, #-0x48]
    // 0xeac6e4: stur            x3, [fp, #-0x58]
    // 0xeac6e8: asr             x0, x2, #0x10
    // 0xeac6ec: ubfx            x0, x0, #0, #0x20
    // 0xeac6f0: r4 = 63
    //     0xeac6f0: movz            x4, #0x3f
    // 0xeac6f4: and             x1, x0, x4
    // 0xeac6f8: LoadField: r0 = r3->field_b
    //     0xeac6f8: ldur            w0, [x3, #0xb]
    // 0xeac6fc: r5 = LoadInt32Instr(r0)
    //     0xeac6fc: sbfx            x5, x0, #1, #0x1f
    // 0xeac700: mov             x6, x1
    // 0xeac704: ubfx            x6, x6, #0, #0x20
    // 0xeac708: mov             x0, x5
    // 0xeac70c: mov             x1, x6
    // 0xeac710: cmp             x1, x0
    // 0xeac714: b.hs            #0xeacff4
    // 0xeac718: LoadField: r0 = r3->field_f
    //     0xeac718: ldur            w0, [x3, #0xf]
    // 0xeac71c: DecompressPointer r0
    //     0xeac71c: add             x0, x0, HEAP, lsl #32
    // 0xeac720: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xeac720: add             x16, x0, x6, lsl #2
    //     0xeac724: ldur            w1, [x16, #0xf]
    // 0xeac728: DecompressPointer r1
    //     0xeac728: add             x1, x1, HEAP, lsl #32
    // 0xeac72c: r0 = LoadInt32Instr(r1)
    //     0xeac72c: sbfx            x0, x1, #1, #0x1f
    //     0xeac730: tbz             w1, #0, #0xeac738
    //     0xeac734: ldur            x0, [x1, #7]
    // 0xeac738: ldur            x1, [fp, #-0x68]
    // 0xeac73c: orr             x5, x1, x0
    // 0xeac740: stur            x5, [fp, #-0x78]
    // 0xeac744: r0 = InitLateStaticField(0xd74) // [package:pointycastle/block/des_base.dart] DesBase::SP1
    //     0xeac744: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeac748: ldr             x0, [x0, #0x1ae8]
    //     0xeac74c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeac750: cmp             w0, w16
    //     0xeac754: b.ne            #0xeac764
    //     0xeac758: add             x2, PP, #0x23, lsl #12  ; [pp+0x236e0] Field <DesBase.SP1>: static late final (offset: 0xd74)
    //     0xeac75c: ldr             x2, [x2, #0x6e0]
    //     0xeac760: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeac764: mov             x2, x0
    // 0xeac768: ldur            x0, [fp, #-0x48]
    // 0xeac76c: stur            x2, [fp, #-0x80]
    // 0xeac770: asr             x1, x0, #0x18
    // 0xeac774: ubfx            x1, x1, #0, #0x20
    // 0xeac778: r3 = 63
    //     0xeac778: movz            x3, #0x3f
    // 0xeac77c: and             x0, x1, x3
    // 0xeac780: LoadField: r1 = r2->field_b
    //     0xeac780: ldur            w1, [x2, #0xb]
    // 0xeac784: r4 = LoadInt32Instr(r1)
    //     0xeac784: sbfx            x4, x1, #1, #0x1f
    // 0xeac788: mov             x5, x0
    // 0xeac78c: ubfx            x5, x5, #0, #0x20
    // 0xeac790: mov             x0, x4
    // 0xeac794: mov             x1, x5
    // 0xeac798: cmp             x1, x0
    // 0xeac79c: b.hs            #0xeacff8
    // 0xeac7a0: LoadField: r0 = r2->field_f
    //     0xeac7a0: ldur            w0, [x2, #0xf]
    // 0xeac7a4: DecompressPointer r0
    //     0xeac7a4: add             x0, x0, HEAP, lsl #32
    // 0xeac7a8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xeac7a8: add             x16, x0, x5, lsl #2
    //     0xeac7ac: ldur            w1, [x16, #0xf]
    // 0xeac7b0: DecompressPointer r1
    //     0xeac7b0: add             x1, x1, HEAP, lsl #32
    // 0xeac7b4: r0 = LoadInt32Instr(r1)
    //     0xeac7b4: sbfx            x0, x1, #1, #0x1f
    //     0xeac7b8: tbz             w1, #0, #0xeac7c0
    //     0xeac7bc: ldur            x0, [x1, #7]
    // 0xeac7c0: ldur            x1, [fp, #-0x78]
    // 0xeac7c4: orr             x4, x1, x0
    // 0xeac7c8: ldur            x5, [fp, #-0x50]
    // 0xeac7cc: stur            x4, [fp, #-0x68]
    // 0xeac7d0: add             x6, x5, #1
    // 0xeac7d4: ldur            x7, [fp, #-0x10]
    // 0xeac7d8: LoadField: r0 = r7->field_b
    //     0xeac7d8: ldur            w0, [x7, #0xb]
    // 0xeac7dc: r1 = LoadInt32Instr(r0)
    //     0xeac7dc: sbfx            x1, x0, #1, #0x1f
    // 0xeac7e0: mov             x0, x1
    // 0xeac7e4: mov             x1, x6
    // 0xeac7e8: cmp             x1, x0
    // 0xeac7ec: b.hs            #0xeacffc
    // 0xeac7f0: LoadField: r0 = r7->field_f
    //     0xeac7f0: ldur            w0, [x7, #0xf]
    // 0xeac7f4: DecompressPointer r0
    //     0xeac7f4: add             x0, x0, HEAP, lsl #32
    // 0xeac7f8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xeac7f8: add             x16, x0, x6, lsl #2
    //     0xeac7fc: ldur            w1, [x16, #0xf]
    // 0xeac800: DecompressPointer r1
    //     0xeac800: add             x1, x1, HEAP, lsl #32
    // 0xeac804: r0 = LoadInt32Instr(r1)
    //     0xeac804: sbfx            x0, x1, #1, #0x1f
    //     0xeac808: tbz             w1, #0, #0xeac810
    //     0xeac80c: ldur            x0, [x1, #7]
    // 0xeac810: ldur            x1, [fp, #-0x20]
    // 0xeac814: eor             x6, x1, x0
    // 0xeac818: stur            x6, [fp, #-0x48]
    // 0xeac81c: r0 = InitLateStaticField(0xd90) // [package:pointycastle/block/des_base.dart] DesBase::SP8
    //     0xeac81c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeac820: ldr             x0, [x0, #0x1b20]
    //     0xeac824: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeac828: cmp             w0, w16
    //     0xeac82c: b.ne            #0xeac83c
    //     0xeac830: add             x2, PP, #0x23, lsl #12  ; [pp+0x236e8] Field <DesBase.SP8>: static late final (offset: 0xd90)
    //     0xeac834: ldr             x2, [x2, #0x6e8]
    //     0xeac838: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeac83c: mov             x2, x0
    // 0xeac840: ldur            x0, [fp, #-0x48]
    // 0xeac844: stur            x2, [fp, #-0x88]
    // 0xeac848: ubfx            x0, x0, #0, #0x20
    // 0xeac84c: r3 = 63
    //     0xeac84c: movz            x3, #0x3f
    // 0xeac850: and             x1, x0, x3
    // 0xeac854: LoadField: r0 = r2->field_b
    //     0xeac854: ldur            w0, [x2, #0xb]
    // 0xeac858: r4 = LoadInt32Instr(r0)
    //     0xeac858: sbfx            x4, x0, #1, #0x1f
    // 0xeac85c: mov             x5, x1
    // 0xeac860: ubfx            x5, x5, #0, #0x20
    // 0xeac864: mov             x0, x4
    // 0xeac868: mov             x1, x5
    // 0xeac86c: cmp             x1, x0
    // 0xeac870: b.hs            #0xead000
    // 0xeac874: LoadField: r0 = r2->field_f
    //     0xeac874: ldur            w0, [x2, #0xf]
    // 0xeac878: DecompressPointer r0
    //     0xeac878: add             x0, x0, HEAP, lsl #32
    // 0xeac87c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xeac87c: add             x16, x0, x5, lsl #2
    //     0xeac880: ldur            w1, [x16, #0xf]
    // 0xeac884: DecompressPointer r1
    //     0xeac884: add             x1, x1, HEAP, lsl #32
    // 0xeac888: r0 = LoadInt32Instr(r1)
    //     0xeac888: sbfx            x0, x1, #1, #0x1f
    //     0xeac88c: tbz             w1, #0, #0xeac894
    //     0xeac890: ldur            x0, [x1, #7]
    // 0xeac894: ldur            x1, [fp, #-0x68]
    // 0xeac898: orr             x4, x1, x0
    // 0xeac89c: stur            x4, [fp, #-0x78]
    // 0xeac8a0: r0 = InitLateStaticField(0xd88) // [package:pointycastle/block/des_base.dart] DesBase::SP6
    //     0xeac8a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeac8a4: ldr             x0, [x0, #0x1b10]
    //     0xeac8a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeac8ac: cmp             w0, w16
    //     0xeac8b0: b.ne            #0xeac8c0
    //     0xeac8b4: add             x2, PP, #0x23, lsl #12  ; [pp+0x236f0] Field <DesBase.SP6>: static late final (offset: 0xd88)
    //     0xeac8b8: ldr             x2, [x2, #0x6f0]
    //     0xeac8bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeac8c0: mov             x3, x0
    // 0xeac8c4: ldur            x2, [fp, #-0x48]
    // 0xeac8c8: stur            x3, [fp, #-0x90]
    // 0xeac8cc: asr             x0, x2, #8
    // 0xeac8d0: ubfx            x0, x0, #0, #0x20
    // 0xeac8d4: r4 = 63
    //     0xeac8d4: movz            x4, #0x3f
    // 0xeac8d8: and             x1, x0, x4
    // 0xeac8dc: LoadField: r0 = r3->field_b
    //     0xeac8dc: ldur            w0, [x3, #0xb]
    // 0xeac8e0: r5 = LoadInt32Instr(r0)
    //     0xeac8e0: sbfx            x5, x0, #1, #0x1f
    // 0xeac8e4: mov             x6, x1
    // 0xeac8e8: ubfx            x6, x6, #0, #0x20
    // 0xeac8ec: mov             x0, x5
    // 0xeac8f0: mov             x1, x6
    // 0xeac8f4: cmp             x1, x0
    // 0xeac8f8: b.hs            #0xead004
    // 0xeac8fc: LoadField: r0 = r3->field_f
    //     0xeac8fc: ldur            w0, [x3, #0xf]
    // 0xeac900: DecompressPointer r0
    //     0xeac900: add             x0, x0, HEAP, lsl #32
    // 0xeac904: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xeac904: add             x16, x0, x6, lsl #2
    //     0xeac908: ldur            w1, [x16, #0xf]
    // 0xeac90c: DecompressPointer r1
    //     0xeac90c: add             x1, x1, HEAP, lsl #32
    // 0xeac910: r0 = LoadInt32Instr(r1)
    //     0xeac910: sbfx            x0, x1, #1, #0x1f
    //     0xeac914: tbz             w1, #0, #0xeac91c
    //     0xeac918: ldur            x0, [x1, #7]
    // 0xeac91c: ldur            x1, [fp, #-0x78]
    // 0xeac920: orr             x5, x1, x0
    // 0xeac924: stur            x5, [fp, #-0x68]
    // 0xeac928: r0 = InitLateStaticField(0xd80) // [package:pointycastle/block/des_base.dart] DesBase::SP4
    //     0xeac928: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeac92c: ldr             x0, [x0, #0x1b00]
    //     0xeac930: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeac934: cmp             w0, w16
    //     0xeac938: b.ne            #0xeac948
    //     0xeac93c: add             x2, PP, #0x23, lsl #12  ; [pp+0x236f8] Field <DesBase.SP4>: static late final (offset: 0xd80)
    //     0xeac940: ldr             x2, [x2, #0x6f8]
    //     0xeac944: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeac948: mov             x3, x0
    // 0xeac94c: ldur            x2, [fp, #-0x48]
    // 0xeac950: stur            x3, [fp, #-0x98]
    // 0xeac954: asr             x0, x2, #0x10
    // 0xeac958: ubfx            x0, x0, #0, #0x20
    // 0xeac95c: r4 = 63
    //     0xeac95c: movz            x4, #0x3f
    // 0xeac960: and             x1, x0, x4
    // 0xeac964: LoadField: r0 = r3->field_b
    //     0xeac964: ldur            w0, [x3, #0xb]
    // 0xeac968: r5 = LoadInt32Instr(r0)
    //     0xeac968: sbfx            x5, x0, #1, #0x1f
    // 0xeac96c: mov             x6, x1
    // 0xeac970: ubfx            x6, x6, #0, #0x20
    // 0xeac974: mov             x0, x5
    // 0xeac978: mov             x1, x6
    // 0xeac97c: cmp             x1, x0
    // 0xeac980: b.hs            #0xead008
    // 0xeac984: LoadField: r0 = r3->field_f
    //     0xeac984: ldur            w0, [x3, #0xf]
    // 0xeac988: DecompressPointer r0
    //     0xeac988: add             x0, x0, HEAP, lsl #32
    // 0xeac98c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xeac98c: add             x16, x0, x6, lsl #2
    //     0xeac990: ldur            w1, [x16, #0xf]
    // 0xeac994: DecompressPointer r1
    //     0xeac994: add             x1, x1, HEAP, lsl #32
    // 0xeac998: r0 = LoadInt32Instr(r1)
    //     0xeac998: sbfx            x0, x1, #1, #0x1f
    //     0xeac99c: tbz             w1, #0, #0xeac9a4
    //     0xeac9a0: ldur            x0, [x1, #7]
    // 0xeac9a4: ldur            x1, [fp, #-0x68]
    // 0xeac9a8: orr             x5, x1, x0
    // 0xeac9ac: stur            x5, [fp, #-0x78]
    // 0xeac9b0: r0 = InitLateStaticField(0xd78) // [package:pointycastle/block/des_base.dart] DesBase::SP2
    //     0xeac9b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeac9b4: ldr             x0, [x0, #0x1af0]
    //     0xeac9b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeac9bc: cmp             w0, w16
    //     0xeac9c0: b.ne            #0xeac9d0
    //     0xeac9c4: add             x2, PP, #0x23, lsl #12  ; [pp+0x23700] Field <DesBase.SP2>: static late final (offset: 0xd78)
    //     0xeac9c8: ldr             x2, [x2, #0x700]
    //     0xeac9cc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeac9d0: mov             x2, x0
    // 0xeac9d4: ldur            x0, [fp, #-0x48]
    // 0xeac9d8: asr             x1, x0, #0x18
    // 0xeac9dc: ubfx            x1, x1, #0, #0x20
    // 0xeac9e0: r4 = 63
    //     0xeac9e0: movz            x4, #0x3f
    // 0xeac9e4: and             x0, x1, x4
    // 0xeac9e8: LoadField: r1 = r2->field_b
    //     0xeac9e8: ldur            w1, [x2, #0xb]
    // 0xeac9ec: r3 = LoadInt32Instr(r1)
    //     0xeac9ec: sbfx            x3, x1, #1, #0x1f
    // 0xeac9f0: mov             x5, x0
    // 0xeac9f4: ubfx            x5, x5, #0, #0x20
    // 0xeac9f8: mov             x0, x3
    // 0xeac9fc: mov             x1, x5
    // 0xeaca00: cmp             x1, x0
    // 0xeaca04: b.hs            #0xead00c
    // 0xeaca08: LoadField: r6 = r2->field_f
    //     0xeaca08: ldur            w6, [x2, #0xf]
    // 0xeaca0c: DecompressPointer r6
    //     0xeaca0c: add             x6, x6, HEAP, lsl #32
    // 0xeaca10: ArrayLoad: r0 = r6[r5]  ; Unknown_4
    //     0xeaca10: add             x16, x6, x5, lsl #2
    //     0xeaca14: ldur            w0, [x16, #0xf]
    // 0xeaca18: DecompressPointer r0
    //     0xeaca18: add             x0, x0, HEAP, lsl #32
    // 0xeaca1c: r1 = LoadInt32Instr(r0)
    //     0xeaca1c: sbfx            x1, x0, #1, #0x1f
    //     0xeaca20: tbz             w0, #0, #0xeaca28
    //     0xeaca24: ldur            x1, [x0, #7]
    // 0xeaca28: ldur            x0, [fp, #-0x78]
    // 0xeaca2c: orr             x2, x0, x1
    // 0xeaca30: ldur            x0, [fp, #-0x38]
    // 0xeaca34: eor             x5, x0, x2
    // 0xeaca38: ldur            x2, [fp, #-0x18]
    // 0xeaca3c: LoadField: r0 = r2->field_b
    //     0xeaca3c: ldur            w0, [x2, #0xb]
    // 0xeaca40: r1 = LoadInt32Instr(r0)
    //     0xeaca40: sbfx            x1, x0, #1, #0x1f
    // 0xeaca44: mov             x0, x1
    // 0xeaca48: r1 = 28
    //     0xeaca48: movz            x1, #0x1c
    // 0xeaca4c: cmp             x1, x0
    // 0xeaca50: b.hs            #0xead010
    // 0xeaca54: LoadField: r0 = r2->field_f
    //     0xeaca54: ldur            w0, [x2, #0xf]
    // 0xeaca58: DecompressPointer r0
    //     0xeaca58: add             x0, x0, HEAP, lsl #32
    // 0xeaca5c: LoadField: r1 = r0->field_7f
    //     0xeaca5c: ldur            w1, [x0, #0x7f]
    // 0xeaca60: DecompressPointer r1
    //     0xeaca60: add             x1, x1, HEAP, lsl #32
    // 0xeaca64: r0 = LoadInt32Instr(r1)
    //     0xeaca64: sbfx            x0, x1, #1, #0x1f
    //     0xeaca68: tbz             w1, #0, #0xeaca70
    //     0xeaca6c: ldur            x0, [x1, #7]
    // 0xeaca70: mov             x1, x5
    // 0xeaca74: ubfx            x1, x1, #0, #0x20
    // 0xeaca78: and             x2, x1, x0
    // 0xeaca7c: r7 = 28
    //     0xeaca7c: movz            x7, #0x1c
    // 0xeaca80: tbnz            x7, #0x3f, #0xead014
    // 0xeaca84: lsl             w0, w2, w7
    // 0xeaca88: cmp             x7, #0x1f
    // 0xeaca8c: csel            x0, x0, xzr, le
    // 0xeaca90: asr             x1, x5, #4
    // 0xeaca94: ubfx            x0, x0, #0, #0x20
    // 0xeaca98: orr             x2, x0, x1
    // 0xeaca9c: ldur            x8, [fp, #-0x50]
    // 0xeacaa0: add             x9, x8, #2
    // 0xeacaa4: ldur            x10, [fp, #-0x10]
    // 0xeacaa8: LoadField: r0 = r10->field_b
    //     0xeacaa8: ldur            w0, [x10, #0xb]
    // 0xeacaac: r11 = LoadInt32Instr(r0)
    //     0xeacaac: sbfx            x11, x0, #1, #0x1f
    // 0xeacab0: mov             x0, x11
    // 0xeacab4: mov             x1, x9
    // 0xeacab8: cmp             x1, x0
    // 0xeacabc: b.hs            #0xead038
    // 0xeacac0: LoadField: r12 = r10->field_f
    //     0xeacac0: ldur            w12, [x10, #0xf]
    // 0xeacac4: DecompressPointer r12
    //     0xeacac4: add             x12, x12, HEAP, lsl #32
    // 0xeacac8: ArrayLoad: r0 = r12[r9]  ; Unknown_4
    //     0xeacac8: add             x16, x12, x9, lsl #2
    //     0xeacacc: ldur            w0, [x16, #0xf]
    // 0xeacad0: DecompressPointer r0
    //     0xeacad0: add             x0, x0, HEAP, lsl #32
    // 0xeacad4: r1 = LoadInt32Instr(r0)
    //     0xeacad4: sbfx            x1, x0, #1, #0x1f
    //     0xeacad8: tbz             w0, #0, #0xeacae0
    //     0xeacadc: ldur            x1, [x0, #7]
    // 0xeacae0: eor             x9, x2, x1
    // 0xeacae4: mov             x0, x9
    // 0xeacae8: ubfx            x0, x0, #0, #0x20
    // 0xeacaec: and             x1, x0, x4
    // 0xeacaf0: ldur            x2, [fp, #-0x60]
    // 0xeacaf4: LoadField: r0 = r2->field_b
    //     0xeacaf4: ldur            w0, [x2, #0xb]
    // 0xeacaf8: r13 = LoadInt32Instr(r0)
    //     0xeacaf8: sbfx            x13, x0, #1, #0x1f
    // 0xeacafc: mov             x14, x1
    // 0xeacb00: ubfx            x14, x14, #0, #0x20
    // 0xeacb04: mov             x0, x13
    // 0xeacb08: mov             x1, x14
    // 0xeacb0c: cmp             x1, x0
    // 0xeacb10: b.hs            #0xead03c
    // 0xeacb14: LoadField: r0 = r2->field_f
    //     0xeacb14: ldur            w0, [x2, #0xf]
    // 0xeacb18: DecompressPointer r0
    //     0xeacb18: add             x0, x0, HEAP, lsl #32
    // 0xeacb1c: ArrayLoad: r2 = r0[r14]  ; Unknown_4
    //     0xeacb1c: add             x16, x0, x14, lsl #2
    //     0xeacb20: ldur            w2, [x16, #0xf]
    // 0xeacb24: DecompressPointer r2
    //     0xeacb24: add             x2, x2, HEAP, lsl #32
    // 0xeacb28: asr             x0, x9, #8
    // 0xeacb2c: ubfx            x0, x0, #0, #0x20
    // 0xeacb30: and             x1, x0, x4
    // 0xeacb34: ldur            x13, [fp, #-0x70]
    // 0xeacb38: LoadField: r0 = r13->field_b
    //     0xeacb38: ldur            w0, [x13, #0xb]
    // 0xeacb3c: r14 = LoadInt32Instr(r0)
    //     0xeacb3c: sbfx            x14, x0, #1, #0x1f
    // 0xeacb40: mov             x19, x1
    // 0xeacb44: ubfx            x19, x19, #0, #0x20
    // 0xeacb48: mov             x0, x14
    // 0xeacb4c: mov             x1, x19
    // 0xeacb50: cmp             x1, x0
    // 0xeacb54: b.hs            #0xead040
    // 0xeacb58: LoadField: r0 = r13->field_f
    //     0xeacb58: ldur            w0, [x13, #0xf]
    // 0xeacb5c: DecompressPointer r0
    //     0xeacb5c: add             x0, x0, HEAP, lsl #32
    // 0xeacb60: ArrayLoad: r1 = r0[r19]  ; Unknown_4
    //     0xeacb60: add             x16, x0, x19, lsl #2
    //     0xeacb64: ldur            w1, [x16, #0xf]
    // 0xeacb68: DecompressPointer r1
    //     0xeacb68: add             x1, x1, HEAP, lsl #32
    // 0xeacb6c: r0 = LoadInt32Instr(r2)
    //     0xeacb6c: sbfx            x0, x2, #1, #0x1f
    //     0xeacb70: tbz             w2, #0, #0xeacb78
    //     0xeacb74: ldur            x0, [x2, #7]
    // 0xeacb78: r2 = LoadInt32Instr(r1)
    //     0xeacb78: sbfx            x2, x1, #1, #0x1f
    //     0xeacb7c: tbz             w1, #0, #0xeacb84
    //     0xeacb80: ldur            x2, [x1, #7]
    // 0xeacb84: orr             x13, x0, x2
    // 0xeacb88: asr             x0, x9, #0x10
    // 0xeacb8c: ubfx            x0, x0, #0, #0x20
    // 0xeacb90: and             x1, x0, x4
    // 0xeacb94: ldur            x2, [fp, #-0x58]
    // 0xeacb98: LoadField: r0 = r2->field_b
    //     0xeacb98: ldur            w0, [x2, #0xb]
    // 0xeacb9c: r14 = LoadInt32Instr(r0)
    //     0xeacb9c: sbfx            x14, x0, #1, #0x1f
    // 0xeacba0: mov             x19, x1
    // 0xeacba4: ubfx            x19, x19, #0, #0x20
    // 0xeacba8: mov             x0, x14
    // 0xeacbac: mov             x1, x19
    // 0xeacbb0: cmp             x1, x0
    // 0xeacbb4: b.hs            #0xead044
    // 0xeacbb8: LoadField: r0 = r2->field_f
    //     0xeacbb8: ldur            w0, [x2, #0xf]
    // 0xeacbbc: DecompressPointer r0
    //     0xeacbbc: add             x0, x0, HEAP, lsl #32
    // 0xeacbc0: ArrayLoad: r1 = r0[r19]  ; Unknown_4
    //     0xeacbc0: add             x16, x0, x19, lsl #2
    //     0xeacbc4: ldur            w1, [x16, #0xf]
    // 0xeacbc8: DecompressPointer r1
    //     0xeacbc8: add             x1, x1, HEAP, lsl #32
    // 0xeacbcc: r0 = LoadInt32Instr(r1)
    //     0xeacbcc: sbfx            x0, x1, #1, #0x1f
    //     0xeacbd0: tbz             w1, #0, #0xeacbd8
    //     0xeacbd4: ldur            x0, [x1, #7]
    // 0xeacbd8: orr             x2, x13, x0
    // 0xeacbdc: asr             x0, x9, #0x18
    // 0xeacbe0: ubfx            x0, x0, #0, #0x20
    // 0xeacbe4: and             x1, x0, x4
    // 0xeacbe8: ldur            x9, [fp, #-0x80]
    // 0xeacbec: LoadField: r0 = r9->field_b
    //     0xeacbec: ldur            w0, [x9, #0xb]
    // 0xeacbf0: r13 = LoadInt32Instr(r0)
    //     0xeacbf0: sbfx            x13, x0, #1, #0x1f
    // 0xeacbf4: mov             x14, x1
    // 0xeacbf8: ubfx            x14, x14, #0, #0x20
    // 0xeacbfc: mov             x0, x13
    // 0xeacc00: mov             x1, x14
    // 0xeacc04: cmp             x1, x0
    // 0xeacc08: b.hs            #0xead048
    // 0xeacc0c: LoadField: r0 = r9->field_f
    //     0xeacc0c: ldur            w0, [x9, #0xf]
    // 0xeacc10: DecompressPointer r0
    //     0xeacc10: add             x0, x0, HEAP, lsl #32
    // 0xeacc14: ArrayLoad: r1 = r0[r14]  ; Unknown_4
    //     0xeacc14: add             x16, x0, x14, lsl #2
    //     0xeacc18: ldur            w1, [x16, #0xf]
    // 0xeacc1c: DecompressPointer r1
    //     0xeacc1c: add             x1, x1, HEAP, lsl #32
    // 0xeacc20: r0 = LoadInt32Instr(r1)
    //     0xeacc20: sbfx            x0, x1, #1, #0x1f
    //     0xeacc24: tbz             w1, #0, #0xeacc2c
    //     0xeacc28: ldur            x0, [x1, #7]
    // 0xeacc2c: orr             x9, x2, x0
    // 0xeacc30: add             x2, x8, #3
    // 0xeacc34: mov             x0, x11
    // 0xeacc38: mov             x1, x2
    // 0xeacc3c: cmp             x1, x0
    // 0xeacc40: b.hs            #0xead04c
    // 0xeacc44: ArrayLoad: r0 = r12[r2]  ; Unknown_4
    //     0xeacc44: add             x16, x12, x2, lsl #2
    //     0xeacc48: ldur            w0, [x16, #0xf]
    // 0xeacc4c: DecompressPointer r0
    //     0xeacc4c: add             x0, x0, HEAP, lsl #32
    // 0xeacc50: r1 = LoadInt32Instr(r0)
    //     0xeacc50: sbfx            x1, x0, #1, #0x1f
    //     0xeacc54: tbz             w0, #0, #0xeacc5c
    //     0xeacc58: ldur            x1, [x0, #7]
    // 0xeacc5c: eor             x2, x5, x1
    // 0xeacc60: mov             x0, x2
    // 0xeacc64: ubfx            x0, x0, #0, #0x20
    // 0xeacc68: and             x1, x0, x4
    // 0xeacc6c: ldur            x8, [fp, #-0x88]
    // 0xeacc70: LoadField: r0 = r8->field_b
    //     0xeacc70: ldur            w0, [x8, #0xb]
    // 0xeacc74: r11 = LoadInt32Instr(r0)
    //     0xeacc74: sbfx            x11, x0, #1, #0x1f
    // 0xeacc78: mov             x12, x1
    // 0xeacc7c: ubfx            x12, x12, #0, #0x20
    // 0xeacc80: mov             x0, x11
    // 0xeacc84: mov             x1, x12
    // 0xeacc88: cmp             x1, x0
    // 0xeacc8c: b.hs            #0xead050
    // 0xeacc90: LoadField: r0 = r8->field_f
    //     0xeacc90: ldur            w0, [x8, #0xf]
    // 0xeacc94: DecompressPointer r0
    //     0xeacc94: add             x0, x0, HEAP, lsl #32
    // 0xeacc98: ArrayLoad: r1 = r0[r12]  ; Unknown_4
    //     0xeacc98: add             x16, x0, x12, lsl #2
    //     0xeacc9c: ldur            w1, [x16, #0xf]
    // 0xeacca0: DecompressPointer r1
    //     0xeacca0: add             x1, x1, HEAP, lsl #32
    // 0xeacca4: r0 = LoadInt32Instr(r1)
    //     0xeacca4: sbfx            x0, x1, #1, #0x1f
    //     0xeacca8: tbz             w1, #0, #0xeaccb0
    //     0xeaccac: ldur            x0, [x1, #7]
    // 0xeaccb0: orr             x8, x9, x0
    // 0xeaccb4: asr             x0, x2, #8
    // 0xeaccb8: ubfx            x0, x0, #0, #0x20
    // 0xeaccbc: and             x1, x0, x4
    // 0xeaccc0: ldur            x9, [fp, #-0x90]
    // 0xeaccc4: LoadField: r0 = r9->field_b
    //     0xeaccc4: ldur            w0, [x9, #0xb]
    // 0xeaccc8: r11 = LoadInt32Instr(r0)
    //     0xeaccc8: sbfx            x11, x0, #1, #0x1f
    // 0xeacccc: mov             x12, x1
    // 0xeaccd0: ubfx            x12, x12, #0, #0x20
    // 0xeaccd4: mov             x0, x11
    // 0xeaccd8: mov             x1, x12
    // 0xeaccdc: cmp             x1, x0
    // 0xeacce0: b.hs            #0xead054
    // 0xeacce4: LoadField: r0 = r9->field_f
    //     0xeacce4: ldur            w0, [x9, #0xf]
    // 0xeacce8: DecompressPointer r0
    //     0xeacce8: add             x0, x0, HEAP, lsl #32
    // 0xeaccec: ArrayLoad: r1 = r0[r12]  ; Unknown_4
    //     0xeaccec: add             x16, x0, x12, lsl #2
    //     0xeaccf0: ldur            w1, [x16, #0xf]
    // 0xeaccf4: DecompressPointer r1
    //     0xeaccf4: add             x1, x1, HEAP, lsl #32
    // 0xeaccf8: r0 = LoadInt32Instr(r1)
    //     0xeaccf8: sbfx            x0, x1, #1, #0x1f
    //     0xeaccfc: tbz             w1, #0, #0xeacd04
    //     0xeacd00: ldur            x0, [x1, #7]
    // 0xeacd04: orr             x9, x8, x0
    // 0xeacd08: asr             x0, x2, #0x10
    // 0xeacd0c: ubfx            x0, x0, #0, #0x20
    // 0xeacd10: and             x1, x0, x4
    // 0xeacd14: ldur            x8, [fp, #-0x98]
    // 0xeacd18: LoadField: r0 = r8->field_b
    //     0xeacd18: ldur            w0, [x8, #0xb]
    // 0xeacd1c: r11 = LoadInt32Instr(r0)
    //     0xeacd1c: sbfx            x11, x0, #1, #0x1f
    // 0xeacd20: mov             x12, x1
    // 0xeacd24: ubfx            x12, x12, #0, #0x20
    // 0xeacd28: mov             x0, x11
    // 0xeacd2c: mov             x1, x12
    // 0xeacd30: cmp             x1, x0
    // 0xeacd34: b.hs            #0xead058
    // 0xeacd38: LoadField: r0 = r8->field_f
    //     0xeacd38: ldur            w0, [x8, #0xf]
    // 0xeacd3c: DecompressPointer r0
    //     0xeacd3c: add             x0, x0, HEAP, lsl #32
    // 0xeacd40: ArrayLoad: r1 = r0[r12]  ; Unknown_4
    //     0xeacd40: add             x16, x0, x12, lsl #2
    //     0xeacd44: ldur            w1, [x16, #0xf]
    // 0xeacd48: DecompressPointer r1
    //     0xeacd48: add             x1, x1, HEAP, lsl #32
    // 0xeacd4c: r0 = LoadInt32Instr(r1)
    //     0xeacd4c: sbfx            x0, x1, #1, #0x1f
    //     0xeacd50: tbz             w1, #0, #0xeacd58
    //     0xeacd54: ldur            x0, [x1, #7]
    // 0xeacd58: orr             x8, x9, x0
    // 0xeacd5c: asr             x0, x2, #0x18
    // 0xeacd60: ubfx            x0, x0, #0, #0x20
    // 0xeacd64: and             x1, x0, x4
    // 0xeacd68: mov             x2, x1
    // 0xeacd6c: ubfx            x2, x2, #0, #0x20
    // 0xeacd70: mov             x0, x3
    // 0xeacd74: mov             x1, x2
    // 0xeacd78: cmp             x1, x0
    // 0xeacd7c: b.hs            #0xead05c
    // 0xeacd80: ArrayLoad: r0 = r6[r2]  ; Unknown_4
    //     0xeacd80: add             x16, x6, x2, lsl #2
    //     0xeacd84: ldur            w0, [x16, #0xf]
    // 0xeacd88: DecompressPointer r0
    //     0xeacd88: add             x0, x0, HEAP, lsl #32
    // 0xeacd8c: r1 = LoadInt32Instr(r0)
    //     0xeacd8c: sbfx            x1, x0, #1, #0x1f
    //     0xeacd90: tbz             w0, #0, #0xeacd98
    //     0xeacd94: ldur            x1, [x0, #7]
    // 0xeacd98: orr             x0, x8, x1
    // 0xeacd9c: ldur            x3, [fp, #-0x20]
    // 0xeacda0: eor             x6, x3, x0
    // 0xeacda4: ldur            x0, [fp, #-0x40]
    // 0xeacda8: add             x1, x0, #1
    // 0xeacdac: mov             x3, x6
    // 0xeacdb0: mov             x2, x5
    // 0xeacdb4: mov             x0, x10
    // 0xeacdb8: b               #0xeac4bc
    // 0xeacdbc: ldur            x5, [fp, #-0x30]
    // 0xeacdc0: mov             x0, x2
    // 0xeacdc4: mov             x1, x3
    // 0xeacdc8: r2 = 31
    //     0xeacdc8: movz            x2, #0x1f
    // 0xeacdcc: r0 = shiftl32()
    //     0xeacdcc: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeacdd0: mov             x1, x0
    // 0xeacdd4: ldur            x0, [fp, #-0x20]
    // 0xeacdd8: asr             x2, x0, #1
    // 0xeacddc: orr             x0, x1, x2
    // 0xeacde0: ldur            x1, [fp, #-0x38]
    // 0xeacde4: ubfx            x1, x1, #0, #0x20
    // 0xeacde8: mov             x2, x0
    // 0xeacdec: ubfx            x2, x2, #0, #0x20
    // 0xeacdf0: eor             x3, x1, x2
    // 0xeacdf4: r1 = 2863311530
    //     0xeacdf4: movz            x1, #0xaaaa
    //     0xeacdf8: movk            x1, #0xaaaa, lsl #16
    // 0xeacdfc: and             x2, x3, x1
    // 0xeace00: mov             x1, x2
    // 0xeace04: ubfx            x1, x1, #0, #0x20
    // 0xeace08: ldur            x3, [fp, #-0x38]
    // 0xeace0c: eor             x4, x3, x1
    // 0xeace10: stur            x4, [fp, #-0x40]
    // 0xeace14: ubfx            x2, x2, #0, #0x20
    // 0xeace18: eor             x3, x0, x2
    // 0xeace1c: mov             x1, x4
    // 0xeace20: stur            x3, [fp, #-0x20]
    // 0xeace24: r2 = 31
    //     0xeace24: movz            x2, #0x1f
    // 0xeace28: r0 = shiftl32()
    //     0xeace28: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeace2c: mov             x1, x0
    // 0xeace30: ldur            x0, [fp, #-0x40]
    // 0xeace34: asr             x2, x0, #1
    // 0xeace38: orr             x0, x1, x2
    // 0xeace3c: stur            x0, [fp, #-0x40]
    // 0xeace40: asr             x1, x0, #8
    // 0xeace44: ldur            x2, [fp, #-0x20]
    // 0xeace48: ubfx            x2, x2, #0, #0x20
    // 0xeace4c: ubfx            x1, x1, #0, #0x20
    // 0xeace50: eor             x3, x1, x2
    // 0xeace54: r1 = 16711935
    //     0xeace54: movz            x1, #0xff
    //     0xeace58: movk            x1, #0xff, lsl #16
    // 0xeace5c: and             x2, x3, x1
    // 0xeace60: mov             x1, x2
    // 0xeace64: ubfx            x1, x1, #0, #0x20
    // 0xeace68: ldur            x3, [fp, #-0x20]
    // 0xeace6c: eor             x4, x3, x1
    // 0xeace70: stur            x4, [fp, #-0x38]
    // 0xeace74: ubfx            x2, x2, #0, #0x20
    // 0xeace78: mov             x1, x2
    // 0xeace7c: r2 = 8
    //     0xeace7c: movz            x2, #0x8
    // 0xeace80: r0 = shiftl32()
    //     0xeace80: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeace84: mov             x1, x0
    // 0xeace88: ldur            x0, [fp, #-0x40]
    // 0xeace8c: eor             x3, x0, x1
    // 0xeace90: stur            x3, [fp, #-0x48]
    // 0xeace94: asr             x0, x3, #2
    // 0xeace98: ldur            x1, [fp, #-0x38]
    // 0xeace9c: ubfx            x1, x1, #0, #0x20
    // 0xeacea0: ubfx            x0, x0, #0, #0x20
    // 0xeacea4: eor             x2, x0, x1
    // 0xeacea8: r0 = 858993459
    //     0xeacea8: movz            x0, #0x3333
    //     0xeaceac: movk            x0, #0x3333, lsl #16
    // 0xeaceb0: and             x1, x2, x0
    // 0xeaceb4: mov             x0, x1
    // 0xeaceb8: ubfx            x0, x0, #0, #0x20
    // 0xeacebc: ldur            x2, [fp, #-0x38]
    // 0xeacec0: eor             x4, x2, x0
    // 0xeacec4: stur            x4, [fp, #-0x20]
    // 0xeacec8: ubfx            x1, x1, #0, #0x20
    // 0xeacecc: r2 = 2
    //     0xeacecc: movz            x2, #0x2
    // 0xeaced0: r0 = shiftl32()
    //     0xeaced0: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeaced4: mov             x1, x0
    // 0xeaced8: ldur            x0, [fp, #-0x48]
    // 0xeacedc: eor             x2, x0, x1
    // 0xeacee0: ldur            x0, [fp, #-0x20]
    // 0xeacee4: asr             x1, x0, #0x10
    // 0xeacee8: mov             x3, x2
    // 0xeaceec: ubfx            x3, x3, #0, #0x20
    // 0xeacef0: ubfx            x1, x1, #0, #0x20
    // 0xeacef4: eor             x4, x1, x3
    // 0xeacef8: r1 = 65535
    //     0xeacef8: orr             x1, xzr, #0xffff
    // 0xeacefc: and             x3, x4, x1
    // 0xeacf00: mov             x1, x3
    // 0xeacf04: ubfx            x1, x1, #0, #0x20
    // 0xeacf08: eor             x4, x2, x1
    // 0xeacf0c: stur            x4, [fp, #-0x38]
    // 0xeacf10: ubfx            x3, x3, #0, #0x20
    // 0xeacf14: mov             x1, x3
    // 0xeacf18: r2 = 16
    //     0xeacf18: movz            x2, #0x10
    // 0xeacf1c: r0 = shiftl32()
    //     0xeacf1c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeacf20: mov             x1, x0
    // 0xeacf24: ldur            x0, [fp, #-0x20]
    // 0xeacf28: eor             x3, x0, x1
    // 0xeacf2c: stur            x3, [fp, #-0x40]
    // 0xeacf30: asr             x0, x3, #4
    // 0xeacf34: ldur            x1, [fp, #-0x38]
    // 0xeacf38: ubfx            x1, x1, #0, #0x20
    // 0xeacf3c: ubfx            x0, x0, #0, #0x20
    // 0xeacf40: eor             x2, x0, x1
    // 0xeacf44: r0 = 252645135
    //     0xeacf44: movz            x0, #0xf0f
    //     0xeacf48: movk            x0, #0xf0f, lsl #16
    // 0xeacf4c: and             x1, x2, x0
    // 0xeacf50: mov             x0, x1
    // 0xeacf54: ubfx            x0, x0, #0, #0x20
    // 0xeacf58: ldur            x2, [fp, #-0x38]
    // 0xeacf5c: eor             x4, x2, x0
    // 0xeacf60: stur            x4, [fp, #-0x20]
    // 0xeacf64: ubfx            x1, x1, #0, #0x20
    // 0xeacf68: r2 = 4
    //     0xeacf68: movz            x2, #0x4
    // 0xeacf6c: r0 = shiftl32()
    //     0xeacf6c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xeacf70: mov             x1, x0
    // 0xeacf74: ldur            x0, [fp, #-0x40]
    // 0xeacf78: eor             x2, x0, x1
    // 0xeacf7c: ldur            x1, [fp, #-8]
    // 0xeacf80: ldur            x3, [fp, #-0x28]
    // 0xeacf84: ldur            x5, [fp, #-0x30]
    // 0xeacf88: r0 = _intToBigEndian()
    //     0xeacf88: bl              #0xead060  ; [package:pointycastle/block/des_base.dart] DesBase::_intToBigEndian
    // 0xeacf8c: ldur            x0, [fp, #-0x30]
    // 0xeacf90: add             x5, x0, #4
    // 0xeacf94: ldur            x1, [fp, #-8]
    // 0xeacf98: ldur            x2, [fp, #-0x20]
    // 0xeacf9c: ldur            x3, [fp, #-0x28]
    // 0xeacfa0: r0 = _intToBigEndian()
    //     0xeacfa0: bl              #0xead060  ; [package:pointycastle/block/des_base.dart] DesBase::_intToBigEndian
    // 0xeacfa4: r0 = Null
    //     0xeacfa4: mov             x0, NULL
    // 0xeacfa8: LeaveFrame
    //     0xeacfa8: mov             SP, fp
    //     0xeacfac: ldp             fp, lr, [SP], #0x10
    // 0xeacfb0: ret
    //     0xeacfb0: ret             
    // 0xeacfb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeacfb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeacfb8: b               #0xeac2c8
    // 0xeacfbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeacfbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeacfc0: b               #0xeac4d4
    // 0xeacfc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeacfc4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeacfc8: str             x4, [THR, #0x7a8]  ; THR::
    // 0xeacfcc: stp             x3, x4, [SP, #-0x10]!
    // 0xeacfd0: SaveReg r2
    //     0xeacfd0: str             x2, [SP, #-8]!
    // 0xeacfd4: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xeacfd8: r4 = 0
    //     0xeacfd8: movz            x4, #0
    // 0xeacfdc: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeacfe0: blr             lr
    // 0xeacfe4: brk             #0
    // 0xeacfe8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeacfe8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeacfec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeacfec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeacff0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeacff0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeacff4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeacff4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeacff8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeacff8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeacffc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeacffc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead000: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead000: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead004: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead004: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead008: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead008: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead00c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead00c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead010: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead010: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead014: str             x7, [THR, #0x7a8]  ; THR::
    // 0xead018: stp             x6, x7, [SP, #-0x10]!
    // 0xead01c: stp             x4, x5, [SP, #-0x10]!
    // 0xead020: stp             x2, x3, [SP, #-0x10]!
    // 0xead024: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xead028: r4 = 0
    //     0xead028: movz            x4, #0
    // 0xead02c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xead030: blr             lr
    // 0xead034: brk             #0
    // 0xead038: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead038: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead03c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead03c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead040: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead040: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead044: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead044: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead048: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead048: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead04c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead04c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead050: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead050: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead054: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead054: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead058: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead058: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead05c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead05c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _intToBigEndian(/* No info */) {
    // ** addr: 0xead060, size: 0xb0
    // 0xead060: EnterFrame
    //     0xead060: stp             fp, lr, [SP, #-0x10]!
    //     0xead064: mov             fp, SP
    // 0xead068: mov             x4, x2
    // 0xead06c: mov             x2, x5
    // 0xead070: asr             x5, x4, #0x18
    // 0xead074: LoadField: r6 = r3->field_13
    //     0xead074: ldur            w6, [x3, #0x13]
    // 0xead078: r7 = LoadInt32Instr(r6)
    //     0xead078: sbfx            x7, x6, #1, #0x1f
    // 0xead07c: mov             x0, x7
    // 0xead080: mov             x1, x2
    // 0xead084: cmp             x1, x0
    // 0xead088: b.hs            #0xead100
    // 0xead08c: ArrayStore: r3[r2] = r5  ; TypeUnknown_1
    //     0xead08c: add             x6, x3, x2
    //     0xead090: strb            w5, [x6, #0x17]
    // 0xead094: add             x5, x2, #1
    // 0xead098: asr             x2, x4, #0x10
    // 0xead09c: mov             x0, x7
    // 0xead0a0: mov             x1, x5
    // 0xead0a4: cmp             x1, x0
    // 0xead0a8: b.hs            #0xead104
    // 0xead0ac: ArrayStore: r3[r5] = r2  ; TypeUnknown_1
    //     0xead0ac: add             x6, x3, x5
    //     0xead0b0: strb            w2, [x6, #0x17]
    // 0xead0b4: add             x2, x5, #1
    // 0xead0b8: asr             x5, x4, #8
    // 0xead0bc: mov             x0, x7
    // 0xead0c0: mov             x1, x2
    // 0xead0c4: cmp             x1, x0
    // 0xead0c8: b.hs            #0xead108
    // 0xead0cc: ArrayStore: r3[r2] = r5  ; TypeUnknown_1
    //     0xead0cc: add             x6, x3, x2
    //     0xead0d0: strb            w5, [x6, #0x17]
    // 0xead0d4: add             x5, x2, #1
    // 0xead0d8: mov             x0, x7
    // 0xead0dc: mov             x1, x5
    // 0xead0e0: cmp             x1, x0
    // 0xead0e4: b.hs            #0xead10c
    // 0xead0e8: ArrayStore: r3[r5] = r4  ; TypeUnknown_1
    //     0xead0e8: add             x1, x3, x5
    //     0xead0ec: strb            w4, [x1, #0x17]
    // 0xead0f0: r0 = Null
    //     0xead0f0: mov             x0, NULL
    // 0xead0f4: LeaveFrame
    //     0xead0f4: mov             SP, fp
    //     0xead0f8: ldp             fp, lr, [SP], #0x10
    // 0xead0fc: ret
    //     0xead0fc: ret             
    // 0xead100: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead100: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead104: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead104: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead108: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead108: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead10c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead10c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _bigEndianToInt(/* No info */) {
    // ** addr: 0xead110, size: 0x154
    // 0xead110: EnterFrame
    //     0xead110: stp             fp, lr, [SP, #-0x10]!
    //     0xead114: mov             fp, SP
    // 0xead118: AllocStack(0x30)
    //     0xead118: sub             SP, SP, #0x30
    // 0xead11c: SetupParameters(dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xead11c: mov             x4, x2
    //     0xead120: stur            x2, [fp, #-0x10]
    //     0xead124: stur            x3, [fp, #-0x18]
    // 0xead128: CheckStackOverflow
    //     0xead128: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xead12c: cmp             SP, x16
    //     0xead130: b.ls            #0xead24c
    // 0xead134: LoadField: r0 = r4->field_13
    //     0xead134: ldur            w0, [x4, #0x13]
    // 0xead138: r5 = LoadInt32Instr(r0)
    //     0xead138: sbfx            x5, x0, #1, #0x1f
    // 0xead13c: mov             x0, x5
    // 0xead140: mov             x1, x3
    // 0xead144: stur            x5, [fp, #-8]
    // 0xead148: cmp             x1, x0
    // 0xead14c: b.hs            #0xead254
    // 0xead150: ArrayLoad: r1 = r4[r3]  ; List_1
    //     0xead150: add             x16, x4, x3
    //     0xead154: ldrb            w1, [x16, #0x17]
    // 0xead158: r2 = 24
    //     0xead158: movz            x2, #0x18
    // 0xead15c: r0 = shiftl32()
    //     0xead15c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xead160: mov             x3, x0
    // 0xead164: ldur            x0, [fp, #-0x18]
    // 0xead168: stur            x3, [fp, #-0x28]
    // 0xead16c: add             x4, x0, #1
    // 0xead170: ldur            x0, [fp, #-8]
    // 0xead174: mov             x1, x4
    // 0xead178: stur            x4, [fp, #-0x20]
    // 0xead17c: cmp             x1, x0
    // 0xead180: b.hs            #0xead258
    // 0xead184: ldur            x0, [fp, #-0x10]
    // 0xead188: ArrayLoad: r1 = r0[r4]  ; List_1
    //     0xead188: add             x16, x0, x4
    //     0xead18c: ldrb            w1, [x16, #0x17]
    // 0xead190: ubfx            x1, x1, #0, #0x20
    // 0xead194: r5 = 255
    //     0xead194: movz            x5, #0xff
    // 0xead198: and             x2, x1, x5
    // 0xead19c: ubfx            x2, x2, #0, #0x20
    // 0xead1a0: mov             x1, x2
    // 0xead1a4: r2 = 16
    //     0xead1a4: movz            x2, #0x10
    // 0xead1a8: r0 = shiftl32()
    //     0xead1a8: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xead1ac: mov             x1, x0
    // 0xead1b0: ldur            x0, [fp, #-0x28]
    // 0xead1b4: orr             x3, x0, x1
    // 0xead1b8: ldur            x0, [fp, #-0x20]
    // 0xead1bc: stur            x3, [fp, #-0x30]
    // 0xead1c0: add             x4, x0, #1
    // 0xead1c4: ldur            x0, [fp, #-8]
    // 0xead1c8: mov             x1, x4
    // 0xead1cc: stur            x4, [fp, #-0x18]
    // 0xead1d0: cmp             x1, x0
    // 0xead1d4: b.hs            #0xead25c
    // 0xead1d8: ldur            x0, [fp, #-0x10]
    // 0xead1dc: ArrayLoad: r1 = r0[r4]  ; List_1
    //     0xead1dc: add             x16, x0, x4
    //     0xead1e0: ldrb            w1, [x16, #0x17]
    // 0xead1e4: ubfx            x1, x1, #0, #0x20
    // 0xead1e8: r5 = 255
    //     0xead1e8: movz            x5, #0xff
    // 0xead1ec: and             x2, x1, x5
    // 0xead1f0: ubfx            x2, x2, #0, #0x20
    // 0xead1f4: mov             x1, x2
    // 0xead1f8: r2 = 8
    //     0xead1f8: movz            x2, #0x8
    // 0xead1fc: r0 = shiftl32()
    //     0xead1fc: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xead200: ldur            x2, [fp, #-0x30]
    // 0xead204: orr             x3, x2, x0
    // 0xead208: ldur            x2, [fp, #-0x18]
    // 0xead20c: add             x4, x2, #1
    // 0xead210: ldur            x0, [fp, #-8]
    // 0xead214: mov             x1, x4
    // 0xead218: cmp             x1, x0
    // 0xead21c: b.hs            #0xead260
    // 0xead220: ldur            x1, [fp, #-0x10]
    // 0xead224: ArrayLoad: r2 = r1[r4]  ; List_1
    //     0xead224: add             x16, x1, x4
    //     0xead228: ldrb            w2, [x16, #0x17]
    // 0xead22c: ubfx            x2, x2, #0, #0x20
    // 0xead230: r1 = 255
    //     0xead230: movz            x1, #0xff
    // 0xead234: and             x4, x2, x1
    // 0xead238: ubfx            x4, x4, #0, #0x20
    // 0xead23c: orr             x0, x3, x4
    // 0xead240: LeaveFrame
    //     0xead240: mov             SP, fp
    //     0xead244: ldp             fp, lr, [SP], #0x10
    // 0xead248: ret
    //     0xead248: ret             
    // 0xead24c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xead24c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xead250: b               #0xead134
    // 0xead254: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead254: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead258: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead258: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead25c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead25c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xead260: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xead260: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static List<int> SP2() {
    // ** addr: 0xead264, size: 0x2fc
    // 0xead264: EnterFrame
    //     0xead264: stp             fp, lr, [SP, #-0x10]!
    //     0xead268: mov             fp, SP
    // 0xead26c: AllocStack(0x8)
    //     0xead26c: sub             SP, SP, #8
    // 0xead270: r0 = 128
    //     0xead270: movz            x0, #0x80
    // 0xead274: mov             x2, x0
    // 0xead278: r1 = <int>
    //     0xead278: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xead27c: r0 = AllocateArray()
    //     0xead27c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xead280: stur            x0, [fp, #-8]
    // 0xead284: r16 = 2148565024
    //     0xead284: add             x16, PP, #0x23, lsl #12  ; [pp+0x23708] 0x80108020
    //     0xead288: ldr             x16, [x16, #0x708]
    // 0xead28c: StoreField: r0->field_f = r16
    //     0xead28c: stur            w16, [x0, #0xf]
    // 0xead290: r16 = 2147516416
    //     0xead290: add             x16, PP, #0x23, lsl #12  ; [pp+0x23710] 0x80008000
    //     0xead294: ldr             x16, [x16, #0x710]
    // 0xead298: StoreField: r0->field_13 = r16
    //     0xead298: stur            w16, [x0, #0x13]
    // 0xead29c: r16 = 1
    //     0xead29c: movz            x16, #0x1, lsl #16
    // 0xead2a0: ArrayStore: r0[0] = r16  ; List_4
    //     0xead2a0: stur            w16, [x0, #0x17]
    // 0xead2a4: r16 = 2162752
    //     0xead2a4: movz            x16, #0x40
    //     0xead2a8: movk            x16, #0x21, lsl #16
    // 0xead2ac: StoreField: r0->field_1b = r16
    //     0xead2ac: stur            w16, [x0, #0x1b]
    // 0xead2b0: r16 = 32
    //     0xead2b0: movz            x16, #0x20, lsl #16
    // 0xead2b4: StoreField: r0->field_1f = r16
    //     0xead2b4: stur            w16, [x0, #0x1f]
    // 0xead2b8: r16 = 64
    //     0xead2b8: movz            x16, #0x40
    // 0xead2bc: StoreField: r0->field_23 = r16
    //     0xead2bc: stur            w16, [x0, #0x23]
    // 0xead2c0: r16 = 2148532256
    //     0xead2c0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23718] 0x80100020
    //     0xead2c4: ldr             x16, [x16, #0x718]
    // 0xead2c8: StoreField: r0->field_27 = r16
    //     0xead2c8: stur            w16, [x0, #0x27]
    // 0xead2cc: r16 = 2147516448
    //     0xead2cc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23720] 0x80008020
    //     0xead2d0: ldr             x16, [x16, #0x720]
    // 0xead2d4: StoreField: r0->field_2b = r16
    //     0xead2d4: stur            w16, [x0, #0x2b]
    // 0xead2d8: r16 = 2147483680
    //     0xead2d8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23728] 0x80000020
    //     0xead2dc: ldr             x16, [x16, #0x728]
    // 0xead2e0: StoreField: r0->field_2f = r16
    //     0xead2e0: stur            w16, [x0, #0x2f]
    // 0xead2e4: r16 = 2148565024
    //     0xead2e4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23708] 0x80108020
    //     0xead2e8: ldr             x16, [x16, #0x708]
    // 0xead2ec: StoreField: r0->field_33 = r16
    //     0xead2ec: stur            w16, [x0, #0x33]
    // 0xead2f0: r16 = 2148564992
    //     0xead2f0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23730] 0x80108000
    //     0xead2f4: ldr             x16, [x16, #0x730]
    // 0xead2f8: StoreField: r0->field_37 = r16
    //     0xead2f8: stur            w16, [x0, #0x37]
    // 0xead2fc: r16 = 2147483648
    //     0xead2fc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23738] 0x80000000
    //     0xead300: ldr             x16, [x16, #0x738]
    // 0xead304: StoreField: r0->field_3b = r16
    //     0xead304: stur            w16, [x0, #0x3b]
    // 0xead308: r16 = 2147516416
    //     0xead308: add             x16, PP, #0x23, lsl #12  ; [pp+0x23710] 0x80008000
    //     0xead30c: ldr             x16, [x16, #0x710]
    // 0xead310: StoreField: r0->field_3f = r16
    //     0xead310: stur            w16, [x0, #0x3f]
    // 0xead314: r16 = 32
    //     0xead314: movz            x16, #0x20, lsl #16
    // 0xead318: StoreField: r0->field_43 = r16
    //     0xead318: stur            w16, [x0, #0x43]
    // 0xead31c: r16 = 64
    //     0xead31c: movz            x16, #0x40
    // 0xead320: StoreField: r0->field_47 = r16
    //     0xead320: stur            w16, [x0, #0x47]
    // 0xead324: r16 = 2148532256
    //     0xead324: add             x16, PP, #0x23, lsl #12  ; [pp+0x23718] 0x80100020
    //     0xead328: ldr             x16, [x16, #0x718]
    // 0xead32c: StoreField: r0->field_4b = r16
    //     0xead32c: stur            w16, [x0, #0x4b]
    // 0xead330: r16 = 33
    //     0xead330: movz            x16, #0x21, lsl #16
    // 0xead334: StoreField: r0->field_4f = r16
    //     0xead334: stur            w16, [x0, #0x4f]
    // 0xead338: r16 = 2097216
    //     0xead338: movz            x16, #0x40
    //     0xead33c: movk            x16, #0x20, lsl #16
    // 0xead340: StoreField: r0->field_53 = r16
    //     0xead340: stur            w16, [x0, #0x53]
    // 0xead344: r16 = 2147516448
    //     0xead344: add             x16, PP, #0x23, lsl #12  ; [pp+0x23720] 0x80008020
    //     0xead348: ldr             x16, [x16, #0x720]
    // 0xead34c: StoreField: r0->field_57 = r16
    //     0xead34c: stur            w16, [x0, #0x57]
    // 0xead350: StoreField: r0->field_5b = rZR
    //     0xead350: stur            wzr, [x0, #0x5b]
    // 0xead354: r16 = 2147483648
    //     0xead354: add             x16, PP, #0x23, lsl #12  ; [pp+0x23738] 0x80000000
    //     0xead358: ldr             x16, [x16, #0x738]
    // 0xead35c: StoreField: r0->field_5f = r16
    //     0xead35c: stur            w16, [x0, #0x5f]
    // 0xead360: r16 = 1
    //     0xead360: movz            x16, #0x1, lsl #16
    // 0xead364: StoreField: r0->field_63 = r16
    //     0xead364: stur            w16, [x0, #0x63]
    // 0xead368: r16 = 2162752
    //     0xead368: movz            x16, #0x40
    //     0xead36c: movk            x16, #0x21, lsl #16
    // 0xead370: StoreField: r0->field_67 = r16
    //     0xead370: stur            w16, [x0, #0x67]
    // 0xead374: r16 = 2148532224
    //     0xead374: add             x16, PP, #0x23, lsl #12  ; [pp+0x23740] 0x80100000
    //     0xead378: ldr             x16, [x16, #0x740]
    // 0xead37c: StoreField: r0->field_6b = r16
    //     0xead37c: stur            w16, [x0, #0x6b]
    // 0xead380: r16 = 2097216
    //     0xead380: movz            x16, #0x40
    //     0xead384: movk            x16, #0x20, lsl #16
    // 0xead388: StoreField: r0->field_6f = r16
    //     0xead388: stur            w16, [x0, #0x6f]
    // 0xead38c: r16 = 2147483680
    //     0xead38c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23728] 0x80000020
    //     0xead390: ldr             x16, [x16, #0x728]
    // 0xead394: StoreField: r0->field_73 = r16
    //     0xead394: stur            w16, [x0, #0x73]
    // 0xead398: StoreField: r0->field_77 = rZR
    //     0xead398: stur            wzr, [x0, #0x77]
    // 0xead39c: r16 = 33
    //     0xead39c: movz            x16, #0x21, lsl #16
    // 0xead3a0: StoreField: r0->field_7b = r16
    //     0xead3a0: stur            w16, [x0, #0x7b]
    // 0xead3a4: r16 = 65600
    //     0xead3a4: movz            x16, #0x40
    //     0xead3a8: movk            x16, #0x1, lsl #16
    // 0xead3ac: StoreField: r0->field_7f = r16
    //     0xead3ac: stur            w16, [x0, #0x7f]
    // 0xead3b0: r16 = 2148564992
    //     0xead3b0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23730] 0x80108000
    //     0xead3b4: ldr             x16, [x16, #0x730]
    // 0xead3b8: StoreField: r0->field_83 = r16
    //     0xead3b8: stur            w16, [x0, #0x83]
    // 0xead3bc: r16 = 2148532224
    //     0xead3bc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23740] 0x80100000
    //     0xead3c0: ldr             x16, [x16, #0x740]
    // 0xead3c4: StoreField: r0->field_87 = r16
    //     0xead3c4: stur            w16, [x0, #0x87]
    // 0xead3c8: r16 = 65600
    //     0xead3c8: movz            x16, #0x40
    //     0xead3cc: movk            x16, #0x1, lsl #16
    // 0xead3d0: StoreField: r0->field_8b = r16
    //     0xead3d0: stur            w16, [x0, #0x8b]
    // 0xead3d4: StoreField: r0->field_8f = rZR
    //     0xead3d4: stur            wzr, [x0, #0x8f]
    // 0xead3d8: r16 = 2162752
    //     0xead3d8: movz            x16, #0x40
    //     0xead3dc: movk            x16, #0x21, lsl #16
    // 0xead3e0: StoreField: r0->field_93 = r16
    //     0xead3e0: stur            w16, [x0, #0x93]
    // 0xead3e4: r16 = 2148532256
    //     0xead3e4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23718] 0x80100020
    //     0xead3e8: ldr             x16, [x16, #0x718]
    // 0xead3ec: StoreField: r0->field_97 = r16
    //     0xead3ec: stur            w16, [x0, #0x97]
    // 0xead3f0: r16 = 32
    //     0xead3f0: movz            x16, #0x20, lsl #16
    // 0xead3f4: StoreField: r0->field_9b = r16
    //     0xead3f4: stur            w16, [x0, #0x9b]
    // 0xead3f8: r16 = 2147516448
    //     0xead3f8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23720] 0x80008020
    //     0xead3fc: ldr             x16, [x16, #0x720]
    // 0xead400: StoreField: r0->field_9f = r16
    //     0xead400: stur            w16, [x0, #0x9f]
    // 0xead404: r16 = 2148532224
    //     0xead404: add             x16, PP, #0x23, lsl #12  ; [pp+0x23740] 0x80100000
    //     0xead408: ldr             x16, [x16, #0x740]
    // 0xead40c: StoreField: r0->field_a3 = r16
    //     0xead40c: stur            w16, [x0, #0xa3]
    // 0xead410: r16 = 2148564992
    //     0xead410: add             x16, PP, #0x23, lsl #12  ; [pp+0x23730] 0x80108000
    //     0xead414: ldr             x16, [x16, #0x730]
    // 0xead418: StoreField: r0->field_a7 = r16
    //     0xead418: stur            w16, [x0, #0xa7]
    // 0xead41c: r16 = 1
    //     0xead41c: movz            x16, #0x1, lsl #16
    // 0xead420: StoreField: r0->field_ab = r16
    //     0xead420: stur            w16, [x0, #0xab]
    // 0xead424: r16 = 2148532224
    //     0xead424: add             x16, PP, #0x23, lsl #12  ; [pp+0x23740] 0x80100000
    //     0xead428: ldr             x16, [x16, #0x740]
    // 0xead42c: StoreField: r0->field_af = r16
    //     0xead42c: stur            w16, [x0, #0xaf]
    // 0xead430: r16 = 2147516416
    //     0xead430: add             x16, PP, #0x23, lsl #12  ; [pp+0x23710] 0x80008000
    //     0xead434: ldr             x16, [x16, #0x710]
    // 0xead438: StoreField: r0->field_b3 = r16
    //     0xead438: stur            w16, [x0, #0xb3]
    // 0xead43c: r16 = 64
    //     0xead43c: movz            x16, #0x40
    // 0xead440: StoreField: r0->field_b7 = r16
    //     0xead440: stur            w16, [x0, #0xb7]
    // 0xead444: r16 = 2148565024
    //     0xead444: add             x16, PP, #0x23, lsl #12  ; [pp+0x23708] 0x80108020
    //     0xead448: ldr             x16, [x16, #0x708]
    // 0xead44c: StoreField: r0->field_bb = r16
    //     0xead44c: stur            w16, [x0, #0xbb]
    // 0xead450: r16 = 2162752
    //     0xead450: movz            x16, #0x40
    //     0xead454: movk            x16, #0x21, lsl #16
    // 0xead458: StoreField: r0->field_bf = r16
    //     0xead458: stur            w16, [x0, #0xbf]
    // 0xead45c: r16 = 64
    //     0xead45c: movz            x16, #0x40
    // 0xead460: StoreField: r0->field_c3 = r16
    //     0xead460: stur            w16, [x0, #0xc3]
    // 0xead464: r16 = 1
    //     0xead464: movz            x16, #0x1, lsl #16
    // 0xead468: StoreField: r0->field_c7 = r16
    //     0xead468: stur            w16, [x0, #0xc7]
    // 0xead46c: r16 = 2147483648
    //     0xead46c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23738] 0x80000000
    //     0xead470: ldr             x16, [x16, #0x738]
    // 0xead474: StoreField: r0->field_cb = r16
    //     0xead474: stur            w16, [x0, #0xcb]
    // 0xead478: r16 = 65600
    //     0xead478: movz            x16, #0x40
    //     0xead47c: movk            x16, #0x1, lsl #16
    // 0xead480: StoreField: r0->field_cf = r16
    //     0xead480: stur            w16, [x0, #0xcf]
    // 0xead484: r16 = 2148564992
    //     0xead484: add             x16, PP, #0x23, lsl #12  ; [pp+0x23730] 0x80108000
    //     0xead488: ldr             x16, [x16, #0x730]
    // 0xead48c: StoreField: r0->field_d3 = r16
    //     0xead48c: stur            w16, [x0, #0xd3]
    // 0xead490: r16 = 32
    //     0xead490: movz            x16, #0x20, lsl #16
    // 0xead494: StoreField: r0->field_d7 = r16
    //     0xead494: stur            w16, [x0, #0xd7]
    // 0xead498: r16 = 2147483680
    //     0xead498: add             x16, PP, #0x23, lsl #12  ; [pp+0x23728] 0x80000020
    //     0xead49c: ldr             x16, [x16, #0x728]
    // 0xead4a0: StoreField: r0->field_db = r16
    //     0xead4a0: stur            w16, [x0, #0xdb]
    // 0xead4a4: r16 = 2097216
    //     0xead4a4: movz            x16, #0x40
    //     0xead4a8: movk            x16, #0x20, lsl #16
    // 0xead4ac: StoreField: r0->field_df = r16
    //     0xead4ac: stur            w16, [x0, #0xdf]
    // 0xead4b0: r16 = 2147516448
    //     0xead4b0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23720] 0x80008020
    //     0xead4b4: ldr             x16, [x16, #0x720]
    // 0xead4b8: StoreField: r0->field_e3 = r16
    //     0xead4b8: stur            w16, [x0, #0xe3]
    // 0xead4bc: r16 = 2147483680
    //     0xead4bc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23728] 0x80000020
    //     0xead4c0: ldr             x16, [x16, #0x728]
    // 0xead4c4: StoreField: r0->field_e7 = r16
    //     0xead4c4: stur            w16, [x0, #0xe7]
    // 0xead4c8: r16 = 2097216
    //     0xead4c8: movz            x16, #0x40
    //     0xead4cc: movk            x16, #0x20, lsl #16
    // 0xead4d0: StoreField: r0->field_eb = r16
    //     0xead4d0: stur            w16, [x0, #0xeb]
    // 0xead4d4: r16 = 33
    //     0xead4d4: movz            x16, #0x21, lsl #16
    // 0xead4d8: StoreField: r0->field_ef = r16
    //     0xead4d8: stur            w16, [x0, #0xef]
    // 0xead4dc: StoreField: r0->field_f3 = rZR
    //     0xead4dc: stur            wzr, [x0, #0xf3]
    // 0xead4e0: r16 = 2147516416
    //     0xead4e0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23710] 0x80008000
    //     0xead4e4: ldr             x16, [x16, #0x710]
    // 0xead4e8: StoreField: r0->field_f7 = r16
    //     0xead4e8: stur            w16, [x0, #0xf7]
    // 0xead4ec: r16 = 65600
    //     0xead4ec: movz            x16, #0x40
    //     0xead4f0: movk            x16, #0x1, lsl #16
    // 0xead4f4: StoreField: r0->field_fb = r16
    //     0xead4f4: stur            w16, [x0, #0xfb]
    // 0xead4f8: r16 = 2147483648
    //     0xead4f8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23738] 0x80000000
    //     0xead4fc: ldr             x16, [x16, #0x738]
    // 0xead500: StoreField: r0->field_ff = r16
    //     0xead500: stur            w16, [x0, #0xff]
    // 0xead504: r1 = 122
    //     0xead504: movz            x1, #0x7a
    // 0xead508: add             x2, x0, w1, sxtw #1
    // 0xead50c: r16 = 2148532256
    //     0xead50c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23718] 0x80100020
    //     0xead510: ldr             x16, [x16, #0x718]
    // 0xead514: StoreField: r2->field_f = r16
    //     0xead514: stur            w16, [x2, #0xf]
    // 0xead518: r1 = 124
    //     0xead518: movz            x1, #0x7c
    // 0xead51c: add             x2, x0, w1, sxtw #1
    // 0xead520: r16 = 2148565024
    //     0xead520: add             x16, PP, #0x23, lsl #12  ; [pp+0x23708] 0x80108020
    //     0xead524: ldr             x16, [x16, #0x708]
    // 0xead528: StoreField: r2->field_f = r16
    //     0xead528: stur            w16, [x2, #0xf]
    // 0xead52c: r1 = 126
    //     0xead52c: movz            x1, #0x7e
    // 0xead530: add             x2, x0, w1, sxtw #1
    // 0xead534: r16 = 33
    //     0xead534: movz            x16, #0x21, lsl #16
    // 0xead538: StoreField: r2->field_f = r16
    //     0xead538: stur            w16, [x2, #0xf]
    // 0xead53c: r1 = <int>
    //     0xead53c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xead540: r0 = AllocateGrowableArray()
    //     0xead540: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xead544: ldur            x1, [fp, #-8]
    // 0xead548: StoreField: r0->field_f = r1
    //     0xead548: stur            w1, [x0, #0xf]
    // 0xead54c: r1 = 128
    //     0xead54c: movz            x1, #0x80
    // 0xead550: StoreField: r0->field_b = r1
    //     0xead550: stur            w1, [x0, #0xb]
    // 0xead554: LeaveFrame
    //     0xead554: mov             SP, fp
    //     0xead558: ldp             fp, lr, [SP], #0x10
    // 0xead55c: ret
    //     0xead55c: ret             
  }
  static List<int> SP4() {
    // ** addr: 0xead560, size: 0x2bc
    // 0xead560: EnterFrame
    //     0xead560: stp             fp, lr, [SP, #-0x10]!
    //     0xead564: mov             fp, SP
    // 0xead568: AllocStack(0x8)
    //     0xead568: sub             SP, SP, #8
    // 0xead56c: r0 = 128
    //     0xead56c: movz            x0, #0x80
    // 0xead570: mov             x2, x0
    // 0xead574: r1 = <int>
    //     0xead574: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xead578: r0 = AllocateArray()
    //     0xead578: bl              #0xec22fc  ; AllocateArrayStub
    // 0xead57c: stur            x0, [fp, #-8]
    // 0xead580: r16 = 16793602
    //     0xead580: movz            x16, #0x4002
    //     0xead584: movk            x16, #0x100, lsl #16
    // 0xead588: StoreField: r0->field_f = r16
    //     0xead588: stur            w16, [x0, #0xf]
    // 0xead58c: r16 = 16642
    //     0xead58c: movz            x16, #0x4102
    // 0xead590: StoreField: r0->field_13 = r16
    //     0xead590: stur            w16, [x0, #0x13]
    // 0xead594: r16 = 16642
    //     0xead594: movz            x16, #0x4102
    // 0xead598: ArrayStore: r0[0] = r16  ; List_4
    //     0xead598: stur            w16, [x0, #0x17]
    // 0xead59c: r16 = 256
    //     0xead59c: movz            x16, #0x100
    // 0xead5a0: StoreField: r0->field_1b = r16
    //     0xead5a0: stur            w16, [x0, #0x1b]
    // 0xead5a4: r16 = 16793856
    //     0xead5a4: movz            x16, #0x4100
    //     0xead5a8: movk            x16, #0x100, lsl #16
    // 0xead5ac: StoreField: r0->field_1f = r16
    //     0xead5ac: stur            w16, [x0, #0x1f]
    // 0xead5b0: r16 = 16777474
    //     0xead5b0: movz            x16, #0x102
    //     0xead5b4: movk            x16, #0x100, lsl #16
    // 0xead5b8: StoreField: r0->field_23 = r16
    //     0xead5b8: stur            w16, [x0, #0x23]
    // 0xead5bc: r16 = 16777218
    //     0xead5bc: movz            x16, #0x2
    //     0xead5c0: movk            x16, #0x100, lsl #16
    // 0xead5c4: StoreField: r0->field_27 = r16
    //     0xead5c4: stur            w16, [x0, #0x27]
    // 0xead5c8: r16 = 16386
    //     0xead5c8: movz            x16, #0x4002
    // 0xead5cc: StoreField: r0->field_2b = r16
    //     0xead5cc: stur            w16, [x0, #0x2b]
    // 0xead5d0: StoreField: r0->field_2f = rZR
    //     0xead5d0: stur            wzr, [x0, #0x2f]
    // 0xead5d4: r16 = 16793600
    //     0xead5d4: movz            x16, #0x4000
    //     0xead5d8: movk            x16, #0x100, lsl #16
    // 0xead5dc: StoreField: r0->field_33 = r16
    //     0xead5dc: stur            w16, [x0, #0x33]
    // 0xead5e0: r16 = 16793600
    //     0xead5e0: movz            x16, #0x4000
    //     0xead5e4: movk            x16, #0x100, lsl #16
    // 0xead5e8: StoreField: r0->field_37 = r16
    //     0xead5e8: stur            w16, [x0, #0x37]
    // 0xead5ec: r16 = 16793858
    //     0xead5ec: movz            x16, #0x4102
    //     0xead5f0: movk            x16, #0x100, lsl #16
    // 0xead5f4: StoreField: r0->field_3b = r16
    //     0xead5f4: stur            w16, [x0, #0x3b]
    // 0xead5f8: r16 = 258
    //     0xead5f8: movz            x16, #0x102
    // 0xead5fc: StoreField: r0->field_3f = r16
    //     0xead5fc: stur            w16, [x0, #0x3f]
    // 0xead600: StoreField: r0->field_43 = rZR
    //     0xead600: stur            wzr, [x0, #0x43]
    // 0xead604: r16 = 16777472
    //     0xead604: movz            x16, #0x100
    //     0xead608: movk            x16, #0x100, lsl #16
    // 0xead60c: StoreField: r0->field_47 = r16
    //     0xead60c: stur            w16, [x0, #0x47]
    // 0xead610: r16 = 16777218
    //     0xead610: movz            x16, #0x2
    //     0xead614: movk            x16, #0x100, lsl #16
    // 0xead618: StoreField: r0->field_4b = r16
    //     0xead618: stur            w16, [x0, #0x4b]
    // 0xead61c: r16 = 2
    //     0xead61c: movz            x16, #0x2
    // 0xead620: StoreField: r0->field_4f = r16
    //     0xead620: stur            w16, [x0, #0x4f]
    // 0xead624: r16 = 16384
    //     0xead624: movz            x16, #0x4000
    // 0xead628: StoreField: r0->field_53 = r16
    //     0xead628: stur            w16, [x0, #0x53]
    // 0xead62c: r16 = 16777216
    //     0xead62c: orr             x16, xzr, #0x1000000
    // 0xead630: StoreField: r0->field_57 = r16
    //     0xead630: stur            w16, [x0, #0x57]
    // 0xead634: r16 = 16793602
    //     0xead634: movz            x16, #0x4002
    //     0xead638: movk            x16, #0x100, lsl #16
    // 0xead63c: StoreField: r0->field_5b = r16
    //     0xead63c: stur            w16, [x0, #0x5b]
    // 0xead640: r16 = 256
    //     0xead640: movz            x16, #0x100
    // 0xead644: StoreField: r0->field_5f = r16
    //     0xead644: stur            w16, [x0, #0x5f]
    // 0xead648: r16 = 16777216
    //     0xead648: orr             x16, xzr, #0x1000000
    // 0xead64c: StoreField: r0->field_63 = r16
    //     0xead64c: stur            w16, [x0, #0x63]
    // 0xead650: r16 = 16386
    //     0xead650: movz            x16, #0x4002
    // 0xead654: StoreField: r0->field_67 = r16
    //     0xead654: stur            w16, [x0, #0x67]
    // 0xead658: r16 = 16640
    //     0xead658: movz            x16, #0x4100
    // 0xead65c: StoreField: r0->field_6b = r16
    //     0xead65c: stur            w16, [x0, #0x6b]
    // 0xead660: r16 = 16777474
    //     0xead660: movz            x16, #0x102
    //     0xead664: movk            x16, #0x100, lsl #16
    // 0xead668: StoreField: r0->field_6f = r16
    //     0xead668: stur            w16, [x0, #0x6f]
    // 0xead66c: r16 = 2
    //     0xead66c: movz            x16, #0x2
    // 0xead670: StoreField: r0->field_73 = r16
    //     0xead670: stur            w16, [x0, #0x73]
    // 0xead674: r16 = 16640
    //     0xead674: movz            x16, #0x4100
    // 0xead678: StoreField: r0->field_77 = r16
    //     0xead678: stur            w16, [x0, #0x77]
    // 0xead67c: r16 = 16777472
    //     0xead67c: movz            x16, #0x100
    //     0xead680: movk            x16, #0x100, lsl #16
    // 0xead684: StoreField: r0->field_7b = r16
    //     0xead684: stur            w16, [x0, #0x7b]
    // 0xead688: r16 = 16384
    //     0xead688: movz            x16, #0x4000
    // 0xead68c: StoreField: r0->field_7f = r16
    //     0xead68c: stur            w16, [x0, #0x7f]
    // 0xead690: r16 = 16793856
    //     0xead690: movz            x16, #0x4100
    //     0xead694: movk            x16, #0x100, lsl #16
    // 0xead698: StoreField: r0->field_83 = r16
    //     0xead698: stur            w16, [x0, #0x83]
    // 0xead69c: r16 = 16793858
    //     0xead69c: movz            x16, #0x4102
    //     0xead6a0: movk            x16, #0x100, lsl #16
    // 0xead6a4: StoreField: r0->field_87 = r16
    //     0xead6a4: stur            w16, [x0, #0x87]
    // 0xead6a8: r16 = 258
    //     0xead6a8: movz            x16, #0x102
    // 0xead6ac: StoreField: r0->field_8b = r16
    //     0xead6ac: stur            w16, [x0, #0x8b]
    // 0xead6b0: r16 = 16777472
    //     0xead6b0: movz            x16, #0x100
    //     0xead6b4: movk            x16, #0x100, lsl #16
    // 0xead6b8: StoreField: r0->field_8f = r16
    //     0xead6b8: stur            w16, [x0, #0x8f]
    // 0xead6bc: r16 = 16777218
    //     0xead6bc: movz            x16, #0x2
    //     0xead6c0: movk            x16, #0x100, lsl #16
    // 0xead6c4: StoreField: r0->field_93 = r16
    //     0xead6c4: stur            w16, [x0, #0x93]
    // 0xead6c8: r16 = 16793600
    //     0xead6c8: movz            x16, #0x4000
    //     0xead6cc: movk            x16, #0x100, lsl #16
    // 0xead6d0: StoreField: r0->field_97 = r16
    //     0xead6d0: stur            w16, [x0, #0x97]
    // 0xead6d4: r16 = 16793858
    //     0xead6d4: movz            x16, #0x4102
    //     0xead6d8: movk            x16, #0x100, lsl #16
    // 0xead6dc: StoreField: r0->field_9b = r16
    //     0xead6dc: stur            w16, [x0, #0x9b]
    // 0xead6e0: r16 = 258
    //     0xead6e0: movz            x16, #0x102
    // 0xead6e4: StoreField: r0->field_9f = r16
    //     0xead6e4: stur            w16, [x0, #0x9f]
    // 0xead6e8: StoreField: r0->field_a3 = rZR
    //     0xead6e8: stur            wzr, [x0, #0xa3]
    // 0xead6ec: StoreField: r0->field_a7 = rZR
    //     0xead6ec: stur            wzr, [x0, #0xa7]
    // 0xead6f0: r16 = 16793600
    //     0xead6f0: movz            x16, #0x4000
    //     0xead6f4: movk            x16, #0x100, lsl #16
    // 0xead6f8: StoreField: r0->field_ab = r16
    //     0xead6f8: stur            w16, [x0, #0xab]
    // 0xead6fc: r16 = 16640
    //     0xead6fc: movz            x16, #0x4100
    // 0xead700: StoreField: r0->field_af = r16
    //     0xead700: stur            w16, [x0, #0xaf]
    // 0xead704: r16 = 16777472
    //     0xead704: movz            x16, #0x100
    //     0xead708: movk            x16, #0x100, lsl #16
    // 0xead70c: StoreField: r0->field_b3 = r16
    //     0xead70c: stur            w16, [x0, #0xb3]
    // 0xead710: r16 = 16777474
    //     0xead710: movz            x16, #0x102
    //     0xead714: movk            x16, #0x100, lsl #16
    // 0xead718: StoreField: r0->field_b7 = r16
    //     0xead718: stur            w16, [x0, #0xb7]
    // 0xead71c: r16 = 2
    //     0xead71c: movz            x16, #0x2
    // 0xead720: StoreField: r0->field_bb = r16
    //     0xead720: stur            w16, [x0, #0xbb]
    // 0xead724: r16 = 16793602
    //     0xead724: movz            x16, #0x4002
    //     0xead728: movk            x16, #0x100, lsl #16
    // 0xead72c: StoreField: r0->field_bf = r16
    //     0xead72c: stur            w16, [x0, #0xbf]
    // 0xead730: r16 = 16642
    //     0xead730: movz            x16, #0x4102
    // 0xead734: StoreField: r0->field_c3 = r16
    //     0xead734: stur            w16, [x0, #0xc3]
    // 0xead738: r16 = 16642
    //     0xead738: movz            x16, #0x4102
    // 0xead73c: StoreField: r0->field_c7 = r16
    //     0xead73c: stur            w16, [x0, #0xc7]
    // 0xead740: r16 = 256
    //     0xead740: movz            x16, #0x100
    // 0xead744: StoreField: r0->field_cb = r16
    //     0xead744: stur            w16, [x0, #0xcb]
    // 0xead748: r16 = 16793858
    //     0xead748: movz            x16, #0x4102
    //     0xead74c: movk            x16, #0x100, lsl #16
    // 0xead750: StoreField: r0->field_cf = r16
    //     0xead750: stur            w16, [x0, #0xcf]
    // 0xead754: r16 = 258
    //     0xead754: movz            x16, #0x102
    // 0xead758: StoreField: r0->field_d3 = r16
    //     0xead758: stur            w16, [x0, #0xd3]
    // 0xead75c: r16 = 2
    //     0xead75c: movz            x16, #0x2
    // 0xead760: StoreField: r0->field_d7 = r16
    //     0xead760: stur            w16, [x0, #0xd7]
    // 0xead764: r16 = 16384
    //     0xead764: movz            x16, #0x4000
    // 0xead768: StoreField: r0->field_db = r16
    //     0xead768: stur            w16, [x0, #0xdb]
    // 0xead76c: r16 = 16777218
    //     0xead76c: movz            x16, #0x2
    //     0xead770: movk            x16, #0x100, lsl #16
    // 0xead774: StoreField: r0->field_df = r16
    //     0xead774: stur            w16, [x0, #0xdf]
    // 0xead778: r16 = 16386
    //     0xead778: movz            x16, #0x4002
    // 0xead77c: StoreField: r0->field_e3 = r16
    //     0xead77c: stur            w16, [x0, #0xe3]
    // 0xead780: r16 = 16793856
    //     0xead780: movz            x16, #0x4100
    //     0xead784: movk            x16, #0x100, lsl #16
    // 0xead788: StoreField: r0->field_e7 = r16
    //     0xead788: stur            w16, [x0, #0xe7]
    // 0xead78c: r16 = 16777474
    //     0xead78c: movz            x16, #0x102
    //     0xead790: movk            x16, #0x100, lsl #16
    // 0xead794: StoreField: r0->field_eb = r16
    //     0xead794: stur            w16, [x0, #0xeb]
    // 0xead798: r16 = 16386
    //     0xead798: movz            x16, #0x4002
    // 0xead79c: StoreField: r0->field_ef = r16
    //     0xead79c: stur            w16, [x0, #0xef]
    // 0xead7a0: r16 = 16640
    //     0xead7a0: movz            x16, #0x4100
    // 0xead7a4: StoreField: r0->field_f3 = r16
    //     0xead7a4: stur            w16, [x0, #0xf3]
    // 0xead7a8: r16 = 16777216
    //     0xead7a8: orr             x16, xzr, #0x1000000
    // 0xead7ac: StoreField: r0->field_f7 = r16
    //     0xead7ac: stur            w16, [x0, #0xf7]
    // 0xead7b0: r16 = 16793602
    //     0xead7b0: movz            x16, #0x4002
    //     0xead7b4: movk            x16, #0x100, lsl #16
    // 0xead7b8: StoreField: r0->field_fb = r16
    //     0xead7b8: stur            w16, [x0, #0xfb]
    // 0xead7bc: r16 = 256
    //     0xead7bc: movz            x16, #0x100
    // 0xead7c0: StoreField: r0->field_ff = r16
    //     0xead7c0: stur            w16, [x0, #0xff]
    // 0xead7c4: r1 = 122
    //     0xead7c4: movz            x1, #0x7a
    // 0xead7c8: add             x2, x0, w1, sxtw #1
    // 0xead7cc: r16 = 16777216
    //     0xead7cc: orr             x16, xzr, #0x1000000
    // 0xead7d0: StoreField: r2->field_f = r16
    //     0xead7d0: stur            w16, [x2, #0xf]
    // 0xead7d4: r1 = 124
    //     0xead7d4: movz            x1, #0x7c
    // 0xead7d8: add             x2, x0, w1, sxtw #1
    // 0xead7dc: r16 = 16384
    //     0xead7dc: movz            x16, #0x4000
    // 0xead7e0: StoreField: r2->field_f = r16
    //     0xead7e0: stur            w16, [x2, #0xf]
    // 0xead7e4: r1 = 126
    //     0xead7e4: movz            x1, #0x7e
    // 0xead7e8: add             x2, x0, w1, sxtw #1
    // 0xead7ec: r16 = 16793856
    //     0xead7ec: movz            x16, #0x4100
    //     0xead7f0: movk            x16, #0x100, lsl #16
    // 0xead7f4: StoreField: r2->field_f = r16
    //     0xead7f4: stur            w16, [x2, #0xf]
    // 0xead7f8: r1 = <int>
    //     0xead7f8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xead7fc: r0 = AllocateGrowableArray()
    //     0xead7fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xead800: ldur            x1, [fp, #-8]
    // 0xead804: StoreField: r0->field_f = r1
    //     0xead804: stur            w1, [x0, #0xf]
    // 0xead808: r1 = 128
    //     0xead808: movz            x1, #0x80
    // 0xead80c: StoreField: r0->field_b = r1
    //     0xead80c: stur            w1, [x0, #0xb]
    // 0xead810: LeaveFrame
    //     0xead810: mov             SP, fp
    //     0xead814: ldp             fp, lr, [SP], #0x10
    // 0xead818: ret
    //     0xead818: ret             
  }
  static List<int> SP6() {
    // ** addr: 0xead81c, size: 0x2dc
    // 0xead81c: EnterFrame
    //     0xead81c: stp             fp, lr, [SP, #-0x10]!
    //     0xead820: mov             fp, SP
    // 0xead824: AllocStack(0x8)
    //     0xead824: sub             SP, SP, #8
    // 0xead828: r0 = 128
    //     0xead828: movz            x0, #0x80
    // 0xead82c: mov             x2, x0
    // 0xead830: r1 = <int>
    //     0xead830: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xead834: r0 = AllocateArray()
    //     0xead834: bl              #0xec22fc  ; AllocateArrayStub
    // 0xead838: stur            x0, [fp, #-8]
    // 0xead83c: r16 = 1073741856
    //     0xead83c: movz            x16, #0x20
    //     0xead840: movk            x16, #0x4000, lsl #16
    // 0xead844: StoreField: r0->field_f = r16
    //     0xead844: stur            w16, [x0, #0xf]
    // 0xead848: r16 = 16512
    //     0xead848: movz            x16, #0x4080, lsl #16
    // 0xead84c: StoreField: r0->field_13 = r16
    //     0xead84c: stur            w16, [x0, #0x13]
    // 0xead850: r16 = 32768
    //     0xead850: movz            x16, #0x8000
    // 0xead854: ArrayStore: r0[0] = r16  ; List_4
    //     0xead854: stur            w16, [x0, #0x17]
    // 0xead858: r16 = 1082163232
    //     0xead858: movz            x16, #0x8020
    //     0xead85c: movk            x16, #0x4080, lsl #16
    // 0xead860: StoreField: r0->field_1b = r16
    //     0xead860: stur            w16, [x0, #0x1b]
    // 0xead864: r16 = 16512
    //     0xead864: movz            x16, #0x4080, lsl #16
    // 0xead868: StoreField: r0->field_1f = r16
    //     0xead868: stur            w16, [x0, #0x1f]
    // 0xead86c: r16 = 32
    //     0xead86c: movz            x16, #0x20
    // 0xead870: StoreField: r0->field_23 = r16
    //     0xead870: stur            w16, [x0, #0x23]
    // 0xead874: r16 = 1082163232
    //     0xead874: movz            x16, #0x8020
    //     0xead878: movk            x16, #0x4080, lsl #16
    // 0xead87c: StoreField: r0->field_27 = r16
    //     0xead87c: stur            w16, [x0, #0x27]
    // 0xead880: r16 = 128
    //     0xead880: movz            x16, #0x80, lsl #16
    // 0xead884: StoreField: r0->field_2b = r16
    //     0xead884: stur            w16, [x0, #0x2b]
    // 0xead888: r16 = 1073774592
    //     0xead888: movz            x16, #0x8000
    //     0xead88c: movk            x16, #0x4000, lsl #16
    // 0xead890: StoreField: r0->field_2f = r16
    //     0xead890: stur            w16, [x0, #0x2f]
    // 0xead894: r16 = 8421408
    //     0xead894: movz            x16, #0x8020
    //     0xead898: movk            x16, #0x80, lsl #16
    // 0xead89c: StoreField: r0->field_33 = r16
    //     0xead89c: stur            w16, [x0, #0x33]
    // 0xead8a0: r16 = 128
    //     0xead8a0: movz            x16, #0x80, lsl #16
    // 0xead8a4: StoreField: r0->field_37 = r16
    //     0xead8a4: stur            w16, [x0, #0x37]
    // 0xead8a8: r16 = 1073741856
    //     0xead8a8: movz            x16, #0x20
    //     0xead8ac: movk            x16, #0x4000, lsl #16
    // 0xead8b0: StoreField: r0->field_3b = r16
    //     0xead8b0: stur            w16, [x0, #0x3b]
    // 0xead8b4: r16 = 8388640
    //     0xead8b4: movz            x16, #0x20
    //     0xead8b8: movk            x16, #0x80, lsl #16
    // 0xead8bc: StoreField: r0->field_3f = r16
    //     0xead8bc: stur            w16, [x0, #0x3f]
    // 0xead8c0: r16 = 1073774592
    //     0xead8c0: movz            x16, #0x8000
    //     0xead8c4: movk            x16, #0x4000, lsl #16
    // 0xead8c8: StoreField: r0->field_43 = r16
    //     0xead8c8: stur            w16, [x0, #0x43]
    // 0xead8cc: r16 = 1073741824
    //     0xead8cc: orr             x16, xzr, #0x40000000
    // 0xead8d0: StoreField: r0->field_47 = r16
    //     0xead8d0: stur            w16, [x0, #0x47]
    // 0xead8d4: r16 = 32800
    //     0xead8d4: movz            x16, #0x8020
    // 0xead8d8: StoreField: r0->field_4b = r16
    //     0xead8d8: stur            w16, [x0, #0x4b]
    // 0xead8dc: StoreField: r0->field_4f = rZR
    //     0xead8dc: stur            wzr, [x0, #0x4f]
    // 0xead8e0: r16 = 8388640
    //     0xead8e0: movz            x16, #0x20
    //     0xead8e4: movk            x16, #0x80, lsl #16
    // 0xead8e8: StoreField: r0->field_53 = r16
    //     0xead8e8: stur            w16, [x0, #0x53]
    // 0xead8ec: r16 = 1073774624
    //     0xead8ec: movz            x16, #0x8020
    //     0xead8f0: movk            x16, #0x4000, lsl #16
    // 0xead8f4: StoreField: r0->field_57 = r16
    //     0xead8f4: stur            w16, [x0, #0x57]
    // 0xead8f8: r16 = 32768
    //     0xead8f8: movz            x16, #0x8000
    // 0xead8fc: StoreField: r0->field_5b = r16
    //     0xead8fc: stur            w16, [x0, #0x5b]
    // 0xead900: r16 = 8421376
    //     0xead900: movz            x16, #0x8000
    //     0xead904: movk            x16, #0x80, lsl #16
    // 0xead908: StoreField: r0->field_5f = r16
    //     0xead908: stur            w16, [x0, #0x5f]
    // 0xead90c: r16 = 1073774624
    //     0xead90c: movz            x16, #0x8020
    //     0xead910: movk            x16, #0x4000, lsl #16
    // 0xead914: StoreField: r0->field_63 = r16
    //     0xead914: stur            w16, [x0, #0x63]
    // 0xead918: r16 = 32
    //     0xead918: movz            x16, #0x20
    // 0xead91c: StoreField: r0->field_67 = r16
    //     0xead91c: stur            w16, [x0, #0x67]
    // 0xead920: r16 = 1082130464
    //     0xead920: movz            x16, #0x20
    //     0xead924: movk            x16, #0x4080, lsl #16
    // 0xead928: StoreField: r0->field_6b = r16
    //     0xead928: stur            w16, [x0, #0x6b]
    // 0xead92c: r16 = 1082130464
    //     0xead92c: movz            x16, #0x20
    //     0xead930: movk            x16, #0x4080, lsl #16
    // 0xead934: StoreField: r0->field_6f = r16
    //     0xead934: stur            w16, [x0, #0x6f]
    // 0xead938: StoreField: r0->field_73 = rZR
    //     0xead938: stur            wzr, [x0, #0x73]
    // 0xead93c: r16 = 8421408
    //     0xead93c: movz            x16, #0x8020
    //     0xead940: movk            x16, #0x80, lsl #16
    // 0xead944: StoreField: r0->field_77 = r16
    //     0xead944: stur            w16, [x0, #0x77]
    // 0xead948: r16 = 1082163200
    //     0xead948: movz            x16, #0x8000
    //     0xead94c: movk            x16, #0x4080, lsl #16
    // 0xead950: StoreField: r0->field_7b = r16
    //     0xead950: stur            w16, [x0, #0x7b]
    // 0xead954: r16 = 32800
    //     0xead954: movz            x16, #0x8020
    // 0xead958: StoreField: r0->field_7f = r16
    //     0xead958: stur            w16, [x0, #0x7f]
    // 0xead95c: r16 = 8421376
    //     0xead95c: movz            x16, #0x8000
    //     0xead960: movk            x16, #0x80, lsl #16
    // 0xead964: StoreField: r0->field_83 = r16
    //     0xead964: stur            w16, [x0, #0x83]
    // 0xead968: r16 = 1082163200
    //     0xead968: movz            x16, #0x8000
    //     0xead96c: movk            x16, #0x4080, lsl #16
    // 0xead970: StoreField: r0->field_87 = r16
    //     0xead970: stur            w16, [x0, #0x87]
    // 0xead974: r16 = 1073741824
    //     0xead974: orr             x16, xzr, #0x40000000
    // 0xead978: StoreField: r0->field_8b = r16
    //     0xead978: stur            w16, [x0, #0x8b]
    // 0xead97c: r16 = 1073774592
    //     0xead97c: movz            x16, #0x8000
    //     0xead980: movk            x16, #0x4000, lsl #16
    // 0xead984: StoreField: r0->field_8f = r16
    //     0xead984: stur            w16, [x0, #0x8f]
    // 0xead988: r16 = 32
    //     0xead988: movz            x16, #0x20
    // 0xead98c: StoreField: r0->field_93 = r16
    //     0xead98c: stur            w16, [x0, #0x93]
    // 0xead990: r16 = 1082130464
    //     0xead990: movz            x16, #0x20
    //     0xead994: movk            x16, #0x4080, lsl #16
    // 0xead998: StoreField: r0->field_97 = r16
    //     0xead998: stur            w16, [x0, #0x97]
    // 0xead99c: r16 = 8421376
    //     0xead99c: movz            x16, #0x8000
    //     0xead9a0: movk            x16, #0x80, lsl #16
    // 0xead9a4: StoreField: r0->field_9b = r16
    //     0xead9a4: stur            w16, [x0, #0x9b]
    // 0xead9a8: r16 = 1082163232
    //     0xead9a8: movz            x16, #0x8020
    //     0xead9ac: movk            x16, #0x4080, lsl #16
    // 0xead9b0: StoreField: r0->field_9f = r16
    //     0xead9b0: stur            w16, [x0, #0x9f]
    // 0xead9b4: r16 = 128
    //     0xead9b4: movz            x16, #0x80, lsl #16
    // 0xead9b8: StoreField: r0->field_a3 = r16
    //     0xead9b8: stur            w16, [x0, #0xa3]
    // 0xead9bc: r16 = 32800
    //     0xead9bc: movz            x16, #0x8020
    // 0xead9c0: StoreField: r0->field_a7 = r16
    //     0xead9c0: stur            w16, [x0, #0xa7]
    // 0xead9c4: r16 = 1073741856
    //     0xead9c4: movz            x16, #0x20
    //     0xead9c8: movk            x16, #0x4000, lsl #16
    // 0xead9cc: StoreField: r0->field_ab = r16
    //     0xead9cc: stur            w16, [x0, #0xab]
    // 0xead9d0: r16 = 128
    //     0xead9d0: movz            x16, #0x80, lsl #16
    // 0xead9d4: StoreField: r0->field_af = r16
    //     0xead9d4: stur            w16, [x0, #0xaf]
    // 0xead9d8: r16 = 1073774592
    //     0xead9d8: movz            x16, #0x8000
    //     0xead9dc: movk            x16, #0x4000, lsl #16
    // 0xead9e0: StoreField: r0->field_b3 = r16
    //     0xead9e0: stur            w16, [x0, #0xb3]
    // 0xead9e4: r16 = 1073741824
    //     0xead9e4: orr             x16, xzr, #0x40000000
    // 0xead9e8: StoreField: r0->field_b7 = r16
    //     0xead9e8: stur            w16, [x0, #0xb7]
    // 0xead9ec: r16 = 32800
    //     0xead9ec: movz            x16, #0x8020
    // 0xead9f0: StoreField: r0->field_bb = r16
    //     0xead9f0: stur            w16, [x0, #0xbb]
    // 0xead9f4: r16 = 1073741856
    //     0xead9f4: movz            x16, #0x20
    //     0xead9f8: movk            x16, #0x4000, lsl #16
    // 0xead9fc: StoreField: r0->field_bf = r16
    //     0xead9fc: stur            w16, [x0, #0xbf]
    // 0xeada00: r16 = 1082163232
    //     0xeada00: movz            x16, #0x8020
    //     0xeada04: movk            x16, #0x4080, lsl #16
    // 0xeada08: StoreField: r0->field_c3 = r16
    //     0xeada08: stur            w16, [x0, #0xc3]
    // 0xeada0c: r16 = 8421376
    //     0xeada0c: movz            x16, #0x8000
    //     0xeada10: movk            x16, #0x80, lsl #16
    // 0xeada14: StoreField: r0->field_c7 = r16
    //     0xeada14: stur            w16, [x0, #0xc7]
    // 0xeada18: r16 = 16512
    //     0xeada18: movz            x16, #0x4080, lsl #16
    // 0xeada1c: StoreField: r0->field_cb = r16
    //     0xeada1c: stur            w16, [x0, #0xcb]
    // 0xeada20: r16 = 8421408
    //     0xeada20: movz            x16, #0x8020
    //     0xeada24: movk            x16, #0x80, lsl #16
    // 0xeada28: StoreField: r0->field_cf = r16
    //     0xeada28: stur            w16, [x0, #0xcf]
    // 0xeada2c: r16 = 1082163200
    //     0xeada2c: movz            x16, #0x8000
    //     0xeada30: movk            x16, #0x4080, lsl #16
    // 0xeada34: StoreField: r0->field_d3 = r16
    //     0xeada34: stur            w16, [x0, #0xd3]
    // 0xeada38: StoreField: r0->field_d7 = rZR
    //     0xeada38: stur            wzr, [x0, #0xd7]
    // 0xeada3c: r16 = 1082130464
    //     0xeada3c: movz            x16, #0x20
    //     0xeada40: movk            x16, #0x4080, lsl #16
    // 0xeada44: StoreField: r0->field_db = r16
    //     0xeada44: stur            w16, [x0, #0xdb]
    // 0xeada48: r16 = 32
    //     0xeada48: movz            x16, #0x20
    // 0xeada4c: StoreField: r0->field_df = r16
    //     0xeada4c: stur            w16, [x0, #0xdf]
    // 0xeada50: r16 = 32768
    //     0xeada50: movz            x16, #0x8000
    // 0xeada54: StoreField: r0->field_e3 = r16
    //     0xeada54: stur            w16, [x0, #0xe3]
    // 0xeada58: r16 = 16512
    //     0xeada58: movz            x16, #0x4080, lsl #16
    // 0xeada5c: StoreField: r0->field_e7 = r16
    //     0xeada5c: stur            w16, [x0, #0xe7]
    // 0xeada60: r16 = 8421408
    //     0xeada60: movz            x16, #0x8020
    //     0xeada64: movk            x16, #0x80, lsl #16
    // 0xeada68: StoreField: r0->field_eb = r16
    //     0xeada68: stur            w16, [x0, #0xeb]
    // 0xeada6c: r16 = 32768
    //     0xeada6c: movz            x16, #0x8000
    // 0xeada70: StoreField: r0->field_ef = r16
    //     0xeada70: stur            w16, [x0, #0xef]
    // 0xeada74: r16 = 8388640
    //     0xeada74: movz            x16, #0x20
    //     0xeada78: movk            x16, #0x80, lsl #16
    // 0xeada7c: StoreField: r0->field_f3 = r16
    //     0xeada7c: stur            w16, [x0, #0xf3]
    // 0xeada80: r16 = 1073774624
    //     0xeada80: movz            x16, #0x8020
    //     0xeada84: movk            x16, #0x4000, lsl #16
    // 0xeada88: StoreField: r0->field_f7 = r16
    //     0xeada88: stur            w16, [x0, #0xf7]
    // 0xeada8c: StoreField: r0->field_fb = rZR
    //     0xeada8c: stur            wzr, [x0, #0xfb]
    // 0xeada90: r16 = 1082163200
    //     0xeada90: movz            x16, #0x8000
    //     0xeada94: movk            x16, #0x4080, lsl #16
    // 0xeada98: StoreField: r0->field_ff = r16
    //     0xeada98: stur            w16, [x0, #0xff]
    // 0xeada9c: r1 = 122
    //     0xeada9c: movz            x1, #0x7a
    // 0xeadaa0: add             x2, x0, w1, sxtw #1
    // 0xeadaa4: r16 = 1073741824
    //     0xeadaa4: orr             x16, xzr, #0x40000000
    // 0xeadaa8: StoreField: r2->field_f = r16
    //     0xeadaa8: stur            w16, [x2, #0xf]
    // 0xeadaac: r1 = 124
    //     0xeadaac: movz            x1, #0x7c
    // 0xeadab0: add             x2, x0, w1, sxtw #1
    // 0xeadab4: r16 = 8388640
    //     0xeadab4: movz            x16, #0x20
    //     0xeadab8: movk            x16, #0x80, lsl #16
    // 0xeadabc: StoreField: r2->field_f = r16
    //     0xeadabc: stur            w16, [x2, #0xf]
    // 0xeadac0: r1 = 126
    //     0xeadac0: movz            x1, #0x7e
    // 0xeadac4: add             x2, x0, w1, sxtw #1
    // 0xeadac8: r16 = 1073774624
    //     0xeadac8: movz            x16, #0x8020
    //     0xeadacc: movk            x16, #0x4000, lsl #16
    // 0xeadad0: StoreField: r2->field_f = r16
    //     0xeadad0: stur            w16, [x2, #0xf]
    // 0xeadad4: r1 = <int>
    //     0xeadad4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeadad8: r0 = AllocateGrowableArray()
    //     0xeadad8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xeadadc: ldur            x1, [fp, #-8]
    // 0xeadae0: StoreField: r0->field_f = r1
    //     0xeadae0: stur            w1, [x0, #0xf]
    // 0xeadae4: r1 = 128
    //     0xeadae4: movz            x1, #0x80
    // 0xeadae8: StoreField: r0->field_b = r1
    //     0xeadae8: stur            w1, [x0, #0xb]
    // 0xeadaec: LeaveFrame
    //     0xeadaec: mov             SP, fp
    //     0xeadaf0: ldp             fp, lr, [SP], #0x10
    // 0xeadaf4: ret
    //     0xeadaf4: ret             
  }
  static List<int> SP8() {
    // ** addr: 0xeadaf8, size: 0x2dc
    // 0xeadaf8: EnterFrame
    //     0xeadaf8: stp             fp, lr, [SP, #-0x10]!
    //     0xeadafc: mov             fp, SP
    // 0xeadb00: AllocStack(0x8)
    //     0xeadb00: sub             SP, SP, #8
    // 0xeadb04: r0 = 128
    //     0xeadb04: movz            x0, #0x80
    // 0xeadb08: mov             x2, x0
    // 0xeadb0c: r1 = <int>
    //     0xeadb0c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeadb10: r0 = AllocateArray()
    //     0xeadb10: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeadb14: stur            x0, [fp, #-8]
    // 0xeadb18: r16 = 536879232
    //     0xeadb18: movz            x16, #0x2080
    //     0xeadb1c: movk            x16, #0x2000, lsl #16
    // 0xeadb20: StoreField: r0->field_f = r16
    //     0xeadb20: stur            w16, [x0, #0xf]
    // 0xeadb24: r16 = 8192
    //     0xeadb24: movz            x16, #0x2000
    // 0xeadb28: StoreField: r0->field_13 = r16
    //     0xeadb28: stur            w16, [x0, #0x13]
    // 0xeadb2c: r16 = 8
    //     0xeadb2c: movz            x16, #0x8, lsl #16
    // 0xeadb30: ArrayStore: r0[0] = r16  ; List_4
    //     0xeadb30: stur            w16, [x0, #0x17]
    // 0xeadb34: r16 = 537403520
    //     0xeadb34: movz            x16, #0x2080
    //     0xeadb38: movk            x16, #0x2008, lsl #16
    // 0xeadb3c: StoreField: r0->field_1b = r16
    //     0xeadb3c: stur            w16, [x0, #0x1b]
    // 0xeadb40: r16 = 536870912
    //     0xeadb40: orr             x16, xzr, #0x20000000
    // 0xeadb44: StoreField: r0->field_1f = r16
    //     0xeadb44: stur            w16, [x0, #0x1f]
    // 0xeadb48: r16 = 536879232
    //     0xeadb48: movz            x16, #0x2080
    //     0xeadb4c: movk            x16, #0x2000, lsl #16
    // 0xeadb50: StoreField: r0->field_23 = r16
    //     0xeadb50: stur            w16, [x0, #0x23]
    // 0xeadb54: r16 = 128
    //     0xeadb54: movz            x16, #0x80
    // 0xeadb58: StoreField: r0->field_27 = r16
    //     0xeadb58: stur            w16, [x0, #0x27]
    // 0xeadb5c: r16 = 536870912
    //     0xeadb5c: orr             x16, xzr, #0x20000000
    // 0xeadb60: StoreField: r0->field_2b = r16
    //     0xeadb60: stur            w16, [x0, #0x2b]
    // 0xeadb64: r16 = 524416
    //     0xeadb64: movz            x16, #0x80
    //     0xeadb68: movk            x16, #0x8, lsl #16
    // 0xeadb6c: StoreField: r0->field_2f = r16
    //     0xeadb6c: stur            w16, [x0, #0x2f]
    // 0xeadb70: r16 = 8200
    //     0xeadb70: movz            x16, #0x2008, lsl #16
    // 0xeadb74: StoreField: r0->field_33 = r16
    //     0xeadb74: stur            w16, [x0, #0x33]
    // 0xeadb78: r16 = 537403520
    //     0xeadb78: movz            x16, #0x2080
    //     0xeadb7c: movk            x16, #0x2008, lsl #16
    // 0xeadb80: StoreField: r0->field_37 = r16
    //     0xeadb80: stur            w16, [x0, #0x37]
    // 0xeadb84: r16 = 532480
    //     0xeadb84: movz            x16, #0x2000
    //     0xeadb88: movk            x16, #0x8, lsl #16
    // 0xeadb8c: StoreField: r0->field_3b = r16
    //     0xeadb8c: stur            w16, [x0, #0x3b]
    // 0xeadb90: r16 = 537403392
    //     0xeadb90: movz            x16, #0x2000
    //     0xeadb94: movk            x16, #0x2008, lsl #16
    // 0xeadb98: StoreField: r0->field_3f = r16
    //     0xeadb98: stur            w16, [x0, #0x3f]
    // 0xeadb9c: r16 = 532608
    //     0xeadb9c: movz            x16, #0x2080
    //     0xeadba0: movk            x16, #0x8, lsl #16
    // 0xeadba4: StoreField: r0->field_43 = r16
    //     0xeadba4: stur            w16, [x0, #0x43]
    // 0xeadba8: r16 = 8192
    //     0xeadba8: movz            x16, #0x2000
    // 0xeadbac: StoreField: r0->field_47 = r16
    //     0xeadbac: stur            w16, [x0, #0x47]
    // 0xeadbb0: r16 = 128
    //     0xeadbb0: movz            x16, #0x80
    // 0xeadbb4: StoreField: r0->field_4b = r16
    //     0xeadbb4: stur            w16, [x0, #0x4b]
    // 0xeadbb8: r16 = 8200
    //     0xeadbb8: movz            x16, #0x2008, lsl #16
    // 0xeadbbc: StoreField: r0->field_4f = r16
    //     0xeadbbc: stur            w16, [x0, #0x4f]
    // 0xeadbc0: r16 = 536871040
    //     0xeadbc0: movz            x16, #0x80
    //     0xeadbc4: movk            x16, #0x2000, lsl #16
    // 0xeadbc8: StoreField: r0->field_53 = r16
    //     0xeadbc8: stur            w16, [x0, #0x53]
    // 0xeadbcc: r16 = 536879104
    //     0xeadbcc: movz            x16, #0x2000
    //     0xeadbd0: movk            x16, #0x2000, lsl #16
    // 0xeadbd4: StoreField: r0->field_57 = r16
    //     0xeadbd4: stur            w16, [x0, #0x57]
    // 0xeadbd8: r16 = 8320
    //     0xeadbd8: movz            x16, #0x2080
    // 0xeadbdc: StoreField: r0->field_5b = r16
    //     0xeadbdc: stur            w16, [x0, #0x5b]
    // 0xeadbe0: r16 = 532480
    //     0xeadbe0: movz            x16, #0x2000
    //     0xeadbe4: movk            x16, #0x8, lsl #16
    // 0xeadbe8: StoreField: r0->field_5f = r16
    //     0xeadbe8: stur            w16, [x0, #0x5f]
    // 0xeadbec: r16 = 524416
    //     0xeadbec: movz            x16, #0x80
    //     0xeadbf0: movk            x16, #0x8, lsl #16
    // 0xeadbf4: StoreField: r0->field_63 = r16
    //     0xeadbf4: stur            w16, [x0, #0x63]
    // 0xeadbf8: r16 = 537395328
    //     0xeadbf8: movz            x16, #0x80
    //     0xeadbfc: movk            x16, #0x2008, lsl #16
    // 0xeadc00: StoreField: r0->field_67 = r16
    //     0xeadc00: stur            w16, [x0, #0x67]
    // 0xeadc04: r16 = 537403392
    //     0xeadc04: movz            x16, #0x2000
    //     0xeadc08: movk            x16, #0x2008, lsl #16
    // 0xeadc0c: StoreField: r0->field_6b = r16
    //     0xeadc0c: stur            w16, [x0, #0x6b]
    // 0xeadc10: r16 = 8320
    //     0xeadc10: movz            x16, #0x2080
    // 0xeadc14: StoreField: r0->field_6f = r16
    //     0xeadc14: stur            w16, [x0, #0x6f]
    // 0xeadc18: StoreField: r0->field_73 = rZR
    //     0xeadc18: stur            wzr, [x0, #0x73]
    // 0xeadc1c: StoreField: r0->field_77 = rZR
    //     0xeadc1c: stur            wzr, [x0, #0x77]
    // 0xeadc20: r16 = 537395328
    //     0xeadc20: movz            x16, #0x80
    //     0xeadc24: movk            x16, #0x2008, lsl #16
    // 0xeadc28: StoreField: r0->field_7b = r16
    //     0xeadc28: stur            w16, [x0, #0x7b]
    // 0xeadc2c: r16 = 536871040
    //     0xeadc2c: movz            x16, #0x80
    //     0xeadc30: movk            x16, #0x2000, lsl #16
    // 0xeadc34: StoreField: r0->field_7f = r16
    //     0xeadc34: stur            w16, [x0, #0x7f]
    // 0xeadc38: r16 = 536879104
    //     0xeadc38: movz            x16, #0x2000
    //     0xeadc3c: movk            x16, #0x2000, lsl #16
    // 0xeadc40: StoreField: r0->field_83 = r16
    //     0xeadc40: stur            w16, [x0, #0x83]
    // 0xeadc44: r16 = 532608
    //     0xeadc44: movz            x16, #0x2080
    //     0xeadc48: movk            x16, #0x8, lsl #16
    // 0xeadc4c: StoreField: r0->field_87 = r16
    //     0xeadc4c: stur            w16, [x0, #0x87]
    // 0xeadc50: r16 = 8
    //     0xeadc50: movz            x16, #0x8, lsl #16
    // 0xeadc54: StoreField: r0->field_8b = r16
    //     0xeadc54: stur            w16, [x0, #0x8b]
    // 0xeadc58: r16 = 532608
    //     0xeadc58: movz            x16, #0x2080
    //     0xeadc5c: movk            x16, #0x8, lsl #16
    // 0xeadc60: StoreField: r0->field_8f = r16
    //     0xeadc60: stur            w16, [x0, #0x8f]
    // 0xeadc64: r16 = 8
    //     0xeadc64: movz            x16, #0x8, lsl #16
    // 0xeadc68: StoreField: r0->field_93 = r16
    //     0xeadc68: stur            w16, [x0, #0x93]
    // 0xeadc6c: r16 = 537403392
    //     0xeadc6c: movz            x16, #0x2000
    //     0xeadc70: movk            x16, #0x2008, lsl #16
    // 0xeadc74: StoreField: r0->field_97 = r16
    //     0xeadc74: stur            w16, [x0, #0x97]
    // 0xeadc78: r16 = 8192
    //     0xeadc78: movz            x16, #0x2000
    // 0xeadc7c: StoreField: r0->field_9b = r16
    //     0xeadc7c: stur            w16, [x0, #0x9b]
    // 0xeadc80: r16 = 128
    //     0xeadc80: movz            x16, #0x80
    // 0xeadc84: StoreField: r0->field_9f = r16
    //     0xeadc84: stur            w16, [x0, #0x9f]
    // 0xeadc88: r16 = 537395328
    //     0xeadc88: movz            x16, #0x80
    //     0xeadc8c: movk            x16, #0x2008, lsl #16
    // 0xeadc90: StoreField: r0->field_a3 = r16
    //     0xeadc90: stur            w16, [x0, #0xa3]
    // 0xeadc94: r16 = 8192
    //     0xeadc94: movz            x16, #0x2000
    // 0xeadc98: StoreField: r0->field_a7 = r16
    //     0xeadc98: stur            w16, [x0, #0xa7]
    // 0xeadc9c: r16 = 532608
    //     0xeadc9c: movz            x16, #0x2080
    //     0xeadca0: movk            x16, #0x8, lsl #16
    // 0xeadca4: StoreField: r0->field_ab = r16
    //     0xeadca4: stur            w16, [x0, #0xab]
    // 0xeadca8: r16 = 536879104
    //     0xeadca8: movz            x16, #0x2000
    //     0xeadcac: movk            x16, #0x2000, lsl #16
    // 0xeadcb0: StoreField: r0->field_af = r16
    //     0xeadcb0: stur            w16, [x0, #0xaf]
    // 0xeadcb4: r16 = 128
    //     0xeadcb4: movz            x16, #0x80
    // 0xeadcb8: StoreField: r0->field_b3 = r16
    //     0xeadcb8: stur            w16, [x0, #0xb3]
    // 0xeadcbc: r16 = 536871040
    //     0xeadcbc: movz            x16, #0x80
    //     0xeadcc0: movk            x16, #0x2000, lsl #16
    // 0xeadcc4: StoreField: r0->field_b7 = r16
    //     0xeadcc4: stur            w16, [x0, #0xb7]
    // 0xeadcc8: r16 = 8200
    //     0xeadcc8: movz            x16, #0x2008, lsl #16
    // 0xeadccc: StoreField: r0->field_bb = r16
    //     0xeadccc: stur            w16, [x0, #0xbb]
    // 0xeadcd0: r16 = 537395328
    //     0xeadcd0: movz            x16, #0x80
    //     0xeadcd4: movk            x16, #0x2008, lsl #16
    // 0xeadcd8: StoreField: r0->field_bf = r16
    //     0xeadcd8: stur            w16, [x0, #0xbf]
    // 0xeadcdc: r16 = 536870912
    //     0xeadcdc: orr             x16, xzr, #0x20000000
    // 0xeadce0: StoreField: r0->field_c3 = r16
    //     0xeadce0: stur            w16, [x0, #0xc3]
    // 0xeadce4: r16 = 8
    //     0xeadce4: movz            x16, #0x8, lsl #16
    // 0xeadce8: StoreField: r0->field_c7 = r16
    //     0xeadce8: stur            w16, [x0, #0xc7]
    // 0xeadcec: r16 = 536879232
    //     0xeadcec: movz            x16, #0x2080
    //     0xeadcf0: movk            x16, #0x2000, lsl #16
    // 0xeadcf4: StoreField: r0->field_cb = r16
    //     0xeadcf4: stur            w16, [x0, #0xcb]
    // 0xeadcf8: StoreField: r0->field_cf = rZR
    //     0xeadcf8: stur            wzr, [x0, #0xcf]
    // 0xeadcfc: r16 = 537403520
    //     0xeadcfc: movz            x16, #0x2080
    //     0xeadd00: movk            x16, #0x2008, lsl #16
    // 0xeadd04: StoreField: r0->field_d3 = r16
    //     0xeadd04: stur            w16, [x0, #0xd3]
    // 0xeadd08: r16 = 524416
    //     0xeadd08: movz            x16, #0x80
    //     0xeadd0c: movk            x16, #0x8, lsl #16
    // 0xeadd10: StoreField: r0->field_d7 = r16
    //     0xeadd10: stur            w16, [x0, #0xd7]
    // 0xeadd14: r16 = 536871040
    //     0xeadd14: movz            x16, #0x80
    //     0xeadd18: movk            x16, #0x2000, lsl #16
    // 0xeadd1c: StoreField: r0->field_db = r16
    //     0xeadd1c: stur            w16, [x0, #0xdb]
    // 0xeadd20: r16 = 8200
    //     0xeadd20: movz            x16, #0x2008, lsl #16
    // 0xeadd24: StoreField: r0->field_df = r16
    //     0xeadd24: stur            w16, [x0, #0xdf]
    // 0xeadd28: r16 = 536879104
    //     0xeadd28: movz            x16, #0x2000
    //     0xeadd2c: movk            x16, #0x2000, lsl #16
    // 0xeadd30: StoreField: r0->field_e3 = r16
    //     0xeadd30: stur            w16, [x0, #0xe3]
    // 0xeadd34: r16 = 536879232
    //     0xeadd34: movz            x16, #0x2080
    //     0xeadd38: movk            x16, #0x2000, lsl #16
    // 0xeadd3c: StoreField: r0->field_e7 = r16
    //     0xeadd3c: stur            w16, [x0, #0xe7]
    // 0xeadd40: StoreField: r0->field_eb = rZR
    //     0xeadd40: stur            wzr, [x0, #0xeb]
    // 0xeadd44: r16 = 537403520
    //     0xeadd44: movz            x16, #0x2080
    //     0xeadd48: movk            x16, #0x2008, lsl #16
    // 0xeadd4c: StoreField: r0->field_ef = r16
    //     0xeadd4c: stur            w16, [x0, #0xef]
    // 0xeadd50: r16 = 532480
    //     0xeadd50: movz            x16, #0x2000
    //     0xeadd54: movk            x16, #0x8, lsl #16
    // 0xeadd58: StoreField: r0->field_f3 = r16
    //     0xeadd58: stur            w16, [x0, #0xf3]
    // 0xeadd5c: r16 = 532480
    //     0xeadd5c: movz            x16, #0x2000
    //     0xeadd60: movk            x16, #0x8, lsl #16
    // 0xeadd64: StoreField: r0->field_f7 = r16
    //     0xeadd64: stur            w16, [x0, #0xf7]
    // 0xeadd68: r16 = 8320
    //     0xeadd68: movz            x16, #0x2080
    // 0xeadd6c: StoreField: r0->field_fb = r16
    //     0xeadd6c: stur            w16, [x0, #0xfb]
    // 0xeadd70: r16 = 8320
    //     0xeadd70: movz            x16, #0x2080
    // 0xeadd74: StoreField: r0->field_ff = r16
    //     0xeadd74: stur            w16, [x0, #0xff]
    // 0xeadd78: r1 = 122
    //     0xeadd78: movz            x1, #0x7a
    // 0xeadd7c: add             x2, x0, w1, sxtw #1
    // 0xeadd80: r16 = 524416
    //     0xeadd80: movz            x16, #0x80
    //     0xeadd84: movk            x16, #0x8, lsl #16
    // 0xeadd88: StoreField: r2->field_f = r16
    //     0xeadd88: stur            w16, [x2, #0xf]
    // 0xeadd8c: r1 = 124
    //     0xeadd8c: movz            x1, #0x7c
    // 0xeadd90: add             x2, x0, w1, sxtw #1
    // 0xeadd94: r16 = 536870912
    //     0xeadd94: orr             x16, xzr, #0x20000000
    // 0xeadd98: StoreField: r2->field_f = r16
    //     0xeadd98: stur            w16, [x2, #0xf]
    // 0xeadd9c: r1 = 126
    //     0xeadd9c: movz            x1, #0x7e
    // 0xeadda0: add             x2, x0, w1, sxtw #1
    // 0xeadda4: r16 = 537403392
    //     0xeadda4: movz            x16, #0x2000
    //     0xeadda8: movk            x16, #0x2008, lsl #16
    // 0xeaddac: StoreField: r2->field_f = r16
    //     0xeaddac: stur            w16, [x2, #0xf]
    // 0xeaddb0: r1 = <int>
    //     0xeaddb0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeaddb4: r0 = AllocateGrowableArray()
    //     0xeaddb4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xeaddb8: ldur            x1, [fp, #-8]
    // 0xeaddbc: StoreField: r0->field_f = r1
    //     0xeaddbc: stur            w1, [x0, #0xf]
    // 0xeaddc0: r1 = 128
    //     0xeaddc0: movz            x1, #0x80
    // 0xeaddc4: StoreField: r0->field_b = r1
    //     0xeaddc4: stur            w1, [x0, #0xb]
    // 0xeaddc8: LeaveFrame
    //     0xeaddc8: mov             SP, fp
    //     0xeaddcc: ldp             fp, lr, [SP], #0x10
    // 0xeaddd0: ret
    //     0xeaddd0: ret             
  }
  static List<int> SP1() {
    // ** addr: 0xeaddd4, size: 0x2dc
    // 0xeaddd4: EnterFrame
    //     0xeaddd4: stp             fp, lr, [SP, #-0x10]!
    //     0xeaddd8: mov             fp, SP
    // 0xeadddc: AllocStack(0x8)
    //     0xeadddc: sub             SP, SP, #8
    // 0xeadde0: r0 = 128
    //     0xeadde0: movz            x0, #0x80
    // 0xeadde4: mov             x2, x0
    // 0xeadde8: r1 = <int>
    //     0xeadde8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeaddec: r0 = AllocateArray()
    //     0xeaddec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeaddf0: stur            x0, [fp, #-8]
    // 0xeaddf4: r16 = 33687552
    //     0xeaddf4: movz            x16, #0x800
    //     0xeaddf8: movk            x16, #0x202, lsl #16
    // 0xeaddfc: StoreField: r0->field_f = r16
    //     0xeaddfc: stur            w16, [x0, #0xf]
    // 0xeade00: StoreField: r0->field_13 = rZR
    //     0xeade00: stur            wzr, [x0, #0x13]
    // 0xeade04: r16 = 2
    //     0xeade04: movz            x16, #0x2, lsl #16
    // 0xeade08: ArrayStore: r0[0] = r16  ; List_4
    //     0xeade08: stur            w16, [x0, #0x17]
    // 0xeade0c: r16 = 33687560
    //     0xeade0c: movz            x16, #0x808
    //     0xeade10: movk            x16, #0x202, lsl #16
    // 0xeade14: StoreField: r0->field_1b = r16
    //     0xeade14: stur            w16, [x0, #0x1b]
    // 0xeade18: r16 = 33685512
    //     0xeade18: movz            x16, #0x8
    //     0xeade1c: movk            x16, #0x202, lsl #16
    // 0xeade20: StoreField: r0->field_1f = r16
    //     0xeade20: stur            w16, [x0, #0x1f]
    // 0xeade24: r16 = 133128
    //     0xeade24: movz            x16, #0x808
    //     0xeade28: movk            x16, #0x2, lsl #16
    // 0xeade2c: StoreField: r0->field_23 = r16
    //     0xeade2c: stur            w16, [x0, #0x23]
    // 0xeade30: r16 = 8
    //     0xeade30: movz            x16, #0x8
    // 0xeade34: StoreField: r0->field_27 = r16
    //     0xeade34: stur            w16, [x0, #0x27]
    // 0xeade38: r16 = 2
    //     0xeade38: movz            x16, #0x2, lsl #16
    // 0xeade3c: StoreField: r0->field_2b = r16
    //     0xeade3c: stur            w16, [x0, #0x2b]
    // 0xeade40: r16 = 2048
    //     0xeade40: movz            x16, #0x800
    // 0xeade44: StoreField: r0->field_2f = r16
    //     0xeade44: stur            w16, [x0, #0x2f]
    // 0xeade48: r16 = 33687552
    //     0xeade48: movz            x16, #0x800
    //     0xeade4c: movk            x16, #0x202, lsl #16
    // 0xeade50: StoreField: r0->field_33 = r16
    //     0xeade50: stur            w16, [x0, #0x33]
    // 0xeade54: r16 = 33687560
    //     0xeade54: movz            x16, #0x808
    //     0xeade58: movk            x16, #0x202, lsl #16
    // 0xeade5c: StoreField: r0->field_37 = r16
    //     0xeade5c: stur            w16, [x0, #0x37]
    // 0xeade60: r16 = 2048
    //     0xeade60: movz            x16, #0x800
    // 0xeade64: StoreField: r0->field_3b = r16
    //     0xeade64: stur            w16, [x0, #0x3b]
    // 0xeade68: r16 = 33556488
    //     0xeade68: movz            x16, #0x808
    //     0xeade6c: movk            x16, #0x200, lsl #16
    // 0xeade70: StoreField: r0->field_3f = r16
    //     0xeade70: stur            w16, [x0, #0x3f]
    // 0xeade74: r16 = 33685512
    //     0xeade74: movz            x16, #0x8
    //     0xeade78: movk            x16, #0x202, lsl #16
    // 0xeade7c: StoreField: r0->field_43 = r16
    //     0xeade7c: stur            w16, [x0, #0x43]
    // 0xeade80: r16 = 33554432
    //     0xeade80: orr             x16, xzr, #0x2000000
    // 0xeade84: StoreField: r0->field_47 = r16
    //     0xeade84: stur            w16, [x0, #0x47]
    // 0xeade88: r16 = 8
    //     0xeade88: movz            x16, #0x8
    // 0xeade8c: StoreField: r0->field_4b = r16
    //     0xeade8c: stur            w16, [x0, #0x4b]
    // 0xeade90: r16 = 2056
    //     0xeade90: movz            x16, #0x808
    // 0xeade94: StoreField: r0->field_4f = r16
    //     0xeade94: stur            w16, [x0, #0x4f]
    // 0xeade98: r16 = 33556480
    //     0xeade98: movz            x16, #0x800
    //     0xeade9c: movk            x16, #0x200, lsl #16
    // 0xeadea0: StoreField: r0->field_53 = r16
    //     0xeadea0: stur            w16, [x0, #0x53]
    // 0xeadea4: r16 = 33556480
    //     0xeadea4: movz            x16, #0x800
    //     0xeadea8: movk            x16, #0x200, lsl #16
    // 0xeadeac: StoreField: r0->field_57 = r16
    //     0xeadeac: stur            w16, [x0, #0x57]
    // 0xeadeb0: r16 = 133120
    //     0xeadeb0: movz            x16, #0x800
    //     0xeadeb4: movk            x16, #0x2, lsl #16
    // 0xeadeb8: StoreField: r0->field_5b = r16
    //     0xeadeb8: stur            w16, [x0, #0x5b]
    // 0xeadebc: r16 = 133120
    //     0xeadebc: movz            x16, #0x800
    //     0xeadec0: movk            x16, #0x2, lsl #16
    // 0xeadec4: StoreField: r0->field_5f = r16
    //     0xeadec4: stur            w16, [x0, #0x5f]
    // 0xeadec8: r16 = 514
    //     0xeadec8: movz            x16, #0x202, lsl #16
    // 0xeadecc: StoreField: r0->field_63 = r16
    //     0xeadecc: stur            w16, [x0, #0x63]
    // 0xeaded0: r16 = 514
    //     0xeaded0: movz            x16, #0x202, lsl #16
    // 0xeaded4: StoreField: r0->field_67 = r16
    //     0xeaded4: stur            w16, [x0, #0x67]
    // 0xeaded8: r16 = 33556488
    //     0xeaded8: movz            x16, #0x808
    //     0xeadedc: movk            x16, #0x200, lsl #16
    // 0xeadee0: StoreField: r0->field_6b = r16
    //     0xeadee0: stur            w16, [x0, #0x6b]
    // 0xeadee4: r16 = 131080
    //     0xeadee4: movz            x16, #0x8
    //     0xeadee8: movk            x16, #0x2, lsl #16
    // 0xeadeec: StoreField: r0->field_6f = r16
    //     0xeadeec: stur            w16, [x0, #0x6f]
    // 0xeadef0: r16 = 33554440
    //     0xeadef0: movz            x16, #0x8
    //     0xeadef4: movk            x16, #0x200, lsl #16
    // 0xeadef8: StoreField: r0->field_73 = r16
    //     0xeadef8: stur            w16, [x0, #0x73]
    // 0xeadefc: r16 = 33554440
    //     0xeadefc: movz            x16, #0x8
    //     0xeadf00: movk            x16, #0x200, lsl #16
    // 0xeadf04: StoreField: r0->field_77 = r16
    //     0xeadf04: stur            w16, [x0, #0x77]
    // 0xeadf08: r16 = 131080
    //     0xeadf08: movz            x16, #0x8
    //     0xeadf0c: movk            x16, #0x2, lsl #16
    // 0xeadf10: StoreField: r0->field_7b = r16
    //     0xeadf10: stur            w16, [x0, #0x7b]
    // 0xeadf14: StoreField: r0->field_7f = rZR
    //     0xeadf14: stur            wzr, [x0, #0x7f]
    // 0xeadf18: r16 = 2056
    //     0xeadf18: movz            x16, #0x808
    // 0xeadf1c: StoreField: r0->field_83 = r16
    //     0xeadf1c: stur            w16, [x0, #0x83]
    // 0xeadf20: r16 = 133128
    //     0xeadf20: movz            x16, #0x808
    //     0xeadf24: movk            x16, #0x2, lsl #16
    // 0xeadf28: StoreField: r0->field_87 = r16
    //     0xeadf28: stur            w16, [x0, #0x87]
    // 0xeadf2c: r16 = 33554432
    //     0xeadf2c: orr             x16, xzr, #0x2000000
    // 0xeadf30: StoreField: r0->field_8b = r16
    //     0xeadf30: stur            w16, [x0, #0x8b]
    // 0xeadf34: r16 = 2
    //     0xeadf34: movz            x16, #0x2, lsl #16
    // 0xeadf38: StoreField: r0->field_8f = r16
    //     0xeadf38: stur            w16, [x0, #0x8f]
    // 0xeadf3c: r16 = 33687560
    //     0xeadf3c: movz            x16, #0x808
    //     0xeadf40: movk            x16, #0x202, lsl #16
    // 0xeadf44: StoreField: r0->field_93 = r16
    //     0xeadf44: stur            w16, [x0, #0x93]
    // 0xeadf48: r16 = 8
    //     0xeadf48: movz            x16, #0x8
    // 0xeadf4c: StoreField: r0->field_97 = r16
    //     0xeadf4c: stur            w16, [x0, #0x97]
    // 0xeadf50: r16 = 514
    //     0xeadf50: movz            x16, #0x202, lsl #16
    // 0xeadf54: StoreField: r0->field_9b = r16
    //     0xeadf54: stur            w16, [x0, #0x9b]
    // 0xeadf58: r16 = 33687552
    //     0xeadf58: movz            x16, #0x800
    //     0xeadf5c: movk            x16, #0x202, lsl #16
    // 0xeadf60: StoreField: r0->field_9f = r16
    //     0xeadf60: stur            w16, [x0, #0x9f]
    // 0xeadf64: r16 = 33554432
    //     0xeadf64: orr             x16, xzr, #0x2000000
    // 0xeadf68: StoreField: r0->field_a3 = r16
    //     0xeadf68: stur            w16, [x0, #0xa3]
    // 0xeadf6c: r16 = 33554432
    //     0xeadf6c: orr             x16, xzr, #0x2000000
    // 0xeadf70: StoreField: r0->field_a7 = r16
    //     0xeadf70: stur            w16, [x0, #0xa7]
    // 0xeadf74: r16 = 2048
    //     0xeadf74: movz            x16, #0x800
    // 0xeadf78: StoreField: r0->field_ab = r16
    //     0xeadf78: stur            w16, [x0, #0xab]
    // 0xeadf7c: r16 = 33685512
    //     0xeadf7c: movz            x16, #0x8
    //     0xeadf80: movk            x16, #0x202, lsl #16
    // 0xeadf84: StoreField: r0->field_af = r16
    //     0xeadf84: stur            w16, [x0, #0xaf]
    // 0xeadf88: r16 = 2
    //     0xeadf88: movz            x16, #0x2, lsl #16
    // 0xeadf8c: StoreField: r0->field_b3 = r16
    //     0xeadf8c: stur            w16, [x0, #0xb3]
    // 0xeadf90: r16 = 133120
    //     0xeadf90: movz            x16, #0x800
    //     0xeadf94: movk            x16, #0x2, lsl #16
    // 0xeadf98: StoreField: r0->field_b7 = r16
    //     0xeadf98: stur            w16, [x0, #0xb7]
    // 0xeadf9c: r16 = 33554440
    //     0xeadf9c: movz            x16, #0x8
    //     0xeadfa0: movk            x16, #0x200, lsl #16
    // 0xeadfa4: StoreField: r0->field_bb = r16
    //     0xeadfa4: stur            w16, [x0, #0xbb]
    // 0xeadfa8: r16 = 2048
    //     0xeadfa8: movz            x16, #0x800
    // 0xeadfac: StoreField: r0->field_bf = r16
    //     0xeadfac: stur            w16, [x0, #0xbf]
    // 0xeadfb0: r16 = 8
    //     0xeadfb0: movz            x16, #0x8
    // 0xeadfb4: StoreField: r0->field_c3 = r16
    //     0xeadfb4: stur            w16, [x0, #0xc3]
    // 0xeadfb8: r16 = 33556488
    //     0xeadfb8: movz            x16, #0x808
    //     0xeadfbc: movk            x16, #0x200, lsl #16
    // 0xeadfc0: StoreField: r0->field_c7 = r16
    //     0xeadfc0: stur            w16, [x0, #0xc7]
    // 0xeadfc4: r16 = 133128
    //     0xeadfc4: movz            x16, #0x808
    //     0xeadfc8: movk            x16, #0x2, lsl #16
    // 0xeadfcc: StoreField: r0->field_cb = r16
    //     0xeadfcc: stur            w16, [x0, #0xcb]
    // 0xeadfd0: r16 = 33687560
    //     0xeadfd0: movz            x16, #0x808
    //     0xeadfd4: movk            x16, #0x202, lsl #16
    // 0xeadfd8: StoreField: r0->field_cf = r16
    //     0xeadfd8: stur            w16, [x0, #0xcf]
    // 0xeadfdc: r16 = 131080
    //     0xeadfdc: movz            x16, #0x8
    //     0xeadfe0: movk            x16, #0x2, lsl #16
    // 0xeadfe4: StoreField: r0->field_d3 = r16
    //     0xeadfe4: stur            w16, [x0, #0xd3]
    // 0xeadfe8: r16 = 514
    //     0xeadfe8: movz            x16, #0x202, lsl #16
    // 0xeadfec: StoreField: r0->field_d7 = r16
    //     0xeadfec: stur            w16, [x0, #0xd7]
    // 0xeadff0: r16 = 33556488
    //     0xeadff0: movz            x16, #0x808
    //     0xeadff4: movk            x16, #0x200, lsl #16
    // 0xeadff8: StoreField: r0->field_db = r16
    //     0xeadff8: stur            w16, [x0, #0xdb]
    // 0xeadffc: r16 = 33554440
    //     0xeadffc: movz            x16, #0x8
    //     0xeae000: movk            x16, #0x200, lsl #16
    // 0xeae004: StoreField: r0->field_df = r16
    //     0xeae004: stur            w16, [x0, #0xdf]
    // 0xeae008: r16 = 2056
    //     0xeae008: movz            x16, #0x808
    // 0xeae00c: StoreField: r0->field_e3 = r16
    //     0xeae00c: stur            w16, [x0, #0xe3]
    // 0xeae010: r16 = 133128
    //     0xeae010: movz            x16, #0x808
    //     0xeae014: movk            x16, #0x2, lsl #16
    // 0xeae018: StoreField: r0->field_e7 = r16
    //     0xeae018: stur            w16, [x0, #0xe7]
    // 0xeae01c: r16 = 33687552
    //     0xeae01c: movz            x16, #0x800
    //     0xeae020: movk            x16, #0x202, lsl #16
    // 0xeae024: StoreField: r0->field_eb = r16
    //     0xeae024: stur            w16, [x0, #0xeb]
    // 0xeae028: r16 = 2056
    //     0xeae028: movz            x16, #0x808
    // 0xeae02c: StoreField: r0->field_ef = r16
    //     0xeae02c: stur            w16, [x0, #0xef]
    // 0xeae030: r16 = 33556480
    //     0xeae030: movz            x16, #0x800
    //     0xeae034: movk            x16, #0x200, lsl #16
    // 0xeae038: StoreField: r0->field_f3 = r16
    //     0xeae038: stur            w16, [x0, #0xf3]
    // 0xeae03c: r16 = 33556480
    //     0xeae03c: movz            x16, #0x800
    //     0xeae040: movk            x16, #0x200, lsl #16
    // 0xeae044: StoreField: r0->field_f7 = r16
    //     0xeae044: stur            w16, [x0, #0xf7]
    // 0xeae048: StoreField: r0->field_fb = rZR
    //     0xeae048: stur            wzr, [x0, #0xfb]
    // 0xeae04c: r16 = 131080
    //     0xeae04c: movz            x16, #0x8
    //     0xeae050: movk            x16, #0x2, lsl #16
    // 0xeae054: StoreField: r0->field_ff = r16
    //     0xeae054: stur            w16, [x0, #0xff]
    // 0xeae058: r1 = 122
    //     0xeae058: movz            x1, #0x7a
    // 0xeae05c: add             x2, x0, w1, sxtw #1
    // 0xeae060: r16 = 133120
    //     0xeae060: movz            x16, #0x800
    //     0xeae064: movk            x16, #0x2, lsl #16
    // 0xeae068: StoreField: r2->field_f = r16
    //     0xeae068: stur            w16, [x2, #0xf]
    // 0xeae06c: r1 = 124
    //     0xeae06c: movz            x1, #0x7c
    // 0xeae070: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0xeae070: add             x2, x0, w1, sxtw #1
    //     0xeae074: stur            wzr, [x2, #0xf]
    // 0xeae078: r1 = 126
    //     0xeae078: movz            x1, #0x7e
    // 0xeae07c: add             x2, x0, w1, sxtw #1
    // 0xeae080: r16 = 33685512
    //     0xeae080: movz            x16, #0x8
    //     0xeae084: movk            x16, #0x202, lsl #16
    // 0xeae088: StoreField: r2->field_f = r16
    //     0xeae088: stur            w16, [x2, #0xf]
    // 0xeae08c: r1 = <int>
    //     0xeae08c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeae090: r0 = AllocateGrowableArray()
    //     0xeae090: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xeae094: ldur            x1, [fp, #-8]
    // 0xeae098: StoreField: r0->field_f = r1
    //     0xeae098: stur            w1, [x0, #0xf]
    // 0xeae09c: r1 = 128
    //     0xeae09c: movz            x1, #0x80
    // 0xeae0a0: StoreField: r0->field_b = r1
    //     0xeae0a0: stur            w1, [x0, #0xb]
    // 0xeae0a4: LeaveFrame
    //     0xeae0a4: mov             SP, fp
    //     0xeae0a8: ldp             fp, lr, [SP], #0x10
    // 0xeae0ac: ret
    //     0xeae0ac: ret             
  }
  static List<int> SP3() {
    // ** addr: 0xeae0b0, size: 0x2dc
    // 0xeae0b0: EnterFrame
    //     0xeae0b0: stp             fp, lr, [SP, #-0x10]!
    //     0xeae0b4: mov             fp, SP
    // 0xeae0b8: AllocStack(0x8)
    //     0xeae0b8: sub             SP, SP, #8
    // 0xeae0bc: r0 = 128
    //     0xeae0bc: movz            x0, #0x80
    // 0xeae0c0: mov             x2, x0
    // 0xeae0c4: r1 = <int>
    //     0xeae0c4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeae0c8: r0 = AllocateArray()
    //     0xeae0c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeae0cc: stur            x0, [fp, #-8]
    // 0xeae0d0: r16 = 1040
    //     0xeae0d0: movz            x16, #0x410
    // 0xeae0d4: StoreField: r0->field_f = r16
    //     0xeae0d4: stur            w16, [x0, #0xf]
    // 0xeae0d8: r16 = 268698624
    //     0xeae0d8: movz            x16, #0x400
    //     0xeae0dc: movk            x16, #0x1004, lsl #16
    // 0xeae0e0: StoreField: r0->field_13 = r16
    //     0xeae0e0: stur            w16, [x0, #0x13]
    // 0xeae0e4: ArrayStore: r0[0] = rZR  ; List_4
    //     0xeae0e4: stur            wzr, [x0, #0x17]
    // 0xeae0e8: r16 = 268697616
    //     0xeae0e8: movz            x16, #0x10
    //     0xeae0ec: movk            x16, #0x1004, lsl #16
    // 0xeae0f0: StoreField: r0->field_1b = r16
    //     0xeae0f0: stur            w16, [x0, #0x1b]
    // 0xeae0f4: r16 = 268436480
    //     0xeae0f4: movz            x16, #0x400
    //     0xeae0f8: movk            x16, #0x1000, lsl #16
    // 0xeae0fc: StoreField: r0->field_1f = r16
    //     0xeae0fc: stur            w16, [x0, #0x1f]
    // 0xeae100: StoreField: r0->field_23 = rZR
    //     0xeae100: stur            wzr, [x0, #0x23]
    // 0xeae104: r16 = 263184
    //     0xeae104: movz            x16, #0x410
    //     0xeae108: movk            x16, #0x4, lsl #16
    // 0xeae10c: StoreField: r0->field_27 = r16
    //     0xeae10c: stur            w16, [x0, #0x27]
    // 0xeae110: r16 = 268436480
    //     0xeae110: movz            x16, #0x400
    //     0xeae114: movk            x16, #0x1000, lsl #16
    // 0xeae118: StoreField: r0->field_2b = r16
    //     0xeae118: stur            w16, [x0, #0x2b]
    // 0xeae11c: r16 = 262160
    //     0xeae11c: movz            x16, #0x10
    //     0xeae120: movk            x16, #0x4, lsl #16
    // 0xeae124: StoreField: r0->field_2f = r16
    //     0xeae124: stur            w16, [x0, #0x2f]
    // 0xeae128: r16 = 268435472
    //     0xeae128: movz            x16, #0x10
    //     0xeae12c: movk            x16, #0x1000, lsl #16
    // 0xeae130: StoreField: r0->field_33 = r16
    //     0xeae130: stur            w16, [x0, #0x33]
    // 0xeae134: r16 = 268435472
    //     0xeae134: movz            x16, #0x10
    //     0xeae138: movk            x16, #0x1000, lsl #16
    // 0xeae13c: StoreField: r0->field_37 = r16
    //     0xeae13c: stur            w16, [x0, #0x37]
    // 0xeae140: r16 = 4
    //     0xeae140: movz            x16, #0x4, lsl #16
    // 0xeae144: StoreField: r0->field_3b = r16
    //     0xeae144: stur            w16, [x0, #0x3b]
    // 0xeae148: r16 = 268698640
    //     0xeae148: movz            x16, #0x410
    //     0xeae14c: movk            x16, #0x1004, lsl #16
    // 0xeae150: StoreField: r0->field_3f = r16
    //     0xeae150: stur            w16, [x0, #0x3f]
    // 0xeae154: r16 = 262160
    //     0xeae154: movz            x16, #0x10
    //     0xeae158: movk            x16, #0x4, lsl #16
    // 0xeae15c: StoreField: r0->field_43 = r16
    //     0xeae15c: stur            w16, [x0, #0x43]
    // 0xeae160: r16 = 4100
    //     0xeae160: movz            x16, #0x1004, lsl #16
    // 0xeae164: StoreField: r0->field_47 = r16
    //     0xeae164: stur            w16, [x0, #0x47]
    // 0xeae168: r16 = 1040
    //     0xeae168: movz            x16, #0x410
    // 0xeae16c: StoreField: r0->field_4b = r16
    //     0xeae16c: stur            w16, [x0, #0x4b]
    // 0xeae170: r16 = 268435456
    //     0xeae170: orr             x16, xzr, #0x10000000
    // 0xeae174: StoreField: r0->field_4f = r16
    //     0xeae174: stur            w16, [x0, #0x4f]
    // 0xeae178: r16 = 16
    //     0xeae178: movz            x16, #0x10
    // 0xeae17c: StoreField: r0->field_53 = r16
    //     0xeae17c: stur            w16, [x0, #0x53]
    // 0xeae180: r16 = 268698624
    //     0xeae180: movz            x16, #0x400
    //     0xeae184: movk            x16, #0x1004, lsl #16
    // 0xeae188: StoreField: r0->field_57 = r16
    //     0xeae188: stur            w16, [x0, #0x57]
    // 0xeae18c: r16 = 1024
    //     0xeae18c: movz            x16, #0x400
    // 0xeae190: StoreField: r0->field_5b = r16
    //     0xeae190: stur            w16, [x0, #0x5b]
    // 0xeae194: r16 = 263168
    //     0xeae194: movz            x16, #0x400
    //     0xeae198: movk            x16, #0x4, lsl #16
    // 0xeae19c: StoreField: r0->field_5f = r16
    //     0xeae19c: stur            w16, [x0, #0x5f]
    // 0xeae1a0: r16 = 4100
    //     0xeae1a0: movz            x16, #0x1004, lsl #16
    // 0xeae1a4: StoreField: r0->field_63 = r16
    //     0xeae1a4: stur            w16, [x0, #0x63]
    // 0xeae1a8: r16 = 268697616
    //     0xeae1a8: movz            x16, #0x10
    //     0xeae1ac: movk            x16, #0x1004, lsl #16
    // 0xeae1b0: StoreField: r0->field_67 = r16
    //     0xeae1b0: stur            w16, [x0, #0x67]
    // 0xeae1b4: r16 = 263184
    //     0xeae1b4: movz            x16, #0x410
    //     0xeae1b8: movk            x16, #0x4, lsl #16
    // 0xeae1bc: StoreField: r0->field_6b = r16
    //     0xeae1bc: stur            w16, [x0, #0x6b]
    // 0xeae1c0: r16 = 268436496
    //     0xeae1c0: movz            x16, #0x410
    //     0xeae1c4: movk            x16, #0x1000, lsl #16
    // 0xeae1c8: StoreField: r0->field_6f = r16
    //     0xeae1c8: stur            w16, [x0, #0x6f]
    // 0xeae1cc: r16 = 263168
    //     0xeae1cc: movz            x16, #0x400
    //     0xeae1d0: movk            x16, #0x4, lsl #16
    // 0xeae1d4: StoreField: r0->field_73 = r16
    //     0xeae1d4: stur            w16, [x0, #0x73]
    // 0xeae1d8: r16 = 4
    //     0xeae1d8: movz            x16, #0x4, lsl #16
    // 0xeae1dc: StoreField: r0->field_77 = r16
    //     0xeae1dc: stur            w16, [x0, #0x77]
    // 0xeae1e0: r16 = 268436496
    //     0xeae1e0: movz            x16, #0x410
    //     0xeae1e4: movk            x16, #0x1000, lsl #16
    // 0xeae1e8: StoreField: r0->field_7b = r16
    //     0xeae1e8: stur            w16, [x0, #0x7b]
    // 0xeae1ec: r16 = 16
    //     0xeae1ec: movz            x16, #0x10
    // 0xeae1f0: StoreField: r0->field_7f = r16
    //     0xeae1f0: stur            w16, [x0, #0x7f]
    // 0xeae1f4: r16 = 268698640
    //     0xeae1f4: movz            x16, #0x410
    //     0xeae1f8: movk            x16, #0x1004, lsl #16
    // 0xeae1fc: StoreField: r0->field_83 = r16
    //     0xeae1fc: stur            w16, [x0, #0x83]
    // 0xeae200: r16 = 1024
    //     0xeae200: movz            x16, #0x400
    // 0xeae204: StoreField: r0->field_87 = r16
    //     0xeae204: stur            w16, [x0, #0x87]
    // 0xeae208: r16 = 268435456
    //     0xeae208: orr             x16, xzr, #0x10000000
    // 0xeae20c: StoreField: r0->field_8b = r16
    //     0xeae20c: stur            w16, [x0, #0x8b]
    // 0xeae210: r16 = 268698624
    //     0xeae210: movz            x16, #0x400
    //     0xeae214: movk            x16, #0x1004, lsl #16
    // 0xeae218: StoreField: r0->field_8f = r16
    //     0xeae218: stur            w16, [x0, #0x8f]
    // 0xeae21c: r16 = 268435456
    //     0xeae21c: orr             x16, xzr, #0x10000000
    // 0xeae220: StoreField: r0->field_93 = r16
    //     0xeae220: stur            w16, [x0, #0x93]
    // 0xeae224: r16 = 262160
    //     0xeae224: movz            x16, #0x10
    //     0xeae228: movk            x16, #0x4, lsl #16
    // 0xeae22c: StoreField: r0->field_97 = r16
    //     0xeae22c: stur            w16, [x0, #0x97]
    // 0xeae230: r16 = 1040
    //     0xeae230: movz            x16, #0x410
    // 0xeae234: StoreField: r0->field_9b = r16
    //     0xeae234: stur            w16, [x0, #0x9b]
    // 0xeae238: r16 = 4
    //     0xeae238: movz            x16, #0x4, lsl #16
    // 0xeae23c: StoreField: r0->field_9f = r16
    //     0xeae23c: stur            w16, [x0, #0x9f]
    // 0xeae240: r16 = 268698624
    //     0xeae240: movz            x16, #0x400
    //     0xeae244: movk            x16, #0x1004, lsl #16
    // 0xeae248: StoreField: r0->field_a3 = r16
    //     0xeae248: stur            w16, [x0, #0xa3]
    // 0xeae24c: r16 = 268436480
    //     0xeae24c: movz            x16, #0x400
    //     0xeae250: movk            x16, #0x1000, lsl #16
    // 0xeae254: StoreField: r0->field_a7 = r16
    //     0xeae254: stur            w16, [x0, #0xa7]
    // 0xeae258: StoreField: r0->field_ab = rZR
    //     0xeae258: stur            wzr, [x0, #0xab]
    // 0xeae25c: r16 = 1024
    //     0xeae25c: movz            x16, #0x400
    // 0xeae260: StoreField: r0->field_af = r16
    //     0xeae260: stur            w16, [x0, #0xaf]
    // 0xeae264: r16 = 262160
    //     0xeae264: movz            x16, #0x10
    //     0xeae268: movk            x16, #0x4, lsl #16
    // 0xeae26c: StoreField: r0->field_b3 = r16
    //     0xeae26c: stur            w16, [x0, #0xb3]
    // 0xeae270: r16 = 268698640
    //     0xeae270: movz            x16, #0x410
    //     0xeae274: movk            x16, #0x1004, lsl #16
    // 0xeae278: StoreField: r0->field_b7 = r16
    //     0xeae278: stur            w16, [x0, #0xb7]
    // 0xeae27c: r16 = 268436480
    //     0xeae27c: movz            x16, #0x400
    //     0xeae280: movk            x16, #0x1000, lsl #16
    // 0xeae284: StoreField: r0->field_bb = r16
    //     0xeae284: stur            w16, [x0, #0xbb]
    // 0xeae288: r16 = 268435472
    //     0xeae288: movz            x16, #0x10
    //     0xeae28c: movk            x16, #0x1000, lsl #16
    // 0xeae290: StoreField: r0->field_bf = r16
    //     0xeae290: stur            w16, [x0, #0xbf]
    // 0xeae294: r16 = 1024
    //     0xeae294: movz            x16, #0x400
    // 0xeae298: StoreField: r0->field_c3 = r16
    //     0xeae298: stur            w16, [x0, #0xc3]
    // 0xeae29c: StoreField: r0->field_c7 = rZR
    //     0xeae29c: stur            wzr, [x0, #0xc7]
    // 0xeae2a0: r16 = 268697616
    //     0xeae2a0: movz            x16, #0x10
    //     0xeae2a4: movk            x16, #0x1004, lsl #16
    // 0xeae2a8: StoreField: r0->field_cb = r16
    //     0xeae2a8: stur            w16, [x0, #0xcb]
    // 0xeae2ac: r16 = 268436496
    //     0xeae2ac: movz            x16, #0x410
    //     0xeae2b0: movk            x16, #0x1000, lsl #16
    // 0xeae2b4: StoreField: r0->field_cf = r16
    //     0xeae2b4: stur            w16, [x0, #0xcf]
    // 0xeae2b8: r16 = 4
    //     0xeae2b8: movz            x16, #0x4, lsl #16
    // 0xeae2bc: StoreField: r0->field_d3 = r16
    //     0xeae2bc: stur            w16, [x0, #0xd3]
    // 0xeae2c0: r16 = 268435456
    //     0xeae2c0: orr             x16, xzr, #0x10000000
    // 0xeae2c4: StoreField: r0->field_d7 = r16
    //     0xeae2c4: stur            w16, [x0, #0xd7]
    // 0xeae2c8: r16 = 268698640
    //     0xeae2c8: movz            x16, #0x410
    //     0xeae2cc: movk            x16, #0x1004, lsl #16
    // 0xeae2d0: StoreField: r0->field_db = r16
    //     0xeae2d0: stur            w16, [x0, #0xdb]
    // 0xeae2d4: r16 = 16
    //     0xeae2d4: movz            x16, #0x10
    // 0xeae2d8: StoreField: r0->field_df = r16
    //     0xeae2d8: stur            w16, [x0, #0xdf]
    // 0xeae2dc: r16 = 263184
    //     0xeae2dc: movz            x16, #0x410
    //     0xeae2e0: movk            x16, #0x4, lsl #16
    // 0xeae2e4: StoreField: r0->field_e3 = r16
    //     0xeae2e4: stur            w16, [x0, #0xe3]
    // 0xeae2e8: r16 = 263168
    //     0xeae2e8: movz            x16, #0x400
    //     0xeae2ec: movk            x16, #0x4, lsl #16
    // 0xeae2f0: StoreField: r0->field_e7 = r16
    //     0xeae2f0: stur            w16, [x0, #0xe7]
    // 0xeae2f4: r16 = 268435472
    //     0xeae2f4: movz            x16, #0x10
    //     0xeae2f8: movk            x16, #0x1000, lsl #16
    // 0xeae2fc: StoreField: r0->field_eb = r16
    //     0xeae2fc: stur            w16, [x0, #0xeb]
    // 0xeae300: r16 = 4100
    //     0xeae300: movz            x16, #0x1004, lsl #16
    // 0xeae304: StoreField: r0->field_ef = r16
    //     0xeae304: stur            w16, [x0, #0xef]
    // 0xeae308: r16 = 268436496
    //     0xeae308: movz            x16, #0x410
    //     0xeae30c: movk            x16, #0x1000, lsl #16
    // 0xeae310: StoreField: r0->field_f3 = r16
    //     0xeae310: stur            w16, [x0, #0xf3]
    // 0xeae314: r16 = 1040
    //     0xeae314: movz            x16, #0x410
    // 0xeae318: StoreField: r0->field_f7 = r16
    //     0xeae318: stur            w16, [x0, #0xf7]
    // 0xeae31c: r16 = 4100
    //     0xeae31c: movz            x16, #0x1004, lsl #16
    // 0xeae320: StoreField: r0->field_fb = r16
    //     0xeae320: stur            w16, [x0, #0xfb]
    // 0xeae324: r16 = 263184
    //     0xeae324: movz            x16, #0x410
    //     0xeae328: movk            x16, #0x4, lsl #16
    // 0xeae32c: StoreField: r0->field_ff = r16
    //     0xeae32c: stur            w16, [x0, #0xff]
    // 0xeae330: r1 = 122
    //     0xeae330: movz            x1, #0x7a
    // 0xeae334: add             x2, x0, w1, sxtw #1
    // 0xeae338: r16 = 16
    //     0xeae338: movz            x16, #0x10
    // 0xeae33c: StoreField: r2->field_f = r16
    //     0xeae33c: stur            w16, [x2, #0xf]
    // 0xeae340: r1 = 124
    //     0xeae340: movz            x1, #0x7c
    // 0xeae344: add             x2, x0, w1, sxtw #1
    // 0xeae348: r16 = 268697616
    //     0xeae348: movz            x16, #0x10
    //     0xeae34c: movk            x16, #0x1004, lsl #16
    // 0xeae350: StoreField: r2->field_f = r16
    //     0xeae350: stur            w16, [x2, #0xf]
    // 0xeae354: r1 = 126
    //     0xeae354: movz            x1, #0x7e
    // 0xeae358: add             x2, x0, w1, sxtw #1
    // 0xeae35c: r16 = 263168
    //     0xeae35c: movz            x16, #0x400
    //     0xeae360: movk            x16, #0x4, lsl #16
    // 0xeae364: StoreField: r2->field_f = r16
    //     0xeae364: stur            w16, [x2, #0xf]
    // 0xeae368: r1 = <int>
    //     0xeae368: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeae36c: r0 = AllocateGrowableArray()
    //     0xeae36c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xeae370: ldur            x1, [fp, #-8]
    // 0xeae374: StoreField: r0->field_f = r1
    //     0xeae374: stur            w1, [x0, #0xf]
    // 0xeae378: r1 = 128
    //     0xeae378: movz            x1, #0x80
    // 0xeae37c: StoreField: r0->field_b = r1
    //     0xeae37c: stur            w1, [x0, #0xb]
    // 0xeae380: LeaveFrame
    //     0xeae380: mov             SP, fp
    //     0xeae384: ldp             fp, lr, [SP], #0x10
    // 0xeae388: ret
    //     0xeae388: ret             
  }
  static List<int> SP5() {
    // ** addr: 0xeae38c, size: 0x2fc
    // 0xeae38c: EnterFrame
    //     0xeae38c: stp             fp, lr, [SP, #-0x10]!
    //     0xeae390: mov             fp, SP
    // 0xeae394: AllocStack(0x8)
    //     0xeae394: sub             SP, SP, #8
    // 0xeae398: r0 = 128
    //     0xeae398: movz            x0, #0x80
    // 0xeae39c: mov             x2, x0
    // 0xeae3a0: r1 = <int>
    //     0xeae3a0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeae3a4: r0 = AllocateArray()
    //     0xeae3a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeae3a8: stur            x0, [fp, #-8]
    // 0xeae3ac: r16 = 512
    //     0xeae3ac: movz            x16, #0x200
    // 0xeae3b0: StoreField: r0->field_f = r16
    //     0xeae3b0: stur            w16, [x0, #0xf]
    // 0xeae3b4: r16 = 68157952
    //     0xeae3b4: movz            x16, #0x200
    //     0xeae3b8: movk            x16, #0x410, lsl #16
    // 0xeae3bc: StoreField: r0->field_13 = r16
    //     0xeae3bc: stur            w16, [x0, #0x13]
    // 0xeae3c0: r16 = 1040
    //     0xeae3c0: movz            x16, #0x410, lsl #16
    // 0xeae3c4: ArrayStore: r0[0] = r16  ; List_4
    //     0xeae3c4: stur            w16, [x0, #0x17]
    // 0xeae3c8: r16 = 1107296512
    //     0xeae3c8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23748] 0x42000100
    //     0xeae3cc: ldr             x16, [x16, #0x748]
    // 0xeae3d0: StoreField: r0->field_1b = r16
    //     0xeae3d0: stur            w16, [x0, #0x1b]
    // 0xeae3d4: r16 = 16
    //     0xeae3d4: movz            x16, #0x10, lsl #16
    // 0xeae3d8: StoreField: r0->field_1f = r16
    //     0xeae3d8: stur            w16, [x0, #0x1f]
    // 0xeae3dc: r16 = 512
    //     0xeae3dc: movz            x16, #0x200
    // 0xeae3e0: StoreField: r0->field_23 = r16
    //     0xeae3e0: stur            w16, [x0, #0x23]
    // 0xeae3e4: r16 = 1073741824
    //     0xeae3e4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23750] 0x40000000
    //     0xeae3e8: ldr             x16, [x16, #0x750]
    // 0xeae3ec: StoreField: r0->field_27 = r16
    //     0xeae3ec: stur            w16, [x0, #0x27]
    // 0xeae3f0: r16 = 1040
    //     0xeae3f0: movz            x16, #0x410, lsl #16
    // 0xeae3f4: StoreField: r0->field_2b = r16
    //     0xeae3f4: stur            w16, [x0, #0x2b]
    // 0xeae3f8: r16 = 1074266368
    //     0xeae3f8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23758] 0x40080100
    //     0xeae3fc: ldr             x16, [x16, #0x758]
    // 0xeae400: StoreField: r0->field_2f = r16
    //     0xeae400: stur            w16, [x0, #0x2f]
    // 0xeae404: r16 = 16
    //     0xeae404: movz            x16, #0x10, lsl #16
    // 0xeae408: StoreField: r0->field_33 = r16
    //     0xeae408: stur            w16, [x0, #0x33]
    // 0xeae40c: r16 = 67109376
    //     0xeae40c: movz            x16, #0x200
    //     0xeae410: movk            x16, #0x400, lsl #16
    // 0xeae414: StoreField: r0->field_37 = r16
    //     0xeae414: stur            w16, [x0, #0x37]
    // 0xeae418: r16 = 1074266368
    //     0xeae418: add             x16, PP, #0x23, lsl #12  ; [pp+0x23758] 0x40080100
    //     0xeae41c: ldr             x16, [x16, #0x758]
    // 0xeae420: StoreField: r0->field_3b = r16
    //     0xeae420: stur            w16, [x0, #0x3b]
    // 0xeae424: r16 = 1107296512
    //     0xeae424: add             x16, PP, #0x23, lsl #12  ; [pp+0x23748] 0x42000100
    //     0xeae428: ldr             x16, [x16, #0x748]
    // 0xeae42c: StoreField: r0->field_3f = r16
    //     0xeae42c: stur            w16, [x0, #0x3f]
    // 0xeae430: r16 = 1107820544
    //     0xeae430: add             x16, PP, #0x23, lsl #12  ; [pp+0x23760] 0x42080000
    //     0xeae434: ldr             x16, [x16, #0x760]
    // 0xeae438: StoreField: r0->field_43 = r16
    //     0xeae438: stur            w16, [x0, #0x43]
    // 0xeae43c: r16 = 1049088
    //     0xeae43c: movz            x16, #0x200
    //     0xeae440: movk            x16, #0x10, lsl #16
    // 0xeae444: StoreField: r0->field_47 = r16
    //     0xeae444: stur            w16, [x0, #0x47]
    // 0xeae448: r16 = 1073741824
    //     0xeae448: add             x16, PP, #0x23, lsl #12  ; [pp+0x23750] 0x40000000
    //     0xeae44c: ldr             x16, [x16, #0x750]
    // 0xeae450: StoreField: r0->field_4b = r16
    //     0xeae450: stur            w16, [x0, #0x4b]
    // 0xeae454: r16 = 67108864
    //     0xeae454: orr             x16, xzr, #0x4000000
    // 0xeae458: StoreField: r0->field_4f = r16
    //     0xeae458: stur            w16, [x0, #0x4f]
    // 0xeae45c: r16 = 1074266112
    //     0xeae45c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23768] 0x40080000
    //     0xeae460: ldr             x16, [x16, #0x768]
    // 0xeae464: StoreField: r0->field_53 = r16
    //     0xeae464: stur            w16, [x0, #0x53]
    // 0xeae468: r16 = 1074266112
    //     0xeae468: add             x16, PP, #0x23, lsl #12  ; [pp+0x23768] 0x40080000
    //     0xeae46c: ldr             x16, [x16, #0x768]
    // 0xeae470: StoreField: r0->field_57 = r16
    //     0xeae470: stur            w16, [x0, #0x57]
    // 0xeae474: StoreField: r0->field_5b = rZR
    //     0xeae474: stur            wzr, [x0, #0x5b]
    // 0xeae478: r16 = 1073742080
    //     0xeae478: add             x16, PP, #0x23, lsl #12  ; [pp+0x23770] 0x40000100
    //     0xeae47c: ldr             x16, [x16, #0x770]
    // 0xeae480: StoreField: r0->field_5f = r16
    //     0xeae480: stur            w16, [x0, #0x5f]
    // 0xeae484: r16 = 1107820800
    //     0xeae484: add             x16, PP, #0x23, lsl #12  ; [pp+0x23778] 0x42080100
    //     0xeae488: ldr             x16, [x16, #0x778]
    // 0xeae48c: StoreField: r0->field_63 = r16
    //     0xeae48c: stur            w16, [x0, #0x63]
    // 0xeae490: r16 = 1107820800
    //     0xeae490: add             x16, PP, #0x23, lsl #12  ; [pp+0x23778] 0x42080100
    //     0xeae494: ldr             x16, [x16, #0x778]
    // 0xeae498: StoreField: r0->field_67 = r16
    //     0xeae498: stur            w16, [x0, #0x67]
    // 0xeae49c: r16 = 67109376
    //     0xeae49c: movz            x16, #0x200
    //     0xeae4a0: movk            x16, #0x400, lsl #16
    // 0xeae4a4: StoreField: r0->field_6b = r16
    //     0xeae4a4: stur            w16, [x0, #0x6b]
    // 0xeae4a8: r16 = 1107820544
    //     0xeae4a8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23760] 0x42080000
    //     0xeae4ac: ldr             x16, [x16, #0x760]
    // 0xeae4b0: StoreField: r0->field_6f = r16
    //     0xeae4b0: stur            w16, [x0, #0x6f]
    // 0xeae4b4: r16 = 1073742080
    //     0xeae4b4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23770] 0x40000100
    //     0xeae4b8: ldr             x16, [x16, #0x770]
    // 0xeae4bc: StoreField: r0->field_73 = r16
    //     0xeae4bc: stur            w16, [x0, #0x73]
    // 0xeae4c0: StoreField: r0->field_77 = rZR
    //     0xeae4c0: stur            wzr, [x0, #0x77]
    // 0xeae4c4: r16 = 1107296256
    //     0xeae4c4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23780] 0x42000000
    //     0xeae4c8: ldr             x16, [x16, #0x780]
    // 0xeae4cc: StoreField: r0->field_7b = r16
    //     0xeae4cc: stur            w16, [x0, #0x7b]
    // 0xeae4d0: r16 = 68157952
    //     0xeae4d0: movz            x16, #0x200
    //     0xeae4d4: movk            x16, #0x410, lsl #16
    // 0xeae4d8: StoreField: r0->field_7f = r16
    //     0xeae4d8: stur            w16, [x0, #0x7f]
    // 0xeae4dc: r16 = 67108864
    //     0xeae4dc: orr             x16, xzr, #0x4000000
    // 0xeae4e0: StoreField: r0->field_83 = r16
    //     0xeae4e0: stur            w16, [x0, #0x83]
    // 0xeae4e4: r16 = 1107296256
    //     0xeae4e4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23780] 0x42000000
    //     0xeae4e8: ldr             x16, [x16, #0x780]
    // 0xeae4ec: StoreField: r0->field_87 = r16
    //     0xeae4ec: stur            w16, [x0, #0x87]
    // 0xeae4f0: r16 = 1049088
    //     0xeae4f0: movz            x16, #0x200
    //     0xeae4f4: movk            x16, #0x10, lsl #16
    // 0xeae4f8: StoreField: r0->field_8b = r16
    //     0xeae4f8: stur            w16, [x0, #0x8b]
    // 0xeae4fc: r16 = 16
    //     0xeae4fc: movz            x16, #0x10, lsl #16
    // 0xeae500: StoreField: r0->field_8f = r16
    //     0xeae500: stur            w16, [x0, #0x8f]
    // 0xeae504: r16 = 1107296512
    //     0xeae504: add             x16, PP, #0x23, lsl #12  ; [pp+0x23748] 0x42000100
    //     0xeae508: ldr             x16, [x16, #0x748]
    // 0xeae50c: StoreField: r0->field_93 = r16
    //     0xeae50c: stur            w16, [x0, #0x93]
    // 0xeae510: r16 = 512
    //     0xeae510: movz            x16, #0x200
    // 0xeae514: StoreField: r0->field_97 = r16
    //     0xeae514: stur            w16, [x0, #0x97]
    // 0xeae518: r16 = 67108864
    //     0xeae518: orr             x16, xzr, #0x4000000
    // 0xeae51c: StoreField: r0->field_9b = r16
    //     0xeae51c: stur            w16, [x0, #0x9b]
    // 0xeae520: r16 = 1073741824
    //     0xeae520: add             x16, PP, #0x23, lsl #12  ; [pp+0x23750] 0x40000000
    //     0xeae524: ldr             x16, [x16, #0x750]
    // 0xeae528: StoreField: r0->field_9f = r16
    //     0xeae528: stur            w16, [x0, #0x9f]
    // 0xeae52c: r16 = 1040
    //     0xeae52c: movz            x16, #0x410, lsl #16
    // 0xeae530: StoreField: r0->field_a3 = r16
    //     0xeae530: stur            w16, [x0, #0xa3]
    // 0xeae534: r16 = 1107296512
    //     0xeae534: add             x16, PP, #0x23, lsl #12  ; [pp+0x23748] 0x42000100
    //     0xeae538: ldr             x16, [x16, #0x748]
    // 0xeae53c: StoreField: r0->field_a7 = r16
    //     0xeae53c: stur            w16, [x0, #0xa7]
    // 0xeae540: r16 = 1074266368
    //     0xeae540: add             x16, PP, #0x23, lsl #12  ; [pp+0x23758] 0x40080100
    //     0xeae544: ldr             x16, [x16, #0x758]
    // 0xeae548: StoreField: r0->field_ab = r16
    //     0xeae548: stur            w16, [x0, #0xab]
    // 0xeae54c: r16 = 67109376
    //     0xeae54c: movz            x16, #0x200
    //     0xeae550: movk            x16, #0x400, lsl #16
    // 0xeae554: StoreField: r0->field_af = r16
    //     0xeae554: stur            w16, [x0, #0xaf]
    // 0xeae558: r16 = 1073741824
    //     0xeae558: add             x16, PP, #0x23, lsl #12  ; [pp+0x23750] 0x40000000
    //     0xeae55c: ldr             x16, [x16, #0x750]
    // 0xeae560: StoreField: r0->field_b3 = r16
    //     0xeae560: stur            w16, [x0, #0xb3]
    // 0xeae564: r16 = 1107820544
    //     0xeae564: add             x16, PP, #0x23, lsl #12  ; [pp+0x23760] 0x42080000
    //     0xeae568: ldr             x16, [x16, #0x760]
    // 0xeae56c: StoreField: r0->field_b7 = r16
    //     0xeae56c: stur            w16, [x0, #0xb7]
    // 0xeae570: r16 = 68157952
    //     0xeae570: movz            x16, #0x200
    //     0xeae574: movk            x16, #0x410, lsl #16
    // 0xeae578: StoreField: r0->field_bb = r16
    //     0xeae578: stur            w16, [x0, #0xbb]
    // 0xeae57c: r16 = 1074266368
    //     0xeae57c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23758] 0x40080100
    //     0xeae580: ldr             x16, [x16, #0x758]
    // 0xeae584: StoreField: r0->field_bf = r16
    //     0xeae584: stur            w16, [x0, #0xbf]
    // 0xeae588: r16 = 512
    //     0xeae588: movz            x16, #0x200
    // 0xeae58c: StoreField: r0->field_c3 = r16
    //     0xeae58c: stur            w16, [x0, #0xc3]
    // 0xeae590: r16 = 67108864
    //     0xeae590: orr             x16, xzr, #0x4000000
    // 0xeae594: StoreField: r0->field_c7 = r16
    //     0xeae594: stur            w16, [x0, #0xc7]
    // 0xeae598: r16 = 1107820544
    //     0xeae598: add             x16, PP, #0x23, lsl #12  ; [pp+0x23760] 0x42080000
    //     0xeae59c: ldr             x16, [x16, #0x760]
    // 0xeae5a0: StoreField: r0->field_cb = r16
    //     0xeae5a0: stur            w16, [x0, #0xcb]
    // 0xeae5a4: r16 = 1107820800
    //     0xeae5a4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23778] 0x42080100
    //     0xeae5a8: ldr             x16, [x16, #0x778]
    // 0xeae5ac: StoreField: r0->field_cf = r16
    //     0xeae5ac: stur            w16, [x0, #0xcf]
    // 0xeae5b0: r16 = 1049088
    //     0xeae5b0: movz            x16, #0x200
    //     0xeae5b4: movk            x16, #0x10, lsl #16
    // 0xeae5b8: StoreField: r0->field_d3 = r16
    //     0xeae5b8: stur            w16, [x0, #0xd3]
    // 0xeae5bc: r16 = 1107296256
    //     0xeae5bc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23780] 0x42000000
    //     0xeae5c0: ldr             x16, [x16, #0x780]
    // 0xeae5c4: StoreField: r0->field_d7 = r16
    //     0xeae5c4: stur            w16, [x0, #0xd7]
    // 0xeae5c8: r16 = 1107820800
    //     0xeae5c8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23778] 0x42080100
    //     0xeae5cc: ldr             x16, [x16, #0x778]
    // 0xeae5d0: StoreField: r0->field_db = r16
    //     0xeae5d0: stur            w16, [x0, #0xdb]
    // 0xeae5d4: r16 = 1040
    //     0xeae5d4: movz            x16, #0x410, lsl #16
    // 0xeae5d8: StoreField: r0->field_df = r16
    //     0xeae5d8: stur            w16, [x0, #0xdf]
    // 0xeae5dc: StoreField: r0->field_e3 = rZR
    //     0xeae5dc: stur            wzr, [x0, #0xe3]
    // 0xeae5e0: r16 = 1074266112
    //     0xeae5e0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23768] 0x40080000
    //     0xeae5e4: ldr             x16, [x16, #0x768]
    // 0xeae5e8: StoreField: r0->field_e7 = r16
    //     0xeae5e8: stur            w16, [x0, #0xe7]
    // 0xeae5ec: r16 = 1107296256
    //     0xeae5ec: add             x16, PP, #0x23, lsl #12  ; [pp+0x23780] 0x42000000
    //     0xeae5f0: ldr             x16, [x16, #0x780]
    // 0xeae5f4: StoreField: r0->field_eb = r16
    //     0xeae5f4: stur            w16, [x0, #0xeb]
    // 0xeae5f8: r16 = 1049088
    //     0xeae5f8: movz            x16, #0x200
    //     0xeae5fc: movk            x16, #0x10, lsl #16
    // 0xeae600: StoreField: r0->field_ef = r16
    //     0xeae600: stur            w16, [x0, #0xef]
    // 0xeae604: r16 = 67109376
    //     0xeae604: movz            x16, #0x200
    //     0xeae608: movk            x16, #0x400, lsl #16
    // 0xeae60c: StoreField: r0->field_f3 = r16
    //     0xeae60c: stur            w16, [x0, #0xf3]
    // 0xeae610: r16 = 1073742080
    //     0xeae610: add             x16, PP, #0x23, lsl #12  ; [pp+0x23770] 0x40000100
    //     0xeae614: ldr             x16, [x16, #0x770]
    // 0xeae618: StoreField: r0->field_f7 = r16
    //     0xeae618: stur            w16, [x0, #0xf7]
    // 0xeae61c: r16 = 16
    //     0xeae61c: movz            x16, #0x10, lsl #16
    // 0xeae620: StoreField: r0->field_fb = r16
    //     0xeae620: stur            w16, [x0, #0xfb]
    // 0xeae624: StoreField: r0->field_ff = rZR
    //     0xeae624: stur            wzr, [x0, #0xff]
    // 0xeae628: r1 = 122
    //     0xeae628: movz            x1, #0x7a
    // 0xeae62c: add             x2, x0, w1, sxtw #1
    // 0xeae630: r16 = 1074266112
    //     0xeae630: add             x16, PP, #0x23, lsl #12  ; [pp+0x23768] 0x40080000
    //     0xeae634: ldr             x16, [x16, #0x768]
    // 0xeae638: StoreField: r2->field_f = r16
    //     0xeae638: stur            w16, [x2, #0xf]
    // 0xeae63c: r1 = 124
    //     0xeae63c: movz            x1, #0x7c
    // 0xeae640: add             x2, x0, w1, sxtw #1
    // 0xeae644: r16 = 68157952
    //     0xeae644: movz            x16, #0x200
    //     0xeae648: movk            x16, #0x410, lsl #16
    // 0xeae64c: StoreField: r2->field_f = r16
    //     0xeae64c: stur            w16, [x2, #0xf]
    // 0xeae650: r1 = 126
    //     0xeae650: movz            x1, #0x7e
    // 0xeae654: add             x2, x0, w1, sxtw #1
    // 0xeae658: r16 = 1073742080
    //     0xeae658: add             x16, PP, #0x23, lsl #12  ; [pp+0x23770] 0x40000100
    //     0xeae65c: ldr             x16, [x16, #0x770]
    // 0xeae660: StoreField: r2->field_f = r16
    //     0xeae660: stur            w16, [x2, #0xf]
    // 0xeae664: r1 = <int>
    //     0xeae664: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeae668: r0 = AllocateGrowableArray()
    //     0xeae668: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xeae66c: ldur            x1, [fp, #-8]
    // 0xeae670: StoreField: r0->field_f = r1
    //     0xeae670: stur            w1, [x0, #0xf]
    // 0xeae674: r1 = 128
    //     0xeae674: movz            x1, #0x80
    // 0xeae678: StoreField: r0->field_b = r1
    //     0xeae678: stur            w1, [x0, #0xb]
    // 0xeae67c: LeaveFrame
    //     0xeae67c: mov             SP, fp
    //     0xeae680: ldp             fp, lr, [SP], #0x10
    // 0xeae684: ret
    //     0xeae684: ret             
  }
  static List<int> SP7() {
    // ** addr: 0xeae688, size: 0x2dc
    // 0xeae688: EnterFrame
    //     0xeae688: stp             fp, lr, [SP, #-0x10]!
    //     0xeae68c: mov             fp, SP
    // 0xeae690: AllocStack(0x8)
    //     0xeae690: sub             SP, SP, #8
    // 0xeae694: r0 = 128
    //     0xeae694: movz            x0, #0x80
    // 0xeae698: mov             x2, x0
    // 0xeae69c: r1 = <int>
    //     0xeae69c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeae6a0: r0 = AllocateArray()
    //     0xeae6a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeae6a4: stur            x0, [fp, #-8]
    // 0xeae6a8: r16 = 64
    //     0xeae6a8: movz            x16, #0x40, lsl #16
    // 0xeae6ac: StoreField: r0->field_f = r16
    //     0xeae6ac: stur            w16, [x0, #0xf]
    // 0xeae6b0: r16 = 138412036
    //     0xeae6b0: movz            x16, #0x4
    //     0xeae6b4: movk            x16, #0x840, lsl #16
    // 0xeae6b8: StoreField: r0->field_13 = r16
    //     0xeae6b8: stur            w16, [x0, #0x13]
    // 0xeae6bc: r16 = 134221828
    //     0xeae6bc: movz            x16, #0x1004
    //     0xeae6c0: movk            x16, #0x800, lsl #16
    // 0xeae6c4: ArrayStore: r0[0] = r16  ; List_4
    //     0xeae6c4: stur            w16, [x0, #0x17]
    // 0xeae6c8: StoreField: r0->field_1b = rZR
    //     0xeae6c8: stur            wzr, [x0, #0x1b]
    // 0xeae6cc: r16 = 4096
    //     0xeae6cc: movz            x16, #0x1000
    // 0xeae6d0: StoreField: r0->field_1f = r16
    //     0xeae6d0: stur            w16, [x0, #0x1f]
    // 0xeae6d4: r16 = 134221828
    //     0xeae6d4: movz            x16, #0x1004
    //     0xeae6d8: movk            x16, #0x800, lsl #16
    // 0xeae6dc: StoreField: r0->field_23 = r16
    //     0xeae6dc: stur            w16, [x0, #0x23]
    // 0xeae6e0: r16 = 4198404
    //     0xeae6e0: movz            x16, #0x1004
    //     0xeae6e4: movk            x16, #0x40, lsl #16
    // 0xeae6e8: StoreField: r0->field_27 = r16
    //     0xeae6e8: stur            w16, [x0, #0x27]
    // 0xeae6ec: r16 = 138416128
    //     0xeae6ec: movz            x16, #0x1000
    //     0xeae6f0: movk            x16, #0x840, lsl #16
    // 0xeae6f4: StoreField: r0->field_2b = r16
    //     0xeae6f4: stur            w16, [x0, #0x2b]
    // 0xeae6f8: r16 = 138416132
    //     0xeae6f8: movz            x16, #0x1004
    //     0xeae6fc: movk            x16, #0x840, lsl #16
    // 0xeae700: StoreField: r0->field_2f = r16
    //     0xeae700: stur            w16, [x0, #0x2f]
    // 0xeae704: r16 = 64
    //     0xeae704: movz            x16, #0x40, lsl #16
    // 0xeae708: StoreField: r0->field_33 = r16
    //     0xeae708: stur            w16, [x0, #0x33]
    // 0xeae70c: StoreField: r0->field_37 = rZR
    //     0xeae70c: stur            wzr, [x0, #0x37]
    // 0xeae710: r16 = 134217732
    //     0xeae710: movz            x16, #0x4
    //     0xeae714: movk            x16, #0x800, lsl #16
    // 0xeae718: StoreField: r0->field_3b = r16
    //     0xeae718: stur            w16, [x0, #0x3b]
    // 0xeae71c: r16 = 4
    //     0xeae71c: movz            x16, #0x4
    // 0xeae720: StoreField: r0->field_3f = r16
    //     0xeae720: stur            w16, [x0, #0x3f]
    // 0xeae724: r16 = 134217728
    //     0xeae724: orr             x16, xzr, #0x8000000
    // 0xeae728: StoreField: r0->field_43 = r16
    //     0xeae728: stur            w16, [x0, #0x43]
    // 0xeae72c: r16 = 138412036
    //     0xeae72c: movz            x16, #0x4
    //     0xeae730: movk            x16, #0x840, lsl #16
    // 0xeae734: StoreField: r0->field_47 = r16
    //     0xeae734: stur            w16, [x0, #0x47]
    // 0xeae738: r16 = 4100
    //     0xeae738: movz            x16, #0x1004
    // 0xeae73c: StoreField: r0->field_4b = r16
    //     0xeae73c: stur            w16, [x0, #0x4b]
    // 0xeae740: r16 = 134221824
    //     0xeae740: movz            x16, #0x1000
    //     0xeae744: movk            x16, #0x800, lsl #16
    // 0xeae748: StoreField: r0->field_4f = r16
    //     0xeae748: stur            w16, [x0, #0x4f]
    // 0xeae74c: r16 = 4198404
    //     0xeae74c: movz            x16, #0x1004
    //     0xeae750: movk            x16, #0x40, lsl #16
    // 0xeae754: StoreField: r0->field_53 = r16
    //     0xeae754: stur            w16, [x0, #0x53]
    // 0xeae758: r16 = 4194308
    //     0xeae758: movz            x16, #0x4
    //     0xeae75c: movk            x16, #0x40, lsl #16
    // 0xeae760: StoreField: r0->field_57 = r16
    //     0xeae760: stur            w16, [x0, #0x57]
    // 0xeae764: r16 = 134221824
    //     0xeae764: movz            x16, #0x1000
    //     0xeae768: movk            x16, #0x800, lsl #16
    // 0xeae76c: StoreField: r0->field_5b = r16
    //     0xeae76c: stur            w16, [x0, #0x5b]
    // 0xeae770: r16 = 134217732
    //     0xeae770: movz            x16, #0x4
    //     0xeae774: movk            x16, #0x800, lsl #16
    // 0xeae778: StoreField: r0->field_5f = r16
    //     0xeae778: stur            w16, [x0, #0x5f]
    // 0xeae77c: r16 = 2112
    //     0xeae77c: movz            x16, #0x840, lsl #16
    // 0xeae780: StoreField: r0->field_63 = r16
    //     0xeae780: stur            w16, [x0, #0x63]
    // 0xeae784: r16 = 138416128
    //     0xeae784: movz            x16, #0x1000
    //     0xeae788: movk            x16, #0x840, lsl #16
    // 0xeae78c: StoreField: r0->field_67 = r16
    //     0xeae78c: stur            w16, [x0, #0x67]
    // 0xeae790: r16 = 4194308
    //     0xeae790: movz            x16, #0x4
    //     0xeae794: movk            x16, #0x40, lsl #16
    // 0xeae798: StoreField: r0->field_6b = r16
    //     0xeae798: stur            w16, [x0, #0x6b]
    // 0xeae79c: r16 = 2112
    //     0xeae79c: movz            x16, #0x840, lsl #16
    // 0xeae7a0: StoreField: r0->field_6f = r16
    //     0xeae7a0: stur            w16, [x0, #0x6f]
    // 0xeae7a4: r16 = 4096
    //     0xeae7a4: movz            x16, #0x1000
    // 0xeae7a8: StoreField: r0->field_73 = r16
    //     0xeae7a8: stur            w16, [x0, #0x73]
    // 0xeae7ac: r16 = 4100
    //     0xeae7ac: movz            x16, #0x1004
    // 0xeae7b0: StoreField: r0->field_77 = r16
    //     0xeae7b0: stur            w16, [x0, #0x77]
    // 0xeae7b4: r16 = 138416132
    //     0xeae7b4: movz            x16, #0x1004
    //     0xeae7b8: movk            x16, #0x840, lsl #16
    // 0xeae7bc: StoreField: r0->field_7b = r16
    //     0xeae7bc: stur            w16, [x0, #0x7b]
    // 0xeae7c0: r16 = 4198400
    //     0xeae7c0: movz            x16, #0x1000
    //     0xeae7c4: movk            x16, #0x40, lsl #16
    // 0xeae7c8: StoreField: r0->field_7f = r16
    //     0xeae7c8: stur            w16, [x0, #0x7f]
    // 0xeae7cc: r16 = 4
    //     0xeae7cc: movz            x16, #0x4
    // 0xeae7d0: StoreField: r0->field_83 = r16
    //     0xeae7d0: stur            w16, [x0, #0x83]
    // 0xeae7d4: r16 = 134217728
    //     0xeae7d4: orr             x16, xzr, #0x8000000
    // 0xeae7d8: StoreField: r0->field_87 = r16
    //     0xeae7d8: stur            w16, [x0, #0x87]
    // 0xeae7dc: r16 = 4198400
    //     0xeae7dc: movz            x16, #0x1000
    //     0xeae7e0: movk            x16, #0x40, lsl #16
    // 0xeae7e4: StoreField: r0->field_8b = r16
    //     0xeae7e4: stur            w16, [x0, #0x8b]
    // 0xeae7e8: r16 = 134217728
    //     0xeae7e8: orr             x16, xzr, #0x8000000
    // 0xeae7ec: StoreField: r0->field_8f = r16
    //     0xeae7ec: stur            w16, [x0, #0x8f]
    // 0xeae7f0: r16 = 4198400
    //     0xeae7f0: movz            x16, #0x1000
    //     0xeae7f4: movk            x16, #0x40, lsl #16
    // 0xeae7f8: StoreField: r0->field_93 = r16
    //     0xeae7f8: stur            w16, [x0, #0x93]
    // 0xeae7fc: r16 = 64
    //     0xeae7fc: movz            x16, #0x40, lsl #16
    // 0xeae800: StoreField: r0->field_97 = r16
    //     0xeae800: stur            w16, [x0, #0x97]
    // 0xeae804: r16 = 134221828
    //     0xeae804: movz            x16, #0x1004
    //     0xeae808: movk            x16, #0x800, lsl #16
    // 0xeae80c: StoreField: r0->field_9b = r16
    //     0xeae80c: stur            w16, [x0, #0x9b]
    // 0xeae810: r16 = 134221828
    //     0xeae810: movz            x16, #0x1004
    //     0xeae814: movk            x16, #0x800, lsl #16
    // 0xeae818: StoreField: r0->field_9f = r16
    //     0xeae818: stur            w16, [x0, #0x9f]
    // 0xeae81c: r16 = 138412036
    //     0xeae81c: movz            x16, #0x4
    //     0xeae820: movk            x16, #0x840, lsl #16
    // 0xeae824: StoreField: r0->field_a3 = r16
    //     0xeae824: stur            w16, [x0, #0xa3]
    // 0xeae828: r16 = 138412036
    //     0xeae828: movz            x16, #0x4
    //     0xeae82c: movk            x16, #0x840, lsl #16
    // 0xeae830: StoreField: r0->field_a7 = r16
    //     0xeae830: stur            w16, [x0, #0xa7]
    // 0xeae834: r16 = 4
    //     0xeae834: movz            x16, #0x4
    // 0xeae838: StoreField: r0->field_ab = r16
    //     0xeae838: stur            w16, [x0, #0xab]
    // 0xeae83c: r16 = 4194308
    //     0xeae83c: movz            x16, #0x4
    //     0xeae840: movk            x16, #0x40, lsl #16
    // 0xeae844: StoreField: r0->field_af = r16
    //     0xeae844: stur            w16, [x0, #0xaf]
    // 0xeae848: r16 = 134217728
    //     0xeae848: orr             x16, xzr, #0x8000000
    // 0xeae84c: StoreField: r0->field_b3 = r16
    //     0xeae84c: stur            w16, [x0, #0xb3]
    // 0xeae850: r16 = 134221824
    //     0xeae850: movz            x16, #0x1000
    //     0xeae854: movk            x16, #0x800, lsl #16
    // 0xeae858: StoreField: r0->field_b7 = r16
    //     0xeae858: stur            w16, [x0, #0xb7]
    // 0xeae85c: r16 = 64
    //     0xeae85c: movz            x16, #0x40, lsl #16
    // 0xeae860: StoreField: r0->field_bb = r16
    //     0xeae860: stur            w16, [x0, #0xbb]
    // 0xeae864: r16 = 138416128
    //     0xeae864: movz            x16, #0x1000
    //     0xeae868: movk            x16, #0x840, lsl #16
    // 0xeae86c: StoreField: r0->field_bf = r16
    //     0xeae86c: stur            w16, [x0, #0xbf]
    // 0xeae870: r16 = 4100
    //     0xeae870: movz            x16, #0x1004
    // 0xeae874: StoreField: r0->field_c3 = r16
    //     0xeae874: stur            w16, [x0, #0xc3]
    // 0xeae878: r16 = 4198404
    //     0xeae878: movz            x16, #0x1004
    //     0xeae87c: movk            x16, #0x40, lsl #16
    // 0xeae880: StoreField: r0->field_c7 = r16
    //     0xeae880: stur            w16, [x0, #0xc7]
    // 0xeae884: r16 = 138416128
    //     0xeae884: movz            x16, #0x1000
    //     0xeae888: movk            x16, #0x840, lsl #16
    // 0xeae88c: StoreField: r0->field_cb = r16
    //     0xeae88c: stur            w16, [x0, #0xcb]
    // 0xeae890: r16 = 4100
    //     0xeae890: movz            x16, #0x1004
    // 0xeae894: StoreField: r0->field_cf = r16
    //     0xeae894: stur            w16, [x0, #0xcf]
    // 0xeae898: r16 = 134217732
    //     0xeae898: movz            x16, #0x4
    //     0xeae89c: movk            x16, #0x800, lsl #16
    // 0xeae8a0: StoreField: r0->field_d3 = r16
    //     0xeae8a0: stur            w16, [x0, #0xd3]
    // 0xeae8a4: r16 = 138416132
    //     0xeae8a4: movz            x16, #0x1004
    //     0xeae8a8: movk            x16, #0x840, lsl #16
    // 0xeae8ac: StoreField: r0->field_d7 = r16
    //     0xeae8ac: stur            w16, [x0, #0xd7]
    // 0xeae8b0: r16 = 2112
    //     0xeae8b0: movz            x16, #0x840, lsl #16
    // 0xeae8b4: StoreField: r0->field_db = r16
    //     0xeae8b4: stur            w16, [x0, #0xdb]
    // 0xeae8b8: r16 = 4198400
    //     0xeae8b8: movz            x16, #0x1000
    //     0xeae8bc: movk            x16, #0x40, lsl #16
    // 0xeae8c0: StoreField: r0->field_df = r16
    //     0xeae8c0: stur            w16, [x0, #0xdf]
    // 0xeae8c4: StoreField: r0->field_e3 = rZR
    //     0xeae8c4: stur            wzr, [x0, #0xe3]
    // 0xeae8c8: r16 = 4
    //     0xeae8c8: movz            x16, #0x4
    // 0xeae8cc: StoreField: r0->field_e7 = r16
    //     0xeae8cc: stur            w16, [x0, #0xe7]
    // 0xeae8d0: r16 = 138416132
    //     0xeae8d0: movz            x16, #0x1004
    //     0xeae8d4: movk            x16, #0x840, lsl #16
    // 0xeae8d8: StoreField: r0->field_eb = r16
    //     0xeae8d8: stur            w16, [x0, #0xeb]
    // 0xeae8dc: StoreField: r0->field_ef = rZR
    //     0xeae8dc: stur            wzr, [x0, #0xef]
    // 0xeae8e0: r16 = 4198404
    //     0xeae8e0: movz            x16, #0x1004
    //     0xeae8e4: movk            x16, #0x40, lsl #16
    // 0xeae8e8: StoreField: r0->field_f3 = r16
    //     0xeae8e8: stur            w16, [x0, #0xf3]
    // 0xeae8ec: r16 = 2112
    //     0xeae8ec: movz            x16, #0x840, lsl #16
    // 0xeae8f0: StoreField: r0->field_f7 = r16
    //     0xeae8f0: stur            w16, [x0, #0xf7]
    // 0xeae8f4: r16 = 4096
    //     0xeae8f4: movz            x16, #0x1000
    // 0xeae8f8: StoreField: r0->field_fb = r16
    //     0xeae8f8: stur            w16, [x0, #0xfb]
    // 0xeae8fc: r16 = 134217732
    //     0xeae8fc: movz            x16, #0x4
    //     0xeae900: movk            x16, #0x800, lsl #16
    // 0xeae904: StoreField: r0->field_ff = r16
    //     0xeae904: stur            w16, [x0, #0xff]
    // 0xeae908: r1 = 122
    //     0xeae908: movz            x1, #0x7a
    // 0xeae90c: add             x2, x0, w1, sxtw #1
    // 0xeae910: r16 = 134221824
    //     0xeae910: movz            x16, #0x1000
    //     0xeae914: movk            x16, #0x800, lsl #16
    // 0xeae918: StoreField: r2->field_f = r16
    //     0xeae918: stur            w16, [x2, #0xf]
    // 0xeae91c: r1 = 124
    //     0xeae91c: movz            x1, #0x7c
    // 0xeae920: add             x2, x0, w1, sxtw #1
    // 0xeae924: r16 = 4096
    //     0xeae924: movz            x16, #0x1000
    // 0xeae928: StoreField: r2->field_f = r16
    //     0xeae928: stur            w16, [x2, #0xf]
    // 0xeae92c: r1 = 126
    //     0xeae92c: movz            x1, #0x7e
    // 0xeae930: add             x2, x0, w1, sxtw #1
    // 0xeae934: r16 = 4194308
    //     0xeae934: movz            x16, #0x4
    //     0xeae938: movk            x16, #0x40, lsl #16
    // 0xeae93c: StoreField: r2->field_f = r16
    //     0xeae93c: stur            w16, [x2, #0xf]
    // 0xeae940: r1 = <int>
    //     0xeae940: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeae944: r0 = AllocateGrowableArray()
    //     0xeae944: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xeae948: ldur            x1, [fp, #-8]
    // 0xeae94c: StoreField: r0->field_f = r1
    //     0xeae94c: stur            w1, [x0, #0xf]
    // 0xeae950: r1 = 128
    //     0xeae950: movz            x1, #0x80
    // 0xeae954: StoreField: r0->field_b = r1
    //     0xeae954: stur            w1, [x0, #0xb]
    // 0xeae958: LeaveFrame
    //     0xeae958: mov             SP, fp
    //     0xeae95c: ldp             fp, lr, [SP], #0x10
    // 0xeae960: ret
    //     0xeae960: ret             
  }
}
