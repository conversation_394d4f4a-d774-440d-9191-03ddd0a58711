// lib: , url: package:pointycastle/block/desede_engine.dart

// class id: 1050929, size: 0x8
class :: {
}

// class id: 673, size: 0x18, field offset: 0x8
class DESedeEngine extends DesBase
    implements BaseBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xd58

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e68ac, size: 0x58
    // 0x8e68ac: EnterFrame
    //     0x8e68ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8e68b0: mov             fp, SP
    // 0x8e68b4: AllocStack(0x8)
    //     0x8e68b4: sub             SP, SP, #8
    // 0x8e68b8: r0 = StaticFactoryConfig()
    //     0x8e68b8: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e68bc: mov             x3, x0
    // 0x8e68c0: r0 = "DESede"
    //     0x8e68c0: add             x0, PP, #0x19, lsl #12  ; [pp+0x19ba0] "DESede"
    //     0x8e68c4: ldr             x0, [x0, #0xba0]
    // 0x8e68c8: stur            x3, [fp, #-8]
    // 0x8e68cc: StoreField: r3->field_b = r0
    //     0x8e68cc: stur            w0, [x3, #0xb]
    // 0x8e68d0: r1 = Function '<anonymous closure>': static.
    //     0x8e68d0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19ba8] AnonymousClosure: static (0x8e6904), in [package:pointycastle/block/desede_engine.dart] DESedeEngine::factoryConfig (0x8e68ac)
    //     0x8e68d4: ldr             x1, [x1, #0xba8]
    // 0x8e68d8: r2 = Null
    //     0x8e68d8: mov             x2, NULL
    // 0x8e68dc: r0 = AllocateClosure()
    //     0x8e68dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e68e0: mov             x1, x0
    // 0x8e68e4: ldur            x0, [fp, #-8]
    // 0x8e68e8: StoreField: r0->field_f = r1
    //     0x8e68e8: stur            w1, [x0, #0xf]
    // 0x8e68ec: r1 = BlockCipher
    //     0x8e68ec: add             x1, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e68f0: ldr             x1, [x1, #0xa80]
    // 0x8e68f4: StoreField: r0->field_7 = r1
    //     0x8e68f4: stur            w1, [x0, #7]
    // 0x8e68f8: LeaveFrame
    //     0x8e68f8: mov             SP, fp
    //     0x8e68fc: ldp             fp, lr, [SP], #0x10
    // 0x8e6900: ret
    //     0x8e6900: ret             
  }
  [closure] static DESedeEngine <anonymous closure>(dynamic) {
    // ** addr: 0x8e6904, size: 0x20
    // 0x8e6904: EnterFrame
    //     0x8e6904: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6908: mov             fp, SP
    // 0x8e690c: r0 = DESedeEngine()
    //     0x8e690c: bl              #0x8e6924  ; AllocateDESedeEngineStub -> DESedeEngine (size=0x18)
    // 0x8e6910: r1 = false
    //     0x8e6910: add             x1, NULL, #0x30  ; false
    // 0x8e6914: StoreField: r0->field_13 = r1
    //     0x8e6914: stur            w1, [x0, #0x13]
    // 0x8e6918: LeaveFrame
    //     0x8e6918: mov             SP, fp
    //     0x8e691c: ldp             fp, lr, [SP], #0x10
    // 0x8e6920: ret
    //     0x8e6920: ret             
  }
  _ init(/* No info */) {
    // ** addr: 0xe8b700, size: 0x2b8
    // 0xe8b700: EnterFrame
    //     0xe8b700: stp             fp, lr, [SP, #-0x10]!
    //     0xe8b704: mov             fp, SP
    // 0xe8b708: AllocStack(0x20)
    //     0xe8b708: sub             SP, SP, #0x20
    // 0xe8b70c: SetupParameters(DESedeEngine this /* r1 => r1, fp-0x18 */, dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xe8b70c: stur            x1, [fp, #-0x18]
    //     0xe8b710: stur            x2, [fp, #-0x20]
    // 0xe8b714: CheckStackOverflow
    //     0xe8b714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b718: cmp             SP, x16
    //     0xe8b71c: b.ls            #0xe8b980
    // 0xe8b720: r0 = LoadClassIdInstr(r3)
    //     0xe8b720: ldur            x0, [x3, #-1]
    //     0xe8b724: ubfx            x0, x0, #0xc, #0x14
    // 0xe8b728: cmp             x0, #0x2ac
    // 0xe8b72c: b.ne            #0xe8b948
    // 0xe8b730: LoadField: r0 = r3->field_7
    //     0xe8b730: ldur            w0, [x3, #7]
    // 0xe8b734: DecompressPointer r0
    //     0xe8b734: add             x0, x0, HEAP, lsl #32
    // 0xe8b738: r16 = Sentinel
    //     0xe8b738: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8b73c: cmp             w0, w16
    // 0xe8b740: b.eq            #0xe8b988
    // 0xe8b744: stur            x0, [fp, #-0x10]
    // 0xe8b748: LoadField: r3 = r0->field_13
    //     0xe8b748: ldur            w3, [x0, #0x13]
    // 0xe8b74c: r5 = LoadInt32Instr(r3)
    //     0xe8b74c: sbfx            x5, x3, #1, #0x1f
    // 0xe8b750: stur            x5, [fp, #-8]
    // 0xe8b754: cmp             x5, #0x18
    // 0xe8b758: b.eq            #0xe8b764
    // 0xe8b75c: cmp             x5, #0x10
    // 0xe8b760: b.ne            #0xe8b958
    // 0xe8b764: StoreField: r1->field_13 = r2
    //     0xe8b764: stur            w2, [x1, #0x13]
    // 0xe8b768: r4 = 16
    //     0xe8b768: movz            x4, #0x10
    // 0xe8b76c: r0 = AllocateUint8Array()
    //     0xe8b76c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8b770: mov             x2, x0
    // 0xe8b774: ldur            x4, [fp, #-0x10]
    // 0xe8b778: r3 = 0
    //     0xe8b778: movz            x3, #0
    // 0xe8b77c: CheckStackOverflow
    //     0xe8b77c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b780: cmp             SP, x16
    //     0xe8b784: b.ls            #0xe8b994
    // 0xe8b788: cmp             x3, #8
    // 0xe8b78c: b.ge            #0xe8b7bc
    // 0xe8b790: ldur            x0, [fp, #-8]
    // 0xe8b794: mov             x1, x3
    // 0xe8b798: cmp             x1, x0
    // 0xe8b79c: b.hs            #0xe8b99c
    // 0xe8b7a0: ArrayLoad: r0 = r4[r3]  ; List_1
    //     0xe8b7a0: add             x16, x4, x3
    //     0xe8b7a4: ldrb            w0, [x16, #0x17]
    // 0xe8b7a8: ArrayStore: r2[r3] = r0  ; TypeUnknown_1
    //     0xe8b7a8: add             x1, x2, x3
    //     0xe8b7ac: strb            w0, [x1, #0x17]
    // 0xe8b7b0: add             x0, x3, #1
    // 0xe8b7b4: mov             x3, x0
    // 0xe8b7b8: b               #0xe8b77c
    // 0xe8b7bc: ldur            x0, [fp, #-0x18]
    // 0xe8b7c0: mov             x1, x0
    // 0xe8b7c4: mov             x3, x2
    // 0xe8b7c8: ldur            x2, [fp, #-0x20]
    // 0xe8b7cc: r0 = generateWorkingKey()
    //     0xe8b7cc: bl              #0xe8b9b8  ; [package:pointycastle/block/des_base.dart] DesBase::generateWorkingKey
    // 0xe8b7d0: ldur            x1, [fp, #-0x18]
    // 0xe8b7d4: StoreField: r1->field_7 = r0
    //     0xe8b7d4: stur            w0, [x1, #7]
    //     0xe8b7d8: ldurb           w16, [x1, #-1]
    //     0xe8b7dc: ldurb           w17, [x0, #-1]
    //     0xe8b7e0: and             x16, x17, x16, lsr #2
    //     0xe8b7e4: tst             x16, HEAP, lsr #32
    //     0xe8b7e8: b.eq            #0xe8b7f0
    //     0xe8b7ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8b7f0: r4 = 16
    //     0xe8b7f0: movz            x4, #0x10
    // 0xe8b7f4: r0 = AllocateUint8Array()
    //     0xe8b7f4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8b7f8: mov             x2, x0
    // 0xe8b7fc: ldur            x4, [fp, #-0x10]
    // 0xe8b800: r3 = 0
    //     0xe8b800: movz            x3, #0
    // 0xe8b804: CheckStackOverflow
    //     0xe8b804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b808: cmp             SP, x16
    //     0xe8b80c: b.ls            #0xe8b9a0
    // 0xe8b810: cmp             x3, #8
    // 0xe8b814: b.ge            #0xe8b848
    // 0xe8b818: add             x5, x3, #8
    // 0xe8b81c: ldur            x0, [fp, #-8]
    // 0xe8b820: mov             x1, x5
    // 0xe8b824: cmp             x1, x0
    // 0xe8b828: b.hs            #0xe8b9a8
    // 0xe8b82c: ArrayLoad: r0 = r4[r5]  ; List_1
    //     0xe8b82c: add             x16, x4, x5
    //     0xe8b830: ldrb            w0, [x16, #0x17]
    // 0xe8b834: ArrayStore: r2[r3] = r0  ; TypeUnknown_1
    //     0xe8b834: add             x1, x2, x3
    //     0xe8b838: strb            w0, [x1, #0x17]
    // 0xe8b83c: add             x0, x3, #1
    // 0xe8b840: mov             x3, x0
    // 0xe8b844: b               #0xe8b804
    // 0xe8b848: ldur            x0, [fp, #-0x18]
    // 0xe8b84c: ldur            x5, [fp, #-0x20]
    // 0xe8b850: ldur            x6, [fp, #-8]
    // 0xe8b854: eor             x1, x5, #0x10
    // 0xe8b858: mov             x3, x2
    // 0xe8b85c: mov             x2, x1
    // 0xe8b860: mov             x1, x0
    // 0xe8b864: r0 = generateWorkingKey()
    //     0xe8b864: bl              #0xe8b9b8  ; [package:pointycastle/block/des_base.dart] DesBase::generateWorkingKey
    // 0xe8b868: ldur            x1, [fp, #-0x18]
    // 0xe8b86c: StoreField: r1->field_b = r0
    //     0xe8b86c: stur            w0, [x1, #0xb]
    //     0xe8b870: ldurb           w16, [x1, #-1]
    //     0xe8b874: ldurb           w17, [x0, #-1]
    //     0xe8b878: and             x16, x17, x16, lsr #2
    //     0xe8b87c: tst             x16, HEAP, lsr #32
    //     0xe8b880: b.eq            #0xe8b888
    //     0xe8b884: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8b888: ldur            x0, [fp, #-8]
    // 0xe8b88c: cmp             x0, #0x18
    // 0xe8b890: b.ne            #0xe8b924
    // 0xe8b894: r4 = 16
    //     0xe8b894: movz            x4, #0x10
    // 0xe8b898: r0 = AllocateUint8Array()
    //     0xe8b898: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8b89c: mov             x2, x0
    // 0xe8b8a0: ldur            x3, [fp, #-0x10]
    // 0xe8b8a4: r4 = 0
    //     0xe8b8a4: movz            x4, #0
    // 0xe8b8a8: CheckStackOverflow
    //     0xe8b8a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8b8ac: cmp             SP, x16
    //     0xe8b8b0: b.ls            #0xe8b9ac
    // 0xe8b8b4: cmp             x4, #8
    // 0xe8b8b8: b.ge            #0xe8b8ec
    // 0xe8b8bc: add             x5, x4, #0x10
    // 0xe8b8c0: ldur            x0, [fp, #-8]
    // 0xe8b8c4: mov             x1, x5
    // 0xe8b8c8: cmp             x1, x0
    // 0xe8b8cc: b.hs            #0xe8b9b4
    // 0xe8b8d0: ArrayLoad: r0 = r3[r5]  ; List_1
    //     0xe8b8d0: add             x16, x3, x5
    //     0xe8b8d4: ldrb            w0, [x16, #0x17]
    // 0xe8b8d8: ArrayStore: r2[r4] = r0  ; TypeUnknown_1
    //     0xe8b8d8: add             x1, x2, x4
    //     0xe8b8dc: strb            w0, [x1, #0x17]
    // 0xe8b8e0: add             x0, x4, #1
    // 0xe8b8e4: mov             x4, x0
    // 0xe8b8e8: b               #0xe8b8a8
    // 0xe8b8ec: ldur            x0, [fp, #-0x18]
    // 0xe8b8f0: mov             x1, x0
    // 0xe8b8f4: mov             x3, x2
    // 0xe8b8f8: ldur            x2, [fp, #-0x20]
    // 0xe8b8fc: r0 = generateWorkingKey()
    //     0xe8b8fc: bl              #0xe8b9b8  ; [package:pointycastle/block/des_base.dart] DesBase::generateWorkingKey
    // 0xe8b900: ldur            x1, [fp, #-0x18]
    // 0xe8b904: StoreField: r1->field_f = r0
    //     0xe8b904: stur            w0, [x1, #0xf]
    //     0xe8b908: ldurb           w16, [x1, #-1]
    //     0xe8b90c: ldurb           w17, [x0, #-1]
    //     0xe8b910: and             x16, x17, x16, lsr #2
    //     0xe8b914: tst             x16, HEAP, lsr #32
    //     0xe8b918: b.eq            #0xe8b920
    //     0xe8b91c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8b920: b               #0xe8b948
    // 0xe8b924: LoadField: r0 = r1->field_7
    //     0xe8b924: ldur            w0, [x1, #7]
    // 0xe8b928: DecompressPointer r0
    //     0xe8b928: add             x0, x0, HEAP, lsl #32
    // 0xe8b92c: StoreField: r1->field_f = r0
    //     0xe8b92c: stur            w0, [x1, #0xf]
    //     0xe8b930: ldurb           w16, [x1, #-1]
    //     0xe8b934: ldurb           w17, [x0, #-1]
    //     0xe8b938: and             x16, x17, x16, lsr #2
    //     0xe8b93c: tst             x16, HEAP, lsr #32
    //     0xe8b940: b.eq            #0xe8b948
    //     0xe8b944: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8b948: r0 = Null
    //     0xe8b948: mov             x0, NULL
    // 0xe8b94c: LeaveFrame
    //     0xe8b94c: mov             SP, fp
    //     0xe8b950: ldp             fp, lr, [SP], #0x10
    // 0xe8b954: ret
    //     0xe8b954: ret             
    // 0xe8b958: r0 = ArgumentError()
    //     0xe8b958: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe8b95c: mov             x1, x0
    // 0xe8b960: r0 = "key size must be 16 or 24 bytes."
    //     0xe8b960: add             x0, PP, #0x21, lsl #12  ; [pp+0x21d88] "key size must be 16 or 24 bytes."
    //     0xe8b964: ldr             x0, [x0, #0xd88]
    // 0xe8b968: ArrayStore: r1[0] = r0  ; List_4
    //     0xe8b968: stur            w0, [x1, #0x17]
    // 0xe8b96c: r0 = false
    //     0xe8b96c: add             x0, NULL, #0x30  ; false
    // 0xe8b970: StoreField: r1->field_b = r0
    //     0xe8b970: stur            w0, [x1, #0xb]
    // 0xe8b974: mov             x0, x1
    // 0xe8b978: r0 = Throw()
    //     0xe8b978: bl              #0xec04b8  ; ThrowStub
    // 0xe8b97c: brk             #0
    // 0xe8b980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b980: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b984: b               #0xe8b720
    // 0xe8b988: r9 = key
    //     0xe8b988: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a80] Field <KeyParameter.key>: late (offset: 0x8)
    //     0xe8b98c: ldr             x9, [x9, #0xa80]
    // 0xe8b990: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe8b990: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe8b994: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b994: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b998: b               #0xe8b788
    // 0xe8b99c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8b99c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8b9a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b9a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b9a4: b               #0xe8b810
    // 0xe8b9a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8b9a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8b9ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8b9ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8b9b0: b               #0xe8b8b4
    // 0xe8b9b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8b9b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xeac034, size: 0x24c
    // 0xeac034: EnterFrame
    //     0xeac034: stp             fp, lr, [SP, #-0x10]!
    //     0xeac038: mov             fp, SP
    // 0xeac03c: AllocStack(0x40)
    //     0xeac03c: sub             SP, SP, #0x40
    // 0xeac040: SetupParameters(DESedeEngine this /* r1 => r1, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */, dynamic _ /* r3 => r5, fp-0x28 */, dynamic _ /* r5 => r6, fp-0x30 */, dynamic _ /* r6 => r7, fp-0x38 */)
    //     0xeac040: mov             x7, x6
    //     0xeac044: stur            x6, [fp, #-0x38]
    //     0xeac048: mov             x6, x5
    //     0xeac04c: stur            x5, [fp, #-0x30]
    //     0xeac050: mov             x5, x3
    //     0xeac054: stur            x3, [fp, #-0x28]
    //     0xeac058: mov             x3, x2
    //     0xeac05c: stur            x1, [fp, #-0x18]
    //     0xeac060: stur            x2, [fp, #-0x20]
    // 0xeac064: CheckStackOverflow
    //     0xeac064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeac068: cmp             SP, x16
    //     0xeac06c: b.ls            #0xeac268
    // 0xeac070: LoadField: r2 = r1->field_7
    //     0xeac070: ldur            w2, [x1, #7]
    // 0xeac074: DecompressPointer r2
    //     0xeac074: add             x2, x2, HEAP, lsl #32
    // 0xeac078: stur            x2, [fp, #-0x10]
    // 0xeac07c: cmp             w2, NULL
    // 0xeac080: b.eq            #0xeac1e8
    // 0xeac084: LoadField: r0 = r1->field_b
    //     0xeac084: ldur            w0, [x1, #0xb]
    // 0xeac088: DecompressPointer r0
    //     0xeac088: add             x0, x0, HEAP, lsl #32
    // 0xeac08c: cmp             w0, NULL
    // 0xeac090: b.eq            #0xeac1e8
    // 0xeac094: LoadField: r0 = r1->field_f
    //     0xeac094: ldur            w0, [x1, #0xf]
    // 0xeac098: DecompressPointer r0
    //     0xeac098: add             x0, x0, HEAP, lsl #32
    // 0xeac09c: stur            x0, [fp, #-8]
    // 0xeac0a0: cmp             w0, NULL
    // 0xeac0a4: b.eq            #0xeac1e8
    // 0xeac0a8: add             x4, x5, #8
    // 0xeac0ac: LoadField: r8 = r3->field_13
    //     0xeac0ac: ldur            w8, [x3, #0x13]
    // 0xeac0b0: r9 = LoadInt32Instr(r8)
    //     0xeac0b0: sbfx            x9, x8, #1, #0x1f
    // 0xeac0b4: cmp             x4, x9
    // 0xeac0b8: b.gt            #0xeac210
    // 0xeac0bc: add             x4, x7, #8
    // 0xeac0c0: LoadField: r8 = r6->field_13
    //     0xeac0c0: ldur            w8, [x6, #0x13]
    // 0xeac0c4: r9 = LoadInt32Instr(r8)
    //     0xeac0c4: sbfx            x9, x8, #1, #0x1f
    // 0xeac0c8: cmp             x4, x9
    // 0xeac0cc: b.gt            #0xeac23c
    // 0xeac0d0: r4 = 16
    //     0xeac0d0: movz            x4, #0x10
    // 0xeac0d4: r0 = AllocateUint8Array()
    //     0xeac0d4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xeac0d8: mov             x4, x0
    // 0xeac0dc: ldur            x0, [fp, #-0x18]
    // 0xeac0e0: stur            x4, [fp, #-0x40]
    // 0xeac0e4: LoadField: r1 = r0->field_13
    //     0xeac0e4: ldur            w1, [x0, #0x13]
    // 0xeac0e8: DecompressPointer r1
    //     0xeac0e8: add             x1, x1, HEAP, lsl #32
    // 0xeac0ec: tbnz            w1, #4, #0xeac168
    // 0xeac0f0: mov             x1, x0
    // 0xeac0f4: ldur            x2, [fp, #-0x10]
    // 0xeac0f8: ldur            x3, [fp, #-0x20]
    // 0xeac0fc: ldur            x5, [fp, #-0x28]
    // 0xeac100: mov             x6, x4
    // 0xeac104: r7 = 0
    //     0xeac104: movz            x7, #0
    // 0xeac108: r0 = desFunc()
    //     0xeac108: bl              #0xeac280  ; [package:pointycastle/block/des_base.dart] DesBase::desFunc
    // 0xeac10c: ldur            x0, [fp, #-0x18]
    // 0xeac110: LoadField: r2 = r0->field_b
    //     0xeac110: ldur            w2, [x0, #0xb]
    // 0xeac114: DecompressPointer r2
    //     0xeac114: add             x2, x2, HEAP, lsl #32
    // 0xeac118: cmp             w2, NULL
    // 0xeac11c: b.eq            #0xeac270
    // 0xeac120: mov             x1, x0
    // 0xeac124: ldur            x3, [fp, #-0x40]
    // 0xeac128: ldur            x6, [fp, #-0x40]
    // 0xeac12c: r5 = 0
    //     0xeac12c: movz            x5, #0
    // 0xeac130: r7 = 0
    //     0xeac130: movz            x7, #0
    // 0xeac134: r0 = desFunc()
    //     0xeac134: bl              #0xeac280  ; [package:pointycastle/block/des_base.dart] DesBase::desFunc
    // 0xeac138: ldur            x0, [fp, #-0x18]
    // 0xeac13c: LoadField: r2 = r0->field_f
    //     0xeac13c: ldur            w2, [x0, #0xf]
    // 0xeac140: DecompressPointer r2
    //     0xeac140: add             x2, x2, HEAP, lsl #32
    // 0xeac144: cmp             w2, NULL
    // 0xeac148: b.eq            #0xeac274
    // 0xeac14c: mov             x1, x0
    // 0xeac150: ldur            x3, [fp, #-0x40]
    // 0xeac154: ldur            x6, [fp, #-0x30]
    // 0xeac158: ldur            x7, [fp, #-0x38]
    // 0xeac15c: r5 = 0
    //     0xeac15c: movz            x5, #0
    // 0xeac160: r0 = desFunc()
    //     0xeac160: bl              #0xeac280  ; [package:pointycastle/block/des_base.dart] DesBase::desFunc
    // 0xeac164: b               #0xeac1d8
    // 0xeac168: mov             x1, x0
    // 0xeac16c: ldur            x2, [fp, #-8]
    // 0xeac170: ldur            x3, [fp, #-0x20]
    // 0xeac174: ldur            x5, [fp, #-0x28]
    // 0xeac178: ldur            x6, [fp, #-0x40]
    // 0xeac17c: r7 = 0
    //     0xeac17c: movz            x7, #0
    // 0xeac180: r0 = desFunc()
    //     0xeac180: bl              #0xeac280  ; [package:pointycastle/block/des_base.dart] DesBase::desFunc
    // 0xeac184: ldur            x0, [fp, #-0x18]
    // 0xeac188: LoadField: r2 = r0->field_b
    //     0xeac188: ldur            w2, [x0, #0xb]
    // 0xeac18c: DecompressPointer r2
    //     0xeac18c: add             x2, x2, HEAP, lsl #32
    // 0xeac190: cmp             w2, NULL
    // 0xeac194: b.eq            #0xeac278
    // 0xeac198: mov             x1, x0
    // 0xeac19c: ldur            x3, [fp, #-0x40]
    // 0xeac1a0: ldur            x6, [fp, #-0x40]
    // 0xeac1a4: r5 = 0
    //     0xeac1a4: movz            x5, #0
    // 0xeac1a8: r7 = 0
    //     0xeac1a8: movz            x7, #0
    // 0xeac1ac: r0 = desFunc()
    //     0xeac1ac: bl              #0xeac280  ; [package:pointycastle/block/des_base.dart] DesBase::desFunc
    // 0xeac1b0: ldur            x1, [fp, #-0x18]
    // 0xeac1b4: LoadField: r2 = r1->field_7
    //     0xeac1b4: ldur            w2, [x1, #7]
    // 0xeac1b8: DecompressPointer r2
    //     0xeac1b8: add             x2, x2, HEAP, lsl #32
    // 0xeac1bc: cmp             w2, NULL
    // 0xeac1c0: b.eq            #0xeac27c
    // 0xeac1c4: ldur            x3, [fp, #-0x40]
    // 0xeac1c8: ldur            x6, [fp, #-0x30]
    // 0xeac1cc: ldur            x7, [fp, #-0x38]
    // 0xeac1d0: r5 = 0
    //     0xeac1d0: movz            x5, #0
    // 0xeac1d4: r0 = desFunc()
    //     0xeac1d4: bl              #0xeac280  ; [package:pointycastle/block/des_base.dart] DesBase::desFunc
    // 0xeac1d8: r0 = 8
    //     0xeac1d8: movz            x0, #0x8
    // 0xeac1dc: LeaveFrame
    //     0xeac1dc: mov             SP, fp
    //     0xeac1e0: ldp             fp, lr, [SP], #0x10
    // 0xeac1e4: ret
    //     0xeac1e4: ret             
    // 0xeac1e8: r0 = ArgumentError()
    //     0xeac1e8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xeac1ec: mov             x1, x0
    // 0xeac1f0: r0 = "DESede engine not initialised"
    //     0xeac1f0: add             x0, PP, #0x23, lsl #12  ; [pp+0x236c0] "DESede engine not initialised"
    //     0xeac1f4: ldr             x0, [x0, #0x6c0]
    // 0xeac1f8: ArrayStore: r1[0] = r0  ; List_4
    //     0xeac1f8: stur            w0, [x1, #0x17]
    // 0xeac1fc: r0 = false
    //     0xeac1fc: add             x0, NULL, #0x30  ; false
    // 0xeac200: StoreField: r1->field_b = r0
    //     0xeac200: stur            w0, [x1, #0xb]
    // 0xeac204: mov             x0, x1
    // 0xeac208: r0 = Throw()
    //     0xeac208: bl              #0xec04b8  ; ThrowStub
    // 0xeac20c: brk             #0
    // 0xeac210: r0 = false
    //     0xeac210: add             x0, NULL, #0x30  ; false
    // 0xeac214: r0 = ArgumentError()
    //     0xeac214: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xeac218: mov             x1, x0
    // 0xeac21c: r0 = "input buffer too short"
    //     0xeac21c: add             x0, PP, #0x23, lsl #12  ; [pp+0x236b0] "input buffer too short"
    //     0xeac220: ldr             x0, [x0, #0x6b0]
    // 0xeac224: ArrayStore: r1[0] = r0  ; List_4
    //     0xeac224: stur            w0, [x1, #0x17]
    // 0xeac228: r0 = false
    //     0xeac228: add             x0, NULL, #0x30  ; false
    // 0xeac22c: StoreField: r1->field_b = r0
    //     0xeac22c: stur            w0, [x1, #0xb]
    // 0xeac230: mov             x0, x1
    // 0xeac234: r0 = Throw()
    //     0xeac234: bl              #0xec04b8  ; ThrowStub
    // 0xeac238: brk             #0
    // 0xeac23c: r0 = false
    //     0xeac23c: add             x0, NULL, #0x30  ; false
    // 0xeac240: r0 = ArgumentError()
    //     0xeac240: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xeac244: mov             x1, x0
    // 0xeac248: r0 = "output buffer too short"
    //     0xeac248: add             x0, PP, #0x23, lsl #12  ; [pp+0x236b8] "output buffer too short"
    //     0xeac24c: ldr             x0, [x0, #0x6b8]
    // 0xeac250: ArrayStore: r1[0] = r0  ; List_4
    //     0xeac250: stur            w0, [x1, #0x17]
    // 0xeac254: r0 = false
    //     0xeac254: add             x0, NULL, #0x30  ; false
    // 0xeac258: StoreField: r1->field_b = r0
    //     0xeac258: stur            w0, [x1, #0xb]
    // 0xeac25c: mov             x0, x1
    // 0xeac260: r0 = Throw()
    //     0xeac260: bl              #0xec04b8  ; ThrowStub
    // 0xeac264: brk             #0
    // 0xeac268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeac268: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeac26c: b               #0xeac070
    // 0xeac270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeac270: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeac274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeac274: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeac278: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeac278: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeac27c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeac27c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ blockSize(/* No info */) {
    // ** addr: 0xeb532c, size: 0x8
    // 0xeb532c: r0 = 8
    //     0xeb532c: movz            x0, #0x8
    // 0xeb5330: ret
    //     0xeb5330: ret             
  }
}
