// lib: impl.block_cipher.modes.cbc, url: package:pointycastle/block/modes/cbc.dart

// class id: 1050930, size: 0x8
class :: {
}

// class id: 708, size: 0x1c, field offset: 0x8
class CBCBlockCipher extends BaseBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xd9c
  late Uint8List _iv; // offset: 0xc
  late bool _encrypting; // offset: 0x18

  _ CBCBlockCipher(/* No info */) {
    // ** addr: 0x8c4980, size: 0x174
    // 0x8c4980: EnterFrame
    //     0x8c4980: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4984: mov             fp, SP
    // 0x8c4988: AllocStack(0x10)
    //     0x8c4988: sub             SP, SP, #0x10
    // 0x8c498c: r0 = Sentinel
    //     0x8c498c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c4990: mov             x3, x1
    // 0x8c4994: stur            x1, [fp, #-8]
    // 0x8c4998: stur            x2, [fp, #-0x10]
    // 0x8c499c: CheckStackOverflow
    //     0x8c499c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c49a0: cmp             SP, x16
    //     0x8c49a4: b.ls            #0x8c4aec
    // 0x8c49a8: StoreField: r3->field_b = r0
    //     0x8c49a8: stur            w0, [x3, #0xb]
    // 0x8c49ac: ArrayStore: r3[0] = r0  ; List_4
    //     0x8c49ac: stur            w0, [x3, #0x17]
    // 0x8c49b0: mov             x0, x2
    // 0x8c49b4: StoreField: r3->field_7 = r0
    //     0x8c49b4: stur            w0, [x3, #7]
    //     0x8c49b8: ldurb           w16, [x3, #-1]
    //     0x8c49bc: ldurb           w17, [x0, #-1]
    //     0x8c49c0: and             x16, x17, x16, lsr #2
    //     0x8c49c4: tst             x16, HEAP, lsr #32
    //     0x8c49c8: b.eq            #0x8c49d0
    //     0x8c49cc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8c49d0: r0 = LoadClassIdInstr(r2)
    //     0x8c49d0: ldur            x0, [x2, #-1]
    //     0x8c49d4: ubfx            x0, x0, #0xc, #0x14
    // 0x8c49d8: mov             x1, x2
    // 0x8c49dc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c49dc: sub             lr, x0, #1, lsl #12
    //     0x8c49e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8c49e4: blr             lr
    // 0x8c49e8: mov             x2, x0
    // 0x8c49ec: r0 = BoxInt64Instr(r2)
    //     0x8c49ec: sbfiz           x0, x2, #1, #0x1f
    //     0x8c49f0: cmp             x2, x0, asr #1
    //     0x8c49f4: b.eq            #0x8c4a00
    //     0x8c49f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c49fc: stur            x2, [x0, #7]
    // 0x8c4a00: mov             x4, x0
    // 0x8c4a04: r0 = AllocateUint8Array()
    //     0x8c4a04: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4a08: ldur            x2, [fp, #-8]
    // 0x8c4a0c: StoreField: r2->field_b = r0
    //     0x8c4a0c: stur            w0, [x2, #0xb]
    //     0x8c4a10: ldurb           w16, [x2, #-1]
    //     0x8c4a14: ldurb           w17, [x0, #-1]
    //     0x8c4a18: and             x16, x17, x16, lsr #2
    //     0x8c4a1c: tst             x16, HEAP, lsr #32
    //     0x8c4a20: b.eq            #0x8c4a28
    //     0x8c4a24: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c4a28: ldur            x3, [fp, #-0x10]
    // 0x8c4a2c: r0 = LoadClassIdInstr(r3)
    //     0x8c4a2c: ldur            x0, [x3, #-1]
    //     0x8c4a30: ubfx            x0, x0, #0xc, #0x14
    // 0x8c4a34: mov             x1, x3
    // 0x8c4a38: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c4a38: sub             lr, x0, #1, lsl #12
    //     0x8c4a3c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c4a40: blr             lr
    // 0x8c4a44: mov             x2, x0
    // 0x8c4a48: r0 = BoxInt64Instr(r2)
    //     0x8c4a48: sbfiz           x0, x2, #1, #0x1f
    //     0x8c4a4c: cmp             x2, x0, asr #1
    //     0x8c4a50: b.eq            #0x8c4a5c
    //     0x8c4a54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c4a58: stur            x2, [x0, #7]
    // 0x8c4a5c: mov             x4, x0
    // 0x8c4a60: r0 = AllocateUint8Array()
    //     0x8c4a60: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4a64: ldur            x2, [fp, #-8]
    // 0x8c4a68: StoreField: r2->field_f = r0
    //     0x8c4a68: stur            w0, [x2, #0xf]
    //     0x8c4a6c: ldurb           w16, [x2, #-1]
    //     0x8c4a70: ldurb           w17, [x0, #-1]
    //     0x8c4a74: and             x16, x17, x16, lsr #2
    //     0x8c4a78: tst             x16, HEAP, lsr #32
    //     0x8c4a7c: b.eq            #0x8c4a84
    //     0x8c4a80: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c4a84: ldur            x1, [fp, #-0x10]
    // 0x8c4a88: r0 = LoadClassIdInstr(r1)
    //     0x8c4a88: ldur            x0, [x1, #-1]
    //     0x8c4a8c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c4a90: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c4a90: sub             lr, x0, #1, lsl #12
    //     0x8c4a94: ldr             lr, [x21, lr, lsl #3]
    //     0x8c4a98: blr             lr
    // 0x8c4a9c: mov             x2, x0
    // 0x8c4aa0: r0 = BoxInt64Instr(r2)
    //     0x8c4aa0: sbfiz           x0, x2, #1, #0x1f
    //     0x8c4aa4: cmp             x2, x0, asr #1
    //     0x8c4aa8: b.eq            #0x8c4ab4
    //     0x8c4aac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c4ab0: stur            x2, [x0, #7]
    // 0x8c4ab4: mov             x4, x0
    // 0x8c4ab8: r0 = AllocateUint8Array()
    //     0x8c4ab8: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4abc: ldur            x1, [fp, #-8]
    // 0x8c4ac0: StoreField: r1->field_13 = r0
    //     0x8c4ac0: stur            w0, [x1, #0x13]
    //     0x8c4ac4: ldurb           w16, [x1, #-1]
    //     0x8c4ac8: ldurb           w17, [x0, #-1]
    //     0x8c4acc: and             x16, x17, x16, lsr #2
    //     0x8c4ad0: tst             x16, HEAP, lsr #32
    //     0x8c4ad4: b.eq            #0x8c4adc
    //     0x8c4ad8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c4adc: r0 = Null
    //     0x8c4adc: mov             x0, NULL
    // 0x8c4ae0: LeaveFrame
    //     0x8c4ae0: mov             SP, fp
    //     0x8c4ae4: ldp             fp, lr, [SP], #0x10
    // 0x8c4ae8: ret
    //     0x8c4ae8: ret             
    // 0x8c4aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4aec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c4af0: b               #0x8c49a8
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e6728, size: 0x64
    // 0x8e6728: EnterFrame
    //     0x8e6728: stp             fp, lr, [SP, #-0x10]!
    //     0x8e672c: mov             fp, SP
    // 0x8e6730: AllocStack(0x8)
    //     0x8e6730: sub             SP, SP, #8
    // 0x8e6734: CheckStackOverflow
    //     0x8e6734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6738: cmp             SP, x16
    //     0x8e673c: b.ls            #0x8e6784
    // 0x8e6740: r0 = DynamicFactoryConfig()
    //     0x8e6740: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e6744: r1 = Function '<anonymous closure>': static.
    //     0x8e6744: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b88] AnonymousClosure: static (0x8e678c), in [package:pointycastle/block/modes/cbc.dart] CBCBlockCipher::factoryConfig (0x8e6728)
    //     0x8e6748: ldr             x1, [x1, #0xb88]
    // 0x8e674c: r2 = Null
    //     0x8e674c: mov             x2, NULL
    // 0x8e6750: stur            x0, [fp, #-8]
    // 0x8e6754: r0 = AllocateClosure()
    //     0x8e6754: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6758: ldur            x1, [fp, #-8]
    // 0x8e675c: mov             x5, x0
    // 0x8e6760: r2 = BlockCipher
    //     0x8e6760: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e6764: ldr             x2, [x2, #0xa80]
    // 0x8e6768: r3 = "/CBC"
    //     0x8e6768: add             x3, PP, #0x19, lsl #12  ; [pp+0x19b90] "/CBC"
    //     0x8e676c: ldr             x3, [x3, #0xb90]
    // 0x8e6770: r0 = DynamicFactoryConfig.suffix()
    //     0x8e6770: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e6774: ldur            x0, [fp, #-8]
    // 0x8e6778: LeaveFrame
    //     0x8e6778: mov             SP, fp
    //     0x8e677c: ldp             fp, lr, [SP], #0x10
    // 0x8e6780: ret
    //     0x8e6780: ret             
    // 0x8e6784: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6784: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6788: b               #0x8e6740
  }
  [closure] static (dynamic) => CBCBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e678c, size: 0x54
    // 0x8e678c: EnterFrame
    //     0x8e678c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6790: mov             fp, SP
    // 0x8e6794: AllocStack(0x8)
    //     0x8e6794: sub             SP, SP, #8
    // 0x8e6798: SetupParameters()
    //     0x8e6798: ldr             x0, [fp, #0x20]
    //     0x8e679c: ldur            w1, [x0, #0x17]
    //     0x8e67a0: add             x1, x1, HEAP, lsl #32
    //     0x8e67a4: stur            x1, [fp, #-8]
    // 0x8e67a8: r1 = 1
    //     0x8e67a8: movz            x1, #0x1
    // 0x8e67ac: r0 = AllocateContext()
    //     0x8e67ac: bl              #0xec126c  ; AllocateContextStub
    // 0x8e67b0: mov             x1, x0
    // 0x8e67b4: ldur            x0, [fp, #-8]
    // 0x8e67b8: StoreField: r1->field_b = r0
    //     0x8e67b8: stur            w0, [x1, #0xb]
    // 0x8e67bc: ldr             x0, [fp, #0x10]
    // 0x8e67c0: StoreField: r1->field_f = r0
    //     0x8e67c0: stur            w0, [x1, #0xf]
    // 0x8e67c4: mov             x2, x1
    // 0x8e67c8: r1 = Function '<anonymous closure>': static.
    //     0x8e67c8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b98] AnonymousClosure: static (0x8e67e0), in [package:pointycastle/block/modes/cbc.dart] CBCBlockCipher::factoryConfig (0x8e6728)
    //     0x8e67cc: ldr             x1, [x1, #0xb98]
    // 0x8e67d0: r0 = AllocateClosure()
    //     0x8e67d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e67d4: LeaveFrame
    //     0x8e67d4: mov             SP, fp
    //     0x8e67d8: ldp             fp, lr, [SP], #0x10
    // 0x8e67dc: ret
    //     0x8e67dc: ret             
  }
  [closure] static CBCBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e67e0, size: 0xcc
    // 0x8e67e0: EnterFrame
    //     0x8e67e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e67e4: mov             fp, SP
    // 0x8e67e8: AllocStack(0x20)
    //     0x8e67e8: sub             SP, SP, #0x20
    // 0x8e67ec: SetupParameters()
    //     0x8e67ec: ldr             x0, [fp, #0x10]
    //     0x8e67f0: ldur            w1, [x0, #0x17]
    //     0x8e67f4: add             x1, x1, HEAP, lsl #32
    // 0x8e67f8: CheckStackOverflow
    //     0x8e67f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e67fc: cmp             SP, x16
    //     0x8e6800: b.ls            #0x8e68a0
    // 0x8e6804: LoadField: r0 = r1->field_f
    //     0x8e6804: ldur            w0, [x1, #0xf]
    // 0x8e6808: DecompressPointer r0
    //     0x8e6808: add             x0, x0, HEAP, lsl #32
    // 0x8e680c: r1 = LoadClassIdInstr(r0)
    //     0x8e680c: ldur            x1, [x0, #-1]
    //     0x8e6810: ubfx            x1, x1, #0xc, #0x14
    // 0x8e6814: mov             x16, x0
    // 0x8e6818: mov             x0, x1
    // 0x8e681c: mov             x1, x16
    // 0x8e6820: r2 = 1
    //     0x8e6820: movz            x2, #0x1
    // 0x8e6824: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e6824: sub             lr, x0, #0xfdd
    //     0x8e6828: ldr             lr, [x21, lr, lsl #3]
    //     0x8e682c: blr             lr
    // 0x8e6830: stur            x0, [fp, #-8]
    // 0x8e6834: cmp             w0, NULL
    // 0x8e6838: b.eq            #0x8e68a8
    // 0x8e683c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e683c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e6840: ldr             x0, [x0, #0x2e38]
    //     0x8e6844: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6848: cmp             w0, w16
    //     0x8e684c: b.ne            #0x8e685c
    //     0x8e6850: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e6854: ldr             x2, [x2, #0xf80]
    //     0x8e6858: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e685c: r16 = <BlockCipher>
    //     0x8e685c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e6860: ldr             x16, [x16, #0x88]
    // 0x8e6864: stp             x0, x16, [SP, #8]
    // 0x8e6868: ldur            x16, [fp, #-8]
    // 0x8e686c: str             x16, [SP]
    // 0x8e6870: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e6870: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e6874: r0 = create()
    //     0x8e6874: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e6878: stur            x0, [fp, #-8]
    // 0x8e687c: r0 = CBCBlockCipher()
    //     0x8e687c: bl              #0x8c4af4  ; AllocateCBCBlockCipherStub -> CBCBlockCipher (size=0x1c)
    // 0x8e6880: mov             x1, x0
    // 0x8e6884: ldur            x2, [fp, #-8]
    // 0x8e6888: stur            x0, [fp, #-8]
    // 0x8e688c: r0 = CBCBlockCipher()
    //     0x8e688c: bl              #0x8c4980  ; [package:pointycastle/block/modes/cbc.dart] CBCBlockCipher::CBCBlockCipher
    // 0x8e6890: ldur            x0, [fp, #-8]
    // 0x8e6894: LeaveFrame
    //     0x8e6894: mov             SP, fp
    //     0x8e6898: ldp             fp, lr, [SP], #0x10
    // 0x8e689c: ret
    //     0x8e689c: ret             
    // 0x8e68a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e68a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e68a4: b               #0x8e6804
    // 0x8e68a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e68a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ reset(/* No info */) {
    // ** addr: 0xe7de20, size: 0x22c
    // 0xe7de20: EnterFrame
    //     0xe7de20: stp             fp, lr, [SP, #-0x10]!
    //     0xe7de24: mov             fp, SP
    // 0xe7de28: AllocStack(0x28)
    //     0xe7de28: sub             SP, SP, #0x28
    // 0xe7de2c: SetupParameters(CBCBlockCipher this /* r1 => r0, fp-0x28 */)
    //     0xe7de2c: mov             x0, x1
    //     0xe7de30: stur            x1, [fp, #-0x28]
    // 0xe7de34: CheckStackOverflow
    //     0xe7de34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7de38: cmp             SP, x16
    //     0xe7de3c: b.ls            #0xe7e030
    // 0xe7de40: LoadField: r4 = r0->field_f
    //     0xe7de40: ldur            w4, [x0, #0xf]
    // 0xe7de44: DecompressPointer r4
    //     0xe7de44: add             x4, x4, HEAP, lsl #32
    // 0xe7de48: stur            x4, [fp, #-0x20]
    // 0xe7de4c: cmp             w4, NULL
    // 0xe7de50: b.eq            #0xe7e038
    // 0xe7de54: LoadField: r5 = r0->field_b
    //     0xe7de54: ldur            w5, [x0, #0xb]
    // 0xe7de58: DecompressPointer r5
    //     0xe7de58: add             x5, x5, HEAP, lsl #32
    // 0xe7de5c: r16 = Sentinel
    //     0xe7de5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7de60: cmp             w5, w16
    // 0xe7de64: b.eq            #0xe7e03c
    // 0xe7de68: stur            x5, [fp, #-0x18]
    // 0xe7de6c: LoadField: r6 = r5->field_13
    //     0xe7de6c: ldur            w6, [x5, #0x13]
    // 0xe7de70: stur            x6, [fp, #-0x10]
    // 0xe7de74: r7 = LoadInt32Instr(r6)
    //     0xe7de74: sbfx            x7, x6, #1, #0x1f
    // 0xe7de78: stur            x7, [fp, #-8]
    // 0xe7de7c: tbnz            x7, #0x3f, #0xe7de90
    // 0xe7de80: LoadField: r1 = r4->field_13
    //     0xe7de80: ldur            w1, [x4, #0x13]
    // 0xe7de84: r2 = LoadInt32Instr(r1)
    //     0xe7de84: sbfx            x2, x1, #1, #0x1f
    // 0xe7de88: cmp             x7, x2
    // 0xe7de8c: b.le            #0xe7dea8
    // 0xe7de90: LoadField: r1 = r4->field_13
    //     0xe7de90: ldur            w1, [x4, #0x13]
    // 0xe7de94: r3 = LoadInt32Instr(r1)
    //     0xe7de94: sbfx            x3, x1, #1, #0x1f
    // 0xe7de98: mov             x2, x6
    // 0xe7de9c: r1 = 0
    //     0xe7de9c: movz            x1, #0
    // 0xe7dea0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe7dea0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe7dea4: r0 = checkValidRange()
    //     0xe7dea4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe7dea8: ldur            x2, [fp, #-8]
    // 0xe7deac: cbz             x2, #0xe7dfd8
    // 0xe7deb0: ldur            x0, [fp, #-0x10]
    // 0xe7deb4: cmp             w0, #0x800
    // 0xe7deb8: b.ge            #0xe7df8c
    // 0xe7debc: ldur            x1, [fp, #-0x20]
    // 0xe7dec0: ldur            x3, [fp, #-0x18]
    // 0xe7dec4: mov             x4, x0
    // 0xe7dec8: add             x2, x3, #0x17
    // 0xe7decc: add             x0, x1, #0x17
    // 0xe7ded0: cbz             x4, #0xe7df88
    // 0xe7ded4: cmp             x0, x2
    // 0xe7ded8: b.ls            #0xe7df40
    // 0xe7dedc: sxtw            x4, w4
    // 0xe7dee0: add             x16, x2, x4, asr #1
    // 0xe7dee4: cmp             x0, x16
    // 0xe7dee8: b.hs            #0xe7df40
    // 0xe7deec: mov             x2, x16
    // 0xe7def0: add             x0, x0, x4, asr #1
    // 0xe7def4: tbz             w4, #4, #0xe7df00
    // 0xe7def8: ldr             x16, [x2, #-8]!
    // 0xe7defc: str             x16, [x0, #-8]!
    // 0xe7df00: tbz             w4, #3, #0xe7df0c
    // 0xe7df04: ldr             w16, [x2, #-4]!
    // 0xe7df08: str             w16, [x0, #-4]!
    // 0xe7df0c: tbz             w4, #2, #0xe7df18
    // 0xe7df10: ldrh            w16, [x2, #-2]!
    // 0xe7df14: strh            w16, [x0, #-2]!
    // 0xe7df18: tbz             w4, #1, #0xe7df24
    // 0xe7df1c: ldrb            w16, [x2, #-1]!
    // 0xe7df20: strb            w16, [x0, #-1]!
    // 0xe7df24: ands            w4, w4, #0xffffffe1
    // 0xe7df28: b.eq            #0xe7df88
    // 0xe7df2c: ldp             x16, x17, [x2, #-0x10]!
    // 0xe7df30: stp             x16, x17, [x0, #-0x10]!
    // 0xe7df34: subs            w4, w4, #0x20
    // 0xe7df38: b.ne            #0xe7df2c
    // 0xe7df3c: b               #0xe7df88
    // 0xe7df40: tbz             w4, #4, #0xe7df4c
    // 0xe7df44: ldr             x16, [x2], #8
    // 0xe7df48: str             x16, [x0], #8
    // 0xe7df4c: tbz             w4, #3, #0xe7df58
    // 0xe7df50: ldr             w16, [x2], #4
    // 0xe7df54: str             w16, [x0], #4
    // 0xe7df58: tbz             w4, #2, #0xe7df64
    // 0xe7df5c: ldrh            w16, [x2], #2
    // 0xe7df60: strh            w16, [x0], #2
    // 0xe7df64: tbz             w4, #1, #0xe7df70
    // 0xe7df68: ldrb            w16, [x2], #1
    // 0xe7df6c: strb            w16, [x0], #1
    // 0xe7df70: ands            w4, w4, #0xffffffe1
    // 0xe7df74: b.eq            #0xe7df88
    // 0xe7df78: ldp             x16, x17, [x2], #0x10
    // 0xe7df7c: stp             x16, x17, [x0], #0x10
    // 0xe7df80: subs            w4, w4, #0x20
    // 0xe7df84: b.ne            #0xe7df78
    // 0xe7df88: b               #0xe7dfd8
    // 0xe7df8c: ldur            x1, [fp, #-0x20]
    // 0xe7df90: ldur            x3, [fp, #-0x18]
    // 0xe7df94: LoadField: r0 = r1->field_7
    //     0xe7df94: ldur            x0, [x1, #7]
    // 0xe7df98: LoadField: r1 = r3->field_7
    //     0xe7df98: ldur            x1, [x3, #7]
    // 0xe7df9c: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe7df9c: mov             x3, THR
    //     0xe7dfa0: ldr             x9, [x3, #0x658]
    //     0xe7dfa4: mov             x17, fp
    //     0xe7dfa8: str             fp, [SP, #-8]!
    //     0xe7dfac: mov             fp, SP
    //     0xe7dfb0: and             SP, SP, #0xfffffffffffffff0
    //     0xe7dfb4: mov             x19, sp
    //     0xe7dfb8: mov             sp, SP
    //     0xe7dfbc: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7dfc0: blr             x9
    //     0xe7dfc4: movz            x16, #0x8
    //     0xe7dfc8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7dfcc: mov             sp, x19
    //     0xe7dfd0: mov             SP, fp
    //     0xe7dfd4: ldr             fp, [SP], #8
    // 0xe7dfd8: ldur            x0, [fp, #-0x28]
    // 0xe7dfdc: LoadField: r1 = r0->field_13
    //     0xe7dfdc: ldur            w1, [x0, #0x13]
    // 0xe7dfe0: DecompressPointer r1
    //     0xe7dfe0: add             x1, x1, HEAP, lsl #32
    // 0xe7dfe4: cmp             w1, NULL
    // 0xe7dfe8: b.eq            #0xe7e048
    // 0xe7dfec: LoadField: r2 = r1->field_13
    //     0xe7dfec: ldur            w2, [x1, #0x13]
    // 0xe7dff0: r3 = LoadInt32Instr(r2)
    //     0xe7dff0: sbfx            x3, x2, #1, #0x1f
    // 0xe7dff4: r2 = 0
    //     0xe7dff4: movz            x2, #0
    // 0xe7dff8: r5 = 0
    //     0xe7dff8: movz            x5, #0
    // 0xe7dffc: r0 = fillRange()
    //     0xe7dffc: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0xe7e000: ldur            x0, [fp, #-0x28]
    // 0xe7e004: LoadField: r1 = r0->field_7
    //     0xe7e004: ldur            w1, [x0, #7]
    // 0xe7e008: DecompressPointer r1
    //     0xe7e008: add             x1, x1, HEAP, lsl #32
    // 0xe7e00c: r0 = LoadClassIdInstr(r1)
    //     0xe7e00c: ldur            x0, [x1, #-1]
    //     0xe7e010: ubfx            x0, x0, #0xc, #0x14
    // 0xe7e014: r0 = GDT[cid_x0 + -0xeaf]()
    //     0xe7e014: sub             lr, x0, #0xeaf
    //     0xe7e018: ldr             lr, [x21, lr, lsl #3]
    //     0xe7e01c: blr             lr
    // 0xe7e020: r0 = Null
    //     0xe7e020: mov             x0, NULL
    // 0xe7e024: LeaveFrame
    //     0xe7e024: mov             SP, fp
    //     0xe7e028: ldp             fp, lr, [SP], #0x10
    // 0xe7e02c: ret
    //     0xe7e02c: ret             
    // 0xe7e030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7e030: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7e034: b               #0xe7de40
    // 0xe7e038: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe7e038: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe7e03c: r9 = _iv
    //     0xe7e03c: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d78] Field <CBCBlockCipher._iv@908520057>: late (offset: 0xc)
    //     0xe7e040: ldr             x9, [x9, #0xd78]
    // 0xe7e044: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7e044: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe7e048: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe7e048: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0xe84fc8, size: 0x2c0
    // 0xe84fc8: EnterFrame
    //     0xe84fc8: stp             fp, lr, [SP, #-0x10]!
    //     0xe84fcc: mov             fp, SP
    // 0xe84fd0: AllocStack(0x40)
    //     0xe84fd0: sub             SP, SP, #0x40
    // 0xe84fd4: SetupParameters(CBCBlockCipher this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xe84fd4: mov             x5, x1
    //     0xe84fd8: mov             x4, x2
    //     0xe84fdc: stur            x1, [fp, #-8]
    //     0xe84fe0: stur            x2, [fp, #-0x10]
    //     0xe84fe4: stur            x3, [fp, #-0x18]
    // 0xe84fe8: CheckStackOverflow
    //     0xe84fe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe84fec: cmp             SP, x16
    //     0xe84ff0: b.ls            #0xe85274
    // 0xe84ff4: mov             x0, x3
    // 0xe84ff8: r2 = Null
    //     0xe84ff8: mov             x2, NULL
    // 0xe84ffc: r1 = Null
    //     0xe84ffc: mov             x1, NULL
    // 0xe85000: r4 = 60
    //     0xe85000: movz            x4, #0x3c
    // 0xe85004: branchIfSmi(r0, 0xe85010)
    //     0xe85004: tbz             w0, #0, #0xe85010
    // 0xe85008: r4 = LoadClassIdInstr(r0)
    //     0xe85008: ldur            x4, [x0, #-1]
    //     0xe8500c: ubfx            x4, x4, #0xc, #0x14
    // 0xe85010: cmp             x4, #0x2a8
    // 0xe85014: b.eq            #0xe8502c
    // 0xe85018: r8 = ParametersWithIV<CipherParameters?>
    //     0xe85018: add             x8, PP, #0x21, lsl #12  ; [pp+0x21ce8] Type: ParametersWithIV<CipherParameters?>
    //     0xe8501c: ldr             x8, [x8, #0xce8]
    // 0xe85020: r3 = Null
    //     0xe85020: add             x3, PP, #0x21, lsl #12  ; [pp+0x21d68] Null
    //     0xe85024: ldr             x3, [x3, #0xd68]
    // 0xe85028: r0 = DefaultTypeTest()
    //     0xe85028: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe8502c: ldur            x2, [fp, #-0x18]
    // 0xe85030: LoadField: r3 = r2->field_b
    //     0xe85030: ldur            w3, [x2, #0xb]
    // 0xe85034: DecompressPointer r3
    //     0xe85034: add             x3, x3, HEAP, lsl #32
    // 0xe85038: stur            x3, [fp, #-0x30]
    // 0xe8503c: LoadField: r4 = r3->field_13
    //     0xe8503c: ldur            w4, [x3, #0x13]
    // 0xe85040: ldur            x5, [fp, #-8]
    // 0xe85044: stur            x4, [fp, #-0x28]
    // 0xe85048: LoadField: r6 = r5->field_7
    //     0xe85048: ldur            w6, [x5, #7]
    // 0xe8504c: DecompressPointer r6
    //     0xe8504c: add             x6, x6, HEAP, lsl #32
    // 0xe85050: stur            x6, [fp, #-0x20]
    // 0xe85054: r0 = LoadClassIdInstr(r6)
    //     0xe85054: ldur            x0, [x6, #-1]
    //     0xe85058: ubfx            x0, x0, #0xc, #0x14
    // 0xe8505c: mov             x1, x6
    // 0xe85060: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe85060: sub             lr, x0, #1, lsl #12
    //     0xe85064: ldr             lr, [x21, lr, lsl #3]
    //     0xe85068: blr             lr
    // 0xe8506c: mov             x1, x0
    // 0xe85070: ldur            x0, [fp, #-0x28]
    // 0xe85074: r4 = LoadInt32Instr(r0)
    //     0xe85074: sbfx            x4, x0, #1, #0x1f
    // 0xe85078: stur            x4, [fp, #-0x40]
    // 0xe8507c: cmp             x4, x1
    // 0xe85080: b.ne            #0xe8524c
    // 0xe85084: ldur            x5, [fp, #-8]
    // 0xe85088: ldur            x6, [fp, #-0x10]
    // 0xe8508c: ArrayStore: r5[0] = r6  ; List_4
    //     0xe8508c: stur            w6, [x5, #0x17]
    // 0xe85090: LoadField: r7 = r5->field_b
    //     0xe85090: ldur            w7, [x5, #0xb]
    // 0xe85094: DecompressPointer r7
    //     0xe85094: add             x7, x7, HEAP, lsl #32
    // 0xe85098: r16 = Sentinel
    //     0xe85098: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8509c: cmp             w7, w16
    // 0xe850a0: b.eq            #0xe8527c
    // 0xe850a4: stur            x7, [fp, #-0x38]
    // 0xe850a8: tbnz            x4, #0x3f, #0xe850bc
    // 0xe850ac: LoadField: r1 = r7->field_13
    //     0xe850ac: ldur            w1, [x7, #0x13]
    // 0xe850b0: r2 = LoadInt32Instr(r1)
    //     0xe850b0: sbfx            x2, x1, #1, #0x1f
    // 0xe850b4: cmp             x4, x2
    // 0xe850b8: b.le            #0xe850d4
    // 0xe850bc: LoadField: r1 = r7->field_13
    //     0xe850bc: ldur            w1, [x7, #0x13]
    // 0xe850c0: r3 = LoadInt32Instr(r1)
    //     0xe850c0: sbfx            x3, x1, #1, #0x1f
    // 0xe850c4: mov             x2, x0
    // 0xe850c8: r1 = 0
    //     0xe850c8: movz            x1, #0
    // 0xe850cc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe850cc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe850d0: r0 = checkValidRange()
    //     0xe850d0: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe850d4: ldur            x2, [fp, #-0x40]
    // 0xe850d8: cbz             x2, #0xe85204
    // 0xe850dc: ldur            x0, [fp, #-0x28]
    // 0xe850e0: cmp             w0, #0x800
    // 0xe850e4: b.ge            #0xe851b8
    // 0xe850e8: ldur            x3, [fp, #-0x30]
    // 0xe850ec: ldur            x1, [fp, #-0x38]
    // 0xe850f0: mov             x4, x0
    // 0xe850f4: add             x2, x3, #0x17
    // 0xe850f8: add             x0, x1, #0x17
    // 0xe850fc: cbz             x4, #0xe851b4
    // 0xe85100: cmp             x0, x2
    // 0xe85104: b.ls            #0xe8516c
    // 0xe85108: sxtw            x4, w4
    // 0xe8510c: add             x16, x2, x4, asr #1
    // 0xe85110: cmp             x0, x16
    // 0xe85114: b.hs            #0xe8516c
    // 0xe85118: mov             x2, x16
    // 0xe8511c: add             x0, x0, x4, asr #1
    // 0xe85120: tbz             w4, #4, #0xe8512c
    // 0xe85124: ldr             x16, [x2, #-8]!
    // 0xe85128: str             x16, [x0, #-8]!
    // 0xe8512c: tbz             w4, #3, #0xe85138
    // 0xe85130: ldr             w16, [x2, #-4]!
    // 0xe85134: str             w16, [x0, #-4]!
    // 0xe85138: tbz             w4, #2, #0xe85144
    // 0xe8513c: ldrh            w16, [x2, #-2]!
    // 0xe85140: strh            w16, [x0, #-2]!
    // 0xe85144: tbz             w4, #1, #0xe85150
    // 0xe85148: ldrb            w16, [x2, #-1]!
    // 0xe8514c: strb            w16, [x0, #-1]!
    // 0xe85150: ands            w4, w4, #0xffffffe1
    // 0xe85154: b.eq            #0xe851b4
    // 0xe85158: ldp             x16, x17, [x2, #-0x10]!
    // 0xe8515c: stp             x16, x17, [x0, #-0x10]!
    // 0xe85160: subs            w4, w4, #0x20
    // 0xe85164: b.ne            #0xe85158
    // 0xe85168: b               #0xe851b4
    // 0xe8516c: tbz             w4, #4, #0xe85178
    // 0xe85170: ldr             x16, [x2], #8
    // 0xe85174: str             x16, [x0], #8
    // 0xe85178: tbz             w4, #3, #0xe85184
    // 0xe8517c: ldr             w16, [x2], #4
    // 0xe85180: str             w16, [x0], #4
    // 0xe85184: tbz             w4, #2, #0xe85190
    // 0xe85188: ldrh            w16, [x2], #2
    // 0xe8518c: strh            w16, [x0], #2
    // 0xe85190: tbz             w4, #1, #0xe8519c
    // 0xe85194: ldrb            w16, [x2], #1
    // 0xe85198: strb            w16, [x0], #1
    // 0xe8519c: ands            w4, w4, #0xffffffe1
    // 0xe851a0: b.eq            #0xe851b4
    // 0xe851a4: ldp             x16, x17, [x2], #0x10
    // 0xe851a8: stp             x16, x17, [x0], #0x10
    // 0xe851ac: subs            w4, w4, #0x20
    // 0xe851b0: b.ne            #0xe851a4
    // 0xe851b4: b               #0xe85204
    // 0xe851b8: ldur            x3, [fp, #-0x30]
    // 0xe851bc: ldur            x1, [fp, #-0x38]
    // 0xe851c0: LoadField: r0 = r1->field_7
    //     0xe851c0: ldur            x0, [x1, #7]
    // 0xe851c4: LoadField: r1 = r3->field_7
    //     0xe851c4: ldur            x1, [x3, #7]
    // 0xe851c8: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe851c8: mov             x3, THR
    //     0xe851cc: ldr             x9, [x3, #0x658]
    //     0xe851d0: mov             x17, fp
    //     0xe851d4: str             fp, [SP, #-8]!
    //     0xe851d8: mov             fp, SP
    //     0xe851dc: and             SP, SP, #0xfffffffffffffff0
    //     0xe851e0: mov             x19, sp
    //     0xe851e4: mov             sp, SP
    //     0xe851e8: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe851ec: blr             x9
    //     0xe851f0: movz            x16, #0x8
    //     0xe851f4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe851f8: mov             sp, x19
    //     0xe851fc: mov             SP, fp
    //     0xe85200: ldr             fp, [SP], #8
    // 0xe85204: ldur            x0, [fp, #-0x18]
    // 0xe85208: ldur            x2, [fp, #-0x20]
    // 0xe8520c: ldur            x1, [fp, #-8]
    // 0xe85210: r0 = reset()
    //     0xe85210: bl              #0xe7de20  ; [package:pointycastle/block/modes/cbc.dart] CBCBlockCipher::reset
    // 0xe85214: ldur            x0, [fp, #-0x18]
    // 0xe85218: LoadField: r3 = r0->field_f
    //     0xe85218: ldur            w3, [x0, #0xf]
    // 0xe8521c: DecompressPointer r3
    //     0xe8521c: add             x3, x3, HEAP, lsl #32
    // 0xe85220: ldur            x1, [fp, #-0x20]
    // 0xe85224: r0 = LoadClassIdInstr(r1)
    //     0xe85224: ldur            x0, [x1, #-1]
    //     0xe85228: ubfx            x0, x0, #0xc, #0x14
    // 0xe8522c: ldur            x2, [fp, #-0x10]
    // 0xe85230: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe85230: sub             lr, x0, #0xeda
    //     0xe85234: ldr             lr, [x21, lr, lsl #3]
    //     0xe85238: blr             lr
    // 0xe8523c: r0 = Null
    //     0xe8523c: mov             x0, NULL
    // 0xe85240: LeaveFrame
    //     0xe85240: mov             SP, fp
    //     0xe85244: ldp             fp, lr, [SP], #0x10
    // 0xe85248: ret
    //     0xe85248: ret             
    // 0xe8524c: r0 = ArgumentError()
    //     0xe8524c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe85250: mov             x1, x0
    // 0xe85254: r0 = "Initialization vector must be the same length as block size"
    //     0xe85254: add             x0, PP, #0x21, lsl #12  ; [pp+0x21d00] "Initialization vector must be the same length as block size"
    //     0xe85258: ldr             x0, [x0, #0xd00]
    // 0xe8525c: ArrayStore: r1[0] = r0  ; List_4
    //     0xe8525c: stur            w0, [x1, #0x17]
    // 0xe85260: r0 = false
    //     0xe85260: add             x0, NULL, #0x30  ; false
    // 0xe85264: StoreField: r1->field_b = r0
    //     0xe85264: stur            w0, [x1, #0xb]
    // 0xe85268: mov             x0, x1
    // 0xe8526c: r0 = Throw()
    //     0xe8526c: bl              #0xec04b8  ; ThrowStub
    // 0xe85270: brk             #0
    // 0xe85274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe85274: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe85278: b               #0xe84ff4
    // 0xe8527c: r9 = _iv
    //     0xe8527c: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d78] Field <CBCBlockCipher._iv@908520057>: late (offset: 0xc)
    //     0xe85280: ldr             x9, [x9, #0xd78]
    // 0xe85284: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe85284: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xea5c3c, size: 0x58
    // 0xea5c3c: EnterFrame
    //     0xea5c3c: stp             fp, lr, [SP, #-0x10]!
    //     0xea5c40: mov             fp, SP
    // 0xea5c44: CheckStackOverflow
    //     0xea5c44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea5c48: cmp             SP, x16
    //     0xea5c4c: b.ls            #0xea5c80
    // 0xea5c50: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xea5c50: ldur            w0, [x1, #0x17]
    // 0xea5c54: DecompressPointer r0
    //     0xea5c54: add             x0, x0, HEAP, lsl #32
    // 0xea5c58: r16 = Sentinel
    //     0xea5c58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea5c5c: cmp             w0, w16
    // 0xea5c60: b.eq            #0xea5c88
    // 0xea5c64: tbnz            w0, #4, #0xea5c70
    // 0xea5c68: r0 = _encryptBlock()
    //     0xea5c68: bl              #0xea6118  ; [package:pointycastle/block/modes/cbc.dart] CBCBlockCipher::_encryptBlock
    // 0xea5c6c: b               #0xea5c74
    // 0xea5c70: r0 = _decryptBlock()
    //     0xea5c70: bl              #0xea5c94  ; [package:pointycastle/block/modes/cbc.dart] CBCBlockCipher::_decryptBlock
    // 0xea5c74: LeaveFrame
    //     0xea5c74: mov             SP, fp
    //     0xea5c78: ldp             fp, lr, [SP], #0x10
    // 0xea5c7c: ret
    //     0xea5c7c: ret             
    // 0xea5c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea5c80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea5c84: b               #0xea5c50
    // 0xea5c88: r9 = _encrypting
    //     0xea5c88: add             x9, PP, #0x23, lsl #12  ; [pp+0x236a0] Field <CBCBlockCipher._encrypting@908520057>: late (offset: 0x18)
    //     0xea5c8c: ldr             x9, [x9, #0x6a0]
    // 0xea5c90: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea5c90: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _decryptBlock(/* No info */) {
    // ** addr: 0xea5c94, size: 0x484
    // 0xea5c94: EnterFrame
    //     0xea5c94: stp             fp, lr, [SP, #-0x10]!
    //     0xea5c98: mov             fp, SP
    // 0xea5c9c: AllocStack(0x60)
    //     0xea5c9c: sub             SP, SP, #0x60
    // 0xea5ca0: SetupParameters(CBCBlockCipher this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* r6 => r6, fp-0x30 */)
    //     0xea5ca0: mov             x4, x1
    //     0xea5ca4: stur            x1, [fp, #-0x10]
    //     0xea5ca8: stur            x2, [fp, #-0x18]
    //     0xea5cac: stur            x3, [fp, #-0x20]
    //     0xea5cb0: stur            x5, [fp, #-0x28]
    //     0xea5cb4: stur            x6, [fp, #-0x30]
    // 0xea5cb8: CheckStackOverflow
    //     0xea5cb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea5cbc: cmp             SP, x16
    //     0xea5cc0: b.ls            #0xea60f8
    // 0xea5cc4: LoadField: r7 = r4->field_7
    //     0xea5cc4: ldur            w7, [x4, #7]
    // 0xea5cc8: DecompressPointer r7
    //     0xea5cc8: add             x7, x7, HEAP, lsl #32
    // 0xea5ccc: stur            x7, [fp, #-8]
    // 0xea5cd0: r0 = LoadClassIdInstr(r7)
    //     0xea5cd0: ldur            x0, [x7, #-1]
    //     0xea5cd4: ubfx            x0, x0, #0xc, #0x14
    // 0xea5cd8: mov             x1, x7
    // 0xea5cdc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea5cdc: sub             lr, x0, #1, lsl #12
    //     0xea5ce0: ldr             lr, [x21, lr, lsl #3]
    //     0xea5ce4: blr             lr
    // 0xea5ce8: ldur            x3, [fp, #-0x20]
    // 0xea5cec: add             x1, x3, x0
    // 0xea5cf0: ldur            x2, [fp, #-0x18]
    // 0xea5cf4: LoadField: r0 = r2->field_13
    //     0xea5cf4: ldur            w0, [x2, #0x13]
    // 0xea5cf8: r4 = LoadInt32Instr(r0)
    //     0xea5cf8: sbfx            x4, x0, #1, #0x1f
    // 0xea5cfc: cmp             x1, x4
    // 0xea5d00: b.gt            #0xea60c4
    // 0xea5d04: ldur            x4, [fp, #-0x10]
    // 0xea5d08: ldur            x5, [fp, #-8]
    // 0xea5d0c: LoadField: r6 = r4->field_13
    //     0xea5d0c: ldur            w6, [x4, #0x13]
    // 0xea5d10: DecompressPointer r6
    //     0xea5d10: add             x6, x6, HEAP, lsl #32
    // 0xea5d14: stur            x6, [fp, #-0x38]
    // 0xea5d18: cmp             w6, NULL
    // 0xea5d1c: b.eq            #0xea6100
    // 0xea5d20: r0 = LoadClassIdInstr(r5)
    //     0xea5d20: ldur            x0, [x5, #-1]
    //     0xea5d24: ubfx            x0, x0, #0xc, #0x14
    // 0xea5d28: mov             x1, x5
    // 0xea5d2c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea5d2c: sub             lr, x0, #1, lsl #12
    //     0xea5d30: ldr             lr, [x21, lr, lsl #3]
    //     0xea5d34: blr             lr
    // 0xea5d38: stur            x0, [fp, #-0x40]
    // 0xea5d3c: r0 = _ByteBuffer()
    //     0xea5d3c: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0xea5d40: mov             x3, x0
    // 0xea5d44: ldur            x2, [fp, #-0x18]
    // 0xea5d48: stur            x3, [fp, #-0x48]
    // 0xea5d4c: StoreField: r3->field_7 = r2
    //     0xea5d4c: stur            w2, [x3, #7]
    // 0xea5d50: ldur            x4, [fp, #-8]
    // 0xea5d54: r0 = LoadClassIdInstr(r4)
    //     0xea5d54: ldur            x0, [x4, #-1]
    //     0xea5d58: ubfx            x0, x0, #0xc, #0x14
    // 0xea5d5c: mov             x1, x4
    // 0xea5d60: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea5d60: sub             lr, x0, #1, lsl #12
    //     0xea5d64: ldr             lr, [x21, lr, lsl #3]
    //     0xea5d68: blr             lr
    // 0xea5d6c: mov             x2, x0
    // 0xea5d70: ldur            x3, [fp, #-0x20]
    // 0xea5d74: r0 = BoxInt64Instr(r3)
    //     0xea5d74: sbfiz           x0, x3, #1, #0x1f
    //     0xea5d78: cmp             x3, x0, asr #1
    //     0xea5d7c: b.eq            #0xea5d88
    //     0xea5d80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea5d84: stur            x3, [x0, #7]
    // 0xea5d88: mov             x4, x0
    // 0xea5d8c: r0 = BoxInt64Instr(r2)
    //     0xea5d8c: sbfiz           x0, x2, #1, #0x1f
    //     0xea5d90: cmp             x2, x0, asr #1
    //     0xea5d94: b.eq            #0xea5da0
    //     0xea5d98: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea5d9c: stur            x2, [x0, #7]
    // 0xea5da0: stp             x0, x4, [SP]
    // 0xea5da4: ldur            x1, [fp, #-0x48]
    // 0xea5da8: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0xea5da8: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0xea5dac: r0 = asUint8List()
    //     0xea5dac: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0xea5db0: mov             x5, x0
    // 0xea5db4: ldur            x4, [fp, #-0x40]
    // 0xea5db8: stur            x5, [fp, #-0x48]
    // 0xea5dbc: tbz             x4, #0x3f, #0xea5dc8
    // 0xea5dc0: ldur            x6, [fp, #-0x38]
    // 0xea5dc4: b               #0xea5ddc
    // 0xea5dc8: ldur            x6, [fp, #-0x38]
    // 0xea5dcc: LoadField: r0 = r6->field_13
    //     0xea5dcc: ldur            w0, [x6, #0x13]
    // 0xea5dd0: r1 = LoadInt32Instr(r0)
    //     0xea5dd0: sbfx            x1, x0, #1, #0x1f
    // 0xea5dd4: cmp             x4, x1
    // 0xea5dd8: b.le            #0xea5e08
    // 0xea5ddc: LoadField: r2 = r6->field_13
    //     0xea5ddc: ldur            w2, [x6, #0x13]
    // 0xea5de0: r0 = BoxInt64Instr(r4)
    //     0xea5de0: sbfiz           x0, x4, #1, #0x1f
    //     0xea5de4: cmp             x4, x0, asr #1
    //     0xea5de8: b.eq            #0xea5df4
    //     0xea5dec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea5df0: stur            x4, [x0, #7]
    // 0xea5df4: r3 = LoadInt32Instr(r2)
    //     0xea5df4: sbfx            x3, x2, #1, #0x1f
    // 0xea5df8: mov             x2, x0
    // 0xea5dfc: r1 = 0
    //     0xea5dfc: movz            x1, #0
    // 0xea5e00: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea5e00: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea5e04: r0 = checkValidRange()
    //     0xea5e04: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea5e08: ldur            x2, [fp, #-0x40]
    // 0xea5e0c: ldur            x3, [fp, #-0x48]
    // 0xea5e10: LoadField: r0 = r3->field_13
    //     0xea5e10: ldur            w0, [x3, #0x13]
    // 0xea5e14: r1 = LoadInt32Instr(r0)
    //     0xea5e14: sbfx            x1, x0, #1, #0x1f
    // 0xea5e18: cmp             x1, x2
    // 0xea5e1c: b.lt            #0xea60ec
    // 0xea5e20: cbz             x2, #0xea5f58
    // 0xea5e24: r0 = BoxInt64Instr(r2)
    //     0xea5e24: sbfiz           x0, x2, #1, #0x1f
    //     0xea5e28: cmp             x2, x0, asr #1
    //     0xea5e2c: b.eq            #0xea5e38
    //     0xea5e30: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea5e34: stur            x2, [x0, #7]
    // 0xea5e38: cmp             w0, #0x800
    // 0xea5e3c: b.ge            #0xea5f10
    // 0xea5e40: ldur            x1, [fp, #-0x38]
    // 0xea5e44: LoadField: r2 = r3->field_7
    //     0xea5e44: ldur            x2, [x3, #7]
    // 0xea5e48: mov             x4, x0
    // 0xea5e4c: mov             x3, x2
    // 0xea5e50: add             x0, x1, #0x17
    // 0xea5e54: cbz             x4, #0xea5f0c
    // 0xea5e58: cmp             x0, x3
    // 0xea5e5c: b.ls            #0xea5ec4
    // 0xea5e60: sxtw            x4, w4
    // 0xea5e64: add             x16, x3, x4, asr #1
    // 0xea5e68: cmp             x0, x16
    // 0xea5e6c: b.hs            #0xea5ec4
    // 0xea5e70: mov             x3, x16
    // 0xea5e74: add             x0, x0, x4, asr #1
    // 0xea5e78: tbz             w4, #4, #0xea5e84
    // 0xea5e7c: ldr             x16, [x3, #-8]!
    // 0xea5e80: str             x16, [x0, #-8]!
    // 0xea5e84: tbz             w4, #3, #0xea5e90
    // 0xea5e88: ldr             w16, [x3, #-4]!
    // 0xea5e8c: str             w16, [x0, #-4]!
    // 0xea5e90: tbz             w4, #2, #0xea5e9c
    // 0xea5e94: ldrh            w16, [x3, #-2]!
    // 0xea5e98: strh            w16, [x0, #-2]!
    // 0xea5e9c: tbz             w4, #1, #0xea5ea8
    // 0xea5ea0: ldrb            w16, [x3, #-1]!
    // 0xea5ea4: strb            w16, [x0, #-1]!
    // 0xea5ea8: ands            w4, w4, #0xffffffe1
    // 0xea5eac: b.eq            #0xea5f0c
    // 0xea5eb0: ldp             x16, x17, [x3, #-0x10]!
    // 0xea5eb4: stp             x16, x17, [x0, #-0x10]!
    // 0xea5eb8: subs            w4, w4, #0x20
    // 0xea5ebc: b.ne            #0xea5eb0
    // 0xea5ec0: b               #0xea5f0c
    // 0xea5ec4: tbz             w4, #4, #0xea5ed0
    // 0xea5ec8: ldr             x16, [x3], #8
    // 0xea5ecc: str             x16, [x0], #8
    // 0xea5ed0: tbz             w4, #3, #0xea5edc
    // 0xea5ed4: ldr             w16, [x3], #4
    // 0xea5ed8: str             w16, [x0], #4
    // 0xea5edc: tbz             w4, #2, #0xea5ee8
    // 0xea5ee0: ldrh            w16, [x3], #2
    // 0xea5ee4: strh            w16, [x0], #2
    // 0xea5ee8: tbz             w4, #1, #0xea5ef4
    // 0xea5eec: ldrb            w16, [x3], #1
    // 0xea5ef0: strb            w16, [x0], #1
    // 0xea5ef4: ands            w4, w4, #0xffffffe1
    // 0xea5ef8: b.eq            #0xea5f0c
    // 0xea5efc: ldp             x16, x17, [x3], #0x10
    // 0xea5f00: stp             x16, x17, [x0], #0x10
    // 0xea5f04: subs            w4, w4, #0x20
    // 0xea5f08: b.ne            #0xea5efc
    // 0xea5f0c: b               #0xea5f58
    // 0xea5f10: ldur            x1, [fp, #-0x38]
    // 0xea5f14: LoadField: r0 = r1->field_7
    //     0xea5f14: ldur            x0, [x1, #7]
    // 0xea5f18: LoadField: r1 = r3->field_7
    //     0xea5f18: ldur            x1, [x3, #7]
    // 0xea5f1c: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea5f1c: mov             x3, THR
    //     0xea5f20: ldr             x9, [x3, #0x658]
    //     0xea5f24: mov             x17, fp
    //     0xea5f28: str             fp, [SP, #-8]!
    //     0xea5f2c: mov             fp, SP
    //     0xea5f30: and             SP, SP, #0xfffffffffffffff0
    //     0xea5f34: mov             x19, sp
    //     0xea5f38: mov             sp, SP
    //     0xea5f3c: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea5f40: blr             x9
    //     0xea5f44: movz            x16, #0x8
    //     0xea5f48: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea5f4c: mov             sp, x19
    //     0xea5f50: mov             SP, fp
    //     0xea5f54: ldr             fp, [SP], #8
    // 0xea5f58: ldur            x7, [fp, #-0x28]
    // 0xea5f5c: ldur            x4, [fp, #-8]
    // 0xea5f60: r0 = LoadClassIdInstr(r4)
    //     0xea5f60: ldur            x0, [x4, #-1]
    //     0xea5f64: ubfx            x0, x0, #0xc, #0x14
    // 0xea5f68: mov             x1, x4
    // 0xea5f6c: ldur            x2, [fp, #-0x18]
    // 0xea5f70: ldur            x3, [fp, #-0x20]
    // 0xea5f74: mov             x5, x7
    // 0xea5f78: ldur            x6, [fp, #-0x30]
    // 0xea5f7c: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea5f7c: sub             lr, x0, #0xf69
    //     0xea5f80: ldr             lr, [x21, lr, lsl #3]
    //     0xea5f84: blr             lr
    // 0xea5f88: mov             x3, x0
    // 0xea5f8c: ldur            x2, [fp, #-0x28]
    // 0xea5f90: stur            x3, [fp, #-0x50]
    // 0xea5f94: LoadField: r0 = r2->field_13
    //     0xea5f94: ldur            w0, [x2, #0x13]
    // 0xea5f98: r4 = LoadInt32Instr(r0)
    //     0xea5f98: sbfx            x4, x0, #1, #0x1f
    // 0xea5f9c: stur            x4, [fp, #-0x40]
    // 0xea5fa0: ldur            x6, [fp, #-0x10]
    // 0xea5fa4: ldur            x7, [fp, #-0x30]
    // 0xea5fa8: r8 = 0
    //     0xea5fa8: movz            x8, #0
    // 0xea5fac: ldur            x5, [fp, #-8]
    // 0xea5fb0: stur            x8, [fp, #-0x20]
    // 0xea5fb4: CheckStackOverflow
    //     0xea5fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea5fb8: cmp             SP, x16
    //     0xea5fbc: b.ls            #0xea6104
    // 0xea5fc0: r0 = LoadClassIdInstr(r5)
    //     0xea5fc0: ldur            x0, [x5, #-1]
    //     0xea5fc4: ubfx            x0, x0, #0xc, #0x14
    // 0xea5fc8: mov             x1, x5
    // 0xea5fcc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea5fcc: sub             lr, x0, #1, lsl #12
    //     0xea5fd0: ldr             lr, [x21, lr, lsl #3]
    //     0xea5fd4: blr             lr
    // 0xea5fd8: ldur            x2, [fp, #-0x20]
    // 0xea5fdc: cmp             x2, x0
    // 0xea5fe0: b.ge            #0xea6064
    // 0xea5fe4: ldur            x4, [fp, #-0x10]
    // 0xea5fe8: ldur            x3, [fp, #-0x28]
    // 0xea5fec: ldur            x5, [fp, #-0x30]
    // 0xea5ff0: add             x6, x5, x2
    // 0xea5ff4: ldur            x0, [fp, #-0x40]
    // 0xea5ff8: mov             x1, x6
    // 0xea5ffc: cmp             x1, x0
    // 0xea6000: b.hs            #0xea610c
    // 0xea6004: ArrayLoad: r7 = r3[r6]  ; List_1
    //     0xea6004: add             x16, x3, x6
    //     0xea6008: ldrb            w7, [x16, #0x17]
    // 0xea600c: LoadField: r8 = r4->field_f
    //     0xea600c: ldur            w8, [x4, #0xf]
    // 0xea6010: DecompressPointer r8
    //     0xea6010: add             x8, x8, HEAP, lsl #32
    // 0xea6014: cmp             w8, NULL
    // 0xea6018: b.eq            #0xea6110
    // 0xea601c: LoadField: r0 = r8->field_13
    //     0xea601c: ldur            w0, [x8, #0x13]
    // 0xea6020: r1 = LoadInt32Instr(r0)
    //     0xea6020: sbfx            x1, x0, #1, #0x1f
    // 0xea6024: mov             x0, x1
    // 0xea6028: mov             x1, x2
    // 0xea602c: cmp             x1, x0
    // 0xea6030: b.hs            #0xea6114
    // 0xea6034: ArrayLoad: r0 = r8[r2]  ; List_1
    //     0xea6034: add             x16, x8, x2
    //     0xea6038: ldrb            w0, [x16, #0x17]
    // 0xea603c: eor             x1, x7, x0
    // 0xea6040: ArrayStore: r3[r6] = r1  ; TypeUnknown_1
    //     0xea6040: add             x0, x3, x6
    //     0xea6044: strb            w1, [x0, #0x17]
    // 0xea6048: add             x8, x2, #1
    // 0xea604c: mov             x6, x4
    // 0xea6050: mov             x2, x3
    // 0xea6054: mov             x7, x5
    // 0xea6058: ldur            x3, [fp, #-0x50]
    // 0xea605c: ldur            x4, [fp, #-0x40]
    // 0xea6060: b               #0xea5fac
    // 0xea6064: ldur            x4, [fp, #-0x10]
    // 0xea6068: LoadField: r1 = r4->field_f
    //     0xea6068: ldur            w1, [x4, #0xf]
    // 0xea606c: DecompressPointer r1
    //     0xea606c: add             x1, x1, HEAP, lsl #32
    // 0xea6070: LoadField: r0 = r4->field_13
    //     0xea6070: ldur            w0, [x4, #0x13]
    // 0xea6074: DecompressPointer r0
    //     0xea6074: add             x0, x0, HEAP, lsl #32
    // 0xea6078: StoreField: r4->field_f = r0
    //     0xea6078: stur            w0, [x4, #0xf]
    //     0xea607c: ldurb           w16, [x4, #-1]
    //     0xea6080: ldurb           w17, [x0, #-1]
    //     0xea6084: and             x16, x17, x16, lsr #2
    //     0xea6088: tst             x16, HEAP, lsr #32
    //     0xea608c: b.eq            #0xea6094
    //     0xea6090: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xea6094: mov             x0, x1
    // 0xea6098: StoreField: r4->field_13 = r0
    //     0xea6098: stur            w0, [x4, #0x13]
    //     0xea609c: ldurb           w16, [x4, #-1]
    //     0xea60a0: ldurb           w17, [x0, #-1]
    //     0xea60a4: and             x16, x17, x16, lsr #2
    //     0xea60a8: tst             x16, HEAP, lsr #32
    //     0xea60ac: b.eq            #0xea60b4
    //     0xea60b0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xea60b4: ldur            x0, [fp, #-0x50]
    // 0xea60b8: LeaveFrame
    //     0xea60b8: mov             SP, fp
    //     0xea60bc: ldp             fp, lr, [SP], #0x10
    // 0xea60c0: ret
    //     0xea60c0: ret             
    // 0xea60c4: r0 = ArgumentError()
    //     0xea60c4: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea60c8: mov             x1, x0
    // 0xea60cc: r0 = "Input buffer too short"
    //     0xea60cc: add             x0, PP, #0x23, lsl #12  ; [pp+0x23678] "Input buffer too short"
    //     0xea60d0: ldr             x0, [x0, #0x678]
    // 0xea60d4: ArrayStore: r1[0] = r0  ; List_4
    //     0xea60d4: stur            w0, [x1, #0x17]
    // 0xea60d8: r0 = false
    //     0xea60d8: add             x0, NULL, #0x30  ; false
    // 0xea60dc: StoreField: r1->field_b = r0
    //     0xea60dc: stur            w0, [x1, #0xb]
    // 0xea60e0: mov             x0, x1
    // 0xea60e4: r0 = Throw()
    //     0xea60e4: bl              #0xec04b8  ; ThrowStub
    // 0xea60e8: brk             #0
    // 0xea60ec: r0 = tooFew()
    //     0xea60ec: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea60f0: r0 = Throw()
    //     0xea60f0: bl              #0xec04b8  ; ThrowStub
    // 0xea60f4: brk             #0
    // 0xea60f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea60f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea60fc: b               #0xea5cc4
    // 0xea6100: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6100: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea6104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea6104: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea6108: b               #0xea5fc0
    // 0xea610c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea610c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea6110: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6110: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea6114: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea6114: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _encryptBlock(/* No info */) {
    // ** addr: 0xea6118, size: 0x434
    // 0xea6118: EnterFrame
    //     0xea6118: stp             fp, lr, [SP, #-0x10]!
    //     0xea611c: mov             fp, SP
    // 0xea6120: AllocStack(0x50)
    //     0xea6120: sub             SP, SP, #0x50
    // 0xea6124: SetupParameters(CBCBlockCipher this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* r6 => r6, fp-0x30 */)
    //     0xea6124: mov             x4, x1
    //     0xea6128: stur            x1, [fp, #-0x10]
    //     0xea612c: stur            x2, [fp, #-0x18]
    //     0xea6130: stur            x3, [fp, #-0x20]
    //     0xea6134: stur            x5, [fp, #-0x28]
    //     0xea6138: stur            x6, [fp, #-0x30]
    // 0xea613c: CheckStackOverflow
    //     0xea613c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea6140: cmp             SP, x16
    //     0xea6144: b.ls            #0xea6528
    // 0xea6148: LoadField: r7 = r4->field_7
    //     0xea6148: ldur            w7, [x4, #7]
    // 0xea614c: DecompressPointer r7
    //     0xea614c: add             x7, x7, HEAP, lsl #32
    // 0xea6150: stur            x7, [fp, #-8]
    // 0xea6154: r0 = LoadClassIdInstr(r7)
    //     0xea6154: ldur            x0, [x7, #-1]
    //     0xea6158: ubfx            x0, x0, #0xc, #0x14
    // 0xea615c: mov             x1, x7
    // 0xea6160: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea6160: sub             lr, x0, #1, lsl #12
    //     0xea6164: ldr             lr, [x21, lr, lsl #3]
    //     0xea6168: blr             lr
    // 0xea616c: ldur            x2, [fp, #-0x20]
    // 0xea6170: add             x1, x2, x0
    // 0xea6174: ldur            x3, [fp, #-0x18]
    // 0xea6178: LoadField: r0 = r3->field_13
    //     0xea6178: ldur            w0, [x3, #0x13]
    // 0xea617c: r4 = LoadInt32Instr(r0)
    //     0xea617c: sbfx            x4, x0, #1, #0x1f
    // 0xea6180: stur            x4, [fp, #-0x40]
    // 0xea6184: cmp             x1, x4
    // 0xea6188: b.gt            #0xea64f4
    // 0xea618c: ldur            x5, [fp, #-0x10]
    // 0xea6190: r7 = 0
    //     0xea6190: movz            x7, #0
    // 0xea6194: ldur            x6, [fp, #-8]
    // 0xea6198: stur            x7, [fp, #-0x38]
    // 0xea619c: CheckStackOverflow
    //     0xea619c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea61a0: cmp             SP, x16
    //     0xea61a4: b.ls            #0xea6530
    // 0xea61a8: r0 = LoadClassIdInstr(r6)
    //     0xea61a8: ldur            x0, [x6, #-1]
    //     0xea61ac: ubfx            x0, x0, #0xc, #0x14
    // 0xea61b0: mov             x1, x6
    // 0xea61b4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea61b4: sub             lr, x0, #1, lsl #12
    //     0xea61b8: ldr             lr, [x21, lr, lsl #3]
    //     0xea61bc: blr             lr
    // 0xea61c0: ldur            x2, [fp, #-0x38]
    // 0xea61c4: cmp             x2, x0
    // 0xea61c8: b.ge            #0xea6248
    // 0xea61cc: ldur            x8, [fp, #-0x10]
    // 0xea61d0: ldur            x4, [fp, #-0x18]
    // 0xea61d4: ldur            x3, [fp, #-0x20]
    // 0xea61d8: LoadField: r5 = r8->field_f
    //     0xea61d8: ldur            w5, [x8, #0xf]
    // 0xea61dc: DecompressPointer r5
    //     0xea61dc: add             x5, x5, HEAP, lsl #32
    // 0xea61e0: cmp             w5, NULL
    // 0xea61e4: b.eq            #0xea6538
    // 0xea61e8: LoadField: r0 = r5->field_13
    //     0xea61e8: ldur            w0, [x5, #0x13]
    // 0xea61ec: r1 = LoadInt32Instr(r0)
    //     0xea61ec: sbfx            x1, x0, #1, #0x1f
    // 0xea61f0: mov             x0, x1
    // 0xea61f4: mov             x1, x2
    // 0xea61f8: cmp             x1, x0
    // 0xea61fc: b.hs            #0xea653c
    // 0xea6200: ArrayLoad: r6 = r5[r2]  ; List_1
    //     0xea6200: add             x16, x5, x2
    //     0xea6204: ldrb            w6, [x16, #0x17]
    // 0xea6208: add             x7, x3, x2
    // 0xea620c: ldur            x0, [fp, #-0x40]
    // 0xea6210: mov             x1, x7
    // 0xea6214: cmp             x1, x0
    // 0xea6218: b.hs            #0xea6540
    // 0xea621c: ArrayLoad: r0 = r4[r7]  ; List_1
    //     0xea621c: add             x16, x4, x7
    //     0xea6220: ldrb            w0, [x16, #0x17]
    // 0xea6224: eor             x1, x6, x0
    // 0xea6228: ArrayStore: r5[r2] = r1  ; TypeUnknown_1
    //     0xea6228: add             x0, x5, x2
    //     0xea622c: strb            w1, [x0, #0x17]
    // 0xea6230: add             x7, x2, #1
    // 0xea6234: mov             x5, x8
    // 0xea6238: mov             x2, x3
    // 0xea623c: mov             x3, x4
    // 0xea6240: ldur            x4, [fp, #-0x40]
    // 0xea6244: b               #0xea6194
    // 0xea6248: ldur            x8, [fp, #-0x10]
    // 0xea624c: ldur            x9, [fp, #-0x28]
    // 0xea6250: ldur            x7, [fp, #-0x30]
    // 0xea6254: ldur            x4, [fp, #-8]
    // 0xea6258: LoadField: r2 = r8->field_f
    //     0xea6258: ldur            w2, [x8, #0xf]
    // 0xea625c: DecompressPointer r2
    //     0xea625c: add             x2, x2, HEAP, lsl #32
    // 0xea6260: cmp             w2, NULL
    // 0xea6264: b.eq            #0xea6544
    // 0xea6268: r0 = LoadClassIdInstr(r4)
    //     0xea6268: ldur            x0, [x4, #-1]
    //     0xea626c: ubfx            x0, x0, #0xc, #0x14
    // 0xea6270: mov             x1, x4
    // 0xea6274: mov             x5, x9
    // 0xea6278: mov             x6, x7
    // 0xea627c: r3 = 0
    //     0xea627c: movz            x3, #0
    // 0xea6280: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea6280: sub             lr, x0, #0xf69
    //     0xea6284: ldr             lr, [x21, lr, lsl #3]
    //     0xea6288: blr             lr
    // 0xea628c: mov             x2, x0
    // 0xea6290: ldur            x0, [fp, #-0x10]
    // 0xea6294: stur            x2, [fp, #-0x20]
    // 0xea6298: LoadField: r3 = r0->field_f
    //     0xea6298: ldur            w3, [x0, #0xf]
    // 0xea629c: DecompressPointer r3
    //     0xea629c: add             x3, x3, HEAP, lsl #32
    // 0xea62a0: stur            x3, [fp, #-0x18]
    // 0xea62a4: cmp             w3, NULL
    // 0xea62a8: b.eq            #0xea6548
    // 0xea62ac: ldur            x4, [fp, #-8]
    // 0xea62b0: r0 = LoadClassIdInstr(r4)
    //     0xea62b0: ldur            x0, [x4, #-1]
    //     0xea62b4: ubfx            x0, x0, #0xc, #0x14
    // 0xea62b8: mov             x1, x4
    // 0xea62bc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea62bc: sub             lr, x0, #1, lsl #12
    //     0xea62c0: ldr             lr, [x21, lr, lsl #3]
    //     0xea62c4: blr             lr
    // 0xea62c8: stur            x0, [fp, #-0x38]
    // 0xea62cc: r0 = _ByteBuffer()
    //     0xea62cc: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0xea62d0: mov             x2, x0
    // 0xea62d4: ldur            x0, [fp, #-0x28]
    // 0xea62d8: stur            x2, [fp, #-0x10]
    // 0xea62dc: StoreField: r2->field_7 = r0
    //     0xea62dc: stur            w0, [x2, #7]
    // 0xea62e0: ldur            x1, [fp, #-8]
    // 0xea62e4: r0 = LoadClassIdInstr(r1)
    //     0xea62e4: ldur            x0, [x1, #-1]
    //     0xea62e8: ubfx            x0, x0, #0xc, #0x14
    // 0xea62ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea62ec: sub             lr, x0, #1, lsl #12
    //     0xea62f0: ldr             lr, [x21, lr, lsl #3]
    //     0xea62f4: blr             lr
    // 0xea62f8: mov             x3, x0
    // 0xea62fc: ldur            x2, [fp, #-0x30]
    // 0xea6300: r0 = BoxInt64Instr(r2)
    //     0xea6300: sbfiz           x0, x2, #1, #0x1f
    //     0xea6304: cmp             x2, x0, asr #1
    //     0xea6308: b.eq            #0xea6314
    //     0xea630c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea6310: stur            x2, [x0, #7]
    // 0xea6314: mov             x2, x0
    // 0xea6318: r0 = BoxInt64Instr(r3)
    //     0xea6318: sbfiz           x0, x3, #1, #0x1f
    //     0xea631c: cmp             x3, x0, asr #1
    //     0xea6320: b.eq            #0xea632c
    //     0xea6324: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea6328: stur            x3, [x0, #7]
    // 0xea632c: stp             x0, x2, [SP]
    // 0xea6330: ldur            x1, [fp, #-0x10]
    // 0xea6334: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0xea6334: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0xea6338: r0 = asUint8List()
    //     0xea6338: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0xea633c: mov             x5, x0
    // 0xea6340: ldur            x4, [fp, #-0x38]
    // 0xea6344: stur            x5, [fp, #-8]
    // 0xea6348: tbz             x4, #0x3f, #0xea6354
    // 0xea634c: ldur            x6, [fp, #-0x18]
    // 0xea6350: b               #0xea6368
    // 0xea6354: ldur            x6, [fp, #-0x18]
    // 0xea6358: LoadField: r0 = r6->field_13
    //     0xea6358: ldur            w0, [x6, #0x13]
    // 0xea635c: r1 = LoadInt32Instr(r0)
    //     0xea635c: sbfx            x1, x0, #1, #0x1f
    // 0xea6360: cmp             x4, x1
    // 0xea6364: b.le            #0xea6394
    // 0xea6368: LoadField: r2 = r6->field_13
    //     0xea6368: ldur            w2, [x6, #0x13]
    // 0xea636c: r0 = BoxInt64Instr(r4)
    //     0xea636c: sbfiz           x0, x4, #1, #0x1f
    //     0xea6370: cmp             x4, x0, asr #1
    //     0xea6374: b.eq            #0xea6380
    //     0xea6378: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea637c: stur            x4, [x0, #7]
    // 0xea6380: r3 = LoadInt32Instr(r2)
    //     0xea6380: sbfx            x3, x2, #1, #0x1f
    // 0xea6384: mov             x2, x0
    // 0xea6388: r1 = 0
    //     0xea6388: movz            x1, #0
    // 0xea638c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea638c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea6390: r0 = checkValidRange()
    //     0xea6390: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea6394: ldur            x2, [fp, #-0x38]
    // 0xea6398: ldur            x3, [fp, #-8]
    // 0xea639c: LoadField: r0 = r3->field_13
    //     0xea639c: ldur            w0, [x3, #0x13]
    // 0xea63a0: r1 = LoadInt32Instr(r0)
    //     0xea63a0: sbfx            x1, x0, #1, #0x1f
    // 0xea63a4: cmp             x1, x2
    // 0xea63a8: b.lt            #0xea651c
    // 0xea63ac: cbz             x2, #0xea64e4
    // 0xea63b0: r0 = BoxInt64Instr(r2)
    //     0xea63b0: sbfiz           x0, x2, #1, #0x1f
    //     0xea63b4: cmp             x2, x0, asr #1
    //     0xea63b8: b.eq            #0xea63c4
    //     0xea63bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea63c0: stur            x2, [x0, #7]
    // 0xea63c4: cmp             w0, #0x800
    // 0xea63c8: b.ge            #0xea649c
    // 0xea63cc: ldur            x1, [fp, #-0x18]
    // 0xea63d0: LoadField: r2 = r3->field_7
    //     0xea63d0: ldur            x2, [x3, #7]
    // 0xea63d4: mov             x4, x0
    // 0xea63d8: mov             x3, x2
    // 0xea63dc: add             x0, x1, #0x17
    // 0xea63e0: cbz             x4, #0xea6498
    // 0xea63e4: cmp             x0, x3
    // 0xea63e8: b.ls            #0xea6450
    // 0xea63ec: sxtw            x4, w4
    // 0xea63f0: add             x16, x3, x4, asr #1
    // 0xea63f4: cmp             x0, x16
    // 0xea63f8: b.hs            #0xea6450
    // 0xea63fc: mov             x3, x16
    // 0xea6400: add             x0, x0, x4, asr #1
    // 0xea6404: tbz             w4, #4, #0xea6410
    // 0xea6408: ldr             x16, [x3, #-8]!
    // 0xea640c: str             x16, [x0, #-8]!
    // 0xea6410: tbz             w4, #3, #0xea641c
    // 0xea6414: ldr             w16, [x3, #-4]!
    // 0xea6418: str             w16, [x0, #-4]!
    // 0xea641c: tbz             w4, #2, #0xea6428
    // 0xea6420: ldrh            w16, [x3, #-2]!
    // 0xea6424: strh            w16, [x0, #-2]!
    // 0xea6428: tbz             w4, #1, #0xea6434
    // 0xea642c: ldrb            w16, [x3, #-1]!
    // 0xea6430: strb            w16, [x0, #-1]!
    // 0xea6434: ands            w4, w4, #0xffffffe1
    // 0xea6438: b.eq            #0xea6498
    // 0xea643c: ldp             x16, x17, [x3, #-0x10]!
    // 0xea6440: stp             x16, x17, [x0, #-0x10]!
    // 0xea6444: subs            w4, w4, #0x20
    // 0xea6448: b.ne            #0xea643c
    // 0xea644c: b               #0xea6498
    // 0xea6450: tbz             w4, #4, #0xea645c
    // 0xea6454: ldr             x16, [x3], #8
    // 0xea6458: str             x16, [x0], #8
    // 0xea645c: tbz             w4, #3, #0xea6468
    // 0xea6460: ldr             w16, [x3], #4
    // 0xea6464: str             w16, [x0], #4
    // 0xea6468: tbz             w4, #2, #0xea6474
    // 0xea646c: ldrh            w16, [x3], #2
    // 0xea6470: strh            w16, [x0], #2
    // 0xea6474: tbz             w4, #1, #0xea6480
    // 0xea6478: ldrb            w16, [x3], #1
    // 0xea647c: strb            w16, [x0], #1
    // 0xea6480: ands            w4, w4, #0xffffffe1
    // 0xea6484: b.eq            #0xea6498
    // 0xea6488: ldp             x16, x17, [x3], #0x10
    // 0xea648c: stp             x16, x17, [x0], #0x10
    // 0xea6490: subs            w4, w4, #0x20
    // 0xea6494: b.ne            #0xea6488
    // 0xea6498: b               #0xea64e4
    // 0xea649c: ldur            x1, [fp, #-0x18]
    // 0xea64a0: LoadField: r0 = r1->field_7
    //     0xea64a0: ldur            x0, [x1, #7]
    // 0xea64a4: LoadField: r1 = r3->field_7
    //     0xea64a4: ldur            x1, [x3, #7]
    // 0xea64a8: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea64a8: mov             x3, THR
    //     0xea64ac: ldr             x9, [x3, #0x658]
    //     0xea64b0: mov             x17, fp
    //     0xea64b4: str             fp, [SP, #-8]!
    //     0xea64b8: mov             fp, SP
    //     0xea64bc: and             SP, SP, #0xfffffffffffffff0
    //     0xea64c0: mov             x19, sp
    //     0xea64c4: mov             sp, SP
    //     0xea64c8: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea64cc: blr             x9
    //     0xea64d0: movz            x16, #0x8
    //     0xea64d4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea64d8: mov             sp, x19
    //     0xea64dc: mov             SP, fp
    //     0xea64e0: ldr             fp, [SP], #8
    // 0xea64e4: ldur            x0, [fp, #-0x20]
    // 0xea64e8: LeaveFrame
    //     0xea64e8: mov             SP, fp
    //     0xea64ec: ldp             fp, lr, [SP], #0x10
    // 0xea64f0: ret
    //     0xea64f0: ret             
    // 0xea64f4: r0 = ArgumentError()
    //     0xea64f4: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea64f8: mov             x1, x0
    // 0xea64fc: r0 = "Input buffer too short"
    //     0xea64fc: add             x0, PP, #0x23, lsl #12  ; [pp+0x23678] "Input buffer too short"
    //     0xea6500: ldr             x0, [x0, #0x678]
    // 0xea6504: ArrayStore: r1[0] = r0  ; List_4
    //     0xea6504: stur            w0, [x1, #0x17]
    // 0xea6508: r0 = false
    //     0xea6508: add             x0, NULL, #0x30  ; false
    // 0xea650c: StoreField: r1->field_b = r0
    //     0xea650c: stur            w0, [x1, #0xb]
    // 0xea6510: mov             x0, x1
    // 0xea6514: r0 = Throw()
    //     0xea6514: bl              #0xec04b8  ; ThrowStub
    // 0xea6518: brk             #0
    // 0xea651c: r0 = tooFew()
    //     0xea651c: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea6520: r0 = Throw()
    //     0xea6520: bl              #0xec04b8  ; ThrowStub
    // 0xea6524: brk             #0
    // 0xea6528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea6528: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea652c: b               #0xea6148
    // 0xea6530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea6530: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea6534: b               #0xea61a8
    // 0xea6538: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6538: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea653c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea653c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea6540: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea6540: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea6544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6544: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea6548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6548: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
