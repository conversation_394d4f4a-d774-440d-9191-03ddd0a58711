// lib: impl.block_cipher.modes.ccm, url: package:pointycastle/block/modes/ccm.dart

// class id: 1050931, size: 0x8
class :: {

  static _ WriteLen.write(/* No info */) {
    // ** addr: 0xeae9a8, size: 0x78
    // 0xeae9a8: EnterFrame
    //     0xeae9a8: stp             fp, lr, [SP, #-0x10]!
    //     0xeae9ac: mov             fp, SP
    // 0xeae9b0: AllocStack(0x10)
    //     0xeae9b0: sub             SP, SP, #0x10
    // 0xeae9b4: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3 */, dynamic _ /* r3 => r2 */)
    //     0xeae9b4: mov             x4, x1
    //     0xeae9b8: mov             x16, x3
    //     0xeae9bc: mov             x3, x2
    //     0xeae9c0: mov             x2, x16
    //     0xeae9c4: stur            x1, [fp, #-8]
    // 0xeae9c8: CheckStackOverflow
    //     0xeae9c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeae9cc: cmp             SP, x16
    //     0xeae9d0: b.ls            #0xeaea18
    // 0xeae9d4: add             x6, x2, x5
    // 0xeae9d8: r0 = BoxInt64Instr(r6)
    //     0xeae9d8: sbfiz           x0, x6, #1, #0x1f
    //     0xeae9dc: cmp             x6, x0, asr #1
    //     0xeae9e0: b.eq            #0xeae9ec
    //     0xeae9e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeae9e8: stur            x6, [x0, #7]
    // 0xeae9ec: str             x0, [SP]
    // 0xeae9f0: mov             x1, x3
    // 0xeae9f4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeae9f4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeae9f8: r0 = sublist()
    //     0xeae9f8: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xeae9fc: ldur            x1, [fp, #-8]
    // 0xeaea00: mov             x2, x0
    // 0xeaea04: r0 = add()
    //     0xeaea04: bl              #0x7bc0b8  ; [dart:_internal] _CopyingBytesBuilder::add
    // 0xeaea08: r0 = Null
    //     0xeaea08: mov             x0, NULL
    // 0xeaea0c: LeaveFrame
    //     0xeaea0c: mov             SP, fp
    //     0xeaea10: ldp             fp, lr, [SP], #0x10
    // 0xeaea14: ret
    //     0xeaea14: ret             
    // 0xeaea18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaea18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaea1c: b               #0xeae9d4
  }
}

// class id: 671, size: 0x3c, field offset: 0x30
class CCMBlockCipher extends BaseAEADBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xdb8

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e5138, size: 0x64
    // 0x8e5138: EnterFrame
    //     0x8e5138: stp             fp, lr, [SP, #-0x10]!
    //     0x8e513c: mov             fp, SP
    // 0x8e5140: AllocStack(0x8)
    //     0x8e5140: sub             SP, SP, #8
    // 0x8e5144: CheckStackOverflow
    //     0x8e5144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5148: cmp             SP, x16
    //     0x8e514c: b.ls            #0x8e5194
    // 0x8e5150: r0 = DynamicFactoryConfig()
    //     0x8e5150: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e5154: r1 = Function '<anonymous closure>': static.
    //     0x8e5154: add             x1, PP, #0x19, lsl #12  ; [pp+0x19a98] AnonymousClosure: static (0x8e519c), in [package:pointycastle/block/modes/ccm.dart] CCMBlockCipher::factoryConfig (0x8e5138)
    //     0x8e5158: ldr             x1, [x1, #0xa98]
    // 0x8e515c: r2 = Null
    //     0x8e515c: mov             x2, NULL
    // 0x8e5160: stur            x0, [fp, #-8]
    // 0x8e5164: r0 = AllocateClosure()
    //     0x8e5164: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e5168: ldur            x1, [fp, #-8]
    // 0x8e516c: mov             x5, x0
    // 0x8e5170: r2 = BlockCipher
    //     0x8e5170: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e5174: ldr             x2, [x2, #0xa80]
    // 0x8e5178: r3 = "/CCM"
    //     0x8e5178: add             x3, PP, #0x19, lsl #12  ; [pp+0x19aa0] "/CCM"
    //     0x8e517c: ldr             x3, [x3, #0xaa0]
    // 0x8e5180: r0 = DynamicFactoryConfig.suffix()
    //     0x8e5180: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e5184: ldur            x0, [fp, #-8]
    // 0x8e5188: LeaveFrame
    //     0x8e5188: mov             SP, fp
    //     0x8e518c: ldp             fp, lr, [SP], #0x10
    // 0x8e5190: ret
    //     0x8e5190: ret             
    // 0x8e5194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e5194: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5198: b               #0x8e5150
  }
  [closure] static (dynamic) => CCMBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e519c, size: 0x54
    // 0x8e519c: EnterFrame
    //     0x8e519c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e51a0: mov             fp, SP
    // 0x8e51a4: AllocStack(0x8)
    //     0x8e51a4: sub             SP, SP, #8
    // 0x8e51a8: SetupParameters()
    //     0x8e51a8: ldr             x0, [fp, #0x20]
    //     0x8e51ac: ldur            w1, [x0, #0x17]
    //     0x8e51b0: add             x1, x1, HEAP, lsl #32
    //     0x8e51b4: stur            x1, [fp, #-8]
    // 0x8e51b8: r1 = 1
    //     0x8e51b8: movz            x1, #0x1
    // 0x8e51bc: r0 = AllocateContext()
    //     0x8e51bc: bl              #0xec126c  ; AllocateContextStub
    // 0x8e51c0: mov             x1, x0
    // 0x8e51c4: ldur            x0, [fp, #-8]
    // 0x8e51c8: StoreField: r1->field_b = r0
    //     0x8e51c8: stur            w0, [x1, #0xb]
    // 0x8e51cc: ldr             x0, [fp, #0x10]
    // 0x8e51d0: StoreField: r1->field_f = r0
    //     0x8e51d0: stur            w0, [x1, #0xf]
    // 0x8e51d4: mov             x2, x1
    // 0x8e51d8: r1 = Function '<anonymous closure>': static.
    //     0x8e51d8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19aa8] AnonymousClosure: static (0x8e51f0), in [package:pointycastle/block/modes/ccm.dart] CCMBlockCipher::factoryConfig (0x8e5138)
    //     0x8e51dc: ldr             x1, [x1, #0xaa8]
    // 0x8e51e0: r0 = AllocateClosure()
    //     0x8e51e0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e51e4: LeaveFrame
    //     0x8e51e4: mov             SP, fp
    //     0x8e51e8: ldp             fp, lr, [SP], #0x10
    // 0x8e51ec: ret
    //     0x8e51ec: ret             
  }
  [closure] static CCMBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e51f0, size: 0xcc
    // 0x8e51f0: EnterFrame
    //     0x8e51f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e51f4: mov             fp, SP
    // 0x8e51f8: AllocStack(0x20)
    //     0x8e51f8: sub             SP, SP, #0x20
    // 0x8e51fc: SetupParameters()
    //     0x8e51fc: ldr             x0, [fp, #0x10]
    //     0x8e5200: ldur            w1, [x0, #0x17]
    //     0x8e5204: add             x1, x1, HEAP, lsl #32
    // 0x8e5208: CheckStackOverflow
    //     0x8e5208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e520c: cmp             SP, x16
    //     0x8e5210: b.ls            #0x8e52b0
    // 0x8e5214: LoadField: r0 = r1->field_f
    //     0x8e5214: ldur            w0, [x1, #0xf]
    // 0x8e5218: DecompressPointer r0
    //     0x8e5218: add             x0, x0, HEAP, lsl #32
    // 0x8e521c: r1 = LoadClassIdInstr(r0)
    //     0x8e521c: ldur            x1, [x0, #-1]
    //     0x8e5220: ubfx            x1, x1, #0xc, #0x14
    // 0x8e5224: mov             x16, x0
    // 0x8e5228: mov             x0, x1
    // 0x8e522c: mov             x1, x16
    // 0x8e5230: r2 = 1
    //     0x8e5230: movz            x2, #0x1
    // 0x8e5234: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e5234: sub             lr, x0, #0xfdd
    //     0x8e5238: ldr             lr, [x21, lr, lsl #3]
    //     0x8e523c: blr             lr
    // 0x8e5240: stur            x0, [fp, #-8]
    // 0x8e5244: cmp             w0, NULL
    // 0x8e5248: b.eq            #0x8e52b8
    // 0x8e524c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e524c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e5250: ldr             x0, [x0, #0x2e38]
    //     0x8e5254: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e5258: cmp             w0, w16
    //     0x8e525c: b.ne            #0x8e526c
    //     0x8e5260: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e5264: ldr             x2, [x2, #0xf80]
    //     0x8e5268: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e526c: r16 = <BlockCipher>
    //     0x8e526c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e5270: ldr             x16, [x16, #0x88]
    // 0x8e5274: stp             x0, x16, [SP, #8]
    // 0x8e5278: ldur            x16, [fp, #-8]
    // 0x8e527c: str             x16, [SP]
    // 0x8e5280: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e5280: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e5284: r0 = create()
    //     0x8e5284: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e5288: stur            x0, [fp, #-8]
    // 0x8e528c: r0 = CCMBlockCipher()
    //     0x8e528c: bl              #0x8e5414  ; AllocateCCMBlockCipherStub -> CCMBlockCipher (size=0x3c)
    // 0x8e5290: mov             x1, x0
    // 0x8e5294: ldur            x2, [fp, #-8]
    // 0x8e5298: stur            x0, [fp, #-8]
    // 0x8e529c: r0 = CCMBlockCipher()
    //     0x8e529c: bl              #0x8e52bc  ; [package:pointycastle/block/modes/ccm.dart] CCMBlockCipher::CCMBlockCipher
    // 0x8e52a0: ldur            x0, [fp, #-8]
    // 0x8e52a4: LeaveFrame
    //     0x8e52a4: mov             SP, fp
    //     0x8e52a8: ldp             fp, lr, [SP], #0x10
    // 0x8e52ac: ret
    //     0x8e52ac: ret             
    // 0x8e52b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e52b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e52b4: b               #0x8e5214
    // 0x8e52b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e52b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ CCMBlockCipher(/* No info */) {
    // ** addr: 0x8e52bc, size: 0x158
    // 0x8e52bc: EnterFrame
    //     0x8e52bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8e52c0: mov             fp, SP
    // 0x8e52c4: AllocStack(0x10)
    //     0x8e52c4: sub             SP, SP, #0x10
    // 0x8e52c8: r0 = Sentinel
    //     0x8e52c8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e52cc: mov             x3, x1
    // 0x8e52d0: stur            x1, [fp, #-8]
    // 0x8e52d4: stur            x2, [fp, #-0x10]
    // 0x8e52d8: CheckStackOverflow
    //     0x8e52d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e52dc: cmp             SP, x16
    //     0x8e52e0: b.ls            #0x8e540c
    // 0x8e52e4: StoreField: r3->field_2f = r0
    //     0x8e52e4: stur            w0, [x3, #0x2f]
    // 0x8e52e8: r1 = Null
    //     0x8e52e8: mov             x1, NULL
    // 0x8e52ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8e52ec: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8e52f0: r0 = BytesBuilder()
    //     0x8e52f0: bl              #0x706a5c  ; [dart:_internal] BytesBuilder::BytesBuilder
    // 0x8e52f4: ldur            x2, [fp, #-8]
    // 0x8e52f8: StoreField: r2->field_33 = r0
    //     0x8e52f8: stur            w0, [x2, #0x33]
    //     0x8e52fc: ldurb           w16, [x2, #-1]
    //     0x8e5300: ldurb           w17, [x0, #-1]
    //     0x8e5304: and             x16, x17, x16, lsr #2
    //     0x8e5308: tst             x16, HEAP, lsr #32
    //     0x8e530c: b.eq            #0x8e5314
    //     0x8e5310: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e5314: r1 = Null
    //     0x8e5314: mov             x1, NULL
    // 0x8e5318: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8e5318: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8e531c: r0 = BytesBuilder()
    //     0x8e531c: bl              #0x706a5c  ; [dart:_internal] BytesBuilder::BytesBuilder
    // 0x8e5320: ldur            x1, [fp, #-8]
    // 0x8e5324: StoreField: r1->field_37 = r0
    //     0x8e5324: stur            w0, [x1, #0x37]
    //     0x8e5328: ldurb           w16, [x1, #-1]
    //     0x8e532c: ldurb           w17, [x0, #-1]
    //     0x8e5330: and             x16, x17, x16, lsr #2
    //     0x8e5334: tst             x16, HEAP, lsr #32
    //     0x8e5338: b.eq            #0x8e5340
    //     0x8e533c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e5340: r0 = Sentinel
    //     0x8e5340: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e5344: StoreField: r1->field_b = r0
    //     0x8e5344: stur            w0, [x1, #0xb]
    // 0x8e5348: StoreField: r1->field_f = r0
    //     0x8e5348: stur            w0, [x1, #0xf]
    // 0x8e534c: ArrayStore: r1[0] = r0  ; List_4
    //     0x8e534c: stur            w0, [x1, #0x17]
    // 0x8e5350: StoreField: r1->field_1b = r0
    //     0x8e5350: stur            w0, [x1, #0x1b]
    // 0x8e5354: StoreField: r1->field_2b = r0
    //     0x8e5354: stur            w0, [x1, #0x2b]
    // 0x8e5358: ldur            x0, [fp, #-0x10]
    // 0x8e535c: StoreField: r1->field_7 = r0
    //     0x8e535c: stur            w0, [x1, #7]
    //     0x8e5360: ldurb           w16, [x1, #-1]
    //     0x8e5364: ldurb           w17, [x0, #-1]
    //     0x8e5368: and             x16, x17, x16, lsr #2
    //     0x8e536c: tst             x16, HEAP, lsr #32
    //     0x8e5370: b.eq            #0x8e5378
    //     0x8e5374: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e5378: ldur            x2, [fp, #-0x10]
    // 0x8e537c: r0 = LoadClassIdInstr(r2)
    //     0x8e537c: ldur            x0, [x2, #-1]
    //     0x8e5380: ubfx            x0, x0, #0xc, #0x14
    // 0x8e5384: mov             x1, x2
    // 0x8e5388: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5388: sub             lr, x0, #1, lsl #12
    //     0x8e538c: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5390: blr             lr
    // 0x8e5394: mov             x2, x0
    // 0x8e5398: r0 = BoxInt64Instr(r2)
    //     0x8e5398: sbfiz           x0, x2, #1, #0x1f
    //     0x8e539c: cmp             x2, x0, asr #1
    //     0x8e53a0: b.eq            #0x8e53ac
    //     0x8e53a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e53a8: stur            x2, [x0, #7]
    // 0x8e53ac: mov             x4, x0
    // 0x8e53b0: r0 = AllocateUint8Array()
    //     0x8e53b0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e53b4: ldur            x1, [fp, #-0x10]
    // 0x8e53b8: r0 = LoadClassIdInstr(r1)
    //     0x8e53b8: ldur            x0, [x1, #-1]
    //     0x8e53bc: ubfx            x0, x0, #0xc, #0x14
    // 0x8e53c0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e53c0: sub             lr, x0, #1, lsl #12
    //     0x8e53c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8e53c8: blr             lr
    // 0x8e53cc: cmp             x0, #0x10
    // 0x8e53d0: b.ne            #0x8e53e4
    // 0x8e53d4: r0 = Null
    //     0x8e53d4: mov             x0, NULL
    // 0x8e53d8: LeaveFrame
    //     0x8e53d8: mov             SP, fp
    //     0x8e53dc: ldp             fp, lr, [SP], #0x10
    // 0x8e53e0: ret
    //     0x8e53e0: ret             
    // 0x8e53e4: r0 = ArgumentError()
    //     0x8e53e4: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8e53e8: mov             x1, x0
    // 0x8e53ec: r0 = "CCM requires a block size of 16"
    //     0x8e53ec: add             x0, PP, #0x19, lsl #12  ; [pp+0x19ab0] "CCM requires a block size of 16"
    //     0x8e53f0: ldr             x0, [x0, #0xab0]
    // 0x8e53f4: ArrayStore: r1[0] = r0  ; List_4
    //     0x8e53f4: stur            w0, [x1, #0x17]
    // 0x8e53f8: r0 = false
    //     0x8e53f8: add             x0, NULL, #0x30  ; false
    // 0x8e53fc: StoreField: r1->field_b = r0
    //     0x8e53fc: stur            w0, [x1, #0xb]
    // 0x8e5400: mov             x0, x1
    // 0x8e5404: r0 = Throw()
    //     0x8e5404: bl              #0xec04b8  ; ThrowStub
    // 0x8e5408: brk             #0
    // 0x8e540c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e540c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5410: b               #0x8e52e4
  }
  _ reset(/* No info */) {
    // ** addr: 0xe81d80, size: 0x74
    // 0xe81d80: EnterFrame
    //     0xe81d80: stp             fp, lr, [SP, #-0x10]!
    //     0xe81d84: mov             fp, SP
    // 0xe81d88: AllocStack(0x8)
    //     0xe81d88: sub             SP, SP, #8
    // 0xe81d8c: SetupParameters(CCMBlockCipher this /* r1 => r2, fp-0x8 */)
    //     0xe81d8c: mov             x2, x1
    //     0xe81d90: stur            x1, [fp, #-8]
    // 0xe81d94: CheckStackOverflow
    //     0xe81d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81d98: cmp             SP, x16
    //     0xe81d9c: b.ls            #0xe81dec
    // 0xe81da0: LoadField: r1 = r2->field_7
    //     0xe81da0: ldur            w1, [x2, #7]
    // 0xe81da4: DecompressPointer r1
    //     0xe81da4: add             x1, x1, HEAP, lsl #32
    // 0xe81da8: r0 = LoadClassIdInstr(r1)
    //     0xe81da8: ldur            x0, [x1, #-1]
    //     0xe81dac: ubfx            x0, x0, #0xc, #0x14
    // 0xe81db0: r0 = GDT[cid_x0 + -0xeaf]()
    //     0xe81db0: sub             lr, x0, #0xeaf
    //     0xe81db4: ldr             lr, [x21, lr, lsl #3]
    //     0xe81db8: blr             lr
    // 0xe81dbc: ldur            x0, [fp, #-8]
    // 0xe81dc0: LoadField: r1 = r0->field_33
    //     0xe81dc0: ldur            w1, [x0, #0x33]
    // 0xe81dc4: DecompressPointer r1
    //     0xe81dc4: add             x1, x1, HEAP, lsl #32
    // 0xe81dc8: r0 = _clear()
    //     0xe81dc8: bl              #0x7c167c  ; [dart:_internal] _CopyingBytesBuilder::_clear
    // 0xe81dcc: ldur            x0, [fp, #-8]
    // 0xe81dd0: LoadField: r1 = r0->field_37
    //     0xe81dd0: ldur            w1, [x0, #0x37]
    // 0xe81dd4: DecompressPointer r1
    //     0xe81dd4: add             x1, x1, HEAP, lsl #32
    // 0xe81dd8: r0 = _clear()
    //     0xe81dd8: bl              #0x7c167c  ; [dart:_internal] _CopyingBytesBuilder::_clear
    // 0xe81ddc: r0 = Null
    //     0xe81ddc: mov             x0, NULL
    // 0xe81de0: LeaveFrame
    //     0xe81de0: mov             SP, fp
    //     0xe81de4: ldp             fp, lr, [SP], #0x10
    // 0xe81de8: ret
    //     0xe81de8: ret             
    // 0xe81dec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe81dec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe81df0: b               #0xe81da0
  }
  _ init(/* No info */) {
    // ** addr: 0xe8cdfc, size: 0x168
    // 0xe8cdfc: EnterFrame
    //     0xe8cdfc: stp             fp, lr, [SP, #-0x10]!
    //     0xe8ce00: mov             fp, SP
    // 0xe8ce04: AllocStack(0x18)
    //     0xe8ce04: sub             SP, SP, #0x18
    // 0xe8ce08: SetupParameters(CCMBlockCipher this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xe8ce08: mov             x5, x1
    //     0xe8ce0c: mov             x4, x2
    //     0xe8ce10: stur            x1, [fp, #-8]
    //     0xe8ce14: stur            x2, [fp, #-0x10]
    //     0xe8ce18: stur            x3, [fp, #-0x18]
    // 0xe8ce1c: CheckStackOverflow
    //     0xe8ce1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8ce20: cmp             SP, x16
    //     0xe8ce24: b.ls            #0xe8cf5c
    // 0xe8ce28: mov             x0, x3
    // 0xe8ce2c: r2 = Null
    //     0xe8ce2c: mov             x2, NULL
    // 0xe8ce30: r1 = Null
    //     0xe8ce30: mov             x1, NULL
    // 0xe8ce34: cmp             w0, NULL
    // 0xe8ce38: b.eq            #0xe8ce84
    // 0xe8ce3c: branchIfSmi(r0, 0xe8ce84)
    //     0xe8ce3c: tbz             w0, #0, #0xe8ce84
    // 0xe8ce40: r3 = SubtypeTestCache
    //     0xe8ce40: add             x3, PP, #0x21, lsl #12  ; [pp+0x21d28] SubtypeTestCache
    //     0xe8ce44: ldr             x3, [x3, #0xd28]
    // 0xe8ce48: r30 = Subtype2TestCacheStub
    //     0xe8ce48: ldr             lr, [PP, #0x30]  ; [pp+0x30] Stub: Subtype2TestCache (0x5f2e54)
    // 0xe8ce4c: LoadField: r30 = r30->field_7
    //     0xe8ce4c: ldur            lr, [lr, #7]
    // 0xe8ce50: blr             lr
    // 0xe8ce54: cmp             w7, NULL
    // 0xe8ce58: b.eq            #0xe8ce64
    // 0xe8ce5c: tbnz            w7, #4, #0xe8ce84
    // 0xe8ce60: b               #0xe8ce8c
    // 0xe8ce64: r8 = ParametersWithIV<KeyParameter>
    //     0xe8ce64: add             x8, PP, #0x21, lsl #12  ; [pp+0x21d30] Type: ParametersWithIV<KeyParameter>
    //     0xe8ce68: ldr             x8, [x8, #0xd30]
    // 0xe8ce6c: r3 = SubtypeTestCache
    //     0xe8ce6c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21d38] SubtypeTestCache
    //     0xe8ce70: ldr             x3, [x3, #0xd38]
    // 0xe8ce74: r30 = InstanceOfStub
    //     0xe8ce74: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe8ce78: LoadField: r30 = r30->field_7
    //     0xe8ce78: ldur            lr, [lr, #7]
    // 0xe8ce7c: blr             lr
    // 0xe8ce80: b               #0xe8ce90
    // 0xe8ce84: r0 = false
    //     0xe8ce84: add             x0, NULL, #0x30  ; false
    // 0xe8ce88: b               #0xe8ce90
    // 0xe8ce8c: r0 = true
    //     0xe8ce8c: add             x0, NULL, #0x20  ; true
    // 0xe8ce90: tbnz            w0, #4, #0xe8cf30
    // 0xe8ce94: ldur            x3, [fp, #-8]
    // 0xe8ce98: ldur            x0, [fp, #-0x18]
    // 0xe8ce9c: LoadField: r1 = r0->field_b
    //     0xe8ce9c: ldur            w1, [x0, #0xb]
    // 0xe8cea0: DecompressPointer r1
    //     0xe8cea0: add             x1, x1, HEAP, lsl #32
    // 0xe8cea4: mov             x0, x1
    // 0xe8cea8: StoreField: r3->field_2f = r0
    //     0xe8cea8: stur            w0, [x3, #0x2f]
    //     0xe8ceac: ldurb           w16, [x3, #-1]
    //     0xe8ceb0: ldurb           w17, [x0, #-1]
    //     0xe8ceb4: and             x16, x17, x16, lsr #2
    //     0xe8ceb8: tst             x16, HEAP, lsr #32
    //     0xe8cebc: b.eq            #0xe8cec4
    //     0xe8cec0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe8cec4: mov             x1, x3
    // 0xe8cec8: ldur            x2, [fp, #-0x10]
    // 0xe8cecc: r0 = _getMacSize()
    //     0xe8cecc: bl              #0xe8cf64  ; [package:pointycastle/block/modes/ccm.dart] CCMBlockCipher::_getMacSize
    // 0xe8ced0: ldur            x1, [fp, #-8]
    // 0xe8ced4: LoadField: r0 = r1->field_2f
    //     0xe8ced4: ldur            w0, [x1, #0x2f]
    // 0xe8ced8: DecompressPointer r0
    //     0xe8ced8: add             x0, x0, HEAP, lsl #32
    // 0xe8cedc: LoadField: r2 = r0->field_13
    //     0xe8cedc: ldur            w2, [x0, #0x13]
    // 0xe8cee0: r0 = LoadInt32Instr(r2)
    //     0xe8cee0: sbfx            x0, x2, #1, #0x1f
    // 0xe8cee4: cmp             x0, #7
    // 0xe8cee8: b.lt            #0xe8cf08
    // 0xe8ceec: cmp             x0, #0xd
    // 0xe8cef0: b.gt            #0xe8cf08
    // 0xe8cef4: r0 = reset()
    //     0xe8cef4: bl              #0xe81d80  ; [package:pointycastle/block/modes/ccm.dart] CCMBlockCipher::reset
    // 0xe8cef8: r0 = Null
    //     0xe8cef8: mov             x0, NULL
    // 0xe8cefc: LeaveFrame
    //     0xe8cefc: mov             SP, fp
    //     0xe8cf00: ldp             fp, lr, [SP], #0x10
    // 0xe8cf04: ret
    //     0xe8cf04: ret             
    // 0xe8cf08: r0 = ArgumentError()
    //     0xe8cf08: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe8cf0c: mov             x1, x0
    // 0xe8cf10: r0 = "nonce must have length from 7 to 13 octets"
    //     0xe8cf10: add             x0, PP, #0x21, lsl #12  ; [pp+0x21d40] "nonce must have length from 7 to 13 octets"
    //     0xe8cf14: ldr             x0, [x0, #0xd40]
    // 0xe8cf18: ArrayStore: r1[0] = r0  ; List_4
    //     0xe8cf18: stur            w0, [x1, #0x17]
    // 0xe8cf1c: r0 = false
    //     0xe8cf1c: add             x0, NULL, #0x30  ; false
    // 0xe8cf20: StoreField: r1->field_b = r0
    //     0xe8cf20: stur            w0, [x1, #0xb]
    // 0xe8cf24: mov             x0, x1
    // 0xe8cf28: r0 = Throw()
    //     0xe8cf28: bl              #0xec04b8  ; ThrowStub
    // 0xe8cf2c: brk             #0
    // 0xe8cf30: r0 = false
    //     0xe8cf30: add             x0, NULL, #0x30  ; false
    // 0xe8cf34: r0 = ArgumentError()
    //     0xe8cf34: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe8cf38: mov             x1, x0
    // 0xe8cf3c: r0 = "Invalid parameter class"
    //     0xe8cf3c: add             x0, PP, #0x21, lsl #12  ; [pp+0x21d48] "Invalid parameter class"
    //     0xe8cf40: ldr             x0, [x0, #0xd48]
    // 0xe8cf44: ArrayStore: r1[0] = r0  ; List_4
    //     0xe8cf44: stur            w0, [x1, #0x17]
    // 0xe8cf48: r0 = false
    //     0xe8cf48: add             x0, NULL, #0x30  ; false
    // 0xe8cf4c: StoreField: r1->field_b = r0
    //     0xe8cf4c: stur            w0, [x1, #0xb]
    // 0xe8cf50: mov             x0, x1
    // 0xe8cf54: r0 = Throw()
    //     0xe8cf54: bl              #0xec04b8  ; ThrowStub
    // 0xe8cf58: brk             #0
    // 0xe8cf5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8cf5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8cf60: b               #0xe8ce28
  }
  _ _getMacSize(/* No info */) {
    // ** addr: 0xe8cf64, size: 0x2c
    // 0xe8cf64: EnterFrame
    //     0xe8cf64: stp             fp, lr, [SP, #-0x10]!
    //     0xe8cf68: mov             fp, SP
    // 0xe8cf6c: CheckStackOverflow
    //     0xe8cf6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8cf70: cmp             SP, x16
    //     0xe8cf74: b.ls            #0xe8cf88
    // 0xe8cf78: r0 = blockSize()
    //     0xe8cf78: bl              #0xeb532c  ; [package:pointycastle/block/desede_engine.dart] DESedeEngine::blockSize
    // 0xe8cf7c: LeaveFrame
    //     0xe8cf7c: mov             SP, fp
    //     0xe8cf80: ldp             fp, lr, [SP], #0x10
    // 0xe8cf84: ret
    //     0xe8cf84: ret             
    // 0xe8cf88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8cf88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8cf8c: b               #0xe8cf78
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xeae964, size: 0x44
    // 0xeae964: EnterFrame
    //     0xeae964: stp             fp, lr, [SP, #-0x10]!
    //     0xeae968: mov             fp, SP
    // 0xeae96c: CheckStackOverflow
    //     0xeae96c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeae970: cmp             SP, x16
    //     0xeae974: b.ls            #0xeae9a0
    // 0xeae978: LoadField: r0 = r2->field_13
    //     0xeae978: ldur            w0, [x2, #0x13]
    // 0xeae97c: LoadField: r4 = r1->field_37
    //     0xeae97c: ldur            w4, [x1, #0x37]
    // 0xeae980: DecompressPointer r4
    //     0xeae980: add             x4, x4, HEAP, lsl #32
    // 0xeae984: r5 = LoadInt32Instr(r0)
    //     0xeae984: sbfx            x5, x0, #1, #0x1f
    // 0xeae988: mov             x1, x4
    // 0xeae98c: r0 = WriteLen.write()
    //     0xeae98c: bl              #0xeae9a8  ; [package:pointycastle/block/modes/ccm.dart] ::WriteLen.write
    // 0xeae990: r0 = 0
    //     0xeae990: movz            x0, #0
    // 0xeae994: LeaveFrame
    //     0xeae994: mov             SP, fp
    //     0xeae998: ldp             fp, lr, [SP], #0x10
    // 0xeae99c: ret
    //     0xeae99c: ret             
    // 0xeae9a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeae9a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeae9a4: b               #0xeae978
  }
}
