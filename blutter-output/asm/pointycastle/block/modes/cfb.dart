// lib: impl.block_cipher.modes.cfb, url: package:pointycastle/block/modes/cfb.dart

// class id: 1050932, size: 0x8
class :: {
}

// class id: 707, size: 0x24, field offset: 0x8
class CFBBlockCipher extends BaseBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xda0
  late Uint8List _iv; // offset: 0x14
  late bool _encrypting; // offset: 0x20

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e62f8, size: 0x98
    // 0x8e62f8: EnterFrame
    //     0x8e62f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e62fc: mov             fp, SP
    // 0x8e6300: AllocStack(0x40)
    //     0x8e6300: sub             SP, SP, #0x40
    // 0x8e6304: CheckStackOverflow
    //     0x8e6304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6308: cmp             SP, x16
    //     0x8e630c: b.ls            #0x8e6388
    // 0x8e6310: r16 = "^(.+)/CFB-([0-9]+)$"
    //     0x8e6310: add             x16, PP, #0x19, lsl #12  ; [pp+0x19b68] "^(.+)/CFB-([0-9]+)$"
    //     0x8e6314: ldr             x16, [x16, #0xb68]
    // 0x8e6318: stp             x16, NULL, [SP, #0x20]
    // 0x8e631c: r16 = false
    //     0x8e631c: add             x16, NULL, #0x30  ; false
    // 0x8e6320: r30 = true
    //     0x8e6320: add             lr, NULL, #0x20  ; true
    // 0x8e6324: stp             lr, x16, [SP, #0x10]
    // 0x8e6328: r16 = false
    //     0x8e6328: add             x16, NULL, #0x30  ; false
    // 0x8e632c: r30 = false
    //     0x8e632c: add             lr, NULL, #0x30  ; false
    // 0x8e6330: stp             lr, x16, [SP]
    // 0x8e6334: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8e6334: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8e6338: r0 = _RegExp()
    //     0x8e6338: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8e633c: stur            x0, [fp, #-8]
    // 0x8e6340: r0 = DynamicFactoryConfig()
    //     0x8e6340: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e6344: mov             x3, x0
    // 0x8e6348: ldur            x0, [fp, #-8]
    // 0x8e634c: stur            x3, [fp, #-0x10]
    // 0x8e6350: StoreField: r3->field_b = r0
    //     0x8e6350: stur            w0, [x3, #0xb]
    // 0x8e6354: r1 = Function '<anonymous closure>': static.
    //     0x8e6354: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b70] AnonymousClosure: static (0x8e6390), in [package:pointycastle/block/modes/cfb.dart] CFBBlockCipher::factoryConfig (0x8e62f8)
    //     0x8e6358: ldr             x1, [x1, #0xb70]
    // 0x8e635c: r2 = Null
    //     0x8e635c: mov             x2, NULL
    // 0x8e6360: r0 = AllocateClosure()
    //     0x8e6360: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6364: mov             x1, x0
    // 0x8e6368: ldur            x0, [fp, #-0x10]
    // 0x8e636c: StoreField: r0->field_f = r1
    //     0x8e636c: stur            w1, [x0, #0xf]
    // 0x8e6370: r1 = BlockCipher
    //     0x8e6370: add             x1, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e6374: ldr             x1, [x1, #0xa80]
    // 0x8e6378: StoreField: r0->field_7 = r1
    //     0x8e6378: stur            w1, [x0, #7]
    // 0x8e637c: LeaveFrame
    //     0x8e637c: mov             SP, fp
    //     0x8e6380: ldp             fp, lr, [SP], #0x10
    // 0x8e6384: ret
    //     0x8e6384: ret             
    // 0x8e6388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e638c: b               #0x8e6310
  }
  [closure] static (dynamic) => CFBBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e6390, size: 0x54
    // 0x8e6390: EnterFrame
    //     0x8e6390: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6394: mov             fp, SP
    // 0x8e6398: AllocStack(0x8)
    //     0x8e6398: sub             SP, SP, #8
    // 0x8e639c: SetupParameters()
    //     0x8e639c: ldr             x0, [fp, #0x20]
    //     0x8e63a0: ldur            w1, [x0, #0x17]
    //     0x8e63a4: add             x1, x1, HEAP, lsl #32
    //     0x8e63a8: stur            x1, [fp, #-8]
    // 0x8e63ac: r1 = 1
    //     0x8e63ac: movz            x1, #0x1
    // 0x8e63b0: r0 = AllocateContext()
    //     0x8e63b0: bl              #0xec126c  ; AllocateContextStub
    // 0x8e63b4: mov             x1, x0
    // 0x8e63b8: ldur            x0, [fp, #-8]
    // 0x8e63bc: StoreField: r1->field_b = r0
    //     0x8e63bc: stur            w0, [x1, #0xb]
    // 0x8e63c0: ldr             x0, [fp, #0x10]
    // 0x8e63c4: StoreField: r1->field_f = r0
    //     0x8e63c4: stur            w0, [x1, #0xf]
    // 0x8e63c8: mov             x2, x1
    // 0x8e63cc: r1 = Function '<anonymous closure>': static.
    //     0x8e63cc: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b78] AnonymousClosure: static (0x8e63e4), in [package:pointycastle/block/modes/cfb.dart] CFBBlockCipher::factoryConfig (0x8e62f8)
    //     0x8e63d0: ldr             x1, [x1, #0xb78]
    // 0x8e63d4: r0 = AllocateClosure()
    //     0x8e63d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e63d8: LeaveFrame
    //     0x8e63d8: mov             SP, fp
    //     0x8e63dc: ldp             fp, lr, [SP], #0x10
    // 0x8e63e0: ret
    //     0x8e63e0: ret             
  }
  [closure] static CFBBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e63e4, size: 0x1c0
    // 0x8e63e4: EnterFrame
    //     0x8e63e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e63e8: mov             fp, SP
    // 0x8e63ec: AllocStack(0x38)
    //     0x8e63ec: sub             SP, SP, #0x38
    // 0x8e63f0: SetupParameters()
    //     0x8e63f0: ldr             x0, [fp, #0x10]
    //     0x8e63f4: ldur            w1, [x0, #0x17]
    //     0x8e63f8: add             x1, x1, HEAP, lsl #32
    // 0x8e63fc: CheckStackOverflow
    //     0x8e63fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6400: cmp             SP, x16
    //     0x8e6404: b.ls            #0x8e6594
    // 0x8e6408: LoadField: r3 = r1->field_f
    //     0x8e6408: ldur            w3, [x1, #0xf]
    // 0x8e640c: DecompressPointer r3
    //     0x8e640c: add             x3, x3, HEAP, lsl #32
    // 0x8e6410: stur            x3, [fp, #-8]
    // 0x8e6414: r0 = LoadClassIdInstr(r3)
    //     0x8e6414: ldur            x0, [x3, #-1]
    //     0x8e6418: ubfx            x0, x0, #0xc, #0x14
    // 0x8e641c: mov             x1, x3
    // 0x8e6420: r2 = 1
    //     0x8e6420: movz            x2, #0x1
    // 0x8e6424: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e6424: sub             lr, x0, #0xfdd
    //     0x8e6428: ldr             lr, [x21, lr, lsl #3]
    //     0x8e642c: blr             lr
    // 0x8e6430: stur            x0, [fp, #-0x10]
    // 0x8e6434: cmp             w0, NULL
    // 0x8e6438: b.eq            #0x8e659c
    // 0x8e643c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e643c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e6440: ldr             x0, [x0, #0x2e38]
    //     0x8e6444: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6448: cmp             w0, w16
    //     0x8e644c: b.ne            #0x8e645c
    //     0x8e6450: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e6454: ldr             x2, [x2, #0xf80]
    //     0x8e6458: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e645c: r16 = <BlockCipher>
    //     0x8e645c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e6460: ldr             x16, [x16, #0x88]
    // 0x8e6464: stp             x0, x16, [SP, #8]
    // 0x8e6468: ldur            x16, [fp, #-0x10]
    // 0x8e646c: str             x16, [SP]
    // 0x8e6470: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e6470: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e6474: r0 = create()
    //     0x8e6474: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e6478: mov             x3, x0
    // 0x8e647c: ldur            x1, [fp, #-8]
    // 0x8e6480: stur            x3, [fp, #-0x10]
    // 0x8e6484: r0 = LoadClassIdInstr(r1)
    //     0x8e6484: ldur            x0, [x1, #-1]
    //     0x8e6488: ubfx            x0, x0, #0xc, #0x14
    // 0x8e648c: r2 = 2
    //     0x8e648c: movz            x2, #0x2
    // 0x8e6490: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e6490: sub             lr, x0, #0xfdd
    //     0x8e6494: ldr             lr, [x21, lr, lsl #3]
    //     0x8e6498: blr             lr
    // 0x8e649c: cmp             w0, NULL
    // 0x8e64a0: b.eq            #0x8e65a0
    // 0x8e64a4: mov             x1, x0
    // 0x8e64a8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8e64a8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8e64ac: r0 = parse()
    //     0x8e64ac: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8e64b0: stur            x0, [fp, #-0x20]
    // 0x8e64b4: tst             x0, #7
    // 0x8e64b8: b.ne            #0x8e64f0
    // 0x8e64bc: r1 = 8
    //     0x8e64bc: movz            x1, #0x8
    // 0x8e64c0: sdiv            x3, x0, x1
    // 0x8e64c4: stur            x3, [fp, #-0x18]
    // 0x8e64c8: r0 = CFBBlockCipher()
    //     0x8e64c8: bl              #0x8e671c  ; AllocateCFBBlockCipherStub -> CFBBlockCipher (size=0x24)
    // 0x8e64cc: mov             x1, x0
    // 0x8e64d0: ldur            x2, [fp, #-0x10]
    // 0x8e64d4: ldur            x3, [fp, #-0x18]
    // 0x8e64d8: stur            x0, [fp, #-8]
    // 0x8e64dc: r0 = CFBBlockCipher()
    //     0x8e64dc: bl              #0x8e65a4  ; [package:pointycastle/block/modes/cfb.dart] CFBBlockCipher::CFBBlockCipher
    // 0x8e64e0: ldur            x0, [fp, #-8]
    // 0x8e64e4: LeaveFrame
    //     0x8e64e4: mov             SP, fp
    //     0x8e64e8: ldp             fp, lr, [SP], #0x10
    // 0x8e64ec: ret
    //     0x8e64ec: ret             
    // 0x8e64f0: r1 = Null
    //     0x8e64f0: mov             x1, NULL
    // 0x8e64f4: r2 = 6
    //     0x8e64f4: movz            x2, #0x6
    // 0x8e64f8: r0 = AllocateArray()
    //     0x8e64f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e64fc: mov             x2, x0
    // 0x8e6500: r16 = "Bad CFB block size: "
    //     0x8e6500: add             x16, PP, #0x19, lsl #12  ; [pp+0x19b80] "Bad CFB block size: "
    //     0x8e6504: ldr             x16, [x16, #0xb80]
    // 0x8e6508: StoreField: r2->field_f = r16
    //     0x8e6508: stur            w16, [x2, #0xf]
    // 0x8e650c: ldur            x3, [fp, #-0x20]
    // 0x8e6510: r0 = BoxInt64Instr(r3)
    //     0x8e6510: sbfiz           x0, x3, #1, #0x1f
    //     0x8e6514: cmp             x3, x0, asr #1
    //     0x8e6518: b.eq            #0x8e6524
    //     0x8e651c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e6520: stur            x3, [x0, #7]
    // 0x8e6524: StoreField: r2->field_13 = r0
    //     0x8e6524: stur            w0, [x2, #0x13]
    // 0x8e6528: r16 = " (must be a multiple of 8)"
    //     0x8e6528: add             x16, PP, #0x19, lsl #12  ; [pp+0x19b08] " (must be a multiple of 8)"
    //     0x8e652c: ldr             x16, [x16, #0xb08]
    // 0x8e6530: ArrayStore: r2[0] = r16  ; List_4
    //     0x8e6530: stur            w16, [x2, #0x17]
    // 0x8e6534: str             x2, [SP]
    // 0x8e6538: r0 = _interpolate()
    //     0x8e6538: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8e653c: r1 = Null
    //     0x8e653c: mov             x1, NULL
    // 0x8e6540: r2 = 6
    //     0x8e6540: movz            x2, #0x6
    // 0x8e6544: stur            x0, [fp, #-8]
    // 0x8e6548: r0 = AllocateArray()
    //     0x8e6548: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e654c: r16 = "Algorithm name "
    //     0x8e654c: add             x16, PP, #0x19, lsl #12  ; [pp+0x19b10] "Algorithm name "
    //     0x8e6550: ldr             x16, [x16, #0xb10]
    // 0x8e6554: StoreField: r0->field_f = r16
    //     0x8e6554: stur            w16, [x0, #0xf]
    // 0x8e6558: ldur            x1, [fp, #-8]
    // 0x8e655c: StoreField: r0->field_13 = r1
    //     0x8e655c: stur            w1, [x0, #0x13]
    // 0x8e6560: r16 = " is invalid"
    //     0x8e6560: add             x16, PP, #0x19, lsl #12  ; [pp+0x19b18] " is invalid"
    //     0x8e6564: ldr             x16, [x16, #0xb18]
    // 0x8e6568: ArrayStore: r0[0] = r16  ; List_4
    //     0x8e6568: stur            w16, [x0, #0x17]
    // 0x8e656c: str             x0, [SP]
    // 0x8e6570: r0 = _interpolate()
    //     0x8e6570: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8e6574: stur            x0, [fp, #-8]
    // 0x8e6578: r0 = RegistryFactoryException()
    //     0x8e6578: bl              #0x8c37d8  ; AllocateRegistryFactoryExceptionStub -> RegistryFactoryException (size=0xc)
    // 0x8e657c: mov             x1, x0
    // 0x8e6580: ldur            x0, [fp, #-8]
    // 0x8e6584: StoreField: r1->field_7 = r0
    //     0x8e6584: stur            w0, [x1, #7]
    // 0x8e6588: mov             x0, x1
    // 0x8e658c: r0 = Throw()
    //     0x8e658c: bl              #0xec04b8  ; ThrowStub
    // 0x8e6590: brk             #0
    // 0x8e6594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6594: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6598: b               #0x8e6408
    // 0x8e659c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e659c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8e65a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e65a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ CFBBlockCipher(/* No info */) {
    // ** addr: 0x8e65a4, size: 0x178
    // 0x8e65a4: EnterFrame
    //     0x8e65a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e65a8: mov             fp, SP
    // 0x8e65ac: AllocStack(0x10)
    //     0x8e65ac: sub             SP, SP, #0x10
    // 0x8e65b0: r0 = Sentinel
    //     0x8e65b0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e65b4: mov             x4, x1
    // 0x8e65b8: stur            x1, [fp, #-8]
    // 0x8e65bc: stur            x2, [fp, #-0x10]
    // 0x8e65c0: CheckStackOverflow
    //     0x8e65c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e65c4: cmp             SP, x16
    //     0x8e65c8: b.ls            #0x8e6714
    // 0x8e65cc: StoreField: r4->field_13 = r0
    //     0x8e65cc: stur            w0, [x4, #0x13]
    // 0x8e65d0: StoreField: r4->field_1f = r0
    //     0x8e65d0: stur            w0, [x4, #0x1f]
    // 0x8e65d4: mov             x0, x2
    // 0x8e65d8: StoreField: r4->field_f = r0
    //     0x8e65d8: stur            w0, [x4, #0xf]
    //     0x8e65dc: ldurb           w16, [x4, #-1]
    //     0x8e65e0: ldurb           w17, [x0, #-1]
    //     0x8e65e4: and             x16, x17, x16, lsr #2
    //     0x8e65e8: tst             x16, HEAP, lsr #32
    //     0x8e65ec: b.eq            #0x8e65f4
    //     0x8e65f0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8e65f4: StoreField: r4->field_7 = r3
    //     0x8e65f4: stur            x3, [x4, #7]
    // 0x8e65f8: r0 = LoadClassIdInstr(r2)
    //     0x8e65f8: ldur            x0, [x2, #-1]
    //     0x8e65fc: ubfx            x0, x0, #0xc, #0x14
    // 0x8e6600: mov             x1, x2
    // 0x8e6604: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e6604: sub             lr, x0, #1, lsl #12
    //     0x8e6608: ldr             lr, [x21, lr, lsl #3]
    //     0x8e660c: blr             lr
    // 0x8e6610: mov             x2, x0
    // 0x8e6614: r0 = BoxInt64Instr(r2)
    //     0x8e6614: sbfiz           x0, x2, #1, #0x1f
    //     0x8e6618: cmp             x2, x0, asr #1
    //     0x8e661c: b.eq            #0x8e6628
    //     0x8e6620: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e6624: stur            x2, [x0, #7]
    // 0x8e6628: mov             x4, x0
    // 0x8e662c: r0 = AllocateUint8Array()
    //     0x8e662c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e6630: ldur            x2, [fp, #-8]
    // 0x8e6634: StoreField: r2->field_13 = r0
    //     0x8e6634: stur            w0, [x2, #0x13]
    //     0x8e6638: ldurb           w16, [x2, #-1]
    //     0x8e663c: ldurb           w17, [x0, #-1]
    //     0x8e6640: and             x16, x17, x16, lsr #2
    //     0x8e6644: tst             x16, HEAP, lsr #32
    //     0x8e6648: b.eq            #0x8e6650
    //     0x8e664c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e6650: ldur            x3, [fp, #-0x10]
    // 0x8e6654: r0 = LoadClassIdInstr(r3)
    //     0x8e6654: ldur            x0, [x3, #-1]
    //     0x8e6658: ubfx            x0, x0, #0xc, #0x14
    // 0x8e665c: mov             x1, x3
    // 0x8e6660: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e6660: sub             lr, x0, #1, lsl #12
    //     0x8e6664: ldr             lr, [x21, lr, lsl #3]
    //     0x8e6668: blr             lr
    // 0x8e666c: mov             x2, x0
    // 0x8e6670: r0 = BoxInt64Instr(r2)
    //     0x8e6670: sbfiz           x0, x2, #1, #0x1f
    //     0x8e6674: cmp             x2, x0, asr #1
    //     0x8e6678: b.eq            #0x8e6684
    //     0x8e667c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e6680: stur            x2, [x0, #7]
    // 0x8e6684: mov             x4, x0
    // 0x8e6688: r0 = AllocateUint8Array()
    //     0x8e6688: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e668c: ldur            x2, [fp, #-8]
    // 0x8e6690: ArrayStore: r2[0] = r0  ; List_4
    //     0x8e6690: stur            w0, [x2, #0x17]
    //     0x8e6694: ldurb           w16, [x2, #-1]
    //     0x8e6698: ldurb           w17, [x0, #-1]
    //     0x8e669c: and             x16, x17, x16, lsr #2
    //     0x8e66a0: tst             x16, HEAP, lsr #32
    //     0x8e66a4: b.eq            #0x8e66ac
    //     0x8e66a8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e66ac: ldur            x1, [fp, #-0x10]
    // 0x8e66b0: r0 = LoadClassIdInstr(r1)
    //     0x8e66b0: ldur            x0, [x1, #-1]
    //     0x8e66b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8e66b8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e66b8: sub             lr, x0, #1, lsl #12
    //     0x8e66bc: ldr             lr, [x21, lr, lsl #3]
    //     0x8e66c0: blr             lr
    // 0x8e66c4: mov             x2, x0
    // 0x8e66c8: r0 = BoxInt64Instr(r2)
    //     0x8e66c8: sbfiz           x0, x2, #1, #0x1f
    //     0x8e66cc: cmp             x2, x0, asr #1
    //     0x8e66d0: b.eq            #0x8e66dc
    //     0x8e66d4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e66d8: stur            x2, [x0, #7]
    // 0x8e66dc: mov             x4, x0
    // 0x8e66e0: r0 = AllocateUint8Array()
    //     0x8e66e0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e66e4: ldur            x1, [fp, #-8]
    // 0x8e66e8: StoreField: r1->field_1b = r0
    //     0x8e66e8: stur            w0, [x1, #0x1b]
    //     0x8e66ec: ldurb           w16, [x1, #-1]
    //     0x8e66f0: ldurb           w17, [x0, #-1]
    //     0x8e66f4: and             x16, x17, x16, lsr #2
    //     0x8e66f8: tst             x16, HEAP, lsr #32
    //     0x8e66fc: b.eq            #0x8e6704
    //     0x8e6700: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e6704: r0 = Null
    //     0x8e6704: mov             x0, NULL
    // 0x8e6708: LeaveFrame
    //     0x8e6708: mov             SP, fp
    //     0x8e670c: ldp             fp, lr, [SP], #0x10
    // 0x8e6710: ret
    //     0x8e6710: ret             
    // 0x8e6714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6714: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6718: b               #0x8e65cc
  }
  _ reset(/* No info */) {
    // ** addr: 0xe7e04c, size: 0x200
    // 0xe7e04c: EnterFrame
    //     0xe7e04c: stp             fp, lr, [SP, #-0x10]!
    //     0xe7e050: mov             fp, SP
    // 0xe7e054: AllocStack(0x28)
    //     0xe7e054: sub             SP, SP, #0x28
    // 0xe7e058: SetupParameters(CFBBlockCipher this /* r1 => r0, fp-0x28 */)
    //     0xe7e058: mov             x0, x1
    //     0xe7e05c: stur            x1, [fp, #-0x28]
    // 0xe7e060: CheckStackOverflow
    //     0xe7e060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7e064: cmp             SP, x16
    //     0xe7e068: b.ls            #0xe7e234
    // 0xe7e06c: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xe7e06c: ldur            w4, [x0, #0x17]
    // 0xe7e070: DecompressPointer r4
    //     0xe7e070: add             x4, x4, HEAP, lsl #32
    // 0xe7e074: stur            x4, [fp, #-0x20]
    // 0xe7e078: cmp             w4, NULL
    // 0xe7e07c: b.eq            #0xe7e23c
    // 0xe7e080: LoadField: r5 = r0->field_13
    //     0xe7e080: ldur            w5, [x0, #0x13]
    // 0xe7e084: DecompressPointer r5
    //     0xe7e084: add             x5, x5, HEAP, lsl #32
    // 0xe7e088: r16 = Sentinel
    //     0xe7e088: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7e08c: cmp             w5, w16
    // 0xe7e090: b.eq            #0xe7e240
    // 0xe7e094: stur            x5, [fp, #-0x18]
    // 0xe7e098: LoadField: r6 = r5->field_13
    //     0xe7e098: ldur            w6, [x5, #0x13]
    // 0xe7e09c: stur            x6, [fp, #-0x10]
    // 0xe7e0a0: r7 = LoadInt32Instr(r6)
    //     0xe7e0a0: sbfx            x7, x6, #1, #0x1f
    // 0xe7e0a4: stur            x7, [fp, #-8]
    // 0xe7e0a8: tbnz            x7, #0x3f, #0xe7e0bc
    // 0xe7e0ac: LoadField: r1 = r4->field_13
    //     0xe7e0ac: ldur            w1, [x4, #0x13]
    // 0xe7e0b0: r2 = LoadInt32Instr(r1)
    //     0xe7e0b0: sbfx            x2, x1, #1, #0x1f
    // 0xe7e0b4: cmp             x7, x2
    // 0xe7e0b8: b.le            #0xe7e0d4
    // 0xe7e0bc: LoadField: r1 = r4->field_13
    //     0xe7e0bc: ldur            w1, [x4, #0x13]
    // 0xe7e0c0: r3 = LoadInt32Instr(r1)
    //     0xe7e0c0: sbfx            x3, x1, #1, #0x1f
    // 0xe7e0c4: mov             x2, x6
    // 0xe7e0c8: r1 = 0
    //     0xe7e0c8: movz            x1, #0
    // 0xe7e0cc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe7e0cc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe7e0d0: r0 = checkValidRange()
    //     0xe7e0d0: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe7e0d4: ldur            x2, [fp, #-8]
    // 0xe7e0d8: cbz             x2, #0xe7e204
    // 0xe7e0dc: ldur            x0, [fp, #-0x10]
    // 0xe7e0e0: cmp             w0, #0x800
    // 0xe7e0e4: b.ge            #0xe7e1b8
    // 0xe7e0e8: ldur            x1, [fp, #-0x20]
    // 0xe7e0ec: ldur            x3, [fp, #-0x18]
    // 0xe7e0f0: mov             x4, x0
    // 0xe7e0f4: add             x2, x3, #0x17
    // 0xe7e0f8: add             x0, x1, #0x17
    // 0xe7e0fc: cbz             x4, #0xe7e1b4
    // 0xe7e100: cmp             x0, x2
    // 0xe7e104: b.ls            #0xe7e16c
    // 0xe7e108: sxtw            x4, w4
    // 0xe7e10c: add             x16, x2, x4, asr #1
    // 0xe7e110: cmp             x0, x16
    // 0xe7e114: b.hs            #0xe7e16c
    // 0xe7e118: mov             x2, x16
    // 0xe7e11c: add             x0, x0, x4, asr #1
    // 0xe7e120: tbz             w4, #4, #0xe7e12c
    // 0xe7e124: ldr             x16, [x2, #-8]!
    // 0xe7e128: str             x16, [x0, #-8]!
    // 0xe7e12c: tbz             w4, #3, #0xe7e138
    // 0xe7e130: ldr             w16, [x2, #-4]!
    // 0xe7e134: str             w16, [x0, #-4]!
    // 0xe7e138: tbz             w4, #2, #0xe7e144
    // 0xe7e13c: ldrh            w16, [x2, #-2]!
    // 0xe7e140: strh            w16, [x0, #-2]!
    // 0xe7e144: tbz             w4, #1, #0xe7e150
    // 0xe7e148: ldrb            w16, [x2, #-1]!
    // 0xe7e14c: strb            w16, [x0, #-1]!
    // 0xe7e150: ands            w4, w4, #0xffffffe1
    // 0xe7e154: b.eq            #0xe7e1b4
    // 0xe7e158: ldp             x16, x17, [x2, #-0x10]!
    // 0xe7e15c: stp             x16, x17, [x0, #-0x10]!
    // 0xe7e160: subs            w4, w4, #0x20
    // 0xe7e164: b.ne            #0xe7e158
    // 0xe7e168: b               #0xe7e1b4
    // 0xe7e16c: tbz             w4, #4, #0xe7e178
    // 0xe7e170: ldr             x16, [x2], #8
    // 0xe7e174: str             x16, [x0], #8
    // 0xe7e178: tbz             w4, #3, #0xe7e184
    // 0xe7e17c: ldr             w16, [x2], #4
    // 0xe7e180: str             w16, [x0], #4
    // 0xe7e184: tbz             w4, #2, #0xe7e190
    // 0xe7e188: ldrh            w16, [x2], #2
    // 0xe7e18c: strh            w16, [x0], #2
    // 0xe7e190: tbz             w4, #1, #0xe7e19c
    // 0xe7e194: ldrb            w16, [x2], #1
    // 0xe7e198: strb            w16, [x0], #1
    // 0xe7e19c: ands            w4, w4, #0xffffffe1
    // 0xe7e1a0: b.eq            #0xe7e1b4
    // 0xe7e1a4: ldp             x16, x17, [x2], #0x10
    // 0xe7e1a8: stp             x16, x17, [x0], #0x10
    // 0xe7e1ac: subs            w4, w4, #0x20
    // 0xe7e1b0: b.ne            #0xe7e1a4
    // 0xe7e1b4: b               #0xe7e204
    // 0xe7e1b8: ldur            x1, [fp, #-0x20]
    // 0xe7e1bc: ldur            x3, [fp, #-0x18]
    // 0xe7e1c0: LoadField: r0 = r1->field_7
    //     0xe7e1c0: ldur            x0, [x1, #7]
    // 0xe7e1c4: LoadField: r1 = r3->field_7
    //     0xe7e1c4: ldur            x1, [x3, #7]
    // 0xe7e1c8: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe7e1c8: mov             x3, THR
    //     0xe7e1cc: ldr             x9, [x3, #0x658]
    //     0xe7e1d0: mov             x17, fp
    //     0xe7e1d4: str             fp, [SP, #-8]!
    //     0xe7e1d8: mov             fp, SP
    //     0xe7e1dc: and             SP, SP, #0xfffffffffffffff0
    //     0xe7e1e0: mov             x19, sp
    //     0xe7e1e4: mov             sp, SP
    //     0xe7e1e8: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7e1ec: blr             x9
    //     0xe7e1f0: movz            x16, #0x8
    //     0xe7e1f4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7e1f8: mov             sp, x19
    //     0xe7e1fc: mov             SP, fp
    //     0xe7e200: ldr             fp, [SP], #8
    // 0xe7e204: ldur            x0, [fp, #-0x28]
    // 0xe7e208: LoadField: r1 = r0->field_f
    //     0xe7e208: ldur            w1, [x0, #0xf]
    // 0xe7e20c: DecompressPointer r1
    //     0xe7e20c: add             x1, x1, HEAP, lsl #32
    // 0xe7e210: r0 = LoadClassIdInstr(r1)
    //     0xe7e210: ldur            x0, [x1, #-1]
    //     0xe7e214: ubfx            x0, x0, #0xc, #0x14
    // 0xe7e218: r0 = GDT[cid_x0 + -0xeaf]()
    //     0xe7e218: sub             lr, x0, #0xeaf
    //     0xe7e21c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7e220: blr             lr
    // 0xe7e224: r0 = Null
    //     0xe7e224: mov             x0, NULL
    // 0xe7e228: LeaveFrame
    //     0xe7e228: mov             SP, fp
    //     0xe7e22c: ldp             fp, lr, [SP], #0x10
    // 0xe7e230: ret
    //     0xe7e230: ret             
    // 0xe7e234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7e234: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7e238: b               #0xe7e06c
    // 0xe7e23c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe7e23c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe7e240: r9 = _iv
    //     0xe7e240: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d60] Field <CFBBlockCipher._iv@909171941>: late (offset: 0x14)
    //     0xe7e244: ldr             x9, [x9, #0xd60]
    // 0xe7e248: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7e248: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0xe85288, size: 0x474
    // 0xe85288: EnterFrame
    //     0xe85288: stp             fp, lr, [SP, #-0x10]!
    //     0xe8528c: mov             fp, SP
    // 0xe85290: AllocStack(0x50)
    //     0xe85290: sub             SP, SP, #0x50
    // 0xe85294: SetupParameters(CFBBlockCipher this /* r1 => r4, fp-0x20 */, dynamic _ /* r3 => r0, fp-0x28 */)
    //     0xe85294: mov             x4, x1
    //     0xe85298: mov             x0, x3
    //     0xe8529c: stur            x1, [fp, #-0x20]
    //     0xe852a0: stur            x3, [fp, #-0x28]
    // 0xe852a4: CheckStackOverflow
    //     0xe852a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe852a8: cmp             SP, x16
    //     0xe852ac: b.ls            #0xe856e8
    // 0xe852b0: StoreField: r4->field_1f = r2
    //     0xe852b0: stur            w2, [x4, #0x1f]
    // 0xe852b4: r1 = LoadClassIdInstr(r0)
    //     0xe852b4: ldur            x1, [x0, #-1]
    //     0xe852b8: ubfx            x1, x1, #0xc, #0x14
    // 0xe852bc: cmp             x1, #0x2a8
    // 0xe852c0: b.ne            #0xe85688
    // 0xe852c4: LoadField: r6 = r0->field_b
    //     0xe852c4: ldur            w6, [x0, #0xb]
    // 0xe852c8: DecompressPointer r6
    //     0xe852c8: add             x6, x6, HEAP, lsl #32
    // 0xe852cc: stur            x6, [fp, #-0x18]
    // 0xe852d0: LoadField: r1 = r6->field_13
    //     0xe852d0: ldur            w1, [x6, #0x13]
    // 0xe852d4: LoadField: r5 = r4->field_13
    //     0xe852d4: ldur            w5, [x4, #0x13]
    // 0xe852d8: DecompressPointer r5
    //     0xe852d8: add             x5, x5, HEAP, lsl #32
    // 0xe852dc: r16 = Sentinel
    //     0xe852dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe852e0: cmp             w5, w16
    // 0xe852e4: b.eq            #0xe856f0
    // 0xe852e8: stur            x5, [fp, #-0x50]
    // 0xe852ec: LoadField: r7 = r5->field_13
    //     0xe852ec: ldur            w7, [x5, #0x13]
    // 0xe852f0: stur            x7, [fp, #-0x48]
    // 0xe852f4: r8 = LoadInt32Instr(r1)
    //     0xe852f4: sbfx            x8, x1, #1, #0x1f
    // 0xe852f8: stur            x8, [fp, #-0x10]
    // 0xe852fc: r9 = LoadInt32Instr(r7)
    //     0xe852fc: sbfx            x9, x7, #1, #0x1f
    // 0xe85300: stur            x9, [fp, #-0x40]
    // 0xe85304: cmp             x8, x9
    // 0xe85308: b.ge            #0xe854e4
    // 0xe8530c: sub             x7, x9, x8
    // 0xe85310: mov             x1, x5
    // 0xe85314: mov             x3, x7
    // 0xe85318: stur            x7, [fp, #-8]
    // 0xe8531c: r2 = 0
    //     0xe8531c: movz            x2, #0
    // 0xe85320: r5 = 0
    //     0xe85320: movz            x5, #0
    // 0xe85324: r0 = fillRange()
    //     0xe85324: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0xe85328: ldur            x0, [fp, #-0x20]
    // 0xe8532c: LoadField: r4 = r0->field_13
    //     0xe8532c: ldur            w4, [x0, #0x13]
    // 0xe85330: DecompressPointer r4
    //     0xe85330: add             x4, x4, HEAP, lsl #32
    // 0xe85334: stur            x4, [fp, #-0x38]
    // 0xe85338: LoadField: r5 = r4->field_13
    //     0xe85338: ldur            w5, [x4, #0x13]
    // 0xe8533c: ldur            x6, [fp, #-8]
    // 0xe85340: stur            x5, [fp, #-0x30]
    // 0xe85344: tbnz            x6, #0x3f, #0xe85354
    // 0xe85348: r1 = LoadInt32Instr(r5)
    //     0xe85348: sbfx            x1, x5, #1, #0x1f
    // 0xe8534c: cmp             x6, x1
    // 0xe85350: b.le            #0xe85368
    // 0xe85354: r3 = LoadInt32Instr(r5)
    //     0xe85354: sbfx            x3, x5, #1, #0x1f
    // 0xe85358: mov             x1, x6
    // 0xe8535c: mov             x2, x5
    // 0xe85360: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe85360: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe85364: r0 = checkValidRange()
    //     0xe85364: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe85368: ldur            x2, [fp, #-8]
    // 0xe8536c: ldur            x0, [fp, #-0x30]
    // 0xe85370: ldur            x4, [fp, #-0x10]
    // 0xe85374: r1 = LoadInt32Instr(r0)
    //     0xe85374: sbfx            x1, x0, #1, #0x1f
    // 0xe85378: sub             x3, x1, x2
    // 0xe8537c: cmp             x4, x3
    // 0xe85380: b.lt            #0xe856d0
    // 0xe85384: cbz             x3, #0xe8563c
    // 0xe85388: r0 = BoxInt64Instr(r3)
    //     0xe85388: sbfiz           x0, x3, #1, #0x1f
    //     0xe8538c: cmp             x3, x0, asr #1
    //     0xe85390: b.eq            #0xe8539c
    //     0xe85394: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe85398: stur            x3, [x0, #7]
    // 0xe8539c: cmp             w0, #0x800
    // 0xe853a0: b.ge            #0xe85480
    // 0xe853a4: ldur            x6, [fp, #-0x18]
    // 0xe853a8: ldur            x1, [fp, #-0x38]
    // 0xe853ac: lsl             x3, x2, #1
    // 0xe853b0: mov             x4, x0
    // 0xe853b4: add             x2, x6, #0x17
    // 0xe853b8: sxtw            x3, w3
    // 0xe853bc: add             x0, x1, x3, asr #1
    // 0xe853c0: add             x0, x0, #0x17
    // 0xe853c4: cbz             x4, #0xe8547c
    // 0xe853c8: cmp             x0, x2
    // 0xe853cc: b.ls            #0xe85434
    // 0xe853d0: sxtw            x4, w4
    // 0xe853d4: add             x16, x2, x4, asr #1
    // 0xe853d8: cmp             x0, x16
    // 0xe853dc: b.hs            #0xe85434
    // 0xe853e0: mov             x2, x16
    // 0xe853e4: add             x0, x0, x4, asr #1
    // 0xe853e8: tbz             w4, #4, #0xe853f4
    // 0xe853ec: ldr             x16, [x2, #-8]!
    // 0xe853f0: str             x16, [x0, #-8]!
    // 0xe853f4: tbz             w4, #3, #0xe85400
    // 0xe853f8: ldr             w16, [x2, #-4]!
    // 0xe853fc: str             w16, [x0, #-4]!
    // 0xe85400: tbz             w4, #2, #0xe8540c
    // 0xe85404: ldrh            w16, [x2, #-2]!
    // 0xe85408: strh            w16, [x0, #-2]!
    // 0xe8540c: tbz             w4, #1, #0xe85418
    // 0xe85410: ldrb            w16, [x2, #-1]!
    // 0xe85414: strb            w16, [x0, #-1]!
    // 0xe85418: ands            w4, w4, #0xffffffe1
    // 0xe8541c: b.eq            #0xe8547c
    // 0xe85420: ldp             x16, x17, [x2, #-0x10]!
    // 0xe85424: stp             x16, x17, [x0, #-0x10]!
    // 0xe85428: subs            w4, w4, #0x20
    // 0xe8542c: b.ne            #0xe85420
    // 0xe85430: b               #0xe8547c
    // 0xe85434: tbz             w4, #4, #0xe85440
    // 0xe85438: ldr             x16, [x2], #8
    // 0xe8543c: str             x16, [x0], #8
    // 0xe85440: tbz             w4, #3, #0xe8544c
    // 0xe85444: ldr             w16, [x2], #4
    // 0xe85448: str             w16, [x0], #4
    // 0xe8544c: tbz             w4, #2, #0xe85458
    // 0xe85450: ldrh            w16, [x2], #2
    // 0xe85454: strh            w16, [x0], #2
    // 0xe85458: tbz             w4, #1, #0xe85464
    // 0xe8545c: ldrb            w16, [x2], #1
    // 0xe85460: strb            w16, [x0], #1
    // 0xe85464: ands            w4, w4, #0xffffffe1
    // 0xe85468: b.eq            #0xe8547c
    // 0xe8546c: ldp             x16, x17, [x2], #0x10
    // 0xe85470: stp             x16, x17, [x0], #0x10
    // 0xe85474: subs            w4, w4, #0x20
    // 0xe85478: b.ne            #0xe8546c
    // 0xe8547c: b               #0xe8563c
    // 0xe85480: ldur            x6, [fp, #-0x18]
    // 0xe85484: ldur            x1, [fp, #-0x38]
    // 0xe85488: LoadField: r0 = r1->field_7
    //     0xe85488: ldur            x0, [x1, #7]
    // 0xe8548c: add             x1, x0, x2
    // 0xe85490: LoadField: r0 = r6->field_7
    //     0xe85490: ldur            x0, [x6, #7]
    // 0xe85494: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe85494: mov             x2, THR
    //     0xe85498: ldr             x9, [x2, #0x658]
    //     0xe8549c: mov             x16, x0
    //     0xe854a0: mov             x0, x1
    //     0xe854a4: mov             x1, x16
    //     0xe854a8: mov             x2, x3
    //     0xe854ac: mov             x17, fp
    //     0xe854b0: str             fp, [SP, #-8]!
    //     0xe854b4: mov             fp, SP
    //     0xe854b8: and             SP, SP, #0xfffffffffffffff0
    //     0xe854bc: mov             x19, sp
    //     0xe854c0: mov             sp, SP
    //     0xe854c4: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe854c8: blr             x9
    //     0xe854cc: movz            x16, #0x8
    //     0xe854d0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe854d4: mov             sp, x19
    //     0xe854d8: mov             SP, fp
    //     0xe854dc: ldr             fp, [SP], #8
    // 0xe854e0: b               #0xe8563c
    // 0xe854e4: mov             x4, x8
    // 0xe854e8: tbz             x9, #0x3f, #0xe85500
    // 0xe854ec: mov             x2, x7
    // 0xe854f0: mov             x3, x9
    // 0xe854f4: r1 = 0
    //     0xe854f4: movz            x1, #0
    // 0xe854f8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe854f8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe854fc: r0 = checkValidRange()
    //     0xe854fc: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe85500: ldur            x0, [fp, #-0x10]
    // 0xe85504: ldur            x2, [fp, #-0x40]
    // 0xe85508: cmp             x0, x2
    // 0xe8550c: b.lt            #0xe856dc
    // 0xe85510: cbz             x2, #0xe8563c
    // 0xe85514: ldur            x0, [fp, #-0x48]
    // 0xe85518: cmp             w0, #0x800
    // 0xe8551c: b.ge            #0xe855f0
    // 0xe85520: ldur            x3, [fp, #-0x18]
    // 0xe85524: ldur            x1, [fp, #-0x50]
    // 0xe85528: mov             x4, x0
    // 0xe8552c: add             x2, x3, #0x17
    // 0xe85530: add             x0, x1, #0x17
    // 0xe85534: cbz             x4, #0xe855ec
    // 0xe85538: cmp             x0, x2
    // 0xe8553c: b.ls            #0xe855a4
    // 0xe85540: sxtw            x4, w4
    // 0xe85544: add             x16, x2, x4, asr #1
    // 0xe85548: cmp             x0, x16
    // 0xe8554c: b.hs            #0xe855a4
    // 0xe85550: mov             x2, x16
    // 0xe85554: add             x0, x0, x4, asr #1
    // 0xe85558: tbz             w4, #4, #0xe85564
    // 0xe8555c: ldr             x16, [x2, #-8]!
    // 0xe85560: str             x16, [x0, #-8]!
    // 0xe85564: tbz             w4, #3, #0xe85570
    // 0xe85568: ldr             w16, [x2, #-4]!
    // 0xe8556c: str             w16, [x0, #-4]!
    // 0xe85570: tbz             w4, #2, #0xe8557c
    // 0xe85574: ldrh            w16, [x2, #-2]!
    // 0xe85578: strh            w16, [x0, #-2]!
    // 0xe8557c: tbz             w4, #1, #0xe85588
    // 0xe85580: ldrb            w16, [x2, #-1]!
    // 0xe85584: strb            w16, [x0, #-1]!
    // 0xe85588: ands            w4, w4, #0xffffffe1
    // 0xe8558c: b.eq            #0xe855ec
    // 0xe85590: ldp             x16, x17, [x2, #-0x10]!
    // 0xe85594: stp             x16, x17, [x0, #-0x10]!
    // 0xe85598: subs            w4, w4, #0x20
    // 0xe8559c: b.ne            #0xe85590
    // 0xe855a0: b               #0xe855ec
    // 0xe855a4: tbz             w4, #4, #0xe855b0
    // 0xe855a8: ldr             x16, [x2], #8
    // 0xe855ac: str             x16, [x0], #8
    // 0xe855b0: tbz             w4, #3, #0xe855bc
    // 0xe855b4: ldr             w16, [x2], #4
    // 0xe855b8: str             w16, [x0], #4
    // 0xe855bc: tbz             w4, #2, #0xe855c8
    // 0xe855c0: ldrh            w16, [x2], #2
    // 0xe855c4: strh            w16, [x0], #2
    // 0xe855c8: tbz             w4, #1, #0xe855d4
    // 0xe855cc: ldrb            w16, [x2], #1
    // 0xe855d0: strb            w16, [x0], #1
    // 0xe855d4: ands            w4, w4, #0xffffffe1
    // 0xe855d8: b.eq            #0xe855ec
    // 0xe855dc: ldp             x16, x17, [x2], #0x10
    // 0xe855e0: stp             x16, x17, [x0], #0x10
    // 0xe855e4: subs            w4, w4, #0x20
    // 0xe855e8: b.ne            #0xe855dc
    // 0xe855ec: b               #0xe8563c
    // 0xe855f0: ldur            x3, [fp, #-0x18]
    // 0xe855f4: ldur            x1, [fp, #-0x50]
    // 0xe855f8: LoadField: r0 = r1->field_7
    //     0xe855f8: ldur            x0, [x1, #7]
    // 0xe855fc: LoadField: r1 = r3->field_7
    //     0xe855fc: ldur            x1, [x3, #7]
    // 0xe85600: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe85600: mov             x3, THR
    //     0xe85604: ldr             x9, [x3, #0x658]
    //     0xe85608: mov             x17, fp
    //     0xe8560c: str             fp, [SP, #-8]!
    //     0xe85610: mov             fp, SP
    //     0xe85614: and             SP, SP, #0xfffffffffffffff0
    //     0xe85618: mov             x19, sp
    //     0xe8561c: mov             sp, SP
    //     0xe85620: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe85624: blr             x9
    //     0xe85628: movz            x16, #0x8
    //     0xe8562c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe85630: mov             sp, x19
    //     0xe85634: mov             SP, fp
    //     0xe85638: ldr             fp, [SP], #8
    // 0xe8563c: ldur            x0, [fp, #-0x20]
    // 0xe85640: ldur            x3, [fp, #-0x28]
    // 0xe85644: mov             x1, x0
    // 0xe85648: r0 = reset()
    //     0xe85648: bl              #0xe7e04c  ; [package:pointycastle/block/modes/cfb.dart] CFBBlockCipher::reset
    // 0xe8564c: ldur            x0, [fp, #-0x20]
    // 0xe85650: LoadField: r1 = r0->field_f
    //     0xe85650: ldur            w1, [x0, #0xf]
    // 0xe85654: DecompressPointer r1
    //     0xe85654: add             x1, x1, HEAP, lsl #32
    // 0xe85658: ldur            x3, [fp, #-0x28]
    // 0xe8565c: LoadField: r0 = r3->field_f
    //     0xe8565c: ldur            w0, [x3, #0xf]
    // 0xe85660: DecompressPointer r0
    //     0xe85660: add             x0, x0, HEAP, lsl #32
    // 0xe85664: r2 = LoadClassIdInstr(r1)
    //     0xe85664: ldur            x2, [x1, #-1]
    //     0xe85668: ubfx            x2, x2, #0xc, #0x14
    // 0xe8566c: mov             x3, x0
    // 0xe85670: mov             x0, x2
    // 0xe85674: r2 = true
    //     0xe85674: add             x2, NULL, #0x20  ; true
    // 0xe85678: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe85678: sub             lr, x0, #0xeda
    //     0xe8567c: ldr             lr, [x21, lr, lsl #3]
    //     0xe85680: blr             lr
    // 0xe85684: b               #0xe856c0
    // 0xe85688: mov             x3, x0
    // 0xe8568c: mov             x0, x4
    // 0xe85690: mov             x1, x0
    // 0xe85694: r0 = reset()
    //     0xe85694: bl              #0xe7e04c  ; [package:pointycastle/block/modes/cfb.dart] CFBBlockCipher::reset
    // 0xe85698: ldur            x0, [fp, #-0x20]
    // 0xe8569c: LoadField: r1 = r0->field_f
    //     0xe8569c: ldur            w1, [x0, #0xf]
    // 0xe856a0: DecompressPointer r1
    //     0xe856a0: add             x1, x1, HEAP, lsl #32
    // 0xe856a4: r0 = LoadClassIdInstr(r1)
    //     0xe856a4: ldur            x0, [x1, #-1]
    //     0xe856a8: ubfx            x0, x0, #0xc, #0x14
    // 0xe856ac: ldur            x3, [fp, #-0x28]
    // 0xe856b0: r2 = true
    //     0xe856b0: add             x2, NULL, #0x20  ; true
    // 0xe856b4: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe856b4: sub             lr, x0, #0xeda
    //     0xe856b8: ldr             lr, [x21, lr, lsl #3]
    //     0xe856bc: blr             lr
    // 0xe856c0: r0 = Null
    //     0xe856c0: mov             x0, NULL
    // 0xe856c4: LeaveFrame
    //     0xe856c4: mov             SP, fp
    //     0xe856c8: ldp             fp, lr, [SP], #0x10
    // 0xe856cc: ret
    //     0xe856cc: ret             
    // 0xe856d0: r0 = tooFew()
    //     0xe856d0: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xe856d4: r0 = Throw()
    //     0xe856d4: bl              #0xec04b8  ; ThrowStub
    // 0xe856d8: brk             #0
    // 0xe856dc: r0 = tooFew()
    //     0xe856dc: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xe856e0: r0 = Throw()
    //     0xe856e0: bl              #0xec04b8  ; ThrowStub
    // 0xe856e4: brk             #0
    // 0xe856e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe856e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe856ec: b               #0xe852b0
    // 0xe856f0: r9 = _iv
    //     0xe856f0: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d60] Field <CFBBlockCipher._iv@909171941>: late (offset: 0x14)
    //     0xe856f4: ldr             x9, [x9, #0xd60]
    // 0xe856f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe856f8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xea654c, size: 0x58
    // 0xea654c: EnterFrame
    //     0xea654c: stp             fp, lr, [SP, #-0x10]!
    //     0xea6550: mov             fp, SP
    // 0xea6554: CheckStackOverflow
    //     0xea6554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea6558: cmp             SP, x16
    //     0xea655c: b.ls            #0xea6590
    // 0xea6560: LoadField: r0 = r1->field_1f
    //     0xea6560: ldur            w0, [x1, #0x1f]
    // 0xea6564: DecompressPointer r0
    //     0xea6564: add             x0, x0, HEAP, lsl #32
    // 0xea6568: r16 = Sentinel
    //     0xea6568: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea656c: cmp             w0, w16
    // 0xea6570: b.eq            #0xea6598
    // 0xea6574: tbnz            w0, #4, #0xea6580
    // 0xea6578: r0 = _encryptBlock()
    //     0xea6578: bl              #0xea6b80  ; [package:pointycastle/block/modes/cfb.dart] CFBBlockCipher::_encryptBlock
    // 0xea657c: b               #0xea6584
    // 0xea6580: r0 = _decryptBlock()
    //     0xea6580: bl              #0xea65a4  ; [package:pointycastle/block/modes/cfb.dart] CFBBlockCipher::_decryptBlock
    // 0xea6584: LeaveFrame
    //     0xea6584: mov             SP, fp
    //     0xea6588: ldp             fp, lr, [SP], #0x10
    // 0xea658c: ret
    //     0xea658c: ret             
    // 0xea6590: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea6590: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea6594: b               #0xea6560
    // 0xea6598: r9 = _encrypting
    //     0xea6598: add             x9, PP, #0x23, lsl #12  ; [pp+0x23698] Field <CFBBlockCipher._encrypting@909171941>: late (offset: 0x20)
    //     0xea659c: ldr             x9, [x9, #0x698]
    // 0xea65a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea65a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _decryptBlock(/* No info */) {
    // ** addr: 0xea65a4, size: 0x5dc
    // 0xea65a4: EnterFrame
    //     0xea65a4: stp             fp, lr, [SP, #-0x10]!
    //     0xea65a8: mov             fp, SP
    // 0xea65ac: AllocStack(0x68)
    //     0xea65ac: sub             SP, SP, #0x68
    // 0xea65b0: SetupParameters(CFBBlockCipher this /* r1 => r10, fp-0x20 */, dynamic _ /* r2 => r9, fp-0x28 */, dynamic _ /* r3 => r8, fp-0x30 */, dynamic _ /* r5 => r7, fp-0x38 */, dynamic _ /* r6 => r4, fp-0x40 */)
    //     0xea65b0: mov             x10, x1
    //     0xea65b4: mov             x9, x2
    //     0xea65b8: mov             x8, x3
    //     0xea65bc: mov             x7, x5
    //     0xea65c0: mov             x4, x6
    //     0xea65c4: stur            x1, [fp, #-0x20]
    //     0xea65c8: stur            x2, [fp, #-0x28]
    //     0xea65cc: stur            x3, [fp, #-0x30]
    //     0xea65d0: stur            x5, [fp, #-0x38]
    //     0xea65d4: stur            x6, [fp, #-0x40]
    // 0xea65d8: CheckStackOverflow
    //     0xea65d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea65dc: cmp             SP, x16
    //     0xea65e0: b.ls            #0xea6b50
    // 0xea65e4: LoadField: r11 = r10->field_7
    //     0xea65e4: ldur            x11, [x10, #7]
    // 0xea65e8: stur            x11, [fp, #-0x18]
    // 0xea65ec: add             x0, x8, x11
    // 0xea65f0: LoadField: r1 = r9->field_13
    //     0xea65f0: ldur            w1, [x9, #0x13]
    // 0xea65f4: r12 = LoadInt32Instr(r1)
    //     0xea65f4: sbfx            x12, x1, #1, #0x1f
    // 0xea65f8: stur            x12, [fp, #-0x10]
    // 0xea65fc: cmp             x0, x12
    // 0xea6600: b.gt            #0xea6ae4
    // 0xea6604: add             x0, x4, x11
    // 0xea6608: LoadField: r1 = r7->field_13
    //     0xea6608: ldur            w1, [x7, #0x13]
    // 0xea660c: r13 = LoadInt32Instr(r1)
    //     0xea660c: sbfx            x13, x1, #1, #0x1f
    // 0xea6610: stur            x13, [fp, #-8]
    // 0xea6614: cmp             x0, x13
    // 0xea6618: b.gt            #0xea6b0c
    // 0xea661c: LoadField: r1 = r10->field_f
    //     0xea661c: ldur            w1, [x10, #0xf]
    // 0xea6620: DecompressPointer r1
    //     0xea6620: add             x1, x1, HEAP, lsl #32
    // 0xea6624: ArrayLoad: r2 = r10[0]  ; List_4
    //     0xea6624: ldur            w2, [x10, #0x17]
    // 0xea6628: DecompressPointer r2
    //     0xea6628: add             x2, x2, HEAP, lsl #32
    // 0xea662c: cmp             w2, NULL
    // 0xea6630: b.eq            #0xea6b58
    // 0xea6634: LoadField: r5 = r10->field_1b
    //     0xea6634: ldur            w5, [x10, #0x1b]
    // 0xea6638: DecompressPointer r5
    //     0xea6638: add             x5, x5, HEAP, lsl #32
    // 0xea663c: cmp             w5, NULL
    // 0xea6640: b.eq            #0xea6b5c
    // 0xea6644: r0 = LoadClassIdInstr(r1)
    //     0xea6644: ldur            x0, [x1, #-1]
    //     0xea6648: ubfx            x0, x0, #0xc, #0x14
    // 0xea664c: r3 = 0
    //     0xea664c: movz            x3, #0
    // 0xea6650: r6 = 0
    //     0xea6650: movz            x6, #0
    // 0xea6654: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea6654: sub             lr, x0, #0xf69
    //     0xea6658: ldr             lr, [x21, lr, lsl #3]
    //     0xea665c: blr             lr
    // 0xea6660: ldur            x0, [fp, #-0x20]
    // 0xea6664: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xea6664: ldur            w3, [x0, #0x17]
    // 0xea6668: DecompressPointer r3
    //     0xea6668: add             x3, x3, HEAP, lsl #32
    // 0xea666c: stur            x3, [fp, #-0x58]
    // 0xea6670: cmp             w3, NULL
    // 0xea6674: b.eq            #0xea6b60
    // 0xea6678: LoadField: r1 = r3->field_13
    //     0xea6678: ldur            w1, [x3, #0x13]
    // 0xea667c: r4 = LoadInt32Instr(r1)
    //     0xea667c: sbfx            x4, x1, #1, #0x1f
    // 0xea6680: ldur            x5, [fp, #-0x18]
    // 0xea6684: stur            x4, [fp, #-0x50]
    // 0xea6688: sub             x6, x4, x5
    // 0xea668c: mov             x1, x3
    // 0xea6690: mov             x2, x5
    // 0xea6694: stur            x6, [fp, #-0x48]
    // 0xea6698: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xea6698: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xea669c: r0 = sublist()
    //     0xea669c: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xea66a0: mov             x5, x0
    // 0xea66a4: ldur            x4, [fp, #-0x48]
    // 0xea66a8: stur            x5, [fp, #-0x60]
    // 0xea66ac: tbz             x4, #0x3f, #0xea66b8
    // 0xea66b0: ldur            x3, [fp, #-0x50]
    // 0xea66b4: b               #0xea66c4
    // 0xea66b8: ldur            x3, [fp, #-0x50]
    // 0xea66bc: cmp             x4, x3
    // 0xea66c0: b.le            #0xea66e8
    // 0xea66c4: r0 = BoxInt64Instr(r4)
    //     0xea66c4: sbfiz           x0, x4, #1, #0x1f
    //     0xea66c8: cmp             x4, x0, asr #1
    //     0xea66cc: b.eq            #0xea66d8
    //     0xea66d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea66d4: stur            x4, [x0, #7]
    // 0xea66d8: mov             x2, x0
    // 0xea66dc: r1 = 0
    //     0xea66dc: movz            x1, #0
    // 0xea66e0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea66e0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea66e4: r0 = checkValidRange()
    //     0xea66e4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea66e8: ldur            x20, [fp, #-0x48]
    // 0xea66ec: ldur            x2, [fp, #-0x60]
    // 0xea66f0: LoadField: r0 = r2->field_13
    //     0xea66f0: ldur            w0, [x2, #0x13]
    // 0xea66f4: r1 = LoadInt32Instr(r0)
    //     0xea66f4: sbfx            x1, x0, #1, #0x1f
    // 0xea66f8: cmp             x1, x20
    // 0xea66fc: b.lt            #0xea6b38
    // 0xea6700: cbz             x20, #0xea6838
    // 0xea6704: r0 = BoxInt64Instr(r20)
    //     0xea6704: sbfiz           x0, x20, #1, #0x1f
    //     0xea6708: cmp             x20, x0, asr #1
    //     0xea670c: b.eq            #0xea6718
    //     0xea6710: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea6714: stur            x20, [x0, #7]
    // 0xea6718: cmp             w0, #0x800
    // 0xea671c: b.ge            #0xea67ec
    // 0xea6720: ldur            x1, [fp, #-0x58]
    // 0xea6724: mov             x4, x0
    // 0xea6728: add             x3, x2, #0x17
    // 0xea672c: add             x0, x1, #0x17
    // 0xea6730: cbz             x4, #0xea67e8
    // 0xea6734: cmp             x0, x3
    // 0xea6738: b.ls            #0xea67a0
    // 0xea673c: sxtw            x4, w4
    // 0xea6740: add             x16, x3, x4, asr #1
    // 0xea6744: cmp             x0, x16
    // 0xea6748: b.hs            #0xea67a0
    // 0xea674c: mov             x3, x16
    // 0xea6750: add             x0, x0, x4, asr #1
    // 0xea6754: tbz             w4, #4, #0xea6760
    // 0xea6758: ldr             x16, [x3, #-8]!
    // 0xea675c: str             x16, [x0, #-8]!
    // 0xea6760: tbz             w4, #3, #0xea676c
    // 0xea6764: ldr             w16, [x3, #-4]!
    // 0xea6768: str             w16, [x0, #-4]!
    // 0xea676c: tbz             w4, #2, #0xea6778
    // 0xea6770: ldrh            w16, [x3, #-2]!
    // 0xea6774: strh            w16, [x0, #-2]!
    // 0xea6778: tbz             w4, #1, #0xea6784
    // 0xea677c: ldrb            w16, [x3, #-1]!
    // 0xea6780: strb            w16, [x0, #-1]!
    // 0xea6784: ands            w4, w4, #0xffffffe1
    // 0xea6788: b.eq            #0xea67e8
    // 0xea678c: ldp             x16, x17, [x3, #-0x10]!
    // 0xea6790: stp             x16, x17, [x0, #-0x10]!
    // 0xea6794: subs            w4, w4, #0x20
    // 0xea6798: b.ne            #0xea678c
    // 0xea679c: b               #0xea67e8
    // 0xea67a0: tbz             w4, #4, #0xea67ac
    // 0xea67a4: ldr             x16, [x3], #8
    // 0xea67a8: str             x16, [x0], #8
    // 0xea67ac: tbz             w4, #3, #0xea67b8
    // 0xea67b0: ldr             w16, [x3], #4
    // 0xea67b4: str             w16, [x0], #4
    // 0xea67b8: tbz             w4, #2, #0xea67c4
    // 0xea67bc: ldrh            w16, [x3], #2
    // 0xea67c0: strh            w16, [x0], #2
    // 0xea67c4: tbz             w4, #1, #0xea67d0
    // 0xea67c8: ldrb            w16, [x3], #1
    // 0xea67cc: strb            w16, [x0], #1
    // 0xea67d0: ands            w4, w4, #0xffffffe1
    // 0xea67d4: b.eq            #0xea67e8
    // 0xea67d8: ldp             x16, x17, [x3], #0x10
    // 0xea67dc: stp             x16, x17, [x0], #0x10
    // 0xea67e0: subs            w4, w4, #0x20
    // 0xea67e4: b.ne            #0xea67d8
    // 0xea67e8: b               #0xea6838
    // 0xea67ec: ldur            x1, [fp, #-0x58]
    // 0xea67f0: LoadField: r0 = r1->field_7
    //     0xea67f0: ldur            x0, [x1, #7]
    // 0xea67f4: LoadField: r1 = r2->field_7
    //     0xea67f4: ldur            x1, [x2, #7]
    // 0xea67f8: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea67f8: mov             x2, THR
    //     0xea67fc: ldr             x9, [x2, #0x658]
    //     0xea6800: mov             x2, x20
    //     0xea6804: mov             x17, fp
    //     0xea6808: str             fp, [SP, #-8]!
    //     0xea680c: mov             fp, SP
    //     0xea6810: and             SP, SP, #0xfffffffffffffff0
    //     0xea6814: mov             x19, sp
    //     0xea6818: mov             sp, SP
    //     0xea681c: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea6820: blr             x9
    //     0xea6824: movz            x16, #0x8
    //     0xea6828: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea682c: mov             sp, x19
    //     0xea6830: mov             SP, fp
    //     0xea6834: ldr             fp, [SP], #8
    // 0xea6838: ldur            x0, [fp, #-0x20]
    // 0xea683c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xea683c: ldur            w3, [x0, #0x17]
    // 0xea6840: DecompressPointer r3
    //     0xea6840: add             x3, x3, HEAP, lsl #32
    // 0xea6844: stur            x3, [fp, #-0x60]
    // 0xea6848: cmp             w3, NULL
    // 0xea684c: b.eq            #0xea6b64
    // 0xea6850: LoadField: r4 = r3->field_13
    //     0xea6850: ldur            w4, [x3, #0x13]
    // 0xea6854: ldur            x1, [fp, #-0x28]
    // 0xea6858: ldur            x2, [fp, #-0x30]
    // 0xea685c: stur            x4, [fp, #-0x58]
    // 0xea6860: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xea6860: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xea6864: r0 = sublist()
    //     0xea6864: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xea6868: mov             x4, x0
    // 0xea686c: ldur            x0, [fp, #-0x48]
    // 0xea6870: stur            x4, [fp, #-0x68]
    // 0xea6874: tbz             x0, #0x3f, #0xea6880
    // 0xea6878: ldur            x5, [fp, #-0x58]
    // 0xea687c: b               #0xea6890
    // 0xea6880: ldur            x5, [fp, #-0x58]
    // 0xea6884: r1 = LoadInt32Instr(r5)
    //     0xea6884: sbfx            x1, x5, #1, #0x1f
    // 0xea6888: cmp             x0, x1
    // 0xea688c: b.le            #0xea68a4
    // 0xea6890: r3 = LoadInt32Instr(r5)
    //     0xea6890: sbfx            x3, x5, #1, #0x1f
    // 0xea6894: mov             x1, x0
    // 0xea6898: mov             x2, x5
    // 0xea689c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea689c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea68a0: r0 = checkValidRange()
    //     0xea68a0: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea68a4: ldur            x2, [fp, #-0x48]
    // 0xea68a8: ldur            x3, [fp, #-0x68]
    // 0xea68ac: ldur            x0, [fp, #-0x58]
    // 0xea68b0: r1 = LoadInt32Instr(r0)
    //     0xea68b0: sbfx            x1, x0, #1, #0x1f
    // 0xea68b4: sub             x4, x1, x2
    // 0xea68b8: LoadField: r0 = r3->field_13
    //     0xea68b8: ldur            w0, [x3, #0x13]
    // 0xea68bc: r1 = LoadInt32Instr(r0)
    //     0xea68bc: sbfx            x1, x0, #1, #0x1f
    // 0xea68c0: cmp             x1, x4
    // 0xea68c4: b.lt            #0xea6b44
    // 0xea68c8: cbz             x4, #0xea6a2c
    // 0xea68cc: r0 = BoxInt64Instr(r4)
    //     0xea68cc: sbfiz           x0, x4, #1, #0x1f
    //     0xea68d0: cmp             x4, x0, asr #1
    //     0xea68d4: b.eq            #0xea68e0
    //     0xea68d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea68dc: stur            x4, [x0, #7]
    // 0xea68e0: mov             x5, x0
    // 0xea68e4: cmp             w5, #0x800
    // 0xea68e8: b.ge            #0xea69d0
    // 0xea68ec: ldur            x6, [fp, #-0x60]
    // 0xea68f0: r0 = BoxInt64Instr(r2)
    //     0xea68f0: sbfiz           x0, x2, #1, #0x1f
    //     0xea68f4: cmp             x2, x0, asr #1
    //     0xea68f8: b.eq            #0xea6904
    //     0xea68fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea6900: stur            x2, [x0, #7]
    // 0xea6904: add             x2, x3, #0x17
    // 0xea6908: sxtw            x0, w0
    // 0xea690c: add             x1, x6, x0, asr #1
    // 0xea6910: add             x1, x1, #0x17
    // 0xea6914: cbz             x5, #0xea69cc
    // 0xea6918: cmp             x1, x2
    // 0xea691c: b.ls            #0xea6984
    // 0xea6920: sxtw            x5, w5
    // 0xea6924: add             x16, x2, x5, asr #1
    // 0xea6928: cmp             x1, x16
    // 0xea692c: b.hs            #0xea6984
    // 0xea6930: mov             x2, x16
    // 0xea6934: add             x1, x1, x5, asr #1
    // 0xea6938: tbz             w5, #4, #0xea6944
    // 0xea693c: ldr             x16, [x2, #-8]!
    // 0xea6940: str             x16, [x1, #-8]!
    // 0xea6944: tbz             w5, #3, #0xea6950
    // 0xea6948: ldr             w16, [x2, #-4]!
    // 0xea694c: str             w16, [x1, #-4]!
    // 0xea6950: tbz             w5, #2, #0xea695c
    // 0xea6954: ldrh            w16, [x2, #-2]!
    // 0xea6958: strh            w16, [x1, #-2]!
    // 0xea695c: tbz             w5, #1, #0xea6968
    // 0xea6960: ldrb            w16, [x2, #-1]!
    // 0xea6964: strb            w16, [x1, #-1]!
    // 0xea6968: ands            w5, w5, #0xffffffe1
    // 0xea696c: b.eq            #0xea69cc
    // 0xea6970: ldp             x16, x17, [x2, #-0x10]!
    // 0xea6974: stp             x16, x17, [x1, #-0x10]!
    // 0xea6978: subs            w5, w5, #0x20
    // 0xea697c: b.ne            #0xea6970
    // 0xea6980: b               #0xea69cc
    // 0xea6984: tbz             w5, #4, #0xea6990
    // 0xea6988: ldr             x16, [x2], #8
    // 0xea698c: str             x16, [x1], #8
    // 0xea6990: tbz             w5, #3, #0xea699c
    // 0xea6994: ldr             w16, [x2], #4
    // 0xea6998: str             w16, [x1], #4
    // 0xea699c: tbz             w5, #2, #0xea69a8
    // 0xea69a0: ldrh            w16, [x2], #2
    // 0xea69a4: strh            w16, [x1], #2
    // 0xea69a8: tbz             w5, #1, #0xea69b4
    // 0xea69ac: ldrb            w16, [x2], #1
    // 0xea69b0: strb            w16, [x1], #1
    // 0xea69b4: ands            w5, w5, #0xffffffe1
    // 0xea69b8: b.eq            #0xea69cc
    // 0xea69bc: ldp             x16, x17, [x2], #0x10
    // 0xea69c0: stp             x16, x17, [x1], #0x10
    // 0xea69c4: subs            w5, w5, #0x20
    // 0xea69c8: b.ne            #0xea69bc
    // 0xea69cc: b               #0xea6a2c
    // 0xea69d0: ldur            x6, [fp, #-0x60]
    // 0xea69d4: LoadField: r0 = r6->field_7
    //     0xea69d4: ldur            x0, [x6, #7]
    // 0xea69d8: add             x1, x0, x2
    // 0xea69dc: LoadField: r0 = r3->field_7
    //     0xea69dc: ldur            x0, [x3, #7]
    // 0xea69e0: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea69e0: mov             x2, THR
    //     0xea69e4: ldr             x9, [x2, #0x658]
    //     0xea69e8: mov             x16, x0
    //     0xea69ec: mov             x0, x1
    //     0xea69f0: mov             x1, x16
    //     0xea69f4: mov             x2, x4
    //     0xea69f8: mov             x17, fp
    //     0xea69fc: str             fp, [SP, #-8]!
    //     0xea6a00: mov             fp, SP
    //     0xea6a04: and             SP, SP, #0xfffffffffffffff0
    //     0xea6a08: mov             x19, sp
    //     0xea6a0c: mov             sp, SP
    //     0xea6a10: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea6a14: blr             x9
    //     0xea6a18: movz            x16, #0x8
    //     0xea6a1c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea6a20: mov             sp, x19
    //     0xea6a24: mov             SP, fp
    //     0xea6a28: ldr             fp, [SP], #8
    // 0xea6a2c: ldur            x0, [fp, #-0x20]
    // 0xea6a30: LoadField: r2 = r0->field_1b
    //     0xea6a30: ldur            w2, [x0, #0x1b]
    // 0xea6a34: DecompressPointer r2
    //     0xea6a34: add             x2, x2, HEAP, lsl #32
    // 0xea6a38: ldur            x7, [fp, #-0x28]
    // 0xea6a3c: ldur            x6, [fp, #-0x30]
    // 0xea6a40: ldur            x5, [fp, #-0x38]
    // 0xea6a44: ldur            x4, [fp, #-0x40]
    // 0xea6a48: ldur            x3, [fp, #-0x18]
    // 0xea6a4c: r8 = 0
    //     0xea6a4c: movz            x8, #0
    // 0xea6a50: CheckStackOverflow
    //     0xea6a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea6a54: cmp             SP, x16
    //     0xea6a58: b.ls            #0xea6b68
    // 0xea6a5c: cmp             x8, x3
    // 0xea6a60: b.ge            #0xea6ad4
    // 0xea6a64: add             x9, x4, x8
    // 0xea6a68: cmp             w2, NULL
    // 0xea6a6c: b.eq            #0xea6b70
    // 0xea6a70: LoadField: r0 = r2->field_13
    //     0xea6a70: ldur            w0, [x2, #0x13]
    // 0xea6a74: r1 = LoadInt32Instr(r0)
    //     0xea6a74: sbfx            x1, x0, #1, #0x1f
    // 0xea6a78: mov             x0, x1
    // 0xea6a7c: mov             x1, x8
    // 0xea6a80: cmp             x1, x0
    // 0xea6a84: b.hs            #0xea6b74
    // 0xea6a88: ArrayLoad: r10 = r2[r8]  ; List_1
    //     0xea6a88: add             x16, x2, x8
    //     0xea6a8c: ldrb            w10, [x16, #0x17]
    // 0xea6a90: add             x11, x6, x8
    // 0xea6a94: ldur            x0, [fp, #-0x10]
    // 0xea6a98: mov             x1, x11
    // 0xea6a9c: cmp             x1, x0
    // 0xea6aa0: b.hs            #0xea6b78
    // 0xea6aa4: ArrayLoad: r0 = r7[r11]  ; List_1
    //     0xea6aa4: add             x16, x7, x11
    //     0xea6aa8: ldrb            w0, [x16, #0x17]
    // 0xea6aac: eor             x11, x10, x0
    // 0xea6ab0: ldur            x0, [fp, #-8]
    // 0xea6ab4: mov             x1, x9
    // 0xea6ab8: cmp             x1, x0
    // 0xea6abc: b.hs            #0xea6b7c
    // 0xea6ac0: ArrayStore: r5[r9] = r11  ; TypeUnknown_1
    //     0xea6ac0: add             x0, x5, x9
    //     0xea6ac4: strb            w11, [x0, #0x17]
    // 0xea6ac8: add             x0, x8, #1
    // 0xea6acc: mov             x8, x0
    // 0xea6ad0: b               #0xea6a50
    // 0xea6ad4: mov             x0, x3
    // 0xea6ad8: LeaveFrame
    //     0xea6ad8: mov             SP, fp
    //     0xea6adc: ldp             fp, lr, [SP], #0x10
    // 0xea6ae0: ret
    //     0xea6ae0: ret             
    // 0xea6ae4: r0 = ArgumentError()
    //     0xea6ae4: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea6ae8: mov             x1, x0
    // 0xea6aec: r0 = "Input buffer too short"
    //     0xea6aec: add             x0, PP, #0x23, lsl #12  ; [pp+0x23678] "Input buffer too short"
    //     0xea6af0: ldr             x0, [x0, #0x678]
    // 0xea6af4: ArrayStore: r1[0] = r0  ; List_4
    //     0xea6af4: stur            w0, [x1, #0x17]
    // 0xea6af8: r0 = false
    //     0xea6af8: add             x0, NULL, #0x30  ; false
    // 0xea6afc: StoreField: r1->field_b = r0
    //     0xea6afc: stur            w0, [x1, #0xb]
    // 0xea6b00: mov             x0, x1
    // 0xea6b04: r0 = Throw()
    //     0xea6b04: bl              #0xec04b8  ; ThrowStub
    // 0xea6b08: brk             #0
    // 0xea6b0c: r0 = false
    //     0xea6b0c: add             x0, NULL, #0x30  ; false
    // 0xea6b10: r0 = ArgumentError()
    //     0xea6b10: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea6b14: mov             x1, x0
    // 0xea6b18: r0 = "Output buffer too short"
    //     0xea6b18: add             x0, PP, #0x23, lsl #12  ; [pp+0x23680] "Output buffer too short"
    //     0xea6b1c: ldr             x0, [x0, #0x680]
    // 0xea6b20: ArrayStore: r1[0] = r0  ; List_4
    //     0xea6b20: stur            w0, [x1, #0x17]
    // 0xea6b24: r0 = false
    //     0xea6b24: add             x0, NULL, #0x30  ; false
    // 0xea6b28: StoreField: r1->field_b = r0
    //     0xea6b28: stur            w0, [x1, #0xb]
    // 0xea6b2c: mov             x0, x1
    // 0xea6b30: r0 = Throw()
    //     0xea6b30: bl              #0xec04b8  ; ThrowStub
    // 0xea6b34: brk             #0
    // 0xea6b38: r0 = tooFew()
    //     0xea6b38: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea6b3c: r0 = Throw()
    //     0xea6b3c: bl              #0xec04b8  ; ThrowStub
    // 0xea6b40: brk             #0
    // 0xea6b44: r0 = tooFew()
    //     0xea6b44: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea6b48: r0 = Throw()
    //     0xea6b48: bl              #0xec04b8  ; ThrowStub
    // 0xea6b4c: brk             #0
    // 0xea6b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea6b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea6b54: b               #0xea65e4
    // 0xea6b58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6b58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea6b5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6b5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea6b60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6b60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea6b64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6b64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea6b68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea6b68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea6b6c: b               #0xea6a5c
    // 0xea6b70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea6b70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea6b74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea6b74: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea6b78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea6b78: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea6b7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea6b7c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _encryptBlock(/* No info */) {
    // ** addr: 0xea6b80, size: 0x5d4
    // 0xea6b80: EnterFrame
    //     0xea6b80: stp             fp, lr, [SP, #-0x10]!
    //     0xea6b84: mov             fp, SP
    // 0xea6b88: AllocStack(0x48)
    //     0xea6b88: sub             SP, SP, #0x48
    // 0xea6b8c: SetupParameters(CFBBlockCipher this /* r1 => r10, fp-0x20 */, dynamic _ /* r2 => r9, fp-0x28 */, dynamic _ /* r3 => r8, fp-0x30 */, dynamic _ /* r5 => r7, fp-0x38 */, dynamic _ /* r6 => r4, fp-0x40 */)
    //     0xea6b8c: mov             x10, x1
    //     0xea6b90: mov             x9, x2
    //     0xea6b94: mov             x8, x3
    //     0xea6b98: mov             x7, x5
    //     0xea6b9c: mov             x4, x6
    //     0xea6ba0: stur            x1, [fp, #-0x20]
    //     0xea6ba4: stur            x2, [fp, #-0x28]
    //     0xea6ba8: stur            x3, [fp, #-0x30]
    //     0xea6bac: stur            x5, [fp, #-0x38]
    //     0xea6bb0: stur            x6, [fp, #-0x40]
    // 0xea6bb4: CheckStackOverflow
    //     0xea6bb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea6bb8: cmp             SP, x16
    //     0xea6bbc: b.ls            #0xea7124
    // 0xea6bc0: LoadField: r11 = r10->field_7
    //     0xea6bc0: ldur            x11, [x10, #7]
    // 0xea6bc4: stur            x11, [fp, #-0x18]
    // 0xea6bc8: add             x0, x8, x11
    // 0xea6bcc: LoadField: r1 = r9->field_13
    //     0xea6bcc: ldur            w1, [x9, #0x13]
    // 0xea6bd0: r12 = LoadInt32Instr(r1)
    //     0xea6bd0: sbfx            x12, x1, #1, #0x1f
    // 0xea6bd4: stur            x12, [fp, #-0x10]
    // 0xea6bd8: cmp             x0, x12
    // 0xea6bdc: b.gt            #0xea70b8
    // 0xea6be0: add             x0, x4, x11
    // 0xea6be4: LoadField: r1 = r7->field_13
    //     0xea6be4: ldur            w1, [x7, #0x13]
    // 0xea6be8: r13 = LoadInt32Instr(r1)
    //     0xea6be8: sbfx            x13, x1, #1, #0x1f
    // 0xea6bec: stur            x13, [fp, #-8]
    // 0xea6bf0: cmp             x0, x13
    // 0xea6bf4: b.gt            #0xea70e0
    // 0xea6bf8: LoadField: r1 = r10->field_f
    //     0xea6bf8: ldur            w1, [x10, #0xf]
    // 0xea6bfc: DecompressPointer r1
    //     0xea6bfc: add             x1, x1, HEAP, lsl #32
    // 0xea6c00: ArrayLoad: r2 = r10[0]  ; List_4
    //     0xea6c00: ldur            w2, [x10, #0x17]
    // 0xea6c04: DecompressPointer r2
    //     0xea6c04: add             x2, x2, HEAP, lsl #32
    // 0xea6c08: cmp             w2, NULL
    // 0xea6c0c: b.eq            #0xea712c
    // 0xea6c10: LoadField: r5 = r10->field_1b
    //     0xea6c10: ldur            w5, [x10, #0x1b]
    // 0xea6c14: DecompressPointer r5
    //     0xea6c14: add             x5, x5, HEAP, lsl #32
    // 0xea6c18: cmp             w5, NULL
    // 0xea6c1c: b.eq            #0xea7130
    // 0xea6c20: r0 = LoadClassIdInstr(r1)
    //     0xea6c20: ldur            x0, [x1, #-1]
    //     0xea6c24: ubfx            x0, x0, #0xc, #0x14
    // 0xea6c28: r3 = 0
    //     0xea6c28: movz            x3, #0
    // 0xea6c2c: r6 = 0
    //     0xea6c2c: movz            x6, #0
    // 0xea6c30: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea6c30: sub             lr, x0, #0xf69
    //     0xea6c34: ldr             lr, [x21, lr, lsl #3]
    //     0xea6c38: blr             lr
    // 0xea6c3c: ldur            x3, [fp, #-0x20]
    // 0xea6c40: LoadField: r2 = r3->field_1b
    //     0xea6c40: ldur            w2, [x3, #0x1b]
    // 0xea6c44: DecompressPointer r2
    //     0xea6c44: add             x2, x2, HEAP, lsl #32
    // 0xea6c48: ldur            x7, [fp, #-0x28]
    // 0xea6c4c: ldur            x6, [fp, #-0x30]
    // 0xea6c50: ldur            x5, [fp, #-0x38]
    // 0xea6c54: ldur            x4, [fp, #-0x40]
    // 0xea6c58: ldur            x8, [fp, #-0x18]
    // 0xea6c5c: r9 = 0
    //     0xea6c5c: movz            x9, #0
    // 0xea6c60: CheckStackOverflow
    //     0xea6c60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea6c64: cmp             SP, x16
    //     0xea6c68: b.ls            #0xea7134
    // 0xea6c6c: cmp             x9, x8
    // 0xea6c70: b.ge            #0xea6ce4
    // 0xea6c74: add             x10, x4, x9
    // 0xea6c78: cmp             w2, NULL
    // 0xea6c7c: b.eq            #0xea713c
    // 0xea6c80: LoadField: r0 = r2->field_13
    //     0xea6c80: ldur            w0, [x2, #0x13]
    // 0xea6c84: r1 = LoadInt32Instr(r0)
    //     0xea6c84: sbfx            x1, x0, #1, #0x1f
    // 0xea6c88: mov             x0, x1
    // 0xea6c8c: mov             x1, x9
    // 0xea6c90: cmp             x1, x0
    // 0xea6c94: b.hs            #0xea7140
    // 0xea6c98: ArrayLoad: r11 = r2[r9]  ; List_1
    //     0xea6c98: add             x16, x2, x9
    //     0xea6c9c: ldrb            w11, [x16, #0x17]
    // 0xea6ca0: add             x12, x6, x9
    // 0xea6ca4: ldur            x0, [fp, #-0x10]
    // 0xea6ca8: mov             x1, x12
    // 0xea6cac: cmp             x1, x0
    // 0xea6cb0: b.hs            #0xea7144
    // 0xea6cb4: ArrayLoad: r0 = r7[r12]  ; List_1
    //     0xea6cb4: add             x16, x7, x12
    //     0xea6cb8: ldrb            w0, [x16, #0x17]
    // 0xea6cbc: eor             x12, x11, x0
    // 0xea6cc0: ldur            x0, [fp, #-8]
    // 0xea6cc4: mov             x1, x10
    // 0xea6cc8: cmp             x1, x0
    // 0xea6ccc: b.hs            #0xea7148
    // 0xea6cd0: ArrayStore: r5[r10] = r12  ; TypeUnknown_1
    //     0xea6cd0: add             x0, x5, x10
    //     0xea6cd4: strb            w12, [x0, #0x17]
    // 0xea6cd8: add             x0, x9, #1
    // 0xea6cdc: mov             x9, x0
    // 0xea6ce0: b               #0xea6c60
    // 0xea6ce4: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xea6ce4: ldur            w0, [x3, #0x17]
    // 0xea6ce8: DecompressPointer r0
    //     0xea6ce8: add             x0, x0, HEAP, lsl #32
    // 0xea6cec: stur            x0, [fp, #-0x28]
    // 0xea6cf0: cmp             w0, NULL
    // 0xea6cf4: b.eq            #0xea714c
    // 0xea6cf8: LoadField: r1 = r0->field_13
    //     0xea6cf8: ldur            w1, [x0, #0x13]
    // 0xea6cfc: r6 = LoadInt32Instr(r1)
    //     0xea6cfc: sbfx            x6, x1, #1, #0x1f
    // 0xea6d00: stur            x6, [fp, #-0x10]
    // 0xea6d04: sub             x7, x6, x8
    // 0xea6d08: mov             x1, x0
    // 0xea6d0c: mov             x2, x8
    // 0xea6d10: stur            x7, [fp, #-8]
    // 0xea6d14: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xea6d14: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xea6d18: r0 = sublist()
    //     0xea6d18: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xea6d1c: mov             x5, x0
    // 0xea6d20: ldur            x4, [fp, #-8]
    // 0xea6d24: stur            x5, [fp, #-0x48]
    // 0xea6d28: tbz             x4, #0x3f, #0xea6d34
    // 0xea6d2c: ldur            x3, [fp, #-0x10]
    // 0xea6d30: b               #0xea6d40
    // 0xea6d34: ldur            x3, [fp, #-0x10]
    // 0xea6d38: cmp             x4, x3
    // 0xea6d3c: b.le            #0xea6d64
    // 0xea6d40: r0 = BoxInt64Instr(r4)
    //     0xea6d40: sbfiz           x0, x4, #1, #0x1f
    //     0xea6d44: cmp             x4, x0, asr #1
    //     0xea6d48: b.eq            #0xea6d54
    //     0xea6d4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea6d50: stur            x4, [x0, #7]
    // 0xea6d54: mov             x2, x0
    // 0xea6d58: r1 = 0
    //     0xea6d58: movz            x1, #0
    // 0xea6d5c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea6d5c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea6d60: r0 = checkValidRange()
    //     0xea6d60: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea6d64: ldur            x20, [fp, #-8]
    // 0xea6d68: ldur            x2, [fp, #-0x48]
    // 0xea6d6c: LoadField: r0 = r2->field_13
    //     0xea6d6c: ldur            w0, [x2, #0x13]
    // 0xea6d70: r1 = LoadInt32Instr(r0)
    //     0xea6d70: sbfx            x1, x0, #1, #0x1f
    // 0xea6d74: cmp             x1, x20
    // 0xea6d78: b.lt            #0xea710c
    // 0xea6d7c: cbz             x20, #0xea6eb4
    // 0xea6d80: r0 = BoxInt64Instr(r20)
    //     0xea6d80: sbfiz           x0, x20, #1, #0x1f
    //     0xea6d84: cmp             x20, x0, asr #1
    //     0xea6d88: b.eq            #0xea6d94
    //     0xea6d8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea6d90: stur            x20, [x0, #7]
    // 0xea6d94: cmp             w0, #0x800
    // 0xea6d98: b.ge            #0xea6e68
    // 0xea6d9c: ldur            x1, [fp, #-0x28]
    // 0xea6da0: mov             x4, x0
    // 0xea6da4: add             x3, x2, #0x17
    // 0xea6da8: add             x0, x1, #0x17
    // 0xea6dac: cbz             x4, #0xea6e64
    // 0xea6db0: cmp             x0, x3
    // 0xea6db4: b.ls            #0xea6e1c
    // 0xea6db8: sxtw            x4, w4
    // 0xea6dbc: add             x16, x3, x4, asr #1
    // 0xea6dc0: cmp             x0, x16
    // 0xea6dc4: b.hs            #0xea6e1c
    // 0xea6dc8: mov             x3, x16
    // 0xea6dcc: add             x0, x0, x4, asr #1
    // 0xea6dd0: tbz             w4, #4, #0xea6ddc
    // 0xea6dd4: ldr             x16, [x3, #-8]!
    // 0xea6dd8: str             x16, [x0, #-8]!
    // 0xea6ddc: tbz             w4, #3, #0xea6de8
    // 0xea6de0: ldr             w16, [x3, #-4]!
    // 0xea6de4: str             w16, [x0, #-4]!
    // 0xea6de8: tbz             w4, #2, #0xea6df4
    // 0xea6dec: ldrh            w16, [x3, #-2]!
    // 0xea6df0: strh            w16, [x0, #-2]!
    // 0xea6df4: tbz             w4, #1, #0xea6e00
    // 0xea6df8: ldrb            w16, [x3, #-1]!
    // 0xea6dfc: strb            w16, [x0, #-1]!
    // 0xea6e00: ands            w4, w4, #0xffffffe1
    // 0xea6e04: b.eq            #0xea6e64
    // 0xea6e08: ldp             x16, x17, [x3, #-0x10]!
    // 0xea6e0c: stp             x16, x17, [x0, #-0x10]!
    // 0xea6e10: subs            w4, w4, #0x20
    // 0xea6e14: b.ne            #0xea6e08
    // 0xea6e18: b               #0xea6e64
    // 0xea6e1c: tbz             w4, #4, #0xea6e28
    // 0xea6e20: ldr             x16, [x3], #8
    // 0xea6e24: str             x16, [x0], #8
    // 0xea6e28: tbz             w4, #3, #0xea6e34
    // 0xea6e2c: ldr             w16, [x3], #4
    // 0xea6e30: str             w16, [x0], #4
    // 0xea6e34: tbz             w4, #2, #0xea6e40
    // 0xea6e38: ldrh            w16, [x3], #2
    // 0xea6e3c: strh            w16, [x0], #2
    // 0xea6e40: tbz             w4, #1, #0xea6e4c
    // 0xea6e44: ldrb            w16, [x3], #1
    // 0xea6e48: strb            w16, [x0], #1
    // 0xea6e4c: ands            w4, w4, #0xffffffe1
    // 0xea6e50: b.eq            #0xea6e64
    // 0xea6e54: ldp             x16, x17, [x3], #0x10
    // 0xea6e58: stp             x16, x17, [x0], #0x10
    // 0xea6e5c: subs            w4, w4, #0x20
    // 0xea6e60: b.ne            #0xea6e54
    // 0xea6e64: b               #0xea6eb4
    // 0xea6e68: ldur            x1, [fp, #-0x28]
    // 0xea6e6c: LoadField: r0 = r1->field_7
    //     0xea6e6c: ldur            x0, [x1, #7]
    // 0xea6e70: LoadField: r1 = r2->field_7
    //     0xea6e70: ldur            x1, [x2, #7]
    // 0xea6e74: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea6e74: mov             x2, THR
    //     0xea6e78: ldr             x9, [x2, #0x658]
    //     0xea6e7c: mov             x2, x20
    //     0xea6e80: mov             x17, fp
    //     0xea6e84: str             fp, [SP, #-8]!
    //     0xea6e88: mov             fp, SP
    //     0xea6e8c: and             SP, SP, #0xfffffffffffffff0
    //     0xea6e90: mov             x19, sp
    //     0xea6e94: mov             sp, SP
    //     0xea6e98: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea6e9c: blr             x9
    //     0xea6ea0: movz            x16, #0x8
    //     0xea6ea4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea6ea8: mov             sp, x19
    //     0xea6eac: mov             SP, fp
    //     0xea6eb0: ldr             fp, [SP], #8
    // 0xea6eb4: ldur            x0, [fp, #-0x20]
    // 0xea6eb8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xea6eb8: ldur            w3, [x0, #0x17]
    // 0xea6ebc: DecompressPointer r3
    //     0xea6ebc: add             x3, x3, HEAP, lsl #32
    // 0xea6ec0: stur            x3, [fp, #-0x28]
    // 0xea6ec4: cmp             w3, NULL
    // 0xea6ec8: b.eq            #0xea7150
    // 0xea6ecc: LoadField: r0 = r3->field_13
    //     0xea6ecc: ldur            w0, [x3, #0x13]
    // 0xea6ed0: ldur            x1, [fp, #-0x38]
    // 0xea6ed4: ldur            x2, [fp, #-0x40]
    // 0xea6ed8: stur            x0, [fp, #-0x20]
    // 0xea6edc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xea6edc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xea6ee0: r0 = sublist()
    //     0xea6ee0: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xea6ee4: mov             x4, x0
    // 0xea6ee8: ldur            x0, [fp, #-8]
    // 0xea6eec: stur            x4, [fp, #-0x38]
    // 0xea6ef0: tbz             x0, #0x3f, #0xea6efc
    // 0xea6ef4: ldur            x5, [fp, #-0x20]
    // 0xea6ef8: b               #0xea6f0c
    // 0xea6efc: ldur            x5, [fp, #-0x20]
    // 0xea6f00: r1 = LoadInt32Instr(r5)
    //     0xea6f00: sbfx            x1, x5, #1, #0x1f
    // 0xea6f04: cmp             x0, x1
    // 0xea6f08: b.le            #0xea6f20
    // 0xea6f0c: r3 = LoadInt32Instr(r5)
    //     0xea6f0c: sbfx            x3, x5, #1, #0x1f
    // 0xea6f10: mov             x1, x0
    // 0xea6f14: mov             x2, x5
    // 0xea6f18: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea6f18: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea6f1c: r0 = checkValidRange()
    //     0xea6f1c: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea6f20: ldur            x2, [fp, #-8]
    // 0xea6f24: ldur            x3, [fp, #-0x38]
    // 0xea6f28: ldur            x0, [fp, #-0x20]
    // 0xea6f2c: r1 = LoadInt32Instr(r0)
    //     0xea6f2c: sbfx            x1, x0, #1, #0x1f
    // 0xea6f30: sub             x4, x1, x2
    // 0xea6f34: LoadField: r0 = r3->field_13
    //     0xea6f34: ldur            w0, [x3, #0x13]
    // 0xea6f38: r1 = LoadInt32Instr(r0)
    //     0xea6f38: sbfx            x1, x0, #1, #0x1f
    // 0xea6f3c: cmp             x1, x4
    // 0xea6f40: b.lt            #0xea7118
    // 0xea6f44: cbz             x4, #0xea70a8
    // 0xea6f48: r0 = BoxInt64Instr(r4)
    //     0xea6f48: sbfiz           x0, x4, #1, #0x1f
    //     0xea6f4c: cmp             x4, x0, asr #1
    //     0xea6f50: b.eq            #0xea6f5c
    //     0xea6f54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea6f58: stur            x4, [x0, #7]
    // 0xea6f5c: mov             x5, x0
    // 0xea6f60: cmp             w5, #0x800
    // 0xea6f64: b.ge            #0xea704c
    // 0xea6f68: ldur            x6, [fp, #-0x28]
    // 0xea6f6c: r0 = BoxInt64Instr(r2)
    //     0xea6f6c: sbfiz           x0, x2, #1, #0x1f
    //     0xea6f70: cmp             x2, x0, asr #1
    //     0xea6f74: b.eq            #0xea6f80
    //     0xea6f78: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea6f7c: stur            x2, [x0, #7]
    // 0xea6f80: add             x2, x3, #0x17
    // 0xea6f84: sxtw            x0, w0
    // 0xea6f88: add             x1, x6, x0, asr #1
    // 0xea6f8c: add             x1, x1, #0x17
    // 0xea6f90: cbz             x5, #0xea7048
    // 0xea6f94: cmp             x1, x2
    // 0xea6f98: b.ls            #0xea7000
    // 0xea6f9c: sxtw            x5, w5
    // 0xea6fa0: add             x16, x2, x5, asr #1
    // 0xea6fa4: cmp             x1, x16
    // 0xea6fa8: b.hs            #0xea7000
    // 0xea6fac: mov             x2, x16
    // 0xea6fb0: add             x1, x1, x5, asr #1
    // 0xea6fb4: tbz             w5, #4, #0xea6fc0
    // 0xea6fb8: ldr             x16, [x2, #-8]!
    // 0xea6fbc: str             x16, [x1, #-8]!
    // 0xea6fc0: tbz             w5, #3, #0xea6fcc
    // 0xea6fc4: ldr             w16, [x2, #-4]!
    // 0xea6fc8: str             w16, [x1, #-4]!
    // 0xea6fcc: tbz             w5, #2, #0xea6fd8
    // 0xea6fd0: ldrh            w16, [x2, #-2]!
    // 0xea6fd4: strh            w16, [x1, #-2]!
    // 0xea6fd8: tbz             w5, #1, #0xea6fe4
    // 0xea6fdc: ldrb            w16, [x2, #-1]!
    // 0xea6fe0: strb            w16, [x1, #-1]!
    // 0xea6fe4: ands            w5, w5, #0xffffffe1
    // 0xea6fe8: b.eq            #0xea7048
    // 0xea6fec: ldp             x16, x17, [x2, #-0x10]!
    // 0xea6ff0: stp             x16, x17, [x1, #-0x10]!
    // 0xea6ff4: subs            w5, w5, #0x20
    // 0xea6ff8: b.ne            #0xea6fec
    // 0xea6ffc: b               #0xea7048
    // 0xea7000: tbz             w5, #4, #0xea700c
    // 0xea7004: ldr             x16, [x2], #8
    // 0xea7008: str             x16, [x1], #8
    // 0xea700c: tbz             w5, #3, #0xea7018
    // 0xea7010: ldr             w16, [x2], #4
    // 0xea7014: str             w16, [x1], #4
    // 0xea7018: tbz             w5, #2, #0xea7024
    // 0xea701c: ldrh            w16, [x2], #2
    // 0xea7020: strh            w16, [x1], #2
    // 0xea7024: tbz             w5, #1, #0xea7030
    // 0xea7028: ldrb            w16, [x2], #1
    // 0xea702c: strb            w16, [x1], #1
    // 0xea7030: ands            w5, w5, #0xffffffe1
    // 0xea7034: b.eq            #0xea7048
    // 0xea7038: ldp             x16, x17, [x2], #0x10
    // 0xea703c: stp             x16, x17, [x1], #0x10
    // 0xea7040: subs            w5, w5, #0x20
    // 0xea7044: b.ne            #0xea7038
    // 0xea7048: b               #0xea70a8
    // 0xea704c: ldur            x6, [fp, #-0x28]
    // 0xea7050: LoadField: r0 = r6->field_7
    //     0xea7050: ldur            x0, [x6, #7]
    // 0xea7054: add             x1, x0, x2
    // 0xea7058: LoadField: r0 = r3->field_7
    //     0xea7058: ldur            x0, [x3, #7]
    // 0xea705c: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea705c: mov             x2, THR
    //     0xea7060: ldr             x9, [x2, #0x658]
    //     0xea7064: mov             x16, x0
    //     0xea7068: mov             x0, x1
    //     0xea706c: mov             x1, x16
    //     0xea7070: mov             x2, x4
    //     0xea7074: mov             x17, fp
    //     0xea7078: str             fp, [SP, #-8]!
    //     0xea707c: mov             fp, SP
    //     0xea7080: and             SP, SP, #0xfffffffffffffff0
    //     0xea7084: mov             x19, sp
    //     0xea7088: mov             sp, SP
    //     0xea708c: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea7090: blr             x9
    //     0xea7094: movz            x16, #0x8
    //     0xea7098: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea709c: mov             sp, x19
    //     0xea70a0: mov             SP, fp
    //     0xea70a4: ldr             fp, [SP], #8
    // 0xea70a8: ldur            x0, [fp, #-0x18]
    // 0xea70ac: LeaveFrame
    //     0xea70ac: mov             SP, fp
    //     0xea70b0: ldp             fp, lr, [SP], #0x10
    // 0xea70b4: ret
    //     0xea70b4: ret             
    // 0xea70b8: r0 = ArgumentError()
    //     0xea70b8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea70bc: mov             x1, x0
    // 0xea70c0: r0 = "Input buffer too short"
    //     0xea70c0: add             x0, PP, #0x23, lsl #12  ; [pp+0x23678] "Input buffer too short"
    //     0xea70c4: ldr             x0, [x0, #0x678]
    // 0xea70c8: ArrayStore: r1[0] = r0  ; List_4
    //     0xea70c8: stur            w0, [x1, #0x17]
    // 0xea70cc: r0 = false
    //     0xea70cc: add             x0, NULL, #0x30  ; false
    // 0xea70d0: StoreField: r1->field_b = r0
    //     0xea70d0: stur            w0, [x1, #0xb]
    // 0xea70d4: mov             x0, x1
    // 0xea70d8: r0 = Throw()
    //     0xea70d8: bl              #0xec04b8  ; ThrowStub
    // 0xea70dc: brk             #0
    // 0xea70e0: r0 = false
    //     0xea70e0: add             x0, NULL, #0x30  ; false
    // 0xea70e4: r0 = ArgumentError()
    //     0xea70e4: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea70e8: mov             x1, x0
    // 0xea70ec: r0 = "Output buffer too short"
    //     0xea70ec: add             x0, PP, #0x23, lsl #12  ; [pp+0x23680] "Output buffer too short"
    //     0xea70f0: ldr             x0, [x0, #0x680]
    // 0xea70f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xea70f4: stur            w0, [x1, #0x17]
    // 0xea70f8: r0 = false
    //     0xea70f8: add             x0, NULL, #0x30  ; false
    // 0xea70fc: StoreField: r1->field_b = r0
    //     0xea70fc: stur            w0, [x1, #0xb]
    // 0xea7100: mov             x0, x1
    // 0xea7104: r0 = Throw()
    //     0xea7104: bl              #0xec04b8  ; ThrowStub
    // 0xea7108: brk             #0
    // 0xea710c: r0 = tooFew()
    //     0xea710c: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea7110: r0 = Throw()
    //     0xea7110: bl              #0xec04b8  ; ThrowStub
    // 0xea7114: brk             #0
    // 0xea7118: r0 = tooFew()
    //     0xea7118: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea711c: r0 = Throw()
    //     0xea711c: bl              #0xec04b8  ; ThrowStub
    // 0xea7120: brk             #0
    // 0xea7124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7124: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7128: b               #0xea6bc0
    // 0xea712c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea712c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7130: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7130: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7134: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7134: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7138: b               #0xea6c6c
    // 0xea713c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea713c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7140: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7140: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7144: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7144: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7148: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7148: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea714c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea714c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7150: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7150: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
