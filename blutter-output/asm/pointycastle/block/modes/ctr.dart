// lib: impl.block_cipher.modes.ctr, url: package:pointycastle/block/modes/ctr.dart

// class id: 1050933, size: 0x8
class :: {
}

// class id: 712, size: 0x14, field offset: 0x14
class CTRBlockCipher extends StreamCipherAsBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xda4

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e6138, size: 0x64
    // 0x8e6138: EnterFrame
    //     0x8e6138: stp             fp, lr, [SP, #-0x10]!
    //     0x8e613c: mov             fp, SP
    // 0x8e6140: AllocStack(0x8)
    //     0x8e6140: sub             SP, SP, #8
    // 0x8e6144: CheckStackOverflow
    //     0x8e6144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6148: cmp             SP, x16
    //     0x8e614c: b.ls            #0x8e6194
    // 0x8e6150: r0 = DynamicFactoryConfig()
    //     0x8e6150: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e6154: r1 = Function '<anonymous closure>': static.
    //     0x8e6154: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b58] AnonymousClosure: static (0x8e619c), in [package:pointycastle/block/modes/ctr.dart] CTRBlockCipher::factoryConfig (0x8e6138)
    //     0x8e6158: ldr             x1, [x1, #0xb58]
    // 0x8e615c: r2 = Null
    //     0x8e615c: mov             x2, NULL
    // 0x8e6160: stur            x0, [fp, #-8]
    // 0x8e6164: r0 = AllocateClosure()
    //     0x8e6164: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6168: ldur            x1, [fp, #-8]
    // 0x8e616c: mov             x5, x0
    // 0x8e6170: r2 = BlockCipher
    //     0x8e6170: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e6174: ldr             x2, [x2, #0xa80]
    // 0x8e6178: r3 = "/CTR"
    //     0x8e6178: add             x3, PP, #0x18, lsl #12  ; [pp+0x18158] "/CTR"
    //     0x8e617c: ldr             x3, [x3, #0x158]
    // 0x8e6180: r0 = DynamicFactoryConfig.suffix()
    //     0x8e6180: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e6184: ldur            x0, [fp, #-8]
    // 0x8e6188: LeaveFrame
    //     0x8e6188: mov             SP, fp
    //     0x8e618c: ldp             fp, lr, [SP], #0x10
    // 0x8e6190: ret
    //     0x8e6190: ret             
    // 0x8e6194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6194: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6198: b               #0x8e6150
  }
  [closure] static (dynamic) => CTRBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e619c, size: 0x54
    // 0x8e619c: EnterFrame
    //     0x8e619c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e61a0: mov             fp, SP
    // 0x8e61a4: AllocStack(0x8)
    //     0x8e61a4: sub             SP, SP, #8
    // 0x8e61a8: SetupParameters()
    //     0x8e61a8: ldr             x0, [fp, #0x20]
    //     0x8e61ac: ldur            w1, [x0, #0x17]
    //     0x8e61b0: add             x1, x1, HEAP, lsl #32
    //     0x8e61b4: stur            x1, [fp, #-8]
    // 0x8e61b8: r1 = 1
    //     0x8e61b8: movz            x1, #0x1
    // 0x8e61bc: r0 = AllocateContext()
    //     0x8e61bc: bl              #0xec126c  ; AllocateContextStub
    // 0x8e61c0: mov             x1, x0
    // 0x8e61c4: ldur            x0, [fp, #-8]
    // 0x8e61c8: StoreField: r1->field_b = r0
    //     0x8e61c8: stur            w0, [x1, #0xb]
    // 0x8e61cc: ldr             x0, [fp, #0x10]
    // 0x8e61d0: StoreField: r1->field_f = r0
    //     0x8e61d0: stur            w0, [x1, #0xf]
    // 0x8e61d4: mov             x2, x1
    // 0x8e61d8: r1 = Function '<anonymous closure>': static.
    //     0x8e61d8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b60] AnonymousClosure: static (0x8e61f0), in [package:pointycastle/block/modes/ctr.dart] CTRBlockCipher::factoryConfig (0x8e6138)
    //     0x8e61dc: ldr             x1, [x1, #0xb60]
    // 0x8e61e0: r0 = AllocateClosure()
    //     0x8e61e0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e61e4: LeaveFrame
    //     0x8e61e4: mov             SP, fp
    //     0x8e61e8: ldp             fp, lr, [SP], #0x10
    // 0x8e61ec: ret
    //     0x8e61ec: ret             
  }
  [closure] static CTRBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e61f0, size: 0xfc
    // 0x8e61f0: EnterFrame
    //     0x8e61f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e61f4: mov             fp, SP
    // 0x8e61f8: AllocStack(0x28)
    //     0x8e61f8: sub             SP, SP, #0x28
    // 0x8e61fc: SetupParameters()
    //     0x8e61fc: ldr             x0, [fp, #0x10]
    //     0x8e6200: ldur            w1, [x0, #0x17]
    //     0x8e6204: add             x1, x1, HEAP, lsl #32
    // 0x8e6208: CheckStackOverflow
    //     0x8e6208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e620c: cmp             SP, x16
    //     0x8e6210: b.ls            #0x8e62e0
    // 0x8e6214: LoadField: r0 = r1->field_f
    //     0x8e6214: ldur            w0, [x1, #0xf]
    // 0x8e6218: DecompressPointer r0
    //     0x8e6218: add             x0, x0, HEAP, lsl #32
    // 0x8e621c: r1 = LoadClassIdInstr(r0)
    //     0x8e621c: ldur            x1, [x0, #-1]
    //     0x8e6220: ubfx            x1, x1, #0xc, #0x14
    // 0x8e6224: mov             x16, x0
    // 0x8e6228: mov             x0, x1
    // 0x8e622c: mov             x1, x16
    // 0x8e6230: r2 = 1
    //     0x8e6230: movz            x2, #0x1
    // 0x8e6234: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e6234: sub             lr, x0, #0xfdd
    //     0x8e6238: ldr             lr, [x21, lr, lsl #3]
    //     0x8e623c: blr             lr
    // 0x8e6240: stur            x0, [fp, #-8]
    // 0x8e6244: cmp             w0, NULL
    // 0x8e6248: b.eq            #0x8e62e8
    // 0x8e624c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e624c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e6250: ldr             x0, [x0, #0x2e38]
    //     0x8e6254: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6258: cmp             w0, w16
    //     0x8e625c: b.ne            #0x8e626c
    //     0x8e6260: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e6264: ldr             x2, [x2, #0xf80]
    //     0x8e6268: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e626c: r16 = <BlockCipher>
    //     0x8e626c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e6270: ldr             x16, [x16, #0x88]
    // 0x8e6274: stp             x0, x16, [SP, #8]
    // 0x8e6278: ldur            x16, [fp, #-8]
    // 0x8e627c: str             x16, [SP]
    // 0x8e6280: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e6280: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e6284: r0 = create()
    //     0x8e6284: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e6288: mov             x2, x0
    // 0x8e628c: stur            x2, [fp, #-8]
    // 0x8e6290: r0 = LoadClassIdInstr(r2)
    //     0x8e6290: ldur            x0, [x2, #-1]
    //     0x8e6294: ubfx            x0, x0, #0xc, #0x14
    // 0x8e6298: mov             x1, x2
    // 0x8e629c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e629c: sub             lr, x0, #1, lsl #12
    //     0x8e62a0: ldr             lr, [x21, lr, lsl #3]
    //     0x8e62a4: blr             lr
    // 0x8e62a8: stur            x0, [fp, #-0x10]
    // 0x8e62ac: r0 = CTRStreamCipher()
    //     0x8e62ac: bl              #0x8c4c88  ; AllocateCTRStreamCipherStub -> CTRStreamCipher (size=0x1c)
    // 0x8e62b0: mov             x1, x0
    // 0x8e62b4: ldur            x2, [fp, #-8]
    // 0x8e62b8: stur            x0, [fp, #-8]
    // 0x8e62bc: r0 = SICStreamCipher()
    //     0x8e62bc: bl              #0x8c4b0c  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::SICStreamCipher
    // 0x8e62c0: r0 = CTRBlockCipher()
    //     0x8e62c0: bl              #0x8e62ec  ; AllocateCTRBlockCipherStub -> CTRBlockCipher (size=0x14)
    // 0x8e62c4: ldur            x1, [fp, #-0x10]
    // 0x8e62c8: StoreField: r0->field_b = r1
    //     0x8e62c8: stur            x1, [x0, #0xb]
    // 0x8e62cc: ldur            x1, [fp, #-8]
    // 0x8e62d0: StoreField: r0->field_7 = r1
    //     0x8e62d0: stur            w1, [x0, #7]
    // 0x8e62d4: LeaveFrame
    //     0x8e62d4: mov             SP, fp
    //     0x8e62d8: ldp             fp, lr, [SP], #0x10
    // 0x8e62dc: ret
    //     0x8e62dc: ret             
    // 0x8e62e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e62e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e62e4: b               #0x8e6214
    // 0x8e62e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e62e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
