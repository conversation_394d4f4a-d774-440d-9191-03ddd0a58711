// lib: impl.block_cipher.modes.ecb, url: package:pointycastle/block/modes/ecb.dart

// class id: 1050934, size: 0x8
class :: {
}

// class id: 706, size: 0xc, field offset: 0x8
class ECBBlockCipher extends BaseBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xda8

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e5fb4, size: 0x64
    // 0x8e5fb4: EnterFrame
    //     0x8e5fb4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5fb8: mov             fp, SP
    // 0x8e5fbc: AllocStack(0x8)
    //     0x8e5fbc: sub             SP, SP, #8
    // 0x8e5fc0: CheckStackOverflow
    //     0x8e5fc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5fc4: cmp             SP, x16
    //     0x8e5fc8: b.ls            #0x8e6010
    // 0x8e5fcc: r0 = DynamicFactoryConfig()
    //     0x8e5fcc: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e5fd0: r1 = Function '<anonymous closure>': static.
    //     0x8e5fd0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b40] AnonymousClosure: static (0x8e6018), in [package:pointycastle/block/modes/ecb.dart] ECBBlockCipher::factoryConfig (0x8e5fb4)
    //     0x8e5fd4: ldr             x1, [x1, #0xb40]
    // 0x8e5fd8: r2 = Null
    //     0x8e5fd8: mov             x2, NULL
    // 0x8e5fdc: stur            x0, [fp, #-8]
    // 0x8e5fe0: r0 = AllocateClosure()
    //     0x8e5fe0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e5fe4: ldur            x1, [fp, #-8]
    // 0x8e5fe8: mov             x5, x0
    // 0x8e5fec: r2 = BlockCipher
    //     0x8e5fec: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e5ff0: ldr             x2, [x2, #0xa80]
    // 0x8e5ff4: r3 = "/ECB"
    //     0x8e5ff4: add             x3, PP, #0x19, lsl #12  ; [pp+0x19b48] "/ECB"
    //     0x8e5ff8: ldr             x3, [x3, #0xb48]
    // 0x8e5ffc: r0 = DynamicFactoryConfig.suffix()
    //     0x8e5ffc: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e6000: ldur            x0, [fp, #-8]
    // 0x8e6004: LeaveFrame
    //     0x8e6004: mov             SP, fp
    //     0x8e6008: ldp             fp, lr, [SP], #0x10
    // 0x8e600c: ret
    //     0x8e600c: ret             
    // 0x8e6010: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6010: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6014: b               #0x8e5fcc
  }
  [closure] static (dynamic) => ECBBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e6018, size: 0x54
    // 0x8e6018: EnterFrame
    //     0x8e6018: stp             fp, lr, [SP, #-0x10]!
    //     0x8e601c: mov             fp, SP
    // 0x8e6020: AllocStack(0x8)
    //     0x8e6020: sub             SP, SP, #8
    // 0x8e6024: SetupParameters()
    //     0x8e6024: ldr             x0, [fp, #0x20]
    //     0x8e6028: ldur            w1, [x0, #0x17]
    //     0x8e602c: add             x1, x1, HEAP, lsl #32
    //     0x8e6030: stur            x1, [fp, #-8]
    // 0x8e6034: r1 = 1
    //     0x8e6034: movz            x1, #0x1
    // 0x8e6038: r0 = AllocateContext()
    //     0x8e6038: bl              #0xec126c  ; AllocateContextStub
    // 0x8e603c: mov             x1, x0
    // 0x8e6040: ldur            x0, [fp, #-8]
    // 0x8e6044: StoreField: r1->field_b = r0
    //     0x8e6044: stur            w0, [x1, #0xb]
    // 0x8e6048: ldr             x0, [fp, #0x10]
    // 0x8e604c: StoreField: r1->field_f = r0
    //     0x8e604c: stur            w0, [x1, #0xf]
    // 0x8e6050: mov             x2, x1
    // 0x8e6054: r1 = Function '<anonymous closure>': static.
    //     0x8e6054: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b50] AnonymousClosure: static (0x8e606c), in [package:pointycastle/block/modes/ecb.dart] ECBBlockCipher::factoryConfig (0x8e5fb4)
    //     0x8e6058: ldr             x1, [x1, #0xb50]
    // 0x8e605c: r0 = AllocateClosure()
    //     0x8e605c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6060: LeaveFrame
    //     0x8e6060: mov             SP, fp
    //     0x8e6064: ldp             fp, lr, [SP], #0x10
    // 0x8e6068: ret
    //     0x8e6068: ret             
  }
  [closure] static ECBBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e606c, size: 0xc0
    // 0x8e606c: EnterFrame
    //     0x8e606c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6070: mov             fp, SP
    // 0x8e6074: AllocStack(0x20)
    //     0x8e6074: sub             SP, SP, #0x20
    // 0x8e6078: SetupParameters()
    //     0x8e6078: ldr             x0, [fp, #0x10]
    //     0x8e607c: ldur            w1, [x0, #0x17]
    //     0x8e6080: add             x1, x1, HEAP, lsl #32
    // 0x8e6084: CheckStackOverflow
    //     0x8e6084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6088: cmp             SP, x16
    //     0x8e608c: b.ls            #0x8e6120
    // 0x8e6090: LoadField: r0 = r1->field_f
    //     0x8e6090: ldur            w0, [x1, #0xf]
    // 0x8e6094: DecompressPointer r0
    //     0x8e6094: add             x0, x0, HEAP, lsl #32
    // 0x8e6098: r1 = LoadClassIdInstr(r0)
    //     0x8e6098: ldur            x1, [x0, #-1]
    //     0x8e609c: ubfx            x1, x1, #0xc, #0x14
    // 0x8e60a0: mov             x16, x0
    // 0x8e60a4: mov             x0, x1
    // 0x8e60a8: mov             x1, x16
    // 0x8e60ac: r2 = 1
    //     0x8e60ac: movz            x2, #0x1
    // 0x8e60b0: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e60b0: sub             lr, x0, #0xfdd
    //     0x8e60b4: ldr             lr, [x21, lr, lsl #3]
    //     0x8e60b8: blr             lr
    // 0x8e60bc: stur            x0, [fp, #-8]
    // 0x8e60c0: cmp             w0, NULL
    // 0x8e60c4: b.eq            #0x8e6128
    // 0x8e60c8: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e60c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e60cc: ldr             x0, [x0, #0x2e38]
    //     0x8e60d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e60d4: cmp             w0, w16
    //     0x8e60d8: b.ne            #0x8e60e8
    //     0x8e60dc: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e60e0: ldr             x2, [x2, #0xf80]
    //     0x8e60e4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e60e8: r16 = <BlockCipher>
    //     0x8e60e8: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e60ec: ldr             x16, [x16, #0x88]
    // 0x8e60f0: stp             x0, x16, [SP, #8]
    // 0x8e60f4: ldur            x16, [fp, #-8]
    // 0x8e60f8: str             x16, [SP]
    // 0x8e60fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e60fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e6100: r0 = create()
    //     0x8e6100: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e6104: stur            x0, [fp, #-8]
    // 0x8e6108: r0 = ECBBlockCipher()
    //     0x8e6108: bl              #0x8e612c  ; AllocateECBBlockCipherStub -> ECBBlockCipher (size=0xc)
    // 0x8e610c: ldur            x1, [fp, #-8]
    // 0x8e6110: StoreField: r0->field_7 = r1
    //     0x8e6110: stur            w1, [x0, #7]
    // 0x8e6114: LeaveFrame
    //     0x8e6114: mov             SP, fp
    //     0x8e6118: ldp             fp, lr, [SP], #0x10
    // 0x8e611c: ret
    //     0x8e611c: ret             
    // 0x8e6120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6124: b               #0x8e6090
    // 0x8e6128: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e6128: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ reset(/* No info */) {
    // ** addr: 0xe7e24c, size: 0x54
    // 0xe7e24c: EnterFrame
    //     0xe7e24c: stp             fp, lr, [SP, #-0x10]!
    //     0xe7e250: mov             fp, SP
    // 0xe7e254: CheckStackOverflow
    //     0xe7e254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7e258: cmp             SP, x16
    //     0xe7e25c: b.ls            #0xe7e298
    // 0xe7e260: LoadField: r0 = r1->field_7
    //     0xe7e260: ldur            w0, [x1, #7]
    // 0xe7e264: DecompressPointer r0
    //     0xe7e264: add             x0, x0, HEAP, lsl #32
    // 0xe7e268: r1 = LoadClassIdInstr(r0)
    //     0xe7e268: ldur            x1, [x0, #-1]
    //     0xe7e26c: ubfx            x1, x1, #0xc, #0x14
    // 0xe7e270: mov             x16, x0
    // 0xe7e274: mov             x0, x1
    // 0xe7e278: mov             x1, x16
    // 0xe7e27c: r0 = GDT[cid_x0 + -0xeaf]()
    //     0xe7e27c: sub             lr, x0, #0xeaf
    //     0xe7e280: ldr             lr, [x21, lr, lsl #3]
    //     0xe7e284: blr             lr
    // 0xe7e288: r0 = Null
    //     0xe7e288: mov             x0, NULL
    // 0xe7e28c: LeaveFrame
    //     0xe7e28c: mov             SP, fp
    //     0xe7e290: ldp             fp, lr, [SP], #0x10
    // 0xe7e294: ret
    //     0xe7e294: ret             
    // 0xe7e298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7e298: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7e29c: b               #0xe7e260
  }
  _ init(/* No info */) {
    // ** addr: 0xe856fc, size: 0x54
    // 0xe856fc: EnterFrame
    //     0xe856fc: stp             fp, lr, [SP, #-0x10]!
    //     0xe85700: mov             fp, SP
    // 0xe85704: CheckStackOverflow
    //     0xe85704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe85708: cmp             SP, x16
    //     0xe8570c: b.ls            #0xe85748
    // 0xe85710: LoadField: r0 = r1->field_7
    //     0xe85710: ldur            w0, [x1, #7]
    // 0xe85714: DecompressPointer r0
    //     0xe85714: add             x0, x0, HEAP, lsl #32
    // 0xe85718: r1 = LoadClassIdInstr(r0)
    //     0xe85718: ldur            x1, [x0, #-1]
    //     0xe8571c: ubfx            x1, x1, #0xc, #0x14
    // 0xe85720: mov             x16, x0
    // 0xe85724: mov             x0, x1
    // 0xe85728: mov             x1, x16
    // 0xe8572c: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe8572c: sub             lr, x0, #0xeda
    //     0xe85730: ldr             lr, [x21, lr, lsl #3]
    //     0xe85734: blr             lr
    // 0xe85738: r0 = Null
    //     0xe85738: mov             x0, NULL
    // 0xe8573c: LeaveFrame
    //     0xe8573c: mov             SP, fp
    //     0xe85740: ldp             fp, lr, [SP], #0x10
    // 0xe85744: ret
    //     0xe85744: ret             
    // 0xe85748: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe85748: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8574c: b               #0xe85710
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xea7154, size: 0x50
    // 0xea7154: EnterFrame
    //     0xea7154: stp             fp, lr, [SP, #-0x10]!
    //     0xea7158: mov             fp, SP
    // 0xea715c: CheckStackOverflow
    //     0xea715c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea7160: cmp             SP, x16
    //     0xea7164: b.ls            #0xea719c
    // 0xea7168: LoadField: r0 = r1->field_7
    //     0xea7168: ldur            w0, [x1, #7]
    // 0xea716c: DecompressPointer r0
    //     0xea716c: add             x0, x0, HEAP, lsl #32
    // 0xea7170: r1 = LoadClassIdInstr(r0)
    //     0xea7170: ldur            x1, [x0, #-1]
    //     0xea7174: ubfx            x1, x1, #0xc, #0x14
    // 0xea7178: mov             x16, x0
    // 0xea717c: mov             x0, x1
    // 0xea7180: mov             x1, x16
    // 0xea7184: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea7184: sub             lr, x0, #0xf69
    //     0xea7188: ldr             lr, [x21, lr, lsl #3]
    //     0xea718c: blr             lr
    // 0xea7190: LeaveFrame
    //     0xea7190: mov             SP, fp
    //     0xea7194: ldp             fp, lr, [SP], #0x10
    // 0xea7198: ret
    //     0xea7198: ret             
    // 0xea719c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea719c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea71a0: b               #0xea7168
  }
}
