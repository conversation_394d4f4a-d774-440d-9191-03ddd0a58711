// lib: impl.block_cipher.modes.gcm, url: package:pointycastle/block/modes/gcm.dart

// class id: 1050935, size: 0x8
class :: {
}

// class id: 670, size: 0x54, field offset: 0x30
class GCMBlockCipher extends BaseAEADBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xdb4
  late Uint8List _x; // offset: 0x40
  late Uint8List _h; // offset: 0x30
  late int _processedBytes; // offset: 0x44
  late Uint8List _e0; // offset: 0x3c
  late Uint8List _e; // offset: 0x38
  late Uint8List _counter; // offset: 0x34

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e5420, size: 0x64
    // 0x8e5420: EnterFrame
    //     0x8e5420: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5424: mov             fp, SP
    // 0x8e5428: AllocStack(0x8)
    //     0x8e5428: sub             SP, SP, #8
    // 0x8e542c: CheckStackOverflow
    //     0x8e542c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5430: cmp             SP, x16
    //     0x8e5434: b.ls            #0x8e547c
    // 0x8e5438: r0 = DynamicFactoryConfig()
    //     0x8e5438: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e543c: r1 = Function '<anonymous closure>': static.
    //     0x8e543c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19ac0] AnonymousClosure: static (0x8e5484), in [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::factoryConfig (0x8e5420)
    //     0x8e5440: ldr             x1, [x1, #0xac0]
    // 0x8e5444: r2 = Null
    //     0x8e5444: mov             x2, NULL
    // 0x8e5448: stur            x0, [fp, #-8]
    // 0x8e544c: r0 = AllocateClosure()
    //     0x8e544c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e5450: ldur            x1, [fp, #-8]
    // 0x8e5454: mov             x5, x0
    // 0x8e5458: r2 = BlockCipher
    //     0x8e5458: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e545c: ldr             x2, [x2, #0xa80]
    // 0x8e5460: r3 = "/GCM"
    //     0x8e5460: add             x3, PP, #0x19, lsl #12  ; [pp+0x19ac8] "/GCM"
    //     0x8e5464: ldr             x3, [x3, #0xac8]
    // 0x8e5468: r0 = DynamicFactoryConfig.suffix()
    //     0x8e5468: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e546c: ldur            x0, [fp, #-8]
    // 0x8e5470: LeaveFrame
    //     0x8e5470: mov             SP, fp
    //     0x8e5474: ldp             fp, lr, [SP], #0x10
    // 0x8e5478: ret
    //     0x8e5478: ret             
    // 0x8e547c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e547c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5480: b               #0x8e5438
  }
  [closure] static (dynamic) => GCMBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e5484, size: 0x54
    // 0x8e5484: EnterFrame
    //     0x8e5484: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5488: mov             fp, SP
    // 0x8e548c: AllocStack(0x8)
    //     0x8e548c: sub             SP, SP, #8
    // 0x8e5490: SetupParameters()
    //     0x8e5490: ldr             x0, [fp, #0x20]
    //     0x8e5494: ldur            w1, [x0, #0x17]
    //     0x8e5498: add             x1, x1, HEAP, lsl #32
    //     0x8e549c: stur            x1, [fp, #-8]
    // 0x8e54a0: r1 = 1
    //     0x8e54a0: movz            x1, #0x1
    // 0x8e54a4: r0 = AllocateContext()
    //     0x8e54a4: bl              #0xec126c  ; AllocateContextStub
    // 0x8e54a8: mov             x1, x0
    // 0x8e54ac: ldur            x0, [fp, #-8]
    // 0x8e54b0: StoreField: r1->field_b = r0
    //     0x8e54b0: stur            w0, [x1, #0xb]
    // 0x8e54b4: ldr             x0, [fp, #0x10]
    // 0x8e54b8: StoreField: r1->field_f = r0
    //     0x8e54b8: stur            w0, [x1, #0xf]
    // 0x8e54bc: mov             x2, x1
    // 0x8e54c0: r1 = Function '<anonymous closure>': static.
    //     0x8e54c0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19ad0] AnonymousClosure: static (0x8e54d8), in [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::factoryConfig (0x8e5420)
    //     0x8e54c4: ldr             x1, [x1, #0xad0]
    // 0x8e54c8: r0 = AllocateClosure()
    //     0x8e54c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e54cc: LeaveFrame
    //     0x8e54cc: mov             SP, fp
    //     0x8e54d0: ldp             fp, lr, [SP], #0x10
    // 0x8e54d4: ret
    //     0x8e54d4: ret             
  }
  [closure] static GCMBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e54d8, size: 0xcc
    // 0x8e54d8: EnterFrame
    //     0x8e54d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e54dc: mov             fp, SP
    // 0x8e54e0: AllocStack(0x20)
    //     0x8e54e0: sub             SP, SP, #0x20
    // 0x8e54e4: SetupParameters()
    //     0x8e54e4: ldr             x0, [fp, #0x10]
    //     0x8e54e8: ldur            w1, [x0, #0x17]
    //     0x8e54ec: add             x1, x1, HEAP, lsl #32
    // 0x8e54f0: CheckStackOverflow
    //     0x8e54f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e54f4: cmp             SP, x16
    //     0x8e54f8: b.ls            #0x8e5598
    // 0x8e54fc: LoadField: r0 = r1->field_f
    //     0x8e54fc: ldur            w0, [x1, #0xf]
    // 0x8e5500: DecompressPointer r0
    //     0x8e5500: add             x0, x0, HEAP, lsl #32
    // 0x8e5504: r1 = LoadClassIdInstr(r0)
    //     0x8e5504: ldur            x1, [x0, #-1]
    //     0x8e5508: ubfx            x1, x1, #0xc, #0x14
    // 0x8e550c: mov             x16, x0
    // 0x8e5510: mov             x0, x1
    // 0x8e5514: mov             x1, x16
    // 0x8e5518: r2 = 1
    //     0x8e5518: movz            x2, #0x1
    // 0x8e551c: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e551c: sub             lr, x0, #0xfdd
    //     0x8e5520: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5524: blr             lr
    // 0x8e5528: stur            x0, [fp, #-8]
    // 0x8e552c: cmp             w0, NULL
    // 0x8e5530: b.eq            #0x8e55a0
    // 0x8e5534: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e5534: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e5538: ldr             x0, [x0, #0x2e38]
    //     0x8e553c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e5540: cmp             w0, w16
    //     0x8e5544: b.ne            #0x8e5554
    //     0x8e5548: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e554c: ldr             x2, [x2, #0xf80]
    //     0x8e5550: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e5554: r16 = <BlockCipher>
    //     0x8e5554: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e5558: ldr             x16, [x16, #0x88]
    // 0x8e555c: stp             x0, x16, [SP, #8]
    // 0x8e5560: ldur            x16, [fp, #-8]
    // 0x8e5564: str             x16, [SP]
    // 0x8e5568: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e5568: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e556c: r0 = create()
    //     0x8e556c: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e5570: stur            x0, [fp, #-8]
    // 0x8e5574: r0 = GCMBlockCipher()
    //     0x8e5574: bl              #0x8e565c  ; AllocateGCMBlockCipherStub -> GCMBlockCipher (size=0x54)
    // 0x8e5578: mov             x1, x0
    // 0x8e557c: ldur            x2, [fp, #-8]
    // 0x8e5580: stur            x0, [fp, #-8]
    // 0x8e5584: r0 = GCMBlockCipher()
    //     0x8e5584: bl              #0x8e55a4  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::GCMBlockCipher
    // 0x8e5588: ldur            x0, [fp, #-8]
    // 0x8e558c: LeaveFrame
    //     0x8e558c: mov             SP, fp
    //     0x8e5590: ldp             fp, lr, [SP], #0x10
    // 0x8e5594: ret
    //     0x8e5594: ret             
    // 0x8e5598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e5598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e559c: b               #0x8e54fc
    // 0x8e55a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e55a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ GCMBlockCipher(/* No info */) {
    // ** addr: 0x8e55a4, size: 0xb8
    // 0x8e55a4: EnterFrame
    //     0x8e55a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e55a8: mov             fp, SP
    // 0x8e55ac: AllocStack(0x10)
    //     0x8e55ac: sub             SP, SP, #0x10
    // 0x8e55b0: r0 = Sentinel
    //     0x8e55b0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e55b4: stur            x1, [fp, #-8]
    // 0x8e55b8: mov             x16, x2
    // 0x8e55bc: mov             x2, x1
    // 0x8e55c0: mov             x1, x16
    // 0x8e55c4: stur            x1, [fp, #-0x10]
    // 0x8e55c8: StoreField: r2->field_2f = r0
    //     0x8e55c8: stur            w0, [x2, #0x2f]
    // 0x8e55cc: StoreField: r2->field_33 = r0
    //     0x8e55cc: stur            w0, [x2, #0x33]
    // 0x8e55d0: StoreField: r2->field_37 = r0
    //     0x8e55d0: stur            w0, [x2, #0x37]
    // 0x8e55d4: StoreField: r2->field_3b = r0
    //     0x8e55d4: stur            w0, [x2, #0x3b]
    // 0x8e55d8: StoreField: r2->field_3f = r0
    //     0x8e55d8: stur            w0, [x2, #0x3f]
    // 0x8e55dc: StoreField: r2->field_43 = r0
    //     0x8e55dc: stur            w0, [x2, #0x43]
    // 0x8e55e0: StoreField: r2->field_47 = rZR
    //     0x8e55e0: stur            xzr, [x2, #0x47]
    // 0x8e55e4: r4 = 32
    //     0x8e55e4: movz            x4, #0x20
    // 0x8e55e8: r0 = AllocateUint8Array()
    //     0x8e55e8: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e55ec: r1 = 225
    //     0x8e55ec: movz            x1, #0xe1
    // 0x8e55f0: ArrayStore: r0[0] = r1  ; TypeUnknown_1
    //     0x8e55f0: strb            w1, [x0, #0x17]
    // 0x8e55f4: ldur            x1, [fp, #-8]
    // 0x8e55f8: StoreField: r1->field_4f = r0
    //     0x8e55f8: stur            w0, [x1, #0x4f]
    //     0x8e55fc: ldurb           w16, [x1, #-1]
    //     0x8e5600: ldurb           w17, [x0, #-1]
    //     0x8e5604: and             x16, x17, x16, lsr #2
    //     0x8e5608: tst             x16, HEAP, lsr #32
    //     0x8e560c: b.eq            #0x8e5614
    //     0x8e5610: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e5614: r2 = Sentinel
    //     0x8e5614: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e5618: StoreField: r1->field_b = r2
    //     0x8e5618: stur            w2, [x1, #0xb]
    // 0x8e561c: StoreField: r1->field_f = r2
    //     0x8e561c: stur            w2, [x1, #0xf]
    // 0x8e5620: ArrayStore: r1[0] = r2  ; List_4
    //     0x8e5620: stur            w2, [x1, #0x17]
    // 0x8e5624: StoreField: r1->field_1b = r2
    //     0x8e5624: stur            w2, [x1, #0x1b]
    // 0x8e5628: StoreField: r1->field_2b = r2
    //     0x8e5628: stur            w2, [x1, #0x2b]
    // 0x8e562c: ldur            x0, [fp, #-0x10]
    // 0x8e5630: StoreField: r1->field_7 = r0
    //     0x8e5630: stur            w0, [x1, #7]
    //     0x8e5634: ldurb           w16, [x1, #-1]
    //     0x8e5638: ldurb           w17, [x0, #-1]
    //     0x8e563c: and             x16, x17, x16, lsr #2
    //     0x8e5640: tst             x16, HEAP, lsr #32
    //     0x8e5644: b.eq            #0x8e564c
    //     0x8e5648: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e564c: r0 = Null
    //     0x8e564c: mov             x0, NULL
    // 0x8e5650: LeaveFrame
    //     0x8e5650: mov             SP, fp
    //     0x8e5654: ldp             fp, lr, [SP], #0x10
    // 0x8e5658: ret
    //     0x8e5658: ret             
  }
  _ reset(/* No info */) {
    // ** addr: 0xe81df4, size: 0x88
    // 0xe81df4: EnterFrame
    //     0xe81df4: stp             fp, lr, [SP, #-0x10]!
    //     0xe81df8: mov             fp, SP
    // 0xe81dfc: AllocStack(0x8)
    //     0xe81dfc: sub             SP, SP, #8
    // 0xe81e00: SetupParameters(GCMBlockCipher this /* r1 => r2, fp-0x8 */)
    //     0xe81e00: mov             x2, x1
    //     0xe81e04: stur            x1, [fp, #-8]
    // 0xe81e08: CheckStackOverflow
    //     0xe81e08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81e0c: cmp             SP, x16
    //     0xe81e10: b.ls            #0xe81e5c
    // 0xe81e14: LoadField: r1 = r2->field_7
    //     0xe81e14: ldur            w1, [x2, #7]
    // 0xe81e18: DecompressPointer r1
    //     0xe81e18: add             x1, x1, HEAP, lsl #32
    // 0xe81e1c: r0 = LoadClassIdInstr(r1)
    //     0xe81e1c: ldur            x0, [x1, #-1]
    //     0xe81e20: ubfx            x0, x0, #0xc, #0x14
    // 0xe81e24: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe81e24: sub             lr, x0, #1, lsl #12
    //     0xe81e28: ldr             lr, [x21, lr, lsl #3]
    //     0xe81e2c: blr             lr
    // 0xe81e30: mov             x1, x0
    // 0xe81e34: r0 = -26
    //     0xe81e34: movn            x0, #0x19
    // 0xe81e38: cbz             x1, #0xe81e64
    // 0xe81e3c: sdiv            x2, x0, x1
    // 0xe81e40: ldur            x1, [fp, #-8]
    // 0xe81e44: StoreField: r1->field_47 = r2
    //     0xe81e44: stur            x2, [x1, #0x47]
    // 0xe81e48: r0 = reset()
    //     0xe81e48: bl              #0xe81e7c  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::reset
    // 0xe81e4c: r0 = Null
    //     0xe81e4c: mov             x0, NULL
    // 0xe81e50: LeaveFrame
    //     0xe81e50: mov             SP, fp
    //     0xe81e54: ldp             fp, lr, [SP], #0x10
    // 0xe81e58: ret
    //     0xe81e58: ret             
    // 0xe81e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe81e5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe81e60: b               #0xe81e14
    // 0xe81e64: stp             x0, x1, [SP, #-0x10]!
    // 0xe81e68: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0xe81e6c: r4 = 0
    //     0xe81e6c: movz            x4, #0
    // 0xe81e70: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe81e74: blr             lr
    // 0xe81e78: brk             #0
  }
  _ processAADBytes(/* No info */) {
    // ** addr: 0xe81f2c, size: 0x360
    // 0xe81f2c: EnterFrame
    //     0xe81f2c: stp             fp, lr, [SP, #-0x10]!
    //     0xe81f30: mov             fp, SP
    // 0xe81f34: AllocStack(0x50)
    //     0xe81f34: sub             SP, SP, #0x50
    // 0xe81f38: SetupParameters(GCMBlockCipher this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0xe81f38: mov             x0, x1
    //     0xe81f3c: stur            x1, [fp, #-8]
    //     0xe81f40: mov             x1, x2
    //     0xe81f44: stur            x2, [fp, #-0x10]
    //     0xe81f48: stur            x5, [fp, #-0x18]
    // 0xe81f4c: CheckStackOverflow
    //     0xe81f4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81f50: cmp             SP, x16
    //     0xe81f54: b.ls            #0xe82250
    // 0xe81f58: r4 = 32
    //     0xe81f58: movz            x4, #0x20
    // 0xe81f5c: r0 = AllocateUint8Array()
    //     0xe81f5c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe81f60: stur            x0, [fp, #-0x30]
    // 0xe81f64: mov             x4, THR
    // 0xe81f68: stur            x4, [fp, #-0x28]
    // 0xe81f6c: r7 = 0
    //     0xe81f6c: movz            x7, #0
    // 0xe81f70: ldur            x6, [fp, #-8]
    // 0xe81f74: ldur            x5, [fp, #-0x18]
    // 0xe81f78: stur            x7, [fp, #-0x20]
    // 0xe81f7c: CheckStackOverflow
    //     0xe81f7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81f80: cmp             SP, x16
    //     0xe81f84: b.ls            #0xe82258
    // 0xe81f88: cmp             x7, x5
    // 0xe81f8c: b.ge            #0xe82240
    // 0xe81f90: r1 = 0
    //     0xe81f90: movz            x1, #0
    // 0xe81f94: r2 = 32
    //     0xe81f94: movz            x2, #0x20
    // 0xe81f98: r3 = 16
    //     0xe81f98: movz            x3, #0x10
    // 0xe81f9c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe81f9c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe81fa0: r0 = checkValidRange()
    //     0xe81fa0: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe81fa4: ldur            x3, [fp, #-0x30]
    // 0xe81fa8: r0 = 0
    //     0xe81fa8: movz            x0, #0
    // 0xe81fac: CheckStackOverflow
    //     0xe81fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81fb0: cmp             SP, x16
    //     0xe81fb4: b.ls            #0xe82260
    // 0xe81fb8: cmp             x0, #0x10
    // 0xe81fbc: b.ge            #0xe81fd4
    // 0xe81fc0: ArrayStore: r3[r0] = rZR  ; TypeUnknown_1
    //     0xe81fc0: add             x1, x3, x0
    //     0xe81fc4: strb            wzr, [x1, #0x17]
    // 0xe81fc8: add             x1, x0, #1
    // 0xe81fcc: mov             x0, x1
    // 0xe81fd0: b               #0xe81fac
    // 0xe81fd4: ldur            x4, [fp, #-0x18]
    // 0xe81fd8: ldur            x2, [fp, #-0x20]
    // 0xe81fdc: add             x5, x2, #0x10
    // 0xe81fe0: stur            x5, [fp, #-0x38]
    // 0xe81fe4: cmp             x5, x4
    // 0xe81fe8: b.le            #0xe81ff4
    // 0xe81fec: mov             x6, x4
    // 0xe81ff0: b               #0xe82008
    // 0xe81ff4: cmp             x5, x4
    // 0xe81ff8: b.ge            #0xe82004
    // 0xe81ffc: mov             x6, x5
    // 0xe82000: b               #0xe82008
    // 0xe82004: mov             x6, x5
    // 0xe82008: r0 = BoxInt64Instr(r6)
    //     0xe82008: sbfiz           x0, x6, #1, #0x1f
    //     0xe8200c: cmp             x6, x0, asr #1
    //     0xe82010: b.eq            #0xe8201c
    //     0xe82014: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe82018: stur            x6, [x0, #7]
    // 0xe8201c: str             x0, [SP]
    // 0xe82020: ldur            x1, [fp, #-0x10]
    // 0xe82024: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe82024: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe82028: r0 = sublist()
    //     0xe82028: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xe8202c: stur            x0, [fp, #-0x48]
    // 0xe82030: LoadField: r4 = r0->field_13
    //     0xe82030: ldur            w4, [x0, #0x13]
    // 0xe82034: stur            x4, [fp, #-0x40]
    // 0xe82038: r5 = LoadInt32Instr(r4)
    //     0xe82038: sbfx            x5, x4, #1, #0x1f
    // 0xe8203c: stur            x5, [fp, #-0x20]
    // 0xe82040: tbnz            x5, #0x3f, #0xe8204c
    // 0xe82044: cmp             x5, #0x10
    // 0xe82048: b.le            #0xe82060
    // 0xe8204c: mov             x2, x4
    // 0xe82050: r1 = 0
    //     0xe82050: movz            x1, #0
    // 0xe82054: r3 = 16
    //     0xe82054: movz            x3, #0x10
    // 0xe82058: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe82058: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe8205c: r0 = checkValidRange()
    //     0xe8205c: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe82060: ldur            x2, [fp, #-0x20]
    // 0xe82064: cbnz            x2, #0xe82074
    // 0xe82068: ldur            x20, [fp, #-0x30]
    // 0xe8206c: ldur            x23, [fp, #-0x28]
    // 0xe82070: b               #0xe821a4
    // 0xe82074: ldur            x0, [fp, #-0x40]
    // 0xe82078: cmp             w0, #0x800
    // 0xe8207c: b.ge            #0xe82154
    // 0xe82080: ldur            x1, [fp, #-0x48]
    // 0xe82084: ldur            x20, [fp, #-0x30]
    // 0xe82088: mov             x3, x0
    // 0xe8208c: add             x2, x1, #0x17
    // 0xe82090: add             x0, x20, #0x17
    // 0xe82094: cbz             x3, #0xe8214c
    // 0xe82098: cmp             x0, x2
    // 0xe8209c: b.ls            #0xe82104
    // 0xe820a0: sxtw            x3, w3
    // 0xe820a4: add             x16, x2, x3, asr #1
    // 0xe820a8: cmp             x0, x16
    // 0xe820ac: b.hs            #0xe82104
    // 0xe820b0: mov             x2, x16
    // 0xe820b4: add             x0, x0, x3, asr #1
    // 0xe820b8: tbz             w3, #4, #0xe820c4
    // 0xe820bc: ldr             x16, [x2, #-8]!
    // 0xe820c0: str             x16, [x0, #-8]!
    // 0xe820c4: tbz             w3, #3, #0xe820d0
    // 0xe820c8: ldr             w16, [x2, #-4]!
    // 0xe820cc: str             w16, [x0, #-4]!
    // 0xe820d0: tbz             w3, #2, #0xe820dc
    // 0xe820d4: ldrh            w16, [x2, #-2]!
    // 0xe820d8: strh            w16, [x0, #-2]!
    // 0xe820dc: tbz             w3, #1, #0xe820e8
    // 0xe820e0: ldrb            w16, [x2, #-1]!
    // 0xe820e4: strb            w16, [x0, #-1]!
    // 0xe820e8: ands            w3, w3, #0xffffffe1
    // 0xe820ec: b.eq            #0xe8214c
    // 0xe820f0: ldp             x16, x17, [x2, #-0x10]!
    // 0xe820f4: stp             x16, x17, [x0, #-0x10]!
    // 0xe820f8: subs            w3, w3, #0x20
    // 0xe820fc: b.ne            #0xe820f0
    // 0xe82100: b               #0xe8214c
    // 0xe82104: tbz             w3, #4, #0xe82110
    // 0xe82108: ldr             x16, [x2], #8
    // 0xe8210c: str             x16, [x0], #8
    // 0xe82110: tbz             w3, #3, #0xe8211c
    // 0xe82114: ldr             w16, [x2], #4
    // 0xe82118: str             w16, [x0], #4
    // 0xe8211c: tbz             w3, #2, #0xe82128
    // 0xe82120: ldrh            w16, [x2], #2
    // 0xe82124: strh            w16, [x0], #2
    // 0xe82128: tbz             w3, #1, #0xe82134
    // 0xe8212c: ldrb            w16, [x2], #1
    // 0xe82130: strb            w16, [x0], #1
    // 0xe82134: ands            w3, w3, #0xffffffe1
    // 0xe82138: b.eq            #0xe8214c
    // 0xe8213c: ldp             x16, x17, [x2], #0x10
    // 0xe82140: stp             x16, x17, [x0], #0x10
    // 0xe82144: subs            w3, w3, #0x20
    // 0xe82148: b.ne            #0xe8213c
    // 0xe8214c: ldur            x23, [fp, #-0x28]
    // 0xe82150: b               #0xe821a4
    // 0xe82154: ldur            x1, [fp, #-0x48]
    // 0xe82158: ldur            x20, [fp, #-0x30]
    // 0xe8215c: ldur            x23, [fp, #-0x28]
    // 0xe82160: LoadField: r0 = r20->field_7
    //     0xe82160: ldur            x0, [x20, #7]
    // 0xe82164: LoadField: r3 = r1->field_7
    //     0xe82164: ldur            x3, [x1, #7]
    // 0xe82168: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe82168: ldr             x9, [x23, #0x658]
    //     0xe8216c: mov             x1, x3
    //     0xe82170: mov             x17, fp
    //     0xe82174: str             fp, [SP, #-8]!
    //     0xe82178: mov             fp, SP
    //     0xe8217c: and             SP, SP, #0xfffffffffffffff0
    //     0xe82180: mov             x19, sp
    //     0xe82184: mov             sp, SP
    //     0xe82188: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe8218c: blr             x9
    //     0xe82190: movz            x16, #0x8
    //     0xe82194: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe82198: mov             sp, x19
    //     0xe8219c: mov             SP, fp
    //     0xe821a0: ldr             fp, [SP], #8
    // 0xe821a4: ldur            x4, [fp, #-8]
    // 0xe821a8: LoadField: r2 = r4->field_3f
    //     0xe821a8: ldur            w2, [x4, #0x3f]
    // 0xe821ac: DecompressPointer r2
    //     0xe821ac: add             x2, x2, HEAP, lsl #32
    // 0xe821b0: r16 = Sentinel
    //     0xe821b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe821b4: cmp             w2, w16
    // 0xe821b8: b.eq            #0xe82268
    // 0xe821bc: LoadField: r0 = r2->field_13
    //     0xe821bc: ldur            w0, [x2, #0x13]
    // 0xe821c0: r3 = LoadInt32Instr(r0)
    //     0xe821c0: sbfx            x3, x0, #1, #0x1f
    // 0xe821c4: r5 = 0
    //     0xe821c4: movz            x5, #0
    // 0xe821c8: CheckStackOverflow
    //     0xe821c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe821cc: cmp             SP, x16
    //     0xe821d0: b.ls            #0xe82274
    // 0xe821d4: cmp             x5, x3
    // 0xe821d8: b.ge            #0xe82214
    // 0xe821dc: ArrayLoad: r6 = r2[r5]  ; List_1
    //     0xe821dc: add             x16, x2, x5
    //     0xe821e0: ldrb            w6, [x16, #0x17]
    // 0xe821e4: mov             x1, x5
    // 0xe821e8: r0 = 16
    //     0xe821e8: movz            x0, #0x10
    // 0xe821ec: cmp             x1, x0
    // 0xe821f0: b.hs            #0xe8227c
    // 0xe821f4: ArrayLoad: r0 = r20[r5]  ; List_1
    //     0xe821f4: add             x16, x20, x5
    //     0xe821f8: ldrb            w0, [x16, #0x17]
    // 0xe821fc: eor             x1, x6, x0
    // 0xe82200: ArrayStore: r2[r5] = r1  ; TypeUnknown_1
    //     0xe82200: add             x0, x2, x5
    //     0xe82204: strb            w1, [x0, #0x17]
    // 0xe82208: add             x0, x5, #1
    // 0xe8220c: mov             x5, x0
    // 0xe82210: b               #0xe821c8
    // 0xe82214: LoadField: r3 = r4->field_2f
    //     0xe82214: ldur            w3, [x4, #0x2f]
    // 0xe82218: DecompressPointer r3
    //     0xe82218: add             x3, x3, HEAP, lsl #32
    // 0xe8221c: r16 = Sentinel
    //     0xe8221c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe82220: cmp             w3, w16
    // 0xe82224: b.eq            #0xe82280
    // 0xe82228: mov             x1, x4
    // 0xe8222c: r0 = _mult()
    //     0xe8222c: bl              #0xe82350  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_mult
    // 0xe82230: ldur            x7, [fp, #-0x38]
    // 0xe82234: ldur            x0, [fp, #-0x30]
    // 0xe82238: ldur            x4, [fp, #-0x28]
    // 0xe8223c: b               #0xe81f70
    // 0xe82240: r0 = Null
    //     0xe82240: mov             x0, NULL
    // 0xe82244: LeaveFrame
    //     0xe82244: mov             SP, fp
    //     0xe82248: ldp             fp, lr, [SP], #0x10
    // 0xe8224c: ret
    //     0xe8224c: ret             
    // 0xe82250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82254: b               #0xe81f58
    // 0xe82258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82258: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8225c: b               #0xe81f88
    // 0xe82260: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82260: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82264: b               #0xe81fb8
    // 0xe82268: r9 = _x
    //     0xe82268: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a10] Field <GCMBlockCipher._x@914399014>: late (offset: 0x40)
    //     0xe8226c: ldr             x9, [x9, #0xa10]
    // 0xe82270: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe82270: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe82274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82274: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82278: b               #0xe821d4
    // 0xe8227c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8227c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe82280: r9 = _h
    //     0xe82280: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a18] Field <GCMBlockCipher._h@914399014>: late (offset: 0x30)
    //     0xe82284: ldr             x9, [x9, #0xa18]
    // 0xe82288: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe82288: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _gHASHBlock(/* No info */) {
    // ** addr: 0xe8228c, size: 0xc4
    // 0xe8228c: EnterFrame
    //     0xe8228c: stp             fp, lr, [SP, #-0x10]!
    //     0xe82290: mov             fp, SP
    // 0xe82294: mov             x4, x1
    // 0xe82298: CheckStackOverflow
    //     0xe82298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8229c: cmp             SP, x16
    //     0xe822a0: b.ls            #0xe82330
    // 0xe822a4: LoadField: r0 = r2->field_13
    //     0xe822a4: ldur            w0, [x2, #0x13]
    // 0xe822a8: r5 = LoadInt32Instr(r0)
    //     0xe822a8: sbfx            x5, x0, #1, #0x1f
    // 0xe822ac: LoadField: r0 = r3->field_13
    //     0xe822ac: ldur            w0, [x3, #0x13]
    // 0xe822b0: r6 = LoadInt32Instr(r0)
    //     0xe822b0: sbfx            x6, x0, #1, #0x1f
    // 0xe822b4: r7 = 0
    //     0xe822b4: movz            x7, #0
    // 0xe822b8: CheckStackOverflow
    //     0xe822b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe822bc: cmp             SP, x16
    //     0xe822c0: b.ls            #0xe82338
    // 0xe822c4: cmp             x7, x5
    // 0xe822c8: b.ge            #0xe82304
    // 0xe822cc: ArrayLoad: r8 = r2[r7]  ; List_1
    //     0xe822cc: add             x16, x2, x7
    //     0xe822d0: ldrb            w8, [x16, #0x17]
    // 0xe822d4: mov             x0, x6
    // 0xe822d8: mov             x1, x7
    // 0xe822dc: cmp             x1, x0
    // 0xe822e0: b.hs            #0xe82340
    // 0xe822e4: ArrayLoad: r0 = r3[r7]  ; List_1
    //     0xe822e4: add             x16, x3, x7
    //     0xe822e8: ldrb            w0, [x16, #0x17]
    // 0xe822ec: eor             x1, x8, x0
    // 0xe822f0: ArrayStore: r2[r7] = r1  ; TypeUnknown_1
    //     0xe822f0: add             x0, x2, x7
    //     0xe822f4: strb            w1, [x0, #0x17]
    // 0xe822f8: add             x0, x7, #1
    // 0xe822fc: mov             x7, x0
    // 0xe82300: b               #0xe822b8
    // 0xe82304: LoadField: r3 = r4->field_2f
    //     0xe82304: ldur            w3, [x4, #0x2f]
    // 0xe82308: DecompressPointer r3
    //     0xe82308: add             x3, x3, HEAP, lsl #32
    // 0xe8230c: r16 = Sentinel
    //     0xe8230c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe82310: cmp             w3, w16
    // 0xe82314: b.eq            #0xe82344
    // 0xe82318: mov             x1, x4
    // 0xe8231c: r0 = _mult()
    //     0xe8231c: bl              #0xe82350  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_mult
    // 0xe82320: r0 = Null
    //     0xe82320: mov             x0, NULL
    // 0xe82324: LeaveFrame
    //     0xe82324: mov             SP, fp
    //     0xe82328: ldp             fp, lr, [SP], #0x10
    // 0xe8232c: ret
    //     0xe8232c: ret             
    // 0xe82330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82330: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82334: b               #0xe822a4
    // 0xe82338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8233c: b               #0xe822c4
    // 0xe82340: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe82340: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe82344: r9 = _h
    //     0xe82344: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a18] Field <GCMBlockCipher._h@914399014>: late (offset: 0x30)
    //     0xe82348: ldr             x9, [x9, #0xa18]
    // 0xe8234c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe8234c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _mult(/* No info */) {
    // ** addr: 0xe82350, size: 0x3c8
    // 0xe82350: EnterFrame
    //     0xe82350: stp             fp, lr, [SP, #-0x10]!
    //     0xe82354: mov             fp, SP
    // 0xe82358: AllocStack(0x30)
    //     0xe82358: sub             SP, SP, #0x30
    // 0xe8235c: SetupParameters(GCMBlockCipher this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xe8235c: stur            x1, [fp, #-0x10]
    //     0xe82360: stur            x2, [fp, #-0x18]
    //     0xe82364: stur            x3, [fp, #-0x20]
    // 0xe82368: CheckStackOverflow
    //     0xe82368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8236c: cmp             SP, x16
    //     0xe82370: b.ls            #0xe826e8
    // 0xe82374: LoadField: r0 = r2->field_13
    //     0xe82374: ldur            w0, [x2, #0x13]
    // 0xe82378: mov             x4, x0
    // 0xe8237c: stur            x0, [fp, #-8]
    // 0xe82380: r0 = AllocateUint8Array()
    //     0xe82380: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe82384: mov             x4, x0
    // 0xe82388: ldur            x2, [fp, #-0x20]
    // 0xe8238c: stur            x4, [fp, #-0x30]
    // 0xe82390: LoadField: r0 = r2->field_13
    //     0xe82390: ldur            w0, [x2, #0x13]
    // 0xe82394: r3 = LoadInt32Instr(r0)
    //     0xe82394: sbfx            x3, x0, #1, #0x1f
    // 0xe82398: ldur            x5, [fp, #-8]
    // 0xe8239c: r6 = LoadInt32Instr(r5)
    //     0xe8239c: sbfx            x6, x5, #1, #0x1f
    // 0xe823a0: ldur            x0, [fp, #-0x10]
    // 0xe823a4: LoadField: r7 = r0->field_4f
    //     0xe823a4: ldur            w7, [x0, #0x4f]
    // 0xe823a8: DecompressPointer r7
    //     0xe823a8: add             x7, x7, HEAP, lsl #32
    // 0xe823ac: LoadField: r0 = r7->field_13
    //     0xe823ac: ldur            w0, [x7, #0x13]
    // 0xe823b0: r8 = LoadInt32Instr(r0)
    //     0xe823b0: sbfx            x8, x0, #1, #0x1f
    // 0xe823b4: ldur            x9, [fp, #-0x18]
    // 0xe823b8: r19 = 0
    //     0xe823b8: movz            x19, #0
    // 0xe823bc: r14 = 8
    //     0xe823bc: movz            x14, #0x8
    // 0xe823c0: r13 = 7
    //     0xe823c0: movz            x13, #0x7
    // 0xe823c4: r12 = 1
    //     0xe823c4: movz            x12, #0x1
    // 0xe823c8: r11 = 7
    //     0xe823c8: movz            x11, #0x7
    // 0xe823cc: r10 = 1
    //     0xe823cc: movz            x10, #0x1
    // 0xe823d0: CheckStackOverflow
    //     0xe823d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe823d4: cmp             SP, x16
    //     0xe823d8: b.ls            #0xe826f0
    // 0xe823dc: cmp             x19, #0x80
    // 0xe823e0: b.ge            #0xe82588
    // 0xe823e4: sdiv            x20, x19, x14
    // 0xe823e8: mov             x0, x19
    // 0xe823ec: ubfx            x0, x0, #0, #0x20
    // 0xe823f0: and             x1, x0, x11
    // 0xe823f4: ubfx            x1, x1, #0, #0x20
    // 0xe823f8: sub             x0, x13, x1
    // 0xe823fc: lsl             x23, x12, x0
    // 0xe82400: mov             x0, x3
    // 0xe82404: mov             x1, x20
    // 0xe82408: cmp             x1, x0
    // 0xe8240c: b.hs            #0xe826f8
    // 0xe82410: ArrayLoad: r0 = r2[r20]  ; List_1
    //     0xe82410: add             x16, x2, x20
    //     0xe82414: ldrb            w0, [x16, #0x17]
    // 0xe82418: mov             x1, x23
    // 0xe8241c: ubfx            x1, x1, #0, #0x20
    // 0xe82420: ubfx            x0, x0, #0, #0x20
    // 0xe82424: and             x20, x0, x1
    // 0xe82428: ubfx            x20, x20, #0, #0x20
    // 0xe8242c: cmp             x20, x23
    // 0xe82430: b.ne            #0xe8243c
    // 0xe82434: r0 = 255
    //     0xe82434: movz            x0, #0xff
    // 0xe82438: b               #0xe82440
    // 0xe8243c: r0 = 0
    //     0xe8243c: movz            x0, #0
    // 0xe82440: r1 = 0
    //     0xe82440: movz            x1, #0
    // 0xe82444: CheckStackOverflow
    //     0xe82444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe82448: cmp             SP, x16
    //     0xe8244c: b.ls            #0xe826fc
    // 0xe82450: cmp             x1, x6
    // 0xe82454: b.ge            #0xe82494
    // 0xe82458: ArrayLoad: r20 = r4[r1]  ; List_1
    //     0xe82458: add             x16, x4, x1
    //     0xe8245c: ldrb            w20, [x16, #0x17]
    // 0xe82460: ArrayLoad: r23 = r9[r1]  ; List_1
    //     0xe82460: add             x16, x9, x1
    //     0xe82464: ldrb            w23, [x16, #0x17]
    // 0xe82468: mov             x24, x0
    // 0xe8246c: ubfx            x24, x24, #0, #0x20
    // 0xe82470: ubfx            x23, x23, #0, #0x20
    // 0xe82474: and             x25, x23, x24
    // 0xe82478: ubfx            x25, x25, #0, #0x20
    // 0xe8247c: eor             x23, x20, x25
    // 0xe82480: ArrayStore: r4[r1] = r23  ; TypeUnknown_1
    //     0xe82480: add             x20, x4, x1
    //     0xe82484: strb            w23, [x20, #0x17]
    // 0xe82488: add             x20, x1, #1
    // 0xe8248c: mov             x1, x20
    // 0xe82490: b               #0xe82444
    // 0xe82494: r1 = false
    //     0xe82494: add             x1, NULL, #0x30  ; false
    // 0xe82498: r0 = 0
    //     0xe82498: movz            x0, #0
    // 0xe8249c: CheckStackOverflow
    //     0xe8249c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe824a0: cmp             SP, x16
    //     0xe824a4: b.ls            #0xe82704
    // 0xe824a8: cmp             x0, x6
    // 0xe824ac: b.ge            #0xe8250c
    // 0xe824b0: ArrayLoad: r20 = r9[r0]  ; List_1
    //     0xe824b0: add             x16, x9, x0
    //     0xe824b4: ldrb            w20, [x16, #0x17]
    // 0xe824b8: mov             x23, x20
    // 0xe824bc: ubfx            x23, x23, #0, #0x20
    // 0xe824c0: and             x24, x23, x10
    // 0xe824c4: ubfx            x24, x24, #0, #0x20
    // 0xe824c8: cmp             x24, #1
    // 0xe824cc: r16 = true
    //     0xe824cc: add             x16, NULL, #0x20  ; true
    // 0xe824d0: r17 = false
    //     0xe824d0: add             x17, NULL, #0x30  ; false
    // 0xe824d4: csel            x23, x16, x17, eq
    // 0xe824d8: asr             x24, x20, #1
    // 0xe824dc: ArrayStore: r9[r0] = r24  ; TypeUnknown_1
    //     0xe824dc: add             x20, x9, x0
    //     0xe824e0: strb            w24, [x20, #0x17]
    // 0xe824e4: tbnz            w1, #4, #0xe824fc
    // 0xe824e8: ArrayLoad: r1 = r9[r0]  ; List_1
    //     0xe824e8: add             x16, x9, x0
    //     0xe824ec: ldrb            w1, [x16, #0x17]
    // 0xe824f0: orr             x20, x1, #0x80
    // 0xe824f4: ArrayStore: r9[r0] = r20  ; TypeUnknown_1
    //     0xe824f4: add             x1, x9, x0
    //     0xe824f8: strb            w20, [x1, #0x17]
    // 0xe824fc: add             x20, x0, #1
    // 0xe82500: mov             x1, x23
    // 0xe82504: mov             x0, x20
    // 0xe82508: b               #0xe8249c
    // 0xe8250c: tst             x1, #0x10
    // 0xe82510: cset            x0, ne
    // 0xe82514: sub             x0, x0, #1
    // 0xe82518: and             x0, x0, #0x1fe
    // 0xe8251c: r20 = LoadInt32Instr(r0)
    //     0xe8251c: sbfx            x20, x0, #1, #0x1f
    // 0xe82520: r23 = 0
    //     0xe82520: movz            x23, #0
    // 0xe82524: CheckStackOverflow
    //     0xe82524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe82528: cmp             SP, x16
    //     0xe8252c: b.ls            #0xe8270c
    // 0xe82530: cmp             x23, x6
    // 0xe82534: b.ge            #0xe8257c
    // 0xe82538: ArrayLoad: r24 = r9[r23]  ; List_1
    //     0xe82538: add             x16, x9, x23
    //     0xe8253c: ldrb            w24, [x16, #0x17]
    // 0xe82540: mov             x0, x8
    // 0xe82544: mov             x1, x23
    // 0xe82548: cmp             x1, x0
    // 0xe8254c: b.hs            #0xe82714
    // 0xe82550: ArrayLoad: r0 = r7[r23]  ; List_1
    //     0xe82550: add             x16, x7, x23
    //     0xe82554: ldrb            w0, [x16, #0x17]
    // 0xe82558: ubfx            x0, x0, #0, #0x20
    // 0xe8255c: and             x1, x0, x20
    // 0xe82560: ubfx            x1, x1, #0, #0x20
    // 0xe82564: eor             x0, x24, x1
    // 0xe82568: ArrayStore: r9[r23] = r0  ; TypeUnknown_1
    //     0xe82568: add             x1, x9, x23
    //     0xe8256c: strb            w0, [x1, #0x17]
    // 0xe82570: add             x0, x23, #1
    // 0xe82574: mov             x23, x0
    // 0xe82578: b               #0xe82524
    // 0xe8257c: add             x0, x19, #1
    // 0xe82580: mov             x19, x0
    // 0xe82584: b               #0xe823d0
    // 0xe82588: r0 = LoadInt32Instr(r5)
    //     0xe82588: sbfx            x0, x5, #1, #0x1f
    // 0xe8258c: stur            x0, [fp, #-0x28]
    // 0xe82590: tbz             x0, #0x3f, #0xe825a8
    // 0xe82594: mov             x2, x5
    // 0xe82598: mov             x3, x0
    // 0xe8259c: r1 = 0
    //     0xe8259c: movz            x1, #0
    // 0xe825a0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe825a0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe825a4: r0 = checkValidRange()
    //     0xe825a4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe825a8: ldur            x2, [fp, #-0x28]
    // 0xe825ac: cbz             x2, #0xe826d8
    // 0xe825b0: ldur            x20, [fp, #-8]
    // 0xe825b4: cmp             w20, #0x800
    // 0xe825b8: b.ge            #0xe8268c
    // 0xe825bc: ldur            x24, [fp, #-0x18]
    // 0xe825c0: ldur            x23, [fp, #-0x30]
    // 0xe825c4: mov             x0, x20
    // 0xe825c8: add             x25, x23, #0x17
    // 0xe825cc: add             x20, x24, #0x17
    // 0xe825d0: cbz             x0, #0xe82688
    // 0xe825d4: cmp             x20, x25
    // 0xe825d8: b.ls            #0xe82640
    // 0xe825dc: sxtw            x0, w0
    // 0xe825e0: add             x16, x25, x0, asr #1
    // 0xe825e4: cmp             x20, x16
    // 0xe825e8: b.hs            #0xe82640
    // 0xe825ec: mov             x25, x16
    // 0xe825f0: add             x20, x20, x0, asr #1
    // 0xe825f4: tbz             w0, #4, #0xe82600
    // 0xe825f8: ldr             x16, [x25, #-8]!
    // 0xe825fc: str             x16, [x20, #-8]!
    // 0xe82600: tbz             w0, #3, #0xe8260c
    // 0xe82604: ldr             w16, [x25, #-4]!
    // 0xe82608: str             w16, [x20, #-4]!
    // 0xe8260c: tbz             w0, #2, #0xe82618
    // 0xe82610: ldrh            w16, [x25, #-2]!
    // 0xe82614: strh            w16, [x20, #-2]!
    // 0xe82618: tbz             w0, #1, #0xe82624
    // 0xe8261c: ldrb            w16, [x25, #-1]!
    // 0xe82620: strb            w16, [x20, #-1]!
    // 0xe82624: ands            w0, w0, #0xffffffe1
    // 0xe82628: b.eq            #0xe82688
    // 0xe8262c: ldp             x16, x17, [x25, #-0x10]!
    // 0xe82630: stp             x16, x17, [x20, #-0x10]!
    // 0xe82634: subs            w0, w0, #0x20
    // 0xe82638: b.ne            #0xe8262c
    // 0xe8263c: b               #0xe82688
    // 0xe82640: tbz             w0, #4, #0xe8264c
    // 0xe82644: ldr             x16, [x25], #8
    // 0xe82648: str             x16, [x20], #8
    // 0xe8264c: tbz             w0, #3, #0xe82658
    // 0xe82650: ldr             w16, [x25], #4
    // 0xe82654: str             w16, [x20], #4
    // 0xe82658: tbz             w0, #2, #0xe82664
    // 0xe8265c: ldrh            w16, [x25], #2
    // 0xe82660: strh            w16, [x20], #2
    // 0xe82664: tbz             w0, #1, #0xe82670
    // 0xe82668: ldrb            w16, [x25], #1
    // 0xe8266c: strb            w16, [x20], #1
    // 0xe82670: ands            w0, w0, #0xffffffe1
    // 0xe82674: b.eq            #0xe82688
    // 0xe82678: ldp             x16, x17, [x25], #0x10
    // 0xe8267c: stp             x16, x17, [x20], #0x10
    // 0xe82680: subs            w0, w0, #0x20
    // 0xe82684: b.ne            #0xe82678
    // 0xe82688: b               #0xe826d8
    // 0xe8268c: ldur            x24, [fp, #-0x18]
    // 0xe82690: ldur            x23, [fp, #-0x30]
    // 0xe82694: LoadField: r0 = r24->field_7
    //     0xe82694: ldur            x0, [x24, #7]
    // 0xe82698: LoadField: r1 = r23->field_7
    //     0xe82698: ldur            x1, [x23, #7]
    // 0xe8269c: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe8269c: mov             x20, THR
    //     0xe826a0: ldr             x9, [x20, #0x658]
    //     0xe826a4: mov             x17, fp
    //     0xe826a8: str             fp, [SP, #-8]!
    //     0xe826ac: mov             fp, SP
    //     0xe826b0: and             SP, SP, #0xfffffffffffffff0
    //     0xe826b4: mov             x19, sp
    //     0xe826b8: mov             sp, SP
    //     0xe826bc: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe826c0: blr             x9
    //     0xe826c4: movz            x16, #0x8
    //     0xe826c8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe826cc: mov             sp, x19
    //     0xe826d0: mov             SP, fp
    //     0xe826d4: ldr             fp, [SP], #8
    // 0xe826d8: r0 = Null
    //     0xe826d8: mov             x0, NULL
    // 0xe826dc: LeaveFrame
    //     0xe826dc: mov             SP, fp
    //     0xe826e0: ldp             fp, lr, [SP], #0x10
    // 0xe826e4: ret
    //     0xe826e4: ret             
    // 0xe826e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe826e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe826ec: b               #0xe82374
    // 0xe826f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe826f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe826f4: b               #0xe823dc
    // 0xe826f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe826f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe826fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe826fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82700: b               #0xe82450
    // 0xe82704: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82704: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82708: b               #0xe824a8
    // 0xe8270c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8270c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82710: b               #0xe82530
    // 0xe82714: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe82714: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ prepare(/* No info */) {
    // ** addr: 0xe82718, size: 0x234
    // 0xe82718: EnterFrame
    //     0xe82718: stp             fp, lr, [SP, #-0x10]!
    //     0xe8271c: mov             fp, SP
    // 0xe82720: AllocStack(0x10)
    //     0xe82720: sub             SP, SP, #0x10
    // 0xe82724: SetupParameters(GCMBlockCipher this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3 */)
    //     0xe82724: mov             x4, x1
    //     0xe82728: mov             x3, x2
    //     0xe8272c: stur            x1, [fp, #-0x10]
    // 0xe82730: CheckStackOverflow
    //     0xe82730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe82734: cmp             SP, x16
    //     0xe82738: b.ls            #0xe8292c
    // 0xe8273c: LoadField: r0 = r4->field_f
    //     0xe8273c: ldur            w0, [x4, #0xf]
    // 0xe82740: DecompressPointer r0
    //     0xe82740: add             x0, x0, HEAP, lsl #32
    // 0xe82744: r16 = Sentinel
    //     0xe82744: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe82748: cmp             w0, w16
    // 0xe8274c: b.eq            #0xe82934
    // 0xe82750: cmp             w0, #0x20
    // 0xe82754: b.ne            #0xe82904
    // 0xe82758: LoadField: r5 = r4->field_7
    //     0xe82758: ldur            w5, [x4, #7]
    // 0xe8275c: DecompressPointer r5
    //     0xe8275c: add             x5, x5, HEAP, lsl #32
    // 0xe82760: stur            x5, [fp, #-8]
    // 0xe82764: r0 = LoadClassIdInstr(r5)
    //     0xe82764: ldur            x0, [x5, #-1]
    //     0xe82768: ubfx            x0, x0, #0xc, #0x14
    // 0xe8276c: mov             x1, x5
    // 0xe82770: r2 = true
    //     0xe82770: add             x2, NULL, #0x20  ; true
    // 0xe82774: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe82774: sub             lr, x0, #0xeda
    //     0xe82778: ldr             lr, [x21, lr, lsl #3]
    //     0xe8277c: blr             lr
    // 0xe82780: ldur            x2, [fp, #-8]
    // 0xe82784: r0 = LoadClassIdInstr(r2)
    //     0xe82784: ldur            x0, [x2, #-1]
    //     0xe82788: ubfx            x0, x0, #0xc, #0x14
    // 0xe8278c: mov             x1, x2
    // 0xe82790: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe82790: sub             lr, x0, #1, lsl #12
    //     0xe82794: ldr             lr, [x21, lr, lsl #3]
    //     0xe82798: blr             lr
    // 0xe8279c: mov             x2, x0
    // 0xe827a0: r0 = BoxInt64Instr(r2)
    //     0xe827a0: sbfiz           x0, x2, #1, #0x1f
    //     0xe827a4: cmp             x2, x0, asr #1
    //     0xe827a8: b.eq            #0xe827b4
    //     0xe827ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe827b0: stur            x2, [x0, #7]
    // 0xe827b4: mov             x4, x0
    // 0xe827b8: r0 = AllocateUint8Array()
    //     0xe827b8: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe827bc: mov             x1, x0
    // 0xe827c0: ldur            x4, [fp, #-0x10]
    // 0xe827c4: StoreField: r4->field_2f = r0
    //     0xe827c4: stur            w0, [x4, #0x2f]
    //     0xe827c8: ldurb           w16, [x4, #-1]
    //     0xe827cc: ldurb           w17, [x0, #-1]
    //     0xe827d0: and             x16, x17, x16, lsr #2
    //     0xe827d4: tst             x16, HEAP, lsr #32
    //     0xe827d8: b.eq            #0xe827e0
    //     0xe827dc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe827e0: ldur            x0, [fp, #-8]
    // 0xe827e4: r2 = LoadClassIdInstr(r0)
    //     0xe827e4: ldur            x2, [x0, #-1]
    //     0xe827e8: ubfx            x2, x2, #0xc, #0x14
    // 0xe827ec: mov             x16, x1
    // 0xe827f0: mov             x1, x2
    // 0xe827f4: mov             x2, x16
    // 0xe827f8: mov             x16, x0
    // 0xe827fc: mov             x0, x1
    // 0xe82800: mov             x1, x16
    // 0xe82804: mov             x5, x2
    // 0xe82808: r3 = 0
    //     0xe82808: movz            x3, #0
    // 0xe8280c: r6 = 0
    //     0xe8280c: movz            x6, #0
    // 0xe82810: r0 = GDT[cid_x0 + -0xf69]()
    //     0xe82810: sub             lr, x0, #0xf69
    //     0xe82814: ldr             lr, [x21, lr, lsl #3]
    //     0xe82818: blr             lr
    // 0xe8281c: ldur            x0, [fp, #-0x10]
    // 0xe82820: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xe82820: ldur            w2, [x0, #0x17]
    // 0xe82824: DecompressPointer r2
    //     0xe82824: add             x2, x2, HEAP, lsl #32
    // 0xe82828: r16 = Sentinel
    //     0xe82828: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8282c: cmp             w2, w16
    // 0xe82830: b.eq            #0xe82940
    // 0xe82834: mov             x1, x0
    // 0xe82838: r0 = _computeInitialCounter()
    //     0xe82838: bl              #0xe829ac  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_computeInitialCounter
    // 0xe8283c: mov             x2, x0
    // 0xe82840: ldur            x1, [fp, #-0x10]
    // 0xe82844: stur            x2, [fp, #-8]
    // 0xe82848: StoreField: r1->field_33 = r0
    //     0xe82848: stur            w0, [x1, #0x33]
    //     0xe8284c: ldurb           w16, [x1, #-1]
    //     0xe82850: ldurb           w17, [x0, #-1]
    //     0xe82854: and             x16, x17, x16, lsr #2
    //     0xe82858: tst             x16, HEAP, lsr #32
    //     0xe8285c: b.eq            #0xe82864
    //     0xe82860: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe82864: r4 = 32
    //     0xe82864: movz            x4, #0x20
    // 0xe82868: r0 = AllocateUint8Array()
    //     0xe82868: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8286c: mov             x1, x0
    // 0xe82870: ldur            x4, [fp, #-0x10]
    // 0xe82874: StoreField: r4->field_3b = r0
    //     0xe82874: stur            w0, [x4, #0x3b]
    //     0xe82878: ldurb           w16, [x4, #-1]
    //     0xe8287c: ldurb           w17, [x0, #-1]
    //     0xe82880: and             x16, x17, x16, lsr #2
    //     0xe82884: tst             x16, HEAP, lsr #32
    //     0xe82888: b.eq            #0xe82890
    //     0xe8288c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe82890: mov             x3, x1
    // 0xe82894: mov             x1, x4
    // 0xe82898: ldur            x2, [fp, #-8]
    // 0xe8289c: r0 = _computeE()
    //     0xe8289c: bl              #0xe8294c  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_computeE
    // 0xe828a0: r4 = 32
    //     0xe828a0: movz            x4, #0x20
    // 0xe828a4: r0 = AllocateUint8Array()
    //     0xe828a4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe828a8: ldur            x1, [fp, #-0x10]
    // 0xe828ac: StoreField: r1->field_37 = r0
    //     0xe828ac: stur            w0, [x1, #0x37]
    //     0xe828b0: ldurb           w16, [x1, #-1]
    //     0xe828b4: ldurb           w17, [x0, #-1]
    //     0xe828b8: and             x16, x17, x16, lsr #2
    //     0xe828bc: tst             x16, HEAP, lsr #32
    //     0xe828c0: b.eq            #0xe828c8
    //     0xe828c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe828c8: r4 = 32
    //     0xe828c8: movz            x4, #0x20
    // 0xe828cc: r0 = AllocateUint8Array()
    //     0xe828cc: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe828d0: ldur            x1, [fp, #-0x10]
    // 0xe828d4: StoreField: r1->field_3f = r0
    //     0xe828d4: stur            w0, [x1, #0x3f]
    //     0xe828d8: ldurb           w16, [x1, #-1]
    //     0xe828dc: ldurb           w17, [x0, #-1]
    //     0xe828e0: and             x16, x17, x16, lsr #2
    //     0xe828e4: tst             x16, HEAP, lsr #32
    //     0xe828e8: b.eq            #0xe828f0
    //     0xe828ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe828f0: StoreField: r1->field_43 = rZR
    //     0xe828f0: stur            wzr, [x1, #0x43]
    // 0xe828f4: r0 = Null
    //     0xe828f4: mov             x0, NULL
    // 0xe828f8: LeaveFrame
    //     0xe828f8: mov             SP, fp
    //     0xe828fc: ldp             fp, lr, [SP], #0x10
    // 0xe82900: ret
    //     0xe82900: ret             
    // 0xe82904: r0 = ArgumentError()
    //     0xe82904: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe82908: mov             x1, x0
    // 0xe8290c: r0 = "macSize should be equal to 16 for GCM"
    //     0xe8290c: add             x0, PP, #0x20, lsl #12  ; [pp+0x20a20] "macSize should be equal to 16 for GCM"
    //     0xe82910: ldr             x0, [x0, #0xa20]
    // 0xe82914: ArrayStore: r1[0] = r0  ; List_4
    //     0xe82914: stur            w0, [x1, #0x17]
    // 0xe82918: r0 = false
    //     0xe82918: add             x0, NULL, #0x30  ; false
    // 0xe8291c: StoreField: r1->field_b = r0
    //     0xe8291c: stur            w0, [x1, #0xb]
    // 0xe82920: mov             x0, x1
    // 0xe82924: r0 = Throw()
    //     0xe82924: bl              #0xec04b8  ; ThrowStub
    // 0xe82928: brk             #0
    // 0xe8292c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8292c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82930: b               #0xe8273c
    // 0xe82934: r9 = _macSize
    //     0xe82934: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a28] Field <BaseAEADBlockCipher._macSize@2660101045>: late (offset: 0x10)
    //     0xe82938: ldr             x9, [x9, #0xa28]
    // 0xe8293c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe8293c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe82940: r9 = _nonce
    //     0xe82940: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a30] Field <BaseAEADBlockCipher._nonce@2660101045>: late (offset: 0x18)
    //     0xe82944: ldr             x9, [x9, #0xa30]
    // 0xe82948: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe82948: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _computeE(/* No info */) {
    // ** addr: 0xe8294c, size: 0x60
    // 0xe8294c: EnterFrame
    //     0xe8294c: stp             fp, lr, [SP, #-0x10]!
    //     0xe82950: mov             fp, SP
    // 0xe82954: mov             x5, x3
    // 0xe82958: CheckStackOverflow
    //     0xe82958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8295c: cmp             SP, x16
    //     0xe82960: b.ls            #0xe829a4
    // 0xe82964: LoadField: r0 = r1->field_7
    //     0xe82964: ldur            w0, [x1, #7]
    // 0xe82968: DecompressPointer r0
    //     0xe82968: add             x0, x0, HEAP, lsl #32
    // 0xe8296c: r1 = LoadClassIdInstr(r0)
    //     0xe8296c: ldur            x1, [x0, #-1]
    //     0xe82970: ubfx            x1, x1, #0xc, #0x14
    // 0xe82974: mov             x16, x0
    // 0xe82978: mov             x0, x1
    // 0xe8297c: mov             x1, x16
    // 0xe82980: r3 = 0
    //     0xe82980: movz            x3, #0
    // 0xe82984: r6 = 0
    //     0xe82984: movz            x6, #0
    // 0xe82988: r0 = GDT[cid_x0 + -0xf69]()
    //     0xe82988: sub             lr, x0, #0xf69
    //     0xe8298c: ldr             lr, [x21, lr, lsl #3]
    //     0xe82990: blr             lr
    // 0xe82994: r0 = Null
    //     0xe82994: mov             x0, NULL
    // 0xe82998: LeaveFrame
    //     0xe82998: mov             SP, fp
    //     0xe8299c: ldp             fp, lr, [SP], #0x10
    // 0xe829a0: ret
    //     0xe829a0: ret             
    // 0xe829a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe829a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe829a8: b               #0xe82964
  }
  _ _computeInitialCounter(/* No info */) {
    // ** addr: 0xe829ac, size: 0x250
    // 0xe829ac: EnterFrame
    //     0xe829ac: stp             fp, lr, [SP, #-0x10]!
    //     0xe829b0: mov             fp, SP
    // 0xe829b4: AllocStack(0x38)
    //     0xe829b4: sub             SP, SP, #0x38
    // 0xe829b8: SetupParameters(GCMBlockCipher this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xe829b8: mov             x3, x2
    //     0xe829bc: stur            x1, [fp, #-8]
    //     0xe829c0: stur            x2, [fp, #-0x10]
    // 0xe829c4: CheckStackOverflow
    //     0xe829c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe829c8: cmp             SP, x16
    //     0xe829cc: b.ls            #0xe82bf4
    // 0xe829d0: r4 = 32
    //     0xe829d0: movz            x4, #0x20
    // 0xe829d4: r0 = AllocateUint8Array()
    //     0xe829d4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe829d8: mov             x20, x0
    // 0xe829dc: ldur            x3, [fp, #-0x10]
    // 0xe829e0: stur            x20, [fp, #-0x20]
    // 0xe829e4: LoadField: r0 = r3->field_13
    //     0xe829e4: ldur            w0, [x3, #0x13]
    // 0xe829e8: r4 = LoadInt32Instr(r0)
    //     0xe829e8: sbfx            x4, x0, #1, #0x1f
    // 0xe829ec: stur            x4, [fp, #-0x18]
    // 0xe829f0: cmp             x4, #0xc
    // 0xe829f4: b.ne            #0xe82b1c
    // 0xe829f8: cmp             w0, #0x800
    // 0xe829fc: b.ge            #0xe82ac8
    // 0xe82a00: mov             x2, x0
    // 0xe82a04: add             x1, x3, #0x17
    // 0xe82a08: add             x0, x20, #0x17
    // 0xe82a0c: cbz             x2, #0xe82ac4
    // 0xe82a10: cmp             x0, x1
    // 0xe82a14: b.ls            #0xe82a7c
    // 0xe82a18: sxtw            x2, w2
    // 0xe82a1c: add             x16, x1, x2, asr #1
    // 0xe82a20: cmp             x0, x16
    // 0xe82a24: b.hs            #0xe82a7c
    // 0xe82a28: mov             x1, x16
    // 0xe82a2c: add             x0, x0, x2, asr #1
    // 0xe82a30: tbz             w2, #4, #0xe82a3c
    // 0xe82a34: ldr             x16, [x1, #-8]!
    // 0xe82a38: str             x16, [x0, #-8]!
    // 0xe82a3c: tbz             w2, #3, #0xe82a48
    // 0xe82a40: ldr             w16, [x1, #-4]!
    // 0xe82a44: str             w16, [x0, #-4]!
    // 0xe82a48: tbz             w2, #2, #0xe82a54
    // 0xe82a4c: ldrh            w16, [x1, #-2]!
    // 0xe82a50: strh            w16, [x0, #-2]!
    // 0xe82a54: tbz             w2, #1, #0xe82a60
    // 0xe82a58: ldrb            w16, [x1, #-1]!
    // 0xe82a5c: strb            w16, [x0, #-1]!
    // 0xe82a60: ands            w2, w2, #0xffffffe1
    // 0xe82a64: b.eq            #0xe82ac4
    // 0xe82a68: ldp             x16, x17, [x1, #-0x10]!
    // 0xe82a6c: stp             x16, x17, [x0, #-0x10]!
    // 0xe82a70: subs            w2, w2, #0x20
    // 0xe82a74: b.ne            #0xe82a68
    // 0xe82a78: b               #0xe82ac4
    // 0xe82a7c: tbz             w2, #4, #0xe82a88
    // 0xe82a80: ldr             x16, [x1], #8
    // 0xe82a84: str             x16, [x0], #8
    // 0xe82a88: tbz             w2, #3, #0xe82a94
    // 0xe82a8c: ldr             w16, [x1], #4
    // 0xe82a90: str             w16, [x0], #4
    // 0xe82a94: tbz             w2, #2, #0xe82aa0
    // 0xe82a98: ldrh            w16, [x1], #2
    // 0xe82a9c: strh            w16, [x0], #2
    // 0xe82aa0: tbz             w2, #1, #0xe82aac
    // 0xe82aa4: ldrb            w16, [x1], #1
    // 0xe82aa8: strb            w16, [x0], #1
    // 0xe82aac: ands            w2, w2, #0xffffffe1
    // 0xe82ab0: b.eq            #0xe82ac4
    // 0xe82ab4: ldp             x16, x17, [x1], #0x10
    // 0xe82ab8: stp             x16, x17, [x0], #0x10
    // 0xe82abc: subs            w2, w2, #0x20
    // 0xe82ac0: b.ne            #0xe82ab4
    // 0xe82ac4: b               #0xe82b10
    // 0xe82ac8: LoadField: r0 = r20->field_7
    //     0xe82ac8: ldur            x0, [x20, #7]
    // 0xe82acc: LoadField: r1 = r3->field_7
    //     0xe82acc: ldur            x1, [x3, #7]
    // 0xe82ad0: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe82ad0: mov             x2, THR
    //     0xe82ad4: ldr             x9, [x2, #0x658]
    //     0xe82ad8: mov             x2, x4
    //     0xe82adc: mov             x17, fp
    //     0xe82ae0: str             fp, [SP, #-8]!
    //     0xe82ae4: mov             fp, SP
    //     0xe82ae8: and             SP, SP, #0xfffffffffffffff0
    //     0xe82aec: mov             x19, sp
    //     0xe82af0: mov             sp, SP
    //     0xe82af4: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe82af8: blr             x9
    //     0xe82afc: movz            x16, #0x8
    //     0xe82b00: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe82b04: mov             sp, x19
    //     0xe82b08: mov             SP, fp
    //     0xe82b0c: ldr             fp, [SP], #8
    // 0xe82b10: r0 = 1
    //     0xe82b10: movz            x0, #0x1
    // 0xe82b14: ArrayStore: r20[15] = r0  ; TypeUnknown_1
    //     0xe82b14: strb            w0, [x20, #0x26]
    // 0xe82b18: b               #0xe82be4
    // 0xe82b1c: ldur            x1, [fp, #-8]
    // 0xe82b20: mov             x2, x20
    // 0xe82b24: r0 = _gHASH()
    //     0xe82b24: bl              #0xe82bfc  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_gHASH
    // 0xe82b28: ldur            x0, [fp, #-0x18]
    // 0xe82b2c: lsl             x1, x0, #3
    // 0xe82b30: ubfx            x1, x1, #0, #0x20
    // 0xe82b34: stur            x1, [fp, #-0x18]
    // 0xe82b38: r4 = 8
    //     0xe82b38: movz            x4, #0x8
    // 0xe82b3c: r0 = AllocateUint32Array()
    //     0xe82b3c: bl              #0xec1c2c  ; AllocateUint32ArrayStub
    // 0xe82b40: mov             x1, x0
    // 0xe82b44: ldur            x0, [fp, #-0x18]
    // 0xe82b48: stur            x1, [fp, #-0x10]
    // 0xe82b4c: ArrayStore: r1[0] = r0  ; List_4
    //     0xe82b4c: stur            w0, [x1, #0x17]
    // 0xe82b50: r0 = _ByteBuffer()
    //     0xe82b50: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0xe82b54: mov             x1, x0
    // 0xe82b58: ldur            x0, [fp, #-0x10]
    // 0xe82b5c: StoreField: r1->field_7 = r0
    //     0xe82b5c: stur            w0, [x1, #7]
    // 0xe82b60: stp             NULL, xzr, [SP]
    // 0xe82b64: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0xe82b64: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0xe82b68: r0 = asUint8List()
    //     0xe82b68: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0xe82b6c: mov             x1, x0
    // 0xe82b70: r0 = reversed()
    //     0xe82b70: bl              #0x6ec7d8  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::reversed
    // 0xe82b74: LoadField: r1 = r0->field_7
    //     0xe82b74: ldur            w1, [x0, #7]
    // 0xe82b78: DecompressPointer r1
    //     0xe82b78: add             x1, x1, HEAP, lsl #32
    // 0xe82b7c: mov             x2, x0
    // 0xe82b80: r0 = _GrowableList.of()
    //     0xe82b80: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe82b84: stur            x0, [fp, #-0x28]
    // 0xe82b88: LoadField: r4 = r0->field_b
    //     0xe82b88: ldur            w4, [x0, #0xb]
    // 0xe82b8c: stur            x4, [fp, #-0x10]
    // 0xe82b90: r5 = LoadInt32Instr(r4)
    //     0xe82b90: sbfx            x5, x4, #1, #0x1f
    // 0xe82b94: stur            x5, [fp, #-0x18]
    // 0xe82b98: tbz             x5, #0x3f, #0xe82bb0
    // 0xe82b9c: mov             x2, x4
    // 0xe82ba0: mov             x3, x5
    // 0xe82ba4: r1 = 0
    //     0xe82ba4: movz            x1, #0
    // 0xe82ba8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe82ba8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe82bac: r0 = checkValidRange()
    //     0xe82bac: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe82bb0: ldur            x4, [fp, #-0x10]
    // 0xe82bb4: r0 = AllocateUint8Array()
    //     0xe82bb4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe82bb8: mov             x1, x0
    // 0xe82bbc: ldur            x3, [fp, #-0x18]
    // 0xe82bc0: ldur            x5, [fp, #-0x28]
    // 0xe82bc4: r2 = 0
    //     0xe82bc4: movz            x2, #0
    // 0xe82bc8: r6 = 0
    //     0xe82bc8: movz            x6, #0
    // 0xe82bcc: stur            x0, [fp, #-0x10]
    // 0xe82bd0: r0 = _slowSetRange()
    //     0xe82bd0: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xe82bd4: ldur            x1, [fp, #-8]
    // 0xe82bd8: ldur            x2, [fp, #-0x20]
    // 0xe82bdc: ldur            x3, [fp, #-0x10]
    // 0xe82be0: r0 = _gHASHBlock()
    //     0xe82be0: bl              #0xe8228c  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_gHASHBlock
    // 0xe82be4: ldur            x0, [fp, #-0x20]
    // 0xe82be8: LeaveFrame
    //     0xe82be8: mov             SP, fp
    //     0xe82bec: ldp             fp, lr, [SP], #0x10
    // 0xe82bf0: ret
    //     0xe82bf0: ret             
    // 0xe82bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82bf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82bf8: b               #0xe829d0
  }
  _ _gHASH(/* No info */) {
    // ** addr: 0xe82bfc, size: 0x3c0
    // 0xe82bfc: EnterFrame
    //     0xe82bfc: stp             fp, lr, [SP, #-0x10]!
    //     0xe82c00: mov             fp, SP
    // 0xe82c04: AllocStack(0x68)
    //     0xe82c04: sub             SP, SP, #0x68
    // 0xe82c08: SetupParameters(GCMBlockCipher this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xe82c08: mov             x0, x1
    //     0xe82c0c: stur            x1, [fp, #-8]
    //     0xe82c10: mov             x1, x3
    //     0xe82c14: stur            x2, [fp, #-0x10]
    //     0xe82c18: stur            x3, [fp, #-0x18]
    // 0xe82c1c: CheckStackOverflow
    //     0xe82c1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe82c20: cmp             SP, x16
    //     0xe82c24: b.ls            #0xe82f88
    // 0xe82c28: r4 = 32
    //     0xe82c28: movz            x4, #0x20
    // 0xe82c2c: r0 = AllocateUint8Array()
    //     0xe82c2c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe82c30: mov             x3, x0
    // 0xe82c34: ldur            x0, [fp, #-0x18]
    // 0xe82c38: stur            x3, [fp, #-0x48]
    // 0xe82c3c: LoadField: r1 = r0->field_13
    //     0xe82c3c: ldur            w1, [x0, #0x13]
    // 0xe82c40: r4 = LoadInt32Instr(r1)
    //     0xe82c40: sbfx            x4, x1, #1, #0x1f
    // 0xe82c44: stur            x4, [fp, #-0x40]
    // 0xe82c48: mov             x5, THR
    // 0xe82c4c: ldur            x6, [fp, #-0x10]
    // 0xe82c50: stur            x5, [fp, #-0x38]
    // 0xe82c54: LoadField: r1 = r6->field_13
    //     0xe82c54: ldur            w1, [x6, #0x13]
    // 0xe82c58: r7 = LoadInt32Instr(r1)
    //     0xe82c58: sbfx            x7, x1, #1, #0x1f
    // 0xe82c5c: stur            x7, [fp, #-0x30]
    // 0xe82c60: r9 = 0
    //     0xe82c60: movz            x9, #0
    // 0xe82c64: ldur            x8, [fp, #-8]
    // 0xe82c68: stur            x9, [fp, #-0x28]
    // 0xe82c6c: CheckStackOverflow
    //     0xe82c6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe82c70: cmp             SP, x16
    //     0xe82c74: b.ls            #0xe82f90
    // 0xe82c78: cmp             x9, x4
    // 0xe82c7c: b.ge            #0xe82f78
    // 0xe82c80: add             x10, x9, #0x10
    // 0xe82c84: stur            x10, [fp, #-0x20]
    // 0xe82c88: cmp             x10, x4
    // 0xe82c8c: b.le            #0xe82c98
    // 0xe82c90: mov             x1, x4
    // 0xe82c94: b               #0xe82cac
    // 0xe82c98: cmp             x10, x4
    // 0xe82c9c: b.ge            #0xe82ca8
    // 0xe82ca0: mov             x1, x10
    // 0xe82ca4: b               #0xe82cac
    // 0xe82ca8: mov             x1, x10
    // 0xe82cac: lsl             x2, x1, #1
    // 0xe82cb0: str             x2, [SP]
    // 0xe82cb4: mov             x1, x0
    // 0xe82cb8: mov             x2, x9
    // 0xe82cbc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe82cbc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe82cc0: r0 = sublist()
    //     0xe82cc0: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xe82cc4: stur            x0, [fp, #-0x60]
    // 0xe82cc8: LoadField: r4 = r0->field_13
    //     0xe82cc8: ldur            w4, [x0, #0x13]
    // 0xe82ccc: stur            x4, [fp, #-0x58]
    // 0xe82cd0: r5 = LoadInt32Instr(r4)
    //     0xe82cd0: sbfx            x5, x4, #1, #0x1f
    // 0xe82cd4: stur            x5, [fp, #-0x50]
    // 0xe82cd8: tbnz            x5, #0x3f, #0xe82ce4
    // 0xe82cdc: cmp             x5, #0x10
    // 0xe82ce0: b.le            #0xe82cf8
    // 0xe82ce4: mov             x2, x4
    // 0xe82ce8: r1 = 0
    //     0xe82ce8: movz            x1, #0
    // 0xe82cec: r3 = 16
    //     0xe82cec: movz            x3, #0x10
    // 0xe82cf0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe82cf0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe82cf4: r0 = checkValidRange()
    //     0xe82cf4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe82cf8: ldur            x2, [fp, #-0x50]
    // 0xe82cfc: cbnz            x2, #0xe82d0c
    // 0xe82d00: ldur            x20, [fp, #-0x48]
    // 0xe82d04: ldur            x23, [fp, #-0x38]
    // 0xe82d08: b               #0xe82e3c
    // 0xe82d0c: ldur            x0, [fp, #-0x58]
    // 0xe82d10: cmp             w0, #0x800
    // 0xe82d14: b.ge            #0xe82dec
    // 0xe82d18: ldur            x1, [fp, #-0x60]
    // 0xe82d1c: ldur            x20, [fp, #-0x48]
    // 0xe82d20: mov             x3, x0
    // 0xe82d24: add             x2, x1, #0x17
    // 0xe82d28: add             x0, x20, #0x17
    // 0xe82d2c: cbz             x3, #0xe82de4
    // 0xe82d30: cmp             x0, x2
    // 0xe82d34: b.ls            #0xe82d9c
    // 0xe82d38: sxtw            x3, w3
    // 0xe82d3c: add             x16, x2, x3, asr #1
    // 0xe82d40: cmp             x0, x16
    // 0xe82d44: b.hs            #0xe82d9c
    // 0xe82d48: mov             x2, x16
    // 0xe82d4c: add             x0, x0, x3, asr #1
    // 0xe82d50: tbz             w3, #4, #0xe82d5c
    // 0xe82d54: ldr             x16, [x2, #-8]!
    // 0xe82d58: str             x16, [x0, #-8]!
    // 0xe82d5c: tbz             w3, #3, #0xe82d68
    // 0xe82d60: ldr             w16, [x2, #-4]!
    // 0xe82d64: str             w16, [x0, #-4]!
    // 0xe82d68: tbz             w3, #2, #0xe82d74
    // 0xe82d6c: ldrh            w16, [x2, #-2]!
    // 0xe82d70: strh            w16, [x0, #-2]!
    // 0xe82d74: tbz             w3, #1, #0xe82d80
    // 0xe82d78: ldrb            w16, [x2, #-1]!
    // 0xe82d7c: strb            w16, [x0, #-1]!
    // 0xe82d80: ands            w3, w3, #0xffffffe1
    // 0xe82d84: b.eq            #0xe82de4
    // 0xe82d88: ldp             x16, x17, [x2, #-0x10]!
    // 0xe82d8c: stp             x16, x17, [x0, #-0x10]!
    // 0xe82d90: subs            w3, w3, #0x20
    // 0xe82d94: b.ne            #0xe82d88
    // 0xe82d98: b               #0xe82de4
    // 0xe82d9c: tbz             w3, #4, #0xe82da8
    // 0xe82da0: ldr             x16, [x2], #8
    // 0xe82da4: str             x16, [x0], #8
    // 0xe82da8: tbz             w3, #3, #0xe82db4
    // 0xe82dac: ldr             w16, [x2], #4
    // 0xe82db0: str             w16, [x0], #4
    // 0xe82db4: tbz             w3, #2, #0xe82dc0
    // 0xe82db8: ldrh            w16, [x2], #2
    // 0xe82dbc: strh            w16, [x0], #2
    // 0xe82dc0: tbz             w3, #1, #0xe82dcc
    // 0xe82dc4: ldrb            w16, [x2], #1
    // 0xe82dc8: strb            w16, [x0], #1
    // 0xe82dcc: ands            w3, w3, #0xffffffe1
    // 0xe82dd0: b.eq            #0xe82de4
    // 0xe82dd4: ldp             x16, x17, [x2], #0x10
    // 0xe82dd8: stp             x16, x17, [x0], #0x10
    // 0xe82ddc: subs            w3, w3, #0x20
    // 0xe82de0: b.ne            #0xe82dd4
    // 0xe82de4: ldur            x23, [fp, #-0x38]
    // 0xe82de8: b               #0xe82e3c
    // 0xe82dec: ldur            x1, [fp, #-0x60]
    // 0xe82df0: ldur            x20, [fp, #-0x48]
    // 0xe82df4: ldur            x23, [fp, #-0x38]
    // 0xe82df8: LoadField: r0 = r20->field_7
    //     0xe82df8: ldur            x0, [x20, #7]
    // 0xe82dfc: LoadField: r3 = r1->field_7
    //     0xe82dfc: ldur            x3, [x1, #7]
    // 0xe82e00: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe82e00: ldr             x9, [x23, #0x658]
    //     0xe82e04: mov             x1, x3
    //     0xe82e08: mov             x17, fp
    //     0xe82e0c: str             fp, [SP, #-8]!
    //     0xe82e10: mov             fp, SP
    //     0xe82e14: and             SP, SP, #0xfffffffffffffff0
    //     0xe82e18: mov             x19, sp
    //     0xe82e1c: mov             sp, SP
    //     0xe82e20: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe82e24: blr             x9
    //     0xe82e28: movz            x16, #0x8
    //     0xe82e2c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe82e30: mov             sp, x19
    //     0xe82e34: mov             SP, fp
    //     0xe82e38: ldr             fp, [SP], #8
    // 0xe82e3c: ldur            x4, [fp, #-0x20]
    // 0xe82e40: ldur            x0, [fp, #-0x40]
    // 0xe82e44: cmp             x4, x0
    // 0xe82e48: b.le            #0xe82e54
    // 0xe82e4c: mov             x2, x0
    // 0xe82e50: b               #0xe82e68
    // 0xe82e54: cmp             x4, x0
    // 0xe82e58: b.ge            #0xe82e64
    // 0xe82e5c: mov             x2, x4
    // 0xe82e60: b               #0xe82e68
    // 0xe82e64: mov             x2, x4
    // 0xe82e68: ldur            x1, [fp, #-0x28]
    // 0xe82e6c: sub             x5, x2, x1
    // 0xe82e70: mov             x1, x5
    // 0xe82e74: stur            x5, [fp, #-0x50]
    // 0xe82e78: r2 = 32
    //     0xe82e78: movz            x2, #0x20
    // 0xe82e7c: r3 = 16
    //     0xe82e7c: movz            x3, #0x10
    // 0xe82e80: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe82e80: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe82e84: r0 = checkValidRange()
    //     0xe82e84: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe82e88: ldur            x0, [fp, #-0x50]
    // 0xe82e8c: cmp             x0, #0x10
    // 0xe82e90: b.ne            #0xe82e9c
    // 0xe82e94: ldur            x4, [fp, #-0x48]
    // 0xe82e98: b               #0xe82edc
    // 0xe82e9c: mov             x2, x0
    // 0xe82ea0: ldur            x4, [fp, #-0x48]
    // 0xe82ea4: CheckStackOverflow
    //     0xe82ea4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe82ea8: cmp             SP, x16
    //     0xe82eac: b.ls            #0xe82f98
    // 0xe82eb0: cmp             x2, #0x10
    // 0xe82eb4: b.ge            #0xe82edc
    // 0xe82eb8: mov             x1, x2
    // 0xe82ebc: r0 = 16
    //     0xe82ebc: movz            x0, #0x10
    // 0xe82ec0: cmp             x1, x0
    // 0xe82ec4: b.hs            #0xe82fa0
    // 0xe82ec8: ArrayStore: r4[r2] = rZR  ; TypeUnknown_1
    //     0xe82ec8: add             x0, x4, x2
    //     0xe82ecc: strb            wzr, [x0, #0x17]
    // 0xe82ed0: add             x0, x2, #1
    // 0xe82ed4: mov             x2, x0
    // 0xe82ed8: b               #0xe82ea4
    // 0xe82edc: ldur            x5, [fp, #-0x10]
    // 0xe82ee0: ldur            x6, [fp, #-0x30]
    // 0xe82ee4: r2 = 0
    //     0xe82ee4: movz            x2, #0
    // 0xe82ee8: CheckStackOverflow
    //     0xe82ee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe82eec: cmp             SP, x16
    //     0xe82ef0: b.ls            #0xe82fa4
    // 0xe82ef4: cmp             x2, x6
    // 0xe82ef8: b.ge            #0xe82f34
    // 0xe82efc: ArrayLoad: r3 = r5[r2]  ; List_1
    //     0xe82efc: add             x16, x5, x2
    //     0xe82f00: ldrb            w3, [x16, #0x17]
    // 0xe82f04: mov             x1, x2
    // 0xe82f08: r0 = 16
    //     0xe82f08: movz            x0, #0x10
    // 0xe82f0c: cmp             x1, x0
    // 0xe82f10: b.hs            #0xe82fac
    // 0xe82f14: ArrayLoad: r0 = r4[r2]  ; List_1
    //     0xe82f14: add             x16, x4, x2
    //     0xe82f18: ldrb            w0, [x16, #0x17]
    // 0xe82f1c: eor             x1, x3, x0
    // 0xe82f20: ArrayStore: r5[r2] = r1  ; TypeUnknown_1
    //     0xe82f20: add             x0, x5, x2
    //     0xe82f24: strb            w1, [x0, #0x17]
    // 0xe82f28: add             x0, x2, #1
    // 0xe82f2c: mov             x2, x0
    // 0xe82f30: b               #0xe82ee8
    // 0xe82f34: ldur            x0, [fp, #-8]
    // 0xe82f38: LoadField: r3 = r0->field_2f
    //     0xe82f38: ldur            w3, [x0, #0x2f]
    // 0xe82f3c: DecompressPointer r3
    //     0xe82f3c: add             x3, x3, HEAP, lsl #32
    // 0xe82f40: r16 = Sentinel
    //     0xe82f40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe82f44: cmp             w3, w16
    // 0xe82f48: b.eq            #0xe82fb0
    // 0xe82f4c: mov             x1, x0
    // 0xe82f50: mov             x2, x5
    // 0xe82f54: r0 = _mult()
    //     0xe82f54: bl              #0xe82350  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_mult
    // 0xe82f58: ldur            x9, [fp, #-0x20]
    // 0xe82f5c: ldur            x6, [fp, #-0x10]
    // 0xe82f60: ldur            x0, [fp, #-0x18]
    // 0xe82f64: ldur            x3, [fp, #-0x48]
    // 0xe82f68: ldur            x5, [fp, #-0x38]
    // 0xe82f6c: ldur            x4, [fp, #-0x40]
    // 0xe82f70: ldur            x7, [fp, #-0x30]
    // 0xe82f74: b               #0xe82c64
    // 0xe82f78: r0 = Null
    //     0xe82f78: mov             x0, NULL
    // 0xe82f7c: LeaveFrame
    //     0xe82f7c: mov             SP, fp
    //     0xe82f80: ldp             fp, lr, [SP], #0x10
    // 0xe82f84: ret
    //     0xe82f84: ret             
    // 0xe82f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82f8c: b               #0xe82c28
    // 0xe82f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82f90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82f94: b               #0xe82c78
    // 0xe82f98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82f98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82f9c: b               #0xe82eb0
    // 0xe82fa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe82fa0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe82fa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82fa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82fa8: b               #0xe82ef4
    // 0xe82fac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe82fac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe82fb0: r9 = _h
    //     0xe82fb0: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a18] Field <GCMBlockCipher._h@914399014>: late (offset: 0x30)
    //     0xe82fb4: ldr             x9, [x9, #0xa18]
    // 0xe82fb8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe82fb8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0xe8cf90, size: 0x98
    // 0xe8cf90: EnterFrame
    //     0xe8cf90: stp             fp, lr, [SP, #-0x10]!
    //     0xe8cf94: mov             fp, SP
    // 0xe8cf98: AllocStack(0x18)
    //     0xe8cf98: sub             SP, SP, #0x18
    // 0xe8cf9c: SetupParameters(GCMBlockCipher this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xe8cf9c: mov             x4, x1
    //     0xe8cfa0: stur            x1, [fp, #-8]
    //     0xe8cfa4: stur            x2, [fp, #-0x10]
    //     0xe8cfa8: stur            x3, [fp, #-0x18]
    // 0xe8cfac: CheckStackOverflow
    //     0xe8cfac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8cfb0: cmp             SP, x16
    //     0xe8cfb4: b.ls            #0xe8d008
    // 0xe8cfb8: LoadField: r1 = r4->field_7
    //     0xe8cfb8: ldur            w1, [x4, #7]
    // 0xe8cfbc: DecompressPointer r1
    //     0xe8cfbc: add             x1, x1, HEAP, lsl #32
    // 0xe8cfc0: r0 = LoadClassIdInstr(r1)
    //     0xe8cfc0: ldur            x0, [x1, #-1]
    //     0xe8cfc4: ubfx            x0, x0, #0xc, #0x14
    // 0xe8cfc8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe8cfc8: sub             lr, x0, #1, lsl #12
    //     0xe8cfcc: ldr             lr, [x21, lr, lsl #3]
    //     0xe8cfd0: blr             lr
    // 0xe8cfd4: mov             x1, x0
    // 0xe8cfd8: r0 = -26
    //     0xe8cfd8: movn            x0, #0x19
    // 0xe8cfdc: cbz             x1, #0xe8d010
    // 0xe8cfe0: sdiv            x2, x0, x1
    // 0xe8cfe4: ldur            x1, [fp, #-8]
    // 0xe8cfe8: StoreField: r1->field_47 = r2
    //     0xe8cfe8: stur            x2, [x1, #0x47]
    // 0xe8cfec: ldur            x2, [fp, #-0x10]
    // 0xe8cff0: ldur            x3, [fp, #-0x18]
    // 0xe8cff4: r0 = init()
    //     0xe8cff4: bl              #0xe8d028  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::init
    // 0xe8cff8: r0 = Null
    //     0xe8cff8: mov             x0, NULL
    // 0xe8cffc: LeaveFrame
    //     0xe8cffc: mov             SP, fp
    //     0xe8d000: ldp             fp, lr, [SP], #0x10
    // 0xe8d004: ret
    //     0xe8d004: ret             
    // 0xe8d008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8d008: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8d00c: b               #0xe8cfb8
    // 0xe8d010: stp             x0, x1, [SP, #-0x10]!
    // 0xe8d014: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0xe8d018: r4 = 0
    //     0xe8d018: movz            x4, #0
    // 0xe8d01c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe8d020: blr             lr
    // 0xe8d024: brk             #0
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xeaea20, size: 0x6e4
    // 0xeaea20: EnterFrame
    //     0xeaea20: stp             fp, lr, [SP, #-0x10]!
    //     0xeaea24: mov             fp, SP
    // 0xeaea28: AllocStack(0x58)
    //     0xeaea28: sub             SP, SP, #0x58
    // 0xeaea2c: SetupParameters(GCMBlockCipher this /* r1 => r6, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* r6 => r2, fp-0x30 */)
    //     0xeaea2c: mov             x4, x2
    //     0xeaea30: stur            x2, [fp, #-0x18]
    //     0xeaea34: mov             x2, x6
    //     0xeaea38: stur            x6, [fp, #-0x30]
    //     0xeaea3c: mov             x6, x1
    //     0xeaea40: stur            x1, [fp, #-0x10]
    //     0xeaea44: stur            x3, [fp, #-0x20]
    //     0xeaea48: stur            x5, [fp, #-0x28]
    // 0xeaea4c: CheckStackOverflow
    //     0xeaea4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaea50: cmp             SP, x16
    //     0xeaea54: b.ls            #0xeaf0c0
    // 0xeaea58: LoadField: r7 = r6->field_7
    //     0xeaea58: ldur            w7, [x6, #7]
    // 0xeaea5c: DecompressPointer r7
    //     0xeaea5c: add             x7, x7, HEAP, lsl #32
    // 0xeaea60: stur            x7, [fp, #-8]
    // 0xeaea64: r0 = LoadClassIdInstr(r7)
    //     0xeaea64: ldur            x0, [x7, #-1]
    //     0xeaea68: ubfx            x0, x0, #0xc, #0x14
    // 0xeaea6c: mov             x1, x7
    // 0xeaea70: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeaea70: sub             lr, x0, #1, lsl #12
    //     0xeaea74: ldr             lr, [x21, lr, lsl #3]
    //     0xeaea78: blr             lr
    // 0xeaea7c: ldur            x2, [fp, #-0x18]
    // 0xeaea80: LoadField: r1 = r2->field_13
    //     0xeaea80: ldur            w1, [x2, #0x13]
    // 0xeaea84: r3 = LoadInt32Instr(r1)
    //     0xeaea84: sbfx            x3, x1, #1, #0x1f
    // 0xeaea88: ldur            x4, [fp, #-0x20]
    // 0xeaea8c: sub             x1, x3, x4
    // 0xeaea90: cmp             x0, x1
    // 0xeaea94: b.ge            #0xeaeabc
    // 0xeaea98: ldur            x3, [fp, #-8]
    // 0xeaea9c: r0 = LoadClassIdInstr(r3)
    //     0xeaea9c: ldur            x0, [x3, #-1]
    //     0xeaeaa0: ubfx            x0, x0, #0xc, #0x14
    // 0xeaeaa4: mov             x1, x3
    // 0xeaeaa8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeaeaa8: sub             lr, x0, #1, lsl #12
    //     0xeaeaac: ldr             lr, [x21, lr, lsl #3]
    //     0xeaeab0: blr             lr
    // 0xeaeab4: mov             x4, x0
    // 0xeaeab8: b               #0xeaeac0
    // 0xeaeabc: mov             x4, x1
    // 0xeaeac0: ldur            x2, [fp, #-0x18]
    // 0xeaeac4: ldur            x3, [fp, #-8]
    // 0xeaeac8: stur            x4, [fp, #-0x38]
    // 0xeaeacc: r0 = LoadClassIdInstr(r3)
    //     0xeaeacc: ldur            x0, [x3, #-1]
    //     0xeaead0: ubfx            x0, x0, #0xc, #0x14
    // 0xeaead4: mov             x1, x3
    // 0xeaead8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeaead8: sub             lr, x0, #1, lsl #12
    //     0xeaeadc: ldr             lr, [x21, lr, lsl #3]
    //     0xeaeae0: blr             lr
    // 0xeaeae4: mov             x3, x0
    // 0xeaeae8: stur            x3, [fp, #-0x48]
    // 0xeaeaec: r0 = BoxInt64Instr(r3)
    //     0xeaeaec: sbfiz           x0, x3, #1, #0x1f
    //     0xeaeaf0: cmp             x3, x0, asr #1
    //     0xeaeaf4: b.eq            #0xeaeb00
    //     0xeaeaf8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaeafc: stur            x3, [x0, #7]
    // 0xeaeb00: mov             x4, x0
    // 0xeaeb04: ldur            x1, [fp, #-0x18]
    // 0xeaeb08: stur            x4, [fp, #-0x40]
    // 0xeaeb0c: r0 = LoadClassIdInstr(r1)
    //     0xeaeb0c: ldur            x0, [x1, #-1]
    //     0xeaeb10: ubfx            x0, x0, #0xc, #0x14
    // 0xeaeb14: ldur            x2, [fp, #-0x20]
    // 0xeaeb18: r0 = GDT[cid_x0 + 0xd3e2]()
    //     0xeaeb18: movz            x17, #0xd3e2
    //     0xeaeb1c: add             lr, x0, x17
    //     0xeaeb20: ldr             lr, [x21, lr, lsl #3]
    //     0xeaeb24: blr             lr
    // 0xeaeb28: mov             x1, x0
    // 0xeaeb2c: ldur            x2, [fp, #-0x38]
    // 0xeaeb30: r0 = take()
    //     0xeaeb30: bl              #0x7a97a4  ; [dart:_internal] SubListIterable::take
    // 0xeaeb34: stur            x0, [fp, #-0x18]
    // 0xeaeb38: str             x0, [SP]
    // 0xeaeb3c: r0 = length()
    //     0xeaeb3c: bl              #0x9132cc  ; [dart:_internal] SubListIterable::length
    // 0xeaeb40: r4 = LoadInt32Instr(r0)
    //     0xeaeb40: sbfx            x4, x0, #1, #0x1f
    //     0xeaeb44: tbz             w0, #0, #0xeaeb4c
    //     0xeaeb48: ldur            x4, [x0, #7]
    // 0xeaeb4c: stur            x4, [fp, #-0x20]
    // 0xeaeb50: tbz             x4, #0x3f, #0xeaeb5c
    // 0xeaeb54: ldur            x5, [fp, #-0x48]
    // 0xeaeb58: b               #0xeaeb68
    // 0xeaeb5c: ldur            x5, [fp, #-0x48]
    // 0xeaeb60: cmp             x4, x5
    // 0xeaeb64: b.le            #0xeaeb7c
    // 0xeaeb68: mov             x2, x0
    // 0xeaeb6c: mov             x3, x5
    // 0xeaeb70: r1 = 0
    //     0xeaeb70: movz            x1, #0
    // 0xeaeb74: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeaeb74: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeaeb78: r0 = checkValidRange()
    //     0xeaeb78: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeaeb7c: ldur            x1, [fp, #-0x10]
    // 0xeaeb80: ldur            x2, [fp, #-0x38]
    // 0xeaeb84: ldur            x3, [fp, #-0x48]
    // 0xeaeb88: ldur            x4, [fp, #-0x40]
    // 0xeaeb8c: r0 = AllocateUint8Array()
    //     0xeaeb8c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xeaeb90: mov             x1, x0
    // 0xeaeb94: ldur            x3, [fp, #-0x20]
    // 0xeaeb98: ldur            x5, [fp, #-0x18]
    // 0xeaeb9c: r2 = 0
    //     0xeaeb9c: movz            x2, #0
    // 0xeaeba0: r6 = 0
    //     0xeaeba0: movz            x6, #0
    // 0xeaeba4: stur            x0, [fp, #-0x18]
    // 0xeaeba8: r0 = _slowSetRange()
    //     0xeaeba8: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xeaebac: ldur            x3, [fp, #-0x10]
    // 0xeaebb0: LoadField: r0 = r3->field_43
    //     0xeaebb0: ldur            w0, [x3, #0x43]
    // 0xeaebb4: DecompressPointer r0
    //     0xeaebb4: add             x0, x0, HEAP, lsl #32
    // 0xeaebb8: r16 = Sentinel
    //     0xeaebb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeaebbc: cmp             w0, w16
    // 0xeaebc0: b.eq            #0xeaf0c8
    // 0xeaebc4: r1 = LoadInt32Instr(r0)
    //     0xeaebc4: sbfx            x1, x0, #1, #0x1f
    //     0xeaebc8: tbz             w0, #0, #0xeaebd0
    //     0xeaebcc: ldur            x1, [x0, #7]
    // 0xeaebd0: ldur            x4, [fp, #-0x38]
    // 0xeaebd4: add             x2, x1, x4
    // 0xeaebd8: r0 = BoxInt64Instr(r2)
    //     0xeaebd8: sbfiz           x0, x2, #1, #0x1f
    //     0xeaebdc: cmp             x2, x0, asr #1
    //     0xeaebe0: b.eq            #0xeaebec
    //     0xeaebe4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaebe8: stur            x2, [x0, #7]
    // 0xeaebec: StoreField: r3->field_43 = r0
    //     0xeaebec: stur            w0, [x3, #0x43]
    //     0xeaebf0: tbz             w0, #0, #0xeaec0c
    //     0xeaebf4: ldurb           w16, [x3, #-1]
    //     0xeaebf8: ldurb           w17, [x0, #-1]
    //     0xeaebfc: and             x16, x17, x16, lsr #2
    //     0xeaec00: tst             x16, HEAP, lsr #32
    //     0xeaec04: b.eq            #0xeaec0c
    //     0xeaec08: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xeaec0c: LoadField: r2 = r3->field_37
    //     0xeaec0c: ldur            w2, [x3, #0x37]
    // 0xeaec10: DecompressPointer r2
    //     0xeaec10: add             x2, x2, HEAP, lsl #32
    // 0xeaec14: r16 = Sentinel
    //     0xeaec14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeaec18: cmp             w2, w16
    // 0xeaec1c: b.eq            #0xeaf0d4
    // 0xeaec20: mov             x1, x3
    // 0xeaec24: r0 = _getNextCTRBlock()
    //     0xeaec24: bl              #0xeaf104  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_getNextCTRBlock
    // 0xeaec28: ldur            x4, [fp, #-0x40]
    // 0xeaec2c: r0 = AllocateUint8Array()
    //     0xeaec2c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xeaec30: mov             x4, x0
    // 0xeaec34: ldur            x0, [fp, #-0x48]
    // 0xeaec38: stur            x4, [fp, #-0x50]
    // 0xeaec3c: tbnz            x0, #0x3f, #0xeaec48
    // 0xeaec40: cmp             x0, x0
    // 0xeaec44: b.le            #0xeaec5c
    // 0xeaec48: ldur            x2, [fp, #-0x40]
    // 0xeaec4c: mov             x3, x0
    // 0xeaec50: r1 = 0
    //     0xeaec50: movz            x1, #0
    // 0xeaec54: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeaec54: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeaec58: r0 = checkValidRange()
    //     0xeaec58: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeaec5c: ldur            x20, [fp, #-0x48]
    // 0xeaec60: cmp             x20, x20
    // 0xeaec64: b.lt            #0xeaf0a8
    // 0xeaec68: cbnz            x20, #0xeaec78
    // 0xeaec6c: ldur            x24, [fp, #-0x18]
    // 0xeaec70: ldur            x23, [fp, #-0x50]
    // 0xeaec74: b               #0xeaeda4
    // 0xeaec78: ldur            x0, [fp, #-0x40]
    // 0xeaec7c: cmp             w0, #0x800
    // 0xeaec80: b.ge            #0xeaed54
    // 0xeaec84: ldur            x24, [fp, #-0x18]
    // 0xeaec88: ldur            x23, [fp, #-0x50]
    // 0xeaec8c: mov             x2, x0
    // 0xeaec90: add             x1, x24, #0x17
    // 0xeaec94: add             x0, x23, #0x17
    // 0xeaec98: cbz             x2, #0xeaed50
    // 0xeaec9c: cmp             x0, x1
    // 0xeaeca0: b.ls            #0xeaed08
    // 0xeaeca4: sxtw            x2, w2
    // 0xeaeca8: add             x16, x1, x2, asr #1
    // 0xeaecac: cmp             x0, x16
    // 0xeaecb0: b.hs            #0xeaed08
    // 0xeaecb4: mov             x1, x16
    // 0xeaecb8: add             x0, x0, x2, asr #1
    // 0xeaecbc: tbz             w2, #4, #0xeaecc8
    // 0xeaecc0: ldr             x16, [x1, #-8]!
    // 0xeaecc4: str             x16, [x0, #-8]!
    // 0xeaecc8: tbz             w2, #3, #0xeaecd4
    // 0xeaeccc: ldr             w16, [x1, #-4]!
    // 0xeaecd0: str             w16, [x0, #-4]!
    // 0xeaecd4: tbz             w2, #2, #0xeaece0
    // 0xeaecd8: ldrh            w16, [x1, #-2]!
    // 0xeaecdc: strh            w16, [x0, #-2]!
    // 0xeaece0: tbz             w2, #1, #0xeaecec
    // 0xeaece4: ldrb            w16, [x1, #-1]!
    // 0xeaece8: strb            w16, [x0, #-1]!
    // 0xeaecec: ands            w2, w2, #0xffffffe1
    // 0xeaecf0: b.eq            #0xeaed50
    // 0xeaecf4: ldp             x16, x17, [x1, #-0x10]!
    // 0xeaecf8: stp             x16, x17, [x0, #-0x10]!
    // 0xeaecfc: subs            w2, w2, #0x20
    // 0xeaed00: b.ne            #0xeaecf4
    // 0xeaed04: b               #0xeaed50
    // 0xeaed08: tbz             w2, #4, #0xeaed14
    // 0xeaed0c: ldr             x16, [x1], #8
    // 0xeaed10: str             x16, [x0], #8
    // 0xeaed14: tbz             w2, #3, #0xeaed20
    // 0xeaed18: ldr             w16, [x1], #4
    // 0xeaed1c: str             w16, [x0], #4
    // 0xeaed20: tbz             w2, #2, #0xeaed2c
    // 0xeaed24: ldrh            w16, [x1], #2
    // 0xeaed28: strh            w16, [x0], #2
    // 0xeaed2c: tbz             w2, #1, #0xeaed38
    // 0xeaed30: ldrb            w16, [x1], #1
    // 0xeaed34: strb            w16, [x0], #1
    // 0xeaed38: ands            w2, w2, #0xffffffe1
    // 0xeaed3c: b.eq            #0xeaed50
    // 0xeaed40: ldp             x16, x17, [x1], #0x10
    // 0xeaed44: stp             x16, x17, [x0], #0x10
    // 0xeaed48: subs            w2, w2, #0x20
    // 0xeaed4c: b.ne            #0xeaed40
    // 0xeaed50: b               #0xeaeda4
    // 0xeaed54: ldur            x24, [fp, #-0x18]
    // 0xeaed58: ldur            x23, [fp, #-0x50]
    // 0xeaed5c: LoadField: r0 = r23->field_7
    //     0xeaed5c: ldur            x0, [x23, #7]
    // 0xeaed60: LoadField: r1 = r24->field_7
    //     0xeaed60: ldur            x1, [x24, #7]
    // 0xeaed64: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xeaed64: mov             x2, THR
    //     0xeaed68: ldr             x9, [x2, #0x658]
    //     0xeaed6c: mov             x2, x20
    //     0xeaed70: mov             x17, fp
    //     0xeaed74: str             fp, [SP, #-8]!
    //     0xeaed78: mov             fp, SP
    //     0xeaed7c: and             SP, SP, #0xfffffffffffffff0
    //     0xeaed80: mov             x19, sp
    //     0xeaed84: mov             sp, SP
    //     0xeaed88: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaed8c: blr             x9
    //     0xeaed90: movz            x16, #0x8
    //     0xeaed94: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaed98: mov             sp, x19
    //     0xeaed9c: mov             SP, fp
    //     0xeaeda0: ldr             fp, [SP], #8
    // 0xeaeda4: ldur            x2, [fp, #-0x10]
    // 0xeaeda8: LoadField: r3 = r2->field_37
    //     0xeaeda8: ldur            w3, [x2, #0x37]
    // 0xeaedac: DecompressPointer r3
    //     0xeaedac: add             x3, x3, HEAP, lsl #32
    // 0xeaedb0: LoadField: r0 = r3->field_13
    //     0xeaedb0: ldur            w0, [x3, #0x13]
    // 0xeaedb4: r4 = LoadInt32Instr(r0)
    //     0xeaedb4: sbfx            x4, x0, #1, #0x1f
    // 0xeaedb8: r5 = 0
    //     0xeaedb8: movz            x5, #0
    // 0xeaedbc: CheckStackOverflow
    //     0xeaedbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaedc0: cmp             SP, x16
    //     0xeaedc4: b.ls            #0xeaf0e0
    // 0xeaedc8: cmp             x5, x20
    // 0xeaedcc: b.ge            #0xeaee08
    // 0xeaedd0: ArrayLoad: r6 = r23[r5]  ; List_1
    //     0xeaedd0: add             x16, x23, x5
    //     0xeaedd4: ldrb            w6, [x16, #0x17]
    // 0xeaedd8: mov             x0, x4
    // 0xeaeddc: mov             x1, x5
    // 0xeaede0: cmp             x1, x0
    // 0xeaede4: b.hs            #0xeaf0e8
    // 0xeaede8: ArrayLoad: r0 = r3[r5]  ; List_1
    //     0xeaede8: add             x16, x3, x5
    //     0xeaedec: ldrb            w0, [x16, #0x17]
    // 0xeaedf0: eor             x1, x6, x0
    // 0xeaedf4: ArrayStore: r23[r5] = r1  ; TypeUnknown_1
    //     0xeaedf4: add             x0, x23, x5
    //     0xeaedf8: strb            w1, [x0, #0x17]
    // 0xeaedfc: add             x0, x5, #1
    // 0xeaee00: mov             x5, x0
    // 0xeaee04: b               #0xeaedbc
    // 0xeaee08: ldur            x3, [fp, #-0x38]
    // 0xeaee0c: ldur            x4, [fp, #-8]
    // 0xeaee10: r0 = LoadClassIdInstr(r4)
    //     0xeaee10: ldur            x0, [x4, #-1]
    //     0xeaee14: ubfx            x0, x0, #0xc, #0x14
    // 0xeaee18: mov             x1, x4
    // 0xeaee1c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeaee1c: sub             lr, x0, #1, lsl #12
    //     0xeaee20: ldr             lr, [x21, lr, lsl #3]
    //     0xeaee24: blr             lr
    // 0xeaee28: ldur            x2, [fp, #-0x38]
    // 0xeaee2c: cmp             x2, x0
    // 0xeaee30: b.ge            #0xeaee60
    // 0xeaee34: ldur            x1, [fp, #-8]
    // 0xeaee38: r0 = LoadClassIdInstr(r1)
    //     0xeaee38: ldur            x0, [x1, #-1]
    //     0xeaee3c: ubfx            x0, x0, #0xc, #0x14
    // 0xeaee40: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeaee40: sub             lr, x0, #1, lsl #12
    //     0xeaee44: ldr             lr, [x21, lr, lsl #3]
    //     0xeaee48: blr             lr
    // 0xeaee4c: ldur            x1, [fp, #-0x50]
    // 0xeaee50: ldur            x2, [fp, #-0x38]
    // 0xeaee54: mov             x3, x0
    // 0xeaee58: r5 = 0
    //     0xeaee58: movz            x5, #0
    // 0xeaee5c: r0 = fillRange()
    //     0xeaee5c: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0xeaee60: ldur            x5, [fp, #-0x30]
    // 0xeaee64: ldur            x4, [fp, #-0x38]
    // 0xeaee68: add             x6, x5, x4
    // 0xeaee6c: stur            x6, [fp, #-0x20]
    // 0xeaee70: tbz             x5, #0x3f, #0xeaee7c
    // 0xeaee74: ldur            x7, [fp, #-0x28]
    // 0xeaee78: b               #0xeaeea0
    // 0xeaee7c: cmp             x5, x6
    // 0xeaee80: b.le            #0xeaee8c
    // 0xeaee84: ldur            x7, [fp, #-0x28]
    // 0xeaee88: b               #0xeaeea0
    // 0xeaee8c: ldur            x7, [fp, #-0x28]
    // 0xeaee90: LoadField: r0 = r7->field_13
    //     0xeaee90: ldur            w0, [x7, #0x13]
    // 0xeaee94: r1 = LoadInt32Instr(r0)
    //     0xeaee94: sbfx            x1, x0, #1, #0x1f
    // 0xeaee98: cmp             x6, x1
    // 0xeaee9c: b.le            #0xeaeecc
    // 0xeaeea0: LoadField: r2 = r7->field_13
    //     0xeaeea0: ldur            w2, [x7, #0x13]
    // 0xeaeea4: r0 = BoxInt64Instr(r6)
    //     0xeaeea4: sbfiz           x0, x6, #1, #0x1f
    //     0xeaeea8: cmp             x6, x0, asr #1
    //     0xeaeeac: b.eq            #0xeaeeb8
    //     0xeaeeb0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaeeb4: stur            x6, [x0, #7]
    // 0xeaeeb8: r3 = LoadInt32Instr(r2)
    //     0xeaeeb8: sbfx            x3, x2, #1, #0x1f
    // 0xeaeebc: mov             x1, x5
    // 0xeaeec0: mov             x2, x0
    // 0xeaeec4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeaeec4: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeaeec8: r0 = checkValidRange()
    //     0xeaeec8: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeaeecc: ldur            x2, [fp, #-0x30]
    // 0xeaeed0: ldur            x0, [fp, #-0x20]
    // 0xeaeed4: ldur            x1, [fp, #-0x48]
    // 0xeaeed8: sub             x3, x0, x2
    // 0xeaeedc: cmp             x1, x3
    // 0xeaeee0: b.lt            #0xeaf0b4
    // 0xeaeee4: cbnz            x3, #0xeaeef0
    // 0xeaeee8: ldur            x20, [fp, #-0x50]
    // 0xeaeeec: b               #0xeaf058
    // 0xeaeef0: r0 = BoxInt64Instr(r3)
    //     0xeaeef0: sbfiz           x0, x3, #1, #0x1f
    //     0xeaeef4: cmp             x3, x0, asr #1
    //     0xeaeef8: b.eq            #0xeaef04
    //     0xeaeefc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaef00: stur            x3, [x0, #7]
    // 0xeaef04: mov             x4, x0
    // 0xeaef08: cmp             w4, #0x800
    // 0xeaef0c: b.ge            #0xeaeff8
    // 0xeaef10: ldur            x5, [fp, #-0x28]
    // 0xeaef14: ldur            x20, [fp, #-0x50]
    // 0xeaef18: r0 = BoxInt64Instr(r2)
    //     0xeaef18: sbfiz           x0, x2, #1, #0x1f
    //     0xeaef1c: cmp             x2, x0, asr #1
    //     0xeaef20: b.eq            #0xeaef2c
    //     0xeaef24: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaef28: stur            x2, [x0, #7]
    // 0xeaef2c: add             x2, x20, #0x17
    // 0xeaef30: sxtw            x0, w0
    // 0xeaef34: add             x1, x5, x0, asr #1
    // 0xeaef38: add             x1, x1, #0x17
    // 0xeaef3c: cbz             x4, #0xeaeff4
    // 0xeaef40: cmp             x1, x2
    // 0xeaef44: b.ls            #0xeaefac
    // 0xeaef48: sxtw            x4, w4
    // 0xeaef4c: add             x16, x2, x4, asr #1
    // 0xeaef50: cmp             x1, x16
    // 0xeaef54: b.hs            #0xeaefac
    // 0xeaef58: mov             x2, x16
    // 0xeaef5c: add             x1, x1, x4, asr #1
    // 0xeaef60: tbz             w4, #4, #0xeaef6c
    // 0xeaef64: ldr             x16, [x2, #-8]!
    // 0xeaef68: str             x16, [x1, #-8]!
    // 0xeaef6c: tbz             w4, #3, #0xeaef78
    // 0xeaef70: ldr             w16, [x2, #-4]!
    // 0xeaef74: str             w16, [x1, #-4]!
    // 0xeaef78: tbz             w4, #2, #0xeaef84
    // 0xeaef7c: ldrh            w16, [x2, #-2]!
    // 0xeaef80: strh            w16, [x1, #-2]!
    // 0xeaef84: tbz             w4, #1, #0xeaef90
    // 0xeaef88: ldrb            w16, [x2, #-1]!
    // 0xeaef8c: strb            w16, [x1, #-1]!
    // 0xeaef90: ands            w4, w4, #0xffffffe1
    // 0xeaef94: b.eq            #0xeaeff4
    // 0xeaef98: ldp             x16, x17, [x2, #-0x10]!
    // 0xeaef9c: stp             x16, x17, [x1, #-0x10]!
    // 0xeaefa0: subs            w4, w4, #0x20
    // 0xeaefa4: b.ne            #0xeaef98
    // 0xeaefa8: b               #0xeaeff4
    // 0xeaefac: tbz             w4, #4, #0xeaefb8
    // 0xeaefb0: ldr             x16, [x2], #8
    // 0xeaefb4: str             x16, [x1], #8
    // 0xeaefb8: tbz             w4, #3, #0xeaefc4
    // 0xeaefbc: ldr             w16, [x2], #4
    // 0xeaefc0: str             w16, [x1], #4
    // 0xeaefc4: tbz             w4, #2, #0xeaefd0
    // 0xeaefc8: ldrh            w16, [x2], #2
    // 0xeaefcc: strh            w16, [x1], #2
    // 0xeaefd0: tbz             w4, #1, #0xeaefdc
    // 0xeaefd4: ldrb            w16, [x2], #1
    // 0xeaefd8: strb            w16, [x1], #1
    // 0xeaefdc: ands            w4, w4, #0xffffffe1
    // 0xeaefe0: b.eq            #0xeaeff4
    // 0xeaefe4: ldp             x16, x17, [x2], #0x10
    // 0xeaefe8: stp             x16, x17, [x1], #0x10
    // 0xeaefec: subs            w4, w4, #0x20
    // 0xeaeff0: b.ne            #0xeaefe4
    // 0xeaeff4: b               #0xeaf058
    // 0xeaeff8: ldur            x5, [fp, #-0x28]
    // 0xeaeffc: ldur            x20, [fp, #-0x50]
    // 0xeaf000: LoadField: r0 = r5->field_7
    //     0xeaf000: ldur            x0, [x5, #7]
    // 0xeaf004: add             x1, x0, x2
    // 0xeaf008: LoadField: r0 = r20->field_7
    //     0xeaf008: ldur            x0, [x20, #7]
    // 0xeaf00c: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xeaf00c: mov             x2, THR
    //     0xeaf010: ldr             x9, [x2, #0x658]
    //     0xeaf014: mov             x16, x0
    //     0xeaf018: mov             x0, x1
    //     0xeaf01c: mov             x1, x16
    //     0xeaf020: mov             x2, x3
    //     0xeaf024: mov             x17, fp
    //     0xeaf028: str             fp, [SP, #-8]!
    //     0xeaf02c: mov             fp, SP
    //     0xeaf030: and             SP, SP, #0xfffffffffffffff0
    //     0xeaf034: mov             x19, sp
    //     0xeaf038: mov             sp, SP
    //     0xeaf03c: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaf040: blr             x9
    //     0xeaf044: movz            x16, #0x8
    //     0xeaf048: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeaf04c: mov             sp, x19
    //     0xeaf050: mov             SP, fp
    //     0xeaf054: ldr             fp, [SP], #8
    // 0xeaf058: ldur            x1, [fp, #-0x10]
    // 0xeaf05c: LoadField: r0 = r1->field_b
    //     0xeaf05c: ldur            w0, [x1, #0xb]
    // 0xeaf060: DecompressPointer r0
    //     0xeaf060: add             x0, x0, HEAP, lsl #32
    // 0xeaf064: r16 = Sentinel
    //     0xeaf064: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeaf068: cmp             w0, w16
    // 0xeaf06c: b.eq            #0xeaf0ec
    // 0xeaf070: tbnz            w0, #4, #0xeaf07c
    // 0xeaf074: mov             x3, x20
    // 0xeaf078: b               #0xeaf080
    // 0xeaf07c: ldur            x3, [fp, #-0x18]
    // 0xeaf080: LoadField: r2 = r1->field_3f
    //     0xeaf080: ldur            w2, [x1, #0x3f]
    // 0xeaf084: DecompressPointer r2
    //     0xeaf084: add             x2, x2, HEAP, lsl #32
    // 0xeaf088: r16 = Sentinel
    //     0xeaf088: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeaf08c: cmp             w2, w16
    // 0xeaf090: b.eq            #0xeaf0f8
    // 0xeaf094: r0 = _gHASHBlock()
    //     0xeaf094: bl              #0xe8228c  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_gHASHBlock
    // 0xeaf098: ldur            x0, [fp, #-0x38]
    // 0xeaf09c: LeaveFrame
    //     0xeaf09c: mov             SP, fp
    //     0xeaf0a0: ldp             fp, lr, [SP], #0x10
    // 0xeaf0a4: ret
    //     0xeaf0a4: ret             
    // 0xeaf0a8: r0 = tooFew()
    //     0xeaf0a8: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xeaf0ac: r0 = Throw()
    //     0xeaf0ac: bl              #0xec04b8  ; ThrowStub
    // 0xeaf0b0: brk             #0
    // 0xeaf0b4: r0 = tooFew()
    //     0xeaf0b4: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xeaf0b8: r0 = Throw()
    //     0xeaf0b8: bl              #0xec04b8  ; ThrowStub
    // 0xeaf0bc: brk             #0
    // 0xeaf0c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf0c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf0c4: b               #0xeaea58
    // 0xeaf0c8: r9 = _processedBytes
    //     0xeaf0c8: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a38] Field <GCMBlockCipher._processedBytes@914399014>: late (offset: 0x44)
    //     0xeaf0cc: ldr             x9, [x9, #0xa38]
    // 0xeaf0d0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeaf0d0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeaf0d4: r9 = _e
    //     0xeaf0d4: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a58] Field <GCMBlockCipher._e@914399014>: late (offset: 0x38)
    //     0xeaf0d8: ldr             x9, [x9, #0xa58]
    // 0xeaf0dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeaf0dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeaf0e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf0e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf0e4: b               #0xeaedc8
    // 0xeaf0e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaf0e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaf0ec: r9 = _forEncryption
    //     0xeaf0ec: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a48] Field <BaseAEADBlockCipher._forEncryption@2660101045>: late (offset: 0xc)
    //     0xeaf0f0: ldr             x9, [x9, #0xa48]
    // 0xeaf0f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeaf0f4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeaf0f8: r9 = _x
    //     0xeaf0f8: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a10] Field <GCMBlockCipher._x@914399014>: late (offset: 0x40)
    //     0xeaf0fc: ldr             x9, [x9, #0xa10]
    // 0xeaf100: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeaf100: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _getNextCTRBlock(/* No info */) {
    // ** addr: 0xeaf104, size: 0x140
    // 0xeaf104: EnterFrame
    //     0xeaf104: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf108: mov             fp, SP
    // 0xeaf10c: mov             x3, x2
    // 0xeaf110: mov             x2, x1
    // 0xeaf114: CheckStackOverflow
    //     0xeaf114: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf118: cmp             SP, x16
    //     0xeaf11c: b.ls            #0xeaf21c
    // 0xeaf120: LoadField: r0 = r2->field_47
    //     0xeaf120: ldur            x0, [x2, #0x47]
    // 0xeaf124: cbz             x0, #0xeaf1fc
    // 0xeaf128: sub             x1, x0, #1
    // 0xeaf12c: StoreField: r2->field_47 = r1
    //     0xeaf12c: stur            x1, [x2, #0x47]
    // 0xeaf130: LoadField: r4 = r2->field_33
    //     0xeaf130: ldur            w4, [x2, #0x33]
    // 0xeaf134: DecompressPointer r4
    //     0xeaf134: add             x4, x4, HEAP, lsl #32
    // 0xeaf138: r16 = Sentinel
    //     0xeaf138: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeaf13c: cmp             w4, w16
    // 0xeaf140: b.eq            #0xeaf224
    // 0xeaf144: LoadField: r0 = r4->field_13
    //     0xeaf144: ldur            w0, [x4, #0x13]
    // 0xeaf148: r5 = LoadInt32Instr(r0)
    //     0xeaf148: sbfx            x5, x0, #1, #0x1f
    // 0xeaf14c: mov             x0, x5
    // 0xeaf150: r1 = 15
    //     0xeaf150: movz            x1, #0xf
    // 0xeaf154: cmp             x1, x0
    // 0xeaf158: b.hs            #0xeaf230
    // 0xeaf15c: ArrayLoad: r0 = r4[15]  ; TypedUnsigned_1
    //     0xeaf15c: ldrb            w0, [x4, #0x26]
    // 0xeaf160: add             x1, x0, #1
    // 0xeaf164: ArrayStore: r4[15] = r1  ; TypeUnknown_1
    //     0xeaf164: strb            w1, [x4, #0x26]
    // 0xeaf168: r6 = 15
    //     0xeaf168: movz            x6, #0xf
    // 0xeaf16c: CheckStackOverflow
    //     0xeaf16c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf170: cmp             SP, x16
    //     0xeaf174: b.ls            #0xeaf234
    // 0xeaf178: cmp             x6, #0xc
    // 0xeaf17c: b.lt            #0xeaf1e0
    // 0xeaf180: mov             x0, x5
    // 0xeaf184: mov             x1, x6
    // 0xeaf188: cmp             x1, x0
    // 0xeaf18c: b.hs            #0xeaf23c
    // 0xeaf190: ArrayLoad: r0 = r4[r6]  ; List_1
    //     0xeaf190: add             x16, x4, x6
    //     0xeaf194: ldrb            w0, [x16, #0x17]
    // 0xeaf198: cbnz            x0, #0xeaf1e0
    // 0xeaf19c: ArrayStore: r4[r6] = rZR  ; TypeUnknown_1
    //     0xeaf19c: add             x0, x4, x6
    //     0xeaf1a0: strb            wzr, [x0, #0x17]
    // 0xeaf1a4: cmp             x6, #0xc
    // 0xeaf1a8: b.le            #0xeaf1d4
    // 0xeaf1ac: sub             x7, x6, #1
    // 0xeaf1b0: mov             x0, x5
    // 0xeaf1b4: mov             x1, x7
    // 0xeaf1b8: cmp             x1, x0
    // 0xeaf1bc: b.hs            #0xeaf240
    // 0xeaf1c0: ArrayLoad: r0 = r4[r7]  ; List_1
    //     0xeaf1c0: add             x16, x4, x7
    //     0xeaf1c4: ldrb            w0, [x16, #0x17]
    // 0xeaf1c8: add             x1, x0, #1
    // 0xeaf1cc: ArrayStore: r4[r7] = r1  ; TypeUnknown_1
    //     0xeaf1cc: add             x0, x4, x7
    //     0xeaf1d0: strb            w1, [x0, #0x17]
    // 0xeaf1d4: sub             x0, x6, #1
    // 0xeaf1d8: mov             x6, x0
    // 0xeaf1dc: b               #0xeaf16c
    // 0xeaf1e0: mov             x1, x2
    // 0xeaf1e4: mov             x2, x4
    // 0xeaf1e8: r0 = _computeE()
    //     0xeaf1e8: bl              #0xe8294c  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_computeE
    // 0xeaf1ec: r0 = Null
    //     0xeaf1ec: mov             x0, NULL
    // 0xeaf1f0: LeaveFrame
    //     0xeaf1f0: mov             SP, fp
    //     0xeaf1f4: ldp             fp, lr, [SP], #0x10
    // 0xeaf1f8: ret
    //     0xeaf1f8: ret             
    // 0xeaf1fc: r0 = StateError()
    //     0xeaf1fc: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xeaf200: mov             x1, x0
    // 0xeaf204: r0 = "Attempt to process too many blocks"
    //     0xeaf204: add             x0, PP, #0x20, lsl #12  ; [pp+0x20a60] "Attempt to process too many blocks"
    //     0xeaf208: ldr             x0, [x0, #0xa60]
    // 0xeaf20c: StoreField: r1->field_b = r0
    //     0xeaf20c: stur            w0, [x1, #0xb]
    // 0xeaf210: mov             x0, x1
    // 0xeaf214: r0 = Throw()
    //     0xeaf214: bl              #0xec04b8  ; ThrowStub
    // 0xeaf218: brk             #0
    // 0xeaf21c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf21c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf220: b               #0xeaf120
    // 0xeaf224: r9 = _counter
    //     0xeaf224: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a68] Field <GCMBlockCipher._counter@914399014>: late (offset: 0x34)
    //     0xeaf228: ldr             x9, [x9, #0xa68]
    // 0xeaf22c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeaf22c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeaf230: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaf230: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaf234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf234: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf238: b               #0xeaf178
    // 0xeaf23c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaf23c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaf240: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaf240: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ doFinal(/* No info */) {
    // ** addr: 0xeb3bac, size: 0x4f8
    // 0xeb3bac: EnterFrame
    //     0xeb3bac: stp             fp, lr, [SP, #-0x10]!
    //     0xeb3bb0: mov             fp, SP
    // 0xeb3bb4: AllocStack(0x50)
    //     0xeb3bb4: sub             SP, SP, #0x50
    // 0xeb3bb8: SetupParameters(GCMBlockCipher this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r5, fp-0x10 */, dynamic _ /* r3 => r6, fp-0x18 */)
    //     0xeb3bb8: mov             x0, x1
    //     0xeb3bbc: mov             x5, x2
    //     0xeb3bc0: mov             x6, x3
    //     0xeb3bc4: stur            x1, [fp, #-8]
    //     0xeb3bc8: stur            x2, [fp, #-0x10]
    //     0xeb3bcc: stur            x3, [fp, #-0x18]
    // 0xeb3bd0: CheckStackOverflow
    //     0xeb3bd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb3bd4: cmp             SP, x16
    //     0xeb3bd8: b.ls            #0xeb4048
    // 0xeb3bdc: mov             x1, x0
    // 0xeb3be0: r0 = remainingInput()
    //     0xeb3be0: bl              #0xeb42ec  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::remainingInput
    // 0xeb3be4: LoadField: r1 = r0->field_13
    //     0xeb3be4: ldur            w1, [x0, #0x13]
    // 0xeb3be8: cbz             w1, #0xeb3c10
    // 0xeb3bec: ldur            x1, [fp, #-8]
    // 0xeb3bf0: r0 = remainingInput()
    //     0xeb3bf0: bl              #0xeb42ec  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::remainingInput
    // 0xeb3bf4: ldur            x1, [fp, #-8]
    // 0xeb3bf8: mov             x2, x0
    // 0xeb3bfc: ldur            x5, [fp, #-0x10]
    // 0xeb3c00: ldur            x6, [fp, #-0x18]
    // 0xeb3c04: r3 = 0
    //     0xeb3c04: movz            x3, #0
    // 0xeb3c08: r0 = processBlock()
    //     0xeb3c08: bl              #0xeaea20  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::processBlock
    // 0xeb3c0c: b               #0xeb3c14
    // 0xeb3c10: r0 = 0
    //     0xeb3c10: movz            x0, #0
    // 0xeb3c14: ldur            x1, [fp, #-8]
    // 0xeb3c18: stur            x0, [fp, #-0x28]
    // 0xeb3c1c: LoadField: r2 = r1->field_1b
    //     0xeb3c1c: ldur            w2, [x1, #0x1b]
    // 0xeb3c20: DecompressPointer r2
    //     0xeb3c20: add             x2, x2, HEAP, lsl #32
    // 0xeb3c24: r16 = Sentinel
    //     0xeb3c24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb3c28: cmp             w2, w16
    // 0xeb3c2c: b.eq            #0xeb4050
    // 0xeb3c30: LoadField: r3 = r2->field_13
    //     0xeb3c30: ldur            w3, [x2, #0x13]
    // 0xeb3c34: r2 = LoadInt32Instr(r3)
    //     0xeb3c34: sbfx            x2, x3, #1, #0x1f
    // 0xeb3c38: lsl             x3, x2, #3
    // 0xeb3c3c: ubfx            x3, x3, #0, #0x20
    // 0xeb3c40: stur            x3, [fp, #-0x20]
    // 0xeb3c44: r4 = 8
    //     0xeb3c44: movz            x4, #0x8
    // 0xeb3c48: r0 = AllocateUint32Array()
    //     0xeb3c48: bl              #0xec1c2c  ; AllocateUint32ArrayStub
    // 0xeb3c4c: mov             x1, x0
    // 0xeb3c50: ldur            x0, [fp, #-0x20]
    // 0xeb3c54: stur            x1, [fp, #-0x30]
    // 0xeb3c58: StoreField: r1->field_1f = r0
    //     0xeb3c58: stur            w0, [x1, #0x1f]
    // 0xeb3c5c: ldur            x0, [fp, #-8]
    // 0xeb3c60: LoadField: r2 = r0->field_43
    //     0xeb3c60: ldur            w2, [x0, #0x43]
    // 0xeb3c64: DecompressPointer r2
    //     0xeb3c64: add             x2, x2, HEAP, lsl #32
    // 0xeb3c68: r16 = Sentinel
    //     0xeb3c68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb3c6c: cmp             w2, w16
    // 0xeb3c70: b.eq            #0xeb405c
    // 0xeb3c74: r3 = LoadInt32Instr(r2)
    //     0xeb3c74: sbfx            x3, x2, #1, #0x1f
    //     0xeb3c78: tbz             w2, #0, #0xeb3c80
    //     0xeb3c7c: ldur            x3, [x2, #7]
    // 0xeb3c80: lsl             x2, x3, #3
    // 0xeb3c84: ubfx            x2, x2, #0, #0x20
    // 0xeb3c88: ArrayStore: r1[0] = r2  ; List_4
    //     0xeb3c88: stur            w2, [x1, #0x17]
    // 0xeb3c8c: r0 = _ByteBuffer()
    //     0xeb3c8c: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0xeb3c90: mov             x1, x0
    // 0xeb3c94: ldur            x0, [fp, #-0x30]
    // 0xeb3c98: StoreField: r1->field_7 = r0
    //     0xeb3c98: stur            w0, [x1, #7]
    // 0xeb3c9c: stp             NULL, xzr, [SP]
    // 0xeb3ca0: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0xeb3ca0: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0xeb3ca4: r0 = asUint8List()
    //     0xeb3ca4: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0xeb3ca8: r1 = <int>
    //     0xeb3ca8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeb3cac: stur            x0, [fp, #-0x30]
    // 0xeb3cb0: r0 = ReversedListIterable()
    //     0xeb3cb0: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xeb3cb4: mov             x1, x0
    // 0xeb3cb8: ldur            x0, [fp, #-0x30]
    // 0xeb3cbc: StoreField: r1->field_b = r0
    //     0xeb3cbc: stur            w0, [x1, #0xb]
    // 0xeb3cc0: mov             x2, x1
    // 0xeb3cc4: r1 = <int>
    //     0xeb3cc4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xeb3cc8: r0 = _GrowableList._ofEfficientLengthIterable()
    //     0xeb3cc8: bl              #0x60b858  ; [dart:core] _GrowableList::_GrowableList._ofEfficientLengthIterable
    // 0xeb3ccc: stur            x0, [fp, #-0x38]
    // 0xeb3cd0: LoadField: r4 = r0->field_b
    //     0xeb3cd0: ldur            w4, [x0, #0xb]
    // 0xeb3cd4: stur            x4, [fp, #-0x30]
    // 0xeb3cd8: r5 = LoadInt32Instr(r4)
    //     0xeb3cd8: sbfx            x5, x4, #1, #0x1f
    // 0xeb3cdc: stur            x5, [fp, #-0x20]
    // 0xeb3ce0: tbz             x5, #0x3f, #0xeb3cf8
    // 0xeb3ce4: mov             x2, x4
    // 0xeb3ce8: mov             x3, x5
    // 0xeb3cec: r1 = 0
    //     0xeb3cec: movz            x1, #0
    // 0xeb3cf0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeb3cf0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeb3cf4: r0 = checkValidRange()
    //     0xeb3cf4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeb3cf8: ldur            x1, [fp, #-8]
    // 0xeb3cfc: ldur            x4, [fp, #-0x30]
    // 0xeb3d00: r0 = AllocateUint8Array()
    //     0xeb3d00: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xeb3d04: mov             x1, x0
    // 0xeb3d08: ldur            x3, [fp, #-0x20]
    // 0xeb3d0c: ldur            x5, [fp, #-0x38]
    // 0xeb3d10: r2 = 0
    //     0xeb3d10: movz            x2, #0
    // 0xeb3d14: r6 = 0
    //     0xeb3d14: movz            x6, #0
    // 0xeb3d18: stur            x0, [fp, #-0x30]
    // 0xeb3d1c: r0 = _slowSetRange()
    //     0xeb3d1c: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xeb3d20: ldur            x0, [fp, #-8]
    // 0xeb3d24: LoadField: r2 = r0->field_3f
    //     0xeb3d24: ldur            w2, [x0, #0x3f]
    // 0xeb3d28: DecompressPointer r2
    //     0xeb3d28: add             x2, x2, HEAP, lsl #32
    // 0xeb3d2c: r16 = Sentinel
    //     0xeb3d2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb3d30: cmp             w2, w16
    // 0xeb3d34: b.eq            #0xeb4068
    // 0xeb3d38: mov             x1, x0
    // 0xeb3d3c: ldur            x3, [fp, #-0x30]
    // 0xeb3d40: r0 = _gHASHBlock()
    //     0xeb3d40: bl              #0xe8228c  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::_gHASHBlock
    // 0xeb3d44: ldur            x4, [fp, #-8]
    // 0xeb3d48: LoadField: r2 = r4->field_3f
    //     0xeb3d48: ldur            w2, [x4, #0x3f]
    // 0xeb3d4c: DecompressPointer r2
    //     0xeb3d4c: add             x2, x2, HEAP, lsl #32
    // 0xeb3d50: LoadField: r3 = r4->field_3b
    //     0xeb3d50: ldur            w3, [x4, #0x3b]
    // 0xeb3d54: DecompressPointer r3
    //     0xeb3d54: add             x3, x3, HEAP, lsl #32
    // 0xeb3d58: r16 = Sentinel
    //     0xeb3d58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb3d5c: cmp             w3, w16
    // 0xeb3d60: b.eq            #0xeb4074
    // 0xeb3d64: LoadField: r0 = r2->field_13
    //     0xeb3d64: ldur            w0, [x2, #0x13]
    // 0xeb3d68: r5 = LoadInt32Instr(r0)
    //     0xeb3d68: sbfx            x5, x0, #1, #0x1f
    // 0xeb3d6c: LoadField: r0 = r3->field_13
    //     0xeb3d6c: ldur            w0, [x3, #0x13]
    // 0xeb3d70: r6 = LoadInt32Instr(r0)
    //     0xeb3d70: sbfx            x6, x0, #1, #0x1f
    // 0xeb3d74: r7 = 0
    //     0xeb3d74: movz            x7, #0
    // 0xeb3d78: CheckStackOverflow
    //     0xeb3d78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb3d7c: cmp             SP, x16
    //     0xeb3d80: b.ls            #0xeb4080
    // 0xeb3d84: cmp             x7, x5
    // 0xeb3d88: b.ge            #0xeb3dc4
    // 0xeb3d8c: ArrayLoad: r8 = r2[r7]  ; List_1
    //     0xeb3d8c: add             x16, x2, x7
    //     0xeb3d90: ldrb            w8, [x16, #0x17]
    // 0xeb3d94: mov             x0, x6
    // 0xeb3d98: mov             x1, x7
    // 0xeb3d9c: cmp             x1, x0
    // 0xeb3da0: b.hs            #0xeb4088
    // 0xeb3da4: ArrayLoad: r0 = r3[r7]  ; List_1
    //     0xeb3da4: add             x16, x3, x7
    //     0xeb3da8: ldrb            w0, [x16, #0x17]
    // 0xeb3dac: eor             x1, x8, x0
    // 0xeb3db0: ArrayStore: r2[r7] = r1  ; TypeUnknown_1
    //     0xeb3db0: add             x0, x2, x7
    //     0xeb3db4: strb            w1, [x0, #0x17]
    // 0xeb3db8: add             x0, x7, #1
    // 0xeb3dbc: mov             x7, x0
    // 0xeb3dc0: b               #0xeb3d78
    // 0xeb3dc4: LoadField: r0 = r4->field_b
    //     0xeb3dc4: ldur            w0, [x4, #0xb]
    // 0xeb3dc8: DecompressPointer r0
    //     0xeb3dc8: add             x0, x0, HEAP, lsl #32
    // 0xeb3dcc: r16 = Sentinel
    //     0xeb3dcc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb3dd0: cmp             w0, w16
    // 0xeb3dd4: b.eq            #0xeb408c
    // 0xeb3dd8: tbnz            w0, #4, #0xeb401c
    // 0xeb3ddc: ldur            x0, [fp, #-0x18]
    // 0xeb3de0: ldur            x5, [fp, #-0x28]
    // 0xeb3de4: add             x6, x0, x5
    // 0xeb3de8: stur            x6, [fp, #-0x40]
    // 0xeb3dec: LoadField: r7 = r4->field_3f
    //     0xeb3dec: ldur            w7, [x4, #0x3f]
    // 0xeb3df0: DecompressPointer r7
    //     0xeb3df0: add             x7, x7, HEAP, lsl #32
    // 0xeb3df4: r16 = Sentinel
    //     0xeb3df4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb3df8: cmp             w7, w16
    // 0xeb3dfc: b.eq            #0xeb4098
    // 0xeb3e00: stur            x7, [fp, #-0x30]
    // 0xeb3e04: LoadField: r0 = r7->field_13
    //     0xeb3e04: ldur            w0, [x7, #0x13]
    // 0xeb3e08: r8 = LoadInt32Instr(r0)
    //     0xeb3e08: sbfx            x8, x0, #1, #0x1f
    // 0xeb3e0c: stur            x8, [fp, #-0x20]
    // 0xeb3e10: add             x9, x8, x6
    // 0xeb3e14: stur            x9, [fp, #-0x18]
    // 0xeb3e18: tbz             x6, #0x3f, #0xeb3e24
    // 0xeb3e1c: ldur            x10, [fp, #-0x10]
    // 0xeb3e20: b               #0xeb3e48
    // 0xeb3e24: cmp             x6, x9
    // 0xeb3e28: b.le            #0xeb3e34
    // 0xeb3e2c: ldur            x10, [fp, #-0x10]
    // 0xeb3e30: b               #0xeb3e48
    // 0xeb3e34: ldur            x10, [fp, #-0x10]
    // 0xeb3e38: LoadField: r0 = r10->field_13
    //     0xeb3e38: ldur            w0, [x10, #0x13]
    // 0xeb3e3c: r1 = LoadInt32Instr(r0)
    //     0xeb3e3c: sbfx            x1, x0, #1, #0x1f
    // 0xeb3e40: cmp             x9, x1
    // 0xeb3e44: b.le            #0xeb3e74
    // 0xeb3e48: LoadField: r2 = r10->field_13
    //     0xeb3e48: ldur            w2, [x10, #0x13]
    // 0xeb3e4c: r0 = BoxInt64Instr(r9)
    //     0xeb3e4c: sbfiz           x0, x9, #1, #0x1f
    //     0xeb3e50: cmp             x9, x0, asr #1
    //     0xeb3e54: b.eq            #0xeb3e60
    //     0xeb3e58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3e5c: stur            x9, [x0, #7]
    // 0xeb3e60: r3 = LoadInt32Instr(r2)
    //     0xeb3e60: sbfx            x3, x2, #1, #0x1f
    // 0xeb3e64: mov             x1, x6
    // 0xeb3e68: mov             x2, x0
    // 0xeb3e6c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeb3e6c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeb3e70: r0 = checkValidRange()
    //     0xeb3e70: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeb3e74: ldur            x2, [fp, #-0x40]
    // 0xeb3e78: ldur            x1, [fp, #-0x18]
    // 0xeb3e7c: ldur            x0, [fp, #-0x20]
    // 0xeb3e80: sub             x3, x1, x2
    // 0xeb3e84: cmp             x0, x3
    // 0xeb3e88: b.lt            #0xeb403c
    // 0xeb3e8c: cbz             x3, #0xeb3ff8
    // 0xeb3e90: r0 = BoxInt64Instr(r3)
    //     0xeb3e90: sbfiz           x0, x3, #1, #0x1f
    //     0xeb3e94: cmp             x3, x0, asr #1
    //     0xeb3e98: b.eq            #0xeb3ea4
    //     0xeb3e9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3ea0: stur            x3, [x0, #7]
    // 0xeb3ea4: mov             x4, x0
    // 0xeb3ea8: cmp             w4, #0x800
    // 0xeb3eac: b.ge            #0xeb3f98
    // 0xeb3eb0: ldur            x6, [fp, #-0x10]
    // 0xeb3eb4: ldur            x5, [fp, #-0x30]
    // 0xeb3eb8: r0 = BoxInt64Instr(r2)
    //     0xeb3eb8: sbfiz           x0, x2, #1, #0x1f
    //     0xeb3ebc: cmp             x2, x0, asr #1
    //     0xeb3ec0: b.eq            #0xeb3ecc
    //     0xeb3ec4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3ec8: stur            x2, [x0, #7]
    // 0xeb3ecc: add             x2, x5, #0x17
    // 0xeb3ed0: sxtw            x0, w0
    // 0xeb3ed4: add             x1, x6, x0, asr #1
    // 0xeb3ed8: add             x1, x1, #0x17
    // 0xeb3edc: cbz             x4, #0xeb3f94
    // 0xeb3ee0: cmp             x1, x2
    // 0xeb3ee4: b.ls            #0xeb3f4c
    // 0xeb3ee8: sxtw            x4, w4
    // 0xeb3eec: add             x16, x2, x4, asr #1
    // 0xeb3ef0: cmp             x1, x16
    // 0xeb3ef4: b.hs            #0xeb3f4c
    // 0xeb3ef8: mov             x2, x16
    // 0xeb3efc: add             x1, x1, x4, asr #1
    // 0xeb3f00: tbz             w4, #4, #0xeb3f0c
    // 0xeb3f04: ldr             x16, [x2, #-8]!
    // 0xeb3f08: str             x16, [x1, #-8]!
    // 0xeb3f0c: tbz             w4, #3, #0xeb3f18
    // 0xeb3f10: ldr             w16, [x2, #-4]!
    // 0xeb3f14: str             w16, [x1, #-4]!
    // 0xeb3f18: tbz             w4, #2, #0xeb3f24
    // 0xeb3f1c: ldrh            w16, [x2, #-2]!
    // 0xeb3f20: strh            w16, [x1, #-2]!
    // 0xeb3f24: tbz             w4, #1, #0xeb3f30
    // 0xeb3f28: ldrb            w16, [x2, #-1]!
    // 0xeb3f2c: strb            w16, [x1, #-1]!
    // 0xeb3f30: ands            w4, w4, #0xffffffe1
    // 0xeb3f34: b.eq            #0xeb3f94
    // 0xeb3f38: ldp             x16, x17, [x2, #-0x10]!
    // 0xeb3f3c: stp             x16, x17, [x1, #-0x10]!
    // 0xeb3f40: subs            w4, w4, #0x20
    // 0xeb3f44: b.ne            #0xeb3f38
    // 0xeb3f48: b               #0xeb3f94
    // 0xeb3f4c: tbz             w4, #4, #0xeb3f58
    // 0xeb3f50: ldr             x16, [x2], #8
    // 0xeb3f54: str             x16, [x1], #8
    // 0xeb3f58: tbz             w4, #3, #0xeb3f64
    // 0xeb3f5c: ldr             w16, [x2], #4
    // 0xeb3f60: str             w16, [x1], #4
    // 0xeb3f64: tbz             w4, #2, #0xeb3f70
    // 0xeb3f68: ldrh            w16, [x2], #2
    // 0xeb3f6c: strh            w16, [x1], #2
    // 0xeb3f70: tbz             w4, #1, #0xeb3f7c
    // 0xeb3f74: ldrb            w16, [x2], #1
    // 0xeb3f78: strb            w16, [x1], #1
    // 0xeb3f7c: ands            w4, w4, #0xffffffe1
    // 0xeb3f80: b.eq            #0xeb3f94
    // 0xeb3f84: ldp             x16, x17, [x2], #0x10
    // 0xeb3f88: stp             x16, x17, [x1], #0x10
    // 0xeb3f8c: subs            w4, w4, #0x20
    // 0xeb3f90: b.ne            #0xeb3f84
    // 0xeb3f94: b               #0xeb3ff8
    // 0xeb3f98: ldur            x6, [fp, #-0x10]
    // 0xeb3f9c: ldur            x5, [fp, #-0x30]
    // 0xeb3fa0: LoadField: r0 = r6->field_7
    //     0xeb3fa0: ldur            x0, [x6, #7]
    // 0xeb3fa4: add             x1, x0, x2
    // 0xeb3fa8: LoadField: r0 = r5->field_7
    //     0xeb3fa8: ldur            x0, [x5, #7]
    // 0xeb3fac: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xeb3fac: mov             x2, THR
    //     0xeb3fb0: ldr             x9, [x2, #0x658]
    //     0xeb3fb4: mov             x16, x0
    //     0xeb3fb8: mov             x0, x1
    //     0xeb3fbc: mov             x1, x16
    //     0xeb3fc0: mov             x2, x3
    //     0xeb3fc4: mov             x17, fp
    //     0xeb3fc8: str             fp, [SP, #-8]!
    //     0xeb3fcc: mov             fp, SP
    //     0xeb3fd0: and             SP, SP, #0xfffffffffffffff0
    //     0xeb3fd4: mov             x19, sp
    //     0xeb3fd8: mov             sp, SP
    //     0xeb3fdc: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeb3fe0: blr             x9
    //     0xeb3fe4: movz            x16, #0x8
    //     0xeb3fe8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeb3fec: mov             sp, x19
    //     0xeb3ff0: mov             SP, fp
    //     0xeb3ff4: ldr             fp, [SP], #8
    // 0xeb3ff8: ldur            x1, [fp, #-8]
    // 0xeb3ffc: ldur            x0, [fp, #-0x28]
    // 0xeb4000: LoadField: r2 = r1->field_3f
    //     0xeb4000: ldur            w2, [x1, #0x3f]
    // 0xeb4004: DecompressPointer r2
    //     0xeb4004: add             x2, x2, HEAP, lsl #32
    // 0xeb4008: LoadField: r3 = r2->field_13
    //     0xeb4008: ldur            w3, [x2, #0x13]
    // 0xeb400c: r2 = LoadInt32Instr(r3)
    //     0xeb400c: sbfx            x2, x3, #1, #0x1f
    // 0xeb4010: add             x3, x0, x2
    // 0xeb4014: mov             x0, x3
    // 0xeb4018: b               #0xeb4024
    // 0xeb401c: mov             x1, x4
    // 0xeb4020: ldur            x0, [fp, #-0x28]
    // 0xeb4024: stur            x0, [fp, #-0x18]
    // 0xeb4028: r0 = validateMac()
    //     0xeb4028: bl              #0xeb40a4  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::validateMac
    // 0xeb402c: ldur            x0, [fp, #-0x18]
    // 0xeb4030: LeaveFrame
    //     0xeb4030: mov             SP, fp
    //     0xeb4034: ldp             fp, lr, [SP], #0x10
    // 0xeb4038: ret
    //     0xeb4038: ret             
    // 0xeb403c: r0 = tooFew()
    //     0xeb403c: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xeb4040: r0 = Throw()
    //     0xeb4040: bl              #0xec04b8  ; ThrowStub
    // 0xeb4044: brk             #0
    // 0xeb4048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb4048: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb404c: b               #0xeb3bdc
    // 0xeb4050: r9 = _initialAssociatedText
    //     0xeb4050: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a08] Field <BaseAEADBlockCipher._initialAssociatedText@2660101045>: late (offset: 0x1c)
    //     0xeb4054: ldr             x9, [x9, #0xa08]
    // 0xeb4058: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4058: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb405c: r9 = _processedBytes
    //     0xeb405c: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a38] Field <GCMBlockCipher._processedBytes@914399014>: late (offset: 0x44)
    //     0xeb4060: ldr             x9, [x9, #0xa38]
    // 0xeb4064: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4064: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4068: r9 = _x
    //     0xeb4068: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a10] Field <GCMBlockCipher._x@914399014>: late (offset: 0x40)
    //     0xeb406c: ldr             x9, [x9, #0xa10]
    // 0xeb4070: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4070: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4074: r9 = _e0
    //     0xeb4074: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a40] Field <GCMBlockCipher._e0@914399014>: late (offset: 0x3c)
    //     0xeb4078: ldr             x9, [x9, #0xa40]
    // 0xeb407c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb407c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb4080: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb4084: b               #0xeb3d84
    // 0xeb4088: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb4088: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeb408c: r9 = _forEncryption
    //     0xeb408c: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a48] Field <BaseAEADBlockCipher._forEncryption@2660101045>: late (offset: 0xc)
    //     0xeb4090: ldr             x9, [x9, #0xa48]
    // 0xeb4094: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4094: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4098: r9 = _x
    //     0xeb4098: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a10] Field <GCMBlockCipher._x@914399014>: late (offset: 0x40)
    //     0xeb409c: ldr             x9, [x9, #0xa10]
    // 0xeb40a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb40a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
