// lib: impl.block_cipher.modes.gctr, url: package:pointycastle/block/modes/gctr.dart

// class id: 1050936, size: 0x8
class :: {
}

// class id: 705, size: 0x24, field offset: 0x8
class GCTRBlockCipher extends BaseBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xdac
  late Uint8List _iv; // offset: 0xc
  late int _n3; // offset: 0x1c
  late int _n4; // offset: 0x20

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e5c54, size: 0x64
    // 0x8e5c54: EnterFrame
    //     0x8e5c54: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5c58: mov             fp, SP
    // 0x8e5c5c: AllocStack(0x8)
    //     0x8e5c5c: sub             SP, SP, #8
    // 0x8e5c60: CheckStackOverflow
    //     0x8e5c60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5c64: cmp             SP, x16
    //     0x8e5c68: b.ls            #0x8e5cb0
    // 0x8e5c6c: r0 = DynamicFactoryConfig()
    //     0x8e5c6c: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e5c70: r1 = Function '<anonymous closure>': static.
    //     0x8e5c70: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b20] AnonymousClosure: static (0x8e5cb8), in [package:pointycastle/block/modes/gctr.dart] GCTRBlockCipher::factoryConfig (0x8e5c54)
    //     0x8e5c74: ldr             x1, [x1, #0xb20]
    // 0x8e5c78: r2 = Null
    //     0x8e5c78: mov             x2, NULL
    // 0x8e5c7c: stur            x0, [fp, #-8]
    // 0x8e5c80: r0 = AllocateClosure()
    //     0x8e5c80: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e5c84: ldur            x1, [fp, #-8]
    // 0x8e5c88: mov             x5, x0
    // 0x8e5c8c: r2 = BlockCipher
    //     0x8e5c8c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e5c90: ldr             x2, [x2, #0xa80]
    // 0x8e5c94: r3 = "/GCTR"
    //     0x8e5c94: add             x3, PP, #0x19, lsl #12  ; [pp+0x19b28] "/GCTR"
    //     0x8e5c98: ldr             x3, [x3, #0xb28]
    // 0x8e5c9c: r0 = DynamicFactoryConfig.suffix()
    //     0x8e5c9c: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e5ca0: ldur            x0, [fp, #-8]
    // 0x8e5ca4: LeaveFrame
    //     0x8e5ca4: mov             SP, fp
    //     0x8e5ca8: ldp             fp, lr, [SP], #0x10
    // 0x8e5cac: ret
    //     0x8e5cac: ret             
    // 0x8e5cb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e5cb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5cb4: b               #0x8e5c6c
  }
  [closure] static (dynamic) => GCTRBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e5cb8, size: 0x54
    // 0x8e5cb8: EnterFrame
    //     0x8e5cb8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5cbc: mov             fp, SP
    // 0x8e5cc0: AllocStack(0x8)
    //     0x8e5cc0: sub             SP, SP, #8
    // 0x8e5cc4: SetupParameters()
    //     0x8e5cc4: ldr             x0, [fp, #0x20]
    //     0x8e5cc8: ldur            w1, [x0, #0x17]
    //     0x8e5ccc: add             x1, x1, HEAP, lsl #32
    //     0x8e5cd0: stur            x1, [fp, #-8]
    // 0x8e5cd4: r1 = 1
    //     0x8e5cd4: movz            x1, #0x1
    // 0x8e5cd8: r0 = AllocateContext()
    //     0x8e5cd8: bl              #0xec126c  ; AllocateContextStub
    // 0x8e5cdc: mov             x1, x0
    // 0x8e5ce0: ldur            x0, [fp, #-8]
    // 0x8e5ce4: StoreField: r1->field_b = r0
    //     0x8e5ce4: stur            w0, [x1, #0xb]
    // 0x8e5ce8: ldr             x0, [fp, #0x10]
    // 0x8e5cec: StoreField: r1->field_f = r0
    //     0x8e5cec: stur            w0, [x1, #0xf]
    // 0x8e5cf0: mov             x2, x1
    // 0x8e5cf4: r1 = Function '<anonymous closure>': static.
    //     0x8e5cf4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19b30] AnonymousClosure: static (0x8e5d0c), in [package:pointycastle/block/modes/gctr.dart] GCTRBlockCipher::factoryConfig (0x8e5c54)
    //     0x8e5cf8: ldr             x1, [x1, #0xb30]
    // 0x8e5cfc: r0 = AllocateClosure()
    //     0x8e5cfc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e5d00: LeaveFrame
    //     0x8e5d00: mov             SP, fp
    //     0x8e5d04: ldp             fp, lr, [SP], #0x10
    // 0x8e5d08: ret
    //     0x8e5d08: ret             
  }
  [closure] static GCTRBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e5d0c, size: 0xcc
    // 0x8e5d0c: EnterFrame
    //     0x8e5d0c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5d10: mov             fp, SP
    // 0x8e5d14: AllocStack(0x20)
    //     0x8e5d14: sub             SP, SP, #0x20
    // 0x8e5d18: SetupParameters()
    //     0x8e5d18: ldr             x0, [fp, #0x10]
    //     0x8e5d1c: ldur            w1, [x0, #0x17]
    //     0x8e5d20: add             x1, x1, HEAP, lsl #32
    // 0x8e5d24: CheckStackOverflow
    //     0x8e5d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5d28: cmp             SP, x16
    //     0x8e5d2c: b.ls            #0x8e5dcc
    // 0x8e5d30: LoadField: r0 = r1->field_f
    //     0x8e5d30: ldur            w0, [x1, #0xf]
    // 0x8e5d34: DecompressPointer r0
    //     0x8e5d34: add             x0, x0, HEAP, lsl #32
    // 0x8e5d38: r1 = LoadClassIdInstr(r0)
    //     0x8e5d38: ldur            x1, [x0, #-1]
    //     0x8e5d3c: ubfx            x1, x1, #0xc, #0x14
    // 0x8e5d40: mov             x16, x0
    // 0x8e5d44: mov             x0, x1
    // 0x8e5d48: mov             x1, x16
    // 0x8e5d4c: r2 = 1
    //     0x8e5d4c: movz            x2, #0x1
    // 0x8e5d50: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e5d50: sub             lr, x0, #0xfdd
    //     0x8e5d54: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5d58: blr             lr
    // 0x8e5d5c: stur            x0, [fp, #-8]
    // 0x8e5d60: cmp             w0, NULL
    // 0x8e5d64: b.eq            #0x8e5dd4
    // 0x8e5d68: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e5d68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e5d6c: ldr             x0, [x0, #0x2e38]
    //     0x8e5d70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e5d74: cmp             w0, w16
    //     0x8e5d78: b.ne            #0x8e5d88
    //     0x8e5d7c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e5d80: ldr             x2, [x2, #0xf80]
    //     0x8e5d84: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e5d88: r16 = <BlockCipher>
    //     0x8e5d88: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e5d8c: ldr             x16, [x16, #0x88]
    // 0x8e5d90: stp             x0, x16, [SP, #8]
    // 0x8e5d94: ldur            x16, [fp, #-8]
    // 0x8e5d98: str             x16, [SP]
    // 0x8e5d9c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e5d9c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e5da0: r0 = create()
    //     0x8e5da0: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e5da4: stur            x0, [fp, #-8]
    // 0x8e5da8: r0 = GCTRBlockCipher()
    //     0x8e5da8: bl              #0x8e5fa8  ; AllocateGCTRBlockCipherStub -> GCTRBlockCipher (size=0x24)
    // 0x8e5dac: mov             x1, x0
    // 0x8e5db0: ldur            x2, [fp, #-8]
    // 0x8e5db4: stur            x0, [fp, #-8]
    // 0x8e5db8: r0 = GCTRBlockCipher()
    //     0x8e5db8: bl              #0x8e5dd8  ; [package:pointycastle/block/modes/gctr.dart] GCTRBlockCipher::GCTRBlockCipher
    // 0x8e5dbc: ldur            x0, [fp, #-8]
    // 0x8e5dc0: LeaveFrame
    //     0x8e5dc0: mov             SP, fp
    //     0x8e5dc4: ldp             fp, lr, [SP], #0x10
    // 0x8e5dc8: ret
    //     0x8e5dc8: ret             
    // 0x8e5dcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e5dcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5dd0: b               #0x8e5d30
    // 0x8e5dd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e5dd4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ GCTRBlockCipher(/* No info */) {
    // ** addr: 0x8e5dd8, size: 0x1d0
    // 0x8e5dd8: EnterFrame
    //     0x8e5dd8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5ddc: mov             fp, SP
    // 0x8e5de0: AllocStack(0x10)
    //     0x8e5de0: sub             SP, SP, #0x10
    // 0x8e5de4: r3 = Sentinel
    //     0x8e5de4: ldr             x3, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e5de8: r0 = true
    //     0x8e5de8: add             x0, NULL, #0x20  ; true
    // 0x8e5dec: mov             x4, x1
    // 0x8e5df0: stur            x1, [fp, #-8]
    // 0x8e5df4: stur            x2, [fp, #-0x10]
    // 0x8e5df8: CheckStackOverflow
    //     0x8e5df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5dfc: cmp             SP, x16
    //     0x8e5e00: b.ls            #0x8e5fa0
    // 0x8e5e04: StoreField: r4->field_b = r3
    //     0x8e5e04: stur            w3, [x4, #0xb]
    // 0x8e5e08: ArrayStore: r4[0] = r0  ; List_4
    //     0x8e5e08: stur            w0, [x4, #0x17]
    // 0x8e5e0c: StoreField: r4->field_1b = r3
    //     0x8e5e0c: stur            w3, [x4, #0x1b]
    // 0x8e5e10: StoreField: r4->field_1f = r3
    //     0x8e5e10: stur            w3, [x4, #0x1f]
    // 0x8e5e14: mov             x0, x2
    // 0x8e5e18: StoreField: r4->field_7 = r0
    //     0x8e5e18: stur            w0, [x4, #7]
    //     0x8e5e1c: ldurb           w16, [x4, #-1]
    //     0x8e5e20: ldurb           w17, [x0, #-1]
    //     0x8e5e24: and             x16, x17, x16, lsr #2
    //     0x8e5e28: tst             x16, HEAP, lsr #32
    //     0x8e5e2c: b.eq            #0x8e5e34
    //     0x8e5e30: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8e5e34: r0 = LoadClassIdInstr(r2)
    //     0x8e5e34: ldur            x0, [x2, #-1]
    //     0x8e5e38: ubfx            x0, x0, #0xc, #0x14
    // 0x8e5e3c: mov             x1, x2
    // 0x8e5e40: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5e40: sub             lr, x0, #1, lsl #12
    //     0x8e5e44: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5e48: blr             lr
    // 0x8e5e4c: cmp             x0, #8
    // 0x8e5e50: b.ne            #0x8e5f78
    // 0x8e5e54: ldur            x3, [fp, #-8]
    // 0x8e5e58: ldur            x2, [fp, #-0x10]
    // 0x8e5e5c: r0 = LoadClassIdInstr(r2)
    //     0x8e5e5c: ldur            x0, [x2, #-1]
    //     0x8e5e60: ubfx            x0, x0, #0xc, #0x14
    // 0x8e5e64: mov             x1, x2
    // 0x8e5e68: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5e68: sub             lr, x0, #1, lsl #12
    //     0x8e5e6c: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5e70: blr             lr
    // 0x8e5e74: mov             x2, x0
    // 0x8e5e78: r0 = BoxInt64Instr(r2)
    //     0x8e5e78: sbfiz           x0, x2, #1, #0x1f
    //     0x8e5e7c: cmp             x2, x0, asr #1
    //     0x8e5e80: b.eq            #0x8e5e8c
    //     0x8e5e84: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e5e88: stur            x2, [x0, #7]
    // 0x8e5e8c: mov             x4, x0
    // 0x8e5e90: r0 = AllocateUint8Array()
    //     0x8e5e90: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e5e94: ldur            x2, [fp, #-8]
    // 0x8e5e98: StoreField: r2->field_b = r0
    //     0x8e5e98: stur            w0, [x2, #0xb]
    //     0x8e5e9c: ldurb           w16, [x2, #-1]
    //     0x8e5ea0: ldurb           w17, [x0, #-1]
    //     0x8e5ea4: and             x16, x17, x16, lsr #2
    //     0x8e5ea8: tst             x16, HEAP, lsr #32
    //     0x8e5eac: b.eq            #0x8e5eb4
    //     0x8e5eb0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e5eb4: ldur            x3, [fp, #-0x10]
    // 0x8e5eb8: r0 = LoadClassIdInstr(r3)
    //     0x8e5eb8: ldur            x0, [x3, #-1]
    //     0x8e5ebc: ubfx            x0, x0, #0xc, #0x14
    // 0x8e5ec0: mov             x1, x3
    // 0x8e5ec4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5ec4: sub             lr, x0, #1, lsl #12
    //     0x8e5ec8: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5ecc: blr             lr
    // 0x8e5ed0: mov             x2, x0
    // 0x8e5ed4: r0 = BoxInt64Instr(r2)
    //     0x8e5ed4: sbfiz           x0, x2, #1, #0x1f
    //     0x8e5ed8: cmp             x2, x0, asr #1
    //     0x8e5edc: b.eq            #0x8e5ee8
    //     0x8e5ee0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e5ee4: stur            x2, [x0, #7]
    // 0x8e5ee8: mov             x4, x0
    // 0x8e5eec: r0 = AllocateUint8Array()
    //     0x8e5eec: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e5ef0: ldur            x2, [fp, #-8]
    // 0x8e5ef4: StoreField: r2->field_f = r0
    //     0x8e5ef4: stur            w0, [x2, #0xf]
    //     0x8e5ef8: ldurb           w16, [x2, #-1]
    //     0x8e5efc: ldurb           w17, [x0, #-1]
    //     0x8e5f00: and             x16, x17, x16, lsr #2
    //     0x8e5f04: tst             x16, HEAP, lsr #32
    //     0x8e5f08: b.eq            #0x8e5f10
    //     0x8e5f0c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e5f10: ldur            x1, [fp, #-0x10]
    // 0x8e5f14: r0 = LoadClassIdInstr(r1)
    //     0x8e5f14: ldur            x0, [x1, #-1]
    //     0x8e5f18: ubfx            x0, x0, #0xc, #0x14
    // 0x8e5f1c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5f1c: sub             lr, x0, #1, lsl #12
    //     0x8e5f20: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5f24: blr             lr
    // 0x8e5f28: mov             x2, x0
    // 0x8e5f2c: r0 = BoxInt64Instr(r2)
    //     0x8e5f2c: sbfiz           x0, x2, #1, #0x1f
    //     0x8e5f30: cmp             x2, x0, asr #1
    //     0x8e5f34: b.eq            #0x8e5f40
    //     0x8e5f38: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e5f3c: stur            x2, [x0, #7]
    // 0x8e5f40: mov             x4, x0
    // 0x8e5f44: r0 = AllocateUint8Array()
    //     0x8e5f44: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e5f48: ldur            x1, [fp, #-8]
    // 0x8e5f4c: StoreField: r1->field_13 = r0
    //     0x8e5f4c: stur            w0, [x1, #0x13]
    //     0x8e5f50: ldurb           w16, [x1, #-1]
    //     0x8e5f54: ldurb           w17, [x0, #-1]
    //     0x8e5f58: and             x16, x17, x16, lsr #2
    //     0x8e5f5c: tst             x16, HEAP, lsr #32
    //     0x8e5f60: b.eq            #0x8e5f68
    //     0x8e5f64: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e5f68: r0 = Null
    //     0x8e5f68: mov             x0, NULL
    // 0x8e5f6c: LeaveFrame
    //     0x8e5f6c: mov             SP, fp
    //     0x8e5f70: ldp             fp, lr, [SP], #0x10
    // 0x8e5f74: ret
    //     0x8e5f74: ret             
    // 0x8e5f78: r0 = ArgumentError()
    //     0x8e5f78: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8e5f7c: mov             x1, x0
    // 0x8e5f80: r0 = "GCTR can only be used with 64 bit block ciphers"
    //     0x8e5f80: add             x0, PP, #0x19, lsl #12  ; [pp+0x19b38] "GCTR can only be used with 64 bit block ciphers"
    //     0x8e5f84: ldr             x0, [x0, #0xb38]
    // 0x8e5f88: ArrayStore: r1[0] = r0  ; List_4
    //     0x8e5f88: stur            w0, [x1, #0x17]
    // 0x8e5f8c: r0 = false
    //     0x8e5f8c: add             x0, NULL, #0x30  ; false
    // 0x8e5f90: StoreField: r1->field_b = r0
    //     0x8e5f90: stur            w0, [x1, #0xb]
    // 0x8e5f94: mov             x0, x1
    // 0x8e5f98: r0 = Throw()
    //     0x8e5f98: bl              #0xec04b8  ; ThrowStub
    // 0x8e5f9c: brk             #0
    // 0x8e5fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e5fa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5fa4: b               #0x8e5e04
  }
  _ reset(/* No info */) {
    // ** addr: 0xe7e2a0, size: 0x200
    // 0xe7e2a0: EnterFrame
    //     0xe7e2a0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7e2a4: mov             fp, SP
    // 0xe7e2a8: AllocStack(0x28)
    //     0xe7e2a8: sub             SP, SP, #0x28
    // 0xe7e2ac: SetupParameters(GCTRBlockCipher this /* r1 => r0, fp-0x28 */)
    //     0xe7e2ac: mov             x0, x1
    //     0xe7e2b0: stur            x1, [fp, #-0x28]
    // 0xe7e2b4: CheckStackOverflow
    //     0xe7e2b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7e2b8: cmp             SP, x16
    //     0xe7e2bc: b.ls            #0xe7e488
    // 0xe7e2c0: LoadField: r4 = r0->field_f
    //     0xe7e2c0: ldur            w4, [x0, #0xf]
    // 0xe7e2c4: DecompressPointer r4
    //     0xe7e2c4: add             x4, x4, HEAP, lsl #32
    // 0xe7e2c8: stur            x4, [fp, #-0x20]
    // 0xe7e2cc: cmp             w4, NULL
    // 0xe7e2d0: b.eq            #0xe7e490
    // 0xe7e2d4: LoadField: r5 = r0->field_b
    //     0xe7e2d4: ldur            w5, [x0, #0xb]
    // 0xe7e2d8: DecompressPointer r5
    //     0xe7e2d8: add             x5, x5, HEAP, lsl #32
    // 0xe7e2dc: r16 = Sentinel
    //     0xe7e2dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7e2e0: cmp             w5, w16
    // 0xe7e2e4: b.eq            #0xe7e494
    // 0xe7e2e8: stur            x5, [fp, #-0x18]
    // 0xe7e2ec: LoadField: r6 = r5->field_13
    //     0xe7e2ec: ldur            w6, [x5, #0x13]
    // 0xe7e2f0: stur            x6, [fp, #-0x10]
    // 0xe7e2f4: r7 = LoadInt32Instr(r6)
    //     0xe7e2f4: sbfx            x7, x6, #1, #0x1f
    // 0xe7e2f8: stur            x7, [fp, #-8]
    // 0xe7e2fc: tbnz            x7, #0x3f, #0xe7e310
    // 0xe7e300: LoadField: r1 = r4->field_13
    //     0xe7e300: ldur            w1, [x4, #0x13]
    // 0xe7e304: r2 = LoadInt32Instr(r1)
    //     0xe7e304: sbfx            x2, x1, #1, #0x1f
    // 0xe7e308: cmp             x7, x2
    // 0xe7e30c: b.le            #0xe7e328
    // 0xe7e310: LoadField: r1 = r4->field_13
    //     0xe7e310: ldur            w1, [x4, #0x13]
    // 0xe7e314: r3 = LoadInt32Instr(r1)
    //     0xe7e314: sbfx            x3, x1, #1, #0x1f
    // 0xe7e318: mov             x2, x6
    // 0xe7e31c: r1 = 0
    //     0xe7e31c: movz            x1, #0
    // 0xe7e320: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe7e320: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe7e324: r0 = checkValidRange()
    //     0xe7e324: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe7e328: ldur            x2, [fp, #-8]
    // 0xe7e32c: cbz             x2, #0xe7e458
    // 0xe7e330: ldur            x0, [fp, #-0x10]
    // 0xe7e334: cmp             w0, #0x800
    // 0xe7e338: b.ge            #0xe7e40c
    // 0xe7e33c: ldur            x1, [fp, #-0x20]
    // 0xe7e340: ldur            x3, [fp, #-0x18]
    // 0xe7e344: mov             x4, x0
    // 0xe7e348: add             x2, x3, #0x17
    // 0xe7e34c: add             x0, x1, #0x17
    // 0xe7e350: cbz             x4, #0xe7e408
    // 0xe7e354: cmp             x0, x2
    // 0xe7e358: b.ls            #0xe7e3c0
    // 0xe7e35c: sxtw            x4, w4
    // 0xe7e360: add             x16, x2, x4, asr #1
    // 0xe7e364: cmp             x0, x16
    // 0xe7e368: b.hs            #0xe7e3c0
    // 0xe7e36c: mov             x2, x16
    // 0xe7e370: add             x0, x0, x4, asr #1
    // 0xe7e374: tbz             w4, #4, #0xe7e380
    // 0xe7e378: ldr             x16, [x2, #-8]!
    // 0xe7e37c: str             x16, [x0, #-8]!
    // 0xe7e380: tbz             w4, #3, #0xe7e38c
    // 0xe7e384: ldr             w16, [x2, #-4]!
    // 0xe7e388: str             w16, [x0, #-4]!
    // 0xe7e38c: tbz             w4, #2, #0xe7e398
    // 0xe7e390: ldrh            w16, [x2, #-2]!
    // 0xe7e394: strh            w16, [x0, #-2]!
    // 0xe7e398: tbz             w4, #1, #0xe7e3a4
    // 0xe7e39c: ldrb            w16, [x2, #-1]!
    // 0xe7e3a0: strb            w16, [x0, #-1]!
    // 0xe7e3a4: ands            w4, w4, #0xffffffe1
    // 0xe7e3a8: b.eq            #0xe7e408
    // 0xe7e3ac: ldp             x16, x17, [x2, #-0x10]!
    // 0xe7e3b0: stp             x16, x17, [x0, #-0x10]!
    // 0xe7e3b4: subs            w4, w4, #0x20
    // 0xe7e3b8: b.ne            #0xe7e3ac
    // 0xe7e3bc: b               #0xe7e408
    // 0xe7e3c0: tbz             w4, #4, #0xe7e3cc
    // 0xe7e3c4: ldr             x16, [x2], #8
    // 0xe7e3c8: str             x16, [x0], #8
    // 0xe7e3cc: tbz             w4, #3, #0xe7e3d8
    // 0xe7e3d0: ldr             w16, [x2], #4
    // 0xe7e3d4: str             w16, [x0], #4
    // 0xe7e3d8: tbz             w4, #2, #0xe7e3e4
    // 0xe7e3dc: ldrh            w16, [x2], #2
    // 0xe7e3e0: strh            w16, [x0], #2
    // 0xe7e3e4: tbz             w4, #1, #0xe7e3f0
    // 0xe7e3e8: ldrb            w16, [x2], #1
    // 0xe7e3ec: strb            w16, [x0], #1
    // 0xe7e3f0: ands            w4, w4, #0xffffffe1
    // 0xe7e3f4: b.eq            #0xe7e408
    // 0xe7e3f8: ldp             x16, x17, [x2], #0x10
    // 0xe7e3fc: stp             x16, x17, [x0], #0x10
    // 0xe7e400: subs            w4, w4, #0x20
    // 0xe7e404: b.ne            #0xe7e3f8
    // 0xe7e408: b               #0xe7e458
    // 0xe7e40c: ldur            x1, [fp, #-0x20]
    // 0xe7e410: ldur            x3, [fp, #-0x18]
    // 0xe7e414: LoadField: r0 = r1->field_7
    //     0xe7e414: ldur            x0, [x1, #7]
    // 0xe7e418: LoadField: r1 = r3->field_7
    //     0xe7e418: ldur            x1, [x3, #7]
    // 0xe7e41c: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe7e41c: mov             x3, THR
    //     0xe7e420: ldr             x9, [x3, #0x658]
    //     0xe7e424: mov             x17, fp
    //     0xe7e428: str             fp, [SP, #-8]!
    //     0xe7e42c: mov             fp, SP
    //     0xe7e430: and             SP, SP, #0xfffffffffffffff0
    //     0xe7e434: mov             x19, sp
    //     0xe7e438: mov             sp, SP
    //     0xe7e43c: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7e440: blr             x9
    //     0xe7e444: movz            x16, #0x8
    //     0xe7e448: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7e44c: mov             sp, x19
    //     0xe7e450: mov             SP, fp
    //     0xe7e454: ldr             fp, [SP], #8
    // 0xe7e458: ldur            x0, [fp, #-0x28]
    // 0xe7e45c: LoadField: r1 = r0->field_7
    //     0xe7e45c: ldur            w1, [x0, #7]
    // 0xe7e460: DecompressPointer r1
    //     0xe7e460: add             x1, x1, HEAP, lsl #32
    // 0xe7e464: r0 = LoadClassIdInstr(r1)
    //     0xe7e464: ldur            x0, [x1, #-1]
    //     0xe7e468: ubfx            x0, x0, #0xc, #0x14
    // 0xe7e46c: r0 = GDT[cid_x0 + -0xeaf]()
    //     0xe7e46c: sub             lr, x0, #0xeaf
    //     0xe7e470: ldr             lr, [x21, lr, lsl #3]
    //     0xe7e474: blr             lr
    // 0xe7e478: r0 = Null
    //     0xe7e478: mov             x0, NULL
    // 0xe7e47c: LeaveFrame
    //     0xe7e47c: mov             SP, fp
    //     0xe7e480: ldp             fp, lr, [SP], #0x10
    // 0xe7e484: ret
    //     0xe7e484: ret             
    // 0xe7e488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7e488: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7e48c: b               #0xe7e2c0
    // 0xe7e490: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe7e490: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe7e494: r9 = _iv
    //     0xe7e494: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d58] Field <GCTRBlockCipher._iv@912283055>: late (offset: 0xc)
    //     0xe7e498: ldr             x9, [x9, #0xd58]
    // 0xe7e49c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7e49c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0xe85750, size: 0x484
    // 0xe85750: EnterFrame
    //     0xe85750: stp             fp, lr, [SP, #-0x10]!
    //     0xe85754: mov             fp, SP
    // 0xe85758: AllocStack(0x50)
    //     0xe85758: sub             SP, SP, #0x50
    // 0xe8575c: r0 = true
    //     0xe8575c: add             x0, NULL, #0x20  ; true
    // 0xe85760: mov             x6, x1
    // 0xe85764: mov             x4, x3
    // 0xe85768: stur            x1, [fp, #-0x20]
    // 0xe8576c: stur            x3, [fp, #-0x28]
    // 0xe85770: CheckStackOverflow
    //     0xe85770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe85774: cmp             SP, x16
    //     0xe85778: b.ls            #0xe85bc0
    // 0xe8577c: ArrayStore: r6[0] = r0  ; List_4
    //     0xe8577c: stur            w0, [x6, #0x17]
    // 0xe85780: StoreField: r6->field_1b = rZR
    //     0xe85780: stur            wzr, [x6, #0x1b]
    // 0xe85784: StoreField: r6->field_1f = rZR
    //     0xe85784: stur            wzr, [x6, #0x1f]
    // 0xe85788: r1 = LoadClassIdInstr(r4)
    //     0xe85788: ldur            x1, [x4, #-1]
    //     0xe8578c: ubfx            x1, x1, #0xc, #0x14
    // 0xe85790: cmp             x1, #0x2a8
    // 0xe85794: b.ne            #0xe85b60
    // 0xe85798: LoadField: r7 = r4->field_b
    //     0xe85798: ldur            w7, [x4, #0xb]
    // 0xe8579c: DecompressPointer r7
    //     0xe8579c: add             x7, x7, HEAP, lsl #32
    // 0xe857a0: stur            x7, [fp, #-0x18]
    // 0xe857a4: LoadField: r1 = r7->field_13
    //     0xe857a4: ldur            w1, [x7, #0x13]
    // 0xe857a8: LoadField: r5 = r6->field_b
    //     0xe857a8: ldur            w5, [x6, #0xb]
    // 0xe857ac: DecompressPointer r5
    //     0xe857ac: add             x5, x5, HEAP, lsl #32
    // 0xe857b0: r16 = Sentinel
    //     0xe857b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe857b4: cmp             w5, w16
    // 0xe857b8: b.eq            #0xe85bc8
    // 0xe857bc: stur            x5, [fp, #-0x50]
    // 0xe857c0: LoadField: r8 = r5->field_13
    //     0xe857c0: ldur            w8, [x5, #0x13]
    // 0xe857c4: stur            x8, [fp, #-0x48]
    // 0xe857c8: r9 = LoadInt32Instr(r1)
    //     0xe857c8: sbfx            x9, x1, #1, #0x1f
    // 0xe857cc: stur            x9, [fp, #-0x10]
    // 0xe857d0: r10 = LoadInt32Instr(r8)
    //     0xe857d0: sbfx            x10, x8, #1, #0x1f
    // 0xe857d4: stur            x10, [fp, #-0x40]
    // 0xe857d8: cmp             x9, x10
    // 0xe857dc: b.ge            #0xe859b8
    // 0xe857e0: sub             x8, x10, x9
    // 0xe857e4: mov             x1, x5
    // 0xe857e8: mov             x3, x8
    // 0xe857ec: stur            x8, [fp, #-8]
    // 0xe857f0: r2 = 0
    //     0xe857f0: movz            x2, #0
    // 0xe857f4: r5 = 0
    //     0xe857f4: movz            x5, #0
    // 0xe857f8: r0 = fillRange()
    //     0xe857f8: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0xe857fc: ldur            x0, [fp, #-0x20]
    // 0xe85800: LoadField: r4 = r0->field_b
    //     0xe85800: ldur            w4, [x0, #0xb]
    // 0xe85804: DecompressPointer r4
    //     0xe85804: add             x4, x4, HEAP, lsl #32
    // 0xe85808: stur            x4, [fp, #-0x38]
    // 0xe8580c: LoadField: r5 = r4->field_13
    //     0xe8580c: ldur            w5, [x4, #0x13]
    // 0xe85810: ldur            x6, [fp, #-8]
    // 0xe85814: stur            x5, [fp, #-0x30]
    // 0xe85818: tbnz            x6, #0x3f, #0xe85828
    // 0xe8581c: r1 = LoadInt32Instr(r5)
    //     0xe8581c: sbfx            x1, x5, #1, #0x1f
    // 0xe85820: cmp             x6, x1
    // 0xe85824: b.le            #0xe8583c
    // 0xe85828: r3 = LoadInt32Instr(r5)
    //     0xe85828: sbfx            x3, x5, #1, #0x1f
    // 0xe8582c: mov             x1, x6
    // 0xe85830: mov             x2, x5
    // 0xe85834: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe85834: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe85838: r0 = checkValidRange()
    //     0xe85838: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe8583c: ldur            x2, [fp, #-8]
    // 0xe85840: ldur            x0, [fp, #-0x30]
    // 0xe85844: ldur            x4, [fp, #-0x10]
    // 0xe85848: r1 = LoadInt32Instr(r0)
    //     0xe85848: sbfx            x1, x0, #1, #0x1f
    // 0xe8584c: sub             x3, x1, x2
    // 0xe85850: cmp             x4, x3
    // 0xe85854: b.lt            #0xe85ba8
    // 0xe85858: cbz             x3, #0xe85b14
    // 0xe8585c: r0 = BoxInt64Instr(r3)
    //     0xe8585c: sbfiz           x0, x3, #1, #0x1f
    //     0xe85860: cmp             x3, x0, asr #1
    //     0xe85864: b.eq            #0xe85870
    //     0xe85868: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8586c: stur            x3, [x0, #7]
    // 0xe85870: cmp             w0, #0x800
    // 0xe85874: b.ge            #0xe85954
    // 0xe85878: ldur            x6, [fp, #-0x18]
    // 0xe8587c: ldur            x1, [fp, #-0x38]
    // 0xe85880: lsl             x3, x2, #1
    // 0xe85884: mov             x4, x0
    // 0xe85888: add             x2, x6, #0x17
    // 0xe8588c: sxtw            x3, w3
    // 0xe85890: add             x0, x1, x3, asr #1
    // 0xe85894: add             x0, x0, #0x17
    // 0xe85898: cbz             x4, #0xe85950
    // 0xe8589c: cmp             x0, x2
    // 0xe858a0: b.ls            #0xe85908
    // 0xe858a4: sxtw            x4, w4
    // 0xe858a8: add             x16, x2, x4, asr #1
    // 0xe858ac: cmp             x0, x16
    // 0xe858b0: b.hs            #0xe85908
    // 0xe858b4: mov             x2, x16
    // 0xe858b8: add             x0, x0, x4, asr #1
    // 0xe858bc: tbz             w4, #4, #0xe858c8
    // 0xe858c0: ldr             x16, [x2, #-8]!
    // 0xe858c4: str             x16, [x0, #-8]!
    // 0xe858c8: tbz             w4, #3, #0xe858d4
    // 0xe858cc: ldr             w16, [x2, #-4]!
    // 0xe858d0: str             w16, [x0, #-4]!
    // 0xe858d4: tbz             w4, #2, #0xe858e0
    // 0xe858d8: ldrh            w16, [x2, #-2]!
    // 0xe858dc: strh            w16, [x0, #-2]!
    // 0xe858e0: tbz             w4, #1, #0xe858ec
    // 0xe858e4: ldrb            w16, [x2, #-1]!
    // 0xe858e8: strb            w16, [x0, #-1]!
    // 0xe858ec: ands            w4, w4, #0xffffffe1
    // 0xe858f0: b.eq            #0xe85950
    // 0xe858f4: ldp             x16, x17, [x2, #-0x10]!
    // 0xe858f8: stp             x16, x17, [x0, #-0x10]!
    // 0xe858fc: subs            w4, w4, #0x20
    // 0xe85900: b.ne            #0xe858f4
    // 0xe85904: b               #0xe85950
    // 0xe85908: tbz             w4, #4, #0xe85914
    // 0xe8590c: ldr             x16, [x2], #8
    // 0xe85910: str             x16, [x0], #8
    // 0xe85914: tbz             w4, #3, #0xe85920
    // 0xe85918: ldr             w16, [x2], #4
    // 0xe8591c: str             w16, [x0], #4
    // 0xe85920: tbz             w4, #2, #0xe8592c
    // 0xe85924: ldrh            w16, [x2], #2
    // 0xe85928: strh            w16, [x0], #2
    // 0xe8592c: tbz             w4, #1, #0xe85938
    // 0xe85930: ldrb            w16, [x2], #1
    // 0xe85934: strb            w16, [x0], #1
    // 0xe85938: ands            w4, w4, #0xffffffe1
    // 0xe8593c: b.eq            #0xe85950
    // 0xe85940: ldp             x16, x17, [x2], #0x10
    // 0xe85944: stp             x16, x17, [x0], #0x10
    // 0xe85948: subs            w4, w4, #0x20
    // 0xe8594c: b.ne            #0xe85940
    // 0xe85950: b               #0xe85b14
    // 0xe85954: ldur            x6, [fp, #-0x18]
    // 0xe85958: ldur            x1, [fp, #-0x38]
    // 0xe8595c: LoadField: r0 = r1->field_7
    //     0xe8595c: ldur            x0, [x1, #7]
    // 0xe85960: add             x1, x0, x2
    // 0xe85964: LoadField: r0 = r6->field_7
    //     0xe85964: ldur            x0, [x6, #7]
    // 0xe85968: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe85968: mov             x2, THR
    //     0xe8596c: ldr             x9, [x2, #0x658]
    //     0xe85970: mov             x16, x0
    //     0xe85974: mov             x0, x1
    //     0xe85978: mov             x1, x16
    //     0xe8597c: mov             x2, x3
    //     0xe85980: mov             x17, fp
    //     0xe85984: str             fp, [SP, #-8]!
    //     0xe85988: mov             fp, SP
    //     0xe8598c: and             SP, SP, #0xfffffffffffffff0
    //     0xe85990: mov             x19, sp
    //     0xe85994: mov             sp, SP
    //     0xe85998: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe8599c: blr             x9
    //     0xe859a0: movz            x16, #0x8
    //     0xe859a4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe859a8: mov             sp, x19
    //     0xe859ac: mov             SP, fp
    //     0xe859b0: ldr             fp, [SP], #8
    // 0xe859b4: b               #0xe85b14
    // 0xe859b8: mov             x6, x7
    // 0xe859bc: mov             x4, x9
    // 0xe859c0: tbz             x10, #0x3f, #0xe859d8
    // 0xe859c4: mov             x2, x8
    // 0xe859c8: mov             x3, x10
    // 0xe859cc: r1 = 0
    //     0xe859cc: movz            x1, #0
    // 0xe859d0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe859d0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe859d4: r0 = checkValidRange()
    //     0xe859d4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe859d8: ldur            x0, [fp, #-0x10]
    // 0xe859dc: ldur            x2, [fp, #-0x40]
    // 0xe859e0: cmp             x0, x2
    // 0xe859e4: b.lt            #0xe85bb4
    // 0xe859e8: cbz             x2, #0xe85b14
    // 0xe859ec: ldur            x0, [fp, #-0x48]
    // 0xe859f0: cmp             w0, #0x800
    // 0xe859f4: b.ge            #0xe85ac8
    // 0xe859f8: ldur            x3, [fp, #-0x18]
    // 0xe859fc: ldur            x1, [fp, #-0x50]
    // 0xe85a00: mov             x4, x0
    // 0xe85a04: add             x2, x3, #0x17
    // 0xe85a08: add             x0, x1, #0x17
    // 0xe85a0c: cbz             x4, #0xe85ac4
    // 0xe85a10: cmp             x0, x2
    // 0xe85a14: b.ls            #0xe85a7c
    // 0xe85a18: sxtw            x4, w4
    // 0xe85a1c: add             x16, x2, x4, asr #1
    // 0xe85a20: cmp             x0, x16
    // 0xe85a24: b.hs            #0xe85a7c
    // 0xe85a28: mov             x2, x16
    // 0xe85a2c: add             x0, x0, x4, asr #1
    // 0xe85a30: tbz             w4, #4, #0xe85a3c
    // 0xe85a34: ldr             x16, [x2, #-8]!
    // 0xe85a38: str             x16, [x0, #-8]!
    // 0xe85a3c: tbz             w4, #3, #0xe85a48
    // 0xe85a40: ldr             w16, [x2, #-4]!
    // 0xe85a44: str             w16, [x0, #-4]!
    // 0xe85a48: tbz             w4, #2, #0xe85a54
    // 0xe85a4c: ldrh            w16, [x2, #-2]!
    // 0xe85a50: strh            w16, [x0, #-2]!
    // 0xe85a54: tbz             w4, #1, #0xe85a60
    // 0xe85a58: ldrb            w16, [x2, #-1]!
    // 0xe85a5c: strb            w16, [x0, #-1]!
    // 0xe85a60: ands            w4, w4, #0xffffffe1
    // 0xe85a64: b.eq            #0xe85ac4
    // 0xe85a68: ldp             x16, x17, [x2, #-0x10]!
    // 0xe85a6c: stp             x16, x17, [x0, #-0x10]!
    // 0xe85a70: subs            w4, w4, #0x20
    // 0xe85a74: b.ne            #0xe85a68
    // 0xe85a78: b               #0xe85ac4
    // 0xe85a7c: tbz             w4, #4, #0xe85a88
    // 0xe85a80: ldr             x16, [x2], #8
    // 0xe85a84: str             x16, [x0], #8
    // 0xe85a88: tbz             w4, #3, #0xe85a94
    // 0xe85a8c: ldr             w16, [x2], #4
    // 0xe85a90: str             w16, [x0], #4
    // 0xe85a94: tbz             w4, #2, #0xe85aa0
    // 0xe85a98: ldrh            w16, [x2], #2
    // 0xe85a9c: strh            w16, [x0], #2
    // 0xe85aa0: tbz             w4, #1, #0xe85aac
    // 0xe85aa4: ldrb            w16, [x2], #1
    // 0xe85aa8: strb            w16, [x0], #1
    // 0xe85aac: ands            w4, w4, #0xffffffe1
    // 0xe85ab0: b.eq            #0xe85ac4
    // 0xe85ab4: ldp             x16, x17, [x2], #0x10
    // 0xe85ab8: stp             x16, x17, [x0], #0x10
    // 0xe85abc: subs            w4, w4, #0x20
    // 0xe85ac0: b.ne            #0xe85ab4
    // 0xe85ac4: b               #0xe85b14
    // 0xe85ac8: ldur            x3, [fp, #-0x18]
    // 0xe85acc: ldur            x1, [fp, #-0x50]
    // 0xe85ad0: LoadField: r0 = r1->field_7
    //     0xe85ad0: ldur            x0, [x1, #7]
    // 0xe85ad4: LoadField: r1 = r3->field_7
    //     0xe85ad4: ldur            x1, [x3, #7]
    // 0xe85ad8: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe85ad8: mov             x3, THR
    //     0xe85adc: ldr             x9, [x3, #0x658]
    //     0xe85ae0: mov             x17, fp
    //     0xe85ae4: str             fp, [SP, #-8]!
    //     0xe85ae8: mov             fp, SP
    //     0xe85aec: and             SP, SP, #0xfffffffffffffff0
    //     0xe85af0: mov             x19, sp
    //     0xe85af4: mov             sp, SP
    //     0xe85af8: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe85afc: blr             x9
    //     0xe85b00: movz            x16, #0x8
    //     0xe85b04: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe85b08: mov             sp, x19
    //     0xe85b0c: mov             SP, fp
    //     0xe85b10: ldr             fp, [SP], #8
    // 0xe85b14: ldur            x0, [fp, #-0x20]
    // 0xe85b18: ldur            x3, [fp, #-0x28]
    // 0xe85b1c: mov             x1, x0
    // 0xe85b20: r0 = reset()
    //     0xe85b20: bl              #0xe7e2a0  ; [package:pointycastle/block/modes/gctr.dart] GCTRBlockCipher::reset
    // 0xe85b24: ldur            x0, [fp, #-0x20]
    // 0xe85b28: LoadField: r1 = r0->field_7
    //     0xe85b28: ldur            w1, [x0, #7]
    // 0xe85b2c: DecompressPointer r1
    //     0xe85b2c: add             x1, x1, HEAP, lsl #32
    // 0xe85b30: ldur            x3, [fp, #-0x28]
    // 0xe85b34: LoadField: r0 = r3->field_f
    //     0xe85b34: ldur            w0, [x3, #0xf]
    // 0xe85b38: DecompressPointer r0
    //     0xe85b38: add             x0, x0, HEAP, lsl #32
    // 0xe85b3c: r2 = LoadClassIdInstr(r1)
    //     0xe85b3c: ldur            x2, [x1, #-1]
    //     0xe85b40: ubfx            x2, x2, #0xc, #0x14
    // 0xe85b44: mov             x3, x0
    // 0xe85b48: mov             x0, x2
    // 0xe85b4c: r2 = true
    //     0xe85b4c: add             x2, NULL, #0x20  ; true
    // 0xe85b50: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe85b50: sub             lr, x0, #0xeda
    //     0xe85b54: ldr             lr, [x21, lr, lsl #3]
    //     0xe85b58: blr             lr
    // 0xe85b5c: b               #0xe85b98
    // 0xe85b60: mov             x0, x6
    // 0xe85b64: mov             x3, x4
    // 0xe85b68: mov             x1, x0
    // 0xe85b6c: r0 = reset()
    //     0xe85b6c: bl              #0xe7e2a0  ; [package:pointycastle/block/modes/gctr.dart] GCTRBlockCipher::reset
    // 0xe85b70: ldur            x0, [fp, #-0x20]
    // 0xe85b74: LoadField: r1 = r0->field_7
    //     0xe85b74: ldur            w1, [x0, #7]
    // 0xe85b78: DecompressPointer r1
    //     0xe85b78: add             x1, x1, HEAP, lsl #32
    // 0xe85b7c: r0 = LoadClassIdInstr(r1)
    //     0xe85b7c: ldur            x0, [x1, #-1]
    //     0xe85b80: ubfx            x0, x0, #0xc, #0x14
    // 0xe85b84: ldur            x3, [fp, #-0x28]
    // 0xe85b88: r2 = true
    //     0xe85b88: add             x2, NULL, #0x20  ; true
    // 0xe85b8c: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe85b8c: sub             lr, x0, #0xeda
    //     0xe85b90: ldr             lr, [x21, lr, lsl #3]
    //     0xe85b94: blr             lr
    // 0xe85b98: r0 = Null
    //     0xe85b98: mov             x0, NULL
    // 0xe85b9c: LeaveFrame
    //     0xe85b9c: mov             SP, fp
    //     0xe85ba0: ldp             fp, lr, [SP], #0x10
    // 0xe85ba4: ret
    //     0xe85ba4: ret             
    // 0xe85ba8: r0 = tooFew()
    //     0xe85ba8: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xe85bac: r0 = Throw()
    //     0xe85bac: bl              #0xec04b8  ; ThrowStub
    // 0xe85bb0: brk             #0
    // 0xe85bb4: r0 = tooFew()
    //     0xe85bb4: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xe85bb8: r0 = Throw()
    //     0xe85bb8: bl              #0xec04b8  ; ThrowStub
    // 0xe85bbc: brk             #0
    // 0xe85bc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe85bc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe85bc4: b               #0xe8577c
    // 0xe85bc8: r9 = _iv
    //     0xe85bc8: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d58] Field <GCTRBlockCipher._iv@912283055>: late (offset: 0xc)
    //     0xe85bcc: ldr             x9, [x9, #0xd58]
    // 0xe85bd0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe85bd0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xea71a4, size: 0x908
    // 0xea71a4: EnterFrame
    //     0xea71a4: stp             fp, lr, [SP, #-0x10]!
    //     0xea71a8: mov             fp, SP
    // 0xea71ac: AllocStack(0x50)
    //     0xea71ac: sub             SP, SP, #0x50
    // 0xea71b0: SetupParameters(GCTRBlockCipher this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* r6 => r6, fp-0x30 */)
    //     0xea71b0: mov             x4, x1
    //     0xea71b4: stur            x1, [fp, #-0x10]
    //     0xea71b8: stur            x2, [fp, #-0x18]
    //     0xea71bc: stur            x3, [fp, #-0x20]
    //     0xea71c0: stur            x5, [fp, #-0x28]
    //     0xea71c4: stur            x6, [fp, #-0x30]
    // 0xea71c8: CheckStackOverflow
    //     0xea71c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea71cc: cmp             SP, x16
    //     0xea71d0: b.ls            #0xea7a54
    // 0xea71d4: LoadField: r7 = r4->field_7
    //     0xea71d4: ldur            w7, [x4, #7]
    // 0xea71d8: DecompressPointer r7
    //     0xea71d8: add             x7, x7, HEAP, lsl #32
    // 0xea71dc: stur            x7, [fp, #-8]
    // 0xea71e0: r0 = LoadClassIdInstr(r7)
    //     0xea71e0: ldur            x0, [x7, #-1]
    //     0xea71e4: ubfx            x0, x0, #0xc, #0x14
    // 0xea71e8: mov             x1, x7
    // 0xea71ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea71ec: sub             lr, x0, #1, lsl #12
    //     0xea71f0: ldr             lr, [x21, lr, lsl #3]
    //     0xea71f4: blr             lr
    // 0xea71f8: ldur            x2, [fp, #-0x20]
    // 0xea71fc: add             x1, x2, x0
    // 0xea7200: ldur            x3, [fp, #-0x18]
    // 0xea7204: LoadField: r0 = r3->field_13
    //     0xea7204: ldur            w0, [x3, #0x13]
    // 0xea7208: r4 = LoadInt32Instr(r0)
    //     0xea7208: sbfx            x4, x0, #1, #0x1f
    // 0xea720c: stur            x4, [fp, #-0x38]
    // 0xea7210: cmp             x1, x4
    // 0xea7214: b.gt            #0xea79e4
    // 0xea7218: ldur            x6, [fp, #-0x28]
    // 0xea721c: ldur            x5, [fp, #-0x30]
    // 0xea7220: ldur            x7, [fp, #-8]
    // 0xea7224: r0 = LoadClassIdInstr(r7)
    //     0xea7224: ldur            x0, [x7, #-1]
    //     0xea7228: ubfx            x0, x0, #0xc, #0x14
    // 0xea722c: mov             x1, x7
    // 0xea7230: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea7230: sub             lr, x0, #1, lsl #12
    //     0xea7234: ldr             lr, [x21, lr, lsl #3]
    //     0xea7238: blr             lr
    // 0xea723c: ldur            x4, [fp, #-0x30]
    // 0xea7240: add             x1, x4, x0
    // 0xea7244: ldur            x7, [fp, #-0x28]
    // 0xea7248: LoadField: r0 = r7->field_13
    //     0xea7248: ldur            w0, [x7, #0x13]
    // 0xea724c: r8 = LoadInt32Instr(r0)
    //     0xea724c: sbfx            x8, x0, #1, #0x1f
    // 0xea7250: stur            x8, [fp, #-0x40]
    // 0xea7254: cmp             x1, x8
    // 0xea7258: b.gt            #0xea7a10
    // 0xea725c: ldur            x9, [fp, #-0x10]
    // 0xea7260: ArrayLoad: r0 = r9[0]  ; List_4
    //     0xea7260: ldur            w0, [x9, #0x17]
    // 0xea7264: DecompressPointer r0
    //     0xea7264: add             x0, x0, HEAP, lsl #32
    // 0xea7268: tbnz            w0, #4, #0xea7368
    // 0xea726c: ldur            x10, [fp, #-8]
    // 0xea7270: r0 = false
    //     0xea7270: add             x0, NULL, #0x30  ; false
    // 0xea7274: ArrayStore: r9[0] = r0  ; List_4
    //     0xea7274: stur            w0, [x9, #0x17]
    // 0xea7278: LoadField: r2 = r9->field_f
    //     0xea7278: ldur            w2, [x9, #0xf]
    // 0xea727c: DecompressPointer r2
    //     0xea727c: add             x2, x2, HEAP, lsl #32
    // 0xea7280: cmp             w2, NULL
    // 0xea7284: b.eq            #0xea7a5c
    // 0xea7288: LoadField: r5 = r9->field_13
    //     0xea7288: ldur            w5, [x9, #0x13]
    // 0xea728c: DecompressPointer r5
    //     0xea728c: add             x5, x5, HEAP, lsl #32
    // 0xea7290: cmp             w5, NULL
    // 0xea7294: b.eq            #0xea7a60
    // 0xea7298: r0 = LoadClassIdInstr(r10)
    //     0xea7298: ldur            x0, [x10, #-1]
    //     0xea729c: ubfx            x0, x0, #0xc, #0x14
    // 0xea72a0: mov             x1, x10
    // 0xea72a4: r3 = 0
    //     0xea72a4: movz            x3, #0
    // 0xea72a8: r6 = 0
    //     0xea72a8: movz            x6, #0
    // 0xea72ac: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea72ac: sub             lr, x0, #0xf69
    //     0xea72b0: ldr             lr, [x21, lr, lsl #3]
    //     0xea72b4: blr             lr
    // 0xea72b8: ldur            x0, [fp, #-0x10]
    // 0xea72bc: LoadField: r1 = r0->field_13
    //     0xea72bc: ldur            w1, [x0, #0x13]
    // 0xea72c0: DecompressPointer r1
    //     0xea72c0: add             x1, x1, HEAP, lsl #32
    // 0xea72c4: r2 = 0
    //     0xea72c4: movz            x2, #0
    // 0xea72c8: r3 = Instance_Endian
    //     0xea72c8: add             x3, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xea72cc: ldr             x3, [x3, #0x8b8]
    // 0xea72d0: r0 = unpack32()
    //     0xea72d0: bl              #0x8e258c  ; [package:pointycastle/src/ufixnum.dart] ::unpack32
    // 0xea72d4: mov             x2, x0
    // 0xea72d8: r0 = BoxInt64Instr(r2)
    //     0xea72d8: sbfiz           x0, x2, #1, #0x1f
    //     0xea72dc: cmp             x2, x0, asr #1
    //     0xea72e0: b.eq            #0xea72ec
    //     0xea72e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea72e8: stur            x2, [x0, #7]
    // 0xea72ec: ldur            x4, [fp, #-0x10]
    // 0xea72f0: StoreField: r4->field_1b = r0
    //     0xea72f0: stur            w0, [x4, #0x1b]
    //     0xea72f4: tbz             w0, #0, #0xea7310
    //     0xea72f8: ldurb           w16, [x4, #-1]
    //     0xea72fc: ldurb           w17, [x0, #-1]
    //     0xea7300: and             x16, x17, x16, lsr #2
    //     0xea7304: tst             x16, HEAP, lsr #32
    //     0xea7308: b.eq            #0xea7310
    //     0xea730c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xea7310: LoadField: r1 = r4->field_13
    //     0xea7310: ldur            w1, [x4, #0x13]
    // 0xea7314: DecompressPointer r1
    //     0xea7314: add             x1, x1, HEAP, lsl #32
    // 0xea7318: r2 = 4
    //     0xea7318: movz            x2, #0x4
    // 0xea731c: r3 = Instance_Endian
    //     0xea731c: add             x3, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xea7320: ldr             x3, [x3, #0x8b8]
    // 0xea7324: r0 = unpack32()
    //     0xea7324: bl              #0x8e258c  ; [package:pointycastle/src/ufixnum.dart] ::unpack32
    // 0xea7328: mov             x2, x0
    // 0xea732c: r0 = BoxInt64Instr(r2)
    //     0xea732c: sbfiz           x0, x2, #1, #0x1f
    //     0xea7330: cmp             x2, x0, asr #1
    //     0xea7334: b.eq            #0xea7340
    //     0xea7338: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea733c: stur            x2, [x0, #7]
    // 0xea7340: ldur            x4, [fp, #-0x10]
    // 0xea7344: StoreField: r4->field_1f = r0
    //     0xea7344: stur            w0, [x4, #0x1f]
    //     0xea7348: tbz             w0, #0, #0xea7364
    //     0xea734c: ldurb           w16, [x4, #-1]
    //     0xea7350: ldurb           w17, [x0, #-1]
    //     0xea7354: and             x16, x17, x16, lsr #2
    //     0xea7358: tst             x16, HEAP, lsr #32
    //     0xea735c: b.eq            #0xea7364
    //     0xea7360: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xea7364: b               #0xea736c
    // 0xea7368: mov             x4, x9
    // 0xea736c: ldur            x6, [fp, #-8]
    // 0xea7370: LoadField: r0 = r4->field_1b
    //     0xea7370: ldur            w0, [x4, #0x1b]
    // 0xea7374: DecompressPointer r0
    //     0xea7374: add             x0, x0, HEAP, lsl #32
    // 0xea7378: r16 = Sentinel
    //     0xea7378: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea737c: cmp             w0, w16
    // 0xea7380: b.eq            #0xea7a64
    // 0xea7384: r1 = LoadInt32Instr(r0)
    //     0xea7384: sbfx            x1, x0, #1, #0x1f
    //     0xea7388: tbz             w0, #0, #0xea7390
    //     0xea738c: ldur            x1, [x0, #7]
    // 0xea7390: r17 = 16843009
    //     0xea7390: movz            x17, #0x101
    //     0xea7394: movk            x17, #0x101, lsl #16
    // 0xea7398: add             x2, x1, x17
    // 0xea739c: r0 = BoxInt64Instr(r2)
    //     0xea739c: sbfiz           x0, x2, #1, #0x1f
    //     0xea73a0: cmp             x2, x0, asr #1
    //     0xea73a4: b.eq            #0xea73b0
    //     0xea73a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea73ac: stur            x2, [x0, #7]
    // 0xea73b0: StoreField: r4->field_1b = r0
    //     0xea73b0: stur            w0, [x4, #0x1b]
    //     0xea73b4: tbz             w0, #0, #0xea73d0
    //     0xea73b8: ldurb           w16, [x4, #-1]
    //     0xea73bc: ldurb           w17, [x0, #-1]
    //     0xea73c0: and             x16, x17, x16, lsr #2
    //     0xea73c4: tst             x16, HEAP, lsr #32
    //     0xea73c8: b.eq            #0xea73d0
    //     0xea73cc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xea73d0: LoadField: r0 = r4->field_1f
    //     0xea73d0: ldur            w0, [x4, #0x1f]
    // 0xea73d4: DecompressPointer r0
    //     0xea73d4: add             x0, x0, HEAP, lsl #32
    // 0xea73d8: r16 = Sentinel
    //     0xea73d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea73dc: cmp             w0, w16
    // 0xea73e0: b.eq            #0xea7a70
    // 0xea73e4: r1 = LoadInt32Instr(r0)
    //     0xea73e4: sbfx            x1, x0, #1, #0x1f
    //     0xea73e8: tbz             w0, #0, #0xea73f0
    //     0xea73ec: ldur            x1, [x0, #7]
    // 0xea73f0: r17 = 16843012
    //     0xea73f0: movz            x17, #0x104
    //     0xea73f4: movk            x17, #0x101, lsl #16
    // 0xea73f8: add             x3, x1, x17
    // 0xea73fc: r0 = BoxInt64Instr(r3)
    //     0xea73fc: sbfiz           x0, x3, #1, #0x1f
    //     0xea7400: cmp             x3, x0, asr #1
    //     0xea7404: b.eq            #0xea7410
    //     0xea7408: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea740c: stur            x3, [x0, #7]
    // 0xea7410: StoreField: r4->field_1f = r0
    //     0xea7410: stur            w0, [x4, #0x1f]
    //     0xea7414: tbz             w0, #0, #0xea7430
    //     0xea7418: ldurb           w16, [x4, #-1]
    //     0xea741c: ldurb           w17, [x0, #-1]
    //     0xea7420: and             x16, x17, x16, lsr #2
    //     0xea7424: tst             x16, HEAP, lsr #32
    //     0xea7428: b.eq            #0xea7430
    //     0xea742c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xea7430: LoadField: r0 = r4->field_f
    //     0xea7430: ldur            w0, [x4, #0xf]
    // 0xea7434: DecompressPointer r0
    //     0xea7434: add             x0, x0, HEAP, lsl #32
    // 0xea7438: mov             x1, x2
    // 0xea743c: mov             x2, x0
    // 0xea7440: r3 = 0
    //     0xea7440: movz            x3, #0
    // 0xea7444: r5 = Instance_Endian
    //     0xea7444: add             x5, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xea7448: ldr             x5, [x5, #0x8b8]
    // 0xea744c: r0 = pack32()
    //     0xea744c: bl              #0x8e7264  ; [package:pointycastle/src/ufixnum.dart] ::pack32
    // 0xea7450: ldur            x0, [fp, #-0x10]
    // 0xea7454: LoadField: r1 = r0->field_1f
    //     0xea7454: ldur            w1, [x0, #0x1f]
    // 0xea7458: DecompressPointer r1
    //     0xea7458: add             x1, x1, HEAP, lsl #32
    // 0xea745c: LoadField: r2 = r0->field_f
    //     0xea745c: ldur            w2, [x0, #0xf]
    // 0xea7460: DecompressPointer r2
    //     0xea7460: add             x2, x2, HEAP, lsl #32
    // 0xea7464: r3 = LoadInt32Instr(r1)
    //     0xea7464: sbfx            x3, x1, #1, #0x1f
    //     0xea7468: tbz             w1, #0, #0xea7470
    //     0xea746c: ldur            x3, [x1, #7]
    // 0xea7470: mov             x1, x3
    // 0xea7474: r3 = 4
    //     0xea7474: movz            x3, #0x4
    // 0xea7478: r5 = Instance_Endian
    //     0xea7478: add             x5, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0xea747c: ldr             x5, [x5, #0x8b8]
    // 0xea7480: r0 = pack32()
    //     0xea7480: bl              #0x8e7264  ; [package:pointycastle/src/ufixnum.dart] ::pack32
    // 0xea7484: ldur            x4, [fp, #-0x10]
    // 0xea7488: LoadField: r2 = r4->field_f
    //     0xea7488: ldur            w2, [x4, #0xf]
    // 0xea748c: DecompressPointer r2
    //     0xea748c: add             x2, x2, HEAP, lsl #32
    // 0xea7490: cmp             w2, NULL
    // 0xea7494: b.eq            #0xea7a7c
    // 0xea7498: LoadField: r5 = r4->field_13
    //     0xea7498: ldur            w5, [x4, #0x13]
    // 0xea749c: DecompressPointer r5
    //     0xea749c: add             x5, x5, HEAP, lsl #32
    // 0xea74a0: cmp             w5, NULL
    // 0xea74a4: b.eq            #0xea7a80
    // 0xea74a8: ldur            x7, [fp, #-8]
    // 0xea74ac: r0 = LoadClassIdInstr(r7)
    //     0xea74ac: ldur            x0, [x7, #-1]
    //     0xea74b0: ubfx            x0, x0, #0xc, #0x14
    // 0xea74b4: mov             x1, x7
    // 0xea74b8: r3 = 0
    //     0xea74b8: movz            x3, #0
    // 0xea74bc: r6 = 0
    //     0xea74bc: movz            x6, #0
    // 0xea74c0: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea74c0: sub             lr, x0, #0xf69
    //     0xea74c4: ldr             lr, [x21, lr, lsl #3]
    //     0xea74c8: blr             lr
    // 0xea74cc: ldur            x2, [fp, #-0x10]
    // 0xea74d0: ldur            x7, [fp, #-0x18]
    // 0xea74d4: ldur            x6, [fp, #-0x20]
    // 0xea74d8: ldur            x5, [fp, #-0x28]
    // 0xea74dc: ldur            x4, [fp, #-0x30]
    // 0xea74e0: r8 = 0
    //     0xea74e0: movz            x8, #0
    // 0xea74e4: ldur            x3, [fp, #-8]
    // 0xea74e8: stur            x8, [fp, #-0x48]
    // 0xea74ec: CheckStackOverflow
    //     0xea74ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea74f0: cmp             SP, x16
    //     0xea74f4: b.ls            #0xea7a84
    // 0xea74f8: r0 = LoadClassIdInstr(r3)
    //     0xea74f8: ldur            x0, [x3, #-1]
    //     0xea74fc: ubfx            x0, x0, #0xc, #0x14
    // 0xea7500: mov             x1, x3
    // 0xea7504: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea7504: sub             lr, x0, #1, lsl #12
    //     0xea7508: ldr             lr, [x21, lr, lsl #3]
    //     0xea750c: blr             lr
    // 0xea7510: ldur            x2, [fp, #-0x48]
    // 0xea7514: cmp             x2, x0
    // 0xea7518: b.ge            #0xea75a8
    // 0xea751c: ldur            x3, [fp, #-0x10]
    // 0xea7520: ldur            x7, [fp, #-0x18]
    // 0xea7524: ldur            x6, [fp, #-0x20]
    // 0xea7528: ldur            x5, [fp, #-0x28]
    // 0xea752c: ldur            x4, [fp, #-0x30]
    // 0xea7530: add             x8, x4, x2
    // 0xea7534: LoadField: r9 = r3->field_13
    //     0xea7534: ldur            w9, [x3, #0x13]
    // 0xea7538: DecompressPointer r9
    //     0xea7538: add             x9, x9, HEAP, lsl #32
    // 0xea753c: cmp             w9, NULL
    // 0xea7540: b.eq            #0xea7a8c
    // 0xea7544: LoadField: r0 = r9->field_13
    //     0xea7544: ldur            w0, [x9, #0x13]
    // 0xea7548: r1 = LoadInt32Instr(r0)
    //     0xea7548: sbfx            x1, x0, #1, #0x1f
    // 0xea754c: mov             x0, x1
    // 0xea7550: mov             x1, x2
    // 0xea7554: cmp             x1, x0
    // 0xea7558: b.hs            #0xea7a90
    // 0xea755c: ArrayLoad: r10 = r9[r2]  ; List_1
    //     0xea755c: add             x16, x9, x2
    //     0xea7560: ldrb            w10, [x16, #0x17]
    // 0xea7564: add             x9, x6, x2
    // 0xea7568: ldur            x0, [fp, #-0x38]
    // 0xea756c: mov             x1, x9
    // 0xea7570: cmp             x1, x0
    // 0xea7574: b.hs            #0xea7a94
    // 0xea7578: ArrayLoad: r0 = r7[r9]  ; List_1
    //     0xea7578: add             x16, x7, x9
    //     0xea757c: ldrb            w0, [x16, #0x17]
    // 0xea7580: eor             x9, x10, x0
    // 0xea7584: ldur            x0, [fp, #-0x40]
    // 0xea7588: mov             x1, x8
    // 0xea758c: cmp             x1, x0
    // 0xea7590: b.hs            #0xea7a98
    // 0xea7594: ArrayStore: r5[r8] = r9  ; TypeUnknown_1
    //     0xea7594: add             x0, x5, x8
    //     0xea7598: strb            w9, [x0, #0x17]
    // 0xea759c: add             x8, x2, #1
    // 0xea75a0: mov             x2, x3
    // 0xea75a4: b               #0xea74e4
    // 0xea75a8: ldur            x3, [fp, #-0x10]
    // 0xea75ac: ldur            x2, [fp, #-8]
    // 0xea75b0: LoadField: r0 = r3->field_f
    //     0xea75b0: ldur            w0, [x3, #0xf]
    // 0xea75b4: DecompressPointer r0
    //     0xea75b4: add             x0, x0, HEAP, lsl #32
    // 0xea75b8: cmp             w0, NULL
    // 0xea75bc: b.eq            #0xea7a9c
    // 0xea75c0: LoadField: r4 = r0->field_13
    //     0xea75c0: ldur            w4, [x0, #0x13]
    // 0xea75c4: stur            x4, [fp, #-0x18]
    // 0xea75c8: r0 = LoadClassIdInstr(r2)
    //     0xea75c8: ldur            x0, [x2, #-1]
    //     0xea75cc: ubfx            x0, x0, #0xc, #0x14
    // 0xea75d0: mov             x1, x2
    // 0xea75d4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea75d4: sub             lr, x0, #1, lsl #12
    //     0xea75d8: ldr             lr, [x21, lr, lsl #3]
    //     0xea75dc: blr             lr
    // 0xea75e0: mov             x1, x0
    // 0xea75e4: ldur            x0, [fp, #-0x18]
    // 0xea75e8: r2 = LoadInt32Instr(r0)
    //     0xea75e8: sbfx            x2, x0, #1, #0x1f
    // 0xea75ec: sub             x3, x2, x1
    // 0xea75f0: ldur            x2, [fp, #-0x10]
    // 0xea75f4: stur            x3, [fp, #-0x20]
    // 0xea75f8: LoadField: r4 = r2->field_f
    //     0xea75f8: ldur            w4, [x2, #0xf]
    // 0xea75fc: DecompressPointer r4
    //     0xea75fc: add             x4, x4, HEAP, lsl #32
    // 0xea7600: stur            x4, [fp, #-0x18]
    // 0xea7604: cmp             w4, NULL
    // 0xea7608: b.eq            #0xea7aa0
    // 0xea760c: ldur            x5, [fp, #-8]
    // 0xea7610: r0 = LoadClassIdInstr(r5)
    //     0xea7610: ldur            x0, [x5, #-1]
    //     0xea7614: ubfx            x0, x0, #0xc, #0x14
    // 0xea7618: mov             x1, x5
    // 0xea761c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea761c: sub             lr, x0, #1, lsl #12
    //     0xea7620: ldr             lr, [x21, lr, lsl #3]
    //     0xea7624: blr             lr
    // 0xea7628: ldur            x1, [fp, #-0x18]
    // 0xea762c: mov             x2, x0
    // 0xea7630: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xea7630: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xea7634: r0 = sublist()
    //     0xea7634: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xea7638: mov             x5, x0
    // 0xea763c: ldur            x4, [fp, #-0x20]
    // 0xea7640: stur            x5, [fp, #-0x28]
    // 0xea7644: tbz             x4, #0x3f, #0xea7650
    // 0xea7648: ldur            x6, [fp, #-0x18]
    // 0xea764c: b               #0xea7664
    // 0xea7650: ldur            x6, [fp, #-0x18]
    // 0xea7654: LoadField: r0 = r6->field_13
    //     0xea7654: ldur            w0, [x6, #0x13]
    // 0xea7658: r1 = LoadInt32Instr(r0)
    //     0xea7658: sbfx            x1, x0, #1, #0x1f
    // 0xea765c: cmp             x4, x1
    // 0xea7660: b.le            #0xea7690
    // 0xea7664: LoadField: r2 = r6->field_13
    //     0xea7664: ldur            w2, [x6, #0x13]
    // 0xea7668: r0 = BoxInt64Instr(r4)
    //     0xea7668: sbfiz           x0, x4, #1, #0x1f
    //     0xea766c: cmp             x4, x0, asr #1
    //     0xea7670: b.eq            #0xea767c
    //     0xea7674: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea7678: stur            x4, [x0, #7]
    // 0xea767c: r3 = LoadInt32Instr(r2)
    //     0xea767c: sbfx            x3, x2, #1, #0x1f
    // 0xea7680: mov             x2, x0
    // 0xea7684: r1 = 0
    //     0xea7684: movz            x1, #0
    // 0xea7688: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea7688: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea768c: r0 = checkValidRange()
    //     0xea768c: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea7690: ldur            x20, [fp, #-0x20]
    // 0xea7694: ldur            x2, [fp, #-0x28]
    // 0xea7698: LoadField: r0 = r2->field_13
    //     0xea7698: ldur            w0, [x2, #0x13]
    // 0xea769c: r1 = LoadInt32Instr(r0)
    //     0xea769c: sbfx            x1, x0, #1, #0x1f
    // 0xea76a0: cmp             x1, x20
    // 0xea76a4: b.lt            #0xea7a3c
    // 0xea76a8: cbz             x20, #0xea77e0
    // 0xea76ac: r0 = BoxInt64Instr(r20)
    //     0xea76ac: sbfiz           x0, x20, #1, #0x1f
    //     0xea76b0: cmp             x20, x0, asr #1
    //     0xea76b4: b.eq            #0xea76c0
    //     0xea76b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea76bc: stur            x20, [x0, #7]
    // 0xea76c0: cmp             w0, #0x800
    // 0xea76c4: b.ge            #0xea7794
    // 0xea76c8: ldur            x1, [fp, #-0x18]
    // 0xea76cc: mov             x4, x0
    // 0xea76d0: add             x3, x2, #0x17
    // 0xea76d4: add             x0, x1, #0x17
    // 0xea76d8: cbz             x4, #0xea7790
    // 0xea76dc: cmp             x0, x3
    // 0xea76e0: b.ls            #0xea7748
    // 0xea76e4: sxtw            x4, w4
    // 0xea76e8: add             x16, x3, x4, asr #1
    // 0xea76ec: cmp             x0, x16
    // 0xea76f0: b.hs            #0xea7748
    // 0xea76f4: mov             x3, x16
    // 0xea76f8: add             x0, x0, x4, asr #1
    // 0xea76fc: tbz             w4, #4, #0xea7708
    // 0xea7700: ldr             x16, [x3, #-8]!
    // 0xea7704: str             x16, [x0, #-8]!
    // 0xea7708: tbz             w4, #3, #0xea7714
    // 0xea770c: ldr             w16, [x3, #-4]!
    // 0xea7710: str             w16, [x0, #-4]!
    // 0xea7714: tbz             w4, #2, #0xea7720
    // 0xea7718: ldrh            w16, [x3, #-2]!
    // 0xea771c: strh            w16, [x0, #-2]!
    // 0xea7720: tbz             w4, #1, #0xea772c
    // 0xea7724: ldrb            w16, [x3, #-1]!
    // 0xea7728: strb            w16, [x0, #-1]!
    // 0xea772c: ands            w4, w4, #0xffffffe1
    // 0xea7730: b.eq            #0xea7790
    // 0xea7734: ldp             x16, x17, [x3, #-0x10]!
    // 0xea7738: stp             x16, x17, [x0, #-0x10]!
    // 0xea773c: subs            w4, w4, #0x20
    // 0xea7740: b.ne            #0xea7734
    // 0xea7744: b               #0xea7790
    // 0xea7748: tbz             w4, #4, #0xea7754
    // 0xea774c: ldr             x16, [x3], #8
    // 0xea7750: str             x16, [x0], #8
    // 0xea7754: tbz             w4, #3, #0xea7760
    // 0xea7758: ldr             w16, [x3], #4
    // 0xea775c: str             w16, [x0], #4
    // 0xea7760: tbz             w4, #2, #0xea776c
    // 0xea7764: ldrh            w16, [x3], #2
    // 0xea7768: strh            w16, [x0], #2
    // 0xea776c: tbz             w4, #1, #0xea7778
    // 0xea7770: ldrb            w16, [x3], #1
    // 0xea7774: strb            w16, [x0], #1
    // 0xea7778: ands            w4, w4, #0xffffffe1
    // 0xea777c: b.eq            #0xea7790
    // 0xea7780: ldp             x16, x17, [x3], #0x10
    // 0xea7784: stp             x16, x17, [x0], #0x10
    // 0xea7788: subs            w4, w4, #0x20
    // 0xea778c: b.ne            #0xea7780
    // 0xea7790: b               #0xea77e0
    // 0xea7794: ldur            x1, [fp, #-0x18]
    // 0xea7798: LoadField: r0 = r1->field_7
    //     0xea7798: ldur            x0, [x1, #7]
    // 0xea779c: LoadField: r1 = r2->field_7
    //     0xea779c: ldur            x1, [x2, #7]
    // 0xea77a0: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea77a0: mov             x2, THR
    //     0xea77a4: ldr             x9, [x2, #0x658]
    //     0xea77a8: mov             x2, x20
    //     0xea77ac: mov             x17, fp
    //     0xea77b0: str             fp, [SP, #-8]!
    //     0xea77b4: mov             fp, SP
    //     0xea77b8: and             SP, SP, #0xfffffffffffffff0
    //     0xea77bc: mov             x19, sp
    //     0xea77c0: mov             sp, SP
    //     0xea77c4: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea77c8: blr             x9
    //     0xea77cc: movz            x16, #0x8
    //     0xea77d0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea77d4: mov             sp, x19
    //     0xea77d8: mov             SP, fp
    //     0xea77dc: ldr             fp, [SP], #8
    // 0xea77e0: ldur            x0, [fp, #-0x10]
    // 0xea77e4: LoadField: r4 = r0->field_f
    //     0xea77e4: ldur            w4, [x0, #0xf]
    // 0xea77e8: DecompressPointer r4
    //     0xea77e8: add             x4, x4, HEAP, lsl #32
    // 0xea77ec: stur            x4, [fp, #-0x50]
    // 0xea77f0: cmp             w4, NULL
    // 0xea77f4: b.eq            #0xea7aa4
    // 0xea77f8: LoadField: r5 = r4->field_13
    //     0xea77f8: ldur            w5, [x4, #0x13]
    // 0xea77fc: stur            x5, [fp, #-0x28]
    // 0xea7800: LoadField: r6 = r0->field_13
    //     0xea7800: ldur            w6, [x0, #0x13]
    // 0xea7804: DecompressPointer r6
    //     0xea7804: add             x6, x6, HEAP, lsl #32
    // 0xea7808: stur            x6, [fp, #-0x18]
    // 0xea780c: cmp             w6, NULL
    // 0xea7810: b.eq            #0xea7aa8
    // 0xea7814: tbnz            x20, #0x3f, #0xea7824
    // 0xea7818: r0 = LoadInt32Instr(r5)
    //     0xea7818: sbfx            x0, x5, #1, #0x1f
    // 0xea781c: cmp             x20, x0
    // 0xea7820: b.le            #0xea7838
    // 0xea7824: r3 = LoadInt32Instr(r5)
    //     0xea7824: sbfx            x3, x5, #1, #0x1f
    // 0xea7828: mov             x1, x20
    // 0xea782c: mov             x2, x5
    // 0xea7830: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea7830: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea7834: r0 = checkValidRange()
    //     0xea7834: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea7838: ldur            x2, [fp, #-0x20]
    // 0xea783c: ldur            x3, [fp, #-0x18]
    // 0xea7840: ldur            x0, [fp, #-0x28]
    // 0xea7844: r1 = LoadInt32Instr(r0)
    //     0xea7844: sbfx            x1, x0, #1, #0x1f
    // 0xea7848: sub             x4, x1, x2
    // 0xea784c: LoadField: r0 = r3->field_13
    //     0xea784c: ldur            w0, [x3, #0x13]
    // 0xea7850: r1 = LoadInt32Instr(r0)
    //     0xea7850: sbfx            x1, x0, #1, #0x1f
    // 0xea7854: cmp             x1, x4
    // 0xea7858: b.lt            #0xea7a48
    // 0xea785c: cbz             x4, #0xea79c0
    // 0xea7860: r0 = BoxInt64Instr(r4)
    //     0xea7860: sbfiz           x0, x4, #1, #0x1f
    //     0xea7864: cmp             x4, x0, asr #1
    //     0xea7868: b.eq            #0xea7874
    //     0xea786c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea7870: stur            x4, [x0, #7]
    // 0xea7874: mov             x5, x0
    // 0xea7878: cmp             w5, #0x800
    // 0xea787c: b.ge            #0xea7964
    // 0xea7880: ldur            x6, [fp, #-0x50]
    // 0xea7884: r0 = BoxInt64Instr(r2)
    //     0xea7884: sbfiz           x0, x2, #1, #0x1f
    //     0xea7888: cmp             x2, x0, asr #1
    //     0xea788c: b.eq            #0xea7898
    //     0xea7890: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea7894: stur            x2, [x0, #7]
    // 0xea7898: add             x2, x3, #0x17
    // 0xea789c: sxtw            x0, w0
    // 0xea78a0: add             x1, x6, x0, asr #1
    // 0xea78a4: add             x1, x1, #0x17
    // 0xea78a8: cbz             x5, #0xea7960
    // 0xea78ac: cmp             x1, x2
    // 0xea78b0: b.ls            #0xea7918
    // 0xea78b4: sxtw            x5, w5
    // 0xea78b8: add             x16, x2, x5, asr #1
    // 0xea78bc: cmp             x1, x16
    // 0xea78c0: b.hs            #0xea7918
    // 0xea78c4: mov             x2, x16
    // 0xea78c8: add             x1, x1, x5, asr #1
    // 0xea78cc: tbz             w5, #4, #0xea78d8
    // 0xea78d0: ldr             x16, [x2, #-8]!
    // 0xea78d4: str             x16, [x1, #-8]!
    // 0xea78d8: tbz             w5, #3, #0xea78e4
    // 0xea78dc: ldr             w16, [x2, #-4]!
    // 0xea78e0: str             w16, [x1, #-4]!
    // 0xea78e4: tbz             w5, #2, #0xea78f0
    // 0xea78e8: ldrh            w16, [x2, #-2]!
    // 0xea78ec: strh            w16, [x1, #-2]!
    // 0xea78f0: tbz             w5, #1, #0xea78fc
    // 0xea78f4: ldrb            w16, [x2, #-1]!
    // 0xea78f8: strb            w16, [x1, #-1]!
    // 0xea78fc: ands            w5, w5, #0xffffffe1
    // 0xea7900: b.eq            #0xea7960
    // 0xea7904: ldp             x16, x17, [x2, #-0x10]!
    // 0xea7908: stp             x16, x17, [x1, #-0x10]!
    // 0xea790c: subs            w5, w5, #0x20
    // 0xea7910: b.ne            #0xea7904
    // 0xea7914: b               #0xea7960
    // 0xea7918: tbz             w5, #4, #0xea7924
    // 0xea791c: ldr             x16, [x2], #8
    // 0xea7920: str             x16, [x1], #8
    // 0xea7924: tbz             w5, #3, #0xea7930
    // 0xea7928: ldr             w16, [x2], #4
    // 0xea792c: str             w16, [x1], #4
    // 0xea7930: tbz             w5, #2, #0xea793c
    // 0xea7934: ldrh            w16, [x2], #2
    // 0xea7938: strh            w16, [x1], #2
    // 0xea793c: tbz             w5, #1, #0xea7948
    // 0xea7940: ldrb            w16, [x2], #1
    // 0xea7944: strb            w16, [x1], #1
    // 0xea7948: ands            w5, w5, #0xffffffe1
    // 0xea794c: b.eq            #0xea7960
    // 0xea7950: ldp             x16, x17, [x2], #0x10
    // 0xea7954: stp             x16, x17, [x1], #0x10
    // 0xea7958: subs            w5, w5, #0x20
    // 0xea795c: b.ne            #0xea7950
    // 0xea7960: b               #0xea79c0
    // 0xea7964: ldur            x6, [fp, #-0x50]
    // 0xea7968: LoadField: r0 = r6->field_7
    //     0xea7968: ldur            x0, [x6, #7]
    // 0xea796c: add             x1, x0, x2
    // 0xea7970: LoadField: r0 = r3->field_7
    //     0xea7970: ldur            x0, [x3, #7]
    // 0xea7974: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea7974: mov             x2, THR
    //     0xea7978: ldr             x9, [x2, #0x658]
    //     0xea797c: mov             x16, x0
    //     0xea7980: mov             x0, x1
    //     0xea7984: mov             x1, x16
    //     0xea7988: mov             x2, x4
    //     0xea798c: mov             x17, fp
    //     0xea7990: str             fp, [SP, #-8]!
    //     0xea7994: mov             fp, SP
    //     0xea7998: and             SP, SP, #0xfffffffffffffff0
    //     0xea799c: mov             x19, sp
    //     0xea79a0: mov             sp, SP
    //     0xea79a4: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea79a8: blr             x9
    //     0xea79ac: movz            x16, #0x8
    //     0xea79b0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea79b4: mov             sp, x19
    //     0xea79b8: mov             SP, fp
    //     0xea79bc: ldr             fp, [SP], #8
    // 0xea79c0: ldur            x1, [fp, #-8]
    // 0xea79c4: r0 = LoadClassIdInstr(r1)
    //     0xea79c4: ldur            x0, [x1, #-1]
    //     0xea79c8: ubfx            x0, x0, #0xc, #0x14
    // 0xea79cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea79cc: sub             lr, x0, #1, lsl #12
    //     0xea79d0: ldr             lr, [x21, lr, lsl #3]
    //     0xea79d4: blr             lr
    // 0xea79d8: LeaveFrame
    //     0xea79d8: mov             SP, fp
    //     0xea79dc: ldp             fp, lr, [SP], #0x10
    // 0xea79e0: ret
    //     0xea79e0: ret             
    // 0xea79e4: r0 = false
    //     0xea79e4: add             x0, NULL, #0x30  ; false
    // 0xea79e8: r0 = ArgumentError()
    //     0xea79e8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea79ec: mov             x1, x0
    // 0xea79f0: r0 = "Input buffer too short"
    //     0xea79f0: add             x0, PP, #0x23, lsl #12  ; [pp+0x23678] "Input buffer too short"
    //     0xea79f4: ldr             x0, [x0, #0x678]
    // 0xea79f8: ArrayStore: r1[0] = r0  ; List_4
    //     0xea79f8: stur            w0, [x1, #0x17]
    // 0xea79fc: r0 = false
    //     0xea79fc: add             x0, NULL, #0x30  ; false
    // 0xea7a00: StoreField: r1->field_b = r0
    //     0xea7a00: stur            w0, [x1, #0xb]
    // 0xea7a04: mov             x0, x1
    // 0xea7a08: r0 = Throw()
    //     0xea7a08: bl              #0xec04b8  ; ThrowStub
    // 0xea7a0c: brk             #0
    // 0xea7a10: r0 = false
    //     0xea7a10: add             x0, NULL, #0x30  ; false
    // 0xea7a14: r0 = ArgumentError()
    //     0xea7a14: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea7a18: mov             x1, x0
    // 0xea7a1c: r0 = "Output buffer too short"
    //     0xea7a1c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23680] "Output buffer too short"
    //     0xea7a20: ldr             x0, [x0, #0x680]
    // 0xea7a24: ArrayStore: r1[0] = r0  ; List_4
    //     0xea7a24: stur            w0, [x1, #0x17]
    // 0xea7a28: r0 = false
    //     0xea7a28: add             x0, NULL, #0x30  ; false
    // 0xea7a2c: StoreField: r1->field_b = r0
    //     0xea7a2c: stur            w0, [x1, #0xb]
    // 0xea7a30: mov             x0, x1
    // 0xea7a34: r0 = Throw()
    //     0xea7a34: bl              #0xec04b8  ; ThrowStub
    // 0xea7a38: brk             #0
    // 0xea7a3c: r0 = tooFew()
    //     0xea7a3c: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea7a40: r0 = Throw()
    //     0xea7a40: bl              #0xec04b8  ; ThrowStub
    // 0xea7a44: brk             #0
    // 0xea7a48: r0 = tooFew()
    //     0xea7a48: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea7a4c: r0 = Throw()
    //     0xea7a4c: bl              #0xec04b8  ; ThrowStub
    // 0xea7a50: brk             #0
    // 0xea7a54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7a54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7a58: b               #0xea71d4
    // 0xea7a5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7a5c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7a60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7a60: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7a64: r9 = _n3
    //     0xea7a64: add             x9, PP, #0x23, lsl #12  ; [pp+0x23688] Field <GCTRBlockCipher._n3@912283055>: late (offset: 0x1c)
    //     0xea7a68: ldr             x9, [x9, #0x688]
    // 0xea7a6c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea7a6c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea7a70: r9 = _n4
    //     0xea7a70: add             x9, PP, #0x23, lsl #12  ; [pp+0x23690] Field <GCTRBlockCipher._n4@912283055>: late (offset: 0x20)
    //     0xea7a74: ldr             x9, [x9, #0x690]
    // 0xea7a78: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea7a78: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea7a7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7a7c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7a80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7a80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7a84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7a88: b               #0xea74f8
    // 0xea7a8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7a8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7a90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7a90: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7a94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7a94: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7a98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7a98: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7a9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7a9c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7aa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7aa0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7aa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7aa4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea7aa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea7aa8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
