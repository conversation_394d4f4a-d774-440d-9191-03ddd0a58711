// lib: impl.block_cipher.modes.ige, url: package:pointycastle/block/modes/ige.dart

// class id: 1050937, size: 0x8
class :: {
}

// class id: 704, size: 0x20, field offset: 0x8
class IGEBlockCipher extends BaseBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xdc0
  late Uint8List _x0; // offset: 0xc
  late Uint8List _y0; // offset: 0x10
  late Uint8List _xPrev; // offset: 0x14
  late Uint8List _yPrev; // offset: 0x18
  late bool _encrypting; // offset: 0x1c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e4dcc, size: 0x64
    // 0x8e4dcc: EnterFrame
    //     0x8e4dcc: stp             fp, lr, [SP, #-0x10]!
    //     0x8e4dd0: mov             fp, SP
    // 0x8e4dd4: AllocStack(0x8)
    //     0x8e4dd4: sub             SP, SP, #8
    // 0x8e4dd8: CheckStackOverflow
    //     0x8e4dd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e4ddc: cmp             SP, x16
    //     0x8e4de0: b.ls            #0x8e4e28
    // 0x8e4de4: r0 = DynamicFactoryConfig()
    //     0x8e4de4: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e4de8: r1 = Function '<anonymous closure>': static.
    //     0x8e4de8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19a78] AnonymousClosure: static (0x8e4e30), in [package:pointycastle/block/modes/ige.dart] IGEBlockCipher::factoryConfig (0x8e4dcc)
    //     0x8e4dec: ldr             x1, [x1, #0xa78]
    // 0x8e4df0: r2 = Null
    //     0x8e4df0: mov             x2, NULL
    // 0x8e4df4: stur            x0, [fp, #-8]
    // 0x8e4df8: r0 = AllocateClosure()
    //     0x8e4df8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e4dfc: ldur            x1, [fp, #-8]
    // 0x8e4e00: mov             x5, x0
    // 0x8e4e04: r2 = BlockCipher
    //     0x8e4e04: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e4e08: ldr             x2, [x2, #0xa80]
    // 0x8e4e0c: r3 = "/IGE"
    //     0x8e4e0c: add             x3, PP, #0x19, lsl #12  ; [pp+0x19a88] "/IGE"
    //     0x8e4e10: ldr             x3, [x3, #0xa88]
    // 0x8e4e14: r0 = DynamicFactoryConfig.suffix()
    //     0x8e4e14: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e4e18: ldur            x0, [fp, #-8]
    // 0x8e4e1c: LeaveFrame
    //     0x8e4e1c: mov             SP, fp
    //     0x8e4e20: ldp             fp, lr, [SP], #0x10
    // 0x8e4e24: ret
    //     0x8e4e24: ret             
    // 0x8e4e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e4e28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e4e2c: b               #0x8e4de4
  }
  [closure] static (dynamic) => IGEBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e4e30, size: 0x54
    // 0x8e4e30: EnterFrame
    //     0x8e4e30: stp             fp, lr, [SP, #-0x10]!
    //     0x8e4e34: mov             fp, SP
    // 0x8e4e38: AllocStack(0x8)
    //     0x8e4e38: sub             SP, SP, #8
    // 0x8e4e3c: SetupParameters()
    //     0x8e4e3c: ldr             x0, [fp, #0x20]
    //     0x8e4e40: ldur            w1, [x0, #0x17]
    //     0x8e4e44: add             x1, x1, HEAP, lsl #32
    //     0x8e4e48: stur            x1, [fp, #-8]
    // 0x8e4e4c: r1 = 1
    //     0x8e4e4c: movz            x1, #0x1
    // 0x8e4e50: r0 = AllocateContext()
    //     0x8e4e50: bl              #0xec126c  ; AllocateContextStub
    // 0x8e4e54: mov             x1, x0
    // 0x8e4e58: ldur            x0, [fp, #-8]
    // 0x8e4e5c: StoreField: r1->field_b = r0
    //     0x8e4e5c: stur            w0, [x1, #0xb]
    // 0x8e4e60: ldr             x0, [fp, #0x10]
    // 0x8e4e64: StoreField: r1->field_f = r0
    //     0x8e4e64: stur            w0, [x1, #0xf]
    // 0x8e4e68: mov             x2, x1
    // 0x8e4e6c: r1 = Function '<anonymous closure>': static.
    //     0x8e4e6c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19a90] AnonymousClosure: static (0x8e4e84), in [package:pointycastle/block/modes/ige.dart] IGEBlockCipher::factoryConfig (0x8e4dcc)
    //     0x8e4e70: ldr             x1, [x1, #0xa90]
    // 0x8e4e74: r0 = AllocateClosure()
    //     0x8e4e74: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e4e78: LeaveFrame
    //     0x8e4e78: mov             SP, fp
    //     0x8e4e7c: ldp             fp, lr, [SP], #0x10
    // 0x8e4e80: ret
    //     0x8e4e80: ret             
  }
  [closure] static IGEBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e4e84, size: 0xcc
    // 0x8e4e84: EnterFrame
    //     0x8e4e84: stp             fp, lr, [SP, #-0x10]!
    //     0x8e4e88: mov             fp, SP
    // 0x8e4e8c: AllocStack(0x20)
    //     0x8e4e8c: sub             SP, SP, #0x20
    // 0x8e4e90: SetupParameters()
    //     0x8e4e90: ldr             x0, [fp, #0x10]
    //     0x8e4e94: ldur            w1, [x0, #0x17]
    //     0x8e4e98: add             x1, x1, HEAP, lsl #32
    // 0x8e4e9c: CheckStackOverflow
    //     0x8e4e9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e4ea0: cmp             SP, x16
    //     0x8e4ea4: b.ls            #0x8e4f44
    // 0x8e4ea8: LoadField: r0 = r1->field_f
    //     0x8e4ea8: ldur            w0, [x1, #0xf]
    // 0x8e4eac: DecompressPointer r0
    //     0x8e4eac: add             x0, x0, HEAP, lsl #32
    // 0x8e4eb0: r1 = LoadClassIdInstr(r0)
    //     0x8e4eb0: ldur            x1, [x0, #-1]
    //     0x8e4eb4: ubfx            x1, x1, #0xc, #0x14
    // 0x8e4eb8: mov             x16, x0
    // 0x8e4ebc: mov             x0, x1
    // 0x8e4ec0: mov             x1, x16
    // 0x8e4ec4: r2 = 1
    //     0x8e4ec4: movz            x2, #0x1
    // 0x8e4ec8: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e4ec8: sub             lr, x0, #0xfdd
    //     0x8e4ecc: ldr             lr, [x21, lr, lsl #3]
    //     0x8e4ed0: blr             lr
    // 0x8e4ed4: stur            x0, [fp, #-8]
    // 0x8e4ed8: cmp             w0, NULL
    // 0x8e4edc: b.eq            #0x8e4f4c
    // 0x8e4ee0: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e4ee0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4ee4: ldr             x0, [x0, #0x2e38]
    //     0x8e4ee8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4eec: cmp             w0, w16
    //     0x8e4ef0: b.ne            #0x8e4f00
    //     0x8e4ef4: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e4ef8: ldr             x2, [x2, #0xf80]
    //     0x8e4efc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4f00: r16 = <BlockCipher>
    //     0x8e4f00: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e4f04: ldr             x16, [x16, #0x88]
    // 0x8e4f08: stp             x0, x16, [SP, #8]
    // 0x8e4f0c: ldur            x16, [fp, #-8]
    // 0x8e4f10: str             x16, [SP]
    // 0x8e4f14: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e4f14: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e4f18: r0 = create()
    //     0x8e4f18: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e4f1c: stur            x0, [fp, #-8]
    // 0x8e4f20: r0 = IGEBlockCipher()
    //     0x8e4f20: bl              #0x8e512c  ; AllocateIGEBlockCipherStub -> IGEBlockCipher (size=0x20)
    // 0x8e4f24: mov             x1, x0
    // 0x8e4f28: ldur            x2, [fp, #-8]
    // 0x8e4f2c: stur            x0, [fp, #-8]
    // 0x8e4f30: r0 = IGEBlockCipher()
    //     0x8e4f30: bl              #0x8e4f50  ; [package:pointycastle/block/modes/ige.dart] IGEBlockCipher::IGEBlockCipher
    // 0x8e4f34: ldur            x0, [fp, #-8]
    // 0x8e4f38: LeaveFrame
    //     0x8e4f38: mov             SP, fp
    //     0x8e4f3c: ldp             fp, lr, [SP], #0x10
    // 0x8e4f40: ret
    //     0x8e4f40: ret             
    // 0x8e4f44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e4f44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e4f48: b               #0x8e4ea8
    // 0x8e4f4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e4f4c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ IGEBlockCipher(/* No info */) {
    // ** addr: 0x8e4f50, size: 0x1dc
    // 0x8e4f50: EnterFrame
    //     0x8e4f50: stp             fp, lr, [SP, #-0x10]!
    //     0x8e4f54: mov             fp, SP
    // 0x8e4f58: AllocStack(0x10)
    //     0x8e4f58: sub             SP, SP, #0x10
    // 0x8e4f5c: r0 = Sentinel
    //     0x8e4f5c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e4f60: mov             x3, x1
    // 0x8e4f64: stur            x1, [fp, #-8]
    // 0x8e4f68: stur            x2, [fp, #-0x10]
    // 0x8e4f6c: CheckStackOverflow
    //     0x8e4f6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e4f70: cmp             SP, x16
    //     0x8e4f74: b.ls            #0x8e5124
    // 0x8e4f78: StoreField: r3->field_b = r0
    //     0x8e4f78: stur            w0, [x3, #0xb]
    // 0x8e4f7c: StoreField: r3->field_f = r0
    //     0x8e4f7c: stur            w0, [x3, #0xf]
    // 0x8e4f80: StoreField: r3->field_13 = r0
    //     0x8e4f80: stur            w0, [x3, #0x13]
    // 0x8e4f84: ArrayStore: r3[0] = r0  ; List_4
    //     0x8e4f84: stur            w0, [x3, #0x17]
    // 0x8e4f88: StoreField: r3->field_1b = r0
    //     0x8e4f88: stur            w0, [x3, #0x1b]
    // 0x8e4f8c: mov             x0, x2
    // 0x8e4f90: StoreField: r3->field_7 = r0
    //     0x8e4f90: stur            w0, [x3, #7]
    //     0x8e4f94: ldurb           w16, [x3, #-1]
    //     0x8e4f98: ldurb           w17, [x0, #-1]
    //     0x8e4f9c: and             x16, x17, x16, lsr #2
    //     0x8e4fa0: tst             x16, HEAP, lsr #32
    //     0x8e4fa4: b.eq            #0x8e4fac
    //     0x8e4fa8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8e4fac: r0 = LoadClassIdInstr(r2)
    //     0x8e4fac: ldur            x0, [x2, #-1]
    //     0x8e4fb0: ubfx            x0, x0, #0xc, #0x14
    // 0x8e4fb4: mov             x1, x2
    // 0x8e4fb8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e4fb8: sub             lr, x0, #1, lsl #12
    //     0x8e4fbc: ldr             lr, [x21, lr, lsl #3]
    //     0x8e4fc0: blr             lr
    // 0x8e4fc4: mov             x2, x0
    // 0x8e4fc8: r0 = BoxInt64Instr(r2)
    //     0x8e4fc8: sbfiz           x0, x2, #1, #0x1f
    //     0x8e4fcc: cmp             x2, x0, asr #1
    //     0x8e4fd0: b.eq            #0x8e4fdc
    //     0x8e4fd4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e4fd8: stur            x2, [x0, #7]
    // 0x8e4fdc: mov             x4, x0
    // 0x8e4fe0: r0 = AllocateUint8Array()
    //     0x8e4fe0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e4fe4: ldur            x2, [fp, #-8]
    // 0x8e4fe8: StoreField: r2->field_b = r0
    //     0x8e4fe8: stur            w0, [x2, #0xb]
    //     0x8e4fec: ldurb           w16, [x2, #-1]
    //     0x8e4ff0: ldurb           w17, [x0, #-1]
    //     0x8e4ff4: and             x16, x17, x16, lsr #2
    //     0x8e4ff8: tst             x16, HEAP, lsr #32
    //     0x8e4ffc: b.eq            #0x8e5004
    //     0x8e5000: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e5004: ldur            x3, [fp, #-0x10]
    // 0x8e5008: r0 = LoadClassIdInstr(r3)
    //     0x8e5008: ldur            x0, [x3, #-1]
    //     0x8e500c: ubfx            x0, x0, #0xc, #0x14
    // 0x8e5010: mov             x1, x3
    // 0x8e5014: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5014: sub             lr, x0, #1, lsl #12
    //     0x8e5018: ldr             lr, [x21, lr, lsl #3]
    //     0x8e501c: blr             lr
    // 0x8e5020: mov             x2, x0
    // 0x8e5024: r0 = BoxInt64Instr(r2)
    //     0x8e5024: sbfiz           x0, x2, #1, #0x1f
    //     0x8e5028: cmp             x2, x0, asr #1
    //     0x8e502c: b.eq            #0x8e5038
    //     0x8e5030: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e5034: stur            x2, [x0, #7]
    // 0x8e5038: mov             x4, x0
    // 0x8e503c: r0 = AllocateUint8Array()
    //     0x8e503c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e5040: ldur            x2, [fp, #-8]
    // 0x8e5044: StoreField: r2->field_f = r0
    //     0x8e5044: stur            w0, [x2, #0xf]
    //     0x8e5048: ldurb           w16, [x2, #-1]
    //     0x8e504c: ldurb           w17, [x0, #-1]
    //     0x8e5050: and             x16, x17, x16, lsr #2
    //     0x8e5054: tst             x16, HEAP, lsr #32
    //     0x8e5058: b.eq            #0x8e5060
    //     0x8e505c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e5060: ldur            x3, [fp, #-0x10]
    // 0x8e5064: r0 = LoadClassIdInstr(r3)
    //     0x8e5064: ldur            x0, [x3, #-1]
    //     0x8e5068: ubfx            x0, x0, #0xc, #0x14
    // 0x8e506c: mov             x1, x3
    // 0x8e5070: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5070: sub             lr, x0, #1, lsl #12
    //     0x8e5074: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5078: blr             lr
    // 0x8e507c: mov             x2, x0
    // 0x8e5080: r0 = BoxInt64Instr(r2)
    //     0x8e5080: sbfiz           x0, x2, #1, #0x1f
    //     0x8e5084: cmp             x2, x0, asr #1
    //     0x8e5088: b.eq            #0x8e5094
    //     0x8e508c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e5090: stur            x2, [x0, #7]
    // 0x8e5094: mov             x4, x0
    // 0x8e5098: r0 = AllocateUint8Array()
    //     0x8e5098: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e509c: ldur            x2, [fp, #-8]
    // 0x8e50a0: StoreField: r2->field_13 = r0
    //     0x8e50a0: stur            w0, [x2, #0x13]
    //     0x8e50a4: ldurb           w16, [x2, #-1]
    //     0x8e50a8: ldurb           w17, [x0, #-1]
    //     0x8e50ac: and             x16, x17, x16, lsr #2
    //     0x8e50b0: tst             x16, HEAP, lsr #32
    //     0x8e50b4: b.eq            #0x8e50bc
    //     0x8e50b8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e50bc: ldur            x1, [fp, #-0x10]
    // 0x8e50c0: r0 = LoadClassIdInstr(r1)
    //     0x8e50c0: ldur            x0, [x1, #-1]
    //     0x8e50c4: ubfx            x0, x0, #0xc, #0x14
    // 0x8e50c8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e50c8: sub             lr, x0, #1, lsl #12
    //     0x8e50cc: ldr             lr, [x21, lr, lsl #3]
    //     0x8e50d0: blr             lr
    // 0x8e50d4: mov             x2, x0
    // 0x8e50d8: r0 = BoxInt64Instr(r2)
    //     0x8e50d8: sbfiz           x0, x2, #1, #0x1f
    //     0x8e50dc: cmp             x2, x0, asr #1
    //     0x8e50e0: b.eq            #0x8e50ec
    //     0x8e50e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e50e8: stur            x2, [x0, #7]
    // 0x8e50ec: mov             x4, x0
    // 0x8e50f0: r0 = AllocateUint8Array()
    //     0x8e50f0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e50f4: ldur            x1, [fp, #-8]
    // 0x8e50f8: ArrayStore: r1[0] = r0  ; List_4
    //     0x8e50f8: stur            w0, [x1, #0x17]
    //     0x8e50fc: ldurb           w16, [x1, #-1]
    //     0x8e5100: ldurb           w17, [x0, #-1]
    //     0x8e5104: and             x16, x17, x16, lsr #2
    //     0x8e5108: tst             x16, HEAP, lsr #32
    //     0x8e510c: b.eq            #0x8e5114
    //     0x8e5110: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e5114: r0 = Null
    //     0x8e5114: mov             x0, NULL
    // 0x8e5118: LeaveFrame
    //     0x8e5118: mov             SP, fp
    //     0x8e511c: ldp             fp, lr, [SP], #0x10
    // 0x8e5120: ret
    //     0x8e5120: ret             
    // 0x8e5124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e5124: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5128: b               #0x8e4f78
  }
  _ reset(/* No info */) {
    // ** addr: 0xe7e4a0, size: 0x224
    // 0xe7e4a0: EnterFrame
    //     0xe7e4a0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7e4a4: mov             fp, SP
    // 0xe7e4a8: AllocStack(0x20)
    //     0xe7e4a8: sub             SP, SP, #0x20
    // 0xe7e4ac: SetupParameters(IGEBlockCipher this /* r1 => r2, fp-0x20 */)
    //     0xe7e4ac: mov             x2, x1
    //     0xe7e4b0: stur            x1, [fp, #-0x20]
    // 0xe7e4b4: CheckStackOverflow
    //     0xe7e4b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7e4b8: cmp             SP, x16
    //     0xe7e4bc: b.ls            #0xe7e66c
    // 0xe7e4c0: LoadField: r3 = r2->field_b
    //     0xe7e4c0: ldur            w3, [x2, #0xb]
    // 0xe7e4c4: DecompressPointer r3
    //     0xe7e4c4: add             x3, x3, HEAP, lsl #32
    // 0xe7e4c8: r16 = Sentinel
    //     0xe7e4c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7e4cc: cmp             w3, w16
    // 0xe7e4d0: b.eq            #0xe7e674
    // 0xe7e4d4: stur            x3, [fp, #-0x18]
    // 0xe7e4d8: LoadField: r4 = r2->field_13
    //     0xe7e4d8: ldur            w4, [x2, #0x13]
    // 0xe7e4dc: DecompressPointer r4
    //     0xe7e4dc: add             x4, x4, HEAP, lsl #32
    // 0xe7e4e0: r16 = Sentinel
    //     0xe7e4e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7e4e4: cmp             w4, w16
    // 0xe7e4e8: b.eq            #0xe7e680
    // 0xe7e4ec: stur            x4, [fp, #-0x10]
    // 0xe7e4f0: LoadField: r5 = r2->field_7
    //     0xe7e4f0: ldur            w5, [x2, #7]
    // 0xe7e4f4: DecompressPointer r5
    //     0xe7e4f4: add             x5, x5, HEAP, lsl #32
    // 0xe7e4f8: stur            x5, [fp, #-8]
    // 0xe7e4fc: r0 = LoadClassIdInstr(r5)
    //     0xe7e4fc: ldur            x0, [x5, #-1]
    //     0xe7e500: ubfx            x0, x0, #0xc, #0x14
    // 0xe7e504: mov             x1, x5
    // 0xe7e508: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe7e508: sub             lr, x0, #1, lsl #12
    //     0xe7e50c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7e510: blr             lr
    // 0xe7e514: mov             x3, x0
    // 0xe7e518: ldur            x2, [fp, #-0x18]
    // 0xe7e51c: LoadField: r0 = r2->field_13
    //     0xe7e51c: ldur            w0, [x2, #0x13]
    // 0xe7e520: r4 = LoadInt32Instr(r0)
    //     0xe7e520: sbfx            x4, x0, #1, #0x1f
    // 0xe7e524: ldur            x5, [fp, #-0x10]
    // 0xe7e528: LoadField: r0 = r5->field_13
    //     0xe7e528: ldur            w0, [x5, #0x13]
    // 0xe7e52c: r6 = LoadInt32Instr(r0)
    //     0xe7e52c: sbfx            x6, x0, #1, #0x1f
    // 0xe7e530: r7 = 0
    //     0xe7e530: movz            x7, #0
    // 0xe7e534: CheckStackOverflow
    //     0xe7e534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7e538: cmp             SP, x16
    //     0xe7e53c: b.ls            #0xe7e68c
    // 0xe7e540: cmp             x7, x3
    // 0xe7e544: b.ge            #0xe7e584
    // 0xe7e548: mov             x0, x4
    // 0xe7e54c: mov             x1, x7
    // 0xe7e550: cmp             x1, x0
    // 0xe7e554: b.hs            #0xe7e694
    // 0xe7e558: ArrayLoad: r8 = r2[r7]  ; List_1
    //     0xe7e558: add             x16, x2, x7
    //     0xe7e55c: ldrb            w8, [x16, #0x17]
    // 0xe7e560: mov             x0, x6
    // 0xe7e564: mov             x1, x7
    // 0xe7e568: cmp             x1, x0
    // 0xe7e56c: b.hs            #0xe7e698
    // 0xe7e570: ArrayStore: r5[r7] = r8  ; TypeUnknown_1
    //     0xe7e570: add             x0, x5, x7
    //     0xe7e574: strb            w8, [x0, #0x17]
    // 0xe7e578: add             x0, x7, #1
    // 0xe7e57c: mov             x7, x0
    // 0xe7e580: b               #0xe7e534
    // 0xe7e584: ldur            x0, [fp, #-0x20]
    // 0xe7e588: ldur            x2, [fp, #-8]
    // 0xe7e58c: LoadField: r3 = r0->field_f
    //     0xe7e58c: ldur            w3, [x0, #0xf]
    // 0xe7e590: DecompressPointer r3
    //     0xe7e590: add             x3, x3, HEAP, lsl #32
    // 0xe7e594: r16 = Sentinel
    //     0xe7e594: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7e598: cmp             w3, w16
    // 0xe7e59c: b.eq            #0xe7e69c
    // 0xe7e5a0: stur            x3, [fp, #-0x18]
    // 0xe7e5a4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xe7e5a4: ldur            w4, [x0, #0x17]
    // 0xe7e5a8: DecompressPointer r4
    //     0xe7e5a8: add             x4, x4, HEAP, lsl #32
    // 0xe7e5ac: r16 = Sentinel
    //     0xe7e5ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7e5b0: cmp             w4, w16
    // 0xe7e5b4: b.eq            #0xe7e6a8
    // 0xe7e5b8: stur            x4, [fp, #-0x10]
    // 0xe7e5bc: r0 = LoadClassIdInstr(r2)
    //     0xe7e5bc: ldur            x0, [x2, #-1]
    //     0xe7e5c0: ubfx            x0, x0, #0xc, #0x14
    // 0xe7e5c4: mov             x1, x2
    // 0xe7e5c8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe7e5c8: sub             lr, x0, #1, lsl #12
    //     0xe7e5cc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7e5d0: blr             lr
    // 0xe7e5d4: mov             x3, x0
    // 0xe7e5d8: ldur            x2, [fp, #-0x18]
    // 0xe7e5dc: LoadField: r0 = r2->field_13
    //     0xe7e5dc: ldur            w0, [x2, #0x13]
    // 0xe7e5e0: r4 = LoadInt32Instr(r0)
    //     0xe7e5e0: sbfx            x4, x0, #1, #0x1f
    // 0xe7e5e4: ldur            x5, [fp, #-0x10]
    // 0xe7e5e8: LoadField: r0 = r5->field_13
    //     0xe7e5e8: ldur            w0, [x5, #0x13]
    // 0xe7e5ec: r6 = LoadInt32Instr(r0)
    //     0xe7e5ec: sbfx            x6, x0, #1, #0x1f
    // 0xe7e5f0: r7 = 0
    //     0xe7e5f0: movz            x7, #0
    // 0xe7e5f4: CheckStackOverflow
    //     0xe7e5f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7e5f8: cmp             SP, x16
    //     0xe7e5fc: b.ls            #0xe7e6b4
    // 0xe7e600: cmp             x7, x3
    // 0xe7e604: b.ge            #0xe7e644
    // 0xe7e608: mov             x0, x4
    // 0xe7e60c: mov             x1, x7
    // 0xe7e610: cmp             x1, x0
    // 0xe7e614: b.hs            #0xe7e6bc
    // 0xe7e618: ArrayLoad: r8 = r2[r7]  ; List_1
    //     0xe7e618: add             x16, x2, x7
    //     0xe7e61c: ldrb            w8, [x16, #0x17]
    // 0xe7e620: mov             x0, x6
    // 0xe7e624: mov             x1, x7
    // 0xe7e628: cmp             x1, x0
    // 0xe7e62c: b.hs            #0xe7e6c0
    // 0xe7e630: ArrayStore: r5[r7] = r8  ; TypeUnknown_1
    //     0xe7e630: add             x0, x5, x7
    //     0xe7e634: strb            w8, [x0, #0x17]
    // 0xe7e638: add             x0, x7, #1
    // 0xe7e63c: mov             x7, x0
    // 0xe7e640: b               #0xe7e5f4
    // 0xe7e644: ldur            x1, [fp, #-8]
    // 0xe7e648: r0 = LoadClassIdInstr(r1)
    //     0xe7e648: ldur            x0, [x1, #-1]
    //     0xe7e64c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7e650: r0 = GDT[cid_x0 + -0xeaf]()
    //     0xe7e650: sub             lr, x0, #0xeaf
    //     0xe7e654: ldr             lr, [x21, lr, lsl #3]
    //     0xe7e658: blr             lr
    // 0xe7e65c: r0 = Null
    //     0xe7e65c: mov             x0, NULL
    // 0xe7e660: LeaveFrame
    //     0xe7e660: mov             SP, fp
    //     0xe7e664: ldp             fp, lr, [SP], #0x10
    // 0xe7e668: ret
    //     0xe7e668: ret             
    // 0xe7e66c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7e66c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7e670: b               #0xe7e4c0
    // 0xe7e674: r9 = _x0
    //     0xe7e674: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d08] Field <IGEBlockCipher._x0@917186754>: late (offset: 0xc)
    //     0xe7e678: ldr             x9, [x9, #0xd08]
    // 0xe7e67c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7e67c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe7e680: r9 = _xPrev
    //     0xe7e680: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d18] Field <IGEBlockCipher._xPrev@917186754>: late (offset: 0x14)
    //     0xe7e684: ldr             x9, [x9, #0xd18]
    // 0xe7e688: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7e688: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe7e68c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7e68c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7e690: b               #0xe7e540
    // 0xe7e694: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7e694: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7e698: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7e698: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7e69c: r9 = _y0
    //     0xe7e69c: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d10] Field <IGEBlockCipher._y0@917186754>: late (offset: 0x10)
    //     0xe7e6a0: ldr             x9, [x9, #0xd10]
    // 0xe7e6a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7e6a4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe7e6a8: r9 = _yPrev
    //     0xe7e6a8: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d20] Field <IGEBlockCipher._yPrev@917186754>: late (offset: 0x18)
    //     0xe7e6ac: ldr             x9, [x9, #0xd20]
    // 0xe7e6b0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7e6b0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe7e6b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7e6b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7e6b8: b               #0xe7e600
    // 0xe7e6bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7e6bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe7e6c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe7e6c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0xe85bd4, size: 0x2e4
    // 0xe85bd4: EnterFrame
    //     0xe85bd4: stp             fp, lr, [SP, #-0x10]!
    //     0xe85bd8: mov             fp, SP
    // 0xe85bdc: AllocStack(0x40)
    //     0xe85bdc: sub             SP, SP, #0x40
    // 0xe85be0: SetupParameters(IGEBlockCipher this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xe85be0: mov             x5, x1
    //     0xe85be4: mov             x4, x2
    //     0xe85be8: stur            x1, [fp, #-8]
    //     0xe85bec: stur            x2, [fp, #-0x10]
    //     0xe85bf0: stur            x3, [fp, #-0x18]
    // 0xe85bf4: CheckStackOverflow
    //     0xe85bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe85bf8: cmp             SP, x16
    //     0xe85bfc: b.ls            #0xe85e78
    // 0xe85c00: mov             x0, x3
    // 0xe85c04: r2 = Null
    //     0xe85c04: mov             x2, NULL
    // 0xe85c08: r1 = Null
    //     0xe85c08: mov             x1, NULL
    // 0xe85c0c: r4 = 60
    //     0xe85c0c: movz            x4, #0x3c
    // 0xe85c10: branchIfSmi(r0, 0xe85c1c)
    //     0xe85c10: tbz             w0, #0, #0xe85c1c
    // 0xe85c14: r4 = LoadClassIdInstr(r0)
    //     0xe85c14: ldur            x4, [x0, #-1]
    //     0xe85c18: ubfx            x4, x4, #0xc, #0x14
    // 0xe85c1c: cmp             x4, #0x2a8
    // 0xe85c20: b.eq            #0xe85c38
    // 0xe85c24: r8 = ParametersWithIV<CipherParameters?>
    //     0xe85c24: add             x8, PP, #0x21, lsl #12  ; [pp+0x21ce8] Type: ParametersWithIV<CipherParameters?>
    //     0xe85c28: ldr             x8, [x8, #0xce8]
    // 0xe85c2c: r3 = Null
    //     0xe85c2c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21cf0] Null
    //     0xe85c30: ldr             x3, [x3, #0xcf0]
    // 0xe85c34: r0 = DefaultTypeTest()
    //     0xe85c34: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe85c38: ldur            x2, [fp, #-0x18]
    // 0xe85c3c: LoadField: r3 = r2->field_b
    //     0xe85c3c: ldur            w3, [x2, #0xb]
    // 0xe85c40: DecompressPointer r3
    //     0xe85c40: add             x3, x3, HEAP, lsl #32
    // 0xe85c44: stur            x3, [fp, #-0x30]
    // 0xe85c48: LoadField: r4 = r3->field_13
    //     0xe85c48: ldur            w4, [x3, #0x13]
    // 0xe85c4c: ldur            x5, [fp, #-8]
    // 0xe85c50: stur            x4, [fp, #-0x28]
    // 0xe85c54: LoadField: r6 = r5->field_7
    //     0xe85c54: ldur            w6, [x5, #7]
    // 0xe85c58: DecompressPointer r6
    //     0xe85c58: add             x6, x6, HEAP, lsl #32
    // 0xe85c5c: stur            x6, [fp, #-0x20]
    // 0xe85c60: r0 = LoadClassIdInstr(r6)
    //     0xe85c60: ldur            x0, [x6, #-1]
    //     0xe85c64: ubfx            x0, x0, #0xc, #0x14
    // 0xe85c68: mov             x1, x6
    // 0xe85c6c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe85c6c: sub             lr, x0, #1, lsl #12
    //     0xe85c70: ldr             lr, [x21, lr, lsl #3]
    //     0xe85c74: blr             lr
    // 0xe85c78: lsl             x1, x0, #1
    // 0xe85c7c: ldur            x0, [fp, #-0x28]
    // 0xe85c80: r2 = LoadInt32Instr(r0)
    //     0xe85c80: sbfx            x2, x0, #1, #0x1f
    // 0xe85c84: stur            x2, [fp, #-0x38]
    // 0xe85c88: cmp             x2, x1
    // 0xe85c8c: b.ne            #0xe85e50
    // 0xe85c90: ldur            x3, [fp, #-8]
    // 0xe85c94: ldur            x5, [fp, #-0x10]
    // 0xe85c98: ldur            x4, [fp, #-0x20]
    // 0xe85c9c: StoreField: r3->field_1b = r5
    //     0xe85c9c: stur            w5, [x3, #0x1b]
    // 0xe85ca0: LoadField: r6 = r3->field_b
    //     0xe85ca0: ldur            w6, [x3, #0xb]
    // 0xe85ca4: DecompressPointer r6
    //     0xe85ca4: add             x6, x6, HEAP, lsl #32
    // 0xe85ca8: r16 = Sentinel
    //     0xe85ca8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe85cac: cmp             w6, w16
    // 0xe85cb0: b.eq            #0xe85e80
    // 0xe85cb4: stur            x6, [fp, #-0x28]
    // 0xe85cb8: r0 = LoadClassIdInstr(r4)
    //     0xe85cb8: ldur            x0, [x4, #-1]
    //     0xe85cbc: ubfx            x0, x0, #0xc, #0x14
    // 0xe85cc0: mov             x1, x4
    // 0xe85cc4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe85cc4: sub             lr, x0, #1, lsl #12
    //     0xe85cc8: ldr             lr, [x21, lr, lsl #3]
    //     0xe85ccc: blr             lr
    // 0xe85cd0: mov             x3, x0
    // 0xe85cd4: ldur            x2, [fp, #-0x28]
    // 0xe85cd8: LoadField: r0 = r2->field_13
    //     0xe85cd8: ldur            w0, [x2, #0x13]
    // 0xe85cdc: r4 = LoadInt32Instr(r0)
    //     0xe85cdc: sbfx            x4, x0, #1, #0x1f
    // 0xe85ce0: ldur            x5, [fp, #-0x30]
    // 0xe85ce4: r6 = 0
    //     0xe85ce4: movz            x6, #0
    // 0xe85ce8: CheckStackOverflow
    //     0xe85ce8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe85cec: cmp             SP, x16
    //     0xe85cf0: b.ls            #0xe85e8c
    // 0xe85cf4: cmp             x6, x3
    // 0xe85cf8: b.ge            #0xe85d38
    // 0xe85cfc: ldur            x0, [fp, #-0x38]
    // 0xe85d00: mov             x1, x6
    // 0xe85d04: cmp             x1, x0
    // 0xe85d08: b.hs            #0xe85e94
    // 0xe85d0c: ArrayLoad: r7 = r5[r6]  ; List_1
    //     0xe85d0c: add             x16, x5, x6
    //     0xe85d10: ldrb            w7, [x16, #0x17]
    // 0xe85d14: mov             x0, x4
    // 0xe85d18: mov             x1, x6
    // 0xe85d1c: cmp             x1, x0
    // 0xe85d20: b.hs            #0xe85e98
    // 0xe85d24: ArrayStore: r2[r6] = r7  ; TypeUnknown_1
    //     0xe85d24: add             x0, x2, x6
    //     0xe85d28: strb            w7, [x0, #0x17]
    // 0xe85d2c: add             x0, x6, #1
    // 0xe85d30: mov             x6, x0
    // 0xe85d34: b               #0xe85ce8
    // 0xe85d38: ldur            x2, [fp, #-8]
    // 0xe85d3c: ldur            x3, [fp, #-0x20]
    // 0xe85d40: r0 = LoadClassIdInstr(r3)
    //     0xe85d40: ldur            x0, [x3, #-1]
    //     0xe85d44: ubfx            x0, x0, #0xc, #0x14
    // 0xe85d48: mov             x1, x3
    // 0xe85d4c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe85d4c: sub             lr, x0, #1, lsl #12
    //     0xe85d50: ldr             lr, [x21, lr, lsl #3]
    //     0xe85d54: blr             lr
    // 0xe85d58: mov             x3, x0
    // 0xe85d5c: ldur            x2, [fp, #-8]
    // 0xe85d60: stur            x3, [fp, #-0x40]
    // 0xe85d64: LoadField: r4 = r2->field_f
    //     0xe85d64: ldur            w4, [x2, #0xf]
    // 0xe85d68: DecompressPointer r4
    //     0xe85d68: add             x4, x4, HEAP, lsl #32
    // 0xe85d6c: r16 = Sentinel
    //     0xe85d6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe85d70: cmp             w4, w16
    // 0xe85d74: b.eq            #0xe85e9c
    // 0xe85d78: ldur            x5, [fp, #-0x20]
    // 0xe85d7c: stur            x4, [fp, #-0x28]
    // 0xe85d80: r0 = LoadClassIdInstr(r5)
    //     0xe85d80: ldur            x0, [x5, #-1]
    //     0xe85d84: ubfx            x0, x0, #0xc, #0x14
    // 0xe85d88: mov             x1, x5
    // 0xe85d8c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe85d8c: sub             lr, x0, #1, lsl #12
    //     0xe85d90: ldr             lr, [x21, lr, lsl #3]
    //     0xe85d94: blr             lr
    // 0xe85d98: mov             x3, x0
    // 0xe85d9c: ldur            x2, [fp, #-0x28]
    // 0xe85da0: LoadField: r0 = r2->field_13
    //     0xe85da0: ldur            w0, [x2, #0x13]
    // 0xe85da4: r4 = LoadInt32Instr(r0)
    //     0xe85da4: sbfx            x4, x0, #1, #0x1f
    // 0xe85da8: ldur            x6, [fp, #-0x30]
    // 0xe85dac: ldur            x5, [fp, #-0x40]
    // 0xe85db0: r7 = 0
    //     0xe85db0: movz            x7, #0
    // 0xe85db4: CheckStackOverflow
    //     0xe85db4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe85db8: cmp             SP, x16
    //     0xe85dbc: b.ls            #0xe85ea8
    // 0xe85dc0: cmp             x7, x3
    // 0xe85dc4: b.ge            #0xe85e08
    // 0xe85dc8: add             x8, x5, x7
    // 0xe85dcc: ldur            x0, [fp, #-0x38]
    // 0xe85dd0: mov             x1, x8
    // 0xe85dd4: cmp             x1, x0
    // 0xe85dd8: b.hs            #0xe85eb0
    // 0xe85ddc: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xe85ddc: add             x16, x6, x8
    //     0xe85de0: ldrb            w9, [x16, #0x17]
    // 0xe85de4: mov             x0, x4
    // 0xe85de8: mov             x1, x7
    // 0xe85dec: cmp             x1, x0
    // 0xe85df0: b.hs            #0xe85eb4
    // 0xe85df4: ArrayStore: r2[r7] = r9  ; TypeUnknown_1
    //     0xe85df4: add             x0, x2, x7
    //     0xe85df8: strb            w9, [x0, #0x17]
    // 0xe85dfc: add             x0, x7, #1
    // 0xe85e00: mov             x7, x0
    // 0xe85e04: b               #0xe85db4
    // 0xe85e08: ldur            x2, [fp, #-0x18]
    // 0xe85e0c: ldur            x0, [fp, #-0x20]
    // 0xe85e10: ldur            x1, [fp, #-8]
    // 0xe85e14: r0 = reset()
    //     0xe85e14: bl              #0xe7e4a0  ; [package:pointycastle/block/modes/ige.dart] IGEBlockCipher::reset
    // 0xe85e18: ldur            x0, [fp, #-0x18]
    // 0xe85e1c: LoadField: r3 = r0->field_f
    //     0xe85e1c: ldur            w3, [x0, #0xf]
    // 0xe85e20: DecompressPointer r3
    //     0xe85e20: add             x3, x3, HEAP, lsl #32
    // 0xe85e24: ldur            x1, [fp, #-0x20]
    // 0xe85e28: r0 = LoadClassIdInstr(r1)
    //     0xe85e28: ldur            x0, [x1, #-1]
    //     0xe85e2c: ubfx            x0, x0, #0xc, #0x14
    // 0xe85e30: ldur            x2, [fp, #-0x10]
    // 0xe85e34: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe85e34: sub             lr, x0, #0xeda
    //     0xe85e38: ldr             lr, [x21, lr, lsl #3]
    //     0xe85e3c: blr             lr
    // 0xe85e40: r0 = Null
    //     0xe85e40: mov             x0, NULL
    // 0xe85e44: LeaveFrame
    //     0xe85e44: mov             SP, fp
    //     0xe85e48: ldp             fp, lr, [SP], #0x10
    // 0xe85e4c: ret
    //     0xe85e4c: ret             
    // 0xe85e50: r0 = ArgumentError()
    //     0xe85e50: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe85e54: mov             x1, x0
    // 0xe85e58: r0 = "Initialization vector must be the same length as block size"
    //     0xe85e58: add             x0, PP, #0x21, lsl #12  ; [pp+0x21d00] "Initialization vector must be the same length as block size"
    //     0xe85e5c: ldr             x0, [x0, #0xd00]
    // 0xe85e60: ArrayStore: r1[0] = r0  ; List_4
    //     0xe85e60: stur            w0, [x1, #0x17]
    // 0xe85e64: r0 = false
    //     0xe85e64: add             x0, NULL, #0x30  ; false
    // 0xe85e68: StoreField: r1->field_b = r0
    //     0xe85e68: stur            w0, [x1, #0xb]
    // 0xe85e6c: mov             x0, x1
    // 0xe85e70: r0 = Throw()
    //     0xe85e70: bl              #0xec04b8  ; ThrowStub
    // 0xe85e74: brk             #0
    // 0xe85e78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe85e78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe85e7c: b               #0xe85c00
    // 0xe85e80: r9 = _x0
    //     0xe85e80: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d08] Field <IGEBlockCipher._x0@917186754>: late (offset: 0xc)
    //     0xe85e84: ldr             x9, [x9, #0xd08]
    // 0xe85e88: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe85e88: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe85e8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe85e8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe85e90: b               #0xe85cf4
    // 0xe85e94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe85e94: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe85e98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe85e98: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe85e9c: r9 = _y0
    //     0xe85e9c: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d10] Field <IGEBlockCipher._y0@917186754>: late (offset: 0x10)
    //     0xe85ea0: ldr             x9, [x9, #0xd10]
    // 0xe85ea4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe85ea4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe85ea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe85ea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe85eac: b               #0xe85dc0
    // 0xe85eb0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe85eb0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe85eb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe85eb4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xea7aac, size: 0x58
    // 0xea7aac: EnterFrame
    //     0xea7aac: stp             fp, lr, [SP, #-0x10]!
    //     0xea7ab0: mov             fp, SP
    // 0xea7ab4: CheckStackOverflow
    //     0xea7ab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea7ab8: cmp             SP, x16
    //     0xea7abc: b.ls            #0xea7af0
    // 0xea7ac0: LoadField: r0 = r1->field_1b
    //     0xea7ac0: ldur            w0, [x1, #0x1b]
    // 0xea7ac4: DecompressPointer r0
    //     0xea7ac4: add             x0, x0, HEAP, lsl #32
    // 0xea7ac8: r16 = Sentinel
    //     0xea7ac8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea7acc: cmp             w0, w16
    // 0xea7ad0: b.eq            #0xea7af8
    // 0xea7ad4: tbnz            w0, #4, #0xea7ae0
    // 0xea7ad8: r0 = _encryptBlock()
    //     0xea7ad8: bl              #0xea7f88  ; [package:pointycastle/block/modes/ige.dart] IGEBlockCipher::_encryptBlock
    // 0xea7adc: b               #0xea7ae4
    // 0xea7ae0: r0 = _decryptBlock()
    //     0xea7ae0: bl              #0xea7b04  ; [package:pointycastle/block/modes/ige.dart] IGEBlockCipher::_decryptBlock
    // 0xea7ae4: LeaveFrame
    //     0xea7ae4: mov             SP, fp
    //     0xea7ae8: ldp             fp, lr, [SP], #0x10
    // 0xea7aec: ret
    //     0xea7aec: ret             
    // 0xea7af0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7af0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7af4: b               #0xea7ac0
    // 0xea7af8: r9 = _encrypting
    //     0xea7af8: add             x9, PP, #0x23, lsl #12  ; [pp+0x23670] Field <IGEBlockCipher._encrypting@917186754>: late (offset: 0x1c)
    //     0xea7afc: ldr             x9, [x9, #0x670]
    // 0xea7b00: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea7b00: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _decryptBlock(/* No info */) {
    // ** addr: 0xea7b04, size: 0x484
    // 0xea7b04: EnterFrame
    //     0xea7b04: stp             fp, lr, [SP, #-0x10]!
    //     0xea7b08: mov             fp, SP
    // 0xea7b0c: AllocStack(0x60)
    //     0xea7b0c: sub             SP, SP, #0x60
    // 0xea7b10: SetupParameters(IGEBlockCipher this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* r6 => r6, fp-0x30 */)
    //     0xea7b10: mov             x4, x1
    //     0xea7b14: stur            x1, [fp, #-0x10]
    //     0xea7b18: stur            x2, [fp, #-0x18]
    //     0xea7b1c: stur            x3, [fp, #-0x20]
    //     0xea7b20: stur            x5, [fp, #-0x28]
    //     0xea7b24: stur            x6, [fp, #-0x30]
    // 0xea7b28: CheckStackOverflow
    //     0xea7b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea7b2c: cmp             SP, x16
    //     0xea7b30: b.ls            #0xea7f04
    // 0xea7b34: LoadField: r7 = r4->field_7
    //     0xea7b34: ldur            w7, [x4, #7]
    // 0xea7b38: DecompressPointer r7
    //     0xea7b38: add             x7, x7, HEAP, lsl #32
    // 0xea7b3c: stur            x7, [fp, #-8]
    // 0xea7b40: r0 = LoadClassIdInstr(r7)
    //     0xea7b40: ldur            x0, [x7, #-1]
    //     0xea7b44: ubfx            x0, x0, #0xc, #0x14
    // 0xea7b48: mov             x1, x7
    // 0xea7b4c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea7b4c: sub             lr, x0, #1, lsl #12
    //     0xea7b50: ldr             lr, [x21, lr, lsl #3]
    //     0xea7b54: blr             lr
    // 0xea7b58: ldur            x2, [fp, #-0x20]
    // 0xea7b5c: add             x1, x2, x0
    // 0xea7b60: ldur            x3, [fp, #-0x18]
    // 0xea7b64: LoadField: r0 = r3->field_13
    //     0xea7b64: ldur            w0, [x3, #0x13]
    // 0xea7b68: r4 = LoadInt32Instr(r0)
    //     0xea7b68: sbfx            x4, x0, #1, #0x1f
    // 0xea7b6c: stur            x4, [fp, #-0x40]
    // 0xea7b70: cmp             x1, x4
    // 0xea7b74: b.gt            #0xea7edc
    // 0xea7b78: ldur            x5, [fp, #-0x10]
    // 0xea7b7c: r7 = 0
    //     0xea7b7c: movz            x7, #0
    // 0xea7b80: ldur            x6, [fp, #-8]
    // 0xea7b84: stur            x7, [fp, #-0x38]
    // 0xea7b88: CheckStackOverflow
    //     0xea7b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea7b8c: cmp             SP, x16
    //     0xea7b90: b.ls            #0xea7f0c
    // 0xea7b94: r0 = LoadClassIdInstr(r6)
    //     0xea7b94: ldur            x0, [x6, #-1]
    //     0xea7b98: ubfx            x0, x0, #0xc, #0x14
    // 0xea7b9c: mov             x1, x6
    // 0xea7ba0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea7ba0: sub             lr, x0, #1, lsl #12
    //     0xea7ba4: ldr             lr, [x21, lr, lsl #3]
    //     0xea7ba8: blr             lr
    // 0xea7bac: ldur            x2, [fp, #-0x38]
    // 0xea7bb0: cmp             x2, x0
    // 0xea7bb4: b.ge            #0xea7c38
    // 0xea7bb8: ldur            x10, [fp, #-0x10]
    // 0xea7bbc: ldur            x8, [fp, #-0x18]
    // 0xea7bc0: ldur            x4, [fp, #-0x20]
    // 0xea7bc4: ArrayLoad: r3 = r10[0]  ; List_4
    //     0xea7bc4: ldur            w3, [x10, #0x17]
    // 0xea7bc8: DecompressPointer r3
    //     0xea7bc8: add             x3, x3, HEAP, lsl #32
    // 0xea7bcc: r16 = Sentinel
    //     0xea7bcc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea7bd0: cmp             w3, w16
    // 0xea7bd4: b.eq            #0xea7f14
    // 0xea7bd8: LoadField: r0 = r3->field_13
    //     0xea7bd8: ldur            w0, [x3, #0x13]
    // 0xea7bdc: r1 = LoadInt32Instr(r0)
    //     0xea7bdc: sbfx            x1, x0, #1, #0x1f
    // 0xea7be0: mov             x0, x1
    // 0xea7be4: mov             x1, x2
    // 0xea7be8: cmp             x1, x0
    // 0xea7bec: b.hs            #0xea7f20
    // 0xea7bf0: ArrayLoad: r5 = r3[r2]  ; List_1
    //     0xea7bf0: add             x16, x3, x2
    //     0xea7bf4: ldrb            w5, [x16, #0x17]
    // 0xea7bf8: add             x6, x4, x2
    // 0xea7bfc: ldur            x0, [fp, #-0x40]
    // 0xea7c00: mov             x1, x6
    // 0xea7c04: cmp             x1, x0
    // 0xea7c08: b.hs            #0xea7f24
    // 0xea7c0c: ArrayLoad: r0 = r8[r6]  ; List_1
    //     0xea7c0c: add             x16, x8, x6
    //     0xea7c10: ldrb            w0, [x16, #0x17]
    // 0xea7c14: eor             x1, x5, x0
    // 0xea7c18: ArrayStore: r3[r2] = r1  ; TypeUnknown_1
    //     0xea7c18: add             x0, x3, x2
    //     0xea7c1c: strb            w1, [x0, #0x17]
    // 0xea7c20: add             x7, x2, #1
    // 0xea7c24: mov             x5, x10
    // 0xea7c28: mov             x3, x8
    // 0xea7c2c: mov             x2, x4
    // 0xea7c30: ldur            x4, [fp, #-0x40]
    // 0xea7c34: b               #0xea7b80
    // 0xea7c38: ldur            x10, [fp, #-0x10]
    // 0xea7c3c: ldur            x8, [fp, #-0x18]
    // 0xea7c40: ldur            x4, [fp, #-0x20]
    // 0xea7c44: ldur            x11, [fp, #-0x28]
    // 0xea7c48: ldur            x7, [fp, #-8]
    // 0xea7c4c: ArrayLoad: r2 = r10[0]  ; List_4
    //     0xea7c4c: ldur            w2, [x10, #0x17]
    // 0xea7c50: DecompressPointer r2
    //     0xea7c50: add             x2, x2, HEAP, lsl #32
    // 0xea7c54: r16 = Sentinel
    //     0xea7c54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea7c58: cmp             w2, w16
    // 0xea7c5c: b.eq            #0xea7f28
    // 0xea7c60: r0 = LoadClassIdInstr(r7)
    //     0xea7c60: ldur            x0, [x7, #-1]
    //     0xea7c64: ubfx            x0, x0, #0xc, #0x14
    // 0xea7c68: mov             x1, x7
    // 0xea7c6c: mov             x5, x11
    // 0xea7c70: ldur            x6, [fp, #-0x30]
    // 0xea7c74: r3 = 0
    //     0xea7c74: movz            x3, #0
    // 0xea7c78: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea7c78: sub             lr, x0, #0xf69
    //     0xea7c7c: ldr             lr, [x21, lr, lsl #3]
    //     0xea7c80: blr             lr
    // 0xea7c84: mov             x3, x0
    // 0xea7c88: ldur            x2, [fp, #-0x28]
    // 0xea7c8c: stur            x3, [fp, #-0x58]
    // 0xea7c90: LoadField: r4 = r2->field_13
    //     0xea7c90: ldur            w4, [x2, #0x13]
    // 0xea7c94: stur            x4, [fp, #-0x50]
    // 0xea7c98: r5 = LoadInt32Instr(r4)
    //     0xea7c98: sbfx            x5, x4, #1, #0x1f
    // 0xea7c9c: stur            x5, [fp, #-0x48]
    // 0xea7ca0: ldur            x6, [fp, #-0x10]
    // 0xea7ca4: ldur            x8, [fp, #-0x30]
    // 0xea7ca8: r9 = 0
    //     0xea7ca8: movz            x9, #0
    // 0xea7cac: ldur            x7, [fp, #-8]
    // 0xea7cb0: stur            x9, [fp, #-0x38]
    // 0xea7cb4: CheckStackOverflow
    //     0xea7cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea7cb8: cmp             SP, x16
    //     0xea7cbc: b.ls            #0xea7f34
    // 0xea7cc0: r0 = LoadClassIdInstr(r7)
    //     0xea7cc0: ldur            x0, [x7, #-1]
    //     0xea7cc4: ubfx            x0, x0, #0xc, #0x14
    // 0xea7cc8: mov             x1, x7
    // 0xea7ccc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea7ccc: sub             lr, x0, #1, lsl #12
    //     0xea7cd0: ldr             lr, [x21, lr, lsl #3]
    //     0xea7cd4: blr             lr
    // 0xea7cd8: ldur            x2, [fp, #-0x38]
    // 0xea7cdc: cmp             x2, x0
    // 0xea7ce0: b.ge            #0xea7d6c
    // 0xea7ce4: ldur            x4, [fp, #-0x10]
    // 0xea7ce8: ldur            x3, [fp, #-0x28]
    // 0xea7cec: ldur            x5, [fp, #-0x30]
    // 0xea7cf0: add             x6, x5, x2
    // 0xea7cf4: ldur            x0, [fp, #-0x48]
    // 0xea7cf8: mov             x1, x6
    // 0xea7cfc: cmp             x1, x0
    // 0xea7d00: b.hs            #0xea7f3c
    // 0xea7d04: ArrayLoad: r7 = r3[r6]  ; List_1
    //     0xea7d04: add             x16, x3, x6
    //     0xea7d08: ldrb            w7, [x16, #0x17]
    // 0xea7d0c: LoadField: r8 = r4->field_13
    //     0xea7d0c: ldur            w8, [x4, #0x13]
    // 0xea7d10: DecompressPointer r8
    //     0xea7d10: add             x8, x8, HEAP, lsl #32
    // 0xea7d14: r16 = Sentinel
    //     0xea7d14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea7d18: cmp             w8, w16
    // 0xea7d1c: b.eq            #0xea7f40
    // 0xea7d20: LoadField: r0 = r8->field_13
    //     0xea7d20: ldur            w0, [x8, #0x13]
    // 0xea7d24: r1 = LoadInt32Instr(r0)
    //     0xea7d24: sbfx            x1, x0, #1, #0x1f
    // 0xea7d28: mov             x0, x1
    // 0xea7d2c: mov             x1, x2
    // 0xea7d30: cmp             x1, x0
    // 0xea7d34: b.hs            #0xea7f4c
    // 0xea7d38: ArrayLoad: r0 = r8[r2]  ; List_1
    //     0xea7d38: add             x16, x8, x2
    //     0xea7d3c: ldrb            w0, [x16, #0x17]
    // 0xea7d40: eor             x1, x7, x0
    // 0xea7d44: ArrayStore: r3[r6] = r1  ; TypeUnknown_1
    //     0xea7d44: add             x0, x3, x6
    //     0xea7d48: strb            w1, [x0, #0x17]
    // 0xea7d4c: add             x9, x2, #1
    // 0xea7d50: mov             x6, x4
    // 0xea7d54: mov             x2, x3
    // 0xea7d58: mov             x8, x5
    // 0xea7d5c: ldur            x3, [fp, #-0x58]
    // 0xea7d60: ldur            x4, [fp, #-0x50]
    // 0xea7d64: ldur            x5, [fp, #-0x48]
    // 0xea7d68: b               #0xea7cac
    // 0xea7d6c: ldur            x4, [fp, #-0x10]
    // 0xea7d70: ldur            x3, [fp, #-0x28]
    // 0xea7d74: ldur            x5, [fp, #-0x30]
    // 0xea7d78: ldur            x2, [fp, #-0x50]
    // 0xea7d7c: ldur            x6, [fp, #-8]
    // 0xea7d80: ArrayLoad: r7 = r4[0]  ; List_4
    //     0xea7d80: ldur            w7, [x4, #0x17]
    // 0xea7d84: DecompressPointer r7
    //     0xea7d84: add             x7, x7, HEAP, lsl #32
    // 0xea7d88: r16 = Sentinel
    //     0xea7d88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea7d8c: cmp             w7, w16
    // 0xea7d90: b.eq            #0xea7f50
    // 0xea7d94: stur            x7, [fp, #-0x60]
    // 0xea7d98: r0 = LoadClassIdInstr(r6)
    //     0xea7d98: ldur            x0, [x6, #-1]
    //     0xea7d9c: ubfx            x0, x0, #0xc, #0x14
    // 0xea7da0: mov             x1, x6
    // 0xea7da4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea7da4: sub             lr, x0, #1, lsl #12
    //     0xea7da8: ldr             lr, [x21, lr, lsl #3]
    //     0xea7dac: blr             lr
    // 0xea7db0: mov             x2, x0
    // 0xea7db4: ldur            x0, [fp, #-0x50]
    // 0xea7db8: r3 = LoadInt32Instr(r0)
    //     0xea7db8: sbfx            x3, x0, #1, #0x1f
    // 0xea7dbc: ldur            x4, [fp, #-0x60]
    // 0xea7dc0: LoadField: r0 = r4->field_13
    //     0xea7dc0: ldur            w0, [x4, #0x13]
    // 0xea7dc4: r5 = LoadInt32Instr(r0)
    //     0xea7dc4: sbfx            x5, x0, #1, #0x1f
    // 0xea7dc8: ldur            x6, [fp, #-0x28]
    // 0xea7dcc: ldur            x7, [fp, #-0x30]
    // 0xea7dd0: r8 = 0
    //     0xea7dd0: movz            x8, #0
    // 0xea7dd4: CheckStackOverflow
    //     0xea7dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea7dd8: cmp             SP, x16
    //     0xea7ddc: b.ls            #0xea7f5c
    // 0xea7de0: cmp             x8, x2
    // 0xea7de4: b.ge            #0xea7e28
    // 0xea7de8: add             x9, x7, x8
    // 0xea7dec: mov             x0, x3
    // 0xea7df0: mov             x1, x9
    // 0xea7df4: cmp             x1, x0
    // 0xea7df8: b.hs            #0xea7f64
    // 0xea7dfc: ArrayLoad: r10 = r6[r9]  ; List_1
    //     0xea7dfc: add             x16, x6, x9
    //     0xea7e00: ldrb            w10, [x16, #0x17]
    // 0xea7e04: mov             x0, x5
    // 0xea7e08: mov             x1, x8
    // 0xea7e0c: cmp             x1, x0
    // 0xea7e10: b.hs            #0xea7f68
    // 0xea7e14: ArrayStore: r4[r8] = r10  ; TypeUnknown_1
    //     0xea7e14: add             x0, x4, x8
    //     0xea7e18: strb            w10, [x0, #0x17]
    // 0xea7e1c: add             x0, x8, #1
    // 0xea7e20: mov             x8, x0
    // 0xea7e24: b               #0xea7dd4
    // 0xea7e28: ldur            x0, [fp, #-0x10]
    // 0xea7e2c: ldur            x1, [fp, #-8]
    // 0xea7e30: LoadField: r2 = r0->field_13
    //     0xea7e30: ldur            w2, [x0, #0x13]
    // 0xea7e34: DecompressPointer r2
    //     0xea7e34: add             x2, x2, HEAP, lsl #32
    // 0xea7e38: r16 = Sentinel
    //     0xea7e38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea7e3c: cmp             w2, w16
    // 0xea7e40: b.eq            #0xea7f6c
    // 0xea7e44: stur            x2, [fp, #-0x28]
    // 0xea7e48: r0 = LoadClassIdInstr(r1)
    //     0xea7e48: ldur            x0, [x1, #-1]
    //     0xea7e4c: ubfx            x0, x0, #0xc, #0x14
    // 0xea7e50: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea7e50: sub             lr, x0, #1, lsl #12
    //     0xea7e54: ldr             lr, [x21, lr, lsl #3]
    //     0xea7e58: blr             lr
    // 0xea7e5c: mov             x3, x0
    // 0xea7e60: ldur            x2, [fp, #-0x28]
    // 0xea7e64: LoadField: r0 = r2->field_13
    //     0xea7e64: ldur            w0, [x2, #0x13]
    // 0xea7e68: r4 = LoadInt32Instr(r0)
    //     0xea7e68: sbfx            x4, x0, #1, #0x1f
    // 0xea7e6c: ldur            x6, [fp, #-0x18]
    // 0xea7e70: ldur            x5, [fp, #-0x20]
    // 0xea7e74: r7 = 0
    //     0xea7e74: movz            x7, #0
    // 0xea7e78: CheckStackOverflow
    //     0xea7e78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea7e7c: cmp             SP, x16
    //     0xea7e80: b.ls            #0xea7f78
    // 0xea7e84: cmp             x7, x3
    // 0xea7e88: b.ge            #0xea7ecc
    // 0xea7e8c: add             x8, x5, x7
    // 0xea7e90: ldur            x0, [fp, #-0x40]
    // 0xea7e94: mov             x1, x8
    // 0xea7e98: cmp             x1, x0
    // 0xea7e9c: b.hs            #0xea7f80
    // 0xea7ea0: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea7ea0: add             x16, x6, x8
    //     0xea7ea4: ldrb            w9, [x16, #0x17]
    // 0xea7ea8: mov             x0, x4
    // 0xea7eac: mov             x1, x7
    // 0xea7eb0: cmp             x1, x0
    // 0xea7eb4: b.hs            #0xea7f84
    // 0xea7eb8: ArrayStore: r2[r7] = r9  ; TypeUnknown_1
    //     0xea7eb8: add             x0, x2, x7
    //     0xea7ebc: strb            w9, [x0, #0x17]
    // 0xea7ec0: add             x0, x7, #1
    // 0xea7ec4: mov             x7, x0
    // 0xea7ec8: b               #0xea7e78
    // 0xea7ecc: ldur            x0, [fp, #-0x58]
    // 0xea7ed0: LeaveFrame
    //     0xea7ed0: mov             SP, fp
    //     0xea7ed4: ldp             fp, lr, [SP], #0x10
    // 0xea7ed8: ret
    //     0xea7ed8: ret             
    // 0xea7edc: r0 = ArgumentError()
    //     0xea7edc: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea7ee0: mov             x1, x0
    // 0xea7ee4: r0 = "Input buffer too short"
    //     0xea7ee4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23678] "Input buffer too short"
    //     0xea7ee8: ldr             x0, [x0, #0x678]
    // 0xea7eec: ArrayStore: r1[0] = r0  ; List_4
    //     0xea7eec: stur            w0, [x1, #0x17]
    // 0xea7ef0: r0 = false
    //     0xea7ef0: add             x0, NULL, #0x30  ; false
    // 0xea7ef4: StoreField: r1->field_b = r0
    //     0xea7ef4: stur            w0, [x1, #0xb]
    // 0xea7ef8: mov             x0, x1
    // 0xea7efc: r0 = Throw()
    //     0xea7efc: bl              #0xec04b8  ; ThrowStub
    // 0xea7f00: brk             #0
    // 0xea7f04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7f04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7f08: b               #0xea7b34
    // 0xea7f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7f10: b               #0xea7b94
    // 0xea7f14: r9 = _yPrev
    //     0xea7f14: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d20] Field <IGEBlockCipher._yPrev@917186754>: late (offset: 0x18)
    //     0xea7f18: ldr             x9, [x9, #0xd20]
    // 0xea7f1c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea7f1c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea7f20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7f20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7f24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7f24: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7f28: r9 = _yPrev
    //     0xea7f28: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d20] Field <IGEBlockCipher._yPrev@917186754>: late (offset: 0x18)
    //     0xea7f2c: ldr             x9, [x9, #0xd20]
    // 0xea7f30: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea7f30: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea7f34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7f34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7f38: b               #0xea7cc0
    // 0xea7f3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7f3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7f40: r9 = _xPrev
    //     0xea7f40: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d18] Field <IGEBlockCipher._xPrev@917186754>: late (offset: 0x14)
    //     0xea7f44: ldr             x9, [x9, #0xd18]
    // 0xea7f48: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea7f48: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea7f4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7f4c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7f50: r9 = _yPrev
    //     0xea7f50: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d20] Field <IGEBlockCipher._yPrev@917186754>: late (offset: 0x18)
    //     0xea7f54: ldr             x9, [x9, #0xd20]
    // 0xea7f58: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea7f58: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea7f5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7f5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7f60: b               #0xea7de0
    // 0xea7f64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7f64: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7f68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7f68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7f6c: r9 = _xPrev
    //     0xea7f6c: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d18] Field <IGEBlockCipher._xPrev@917186754>: late (offset: 0x14)
    //     0xea7f70: ldr             x9, [x9, #0xd18]
    // 0xea7f74: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea7f74: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea7f78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea7f78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea7f7c: b               #0xea7e84
    // 0xea7f80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7f80: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea7f84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea7f84: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _encryptBlock(/* No info */) {
    // ** addr: 0xea7f88, size: 0x484
    // 0xea7f88: EnterFrame
    //     0xea7f88: stp             fp, lr, [SP, #-0x10]!
    //     0xea7f8c: mov             fp, SP
    // 0xea7f90: AllocStack(0x60)
    //     0xea7f90: sub             SP, SP, #0x60
    // 0xea7f94: SetupParameters(IGEBlockCipher this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* r6 => r6, fp-0x30 */)
    //     0xea7f94: mov             x4, x1
    //     0xea7f98: stur            x1, [fp, #-0x10]
    //     0xea7f9c: stur            x2, [fp, #-0x18]
    //     0xea7fa0: stur            x3, [fp, #-0x20]
    //     0xea7fa4: stur            x5, [fp, #-0x28]
    //     0xea7fa8: stur            x6, [fp, #-0x30]
    // 0xea7fac: CheckStackOverflow
    //     0xea7fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea7fb0: cmp             SP, x16
    //     0xea7fb4: b.ls            #0xea8388
    // 0xea7fb8: LoadField: r7 = r4->field_7
    //     0xea7fb8: ldur            w7, [x4, #7]
    // 0xea7fbc: DecompressPointer r7
    //     0xea7fbc: add             x7, x7, HEAP, lsl #32
    // 0xea7fc0: stur            x7, [fp, #-8]
    // 0xea7fc4: r0 = LoadClassIdInstr(r7)
    //     0xea7fc4: ldur            x0, [x7, #-1]
    //     0xea7fc8: ubfx            x0, x0, #0xc, #0x14
    // 0xea7fcc: mov             x1, x7
    // 0xea7fd0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea7fd0: sub             lr, x0, #1, lsl #12
    //     0xea7fd4: ldr             lr, [x21, lr, lsl #3]
    //     0xea7fd8: blr             lr
    // 0xea7fdc: ldur            x2, [fp, #-0x20]
    // 0xea7fe0: add             x1, x2, x0
    // 0xea7fe4: ldur            x3, [fp, #-0x18]
    // 0xea7fe8: LoadField: r0 = r3->field_13
    //     0xea7fe8: ldur            w0, [x3, #0x13]
    // 0xea7fec: r4 = LoadInt32Instr(r0)
    //     0xea7fec: sbfx            x4, x0, #1, #0x1f
    // 0xea7ff0: stur            x4, [fp, #-0x40]
    // 0xea7ff4: cmp             x1, x4
    // 0xea7ff8: b.gt            #0xea8360
    // 0xea7ffc: ldur            x5, [fp, #-0x10]
    // 0xea8000: r7 = 0
    //     0xea8000: movz            x7, #0
    // 0xea8004: ldur            x6, [fp, #-8]
    // 0xea8008: stur            x7, [fp, #-0x38]
    // 0xea800c: CheckStackOverflow
    //     0xea800c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8010: cmp             SP, x16
    //     0xea8014: b.ls            #0xea8390
    // 0xea8018: r0 = LoadClassIdInstr(r6)
    //     0xea8018: ldur            x0, [x6, #-1]
    //     0xea801c: ubfx            x0, x0, #0xc, #0x14
    // 0xea8020: mov             x1, x6
    // 0xea8024: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea8024: sub             lr, x0, #1, lsl #12
    //     0xea8028: ldr             lr, [x21, lr, lsl #3]
    //     0xea802c: blr             lr
    // 0xea8030: ldur            x2, [fp, #-0x38]
    // 0xea8034: cmp             x2, x0
    // 0xea8038: b.ge            #0xea80bc
    // 0xea803c: ldur            x10, [fp, #-0x10]
    // 0xea8040: ldur            x8, [fp, #-0x18]
    // 0xea8044: ldur            x4, [fp, #-0x20]
    // 0xea8048: LoadField: r3 = r10->field_13
    //     0xea8048: ldur            w3, [x10, #0x13]
    // 0xea804c: DecompressPointer r3
    //     0xea804c: add             x3, x3, HEAP, lsl #32
    // 0xea8050: r16 = Sentinel
    //     0xea8050: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea8054: cmp             w3, w16
    // 0xea8058: b.eq            #0xea8398
    // 0xea805c: LoadField: r0 = r3->field_13
    //     0xea805c: ldur            w0, [x3, #0x13]
    // 0xea8060: r1 = LoadInt32Instr(r0)
    //     0xea8060: sbfx            x1, x0, #1, #0x1f
    // 0xea8064: mov             x0, x1
    // 0xea8068: mov             x1, x2
    // 0xea806c: cmp             x1, x0
    // 0xea8070: b.hs            #0xea83a4
    // 0xea8074: ArrayLoad: r5 = r3[r2]  ; List_1
    //     0xea8074: add             x16, x3, x2
    //     0xea8078: ldrb            w5, [x16, #0x17]
    // 0xea807c: add             x6, x4, x2
    // 0xea8080: ldur            x0, [fp, #-0x40]
    // 0xea8084: mov             x1, x6
    // 0xea8088: cmp             x1, x0
    // 0xea808c: b.hs            #0xea83a8
    // 0xea8090: ArrayLoad: r0 = r8[r6]  ; List_1
    //     0xea8090: add             x16, x8, x6
    //     0xea8094: ldrb            w0, [x16, #0x17]
    // 0xea8098: eor             x1, x5, x0
    // 0xea809c: ArrayStore: r3[r2] = r1  ; TypeUnknown_1
    //     0xea809c: add             x0, x3, x2
    //     0xea80a0: strb            w1, [x0, #0x17]
    // 0xea80a4: add             x7, x2, #1
    // 0xea80a8: mov             x5, x10
    // 0xea80ac: mov             x3, x8
    // 0xea80b0: mov             x2, x4
    // 0xea80b4: ldur            x4, [fp, #-0x40]
    // 0xea80b8: b               #0xea8004
    // 0xea80bc: ldur            x10, [fp, #-0x10]
    // 0xea80c0: ldur            x8, [fp, #-0x18]
    // 0xea80c4: ldur            x4, [fp, #-0x20]
    // 0xea80c8: ldur            x11, [fp, #-0x28]
    // 0xea80cc: ldur            x7, [fp, #-8]
    // 0xea80d0: LoadField: r2 = r10->field_13
    //     0xea80d0: ldur            w2, [x10, #0x13]
    // 0xea80d4: DecompressPointer r2
    //     0xea80d4: add             x2, x2, HEAP, lsl #32
    // 0xea80d8: r16 = Sentinel
    //     0xea80d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea80dc: cmp             w2, w16
    // 0xea80e0: b.eq            #0xea83ac
    // 0xea80e4: r0 = LoadClassIdInstr(r7)
    //     0xea80e4: ldur            x0, [x7, #-1]
    //     0xea80e8: ubfx            x0, x0, #0xc, #0x14
    // 0xea80ec: mov             x1, x7
    // 0xea80f0: mov             x5, x11
    // 0xea80f4: ldur            x6, [fp, #-0x30]
    // 0xea80f8: r3 = 0
    //     0xea80f8: movz            x3, #0
    // 0xea80fc: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea80fc: sub             lr, x0, #0xf69
    //     0xea8100: ldr             lr, [x21, lr, lsl #3]
    //     0xea8104: blr             lr
    // 0xea8108: mov             x3, x0
    // 0xea810c: ldur            x2, [fp, #-0x28]
    // 0xea8110: stur            x3, [fp, #-0x58]
    // 0xea8114: LoadField: r4 = r2->field_13
    //     0xea8114: ldur            w4, [x2, #0x13]
    // 0xea8118: stur            x4, [fp, #-0x50]
    // 0xea811c: r5 = LoadInt32Instr(r4)
    //     0xea811c: sbfx            x5, x4, #1, #0x1f
    // 0xea8120: stur            x5, [fp, #-0x48]
    // 0xea8124: ldur            x6, [fp, #-0x10]
    // 0xea8128: ldur            x8, [fp, #-0x30]
    // 0xea812c: r9 = 0
    //     0xea812c: movz            x9, #0
    // 0xea8130: ldur            x7, [fp, #-8]
    // 0xea8134: stur            x9, [fp, #-0x38]
    // 0xea8138: CheckStackOverflow
    //     0xea8138: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea813c: cmp             SP, x16
    //     0xea8140: b.ls            #0xea83b8
    // 0xea8144: r0 = LoadClassIdInstr(r7)
    //     0xea8144: ldur            x0, [x7, #-1]
    //     0xea8148: ubfx            x0, x0, #0xc, #0x14
    // 0xea814c: mov             x1, x7
    // 0xea8150: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea8150: sub             lr, x0, #1, lsl #12
    //     0xea8154: ldr             lr, [x21, lr, lsl #3]
    //     0xea8158: blr             lr
    // 0xea815c: ldur            x2, [fp, #-0x38]
    // 0xea8160: cmp             x2, x0
    // 0xea8164: b.ge            #0xea81f0
    // 0xea8168: ldur            x4, [fp, #-0x10]
    // 0xea816c: ldur            x3, [fp, #-0x28]
    // 0xea8170: ldur            x5, [fp, #-0x30]
    // 0xea8174: add             x6, x5, x2
    // 0xea8178: ldur            x0, [fp, #-0x48]
    // 0xea817c: mov             x1, x6
    // 0xea8180: cmp             x1, x0
    // 0xea8184: b.hs            #0xea83c0
    // 0xea8188: ArrayLoad: r7 = r3[r6]  ; List_1
    //     0xea8188: add             x16, x3, x6
    //     0xea818c: ldrb            w7, [x16, #0x17]
    // 0xea8190: ArrayLoad: r8 = r4[0]  ; List_4
    //     0xea8190: ldur            w8, [x4, #0x17]
    // 0xea8194: DecompressPointer r8
    //     0xea8194: add             x8, x8, HEAP, lsl #32
    // 0xea8198: r16 = Sentinel
    //     0xea8198: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea819c: cmp             w8, w16
    // 0xea81a0: b.eq            #0xea83c4
    // 0xea81a4: LoadField: r0 = r8->field_13
    //     0xea81a4: ldur            w0, [x8, #0x13]
    // 0xea81a8: r1 = LoadInt32Instr(r0)
    //     0xea81a8: sbfx            x1, x0, #1, #0x1f
    // 0xea81ac: mov             x0, x1
    // 0xea81b0: mov             x1, x2
    // 0xea81b4: cmp             x1, x0
    // 0xea81b8: b.hs            #0xea83d0
    // 0xea81bc: ArrayLoad: r0 = r8[r2]  ; List_1
    //     0xea81bc: add             x16, x8, x2
    //     0xea81c0: ldrb            w0, [x16, #0x17]
    // 0xea81c4: eor             x1, x7, x0
    // 0xea81c8: ArrayStore: r3[r6] = r1  ; TypeUnknown_1
    //     0xea81c8: add             x0, x3, x6
    //     0xea81cc: strb            w1, [x0, #0x17]
    // 0xea81d0: add             x9, x2, #1
    // 0xea81d4: mov             x6, x4
    // 0xea81d8: mov             x2, x3
    // 0xea81dc: mov             x8, x5
    // 0xea81e0: ldur            x3, [fp, #-0x58]
    // 0xea81e4: ldur            x4, [fp, #-0x50]
    // 0xea81e8: ldur            x5, [fp, #-0x48]
    // 0xea81ec: b               #0xea8130
    // 0xea81f0: ldur            x4, [fp, #-0x10]
    // 0xea81f4: ldur            x3, [fp, #-0x28]
    // 0xea81f8: ldur            x5, [fp, #-0x30]
    // 0xea81fc: ldur            x2, [fp, #-8]
    // 0xea8200: ArrayLoad: r6 = r4[0]  ; List_4
    //     0xea8200: ldur            w6, [x4, #0x17]
    // 0xea8204: DecompressPointer r6
    //     0xea8204: add             x6, x6, HEAP, lsl #32
    // 0xea8208: r16 = Sentinel
    //     0xea8208: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea820c: cmp             w6, w16
    // 0xea8210: b.eq            #0xea83d4
    // 0xea8214: stur            x6, [fp, #-0x60]
    // 0xea8218: r0 = LoadClassIdInstr(r2)
    //     0xea8218: ldur            x0, [x2, #-1]
    //     0xea821c: ubfx            x0, x0, #0xc, #0x14
    // 0xea8220: mov             x1, x2
    // 0xea8224: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea8224: sub             lr, x0, #1, lsl #12
    //     0xea8228: ldr             lr, [x21, lr, lsl #3]
    //     0xea822c: blr             lr
    // 0xea8230: mov             x3, x0
    // 0xea8234: ldur            x2, [fp, #-0x60]
    // 0xea8238: LoadField: r0 = r2->field_13
    //     0xea8238: ldur            w0, [x2, #0x13]
    // 0xea823c: r4 = LoadInt32Instr(r0)
    //     0xea823c: sbfx            x4, x0, #1, #0x1f
    // 0xea8240: ldur            x6, [fp, #-0x18]
    // 0xea8244: ldur            x5, [fp, #-0x20]
    // 0xea8248: r7 = 0
    //     0xea8248: movz            x7, #0
    // 0xea824c: CheckStackOverflow
    //     0xea824c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8250: cmp             SP, x16
    //     0xea8254: b.ls            #0xea83e0
    // 0xea8258: cmp             x7, x3
    // 0xea825c: b.ge            #0xea82a0
    // 0xea8260: add             x8, x5, x7
    // 0xea8264: ldur            x0, [fp, #-0x40]
    // 0xea8268: mov             x1, x8
    // 0xea826c: cmp             x1, x0
    // 0xea8270: b.hs            #0xea83e8
    // 0xea8274: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea8274: add             x16, x6, x8
    //     0xea8278: ldrb            w9, [x16, #0x17]
    // 0xea827c: mov             x0, x4
    // 0xea8280: mov             x1, x7
    // 0xea8284: cmp             x1, x0
    // 0xea8288: b.hs            #0xea83ec
    // 0xea828c: ArrayStore: r2[r7] = r9  ; TypeUnknown_1
    //     0xea828c: add             x0, x2, x7
    //     0xea8290: strb            w9, [x0, #0x17]
    // 0xea8294: add             x0, x7, #1
    // 0xea8298: mov             x7, x0
    // 0xea829c: b               #0xea824c
    // 0xea82a0: ldur            x0, [fp, #-0x10]
    // 0xea82a4: ldur            x2, [fp, #-0x50]
    // 0xea82a8: ldur            x1, [fp, #-8]
    // 0xea82ac: LoadField: r3 = r0->field_13
    //     0xea82ac: ldur            w3, [x0, #0x13]
    // 0xea82b0: DecompressPointer r3
    //     0xea82b0: add             x3, x3, HEAP, lsl #32
    // 0xea82b4: r16 = Sentinel
    //     0xea82b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea82b8: cmp             w3, w16
    // 0xea82bc: b.eq            #0xea83f0
    // 0xea82c0: stur            x3, [fp, #-0x18]
    // 0xea82c4: r0 = LoadClassIdInstr(r1)
    //     0xea82c4: ldur            x0, [x1, #-1]
    //     0xea82c8: ubfx            x0, x0, #0xc, #0x14
    // 0xea82cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xea82cc: sub             lr, x0, #1, lsl #12
    //     0xea82d0: ldr             lr, [x21, lr, lsl #3]
    //     0xea82d4: blr             lr
    // 0xea82d8: mov             x2, x0
    // 0xea82dc: ldur            x0, [fp, #-0x50]
    // 0xea82e0: r3 = LoadInt32Instr(r0)
    //     0xea82e0: sbfx            x3, x0, #1, #0x1f
    // 0xea82e4: ldur            x4, [fp, #-0x18]
    // 0xea82e8: LoadField: r0 = r4->field_13
    //     0xea82e8: ldur            w0, [x4, #0x13]
    // 0xea82ec: r5 = LoadInt32Instr(r0)
    //     0xea82ec: sbfx            x5, x0, #1, #0x1f
    // 0xea82f0: ldur            x6, [fp, #-0x28]
    // 0xea82f4: ldur            x7, [fp, #-0x30]
    // 0xea82f8: r8 = 0
    //     0xea82f8: movz            x8, #0
    // 0xea82fc: CheckStackOverflow
    //     0xea82fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8300: cmp             SP, x16
    //     0xea8304: b.ls            #0xea83fc
    // 0xea8308: cmp             x8, x2
    // 0xea830c: b.ge            #0xea8350
    // 0xea8310: add             x9, x7, x8
    // 0xea8314: mov             x0, x3
    // 0xea8318: mov             x1, x9
    // 0xea831c: cmp             x1, x0
    // 0xea8320: b.hs            #0xea8404
    // 0xea8324: ArrayLoad: r10 = r6[r9]  ; List_1
    //     0xea8324: add             x16, x6, x9
    //     0xea8328: ldrb            w10, [x16, #0x17]
    // 0xea832c: mov             x0, x5
    // 0xea8330: mov             x1, x8
    // 0xea8334: cmp             x1, x0
    // 0xea8338: b.hs            #0xea8408
    // 0xea833c: ArrayStore: r4[r8] = r10  ; TypeUnknown_1
    //     0xea833c: add             x0, x4, x8
    //     0xea8340: strb            w10, [x0, #0x17]
    // 0xea8344: add             x0, x8, #1
    // 0xea8348: mov             x8, x0
    // 0xea834c: b               #0xea82fc
    // 0xea8350: ldur            x0, [fp, #-0x58]
    // 0xea8354: LeaveFrame
    //     0xea8354: mov             SP, fp
    //     0xea8358: ldp             fp, lr, [SP], #0x10
    // 0xea835c: ret
    //     0xea835c: ret             
    // 0xea8360: r0 = ArgumentError()
    //     0xea8360: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea8364: mov             x1, x0
    // 0xea8368: r0 = "Input buffer too short"
    //     0xea8368: add             x0, PP, #0x23, lsl #12  ; [pp+0x23678] "Input buffer too short"
    //     0xea836c: ldr             x0, [x0, #0x678]
    // 0xea8370: ArrayStore: r1[0] = r0  ; List_4
    //     0xea8370: stur            w0, [x1, #0x17]
    // 0xea8374: r0 = false
    //     0xea8374: add             x0, NULL, #0x30  ; false
    // 0xea8378: StoreField: r1->field_b = r0
    //     0xea8378: stur            w0, [x1, #0xb]
    // 0xea837c: mov             x0, x1
    // 0xea8380: r0 = Throw()
    //     0xea8380: bl              #0xec04b8  ; ThrowStub
    // 0xea8384: brk             #0
    // 0xea8388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea8388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea838c: b               #0xea7fb8
    // 0xea8390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea8390: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea8394: b               #0xea8018
    // 0xea8398: r9 = _xPrev
    //     0xea8398: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d18] Field <IGEBlockCipher._xPrev@917186754>: late (offset: 0x14)
    //     0xea839c: ldr             x9, [x9, #0xd18]
    // 0xea83a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea83a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea83a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea83a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea83a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea83a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea83ac: r9 = _xPrev
    //     0xea83ac: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d18] Field <IGEBlockCipher._xPrev@917186754>: late (offset: 0x14)
    //     0xea83b0: ldr             x9, [x9, #0xd18]
    // 0xea83b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea83b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea83b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea83b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea83bc: b               #0xea8144
    // 0xea83c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea83c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea83c4: r9 = _yPrev
    //     0xea83c4: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d20] Field <IGEBlockCipher._yPrev@917186754>: late (offset: 0x18)
    //     0xea83c8: ldr             x9, [x9, #0xd20]
    // 0xea83cc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea83cc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea83d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea83d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea83d4: r9 = _yPrev
    //     0xea83d4: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d20] Field <IGEBlockCipher._yPrev@917186754>: late (offset: 0x18)
    //     0xea83d8: ldr             x9, [x9, #0xd20]
    // 0xea83dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea83dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea83e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea83e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea83e4: b               #0xea8258
    // 0xea83e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea83e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea83ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea83ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea83f0: r9 = _xPrev
    //     0xea83f0: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d18] Field <IGEBlockCipher._xPrev@917186754>: late (offset: 0x14)
    //     0xea83f4: ldr             x9, [x9, #0xd18]
    // 0xea83f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea83f8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea83fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea83fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea8400: b               #0xea8308
    // 0xea8404: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea8404: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea8408: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea8408: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
