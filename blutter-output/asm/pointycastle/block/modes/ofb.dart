// lib: impl.block_cipher.modes.ofb, url: package:pointycastle/block/modes/ofb.dart

// class id: 1050938, size: 0x8
class :: {
}

// class id: 703, size: 0x20, field offset: 0x8
class OFBBlockCipher extends BaseBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xdb0
  late Uint8List _iv; // offset: 0x14

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e5828, size: 0x98
    // 0x8e5828: EnterFrame
    //     0x8e5828: stp             fp, lr, [SP, #-0x10]!
    //     0x8e582c: mov             fp, SP
    // 0x8e5830: AllocStack(0x40)
    //     0x8e5830: sub             SP, SP, #0x40
    // 0x8e5834: CheckStackOverflow
    //     0x8e5834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5838: cmp             SP, x16
    //     0x8e583c: b.ls            #0x8e58b8
    // 0x8e5840: r16 = "^(.+)/OFB-([0-9]+)$"
    //     0x8e5840: add             x16, PP, #0x19, lsl #12  ; [pp+0x19ae8] "^(.+)/OFB-([0-9]+)$"
    //     0x8e5844: ldr             x16, [x16, #0xae8]
    // 0x8e5848: stp             x16, NULL, [SP, #0x20]
    // 0x8e584c: r16 = false
    //     0x8e584c: add             x16, NULL, #0x30  ; false
    // 0x8e5850: r30 = true
    //     0x8e5850: add             lr, NULL, #0x20  ; true
    // 0x8e5854: stp             lr, x16, [SP, #0x10]
    // 0x8e5858: r16 = false
    //     0x8e5858: add             x16, NULL, #0x30  ; false
    // 0x8e585c: r30 = false
    //     0x8e585c: add             lr, NULL, #0x30  ; false
    // 0x8e5860: stp             lr, x16, [SP]
    // 0x8e5864: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8e5864: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8e5868: r0 = _RegExp()
    //     0x8e5868: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8e586c: stur            x0, [fp, #-8]
    // 0x8e5870: r0 = DynamicFactoryConfig()
    //     0x8e5870: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e5874: mov             x3, x0
    // 0x8e5878: ldur            x0, [fp, #-8]
    // 0x8e587c: stur            x3, [fp, #-0x10]
    // 0x8e5880: StoreField: r3->field_b = r0
    //     0x8e5880: stur            w0, [x3, #0xb]
    // 0x8e5884: r1 = Function '<anonymous closure>': static.
    //     0x8e5884: add             x1, PP, #0x19, lsl #12  ; [pp+0x19af0] AnonymousClosure: static (0x8e58c0), in [package:pointycastle/block/modes/ofb.dart] OFBBlockCipher::factoryConfig (0x8e5828)
    //     0x8e5888: ldr             x1, [x1, #0xaf0]
    // 0x8e588c: r2 = Null
    //     0x8e588c: mov             x2, NULL
    // 0x8e5890: r0 = AllocateClosure()
    //     0x8e5890: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e5894: mov             x1, x0
    // 0x8e5898: ldur            x0, [fp, #-0x10]
    // 0x8e589c: StoreField: r0->field_f = r1
    //     0x8e589c: stur            w1, [x0, #0xf]
    // 0x8e58a0: r1 = BlockCipher
    //     0x8e58a0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e58a4: ldr             x1, [x1, #0xa80]
    // 0x8e58a8: StoreField: r0->field_7 = r1
    //     0x8e58a8: stur            w1, [x0, #7]
    // 0x8e58ac: LeaveFrame
    //     0x8e58ac: mov             SP, fp
    //     0x8e58b0: ldp             fp, lr, [SP], #0x10
    // 0x8e58b4: ret
    //     0x8e58b4: ret             
    // 0x8e58b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e58b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e58bc: b               #0x8e5840
  }
  [closure] static (dynamic) => OFBBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e58c0, size: 0x54
    // 0x8e58c0: EnterFrame
    //     0x8e58c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e58c4: mov             fp, SP
    // 0x8e58c8: AllocStack(0x8)
    //     0x8e58c8: sub             SP, SP, #8
    // 0x8e58cc: SetupParameters()
    //     0x8e58cc: ldr             x0, [fp, #0x20]
    //     0x8e58d0: ldur            w1, [x0, #0x17]
    //     0x8e58d4: add             x1, x1, HEAP, lsl #32
    //     0x8e58d8: stur            x1, [fp, #-8]
    // 0x8e58dc: r1 = 1
    //     0x8e58dc: movz            x1, #0x1
    // 0x8e58e0: r0 = AllocateContext()
    //     0x8e58e0: bl              #0xec126c  ; AllocateContextStub
    // 0x8e58e4: mov             x1, x0
    // 0x8e58e8: ldur            x0, [fp, #-8]
    // 0x8e58ec: StoreField: r1->field_b = r0
    //     0x8e58ec: stur            w0, [x1, #0xb]
    // 0x8e58f0: ldr             x0, [fp, #0x10]
    // 0x8e58f4: StoreField: r1->field_f = r0
    //     0x8e58f4: stur            w0, [x1, #0xf]
    // 0x8e58f8: mov             x2, x1
    // 0x8e58fc: r1 = Function '<anonymous closure>': static.
    //     0x8e58fc: add             x1, PP, #0x19, lsl #12  ; [pp+0x19af8] AnonymousClosure: static (0x8e5914), in [package:pointycastle/block/modes/ofb.dart] OFBBlockCipher::factoryConfig (0x8e5828)
    //     0x8e5900: ldr             x1, [x1, #0xaf8]
    // 0x8e5904: r0 = AllocateClosure()
    //     0x8e5904: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e5908: LeaveFrame
    //     0x8e5908: mov             SP, fp
    //     0x8e590c: ldp             fp, lr, [SP], #0x10
    // 0x8e5910: ret
    //     0x8e5910: ret             
  }
  [closure] static OFBBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e5914, size: 0x1c0
    // 0x8e5914: EnterFrame
    //     0x8e5914: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5918: mov             fp, SP
    // 0x8e591c: AllocStack(0x38)
    //     0x8e591c: sub             SP, SP, #0x38
    // 0x8e5920: SetupParameters()
    //     0x8e5920: ldr             x0, [fp, #0x10]
    //     0x8e5924: ldur            w1, [x0, #0x17]
    //     0x8e5928: add             x1, x1, HEAP, lsl #32
    // 0x8e592c: CheckStackOverflow
    //     0x8e592c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5930: cmp             SP, x16
    //     0x8e5934: b.ls            #0x8e5ac4
    // 0x8e5938: LoadField: r3 = r1->field_f
    //     0x8e5938: ldur            w3, [x1, #0xf]
    // 0x8e593c: DecompressPointer r3
    //     0x8e593c: add             x3, x3, HEAP, lsl #32
    // 0x8e5940: stur            x3, [fp, #-8]
    // 0x8e5944: r0 = LoadClassIdInstr(r3)
    //     0x8e5944: ldur            x0, [x3, #-1]
    //     0x8e5948: ubfx            x0, x0, #0xc, #0x14
    // 0x8e594c: mov             x1, x3
    // 0x8e5950: r2 = 1
    //     0x8e5950: movz            x2, #0x1
    // 0x8e5954: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e5954: sub             lr, x0, #0xfdd
    //     0x8e5958: ldr             lr, [x21, lr, lsl #3]
    //     0x8e595c: blr             lr
    // 0x8e5960: stur            x0, [fp, #-0x10]
    // 0x8e5964: cmp             w0, NULL
    // 0x8e5968: b.eq            #0x8e5acc
    // 0x8e596c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e596c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e5970: ldr             x0, [x0, #0x2e38]
    //     0x8e5974: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e5978: cmp             w0, w16
    //     0x8e597c: b.ne            #0x8e598c
    //     0x8e5980: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e5984: ldr             x2, [x2, #0xf80]
    //     0x8e5988: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e598c: r16 = <BlockCipher>
    //     0x8e598c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e5990: ldr             x16, [x16, #0x88]
    // 0x8e5994: stp             x0, x16, [SP, #8]
    // 0x8e5998: ldur            x16, [fp, #-0x10]
    // 0x8e599c: str             x16, [SP]
    // 0x8e59a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e59a0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e59a4: r0 = create()
    //     0x8e59a4: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e59a8: mov             x3, x0
    // 0x8e59ac: ldur            x1, [fp, #-8]
    // 0x8e59b0: stur            x3, [fp, #-0x10]
    // 0x8e59b4: r0 = LoadClassIdInstr(r1)
    //     0x8e59b4: ldur            x0, [x1, #-1]
    //     0x8e59b8: ubfx            x0, x0, #0xc, #0x14
    // 0x8e59bc: r2 = 2
    //     0x8e59bc: movz            x2, #0x2
    // 0x8e59c0: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e59c0: sub             lr, x0, #0xfdd
    //     0x8e59c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8e59c8: blr             lr
    // 0x8e59cc: cmp             w0, NULL
    // 0x8e59d0: b.eq            #0x8e5ad0
    // 0x8e59d4: mov             x1, x0
    // 0x8e59d8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8e59d8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8e59dc: r0 = parse()
    //     0x8e59dc: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8e59e0: stur            x0, [fp, #-0x20]
    // 0x8e59e4: tst             x0, #7
    // 0x8e59e8: b.ne            #0x8e5a20
    // 0x8e59ec: r1 = 8
    //     0x8e59ec: movz            x1, #0x8
    // 0x8e59f0: sdiv            x3, x0, x1
    // 0x8e59f4: stur            x3, [fp, #-0x18]
    // 0x8e59f8: r0 = OFBBlockCipher()
    //     0x8e59f8: bl              #0x8e5c48  ; AllocateOFBBlockCipherStub -> OFBBlockCipher (size=0x20)
    // 0x8e59fc: mov             x1, x0
    // 0x8e5a00: ldur            x2, [fp, #-0x10]
    // 0x8e5a04: ldur            x3, [fp, #-0x18]
    // 0x8e5a08: stur            x0, [fp, #-8]
    // 0x8e5a0c: r0 = OFBBlockCipher()
    //     0x8e5a0c: bl              #0x8e5ad4  ; [package:pointycastle/block/modes/ofb.dart] OFBBlockCipher::OFBBlockCipher
    // 0x8e5a10: ldur            x0, [fp, #-8]
    // 0x8e5a14: LeaveFrame
    //     0x8e5a14: mov             SP, fp
    //     0x8e5a18: ldp             fp, lr, [SP], #0x10
    // 0x8e5a1c: ret
    //     0x8e5a1c: ret             
    // 0x8e5a20: r1 = Null
    //     0x8e5a20: mov             x1, NULL
    // 0x8e5a24: r2 = 6
    //     0x8e5a24: movz            x2, #0x6
    // 0x8e5a28: r0 = AllocateArray()
    //     0x8e5a28: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e5a2c: mov             x2, x0
    // 0x8e5a30: r16 = "Bad OFB block size: "
    //     0x8e5a30: add             x16, PP, #0x19, lsl #12  ; [pp+0x19b00] "Bad OFB block size: "
    //     0x8e5a34: ldr             x16, [x16, #0xb00]
    // 0x8e5a38: StoreField: r2->field_f = r16
    //     0x8e5a38: stur            w16, [x2, #0xf]
    // 0x8e5a3c: ldur            x3, [fp, #-0x20]
    // 0x8e5a40: r0 = BoxInt64Instr(r3)
    //     0x8e5a40: sbfiz           x0, x3, #1, #0x1f
    //     0x8e5a44: cmp             x3, x0, asr #1
    //     0x8e5a48: b.eq            #0x8e5a54
    //     0x8e5a4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e5a50: stur            x3, [x0, #7]
    // 0x8e5a54: StoreField: r2->field_13 = r0
    //     0x8e5a54: stur            w0, [x2, #0x13]
    // 0x8e5a58: r16 = " (must be a multiple of 8)"
    //     0x8e5a58: add             x16, PP, #0x19, lsl #12  ; [pp+0x19b08] " (must be a multiple of 8)"
    //     0x8e5a5c: ldr             x16, [x16, #0xb08]
    // 0x8e5a60: ArrayStore: r2[0] = r16  ; List_4
    //     0x8e5a60: stur            w16, [x2, #0x17]
    // 0x8e5a64: str             x2, [SP]
    // 0x8e5a68: r0 = _interpolate()
    //     0x8e5a68: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8e5a6c: r1 = Null
    //     0x8e5a6c: mov             x1, NULL
    // 0x8e5a70: r2 = 6
    //     0x8e5a70: movz            x2, #0x6
    // 0x8e5a74: stur            x0, [fp, #-8]
    // 0x8e5a78: r0 = AllocateArray()
    //     0x8e5a78: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e5a7c: r16 = "Algorithm name "
    //     0x8e5a7c: add             x16, PP, #0x19, lsl #12  ; [pp+0x19b10] "Algorithm name "
    //     0x8e5a80: ldr             x16, [x16, #0xb10]
    // 0x8e5a84: StoreField: r0->field_f = r16
    //     0x8e5a84: stur            w16, [x0, #0xf]
    // 0x8e5a88: ldur            x1, [fp, #-8]
    // 0x8e5a8c: StoreField: r0->field_13 = r1
    //     0x8e5a8c: stur            w1, [x0, #0x13]
    // 0x8e5a90: r16 = " is invalid"
    //     0x8e5a90: add             x16, PP, #0x19, lsl #12  ; [pp+0x19b18] " is invalid"
    //     0x8e5a94: ldr             x16, [x16, #0xb18]
    // 0x8e5a98: ArrayStore: r0[0] = r16  ; List_4
    //     0x8e5a98: stur            w16, [x0, #0x17]
    // 0x8e5a9c: str             x0, [SP]
    // 0x8e5aa0: r0 = _interpolate()
    //     0x8e5aa0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8e5aa4: stur            x0, [fp, #-8]
    // 0x8e5aa8: r0 = RegistryFactoryException()
    //     0x8e5aa8: bl              #0x8c37d8  ; AllocateRegistryFactoryExceptionStub -> RegistryFactoryException (size=0xc)
    // 0x8e5aac: mov             x1, x0
    // 0x8e5ab0: ldur            x0, [fp, #-8]
    // 0x8e5ab4: StoreField: r1->field_7 = r0
    //     0x8e5ab4: stur            w0, [x1, #7]
    // 0x8e5ab8: mov             x0, x1
    // 0x8e5abc: r0 = Throw()
    //     0x8e5abc: bl              #0xec04b8  ; ThrowStub
    // 0x8e5ac0: brk             #0
    // 0x8e5ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e5ac4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5ac8: b               #0x8e5938
    // 0x8e5acc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e5acc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8e5ad0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e5ad0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ OFBBlockCipher(/* No info */) {
    // ** addr: 0x8e5ad4, size: 0x174
    // 0x8e5ad4: EnterFrame
    //     0x8e5ad4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5ad8: mov             fp, SP
    // 0x8e5adc: AllocStack(0x10)
    //     0x8e5adc: sub             SP, SP, #0x10
    // 0x8e5ae0: r0 = Sentinel
    //     0x8e5ae0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e5ae4: mov             x4, x1
    // 0x8e5ae8: stur            x1, [fp, #-8]
    // 0x8e5aec: stur            x2, [fp, #-0x10]
    // 0x8e5af0: CheckStackOverflow
    //     0x8e5af0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5af4: cmp             SP, x16
    //     0x8e5af8: b.ls            #0x8e5c40
    // 0x8e5afc: StoreField: r4->field_13 = r0
    //     0x8e5afc: stur            w0, [x4, #0x13]
    // 0x8e5b00: mov             x0, x2
    // 0x8e5b04: StoreField: r4->field_f = r0
    //     0x8e5b04: stur            w0, [x4, #0xf]
    //     0x8e5b08: ldurb           w16, [x4, #-1]
    //     0x8e5b0c: ldurb           w17, [x0, #-1]
    //     0x8e5b10: and             x16, x17, x16, lsr #2
    //     0x8e5b14: tst             x16, HEAP, lsr #32
    //     0x8e5b18: b.eq            #0x8e5b20
    //     0x8e5b1c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8e5b20: StoreField: r4->field_7 = r3
    //     0x8e5b20: stur            x3, [x4, #7]
    // 0x8e5b24: r0 = LoadClassIdInstr(r2)
    //     0x8e5b24: ldur            x0, [x2, #-1]
    //     0x8e5b28: ubfx            x0, x0, #0xc, #0x14
    // 0x8e5b2c: mov             x1, x2
    // 0x8e5b30: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5b30: sub             lr, x0, #1, lsl #12
    //     0x8e5b34: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5b38: blr             lr
    // 0x8e5b3c: mov             x2, x0
    // 0x8e5b40: r0 = BoxInt64Instr(r2)
    //     0x8e5b40: sbfiz           x0, x2, #1, #0x1f
    //     0x8e5b44: cmp             x2, x0, asr #1
    //     0x8e5b48: b.eq            #0x8e5b54
    //     0x8e5b4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e5b50: stur            x2, [x0, #7]
    // 0x8e5b54: mov             x4, x0
    // 0x8e5b58: r0 = AllocateUint8Array()
    //     0x8e5b58: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e5b5c: ldur            x2, [fp, #-8]
    // 0x8e5b60: StoreField: r2->field_13 = r0
    //     0x8e5b60: stur            w0, [x2, #0x13]
    //     0x8e5b64: ldurb           w16, [x2, #-1]
    //     0x8e5b68: ldurb           w17, [x0, #-1]
    //     0x8e5b6c: and             x16, x17, x16, lsr #2
    //     0x8e5b70: tst             x16, HEAP, lsr #32
    //     0x8e5b74: b.eq            #0x8e5b7c
    //     0x8e5b78: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e5b7c: ldur            x3, [fp, #-0x10]
    // 0x8e5b80: r0 = LoadClassIdInstr(r3)
    //     0x8e5b80: ldur            x0, [x3, #-1]
    //     0x8e5b84: ubfx            x0, x0, #0xc, #0x14
    // 0x8e5b88: mov             x1, x3
    // 0x8e5b8c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5b8c: sub             lr, x0, #1, lsl #12
    //     0x8e5b90: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5b94: blr             lr
    // 0x8e5b98: mov             x2, x0
    // 0x8e5b9c: r0 = BoxInt64Instr(r2)
    //     0x8e5b9c: sbfiz           x0, x2, #1, #0x1f
    //     0x8e5ba0: cmp             x2, x0, asr #1
    //     0x8e5ba4: b.eq            #0x8e5bb0
    //     0x8e5ba8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e5bac: stur            x2, [x0, #7]
    // 0x8e5bb0: mov             x4, x0
    // 0x8e5bb4: r0 = AllocateUint8Array()
    //     0x8e5bb4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e5bb8: ldur            x2, [fp, #-8]
    // 0x8e5bbc: ArrayStore: r2[0] = r0  ; List_4
    //     0x8e5bbc: stur            w0, [x2, #0x17]
    //     0x8e5bc0: ldurb           w16, [x2, #-1]
    //     0x8e5bc4: ldurb           w17, [x0, #-1]
    //     0x8e5bc8: and             x16, x17, x16, lsr #2
    //     0x8e5bcc: tst             x16, HEAP, lsr #32
    //     0x8e5bd0: b.eq            #0x8e5bd8
    //     0x8e5bd4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e5bd8: ldur            x1, [fp, #-0x10]
    // 0x8e5bdc: r0 = LoadClassIdInstr(r1)
    //     0x8e5bdc: ldur            x0, [x1, #-1]
    //     0x8e5be0: ubfx            x0, x0, #0xc, #0x14
    // 0x8e5be4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e5be4: sub             lr, x0, #1, lsl #12
    //     0x8e5be8: ldr             lr, [x21, lr, lsl #3]
    //     0x8e5bec: blr             lr
    // 0x8e5bf0: mov             x2, x0
    // 0x8e5bf4: r0 = BoxInt64Instr(r2)
    //     0x8e5bf4: sbfiz           x0, x2, #1, #0x1f
    //     0x8e5bf8: cmp             x2, x0, asr #1
    //     0x8e5bfc: b.eq            #0x8e5c08
    //     0x8e5c00: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e5c04: stur            x2, [x0, #7]
    // 0x8e5c08: mov             x4, x0
    // 0x8e5c0c: r0 = AllocateUint8Array()
    //     0x8e5c0c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e5c10: ldur            x1, [fp, #-8]
    // 0x8e5c14: StoreField: r1->field_1b = r0
    //     0x8e5c14: stur            w0, [x1, #0x1b]
    //     0x8e5c18: ldurb           w16, [x1, #-1]
    //     0x8e5c1c: ldurb           w17, [x0, #-1]
    //     0x8e5c20: and             x16, x17, x16, lsr #2
    //     0x8e5c24: tst             x16, HEAP, lsr #32
    //     0x8e5c28: b.eq            #0x8e5c30
    //     0x8e5c2c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e5c30: r0 = Null
    //     0x8e5c30: mov             x0, NULL
    // 0x8e5c34: LeaveFrame
    //     0x8e5c34: mov             SP, fp
    //     0x8e5c38: ldp             fp, lr, [SP], #0x10
    // 0x8e5c3c: ret
    //     0x8e5c3c: ret             
    // 0x8e5c40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e5c40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5c44: b               #0x8e5afc
  }
  _ reset(/* No info */) {
    // ** addr: 0xe7e6c4, size: 0x200
    // 0xe7e6c4: EnterFrame
    //     0xe7e6c4: stp             fp, lr, [SP, #-0x10]!
    //     0xe7e6c8: mov             fp, SP
    // 0xe7e6cc: AllocStack(0x28)
    //     0xe7e6cc: sub             SP, SP, #0x28
    // 0xe7e6d0: SetupParameters(OFBBlockCipher this /* r1 => r0, fp-0x28 */)
    //     0xe7e6d0: mov             x0, x1
    //     0xe7e6d4: stur            x1, [fp, #-0x28]
    // 0xe7e6d8: CheckStackOverflow
    //     0xe7e6d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7e6dc: cmp             SP, x16
    //     0xe7e6e0: b.ls            #0xe7e8ac
    // 0xe7e6e4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xe7e6e4: ldur            w4, [x0, #0x17]
    // 0xe7e6e8: DecompressPointer r4
    //     0xe7e6e8: add             x4, x4, HEAP, lsl #32
    // 0xe7e6ec: stur            x4, [fp, #-0x20]
    // 0xe7e6f0: cmp             w4, NULL
    // 0xe7e6f4: b.eq            #0xe7e8b4
    // 0xe7e6f8: LoadField: r5 = r0->field_13
    //     0xe7e6f8: ldur            w5, [x0, #0x13]
    // 0xe7e6fc: DecompressPointer r5
    //     0xe7e6fc: add             x5, x5, HEAP, lsl #32
    // 0xe7e700: r16 = Sentinel
    //     0xe7e700: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7e704: cmp             w5, w16
    // 0xe7e708: b.eq            #0xe7e8b8
    // 0xe7e70c: stur            x5, [fp, #-0x18]
    // 0xe7e710: LoadField: r6 = r5->field_13
    //     0xe7e710: ldur            w6, [x5, #0x13]
    // 0xe7e714: stur            x6, [fp, #-0x10]
    // 0xe7e718: r7 = LoadInt32Instr(r6)
    //     0xe7e718: sbfx            x7, x6, #1, #0x1f
    // 0xe7e71c: stur            x7, [fp, #-8]
    // 0xe7e720: tbnz            x7, #0x3f, #0xe7e734
    // 0xe7e724: LoadField: r1 = r4->field_13
    //     0xe7e724: ldur            w1, [x4, #0x13]
    // 0xe7e728: r2 = LoadInt32Instr(r1)
    //     0xe7e728: sbfx            x2, x1, #1, #0x1f
    // 0xe7e72c: cmp             x7, x2
    // 0xe7e730: b.le            #0xe7e74c
    // 0xe7e734: LoadField: r1 = r4->field_13
    //     0xe7e734: ldur            w1, [x4, #0x13]
    // 0xe7e738: r3 = LoadInt32Instr(r1)
    //     0xe7e738: sbfx            x3, x1, #1, #0x1f
    // 0xe7e73c: mov             x2, x6
    // 0xe7e740: r1 = 0
    //     0xe7e740: movz            x1, #0
    // 0xe7e744: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe7e744: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe7e748: r0 = checkValidRange()
    //     0xe7e748: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe7e74c: ldur            x2, [fp, #-8]
    // 0xe7e750: cbz             x2, #0xe7e87c
    // 0xe7e754: ldur            x0, [fp, #-0x10]
    // 0xe7e758: cmp             w0, #0x800
    // 0xe7e75c: b.ge            #0xe7e830
    // 0xe7e760: ldur            x1, [fp, #-0x20]
    // 0xe7e764: ldur            x3, [fp, #-0x18]
    // 0xe7e768: mov             x4, x0
    // 0xe7e76c: add             x2, x3, #0x17
    // 0xe7e770: add             x0, x1, #0x17
    // 0xe7e774: cbz             x4, #0xe7e82c
    // 0xe7e778: cmp             x0, x2
    // 0xe7e77c: b.ls            #0xe7e7e4
    // 0xe7e780: sxtw            x4, w4
    // 0xe7e784: add             x16, x2, x4, asr #1
    // 0xe7e788: cmp             x0, x16
    // 0xe7e78c: b.hs            #0xe7e7e4
    // 0xe7e790: mov             x2, x16
    // 0xe7e794: add             x0, x0, x4, asr #1
    // 0xe7e798: tbz             w4, #4, #0xe7e7a4
    // 0xe7e79c: ldr             x16, [x2, #-8]!
    // 0xe7e7a0: str             x16, [x0, #-8]!
    // 0xe7e7a4: tbz             w4, #3, #0xe7e7b0
    // 0xe7e7a8: ldr             w16, [x2, #-4]!
    // 0xe7e7ac: str             w16, [x0, #-4]!
    // 0xe7e7b0: tbz             w4, #2, #0xe7e7bc
    // 0xe7e7b4: ldrh            w16, [x2, #-2]!
    // 0xe7e7b8: strh            w16, [x0, #-2]!
    // 0xe7e7bc: tbz             w4, #1, #0xe7e7c8
    // 0xe7e7c0: ldrb            w16, [x2, #-1]!
    // 0xe7e7c4: strb            w16, [x0, #-1]!
    // 0xe7e7c8: ands            w4, w4, #0xffffffe1
    // 0xe7e7cc: b.eq            #0xe7e82c
    // 0xe7e7d0: ldp             x16, x17, [x2, #-0x10]!
    // 0xe7e7d4: stp             x16, x17, [x0, #-0x10]!
    // 0xe7e7d8: subs            w4, w4, #0x20
    // 0xe7e7dc: b.ne            #0xe7e7d0
    // 0xe7e7e0: b               #0xe7e82c
    // 0xe7e7e4: tbz             w4, #4, #0xe7e7f0
    // 0xe7e7e8: ldr             x16, [x2], #8
    // 0xe7e7ec: str             x16, [x0], #8
    // 0xe7e7f0: tbz             w4, #3, #0xe7e7fc
    // 0xe7e7f4: ldr             w16, [x2], #4
    // 0xe7e7f8: str             w16, [x0], #4
    // 0xe7e7fc: tbz             w4, #2, #0xe7e808
    // 0xe7e800: ldrh            w16, [x2], #2
    // 0xe7e804: strh            w16, [x0], #2
    // 0xe7e808: tbz             w4, #1, #0xe7e814
    // 0xe7e80c: ldrb            w16, [x2], #1
    // 0xe7e810: strb            w16, [x0], #1
    // 0xe7e814: ands            w4, w4, #0xffffffe1
    // 0xe7e818: b.eq            #0xe7e82c
    // 0xe7e81c: ldp             x16, x17, [x2], #0x10
    // 0xe7e820: stp             x16, x17, [x0], #0x10
    // 0xe7e824: subs            w4, w4, #0x20
    // 0xe7e828: b.ne            #0xe7e81c
    // 0xe7e82c: b               #0xe7e87c
    // 0xe7e830: ldur            x1, [fp, #-0x20]
    // 0xe7e834: ldur            x3, [fp, #-0x18]
    // 0xe7e838: LoadField: r0 = r1->field_7
    //     0xe7e838: ldur            x0, [x1, #7]
    // 0xe7e83c: LoadField: r1 = r3->field_7
    //     0xe7e83c: ldur            x1, [x3, #7]
    // 0xe7e840: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe7e840: mov             x3, THR
    //     0xe7e844: ldr             x9, [x3, #0x658]
    //     0xe7e848: mov             x17, fp
    //     0xe7e84c: str             fp, [SP, #-8]!
    //     0xe7e850: mov             fp, SP
    //     0xe7e854: and             SP, SP, #0xfffffffffffffff0
    //     0xe7e858: mov             x19, sp
    //     0xe7e85c: mov             sp, SP
    //     0xe7e860: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7e864: blr             x9
    //     0xe7e868: movz            x16, #0x8
    //     0xe7e86c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7e870: mov             sp, x19
    //     0xe7e874: mov             SP, fp
    //     0xe7e878: ldr             fp, [SP], #8
    // 0xe7e87c: ldur            x0, [fp, #-0x28]
    // 0xe7e880: LoadField: r1 = r0->field_f
    //     0xe7e880: ldur            w1, [x0, #0xf]
    // 0xe7e884: DecompressPointer r1
    //     0xe7e884: add             x1, x1, HEAP, lsl #32
    // 0xe7e888: r0 = LoadClassIdInstr(r1)
    //     0xe7e888: ldur            x0, [x1, #-1]
    //     0xe7e88c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7e890: r0 = GDT[cid_x0 + -0xeaf]()
    //     0xe7e890: sub             lr, x0, #0xeaf
    //     0xe7e894: ldr             lr, [x21, lr, lsl #3]
    //     0xe7e898: blr             lr
    // 0xe7e89c: r0 = Null
    //     0xe7e89c: mov             x0, NULL
    // 0xe7e8a0: LeaveFrame
    //     0xe7e8a0: mov             SP, fp
    //     0xe7e8a4: ldp             fp, lr, [SP], #0x10
    // 0xe7e8a8: ret
    //     0xe7e8a8: ret             
    // 0xe7e8ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7e8ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7e8b0: b               #0xe7e6e4
    // 0xe7e8b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe7e8b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe7e8b8: r9 = _iv
    //     0xe7e8b8: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d50] Field <OFBBlockCipher._iv@913308593>: late (offset: 0x14)
    //     0xe7e8bc: ldr             x9, [x9, #0xd50]
    // 0xe7e8c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7e8c0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0xe85eb8, size: 0x488
    // 0xe85eb8: EnterFrame
    //     0xe85eb8: stp             fp, lr, [SP, #-0x10]!
    //     0xe85ebc: mov             fp, SP
    // 0xe85ec0: AllocStack(0x50)
    //     0xe85ec0: sub             SP, SP, #0x50
    // 0xe85ec4: SetupParameters(OFBBlockCipher this /* r1 => r4, fp-0x20 */, dynamic _ /* r3 => r0, fp-0x28 */)
    //     0xe85ec4: mov             x4, x1
    //     0xe85ec8: mov             x0, x3
    //     0xe85ecc: stur            x1, [fp, #-0x20]
    //     0xe85ed0: stur            x3, [fp, #-0x28]
    // 0xe85ed4: CheckStackOverflow
    //     0xe85ed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe85ed8: cmp             SP, x16
    //     0xe85edc: b.ls            #0xe8632c
    // 0xe85ee0: r1 = LoadClassIdInstr(r0)
    //     0xe85ee0: ldur            x1, [x0, #-1]
    //     0xe85ee4: ubfx            x1, x1, #0xc, #0x14
    // 0xe85ee8: cmp             x1, #0x2a8
    // 0xe85eec: b.ne            #0xe862dc
    // 0xe85ef0: LoadField: r6 = r0->field_b
    //     0xe85ef0: ldur            w6, [x0, #0xb]
    // 0xe85ef4: DecompressPointer r6
    //     0xe85ef4: add             x6, x6, HEAP, lsl #32
    // 0xe85ef8: stur            x6, [fp, #-0x18]
    // 0xe85efc: LoadField: r1 = r6->field_13
    //     0xe85efc: ldur            w1, [x6, #0x13]
    // 0xe85f00: LoadField: r5 = r4->field_13
    //     0xe85f00: ldur            w5, [x4, #0x13]
    // 0xe85f04: DecompressPointer r5
    //     0xe85f04: add             x5, x5, HEAP, lsl #32
    // 0xe85f08: r16 = Sentinel
    //     0xe85f08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe85f0c: cmp             w5, w16
    // 0xe85f10: b.eq            #0xe86334
    // 0xe85f14: stur            x5, [fp, #-0x50]
    // 0xe85f18: LoadField: r7 = r5->field_13
    //     0xe85f18: ldur            w7, [x5, #0x13]
    // 0xe85f1c: stur            x7, [fp, #-0x48]
    // 0xe85f20: r8 = LoadInt32Instr(r1)
    //     0xe85f20: sbfx            x8, x1, #1, #0x1f
    // 0xe85f24: stur            x8, [fp, #-0x10]
    // 0xe85f28: r9 = LoadInt32Instr(r7)
    //     0xe85f28: sbfx            x9, x7, #1, #0x1f
    // 0xe85f2c: stur            x9, [fp, #-0x40]
    // 0xe85f30: cmp             x8, x9
    // 0xe85f34: b.ge            #0xe86134
    // 0xe85f38: sub             x7, x9, x8
    // 0xe85f3c: mov             x1, x5
    // 0xe85f40: mov             x3, x7
    // 0xe85f44: stur            x7, [fp, #-8]
    // 0xe85f48: r2 = 0
    //     0xe85f48: movz            x2, #0
    // 0xe85f4c: r5 = 0
    //     0xe85f4c: movz            x5, #0
    // 0xe85f50: r0 = fillRange()
    //     0xe85f50: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0xe85f54: ldur            x4, [fp, #-0x20]
    // 0xe85f58: LoadField: r5 = r4->field_13
    //     0xe85f58: ldur            w5, [x4, #0x13]
    // 0xe85f5c: DecompressPointer r5
    //     0xe85f5c: add             x5, x5, HEAP, lsl #32
    // 0xe85f60: ldur            x7, [fp, #-8]
    // 0xe85f64: ldur            x6, [fp, #-0x10]
    // 0xe85f68: stur            x5, [fp, #-0x38]
    // 0xe85f6c: add             x8, x6, x7
    // 0xe85f70: stur            x8, [fp, #-0x30]
    // 0xe85f74: tbnz            x7, #0x3f, #0xe85f90
    // 0xe85f78: cmp             x7, x8
    // 0xe85f7c: b.gt            #0xe85f90
    // 0xe85f80: LoadField: r0 = r5->field_13
    //     0xe85f80: ldur            w0, [x5, #0x13]
    // 0xe85f84: r1 = LoadInt32Instr(r0)
    //     0xe85f84: sbfx            x1, x0, #1, #0x1f
    // 0xe85f88: cmp             x8, x1
    // 0xe85f8c: b.le            #0xe85fbc
    // 0xe85f90: LoadField: r2 = r5->field_13
    //     0xe85f90: ldur            w2, [x5, #0x13]
    // 0xe85f94: r0 = BoxInt64Instr(r8)
    //     0xe85f94: sbfiz           x0, x8, #1, #0x1f
    //     0xe85f98: cmp             x8, x0, asr #1
    //     0xe85f9c: b.eq            #0xe85fa8
    //     0xe85fa0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe85fa4: stur            x8, [x0, #7]
    // 0xe85fa8: r3 = LoadInt32Instr(r2)
    //     0xe85fa8: sbfx            x3, x2, #1, #0x1f
    // 0xe85fac: mov             x1, x7
    // 0xe85fb0: mov             x2, x0
    // 0xe85fb4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe85fb4: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe85fb8: r0 = checkValidRange()
    //     0xe85fb8: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe85fbc: ldur            x2, [fp, #-8]
    // 0xe85fc0: ldur            x1, [fp, #-0x30]
    // 0xe85fc4: ldur            x0, [fp, #-0x10]
    // 0xe85fc8: sub             x3, x1, x2
    // 0xe85fcc: cmp             x0, x3
    // 0xe85fd0: b.lt            #0xe86314
    // 0xe85fd4: cbz             x3, #0xe86290
    // 0xe85fd8: r0 = BoxInt64Instr(r3)
    //     0xe85fd8: sbfiz           x0, x3, #1, #0x1f
    //     0xe85fdc: cmp             x3, x0, asr #1
    //     0xe85fe0: b.eq            #0xe85fec
    //     0xe85fe4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe85fe8: stur            x3, [x0, #7]
    // 0xe85fec: cmp             w0, #0x800
    // 0xe85ff0: b.ge            #0xe860d0
    // 0xe85ff4: ldur            x4, [fp, #-0x18]
    // 0xe85ff8: ldur            x1, [fp, #-0x38]
    // 0xe85ffc: lsl             x3, x2, #1
    // 0xe86000: mov             x5, x0
    // 0xe86004: add             x2, x4, #0x17
    // 0xe86008: sxtw            x3, w3
    // 0xe8600c: add             x0, x1, x3, asr #1
    // 0xe86010: add             x0, x0, #0x17
    // 0xe86014: cbz             x5, #0xe860cc
    // 0xe86018: cmp             x0, x2
    // 0xe8601c: b.ls            #0xe86084
    // 0xe86020: sxtw            x5, w5
    // 0xe86024: add             x16, x2, x5, asr #1
    // 0xe86028: cmp             x0, x16
    // 0xe8602c: b.hs            #0xe86084
    // 0xe86030: mov             x2, x16
    // 0xe86034: add             x0, x0, x5, asr #1
    // 0xe86038: tbz             w5, #4, #0xe86044
    // 0xe8603c: ldr             x16, [x2, #-8]!
    // 0xe86040: str             x16, [x0, #-8]!
    // 0xe86044: tbz             w5, #3, #0xe86050
    // 0xe86048: ldr             w16, [x2, #-4]!
    // 0xe8604c: str             w16, [x0, #-4]!
    // 0xe86050: tbz             w5, #2, #0xe8605c
    // 0xe86054: ldrh            w16, [x2, #-2]!
    // 0xe86058: strh            w16, [x0, #-2]!
    // 0xe8605c: tbz             w5, #1, #0xe86068
    // 0xe86060: ldrb            w16, [x2, #-1]!
    // 0xe86064: strb            w16, [x0, #-1]!
    // 0xe86068: ands            w5, w5, #0xffffffe1
    // 0xe8606c: b.eq            #0xe860cc
    // 0xe86070: ldp             x16, x17, [x2, #-0x10]!
    // 0xe86074: stp             x16, x17, [x0, #-0x10]!
    // 0xe86078: subs            w5, w5, #0x20
    // 0xe8607c: b.ne            #0xe86070
    // 0xe86080: b               #0xe860cc
    // 0xe86084: tbz             w5, #4, #0xe86090
    // 0xe86088: ldr             x16, [x2], #8
    // 0xe8608c: str             x16, [x0], #8
    // 0xe86090: tbz             w5, #3, #0xe8609c
    // 0xe86094: ldr             w16, [x2], #4
    // 0xe86098: str             w16, [x0], #4
    // 0xe8609c: tbz             w5, #2, #0xe860a8
    // 0xe860a0: ldrh            w16, [x2], #2
    // 0xe860a4: strh            w16, [x0], #2
    // 0xe860a8: tbz             w5, #1, #0xe860b4
    // 0xe860ac: ldrb            w16, [x2], #1
    // 0xe860b0: strb            w16, [x0], #1
    // 0xe860b4: ands            w5, w5, #0xffffffe1
    // 0xe860b8: b.eq            #0xe860cc
    // 0xe860bc: ldp             x16, x17, [x2], #0x10
    // 0xe860c0: stp             x16, x17, [x0], #0x10
    // 0xe860c4: subs            w5, w5, #0x20
    // 0xe860c8: b.ne            #0xe860bc
    // 0xe860cc: b               #0xe86290
    // 0xe860d0: ldur            x4, [fp, #-0x18]
    // 0xe860d4: ldur            x1, [fp, #-0x38]
    // 0xe860d8: LoadField: r0 = r1->field_7
    //     0xe860d8: ldur            x0, [x1, #7]
    // 0xe860dc: add             x1, x0, x2
    // 0xe860e0: LoadField: r0 = r4->field_7
    //     0xe860e0: ldur            x0, [x4, #7]
    // 0xe860e4: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe860e4: mov             x2, THR
    //     0xe860e8: ldr             x9, [x2, #0x658]
    //     0xe860ec: mov             x16, x0
    //     0xe860f0: mov             x0, x1
    //     0xe860f4: mov             x1, x16
    //     0xe860f8: mov             x2, x3
    //     0xe860fc: mov             x17, fp
    //     0xe86100: str             fp, [SP, #-8]!
    //     0xe86104: mov             fp, SP
    //     0xe86108: and             SP, SP, #0xfffffffffffffff0
    //     0xe8610c: mov             x19, sp
    //     0xe86110: mov             sp, SP
    //     0xe86114: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe86118: blr             x9
    //     0xe8611c: movz            x16, #0x8
    //     0xe86120: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe86124: mov             sp, x19
    //     0xe86128: mov             SP, fp
    //     0xe8612c: ldr             fp, [SP], #8
    // 0xe86130: b               #0xe86290
    // 0xe86134: mov             x4, x6
    // 0xe86138: mov             x0, x8
    // 0xe8613c: tbz             x9, #0x3f, #0xe86154
    // 0xe86140: mov             x2, x7
    // 0xe86144: mov             x3, x9
    // 0xe86148: r1 = 0
    //     0xe86148: movz            x1, #0
    // 0xe8614c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe8614c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe86150: r0 = checkValidRange()
    //     0xe86150: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe86154: ldur            x0, [fp, #-0x10]
    // 0xe86158: ldur            x2, [fp, #-0x40]
    // 0xe8615c: cmp             x0, x2
    // 0xe86160: b.lt            #0xe86320
    // 0xe86164: cbz             x2, #0xe86290
    // 0xe86168: ldur            x0, [fp, #-0x48]
    // 0xe8616c: cmp             w0, #0x800
    // 0xe86170: b.ge            #0xe86244
    // 0xe86174: ldur            x3, [fp, #-0x18]
    // 0xe86178: ldur            x1, [fp, #-0x50]
    // 0xe8617c: mov             x4, x0
    // 0xe86180: add             x2, x3, #0x17
    // 0xe86184: add             x0, x1, #0x17
    // 0xe86188: cbz             x4, #0xe86240
    // 0xe8618c: cmp             x0, x2
    // 0xe86190: b.ls            #0xe861f8
    // 0xe86194: sxtw            x4, w4
    // 0xe86198: add             x16, x2, x4, asr #1
    // 0xe8619c: cmp             x0, x16
    // 0xe861a0: b.hs            #0xe861f8
    // 0xe861a4: mov             x2, x16
    // 0xe861a8: add             x0, x0, x4, asr #1
    // 0xe861ac: tbz             w4, #4, #0xe861b8
    // 0xe861b0: ldr             x16, [x2, #-8]!
    // 0xe861b4: str             x16, [x0, #-8]!
    // 0xe861b8: tbz             w4, #3, #0xe861c4
    // 0xe861bc: ldr             w16, [x2, #-4]!
    // 0xe861c0: str             w16, [x0, #-4]!
    // 0xe861c4: tbz             w4, #2, #0xe861d0
    // 0xe861c8: ldrh            w16, [x2, #-2]!
    // 0xe861cc: strh            w16, [x0, #-2]!
    // 0xe861d0: tbz             w4, #1, #0xe861dc
    // 0xe861d4: ldrb            w16, [x2, #-1]!
    // 0xe861d8: strb            w16, [x0, #-1]!
    // 0xe861dc: ands            w4, w4, #0xffffffe1
    // 0xe861e0: b.eq            #0xe86240
    // 0xe861e4: ldp             x16, x17, [x2, #-0x10]!
    // 0xe861e8: stp             x16, x17, [x0, #-0x10]!
    // 0xe861ec: subs            w4, w4, #0x20
    // 0xe861f0: b.ne            #0xe861e4
    // 0xe861f4: b               #0xe86240
    // 0xe861f8: tbz             w4, #4, #0xe86204
    // 0xe861fc: ldr             x16, [x2], #8
    // 0xe86200: str             x16, [x0], #8
    // 0xe86204: tbz             w4, #3, #0xe86210
    // 0xe86208: ldr             w16, [x2], #4
    // 0xe8620c: str             w16, [x0], #4
    // 0xe86210: tbz             w4, #2, #0xe8621c
    // 0xe86214: ldrh            w16, [x2], #2
    // 0xe86218: strh            w16, [x0], #2
    // 0xe8621c: tbz             w4, #1, #0xe86228
    // 0xe86220: ldrb            w16, [x2], #1
    // 0xe86224: strb            w16, [x0], #1
    // 0xe86228: ands            w4, w4, #0xffffffe1
    // 0xe8622c: b.eq            #0xe86240
    // 0xe86230: ldp             x16, x17, [x2], #0x10
    // 0xe86234: stp             x16, x17, [x0], #0x10
    // 0xe86238: subs            w4, w4, #0x20
    // 0xe8623c: b.ne            #0xe86230
    // 0xe86240: b               #0xe86290
    // 0xe86244: ldur            x3, [fp, #-0x18]
    // 0xe86248: ldur            x1, [fp, #-0x50]
    // 0xe8624c: LoadField: r0 = r1->field_7
    //     0xe8624c: ldur            x0, [x1, #7]
    // 0xe86250: LoadField: r1 = r3->field_7
    //     0xe86250: ldur            x1, [x3, #7]
    // 0xe86254: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe86254: mov             x3, THR
    //     0xe86258: ldr             x9, [x3, #0x658]
    //     0xe8625c: mov             x17, fp
    //     0xe86260: str             fp, [SP, #-8]!
    //     0xe86264: mov             fp, SP
    //     0xe86268: and             SP, SP, #0xfffffffffffffff0
    //     0xe8626c: mov             x19, sp
    //     0xe86270: mov             sp, SP
    //     0xe86274: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe86278: blr             x9
    //     0xe8627c: movz            x16, #0x8
    //     0xe86280: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe86284: mov             sp, x19
    //     0xe86288: mov             SP, fp
    //     0xe8628c: ldr             fp, [SP], #8
    // 0xe86290: ldur            x0, [fp, #-0x20]
    // 0xe86294: ldur            x3, [fp, #-0x28]
    // 0xe86298: mov             x1, x0
    // 0xe8629c: r0 = reset()
    //     0xe8629c: bl              #0xe7e6c4  ; [package:pointycastle/block/modes/ofb.dart] OFBBlockCipher::reset
    // 0xe862a0: ldur            x0, [fp, #-0x20]
    // 0xe862a4: LoadField: r1 = r0->field_f
    //     0xe862a4: ldur            w1, [x0, #0xf]
    // 0xe862a8: DecompressPointer r1
    //     0xe862a8: add             x1, x1, HEAP, lsl #32
    // 0xe862ac: ldur            x3, [fp, #-0x28]
    // 0xe862b0: LoadField: r0 = r3->field_f
    //     0xe862b0: ldur            w0, [x3, #0xf]
    // 0xe862b4: DecompressPointer r0
    //     0xe862b4: add             x0, x0, HEAP, lsl #32
    // 0xe862b8: r2 = LoadClassIdInstr(r1)
    //     0xe862b8: ldur            x2, [x1, #-1]
    //     0xe862bc: ubfx            x2, x2, #0xc, #0x14
    // 0xe862c0: mov             x3, x0
    // 0xe862c4: mov             x0, x2
    // 0xe862c8: r2 = true
    //     0xe862c8: add             x2, NULL, #0x20  ; true
    // 0xe862cc: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe862cc: sub             lr, x0, #0xeda
    //     0xe862d0: ldr             lr, [x21, lr, lsl #3]
    //     0xe862d4: blr             lr
    // 0xe862d8: b               #0xe86304
    // 0xe862dc: mov             x3, x0
    // 0xe862e0: mov             x0, x4
    // 0xe862e4: LoadField: r1 = r0->field_f
    //     0xe862e4: ldur            w1, [x0, #0xf]
    // 0xe862e8: DecompressPointer r1
    //     0xe862e8: add             x1, x1, HEAP, lsl #32
    // 0xe862ec: r0 = LoadClassIdInstr(r1)
    //     0xe862ec: ldur            x0, [x1, #-1]
    //     0xe862f0: ubfx            x0, x0, #0xc, #0x14
    // 0xe862f4: r2 = true
    //     0xe862f4: add             x2, NULL, #0x20  ; true
    // 0xe862f8: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe862f8: sub             lr, x0, #0xeda
    //     0xe862fc: ldr             lr, [x21, lr, lsl #3]
    //     0xe86300: blr             lr
    // 0xe86304: r0 = Null
    //     0xe86304: mov             x0, NULL
    // 0xe86308: LeaveFrame
    //     0xe86308: mov             SP, fp
    //     0xe8630c: ldp             fp, lr, [SP], #0x10
    // 0xe86310: ret
    //     0xe86310: ret             
    // 0xe86314: r0 = tooFew()
    //     0xe86314: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xe86318: r0 = Throw()
    //     0xe86318: bl              #0xec04b8  ; ThrowStub
    // 0xe8631c: brk             #0
    // 0xe86320: r0 = tooFew()
    //     0xe86320: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xe86324: r0 = Throw()
    //     0xe86324: bl              #0xec04b8  ; ThrowStub
    // 0xe86328: brk             #0
    // 0xe8632c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8632c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe86330: b               #0xe85ee0
    // 0xe86334: r9 = _iv
    //     0xe86334: add             x9, PP, #0x21, lsl #12  ; [pp+0x21d50] Field <OFBBlockCipher._iv@913308593>: late (offset: 0x14)
    //     0xe86338: ldr             x9, [x9, #0xd50]
    // 0xe8633c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe8633c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xea840c, size: 0x5c4
    // 0xea840c: EnterFrame
    //     0xea840c: stp             fp, lr, [SP, #-0x10]!
    //     0xea8410: mov             fp, SP
    // 0xea8414: AllocStack(0x48)
    //     0xea8414: sub             SP, SP, #0x48
    // 0xea8418: SetupParameters(OFBBlockCipher this /* r1 => r10, fp-0x20 */, dynamic _ /* r2 => r9, fp-0x28 */, dynamic _ /* r3 => r8, fp-0x30 */, dynamic _ /* r5 => r7, fp-0x38 */, dynamic _ /* r6 => r4, fp-0x40 */)
    //     0xea8418: mov             x10, x1
    //     0xea841c: mov             x9, x2
    //     0xea8420: mov             x8, x3
    //     0xea8424: mov             x7, x5
    //     0xea8428: mov             x4, x6
    //     0xea842c: stur            x1, [fp, #-0x20]
    //     0xea8430: stur            x2, [fp, #-0x28]
    //     0xea8434: stur            x3, [fp, #-0x30]
    //     0xea8438: stur            x5, [fp, #-0x38]
    //     0xea843c: stur            x6, [fp, #-0x40]
    // 0xea8440: CheckStackOverflow
    //     0xea8440: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8444: cmp             SP, x16
    //     0xea8448: b.ls            #0xea899c
    // 0xea844c: LoadField: r11 = r10->field_7
    //     0xea844c: ldur            x11, [x10, #7]
    // 0xea8450: stur            x11, [fp, #-0x18]
    // 0xea8454: add             x0, x8, x11
    // 0xea8458: LoadField: r1 = r9->field_13
    //     0xea8458: ldur            w1, [x9, #0x13]
    // 0xea845c: r12 = LoadInt32Instr(r1)
    //     0xea845c: sbfx            x12, x1, #1, #0x1f
    // 0xea8460: stur            x12, [fp, #-0x10]
    // 0xea8464: cmp             x0, x12
    // 0xea8468: b.gt            #0xea8930
    // 0xea846c: add             x0, x4, x11
    // 0xea8470: LoadField: r1 = r7->field_13
    //     0xea8470: ldur            w1, [x7, #0x13]
    // 0xea8474: r13 = LoadInt32Instr(r1)
    //     0xea8474: sbfx            x13, x1, #1, #0x1f
    // 0xea8478: stur            x13, [fp, #-8]
    // 0xea847c: cmp             x0, x13
    // 0xea8480: b.gt            #0xea8958
    // 0xea8484: LoadField: r1 = r10->field_f
    //     0xea8484: ldur            w1, [x10, #0xf]
    // 0xea8488: DecompressPointer r1
    //     0xea8488: add             x1, x1, HEAP, lsl #32
    // 0xea848c: ArrayLoad: r2 = r10[0]  ; List_4
    //     0xea848c: ldur            w2, [x10, #0x17]
    // 0xea8490: DecompressPointer r2
    //     0xea8490: add             x2, x2, HEAP, lsl #32
    // 0xea8494: cmp             w2, NULL
    // 0xea8498: b.eq            #0xea89a4
    // 0xea849c: LoadField: r5 = r10->field_1b
    //     0xea849c: ldur            w5, [x10, #0x1b]
    // 0xea84a0: DecompressPointer r5
    //     0xea84a0: add             x5, x5, HEAP, lsl #32
    // 0xea84a4: cmp             w5, NULL
    // 0xea84a8: b.eq            #0xea89a8
    // 0xea84ac: r0 = LoadClassIdInstr(r1)
    //     0xea84ac: ldur            x0, [x1, #-1]
    //     0xea84b0: ubfx            x0, x0, #0xc, #0x14
    // 0xea84b4: r3 = 0
    //     0xea84b4: movz            x3, #0
    // 0xea84b8: r6 = 0
    //     0xea84b8: movz            x6, #0
    // 0xea84bc: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea84bc: sub             lr, x0, #0xf69
    //     0xea84c0: ldr             lr, [x21, lr, lsl #3]
    //     0xea84c4: blr             lr
    // 0xea84c8: ldur            x3, [fp, #-0x20]
    // 0xea84cc: LoadField: r2 = r3->field_1b
    //     0xea84cc: ldur            w2, [x3, #0x1b]
    // 0xea84d0: DecompressPointer r2
    //     0xea84d0: add             x2, x2, HEAP, lsl #32
    // 0xea84d4: ldur            x7, [fp, #-0x28]
    // 0xea84d8: ldur            x6, [fp, #-0x30]
    // 0xea84dc: ldur            x5, [fp, #-0x38]
    // 0xea84e0: ldur            x4, [fp, #-0x40]
    // 0xea84e4: ldur            x8, [fp, #-0x18]
    // 0xea84e8: r9 = 0
    //     0xea84e8: movz            x9, #0
    // 0xea84ec: CheckStackOverflow
    //     0xea84ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea84f0: cmp             SP, x16
    //     0xea84f4: b.ls            #0xea89ac
    // 0xea84f8: cmp             x9, x8
    // 0xea84fc: b.ge            #0xea8570
    // 0xea8500: add             x10, x4, x9
    // 0xea8504: cmp             w2, NULL
    // 0xea8508: b.eq            #0xea89b4
    // 0xea850c: LoadField: r0 = r2->field_13
    //     0xea850c: ldur            w0, [x2, #0x13]
    // 0xea8510: r1 = LoadInt32Instr(r0)
    //     0xea8510: sbfx            x1, x0, #1, #0x1f
    // 0xea8514: mov             x0, x1
    // 0xea8518: mov             x1, x9
    // 0xea851c: cmp             x1, x0
    // 0xea8520: b.hs            #0xea89b8
    // 0xea8524: ArrayLoad: r11 = r2[r9]  ; List_1
    //     0xea8524: add             x16, x2, x9
    //     0xea8528: ldrb            w11, [x16, #0x17]
    // 0xea852c: add             x12, x6, x9
    // 0xea8530: ldur            x0, [fp, #-0x10]
    // 0xea8534: mov             x1, x12
    // 0xea8538: cmp             x1, x0
    // 0xea853c: b.hs            #0xea89bc
    // 0xea8540: ArrayLoad: r0 = r7[r12]  ; List_1
    //     0xea8540: add             x16, x7, x12
    //     0xea8544: ldrb            w0, [x16, #0x17]
    // 0xea8548: eor             x12, x11, x0
    // 0xea854c: ldur            x0, [fp, #-8]
    // 0xea8550: mov             x1, x10
    // 0xea8554: cmp             x1, x0
    // 0xea8558: b.hs            #0xea89c0
    // 0xea855c: ArrayStore: r5[r10] = r12  ; TypeUnknown_1
    //     0xea855c: add             x0, x5, x10
    //     0xea8560: strb            w12, [x0, #0x17]
    // 0xea8564: add             x0, x9, #1
    // 0xea8568: mov             x9, x0
    // 0xea856c: b               #0xea84ec
    // 0xea8570: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xea8570: ldur            w0, [x3, #0x17]
    // 0xea8574: DecompressPointer r0
    //     0xea8574: add             x0, x0, HEAP, lsl #32
    // 0xea8578: stur            x0, [fp, #-0x28]
    // 0xea857c: cmp             w0, NULL
    // 0xea8580: b.eq            #0xea89c4
    // 0xea8584: LoadField: r1 = r0->field_13
    //     0xea8584: ldur            w1, [x0, #0x13]
    // 0xea8588: r4 = LoadInt32Instr(r1)
    //     0xea8588: sbfx            x4, x1, #1, #0x1f
    // 0xea858c: stur            x4, [fp, #-0x10]
    // 0xea8590: sub             x5, x4, x8
    // 0xea8594: mov             x1, x0
    // 0xea8598: mov             x2, x8
    // 0xea859c: stur            x5, [fp, #-8]
    // 0xea85a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xea85a0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xea85a4: r0 = sublist()
    //     0xea85a4: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xea85a8: mov             x5, x0
    // 0xea85ac: ldur            x4, [fp, #-8]
    // 0xea85b0: stur            x5, [fp, #-0x38]
    // 0xea85b4: tbz             x4, #0x3f, #0xea85c0
    // 0xea85b8: ldur            x3, [fp, #-0x10]
    // 0xea85bc: b               #0xea85cc
    // 0xea85c0: ldur            x3, [fp, #-0x10]
    // 0xea85c4: cmp             x4, x3
    // 0xea85c8: b.le            #0xea85f0
    // 0xea85cc: r0 = BoxInt64Instr(r4)
    //     0xea85cc: sbfiz           x0, x4, #1, #0x1f
    //     0xea85d0: cmp             x4, x0, asr #1
    //     0xea85d4: b.eq            #0xea85e0
    //     0xea85d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea85dc: stur            x4, [x0, #7]
    // 0xea85e0: mov             x2, x0
    // 0xea85e4: r1 = 0
    //     0xea85e4: movz            x1, #0
    // 0xea85e8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea85e8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea85ec: r0 = checkValidRange()
    //     0xea85ec: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea85f0: ldur            x20, [fp, #-8]
    // 0xea85f4: ldur            x2, [fp, #-0x38]
    // 0xea85f8: LoadField: r0 = r2->field_13
    //     0xea85f8: ldur            w0, [x2, #0x13]
    // 0xea85fc: r1 = LoadInt32Instr(r0)
    //     0xea85fc: sbfx            x1, x0, #1, #0x1f
    // 0xea8600: cmp             x1, x20
    // 0xea8604: b.lt            #0xea8984
    // 0xea8608: cbz             x20, #0xea8740
    // 0xea860c: r0 = BoxInt64Instr(r20)
    //     0xea860c: sbfiz           x0, x20, #1, #0x1f
    //     0xea8610: cmp             x20, x0, asr #1
    //     0xea8614: b.eq            #0xea8620
    //     0xea8618: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea861c: stur            x20, [x0, #7]
    // 0xea8620: cmp             w0, #0x800
    // 0xea8624: b.ge            #0xea86f4
    // 0xea8628: ldur            x1, [fp, #-0x28]
    // 0xea862c: mov             x4, x0
    // 0xea8630: add             x3, x2, #0x17
    // 0xea8634: add             x0, x1, #0x17
    // 0xea8638: cbz             x4, #0xea86f0
    // 0xea863c: cmp             x0, x3
    // 0xea8640: b.ls            #0xea86a8
    // 0xea8644: sxtw            x4, w4
    // 0xea8648: add             x16, x3, x4, asr #1
    // 0xea864c: cmp             x0, x16
    // 0xea8650: b.hs            #0xea86a8
    // 0xea8654: mov             x3, x16
    // 0xea8658: add             x0, x0, x4, asr #1
    // 0xea865c: tbz             w4, #4, #0xea8668
    // 0xea8660: ldr             x16, [x3, #-8]!
    // 0xea8664: str             x16, [x0, #-8]!
    // 0xea8668: tbz             w4, #3, #0xea8674
    // 0xea866c: ldr             w16, [x3, #-4]!
    // 0xea8670: str             w16, [x0, #-4]!
    // 0xea8674: tbz             w4, #2, #0xea8680
    // 0xea8678: ldrh            w16, [x3, #-2]!
    // 0xea867c: strh            w16, [x0, #-2]!
    // 0xea8680: tbz             w4, #1, #0xea868c
    // 0xea8684: ldrb            w16, [x3, #-1]!
    // 0xea8688: strb            w16, [x0, #-1]!
    // 0xea868c: ands            w4, w4, #0xffffffe1
    // 0xea8690: b.eq            #0xea86f0
    // 0xea8694: ldp             x16, x17, [x3, #-0x10]!
    // 0xea8698: stp             x16, x17, [x0, #-0x10]!
    // 0xea869c: subs            w4, w4, #0x20
    // 0xea86a0: b.ne            #0xea8694
    // 0xea86a4: b               #0xea86f0
    // 0xea86a8: tbz             w4, #4, #0xea86b4
    // 0xea86ac: ldr             x16, [x3], #8
    // 0xea86b0: str             x16, [x0], #8
    // 0xea86b4: tbz             w4, #3, #0xea86c0
    // 0xea86b8: ldr             w16, [x3], #4
    // 0xea86bc: str             w16, [x0], #4
    // 0xea86c0: tbz             w4, #2, #0xea86cc
    // 0xea86c4: ldrh            w16, [x3], #2
    // 0xea86c8: strh            w16, [x0], #2
    // 0xea86cc: tbz             w4, #1, #0xea86d8
    // 0xea86d0: ldrb            w16, [x3], #1
    // 0xea86d4: strb            w16, [x0], #1
    // 0xea86d8: ands            w4, w4, #0xffffffe1
    // 0xea86dc: b.eq            #0xea86f0
    // 0xea86e0: ldp             x16, x17, [x3], #0x10
    // 0xea86e4: stp             x16, x17, [x0], #0x10
    // 0xea86e8: subs            w4, w4, #0x20
    // 0xea86ec: b.ne            #0xea86e0
    // 0xea86f0: b               #0xea8740
    // 0xea86f4: ldur            x1, [fp, #-0x28]
    // 0xea86f8: LoadField: r0 = r1->field_7
    //     0xea86f8: ldur            x0, [x1, #7]
    // 0xea86fc: LoadField: r1 = r2->field_7
    //     0xea86fc: ldur            x1, [x2, #7]
    // 0xea8700: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea8700: mov             x2, THR
    //     0xea8704: ldr             x9, [x2, #0x658]
    //     0xea8708: mov             x2, x20
    //     0xea870c: mov             x17, fp
    //     0xea8710: str             fp, [SP, #-8]!
    //     0xea8714: mov             fp, SP
    //     0xea8718: and             SP, SP, #0xfffffffffffffff0
    //     0xea871c: mov             x19, sp
    //     0xea8720: mov             sp, SP
    //     0xea8724: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea8728: blr             x9
    //     0xea872c: movz            x16, #0x8
    //     0xea8730: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea8734: mov             sp, x19
    //     0xea8738: mov             SP, fp
    //     0xea873c: ldr             fp, [SP], #8
    // 0xea8740: ldur            x0, [fp, #-0x20]
    // 0xea8744: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xea8744: ldur            w4, [x0, #0x17]
    // 0xea8748: DecompressPointer r4
    //     0xea8748: add             x4, x4, HEAP, lsl #32
    // 0xea874c: stur            x4, [fp, #-0x48]
    // 0xea8750: cmp             w4, NULL
    // 0xea8754: b.eq            #0xea89c8
    // 0xea8758: LoadField: r5 = r4->field_13
    //     0xea8758: ldur            w5, [x4, #0x13]
    // 0xea875c: stur            x5, [fp, #-0x38]
    // 0xea8760: LoadField: r6 = r0->field_1b
    //     0xea8760: ldur            w6, [x0, #0x1b]
    // 0xea8764: DecompressPointer r6
    //     0xea8764: add             x6, x6, HEAP, lsl #32
    // 0xea8768: stur            x6, [fp, #-0x28]
    // 0xea876c: cmp             w6, NULL
    // 0xea8770: b.eq            #0xea89cc
    // 0xea8774: tbnz            x20, #0x3f, #0xea8784
    // 0xea8778: r0 = LoadInt32Instr(r5)
    //     0xea8778: sbfx            x0, x5, #1, #0x1f
    // 0xea877c: cmp             x20, x0
    // 0xea8780: b.le            #0xea8798
    // 0xea8784: r3 = LoadInt32Instr(r5)
    //     0xea8784: sbfx            x3, x5, #1, #0x1f
    // 0xea8788: mov             x1, x20
    // 0xea878c: mov             x2, x5
    // 0xea8790: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xea8790: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xea8794: r0 = checkValidRange()
    //     0xea8794: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xea8798: ldur            x2, [fp, #-8]
    // 0xea879c: ldur            x3, [fp, #-0x28]
    // 0xea87a0: ldur            x0, [fp, #-0x38]
    // 0xea87a4: r1 = LoadInt32Instr(r0)
    //     0xea87a4: sbfx            x1, x0, #1, #0x1f
    // 0xea87a8: sub             x4, x1, x2
    // 0xea87ac: LoadField: r0 = r3->field_13
    //     0xea87ac: ldur            w0, [x3, #0x13]
    // 0xea87b0: r1 = LoadInt32Instr(r0)
    //     0xea87b0: sbfx            x1, x0, #1, #0x1f
    // 0xea87b4: cmp             x1, x4
    // 0xea87b8: b.lt            #0xea8990
    // 0xea87bc: cbz             x4, #0xea8920
    // 0xea87c0: r0 = BoxInt64Instr(r4)
    //     0xea87c0: sbfiz           x0, x4, #1, #0x1f
    //     0xea87c4: cmp             x4, x0, asr #1
    //     0xea87c8: b.eq            #0xea87d4
    //     0xea87cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea87d0: stur            x4, [x0, #7]
    // 0xea87d4: mov             x5, x0
    // 0xea87d8: cmp             w5, #0x800
    // 0xea87dc: b.ge            #0xea88c4
    // 0xea87e0: ldur            x6, [fp, #-0x48]
    // 0xea87e4: r0 = BoxInt64Instr(r2)
    //     0xea87e4: sbfiz           x0, x2, #1, #0x1f
    //     0xea87e8: cmp             x2, x0, asr #1
    //     0xea87ec: b.eq            #0xea87f8
    //     0xea87f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea87f4: stur            x2, [x0, #7]
    // 0xea87f8: add             x2, x3, #0x17
    // 0xea87fc: sxtw            x0, w0
    // 0xea8800: add             x1, x6, x0, asr #1
    // 0xea8804: add             x1, x1, #0x17
    // 0xea8808: cbz             x5, #0xea88c0
    // 0xea880c: cmp             x1, x2
    // 0xea8810: b.ls            #0xea8878
    // 0xea8814: sxtw            x5, w5
    // 0xea8818: add             x16, x2, x5, asr #1
    // 0xea881c: cmp             x1, x16
    // 0xea8820: b.hs            #0xea8878
    // 0xea8824: mov             x2, x16
    // 0xea8828: add             x1, x1, x5, asr #1
    // 0xea882c: tbz             w5, #4, #0xea8838
    // 0xea8830: ldr             x16, [x2, #-8]!
    // 0xea8834: str             x16, [x1, #-8]!
    // 0xea8838: tbz             w5, #3, #0xea8844
    // 0xea883c: ldr             w16, [x2, #-4]!
    // 0xea8840: str             w16, [x1, #-4]!
    // 0xea8844: tbz             w5, #2, #0xea8850
    // 0xea8848: ldrh            w16, [x2, #-2]!
    // 0xea884c: strh            w16, [x1, #-2]!
    // 0xea8850: tbz             w5, #1, #0xea885c
    // 0xea8854: ldrb            w16, [x2, #-1]!
    // 0xea8858: strb            w16, [x1, #-1]!
    // 0xea885c: ands            w5, w5, #0xffffffe1
    // 0xea8860: b.eq            #0xea88c0
    // 0xea8864: ldp             x16, x17, [x2, #-0x10]!
    // 0xea8868: stp             x16, x17, [x1, #-0x10]!
    // 0xea886c: subs            w5, w5, #0x20
    // 0xea8870: b.ne            #0xea8864
    // 0xea8874: b               #0xea88c0
    // 0xea8878: tbz             w5, #4, #0xea8884
    // 0xea887c: ldr             x16, [x2], #8
    // 0xea8880: str             x16, [x1], #8
    // 0xea8884: tbz             w5, #3, #0xea8890
    // 0xea8888: ldr             w16, [x2], #4
    // 0xea888c: str             w16, [x1], #4
    // 0xea8890: tbz             w5, #2, #0xea889c
    // 0xea8894: ldrh            w16, [x2], #2
    // 0xea8898: strh            w16, [x1], #2
    // 0xea889c: tbz             w5, #1, #0xea88a8
    // 0xea88a0: ldrb            w16, [x2], #1
    // 0xea88a4: strb            w16, [x1], #1
    // 0xea88a8: ands            w5, w5, #0xffffffe1
    // 0xea88ac: b.eq            #0xea88c0
    // 0xea88b0: ldp             x16, x17, [x2], #0x10
    // 0xea88b4: stp             x16, x17, [x1], #0x10
    // 0xea88b8: subs            w5, w5, #0x20
    // 0xea88bc: b.ne            #0xea88b0
    // 0xea88c0: b               #0xea8920
    // 0xea88c4: ldur            x6, [fp, #-0x48]
    // 0xea88c8: LoadField: r0 = r6->field_7
    //     0xea88c8: ldur            x0, [x6, #7]
    // 0xea88cc: add             x1, x0, x2
    // 0xea88d0: LoadField: r0 = r3->field_7
    //     0xea88d0: ldur            x0, [x3, #7]
    // 0xea88d4: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xea88d4: mov             x2, THR
    //     0xea88d8: ldr             x9, [x2, #0x658]
    //     0xea88dc: mov             x16, x0
    //     0xea88e0: mov             x0, x1
    //     0xea88e4: mov             x1, x16
    //     0xea88e8: mov             x2, x4
    //     0xea88ec: mov             x17, fp
    //     0xea88f0: str             fp, [SP, #-8]!
    //     0xea88f4: mov             fp, SP
    //     0xea88f8: and             SP, SP, #0xfffffffffffffff0
    //     0xea88fc: mov             x19, sp
    //     0xea8900: mov             sp, SP
    //     0xea8904: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea8908: blr             x9
    //     0xea890c: movz            x16, #0x8
    //     0xea8910: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xea8914: mov             sp, x19
    //     0xea8918: mov             SP, fp
    //     0xea891c: ldr             fp, [SP], #8
    // 0xea8920: ldur            x0, [fp, #-0x18]
    // 0xea8924: LeaveFrame
    //     0xea8924: mov             SP, fp
    //     0xea8928: ldp             fp, lr, [SP], #0x10
    // 0xea892c: ret
    //     0xea892c: ret             
    // 0xea8930: r0 = ArgumentError()
    //     0xea8930: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea8934: mov             x1, x0
    // 0xea8938: r0 = "Input buffer too short"
    //     0xea8938: add             x0, PP, #0x23, lsl #12  ; [pp+0x23678] "Input buffer too short"
    //     0xea893c: ldr             x0, [x0, #0x678]
    // 0xea8940: ArrayStore: r1[0] = r0  ; List_4
    //     0xea8940: stur            w0, [x1, #0x17]
    // 0xea8944: r0 = false
    //     0xea8944: add             x0, NULL, #0x30  ; false
    // 0xea8948: StoreField: r1->field_b = r0
    //     0xea8948: stur            w0, [x1, #0xb]
    // 0xea894c: mov             x0, x1
    // 0xea8950: r0 = Throw()
    //     0xea8950: bl              #0xec04b8  ; ThrowStub
    // 0xea8954: brk             #0
    // 0xea8958: r0 = false
    //     0xea8958: add             x0, NULL, #0x30  ; false
    // 0xea895c: r0 = ArgumentError()
    //     0xea895c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea8960: mov             x1, x0
    // 0xea8964: r0 = "Output buffer too short"
    //     0xea8964: add             x0, PP, #0x23, lsl #12  ; [pp+0x23680] "Output buffer too short"
    //     0xea8968: ldr             x0, [x0, #0x680]
    // 0xea896c: ArrayStore: r1[0] = r0  ; List_4
    //     0xea896c: stur            w0, [x1, #0x17]
    // 0xea8970: r0 = false
    //     0xea8970: add             x0, NULL, #0x30  ; false
    // 0xea8974: StoreField: r1->field_b = r0
    //     0xea8974: stur            w0, [x1, #0xb]
    // 0xea8978: mov             x0, x1
    // 0xea897c: r0 = Throw()
    //     0xea897c: bl              #0xec04b8  ; ThrowStub
    // 0xea8980: brk             #0
    // 0xea8984: r0 = tooFew()
    //     0xea8984: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea8988: r0 = Throw()
    //     0xea8988: bl              #0xec04b8  ; ThrowStub
    // 0xea898c: brk             #0
    // 0xea8990: r0 = tooFew()
    //     0xea8990: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0xea8994: r0 = Throw()
    //     0xea8994: bl              #0xec04b8  ; ThrowStub
    // 0xea8998: brk             #0
    // 0xea899c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea899c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea89a0: b               #0xea844c
    // 0xea89a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea89a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea89a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea89a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea89ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea89ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea89b0: b               #0xea84f8
    // 0xea89b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea89b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea89b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea89b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea89bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea89bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea89c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea89c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea89c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea89c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea89c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea89c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea89cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea89cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
