// lib: impl.block_cipher.modes.sic, url: package:pointycastle/block/modes/sic.dart

// class id: 1050939, size: 0x8
class :: {
}

// class id: 711, size: 0x14, field offset: 0x14
class SICBlockCipher extends StreamCipherAsBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xdbc

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e5668, size: 0x64
    // 0x8e5668: EnterFrame
    //     0x8e5668: stp             fp, lr, [SP, #-0x10]!
    //     0x8e566c: mov             fp, SP
    // 0x8e5670: AllocStack(0x8)
    //     0x8e5670: sub             SP, SP, #8
    // 0x8e5674: CheckStackOverflow
    //     0x8e5674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e5678: cmp             SP, x16
    //     0x8e567c: b.ls            #0x8e56c4
    // 0x8e5680: r0 = DynamicFactoryConfig()
    //     0x8e5680: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e5684: r1 = Function '<anonymous closure>': static.
    //     0x8e5684: add             x1, PP, #0x19, lsl #12  ; [pp+0x19ad8] AnonymousClosure: static (0x8e56cc), in [package:pointycastle/block/modes/sic.dart] SICBlockCipher::factoryConfig (0x8e5668)
    //     0x8e5688: ldr             x1, [x1, #0xad8]
    // 0x8e568c: r2 = Null
    //     0x8e568c: mov             x2, NULL
    // 0x8e5690: stur            x0, [fp, #-8]
    // 0x8e5694: r0 = AllocateClosure()
    //     0x8e5694: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e5698: ldur            x1, [fp, #-8]
    // 0x8e569c: mov             x5, x0
    // 0x8e56a0: r2 = BlockCipher
    //     0x8e56a0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e56a4: ldr             x2, [x2, #0xa80]
    // 0x8e56a8: r3 = "/SIC"
    //     0x8e56a8: add             x3, PP, #0x18, lsl #12  ; [pp+0x180d8] "/SIC"
    //     0x8e56ac: ldr             x3, [x3, #0xd8]
    // 0x8e56b0: r0 = DynamicFactoryConfig.suffix()
    //     0x8e56b0: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8e56b4: ldur            x0, [fp, #-8]
    // 0x8e56b8: LeaveFrame
    //     0x8e56b8: mov             SP, fp
    //     0x8e56bc: ldp             fp, lr, [SP], #0x10
    // 0x8e56c0: ret
    //     0x8e56c0: ret             
    // 0x8e56c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e56c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e56c8: b               #0x8e5680
  }
  [closure] static (dynamic) => SICBlockCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e56cc, size: 0x54
    // 0x8e56cc: EnterFrame
    //     0x8e56cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8e56d0: mov             fp, SP
    // 0x8e56d4: AllocStack(0x8)
    //     0x8e56d4: sub             SP, SP, #8
    // 0x8e56d8: SetupParameters()
    //     0x8e56d8: ldr             x0, [fp, #0x20]
    //     0x8e56dc: ldur            w1, [x0, #0x17]
    //     0x8e56e0: add             x1, x1, HEAP, lsl #32
    //     0x8e56e4: stur            x1, [fp, #-8]
    // 0x8e56e8: r1 = 1
    //     0x8e56e8: movz            x1, #0x1
    // 0x8e56ec: r0 = AllocateContext()
    //     0x8e56ec: bl              #0xec126c  ; AllocateContextStub
    // 0x8e56f0: mov             x1, x0
    // 0x8e56f4: ldur            x0, [fp, #-8]
    // 0x8e56f8: StoreField: r1->field_b = r0
    //     0x8e56f8: stur            w0, [x1, #0xb]
    // 0x8e56fc: ldr             x0, [fp, #0x10]
    // 0x8e5700: StoreField: r1->field_f = r0
    //     0x8e5700: stur            w0, [x1, #0xf]
    // 0x8e5704: mov             x2, x1
    // 0x8e5708: r1 = Function '<anonymous closure>': static.
    //     0x8e5708: add             x1, PP, #0x19, lsl #12  ; [pp+0x19ae0] AnonymousClosure: static (0x8e5720), in [package:pointycastle/block/modes/sic.dart] SICBlockCipher::factoryConfig (0x8e5668)
    //     0x8e570c: ldr             x1, [x1, #0xae0]
    // 0x8e5710: r0 = AllocateClosure()
    //     0x8e5710: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e5714: LeaveFrame
    //     0x8e5714: mov             SP, fp
    //     0x8e5718: ldp             fp, lr, [SP], #0x10
    // 0x8e571c: ret
    //     0x8e571c: ret             
  }
  [closure] static SICBlockCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8e5720, size: 0xfc
    // 0x8e5720: EnterFrame
    //     0x8e5720: stp             fp, lr, [SP, #-0x10]!
    //     0x8e5724: mov             fp, SP
    // 0x8e5728: AllocStack(0x28)
    //     0x8e5728: sub             SP, SP, #0x28
    // 0x8e572c: SetupParameters()
    //     0x8e572c: ldr             x0, [fp, #0x10]
    //     0x8e5730: ldur            w1, [x0, #0x17]
    //     0x8e5734: add             x1, x1, HEAP, lsl #32
    // 0x8e5738: CheckStackOverflow
    //     0x8e5738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e573c: cmp             SP, x16
    //     0x8e5740: b.ls            #0x8e5810
    // 0x8e5744: LoadField: r0 = r1->field_f
    //     0x8e5744: ldur            w0, [x1, #0xf]
    // 0x8e5748: DecompressPointer r0
    //     0x8e5748: add             x0, x0, HEAP, lsl #32
    // 0x8e574c: r1 = LoadClassIdInstr(r0)
    //     0x8e574c: ldur            x1, [x0, #-1]
    //     0x8e5750: ubfx            x1, x1, #0xc, #0x14
    // 0x8e5754: mov             x16, x0
    // 0x8e5758: mov             x0, x1
    // 0x8e575c: mov             x1, x16
    // 0x8e5760: r2 = 1
    //     0x8e5760: movz            x2, #0x1
    // 0x8e5764: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e5764: sub             lr, x0, #0xfdd
    //     0x8e5768: ldr             lr, [x21, lr, lsl #3]
    //     0x8e576c: blr             lr
    // 0x8e5770: stur            x0, [fp, #-8]
    // 0x8e5774: cmp             w0, NULL
    // 0x8e5778: b.eq            #0x8e5818
    // 0x8e577c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8e577c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e5780: ldr             x0, [x0, #0x2e38]
    //     0x8e5784: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e5788: cmp             w0, w16
    //     0x8e578c: b.ne            #0x8e579c
    //     0x8e5790: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8e5794: ldr             x2, [x2, #0xf80]
    //     0x8e5798: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e579c: r16 = <BlockCipher>
    //     0x8e579c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8e57a0: ldr             x16, [x16, #0x88]
    // 0x8e57a4: stp             x0, x16, [SP, #8]
    // 0x8e57a8: ldur            x16, [fp, #-8]
    // 0x8e57ac: str             x16, [SP]
    // 0x8e57b0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e57b0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e57b4: r0 = create()
    //     0x8e57b4: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8e57b8: mov             x2, x0
    // 0x8e57bc: stur            x2, [fp, #-8]
    // 0x8e57c0: r0 = LoadClassIdInstr(r2)
    //     0x8e57c0: ldur            x0, [x2, #-1]
    //     0x8e57c4: ubfx            x0, x0, #0xc, #0x14
    // 0x8e57c8: mov             x1, x2
    // 0x8e57cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8e57cc: sub             lr, x0, #1, lsl #12
    //     0x8e57d0: ldr             lr, [x21, lr, lsl #3]
    //     0x8e57d4: blr             lr
    // 0x8e57d8: stur            x0, [fp, #-0x10]
    // 0x8e57dc: r0 = SICStreamCipher()
    //     0x8e57dc: bl              #0x8c4e90  ; AllocateSICStreamCipherStub -> SICStreamCipher (size=0x1c)
    // 0x8e57e0: mov             x1, x0
    // 0x8e57e4: ldur            x2, [fp, #-8]
    // 0x8e57e8: stur            x0, [fp, #-8]
    // 0x8e57ec: r0 = SICStreamCipher()
    //     0x8e57ec: bl              #0x8c4b0c  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::SICStreamCipher
    // 0x8e57f0: r0 = SICBlockCipher()
    //     0x8e57f0: bl              #0x8e581c  ; AllocateSICBlockCipherStub -> SICBlockCipher (size=0x14)
    // 0x8e57f4: ldur            x1, [fp, #-0x10]
    // 0x8e57f8: StoreField: r0->field_b = r1
    //     0x8e57f8: stur            x1, [x0, #0xb]
    // 0x8e57fc: ldur            x1, [fp, #-8]
    // 0x8e5800: StoreField: r0->field_7 = r1
    //     0x8e5800: stur            w1, [x0, #7]
    // 0x8e5804: LeaveFrame
    //     0x8e5804: mov             SP, fp
    //     0x8e5808: ldp             fp, lr, [SP], #0x10
    // 0x8e580c: ret
    //     0x8e580c: ret             
    // 0x8e5810: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e5810: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e5814: b               #0x8e5744
    // 0x8e5818: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e5818: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
