// lib: , url: package:pointycastle/block/rc2_engine.dart

// class id: 1050940, size: 0x8
class :: {
}

// class id: 702, size: 0x10, field offset: 0x8
class RC2Engine extends BaseBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xd94
  static late Uint8List piTable; // offset: 0xd98

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e6930, size: 0x58
    // 0x8e6930: EnterFrame
    //     0x8e6930: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6934: mov             fp, SP
    // 0x8e6938: AllocStack(0x8)
    //     0x8e6938: sub             SP, SP, #8
    // 0x8e693c: r0 = StaticFactoryConfig()
    //     0x8e693c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e6940: mov             x3, x0
    // 0x8e6944: r0 = "RC2"
    //     0x8e6944: add             x0, PP, #0x19, lsl #12  ; [pp+0x19bb0] "RC2"
    //     0x8e6948: ldr             x0, [x0, #0xbb0]
    // 0x8e694c: stur            x3, [fp, #-8]
    // 0x8e6950: StoreField: r3->field_b = r0
    //     0x8e6950: stur            w0, [x3, #0xb]
    // 0x8e6954: r1 = Function '<anonymous closure>': static.
    //     0x8e6954: add             x1, PP, #0x19, lsl #12  ; [pp+0x19bb8] AnonymousClosure: static (0x8e6988), in [package:pointycastle/block/rc2_engine.dart] RC2Engine::factoryConfig (0x8e6930)
    //     0x8e6958: ldr             x1, [x1, #0xbb8]
    // 0x8e695c: r2 = Null
    //     0x8e695c: mov             x2, NULL
    // 0x8e6960: r0 = AllocateClosure()
    //     0x8e6960: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e6964: mov             x1, x0
    // 0x8e6968: ldur            x0, [fp, #-8]
    // 0x8e696c: StoreField: r0->field_f = r1
    //     0x8e696c: stur            w1, [x0, #0xf]
    // 0x8e6970: r1 = BlockCipher
    //     0x8e6970: add             x1, PP, #0x19, lsl #12  ; [pp+0x19a80] Type: BlockCipher
    //     0x8e6974: ldr             x1, [x1, #0xa80]
    // 0x8e6978: StoreField: r0->field_7 = r1
    //     0x8e6978: stur            w1, [x0, #7]
    // 0x8e697c: LeaveFrame
    //     0x8e697c: mov             SP, fp
    //     0x8e6980: ldp             fp, lr, [SP], #0x10
    // 0x8e6984: ret
    //     0x8e6984: ret             
  }
  [closure] static RC2Engine <anonymous closure>(dynamic) {
    // ** addr: 0x8e6988, size: 0x20
    // 0x8e6988: EnterFrame
    //     0x8e6988: stp             fp, lr, [SP, #-0x10]!
    //     0x8e698c: mov             fp, SP
    // 0x8e6990: r0 = RC2Engine()
    //     0x8e6990: bl              #0x8e69a8  ; AllocateRC2EngineStub -> RC2Engine (size=0x10)
    // 0x8e6994: r1 = false
    //     0x8e6994: add             x1, NULL, #0x30  ; false
    // 0x8e6998: StoreField: r0->field_7 = r1
    //     0x8e6998: stur            w1, [x0, #7]
    // 0x8e699c: LeaveFrame
    //     0x8e699c: mov             SP, fp
    //     0x8e69a0: ldp             fp, lr, [SP], #0x10
    // 0x8e69a4: ret
    //     0x8e69a4: ret             
  }
  _ init(/* No info */) {
    // ** addr: 0xe86340, size: 0xa4
    // 0xe86340: EnterFrame
    //     0xe86340: stp             fp, lr, [SP, #-0x10]!
    //     0xe86344: mov             fp, SP
    // 0xe86348: AllocStack(0x8)
    //     0xe86348: sub             SP, SP, #8
    // 0xe8634c: SetupParameters(RC2Engine this /* r1 => r0, fp-0x8 */)
    //     0xe8634c: mov             x0, x1
    //     0xe86350: stur            x1, [fp, #-8]
    // 0xe86354: CheckStackOverflow
    //     0xe86354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe86358: cmp             SP, x16
    //     0xe8635c: b.ls            #0xe863d0
    // 0xe86360: StoreField: r0->field_7 = r2
    //     0xe86360: stur            w2, [x0, #7]
    // 0xe86364: r1 = LoadClassIdInstr(r3)
    //     0xe86364: ldur            x1, [x3, #-1]
    //     0xe86368: ubfx            x1, x1, #0xc, #0x14
    // 0xe8636c: cmp             x1, #0x2ac
    // 0xe86370: b.ne            #0xe863c0
    // 0xe86374: LoadField: r2 = r3->field_7
    //     0xe86374: ldur            w2, [x3, #7]
    // 0xe86378: DecompressPointer r2
    //     0xe86378: add             x2, x2, HEAP, lsl #32
    // 0xe8637c: r16 = Sentinel
    //     0xe8637c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe86380: cmp             w2, w16
    // 0xe86384: b.eq            #0xe863d8
    // 0xe86388: LoadField: r1 = r2->field_13
    //     0xe86388: ldur            w1, [x2, #0x13]
    // 0xe8638c: r3 = LoadInt32Instr(r1)
    //     0xe8638c: sbfx            x3, x1, #1, #0x1f
    // 0xe86390: lsl             x1, x3, #3
    // 0xe86394: mov             x3, x1
    // 0xe86398: mov             x1, x0
    // 0xe8639c: r0 = generateWorkingKey()
    //     0xe8639c: bl              #0xe863e4  ; [package:pointycastle/block/rc2_engine.dart] RC2Engine::generateWorkingKey
    // 0xe863a0: ldur            x1, [fp, #-8]
    // 0xe863a4: StoreField: r1->field_b = r0
    //     0xe863a4: stur            w0, [x1, #0xb]
    //     0xe863a8: ldurb           w16, [x1, #-1]
    //     0xe863ac: ldurb           w17, [x0, #-1]
    //     0xe863b0: and             x16, x17, x16, lsr #2
    //     0xe863b4: tst             x16, HEAP, lsr #32
    //     0xe863b8: b.eq            #0xe863c0
    //     0xe863bc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe863c0: r0 = Null
    //     0xe863c0: mov             x0, NULL
    // 0xe863c4: LeaveFrame
    //     0xe863c4: mov             SP, fp
    //     0xe863c8: ldp             fp, lr, [SP], #0x10
    // 0xe863cc: ret
    //     0xe863cc: ret             
    // 0xe863d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe863d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe863d4: b               #0xe86360
    // 0xe863d8: r9 = key
    //     0xe863d8: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a80] Field <KeyParameter.key>: late (offset: 0x8)
    //     0xe863dc: ldr             x9, [x9, #0xa80]
    // 0xe863e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe863e0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ generateWorkingKey(/* No info */) {
    // ** addr: 0xe863e4, size: 0x570
    // 0xe863e4: EnterFrame
    //     0xe863e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe863e8: mov             fp, SP
    // 0xe863ec: AllocStack(0x38)
    //     0xe863ec: sub             SP, SP, #0x38
    // 0xe863f0: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xe863f0: mov             x0, x2
    //     0xe863f4: stur            x2, [fp, #-8]
    //     0xe863f8: stur            x3, [fp, #-0x10]
    // 0xe863fc: CheckStackOverflow
    //     0xe863fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe86400: cmp             SP, x16
    //     0xe86404: b.ls            #0xe868bc
    // 0xe86408: r1 = <int>
    //     0xe86408: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8640c: r2 = 128
    //     0xe8640c: movz            x2, #0x80
    // 0xe86410: r0 = _GrowableList()
    //     0xe86410: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe86414: LoadField: r1 = r0->field_b
    //     0xe86414: ldur            w1, [x0, #0xb]
    // 0xe86418: r2 = LoadInt32Instr(r1)
    //     0xe86418: sbfx            x2, x1, #1, #0x1f
    // 0xe8641c: stur            x2, [fp, #-0x38]
    // 0xe86420: LoadField: r3 = r0->field_f
    //     0xe86420: ldur            w3, [x0, #0xf]
    // 0xe86424: DecompressPointer r3
    //     0xe86424: add             x3, x3, HEAP, lsl #32
    // 0xe86428: stur            x3, [fp, #-0x30]
    // 0xe8642c: r0 = 0
    //     0xe8642c: movz            x0, #0
    // 0xe86430: CheckStackOverflow
    //     0xe86430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe86434: cmp             SP, x16
    //     0xe86438: b.ls            #0xe868c4
    // 0xe8643c: cmp             x0, x2
    // 0xe86440: b.ge            #0xe86458
    // 0xe86444: ArrayStore: r3[r0] = rZR  ; Unknown_4
    //     0xe86444: add             x1, x3, x0, lsl #2
    //     0xe86448: stur            wzr, [x1, #0xf]
    // 0xe8644c: add             x1, x0, #1
    // 0xe86450: mov             x0, x1
    // 0xe86454: b               #0xe86430
    // 0xe86458: ldur            x4, [fp, #-8]
    // 0xe8645c: LoadField: r0 = r4->field_13
    //     0xe8645c: ldur            w0, [x4, #0x13]
    // 0xe86460: r5 = LoadInt32Instr(r0)
    //     0xe86460: sbfx            x5, x0, #1, #0x1f
    // 0xe86464: r7 = 0
    //     0xe86464: movz            x7, #0
    // 0xe86468: r6 = 255
    //     0xe86468: movz            x6, #0xff
    // 0xe8646c: CheckStackOverflow
    //     0xe8646c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe86470: cmp             SP, x16
    //     0xe86474: b.ls            #0xe868cc
    // 0xe86478: cmp             x7, x5
    // 0xe8647c: b.eq            #0xe864c8
    // 0xe86480: mov             x0, x5
    // 0xe86484: mov             x1, x7
    // 0xe86488: cmp             x1, x0
    // 0xe8648c: b.hs            #0xe868d4
    // 0xe86490: ArrayLoad: r0 = r4[r7]  ; List_1
    //     0xe86490: add             x16, x4, x7
    //     0xe86494: ldrb            w0, [x16, #0x17]
    // 0xe86498: ubfx            x0, x0, #0, #0x20
    // 0xe8649c: and             x8, x0, x6
    // 0xe864a0: mov             x0, x2
    // 0xe864a4: mov             x1, x7
    // 0xe864a8: cmp             x1, x0
    // 0xe864ac: b.hs            #0xe868d8
    // 0xe864b0: lsl             w0, w8, #1
    // 0xe864b4: ArrayStore: r3[r7] = r0  ; Unknown_4
    //     0xe864b4: add             x1, x3, x7, lsl #2
    //     0xe864b8: stur            w0, [x1, #0xf]
    // 0xe864bc: add             x0, x7, #1
    // 0xe864c0: mov             x7, x0
    // 0xe864c4: b               #0xe8646c
    // 0xe864c8: cmp             x5, #0x80
    // 0xe864cc: b.ge            #0xe86608
    // 0xe864d0: sub             x4, x5, #1
    // 0xe864d4: mov             x0, x2
    // 0xe864d8: mov             x1, x4
    // 0xe864dc: cmp             x1, x0
    // 0xe864e0: b.hs            #0xe868dc
    // 0xe864e4: ArrayLoad: r0 = r3[r4]  ; Unknown_4
    //     0xe864e4: add             x16, x3, x4, lsl #2
    //     0xe864e8: ldur            w0, [x16, #0xf]
    // 0xe864ec: DecompressPointer r0
    //     0xe864ec: add             x0, x0, HEAP, lsl #32
    // 0xe864f0: r1 = LoadInt32Instr(r0)
    //     0xe864f0: sbfx            x1, x0, #1, #0x1f
    //     0xe864f4: tbz             w0, #0, #0xe864fc
    //     0xe864f8: ldur            x1, [x0, #7]
    // 0xe864fc: mov             x4, x1
    // 0xe86500: mov             x0, x5
    // 0xe86504: r1 = 0
    //     0xe86504: movz            x1, #0
    // 0xe86508: stur            x4, [fp, #-0x18]
    // 0xe8650c: stur            x0, [fp, #-0x20]
    // 0xe86510: stur            x1, [fp, #-0x28]
    // 0xe86514: CheckStackOverflow
    //     0xe86514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe86518: cmp             SP, x16
    //     0xe8651c: b.ls            #0xe868e0
    // 0xe86520: r0 = InitLateStaticField(0xd98) // [package:pointycastle/block/rc2_engine.dart] RC2Engine::piTable
    //     0xe86520: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe86524: ldr             x0, [x0, #0x1b30]
    //     0xe86528: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8652c: cmp             w0, w16
    //     0xe86530: b.ne            #0xe86540
    //     0xe86534: add             x2, PP, #0x21, lsl #12  ; [pp+0x21d80] Field <RC2Engine.piTable>: static late (offset: 0xd98)
    //     0xe86538: ldr             x2, [x2, #0xd80]
    //     0xe8653c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xe86540: mov             x3, x0
    // 0xe86544: ldur            x2, [fp, #-0x28]
    // 0xe86548: add             x5, x2, #1
    // 0xe8654c: ldur            x0, [fp, #-0x38]
    // 0xe86550: mov             x1, x2
    // 0xe86554: cmp             x1, x0
    // 0xe86558: b.hs            #0xe868e8
    // 0xe8655c: ldur            x6, [fp, #-0x30]
    // 0xe86560: ArrayLoad: r0 = r6[r2]  ; Unknown_4
    //     0xe86560: add             x16, x6, x2, lsl #2
    //     0xe86564: ldur            w0, [x16, #0xf]
    // 0xe86568: DecompressPointer r0
    //     0xe86568: add             x0, x0, HEAP, lsl #32
    // 0xe8656c: r1 = LoadInt32Instr(r0)
    //     0xe8656c: sbfx            x1, x0, #1, #0x1f
    //     0xe86570: tbz             w0, #0, #0xe86578
    //     0xe86574: ldur            x1, [x0, #7]
    // 0xe86578: ldur            x0, [fp, #-0x18]
    // 0xe8657c: ubfx            x0, x0, #0, #0x20
    // 0xe86580: add             w2, w0, w1
    // 0xe86584: r7 = 255
    //     0xe86584: movz            x7, #0xff
    // 0xe86588: and             x0, x2, x7
    // 0xe8658c: LoadField: r1 = r3->field_13
    //     0xe8658c: ldur            w1, [x3, #0x13]
    // 0xe86590: r2 = LoadInt32Instr(r1)
    //     0xe86590: sbfx            x2, x1, #1, #0x1f
    // 0xe86594: mov             x4, x0
    // 0xe86598: ubfx            x4, x4, #0, #0x20
    // 0xe8659c: mov             x0, x2
    // 0xe865a0: mov             x1, x4
    // 0xe865a4: cmp             x1, x0
    // 0xe865a8: b.hs            #0xe868ec
    // 0xe865ac: ArrayLoad: r0 = r3[r4]  ; List_1
    //     0xe865ac: add             x16, x3, x4
    //     0xe865b0: ldrb            w0, [x16, #0x17]
    // 0xe865b4: ubfx            x0, x0, #0, #0x20
    // 0xe865b8: and             x2, x0, x7
    // 0xe865bc: ldur            x3, [fp, #-0x20]
    // 0xe865c0: add             x8, x3, #1
    // 0xe865c4: ldur            x0, [fp, #-0x38]
    // 0xe865c8: mov             x1, x3
    // 0xe865cc: cmp             x1, x0
    // 0xe865d0: b.hs            #0xe868f0
    // 0xe865d4: lsl             w0, w2, #1
    // 0xe865d8: ArrayStore: r6[r3] = r0  ; Unknown_4
    //     0xe865d8: add             x1, x6, x3, lsl #2
    //     0xe865dc: stur            w0, [x1, #0xf]
    // 0xe865e0: cmp             x8, #0x80
    // 0xe865e4: b.ge            #0xe86610
    // 0xe865e8: ubfx            x2, x2, #0, #0x20
    // 0xe865ec: mov             x4, x2
    // 0xe865f0: mov             x0, x8
    // 0xe865f4: mov             x1, x5
    // 0xe865f8: mov             x3, x6
    // 0xe865fc: ldur            x2, [fp, #-0x38]
    // 0xe86600: mov             x6, x7
    // 0xe86604: b               #0xe86508
    // 0xe86608: mov             x7, x6
    // 0xe8660c: mov             x6, x3
    // 0xe86610: ldur            x0, [fp, #-0x10]
    // 0xe86614: add             x1, x0, #7
    // 0xe86618: asr             x2, x1, #3
    // 0xe8661c: stur            x2, [fp, #-0x18]
    // 0xe86620: r0 = InitLateStaticField(0xd98) // [package:pointycastle/block/rc2_engine.dart] RC2Engine::piTable
    //     0xe86620: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe86624: ldr             x0, [x0, #0x1b30]
    //     0xe86628: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8662c: cmp             w0, w16
    //     0xe86630: b.ne            #0xe86640
    //     0xe86634: add             x2, PP, #0x21, lsl #12  ; [pp+0x21d80] Field <RC2Engine.piTable>: static late (offset: 0xd98)
    //     0xe86638: ldr             x2, [x2, #0xd80]
    //     0xe8663c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xe86640: mov             x3, x0
    // 0xe86644: ldur            x2, [fp, #-0x18]
    // 0xe86648: r0 = 128
    //     0xe86648: movz            x0, #0x80
    // 0xe8664c: sub             x4, x0, x2
    // 0xe86650: ldur            x0, [fp, #-0x38]
    // 0xe86654: mov             x1, x4
    // 0xe86658: cmp             x1, x0
    // 0xe8665c: b.hs            #0xe868f4
    // 0xe86660: ldur            x5, [fp, #-0x30]
    // 0xe86664: ArrayLoad: r0 = r5[r4]  ; Unknown_4
    //     0xe86664: add             x16, x5, x4, lsl #2
    //     0xe86668: ldur            w0, [x16, #0xf]
    // 0xe8666c: DecompressPointer r0
    //     0xe8666c: add             x0, x0, HEAP, lsl #32
    // 0xe86670: ldur            x1, [fp, #-0x10]
    // 0xe86674: neg             x6, x1
    // 0xe86678: ubfx            x6, x6, #0, #0x20
    // 0xe8667c: r1 = 7
    //     0xe8667c: movz            x1, #0x7
    // 0xe86680: and             x7, x6, x1
    // 0xe86684: ubfx            x7, x7, #0, #0x20
    // 0xe86688: r6 = 255
    //     0xe86688: movz            x6, #0xff
    // 0xe8668c: tbnz            x7, #0x3f, #0xe868f8
    // 0xe86690: lsr             w1, w6, w7
    // 0xe86694: cmp             x7, #0x1f
    // 0xe86698: csel            x1, x1, xzr, le
    // 0xe8669c: r7 = LoadInt32Instr(r0)
    //     0xe8669c: sbfx            x7, x0, #1, #0x1f
    //     0xe866a0: tbz             w0, #0, #0xe866a8
    //     0xe866a4: ldur            x7, [x0, #7]
    // 0xe866a8: and             x0, x7, x1
    // 0xe866ac: LoadField: r1 = r3->field_13
    //     0xe866ac: ldur            w1, [x3, #0x13]
    // 0xe866b0: r7 = LoadInt32Instr(r1)
    //     0xe866b0: sbfx            x7, x1, #1, #0x1f
    // 0xe866b4: mov             x8, x0
    // 0xe866b8: ubfx            x8, x8, #0, #0x20
    // 0xe866bc: mov             x0, x7
    // 0xe866c0: mov             x1, x8
    // 0xe866c4: cmp             x1, x0
    // 0xe866c8: b.hs            #0xe86920
    // 0xe866cc: ArrayLoad: r0 = r3[r8]  ; List_1
    //     0xe866cc: add             x16, x3, x8
    //     0xe866d0: ldrb            w0, [x16, #0x17]
    // 0xe866d4: ubfx            x0, x0, #0, #0x20
    // 0xe866d8: and             x1, x0, x6
    // 0xe866dc: lsl             w0, w1, #1
    // 0xe866e0: ArrayStore: r5[r4] = r0  ; Unknown_4
    //     0xe866e0: add             x8, x5, x4, lsl #2
    //     0xe866e4: stur            w0, [x8, #0xf]
    // 0xe866e8: sub             x0, x4, #1
    // 0xe866ec: ubfx            x1, x1, #0, #0x20
    // 0xe866f0: mov             x8, x1
    // 0xe866f4: mov             x4, x0
    // 0xe866f8: CheckStackOverflow
    //     0xe866f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe866fc: cmp             SP, x16
    //     0xe86700: b.ls            #0xe86924
    // 0xe86704: tbnz            x4, #0x3f, #0xe86788
    // 0xe86708: add             x9, x4, x2
    // 0xe8670c: ldur            x0, [fp, #-0x38]
    // 0xe86710: mov             x1, x9
    // 0xe86714: cmp             x1, x0
    // 0xe86718: b.hs            #0xe8692c
    // 0xe8671c: ArrayLoad: r0 = r5[r9]  ; Unknown_4
    //     0xe8671c: add             x16, x5, x9, lsl #2
    //     0xe86720: ldur            w0, [x16, #0xf]
    // 0xe86724: DecompressPointer r0
    //     0xe86724: add             x0, x0, HEAP, lsl #32
    // 0xe86728: r1 = LoadInt32Instr(r0)
    //     0xe86728: sbfx            x1, x0, #1, #0x1f
    //     0xe8672c: tbz             w0, #0, #0xe86734
    //     0xe86730: ldur            x1, [x0, #7]
    // 0xe86734: eor             x9, x8, x1
    // 0xe86738: mov             x0, x7
    // 0xe8673c: mov             x1, x9
    // 0xe86740: cmp             x1, x0
    // 0xe86744: b.hs            #0xe86930
    // 0xe86748: ArrayLoad: r0 = r3[r9]  ; List_1
    //     0xe86748: add             x16, x3, x9
    //     0xe8674c: ldrb            w0, [x16, #0x17]
    // 0xe86750: ubfx            x0, x0, #0, #0x20
    // 0xe86754: and             x9, x0, x6
    // 0xe86758: ldur            x0, [fp, #-0x38]
    // 0xe8675c: mov             x1, x4
    // 0xe86760: cmp             x1, x0
    // 0xe86764: b.hs            #0xe86934
    // 0xe86768: lsl             w0, w9, #1
    // 0xe8676c: ArrayStore: r5[r4] = r0  ; Unknown_4
    //     0xe8676c: add             x1, x5, x4, lsl #2
    //     0xe86770: stur            w0, [x1, #0xf]
    // 0xe86774: sub             x0, x4, #1
    // 0xe86778: ubfx            x9, x9, #0, #0x20
    // 0xe8677c: mov             x8, x9
    // 0xe86780: mov             x4, x0
    // 0xe86784: b               #0xe866f8
    // 0xe86788: r1 = <int>
    //     0xe86788: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe8678c: r2 = 64
    //     0xe8678c: movz            x2, #0x40
    // 0xe86790: r0 = _GrowableList()
    //     0xe86790: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe86794: mov             x2, x0
    // 0xe86798: LoadField: r3 = r2->field_b
    //     0xe86798: ldur            w3, [x2, #0xb]
    // 0xe8679c: r4 = LoadInt32Instr(r3)
    //     0xe8679c: sbfx            x4, x3, #1, #0x1f
    // 0xe867a0: LoadField: r3 = r2->field_f
    //     0xe867a0: ldur            w3, [x2, #0xf]
    // 0xe867a4: DecompressPointer r3
    //     0xe867a4: add             x3, x3, HEAP, lsl #32
    // 0xe867a8: r5 = 0
    //     0xe867a8: movz            x5, #0
    // 0xe867ac: CheckStackOverflow
    //     0xe867ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe867b0: cmp             SP, x16
    //     0xe867b4: b.ls            #0xe86938
    // 0xe867b8: cmp             x5, x4
    // 0xe867bc: b.ge            #0xe867d4
    // 0xe867c0: ArrayStore: r3[r5] = rZR  ; Unknown_4
    //     0xe867c0: add             x6, x3, x5, lsl #2
    //     0xe867c4: stur            wzr, [x6, #0xf]
    // 0xe867c8: add             x0, x5, #1
    // 0xe867cc: mov             x5, x0
    // 0xe867d0: b               #0xe867ac
    // 0xe867d4: ldur            x5, [fp, #-0x30]
    // 0xe867d8: r6 = 0
    //     0xe867d8: movz            x6, #0
    // 0xe867dc: CheckStackOverflow
    //     0xe867dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe867e0: cmp             SP, x16
    //     0xe867e4: b.ls            #0xe86940
    // 0xe867e8: cmp             x6, x4
    // 0xe867ec: b.eq            #0xe868ac
    // 0xe867f0: lsl             x7, x6, #1
    // 0xe867f4: ldur            x0, [fp, #-0x38]
    // 0xe867f8: mov             x1, x7
    // 0xe867fc: cmp             x1, x0
    // 0xe86800: b.hs            #0xe86948
    // 0xe86804: ArrayLoad: r8 = r5[r7]  ; Unknown_4
    //     0xe86804: add             x16, x5, x7, lsl #2
    //     0xe86808: ldur            w8, [x16, #0xf]
    // 0xe8680c: DecompressPointer r8
    //     0xe8680c: add             x8, x8, HEAP, lsl #32
    // 0xe86810: add             x9, x7, #1
    // 0xe86814: ldur            x0, [fp, #-0x38]
    // 0xe86818: mov             x1, x9
    // 0xe8681c: cmp             x1, x0
    // 0xe86820: b.hs            #0xe8694c
    // 0xe86824: ArrayLoad: r7 = r5[r9]  ; Unknown_4
    //     0xe86824: add             x16, x5, x9, lsl #2
    //     0xe86828: ldur            w7, [x16, #0xf]
    // 0xe8682c: DecompressPointer r7
    //     0xe8682c: add             x7, x7, HEAP, lsl #32
    // 0xe86830: r9 = LoadInt32Instr(r7)
    //     0xe86830: sbfx            x9, x7, #1, #0x1f
    //     0xe86834: tbz             w7, #0, #0xe8683c
    //     0xe86838: ldur            x9, [x7, #7]
    // 0xe8683c: lsl             x7, x9, #8
    // 0xe86840: r9 = LoadInt32Instr(r8)
    //     0xe86840: sbfx            x9, x8, #1, #0x1f
    //     0xe86844: tbz             w8, #0, #0xe8684c
    //     0xe86848: ldur            x9, [x8, #7]
    // 0xe8684c: add             x8, x9, x7
    // 0xe86850: mov             x0, x4
    // 0xe86854: mov             x1, x6
    // 0xe86858: cmp             x1, x0
    // 0xe8685c: b.hs            #0xe86950
    // 0xe86860: r0 = BoxInt64Instr(r8)
    //     0xe86860: sbfiz           x0, x8, #1, #0x1f
    //     0xe86864: cmp             x8, x0, asr #1
    //     0xe86868: b.eq            #0xe86874
    //     0xe8686c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe86870: stur            x8, [x0, #7]
    // 0xe86874: mov             x1, x3
    // 0xe86878: ArrayStore: r1[r6] = r0  ; List_4
    //     0xe86878: add             x25, x1, x6, lsl #2
    //     0xe8687c: add             x25, x25, #0xf
    //     0xe86880: str             w0, [x25]
    //     0xe86884: tbz             w0, #0, #0xe868a0
    //     0xe86888: ldurb           w16, [x1, #-1]
    //     0xe8688c: ldurb           w17, [x0, #-1]
    //     0xe86890: and             x16, x17, x16, lsr #2
    //     0xe86894: tst             x16, HEAP, lsr #32
    //     0xe86898: b.eq            #0xe868a0
    //     0xe8689c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe868a0: add             x0, x6, #1
    // 0xe868a4: mov             x6, x0
    // 0xe868a8: b               #0xe867dc
    // 0xe868ac: mov             x0, x2
    // 0xe868b0: LeaveFrame
    //     0xe868b0: mov             SP, fp
    //     0xe868b4: ldp             fp, lr, [SP], #0x10
    // 0xe868b8: ret
    //     0xe868b8: ret             
    // 0xe868bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe868bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe868c0: b               #0xe86408
    // 0xe868c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe868c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe868c8: b               #0xe8643c
    // 0xe868cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe868cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe868d0: b               #0xe86478
    // 0xe868d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe868d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe868d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe868d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe868dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe868dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe868e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe868e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe868e4: b               #0xe86520
    // 0xe868e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe868e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe868ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe868ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe868f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe868f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe868f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe868f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe868f8: str             x7, [THR, #0x7a8]  ; THR::
    // 0xe868fc: stp             x6, x7, [SP, #-0x10]!
    // 0xe86900: stp             x4, x5, [SP, #-0x10]!
    // 0xe86904: stp             x2, x3, [SP, #-0x10]!
    // 0xe86908: SaveReg r0
    //     0xe86908: str             x0, [SP, #-8]!
    // 0xe8690c: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xe86910: r4 = 0
    //     0xe86910: movz            x4, #0
    // 0xe86914: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xe86918: blr             lr
    // 0xe8691c: brk             #0
    // 0xe86920: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe86920: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe86924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe86924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe86928: b               #0xe86704
    // 0xe8692c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8692c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe86930: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe86930: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe86934: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe86934: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe86938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe86938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8693c: b               #0xe867b8
    // 0xe86940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe86940: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe86944: b               #0xe867e8
    // 0xe86948: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe86948: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe8694c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe8694c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe86950: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe86950: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static Uint8List piTable() {
    // ** addr: 0xe86954, size: 0xe98
    // 0xe86954: EnterFrame
    //     0xe86954: stp             fp, lr, [SP, #-0x10]!
    //     0xe86958: mov             fp, SP
    // 0xe8695c: AllocStack(0x10)
    //     0xe8695c: sub             SP, SP, #0x10
    // 0xe86960: r0 = 512
    //     0xe86960: movz            x0, #0x200
    // 0xe86964: CheckStackOverflow
    //     0xe86964: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe86968: cmp             SP, x16
    //     0xe8696c: b.ls            #0xe877e4
    // 0xe86970: mov             x2, x0
    // 0xe86974: r1 = <int>
    //     0xe86974: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe86978: r0 = AllocateArray()
    //     0xe86978: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe8697c: stur            x0, [fp, #-8]
    // 0xe86980: r16 = 434
    //     0xe86980: movz            x16, #0x1b2
    // 0xe86984: StoreField: r0->field_f = r16
    //     0xe86984: stur            w16, [x0, #0xf]
    // 0xe86988: r16 = 240
    //     0xe86988: movz            x16, #0xf0
    // 0xe8698c: StoreField: r0->field_13 = r16
    //     0xe8698c: stur            w16, [x0, #0x13]
    // 0xe86990: r16 = 498
    //     0xe86990: movz            x16, #0x1f2
    // 0xe86994: ArrayStore: r0[0] = r16  ; List_4
    //     0xe86994: stur            w16, [x0, #0x17]
    // 0xe86998: r16 = 392
    //     0xe86998: movz            x16, #0x188
    // 0xe8699c: StoreField: r0->field_1b = r16
    //     0xe8699c: stur            w16, [x0, #0x1b]
    // 0xe869a0: r16 = 50
    //     0xe869a0: movz            x16, #0x32
    // 0xe869a4: StoreField: r0->field_1f = r16
    //     0xe869a4: stur            w16, [x0, #0x1f]
    // 0xe869a8: r16 = 442
    //     0xe869a8: movz            x16, #0x1ba
    // 0xe869ac: StoreField: r0->field_23 = r16
    //     0xe869ac: stur            w16, [x0, #0x23]
    // 0xe869b0: r16 = 362
    //     0xe869b0: movz            x16, #0x16a
    // 0xe869b4: StoreField: r0->field_27 = r16
    //     0xe869b4: stur            w16, [x0, #0x27]
    // 0xe869b8: r16 = 474
    //     0xe869b8: movz            x16, #0x1da
    // 0xe869bc: StoreField: r0->field_2b = r16
    //     0xe869bc: stur            w16, [x0, #0x2b]
    // 0xe869c0: r16 = 80
    //     0xe869c0: movz            x16, #0x50
    // 0xe869c4: StoreField: r0->field_2f = r16
    //     0xe869c4: stur            w16, [x0, #0x2f]
    // 0xe869c8: r16 = 466
    //     0xe869c8: movz            x16, #0x1d2
    // 0xe869cc: StoreField: r0->field_33 = r16
    //     0xe869cc: stur            w16, [x0, #0x33]
    // 0xe869d0: r16 = 506
    //     0xe869d0: movz            x16, #0x1fa
    // 0xe869d4: StoreField: r0->field_37 = r16
    //     0xe869d4: stur            w16, [x0, #0x37]
    // 0xe869d8: r16 = 242
    //     0xe869d8: movz            x16, #0xf2
    // 0xe869dc: StoreField: r0->field_3b = r16
    //     0xe869dc: stur            w16, [x0, #0x3b]
    // 0xe869e0: r16 = 148
    //     0xe869e0: movz            x16, #0x94
    // 0xe869e4: StoreField: r0->field_3f = r16
    //     0xe869e4: stur            w16, [x0, #0x3f]
    // 0xe869e8: r16 = 320
    //     0xe869e8: movz            x16, #0x140
    // 0xe869ec: StoreField: r0->field_43 = r16
    //     0xe869ec: stur            w16, [x0, #0x43]
    // 0xe869f0: r16 = 432
    //     0xe869f0: movz            x16, #0x1b0
    // 0xe869f4: StoreField: r0->field_47 = r16
    //     0xe869f4: stur            w16, [x0, #0x47]
    // 0xe869f8: r16 = 314
    //     0xe869f8: movz            x16, #0x13a
    // 0xe869fc: StoreField: r0->field_4b = r16
    //     0xe869fc: stur            w16, [x0, #0x4b]
    // 0xe86a00: r16 = 396
    //     0xe86a00: movz            x16, #0x18c
    // 0xe86a04: StoreField: r0->field_4f = r16
    //     0xe86a04: stur            w16, [x0, #0x4f]
    // 0xe86a08: r16 = 252
    //     0xe86a08: movz            x16, #0xfc
    // 0xe86a0c: StoreField: r0->field_53 = r16
    //     0xe86a0c: stur            w16, [x0, #0x53]
    // 0xe86a10: r16 = 110
    //     0xe86a10: movz            x16, #0x6e
    // 0xe86a14: StoreField: r0->field_57 = r16
    //     0xe86a14: stur            w16, [x0, #0x57]
    // 0xe86a18: r16 = 262
    //     0xe86a18: movz            x16, #0x106
    // 0xe86a1c: StoreField: r0->field_5b = r16
    //     0xe86a1c: stur            w16, [x0, #0x5b]
    // 0xe86a20: r16 = 86
    //     0xe86a20: movz            x16, #0x56
    // 0xe86a24: StoreField: r0->field_5f = r16
    //     0xe86a24: stur            w16, [x0, #0x5f]
    // 0xe86a28: r16 = 236
    //     0xe86a28: movz            x16, #0xec
    // 0xe86a2c: StoreField: r0->field_63 = r16
    //     0xe86a2c: stur            w16, [x0, #0x63]
    // 0xe86a30: r16 = 166
    //     0xe86a30: movz            x16, #0xa6
    // 0xe86a34: StoreField: r0->field_67 = r16
    //     0xe86a34: stur            w16, [x0, #0x67]
    // 0xe86a38: r16 = 284
    //     0xe86a38: movz            x16, #0x11c
    // 0xe86a3c: StoreField: r0->field_6b = r16
    //     0xe86a3c: stur            w16, [x0, #0x6b]
    // 0xe86a40: r16 = 196
    //     0xe86a40: movz            x16, #0xc4
    // 0xe86a44: StoreField: r0->field_6f = r16
    //     0xe86a44: stur            w16, [x0, #0x6f]
    // 0xe86a48: r16 = 152
    //     0xe86a48: movz            x16, #0x98
    // 0xe86a4c: StoreField: r0->field_73 = r16
    //     0xe86a4c: stur            w16, [x0, #0x73]
    // 0xe86a50: r16 = 200
    //     0xe86a50: movz            x16, #0xc8
    // 0xe86a54: StoreField: r0->field_77 = r16
    //     0xe86a54: stur            w16, [x0, #0x77]
    // 0xe86a58: r16 = 272
    //     0xe86a58: movz            x16, #0x110
    // 0xe86a5c: StoreField: r0->field_7b = r16
    //     0xe86a5c: stur            w16, [x0, #0x7b]
    // 0xe86a60: r16 = 136
    //     0xe86a60: movz            x16, #0x88
    // 0xe86a64: StoreField: r0->field_7f = r16
    //     0xe86a64: stur            w16, [x0, #0x7f]
    // 0xe86a68: r16 = 278
    //     0xe86a68: movz            x16, #0x116
    // 0xe86a6c: StoreField: r0->field_83 = r16
    //     0xe86a6c: stur            w16, [x0, #0x83]
    // 0xe86a70: r16 = 502
    //     0xe86a70: movz            x16, #0x1f6
    // 0xe86a74: StoreField: r0->field_87 = r16
    //     0xe86a74: stur            w16, [x0, #0x87]
    // 0xe86a78: r16 = 324
    //     0xe86a78: movz            x16, #0x144
    // 0xe86a7c: StoreField: r0->field_8b = r16
    //     0xe86a7c: stur            w16, [x0, #0x8b]
    // 0xe86a80: r16 = 46
    //     0xe86a80: movz            x16, #0x2e
    // 0xe86a84: StoreField: r0->field_8f = r16
    //     0xe86a84: stur            w16, [x0, #0x8f]
    // 0xe86a88: r16 = 308
    //     0xe86a88: movz            x16, #0x134
    // 0xe86a8c: StoreField: r0->field_93 = r16
    //     0xe86a8c: stur            w16, [x0, #0x93]
    // 0xe86a90: r16 = 178
    //     0xe86a90: movz            x16, #0xb2
    // 0xe86a94: StoreField: r0->field_97 = r16
    //     0xe86a94: stur            w16, [x0, #0x97]
    // 0xe86a98: r16 = 490
    //     0xe86a98: movz            x16, #0x1ea
    // 0xe86a9c: StoreField: r0->field_9b = r16
    //     0xe86a9c: stur            w16, [x0, #0x9b]
    // 0xe86aa0: r16 = 270
    //     0xe86aa0: movz            x16, #0x10e
    // 0xe86aa4: StoreField: r0->field_9f = r16
    //     0xe86aa4: stur            w16, [x0, #0x9f]
    // 0xe86aa8: r16 = 358
    //     0xe86aa8: movz            x16, #0x166
    // 0xe86aac: StoreField: r0->field_a3 = r16
    //     0xe86aac: stur            w16, [x0, #0xa3]
    // 0xe86ab0: r16 = 158
    //     0xe86ab0: movz            x16, #0x9e
    // 0xe86ab4: StoreField: r0->field_a7 = r16
    //     0xe86ab4: stur            w16, [x0, #0xa7]
    // 0xe86ab8: r16 = 38
    //     0xe86ab8: movz            x16, #0x26
    // 0xe86abc: StoreField: r0->field_ab = r16
    //     0xe86abc: stur            w16, [x0, #0xab]
    // 0xe86ac0: r16 = 194
    //     0xe86ac0: movz            x16, #0xc2
    // 0xe86ac4: StoreField: r0->field_af = r16
    //     0xe86ac4: stur            w16, [x0, #0xaf]
    // 0xe86ac8: r16 = 138
    //     0xe86ac8: movz            x16, #0x8a
    // 0xe86acc: StoreField: r0->field_b3 = r16
    //     0xe86acc: stur            w16, [x0, #0xb3]
    // 0xe86ad0: r16 = 218
    //     0xe86ad0: movz            x16, #0xda
    // 0xe86ad4: StoreField: r0->field_b7 = r16
    //     0xe86ad4: stur            w16, [x0, #0xb7]
    // 0xe86ad8: r16 = 282
    //     0xe86ad8: movz            x16, #0x11a
    // 0xe86adc: StoreField: r0->field_bb = r16
    //     0xe86adc: stur            w16, [x0, #0xbb]
    // 0xe86ae0: r16 = 18
    //     0xe86ae0: movz            x16, #0x12
    // 0xe86ae4: StoreField: r0->field_bf = r16
    //     0xe86ae4: stur            w16, [x0, #0xbf]
    // 0xe86ae8: r16 = 258
    //     0xe86ae8: movz            x16, #0x102
    // 0xe86aec: StoreField: r0->field_c3 = r16
    //     0xe86aec: stur            w16, [x0, #0xc3]
    // 0xe86af0: r16 = 250
    //     0xe86af0: movz            x16, #0xfa
    // 0xe86af4: StoreField: r0->field_c7 = r16
    //     0xe86af4: stur            w16, [x0, #0xc7]
    // 0xe86af8: r16 = 100
    //     0xe86af8: movz            x16, #0x64
    // 0xe86afc: StoreField: r0->field_cb = r16
    //     0xe86afc: stur            w16, [x0, #0xcb]
    // 0xe86b00: r16 = 378
    //     0xe86b00: movz            x16, #0x17a
    // 0xe86b04: StoreField: r0->field_cf = r16
    //     0xe86b04: stur            w16, [x0, #0xcf]
    // 0xe86b08: r16 = 286
    //     0xe86b08: movz            x16, #0x11e
    // 0xe86b0c: StoreField: r0->field_d3 = r16
    //     0xe86b0c: stur            w16, [x0, #0xd3]
    // 0xe86b10: r16 = 128
    //     0xe86b10: movz            x16, #0x80
    // 0xe86b14: StoreField: r0->field_d7 = r16
    //     0xe86b14: stur            w16, [x0, #0xd7]
    // 0xe86b18: r16 = 470
    //     0xe86b18: movz            x16, #0x1d6
    // 0xe86b1c: StoreField: r0->field_db = r16
    //     0xe86b1c: stur            w16, [x0, #0xdb]
    // 0xe86b20: r16 = 268
    //     0xe86b20: movz            x16, #0x10c
    // 0xe86b24: StoreField: r0->field_df = r16
    //     0xe86b24: stur            w16, [x0, #0xdf]
    // 0xe86b28: r16 = 366
    //     0xe86b28: movz            x16, #0x16e
    // 0xe86b2c: StoreField: r0->field_e3 = r16
    //     0xe86b2c: stur            w16, [x0, #0xe3]
    // 0xe86b30: r16 = 246
    //     0xe86b30: movz            x16, #0xf6
    // 0xe86b34: StoreField: r0->field_e7 = r16
    //     0xe86b34: stur            w16, [x0, #0xe7]
    // 0xe86b38: r16 = 22
    //     0xe86b38: movz            x16, #0x16
    // 0xe86b3c: StoreField: r0->field_eb = r16
    //     0xe86b3c: stur            w16, [x0, #0xeb]
    // 0xe86b40: r16 = 480
    //     0xe86b40: movz            x16, #0x1e0
    // 0xe86b44: StoreField: r0->field_ef = r16
    //     0xe86b44: stur            w16, [x0, #0xef]
    // 0xe86b48: r16 = 298
    //     0xe86b48: movz            x16, #0x12a
    // 0xe86b4c: StoreField: r0->field_f3 = r16
    //     0xe86b4c: stur            w16, [x0, #0xf3]
    // 0xe86b50: r16 = 66
    //     0xe86b50: movz            x16, #0x42
    // 0xe86b54: StoreField: r0->field_f7 = r16
    //     0xe86b54: stur            w16, [x0, #0xf7]
    // 0xe86b58: r16 = 68
    //     0xe86b58: movz            x16, #0x44
    // 0xe86b5c: StoreField: r0->field_fb = r16
    //     0xe86b5c: stur            w16, [x0, #0xfb]
    // 0xe86b60: r16 = 184
    //     0xe86b60: movz            x16, #0xb8
    // 0xe86b64: StoreField: r0->field_ff = r16
    //     0xe86b64: stur            w16, [x0, #0xff]
    // 0xe86b68: r1 = 122
    //     0xe86b68: movz            x1, #0x7a
    // 0xe86b6c: add             x2, x0, w1, sxtw #1
    // 0xe86b70: r16 = 214
    //     0xe86b70: movz            x16, #0xd6
    // 0xe86b74: StoreField: r2->field_f = r16
    //     0xe86b74: stur            w16, [x2, #0xf]
    // 0xe86b78: r1 = 124
    //     0xe86b78: movz            x1, #0x7c
    // 0xe86b7c: add             x2, x0, w1, sxtw #1
    // 0xe86b80: r16 = 156
    //     0xe86b80: movz            x16, #0x9c
    // 0xe86b84: StoreField: r2->field_f = r16
    //     0xe86b84: stur            w16, [x2, #0xf]
    // 0xe86b88: r1 = 126
    //     0xe86b88: movz            x1, #0x7e
    // 0xe86b8c: add             x2, x0, w1, sxtw #1
    // 0xe86b90: r16 = 260
    //     0xe86b90: movz            x16, #0x104
    // 0xe86b94: StoreField: r2->field_f = r16
    //     0xe86b94: stur            w16, [x2, #0xf]
    // 0xe86b98: r1 = 128
    //     0xe86b98: movz            x1, #0x80
    // 0xe86b9c: add             x2, x0, w1, sxtw #1
    // 0xe86ba0: r16 = 168
    //     0xe86ba0: movz            x16, #0xa8
    // 0xe86ba4: StoreField: r2->field_f = r16
    //     0xe86ba4: stur            w16, [x2, #0xf]
    // 0xe86ba8: r1 = 130
    //     0xe86ba8: movz            x1, #0x82
    // 0xe86bac: add             x2, x0, w1, sxtw #1
    // 0xe86bb0: r16 = 428
    //     0xe86bb0: movz            x16, #0x1ac
    // 0xe86bb4: StoreField: r2->field_f = r16
    //     0xe86bb4: stur            w16, [x2, #0xf]
    // 0xe86bb8: r1 = 132
    //     0xe86bb8: movz            x1, #0x84
    // 0xe86bbc: add             x2, x0, w1, sxtw #1
    // 0xe86bc0: r16 = 202
    //     0xe86bc0: movz            x16, #0xca
    // 0xe86bc4: StoreField: r2->field_f = r16
    //     0xe86bc4: stur            w16, [x2, #0xf]
    // 0xe86bc8: r1 = 134
    //     0xe86bc8: movz            x1, #0x86
    // 0xe86bcc: add             x2, x0, w1, sxtw #1
    // 0xe86bd0: r16 = 294
    //     0xe86bd0: movz            x16, #0x126
    // 0xe86bd4: StoreField: r2->field_f = r16
    //     0xe86bd4: stur            w16, [x2, #0xf]
    // 0xe86bd8: r1 = 136
    //     0xe86bd8: movz            x1, #0x88
    // 0xe86bdc: add             x2, x0, w1, sxtw #1
    // 0xe86be0: r16 = 412
    //     0xe86be0: movz            x16, #0x19c
    // 0xe86be4: StoreField: r2->field_f = r16
    //     0xe86be4: stur            w16, [x2, #0xf]
    // 0xe86be8: r1 = 138
    //     0xe86be8: movz            x1, #0x8a
    // 0xe86bec: add             x2, x0, w1, sxtw #1
    // 0xe86bf0: r16 = 192
    //     0xe86bf0: movz            x16, #0xc0
    // 0xe86bf4: StoreField: r2->field_f = r16
    //     0xe86bf4: stur            w16, [x2, #0xf]
    // 0xe86bf8: r1 = 140
    //     0xe86bf8: movz            x1, #0x8c
    // 0xe86bfc: add             x2, x0, w1, sxtw #1
    // 0xe86c00: r16 = 356
    //     0xe86c00: movz            x16, #0x164
    // 0xe86c04: StoreField: r2->field_f = r16
    //     0xe86c04: stur            w16, [x2, #0xf]
    // 0xe86c08: r1 = 142
    //     0xe86c08: movz            x1, #0x8e
    // 0xe86c0c: add             x2, x0, w1, sxtw #1
    // 0xe86c10: r16 = 56
    //     0xe86c10: movz            x16, #0x38
    // 0xe86c14: StoreField: r2->field_f = r16
    //     0xe86c14: stur            w16, [x2, #0xf]
    // 0xe86c18: r1 = 144
    //     0xe86c18: movz            x1, #0x90
    // 0xe86c1c: add             x2, x0, w1, sxtw #1
    // 0xe86c20: r16 = 230
    //     0xe86c20: movz            x16, #0xe6
    // 0xe86c24: StoreField: r2->field_f = r16
    //     0xe86c24: stur            w16, [x2, #0xf]
    // 0xe86c28: r1 = 146
    //     0xe86c28: movz            x1, #0x92
    // 0xe86c2c: add             x2, x0, w1, sxtw #1
    // 0xe86c30: r16 = 172
    //     0xe86c30: movz            x16, #0xac
    // 0xe86c34: StoreField: r2->field_f = r16
    //     0xe86c34: stur            w16, [x2, #0xf]
    // 0xe86c38: r1 = 148
    //     0xe86c38: movz            x1, #0x94
    // 0xe86c3c: add             x2, x0, w1, sxtw #1
    // 0xe86c40: r16 = 384
    //     0xe86c40: movz            x16, #0x180
    // 0xe86c44: StoreField: r2->field_f = r16
    //     0xe86c44: stur            w16, [x2, #0xf]
    // 0xe86c48: r1 = 150
    //     0xe86c48: movz            x1, #0x96
    // 0xe86c4c: add             x2, x0, w1, sxtw #1
    // 0xe86c50: r16 = 40
    //     0xe86c50: movz            x16, #0x28
    // 0xe86c54: StoreField: r2->field_f = r16
    //     0xe86c54: stur            w16, [x2, #0xf]
    // 0xe86c58: r1 = 152
    //     0xe86c58: movz            x1, #0x98
    // 0xe86c5c: add             x2, x0, w1, sxtw #1
    // 0xe86c60: r16 = 334
    //     0xe86c60: movz            x16, #0x14e
    // 0xe86c64: StoreField: r2->field_f = r16
    //     0xe86c64: stur            w16, [x2, #0xf]
    // 0xe86c68: r1 = 154
    //     0xe86c68: movz            x1, #0x9a
    // 0xe86c6c: add             x2, x0, w1, sxtw #1
    // 0xe86c70: r16 = 280
    //     0xe86c70: movz            x16, #0x118
    // 0xe86c74: StoreField: r2->field_f = r16
    //     0xe86c74: stur            w16, [x2, #0xf]
    // 0xe86c78: r1 = 156
    //     0xe86c78: movz            x1, #0x9c
    // 0xe86c7c: add             x2, x0, w1, sxtw #1
    // 0xe86c80: r16 = 482
    //     0xe86c80: movz            x16, #0x1e2
    // 0xe86c84: StoreField: r2->field_f = r16
    //     0xe86c84: stur            w16, [x2, #0xf]
    // 0xe86c88: r1 = 158
    //     0xe86c88: movz            x1, #0x9e
    // 0xe86c8c: add             x2, x0, w1, sxtw #1
    // 0xe86c90: r16 = 440
    //     0xe86c90: movz            x16, #0x1b8
    // 0xe86c94: StoreField: r2->field_f = r16
    //     0xe86c94: stur            w16, [x2, #0xf]
    // 0xe86c98: r1 = 160
    //     0xe86c98: movz            x1, #0xa0
    // 0xe86c9c: add             x2, x0, w1, sxtw #1
    // 0xe86ca0: r16 = 36
    //     0xe86ca0: movz            x16, #0x24
    // 0xe86ca4: StoreField: r2->field_f = r16
    //     0xe86ca4: stur            w16, [x2, #0xf]
    // 0xe86ca8: r1 = 162
    //     0xe86ca8: movz            x1, #0xa2
    // 0xe86cac: add             x2, x0, w1, sxtw #1
    // 0xe86cb0: r16 = 234
    //     0xe86cb0: movz            x16, #0xea
    // 0xe86cb4: StoreField: r2->field_f = r16
    //     0xe86cb4: stur            w16, [x2, #0xf]
    // 0xe86cb8: r1 = 164
    //     0xe86cb8: movz            x1, #0xa4
    // 0xe86cbc: add             x2, x0, w1, sxtw #1
    // 0xe86cc0: r16 = 404
    //     0xe86cc0: movz            x16, #0x194
    // 0xe86cc4: StoreField: r2->field_f = r16
    //     0xe86cc4: stur            w16, [x2, #0xf]
    // 0xe86cc8: r1 = 166
    //     0xe86cc8: movz            x1, #0xa6
    // 0xe86ccc: add             x2, x0, w1, sxtw #1
    // 0xe86cd0: r16 = 62
    //     0xe86cd0: movz            x16, #0x3e
    // 0xe86cd4: StoreField: r2->field_f = r16
    //     0xe86cd4: stur            w16, [x2, #0xf]
    // 0xe86cd8: r1 = 168
    //     0xe86cd8: movz            x1, #0xa8
    // 0xe86cdc: add             x2, x0, w1, sxtw #1
    // 0xe86ce0: r16 = 118
    //     0xe86ce0: movz            x16, #0x76
    // 0xe86ce4: StoreField: r2->field_f = r16
    //     0xe86ce4: stur            w16, [x2, #0xf]
    // 0xe86ce8: r1 = 170
    //     0xe86ce8: movz            x1, #0xaa
    // 0xe86cec: add             x2, x0, w1, sxtw #1
    // 0xe86cf0: r16 = 380
    //     0xe86cf0: movz            x16, #0x17c
    // 0xe86cf4: StoreField: r2->field_f = r16
    //     0xe86cf4: stur            w16, [x2, #0xf]
    // 0xe86cf8: r1 = 172
    //     0xe86cf8: movz            x1, #0xac
    // 0xe86cfc: add             x2, x0, w1, sxtw #1
    // 0xe86d00: r16 = 456
    //     0xe86d00: movz            x16, #0x1c8
    // 0xe86d04: StoreField: r2->field_f = r16
    //     0xe86d04: stur            w16, [x2, #0xf]
    // 0xe86d08: r1 = 174
    //     0xe86d08: movz            x1, #0xae
    // 0xe86d0c: add             x2, x0, w1, sxtw #1
    // 0xe86d10: r16 = 418
    //     0xe86d10: movz            x16, #0x1a2
    // 0xe86d14: StoreField: r2->field_f = r16
    //     0xe86d14: stur            w16, [x2, #0xf]
    // 0xe86d18: r1 = 176
    //     0xe86d18: movz            x1, #0xb0
    // 0xe86d1c: add             x2, x0, w1, sxtw #1
    // 0xe86d20: r16 = 132
    //     0xe86d20: movz            x16, #0x84
    // 0xe86d24: StoreField: r2->field_f = r16
    //     0xe86d24: stur            w16, [x2, #0xf]
    // 0xe86d28: r1 = 178
    //     0xe86d28: movz            x1, #0xb2
    // 0xe86d2c: add             x2, x0, w1, sxtw #1
    // 0xe86d30: r16 = 122
    //     0xe86d30: movz            x16, #0x7a
    // 0xe86d34: StoreField: r2->field_f = r16
    //     0xe86d34: stur            w16, [x2, #0xf]
    // 0xe86d38: r1 = 180
    //     0xe86d38: movz            x1, #0xb4
    // 0xe86d3c: add             x2, x0, w1, sxtw #1
    // 0xe86d40: r16 = 424
    //     0xe86d40: movz            x16, #0x1a8
    // 0xe86d44: StoreField: r2->field_f = r16
    //     0xe86d44: stur            w16, [x2, #0xf]
    // 0xe86d48: r1 = 182
    //     0xe86d48: movz            x1, #0xb6
    // 0xe86d4c: add             x2, x0, w1, sxtw #1
    // 0xe86d50: r16 = 96
    //     0xe86d50: movz            x16, #0x60
    // 0xe86d54: StoreField: r2->field_f = r16
    //     0xe86d54: stur            w16, [x2, #0xf]
    // 0xe86d58: r1 = 184
    //     0xe86d58: movz            x1, #0xb8
    // 0xe86d5c: add             x2, x0, w1, sxtw #1
    // 0xe86d60: r16 = 326
    //     0xe86d60: movz            x16, #0x146
    // 0xe86d64: StoreField: r2->field_f = r16
    //     0xe86d64: stur            w16, [x2, #0xf]
    // 0xe86d68: r1 = 186
    //     0xe86d68: movz            x1, #0xba
    // 0xe86d6c: add             x2, x0, w1, sxtw #1
    // 0xe86d70: r16 = 120
    //     0xe86d70: movz            x16, #0x78
    // 0xe86d74: StoreField: r2->field_f = r16
    //     0xe86d74: stur            w16, [x2, #0xf]
    // 0xe86d78: r1 = 188
    //     0xe86d78: movz            x1, #0xbc
    // 0xe86d7c: add             x2, x0, w1, sxtw #1
    // 0xe86d80: r16 = 364
    //     0xe86d80: movz            x16, #0x16c
    // 0xe86d84: StoreField: r2->field_f = r16
    //     0xe86d84: stur            w16, [x2, #0xf]
    // 0xe86d88: r1 = 190
    //     0xe86d88: movz            x1, #0xbe
    // 0xe86d8c: add             x2, x0, w1, sxtw #1
    // 0xe86d90: r16 = 76
    //     0xe86d90: movz            x16, #0x4c
    // 0xe86d94: StoreField: r2->field_f = r16
    //     0xe86d94: stur            w16, [x2, #0xf]
    // 0xe86d98: r1 = 192
    //     0xe86d98: movz            x1, #0xc0
    // 0xe86d9c: add             x2, x0, w1, sxtw #1
    // 0xe86da0: r16 = 222
    //     0xe86da0: movz            x16, #0xde
    // 0xe86da4: StoreField: r2->field_f = r16
    //     0xe86da4: stur            w16, [x2, #0xf]
    // 0xe86da8: r1 = 194
    //     0xe86da8: movz            x1, #0xc2
    // 0xe86dac: add             x2, x0, w1, sxtw #1
    // 0xe86db0: r16 = 382
    //     0xe86db0: movz            x16, #0x17e
    // 0xe86db4: StoreField: r2->field_f = r16
    //     0xe86db4: stur            w16, [x2, #0xf]
    // 0xe86db8: r1 = 196
    //     0xe86db8: movz            x1, #0xc4
    // 0xe86dbc: add             x2, x0, w1, sxtw #1
    // 0xe86dc0: r16 = 28
    //     0xe86dc0: movz            x16, #0x1c
    // 0xe86dc4: StoreField: r2->field_f = r16
    //     0xe86dc4: stur            w16, [x2, #0xf]
    // 0xe86dc8: r1 = 198
    //     0xe86dc8: movz            x1, #0xc6
    // 0xe86dcc: add             x2, x0, w1, sxtw #1
    // 0xe86dd0: r16 = 436
    //     0xe86dd0: movz            x16, #0x1b4
    // 0xe86dd4: StoreField: r2->field_f = r16
    //     0xe86dd4: stur            w16, [x2, #0xf]
    // 0xe86dd8: r1 = 200
    //     0xe86dd8: movz            x1, #0xc8
    // 0xe86ddc: add             x2, x0, w1, sxtw #1
    // 0xe86de0: r16 = 140
    //     0xe86de0: movz            x16, #0x8c
    // 0xe86de4: StoreField: r2->field_f = r16
    //     0xe86de4: stur            w16, [x2, #0xf]
    // 0xe86de8: r1 = 202
    //     0xe86de8: movz            x1, #0xca
    // 0xe86dec: add             x2, x0, w1, sxtw #1
    // 0xe86df0: r16 = 210
    //     0xe86df0: movz            x16, #0xd2
    // 0xe86df4: StoreField: r2->field_f = r16
    //     0xe86df4: stur            w16, [x2, #0xf]
    // 0xe86df8: r1 = 204
    //     0xe86df8: movz            x1, #0xcc
    // 0xe86dfc: add             x2, x0, w1, sxtw #1
    // 0xe86e00: r16 = 14
    //     0xe86e00: movz            x16, #0xe
    // 0xe86e04: StoreField: r2->field_f = r16
    //     0xe86e04: stur            w16, [x2, #0xf]
    // 0xe86e08: r1 = 206
    //     0xe86e08: movz            x1, #0xce
    // 0xe86e0c: add             x2, x0, w1, sxtw #1
    // 0xe86e10: r16 = 174
    //     0xe86e10: movz            x16, #0xae
    // 0xe86e14: StoreField: r2->field_f = r16
    //     0xe86e14: stur            w16, [x2, #0xf]
    // 0xe86e18: r1 = 208
    //     0xe86e18: movz            x1, #0xd0
    // 0xe86e1c: add             x2, x0, w1, sxtw #1
    // 0xe86e20: r16 = 78
    //     0xe86e20: movz            x16, #0x4e
    // 0xe86e24: StoreField: r2->field_f = r16
    //     0xe86e24: stur            w16, [x2, #0xf]
    // 0xe86e28: r1 = 210
    //     0xe86e28: movz            x1, #0xd2
    // 0xe86e2c: add             x2, x0, w1, sxtw #1
    // 0xe86e30: r16 = 484
    //     0xe86e30: movz            x16, #0x1e4
    // 0xe86e34: StoreField: r2->field_f = r16
    //     0xe86e34: stur            w16, [x2, #0xf]
    // 0xe86e38: r1 = 212
    //     0xe86e38: movz            x1, #0xd4
    // 0xe86e3c: add             x2, x0, w1, sxtw #1
    // 0xe86e40: r16 = 58
    //     0xe86e40: movz            x16, #0x3a
    // 0xe86e44: StoreField: r2->field_f = r16
    //     0xe86e44: stur            w16, [x2, #0xf]
    // 0xe86e48: r1 = 214
    //     0xe86e48: movz            x1, #0xd6
    // 0xe86e4c: add             x2, x0, w1, sxtw #1
    // 0xe86e50: r16 = 310
    //     0xe86e50: movz            x16, #0x136
    // 0xe86e54: StoreField: r2->field_f = r16
    //     0xe86e54: stur            w16, [x2, #0xf]
    // 0xe86e58: r1 = 216
    //     0xe86e58: movz            x1, #0xd8
    // 0xe86e5c: add             x2, x0, w1, sxtw #1
    // 0xe86e60: r16 = 376
    //     0xe86e60: movz            x16, #0x178
    // 0xe86e64: StoreField: r2->field_f = r16
    //     0xe86e64: stur            w16, [x2, #0xf]
    // 0xe86e68: r1 = 218
    //     0xe86e68: movz            x1, #0xda
    // 0xe86e6c: add             x2, x0, w1, sxtw #1
    // 0xe86e70: r16 = 296
    //     0xe86e70: movz            x16, #0x128
    // 0xe86e74: StoreField: r2->field_f = r16
    //     0xe86e74: stur            w16, [x2, #0xf]
    // 0xe86e78: r1 = 220
    //     0xe86e78: movz            x1, #0xdc
    // 0xe86e7c: add             x2, x0, w1, sxtw #1
    // 0xe86e80: r16 = 134
    //     0xe86e80: movz            x16, #0x86
    // 0xe86e84: StoreField: r2->field_f = r16
    //     0xe86e84: stur            w16, [x2, #0xf]
    // 0xe86e88: r1 = 222
    //     0xe86e88: movz            x1, #0xde
    // 0xe86e8c: add             x2, x0, w1, sxtw #1
    // 0xe86e90: r16 = 6
    //     0xe86e90: movz            x16, #0x6
    // 0xe86e94: StoreField: r2->field_f = r16
    //     0xe86e94: stur            w16, [x2, #0xf]
    // 0xe86e98: r1 = 224
    //     0xe86e98: movz            x1, #0xe0
    // 0xe86e9c: add             x2, x0, w1, sxtw #1
    // 0xe86ea0: r16 = 496
    //     0xe86ea0: movz            x16, #0x1f0
    // 0xe86ea4: StoreField: r2->field_f = r16
    //     0xe86ea4: stur            w16, [x2, #0xf]
    // 0xe86ea8: r1 = 226
    //     0xe86ea8: movz            x1, #0xe2
    // 0xe86eac: add             x2, x0, w1, sxtw #1
    // 0xe86eb0: r16 = 34
    //     0xe86eb0: movz            x16, #0x22
    // 0xe86eb4: StoreField: r2->field_f = r16
    //     0xe86eb4: stur            w16, [x2, #0xf]
    // 0xe86eb8: r1 = 228
    //     0xe86eb8: movz            x1, #0xe4
    // 0xe86ebc: add             x2, x0, w1, sxtw #1
    // 0xe86ec0: r16 = 398
    //     0xe86ec0: movz            x16, #0x18e
    // 0xe86ec4: StoreField: r2->field_f = r16
    //     0xe86ec4: stur            w16, [x2, #0xf]
    // 0xe86ec8: r1 = 230
    //     0xe86ec8: movz            x1, #0xe6
    // 0xe86ecc: add             x2, x0, w1, sxtw #1
    // 0xe86ed0: r16 = 492
    //     0xe86ed0: movz            x16, #0x1ec
    // 0xe86ed4: StoreField: r2->field_f = r16
    //     0xe86ed4: stur            w16, [x2, #0xf]
    // 0xe86ed8: r1 = 232
    //     0xe86ed8: movz            x1, #0xe8
    // 0xe86edc: add             x2, x0, w1, sxtw #1
    // 0xe86ee0: r16 = 288
    //     0xe86ee0: movz            x16, #0x120
    // 0xe86ee4: StoreField: r2->field_f = r16
    //     0xe86ee4: stur            w16, [x2, #0xf]
    // 0xe86ee8: r1 = 234
    //     0xe86ee8: movz            x1, #0xea
    // 0xe86eec: add             x2, x0, w1, sxtw #1
    // 0xe86ef0: r16 = 478
    //     0xe86ef0: movz            x16, #0x1de
    // 0xe86ef4: StoreField: r2->field_f = r16
    //     0xe86ef4: stur            w16, [x2, #0xf]
    // 0xe86ef8: r1 = 236
    //     0xe86ef8: movz            x1, #0xec
    // 0xe86efc: add             x2, x0, w1, sxtw #1
    // 0xe86f00: r16 = 124
    //     0xe86f00: movz            x16, #0x7c
    // 0xe86f04: StoreField: r2->field_f = r16
    //     0xe86f04: stur            w16, [x2, #0xf]
    // 0xe86f08: r1 = 238
    //     0xe86f08: movz            x1, #0xee
    // 0xe86f0c: add             x2, x0, w1, sxtw #1
    // 0xe86f10: r16 = 462
    //     0xe86f10: movz            x16, #0x1ce
    // 0xe86f14: StoreField: r2->field_f = r16
    //     0xe86f14: stur            w16, [x2, #0xf]
    // 0xe86f18: r1 = 240
    //     0xe86f18: movz            x1, #0xf0
    // 0xe86f1c: add             x2, x0, w1, sxtw #1
    // 0xe86f20: r16 = 12
    //     0xe86f20: movz            x16, #0xc
    // 0xe86f24: StoreField: r2->field_f = r16
    //     0xe86f24: stur            w16, [x2, #0xf]
    // 0xe86f28: r1 = 242
    //     0xe86f28: movz            x1, #0xf2
    // 0xe86f2c: add             x2, x0, w1, sxtw #1
    // 0xe86f30: r16 = 390
    //     0xe86f30: movz            x16, #0x186
    // 0xe86f34: StoreField: r2->field_f = r16
    //     0xe86f34: stur            w16, [x2, #0xf]
    // 0xe86f38: r1 = 244
    //     0xe86f38: movz            x1, #0xf4
    // 0xe86f3c: add             x2, x0, w1, sxtw #1
    // 0xe86f40: r16 = 426
    //     0xe86f40: movz            x16, #0x1aa
    // 0xe86f44: StoreField: r2->field_f = r16
    //     0xe86f44: stur            w16, [x2, #0xf]
    // 0xe86f48: r1 = 246
    //     0xe86f48: movz            x1, #0xf6
    // 0xe86f4c: add             x2, x0, w1, sxtw #1
    // 0xe86f50: r16 = 94
    //     0xe86f50: movz            x16, #0x5e
    // 0xe86f54: StoreField: r2->field_f = r16
    //     0xe86f54: stur            w16, [x2, #0xf]
    // 0xe86f58: r1 = 248
    //     0xe86f58: movz            x1, #0xf8
    // 0xe86f5c: add             x2, x0, w1, sxtw #1
    // 0xe86f60: r16 = 400
    //     0xe86f60: movz            x16, #0x190
    // 0xe86f64: StoreField: r2->field_f = r16
    //     0xe86f64: stur            w16, [x2, #0xf]
    // 0xe86f68: r1 = 250
    //     0xe86f68: movz            x1, #0xfa
    // 0xe86f6c: add             x2, x0, w1, sxtw #1
    // 0xe86f70: r16 = 204
    //     0xe86f70: movz            x16, #0xcc
    // 0xe86f74: StoreField: r2->field_f = r16
    //     0xe86f74: stur            w16, [x2, #0xf]
    // 0xe86f78: r1 = 252
    //     0xe86f78: movz            x1, #0xfc
    // 0xe86f7c: add             x2, x0, w1, sxtw #1
    // 0xe86f80: r16 = 60
    //     0xe86f80: movz            x16, #0x3c
    // 0xe86f84: StoreField: r2->field_f = r16
    //     0xe86f84: stur            w16, [x2, #0xf]
    // 0xe86f88: r1 = 254
    //     0xe86f88: movz            x1, #0xfe
    // 0xe86f8c: add             x2, x0, w1, sxtw #1
    // 0xe86f90: r16 = 430
    //     0xe86f90: movz            x16, #0x1ae
    // 0xe86f94: StoreField: r2->field_f = r16
    //     0xe86f94: stur            w16, [x2, #0xf]
    // 0xe86f98: r1 = 256
    //     0xe86f98: movz            x1, #0x100
    // 0xe86f9c: add             x2, x0, w1, sxtw #1
    // 0xe86fa0: r16 = 16
    //     0xe86fa0: movz            x16, #0x10
    // 0xe86fa4: StoreField: r2->field_f = r16
    //     0xe86fa4: stur            w16, [x2, #0xf]
    // 0xe86fa8: r1 = 258
    //     0xe86fa8: movz            x1, #0x102
    // 0xe86fac: add             x2, x0, w1, sxtw #1
    // 0xe86fb0: r16 = 464
    //     0xe86fb0: movz            x16, #0x1d0
    // 0xe86fb4: StoreField: r2->field_f = r16
    //     0xe86fb4: stur            w16, [x2, #0xf]
    // 0xe86fb8: r1 = 260
    //     0xe86fb8: movz            x1, #0x104
    // 0xe86fbc: add             x2, x0, w1, sxtw #1
    // 0xe86fc0: r16 = 468
    //     0xe86fc0: movz            x16, #0x1d4
    // 0xe86fc4: StoreField: r2->field_f = r16
    //     0xe86fc4: stur            w16, [x2, #0xf]
    // 0xe86fc8: r1 = 262
    //     0xe86fc8: movz            x1, #0x106
    // 0xe86fcc: add             x2, x0, w1, sxtw #1
    // 0xe86fd0: r16 = 444
    //     0xe86fd0: movz            x16, #0x1bc
    // 0xe86fd4: StoreField: r2->field_f = r16
    //     0xe86fd4: stur            w16, [x2, #0xf]
    // 0xe86fd8: r1 = 264
    //     0xe86fd8: movz            x1, #0x108
    // 0xe86fdc: add             x2, x0, w1, sxtw #1
    // 0xe86fe0: r16 = 256
    //     0xe86fe0: movz            x16, #0x100
    // 0xe86fe4: StoreField: r2->field_f = r16
    //     0xe86fe4: stur            w16, [x2, #0xf]
    // 0xe86fe8: r1 = 266
    //     0xe86fe8: movz            x1, #0x10a
    // 0xe86fec: add             x2, x0, w1, sxtw #1
    // 0xe86ff0: r16 = 164
    //     0xe86ff0: movz            x16, #0xa4
    // 0xe86ff4: StoreField: r2->field_f = r16
    //     0xe86ff4: stur            w16, [x2, #0xf]
    // 0xe86ff8: r1 = 268
    //     0xe86ff8: movz            x1, #0x10c
    // 0xe86ffc: add             x2, x0, w1, sxtw #1
    // 0xe87000: r16 = 476
    //     0xe87000: movz            x16, #0x1dc
    // 0xe87004: StoreField: r2->field_f = r16
    //     0xe87004: stur            w16, [x2, #0xf]
    // 0xe87008: r1 = 270
    //     0xe87008: movz            x1, #0x10e
    // 0xe8700c: add             x2, x0, w1, sxtw #1
    // 0xe87010: r16 = 494
    //     0xe87010: movz            x16, #0x1ee
    // 0xe87014: StoreField: r2->field_f = r16
    //     0xe87014: stur            w16, [x2, #0xf]
    // 0xe87018: r1 = 272
    //     0xe87018: movz            x1, #0x110
    // 0xe8701c: add             x2, x0, w1, sxtw #1
    // 0xe87020: r16 = 264
    //     0xe87020: movz            x16, #0x108
    // 0xe87024: StoreField: r2->field_f = r16
    //     0xe87024: stur            w16, [x2, #0xf]
    // 0xe87028: r1 = 274
    //     0xe87028: movz            x1, #0x112
    // 0xe8702c: add             x2, x0, w1, sxtw #1
    // 0xe87030: r16 = 340
    //     0xe87030: movz            x16, #0x154
    // 0xe87034: StoreField: r2->field_f = r16
    //     0xe87034: stur            w16, [x2, #0xf]
    // 0xe87038: r1 = 276
    //     0xe87038: movz            x1, #0x114
    // 0xe8703c: add             x2, x0, w1, sxtw #1
    // 0xe87040: r16 = 228
    //     0xe87040: movz            x16, #0xe4
    // 0xe87044: StoreField: r2->field_f = r16
    //     0xe87044: stur            w16, [x2, #0xf]
    // 0xe87048: r1 = 278
    //     0xe87048: movz            x1, #0x116
    // 0xe8704c: add             x2, x0, w1, sxtw #1
    // 0xe87050: r16 = 344
    //     0xe87050: movz            x16, #0x158
    // 0xe87054: StoreField: r2->field_f = r16
    //     0xe87054: stur            w16, [x2, #0xf]
    // 0xe87058: r1 = 280
    //     0xe87058: movz            x1, #0x118
    // 0xe8705c: add             x2, x0, w1, sxtw #1
    // 0xe87060: r16 = 106
    //     0xe87060: movz            x16, #0x6a
    // 0xe87064: StoreField: r2->field_f = r16
    //     0xe87064: stur            w16, [x2, #0xf]
    // 0xe87068: r1 = 282
    //     0xe87068: movz            x1, #0x11a
    // 0xe8706c: add             x2, x0, w1, sxtw #1
    // 0xe87070: r16 = 154
    //     0xe87070: movz            x16, #0x9a
    // 0xe87074: StoreField: r2->field_f = r16
    //     0xe87074: stur            w16, [x2, #0xf]
    // 0xe87078: r1 = 284
    //     0xe87078: movz            x1, #0x11c
    // 0xe8707c: add             x2, x0, w1, sxtw #1
    // 0xe87080: r16 = 212
    //     0xe87080: movz            x16, #0xd4
    // 0xe87084: StoreField: r2->field_f = r16
    //     0xe87084: stur            w16, [x2, #0xf]
    // 0xe87088: r1 = 286
    //     0xe87088: movz            x1, #0x11e
    // 0xe8708c: add             x2, x0, w1, sxtw #1
    // 0xe87090: r16 = 84
    //     0xe87090: movz            x16, #0x54
    // 0xe87094: StoreField: r2->field_f = r16
    //     0xe87094: stur            w16, [x2, #0xf]
    // 0xe87098: r1 = 288
    //     0xe87098: movz            x1, #0x120
    // 0xe8709c: add             x2, x0, w1, sxtw #1
    // 0xe870a0: r16 = 300
    //     0xe870a0: movz            x16, #0x12c
    // 0xe870a4: StoreField: r2->field_f = r16
    //     0xe870a4: stur            w16, [x2, #0xf]
    // 0xe870a8: r1 = 290
    //     0xe870a8: movz            x1, #0x122
    // 0xe870ac: add             x2, x0, w1, sxtw #1
    // 0xe870b0: r16 = 52
    //     0xe870b0: movz            x16, #0x34
    // 0xe870b4: StoreField: r2->field_f = r16
    //     0xe870b4: stur            w16, [x2, #0xf]
    // 0xe870b8: r1 = 292
    //     0xe870b8: movz            x1, #0x124
    // 0xe870bc: add             x2, x0, w1, sxtw #1
    // 0xe870c0: r16 = 420
    //     0xe870c0: movz            x16, #0x1a4
    // 0xe870c4: StoreField: r2->field_f = r16
    //     0xe870c4: stur            w16, [x2, #0xf]
    // 0xe870c8: r1 = 294
    //     0xe870c8: movz            x1, #0x126
    // 0xe870cc: add             x2, x0, w1, sxtw #1
    // 0xe870d0: r16 = 226
    //     0xe870d0: movz            x16, #0xe2
    // 0xe870d4: StoreField: r2->field_f = r16
    //     0xe870d4: stur            w16, [x2, #0xf]
    // 0xe870d8: r1 = 296
    //     0xe870d8: movz            x1, #0x128
    // 0xe870dc: add             x2, x0, w1, sxtw #1
    // 0xe870e0: r16 = 180
    //     0xe870e0: movz            x16, #0xb4
    // 0xe870e4: StoreField: r2->field_f = r16
    //     0xe870e4: stur            w16, [x2, #0xf]
    // 0xe870e8: r1 = 298
    //     0xe870e8: movz            x1, #0x12a
    // 0xe870ec: add             x2, x0, w1, sxtw #1
    // 0xe870f0: r16 = 42
    //     0xe870f0: movz            x16, #0x2a
    // 0xe870f4: StoreField: r2->field_f = r16
    //     0xe870f4: stur            w16, [x2, #0xf]
    // 0xe870f8: r1 = 300
    //     0xe870f8: movz            x1, #0x12c
    // 0xe870fc: add             x2, x0, w1, sxtw #1
    // 0xe87100: r16 = 146
    //     0xe87100: movz            x16, #0x92
    // 0xe87104: StoreField: r2->field_f = r16
    //     0xe87104: stur            w16, [x2, #0xf]
    // 0xe87108: r1 = 302
    //     0xe87108: movz            x1, #0x12e
    // 0xe8710c: add             x2, x0, w1, sxtw #1
    // 0xe87110: r16 = 232
    //     0xe87110: movz            x16, #0xe8
    // 0xe87114: StoreField: r2->field_f = r16
    //     0xe87114: stur            w16, [x2, #0xf]
    // 0xe87118: r1 = 304
    //     0xe87118: movz            x1, #0x130
    // 0xe8711c: add             x2, x0, w1, sxtw #1
    // 0xe87120: r16 = 150
    //     0xe87120: movz            x16, #0x96
    // 0xe87124: StoreField: r2->field_f = r16
    //     0xe87124: stur            w16, [x2, #0xf]
    // 0xe87128: r1 = 306
    //     0xe87128: movz            x1, #0x132
    // 0xe8712c: add             x2, x0, w1, sxtw #1
    // 0xe87130: r16 = 318
    //     0xe87130: movz            x16, #0x13e
    // 0xe87134: StoreField: r2->field_f = r16
    //     0xe87134: stur            w16, [x2, #0xf]
    // 0xe87138: r1 = 308
    //     0xe87138: movz            x1, #0x134
    // 0xe8713c: add             x2, x0, w1, sxtw #1
    // 0xe87140: r16 = 416
    //     0xe87140: movz            x16, #0x1a0
    // 0xe87144: StoreField: r2->field_f = r16
    //     0xe87144: stur            w16, [x2, #0xf]
    // 0xe87148: r1 = 310
    //     0xe87148: movz            x1, #0x136
    // 0xe8714c: add             x2, x0, w1, sxtw #1
    // 0xe87150: r16 = 188
    //     0xe87150: movz            x16, #0xbc
    // 0xe87154: StoreField: r2->field_f = r16
    //     0xe87154: stur            w16, [x2, #0xf]
    // 0xe87158: r1 = 312
    //     0xe87158: movz            x1, #0x138
    // 0xe8715c: add             x2, x0, w1, sxtw #1
    // 0xe87160: r16 = 8
    //     0xe87160: movz            x16, #0x8
    // 0xe87164: StoreField: r2->field_f = r16
    //     0xe87164: stur            w16, [x2, #0xf]
    // 0xe87168: r1 = 314
    //     0xe87168: movz            x1, #0x13a
    // 0xe8716c: add             x2, x0, w1, sxtw #1
    // 0xe87170: r16 = 48
    //     0xe87170: movz            x16, #0x30
    // 0xe87174: StoreField: r2->field_f = r16
    //     0xe87174: stur            w16, [x2, #0xf]
    // 0xe87178: r1 = 316
    //     0xe87178: movz            x1, #0x13c
    // 0xe8717c: add             x2, x0, w1, sxtw #1
    // 0xe87180: r16 = 328
    //     0xe87180: movz            x16, #0x148
    // 0xe87184: StoreField: r2->field_f = r16
    //     0xe87184: stur            w16, [x2, #0xf]
    // 0xe87188: r1 = 318
    //     0xe87188: movz            x1, #0x13e
    // 0xe8718c: add             x2, x0, w1, sxtw #1
    // 0xe87190: r16 = 472
    //     0xe87190: movz            x16, #0x1d8
    // 0xe87194: StoreField: r2->field_f = r16
    //     0xe87194: stur            w16, [x2, #0xf]
    // 0xe87198: r1 = 320
    //     0xe87198: movz            x1, #0x140
    // 0xe8719c: add             x2, x0, w1, sxtw #1
    // 0xe871a0: r16 = 388
    //     0xe871a0: movz            x16, #0x184
    // 0xe871a4: StoreField: r2->field_f = r16
    //     0xe871a4: stur            w16, [x2, #0xf]
    // 0xe871a8: r1 = 322
    //     0xe871a8: movz            x1, #0x142
    // 0xe871ac: add             x2, x0, w1, sxtw #1
    // 0xe871b0: r16 = 448
    //     0xe871b0: movz            x16, #0x1c0
    // 0xe871b4: StoreField: r2->field_f = r16
    //     0xe871b4: stur            w16, [x2, #0xf]
    // 0xe871b8: r1 = 324
    //     0xe871b8: movz            x1, #0x144
    // 0xe871bc: add             x2, x0, w1, sxtw #1
    // 0xe871c0: r16 = 130
    //     0xe871c0: movz            x16, #0x82
    // 0xe871c4: StoreField: r2->field_f = r16
    //     0xe871c4: stur            w16, [x2, #0xf]
    // 0xe871c8: r1 = 326
    //     0xe871c8: movz            x1, #0x146
    // 0xe871cc: add             x2, x0, w1, sxtw #1
    // 0xe871d0: r16 = 220
    //     0xe871d0: movz            x16, #0xdc
    // 0xe871d4: StoreField: r2->field_f = r16
    //     0xe871d4: stur            w16, [x2, #0xf]
    // 0xe871d8: r1 = 328
    //     0xe871d8: movz            x1, #0x148
    // 0xe871dc: add             x2, x0, w1, sxtw #1
    // 0xe871e0: r16 = 30
    //     0xe871e0: movz            x16, #0x1e
    // 0xe871e4: StoreField: r2->field_f = r16
    //     0xe871e4: stur            w16, [x2, #0xf]
    // 0xe871e8: r1 = 330
    //     0xe871e8: movz            x1, #0x14a
    // 0xe871ec: add             x2, x0, w1, sxtw #1
    // 0xe871f0: r16 = 162
    //     0xe871f0: movz            x16, #0xa2
    // 0xe871f4: StoreField: r2->field_f = r16
    //     0xe871f4: stur            w16, [x2, #0xf]
    // 0xe871f8: r1 = 332
    //     0xe871f8: movz            x1, #0x14c
    // 0xe871fc: add             x2, x0, w1, sxtw #1
    // 0xe87200: r16 = 406
    //     0xe87200: movz            x16, #0x196
    // 0xe87204: StoreField: r2->field_f = r16
    //     0xe87204: stur            w16, [x2, #0xf]
    // 0xe87208: r1 = 334
    //     0xe87208: movz            x1, #0x14e
    // 0xe8720c: add             x2, x0, w1, sxtw #1
    // 0xe87210: r16 = 408
    //     0xe87210: movz            x16, #0x198
    // 0xe87214: StoreField: r2->field_f = r16
    //     0xe87214: stur            w16, [x2, #0xf]
    // 0xe87218: r1 = 336
    //     0xe87218: movz            x1, #0x150
    // 0xe8721c: add             x2, x0, w1, sxtw #1
    // 0xe87220: r16 = 72
    //     0xe87220: movz            x16, #0x48
    // 0xe87224: StoreField: r2->field_f = r16
    //     0xe87224: stur            w16, [x2, #0xf]
    // 0xe87228: r1 = 338
    //     0xe87228: movz            x1, #0x152
    // 0xe8722c: add             x2, x0, w1, sxtw #1
    // 0xe87230: r16 = 290
    //     0xe87230: movz            x16, #0x122
    // 0xe87234: StoreField: r2->field_f = r16
    //     0xe87234: stur            w16, [x2, #0xf]
    // 0xe87238: r1 = 340
    //     0xe87238: movz            x1, #0x154
    // 0xe8723c: add             x2, x0, w1, sxtw #1
    // 0xe87240: r16 = 350
    //     0xe87240: movz            x16, #0x15e
    // 0xe87244: StoreField: r2->field_f = r16
    //     0xe87244: stur            w16, [x2, #0xf]
    // 0xe87248: r1 = 342
    //     0xe87248: movz            x1, #0x156
    // 0xe8724c: add             x2, x0, w1, sxtw #1
    // 0xe87250: r16 = 160
    //     0xe87250: movz            x16, #0xa0
    // 0xe87254: StoreField: r2->field_f = r16
    //     0xe87254: stur            w16, [x2, #0xf]
    // 0xe87258: r1 = 344
    //     0xe87258: movz            x1, #0x158
    // 0xe8725c: add             x2, x0, w1, sxtw #1
    // 0xe87260: r16 = 322
    //     0xe87260: movz            x16, #0x142
    // 0xe87264: StoreField: r2->field_f = r16
    //     0xe87264: stur            w16, [x2, #0xf]
    // 0xe87268: r1 = 346
    //     0xe87268: movz            x1, #0x15a
    // 0xe8726c: add             x2, x0, w1, sxtw #1
    // 0xe87270: r16 = 488
    //     0xe87270: movz            x16, #0x1e8
    // 0xe87274: StoreField: r2->field_f = r16
    //     0xe87274: stur            w16, [x2, #0xf]
    // 0xe87278: r1 = 348
    //     0xe87278: movz            x1, #0x15c
    // 0xe8727c: add             x2, x0, w1, sxtw #1
    // 0xe87280: r16 = 224
    //     0xe87280: movz            x16, #0xe0
    // 0xe87284: StoreField: r2->field_f = r16
    //     0xe87284: stur            w16, [x2, #0xf]
    // 0xe87288: r1 = 350
    //     0xe87288: movz            x1, #0x15e
    // 0xe8728c: add             x2, x0, w1, sxtw #1
    // 0xe87290: r16 = 114
    //     0xe87290: movz            x16, #0x72
    // 0xe87294: StoreField: r2->field_f = r16
    //     0xe87294: stur            w16, [x2, #0xf]
    // 0xe87298: r1 = 352
    //     0xe87298: movz            x1, #0x160
    // 0xe8729c: add             x2, x0, w1, sxtw #1
    // 0xe872a0: r16 = 306
    //     0xe872a0: movz            x16, #0x132
    // 0xe872a4: StoreField: r2->field_f = r16
    //     0xe872a4: stur            w16, [x2, #0xf]
    // 0xe872a8: r1 = 354
    //     0xe872a8: movz            x1, #0x162
    // 0xe872ac: add             x2, x0, w1, sxtw #1
    // 0xe872b0: r16 = 248
    //     0xe872b0: movz            x16, #0xf8
    // 0xe872b4: StoreField: r2->field_f = r16
    //     0xe872b4: stur            w16, [x2, #0xf]
    // 0xe872b8: r1 = 356
    //     0xe872b8: movz            x1, #0x164
    // 0xe872bc: add             x2, x0, w1, sxtw #1
    // 0xe872c0: r16 = 116
    //     0xe872c0: movz            x16, #0x74
    // 0xe872c4: StoreField: r2->field_f = r16
    //     0xe872c4: stur            w16, [x2, #0xf]
    // 0xe872c8: r1 = 358
    //     0xe872c8: movz            x1, #0x166
    // 0xe872cc: add             x2, x0, w1, sxtw #1
    // 0xe872d0: r16 = 266
    //     0xe872d0: movz            x16, #0x10a
    // 0xe872d4: StoreField: r2->field_f = r16
    //     0xe872d4: stur            w16, [x2, #0xf]
    // 0xe872d8: r1 = 360
    //     0xe872d8: movz            x1, #0x168
    // 0xe872dc: add             x2, x0, w1, sxtw #1
    // 0xe872e0: r16 = 70
    //     0xe872e0: movz            x16, #0x46
    // 0xe872e4: StoreField: r2->field_f = r16
    //     0xe872e4: stur            w16, [x2, #0xf]
    // 0xe872e8: r1 = 362
    //     0xe872e8: movz            x1, #0x16a
    // 0xe872ec: add             x2, x0, w1, sxtw #1
    // 0xe872f0: r16 = 368
    //     0xe872f0: movz            x16, #0x170
    // 0xe872f4: StoreField: r2->field_f = r16
    //     0xe872f4: stur            w16, [x2, #0xf]
    // 0xe872f8: r1 = 364
    //     0xe872f8: movz            x1, #0x16c
    // 0xe872fc: add             x2, x0, w1, sxtw #1
    // 0xe87300: r16 = 360
    //     0xe87300: movz            x16, #0x168
    // 0xe87304: StoreField: r2->field_f = r16
    //     0xe87304: stur            w16, [x2, #0xf]
    // 0xe87308: r1 = 366
    //     0xe87308: movz            x1, #0x16e
    // 0xe8730c: add             x2, x0, w1, sxtw #1
    // 0xe87310: r16 = 244
    //     0xe87310: movz            x16, #0xf4
    // 0xe87314: StoreField: r2->field_f = r16
    //     0xe87314: stur            w16, [x2, #0xf]
    // 0xe87318: r1 = 368
    //     0xe87318: movz            x1, #0x170
    // 0xe8731c: add             x2, x0, w1, sxtw #1
    // 0xe87320: r16 = 504
    //     0xe87320: movz            x16, #0x1f8
    // 0xe87324: StoreField: r2->field_f = r16
    //     0xe87324: stur            w16, [x2, #0xf]
    // 0xe87328: r1 = 370
    //     0xe87328: movz            x1, #0x172
    // 0xe8732c: add             x2, x0, w1, sxtw #1
    // 0xe87330: r16 = 4
    //     0xe87330: movz            x16, #0x4
    // 0xe87334: StoreField: r2->field_f = r16
    //     0xe87334: stur            w16, [x2, #0xf]
    // 0xe87338: r1 = 372
    //     0xe87338: movz            x1, #0x174
    // 0xe8733c: add             x2, x0, w1, sxtw #1
    // 0xe87340: r16 = 108
    //     0xe87340: movz            x16, #0x6c
    // 0xe87344: StoreField: r2->field_f = r16
    //     0xe87344: stur            w16, [x2, #0xf]
    // 0xe87348: r1 = 374
    //     0xe87348: movz            x1, #0x176
    // 0xe8734c: add             x2, x0, w1, sxtw #1
    // 0xe87350: r16 = 182
    //     0xe87350: movz            x16, #0xb6
    // 0xe87354: StoreField: r2->field_f = r16
    //     0xe87354: stur            w16, [x2, #0xf]
    // 0xe87358: r1 = 376
    //     0xe87358: movz            x1, #0x178
    // 0xe8735c: add             x2, x0, w1, sxtw #1
    // 0xe87360: r16 = 74
    //     0xe87360: movz            x16, #0x4a
    // 0xe87364: StoreField: r2->field_f = r16
    //     0xe87364: stur            w16, [x2, #0xf]
    // 0xe87368: r1 = 378
    //     0xe87368: movz            x1, #0x17a
    // 0xe8736c: add             x2, x0, w1, sxtw #1
    // 0xe87370: r16 = 170
    //     0xe87370: movz            x16, #0xaa
    // 0xe87374: StoreField: r2->field_f = r16
    //     0xe87374: stur            w16, [x2, #0xf]
    // 0xe87378: r1 = 380
    //     0xe87378: movz            x1, #0x17c
    // 0xe8737c: add             x2, x0, w1, sxtw #1
    // 0xe87380: r16 = 302
    //     0xe87380: movz            x16, #0x12e
    // 0xe87384: StoreField: r2->field_f = r16
    //     0xe87384: stur            w16, [x2, #0xf]
    // 0xe87388: r1 = 382
    //     0xe87388: movz            x1, #0x17e
    // 0xe8738c: add             x2, x0, w1, sxtw #1
    // 0xe87390: r16 = 98
    //     0xe87390: movz            x16, #0x62
    // 0xe87394: StoreField: r2->field_f = r16
    //     0xe87394: stur            w16, [x2, #0xf]
    // 0xe87398: r1 = 384
    //     0xe87398: movz            x1, #0x180
    // 0xe8739c: add             x2, x0, w1, sxtw #1
    // 0xe873a0: r16 = 90
    //     0xe873a0: movz            x16, #0x5a
    // 0xe873a4: StoreField: r2->field_f = r16
    //     0xe873a4: stur            w16, [x2, #0xf]
    // 0xe873a8: r1 = 386
    //     0xe873a8: movz            x1, #0x182
    // 0xe873ac: add             x2, x0, w1, sxtw #1
    // 0xe873b0: r16 = 186
    //     0xe873b0: movz            x16, #0xba
    // 0xe873b4: StoreField: r2->field_f = r16
    //     0xe873b4: stur            w16, [x2, #0xf]
    // 0xe873b8: r1 = 388
    //     0xe873b8: movz            x1, #0x184
    // 0xe873bc: add             x2, x0, w1, sxtw #1
    // 0xe873c0: r16 = 500
    //     0xe873c0: movz            x16, #0x1f4
    // 0xe873c4: StoreField: r2->field_f = r16
    //     0xe873c4: stur            w16, [x2, #0xf]
    // 0xe873c8: r1 = 390
    //     0xe873c8: movz            x1, #0x186
    // 0xe873cc: add             x2, x0, w1, sxtw #1
    // 0xe873d0: r16 = 304
    //     0xe873d0: movz            x16, #0x130
    // 0xe873d4: StoreField: r2->field_f = r16
    //     0xe873d4: stur            w16, [x2, #0xf]
    // 0xe873d8: r1 = 392
    //     0xe873d8: movz            x1, #0x188
    // 0xe873dc: add             x2, x0, w1, sxtw #1
    // 0xe873e0: r16 = 454
    //     0xe873e0: movz            x16, #0x1c6
    // 0xe873e4: StoreField: r2->field_f = r16
    //     0xe873e4: stur            w16, [x2, #0xf]
    // 0xe873e8: r1 = 394
    //     0xe873e8: movz            x1, #0x18a
    // 0xe873ec: add             x2, x0, w1, sxtw #1
    // 0xe873f0: r16 = 276
    //     0xe873f0: movz            x16, #0x114
    // 0xe873f4: StoreField: r2->field_f = r16
    //     0xe873f4: stur            w16, [x2, #0xf]
    // 0xe873f8: r1 = 396
    //     0xe873f8: movz            x1, #0x18c
    // 0xe873fc: add             x2, x0, w1, sxtw #1
    // 0xe87400: r16 = 292
    //     0xe87400: movz            x16, #0x124
    // 0xe87404: StoreField: r2->field_f = r16
    //     0xe87404: stur            w16, [x2, #0xf]
    // 0xe87408: r1 = 398
    //     0xe87408: movz            x1, #0x18e
    // 0xe8740c: add             x2, x0, w1, sxtw #1
    // 0xe87410: r16 = 348
    //     0xe87410: movz            x16, #0x15c
    // 0xe87414: StoreField: r2->field_f = r16
    //     0xe87414: stur            w16, [x2, #0xf]
    // 0xe87418: r1 = 400
    //     0xe87418: movz            x1, #0x190
    // 0xe8741c: add             x2, x0, w1, sxtw #1
    // 0xe87420: r16 = 10
    //     0xe87420: movz            x16, #0xa
    // 0xe87424: StoreField: r2->field_f = r16
    //     0xe87424: stur            w16, [x2, #0xf]
    // 0xe87428: r1 = 402
    //     0xe87428: movz            x1, #0x192
    // 0xe8742c: add             x2, x0, w1, sxtw #1
    // 0xe87430: r16 = 446
    //     0xe87430: movz            x16, #0x1be
    // 0xe87434: StoreField: r2->field_f = r16
    //     0xe87434: stur            w16, [x2, #0xf]
    // 0xe87438: r1 = 404
    //     0xe87438: movz            x1, #0x194
    // 0xe8743c: add             x2, x0, w1, sxtw #1
    // 0xe87440: r16 = 82
    //     0xe87440: movz            x16, #0x52
    // 0xe87444: StoreField: r2->field_f = r16
    //     0xe87444: stur            w16, [x2, #0xf]
    // 0xe87448: r1 = 406
    //     0xe87448: movz            x1, #0x196
    // 0xe8744c: add             x2, x0, w1, sxtw #1
    // 0xe87450: r16 = 32
    //     0xe87450: movz            x16, #0x20
    // 0xe87454: StoreField: r2->field_f = r16
    //     0xe87454: stur            w16, [x2, #0xf]
    // 0xe87458: r1 = 408
    //     0xe87458: movz            x1, #0x198
    // 0xe8745c: add             x2, x0, w1, sxtw #1
    // 0xe87460: r16 = 206
    //     0xe87460: movz            x16, #0xce
    // 0xe87464: StoreField: r2->field_f = r16
    //     0xe87464: stur            w16, [x2, #0xf]
    // 0xe87468: r1 = 410
    //     0xe87468: movz            x1, #0x19a
    // 0xe8746c: add             x2, x0, w1, sxtw #1
    // 0xe87470: r16 = 216
    //     0xe87470: movz            x16, #0xd8
    // 0xe87474: StoreField: r2->field_f = r16
    //     0xe87474: stur            w16, [x2, #0xf]
    // 0xe87478: r1 = 412
    //     0xe87478: movz            x1, #0x19c
    // 0xe8747c: add             x2, x0, w1, sxtw #1
    // 0xe87480: r16 = 372
    //     0xe87480: movz            x16, #0x174
    // 0xe87484: StoreField: r2->field_f = r16
    //     0xe87484: stur            w16, [x2, #0xf]
    // 0xe87488: r1 = 414
    //     0xe87488: movz            x1, #0x19e
    // 0xe8748c: add             x2, x0, w1, sxtw #1
    // 0xe87490: r16 = 402
    //     0xe87490: movz            x16, #0x192
    // 0xe87494: StoreField: r2->field_f = r16
    //     0xe87494: stur            w16, [x2, #0xf]
    // 0xe87498: r1 = 416
    //     0xe87498: movz            x1, #0x1a0
    // 0xe8749c: add             x2, x0, w1, sxtw #1
    // 0xe874a0: r16 = 422
    //     0xe874a0: movz            x16, #0x1a6
    // 0xe874a4: StoreField: r2->field_f = r16
    //     0xe874a4: stur            w16, [x2, #0xf]
    // 0xe874a8: r1 = 418
    //     0xe874a8: movz            x1, #0x1a2
    // 0xe874ac: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0xe874ac: add             x2, x0, w1, sxtw #1
    //     0xe874b0: stur            wzr, [x2, #0xf]
    // 0xe874b4: r1 = 420
    //     0xe874b4: movz            x1, #0x1a4
    // 0xe874b8: add             x2, x0, w1, sxtw #1
    // 0xe874bc: r16 = 460
    //     0xe874bc: movz            x16, #0x1cc
    // 0xe874c0: StoreField: r2->field_f = r16
    //     0xe874c0: stur            w16, [x2, #0xf]
    // 0xe874c4: r1 = 422
    //     0xe874c4: movz            x1, #0x1a6
    // 0xe874c8: add             x2, x0, w1, sxtw #1
    // 0xe874cc: r16 = 414
    //     0xe874cc: movz            x16, #0x19e
    // 0xe874d0: StoreField: r2->field_f = r16
    //     0xe874d0: stur            w16, [x2, #0xf]
    // 0xe874d4: r1 = 424
    //     0xe874d4: movz            x1, #0x1a8
    // 0xe874d8: add             x2, x0, w1, sxtw #1
    // 0xe874dc: r16 = 450
    //     0xe874dc: movz            x16, #0x1c2
    // 0xe874e0: StoreField: r2->field_f = r16
    //     0xe874e0: stur            w16, [x2, #0xf]
    // 0xe874e4: r1 = 426
    //     0xe874e4: movz            x1, #0x1aa
    // 0xe874e8: add             x2, x0, w1, sxtw #1
    // 0xe874ec: r16 = 316
    //     0xe874ec: movz            x16, #0x13c
    // 0xe874f0: StoreField: r2->field_f = r16
    //     0xe874f0: stur            w16, [x2, #0xf]
    // 0xe874f4: r1 = 428
    //     0xe874f4: movz            x1, #0x1ac
    // 0xe874f8: add             x2, x0, w1, sxtw #1
    // 0xe874fc: r16 = 336
    //     0xe874fc: movz            x16, #0x150
    // 0xe87500: StoreField: r2->field_f = r16
    //     0xe87500: stur            w16, [x2, #0xf]
    // 0xe87504: r1 = 430
    //     0xe87504: movz            x1, #0x1ae
    // 0xe87508: add             x2, x0, w1, sxtw #1
    // 0xe8750c: r16 = 88
    //     0xe8750c: movz            x16, #0x58
    // 0xe87510: StoreField: r2->field_f = r16
    //     0xe87510: stur            w16, [x2, #0xf]
    // 0xe87514: r1 = 432
    //     0xe87514: movz            x1, #0x1b0
    // 0xe87518: add             x2, x0, w1, sxtw #1
    // 0xe8751c: r16 = 198
    //     0xe8751c: movz            x16, #0xc6
    // 0xe87520: StoreField: r2->field_f = r16
    //     0xe87520: stur            w16, [x2, #0xf]
    // 0xe87524: r1 = 434
    //     0xe87524: movz            x1, #0x1b2
    // 0xe87528: add             x2, x0, w1, sxtw #1
    // 0xe8752c: r16 = 44
    //     0xe8752c: movz            x16, #0x2c
    // 0xe87530: StoreField: r2->field_f = r16
    //     0xe87530: stur            w16, [x2, #0xf]
    // 0xe87534: r1 = 436
    //     0xe87534: movz            x1, #0x1b4
    // 0xe87538: add             x2, x0, w1, sxtw #1
    // 0xe8753c: r16 = 2
    //     0xe8753c: movz            x16, #0x2
    // 0xe87540: StoreField: r2->field_f = r16
    //     0xe87540: stur            w16, [x2, #0xf]
    // 0xe87544: r1 = 438
    //     0xe87544: movz            x1, #0x1b6
    // 0xe87548: add             x2, x0, w1, sxtw #1
    // 0xe8754c: r16 = 126
    //     0xe8754c: movz            x16, #0x7e
    // 0xe87550: StoreField: r2->field_f = r16
    //     0xe87550: stur            w16, [x2, #0xf]
    // 0xe87554: r1 = 440
    //     0xe87554: movz            x1, #0x1b8
    // 0xe87558: add             x2, x0, w1, sxtw #1
    // 0xe8755c: r16 = 176
    //     0xe8755c: movz            x16, #0xb0
    // 0xe87560: StoreField: r2->field_f = r16
    //     0xe87560: stur            w16, [x2, #0xf]
    // 0xe87564: r1 = 442
    //     0xe87564: movz            x1, #0x1ba
    // 0xe87568: add             x2, x0, w1, sxtw #1
    // 0xe8756c: r16 = 452
    //     0xe8756c: movz            x16, #0x1c4
    // 0xe87570: StoreField: r2->field_f = r16
    //     0xe87570: stur            w16, [x2, #0xf]
    // 0xe87574: r1 = 444
    //     0xe87574: movz            x1, #0x1bc
    // 0xe87578: add             x2, x0, w1, sxtw #1
    // 0xe8757c: r16 = 274
    //     0xe8757c: movz            x16, #0x112
    // 0xe87580: StoreField: r2->field_f = r16
    //     0xe87580: stur            w16, [x2, #0xf]
    // 0xe87584: r1 = 446
    //     0xe87584: movz            x1, #0x1be
    // 0xe87588: add             x2, x0, w1, sxtw #1
    // 0xe8758c: r16 = 338
    //     0xe8758c: movz            x16, #0x152
    // 0xe87590: StoreField: r2->field_f = r16
    //     0xe87590: stur            w16, [x2, #0xf]
    // 0xe87594: r1 = 448
    //     0xe87594: movz            x1, #0x1c0
    // 0xe87598: add             x2, x0, w1, sxtw #1
    // 0xe8759c: r16 = 26
    //     0xe8759c: movz            x16, #0x1a
    // 0xe875a0: StoreField: r2->field_f = r16
    //     0xe875a0: stur            w16, [x2, #0xf]
    // 0xe875a4: r1 = 450
    //     0xe875a4: movz            x1, #0x1c2
    // 0xe875a8: add             x2, x0, w1, sxtw #1
    // 0xe875ac: r16 = 112
    //     0xe875ac: movz            x16, #0x70
    // 0xe875b0: StoreField: r2->field_f = r16
    //     0xe875b0: stur            w16, [x2, #0xf]
    // 0xe875b4: r1 = 452
    //     0xe875b4: movz            x1, #0x1c4
    // 0xe875b8: add             x2, x0, w1, sxtw #1
    // 0xe875bc: r16 = 104
    //     0xe875bc: movz            x16, #0x68
    // 0xe875c0: StoreField: r2->field_f = r16
    //     0xe875c0: stur            w16, [x2, #0xf]
    // 0xe875c4: r1 = 454
    //     0xe875c4: movz            x1, #0x1c6
    // 0xe875c8: add             x2, x0, w1, sxtw #1
    // 0xe875cc: r16 = 54
    //     0xe875cc: movz            x16, #0x36
    // 0xe875d0: StoreField: r2->field_f = r16
    //     0xe875d0: stur            w16, [x2, #0xf]
    // 0xe875d4: r1 = 456
    //     0xe875d4: movz            x1, #0x1c8
    // 0xe875d8: add             x2, x0, w1, sxtw #1
    // 0xe875dc: r16 = 342
    //     0xe875dc: movz            x16, #0x156
    // 0xe875e0: StoreField: r2->field_f = r16
    //     0xe875e0: stur            w16, [x2, #0xf]
    // 0xe875e4: r1 = 458
    //     0xe875e4: movz            x1, #0x1ca
    // 0xe875e8: add             x2, x0, w1, sxtw #1
    // 0xe875ec: r16 = 102
    //     0xe875ec: movz            x16, #0x66
    // 0xe875f0: StoreField: r2->field_f = r16
    //     0xe875f0: stur            w16, [x2, #0xf]
    // 0xe875f4: r1 = 460
    //     0xe875f4: movz            x1, #0x1cc
    // 0xe875f8: add             x2, x0, w1, sxtw #1
    // 0xe875fc: r16 = 510
    //     0xe875fc: movz            x16, #0x1fe
    // 0xe87600: StoreField: r2->field_f = r16
    //     0xe87600: stur            w16, [x2, #0xf]
    // 0xe87604: r1 = 462
    //     0xe87604: movz            x1, #0x1ce
    // 0xe87608: add             x2, x0, w1, sxtw #1
    // 0xe8760c: r16 = 352
    //     0xe8760c: movz            x16, #0x160
    // 0xe87610: StoreField: r2->field_f = r16
    //     0xe87610: stur            w16, [x2, #0xf]
    // 0xe87614: r1 = 464
    //     0xe87614: movz            x1, #0x1d0
    // 0xe87618: add             x2, x0, w1, sxtw #1
    // 0xe8761c: r16 = 374
    //     0xe8761c: movz            x16, #0x176
    // 0xe87620: StoreField: r2->field_f = r16
    //     0xe87620: stur            w16, [x2, #0xf]
    // 0xe87624: r1 = 466
    //     0xe87624: movz            x1, #0x1d2
    // 0xe87628: add             x2, x0, w1, sxtw #1
    // 0xe8762c: r16 = 144
    //     0xe8762c: movz            x16, #0x90
    // 0xe87630: StoreField: r2->field_f = r16
    //     0xe87630: stur            w16, [x2, #0xf]
    // 0xe87634: r1 = 468
    //     0xe87634: movz            x1, #0x1d4
    // 0xe87638: add             x2, x0, w1, sxtw #1
    // 0xe8763c: r16 = 24
    //     0xe8763c: movz            x16, #0x18
    // 0xe87640: StoreField: r2->field_f = r16
    //     0xe87640: stur            w16, [x2, #0xf]
    // 0xe87644: r1 = 470
    //     0xe87644: movz            x1, #0x1d6
    // 0xe87648: add             x2, x0, w1, sxtw #1
    // 0xe8764c: r16 = 190
    //     0xe8764c: movz            x16, #0xbe
    // 0xe87650: StoreField: r2->field_f = r16
    //     0xe87650: stur            w16, [x2, #0xf]
    // 0xe87654: r1 = 472
    //     0xe87654: movz            x1, #0x1d8
    // 0xe87658: add             x2, x0, w1, sxtw #1
    // 0xe8765c: r16 = 370
    //     0xe8765c: movz            x16, #0x172
    // 0xe87660: StoreField: r2->field_f = r16
    //     0xe87660: stur            w16, [x2, #0xf]
    // 0xe87664: r1 = 474
    //     0xe87664: movz            x1, #0x1da
    // 0xe87668: add             x2, x0, w1, sxtw #1
    // 0xe8766c: r16 = 354
    //     0xe8766c: movz            x16, #0x162
    // 0xe87670: StoreField: r2->field_f = r16
    //     0xe87670: stur            w16, [x2, #0xf]
    // 0xe87674: r1 = 476
    //     0xe87674: movz            x1, #0x1dc
    // 0xe87678: add             x2, x0, w1, sxtw #1
    // 0xe8767c: r16 = 410
    //     0xe8767c: movz            x16, #0x19a
    // 0xe87680: StoreField: r2->field_f = r16
    //     0xe87680: stur            w16, [x2, #0xf]
    // 0xe87684: r1 = 478
    //     0xe87684: movz            x1, #0x1de
    // 0xe87688: add             x2, x0, w1, sxtw #1
    // 0xe8768c: r16 = 92
    //     0xe8768c: movz            x16, #0x5c
    // 0xe87690: StoreField: r2->field_f = r16
    //     0xe87690: stur            w16, [x2, #0xf]
    // 0xe87694: r1 = 480
    //     0xe87694: movz            x1, #0x1e0
    // 0xe87698: add             x2, x0, w1, sxtw #1
    // 0xe8769c: r16 = 394
    //     0xe8769c: movz            x16, #0x18a
    // 0xe876a0: StoreField: r2->field_f = r16
    //     0xe876a0: stur            w16, [x2, #0xf]
    // 0xe876a4: r1 = 482
    //     0xe876a4: movz            x1, #0x1e2
    // 0xe876a8: add             x2, x0, w1, sxtw #1
    // 0xe876ac: r16 = 486
    //     0xe876ac: movz            x16, #0x1e6
    // 0xe876b0: StoreField: r2->field_f = r16
    //     0xe876b0: stur            w16, [x2, #0xf]
    // 0xe876b4: r1 = 484
    //     0xe876b4: movz            x1, #0x1e4
    // 0xe876b8: add             x2, x0, w1, sxtw #1
    // 0xe876bc: r16 = 438
    //     0xe876bc: movz            x16, #0x1b6
    // 0xe876c0: StoreField: r2->field_f = r16
    //     0xe876c0: stur            w16, [x2, #0xf]
    // 0xe876c4: r1 = 486
    //     0xe876c4: movz            x1, #0x1e6
    // 0xe876c8: add             x2, x0, w1, sxtw #1
    // 0xe876cc: r16 = 142
    //     0xe876cc: movz            x16, #0x8e
    // 0xe876d0: StoreField: r2->field_f = r16
    //     0xe876d0: stur            w16, [x2, #0xf]
    // 0xe876d4: r1 = 488
    //     0xe876d4: movz            x1, #0x1e8
    // 0xe876d8: add             x2, x0, w1, sxtw #1
    // 0xe876dc: r16 = 458
    //     0xe876dc: movz            x16, #0x1ca
    // 0xe876e0: StoreField: r2->field_f = r16
    //     0xe876e0: stur            w16, [x2, #0xf]
    // 0xe876e4: r1 = 490
    //     0xe876e4: movz            x1, #0x1ea
    // 0xe876e8: add             x2, x0, w1, sxtw #1
    // 0xe876ec: r16 = 330
    //     0xe876ec: movz            x16, #0x14a
    // 0xe876f0: StoreField: r2->field_f = r16
    //     0xe876f0: stur            w16, [x2, #0xf]
    // 0xe876f4: r1 = 492
    //     0xe876f4: movz            x1, #0x1ec
    // 0xe876f8: add             x2, x0, w1, sxtw #1
    // 0xe876fc: r16 = 312
    //     0xe876fc: movz            x16, #0x138
    // 0xe87700: StoreField: r2->field_f = r16
    //     0xe87700: stur            w16, [x2, #0xf]
    // 0xe87704: r1 = 494
    //     0xe87704: movz            x1, #0x1ee
    // 0xe87708: add             x2, x0, w1, sxtw #1
    // 0xe8770c: r16 = 238
    //     0xe8770c: movz            x16, #0xee
    // 0xe87710: StoreField: r2->field_f = r16
    //     0xe87710: stur            w16, [x2, #0xf]
    // 0xe87714: r1 = 496
    //     0xe87714: movz            x1, #0x1f0
    // 0xe87718: add             x2, x0, w1, sxtw #1
    // 0xe8771c: r16 = 20
    //     0xe8771c: movz            x16, #0x14
    // 0xe87720: StoreField: r2->field_f = r16
    //     0xe87720: stur            w16, [x2, #0xf]
    // 0xe87724: r1 = 498
    //     0xe87724: movz            x1, #0x1f2
    // 0xe87728: add             x2, x0, w1, sxtw #1
    // 0xe8772c: r16 = 332
    //     0xe8772c: movz            x16, #0x14c
    // 0xe87730: StoreField: r2->field_f = r16
    //     0xe87730: stur            w16, [x2, #0xf]
    // 0xe87734: r1 = 500
    //     0xe87734: movz            x1, #0x1f4
    // 0xe87738: add             x2, x0, w1, sxtw #1
    // 0xe8773c: r16 = 64
    //     0xe8773c: movz            x16, #0x40
    // 0xe87740: StoreField: r2->field_f = r16
    //     0xe87740: stur            w16, [x2, #0xf]
    // 0xe87744: r1 = 502
    //     0xe87744: movz            x1, #0x1f6
    // 0xe87748: add             x2, x0, w1, sxtw #1
    // 0xe8774c: r16 = 208
    //     0xe8774c: movz            x16, #0xd0
    // 0xe87750: StoreField: r2->field_f = r16
    //     0xe87750: stur            w16, [x2, #0xf]
    // 0xe87754: r1 = 504
    //     0xe87754: movz            x1, #0x1f8
    // 0xe87758: add             x2, x0, w1, sxtw #1
    // 0xe8775c: r16 = 508
    //     0xe8775c: movz            x16, #0x1fc
    // 0xe87760: StoreField: r2->field_f = r16
    //     0xe87760: stur            w16, [x2, #0xf]
    // 0xe87764: r1 = 506
    //     0xe87764: movz            x1, #0x1fa
    // 0xe87768: add             x2, x0, w1, sxtw #1
    // 0xe8776c: r16 = 254
    //     0xe8776c: movz            x16, #0xfe
    // 0xe87770: StoreField: r2->field_f = r16
    //     0xe87770: stur            w16, [x2, #0xf]
    // 0xe87774: r1 = 508
    //     0xe87774: movz            x1, #0x1fc
    // 0xe87778: add             x2, x0, w1, sxtw #1
    // 0xe8777c: r16 = 386
    //     0xe8777c: movz            x16, #0x182
    // 0xe87780: StoreField: r2->field_f = r16
    //     0xe87780: stur            w16, [x2, #0xf]
    // 0xe87784: r1 = 510
    //     0xe87784: movz            x1, #0x1fe
    // 0xe87788: add             x2, x0, w1, sxtw #1
    // 0xe8778c: r16 = 346
    //     0xe8778c: movz            x16, #0x15a
    // 0xe87790: StoreField: r2->field_f = r16
    //     0xe87790: stur            w16, [x2, #0xf]
    // 0xe87794: r1 = <int>
    //     0xe87794: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe87798: r0 = AllocateGrowableArray()
    //     0xe87798: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe8779c: mov             x1, x0
    // 0xe877a0: ldur            x0, [fp, #-8]
    // 0xe877a4: stur            x1, [fp, #-0x10]
    // 0xe877a8: StoreField: r1->field_f = r0
    //     0xe877a8: stur            w0, [x1, #0xf]
    // 0xe877ac: r4 = 512
    //     0xe877ac: movz            x4, #0x200
    // 0xe877b0: StoreField: r1->field_b = r4
    //     0xe877b0: stur            w4, [x1, #0xb]
    // 0xe877b4: r0 = AllocateUint8Array()
    //     0xe877b4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe877b8: mov             x1, x0
    // 0xe877bc: ldur            x5, [fp, #-0x10]
    // 0xe877c0: r2 = 0
    //     0xe877c0: movz            x2, #0
    // 0xe877c4: r3 = 256
    //     0xe877c4: movz            x3, #0x100
    // 0xe877c8: r6 = 0
    //     0xe877c8: movz            x6, #0
    // 0xe877cc: stur            x0, [fp, #-8]
    // 0xe877d0: r0 = _slowSetRange()
    //     0xe877d0: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xe877d4: ldur            x0, [fp, #-8]
    // 0xe877d8: LeaveFrame
    //     0xe877d8: mov             SP, fp
    //     0xe877dc: ldp             fp, lr, [SP], #0x10
    // 0xe877e0: ret
    //     0xe877e0: ret             
    // 0xe877e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe877e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe877e8: b               #0xe86970
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xea89d0, size: 0xfc
    // 0xea89d0: EnterFrame
    //     0xea89d0: stp             fp, lr, [SP, #-0x10]!
    //     0xea89d4: mov             fp, SP
    // 0xea89d8: CheckStackOverflow
    //     0xea89d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea89dc: cmp             SP, x16
    //     0xea89e0: b.ls            #0xea8ac4
    // 0xea89e4: LoadField: r0 = r1->field_b
    //     0xea89e4: ldur            w0, [x1, #0xb]
    // 0xea89e8: DecompressPointer r0
    //     0xea89e8: add             x0, x0, HEAP, lsl #32
    // 0xea89ec: cmp             w0, NULL
    // 0xea89f0: b.eq            #0xea8a44
    // 0xea89f4: add             x0, x3, #8
    // 0xea89f8: LoadField: r4 = r2->field_13
    //     0xea89f8: ldur            w4, [x2, #0x13]
    // 0xea89fc: r7 = LoadInt32Instr(r4)
    //     0xea89fc: sbfx            x7, x4, #1, #0x1f
    // 0xea8a00: cmp             x0, x7
    // 0xea8a04: b.gt            #0xea8a6c
    // 0xea8a08: add             x0, x6, #8
    // 0xea8a0c: LoadField: r4 = r5->field_13
    //     0xea8a0c: ldur            w4, [x5, #0x13]
    // 0xea8a10: r7 = LoadInt32Instr(r4)
    //     0xea8a10: sbfx            x7, x4, #1, #0x1f
    // 0xea8a14: cmp             x0, x7
    // 0xea8a18: b.gt            #0xea8a98
    // 0xea8a1c: LoadField: r0 = r1->field_7
    //     0xea8a1c: ldur            w0, [x1, #7]
    // 0xea8a20: DecompressPointer r0
    //     0xea8a20: add             x0, x0, HEAP, lsl #32
    // 0xea8a24: tbnz            w0, #4, #0xea8a30
    // 0xea8a28: r0 = encryptBlock()
    //     0xea8a28: bl              #0xea9588  ; [package:pointycastle/block/rc2_engine.dart] RC2Engine::encryptBlock
    // 0xea8a2c: b               #0xea8a34
    // 0xea8a30: r0 = decryptBlock()
    //     0xea8a30: bl              #0xea8acc  ; [package:pointycastle/block/rc2_engine.dart] RC2Engine::decryptBlock
    // 0xea8a34: r0 = 8
    //     0xea8a34: movz            x0, #0x8
    // 0xea8a38: LeaveFrame
    //     0xea8a38: mov             SP, fp
    //     0xea8a3c: ldp             fp, lr, [SP], #0x10
    // 0xea8a40: ret
    //     0xea8a40: ret             
    // 0xea8a44: r0 = ArgumentError()
    //     0xea8a44: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea8a48: mov             x1, x0
    // 0xea8a4c: r0 = "RC2 engine not initialised"
    //     0xea8a4c: add             x0, PP, #0x23, lsl #12  ; [pp+0x236a8] "RC2 engine not initialised"
    //     0xea8a50: ldr             x0, [x0, #0x6a8]
    // 0xea8a54: ArrayStore: r1[0] = r0  ; List_4
    //     0xea8a54: stur            w0, [x1, #0x17]
    // 0xea8a58: r0 = false
    //     0xea8a58: add             x0, NULL, #0x30  ; false
    // 0xea8a5c: StoreField: r1->field_b = r0
    //     0xea8a5c: stur            w0, [x1, #0xb]
    // 0xea8a60: mov             x0, x1
    // 0xea8a64: r0 = Throw()
    //     0xea8a64: bl              #0xec04b8  ; ThrowStub
    // 0xea8a68: brk             #0
    // 0xea8a6c: r0 = false
    //     0xea8a6c: add             x0, NULL, #0x30  ; false
    // 0xea8a70: r0 = ArgumentError()
    //     0xea8a70: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea8a74: mov             x1, x0
    // 0xea8a78: r0 = "input buffer too short"
    //     0xea8a78: add             x0, PP, #0x23, lsl #12  ; [pp+0x236b0] "input buffer too short"
    //     0xea8a7c: ldr             x0, [x0, #0x6b0]
    // 0xea8a80: ArrayStore: r1[0] = r0  ; List_4
    //     0xea8a80: stur            w0, [x1, #0x17]
    // 0xea8a84: r0 = false
    //     0xea8a84: add             x0, NULL, #0x30  ; false
    // 0xea8a88: StoreField: r1->field_b = r0
    //     0xea8a88: stur            w0, [x1, #0xb]
    // 0xea8a8c: mov             x0, x1
    // 0xea8a90: r0 = Throw()
    //     0xea8a90: bl              #0xec04b8  ; ThrowStub
    // 0xea8a94: brk             #0
    // 0xea8a98: r0 = false
    //     0xea8a98: add             x0, NULL, #0x30  ; false
    // 0xea8a9c: r0 = ArgumentError()
    //     0xea8a9c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xea8aa0: mov             x1, x0
    // 0xea8aa4: r0 = "output buffer too short"
    //     0xea8aa4: add             x0, PP, #0x23, lsl #12  ; [pp+0x236b8] "output buffer too short"
    //     0xea8aa8: ldr             x0, [x0, #0x6b8]
    // 0xea8aac: ArrayStore: r1[0] = r0  ; List_4
    //     0xea8aac: stur            w0, [x1, #0x17]
    // 0xea8ab0: r0 = false
    //     0xea8ab0: add             x0, NULL, #0x30  ; false
    // 0xea8ab4: StoreField: r1->field_b = r0
    //     0xea8ab4: stur            w0, [x1, #0xb]
    // 0xea8ab8: mov             x0, x1
    // 0xea8abc: r0 = Throw()
    //     0xea8abc: bl              #0xec04b8  ; ThrowStub
    // 0xea8ac0: brk             #0
    // 0xea8ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea8ac4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea8ac8: b               #0xea89e4
  }
  _ decryptBlock(/* No info */) {
    // ** addr: 0xea8acc, size: 0xabc
    // 0xea8acc: EnterFrame
    //     0xea8acc: stp             fp, lr, [SP, #-0x10]!
    //     0xea8ad0: mov             fp, SP
    // 0xea8ad4: r4 = 255
    //     0xea8ad4: movz            x4, #0xff
    // 0xea8ad8: mov             x7, x1
    // 0xea8adc: mov             x16, x6
    // 0xea8ae0: mov             x6, x2
    // 0xea8ae4: mov             x2, x16
    // 0xea8ae8: add             x8, x3, #7
    // 0xea8aec: LoadField: r9 = r6->field_13
    //     0xea8aec: ldur            w9, [x6, #0x13]
    // 0xea8af0: r10 = LoadInt32Instr(r9)
    //     0xea8af0: sbfx            x10, x9, #1, #0x1f
    // 0xea8af4: mov             x0, x10
    // 0xea8af8: mov             x1, x8
    // 0xea8afc: cmp             x1, x0
    // 0xea8b00: b.hs            #0xea94d8
    // 0xea8b04: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea8b04: add             x16, x6, x8
    //     0xea8b08: ldrb            w9, [x16, #0x17]
    // 0xea8b0c: ubfx            x9, x9, #0, #0x20
    // 0xea8b10: and             x8, x9, x4
    // 0xea8b14: ubfx            x8, x8, #0, #0x20
    // 0xea8b18: lsl             x9, x8, #8
    // 0xea8b1c: add             x8, x3, #6
    // 0xea8b20: mov             x0, x10
    // 0xea8b24: mov             x1, x8
    // 0xea8b28: cmp             x1, x0
    // 0xea8b2c: b.hs            #0xea94dc
    // 0xea8b30: ArrayLoad: r11 = r6[r8]  ; List_1
    //     0xea8b30: add             x16, x6, x8
    //     0xea8b34: ldrb            w11, [x16, #0x17]
    // 0xea8b38: ubfx            x11, x11, #0, #0x20
    // 0xea8b3c: and             x8, x11, x4
    // 0xea8b40: ubfx            x8, x8, #0, #0x20
    // 0xea8b44: add             x11, x9, x8
    // 0xea8b48: add             x8, x3, #5
    // 0xea8b4c: mov             x0, x10
    // 0xea8b50: mov             x1, x8
    // 0xea8b54: cmp             x1, x0
    // 0xea8b58: b.hs            #0xea94e0
    // 0xea8b5c: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea8b5c: add             x16, x6, x8
    //     0xea8b60: ldrb            w9, [x16, #0x17]
    // 0xea8b64: ubfx            x9, x9, #0, #0x20
    // 0xea8b68: and             x8, x9, x4
    // 0xea8b6c: ubfx            x8, x8, #0, #0x20
    // 0xea8b70: lsl             x9, x8, #8
    // 0xea8b74: add             x8, x3, #4
    // 0xea8b78: mov             x0, x10
    // 0xea8b7c: mov             x1, x8
    // 0xea8b80: cmp             x1, x0
    // 0xea8b84: b.hs            #0xea94e4
    // 0xea8b88: ArrayLoad: r12 = r6[r8]  ; List_1
    //     0xea8b88: add             x16, x6, x8
    //     0xea8b8c: ldrb            w12, [x16, #0x17]
    // 0xea8b90: ubfx            x12, x12, #0, #0x20
    // 0xea8b94: and             x8, x12, x4
    // 0xea8b98: ubfx            x8, x8, #0, #0x20
    // 0xea8b9c: add             x12, x9, x8
    // 0xea8ba0: add             x8, x3, #3
    // 0xea8ba4: mov             x0, x10
    // 0xea8ba8: mov             x1, x8
    // 0xea8bac: cmp             x1, x0
    // 0xea8bb0: b.hs            #0xea94e8
    // 0xea8bb4: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea8bb4: add             x16, x6, x8
    //     0xea8bb8: ldrb            w9, [x16, #0x17]
    // 0xea8bbc: ubfx            x9, x9, #0, #0x20
    // 0xea8bc0: and             x8, x9, x4
    // 0xea8bc4: ubfx            x8, x8, #0, #0x20
    // 0xea8bc8: lsl             x9, x8, #8
    // 0xea8bcc: add             x8, x3, #2
    // 0xea8bd0: mov             x0, x10
    // 0xea8bd4: mov             x1, x8
    // 0xea8bd8: cmp             x1, x0
    // 0xea8bdc: b.hs            #0xea94ec
    // 0xea8be0: ArrayLoad: r13 = r6[r8]  ; List_1
    //     0xea8be0: add             x16, x6, x8
    //     0xea8be4: ldrb            w13, [x16, #0x17]
    // 0xea8be8: ubfx            x13, x13, #0, #0x20
    // 0xea8bec: and             x8, x13, x4
    // 0xea8bf0: ubfx            x8, x8, #0, #0x20
    // 0xea8bf4: add             x13, x9, x8
    // 0xea8bf8: add             x8, x3, #1
    // 0xea8bfc: mov             x0, x10
    // 0xea8c00: mov             x1, x8
    // 0xea8c04: cmp             x1, x0
    // 0xea8c08: b.hs            #0xea94f0
    // 0xea8c0c: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea8c0c: add             x16, x6, x8
    //     0xea8c10: ldrb            w9, [x16, #0x17]
    // 0xea8c14: ubfx            x9, x9, #0, #0x20
    // 0xea8c18: and             x8, x9, x4
    // 0xea8c1c: ubfx            x8, x8, #0, #0x20
    // 0xea8c20: lsl             x9, x8, #8
    // 0xea8c24: mov             x0, x10
    // 0xea8c28: mov             x1, x3
    // 0xea8c2c: cmp             x1, x0
    // 0xea8c30: b.hs            #0xea94f4
    // 0xea8c34: ArrayLoad: r8 = r6[r3]  ; List_1
    //     0xea8c34: add             x16, x6, x3
    //     0xea8c38: ldrb            w8, [x16, #0x17]
    // 0xea8c3c: ubfx            x8, x8, #0, #0x20
    // 0xea8c40: and             x3, x8, x4
    // 0xea8c44: ubfx            x3, x3, #0, #0x20
    // 0xea8c48: add             x4, x9, x3
    // 0xea8c4c: LoadField: r3 = r7->field_b
    //     0xea8c4c: ldur            w3, [x7, #0xb]
    // 0xea8c50: DecompressPointer r3
    //     0xea8c50: add             x3, x3, HEAP, lsl #32
    // 0xea8c54: mov             x10, x11
    // 0xea8c58: mov             x9, x12
    // 0xea8c5c: mov             x8, x13
    // 0xea8c60: mov             x7, x4
    // 0xea8c64: r6 = 60
    //     0xea8c64: movz            x6, #0x3c
    // 0xea8c68: r4 = 65535
    //     0xea8c68: orr             x4, xzr, #0xffff
    // 0xea8c6c: CheckStackOverflow
    //     0xea8c6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8c70: cmp             SP, x16
    //     0xea8c74: b.ls            #0xea94f8
    // 0xea8c78: cmp             x6, #0x2c
    // 0xea8c7c: b.lt            #0xea8e40
    // 0xea8c80: mov             x11, x10
    // 0xea8c84: ubfx            x11, x11, #0, #0x20
    // 0xea8c88: and             x12, x11, x4
    // 0xea8c8c: mov             x11, x12
    // 0xea8c90: ubfx            x11, x11, #0, #0x20
    // 0xea8c94: lsl             x13, x11, #0xb
    // 0xea8c98: ubfx            x12, x12, #0, #0x20
    // 0xea8c9c: asr             x11, x12, #5
    // 0xea8ca0: orr             x12, x13, x11
    // 0xea8ca4: mvn             x11, x9
    // 0xea8ca8: and             x13, x7, x11
    // 0xea8cac: and             x11, x8, x9
    // 0xea8cb0: add             x14, x13, x11
    // 0xea8cb4: cmp             w3, NULL
    // 0xea8cb8: b.eq            #0xea9500
    // 0xea8cbc: add             x10, x6, #3
    // 0xea8cc0: LoadField: r11 = r3->field_b
    //     0xea8cc0: ldur            w11, [x3, #0xb]
    // 0xea8cc4: r13 = LoadInt32Instr(r11)
    //     0xea8cc4: sbfx            x13, x11, #1, #0x1f
    // 0xea8cc8: mov             x0, x13
    // 0xea8ccc: mov             x1, x10
    // 0xea8cd0: cmp             x1, x0
    // 0xea8cd4: b.hs            #0xea9504
    // 0xea8cd8: LoadField: r11 = r3->field_f
    //     0xea8cd8: ldur            w11, [x3, #0xf]
    // 0xea8cdc: DecompressPointer r11
    //     0xea8cdc: add             x11, x11, HEAP, lsl #32
    // 0xea8ce0: ArrayLoad: r19 = r11[r10]  ; Unknown_4
    //     0xea8ce0: add             x16, x11, x10, lsl #2
    //     0xea8ce4: ldur            w19, [x16, #0xf]
    // 0xea8ce8: DecompressPointer r19
    //     0xea8ce8: add             x19, x19, HEAP, lsl #32
    // 0xea8cec: r20 = LoadInt32Instr(r19)
    //     0xea8cec: sbfx            x20, x19, #1, #0x1f
    //     0xea8cf0: tbz             w19, #0, #0xea8cf8
    //     0xea8cf4: ldur            x20, [x19, #7]
    // 0xea8cf8: add             x19, x14, x20
    // 0xea8cfc: sub             x10, x12, x19
    // 0xea8d00: mov             x12, x9
    // 0xea8d04: ubfx            x12, x12, #0, #0x20
    // 0xea8d08: and             x14, x12, x4
    // 0xea8d0c: mov             x12, x14
    // 0xea8d10: ubfx            x12, x12, #0, #0x20
    // 0xea8d14: lsl             x19, x12, #0xd
    // 0xea8d18: ubfx            x14, x14, #0, #0x20
    // 0xea8d1c: asr             x12, x14, #3
    // 0xea8d20: orr             x14, x19, x12
    // 0xea8d24: mvn             x12, x8
    // 0xea8d28: and             x19, x10, x12
    // 0xea8d2c: and             x12, x7, x8
    // 0xea8d30: add             x20, x19, x12
    // 0xea8d34: add             x9, x6, #2
    // 0xea8d38: mov             x0, x13
    // 0xea8d3c: mov             x1, x9
    // 0xea8d40: cmp             x1, x0
    // 0xea8d44: b.hs            #0xea9508
    // 0xea8d48: ArrayLoad: r12 = r11[r9]  ; Unknown_4
    //     0xea8d48: add             x16, x11, x9, lsl #2
    //     0xea8d4c: ldur            w12, [x16, #0xf]
    // 0xea8d50: DecompressPointer r12
    //     0xea8d50: add             x12, x12, HEAP, lsl #32
    // 0xea8d54: r19 = LoadInt32Instr(r12)
    //     0xea8d54: sbfx            x19, x12, #1, #0x1f
    //     0xea8d58: tbz             w12, #0, #0xea8d60
    //     0xea8d5c: ldur            x19, [x12, #7]
    // 0xea8d60: add             x12, x20, x19
    // 0xea8d64: sub             x9, x14, x12
    // 0xea8d68: mov             x12, x8
    // 0xea8d6c: ubfx            x12, x12, #0, #0x20
    // 0xea8d70: and             x14, x12, x4
    // 0xea8d74: mov             x12, x14
    // 0xea8d78: ubfx            x12, x12, #0, #0x20
    // 0xea8d7c: lsl             x19, x12, #0xe
    // 0xea8d80: ubfx            x14, x14, #0, #0x20
    // 0xea8d84: asr             x12, x14, #2
    // 0xea8d88: orr             x14, x19, x12
    // 0xea8d8c: mvn             x12, x7
    // 0xea8d90: and             x19, x9, x12
    // 0xea8d94: and             x12, x10, x7
    // 0xea8d98: add             x20, x19, x12
    // 0xea8d9c: add             x8, x6, #1
    // 0xea8da0: mov             x0, x13
    // 0xea8da4: mov             x1, x8
    // 0xea8da8: cmp             x1, x0
    // 0xea8dac: b.hs            #0xea950c
    // 0xea8db0: ArrayLoad: r12 = r11[r8]  ; Unknown_4
    //     0xea8db0: add             x16, x11, x8, lsl #2
    //     0xea8db4: ldur            w12, [x16, #0xf]
    // 0xea8db8: DecompressPointer r12
    //     0xea8db8: add             x12, x12, HEAP, lsl #32
    // 0xea8dbc: r19 = LoadInt32Instr(r12)
    //     0xea8dbc: sbfx            x19, x12, #1, #0x1f
    //     0xea8dc0: tbz             w12, #0, #0xea8dc8
    //     0xea8dc4: ldur            x19, [x12, #7]
    // 0xea8dc8: add             x12, x20, x19
    // 0xea8dcc: sub             x8, x14, x12
    // 0xea8dd0: mov             x12, x7
    // 0xea8dd4: ubfx            x12, x12, #0, #0x20
    // 0xea8dd8: and             x14, x12, x4
    // 0xea8ddc: mov             x12, x14
    // 0xea8de0: ubfx            x12, x12, #0, #0x20
    // 0xea8de4: lsl             x19, x12, #0xf
    // 0xea8de8: ubfx            x14, x14, #0, #0x20
    // 0xea8dec: asr             x12, x14, #1
    // 0xea8df0: orr             x14, x19, x12
    // 0xea8df4: mvn             x12, x10
    // 0xea8df8: and             x19, x8, x12
    // 0xea8dfc: and             x12, x9, x10
    // 0xea8e00: add             x20, x19, x12
    // 0xea8e04: mov             x0, x13
    // 0xea8e08: mov             x1, x6
    // 0xea8e0c: cmp             x1, x0
    // 0xea8e10: b.hs            #0xea9510
    // 0xea8e14: ArrayLoad: r12 = r11[r6]  ; Unknown_4
    //     0xea8e14: add             x16, x11, x6, lsl #2
    //     0xea8e18: ldur            w12, [x16, #0xf]
    // 0xea8e1c: DecompressPointer r12
    //     0xea8e1c: add             x12, x12, HEAP, lsl #32
    // 0xea8e20: r11 = LoadInt32Instr(r12)
    //     0xea8e20: sbfx            x11, x12, #1, #0x1f
    //     0xea8e24: tbz             w12, #0, #0xea8e2c
    //     0xea8e28: ldur            x11, [x12, #7]
    // 0xea8e2c: add             x12, x20, x11
    // 0xea8e30: sub             x7, x14, x12
    // 0xea8e34: sub             x0, x6, #4
    // 0xea8e38: mov             x6, x0
    // 0xea8e3c: b               #0xea8c6c
    // 0xea8e40: r6 = 63
    //     0xea8e40: movz            x6, #0x3f
    // 0xea8e44: cmp             w3, NULL
    // 0xea8e48: b.eq            #0xea9514
    // 0xea8e4c: mov             x11, x9
    // 0xea8e50: ubfx            x11, x11, #0, #0x20
    // 0xea8e54: and             x12, x11, x6
    // 0xea8e58: LoadField: r11 = r3->field_b
    //     0xea8e58: ldur            w11, [x3, #0xb]
    // 0xea8e5c: r13 = LoadInt32Instr(r11)
    //     0xea8e5c: sbfx            x13, x11, #1, #0x1f
    // 0xea8e60: ubfx            x12, x12, #0, #0x20
    // 0xea8e64: mov             x0, x13
    // 0xea8e68: mov             x1, x12
    // 0xea8e6c: cmp             x1, x0
    // 0xea8e70: b.hs            #0xea9518
    // 0xea8e74: LoadField: r11 = r3->field_f
    //     0xea8e74: ldur            w11, [x3, #0xf]
    // 0xea8e78: DecompressPointer r11
    //     0xea8e78: add             x11, x11, HEAP, lsl #32
    // 0xea8e7c: ArrayLoad: r3 = r11[r12]  ; Unknown_4
    //     0xea8e7c: add             x16, x11, x12, lsl #2
    //     0xea8e80: ldur            w3, [x16, #0xf]
    // 0xea8e84: DecompressPointer r3
    //     0xea8e84: add             x3, x3, HEAP, lsl #32
    // 0xea8e88: r12 = LoadInt32Instr(r3)
    //     0xea8e88: sbfx            x12, x3, #1, #0x1f
    //     0xea8e8c: tbz             w3, #0, #0xea8e94
    //     0xea8e90: ldur            x12, [x3, #7]
    // 0xea8e94: sub             x3, x10, x12
    // 0xea8e98: mov             x10, x8
    // 0xea8e9c: ubfx            x10, x10, #0, #0x20
    // 0xea8ea0: and             x12, x10, x6
    // 0xea8ea4: ubfx            x12, x12, #0, #0x20
    // 0xea8ea8: mov             x0, x13
    // 0xea8eac: mov             x1, x12
    // 0xea8eb0: cmp             x1, x0
    // 0xea8eb4: b.hs            #0xea951c
    // 0xea8eb8: ArrayLoad: r10 = r11[r12]  ; Unknown_4
    //     0xea8eb8: add             x16, x11, x12, lsl #2
    //     0xea8ebc: ldur            w10, [x16, #0xf]
    // 0xea8ec0: DecompressPointer r10
    //     0xea8ec0: add             x10, x10, HEAP, lsl #32
    // 0xea8ec4: r12 = LoadInt32Instr(r10)
    //     0xea8ec4: sbfx            x12, x10, #1, #0x1f
    //     0xea8ec8: tbz             w10, #0, #0xea8ed0
    //     0xea8ecc: ldur            x12, [x10, #7]
    // 0xea8ed0: sub             x10, x9, x12
    // 0xea8ed4: mov             x9, x7
    // 0xea8ed8: ubfx            x9, x9, #0, #0x20
    // 0xea8edc: and             x12, x9, x6
    // 0xea8ee0: ubfx            x12, x12, #0, #0x20
    // 0xea8ee4: mov             x0, x13
    // 0xea8ee8: mov             x1, x12
    // 0xea8eec: cmp             x1, x0
    // 0xea8ef0: b.hs            #0xea9520
    // 0xea8ef4: ArrayLoad: r9 = r11[r12]  ; Unknown_4
    //     0xea8ef4: add             x16, x11, x12, lsl #2
    //     0xea8ef8: ldur            w9, [x16, #0xf]
    // 0xea8efc: DecompressPointer r9
    //     0xea8efc: add             x9, x9, HEAP, lsl #32
    // 0xea8f00: r12 = LoadInt32Instr(r9)
    //     0xea8f00: sbfx            x12, x9, #1, #0x1f
    //     0xea8f04: tbz             w9, #0, #0xea8f0c
    //     0xea8f08: ldur            x12, [x9, #7]
    // 0xea8f0c: sub             x9, x8, x12
    // 0xea8f10: mov             x8, x3
    // 0xea8f14: ubfx            x8, x8, #0, #0x20
    // 0xea8f18: and             x12, x8, x6
    // 0xea8f1c: ubfx            x12, x12, #0, #0x20
    // 0xea8f20: mov             x0, x13
    // 0xea8f24: mov             x1, x12
    // 0xea8f28: cmp             x1, x0
    // 0xea8f2c: b.hs            #0xea9524
    // 0xea8f30: ArrayLoad: r8 = r11[r12]  ; Unknown_4
    //     0xea8f30: add             x16, x11, x12, lsl #2
    //     0xea8f34: ldur            w8, [x16, #0xf]
    // 0xea8f38: DecompressPointer r8
    //     0xea8f38: add             x8, x8, HEAP, lsl #32
    // 0xea8f3c: r12 = LoadInt32Instr(r8)
    //     0xea8f3c: sbfx            x12, x8, #1, #0x1f
    //     0xea8f40: tbz             w8, #0, #0xea8f48
    //     0xea8f44: ldur            x12, [x8, #7]
    // 0xea8f48: sub             x8, x7, x12
    // 0xea8f4c: mov             x7, x8
    // 0xea8f50: mov             x8, x9
    // 0xea8f54: mov             x9, x10
    // 0xea8f58: mov             x10, x3
    // 0xea8f5c: r3 = 40
    //     0xea8f5c: movz            x3, #0x28
    // 0xea8f60: CheckStackOverflow
    //     0xea8f60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8f64: cmp             SP, x16
    //     0xea8f68: b.ls            #0xea9528
    // 0xea8f6c: cmp             x3, #0x14
    // 0xea8f70: b.lt            #0xea911c
    // 0xea8f74: mov             x12, x10
    // 0xea8f78: ubfx            x12, x12, #0, #0x20
    // 0xea8f7c: and             x14, x12, x4
    // 0xea8f80: mov             x12, x14
    // 0xea8f84: ubfx            x12, x12, #0, #0x20
    // 0xea8f88: lsl             x19, x12, #0xb
    // 0xea8f8c: ubfx            x14, x14, #0, #0x20
    // 0xea8f90: asr             x12, x14, #5
    // 0xea8f94: orr             x14, x19, x12
    // 0xea8f98: mvn             x12, x9
    // 0xea8f9c: and             x19, x7, x12
    // 0xea8fa0: and             x12, x8, x9
    // 0xea8fa4: add             x20, x19, x12
    // 0xea8fa8: add             x10, x3, #3
    // 0xea8fac: mov             x0, x13
    // 0xea8fb0: mov             x1, x10
    // 0xea8fb4: cmp             x1, x0
    // 0xea8fb8: b.hs            #0xea9530
    // 0xea8fbc: ArrayLoad: r12 = r11[r10]  ; Unknown_4
    //     0xea8fbc: add             x16, x11, x10, lsl #2
    //     0xea8fc0: ldur            w12, [x16, #0xf]
    // 0xea8fc4: DecompressPointer r12
    //     0xea8fc4: add             x12, x12, HEAP, lsl #32
    // 0xea8fc8: r19 = LoadInt32Instr(r12)
    //     0xea8fc8: sbfx            x19, x12, #1, #0x1f
    //     0xea8fcc: tbz             w12, #0, #0xea8fd4
    //     0xea8fd0: ldur            x19, [x12, #7]
    // 0xea8fd4: add             x12, x20, x19
    // 0xea8fd8: sub             x10, x14, x12
    // 0xea8fdc: mov             x12, x9
    // 0xea8fe0: ubfx            x12, x12, #0, #0x20
    // 0xea8fe4: and             x14, x12, x4
    // 0xea8fe8: mov             x12, x14
    // 0xea8fec: ubfx            x12, x12, #0, #0x20
    // 0xea8ff0: lsl             x19, x12, #0xd
    // 0xea8ff4: ubfx            x14, x14, #0, #0x20
    // 0xea8ff8: asr             x12, x14, #3
    // 0xea8ffc: orr             x14, x19, x12
    // 0xea9000: mvn             x12, x8
    // 0xea9004: and             x19, x10, x12
    // 0xea9008: and             x12, x7, x8
    // 0xea900c: add             x20, x19, x12
    // 0xea9010: add             x9, x3, #2
    // 0xea9014: mov             x0, x13
    // 0xea9018: mov             x1, x9
    // 0xea901c: cmp             x1, x0
    // 0xea9020: b.hs            #0xea9534
    // 0xea9024: ArrayLoad: r12 = r11[r9]  ; Unknown_4
    //     0xea9024: add             x16, x11, x9, lsl #2
    //     0xea9028: ldur            w12, [x16, #0xf]
    // 0xea902c: DecompressPointer r12
    //     0xea902c: add             x12, x12, HEAP, lsl #32
    // 0xea9030: r19 = LoadInt32Instr(r12)
    //     0xea9030: sbfx            x19, x12, #1, #0x1f
    //     0xea9034: tbz             w12, #0, #0xea903c
    //     0xea9038: ldur            x19, [x12, #7]
    // 0xea903c: add             x12, x20, x19
    // 0xea9040: sub             x9, x14, x12
    // 0xea9044: mov             x12, x8
    // 0xea9048: ubfx            x12, x12, #0, #0x20
    // 0xea904c: and             x14, x12, x4
    // 0xea9050: mov             x12, x14
    // 0xea9054: ubfx            x12, x12, #0, #0x20
    // 0xea9058: lsl             x19, x12, #0xe
    // 0xea905c: ubfx            x14, x14, #0, #0x20
    // 0xea9060: asr             x12, x14, #2
    // 0xea9064: orr             x14, x19, x12
    // 0xea9068: mvn             x12, x7
    // 0xea906c: and             x19, x9, x12
    // 0xea9070: and             x12, x10, x7
    // 0xea9074: add             x20, x19, x12
    // 0xea9078: add             x8, x3, #1
    // 0xea907c: mov             x0, x13
    // 0xea9080: mov             x1, x8
    // 0xea9084: cmp             x1, x0
    // 0xea9088: b.hs            #0xea9538
    // 0xea908c: ArrayLoad: r12 = r11[r8]  ; Unknown_4
    //     0xea908c: add             x16, x11, x8, lsl #2
    //     0xea9090: ldur            w12, [x16, #0xf]
    // 0xea9094: DecompressPointer r12
    //     0xea9094: add             x12, x12, HEAP, lsl #32
    // 0xea9098: r19 = LoadInt32Instr(r12)
    //     0xea9098: sbfx            x19, x12, #1, #0x1f
    //     0xea909c: tbz             w12, #0, #0xea90a4
    //     0xea90a0: ldur            x19, [x12, #7]
    // 0xea90a4: add             x12, x20, x19
    // 0xea90a8: sub             x8, x14, x12
    // 0xea90ac: mov             x12, x7
    // 0xea90b0: ubfx            x12, x12, #0, #0x20
    // 0xea90b4: and             x14, x12, x4
    // 0xea90b8: mov             x12, x14
    // 0xea90bc: ubfx            x12, x12, #0, #0x20
    // 0xea90c0: lsl             x19, x12, #0xf
    // 0xea90c4: ubfx            x14, x14, #0, #0x20
    // 0xea90c8: asr             x12, x14, #1
    // 0xea90cc: orr             x14, x19, x12
    // 0xea90d0: mvn             x12, x10
    // 0xea90d4: and             x19, x8, x12
    // 0xea90d8: and             x12, x9, x10
    // 0xea90dc: add             x20, x19, x12
    // 0xea90e0: mov             x0, x13
    // 0xea90e4: mov             x1, x3
    // 0xea90e8: cmp             x1, x0
    // 0xea90ec: b.hs            #0xea953c
    // 0xea90f0: ArrayLoad: r12 = r11[r3]  ; Unknown_4
    //     0xea90f0: add             x16, x11, x3, lsl #2
    //     0xea90f4: ldur            w12, [x16, #0xf]
    // 0xea90f8: DecompressPointer r12
    //     0xea90f8: add             x12, x12, HEAP, lsl #32
    // 0xea90fc: r19 = LoadInt32Instr(r12)
    //     0xea90fc: sbfx            x19, x12, #1, #0x1f
    //     0xea9100: tbz             w12, #0, #0xea9108
    //     0xea9104: ldur            x19, [x12, #7]
    // 0xea9108: add             x12, x20, x19
    // 0xea910c: sub             x7, x14, x12
    // 0xea9110: sub             x0, x3, #4
    // 0xea9114: mov             x3, x0
    // 0xea9118: b               #0xea8f60
    // 0xea911c: mov             x3, x9
    // 0xea9120: ubfx            x3, x3, #0, #0x20
    // 0xea9124: and             x12, x3, x6
    // 0xea9128: ubfx            x12, x12, #0, #0x20
    // 0xea912c: mov             x0, x13
    // 0xea9130: mov             x1, x12
    // 0xea9134: cmp             x1, x0
    // 0xea9138: b.hs            #0xea9540
    // 0xea913c: ArrayLoad: r3 = r11[r12]  ; Unknown_4
    //     0xea913c: add             x16, x11, x12, lsl #2
    //     0xea9140: ldur            w3, [x16, #0xf]
    // 0xea9144: DecompressPointer r3
    //     0xea9144: add             x3, x3, HEAP, lsl #32
    // 0xea9148: r12 = LoadInt32Instr(r3)
    //     0xea9148: sbfx            x12, x3, #1, #0x1f
    //     0xea914c: tbz             w3, #0, #0xea9154
    //     0xea9150: ldur            x12, [x3, #7]
    // 0xea9154: sub             x3, x10, x12
    // 0xea9158: mov             x10, x8
    // 0xea915c: ubfx            x10, x10, #0, #0x20
    // 0xea9160: and             x12, x10, x6
    // 0xea9164: ubfx            x12, x12, #0, #0x20
    // 0xea9168: mov             x0, x13
    // 0xea916c: mov             x1, x12
    // 0xea9170: cmp             x1, x0
    // 0xea9174: b.hs            #0xea9544
    // 0xea9178: ArrayLoad: r10 = r11[r12]  ; Unknown_4
    //     0xea9178: add             x16, x11, x12, lsl #2
    //     0xea917c: ldur            w10, [x16, #0xf]
    // 0xea9180: DecompressPointer r10
    //     0xea9180: add             x10, x10, HEAP, lsl #32
    // 0xea9184: r12 = LoadInt32Instr(r10)
    //     0xea9184: sbfx            x12, x10, #1, #0x1f
    //     0xea9188: tbz             w10, #0, #0xea9190
    //     0xea918c: ldur            x12, [x10, #7]
    // 0xea9190: sub             x10, x9, x12
    // 0xea9194: mov             x9, x7
    // 0xea9198: ubfx            x9, x9, #0, #0x20
    // 0xea919c: and             x12, x9, x6
    // 0xea91a0: ubfx            x12, x12, #0, #0x20
    // 0xea91a4: mov             x0, x13
    // 0xea91a8: mov             x1, x12
    // 0xea91ac: cmp             x1, x0
    // 0xea91b0: b.hs            #0xea9548
    // 0xea91b4: ArrayLoad: r9 = r11[r12]  ; Unknown_4
    //     0xea91b4: add             x16, x11, x12, lsl #2
    //     0xea91b8: ldur            w9, [x16, #0xf]
    // 0xea91bc: DecompressPointer r9
    //     0xea91bc: add             x9, x9, HEAP, lsl #32
    // 0xea91c0: r12 = LoadInt32Instr(r9)
    //     0xea91c0: sbfx            x12, x9, #1, #0x1f
    //     0xea91c4: tbz             w9, #0, #0xea91cc
    //     0xea91c8: ldur            x12, [x9, #7]
    // 0xea91cc: sub             x9, x8, x12
    // 0xea91d0: mov             x8, x3
    // 0xea91d4: ubfx            x8, x8, #0, #0x20
    // 0xea91d8: and             x12, x8, x6
    // 0xea91dc: ubfx            x12, x12, #0, #0x20
    // 0xea91e0: mov             x0, x13
    // 0xea91e4: mov             x1, x12
    // 0xea91e8: cmp             x1, x0
    // 0xea91ec: b.hs            #0xea954c
    // 0xea91f0: ArrayLoad: r6 = r11[r12]  ; Unknown_4
    //     0xea91f0: add             x16, x11, x12, lsl #2
    //     0xea91f4: ldur            w6, [x16, #0xf]
    // 0xea91f8: DecompressPointer r6
    //     0xea91f8: add             x6, x6, HEAP, lsl #32
    // 0xea91fc: r8 = LoadInt32Instr(r6)
    //     0xea91fc: sbfx            x8, x6, #1, #0x1f
    //     0xea9200: tbz             w6, #0, #0xea9208
    //     0xea9204: ldur            x8, [x6, #7]
    // 0xea9208: sub             x6, x7, x8
    // 0xea920c: mov             x7, x9
    // 0xea9210: mov             x9, x3
    // 0xea9214: mov             x8, x10
    // 0xea9218: r3 = 16
    //     0xea9218: movz            x3, #0x10
    // 0xea921c: CheckStackOverflow
    //     0xea921c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9220: cmp             SP, x16
    //     0xea9224: b.ls            #0xea9550
    // 0xea9228: tbnz            x3, #0x3f, #0xea93d4
    // 0xea922c: mov             x10, x9
    // 0xea9230: ubfx            x10, x10, #0, #0x20
    // 0xea9234: and             x12, x10, x4
    // 0xea9238: mov             x10, x12
    // 0xea923c: ubfx            x10, x10, #0, #0x20
    // 0xea9240: lsl             x14, x10, #0xb
    // 0xea9244: ubfx            x12, x12, #0, #0x20
    // 0xea9248: asr             x10, x12, #5
    // 0xea924c: orr             x12, x14, x10
    // 0xea9250: mvn             x10, x8
    // 0xea9254: and             x14, x6, x10
    // 0xea9258: and             x10, x7, x8
    // 0xea925c: add             x19, x14, x10
    // 0xea9260: add             x9, x3, #3
    // 0xea9264: mov             x0, x13
    // 0xea9268: mov             x1, x9
    // 0xea926c: cmp             x1, x0
    // 0xea9270: b.hs            #0xea9558
    // 0xea9274: ArrayLoad: r10 = r11[r9]  ; Unknown_4
    //     0xea9274: add             x16, x11, x9, lsl #2
    //     0xea9278: ldur            w10, [x16, #0xf]
    // 0xea927c: DecompressPointer r10
    //     0xea927c: add             x10, x10, HEAP, lsl #32
    // 0xea9280: r14 = LoadInt32Instr(r10)
    //     0xea9280: sbfx            x14, x10, #1, #0x1f
    //     0xea9284: tbz             w10, #0, #0xea928c
    //     0xea9288: ldur            x14, [x10, #7]
    // 0xea928c: add             x10, x19, x14
    // 0xea9290: sub             x9, x12, x10
    // 0xea9294: mov             x10, x8
    // 0xea9298: ubfx            x10, x10, #0, #0x20
    // 0xea929c: and             x12, x10, x4
    // 0xea92a0: mov             x10, x12
    // 0xea92a4: ubfx            x10, x10, #0, #0x20
    // 0xea92a8: lsl             x14, x10, #0xd
    // 0xea92ac: ubfx            x12, x12, #0, #0x20
    // 0xea92b0: asr             x10, x12, #3
    // 0xea92b4: orr             x12, x14, x10
    // 0xea92b8: mvn             x10, x7
    // 0xea92bc: and             x14, x9, x10
    // 0xea92c0: and             x10, x6, x7
    // 0xea92c4: add             x19, x14, x10
    // 0xea92c8: add             x8, x3, #2
    // 0xea92cc: mov             x0, x13
    // 0xea92d0: mov             x1, x8
    // 0xea92d4: cmp             x1, x0
    // 0xea92d8: b.hs            #0xea955c
    // 0xea92dc: ArrayLoad: r10 = r11[r8]  ; Unknown_4
    //     0xea92dc: add             x16, x11, x8, lsl #2
    //     0xea92e0: ldur            w10, [x16, #0xf]
    // 0xea92e4: DecompressPointer r10
    //     0xea92e4: add             x10, x10, HEAP, lsl #32
    // 0xea92e8: r14 = LoadInt32Instr(r10)
    //     0xea92e8: sbfx            x14, x10, #1, #0x1f
    //     0xea92ec: tbz             w10, #0, #0xea92f4
    //     0xea92f0: ldur            x14, [x10, #7]
    // 0xea92f4: add             x10, x19, x14
    // 0xea92f8: sub             x8, x12, x10
    // 0xea92fc: mov             x10, x7
    // 0xea9300: ubfx            x10, x10, #0, #0x20
    // 0xea9304: and             x12, x10, x4
    // 0xea9308: mov             x10, x12
    // 0xea930c: ubfx            x10, x10, #0, #0x20
    // 0xea9310: lsl             x14, x10, #0xe
    // 0xea9314: ubfx            x12, x12, #0, #0x20
    // 0xea9318: asr             x10, x12, #2
    // 0xea931c: orr             x12, x14, x10
    // 0xea9320: mvn             x10, x6
    // 0xea9324: and             x14, x8, x10
    // 0xea9328: and             x10, x9, x6
    // 0xea932c: add             x19, x14, x10
    // 0xea9330: add             x7, x3, #1
    // 0xea9334: mov             x0, x13
    // 0xea9338: mov             x1, x7
    // 0xea933c: cmp             x1, x0
    // 0xea9340: b.hs            #0xea9560
    // 0xea9344: ArrayLoad: r10 = r11[r7]  ; Unknown_4
    //     0xea9344: add             x16, x11, x7, lsl #2
    //     0xea9348: ldur            w10, [x16, #0xf]
    // 0xea934c: DecompressPointer r10
    //     0xea934c: add             x10, x10, HEAP, lsl #32
    // 0xea9350: r14 = LoadInt32Instr(r10)
    //     0xea9350: sbfx            x14, x10, #1, #0x1f
    //     0xea9354: tbz             w10, #0, #0xea935c
    //     0xea9358: ldur            x14, [x10, #7]
    // 0xea935c: add             x10, x19, x14
    // 0xea9360: sub             x7, x12, x10
    // 0xea9364: mov             x10, x6
    // 0xea9368: ubfx            x10, x10, #0, #0x20
    // 0xea936c: and             x12, x10, x4
    // 0xea9370: mov             x10, x12
    // 0xea9374: ubfx            x10, x10, #0, #0x20
    // 0xea9378: lsl             x14, x10, #0xf
    // 0xea937c: ubfx            x12, x12, #0, #0x20
    // 0xea9380: asr             x10, x12, #1
    // 0xea9384: orr             x12, x14, x10
    // 0xea9388: mvn             x10, x9
    // 0xea938c: and             x14, x7, x10
    // 0xea9390: and             x10, x8, x9
    // 0xea9394: add             x19, x14, x10
    // 0xea9398: mov             x0, x13
    // 0xea939c: mov             x1, x3
    // 0xea93a0: cmp             x1, x0
    // 0xea93a4: b.hs            #0xea9564
    // 0xea93a8: ArrayLoad: r10 = r11[r3]  ; Unknown_4
    //     0xea93a8: add             x16, x11, x3, lsl #2
    //     0xea93ac: ldur            w10, [x16, #0xf]
    // 0xea93b0: DecompressPointer r10
    //     0xea93b0: add             x10, x10, HEAP, lsl #32
    // 0xea93b4: r14 = LoadInt32Instr(r10)
    //     0xea93b4: sbfx            x14, x10, #1, #0x1f
    //     0xea93b8: tbz             w10, #0, #0xea93c0
    //     0xea93bc: ldur            x14, [x10, #7]
    // 0xea93c0: add             x10, x19, x14
    // 0xea93c4: sub             x6, x12, x10
    // 0xea93c8: sub             x0, x3, #4
    // 0xea93cc: mov             x3, x0
    // 0xea93d0: b               #0xea921c
    // 0xea93d4: LoadField: r3 = r5->field_13
    //     0xea93d4: ldur            w3, [x5, #0x13]
    // 0xea93d8: r4 = LoadInt32Instr(r3)
    //     0xea93d8: sbfx            x4, x3, #1, #0x1f
    // 0xea93dc: mov             x0, x4
    // 0xea93e0: mov             x1, x2
    // 0xea93e4: cmp             x1, x0
    // 0xea93e8: b.hs            #0xea9568
    // 0xea93ec: ArrayStore: r5[r2] = r6  ; TypeUnknown_1
    //     0xea93ec: add             x3, x5, x2
    //     0xea93f0: strb            w6, [x3, #0x17]
    // 0xea93f4: add             x3, x2, #1
    // 0xea93f8: asr             x10, x6, #8
    // 0xea93fc: mov             x0, x4
    // 0xea9400: mov             x1, x3
    // 0xea9404: cmp             x1, x0
    // 0xea9408: b.hs            #0xea956c
    // 0xea940c: ArrayStore: r5[r3] = r10  ; TypeUnknown_1
    //     0xea940c: add             x6, x5, x3
    //     0xea9410: strb            w10, [x6, #0x17]
    // 0xea9414: add             x3, x2, #2
    // 0xea9418: mov             x0, x4
    // 0xea941c: mov             x1, x3
    // 0xea9420: cmp             x1, x0
    // 0xea9424: b.hs            #0xea9570
    // 0xea9428: ArrayStore: r5[r3] = r7  ; TypeUnknown_1
    //     0xea9428: add             x6, x5, x3
    //     0xea942c: strb            w7, [x6, #0x17]
    // 0xea9430: add             x3, x2, #3
    // 0xea9434: asr             x6, x7, #8
    // 0xea9438: mov             x0, x4
    // 0xea943c: mov             x1, x3
    // 0xea9440: cmp             x1, x0
    // 0xea9444: b.hs            #0xea9574
    // 0xea9448: ArrayStore: r5[r3] = r6  ; TypeUnknown_1
    //     0xea9448: add             x7, x5, x3
    //     0xea944c: strb            w6, [x7, #0x17]
    // 0xea9450: add             x3, x2, #4
    // 0xea9454: mov             x0, x4
    // 0xea9458: mov             x1, x3
    // 0xea945c: cmp             x1, x0
    // 0xea9460: b.hs            #0xea9578
    // 0xea9464: ArrayStore: r5[r3] = r8  ; TypeUnknown_1
    //     0xea9464: add             x6, x5, x3
    //     0xea9468: strb            w8, [x6, #0x17]
    // 0xea946c: add             x3, x2, #5
    // 0xea9470: asr             x6, x8, #8
    // 0xea9474: mov             x0, x4
    // 0xea9478: mov             x1, x3
    // 0xea947c: cmp             x1, x0
    // 0xea9480: b.hs            #0xea957c
    // 0xea9484: ArrayStore: r5[r3] = r6  ; TypeUnknown_1
    //     0xea9484: add             x7, x5, x3
    //     0xea9488: strb            w6, [x7, #0x17]
    // 0xea948c: add             x3, x2, #6
    // 0xea9490: mov             x0, x4
    // 0xea9494: mov             x1, x3
    // 0xea9498: cmp             x1, x0
    // 0xea949c: b.hs            #0xea9580
    // 0xea94a0: ArrayStore: r5[r3] = r9  ; TypeUnknown_1
    //     0xea94a0: add             x6, x5, x3
    //     0xea94a4: strb            w9, [x6, #0x17]
    // 0xea94a8: add             x3, x2, #7
    // 0xea94ac: asr             x2, x9, #8
    // 0xea94b0: mov             x0, x4
    // 0xea94b4: mov             x1, x3
    // 0xea94b8: cmp             x1, x0
    // 0xea94bc: b.hs            #0xea9584
    // 0xea94c0: ArrayStore: r5[r3] = r2  ; TypeUnknown_1
    //     0xea94c0: add             x1, x5, x3
    //     0xea94c4: strb            w2, [x1, #0x17]
    // 0xea94c8: r0 = Null
    //     0xea94c8: mov             x0, NULL
    // 0xea94cc: LeaveFrame
    //     0xea94cc: mov             SP, fp
    //     0xea94d0: ldp             fp, lr, [SP], #0x10
    // 0xea94d4: ret
    //     0xea94d4: ret             
    // 0xea94d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea94d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea94dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea94dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea94e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea94e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea94e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea94e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea94e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea94e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea94ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea94ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea94f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea94f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea94f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea94f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea94f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea94f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea94fc: b               #0xea8c78
    // 0xea9500: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea9500: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea9504: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9504: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9508: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9508: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea950c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea950c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9510: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9510: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9514: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xea9514: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xea9518: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9518: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea951c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea951c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9520: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9520: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9524: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9524: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9528: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea952c: b               #0xea8f6c
    // 0xea9530: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9530: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9534: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9534: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9538: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9538: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea953c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea953c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9540: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9540: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9544: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9544: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9548: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9548: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea954c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea954c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9550: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9554: b               #0xea9228
    // 0xea9558: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9558: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea955c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea955c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9560: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9560: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9564: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9564: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9568: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9568: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea956c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea956c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9570: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9570: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9574: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9574: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9578: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9578: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea957c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea957c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9580: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9580: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea9584: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea9584: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ encryptBlock(/* No info */) {
    // ** addr: 0xea9588, size: 0xbd0
    // 0xea9588: EnterFrame
    //     0xea9588: stp             fp, lr, [SP, #-0x10]!
    //     0xea958c: mov             fp, SP
    // 0xea9590: r4 = 255
    //     0xea9590: movz            x4, #0xff
    // 0xea9594: mov             x7, x1
    // 0xea9598: mov             x16, x6
    // 0xea959c: mov             x6, x2
    // 0xea95a0: mov             x2, x16
    // 0xea95a4: add             x8, x3, #7
    // 0xea95a8: LoadField: r9 = r6->field_13
    //     0xea95a8: ldur            w9, [x6, #0x13]
    // 0xea95ac: r10 = LoadInt32Instr(r9)
    //     0xea95ac: sbfx            x10, x9, #1, #0x1f
    // 0xea95b0: mov             x0, x10
    // 0xea95b4: mov             x1, x8
    // 0xea95b8: cmp             x1, x0
    // 0xea95bc: b.hs            #0xeaa0a8
    // 0xea95c0: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea95c0: add             x16, x6, x8
    //     0xea95c4: ldrb            w9, [x16, #0x17]
    // 0xea95c8: ubfx            x9, x9, #0, #0x20
    // 0xea95cc: and             x8, x9, x4
    // 0xea95d0: ubfx            x8, x8, #0, #0x20
    // 0xea95d4: lsl             x9, x8, #8
    // 0xea95d8: add             x8, x3, #6
    // 0xea95dc: mov             x0, x10
    // 0xea95e0: mov             x1, x8
    // 0xea95e4: cmp             x1, x0
    // 0xea95e8: b.hs            #0xeaa0ac
    // 0xea95ec: ArrayLoad: r11 = r6[r8]  ; List_1
    //     0xea95ec: add             x16, x6, x8
    //     0xea95f0: ldrb            w11, [x16, #0x17]
    // 0xea95f4: ubfx            x11, x11, #0, #0x20
    // 0xea95f8: and             x8, x11, x4
    // 0xea95fc: ubfx            x8, x8, #0, #0x20
    // 0xea9600: add             x11, x9, x8
    // 0xea9604: add             x8, x3, #5
    // 0xea9608: mov             x0, x10
    // 0xea960c: mov             x1, x8
    // 0xea9610: cmp             x1, x0
    // 0xea9614: b.hs            #0xeaa0b0
    // 0xea9618: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea9618: add             x16, x6, x8
    //     0xea961c: ldrb            w9, [x16, #0x17]
    // 0xea9620: ubfx            x9, x9, #0, #0x20
    // 0xea9624: and             x8, x9, x4
    // 0xea9628: ubfx            x8, x8, #0, #0x20
    // 0xea962c: lsl             x9, x8, #8
    // 0xea9630: add             x8, x3, #4
    // 0xea9634: mov             x0, x10
    // 0xea9638: mov             x1, x8
    // 0xea963c: cmp             x1, x0
    // 0xea9640: b.hs            #0xeaa0b4
    // 0xea9644: ArrayLoad: r12 = r6[r8]  ; List_1
    //     0xea9644: add             x16, x6, x8
    //     0xea9648: ldrb            w12, [x16, #0x17]
    // 0xea964c: ubfx            x12, x12, #0, #0x20
    // 0xea9650: and             x8, x12, x4
    // 0xea9654: ubfx            x8, x8, #0, #0x20
    // 0xea9658: add             x12, x9, x8
    // 0xea965c: add             x8, x3, #3
    // 0xea9660: mov             x0, x10
    // 0xea9664: mov             x1, x8
    // 0xea9668: cmp             x1, x0
    // 0xea966c: b.hs            #0xeaa0b8
    // 0xea9670: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea9670: add             x16, x6, x8
    //     0xea9674: ldrb            w9, [x16, #0x17]
    // 0xea9678: ubfx            x9, x9, #0, #0x20
    // 0xea967c: and             x8, x9, x4
    // 0xea9680: ubfx            x8, x8, #0, #0x20
    // 0xea9684: lsl             x9, x8, #8
    // 0xea9688: add             x8, x3, #2
    // 0xea968c: mov             x0, x10
    // 0xea9690: mov             x1, x8
    // 0xea9694: cmp             x1, x0
    // 0xea9698: b.hs            #0xeaa0bc
    // 0xea969c: ArrayLoad: r13 = r6[r8]  ; List_1
    //     0xea969c: add             x16, x6, x8
    //     0xea96a0: ldrb            w13, [x16, #0x17]
    // 0xea96a4: ubfx            x13, x13, #0, #0x20
    // 0xea96a8: and             x8, x13, x4
    // 0xea96ac: ubfx            x8, x8, #0, #0x20
    // 0xea96b0: add             x13, x9, x8
    // 0xea96b4: add             x8, x3, #1
    // 0xea96b8: mov             x0, x10
    // 0xea96bc: mov             x1, x8
    // 0xea96c0: cmp             x1, x0
    // 0xea96c4: b.hs            #0xeaa0c0
    // 0xea96c8: ArrayLoad: r9 = r6[r8]  ; List_1
    //     0xea96c8: add             x16, x6, x8
    //     0xea96cc: ldrb            w9, [x16, #0x17]
    // 0xea96d0: ubfx            x9, x9, #0, #0x20
    // 0xea96d4: and             x8, x9, x4
    // 0xea96d8: ubfx            x8, x8, #0, #0x20
    // 0xea96dc: lsl             x9, x8, #8
    // 0xea96e0: mov             x0, x10
    // 0xea96e4: mov             x1, x3
    // 0xea96e8: cmp             x1, x0
    // 0xea96ec: b.hs            #0xeaa0c4
    // 0xea96f0: ArrayLoad: r8 = r6[r3]  ; List_1
    //     0xea96f0: add             x16, x6, x3
    //     0xea96f4: ldrb            w8, [x16, #0x17]
    // 0xea96f8: ubfx            x8, x8, #0, #0x20
    // 0xea96fc: and             x3, x8, x4
    // 0xea9700: ubfx            x3, x3, #0, #0x20
    // 0xea9704: add             x4, x9, x3
    // 0xea9708: LoadField: r3 = r7->field_b
    //     0xea9708: ldur            w3, [x7, #0xb]
    // 0xea970c: DecompressPointer r3
    //     0xea970c: add             x3, x3, HEAP, lsl #32
    // 0xea9710: mov             x10, x11
    // 0xea9714: mov             x9, x12
    // 0xea9718: mov             x8, x13
    // 0xea971c: mov             x7, x4
    // 0xea9720: r6 = 0
    //     0xea9720: movz            x6, #0
    // 0xea9724: r4 = 65535
    //     0xea9724: orr             x4, xzr, #0xffff
    // 0xea9728: CheckStackOverflow
    //     0xea9728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea972c: cmp             SP, x16
    //     0xea9730: b.ls            #0xeaa0c8
    // 0xea9734: cmp             x6, #0x10
    // 0xea9738: b.gt            #0xea9990
    // 0xea973c: mov             x11, x10
    // 0xea9740: ubfx            x11, x11, #0, #0x20
    // 0xea9744: mvn             w12, w11
    // 0xea9748: mov             x11, x8
    // 0xea974c: ubfx            x11, x11, #0, #0x20
    // 0xea9750: and             x13, x11, x12
    // 0xea9754: ubfx            x13, x13, #0, #0x20
    // 0xea9758: add             x11, x7, x13
    // 0xea975c: mov             x12, x10
    // 0xea9760: ubfx            x12, x12, #0, #0x20
    // 0xea9764: mov             x13, x9
    // 0xea9768: ubfx            x13, x13, #0, #0x20
    // 0xea976c: and             x14, x13, x12
    // 0xea9770: ubfx            x14, x14, #0, #0x20
    // 0xea9774: add             x12, x11, x14
    // 0xea9778: cmp             w3, NULL
    // 0xea977c: b.eq            #0xeaa0d0
    // 0xea9780: LoadField: r11 = r3->field_b
    //     0xea9780: ldur            w11, [x3, #0xb]
    // 0xea9784: r7 = LoadInt32Instr(r11)
    //     0xea9784: sbfx            x7, x11, #1, #0x1f
    // 0xea9788: mov             x0, x7
    // 0xea978c: mov             x1, x6
    // 0xea9790: cmp             x1, x0
    // 0xea9794: b.hs            #0xeaa0d4
    // 0xea9798: LoadField: r11 = r3->field_f
    //     0xea9798: ldur            w11, [x3, #0xf]
    // 0xea979c: DecompressPointer r11
    //     0xea979c: add             x11, x11, HEAP, lsl #32
    // 0xea97a0: ArrayLoad: r13 = r11[r6]  ; Unknown_4
    //     0xea97a0: add             x16, x11, x6, lsl #2
    //     0xea97a4: ldur            w13, [x16, #0xf]
    // 0xea97a8: DecompressPointer r13
    //     0xea97a8: add             x13, x13, HEAP, lsl #32
    // 0xea97ac: r14 = LoadInt32Instr(r13)
    //     0xea97ac: sbfx            x14, x13, #1, #0x1f
    //     0xea97b0: tbz             w13, #0, #0xea97b8
    //     0xea97b4: ldur            x14, [x13, #7]
    // 0xea97b8: ubfx            x12, x12, #0, #0x20
    // 0xea97bc: add             w13, w12, w14
    // 0xea97c0: and             x12, x13, x4
    // 0xea97c4: mov             x13, x12
    // 0xea97c8: ubfx            x13, x13, #0, #0x20
    // 0xea97cc: lsl             x14, x13, #1
    // 0xea97d0: ubfx            x12, x12, #0, #0x20
    // 0xea97d4: asr             x13, x12, #0xf
    // 0xea97d8: orr             x12, x14, x13
    // 0xea97dc: mov             x13, x12
    // 0xea97e0: ubfx            x13, x13, #0, #0x20
    // 0xea97e4: mvn             w14, w13
    // 0xea97e8: mov             x13, x9
    // 0xea97ec: ubfx            x13, x13, #0, #0x20
    // 0xea97f0: and             x19, x13, x14
    // 0xea97f4: ubfx            x19, x19, #0, #0x20
    // 0xea97f8: add             x13, x8, x19
    // 0xea97fc: mov             x14, x10
    // 0xea9800: ubfx            x14, x14, #0, #0x20
    // 0xea9804: mov             x19, x12
    // 0xea9808: ubfx            x19, x19, #0, #0x20
    // 0xea980c: and             x20, x14, x19
    // 0xea9810: ubfx            x20, x20, #0, #0x20
    // 0xea9814: add             x14, x13, x20
    // 0xea9818: add             x8, x6, #1
    // 0xea981c: mov             x0, x7
    // 0xea9820: mov             x1, x8
    // 0xea9824: cmp             x1, x0
    // 0xea9828: b.hs            #0xeaa0d8
    // 0xea982c: ArrayLoad: r13 = r11[r8]  ; Unknown_4
    //     0xea982c: add             x16, x11, x8, lsl #2
    //     0xea9830: ldur            w13, [x16, #0xf]
    // 0xea9834: DecompressPointer r13
    //     0xea9834: add             x13, x13, HEAP, lsl #32
    // 0xea9838: r19 = LoadInt32Instr(r13)
    //     0xea9838: sbfx            x19, x13, #1, #0x1f
    //     0xea983c: tbz             w13, #0, #0xea9844
    //     0xea9840: ldur            x19, [x13, #7]
    // 0xea9844: ubfx            x14, x14, #0, #0x20
    // 0xea9848: add             w13, w14, w19
    // 0xea984c: and             x14, x13, x4
    // 0xea9850: mov             x13, x14
    // 0xea9854: ubfx            x13, x13, #0, #0x20
    // 0xea9858: lsl             x19, x13, #2
    // 0xea985c: ubfx            x14, x14, #0, #0x20
    // 0xea9860: asr             x13, x14, #0xe
    // 0xea9864: orr             x8, x19, x13
    // 0xea9868: mov             x13, x8
    // 0xea986c: ubfx            x13, x13, #0, #0x20
    // 0xea9870: mvn             w14, w13
    // 0xea9874: mov             x13, x10
    // 0xea9878: ubfx            x13, x13, #0, #0x20
    // 0xea987c: and             x19, x13, x14
    // 0xea9880: ubfx            x19, x19, #0, #0x20
    // 0xea9884: add             x13, x9, x19
    // 0xea9888: mov             x14, x12
    // 0xea988c: ubfx            x14, x14, #0, #0x20
    // 0xea9890: mov             x19, x8
    // 0xea9894: ubfx            x19, x19, #0, #0x20
    // 0xea9898: and             x20, x14, x19
    // 0xea989c: ubfx            x20, x20, #0, #0x20
    // 0xea98a0: add             x14, x13, x20
    // 0xea98a4: add             x9, x6, #2
    // 0xea98a8: mov             x0, x7
    // 0xea98ac: mov             x1, x9
    // 0xea98b0: cmp             x1, x0
    // 0xea98b4: b.hs            #0xeaa0dc
    // 0xea98b8: ArrayLoad: r13 = r11[r9]  ; Unknown_4
    //     0xea98b8: add             x16, x11, x9, lsl #2
    //     0xea98bc: ldur            w13, [x16, #0xf]
    // 0xea98c0: DecompressPointer r13
    //     0xea98c0: add             x13, x13, HEAP, lsl #32
    // 0xea98c4: r19 = LoadInt32Instr(r13)
    //     0xea98c4: sbfx            x19, x13, #1, #0x1f
    //     0xea98c8: tbz             w13, #0, #0xea98d0
    //     0xea98cc: ldur            x19, [x13, #7]
    // 0xea98d0: ubfx            x14, x14, #0, #0x20
    // 0xea98d4: add             w13, w14, w19
    // 0xea98d8: and             x14, x13, x4
    // 0xea98dc: mov             x13, x14
    // 0xea98e0: ubfx            x13, x13, #0, #0x20
    // 0xea98e4: lsl             x19, x13, #3
    // 0xea98e8: ubfx            x14, x14, #0, #0x20
    // 0xea98ec: asr             x13, x14, #0xd
    // 0xea98f0: orr             x9, x19, x13
    // 0xea98f4: mov             x13, x9
    // 0xea98f8: ubfx            x13, x13, #0, #0x20
    // 0xea98fc: mvn             w14, w13
    // 0xea9900: mov             x13, x12
    // 0xea9904: ubfx            x13, x13, #0, #0x20
    // 0xea9908: and             x19, x13, x14
    // 0xea990c: ubfx            x19, x19, #0, #0x20
    // 0xea9910: add             x13, x10, x19
    // 0xea9914: mov             x14, x8
    // 0xea9918: ubfx            x14, x14, #0, #0x20
    // 0xea991c: mov             x19, x9
    // 0xea9920: ubfx            x19, x19, #0, #0x20
    // 0xea9924: and             x20, x14, x19
    // 0xea9928: ubfx            x20, x20, #0, #0x20
    // 0xea992c: add             x14, x13, x20
    // 0xea9930: add             x10, x6, #3
    // 0xea9934: mov             x0, x7
    // 0xea9938: mov             x1, x10
    // 0xea993c: cmp             x1, x0
    // 0xea9940: b.hs            #0xeaa0e0
    // 0xea9944: ArrayLoad: r13 = r11[r10]  ; Unknown_4
    //     0xea9944: add             x16, x11, x10, lsl #2
    //     0xea9948: ldur            w13, [x16, #0xf]
    // 0xea994c: DecompressPointer r13
    //     0xea994c: add             x13, x13, HEAP, lsl #32
    // 0xea9950: r11 = LoadInt32Instr(r13)
    //     0xea9950: sbfx            x11, x13, #1, #0x1f
    //     0xea9954: tbz             w13, #0, #0xea995c
    //     0xea9958: ldur            x11, [x13, #7]
    // 0xea995c: ubfx            x14, x14, #0, #0x20
    // 0xea9960: add             w13, w14, w11
    // 0xea9964: and             x11, x13, x4
    // 0xea9968: mov             x13, x11
    // 0xea996c: ubfx            x13, x13, #0, #0x20
    // 0xea9970: lsl             x14, x13, #5
    // 0xea9974: ubfx            x11, x11, #0, #0x20
    // 0xea9978: asr             x13, x11, #0xb
    // 0xea997c: orr             x10, x14, x13
    // 0xea9980: add             x0, x6, #4
    // 0xea9984: mov             x7, x12
    // 0xea9988: mov             x6, x0
    // 0xea998c: b               #0xea9728
    // 0xea9990: r6 = 63
    //     0xea9990: movz            x6, #0x3f
    // 0xea9994: cmp             w3, NULL
    // 0xea9998: b.eq            #0xeaa0e4
    // 0xea999c: mov             x11, x10
    // 0xea99a0: ubfx            x11, x11, #0, #0x20
    // 0xea99a4: and             x12, x11, x6
    // 0xea99a8: LoadField: r11 = r3->field_b
    //     0xea99a8: ldur            w11, [x3, #0xb]
    // 0xea99ac: r13 = LoadInt32Instr(r11)
    //     0xea99ac: sbfx            x13, x11, #1, #0x1f
    // 0xea99b0: ubfx            x12, x12, #0, #0x20
    // 0xea99b4: mov             x0, x13
    // 0xea99b8: mov             x1, x12
    // 0xea99bc: cmp             x1, x0
    // 0xea99c0: b.hs            #0xeaa0e8
    // 0xea99c4: LoadField: r11 = r3->field_f
    //     0xea99c4: ldur            w11, [x3, #0xf]
    // 0xea99c8: DecompressPointer r11
    //     0xea99c8: add             x11, x11, HEAP, lsl #32
    // 0xea99cc: ArrayLoad: r3 = r11[r12]  ; Unknown_4
    //     0xea99cc: add             x16, x11, x12, lsl #2
    //     0xea99d0: ldur            w3, [x16, #0xf]
    // 0xea99d4: DecompressPointer r3
    //     0xea99d4: add             x3, x3, HEAP, lsl #32
    // 0xea99d8: r12 = LoadInt32Instr(r3)
    //     0xea99d8: sbfx            x12, x3, #1, #0x1f
    //     0xea99dc: tbz             w3, #0, #0xea99e4
    //     0xea99e0: ldur            x12, [x3, #7]
    // 0xea99e4: add             x3, x7, x12
    // 0xea99e8: mov             x7, x3
    // 0xea99ec: ubfx            x7, x7, #0, #0x20
    // 0xea99f0: and             x12, x7, x6
    // 0xea99f4: ubfx            x12, x12, #0, #0x20
    // 0xea99f8: mov             x0, x13
    // 0xea99fc: mov             x1, x12
    // 0xea9a00: cmp             x1, x0
    // 0xea9a04: b.hs            #0xeaa0ec
    // 0xea9a08: ArrayLoad: r7 = r11[r12]  ; Unknown_4
    //     0xea9a08: add             x16, x11, x12, lsl #2
    //     0xea9a0c: ldur            w7, [x16, #0xf]
    // 0xea9a10: DecompressPointer r7
    //     0xea9a10: add             x7, x7, HEAP, lsl #32
    // 0xea9a14: r12 = LoadInt32Instr(r7)
    //     0xea9a14: sbfx            x12, x7, #1, #0x1f
    //     0xea9a18: tbz             w7, #0, #0xea9a20
    //     0xea9a1c: ldur            x12, [x7, #7]
    // 0xea9a20: add             x7, x8, x12
    // 0xea9a24: mov             x8, x7
    // 0xea9a28: ubfx            x8, x8, #0, #0x20
    // 0xea9a2c: and             x12, x8, x6
    // 0xea9a30: ubfx            x12, x12, #0, #0x20
    // 0xea9a34: mov             x0, x13
    // 0xea9a38: mov             x1, x12
    // 0xea9a3c: cmp             x1, x0
    // 0xea9a40: b.hs            #0xeaa0f0
    // 0xea9a44: ArrayLoad: r8 = r11[r12]  ; Unknown_4
    //     0xea9a44: add             x16, x11, x12, lsl #2
    //     0xea9a48: ldur            w8, [x16, #0xf]
    // 0xea9a4c: DecompressPointer r8
    //     0xea9a4c: add             x8, x8, HEAP, lsl #32
    // 0xea9a50: r12 = LoadInt32Instr(r8)
    //     0xea9a50: sbfx            x12, x8, #1, #0x1f
    //     0xea9a54: tbz             w8, #0, #0xea9a5c
    //     0xea9a58: ldur            x12, [x8, #7]
    // 0xea9a5c: add             x8, x9, x12
    // 0xea9a60: mov             x9, x8
    // 0xea9a64: ubfx            x9, x9, #0, #0x20
    // 0xea9a68: and             x12, x9, x6
    // 0xea9a6c: ubfx            x12, x12, #0, #0x20
    // 0xea9a70: mov             x0, x13
    // 0xea9a74: mov             x1, x12
    // 0xea9a78: cmp             x1, x0
    // 0xea9a7c: b.hs            #0xeaa0f4
    // 0xea9a80: ArrayLoad: r9 = r11[r12]  ; Unknown_4
    //     0xea9a80: add             x16, x11, x12, lsl #2
    //     0xea9a84: ldur            w9, [x16, #0xf]
    // 0xea9a88: DecompressPointer r9
    //     0xea9a88: add             x9, x9, HEAP, lsl #32
    // 0xea9a8c: r12 = LoadInt32Instr(r9)
    //     0xea9a8c: sbfx            x12, x9, #1, #0x1f
    //     0xea9a90: tbz             w9, #0, #0xea9a98
    //     0xea9a94: ldur            x12, [x9, #7]
    // 0xea9a98: add             x9, x10, x12
    // 0xea9a9c: mov             x10, x9
    // 0xea9aa0: mov             x9, x8
    // 0xea9aa4: mov             x8, x7
    // 0xea9aa8: mov             x7, x3
    // 0xea9aac: r3 = 20
    //     0xea9aac: movz            x3, #0x14
    // 0xea9ab0: CheckStackOverflow
    //     0xea9ab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9ab4: cmp             SP, x16
    //     0xea9ab8: b.ls            #0xeaa0f8
    // 0xea9abc: cmp             x3, #0x28
    // 0xea9ac0: b.gt            #0xea9cac
    // 0xea9ac4: mvn             x12, x10
    // 0xea9ac8: and             x14, x8, x12
    // 0xea9acc: add             x12, x7, x14
    // 0xea9ad0: and             x14, x9, x10
    // 0xea9ad4: add             x19, x12, x14
    // 0xea9ad8: mov             x0, x13
    // 0xea9adc: mov             x1, x3
    // 0xea9ae0: cmp             x1, x0
    // 0xea9ae4: b.hs            #0xeaa100
    // 0xea9ae8: ArrayLoad: r12 = r11[r3]  ; Unknown_4
    //     0xea9ae8: add             x16, x11, x3, lsl #2
    //     0xea9aec: ldur            w12, [x16, #0xf]
    // 0xea9af0: DecompressPointer r12
    //     0xea9af0: add             x12, x12, HEAP, lsl #32
    // 0xea9af4: r14 = LoadInt32Instr(r12)
    //     0xea9af4: sbfx            x14, x12, #1, #0x1f
    //     0xea9af8: tbz             w12, #0, #0xea9b00
    //     0xea9afc: ldur            x14, [x12, #7]
    // 0xea9b00: ubfx            x19, x19, #0, #0x20
    // 0xea9b04: add             w12, w19, w14
    // 0xea9b08: and             x14, x12, x4
    // 0xea9b0c: mov             x12, x14
    // 0xea9b10: ubfx            x12, x12, #0, #0x20
    // 0xea9b14: lsl             x19, x12, #1
    // 0xea9b18: ubfx            x14, x14, #0, #0x20
    // 0xea9b1c: asr             x12, x14, #0xf
    // 0xea9b20: orr             x7, x19, x12
    // 0xea9b24: mvn             x12, x7
    // 0xea9b28: and             x14, x9, x12
    // 0xea9b2c: add             x12, x8, x14
    // 0xea9b30: mov             x14, x10
    // 0xea9b34: ubfx            x14, x14, #0, #0x20
    // 0xea9b38: mov             x19, x7
    // 0xea9b3c: ubfx            x19, x19, #0, #0x20
    // 0xea9b40: and             x20, x14, x19
    // 0xea9b44: ubfx            x20, x20, #0, #0x20
    // 0xea9b48: add             x14, x12, x20
    // 0xea9b4c: add             x8, x3, #1
    // 0xea9b50: mov             x0, x13
    // 0xea9b54: mov             x1, x8
    // 0xea9b58: cmp             x1, x0
    // 0xea9b5c: b.hs            #0xeaa104
    // 0xea9b60: ArrayLoad: r12 = r11[r8]  ; Unknown_4
    //     0xea9b60: add             x16, x11, x8, lsl #2
    //     0xea9b64: ldur            w12, [x16, #0xf]
    // 0xea9b68: DecompressPointer r12
    //     0xea9b68: add             x12, x12, HEAP, lsl #32
    // 0xea9b6c: r19 = LoadInt32Instr(r12)
    //     0xea9b6c: sbfx            x19, x12, #1, #0x1f
    //     0xea9b70: tbz             w12, #0, #0xea9b78
    //     0xea9b74: ldur            x19, [x12, #7]
    // 0xea9b78: ubfx            x14, x14, #0, #0x20
    // 0xea9b7c: add             w12, w14, w19
    // 0xea9b80: and             x14, x12, x4
    // 0xea9b84: mov             x12, x14
    // 0xea9b88: ubfx            x12, x12, #0, #0x20
    // 0xea9b8c: lsl             x19, x12, #2
    // 0xea9b90: ubfx            x14, x14, #0, #0x20
    // 0xea9b94: asr             x12, x14, #0xe
    // 0xea9b98: orr             x8, x19, x12
    // 0xea9b9c: mvn             x12, x8
    // 0xea9ba0: and             x14, x10, x12
    // 0xea9ba4: add             x12, x9, x14
    // 0xea9ba8: mov             x14, x7
    // 0xea9bac: ubfx            x14, x14, #0, #0x20
    // 0xea9bb0: mov             x19, x8
    // 0xea9bb4: ubfx            x19, x19, #0, #0x20
    // 0xea9bb8: and             x20, x14, x19
    // 0xea9bbc: ubfx            x20, x20, #0, #0x20
    // 0xea9bc0: add             x14, x12, x20
    // 0xea9bc4: add             x9, x3, #2
    // 0xea9bc8: mov             x0, x13
    // 0xea9bcc: mov             x1, x9
    // 0xea9bd0: cmp             x1, x0
    // 0xea9bd4: b.hs            #0xeaa108
    // 0xea9bd8: ArrayLoad: r12 = r11[r9]  ; Unknown_4
    //     0xea9bd8: add             x16, x11, x9, lsl #2
    //     0xea9bdc: ldur            w12, [x16, #0xf]
    // 0xea9be0: DecompressPointer r12
    //     0xea9be0: add             x12, x12, HEAP, lsl #32
    // 0xea9be4: r19 = LoadInt32Instr(r12)
    //     0xea9be4: sbfx            x19, x12, #1, #0x1f
    //     0xea9be8: tbz             w12, #0, #0xea9bf0
    //     0xea9bec: ldur            x19, [x12, #7]
    // 0xea9bf0: ubfx            x14, x14, #0, #0x20
    // 0xea9bf4: add             w12, w14, w19
    // 0xea9bf8: and             x14, x12, x4
    // 0xea9bfc: mov             x12, x14
    // 0xea9c00: ubfx            x12, x12, #0, #0x20
    // 0xea9c04: lsl             x19, x12, #3
    // 0xea9c08: ubfx            x14, x14, #0, #0x20
    // 0xea9c0c: asr             x12, x14, #0xd
    // 0xea9c10: orr             x9, x19, x12
    // 0xea9c14: mov             x12, x9
    // 0xea9c18: ubfx            x12, x12, #0, #0x20
    // 0xea9c1c: mvn             w14, w12
    // 0xea9c20: mov             x12, x7
    // 0xea9c24: ubfx            x12, x12, #0, #0x20
    // 0xea9c28: and             x19, x12, x14
    // 0xea9c2c: ubfx            x19, x19, #0, #0x20
    // 0xea9c30: add             x12, x10, x19
    // 0xea9c34: mov             x14, x8
    // 0xea9c38: ubfx            x14, x14, #0, #0x20
    // 0xea9c3c: mov             x19, x9
    // 0xea9c40: ubfx            x19, x19, #0, #0x20
    // 0xea9c44: and             x20, x14, x19
    // 0xea9c48: ubfx            x20, x20, #0, #0x20
    // 0xea9c4c: add             x14, x12, x20
    // 0xea9c50: add             x10, x3, #3
    // 0xea9c54: mov             x0, x13
    // 0xea9c58: mov             x1, x10
    // 0xea9c5c: cmp             x1, x0
    // 0xea9c60: b.hs            #0xeaa10c
    // 0xea9c64: ArrayLoad: r12 = r11[r10]  ; Unknown_4
    //     0xea9c64: add             x16, x11, x10, lsl #2
    //     0xea9c68: ldur            w12, [x16, #0xf]
    // 0xea9c6c: DecompressPointer r12
    //     0xea9c6c: add             x12, x12, HEAP, lsl #32
    // 0xea9c70: r19 = LoadInt32Instr(r12)
    //     0xea9c70: sbfx            x19, x12, #1, #0x1f
    //     0xea9c74: tbz             w12, #0, #0xea9c7c
    //     0xea9c78: ldur            x19, [x12, #7]
    // 0xea9c7c: ubfx            x14, x14, #0, #0x20
    // 0xea9c80: add             w12, w14, w19
    // 0xea9c84: and             x14, x12, x4
    // 0xea9c88: mov             x12, x14
    // 0xea9c8c: ubfx            x12, x12, #0, #0x20
    // 0xea9c90: lsl             x19, x12, #5
    // 0xea9c94: ubfx            x14, x14, #0, #0x20
    // 0xea9c98: asr             x12, x14, #0xb
    // 0xea9c9c: orr             x10, x19, x12
    // 0xea9ca0: add             x0, x3, #4
    // 0xea9ca4: mov             x3, x0
    // 0xea9ca8: b               #0xea9ab0
    // 0xea9cac: mov             x3, x10
    // 0xea9cb0: ubfx            x3, x3, #0, #0x20
    // 0xea9cb4: and             x12, x3, x6
    // 0xea9cb8: ubfx            x12, x12, #0, #0x20
    // 0xea9cbc: mov             x0, x13
    // 0xea9cc0: mov             x1, x12
    // 0xea9cc4: cmp             x1, x0
    // 0xea9cc8: b.hs            #0xeaa110
    // 0xea9ccc: ArrayLoad: r3 = r11[r12]  ; Unknown_4
    //     0xea9ccc: add             x16, x11, x12, lsl #2
    //     0xea9cd0: ldur            w3, [x16, #0xf]
    // 0xea9cd4: DecompressPointer r3
    //     0xea9cd4: add             x3, x3, HEAP, lsl #32
    // 0xea9cd8: r12 = LoadInt32Instr(r3)
    //     0xea9cd8: sbfx            x12, x3, #1, #0x1f
    //     0xea9cdc: tbz             w3, #0, #0xea9ce4
    //     0xea9ce0: ldur            x12, [x3, #7]
    // 0xea9ce4: add             x3, x7, x12
    // 0xea9ce8: mov             x7, x3
    // 0xea9cec: ubfx            x7, x7, #0, #0x20
    // 0xea9cf0: and             x12, x7, x6
    // 0xea9cf4: ubfx            x12, x12, #0, #0x20
    // 0xea9cf8: mov             x0, x13
    // 0xea9cfc: mov             x1, x12
    // 0xea9d00: cmp             x1, x0
    // 0xea9d04: b.hs            #0xeaa114
    // 0xea9d08: ArrayLoad: r7 = r11[r12]  ; Unknown_4
    //     0xea9d08: add             x16, x11, x12, lsl #2
    //     0xea9d0c: ldur            w7, [x16, #0xf]
    // 0xea9d10: DecompressPointer r7
    //     0xea9d10: add             x7, x7, HEAP, lsl #32
    // 0xea9d14: r12 = LoadInt32Instr(r7)
    //     0xea9d14: sbfx            x12, x7, #1, #0x1f
    //     0xea9d18: tbz             w7, #0, #0xea9d20
    //     0xea9d1c: ldur            x12, [x7, #7]
    // 0xea9d20: add             x7, x8, x12
    // 0xea9d24: mov             x8, x7
    // 0xea9d28: ubfx            x8, x8, #0, #0x20
    // 0xea9d2c: and             x12, x8, x6
    // 0xea9d30: ubfx            x12, x12, #0, #0x20
    // 0xea9d34: mov             x0, x13
    // 0xea9d38: mov             x1, x12
    // 0xea9d3c: cmp             x1, x0
    // 0xea9d40: b.hs            #0xeaa118
    // 0xea9d44: ArrayLoad: r8 = r11[r12]  ; Unknown_4
    //     0xea9d44: add             x16, x11, x12, lsl #2
    //     0xea9d48: ldur            w8, [x16, #0xf]
    // 0xea9d4c: DecompressPointer r8
    //     0xea9d4c: add             x8, x8, HEAP, lsl #32
    // 0xea9d50: r12 = LoadInt32Instr(r8)
    //     0xea9d50: sbfx            x12, x8, #1, #0x1f
    //     0xea9d54: tbz             w8, #0, #0xea9d5c
    //     0xea9d58: ldur            x12, [x8, #7]
    // 0xea9d5c: add             x8, x9, x12
    // 0xea9d60: mov             x9, x8
    // 0xea9d64: ubfx            x9, x9, #0, #0x20
    // 0xea9d68: and             x12, x9, x6
    // 0xea9d6c: ubfx            x12, x12, #0, #0x20
    // 0xea9d70: mov             x0, x13
    // 0xea9d74: mov             x1, x12
    // 0xea9d78: cmp             x1, x0
    // 0xea9d7c: b.hs            #0xeaa11c
    // 0xea9d80: ArrayLoad: r6 = r11[r12]  ; Unknown_4
    //     0xea9d80: add             x16, x11, x12, lsl #2
    //     0xea9d84: ldur            w6, [x16, #0xf]
    // 0xea9d88: DecompressPointer r6
    //     0xea9d88: add             x6, x6, HEAP, lsl #32
    // 0xea9d8c: r9 = LoadInt32Instr(r6)
    //     0xea9d8c: sbfx            x9, x6, #1, #0x1f
    //     0xea9d90: tbz             w6, #0, #0xea9d98
    //     0xea9d94: ldur            x9, [x6, #7]
    // 0xea9d98: add             x6, x10, x9
    // 0xea9d9c: mov             x9, x6
    // 0xea9da0: mov             x6, x3
    // 0xea9da4: r3 = 44
    //     0xea9da4: movz            x3, #0x2c
    // 0xea9da8: CheckStackOverflow
    //     0xea9da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9dac: cmp             SP, x16
    //     0xea9db0: b.ls            #0xeaa120
    // 0xea9db4: cmp             x3, #0x40
    // 0xea9db8: b.ge            #0xea9fa4
    // 0xea9dbc: mvn             x10, x9
    // 0xea9dc0: and             x12, x7, x10
    // 0xea9dc4: add             x10, x6, x12
    // 0xea9dc8: and             x12, x8, x9
    // 0xea9dcc: add             x14, x10, x12
    // 0xea9dd0: mov             x0, x13
    // 0xea9dd4: mov             x1, x3
    // 0xea9dd8: cmp             x1, x0
    // 0xea9ddc: b.hs            #0xeaa128
    // 0xea9de0: ArrayLoad: r10 = r11[r3]  ; Unknown_4
    //     0xea9de0: add             x16, x11, x3, lsl #2
    //     0xea9de4: ldur            w10, [x16, #0xf]
    // 0xea9de8: DecompressPointer r10
    //     0xea9de8: add             x10, x10, HEAP, lsl #32
    // 0xea9dec: r12 = LoadInt32Instr(r10)
    //     0xea9dec: sbfx            x12, x10, #1, #0x1f
    //     0xea9df0: tbz             w10, #0, #0xea9df8
    //     0xea9df4: ldur            x12, [x10, #7]
    // 0xea9df8: ubfx            x14, x14, #0, #0x20
    // 0xea9dfc: add             w10, w14, w12
    // 0xea9e00: and             x12, x10, x4
    // 0xea9e04: mov             x10, x12
    // 0xea9e08: ubfx            x10, x10, #0, #0x20
    // 0xea9e0c: lsl             x14, x10, #1
    // 0xea9e10: ubfx            x12, x12, #0, #0x20
    // 0xea9e14: asr             x10, x12, #0xf
    // 0xea9e18: orr             x6, x14, x10
    // 0xea9e1c: mvn             x10, x6
    // 0xea9e20: and             x12, x8, x10
    // 0xea9e24: add             x10, x7, x12
    // 0xea9e28: mov             x12, x9
    // 0xea9e2c: ubfx            x12, x12, #0, #0x20
    // 0xea9e30: mov             x14, x6
    // 0xea9e34: ubfx            x14, x14, #0, #0x20
    // 0xea9e38: and             x19, x12, x14
    // 0xea9e3c: ubfx            x19, x19, #0, #0x20
    // 0xea9e40: add             x12, x10, x19
    // 0xea9e44: add             x7, x3, #1
    // 0xea9e48: mov             x0, x13
    // 0xea9e4c: mov             x1, x7
    // 0xea9e50: cmp             x1, x0
    // 0xea9e54: b.hs            #0xeaa12c
    // 0xea9e58: ArrayLoad: r10 = r11[r7]  ; Unknown_4
    //     0xea9e58: add             x16, x11, x7, lsl #2
    //     0xea9e5c: ldur            w10, [x16, #0xf]
    // 0xea9e60: DecompressPointer r10
    //     0xea9e60: add             x10, x10, HEAP, lsl #32
    // 0xea9e64: r14 = LoadInt32Instr(r10)
    //     0xea9e64: sbfx            x14, x10, #1, #0x1f
    //     0xea9e68: tbz             w10, #0, #0xea9e70
    //     0xea9e6c: ldur            x14, [x10, #7]
    // 0xea9e70: ubfx            x12, x12, #0, #0x20
    // 0xea9e74: add             w10, w12, w14
    // 0xea9e78: and             x12, x10, x4
    // 0xea9e7c: mov             x10, x12
    // 0xea9e80: ubfx            x10, x10, #0, #0x20
    // 0xea9e84: lsl             x14, x10, #2
    // 0xea9e88: ubfx            x12, x12, #0, #0x20
    // 0xea9e8c: asr             x10, x12, #0xe
    // 0xea9e90: orr             x7, x14, x10
    // 0xea9e94: mvn             x10, x7
    // 0xea9e98: and             x12, x9, x10
    // 0xea9e9c: add             x10, x8, x12
    // 0xea9ea0: mov             x12, x6
    // 0xea9ea4: ubfx            x12, x12, #0, #0x20
    // 0xea9ea8: mov             x14, x7
    // 0xea9eac: ubfx            x14, x14, #0, #0x20
    // 0xea9eb0: and             x19, x12, x14
    // 0xea9eb4: ubfx            x19, x19, #0, #0x20
    // 0xea9eb8: add             x12, x10, x19
    // 0xea9ebc: add             x8, x3, #2
    // 0xea9ec0: mov             x0, x13
    // 0xea9ec4: mov             x1, x8
    // 0xea9ec8: cmp             x1, x0
    // 0xea9ecc: b.hs            #0xeaa130
    // 0xea9ed0: ArrayLoad: r10 = r11[r8]  ; Unknown_4
    //     0xea9ed0: add             x16, x11, x8, lsl #2
    //     0xea9ed4: ldur            w10, [x16, #0xf]
    // 0xea9ed8: DecompressPointer r10
    //     0xea9ed8: add             x10, x10, HEAP, lsl #32
    // 0xea9edc: r14 = LoadInt32Instr(r10)
    //     0xea9edc: sbfx            x14, x10, #1, #0x1f
    //     0xea9ee0: tbz             w10, #0, #0xea9ee8
    //     0xea9ee4: ldur            x14, [x10, #7]
    // 0xea9ee8: ubfx            x12, x12, #0, #0x20
    // 0xea9eec: add             w10, w12, w14
    // 0xea9ef0: and             x12, x10, x4
    // 0xea9ef4: mov             x10, x12
    // 0xea9ef8: ubfx            x10, x10, #0, #0x20
    // 0xea9efc: lsl             x14, x10, #3
    // 0xea9f00: ubfx            x12, x12, #0, #0x20
    // 0xea9f04: asr             x10, x12, #0xd
    // 0xea9f08: orr             x8, x14, x10
    // 0xea9f0c: mov             x10, x8
    // 0xea9f10: ubfx            x10, x10, #0, #0x20
    // 0xea9f14: mvn             w12, w10
    // 0xea9f18: mov             x10, x6
    // 0xea9f1c: ubfx            x10, x10, #0, #0x20
    // 0xea9f20: and             x14, x10, x12
    // 0xea9f24: ubfx            x14, x14, #0, #0x20
    // 0xea9f28: add             x10, x9, x14
    // 0xea9f2c: mov             x12, x7
    // 0xea9f30: ubfx            x12, x12, #0, #0x20
    // 0xea9f34: mov             x14, x8
    // 0xea9f38: ubfx            x14, x14, #0, #0x20
    // 0xea9f3c: and             x19, x12, x14
    // 0xea9f40: ubfx            x19, x19, #0, #0x20
    // 0xea9f44: add             x12, x10, x19
    // 0xea9f48: add             x9, x3, #3
    // 0xea9f4c: mov             x0, x13
    // 0xea9f50: mov             x1, x9
    // 0xea9f54: cmp             x1, x0
    // 0xea9f58: b.hs            #0xeaa134
    // 0xea9f5c: ArrayLoad: r10 = r11[r9]  ; Unknown_4
    //     0xea9f5c: add             x16, x11, x9, lsl #2
    //     0xea9f60: ldur            w10, [x16, #0xf]
    // 0xea9f64: DecompressPointer r10
    //     0xea9f64: add             x10, x10, HEAP, lsl #32
    // 0xea9f68: r14 = LoadInt32Instr(r10)
    //     0xea9f68: sbfx            x14, x10, #1, #0x1f
    //     0xea9f6c: tbz             w10, #0, #0xea9f74
    //     0xea9f70: ldur            x14, [x10, #7]
    // 0xea9f74: ubfx            x12, x12, #0, #0x20
    // 0xea9f78: add             w10, w12, w14
    // 0xea9f7c: and             x12, x10, x4
    // 0xea9f80: mov             x10, x12
    // 0xea9f84: ubfx            x10, x10, #0, #0x20
    // 0xea9f88: lsl             x14, x10, #5
    // 0xea9f8c: ubfx            x12, x12, #0, #0x20
    // 0xea9f90: asr             x10, x12, #0xb
    // 0xea9f94: orr             x9, x14, x10
    // 0xea9f98: add             x0, x3, #4
    // 0xea9f9c: mov             x3, x0
    // 0xea9fa0: b               #0xea9da8
    // 0xea9fa4: LoadField: r3 = r5->field_13
    //     0xea9fa4: ldur            w3, [x5, #0x13]
    // 0xea9fa8: r4 = LoadInt32Instr(r3)
    //     0xea9fa8: sbfx            x4, x3, #1, #0x1f
    // 0xea9fac: mov             x0, x4
    // 0xea9fb0: mov             x1, x2
    // 0xea9fb4: cmp             x1, x0
    // 0xea9fb8: b.hs            #0xeaa138
    // 0xea9fbc: ArrayStore: r5[r2] = r6  ; TypeUnknown_1
    //     0xea9fbc: add             x3, x5, x2
    //     0xea9fc0: strb            w6, [x3, #0x17]
    // 0xea9fc4: add             x3, x2, #1
    // 0xea9fc8: asr             x10, x6, #8
    // 0xea9fcc: mov             x0, x4
    // 0xea9fd0: mov             x1, x3
    // 0xea9fd4: cmp             x1, x0
    // 0xea9fd8: b.hs            #0xeaa13c
    // 0xea9fdc: ArrayStore: r5[r3] = r10  ; TypeUnknown_1
    //     0xea9fdc: add             x6, x5, x3
    //     0xea9fe0: strb            w10, [x6, #0x17]
    // 0xea9fe4: add             x3, x2, #2
    // 0xea9fe8: mov             x0, x4
    // 0xea9fec: mov             x1, x3
    // 0xea9ff0: cmp             x1, x0
    // 0xea9ff4: b.hs            #0xeaa140
    // 0xea9ff8: ArrayStore: r5[r3] = r7  ; TypeUnknown_1
    //     0xea9ff8: add             x6, x5, x3
    //     0xea9ffc: strb            w7, [x6, #0x17]
    // 0xeaa000: add             x3, x2, #3
    // 0xeaa004: asr             x6, x7, #8
    // 0xeaa008: mov             x0, x4
    // 0xeaa00c: mov             x1, x3
    // 0xeaa010: cmp             x1, x0
    // 0xeaa014: b.hs            #0xeaa144
    // 0xeaa018: ArrayStore: r5[r3] = r6  ; TypeUnknown_1
    //     0xeaa018: add             x7, x5, x3
    //     0xeaa01c: strb            w6, [x7, #0x17]
    // 0xeaa020: add             x3, x2, #4
    // 0xeaa024: mov             x0, x4
    // 0xeaa028: mov             x1, x3
    // 0xeaa02c: cmp             x1, x0
    // 0xeaa030: b.hs            #0xeaa148
    // 0xeaa034: ArrayStore: r5[r3] = r8  ; TypeUnknown_1
    //     0xeaa034: add             x6, x5, x3
    //     0xeaa038: strb            w8, [x6, #0x17]
    // 0xeaa03c: add             x3, x2, #5
    // 0xeaa040: asr             x6, x8, #8
    // 0xeaa044: mov             x0, x4
    // 0xeaa048: mov             x1, x3
    // 0xeaa04c: cmp             x1, x0
    // 0xeaa050: b.hs            #0xeaa14c
    // 0xeaa054: ArrayStore: r5[r3] = r6  ; TypeUnknown_1
    //     0xeaa054: add             x7, x5, x3
    //     0xeaa058: strb            w6, [x7, #0x17]
    // 0xeaa05c: add             x3, x2, #6
    // 0xeaa060: mov             x0, x4
    // 0xeaa064: mov             x1, x3
    // 0xeaa068: cmp             x1, x0
    // 0xeaa06c: b.hs            #0xeaa150
    // 0xeaa070: ArrayStore: r5[r3] = r9  ; TypeUnknown_1
    //     0xeaa070: add             x6, x5, x3
    //     0xeaa074: strb            w9, [x6, #0x17]
    // 0xeaa078: add             x3, x2, #7
    // 0xeaa07c: asr             x2, x9, #8
    // 0xeaa080: mov             x0, x4
    // 0xeaa084: mov             x1, x3
    // 0xeaa088: cmp             x1, x0
    // 0xeaa08c: b.hs            #0xeaa154
    // 0xeaa090: ArrayStore: r5[r3] = r2  ; TypeUnknown_1
    //     0xeaa090: add             x1, x5, x3
    //     0xeaa094: strb            w2, [x1, #0x17]
    // 0xeaa098: r0 = Null
    //     0xeaa098: mov             x0, NULL
    // 0xeaa09c: LeaveFrame
    //     0xeaa09c: mov             SP, fp
    //     0xeaa0a0: ldp             fp, lr, [SP], #0x10
    // 0xeaa0a4: ret
    //     0xeaa0a4: ret             
    // 0xeaa0a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa0c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa0cc: b               #0xea9734
    // 0xeaa0d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeaa0d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeaa0d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeaa0e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeaa0e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa0f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa0f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa0f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa0fc: b               #0xea9abc
    // 0xeaa100: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa100: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa104: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa104: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa108: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa108: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa10c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa10c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa110: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa110: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa114: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa114: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa118: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa118: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa11c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa11c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa124: b               #0xea9db4
    // 0xeaa128: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa128: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa12c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa12c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa130: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa130: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa134: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa134: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa138: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa138: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa13c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa13c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa140: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa140: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa144: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa144: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa148: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa148: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa14c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa14c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa150: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa150: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeaa154: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeaa154: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
