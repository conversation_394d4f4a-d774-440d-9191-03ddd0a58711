// lib: impl.digest.blake2b, url: package:pointycastle/digests/blake2b.dart

// class id: 1050941, size: 0x8
class :: {

  static late final Register64List _blake2bIV; // offset: 0xdc8

  static Register64List _blake2bIV() {
    // ** addr: 0x8e4660, size: 0x2fc
    // 0x8e4660: EnterFrame
    //     0x8e4660: stp             fp, lr, [SP, #-0x10]!
    //     0x8e4664: mov             fp, SP
    // 0x8e4668: AllocStack(0x48)
    //     0x8e4668: sub             SP, SP, #0x48
    // 0x8e466c: r0 = 4
    //     0x8e466c: movz            x0, #0x4
    // 0x8e4670: CheckStackOverflow
    //     0x8e4670: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e4674: cmp             SP, x16
    //     0x8e4678: b.ls            #0x8e4954
    // 0x8e467c: mov             x2, x0
    // 0x8e4680: r1 = Null
    //     0x8e4680: mov             x1, NULL
    // 0x8e4684: r0 = AllocateArray()
    //     0x8e4684: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e4688: stur            x0, [fp, #-8]
    // 0x8e468c: r16 = 1779033703
    //     0x8e468c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12360] 0x6a09e667
    //     0x8e4690: ldr             x16, [x16, #0x360]
    // 0x8e4694: StoreField: r0->field_f = r16
    //     0x8e4694: stur            w16, [x0, #0xf]
    // 0x8e4698: r16 = 4089235720
    //     0x8e4698: add             x16, PP, #0x19, lsl #12  ; [pp+0x19480] 0xf3bcc908
    //     0x8e469c: ldr             x16, [x16, #0x480]
    // 0x8e46a0: StoreField: r0->field_13 = r16
    //     0x8e46a0: stur            w16, [x0, #0x13]
    // 0x8e46a4: r1 = <int>
    //     0x8e46a4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8e46a8: r0 = AllocateGrowableArray()
    //     0x8e46a8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8e46ac: mov             x3, x0
    // 0x8e46b0: ldur            x0, [fp, #-8]
    // 0x8e46b4: stur            x3, [fp, #-0x10]
    // 0x8e46b8: StoreField: r3->field_f = r0
    //     0x8e46b8: stur            w0, [x3, #0xf]
    // 0x8e46bc: r0 = 4
    //     0x8e46bc: movz            x0, #0x4
    // 0x8e46c0: StoreField: r3->field_b = r0
    //     0x8e46c0: stur            w0, [x3, #0xb]
    // 0x8e46c4: mov             x2, x0
    // 0x8e46c8: r1 = Null
    //     0x8e46c8: mov             x1, NULL
    // 0x8e46cc: r0 = AllocateArray()
    //     0x8e46cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e46d0: stur            x0, [fp, #-8]
    // 0x8e46d4: r16 = 3144134277
    //     0x8e46d4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12368] 0xbb67ae85
    //     0x8e46d8: ldr             x16, [x16, #0x368]
    // 0x8e46dc: StoreField: r0->field_f = r16
    //     0x8e46dc: stur            w16, [x0, #0xf]
    // 0x8e46e0: r16 = 2227873595
    //     0x8e46e0: add             x16, PP, #0x19, lsl #12  ; [pp+0x19490] 0x84caa73b
    //     0x8e46e4: ldr             x16, [x16, #0x490]
    // 0x8e46e8: StoreField: r0->field_13 = r16
    //     0x8e46e8: stur            w16, [x0, #0x13]
    // 0x8e46ec: r1 = <int>
    //     0x8e46ec: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8e46f0: r0 = AllocateGrowableArray()
    //     0x8e46f0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8e46f4: mov             x3, x0
    // 0x8e46f8: ldur            x0, [fp, #-8]
    // 0x8e46fc: stur            x3, [fp, #-0x18]
    // 0x8e4700: StoreField: r3->field_f = r0
    //     0x8e4700: stur            w0, [x3, #0xf]
    // 0x8e4704: r0 = 4
    //     0x8e4704: movz            x0, #0x4
    // 0x8e4708: StoreField: r3->field_b = r0
    //     0x8e4708: stur            w0, [x3, #0xb]
    // 0x8e470c: mov             x2, x0
    // 0x8e4710: r1 = Null
    //     0x8e4710: mov             x1, NULL
    // 0x8e4714: r0 = AllocateArray()
    //     0x8e4714: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e4718: stur            x0, [fp, #-8]
    // 0x8e471c: r16 = 2027808484
    //     0x8e471c: movz            x16, #0xe6e4
    //     0x8e4720: movk            x16, #0x78dd, lsl #16
    // 0x8e4724: StoreField: r0->field_f = r16
    //     0x8e4724: stur            w16, [x0, #0xf]
    // 0x8e4728: r16 = 4271175723
    //     0x8e4728: add             x16, PP, #0x19, lsl #12  ; [pp+0x19498] 0xfe94f82b
    //     0x8e472c: ldr             x16, [x16, #0x498]
    // 0x8e4730: StoreField: r0->field_13 = r16
    //     0x8e4730: stur            w16, [x0, #0x13]
    // 0x8e4734: r1 = <int>
    //     0x8e4734: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8e4738: r0 = AllocateGrowableArray()
    //     0x8e4738: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8e473c: mov             x3, x0
    // 0x8e4740: ldur            x0, [fp, #-8]
    // 0x8e4744: stur            x3, [fp, #-0x20]
    // 0x8e4748: StoreField: r3->field_f = r0
    //     0x8e4748: stur            w0, [x3, #0xf]
    // 0x8e474c: r0 = 4
    //     0x8e474c: movz            x0, #0x4
    // 0x8e4750: StoreField: r3->field_b = r0
    //     0x8e4750: stur            w0, [x3, #0xb]
    // 0x8e4754: mov             x2, x0
    // 0x8e4758: r1 = Null
    //     0x8e4758: mov             x1, NULL
    // 0x8e475c: r0 = AllocateArray()
    //     0x8e475c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e4760: stur            x0, [fp, #-8]
    // 0x8e4764: r16 = 2773480762
    //     0x8e4764: add             x16, PP, #0x12, lsl #12  ; [pp+0x12370] 0xa54ff53a
    //     0x8e4768: ldr             x16, [x16, #0x370]
    // 0x8e476c: StoreField: r0->field_f = r16
    //     0x8e476c: stur            w16, [x0, #0xf]
    // 0x8e4770: r16 = 1595750129
    //     0x8e4770: add             x16, PP, #0x19, lsl #12  ; [pp+0x194a0] 0x5f1d36f1
    //     0x8e4774: ldr             x16, [x16, #0x4a0]
    // 0x8e4778: StoreField: r0->field_13 = r16
    //     0x8e4778: stur            w16, [x0, #0x13]
    // 0x8e477c: r1 = <int>
    //     0x8e477c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8e4780: r0 = AllocateGrowableArray()
    //     0x8e4780: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8e4784: mov             x3, x0
    // 0x8e4788: ldur            x0, [fp, #-8]
    // 0x8e478c: stur            x3, [fp, #-0x28]
    // 0x8e4790: StoreField: r3->field_f = r0
    //     0x8e4790: stur            w0, [x3, #0xf]
    // 0x8e4794: r0 = 4
    //     0x8e4794: movz            x0, #0x4
    // 0x8e4798: StoreField: r3->field_b = r0
    //     0x8e4798: stur            w0, [x3, #0xb]
    // 0x8e479c: mov             x2, x0
    // 0x8e47a0: r1 = Null
    //     0x8e47a0: mov             x1, NULL
    // 0x8e47a4: r0 = AllocateArray()
    //     0x8e47a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e47a8: stur            x0, [fp, #-8]
    // 0x8e47ac: r16 = 1359893119
    //     0x8e47ac: add             x16, PP, #0x12, lsl #12  ; [pp+0x12378] 0x510e527f
    //     0x8e47b0: ldr             x16, [x16, #0x378]
    // 0x8e47b4: StoreField: r0->field_f = r16
    //     0x8e47b4: stur            w16, [x0, #0xf]
    // 0x8e47b8: r16 = 2917565137
    //     0x8e47b8: add             x16, PP, #0x19, lsl #12  ; [pp+0x194a8] 0xade682d1
    //     0x8e47bc: ldr             x16, [x16, #0x4a8]
    // 0x8e47c0: StoreField: r0->field_13 = r16
    //     0x8e47c0: stur            w16, [x0, #0x13]
    // 0x8e47c4: r1 = <int>
    //     0x8e47c4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8e47c8: r0 = AllocateGrowableArray()
    //     0x8e47c8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8e47cc: mov             x3, x0
    // 0x8e47d0: ldur            x0, [fp, #-8]
    // 0x8e47d4: stur            x3, [fp, #-0x30]
    // 0x8e47d8: StoreField: r3->field_f = r0
    //     0x8e47d8: stur            w0, [x3, #0xf]
    // 0x8e47dc: r0 = 4
    //     0x8e47dc: movz            x0, #0x4
    // 0x8e47e0: StoreField: r3->field_b = r0
    //     0x8e47e0: stur            w0, [x3, #0xb]
    // 0x8e47e4: mov             x2, x0
    // 0x8e47e8: r1 = Null
    //     0x8e47e8: mov             x1, NULL
    // 0x8e47ec: r0 = AllocateArray()
    //     0x8e47ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e47f0: stur            x0, [fp, #-8]
    // 0x8e47f4: r16 = 2600822924
    //     0x8e47f4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12380] 0x9b05688c
    //     0x8e47f8: ldr             x16, [x16, #0x380]
    // 0x8e47fc: StoreField: r0->field_f = r16
    //     0x8e47fc: stur            w16, [x0, #0xf]
    // 0x8e4800: r16 = 1451022398
    //     0x8e4800: movz            x16, #0xd83e
    //     0x8e4804: movk            x16, #0x567c, lsl #16
    // 0x8e4808: StoreField: r0->field_13 = r16
    //     0x8e4808: stur            w16, [x0, #0x13]
    // 0x8e480c: r1 = <int>
    //     0x8e480c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8e4810: r0 = AllocateGrowableArray()
    //     0x8e4810: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8e4814: mov             x3, x0
    // 0x8e4818: ldur            x0, [fp, #-8]
    // 0x8e481c: stur            x3, [fp, #-0x38]
    // 0x8e4820: StoreField: r3->field_f = r0
    //     0x8e4820: stur            w0, [x3, #0xf]
    // 0x8e4824: r0 = 4
    //     0x8e4824: movz            x0, #0x4
    // 0x8e4828: StoreField: r3->field_b = r0
    //     0x8e4828: stur            w0, [x3, #0xb]
    // 0x8e482c: mov             x2, x0
    // 0x8e4830: r1 = Null
    //     0x8e4830: mov             x1, NULL
    // 0x8e4834: r0 = AllocateArray()
    //     0x8e4834: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e4838: stur            x0, [fp, #-8]
    // 0x8e483c: r16 = 1057469270
    //     0x8e483c: movz            x16, #0xb356
    //     0x8e4840: movk            x16, #0x3f07, lsl #16
    // 0x8e4844: StoreField: r0->field_f = r16
    //     0x8e4844: stur            w16, [x0, #0xf]
    // 0x8e4848: r16 = 4215389547
    //     0x8e4848: add             x16, PP, #0x19, lsl #12  ; [pp+0x194b0] 0xfb41bd6b
    //     0x8e484c: ldr             x16, [x16, #0x4b0]
    // 0x8e4850: StoreField: r0->field_13 = r16
    //     0x8e4850: stur            w16, [x0, #0x13]
    // 0x8e4854: r1 = <int>
    //     0x8e4854: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8e4858: r0 = AllocateGrowableArray()
    //     0x8e4858: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8e485c: mov             x3, x0
    // 0x8e4860: ldur            x0, [fp, #-8]
    // 0x8e4864: stur            x3, [fp, #-0x40]
    // 0x8e4868: StoreField: r3->field_f = r0
    //     0x8e4868: stur            w0, [x3, #0xf]
    // 0x8e486c: r0 = 4
    //     0x8e486c: movz            x0, #0x4
    // 0x8e4870: StoreField: r3->field_b = r0
    //     0x8e4870: stur            w0, [x3, #0xb]
    // 0x8e4874: mov             x2, x0
    // 0x8e4878: r1 = Null
    //     0x8e4878: mov             x1, NULL
    // 0x8e487c: r0 = AllocateArray()
    //     0x8e487c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e4880: stur            x0, [fp, #-8]
    // 0x8e4884: r16 = 1541459225
    //     0x8e4884: add             x16, PP, #0x12, lsl #12  ; [pp+0x12388] 0x5be0cd19
    //     0x8e4888: ldr             x16, [x16, #0x388]
    // 0x8e488c: StoreField: r0->field_f = r16
    //     0x8e488c: stur            w16, [x0, #0xf]
    // 0x8e4890: r16 = 654066418
    //     0x8e4890: movz            x16, #0x42f2
    //     0x8e4894: movk            x16, #0x26fc, lsl #16
    // 0x8e4898: StoreField: r0->field_13 = r16
    //     0x8e4898: stur            w16, [x0, #0x13]
    // 0x8e489c: r1 = <int>
    //     0x8e489c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8e48a0: r0 = AllocateGrowableArray()
    //     0x8e48a0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8e48a4: mov             x3, x0
    // 0x8e48a8: ldur            x0, [fp, #-8]
    // 0x8e48ac: stur            x3, [fp, #-0x48]
    // 0x8e48b0: StoreField: r3->field_f = r0
    //     0x8e48b0: stur            w0, [x3, #0xf]
    // 0x8e48b4: r0 = 4
    //     0x8e48b4: movz            x0, #0x4
    // 0x8e48b8: StoreField: r3->field_b = r0
    //     0x8e48b8: stur            w0, [x3, #0xb]
    // 0x8e48bc: r1 = Null
    //     0x8e48bc: mov             x1, NULL
    // 0x8e48c0: r2 = 16
    //     0x8e48c0: movz            x2, #0x10
    // 0x8e48c4: r0 = AllocateArray()
    //     0x8e48c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e48c8: mov             x2, x0
    // 0x8e48cc: ldur            x0, [fp, #-0x10]
    // 0x8e48d0: stur            x2, [fp, #-8]
    // 0x8e48d4: StoreField: r2->field_f = r0
    //     0x8e48d4: stur            w0, [x2, #0xf]
    // 0x8e48d8: ldur            x0, [fp, #-0x18]
    // 0x8e48dc: StoreField: r2->field_13 = r0
    //     0x8e48dc: stur            w0, [x2, #0x13]
    // 0x8e48e0: ldur            x0, [fp, #-0x20]
    // 0x8e48e4: ArrayStore: r2[0] = r0  ; List_4
    //     0x8e48e4: stur            w0, [x2, #0x17]
    // 0x8e48e8: ldur            x0, [fp, #-0x28]
    // 0x8e48ec: StoreField: r2->field_1b = r0
    //     0x8e48ec: stur            w0, [x2, #0x1b]
    // 0x8e48f0: ldur            x0, [fp, #-0x30]
    // 0x8e48f4: StoreField: r2->field_1f = r0
    //     0x8e48f4: stur            w0, [x2, #0x1f]
    // 0x8e48f8: ldur            x0, [fp, #-0x38]
    // 0x8e48fc: StoreField: r2->field_23 = r0
    //     0x8e48fc: stur            w0, [x2, #0x23]
    // 0x8e4900: ldur            x0, [fp, #-0x40]
    // 0x8e4904: StoreField: r2->field_27 = r0
    //     0x8e4904: stur            w0, [x2, #0x27]
    // 0x8e4908: ldur            x0, [fp, #-0x48]
    // 0x8e490c: StoreField: r2->field_2b = r0
    //     0x8e490c: stur            w0, [x2, #0x2b]
    // 0x8e4910: r1 = <List<int>>
    //     0x8e4910: ldr             x1, [PP, #0x14c0]  ; [pp+0x14c0] TypeArguments: <List<int>>
    // 0x8e4914: r0 = AllocateGrowableArray()
    //     0x8e4914: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8e4918: mov             x1, x0
    // 0x8e491c: ldur            x0, [fp, #-8]
    // 0x8e4920: stur            x1, [fp, #-0x10]
    // 0x8e4924: StoreField: r1->field_f = r0
    //     0x8e4924: stur            w0, [x1, #0xf]
    // 0x8e4928: r0 = 16
    //     0x8e4928: movz            x0, #0x10
    // 0x8e492c: StoreField: r1->field_b = r0
    //     0x8e492c: stur            w0, [x1, #0xb]
    // 0x8e4930: r0 = Register64List()
    //     0x8e4930: bl              #0x8d72c4  ; AllocateRegister64ListStub -> Register64List (size=0xc)
    // 0x8e4934: mov             x1, x0
    // 0x8e4938: ldur            x2, [fp, #-0x10]
    // 0x8e493c: stur            x0, [fp, #-8]
    // 0x8e4940: r0 = Register64List.from()
    //     0x8e4940: bl              #0x8e495c  ; [package:pointycastle/src/ufixnum.dart] Register64List::Register64List.from
    // 0x8e4944: ldur            x0, [fp, #-8]
    // 0x8e4948: LeaveFrame
    //     0x8e4948: mov             SP, fp
    //     0x8e494c: ldp             fp, lr, [SP], #0x10
    // 0x8e4950: ret
    //     0x8e4950: ret             
    // 0x8e4954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e4954: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e4958: b               #0x8e467c
  }
}

// class id: 668, size: 0x24, field offset: 0x8
class Blake2bDigest extends BaseDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xdc4

  get _ algorithmName(/* No info */) {
    // ** addr: 0x869ae8, size: 0xc
    // 0x869ae8: r0 = "Blake2b"
    //     0x869ae8: add             x0, PP, #0x19, lsl #12  ; [pp+0x199f8] "Blake2b"
    //     0x869aec: ldr             x0, [x0, #0x9f8]
    // 0x869af0: ret
    //     0x869af0: ret             
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e3d7c, size: 0x58
    // 0x8e3d7c: EnterFrame
    //     0x8e3d7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3d80: mov             fp, SP
    // 0x8e3d84: AllocStack(0x8)
    //     0x8e3d84: sub             SP, SP, #8
    // 0x8e3d88: r0 = StaticFactoryConfig()
    //     0x8e3d88: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e3d8c: mov             x3, x0
    // 0x8e3d90: r0 = "Blake2b"
    //     0x8e3d90: add             x0, PP, #0x19, lsl #12  ; [pp+0x199f8] "Blake2b"
    //     0x8e3d94: ldr             x0, [x0, #0x9f8]
    // 0x8e3d98: stur            x3, [fp, #-8]
    // 0x8e3d9c: StoreField: r3->field_b = r0
    //     0x8e3d9c: stur            w0, [x3, #0xb]
    // 0x8e3da0: r1 = Function '<anonymous closure>': static.
    //     0x8e3da0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19a00] AnonymousClosure: static (0x8e3dd4), in [package:pointycastle/digests/blake2b.dart] Blake2bDigest::factoryConfig (0x8e3d7c)
    //     0x8e3da4: ldr             x1, [x1, #0xa00]
    // 0x8e3da8: r2 = Null
    //     0x8e3da8: mov             x2, NULL
    // 0x8e3dac: r0 = AllocateClosure()
    //     0x8e3dac: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3db0: mov             x1, x0
    // 0x8e3db4: ldur            x0, [fp, #-8]
    // 0x8e3db8: StoreField: r0->field_f = r1
    //     0x8e3db8: stur            w1, [x0, #0xf]
    // 0x8e3dbc: r1 = Digest
    //     0x8e3dbc: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3dc0: ldr             x1, [x1, #0x388]
    // 0x8e3dc4: StoreField: r0->field_7 = r1
    //     0x8e3dc4: stur            w1, [x0, #7]
    // 0x8e3dc8: LeaveFrame
    //     0x8e3dc8: mov             SP, fp
    //     0x8e3dcc: ldp             fp, lr, [SP], #0x10
    // 0x8e3dd0: ret
    //     0x8e3dd0: ret             
  }
  [closure] static Blake2bDigest <anonymous closure>(dynamic) {
    // ** addr: 0x8e3dd4, size: 0x40
    // 0x8e3dd4: EnterFrame
    //     0x8e3dd4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3dd8: mov             fp, SP
    // 0x8e3ddc: AllocStack(0x8)
    //     0x8e3ddc: sub             SP, SP, #8
    // 0x8e3de0: CheckStackOverflow
    //     0x8e3de0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3de4: cmp             SP, x16
    //     0x8e3de8: b.ls            #0x8e3e0c
    // 0x8e3dec: r0 = Blake2bDigest()
    //     0x8e3dec: bl              #0x8e4b50  ; AllocateBlake2bDigestStub -> Blake2bDigest (size=0x24)
    // 0x8e3df0: mov             x1, x0
    // 0x8e3df4: stur            x0, [fp, #-8]
    // 0x8e3df8: r0 = Blake2bDigest()
    //     0x8e3df8: bl              #0x8e3e14  ; [package:pointycastle/digests/blake2b.dart] Blake2bDigest::Blake2bDigest
    // 0x8e3dfc: ldur            x0, [fp, #-8]
    // 0x8e3e00: LeaveFrame
    //     0x8e3e00: mov             SP, fp
    //     0x8e3e04: ldp             fp, lr, [SP], #0x10
    // 0x8e3e08: ret
    //     0x8e3e08: ret             
    // 0x8e3e0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3e0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3e10: b               #0x8e3dec
  }
  _ Blake2bDigest(/* No info */) {
    // ** addr: 0x8e3e14, size: 0x244
    // 0x8e3e14: EnterFrame
    //     0x8e3e14: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3e18: mov             fp, SP
    // 0x8e3e1c: AllocStack(0x30)
    //     0x8e3e1c: sub             SP, SP, #0x30
    // 0x8e3e20: r0 = 64
    //     0x8e3e20: movz            x0, #0x40
    // 0x8e3e24: mov             x3, x1
    // 0x8e3e28: stur            x1, [fp, #-8]
    // 0x8e3e2c: CheckStackOverflow
    //     0x8e3e2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3e30: cmp             SP, x16
    //     0x8e3e34: b.ls            #0x8e4040
    // 0x8e3e38: StoreField: r3->field_7 = r0
    //     0x8e3e38: stur            x0, [x3, #7]
    // 0x8e3e3c: StoreField: r3->field_f = rZR
    //     0x8e3e3c: stur            xzr, [x3, #0xf]
    // 0x8e3e40: r1 = <Register64>
    //     0x8e3e40: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8e3e44: ldr             x1, [x1, #0x418]
    // 0x8e3e48: r2 = 16
    //     0x8e3e48: movz            x2, #0x10
    // 0x8e3e4c: r0 = _GrowableList()
    //     0x8e3e4c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8e3e50: LoadField: r1 = r0->field_b
    //     0x8e3e50: ldur            w1, [x0, #0xb]
    // 0x8e3e54: r2 = LoadInt32Instr(r1)
    //     0x8e3e54: sbfx            x2, x1, #1, #0x1f
    // 0x8e3e58: stur            x2, [fp, #-0x20]
    // 0x8e3e5c: LoadField: r1 = r0->field_f
    //     0x8e3e5c: ldur            w1, [x0, #0xf]
    // 0x8e3e60: DecompressPointer r1
    //     0x8e3e60: add             x1, x1, HEAP, lsl #32
    // 0x8e3e64: stur            x1, [fp, #-0x18]
    // 0x8e3e68: r0 = 0
    //     0x8e3e68: movz            x0, #0
    // 0x8e3e6c: stur            x0, [fp, #-0x10]
    // 0x8e3e70: CheckStackOverflow
    //     0x8e3e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3e74: cmp             SP, x16
    //     0x8e3e78: b.ls            #0x8e4048
    // 0x8e3e7c: cmp             x0, x2
    // 0x8e3e80: b.ge            #0x8e3ef4
    // 0x8e3e84: r0 = Register64()
    //     0x8e3e84: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e3e88: mov             x3, x0
    // 0x8e3e8c: r0 = Sentinel
    //     0x8e3e8c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e3e90: stur            x3, [fp, #-0x28]
    // 0x8e3e94: StoreField: r3->field_7 = r0
    //     0x8e3e94: stur            w0, [x3, #7]
    // 0x8e3e98: StoreField: r3->field_b = r0
    //     0x8e3e98: stur            w0, [x3, #0xb]
    // 0x8e3e9c: str             NULL, [SP]
    // 0x8e3ea0: mov             x1, x3
    // 0x8e3ea4: r2 = 0
    //     0x8e3ea4: movz            x2, #0
    // 0x8e3ea8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e3ea8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e3eac: r0 = set()
    //     0x8e3eac: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e3eb0: ldur            x1, [fp, #-0x18]
    // 0x8e3eb4: ldur            x0, [fp, #-0x28]
    // 0x8e3eb8: ldur            x2, [fp, #-0x10]
    // 0x8e3ebc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8e3ebc: add             x25, x1, x2, lsl #2
    //     0x8e3ec0: add             x25, x25, #0xf
    //     0x8e3ec4: str             w0, [x25]
    //     0x8e3ec8: tbz             w0, #0, #0x8e3ee4
    //     0x8e3ecc: ldurb           w16, [x1, #-1]
    //     0x8e3ed0: ldurb           w17, [x0, #-1]
    //     0x8e3ed4: and             x16, x17, x16, lsr #2
    //     0x8e3ed8: tst             x16, HEAP, lsr #32
    //     0x8e3edc: b.eq            #0x8e3ee4
    //     0x8e3ee0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e3ee4: add             x0, x2, #1
    // 0x8e3ee8: ldur            x1, [fp, #-0x18]
    // 0x8e3eec: ldur            x2, [fp, #-0x20]
    // 0x8e3ef0: b               #0x8e3e6c
    // 0x8e3ef4: r0 = Register64()
    //     0x8e3ef4: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e3ef8: mov             x1, x0
    // 0x8e3efc: r0 = Sentinel
    //     0x8e3efc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e3f00: StoreField: r1->field_7 = r0
    //     0x8e3f00: stur            w0, [x1, #7]
    // 0x8e3f04: StoreField: r1->field_b = r0
    //     0x8e3f04: stur            w0, [x1, #0xb]
    // 0x8e3f08: str             NULL, [SP]
    // 0x8e3f0c: r2 = 0
    //     0x8e3f0c: movz            x2, #0
    // 0x8e3f10: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e3f10: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e3f14: r0 = set()
    //     0x8e3f14: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e3f18: r0 = Register64()
    //     0x8e3f18: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e3f1c: mov             x1, x0
    // 0x8e3f20: r0 = Sentinel
    //     0x8e3f20: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e3f24: StoreField: r1->field_7 = r0
    //     0x8e3f24: stur            w0, [x1, #7]
    // 0x8e3f28: StoreField: r1->field_b = r0
    //     0x8e3f28: stur            w0, [x1, #0xb]
    // 0x8e3f2c: str             NULL, [SP]
    // 0x8e3f30: r2 = 0
    //     0x8e3f30: movz            x2, #0
    // 0x8e3f34: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e3f34: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e3f38: r0 = set()
    //     0x8e3f38: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e3f3c: r0 = Register64()
    //     0x8e3f3c: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e3f40: mov             x1, x0
    // 0x8e3f44: r0 = Sentinel
    //     0x8e3f44: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e3f48: StoreField: r1->field_7 = r0
    //     0x8e3f48: stur            w0, [x1, #7]
    // 0x8e3f4c: StoreField: r1->field_b = r0
    //     0x8e3f4c: stur            w0, [x1, #0xb]
    // 0x8e3f50: str             NULL, [SP]
    // 0x8e3f54: r2 = 0
    //     0x8e3f54: movz            x2, #0
    // 0x8e3f58: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e3f58: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e3f5c: r0 = set()
    //     0x8e3f5c: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e3f60: r1 = <Register64>
    //     0x8e3f60: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8e3f64: ldr             x1, [x1, #0x418]
    // 0x8e3f68: r2 = 16
    //     0x8e3f68: movz            x2, #0x10
    // 0x8e3f6c: r0 = _GrowableList()
    //     0x8e3f6c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8e3f70: LoadField: r1 = r0->field_b
    //     0x8e3f70: ldur            w1, [x0, #0xb]
    // 0x8e3f74: r2 = LoadInt32Instr(r1)
    //     0x8e3f74: sbfx            x2, x1, #1, #0x1f
    // 0x8e3f78: stur            x2, [fp, #-0x20]
    // 0x8e3f7c: LoadField: r1 = r0->field_f
    //     0x8e3f7c: ldur            w1, [x0, #0xf]
    // 0x8e3f80: DecompressPointer r1
    //     0x8e3f80: add             x1, x1, HEAP, lsl #32
    // 0x8e3f84: stur            x1, [fp, #-0x18]
    // 0x8e3f88: r0 = 0
    //     0x8e3f88: movz            x0, #0
    // 0x8e3f8c: stur            x0, [fp, #-0x10]
    // 0x8e3f90: CheckStackOverflow
    //     0x8e3f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3f94: cmp             SP, x16
    //     0x8e3f98: b.ls            #0x8e4050
    // 0x8e3f9c: cmp             x0, x2
    // 0x8e3fa0: b.ge            #0x8e4014
    // 0x8e3fa4: r0 = Register64()
    //     0x8e3fa4: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e3fa8: mov             x3, x0
    // 0x8e3fac: r0 = Sentinel
    //     0x8e3fac: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e3fb0: stur            x3, [fp, #-0x28]
    // 0x8e3fb4: StoreField: r3->field_7 = r0
    //     0x8e3fb4: stur            w0, [x3, #7]
    // 0x8e3fb8: StoreField: r3->field_b = r0
    //     0x8e3fb8: stur            w0, [x3, #0xb]
    // 0x8e3fbc: str             NULL, [SP]
    // 0x8e3fc0: mov             x1, x3
    // 0x8e3fc4: r2 = 0
    //     0x8e3fc4: movz            x2, #0
    // 0x8e3fc8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e3fc8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e3fcc: r0 = set()
    //     0x8e3fcc: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e3fd0: ldur            x1, [fp, #-0x18]
    // 0x8e3fd4: ldur            x0, [fp, #-0x28]
    // 0x8e3fd8: ldur            x2, [fp, #-0x10]
    // 0x8e3fdc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8e3fdc: add             x25, x1, x2, lsl #2
    //     0x8e3fe0: add             x25, x25, #0xf
    //     0x8e3fe4: str             w0, [x25]
    //     0x8e3fe8: tbz             w0, #0, #0x8e4004
    //     0x8e3fec: ldurb           w16, [x1, #-1]
    //     0x8e3ff0: ldurb           w17, [x0, #-1]
    //     0x8e3ff4: and             x16, x17, x16, lsr #2
    //     0x8e3ff8: tst             x16, HEAP, lsr #32
    //     0x8e3ffc: b.eq            #0x8e4004
    //     0x8e4000: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e4004: add             x0, x2, #1
    // 0x8e4008: ldur            x1, [fp, #-0x18]
    // 0x8e400c: ldur            x2, [fp, #-0x20]
    // 0x8e4010: b               #0x8e3f8c
    // 0x8e4014: ldur            x1, [fp, #-8]
    // 0x8e4018: r4 = 256
    //     0x8e4018: movz            x4, #0x100
    // 0x8e401c: r0 = AllocateUint8Array()
    //     0x8e401c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e4020: ldur            x1, [fp, #-8]
    // 0x8e4024: r0 = 64
    //     0x8e4024: movz            x0, #0x40
    // 0x8e4028: StoreField: r1->field_7 = r0
    //     0x8e4028: stur            x0, [x1, #7]
    // 0x8e402c: r0 = init()
    //     0x8e402c: bl              #0x8e4058  ; [package:pointycastle/digests/blake2b.dart] Blake2bDigest::init
    // 0x8e4030: r0 = Null
    //     0x8e4030: mov             x0, NULL
    // 0x8e4034: LeaveFrame
    //     0x8e4034: mov             SP, fp
    //     0x8e4038: ldp             fp, lr, [SP], #0x10
    // 0x8e403c: ret
    //     0x8e403c: ret             
    // 0x8e4040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e4040: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e4044: b               #0x8e3e38
    // 0x8e4048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e4048: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e404c: b               #0x8e3e7c
    // 0x8e4050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e4050: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e4054: b               #0x8e3f9c
  }
  _ init(/* No info */) {
    // ** addr: 0x8e4058, size: 0x608
    // 0x8e4058: EnterFrame
    //     0x8e4058: stp             fp, lr, [SP, #-0x10]!
    //     0x8e405c: mov             fp, SP
    // 0x8e4060: AllocStack(0x38)
    //     0x8e4060: sub             SP, SP, #0x38
    // 0x8e4064: SetupParameters(Blake2bDigest this /* r1 => r0, fp-0x8 */)
    //     0x8e4064: mov             x0, x1
    //     0x8e4068: stur            x1, [fp, #-8]
    // 0x8e406c: CheckStackOverflow
    //     0x8e406c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e4070: cmp             SP, x16
    //     0x8e4074: b.ls            #0x8e45f0
    // 0x8e4078: LoadField: r1 = r0->field_1f
    //     0x8e4078: ldur            w1, [x0, #0x1f]
    // 0x8e407c: DecompressPointer r1
    //     0x8e407c: add             x1, x1, HEAP, lsl #32
    // 0x8e4080: cmp             w1, NULL
    // 0x8e4084: b.ne            #0x8e45e0
    // 0x8e4088: r1 = <Register64>
    //     0x8e4088: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8e408c: ldr             x1, [x1, #0x418]
    // 0x8e4090: r2 = 8
    //     0x8e4090: movz            x2, #0x8
    // 0x8e4094: r0 = _GrowableList()
    //     0x8e4094: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8e4098: stur            x0, [fp, #-0x18]
    // 0x8e409c: r1 = 0
    //     0x8e409c: movz            x1, #0
    // 0x8e40a0: stur            x1, [fp, #-0x10]
    // 0x8e40a4: CheckStackOverflow
    //     0x8e40a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e40a8: cmp             SP, x16
    //     0x8e40ac: b.ls            #0x8e45f8
    // 0x8e40b0: LoadField: r2 = r0->field_b
    //     0x8e40b0: ldur            w2, [x0, #0xb]
    // 0x8e40b4: r3 = LoadInt32Instr(r2)
    //     0x8e40b4: sbfx            x3, x2, #1, #0x1f
    // 0x8e40b8: stur            x3, [fp, #-0x28]
    // 0x8e40bc: cmp             x1, x3
    // 0x8e40c0: b.ge            #0x8e4150
    // 0x8e40c4: r0 = Register64()
    //     0x8e40c4: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e40c8: mov             x3, x0
    // 0x8e40cc: r0 = Sentinel
    //     0x8e40cc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e40d0: stur            x3, [fp, #-0x20]
    // 0x8e40d4: StoreField: r3->field_7 = r0
    //     0x8e40d4: stur            w0, [x3, #7]
    // 0x8e40d8: StoreField: r3->field_b = r0
    //     0x8e40d8: stur            w0, [x3, #0xb]
    // 0x8e40dc: str             NULL, [SP]
    // 0x8e40e0: mov             x1, x3
    // 0x8e40e4: r2 = 0
    //     0x8e40e4: movz            x2, #0
    // 0x8e40e8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e40e8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e40ec: r0 = set()
    //     0x8e40ec: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e40f0: ldur            x2, [fp, #-0x18]
    // 0x8e40f4: LoadField: r0 = r2->field_b
    //     0x8e40f4: ldur            w0, [x2, #0xb]
    // 0x8e40f8: r1 = LoadInt32Instr(r0)
    //     0x8e40f8: sbfx            x1, x0, #1, #0x1f
    // 0x8e40fc: mov             x0, x1
    // 0x8e4100: ldur            x1, [fp, #-0x10]
    // 0x8e4104: cmp             x1, x0
    // 0x8e4108: b.hs            #0x8e4600
    // 0x8e410c: LoadField: r1 = r2->field_f
    //     0x8e410c: ldur            w1, [x2, #0xf]
    // 0x8e4110: DecompressPointer r1
    //     0x8e4110: add             x1, x1, HEAP, lsl #32
    // 0x8e4114: ldur            x0, [fp, #-0x20]
    // 0x8e4118: ldur            x3, [fp, #-0x10]
    // 0x8e411c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8e411c: add             x25, x1, x3, lsl #2
    //     0x8e4120: add             x25, x25, #0xf
    //     0x8e4124: str             w0, [x25]
    //     0x8e4128: tbz             w0, #0, #0x8e4144
    //     0x8e412c: ldurb           w16, [x1, #-1]
    //     0x8e4130: ldurb           w17, [x0, #-1]
    //     0x8e4134: and             x16, x17, x16, lsr #2
    //     0x8e4138: tst             x16, HEAP, lsr #32
    //     0x8e413c: b.eq            #0x8e4144
    //     0x8e4140: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e4144: add             x1, x3, #1
    // 0x8e4148: mov             x0, x2
    // 0x8e414c: b               #0x8e40a0
    // 0x8e4150: mov             x2, x0
    // 0x8e4154: ldur            x0, [fp, #-8]
    // 0x8e4158: r0 = Register64List()
    //     0x8e4158: bl              #0x8d72c4  ; AllocateRegister64ListStub -> Register64List (size=0xc)
    // 0x8e415c: ldur            x2, [fp, #-0x18]
    // 0x8e4160: StoreField: r0->field_7 = r2
    //     0x8e4160: stur            w2, [x0, #7]
    // 0x8e4164: ldur            x3, [fp, #-8]
    // 0x8e4168: StoreField: r3->field_1f = r0
    //     0x8e4168: stur            w0, [x3, #0x1f]
    //     0x8e416c: ldurb           w16, [x3, #-1]
    //     0x8e4170: ldurb           w17, [x0, #-1]
    //     0x8e4174: and             x16, x17, x16, lsr #2
    //     0x8e4178: tst             x16, HEAP, lsr #32
    //     0x8e417c: b.eq            #0x8e4184
    //     0x8e4180: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8e4184: ldur            x0, [fp, #-0x28]
    // 0x8e4188: r1 = 0
    //     0x8e4188: movz            x1, #0
    // 0x8e418c: cmp             x1, x0
    // 0x8e4190: b.hs            #0x8e4604
    // 0x8e4194: LoadField: r0 = r2->field_f
    //     0x8e4194: ldur            w0, [x2, #0xf]
    // 0x8e4198: DecompressPointer r0
    //     0x8e4198: add             x0, x0, HEAP, lsl #32
    // 0x8e419c: LoadField: r1 = r0->field_f
    //     0x8e419c: ldur            w1, [x0, #0xf]
    // 0x8e41a0: DecompressPointer r1
    //     0x8e41a0: add             x1, x1, HEAP, lsl #32
    // 0x8e41a4: stur            x1, [fp, #-0x18]
    // 0x8e41a8: r0 = InitLateStaticField(0xdc8) // [package:pointycastle/digests/blake2b.dart] ::_blake2bIV
    //     0x8e41a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e41ac: ldr             x0, [x0, #0x1b90]
    //     0x8e41b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e41b4: cmp             w0, w16
    //     0x8e41b8: b.ne            #0x8e41c8
    //     0x8e41bc: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a08] Field <::._blake2bIV@918051828>: static late final (offset: 0xdc8)
    //     0x8e41c0: ldr             x2, [x2, #0xa08]
    //     0x8e41c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e41c8: LoadField: r3 = r0->field_7
    //     0x8e41c8: ldur            w3, [x0, #7]
    // 0x8e41cc: DecompressPointer r3
    //     0x8e41cc: add             x3, x3, HEAP, lsl #32
    // 0x8e41d0: stur            x3, [fp, #-0x20]
    // 0x8e41d4: LoadField: r0 = r3->field_b
    //     0x8e41d4: ldur            w0, [x3, #0xb]
    // 0x8e41d8: r1 = LoadInt32Instr(r0)
    //     0x8e41d8: sbfx            x1, x0, #1, #0x1f
    // 0x8e41dc: mov             x0, x1
    // 0x8e41e0: r1 = 0
    //     0x8e41e0: movz            x1, #0
    // 0x8e41e4: cmp             x1, x0
    // 0x8e41e8: b.hs            #0x8e4608
    // 0x8e41ec: LoadField: r0 = r3->field_f
    //     0x8e41ec: ldur            w0, [x3, #0xf]
    // 0x8e41f0: DecompressPointer r0
    //     0x8e41f0: add             x0, x0, HEAP, lsl #32
    // 0x8e41f4: LoadField: r2 = r0->field_f
    //     0x8e41f4: ldur            w2, [x0, #0xf]
    // 0x8e41f8: DecompressPointer r2
    //     0x8e41f8: add             x2, x2, HEAP, lsl #32
    // 0x8e41fc: ldur            x1, [fp, #-0x18]
    // 0x8e4200: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8e4200: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8e4204: r0 = set()
    //     0x8e4204: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e4208: r0 = Register64()
    //     0x8e4208: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e420c: mov             x3, x0
    // 0x8e4210: r0 = Sentinel
    //     0x8e4210: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e4214: stur            x3, [fp, #-0x30]
    // 0x8e4218: StoreField: r3->field_7 = r0
    //     0x8e4218: stur            w0, [x3, #7]
    // 0x8e421c: StoreField: r3->field_b = r0
    //     0x8e421c: stur            w0, [x3, #0xb]
    // 0x8e4220: str             NULL, [SP]
    // 0x8e4224: mov             x1, x3
    // 0x8e4228: r2 = 33685632
    //     0x8e4228: movz            x2, #0x80
    //     0x8e422c: movk            x2, #0x202, lsl #16
    // 0x8e4230: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e4230: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e4234: r0 = set()
    //     0x8e4234: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e4238: ldur            x1, [fp, #-0x18]
    // 0x8e423c: ldur            x2, [fp, #-0x30]
    // 0x8e4240: r0 = xor()
    //     0x8e4240: bl              #0x8e26bc  ; [package:pointycastle/src/ufixnum.dart] Register64::xor
    // 0x8e4244: ldur            x3, [fp, #-8]
    // 0x8e4248: LoadField: r0 = r3->field_1f
    //     0x8e4248: ldur            w0, [x3, #0x1f]
    // 0x8e424c: DecompressPointer r0
    //     0x8e424c: add             x0, x0, HEAP, lsl #32
    // 0x8e4250: cmp             w0, NULL
    // 0x8e4254: b.eq            #0x8e460c
    // 0x8e4258: LoadField: r2 = r0->field_7
    //     0x8e4258: ldur            w2, [x0, #7]
    // 0x8e425c: DecompressPointer r2
    //     0x8e425c: add             x2, x2, HEAP, lsl #32
    // 0x8e4260: LoadField: r0 = r2->field_b
    //     0x8e4260: ldur            w0, [x2, #0xb]
    // 0x8e4264: r1 = LoadInt32Instr(r0)
    //     0x8e4264: sbfx            x1, x0, #1, #0x1f
    // 0x8e4268: mov             x0, x1
    // 0x8e426c: r1 = 1
    //     0x8e426c: movz            x1, #0x1
    // 0x8e4270: cmp             x1, x0
    // 0x8e4274: b.hs            #0x8e4610
    // 0x8e4278: LoadField: r0 = r2->field_f
    //     0x8e4278: ldur            w0, [x2, #0xf]
    // 0x8e427c: DecompressPointer r0
    //     0x8e427c: add             x0, x0, HEAP, lsl #32
    // 0x8e4280: LoadField: r2 = r0->field_13
    //     0x8e4280: ldur            w2, [x0, #0x13]
    // 0x8e4284: DecompressPointer r2
    //     0x8e4284: add             x2, x2, HEAP, lsl #32
    // 0x8e4288: ldur            x4, [fp, #-0x20]
    // 0x8e428c: LoadField: r0 = r4->field_b
    //     0x8e428c: ldur            w0, [x4, #0xb]
    // 0x8e4290: r1 = LoadInt32Instr(r0)
    //     0x8e4290: sbfx            x1, x0, #1, #0x1f
    // 0x8e4294: mov             x0, x1
    // 0x8e4298: r1 = 1
    //     0x8e4298: movz            x1, #0x1
    // 0x8e429c: cmp             x1, x0
    // 0x8e42a0: b.hs            #0x8e4614
    // 0x8e42a4: LoadField: r0 = r4->field_f
    //     0x8e42a4: ldur            w0, [x4, #0xf]
    // 0x8e42a8: DecompressPointer r0
    //     0x8e42a8: add             x0, x0, HEAP, lsl #32
    // 0x8e42ac: LoadField: r1 = r0->field_13
    //     0x8e42ac: ldur            w1, [x0, #0x13]
    // 0x8e42b0: DecompressPointer r1
    //     0x8e42b0: add             x1, x1, HEAP, lsl #32
    // 0x8e42b4: mov             x16, x1
    // 0x8e42b8: mov             x1, x2
    // 0x8e42bc: mov             x2, x16
    // 0x8e42c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8e42c0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8e42c4: r0 = set()
    //     0x8e42c4: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e42c8: ldur            x3, [fp, #-8]
    // 0x8e42cc: LoadField: r0 = r3->field_1f
    //     0x8e42cc: ldur            w0, [x3, #0x1f]
    // 0x8e42d0: DecompressPointer r0
    //     0x8e42d0: add             x0, x0, HEAP, lsl #32
    // 0x8e42d4: cmp             w0, NULL
    // 0x8e42d8: b.eq            #0x8e4618
    // 0x8e42dc: LoadField: r2 = r0->field_7
    //     0x8e42dc: ldur            w2, [x0, #7]
    // 0x8e42e0: DecompressPointer r2
    //     0x8e42e0: add             x2, x2, HEAP, lsl #32
    // 0x8e42e4: LoadField: r0 = r2->field_b
    //     0x8e42e4: ldur            w0, [x2, #0xb]
    // 0x8e42e8: r1 = LoadInt32Instr(r0)
    //     0x8e42e8: sbfx            x1, x0, #1, #0x1f
    // 0x8e42ec: mov             x0, x1
    // 0x8e42f0: r1 = 2
    //     0x8e42f0: movz            x1, #0x2
    // 0x8e42f4: cmp             x1, x0
    // 0x8e42f8: b.hs            #0x8e461c
    // 0x8e42fc: LoadField: r0 = r2->field_f
    //     0x8e42fc: ldur            w0, [x2, #0xf]
    // 0x8e4300: DecompressPointer r0
    //     0x8e4300: add             x0, x0, HEAP, lsl #32
    // 0x8e4304: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8e4304: ldur            w2, [x0, #0x17]
    // 0x8e4308: DecompressPointer r2
    //     0x8e4308: add             x2, x2, HEAP, lsl #32
    // 0x8e430c: ldur            x4, [fp, #-0x20]
    // 0x8e4310: LoadField: r0 = r4->field_b
    //     0x8e4310: ldur            w0, [x4, #0xb]
    // 0x8e4314: r1 = LoadInt32Instr(r0)
    //     0x8e4314: sbfx            x1, x0, #1, #0x1f
    // 0x8e4318: mov             x0, x1
    // 0x8e431c: r1 = 2
    //     0x8e431c: movz            x1, #0x2
    // 0x8e4320: cmp             x1, x0
    // 0x8e4324: b.hs            #0x8e4620
    // 0x8e4328: LoadField: r0 = r4->field_f
    //     0x8e4328: ldur            w0, [x4, #0xf]
    // 0x8e432c: DecompressPointer r0
    //     0x8e432c: add             x0, x0, HEAP, lsl #32
    // 0x8e4330: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8e4330: ldur            w1, [x0, #0x17]
    // 0x8e4334: DecompressPointer r1
    //     0x8e4334: add             x1, x1, HEAP, lsl #32
    // 0x8e4338: mov             x16, x1
    // 0x8e433c: mov             x1, x2
    // 0x8e4340: mov             x2, x16
    // 0x8e4344: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8e4344: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8e4348: r0 = set()
    //     0x8e4348: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e434c: ldur            x3, [fp, #-8]
    // 0x8e4350: LoadField: r0 = r3->field_1f
    //     0x8e4350: ldur            w0, [x3, #0x1f]
    // 0x8e4354: DecompressPointer r0
    //     0x8e4354: add             x0, x0, HEAP, lsl #32
    // 0x8e4358: cmp             w0, NULL
    // 0x8e435c: b.eq            #0x8e4624
    // 0x8e4360: LoadField: r2 = r0->field_7
    //     0x8e4360: ldur            w2, [x0, #7]
    // 0x8e4364: DecompressPointer r2
    //     0x8e4364: add             x2, x2, HEAP, lsl #32
    // 0x8e4368: LoadField: r0 = r2->field_b
    //     0x8e4368: ldur            w0, [x2, #0xb]
    // 0x8e436c: r1 = LoadInt32Instr(r0)
    //     0x8e436c: sbfx            x1, x0, #1, #0x1f
    // 0x8e4370: mov             x0, x1
    // 0x8e4374: r1 = 3
    //     0x8e4374: movz            x1, #0x3
    // 0x8e4378: cmp             x1, x0
    // 0x8e437c: b.hs            #0x8e4628
    // 0x8e4380: LoadField: r0 = r2->field_f
    //     0x8e4380: ldur            w0, [x2, #0xf]
    // 0x8e4384: DecompressPointer r0
    //     0x8e4384: add             x0, x0, HEAP, lsl #32
    // 0x8e4388: LoadField: r2 = r0->field_1b
    //     0x8e4388: ldur            w2, [x0, #0x1b]
    // 0x8e438c: DecompressPointer r2
    //     0x8e438c: add             x2, x2, HEAP, lsl #32
    // 0x8e4390: ldur            x4, [fp, #-0x20]
    // 0x8e4394: LoadField: r0 = r4->field_b
    //     0x8e4394: ldur            w0, [x4, #0xb]
    // 0x8e4398: r1 = LoadInt32Instr(r0)
    //     0x8e4398: sbfx            x1, x0, #1, #0x1f
    // 0x8e439c: mov             x0, x1
    // 0x8e43a0: r1 = 3
    //     0x8e43a0: movz            x1, #0x3
    // 0x8e43a4: cmp             x1, x0
    // 0x8e43a8: b.hs            #0x8e462c
    // 0x8e43ac: LoadField: r0 = r4->field_f
    //     0x8e43ac: ldur            w0, [x4, #0xf]
    // 0x8e43b0: DecompressPointer r0
    //     0x8e43b0: add             x0, x0, HEAP, lsl #32
    // 0x8e43b4: LoadField: r1 = r0->field_1b
    //     0x8e43b4: ldur            w1, [x0, #0x1b]
    // 0x8e43b8: DecompressPointer r1
    //     0x8e43b8: add             x1, x1, HEAP, lsl #32
    // 0x8e43bc: mov             x16, x1
    // 0x8e43c0: mov             x1, x2
    // 0x8e43c4: mov             x2, x16
    // 0x8e43c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8e43c8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8e43cc: r0 = set()
    //     0x8e43cc: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e43d0: ldur            x3, [fp, #-8]
    // 0x8e43d4: LoadField: r0 = r3->field_1f
    //     0x8e43d4: ldur            w0, [x3, #0x1f]
    // 0x8e43d8: DecompressPointer r0
    //     0x8e43d8: add             x0, x0, HEAP, lsl #32
    // 0x8e43dc: cmp             w0, NULL
    // 0x8e43e0: b.eq            #0x8e4630
    // 0x8e43e4: LoadField: r2 = r0->field_7
    //     0x8e43e4: ldur            w2, [x0, #7]
    // 0x8e43e8: DecompressPointer r2
    //     0x8e43e8: add             x2, x2, HEAP, lsl #32
    // 0x8e43ec: LoadField: r0 = r2->field_b
    //     0x8e43ec: ldur            w0, [x2, #0xb]
    // 0x8e43f0: r1 = LoadInt32Instr(r0)
    //     0x8e43f0: sbfx            x1, x0, #1, #0x1f
    // 0x8e43f4: mov             x0, x1
    // 0x8e43f8: r1 = 4
    //     0x8e43f8: movz            x1, #0x4
    // 0x8e43fc: cmp             x1, x0
    // 0x8e4400: b.hs            #0x8e4634
    // 0x8e4404: LoadField: r0 = r2->field_f
    //     0x8e4404: ldur            w0, [x2, #0xf]
    // 0x8e4408: DecompressPointer r0
    //     0x8e4408: add             x0, x0, HEAP, lsl #32
    // 0x8e440c: LoadField: r2 = r0->field_1f
    //     0x8e440c: ldur            w2, [x0, #0x1f]
    // 0x8e4410: DecompressPointer r2
    //     0x8e4410: add             x2, x2, HEAP, lsl #32
    // 0x8e4414: ldur            x4, [fp, #-0x20]
    // 0x8e4418: LoadField: r0 = r4->field_b
    //     0x8e4418: ldur            w0, [x4, #0xb]
    // 0x8e441c: r1 = LoadInt32Instr(r0)
    //     0x8e441c: sbfx            x1, x0, #1, #0x1f
    // 0x8e4420: mov             x0, x1
    // 0x8e4424: r1 = 4
    //     0x8e4424: movz            x1, #0x4
    // 0x8e4428: cmp             x1, x0
    // 0x8e442c: b.hs            #0x8e4638
    // 0x8e4430: LoadField: r0 = r4->field_f
    //     0x8e4430: ldur            w0, [x4, #0xf]
    // 0x8e4434: DecompressPointer r0
    //     0x8e4434: add             x0, x0, HEAP, lsl #32
    // 0x8e4438: LoadField: r1 = r0->field_1f
    //     0x8e4438: ldur            w1, [x0, #0x1f]
    // 0x8e443c: DecompressPointer r1
    //     0x8e443c: add             x1, x1, HEAP, lsl #32
    // 0x8e4440: mov             x16, x1
    // 0x8e4444: mov             x1, x2
    // 0x8e4448: mov             x2, x16
    // 0x8e444c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8e444c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8e4450: r0 = set()
    //     0x8e4450: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e4454: ldur            x3, [fp, #-8]
    // 0x8e4458: LoadField: r0 = r3->field_1f
    //     0x8e4458: ldur            w0, [x3, #0x1f]
    // 0x8e445c: DecompressPointer r0
    //     0x8e445c: add             x0, x0, HEAP, lsl #32
    // 0x8e4460: cmp             w0, NULL
    // 0x8e4464: b.eq            #0x8e463c
    // 0x8e4468: LoadField: r2 = r0->field_7
    //     0x8e4468: ldur            w2, [x0, #7]
    // 0x8e446c: DecompressPointer r2
    //     0x8e446c: add             x2, x2, HEAP, lsl #32
    // 0x8e4470: LoadField: r0 = r2->field_b
    //     0x8e4470: ldur            w0, [x2, #0xb]
    // 0x8e4474: r1 = LoadInt32Instr(r0)
    //     0x8e4474: sbfx            x1, x0, #1, #0x1f
    // 0x8e4478: mov             x0, x1
    // 0x8e447c: r1 = 5
    //     0x8e447c: movz            x1, #0x5
    // 0x8e4480: cmp             x1, x0
    // 0x8e4484: b.hs            #0x8e4640
    // 0x8e4488: LoadField: r0 = r2->field_f
    //     0x8e4488: ldur            w0, [x2, #0xf]
    // 0x8e448c: DecompressPointer r0
    //     0x8e448c: add             x0, x0, HEAP, lsl #32
    // 0x8e4490: LoadField: r2 = r0->field_23
    //     0x8e4490: ldur            w2, [x0, #0x23]
    // 0x8e4494: DecompressPointer r2
    //     0x8e4494: add             x2, x2, HEAP, lsl #32
    // 0x8e4498: ldur            x4, [fp, #-0x20]
    // 0x8e449c: LoadField: r0 = r4->field_b
    //     0x8e449c: ldur            w0, [x4, #0xb]
    // 0x8e44a0: r1 = LoadInt32Instr(r0)
    //     0x8e44a0: sbfx            x1, x0, #1, #0x1f
    // 0x8e44a4: mov             x0, x1
    // 0x8e44a8: r1 = 5
    //     0x8e44a8: movz            x1, #0x5
    // 0x8e44ac: cmp             x1, x0
    // 0x8e44b0: b.hs            #0x8e4644
    // 0x8e44b4: LoadField: r0 = r4->field_f
    //     0x8e44b4: ldur            w0, [x4, #0xf]
    // 0x8e44b8: DecompressPointer r0
    //     0x8e44b8: add             x0, x0, HEAP, lsl #32
    // 0x8e44bc: LoadField: r1 = r0->field_23
    //     0x8e44bc: ldur            w1, [x0, #0x23]
    // 0x8e44c0: DecompressPointer r1
    //     0x8e44c0: add             x1, x1, HEAP, lsl #32
    // 0x8e44c4: mov             x16, x1
    // 0x8e44c8: mov             x1, x2
    // 0x8e44cc: mov             x2, x16
    // 0x8e44d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8e44d0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8e44d4: r0 = set()
    //     0x8e44d4: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e44d8: ldur            x3, [fp, #-8]
    // 0x8e44dc: LoadField: r0 = r3->field_1f
    //     0x8e44dc: ldur            w0, [x3, #0x1f]
    // 0x8e44e0: DecompressPointer r0
    //     0x8e44e0: add             x0, x0, HEAP, lsl #32
    // 0x8e44e4: cmp             w0, NULL
    // 0x8e44e8: b.eq            #0x8e4648
    // 0x8e44ec: LoadField: r2 = r0->field_7
    //     0x8e44ec: ldur            w2, [x0, #7]
    // 0x8e44f0: DecompressPointer r2
    //     0x8e44f0: add             x2, x2, HEAP, lsl #32
    // 0x8e44f4: LoadField: r0 = r2->field_b
    //     0x8e44f4: ldur            w0, [x2, #0xb]
    // 0x8e44f8: r1 = LoadInt32Instr(r0)
    //     0x8e44f8: sbfx            x1, x0, #1, #0x1f
    // 0x8e44fc: mov             x0, x1
    // 0x8e4500: r1 = 6
    //     0x8e4500: movz            x1, #0x6
    // 0x8e4504: cmp             x1, x0
    // 0x8e4508: b.hs            #0x8e464c
    // 0x8e450c: LoadField: r0 = r2->field_f
    //     0x8e450c: ldur            w0, [x2, #0xf]
    // 0x8e4510: DecompressPointer r0
    //     0x8e4510: add             x0, x0, HEAP, lsl #32
    // 0x8e4514: LoadField: r2 = r0->field_27
    //     0x8e4514: ldur            w2, [x0, #0x27]
    // 0x8e4518: DecompressPointer r2
    //     0x8e4518: add             x2, x2, HEAP, lsl #32
    // 0x8e451c: ldur            x4, [fp, #-0x20]
    // 0x8e4520: LoadField: r0 = r4->field_b
    //     0x8e4520: ldur            w0, [x4, #0xb]
    // 0x8e4524: r1 = LoadInt32Instr(r0)
    //     0x8e4524: sbfx            x1, x0, #1, #0x1f
    // 0x8e4528: mov             x0, x1
    // 0x8e452c: r1 = 6
    //     0x8e452c: movz            x1, #0x6
    // 0x8e4530: cmp             x1, x0
    // 0x8e4534: b.hs            #0x8e4650
    // 0x8e4538: LoadField: r0 = r4->field_f
    //     0x8e4538: ldur            w0, [x4, #0xf]
    // 0x8e453c: DecompressPointer r0
    //     0x8e453c: add             x0, x0, HEAP, lsl #32
    // 0x8e4540: LoadField: r1 = r0->field_27
    //     0x8e4540: ldur            w1, [x0, #0x27]
    // 0x8e4544: DecompressPointer r1
    //     0x8e4544: add             x1, x1, HEAP, lsl #32
    // 0x8e4548: mov             x16, x1
    // 0x8e454c: mov             x1, x2
    // 0x8e4550: mov             x2, x16
    // 0x8e4554: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8e4554: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8e4558: r0 = set()
    //     0x8e4558: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e455c: ldur            x0, [fp, #-8]
    // 0x8e4560: LoadField: r1 = r0->field_1f
    //     0x8e4560: ldur            w1, [x0, #0x1f]
    // 0x8e4564: DecompressPointer r1
    //     0x8e4564: add             x1, x1, HEAP, lsl #32
    // 0x8e4568: cmp             w1, NULL
    // 0x8e456c: b.eq            #0x8e4654
    // 0x8e4570: LoadField: r2 = r1->field_7
    //     0x8e4570: ldur            w2, [x1, #7]
    // 0x8e4574: DecompressPointer r2
    //     0x8e4574: add             x2, x2, HEAP, lsl #32
    // 0x8e4578: LoadField: r0 = r2->field_b
    //     0x8e4578: ldur            w0, [x2, #0xb]
    // 0x8e457c: r1 = LoadInt32Instr(r0)
    //     0x8e457c: sbfx            x1, x0, #1, #0x1f
    // 0x8e4580: mov             x0, x1
    // 0x8e4584: r1 = 7
    //     0x8e4584: movz            x1, #0x7
    // 0x8e4588: cmp             x1, x0
    // 0x8e458c: b.hs            #0x8e4658
    // 0x8e4590: LoadField: r0 = r2->field_f
    //     0x8e4590: ldur            w0, [x2, #0xf]
    // 0x8e4594: DecompressPointer r0
    //     0x8e4594: add             x0, x0, HEAP, lsl #32
    // 0x8e4598: LoadField: r2 = r0->field_2b
    //     0x8e4598: ldur            w2, [x0, #0x2b]
    // 0x8e459c: DecompressPointer r2
    //     0x8e459c: add             x2, x2, HEAP, lsl #32
    // 0x8e45a0: ldur            x3, [fp, #-0x20]
    // 0x8e45a4: LoadField: r0 = r3->field_b
    //     0x8e45a4: ldur            w0, [x3, #0xb]
    // 0x8e45a8: r1 = LoadInt32Instr(r0)
    //     0x8e45a8: sbfx            x1, x0, #1, #0x1f
    // 0x8e45ac: mov             x0, x1
    // 0x8e45b0: r1 = 7
    //     0x8e45b0: movz            x1, #0x7
    // 0x8e45b4: cmp             x1, x0
    // 0x8e45b8: b.hs            #0x8e465c
    // 0x8e45bc: LoadField: r0 = r3->field_f
    //     0x8e45bc: ldur            w0, [x3, #0xf]
    // 0x8e45c0: DecompressPointer r0
    //     0x8e45c0: add             x0, x0, HEAP, lsl #32
    // 0x8e45c4: LoadField: r1 = r0->field_2b
    //     0x8e45c4: ldur            w1, [x0, #0x2b]
    // 0x8e45c8: DecompressPointer r1
    //     0x8e45c8: add             x1, x1, HEAP, lsl #32
    // 0x8e45cc: mov             x16, x1
    // 0x8e45d0: mov             x1, x2
    // 0x8e45d4: mov             x2, x16
    // 0x8e45d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8e45d8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8e45dc: r0 = set()
    //     0x8e45dc: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e45e0: r0 = Null
    //     0x8e45e0: mov             x0, NULL
    // 0x8e45e4: LeaveFrame
    //     0x8e45e4: mov             SP, fp
    //     0x8e45e8: ldp             fp, lr, [SP], #0x10
    // 0x8e45ec: ret
    //     0x8e45ec: ret             
    // 0x8e45f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e45f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e45f4: b               #0x8e4078
    // 0x8e45f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e45f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e45fc: b               #0x8e40b0
    // 0x8e4600: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4600: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4604: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4604: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4608: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4608: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e460c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e460c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8e4610: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4610: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4614: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4614: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4618: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e4618: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8e461c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e461c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4620: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4620: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e4624: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8e4628: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4628: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e462c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e462c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4630: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e4630: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8e4634: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4634: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4638: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4638: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e463c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e463c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8e4640: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4640: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4644: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4644: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4648: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e4648: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8e464c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e464c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4650: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4650: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4654: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e4654: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8e4658: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4658: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e465c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e465c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ digestSize(/* No info */) {
    // ** addr: 0xe61db0, size: 0x8
    // 0xe61db0: r0 = 64
    //     0xe61db0: movz            x0, #0x40
    // 0xe61db4: ret
    //     0xe61db4: ret             
  }
}
