// lib: impl.digest.cshake, url: package:pointycastle/digests/cshake.dart

// class id: 1050942, size: 0x8
class :: {
}

// class id: 667, size: 0x18, field offset: 0x18
class CSHAKEDigest extends SHAKEDigest
    implements Xof {

  static late final FactoryConfig factoryConfig; // offset: 0xde8
  static late final RegExp _cshakeREGEX; // offset: 0xde4

  get _ algorithmName(/* No info */) {
    // ** addr: 0x869af4, size: 0x7c
    // 0x869af4: EnterFrame
    //     0x869af4: stp             fp, lr, [SP, #-0x10]!
    //     0x869af8: mov             fp, SP
    // 0x869afc: AllocStack(0x10)
    //     0x869afc: sub             SP, SP, #0x10
    // 0x869b00: SetupParameters(CSHAKEDigest this /* r1 => r0, fp-0x8 */)
    //     0x869b00: mov             x0, x1
    //     0x869b04: stur            x1, [fp, #-8]
    // 0x869b08: CheckStackOverflow
    //     0x869b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x869b0c: cmp             SP, x16
    //     0x869b10: b.ls            #0x869b5c
    // 0x869b14: r1 = Null
    //     0x869b14: mov             x1, NULL
    // 0x869b18: r2 = 4
    //     0x869b18: movz            x2, #0x4
    // 0x869b1c: r0 = AllocateArray()
    //     0x869b1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x869b20: r16 = "CSHAKE-"
    //     0x869b20: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cc8] "CSHAKE-"
    //     0x869b24: ldr             x16, [x16, #0xcc8]
    // 0x869b28: StoreField: r0->field_f = r16
    //     0x869b28: stur            w16, [x0, #0xf]
    // 0x869b2c: ldur            x1, [fp, #-8]
    // 0x869b30: LoadField: r2 = r1->field_13
    //     0x869b30: ldur            w2, [x1, #0x13]
    // 0x869b34: DecompressPointer r2
    //     0x869b34: add             x2, x2, HEAP, lsl #32
    // 0x869b38: r16 = Sentinel
    //     0x869b38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x869b3c: cmp             w2, w16
    // 0x869b40: b.eq            #0x869b64
    // 0x869b44: StoreField: r0->field_13 = r2
    //     0x869b44: stur            w2, [x0, #0x13]
    // 0x869b48: str             x0, [SP]
    // 0x869b4c: r0 = _interpolate()
    //     0x869b4c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x869b50: LeaveFrame
    //     0x869b50: mov             SP, fp
    //     0x869b54: ldp             fp, lr, [SP], #0x10
    // 0x869b58: ret
    //     0x869b58: ret             
    // 0x869b5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x869b5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x869b60: b               #0x869b14
    // 0x869b64: r9 = fixedOutputLength
    //     0x869b64: add             x9, PP, #0x20, lsl #12  ; [pp+0x209f8] Field <KeccakEngine.fixedOutputLength>: late (offset: 0x14)
    //     0x869b68: ldr             x9, [x9, #0x9f8]
    // 0x869b6c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x869b6c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d6110, size: 0x8c
    // 0x8d6110: EnterFrame
    //     0x8d6110: stp             fp, lr, [SP, #-0x10]!
    //     0x8d6114: mov             fp, SP
    // 0x8d6118: AllocStack(0x10)
    //     0x8d6118: sub             SP, SP, #0x10
    // 0x8d611c: CheckStackOverflow
    //     0x8d611c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6120: cmp             SP, x16
    //     0x8d6124: b.ls            #0x8d6194
    // 0x8d6128: r0 = InitLateStaticField(0xde4) // [package:pointycastle/digests/cshake.dart] CSHAKEDigest::_cshakeREGEX
    //     0x8d6128: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d612c: ldr             x0, [x0, #0x1bc8]
    //     0x8d6130: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d6134: cmp             w0, w16
    //     0x8d6138: b.ne            #0x8d6148
    //     0x8d613c: add             x2, PP, #0x19, lsl #12  ; [pp+0x193b0] Field <CSHAKEDigest._cshakeREGEX@922480775>: static late final (offset: 0xde4)
    //     0x8d6140: ldr             x2, [x2, #0x3b0]
    //     0x8d6144: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d6148: stur            x0, [fp, #-8]
    // 0x8d614c: r0 = DynamicFactoryConfig()
    //     0x8d614c: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8d6150: mov             x3, x0
    // 0x8d6154: ldur            x0, [fp, #-8]
    // 0x8d6158: stur            x3, [fp, #-0x10]
    // 0x8d615c: StoreField: r3->field_b = r0
    //     0x8d615c: stur            w0, [x3, #0xb]
    // 0x8d6160: r1 = Function '<anonymous closure>': static.
    //     0x8d6160: add             x1, PP, #0x19, lsl #12  ; [pp+0x193b8] AnonymousClosure: static (0x8d619c), in [package:pointycastle/digests/cshake.dart] CSHAKEDigest::factoryConfig (0x8d6110)
    //     0x8d6164: ldr             x1, [x1, #0x3b8]
    // 0x8d6168: r2 = Null
    //     0x8d6168: mov             x2, NULL
    // 0x8d616c: r0 = AllocateClosure()
    //     0x8d616c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d6170: mov             x1, x0
    // 0x8d6174: ldur            x0, [fp, #-0x10]
    // 0x8d6178: StoreField: r0->field_f = r1
    //     0x8d6178: stur            w1, [x0, #0xf]
    // 0x8d617c: r1 = Digest
    //     0x8d617c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8d6180: ldr             x1, [x1, #0x388]
    // 0x8d6184: StoreField: r0->field_7 = r1
    //     0x8d6184: stur            w1, [x0, #7]
    // 0x8d6188: LeaveFrame
    //     0x8d6188: mov             SP, fp
    //     0x8d618c: ldp             fp, lr, [SP], #0x10
    // 0x8d6190: ret
    //     0x8d6190: ret             
    // 0x8d6194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d6194: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6198: b               #0x8d6128
  }
  [closure] static (dynamic) => CSHAKEDigest <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8d619c, size: 0x54
    // 0x8d619c: EnterFrame
    //     0x8d619c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d61a0: mov             fp, SP
    // 0x8d61a4: AllocStack(0x8)
    //     0x8d61a4: sub             SP, SP, #8
    // 0x8d61a8: SetupParameters()
    //     0x8d61a8: ldr             x0, [fp, #0x20]
    //     0x8d61ac: ldur            w1, [x0, #0x17]
    //     0x8d61b0: add             x1, x1, HEAP, lsl #32
    //     0x8d61b4: stur            x1, [fp, #-8]
    // 0x8d61b8: r1 = 1
    //     0x8d61b8: movz            x1, #0x1
    // 0x8d61bc: r0 = AllocateContext()
    //     0x8d61bc: bl              #0xec126c  ; AllocateContextStub
    // 0x8d61c0: mov             x1, x0
    // 0x8d61c4: ldur            x0, [fp, #-8]
    // 0x8d61c8: StoreField: r1->field_b = r0
    //     0x8d61c8: stur            w0, [x1, #0xb]
    // 0x8d61cc: ldr             x0, [fp, #0x10]
    // 0x8d61d0: StoreField: r1->field_f = r0
    //     0x8d61d0: stur            w0, [x1, #0xf]
    // 0x8d61d4: mov             x2, x1
    // 0x8d61d8: r1 = Function '<anonymous closure>': static.
    //     0x8d61d8: add             x1, PP, #0x19, lsl #12  ; [pp+0x193c0] AnonymousClosure: static (0x8d61f0), in [package:pointycastle/digests/cshake.dart] CSHAKEDigest::factoryConfig (0x8d6110)
    //     0x8d61dc: ldr             x1, [x1, #0x3c0]
    // 0x8d61e0: r0 = AllocateClosure()
    //     0x8d61e0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d61e4: LeaveFrame
    //     0x8d61e4: mov             SP, fp
    //     0x8d61e8: ldp             fp, lr, [SP], #0x10
    // 0x8d61ec: ret
    //     0x8d61ec: ret             
  }
  [closure] static CSHAKEDigest <anonymous closure>(dynamic) {
    // ** addr: 0x8d61f0, size: 0x98
    // 0x8d61f0: EnterFrame
    //     0x8d61f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d61f4: mov             fp, SP
    // 0x8d61f8: AllocStack(0x10)
    //     0x8d61f8: sub             SP, SP, #0x10
    // 0x8d61fc: SetupParameters()
    //     0x8d61fc: ldr             x0, [fp, #0x10]
    //     0x8d6200: ldur            w1, [x0, #0x17]
    //     0x8d6204: add             x1, x1, HEAP, lsl #32
    // 0x8d6208: CheckStackOverflow
    //     0x8d6208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d620c: cmp             SP, x16
    //     0x8d6210: b.ls            #0x8d627c
    // 0x8d6214: LoadField: r0 = r1->field_f
    //     0x8d6214: ldur            w0, [x1, #0xf]
    // 0x8d6218: DecompressPointer r0
    //     0x8d6218: add             x0, x0, HEAP, lsl #32
    // 0x8d621c: r1 = LoadClassIdInstr(r0)
    //     0x8d621c: ldur            x1, [x0, #-1]
    //     0x8d6220: ubfx            x1, x1, #0xc, #0x14
    // 0x8d6224: mov             x16, x0
    // 0x8d6228: mov             x0, x1
    // 0x8d622c: mov             x1, x16
    // 0x8d6230: r2 = 1
    //     0x8d6230: movz            x2, #0x1
    // 0x8d6234: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8d6234: sub             lr, x0, #0xfdd
    //     0x8d6238: ldr             lr, [x21, lr, lsl #3]
    //     0x8d623c: blr             lr
    // 0x8d6240: cmp             w0, NULL
    // 0x8d6244: b.eq            #0x8d6284
    // 0x8d6248: mov             x1, x0
    // 0x8d624c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8d624c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8d6250: r0 = parse()
    //     0x8d6250: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8d6254: stur            x0, [fp, #-8]
    // 0x8d6258: r0 = CSHAKEDigest()
    //     0x8d6258: bl              #0x8d6694  ; AllocateCSHAKEDigestStub -> CSHAKEDigest (size=0x18)
    // 0x8d625c: mov             x1, x0
    // 0x8d6260: ldur            x2, [fp, #-8]
    // 0x8d6264: stur            x0, [fp, #-0x10]
    // 0x8d6268: r0 = CSHAKEDigest()
    //     0x8d6268: bl              #0x8d6288  ; [package:pointycastle/digests/cshake.dart] CSHAKEDigest::CSHAKEDigest
    // 0x8d626c: ldur            x0, [fp, #-0x10]
    // 0x8d6270: LeaveFrame
    //     0x8d6270: mov             SP, fp
    //     0x8d6274: ldp             fp, lr, [SP], #0x10
    // 0x8d6278: ret
    //     0x8d6278: ret             
    // 0x8d627c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d627c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6280: b               #0x8d6214
    // 0x8d6284: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8d6284: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ CSHAKEDigest(/* No info */) {
    // ** addr: 0x8d6288, size: 0x114
    // 0x8d6288: EnterFrame
    //     0x8d6288: stp             fp, lr, [SP, #-0x10]!
    //     0x8d628c: mov             fp, SP
    // 0x8d6290: AllocStack(0x18)
    //     0x8d6290: sub             SP, SP, #0x18
    // 0x8d6294: SetupParameters(CSHAKEDigest this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8d6294: stur            x1, [fp, #-8]
    //     0x8d6298: stur            x2, [fp, #-0x10]
    // 0x8d629c: CheckStackOverflow
    //     0x8d629c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d62a0: cmp             SP, x16
    //     0x8d62a4: b.ls            #0x8d6394
    // 0x8d62a8: r4 = 200
    //     0x8d62a8: movz            x4, #0xc8
    // 0x8d62ac: r0 = AllocateUint8Array()
    //     0x8d62ac: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8d62b0: ldur            x1, [fp, #-8]
    // 0x8d62b4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8d62b4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8d62b8: r0 = SHAKEDigest()
    //     0x8d62b8: bl              #0x8d650c  ; [package:pointycastle/digests/shake.dart] SHAKEDigest::SHAKEDigest
    // 0x8d62bc: ldur            x3, [fp, #-0x10]
    // 0x8d62c0: cmp             x3, #0x80
    // 0x8d62c4: b.gt            #0x8d62e8
    // 0x8d62c8: r0 = BoxInt64Instr(r3)
    //     0x8d62c8: sbfiz           x0, x3, #1, #0x1f
    //     0x8d62cc: cmp             x3, x0, asr #1
    //     0x8d62d0: b.eq            #0x8d62dc
    //     0x8d62d4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d62d8: stur            x3, [x0, #7]
    // 0x8d62dc: cmp             w0, #0x100
    // 0x8d62e0: b.ne            #0x8d6328
    // 0x8d62e4: b               #0x8d630c
    // 0x8d62e8: cmp             x3, #0x100
    // 0x8d62ec: b.lt            #0x8d6328
    // 0x8d62f0: r0 = BoxInt64Instr(r3)
    //     0x8d62f0: sbfiz           x0, x3, #1, #0x1f
    //     0x8d62f4: cmp             x3, x0, asr #1
    //     0x8d62f8: b.eq            #0x8d6304
    //     0x8d62fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d6300: stur            x3, [x0, #7]
    // 0x8d6304: cmp             w0, #0x200
    // 0x8d6308: b.ne            #0x8d6328
    // 0x8d630c: ldur            x1, [fp, #-8]
    // 0x8d6310: mov             x2, x3
    // 0x8d6314: r0 = init()
    //     0x8d6314: bl              #0x8d639c  ; [package:pointycastle/src/impl/keccak_engine.dart] KeccakEngine::init
    // 0x8d6318: r0 = Null
    //     0x8d6318: mov             x0, NULL
    // 0x8d631c: LeaveFrame
    //     0x8d631c: mov             SP, fp
    //     0x8d6320: ldp             fp, lr, [SP], #0x10
    // 0x8d6324: ret
    //     0x8d6324: ret             
    // 0x8d6328: r1 = Null
    //     0x8d6328: mov             x1, NULL
    // 0x8d632c: r2 = 6
    //     0x8d632c: movz            x2, #0x6
    // 0x8d6330: r0 = AllocateArray()
    //     0x8d6330: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8d6334: mov             x2, x0
    // 0x8d6338: r16 = "invalid bitLength ("
    //     0x8d6338: add             x16, PP, #0x19, lsl #12  ; [pp+0x193c8] "invalid bitLength ("
    //     0x8d633c: ldr             x16, [x16, #0x3c8]
    // 0x8d6340: StoreField: r2->field_f = r16
    //     0x8d6340: stur            w16, [x2, #0xf]
    // 0x8d6344: ldur            x3, [fp, #-0x10]
    // 0x8d6348: r0 = BoxInt64Instr(r3)
    //     0x8d6348: sbfiz           x0, x3, #1, #0x1f
    //     0x8d634c: cmp             x3, x0, asr #1
    //     0x8d6350: b.eq            #0x8d635c
    //     0x8d6354: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d6358: stur            x3, [x0, #7]
    // 0x8d635c: StoreField: r2->field_13 = r0
    //     0x8d635c: stur            w0, [x2, #0x13]
    // 0x8d6360: r16 = ") for CSHAKE must only be 128 or 256"
    //     0x8d6360: add             x16, PP, #0x19, lsl #12  ; [pp+0x193d0] ") for CSHAKE must only be 128 or 256"
    //     0x8d6364: ldr             x16, [x16, #0x3d0]
    // 0x8d6368: ArrayStore: r2[0] = r16  ; List_4
    //     0x8d6368: stur            w16, [x2, #0x17]
    // 0x8d636c: str             x2, [SP]
    // 0x8d6370: r0 = _interpolate()
    //     0x8d6370: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8d6374: stur            x0, [fp, #-8]
    // 0x8d6378: r0 = StateError()
    //     0x8d6378: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8d637c: mov             x1, x0
    // 0x8d6380: ldur            x0, [fp, #-8]
    // 0x8d6384: StoreField: r1->field_b = r0
    //     0x8d6384: stur            w0, [x1, #0xb]
    // 0x8d6388: mov             x0, x1
    // 0x8d638c: r0 = Throw()
    //     0x8d638c: bl              #0xec04b8  ; ThrowStub
    // 0x8d6390: brk             #0
    // 0x8d6394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d6394: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6398: b               #0x8d62a8
  }
  static RegExp _cshakeREGEX() {
    // ** addr: 0x8d66a0, size: 0x58
    // 0x8d66a0: EnterFrame
    //     0x8d66a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d66a4: mov             fp, SP
    // 0x8d66a8: AllocStack(0x30)
    //     0x8d66a8: sub             SP, SP, #0x30
    // 0x8d66ac: CheckStackOverflow
    //     0x8d66ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d66b0: cmp             SP, x16
    //     0x8d66b4: b.ls            #0x8d66f0
    // 0x8d66b8: r16 = "^CSHAKE-([0-9]+)$"
    //     0x8d66b8: add             x16, PP, #0x19, lsl #12  ; [pp+0x193e8] "^CSHAKE-([0-9]+)$"
    //     0x8d66bc: ldr             x16, [x16, #0x3e8]
    // 0x8d66c0: stp             x16, NULL, [SP, #0x20]
    // 0x8d66c4: r16 = false
    //     0x8d66c4: add             x16, NULL, #0x30  ; false
    // 0x8d66c8: r30 = true
    //     0x8d66c8: add             lr, NULL, #0x20  ; true
    // 0x8d66cc: stp             lr, x16, [SP, #0x10]
    // 0x8d66d0: r16 = false
    //     0x8d66d0: add             x16, NULL, #0x30  ; false
    // 0x8d66d4: r30 = false
    //     0x8d66d4: add             lr, NULL, #0x30  ; false
    // 0x8d66d8: stp             lr, x16, [SP]
    // 0x8d66dc: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8d66dc: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8d66e0: r0 = _RegExp()
    //     0x8d66e0: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8d66e4: LeaveFrame
    //     0x8d66e4: mov             SP, fp
    //     0x8d66e8: ldp             fp, lr, [SP], #0x10
    // 0x8d66ec: ret
    //     0x8d66ec: ret             
    // 0x8d66f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d66f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d66f4: b               #0x8d66b8
  }
}
