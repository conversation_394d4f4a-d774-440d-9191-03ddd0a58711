// lib: impl.digest.keccak, url: package:pointycastle/digests/keccak.dart

// class id: 1050943, size: 0x8
class :: {
}

// class id: 665, size: 0x18, field offset: 0x18
class KeccakDigest extends KeccakEngine {

  static late final FactoryConfig factoryConfig; // offset: 0xdd0
  static late final RegExp _keccakREGEX; // offset: 0xdcc

  get _ algorithmName(/* No info */) {
    // ** addr: 0x869bec, size: 0x7c
    // 0x869bec: EnterFrame
    //     0x869bec: stp             fp, lr, [SP, #-0x10]!
    //     0x869bf0: mov             fp, SP
    // 0x869bf4: AllocStack(0x10)
    //     0x869bf4: sub             SP, SP, #0x10
    // 0x869bf8: SetupParameters(KeccakDigest this /* r1 => r0, fp-0x8 */)
    //     0x869bf8: mov             x0, x1
    //     0x869bfc: stur            x1, [fp, #-8]
    // 0x869c00: CheckStackOverflow
    //     0x869c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x869c04: cmp             SP, x16
    //     0x869c08: b.ls            #0x869c54
    // 0x869c0c: r1 = Null
    //     0x869c0c: mov             x1, NULL
    // 0x869c10: r2 = 4
    //     0x869c10: movz            x2, #0x4
    // 0x869c14: r0 = AllocateArray()
    //     0x869c14: bl              #0xec22fc  ; AllocateArrayStub
    // 0x869c18: r16 = "Keccak/"
    //     0x869c18: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ce0] "Keccak/"
    //     0x869c1c: ldr             x16, [x16, #0xce0]
    // 0x869c20: StoreField: r0->field_f = r16
    //     0x869c20: stur            w16, [x0, #0xf]
    // 0x869c24: ldur            x1, [fp, #-8]
    // 0x869c28: LoadField: r2 = r1->field_13
    //     0x869c28: ldur            w2, [x1, #0x13]
    // 0x869c2c: DecompressPointer r2
    //     0x869c2c: add             x2, x2, HEAP, lsl #32
    // 0x869c30: r16 = Sentinel
    //     0x869c30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x869c34: cmp             w2, w16
    // 0x869c38: b.eq            #0x869c5c
    // 0x869c3c: StoreField: r0->field_13 = r2
    //     0x869c3c: stur            w2, [x0, #0x13]
    // 0x869c40: str             x0, [SP]
    // 0x869c44: r0 = _interpolate()
    //     0x869c44: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x869c48: LeaveFrame
    //     0x869c48: mov             SP, fp
    //     0x869c4c: ldp             fp, lr, [SP], #0x10
    // 0x869c50: ret
    //     0x869c50: ret             
    // 0x869c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x869c54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x869c58: b               #0x869c0c
    // 0x869c5c: r9 = fixedOutputLength
    //     0x869c5c: add             x9, PP, #0x20, lsl #12  ; [pp+0x209f8] Field <KeccakEngine.fixedOutputLength>: late (offset: 0x14)
    //     0x869c60: ldr             x9, [x9, #0x9f8]
    // 0x869c64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x869c64: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e30c8, size: 0x8c
    // 0x8e30c8: EnterFrame
    //     0x8e30c8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e30cc: mov             fp, SP
    // 0x8e30d0: AllocStack(0x10)
    //     0x8e30d0: sub             SP, SP, #0x10
    // 0x8e30d4: CheckStackOverflow
    //     0x8e30d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e30d8: cmp             SP, x16
    //     0x8e30dc: b.ls            #0x8e314c
    // 0x8e30e0: r0 = InitLateStaticField(0xdcc) // [package:pointycastle/digests/keccak.dart] KeccakDigest::_keccakREGEX
    //     0x8e30e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e30e4: ldr             x0, [x0, #0x1b98]
    //     0x8e30e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e30ec: cmp             w0, w16
    //     0x8e30f0: b.ne            #0x8e3100
    //     0x8e30f4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19960] Field <KeccakDigest._keccakREGEX@919186498>: static late final (offset: 0xdcc)
    //     0x8e30f8: ldr             x2, [x2, #0x960]
    //     0x8e30fc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e3100: stur            x0, [fp, #-8]
    // 0x8e3104: r0 = DynamicFactoryConfig()
    //     0x8e3104: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e3108: mov             x3, x0
    // 0x8e310c: ldur            x0, [fp, #-8]
    // 0x8e3110: stur            x3, [fp, #-0x10]
    // 0x8e3114: StoreField: r3->field_b = r0
    //     0x8e3114: stur            w0, [x3, #0xb]
    // 0x8e3118: r1 = Function '<anonymous closure>': static.
    //     0x8e3118: add             x1, PP, #0x19, lsl #12  ; [pp+0x19968] AnonymousClosure: static (0x8e3154), in [package:pointycastle/digests/keccak.dart] KeccakDigest::factoryConfig (0x8e30c8)
    //     0x8e311c: ldr             x1, [x1, #0x968]
    // 0x8e3120: r2 = Null
    //     0x8e3120: mov             x2, NULL
    // 0x8e3124: r0 = AllocateClosure()
    //     0x8e3124: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3128: mov             x1, x0
    // 0x8e312c: ldur            x0, [fp, #-0x10]
    // 0x8e3130: StoreField: r0->field_f = r1
    //     0x8e3130: stur            w1, [x0, #0xf]
    // 0x8e3134: r1 = Digest
    //     0x8e3134: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3138: ldr             x1, [x1, #0x388]
    // 0x8e313c: StoreField: r0->field_7 = r1
    //     0x8e313c: stur            w1, [x0, #7]
    // 0x8e3140: LeaveFrame
    //     0x8e3140: mov             SP, fp
    //     0x8e3144: ldp             fp, lr, [SP], #0x10
    // 0x8e3148: ret
    //     0x8e3148: ret             
    // 0x8e314c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e314c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3150: b               #0x8e30e0
  }
  [closure] static (dynamic) => KeccakDigest <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e3154, size: 0x54
    // 0x8e3154: EnterFrame
    //     0x8e3154: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3158: mov             fp, SP
    // 0x8e315c: AllocStack(0x8)
    //     0x8e315c: sub             SP, SP, #8
    // 0x8e3160: SetupParameters()
    //     0x8e3160: ldr             x0, [fp, #0x20]
    //     0x8e3164: ldur            w1, [x0, #0x17]
    //     0x8e3168: add             x1, x1, HEAP, lsl #32
    //     0x8e316c: stur            x1, [fp, #-8]
    // 0x8e3170: r1 = 1
    //     0x8e3170: movz            x1, #0x1
    // 0x8e3174: r0 = AllocateContext()
    //     0x8e3174: bl              #0xec126c  ; AllocateContextStub
    // 0x8e3178: mov             x1, x0
    // 0x8e317c: ldur            x0, [fp, #-8]
    // 0x8e3180: StoreField: r1->field_b = r0
    //     0x8e3180: stur            w0, [x1, #0xb]
    // 0x8e3184: ldr             x0, [fp, #0x10]
    // 0x8e3188: StoreField: r1->field_f = r0
    //     0x8e3188: stur            w0, [x1, #0xf]
    // 0x8e318c: mov             x2, x1
    // 0x8e3190: r1 = Function '<anonymous closure>': static.
    //     0x8e3190: add             x1, PP, #0x19, lsl #12  ; [pp+0x19970] AnonymousClosure: static (0x8e31a8), in [package:pointycastle/digests/keccak.dart] KeccakDigest::factoryConfig (0x8e30c8)
    //     0x8e3194: ldr             x1, [x1, #0x970]
    // 0x8e3198: r0 = AllocateClosure()
    //     0x8e3198: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e319c: LeaveFrame
    //     0x8e319c: mov             SP, fp
    //     0x8e31a0: ldp             fp, lr, [SP], #0x10
    // 0x8e31a4: ret
    //     0x8e31a4: ret             
  }
  [closure] static KeccakDigest <anonymous closure>(dynamic) {
    // ** addr: 0x8e31a8, size: 0x98
    // 0x8e31a8: EnterFrame
    //     0x8e31a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e31ac: mov             fp, SP
    // 0x8e31b0: AllocStack(0x10)
    //     0x8e31b0: sub             SP, SP, #0x10
    // 0x8e31b4: SetupParameters()
    //     0x8e31b4: ldr             x0, [fp, #0x10]
    //     0x8e31b8: ldur            w1, [x0, #0x17]
    //     0x8e31bc: add             x1, x1, HEAP, lsl #32
    // 0x8e31c0: CheckStackOverflow
    //     0x8e31c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e31c4: cmp             SP, x16
    //     0x8e31c8: b.ls            #0x8e3234
    // 0x8e31cc: LoadField: r0 = r1->field_f
    //     0x8e31cc: ldur            w0, [x1, #0xf]
    // 0x8e31d0: DecompressPointer r0
    //     0x8e31d0: add             x0, x0, HEAP, lsl #32
    // 0x8e31d4: r1 = LoadClassIdInstr(r0)
    //     0x8e31d4: ldur            x1, [x0, #-1]
    //     0x8e31d8: ubfx            x1, x1, #0xc, #0x14
    // 0x8e31dc: mov             x16, x0
    // 0x8e31e0: mov             x0, x1
    // 0x8e31e4: mov             x1, x16
    // 0x8e31e8: r2 = 1
    //     0x8e31e8: movz            x2, #0x1
    // 0x8e31ec: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e31ec: sub             lr, x0, #0xfdd
    //     0x8e31f0: ldr             lr, [x21, lr, lsl #3]
    //     0x8e31f4: blr             lr
    // 0x8e31f8: cmp             w0, NULL
    // 0x8e31fc: b.eq            #0x8e323c
    // 0x8e3200: mov             x1, x0
    // 0x8e3204: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8e3204: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8e3208: r0 = parse()
    //     0x8e3208: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8e320c: stur            x0, [fp, #-8]
    // 0x8e3210: r0 = KeccakDigest()
    //     0x8e3210: bl              #0x8e33e8  ; AllocateKeccakDigestStub -> KeccakDigest (size=0x18)
    // 0x8e3214: mov             x1, x0
    // 0x8e3218: ldur            x2, [fp, #-8]
    // 0x8e321c: stur            x0, [fp, #-0x10]
    // 0x8e3220: r0 = KeccakDigest()
    //     0x8e3220: bl              #0x8e3240  ; [package:pointycastle/digests/keccak.dart] KeccakDigest::KeccakDigest
    // 0x8e3224: ldur            x0, [fp, #-0x10]
    // 0x8e3228: LeaveFrame
    //     0x8e3228: mov             SP, fp
    //     0x8e322c: ldp             fp, lr, [SP], #0x10
    // 0x8e3230: ret
    //     0x8e3230: ret             
    // 0x8e3234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3234: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3238: b               #0x8e31cc
    // 0x8e323c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e323c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ KeccakDigest(/* No info */) {
    // ** addr: 0x8e3240, size: 0x1a8
    // 0x8e3240: EnterFrame
    //     0x8e3240: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3244: mov             fp, SP
    // 0x8e3248: AllocStack(0x18)
    //     0x8e3248: sub             SP, SP, #0x18
    // 0x8e324c: r0 = Sentinel
    //     0x8e324c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e3250: stur            x1, [fp, #-8]
    // 0x8e3254: stur            x2, [fp, #-0x10]
    // 0x8e3258: CheckStackOverflow
    //     0x8e3258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e325c: cmp             SP, x16
    //     0x8e3260: b.ls            #0x8e33e0
    // 0x8e3264: StoreField: r1->field_f = r0
    //     0x8e3264: stur            w0, [x1, #0xf]
    // 0x8e3268: StoreField: r1->field_13 = r0
    //     0x8e3268: stur            w0, [x1, #0x13]
    // 0x8e326c: r4 = 400
    //     0x8e326c: movz            x4, #0x190
    // 0x8e3270: r0 = AllocateUint8Array()
    //     0x8e3270: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e3274: ldur            x1, [fp, #-8]
    // 0x8e3278: StoreField: r1->field_7 = r0
    //     0x8e3278: stur            w0, [x1, #7]
    //     0x8e327c: ldurb           w16, [x1, #-1]
    //     0x8e3280: ldurb           w17, [x0, #-1]
    //     0x8e3284: and             x16, x17, x16, lsr #2
    //     0x8e3288: tst             x16, HEAP, lsr #32
    //     0x8e328c: b.eq            #0x8e3294
    //     0x8e3290: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e3294: r4 = 384
    //     0x8e3294: movz            x4, #0x180
    // 0x8e3298: r0 = AllocateUint8Array()
    //     0x8e3298: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e329c: ldur            x2, [fp, #-8]
    // 0x8e32a0: StoreField: r2->field_b = r0
    //     0x8e32a0: stur            w0, [x2, #0xb]
    //     0x8e32a4: ldurb           w16, [x2, #-1]
    //     0x8e32a8: ldurb           w17, [x0, #-1]
    //     0x8e32ac: and             x16, x17, x16, lsr #2
    //     0x8e32b0: tst             x16, HEAP, lsr #32
    //     0x8e32b4: b.eq            #0x8e32bc
    //     0x8e32b8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e32bc: ldur            x3, [fp, #-0x10]
    // 0x8e32c0: cmp             x3, #0x100
    // 0x8e32c4: b.gt            #0x8e3310
    // 0x8e32c8: cmp             x3, #0xe0
    // 0x8e32cc: b.gt            #0x8e3304
    // 0x8e32d0: cmp             x3, #0x80
    // 0x8e32d4: b.gt            #0x8e32f8
    // 0x8e32d8: r0 = BoxInt64Instr(r3)
    //     0x8e32d8: sbfiz           x0, x3, #1, #0x1f
    //     0x8e32dc: cmp             x3, x0, asr #1
    //     0x8e32e0: b.eq            #0x8e32ec
    //     0x8e32e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e32e8: stur            x3, [x0, #7]
    // 0x8e32ec: cmp             w0, #0x100
    // 0x8e32f0: b.ne            #0x8e3374
    // 0x8e32f4: b               #0x8e3358
    // 0x8e32f8: cmp             x3, #0xe0
    // 0x8e32fc: b.lt            #0x8e3374
    // 0x8e3300: b               #0x8e3358
    // 0x8e3304: cmp             x3, #0x100
    // 0x8e3308: b.lt            #0x8e3374
    // 0x8e330c: b               #0x8e3358
    // 0x8e3310: cmp             x3, #0x120
    // 0x8e3314: b.lt            #0x8e3374
    // 0x8e3318: cmp             x3, #0x180
    // 0x8e331c: b.gt            #0x8e3334
    // 0x8e3320: cmp             x3, #0x120
    // 0x8e3324: b.le            #0x8e3358
    // 0x8e3328: cmp             x3, #0x180
    // 0x8e332c: b.lt            #0x8e3374
    // 0x8e3330: b               #0x8e3358
    // 0x8e3334: cmp             x3, #0x200
    // 0x8e3338: b.lt            #0x8e3374
    // 0x8e333c: r0 = BoxInt64Instr(r3)
    //     0x8e333c: sbfiz           x0, x3, #1, #0x1f
    //     0x8e3340: cmp             x3, x0, asr #1
    //     0x8e3344: b.eq            #0x8e3350
    //     0x8e3348: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e334c: stur            x3, [x0, #7]
    // 0x8e3350: cmp             w0, #0x400
    // 0x8e3354: b.ne            #0x8e3374
    // 0x8e3358: mov             x1, x2
    // 0x8e335c: mov             x2, x3
    // 0x8e3360: r0 = init()
    //     0x8e3360: bl              #0x8d639c  ; [package:pointycastle/src/impl/keccak_engine.dart] KeccakEngine::init
    // 0x8e3364: r0 = Null
    //     0x8e3364: mov             x0, NULL
    // 0x8e3368: LeaveFrame
    //     0x8e3368: mov             SP, fp
    //     0x8e336c: ldp             fp, lr, [SP], #0x10
    // 0x8e3370: ret
    //     0x8e3370: ret             
    // 0x8e3374: r1 = Null
    //     0x8e3374: mov             x1, NULL
    // 0x8e3378: r2 = 6
    //     0x8e3378: movz            x2, #0x6
    // 0x8e337c: r0 = AllocateArray()
    //     0x8e337c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e3380: mov             x2, x0
    // 0x8e3384: r16 = "invalid bitLength ("
    //     0x8e3384: add             x16, PP, #0x19, lsl #12  ; [pp+0x193c8] "invalid bitLength ("
    //     0x8e3388: ldr             x16, [x16, #0x3c8]
    // 0x8e338c: StoreField: r2->field_f = r16
    //     0x8e338c: stur            w16, [x2, #0xf]
    // 0x8e3390: ldur            x3, [fp, #-0x10]
    // 0x8e3394: r0 = BoxInt64Instr(r3)
    //     0x8e3394: sbfiz           x0, x3, #1, #0x1f
    //     0x8e3398: cmp             x3, x0, asr #1
    //     0x8e339c: b.eq            #0x8e33a8
    //     0x8e33a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e33a4: stur            x3, [x0, #7]
    // 0x8e33a8: StoreField: r2->field_13 = r0
    //     0x8e33a8: stur            w0, [x2, #0x13]
    // 0x8e33ac: r16 = ") for Keccak must only be 128,224,256,288,384,512"
    //     0x8e33ac: add             x16, PP, #0x19, lsl #12  ; [pp+0x19978] ") for Keccak must only be 128,224,256,288,384,512"
    //     0x8e33b0: ldr             x16, [x16, #0x978]
    // 0x8e33b4: ArrayStore: r2[0] = r16  ; List_4
    //     0x8e33b4: stur            w16, [x2, #0x17]
    // 0x8e33b8: str             x2, [SP]
    // 0x8e33bc: r0 = _interpolate()
    //     0x8e33bc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8e33c0: stur            x0, [fp, #-8]
    // 0x8e33c4: r0 = StateError()
    //     0x8e33c4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8e33c8: mov             x1, x0
    // 0x8e33cc: ldur            x0, [fp, #-8]
    // 0x8e33d0: StoreField: r1->field_b = r0
    //     0x8e33d0: stur            w0, [x1, #0xb]
    // 0x8e33d4: mov             x0, x1
    // 0x8e33d8: r0 = Throw()
    //     0x8e33d8: bl              #0xec04b8  ; ThrowStub
    // 0x8e33dc: brk             #0
    // 0x8e33e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e33e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e33e4: b               #0x8e3264
  }
  static RegExp _keccakREGEX() {
    // ** addr: 0x8e33f4, size: 0x58
    // 0x8e33f4: EnterFrame
    //     0x8e33f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e33f8: mov             fp, SP
    // 0x8e33fc: AllocStack(0x30)
    //     0x8e33fc: sub             SP, SP, #0x30
    // 0x8e3400: CheckStackOverflow
    //     0x8e3400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3404: cmp             SP, x16
    //     0x8e3408: b.ls            #0x8e3444
    // 0x8e340c: r16 = "^Keccak\\/([0-9]+)$"
    //     0x8e340c: add             x16, PP, #0x19, lsl #12  ; [pp+0x19980] "^Keccak\\/([0-9]+)$"
    //     0x8e3410: ldr             x16, [x16, #0x980]
    // 0x8e3414: stp             x16, NULL, [SP, #0x20]
    // 0x8e3418: r16 = false
    //     0x8e3418: add             x16, NULL, #0x30  ; false
    // 0x8e341c: r30 = true
    //     0x8e341c: add             lr, NULL, #0x20  ; true
    // 0x8e3420: stp             lr, x16, [SP, #0x10]
    // 0x8e3424: r16 = false
    //     0x8e3424: add             x16, NULL, #0x30  ; false
    // 0x8e3428: r30 = false
    //     0x8e3428: add             lr, NULL, #0x30  ; false
    // 0x8e342c: stp             lr, x16, [SP]
    // 0x8e3430: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8e3430: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8e3434: r0 = _RegExp()
    //     0x8e3434: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8e3438: LeaveFrame
    //     0x8e3438: mov             SP, fp
    //     0x8e343c: ldp             fp, lr, [SP], #0x10
    // 0x8e3440: ret
    //     0x8e3440: ret             
    // 0x8e3444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3444: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3448: b               #0x8e340c
  }
}
