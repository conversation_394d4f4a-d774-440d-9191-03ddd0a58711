// lib: impl.digest.md2, url: package:pointycastle/digests/md2.dart

// class id: 1050944, size: 0x8
class :: {
}

// class id: 662, size: 0x8, field offset: 0x8
class MD2Digest extends BaseDigest {

  static late final FactoryConfig factoryConfig; // offset: 0xdec

  get _ algorithmName(/* No info */) {
    // ** addr: 0x869ce4, size: 0xc
    // 0x869ce4: r0 = "MD2"
    //     0x869ce4: add             x0, PP, #0x18, lsl #12  ; [pp+0x181c0] "MD2"
    //     0x869ce8: ldr             x0, [x0, #0x1c0]
    // 0x869cec: ret
    //     0x869cec: ret             
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e3cf8, size: 0x58
    // 0x8e3cf8: EnterFrame
    //     0x8e3cf8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3cfc: mov             fp, SP
    // 0x8e3d00: AllocStack(0x8)
    //     0x8e3d00: sub             SP, SP, #8
    // 0x8e3d04: r0 = StaticFactoryConfig()
    //     0x8e3d04: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e3d08: mov             x3, x0
    // 0x8e3d0c: r0 = "MD2"
    //     0x8e3d0c: add             x0, PP, #0x18, lsl #12  ; [pp+0x181c0] "MD2"
    //     0x8e3d10: ldr             x0, [x0, #0x1c0]
    // 0x8e3d14: stur            x3, [fp, #-8]
    // 0x8e3d18: StoreField: r3->field_b = r0
    //     0x8e3d18: stur            w0, [x3, #0xb]
    // 0x8e3d1c: r1 = Function '<anonymous closure>': static.
    //     0x8e3d1c: add             x1, PP, #0x19, lsl #12  ; [pp+0x199f0] AnonymousClosure: static (0x8e3d50), in [package:pointycastle/digests/md2.dart] MD2Digest::factoryConfig (0x8e3cf8)
    //     0x8e3d20: ldr             x1, [x1, #0x9f0]
    // 0x8e3d24: r2 = Null
    //     0x8e3d24: mov             x2, NULL
    // 0x8e3d28: r0 = AllocateClosure()
    //     0x8e3d28: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3d2c: mov             x1, x0
    // 0x8e3d30: ldur            x0, [fp, #-8]
    // 0x8e3d34: StoreField: r0->field_f = r1
    //     0x8e3d34: stur            w1, [x0, #0xf]
    // 0x8e3d38: r1 = Digest
    //     0x8e3d38: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3d3c: ldr             x1, [x1, #0x388]
    // 0x8e3d40: StoreField: r0->field_7 = r1
    //     0x8e3d40: stur            w1, [x0, #7]
    // 0x8e3d44: LeaveFrame
    //     0x8e3d44: mov             SP, fp
    //     0x8e3d48: ldp             fp, lr, [SP], #0x10
    // 0x8e3d4c: ret
    //     0x8e3d4c: ret             
  }
  [closure] static MD2Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e3d50, size: 0x20
    // 0x8e3d50: EnterFrame
    //     0x8e3d50: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3d54: mov             fp, SP
    // 0x8e3d58: r4 = 96
    //     0x8e3d58: movz            x4, #0x60
    // 0x8e3d5c: r0 = AllocateUint8Array()
    //     0x8e3d5c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e3d60: r0 = MD2Digest()
    //     0x8e3d60: bl              #0x8e3d70  ; AllocateMD2DigestStub -> MD2Digest (size=0x8)
    // 0x8e3d64: LeaveFrame
    //     0x8e3d64: mov             SP, fp
    //     0x8e3d68: ldp             fp, lr, [SP], #0x10
    // 0x8e3d6c: ret
    //     0x8e3d6c: ret             
  }
}
