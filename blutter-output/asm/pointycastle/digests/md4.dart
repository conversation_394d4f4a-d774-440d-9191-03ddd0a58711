// lib: impl.digest.md4, url: package:pointycastle/digests/md4.dart

// class id: 1050945, size: 0x8
class :: {
}

// class id: 661, size: 0x38, field offset: 0x2c
class MD4Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xdf0

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e3c2c, size: 0x58
    // 0x8e3c2c: EnterFrame
    //     0x8e3c2c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3c30: mov             fp, SP
    // 0x8e3c34: AllocStack(0x8)
    //     0x8e3c34: sub             SP, SP, #8
    // 0x8e3c38: r0 = StaticFactoryConfig()
    //     0x8e3c38: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e3c3c: mov             x3, x0
    // 0x8e3c40: r0 = "MD4"
    //     0x8e3c40: add             x0, PP, #0x18, lsl #12  ; [pp+0x181d0] "MD4"
    //     0x8e3c44: ldr             x0, [x0, #0x1d0]
    // 0x8e3c48: stur            x3, [fp, #-8]
    // 0x8e3c4c: StoreField: r3->field_b = r0
    //     0x8e3c4c: stur            w0, [x3, #0xb]
    // 0x8e3c50: r1 = Function '<anonymous closure>': static.
    //     0x8e3c50: add             x1, PP, #0x19, lsl #12  ; [pp+0x199e8] AnonymousClosure: static (0x8e3c84), in [package:pointycastle/digests/md4.dart] MD4Digest::factoryConfig (0x8e3c2c)
    //     0x8e3c54: ldr             x1, [x1, #0x9e8]
    // 0x8e3c58: r2 = Null
    //     0x8e3c58: mov             x2, NULL
    // 0x8e3c5c: r0 = AllocateClosure()
    //     0x8e3c5c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3c60: mov             x1, x0
    // 0x8e3c64: ldur            x0, [fp, #-8]
    // 0x8e3c68: StoreField: r0->field_f = r1
    //     0x8e3c68: stur            w1, [x0, #0xf]
    // 0x8e3c6c: r1 = Digest
    //     0x8e3c6c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3c70: ldr             x1, [x1, #0x388]
    // 0x8e3c74: StoreField: r0->field_7 = r1
    //     0x8e3c74: stur            w1, [x0, #7]
    // 0x8e3c78: LeaveFrame
    //     0x8e3c78: mov             SP, fp
    //     0x8e3c7c: ldp             fp, lr, [SP], #0x10
    // 0x8e3c80: ret
    //     0x8e3c80: ret             
  }
  [closure] static MD4Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e3c84, size: 0x68
    // 0x8e3c84: EnterFrame
    //     0x8e3c84: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3c88: mov             fp, SP
    // 0x8e3c8c: AllocStack(0x8)
    //     0x8e3c8c: sub             SP, SP, #8
    // 0x8e3c90: CheckStackOverflow
    //     0x8e3c90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3c94: cmp             SP, x16
    //     0x8e3c98: b.ls            #0x8e3ce4
    // 0x8e3c9c: r0 = MD4Digest()
    //     0x8e3c9c: bl              #0x8e3cec  ; AllocateMD4DigestStub -> MD4Digest (size=0x38)
    // 0x8e3ca0: mov             x4, x0
    // 0x8e3ca4: r0 = "MD4"
    //     0x8e3ca4: add             x0, PP, #0x18, lsl #12  ; [pp+0x181d0] "MD4"
    //     0x8e3ca8: ldr             x0, [x0, #0x1d0]
    // 0x8e3cac: stur            x4, [fp, #-8]
    // 0x8e3cb0: StoreField: r4->field_2b = r0
    //     0x8e3cb0: stur            w0, [x4, #0x2b]
    // 0x8e3cb4: r5 = 16
    //     0x8e3cb4: movz            x5, #0x10
    // 0x8e3cb8: StoreField: r4->field_2f = r5
    //     0x8e3cb8: stur            x5, [x4, #0x2f]
    // 0x8e3cbc: mov             x1, x4
    // 0x8e3cc0: r2 = Instance_Endian
    //     0x8e3cc0: add             x2, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e3cc4: ldr             x2, [x2, #0x8b8]
    // 0x8e3cc8: r3 = 4
    //     0x8e3cc8: movz            x3, #0x4
    // 0x8e3ccc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8e3ccc: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8e3cd0: r0 = MD4FamilyDigest()
    //     0x8e3cd0: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8e3cd4: ldur            x0, [fp, #-8]
    // 0x8e3cd8: LeaveFrame
    //     0x8e3cd8: mov             SP, fp
    //     0x8e3cdc: ldp             fp, lr, [SP], #0x10
    // 0x8e3ce0: ret
    //     0x8e3ce0: ret             
    // 0x8e3ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3ce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3ce8: b               #0x8e3c9c
  }
}
