// lib: impl.digest.md5, url: package:pointycastle/digests/md5.dart

// class id: 1050946, size: 0x8
class :: {
}

// class id: 660, size: 0x38, field offset: 0x2c
class MD5Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xdf4

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e3b60, size: 0x58
    // 0x8e3b60: EnterFrame
    //     0x8e3b60: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3b64: mov             fp, SP
    // 0x8e3b68: AllocStack(0x8)
    //     0x8e3b68: sub             SP, SP, #8
    // 0x8e3b6c: r0 = StaticFactoryConfig()
    //     0x8e3b6c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e3b70: mov             x3, x0
    // 0x8e3b74: r0 = "MD5"
    //     0x8e3b74: add             x0, PP, #0x18, lsl #12  ; [pp+0x181e0] "MD5"
    //     0x8e3b78: ldr             x0, [x0, #0x1e0]
    // 0x8e3b7c: stur            x3, [fp, #-8]
    // 0x8e3b80: StoreField: r3->field_b = r0
    //     0x8e3b80: stur            w0, [x3, #0xb]
    // 0x8e3b84: r1 = Function '<anonymous closure>': static.
    //     0x8e3b84: add             x1, PP, #0x19, lsl #12  ; [pp+0x199e0] AnonymousClosure: static (0x8e3bb8), in [package:pointycastle/digests/md5.dart] MD5Digest::factoryConfig (0x8e3b60)
    //     0x8e3b88: ldr             x1, [x1, #0x9e0]
    // 0x8e3b8c: r2 = Null
    //     0x8e3b8c: mov             x2, NULL
    // 0x8e3b90: r0 = AllocateClosure()
    //     0x8e3b90: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3b94: mov             x1, x0
    // 0x8e3b98: ldur            x0, [fp, #-8]
    // 0x8e3b9c: StoreField: r0->field_f = r1
    //     0x8e3b9c: stur            w1, [x0, #0xf]
    // 0x8e3ba0: r1 = Digest
    //     0x8e3ba0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3ba4: ldr             x1, [x1, #0x388]
    // 0x8e3ba8: StoreField: r0->field_7 = r1
    //     0x8e3ba8: stur            w1, [x0, #7]
    // 0x8e3bac: LeaveFrame
    //     0x8e3bac: mov             SP, fp
    //     0x8e3bb0: ldp             fp, lr, [SP], #0x10
    // 0x8e3bb4: ret
    //     0x8e3bb4: ret             
  }
  [closure] static MD5Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e3bb8, size: 0x68
    // 0x8e3bb8: EnterFrame
    //     0x8e3bb8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3bbc: mov             fp, SP
    // 0x8e3bc0: AllocStack(0x8)
    //     0x8e3bc0: sub             SP, SP, #8
    // 0x8e3bc4: CheckStackOverflow
    //     0x8e3bc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3bc8: cmp             SP, x16
    //     0x8e3bcc: b.ls            #0x8e3c18
    // 0x8e3bd0: r0 = MD5Digest()
    //     0x8e3bd0: bl              #0x8e3c20  ; AllocateMD5DigestStub -> MD5Digest (size=0x38)
    // 0x8e3bd4: mov             x4, x0
    // 0x8e3bd8: r0 = "MD5"
    //     0x8e3bd8: add             x0, PP, #0x18, lsl #12  ; [pp+0x181e0] "MD5"
    //     0x8e3bdc: ldr             x0, [x0, #0x1e0]
    // 0x8e3be0: stur            x4, [fp, #-8]
    // 0x8e3be4: StoreField: r4->field_2b = r0
    //     0x8e3be4: stur            w0, [x4, #0x2b]
    // 0x8e3be8: r5 = 16
    //     0x8e3be8: movz            x5, #0x10
    // 0x8e3bec: StoreField: r4->field_2f = r5
    //     0x8e3bec: stur            x5, [x4, #0x2f]
    // 0x8e3bf0: mov             x1, x4
    // 0x8e3bf4: r2 = Instance_Endian
    //     0x8e3bf4: add             x2, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e3bf8: ldr             x2, [x2, #0x8b8]
    // 0x8e3bfc: r3 = 4
    //     0x8e3bfc: movz            x3, #0x4
    // 0x8e3c00: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8e3c00: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8e3c04: r0 = MD4FamilyDigest()
    //     0x8e3c04: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8e3c08: ldur            x0, [fp, #-8]
    // 0x8e3c0c: LeaveFrame
    //     0x8e3c0c: mov             SP, fp
    //     0x8e3c10: ldp             fp, lr, [SP], #0x10
    // 0x8e3c14: ret
    //     0x8e3c14: ret             
    // 0x8e3c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3c1c: b               #0x8e3bd0
  }
}
