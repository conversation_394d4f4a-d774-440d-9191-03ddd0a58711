// lib: impl.digest.ripemd128, url: package:pointycastle/digests/ripemd128.dart

// class id: 1050947, size: 0x8
class :: {
}

// class id: 659, size: 0x38, field offset: 0x2c
class RIPEMD128Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xdf8

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e3a94, size: 0x58
    // 0x8e3a94: EnterFrame
    //     0x8e3a94: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3a98: mov             fp, SP
    // 0x8e3a9c: AllocStack(0x8)
    //     0x8e3a9c: sub             SP, SP, #8
    // 0x8e3aa0: r0 = StaticFactoryConfig()
    //     0x8e3aa0: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e3aa4: mov             x3, x0
    // 0x8e3aa8: r0 = "RIPEMD-128"
    //     0x8e3aa8: add             x0, PP, #0x18, lsl #12  ; [pp+0x181f0] "RIPEMD-128"
    //     0x8e3aac: ldr             x0, [x0, #0x1f0]
    // 0x8e3ab0: stur            x3, [fp, #-8]
    // 0x8e3ab4: StoreField: r3->field_b = r0
    //     0x8e3ab4: stur            w0, [x3, #0xb]
    // 0x8e3ab8: r1 = Function '<anonymous closure>': static.
    //     0x8e3ab8: add             x1, PP, #0x19, lsl #12  ; [pp+0x199d8] AnonymousClosure: static (0x8e3aec), in [package:pointycastle/digests/ripemd128.dart] RIPEMD128Digest::factoryConfig (0x8e3a94)
    //     0x8e3abc: ldr             x1, [x1, #0x9d8]
    // 0x8e3ac0: r2 = Null
    //     0x8e3ac0: mov             x2, NULL
    // 0x8e3ac4: r0 = AllocateClosure()
    //     0x8e3ac4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3ac8: mov             x1, x0
    // 0x8e3acc: ldur            x0, [fp, #-8]
    // 0x8e3ad0: StoreField: r0->field_f = r1
    //     0x8e3ad0: stur            w1, [x0, #0xf]
    // 0x8e3ad4: r1 = Digest
    //     0x8e3ad4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3ad8: ldr             x1, [x1, #0x388]
    // 0x8e3adc: StoreField: r0->field_7 = r1
    //     0x8e3adc: stur            w1, [x0, #7]
    // 0x8e3ae0: LeaveFrame
    //     0x8e3ae0: mov             SP, fp
    //     0x8e3ae4: ldp             fp, lr, [SP], #0x10
    // 0x8e3ae8: ret
    //     0x8e3ae8: ret             
  }
  [closure] static RIPEMD128Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e3aec, size: 0x68
    // 0x8e3aec: EnterFrame
    //     0x8e3aec: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3af0: mov             fp, SP
    // 0x8e3af4: AllocStack(0x8)
    //     0x8e3af4: sub             SP, SP, #8
    // 0x8e3af8: CheckStackOverflow
    //     0x8e3af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3afc: cmp             SP, x16
    //     0x8e3b00: b.ls            #0x8e3b4c
    // 0x8e3b04: r0 = RIPEMD128Digest()
    //     0x8e3b04: bl              #0x8e3b54  ; AllocateRIPEMD128DigestStub -> RIPEMD128Digest (size=0x38)
    // 0x8e3b08: mov             x4, x0
    // 0x8e3b0c: r0 = "RIPEMD-128"
    //     0x8e3b0c: add             x0, PP, #0x18, lsl #12  ; [pp+0x181f0] "RIPEMD-128"
    //     0x8e3b10: ldr             x0, [x0, #0x1f0]
    // 0x8e3b14: stur            x4, [fp, #-8]
    // 0x8e3b18: StoreField: r4->field_2b = r0
    //     0x8e3b18: stur            w0, [x4, #0x2b]
    // 0x8e3b1c: r5 = 16
    //     0x8e3b1c: movz            x5, #0x10
    // 0x8e3b20: StoreField: r4->field_2f = r5
    //     0x8e3b20: stur            x5, [x4, #0x2f]
    // 0x8e3b24: mov             x1, x4
    // 0x8e3b28: r2 = Instance_Endian
    //     0x8e3b28: add             x2, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e3b2c: ldr             x2, [x2, #0x8b8]
    // 0x8e3b30: r3 = 4
    //     0x8e3b30: movz            x3, #0x4
    // 0x8e3b34: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8e3b34: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8e3b38: r0 = MD4FamilyDigest()
    //     0x8e3b38: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8e3b3c: ldur            x0, [fp, #-8]
    // 0x8e3b40: LeaveFrame
    //     0x8e3b40: mov             SP, fp
    //     0x8e3b44: ldp             fp, lr, [SP], #0x10
    // 0x8e3b48: ret
    //     0x8e3b48: ret             
    // 0x8e3b4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3b4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3b50: b               #0x8e3b04
  }
  _ resetState(/* No info */) {
    // ** addr: 0xcdda20, size: 0xa8
    // 0xcdda20: EnterFrame
    //     0xcdda20: stp             fp, lr, [SP, #-0x10]!
    //     0xcdda24: mov             fp, SP
    // 0xcdda28: LoadField: r2 = r1->field_1f
    //     0xcdda28: ldur            w2, [x1, #0x1f]
    // 0xcdda2c: DecompressPointer r2
    //     0xcdda2c: add             x2, x2, HEAP, lsl #32
    // 0xcdda30: LoadField: r3 = r2->field_b
    //     0xcdda30: ldur            w3, [x2, #0xb]
    // 0xcdda34: r4 = LoadInt32Instr(r3)
    //     0xcdda34: sbfx            x4, x3, #1, #0x1f
    // 0xcdda38: mov             x0, x4
    // 0xcdda3c: r1 = 0
    //     0xcdda3c: movz            x1, #0
    // 0xcdda40: cmp             x1, x0
    // 0xcdda44: b.hs            #0xcddab8
    // 0xcdda48: r16 = 1732584193
    //     0xcdda48: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ca8] 0x67452301
    //     0xcdda4c: ldr             x16, [x16, #0xca8]
    // 0xcdda50: StoreField: r2->field_f = r16
    //     0xcdda50: stur            w16, [x2, #0xf]
    // 0xcdda54: mov             x0, x4
    // 0xcdda58: r1 = 1
    //     0xcdda58: movz            x1, #0x1
    // 0xcdda5c: cmp             x1, x0
    // 0xcdda60: b.hs            #0xcddabc
    // 0xcdda64: r16 = 4023233417
    //     0xcdda64: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cb0] 0xefcdab89
    //     0xcdda68: ldr             x16, [x16, #0xcb0]
    // 0xcdda6c: StoreField: r2->field_13 = r16
    //     0xcdda6c: stur            w16, [x2, #0x13]
    // 0xcdda70: mov             x0, x4
    // 0xcdda74: r1 = 2
    //     0xcdda74: movz            x1, #0x2
    // 0xcdda78: cmp             x1, x0
    // 0xcdda7c: b.hs            #0xcddac0
    // 0xcdda80: r16 = 2562383102
    //     0xcdda80: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cb8] 0x98badcfe
    //     0xcdda84: ldr             x16, [x16, #0xcb8]
    // 0xcdda88: ArrayStore: r2[0] = r16  ; List_4
    //     0xcdda88: stur            w16, [x2, #0x17]
    // 0xcdda8c: mov             x0, x4
    // 0xcdda90: r1 = 3
    //     0xcdda90: movz            x1, #0x3
    // 0xcdda94: cmp             x1, x0
    // 0xcdda98: b.hs            #0xcddac4
    // 0xcdda9c: r16 = 543467756
    //     0xcdda9c: movz            x16, #0xa8ec
    //     0xcddaa0: movk            x16, #0x2064, lsl #16
    // 0xcddaa4: StoreField: r2->field_1b = r16
    //     0xcddaa4: stur            w16, [x2, #0x1b]
    // 0xcddaa8: r0 = Null
    //     0xcddaa8: mov             x0, NULL
    // 0xcddaac: LeaveFrame
    //     0xcddaac: mov             SP, fp
    //     0xcddab0: ldp             fp, lr, [SP], #0x10
    // 0xcddab4: ret
    //     0xcddab4: ret             
    // 0xcddab8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddab8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddabc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddabc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddac0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddac0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddac4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddac4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
