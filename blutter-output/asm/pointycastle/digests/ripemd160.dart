// lib: impl.digest.ripemd160, url: package:pointycastle/digests/ripemd160.dart

// class id: 1050948, size: 0x8
class :: {
}

// class id: 658, size: 0x38, field offset: 0x2c
class RIPEMD160Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xdfc

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e39c4, size: 0x58
    // 0x8e39c4: EnterFrame
    //     0x8e39c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e39c8: mov             fp, SP
    // 0x8e39cc: AllocStack(0x8)
    //     0x8e39cc: sub             SP, SP, #8
    // 0x8e39d0: r0 = StaticFactoryConfig()
    //     0x8e39d0: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e39d4: mov             x3, x0
    // 0x8e39d8: r0 = "RIPEMD-160"
    //     0x8e39d8: add             x0, PP, #0x18, lsl #12  ; [pp+0x18200] "RIPEMD-160"
    //     0x8e39dc: ldr             x0, [x0, #0x200]
    // 0x8e39e0: stur            x3, [fp, #-8]
    // 0x8e39e4: StoreField: r3->field_b = r0
    //     0x8e39e4: stur            w0, [x3, #0xb]
    // 0x8e39e8: r1 = Function '<anonymous closure>': static.
    //     0x8e39e8: add             x1, PP, #0x19, lsl #12  ; [pp+0x199d0] AnonymousClosure: static (0x8e3a1c), in [package:pointycastle/digests/ripemd160.dart] RIPEMD160Digest::factoryConfig (0x8e39c4)
    //     0x8e39ec: ldr             x1, [x1, #0x9d0]
    // 0x8e39f0: r2 = Null
    //     0x8e39f0: mov             x2, NULL
    // 0x8e39f4: r0 = AllocateClosure()
    //     0x8e39f4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e39f8: mov             x1, x0
    // 0x8e39fc: ldur            x0, [fp, #-8]
    // 0x8e3a00: StoreField: r0->field_f = r1
    //     0x8e3a00: stur            w1, [x0, #0xf]
    // 0x8e3a04: r1 = Digest
    //     0x8e3a04: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3a08: ldr             x1, [x1, #0x388]
    // 0x8e3a0c: StoreField: r0->field_7 = r1
    //     0x8e3a0c: stur            w1, [x0, #7]
    // 0x8e3a10: LeaveFrame
    //     0x8e3a10: mov             SP, fp
    //     0x8e3a14: ldp             fp, lr, [SP], #0x10
    // 0x8e3a18: ret
    //     0x8e3a18: ret             
  }
  [closure] static RIPEMD160Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e3a1c, size: 0x6c
    // 0x8e3a1c: EnterFrame
    //     0x8e3a1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3a20: mov             fp, SP
    // 0x8e3a24: AllocStack(0x8)
    //     0x8e3a24: sub             SP, SP, #8
    // 0x8e3a28: CheckStackOverflow
    //     0x8e3a28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3a2c: cmp             SP, x16
    //     0x8e3a30: b.ls            #0x8e3a80
    // 0x8e3a34: r0 = RIPEMD160Digest()
    //     0x8e3a34: bl              #0x8e3a88  ; AllocateRIPEMD160DigestStub -> RIPEMD160Digest (size=0x38)
    // 0x8e3a38: mov             x4, x0
    // 0x8e3a3c: r0 = "RIPEMD-160"
    //     0x8e3a3c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18200] "RIPEMD-160"
    //     0x8e3a40: ldr             x0, [x0, #0x200]
    // 0x8e3a44: stur            x4, [fp, #-8]
    // 0x8e3a48: StoreField: r4->field_2b = r0
    //     0x8e3a48: stur            w0, [x4, #0x2b]
    // 0x8e3a4c: r0 = 20
    //     0x8e3a4c: movz            x0, #0x14
    // 0x8e3a50: StoreField: r4->field_2f = r0
    //     0x8e3a50: stur            x0, [x4, #0x2f]
    // 0x8e3a54: mov             x1, x4
    // 0x8e3a58: r2 = Instance_Endian
    //     0x8e3a58: add             x2, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e3a5c: ldr             x2, [x2, #0x8b8]
    // 0x8e3a60: r3 = 5
    //     0x8e3a60: movz            x3, #0x5
    // 0x8e3a64: r5 = 16
    //     0x8e3a64: movz            x5, #0x10
    // 0x8e3a68: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8e3a68: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8e3a6c: r0 = MD4FamilyDigest()
    //     0x8e3a6c: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8e3a70: ldur            x0, [fp, #-8]
    // 0x8e3a74: LeaveFrame
    //     0x8e3a74: mov             SP, fp
    //     0x8e3a78: ldp             fp, lr, [SP], #0x10
    // 0x8e3a7c: ret
    //     0x8e3a7c: ret             
    // 0x8e3a80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3a80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3a84: b               #0x8e3a34
  }
}
