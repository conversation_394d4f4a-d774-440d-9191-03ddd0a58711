// lib: impl.digest.ripemd256, url: package:pointycastle/digests/ripemd256.dart

// class id: 1050949, size: 0x8
class :: {
}

// class id: 657, size: 0x38, field offset: 0x2c
class RIPEMD256Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe00

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e38f4, size: 0x58
    // 0x8e38f4: EnterFrame
    //     0x8e38f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e38f8: mov             fp, SP
    // 0x8e38fc: AllocStack(0x8)
    //     0x8e38fc: sub             SP, SP, #8
    // 0x8e3900: r0 = StaticFactoryConfig()
    //     0x8e3900: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e3904: mov             x3, x0
    // 0x8e3908: r0 = "RIPEMD-256"
    //     0x8e3908: add             x0, PP, #0x18, lsl #12  ; [pp+0x18210] "RIPEMD-256"
    //     0x8e390c: ldr             x0, [x0, #0x210]
    // 0x8e3910: stur            x3, [fp, #-8]
    // 0x8e3914: StoreField: r3->field_b = r0
    //     0x8e3914: stur            w0, [x3, #0xb]
    // 0x8e3918: r1 = Function '<anonymous closure>': static.
    //     0x8e3918: add             x1, PP, #0x19, lsl #12  ; [pp+0x199c8] AnonymousClosure: static (0x8e394c), in [package:pointycastle/digests/ripemd256.dart] RIPEMD256Digest::factoryConfig (0x8e38f4)
    //     0x8e391c: ldr             x1, [x1, #0x9c8]
    // 0x8e3920: r2 = Null
    //     0x8e3920: mov             x2, NULL
    // 0x8e3924: r0 = AllocateClosure()
    //     0x8e3924: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3928: mov             x1, x0
    // 0x8e392c: ldur            x0, [fp, #-8]
    // 0x8e3930: StoreField: r0->field_f = r1
    //     0x8e3930: stur            w1, [x0, #0xf]
    // 0x8e3934: r1 = Digest
    //     0x8e3934: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3938: ldr             x1, [x1, #0x388]
    // 0x8e393c: StoreField: r0->field_7 = r1
    //     0x8e393c: stur            w1, [x0, #7]
    // 0x8e3940: LeaveFrame
    //     0x8e3940: mov             SP, fp
    //     0x8e3944: ldp             fp, lr, [SP], #0x10
    // 0x8e3948: ret
    //     0x8e3948: ret             
  }
  [closure] static RIPEMD256Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e394c, size: 0x6c
    // 0x8e394c: EnterFrame
    //     0x8e394c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3950: mov             fp, SP
    // 0x8e3954: AllocStack(0x8)
    //     0x8e3954: sub             SP, SP, #8
    // 0x8e3958: CheckStackOverflow
    //     0x8e3958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e395c: cmp             SP, x16
    //     0x8e3960: b.ls            #0x8e39b0
    // 0x8e3964: r0 = RIPEMD256Digest()
    //     0x8e3964: bl              #0x8e39b8  ; AllocateRIPEMD256DigestStub -> RIPEMD256Digest (size=0x38)
    // 0x8e3968: mov             x4, x0
    // 0x8e396c: r0 = "RIPEMD-256"
    //     0x8e396c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18210] "RIPEMD-256"
    //     0x8e3970: ldr             x0, [x0, #0x210]
    // 0x8e3974: stur            x4, [fp, #-8]
    // 0x8e3978: StoreField: r4->field_2b = r0
    //     0x8e3978: stur            w0, [x4, #0x2b]
    // 0x8e397c: r0 = 32
    //     0x8e397c: movz            x0, #0x20
    // 0x8e3980: StoreField: r4->field_2f = r0
    //     0x8e3980: stur            x0, [x4, #0x2f]
    // 0x8e3984: mov             x1, x4
    // 0x8e3988: r2 = Instance_Endian
    //     0x8e3988: add             x2, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e398c: ldr             x2, [x2, #0x8b8]
    // 0x8e3990: r3 = 8
    //     0x8e3990: movz            x3, #0x8
    // 0x8e3994: r5 = 16
    //     0x8e3994: movz            x5, #0x10
    // 0x8e3998: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8e3998: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8e399c: r0 = MD4FamilyDigest()
    //     0x8e399c: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8e39a0: ldur            x0, [fp, #-8]
    // 0x8e39a4: LeaveFrame
    //     0x8e39a4: mov             SP, fp
    //     0x8e39a8: ldp             fp, lr, [SP], #0x10
    // 0x8e39ac: ret
    //     0x8e39ac: ret             
    // 0x8e39b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e39b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e39b4: b               #0x8e3964
  }
  _ resetState(/* No info */) {
    // ** addr: 0xcddac8, size: 0x128
    // 0xcddac8: EnterFrame
    //     0xcddac8: stp             fp, lr, [SP, #-0x10]!
    //     0xcddacc: mov             fp, SP
    // 0xcddad0: LoadField: r2 = r1->field_1f
    //     0xcddad0: ldur            w2, [x1, #0x1f]
    // 0xcddad4: DecompressPointer r2
    //     0xcddad4: add             x2, x2, HEAP, lsl #32
    // 0xcddad8: LoadField: r3 = r2->field_b
    //     0xcddad8: ldur            w3, [x2, #0xb]
    // 0xcddadc: r4 = LoadInt32Instr(r3)
    //     0xcddadc: sbfx            x4, x3, #1, #0x1f
    // 0xcddae0: mov             x0, x4
    // 0xcddae4: r1 = 0
    //     0xcddae4: movz            x1, #0
    // 0xcddae8: cmp             x1, x0
    // 0xcddaec: b.hs            #0xcddbd0
    // 0xcddaf0: r16 = 1732584193
    //     0xcddaf0: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ca8] 0x67452301
    //     0xcddaf4: ldr             x16, [x16, #0xca8]
    // 0xcddaf8: StoreField: r2->field_f = r16
    //     0xcddaf8: stur            w16, [x2, #0xf]
    // 0xcddafc: mov             x0, x4
    // 0xcddb00: r1 = 1
    //     0xcddb00: movz            x1, #0x1
    // 0xcddb04: cmp             x1, x0
    // 0xcddb08: b.hs            #0xcddbd4
    // 0xcddb0c: r16 = 4023233417
    //     0xcddb0c: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cb0] 0xefcdab89
    //     0xcddb10: ldr             x16, [x16, #0xcb0]
    // 0xcddb14: StoreField: r2->field_13 = r16
    //     0xcddb14: stur            w16, [x2, #0x13]
    // 0xcddb18: mov             x0, x4
    // 0xcddb1c: r1 = 2
    //     0xcddb1c: movz            x1, #0x2
    // 0xcddb20: cmp             x1, x0
    // 0xcddb24: b.hs            #0xcddbd8
    // 0xcddb28: r16 = 2562383102
    //     0xcddb28: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cb8] 0x98badcfe
    //     0xcddb2c: ldr             x16, [x16, #0xcb8]
    // 0xcddb30: ArrayStore: r2[0] = r16  ; List_4
    //     0xcddb30: stur            w16, [x2, #0x17]
    // 0xcddb34: mov             x0, x4
    // 0xcddb38: r1 = 3
    //     0xcddb38: movz            x1, #0x3
    // 0xcddb3c: cmp             x1, x0
    // 0xcddb40: b.hs            #0xcddbdc
    // 0xcddb44: r16 = 543467756
    //     0xcddb44: movz            x16, #0xa8ec
    //     0xcddb48: movk            x16, #0x2064, lsl #16
    // 0xcddb4c: StoreField: r2->field_1b = r16
    //     0xcddb4c: stur            w16, [x2, #0x1b]
    // 0xcddb50: mov             x0, x4
    // 0xcddb54: r1 = 4
    //     0xcddb54: movz            x1, #0x4
    // 0xcddb58: cmp             x1, x0
    // 0xcddb5c: b.hs            #0xcddbe0
    // 0xcddb60: r16 = 1985229328
    //     0xcddb60: add             x16, PP, #0x19, lsl #12  ; [pp+0x19430] 0x76543210
    //     0xcddb64: ldr             x16, [x16, #0x430]
    // 0xcddb68: StoreField: r2->field_1f = r16
    //     0xcddb68: stur            w16, [x2, #0x1f]
    // 0xcddb6c: mov             x0, x4
    // 0xcddb70: r1 = 5
    //     0xcddb70: movz            x1, #0x5
    // 0xcddb74: cmp             x1, x0
    // 0xcddb78: b.hs            #0xcddbe4
    // 0xcddb7c: r16 = 4275878552
    //     0xcddb7c: add             x16, PP, #0x19, lsl #12  ; [pp+0x19438] 0xfedcba98
    //     0xcddb80: ldr             x16, [x16, #0x438]
    // 0xcddb84: StoreField: r2->field_23 = r16
    //     0xcddb84: stur            w16, [x2, #0x23]
    // 0xcddb88: mov             x0, x4
    // 0xcddb8c: r1 = 6
    //     0xcddb8c: movz            x1, #0x6
    // 0xcddb90: cmp             x1, x0
    // 0xcddb94: b.hs            #0xcddbe8
    // 0xcddb98: r16 = 2309737967
    //     0xcddb98: add             x16, PP, #0x19, lsl #12  ; [pp+0x19428] 0x89abcdef
    //     0xcddb9c: ldr             x16, [x16, #0x428]
    // 0xcddba0: StoreField: r2->field_27 = r16
    //     0xcddba0: stur            w16, [x2, #0x27]
    // 0xcddba4: mov             x0, x4
    // 0xcddba8: r1 = 7
    //     0xcddba8: movz            x1, #0x7
    // 0xcddbac: cmp             x1, x0
    // 0xcddbb0: b.hs            #0xcddbec
    // 0xcddbb4: r16 = 38177486
    //     0xcddbb4: movz            x16, #0x8ace
    //     0xcddbb8: movk            x16, #0x246, lsl #16
    // 0xcddbbc: StoreField: r2->field_2b = r16
    //     0xcddbbc: stur            w16, [x2, #0x2b]
    // 0xcddbc0: r0 = Null
    //     0xcddbc0: mov             x0, NULL
    // 0xcddbc4: LeaveFrame
    //     0xcddbc4: mov             SP, fp
    //     0xcddbc8: ldp             fp, lr, [SP], #0x10
    // 0xcddbcc: ret
    //     0xcddbcc: ret             
    // 0xcddbd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddbd0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddbd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddbd4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddbd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddbd8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddbdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddbdc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddbe0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddbe0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddbe4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddbe4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddbe8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddbe8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddbec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddbec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
