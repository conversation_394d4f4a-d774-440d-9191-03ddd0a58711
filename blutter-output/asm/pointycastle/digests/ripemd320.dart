// lib: impl.digest.ripemd320, url: package:pointycastle/digests/ripemd320.dart

// class id: 1050950, size: 0x8
class :: {
}

// class id: 656, size: 0x38, field offset: 0x2c
class RIPEMD320Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe04

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e3824, size: 0x58
    // 0x8e3824: EnterFrame
    //     0x8e3824: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3828: mov             fp, SP
    // 0x8e382c: AllocStack(0x8)
    //     0x8e382c: sub             SP, SP, #8
    // 0x8e3830: r0 = StaticFactoryConfig()
    //     0x8e3830: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e3834: mov             x3, x0
    // 0x8e3838: r0 = "RIPEMD-320"
    //     0x8e3838: add             x0, PP, #0x19, lsl #12  ; [pp+0x199b8] "RIPEMD-320"
    //     0x8e383c: ldr             x0, [x0, #0x9b8]
    // 0x8e3840: stur            x3, [fp, #-8]
    // 0x8e3844: StoreField: r3->field_b = r0
    //     0x8e3844: stur            w0, [x3, #0xb]
    // 0x8e3848: r1 = Function '<anonymous closure>': static.
    //     0x8e3848: add             x1, PP, #0x19, lsl #12  ; [pp+0x199c0] AnonymousClosure: static (0x8e387c), in [package:pointycastle/digests/ripemd320.dart] RIPEMD320Digest::factoryConfig (0x8e3824)
    //     0x8e384c: ldr             x1, [x1, #0x9c0]
    // 0x8e3850: r2 = Null
    //     0x8e3850: mov             x2, NULL
    // 0x8e3854: r0 = AllocateClosure()
    //     0x8e3854: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3858: mov             x1, x0
    // 0x8e385c: ldur            x0, [fp, #-8]
    // 0x8e3860: StoreField: r0->field_f = r1
    //     0x8e3860: stur            w1, [x0, #0xf]
    // 0x8e3864: r1 = Digest
    //     0x8e3864: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3868: ldr             x1, [x1, #0x388]
    // 0x8e386c: StoreField: r0->field_7 = r1
    //     0x8e386c: stur            w1, [x0, #7]
    // 0x8e3870: LeaveFrame
    //     0x8e3870: mov             SP, fp
    //     0x8e3874: ldp             fp, lr, [SP], #0x10
    // 0x8e3878: ret
    //     0x8e3878: ret             
  }
  [closure] static RIPEMD320Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e387c, size: 0x6c
    // 0x8e387c: EnterFrame
    //     0x8e387c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3880: mov             fp, SP
    // 0x8e3884: AllocStack(0x8)
    //     0x8e3884: sub             SP, SP, #8
    // 0x8e3888: CheckStackOverflow
    //     0x8e3888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e388c: cmp             SP, x16
    //     0x8e3890: b.ls            #0x8e38e0
    // 0x8e3894: r0 = RIPEMD320Digest()
    //     0x8e3894: bl              #0x8e38e8  ; AllocateRIPEMD320DigestStub -> RIPEMD320Digest (size=0x38)
    // 0x8e3898: mov             x4, x0
    // 0x8e389c: r0 = "RIPEMD-320"
    //     0x8e389c: add             x0, PP, #0x19, lsl #12  ; [pp+0x199b8] "RIPEMD-320"
    //     0x8e38a0: ldr             x0, [x0, #0x9b8]
    // 0x8e38a4: stur            x4, [fp, #-8]
    // 0x8e38a8: StoreField: r4->field_2b = r0
    //     0x8e38a8: stur            w0, [x4, #0x2b]
    // 0x8e38ac: r0 = 40
    //     0x8e38ac: movz            x0, #0x28
    // 0x8e38b0: StoreField: r4->field_2f = r0
    //     0x8e38b0: stur            x0, [x4, #0x2f]
    // 0x8e38b4: mov             x1, x4
    // 0x8e38b8: r2 = Instance_Endian
    //     0x8e38b8: add             x2, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e38bc: ldr             x2, [x2, #0x8b8]
    // 0x8e38c0: r3 = 10
    //     0x8e38c0: movz            x3, #0xa
    // 0x8e38c4: r5 = 16
    //     0x8e38c4: movz            x5, #0x10
    // 0x8e38c8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8e38c8: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8e38cc: r0 = MD4FamilyDigest()
    //     0x8e38cc: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8e38d0: ldur            x0, [fp, #-8]
    // 0x8e38d4: LeaveFrame
    //     0x8e38d4: mov             SP, fp
    //     0x8e38d8: ldp             fp, lr, [SP], #0x10
    // 0x8e38dc: ret
    //     0x8e38dc: ret             
    // 0x8e38e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e38e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e38e4: b               #0x8e3894
  }
  _ resetState(/* No info */) {
    // ** addr: 0xcddbf0, size: 0x168
    // 0xcddbf0: EnterFrame
    //     0xcddbf0: stp             fp, lr, [SP, #-0x10]!
    //     0xcddbf4: mov             fp, SP
    // 0xcddbf8: LoadField: r2 = r1->field_1f
    //     0xcddbf8: ldur            w2, [x1, #0x1f]
    // 0xcddbfc: DecompressPointer r2
    //     0xcddbfc: add             x2, x2, HEAP, lsl #32
    // 0xcddc00: LoadField: r3 = r2->field_b
    //     0xcddc00: ldur            w3, [x2, #0xb]
    // 0xcddc04: r4 = LoadInt32Instr(r3)
    //     0xcddc04: sbfx            x4, x3, #1, #0x1f
    // 0xcddc08: mov             x0, x4
    // 0xcddc0c: r1 = 0
    //     0xcddc0c: movz            x1, #0
    // 0xcddc10: cmp             x1, x0
    // 0xcddc14: b.hs            #0xcddd30
    // 0xcddc18: r16 = 1732584193
    //     0xcddc18: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ca8] 0x67452301
    //     0xcddc1c: ldr             x16, [x16, #0xca8]
    // 0xcddc20: StoreField: r2->field_f = r16
    //     0xcddc20: stur            w16, [x2, #0xf]
    // 0xcddc24: mov             x0, x4
    // 0xcddc28: r1 = 1
    //     0xcddc28: movz            x1, #0x1
    // 0xcddc2c: cmp             x1, x0
    // 0xcddc30: b.hs            #0xcddd34
    // 0xcddc34: r16 = 4023233417
    //     0xcddc34: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cb0] 0xefcdab89
    //     0xcddc38: ldr             x16, [x16, #0xcb0]
    // 0xcddc3c: StoreField: r2->field_13 = r16
    //     0xcddc3c: stur            w16, [x2, #0x13]
    // 0xcddc40: mov             x0, x4
    // 0xcddc44: r1 = 2
    //     0xcddc44: movz            x1, #0x2
    // 0xcddc48: cmp             x1, x0
    // 0xcddc4c: b.hs            #0xcddd38
    // 0xcddc50: r16 = 2562383102
    //     0xcddc50: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cb8] 0x98badcfe
    //     0xcddc54: ldr             x16, [x16, #0xcb8]
    // 0xcddc58: ArrayStore: r2[0] = r16  ; List_4
    //     0xcddc58: stur            w16, [x2, #0x17]
    // 0xcddc5c: mov             x0, x4
    // 0xcddc60: r1 = 3
    //     0xcddc60: movz            x1, #0x3
    // 0xcddc64: cmp             x1, x0
    // 0xcddc68: b.hs            #0xcddd3c
    // 0xcddc6c: r16 = 543467756
    //     0xcddc6c: movz            x16, #0xa8ec
    //     0xcddc70: movk            x16, #0x2064, lsl #16
    // 0xcddc74: StoreField: r2->field_1b = r16
    //     0xcddc74: stur            w16, [x2, #0x1b]
    // 0xcddc78: mov             x0, x4
    // 0xcddc7c: r1 = 4
    //     0xcddc7c: movz            x1, #0x4
    // 0xcddc80: cmp             x1, x0
    // 0xcddc84: b.hs            #0xcddd40
    // 0xcddc88: r16 = 3285377520
    //     0xcddc88: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cc0] 0xc3d2e1f0
    //     0xcddc8c: ldr             x16, [x16, #0xcc0]
    // 0xcddc90: StoreField: r2->field_1f = r16
    //     0xcddc90: stur            w16, [x2, #0x1f]
    // 0xcddc94: mov             x0, x4
    // 0xcddc98: r1 = 5
    //     0xcddc98: movz            x1, #0x5
    // 0xcddc9c: cmp             x1, x0
    // 0xcddca0: b.hs            #0xcddd44
    // 0xcddca4: r16 = 1985229328
    //     0xcddca4: add             x16, PP, #0x19, lsl #12  ; [pp+0x19430] 0x76543210
    //     0xcddca8: ldr             x16, [x16, #0x430]
    // 0xcddcac: StoreField: r2->field_23 = r16
    //     0xcddcac: stur            w16, [x2, #0x23]
    // 0xcddcb0: mov             x0, x4
    // 0xcddcb4: r1 = 6
    //     0xcddcb4: movz            x1, #0x6
    // 0xcddcb8: cmp             x1, x0
    // 0xcddcbc: b.hs            #0xcddd48
    // 0xcddcc0: r16 = 4275878552
    //     0xcddcc0: add             x16, PP, #0x19, lsl #12  ; [pp+0x19438] 0xfedcba98
    //     0xcddcc4: ldr             x16, [x16, #0x438]
    // 0xcddcc8: StoreField: r2->field_27 = r16
    //     0xcddcc8: stur            w16, [x2, #0x27]
    // 0xcddccc: mov             x0, x4
    // 0xcddcd0: r1 = 7
    //     0xcddcd0: movz            x1, #0x7
    // 0xcddcd4: cmp             x1, x0
    // 0xcddcd8: b.hs            #0xcddd4c
    // 0xcddcdc: r16 = 2309737967
    //     0xcddcdc: add             x16, PP, #0x19, lsl #12  ; [pp+0x19428] 0x89abcdef
    //     0xcddce0: ldr             x16, [x16, #0x428]
    // 0xcddce4: StoreField: r2->field_2b = r16
    //     0xcddce4: stur            w16, [x2, #0x2b]
    // 0xcddce8: mov             x0, x4
    // 0xcddcec: r1 = 8
    //     0xcddcec: movz            x1, #0x8
    // 0xcddcf0: cmp             x1, x0
    // 0xcddcf4: b.hs            #0xcddd50
    // 0xcddcf8: r16 = 38177486
    //     0xcddcf8: movz            x16, #0x8ace
    //     0xcddcfc: movk            x16, #0x246, lsl #16
    // 0xcddd00: StoreField: r2->field_2f = r16
    //     0xcddd00: stur            w16, [x2, #0x2f]
    // 0xcddd04: mov             x0, x4
    // 0xcddd08: r1 = 9
    //     0xcddd08: movz            x1, #0x9
    // 0xcddd0c: cmp             x1, x0
    // 0xcddd10: b.hs            #0xcddd54
    // 0xcddd14: r16 = 2019179550
    //     0xcddd14: movz            x16, #0x3c1e
    //     0xcddd18: movk            x16, #0x785a, lsl #16
    // 0xcddd1c: StoreField: r2->field_33 = r16
    //     0xcddd1c: stur            w16, [x2, #0x33]
    // 0xcddd20: r0 = Null
    //     0xcddd20: mov             x0, NULL
    // 0xcddd24: LeaveFrame
    //     0xcddd24: mov             SP, fp
    //     0xcddd28: ldp             fp, lr, [SP], #0x10
    // 0xcddd2c: ret
    //     0xcddd2c: ret             
    // 0xcddd30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddd34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddd38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd38: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddd3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddd40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd40: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddd44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddd48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd48: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddd4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd4c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddd50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd50: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddd54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddd54: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
