// lib: impl.digest.sha1, url: package:pointycastle/digests/sha1.dart

// class id: 1050951, size: 0x8
class :: {
}

// class id: 655, size: 0x38, field offset: 0x2c
class SHA1Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe08

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e3754, size: 0x58
    // 0x8e3754: EnterFrame
    //     0x8e3754: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3758: mov             fp, SP
    // 0x8e375c: AllocStack(0x8)
    //     0x8e375c: sub             SP, SP, #8
    // 0x8e3760: r0 = StaticFactoryConfig()
    //     0x8e3760: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e3764: mov             x3, x0
    // 0x8e3768: r0 = "SHA-1"
    //     0x8e3768: add             x0, PP, #0x18, lsl #12  ; [pp+0x18220] "SHA-1"
    //     0x8e376c: ldr             x0, [x0, #0x220]
    // 0x8e3770: stur            x3, [fp, #-8]
    // 0x8e3774: StoreField: r3->field_b = r0
    //     0x8e3774: stur            w0, [x3, #0xb]
    // 0x8e3778: r1 = Function '<anonymous closure>': static.
    //     0x8e3778: add             x1, PP, #0x19, lsl #12  ; [pp+0x199b0] AnonymousClosure: static (0x8e37ac), in [package:pointycastle/digests/sha1.dart] SHA1Digest::factoryConfig (0x8e3754)
    //     0x8e377c: ldr             x1, [x1, #0x9b0]
    // 0x8e3780: r2 = Null
    //     0x8e3780: mov             x2, NULL
    // 0x8e3784: r0 = AllocateClosure()
    //     0x8e3784: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3788: mov             x1, x0
    // 0x8e378c: ldur            x0, [fp, #-8]
    // 0x8e3790: StoreField: r0->field_f = r1
    //     0x8e3790: stur            w1, [x0, #0xf]
    // 0x8e3794: r1 = Digest
    //     0x8e3794: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3798: ldr             x1, [x1, #0x388]
    // 0x8e379c: StoreField: r0->field_7 = r1
    //     0x8e379c: stur            w1, [x0, #7]
    // 0x8e37a0: LeaveFrame
    //     0x8e37a0: mov             SP, fp
    //     0x8e37a4: ldp             fp, lr, [SP], #0x10
    // 0x8e37a8: ret
    //     0x8e37a8: ret             
  }
  [closure] static SHA1Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e37ac, size: 0x6c
    // 0x8e37ac: EnterFrame
    //     0x8e37ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8e37b0: mov             fp, SP
    // 0x8e37b4: AllocStack(0x8)
    //     0x8e37b4: sub             SP, SP, #8
    // 0x8e37b8: CheckStackOverflow
    //     0x8e37b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e37bc: cmp             SP, x16
    //     0x8e37c0: b.ls            #0x8e3810
    // 0x8e37c4: r0 = SHA1Digest()
    //     0x8e37c4: bl              #0x8e3818  ; AllocateSHA1DigestStub -> SHA1Digest (size=0x38)
    // 0x8e37c8: mov             x4, x0
    // 0x8e37cc: r0 = "SHA-1"
    //     0x8e37cc: add             x0, PP, #0x18, lsl #12  ; [pp+0x18220] "SHA-1"
    //     0x8e37d0: ldr             x0, [x0, #0x220]
    // 0x8e37d4: stur            x4, [fp, #-8]
    // 0x8e37d8: StoreField: r4->field_2b = r0
    //     0x8e37d8: stur            w0, [x4, #0x2b]
    // 0x8e37dc: r0 = 20
    //     0x8e37dc: movz            x0, #0x14
    // 0x8e37e0: StoreField: r4->field_2f = r0
    //     0x8e37e0: stur            x0, [x4, #0x2f]
    // 0x8e37e4: mov             x1, x4
    // 0x8e37e8: r2 = Instance_Endian
    //     0x8e37e8: add             x2, PP, #0x12, lsl #12  ; [pp+0x12390] Obj!Endian@e2cbb1
    //     0x8e37ec: ldr             x2, [x2, #0x390]
    // 0x8e37f0: r3 = 5
    //     0x8e37f0: movz            x3, #0x5
    // 0x8e37f4: r5 = 80
    //     0x8e37f4: movz            x5, #0x50
    // 0x8e37f8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8e37f8: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8e37fc: r0 = MD4FamilyDigest()
    //     0x8e37fc: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8e3800: ldur            x0, [fp, #-8]
    // 0x8e3804: LeaveFrame
    //     0x8e3804: mov             SP, fp
    //     0x8e3808: ldp             fp, lr, [SP], #0x10
    // 0x8e380c: ret
    //     0x8e380c: ret             
    // 0x8e3810: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3810: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3814: b               #0x8e37c4
  }
  _ processBlock(/* No info */) {
    // ** addr: 0x8e7408, size: 0x1798
    // 0x8e7408: EnterFrame
    //     0x8e7408: stp             fp, lr, [SP, #-0x10]!
    //     0x8e740c: mov             fp, SP
    // 0x8e7410: AllocStack(0x90)
    //     0x8e7410: sub             SP, SP, #0x90
    // 0x8e7414: SetupParameters(SHA1Digest this /* r1 => r3, fp-0x28 */)
    //     0x8e7414: mov             x3, x1
    //     0x8e7418: stur            x1, [fp, #-0x28]
    // 0x8e741c: CheckStackOverflow
    //     0x8e741c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e7420: cmp             SP, x16
    //     0x8e7424: b.ls            #0x8e8a78
    // 0x8e7428: LoadField: r4 = r3->field_23
    //     0x8e7428: ldur            w4, [x3, #0x23]
    // 0x8e742c: DecompressPointer r4
    //     0x8e742c: add             x4, x4, HEAP, lsl #32
    // 0x8e7430: stur            x4, [fp, #-0x20]
    // 0x8e7434: LoadField: r0 = r4->field_b
    //     0x8e7434: ldur            w0, [x4, #0xb]
    // 0x8e7438: r5 = LoadInt32Instr(r0)
    //     0x8e7438: sbfx            x5, x0, #1, #0x1f
    // 0x8e743c: stur            x5, [fp, #-0x18]
    // 0x8e7440: r6 = 16
    //     0x8e7440: movz            x6, #0x10
    // 0x8e7444: stur            x6, [fp, #-0x10]
    // 0x8e7448: CheckStackOverflow
    //     0x8e7448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e744c: cmp             SP, x16
    //     0x8e7450: b.ls            #0x8e8a80
    // 0x8e7454: cmp             x6, #0x50
    // 0x8e7458: b.ge            #0x8e75a0
    // 0x8e745c: sub             x2, x6, #3
    // 0x8e7460: mov             x0, x5
    // 0x8e7464: mov             x1, x2
    // 0x8e7468: cmp             x1, x0
    // 0x8e746c: b.hs            #0x8e8a88
    // 0x8e7470: ArrayLoad: r7 = r4[r2]  ; Unknown_4
    //     0x8e7470: add             x16, x4, x2, lsl #2
    //     0x8e7474: ldur            w7, [x16, #0xf]
    // 0x8e7478: DecompressPointer r7
    //     0x8e7478: add             x7, x7, HEAP, lsl #32
    // 0x8e747c: sub             x2, x6, #8
    // 0x8e7480: mov             x0, x5
    // 0x8e7484: mov             x1, x2
    // 0x8e7488: cmp             x1, x0
    // 0x8e748c: b.hs            #0x8e8a8c
    // 0x8e7490: ArrayLoad: r0 = r4[r2]  ; Unknown_4
    //     0x8e7490: add             x16, x4, x2, lsl #2
    //     0x8e7494: ldur            w0, [x16, #0xf]
    // 0x8e7498: DecompressPointer r0
    //     0x8e7498: add             x0, x0, HEAP, lsl #32
    // 0x8e749c: r1 = LoadInt32Instr(r7)
    //     0x8e749c: sbfx            x1, x7, #1, #0x1f
    //     0x8e74a0: tbz             w7, #0, #0x8e74a8
    //     0x8e74a4: ldur            x1, [x7, #7]
    // 0x8e74a8: r2 = LoadInt32Instr(r0)
    //     0x8e74a8: sbfx            x2, x0, #1, #0x1f
    //     0x8e74ac: tbz             w0, #0, #0x8e74b4
    //     0x8e74b0: ldur            x2, [x0, #7]
    // 0x8e74b4: eor             x7, x1, x2
    // 0x8e74b8: sub             x2, x6, #0xe
    // 0x8e74bc: mov             x0, x5
    // 0x8e74c0: mov             x1, x2
    // 0x8e74c4: cmp             x1, x0
    // 0x8e74c8: b.hs            #0x8e8a90
    // 0x8e74cc: ArrayLoad: r0 = r4[r2]  ; Unknown_4
    //     0x8e74cc: add             x16, x4, x2, lsl #2
    //     0x8e74d0: ldur            w0, [x16, #0xf]
    // 0x8e74d4: DecompressPointer r0
    //     0x8e74d4: add             x0, x0, HEAP, lsl #32
    // 0x8e74d8: r1 = LoadInt32Instr(r0)
    //     0x8e74d8: sbfx            x1, x0, #1, #0x1f
    //     0x8e74dc: tbz             w0, #0, #0x8e74e4
    //     0x8e74e0: ldur            x1, [x0, #7]
    // 0x8e74e4: eor             x2, x7, x1
    // 0x8e74e8: sub             x7, x6, #0x10
    // 0x8e74ec: mov             x0, x5
    // 0x8e74f0: mov             x1, x7
    // 0x8e74f4: cmp             x1, x0
    // 0x8e74f8: b.hs            #0x8e8a94
    // 0x8e74fc: ArrayLoad: r0 = r4[r7]  ; Unknown_4
    //     0x8e74fc: add             x16, x4, x7, lsl #2
    //     0x8e7500: ldur            w0, [x16, #0xf]
    // 0x8e7504: DecompressPointer r0
    //     0x8e7504: add             x0, x0, HEAP, lsl #32
    // 0x8e7508: r1 = LoadInt32Instr(r0)
    //     0x8e7508: sbfx            x1, x0, #1, #0x1f
    //     0x8e750c: tbz             w0, #0, #0x8e7514
    //     0x8e7510: ldur            x1, [x0, #7]
    // 0x8e7514: eor             x0, x2, x1
    // 0x8e7518: mov             x1, x0
    // 0x8e751c: stur            x0, [fp, #-8]
    // 0x8e7520: r2 = 1
    //     0x8e7520: movz            x2, #0x1
    // 0x8e7524: r0 = shiftl32()
    //     0x8e7524: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7528: mov             x1, x0
    // 0x8e752c: ldur            x0, [fp, #-8]
    // 0x8e7530: asr             x2, x0, #0x1f
    // 0x8e7534: orr             x3, x1, x2
    // 0x8e7538: ldur            x0, [fp, #-0x18]
    // 0x8e753c: ldur            x1, [fp, #-0x10]
    // 0x8e7540: cmp             x1, x0
    // 0x8e7544: b.hs            #0x8e8a98
    // 0x8e7548: r0 = BoxInt64Instr(r3)
    //     0x8e7548: sbfiz           x0, x3, #1, #0x1f
    //     0x8e754c: cmp             x3, x0, asr #1
    //     0x8e7550: b.eq            #0x8e755c
    //     0x8e7554: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e7558: stur            x3, [x0, #7]
    // 0x8e755c: ldur            x1, [fp, #-0x20]
    // 0x8e7560: ldur            x2, [fp, #-0x10]
    // 0x8e7564: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8e7564: add             x25, x1, x2, lsl #2
    //     0x8e7568: add             x25, x25, #0xf
    //     0x8e756c: str             w0, [x25]
    //     0x8e7570: tbz             w0, #0, #0x8e758c
    //     0x8e7574: ldurb           w16, [x1, #-1]
    //     0x8e7578: ldurb           w17, [x0, #-1]
    //     0x8e757c: and             x16, x17, x16, lsr #2
    //     0x8e7580: tst             x16, HEAP, lsr #32
    //     0x8e7584: b.eq            #0x8e758c
    //     0x8e7588: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e758c: add             x6, x2, #1
    // 0x8e7590: ldur            x3, [fp, #-0x28]
    // 0x8e7594: ldur            x4, [fp, #-0x20]
    // 0x8e7598: ldur            x5, [fp, #-0x18]
    // 0x8e759c: b               #0x8e7444
    // 0x8e75a0: mov             x0, x3
    // 0x8e75a4: mov             x3, x4
    // 0x8e75a8: LoadField: r4 = r0->field_1f
    //     0x8e75a8: ldur            w4, [x0, #0x1f]
    // 0x8e75ac: DecompressPointer r4
    //     0x8e75ac: add             x4, x4, HEAP, lsl #32
    // 0x8e75b0: stur            x4, [fp, #-0x58]
    // 0x8e75b4: LoadField: r0 = r4->field_b
    //     0x8e75b4: ldur            w0, [x4, #0xb]
    // 0x8e75b8: r2 = LoadInt32Instr(r0)
    //     0x8e75b8: sbfx            x2, x0, #1, #0x1f
    // 0x8e75bc: mov             x0, x2
    // 0x8e75c0: r1 = 0
    //     0x8e75c0: movz            x1, #0
    // 0x8e75c4: cmp             x1, x0
    // 0x8e75c8: b.hs            #0x8e8a9c
    // 0x8e75cc: LoadField: r5 = r4->field_f
    //     0x8e75cc: ldur            w5, [x4, #0xf]
    // 0x8e75d0: DecompressPointer r5
    //     0x8e75d0: add             x5, x5, HEAP, lsl #32
    // 0x8e75d4: mov             x0, x2
    // 0x8e75d8: r1 = 1
    //     0x8e75d8: movz            x1, #0x1
    // 0x8e75dc: cmp             x1, x0
    // 0x8e75e0: b.hs            #0x8e8aa0
    // 0x8e75e4: LoadField: r6 = r4->field_13
    //     0x8e75e4: ldur            w6, [x4, #0x13]
    // 0x8e75e8: DecompressPointer r6
    //     0x8e75e8: add             x6, x6, HEAP, lsl #32
    // 0x8e75ec: mov             x0, x2
    // 0x8e75f0: r1 = 2
    //     0x8e75f0: movz            x1, #0x2
    // 0x8e75f4: cmp             x1, x0
    // 0x8e75f8: b.hs            #0x8e8aa4
    // 0x8e75fc: ArrayLoad: r7 = r4[0]  ; List_4
    //     0x8e75fc: ldur            w7, [x4, #0x17]
    // 0x8e7600: DecompressPointer r7
    //     0x8e7600: add             x7, x7, HEAP, lsl #32
    // 0x8e7604: mov             x0, x2
    // 0x8e7608: r1 = 3
    //     0x8e7608: movz            x1, #0x3
    // 0x8e760c: cmp             x1, x0
    // 0x8e7610: b.hs            #0x8e8aa8
    // 0x8e7614: LoadField: r8 = r4->field_1b
    //     0x8e7614: ldur            w8, [x4, #0x1b]
    // 0x8e7618: DecompressPointer r8
    //     0x8e7618: add             x8, x8, HEAP, lsl #32
    // 0x8e761c: mov             x0, x2
    // 0x8e7620: r1 = 4
    //     0x8e7620: movz            x1, #0x4
    // 0x8e7624: cmp             x1, x0
    // 0x8e7628: b.hs            #0x8e8aac
    // 0x8e762c: LoadField: r0 = r4->field_1f
    //     0x8e762c: ldur            w0, [x4, #0x1f]
    // 0x8e7630: DecompressPointer r0
    //     0x8e7630: add             x0, x0, HEAP, lsl #32
    // 0x8e7634: r1 = LoadInt32Instr(r5)
    //     0x8e7634: sbfx            x1, x5, #1, #0x1f
    //     0x8e7638: tbz             w5, #0, #0x8e7640
    //     0x8e763c: ldur            x1, [x5, #7]
    // 0x8e7640: r2 = LoadInt32Instr(r6)
    //     0x8e7640: sbfx            x2, x6, #1, #0x1f
    //     0x8e7644: tbz             w6, #0, #0x8e764c
    //     0x8e7648: ldur            x2, [x6, #7]
    // 0x8e764c: r5 = LoadInt32Instr(r7)
    //     0x8e764c: sbfx            x5, x7, #1, #0x1f
    //     0x8e7650: tbz             w7, #0, #0x8e7658
    //     0x8e7654: ldur            x5, [x7, #7]
    // 0x8e7658: r6 = LoadInt32Instr(r8)
    //     0x8e7658: sbfx            x6, x8, #1, #0x1f
    //     0x8e765c: tbz             w8, #0, #0x8e7664
    //     0x8e7660: ldur            x6, [x8, #7]
    // 0x8e7664: r7 = LoadInt32Instr(r0)
    //     0x8e7664: sbfx            x7, x0, #1, #0x1f
    //     0x8e7668: tbz             w0, #0, #0x8e7670
    //     0x8e766c: ldur            x7, [x0, #7]
    // 0x8e7670: LoadField: r0 = r3->field_b
    //     0x8e7670: ldur            w0, [x3, #0xb]
    // 0x8e7674: r8 = LoadInt32Instr(r0)
    //     0x8e7674: sbfx            x8, x0, #1, #0x1f
    // 0x8e7678: stur            x8, [fp, #-0x50]
    // 0x8e767c: mov             x11, x1
    // 0x8e7680: mov             x10, x2
    // 0x8e7684: mov             x9, x5
    // 0x8e7688: mov             x16, x7
    // 0x8e768c: mov             x7, x6
    // 0x8e7690: mov             x6, x16
    // 0x8e7694: r5 = 0
    //     0x8e7694: movz            x5, #0
    // 0x8e7698: r0 = 0
    //     0x8e7698: movz            x0, #0
    // 0x8e769c: stur            x11, [fp, #-8]
    // 0x8e76a0: stur            x10, [fp, #-0x10]
    // 0x8e76a4: stur            x9, [fp, #-0x18]
    // 0x8e76a8: stur            x7, [fp, #-0x30]
    // 0x8e76ac: stur            x6, [fp, #-0x38]
    // 0x8e76b0: stur            x5, [fp, #-0x40]
    // 0x8e76b4: stur            x0, [fp, #-0x48]
    // 0x8e76b8: CheckStackOverflow
    //     0x8e76b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e76bc: cmp             SP, x16
    //     0x8e76c0: b.ls            #0x8e8ab0
    // 0x8e76c4: cmp             x0, #4
    // 0x8e76c8: b.ge            #0x8e7adc
    // 0x8e76cc: mov             x1, x11
    // 0x8e76d0: r2 = 5
    //     0x8e76d0: movz            x2, #0x5
    // 0x8e76d4: r0 = shiftl32()
    //     0x8e76d4: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e76d8: ldur            x3, [fp, #-8]
    // 0x8e76dc: asr             x1, x3, #0x1b
    // 0x8e76e0: orr             x2, x0, x1
    // 0x8e76e4: ldur            x0, [fp, #-0x38]
    // 0x8e76e8: add             x1, x0, x2
    // 0x8e76ec: ldur            x5, [fp, #-0x10]
    // 0x8e76f0: ldur            x4, [fp, #-0x18]
    // 0x8e76f4: and             x0, x5, x4
    // 0x8e76f8: mvn             x2, x5
    // 0x8e76fc: ldur            x6, [fp, #-0x30]
    // 0x8e7700: and             x7, x2, x6
    // 0x8e7704: orr             x2, x0, x7
    // 0x8e7708: add             x7, x1, x2
    // 0x8e770c: ldur            x2, [fp, #-0x40]
    // 0x8e7710: add             x8, x2, #1
    // 0x8e7714: ldur            x0, [fp, #-0x50]
    // 0x8e7718: mov             x1, x2
    // 0x8e771c: stur            x8, [fp, #-0x68]
    // 0x8e7720: cmp             x1, x0
    // 0x8e7724: b.hs            #0x8e8ab8
    // 0x8e7728: ldur            x0, [fp, #-0x20]
    // 0x8e772c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e772c: add             x16, x0, x2, lsl #2
    //     0x8e7730: ldur            w1, [x16, #0xf]
    // 0x8e7734: DecompressPointer r1
    //     0x8e7734: add             x1, x1, HEAP, lsl #32
    // 0x8e7738: r2 = LoadInt32Instr(r1)
    //     0x8e7738: sbfx            x2, x1, #1, #0x1f
    //     0x8e773c: tbz             w1, #0, #0x8e7744
    //     0x8e7740: ldur            x2, [x1, #7]
    // 0x8e7744: ubfx            x7, x7, #0, #0x20
    // 0x8e7748: add             w1, w7, w2
    // 0x8e774c: r7 = 1518500249
    //     0x8e774c: movz            x7, #0x7999
    //     0x8e7750: movk            x7, #0x5a82, lsl #16
    // 0x8e7754: add             w9, w1, w7
    // 0x8e7758: mov             x1, x5
    // 0x8e775c: stur            x9, [fp, #-0x60]
    // 0x8e7760: r2 = 30
    //     0x8e7760: movz            x2, #0x1e
    // 0x8e7764: r0 = shiftl32()
    //     0x8e7764: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7768: ldur            x1, [fp, #-0x10]
    // 0x8e776c: asr             x2, x1, #2
    // 0x8e7770: orr             x3, x0, x2
    // 0x8e7774: stur            x3, [fp, #-0x70]
    // 0x8e7778: ldur            x0, [fp, #-0x60]
    // 0x8e777c: ubfx            x0, x0, #0, #0x20
    // 0x8e7780: mov             x1, x0
    // 0x8e7784: r2 = 5
    //     0x8e7784: movz            x2, #0x5
    // 0x8e7788: r0 = shiftl32()
    //     0x8e7788: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e778c: ldur            x1, [fp, #-0x60]
    // 0x8e7790: ubfx            x1, x1, #0, #0x20
    // 0x8e7794: asr             x2, x1, #0x1b
    // 0x8e7798: orr             x1, x0, x2
    // 0x8e779c: ldur            x3, [fp, #-0x30]
    // 0x8e77a0: add             x0, x3, x1
    // 0x8e77a4: ldur            x4, [fp, #-8]
    // 0x8e77a8: ldur            x3, [fp, #-0x70]
    // 0x8e77ac: and             x1, x4, x3
    // 0x8e77b0: mvn             x2, x4
    // 0x8e77b4: ldur            x5, [fp, #-0x18]
    // 0x8e77b8: and             x6, x2, x5
    // 0x8e77bc: orr             x2, x1, x6
    // 0x8e77c0: add             x6, x0, x2
    // 0x8e77c4: ldur            x2, [fp, #-0x68]
    // 0x8e77c8: add             x7, x2, #1
    // 0x8e77cc: ldur            x0, [fp, #-0x50]
    // 0x8e77d0: mov             x1, x2
    // 0x8e77d4: stur            x7, [fp, #-0x78]
    // 0x8e77d8: cmp             x1, x0
    // 0x8e77dc: b.hs            #0x8e8abc
    // 0x8e77e0: ldur            x0, [fp, #-0x20]
    // 0x8e77e4: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e77e4: add             x16, x0, x2, lsl #2
    //     0x8e77e8: ldur            w1, [x16, #0xf]
    // 0x8e77ec: DecompressPointer r1
    //     0x8e77ec: add             x1, x1, HEAP, lsl #32
    // 0x8e77f0: r2 = LoadInt32Instr(r1)
    //     0x8e77f0: sbfx            x2, x1, #1, #0x1f
    //     0x8e77f4: tbz             w1, #0, #0x8e77fc
    //     0x8e77f8: ldur            x2, [x1, #7]
    // 0x8e77fc: ubfx            x6, x6, #0, #0x20
    // 0x8e7800: add             w1, w6, w2
    // 0x8e7804: r6 = 1518500249
    //     0x8e7804: movz            x6, #0x7999
    //     0x8e7808: movk            x6, #0x5a82, lsl #16
    // 0x8e780c: add             w8, w1, w6
    // 0x8e7810: mov             x1, x4
    // 0x8e7814: stur            x8, [fp, #-0x68]
    // 0x8e7818: r2 = 30
    //     0x8e7818: movz            x2, #0x1e
    // 0x8e781c: r0 = shiftl32()
    //     0x8e781c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7820: ldur            x4, [fp, #-8]
    // 0x8e7824: asr             x1, x4, #2
    // 0x8e7828: orr             x3, x0, x1
    // 0x8e782c: stur            x3, [fp, #-0x80]
    // 0x8e7830: ldur            x0, [fp, #-0x68]
    // 0x8e7834: ubfx            x0, x0, #0, #0x20
    // 0x8e7838: mov             x1, x0
    // 0x8e783c: r2 = 5
    //     0x8e783c: movz            x2, #0x5
    // 0x8e7840: r0 = shiftl32()
    //     0x8e7840: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7844: ldur            x1, [fp, #-0x68]
    // 0x8e7848: ubfx            x1, x1, #0, #0x20
    // 0x8e784c: asr             x2, x1, #0x1b
    // 0x8e7850: orr             x1, x0, x2
    // 0x8e7854: ldur            x5, [fp, #-0x18]
    // 0x8e7858: add             x0, x5, x1
    // 0x8e785c: ldur            x1, [fp, #-0x80]
    // 0x8e7860: ubfx            x1, x1, #0, #0x20
    // 0x8e7864: ldur            x3, [fp, #-0x60]
    // 0x8e7868: and             x2, x3, x1
    // 0x8e786c: mov             x1, x3
    // 0x8e7870: ubfx            x1, x1, #0, #0x20
    // 0x8e7874: mvn             x4, x1
    // 0x8e7878: ldur            x5, [fp, #-0x70]
    // 0x8e787c: and             x1, x4, x5
    // 0x8e7880: ubfx            x2, x2, #0, #0x20
    // 0x8e7884: orr             x4, x2, x1
    // 0x8e7888: add             x2, x0, x4
    // 0x8e788c: ldur            x4, [fp, #-0x78]
    // 0x8e7890: add             x6, x4, #1
    // 0x8e7894: ldur            x0, [fp, #-0x50]
    // 0x8e7898: mov             x1, x4
    // 0x8e789c: stur            x6, [fp, #-0x88]
    // 0x8e78a0: cmp             x1, x0
    // 0x8e78a4: b.hs            #0x8e8ac0
    // 0x8e78a8: ldur            x0, [fp, #-0x20]
    // 0x8e78ac: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x8e78ac: add             x16, x0, x4, lsl #2
    //     0x8e78b0: ldur            w1, [x16, #0xf]
    // 0x8e78b4: DecompressPointer r1
    //     0x8e78b4: add             x1, x1, HEAP, lsl #32
    // 0x8e78b8: r4 = LoadInt32Instr(r1)
    //     0x8e78b8: sbfx            x4, x1, #1, #0x1f
    //     0x8e78bc: tbz             w1, #0, #0x8e78c4
    //     0x8e78c0: ldur            x4, [x1, #7]
    // 0x8e78c4: ubfx            x2, x2, #0, #0x20
    // 0x8e78c8: add             w1, w2, w4
    // 0x8e78cc: r4 = 1518500249
    //     0x8e78cc: movz            x4, #0x7999
    //     0x8e78d0: movk            x4, #0x5a82, lsl #16
    // 0x8e78d4: add             w7, w1, w4
    // 0x8e78d8: stur            x7, [fp, #-0x78]
    // 0x8e78dc: mov             x1, x3
    // 0x8e78e0: ubfx            x1, x1, #0, #0x20
    // 0x8e78e4: r2 = 30
    //     0x8e78e4: movz            x2, #0x1e
    // 0x8e78e8: r0 = shiftl32()
    //     0x8e78e8: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e78ec: ldur            x1, [fp, #-0x60]
    // 0x8e78f0: ubfx            x1, x1, #0, #0x20
    // 0x8e78f4: asr             x2, x1, #2
    // 0x8e78f8: orr             x6, x0, x2
    // 0x8e78fc: stur            x6, [fp, #-0x60]
    // 0x8e7900: ldur            x0, [fp, #-0x78]
    // 0x8e7904: ubfx            x0, x0, #0, #0x20
    // 0x8e7908: mov             x1, x0
    // 0x8e790c: r2 = 5
    //     0x8e790c: movz            x2, #0x5
    // 0x8e7910: r0 = shiftl32()
    //     0x8e7910: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7914: ldur            x1, [fp, #-0x78]
    // 0x8e7918: ubfx            x1, x1, #0, #0x20
    // 0x8e791c: asr             x2, x1, #0x1b
    // 0x8e7920: orr             x1, x0, x2
    // 0x8e7924: ldur            x0, [fp, #-0x70]
    // 0x8e7928: add             x2, x0, x1
    // 0x8e792c: ldur            x0, [fp, #-0x60]
    // 0x8e7930: ubfx            x0, x0, #0, #0x20
    // 0x8e7934: ldur            x3, [fp, #-0x68]
    // 0x8e7938: and             x1, x3, x0
    // 0x8e793c: mov             x0, x3
    // 0x8e7940: ubfx            x0, x0, #0, #0x20
    // 0x8e7944: mvn             x4, x0
    // 0x8e7948: ldur            x5, [fp, #-0x80]
    // 0x8e794c: and             x0, x4, x5
    // 0x8e7950: ubfx            x1, x1, #0, #0x20
    // 0x8e7954: orr             x4, x1, x0
    // 0x8e7958: add             x6, x2, x4
    // 0x8e795c: ldur            x2, [fp, #-0x88]
    // 0x8e7960: add             x4, x2, #1
    // 0x8e7964: ldur            x0, [fp, #-0x50]
    // 0x8e7968: mov             x1, x2
    // 0x8e796c: stur            x4, [fp, #-0x90]
    // 0x8e7970: cmp             x1, x0
    // 0x8e7974: b.hs            #0x8e8ac4
    // 0x8e7978: ldur            x0, [fp, #-0x20]
    // 0x8e797c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e797c: add             x16, x0, x2, lsl #2
    //     0x8e7980: ldur            w1, [x16, #0xf]
    // 0x8e7984: DecompressPointer r1
    //     0x8e7984: add             x1, x1, HEAP, lsl #32
    // 0x8e7988: r2 = LoadInt32Instr(r1)
    //     0x8e7988: sbfx            x2, x1, #1, #0x1f
    //     0x8e798c: tbz             w1, #0, #0x8e7994
    //     0x8e7990: ldur            x2, [x1, #7]
    // 0x8e7994: ubfx            x6, x6, #0, #0x20
    // 0x8e7998: add             w1, w6, w2
    // 0x8e799c: r6 = 1518500249
    //     0x8e799c: movz            x6, #0x7999
    //     0x8e79a0: movk            x6, #0x5a82, lsl #16
    // 0x8e79a4: add             w7, w1, w6
    // 0x8e79a8: stur            x7, [fp, #-0x70]
    // 0x8e79ac: mov             x1, x3
    // 0x8e79b0: ubfx            x1, x1, #0, #0x20
    // 0x8e79b4: r2 = 30
    //     0x8e79b4: movz            x2, #0x1e
    // 0x8e79b8: r0 = shiftl32()
    //     0x8e79b8: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e79bc: ldur            x1, [fp, #-0x68]
    // 0x8e79c0: ubfx            x1, x1, #0, #0x20
    // 0x8e79c4: asr             x2, x1, #2
    // 0x8e79c8: orr             x7, x0, x2
    // 0x8e79cc: stur            x7, [fp, #-0x68]
    // 0x8e79d0: ldur            x0, [fp, #-0x70]
    // 0x8e79d4: ubfx            x0, x0, #0, #0x20
    // 0x8e79d8: mov             x1, x0
    // 0x8e79dc: r2 = 5
    //     0x8e79dc: movz            x2, #0x5
    // 0x8e79e0: r0 = shiftl32()
    //     0x8e79e0: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e79e4: ldur            x1, [fp, #-0x70]
    // 0x8e79e8: ubfx            x1, x1, #0, #0x20
    // 0x8e79ec: asr             x2, x1, #0x1b
    // 0x8e79f0: orr             x1, x0, x2
    // 0x8e79f4: ldur            x0, [fp, #-0x80]
    // 0x8e79f8: add             x2, x0, x1
    // 0x8e79fc: ldur            x0, [fp, #-0x68]
    // 0x8e7a00: ubfx            x0, x0, #0, #0x20
    // 0x8e7a04: ldur            x3, [fp, #-0x78]
    // 0x8e7a08: and             x1, x3, x0
    // 0x8e7a0c: mov             x0, x3
    // 0x8e7a10: ubfx            x0, x0, #0, #0x20
    // 0x8e7a14: mvn             x4, x0
    // 0x8e7a18: ldur            x6, [fp, #-0x60]
    // 0x8e7a1c: and             x0, x4, x6
    // 0x8e7a20: ubfx            x1, x1, #0, #0x20
    // 0x8e7a24: orr             x4, x1, x0
    // 0x8e7a28: add             x5, x2, x4
    // 0x8e7a2c: ldur            x2, [fp, #-0x90]
    // 0x8e7a30: add             x4, x2, #1
    // 0x8e7a34: ldur            x0, [fp, #-0x50]
    // 0x8e7a38: mov             x1, x2
    // 0x8e7a3c: stur            x4, [fp, #-0x88]
    // 0x8e7a40: cmp             x1, x0
    // 0x8e7a44: b.hs            #0x8e8ac8
    // 0x8e7a48: ldur            x0, [fp, #-0x20]
    // 0x8e7a4c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e7a4c: add             x16, x0, x2, lsl #2
    //     0x8e7a50: ldur            w1, [x16, #0xf]
    // 0x8e7a54: DecompressPointer r1
    //     0x8e7a54: add             x1, x1, HEAP, lsl #32
    // 0x8e7a58: r2 = LoadInt32Instr(r1)
    //     0x8e7a58: sbfx            x2, x1, #1, #0x1f
    //     0x8e7a5c: tbz             w1, #0, #0x8e7a64
    //     0x8e7a60: ldur            x2, [x1, #7]
    // 0x8e7a64: ubfx            x5, x5, #0, #0x20
    // 0x8e7a68: add             w1, w5, w2
    // 0x8e7a6c: r5 = 1518500249
    //     0x8e7a6c: movz            x5, #0x7999
    //     0x8e7a70: movk            x5, #0x5a82, lsl #16
    // 0x8e7a74: add             w7, w1, w5
    // 0x8e7a78: stur            x7, [fp, #-0x80]
    // 0x8e7a7c: mov             x1, x3
    // 0x8e7a80: ubfx            x1, x1, #0, #0x20
    // 0x8e7a84: r2 = 30
    //     0x8e7a84: movz            x2, #0x1e
    // 0x8e7a88: r0 = shiftl32()
    //     0x8e7a88: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7a8c: ldur            x1, [fp, #-0x78]
    // 0x8e7a90: ubfx            x1, x1, #0, #0x20
    // 0x8e7a94: asr             x2, x1, #2
    // 0x8e7a98: orr             x9, x0, x2
    // 0x8e7a9c: ldur            x0, [fp, #-0x48]
    // 0x8e7aa0: add             x1, x0, #1
    // 0x8e7aa4: ldur            x2, [fp, #-0x70]
    // 0x8e7aa8: ubfx            x2, x2, #0, #0x20
    // 0x8e7aac: ldur            x3, [fp, #-0x80]
    // 0x8e7ab0: ubfx            x3, x3, #0, #0x20
    // 0x8e7ab4: mov             x11, x3
    // 0x8e7ab8: mov             x10, x2
    // 0x8e7abc: ldur            x7, [fp, #-0x68]
    // 0x8e7ac0: ldur            x6, [fp, #-0x60]
    // 0x8e7ac4: ldur            x5, [fp, #-0x88]
    // 0x8e7ac8: mov             x0, x1
    // 0x8e7acc: ldur            x4, [fp, #-0x58]
    // 0x8e7ad0: ldur            x3, [fp, #-0x20]
    // 0x8e7ad4: ldur            x8, [fp, #-0x50]
    // 0x8e7ad8: b               #0x8e769c
    // 0x8e7adc: mov             x4, x11
    // 0x8e7ae0: mov             x1, x10
    // 0x8e7ae4: mov             x2, x5
    // 0x8e7ae8: mov             x5, x9
    // 0x8e7aec: mov             x0, x6
    // 0x8e7af0: mov             x6, x3
    // 0x8e7af4: mov             x3, x7
    // 0x8e7af8: LoadField: r7 = r6->field_b
    //     0x8e7af8: ldur            w7, [x6, #0xb]
    // 0x8e7afc: r8 = LoadInt32Instr(r7)
    //     0x8e7afc: sbfx            x8, x7, #1, #0x1f
    // 0x8e7b00: stur            x8, [fp, #-0x50]
    // 0x8e7b04: mov             x10, x4
    // 0x8e7b08: mov             x9, x1
    // 0x8e7b0c: mov             x7, x5
    // 0x8e7b10: mov             x5, x3
    // 0x8e7b14: mov             x4, x0
    // 0x8e7b18: mov             x3, x2
    // 0x8e7b1c: r0 = 0
    //     0x8e7b1c: movz            x0, #0
    // 0x8e7b20: stur            x10, [fp, #-8]
    // 0x8e7b24: stur            x9, [fp, #-0x10]
    // 0x8e7b28: stur            x7, [fp, #-0x18]
    // 0x8e7b2c: stur            x5, [fp, #-0x30]
    // 0x8e7b30: stur            x4, [fp, #-0x38]
    // 0x8e7b34: stur            x3, [fp, #-0x40]
    // 0x8e7b38: stur            x0, [fp, #-0x48]
    // 0x8e7b3c: CheckStackOverflow
    //     0x8e7b3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e7b40: cmp             SP, x16
    //     0x8e7b44: b.ls            #0x8e8acc
    // 0x8e7b48: cmp             x0, #4
    // 0x8e7b4c: b.ge            #0x8e7f10
    // 0x8e7b50: mov             x1, x10
    // 0x8e7b54: r2 = 5
    //     0x8e7b54: movz            x2, #0x5
    // 0x8e7b58: r0 = shiftl32()
    //     0x8e7b58: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7b5c: ldur            x3, [fp, #-8]
    // 0x8e7b60: asr             x1, x3, #0x1b
    // 0x8e7b64: orr             x2, x0, x1
    // 0x8e7b68: ldur            x0, [fp, #-0x38]
    // 0x8e7b6c: add             x1, x0, x2
    // 0x8e7b70: ldur            x5, [fp, #-0x10]
    // 0x8e7b74: ldur            x4, [fp, #-0x18]
    // 0x8e7b78: eor             x0, x5, x4
    // 0x8e7b7c: ldur            x6, [fp, #-0x30]
    // 0x8e7b80: eor             x2, x0, x6
    // 0x8e7b84: add             x7, x1, x2
    // 0x8e7b88: ldur            x2, [fp, #-0x40]
    // 0x8e7b8c: add             x8, x2, #1
    // 0x8e7b90: ldur            x0, [fp, #-0x50]
    // 0x8e7b94: mov             x1, x2
    // 0x8e7b98: stur            x8, [fp, #-0x68]
    // 0x8e7b9c: cmp             x1, x0
    // 0x8e7ba0: b.hs            #0x8e8ad4
    // 0x8e7ba4: ldur            x0, [fp, #-0x20]
    // 0x8e7ba8: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e7ba8: add             x16, x0, x2, lsl #2
    //     0x8e7bac: ldur            w1, [x16, #0xf]
    // 0x8e7bb0: DecompressPointer r1
    //     0x8e7bb0: add             x1, x1, HEAP, lsl #32
    // 0x8e7bb4: r2 = LoadInt32Instr(r1)
    //     0x8e7bb4: sbfx            x2, x1, #1, #0x1f
    //     0x8e7bb8: tbz             w1, #0, #0x8e7bc0
    //     0x8e7bbc: ldur            x2, [x1, #7]
    // 0x8e7bc0: ubfx            x7, x7, #0, #0x20
    // 0x8e7bc4: add             w1, w7, w2
    // 0x8e7bc8: r7 = 1859775393
    //     0x8e7bc8: movz            x7, #0xeba1
    //     0x8e7bcc: movk            x7, #0x6ed9, lsl #16
    // 0x8e7bd0: add             w9, w1, w7
    // 0x8e7bd4: mov             x1, x5
    // 0x8e7bd8: stur            x9, [fp, #-0x60]
    // 0x8e7bdc: r2 = 30
    //     0x8e7bdc: movz            x2, #0x1e
    // 0x8e7be0: r0 = shiftl32()
    //     0x8e7be0: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7be4: ldur            x1, [fp, #-0x10]
    // 0x8e7be8: asr             x2, x1, #2
    // 0x8e7bec: orr             x3, x0, x2
    // 0x8e7bf0: stur            x3, [fp, #-0x70]
    // 0x8e7bf4: ldur            x0, [fp, #-0x60]
    // 0x8e7bf8: ubfx            x0, x0, #0, #0x20
    // 0x8e7bfc: mov             x1, x0
    // 0x8e7c00: r2 = 5
    //     0x8e7c00: movz            x2, #0x5
    // 0x8e7c04: r0 = shiftl32()
    //     0x8e7c04: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7c08: ldur            x1, [fp, #-0x60]
    // 0x8e7c0c: ubfx            x1, x1, #0, #0x20
    // 0x8e7c10: asr             x2, x1, #0x1b
    // 0x8e7c14: orr             x1, x0, x2
    // 0x8e7c18: ldur            x3, [fp, #-0x30]
    // 0x8e7c1c: add             x0, x3, x1
    // 0x8e7c20: ldur            x4, [fp, #-8]
    // 0x8e7c24: ldur            x3, [fp, #-0x70]
    // 0x8e7c28: eor             x1, x4, x3
    // 0x8e7c2c: ldur            x5, [fp, #-0x18]
    // 0x8e7c30: eor             x2, x1, x5
    // 0x8e7c34: add             x6, x0, x2
    // 0x8e7c38: ldur            x2, [fp, #-0x68]
    // 0x8e7c3c: add             x7, x2, #1
    // 0x8e7c40: ldur            x0, [fp, #-0x50]
    // 0x8e7c44: mov             x1, x2
    // 0x8e7c48: stur            x7, [fp, #-0x78]
    // 0x8e7c4c: cmp             x1, x0
    // 0x8e7c50: b.hs            #0x8e8ad8
    // 0x8e7c54: ldur            x0, [fp, #-0x20]
    // 0x8e7c58: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e7c58: add             x16, x0, x2, lsl #2
    //     0x8e7c5c: ldur            w1, [x16, #0xf]
    // 0x8e7c60: DecompressPointer r1
    //     0x8e7c60: add             x1, x1, HEAP, lsl #32
    // 0x8e7c64: r2 = LoadInt32Instr(r1)
    //     0x8e7c64: sbfx            x2, x1, #1, #0x1f
    //     0x8e7c68: tbz             w1, #0, #0x8e7c70
    //     0x8e7c6c: ldur            x2, [x1, #7]
    // 0x8e7c70: ubfx            x6, x6, #0, #0x20
    // 0x8e7c74: add             w1, w6, w2
    // 0x8e7c78: r6 = 1859775393
    //     0x8e7c78: movz            x6, #0xeba1
    //     0x8e7c7c: movk            x6, #0x6ed9, lsl #16
    // 0x8e7c80: add             w8, w1, w6
    // 0x8e7c84: mov             x1, x4
    // 0x8e7c88: stur            x8, [fp, #-0x68]
    // 0x8e7c8c: r2 = 30
    //     0x8e7c8c: movz            x2, #0x1e
    // 0x8e7c90: r0 = shiftl32()
    //     0x8e7c90: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7c94: ldur            x4, [fp, #-8]
    // 0x8e7c98: asr             x1, x4, #2
    // 0x8e7c9c: orr             x3, x0, x1
    // 0x8e7ca0: stur            x3, [fp, #-0x80]
    // 0x8e7ca4: ldur            x0, [fp, #-0x68]
    // 0x8e7ca8: ubfx            x0, x0, #0, #0x20
    // 0x8e7cac: mov             x1, x0
    // 0x8e7cb0: r2 = 5
    //     0x8e7cb0: movz            x2, #0x5
    // 0x8e7cb4: r0 = shiftl32()
    //     0x8e7cb4: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7cb8: ldur            x1, [fp, #-0x68]
    // 0x8e7cbc: ubfx            x1, x1, #0, #0x20
    // 0x8e7cc0: asr             x2, x1, #0x1b
    // 0x8e7cc4: orr             x1, x0, x2
    // 0x8e7cc8: ldur            x5, [fp, #-0x18]
    // 0x8e7ccc: add             x0, x5, x1
    // 0x8e7cd0: ldur            x1, [fp, #-0x60]
    // 0x8e7cd4: ubfx            x1, x1, #0, #0x20
    // 0x8e7cd8: ldur            x3, [fp, #-0x80]
    // 0x8e7cdc: eor             x2, x1, x3
    // 0x8e7ce0: ldur            x4, [fp, #-0x70]
    // 0x8e7ce4: eor             x1, x2, x4
    // 0x8e7ce8: add             x2, x0, x1
    // 0x8e7cec: ldur            x5, [fp, #-0x78]
    // 0x8e7cf0: add             x6, x5, #1
    // 0x8e7cf4: ldur            x0, [fp, #-0x50]
    // 0x8e7cf8: mov             x1, x5
    // 0x8e7cfc: stur            x6, [fp, #-0x88]
    // 0x8e7d00: cmp             x1, x0
    // 0x8e7d04: b.hs            #0x8e8adc
    // 0x8e7d08: ldur            x0, [fp, #-0x20]
    // 0x8e7d0c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x8e7d0c: add             x16, x0, x5, lsl #2
    //     0x8e7d10: ldur            w1, [x16, #0xf]
    // 0x8e7d14: DecompressPointer r1
    //     0x8e7d14: add             x1, x1, HEAP, lsl #32
    // 0x8e7d18: r5 = LoadInt32Instr(r1)
    //     0x8e7d18: sbfx            x5, x1, #1, #0x1f
    //     0x8e7d1c: tbz             w1, #0, #0x8e7d24
    //     0x8e7d20: ldur            x5, [x1, #7]
    // 0x8e7d24: ubfx            x2, x2, #0, #0x20
    // 0x8e7d28: add             w1, w2, w5
    // 0x8e7d2c: r5 = 1859775393
    //     0x8e7d2c: movz            x5, #0xeba1
    //     0x8e7d30: movk            x5, #0x6ed9, lsl #16
    // 0x8e7d34: add             w7, w1, w5
    // 0x8e7d38: stur            x7, [fp, #-0x78]
    // 0x8e7d3c: ldur            x1, [fp, #-0x60]
    // 0x8e7d40: ubfx            x1, x1, #0, #0x20
    // 0x8e7d44: r2 = 30
    //     0x8e7d44: movz            x2, #0x1e
    // 0x8e7d48: r0 = shiftl32()
    //     0x8e7d48: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7d4c: ldur            x1, [fp, #-0x60]
    // 0x8e7d50: ubfx            x1, x1, #0, #0x20
    // 0x8e7d54: asr             x2, x1, #2
    // 0x8e7d58: orr             x4, x0, x2
    // 0x8e7d5c: stur            x4, [fp, #-0x60]
    // 0x8e7d60: ldur            x0, [fp, #-0x78]
    // 0x8e7d64: ubfx            x0, x0, #0, #0x20
    // 0x8e7d68: mov             x1, x0
    // 0x8e7d6c: r2 = 5
    //     0x8e7d6c: movz            x2, #0x5
    // 0x8e7d70: r0 = shiftl32()
    //     0x8e7d70: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7d74: ldur            x1, [fp, #-0x78]
    // 0x8e7d78: ubfx            x1, x1, #0, #0x20
    // 0x8e7d7c: asr             x2, x1, #0x1b
    // 0x8e7d80: orr             x1, x0, x2
    // 0x8e7d84: ldur            x0, [fp, #-0x70]
    // 0x8e7d88: add             x2, x0, x1
    // 0x8e7d8c: ldur            x0, [fp, #-0x68]
    // 0x8e7d90: ubfx            x0, x0, #0, #0x20
    // 0x8e7d94: ldur            x4, [fp, #-0x60]
    // 0x8e7d98: eor             x1, x0, x4
    // 0x8e7d9c: ldur            x3, [fp, #-0x80]
    // 0x8e7da0: eor             x0, x1, x3
    // 0x8e7da4: add             x5, x2, x0
    // 0x8e7da8: ldur            x2, [fp, #-0x88]
    // 0x8e7dac: add             x6, x2, #1
    // 0x8e7db0: ldur            x0, [fp, #-0x50]
    // 0x8e7db4: mov             x1, x2
    // 0x8e7db8: stur            x6, [fp, #-0x90]
    // 0x8e7dbc: cmp             x1, x0
    // 0x8e7dc0: b.hs            #0x8e8ae0
    // 0x8e7dc4: ldur            x0, [fp, #-0x20]
    // 0x8e7dc8: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e7dc8: add             x16, x0, x2, lsl #2
    //     0x8e7dcc: ldur            w1, [x16, #0xf]
    // 0x8e7dd0: DecompressPointer r1
    //     0x8e7dd0: add             x1, x1, HEAP, lsl #32
    // 0x8e7dd4: r2 = LoadInt32Instr(r1)
    //     0x8e7dd4: sbfx            x2, x1, #1, #0x1f
    //     0x8e7dd8: tbz             w1, #0, #0x8e7de0
    //     0x8e7ddc: ldur            x2, [x1, #7]
    // 0x8e7de0: ubfx            x5, x5, #0, #0x20
    // 0x8e7de4: add             w1, w5, w2
    // 0x8e7de8: r5 = 1859775393
    //     0x8e7de8: movz            x5, #0xeba1
    //     0x8e7dec: movk            x5, #0x6ed9, lsl #16
    // 0x8e7df0: add             w7, w1, w5
    // 0x8e7df4: stur            x7, [fp, #-0x70]
    // 0x8e7df8: ldur            x1, [fp, #-0x68]
    // 0x8e7dfc: ubfx            x1, x1, #0, #0x20
    // 0x8e7e00: r2 = 30
    //     0x8e7e00: movz            x2, #0x1e
    // 0x8e7e04: r0 = shiftl32()
    //     0x8e7e04: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7e08: ldur            x1, [fp, #-0x68]
    // 0x8e7e0c: ubfx            x1, x1, #0, #0x20
    // 0x8e7e10: asr             x2, x1, #2
    // 0x8e7e14: orr             x5, x0, x2
    // 0x8e7e18: stur            x5, [fp, #-0x68]
    // 0x8e7e1c: ldur            x0, [fp, #-0x70]
    // 0x8e7e20: ubfx            x0, x0, #0, #0x20
    // 0x8e7e24: mov             x1, x0
    // 0x8e7e28: r2 = 5
    //     0x8e7e28: movz            x2, #0x5
    // 0x8e7e2c: r0 = shiftl32()
    //     0x8e7e2c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7e30: ldur            x1, [fp, #-0x70]
    // 0x8e7e34: ubfx            x1, x1, #0, #0x20
    // 0x8e7e38: asr             x2, x1, #0x1b
    // 0x8e7e3c: orr             x1, x0, x2
    // 0x8e7e40: ldur            x0, [fp, #-0x80]
    // 0x8e7e44: add             x2, x0, x1
    // 0x8e7e48: ldur            x0, [fp, #-0x78]
    // 0x8e7e4c: ubfx            x0, x0, #0, #0x20
    // 0x8e7e50: ldur            x5, [fp, #-0x68]
    // 0x8e7e54: eor             x1, x0, x5
    // 0x8e7e58: ldur            x4, [fp, #-0x60]
    // 0x8e7e5c: eor             x0, x1, x4
    // 0x8e7e60: add             x3, x2, x0
    // 0x8e7e64: ldur            x2, [fp, #-0x90]
    // 0x8e7e68: add             x6, x2, #1
    // 0x8e7e6c: ldur            x0, [fp, #-0x50]
    // 0x8e7e70: mov             x1, x2
    // 0x8e7e74: stur            x6, [fp, #-0x88]
    // 0x8e7e78: cmp             x1, x0
    // 0x8e7e7c: b.hs            #0x8e8ae4
    // 0x8e7e80: ldur            x0, [fp, #-0x20]
    // 0x8e7e84: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e7e84: add             x16, x0, x2, lsl #2
    //     0x8e7e88: ldur            w1, [x16, #0xf]
    // 0x8e7e8c: DecompressPointer r1
    //     0x8e7e8c: add             x1, x1, HEAP, lsl #32
    // 0x8e7e90: r2 = LoadInt32Instr(r1)
    //     0x8e7e90: sbfx            x2, x1, #1, #0x1f
    //     0x8e7e94: tbz             w1, #0, #0x8e7e9c
    //     0x8e7e98: ldur            x2, [x1, #7]
    // 0x8e7e9c: ubfx            x3, x3, #0, #0x20
    // 0x8e7ea0: add             w1, w3, w2
    // 0x8e7ea4: r3 = 1859775393
    //     0x8e7ea4: movz            x3, #0xeba1
    //     0x8e7ea8: movk            x3, #0x6ed9, lsl #16
    // 0x8e7eac: add             w7, w1, w3
    // 0x8e7eb0: stur            x7, [fp, #-0x80]
    // 0x8e7eb4: ldur            x1, [fp, #-0x78]
    // 0x8e7eb8: ubfx            x1, x1, #0, #0x20
    // 0x8e7ebc: r2 = 30
    //     0x8e7ebc: movz            x2, #0x1e
    // 0x8e7ec0: r0 = shiftl32()
    //     0x8e7ec0: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7ec4: ldur            x1, [fp, #-0x78]
    // 0x8e7ec8: ubfx            x1, x1, #0, #0x20
    // 0x8e7ecc: asr             x2, x1, #2
    // 0x8e7ed0: orr             x7, x0, x2
    // 0x8e7ed4: ldur            x0, [fp, #-0x48]
    // 0x8e7ed8: add             x1, x0, #1
    // 0x8e7edc: ldur            x2, [fp, #-0x70]
    // 0x8e7ee0: ubfx            x2, x2, #0, #0x20
    // 0x8e7ee4: ldur            x6, [fp, #-0x80]
    // 0x8e7ee8: ubfx            x6, x6, #0, #0x20
    // 0x8e7eec: mov             x10, x6
    // 0x8e7ef0: mov             x9, x2
    // 0x8e7ef4: ldur            x5, [fp, #-0x68]
    // 0x8e7ef8: ldur            x4, [fp, #-0x60]
    // 0x8e7efc: ldur            x3, [fp, #-0x88]
    // 0x8e7f00: mov             x0, x1
    // 0x8e7f04: ldur            x6, [fp, #-0x20]
    // 0x8e7f08: ldur            x8, [fp, #-0x50]
    // 0x8e7f0c: b               #0x8e7b20
    // 0x8e7f10: mov             x0, x4
    // 0x8e7f14: mov             x4, x10
    // 0x8e7f18: mov             x1, x9
    // 0x8e7f1c: mov             x2, x3
    // 0x8e7f20: mov             x3, x5
    // 0x8e7f24: mov             x5, x7
    // 0x8e7f28: LoadField: r7 = r6->field_b
    //     0x8e7f28: ldur            w7, [x6, #0xb]
    // 0x8e7f2c: r8 = LoadInt32Instr(r7)
    //     0x8e7f2c: sbfx            x8, x7, #1, #0x1f
    // 0x8e7f30: stur            x8, [fp, #-0x50]
    // 0x8e7f34: mov             x10, x4
    // 0x8e7f38: mov             x9, x1
    // 0x8e7f3c: mov             x7, x5
    // 0x8e7f40: mov             x5, x3
    // 0x8e7f44: mov             x4, x0
    // 0x8e7f48: mov             x3, x2
    // 0x8e7f4c: r0 = 0
    //     0x8e7f4c: movz            x0, #0
    // 0x8e7f50: stur            x10, [fp, #-8]
    // 0x8e7f54: stur            x9, [fp, #-0x10]
    // 0x8e7f58: stur            x7, [fp, #-0x18]
    // 0x8e7f5c: stur            x5, [fp, #-0x30]
    // 0x8e7f60: stur            x4, [fp, #-0x38]
    // 0x8e7f64: stur            x3, [fp, #-0x40]
    // 0x8e7f68: stur            x0, [fp, #-0x48]
    // 0x8e7f6c: CheckStackOverflow
    //     0x8e7f6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e7f70: cmp             SP, x16
    //     0x8e7f74: b.ls            #0x8e8ae8
    // 0x8e7f78: cmp             x0, #4
    // 0x8e7f7c: b.ge            #0x8e83b8
    // 0x8e7f80: mov             x1, x10
    // 0x8e7f84: r2 = 5
    //     0x8e7f84: movz            x2, #0x5
    // 0x8e7f88: r0 = shiftl32()
    //     0x8e7f88: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e7f8c: ldur            x3, [fp, #-8]
    // 0x8e7f90: asr             x1, x3, #0x1b
    // 0x8e7f94: orr             x2, x0, x1
    // 0x8e7f98: ldur            x0, [fp, #-0x38]
    // 0x8e7f9c: add             x1, x0, x2
    // 0x8e7fa0: ldur            x5, [fp, #-0x10]
    // 0x8e7fa4: ldur            x4, [fp, #-0x18]
    // 0x8e7fa8: and             x0, x5, x4
    // 0x8e7fac: ldur            x6, [fp, #-0x30]
    // 0x8e7fb0: and             x2, x5, x6
    // 0x8e7fb4: orr             x7, x0, x2
    // 0x8e7fb8: and             x0, x4, x6
    // 0x8e7fbc: orr             x2, x7, x0
    // 0x8e7fc0: add             x7, x1, x2
    // 0x8e7fc4: ldur            x2, [fp, #-0x40]
    // 0x8e7fc8: add             x8, x2, #1
    // 0x8e7fcc: ldur            x0, [fp, #-0x50]
    // 0x8e7fd0: mov             x1, x2
    // 0x8e7fd4: stur            x8, [fp, #-0x68]
    // 0x8e7fd8: cmp             x1, x0
    // 0x8e7fdc: b.hs            #0x8e8af0
    // 0x8e7fe0: ldur            x0, [fp, #-0x20]
    // 0x8e7fe4: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e7fe4: add             x16, x0, x2, lsl #2
    //     0x8e7fe8: ldur            w1, [x16, #0xf]
    // 0x8e7fec: DecompressPointer r1
    //     0x8e7fec: add             x1, x1, HEAP, lsl #32
    // 0x8e7ff0: r2 = LoadInt32Instr(r1)
    //     0x8e7ff0: sbfx            x2, x1, #1, #0x1f
    //     0x8e7ff4: tbz             w1, #0, #0x8e7ffc
    //     0x8e7ff8: ldur            x2, [x1, #7]
    // 0x8e7ffc: ubfx            x7, x7, #0, #0x20
    // 0x8e8000: add             w1, w7, w2
    // 0x8e8004: r7 = 2400959708
    //     0x8e8004: movz            x7, #0xbcdc
    //     0x8e8008: movk            x7, #0x8f1b, lsl #16
    // 0x8e800c: add             w9, w1, w7
    // 0x8e8010: mov             x1, x5
    // 0x8e8014: stur            x9, [fp, #-0x60]
    // 0x8e8018: r2 = 30
    //     0x8e8018: movz            x2, #0x1e
    // 0x8e801c: r0 = shiftl32()
    //     0x8e801c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e8020: ldur            x1, [fp, #-0x10]
    // 0x8e8024: asr             x2, x1, #2
    // 0x8e8028: orr             x3, x0, x2
    // 0x8e802c: stur            x3, [fp, #-0x70]
    // 0x8e8030: ldur            x0, [fp, #-0x60]
    // 0x8e8034: ubfx            x0, x0, #0, #0x20
    // 0x8e8038: mov             x1, x0
    // 0x8e803c: r2 = 5
    //     0x8e803c: movz            x2, #0x5
    // 0x8e8040: r0 = shiftl32()
    //     0x8e8040: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e8044: ldur            x1, [fp, #-0x60]
    // 0x8e8048: ubfx            x1, x1, #0, #0x20
    // 0x8e804c: asr             x2, x1, #0x1b
    // 0x8e8050: orr             x1, x0, x2
    // 0x8e8054: ldur            x3, [fp, #-0x30]
    // 0x8e8058: add             x0, x3, x1
    // 0x8e805c: ldur            x4, [fp, #-8]
    // 0x8e8060: ldur            x3, [fp, #-0x70]
    // 0x8e8064: and             x1, x4, x3
    // 0x8e8068: ldur            x5, [fp, #-0x18]
    // 0x8e806c: and             x2, x4, x5
    // 0x8e8070: orr             x6, x1, x2
    // 0x8e8074: and             x1, x3, x5
    // 0x8e8078: orr             x2, x6, x1
    // 0x8e807c: add             x6, x0, x2
    // 0x8e8080: ldur            x2, [fp, #-0x68]
    // 0x8e8084: add             x7, x2, #1
    // 0x8e8088: ldur            x0, [fp, #-0x50]
    // 0x8e808c: mov             x1, x2
    // 0x8e8090: stur            x7, [fp, #-0x78]
    // 0x8e8094: cmp             x1, x0
    // 0x8e8098: b.hs            #0x8e8af4
    // 0x8e809c: ldur            x0, [fp, #-0x20]
    // 0x8e80a0: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e80a0: add             x16, x0, x2, lsl #2
    //     0x8e80a4: ldur            w1, [x16, #0xf]
    // 0x8e80a8: DecompressPointer r1
    //     0x8e80a8: add             x1, x1, HEAP, lsl #32
    // 0x8e80ac: r2 = LoadInt32Instr(r1)
    //     0x8e80ac: sbfx            x2, x1, #1, #0x1f
    //     0x8e80b0: tbz             w1, #0, #0x8e80b8
    //     0x8e80b4: ldur            x2, [x1, #7]
    // 0x8e80b8: ubfx            x6, x6, #0, #0x20
    // 0x8e80bc: add             w1, w6, w2
    // 0x8e80c0: r6 = 2400959708
    //     0x8e80c0: movz            x6, #0xbcdc
    //     0x8e80c4: movk            x6, #0x8f1b, lsl #16
    // 0x8e80c8: add             w8, w1, w6
    // 0x8e80cc: mov             x1, x4
    // 0x8e80d0: stur            x8, [fp, #-0x68]
    // 0x8e80d4: r2 = 30
    //     0x8e80d4: movz            x2, #0x1e
    // 0x8e80d8: r0 = shiftl32()
    //     0x8e80d8: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e80dc: ldur            x4, [fp, #-8]
    // 0x8e80e0: asr             x1, x4, #2
    // 0x8e80e4: orr             x3, x0, x1
    // 0x8e80e8: stur            x3, [fp, #-0x80]
    // 0x8e80ec: ldur            x0, [fp, #-0x68]
    // 0x8e80f0: ubfx            x0, x0, #0, #0x20
    // 0x8e80f4: mov             x1, x0
    // 0x8e80f8: r2 = 5
    //     0x8e80f8: movz            x2, #0x5
    // 0x8e80fc: r0 = shiftl32()
    //     0x8e80fc: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e8100: ldur            x1, [fp, #-0x68]
    // 0x8e8104: ubfx            x1, x1, #0, #0x20
    // 0x8e8108: asr             x2, x1, #0x1b
    // 0x8e810c: orr             x1, x0, x2
    // 0x8e8110: ldur            x5, [fp, #-0x18]
    // 0x8e8114: add             x0, x5, x1
    // 0x8e8118: ldur            x1, [fp, #-0x80]
    // 0x8e811c: ubfx            x1, x1, #0, #0x20
    // 0x8e8120: ldur            x3, [fp, #-0x60]
    // 0x8e8124: and             x2, x3, x1
    // 0x8e8128: ldur            x1, [fp, #-0x70]
    // 0x8e812c: ubfx            x1, x1, #0, #0x20
    // 0x8e8130: and             x4, x3, x1
    // 0x8e8134: ubfx            x2, x2, #0, #0x20
    // 0x8e8138: ubfx            x4, x4, #0, #0x20
    // 0x8e813c: orr             x1, x2, x4
    // 0x8e8140: ldur            x5, [fp, #-0x70]
    // 0x8e8144: ldur            x4, [fp, #-0x80]
    // 0x8e8148: and             x2, x4, x5
    // 0x8e814c: orr             x6, x1, x2
    // 0x8e8150: add             x2, x0, x6
    // 0x8e8154: ldur            x6, [fp, #-0x78]
    // 0x8e8158: add             x7, x6, #1
    // 0x8e815c: ldur            x0, [fp, #-0x50]
    // 0x8e8160: mov             x1, x6
    // 0x8e8164: stur            x7, [fp, #-0x88]
    // 0x8e8168: cmp             x1, x0
    // 0x8e816c: b.hs            #0x8e8af8
    // 0x8e8170: ldur            x0, [fp, #-0x20]
    // 0x8e8174: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x8e8174: add             x16, x0, x6, lsl #2
    //     0x8e8178: ldur            w1, [x16, #0xf]
    // 0x8e817c: DecompressPointer r1
    //     0x8e817c: add             x1, x1, HEAP, lsl #32
    // 0x8e8180: r6 = LoadInt32Instr(r1)
    //     0x8e8180: sbfx            x6, x1, #1, #0x1f
    //     0x8e8184: tbz             w1, #0, #0x8e818c
    //     0x8e8188: ldur            x6, [x1, #7]
    // 0x8e818c: ubfx            x2, x2, #0, #0x20
    // 0x8e8190: add             w1, w2, w6
    // 0x8e8194: r6 = 2400959708
    //     0x8e8194: movz            x6, #0xbcdc
    //     0x8e8198: movk            x6, #0x8f1b, lsl #16
    // 0x8e819c: add             w8, w1, w6
    // 0x8e81a0: stur            x8, [fp, #-0x78]
    // 0x8e81a4: mov             x1, x3
    // 0x8e81a8: ubfx            x1, x1, #0, #0x20
    // 0x8e81ac: r2 = 30
    //     0x8e81ac: movz            x2, #0x1e
    // 0x8e81b0: r0 = shiftl32()
    //     0x8e81b0: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e81b4: ldur            x1, [fp, #-0x60]
    // 0x8e81b8: ubfx            x1, x1, #0, #0x20
    // 0x8e81bc: asr             x2, x1, #2
    // 0x8e81c0: orr             x4, x0, x2
    // 0x8e81c4: stur            x4, [fp, #-0x60]
    // 0x8e81c8: ldur            x0, [fp, #-0x78]
    // 0x8e81cc: ubfx            x0, x0, #0, #0x20
    // 0x8e81d0: mov             x1, x0
    // 0x8e81d4: r2 = 5
    //     0x8e81d4: movz            x2, #0x5
    // 0x8e81d8: r0 = shiftl32()
    //     0x8e81d8: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e81dc: ldur            x1, [fp, #-0x78]
    // 0x8e81e0: ubfx            x1, x1, #0, #0x20
    // 0x8e81e4: asr             x2, x1, #0x1b
    // 0x8e81e8: orr             x1, x0, x2
    // 0x8e81ec: ldur            x0, [fp, #-0x70]
    // 0x8e81f0: add             x2, x0, x1
    // 0x8e81f4: ldur            x0, [fp, #-0x60]
    // 0x8e81f8: ubfx            x0, x0, #0, #0x20
    // 0x8e81fc: ldur            x3, [fp, #-0x68]
    // 0x8e8200: and             x1, x3, x0
    // 0x8e8204: ldur            x0, [fp, #-0x80]
    // 0x8e8208: ubfx            x0, x0, #0, #0x20
    // 0x8e820c: and             x4, x3, x0
    // 0x8e8210: ubfx            x1, x1, #0, #0x20
    // 0x8e8214: ubfx            x4, x4, #0, #0x20
    // 0x8e8218: orr             x0, x1, x4
    // 0x8e821c: ldur            x5, [fp, #-0x80]
    // 0x8e8220: ldur            x4, [fp, #-0x60]
    // 0x8e8224: and             x1, x4, x5
    // 0x8e8228: orr             x6, x0, x1
    // 0x8e822c: add             x7, x2, x6
    // 0x8e8230: ldur            x2, [fp, #-0x88]
    // 0x8e8234: add             x6, x2, #1
    // 0x8e8238: ldur            x0, [fp, #-0x50]
    // 0x8e823c: mov             x1, x2
    // 0x8e8240: stur            x6, [fp, #-0x90]
    // 0x8e8244: cmp             x1, x0
    // 0x8e8248: b.hs            #0x8e8afc
    // 0x8e824c: ldur            x0, [fp, #-0x20]
    // 0x8e8250: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e8250: add             x16, x0, x2, lsl #2
    //     0x8e8254: ldur            w1, [x16, #0xf]
    // 0x8e8258: DecompressPointer r1
    //     0x8e8258: add             x1, x1, HEAP, lsl #32
    // 0x8e825c: r2 = LoadInt32Instr(r1)
    //     0x8e825c: sbfx            x2, x1, #1, #0x1f
    //     0x8e8260: tbz             w1, #0, #0x8e8268
    //     0x8e8264: ldur            x2, [x1, #7]
    // 0x8e8268: ubfx            x7, x7, #0, #0x20
    // 0x8e826c: add             w1, w7, w2
    // 0x8e8270: r7 = 2400959708
    //     0x8e8270: movz            x7, #0xbcdc
    //     0x8e8274: movk            x7, #0x8f1b, lsl #16
    // 0x8e8278: add             w8, w1, w7
    // 0x8e827c: stur            x8, [fp, #-0x70]
    // 0x8e8280: mov             x1, x3
    // 0x8e8284: ubfx            x1, x1, #0, #0x20
    // 0x8e8288: r2 = 30
    //     0x8e8288: movz            x2, #0x1e
    // 0x8e828c: r0 = shiftl32()
    //     0x8e828c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e8290: ldur            x1, [fp, #-0x68]
    // 0x8e8294: ubfx            x1, x1, #0, #0x20
    // 0x8e8298: asr             x2, x1, #2
    // 0x8e829c: orr             x5, x0, x2
    // 0x8e82a0: stur            x5, [fp, #-0x68]
    // 0x8e82a4: ldur            x0, [fp, #-0x70]
    // 0x8e82a8: ubfx            x0, x0, #0, #0x20
    // 0x8e82ac: mov             x1, x0
    // 0x8e82b0: r2 = 5
    //     0x8e82b0: movz            x2, #0x5
    // 0x8e82b4: r0 = shiftl32()
    //     0x8e82b4: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e82b8: ldur            x1, [fp, #-0x70]
    // 0x8e82bc: ubfx            x1, x1, #0, #0x20
    // 0x8e82c0: asr             x2, x1, #0x1b
    // 0x8e82c4: orr             x1, x0, x2
    // 0x8e82c8: ldur            x0, [fp, #-0x80]
    // 0x8e82cc: add             x2, x0, x1
    // 0x8e82d0: ldur            x0, [fp, #-0x68]
    // 0x8e82d4: ubfx            x0, x0, #0, #0x20
    // 0x8e82d8: ldur            x3, [fp, #-0x78]
    // 0x8e82dc: and             x1, x3, x0
    // 0x8e82e0: ldur            x0, [fp, #-0x60]
    // 0x8e82e4: ubfx            x0, x0, #0, #0x20
    // 0x8e82e8: and             x4, x3, x0
    // 0x8e82ec: ubfx            x1, x1, #0, #0x20
    // 0x8e82f0: ubfx            x4, x4, #0, #0x20
    // 0x8e82f4: orr             x0, x1, x4
    // 0x8e82f8: ldur            x4, [fp, #-0x60]
    // 0x8e82fc: ldur            x5, [fp, #-0x68]
    // 0x8e8300: and             x1, x5, x4
    // 0x8e8304: orr             x6, x0, x1
    // 0x8e8308: add             x7, x2, x6
    // 0x8e830c: ldur            x2, [fp, #-0x90]
    // 0x8e8310: add             x6, x2, #1
    // 0x8e8314: ldur            x0, [fp, #-0x50]
    // 0x8e8318: mov             x1, x2
    // 0x8e831c: stur            x6, [fp, #-0x88]
    // 0x8e8320: cmp             x1, x0
    // 0x8e8324: b.hs            #0x8e8b00
    // 0x8e8328: ldur            x0, [fp, #-0x20]
    // 0x8e832c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e832c: add             x16, x0, x2, lsl #2
    //     0x8e8330: ldur            w1, [x16, #0xf]
    // 0x8e8334: DecompressPointer r1
    //     0x8e8334: add             x1, x1, HEAP, lsl #32
    // 0x8e8338: r2 = LoadInt32Instr(r1)
    //     0x8e8338: sbfx            x2, x1, #1, #0x1f
    //     0x8e833c: tbz             w1, #0, #0x8e8344
    //     0x8e8340: ldur            x2, [x1, #7]
    // 0x8e8344: ubfx            x7, x7, #0, #0x20
    // 0x8e8348: add             w1, w7, w2
    // 0x8e834c: r7 = 2400959708
    //     0x8e834c: movz            x7, #0xbcdc
    //     0x8e8350: movk            x7, #0x8f1b, lsl #16
    // 0x8e8354: add             w8, w1, w7
    // 0x8e8358: stur            x8, [fp, #-0x80]
    // 0x8e835c: mov             x1, x3
    // 0x8e8360: ubfx            x1, x1, #0, #0x20
    // 0x8e8364: r2 = 30
    //     0x8e8364: movz            x2, #0x1e
    // 0x8e8368: r0 = shiftl32()
    //     0x8e8368: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e836c: ldur            x1, [fp, #-0x78]
    // 0x8e8370: ubfx            x1, x1, #0, #0x20
    // 0x8e8374: asr             x2, x1, #2
    // 0x8e8378: orr             x7, x0, x2
    // 0x8e837c: ldur            x0, [fp, #-0x48]
    // 0x8e8380: add             x1, x0, #1
    // 0x8e8384: ldur            x2, [fp, #-0x70]
    // 0x8e8388: ubfx            x2, x2, #0, #0x20
    // 0x8e838c: ldur            x6, [fp, #-0x80]
    // 0x8e8390: ubfx            x6, x6, #0, #0x20
    // 0x8e8394: mov             x10, x6
    // 0x8e8398: mov             x9, x2
    // 0x8e839c: ldur            x5, [fp, #-0x68]
    // 0x8e83a0: ldur            x4, [fp, #-0x60]
    // 0x8e83a4: ldur            x3, [fp, #-0x88]
    // 0x8e83a8: mov             x0, x1
    // 0x8e83ac: ldur            x6, [fp, #-0x20]
    // 0x8e83b0: ldur            x8, [fp, #-0x50]
    // 0x8e83b4: b               #0x8e7f50
    // 0x8e83b8: mov             x0, x4
    // 0x8e83bc: mov             x4, x10
    // 0x8e83c0: mov             x1, x9
    // 0x8e83c4: mov             x2, x3
    // 0x8e83c8: mov             x3, x5
    // 0x8e83cc: mov             x5, x7
    // 0x8e83d0: LoadField: r7 = r6->field_b
    //     0x8e83d0: ldur            w7, [x6, #0xb]
    // 0x8e83d4: r8 = LoadInt32Instr(r7)
    //     0x8e83d4: sbfx            x8, x7, #1, #0x1f
    // 0x8e83d8: stur            x8, [fp, #-0x50]
    // 0x8e83dc: mov             x10, x4
    // 0x8e83e0: mov             x9, x1
    // 0x8e83e4: mov             x7, x5
    // 0x8e83e8: mov             x5, x3
    // 0x8e83ec: mov             x4, x0
    // 0x8e83f0: mov             x3, x2
    // 0x8e83f4: r0 = 0
    //     0x8e83f4: movz            x0, #0
    // 0x8e83f8: stur            x10, [fp, #-8]
    // 0x8e83fc: stur            x9, [fp, #-0x10]
    // 0x8e8400: stur            x7, [fp, #-0x18]
    // 0x8e8404: stur            x5, [fp, #-0x30]
    // 0x8e8408: stur            x4, [fp, #-0x38]
    // 0x8e840c: stur            x3, [fp, #-0x40]
    // 0x8e8410: stur            x0, [fp, #-0x48]
    // 0x8e8414: CheckStackOverflow
    //     0x8e8414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e8418: cmp             SP, x16
    //     0x8e841c: b.ls            #0x8e8b04
    // 0x8e8420: cmp             x0, #4
    // 0x8e8424: b.ge            #0x8e87e8
    // 0x8e8428: mov             x1, x10
    // 0x8e842c: r2 = 5
    //     0x8e842c: movz            x2, #0x5
    // 0x8e8430: r0 = shiftl32()
    //     0x8e8430: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e8434: ldur            x3, [fp, #-8]
    // 0x8e8438: asr             x1, x3, #0x1b
    // 0x8e843c: orr             x2, x0, x1
    // 0x8e8440: ldur            x4, [fp, #-0x38]
    // 0x8e8444: add             x0, x4, x2
    // 0x8e8448: ldur            x5, [fp, #-0x10]
    // 0x8e844c: ldur            x4, [fp, #-0x18]
    // 0x8e8450: eor             x1, x5, x4
    // 0x8e8454: ldur            x6, [fp, #-0x30]
    // 0x8e8458: eor             x2, x1, x6
    // 0x8e845c: add             x7, x0, x2
    // 0x8e8460: ldur            x2, [fp, #-0x40]
    // 0x8e8464: add             x8, x2, #1
    // 0x8e8468: ldur            x0, [fp, #-0x50]
    // 0x8e846c: mov             x1, x2
    // 0x8e8470: stur            x8, [fp, #-0x60]
    // 0x8e8474: cmp             x1, x0
    // 0x8e8478: b.hs            #0x8e8b0c
    // 0x8e847c: ldur            x0, [fp, #-0x20]
    // 0x8e8480: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e8480: add             x16, x0, x2, lsl #2
    //     0x8e8484: ldur            w1, [x16, #0xf]
    // 0x8e8488: DecompressPointer r1
    //     0x8e8488: add             x1, x1, HEAP, lsl #32
    // 0x8e848c: r2 = LoadInt32Instr(r1)
    //     0x8e848c: sbfx            x2, x1, #1, #0x1f
    //     0x8e8490: tbz             w1, #0, #0x8e8498
    //     0x8e8494: ldur            x2, [x1, #7]
    // 0x8e8498: ubfx            x7, x7, #0, #0x20
    // 0x8e849c: add             w1, w7, w2
    // 0x8e84a0: r7 = 3395469782
    //     0x8e84a0: movz            x7, #0xc1d6
    //     0x8e84a4: movk            x7, #0xca62, lsl #16
    // 0x8e84a8: add             w9, w1, w7
    // 0x8e84ac: mov             x1, x5
    // 0x8e84b0: stur            x9, [fp, #-0x40]
    // 0x8e84b4: r2 = 30
    //     0x8e84b4: movz            x2, #0x1e
    // 0x8e84b8: r0 = shiftl32()
    //     0x8e84b8: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e84bc: ldur            x2, [fp, #-0x10]
    // 0x8e84c0: asr             x1, x2, #2
    // 0x8e84c4: orr             x3, x0, x1
    // 0x8e84c8: stur            x3, [fp, #-0x68]
    // 0x8e84cc: ldur            x0, [fp, #-0x40]
    // 0x8e84d0: ubfx            x0, x0, #0, #0x20
    // 0x8e84d4: mov             x1, x0
    // 0x8e84d8: r2 = 5
    //     0x8e84d8: movz            x2, #0x5
    // 0x8e84dc: r0 = shiftl32()
    //     0x8e84dc: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e84e0: ldur            x1, [fp, #-0x40]
    // 0x8e84e4: ubfx            x1, x1, #0, #0x20
    // 0x8e84e8: asr             x2, x1, #0x1b
    // 0x8e84ec: orr             x1, x0, x2
    // 0x8e84f0: ldur            x3, [fp, #-0x30]
    // 0x8e84f4: add             x0, x3, x1
    // 0x8e84f8: ldur            x4, [fp, #-8]
    // 0x8e84fc: ldur            x3, [fp, #-0x68]
    // 0x8e8500: eor             x1, x4, x3
    // 0x8e8504: ldur            x5, [fp, #-0x18]
    // 0x8e8508: eor             x2, x1, x5
    // 0x8e850c: add             x6, x0, x2
    // 0x8e8510: ldur            x2, [fp, #-0x60]
    // 0x8e8514: add             x7, x2, #1
    // 0x8e8518: ldur            x0, [fp, #-0x50]
    // 0x8e851c: mov             x1, x2
    // 0x8e8520: stur            x7, [fp, #-0x70]
    // 0x8e8524: cmp             x1, x0
    // 0x8e8528: b.hs            #0x8e8b10
    // 0x8e852c: ldur            x0, [fp, #-0x20]
    // 0x8e8530: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e8530: add             x16, x0, x2, lsl #2
    //     0x8e8534: ldur            w1, [x16, #0xf]
    // 0x8e8538: DecompressPointer r1
    //     0x8e8538: add             x1, x1, HEAP, lsl #32
    // 0x8e853c: r2 = LoadInt32Instr(r1)
    //     0x8e853c: sbfx            x2, x1, #1, #0x1f
    //     0x8e8540: tbz             w1, #0, #0x8e8548
    //     0x8e8544: ldur            x2, [x1, #7]
    // 0x8e8548: ubfx            x6, x6, #0, #0x20
    // 0x8e854c: add             w1, w6, w2
    // 0x8e8550: r6 = 3395469782
    //     0x8e8550: movz            x6, #0xc1d6
    //     0x8e8554: movk            x6, #0xca62, lsl #16
    // 0x8e8558: add             w8, w1, w6
    // 0x8e855c: mov             x1, x4
    // 0x8e8560: stur            x8, [fp, #-0x60]
    // 0x8e8564: r2 = 30
    //     0x8e8564: movz            x2, #0x1e
    // 0x8e8568: r0 = shiftl32()
    //     0x8e8568: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e856c: mov             x1, x0
    // 0x8e8570: ldur            x0, [fp, #-8]
    // 0x8e8574: asr             x2, x0, #2
    // 0x8e8578: orr             x0, x1, x2
    // 0x8e857c: stur            x0, [fp, #-0x78]
    // 0x8e8580: ldur            x1, [fp, #-0x60]
    // 0x8e8584: ubfx            x1, x1, #0, #0x20
    // 0x8e8588: r2 = 5
    //     0x8e8588: movz            x2, #0x5
    // 0x8e858c: r0 = shiftl32()
    //     0x8e858c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e8590: ldur            x1, [fp, #-0x60]
    // 0x8e8594: ubfx            x1, x1, #0, #0x20
    // 0x8e8598: asr             x2, x1, #0x1b
    // 0x8e859c: orr             x1, x0, x2
    // 0x8e85a0: ldur            x5, [fp, #-0x18]
    // 0x8e85a4: add             x0, x5, x1
    // 0x8e85a8: ldur            x1, [fp, #-0x40]
    // 0x8e85ac: ubfx            x1, x1, #0, #0x20
    // 0x8e85b0: ldur            x3, [fp, #-0x78]
    // 0x8e85b4: eor             x2, x1, x3
    // 0x8e85b8: ldur            x4, [fp, #-0x68]
    // 0x8e85bc: eor             x1, x2, x4
    // 0x8e85c0: add             x2, x0, x1
    // 0x8e85c4: ldur            x5, [fp, #-0x70]
    // 0x8e85c8: add             x6, x5, #1
    // 0x8e85cc: ldur            x0, [fp, #-0x50]
    // 0x8e85d0: mov             x1, x5
    // 0x8e85d4: stur            x6, [fp, #-0x80]
    // 0x8e85d8: cmp             x1, x0
    // 0x8e85dc: b.hs            #0x8e8b14
    // 0x8e85e0: ldur            x0, [fp, #-0x20]
    // 0x8e85e4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x8e85e4: add             x16, x0, x5, lsl #2
    //     0x8e85e8: ldur            w1, [x16, #0xf]
    // 0x8e85ec: DecompressPointer r1
    //     0x8e85ec: add             x1, x1, HEAP, lsl #32
    // 0x8e85f0: r5 = LoadInt32Instr(r1)
    //     0x8e85f0: sbfx            x5, x1, #1, #0x1f
    //     0x8e85f4: tbz             w1, #0, #0x8e85fc
    //     0x8e85f8: ldur            x5, [x1, #7]
    // 0x8e85fc: ubfx            x2, x2, #0, #0x20
    // 0x8e8600: add             w1, w2, w5
    // 0x8e8604: r5 = 3395469782
    //     0x8e8604: movz            x5, #0xc1d6
    //     0x8e8608: movk            x5, #0xca62, lsl #16
    // 0x8e860c: add             w7, w1, w5
    // 0x8e8610: stur            x7, [fp, #-0x70]
    // 0x8e8614: ldur            x1, [fp, #-0x40]
    // 0x8e8618: ubfx            x1, x1, #0, #0x20
    // 0x8e861c: r2 = 30
    //     0x8e861c: movz            x2, #0x1e
    // 0x8e8620: r0 = shiftl32()
    //     0x8e8620: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e8624: ldur            x1, [fp, #-0x40]
    // 0x8e8628: ubfx            x1, x1, #0, #0x20
    // 0x8e862c: asr             x2, x1, #2
    // 0x8e8630: orr             x4, x0, x2
    // 0x8e8634: stur            x4, [fp, #-0x40]
    // 0x8e8638: ldur            x0, [fp, #-0x70]
    // 0x8e863c: ubfx            x0, x0, #0, #0x20
    // 0x8e8640: mov             x1, x0
    // 0x8e8644: r2 = 5
    //     0x8e8644: movz            x2, #0x5
    // 0x8e8648: r0 = shiftl32()
    //     0x8e8648: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e864c: ldur            x1, [fp, #-0x70]
    // 0x8e8650: ubfx            x1, x1, #0, #0x20
    // 0x8e8654: asr             x2, x1, #0x1b
    // 0x8e8658: orr             x1, x0, x2
    // 0x8e865c: ldur            x0, [fp, #-0x68]
    // 0x8e8660: add             x2, x0, x1
    // 0x8e8664: ldur            x0, [fp, #-0x60]
    // 0x8e8668: ubfx            x0, x0, #0, #0x20
    // 0x8e866c: ldur            x4, [fp, #-0x40]
    // 0x8e8670: eor             x1, x0, x4
    // 0x8e8674: ldur            x3, [fp, #-0x78]
    // 0x8e8678: eor             x0, x1, x3
    // 0x8e867c: add             x5, x2, x0
    // 0x8e8680: ldur            x2, [fp, #-0x80]
    // 0x8e8684: add             x6, x2, #1
    // 0x8e8688: ldur            x0, [fp, #-0x50]
    // 0x8e868c: mov             x1, x2
    // 0x8e8690: stur            x6, [fp, #-0x88]
    // 0x8e8694: cmp             x1, x0
    // 0x8e8698: b.hs            #0x8e8b18
    // 0x8e869c: ldur            x0, [fp, #-0x20]
    // 0x8e86a0: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e86a0: add             x16, x0, x2, lsl #2
    //     0x8e86a4: ldur            w1, [x16, #0xf]
    // 0x8e86a8: DecompressPointer r1
    //     0x8e86a8: add             x1, x1, HEAP, lsl #32
    // 0x8e86ac: r2 = LoadInt32Instr(r1)
    //     0x8e86ac: sbfx            x2, x1, #1, #0x1f
    //     0x8e86b0: tbz             w1, #0, #0x8e86b8
    //     0x8e86b4: ldur            x2, [x1, #7]
    // 0x8e86b8: ubfx            x5, x5, #0, #0x20
    // 0x8e86bc: add             w1, w5, w2
    // 0x8e86c0: r5 = 3395469782
    //     0x8e86c0: movz            x5, #0xc1d6
    //     0x8e86c4: movk            x5, #0xca62, lsl #16
    // 0x8e86c8: add             w7, w1, w5
    // 0x8e86cc: stur            x7, [fp, #-0x68]
    // 0x8e86d0: ldur            x1, [fp, #-0x60]
    // 0x8e86d4: ubfx            x1, x1, #0, #0x20
    // 0x8e86d8: r2 = 30
    //     0x8e86d8: movz            x2, #0x1e
    // 0x8e86dc: r0 = shiftl32()
    //     0x8e86dc: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e86e0: ldur            x1, [fp, #-0x60]
    // 0x8e86e4: ubfx            x1, x1, #0, #0x20
    // 0x8e86e8: asr             x2, x1, #2
    // 0x8e86ec: orr             x5, x0, x2
    // 0x8e86f0: stur            x5, [fp, #-0x60]
    // 0x8e86f4: ldur            x0, [fp, #-0x68]
    // 0x8e86f8: ubfx            x0, x0, #0, #0x20
    // 0x8e86fc: mov             x1, x0
    // 0x8e8700: r2 = 5
    //     0x8e8700: movz            x2, #0x5
    // 0x8e8704: r0 = shiftl32()
    //     0x8e8704: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e8708: ldur            x1, [fp, #-0x68]
    // 0x8e870c: ubfx            x1, x1, #0, #0x20
    // 0x8e8710: asr             x2, x1, #0x1b
    // 0x8e8714: orr             x1, x0, x2
    // 0x8e8718: ldur            x0, [fp, #-0x78]
    // 0x8e871c: add             x2, x0, x1
    // 0x8e8720: ldur            x0, [fp, #-0x70]
    // 0x8e8724: ubfx            x0, x0, #0, #0x20
    // 0x8e8728: ldur            x5, [fp, #-0x60]
    // 0x8e872c: eor             x1, x0, x5
    // 0x8e8730: ldur            x4, [fp, #-0x40]
    // 0x8e8734: eor             x0, x1, x4
    // 0x8e8738: add             x3, x2, x0
    // 0x8e873c: ldur            x2, [fp, #-0x88]
    // 0x8e8740: add             x6, x2, #1
    // 0x8e8744: ldur            x0, [fp, #-0x50]
    // 0x8e8748: mov             x1, x2
    // 0x8e874c: stur            x6, [fp, #-0x80]
    // 0x8e8750: cmp             x1, x0
    // 0x8e8754: b.hs            #0x8e8b1c
    // 0x8e8758: ldur            x0, [fp, #-0x20]
    // 0x8e875c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8e875c: add             x16, x0, x2, lsl #2
    //     0x8e8760: ldur            w1, [x16, #0xf]
    // 0x8e8764: DecompressPointer r1
    //     0x8e8764: add             x1, x1, HEAP, lsl #32
    // 0x8e8768: r2 = LoadInt32Instr(r1)
    //     0x8e8768: sbfx            x2, x1, #1, #0x1f
    //     0x8e876c: tbz             w1, #0, #0x8e8774
    //     0x8e8770: ldur            x2, [x1, #7]
    // 0x8e8774: ubfx            x3, x3, #0, #0x20
    // 0x8e8778: add             w1, w3, w2
    // 0x8e877c: r3 = 3395469782
    //     0x8e877c: movz            x3, #0xc1d6
    //     0x8e8780: movk            x3, #0xca62, lsl #16
    // 0x8e8784: add             w7, w1, w3
    // 0x8e8788: stur            x7, [fp, #-0x78]
    // 0x8e878c: ldur            x1, [fp, #-0x70]
    // 0x8e8790: ubfx            x1, x1, #0, #0x20
    // 0x8e8794: r2 = 30
    //     0x8e8794: movz            x2, #0x1e
    // 0x8e8798: r0 = shiftl32()
    //     0x8e8798: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e879c: ldur            x6, [fp, #-0x70]
    // 0x8e87a0: ubfx            x6, x6, #0, #0x20
    // 0x8e87a4: asr             x7, x6, #2
    // 0x8e87a8: orr             x1, x0, x7
    // 0x8e87ac: ldur            x6, [fp, #-0x48]
    // 0x8e87b0: add             x0, x6, #1
    // 0x8e87b4: ldur            x6, [fp, #-0x68]
    // 0x8e87b8: ubfx            x6, x6, #0, #0x20
    // 0x8e87bc: ldur            x2, [fp, #-0x78]
    // 0x8e87c0: ubfx            x2, x2, #0, #0x20
    // 0x8e87c4: mov             x10, x2
    // 0x8e87c8: mov             x9, x6
    // 0x8e87cc: mov             x7, x1
    // 0x8e87d0: ldur            x5, [fp, #-0x60]
    // 0x8e87d4: ldur            x4, [fp, #-0x40]
    // 0x8e87d8: ldur            x3, [fp, #-0x80]
    // 0x8e87dc: ldur            x6, [fp, #-0x20]
    // 0x8e87e0: ldur            x8, [fp, #-0x50]
    // 0x8e87e4: b               #0x8e83f8
    // 0x8e87e8: ldur            x6, [fp, #-0x58]
    // 0x8e87ec: mov             x0, x10
    // 0x8e87f0: mov             x2, x9
    // 0x8e87f4: mov             x3, x5
    // 0x8e87f8: mov             x5, x7
    // 0x8e87fc: LoadField: r7 = r6->field_f
    //     0x8e87fc: ldur            w7, [x6, #0xf]
    // 0x8e8800: DecompressPointer r7
    //     0x8e8800: add             x7, x7, HEAP, lsl #32
    // 0x8e8804: r8 = LoadInt32Instr(r7)
    //     0x8e8804: sbfx            x8, x7, #1, #0x1f
    //     0x8e8808: tbz             w7, #0, #0x8e8810
    //     0x8e880c: ldur            x8, [x7, #7]
    // 0x8e8810: ubfx            x0, x0, #0, #0x20
    // 0x8e8814: add             w7, w8, w0
    // 0x8e8818: lsl             w0, w7, #1
    // 0x8e881c: tst             x7, #0xc0000000
    // 0x8e8820: b.eq            #0x8e8850
    // 0x8e8824: r0 = inline_Allocate_Mint()
    //     0x8e8824: ldp             x0, x8, [THR, #0x50]  ; THR::top
    //     0x8e8828: add             x0, x0, #0x10
    //     0x8e882c: cmp             x8, x0
    //     0x8e8830: b.ls            #0x8e8b20
    //     0x8e8834: str             x0, [THR, #0x50]  ; THR::top
    //     0x8e8838: sub             x0, x0, #0xf
    //     0x8e883c: movz            x8, #0xd15c
    //     0x8e8840: movk            x8, #0x3, lsl #16
    //     0x8e8844: stur            x8, [x0, #-1]
    // 0x8e8848: ubfx            x8, x7, #0, #0x20
    // 0x8e884c: StoreField: r0->field_7 = r8
    //     0x8e884c: stur            x8, [x0, #7]
    // 0x8e8850: mov             x1, x6
    // 0x8e8854: ArrayStore: r1[0] = r0  ; List_4
    //     0x8e8854: add             x25, x1, #0xf
    //     0x8e8858: str             w0, [x25]
    //     0x8e885c: tbz             w0, #0, #0x8e8878
    //     0x8e8860: ldurb           w16, [x1, #-1]
    //     0x8e8864: ldurb           w17, [x0, #-1]
    //     0x8e8868: and             x16, x17, x16, lsr #2
    //     0x8e886c: tst             x16, HEAP, lsr #32
    //     0x8e8870: b.eq            #0x8e8878
    //     0x8e8874: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e8878: LoadField: r7 = r6->field_13
    //     0x8e8878: ldur            w7, [x6, #0x13]
    // 0x8e887c: DecompressPointer r7
    //     0x8e887c: add             x7, x7, HEAP, lsl #32
    // 0x8e8880: r8 = LoadInt32Instr(r7)
    //     0x8e8880: sbfx            x8, x7, #1, #0x1f
    //     0x8e8884: tbz             w7, #0, #0x8e888c
    //     0x8e8888: ldur            x8, [x7, #7]
    // 0x8e888c: ubfx            x2, x2, #0, #0x20
    // 0x8e8890: add             w7, w8, w2
    // 0x8e8894: lsl             w0, w7, #1
    // 0x8e8898: tst             x7, #0xc0000000
    // 0x8e889c: b.eq            #0x8e88cc
    // 0x8e88a0: r0 = inline_Allocate_Mint()
    //     0x8e88a0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x8e88a4: add             x0, x0, #0x10
    //     0x8e88a8: cmp             x2, x0
    //     0x8e88ac: b.ls            #0x8e8b40
    //     0x8e88b0: str             x0, [THR, #0x50]  ; THR::top
    //     0x8e88b4: sub             x0, x0, #0xf
    //     0x8e88b8: movz            x2, #0xd15c
    //     0x8e88bc: movk            x2, #0x3, lsl #16
    //     0x8e88c0: stur            x2, [x0, #-1]
    // 0x8e88c4: ubfx            x2, x7, #0, #0x20
    // 0x8e88c8: StoreField: r0->field_7 = r2
    //     0x8e88c8: stur            x2, [x0, #7]
    // 0x8e88cc: mov             x1, x6
    // 0x8e88d0: ArrayStore: r1[1] = r0  ; List_4
    //     0x8e88d0: add             x25, x1, #0x13
    //     0x8e88d4: str             w0, [x25]
    //     0x8e88d8: tbz             w0, #0, #0x8e88f4
    //     0x8e88dc: ldurb           w16, [x1, #-1]
    //     0x8e88e0: ldurb           w17, [x0, #-1]
    //     0x8e88e4: and             x16, x17, x16, lsr #2
    //     0x8e88e8: tst             x16, HEAP, lsr #32
    //     0x8e88ec: b.eq            #0x8e88f4
    //     0x8e88f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e88f4: ArrayLoad: r2 = r6[0]  ; List_4
    //     0x8e88f4: ldur            w2, [x6, #0x17]
    // 0x8e88f8: DecompressPointer r2
    //     0x8e88f8: add             x2, x2, HEAP, lsl #32
    // 0x8e88fc: r7 = LoadInt32Instr(r2)
    //     0x8e88fc: sbfx            x7, x2, #1, #0x1f
    //     0x8e8900: tbz             w2, #0, #0x8e8908
    //     0x8e8904: ldur            x7, [x2, #7]
    // 0x8e8908: ubfx            x5, x5, #0, #0x20
    // 0x8e890c: add             w2, w7, w5
    // 0x8e8910: lsl             w0, w2, #1
    // 0x8e8914: tst             x2, #0xc0000000
    // 0x8e8918: b.eq            #0x8e8948
    // 0x8e891c: r0 = inline_Allocate_Mint()
    //     0x8e891c: ldp             x0, x5, [THR, #0x50]  ; THR::top
    //     0x8e8920: add             x0, x0, #0x10
    //     0x8e8924: cmp             x5, x0
    //     0x8e8928: b.ls            #0x8e8b60
    //     0x8e892c: str             x0, [THR, #0x50]  ; THR::top
    //     0x8e8930: sub             x0, x0, #0xf
    //     0x8e8934: movz            x5, #0xd15c
    //     0x8e8938: movk            x5, #0x3, lsl #16
    //     0x8e893c: stur            x5, [x0, #-1]
    // 0x8e8940: ubfx            x5, x2, #0, #0x20
    // 0x8e8944: StoreField: r0->field_7 = r5
    //     0x8e8944: stur            x5, [x0, #7]
    // 0x8e8948: mov             x1, x6
    // 0x8e894c: ArrayStore: r1[2] = r0  ; List_4
    //     0x8e894c: add             x25, x1, #0x17
    //     0x8e8950: str             w0, [x25]
    //     0x8e8954: tbz             w0, #0, #0x8e8970
    //     0x8e8958: ldurb           w16, [x1, #-1]
    //     0x8e895c: ldurb           w17, [x0, #-1]
    //     0x8e8960: and             x16, x17, x16, lsr #2
    //     0x8e8964: tst             x16, HEAP, lsr #32
    //     0x8e8968: b.eq            #0x8e8970
    //     0x8e896c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e8970: LoadField: r2 = r6->field_1b
    //     0x8e8970: ldur            w2, [x6, #0x1b]
    // 0x8e8974: DecompressPointer r2
    //     0x8e8974: add             x2, x2, HEAP, lsl #32
    // 0x8e8978: r5 = LoadInt32Instr(r2)
    //     0x8e8978: sbfx            x5, x2, #1, #0x1f
    //     0x8e897c: tbz             w2, #0, #0x8e8984
    //     0x8e8980: ldur            x5, [x2, #7]
    // 0x8e8984: ubfx            x3, x3, #0, #0x20
    // 0x8e8988: add             w2, w5, w3
    // 0x8e898c: lsl             w0, w2, #1
    // 0x8e8990: tst             x2, #0xc0000000
    // 0x8e8994: b.eq            #0x8e89c4
    // 0x8e8998: r0 = inline_Allocate_Mint()
    //     0x8e8998: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x8e899c: add             x0, x0, #0x10
    //     0x8e89a0: cmp             x3, x0
    //     0x8e89a4: b.ls            #0x8e8b78
    //     0x8e89a8: str             x0, [THR, #0x50]  ; THR::top
    //     0x8e89ac: sub             x0, x0, #0xf
    //     0x8e89b0: movz            x3, #0xd15c
    //     0x8e89b4: movk            x3, #0x3, lsl #16
    //     0x8e89b8: stur            x3, [x0, #-1]
    // 0x8e89bc: ubfx            x3, x2, #0, #0x20
    // 0x8e89c0: StoreField: r0->field_7 = r3
    //     0x8e89c0: stur            x3, [x0, #7]
    // 0x8e89c4: mov             x1, x6
    // 0x8e89c8: ArrayStore: r1[3] = r0  ; List_4
    //     0x8e89c8: add             x25, x1, #0x1b
    //     0x8e89cc: str             w0, [x25]
    //     0x8e89d0: tbz             w0, #0, #0x8e89ec
    //     0x8e89d4: ldurb           w16, [x1, #-1]
    //     0x8e89d8: ldurb           w17, [x0, #-1]
    //     0x8e89dc: and             x16, x17, x16, lsr #2
    //     0x8e89e0: tst             x16, HEAP, lsr #32
    //     0x8e89e4: b.eq            #0x8e89ec
    //     0x8e89e8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e89ec: LoadField: r2 = r6->field_1f
    //     0x8e89ec: ldur            w2, [x6, #0x1f]
    // 0x8e89f0: DecompressPointer r2
    //     0x8e89f0: add             x2, x2, HEAP, lsl #32
    // 0x8e89f4: r3 = LoadInt32Instr(r2)
    //     0x8e89f4: sbfx            x3, x2, #1, #0x1f
    //     0x8e89f8: tbz             w2, #0, #0x8e8a00
    //     0x8e89fc: ldur            x3, [x2, #7]
    // 0x8e8a00: ubfx            x4, x4, #0, #0x20
    // 0x8e8a04: add             w2, w3, w4
    // 0x8e8a08: lsl             w0, w2, #1
    // 0x8e8a0c: tst             x2, #0xc0000000
    // 0x8e8a10: b.eq            #0x8e8a40
    // 0x8e8a14: r0 = inline_Allocate_Mint()
    //     0x8e8a14: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x8e8a18: add             x0, x0, #0x10
    //     0x8e8a1c: cmp             x3, x0
    //     0x8e8a20: b.ls            #0x8e8b90
    //     0x8e8a24: str             x0, [THR, #0x50]  ; THR::top
    //     0x8e8a28: sub             x0, x0, #0xf
    //     0x8e8a2c: movz            x3, #0xd15c
    //     0x8e8a30: movk            x3, #0x3, lsl #16
    //     0x8e8a34: stur            x3, [x0, #-1]
    // 0x8e8a38: ubfx            x3, x2, #0, #0x20
    // 0x8e8a3c: StoreField: r0->field_7 = r3
    //     0x8e8a3c: stur            x3, [x0, #7]
    // 0x8e8a40: mov             x1, x6
    // 0x8e8a44: ArrayStore: r1[4] = r0  ; List_4
    //     0x8e8a44: add             x25, x1, #0x1f
    //     0x8e8a48: str             w0, [x25]
    //     0x8e8a4c: tbz             w0, #0, #0x8e8a68
    //     0x8e8a50: ldurb           w16, [x1, #-1]
    //     0x8e8a54: ldurb           w17, [x0, #-1]
    //     0x8e8a58: and             x16, x17, x16, lsr #2
    //     0x8e8a5c: tst             x16, HEAP, lsr #32
    //     0x8e8a60: b.eq            #0x8e8a68
    //     0x8e8a64: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e8a68: r0 = Null
    //     0x8e8a68: mov             x0, NULL
    // 0x8e8a6c: LeaveFrame
    //     0x8e8a6c: mov             SP, fp
    //     0x8e8a70: ldp             fp, lr, [SP], #0x10
    // 0x8e8a74: ret
    //     0x8e8a74: ret             
    // 0x8e8a78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8a78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8a7c: b               #0x8e7428
    // 0x8e8a80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8a80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8a84: b               #0x8e7454
    // 0x8e8a88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8a88: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8a8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8a8c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8a90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8a90: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8a94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8a94: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8a98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8a98: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8a9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8a9c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8aa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8aa0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8aa4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8aa4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8aa8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8aa8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8aac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8aac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8ab0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8ab0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8ab4: b               #0x8e76c4
    // 0x8e8ab8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8ab8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8abc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8abc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8ac0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8ac0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8ac4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8ac4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8ac8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8ac8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8acc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8acc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8ad0: b               #0x8e7b48
    // 0x8e8ad4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8ad4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8ad8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8ad8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8adc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8adc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8ae0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8ae0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8ae4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8ae4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8ae8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8ae8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8aec: b               #0x8e7f78
    // 0x8e8af0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8af0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8af4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8af4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8af8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8af8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8afc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8afc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8b00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8b00: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8b04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8b04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8b08: b               #0x8e8420
    // 0x8e8b0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8b0c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8b10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8b10: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8b14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8b14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8b18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8b18: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8b1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8b1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8b20: stp             x6, x7, [SP, #-0x10]!
    // 0x8e8b24: stp             x4, x5, [SP, #-0x10]!
    // 0x8e8b28: stp             x2, x3, [SP, #-0x10]!
    // 0x8e8b2c: r0 = AllocateMint()
    //     0x8e8b2c: bl              #0xec22a8  ; AllocateMintStub
    // 0x8e8b30: ldp             x2, x3, [SP], #0x10
    // 0x8e8b34: ldp             x4, x5, [SP], #0x10
    // 0x8e8b38: ldp             x6, x7, [SP], #0x10
    // 0x8e8b3c: b               #0x8e8848
    // 0x8e8b40: stp             x6, x7, [SP, #-0x10]!
    // 0x8e8b44: stp             x4, x5, [SP, #-0x10]!
    // 0x8e8b48: SaveReg r3
    //     0x8e8b48: str             x3, [SP, #-8]!
    // 0x8e8b4c: r0 = AllocateMint()
    //     0x8e8b4c: bl              #0xec22a8  ; AllocateMintStub
    // 0x8e8b50: RestoreReg r3
    //     0x8e8b50: ldr             x3, [SP], #8
    // 0x8e8b54: ldp             x4, x5, [SP], #0x10
    // 0x8e8b58: ldp             x6, x7, [SP], #0x10
    // 0x8e8b5c: b               #0x8e88c4
    // 0x8e8b60: stp             x4, x6, [SP, #-0x10]!
    // 0x8e8b64: stp             x2, x3, [SP, #-0x10]!
    // 0x8e8b68: r0 = AllocateMint()
    //     0x8e8b68: bl              #0xec22a8  ; AllocateMintStub
    // 0x8e8b6c: ldp             x2, x3, [SP], #0x10
    // 0x8e8b70: ldp             x4, x6, [SP], #0x10
    // 0x8e8b74: b               #0x8e8940
    // 0x8e8b78: stp             x4, x6, [SP, #-0x10]!
    // 0x8e8b7c: SaveReg r2
    //     0x8e8b7c: str             x2, [SP, #-8]!
    // 0x8e8b80: r0 = AllocateMint()
    //     0x8e8b80: bl              #0xec22a8  ; AllocateMintStub
    // 0x8e8b84: RestoreReg r2
    //     0x8e8b84: ldr             x2, [SP], #8
    // 0x8e8b88: ldp             x4, x6, [SP], #0x10
    // 0x8e8b8c: b               #0x8e89bc
    // 0x8e8b90: stp             x2, x6, [SP, #-0x10]!
    // 0x8e8b94: r0 = AllocateMint()
    //     0x8e8b94: bl              #0xec22a8  ; AllocateMintStub
    // 0x8e8b98: ldp             x2, x6, [SP], #0x10
    // 0x8e8b9c: b               #0x8e8a38
  }
  _ resetState(/* No info */) {
    // ** addr: 0xcddd58, size: 0xc8
    // 0xcddd58: EnterFrame
    //     0xcddd58: stp             fp, lr, [SP, #-0x10]!
    //     0xcddd5c: mov             fp, SP
    // 0xcddd60: LoadField: r2 = r1->field_1f
    //     0xcddd60: ldur            w2, [x1, #0x1f]
    // 0xcddd64: DecompressPointer r2
    //     0xcddd64: add             x2, x2, HEAP, lsl #32
    // 0xcddd68: LoadField: r3 = r2->field_b
    //     0xcddd68: ldur            w3, [x2, #0xb]
    // 0xcddd6c: r4 = LoadInt32Instr(r3)
    //     0xcddd6c: sbfx            x4, x3, #1, #0x1f
    // 0xcddd70: mov             x0, x4
    // 0xcddd74: r1 = 0
    //     0xcddd74: movz            x1, #0
    // 0xcddd78: cmp             x1, x0
    // 0xcddd7c: b.hs            #0xcdde0c
    // 0xcddd80: r16 = 1732584193
    //     0xcddd80: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ca8] 0x67452301
    //     0xcddd84: ldr             x16, [x16, #0xca8]
    // 0xcddd88: StoreField: r2->field_f = r16
    //     0xcddd88: stur            w16, [x2, #0xf]
    // 0xcddd8c: mov             x0, x4
    // 0xcddd90: r1 = 1
    //     0xcddd90: movz            x1, #0x1
    // 0xcddd94: cmp             x1, x0
    // 0xcddd98: b.hs            #0xcdde10
    // 0xcddd9c: r16 = 4023233417
    //     0xcddd9c: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cb0] 0xefcdab89
    //     0xcddda0: ldr             x16, [x16, #0xcb0]
    // 0xcddda4: StoreField: r2->field_13 = r16
    //     0xcddda4: stur            w16, [x2, #0x13]
    // 0xcddda8: mov             x0, x4
    // 0xcdddac: r1 = 2
    //     0xcdddac: movz            x1, #0x2
    // 0xcdddb0: cmp             x1, x0
    // 0xcdddb4: b.hs            #0xcdde14
    // 0xcdddb8: r16 = 2562383102
    //     0xcdddb8: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cb8] 0x98badcfe
    //     0xcdddbc: ldr             x16, [x16, #0xcb8]
    // 0xcdddc0: ArrayStore: r2[0] = r16  ; List_4
    //     0xcdddc0: stur            w16, [x2, #0x17]
    // 0xcdddc4: mov             x0, x4
    // 0xcdddc8: r1 = 3
    //     0xcdddc8: movz            x1, #0x3
    // 0xcdddcc: cmp             x1, x0
    // 0xcdddd0: b.hs            #0xcdde18
    // 0xcdddd4: r16 = 543467756
    //     0xcdddd4: movz            x16, #0xa8ec
    //     0xcdddd8: movk            x16, #0x2064, lsl #16
    // 0xcddddc: StoreField: r2->field_1b = r16
    //     0xcddddc: stur            w16, [x2, #0x1b]
    // 0xcddde0: mov             x0, x4
    // 0xcddde4: r1 = 4
    //     0xcddde4: movz            x1, #0x4
    // 0xcddde8: cmp             x1, x0
    // 0xcdddec: b.hs            #0xcdde1c
    // 0xcdddf0: r16 = 3285377520
    //     0xcdddf0: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cc0] 0xc3d2e1f0
    //     0xcdddf4: ldr             x16, [x16, #0xcc0]
    // 0xcdddf8: StoreField: r2->field_1f = r16
    //     0xcdddf8: stur            w16, [x2, #0x1f]
    // 0xcdddfc: r0 = Null
    //     0xcdddfc: mov             x0, NULL
    // 0xcdde00: LeaveFrame
    //     0xcdde00: mov             SP, fp
    //     0xcdde04: ldp             fp, lr, [SP], #0x10
    // 0xcdde08: ret
    //     0xcdde08: ret             
    // 0xcdde0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcdde0c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcdde10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcdde10: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcdde14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcdde14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcdde18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcdde18: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcdde1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcdde1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
