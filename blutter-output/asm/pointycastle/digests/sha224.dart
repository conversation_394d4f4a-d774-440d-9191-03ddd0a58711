// lib: impl.digest.sha224, url: package:pointycastle/digests/sha224.dart

// class id: 1050952, size: 0x8
class :: {
}

// class id: 654, size: 0x38, field offset: 0x2c
class SHA224Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe0c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e2fc0, size: 0x58
    // 0x8e2fc0: EnterFrame
    //     0x8e2fc0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2fc4: mov             fp, SP
    // 0x8e2fc8: AllocStack(0x8)
    //     0x8e2fc8: sub             SP, SP, #8
    // 0x8e2fcc: r0 = StaticFactoryConfig()
    //     0x8e2fcc: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e2fd0: mov             x3, x0
    // 0x8e2fd4: r0 = "SHA-224"
    //     0x8e2fd4: add             x0, PP, #0x18, lsl #12  ; [pp+0x18230] "SHA-224"
    //     0x8e2fd8: ldr             x0, [x0, #0x230]
    // 0x8e2fdc: stur            x3, [fp, #-8]
    // 0x8e2fe0: StoreField: r3->field_b = r0
    //     0x8e2fe0: stur            w0, [x3, #0xb]
    // 0x8e2fe4: r1 = Function '<anonymous closure>': static.
    //     0x8e2fe4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19958] AnonymousClosure: static (0x8e3018), in [package:pointycastle/digests/sha224.dart] SHA224Digest::factoryConfig (0x8e2fc0)
    //     0x8e2fe8: ldr             x1, [x1, #0x958]
    // 0x8e2fec: r2 = Null
    //     0x8e2fec: mov             x2, NULL
    // 0x8e2ff0: r0 = AllocateClosure()
    //     0x8e2ff0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e2ff4: mov             x1, x0
    // 0x8e2ff8: ldur            x0, [fp, #-8]
    // 0x8e2ffc: StoreField: r0->field_f = r1
    //     0x8e2ffc: stur            w1, [x0, #0xf]
    // 0x8e3000: r1 = Digest
    //     0x8e3000: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e3004: ldr             x1, [x1, #0x388]
    // 0x8e3008: StoreField: r0->field_7 = r1
    //     0x8e3008: stur            w1, [x0, #7]
    // 0x8e300c: LeaveFrame
    //     0x8e300c: mov             SP, fp
    //     0x8e3010: ldp             fp, lr, [SP], #0x10
    // 0x8e3014: ret
    //     0x8e3014: ret             
  }
  [closure] static SHA224Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e3018, size: 0x40
    // 0x8e3018: EnterFrame
    //     0x8e3018: stp             fp, lr, [SP, #-0x10]!
    //     0x8e301c: mov             fp, SP
    // 0x8e3020: AllocStack(0x8)
    //     0x8e3020: sub             SP, SP, #8
    // 0x8e3024: CheckStackOverflow
    //     0x8e3024: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3028: cmp             SP, x16
    //     0x8e302c: b.ls            #0x8e3050
    // 0x8e3030: r0 = SHA224Digest()
    //     0x8e3030: bl              #0x8e30bc  ; AllocateSHA224DigestStub -> SHA224Digest (size=0x38)
    // 0x8e3034: mov             x1, x0
    // 0x8e3038: stur            x0, [fp, #-8]
    // 0x8e303c: r0 = SHA224Digest()
    //     0x8e303c: bl              #0x8e3058  ; [package:pointycastle/digests/sha224.dart] SHA224Digest::SHA224Digest
    // 0x8e3040: ldur            x0, [fp, #-8]
    // 0x8e3044: LeaveFrame
    //     0x8e3044: mov             SP, fp
    //     0x8e3048: ldp             fp, lr, [SP], #0x10
    // 0x8e304c: ret
    //     0x8e304c: ret             
    // 0x8e3050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3050: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3054: b               #0x8e3030
  }
  _ SHA224Digest(/* No info */) {
    // ** addr: 0x8e3058, size: 0x64
    // 0x8e3058: EnterFrame
    //     0x8e3058: stp             fp, lr, [SP, #-0x10]!
    //     0x8e305c: mov             fp, SP
    // 0x8e3060: AllocStack(0x8)
    //     0x8e3060: sub             SP, SP, #8
    // 0x8e3064: r2 = "SHA-224"
    //     0x8e3064: add             x2, PP, #0x18, lsl #12  ; [pp+0x18230] "SHA-224"
    //     0x8e3068: ldr             x2, [x2, #0x230]
    // 0x8e306c: r0 = 28
    //     0x8e306c: movz            x0, #0x1c
    // 0x8e3070: CheckStackOverflow
    //     0x8e3070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3074: cmp             SP, x16
    //     0x8e3078: b.ls            #0x8e30b4
    // 0x8e307c: StoreField: r1->field_2b = r2
    //     0x8e307c: stur            w2, [x1, #0x2b]
    // 0x8e3080: StoreField: r1->field_2f = r0
    //     0x8e3080: stur            x0, [x1, #0x2f]
    // 0x8e3084: r16 = 14
    //     0x8e3084: movz            x16, #0xe
    // 0x8e3088: str             x16, [SP]
    // 0x8e308c: r2 = Instance_Endian
    //     0x8e308c: add             x2, PP, #0x12, lsl #12  ; [pp+0x12390] Obj!Endian@e2cbb1
    //     0x8e3090: ldr             x2, [x2, #0x390]
    // 0x8e3094: r3 = 8
    //     0x8e3094: movz            x3, #0x8
    // 0x8e3098: r5 = 64
    //     0x8e3098: movz            x5, #0x40
    // 0x8e309c: r4 = const [0, 0x5, 0x1, 0x5, null]
    //     0x8e309c: ldr             x4, [PP, #0x718]  ; [pp+0x718] List(5) [0, 0x5, 0x1, 0x5, Null]
    // 0x8e30a0: r0 = MD4FamilyDigest()
    //     0x8e30a0: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8e30a4: r0 = Null
    //     0x8e30a4: mov             x0, NULL
    // 0x8e30a8: LeaveFrame
    //     0x8e30a8: mov             SP, fp
    //     0x8e30ac: ldp             fp, lr, [SP], #0x10
    // 0x8e30b0: ret
    //     0x8e30b0: ret             
    // 0x8e30b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e30b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e30b8: b               #0x8e307c
  }
  _ resetState(/* No info */) {
    // ** addr: 0xcdde20, size: 0x128
    // 0xcdde20: EnterFrame
    //     0xcdde20: stp             fp, lr, [SP, #-0x10]!
    //     0xcdde24: mov             fp, SP
    // 0xcdde28: LoadField: r2 = r1->field_1f
    //     0xcdde28: ldur            w2, [x1, #0x1f]
    // 0xcdde2c: DecompressPointer r2
    //     0xcdde2c: add             x2, x2, HEAP, lsl #32
    // 0xcdde30: LoadField: r3 = r2->field_b
    //     0xcdde30: ldur            w3, [x2, #0xb]
    // 0xcdde34: r4 = LoadInt32Instr(r3)
    //     0xcdde34: sbfx            x4, x3, #1, #0x1f
    // 0xcdde38: mov             x0, x4
    // 0xcdde3c: r1 = 0
    //     0xcdde3c: movz            x1, #0
    // 0xcdde40: cmp             x1, x0
    // 0xcdde44: b.hs            #0xcddf28
    // 0xcdde48: r16 = 3238371032
    //     0xcdde48: add             x16, PP, #0x19, lsl #12  ; [pp+0x198e8] 0xc1059ed8
    //     0xcdde4c: ldr             x16, [x16, #0x8e8]
    // 0xcdde50: StoreField: r2->field_f = r16
    //     0xcdde50: stur            w16, [x2, #0xf]
    // 0xcdde54: mov             x0, x4
    // 0xcdde58: r1 = 1
    //     0xcdde58: movz            x1, #0x1
    // 0xcdde5c: cmp             x1, x0
    // 0xcdde60: b.hs            #0xcddf2c
    // 0xcdde64: r16 = 1828301326
    //     0xcdde64: movz            x16, #0xaa0e
    //     0xcdde68: movk            x16, #0x6cf9, lsl #16
    // 0xcdde6c: StoreField: r2->field_13 = r16
    //     0xcdde6c: stur            w16, [x2, #0x13]
    // 0xcdde70: mov             x0, x4
    // 0xcdde74: r1 = 2
    //     0xcdde74: movz            x1, #0x2
    // 0xcdde78: cmp             x1, x0
    // 0xcdde7c: b.hs            #0xcddf30
    // 0xcdde80: r16 = 1625405998
    //     0xcdde80: movz            x16, #0xba2e
    //     0xcdde84: movk            x16, #0x60e1, lsl #16
    // 0xcdde88: ArrayStore: r2[0] = r16  ; List_4
    //     0xcdde88: stur            w16, [x2, #0x17]
    // 0xcdde8c: mov             x0, x4
    // 0xcdde90: r1 = 3
    //     0xcdde90: movz            x1, #0x3
    // 0xcdde94: cmp             x1, x0
    // 0xcdde98: b.hs            #0xcddf34
    // 0xcdde9c: r16 = 4144912697
    //     0xcdde9c: add             x16, PP, #0x19, lsl #12  ; [pp+0x19908] 0xf70e5939
    //     0xcddea0: ldr             x16, [x16, #0x908]
    // 0xcddea4: StoreField: r2->field_1b = r16
    //     0xcddea4: stur            w16, [x2, #0x1b]
    // 0xcddea8: mov             x0, x4
    // 0xcddeac: r1 = 4
    //     0xcddeac: movz            x1, #0x4
    // 0xcddeb0: cmp             x1, x0
    // 0xcddeb4: b.hs            #0xcddf38
    // 0xcddeb8: r16 = 4290775857
    //     0xcddeb8: add             x16, PP, #0x19, lsl #12  ; [pp+0x19910] 0xffc00b31
    //     0xcddebc: ldr             x16, [x16, #0x910]
    // 0xcddec0: StoreField: r2->field_1f = r16
    //     0xcddec0: stur            w16, [x2, #0x1f]
    // 0xcddec4: mov             x0, x4
    // 0xcddec8: r1 = 5
    //     0xcddec8: movz            x1, #0x5
    // 0xcddecc: cmp             x1, x0
    // 0xcdded0: b.hs            #0xcddf3c
    // 0xcdded4: r16 = 1750603025
    //     0xcdded4: add             x16, PP, #0x19, lsl #12  ; [pp+0x19920] 0x68581511
    //     0xcdded8: ldr             x16, [x16, #0x920]
    // 0xcddedc: StoreField: r2->field_23 = r16
    //     0xcddedc: stur            w16, [x2, #0x23]
    // 0xcddee0: mov             x0, x4
    // 0xcddee4: r1 = 6
    //     0xcddee4: movz            x1, #0x6
    // 0xcddee8: cmp             x1, x0
    // 0xcddeec: b.hs            #0xcddf40
    // 0xcddef0: r16 = 1694076839
    //     0xcddef0: add             x16, PP, #0x19, lsl #12  ; [pp+0x19930] 0x64f98fa7
    //     0xcddef4: ldr             x16, [x16, #0x930]
    // 0xcddef8: StoreField: r2->field_27 = r16
    //     0xcddef8: stur            w16, [x2, #0x27]
    // 0xcddefc: mov             x0, x4
    // 0xcddf00: r1 = 7
    //     0xcddf00: movz            x1, #0x7
    // 0xcddf04: cmp             x1, x0
    // 0xcddf08: b.hs            #0xcddf44
    // 0xcddf0c: r16 = 3204075428
    //     0xcddf0c: add             x16, PP, #0x19, lsl #12  ; [pp+0x19940] 0xbefa4fa4
    //     0xcddf10: ldr             x16, [x16, #0x940]
    // 0xcddf14: StoreField: r2->field_2b = r16
    //     0xcddf14: stur            w16, [x2, #0x2b]
    // 0xcddf18: r0 = Null
    //     0xcddf18: mov             x0, NULL
    // 0xcddf1c: LeaveFrame
    //     0xcddf1c: mov             SP, fp
    //     0xcddf20: ldp             fp, lr, [SP], #0x10
    // 0xcddf24: ret
    //     0xcddf24: ret             
    // 0xcddf28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddf28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddf2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddf2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddf30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddf30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddf34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddf34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddf38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddf38: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddf3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddf3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddf40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddf40: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcddf44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcddf44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
