// lib: impl.digest.sha256, url: package:pointycastle/digests/sha256.dart

// class id: 1050953, size: 0x8
class :: {
}

// class id: 653, size: 0x38, field offset: 0x2c
class SHA256Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe10

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e2ef0, size: 0x58
    // 0x8e2ef0: EnterFrame
    //     0x8e2ef0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2ef4: mov             fp, SP
    // 0x8e2ef8: AllocStack(0x8)
    //     0x8e2ef8: sub             SP, SP, #8
    // 0x8e2efc: r0 = StaticFactoryConfig()
    //     0x8e2efc: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e2f00: mov             x3, x0
    // 0x8e2f04: r0 = "SHA-256"
    //     0x8e2f04: add             x0, PP, #0x18, lsl #12  ; [pp+0x18240] "SHA-256"
    //     0x8e2f08: ldr             x0, [x0, #0x240]
    // 0x8e2f0c: stur            x3, [fp, #-8]
    // 0x8e2f10: StoreField: r3->field_b = r0
    //     0x8e2f10: stur            w0, [x3, #0xb]
    // 0x8e2f14: r1 = Function '<anonymous closure>': static.
    //     0x8e2f14: add             x1, PP, #0x19, lsl #12  ; [pp+0x19950] AnonymousClosure: static (0x8e2f48), in [package:pointycastle/digests/sha256.dart] SHA256Digest::factoryConfig (0x8e2ef0)
    //     0x8e2f18: ldr             x1, [x1, #0x950]
    // 0x8e2f1c: r2 = Null
    //     0x8e2f1c: mov             x2, NULL
    // 0x8e2f20: r0 = AllocateClosure()
    //     0x8e2f20: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e2f24: mov             x1, x0
    // 0x8e2f28: ldur            x0, [fp, #-8]
    // 0x8e2f2c: StoreField: r0->field_f = r1
    //     0x8e2f2c: stur            w1, [x0, #0xf]
    // 0x8e2f30: r1 = Digest
    //     0x8e2f30: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e2f34: ldr             x1, [x1, #0x388]
    // 0x8e2f38: StoreField: r0->field_7 = r1
    //     0x8e2f38: stur            w1, [x0, #7]
    // 0x8e2f3c: LeaveFrame
    //     0x8e2f3c: mov             SP, fp
    //     0x8e2f40: ldp             fp, lr, [SP], #0x10
    // 0x8e2f44: ret
    //     0x8e2f44: ret             
  }
  [closure] static SHA256Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e2f48, size: 0x6c
    // 0x8e2f48: EnterFrame
    //     0x8e2f48: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2f4c: mov             fp, SP
    // 0x8e2f50: AllocStack(0x8)
    //     0x8e2f50: sub             SP, SP, #8
    // 0x8e2f54: CheckStackOverflow
    //     0x8e2f54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e2f58: cmp             SP, x16
    //     0x8e2f5c: b.ls            #0x8e2fac
    // 0x8e2f60: r0 = SHA256Digest()
    //     0x8e2f60: bl              #0x8e2fb4  ; AllocateSHA256DigestStub -> SHA256Digest (size=0x38)
    // 0x8e2f64: mov             x4, x0
    // 0x8e2f68: r0 = "SHA-256"
    //     0x8e2f68: add             x0, PP, #0x18, lsl #12  ; [pp+0x18240] "SHA-256"
    //     0x8e2f6c: ldr             x0, [x0, #0x240]
    // 0x8e2f70: stur            x4, [fp, #-8]
    // 0x8e2f74: StoreField: r4->field_2b = r0
    //     0x8e2f74: stur            w0, [x4, #0x2b]
    // 0x8e2f78: r0 = 32
    //     0x8e2f78: movz            x0, #0x20
    // 0x8e2f7c: StoreField: r4->field_2f = r0
    //     0x8e2f7c: stur            x0, [x4, #0x2f]
    // 0x8e2f80: mov             x1, x4
    // 0x8e2f84: r2 = Instance_Endian
    //     0x8e2f84: add             x2, PP, #0x12, lsl #12  ; [pp+0x12390] Obj!Endian@e2cbb1
    //     0x8e2f88: ldr             x2, [x2, #0x390]
    // 0x8e2f8c: r3 = 8
    //     0x8e2f8c: movz            x3, #0x8
    // 0x8e2f90: r5 = 64
    //     0x8e2f90: movz            x5, #0x40
    // 0x8e2f94: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8e2f94: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8e2f98: r0 = MD4FamilyDigest()
    //     0x8e2f98: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8e2f9c: ldur            x0, [fp, #-8]
    // 0x8e2fa0: LeaveFrame
    //     0x8e2fa0: mov             SP, fp
    //     0x8e2fa4: ldp             fp, lr, [SP], #0x10
    // 0x8e2fa8: ret
    //     0x8e2fa8: ret             
    // 0x8e2fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e2fac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e2fb0: b               #0x8e2f60
  }
  _ resetState(/* No info */) {
    // ** addr: 0xcddf48, size: 0x128
    // 0xcddf48: EnterFrame
    //     0xcddf48: stp             fp, lr, [SP, #-0x10]!
    //     0xcddf4c: mov             fp, SP
    // 0xcddf50: LoadField: r2 = r1->field_1f
    //     0xcddf50: ldur            w2, [x1, #0x1f]
    // 0xcddf54: DecompressPointer r2
    //     0xcddf54: add             x2, x2, HEAP, lsl #32
    // 0xcddf58: LoadField: r3 = r2->field_b
    //     0xcddf58: ldur            w3, [x2, #0xb]
    // 0xcddf5c: r4 = LoadInt32Instr(r3)
    //     0xcddf5c: sbfx            x4, x3, #1, #0x1f
    // 0xcddf60: mov             x0, x4
    // 0xcddf64: r1 = 0
    //     0xcddf64: movz            x1, #0
    // 0xcddf68: cmp             x1, x0
    // 0xcddf6c: b.hs            #0xcde050
    // 0xcddf70: r16 = 1779033703
    //     0xcddf70: add             x16, PP, #0x12, lsl #12  ; [pp+0x12360] 0x6a09e667
    //     0xcddf74: ldr             x16, [x16, #0x360]
    // 0xcddf78: StoreField: r2->field_f = r16
    //     0xcddf78: stur            w16, [x2, #0xf]
    // 0xcddf7c: mov             x0, x4
    // 0xcddf80: r1 = 1
    //     0xcddf80: movz            x1, #0x1
    // 0xcddf84: cmp             x1, x0
    // 0xcddf88: b.hs            #0xcde054
    // 0xcddf8c: r16 = 3144134277
    //     0xcddf8c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12368] 0xbb67ae85
    //     0xcddf90: ldr             x16, [x16, #0x368]
    // 0xcddf94: StoreField: r2->field_13 = r16
    //     0xcddf94: stur            w16, [x2, #0x13]
    // 0xcddf98: mov             x0, x4
    // 0xcddf9c: r1 = 2
    //     0xcddf9c: movz            x1, #0x2
    // 0xcddfa0: cmp             x1, x0
    // 0xcddfa4: b.hs            #0xcde058
    // 0xcddfa8: r16 = 2027808484
    //     0xcddfa8: movz            x16, #0xe6e4
    //     0xcddfac: movk            x16, #0x78dd, lsl #16
    // 0xcddfb0: ArrayStore: r2[0] = r16  ; List_4
    //     0xcddfb0: stur            w16, [x2, #0x17]
    // 0xcddfb4: mov             x0, x4
    // 0xcddfb8: r1 = 3
    //     0xcddfb8: movz            x1, #0x3
    // 0xcddfbc: cmp             x1, x0
    // 0xcddfc0: b.hs            #0xcde05c
    // 0xcddfc4: r16 = 2773480762
    //     0xcddfc4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12370] 0xa54ff53a
    //     0xcddfc8: ldr             x16, [x16, #0x370]
    // 0xcddfcc: StoreField: r2->field_1b = r16
    //     0xcddfcc: stur            w16, [x2, #0x1b]
    // 0xcddfd0: mov             x0, x4
    // 0xcddfd4: r1 = 4
    //     0xcddfd4: movz            x1, #0x4
    // 0xcddfd8: cmp             x1, x0
    // 0xcddfdc: b.hs            #0xcde060
    // 0xcddfe0: r16 = 1359893119
    //     0xcddfe0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12378] 0x510e527f
    //     0xcddfe4: ldr             x16, [x16, #0x378]
    // 0xcddfe8: StoreField: r2->field_1f = r16
    //     0xcddfe8: stur            w16, [x2, #0x1f]
    // 0xcddfec: mov             x0, x4
    // 0xcddff0: r1 = 5
    //     0xcddff0: movz            x1, #0x5
    // 0xcddff4: cmp             x1, x0
    // 0xcddff8: b.hs            #0xcde064
    // 0xcddffc: r16 = 2600822924
    //     0xcddffc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12380] 0x9b05688c
    //     0xcde000: ldr             x16, [x16, #0x380]
    // 0xcde004: StoreField: r2->field_23 = r16
    //     0xcde004: stur            w16, [x2, #0x23]
    // 0xcde008: mov             x0, x4
    // 0xcde00c: r1 = 6
    //     0xcde00c: movz            x1, #0x6
    // 0xcde010: cmp             x1, x0
    // 0xcde014: b.hs            #0xcde068
    // 0xcde018: r16 = 1057469270
    //     0xcde018: movz            x16, #0xb356
    //     0xcde01c: movk            x16, #0x3f07, lsl #16
    // 0xcde020: StoreField: r2->field_27 = r16
    //     0xcde020: stur            w16, [x2, #0x27]
    // 0xcde024: mov             x0, x4
    // 0xcde028: r1 = 7
    //     0xcde028: movz            x1, #0x7
    // 0xcde02c: cmp             x1, x0
    // 0xcde030: b.hs            #0xcde06c
    // 0xcde034: r16 = 1541459225
    //     0xcde034: add             x16, PP, #0x12, lsl #12  ; [pp+0x12388] 0x5be0cd19
    //     0xcde038: ldr             x16, [x16, #0x388]
    // 0xcde03c: StoreField: r2->field_2b = r16
    //     0xcde03c: stur            w16, [x2, #0x2b]
    // 0xcde040: r0 = Null
    //     0xcde040: mov             x0, NULL
    // 0xcde044: LeaveFrame
    //     0xcde044: mov             SP, fp
    //     0xcde048: ldp             fp, lr, [SP], #0x10
    // 0xcde04c: ret
    //     0xcde04c: ret             
    // 0xcde050: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde050: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde054: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde054: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde058: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde058: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde05c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde05c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde060: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde060: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde064: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde064: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde068: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde068: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde06c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde06c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
