// lib: impl.digest.sha3, url: package:pointycastle/digests/sha3.dart

// class id: 1050954, size: 0x8
class :: {
}

// class id: 664, size: 0x18, field offset: 0x18
class SHA3Digest extends KeccakEngine {

  static late final FactoryConfig factoryConfig; // offset: 0xdd8
  static late final RegExp _sha3REGEX; // offset: 0xdd4

  get _ algorithmName(/* No info */) {
    // ** addr: 0x869c68, size: 0x7c
    // 0x869c68: EnterFrame
    //     0x869c68: stp             fp, lr, [SP, #-0x10]!
    //     0x869c6c: mov             fp, SP
    // 0x869c70: AllocStack(0x10)
    //     0x869c70: sub             SP, SP, #0x10
    // 0x869c74: SetupParameters(SHA3Digest this /* r1 => r0, fp-0x8 */)
    //     0x869c74: mov             x0, x1
    //     0x869c78: stur            x1, [fp, #-8]
    // 0x869c7c: CheckStackOverflow
    //     0x869c7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x869c80: cmp             SP, x16
    //     0x869c84: b.ls            #0x869cd0
    // 0x869c88: r1 = Null
    //     0x869c88: mov             x1, NULL
    // 0x869c8c: r2 = 4
    //     0x869c8c: movz            x2, #0x4
    // 0x869c90: r0 = AllocateArray()
    //     0x869c90: bl              #0xec22fc  ; AllocateArrayStub
    // 0x869c94: r16 = "SHA3-"
    //     0x869c94: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cd8] "SHA3-"
    //     0x869c98: ldr             x16, [x16, #0xcd8]
    // 0x869c9c: StoreField: r0->field_f = r16
    //     0x869c9c: stur            w16, [x0, #0xf]
    // 0x869ca0: ldur            x1, [fp, #-8]
    // 0x869ca4: LoadField: r2 = r1->field_13
    //     0x869ca4: ldur            w2, [x1, #0x13]
    // 0x869ca8: DecompressPointer r2
    //     0x869ca8: add             x2, x2, HEAP, lsl #32
    // 0x869cac: r16 = Sentinel
    //     0x869cac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x869cb0: cmp             w2, w16
    // 0x869cb4: b.eq            #0x869cd8
    // 0x869cb8: StoreField: r0->field_13 = r2
    //     0x869cb8: stur            w2, [x0, #0x13]
    // 0x869cbc: str             x0, [SP]
    // 0x869cc0: r0 = _interpolate()
    //     0x869cc0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x869cc4: LeaveFrame
    //     0x869cc4: mov             SP, fp
    //     0x869cc8: ldp             fp, lr, [SP], #0x10
    // 0x869ccc: ret
    //     0x869ccc: ret             
    // 0x869cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x869cd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x869cd4: b               #0x869c88
    // 0x869cd8: r9 = fixedOutputLength
    //     0x869cd8: add             x9, PP, #0x20, lsl #12  ; [pp+0x209f8] Field <KeccakEngine.fixedOutputLength>: late (offset: 0x14)
    //     0x869cdc: ldr             x9, [x9, #0x9f8]
    // 0x869ce0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x869ce0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e344c, size: 0x8c
    // 0x8e344c: EnterFrame
    //     0x8e344c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3450: mov             fp, SP
    // 0x8e3454: AllocStack(0x10)
    //     0x8e3454: sub             SP, SP, #0x10
    // 0x8e3458: CheckStackOverflow
    //     0x8e3458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e345c: cmp             SP, x16
    //     0x8e3460: b.ls            #0x8e34d0
    // 0x8e3464: r0 = InitLateStaticField(0xdd4) // [package:pointycastle/digests/sha3.dart] SHA3Digest::_sha3REGEX
    //     0x8e3464: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e3468: ldr             x0, [x0, #0x1ba8]
    //     0x8e346c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e3470: cmp             w0, w16
    //     0x8e3474: b.ne            #0x8e3484
    //     0x8e3478: add             x2, PP, #0x19, lsl #12  ; [pp+0x19988] Field <SHA3Digest._sha3REGEX@920312058>: static late final (offset: 0xdd4)
    //     0x8e347c: ldr             x2, [x2, #0x988]
    //     0x8e3480: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e3484: stur            x0, [fp, #-8]
    // 0x8e3488: r0 = DynamicFactoryConfig()
    //     0x8e3488: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8e348c: mov             x3, x0
    // 0x8e3490: ldur            x0, [fp, #-8]
    // 0x8e3494: stur            x3, [fp, #-0x10]
    // 0x8e3498: StoreField: r3->field_b = r0
    //     0x8e3498: stur            w0, [x3, #0xb]
    // 0x8e349c: r1 = Function '<anonymous closure>': static.
    //     0x8e349c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19990] AnonymousClosure: static (0x8e34d8), in [package:pointycastle/digests/sha3.dart] SHA3Digest::factoryConfig (0x8e344c)
    //     0x8e34a0: ldr             x1, [x1, #0x990]
    // 0x8e34a4: r2 = Null
    //     0x8e34a4: mov             x2, NULL
    // 0x8e34a8: r0 = AllocateClosure()
    //     0x8e34a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e34ac: mov             x1, x0
    // 0x8e34b0: ldur            x0, [fp, #-0x10]
    // 0x8e34b4: StoreField: r0->field_f = r1
    //     0x8e34b4: stur            w1, [x0, #0xf]
    // 0x8e34b8: r1 = Digest
    //     0x8e34b8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e34bc: ldr             x1, [x1, #0x388]
    // 0x8e34c0: StoreField: r0->field_7 = r1
    //     0x8e34c0: stur            w1, [x0, #7]
    // 0x8e34c4: LeaveFrame
    //     0x8e34c4: mov             SP, fp
    //     0x8e34c8: ldp             fp, lr, [SP], #0x10
    // 0x8e34cc: ret
    //     0x8e34cc: ret             
    // 0x8e34d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e34d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e34d4: b               #0x8e3464
  }
  [closure] static (dynamic) => SHA3Digest <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8e34d8, size: 0x54
    // 0x8e34d8: EnterFrame
    //     0x8e34d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e34dc: mov             fp, SP
    // 0x8e34e0: AllocStack(0x8)
    //     0x8e34e0: sub             SP, SP, #8
    // 0x8e34e4: SetupParameters()
    //     0x8e34e4: ldr             x0, [fp, #0x20]
    //     0x8e34e8: ldur            w1, [x0, #0x17]
    //     0x8e34ec: add             x1, x1, HEAP, lsl #32
    //     0x8e34f0: stur            x1, [fp, #-8]
    // 0x8e34f4: r1 = 1
    //     0x8e34f4: movz            x1, #0x1
    // 0x8e34f8: r0 = AllocateContext()
    //     0x8e34f8: bl              #0xec126c  ; AllocateContextStub
    // 0x8e34fc: mov             x1, x0
    // 0x8e3500: ldur            x0, [fp, #-8]
    // 0x8e3504: StoreField: r1->field_b = r0
    //     0x8e3504: stur            w0, [x1, #0xb]
    // 0x8e3508: ldr             x0, [fp, #0x10]
    // 0x8e350c: StoreField: r1->field_f = r0
    //     0x8e350c: stur            w0, [x1, #0xf]
    // 0x8e3510: mov             x2, x1
    // 0x8e3514: r1 = Function '<anonymous closure>': static.
    //     0x8e3514: add             x1, PP, #0x19, lsl #12  ; [pp+0x19998] AnonymousClosure: static (0x8e352c), in [package:pointycastle/digests/sha3.dart] SHA3Digest::factoryConfig (0x8e344c)
    //     0x8e3518: ldr             x1, [x1, #0x998]
    // 0x8e351c: r0 = AllocateClosure()
    //     0x8e351c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e3520: LeaveFrame
    //     0x8e3520: mov             SP, fp
    //     0x8e3524: ldp             fp, lr, [SP], #0x10
    // 0x8e3528: ret
    //     0x8e3528: ret             
  }
  [closure] static SHA3Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e352c, size: 0x98
    // 0x8e352c: EnterFrame
    //     0x8e352c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3530: mov             fp, SP
    // 0x8e3534: AllocStack(0x10)
    //     0x8e3534: sub             SP, SP, #0x10
    // 0x8e3538: SetupParameters()
    //     0x8e3538: ldr             x0, [fp, #0x10]
    //     0x8e353c: ldur            w1, [x0, #0x17]
    //     0x8e3540: add             x1, x1, HEAP, lsl #32
    // 0x8e3544: CheckStackOverflow
    //     0x8e3544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3548: cmp             SP, x16
    //     0x8e354c: b.ls            #0x8e35b8
    // 0x8e3550: LoadField: r0 = r1->field_f
    //     0x8e3550: ldur            w0, [x1, #0xf]
    // 0x8e3554: DecompressPointer r0
    //     0x8e3554: add             x0, x0, HEAP, lsl #32
    // 0x8e3558: r1 = LoadClassIdInstr(r0)
    //     0x8e3558: ldur            x1, [x0, #-1]
    //     0x8e355c: ubfx            x1, x1, #0xc, #0x14
    // 0x8e3560: mov             x16, x0
    // 0x8e3564: mov             x0, x1
    // 0x8e3568: mov             x1, x16
    // 0x8e356c: r2 = 1
    //     0x8e356c: movz            x2, #0x1
    // 0x8e3570: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8e3570: sub             lr, x0, #0xfdd
    //     0x8e3574: ldr             lr, [x21, lr, lsl #3]
    //     0x8e3578: blr             lr
    // 0x8e357c: cmp             w0, NULL
    // 0x8e3580: b.eq            #0x8e35c0
    // 0x8e3584: mov             x1, x0
    // 0x8e3588: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8e3588: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8e358c: r0 = parse()
    //     0x8e358c: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8e3590: stur            x0, [fp, #-8]
    // 0x8e3594: r0 = SHA3Digest()
    //     0x8e3594: bl              #0x8e36f0  ; AllocateSHA3DigestStub -> SHA3Digest (size=0x18)
    // 0x8e3598: mov             x1, x0
    // 0x8e359c: ldur            x2, [fp, #-8]
    // 0x8e35a0: stur            x0, [fp, #-0x10]
    // 0x8e35a4: r0 = SHA3Digest()
    //     0x8e35a4: bl              #0x8e35c4  ; [package:pointycastle/digests/sha3.dart] SHA3Digest::SHA3Digest
    // 0x8e35a8: ldur            x0, [fp, #-0x10]
    // 0x8e35ac: LeaveFrame
    //     0x8e35ac: mov             SP, fp
    //     0x8e35b0: ldp             fp, lr, [SP], #0x10
    // 0x8e35b4: ret
    //     0x8e35b4: ret             
    // 0x8e35b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e35b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e35bc: b               #0x8e3550
    // 0x8e35c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8e35c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ SHA3Digest(/* No info */) {
    // ** addr: 0x8e35c4, size: 0x12c
    // 0x8e35c4: EnterFrame
    //     0x8e35c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e35c8: mov             fp, SP
    // 0x8e35cc: AllocStack(0x18)
    //     0x8e35cc: sub             SP, SP, #0x18
    // 0x8e35d0: r0 = Sentinel
    //     0x8e35d0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e35d4: stur            x1, [fp, #-8]
    // 0x8e35d8: stur            x2, [fp, #-0x10]
    // 0x8e35dc: CheckStackOverflow
    //     0x8e35dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e35e0: cmp             SP, x16
    //     0x8e35e4: b.ls            #0x8e36e8
    // 0x8e35e8: StoreField: r1->field_f = r0
    //     0x8e35e8: stur            w0, [x1, #0xf]
    // 0x8e35ec: StoreField: r1->field_13 = r0
    //     0x8e35ec: stur            w0, [x1, #0x13]
    // 0x8e35f0: r4 = 400
    //     0x8e35f0: movz            x4, #0x190
    // 0x8e35f4: r0 = AllocateUint8Array()
    //     0x8e35f4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e35f8: ldur            x1, [fp, #-8]
    // 0x8e35fc: StoreField: r1->field_7 = r0
    //     0x8e35fc: stur            w0, [x1, #7]
    //     0x8e3600: ldurb           w16, [x1, #-1]
    //     0x8e3604: ldurb           w17, [x0, #-1]
    //     0x8e3608: and             x16, x17, x16, lsr #2
    //     0x8e360c: tst             x16, HEAP, lsr #32
    //     0x8e3610: b.eq            #0x8e3618
    //     0x8e3614: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e3618: r4 = 384
    //     0x8e3618: movz            x4, #0x180
    // 0x8e361c: r0 = AllocateUint8Array()
    //     0x8e361c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8e3620: ldur            x1, [fp, #-8]
    // 0x8e3624: StoreField: r1->field_b = r0
    //     0x8e3624: stur            w0, [x1, #0xb]
    //     0x8e3628: ldurb           w16, [x1, #-1]
    //     0x8e362c: ldurb           w17, [x0, #-1]
    //     0x8e3630: and             x16, x17, x16, lsr #2
    //     0x8e3634: tst             x16, HEAP, lsr #32
    //     0x8e3638: b.eq            #0x8e3640
    //     0x8e363c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e3640: ldur            x0, [fp, #-0x10]
    // 0x8e3644: cmp             x0, #0xe0
    // 0x8e3648: b.eq            #0x8e3664
    // 0x8e364c: cmp             x0, #0x100
    // 0x8e3650: b.eq            #0x8e3664
    // 0x8e3654: cmp             x0, #0x180
    // 0x8e3658: b.eq            #0x8e3664
    // 0x8e365c: cmp             x0, #0x200
    // 0x8e3660: b.ne            #0x8e367c
    // 0x8e3664: mov             x2, x0
    // 0x8e3668: r0 = init()
    //     0x8e3668: bl              #0x8d639c  ; [package:pointycastle/src/impl/keccak_engine.dart] KeccakEngine::init
    // 0x8e366c: r0 = Null
    //     0x8e366c: mov             x0, NULL
    // 0x8e3670: LeaveFrame
    //     0x8e3670: mov             SP, fp
    //     0x8e3674: ldp             fp, lr, [SP], #0x10
    // 0x8e3678: ret
    //     0x8e3678: ret             
    // 0x8e367c: r1 = Null
    //     0x8e367c: mov             x1, NULL
    // 0x8e3680: r2 = 6
    //     0x8e3680: movz            x2, #0x6
    // 0x8e3684: r0 = AllocateArray()
    //     0x8e3684: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e3688: mov             x2, x0
    // 0x8e368c: r16 = "invalid bitLength ("
    //     0x8e368c: add             x16, PP, #0x19, lsl #12  ; [pp+0x193c8] "invalid bitLength ("
    //     0x8e3690: ldr             x16, [x16, #0x3c8]
    // 0x8e3694: StoreField: r2->field_f = r16
    //     0x8e3694: stur            w16, [x2, #0xf]
    // 0x8e3698: ldur            x3, [fp, #-0x10]
    // 0x8e369c: r0 = BoxInt64Instr(r3)
    //     0x8e369c: sbfiz           x0, x3, #1, #0x1f
    //     0x8e36a0: cmp             x3, x0, asr #1
    //     0x8e36a4: b.eq            #0x8e36b0
    //     0x8e36a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e36ac: stur            x3, [x0, #7]
    // 0x8e36b0: StoreField: r2->field_13 = r0
    //     0x8e36b0: stur            w0, [x2, #0x13]
    // 0x8e36b4: r16 = ") for SHA-3 must only be 224,256,384,512"
    //     0x8e36b4: add             x16, PP, #0x19, lsl #12  ; [pp+0x199a0] ") for SHA-3 must only be 224,256,384,512"
    //     0x8e36b8: ldr             x16, [x16, #0x9a0]
    // 0x8e36bc: ArrayStore: r2[0] = r16  ; List_4
    //     0x8e36bc: stur            w16, [x2, #0x17]
    // 0x8e36c0: str             x2, [SP]
    // 0x8e36c4: r0 = _interpolate()
    //     0x8e36c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8e36c8: stur            x0, [fp, #-8]
    // 0x8e36cc: r0 = StateError()
    //     0x8e36cc: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8e36d0: mov             x1, x0
    // 0x8e36d4: ldur            x0, [fp, #-8]
    // 0x8e36d8: StoreField: r1->field_b = r0
    //     0x8e36d8: stur            w0, [x1, #0xb]
    // 0x8e36dc: mov             x0, x1
    // 0x8e36e0: r0 = Throw()
    //     0x8e36e0: bl              #0xec04b8  ; ThrowStub
    // 0x8e36e4: brk             #0
    // 0x8e36e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e36e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e36ec: b               #0x8e35e8
  }
  static RegExp _sha3REGEX() {
    // ** addr: 0x8e36fc, size: 0x58
    // 0x8e36fc: EnterFrame
    //     0x8e36fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3700: mov             fp, SP
    // 0x8e3704: AllocStack(0x30)
    //     0x8e3704: sub             SP, SP, #0x30
    // 0x8e3708: CheckStackOverflow
    //     0x8e3708: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e370c: cmp             SP, x16
    //     0x8e3710: b.ls            #0x8e374c
    // 0x8e3714: r16 = "^SHA3-([0-9]+)$"
    //     0x8e3714: add             x16, PP, #0x19, lsl #12  ; [pp+0x199a8] "^SHA3-([0-9]+)$"
    //     0x8e3718: ldr             x16, [x16, #0x9a8]
    // 0x8e371c: stp             x16, NULL, [SP, #0x20]
    // 0x8e3720: r16 = false
    //     0x8e3720: add             x16, NULL, #0x30  ; false
    // 0x8e3724: r30 = true
    //     0x8e3724: add             lr, NULL, #0x20  ; true
    // 0x8e3728: stp             lr, x16, [SP, #0x10]
    // 0x8e372c: r16 = false
    //     0x8e372c: add             x16, NULL, #0x30  ; false
    // 0x8e3730: r30 = false
    //     0x8e3730: add             lr, NULL, #0x30  ; false
    // 0x8e3734: stp             lr, x16, [SP]
    // 0x8e3738: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8e3738: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8e373c: r0 = _RegExp()
    //     0x8e373c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8e3740: LeaveFrame
    //     0x8e3740: mov             SP, fp
    //     0x8e3744: ldp             fp, lr, [SP], #0x10
    // 0x8e3748: ret
    //     0x8e3748: ret             
    // 0x8e374c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e374c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3750: b               #0x8e3714
  }
}
