// lib: impl.digest.sha384, url: package:pointycastle/digests/sha384.dart

// class id: 1050955, size: 0x8
class :: {
}

// class id: 650, size: 0x54, field offset: 0x48
class SHA384Digest extends LongSHA2FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe14

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e2e2c, size: 0x58
    // 0x8e2e2c: EnterFrame
    //     0x8e2e2c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2e30: mov             fp, SP
    // 0x8e2e34: AllocStack(0x8)
    //     0x8e2e34: sub             SP, SP, #8
    // 0x8e2e38: r0 = StaticFactoryConfig()
    //     0x8e2e38: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e2e3c: mov             x3, x0
    // 0x8e2e40: r0 = "SHA-384"
    //     0x8e2e40: add             x0, PP, #0x18, lsl #12  ; [pp+0x18250] "SHA-384"
    //     0x8e2e44: ldr             x0, [x0, #0x250]
    // 0x8e2e48: stur            x3, [fp, #-8]
    // 0x8e2e4c: StoreField: r3->field_b = r0
    //     0x8e2e4c: stur            w0, [x3, #0xb]
    // 0x8e2e50: r1 = Function '<anonymous closure>': static.
    //     0x8e2e50: add             x1, PP, #0x19, lsl #12  ; [pp+0x198e0] AnonymousClosure: static (0x8e2e84), in [package:pointycastle/digests/sha384.dart] SHA384Digest::factoryConfig (0x8e2e2c)
    //     0x8e2e54: ldr             x1, [x1, #0x8e0]
    // 0x8e2e58: r2 = Null
    //     0x8e2e58: mov             x2, NULL
    // 0x8e2e5c: r0 = AllocateClosure()
    //     0x8e2e5c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e2e60: mov             x1, x0
    // 0x8e2e64: ldur            x0, [fp, #-8]
    // 0x8e2e68: StoreField: r0->field_f = r1
    //     0x8e2e68: stur            w1, [x0, #0xf]
    // 0x8e2e6c: r1 = Digest
    //     0x8e2e6c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e2e70: ldr             x1, [x1, #0x388]
    // 0x8e2e74: StoreField: r0->field_7 = r1
    //     0x8e2e74: stur            w1, [x0, #7]
    // 0x8e2e78: LeaveFrame
    //     0x8e2e78: mov             SP, fp
    //     0x8e2e7c: ldp             fp, lr, [SP], #0x10
    // 0x8e2e80: ret
    //     0x8e2e80: ret             
  }
  [closure] static SHA384Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e2e84, size: 0x60
    // 0x8e2e84: EnterFrame
    //     0x8e2e84: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2e88: mov             fp, SP
    // 0x8e2e8c: AllocStack(0x8)
    //     0x8e2e8c: sub             SP, SP, #8
    // 0x8e2e90: CheckStackOverflow
    //     0x8e2e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e2e94: cmp             SP, x16
    //     0x8e2e98: b.ls            #0x8e2edc
    // 0x8e2e9c: r0 = SHA384Digest()
    //     0x8e2e9c: bl              #0x8e2ee4  ; AllocateSHA384DigestStub -> SHA384Digest (size=0x54)
    // 0x8e2ea0: mov             x2, x0
    // 0x8e2ea4: r0 = "SHA-384"
    //     0x8e2ea4: add             x0, PP, #0x18, lsl #12  ; [pp+0x18250] "SHA-384"
    //     0x8e2ea8: ldr             x0, [x0, #0x250]
    // 0x8e2eac: stur            x2, [fp, #-8]
    // 0x8e2eb0: StoreField: r2->field_47 = r0
    //     0x8e2eb0: stur            w0, [x2, #0x47]
    // 0x8e2eb4: r0 = 48
    //     0x8e2eb4: movz            x0, #0x30
    // 0x8e2eb8: StoreField: r2->field_4b = r0
    //     0x8e2eb8: stur            x0, [x2, #0x4b]
    // 0x8e2ebc: mov             x1, x2
    // 0x8e2ec0: r0 = LongSHA2FamilyDigest()
    //     0x8e2ec0: bl              #0x8e2860  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::LongSHA2FamilyDigest
    // 0x8e2ec4: ldur            x1, [fp, #-8]
    // 0x8e2ec8: r0 = reset()
    //     0x8e2ec8: bl              #0xeb5624  ; [package:pointycastle/digests/sha384.dart] SHA384Digest::reset
    // 0x8e2ecc: ldur            x0, [fp, #-8]
    // 0x8e2ed0: LeaveFrame
    //     0x8e2ed0: mov             SP, fp
    //     0x8e2ed4: ldp             fp, lr, [SP], #0x10
    // 0x8e2ed8: ret
    //     0x8e2ed8: ret             
    // 0x8e2edc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e2edc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e2ee0: b               #0x8e2e9c
  }
  _ reset(/* No info */) {
    // ** addr: 0xeb5624, size: 0x180
    // 0xeb5624: EnterFrame
    //     0xeb5624: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5628: mov             fp, SP
    // 0xeb562c: AllocStack(0x10)
    //     0xeb562c: sub             SP, SP, #0x10
    // 0xeb5630: SetupParameters(SHA384Digest this /* r1 => r0, fp-0x8 */)
    //     0xeb5630: mov             x0, x1
    //     0xeb5634: stur            x1, [fp, #-8]
    // 0xeb5638: CheckStackOverflow
    //     0xeb5638: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb563c: cmp             SP, x16
    //     0xeb5640: b.ls            #0xeb579c
    // 0xeb5644: mov             x1, x0
    // 0xeb5648: r0 = reset()
    //     0xeb5648: bl              #0xeb57a4  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::reset
    // 0xeb564c: ldur            x0, [fp, #-8]
    // 0xeb5650: LoadField: r1 = r0->field_7
    //     0xeb5650: ldur            w1, [x0, #7]
    // 0xeb5654: DecompressPointer r1
    //     0xeb5654: add             x1, x1, HEAP, lsl #32
    // 0xeb5658: r16 = 3238371032
    //     0xeb5658: add             x16, PP, #0x19, lsl #12  ; [pp+0x198e8] 0xc1059ed8
    //     0xeb565c: ldr             x16, [x16, #0x8e8]
    // 0xeb5660: str             x16, [SP]
    // 0xeb5664: r2 = 3418070365
    //     0xeb5664: add             x2, PP, #0x19, lsl #12  ; [pp+0x198f0] 0xcbbb9d5d
    //     0xeb5668: ldr             x2, [x2, #0x8f0]
    // 0xeb566c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb566c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5670: r0 = set()
    //     0xeb5670: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5674: ldur            x0, [fp, #-8]
    // 0xeb5678: LoadField: r1 = r0->field_b
    //     0xeb5678: ldur            w1, [x0, #0xb]
    // 0xeb567c: DecompressPointer r1
    //     0xeb567c: add             x1, x1, HEAP, lsl #32
    // 0xeb5680: r16 = 1828301326
    //     0xeb5680: movz            x16, #0xaa0e
    //     0xeb5684: movk            x16, #0x6cf9, lsl #16
    // 0xeb5688: str             x16, [SP]
    // 0xeb568c: r2 = 1654270250
    //     0xeb568c: add             x2, PP, #0x19, lsl #12  ; [pp+0x198f8] 0x629a292a
    //     0xeb5690: ldr             x2, [x2, #0x8f8]
    // 0xeb5694: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb5694: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5698: r0 = set()
    //     0xeb5698: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb569c: ldur            x0, [fp, #-8]
    // 0xeb56a0: LoadField: r1 = r0->field_f
    //     0xeb56a0: ldur            w1, [x0, #0xf]
    // 0xeb56a4: DecompressPointer r1
    //     0xeb56a4: add             x1, x1, HEAP, lsl #32
    // 0xeb56a8: r16 = 1625405998
    //     0xeb56a8: movz            x16, #0xba2e
    //     0xeb56ac: movk            x16, #0x60e1, lsl #16
    // 0xeb56b0: str             x16, [SP]
    // 0xeb56b4: r2 = 2438529370
    //     0xeb56b4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19900] 0x9159015a
    //     0xeb56b8: ldr             x2, [x2, #0x900]
    // 0xeb56bc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb56bc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb56c0: r0 = set()
    //     0xeb56c0: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb56c4: ldur            x0, [fp, #-8]
    // 0xeb56c8: LoadField: r1 = r0->field_13
    //     0xeb56c8: ldur            w1, [x0, #0x13]
    // 0xeb56cc: DecompressPointer r1
    //     0xeb56cc: add             x1, x1, HEAP, lsl #32
    // 0xeb56d0: r16 = 4144912697
    //     0xeb56d0: add             x16, PP, #0x19, lsl #12  ; [pp+0x19908] 0xf70e5939
    //     0xeb56d4: ldr             x16, [x16, #0x908]
    // 0xeb56d8: str             x16, [SP]
    // 0xeb56dc: r2 = 710924720
    //     0xeb56dc: movz            x2, #0xd9b0
    //     0xeb56e0: movk            x2, #0x2a5f, lsl #16
    // 0xeb56e4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb56e4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb56e8: r0 = set()
    //     0xeb56e8: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb56ec: ldur            x0, [fp, #-8]
    // 0xeb56f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb56f0: ldur            w1, [x0, #0x17]
    // 0xeb56f4: DecompressPointer r1
    //     0xeb56f4: add             x1, x1, HEAP, lsl #32
    // 0xeb56f8: r16 = 4290775857
    //     0xeb56f8: add             x16, PP, #0x19, lsl #12  ; [pp+0x19910] 0xffc00b31
    //     0xeb56fc: ldr             x16, [x16, #0x910]
    // 0xeb5700: str             x16, [SP]
    // 0xeb5704: r2 = 1731405415
    //     0xeb5704: add             x2, PP, #0x19, lsl #12  ; [pp+0x19918] 0x67332667
    //     0xeb5708: ldr             x2, [x2, #0x918]
    // 0xeb570c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb570c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5710: r0 = set()
    //     0xeb5710: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5714: ldur            x0, [fp, #-8]
    // 0xeb5718: LoadField: r1 = r0->field_1b
    //     0xeb5718: ldur            w1, [x0, #0x1b]
    // 0xeb571c: DecompressPointer r1
    //     0xeb571c: add             x1, x1, HEAP, lsl #32
    // 0xeb5720: r16 = 1750603025
    //     0xeb5720: add             x16, PP, #0x19, lsl #12  ; [pp+0x19920] 0x68581511
    //     0xeb5724: ldr             x16, [x16, #0x920]
    // 0xeb5728: str             x16, [SP]
    // 0xeb572c: r2 = 2394180231
    //     0xeb572c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19928] 0x8eb44a87
    //     0xeb5730: ldr             x2, [x2, #0x928]
    // 0xeb5734: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb5734: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5738: r0 = set()
    //     0xeb5738: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb573c: ldur            x0, [fp, #-8]
    // 0xeb5740: LoadField: r1 = r0->field_1f
    //     0xeb5740: ldur            w1, [x0, #0x1f]
    // 0xeb5744: DecompressPointer r1
    //     0xeb5744: add             x1, x1, HEAP, lsl #32
    // 0xeb5748: r16 = 1694076839
    //     0xeb5748: add             x16, PP, #0x19, lsl #12  ; [pp+0x19930] 0x64f98fa7
    //     0xeb574c: ldr             x16, [x16, #0x930]
    // 0xeb5750: str             x16, [SP]
    // 0xeb5754: r2 = 3675008525
    //     0xeb5754: add             x2, PP, #0x19, lsl #12  ; [pp+0x19938] 0xdb0c2e0d
    //     0xeb5758: ldr             x2, [x2, #0x938]
    // 0xeb575c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb575c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5760: r0 = set()
    //     0xeb5760: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5764: ldur            x0, [fp, #-8]
    // 0xeb5768: LoadField: r1 = r0->field_23
    //     0xeb5768: ldur            w1, [x0, #0x23]
    // 0xeb576c: DecompressPointer r1
    //     0xeb576c: add             x1, x1, HEAP, lsl #32
    // 0xeb5770: r16 = 3204075428
    //     0xeb5770: add             x16, PP, #0x19, lsl #12  ; [pp+0x19940] 0xbefa4fa4
    //     0xeb5774: ldr             x16, [x16, #0x940]
    // 0xeb5778: str             x16, [SP]
    // 0xeb577c: r2 = 1203062813
    //     0xeb577c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19948] 0x47b5481d
    //     0xeb5780: ldr             x2, [x2, #0x948]
    // 0xeb5784: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb5784: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5788: r0 = set()
    //     0xeb5788: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb578c: r0 = Null
    //     0xeb578c: mov             x0, NULL
    // 0xeb5790: LeaveFrame
    //     0xeb5790: mov             SP, fp
    //     0xeb5794: ldp             fp, lr, [SP], #0x10
    // 0xeb5798: ret
    //     0xeb5798: ret             
    // 0xeb579c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb579c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb57a0: b               #0xeb5644
  }
}
