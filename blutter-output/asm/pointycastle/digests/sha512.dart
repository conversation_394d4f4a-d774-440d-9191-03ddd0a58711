// lib: impl.digest.sha512, url: package:pointycastle/digests/sha512.dart

// class id: 1050956, size: 0x8
class :: {
}

// class id: 649, size: 0x54, field offset: 0x48
class SHA512Digest extends LongSHA2FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe20

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8e2d68, size: 0x58
    // 0x8e2d68: EnterFrame
    //     0x8e2d68: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2d6c: mov             fp, SP
    // 0x8e2d70: AllocStack(0x8)
    //     0x8e2d70: sub             SP, SP, #8
    // 0x8e2d74: r0 = StaticFactoryConfig()
    //     0x8e2d74: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8e2d78: mov             x3, x0
    // 0x8e2d7c: r0 = "SHA-512"
    //     0x8e2d7c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18260] "SHA-512"
    //     0x8e2d80: ldr             x0, [x0, #0x260]
    // 0x8e2d84: stur            x3, [fp, #-8]
    // 0x8e2d88: StoreField: r3->field_b = r0
    //     0x8e2d88: stur            w0, [x3, #0xb]
    // 0x8e2d8c: r1 = Function '<anonymous closure>': static.
    //     0x8e2d8c: add             x1, PP, #0x19, lsl #12  ; [pp+0x198d8] AnonymousClosure: static (0x8e2dc0), in [package:pointycastle/digests/sha512.dart] SHA512Digest::factoryConfig (0x8e2d68)
    //     0x8e2d90: ldr             x1, [x1, #0x8d8]
    // 0x8e2d94: r2 = Null
    //     0x8e2d94: mov             x2, NULL
    // 0x8e2d98: r0 = AllocateClosure()
    //     0x8e2d98: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e2d9c: mov             x1, x0
    // 0x8e2da0: ldur            x0, [fp, #-8]
    // 0x8e2da4: StoreField: r0->field_f = r1
    //     0x8e2da4: stur            w1, [x0, #0xf]
    // 0x8e2da8: r1 = Digest
    //     0x8e2da8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8e2dac: ldr             x1, [x1, #0x388]
    // 0x8e2db0: StoreField: r0->field_7 = r1
    //     0x8e2db0: stur            w1, [x0, #7]
    // 0x8e2db4: LeaveFrame
    //     0x8e2db4: mov             SP, fp
    //     0x8e2db8: ldp             fp, lr, [SP], #0x10
    // 0x8e2dbc: ret
    //     0x8e2dbc: ret             
  }
  [closure] static SHA512Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8e2dc0, size: 0x60
    // 0x8e2dc0: EnterFrame
    //     0x8e2dc0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2dc4: mov             fp, SP
    // 0x8e2dc8: AllocStack(0x8)
    //     0x8e2dc8: sub             SP, SP, #8
    // 0x8e2dcc: CheckStackOverflow
    //     0x8e2dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e2dd0: cmp             SP, x16
    //     0x8e2dd4: b.ls            #0x8e2e18
    // 0x8e2dd8: r0 = SHA512Digest()
    //     0x8e2dd8: bl              #0x8e2e20  ; AllocateSHA512DigestStub -> SHA512Digest (size=0x54)
    // 0x8e2ddc: mov             x2, x0
    // 0x8e2de0: r0 = "SHA-512"
    //     0x8e2de0: add             x0, PP, #0x18, lsl #12  ; [pp+0x18260] "SHA-512"
    //     0x8e2de4: ldr             x0, [x0, #0x260]
    // 0x8e2de8: stur            x2, [fp, #-8]
    // 0x8e2dec: StoreField: r2->field_47 = r0
    //     0x8e2dec: stur            w0, [x2, #0x47]
    // 0x8e2df0: r0 = 64
    //     0x8e2df0: movz            x0, #0x40
    // 0x8e2df4: StoreField: r2->field_4b = r0
    //     0x8e2df4: stur            x0, [x2, #0x4b]
    // 0x8e2df8: mov             x1, x2
    // 0x8e2dfc: r0 = LongSHA2FamilyDigest()
    //     0x8e2dfc: bl              #0x8e2860  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::LongSHA2FamilyDigest
    // 0x8e2e00: ldur            x1, [fp, #-8]
    // 0x8e2e04: r0 = reset()
    //     0x8e2e04: bl              #0xeb5854  ; [package:pointycastle/digests/sha512.dart] SHA512Digest::reset
    // 0x8e2e08: ldur            x0, [fp, #-8]
    // 0x8e2e0c: LeaveFrame
    //     0x8e2e0c: mov             SP, fp
    //     0x8e2e10: ldp             fp, lr, [SP], #0x10
    // 0x8e2e14: ret
    //     0x8e2e14: ret             
    // 0x8e2e18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e2e18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e2e1c: b               #0x8e2dd8
  }
  const get _ digestSize(/* No info */) {
    // ** addr: 0xe61e08, size: 0x8
    // 0xe61e08: LoadField: r0 = r1->field_4b
    //     0xe61e08: ldur            x0, [x1, #0x4b]
    // 0xe61e0c: ret
    //     0xe61e0c: ret             
  }
  _ reset(/* No info */) {
    // ** addr: 0xeb5854, size: 0x180
    // 0xeb5854: EnterFrame
    //     0xeb5854: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5858: mov             fp, SP
    // 0xeb585c: AllocStack(0x10)
    //     0xeb585c: sub             SP, SP, #0x10
    // 0xeb5860: SetupParameters(SHA512Digest this /* r1 => r0, fp-0x8 */)
    //     0xeb5860: mov             x0, x1
    //     0xeb5864: stur            x1, [fp, #-8]
    // 0xeb5868: CheckStackOverflow
    //     0xeb5868: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb586c: cmp             SP, x16
    //     0xeb5870: b.ls            #0xeb59cc
    // 0xeb5874: mov             x1, x0
    // 0xeb5878: r0 = reset()
    //     0xeb5878: bl              #0xeb57a4  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::reset
    // 0xeb587c: ldur            x0, [fp, #-8]
    // 0xeb5880: LoadField: r1 = r0->field_7
    //     0xeb5880: ldur            w1, [x0, #7]
    // 0xeb5884: DecompressPointer r1
    //     0xeb5884: add             x1, x1, HEAP, lsl #32
    // 0xeb5888: r16 = 4089235720
    //     0xeb5888: add             x16, PP, #0x19, lsl #12  ; [pp+0x19480] 0xf3bcc908
    //     0xeb588c: ldr             x16, [x16, #0x480]
    // 0xeb5890: str             x16, [SP]
    // 0xeb5894: r2 = 1779033703
    //     0xeb5894: add             x2, PP, #0x12, lsl #12  ; [pp+0x12360] 0x6a09e667
    //     0xeb5898: ldr             x2, [x2, #0x360]
    // 0xeb589c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb589c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb58a0: r0 = set()
    //     0xeb58a0: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb58a4: ldur            x0, [fp, #-8]
    // 0xeb58a8: LoadField: r1 = r0->field_b
    //     0xeb58a8: ldur            w1, [x0, #0xb]
    // 0xeb58ac: DecompressPointer r1
    //     0xeb58ac: add             x1, x1, HEAP, lsl #32
    // 0xeb58b0: r16 = 2227873595
    //     0xeb58b0: add             x16, PP, #0x19, lsl #12  ; [pp+0x19490] 0x84caa73b
    //     0xeb58b4: ldr             x16, [x16, #0x490]
    // 0xeb58b8: str             x16, [SP]
    // 0xeb58bc: r2 = 3144134277
    //     0xeb58bc: add             x2, PP, #0x12, lsl #12  ; [pp+0x12368] 0xbb67ae85
    //     0xeb58c0: ldr             x2, [x2, #0x368]
    // 0xeb58c4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb58c4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb58c8: r0 = set()
    //     0xeb58c8: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb58cc: ldur            x0, [fp, #-8]
    // 0xeb58d0: LoadField: r1 = r0->field_f
    //     0xeb58d0: ldur            w1, [x0, #0xf]
    // 0xeb58d4: DecompressPointer r1
    //     0xeb58d4: add             x1, x1, HEAP, lsl #32
    // 0xeb58d8: r16 = 4271175723
    //     0xeb58d8: add             x16, PP, #0x19, lsl #12  ; [pp+0x19498] 0xfe94f82b
    //     0xeb58dc: ldr             x16, [x16, #0x498]
    // 0xeb58e0: str             x16, [SP]
    // 0xeb58e4: r2 = 2027808484
    //     0xeb58e4: movz            x2, #0xe6e4
    //     0xeb58e8: movk            x2, #0x78dd, lsl #16
    // 0xeb58ec: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb58ec: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb58f0: r0 = set()
    //     0xeb58f0: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb58f4: ldur            x0, [fp, #-8]
    // 0xeb58f8: LoadField: r1 = r0->field_13
    //     0xeb58f8: ldur            w1, [x0, #0x13]
    // 0xeb58fc: DecompressPointer r1
    //     0xeb58fc: add             x1, x1, HEAP, lsl #32
    // 0xeb5900: r16 = 1595750129
    //     0xeb5900: add             x16, PP, #0x19, lsl #12  ; [pp+0x194a0] 0x5f1d36f1
    //     0xeb5904: ldr             x16, [x16, #0x4a0]
    // 0xeb5908: str             x16, [SP]
    // 0xeb590c: r2 = 2773480762
    //     0xeb590c: add             x2, PP, #0x12, lsl #12  ; [pp+0x12370] 0xa54ff53a
    //     0xeb5910: ldr             x2, [x2, #0x370]
    // 0xeb5914: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb5914: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5918: r0 = set()
    //     0xeb5918: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb591c: ldur            x0, [fp, #-8]
    // 0xeb5920: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb5920: ldur            w1, [x0, #0x17]
    // 0xeb5924: DecompressPointer r1
    //     0xeb5924: add             x1, x1, HEAP, lsl #32
    // 0xeb5928: r16 = 2917565137
    //     0xeb5928: add             x16, PP, #0x19, lsl #12  ; [pp+0x194a8] 0xade682d1
    //     0xeb592c: ldr             x16, [x16, #0x4a8]
    // 0xeb5930: str             x16, [SP]
    // 0xeb5934: r2 = 1359893119
    //     0xeb5934: add             x2, PP, #0x12, lsl #12  ; [pp+0x12378] 0x510e527f
    //     0xeb5938: ldr             x2, [x2, #0x378]
    // 0xeb593c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb593c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5940: r0 = set()
    //     0xeb5940: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5944: ldur            x0, [fp, #-8]
    // 0xeb5948: LoadField: r1 = r0->field_1b
    //     0xeb5948: ldur            w1, [x0, #0x1b]
    // 0xeb594c: DecompressPointer r1
    //     0xeb594c: add             x1, x1, HEAP, lsl #32
    // 0xeb5950: r16 = 1451022398
    //     0xeb5950: movz            x16, #0xd83e
    //     0xeb5954: movk            x16, #0x567c, lsl #16
    // 0xeb5958: str             x16, [SP]
    // 0xeb595c: r2 = 2600822924
    //     0xeb595c: add             x2, PP, #0x12, lsl #12  ; [pp+0x12380] 0x9b05688c
    //     0xeb5960: ldr             x2, [x2, #0x380]
    // 0xeb5964: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb5964: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5968: r0 = set()
    //     0xeb5968: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb596c: ldur            x0, [fp, #-8]
    // 0xeb5970: LoadField: r1 = r0->field_1f
    //     0xeb5970: ldur            w1, [x0, #0x1f]
    // 0xeb5974: DecompressPointer r1
    //     0xeb5974: add             x1, x1, HEAP, lsl #32
    // 0xeb5978: r16 = 4215389547
    //     0xeb5978: add             x16, PP, #0x19, lsl #12  ; [pp+0x194b0] 0xfb41bd6b
    //     0xeb597c: ldr             x16, [x16, #0x4b0]
    // 0xeb5980: str             x16, [SP]
    // 0xeb5984: r2 = 1057469270
    //     0xeb5984: movz            x2, #0xb356
    //     0xeb5988: movk            x2, #0x3f07, lsl #16
    // 0xeb598c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb598c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb5990: r0 = set()
    //     0xeb5990: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5994: ldur            x0, [fp, #-8]
    // 0xeb5998: LoadField: r1 = r0->field_23
    //     0xeb5998: ldur            w1, [x0, #0x23]
    // 0xeb599c: DecompressPointer r1
    //     0xeb599c: add             x1, x1, HEAP, lsl #32
    // 0xeb59a0: r16 = 654066418
    //     0xeb59a0: movz            x16, #0x42f2
    //     0xeb59a4: movk            x16, #0x26fc, lsl #16
    // 0xeb59a8: str             x16, [SP]
    // 0xeb59ac: r2 = 1541459225
    //     0xeb59ac: add             x2, PP, #0x12, lsl #12  ; [pp+0x12388] 0x5be0cd19
    //     0xeb59b0: ldr             x2, [x2, #0x388]
    // 0xeb59b4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb59b4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb59b8: r0 = set()
    //     0xeb59b8: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb59bc: r0 = Null
    //     0xeb59bc: mov             x0, NULL
    // 0xeb59c0: LeaveFrame
    //     0xeb59c0: mov             SP, fp
    //     0xeb59c4: ldp             fp, lr, [SP], #0x10
    // 0xeb59c8: ret
    //     0xeb59c8: ret             
    // 0xeb59cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb59cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb59d0: b               #0xeb5874
  }
}
