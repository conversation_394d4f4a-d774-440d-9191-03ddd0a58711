// lib: impl.digest.sha512t, url: package:pointycastle/digests/sha512t.dart

// class id: 1050957, size: 0x8
class :: {
}

// class id: 648, size: 0x70, field offset: 0x48
class SHA512tDigest extends LongSHA2FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe28
  static late final RegExp _nameRegex; // offset: 0xe24
  static late final Register64 _hMask; // offset: 0xe2c

  get _ algorithmName(/* No info */) {
    // ** addr: 0x869cf0, size: 0x7c
    // 0x869cf0: EnterFrame
    //     0x869cf0: stp             fp, lr, [SP, #-0x10]!
    //     0x869cf4: mov             fp, SP
    // 0x869cf8: AllocStack(0x10)
    //     0x869cf8: sub             SP, SP, #0x10
    // 0x869cfc: SetupParameters(SHA512tDigest this /* r1 => r0, fp-0x8 */)
    //     0x869cfc: mov             x0, x1
    //     0x869d00: stur            x1, [fp, #-8]
    // 0x869d04: CheckStackOverflow
    //     0x869d04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x869d08: cmp             SP, x16
    //     0x869d0c: b.ls            #0x869d64
    // 0x869d10: r1 = Null
    //     0x869d10: mov             x1, NULL
    // 0x869d14: r2 = 4
    //     0x869d14: movz            x2, #0x4
    // 0x869d18: r0 = AllocateArray()
    //     0x869d18: bl              #0xec22fc  ; AllocateArrayStub
    // 0x869d1c: mov             x2, x0
    // 0x869d20: r16 = "SHA-512/"
    //     0x869d20: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ca0] "SHA-512/"
    //     0x869d24: ldr             x16, [x16, #0xca0]
    // 0x869d28: StoreField: r2->field_f = r16
    //     0x869d28: stur            w16, [x2, #0xf]
    // 0x869d2c: ldur            x0, [fp, #-8]
    // 0x869d30: LoadField: r1 = r0->field_47
    //     0x869d30: ldur            x1, [x0, #0x47]
    // 0x869d34: lsl             x3, x1, #3
    // 0x869d38: r0 = BoxInt64Instr(r3)
    //     0x869d38: sbfiz           x0, x3, #1, #0x1f
    //     0x869d3c: cmp             x3, x0, asr #1
    //     0x869d40: b.eq            #0x869d4c
    //     0x869d44: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x869d48: stur            x3, [x0, #7]
    // 0x869d4c: StoreField: r2->field_13 = r0
    //     0x869d4c: stur            w0, [x2, #0x13]
    // 0x869d50: str             x2, [SP]
    // 0x869d54: r0 = _interpolate()
    //     0x869d54: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x869d58: LeaveFrame
    //     0x869d58: mov             SP, fp
    //     0x869d5c: ldp             fp, lr, [SP], #0x10
    // 0x869d60: ret
    //     0x869d60: ret             
    // 0x869d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x869d64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x869d68: b               #0x869d10
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d7744, size: 0x8c
    // 0x8d7744: EnterFrame
    //     0x8d7744: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7748: mov             fp, SP
    // 0x8d774c: AllocStack(0x10)
    //     0x8d774c: sub             SP, SP, #0x10
    // 0x8d7750: CheckStackOverflow
    //     0x8d7750: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7754: cmp             SP, x16
    //     0x8d7758: b.ls            #0x8d77c8
    // 0x8d775c: r0 = InitLateStaticField(0xe24) // [package:pointycastle/digests/sha512t.dart] SHA512tDigest::_nameRegex
    //     0x8d775c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d7760: ldr             x0, [x0, #0x1c48]
    //     0x8d7764: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d7768: cmp             w0, w16
    //     0x8d776c: b.ne            #0x8d777c
    //     0x8d7770: add             x2, PP, #0x19, lsl #12  ; [pp+0x19450] Field <SHA512tDigest._nameRegex@935210196>: static late final (offset: 0xe24)
    //     0x8d7774: ldr             x2, [x2, #0x450]
    //     0x8d7778: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d777c: stur            x0, [fp, #-8]
    // 0x8d7780: r0 = DynamicFactoryConfig()
    //     0x8d7780: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8d7784: mov             x3, x0
    // 0x8d7788: ldur            x0, [fp, #-8]
    // 0x8d778c: stur            x3, [fp, #-0x10]
    // 0x8d7790: StoreField: r3->field_b = r0
    //     0x8d7790: stur            w0, [x3, #0xb]
    // 0x8d7794: r1 = Function '<anonymous closure>': static.
    //     0x8d7794: add             x1, PP, #0x19, lsl #12  ; [pp+0x19458] AnonymousClosure: static (0x8d77d0), in [package:pointycastle/digests/sha512t.dart] SHA512tDigest::factoryConfig (0x8d7744)
    //     0x8d7798: ldr             x1, [x1, #0x458]
    // 0x8d779c: r2 = Null
    //     0x8d779c: mov             x2, NULL
    // 0x8d77a0: r0 = AllocateClosure()
    //     0x8d77a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d77a4: mov             x1, x0
    // 0x8d77a8: ldur            x0, [fp, #-0x10]
    // 0x8d77ac: StoreField: r0->field_f = r1
    //     0x8d77ac: stur            w1, [x0, #0xf]
    // 0x8d77b0: r1 = Digest
    //     0x8d77b0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8d77b4: ldr             x1, [x1, #0x388]
    // 0x8d77b8: StoreField: r0->field_7 = r1
    //     0x8d77b8: stur            w1, [x0, #7]
    // 0x8d77bc: LeaveFrame
    //     0x8d77bc: mov             SP, fp
    //     0x8d77c0: ldp             fp, lr, [SP], #0x10
    // 0x8d77c4: ret
    //     0x8d77c4: ret             
    // 0x8d77c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d77c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d77cc: b               #0x8d775c
  }
  [closure] static (dynamic) => SHA512tDigest <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8d77d0, size: 0x54
    // 0x8d77d0: EnterFrame
    //     0x8d77d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d77d4: mov             fp, SP
    // 0x8d77d8: AllocStack(0x8)
    //     0x8d77d8: sub             SP, SP, #8
    // 0x8d77dc: SetupParameters()
    //     0x8d77dc: ldr             x0, [fp, #0x20]
    //     0x8d77e0: ldur            w1, [x0, #0x17]
    //     0x8d77e4: add             x1, x1, HEAP, lsl #32
    //     0x8d77e8: stur            x1, [fp, #-8]
    // 0x8d77ec: r1 = 1
    //     0x8d77ec: movz            x1, #0x1
    // 0x8d77f0: r0 = AllocateContext()
    //     0x8d77f0: bl              #0xec126c  ; AllocateContextStub
    // 0x8d77f4: mov             x1, x0
    // 0x8d77f8: ldur            x0, [fp, #-8]
    // 0x8d77fc: StoreField: r1->field_b = r0
    //     0x8d77fc: stur            w0, [x1, #0xb]
    // 0x8d7800: ldr             x0, [fp, #0x10]
    // 0x8d7804: StoreField: r1->field_f = r0
    //     0x8d7804: stur            w0, [x1, #0xf]
    // 0x8d7808: mov             x2, x1
    // 0x8d780c: r1 = Function '<anonymous closure>': static.
    //     0x8d780c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19460] AnonymousClosure: static (0x8d7824), in [package:pointycastle/digests/sha512t.dart] SHA512tDigest::factoryConfig (0x8d7744)
    //     0x8d7810: ldr             x1, [x1, #0x460]
    // 0x8d7814: r0 = AllocateClosure()
    //     0x8d7814: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d7818: LeaveFrame
    //     0x8d7818: mov             SP, fp
    //     0x8d781c: ldp             fp, lr, [SP], #0x10
    // 0x8d7820: ret
    //     0x8d7820: ret             
  }
  [closure] static SHA512tDigest <anonymous closure>(dynamic) {
    // ** addr: 0x8d7824, size: 0x10c
    // 0x8d7824: EnterFrame
    //     0x8d7824: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7828: mov             fp, SP
    // 0x8d782c: AllocStack(0x20)
    //     0x8d782c: sub             SP, SP, #0x20
    // 0x8d7830: SetupParameters()
    //     0x8d7830: ldr             x0, [fp, #0x10]
    //     0x8d7834: ldur            w1, [x0, #0x17]
    //     0x8d7838: add             x1, x1, HEAP, lsl #32
    // 0x8d783c: CheckStackOverflow
    //     0x8d783c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7840: cmp             SP, x16
    //     0x8d7844: b.ls            #0x8d7924
    // 0x8d7848: LoadField: r0 = r1->field_f
    //     0x8d7848: ldur            w0, [x1, #0xf]
    // 0x8d784c: DecompressPointer r0
    //     0x8d784c: add             x0, x0, HEAP, lsl #32
    // 0x8d7850: r1 = LoadClassIdInstr(r0)
    //     0x8d7850: ldur            x1, [x0, #-1]
    //     0x8d7854: ubfx            x1, x1, #0xc, #0x14
    // 0x8d7858: mov             x16, x0
    // 0x8d785c: mov             x0, x1
    // 0x8d7860: mov             x1, x16
    // 0x8d7864: r2 = 1
    //     0x8d7864: movz            x2, #0x1
    // 0x8d7868: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8d7868: sub             lr, x0, #0xfdd
    //     0x8d786c: ldr             lr, [x21, lr, lsl #3]
    //     0x8d7870: blr             lr
    // 0x8d7874: cmp             w0, NULL
    // 0x8d7878: b.eq            #0x8d792c
    // 0x8d787c: mov             x1, x0
    // 0x8d7880: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8d7880: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8d7884: r0 = parse()
    //     0x8d7884: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8d7888: stur            x0, [fp, #-0x18]
    // 0x8d788c: tst             x0, #7
    // 0x8d7890: b.ne            #0x8d78c4
    // 0x8d7894: r1 = 8
    //     0x8d7894: movz            x1, #0x8
    // 0x8d7898: sdiv            x2, x0, x1
    // 0x8d789c: stur            x2, [fp, #-8]
    // 0x8d78a0: r0 = SHA512tDigest()
    //     0x8d78a0: bl              #0x8e2d04  ; AllocateSHA512tDigestStub -> SHA512tDigest (size=0x70)
    // 0x8d78a4: mov             x1, x0
    // 0x8d78a8: ldur            x2, [fp, #-8]
    // 0x8d78ac: stur            x0, [fp, #-0x10]
    // 0x8d78b0: r0 = SHA512tDigest()
    //     0x8d78b0: bl              #0x8d7930  ; [package:pointycastle/digests/sha512t.dart] SHA512tDigest::SHA512tDigest
    // 0x8d78b4: ldur            x0, [fp, #-0x10]
    // 0x8d78b8: LeaveFrame
    //     0x8d78b8: mov             SP, fp
    //     0x8d78bc: ldp             fp, lr, [SP], #0x10
    // 0x8d78c0: ret
    //     0x8d78c0: ret             
    // 0x8d78c4: r1 = Null
    //     0x8d78c4: mov             x1, NULL
    // 0x8d78c8: r2 = 4
    //     0x8d78c8: movz            x2, #0x4
    // 0x8d78cc: r0 = AllocateArray()
    //     0x8d78cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8d78d0: mov             x2, x0
    // 0x8d78d4: r16 = "Digest length for SHA-512/t is not a multiple of 8: "
    //     0x8d78d4: add             x16, PP, #0x19, lsl #12  ; [pp+0x19468] "Digest length for SHA-512/t is not a multiple of 8: "
    //     0x8d78d8: ldr             x16, [x16, #0x468]
    // 0x8d78dc: StoreField: r2->field_f = r16
    //     0x8d78dc: stur            w16, [x2, #0xf]
    // 0x8d78e0: ldur            x3, [fp, #-0x18]
    // 0x8d78e4: r0 = BoxInt64Instr(r3)
    //     0x8d78e4: sbfiz           x0, x3, #1, #0x1f
    //     0x8d78e8: cmp             x3, x0, asr #1
    //     0x8d78ec: b.eq            #0x8d78f8
    //     0x8d78f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d78f4: stur            x3, [x0, #7]
    // 0x8d78f8: StoreField: r2->field_13 = r0
    //     0x8d78f8: stur            w0, [x2, #0x13]
    // 0x8d78fc: str             x2, [SP]
    // 0x8d7900: r0 = _interpolate()
    //     0x8d7900: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8d7904: stur            x0, [fp, #-0x10]
    // 0x8d7908: r0 = RegistryFactoryException()
    //     0x8d7908: bl              #0x8c37d8  ; AllocateRegistryFactoryExceptionStub -> RegistryFactoryException (size=0xc)
    // 0x8d790c: mov             x1, x0
    // 0x8d7910: ldur            x0, [fp, #-0x10]
    // 0x8d7914: StoreField: r1->field_7 = r0
    //     0x8d7914: stur            w0, [x1, #7]
    // 0x8d7918: mov             x0, x1
    // 0x8d791c: r0 = Throw()
    //     0x8d791c: bl              #0xec04b8  ; ThrowStub
    // 0x8d7920: brk             #0
    // 0x8d7924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7928: b               #0x8d7848
    // 0x8d792c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8d792c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ SHA512tDigest(/* No info */) {
    // ** addr: 0x8d7930, size: 0x344
    // 0x8d7930: EnterFrame
    //     0x8d7930: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7934: mov             fp, SP
    // 0x8d7938: AllocStack(0x20)
    //     0x8d7938: sub             SP, SP, #0x20
    // 0x8d793c: SetupParameters(SHA512tDigest this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8d793c: stur            x1, [fp, #-8]
    //     0x8d7940: stur            x2, [fp, #-0x10]
    // 0x8d7944: CheckStackOverflow
    //     0x8d7944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7948: cmp             SP, x16
    //     0x8d794c: b.ls            #0x8d7c6c
    // 0x8d7950: r0 = Register64()
    //     0x8d7950: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d7954: mov             x3, x0
    // 0x8d7958: r0 = Sentinel
    //     0x8d7958: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d795c: stur            x3, [fp, #-0x18]
    // 0x8d7960: StoreField: r3->field_7 = r0
    //     0x8d7960: stur            w0, [x3, #7]
    // 0x8d7964: StoreField: r3->field_b = r0
    //     0x8d7964: stur            w0, [x3, #0xb]
    // 0x8d7968: str             NULL, [SP]
    // 0x8d796c: mov             x1, x3
    // 0x8d7970: r2 = 0
    //     0x8d7970: movz            x2, #0
    // 0x8d7974: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7974: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7978: r0 = set()
    //     0x8d7978: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d797c: ldur            x0, [fp, #-0x18]
    // 0x8d7980: ldur            x1, [fp, #-8]
    // 0x8d7984: StoreField: r1->field_4f = r0
    //     0x8d7984: stur            w0, [x1, #0x4f]
    //     0x8d7988: ldurb           w16, [x1, #-1]
    //     0x8d798c: ldurb           w17, [x0, #-1]
    //     0x8d7990: and             x16, x17, x16, lsr #2
    //     0x8d7994: tst             x16, HEAP, lsr #32
    //     0x8d7998: b.eq            #0x8d79a0
    //     0x8d799c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d79a0: r0 = Register64()
    //     0x8d79a0: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d79a4: mov             x3, x0
    // 0x8d79a8: r0 = Sentinel
    //     0x8d79a8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d79ac: stur            x3, [fp, #-0x18]
    // 0x8d79b0: StoreField: r3->field_7 = r0
    //     0x8d79b0: stur            w0, [x3, #7]
    // 0x8d79b4: StoreField: r3->field_b = r0
    //     0x8d79b4: stur            w0, [x3, #0xb]
    // 0x8d79b8: str             NULL, [SP]
    // 0x8d79bc: mov             x1, x3
    // 0x8d79c0: r2 = 0
    //     0x8d79c0: movz            x2, #0
    // 0x8d79c4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d79c4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d79c8: r0 = set()
    //     0x8d79c8: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d79cc: ldur            x0, [fp, #-0x18]
    // 0x8d79d0: ldur            x1, [fp, #-8]
    // 0x8d79d4: StoreField: r1->field_53 = r0
    //     0x8d79d4: stur            w0, [x1, #0x53]
    //     0x8d79d8: ldurb           w16, [x1, #-1]
    //     0x8d79dc: ldurb           w17, [x0, #-1]
    //     0x8d79e0: and             x16, x17, x16, lsr #2
    //     0x8d79e4: tst             x16, HEAP, lsr #32
    //     0x8d79e8: b.eq            #0x8d79f0
    //     0x8d79ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d79f0: r0 = Register64()
    //     0x8d79f0: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d79f4: mov             x3, x0
    // 0x8d79f8: r0 = Sentinel
    //     0x8d79f8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d79fc: stur            x3, [fp, #-0x18]
    // 0x8d7a00: StoreField: r3->field_7 = r0
    //     0x8d7a00: stur            w0, [x3, #7]
    // 0x8d7a04: StoreField: r3->field_b = r0
    //     0x8d7a04: stur            w0, [x3, #0xb]
    // 0x8d7a08: str             NULL, [SP]
    // 0x8d7a0c: mov             x1, x3
    // 0x8d7a10: r2 = 0
    //     0x8d7a10: movz            x2, #0
    // 0x8d7a14: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7a14: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7a18: r0 = set()
    //     0x8d7a18: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7a1c: ldur            x0, [fp, #-0x18]
    // 0x8d7a20: ldur            x1, [fp, #-8]
    // 0x8d7a24: StoreField: r1->field_57 = r0
    //     0x8d7a24: stur            w0, [x1, #0x57]
    //     0x8d7a28: ldurb           w16, [x1, #-1]
    //     0x8d7a2c: ldurb           w17, [x0, #-1]
    //     0x8d7a30: and             x16, x17, x16, lsr #2
    //     0x8d7a34: tst             x16, HEAP, lsr #32
    //     0x8d7a38: b.eq            #0x8d7a40
    //     0x8d7a3c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d7a40: r0 = Register64()
    //     0x8d7a40: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d7a44: mov             x3, x0
    // 0x8d7a48: r0 = Sentinel
    //     0x8d7a48: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d7a4c: stur            x3, [fp, #-0x18]
    // 0x8d7a50: StoreField: r3->field_7 = r0
    //     0x8d7a50: stur            w0, [x3, #7]
    // 0x8d7a54: StoreField: r3->field_b = r0
    //     0x8d7a54: stur            w0, [x3, #0xb]
    // 0x8d7a58: str             NULL, [SP]
    // 0x8d7a5c: mov             x1, x3
    // 0x8d7a60: r2 = 0
    //     0x8d7a60: movz            x2, #0
    // 0x8d7a64: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7a64: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7a68: r0 = set()
    //     0x8d7a68: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7a6c: ldur            x0, [fp, #-0x18]
    // 0x8d7a70: ldur            x1, [fp, #-8]
    // 0x8d7a74: StoreField: r1->field_5b = r0
    //     0x8d7a74: stur            w0, [x1, #0x5b]
    //     0x8d7a78: ldurb           w16, [x1, #-1]
    //     0x8d7a7c: ldurb           w17, [x0, #-1]
    //     0x8d7a80: and             x16, x17, x16, lsr #2
    //     0x8d7a84: tst             x16, HEAP, lsr #32
    //     0x8d7a88: b.eq            #0x8d7a90
    //     0x8d7a8c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d7a90: r0 = Register64()
    //     0x8d7a90: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d7a94: mov             x3, x0
    // 0x8d7a98: r0 = Sentinel
    //     0x8d7a98: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d7a9c: stur            x3, [fp, #-0x18]
    // 0x8d7aa0: StoreField: r3->field_7 = r0
    //     0x8d7aa0: stur            w0, [x3, #7]
    // 0x8d7aa4: StoreField: r3->field_b = r0
    //     0x8d7aa4: stur            w0, [x3, #0xb]
    // 0x8d7aa8: str             NULL, [SP]
    // 0x8d7aac: mov             x1, x3
    // 0x8d7ab0: r2 = 0
    //     0x8d7ab0: movz            x2, #0
    // 0x8d7ab4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7ab4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7ab8: r0 = set()
    //     0x8d7ab8: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7abc: ldur            x0, [fp, #-0x18]
    // 0x8d7ac0: ldur            x1, [fp, #-8]
    // 0x8d7ac4: StoreField: r1->field_5f = r0
    //     0x8d7ac4: stur            w0, [x1, #0x5f]
    //     0x8d7ac8: ldurb           w16, [x1, #-1]
    //     0x8d7acc: ldurb           w17, [x0, #-1]
    //     0x8d7ad0: and             x16, x17, x16, lsr #2
    //     0x8d7ad4: tst             x16, HEAP, lsr #32
    //     0x8d7ad8: b.eq            #0x8d7ae0
    //     0x8d7adc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d7ae0: r0 = Register64()
    //     0x8d7ae0: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d7ae4: mov             x3, x0
    // 0x8d7ae8: r0 = Sentinel
    //     0x8d7ae8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d7aec: stur            x3, [fp, #-0x18]
    // 0x8d7af0: StoreField: r3->field_7 = r0
    //     0x8d7af0: stur            w0, [x3, #7]
    // 0x8d7af4: StoreField: r3->field_b = r0
    //     0x8d7af4: stur            w0, [x3, #0xb]
    // 0x8d7af8: str             NULL, [SP]
    // 0x8d7afc: mov             x1, x3
    // 0x8d7b00: r2 = 0
    //     0x8d7b00: movz            x2, #0
    // 0x8d7b04: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7b04: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7b08: r0 = set()
    //     0x8d7b08: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7b0c: ldur            x0, [fp, #-0x18]
    // 0x8d7b10: ldur            x1, [fp, #-8]
    // 0x8d7b14: StoreField: r1->field_63 = r0
    //     0x8d7b14: stur            w0, [x1, #0x63]
    //     0x8d7b18: ldurb           w16, [x1, #-1]
    //     0x8d7b1c: ldurb           w17, [x0, #-1]
    //     0x8d7b20: and             x16, x17, x16, lsr #2
    //     0x8d7b24: tst             x16, HEAP, lsr #32
    //     0x8d7b28: b.eq            #0x8d7b30
    //     0x8d7b2c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d7b30: r0 = Register64()
    //     0x8d7b30: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d7b34: mov             x3, x0
    // 0x8d7b38: r0 = Sentinel
    //     0x8d7b38: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d7b3c: stur            x3, [fp, #-0x18]
    // 0x8d7b40: StoreField: r3->field_7 = r0
    //     0x8d7b40: stur            w0, [x3, #7]
    // 0x8d7b44: StoreField: r3->field_b = r0
    //     0x8d7b44: stur            w0, [x3, #0xb]
    // 0x8d7b48: str             NULL, [SP]
    // 0x8d7b4c: mov             x1, x3
    // 0x8d7b50: r2 = 0
    //     0x8d7b50: movz            x2, #0
    // 0x8d7b54: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7b54: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7b58: r0 = set()
    //     0x8d7b58: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7b5c: ldur            x0, [fp, #-0x18]
    // 0x8d7b60: ldur            x1, [fp, #-8]
    // 0x8d7b64: StoreField: r1->field_67 = r0
    //     0x8d7b64: stur            w0, [x1, #0x67]
    //     0x8d7b68: ldurb           w16, [x1, #-1]
    //     0x8d7b6c: ldurb           w17, [x0, #-1]
    //     0x8d7b70: and             x16, x17, x16, lsr #2
    //     0x8d7b74: tst             x16, HEAP, lsr #32
    //     0x8d7b78: b.eq            #0x8d7b80
    //     0x8d7b7c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d7b80: r0 = Register64()
    //     0x8d7b80: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d7b84: mov             x3, x0
    // 0x8d7b88: r0 = Sentinel
    //     0x8d7b88: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d7b8c: stur            x3, [fp, #-0x18]
    // 0x8d7b90: StoreField: r3->field_7 = r0
    //     0x8d7b90: stur            w0, [x3, #7]
    // 0x8d7b94: StoreField: r3->field_b = r0
    //     0x8d7b94: stur            w0, [x3, #0xb]
    // 0x8d7b98: str             NULL, [SP]
    // 0x8d7b9c: mov             x1, x3
    // 0x8d7ba0: r2 = 0
    //     0x8d7ba0: movz            x2, #0
    // 0x8d7ba4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7ba4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7ba8: r0 = set()
    //     0x8d7ba8: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7bac: ldur            x0, [fp, #-0x18]
    // 0x8d7bb0: ldur            x2, [fp, #-8]
    // 0x8d7bb4: StoreField: r2->field_6b = r0
    //     0x8d7bb4: stur            w0, [x2, #0x6b]
    //     0x8d7bb8: ldurb           w16, [x2, #-1]
    //     0x8d7bbc: ldurb           w17, [x0, #-1]
    //     0x8d7bc0: and             x16, x17, x16, lsr #2
    //     0x8d7bc4: tst             x16, HEAP, lsr #32
    //     0x8d7bc8: b.eq            #0x8d7bd0
    //     0x8d7bcc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8d7bd0: ldur            x0, [fp, #-0x10]
    // 0x8d7bd4: StoreField: r2->field_47 = r0
    //     0x8d7bd4: stur            x0, [x2, #0x47]
    // 0x8d7bd8: mov             x1, x2
    // 0x8d7bdc: r0 = LongSHA2FamilyDigest()
    //     0x8d7bdc: bl              #0x8e2860  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::LongSHA2FamilyDigest
    // 0x8d7be0: ldur            x0, [fp, #-0x10]
    // 0x8d7be4: cmp             x0, #0x40
    // 0x8d7be8: b.ge            #0x8d7c18
    // 0x8d7bec: cmp             x0, #0x30
    // 0x8d7bf0: b.eq            #0x8d7c40
    // 0x8d7bf4: lsl             x2, x0, #3
    // 0x8d7bf8: ldur            x1, [fp, #-8]
    // 0x8d7bfc: r0 = _generateIVs()
    //     0x8d7bfc: bl              #0x8d7c74  ; [package:pointycastle/digests/sha512t.dart] SHA512tDigest::_generateIVs
    // 0x8d7c00: ldur            x1, [fp, #-8]
    // 0x8d7c04: r0 = reset()
    //     0x8d7c04: bl              #0xeb59d4  ; [package:pointycastle/digests/sha512t.dart] SHA512tDigest::reset
    // 0x8d7c08: r0 = Null
    //     0x8d7c08: mov             x0, NULL
    // 0x8d7c0c: LeaveFrame
    //     0x8d7c0c: mov             SP, fp
    //     0x8d7c10: ldp             fp, lr, [SP], #0x10
    // 0x8d7c14: ret
    //     0x8d7c14: ret             
    // 0x8d7c18: r0 = ArgumentError()
    //     0x8d7c18: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8d7c1c: mov             x1, x0
    // 0x8d7c20: r0 = "Digest size cannot be >= 64 bytes (512 bits)"
    //     0x8d7c20: add             x0, PP, #0x19, lsl #12  ; [pp+0x19470] "Digest size cannot be >= 64 bytes (512 bits)"
    //     0x8d7c24: ldr             x0, [x0, #0x470]
    // 0x8d7c28: ArrayStore: r1[0] = r0  ; List_4
    //     0x8d7c28: stur            w0, [x1, #0x17]
    // 0x8d7c2c: r0 = false
    //     0x8d7c2c: add             x0, NULL, #0x30  ; false
    // 0x8d7c30: StoreField: r1->field_b = r0
    //     0x8d7c30: stur            w0, [x1, #0xb]
    // 0x8d7c34: mov             x0, x1
    // 0x8d7c38: r0 = Throw()
    //     0x8d7c38: bl              #0xec04b8  ; ThrowStub
    // 0x8d7c3c: brk             #0
    // 0x8d7c40: r0 = false
    //     0x8d7c40: add             x0, NULL, #0x30  ; false
    // 0x8d7c44: r0 = ArgumentError()
    //     0x8d7c44: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8d7c48: mov             x1, x0
    // 0x8d7c4c: r0 = "Digest size cannot be 48 bytes (384 bits): use SHA-384 instead"
    //     0x8d7c4c: add             x0, PP, #0x19, lsl #12  ; [pp+0x19478] "Digest size cannot be 48 bytes (384 bits): use SHA-384 instead"
    //     0x8d7c50: ldr             x0, [x0, #0x478]
    // 0x8d7c54: ArrayStore: r1[0] = r0  ; List_4
    //     0x8d7c54: stur            w0, [x1, #0x17]
    // 0x8d7c58: r0 = false
    //     0x8d7c58: add             x0, NULL, #0x30  ; false
    // 0x8d7c5c: StoreField: r1->field_b = r0
    //     0x8d7c5c: stur            w0, [x1, #0xb]
    // 0x8d7c60: mov             x0, x1
    // 0x8d7c64: r0 = Throw()
    //     0x8d7c64: bl              #0xec04b8  ; ThrowStub
    // 0x8d7c68: brk             #0
    // 0x8d7c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7c6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7c70: b               #0x8d7950
  }
  _ _generateIVs(/* No info */) {
    // ** addr: 0x8d7c74, size: 0x450
    // 0x8d7c74: EnterFrame
    //     0x8d7c74: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7c78: mov             fp, SP
    // 0x8d7c7c: AllocStack(0x68)
    //     0x8d7c7c: sub             SP, SP, #0x68
    // 0x8d7c80: SetupParameters(SHA512tDigest this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x8d7c80: mov             x3, x1
    //     0x8d7c84: mov             x0, x2
    //     0x8d7c88: stur            x1, [fp, #-0x10]
    //     0x8d7c8c: stur            x2, [fp, #-0x18]
    // 0x8d7c90: CheckStackOverflow
    //     0x8d7c90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7c94: cmp             SP, x16
    //     0x8d7c98: b.ls            #0x8d80a4
    // 0x8d7c9c: LoadField: r4 = r3->field_7
    //     0x8d7c9c: ldur            w4, [x3, #7]
    // 0x8d7ca0: DecompressPointer r4
    //     0x8d7ca0: add             x4, x4, HEAP, lsl #32
    // 0x8d7ca4: stur            x4, [fp, #-8]
    // 0x8d7ca8: r16 = 4089235720
    //     0x8d7ca8: add             x16, PP, #0x19, lsl #12  ; [pp+0x19480] 0xf3bcc908
    //     0x8d7cac: ldr             x16, [x16, #0x480]
    // 0x8d7cb0: str             x16, [SP]
    // 0x8d7cb4: mov             x1, x4
    // 0x8d7cb8: r2 = 1779033703
    //     0x8d7cb8: add             x2, PP, #0x12, lsl #12  ; [pp+0x12360] 0x6a09e667
    //     0x8d7cbc: ldr             x2, [x2, #0x360]
    // 0x8d7cc0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7cc0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7cc4: r0 = set()
    //     0x8d7cc4: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7cc8: r0 = InitLateStaticField(0xe2c) // [package:pointycastle/digests/sha512t.dart] SHA512tDigest::_hMask
    //     0x8d7cc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d7ccc: ldr             x0, [x0, #0x1c58]
    //     0x8d7cd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d7cd4: cmp             w0, w16
    //     0x8d7cd8: b.ne            #0x8d7ce8
    //     0x8d7cdc: add             x2, PP, #0x19, lsl #12  ; [pp+0x19488] Field <SHA512tDigest._hMask@935210196>: static late final (offset: 0xe2c)
    //     0x8d7ce0: ldr             x2, [x2, #0x488]
    //     0x8d7ce4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d7ce8: ldur            x1, [fp, #-8]
    // 0x8d7cec: mov             x2, x0
    // 0x8d7cf0: stur            x0, [fp, #-0x20]
    // 0x8d7cf4: r0 = xor()
    //     0x8d7cf4: bl              #0x8e26bc  ; [package:pointycastle/src/ufixnum.dart] Register64::xor
    // 0x8d7cf8: ldur            x0, [fp, #-0x10]
    // 0x8d7cfc: LoadField: r3 = r0->field_b
    //     0x8d7cfc: ldur            w3, [x0, #0xb]
    // 0x8d7d00: DecompressPointer r3
    //     0x8d7d00: add             x3, x3, HEAP, lsl #32
    // 0x8d7d04: stur            x3, [fp, #-0x28]
    // 0x8d7d08: r16 = 2227873595
    //     0x8d7d08: add             x16, PP, #0x19, lsl #12  ; [pp+0x19490] 0x84caa73b
    //     0x8d7d0c: ldr             x16, [x16, #0x490]
    // 0x8d7d10: str             x16, [SP]
    // 0x8d7d14: mov             x1, x3
    // 0x8d7d18: r2 = 3144134277
    //     0x8d7d18: add             x2, PP, #0x12, lsl #12  ; [pp+0x12368] 0xbb67ae85
    //     0x8d7d1c: ldr             x2, [x2, #0x368]
    // 0x8d7d20: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7d20: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7d24: r0 = set()
    //     0x8d7d24: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7d28: ldur            x1, [fp, #-0x28]
    // 0x8d7d2c: ldur            x2, [fp, #-0x20]
    // 0x8d7d30: r0 = xor()
    //     0x8d7d30: bl              #0x8e26bc  ; [package:pointycastle/src/ufixnum.dart] Register64::xor
    // 0x8d7d34: ldur            x0, [fp, #-0x10]
    // 0x8d7d38: LoadField: r3 = r0->field_f
    //     0x8d7d38: ldur            w3, [x0, #0xf]
    // 0x8d7d3c: DecompressPointer r3
    //     0x8d7d3c: add             x3, x3, HEAP, lsl #32
    // 0x8d7d40: stur            x3, [fp, #-0x30]
    // 0x8d7d44: r16 = 4271175723
    //     0x8d7d44: add             x16, PP, #0x19, lsl #12  ; [pp+0x19498] 0xfe94f82b
    //     0x8d7d48: ldr             x16, [x16, #0x498]
    // 0x8d7d4c: str             x16, [SP]
    // 0x8d7d50: mov             x1, x3
    // 0x8d7d54: r2 = 2027808484
    //     0x8d7d54: movz            x2, #0xe6e4
    //     0x8d7d58: movk            x2, #0x78dd, lsl #16
    // 0x8d7d5c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7d5c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7d60: r0 = set()
    //     0x8d7d60: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7d64: ldur            x1, [fp, #-0x30]
    // 0x8d7d68: ldur            x2, [fp, #-0x20]
    // 0x8d7d6c: r0 = xor()
    //     0x8d7d6c: bl              #0x8e26bc  ; [package:pointycastle/src/ufixnum.dart] Register64::xor
    // 0x8d7d70: ldur            x0, [fp, #-0x10]
    // 0x8d7d74: LoadField: r3 = r0->field_13
    //     0x8d7d74: ldur            w3, [x0, #0x13]
    // 0x8d7d78: DecompressPointer r3
    //     0x8d7d78: add             x3, x3, HEAP, lsl #32
    // 0x8d7d7c: stur            x3, [fp, #-0x38]
    // 0x8d7d80: r16 = 1595750129
    //     0x8d7d80: add             x16, PP, #0x19, lsl #12  ; [pp+0x194a0] 0x5f1d36f1
    //     0x8d7d84: ldr             x16, [x16, #0x4a0]
    // 0x8d7d88: str             x16, [SP]
    // 0x8d7d8c: mov             x1, x3
    // 0x8d7d90: r2 = 2773480762
    //     0x8d7d90: add             x2, PP, #0x12, lsl #12  ; [pp+0x12370] 0xa54ff53a
    //     0x8d7d94: ldr             x2, [x2, #0x370]
    // 0x8d7d98: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7d98: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7d9c: r0 = set()
    //     0x8d7d9c: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7da0: ldur            x1, [fp, #-0x38]
    // 0x8d7da4: ldur            x2, [fp, #-0x20]
    // 0x8d7da8: r0 = xor()
    //     0x8d7da8: bl              #0x8e26bc  ; [package:pointycastle/src/ufixnum.dart] Register64::xor
    // 0x8d7dac: ldur            x0, [fp, #-0x10]
    // 0x8d7db0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x8d7db0: ldur            w3, [x0, #0x17]
    // 0x8d7db4: DecompressPointer r3
    //     0x8d7db4: add             x3, x3, HEAP, lsl #32
    // 0x8d7db8: stur            x3, [fp, #-0x40]
    // 0x8d7dbc: r16 = 2917565137
    //     0x8d7dbc: add             x16, PP, #0x19, lsl #12  ; [pp+0x194a8] 0xade682d1
    //     0x8d7dc0: ldr             x16, [x16, #0x4a8]
    // 0x8d7dc4: str             x16, [SP]
    // 0x8d7dc8: mov             x1, x3
    // 0x8d7dcc: r2 = 1359893119
    //     0x8d7dcc: add             x2, PP, #0x12, lsl #12  ; [pp+0x12378] 0x510e527f
    //     0x8d7dd0: ldr             x2, [x2, #0x378]
    // 0x8d7dd4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7dd4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7dd8: r0 = set()
    //     0x8d7dd8: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7ddc: ldur            x1, [fp, #-0x40]
    // 0x8d7de0: ldur            x2, [fp, #-0x20]
    // 0x8d7de4: r0 = xor()
    //     0x8d7de4: bl              #0x8e26bc  ; [package:pointycastle/src/ufixnum.dart] Register64::xor
    // 0x8d7de8: ldur            x0, [fp, #-0x10]
    // 0x8d7dec: LoadField: r3 = r0->field_1b
    //     0x8d7dec: ldur            w3, [x0, #0x1b]
    // 0x8d7df0: DecompressPointer r3
    //     0x8d7df0: add             x3, x3, HEAP, lsl #32
    // 0x8d7df4: stur            x3, [fp, #-0x48]
    // 0x8d7df8: r16 = 1451022398
    //     0x8d7df8: movz            x16, #0xd83e
    //     0x8d7dfc: movk            x16, #0x567c, lsl #16
    // 0x8d7e00: str             x16, [SP]
    // 0x8d7e04: mov             x1, x3
    // 0x8d7e08: r2 = 2600822924
    //     0x8d7e08: add             x2, PP, #0x12, lsl #12  ; [pp+0x12380] 0x9b05688c
    //     0x8d7e0c: ldr             x2, [x2, #0x380]
    // 0x8d7e10: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7e10: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7e14: r0 = set()
    //     0x8d7e14: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7e18: ldur            x1, [fp, #-0x48]
    // 0x8d7e1c: ldur            x2, [fp, #-0x20]
    // 0x8d7e20: r0 = xor()
    //     0x8d7e20: bl              #0x8e26bc  ; [package:pointycastle/src/ufixnum.dart] Register64::xor
    // 0x8d7e24: ldur            x0, [fp, #-0x10]
    // 0x8d7e28: LoadField: r3 = r0->field_1f
    //     0x8d7e28: ldur            w3, [x0, #0x1f]
    // 0x8d7e2c: DecompressPointer r3
    //     0x8d7e2c: add             x3, x3, HEAP, lsl #32
    // 0x8d7e30: stur            x3, [fp, #-0x50]
    // 0x8d7e34: r16 = 4215389547
    //     0x8d7e34: add             x16, PP, #0x19, lsl #12  ; [pp+0x194b0] 0xfb41bd6b
    //     0x8d7e38: ldr             x16, [x16, #0x4b0]
    // 0x8d7e3c: str             x16, [SP]
    // 0x8d7e40: mov             x1, x3
    // 0x8d7e44: r2 = 1057469270
    //     0x8d7e44: movz            x2, #0xb356
    //     0x8d7e48: movk            x2, #0x3f07, lsl #16
    // 0x8d7e4c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7e4c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7e50: r0 = set()
    //     0x8d7e50: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7e54: ldur            x1, [fp, #-0x50]
    // 0x8d7e58: ldur            x2, [fp, #-0x20]
    // 0x8d7e5c: r0 = xor()
    //     0x8d7e5c: bl              #0x8e26bc  ; [package:pointycastle/src/ufixnum.dart] Register64::xor
    // 0x8d7e60: ldur            x0, [fp, #-0x10]
    // 0x8d7e64: LoadField: r3 = r0->field_23
    //     0x8d7e64: ldur            w3, [x0, #0x23]
    // 0x8d7e68: DecompressPointer r3
    //     0x8d7e68: add             x3, x3, HEAP, lsl #32
    // 0x8d7e6c: stur            x3, [fp, #-0x58]
    // 0x8d7e70: r16 = 654066418
    //     0x8d7e70: movz            x16, #0x42f2
    //     0x8d7e74: movk            x16, #0x26fc, lsl #16
    // 0x8d7e78: str             x16, [SP]
    // 0x8d7e7c: mov             x1, x3
    // 0x8d7e80: r2 = 1541459225
    //     0x8d7e80: add             x2, PP, #0x12, lsl #12  ; [pp+0x12388] 0x5be0cd19
    //     0x8d7e84: ldr             x2, [x2, #0x388]
    // 0x8d7e88: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7e88: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7e8c: r0 = set()
    //     0x8d7e8c: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7e90: ldur            x1, [fp, #-0x58]
    // 0x8d7e94: ldur            x2, [fp, #-0x20]
    // 0x8d7e98: r0 = xor()
    //     0x8d7e98: bl              #0x8e26bc  ; [package:pointycastle/src/ufixnum.dart] Register64::xor
    // 0x8d7e9c: ldur            x1, [fp, #-0x10]
    // 0x8d7ea0: r2 = 83
    //     0x8d7ea0: movz            x2, #0x53
    // 0x8d7ea4: r0 = updateByte()
    //     0x8d7ea4: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7ea8: ldur            x1, [fp, #-0x10]
    // 0x8d7eac: r2 = 72
    //     0x8d7eac: movz            x2, #0x48
    // 0x8d7eb0: r0 = updateByte()
    //     0x8d7eb0: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7eb4: ldur            x1, [fp, #-0x10]
    // 0x8d7eb8: r2 = 65
    //     0x8d7eb8: movz            x2, #0x41
    // 0x8d7ebc: r0 = updateByte()
    //     0x8d7ebc: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7ec0: ldur            x1, [fp, #-0x10]
    // 0x8d7ec4: r2 = 45
    //     0x8d7ec4: movz            x2, #0x2d
    // 0x8d7ec8: r0 = updateByte()
    //     0x8d7ec8: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7ecc: ldur            x1, [fp, #-0x10]
    // 0x8d7ed0: r2 = 53
    //     0x8d7ed0: movz            x2, #0x35
    // 0x8d7ed4: r0 = updateByte()
    //     0x8d7ed4: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7ed8: ldur            x1, [fp, #-0x10]
    // 0x8d7edc: r2 = 49
    //     0x8d7edc: movz            x2, #0x31
    // 0x8d7ee0: r0 = updateByte()
    //     0x8d7ee0: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7ee4: ldur            x1, [fp, #-0x10]
    // 0x8d7ee8: r2 = 50
    //     0x8d7ee8: movz            x2, #0x32
    // 0x8d7eec: r0 = updateByte()
    //     0x8d7eec: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7ef0: ldur            x1, [fp, #-0x10]
    // 0x8d7ef4: r2 = 47
    //     0x8d7ef4: movz            x2, #0x2f
    // 0x8d7ef8: r0 = updateByte()
    //     0x8d7ef8: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7efc: ldur            x0, [fp, #-0x18]
    // 0x8d7f00: cmp             x0, #0x64
    // 0x8d7f04: b.le            #0x8d7f74
    // 0x8d7f08: r3 = 100
    //     0x8d7f08: movz            x3, #0x64
    // 0x8d7f0c: sdiv            x1, x0, x3
    // 0x8d7f10: add             x2, x1, #0x30
    // 0x8d7f14: ldur            x1, [fp, #-0x10]
    // 0x8d7f18: r0 = updateByte()
    //     0x8d7f18: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7f1c: ldur            x0, [fp, #-0x18]
    // 0x8d7f20: r1 = 100
    //     0x8d7f20: movz            x1, #0x64
    // 0x8d7f24: sdiv            x2, x0, x1
    // 0x8d7f28: msub            x3, x2, x1, x0
    // 0x8d7f2c: cmp             x3, xzr
    // 0x8d7f30: b.lt            #0x8d80ac
    // 0x8d7f34: stur            x3, [fp, #-0x60]
    // 0x8d7f38: r0 = 10
    //     0x8d7f38: movz            x0, #0xa
    // 0x8d7f3c: sdiv            x1, x3, x0
    // 0x8d7f40: add             x2, x1, #0x30
    // 0x8d7f44: ldur            x1, [fp, #-0x10]
    // 0x8d7f48: r0 = updateByte()
    //     0x8d7f48: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7f4c: ldur            x0, [fp, #-0x60]
    // 0x8d7f50: r3 = 10
    //     0x8d7f50: movz            x3, #0xa
    // 0x8d7f54: sdiv            x2, x0, x3
    // 0x8d7f58: msub            x1, x2, x3, x0
    // 0x8d7f5c: cmp             x1, xzr
    // 0x8d7f60: b.lt            #0x8d80b4
    // 0x8d7f64: add             x2, x1, #0x30
    // 0x8d7f68: ldur            x1, [fp, #-0x10]
    // 0x8d7f6c: r0 = updateByte()
    //     0x8d7f6c: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7f70: b               #0x8d7fc8
    // 0x8d7f74: r3 = 10
    //     0x8d7f74: movz            x3, #0xa
    // 0x8d7f78: cmp             x0, #0xa
    // 0x8d7f7c: b.le            #0x8d7fbc
    // 0x8d7f80: sdiv            x1, x0, x3
    // 0x8d7f84: add             x2, x1, #0x30
    // 0x8d7f88: ldur            x1, [fp, #-0x10]
    // 0x8d7f8c: r0 = updateByte()
    //     0x8d7f8c: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7f90: ldur            x0, [fp, #-0x18]
    // 0x8d7f94: r1 = 10
    //     0x8d7f94: movz            x1, #0xa
    // 0x8d7f98: sdiv            x3, x0, x1
    // 0x8d7f9c: msub            x2, x3, x1, x0
    // 0x8d7fa0: cmp             x2, xzr
    // 0x8d7fa4: b.lt            #0x8d80bc
    // 0x8d7fa8: add             x0, x2, #0x30
    // 0x8d7fac: ldur            x1, [fp, #-0x10]
    // 0x8d7fb0: mov             x2, x0
    // 0x8d7fb4: r0 = updateByte()
    //     0x8d7fb4: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7fb8: b               #0x8d7fc8
    // 0x8d7fbc: add             x2, x0, #0x30
    // 0x8d7fc0: ldur            x1, [fp, #-0x10]
    // 0x8d7fc4: r0 = updateByte()
    //     0x8d7fc4: bl              #0x8e236c  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::updateByte
    // 0x8d7fc8: ldur            x0, [fp, #-0x10]
    // 0x8d7fcc: mov             x1, x0
    // 0x8d7fd0: r0 = finish()
    //     0x8d7fd0: bl              #0x8d80c4  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::finish
    // 0x8d7fd4: ldur            x0, [fp, #-0x10]
    // 0x8d7fd8: LoadField: r1 = r0->field_4f
    //     0x8d7fd8: ldur            w1, [x0, #0x4f]
    // 0x8d7fdc: DecompressPointer r1
    //     0x8d7fdc: add             x1, x1, HEAP, lsl #32
    // 0x8d7fe0: ldur            x2, [fp, #-8]
    // 0x8d7fe4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d7fe4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d7fe8: r0 = set()
    //     0x8d7fe8: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7fec: ldur            x0, [fp, #-0x10]
    // 0x8d7ff0: LoadField: r1 = r0->field_53
    //     0x8d7ff0: ldur            w1, [x0, #0x53]
    // 0x8d7ff4: DecompressPointer r1
    //     0x8d7ff4: add             x1, x1, HEAP, lsl #32
    // 0x8d7ff8: ldur            x2, [fp, #-0x28]
    // 0x8d7ffc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d7ffc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d8000: r0 = set()
    //     0x8d8000: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d8004: ldur            x0, [fp, #-0x10]
    // 0x8d8008: LoadField: r1 = r0->field_57
    //     0x8d8008: ldur            w1, [x0, #0x57]
    // 0x8d800c: DecompressPointer r1
    //     0x8d800c: add             x1, x1, HEAP, lsl #32
    // 0x8d8010: ldur            x2, [fp, #-0x30]
    // 0x8d8014: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d8014: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d8018: r0 = set()
    //     0x8d8018: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d801c: ldur            x0, [fp, #-0x10]
    // 0x8d8020: LoadField: r1 = r0->field_5b
    //     0x8d8020: ldur            w1, [x0, #0x5b]
    // 0x8d8024: DecompressPointer r1
    //     0x8d8024: add             x1, x1, HEAP, lsl #32
    // 0x8d8028: ldur            x2, [fp, #-0x38]
    // 0x8d802c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d802c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d8030: r0 = set()
    //     0x8d8030: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d8034: ldur            x0, [fp, #-0x10]
    // 0x8d8038: LoadField: r1 = r0->field_5f
    //     0x8d8038: ldur            w1, [x0, #0x5f]
    // 0x8d803c: DecompressPointer r1
    //     0x8d803c: add             x1, x1, HEAP, lsl #32
    // 0x8d8040: ldur            x2, [fp, #-0x40]
    // 0x8d8044: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d8044: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d8048: r0 = set()
    //     0x8d8048: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d804c: ldur            x0, [fp, #-0x10]
    // 0x8d8050: LoadField: r1 = r0->field_63
    //     0x8d8050: ldur            w1, [x0, #0x63]
    // 0x8d8054: DecompressPointer r1
    //     0x8d8054: add             x1, x1, HEAP, lsl #32
    // 0x8d8058: ldur            x2, [fp, #-0x48]
    // 0x8d805c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d805c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d8060: r0 = set()
    //     0x8d8060: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d8064: ldur            x0, [fp, #-0x10]
    // 0x8d8068: LoadField: r1 = r0->field_67
    //     0x8d8068: ldur            w1, [x0, #0x67]
    // 0x8d806c: DecompressPointer r1
    //     0x8d806c: add             x1, x1, HEAP, lsl #32
    // 0x8d8070: ldur            x2, [fp, #-0x50]
    // 0x8d8074: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d8074: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d8078: r0 = set()
    //     0x8d8078: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d807c: ldur            x0, [fp, #-0x10]
    // 0x8d8080: LoadField: r1 = r0->field_6b
    //     0x8d8080: ldur            w1, [x0, #0x6b]
    // 0x8d8084: DecompressPointer r1
    //     0x8d8084: add             x1, x1, HEAP, lsl #32
    // 0x8d8088: ldur            x2, [fp, #-0x58]
    // 0x8d808c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d808c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d8090: r0 = set()
    //     0x8d8090: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d8094: r0 = Null
    //     0x8d8094: mov             x0, NULL
    // 0x8d8098: LeaveFrame
    //     0x8d8098: mov             SP, fp
    //     0x8d809c: ldp             fp, lr, [SP], #0x10
    // 0x8d80a0: ret
    //     0x8d80a0: ret             
    // 0x8d80a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d80a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d80a8: b               #0x8d7c9c
    // 0x8d80ac: add             x3, x3, x1
    // 0x8d80b0: b               #0x8d7f34
    // 0x8d80b4: add             x1, x1, x3
    // 0x8d80b8: b               #0x8d7f64
    // 0x8d80bc: add             x2, x2, x1
    // 0x8d80c0: b               #0x8d7fa8
  }
  static Register64 _hMask() {
    // ** addr: 0x8e27f8, size: 0x68
    // 0x8e27f8: EnterFrame
    //     0x8e27f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e27fc: mov             fp, SP
    // 0x8e2800: AllocStack(0x10)
    //     0x8e2800: sub             SP, SP, #0x10
    // 0x8e2804: CheckStackOverflow
    //     0x8e2804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e2808: cmp             SP, x16
    //     0x8e280c: b.ls            #0x8e2858
    // 0x8e2810: r0 = Register64()
    //     0x8e2810: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e2814: mov             x3, x0
    // 0x8e2818: r0 = Sentinel
    //     0x8e2818: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e281c: stur            x3, [fp, #-8]
    // 0x8e2820: StoreField: r3->field_7 = r0
    //     0x8e2820: stur            w0, [x3, #7]
    // 0x8e2824: StoreField: r3->field_b = r0
    //     0x8e2824: stur            w0, [x3, #0xb]
    // 0x8e2828: r16 = 2779096485
    //     0x8e2828: add             x16, PP, #0x19, lsl #12  ; [pp+0x198c8] 0xa5a5a5a5
    //     0x8e282c: ldr             x16, [x16, #0x8c8]
    // 0x8e2830: str             x16, [SP]
    // 0x8e2834: mov             x1, x3
    // 0x8e2838: r2 = 2779096485
    //     0x8e2838: add             x2, PP, #0x19, lsl #12  ; [pp+0x198c8] 0xa5a5a5a5
    //     0x8e283c: ldr             x2, [x2, #0x8c8]
    // 0x8e2840: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e2840: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e2844: r0 = set()
    //     0x8e2844: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e2848: ldur            x0, [fp, #-8]
    // 0x8e284c: LeaveFrame
    //     0x8e284c: mov             SP, fp
    //     0x8e2850: ldp             fp, lr, [SP], #0x10
    // 0x8e2854: ret
    //     0x8e2854: ret             
    // 0x8e2858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e2858: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e285c: b               #0x8e2810
  }
  static RegExp _nameRegex() {
    // ** addr: 0x8e2d10, size: 0x58
    // 0x8e2d10: EnterFrame
    //     0x8e2d10: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2d14: mov             fp, SP
    // 0x8e2d18: AllocStack(0x30)
    //     0x8e2d18: sub             SP, SP, #0x30
    // 0x8e2d1c: CheckStackOverflow
    //     0x8e2d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e2d20: cmp             SP, x16
    //     0x8e2d24: b.ls            #0x8e2d60
    // 0x8e2d28: r16 = "^SHA-512\\/([0-9]+)$"
    //     0x8e2d28: add             x16, PP, #0x19, lsl #12  ; [pp+0x198d0] "^SHA-512\\/([0-9]+)$"
    //     0x8e2d2c: ldr             x16, [x16, #0x8d0]
    // 0x8e2d30: stp             x16, NULL, [SP, #0x20]
    // 0x8e2d34: r16 = false
    //     0x8e2d34: add             x16, NULL, #0x30  ; false
    // 0x8e2d38: r30 = true
    //     0x8e2d38: add             lr, NULL, #0x20  ; true
    // 0x8e2d3c: stp             lr, x16, [SP, #0x10]
    // 0x8e2d40: r16 = false
    //     0x8e2d40: add             x16, NULL, #0x30  ; false
    // 0x8e2d44: r30 = false
    //     0x8e2d44: add             lr, NULL, #0x30  ; false
    // 0x8e2d48: stp             lr, x16, [SP]
    // 0x8e2d4c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8e2d4c: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8e2d50: r0 = _RegExp()
    //     0x8e2d50: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8e2d54: LeaveFrame
    //     0x8e2d54: mov             SP, fp
    //     0x8e2d58: ldp             fp, lr, [SP], #0x10
    // 0x8e2d5c: ret
    //     0x8e2d5c: ret             
    // 0x8e2d60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e2d60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e2d64: b               #0x8e2d28
  }
  const get _ digestSize(/* No info */) {
    // ** addr: 0xe61e10, size: 0x8
    // 0xe61e10: LoadField: r0 = r1->field_47
    //     0xe61e10: ldur            x0, [x1, #0x47]
    // 0xe61e14: ret
    //     0xe61e14: ret             
  }
  _ reset(/* No info */) {
    // ** addr: 0xeb59d4, size: 0x120
    // 0xeb59d4: EnterFrame
    //     0xeb59d4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb59d8: mov             fp, SP
    // 0xeb59dc: AllocStack(0x8)
    //     0xeb59dc: sub             SP, SP, #8
    // 0xeb59e0: SetupParameters(SHA512tDigest this /* r1 => r0, fp-0x8 */)
    //     0xeb59e0: mov             x0, x1
    //     0xeb59e4: stur            x1, [fp, #-8]
    // 0xeb59e8: CheckStackOverflow
    //     0xeb59e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb59ec: cmp             SP, x16
    //     0xeb59f0: b.ls            #0xeb5aec
    // 0xeb59f4: mov             x1, x0
    // 0xeb59f8: r0 = reset()
    //     0xeb59f8: bl              #0xeb57a4  ; [package:pointycastle/src/impl/long_sha2_family_digest.dart] LongSHA2FamilyDigest::reset
    // 0xeb59fc: ldur            x0, [fp, #-8]
    // 0xeb5a00: LoadField: r1 = r0->field_7
    //     0xeb5a00: ldur            w1, [x0, #7]
    // 0xeb5a04: DecompressPointer r1
    //     0xeb5a04: add             x1, x1, HEAP, lsl #32
    // 0xeb5a08: LoadField: r2 = r0->field_4f
    //     0xeb5a08: ldur            w2, [x0, #0x4f]
    // 0xeb5a0c: DecompressPointer r2
    //     0xeb5a0c: add             x2, x2, HEAP, lsl #32
    // 0xeb5a10: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb5a10: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb5a14: r0 = set()
    //     0xeb5a14: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5a18: ldur            x0, [fp, #-8]
    // 0xeb5a1c: LoadField: r1 = r0->field_b
    //     0xeb5a1c: ldur            w1, [x0, #0xb]
    // 0xeb5a20: DecompressPointer r1
    //     0xeb5a20: add             x1, x1, HEAP, lsl #32
    // 0xeb5a24: LoadField: r2 = r0->field_53
    //     0xeb5a24: ldur            w2, [x0, #0x53]
    // 0xeb5a28: DecompressPointer r2
    //     0xeb5a28: add             x2, x2, HEAP, lsl #32
    // 0xeb5a2c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb5a2c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb5a30: r0 = set()
    //     0xeb5a30: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5a34: ldur            x0, [fp, #-8]
    // 0xeb5a38: LoadField: r1 = r0->field_f
    //     0xeb5a38: ldur            w1, [x0, #0xf]
    // 0xeb5a3c: DecompressPointer r1
    //     0xeb5a3c: add             x1, x1, HEAP, lsl #32
    // 0xeb5a40: LoadField: r2 = r0->field_57
    //     0xeb5a40: ldur            w2, [x0, #0x57]
    // 0xeb5a44: DecompressPointer r2
    //     0xeb5a44: add             x2, x2, HEAP, lsl #32
    // 0xeb5a48: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb5a48: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb5a4c: r0 = set()
    //     0xeb5a4c: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5a50: ldur            x0, [fp, #-8]
    // 0xeb5a54: LoadField: r1 = r0->field_13
    //     0xeb5a54: ldur            w1, [x0, #0x13]
    // 0xeb5a58: DecompressPointer r1
    //     0xeb5a58: add             x1, x1, HEAP, lsl #32
    // 0xeb5a5c: LoadField: r2 = r0->field_5b
    //     0xeb5a5c: ldur            w2, [x0, #0x5b]
    // 0xeb5a60: DecompressPointer r2
    //     0xeb5a60: add             x2, x2, HEAP, lsl #32
    // 0xeb5a64: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb5a64: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb5a68: r0 = set()
    //     0xeb5a68: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5a6c: ldur            x0, [fp, #-8]
    // 0xeb5a70: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb5a70: ldur            w1, [x0, #0x17]
    // 0xeb5a74: DecompressPointer r1
    //     0xeb5a74: add             x1, x1, HEAP, lsl #32
    // 0xeb5a78: LoadField: r2 = r0->field_5f
    //     0xeb5a78: ldur            w2, [x0, #0x5f]
    // 0xeb5a7c: DecompressPointer r2
    //     0xeb5a7c: add             x2, x2, HEAP, lsl #32
    // 0xeb5a80: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb5a80: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb5a84: r0 = set()
    //     0xeb5a84: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5a88: ldur            x0, [fp, #-8]
    // 0xeb5a8c: LoadField: r1 = r0->field_1b
    //     0xeb5a8c: ldur            w1, [x0, #0x1b]
    // 0xeb5a90: DecompressPointer r1
    //     0xeb5a90: add             x1, x1, HEAP, lsl #32
    // 0xeb5a94: LoadField: r2 = r0->field_63
    //     0xeb5a94: ldur            w2, [x0, #0x63]
    // 0xeb5a98: DecompressPointer r2
    //     0xeb5a98: add             x2, x2, HEAP, lsl #32
    // 0xeb5a9c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb5a9c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb5aa0: r0 = set()
    //     0xeb5aa0: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5aa4: ldur            x0, [fp, #-8]
    // 0xeb5aa8: LoadField: r1 = r0->field_1f
    //     0xeb5aa8: ldur            w1, [x0, #0x1f]
    // 0xeb5aac: DecompressPointer r1
    //     0xeb5aac: add             x1, x1, HEAP, lsl #32
    // 0xeb5ab0: LoadField: r2 = r0->field_67
    //     0xeb5ab0: ldur            w2, [x0, #0x67]
    // 0xeb5ab4: DecompressPointer r2
    //     0xeb5ab4: add             x2, x2, HEAP, lsl #32
    // 0xeb5ab8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb5ab8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb5abc: r0 = set()
    //     0xeb5abc: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5ac0: ldur            x0, [fp, #-8]
    // 0xeb5ac4: LoadField: r1 = r0->field_23
    //     0xeb5ac4: ldur            w1, [x0, #0x23]
    // 0xeb5ac8: DecompressPointer r1
    //     0xeb5ac8: add             x1, x1, HEAP, lsl #32
    // 0xeb5acc: LoadField: r2 = r0->field_6b
    //     0xeb5acc: ldur            w2, [x0, #0x6b]
    // 0xeb5ad0: DecompressPointer r2
    //     0xeb5ad0: add             x2, x2, HEAP, lsl #32
    // 0xeb5ad4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb5ad4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb5ad8: r0 = set()
    //     0xeb5ad8: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0xeb5adc: r0 = Null
    //     0xeb5adc: mov             x0, NULL
    // 0xeb5ae0: LeaveFrame
    //     0xeb5ae0: mov             SP, fp
    //     0xeb5ae4: ldp             fp, lr, [SP], #0x10
    // 0xeb5ae8: ret
    //     0xeb5ae8: ret             
    // 0xeb5aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5aec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5af0: b               #0xeb59f4
  }
}
