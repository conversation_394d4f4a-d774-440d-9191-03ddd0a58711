// lib: impl.digest.shake, url: package:pointycastle/digests/shake.dart

// class id: 1050958, size: 0x8
class :: {
}

// class id: 666, size: 0x18, field offset: 0x18
class SHAKEDigest extends KeccakEngine
    implements Xof {

  static late final FactoryConfig factoryConfig; // offset: 0xde0
  static late final RegExp _shakeREGEX; // offset: 0xddc

  get _ algorithmName(/* No info */) {
    // ** addr: 0x869b70, size: 0x7c
    // 0x869b70: EnterFrame
    //     0x869b70: stp             fp, lr, [SP, #-0x10]!
    //     0x869b74: mov             fp, SP
    // 0x869b78: AllocStack(0x10)
    //     0x869b78: sub             SP, SP, #0x10
    // 0x869b7c: SetupParameters(SHAKEDigest this /* r1 => r0, fp-0x8 */)
    //     0x869b7c: mov             x0, x1
    //     0x869b80: stur            x1, [fp, #-8]
    // 0x869b84: CheckStackOverflow
    //     0x869b84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x869b88: cmp             SP, x16
    //     0x869b8c: b.ls            #0x869bd8
    // 0x869b90: r1 = Null
    //     0x869b90: mov             x1, NULL
    // 0x869b94: r2 = 4
    //     0x869b94: movz            x2, #0x4
    // 0x869b98: r0 = AllocateArray()
    //     0x869b98: bl              #0xec22fc  ; AllocateArrayStub
    // 0x869b9c: r16 = "SHAKE-"
    //     0x869b9c: add             x16, PP, #0x21, lsl #12  ; [pp+0x21cd0] "SHAKE-"
    //     0x869ba0: ldr             x16, [x16, #0xcd0]
    // 0x869ba4: StoreField: r0->field_f = r16
    //     0x869ba4: stur            w16, [x0, #0xf]
    // 0x869ba8: ldur            x1, [fp, #-8]
    // 0x869bac: LoadField: r2 = r1->field_13
    //     0x869bac: ldur            w2, [x1, #0x13]
    // 0x869bb0: DecompressPointer r2
    //     0x869bb0: add             x2, x2, HEAP, lsl #32
    // 0x869bb4: r16 = Sentinel
    //     0x869bb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x869bb8: cmp             w2, w16
    // 0x869bbc: b.eq            #0x869be0
    // 0x869bc0: StoreField: r0->field_13 = r2
    //     0x869bc0: stur            w2, [x0, #0x13]
    // 0x869bc4: str             x0, [SP]
    // 0x869bc8: r0 = _interpolate()
    //     0x869bc8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x869bcc: LeaveFrame
    //     0x869bcc: mov             SP, fp
    //     0x869bd0: ldp             fp, lr, [SP], #0x10
    // 0x869bd4: ret
    //     0x869bd4: ret             
    // 0x869bd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x869bd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x869bdc: b               #0x869b90
    // 0x869be0: r9 = fixedOutputLength
    //     0x869be0: add             x9, PP, #0x20, lsl #12  ; [pp+0x209f8] Field <KeccakEngine.fixedOutputLength>: late (offset: 0x14)
    //     0x869be4: ldr             x9, [x9, #0x9f8]
    // 0x869be8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x869be8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ SHAKEDigest(/* No info */) {
    // ** addr: 0x8d650c, size: 0x188
    // 0x8d650c: EnterFrame
    //     0x8d650c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d6510: mov             fp, SP
    // 0x8d6514: AllocStack(0x18)
    //     0x8d6514: sub             SP, SP, #0x18
    // 0x8d6518: SetupParameters(SHAKEDigest this /* r1 => r1, fp-0x10 */, [int _ = 256 /* r2, fp-0x8 */])
    //     0x8d6518: stur            x1, [fp, #-0x10]
    //     0x8d651c: ldur            w0, [x4, #0x13]
    //     0x8d6520: sub             x2, x0, #2
    //     0x8d6524: cmp             w2, #2
    //     0x8d6528: b.lt            #0x8d6544
    //     0x8d652c: add             x0, fp, w2, sxtw #2
    //     0x8d6530: ldr             x0, [x0, #8]
    //     0x8d6534: sbfx            x2, x0, #1, #0x1f
    //     0x8d6538: tbz             w0, #0, #0x8d6540
    //     0x8d653c: ldur            x2, [x0, #7]
    //     0x8d6540: b               #0x8d6548
    //     0x8d6544: movz            x2, #0x100
    //     0x8d6548: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d654c: stur            x2, [fp, #-8]
    // 0x8d6548: r0 = Sentinel
    // 0x8d6550: CheckStackOverflow
    //     0x8d6550: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6554: cmp             SP, x16
    //     0x8d6558: b.ls            #0x8d668c
    // 0x8d655c: StoreField: r1->field_f = r0
    //     0x8d655c: stur            w0, [x1, #0xf]
    // 0x8d6560: StoreField: r1->field_13 = r0
    //     0x8d6560: stur            w0, [x1, #0x13]
    // 0x8d6564: r4 = 400
    //     0x8d6564: movz            x4, #0x190
    // 0x8d6568: r0 = AllocateUint8Array()
    //     0x8d6568: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8d656c: ldur            x1, [fp, #-0x10]
    // 0x8d6570: StoreField: r1->field_7 = r0
    //     0x8d6570: stur            w0, [x1, #7]
    //     0x8d6574: ldurb           w16, [x1, #-1]
    //     0x8d6578: ldurb           w17, [x0, #-1]
    //     0x8d657c: and             x16, x17, x16, lsr #2
    //     0x8d6580: tst             x16, HEAP, lsr #32
    //     0x8d6584: b.eq            #0x8d658c
    //     0x8d6588: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d658c: r4 = 384
    //     0x8d658c: movz            x4, #0x180
    // 0x8d6590: r0 = AllocateUint8Array()
    //     0x8d6590: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8d6594: ldur            x2, [fp, #-0x10]
    // 0x8d6598: StoreField: r2->field_b = r0
    //     0x8d6598: stur            w0, [x2, #0xb]
    //     0x8d659c: ldurb           w16, [x2, #-1]
    //     0x8d65a0: ldurb           w17, [x0, #-1]
    //     0x8d65a4: and             x16, x17, x16, lsr #2
    //     0x8d65a8: tst             x16, HEAP, lsr #32
    //     0x8d65ac: b.eq            #0x8d65b4
    //     0x8d65b0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8d65b4: ldur            x3, [fp, #-8]
    // 0x8d65b8: cmp             x3, #0x80
    // 0x8d65bc: b.gt            #0x8d65e0
    // 0x8d65c0: r0 = BoxInt64Instr(r3)
    //     0x8d65c0: sbfiz           x0, x3, #1, #0x1f
    //     0x8d65c4: cmp             x3, x0, asr #1
    //     0x8d65c8: b.eq            #0x8d65d4
    //     0x8d65cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d65d0: stur            x3, [x0, #7]
    // 0x8d65d4: cmp             w0, #0x100
    // 0x8d65d8: b.ne            #0x8d6620
    // 0x8d65dc: b               #0x8d6604
    // 0x8d65e0: cmp             x3, #0x100
    // 0x8d65e4: b.lt            #0x8d6620
    // 0x8d65e8: r0 = BoxInt64Instr(r3)
    //     0x8d65e8: sbfiz           x0, x3, #1, #0x1f
    //     0x8d65ec: cmp             x3, x0, asr #1
    //     0x8d65f0: b.eq            #0x8d65fc
    //     0x8d65f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d65f8: stur            x3, [x0, #7]
    // 0x8d65fc: cmp             w0, #0x200
    // 0x8d6600: b.ne            #0x8d6620
    // 0x8d6604: mov             x1, x2
    // 0x8d6608: mov             x2, x3
    // 0x8d660c: r0 = init()
    //     0x8d660c: bl              #0x8d639c  ; [package:pointycastle/src/impl/keccak_engine.dart] KeccakEngine::init
    // 0x8d6610: r0 = Null
    //     0x8d6610: mov             x0, NULL
    // 0x8d6614: LeaveFrame
    //     0x8d6614: mov             SP, fp
    //     0x8d6618: ldp             fp, lr, [SP], #0x10
    // 0x8d661c: ret
    //     0x8d661c: ret             
    // 0x8d6620: r1 = Null
    //     0x8d6620: mov             x1, NULL
    // 0x8d6624: r2 = 6
    //     0x8d6624: movz            x2, #0x6
    // 0x8d6628: r0 = AllocateArray()
    //     0x8d6628: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8d662c: mov             x2, x0
    // 0x8d6630: r16 = "invalid bitLength ("
    //     0x8d6630: add             x16, PP, #0x19, lsl #12  ; [pp+0x193c8] "invalid bitLength ("
    //     0x8d6634: ldr             x16, [x16, #0x3c8]
    // 0x8d6638: StoreField: r2->field_f = r16
    //     0x8d6638: stur            w16, [x2, #0xf]
    // 0x8d663c: ldur            x3, [fp, #-8]
    // 0x8d6640: r0 = BoxInt64Instr(r3)
    //     0x8d6640: sbfiz           x0, x3, #1, #0x1f
    //     0x8d6644: cmp             x3, x0, asr #1
    //     0x8d6648: b.eq            #0x8d6654
    //     0x8d664c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d6650: stur            x3, [x0, #7]
    // 0x8d6654: StoreField: r2->field_13 = r0
    //     0x8d6654: stur            w0, [x2, #0x13]
    // 0x8d6658: r16 = ") for SHAKE must only be 128 or 256"
    //     0x8d6658: add             x16, PP, #0x19, lsl #12  ; [pp+0x193e0] ") for SHAKE must only be 128 or 256"
    //     0x8d665c: ldr             x16, [x16, #0x3e0]
    // 0x8d6660: ArrayStore: r2[0] = r16  ; List_4
    //     0x8d6660: stur            w16, [x2, #0x17]
    // 0x8d6664: str             x2, [SP]
    // 0x8d6668: r0 = _interpolate()
    //     0x8d6668: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8d666c: stur            x0, [fp, #-0x10]
    // 0x8d6670: r0 = StateError()
    //     0x8d6670: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8d6674: mov             x1, x0
    // 0x8d6678: ldur            x0, [fp, #-0x10]
    // 0x8d667c: StoreField: r1->field_b = r0
    //     0x8d667c: stur            w0, [x1, #0xb]
    // 0x8d6680: mov             x0, x1
    // 0x8d6684: r0 = Throw()
    //     0x8d6684: bl              #0xec04b8  ; ThrowStub
    // 0x8d6688: brk             #0
    // 0x8d668c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d668c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6690: b               #0x8d655c
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d66f8, size: 0x8c
    // 0x8d66f8: EnterFrame
    //     0x8d66f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d66fc: mov             fp, SP
    // 0x8d6700: AllocStack(0x10)
    //     0x8d6700: sub             SP, SP, #0x10
    // 0x8d6704: CheckStackOverflow
    //     0x8d6704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6708: cmp             SP, x16
    //     0x8d670c: b.ls            #0x8d677c
    // 0x8d6710: r0 = InitLateStaticField(0xddc) // [package:pointycastle/digests/shake.dart] SHAKEDigest::_shakeREGEX
    //     0x8d6710: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d6714: ldr             x0, [x0, #0x1bb8]
    //     0x8d6718: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d671c: cmp             w0, w16
    //     0x8d6720: b.ne            #0x8d6730
    //     0x8d6724: add             x2, PP, #0x19, lsl #12  ; [pp+0x193f0] Field <SHAKEDigest._shakeREGEX@921418232>: static late final (offset: 0xddc)
    //     0x8d6728: ldr             x2, [x2, #0x3f0]
    //     0x8d672c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d6730: stur            x0, [fp, #-8]
    // 0x8d6734: r0 = DynamicFactoryConfig()
    //     0x8d6734: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8d6738: mov             x3, x0
    // 0x8d673c: ldur            x0, [fp, #-8]
    // 0x8d6740: stur            x3, [fp, #-0x10]
    // 0x8d6744: StoreField: r3->field_b = r0
    //     0x8d6744: stur            w0, [x3, #0xb]
    // 0x8d6748: r1 = Function '<anonymous closure>': static.
    //     0x8d6748: add             x1, PP, #0x19, lsl #12  ; [pp+0x193f8] AnonymousClosure: static (0x8d6784), in [package:pointycastle/digests/shake.dart] SHAKEDigest::factoryConfig (0x8d66f8)
    //     0x8d674c: ldr             x1, [x1, #0x3f8]
    // 0x8d6750: r2 = Null
    //     0x8d6750: mov             x2, NULL
    // 0x8d6754: r0 = AllocateClosure()
    //     0x8d6754: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d6758: mov             x1, x0
    // 0x8d675c: ldur            x0, [fp, #-0x10]
    // 0x8d6760: StoreField: r0->field_f = r1
    //     0x8d6760: stur            w1, [x0, #0xf]
    // 0x8d6764: r1 = Digest
    //     0x8d6764: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8d6768: ldr             x1, [x1, #0x388]
    // 0x8d676c: StoreField: r0->field_7 = r1
    //     0x8d676c: stur            w1, [x0, #7]
    // 0x8d6770: LeaveFrame
    //     0x8d6770: mov             SP, fp
    //     0x8d6774: ldp             fp, lr, [SP], #0x10
    // 0x8d6778: ret
    //     0x8d6778: ret             
    // 0x8d677c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d677c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6780: b               #0x8d6710
  }
  [closure] static (dynamic) => SHAKEDigest <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8d6784, size: 0x54
    // 0x8d6784: EnterFrame
    //     0x8d6784: stp             fp, lr, [SP, #-0x10]!
    //     0x8d6788: mov             fp, SP
    // 0x8d678c: AllocStack(0x8)
    //     0x8d678c: sub             SP, SP, #8
    // 0x8d6790: SetupParameters()
    //     0x8d6790: ldr             x0, [fp, #0x20]
    //     0x8d6794: ldur            w1, [x0, #0x17]
    //     0x8d6798: add             x1, x1, HEAP, lsl #32
    //     0x8d679c: stur            x1, [fp, #-8]
    // 0x8d67a0: r1 = 1
    //     0x8d67a0: movz            x1, #0x1
    // 0x8d67a4: r0 = AllocateContext()
    //     0x8d67a4: bl              #0xec126c  ; AllocateContextStub
    // 0x8d67a8: mov             x1, x0
    // 0x8d67ac: ldur            x0, [fp, #-8]
    // 0x8d67b0: StoreField: r1->field_b = r0
    //     0x8d67b0: stur            w0, [x1, #0xb]
    // 0x8d67b4: ldr             x0, [fp, #0x10]
    // 0x8d67b8: StoreField: r1->field_f = r0
    //     0x8d67b8: stur            w0, [x1, #0xf]
    // 0x8d67bc: mov             x2, x1
    // 0x8d67c0: r1 = Function '<anonymous closure>': static.
    //     0x8d67c0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19400] AnonymousClosure: static (0x8d67d8), in [package:pointycastle/digests/shake.dart] SHAKEDigest::factoryConfig (0x8d66f8)
    //     0x8d67c4: ldr             x1, [x1, #0x400]
    // 0x8d67c8: r0 = AllocateClosure()
    //     0x8d67c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d67cc: LeaveFrame
    //     0x8d67cc: mov             SP, fp
    //     0x8d67d0: ldp             fp, lr, [SP], #0x10
    // 0x8d67d4: ret
    //     0x8d67d4: ret             
  }
  [closure] static SHAKEDigest <anonymous closure>(dynamic) {
    // ** addr: 0x8d67d8, size: 0xb8
    // 0x8d67d8: EnterFrame
    //     0x8d67d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d67dc: mov             fp, SP
    // 0x8d67e0: AllocStack(0x18)
    //     0x8d67e0: sub             SP, SP, #0x18
    // 0x8d67e4: SetupParameters()
    //     0x8d67e4: ldr             x0, [fp, #0x10]
    //     0x8d67e8: ldur            w1, [x0, #0x17]
    //     0x8d67ec: add             x1, x1, HEAP, lsl #32
    // 0x8d67f0: CheckStackOverflow
    //     0x8d67f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d67f4: cmp             SP, x16
    //     0x8d67f8: b.ls            #0x8d6884
    // 0x8d67fc: LoadField: r0 = r1->field_f
    //     0x8d67fc: ldur            w0, [x1, #0xf]
    // 0x8d6800: DecompressPointer r0
    //     0x8d6800: add             x0, x0, HEAP, lsl #32
    // 0x8d6804: r1 = LoadClassIdInstr(r0)
    //     0x8d6804: ldur            x1, [x0, #-1]
    //     0x8d6808: ubfx            x1, x1, #0xc, #0x14
    // 0x8d680c: mov             x16, x0
    // 0x8d6810: mov             x0, x1
    // 0x8d6814: mov             x1, x16
    // 0x8d6818: r2 = 1
    //     0x8d6818: movz            x2, #0x1
    // 0x8d681c: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8d681c: sub             lr, x0, #0xfdd
    //     0x8d6820: ldr             lr, [x21, lr, lsl #3]
    //     0x8d6824: blr             lr
    // 0x8d6828: cmp             w0, NULL
    // 0x8d682c: b.eq            #0x8d688c
    // 0x8d6830: mov             x1, x0
    // 0x8d6834: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8d6834: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8d6838: r0 = parse()
    //     0x8d6838: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8d683c: mov             x2, x0
    // 0x8d6840: r0 = BoxInt64Instr(r2)
    //     0x8d6840: sbfiz           x0, x2, #1, #0x1f
    //     0x8d6844: cmp             x2, x0, asr #1
    //     0x8d6848: b.eq            #0x8d6854
    //     0x8d684c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d6850: stur            x2, [x0, #7]
    // 0x8d6854: stur            x0, [fp, #-8]
    // 0x8d6858: r0 = SHAKEDigest()
    //     0x8d6858: bl              #0x8d6890  ; AllocateSHAKEDigestStub -> SHAKEDigest (size=0x18)
    // 0x8d685c: stur            x0, [fp, #-0x10]
    // 0x8d6860: ldur            x16, [fp, #-8]
    // 0x8d6864: str             x16, [SP]
    // 0x8d6868: mov             x1, x0
    // 0x8d686c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8d686c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8d6870: r0 = SHAKEDigest()
    //     0x8d6870: bl              #0x8d650c  ; [package:pointycastle/digests/shake.dart] SHAKEDigest::SHAKEDigest
    // 0x8d6874: ldur            x0, [fp, #-0x10]
    // 0x8d6878: LeaveFrame
    //     0x8d6878: mov             SP, fp
    //     0x8d687c: ldp             fp, lr, [SP], #0x10
    // 0x8d6880: ret
    //     0x8d6880: ret             
    // 0x8d6884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d6884: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6888: b               #0x8d67fc
    // 0x8d688c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8d688c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static RegExp _shakeREGEX() {
    // ** addr: 0x8d689c, size: 0x58
    // 0x8d689c: EnterFrame
    //     0x8d689c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d68a0: mov             fp, SP
    // 0x8d68a4: AllocStack(0x30)
    //     0x8d68a4: sub             SP, SP, #0x30
    // 0x8d68a8: CheckStackOverflow
    //     0x8d68a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d68ac: cmp             SP, x16
    //     0x8d68b0: b.ls            #0x8d68ec
    // 0x8d68b4: r16 = "^SHAKE-([0-9]+)$"
    //     0x8d68b4: add             x16, PP, #0x19, lsl #12  ; [pp+0x19408] "^SHAKE-([0-9]+)$"
    //     0x8d68b8: ldr             x16, [x16, #0x408]
    // 0x8d68bc: stp             x16, NULL, [SP, #0x20]
    // 0x8d68c0: r16 = false
    //     0x8d68c0: add             x16, NULL, #0x30  ; false
    // 0x8d68c4: r30 = true
    //     0x8d68c4: add             lr, NULL, #0x20  ; true
    // 0x8d68c8: stp             lr, x16, [SP, #0x10]
    // 0x8d68cc: r16 = false
    //     0x8d68cc: add             x16, NULL, #0x30  ; false
    // 0x8d68d0: r30 = false
    //     0x8d68d0: add             lr, NULL, #0x30  ; false
    // 0x8d68d4: stp             lr, x16, [SP]
    // 0x8d68d8: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8d68d8: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8d68dc: r0 = _RegExp()
    //     0x8d68dc: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8d68e0: LeaveFrame
    //     0x8d68e0: mov             SP, fp
    //     0x8d68e4: ldp             fp, lr, [SP], #0x10
    // 0x8d68e8: ret
    //     0x8d68e8: ret             
    // 0x8d68ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d68ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d68f0: b               #0x8d68b4
  }
}
