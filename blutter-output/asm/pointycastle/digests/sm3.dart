// lib: impl.digest.sm3, url: package:pointycastle/digests/sm3.dart

// class id: 1050959, size: 0x8
class :: {
}

// class id: 652, size: 0x38, field offset: 0x2c
class SM3Digest extends MD4FamilyDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe38

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d5aac, size: 0x58
    // 0x8d5aac: EnterFrame
    //     0x8d5aac: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5ab0: mov             fp, SP
    // 0x8d5ab4: AllocStack(0x8)
    //     0x8d5ab4: sub             SP, SP, #8
    // 0x8d5ab8: r0 = StaticFactoryConfig()
    //     0x8d5ab8: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d5abc: mov             x3, x0
    // 0x8d5ac0: r0 = "SM3"
    //     0x8d5ac0: add             x0, PP, #0x19, lsl #12  ; [pp+0x19378] "SM3"
    //     0x8d5ac4: ldr             x0, [x0, #0x378]
    // 0x8d5ac8: stur            x3, [fp, #-8]
    // 0x8d5acc: StoreField: r3->field_b = r0
    //     0x8d5acc: stur            w0, [x3, #0xb]
    // 0x8d5ad0: r1 = Function '<anonymous closure>': static.
    //     0x8d5ad0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19380] AnonymousClosure: static (0x8d5b04), in [package:pointycastle/digests/sm3.dart] SM3Digest::factoryConfig (0x8d5aac)
    //     0x8d5ad4: ldr             x1, [x1, #0x380]
    // 0x8d5ad8: r2 = Null
    //     0x8d5ad8: mov             x2, NULL
    // 0x8d5adc: r0 = AllocateClosure()
    //     0x8d5adc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d5ae0: mov             x1, x0
    // 0x8d5ae4: ldur            x0, [fp, #-8]
    // 0x8d5ae8: StoreField: r0->field_f = r1
    //     0x8d5ae8: stur            w1, [x0, #0xf]
    // 0x8d5aec: r1 = Digest
    //     0x8d5aec: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8d5af0: ldr             x1, [x1, #0x388]
    // 0x8d5af4: StoreField: r0->field_7 = r1
    //     0x8d5af4: stur            w1, [x0, #7]
    // 0x8d5af8: LeaveFrame
    //     0x8d5af8: mov             SP, fp
    //     0x8d5afc: ldp             fp, lr, [SP], #0x10
    // 0x8d5b00: ret
    //     0x8d5b00: ret             
  }
  [closure] static SM3Digest <anonymous closure>(dynamic) {
    // ** addr: 0x8d5b04, size: 0x40
    // 0x8d5b04: EnterFrame
    //     0x8d5b04: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5b08: mov             fp, SP
    // 0x8d5b0c: AllocStack(0x8)
    //     0x8d5b0c: sub             SP, SP, #8
    // 0x8d5b10: CheckStackOverflow
    //     0x8d5b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5b14: cmp             SP, x16
    //     0x8d5b18: b.ls            #0x8d5b3c
    // 0x8d5b1c: r0 = SM3Digest()
    //     0x8d5b1c: bl              #0x8d6104  ; AllocateSM3DigestStub -> SM3Digest (size=0x38)
    // 0x8d5b20: mov             x1, x0
    // 0x8d5b24: stur            x0, [fp, #-8]
    // 0x8d5b28: r0 = SM3Digest()
    //     0x8d5b28: bl              #0x8d5b44  ; [package:pointycastle/digests/sm3.dart] SM3Digest::SM3Digest
    // 0x8d5b2c: ldur            x0, [fp, #-8]
    // 0x8d5b30: LeaveFrame
    //     0x8d5b30: mov             SP, fp
    //     0x8d5b34: ldp             fp, lr, [SP], #0x10
    // 0x8d5b38: ret
    //     0x8d5b38: ret             
    // 0x8d5b3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5b3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5b40: b               #0x8d5b1c
  }
  _ SM3Digest(/* No info */) {
    // ** addr: 0x8d5b44, size: 0xa8
    // 0x8d5b44: EnterFrame
    //     0x8d5b44: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5b48: mov             fp, SP
    // 0x8d5b4c: AllocStack(0x8)
    //     0x8d5b4c: sub             SP, SP, #8
    // 0x8d5b50: r2 = "SM3"
    //     0x8d5b50: add             x2, PP, #0x19, lsl #12  ; [pp+0x19378] "SM3"
    //     0x8d5b54: ldr             x2, [x2, #0x378]
    // 0x8d5b58: r0 = 32
    //     0x8d5b58: movz            x0, #0x20
    // 0x8d5b5c: mov             x3, x1
    // 0x8d5b60: stur            x1, [fp, #-8]
    // 0x8d5b64: CheckStackOverflow
    //     0x8d5b64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5b68: cmp             SP, x16
    //     0x8d5b6c: b.ls            #0x8d5bdc
    // 0x8d5b70: StoreField: r3->field_2b = r2
    //     0x8d5b70: stur            w2, [x3, #0x2b]
    // 0x8d5b74: StoreField: r3->field_2f = r0
    //     0x8d5b74: stur            x0, [x3, #0x2f]
    // 0x8d5b78: r1 = <int>
    //     0x8d5b78: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8d5b7c: r2 = 136
    //     0x8d5b7c: movz            x2, #0x88
    // 0x8d5b80: r0 = AllocateArray()
    //     0x8d5b80: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8d5b84: r1 = 0
    //     0x8d5b84: movz            x1, #0
    // 0x8d5b88: CheckStackOverflow
    //     0x8d5b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5b8c: cmp             SP, x16
    //     0x8d5b90: b.ls            #0x8d5be4
    // 0x8d5b94: cmp             x1, #0x44
    // 0x8d5b98: b.ge            #0x8d5bb0
    // 0x8d5b9c: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0x8d5b9c: add             x2, x0, x1, lsl #2
    //     0x8d5ba0: stur            wzr, [x2, #0xf]
    // 0x8d5ba4: add             x2, x1, #1
    // 0x8d5ba8: mov             x1, x2
    // 0x8d5bac: b               #0x8d5b88
    // 0x8d5bb0: ldur            x1, [fp, #-8]
    // 0x8d5bb4: r2 = Instance_Endian
    //     0x8d5bb4: add             x2, PP, #0x12, lsl #12  ; [pp+0x12390] Obj!Endian@e2cbb1
    //     0x8d5bb8: ldr             x2, [x2, #0x390]
    // 0x8d5bbc: r3 = 8
    //     0x8d5bbc: movz            x3, #0x8
    // 0x8d5bc0: r5 = 16
    //     0x8d5bc0: movz            x5, #0x10
    // 0x8d5bc4: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8d5bc4: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8d5bc8: r0 = MD4FamilyDigest()
    //     0x8d5bc8: bl              #0x8d5bec  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::MD4FamilyDigest
    // 0x8d5bcc: r0 = Null
    //     0x8d5bcc: mov             x0, NULL
    // 0x8d5bd0: LeaveFrame
    //     0x8d5bd0: mov             SP, fp
    //     0x8d5bd4: ldp             fp, lr, [SP], #0x10
    // 0x8d5bd8: ret
    //     0x8d5bd8: ret             
    // 0x8d5bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5bdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5be0: b               #0x8d5b70
    // 0x8d5be4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5be4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5be8: b               #0x8d5b94
  }
  _ resetState(/* No info */) {
    // ** addr: 0xcde070, size: 0x128
    // 0xcde070: EnterFrame
    //     0xcde070: stp             fp, lr, [SP, #-0x10]!
    //     0xcde074: mov             fp, SP
    // 0xcde078: LoadField: r2 = r1->field_1f
    //     0xcde078: ldur            w2, [x1, #0x1f]
    // 0xcde07c: DecompressPointer r2
    //     0xcde07c: add             x2, x2, HEAP, lsl #32
    // 0xcde080: LoadField: r3 = r2->field_b
    //     0xcde080: ldur            w3, [x2, #0xb]
    // 0xcde084: r4 = LoadInt32Instr(r3)
    //     0xcde084: sbfx            x4, x3, #1, #0x1f
    // 0xcde088: mov             x0, x4
    // 0xcde08c: r1 = 0
    //     0xcde08c: movz            x1, #0
    // 0xcde090: cmp             x1, x0
    // 0xcde094: b.hs            #0xcde178
    // 0xcde098: r16 = 1937774191
    //     0xcde098: add             x16, PP, #0x21, lsl #12  ; [pp+0x21c70] 0x7380166f
    //     0xcde09c: ldr             x16, [x16, #0xc70]
    // 0xcde0a0: StoreField: r2->field_f = r16
    //     0xcde0a0: stur            w16, [x2, #0xf]
    // 0xcde0a4: mov             x0, x4
    // 0xcde0a8: r1 = 1
    //     0xcde0a8: movz            x1, #0x1
    // 0xcde0ac: cmp             x1, x0
    // 0xcde0b0: b.hs            #0xcde17c
    // 0xcde0b4: r16 = 1226093241
    //     0xcde0b4: add             x16, PP, #0x21, lsl #12  ; [pp+0x21c78] 0x4914b2b9
    //     0xcde0b8: ldr             x16, [x16, #0xc78]
    // 0xcde0bc: StoreField: r2->field_13 = r16
    //     0xcde0bc: stur            w16, [x2, #0x13]
    // 0xcde0c0: mov             x0, x4
    // 0xcde0c4: r1 = 2
    //     0xcde0c4: movz            x1, #0x2
    // 0xcde0c8: cmp             x1, x0
    // 0xcde0cc: b.hs            #0xcde180
    // 0xcde0d0: r16 = 776504750
    //     0xcde0d0: movz            x16, #0x85ae
    //     0xcde0d4: movk            x16, #0x2e48, lsl #16
    // 0xcde0d8: ArrayStore: r2[0] = r16  ; List_4
    //     0xcde0d8: stur            w16, [x2, #0x17]
    // 0xcde0dc: mov             x0, x4
    // 0xcde0e0: r1 = 3
    //     0xcde0e0: movz            x1, #0x3
    // 0xcde0e4: cmp             x1, x0
    // 0xcde0e8: b.hs            #0xcde184
    // 0xcde0ec: r16 = 3666478592
    //     0xcde0ec: add             x16, PP, #0x21, lsl #12  ; [pp+0x21c80] 0xda8a0600
    //     0xcde0f0: ldr             x16, [x16, #0xc80]
    // 0xcde0f4: StoreField: r2->field_1b = r16
    //     0xcde0f4: stur            w16, [x2, #0x1b]
    // 0xcde0f8: mov             x0, x4
    // 0xcde0fc: r1 = 4
    //     0xcde0fc: movz            x1, #0x4
    // 0xcde100: cmp             x1, x0
    // 0xcde104: b.hs            #0xcde188
    // 0xcde108: r16 = 2842636476
    //     0xcde108: add             x16, PP, #0x21, lsl #12  ; [pp+0x21c88] 0xa96f30bc
    //     0xcde10c: ldr             x16, [x16, #0xc88]
    // 0xcde110: StoreField: r2->field_1f = r16
    //     0xcde110: stur            w16, [x2, #0x1f]
    // 0xcde114: mov             x0, x4
    // 0xcde118: r1 = 5
    //     0xcde118: movz            x1, #0x5
    // 0xcde11c: cmp             x1, x0
    // 0xcde120: b.hs            #0xcde18c
    // 0xcde124: r16 = 744649044
    //     0xcde124: movz            x16, #0x7154
    //     0xcde128: movk            x16, #0x2c62, lsl #16
    // 0xcde12c: StoreField: r2->field_23 = r16
    //     0xcde12c: stur            w16, [x2, #0x23]
    // 0xcde130: mov             x0, x4
    // 0xcde134: r1 = 6
    //     0xcde134: movz            x1, #0x6
    // 0xcde138: cmp             x1, x0
    // 0xcde13c: b.hs            #0xcde190
    // 0xcde140: r16 = 3817729613
    //     0xcde140: add             x16, PP, #0x21, lsl #12  ; [pp+0x21c90] 0xe38dee4d
    //     0xcde144: ldr             x16, [x16, #0xc90]
    // 0xcde148: StoreField: r2->field_27 = r16
    //     0xcde148: stur            w16, [x2, #0x27]
    // 0xcde14c: mov             x0, x4
    // 0xcde150: r1 = 7
    //     0xcde150: movz            x1, #0x7
    // 0xcde154: cmp             x1, x0
    // 0xcde158: b.hs            #0xcde194
    // 0xcde15c: r16 = 2969243214
    //     0xcde15c: add             x16, PP, #0x21, lsl #12  ; [pp+0x21c98] 0xb0fb0e4e
    //     0xcde160: ldr             x16, [x16, #0xc98]
    // 0xcde164: StoreField: r2->field_2b = r16
    //     0xcde164: stur            w16, [x2, #0x2b]
    // 0xcde168: r0 = Null
    //     0xcde168: mov             x0, NULL
    // 0xcde16c: LeaveFrame
    //     0xcde16c: mov             SP, fp
    //     0xcde170: ldp             fp, lr, [SP], #0x10
    // 0xcde174: ret
    //     0xcde174: ret             
    // 0xcde178: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde178: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde17c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde17c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde180: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde180: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde184: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde184: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde188: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde188: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde18c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde18c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde190: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde190: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcde194: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcde194: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  const get _ digestSize(/* No info */) {
    // ** addr: 0xe61e00, size: 0x8
    // 0xe61e00: LoadField: r0 = r1->field_2f
    //     0xe61e00: ldur            x0, [x1, #0x2f]
    // 0xe61e04: ret
    //     0xe61e04: ret             
  }
}
