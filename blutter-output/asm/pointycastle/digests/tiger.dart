// lib: impl.digest.tiger, url: package:pointycastle/digests/tiger.dart

// class id: 1050960, size: 0x8
class :: {
}

// class id: 646, size: 0x2c, field offset: 0x8
class TigerDigest extends BaseDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe30

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d72dc, size: 0x58
    // 0x8d72dc: EnterFrame
    //     0x8d72dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d72e0: mov             fp, SP
    // 0x8d72e4: AllocStack(0x8)
    //     0x8d72e4: sub             SP, SP, #8
    // 0x8d72e8: r0 = StaticFactoryConfig()
    //     0x8d72e8: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d72ec: mov             x3, x0
    // 0x8d72f0: r0 = "Tiger"
    //     0x8d72f0: add             x0, PP, #0x18, lsl #12  ; [pp+0x18558] "Tiger"
    //     0x8d72f4: ldr             x0, [x0, #0x558]
    // 0x8d72f8: stur            x3, [fp, #-8]
    // 0x8d72fc: StoreField: r3->field_b = r0
    //     0x8d72fc: stur            w0, [x3, #0xb]
    // 0x8d7300: r1 = Function '<anonymous closure>': static.
    //     0x8d7300: add             x1, PP, #0x19, lsl #12  ; [pp+0x19420] AnonymousClosure: static (0x8d7334), in [package:pointycastle/digests/tiger.dart] TigerDigest::factoryConfig (0x8d72dc)
    //     0x8d7304: ldr             x1, [x1, #0x420]
    // 0x8d7308: r2 = Null
    //     0x8d7308: mov             x2, NULL
    // 0x8d730c: r0 = AllocateClosure()
    //     0x8d730c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d7310: mov             x1, x0
    // 0x8d7314: ldur            x0, [fp, #-8]
    // 0x8d7318: StoreField: r0->field_f = r1
    //     0x8d7318: stur            w1, [x0, #0xf]
    // 0x8d731c: r1 = Digest
    //     0x8d731c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8d7320: ldr             x1, [x1, #0x388]
    // 0x8d7324: StoreField: r0->field_7 = r1
    //     0x8d7324: stur            w1, [x0, #7]
    // 0x8d7328: LeaveFrame
    //     0x8d7328: mov             SP, fp
    //     0x8d732c: ldp             fp, lr, [SP], #0x10
    // 0x8d7330: ret
    //     0x8d7330: ret             
  }
  [closure] static TigerDigest <anonymous closure>(dynamic) {
    // ** addr: 0x8d7334, size: 0x40
    // 0x8d7334: EnterFrame
    //     0x8d7334: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7338: mov             fp, SP
    // 0x8d733c: AllocStack(0x8)
    //     0x8d733c: sub             SP, SP, #8
    // 0x8d7340: CheckStackOverflow
    //     0x8d7340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7344: cmp             SP, x16
    //     0x8d7348: b.ls            #0x8d736c
    // 0x8d734c: r0 = TigerDigest()
    //     0x8d734c: bl              #0x8d7738  ; AllocateTigerDigestStub -> TigerDigest (size=0x2c)
    // 0x8d7350: mov             x1, x0
    // 0x8d7354: stur            x0, [fp, #-8]
    // 0x8d7358: r0 = TigerDigest()
    //     0x8d7358: bl              #0x8d7374  ; [package:pointycastle/digests/tiger.dart] TigerDigest::TigerDigest
    // 0x8d735c: ldur            x0, [fp, #-8]
    // 0x8d7360: LeaveFrame
    //     0x8d7360: mov             SP, fp
    //     0x8d7364: ldp             fp, lr, [SP], #0x10
    // 0x8d7368: ret
    //     0x8d7368: ret             
    // 0x8d736c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d736c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7370: b               #0x8d734c
  }
  _ TigerDigest(/* No info */) {
    // ** addr: 0x8d7374, size: 0x2c0
    // 0x8d7374: EnterFrame
    //     0x8d7374: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7378: mov             fp, SP
    // 0x8d737c: AllocStack(0x28)
    //     0x8d737c: sub             SP, SP, #0x28
    // 0x8d7380: r2 = "Tiger"
    //     0x8d7380: add             x2, PP, #0x18, lsl #12  ; [pp+0x18558] "Tiger"
    //     0x8d7384: ldr             x2, [x2, #0x558]
    // 0x8d7388: r0 = 24
    //     0x8d7388: movz            x0, #0x18
    // 0x8d738c: stur            x1, [fp, #-8]
    // 0x8d7390: CheckStackOverflow
    //     0x8d7390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7394: cmp             SP, x16
    //     0x8d7398: b.ls            #0x8d7620
    // 0x8d739c: StoreField: r1->field_1f = r2
    //     0x8d739c: stur            w2, [x1, #0x1f]
    // 0x8d73a0: StoreField: r1->field_23 = r0
    //     0x8d73a0: stur            x0, [x1, #0x23]
    // 0x8d73a4: r0 = Register64()
    //     0x8d73a4: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d73a8: mov             x3, x0
    // 0x8d73ac: r0 = Sentinel
    //     0x8d73ac: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d73b0: stur            x3, [fp, #-0x10]
    // 0x8d73b4: StoreField: r3->field_7 = r0
    //     0x8d73b4: stur            w0, [x3, #7]
    // 0x8d73b8: StoreField: r3->field_b = r0
    //     0x8d73b8: stur            w0, [x3, #0xb]
    // 0x8d73bc: str             NULL, [SP]
    // 0x8d73c0: mov             x1, x3
    // 0x8d73c4: r2 = 0
    //     0x8d73c4: movz            x2, #0
    // 0x8d73c8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d73c8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d73cc: r0 = set()
    //     0x8d73cc: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d73d0: ldur            x0, [fp, #-0x10]
    // 0x8d73d4: ldur            x1, [fp, #-8]
    // 0x8d73d8: StoreField: r1->field_7 = r0
    //     0x8d73d8: stur            w0, [x1, #7]
    //     0x8d73dc: ldurb           w16, [x1, #-1]
    //     0x8d73e0: ldurb           w17, [x0, #-1]
    //     0x8d73e4: and             x16, x17, x16, lsr #2
    //     0x8d73e8: tst             x16, HEAP, lsr #32
    //     0x8d73ec: b.eq            #0x8d73f4
    //     0x8d73f0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d73f4: r0 = Register64()
    //     0x8d73f4: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d73f8: mov             x3, x0
    // 0x8d73fc: r0 = Sentinel
    //     0x8d73fc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d7400: stur            x3, [fp, #-0x10]
    // 0x8d7404: StoreField: r3->field_7 = r0
    //     0x8d7404: stur            w0, [x3, #7]
    // 0x8d7408: StoreField: r3->field_b = r0
    //     0x8d7408: stur            w0, [x3, #0xb]
    // 0x8d740c: str             NULL, [SP]
    // 0x8d7410: mov             x1, x3
    // 0x8d7414: r2 = 0
    //     0x8d7414: movz            x2, #0
    // 0x8d7418: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7418: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d741c: r0 = set()
    //     0x8d741c: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7420: ldur            x0, [fp, #-0x10]
    // 0x8d7424: ldur            x1, [fp, #-8]
    // 0x8d7428: StoreField: r1->field_b = r0
    //     0x8d7428: stur            w0, [x1, #0xb]
    //     0x8d742c: ldurb           w16, [x1, #-1]
    //     0x8d7430: ldurb           w17, [x0, #-1]
    //     0x8d7434: and             x16, x17, x16, lsr #2
    //     0x8d7438: tst             x16, HEAP, lsr #32
    //     0x8d743c: b.eq            #0x8d7444
    //     0x8d7440: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d7444: r0 = Register64()
    //     0x8d7444: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d7448: mov             x3, x0
    // 0x8d744c: r0 = Sentinel
    //     0x8d744c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d7450: stur            x3, [fp, #-0x10]
    // 0x8d7454: StoreField: r3->field_7 = r0
    //     0x8d7454: stur            w0, [x3, #7]
    // 0x8d7458: StoreField: r3->field_b = r0
    //     0x8d7458: stur            w0, [x3, #0xb]
    // 0x8d745c: str             NULL, [SP]
    // 0x8d7460: mov             x1, x3
    // 0x8d7464: r2 = 0
    //     0x8d7464: movz            x2, #0
    // 0x8d7468: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7468: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d746c: r0 = set()
    //     0x8d746c: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7470: ldur            x0, [fp, #-0x10]
    // 0x8d7474: ldur            x1, [fp, #-8]
    // 0x8d7478: StoreField: r1->field_f = r0
    //     0x8d7478: stur            w0, [x1, #0xf]
    //     0x8d747c: ldurb           w16, [x1, #-1]
    //     0x8d7480: ldurb           w17, [x0, #-1]
    //     0x8d7484: and             x16, x17, x16, lsr #2
    //     0x8d7488: tst             x16, HEAP, lsr #32
    //     0x8d748c: b.eq            #0x8d7494
    //     0x8d7490: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d7494: r0 = Register64()
    //     0x8d7494: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d7498: mov             x3, x0
    // 0x8d749c: r0 = Sentinel
    //     0x8d749c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d74a0: stur            x3, [fp, #-0x10]
    // 0x8d74a4: StoreField: r3->field_7 = r0
    //     0x8d74a4: stur            w0, [x3, #7]
    // 0x8d74a8: StoreField: r3->field_b = r0
    //     0x8d74a8: stur            w0, [x3, #0xb]
    // 0x8d74ac: str             NULL, [SP]
    // 0x8d74b0: mov             x1, x3
    // 0x8d74b4: r2 = 0
    //     0x8d74b4: movz            x2, #0
    // 0x8d74b8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d74b8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d74bc: r0 = set()
    //     0x8d74bc: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d74c0: ldur            x0, [fp, #-0x10]
    // 0x8d74c4: ldur            x1, [fp, #-8]
    // 0x8d74c8: StoreField: r1->field_13 = r0
    //     0x8d74c8: stur            w0, [x1, #0x13]
    //     0x8d74cc: ldurb           w16, [x1, #-1]
    //     0x8d74d0: ldurb           w17, [x0, #-1]
    //     0x8d74d4: and             x16, x17, x16, lsr #2
    //     0x8d74d8: tst             x16, HEAP, lsr #32
    //     0x8d74dc: b.eq            #0x8d74e4
    //     0x8d74e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d74e4: r4 = 16
    //     0x8d74e4: movz            x4, #0x10
    // 0x8d74e8: r0 = AllocateUint8Array()
    //     0x8d74e8: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8d74ec: ldur            x3, [fp, #-8]
    // 0x8d74f0: ArrayStore: r3[0] = r0  ; List_4
    //     0x8d74f0: stur            w0, [x3, #0x17]
    //     0x8d74f4: ldurb           w16, [x3, #-1]
    //     0x8d74f8: ldurb           w17, [x0, #-1]
    //     0x8d74fc: and             x16, x17, x16, lsr #2
    //     0x8d7500: tst             x16, HEAP, lsr #32
    //     0x8d7504: b.eq            #0x8d750c
    //     0x8d7508: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d750c: r1 = <Register64>
    //     0x8d750c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8d7510: ldr             x1, [x1, #0x418]
    // 0x8d7514: r2 = 8
    //     0x8d7514: movz            x2, #0x8
    // 0x8d7518: r0 = _GrowableList()
    //     0x8d7518: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8d751c: stur            x0, [fp, #-0x10]
    // 0x8d7520: r1 = 0
    //     0x8d7520: movz            x1, #0
    // 0x8d7524: stur            x1, [fp, #-0x18]
    // 0x8d7528: CheckStackOverflow
    //     0x8d7528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d752c: cmp             SP, x16
    //     0x8d7530: b.ls            #0x8d7628
    // 0x8d7534: LoadField: r2 = r0->field_b
    //     0x8d7534: ldur            w2, [x0, #0xb]
    // 0x8d7538: r3 = LoadInt32Instr(r2)
    //     0x8d7538: sbfx            x3, x2, #1, #0x1f
    // 0x8d753c: cmp             x1, x3
    // 0x8d7540: b.ge            #0x8d75d0
    // 0x8d7544: r0 = Register64()
    //     0x8d7544: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d7548: mov             x3, x0
    // 0x8d754c: r0 = Sentinel
    //     0x8d754c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d7550: stur            x3, [fp, #-0x20]
    // 0x8d7554: StoreField: r3->field_7 = r0
    //     0x8d7554: stur            w0, [x3, #7]
    // 0x8d7558: StoreField: r3->field_b = r0
    //     0x8d7558: stur            w0, [x3, #0xb]
    // 0x8d755c: str             NULL, [SP]
    // 0x8d7560: mov             x1, x3
    // 0x8d7564: r2 = 0
    //     0x8d7564: movz            x2, #0
    // 0x8d7568: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7568: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d756c: r0 = set()
    //     0x8d756c: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7570: ldur            x2, [fp, #-0x10]
    // 0x8d7574: LoadField: r0 = r2->field_b
    //     0x8d7574: ldur            w0, [x2, #0xb]
    // 0x8d7578: r1 = LoadInt32Instr(r0)
    //     0x8d7578: sbfx            x1, x0, #1, #0x1f
    // 0x8d757c: mov             x0, x1
    // 0x8d7580: ldur            x1, [fp, #-0x18]
    // 0x8d7584: cmp             x1, x0
    // 0x8d7588: b.hs            #0x8d7630
    // 0x8d758c: LoadField: r1 = r2->field_f
    //     0x8d758c: ldur            w1, [x2, #0xf]
    // 0x8d7590: DecompressPointer r1
    //     0x8d7590: add             x1, x1, HEAP, lsl #32
    // 0x8d7594: ldur            x0, [fp, #-0x20]
    // 0x8d7598: ldur            x3, [fp, #-0x18]
    // 0x8d759c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8d759c: add             x25, x1, x3, lsl #2
    //     0x8d75a0: add             x25, x25, #0xf
    //     0x8d75a4: str             w0, [x25]
    //     0x8d75a8: tbz             w0, #0, #0x8d75c4
    //     0x8d75ac: ldurb           w16, [x1, #-1]
    //     0x8d75b0: ldurb           w17, [x0, #-1]
    //     0x8d75b4: and             x16, x17, x16, lsr #2
    //     0x8d75b8: tst             x16, HEAP, lsr #32
    //     0x8d75bc: b.eq            #0x8d75c4
    //     0x8d75c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8d75c4: add             x1, x3, #1
    // 0x8d75c8: mov             x0, x2
    // 0x8d75cc: b               #0x8d7524
    // 0x8d75d0: ldur            x1, [fp, #-8]
    // 0x8d75d4: mov             x2, x0
    // 0x8d75d8: r0 = Register64List()
    //     0x8d75d8: bl              #0x8d72c4  ; AllocateRegister64ListStub -> Register64List (size=0xc)
    // 0x8d75dc: mov             x1, x0
    // 0x8d75e0: ldur            x0, [fp, #-0x10]
    // 0x8d75e4: StoreField: r1->field_7 = r0
    //     0x8d75e4: stur            w0, [x1, #7]
    // 0x8d75e8: mov             x0, x1
    // 0x8d75ec: ldur            x1, [fp, #-8]
    // 0x8d75f0: StoreField: r1->field_1b = r0
    //     0x8d75f0: stur            w0, [x1, #0x1b]
    //     0x8d75f4: ldurb           w16, [x1, #-1]
    //     0x8d75f8: ldurb           w17, [x0, #-1]
    //     0x8d75fc: and             x16, x17, x16, lsr #2
    //     0x8d7600: tst             x16, HEAP, lsr #32
    //     0x8d7604: b.eq            #0x8d760c
    //     0x8d7608: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d760c: r0 = reset()
    //     0x8d760c: bl              #0x8d7634  ; [package:pointycastle/digests/tiger.dart] TigerDigest::reset
    // 0x8d7610: r0 = Null
    //     0x8d7610: mov             x0, NULL
    // 0x8d7614: LeaveFrame
    //     0x8d7614: mov             SP, fp
    //     0x8d7618: ldp             fp, lr, [SP], #0x10
    // 0x8d761c: ret
    //     0x8d761c: ret             
    // 0x8d7620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7620: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7624: b               #0x8d739c
    // 0x8d7628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7628: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d762c: b               #0x8d7534
    // 0x8d7630: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d7630: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ reset(/* No info */) {
    // ** addr: 0x8d7634, size: 0x104
    // 0x8d7634: EnterFrame
    //     0x8d7634: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7638: mov             fp, SP
    // 0x8d763c: AllocStack(0x10)
    //     0x8d763c: sub             SP, SP, #0x10
    // 0x8d7640: SetupParameters(TigerDigest this /* r1 => r0, fp-0x8 */)
    //     0x8d7640: mov             x0, x1
    //     0x8d7644: stur            x1, [fp, #-8]
    // 0x8d7648: CheckStackOverflow
    //     0x8d7648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d764c: cmp             SP, x16
    //     0x8d7650: b.ls            #0x8d7730
    // 0x8d7654: LoadField: r1 = r0->field_7
    //     0x8d7654: ldur            w1, [x0, #7]
    // 0x8d7658: DecompressPointer r1
    //     0x8d7658: add             x1, x1, HEAP, lsl #32
    // 0x8d765c: r16 = 2309737967
    //     0x8d765c: add             x16, PP, #0x19, lsl #12  ; [pp+0x19428] 0x89abcdef
    //     0x8d7660: ldr             x16, [x16, #0x428]
    // 0x8d7664: str             x16, [SP]
    // 0x8d7668: r2 = 38177486
    //     0x8d7668: movz            x2, #0x8ace
    //     0x8d766c: movk            x2, #0x246, lsl #16
    // 0x8d7670: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7670: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7674: r0 = set()
    //     0x8d7674: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7678: ldur            x0, [fp, #-8]
    // 0x8d767c: LoadField: r1 = r0->field_b
    //     0x8d767c: ldur            w1, [x0, #0xb]
    // 0x8d7680: DecompressPointer r1
    //     0x8d7680: add             x1, x1, HEAP, lsl #32
    // 0x8d7684: r16 = 1985229328
    //     0x8d7684: add             x16, PP, #0x19, lsl #12  ; [pp+0x19430] 0x76543210
    //     0x8d7688: ldr             x16, [x16, #0x430]
    // 0x8d768c: str             x16, [SP]
    // 0x8d7690: r2 = 4275878552
    //     0x8d7690: add             x2, PP, #0x19, lsl #12  ; [pp+0x19438] 0xfedcba98
    //     0x8d7694: ldr             x2, [x2, #0x438]
    // 0x8d7698: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7698: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d769c: r0 = set()
    //     0x8d769c: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d76a0: ldur            x0, [fp, #-8]
    // 0x8d76a4: LoadField: r1 = r0->field_f
    //     0x8d76a4: ldur            w1, [x0, #0xf]
    // 0x8d76a8: DecompressPointer r1
    //     0x8d76a8: add             x1, x1, HEAP, lsl #32
    // 0x8d76ac: r16 = 3283280263
    //     0x8d76ac: add             x16, PP, #0x19, lsl #12  ; [pp+0x19440] 0xc3b2e187
    //     0x8d76b0: ldr             x16, [x16, #0x440]
    // 0x8d76b4: str             x16, [SP]
    // 0x8d76b8: r2 = 4036404660
    //     0x8d76b8: add             x2, PP, #0x19, lsl #12  ; [pp+0x19448] 0xf096a5b4
    //     0x8d76bc: ldr             x2, [x2, #0x448]
    // 0x8d76c0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d76c0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d76c4: r0 = set()
    //     0x8d76c4: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d76c8: ldur            x0, [fp, #-8]
    // 0x8d76cc: LoadField: r1 = r0->field_1b
    //     0x8d76cc: ldur            w1, [x0, #0x1b]
    // 0x8d76d0: DecompressPointer r1
    //     0x8d76d0: add             x1, x1, HEAP, lsl #32
    // 0x8d76d4: LoadField: r2 = r1->field_7
    //     0x8d76d4: ldur            w2, [x1, #7]
    // 0x8d76d8: DecompressPointer r2
    //     0x8d76d8: add             x2, x2, HEAP, lsl #32
    // 0x8d76dc: LoadField: r3 = r2->field_b
    //     0x8d76dc: ldur            w3, [x2, #0xb]
    // 0x8d76e0: r2 = LoadInt32Instr(r3)
    //     0x8d76e0: sbfx            x2, x3, #1, #0x1f
    // 0x8d76e4: r0 = fillRange()
    //     0x8d76e4: bl              #0x8d7208  ; [package:pointycastle/src/ufixnum.dart] Register64List::fillRange
    // 0x8d76e8: ldur            x0, [fp, #-8]
    // 0x8d76ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8d76ec: ldur            w1, [x0, #0x17]
    // 0x8d76f0: DecompressPointer r1
    //     0x8d76f0: add             x1, x1, HEAP, lsl #32
    // 0x8d76f4: LoadField: r2 = r1->field_13
    //     0x8d76f4: ldur            w2, [x1, #0x13]
    // 0x8d76f8: r3 = LoadInt32Instr(r2)
    //     0x8d76f8: sbfx            x3, x2, #1, #0x1f
    // 0x8d76fc: r2 = 0
    //     0x8d76fc: movz            x2, #0
    // 0x8d7700: r5 = 0
    //     0x8d7700: movz            x5, #0
    // 0x8d7704: r0 = fillRange()
    //     0x8d7704: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0x8d7708: ldur            x0, [fp, #-8]
    // 0x8d770c: LoadField: r1 = r0->field_13
    //     0x8d770c: ldur            w1, [x0, #0x13]
    // 0x8d7710: DecompressPointer r1
    //     0x8d7710: add             x1, x1, HEAP, lsl #32
    // 0x8d7714: r2 = 0
    //     0x8d7714: movz            x2, #0
    // 0x8d7718: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d7718: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d771c: r0 = set()
    //     0x8d771c: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d7720: r0 = Null
    //     0x8d7720: mov             x0, NULL
    // 0x8d7724: LeaveFrame
    //     0x8d7724: mov             SP, fp
    //     0x8d7728: ldp             fp, lr, [SP], #0x10
    // 0x8d772c: ret
    //     0x8d772c: ret             
    // 0x8d7730: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7730: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7734: b               #0x8d7654
  }
  const get _ digestSize(/* No info */) {
    // ** addr: 0xe61e18, size: 0x8
    // 0xe61e18: LoadField: r0 = r1->field_23
    //     0xe61e18: ldur            x0, [x1, #0x23]
    // 0xe61e1c: ret
    //     0xe61e1c: ret             
  }
}
