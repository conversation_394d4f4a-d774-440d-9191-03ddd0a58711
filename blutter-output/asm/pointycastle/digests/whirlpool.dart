// lib: impl.digest.whirlpool, url: package:pointycastle/digests/whirlpool.dart

// class id: 1050961, size: 0x8
class :: {
}

// class id: 645, size: 0x30, field offset: 0x8
class WhirlpoolDigest extends BaseDigest
    implements Digest {

  static late final FactoryConfig factoryConfig; // offset: 0xe34

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d68f4, size: 0x58
    // 0x8d68f4: EnterFrame
    //     0x8d68f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d68f8: mov             fp, SP
    // 0x8d68fc: AllocStack(0x8)
    //     0x8d68fc: sub             SP, SP, #8
    // 0x8d6900: r0 = StaticFactoryConfig()
    //     0x8d6900: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d6904: mov             x3, x0
    // 0x8d6908: r0 = "Whirlpool"
    //     0x8d6908: add             x0, PP, #0x18, lsl #12  ; [pp+0x18560] "Whirlpool"
    //     0x8d690c: ldr             x0, [x0, #0x560]
    // 0x8d6910: stur            x3, [fp, #-8]
    // 0x8d6914: StoreField: r3->field_b = r0
    //     0x8d6914: stur            w0, [x3, #0xb]
    // 0x8d6918: r1 = Function '<anonymous closure>': static.
    //     0x8d6918: add             x1, PP, #0x19, lsl #12  ; [pp+0x19410] AnonymousClosure: static (0x8d694c), in [package:pointycastle/digests/whirlpool.dart] WhirlpoolDigest::factoryConfig (0x8d68f4)
    //     0x8d691c: ldr             x1, [x1, #0x410]
    // 0x8d6920: r2 = Null
    //     0x8d6920: mov             x2, NULL
    // 0x8d6924: r0 = AllocateClosure()
    //     0x8d6924: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d6928: mov             x1, x0
    // 0x8d692c: ldur            x0, [fp, #-8]
    // 0x8d6930: StoreField: r0->field_f = r1
    //     0x8d6930: stur            w1, [x0, #0xf]
    // 0x8d6934: r1 = Digest
    //     0x8d6934: add             x1, PP, #0x19, lsl #12  ; [pp+0x19388] Type: Digest
    //     0x8d6938: ldr             x1, [x1, #0x388]
    // 0x8d693c: StoreField: r0->field_7 = r1
    //     0x8d693c: stur            w1, [x0, #7]
    // 0x8d6940: LeaveFrame
    //     0x8d6940: mov             SP, fp
    //     0x8d6944: ldp             fp, lr, [SP], #0x10
    // 0x8d6948: ret
    //     0x8d6948: ret             
  }
  [closure] static WhirlpoolDigest <anonymous closure>(dynamic) {
    // ** addr: 0x8d694c, size: 0x40
    // 0x8d694c: EnterFrame
    //     0x8d694c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d6950: mov             fp, SP
    // 0x8d6954: AllocStack(0x8)
    //     0x8d6954: sub             SP, SP, #8
    // 0x8d6958: CheckStackOverflow
    //     0x8d6958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d695c: cmp             SP, x16
    //     0x8d6960: b.ls            #0x8d6984
    // 0x8d6964: r0 = WhirlpoolDigest()
    //     0x8d6964: bl              #0x8d72d0  ; AllocateWhirlpoolDigestStub -> WhirlpoolDigest (size=0x30)
    // 0x8d6968: mov             x1, x0
    // 0x8d696c: stur            x0, [fp, #-8]
    // 0x8d6970: r0 = WhirlpoolDigest()
    //     0x8d6970: bl              #0x8d698c  ; [package:pointycastle/digests/whirlpool.dart] WhirlpoolDigest::WhirlpoolDigest
    // 0x8d6974: ldur            x0, [fp, #-8]
    // 0x8d6978: LeaveFrame
    //     0x8d6978: mov             SP, fp
    //     0x8d697c: ldp             fp, lr, [SP], #0x10
    // 0x8d6980: ret
    //     0x8d6980: ret             
    // 0x8d6984: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d6984: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6988: b               #0x8d6964
  }
  _ WhirlpoolDigest(/* No info */) {
    // ** addr: 0x8d698c, size: 0x6bc
    // 0x8d698c: EnterFrame
    //     0x8d698c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d6990: mov             fp, SP
    // 0x8d6994: AllocStack(0x28)
    //     0x8d6994: sub             SP, SP, #0x28
    // 0x8d6998: r2 = "Whirlpool"
    //     0x8d6998: add             x2, PP, #0x18, lsl #12  ; [pp+0x18560] "Whirlpool"
    //     0x8d699c: ldr             x2, [x2, #0x560]
    // 0x8d69a0: r0 = 64
    //     0x8d69a0: movz            x0, #0x40
    // 0x8d69a4: stur            x1, [fp, #-8]
    // 0x8d69a8: CheckStackOverflow
    //     0x8d69a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d69ac: cmp             SP, x16
    //     0x8d69b0: b.ls            #0x8d6ff8
    // 0x8d69b4: StoreField: r1->field_23 = r2
    //     0x8d69b4: stur            w2, [x1, #0x23]
    // 0x8d69b8: StoreField: r1->field_27 = r0
    //     0x8d69b8: stur            x0, [x1, #0x27]
    // 0x8d69bc: r4 = 128
    //     0x8d69bc: movz            x4, #0x80
    // 0x8d69c0: r0 = AllocateUint8Array()
    //     0x8d69c0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8d69c4: ldur            x3, [fp, #-8]
    // 0x8d69c8: StoreField: r3->field_7 = r0
    //     0x8d69c8: stur            w0, [x3, #7]
    //     0x8d69cc: ldurb           w16, [x3, #-1]
    //     0x8d69d0: ldurb           w17, [x0, #-1]
    //     0x8d69d4: and             x16, x17, x16, lsr #2
    //     0x8d69d8: tst             x16, HEAP, lsr #32
    //     0x8d69dc: b.eq            #0x8d69e4
    //     0x8d69e0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d69e4: r1 = <Register64>
    //     0x8d69e4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8d69e8: ldr             x1, [x1, #0x418]
    // 0x8d69ec: r2 = 4
    //     0x8d69ec: movz            x2, #0x4
    // 0x8d69f0: r0 = _GrowableList()
    //     0x8d69f0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8d69f4: stur            x0, [fp, #-0x18]
    // 0x8d69f8: r1 = 0
    //     0x8d69f8: movz            x1, #0
    // 0x8d69fc: stur            x1, [fp, #-0x10]
    // 0x8d6a00: CheckStackOverflow
    //     0x8d6a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6a04: cmp             SP, x16
    //     0x8d6a08: b.ls            #0x8d7000
    // 0x8d6a0c: LoadField: r2 = r0->field_b
    //     0x8d6a0c: ldur            w2, [x0, #0xb]
    // 0x8d6a10: r3 = LoadInt32Instr(r2)
    //     0x8d6a10: sbfx            x3, x2, #1, #0x1f
    // 0x8d6a14: cmp             x1, x3
    // 0x8d6a18: b.ge            #0x8d6aa8
    // 0x8d6a1c: r0 = Register64()
    //     0x8d6a1c: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d6a20: mov             x3, x0
    // 0x8d6a24: r0 = Sentinel
    //     0x8d6a24: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d6a28: stur            x3, [fp, #-0x20]
    // 0x8d6a2c: StoreField: r3->field_7 = r0
    //     0x8d6a2c: stur            w0, [x3, #7]
    // 0x8d6a30: StoreField: r3->field_b = r0
    //     0x8d6a30: stur            w0, [x3, #0xb]
    // 0x8d6a34: str             NULL, [SP]
    // 0x8d6a38: mov             x1, x3
    // 0x8d6a3c: r2 = 0
    //     0x8d6a3c: movz            x2, #0
    // 0x8d6a40: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d6a40: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d6a44: r0 = set()
    //     0x8d6a44: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d6a48: ldur            x2, [fp, #-0x18]
    // 0x8d6a4c: LoadField: r0 = r2->field_b
    //     0x8d6a4c: ldur            w0, [x2, #0xb]
    // 0x8d6a50: r1 = LoadInt32Instr(r0)
    //     0x8d6a50: sbfx            x1, x0, #1, #0x1f
    // 0x8d6a54: mov             x0, x1
    // 0x8d6a58: ldur            x1, [fp, #-0x10]
    // 0x8d6a5c: cmp             x1, x0
    // 0x8d6a60: b.hs            #0x8d7008
    // 0x8d6a64: LoadField: r1 = r2->field_f
    //     0x8d6a64: ldur            w1, [x2, #0xf]
    // 0x8d6a68: DecompressPointer r1
    //     0x8d6a68: add             x1, x1, HEAP, lsl #32
    // 0x8d6a6c: ldur            x0, [fp, #-0x20]
    // 0x8d6a70: ldur            x3, [fp, #-0x10]
    // 0x8d6a74: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8d6a74: add             x25, x1, x3, lsl #2
    //     0x8d6a78: add             x25, x25, #0xf
    //     0x8d6a7c: str             w0, [x25]
    //     0x8d6a80: tbz             w0, #0, #0x8d6a9c
    //     0x8d6a84: ldurb           w16, [x1, #-1]
    //     0x8d6a88: ldurb           w17, [x0, #-1]
    //     0x8d6a8c: and             x16, x17, x16, lsr #2
    //     0x8d6a90: tst             x16, HEAP, lsr #32
    //     0x8d6a94: b.eq            #0x8d6a9c
    //     0x8d6a98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8d6a9c: add             x1, x3, #1
    // 0x8d6aa0: mov             x0, x2
    // 0x8d6aa4: b               #0x8d69fc
    // 0x8d6aa8: ldur            x1, [fp, #-8]
    // 0x8d6aac: mov             x2, x0
    // 0x8d6ab0: r0 = Register64List()
    //     0x8d6ab0: bl              #0x8d72c4  ; AllocateRegister64ListStub -> Register64List (size=0xc)
    // 0x8d6ab4: mov             x1, x0
    // 0x8d6ab8: ldur            x0, [fp, #-0x18]
    // 0x8d6abc: StoreField: r1->field_7 = r0
    //     0x8d6abc: stur            w0, [x1, #7]
    // 0x8d6ac0: mov             x0, x1
    // 0x8d6ac4: ldur            x3, [fp, #-8]
    // 0x8d6ac8: StoreField: r3->field_b = r0
    //     0x8d6ac8: stur            w0, [x3, #0xb]
    //     0x8d6acc: ldurb           w16, [x3, #-1]
    //     0x8d6ad0: ldurb           w17, [x0, #-1]
    //     0x8d6ad4: and             x16, x17, x16, lsr #2
    //     0x8d6ad8: tst             x16, HEAP, lsr #32
    //     0x8d6adc: b.eq            #0x8d6ae4
    //     0x8d6ae0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d6ae4: r1 = <Register64>
    //     0x8d6ae4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8d6ae8: ldr             x1, [x1, #0x418]
    // 0x8d6aec: r2 = 8
    //     0x8d6aec: movz            x2, #0x8
    // 0x8d6af0: r0 = _GrowableList()
    //     0x8d6af0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8d6af4: stur            x0, [fp, #-0x18]
    // 0x8d6af8: r1 = 0
    //     0x8d6af8: movz            x1, #0
    // 0x8d6afc: stur            x1, [fp, #-0x10]
    // 0x8d6b00: CheckStackOverflow
    //     0x8d6b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6b04: cmp             SP, x16
    //     0x8d6b08: b.ls            #0x8d700c
    // 0x8d6b0c: LoadField: r2 = r0->field_b
    //     0x8d6b0c: ldur            w2, [x0, #0xb]
    // 0x8d6b10: r3 = LoadInt32Instr(r2)
    //     0x8d6b10: sbfx            x3, x2, #1, #0x1f
    // 0x8d6b14: cmp             x1, x3
    // 0x8d6b18: b.ge            #0x8d6ba8
    // 0x8d6b1c: r0 = Register64()
    //     0x8d6b1c: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d6b20: mov             x3, x0
    // 0x8d6b24: r0 = Sentinel
    //     0x8d6b24: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d6b28: stur            x3, [fp, #-0x20]
    // 0x8d6b2c: StoreField: r3->field_7 = r0
    //     0x8d6b2c: stur            w0, [x3, #7]
    // 0x8d6b30: StoreField: r3->field_b = r0
    //     0x8d6b30: stur            w0, [x3, #0xb]
    // 0x8d6b34: str             NULL, [SP]
    // 0x8d6b38: mov             x1, x3
    // 0x8d6b3c: r2 = 0
    //     0x8d6b3c: movz            x2, #0
    // 0x8d6b40: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d6b40: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d6b44: r0 = set()
    //     0x8d6b44: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d6b48: ldur            x2, [fp, #-0x18]
    // 0x8d6b4c: LoadField: r0 = r2->field_b
    //     0x8d6b4c: ldur            w0, [x2, #0xb]
    // 0x8d6b50: r1 = LoadInt32Instr(r0)
    //     0x8d6b50: sbfx            x1, x0, #1, #0x1f
    // 0x8d6b54: mov             x0, x1
    // 0x8d6b58: ldur            x1, [fp, #-0x10]
    // 0x8d6b5c: cmp             x1, x0
    // 0x8d6b60: b.hs            #0x8d7014
    // 0x8d6b64: LoadField: r1 = r2->field_f
    //     0x8d6b64: ldur            w1, [x2, #0xf]
    // 0x8d6b68: DecompressPointer r1
    //     0x8d6b68: add             x1, x1, HEAP, lsl #32
    // 0x8d6b6c: ldur            x0, [fp, #-0x20]
    // 0x8d6b70: ldur            x3, [fp, #-0x10]
    // 0x8d6b74: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8d6b74: add             x25, x1, x3, lsl #2
    //     0x8d6b78: add             x25, x25, #0xf
    //     0x8d6b7c: str             w0, [x25]
    //     0x8d6b80: tbz             w0, #0, #0x8d6b9c
    //     0x8d6b84: ldurb           w16, [x1, #-1]
    //     0x8d6b88: ldurb           w17, [x0, #-1]
    //     0x8d6b8c: and             x16, x17, x16, lsr #2
    //     0x8d6b90: tst             x16, HEAP, lsr #32
    //     0x8d6b94: b.eq            #0x8d6b9c
    //     0x8d6b98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8d6b9c: add             x1, x3, #1
    // 0x8d6ba0: mov             x0, x2
    // 0x8d6ba4: b               #0x8d6afc
    // 0x8d6ba8: ldur            x1, [fp, #-8]
    // 0x8d6bac: mov             x2, x0
    // 0x8d6bb0: r0 = Register64List()
    //     0x8d6bb0: bl              #0x8d72c4  ; AllocateRegister64ListStub -> Register64List (size=0xc)
    // 0x8d6bb4: mov             x1, x0
    // 0x8d6bb8: ldur            x0, [fp, #-0x18]
    // 0x8d6bbc: StoreField: r1->field_7 = r0
    //     0x8d6bbc: stur            w0, [x1, #7]
    // 0x8d6bc0: mov             x0, x1
    // 0x8d6bc4: ldur            x3, [fp, #-8]
    // 0x8d6bc8: StoreField: r3->field_f = r0
    //     0x8d6bc8: stur            w0, [x3, #0xf]
    //     0x8d6bcc: ldurb           w16, [x3, #-1]
    //     0x8d6bd0: ldurb           w17, [x0, #-1]
    //     0x8d6bd4: and             x16, x17, x16, lsr #2
    //     0x8d6bd8: tst             x16, HEAP, lsr #32
    //     0x8d6bdc: b.eq            #0x8d6be4
    //     0x8d6be0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d6be4: r1 = <Register64>
    //     0x8d6be4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8d6be8: ldr             x1, [x1, #0x418]
    // 0x8d6bec: r2 = 8
    //     0x8d6bec: movz            x2, #0x8
    // 0x8d6bf0: r0 = _GrowableList()
    //     0x8d6bf0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8d6bf4: stur            x0, [fp, #-0x18]
    // 0x8d6bf8: r1 = 0
    //     0x8d6bf8: movz            x1, #0
    // 0x8d6bfc: stur            x1, [fp, #-0x10]
    // 0x8d6c00: CheckStackOverflow
    //     0x8d6c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6c04: cmp             SP, x16
    //     0x8d6c08: b.ls            #0x8d7018
    // 0x8d6c0c: LoadField: r2 = r0->field_b
    //     0x8d6c0c: ldur            w2, [x0, #0xb]
    // 0x8d6c10: r3 = LoadInt32Instr(r2)
    //     0x8d6c10: sbfx            x3, x2, #1, #0x1f
    // 0x8d6c14: cmp             x1, x3
    // 0x8d6c18: b.ge            #0x8d6ca8
    // 0x8d6c1c: r0 = Register64()
    //     0x8d6c1c: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d6c20: mov             x3, x0
    // 0x8d6c24: r0 = Sentinel
    //     0x8d6c24: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d6c28: stur            x3, [fp, #-0x20]
    // 0x8d6c2c: StoreField: r3->field_7 = r0
    //     0x8d6c2c: stur            w0, [x3, #7]
    // 0x8d6c30: StoreField: r3->field_b = r0
    //     0x8d6c30: stur            w0, [x3, #0xb]
    // 0x8d6c34: str             NULL, [SP]
    // 0x8d6c38: mov             x1, x3
    // 0x8d6c3c: r2 = 0
    //     0x8d6c3c: movz            x2, #0
    // 0x8d6c40: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d6c40: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d6c44: r0 = set()
    //     0x8d6c44: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d6c48: ldur            x2, [fp, #-0x18]
    // 0x8d6c4c: LoadField: r0 = r2->field_b
    //     0x8d6c4c: ldur            w0, [x2, #0xb]
    // 0x8d6c50: r1 = LoadInt32Instr(r0)
    //     0x8d6c50: sbfx            x1, x0, #1, #0x1f
    // 0x8d6c54: mov             x0, x1
    // 0x8d6c58: ldur            x1, [fp, #-0x10]
    // 0x8d6c5c: cmp             x1, x0
    // 0x8d6c60: b.hs            #0x8d7020
    // 0x8d6c64: LoadField: r1 = r2->field_f
    //     0x8d6c64: ldur            w1, [x2, #0xf]
    // 0x8d6c68: DecompressPointer r1
    //     0x8d6c68: add             x1, x1, HEAP, lsl #32
    // 0x8d6c6c: ldur            x0, [fp, #-0x20]
    // 0x8d6c70: ldur            x3, [fp, #-0x10]
    // 0x8d6c74: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8d6c74: add             x25, x1, x3, lsl #2
    //     0x8d6c78: add             x25, x25, #0xf
    //     0x8d6c7c: str             w0, [x25]
    //     0x8d6c80: tbz             w0, #0, #0x8d6c9c
    //     0x8d6c84: ldurb           w16, [x1, #-1]
    //     0x8d6c88: ldurb           w17, [x0, #-1]
    //     0x8d6c8c: and             x16, x17, x16, lsr #2
    //     0x8d6c90: tst             x16, HEAP, lsr #32
    //     0x8d6c94: b.eq            #0x8d6c9c
    //     0x8d6c98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8d6c9c: add             x1, x3, #1
    // 0x8d6ca0: mov             x0, x2
    // 0x8d6ca4: b               #0x8d6bfc
    // 0x8d6ca8: ldur            x1, [fp, #-8]
    // 0x8d6cac: mov             x2, x0
    // 0x8d6cb0: r0 = Register64List()
    //     0x8d6cb0: bl              #0x8d72c4  ; AllocateRegister64ListStub -> Register64List (size=0xc)
    // 0x8d6cb4: mov             x1, x0
    // 0x8d6cb8: ldur            x0, [fp, #-0x18]
    // 0x8d6cbc: StoreField: r1->field_7 = r0
    //     0x8d6cbc: stur            w0, [x1, #7]
    // 0x8d6cc0: mov             x0, x1
    // 0x8d6cc4: ldur            x3, [fp, #-8]
    // 0x8d6cc8: StoreField: r3->field_13 = r0
    //     0x8d6cc8: stur            w0, [x3, #0x13]
    //     0x8d6ccc: ldurb           w16, [x3, #-1]
    //     0x8d6cd0: ldurb           w17, [x0, #-1]
    //     0x8d6cd4: and             x16, x17, x16, lsr #2
    //     0x8d6cd8: tst             x16, HEAP, lsr #32
    //     0x8d6cdc: b.eq            #0x8d6ce4
    //     0x8d6ce0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d6ce4: r1 = <Register64>
    //     0x8d6ce4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8d6ce8: ldr             x1, [x1, #0x418]
    // 0x8d6cec: r2 = 8
    //     0x8d6cec: movz            x2, #0x8
    // 0x8d6cf0: r0 = _GrowableList()
    //     0x8d6cf0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8d6cf4: stur            x0, [fp, #-0x18]
    // 0x8d6cf8: r1 = 0
    //     0x8d6cf8: movz            x1, #0
    // 0x8d6cfc: stur            x1, [fp, #-0x10]
    // 0x8d6d00: CheckStackOverflow
    //     0x8d6d00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6d04: cmp             SP, x16
    //     0x8d6d08: b.ls            #0x8d7024
    // 0x8d6d0c: LoadField: r2 = r0->field_b
    //     0x8d6d0c: ldur            w2, [x0, #0xb]
    // 0x8d6d10: r3 = LoadInt32Instr(r2)
    //     0x8d6d10: sbfx            x3, x2, #1, #0x1f
    // 0x8d6d14: cmp             x1, x3
    // 0x8d6d18: b.ge            #0x8d6da8
    // 0x8d6d1c: r0 = Register64()
    //     0x8d6d1c: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d6d20: mov             x3, x0
    // 0x8d6d24: r0 = Sentinel
    //     0x8d6d24: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d6d28: stur            x3, [fp, #-0x20]
    // 0x8d6d2c: StoreField: r3->field_7 = r0
    //     0x8d6d2c: stur            w0, [x3, #7]
    // 0x8d6d30: StoreField: r3->field_b = r0
    //     0x8d6d30: stur            w0, [x3, #0xb]
    // 0x8d6d34: str             NULL, [SP]
    // 0x8d6d38: mov             x1, x3
    // 0x8d6d3c: r2 = 0
    //     0x8d6d3c: movz            x2, #0
    // 0x8d6d40: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d6d40: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d6d44: r0 = set()
    //     0x8d6d44: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d6d48: ldur            x2, [fp, #-0x18]
    // 0x8d6d4c: LoadField: r0 = r2->field_b
    //     0x8d6d4c: ldur            w0, [x2, #0xb]
    // 0x8d6d50: r1 = LoadInt32Instr(r0)
    //     0x8d6d50: sbfx            x1, x0, #1, #0x1f
    // 0x8d6d54: mov             x0, x1
    // 0x8d6d58: ldur            x1, [fp, #-0x10]
    // 0x8d6d5c: cmp             x1, x0
    // 0x8d6d60: b.hs            #0x8d702c
    // 0x8d6d64: LoadField: r1 = r2->field_f
    //     0x8d6d64: ldur            w1, [x2, #0xf]
    // 0x8d6d68: DecompressPointer r1
    //     0x8d6d68: add             x1, x1, HEAP, lsl #32
    // 0x8d6d6c: ldur            x0, [fp, #-0x20]
    // 0x8d6d70: ldur            x3, [fp, #-0x10]
    // 0x8d6d74: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8d6d74: add             x25, x1, x3, lsl #2
    //     0x8d6d78: add             x25, x25, #0xf
    //     0x8d6d7c: str             w0, [x25]
    //     0x8d6d80: tbz             w0, #0, #0x8d6d9c
    //     0x8d6d84: ldurb           w16, [x1, #-1]
    //     0x8d6d88: ldurb           w17, [x0, #-1]
    //     0x8d6d8c: and             x16, x17, x16, lsr #2
    //     0x8d6d90: tst             x16, HEAP, lsr #32
    //     0x8d6d94: b.eq            #0x8d6d9c
    //     0x8d6d98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8d6d9c: add             x1, x3, #1
    // 0x8d6da0: mov             x0, x2
    // 0x8d6da4: b               #0x8d6cfc
    // 0x8d6da8: ldur            x1, [fp, #-8]
    // 0x8d6dac: mov             x2, x0
    // 0x8d6db0: r0 = Register64List()
    //     0x8d6db0: bl              #0x8d72c4  ; AllocateRegister64ListStub -> Register64List (size=0xc)
    // 0x8d6db4: mov             x1, x0
    // 0x8d6db8: ldur            x0, [fp, #-0x18]
    // 0x8d6dbc: StoreField: r1->field_7 = r0
    //     0x8d6dbc: stur            w0, [x1, #7]
    // 0x8d6dc0: mov             x0, x1
    // 0x8d6dc4: ldur            x3, [fp, #-8]
    // 0x8d6dc8: ArrayStore: r3[0] = r0  ; List_4
    //     0x8d6dc8: stur            w0, [x3, #0x17]
    //     0x8d6dcc: ldurb           w16, [x3, #-1]
    //     0x8d6dd0: ldurb           w17, [x0, #-1]
    //     0x8d6dd4: and             x16, x17, x16, lsr #2
    //     0x8d6dd8: tst             x16, HEAP, lsr #32
    //     0x8d6ddc: b.eq            #0x8d6de4
    //     0x8d6de0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d6de4: r1 = <Register64>
    //     0x8d6de4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8d6de8: ldr             x1, [x1, #0x418]
    // 0x8d6dec: r2 = 8
    //     0x8d6dec: movz            x2, #0x8
    // 0x8d6df0: r0 = _GrowableList()
    //     0x8d6df0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8d6df4: stur            x0, [fp, #-0x18]
    // 0x8d6df8: r1 = 0
    //     0x8d6df8: movz            x1, #0
    // 0x8d6dfc: stur            x1, [fp, #-0x10]
    // 0x8d6e00: CheckStackOverflow
    //     0x8d6e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6e04: cmp             SP, x16
    //     0x8d6e08: b.ls            #0x8d7030
    // 0x8d6e0c: LoadField: r2 = r0->field_b
    //     0x8d6e0c: ldur            w2, [x0, #0xb]
    // 0x8d6e10: r3 = LoadInt32Instr(r2)
    //     0x8d6e10: sbfx            x3, x2, #1, #0x1f
    // 0x8d6e14: cmp             x1, x3
    // 0x8d6e18: b.ge            #0x8d6ea8
    // 0x8d6e1c: r0 = Register64()
    //     0x8d6e1c: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d6e20: mov             x3, x0
    // 0x8d6e24: r0 = Sentinel
    //     0x8d6e24: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d6e28: stur            x3, [fp, #-0x20]
    // 0x8d6e2c: StoreField: r3->field_7 = r0
    //     0x8d6e2c: stur            w0, [x3, #7]
    // 0x8d6e30: StoreField: r3->field_b = r0
    //     0x8d6e30: stur            w0, [x3, #0xb]
    // 0x8d6e34: str             NULL, [SP]
    // 0x8d6e38: mov             x1, x3
    // 0x8d6e3c: r2 = 0
    //     0x8d6e3c: movz            x2, #0
    // 0x8d6e40: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d6e40: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d6e44: r0 = set()
    //     0x8d6e44: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d6e48: ldur            x2, [fp, #-0x18]
    // 0x8d6e4c: LoadField: r0 = r2->field_b
    //     0x8d6e4c: ldur            w0, [x2, #0xb]
    // 0x8d6e50: r1 = LoadInt32Instr(r0)
    //     0x8d6e50: sbfx            x1, x0, #1, #0x1f
    // 0x8d6e54: mov             x0, x1
    // 0x8d6e58: ldur            x1, [fp, #-0x10]
    // 0x8d6e5c: cmp             x1, x0
    // 0x8d6e60: b.hs            #0x8d7038
    // 0x8d6e64: LoadField: r1 = r2->field_f
    //     0x8d6e64: ldur            w1, [x2, #0xf]
    // 0x8d6e68: DecompressPointer r1
    //     0x8d6e68: add             x1, x1, HEAP, lsl #32
    // 0x8d6e6c: ldur            x0, [fp, #-0x20]
    // 0x8d6e70: ldur            x3, [fp, #-0x10]
    // 0x8d6e74: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8d6e74: add             x25, x1, x3, lsl #2
    //     0x8d6e78: add             x25, x25, #0xf
    //     0x8d6e7c: str             w0, [x25]
    //     0x8d6e80: tbz             w0, #0, #0x8d6e9c
    //     0x8d6e84: ldurb           w16, [x1, #-1]
    //     0x8d6e88: ldurb           w17, [x0, #-1]
    //     0x8d6e8c: and             x16, x17, x16, lsr #2
    //     0x8d6e90: tst             x16, HEAP, lsr #32
    //     0x8d6e94: b.eq            #0x8d6e9c
    //     0x8d6e98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8d6e9c: add             x1, x3, #1
    // 0x8d6ea0: mov             x0, x2
    // 0x8d6ea4: b               #0x8d6dfc
    // 0x8d6ea8: ldur            x1, [fp, #-8]
    // 0x8d6eac: mov             x2, x0
    // 0x8d6eb0: r0 = Register64List()
    //     0x8d6eb0: bl              #0x8d72c4  ; AllocateRegister64ListStub -> Register64List (size=0xc)
    // 0x8d6eb4: mov             x1, x0
    // 0x8d6eb8: ldur            x0, [fp, #-0x18]
    // 0x8d6ebc: StoreField: r1->field_7 = r0
    //     0x8d6ebc: stur            w0, [x1, #7]
    // 0x8d6ec0: mov             x0, x1
    // 0x8d6ec4: ldur            x3, [fp, #-8]
    // 0x8d6ec8: StoreField: r3->field_1b = r0
    //     0x8d6ec8: stur            w0, [x3, #0x1b]
    //     0x8d6ecc: ldurb           w16, [x3, #-1]
    //     0x8d6ed0: ldurb           w17, [x0, #-1]
    //     0x8d6ed4: and             x16, x17, x16, lsr #2
    //     0x8d6ed8: tst             x16, HEAP, lsr #32
    //     0x8d6edc: b.eq            #0x8d6ee4
    //     0x8d6ee0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d6ee4: r1 = <Register64>
    //     0x8d6ee4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8d6ee8: ldr             x1, [x1, #0x418]
    // 0x8d6eec: r2 = 8
    //     0x8d6eec: movz            x2, #0x8
    // 0x8d6ef0: r0 = _GrowableList()
    //     0x8d6ef0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8d6ef4: stur            x0, [fp, #-0x18]
    // 0x8d6ef8: r1 = 0
    //     0x8d6ef8: movz            x1, #0
    // 0x8d6efc: stur            x1, [fp, #-0x10]
    // 0x8d6f00: CheckStackOverflow
    //     0x8d6f00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6f04: cmp             SP, x16
    //     0x8d6f08: b.ls            #0x8d703c
    // 0x8d6f0c: LoadField: r2 = r0->field_b
    //     0x8d6f0c: ldur            w2, [x0, #0xb]
    // 0x8d6f10: r3 = LoadInt32Instr(r2)
    //     0x8d6f10: sbfx            x3, x2, #1, #0x1f
    // 0x8d6f14: cmp             x1, x3
    // 0x8d6f18: b.ge            #0x8d6fa8
    // 0x8d6f1c: r0 = Register64()
    //     0x8d6f1c: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d6f20: mov             x3, x0
    // 0x8d6f24: r0 = Sentinel
    //     0x8d6f24: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d6f28: stur            x3, [fp, #-0x20]
    // 0x8d6f2c: StoreField: r3->field_7 = r0
    //     0x8d6f2c: stur            w0, [x3, #7]
    // 0x8d6f30: StoreField: r3->field_b = r0
    //     0x8d6f30: stur            w0, [x3, #0xb]
    // 0x8d6f34: str             NULL, [SP]
    // 0x8d6f38: mov             x1, x3
    // 0x8d6f3c: r2 = 0
    //     0x8d6f3c: movz            x2, #0
    // 0x8d6f40: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d6f40: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d6f44: r0 = set()
    //     0x8d6f44: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d6f48: ldur            x2, [fp, #-0x18]
    // 0x8d6f4c: LoadField: r0 = r2->field_b
    //     0x8d6f4c: ldur            w0, [x2, #0xb]
    // 0x8d6f50: r1 = LoadInt32Instr(r0)
    //     0x8d6f50: sbfx            x1, x0, #1, #0x1f
    // 0x8d6f54: mov             x0, x1
    // 0x8d6f58: ldur            x1, [fp, #-0x10]
    // 0x8d6f5c: cmp             x1, x0
    // 0x8d6f60: b.hs            #0x8d7044
    // 0x8d6f64: LoadField: r1 = r2->field_f
    //     0x8d6f64: ldur            w1, [x2, #0xf]
    // 0x8d6f68: DecompressPointer r1
    //     0x8d6f68: add             x1, x1, HEAP, lsl #32
    // 0x8d6f6c: ldur            x0, [fp, #-0x20]
    // 0x8d6f70: ldur            x3, [fp, #-0x10]
    // 0x8d6f74: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8d6f74: add             x25, x1, x3, lsl #2
    //     0x8d6f78: add             x25, x25, #0xf
    //     0x8d6f7c: str             w0, [x25]
    //     0x8d6f80: tbz             w0, #0, #0x8d6f9c
    //     0x8d6f84: ldurb           w16, [x1, #-1]
    //     0x8d6f88: ldurb           w17, [x0, #-1]
    //     0x8d6f8c: and             x16, x17, x16, lsr #2
    //     0x8d6f90: tst             x16, HEAP, lsr #32
    //     0x8d6f94: b.eq            #0x8d6f9c
    //     0x8d6f98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8d6f9c: add             x1, x3, #1
    // 0x8d6fa0: mov             x0, x2
    // 0x8d6fa4: b               #0x8d6efc
    // 0x8d6fa8: ldur            x1, [fp, #-8]
    // 0x8d6fac: mov             x2, x0
    // 0x8d6fb0: r0 = Register64List()
    //     0x8d6fb0: bl              #0x8d72c4  ; AllocateRegister64ListStub -> Register64List (size=0xc)
    // 0x8d6fb4: mov             x1, x0
    // 0x8d6fb8: ldur            x0, [fp, #-0x18]
    // 0x8d6fbc: StoreField: r1->field_7 = r0
    //     0x8d6fbc: stur            w0, [x1, #7]
    // 0x8d6fc0: mov             x0, x1
    // 0x8d6fc4: ldur            x1, [fp, #-8]
    // 0x8d6fc8: StoreField: r1->field_1f = r0
    //     0x8d6fc8: stur            w0, [x1, #0x1f]
    //     0x8d6fcc: ldurb           w16, [x1, #-1]
    //     0x8d6fd0: ldurb           w17, [x0, #-1]
    //     0x8d6fd4: and             x16, x17, x16, lsr #2
    //     0x8d6fd8: tst             x16, HEAP, lsr #32
    //     0x8d6fdc: b.eq            #0x8d6fe4
    //     0x8d6fe0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d6fe4: r0 = reset()
    //     0x8d6fe4: bl              #0x8d70f0  ; [package:pointycastle/digests/whirlpool.dart] WhirlpoolDigest::reset
    // 0x8d6fe8: r0 = Null
    //     0x8d6fe8: mov             x0, NULL
    // 0x8d6fec: LeaveFrame
    //     0x8d6fec: mov             SP, fp
    //     0x8d6ff0: ldp             fp, lr, [SP], #0x10
    // 0x8d6ff4: ret
    //     0x8d6ff4: ret             
    // 0x8d6ff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d6ff8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6ffc: b               #0x8d69b4
    // 0x8d7000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7000: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7004: b               #0x8d6a0c
    // 0x8d7008: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d7008: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8d700c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d700c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7010: b               #0x8d6b0c
    // 0x8d7014: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d7014: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8d7018: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7018: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d701c: b               #0x8d6c0c
    // 0x8d7020: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d7020: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8d7024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7024: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7028: b               #0x8d6d0c
    // 0x8d702c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d702c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8d7030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7030: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7034: b               #0x8d6e0c
    // 0x8d7038: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d7038: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8d703c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d703c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7040: b               #0x8d6f0c
    // 0x8d7044: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d7044: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ reset(/* No info */) {
    // ** addr: 0x8d70f0, size: 0x118
    // 0x8d70f0: EnterFrame
    //     0x8d70f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d70f4: mov             fp, SP
    // 0x8d70f8: AllocStack(0x8)
    //     0x8d70f8: sub             SP, SP, #8
    // 0x8d70fc: SetupParameters(WhirlpoolDigest this /* r1 => r0, fp-0x8 */)
    //     0x8d70fc: mov             x0, x1
    //     0x8d7100: stur            x1, [fp, #-8]
    // 0x8d7104: CheckStackOverflow
    //     0x8d7104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7108: cmp             SP, x16
    //     0x8d710c: b.ls            #0x8d7200
    // 0x8d7110: LoadField: r1 = r0->field_7
    //     0x8d7110: ldur            w1, [x0, #7]
    // 0x8d7114: DecompressPointer r1
    //     0x8d7114: add             x1, x1, HEAP, lsl #32
    // 0x8d7118: LoadField: r2 = r1->field_13
    //     0x8d7118: ldur            w2, [x1, #0x13]
    // 0x8d711c: r3 = LoadInt32Instr(r2)
    //     0x8d711c: sbfx            x3, x2, #1, #0x1f
    // 0x8d7120: r2 = 0
    //     0x8d7120: movz            x2, #0
    // 0x8d7124: r5 = 0
    //     0x8d7124: movz            x5, #0
    // 0x8d7128: r0 = fillRange()
    //     0x8d7128: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0x8d712c: ldur            x0, [fp, #-8]
    // 0x8d7130: LoadField: r1 = r0->field_b
    //     0x8d7130: ldur            w1, [x0, #0xb]
    // 0x8d7134: DecompressPointer r1
    //     0x8d7134: add             x1, x1, HEAP, lsl #32
    // 0x8d7138: LoadField: r2 = r1->field_7
    //     0x8d7138: ldur            w2, [x1, #7]
    // 0x8d713c: DecompressPointer r2
    //     0x8d713c: add             x2, x2, HEAP, lsl #32
    // 0x8d7140: LoadField: r3 = r2->field_b
    //     0x8d7140: ldur            w3, [x2, #0xb]
    // 0x8d7144: r2 = LoadInt32Instr(r3)
    //     0x8d7144: sbfx            x2, x3, #1, #0x1f
    // 0x8d7148: r0 = fillRange()
    //     0x8d7148: bl              #0x8d7208  ; [package:pointycastle/src/ufixnum.dart] Register64List::fillRange
    // 0x8d714c: ldur            x0, [fp, #-8]
    // 0x8d7150: LoadField: r1 = r0->field_f
    //     0x8d7150: ldur            w1, [x0, #0xf]
    // 0x8d7154: DecompressPointer r1
    //     0x8d7154: add             x1, x1, HEAP, lsl #32
    // 0x8d7158: LoadField: r2 = r1->field_7
    //     0x8d7158: ldur            w2, [x1, #7]
    // 0x8d715c: DecompressPointer r2
    //     0x8d715c: add             x2, x2, HEAP, lsl #32
    // 0x8d7160: LoadField: r3 = r2->field_b
    //     0x8d7160: ldur            w3, [x2, #0xb]
    // 0x8d7164: r2 = LoadInt32Instr(r3)
    //     0x8d7164: sbfx            x2, x3, #1, #0x1f
    // 0x8d7168: r0 = fillRange()
    //     0x8d7168: bl              #0x8d7208  ; [package:pointycastle/src/ufixnum.dart] Register64List::fillRange
    // 0x8d716c: ldur            x0, [fp, #-8]
    // 0x8d7170: LoadField: r1 = r0->field_13
    //     0x8d7170: ldur            w1, [x0, #0x13]
    // 0x8d7174: DecompressPointer r1
    //     0x8d7174: add             x1, x1, HEAP, lsl #32
    // 0x8d7178: LoadField: r2 = r1->field_7
    //     0x8d7178: ldur            w2, [x1, #7]
    // 0x8d717c: DecompressPointer r2
    //     0x8d717c: add             x2, x2, HEAP, lsl #32
    // 0x8d7180: LoadField: r3 = r2->field_b
    //     0x8d7180: ldur            w3, [x2, #0xb]
    // 0x8d7184: r2 = LoadInt32Instr(r3)
    //     0x8d7184: sbfx            x2, x3, #1, #0x1f
    // 0x8d7188: r0 = fillRange()
    //     0x8d7188: bl              #0x8d7208  ; [package:pointycastle/src/ufixnum.dart] Register64List::fillRange
    // 0x8d718c: ldur            x0, [fp, #-8]
    // 0x8d7190: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8d7190: ldur            w1, [x0, #0x17]
    // 0x8d7194: DecompressPointer r1
    //     0x8d7194: add             x1, x1, HEAP, lsl #32
    // 0x8d7198: LoadField: r2 = r1->field_7
    //     0x8d7198: ldur            w2, [x1, #7]
    // 0x8d719c: DecompressPointer r2
    //     0x8d719c: add             x2, x2, HEAP, lsl #32
    // 0x8d71a0: LoadField: r3 = r2->field_b
    //     0x8d71a0: ldur            w3, [x2, #0xb]
    // 0x8d71a4: r2 = LoadInt32Instr(r3)
    //     0x8d71a4: sbfx            x2, x3, #1, #0x1f
    // 0x8d71a8: r0 = fillRange()
    //     0x8d71a8: bl              #0x8d7208  ; [package:pointycastle/src/ufixnum.dart] Register64List::fillRange
    // 0x8d71ac: ldur            x0, [fp, #-8]
    // 0x8d71b0: LoadField: r1 = r0->field_1b
    //     0x8d71b0: ldur            w1, [x0, #0x1b]
    // 0x8d71b4: DecompressPointer r1
    //     0x8d71b4: add             x1, x1, HEAP, lsl #32
    // 0x8d71b8: LoadField: r2 = r1->field_7
    //     0x8d71b8: ldur            w2, [x1, #7]
    // 0x8d71bc: DecompressPointer r2
    //     0x8d71bc: add             x2, x2, HEAP, lsl #32
    // 0x8d71c0: LoadField: r3 = r2->field_b
    //     0x8d71c0: ldur            w3, [x2, #0xb]
    // 0x8d71c4: r2 = LoadInt32Instr(r3)
    //     0x8d71c4: sbfx            x2, x3, #1, #0x1f
    // 0x8d71c8: r0 = fillRange()
    //     0x8d71c8: bl              #0x8d7208  ; [package:pointycastle/src/ufixnum.dart] Register64List::fillRange
    // 0x8d71cc: ldur            x0, [fp, #-8]
    // 0x8d71d0: LoadField: r1 = r0->field_1f
    //     0x8d71d0: ldur            w1, [x0, #0x1f]
    // 0x8d71d4: DecompressPointer r1
    //     0x8d71d4: add             x1, x1, HEAP, lsl #32
    // 0x8d71d8: LoadField: r0 = r1->field_7
    //     0x8d71d8: ldur            w0, [x1, #7]
    // 0x8d71dc: DecompressPointer r0
    //     0x8d71dc: add             x0, x0, HEAP, lsl #32
    // 0x8d71e0: LoadField: r2 = r0->field_b
    //     0x8d71e0: ldur            w2, [x0, #0xb]
    // 0x8d71e4: r0 = LoadInt32Instr(r2)
    //     0x8d71e4: sbfx            x0, x2, #1, #0x1f
    // 0x8d71e8: mov             x2, x0
    // 0x8d71ec: r0 = fillRange()
    //     0x8d71ec: bl              #0x8d7208  ; [package:pointycastle/src/ufixnum.dart] Register64List::fillRange
    // 0x8d71f0: r0 = Null
    //     0x8d71f0: mov             x0, NULL
    // 0x8d71f4: LeaveFrame
    //     0x8d71f4: mov             SP, fp
    //     0x8d71f8: ldp             fp, lr, [SP], #0x10
    // 0x8d71fc: ret
    //     0x8d71fc: ret             
    // 0x8d7200: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7200: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7204: b               #0x8d7110
  }
  const get _ digestSize(/* No info */) {
    // ** addr: 0xe61e20, size: 0x8
    // 0xe61e20: LoadField: r0 = r1->field_27
    //     0xe61e20: ldur            x0, [x1, #0x27]
    // 0xe61e24: ret
    //     0xe61e24: ret             
  }
}
