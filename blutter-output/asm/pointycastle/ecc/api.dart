// lib: api.ecc, url: package:pointycastle/ecc/api.dart

// class id: 1050962, size: 0x8
class :: {
}

// class id: 640, size: 0x8, field offset: 0x8
abstract class ECCurve extends Object {
}

// class id: 641, size: 0x8, field offset: 0x8
abstract class ECPoint extends Object {
}

// class id: 642, size: 0x8, field offset: 0x8
abstract class ECFieldElement extends Object {
}

// class id: 643, size: 0x8, field offset: 0x8
abstract class ECDomainParameters extends Object {
}
