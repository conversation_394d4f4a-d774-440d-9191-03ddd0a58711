// lib: impl.ec_domain_parameters.brainpoolp160r1, url: package:pointycastle/ecc/curves/brainpoolp160r1.dart

// class id: 1050963, size: 0x8
class :: {
}

// class id: 639, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp160r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xeb4

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d5554, size: 0x58
    // 0x8d5554: EnterFrame
    //     0x8d5554: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5558: mov             fp, SP
    // 0x8d555c: AllocStack(0x8)
    //     0x8d555c: sub             SP, SP, #8
    // 0x8d5560: r0 = StaticFactoryConfig()
    //     0x8d5560: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d5564: mov             x3, x0
    // 0x8d5568: r0 = "brainpoolp160r1"
    //     0x8d5568: add             x0, PP, #0x19, lsl #12  ; [pp+0x192a0] "brainpoolp160r1"
    //     0x8d556c: ldr             x0, [x0, #0x2a0]
    // 0x8d5570: stur            x3, [fp, #-8]
    // 0x8d5574: StoreField: r3->field_b = r0
    //     0x8d5574: stur            w0, [x3, #0xb]
    // 0x8d5578: r1 = Function '<anonymous closure>': static.
    //     0x8d5578: add             x1, PP, #0x19, lsl #12  ; [pp+0x192a8] AnonymousClosure: static (0x8d55ac), in [package:pointycastle/ecc/curves/brainpoolp160r1.dart] ECCurve_brainpoolp160r1::factoryConfig (0x8d5554)
    //     0x8d557c: ldr             x1, [x1, #0x2a8]
    // 0x8d5580: r2 = Null
    //     0x8d5580: mov             x2, NULL
    // 0x8d5584: r0 = AllocateClosure()
    //     0x8d5584: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d5588: mov             x1, x0
    // 0x8d558c: ldur            x0, [fp, #-8]
    // 0x8d5590: StoreField: r0->field_f = r1
    //     0x8d5590: stur            w1, [x0, #0xf]
    // 0x8d5594: r1 = ECDomainParameters
    //     0x8d5594: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d5598: ldr             x1, [x1, #0x6e8]
    // 0x8d559c: StoreField: r0->field_7 = r1
    //     0x8d559c: stur            w1, [x0, #7]
    // 0x8d55a0: LeaveFrame
    //     0x8d55a0: mov             SP, fp
    //     0x8d55a4: ldp             fp, lr, [SP], #0x10
    // 0x8d55a8: ret
    //     0x8d55a8: ret             
  }
  [closure] static ECCurve_brainpoolp160r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d55ac, size: 0x30
    // 0x8d55ac: EnterFrame
    //     0x8d55ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8d55b0: mov             fp, SP
    // 0x8d55b4: CheckStackOverflow
    //     0x8d55b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d55b8: cmp             SP, x16
    //     0x8d55bc: b.ls            #0x8d55d4
    // 0x8d55c0: r1 = Null
    //     0x8d55c0: mov             x1, NULL
    // 0x8d55c4: r0 = ECCurve_brainpoolp160r1()
    //     0x8d55c4: bl              #0x8d55dc  ; [package:pointycastle/ecc/curves/brainpoolp160r1.dart] ECCurve_brainpoolp160r1::ECCurve_brainpoolp160r1
    // 0x8d55c8: LeaveFrame
    //     0x8d55c8: mov             SP, fp
    //     0x8d55cc: ldp             fp, lr, [SP], #0x10
    // 0x8d55d0: ret
    //     0x8d55d0: ret             
    // 0x8d55d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d55d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d55d8: b               #0x8d55c0
  }
  factory ECCurve_brainpoolp160r1 ECCurve_brainpoolp160r1(dynamic) {
    // ** addr: 0x8d55dc, size: 0xd4
    // 0x8d55dc: EnterFrame
    //     0x8d55dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d55e0: mov             fp, SP
    // 0x8d55e4: AllocStack(0x40)
    //     0x8d55e4: sub             SP, SP, #0x40
    // 0x8d55e8: CheckStackOverflow
    //     0x8d55e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d55ec: cmp             SP, x16
    //     0x8d55f0: b.ls            #0x8d56a8
    // 0x8d55f4: r1 = "e95e4a5f737059dc60dfc7ad95b3d8139515620f"
    //     0x8d55f4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19270] "e95e4a5f737059dc60dfc7ad95b3d8139515620f"
    //     0x8d55f8: ldr             x1, [x1, #0x270]
    // 0x8d55fc: r2 = 32
    //     0x8d55fc: movz            x2, #0x20
    // 0x8d5600: r0 = parse()
    //     0x8d5600: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5604: r1 = "340e7be2a280eb74e2be61bada745d97e8f7c300"
    //     0x8d5604: add             x1, PP, #0x19, lsl #12  ; [pp+0x192b0] "340e7be2a280eb74e2be61bada745d97e8f7c300"
    //     0x8d5608: ldr             x1, [x1, #0x2b0]
    // 0x8d560c: r2 = 32
    //     0x8d560c: movz            x2, #0x20
    // 0x8d5610: stur            x0, [fp, #-8]
    // 0x8d5614: r0 = parse()
    //     0x8d5614: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5618: r1 = "1e589a8595423412134faa2dbdec95c8d8675e58"
    //     0x8d5618: add             x1, PP, #0x19, lsl #12  ; [pp+0x192b8] "1e589a8595423412134faa2dbdec95c8d8675e58"
    //     0x8d561c: ldr             x1, [x1, #0x2b8]
    // 0x8d5620: r2 = 32
    //     0x8d5620: movz            x2, #0x20
    // 0x8d5624: stur            x0, [fp, #-0x10]
    // 0x8d5628: r0 = parse()
    //     0x8d5628: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d562c: r1 = "04bed5af16ea3f6a4f62938c4631eb5af7bdbcdbc31667cb477a1a8ec338f94741669c976316da6321"
    //     0x8d562c: add             x1, PP, #0x19, lsl #12  ; [pp+0x192c0] "04bed5af16ea3f6a4f62938c4631eb5af7bdbcdbc31667cb477a1a8ec338f94741669c976316da6321"
    //     0x8d5630: ldr             x1, [x1, #0x2c0]
    // 0x8d5634: r2 = 32
    //     0x8d5634: movz            x2, #0x20
    // 0x8d5638: stur            x0, [fp, #-0x18]
    // 0x8d563c: r0 = parse()
    //     0x8d563c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5640: r1 = "e95e4a5f737059dc60df5991d45029409e60fc09"
    //     0x8d5640: add             x1, PP, #0x19, lsl #12  ; [pp+0x19290] "e95e4a5f737059dc60df5991d45029409e60fc09"
    //     0x8d5644: ldr             x1, [x1, #0x290]
    // 0x8d5648: r2 = 32
    //     0x8d5648: movz            x2, #0x20
    // 0x8d564c: stur            x0, [fp, #-0x20]
    // 0x8d5650: r0 = parse()
    //     0x8d5650: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5654: r1 = "1"
    //     0x8d5654: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d5658: ldr             x1, [x1, #0x718]
    // 0x8d565c: r2 = 32
    //     0x8d565c: movz            x2, #0x20
    // 0x8d5660: stur            x0, [fp, #-0x28]
    // 0x8d5664: r0 = parse()
    //     0x8d5664: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5668: ldur            x16, [fp, #-0x28]
    // 0x8d566c: ldur            lr, [fp, #-8]
    // 0x8d5670: stp             lr, x16, [SP, #8]
    // 0x8d5674: str             NULL, [SP]
    // 0x8d5678: ldur            x3, [fp, #-0x10]
    // 0x8d567c: ldur            x5, [fp, #-0x18]
    // 0x8d5680: ldur            x6, [fp, #-0x20]
    // 0x8d5684: mov             x7, x0
    // 0x8d5688: r1 = "brainpoolp160r1"
    //     0x8d5688: add             x1, PP, #0x19, lsl #12  ; [pp+0x192a0] "brainpoolp160r1"
    //     0x8d568c: ldr             x1, [x1, #0x2a0]
    // 0x8d5690: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp160r1 from Function '_make@969296239': static.
    //     0x8d5690: add             x2, PP, #0x19, lsl #12  ; [pp+0x192c8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp160r1 from Function '_make@969296239': static. (0x7e54fb2d56b0)
    //     0x8d5694: ldr             x2, [x2, #0x2c8]
    // 0x8d5698: r0 = constructFpStandardCurve()
    //     0x8d5698: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d569c: LeaveFrame
    //     0x8d569c: mov             SP, fp
    //     0x8d56a0: ldp             fp, lr, [SP], #0x10
    // 0x8d56a4: ret
    //     0x8d56a4: ret             
    // 0x8d56a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d56a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d56ac: b               #0x8d55f4
  }
  [closure] static ECCurve_brainpoolp160r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d56b0, size: 0x20
    // 0x8d56b0: EnterFrame
    //     0x8d56b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d56b4: mov             fp, SP
    // 0x8d56b8: r0 = ECCurve_brainpoolp160r1()
    //     0x8d56b8: bl              #0x8d56d0  ; AllocateECCurve_brainpoolp160r1Stub -> ECCurve_brainpoolp160r1 (size=0xc)
    // 0x8d56bc: ldr             x1, [fp, #0x18]
    // 0x8d56c0: StoreField: r0->field_7 = r1
    //     0x8d56c0: stur            w1, [x0, #7]
    // 0x8d56c4: LeaveFrame
    //     0x8d56c4: mov             SP, fp
    //     0x8d56c8: ldp             fp, lr, [SP], #0x10
    // 0x8d56cc: ret
    //     0x8d56cc: ret             
  }
}
