// lib: impl.ec_domain_parameters.brainpoolp160t1, url: package:pointycastle/ecc/curves/brainpoolp160t1.dart

// class id: 1050964, size: 0x8
class :: {
}

// class id: 638, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp160t1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xeb8

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d53cc, size: 0x58
    // 0x8d53cc: EnterFrame
    //     0x8d53cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d53d0: mov             fp, SP
    // 0x8d53d4: AllocStack(0x8)
    //     0x8d53d4: sub             SP, SP, #8
    // 0x8d53d8: r0 = StaticFactoryConfig()
    //     0x8d53d8: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d53dc: mov             x3, x0
    // 0x8d53e0: r0 = "brainpoolp160t1"
    //     0x8d53e0: add             x0, PP, #0x19, lsl #12  ; [pp+0x19260] "brainpoolp160t1"
    //     0x8d53e4: ldr             x0, [x0, #0x260]
    // 0x8d53e8: stur            x3, [fp, #-8]
    // 0x8d53ec: StoreField: r3->field_b = r0
    //     0x8d53ec: stur            w0, [x3, #0xb]
    // 0x8d53f0: r1 = Function '<anonymous closure>': static.
    //     0x8d53f0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19268] AnonymousClosure: static (0x8d5424), in [package:pointycastle/ecc/curves/brainpoolp160t1.dart] ECCurve_brainpoolp160t1::factoryConfig (0x8d53cc)
    //     0x8d53f4: ldr             x1, [x1, #0x268]
    // 0x8d53f8: r2 = Null
    //     0x8d53f8: mov             x2, NULL
    // 0x8d53fc: r0 = AllocateClosure()
    //     0x8d53fc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d5400: mov             x1, x0
    // 0x8d5404: ldur            x0, [fp, #-8]
    // 0x8d5408: StoreField: r0->field_f = r1
    //     0x8d5408: stur            w1, [x0, #0xf]
    // 0x8d540c: r1 = ECDomainParameters
    //     0x8d540c: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d5410: ldr             x1, [x1, #0x6e8]
    // 0x8d5414: StoreField: r0->field_7 = r1
    //     0x8d5414: stur            w1, [x0, #7]
    // 0x8d5418: LeaveFrame
    //     0x8d5418: mov             SP, fp
    //     0x8d541c: ldp             fp, lr, [SP], #0x10
    // 0x8d5420: ret
    //     0x8d5420: ret             
  }
  [closure] static ECCurve_brainpoolp160t1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d5424, size: 0x30
    // 0x8d5424: EnterFrame
    //     0x8d5424: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5428: mov             fp, SP
    // 0x8d542c: CheckStackOverflow
    //     0x8d542c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5430: cmp             SP, x16
    //     0x8d5434: b.ls            #0x8d544c
    // 0x8d5438: r1 = Null
    //     0x8d5438: mov             x1, NULL
    // 0x8d543c: r0 = ECCurve_brainpoolp160t1()
    //     0x8d543c: bl              #0x8d5454  ; [package:pointycastle/ecc/curves/brainpoolp160t1.dart] ECCurve_brainpoolp160t1::ECCurve_brainpoolp160t1
    // 0x8d5440: LeaveFrame
    //     0x8d5440: mov             SP, fp
    //     0x8d5444: ldp             fp, lr, [SP], #0x10
    // 0x8d5448: ret
    //     0x8d5448: ret             
    // 0x8d544c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d544c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5450: b               #0x8d5438
  }
  factory ECCurve_brainpoolp160t1 ECCurve_brainpoolp160t1(dynamic) {
    // ** addr: 0x8d5454, size: 0xd4
    // 0x8d5454: EnterFrame
    //     0x8d5454: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5458: mov             fp, SP
    // 0x8d545c: AllocStack(0x40)
    //     0x8d545c: sub             SP, SP, #0x40
    // 0x8d5460: CheckStackOverflow
    //     0x8d5460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5464: cmp             SP, x16
    //     0x8d5468: b.ls            #0x8d5520
    // 0x8d546c: r1 = "e95e4a5f737059dc60dfc7ad95b3d8139515620f"
    //     0x8d546c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19270] "e95e4a5f737059dc60dfc7ad95b3d8139515620f"
    //     0x8d5470: ldr             x1, [x1, #0x270]
    // 0x8d5474: r2 = 32
    //     0x8d5474: movz            x2, #0x20
    // 0x8d5478: r0 = parse()
    //     0x8d5478: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d547c: r1 = "e95e4a5f737059dc60dfc7ad95b3d8139515620c"
    //     0x8d547c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19278] "e95e4a5f737059dc60dfc7ad95b3d8139515620c"
    //     0x8d5480: ldr             x1, [x1, #0x278]
    // 0x8d5484: r2 = 32
    //     0x8d5484: movz            x2, #0x20
    // 0x8d5488: stur            x0, [fp, #-8]
    // 0x8d548c: r0 = parse()
    //     0x8d548c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5490: r1 = "7a556b6dae535b7b51ed2c4d7daa7a0b5c55f380"
    //     0x8d5490: add             x1, PP, #0x19, lsl #12  ; [pp+0x19280] "7a556b6dae535b7b51ed2c4d7daa7a0b5c55f380"
    //     0x8d5494: ldr             x1, [x1, #0x280]
    // 0x8d5498: r2 = 32
    //     0x8d5498: movz            x2, #0x20
    // 0x8d549c: stur            x0, [fp, #-0x10]
    // 0x8d54a0: r0 = parse()
    //     0x8d54a0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d54a4: r1 = "04b199b13b9b34efc1397e64baeb05acc265ff2378add6718b7c7c1961f0991b842443772152c9e0ad"
    //     0x8d54a4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19288] "04b199b13b9b34efc1397e64baeb05acc265ff2378add6718b7c7c1961f0991b842443772152c9e0ad"
    //     0x8d54a8: ldr             x1, [x1, #0x288]
    // 0x8d54ac: r2 = 32
    //     0x8d54ac: movz            x2, #0x20
    // 0x8d54b0: stur            x0, [fp, #-0x18]
    // 0x8d54b4: r0 = parse()
    //     0x8d54b4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d54b8: r1 = "e95e4a5f737059dc60df5991d45029409e60fc09"
    //     0x8d54b8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19290] "e95e4a5f737059dc60df5991d45029409e60fc09"
    //     0x8d54bc: ldr             x1, [x1, #0x290]
    // 0x8d54c0: r2 = 32
    //     0x8d54c0: movz            x2, #0x20
    // 0x8d54c4: stur            x0, [fp, #-0x20]
    // 0x8d54c8: r0 = parse()
    //     0x8d54c8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d54cc: r1 = "1"
    //     0x8d54cc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d54d0: ldr             x1, [x1, #0x718]
    // 0x8d54d4: r2 = 32
    //     0x8d54d4: movz            x2, #0x20
    // 0x8d54d8: stur            x0, [fp, #-0x28]
    // 0x8d54dc: r0 = parse()
    //     0x8d54dc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d54e0: ldur            x16, [fp, #-0x28]
    // 0x8d54e4: ldur            lr, [fp, #-8]
    // 0x8d54e8: stp             lr, x16, [SP, #8]
    // 0x8d54ec: str             NULL, [SP]
    // 0x8d54f0: ldur            x3, [fp, #-0x10]
    // 0x8d54f4: ldur            x5, [fp, #-0x18]
    // 0x8d54f8: ldur            x6, [fp, #-0x20]
    // 0x8d54fc: mov             x7, x0
    // 0x8d5500: r1 = "brainpoolp160t1"
    //     0x8d5500: add             x1, PP, #0x19, lsl #12  ; [pp+0x19260] "brainpoolp160t1"
    //     0x8d5504: ldr             x1, [x1, #0x260]
    // 0x8d5508: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp160t1 from Function '_make@970372507': static.
    //     0x8d5508: add             x2, PP, #0x19, lsl #12  ; [pp+0x19298] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp160t1 from Function '_make@970372507': static. (0x7e54fb2d5528)
    //     0x8d550c: ldr             x2, [x2, #0x298]
    // 0x8d5510: r0 = constructFpStandardCurve()
    //     0x8d5510: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d5514: LeaveFrame
    //     0x8d5514: mov             SP, fp
    //     0x8d5518: ldp             fp, lr, [SP], #0x10
    // 0x8d551c: ret
    //     0x8d551c: ret             
    // 0x8d5520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5524: b               #0x8d546c
  }
  [closure] static ECCurve_brainpoolp160t1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d5528, size: 0x20
    // 0x8d5528: EnterFrame
    //     0x8d5528: stp             fp, lr, [SP, #-0x10]!
    //     0x8d552c: mov             fp, SP
    // 0x8d5530: r0 = ECCurve_brainpoolp160t1()
    //     0x8d5530: bl              #0x8d5548  ; AllocateECCurve_brainpoolp160t1Stub -> ECCurve_brainpoolp160t1 (size=0xc)
    // 0x8d5534: ldr             x1, [fp, #0x18]
    // 0x8d5538: StoreField: r0->field_7 = r1
    //     0x8d5538: stur            w1, [x0, #7]
    // 0x8d553c: LeaveFrame
    //     0x8d553c: mov             SP, fp
    //     0x8d5540: ldp             fp, lr, [SP], #0x10
    // 0x8d5544: ret
    //     0x8d5544: ret             
  }
}
