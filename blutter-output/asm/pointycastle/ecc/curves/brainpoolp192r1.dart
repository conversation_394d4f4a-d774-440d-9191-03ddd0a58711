// lib: impl.ec_domain_parameters.brainpoolp192r1, url: package:pointycastle/ecc/curves/brainpoolp192r1.dart

// class id: 1050965, size: 0x8
class :: {
}

// class id: 637, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp192r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xebc

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d5244, size: 0x58
    // 0x8d5244: EnterFrame
    //     0x8d5244: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5248: mov             fp, SP
    // 0x8d524c: AllocStack(0x8)
    //     0x8d524c: sub             SP, SP, #8
    // 0x8d5250: r0 = StaticFactoryConfig()
    //     0x8d5250: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d5254: mov             x3, x0
    // 0x8d5258: r0 = "brainpoolp192r1"
    //     0x8d5258: add             x0, PP, #0x19, lsl #12  ; [pp+0x19230] "brainpoolp192r1"
    //     0x8d525c: ldr             x0, [x0, #0x230]
    // 0x8d5260: stur            x3, [fp, #-8]
    // 0x8d5264: StoreField: r3->field_b = r0
    //     0x8d5264: stur            w0, [x3, #0xb]
    // 0x8d5268: r1 = Function '<anonymous closure>': static.
    //     0x8d5268: add             x1, PP, #0x19, lsl #12  ; [pp+0x19238] AnonymousClosure: static (0x8d529c), in [package:pointycastle/ecc/curves/brainpoolp192r1.dart] ECCurve_brainpoolp192r1::factoryConfig (0x8d5244)
    //     0x8d526c: ldr             x1, [x1, #0x238]
    // 0x8d5270: r2 = Null
    //     0x8d5270: mov             x2, NULL
    // 0x8d5274: r0 = AllocateClosure()
    //     0x8d5274: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d5278: mov             x1, x0
    // 0x8d527c: ldur            x0, [fp, #-8]
    // 0x8d5280: StoreField: r0->field_f = r1
    //     0x8d5280: stur            w1, [x0, #0xf]
    // 0x8d5284: r1 = ECDomainParameters
    //     0x8d5284: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d5288: ldr             x1, [x1, #0x6e8]
    // 0x8d528c: StoreField: r0->field_7 = r1
    //     0x8d528c: stur            w1, [x0, #7]
    // 0x8d5290: LeaveFrame
    //     0x8d5290: mov             SP, fp
    //     0x8d5294: ldp             fp, lr, [SP], #0x10
    // 0x8d5298: ret
    //     0x8d5298: ret             
  }
  [closure] static ECCurve_brainpoolp192r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d529c, size: 0x30
    // 0x8d529c: EnterFrame
    //     0x8d529c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d52a0: mov             fp, SP
    // 0x8d52a4: CheckStackOverflow
    //     0x8d52a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d52a8: cmp             SP, x16
    //     0x8d52ac: b.ls            #0x8d52c4
    // 0x8d52b0: r1 = Null
    //     0x8d52b0: mov             x1, NULL
    // 0x8d52b4: r0 = ECCurve_brainpoolp192r1()
    //     0x8d52b4: bl              #0x8d52cc  ; [package:pointycastle/ecc/curves/brainpoolp192r1.dart] ECCurve_brainpoolp192r1::ECCurve_brainpoolp192r1
    // 0x8d52b8: LeaveFrame
    //     0x8d52b8: mov             SP, fp
    //     0x8d52bc: ldp             fp, lr, [SP], #0x10
    // 0x8d52c0: ret
    //     0x8d52c0: ret             
    // 0x8d52c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d52c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d52c8: b               #0x8d52b0
  }
  factory ECCurve_brainpoolp192r1 ECCurve_brainpoolp192r1(dynamic) {
    // ** addr: 0x8d52cc, size: 0xd4
    // 0x8d52cc: EnterFrame
    //     0x8d52cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d52d0: mov             fp, SP
    // 0x8d52d4: AllocStack(0x40)
    //     0x8d52d4: sub             SP, SP, #0x40
    // 0x8d52d8: CheckStackOverflow
    //     0x8d52d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d52dc: cmp             SP, x16
    //     0x8d52e0: b.ls            #0x8d5398
    // 0x8d52e4: r1 = "c302f41d932a36cda7a3463093d18db78fce476de1a86297"
    //     0x8d52e4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19200] "c302f41d932a36cda7a3463093d18db78fce476de1a86297"
    //     0x8d52e8: ldr             x1, [x1, #0x200]
    // 0x8d52ec: r2 = 32
    //     0x8d52ec: movz            x2, #0x20
    // 0x8d52f0: r0 = parse()
    //     0x8d52f0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d52f4: r1 = "6a91174076b1e0e19c39c031fe8685c1cae040e5c69a28ef"
    //     0x8d52f4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19240] "6a91174076b1e0e19c39c031fe8685c1cae040e5c69a28ef"
    //     0x8d52f8: ldr             x1, [x1, #0x240]
    // 0x8d52fc: r2 = 32
    //     0x8d52fc: movz            x2, #0x20
    // 0x8d5300: stur            x0, [fp, #-8]
    // 0x8d5304: r0 = parse()
    //     0x8d5304: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5308: r1 = "469a28ef7c28cca3dc721d044f4496bcca7ef4146fbf25c9"
    //     0x8d5308: add             x1, PP, #0x19, lsl #12  ; [pp+0x19248] "469a28ef7c28cca3dc721d044f4496bcca7ef4146fbf25c9"
    //     0x8d530c: ldr             x1, [x1, #0x248]
    // 0x8d5310: r2 = 32
    //     0x8d5310: movz            x2, #0x20
    // 0x8d5314: stur            x0, [fp, #-0x10]
    // 0x8d5318: r0 = parse()
    //     0x8d5318: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d531c: r1 = "04c0a0647eaab6a48753b033c56cb0f0900a2f5c4853375fd614b690866abd5bb88b5f4828c1490002e6773fa2fa299b8f"
    //     0x8d531c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19250] "04c0a0647eaab6a48753b033c56cb0f0900a2f5c4853375fd614b690866abd5bb88b5f4828c1490002e6773fa2fa299b8f"
    //     0x8d5320: ldr             x1, [x1, #0x250]
    // 0x8d5324: r2 = 32
    //     0x8d5324: movz            x2, #0x20
    // 0x8d5328: stur            x0, [fp, #-0x18]
    // 0x8d532c: r0 = parse()
    //     0x8d532c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5330: r1 = "c302f41d932a36cda7a3462f9e9e916b5be8f1029ac4acc1"
    //     0x8d5330: add             x1, PP, #0x19, lsl #12  ; [pp+0x19220] "c302f41d932a36cda7a3462f9e9e916b5be8f1029ac4acc1"
    //     0x8d5334: ldr             x1, [x1, #0x220]
    // 0x8d5338: r2 = 32
    //     0x8d5338: movz            x2, #0x20
    // 0x8d533c: stur            x0, [fp, #-0x20]
    // 0x8d5340: r0 = parse()
    //     0x8d5340: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5344: r1 = "1"
    //     0x8d5344: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d5348: ldr             x1, [x1, #0x718]
    // 0x8d534c: r2 = 32
    //     0x8d534c: movz            x2, #0x20
    // 0x8d5350: stur            x0, [fp, #-0x28]
    // 0x8d5354: r0 = parse()
    //     0x8d5354: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5358: ldur            x16, [fp, #-0x28]
    // 0x8d535c: ldur            lr, [fp, #-8]
    // 0x8d5360: stp             lr, x16, [SP, #8]
    // 0x8d5364: str             NULL, [SP]
    // 0x8d5368: ldur            x3, [fp, #-0x10]
    // 0x8d536c: ldur            x5, [fp, #-0x18]
    // 0x8d5370: ldur            x6, [fp, #-0x20]
    // 0x8d5374: mov             x7, x0
    // 0x8d5378: r1 = "brainpoolp192r1"
    //     0x8d5378: add             x1, PP, #0x19, lsl #12  ; [pp+0x19230] "brainpoolp192r1"
    //     0x8d537c: ldr             x1, [x1, #0x230]
    // 0x8d5380: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp192r1 from Function '_make@971452040': static.
    //     0x8d5380: add             x2, PP, #0x19, lsl #12  ; [pp+0x19258] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp192r1 from Function '_make@971452040': static. (0x7e54fb2d53a0)
    //     0x8d5384: ldr             x2, [x2, #0x258]
    // 0x8d5388: r0 = constructFpStandardCurve()
    //     0x8d5388: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d538c: LeaveFrame
    //     0x8d538c: mov             SP, fp
    //     0x8d5390: ldp             fp, lr, [SP], #0x10
    // 0x8d5394: ret
    //     0x8d5394: ret             
    // 0x8d5398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d539c: b               #0x8d52e4
  }
  [closure] static ECCurve_brainpoolp192r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d53a0, size: 0x20
    // 0x8d53a0: EnterFrame
    //     0x8d53a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d53a4: mov             fp, SP
    // 0x8d53a8: r0 = ECCurve_brainpoolp192r1()
    //     0x8d53a8: bl              #0x8d53c0  ; AllocateECCurve_brainpoolp192r1Stub -> ECCurve_brainpoolp192r1 (size=0xc)
    // 0x8d53ac: ldr             x1, [fp, #0x18]
    // 0x8d53b0: StoreField: r0->field_7 = r1
    //     0x8d53b0: stur            w1, [x0, #7]
    // 0x8d53b4: LeaveFrame
    //     0x8d53b4: mov             SP, fp
    //     0x8d53b8: ldp             fp, lr, [SP], #0x10
    // 0x8d53bc: ret
    //     0x8d53bc: ret             
  }
}
