// lib: impl.ec_domain_parameters.brainpoolp192t1, url: package:pointycastle/ecc/curves/brainpoolp192t1.dart

// class id: 1050966, size: 0x8
class :: {
}

// class id: 636, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp192t1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xec0

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d50bc, size: 0x58
    // 0x8d50bc: EnterFrame
    //     0x8d50bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d50c0: mov             fp, SP
    // 0x8d50c4: AllocStack(0x8)
    //     0x8d50c4: sub             SP, SP, #8
    // 0x8d50c8: r0 = StaticFactoryConfig()
    //     0x8d50c8: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d50cc: mov             x3, x0
    // 0x8d50d0: r0 = "brainpoolp192t1"
    //     0x8d50d0: add             x0, PP, #0x19, lsl #12  ; [pp+0x191f0] "brainpoolp192t1"
    //     0x8d50d4: ldr             x0, [x0, #0x1f0]
    // 0x8d50d8: stur            x3, [fp, #-8]
    // 0x8d50dc: StoreField: r3->field_b = r0
    //     0x8d50dc: stur            w0, [x3, #0xb]
    // 0x8d50e0: r1 = Function '<anonymous closure>': static.
    //     0x8d50e0: add             x1, PP, #0x19, lsl #12  ; [pp+0x191f8] AnonymousClosure: static (0x8d5114), in [package:pointycastle/ecc/curves/brainpoolp192t1.dart] ECCurve_brainpoolp192t1::factoryConfig (0x8d50bc)
    //     0x8d50e4: ldr             x1, [x1, #0x1f8]
    // 0x8d50e8: r2 = Null
    //     0x8d50e8: mov             x2, NULL
    // 0x8d50ec: r0 = AllocateClosure()
    //     0x8d50ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d50f0: mov             x1, x0
    // 0x8d50f4: ldur            x0, [fp, #-8]
    // 0x8d50f8: StoreField: r0->field_f = r1
    //     0x8d50f8: stur            w1, [x0, #0xf]
    // 0x8d50fc: r1 = ECDomainParameters
    //     0x8d50fc: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d5100: ldr             x1, [x1, #0x6e8]
    // 0x8d5104: StoreField: r0->field_7 = r1
    //     0x8d5104: stur            w1, [x0, #7]
    // 0x8d5108: LeaveFrame
    //     0x8d5108: mov             SP, fp
    //     0x8d510c: ldp             fp, lr, [SP], #0x10
    // 0x8d5110: ret
    //     0x8d5110: ret             
  }
  [closure] static ECCurve_brainpoolp192t1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d5114, size: 0x30
    // 0x8d5114: EnterFrame
    //     0x8d5114: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5118: mov             fp, SP
    // 0x8d511c: CheckStackOverflow
    //     0x8d511c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5120: cmp             SP, x16
    //     0x8d5124: b.ls            #0x8d513c
    // 0x8d5128: r1 = Null
    //     0x8d5128: mov             x1, NULL
    // 0x8d512c: r0 = ECCurve_brainpoolp192t1()
    //     0x8d512c: bl              #0x8d5144  ; [package:pointycastle/ecc/curves/brainpoolp192t1.dart] ECCurve_brainpoolp192t1::ECCurve_brainpoolp192t1
    // 0x8d5130: LeaveFrame
    //     0x8d5130: mov             SP, fp
    //     0x8d5134: ldp             fp, lr, [SP], #0x10
    // 0x8d5138: ret
    //     0x8d5138: ret             
    // 0x8d513c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d513c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5140: b               #0x8d5128
  }
  factory ECCurve_brainpoolp192t1 ECCurve_brainpoolp192t1(dynamic) {
    // ** addr: 0x8d5144, size: 0xd4
    // 0x8d5144: EnterFrame
    //     0x8d5144: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5148: mov             fp, SP
    // 0x8d514c: AllocStack(0x40)
    //     0x8d514c: sub             SP, SP, #0x40
    // 0x8d5150: CheckStackOverflow
    //     0x8d5150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5154: cmp             SP, x16
    //     0x8d5158: b.ls            #0x8d5210
    // 0x8d515c: r1 = "c302f41d932a36cda7a3463093d18db78fce476de1a86297"
    //     0x8d515c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19200] "c302f41d932a36cda7a3463093d18db78fce476de1a86297"
    //     0x8d5160: ldr             x1, [x1, #0x200]
    // 0x8d5164: r2 = 32
    //     0x8d5164: movz            x2, #0x20
    // 0x8d5168: r0 = parse()
    //     0x8d5168: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d516c: r1 = "c302f41d932a36cda7a3463093d18db78fce476de1a86294"
    //     0x8d516c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19208] "c302f41d932a36cda7a3463093d18db78fce476de1a86294"
    //     0x8d5170: ldr             x1, [x1, #0x208]
    // 0x8d5174: r2 = 32
    //     0x8d5174: movz            x2, #0x20
    // 0x8d5178: stur            x0, [fp, #-8]
    // 0x8d517c: r0 = parse()
    //     0x8d517c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5180: r1 = "13d56ffaec78681e68f9deb43b35bec2fb68542e27897b79"
    //     0x8d5180: add             x1, PP, #0x19, lsl #12  ; [pp+0x19210] "13d56ffaec78681e68f9deb43b35bec2fb68542e27897b79"
    //     0x8d5184: ldr             x1, [x1, #0x210]
    // 0x8d5188: r2 = 32
    //     0x8d5188: movz            x2, #0x20
    // 0x8d518c: stur            x0, [fp, #-0x10]
    // 0x8d5190: r0 = parse()
    //     0x8d5190: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5194: r1 = "043ae9e58c82f63c30282e1fe7bbf43fa72c446af6f4618129097e2c5667c2223a902ab5ca449d0084b7e5b3de7ccc01c9"
    //     0x8d5194: add             x1, PP, #0x19, lsl #12  ; [pp+0x19218] "043ae9e58c82f63c30282e1fe7bbf43fa72c446af6f4618129097e2c5667c2223a902ab5ca449d0084b7e5b3de7ccc01c9"
    //     0x8d5198: ldr             x1, [x1, #0x218]
    // 0x8d519c: r2 = 32
    //     0x8d519c: movz            x2, #0x20
    // 0x8d51a0: stur            x0, [fp, #-0x18]
    // 0x8d51a4: r0 = parse()
    //     0x8d51a4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d51a8: r1 = "c302f41d932a36cda7a3462f9e9e916b5be8f1029ac4acc1"
    //     0x8d51a8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19220] "c302f41d932a36cda7a3462f9e9e916b5be8f1029ac4acc1"
    //     0x8d51ac: ldr             x1, [x1, #0x220]
    // 0x8d51b0: r2 = 32
    //     0x8d51b0: movz            x2, #0x20
    // 0x8d51b4: stur            x0, [fp, #-0x20]
    // 0x8d51b8: r0 = parse()
    //     0x8d51b8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d51bc: r1 = "1"
    //     0x8d51bc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d51c0: ldr             x1, [x1, #0x718]
    // 0x8d51c4: r2 = 32
    //     0x8d51c4: movz            x2, #0x20
    // 0x8d51c8: stur            x0, [fp, #-0x28]
    // 0x8d51cc: r0 = parse()
    //     0x8d51cc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d51d0: ldur            x16, [fp, #-0x28]
    // 0x8d51d4: ldur            lr, [fp, #-8]
    // 0x8d51d8: stp             lr, x16, [SP, #8]
    // 0x8d51dc: str             NULL, [SP]
    // 0x8d51e0: ldur            x3, [fp, #-0x10]
    // 0x8d51e4: ldur            x5, [fp, #-0x18]
    // 0x8d51e8: ldur            x6, [fp, #-0x20]
    // 0x8d51ec: mov             x7, x0
    // 0x8d51f0: r1 = "brainpoolp192t1"
    //     0x8d51f0: add             x1, PP, #0x19, lsl #12  ; [pp+0x191f0] "brainpoolp192t1"
    //     0x8d51f4: ldr             x1, [x1, #0x1f0]
    // 0x8d51f8: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp192t1 from Function '_make@972366774': static.
    //     0x8d51f8: add             x2, PP, #0x19, lsl #12  ; [pp+0x19228] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp192t1 from Function '_make@972366774': static. (0x7e54fb2d5218)
    //     0x8d51fc: ldr             x2, [x2, #0x228]
    // 0x8d5200: r0 = constructFpStandardCurve()
    //     0x8d5200: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d5204: LeaveFrame
    //     0x8d5204: mov             SP, fp
    //     0x8d5208: ldp             fp, lr, [SP], #0x10
    // 0x8d520c: ret
    //     0x8d520c: ret             
    // 0x8d5210: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5210: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5214: b               #0x8d515c
  }
  [closure] static ECCurve_brainpoolp192t1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d5218, size: 0x20
    // 0x8d5218: EnterFrame
    //     0x8d5218: stp             fp, lr, [SP, #-0x10]!
    //     0x8d521c: mov             fp, SP
    // 0x8d5220: r0 = ECCurve_brainpoolp192t1()
    //     0x8d5220: bl              #0x8d5238  ; AllocateECCurve_brainpoolp192t1Stub -> ECCurve_brainpoolp192t1 (size=0xc)
    // 0x8d5224: ldr             x1, [fp, #0x18]
    // 0x8d5228: StoreField: r0->field_7 = r1
    //     0x8d5228: stur            w1, [x0, #7]
    // 0x8d522c: LeaveFrame
    //     0x8d522c: mov             SP, fp
    //     0x8d5230: ldp             fp, lr, [SP], #0x10
    // 0x8d5234: ret
    //     0x8d5234: ret             
  }
}
