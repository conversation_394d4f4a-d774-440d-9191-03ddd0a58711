// lib: impl.ec_domain_parameters.brainpoolp224r1, url: package:pointycastle/ecc/curves/brainpoolp224r1.dart

// class id: 1050967, size: 0x8
class :: {
}

// class id: 635, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp224r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xec4

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d4f34, size: 0x58
    // 0x8d4f34: EnterFrame
    //     0x8d4f34: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4f38: mov             fp, SP
    // 0x8d4f3c: AllocStack(0x8)
    //     0x8d4f3c: sub             SP, SP, #8
    // 0x8d4f40: r0 = StaticFactoryConfig()
    //     0x8d4f40: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d4f44: mov             x3, x0
    // 0x8d4f48: r0 = "brainpoolp224r1"
    //     0x8d4f48: add             x0, PP, #0x19, lsl #12  ; [pp+0x191c0] "brainpoolp224r1"
    //     0x8d4f4c: ldr             x0, [x0, #0x1c0]
    // 0x8d4f50: stur            x3, [fp, #-8]
    // 0x8d4f54: StoreField: r3->field_b = r0
    //     0x8d4f54: stur            w0, [x3, #0xb]
    // 0x8d4f58: r1 = Function '<anonymous closure>': static.
    //     0x8d4f58: add             x1, PP, #0x19, lsl #12  ; [pp+0x191c8] AnonymousClosure: static (0x8d4f8c), in [package:pointycastle/ecc/curves/brainpoolp224r1.dart] ECCurve_brainpoolp224r1::factoryConfig (0x8d4f34)
    //     0x8d4f5c: ldr             x1, [x1, #0x1c8]
    // 0x8d4f60: r2 = Null
    //     0x8d4f60: mov             x2, NULL
    // 0x8d4f64: r0 = AllocateClosure()
    //     0x8d4f64: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d4f68: mov             x1, x0
    // 0x8d4f6c: ldur            x0, [fp, #-8]
    // 0x8d4f70: StoreField: r0->field_f = r1
    //     0x8d4f70: stur            w1, [x0, #0xf]
    // 0x8d4f74: r1 = ECDomainParameters
    //     0x8d4f74: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d4f78: ldr             x1, [x1, #0x6e8]
    // 0x8d4f7c: StoreField: r0->field_7 = r1
    //     0x8d4f7c: stur            w1, [x0, #7]
    // 0x8d4f80: LeaveFrame
    //     0x8d4f80: mov             SP, fp
    //     0x8d4f84: ldp             fp, lr, [SP], #0x10
    // 0x8d4f88: ret
    //     0x8d4f88: ret             
  }
  [closure] static ECCurve_brainpoolp224r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d4f8c, size: 0x30
    // 0x8d4f8c: EnterFrame
    //     0x8d4f8c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4f90: mov             fp, SP
    // 0x8d4f94: CheckStackOverflow
    //     0x8d4f94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4f98: cmp             SP, x16
    //     0x8d4f9c: b.ls            #0x8d4fb4
    // 0x8d4fa0: r1 = Null
    //     0x8d4fa0: mov             x1, NULL
    // 0x8d4fa4: r0 = ECCurve_brainpoolp224r1()
    //     0x8d4fa4: bl              #0x8d4fbc  ; [package:pointycastle/ecc/curves/brainpoolp224r1.dart] ECCurve_brainpoolp224r1::ECCurve_brainpoolp224r1
    // 0x8d4fa8: LeaveFrame
    //     0x8d4fa8: mov             SP, fp
    //     0x8d4fac: ldp             fp, lr, [SP], #0x10
    // 0x8d4fb0: ret
    //     0x8d4fb0: ret             
    // 0x8d4fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4fb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4fb8: b               #0x8d4fa0
  }
  factory ECCurve_brainpoolp224r1 ECCurve_brainpoolp224r1(dynamic) {
    // ** addr: 0x8d4fbc, size: 0xd4
    // 0x8d4fbc: EnterFrame
    //     0x8d4fbc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4fc0: mov             fp, SP
    // 0x8d4fc4: AllocStack(0x40)
    //     0x8d4fc4: sub             SP, SP, #0x40
    // 0x8d4fc8: CheckStackOverflow
    //     0x8d4fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4fcc: cmp             SP, x16
    //     0x8d4fd0: b.ls            #0x8d5088
    // 0x8d4fd4: r1 = "d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff"
    //     0x8d4fd4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19190] "d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff"
    //     0x8d4fd8: ldr             x1, [x1, #0x190]
    // 0x8d4fdc: r2 = 32
    //     0x8d4fdc: movz            x2, #0x20
    // 0x8d4fe0: r0 = parse()
    //     0x8d4fe0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4fe4: r1 = "68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43"
    //     0x8d4fe4: add             x1, PP, #0x19, lsl #12  ; [pp+0x191d0] "68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43"
    //     0x8d4fe8: ldr             x1, [x1, #0x1d0]
    // 0x8d4fec: r2 = 32
    //     0x8d4fec: movz            x2, #0x20
    // 0x8d4ff0: stur            x0, [fp, #-8]
    // 0x8d4ff4: r0 = parse()
    //     0x8d4ff4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4ff8: r1 = "2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b"
    //     0x8d4ff8: add             x1, PP, #0x19, lsl #12  ; [pp+0x191d8] "2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b"
    //     0x8d4ffc: ldr             x1, [x1, #0x1d8]
    // 0x8d5000: r2 = 32
    //     0x8d5000: movz            x2, #0x20
    // 0x8d5004: stur            x0, [fp, #-0x10]
    // 0x8d5008: r0 = parse()
    //     0x8d5008: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d500c: r1 = "040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd"
    //     0x8d500c: add             x1, PP, #0x19, lsl #12  ; [pp+0x191e0] "040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd"
    //     0x8d5010: ldr             x1, [x1, #0x1e0]
    // 0x8d5014: r2 = 32
    //     0x8d5014: movz            x2, #0x20
    // 0x8d5018: stur            x0, [fp, #-0x18]
    // 0x8d501c: r0 = parse()
    //     0x8d501c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5020: r1 = "d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f"
    //     0x8d5020: add             x1, PP, #0x19, lsl #12  ; [pp+0x191b0] "d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f"
    //     0x8d5024: ldr             x1, [x1, #0x1b0]
    // 0x8d5028: r2 = 32
    //     0x8d5028: movz            x2, #0x20
    // 0x8d502c: stur            x0, [fp, #-0x20]
    // 0x8d5030: r0 = parse()
    //     0x8d5030: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5034: r1 = "1"
    //     0x8d5034: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d5038: ldr             x1, [x1, #0x718]
    // 0x8d503c: r2 = 32
    //     0x8d503c: movz            x2, #0x20
    // 0x8d5040: stur            x0, [fp, #-0x28]
    // 0x8d5044: r0 = parse()
    //     0x8d5044: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d5048: ldur            x16, [fp, #-0x28]
    // 0x8d504c: ldur            lr, [fp, #-8]
    // 0x8d5050: stp             lr, x16, [SP, #8]
    // 0x8d5054: str             NULL, [SP]
    // 0x8d5058: ldur            x3, [fp, #-0x10]
    // 0x8d505c: ldur            x5, [fp, #-0x18]
    // 0x8d5060: ldur            x6, [fp, #-0x20]
    // 0x8d5064: mov             x7, x0
    // 0x8d5068: r1 = "brainpoolp224r1"
    //     0x8d5068: add             x1, PP, #0x19, lsl #12  ; [pp+0x191c0] "brainpoolp224r1"
    //     0x8d506c: ldr             x1, [x1, #0x1c0]
    // 0x8d5070: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp224r1 from Function '_make@973447697': static.
    //     0x8d5070: add             x2, PP, #0x19, lsl #12  ; [pp+0x191e8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp224r1 from Function '_make@973447697': static. (0x7e54fb2d5090)
    //     0x8d5074: ldr             x2, [x2, #0x1e8]
    // 0x8d5078: r0 = constructFpStandardCurve()
    //     0x8d5078: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d507c: LeaveFrame
    //     0x8d507c: mov             SP, fp
    //     0x8d5080: ldp             fp, lr, [SP], #0x10
    // 0x8d5084: ret
    //     0x8d5084: ret             
    // 0x8d5088: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5088: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d508c: b               #0x8d4fd4
  }
  [closure] static ECCurve_brainpoolp224r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d5090, size: 0x20
    // 0x8d5090: EnterFrame
    //     0x8d5090: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5094: mov             fp, SP
    // 0x8d5098: r0 = ECCurve_brainpoolp224r1()
    //     0x8d5098: bl              #0x8d50b0  ; AllocateECCurve_brainpoolp224r1Stub -> ECCurve_brainpoolp224r1 (size=0xc)
    // 0x8d509c: ldr             x1, [fp, #0x18]
    // 0x8d50a0: StoreField: r0->field_7 = r1
    //     0x8d50a0: stur            w1, [x0, #7]
    // 0x8d50a4: LeaveFrame
    //     0x8d50a4: mov             SP, fp
    //     0x8d50a8: ldp             fp, lr, [SP], #0x10
    // 0x8d50ac: ret
    //     0x8d50ac: ret             
  }
}
