// lib: impl.ec_domain_parameters.brainpoolp224t1, url: package:pointycastle/ecc/curves/brainpoolp224t1.dart

// class id: 1050968, size: 0x8
class :: {
}

// class id: 634, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp224t1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xec8

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d4d50, size: 0x58
    // 0x8d4d50: EnterFrame
    //     0x8d4d50: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4d54: mov             fp, SP
    // 0x8d4d58: AllocStack(0x8)
    //     0x8d4d58: sub             SP, SP, #8
    // 0x8d4d5c: r0 = StaticFactoryConfig()
    //     0x8d4d5c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d4d60: mov             x3, x0
    // 0x8d4d64: r0 = "brainpoolp224t1"
    //     0x8d4d64: add             x0, PP, #0x19, lsl #12  ; [pp+0x19180] "brainpoolp224t1"
    //     0x8d4d68: ldr             x0, [x0, #0x180]
    // 0x8d4d6c: stur            x3, [fp, #-8]
    // 0x8d4d70: StoreField: r3->field_b = r0
    //     0x8d4d70: stur            w0, [x3, #0xb]
    // 0x8d4d74: r1 = Function '<anonymous closure>': static.
    //     0x8d4d74: add             x1, PP, #0x19, lsl #12  ; [pp+0x19188] AnonymousClosure: static (0x8d4da8), in [package:pointycastle/ecc/curves/brainpoolp224t1.dart] ECCurve_brainpoolp224t1::factoryConfig (0x8d4d50)
    //     0x8d4d78: ldr             x1, [x1, #0x188]
    // 0x8d4d7c: r2 = Null
    //     0x8d4d7c: mov             x2, NULL
    // 0x8d4d80: r0 = AllocateClosure()
    //     0x8d4d80: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d4d84: mov             x1, x0
    // 0x8d4d88: ldur            x0, [fp, #-8]
    // 0x8d4d8c: StoreField: r0->field_f = r1
    //     0x8d4d8c: stur            w1, [x0, #0xf]
    // 0x8d4d90: r1 = ECDomainParameters
    //     0x8d4d90: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d4d94: ldr             x1, [x1, #0x6e8]
    // 0x8d4d98: StoreField: r0->field_7 = r1
    //     0x8d4d98: stur            w1, [x0, #7]
    // 0x8d4d9c: LeaveFrame
    //     0x8d4d9c: mov             SP, fp
    //     0x8d4da0: ldp             fp, lr, [SP], #0x10
    // 0x8d4da4: ret
    //     0x8d4da4: ret             
  }
  [closure] static ECCurve_brainpoolp224t1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d4da8, size: 0x30
    // 0x8d4da8: EnterFrame
    //     0x8d4da8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4dac: mov             fp, SP
    // 0x8d4db0: CheckStackOverflow
    //     0x8d4db0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4db4: cmp             SP, x16
    //     0x8d4db8: b.ls            #0x8d4dd0
    // 0x8d4dbc: r1 = Null
    //     0x8d4dbc: mov             x1, NULL
    // 0x8d4dc0: r0 = ECCurve_brainpoolp224t1()
    //     0x8d4dc0: bl              #0x8d4dd8  ; [package:pointycastle/ecc/curves/brainpoolp224t1.dart] ECCurve_brainpoolp224t1::ECCurve_brainpoolp224t1
    // 0x8d4dc4: LeaveFrame
    //     0x8d4dc4: mov             SP, fp
    //     0x8d4dc8: ldp             fp, lr, [SP], #0x10
    // 0x8d4dcc: ret
    //     0x8d4dcc: ret             
    // 0x8d4dd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4dd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4dd4: b               #0x8d4dbc
  }
  factory ECCurve_brainpoolp224t1 ECCurve_brainpoolp224t1(dynamic) {
    // ** addr: 0x8d4dd8, size: 0xd4
    // 0x8d4dd8: EnterFrame
    //     0x8d4dd8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4ddc: mov             fp, SP
    // 0x8d4de0: AllocStack(0x40)
    //     0x8d4de0: sub             SP, SP, #0x40
    // 0x8d4de4: CheckStackOverflow
    //     0x8d4de4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4de8: cmp             SP, x16
    //     0x8d4dec: b.ls            #0x8d4ea4
    // 0x8d4df0: r1 = "d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff"
    //     0x8d4df0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19190] "d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff"
    //     0x8d4df4: ldr             x1, [x1, #0x190]
    // 0x8d4df8: r2 = 32
    //     0x8d4df8: movz            x2, #0x20
    // 0x8d4dfc: r0 = parse()
    //     0x8d4dfc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4e00: r1 = "d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fc"
    //     0x8d4e00: add             x1, PP, #0x19, lsl #12  ; [pp+0x19198] "d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fc"
    //     0x8d4e04: ldr             x1, [x1, #0x198]
    // 0x8d4e08: r2 = 32
    //     0x8d4e08: movz            x2, #0x20
    // 0x8d4e0c: stur            x0, [fp, #-8]
    // 0x8d4e10: r0 = parse()
    //     0x8d4e10: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4e14: r1 = "4b337d934104cd7bef271bf60ced1ed20da14c08b3bb64f18a60888d"
    //     0x8d4e14: add             x1, PP, #0x19, lsl #12  ; [pp+0x191a0] "4b337d934104cd7bef271bf60ced1ed20da14c08b3bb64f18a60888d"
    //     0x8d4e18: ldr             x1, [x1, #0x1a0]
    // 0x8d4e1c: r2 = 32
    //     0x8d4e1c: movz            x2, #0x20
    // 0x8d4e20: stur            x0, [fp, #-0x10]
    // 0x8d4e24: r0 = parse()
    //     0x8d4e24: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4e28: r1 = "046ab1e344ce25ff3896424e7ffe14762ecb49f8928ac0c76029b4d5800374e9f5143e568cd23f3f4d7c0d4b1e41c8cc0d1c6abd5f1a46db4c"
    //     0x8d4e28: add             x1, PP, #0x19, lsl #12  ; [pp+0x191a8] "046ab1e344ce25ff3896424e7ffe14762ecb49f8928ac0c76029b4d5800374e9f5143e568cd23f3f4d7c0d4b1e41c8cc0d1c6abd5f1a46db4c"
    //     0x8d4e2c: ldr             x1, [x1, #0x1a8]
    // 0x8d4e30: r2 = 32
    //     0x8d4e30: movz            x2, #0x20
    // 0x8d4e34: stur            x0, [fp, #-0x18]
    // 0x8d4e38: r0 = parse()
    //     0x8d4e38: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4e3c: r1 = "d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f"
    //     0x8d4e3c: add             x1, PP, #0x19, lsl #12  ; [pp+0x191b0] "d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f"
    //     0x8d4e40: ldr             x1, [x1, #0x1b0]
    // 0x8d4e44: r2 = 32
    //     0x8d4e44: movz            x2, #0x20
    // 0x8d4e48: stur            x0, [fp, #-0x20]
    // 0x8d4e4c: r0 = parse()
    //     0x8d4e4c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4e50: r1 = "1"
    //     0x8d4e50: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d4e54: ldr             x1, [x1, #0x718]
    // 0x8d4e58: r2 = 32
    //     0x8d4e58: movz            x2, #0x20
    // 0x8d4e5c: stur            x0, [fp, #-0x28]
    // 0x8d4e60: r0 = parse()
    //     0x8d4e60: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4e64: ldur            x16, [fp, #-0x28]
    // 0x8d4e68: ldur            lr, [fp, #-8]
    // 0x8d4e6c: stp             lr, x16, [SP, #8]
    // 0x8d4e70: str             NULL, [SP]
    // 0x8d4e74: ldur            x3, [fp, #-0x10]
    // 0x8d4e78: ldur            x5, [fp, #-0x18]
    // 0x8d4e7c: ldur            x6, [fp, #-0x20]
    // 0x8d4e80: mov             x7, x0
    // 0x8d4e84: r1 = "brainpoolp224t1"
    //     0x8d4e84: add             x1, PP, #0x19, lsl #12  ; [pp+0x19180] "brainpoolp224t1"
    //     0x8d4e88: ldr             x1, [x1, #0x180]
    // 0x8d4e8c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt?, List<int>?) => ECCurve_brainpoolp224t1 from Function '_make@974044209': static.
    //     0x8d4e8c: add             x2, PP, #0x19, lsl #12  ; [pp+0x191b8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt?, List<int>?) => ECCurve_brainpoolp224t1 from Function '_make@974044209': static. (0x7e54fb2d4eac)
    //     0x8d4e90: ldr             x2, [x2, #0x1b8]
    // 0x8d4e94: r0 = constructFpStandardCurve()
    //     0x8d4e94: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d4e98: LeaveFrame
    //     0x8d4e98: mov             SP, fp
    //     0x8d4e9c: ldp             fp, lr, [SP], #0x10
    // 0x8d4ea0: ret
    //     0x8d4ea0: ret             
    // 0x8d4ea4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4ea4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4ea8: b               #0x8d4df0
  }
  [closure] static ECCurve_brainpoolp224t1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt?, List<int>?) {
    // ** addr: 0x8d4eac, size: 0x7c
    // 0x8d4eac: EnterFrame
    //     0x8d4eac: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4eb0: mov             fp, SP
    // 0x8d4eb4: AllocStack(0x8)
    //     0x8d4eb4: sub             SP, SP, #8
    // 0x8d4eb8: CheckStackOverflow
    //     0x8d4eb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4ebc: cmp             SP, x16
    //     0x8d4ec0: b.ls            #0x8d4f20
    // 0x8d4ec4: r0 = ECCurve_brainpoolp224t1()
    //     0x8d4ec4: bl              #0x8d4f28  ; AllocateECCurve_brainpoolp224t1Stub -> ECCurve_brainpoolp224t1 (size=0xc)
    // 0x8d4ec8: mov             x1, x0
    // 0x8d4ecc: ldr             x0, [fp, #0x18]
    // 0x8d4ed0: stur            x1, [fp, #-8]
    // 0x8d4ed4: StoreField: r1->field_7 = r0
    //     0x8d4ed4: stur            w0, [x1, #7]
    // 0x8d4ed8: cmp             w0, NULL
    // 0x8d4edc: b.ne            #0x8d4f10
    // 0x8d4ee0: r0 = InitLateStaticField(0x334) // [dart:core] _BigIntImpl::one
    //     0x8d4ee0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d4ee4: ldr             x0, [x0, #0x668]
    //     0x8d4ee8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d4eec: cmp             w0, w16
    //     0x8d4ef0: b.ne            #0x8d4f00
    //     0x8d4ef4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18820] Field <<EMAIL>>: static late final (offset: 0x334)
    //     0x8d4ef8: ldr             x2, [x2, #0x820]
    //     0x8d4efc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d4f00: mov             x1, x0
    // 0x8d4f04: ldur            x0, [fp, #-8]
    // 0x8d4f08: StoreField: r0->field_7 = r1
    //     0x8d4f08: stur            w1, [x0, #7]
    // 0x8d4f0c: b               #0x8d4f14
    // 0x8d4f10: mov             x0, x1
    // 0x8d4f14: LeaveFrame
    //     0x8d4f14: mov             SP, fp
    //     0x8d4f18: ldp             fp, lr, [SP], #0x10
    // 0x8d4f1c: ret
    //     0x8d4f1c: ret             
    // 0x8d4f20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4f20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4f24: b               #0x8d4ec4
  }
}
