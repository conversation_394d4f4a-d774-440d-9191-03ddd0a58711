// lib: impl.ec_domain_parameters.brainpoolp256r1, url: package:pointycastle/ecc/curves/brainpoolp256r1.dart

// class id: 1050969, size: 0x8
class :: {
}

// class id: 633, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp256r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xecc

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d4bc8, size: 0x58
    // 0x8d4bc8: EnterFrame
    //     0x8d4bc8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4bcc: mov             fp, SP
    // 0x8d4bd0: AllocStack(0x8)
    //     0x8d4bd0: sub             SP, SP, #8
    // 0x8d4bd4: r0 = StaticFactoryConfig()
    //     0x8d4bd4: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d4bd8: mov             x3, x0
    // 0x8d4bdc: r0 = "brainpoolp256r1"
    //     0x8d4bdc: add             x0, PP, #0x19, lsl #12  ; [pp+0x19150] "brainpoolp256r1"
    //     0x8d4be0: ldr             x0, [x0, #0x150]
    // 0x8d4be4: stur            x3, [fp, #-8]
    // 0x8d4be8: StoreField: r3->field_b = r0
    //     0x8d4be8: stur            w0, [x3, #0xb]
    // 0x8d4bec: r1 = Function '<anonymous closure>': static.
    //     0x8d4bec: add             x1, PP, #0x19, lsl #12  ; [pp+0x19158] AnonymousClosure: static (0x8d4c20), in [package:pointycastle/ecc/curves/brainpoolp256r1.dart] ECCurve_brainpoolp256r1::factoryConfig (0x8d4bc8)
    //     0x8d4bf0: ldr             x1, [x1, #0x158]
    // 0x8d4bf4: r2 = Null
    //     0x8d4bf4: mov             x2, NULL
    // 0x8d4bf8: r0 = AllocateClosure()
    //     0x8d4bf8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d4bfc: mov             x1, x0
    // 0x8d4c00: ldur            x0, [fp, #-8]
    // 0x8d4c04: StoreField: r0->field_f = r1
    //     0x8d4c04: stur            w1, [x0, #0xf]
    // 0x8d4c08: r1 = ECDomainParameters
    //     0x8d4c08: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d4c0c: ldr             x1, [x1, #0x6e8]
    // 0x8d4c10: StoreField: r0->field_7 = r1
    //     0x8d4c10: stur            w1, [x0, #7]
    // 0x8d4c14: LeaveFrame
    //     0x8d4c14: mov             SP, fp
    //     0x8d4c18: ldp             fp, lr, [SP], #0x10
    // 0x8d4c1c: ret
    //     0x8d4c1c: ret             
  }
  [closure] static ECCurve_brainpoolp256r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d4c20, size: 0x30
    // 0x8d4c20: EnterFrame
    //     0x8d4c20: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4c24: mov             fp, SP
    // 0x8d4c28: CheckStackOverflow
    //     0x8d4c28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4c2c: cmp             SP, x16
    //     0x8d4c30: b.ls            #0x8d4c48
    // 0x8d4c34: r1 = Null
    //     0x8d4c34: mov             x1, NULL
    // 0x8d4c38: r0 = ECCurve_brainpoolp256r1()
    //     0x8d4c38: bl              #0x8d4c50  ; [package:pointycastle/ecc/curves/brainpoolp256r1.dart] ECCurve_brainpoolp256r1::ECCurve_brainpoolp256r1
    // 0x8d4c3c: LeaveFrame
    //     0x8d4c3c: mov             SP, fp
    //     0x8d4c40: ldp             fp, lr, [SP], #0x10
    // 0x8d4c44: ret
    //     0x8d4c44: ret             
    // 0x8d4c48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4c48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4c4c: b               #0x8d4c34
  }
  factory ECCurve_brainpoolp256r1 ECCurve_brainpoolp256r1(dynamic) {
    // ** addr: 0x8d4c50, size: 0xd4
    // 0x8d4c50: EnterFrame
    //     0x8d4c50: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4c54: mov             fp, SP
    // 0x8d4c58: AllocStack(0x40)
    //     0x8d4c58: sub             SP, SP, #0x40
    // 0x8d4c5c: CheckStackOverflow
    //     0x8d4c5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4c60: cmp             SP, x16
    //     0x8d4c64: b.ls            #0x8d4d1c
    // 0x8d4c68: r1 = "a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377"
    //     0x8d4c68: add             x1, PP, #0x19, lsl #12  ; [pp+0x19120] "a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377"
    //     0x8d4c6c: ldr             x1, [x1, #0x120]
    // 0x8d4c70: r2 = 32
    //     0x8d4c70: movz            x2, #0x20
    // 0x8d4c74: r0 = parse()
    //     0x8d4c74: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4c78: r1 = "7d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9"
    //     0x8d4c78: add             x1, PP, #0x19, lsl #12  ; [pp+0x19160] "7d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9"
    //     0x8d4c7c: ldr             x1, [x1, #0x160]
    // 0x8d4c80: r2 = 32
    //     0x8d4c80: movz            x2, #0x20
    // 0x8d4c84: stur            x0, [fp, #-8]
    // 0x8d4c88: r0 = parse()
    //     0x8d4c88: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4c8c: r1 = "26dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b6"
    //     0x8d4c8c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19168] "26dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b6"
    //     0x8d4c90: ldr             x1, [x1, #0x168]
    // 0x8d4c94: r2 = 32
    //     0x8d4c94: movz            x2, #0x20
    // 0x8d4c98: stur            x0, [fp, #-0x10]
    // 0x8d4c9c: r0 = parse()
    //     0x8d4c9c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4ca0: r1 = "048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997"
    //     0x8d4ca0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19170] "048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997"
    //     0x8d4ca4: ldr             x1, [x1, #0x170]
    // 0x8d4ca8: r2 = 32
    //     0x8d4ca8: movz            x2, #0x20
    // 0x8d4cac: stur            x0, [fp, #-0x18]
    // 0x8d4cb0: r0 = parse()
    //     0x8d4cb0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4cb4: r1 = "a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7"
    //     0x8d4cb4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19140] "a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7"
    //     0x8d4cb8: ldr             x1, [x1, #0x140]
    // 0x8d4cbc: r2 = 32
    //     0x8d4cbc: movz            x2, #0x20
    // 0x8d4cc0: stur            x0, [fp, #-0x20]
    // 0x8d4cc4: r0 = parse()
    //     0x8d4cc4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4cc8: r1 = "1"
    //     0x8d4cc8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d4ccc: ldr             x1, [x1, #0x718]
    // 0x8d4cd0: r2 = 32
    //     0x8d4cd0: movz            x2, #0x20
    // 0x8d4cd4: stur            x0, [fp, #-0x28]
    // 0x8d4cd8: r0 = parse()
    //     0x8d4cd8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4cdc: ldur            x16, [fp, #-0x28]
    // 0x8d4ce0: ldur            lr, [fp, #-8]
    // 0x8d4ce4: stp             lr, x16, [SP, #8]
    // 0x8d4ce8: str             NULL, [SP]
    // 0x8d4cec: ldur            x3, [fp, #-0x10]
    // 0x8d4cf0: ldur            x5, [fp, #-0x18]
    // 0x8d4cf4: ldur            x6, [fp, #-0x20]
    // 0x8d4cf8: mov             x7, x0
    // 0x8d4cfc: r1 = "brainpoolp256r1"
    //     0x8d4cfc: add             x1, PP, #0x19, lsl #12  ; [pp+0x19150] "brainpoolp256r1"
    //     0x8d4d00: ldr             x1, [x1, #0x150]
    // 0x8d4d04: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp256r1 from Function '_make@975275808': static.
    //     0x8d4d04: add             x2, PP, #0x19, lsl #12  ; [pp+0x19178] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp256r1 from Function '_make@975275808': static. (0x7e54fb2d4d24)
    //     0x8d4d08: ldr             x2, [x2, #0x178]
    // 0x8d4d0c: r0 = constructFpStandardCurve()
    //     0x8d4d0c: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d4d10: LeaveFrame
    //     0x8d4d10: mov             SP, fp
    //     0x8d4d14: ldp             fp, lr, [SP], #0x10
    // 0x8d4d18: ret
    //     0x8d4d18: ret             
    // 0x8d4d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4d1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4d20: b               #0x8d4c68
  }
  [closure] static ECCurve_brainpoolp256r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d4d24, size: 0x20
    // 0x8d4d24: EnterFrame
    //     0x8d4d24: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4d28: mov             fp, SP
    // 0x8d4d2c: r0 = ECCurve_brainpoolp256r1()
    //     0x8d4d2c: bl              #0x8d4d44  ; AllocateECCurve_brainpoolp256r1Stub -> ECCurve_brainpoolp256r1 (size=0xc)
    // 0x8d4d30: ldr             x1, [fp, #0x18]
    // 0x8d4d34: StoreField: r0->field_7 = r1
    //     0x8d4d34: stur            w1, [x0, #7]
    // 0x8d4d38: LeaveFrame
    //     0x8d4d38: mov             SP, fp
    //     0x8d4d3c: ldp             fp, lr, [SP], #0x10
    // 0x8d4d40: ret
    //     0x8d4d40: ret             
  }
}
