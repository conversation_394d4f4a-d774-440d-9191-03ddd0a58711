// lib: impl.ec_domain_parameters.brainpoolp256t1, url: package:pointycastle/ecc/curves/brainpoolp256t1.dart

// class id: 1050970, size: 0x8
class :: {
}

// class id: 632, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp256t1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xed0

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d4a40, size: 0x58
    // 0x8d4a40: EnterFrame
    //     0x8d4a40: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4a44: mov             fp, SP
    // 0x8d4a48: AllocStack(0x8)
    //     0x8d4a48: sub             SP, SP, #8
    // 0x8d4a4c: r0 = StaticFactoryConfig()
    //     0x8d4a4c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d4a50: mov             x3, x0
    // 0x8d4a54: r0 = "brainpoolp256t1"
    //     0x8d4a54: add             x0, PP, #0x19, lsl #12  ; [pp+0x19110] "brainpoolp256t1"
    //     0x8d4a58: ldr             x0, [x0, #0x110]
    // 0x8d4a5c: stur            x3, [fp, #-8]
    // 0x8d4a60: StoreField: r3->field_b = r0
    //     0x8d4a60: stur            w0, [x3, #0xb]
    // 0x8d4a64: r1 = Function '<anonymous closure>': static.
    //     0x8d4a64: add             x1, PP, #0x19, lsl #12  ; [pp+0x19118] AnonymousClosure: static (0x8d4a98), in [package:pointycastle/ecc/curves/brainpoolp256t1.dart] ECCurve_brainpoolp256t1::factoryConfig (0x8d4a40)
    //     0x8d4a68: ldr             x1, [x1, #0x118]
    // 0x8d4a6c: r2 = Null
    //     0x8d4a6c: mov             x2, NULL
    // 0x8d4a70: r0 = AllocateClosure()
    //     0x8d4a70: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d4a74: mov             x1, x0
    // 0x8d4a78: ldur            x0, [fp, #-8]
    // 0x8d4a7c: StoreField: r0->field_f = r1
    //     0x8d4a7c: stur            w1, [x0, #0xf]
    // 0x8d4a80: r1 = ECDomainParameters
    //     0x8d4a80: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d4a84: ldr             x1, [x1, #0x6e8]
    // 0x8d4a88: StoreField: r0->field_7 = r1
    //     0x8d4a88: stur            w1, [x0, #7]
    // 0x8d4a8c: LeaveFrame
    //     0x8d4a8c: mov             SP, fp
    //     0x8d4a90: ldp             fp, lr, [SP], #0x10
    // 0x8d4a94: ret
    //     0x8d4a94: ret             
  }
  [closure] static ECCurve_brainpoolp256t1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d4a98, size: 0x30
    // 0x8d4a98: EnterFrame
    //     0x8d4a98: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4a9c: mov             fp, SP
    // 0x8d4aa0: CheckStackOverflow
    //     0x8d4aa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4aa4: cmp             SP, x16
    //     0x8d4aa8: b.ls            #0x8d4ac0
    // 0x8d4aac: r1 = Null
    //     0x8d4aac: mov             x1, NULL
    // 0x8d4ab0: r0 = ECCurve_brainpoolp256t1()
    //     0x8d4ab0: bl              #0x8d4ac8  ; [package:pointycastle/ecc/curves/brainpoolp256t1.dart] ECCurve_brainpoolp256t1::ECCurve_brainpoolp256t1
    // 0x8d4ab4: LeaveFrame
    //     0x8d4ab4: mov             SP, fp
    //     0x8d4ab8: ldp             fp, lr, [SP], #0x10
    // 0x8d4abc: ret
    //     0x8d4abc: ret             
    // 0x8d4ac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4ac0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4ac4: b               #0x8d4aac
  }
  factory ECCurve_brainpoolp256t1 ECCurve_brainpoolp256t1(dynamic) {
    // ** addr: 0x8d4ac8, size: 0xd4
    // 0x8d4ac8: EnterFrame
    //     0x8d4ac8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4acc: mov             fp, SP
    // 0x8d4ad0: AllocStack(0x40)
    //     0x8d4ad0: sub             SP, SP, #0x40
    // 0x8d4ad4: CheckStackOverflow
    //     0x8d4ad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4ad8: cmp             SP, x16
    //     0x8d4adc: b.ls            #0x8d4b94
    // 0x8d4ae0: r1 = "a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377"
    //     0x8d4ae0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19120] "a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377"
    //     0x8d4ae4: ldr             x1, [x1, #0x120]
    // 0x8d4ae8: r2 = 32
    //     0x8d4ae8: movz            x2, #0x20
    // 0x8d4aec: r0 = parse()
    //     0x8d4aec: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4af0: r1 = "a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5374"
    //     0x8d4af0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19128] "a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5374"
    //     0x8d4af4: ldr             x1, [x1, #0x128]
    // 0x8d4af8: r2 = 32
    //     0x8d4af8: movz            x2, #0x20
    // 0x8d4afc: stur            x0, [fp, #-8]
    // 0x8d4b00: r0 = parse()
    //     0x8d4b00: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4b04: r1 = "662c61c430d84ea4fe66a7733d0b76b7bf93ebc4af2f49256ae58101fee92b04"
    //     0x8d4b04: add             x1, PP, #0x19, lsl #12  ; [pp+0x19130] "662c61c430d84ea4fe66a7733d0b76b7bf93ebc4af2f49256ae58101fee92b04"
    //     0x8d4b08: ldr             x1, [x1, #0x130]
    // 0x8d4b0c: r2 = 32
    //     0x8d4b0c: movz            x2, #0x20
    // 0x8d4b10: stur            x0, [fp, #-0x10]
    // 0x8d4b14: r0 = parse()
    //     0x8d4b14: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4b18: r1 = "04a3e8eb3cc1cfe7b7732213b23a656149afa142c47aafbc2b79a191562e1305f42d996c823439c56d7f7b22e14644417e69bcb6de39d027001dabe8f35b25c9be"
    //     0x8d4b18: add             x1, PP, #0x19, lsl #12  ; [pp+0x19138] "04a3e8eb3cc1cfe7b7732213b23a656149afa142c47aafbc2b79a191562e1305f42d996c823439c56d7f7b22e14644417e69bcb6de39d027001dabe8f35b25c9be"
    //     0x8d4b1c: ldr             x1, [x1, #0x138]
    // 0x8d4b20: r2 = 32
    //     0x8d4b20: movz            x2, #0x20
    // 0x8d4b24: stur            x0, [fp, #-0x18]
    // 0x8d4b28: r0 = parse()
    //     0x8d4b28: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4b2c: r1 = "a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7"
    //     0x8d4b2c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19140] "a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7"
    //     0x8d4b30: ldr             x1, [x1, #0x140]
    // 0x8d4b34: r2 = 32
    //     0x8d4b34: movz            x2, #0x20
    // 0x8d4b38: stur            x0, [fp, #-0x20]
    // 0x8d4b3c: r0 = parse()
    //     0x8d4b3c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4b40: r1 = "1"
    //     0x8d4b40: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d4b44: ldr             x1, [x1, #0x718]
    // 0x8d4b48: r2 = 32
    //     0x8d4b48: movz            x2, #0x20
    // 0x8d4b4c: stur            x0, [fp, #-0x28]
    // 0x8d4b50: r0 = parse()
    //     0x8d4b50: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4b54: ldur            x16, [fp, #-0x28]
    // 0x8d4b58: ldur            lr, [fp, #-8]
    // 0x8d4b5c: stp             lr, x16, [SP, #8]
    // 0x8d4b60: str             NULL, [SP]
    // 0x8d4b64: ldur            x3, [fp, #-0x10]
    // 0x8d4b68: ldur            x5, [fp, #-0x18]
    // 0x8d4b6c: ldur            x6, [fp, #-0x20]
    // 0x8d4b70: mov             x7, x0
    // 0x8d4b74: r1 = "brainpoolp256t1"
    //     0x8d4b74: add             x1, PP, #0x19, lsl #12  ; [pp+0x19110] "brainpoolp256t1"
    //     0x8d4b78: ldr             x1, [x1, #0x110]
    // 0x8d4b7c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp256t1 from Function '_make@976262991': static.
    //     0x8d4b7c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19148] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp256t1 from Function '_make@976262991': static. (0x7e54fb2d4b9c)
    //     0x8d4b80: ldr             x2, [x2, #0x148]
    // 0x8d4b84: r0 = constructFpStandardCurve()
    //     0x8d4b84: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d4b88: LeaveFrame
    //     0x8d4b88: mov             SP, fp
    //     0x8d4b8c: ldp             fp, lr, [SP], #0x10
    // 0x8d4b90: ret
    //     0x8d4b90: ret             
    // 0x8d4b94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4b94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4b98: b               #0x8d4ae0
  }
  [closure] static ECCurve_brainpoolp256t1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d4b9c, size: 0x20
    // 0x8d4b9c: EnterFrame
    //     0x8d4b9c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4ba0: mov             fp, SP
    // 0x8d4ba4: r0 = ECCurve_brainpoolp256t1()
    //     0x8d4ba4: bl              #0x8d4bbc  ; AllocateECCurve_brainpoolp256t1Stub -> ECCurve_brainpoolp256t1 (size=0xc)
    // 0x8d4ba8: ldr             x1, [fp, #0x18]
    // 0x8d4bac: StoreField: r0->field_7 = r1
    //     0x8d4bac: stur            w1, [x0, #7]
    // 0x8d4bb0: LeaveFrame
    //     0x8d4bb0: mov             SP, fp
    //     0x8d4bb4: ldp             fp, lr, [SP], #0x10
    // 0x8d4bb8: ret
    //     0x8d4bb8: ret             
  }
}
