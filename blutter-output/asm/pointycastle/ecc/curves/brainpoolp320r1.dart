// lib: impl.ec_domain_parameters.brainpoolp320r1, url: package:pointycastle/ecc/curves/brainpoolp320r1.dart

// class id: 1050971, size: 0x8
class :: {
}

// class id: 631, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp320r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xed4

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d48b8, size: 0x58
    // 0x8d48b8: EnterFrame
    //     0x8d48b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d48bc: mov             fp, SP
    // 0x8d48c0: AllocStack(0x8)
    //     0x8d48c0: sub             SP, SP, #8
    // 0x8d48c4: r0 = StaticFactoryConfig()
    //     0x8d48c4: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d48c8: mov             x3, x0
    // 0x8d48cc: r0 = "brainpoolp320r1"
    //     0x8d48cc: add             x0, PP, #0x19, lsl #12  ; [pp+0x190e0] "brainpoolp320r1"
    //     0x8d48d0: ldr             x0, [x0, #0xe0]
    // 0x8d48d4: stur            x3, [fp, #-8]
    // 0x8d48d8: StoreField: r3->field_b = r0
    //     0x8d48d8: stur            w0, [x3, #0xb]
    // 0x8d48dc: r1 = Function '<anonymous closure>': static.
    //     0x8d48dc: add             x1, PP, #0x19, lsl #12  ; [pp+0x190e8] AnonymousClosure: static (0x8d4910), in [package:pointycastle/ecc/curves/brainpoolp320r1.dart] ECCurve_brainpoolp320r1::factoryConfig (0x8d48b8)
    //     0x8d48e0: ldr             x1, [x1, #0xe8]
    // 0x8d48e4: r2 = Null
    //     0x8d48e4: mov             x2, NULL
    // 0x8d48e8: r0 = AllocateClosure()
    //     0x8d48e8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d48ec: mov             x1, x0
    // 0x8d48f0: ldur            x0, [fp, #-8]
    // 0x8d48f4: StoreField: r0->field_f = r1
    //     0x8d48f4: stur            w1, [x0, #0xf]
    // 0x8d48f8: r1 = ECDomainParameters
    //     0x8d48f8: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d48fc: ldr             x1, [x1, #0x6e8]
    // 0x8d4900: StoreField: r0->field_7 = r1
    //     0x8d4900: stur            w1, [x0, #7]
    // 0x8d4904: LeaveFrame
    //     0x8d4904: mov             SP, fp
    //     0x8d4908: ldp             fp, lr, [SP], #0x10
    // 0x8d490c: ret
    //     0x8d490c: ret             
  }
  [closure] static ECCurve_brainpoolp320r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d4910, size: 0x30
    // 0x8d4910: EnterFrame
    //     0x8d4910: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4914: mov             fp, SP
    // 0x8d4918: CheckStackOverflow
    //     0x8d4918: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d491c: cmp             SP, x16
    //     0x8d4920: b.ls            #0x8d4938
    // 0x8d4924: r1 = Null
    //     0x8d4924: mov             x1, NULL
    // 0x8d4928: r0 = ECCurve_brainpoolp320r1()
    //     0x8d4928: bl              #0x8d4940  ; [package:pointycastle/ecc/curves/brainpoolp320r1.dart] ECCurve_brainpoolp320r1::ECCurve_brainpoolp320r1
    // 0x8d492c: LeaveFrame
    //     0x8d492c: mov             SP, fp
    //     0x8d4930: ldp             fp, lr, [SP], #0x10
    // 0x8d4934: ret
    //     0x8d4934: ret             
    // 0x8d4938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d493c: b               #0x8d4924
  }
  factory ECCurve_brainpoolp320r1 ECCurve_brainpoolp320r1(dynamic) {
    // ** addr: 0x8d4940, size: 0xd4
    // 0x8d4940: EnterFrame
    //     0x8d4940: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4944: mov             fp, SP
    // 0x8d4948: AllocStack(0x40)
    //     0x8d4948: sub             SP, SP, #0x40
    // 0x8d494c: CheckStackOverflow
    //     0x8d494c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4950: cmp             SP, x16
    //     0x8d4954: b.ls            #0x8d4a0c
    // 0x8d4958: r1 = "d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27"
    //     0x8d4958: add             x1, PP, #0x19, lsl #12  ; [pp+0x190b0] "d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27"
    //     0x8d495c: ldr             x1, [x1, #0xb0]
    // 0x8d4960: r2 = 32
    //     0x8d4960: movz            x2, #0x20
    // 0x8d4964: r0 = parse()
    //     0x8d4964: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4968: r1 = "3ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb4"
    //     0x8d4968: add             x1, PP, #0x19, lsl #12  ; [pp+0x190f0] "3ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb4"
    //     0x8d496c: ldr             x1, [x1, #0xf0]
    // 0x8d4970: r2 = 32
    //     0x8d4970: movz            x2, #0x20
    // 0x8d4974: stur            x0, [fp, #-8]
    // 0x8d4978: r0 = parse()
    //     0x8d4978: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d497c: r1 = "520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a6"
    //     0x8d497c: add             x1, PP, #0x19, lsl #12  ; [pp+0x190f8] "520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a6"
    //     0x8d4980: ldr             x1, [x1, #0xf8]
    // 0x8d4984: r2 = 32
    //     0x8d4984: movz            x2, #0x20
    // 0x8d4988: stur            x0, [fp, #-0x10]
    // 0x8d498c: r0 = parse()
    //     0x8d498c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4990: r1 = "0443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1"
    //     0x8d4990: add             x1, PP, #0x19, lsl #12  ; [pp+0x19100] "0443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1"
    //     0x8d4994: ldr             x1, [x1, #0x100]
    // 0x8d4998: r2 = 32
    //     0x8d4998: movz            x2, #0x20
    // 0x8d499c: stur            x0, [fp, #-0x18]
    // 0x8d49a0: r0 = parse()
    //     0x8d49a0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d49a4: r1 = "d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311"
    //     0x8d49a4: add             x1, PP, #0x19, lsl #12  ; [pp+0x190d0] "d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311"
    //     0x8d49a8: ldr             x1, [x1, #0xd0]
    // 0x8d49ac: r2 = 32
    //     0x8d49ac: movz            x2, #0x20
    // 0x8d49b0: stur            x0, [fp, #-0x20]
    // 0x8d49b4: r0 = parse()
    //     0x8d49b4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d49b8: r1 = "1"
    //     0x8d49b8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d49bc: ldr             x1, [x1, #0x718]
    // 0x8d49c0: r2 = 32
    //     0x8d49c0: movz            x2, #0x20
    // 0x8d49c4: stur            x0, [fp, #-0x28]
    // 0x8d49c8: r0 = parse()
    //     0x8d49c8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d49cc: ldur            x16, [fp, #-0x28]
    // 0x8d49d0: ldur            lr, [fp, #-8]
    // 0x8d49d4: stp             lr, x16, [SP, #8]
    // 0x8d49d8: str             NULL, [SP]
    // 0x8d49dc: ldur            x3, [fp, #-0x10]
    // 0x8d49e0: ldur            x5, [fp, #-0x18]
    // 0x8d49e4: ldur            x6, [fp, #-0x20]
    // 0x8d49e8: mov             x7, x0
    // 0x8d49ec: r1 = "brainpoolp320r1"
    //     0x8d49ec: add             x1, PP, #0x19, lsl #12  ; [pp+0x190e0] "brainpoolp320r1"
    //     0x8d49f0: ldr             x1, [x1, #0xe0]
    // 0x8d49f4: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp320r1 from Function '_make@977072419': static.
    //     0x8d49f4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19108] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp320r1 from Function '_make@977072419': static. (0x7e54fb2d4a14)
    //     0x8d49f8: ldr             x2, [x2, #0x108]
    // 0x8d49fc: r0 = constructFpStandardCurve()
    //     0x8d49fc: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d4a00: LeaveFrame
    //     0x8d4a00: mov             SP, fp
    //     0x8d4a04: ldp             fp, lr, [SP], #0x10
    // 0x8d4a08: ret
    //     0x8d4a08: ret             
    // 0x8d4a0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4a0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4a10: b               #0x8d4958
  }
  [closure] static ECCurve_brainpoolp320r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d4a14, size: 0x20
    // 0x8d4a14: EnterFrame
    //     0x8d4a14: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4a18: mov             fp, SP
    // 0x8d4a1c: r0 = ECCurve_brainpoolp320r1()
    //     0x8d4a1c: bl              #0x8d4a34  ; AllocateECCurve_brainpoolp320r1Stub -> ECCurve_brainpoolp320r1 (size=0xc)
    // 0x8d4a20: ldr             x1, [fp, #0x18]
    // 0x8d4a24: StoreField: r0->field_7 = r1
    //     0x8d4a24: stur            w1, [x0, #7]
    // 0x8d4a28: LeaveFrame
    //     0x8d4a28: mov             SP, fp
    //     0x8d4a2c: ldp             fp, lr, [SP], #0x10
    // 0x8d4a30: ret
    //     0x8d4a30: ret             
  }
}
