// lib: impl.ec_domain_parameters.brainpoolp320t1, url: package:pointycastle/ecc/curves/brainpoolp320t1.dart

// class id: 1050972, size: 0x8
class :: {
}

// class id: 630, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp320t1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xed8

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d4730, size: 0x58
    // 0x8d4730: EnterFrame
    //     0x8d4730: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4734: mov             fp, SP
    // 0x8d4738: AllocStack(0x8)
    //     0x8d4738: sub             SP, SP, #8
    // 0x8d473c: r0 = StaticFactoryConfig()
    //     0x8d473c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d4740: mov             x3, x0
    // 0x8d4744: r0 = "brainpoolp320t1"
    //     0x8d4744: add             x0, PP, #0x19, lsl #12  ; [pp+0x190a0] "brainpoolp320t1"
    //     0x8d4748: ldr             x0, [x0, #0xa0]
    // 0x8d474c: stur            x3, [fp, #-8]
    // 0x8d4750: StoreField: r3->field_b = r0
    //     0x8d4750: stur            w0, [x3, #0xb]
    // 0x8d4754: r1 = Function '<anonymous closure>': static.
    //     0x8d4754: add             x1, PP, #0x19, lsl #12  ; [pp+0x190a8] AnonymousClosure: static (0x8d4788), in [package:pointycastle/ecc/curves/brainpoolp320t1.dart] ECCurve_brainpoolp320t1::factoryConfig (0x8d4730)
    //     0x8d4758: ldr             x1, [x1, #0xa8]
    // 0x8d475c: r2 = Null
    //     0x8d475c: mov             x2, NULL
    // 0x8d4760: r0 = AllocateClosure()
    //     0x8d4760: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d4764: mov             x1, x0
    // 0x8d4768: ldur            x0, [fp, #-8]
    // 0x8d476c: StoreField: r0->field_f = r1
    //     0x8d476c: stur            w1, [x0, #0xf]
    // 0x8d4770: r1 = ECDomainParameters
    //     0x8d4770: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d4774: ldr             x1, [x1, #0x6e8]
    // 0x8d4778: StoreField: r0->field_7 = r1
    //     0x8d4778: stur            w1, [x0, #7]
    // 0x8d477c: LeaveFrame
    //     0x8d477c: mov             SP, fp
    //     0x8d4780: ldp             fp, lr, [SP], #0x10
    // 0x8d4784: ret
    //     0x8d4784: ret             
  }
  [closure] static ECCurve_brainpoolp320t1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d4788, size: 0x30
    // 0x8d4788: EnterFrame
    //     0x8d4788: stp             fp, lr, [SP, #-0x10]!
    //     0x8d478c: mov             fp, SP
    // 0x8d4790: CheckStackOverflow
    //     0x8d4790: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4794: cmp             SP, x16
    //     0x8d4798: b.ls            #0x8d47b0
    // 0x8d479c: r1 = Null
    //     0x8d479c: mov             x1, NULL
    // 0x8d47a0: r0 = ECCurve_brainpoolp320t1()
    //     0x8d47a0: bl              #0x8d47b8  ; [package:pointycastle/ecc/curves/brainpoolp320t1.dart] ECCurve_brainpoolp320t1::ECCurve_brainpoolp320t1
    // 0x8d47a4: LeaveFrame
    //     0x8d47a4: mov             SP, fp
    //     0x8d47a8: ldp             fp, lr, [SP], #0x10
    // 0x8d47ac: ret
    //     0x8d47ac: ret             
    // 0x8d47b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d47b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d47b4: b               #0x8d479c
  }
  factory ECCurve_brainpoolp320t1 ECCurve_brainpoolp320t1(dynamic) {
    // ** addr: 0x8d47b8, size: 0xd4
    // 0x8d47b8: EnterFrame
    //     0x8d47b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d47bc: mov             fp, SP
    // 0x8d47c0: AllocStack(0x40)
    //     0x8d47c0: sub             SP, SP, #0x40
    // 0x8d47c4: CheckStackOverflow
    //     0x8d47c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d47c8: cmp             SP, x16
    //     0x8d47cc: b.ls            #0x8d4884
    // 0x8d47d0: r1 = "d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27"
    //     0x8d47d0: add             x1, PP, #0x19, lsl #12  ; [pp+0x190b0] "d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27"
    //     0x8d47d4: ldr             x1, [x1, #0xb0]
    // 0x8d47d8: r2 = 32
    //     0x8d47d8: movz            x2, #0x20
    // 0x8d47dc: r0 = parse()
    //     0x8d47dc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d47e0: r1 = "d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e24"
    //     0x8d47e0: add             x1, PP, #0x19, lsl #12  ; [pp+0x190b8] "d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e24"
    //     0x8d47e4: ldr             x1, [x1, #0xb8]
    // 0x8d47e8: r2 = 32
    //     0x8d47e8: movz            x2, #0x20
    // 0x8d47ec: stur            x0, [fp, #-8]
    // 0x8d47f0: r0 = parse()
    //     0x8d47f0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d47f4: r1 = "a7f561e038eb1ed560b3d147db782013064c19f27ed27c6780aaf77fb8a547ceb5b4fef422340353"
    //     0x8d47f4: add             x1, PP, #0x19, lsl #12  ; [pp+0x190c0] "a7f561e038eb1ed560b3d147db782013064c19f27ed27c6780aaf77fb8a547ceb5b4fef422340353"
    //     0x8d47f8: ldr             x1, [x1, #0xc0]
    // 0x8d47fc: r2 = 32
    //     0x8d47fc: movz            x2, #0x20
    // 0x8d4800: stur            x0, [fp, #-0x10]
    // 0x8d4804: r0 = parse()
    //     0x8d4804: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4808: r1 = "04925be9fb01afc6fb4d3e7d4990010f813408ab106c4f09cb7ee07868cc136fff3357f624a21bed5263ba3a7a27483ebf6671dbef7abb30ebee084e58a0b077ad42a5a0989d1ee71b1b9bc0455fb0d2c3"
    //     0x8d4808: add             x1, PP, #0x19, lsl #12  ; [pp+0x190c8] "04925be9fb01afc6fb4d3e7d4990010f813408ab106c4f09cb7ee07868cc136fff3357f624a21bed5263ba3a7a27483ebf6671dbef7abb30ebee084e58a0b077ad42a5a0989d1ee71b1b9bc0455fb0d2c3"
    //     0x8d480c: ldr             x1, [x1, #0xc8]
    // 0x8d4810: r2 = 32
    //     0x8d4810: movz            x2, #0x20
    // 0x8d4814: stur            x0, [fp, #-0x18]
    // 0x8d4818: r0 = parse()
    //     0x8d4818: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d481c: r1 = "d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311"
    //     0x8d481c: add             x1, PP, #0x19, lsl #12  ; [pp+0x190d0] "d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311"
    //     0x8d4820: ldr             x1, [x1, #0xd0]
    // 0x8d4824: r2 = 32
    //     0x8d4824: movz            x2, #0x20
    // 0x8d4828: stur            x0, [fp, #-0x20]
    // 0x8d482c: r0 = parse()
    //     0x8d482c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4830: r1 = "1"
    //     0x8d4830: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d4834: ldr             x1, [x1, #0x718]
    // 0x8d4838: r2 = 32
    //     0x8d4838: movz            x2, #0x20
    // 0x8d483c: stur            x0, [fp, #-0x28]
    // 0x8d4840: r0 = parse()
    //     0x8d4840: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4844: ldur            x16, [fp, #-0x28]
    // 0x8d4848: ldur            lr, [fp, #-8]
    // 0x8d484c: stp             lr, x16, [SP, #8]
    // 0x8d4850: str             NULL, [SP]
    // 0x8d4854: ldur            x3, [fp, #-0x10]
    // 0x8d4858: ldur            x5, [fp, #-0x18]
    // 0x8d485c: ldur            x6, [fp, #-0x20]
    // 0x8d4860: mov             x7, x0
    // 0x8d4864: r1 = "brainpoolp320t1"
    //     0x8d4864: add             x1, PP, #0x19, lsl #12  ; [pp+0x190a0] "brainpoolp320t1"
    //     0x8d4868: ldr             x1, [x1, #0xa0]
    // 0x8d486c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp320t1 from Function '_make@978083086': static.
    //     0x8d486c: add             x2, PP, #0x19, lsl #12  ; [pp+0x190d8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp320t1 from Function '_make@978083086': static. (0x7e54fb2d488c)
    //     0x8d4870: ldr             x2, [x2, #0xd8]
    // 0x8d4874: r0 = constructFpStandardCurve()
    //     0x8d4874: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d4878: LeaveFrame
    //     0x8d4878: mov             SP, fp
    //     0x8d487c: ldp             fp, lr, [SP], #0x10
    // 0x8d4880: ret
    //     0x8d4880: ret             
    // 0x8d4884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4884: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4888: b               #0x8d47d0
  }
  [closure] static ECCurve_brainpoolp320t1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d488c, size: 0x20
    // 0x8d488c: EnterFrame
    //     0x8d488c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4890: mov             fp, SP
    // 0x8d4894: r0 = ECCurve_brainpoolp320t1()
    //     0x8d4894: bl              #0x8d48ac  ; AllocateECCurve_brainpoolp320t1Stub -> ECCurve_brainpoolp320t1 (size=0xc)
    // 0x8d4898: ldr             x1, [fp, #0x18]
    // 0x8d489c: StoreField: r0->field_7 = r1
    //     0x8d489c: stur            w1, [x0, #7]
    // 0x8d48a0: LeaveFrame
    //     0x8d48a0: mov             SP, fp
    //     0x8d48a4: ldp             fp, lr, [SP], #0x10
    // 0x8d48a8: ret
    //     0x8d48a8: ret             
  }
}
