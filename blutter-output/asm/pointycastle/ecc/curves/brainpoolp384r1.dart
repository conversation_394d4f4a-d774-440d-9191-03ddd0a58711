// lib: impl.ec_domain_parameters.brainpoolp384r1, url: package:pointycastle/ecc/curves/brainpoolp384r1.dart

// class id: 1050973, size: 0x8
class :: {
}

// class id: 629, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp384r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xedc

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d45a8, size: 0x58
    // 0x8d45a8: EnterFrame
    //     0x8d45a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d45ac: mov             fp, SP
    // 0x8d45b0: AllocStack(0x8)
    //     0x8d45b0: sub             SP, SP, #8
    // 0x8d45b4: r0 = StaticFactoryConfig()
    //     0x8d45b4: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d45b8: mov             x3, x0
    // 0x8d45bc: r0 = "brainpoolp384r1"
    //     0x8d45bc: add             x0, PP, #0x19, lsl #12  ; [pp+0x19070] "brainpoolp384r1"
    //     0x8d45c0: ldr             x0, [x0, #0x70]
    // 0x8d45c4: stur            x3, [fp, #-8]
    // 0x8d45c8: StoreField: r3->field_b = r0
    //     0x8d45c8: stur            w0, [x3, #0xb]
    // 0x8d45cc: r1 = Function '<anonymous closure>': static.
    //     0x8d45cc: add             x1, PP, #0x19, lsl #12  ; [pp+0x19078] AnonymousClosure: static (0x8d4600), in [package:pointycastle/ecc/curves/brainpoolp384r1.dart] ECCurve_brainpoolp384r1::factoryConfig (0x8d45a8)
    //     0x8d45d0: ldr             x1, [x1, #0x78]
    // 0x8d45d4: r2 = Null
    //     0x8d45d4: mov             x2, NULL
    // 0x8d45d8: r0 = AllocateClosure()
    //     0x8d45d8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d45dc: mov             x1, x0
    // 0x8d45e0: ldur            x0, [fp, #-8]
    // 0x8d45e4: StoreField: r0->field_f = r1
    //     0x8d45e4: stur            w1, [x0, #0xf]
    // 0x8d45e8: r1 = ECDomainParameters
    //     0x8d45e8: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d45ec: ldr             x1, [x1, #0x6e8]
    // 0x8d45f0: StoreField: r0->field_7 = r1
    //     0x8d45f0: stur            w1, [x0, #7]
    // 0x8d45f4: LeaveFrame
    //     0x8d45f4: mov             SP, fp
    //     0x8d45f8: ldp             fp, lr, [SP], #0x10
    // 0x8d45fc: ret
    //     0x8d45fc: ret             
  }
  [closure] static ECCurve_brainpoolp384r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d4600, size: 0x30
    // 0x8d4600: EnterFrame
    //     0x8d4600: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4604: mov             fp, SP
    // 0x8d4608: CheckStackOverflow
    //     0x8d4608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d460c: cmp             SP, x16
    //     0x8d4610: b.ls            #0x8d4628
    // 0x8d4614: r1 = Null
    //     0x8d4614: mov             x1, NULL
    // 0x8d4618: r0 = ECCurve_brainpoolp384r1()
    //     0x8d4618: bl              #0x8d4630  ; [package:pointycastle/ecc/curves/brainpoolp384r1.dart] ECCurve_brainpoolp384r1::ECCurve_brainpoolp384r1
    // 0x8d461c: LeaveFrame
    //     0x8d461c: mov             SP, fp
    //     0x8d4620: ldp             fp, lr, [SP], #0x10
    // 0x8d4624: ret
    //     0x8d4624: ret             
    // 0x8d4628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4628: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d462c: b               #0x8d4614
  }
  factory ECCurve_brainpoolp384r1 ECCurve_brainpoolp384r1(dynamic) {
    // ** addr: 0x8d4630, size: 0xd4
    // 0x8d4630: EnterFrame
    //     0x8d4630: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4634: mov             fp, SP
    // 0x8d4638: AllocStack(0x40)
    //     0x8d4638: sub             SP, SP, #0x40
    // 0x8d463c: CheckStackOverflow
    //     0x8d463c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4640: cmp             SP, x16
    //     0x8d4644: b.ls            #0x8d46fc
    // 0x8d4648: r1 = "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53"
    //     0x8d4648: add             x1, PP, #0x19, lsl #12  ; [pp+0x19040] "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53"
    //     0x8d464c: ldr             x1, [x1, #0x40]
    // 0x8d4650: r2 = 32
    //     0x8d4650: movz            x2, #0x20
    // 0x8d4654: r0 = parse()
    //     0x8d4654: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4658: r1 = "7bc382c63d8c150c3c72080ace05afa0c2bea28e4fb22787139165efba91f90f8aa5814a503ad4eb04a8c7dd22ce2826"
    //     0x8d4658: add             x1, PP, #0x19, lsl #12  ; [pp+0x19080] "7bc382c63d8c150c3c72080ace05afa0c2bea28e4fb22787139165efba91f90f8aa5814a503ad4eb04a8c7dd22ce2826"
    //     0x8d465c: ldr             x1, [x1, #0x80]
    // 0x8d4660: r2 = 32
    //     0x8d4660: movz            x2, #0x20
    // 0x8d4664: stur            x0, [fp, #-8]
    // 0x8d4668: r0 = parse()
    //     0x8d4668: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d466c: r1 = "4a8c7dd22ce28268b39b55416f0447c2fb77de107dcd2a62e880ea53eeb62d57cb4390295dbc9943ab78696fa504c11"
    //     0x8d466c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19088] "4a8c7dd22ce28268b39b55416f0447c2fb77de107dcd2a62e880ea53eeb62d57cb4390295dbc9943ab78696fa504c11"
    //     0x8d4670: ldr             x1, [x1, #0x88]
    // 0x8d4674: r2 = 32
    //     0x8d4674: movz            x2, #0x20
    // 0x8d4678: stur            x0, [fp, #-0x10]
    // 0x8d467c: r0 = parse()
    //     0x8d467c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4680: r1 = "041d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e8abe1d7520f9c2a45cb1eb8e95cfd55262b70b29feec5864e19c054ff99129280e4646217791811142820341263c5315"
    //     0x8d4680: add             x1, PP, #0x19, lsl #12  ; [pp+0x19090] "041d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e8abe1d7520f9c2a45cb1eb8e95cfd55262b70b29feec5864e19c054ff99129280e4646217791811142820341263c5315"
    //     0x8d4684: ldr             x1, [x1, #0x90]
    // 0x8d4688: r2 = 32
    //     0x8d4688: movz            x2, #0x20
    // 0x8d468c: stur            x0, [fp, #-0x18]
    // 0x8d4690: r0 = parse()
    //     0x8d4690: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4694: r1 = "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565"
    //     0x8d4694: add             x1, PP, #0x19, lsl #12  ; [pp+0x19060] "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565"
    //     0x8d4698: ldr             x1, [x1, #0x60]
    // 0x8d469c: r2 = 32
    //     0x8d469c: movz            x2, #0x20
    // 0x8d46a0: stur            x0, [fp, #-0x20]
    // 0x8d46a4: r0 = parse()
    //     0x8d46a4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d46a8: r1 = "1"
    //     0x8d46a8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d46ac: ldr             x1, [x1, #0x718]
    // 0x8d46b0: r2 = 32
    //     0x8d46b0: movz            x2, #0x20
    // 0x8d46b4: stur            x0, [fp, #-0x28]
    // 0x8d46b8: r0 = parse()
    //     0x8d46b8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d46bc: ldur            x16, [fp, #-0x28]
    // 0x8d46c0: ldur            lr, [fp, #-8]
    // 0x8d46c4: stp             lr, x16, [SP, #8]
    // 0x8d46c8: str             NULL, [SP]
    // 0x8d46cc: ldur            x3, [fp, #-0x10]
    // 0x8d46d0: ldur            x5, [fp, #-0x18]
    // 0x8d46d4: ldur            x6, [fp, #-0x20]
    // 0x8d46d8: mov             x7, x0
    // 0x8d46dc: r1 = "brainpoolp384r1"
    //     0x8d46dc: add             x1, PP, #0x19, lsl #12  ; [pp+0x19070] "brainpoolp384r1"
    //     0x8d46e0: ldr             x1, [x1, #0x70]
    // 0x8d46e4: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp384r1 from Function '_make@979278996': static.
    //     0x8d46e4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19098] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp384r1 from Function '_make@979278996': static. (0x7e54fb2d4704)
    //     0x8d46e8: ldr             x2, [x2, #0x98]
    // 0x8d46ec: r0 = constructFpStandardCurve()
    //     0x8d46ec: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d46f0: LeaveFrame
    //     0x8d46f0: mov             SP, fp
    //     0x8d46f4: ldp             fp, lr, [SP], #0x10
    // 0x8d46f8: ret
    //     0x8d46f8: ret             
    // 0x8d46fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d46fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4700: b               #0x8d4648
  }
  [closure] static ECCurve_brainpoolp384r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d4704, size: 0x20
    // 0x8d4704: EnterFrame
    //     0x8d4704: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4708: mov             fp, SP
    // 0x8d470c: r0 = ECCurve_brainpoolp384r1()
    //     0x8d470c: bl              #0x8d4724  ; AllocateECCurve_brainpoolp384r1Stub -> ECCurve_brainpoolp384r1 (size=0xc)
    // 0x8d4710: ldr             x1, [fp, #0x18]
    // 0x8d4714: StoreField: r0->field_7 = r1
    //     0x8d4714: stur            w1, [x0, #7]
    // 0x8d4718: LeaveFrame
    //     0x8d4718: mov             SP, fp
    //     0x8d471c: ldp             fp, lr, [SP], #0x10
    // 0x8d4720: ret
    //     0x8d4720: ret             
  }
}
