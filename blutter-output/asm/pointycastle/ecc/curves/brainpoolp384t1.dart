// lib: impl.ec_domain_parameters.brainpoolp384t1, url: package:pointycastle/ecc/curves/brainpoolp384t1.dart

// class id: 1050974, size: 0x8
class :: {
}

// class id: 628, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp384t1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xee0

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d4420, size: 0x58
    // 0x8d4420: EnterFrame
    //     0x8d4420: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4424: mov             fp, SP
    // 0x8d4428: AllocStack(0x8)
    //     0x8d4428: sub             SP, SP, #8
    // 0x8d442c: r0 = StaticFactoryConfig()
    //     0x8d442c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d4430: mov             x3, x0
    // 0x8d4434: r0 = "brainpoolp384t1"
    //     0x8d4434: add             x0, PP, #0x19, lsl #12  ; [pp+0x19030] "brainpoolp384t1"
    //     0x8d4438: ldr             x0, [x0, #0x30]
    // 0x8d443c: stur            x3, [fp, #-8]
    // 0x8d4440: StoreField: r3->field_b = r0
    //     0x8d4440: stur            w0, [x3, #0xb]
    // 0x8d4444: r1 = Function '<anonymous closure>': static.
    //     0x8d4444: add             x1, PP, #0x19, lsl #12  ; [pp+0x19038] AnonymousClosure: static (0x8d4478), in [package:pointycastle/ecc/curves/brainpoolp384t1.dart] ECCurve_brainpoolp384t1::factoryConfig (0x8d4420)
    //     0x8d4448: ldr             x1, [x1, #0x38]
    // 0x8d444c: r2 = Null
    //     0x8d444c: mov             x2, NULL
    // 0x8d4450: r0 = AllocateClosure()
    //     0x8d4450: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d4454: mov             x1, x0
    // 0x8d4458: ldur            x0, [fp, #-8]
    // 0x8d445c: StoreField: r0->field_f = r1
    //     0x8d445c: stur            w1, [x0, #0xf]
    // 0x8d4460: r1 = ECDomainParameters
    //     0x8d4460: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d4464: ldr             x1, [x1, #0x6e8]
    // 0x8d4468: StoreField: r0->field_7 = r1
    //     0x8d4468: stur            w1, [x0, #7]
    // 0x8d446c: LeaveFrame
    //     0x8d446c: mov             SP, fp
    //     0x8d4470: ldp             fp, lr, [SP], #0x10
    // 0x8d4474: ret
    //     0x8d4474: ret             
  }
  [closure] static ECCurve_brainpoolp384t1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d4478, size: 0x30
    // 0x8d4478: EnterFrame
    //     0x8d4478: stp             fp, lr, [SP, #-0x10]!
    //     0x8d447c: mov             fp, SP
    // 0x8d4480: CheckStackOverflow
    //     0x8d4480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4484: cmp             SP, x16
    //     0x8d4488: b.ls            #0x8d44a0
    // 0x8d448c: r1 = Null
    //     0x8d448c: mov             x1, NULL
    // 0x8d4490: r0 = ECCurve_brainpoolp384t1()
    //     0x8d4490: bl              #0x8d44a8  ; [package:pointycastle/ecc/curves/brainpoolp384t1.dart] ECCurve_brainpoolp384t1::ECCurve_brainpoolp384t1
    // 0x8d4494: LeaveFrame
    //     0x8d4494: mov             SP, fp
    //     0x8d4498: ldp             fp, lr, [SP], #0x10
    // 0x8d449c: ret
    //     0x8d449c: ret             
    // 0x8d44a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d44a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d44a4: b               #0x8d448c
  }
  factory ECCurve_brainpoolp384t1 ECCurve_brainpoolp384t1(dynamic) {
    // ** addr: 0x8d44a8, size: 0xd4
    // 0x8d44a8: EnterFrame
    //     0x8d44a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d44ac: mov             fp, SP
    // 0x8d44b0: AllocStack(0x40)
    //     0x8d44b0: sub             SP, SP, #0x40
    // 0x8d44b4: CheckStackOverflow
    //     0x8d44b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d44b8: cmp             SP, x16
    //     0x8d44bc: b.ls            #0x8d4574
    // 0x8d44c0: r1 = "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53"
    //     0x8d44c0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19040] "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53"
    //     0x8d44c4: ldr             x1, [x1, #0x40]
    // 0x8d44c8: r2 = 32
    //     0x8d44c8: movz            x2, #0x20
    // 0x8d44cc: r0 = parse()
    //     0x8d44cc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d44d0: r1 = "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec50"
    //     0x8d44d0: add             x1, PP, #0x19, lsl #12  ; [pp+0x19048] "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec50"
    //     0x8d44d4: ldr             x1, [x1, #0x48]
    // 0x8d44d8: r2 = 32
    //     0x8d44d8: movz            x2, #0x20
    // 0x8d44dc: stur            x0, [fp, #-8]
    // 0x8d44e0: r0 = parse()
    //     0x8d44e0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d44e4: r1 = "7f519eada7bda81bd826dba647910f8c4b9346ed8ccdc64e4b1abd11756dce1d2074aa263b88805ced70355a33b471ee"
    //     0x8d44e4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19050] "7f519eada7bda81bd826dba647910f8c4b9346ed8ccdc64e4b1abd11756dce1d2074aa263b88805ced70355a33b471ee"
    //     0x8d44e8: ldr             x1, [x1, #0x50]
    // 0x8d44ec: r2 = 32
    //     0x8d44ec: movz            x2, #0x20
    // 0x8d44f0: stur            x0, [fp, #-0x10]
    // 0x8d44f4: r0 = parse()
    //     0x8d44f4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d44f8: r1 = "0418de98b02db9a306f2afcd7235f72a819b80ab12ebd653172476fecd462aabffc4ff191b946a5f54d8d0aa2f418808cc25ab056962d30651a114afd2755ad336747f93475b7a1fca3b88f2b6a208ccfe469408584dc2b2912675bf5b9e582928"
    //     0x8d44f8: add             x1, PP, #0x19, lsl #12  ; [pp+0x19058] "0418de98b02db9a306f2afcd7235f72a819b80ab12ebd653172476fecd462aabffc4ff191b946a5f54d8d0aa2f418808cc25ab056962d30651a114afd2755ad336747f93475b7a1fca3b88f2b6a208ccfe469408584dc2b2912675bf5b9e582928"
    //     0x8d44fc: ldr             x1, [x1, #0x58]
    // 0x8d4500: r2 = 32
    //     0x8d4500: movz            x2, #0x20
    // 0x8d4504: stur            x0, [fp, #-0x18]
    // 0x8d4508: r0 = parse()
    //     0x8d4508: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d450c: r1 = "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565"
    //     0x8d450c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19060] "8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565"
    //     0x8d4510: ldr             x1, [x1, #0x60]
    // 0x8d4514: r2 = 32
    //     0x8d4514: movz            x2, #0x20
    // 0x8d4518: stur            x0, [fp, #-0x20]
    // 0x8d451c: r0 = parse()
    //     0x8d451c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4520: r1 = "1"
    //     0x8d4520: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d4524: ldr             x1, [x1, #0x718]
    // 0x8d4528: r2 = 32
    //     0x8d4528: movz            x2, #0x20
    // 0x8d452c: stur            x0, [fp, #-0x28]
    // 0x8d4530: r0 = parse()
    //     0x8d4530: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4534: ldur            x16, [fp, #-0x28]
    // 0x8d4538: ldur            lr, [fp, #-8]
    // 0x8d453c: stp             lr, x16, [SP, #8]
    // 0x8d4540: str             NULL, [SP]
    // 0x8d4544: ldur            x3, [fp, #-0x10]
    // 0x8d4548: ldur            x5, [fp, #-0x18]
    // 0x8d454c: ldur            x6, [fp, #-0x20]
    // 0x8d4550: mov             x7, x0
    // 0x8d4554: r1 = "brainpoolp384t1"
    //     0x8d4554: add             x1, PP, #0x19, lsl #12  ; [pp+0x19030] "brainpoolp384t1"
    //     0x8d4558: ldr             x1, [x1, #0x30]
    // 0x8d455c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp384t1 from Function '_make@980104730': static.
    //     0x8d455c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19068] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp384t1 from Function '_make@980104730': static. (0x7e54fb2d457c)
    //     0x8d4560: ldr             x2, [x2, #0x68]
    // 0x8d4564: r0 = constructFpStandardCurve()
    //     0x8d4564: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d4568: LeaveFrame
    //     0x8d4568: mov             SP, fp
    //     0x8d456c: ldp             fp, lr, [SP], #0x10
    // 0x8d4570: ret
    //     0x8d4570: ret             
    // 0x8d4574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4578: b               #0x8d44c0
  }
  [closure] static ECCurve_brainpoolp384t1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d457c, size: 0x20
    // 0x8d457c: EnterFrame
    //     0x8d457c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4580: mov             fp, SP
    // 0x8d4584: r0 = ECCurve_brainpoolp384t1()
    //     0x8d4584: bl              #0x8d459c  ; AllocateECCurve_brainpoolp384t1Stub -> ECCurve_brainpoolp384t1 (size=0xc)
    // 0x8d4588: ldr             x1, [fp, #0x18]
    // 0x8d458c: StoreField: r0->field_7 = r1
    //     0x8d458c: stur            w1, [x0, #7]
    // 0x8d4590: LeaveFrame
    //     0x8d4590: mov             SP, fp
    //     0x8d4594: ldp             fp, lr, [SP], #0x10
    // 0x8d4598: ret
    //     0x8d4598: ret             
  }
}
