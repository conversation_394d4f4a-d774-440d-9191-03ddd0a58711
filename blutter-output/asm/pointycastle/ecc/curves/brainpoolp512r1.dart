// lib: impl.ec_domain_parameters.brainpoolp512r1, url: package:pointycastle/ecc/curves/brainpoolp512r1.dart

// class id: 1050975, size: 0x8
class :: {
}

// class id: 627, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp512r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xee4

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d4298, size: 0x58
    // 0x8d4298: EnterFrame
    //     0x8d4298: stp             fp, lr, [SP, #-0x10]!
    //     0x8d429c: mov             fp, SP
    // 0x8d42a0: AllocStack(0x8)
    //     0x8d42a0: sub             SP, SP, #8
    // 0x8d42a4: r0 = StaticFactoryConfig()
    //     0x8d42a4: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d42a8: mov             x3, x0
    // 0x8d42ac: r0 = "brainpoolp512r1"
    //     0x8d42ac: add             x0, PP, #0x19, lsl #12  ; [pp+0x19000] "brainpoolp512r1"
    //     0x8d42b0: ldr             x0, [x0]
    // 0x8d42b4: stur            x3, [fp, #-8]
    // 0x8d42b8: StoreField: r3->field_b = r0
    //     0x8d42b8: stur            w0, [x3, #0xb]
    // 0x8d42bc: r1 = Function '<anonymous closure>': static.
    //     0x8d42bc: add             x1, PP, #0x19, lsl #12  ; [pp+0x19008] AnonymousClosure: static (0x8d42f0), in [package:pointycastle/ecc/curves/brainpoolp512r1.dart] ECCurve_brainpoolp512r1::factoryConfig (0x8d4298)
    //     0x8d42c0: ldr             x1, [x1, #8]
    // 0x8d42c4: r2 = Null
    //     0x8d42c4: mov             x2, NULL
    // 0x8d42c8: r0 = AllocateClosure()
    //     0x8d42c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d42cc: mov             x1, x0
    // 0x8d42d0: ldur            x0, [fp, #-8]
    // 0x8d42d4: StoreField: r0->field_f = r1
    //     0x8d42d4: stur            w1, [x0, #0xf]
    // 0x8d42d8: r1 = ECDomainParameters
    //     0x8d42d8: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d42dc: ldr             x1, [x1, #0x6e8]
    // 0x8d42e0: StoreField: r0->field_7 = r1
    //     0x8d42e0: stur            w1, [x0, #7]
    // 0x8d42e4: LeaveFrame
    //     0x8d42e4: mov             SP, fp
    //     0x8d42e8: ldp             fp, lr, [SP], #0x10
    // 0x8d42ec: ret
    //     0x8d42ec: ret             
  }
  [closure] static ECCurve_brainpoolp512r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d42f0, size: 0x30
    // 0x8d42f0: EnterFrame
    //     0x8d42f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d42f4: mov             fp, SP
    // 0x8d42f8: CheckStackOverflow
    //     0x8d42f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d42fc: cmp             SP, x16
    //     0x8d4300: b.ls            #0x8d4318
    // 0x8d4304: r1 = Null
    //     0x8d4304: mov             x1, NULL
    // 0x8d4308: r0 = ECCurve_brainpoolp512r1()
    //     0x8d4308: bl              #0x8d4320  ; [package:pointycastle/ecc/curves/brainpoolp512r1.dart] ECCurve_brainpoolp512r1::ECCurve_brainpoolp512r1
    // 0x8d430c: LeaveFrame
    //     0x8d430c: mov             SP, fp
    //     0x8d4310: ldp             fp, lr, [SP], #0x10
    // 0x8d4314: ret
    //     0x8d4314: ret             
    // 0x8d4318: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4318: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d431c: b               #0x8d4304
  }
  factory ECCurve_brainpoolp512r1 ECCurve_brainpoolp512r1(dynamic) {
    // ** addr: 0x8d4320, size: 0xd4
    // 0x8d4320: EnterFrame
    //     0x8d4320: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4324: mov             fp, SP
    // 0x8d4328: AllocStack(0x40)
    //     0x8d4328: sub             SP, SP, #0x40
    // 0x8d432c: CheckStackOverflow
    //     0x8d432c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4330: cmp             SP, x16
    //     0x8d4334: b.ls            #0x8d43ec
    // 0x8d4338: r1 = "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f3"
    //     0x8d4338: add             x1, PP, #0x18, lsl #12  ; [pp+0x18fd0] "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f3"
    //     0x8d433c: ldr             x1, [x1, #0xfd0]
    // 0x8d4340: r2 = 32
    //     0x8d4340: movz            x2, #0x20
    // 0x8d4344: r0 = parse()
    //     0x8d4344: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4348: r1 = "7830a3318b603b89e2327145ac234cc594cbdd8d3df91610a83441caea9863bc2ded5d5aa8253aa10a2ef1c98b9ac8b57f1117a72bf2c7b9e7c1ac4d77fc94ca"
    //     0x8d4348: add             x1, PP, #0x19, lsl #12  ; [pp+0x19010] "7830a3318b603b89e2327145ac234cc594cbdd8d3df91610a83441caea9863bc2ded5d5aa8253aa10a2ef1c98b9ac8b57f1117a72bf2c7b9e7c1ac4d77fc94ca"
    //     0x8d434c: ldr             x1, [x1, #0x10]
    // 0x8d4350: r2 = 32
    //     0x8d4350: movz            x2, #0x20
    // 0x8d4354: stur            x0, [fp, #-8]
    // 0x8d4358: r0 = parse()
    //     0x8d4358: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d435c: r1 = "3df91610a83441caea9863bc2ded5d5aa8253aa10a2ef1c98b9ac8b57f1117a72bf2c7b9e7c1ac4d77fc94cadc083e67984050b75ebae5dd2809bd638016f723"
    //     0x8d435c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19018] "3df91610a83441caea9863bc2ded5d5aa8253aa10a2ef1c98b9ac8b57f1117a72bf2c7b9e7c1ac4d77fc94cadc083e67984050b75ebae5dd2809bd638016f723"
    //     0x8d4360: ldr             x1, [x1, #0x18]
    // 0x8d4364: r2 = 32
    //     0x8d4364: movz            x2, #0x20
    // 0x8d4368: stur            x0, [fp, #-0x10]
    // 0x8d436c: r0 = parse()
    //     0x8d436c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4370: r1 = "0481aee4bdd82ed9645a21322e9c4c6a9385ed9f70b5d916c1b43b62eef4d0098eff3b1f78e2d0d48d50d1687b93b97d5f7c6d5047406a5e688b352209bcb9f8227dde385d566332ecc0eabfa9cf7822fdf209f70024a57b1aa000c55b881f8111b2dcde494a5f485e5bca4bd88a2763aed1ca2b2fa8f0540678cd1e0f3ad80892"
    //     0x8d4370: add             x1, PP, #0x19, lsl #12  ; [pp+0x19020] "0481aee4bdd82ed9645a21322e9c4c6a9385ed9f70b5d916c1b43b62eef4d0098eff3b1f78e2d0d48d50d1687b93b97d5f7c6d5047406a5e688b352209bcb9f8227dde385d566332ecc0eabfa9cf7822fdf209f70024a57b1aa000c55b881f8111b2dcde494a5f485e5bca4bd88a2763aed1ca2b2fa8f0540678cd1e0f3ad80892"
    //     0x8d4374: ldr             x1, [x1, #0x20]
    // 0x8d4378: r2 = 32
    //     0x8d4378: movz            x2, #0x20
    // 0x8d437c: stur            x0, [fp, #-0x18]
    // 0x8d4380: r0 = parse()
    //     0x8d4380: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4384: r1 = "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca70330870553e5c414ca92619418661197fac10471db1d381085ddaddb58796829ca90069"
    //     0x8d4384: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ff0] "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca70330870553e5c414ca92619418661197fac10471db1d381085ddaddb58796829ca90069"
    //     0x8d4388: ldr             x1, [x1, #0xff0]
    // 0x8d438c: r2 = 32
    //     0x8d438c: movz            x2, #0x20
    // 0x8d4390: stur            x0, [fp, #-0x20]
    // 0x8d4394: r0 = parse()
    //     0x8d4394: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4398: r1 = "1"
    //     0x8d4398: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d439c: ldr             x1, [x1, #0x718]
    // 0x8d43a0: r2 = 32
    //     0x8d43a0: movz            x2, #0x20
    // 0x8d43a4: stur            x0, [fp, #-0x28]
    // 0x8d43a8: r0 = parse()
    //     0x8d43a8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d43ac: ldur            x16, [fp, #-0x28]
    // 0x8d43b0: ldur            lr, [fp, #-8]
    // 0x8d43b4: stp             lr, x16, [SP, #8]
    // 0x8d43b8: str             NULL, [SP]
    // 0x8d43bc: ldur            x3, [fp, #-0x10]
    // 0x8d43c0: ldur            x5, [fp, #-0x18]
    // 0x8d43c4: ldur            x6, [fp, #-0x20]
    // 0x8d43c8: mov             x7, x0
    // 0x8d43cc: r1 = "brainpoolp512r1"
    //     0x8d43cc: add             x1, PP, #0x19, lsl #12  ; [pp+0x19000] "brainpoolp512r1"
    //     0x8d43d0: ldr             x1, [x1]
    // 0x8d43d4: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp512r1 from Function '_make@981215992': static.
    //     0x8d43d4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19028] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp512r1 from Function '_make@981215992': static. (0x7e54fb2d43f4)
    //     0x8d43d8: ldr             x2, [x2, #0x28]
    // 0x8d43dc: r0 = constructFpStandardCurve()
    //     0x8d43dc: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d43e0: LeaveFrame
    //     0x8d43e0: mov             SP, fp
    //     0x8d43e4: ldp             fp, lr, [SP], #0x10
    // 0x8d43e8: ret
    //     0x8d43e8: ret             
    // 0x8d43ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d43ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d43f0: b               #0x8d4338
  }
  [closure] static ECCurve_brainpoolp512r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d43f4, size: 0x20
    // 0x8d43f4: EnterFrame
    //     0x8d43f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d43f8: mov             fp, SP
    // 0x8d43fc: r0 = ECCurve_brainpoolp512r1()
    //     0x8d43fc: bl              #0x8d4414  ; AllocateECCurve_brainpoolp512r1Stub -> ECCurve_brainpoolp512r1 (size=0xc)
    // 0x8d4400: ldr             x1, [fp, #0x18]
    // 0x8d4404: StoreField: r0->field_7 = r1
    //     0x8d4404: stur            w1, [x0, #7]
    // 0x8d4408: LeaveFrame
    //     0x8d4408: mov             SP, fp
    //     0x8d440c: ldp             fp, lr, [SP], #0x10
    // 0x8d4410: ret
    //     0x8d4410: ret             
  }
}
