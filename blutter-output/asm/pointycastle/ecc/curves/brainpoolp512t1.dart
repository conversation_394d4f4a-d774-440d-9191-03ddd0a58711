// lib: impl.ec_domain_parameters.brainpoolp512t1, url: package:pointycastle/ecc/curves/brainpoolp512t1.dart

// class id: 1050976, size: 0x8
class :: {
}

// class id: 626, size: 0xc, field offset: 0xc
class ECCurve_brainpoolp512t1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xee8

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d4110, size: 0x58
    // 0x8d4110: EnterFrame
    //     0x8d4110: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4114: mov             fp, SP
    // 0x8d4118: AllocStack(0x8)
    //     0x8d4118: sub             SP, SP, #8
    // 0x8d411c: r0 = StaticFactoryConfig()
    //     0x8d411c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d4120: mov             x3, x0
    // 0x8d4124: r0 = "brainpoolp512t1"
    //     0x8d4124: add             x0, PP, #0x18, lsl #12  ; [pp+0x18fc0] "brainpoolp512t1"
    //     0x8d4128: ldr             x0, [x0, #0xfc0]
    // 0x8d412c: stur            x3, [fp, #-8]
    // 0x8d4130: StoreField: r3->field_b = r0
    //     0x8d4130: stur            w0, [x3, #0xb]
    // 0x8d4134: r1 = Function '<anonymous closure>': static.
    //     0x8d4134: add             x1, PP, #0x18, lsl #12  ; [pp+0x18fc8] AnonymousClosure: static (0x8d4168), in [package:pointycastle/ecc/curves/brainpoolp512t1.dart] ECCurve_brainpoolp512t1::factoryConfig (0x8d4110)
    //     0x8d4138: ldr             x1, [x1, #0xfc8]
    // 0x8d413c: r2 = Null
    //     0x8d413c: mov             x2, NULL
    // 0x8d4140: r0 = AllocateClosure()
    //     0x8d4140: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d4144: mov             x1, x0
    // 0x8d4148: ldur            x0, [fp, #-8]
    // 0x8d414c: StoreField: r0->field_f = r1
    //     0x8d414c: stur            w1, [x0, #0xf]
    // 0x8d4150: r1 = ECDomainParameters
    //     0x8d4150: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d4154: ldr             x1, [x1, #0x6e8]
    // 0x8d4158: StoreField: r0->field_7 = r1
    //     0x8d4158: stur            w1, [x0, #7]
    // 0x8d415c: LeaveFrame
    //     0x8d415c: mov             SP, fp
    //     0x8d4160: ldp             fp, lr, [SP], #0x10
    // 0x8d4164: ret
    //     0x8d4164: ret             
  }
  [closure] static ECCurve_brainpoolp512t1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d4168, size: 0x30
    // 0x8d4168: EnterFrame
    //     0x8d4168: stp             fp, lr, [SP, #-0x10]!
    //     0x8d416c: mov             fp, SP
    // 0x8d4170: CheckStackOverflow
    //     0x8d4170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4174: cmp             SP, x16
    //     0x8d4178: b.ls            #0x8d4190
    // 0x8d417c: r1 = Null
    //     0x8d417c: mov             x1, NULL
    // 0x8d4180: r0 = ECCurve_brainpoolp512t1()
    //     0x8d4180: bl              #0x8d4198  ; [package:pointycastle/ecc/curves/brainpoolp512t1.dart] ECCurve_brainpoolp512t1::ECCurve_brainpoolp512t1
    // 0x8d4184: LeaveFrame
    //     0x8d4184: mov             SP, fp
    //     0x8d4188: ldp             fp, lr, [SP], #0x10
    // 0x8d418c: ret
    //     0x8d418c: ret             
    // 0x8d4190: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4190: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4194: b               #0x8d417c
  }
  factory ECCurve_brainpoolp512t1 ECCurve_brainpoolp512t1(dynamic) {
    // ** addr: 0x8d4198, size: 0xd4
    // 0x8d4198: EnterFrame
    //     0x8d4198: stp             fp, lr, [SP, #-0x10]!
    //     0x8d419c: mov             fp, SP
    // 0x8d41a0: AllocStack(0x40)
    //     0x8d41a0: sub             SP, SP, #0x40
    // 0x8d41a4: CheckStackOverflow
    //     0x8d41a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d41a8: cmp             SP, x16
    //     0x8d41ac: b.ls            #0x8d4264
    // 0x8d41b0: r1 = "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f3"
    //     0x8d41b0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18fd0] "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f3"
    //     0x8d41b4: ldr             x1, [x1, #0xfd0]
    // 0x8d41b8: r2 = 32
    //     0x8d41b8: movz            x2, #0x20
    // 0x8d41bc: r0 = parse()
    //     0x8d41bc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d41c0: r1 = "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f0"
    //     0x8d41c0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18fd8] "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f0"
    //     0x8d41c4: ldr             x1, [x1, #0xfd8]
    // 0x8d41c8: r2 = 32
    //     0x8d41c8: movz            x2, #0x20
    // 0x8d41cc: stur            x0, [fp, #-8]
    // 0x8d41d0: r0 = parse()
    //     0x8d41d0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d41d4: r1 = "7cbbbcf9441cfab76e1890e46884eae321f70c0bcb4981527897504bec3e36a62bcdfa2304976540f6450085f2dae145c22553b465763689180ea2571867423e"
    //     0x8d41d4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18fe0] "7cbbbcf9441cfab76e1890e46884eae321f70c0bcb4981527897504bec3e36a62bcdfa2304976540f6450085f2dae145c22553b465763689180ea2571867423e"
    //     0x8d41d8: ldr             x1, [x1, #0xfe0]
    // 0x8d41dc: r2 = 32
    //     0x8d41dc: movz            x2, #0x20
    // 0x8d41e0: stur            x0, [fp, #-0x10]
    // 0x8d41e4: r0 = parse()
    //     0x8d41e4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d41e8: r1 = "04640ece5c12788717b9c1ba06cbc2a6feba85842458c56dde9db1758d39c0313d82ba51735cdb3ea499aa77a7d6943a64f7a3f25fe26f06b51baa2696fa9035da5b534bd595f5af0fa2c892376c84ace1bb4e3019b71634c01131159cae03cee9d9932184beef216bd71df2dadf86a627306ecff96dbb8bace198b61e00f8b332"
    //     0x8d41e8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18fe8] "04640ece5c12788717b9c1ba06cbc2a6feba85842458c56dde9db1758d39c0313d82ba51735cdb3ea499aa77a7d6943a64f7a3f25fe26f06b51baa2696fa9035da5b534bd595f5af0fa2c892376c84ace1bb4e3019b71634c01131159cae03cee9d9932184beef216bd71df2dadf86a627306ecff96dbb8bace198b61e00f8b332"
    //     0x8d41ec: ldr             x1, [x1, #0xfe8]
    // 0x8d41f0: r2 = 32
    //     0x8d41f0: movz            x2, #0x20
    // 0x8d41f4: stur            x0, [fp, #-0x18]
    // 0x8d41f8: r0 = parse()
    //     0x8d41f8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d41fc: r1 = "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca70330870553e5c414ca92619418661197fac10471db1d381085ddaddb58796829ca90069"
    //     0x8d41fc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ff0] "aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca70330870553e5c414ca92619418661197fac10471db1d381085ddaddb58796829ca90069"
    //     0x8d4200: ldr             x1, [x1, #0xff0]
    // 0x8d4204: r2 = 32
    //     0x8d4204: movz            x2, #0x20
    // 0x8d4208: stur            x0, [fp, #-0x20]
    // 0x8d420c: r0 = parse()
    //     0x8d420c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4210: r1 = "1"
    //     0x8d4210: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d4214: ldr             x1, [x1, #0x718]
    // 0x8d4218: r2 = 32
    //     0x8d4218: movz            x2, #0x20
    // 0x8d421c: stur            x0, [fp, #-0x28]
    // 0x8d4220: r0 = parse()
    //     0x8d4220: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4224: ldur            x16, [fp, #-0x28]
    // 0x8d4228: ldur            lr, [fp, #-8]
    // 0x8d422c: stp             lr, x16, [SP, #8]
    // 0x8d4230: str             NULL, [SP]
    // 0x8d4234: ldur            x3, [fp, #-0x10]
    // 0x8d4238: ldur            x5, [fp, #-0x18]
    // 0x8d423c: ldur            x6, [fp, #-0x20]
    // 0x8d4240: mov             x7, x0
    // 0x8d4244: r1 = "brainpoolp512t1"
    //     0x8d4244: add             x1, PP, #0x18, lsl #12  ; [pp+0x18fc0] "brainpoolp512t1"
    //     0x8d4248: ldr             x1, [x1, #0xfc0]
    // 0x8d424c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp512t1 from Function '_make@982113648': static.
    //     0x8d424c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18ff8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_brainpoolp512t1 from Function '_make@982113648': static. (0x7e54fb2d426c)
    //     0x8d4250: ldr             x2, [x2, #0xff8]
    // 0x8d4254: r0 = constructFpStandardCurve()
    //     0x8d4254: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d4258: LeaveFrame
    //     0x8d4258: mov             SP, fp
    //     0x8d425c: ldp             fp, lr, [SP], #0x10
    // 0x8d4260: ret
    //     0x8d4260: ret             
    // 0x8d4264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4264: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4268: b               #0x8d41b0
  }
  [closure] static ECCurve_brainpoolp512t1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d426c, size: 0x20
    // 0x8d426c: EnterFrame
    //     0x8d426c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4270: mov             fp, SP
    // 0x8d4274: r0 = ECCurve_brainpoolp512t1()
    //     0x8d4274: bl              #0x8d428c  ; AllocateECCurve_brainpoolp512t1Stub -> ECCurve_brainpoolp512t1 (size=0xc)
    // 0x8d4278: ldr             x1, [fp, #0x18]
    // 0x8d427c: StoreField: r0->field_7 = r1
    //     0x8d427c: stur            w1, [x0, #7]
    // 0x8d4280: LeaveFrame
    //     0x8d4280: mov             SP, fp
    //     0x8d4284: ldp             fp, lr, [SP], #0x10
    // 0x8d4288: ret
    //     0x8d4288: ret             
  }
}
