// lib: impl.ec_domain_parameters.gostr3410_2001_cryptopro_a, url: package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_a.dart

// class id: 1050977, size: 0x8
class :: {
}

// class id: 625, size: 0xc, field offset: 0xc
class ECCurve_gostr3410_2001_cryptopro_a extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xeec

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d3f88, size: 0x58
    // 0x8d3f88: EnterFrame
    //     0x8d3f88: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3f8c: mov             fp, SP
    // 0x8d3f90: AllocStack(0x8)
    //     0x8d3f90: sub             SP, SP, #8
    // 0x8d3f94: r0 = StaticFactoryConfig()
    //     0x8d3f94: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d3f98: mov             x3, x0
    // 0x8d3f9c: r0 = "GostR3410-2001-CryptoPro-A"
    //     0x8d3f9c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18fa8] "GostR3410-2001-CryptoPro-A"
    //     0x8d3fa0: ldr             x0, [x0, #0xfa8]
    // 0x8d3fa4: stur            x3, [fp, #-8]
    // 0x8d3fa8: StoreField: r3->field_b = r0
    //     0x8d3fa8: stur            w0, [x3, #0xb]
    // 0x8d3fac: r1 = Function '<anonymous closure>': static.
    //     0x8d3fac: add             x1, PP, #0x18, lsl #12  ; [pp+0x18fb0] AnonymousClosure: static (0x8d3fe0), in [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_a.dart] ECCurve_gostr3410_2001_cryptopro_a::factoryConfig (0x8d3f88)
    //     0x8d3fb0: ldr             x1, [x1, #0xfb0]
    // 0x8d3fb4: r2 = Null
    //     0x8d3fb4: mov             x2, NULL
    // 0x8d3fb8: r0 = AllocateClosure()
    //     0x8d3fb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d3fbc: mov             x1, x0
    // 0x8d3fc0: ldur            x0, [fp, #-8]
    // 0x8d3fc4: StoreField: r0->field_f = r1
    //     0x8d3fc4: stur            w1, [x0, #0xf]
    // 0x8d3fc8: r1 = ECDomainParameters
    //     0x8d3fc8: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d3fcc: ldr             x1, [x1, #0x6e8]
    // 0x8d3fd0: StoreField: r0->field_7 = r1
    //     0x8d3fd0: stur            w1, [x0, #7]
    // 0x8d3fd4: LeaveFrame
    //     0x8d3fd4: mov             SP, fp
    //     0x8d3fd8: ldp             fp, lr, [SP], #0x10
    // 0x8d3fdc: ret
    //     0x8d3fdc: ret             
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_a <anonymous closure>(dynamic) {
    // ** addr: 0x8d3fe0, size: 0x30
    // 0x8d3fe0: EnterFrame
    //     0x8d3fe0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3fe4: mov             fp, SP
    // 0x8d3fe8: CheckStackOverflow
    //     0x8d3fe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3fec: cmp             SP, x16
    //     0x8d3ff0: b.ls            #0x8d4008
    // 0x8d3ff4: r1 = Null
    //     0x8d3ff4: mov             x1, NULL
    // 0x8d3ff8: r0 = ECCurve_gostr3410_2001_cryptopro_a()
    //     0x8d3ff8: bl              #0x8d4010  ; [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_a.dart] ECCurve_gostr3410_2001_cryptopro_a::ECCurve_gostr3410_2001_cryptopro_a
    // 0x8d3ffc: LeaveFrame
    //     0x8d3ffc: mov             SP, fp
    //     0x8d4000: ldp             fp, lr, [SP], #0x10
    // 0x8d4004: ret
    //     0x8d4004: ret             
    // 0x8d4008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4008: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d400c: b               #0x8d3ff4
  }
  factory ECCurve_gostr3410_2001_cryptopro_a ECCurve_gostr3410_2001_cryptopro_a(dynamic) {
    // ** addr: 0x8d4010, size: 0xd4
    // 0x8d4010: EnterFrame
    //     0x8d4010: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4014: mov             fp, SP
    // 0x8d4018: AllocStack(0x40)
    //     0x8d4018: sub             SP, SP, #0x40
    // 0x8d401c: CheckStackOverflow
    //     0x8d401c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4020: cmp             SP, x16
    //     0x8d4024: b.ls            #0x8d40dc
    // 0x8d4028: r1 = "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd97"
    //     0x8d4028: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f20] "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd97"
    //     0x8d402c: ldr             x1, [x1, #0xf20]
    // 0x8d4030: r2 = 32
    //     0x8d4030: movz            x2, #0x20
    // 0x8d4034: r0 = parse()
    //     0x8d4034: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4038: r1 = "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd94"
    //     0x8d4038: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f28] "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd94"
    //     0x8d403c: ldr             x1, [x1, #0xf28]
    // 0x8d4040: r2 = 32
    //     0x8d4040: movz            x2, #0x20
    // 0x8d4044: stur            x0, [fp, #-8]
    // 0x8d4048: r0 = parse()
    //     0x8d4048: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d404c: r1 = "a6"
    //     0x8d404c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f30] "a6"
    //     0x8d4050: ldr             x1, [x1, #0xf30]
    // 0x8d4054: r2 = 32
    //     0x8d4054: movz            x2, #0x20
    // 0x8d4058: stur            x0, [fp, #-0x10]
    // 0x8d405c: r0 = parse()
    //     0x8d405c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4060: r1 = "0400000000000000000000000000000000000000000000000000000000000000018d91e471e0989cda27df505a453f2b7635294f2ddf23e3b122acc99c9e9f1e14"
    //     0x8d4060: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f38] "0400000000000000000000000000000000000000000000000000000000000000018d91e471e0989cda27df505a453f2b7635294f2ddf23e3b122acc99c9e9f1e14"
    //     0x8d4064: ldr             x1, [x1, #0xf38]
    // 0x8d4068: r2 = 32
    //     0x8d4068: movz            x2, #0x20
    // 0x8d406c: stur            x0, [fp, #-0x18]
    // 0x8d4070: r0 = parse()
    //     0x8d4070: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4074: r1 = "ffffffffffffffffffffffffffffffff6c611070995ad10045841b09b761b893"
    //     0x8d4074: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f40] "ffffffffffffffffffffffffffffffff6c611070995ad10045841b09b761b893"
    //     0x8d4078: ldr             x1, [x1, #0xf40]
    // 0x8d407c: r2 = 32
    //     0x8d407c: movz            x2, #0x20
    // 0x8d4080: stur            x0, [fp, #-0x20]
    // 0x8d4084: r0 = parse()
    //     0x8d4084: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d4088: r1 = "1"
    //     0x8d4088: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d408c: ldr             x1, [x1, #0x718]
    // 0x8d4090: r2 = 32
    //     0x8d4090: movz            x2, #0x20
    // 0x8d4094: stur            x0, [fp, #-0x28]
    // 0x8d4098: r0 = parse()
    //     0x8d4098: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d409c: ldur            x16, [fp, #-0x28]
    // 0x8d40a0: ldur            lr, [fp, #-8]
    // 0x8d40a4: stp             lr, x16, [SP, #8]
    // 0x8d40a8: str             NULL, [SP]
    // 0x8d40ac: ldur            x3, [fp, #-0x10]
    // 0x8d40b0: ldur            x5, [fp, #-0x18]
    // 0x8d40b4: ldur            x6, [fp, #-0x20]
    // 0x8d40b8: mov             x7, x0
    // 0x8d40bc: r1 = "GostR3410-2001-CryptoPro-A"
    //     0x8d40bc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18fa8] "GostR3410-2001-CryptoPro-A"
    //     0x8d40c0: ldr             x1, [x1, #0xfa8]
    // 0x8d40c4: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_a from Function '_make@983201361': static.
    //     0x8d40c4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18fb8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_a from Function '_make@983201361': static. (0x7e54fb2d40e4)
    //     0x8d40c8: ldr             x2, [x2, #0xfb8]
    // 0x8d40cc: r0 = constructFpStandardCurve()
    //     0x8d40cc: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d40d0: LeaveFrame
    //     0x8d40d0: mov             SP, fp
    //     0x8d40d4: ldp             fp, lr, [SP], #0x10
    // 0x8d40d8: ret
    //     0x8d40d8: ret             
    // 0x8d40dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d40dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d40e0: b               #0x8d4028
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_a _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d40e4, size: 0x20
    // 0x8d40e4: EnterFrame
    //     0x8d40e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d40e8: mov             fp, SP
    // 0x8d40ec: r0 = ECCurve_gostr3410_2001_cryptopro_a()
    //     0x8d40ec: bl              #0x8d4104  ; AllocateECCurve_gostr3410_2001_cryptopro_aStub -> ECCurve_gostr3410_2001_cryptopro_a (size=0xc)
    // 0x8d40f0: ldr             x1, [fp, #0x18]
    // 0x8d40f4: StoreField: r0->field_7 = r1
    //     0x8d40f4: stur            w1, [x0, #7]
    // 0x8d40f8: LeaveFrame
    //     0x8d40f8: mov             SP, fp
    //     0x8d40fc: ldp             fp, lr, [SP], #0x10
    // 0x8d4100: ret
    //     0x8d4100: ret             
  }
}
