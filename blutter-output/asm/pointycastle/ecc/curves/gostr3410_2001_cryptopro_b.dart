// lib: impl.ec_domain_parameters.gostr3410_2001_cryptopro_b, url: package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_b.dart

// class id: 1050978, size: 0x8
class :: {
}

// class id: 624, size: 0xc, field offset: 0xc
class ECCurve_gostr3410_2001_cryptopro_b extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xef0

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d3e00, size: 0x58
    // 0x8d3e00: EnterFrame
    //     0x8d3e00: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3e04: mov             fp, SP
    // 0x8d3e08: AllocStack(0x8)
    //     0x8d3e08: sub             SP, SP, #8
    // 0x8d3e0c: r0 = StaticFactoryConfig()
    //     0x8d3e0c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d3e10: mov             x3, x0
    // 0x8d3e14: r0 = "GostR3410-2001-CryptoPro-B"
    //     0x8d3e14: add             x0, PP, #0x18, lsl #12  ; [pp+0x18f68] "GostR3410-2001-CryptoPro-B"
    //     0x8d3e18: ldr             x0, [x0, #0xf68]
    // 0x8d3e1c: stur            x3, [fp, #-8]
    // 0x8d3e20: StoreField: r3->field_b = r0
    //     0x8d3e20: stur            w0, [x3, #0xb]
    // 0x8d3e24: r1 = Function '<anonymous closure>': static.
    //     0x8d3e24: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f70] AnonymousClosure: static (0x8d3e58), in [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_b.dart] ECCurve_gostr3410_2001_cryptopro_b::factoryConfig (0x8d3e00)
    //     0x8d3e28: ldr             x1, [x1, #0xf70]
    // 0x8d3e2c: r2 = Null
    //     0x8d3e2c: mov             x2, NULL
    // 0x8d3e30: r0 = AllocateClosure()
    //     0x8d3e30: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d3e34: mov             x1, x0
    // 0x8d3e38: ldur            x0, [fp, #-8]
    // 0x8d3e3c: StoreField: r0->field_f = r1
    //     0x8d3e3c: stur            w1, [x0, #0xf]
    // 0x8d3e40: r1 = ECDomainParameters
    //     0x8d3e40: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d3e44: ldr             x1, [x1, #0x6e8]
    // 0x8d3e48: StoreField: r0->field_7 = r1
    //     0x8d3e48: stur            w1, [x0, #7]
    // 0x8d3e4c: LeaveFrame
    //     0x8d3e4c: mov             SP, fp
    //     0x8d3e50: ldp             fp, lr, [SP], #0x10
    // 0x8d3e54: ret
    //     0x8d3e54: ret             
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_b <anonymous closure>(dynamic) {
    // ** addr: 0x8d3e58, size: 0x30
    // 0x8d3e58: EnterFrame
    //     0x8d3e58: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3e5c: mov             fp, SP
    // 0x8d3e60: CheckStackOverflow
    //     0x8d3e60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3e64: cmp             SP, x16
    //     0x8d3e68: b.ls            #0x8d3e80
    // 0x8d3e6c: r1 = Null
    //     0x8d3e6c: mov             x1, NULL
    // 0x8d3e70: r0 = ECCurve_gostr3410_2001_cryptopro_b()
    //     0x8d3e70: bl              #0x8d3e88  ; [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_b.dart] ECCurve_gostr3410_2001_cryptopro_b::ECCurve_gostr3410_2001_cryptopro_b
    // 0x8d3e74: LeaveFrame
    //     0x8d3e74: mov             SP, fp
    //     0x8d3e78: ldp             fp, lr, [SP], #0x10
    // 0x8d3e7c: ret
    //     0x8d3e7c: ret             
    // 0x8d3e80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3e80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3e84: b               #0x8d3e6c
  }
  factory ECCurve_gostr3410_2001_cryptopro_b ECCurve_gostr3410_2001_cryptopro_b(dynamic) {
    // ** addr: 0x8d3e88, size: 0xd4
    // 0x8d3e88: EnterFrame
    //     0x8d3e88: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3e8c: mov             fp, SP
    // 0x8d3e90: AllocStack(0x40)
    //     0x8d3e90: sub             SP, SP, #0x40
    // 0x8d3e94: CheckStackOverflow
    //     0x8d3e94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3e98: cmp             SP, x16
    //     0x8d3e9c: b.ls            #0x8d3f54
    // 0x8d3ea0: r1 = "8000000000000000000000000000000000000000000000000000000000000c99"
    //     0x8d3ea0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f78] "8000000000000000000000000000000000000000000000000000000000000c99"
    //     0x8d3ea4: ldr             x1, [x1, #0xf78]
    // 0x8d3ea8: r2 = 32
    //     0x8d3ea8: movz            x2, #0x20
    // 0x8d3eac: r0 = parse()
    //     0x8d3eac: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3eb0: r1 = "8000000000000000000000000000000000000000000000000000000000000c96"
    //     0x8d3eb0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f80] "8000000000000000000000000000000000000000000000000000000000000c96"
    //     0x8d3eb4: ldr             x1, [x1, #0xf80]
    // 0x8d3eb8: r2 = 32
    //     0x8d3eb8: movz            x2, #0x20
    // 0x8d3ebc: stur            x0, [fp, #-8]
    // 0x8d3ec0: r0 = parse()
    //     0x8d3ec0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3ec4: r1 = "3e1af419a269a5f866a7d3c25c3df80ae979259373ff2b182f49d4ce7e1bbc8b"
    //     0x8d3ec4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f88] "3e1af419a269a5f866a7d3c25c3df80ae979259373ff2b182f49d4ce7e1bbc8b"
    //     0x8d3ec8: ldr             x1, [x1, #0xf88]
    // 0x8d3ecc: r2 = 32
    //     0x8d3ecc: movz            x2, #0x20
    // 0x8d3ed0: stur            x0, [fp, #-0x10]
    // 0x8d3ed4: r0 = parse()
    //     0x8d3ed4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3ed8: r1 = "0400000000000000000000000000000000000000000000000000000000000000013fa8124359f96680b83d1c3eb2c070e5c545c9858d03ecfb744bf8d717717efc"
    //     0x8d3ed8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f90] "0400000000000000000000000000000000000000000000000000000000000000013fa8124359f96680b83d1c3eb2c070e5c545c9858d03ecfb744bf8d717717efc"
    //     0x8d3edc: ldr             x1, [x1, #0xf90]
    // 0x8d3ee0: r2 = 32
    //     0x8d3ee0: movz            x2, #0x20
    // 0x8d3ee4: stur            x0, [fp, #-0x18]
    // 0x8d3ee8: r0 = parse()
    //     0x8d3ee8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3eec: r1 = "800000000000000000000000000000015f700cfff1a624e5e497161bcc8a198f"
    //     0x8d3eec: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f98] "800000000000000000000000000000015f700cfff1a624e5e497161bcc8a198f"
    //     0x8d3ef0: ldr             x1, [x1, #0xf98]
    // 0x8d3ef4: r2 = 32
    //     0x8d3ef4: movz            x2, #0x20
    // 0x8d3ef8: stur            x0, [fp, #-0x20]
    // 0x8d3efc: r0 = parse()
    //     0x8d3efc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3f00: r1 = "1"
    //     0x8d3f00: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d3f04: ldr             x1, [x1, #0x718]
    // 0x8d3f08: r2 = 32
    //     0x8d3f08: movz            x2, #0x20
    // 0x8d3f0c: stur            x0, [fp, #-0x28]
    // 0x8d3f10: r0 = parse()
    //     0x8d3f10: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3f14: ldur            x16, [fp, #-0x28]
    // 0x8d3f18: ldur            lr, [fp, #-8]
    // 0x8d3f1c: stp             lr, x16, [SP, #8]
    // 0x8d3f20: str             NULL, [SP]
    // 0x8d3f24: ldur            x3, [fp, #-0x10]
    // 0x8d3f28: ldur            x5, [fp, #-0x18]
    // 0x8d3f2c: ldur            x6, [fp, #-0x20]
    // 0x8d3f30: mov             x7, x0
    // 0x8d3f34: r1 = "GostR3410-2001-CryptoPro-B"
    //     0x8d3f34: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f68] "GostR3410-2001-CryptoPro-B"
    //     0x8d3f38: ldr             x1, [x1, #0xf68]
    // 0x8d3f3c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_b from Function '_make@984271363': static.
    //     0x8d3f3c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18fa0] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_b from Function '_make@984271363': static. (0x7e54fb2d3f5c)
    //     0x8d3f40: ldr             x2, [x2, #0xfa0]
    // 0x8d3f44: r0 = constructFpStandardCurve()
    //     0x8d3f44: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d3f48: LeaveFrame
    //     0x8d3f48: mov             SP, fp
    //     0x8d3f4c: ldp             fp, lr, [SP], #0x10
    // 0x8d3f50: ret
    //     0x8d3f50: ret             
    // 0x8d3f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3f54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3f58: b               #0x8d3ea0
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_b _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d3f5c, size: 0x20
    // 0x8d3f5c: EnterFrame
    //     0x8d3f5c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3f60: mov             fp, SP
    // 0x8d3f64: r0 = ECCurve_gostr3410_2001_cryptopro_b()
    //     0x8d3f64: bl              #0x8d3f7c  ; AllocateECCurve_gostr3410_2001_cryptopro_bStub -> ECCurve_gostr3410_2001_cryptopro_b (size=0xc)
    // 0x8d3f68: ldr             x1, [fp, #0x18]
    // 0x8d3f6c: StoreField: r0->field_7 = r1
    //     0x8d3f6c: stur            w1, [x0, #7]
    // 0x8d3f70: LeaveFrame
    //     0x8d3f70: mov             SP, fp
    //     0x8d3f74: ldp             fp, lr, [SP], #0x10
    // 0x8d3f78: ret
    //     0x8d3f78: ret             
  }
}
