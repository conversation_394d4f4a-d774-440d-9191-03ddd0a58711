// lib: impl.ec_domain_parameters.gostr3410_2001_cryptopro_c, url: package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_c.dart

// class id: 1050979, size: 0x8
class :: {
}

// class id: 623, size: 0xc, field offset: 0xc
class ECCurve_gostr3410_2001_cryptopro_c extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xef4

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d3c78, size: 0x58
    // 0x8d3c78: EnterFrame
    //     0x8d3c78: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3c7c: mov             fp, SP
    // 0x8d3c80: AllocStack(0x8)
    //     0x8d3c80: sub             SP, SP, #8
    // 0x8d3c84: r0 = StaticFactoryConfig()
    //     0x8d3c84: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d3c88: mov             x3, x0
    // 0x8d3c8c: r0 = "GostR3410-2001-CryptoPro-C"
    //     0x8d3c8c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18f50] "GostR3410-2001-CryptoPro-C"
    //     0x8d3c90: ldr             x0, [x0, #0xf50]
    // 0x8d3c94: stur            x3, [fp, #-8]
    // 0x8d3c98: StoreField: r3->field_b = r0
    //     0x8d3c98: stur            w0, [x3, #0xb]
    // 0x8d3c9c: r1 = Function '<anonymous closure>': static.
    //     0x8d3c9c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f58] AnonymousClosure: static (0x8d3cd0), in [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_c.dart] ECCurve_gostr3410_2001_cryptopro_c::factoryConfig (0x8d3c78)
    //     0x8d3ca0: ldr             x1, [x1, #0xf58]
    // 0x8d3ca4: r2 = Null
    //     0x8d3ca4: mov             x2, NULL
    // 0x8d3ca8: r0 = AllocateClosure()
    //     0x8d3ca8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d3cac: mov             x1, x0
    // 0x8d3cb0: ldur            x0, [fp, #-8]
    // 0x8d3cb4: StoreField: r0->field_f = r1
    //     0x8d3cb4: stur            w1, [x0, #0xf]
    // 0x8d3cb8: r1 = ECDomainParameters
    //     0x8d3cb8: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d3cbc: ldr             x1, [x1, #0x6e8]
    // 0x8d3cc0: StoreField: r0->field_7 = r1
    //     0x8d3cc0: stur            w1, [x0, #7]
    // 0x8d3cc4: LeaveFrame
    //     0x8d3cc4: mov             SP, fp
    //     0x8d3cc8: ldp             fp, lr, [SP], #0x10
    // 0x8d3ccc: ret
    //     0x8d3ccc: ret             
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_c <anonymous closure>(dynamic) {
    // ** addr: 0x8d3cd0, size: 0x30
    // 0x8d3cd0: EnterFrame
    //     0x8d3cd0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3cd4: mov             fp, SP
    // 0x8d3cd8: CheckStackOverflow
    //     0x8d3cd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3cdc: cmp             SP, x16
    //     0x8d3ce0: b.ls            #0x8d3cf8
    // 0x8d3ce4: r1 = Null
    //     0x8d3ce4: mov             x1, NULL
    // 0x8d3ce8: r0 = ECCurve_gostr3410_2001_cryptopro_c()
    //     0x8d3ce8: bl              #0x8d3d00  ; [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_c.dart] ECCurve_gostr3410_2001_cryptopro_c::ECCurve_gostr3410_2001_cryptopro_c
    // 0x8d3cec: LeaveFrame
    //     0x8d3cec: mov             SP, fp
    //     0x8d3cf0: ldp             fp, lr, [SP], #0x10
    // 0x8d3cf4: ret
    //     0x8d3cf4: ret             
    // 0x8d3cf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3cf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3cfc: b               #0x8d3ce4
  }
  factory ECCurve_gostr3410_2001_cryptopro_c ECCurve_gostr3410_2001_cryptopro_c(dynamic) {
    // ** addr: 0x8d3d00, size: 0xd4
    // 0x8d3d00: EnterFrame
    //     0x8d3d00: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3d04: mov             fp, SP
    // 0x8d3d08: AllocStack(0x40)
    //     0x8d3d08: sub             SP, SP, #0x40
    // 0x8d3d0c: CheckStackOverflow
    //     0x8d3d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3d10: cmp             SP, x16
    //     0x8d3d14: b.ls            #0x8d3dcc
    // 0x8d3d18: r1 = "9b9f605f5a858107ab1ec85e6b41c8aacf846e86789051d37998f7b9022d759b"
    //     0x8d3d18: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ee0] "9b9f605f5a858107ab1ec85e6b41c8aacf846e86789051d37998f7b9022d759b"
    //     0x8d3d1c: ldr             x1, [x1, #0xee0]
    // 0x8d3d20: r2 = 32
    //     0x8d3d20: movz            x2, #0x20
    // 0x8d3d24: r0 = parse()
    //     0x8d3d24: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3d28: r1 = "9b9f605f5a858107ab1ec85e6b41c8aacf846e86789051d37998f7b9022d7598"
    //     0x8d3d28: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ee8] "9b9f605f5a858107ab1ec85e6b41c8aacf846e86789051d37998f7b9022d7598"
    //     0x8d3d2c: ldr             x1, [x1, #0xee8]
    // 0x8d3d30: r2 = 32
    //     0x8d3d30: movz            x2, #0x20
    // 0x8d3d34: stur            x0, [fp, #-8]
    // 0x8d3d38: r0 = parse()
    //     0x8d3d38: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3d3c: r1 = "805a"
    //     0x8d3d3c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ef0] "805a"
    //     0x8d3d40: ldr             x1, [x1, #0xef0]
    // 0x8d3d44: r2 = 32
    //     0x8d3d44: movz            x2, #0x20
    // 0x8d3d48: stur            x0, [fp, #-0x10]
    // 0x8d3d4c: r0 = parse()
    //     0x8d3d4c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3d50: r1 = "04000000000000000000000000000000000000000000000000000000000000000041ece55743711a8c3cbf3783cd08c0ee4d4dc440d4641a8f366e550dfdb3bb67"
    //     0x8d3d50: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ef8] "04000000000000000000000000000000000000000000000000000000000000000041ece55743711a8c3cbf3783cd08c0ee4d4dc440d4641a8f366e550dfdb3bb67"
    //     0x8d3d54: ldr             x1, [x1, #0xef8]
    // 0x8d3d58: r2 = 32
    //     0x8d3d58: movz            x2, #0x20
    // 0x8d3d5c: stur            x0, [fp, #-0x18]
    // 0x8d3d60: r0 = parse()
    //     0x8d3d60: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3d64: r1 = "9b9f605f5a858107ab1ec85e6b41c8aa582ca3511eddfb74f02f3a6598980bb9"
    //     0x8d3d64: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f00] "9b9f605f5a858107ab1ec85e6b41c8aa582ca3511eddfb74f02f3a6598980bb9"
    //     0x8d3d68: ldr             x1, [x1, #0xf00]
    // 0x8d3d6c: r2 = 32
    //     0x8d3d6c: movz            x2, #0x20
    // 0x8d3d70: stur            x0, [fp, #-0x20]
    // 0x8d3d74: r0 = parse()
    //     0x8d3d74: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3d78: r1 = "1"
    //     0x8d3d78: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d3d7c: ldr             x1, [x1, #0x718]
    // 0x8d3d80: r2 = 32
    //     0x8d3d80: movz            x2, #0x20
    // 0x8d3d84: stur            x0, [fp, #-0x28]
    // 0x8d3d88: r0 = parse()
    //     0x8d3d88: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3d8c: ldur            x16, [fp, #-0x28]
    // 0x8d3d90: ldur            lr, [fp, #-8]
    // 0x8d3d94: stp             lr, x16, [SP, #8]
    // 0x8d3d98: str             NULL, [SP]
    // 0x8d3d9c: ldur            x3, [fp, #-0x10]
    // 0x8d3da0: ldur            x5, [fp, #-0x18]
    // 0x8d3da4: ldur            x6, [fp, #-0x20]
    // 0x8d3da8: mov             x7, x0
    // 0x8d3dac: r1 = "GostR3410-2001-CryptoPro-C"
    //     0x8d3dac: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f50] "GostR3410-2001-CryptoPro-C"
    //     0x8d3db0: ldr             x1, [x1, #0xf50]
    // 0x8d3db4: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_c from Function '_make@985029833': static.
    //     0x8d3db4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18f60] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_c from Function '_make@985029833': static. (0x7e54fb2d3dd4)
    //     0x8d3db8: ldr             x2, [x2, #0xf60]
    // 0x8d3dbc: r0 = constructFpStandardCurve()
    //     0x8d3dbc: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d3dc0: LeaveFrame
    //     0x8d3dc0: mov             SP, fp
    //     0x8d3dc4: ldp             fp, lr, [SP], #0x10
    // 0x8d3dc8: ret
    //     0x8d3dc8: ret             
    // 0x8d3dcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3dcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3dd0: b               #0x8d3d18
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_c _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d3dd4, size: 0x20
    // 0x8d3dd4: EnterFrame
    //     0x8d3dd4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3dd8: mov             fp, SP
    // 0x8d3ddc: r0 = ECCurve_gostr3410_2001_cryptopro_c()
    //     0x8d3ddc: bl              #0x8d3df4  ; AllocateECCurve_gostr3410_2001_cryptopro_cStub -> ECCurve_gostr3410_2001_cryptopro_c (size=0xc)
    // 0x8d3de0: ldr             x1, [fp, #0x18]
    // 0x8d3de4: StoreField: r0->field_7 = r1
    //     0x8d3de4: stur            w1, [x0, #7]
    // 0x8d3de8: LeaveFrame
    //     0x8d3de8: mov             SP, fp
    //     0x8d3dec: ldp             fp, lr, [SP], #0x10
    // 0x8d3df0: ret
    //     0x8d3df0: ret             
  }
}
