// lib: impl.ec_domain_parameters.gostr3410_2001_cryptopro_xcha, url: package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_xcha.dart

// class id: 1050980, size: 0x8
class :: {
}

// class id: 622, size: 0xc, field offset: 0xc
class ECCurve_gostr3410_2001_cryptopro_xcha extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xef8

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d3af0, size: 0x58
    // 0x8d3af0: EnterFrame
    //     0x8d3af0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3af4: mov             fp, SP
    // 0x8d3af8: AllocStack(0x8)
    //     0x8d3af8: sub             SP, SP, #8
    // 0x8d3afc: r0 = StaticFactoryConfig()
    //     0x8d3afc: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d3b00: mov             x3, x0
    // 0x8d3b04: r0 = "GostR3410-2001-CryptoPro-XchA"
    //     0x8d3b04: add             x0, PP, #0x18, lsl #12  ; [pp+0x18f10] "GostR3410-2001-CryptoPro-XchA"
    //     0x8d3b08: ldr             x0, [x0, #0xf10]
    // 0x8d3b0c: stur            x3, [fp, #-8]
    // 0x8d3b10: StoreField: r3->field_b = r0
    //     0x8d3b10: stur            w0, [x3, #0xb]
    // 0x8d3b14: r1 = Function '<anonymous closure>': static.
    //     0x8d3b14: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f18] AnonymousClosure: static (0x8d3b48), in [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_xcha.dart] ECCurve_gostr3410_2001_cryptopro_xcha::factoryConfig (0x8d3af0)
    //     0x8d3b18: ldr             x1, [x1, #0xf18]
    // 0x8d3b1c: r2 = Null
    //     0x8d3b1c: mov             x2, NULL
    // 0x8d3b20: r0 = AllocateClosure()
    //     0x8d3b20: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d3b24: mov             x1, x0
    // 0x8d3b28: ldur            x0, [fp, #-8]
    // 0x8d3b2c: StoreField: r0->field_f = r1
    //     0x8d3b2c: stur            w1, [x0, #0xf]
    // 0x8d3b30: r1 = ECDomainParameters
    //     0x8d3b30: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d3b34: ldr             x1, [x1, #0x6e8]
    // 0x8d3b38: StoreField: r0->field_7 = r1
    //     0x8d3b38: stur            w1, [x0, #7]
    // 0x8d3b3c: LeaveFrame
    //     0x8d3b3c: mov             SP, fp
    //     0x8d3b40: ldp             fp, lr, [SP], #0x10
    // 0x8d3b44: ret
    //     0x8d3b44: ret             
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_xcha <anonymous closure>(dynamic) {
    // ** addr: 0x8d3b48, size: 0x30
    // 0x8d3b48: EnterFrame
    //     0x8d3b48: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3b4c: mov             fp, SP
    // 0x8d3b50: CheckStackOverflow
    //     0x8d3b50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3b54: cmp             SP, x16
    //     0x8d3b58: b.ls            #0x8d3b70
    // 0x8d3b5c: r1 = Null
    //     0x8d3b5c: mov             x1, NULL
    // 0x8d3b60: r0 = ECCurve_gostr3410_2001_cryptopro_xcha()
    //     0x8d3b60: bl              #0x8d3b78  ; [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_xcha.dart] ECCurve_gostr3410_2001_cryptopro_xcha::ECCurve_gostr3410_2001_cryptopro_xcha
    // 0x8d3b64: LeaveFrame
    //     0x8d3b64: mov             SP, fp
    //     0x8d3b68: ldp             fp, lr, [SP], #0x10
    // 0x8d3b6c: ret
    //     0x8d3b6c: ret             
    // 0x8d3b70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3b70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3b74: b               #0x8d3b5c
  }
  factory ECCurve_gostr3410_2001_cryptopro_xcha ECCurve_gostr3410_2001_cryptopro_xcha(dynamic) {
    // ** addr: 0x8d3b78, size: 0xd4
    // 0x8d3b78: EnterFrame
    //     0x8d3b78: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3b7c: mov             fp, SP
    // 0x8d3b80: AllocStack(0x40)
    //     0x8d3b80: sub             SP, SP, #0x40
    // 0x8d3b84: CheckStackOverflow
    //     0x8d3b84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3b88: cmp             SP, x16
    //     0x8d3b8c: b.ls            #0x8d3c44
    // 0x8d3b90: r1 = "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd97"
    //     0x8d3b90: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f20] "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd97"
    //     0x8d3b94: ldr             x1, [x1, #0xf20]
    // 0x8d3b98: r2 = 32
    //     0x8d3b98: movz            x2, #0x20
    // 0x8d3b9c: r0 = parse()
    //     0x8d3b9c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3ba0: r1 = "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd94"
    //     0x8d3ba0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f28] "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd94"
    //     0x8d3ba4: ldr             x1, [x1, #0xf28]
    // 0x8d3ba8: r2 = 32
    //     0x8d3ba8: movz            x2, #0x20
    // 0x8d3bac: stur            x0, [fp, #-8]
    // 0x8d3bb0: r0 = parse()
    //     0x8d3bb0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3bb4: r1 = "a6"
    //     0x8d3bb4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f30] "a6"
    //     0x8d3bb8: ldr             x1, [x1, #0xf30]
    // 0x8d3bbc: r2 = 32
    //     0x8d3bbc: movz            x2, #0x20
    // 0x8d3bc0: stur            x0, [fp, #-0x10]
    // 0x8d3bc4: r0 = parse()
    //     0x8d3bc4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3bc8: r1 = "0400000000000000000000000000000000000000000000000000000000000000018d91e471e0989cda27df505a453f2b7635294f2ddf23e3b122acc99c9e9f1e14"
    //     0x8d3bc8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f38] "0400000000000000000000000000000000000000000000000000000000000000018d91e471e0989cda27df505a453f2b7635294f2ddf23e3b122acc99c9e9f1e14"
    //     0x8d3bcc: ldr             x1, [x1, #0xf38]
    // 0x8d3bd0: r2 = 32
    //     0x8d3bd0: movz            x2, #0x20
    // 0x8d3bd4: stur            x0, [fp, #-0x18]
    // 0x8d3bd8: r0 = parse()
    //     0x8d3bd8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3bdc: r1 = "ffffffffffffffffffffffffffffffff6c611070995ad10045841b09b761b893"
    //     0x8d3bdc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f40] "ffffffffffffffffffffffffffffffff6c611070995ad10045841b09b761b893"
    //     0x8d3be0: ldr             x1, [x1, #0xf40]
    // 0x8d3be4: r2 = 32
    //     0x8d3be4: movz            x2, #0x20
    // 0x8d3be8: stur            x0, [fp, #-0x20]
    // 0x8d3bec: r0 = parse()
    //     0x8d3bec: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3bf0: r1 = "1"
    //     0x8d3bf0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d3bf4: ldr             x1, [x1, #0x718]
    // 0x8d3bf8: r2 = 32
    //     0x8d3bf8: movz            x2, #0x20
    // 0x8d3bfc: stur            x0, [fp, #-0x28]
    // 0x8d3c00: r0 = parse()
    //     0x8d3c00: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3c04: ldur            x16, [fp, #-0x28]
    // 0x8d3c08: ldur            lr, [fp, #-8]
    // 0x8d3c0c: stp             lr, x16, [SP, #8]
    // 0x8d3c10: str             NULL, [SP]
    // 0x8d3c14: ldur            x3, [fp, #-0x10]
    // 0x8d3c18: ldur            x5, [fp, #-0x18]
    // 0x8d3c1c: ldur            x6, [fp, #-0x20]
    // 0x8d3c20: mov             x7, x0
    // 0x8d3c24: r1 = "GostR3410-2001-CryptoPro-XchA"
    //     0x8d3c24: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f10] "GostR3410-2001-CryptoPro-XchA"
    //     0x8d3c28: ldr             x1, [x1, #0xf10]
    // 0x8d3c2c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_xcha from Function '_make@986493575': static.
    //     0x8d3c2c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18f48] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_xcha from Function '_make@986493575': static. (0x7e54fb2d3c4c)
    //     0x8d3c30: ldr             x2, [x2, #0xf48]
    // 0x8d3c34: r0 = constructFpStandardCurve()
    //     0x8d3c34: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d3c38: LeaveFrame
    //     0x8d3c38: mov             SP, fp
    //     0x8d3c3c: ldp             fp, lr, [SP], #0x10
    // 0x8d3c40: ret
    //     0x8d3c40: ret             
    // 0x8d3c44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3c44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3c48: b               #0x8d3b90
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_xcha _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d3c4c, size: 0x20
    // 0x8d3c4c: EnterFrame
    //     0x8d3c4c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3c50: mov             fp, SP
    // 0x8d3c54: r0 = ECCurve_gostr3410_2001_cryptopro_xcha()
    //     0x8d3c54: bl              #0x8d3c6c  ; AllocateECCurve_gostr3410_2001_cryptopro_xchaStub -> ECCurve_gostr3410_2001_cryptopro_xcha (size=0xc)
    // 0x8d3c58: ldr             x1, [fp, #0x18]
    // 0x8d3c5c: StoreField: r0->field_7 = r1
    //     0x8d3c5c: stur            w1, [x0, #7]
    // 0x8d3c60: LeaveFrame
    //     0x8d3c60: mov             SP, fp
    //     0x8d3c64: ldp             fp, lr, [SP], #0x10
    // 0x8d3c68: ret
    //     0x8d3c68: ret             
  }
}
