// lib: impl.ec_domain_parameters.gostr3410_2001_cryptopro_xchb, url: package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_xchb.dart

// class id: 1050981, size: 0x8
class :: {
}

// class id: 621, size: 0xc, field offset: 0xc
class ECCurve_gostr3410_2001_cryptopro_xchb extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xefc

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d3968, size: 0x58
    // 0x8d3968: EnterFrame
    //     0x8d3968: stp             fp, lr, [SP, #-0x10]!
    //     0x8d396c: mov             fp, SP
    // 0x8d3970: AllocStack(0x8)
    //     0x8d3970: sub             SP, SP, #8
    // 0x8d3974: r0 = StaticFactoryConfig()
    //     0x8d3974: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d3978: mov             x3, x0
    // 0x8d397c: r0 = "GostR3410-2001-CryptoPro-XchB"
    //     0x8d397c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18ed0] "GostR3410-2001-CryptoPro-XchB"
    //     0x8d3980: ldr             x0, [x0, #0xed0]
    // 0x8d3984: stur            x3, [fp, #-8]
    // 0x8d3988: StoreField: r3->field_b = r0
    //     0x8d3988: stur            w0, [x3, #0xb]
    // 0x8d398c: r1 = Function '<anonymous closure>': static.
    //     0x8d398c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ed8] AnonymousClosure: static (0x8d39c0), in [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_xchb.dart] ECCurve_gostr3410_2001_cryptopro_xchb::factoryConfig (0x8d3968)
    //     0x8d3990: ldr             x1, [x1, #0xed8]
    // 0x8d3994: r2 = Null
    //     0x8d3994: mov             x2, NULL
    // 0x8d3998: r0 = AllocateClosure()
    //     0x8d3998: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d399c: mov             x1, x0
    // 0x8d39a0: ldur            x0, [fp, #-8]
    // 0x8d39a4: StoreField: r0->field_f = r1
    //     0x8d39a4: stur            w1, [x0, #0xf]
    // 0x8d39a8: r1 = ECDomainParameters
    //     0x8d39a8: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d39ac: ldr             x1, [x1, #0x6e8]
    // 0x8d39b0: StoreField: r0->field_7 = r1
    //     0x8d39b0: stur            w1, [x0, #7]
    // 0x8d39b4: LeaveFrame
    //     0x8d39b4: mov             SP, fp
    //     0x8d39b8: ldp             fp, lr, [SP], #0x10
    // 0x8d39bc: ret
    //     0x8d39bc: ret             
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_xchb <anonymous closure>(dynamic) {
    // ** addr: 0x8d39c0, size: 0x30
    // 0x8d39c0: EnterFrame
    //     0x8d39c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d39c4: mov             fp, SP
    // 0x8d39c8: CheckStackOverflow
    //     0x8d39c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d39cc: cmp             SP, x16
    //     0x8d39d0: b.ls            #0x8d39e8
    // 0x8d39d4: r1 = Null
    //     0x8d39d4: mov             x1, NULL
    // 0x8d39d8: r0 = ECCurve_gostr3410_2001_cryptopro_xchb()
    //     0x8d39d8: bl              #0x8d39f0  ; [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_xchb.dart] ECCurve_gostr3410_2001_cryptopro_xchb::ECCurve_gostr3410_2001_cryptopro_xchb
    // 0x8d39dc: LeaveFrame
    //     0x8d39dc: mov             SP, fp
    //     0x8d39e0: ldp             fp, lr, [SP], #0x10
    // 0x8d39e4: ret
    //     0x8d39e4: ret             
    // 0x8d39e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d39e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d39ec: b               #0x8d39d4
  }
  factory ECCurve_gostr3410_2001_cryptopro_xchb ECCurve_gostr3410_2001_cryptopro_xchb(dynamic) {
    // ** addr: 0x8d39f0, size: 0xd4
    // 0x8d39f0: EnterFrame
    //     0x8d39f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d39f4: mov             fp, SP
    // 0x8d39f8: AllocStack(0x40)
    //     0x8d39f8: sub             SP, SP, #0x40
    // 0x8d39fc: CheckStackOverflow
    //     0x8d39fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3a00: cmp             SP, x16
    //     0x8d3a04: b.ls            #0x8d3abc
    // 0x8d3a08: r1 = "9b9f605f5a858107ab1ec85e6b41c8aacf846e86789051d37998f7b9022d759b"
    //     0x8d3a08: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ee0] "9b9f605f5a858107ab1ec85e6b41c8aacf846e86789051d37998f7b9022d759b"
    //     0x8d3a0c: ldr             x1, [x1, #0xee0]
    // 0x8d3a10: r2 = 32
    //     0x8d3a10: movz            x2, #0x20
    // 0x8d3a14: r0 = parse()
    //     0x8d3a14: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3a18: r1 = "9b9f605f5a858107ab1ec85e6b41c8aacf846e86789051d37998f7b9022d7598"
    //     0x8d3a18: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ee8] "9b9f605f5a858107ab1ec85e6b41c8aacf846e86789051d37998f7b9022d7598"
    //     0x8d3a1c: ldr             x1, [x1, #0xee8]
    // 0x8d3a20: r2 = 32
    //     0x8d3a20: movz            x2, #0x20
    // 0x8d3a24: stur            x0, [fp, #-8]
    // 0x8d3a28: r0 = parse()
    //     0x8d3a28: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3a2c: r1 = "805a"
    //     0x8d3a2c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ef0] "805a"
    //     0x8d3a30: ldr             x1, [x1, #0xef0]
    // 0x8d3a34: r2 = 32
    //     0x8d3a34: movz            x2, #0x20
    // 0x8d3a38: stur            x0, [fp, #-0x10]
    // 0x8d3a3c: r0 = parse()
    //     0x8d3a3c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3a40: r1 = "04000000000000000000000000000000000000000000000000000000000000000041ece55743711a8c3cbf3783cd08c0ee4d4dc440d4641a8f366e550dfdb3bb67"
    //     0x8d3a40: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ef8] "04000000000000000000000000000000000000000000000000000000000000000041ece55743711a8c3cbf3783cd08c0ee4d4dc440d4641a8f366e550dfdb3bb67"
    //     0x8d3a44: ldr             x1, [x1, #0xef8]
    // 0x8d3a48: r2 = 32
    //     0x8d3a48: movz            x2, #0x20
    // 0x8d3a4c: stur            x0, [fp, #-0x18]
    // 0x8d3a50: r0 = parse()
    //     0x8d3a50: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3a54: r1 = "9b9f605f5a858107ab1ec85e6b41c8aa582ca3511eddfb74f02f3a6598980bb9"
    //     0x8d3a54: add             x1, PP, #0x18, lsl #12  ; [pp+0x18f00] "9b9f605f5a858107ab1ec85e6b41c8aa582ca3511eddfb74f02f3a6598980bb9"
    //     0x8d3a58: ldr             x1, [x1, #0xf00]
    // 0x8d3a5c: r2 = 32
    //     0x8d3a5c: movz            x2, #0x20
    // 0x8d3a60: stur            x0, [fp, #-0x20]
    // 0x8d3a64: r0 = parse()
    //     0x8d3a64: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3a68: r1 = "1"
    //     0x8d3a68: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d3a6c: ldr             x1, [x1, #0x718]
    // 0x8d3a70: r2 = 32
    //     0x8d3a70: movz            x2, #0x20
    // 0x8d3a74: stur            x0, [fp, #-0x28]
    // 0x8d3a78: r0 = parse()
    //     0x8d3a78: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3a7c: ldur            x16, [fp, #-0x28]
    // 0x8d3a80: ldur            lr, [fp, #-8]
    // 0x8d3a84: stp             lr, x16, [SP, #8]
    // 0x8d3a88: str             NULL, [SP]
    // 0x8d3a8c: ldur            x3, [fp, #-0x10]
    // 0x8d3a90: ldur            x5, [fp, #-0x18]
    // 0x8d3a94: ldur            x6, [fp, #-0x20]
    // 0x8d3a98: mov             x7, x0
    // 0x8d3a9c: r1 = "GostR3410-2001-CryptoPro-XchB"
    //     0x8d3a9c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ed0] "GostR3410-2001-CryptoPro-XchB"
    //     0x8d3aa0: ldr             x1, [x1, #0xed0]
    // 0x8d3aa4: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_xchb from Function '_make@987510465': static.
    //     0x8d3aa4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18f08] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_gostr3410_2001_cryptopro_xchb from Function '_make@987510465': static. (0x7e54fb2d3ac4)
    //     0x8d3aa8: ldr             x2, [x2, #0xf08]
    // 0x8d3aac: r0 = constructFpStandardCurve()
    //     0x8d3aac: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d3ab0: LeaveFrame
    //     0x8d3ab0: mov             SP, fp
    //     0x8d3ab4: ldp             fp, lr, [SP], #0x10
    // 0x8d3ab8: ret
    //     0x8d3ab8: ret             
    // 0x8d3abc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3abc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3ac0: b               #0x8d3a08
  }
  [closure] static ECCurve_gostr3410_2001_cryptopro_xchb _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d3ac4, size: 0x20
    // 0x8d3ac4: EnterFrame
    //     0x8d3ac4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3ac8: mov             fp, SP
    // 0x8d3acc: r0 = ECCurve_gostr3410_2001_cryptopro_xchb()
    //     0x8d3acc: bl              #0x8d3ae4  ; AllocateECCurve_gostr3410_2001_cryptopro_xchbStub -> ECCurve_gostr3410_2001_cryptopro_xchb (size=0xc)
    // 0x8d3ad0: ldr             x1, [fp, #0x18]
    // 0x8d3ad4: StoreField: r0->field_7 = r1
    //     0x8d3ad4: stur            w1, [x0, #7]
    // 0x8d3ad8: LeaveFrame
    //     0x8d3ad8: mov             SP, fp
    //     0x8d3adc: ldp             fp, lr, [SP], #0x10
    // 0x8d3ae0: ret
    //     0x8d3ae0: ret             
  }
}
