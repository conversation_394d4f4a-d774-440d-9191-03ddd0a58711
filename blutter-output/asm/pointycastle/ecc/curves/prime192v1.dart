// lib: impl.ec_domain_parameters.prime192v1, url: package:pointycastle/ecc/curves/prime192v1.dart

// class id: 1050982, size: 0x8
class :: {
}

// class id: 620, size: 0xc, field offset: 0xc
class ECCurve_prime192v1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf00

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d37cc, size: 0x58
    // 0x8d37cc: EnterFrame
    //     0x8d37cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d37d0: mov             fp, SP
    // 0x8d37d4: AllocStack(0x8)
    //     0x8d37d4: sub             SP, SP, #8
    // 0x8d37d8: r0 = StaticFactoryConfig()
    //     0x8d37d8: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d37dc: mov             x3, x0
    // 0x8d37e0: r0 = "prime192v1"
    //     0x8d37e0: add             x0, PP, #0x18, lsl #12  ; [pp+0x18eb0] "prime192v1"
    //     0x8d37e4: ldr             x0, [x0, #0xeb0]
    // 0x8d37e8: stur            x3, [fp, #-8]
    // 0x8d37ec: StoreField: r3->field_b = r0
    //     0x8d37ec: stur            w0, [x3, #0xb]
    // 0x8d37f0: r1 = Function '<anonymous closure>': static.
    //     0x8d37f0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18eb8] AnonymousClosure: static (0x8d3824), in [package:pointycastle/ecc/curves/prime192v1.dart] ECCurve_prime192v1::factoryConfig (0x8d37cc)
    //     0x8d37f4: ldr             x1, [x1, #0xeb8]
    // 0x8d37f8: r2 = Null
    //     0x8d37f8: mov             x2, NULL
    // 0x8d37fc: r0 = AllocateClosure()
    //     0x8d37fc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d3800: mov             x1, x0
    // 0x8d3804: ldur            x0, [fp, #-8]
    // 0x8d3808: StoreField: r0->field_f = r1
    //     0x8d3808: stur            w1, [x0, #0xf]
    // 0x8d380c: r1 = ECDomainParameters
    //     0x8d380c: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d3810: ldr             x1, [x1, #0x6e8]
    // 0x8d3814: StoreField: r0->field_7 = r1
    //     0x8d3814: stur            w1, [x0, #7]
    // 0x8d3818: LeaveFrame
    //     0x8d3818: mov             SP, fp
    //     0x8d381c: ldp             fp, lr, [SP], #0x10
    // 0x8d3820: ret
    //     0x8d3820: ret             
  }
  [closure] static ECCurve_prime192v1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d3824, size: 0x30
    // 0x8d3824: EnterFrame
    //     0x8d3824: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3828: mov             fp, SP
    // 0x8d382c: CheckStackOverflow
    //     0x8d382c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3830: cmp             SP, x16
    //     0x8d3834: b.ls            #0x8d384c
    // 0x8d3838: r1 = Null
    //     0x8d3838: mov             x1, NULL
    // 0x8d383c: r0 = ECCurve_prime192v1()
    //     0x8d383c: bl              #0x8d3854  ; [package:pointycastle/ecc/curves/prime192v1.dart] ECCurve_prime192v1::ECCurve_prime192v1
    // 0x8d3840: LeaveFrame
    //     0x8d3840: mov             SP, fp
    //     0x8d3844: ldp             fp, lr, [SP], #0x10
    // 0x8d3848: ret
    //     0x8d3848: ret             
    // 0x8d384c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d384c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3850: b               #0x8d3838
  }
  factory ECCurve_prime192v1 ECCurve_prime192v1(dynamic) {
    // ** addr: 0x8d3854, size: 0xe8
    // 0x8d3854: EnterFrame
    //     0x8d3854: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3858: mov             fp, SP
    // 0x8d385c: AllocStack(0x48)
    //     0x8d385c: sub             SP, SP, #0x48
    // 0x8d3860: CheckStackOverflow
    //     0x8d3860: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3864: cmp             SP, x16
    //     0x8d3868: b.ls            #0x8d3934
    // 0x8d386c: r1 = "fffffffffffffffffffffffffffffffeffffffffffffffff"
    //     0x8d386c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b28] "fffffffffffffffffffffffffffffffeffffffffffffffff"
    //     0x8d3870: ldr             x1, [x1, #0xb28]
    // 0x8d3874: r2 = 32
    //     0x8d3874: movz            x2, #0x20
    // 0x8d3878: r0 = parse()
    //     0x8d3878: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d387c: r1 = "fffffffffffffffffffffffffffffffefffffffffffffffc"
    //     0x8d387c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b30] "fffffffffffffffffffffffffffffffefffffffffffffffc"
    //     0x8d3880: ldr             x1, [x1, #0xb30]
    // 0x8d3884: r2 = 32
    //     0x8d3884: movz            x2, #0x20
    // 0x8d3888: stur            x0, [fp, #-8]
    // 0x8d388c: r0 = parse()
    //     0x8d388c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3890: r1 = "64210519e59c80e70fa7e9ab72243049feb8deecc146b9b1"
    //     0x8d3890: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b38] "64210519e59c80e70fa7e9ab72243049feb8deecc146b9b1"
    //     0x8d3894: ldr             x1, [x1, #0xb38]
    // 0x8d3898: r2 = 32
    //     0x8d3898: movz            x2, #0x20
    // 0x8d389c: stur            x0, [fp, #-0x10]
    // 0x8d38a0: r0 = parse()
    //     0x8d38a0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d38a4: r1 = "03188da80eb03090f67cbf20eb43a18800f4ff0afd82ff1012"
    //     0x8d38a4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ec0] "03188da80eb03090f67cbf20eb43a18800f4ff0afd82ff1012"
    //     0x8d38a8: ldr             x1, [x1, #0xec0]
    // 0x8d38ac: r2 = 32
    //     0x8d38ac: movz            x2, #0x20
    // 0x8d38b0: stur            x0, [fp, #-0x18]
    // 0x8d38b4: r0 = parse()
    //     0x8d38b4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d38b8: r1 = "ffffffffffffffffffffffff99def836146bc9b1b4d22831"
    //     0x8d38b8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b48] "ffffffffffffffffffffffff99def836146bc9b1b4d22831"
    //     0x8d38bc: ldr             x1, [x1, #0xb48]
    // 0x8d38c0: r2 = 32
    //     0x8d38c0: movz            x2, #0x20
    // 0x8d38c4: stur            x0, [fp, #-0x20]
    // 0x8d38c8: r0 = parse()
    //     0x8d38c8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d38cc: r1 = "1"
    //     0x8d38cc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d38d0: ldr             x1, [x1, #0x718]
    // 0x8d38d4: r2 = 32
    //     0x8d38d4: movz            x2, #0x20
    // 0x8d38d8: stur            x0, [fp, #-0x28]
    // 0x8d38dc: r0 = parse()
    //     0x8d38dc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d38e0: r1 = "3045ae6fc8422f64ed579528d38120eae12196d5"
    //     0x8d38e0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b50] "3045ae6fc8422f64ed579528d38120eae12196d5"
    //     0x8d38e4: ldr             x1, [x1, #0xb50]
    // 0x8d38e8: r2 = 32
    //     0x8d38e8: movz            x2, #0x20
    // 0x8d38ec: stur            x0, [fp, #-0x30]
    // 0x8d38f0: r0 = parse()
    //     0x8d38f0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d38f4: ldur            x16, [fp, #-0x28]
    // 0x8d38f8: ldur            lr, [fp, #-8]
    // 0x8d38fc: stp             lr, x16, [SP, #8]
    // 0x8d3900: str             x0, [SP]
    // 0x8d3904: ldur            x3, [fp, #-0x10]
    // 0x8d3908: ldur            x5, [fp, #-0x18]
    // 0x8d390c: ldur            x6, [fp, #-0x20]
    // 0x8d3910: ldur            x7, [fp, #-0x30]
    // 0x8d3914: r1 = "prime192v1"
    //     0x8d3914: add             x1, PP, #0x18, lsl #12  ; [pp+0x18eb0] "prime192v1"
    //     0x8d3918: ldr             x1, [x1, #0xeb0]
    // 0x8d391c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime192v1 from Function '_make@988050614': static.
    //     0x8d391c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18ec8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime192v1 from Function '_make@988050614': static. (0x7e54fb2d393c)
    //     0x8d3920: ldr             x2, [x2, #0xec8]
    // 0x8d3924: r0 = constructFpStandardCurve()
    //     0x8d3924: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d3928: LeaveFrame
    //     0x8d3928: mov             SP, fp
    //     0x8d392c: ldp             fp, lr, [SP], #0x10
    // 0x8d3930: ret
    //     0x8d3930: ret             
    // 0x8d3934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3938: b               #0x8d386c
  }
  [closure] static ECCurve_prime192v1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d393c, size: 0x20
    // 0x8d393c: EnterFrame
    //     0x8d393c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3940: mov             fp, SP
    // 0x8d3944: r0 = ECCurve_prime192v1()
    //     0x8d3944: bl              #0x8d395c  ; AllocateECCurve_prime192v1Stub -> ECCurve_prime192v1 (size=0xc)
    // 0x8d3948: ldr             x1, [fp, #0x18]
    // 0x8d394c: StoreField: r0->field_7 = r1
    //     0x8d394c: stur            w1, [x0, #7]
    // 0x8d3950: LeaveFrame
    //     0x8d3950: mov             SP, fp
    //     0x8d3954: ldp             fp, lr, [SP], #0x10
    // 0x8d3958: ret
    //     0x8d3958: ret             
  }
}
