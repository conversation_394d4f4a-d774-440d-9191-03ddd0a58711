// lib: impl.ec_domain_parameters.prime192v2, url: package:pointycastle/ecc/curves/prime192v2.dart

// class id: 1050983, size: 0x8
class :: {
}

// class id: 619, size: 0xc, field offset: 0xc
class ECCurve_prime192v2 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf04

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d3630, size: 0x58
    // 0x8d3630: EnterFrame
    //     0x8d3630: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3634: mov             fp, SP
    // 0x8d3638: AllocStack(0x8)
    //     0x8d3638: sub             SP, SP, #8
    // 0x8d363c: r0 = StaticFactoryConfig()
    //     0x8d363c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d3640: mov             x3, x0
    // 0x8d3644: r0 = "prime192v2"
    //     0x8d3644: add             x0, PP, #0x18, lsl #12  ; [pp+0x18e78] "prime192v2"
    //     0x8d3648: ldr             x0, [x0, #0xe78]
    // 0x8d364c: stur            x3, [fp, #-8]
    // 0x8d3650: StoreField: r3->field_b = r0
    //     0x8d3650: stur            w0, [x3, #0xb]
    // 0x8d3654: r1 = Function '<anonymous closure>': static.
    //     0x8d3654: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e80] AnonymousClosure: static (0x8d3688), in [package:pointycastle/ecc/curves/prime192v2.dart] ECCurve_prime192v2::factoryConfig (0x8d3630)
    //     0x8d3658: ldr             x1, [x1, #0xe80]
    // 0x8d365c: r2 = Null
    //     0x8d365c: mov             x2, NULL
    // 0x8d3660: r0 = AllocateClosure()
    //     0x8d3660: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d3664: mov             x1, x0
    // 0x8d3668: ldur            x0, [fp, #-8]
    // 0x8d366c: StoreField: r0->field_f = r1
    //     0x8d366c: stur            w1, [x0, #0xf]
    // 0x8d3670: r1 = ECDomainParameters
    //     0x8d3670: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d3674: ldr             x1, [x1, #0x6e8]
    // 0x8d3678: StoreField: r0->field_7 = r1
    //     0x8d3678: stur            w1, [x0, #7]
    // 0x8d367c: LeaveFrame
    //     0x8d367c: mov             SP, fp
    //     0x8d3680: ldp             fp, lr, [SP], #0x10
    // 0x8d3684: ret
    //     0x8d3684: ret             
  }
  [closure] static ECCurve_prime192v2 <anonymous closure>(dynamic) {
    // ** addr: 0x8d3688, size: 0x30
    // 0x8d3688: EnterFrame
    //     0x8d3688: stp             fp, lr, [SP, #-0x10]!
    //     0x8d368c: mov             fp, SP
    // 0x8d3690: CheckStackOverflow
    //     0x8d3690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3694: cmp             SP, x16
    //     0x8d3698: b.ls            #0x8d36b0
    // 0x8d369c: r1 = Null
    //     0x8d369c: mov             x1, NULL
    // 0x8d36a0: r0 = ECCurve_prime192v2()
    //     0x8d36a0: bl              #0x8d36b8  ; [package:pointycastle/ecc/curves/prime192v2.dart] ECCurve_prime192v2::ECCurve_prime192v2
    // 0x8d36a4: LeaveFrame
    //     0x8d36a4: mov             SP, fp
    //     0x8d36a8: ldp             fp, lr, [SP], #0x10
    // 0x8d36ac: ret
    //     0x8d36ac: ret             
    // 0x8d36b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d36b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d36b4: b               #0x8d369c
  }
  factory ECCurve_prime192v2 ECCurve_prime192v2(dynamic) {
    // ** addr: 0x8d36b8, size: 0xe8
    // 0x8d36b8: EnterFrame
    //     0x8d36b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d36bc: mov             fp, SP
    // 0x8d36c0: AllocStack(0x48)
    //     0x8d36c0: sub             SP, SP, #0x48
    // 0x8d36c4: CheckStackOverflow
    //     0x8d36c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d36c8: cmp             SP, x16
    //     0x8d36cc: b.ls            #0x8d3798
    // 0x8d36d0: r1 = "fffffffffffffffffffffffffffffffeffffffffffffffff"
    //     0x8d36d0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b28] "fffffffffffffffffffffffffffffffeffffffffffffffff"
    //     0x8d36d4: ldr             x1, [x1, #0xb28]
    // 0x8d36d8: r2 = 32
    //     0x8d36d8: movz            x2, #0x20
    // 0x8d36dc: r0 = parse()
    //     0x8d36dc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d36e0: r1 = "fffffffffffffffffffffffffffffffefffffffffffffffc"
    //     0x8d36e0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b30] "fffffffffffffffffffffffffffffffefffffffffffffffc"
    //     0x8d36e4: ldr             x1, [x1, #0xb30]
    // 0x8d36e8: r2 = 32
    //     0x8d36e8: movz            x2, #0x20
    // 0x8d36ec: stur            x0, [fp, #-8]
    // 0x8d36f0: r0 = parse()
    //     0x8d36f0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d36f4: r1 = "cc22d6dfb95c6b25e49c0d6364a4e5980c393aa21668d953"
    //     0x8d36f4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e88] "cc22d6dfb95c6b25e49c0d6364a4e5980c393aa21668d953"
    //     0x8d36f8: ldr             x1, [x1, #0xe88]
    // 0x8d36fc: r2 = 32
    //     0x8d36fc: movz            x2, #0x20
    // 0x8d3700: stur            x0, [fp, #-0x10]
    // 0x8d3704: r0 = parse()
    //     0x8d3704: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3708: r1 = "03eea2bae7e1497842f2de7769cfe9c989c072ad696f48034a"
    //     0x8d3708: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e90] "03eea2bae7e1497842f2de7769cfe9c989c072ad696f48034a"
    //     0x8d370c: ldr             x1, [x1, #0xe90]
    // 0x8d3710: r2 = 32
    //     0x8d3710: movz            x2, #0x20
    // 0x8d3714: stur            x0, [fp, #-0x18]
    // 0x8d3718: r0 = parse()
    //     0x8d3718: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d371c: r1 = "fffffffffffffffffffffffe5fb1a724dc80418648d8dd31"
    //     0x8d371c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e98] "fffffffffffffffffffffffe5fb1a724dc80418648d8dd31"
    //     0x8d3720: ldr             x1, [x1, #0xe98]
    // 0x8d3724: r2 = 32
    //     0x8d3724: movz            x2, #0x20
    // 0x8d3728: stur            x0, [fp, #-0x20]
    // 0x8d372c: r0 = parse()
    //     0x8d372c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3730: r1 = "1"
    //     0x8d3730: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d3734: ldr             x1, [x1, #0x718]
    // 0x8d3738: r2 = 32
    //     0x8d3738: movz            x2, #0x20
    // 0x8d373c: stur            x0, [fp, #-0x28]
    // 0x8d3740: r0 = parse()
    //     0x8d3740: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3744: r1 = "31a92ee2029fd10d901b113e990710f0d21ac6b6"
    //     0x8d3744: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ea0] "31a92ee2029fd10d901b113e990710f0d21ac6b6"
    //     0x8d3748: ldr             x1, [x1, #0xea0]
    // 0x8d374c: r2 = 32
    //     0x8d374c: movz            x2, #0x20
    // 0x8d3750: stur            x0, [fp, #-0x30]
    // 0x8d3754: r0 = parse()
    //     0x8d3754: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3758: ldur            x16, [fp, #-0x28]
    // 0x8d375c: ldur            lr, [fp, #-8]
    // 0x8d3760: stp             lr, x16, [SP, #8]
    // 0x8d3764: str             x0, [SP]
    // 0x8d3768: ldur            x3, [fp, #-0x10]
    // 0x8d376c: ldur            x5, [fp, #-0x18]
    // 0x8d3770: ldur            x6, [fp, #-0x20]
    // 0x8d3774: ldur            x7, [fp, #-0x30]
    // 0x8d3778: r1 = "prime192v2"
    //     0x8d3778: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e78] "prime192v2"
    //     0x8d377c: ldr             x1, [x1, #0xe78]
    // 0x8d3780: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime192v2 from Function '_make@989358284': static.
    //     0x8d3780: add             x2, PP, #0x18, lsl #12  ; [pp+0x18ea8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime192v2 from Function '_make@989358284': static. (0x7e54fb2d37a0)
    //     0x8d3784: ldr             x2, [x2, #0xea8]
    // 0x8d3788: r0 = constructFpStandardCurve()
    //     0x8d3788: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d378c: LeaveFrame
    //     0x8d378c: mov             SP, fp
    //     0x8d3790: ldp             fp, lr, [SP], #0x10
    // 0x8d3794: ret
    //     0x8d3794: ret             
    // 0x8d3798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3798: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d379c: b               #0x8d36d0
  }
  [closure] static ECCurve_prime192v2 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d37a0, size: 0x20
    // 0x8d37a0: EnterFrame
    //     0x8d37a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d37a4: mov             fp, SP
    // 0x8d37a8: r0 = ECCurve_prime192v2()
    //     0x8d37a8: bl              #0x8d37c0  ; AllocateECCurve_prime192v2Stub -> ECCurve_prime192v2 (size=0xc)
    // 0x8d37ac: ldr             x1, [fp, #0x18]
    // 0x8d37b0: StoreField: r0->field_7 = r1
    //     0x8d37b0: stur            w1, [x0, #7]
    // 0x8d37b4: LeaveFrame
    //     0x8d37b4: mov             SP, fp
    //     0x8d37b8: ldp             fp, lr, [SP], #0x10
    // 0x8d37bc: ret
    //     0x8d37bc: ret             
  }
}
