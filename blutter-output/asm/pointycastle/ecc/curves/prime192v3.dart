// lib: impl.ec_domain_parameters.prime192v3, url: package:pointycastle/ecc/curves/prime192v3.dart

// class id: 1050984, size: 0x8
class :: {
}

// class id: 618, size: 0xc, field offset: 0xc
class ECCurve_prime192v3 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf08

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d3494, size: 0x58
    // 0x8d3494: EnterFrame
    //     0x8d3494: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3498: mov             fp, SP
    // 0x8d349c: AllocStack(0x8)
    //     0x8d349c: sub             SP, SP, #8
    // 0x8d34a0: r0 = StaticFactoryConfig()
    //     0x8d34a0: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d34a4: mov             x3, x0
    // 0x8d34a8: r0 = "prime192v3"
    //     0x8d34a8: add             x0, PP, #0x18, lsl #12  ; [pp+0x18e40] "prime192v3"
    //     0x8d34ac: ldr             x0, [x0, #0xe40]
    // 0x8d34b0: stur            x3, [fp, #-8]
    // 0x8d34b4: StoreField: r3->field_b = r0
    //     0x8d34b4: stur            w0, [x3, #0xb]
    // 0x8d34b8: r1 = Function '<anonymous closure>': static.
    //     0x8d34b8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e48] AnonymousClosure: static (0x8d34ec), in [package:pointycastle/ecc/curves/prime192v3.dart] ECCurve_prime192v3::factoryConfig (0x8d3494)
    //     0x8d34bc: ldr             x1, [x1, #0xe48]
    // 0x8d34c0: r2 = Null
    //     0x8d34c0: mov             x2, NULL
    // 0x8d34c4: r0 = AllocateClosure()
    //     0x8d34c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d34c8: mov             x1, x0
    // 0x8d34cc: ldur            x0, [fp, #-8]
    // 0x8d34d0: StoreField: r0->field_f = r1
    //     0x8d34d0: stur            w1, [x0, #0xf]
    // 0x8d34d4: r1 = ECDomainParameters
    //     0x8d34d4: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d34d8: ldr             x1, [x1, #0x6e8]
    // 0x8d34dc: StoreField: r0->field_7 = r1
    //     0x8d34dc: stur            w1, [x0, #7]
    // 0x8d34e0: LeaveFrame
    //     0x8d34e0: mov             SP, fp
    //     0x8d34e4: ldp             fp, lr, [SP], #0x10
    // 0x8d34e8: ret
    //     0x8d34e8: ret             
  }
  [closure] static ECCurve_prime192v3 <anonymous closure>(dynamic) {
    // ** addr: 0x8d34ec, size: 0x30
    // 0x8d34ec: EnterFrame
    //     0x8d34ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8d34f0: mov             fp, SP
    // 0x8d34f4: CheckStackOverflow
    //     0x8d34f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d34f8: cmp             SP, x16
    //     0x8d34fc: b.ls            #0x8d3514
    // 0x8d3500: r1 = Null
    //     0x8d3500: mov             x1, NULL
    // 0x8d3504: r0 = ECCurve_prime192v3()
    //     0x8d3504: bl              #0x8d351c  ; [package:pointycastle/ecc/curves/prime192v3.dart] ECCurve_prime192v3::ECCurve_prime192v3
    // 0x8d3508: LeaveFrame
    //     0x8d3508: mov             SP, fp
    //     0x8d350c: ldp             fp, lr, [SP], #0x10
    // 0x8d3510: ret
    //     0x8d3510: ret             
    // 0x8d3514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3514: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3518: b               #0x8d3500
  }
  factory ECCurve_prime192v3 ECCurve_prime192v3(dynamic) {
    // ** addr: 0x8d351c, size: 0xe8
    // 0x8d351c: EnterFrame
    //     0x8d351c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3520: mov             fp, SP
    // 0x8d3524: AllocStack(0x48)
    //     0x8d3524: sub             SP, SP, #0x48
    // 0x8d3528: CheckStackOverflow
    //     0x8d3528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d352c: cmp             SP, x16
    //     0x8d3530: b.ls            #0x8d35fc
    // 0x8d3534: r1 = "fffffffffffffffffffffffffffffffeffffffffffffffff"
    //     0x8d3534: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b28] "fffffffffffffffffffffffffffffffeffffffffffffffff"
    //     0x8d3538: ldr             x1, [x1, #0xb28]
    // 0x8d353c: r2 = 32
    //     0x8d353c: movz            x2, #0x20
    // 0x8d3540: r0 = parse()
    //     0x8d3540: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3544: r1 = "fffffffffffffffffffffffffffffffefffffffffffffffc"
    //     0x8d3544: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b30] "fffffffffffffffffffffffffffffffefffffffffffffffc"
    //     0x8d3548: ldr             x1, [x1, #0xb30]
    // 0x8d354c: r2 = 32
    //     0x8d354c: movz            x2, #0x20
    // 0x8d3550: stur            x0, [fp, #-8]
    // 0x8d3554: r0 = parse()
    //     0x8d3554: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3558: r1 = "22123dc2395a05caa7423daeccc94760a7d462256bd56916"
    //     0x8d3558: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e50] "22123dc2395a05caa7423daeccc94760a7d462256bd56916"
    //     0x8d355c: ldr             x1, [x1, #0xe50]
    // 0x8d3560: r2 = 32
    //     0x8d3560: movz            x2, #0x20
    // 0x8d3564: stur            x0, [fp, #-0x10]
    // 0x8d3568: r0 = parse()
    //     0x8d3568: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d356c: r1 = "027d29778100c65a1da1783716588dce2b8b4aee8e228f1896"
    //     0x8d356c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e58] "027d29778100c65a1da1783716588dce2b8b4aee8e228f1896"
    //     0x8d3570: ldr             x1, [x1, #0xe58]
    // 0x8d3574: r2 = 32
    //     0x8d3574: movz            x2, #0x20
    // 0x8d3578: stur            x0, [fp, #-0x18]
    // 0x8d357c: r0 = parse()
    //     0x8d357c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3580: r1 = "ffffffffffffffffffffffff7a62d031c83f4294f640ec13"
    //     0x8d3580: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e60] "ffffffffffffffffffffffff7a62d031c83f4294f640ec13"
    //     0x8d3584: ldr             x1, [x1, #0xe60]
    // 0x8d3588: r2 = 32
    //     0x8d3588: movz            x2, #0x20
    // 0x8d358c: stur            x0, [fp, #-0x20]
    // 0x8d3590: r0 = parse()
    //     0x8d3590: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3594: r1 = "1"
    //     0x8d3594: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d3598: ldr             x1, [x1, #0x718]
    // 0x8d359c: r2 = 32
    //     0x8d359c: movz            x2, #0x20
    // 0x8d35a0: stur            x0, [fp, #-0x28]
    // 0x8d35a4: r0 = parse()
    //     0x8d35a4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d35a8: r1 = "c469684435deb378c4b65ca9591e2a5763059a2e"
    //     0x8d35a8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e68] "c469684435deb378c4b65ca9591e2a5763059a2e"
    //     0x8d35ac: ldr             x1, [x1, #0xe68]
    // 0x8d35b0: r2 = 32
    //     0x8d35b0: movz            x2, #0x20
    // 0x8d35b4: stur            x0, [fp, #-0x30]
    // 0x8d35b8: r0 = parse()
    //     0x8d35b8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d35bc: ldur            x16, [fp, #-0x28]
    // 0x8d35c0: ldur            lr, [fp, #-8]
    // 0x8d35c4: stp             lr, x16, [SP, #8]
    // 0x8d35c8: str             x0, [SP]
    // 0x8d35cc: ldur            x3, [fp, #-0x10]
    // 0x8d35d0: ldur            x5, [fp, #-0x18]
    // 0x8d35d4: ldur            x6, [fp, #-0x20]
    // 0x8d35d8: ldur            x7, [fp, #-0x30]
    // 0x8d35dc: r1 = "prime192v3"
    //     0x8d35dc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e40] "prime192v3"
    //     0x8d35e0: ldr             x1, [x1, #0xe40]
    // 0x8d35e4: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime192v3 from Function '_make@990166994': static.
    //     0x8d35e4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18e70] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime192v3 from Function '_make@990166994': static. (0x7e54fb2d3604)
    //     0x8d35e8: ldr             x2, [x2, #0xe70]
    // 0x8d35ec: r0 = constructFpStandardCurve()
    //     0x8d35ec: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d35f0: LeaveFrame
    //     0x8d35f0: mov             SP, fp
    //     0x8d35f4: ldp             fp, lr, [SP], #0x10
    // 0x8d35f8: ret
    //     0x8d35f8: ret             
    // 0x8d35fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d35fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3600: b               #0x8d3534
  }
  [closure] static ECCurve_prime192v3 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d3604, size: 0x20
    // 0x8d3604: EnterFrame
    //     0x8d3604: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3608: mov             fp, SP
    // 0x8d360c: r0 = ECCurve_prime192v3()
    //     0x8d360c: bl              #0x8d3624  ; AllocateECCurve_prime192v3Stub -> ECCurve_prime192v3 (size=0xc)
    // 0x8d3610: ldr             x1, [fp, #0x18]
    // 0x8d3614: StoreField: r0->field_7 = r1
    //     0x8d3614: stur            w1, [x0, #7]
    // 0x8d3618: LeaveFrame
    //     0x8d3618: mov             SP, fp
    //     0x8d361c: ldp             fp, lr, [SP], #0x10
    // 0x8d3620: ret
    //     0x8d3620: ret             
  }
}
