// lib: impl.ec_domain_parameters.prime239v1, url: package:pointycastle/ecc/curves/prime239v1.dart

// class id: 1050985, size: 0x8
class :: {
}

// class id: 617, size: 0xc, field offset: 0xc
class ECCurve_prime239v1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf0c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d32f8, size: 0x58
    // 0x8d32f8: EnterFrame
    //     0x8d32f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d32fc: mov             fp, SP
    // 0x8d3300: AllocStack(0x8)
    //     0x8d3300: sub             SP, SP, #8
    // 0x8d3304: r0 = StaticFactoryConfig()
    //     0x8d3304: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d3308: mov             x3, x0
    // 0x8d330c: r0 = "prime239v1"
    //     0x8d330c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18e08] "prime239v1"
    //     0x8d3310: ldr             x0, [x0, #0xe08]
    // 0x8d3314: stur            x3, [fp, #-8]
    // 0x8d3318: StoreField: r3->field_b = r0
    //     0x8d3318: stur            w0, [x3, #0xb]
    // 0x8d331c: r1 = Function '<anonymous closure>': static.
    //     0x8d331c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e10] AnonymousClosure: static (0x8d3350), in [package:pointycastle/ecc/curves/prime239v1.dart] ECCurve_prime239v1::factoryConfig (0x8d32f8)
    //     0x8d3320: ldr             x1, [x1, #0xe10]
    // 0x8d3324: r2 = Null
    //     0x8d3324: mov             x2, NULL
    // 0x8d3328: r0 = AllocateClosure()
    //     0x8d3328: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d332c: mov             x1, x0
    // 0x8d3330: ldur            x0, [fp, #-8]
    // 0x8d3334: StoreField: r0->field_f = r1
    //     0x8d3334: stur            w1, [x0, #0xf]
    // 0x8d3338: r1 = ECDomainParameters
    //     0x8d3338: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d333c: ldr             x1, [x1, #0x6e8]
    // 0x8d3340: StoreField: r0->field_7 = r1
    //     0x8d3340: stur            w1, [x0, #7]
    // 0x8d3344: LeaveFrame
    //     0x8d3344: mov             SP, fp
    //     0x8d3348: ldp             fp, lr, [SP], #0x10
    // 0x8d334c: ret
    //     0x8d334c: ret             
  }
  [closure] static ECCurve_prime239v1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d3350, size: 0x30
    // 0x8d3350: EnterFrame
    //     0x8d3350: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3354: mov             fp, SP
    // 0x8d3358: CheckStackOverflow
    //     0x8d3358: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d335c: cmp             SP, x16
    //     0x8d3360: b.ls            #0x8d3378
    // 0x8d3364: r1 = Null
    //     0x8d3364: mov             x1, NULL
    // 0x8d3368: r0 = ECCurve_prime239v1()
    //     0x8d3368: bl              #0x8d3380  ; [package:pointycastle/ecc/curves/prime239v1.dart] ECCurve_prime239v1::ECCurve_prime239v1
    // 0x8d336c: LeaveFrame
    //     0x8d336c: mov             SP, fp
    //     0x8d3370: ldp             fp, lr, [SP], #0x10
    // 0x8d3374: ret
    //     0x8d3374: ret             
    // 0x8d3378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d337c: b               #0x8d3364
  }
  factory ECCurve_prime239v1 ECCurve_prime239v1(dynamic) {
    // ** addr: 0x8d3380, size: 0xe8
    // 0x8d3380: EnterFrame
    //     0x8d3380: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3384: mov             fp, SP
    // 0x8d3388: AllocStack(0x48)
    //     0x8d3388: sub             SP, SP, #0x48
    // 0x8d338c: CheckStackOverflow
    //     0x8d338c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3390: cmp             SP, x16
    //     0x8d3394: b.ls            #0x8d3460
    // 0x8d3398: r1 = "7fffffffffffffffffffffff7fffffffffff8000000000007fffffffffff"
    //     0x8d3398: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d98] "7fffffffffffffffffffffff7fffffffffff8000000000007fffffffffff"
    //     0x8d339c: ldr             x1, [x1, #0xd98]
    // 0x8d33a0: r2 = 32
    //     0x8d33a0: movz            x2, #0x20
    // 0x8d33a4: r0 = parse()
    //     0x8d33a4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d33a8: r1 = "7fffffffffffffffffffffff7fffffffffff8000000000007ffffffffffc"
    //     0x8d33a8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18da0] "7fffffffffffffffffffffff7fffffffffff8000000000007ffffffffffc"
    //     0x8d33ac: ldr             x1, [x1, #0xda0]
    // 0x8d33b0: r2 = 32
    //     0x8d33b0: movz            x2, #0x20
    // 0x8d33b4: stur            x0, [fp, #-8]
    // 0x8d33b8: r0 = parse()
    //     0x8d33b8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d33bc: r1 = "6b016c3bdcf18941d0d654921475ca71a9db2fb27d1d37796185c2942c0a"
    //     0x8d33bc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e18] "6b016c3bdcf18941d0d654921475ca71a9db2fb27d1d37796185c2942c0a"
    //     0x8d33c0: ldr             x1, [x1, #0xe18]
    // 0x8d33c4: r2 = 32
    //     0x8d33c4: movz            x2, #0x20
    // 0x8d33c8: stur            x0, [fp, #-0x10]
    // 0x8d33cc: r0 = parse()
    //     0x8d33cc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d33d0: r1 = "020ffa963cdca8816ccc33b8642bedf905c3d358573d3f27fbbd3b3cb9aaaf"
    //     0x8d33d0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e20] "020ffa963cdca8816ccc33b8642bedf905c3d358573d3f27fbbd3b3cb9aaaf"
    //     0x8d33d4: ldr             x1, [x1, #0xe20]
    // 0x8d33d8: r2 = 32
    //     0x8d33d8: movz            x2, #0x20
    // 0x8d33dc: stur            x0, [fp, #-0x18]
    // 0x8d33e0: r0 = parse()
    //     0x8d33e0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d33e4: r1 = "7fffffffffffffffffffffff7fffff9e5e9a9f5d9071fbd1522688909d0b"
    //     0x8d33e4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e28] "7fffffffffffffffffffffff7fffff9e5e9a9f5d9071fbd1522688909d0b"
    //     0x8d33e8: ldr             x1, [x1, #0xe28]
    // 0x8d33ec: r2 = 32
    //     0x8d33ec: movz            x2, #0x20
    // 0x8d33f0: stur            x0, [fp, #-0x20]
    // 0x8d33f4: r0 = parse()
    //     0x8d33f4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d33f8: r1 = "1"
    //     0x8d33f8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d33fc: ldr             x1, [x1, #0x718]
    // 0x8d3400: r2 = 32
    //     0x8d3400: movz            x2, #0x20
    // 0x8d3404: stur            x0, [fp, #-0x28]
    // 0x8d3408: r0 = parse()
    //     0x8d3408: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d340c: r1 = "e43bb460f0b80cc0c0b075798e948060f8321b7d"
    //     0x8d340c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e30] "e43bb460f0b80cc0c0b075798e948060f8321b7d"
    //     0x8d3410: ldr             x1, [x1, #0xe30]
    // 0x8d3414: r2 = 32
    //     0x8d3414: movz            x2, #0x20
    // 0x8d3418: stur            x0, [fp, #-0x30]
    // 0x8d341c: r0 = parse()
    //     0x8d341c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3420: ldur            x16, [fp, #-0x28]
    // 0x8d3424: ldur            lr, [fp, #-8]
    // 0x8d3428: stp             lr, x16, [SP, #8]
    // 0x8d342c: str             x0, [SP]
    // 0x8d3430: ldur            x3, [fp, #-0x10]
    // 0x8d3434: ldur            x5, [fp, #-0x18]
    // 0x8d3438: ldur            x6, [fp, #-0x20]
    // 0x8d343c: ldur            x7, [fp, #-0x30]
    // 0x8d3440: r1 = "prime239v1"
    //     0x8d3440: add             x1, PP, #0x18, lsl #12  ; [pp+0x18e08] "prime239v1"
    //     0x8d3444: ldr             x1, [x1, #0xe08]
    // 0x8d3448: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime239v1 from Function '_make@991209703': static.
    //     0x8d3448: add             x2, PP, #0x18, lsl #12  ; [pp+0x18e38] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime239v1 from Function '_make@991209703': static. (0x7e54fb2d3468)
    //     0x8d344c: ldr             x2, [x2, #0xe38]
    // 0x8d3450: r0 = constructFpStandardCurve()
    //     0x8d3450: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d3454: LeaveFrame
    //     0x8d3454: mov             SP, fp
    //     0x8d3458: ldp             fp, lr, [SP], #0x10
    // 0x8d345c: ret
    //     0x8d345c: ret             
    // 0x8d3460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3460: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3464: b               #0x8d3398
  }
  [closure] static ECCurve_prime239v1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d3468, size: 0x20
    // 0x8d3468: EnterFrame
    //     0x8d3468: stp             fp, lr, [SP, #-0x10]!
    //     0x8d346c: mov             fp, SP
    // 0x8d3470: r0 = ECCurve_prime239v1()
    //     0x8d3470: bl              #0x8d3488  ; AllocateECCurve_prime239v1Stub -> ECCurve_prime239v1 (size=0xc)
    // 0x8d3474: ldr             x1, [fp, #0x18]
    // 0x8d3478: StoreField: r0->field_7 = r1
    //     0x8d3478: stur            w1, [x0, #7]
    // 0x8d347c: LeaveFrame
    //     0x8d347c: mov             SP, fp
    //     0x8d3480: ldp             fp, lr, [SP], #0x10
    // 0x8d3484: ret
    //     0x8d3484: ret             
  }
}
