// lib: impl.ec_domain_parameters.prime239v2, url: package:pointycastle/ecc/curves/prime239v2.dart

// class id: 1050986, size: 0x8
class :: {
}

// class id: 616, size: 0xc, field offset: 0xc
class ECCurve_prime239v2 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf10

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d315c, size: 0x58
    // 0x8d315c: EnterFrame
    //     0x8d315c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3160: mov             fp, SP
    // 0x8d3164: AllocStack(0x8)
    //     0x8d3164: sub             SP, SP, #8
    // 0x8d3168: r0 = StaticFactoryConfig()
    //     0x8d3168: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d316c: mov             x3, x0
    // 0x8d3170: r0 = "prime239v2"
    //     0x8d3170: add             x0, PP, #0x18, lsl #12  ; [pp+0x18dd0] "prime239v2"
    //     0x8d3174: ldr             x0, [x0, #0xdd0]
    // 0x8d3178: stur            x3, [fp, #-8]
    // 0x8d317c: StoreField: r3->field_b = r0
    //     0x8d317c: stur            w0, [x3, #0xb]
    // 0x8d3180: r1 = Function '<anonymous closure>': static.
    //     0x8d3180: add             x1, PP, #0x18, lsl #12  ; [pp+0x18dd8] AnonymousClosure: static (0x8d31b4), in [package:pointycastle/ecc/curves/prime239v2.dart] ECCurve_prime239v2::factoryConfig (0x8d315c)
    //     0x8d3184: ldr             x1, [x1, #0xdd8]
    // 0x8d3188: r2 = Null
    //     0x8d3188: mov             x2, NULL
    // 0x8d318c: r0 = AllocateClosure()
    //     0x8d318c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d3190: mov             x1, x0
    // 0x8d3194: ldur            x0, [fp, #-8]
    // 0x8d3198: StoreField: r0->field_f = r1
    //     0x8d3198: stur            w1, [x0, #0xf]
    // 0x8d319c: r1 = ECDomainParameters
    //     0x8d319c: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d31a0: ldr             x1, [x1, #0x6e8]
    // 0x8d31a4: StoreField: r0->field_7 = r1
    //     0x8d31a4: stur            w1, [x0, #7]
    // 0x8d31a8: LeaveFrame
    //     0x8d31a8: mov             SP, fp
    //     0x8d31ac: ldp             fp, lr, [SP], #0x10
    // 0x8d31b0: ret
    //     0x8d31b0: ret             
  }
  [closure] static ECCurve_prime239v2 <anonymous closure>(dynamic) {
    // ** addr: 0x8d31b4, size: 0x30
    // 0x8d31b4: EnterFrame
    //     0x8d31b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d31b8: mov             fp, SP
    // 0x8d31bc: CheckStackOverflow
    //     0x8d31bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d31c0: cmp             SP, x16
    //     0x8d31c4: b.ls            #0x8d31dc
    // 0x8d31c8: r1 = Null
    //     0x8d31c8: mov             x1, NULL
    // 0x8d31cc: r0 = ECCurve_prime239v2()
    //     0x8d31cc: bl              #0x8d31e4  ; [package:pointycastle/ecc/curves/prime239v2.dart] ECCurve_prime239v2::ECCurve_prime239v2
    // 0x8d31d0: LeaveFrame
    //     0x8d31d0: mov             SP, fp
    //     0x8d31d4: ldp             fp, lr, [SP], #0x10
    // 0x8d31d8: ret
    //     0x8d31d8: ret             
    // 0x8d31dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d31dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d31e0: b               #0x8d31c8
  }
  factory ECCurve_prime239v2 ECCurve_prime239v2(dynamic) {
    // ** addr: 0x8d31e4, size: 0xe8
    // 0x8d31e4: EnterFrame
    //     0x8d31e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d31e8: mov             fp, SP
    // 0x8d31ec: AllocStack(0x48)
    //     0x8d31ec: sub             SP, SP, #0x48
    // 0x8d31f0: CheckStackOverflow
    //     0x8d31f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d31f4: cmp             SP, x16
    //     0x8d31f8: b.ls            #0x8d32c4
    // 0x8d31fc: r1 = "7fffffffffffffffffffffff7fffffffffff8000000000007fffffffffff"
    //     0x8d31fc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d98] "7fffffffffffffffffffffff7fffffffffff8000000000007fffffffffff"
    //     0x8d3200: ldr             x1, [x1, #0xd98]
    // 0x8d3204: r2 = 32
    //     0x8d3204: movz            x2, #0x20
    // 0x8d3208: r0 = parse()
    //     0x8d3208: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d320c: r1 = "7fffffffffffffffffffffff7fffffffffff8000000000007ffffffffffc"
    //     0x8d320c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18da0] "7fffffffffffffffffffffff7fffffffffff8000000000007ffffffffffc"
    //     0x8d3210: ldr             x1, [x1, #0xda0]
    // 0x8d3214: r2 = 32
    //     0x8d3214: movz            x2, #0x20
    // 0x8d3218: stur            x0, [fp, #-8]
    // 0x8d321c: r0 = parse()
    //     0x8d321c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3220: r1 = "617fab6832576cbbfed50d99f0249c3fee58b94ba0038c7ae84c8c832f2c"
    //     0x8d3220: add             x1, PP, #0x18, lsl #12  ; [pp+0x18de0] "617fab6832576cbbfed50d99f0249c3fee58b94ba0038c7ae84c8c832f2c"
    //     0x8d3224: ldr             x1, [x1, #0xde0]
    // 0x8d3228: r2 = 32
    //     0x8d3228: movz            x2, #0x20
    // 0x8d322c: stur            x0, [fp, #-0x10]
    // 0x8d3230: r0 = parse()
    //     0x8d3230: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3234: r1 = "0238af09d98727705120c921bb5e9e26296a3cdcf2f35757a0eafd87b830e7"
    //     0x8d3234: add             x1, PP, #0x18, lsl #12  ; [pp+0x18de8] "0238af09d98727705120c921bb5e9e26296a3cdcf2f35757a0eafd87b830e7"
    //     0x8d3238: ldr             x1, [x1, #0xde8]
    // 0x8d323c: r2 = 32
    //     0x8d323c: movz            x2, #0x20
    // 0x8d3240: stur            x0, [fp, #-0x18]
    // 0x8d3244: r0 = parse()
    //     0x8d3244: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3248: r1 = "7fffffffffffffffffffffff800000cfa7e8594377d414c03821bc582063"
    //     0x8d3248: add             x1, PP, #0x18, lsl #12  ; [pp+0x18df0] "7fffffffffffffffffffffff800000cfa7e8594377d414c03821bc582063"
    //     0x8d324c: ldr             x1, [x1, #0xdf0]
    // 0x8d3250: r2 = 32
    //     0x8d3250: movz            x2, #0x20
    // 0x8d3254: stur            x0, [fp, #-0x20]
    // 0x8d3258: r0 = parse()
    //     0x8d3258: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d325c: r1 = "1"
    //     0x8d325c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d3260: ldr             x1, [x1, #0x718]
    // 0x8d3264: r2 = 32
    //     0x8d3264: movz            x2, #0x20
    // 0x8d3268: stur            x0, [fp, #-0x28]
    // 0x8d326c: r0 = parse()
    //     0x8d326c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3270: r1 = "e8b4011604095303ca3b8099982be09fcb9ae616"
    //     0x8d3270: add             x1, PP, #0x18, lsl #12  ; [pp+0x18df8] "e8b4011604095303ca3b8099982be09fcb9ae616"
    //     0x8d3274: ldr             x1, [x1, #0xdf8]
    // 0x8d3278: r2 = 32
    //     0x8d3278: movz            x2, #0x20
    // 0x8d327c: stur            x0, [fp, #-0x30]
    // 0x8d3280: r0 = parse()
    //     0x8d3280: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3284: ldur            x16, [fp, #-0x28]
    // 0x8d3288: ldur            lr, [fp, #-8]
    // 0x8d328c: stp             lr, x16, [SP, #8]
    // 0x8d3290: str             x0, [SP]
    // 0x8d3294: ldur            x3, [fp, #-0x10]
    // 0x8d3298: ldur            x5, [fp, #-0x18]
    // 0x8d329c: ldur            x6, [fp, #-0x20]
    // 0x8d32a0: ldur            x7, [fp, #-0x30]
    // 0x8d32a4: r1 = "prime239v2"
    //     0x8d32a4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18dd0] "prime239v2"
    //     0x8d32a8: ldr             x1, [x1, #0xdd0]
    // 0x8d32ac: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime239v2 from Function '_make@992456944': static.
    //     0x8d32ac: add             x2, PP, #0x18, lsl #12  ; [pp+0x18e00] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime239v2 from Function '_make@992456944': static. (0x7e54fb2d32cc)
    //     0x8d32b0: ldr             x2, [x2, #0xe00]
    // 0x8d32b4: r0 = constructFpStandardCurve()
    //     0x8d32b4: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d32b8: LeaveFrame
    //     0x8d32b8: mov             SP, fp
    //     0x8d32bc: ldp             fp, lr, [SP], #0x10
    // 0x8d32c0: ret
    //     0x8d32c0: ret             
    // 0x8d32c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d32c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d32c8: b               #0x8d31fc
  }
  [closure] static ECCurve_prime239v2 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d32cc, size: 0x20
    // 0x8d32cc: EnterFrame
    //     0x8d32cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d32d0: mov             fp, SP
    // 0x8d32d4: r0 = ECCurve_prime239v2()
    //     0x8d32d4: bl              #0x8d32ec  ; AllocateECCurve_prime239v2Stub -> ECCurve_prime239v2 (size=0xc)
    // 0x8d32d8: ldr             x1, [fp, #0x18]
    // 0x8d32dc: StoreField: r0->field_7 = r1
    //     0x8d32dc: stur            w1, [x0, #7]
    // 0x8d32e0: LeaveFrame
    //     0x8d32e0: mov             SP, fp
    //     0x8d32e4: ldp             fp, lr, [SP], #0x10
    // 0x8d32e8: ret
    //     0x8d32e8: ret             
  }
}
