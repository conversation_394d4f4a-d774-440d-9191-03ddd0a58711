// lib: impl.ec_domain_parameters.prime239v3, url: package:pointycastle/ecc/curves/prime239v3.dart

// class id: 1050987, size: 0x8
class :: {
}

// class id: 615, size: 0xc, field offset: 0xc
class ECCurve_prime239v3 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf14

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d2fc0, size: 0x58
    // 0x8d2fc0: EnterFrame
    //     0x8d2fc0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2fc4: mov             fp, SP
    // 0x8d2fc8: AllocStack(0x8)
    //     0x8d2fc8: sub             SP, SP, #8
    // 0x8d2fcc: r0 = StaticFactoryConfig()
    //     0x8d2fcc: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d2fd0: mov             x3, x0
    // 0x8d2fd4: r0 = "prime239v3"
    //     0x8d2fd4: add             x0, PP, #0x18, lsl #12  ; [pp+0x18d88] "prime239v3"
    //     0x8d2fd8: ldr             x0, [x0, #0xd88]
    // 0x8d2fdc: stur            x3, [fp, #-8]
    // 0x8d2fe0: StoreField: r3->field_b = r0
    //     0x8d2fe0: stur            w0, [x3, #0xb]
    // 0x8d2fe4: r1 = Function '<anonymous closure>': static.
    //     0x8d2fe4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d90] AnonymousClosure: static (0x8d3018), in [package:pointycastle/ecc/curves/prime239v3.dart] ECCurve_prime239v3::factoryConfig (0x8d2fc0)
    //     0x8d2fe8: ldr             x1, [x1, #0xd90]
    // 0x8d2fec: r2 = Null
    //     0x8d2fec: mov             x2, NULL
    // 0x8d2ff0: r0 = AllocateClosure()
    //     0x8d2ff0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d2ff4: mov             x1, x0
    // 0x8d2ff8: ldur            x0, [fp, #-8]
    // 0x8d2ffc: StoreField: r0->field_f = r1
    //     0x8d2ffc: stur            w1, [x0, #0xf]
    // 0x8d3000: r1 = ECDomainParameters
    //     0x8d3000: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d3004: ldr             x1, [x1, #0x6e8]
    // 0x8d3008: StoreField: r0->field_7 = r1
    //     0x8d3008: stur            w1, [x0, #7]
    // 0x8d300c: LeaveFrame
    //     0x8d300c: mov             SP, fp
    //     0x8d3010: ldp             fp, lr, [SP], #0x10
    // 0x8d3014: ret
    //     0x8d3014: ret             
  }
  [closure] static ECCurve_prime239v3 <anonymous closure>(dynamic) {
    // ** addr: 0x8d3018, size: 0x30
    // 0x8d3018: EnterFrame
    //     0x8d3018: stp             fp, lr, [SP, #-0x10]!
    //     0x8d301c: mov             fp, SP
    // 0x8d3020: CheckStackOverflow
    //     0x8d3020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3024: cmp             SP, x16
    //     0x8d3028: b.ls            #0x8d3040
    // 0x8d302c: r1 = Null
    //     0x8d302c: mov             x1, NULL
    // 0x8d3030: r0 = ECCurve_prime239v3()
    //     0x8d3030: bl              #0x8d3048  ; [package:pointycastle/ecc/curves/prime239v3.dart] ECCurve_prime239v3::ECCurve_prime239v3
    // 0x8d3034: LeaveFrame
    //     0x8d3034: mov             SP, fp
    //     0x8d3038: ldp             fp, lr, [SP], #0x10
    // 0x8d303c: ret
    //     0x8d303c: ret             
    // 0x8d3040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3040: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3044: b               #0x8d302c
  }
  factory ECCurve_prime239v3 ECCurve_prime239v3(dynamic) {
    // ** addr: 0x8d3048, size: 0xe8
    // 0x8d3048: EnterFrame
    //     0x8d3048: stp             fp, lr, [SP, #-0x10]!
    //     0x8d304c: mov             fp, SP
    // 0x8d3050: AllocStack(0x48)
    //     0x8d3050: sub             SP, SP, #0x48
    // 0x8d3054: CheckStackOverflow
    //     0x8d3054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3058: cmp             SP, x16
    //     0x8d305c: b.ls            #0x8d3128
    // 0x8d3060: r1 = "7fffffffffffffffffffffff7fffffffffff8000000000007fffffffffff"
    //     0x8d3060: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d98] "7fffffffffffffffffffffff7fffffffffff8000000000007fffffffffff"
    //     0x8d3064: ldr             x1, [x1, #0xd98]
    // 0x8d3068: r2 = 32
    //     0x8d3068: movz            x2, #0x20
    // 0x8d306c: r0 = parse()
    //     0x8d306c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3070: r1 = "7fffffffffffffffffffffff7fffffffffff8000000000007ffffffffffc"
    //     0x8d3070: add             x1, PP, #0x18, lsl #12  ; [pp+0x18da0] "7fffffffffffffffffffffff7fffffffffff8000000000007ffffffffffc"
    //     0x8d3074: ldr             x1, [x1, #0xda0]
    // 0x8d3078: r2 = 32
    //     0x8d3078: movz            x2, #0x20
    // 0x8d307c: stur            x0, [fp, #-8]
    // 0x8d3080: r0 = parse()
    //     0x8d3080: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3084: r1 = "255705fa2a306654b1f4cb03d6a750a30c250102d4988717d9ba15ab6d3e"
    //     0x8d3084: add             x1, PP, #0x18, lsl #12  ; [pp+0x18da8] "255705fa2a306654b1f4cb03d6a750a30c250102d4988717d9ba15ab6d3e"
    //     0x8d3088: ldr             x1, [x1, #0xda8]
    // 0x8d308c: r2 = 32
    //     0x8d308c: movz            x2, #0x20
    // 0x8d3090: stur            x0, [fp, #-0x10]
    // 0x8d3094: r0 = parse()
    //     0x8d3094: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d3098: r1 = "036768ae8e18bb92cfcf005c949aa2c6d94853d0e660bbf854b1c9505fe95a"
    //     0x8d3098: add             x1, PP, #0x18, lsl #12  ; [pp+0x18db0] "036768ae8e18bb92cfcf005c949aa2c6d94853d0e660bbf854b1c9505fe95a"
    //     0x8d309c: ldr             x1, [x1, #0xdb0]
    // 0x8d30a0: r2 = 32
    //     0x8d30a0: movz            x2, #0x20
    // 0x8d30a4: stur            x0, [fp, #-0x18]
    // 0x8d30a8: r0 = parse()
    //     0x8d30a8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d30ac: r1 = "7fffffffffffffffffffffff7fffff975deb41b3a6057c3c432146526551"
    //     0x8d30ac: add             x1, PP, #0x18, lsl #12  ; [pp+0x18db8] "7fffffffffffffffffffffff7fffff975deb41b3a6057c3c432146526551"
    //     0x8d30b0: ldr             x1, [x1, #0xdb8]
    // 0x8d30b4: r2 = 32
    //     0x8d30b4: movz            x2, #0x20
    // 0x8d30b8: stur            x0, [fp, #-0x20]
    // 0x8d30bc: r0 = parse()
    //     0x8d30bc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d30c0: r1 = "1"
    //     0x8d30c0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d30c4: ldr             x1, [x1, #0x718]
    // 0x8d30c8: r2 = 32
    //     0x8d30c8: movz            x2, #0x20
    // 0x8d30cc: stur            x0, [fp, #-0x28]
    // 0x8d30d0: r0 = parse()
    //     0x8d30d0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d30d4: r1 = "7d7374168ffe3471b60a857686a19475d3bfa2ff"
    //     0x8d30d4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18dc0] "7d7374168ffe3471b60a857686a19475d3bfa2ff"
    //     0x8d30d8: ldr             x1, [x1, #0xdc0]
    // 0x8d30dc: r2 = 32
    //     0x8d30dc: movz            x2, #0x20
    // 0x8d30e0: stur            x0, [fp, #-0x30]
    // 0x8d30e4: r0 = parse()
    //     0x8d30e4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d30e8: ldur            x16, [fp, #-0x28]
    // 0x8d30ec: ldur            lr, [fp, #-8]
    // 0x8d30f0: stp             lr, x16, [SP, #8]
    // 0x8d30f4: str             x0, [SP]
    // 0x8d30f8: ldur            x3, [fp, #-0x10]
    // 0x8d30fc: ldur            x5, [fp, #-0x18]
    // 0x8d3100: ldur            x6, [fp, #-0x20]
    // 0x8d3104: ldur            x7, [fp, #-0x30]
    // 0x8d3108: r1 = "prime239v3"
    //     0x8d3108: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d88] "prime239v3"
    //     0x8d310c: ldr             x1, [x1, #0xd88]
    // 0x8d3110: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime239v3 from Function '_make@993315396': static.
    //     0x8d3110: add             x2, PP, #0x18, lsl #12  ; [pp+0x18dc8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime239v3 from Function '_make@993315396': static. (0x7e54fb2d3130)
    //     0x8d3114: ldr             x2, [x2, #0xdc8]
    // 0x8d3118: r0 = constructFpStandardCurve()
    //     0x8d3118: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d311c: LeaveFrame
    //     0x8d311c: mov             SP, fp
    //     0x8d3120: ldp             fp, lr, [SP], #0x10
    // 0x8d3124: ret
    //     0x8d3124: ret             
    // 0x8d3128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3128: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d312c: b               #0x8d3060
  }
  [closure] static ECCurve_prime239v3 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d3130, size: 0x20
    // 0x8d3130: EnterFrame
    //     0x8d3130: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3134: mov             fp, SP
    // 0x8d3138: r0 = ECCurve_prime239v3()
    //     0x8d3138: bl              #0x8d3150  ; AllocateECCurve_prime239v3Stub -> ECCurve_prime239v3 (size=0xc)
    // 0x8d313c: ldr             x1, [fp, #0x18]
    // 0x8d3140: StoreField: r0->field_7 = r1
    //     0x8d3140: stur            w1, [x0, #7]
    // 0x8d3144: LeaveFrame
    //     0x8d3144: mov             SP, fp
    //     0x8d3148: ldp             fp, lr, [SP], #0x10
    // 0x8d314c: ret
    //     0x8d314c: ret             
  }
}
