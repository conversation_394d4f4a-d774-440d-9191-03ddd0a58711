// lib: impl.ec_domain_parameters.prime256v1, url: package:pointycastle/ecc/curves/prime256v1.dart

// class id: 1050988, size: 0x8
class :: {
}

// class id: 614, size: 0xc, field offset: 0xc
class ECCurve_prime256v1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf18

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d2e24, size: 0x58
    // 0x8d2e24: EnterFrame
    //     0x8d2e24: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2e28: mov             fp, SP
    // 0x8d2e2c: AllocStack(0x8)
    //     0x8d2e2c: sub             SP, SP, #8
    // 0x8d2e30: r0 = StaticFactoryConfig()
    //     0x8d2e30: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d2e34: mov             x3, x0
    // 0x8d2e38: r0 = "prime256v1"
    //     0x8d2e38: add             x0, PP, #0x18, lsl #12  ; [pp+0x18d68] "prime256v1"
    //     0x8d2e3c: ldr             x0, [x0, #0xd68]
    // 0x8d2e40: stur            x3, [fp, #-8]
    // 0x8d2e44: StoreField: r3->field_b = r0
    //     0x8d2e44: stur            w0, [x3, #0xb]
    // 0x8d2e48: r1 = Function '<anonymous closure>': static.
    //     0x8d2e48: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d70] AnonymousClosure: static (0x8d2e7c), in [package:pointycastle/ecc/curves/prime256v1.dart] ECCurve_prime256v1::factoryConfig (0x8d2e24)
    //     0x8d2e4c: ldr             x1, [x1, #0xd70]
    // 0x8d2e50: r2 = Null
    //     0x8d2e50: mov             x2, NULL
    // 0x8d2e54: r0 = AllocateClosure()
    //     0x8d2e54: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d2e58: mov             x1, x0
    // 0x8d2e5c: ldur            x0, [fp, #-8]
    // 0x8d2e60: StoreField: r0->field_f = r1
    //     0x8d2e60: stur            w1, [x0, #0xf]
    // 0x8d2e64: r1 = ECDomainParameters
    //     0x8d2e64: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d2e68: ldr             x1, [x1, #0x6e8]
    // 0x8d2e6c: StoreField: r0->field_7 = r1
    //     0x8d2e6c: stur            w1, [x0, #7]
    // 0x8d2e70: LeaveFrame
    //     0x8d2e70: mov             SP, fp
    //     0x8d2e74: ldp             fp, lr, [SP], #0x10
    // 0x8d2e78: ret
    //     0x8d2e78: ret             
  }
  [closure] static ECCurve_prime256v1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d2e7c, size: 0x30
    // 0x8d2e7c: EnterFrame
    //     0x8d2e7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2e80: mov             fp, SP
    // 0x8d2e84: CheckStackOverflow
    //     0x8d2e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2e88: cmp             SP, x16
    //     0x8d2e8c: b.ls            #0x8d2ea4
    // 0x8d2e90: r1 = Null
    //     0x8d2e90: mov             x1, NULL
    // 0x8d2e94: r0 = ECCurve_prime256v1()
    //     0x8d2e94: bl              #0x8d2eac  ; [package:pointycastle/ecc/curves/prime256v1.dart] ECCurve_prime256v1::ECCurve_prime256v1
    // 0x8d2e98: LeaveFrame
    //     0x8d2e98: mov             SP, fp
    //     0x8d2e9c: ldp             fp, lr, [SP], #0x10
    // 0x8d2ea0: ret
    //     0x8d2ea0: ret             
    // 0x8d2ea4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2ea4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2ea8: b               #0x8d2e90
  }
  factory ECCurve_prime256v1 ECCurve_prime256v1(dynamic) {
    // ** addr: 0x8d2eac, size: 0xe8
    // 0x8d2eac: EnterFrame
    //     0x8d2eac: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2eb0: mov             fp, SP
    // 0x8d2eb4: AllocStack(0x48)
    //     0x8d2eb4: sub             SP, SP, #0x48
    // 0x8d2eb8: CheckStackOverflow
    //     0x8d2eb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2ebc: cmp             SP, x16
    //     0x8d2ec0: b.ls            #0x8d2f8c
    // 0x8d2ec4: r1 = "ffffffff00000001000000000000000000000000ffffffffffffffffffffffff"
    //     0x8d2ec4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a28] "ffffffff00000001000000000000000000000000ffffffffffffffffffffffff"
    //     0x8d2ec8: ldr             x1, [x1, #0xa28]
    // 0x8d2ecc: r2 = 32
    //     0x8d2ecc: movz            x2, #0x20
    // 0x8d2ed0: r0 = parse()
    //     0x8d2ed0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2ed4: r1 = "ffffffff00000001000000000000000000000000fffffffffffffffffffffffc"
    //     0x8d2ed4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a30] "ffffffff00000001000000000000000000000000fffffffffffffffffffffffc"
    //     0x8d2ed8: ldr             x1, [x1, #0xa30]
    // 0x8d2edc: r2 = 32
    //     0x8d2edc: movz            x2, #0x20
    // 0x8d2ee0: stur            x0, [fp, #-8]
    // 0x8d2ee4: r0 = parse()
    //     0x8d2ee4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2ee8: r1 = "5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"
    //     0x8d2ee8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a38] "5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"
    //     0x8d2eec: ldr             x1, [x1, #0xa38]
    // 0x8d2ef0: r2 = 32
    //     0x8d2ef0: movz            x2, #0x20
    // 0x8d2ef4: stur            x0, [fp, #-0x10]
    // 0x8d2ef8: r0 = parse()
    //     0x8d2ef8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2efc: r1 = "036b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"
    //     0x8d2efc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d78] "036b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"
    //     0x8d2f00: ldr             x1, [x1, #0xd78]
    // 0x8d2f04: r2 = 32
    //     0x8d2f04: movz            x2, #0x20
    // 0x8d2f08: stur            x0, [fp, #-0x18]
    // 0x8d2f0c: r0 = parse()
    //     0x8d2f0c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2f10: r1 = "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"
    //     0x8d2f10: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a48] "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"
    //     0x8d2f14: ldr             x1, [x1, #0xa48]
    // 0x8d2f18: r2 = 32
    //     0x8d2f18: movz            x2, #0x20
    // 0x8d2f1c: stur            x0, [fp, #-0x20]
    // 0x8d2f20: r0 = parse()
    //     0x8d2f20: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2f24: r1 = "1"
    //     0x8d2f24: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d2f28: ldr             x1, [x1, #0x718]
    // 0x8d2f2c: r2 = 32
    //     0x8d2f2c: movz            x2, #0x20
    // 0x8d2f30: stur            x0, [fp, #-0x28]
    // 0x8d2f34: r0 = parse()
    //     0x8d2f34: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2f38: r1 = "c49d360886e704936a6678e1139d26b7819f7e90"
    //     0x8d2f38: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a50] "c49d360886e704936a6678e1139d26b7819f7e90"
    //     0x8d2f3c: ldr             x1, [x1, #0xa50]
    // 0x8d2f40: r2 = 32
    //     0x8d2f40: movz            x2, #0x20
    // 0x8d2f44: stur            x0, [fp, #-0x30]
    // 0x8d2f48: r0 = parse()
    //     0x8d2f48: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2f4c: ldur            x16, [fp, #-0x28]
    // 0x8d2f50: ldur            lr, [fp, #-8]
    // 0x8d2f54: stp             lr, x16, [SP, #8]
    // 0x8d2f58: str             x0, [SP]
    // 0x8d2f5c: ldur            x3, [fp, #-0x10]
    // 0x8d2f60: ldur            x5, [fp, #-0x18]
    // 0x8d2f64: ldur            x6, [fp, #-0x20]
    // 0x8d2f68: ldur            x7, [fp, #-0x30]
    // 0x8d2f6c: r1 = "prime256v1"
    //     0x8d2f6c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d68] "prime256v1"
    //     0x8d2f70: ldr             x1, [x1, #0xd68]
    // 0x8d2f74: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime256v1 from Function '_make@994383858': static.
    //     0x8d2f74: add             x2, PP, #0x18, lsl #12  ; [pp+0x18d80] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_prime256v1 from Function '_make@994383858': static. (0x7e54fb2d2f94)
    //     0x8d2f78: ldr             x2, [x2, #0xd80]
    // 0x8d2f7c: r0 = constructFpStandardCurve()
    //     0x8d2f7c: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d2f80: LeaveFrame
    //     0x8d2f80: mov             SP, fp
    //     0x8d2f84: ldp             fp, lr, [SP], #0x10
    // 0x8d2f88: ret
    //     0x8d2f88: ret             
    // 0x8d2f8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2f8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2f90: b               #0x8d2ec4
  }
  [closure] static ECCurve_prime256v1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d2f94, size: 0x20
    // 0x8d2f94: EnterFrame
    //     0x8d2f94: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2f98: mov             fp, SP
    // 0x8d2f9c: r0 = ECCurve_prime256v1()
    //     0x8d2f9c: bl              #0x8d2fb4  ; AllocateECCurve_prime256v1Stub -> ECCurve_prime256v1 (size=0xc)
    // 0x8d2fa0: ldr             x1, [fp, #0x18]
    // 0x8d2fa4: StoreField: r0->field_7 = r1
    //     0x8d2fa4: stur            w1, [x0, #7]
    // 0x8d2fa8: LeaveFrame
    //     0x8d2fa8: mov             SP, fp
    //     0x8d2fac: ldp             fp, lr, [SP], #0x10
    // 0x8d2fb0: ret
    //     0x8d2fb0: ret             
  }
}
