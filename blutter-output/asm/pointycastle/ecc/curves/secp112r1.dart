// lib: impl.ec_domain_parameters.secp112r1, url: package:pointycastle/ecc/curves/secp112r1.dart

// class id: 1050989, size: 0x8
class :: {
}

// class id: 613, size: 0xc, field offset: 0xc
class ECCurve_secp112r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf1c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d2c88, size: 0x58
    // 0x8d2c88: EnterFrame
    //     0x8d2c88: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2c8c: mov             fp, SP
    // 0x8d2c90: AllocStack(0x8)
    //     0x8d2c90: sub             SP, SP, #8
    // 0x8d2c94: r0 = StaticFactoryConfig()
    //     0x8d2c94: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d2c98: mov             x3, x0
    // 0x8d2c9c: r0 = "secp112r1"
    //     0x8d2c9c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18d28] "secp112r1"
    //     0x8d2ca0: ldr             x0, [x0, #0xd28]
    // 0x8d2ca4: stur            x3, [fp, #-8]
    // 0x8d2ca8: StoreField: r3->field_b = r0
    //     0x8d2ca8: stur            w0, [x3, #0xb]
    // 0x8d2cac: r1 = Function '<anonymous closure>': static.
    //     0x8d2cac: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d30] AnonymousClosure: static (0x8d2ce0), in [package:pointycastle/ecc/curves/secp112r1.dart] ECCurve_secp112r1::factoryConfig (0x8d2c88)
    //     0x8d2cb0: ldr             x1, [x1, #0xd30]
    // 0x8d2cb4: r2 = Null
    //     0x8d2cb4: mov             x2, NULL
    // 0x8d2cb8: r0 = AllocateClosure()
    //     0x8d2cb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d2cbc: mov             x1, x0
    // 0x8d2cc0: ldur            x0, [fp, #-8]
    // 0x8d2cc4: StoreField: r0->field_f = r1
    //     0x8d2cc4: stur            w1, [x0, #0xf]
    // 0x8d2cc8: r1 = ECDomainParameters
    //     0x8d2cc8: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d2ccc: ldr             x1, [x1, #0x6e8]
    // 0x8d2cd0: StoreField: r0->field_7 = r1
    //     0x8d2cd0: stur            w1, [x0, #7]
    // 0x8d2cd4: LeaveFrame
    //     0x8d2cd4: mov             SP, fp
    //     0x8d2cd8: ldp             fp, lr, [SP], #0x10
    // 0x8d2cdc: ret
    //     0x8d2cdc: ret             
  }
  [closure] static ECCurve_secp112r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d2ce0, size: 0x30
    // 0x8d2ce0: EnterFrame
    //     0x8d2ce0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2ce4: mov             fp, SP
    // 0x8d2ce8: CheckStackOverflow
    //     0x8d2ce8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2cec: cmp             SP, x16
    //     0x8d2cf0: b.ls            #0x8d2d08
    // 0x8d2cf4: r1 = Null
    //     0x8d2cf4: mov             x1, NULL
    // 0x8d2cf8: r0 = ECCurve_secp112r1()
    //     0x8d2cf8: bl              #0x8d2d10  ; [package:pointycastle/ecc/curves/secp112r1.dart] ECCurve_secp112r1::ECCurve_secp112r1
    // 0x8d2cfc: LeaveFrame
    //     0x8d2cfc: mov             SP, fp
    //     0x8d2d00: ldp             fp, lr, [SP], #0x10
    // 0x8d2d04: ret
    //     0x8d2d04: ret             
    // 0x8d2d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2d08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2d0c: b               #0x8d2cf4
  }
  factory ECCurve_secp112r1 ECCurve_secp112r1(dynamic) {
    // ** addr: 0x8d2d10, size: 0xe8
    // 0x8d2d10: EnterFrame
    //     0x8d2d10: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2d14: mov             fp, SP
    // 0x8d2d18: AllocStack(0x48)
    //     0x8d2d18: sub             SP, SP, #0x48
    // 0x8d2d1c: CheckStackOverflow
    //     0x8d2d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2d20: cmp             SP, x16
    //     0x8d2d24: b.ls            #0x8d2df0
    // 0x8d2d28: r1 = "db7c2abf62e35e668076bead208b"
    //     0x8d2d28: add             x1, PP, #0x18, lsl #12  ; [pp+0x18cf0] "db7c2abf62e35e668076bead208b"
    //     0x8d2d2c: ldr             x1, [x1, #0xcf0]
    // 0x8d2d30: r2 = 32
    //     0x8d2d30: movz            x2, #0x20
    // 0x8d2d34: r0 = parse()
    //     0x8d2d34: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2d38: r1 = "db7c2abf62e35e668076bead2088"
    //     0x8d2d38: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d38] "db7c2abf62e35e668076bead2088"
    //     0x8d2d3c: ldr             x1, [x1, #0xd38]
    // 0x8d2d40: r2 = 32
    //     0x8d2d40: movz            x2, #0x20
    // 0x8d2d44: stur            x0, [fp, #-8]
    // 0x8d2d48: r0 = parse()
    //     0x8d2d48: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2d4c: r1 = "659ef8ba043916eede8911702b22"
    //     0x8d2d4c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d40] "659ef8ba043916eede8911702b22"
    //     0x8d2d50: ldr             x1, [x1, #0xd40]
    // 0x8d2d54: r2 = 32
    //     0x8d2d54: movz            x2, #0x20
    // 0x8d2d58: stur            x0, [fp, #-0x10]
    // 0x8d2d5c: r0 = parse()
    //     0x8d2d5c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2d60: r1 = "0409487239995a5ee76b55f9c2f098a89ce5af8724c0a23e0e0ff77500"
    //     0x8d2d60: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d48] "0409487239995a5ee76b55f9c2f098a89ce5af8724c0a23e0e0ff77500"
    //     0x8d2d64: ldr             x1, [x1, #0xd48]
    // 0x8d2d68: r2 = 32
    //     0x8d2d68: movz            x2, #0x20
    // 0x8d2d6c: stur            x0, [fp, #-0x18]
    // 0x8d2d70: r0 = parse()
    //     0x8d2d70: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2d74: r1 = "db7c2abf62e35e7628dfac6561c5"
    //     0x8d2d74: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d50] "db7c2abf62e35e7628dfac6561c5"
    //     0x8d2d78: ldr             x1, [x1, #0xd50]
    // 0x8d2d7c: r2 = 32
    //     0x8d2d7c: movz            x2, #0x20
    // 0x8d2d80: stur            x0, [fp, #-0x20]
    // 0x8d2d84: r0 = parse()
    //     0x8d2d84: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2d88: r1 = "1"
    //     0x8d2d88: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d2d8c: ldr             x1, [x1, #0x718]
    // 0x8d2d90: r2 = 32
    //     0x8d2d90: movz            x2, #0x20
    // 0x8d2d94: stur            x0, [fp, #-0x28]
    // 0x8d2d98: r0 = parse()
    //     0x8d2d98: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2d9c: r1 = "00f50b028e4d696e676875615175290472783fb1"
    //     0x8d2d9c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d58] "00f50b028e4d696e676875615175290472783fb1"
    //     0x8d2da0: ldr             x1, [x1, #0xd58]
    // 0x8d2da4: r2 = 32
    //     0x8d2da4: movz            x2, #0x20
    // 0x8d2da8: stur            x0, [fp, #-0x30]
    // 0x8d2dac: r0 = parse()
    //     0x8d2dac: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2db0: ldur            x16, [fp, #-0x28]
    // 0x8d2db4: ldur            lr, [fp, #-8]
    // 0x8d2db8: stp             lr, x16, [SP, #8]
    // 0x8d2dbc: str             x0, [SP]
    // 0x8d2dc0: ldur            x3, [fp, #-0x10]
    // 0x8d2dc4: ldur            x5, [fp, #-0x18]
    // 0x8d2dc8: ldur            x6, [fp, #-0x20]
    // 0x8d2dcc: ldur            x7, [fp, #-0x30]
    // 0x8d2dd0: r1 = "secp112r1"
    //     0x8d2dd0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d28] "secp112r1"
    //     0x8d2dd4: ldr             x1, [x1, #0xd28]
    // 0x8d2dd8: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp112r1 from Function '_make@995485455': static.
    //     0x8d2dd8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18d60] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp112r1 from Function '_make@995485455': static. (0x7e54fb2d2df8)
    //     0x8d2ddc: ldr             x2, [x2, #0xd60]
    // 0x8d2de0: r0 = constructFpStandardCurve()
    //     0x8d2de0: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d2de4: LeaveFrame
    //     0x8d2de4: mov             SP, fp
    //     0x8d2de8: ldp             fp, lr, [SP], #0x10
    // 0x8d2dec: ret
    //     0x8d2dec: ret             
    // 0x8d2df0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2df0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2df4: b               #0x8d2d28
  }
  [closure] static ECCurve_secp112r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d2df8, size: 0x20
    // 0x8d2df8: EnterFrame
    //     0x8d2df8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2dfc: mov             fp, SP
    // 0x8d2e00: r0 = ECCurve_secp112r1()
    //     0x8d2e00: bl              #0x8d2e18  ; AllocateECCurve_secp112r1Stub -> ECCurve_secp112r1 (size=0xc)
    // 0x8d2e04: ldr             x1, [fp, #0x18]
    // 0x8d2e08: StoreField: r0->field_7 = r1
    //     0x8d2e08: stur            w1, [x0, #7]
    // 0x8d2e0c: LeaveFrame
    //     0x8d2e0c: mov             SP, fp
    //     0x8d2e10: ldp             fp, lr, [SP], #0x10
    // 0x8d2e14: ret
    //     0x8d2e14: ret             
  }
}
