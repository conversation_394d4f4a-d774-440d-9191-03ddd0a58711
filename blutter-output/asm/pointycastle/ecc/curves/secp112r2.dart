// lib: impl.ec_domain_parameters.secp112r2, url: package:pointycastle/ecc/curves/secp112r2.dart

// class id: 1050990, size: 0x8
class :: {
}

// class id: 612, size: 0xc, field offset: 0xc
class ECCurve_secp112r2 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf20

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d2aec, size: 0x58
    // 0x8d2aec: EnterFrame
    //     0x8d2aec: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2af0: mov             fp, SP
    // 0x8d2af4: AllocStack(0x8)
    //     0x8d2af4: sub             SP, SP, #8
    // 0x8d2af8: r0 = StaticFactoryConfig()
    //     0x8d2af8: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d2afc: mov             x3, x0
    // 0x8d2b00: r0 = "secp112r2"
    //     0x8d2b00: add             x0, PP, #0x18, lsl #12  ; [pp+0x18ce0] "secp112r2"
    //     0x8d2b04: ldr             x0, [x0, #0xce0]
    // 0x8d2b08: stur            x3, [fp, #-8]
    // 0x8d2b0c: StoreField: r3->field_b = r0
    //     0x8d2b0c: stur            w0, [x3, #0xb]
    // 0x8d2b10: r1 = Function '<anonymous closure>': static.
    //     0x8d2b10: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ce8] AnonymousClosure: static (0x8d2b44), in [package:pointycastle/ecc/curves/secp112r2.dart] ECCurve_secp112r2::factoryConfig (0x8d2aec)
    //     0x8d2b14: ldr             x1, [x1, #0xce8]
    // 0x8d2b18: r2 = Null
    //     0x8d2b18: mov             x2, NULL
    // 0x8d2b1c: r0 = AllocateClosure()
    //     0x8d2b1c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d2b20: mov             x1, x0
    // 0x8d2b24: ldur            x0, [fp, #-8]
    // 0x8d2b28: StoreField: r0->field_f = r1
    //     0x8d2b28: stur            w1, [x0, #0xf]
    // 0x8d2b2c: r1 = ECDomainParameters
    //     0x8d2b2c: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d2b30: ldr             x1, [x1, #0x6e8]
    // 0x8d2b34: StoreField: r0->field_7 = r1
    //     0x8d2b34: stur            w1, [x0, #7]
    // 0x8d2b38: LeaveFrame
    //     0x8d2b38: mov             SP, fp
    //     0x8d2b3c: ldp             fp, lr, [SP], #0x10
    // 0x8d2b40: ret
    //     0x8d2b40: ret             
  }
  [closure] static ECCurve_secp112r2 <anonymous closure>(dynamic) {
    // ** addr: 0x8d2b44, size: 0x30
    // 0x8d2b44: EnterFrame
    //     0x8d2b44: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2b48: mov             fp, SP
    // 0x8d2b4c: CheckStackOverflow
    //     0x8d2b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2b50: cmp             SP, x16
    //     0x8d2b54: b.ls            #0x8d2b6c
    // 0x8d2b58: r1 = Null
    //     0x8d2b58: mov             x1, NULL
    // 0x8d2b5c: r0 = ECCurve_secp112r2()
    //     0x8d2b5c: bl              #0x8d2b74  ; [package:pointycastle/ecc/curves/secp112r2.dart] ECCurve_secp112r2::ECCurve_secp112r2
    // 0x8d2b60: LeaveFrame
    //     0x8d2b60: mov             SP, fp
    //     0x8d2b64: ldp             fp, lr, [SP], #0x10
    // 0x8d2b68: ret
    //     0x8d2b68: ret             
    // 0x8d2b6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2b6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2b70: b               #0x8d2b58
  }
  factory ECCurve_secp112r2 ECCurve_secp112r2(dynamic) {
    // ** addr: 0x8d2b74, size: 0xe8
    // 0x8d2b74: EnterFrame
    //     0x8d2b74: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2b78: mov             fp, SP
    // 0x8d2b7c: AllocStack(0x48)
    //     0x8d2b7c: sub             SP, SP, #0x48
    // 0x8d2b80: CheckStackOverflow
    //     0x8d2b80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2b84: cmp             SP, x16
    //     0x8d2b88: b.ls            #0x8d2c54
    // 0x8d2b8c: r1 = "db7c2abf62e35e668076bead208b"
    //     0x8d2b8c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18cf0] "db7c2abf62e35e668076bead208b"
    //     0x8d2b90: ldr             x1, [x1, #0xcf0]
    // 0x8d2b94: r2 = 32
    //     0x8d2b94: movz            x2, #0x20
    // 0x8d2b98: r0 = parse()
    //     0x8d2b98: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2b9c: r1 = "6127c24c05f38a0aaaf65c0ef02c"
    //     0x8d2b9c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18cf8] "6127c24c05f38a0aaaf65c0ef02c"
    //     0x8d2ba0: ldr             x1, [x1, #0xcf8]
    // 0x8d2ba4: r2 = 32
    //     0x8d2ba4: movz            x2, #0x20
    // 0x8d2ba8: stur            x0, [fp, #-8]
    // 0x8d2bac: r0 = parse()
    //     0x8d2bac: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2bb0: r1 = "51def1815db5ed74fcc34c85d709"
    //     0x8d2bb0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d00] "51def1815db5ed74fcc34c85d709"
    //     0x8d2bb4: ldr             x1, [x1, #0xd00]
    // 0x8d2bb8: r2 = 32
    //     0x8d2bb8: movz            x2, #0x20
    // 0x8d2bbc: stur            x0, [fp, #-0x10]
    // 0x8d2bc0: r0 = parse()
    //     0x8d2bc0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2bc4: r1 = "044ba30ab5e892b4e1649dd0928643adcd46f5882e3747def36e956e97"
    //     0x8d2bc4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d08] "044ba30ab5e892b4e1649dd0928643adcd46f5882e3747def36e956e97"
    //     0x8d2bc8: ldr             x1, [x1, #0xd08]
    // 0x8d2bcc: r2 = 32
    //     0x8d2bcc: movz            x2, #0x20
    // 0x8d2bd0: stur            x0, [fp, #-0x18]
    // 0x8d2bd4: r0 = parse()
    //     0x8d2bd4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2bd8: r1 = "36df0aafd8b8d7597ca10520d04b"
    //     0x8d2bd8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d10] "36df0aafd8b8d7597ca10520d04b"
    //     0x8d2bdc: ldr             x1, [x1, #0xd10]
    // 0x8d2be0: r2 = 32
    //     0x8d2be0: movz            x2, #0x20
    // 0x8d2be4: stur            x0, [fp, #-0x20]
    // 0x8d2be8: r0 = parse()
    //     0x8d2be8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2bec: r1 = "4"
    //     0x8d2bec: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c88] "4"
    //     0x8d2bf0: ldr             x1, [x1, #0xc88]
    // 0x8d2bf4: r2 = 32
    //     0x8d2bf4: movz            x2, #0x20
    // 0x8d2bf8: stur            x0, [fp, #-0x28]
    // 0x8d2bfc: r0 = parse()
    //     0x8d2bfc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2c00: r1 = "002757a1114d696e6768756151755316c05e0bd4"
    //     0x8d2c00: add             x1, PP, #0x18, lsl #12  ; [pp+0x18d18] "002757a1114d696e6768756151755316c05e0bd4"
    //     0x8d2c04: ldr             x1, [x1, #0xd18]
    // 0x8d2c08: r2 = 32
    //     0x8d2c08: movz            x2, #0x20
    // 0x8d2c0c: stur            x0, [fp, #-0x30]
    // 0x8d2c10: r0 = parse()
    //     0x8d2c10: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2c14: ldur            x16, [fp, #-0x28]
    // 0x8d2c18: ldur            lr, [fp, #-8]
    // 0x8d2c1c: stp             lr, x16, [SP, #8]
    // 0x8d2c20: str             x0, [SP]
    // 0x8d2c24: ldur            x3, [fp, #-0x10]
    // 0x8d2c28: ldur            x5, [fp, #-0x18]
    // 0x8d2c2c: ldur            x6, [fp, #-0x20]
    // 0x8d2c30: ldur            x7, [fp, #-0x30]
    // 0x8d2c34: r1 = "secp112r2"
    //     0x8d2c34: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ce0] "secp112r2"
    //     0x8d2c38: ldr             x1, [x1, #0xce0]
    // 0x8d2c3c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp112r2 from Function '_make@996397283': static.
    //     0x8d2c3c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18d20] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp112r2 from Function '_make@996397283': static. (0x7e54fb2d2c5c)
    //     0x8d2c40: ldr             x2, [x2, #0xd20]
    // 0x8d2c44: r0 = constructFpStandardCurve()
    //     0x8d2c44: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d2c48: LeaveFrame
    //     0x8d2c48: mov             SP, fp
    //     0x8d2c4c: ldp             fp, lr, [SP], #0x10
    // 0x8d2c50: ret
    //     0x8d2c50: ret             
    // 0x8d2c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2c54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2c58: b               #0x8d2b8c
  }
  [closure] static ECCurve_secp112r2 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d2c5c, size: 0x20
    // 0x8d2c5c: EnterFrame
    //     0x8d2c5c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2c60: mov             fp, SP
    // 0x8d2c64: r0 = ECCurve_secp112r2()
    //     0x8d2c64: bl              #0x8d2c7c  ; AllocateECCurve_secp112r2Stub -> ECCurve_secp112r2 (size=0xc)
    // 0x8d2c68: ldr             x1, [fp, #0x18]
    // 0x8d2c6c: StoreField: r0->field_7 = r1
    //     0x8d2c6c: stur            w1, [x0, #7]
    // 0x8d2c70: LeaveFrame
    //     0x8d2c70: mov             SP, fp
    //     0x8d2c74: ldp             fp, lr, [SP], #0x10
    // 0x8d2c78: ret
    //     0x8d2c78: ret             
  }
}
