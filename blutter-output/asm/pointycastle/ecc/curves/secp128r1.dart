// lib: impl.ec_domain_parameters.secp128r1, url: package:pointycastle/ecc/curves/secp128r1.dart

// class id: 1050991, size: 0x8
class :: {
}

// class id: 611, size: 0xc, field offset: 0xc
class ECCurve_secp128r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf24

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d2950, size: 0x58
    // 0x8d2950: EnterFrame
    //     0x8d2950: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2954: mov             fp, SP
    // 0x8d2958: AllocStack(0x8)
    //     0x8d2958: sub             SP, SP, #8
    // 0x8d295c: r0 = StaticFactoryConfig()
    //     0x8d295c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d2960: mov             x3, x0
    // 0x8d2964: r0 = "secp128r1"
    //     0x8d2964: add             x0, PP, #0x18, lsl #12  ; [pp+0x18ca0] "secp128r1"
    //     0x8d2968: ldr             x0, [x0, #0xca0]
    // 0x8d296c: stur            x3, [fp, #-8]
    // 0x8d2970: StoreField: r3->field_b = r0
    //     0x8d2970: stur            w0, [x3, #0xb]
    // 0x8d2974: r1 = Function '<anonymous closure>': static.
    //     0x8d2974: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ca8] AnonymousClosure: static (0x8d29a8), in [package:pointycastle/ecc/curves/secp128r1.dart] ECCurve_secp128r1::factoryConfig (0x8d2950)
    //     0x8d2978: ldr             x1, [x1, #0xca8]
    // 0x8d297c: r2 = Null
    //     0x8d297c: mov             x2, NULL
    // 0x8d2980: r0 = AllocateClosure()
    //     0x8d2980: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d2984: mov             x1, x0
    // 0x8d2988: ldur            x0, [fp, #-8]
    // 0x8d298c: StoreField: r0->field_f = r1
    //     0x8d298c: stur            w1, [x0, #0xf]
    // 0x8d2990: r1 = ECDomainParameters
    //     0x8d2990: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d2994: ldr             x1, [x1, #0x6e8]
    // 0x8d2998: StoreField: r0->field_7 = r1
    //     0x8d2998: stur            w1, [x0, #7]
    // 0x8d299c: LeaveFrame
    //     0x8d299c: mov             SP, fp
    //     0x8d29a0: ldp             fp, lr, [SP], #0x10
    // 0x8d29a4: ret
    //     0x8d29a4: ret             
  }
  [closure] static ECCurve_secp128r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d29a8, size: 0x30
    // 0x8d29a8: EnterFrame
    //     0x8d29a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d29ac: mov             fp, SP
    // 0x8d29b0: CheckStackOverflow
    //     0x8d29b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d29b4: cmp             SP, x16
    //     0x8d29b8: b.ls            #0x8d29d0
    // 0x8d29bc: r1 = Null
    //     0x8d29bc: mov             x1, NULL
    // 0x8d29c0: r0 = ECCurve_secp128r1()
    //     0x8d29c0: bl              #0x8d29d8  ; [package:pointycastle/ecc/curves/secp128r1.dart] ECCurve_secp128r1::ECCurve_secp128r1
    // 0x8d29c4: LeaveFrame
    //     0x8d29c4: mov             SP, fp
    //     0x8d29c8: ldp             fp, lr, [SP], #0x10
    // 0x8d29cc: ret
    //     0x8d29cc: ret             
    // 0x8d29d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d29d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d29d4: b               #0x8d29bc
  }
  factory ECCurve_secp128r1 ECCurve_secp128r1(dynamic) {
    // ** addr: 0x8d29d8, size: 0xe8
    // 0x8d29d8: EnterFrame
    //     0x8d29d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d29dc: mov             fp, SP
    // 0x8d29e0: AllocStack(0x48)
    //     0x8d29e0: sub             SP, SP, #0x48
    // 0x8d29e4: CheckStackOverflow
    //     0x8d29e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d29e8: cmp             SP, x16
    //     0x8d29ec: b.ls            #0x8d2ab8
    // 0x8d29f0: r1 = "fffffffdffffffffffffffffffffffff"
    //     0x8d29f0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c60] "fffffffdffffffffffffffffffffffff"
    //     0x8d29f4: ldr             x1, [x1, #0xc60]
    // 0x8d29f8: r2 = 32
    //     0x8d29f8: movz            x2, #0x20
    // 0x8d29fc: r0 = parse()
    //     0x8d29fc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2a00: r1 = "fffffffdfffffffffffffffffffffffc"
    //     0x8d2a00: add             x1, PP, #0x18, lsl #12  ; [pp+0x18cb0] "fffffffdfffffffffffffffffffffffc"
    //     0x8d2a04: ldr             x1, [x1, #0xcb0]
    // 0x8d2a08: r2 = 32
    //     0x8d2a08: movz            x2, #0x20
    // 0x8d2a0c: stur            x0, [fp, #-8]
    // 0x8d2a10: r0 = parse()
    //     0x8d2a10: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2a14: r1 = "e87579c11079f43dd824993c2cee5ed3"
    //     0x8d2a14: add             x1, PP, #0x18, lsl #12  ; [pp+0x18cb8] "e87579c11079f43dd824993c2cee5ed3"
    //     0x8d2a18: ldr             x1, [x1, #0xcb8]
    // 0x8d2a1c: r2 = 32
    //     0x8d2a1c: movz            x2, #0x20
    // 0x8d2a20: stur            x0, [fp, #-0x10]
    // 0x8d2a24: r0 = parse()
    //     0x8d2a24: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2a28: r1 = "04161ff7528b899b2d0c28607ca52c5b86cf5ac8395bafeb13c02da292dded7a83"
    //     0x8d2a28: add             x1, PP, #0x18, lsl #12  ; [pp+0x18cc0] "04161ff7528b899b2d0c28607ca52c5b86cf5ac8395bafeb13c02da292dded7a83"
    //     0x8d2a2c: ldr             x1, [x1, #0xcc0]
    // 0x8d2a30: r2 = 32
    //     0x8d2a30: movz            x2, #0x20
    // 0x8d2a34: stur            x0, [fp, #-0x18]
    // 0x8d2a38: r0 = parse()
    //     0x8d2a38: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2a3c: r1 = "fffffffe0000000075a30d1b9038a115"
    //     0x8d2a3c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18cc8] "fffffffe0000000075a30d1b9038a115"
    //     0x8d2a40: ldr             x1, [x1, #0xcc8]
    // 0x8d2a44: r2 = 32
    //     0x8d2a44: movz            x2, #0x20
    // 0x8d2a48: stur            x0, [fp, #-0x20]
    // 0x8d2a4c: r0 = parse()
    //     0x8d2a4c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2a50: r1 = "1"
    //     0x8d2a50: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d2a54: ldr             x1, [x1, #0x718]
    // 0x8d2a58: r2 = 32
    //     0x8d2a58: movz            x2, #0x20
    // 0x8d2a5c: stur            x0, [fp, #-0x28]
    // 0x8d2a60: r0 = parse()
    //     0x8d2a60: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2a64: r1 = "000e0d4d696e6768756151750cc03a4473d03679"
    //     0x8d2a64: add             x1, PP, #0x18, lsl #12  ; [pp+0x18cd0] "000e0d4d696e6768756151750cc03a4473d03679"
    //     0x8d2a68: ldr             x1, [x1, #0xcd0]
    // 0x8d2a6c: r2 = 32
    //     0x8d2a6c: movz            x2, #0x20
    // 0x8d2a70: stur            x0, [fp, #-0x30]
    // 0x8d2a74: r0 = parse()
    //     0x8d2a74: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2a78: ldur            x16, [fp, #-0x28]
    // 0x8d2a7c: ldur            lr, [fp, #-8]
    // 0x8d2a80: stp             lr, x16, [SP, #8]
    // 0x8d2a84: str             x0, [SP]
    // 0x8d2a88: ldur            x3, [fp, #-0x10]
    // 0x8d2a8c: ldur            x5, [fp, #-0x18]
    // 0x8d2a90: ldur            x6, [fp, #-0x20]
    // 0x8d2a94: ldur            x7, [fp, #-0x30]
    // 0x8d2a98: r1 = "secp128r1"
    //     0x8d2a98: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ca0] "secp128r1"
    //     0x8d2a9c: ldr             x1, [x1, #0xca0]
    // 0x8d2aa0: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp128r1 from Function '_make@997377861': static.
    //     0x8d2aa0: add             x2, PP, #0x18, lsl #12  ; [pp+0x18cd8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp128r1 from Function '_make@997377861': static. (0x7e54fb2d2ac0)
    //     0x8d2aa4: ldr             x2, [x2, #0xcd8]
    // 0x8d2aa8: r0 = constructFpStandardCurve()
    //     0x8d2aa8: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d2aac: LeaveFrame
    //     0x8d2aac: mov             SP, fp
    //     0x8d2ab0: ldp             fp, lr, [SP], #0x10
    // 0x8d2ab4: ret
    //     0x8d2ab4: ret             
    // 0x8d2ab8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2ab8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2abc: b               #0x8d29f0
  }
  [closure] static ECCurve_secp128r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d2ac0, size: 0x20
    // 0x8d2ac0: EnterFrame
    //     0x8d2ac0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2ac4: mov             fp, SP
    // 0x8d2ac8: r0 = ECCurve_secp128r1()
    //     0x8d2ac8: bl              #0x8d2ae0  ; AllocateECCurve_secp128r1Stub -> ECCurve_secp128r1 (size=0xc)
    // 0x8d2acc: ldr             x1, [fp, #0x18]
    // 0x8d2ad0: StoreField: r0->field_7 = r1
    //     0x8d2ad0: stur            w1, [x0, #7]
    // 0x8d2ad4: LeaveFrame
    //     0x8d2ad4: mov             SP, fp
    //     0x8d2ad8: ldp             fp, lr, [SP], #0x10
    // 0x8d2adc: ret
    //     0x8d2adc: ret             
  }
}
