// lib: impl.ec_domain_parameters.secp128r2, url: package:pointycastle/ecc/curves/secp128r2.dart

// class id: 1050992, size: 0x8
class :: {
}

// class id: 610, size: 0xc, field offset: 0xc
class ECCurve_secp128r2 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf28

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d27b4, size: 0x58
    // 0x8d27b4: EnterFrame
    //     0x8d27b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d27b8: mov             fp, SP
    // 0x8d27bc: AllocStack(0x8)
    //     0x8d27bc: sub             SP, SP, #8
    // 0x8d27c0: r0 = StaticFactoryConfig()
    //     0x8d27c0: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d27c4: mov             x3, x0
    // 0x8d27c8: r0 = "secp128r2"
    //     0x8d27c8: add             x0, PP, #0x18, lsl #12  ; [pp+0x18c50] "secp128r2"
    //     0x8d27cc: ldr             x0, [x0, #0xc50]
    // 0x8d27d0: stur            x3, [fp, #-8]
    // 0x8d27d4: StoreField: r3->field_b = r0
    //     0x8d27d4: stur            w0, [x3, #0xb]
    // 0x8d27d8: r1 = Function '<anonymous closure>': static.
    //     0x8d27d8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c58] AnonymousClosure: static (0x8d280c), in [package:pointycastle/ecc/curves/secp128r2.dart] ECCurve_secp128r2::factoryConfig (0x8d27b4)
    //     0x8d27dc: ldr             x1, [x1, #0xc58]
    // 0x8d27e0: r2 = Null
    //     0x8d27e0: mov             x2, NULL
    // 0x8d27e4: r0 = AllocateClosure()
    //     0x8d27e4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d27e8: mov             x1, x0
    // 0x8d27ec: ldur            x0, [fp, #-8]
    // 0x8d27f0: StoreField: r0->field_f = r1
    //     0x8d27f0: stur            w1, [x0, #0xf]
    // 0x8d27f4: r1 = ECDomainParameters
    //     0x8d27f4: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d27f8: ldr             x1, [x1, #0x6e8]
    // 0x8d27fc: StoreField: r0->field_7 = r1
    //     0x8d27fc: stur            w1, [x0, #7]
    // 0x8d2800: LeaveFrame
    //     0x8d2800: mov             SP, fp
    //     0x8d2804: ldp             fp, lr, [SP], #0x10
    // 0x8d2808: ret
    //     0x8d2808: ret             
  }
  [closure] static ECCurve_secp128r2 <anonymous closure>(dynamic) {
    // ** addr: 0x8d280c, size: 0x30
    // 0x8d280c: EnterFrame
    //     0x8d280c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2810: mov             fp, SP
    // 0x8d2814: CheckStackOverflow
    //     0x8d2814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2818: cmp             SP, x16
    //     0x8d281c: b.ls            #0x8d2834
    // 0x8d2820: r1 = Null
    //     0x8d2820: mov             x1, NULL
    // 0x8d2824: r0 = ECCurve_secp128r2()
    //     0x8d2824: bl              #0x8d283c  ; [package:pointycastle/ecc/curves/secp128r2.dart] ECCurve_secp128r2::ECCurve_secp128r2
    // 0x8d2828: LeaveFrame
    //     0x8d2828: mov             SP, fp
    //     0x8d282c: ldp             fp, lr, [SP], #0x10
    // 0x8d2830: ret
    //     0x8d2830: ret             
    // 0x8d2834: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2834: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2838: b               #0x8d2820
  }
  factory ECCurve_secp128r2 ECCurve_secp128r2(dynamic) {
    // ** addr: 0x8d283c, size: 0xe8
    // 0x8d283c: EnterFrame
    //     0x8d283c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2840: mov             fp, SP
    // 0x8d2844: AllocStack(0x48)
    //     0x8d2844: sub             SP, SP, #0x48
    // 0x8d2848: CheckStackOverflow
    //     0x8d2848: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d284c: cmp             SP, x16
    //     0x8d2850: b.ls            #0x8d291c
    // 0x8d2854: r1 = "fffffffdffffffffffffffffffffffff"
    //     0x8d2854: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c60] "fffffffdffffffffffffffffffffffff"
    //     0x8d2858: ldr             x1, [x1, #0xc60]
    // 0x8d285c: r2 = 32
    //     0x8d285c: movz            x2, #0x20
    // 0x8d2860: r0 = parse()
    //     0x8d2860: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2864: r1 = "d6031998d1b3bbfebf59cc9bbff9aee1"
    //     0x8d2864: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c68] "d6031998d1b3bbfebf59cc9bbff9aee1"
    //     0x8d2868: ldr             x1, [x1, #0xc68]
    // 0x8d286c: r2 = 32
    //     0x8d286c: movz            x2, #0x20
    // 0x8d2870: stur            x0, [fp, #-8]
    // 0x8d2874: r0 = parse()
    //     0x8d2874: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2878: r1 = "5eeefca380d02919dc2c6558bb6d8a5d"
    //     0x8d2878: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c70] "5eeefca380d02919dc2c6558bb6d8a5d"
    //     0x8d287c: ldr             x1, [x1, #0xc70]
    // 0x8d2880: r2 = 32
    //     0x8d2880: movz            x2, #0x20
    // 0x8d2884: stur            x0, [fp, #-0x10]
    // 0x8d2888: r0 = parse()
    //     0x8d2888: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d288c: r1 = "047b6aa5d85e572983e6fb32a7cdebc14027b6916a894d3aee7106fe805fc34b44"
    //     0x8d288c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c78] "047b6aa5d85e572983e6fb32a7cdebc14027b6916a894d3aee7106fe805fc34b44"
    //     0x8d2890: ldr             x1, [x1, #0xc78]
    // 0x8d2894: r2 = 32
    //     0x8d2894: movz            x2, #0x20
    // 0x8d2898: stur            x0, [fp, #-0x18]
    // 0x8d289c: r0 = parse()
    //     0x8d289c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d28a0: r1 = "3fffffff7fffffffbe0024720613b5a3"
    //     0x8d28a0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c80] "3fffffff7fffffffbe0024720613b5a3"
    //     0x8d28a4: ldr             x1, [x1, #0xc80]
    // 0x8d28a8: r2 = 32
    //     0x8d28a8: movz            x2, #0x20
    // 0x8d28ac: stur            x0, [fp, #-0x20]
    // 0x8d28b0: r0 = parse()
    //     0x8d28b0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d28b4: r1 = "4"
    //     0x8d28b4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c88] "4"
    //     0x8d28b8: ldr             x1, [x1, #0xc88]
    // 0x8d28bc: r2 = 32
    //     0x8d28bc: movz            x2, #0x20
    // 0x8d28c0: stur            x0, [fp, #-0x28]
    // 0x8d28c4: r0 = parse()
    //     0x8d28c4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d28c8: r1 = "004d696e67687561517512d8f03431fce63b88f4"
    //     0x8d28c8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c90] "004d696e67687561517512d8f03431fce63b88f4"
    //     0x8d28cc: ldr             x1, [x1, #0xc90]
    // 0x8d28d0: r2 = 32
    //     0x8d28d0: movz            x2, #0x20
    // 0x8d28d4: stur            x0, [fp, #-0x30]
    // 0x8d28d8: r0 = parse()
    //     0x8d28d8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d28dc: ldur            x16, [fp, #-0x28]
    // 0x8d28e0: ldur            lr, [fp, #-8]
    // 0x8d28e4: stp             lr, x16, [SP, #8]
    // 0x8d28e8: str             x0, [SP]
    // 0x8d28ec: ldur            x3, [fp, #-0x10]
    // 0x8d28f0: ldur            x5, [fp, #-0x18]
    // 0x8d28f4: ldur            x6, [fp, #-0x20]
    // 0x8d28f8: ldur            x7, [fp, #-0x30]
    // 0x8d28fc: r1 = "secp128r2"
    //     0x8d28fc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c50] "secp128r2"
    //     0x8d2900: ldr             x1, [x1, #0xc50]
    // 0x8d2904: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp128r2 from Function '_make@998417788': static.
    //     0x8d2904: add             x2, PP, #0x18, lsl #12  ; [pp+0x18c98] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp128r2 from Function '_make@998417788': static. (0x7e54fb2d2924)
    //     0x8d2908: ldr             x2, [x2, #0xc98]
    // 0x8d290c: r0 = constructFpStandardCurve()
    //     0x8d290c: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d2910: LeaveFrame
    //     0x8d2910: mov             SP, fp
    //     0x8d2914: ldp             fp, lr, [SP], #0x10
    // 0x8d2918: ret
    //     0x8d2918: ret             
    // 0x8d291c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d291c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2920: b               #0x8d2854
  }
  [closure] static ECCurve_secp128r2 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d2924, size: 0x20
    // 0x8d2924: EnterFrame
    //     0x8d2924: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2928: mov             fp, SP
    // 0x8d292c: r0 = ECCurve_secp128r2()
    //     0x8d292c: bl              #0x8d2944  ; AllocateECCurve_secp128r2Stub -> ECCurve_secp128r2 (size=0xc)
    // 0x8d2930: ldr             x1, [fp, #0x18]
    // 0x8d2934: StoreField: r0->field_7 = r1
    //     0x8d2934: stur            w1, [x0, #7]
    // 0x8d2938: LeaveFrame
    //     0x8d2938: mov             SP, fp
    //     0x8d293c: ldp             fp, lr, [SP], #0x10
    // 0x8d2940: ret
    //     0x8d2940: ret             
  }
}
