// lib: impl.ec_domain_parameters.secp160k1, url: package:pointycastle/ecc/curves/secp160k1.dart

// class id: 1050993, size: 0x8
class :: {
}

// class id: 609, size: 0xc, field offset: 0xc
class ECCurve_secp160k1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf2c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d2630, size: 0x58
    // 0x8d2630: EnterFrame
    //     0x8d2630: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2634: mov             fp, SP
    // 0x8d2638: AllocStack(0x8)
    //     0x8d2638: sub             SP, SP, #8
    // 0x8d263c: r0 = StaticFactoryConfig()
    //     0x8d263c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d2640: mov             x3, x0
    // 0x8d2644: r0 = "secp160k1"
    //     0x8d2644: add             x0, PP, #0x18, lsl #12  ; [pp+0x18c28] "secp160k1"
    //     0x8d2648: ldr             x0, [x0, #0xc28]
    // 0x8d264c: stur            x3, [fp, #-8]
    // 0x8d2650: StoreField: r3->field_b = r0
    //     0x8d2650: stur            w0, [x3, #0xb]
    // 0x8d2654: r1 = Function '<anonymous closure>': static.
    //     0x8d2654: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c30] AnonymousClosure: static (0x8d2688), in [package:pointycastle/ecc/curves/secp160k1.dart] ECCurve_secp160k1::factoryConfig (0x8d2630)
    //     0x8d2658: ldr             x1, [x1, #0xc30]
    // 0x8d265c: r2 = Null
    //     0x8d265c: mov             x2, NULL
    // 0x8d2660: r0 = AllocateClosure()
    //     0x8d2660: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d2664: mov             x1, x0
    // 0x8d2668: ldur            x0, [fp, #-8]
    // 0x8d266c: StoreField: r0->field_f = r1
    //     0x8d266c: stur            w1, [x0, #0xf]
    // 0x8d2670: r1 = ECDomainParameters
    //     0x8d2670: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d2674: ldr             x1, [x1, #0x6e8]
    // 0x8d2678: StoreField: r0->field_7 = r1
    //     0x8d2678: stur            w1, [x0, #7]
    // 0x8d267c: LeaveFrame
    //     0x8d267c: mov             SP, fp
    //     0x8d2680: ldp             fp, lr, [SP], #0x10
    // 0x8d2684: ret
    //     0x8d2684: ret             
  }
  [closure] static ECCurve_secp160k1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d2688, size: 0x30
    // 0x8d2688: EnterFrame
    //     0x8d2688: stp             fp, lr, [SP, #-0x10]!
    //     0x8d268c: mov             fp, SP
    // 0x8d2690: CheckStackOverflow
    //     0x8d2690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2694: cmp             SP, x16
    //     0x8d2698: b.ls            #0x8d26b0
    // 0x8d269c: r1 = Null
    //     0x8d269c: mov             x1, NULL
    // 0x8d26a0: r0 = ECCurve_secp160k1()
    //     0x8d26a0: bl              #0x8d26b8  ; [package:pointycastle/ecc/curves/secp160k1.dart] ECCurve_secp160k1::ECCurve_secp160k1
    // 0x8d26a4: LeaveFrame
    //     0x8d26a4: mov             SP, fp
    //     0x8d26a8: ldp             fp, lr, [SP], #0x10
    // 0x8d26ac: ret
    //     0x8d26ac: ret             
    // 0x8d26b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d26b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d26b4: b               #0x8d269c
  }
  factory ECCurve_secp160k1 ECCurve_secp160k1(dynamic) {
    // ** addr: 0x8d26b8, size: 0xd0
    // 0x8d26b8: EnterFrame
    //     0x8d26b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d26bc: mov             fp, SP
    // 0x8d26c0: AllocStack(0x40)
    //     0x8d26c0: sub             SP, SP, #0x40
    // 0x8d26c4: CheckStackOverflow
    //     0x8d26c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d26c8: cmp             SP, x16
    //     0x8d26cc: b.ls            #0x8d2780
    // 0x8d26d0: r1 = "fffffffffffffffffffffffffffffffeffffac73"
    //     0x8d26d0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ba8] "fffffffffffffffffffffffffffffffeffffac73"
    //     0x8d26d4: ldr             x1, [x1, #0xba8]
    // 0x8d26d8: r2 = 32
    //     0x8d26d8: movz            x2, #0x20
    // 0x8d26dc: r0 = parse()
    //     0x8d26dc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d26e0: r1 = "0"
    //     0x8d26e0: ldr             x1, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x8d26e4: r2 = 32
    //     0x8d26e4: movz            x2, #0x20
    // 0x8d26e8: stur            x0, [fp, #-8]
    // 0x8d26ec: r0 = parse()
    //     0x8d26ec: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d26f0: r1 = "7"
    //     0x8d26f0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a78] "7"
    //     0x8d26f4: ldr             x1, [x1, #0xa78]
    // 0x8d26f8: r2 = 32
    //     0x8d26f8: movz            x2, #0x20
    // 0x8d26fc: stur            x0, [fp, #-0x10]
    // 0x8d2700: r0 = parse()
    //     0x8d2700: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2704: r1 = "043b4c382ce37aa192a4019e763036f4f5dd4d7ebb938cf935318fdced6bc28286531733c3f03c4fee"
    //     0x8d2704: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c38] "043b4c382ce37aa192a4019e763036f4f5dd4d7ebb938cf935318fdced6bc28286531733c3f03c4fee"
    //     0x8d2708: ldr             x1, [x1, #0xc38]
    // 0x8d270c: r2 = 32
    //     0x8d270c: movz            x2, #0x20
    // 0x8d2710: stur            x0, [fp, #-0x18]
    // 0x8d2714: r0 = parse()
    //     0x8d2714: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2718: r1 = "100000000000000000001b8fa16dfab9aca16b6b3"
    //     0x8d2718: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c40] "100000000000000000001b8fa16dfab9aca16b6b3"
    //     0x8d271c: ldr             x1, [x1, #0xc40]
    // 0x8d2720: r2 = 32
    //     0x8d2720: movz            x2, #0x20
    // 0x8d2724: stur            x0, [fp, #-0x20]
    // 0x8d2728: r0 = parse()
    //     0x8d2728: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d272c: r1 = "1"
    //     0x8d272c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d2730: ldr             x1, [x1, #0x718]
    // 0x8d2734: r2 = 32
    //     0x8d2734: movz            x2, #0x20
    // 0x8d2738: stur            x0, [fp, #-0x28]
    // 0x8d273c: r0 = parse()
    //     0x8d273c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2740: ldur            x16, [fp, #-0x28]
    // 0x8d2744: ldur            lr, [fp, #-8]
    // 0x8d2748: stp             lr, x16, [SP, #8]
    // 0x8d274c: str             NULL, [SP]
    // 0x8d2750: ldur            x3, [fp, #-0x10]
    // 0x8d2754: ldur            x5, [fp, #-0x18]
    // 0x8d2758: ldur            x6, [fp, #-0x20]
    // 0x8d275c: mov             x7, x0
    // 0x8d2760: r1 = "secp160k1"
    //     0x8d2760: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c28] "secp160k1"
    //     0x8d2764: ldr             x1, [x1, #0xc28]
    // 0x8d2768: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_secp160k1 from Function '_make@999280080': static.
    //     0x8d2768: add             x2, PP, #0x18, lsl #12  ; [pp+0x18c48] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_secp160k1 from Function '_make@999280080': static. (0x7e54fb2d2788)
    //     0x8d276c: ldr             x2, [x2, #0xc48]
    // 0x8d2770: r0 = constructFpStandardCurve()
    //     0x8d2770: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d2774: LeaveFrame
    //     0x8d2774: mov             SP, fp
    //     0x8d2778: ldp             fp, lr, [SP], #0x10
    // 0x8d277c: ret
    //     0x8d277c: ret             
    // 0x8d2780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2780: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2784: b               #0x8d26d0
  }
  [closure] static ECCurve_secp160k1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d2788, size: 0x20
    // 0x8d2788: EnterFrame
    //     0x8d2788: stp             fp, lr, [SP, #-0x10]!
    //     0x8d278c: mov             fp, SP
    // 0x8d2790: r0 = ECCurve_secp160k1()
    //     0x8d2790: bl              #0x8d27a8  ; AllocateECCurve_secp160k1Stub -> ECCurve_secp160k1 (size=0xc)
    // 0x8d2794: ldr             x1, [fp, #0x18]
    // 0x8d2798: StoreField: r0->field_7 = r1
    //     0x8d2798: stur            w1, [x0, #7]
    // 0x8d279c: LeaveFrame
    //     0x8d279c: mov             SP, fp
    //     0x8d27a0: ldp             fp, lr, [SP], #0x10
    // 0x8d27a4: ret
    //     0x8d27a4: ret             
  }
}
