// lib: impl.ec_domain_parameters.secp160r1, url: package:pointycastle/ecc/curves/secp160r1.dart

// class id: 1050994, size: 0x8
class :: {
}

// class id: 608, size: 0xc, field offset: 0xc
class ECCurve_secp160r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf30

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d2494, size: 0x58
    // 0x8d2494: EnterFrame
    //     0x8d2494: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2498: mov             fp, SP
    // 0x8d249c: AllocStack(0x8)
    //     0x8d249c: sub             SP, SP, #8
    // 0x8d24a0: r0 = StaticFactoryConfig()
    //     0x8d24a0: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d24a4: mov             x3, x0
    // 0x8d24a8: r0 = "secp160r1"
    //     0x8d24a8: add             x0, PP, #0x18, lsl #12  ; [pp+0x18be0] "secp160r1"
    //     0x8d24ac: ldr             x0, [x0, #0xbe0]
    // 0x8d24b0: stur            x3, [fp, #-8]
    // 0x8d24b4: StoreField: r3->field_b = r0
    //     0x8d24b4: stur            w0, [x3, #0xb]
    // 0x8d24b8: r1 = Function '<anonymous closure>': static.
    //     0x8d24b8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18be8] AnonymousClosure: static (0x8d24ec), in [package:pointycastle/ecc/curves/secp160r1.dart] ECCurve_secp160r1::factoryConfig (0x8d2494)
    //     0x8d24bc: ldr             x1, [x1, #0xbe8]
    // 0x8d24c0: r2 = Null
    //     0x8d24c0: mov             x2, NULL
    // 0x8d24c4: r0 = AllocateClosure()
    //     0x8d24c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d24c8: mov             x1, x0
    // 0x8d24cc: ldur            x0, [fp, #-8]
    // 0x8d24d0: StoreField: r0->field_f = r1
    //     0x8d24d0: stur            w1, [x0, #0xf]
    // 0x8d24d4: r1 = ECDomainParameters
    //     0x8d24d4: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d24d8: ldr             x1, [x1, #0x6e8]
    // 0x8d24dc: StoreField: r0->field_7 = r1
    //     0x8d24dc: stur            w1, [x0, #7]
    // 0x8d24e0: LeaveFrame
    //     0x8d24e0: mov             SP, fp
    //     0x8d24e4: ldp             fp, lr, [SP], #0x10
    // 0x8d24e8: ret
    //     0x8d24e8: ret             
  }
  [closure] static ECCurve_secp160r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d24ec, size: 0x30
    // 0x8d24ec: EnterFrame
    //     0x8d24ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8d24f0: mov             fp, SP
    // 0x8d24f4: CheckStackOverflow
    //     0x8d24f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d24f8: cmp             SP, x16
    //     0x8d24fc: b.ls            #0x8d2514
    // 0x8d2500: r1 = Null
    //     0x8d2500: mov             x1, NULL
    // 0x8d2504: r0 = ECCurve_secp160r1()
    //     0x8d2504: bl              #0x8d251c  ; [package:pointycastle/ecc/curves/secp160r1.dart] ECCurve_secp160r1::ECCurve_secp160r1
    // 0x8d2508: LeaveFrame
    //     0x8d2508: mov             SP, fp
    //     0x8d250c: ldp             fp, lr, [SP], #0x10
    // 0x8d2510: ret
    //     0x8d2510: ret             
    // 0x8d2514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2514: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2518: b               #0x8d2500
  }
  factory ECCurve_secp160r1 ECCurve_secp160r1(dynamic) {
    // ** addr: 0x8d251c, size: 0xe8
    // 0x8d251c: EnterFrame
    //     0x8d251c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2520: mov             fp, SP
    // 0x8d2524: AllocStack(0x48)
    //     0x8d2524: sub             SP, SP, #0x48
    // 0x8d2528: CheckStackOverflow
    //     0x8d2528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d252c: cmp             SP, x16
    //     0x8d2530: b.ls            #0x8d25fc
    // 0x8d2534: r1 = "ffffffffffffffffffffffffffffffff7fffffff"
    //     0x8d2534: add             x1, PP, #0x18, lsl #12  ; [pp+0x18bf0] "ffffffffffffffffffffffffffffffff7fffffff"
    //     0x8d2538: ldr             x1, [x1, #0xbf0]
    // 0x8d253c: r2 = 32
    //     0x8d253c: movz            x2, #0x20
    // 0x8d2540: r0 = parse()
    //     0x8d2540: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2544: r1 = "ffffffffffffffffffffffffffffffff7ffffffc"
    //     0x8d2544: add             x1, PP, #0x18, lsl #12  ; [pp+0x18bf8] "ffffffffffffffffffffffffffffffff7ffffffc"
    //     0x8d2548: ldr             x1, [x1, #0xbf8]
    // 0x8d254c: r2 = 32
    //     0x8d254c: movz            x2, #0x20
    // 0x8d2550: stur            x0, [fp, #-8]
    // 0x8d2554: r0 = parse()
    //     0x8d2554: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2558: r1 = "1c97befc54bd7a8b65acf89f81d4d4adc565fa45"
    //     0x8d2558: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c00] "1c97befc54bd7a8b65acf89f81d4d4adc565fa45"
    //     0x8d255c: ldr             x1, [x1, #0xc00]
    // 0x8d2560: r2 = 32
    //     0x8d2560: movz            x2, #0x20
    // 0x8d2564: stur            x0, [fp, #-0x10]
    // 0x8d2568: r0 = parse()
    //     0x8d2568: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d256c: r1 = "044a96b5688ef573284664698968c38bb913cbfc8223a628553168947d59dcc912042351377ac5fb32"
    //     0x8d256c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c08] "044a96b5688ef573284664698968c38bb913cbfc8223a628553168947d59dcc912042351377ac5fb32"
    //     0x8d2570: ldr             x1, [x1, #0xc08]
    // 0x8d2574: r2 = 32
    //     0x8d2574: movz            x2, #0x20
    // 0x8d2578: stur            x0, [fp, #-0x18]
    // 0x8d257c: r0 = parse()
    //     0x8d257c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2580: r1 = "100000000000000000001f4c8f927aed3ca752257"
    //     0x8d2580: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c10] "100000000000000000001f4c8f927aed3ca752257"
    //     0x8d2584: ldr             x1, [x1, #0xc10]
    // 0x8d2588: r2 = 32
    //     0x8d2588: movz            x2, #0x20
    // 0x8d258c: stur            x0, [fp, #-0x20]
    // 0x8d2590: r0 = parse()
    //     0x8d2590: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2594: r1 = "1"
    //     0x8d2594: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d2598: ldr             x1, [x1, #0x718]
    // 0x8d259c: r2 = 32
    //     0x8d259c: movz            x2, #0x20
    // 0x8d25a0: stur            x0, [fp, #-0x28]
    // 0x8d25a4: r0 = parse()
    //     0x8d25a4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d25a8: r1 = "1053cde42c14d696e67687561517533bf3f83345"
    //     0x8d25a8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18c18] "1053cde42c14d696e67687561517533bf3f83345"
    //     0x8d25ac: ldr             x1, [x1, #0xc18]
    // 0x8d25b0: r2 = 32
    //     0x8d25b0: movz            x2, #0x20
    // 0x8d25b4: stur            x0, [fp, #-0x30]
    // 0x8d25b8: r0 = parse()
    //     0x8d25b8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d25bc: ldur            x16, [fp, #-0x28]
    // 0x8d25c0: ldur            lr, [fp, #-8]
    // 0x8d25c4: stp             lr, x16, [SP, #8]
    // 0x8d25c8: str             x0, [SP]
    // 0x8d25cc: ldur            x3, [fp, #-0x10]
    // 0x8d25d0: ldur            x5, [fp, #-0x18]
    // 0x8d25d4: ldur            x6, [fp, #-0x20]
    // 0x8d25d8: ldur            x7, [fp, #-0x30]
    // 0x8d25dc: r1 = "secp160r1"
    //     0x8d25dc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18be0] "secp160r1"
    //     0x8d25e0: ldr             x1, [x1, #0xbe0]
    // 0x8d25e4: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp160r1 from Function '_make@1000308126': static.
    //     0x8d25e4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18c20] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp160r1 from Function '_make@1000308126': static. (0x7e54fb2d2604)
    //     0x8d25e8: ldr             x2, [x2, #0xc20]
    // 0x8d25ec: r0 = constructFpStandardCurve()
    //     0x8d25ec: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d25f0: LeaveFrame
    //     0x8d25f0: mov             SP, fp
    //     0x8d25f4: ldp             fp, lr, [SP], #0x10
    // 0x8d25f8: ret
    //     0x8d25f8: ret             
    // 0x8d25fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d25fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2600: b               #0x8d2534
  }
  [closure] static ECCurve_secp160r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d2604, size: 0x20
    // 0x8d2604: EnterFrame
    //     0x8d2604: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2608: mov             fp, SP
    // 0x8d260c: r0 = ECCurve_secp160r1()
    //     0x8d260c: bl              #0x8d2624  ; AllocateECCurve_secp160r1Stub -> ECCurve_secp160r1 (size=0xc)
    // 0x8d2610: ldr             x1, [fp, #0x18]
    // 0x8d2614: StoreField: r0->field_7 = r1
    //     0x8d2614: stur            w1, [x0, #7]
    // 0x8d2618: LeaveFrame
    //     0x8d2618: mov             SP, fp
    //     0x8d261c: ldp             fp, lr, [SP], #0x10
    // 0x8d2620: ret
    //     0x8d2620: ret             
  }
}
