// lib: impl.ec_domain_parameters.secp160r2, url: package:pointycastle/ecc/curves/secp160r2.dart

// class id: 1050995, size: 0x8
class :: {
}

// class id: 607, size: 0xc, field offset: 0xc
class ECCurve_secp160r2 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf34

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d22f8, size: 0x58
    // 0x8d22f8: EnterFrame
    //     0x8d22f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d22fc: mov             fp, SP
    // 0x8d2300: AllocStack(0x8)
    //     0x8d2300: sub             SP, SP, #8
    // 0x8d2304: r0 = StaticFactoryConfig()
    //     0x8d2304: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d2308: mov             x3, x0
    // 0x8d230c: r0 = "secp160r2"
    //     0x8d230c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18b98] "secp160r2"
    //     0x8d2310: ldr             x0, [x0, #0xb98]
    // 0x8d2314: stur            x3, [fp, #-8]
    // 0x8d2318: StoreField: r3->field_b = r0
    //     0x8d2318: stur            w0, [x3, #0xb]
    // 0x8d231c: r1 = Function '<anonymous closure>': static.
    //     0x8d231c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ba0] AnonymousClosure: static (0x8d2350), in [package:pointycastle/ecc/curves/secp160r2.dart] ECCurve_secp160r2::factoryConfig (0x8d22f8)
    //     0x8d2320: ldr             x1, [x1, #0xba0]
    // 0x8d2324: r2 = Null
    //     0x8d2324: mov             x2, NULL
    // 0x8d2328: r0 = AllocateClosure()
    //     0x8d2328: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d232c: mov             x1, x0
    // 0x8d2330: ldur            x0, [fp, #-8]
    // 0x8d2334: StoreField: r0->field_f = r1
    //     0x8d2334: stur            w1, [x0, #0xf]
    // 0x8d2338: r1 = ECDomainParameters
    //     0x8d2338: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d233c: ldr             x1, [x1, #0x6e8]
    // 0x8d2340: StoreField: r0->field_7 = r1
    //     0x8d2340: stur            w1, [x0, #7]
    // 0x8d2344: LeaveFrame
    //     0x8d2344: mov             SP, fp
    //     0x8d2348: ldp             fp, lr, [SP], #0x10
    // 0x8d234c: ret
    //     0x8d234c: ret             
  }
  [closure] static ECCurve_secp160r2 <anonymous closure>(dynamic) {
    // ** addr: 0x8d2350, size: 0x30
    // 0x8d2350: EnterFrame
    //     0x8d2350: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2354: mov             fp, SP
    // 0x8d2358: CheckStackOverflow
    //     0x8d2358: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d235c: cmp             SP, x16
    //     0x8d2360: b.ls            #0x8d2378
    // 0x8d2364: r1 = Null
    //     0x8d2364: mov             x1, NULL
    // 0x8d2368: r0 = ECCurve_secp160r2()
    //     0x8d2368: bl              #0x8d2380  ; [package:pointycastle/ecc/curves/secp160r2.dart] ECCurve_secp160r2::ECCurve_secp160r2
    // 0x8d236c: LeaveFrame
    //     0x8d236c: mov             SP, fp
    //     0x8d2370: ldp             fp, lr, [SP], #0x10
    // 0x8d2374: ret
    //     0x8d2374: ret             
    // 0x8d2378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d237c: b               #0x8d2364
  }
  factory ECCurve_secp160r2 ECCurve_secp160r2(dynamic) {
    // ** addr: 0x8d2380, size: 0xe8
    // 0x8d2380: EnterFrame
    //     0x8d2380: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2384: mov             fp, SP
    // 0x8d2388: AllocStack(0x48)
    //     0x8d2388: sub             SP, SP, #0x48
    // 0x8d238c: CheckStackOverflow
    //     0x8d238c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2390: cmp             SP, x16
    //     0x8d2394: b.ls            #0x8d2460
    // 0x8d2398: r1 = "fffffffffffffffffffffffffffffffeffffac73"
    //     0x8d2398: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ba8] "fffffffffffffffffffffffffffffffeffffac73"
    //     0x8d239c: ldr             x1, [x1, #0xba8]
    // 0x8d23a0: r2 = 32
    //     0x8d23a0: movz            x2, #0x20
    // 0x8d23a4: r0 = parse()
    //     0x8d23a4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d23a8: r1 = "fffffffffffffffffffffffffffffffeffffac70"
    //     0x8d23a8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18bb0] "fffffffffffffffffffffffffffffffeffffac70"
    //     0x8d23ac: ldr             x1, [x1, #0xbb0]
    // 0x8d23b0: r2 = 32
    //     0x8d23b0: movz            x2, #0x20
    // 0x8d23b4: stur            x0, [fp, #-8]
    // 0x8d23b8: r0 = parse()
    //     0x8d23b8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d23bc: r1 = "b4e134d3fb59eb8bab57274904664d5af50388ba"
    //     0x8d23bc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18bb8] "b4e134d3fb59eb8bab57274904664d5af50388ba"
    //     0x8d23c0: ldr             x1, [x1, #0xbb8]
    // 0x8d23c4: r2 = 32
    //     0x8d23c4: movz            x2, #0x20
    // 0x8d23c8: stur            x0, [fp, #-0x10]
    // 0x8d23cc: r0 = parse()
    //     0x8d23cc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d23d0: r1 = "0452dcb034293a117e1f4ff11b30f7199d3144ce6dfeaffef2e331f296e071fa0df9982cfea7d43f2e"
    //     0x8d23d0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18bc0] "0452dcb034293a117e1f4ff11b30f7199d3144ce6dfeaffef2e331f296e071fa0df9982cfea7d43f2e"
    //     0x8d23d4: ldr             x1, [x1, #0xbc0]
    // 0x8d23d8: r2 = 32
    //     0x8d23d8: movz            x2, #0x20
    // 0x8d23dc: stur            x0, [fp, #-0x18]
    // 0x8d23e0: r0 = parse()
    //     0x8d23e0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d23e4: r1 = "100000000000000000000351ee786a818f3a1a16b"
    //     0x8d23e4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18bc8] "100000000000000000000351ee786a818f3a1a16b"
    //     0x8d23e8: ldr             x1, [x1, #0xbc8]
    // 0x8d23ec: r2 = 32
    //     0x8d23ec: movz            x2, #0x20
    // 0x8d23f0: stur            x0, [fp, #-0x20]
    // 0x8d23f4: r0 = parse()
    //     0x8d23f4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d23f8: r1 = "1"
    //     0x8d23f8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d23fc: ldr             x1, [x1, #0x718]
    // 0x8d2400: r2 = 32
    //     0x8d2400: movz            x2, #0x20
    // 0x8d2404: stur            x0, [fp, #-0x28]
    // 0x8d2408: r0 = parse()
    //     0x8d2408: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d240c: r1 = "b99b99b099b323e02709a4d696e6768756151751"
    //     0x8d240c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18bd0] "b99b99b099b323e02709a4d696e6768756151751"
    //     0x8d2410: ldr             x1, [x1, #0xbd0]
    // 0x8d2414: r2 = 32
    //     0x8d2414: movz            x2, #0x20
    // 0x8d2418: stur            x0, [fp, #-0x30]
    // 0x8d241c: r0 = parse()
    //     0x8d241c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2420: ldur            x16, [fp, #-0x28]
    // 0x8d2424: ldur            lr, [fp, #-8]
    // 0x8d2428: stp             lr, x16, [SP, #8]
    // 0x8d242c: str             x0, [SP]
    // 0x8d2430: ldur            x3, [fp, #-0x10]
    // 0x8d2434: ldur            x5, [fp, #-0x18]
    // 0x8d2438: ldur            x6, [fp, #-0x20]
    // 0x8d243c: ldur            x7, [fp, #-0x30]
    // 0x8d2440: r1 = "secp160r2"
    //     0x8d2440: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b98] "secp160r2"
    //     0x8d2444: ldr             x1, [x1, #0xb98]
    // 0x8d2448: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp160r2 from Function '_make@1001166658': static.
    //     0x8d2448: add             x2, PP, #0x18, lsl #12  ; [pp+0x18bd8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp160r2 from Function '_make@1001166658': static. (0x7e54fb2d2468)
    //     0x8d244c: ldr             x2, [x2, #0xbd8]
    // 0x8d2450: r0 = constructFpStandardCurve()
    //     0x8d2450: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d2454: LeaveFrame
    //     0x8d2454: mov             SP, fp
    //     0x8d2458: ldp             fp, lr, [SP], #0x10
    // 0x8d245c: ret
    //     0x8d245c: ret             
    // 0x8d2460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2460: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2464: b               #0x8d2398
  }
  [closure] static ECCurve_secp160r2 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d2468, size: 0x20
    // 0x8d2468: EnterFrame
    //     0x8d2468: stp             fp, lr, [SP, #-0x10]!
    //     0x8d246c: mov             fp, SP
    // 0x8d2470: r0 = ECCurve_secp160r2()
    //     0x8d2470: bl              #0x8d2488  ; AllocateECCurve_secp160r2Stub -> ECCurve_secp160r2 (size=0xc)
    // 0x8d2474: ldr             x1, [fp, #0x18]
    // 0x8d2478: StoreField: r0->field_7 = r1
    //     0x8d2478: stur            w1, [x0, #7]
    // 0x8d247c: LeaveFrame
    //     0x8d247c: mov             SP, fp
    //     0x8d2480: ldp             fp, lr, [SP], #0x10
    // 0x8d2484: ret
    //     0x8d2484: ret             
  }
}
