// lib: impl.ec_domain_parameters.secp192k1, url: package:pointycastle/ecc/curves/secp192k1.dart

// class id: 1050996, size: 0x8
class :: {
}

// class id: 606, size: 0xc, field offset: 0xc
class ECCurve_secp192k1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf38

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d2174, size: 0x58
    // 0x8d2174: EnterFrame
    //     0x8d2174: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2178: mov             fp, SP
    // 0x8d217c: AllocStack(0x8)
    //     0x8d217c: sub             SP, SP, #8
    // 0x8d2180: r0 = StaticFactoryConfig()
    //     0x8d2180: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d2184: mov             x3, x0
    // 0x8d2188: r0 = "secp192k1"
    //     0x8d2188: add             x0, PP, #0x18, lsl #12  ; [pp+0x18b60] "secp192k1"
    //     0x8d218c: ldr             x0, [x0, #0xb60]
    // 0x8d2190: stur            x3, [fp, #-8]
    // 0x8d2194: StoreField: r3->field_b = r0
    //     0x8d2194: stur            w0, [x3, #0xb]
    // 0x8d2198: r1 = Function '<anonymous closure>': static.
    //     0x8d2198: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b68] AnonymousClosure: static (0x8d21cc), in [package:pointycastle/ecc/curves/secp192k1.dart] ECCurve_secp192k1::factoryConfig (0x8d2174)
    //     0x8d219c: ldr             x1, [x1, #0xb68]
    // 0x8d21a0: r2 = Null
    //     0x8d21a0: mov             x2, NULL
    // 0x8d21a4: r0 = AllocateClosure()
    //     0x8d21a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d21a8: mov             x1, x0
    // 0x8d21ac: ldur            x0, [fp, #-8]
    // 0x8d21b0: StoreField: r0->field_f = r1
    //     0x8d21b0: stur            w1, [x0, #0xf]
    // 0x8d21b4: r1 = ECDomainParameters
    //     0x8d21b4: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d21b8: ldr             x1, [x1, #0x6e8]
    // 0x8d21bc: StoreField: r0->field_7 = r1
    //     0x8d21bc: stur            w1, [x0, #7]
    // 0x8d21c0: LeaveFrame
    //     0x8d21c0: mov             SP, fp
    //     0x8d21c4: ldp             fp, lr, [SP], #0x10
    // 0x8d21c8: ret
    //     0x8d21c8: ret             
  }
  [closure] static ECCurve_secp192k1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d21cc, size: 0x30
    // 0x8d21cc: EnterFrame
    //     0x8d21cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d21d0: mov             fp, SP
    // 0x8d21d4: CheckStackOverflow
    //     0x8d21d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d21d8: cmp             SP, x16
    //     0x8d21dc: b.ls            #0x8d21f4
    // 0x8d21e0: r1 = Null
    //     0x8d21e0: mov             x1, NULL
    // 0x8d21e4: r0 = ECCurve_secp192k1()
    //     0x8d21e4: bl              #0x8d21fc  ; [package:pointycastle/ecc/curves/secp192k1.dart] ECCurve_secp192k1::ECCurve_secp192k1
    // 0x8d21e8: LeaveFrame
    //     0x8d21e8: mov             SP, fp
    //     0x8d21ec: ldp             fp, lr, [SP], #0x10
    // 0x8d21f0: ret
    //     0x8d21f0: ret             
    // 0x8d21f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d21f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d21f8: b               #0x8d21e0
  }
  factory ECCurve_secp192k1 ECCurve_secp192k1(dynamic) {
    // ** addr: 0x8d21fc, size: 0xd0
    // 0x8d21fc: EnterFrame
    //     0x8d21fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2200: mov             fp, SP
    // 0x8d2204: AllocStack(0x40)
    //     0x8d2204: sub             SP, SP, #0x40
    // 0x8d2208: CheckStackOverflow
    //     0x8d2208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d220c: cmp             SP, x16
    //     0x8d2210: b.ls            #0x8d22c4
    // 0x8d2214: r1 = "fffffffffffffffffffffffffffffffffffffffeffffee37"
    //     0x8d2214: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b70] "fffffffffffffffffffffffffffffffffffffffeffffee37"
    //     0x8d2218: ldr             x1, [x1, #0xb70]
    // 0x8d221c: r2 = 32
    //     0x8d221c: movz            x2, #0x20
    // 0x8d2220: r0 = parse()
    //     0x8d2220: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2224: r1 = "0"
    //     0x8d2224: ldr             x1, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x8d2228: r2 = 32
    //     0x8d2228: movz            x2, #0x20
    // 0x8d222c: stur            x0, [fp, #-8]
    // 0x8d2230: r0 = parse()
    //     0x8d2230: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2234: r1 = "3"
    //     0x8d2234: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b78] "3"
    //     0x8d2238: ldr             x1, [x1, #0xb78]
    // 0x8d223c: r2 = 32
    //     0x8d223c: movz            x2, #0x20
    // 0x8d2240: stur            x0, [fp, #-0x10]
    // 0x8d2244: r0 = parse()
    //     0x8d2244: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2248: r1 = "04db4ff10ec057e9ae26b07d0280b7f4341da5d1b1eae06c7d9b2f2f6d9c5628a7844163d015be86344082aa88d95e2f9d"
    //     0x8d2248: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b80] "04db4ff10ec057e9ae26b07d0280b7f4341da5d1b1eae06c7d9b2f2f6d9c5628a7844163d015be86344082aa88d95e2f9d"
    //     0x8d224c: ldr             x1, [x1, #0xb80]
    // 0x8d2250: r2 = 32
    //     0x8d2250: movz            x2, #0x20
    // 0x8d2254: stur            x0, [fp, #-0x18]
    // 0x8d2258: r0 = parse()
    //     0x8d2258: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d225c: r1 = "fffffffffffffffffffffffe26f2fc170f69466a74defd8d"
    //     0x8d225c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b88] "fffffffffffffffffffffffe26f2fc170f69466a74defd8d"
    //     0x8d2260: ldr             x1, [x1, #0xb88]
    // 0x8d2264: r2 = 32
    //     0x8d2264: movz            x2, #0x20
    // 0x8d2268: stur            x0, [fp, #-0x20]
    // 0x8d226c: r0 = parse()
    //     0x8d226c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2270: r1 = "1"
    //     0x8d2270: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d2274: ldr             x1, [x1, #0x718]
    // 0x8d2278: r2 = 32
    //     0x8d2278: movz            x2, #0x20
    // 0x8d227c: stur            x0, [fp, #-0x28]
    // 0x8d2280: r0 = parse()
    //     0x8d2280: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2284: ldur            x16, [fp, #-0x28]
    // 0x8d2288: ldur            lr, [fp, #-8]
    // 0x8d228c: stp             lr, x16, [SP, #8]
    // 0x8d2290: str             NULL, [SP]
    // 0x8d2294: ldur            x3, [fp, #-0x10]
    // 0x8d2298: ldur            x5, [fp, #-0x18]
    // 0x8d229c: ldur            x6, [fp, #-0x20]
    // 0x8d22a0: mov             x7, x0
    // 0x8d22a4: r1 = "secp192k1"
    //     0x8d22a4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b60] "secp192k1"
    //     0x8d22a8: ldr             x1, [x1, #0xb60]
    // 0x8d22ac: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_secp192k1 from Function '_make@1002159089': static.
    //     0x8d22ac: add             x2, PP, #0x18, lsl #12  ; [pp+0x18b90] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_secp192k1 from Function '_make@1002159089': static. (0x7e54fb2d22cc)
    //     0x8d22b0: ldr             x2, [x2, #0xb90]
    // 0x8d22b4: r0 = constructFpStandardCurve()
    //     0x8d22b4: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d22b8: LeaveFrame
    //     0x8d22b8: mov             SP, fp
    //     0x8d22bc: ldp             fp, lr, [SP], #0x10
    // 0x8d22c0: ret
    //     0x8d22c0: ret             
    // 0x8d22c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d22c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d22c8: b               #0x8d2214
  }
  [closure] static ECCurve_secp192k1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d22cc, size: 0x20
    // 0x8d22cc: EnterFrame
    //     0x8d22cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d22d0: mov             fp, SP
    // 0x8d22d4: r0 = ECCurve_secp192k1()
    //     0x8d22d4: bl              #0x8d22ec  ; AllocateECCurve_secp192k1Stub -> ECCurve_secp192k1 (size=0xc)
    // 0x8d22d8: ldr             x1, [fp, #0x18]
    // 0x8d22dc: StoreField: r0->field_7 = r1
    //     0x8d22dc: stur            w1, [x0, #7]
    // 0x8d22e0: LeaveFrame
    //     0x8d22e0: mov             SP, fp
    //     0x8d22e4: ldp             fp, lr, [SP], #0x10
    // 0x8d22e8: ret
    //     0x8d22e8: ret             
  }
}
