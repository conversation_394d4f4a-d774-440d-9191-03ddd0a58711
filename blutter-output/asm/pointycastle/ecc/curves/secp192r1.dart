// lib: impl.ec_domain_parameters.secp192r1, url: package:pointycastle/ecc/curves/secp192r1.dart

// class id: 1050997, size: 0x8
class :: {
}

// class id: 605, size: 0xc, field offset: 0xc
class ECCurve_secp192r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf3c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d1fd8, size: 0x58
    // 0x8d1fd8: EnterFrame
    //     0x8d1fd8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1fdc: mov             fp, SP
    // 0x8d1fe0: AllocStack(0x8)
    //     0x8d1fe0: sub             SP, SP, #8
    // 0x8d1fe4: r0 = StaticFactoryConfig()
    //     0x8d1fe4: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d1fe8: mov             x3, x0
    // 0x8d1fec: r0 = "secp192r1"
    //     0x8d1fec: add             x0, PP, #0x18, lsl #12  ; [pp+0x18b18] "secp192r1"
    //     0x8d1ff0: ldr             x0, [x0, #0xb18]
    // 0x8d1ff4: stur            x3, [fp, #-8]
    // 0x8d1ff8: StoreField: r3->field_b = r0
    //     0x8d1ff8: stur            w0, [x3, #0xb]
    // 0x8d1ffc: r1 = Function '<anonymous closure>': static.
    //     0x8d1ffc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b20] AnonymousClosure: static (0x8d2030), in [package:pointycastle/ecc/curves/secp192r1.dart] ECCurve_secp192r1::factoryConfig (0x8d1fd8)
    //     0x8d2000: ldr             x1, [x1, #0xb20]
    // 0x8d2004: r2 = Null
    //     0x8d2004: mov             x2, NULL
    // 0x8d2008: r0 = AllocateClosure()
    //     0x8d2008: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d200c: mov             x1, x0
    // 0x8d2010: ldur            x0, [fp, #-8]
    // 0x8d2014: StoreField: r0->field_f = r1
    //     0x8d2014: stur            w1, [x0, #0xf]
    // 0x8d2018: r1 = ECDomainParameters
    //     0x8d2018: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d201c: ldr             x1, [x1, #0x6e8]
    // 0x8d2020: StoreField: r0->field_7 = r1
    //     0x8d2020: stur            w1, [x0, #7]
    // 0x8d2024: LeaveFrame
    //     0x8d2024: mov             SP, fp
    //     0x8d2028: ldp             fp, lr, [SP], #0x10
    // 0x8d202c: ret
    //     0x8d202c: ret             
  }
  [closure] static ECCurve_secp192r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d2030, size: 0x30
    // 0x8d2030: EnterFrame
    //     0x8d2030: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2034: mov             fp, SP
    // 0x8d2038: CheckStackOverflow
    //     0x8d2038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d203c: cmp             SP, x16
    //     0x8d2040: b.ls            #0x8d2058
    // 0x8d2044: r1 = Null
    //     0x8d2044: mov             x1, NULL
    // 0x8d2048: r0 = ECCurve_secp192r1()
    //     0x8d2048: bl              #0x8d2060  ; [package:pointycastle/ecc/curves/secp192r1.dart] ECCurve_secp192r1::ECCurve_secp192r1
    // 0x8d204c: LeaveFrame
    //     0x8d204c: mov             SP, fp
    //     0x8d2050: ldp             fp, lr, [SP], #0x10
    // 0x8d2054: ret
    //     0x8d2054: ret             
    // 0x8d2058: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2058: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d205c: b               #0x8d2044
  }
  factory ECCurve_secp192r1 ECCurve_secp192r1(dynamic) {
    // ** addr: 0x8d2060, size: 0xe8
    // 0x8d2060: EnterFrame
    //     0x8d2060: stp             fp, lr, [SP, #-0x10]!
    //     0x8d2064: mov             fp, SP
    // 0x8d2068: AllocStack(0x48)
    //     0x8d2068: sub             SP, SP, #0x48
    // 0x8d206c: CheckStackOverflow
    //     0x8d206c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d2070: cmp             SP, x16
    //     0x8d2074: b.ls            #0x8d2140
    // 0x8d2078: r1 = "fffffffffffffffffffffffffffffffeffffffffffffffff"
    //     0x8d2078: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b28] "fffffffffffffffffffffffffffffffeffffffffffffffff"
    //     0x8d207c: ldr             x1, [x1, #0xb28]
    // 0x8d2080: r2 = 32
    //     0x8d2080: movz            x2, #0x20
    // 0x8d2084: r0 = parse()
    //     0x8d2084: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2088: r1 = "fffffffffffffffffffffffffffffffefffffffffffffffc"
    //     0x8d2088: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b30] "fffffffffffffffffffffffffffffffefffffffffffffffc"
    //     0x8d208c: ldr             x1, [x1, #0xb30]
    // 0x8d2090: r2 = 32
    //     0x8d2090: movz            x2, #0x20
    // 0x8d2094: stur            x0, [fp, #-8]
    // 0x8d2098: r0 = parse()
    //     0x8d2098: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d209c: r1 = "64210519e59c80e70fa7e9ab72243049feb8deecc146b9b1"
    //     0x8d209c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b38] "64210519e59c80e70fa7e9ab72243049feb8deecc146b9b1"
    //     0x8d20a0: ldr             x1, [x1, #0xb38]
    // 0x8d20a4: r2 = 32
    //     0x8d20a4: movz            x2, #0x20
    // 0x8d20a8: stur            x0, [fp, #-0x10]
    // 0x8d20ac: r0 = parse()
    //     0x8d20ac: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d20b0: r1 = "04188da80eb03090f67cbf20eb43a18800f4ff0afd82ff101207192b95ffc8da78631011ed6b24cdd573f977a11e794811"
    //     0x8d20b0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b40] "04188da80eb03090f67cbf20eb43a18800f4ff0afd82ff101207192b95ffc8da78631011ed6b24cdd573f977a11e794811"
    //     0x8d20b4: ldr             x1, [x1, #0xb40]
    // 0x8d20b8: r2 = 32
    //     0x8d20b8: movz            x2, #0x20
    // 0x8d20bc: stur            x0, [fp, #-0x18]
    // 0x8d20c0: r0 = parse()
    //     0x8d20c0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d20c4: r1 = "ffffffffffffffffffffffff99def836146bc9b1b4d22831"
    //     0x8d20c4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b48] "ffffffffffffffffffffffff99def836146bc9b1b4d22831"
    //     0x8d20c8: ldr             x1, [x1, #0xb48]
    // 0x8d20cc: r2 = 32
    //     0x8d20cc: movz            x2, #0x20
    // 0x8d20d0: stur            x0, [fp, #-0x20]
    // 0x8d20d4: r0 = parse()
    //     0x8d20d4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d20d8: r1 = "1"
    //     0x8d20d8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d20dc: ldr             x1, [x1, #0x718]
    // 0x8d20e0: r2 = 32
    //     0x8d20e0: movz            x2, #0x20
    // 0x8d20e4: stur            x0, [fp, #-0x28]
    // 0x8d20e8: r0 = parse()
    //     0x8d20e8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d20ec: r1 = "3045ae6fc8422f64ed579528d38120eae12196d5"
    //     0x8d20ec: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b50] "3045ae6fc8422f64ed579528d38120eae12196d5"
    //     0x8d20f0: ldr             x1, [x1, #0xb50]
    // 0x8d20f4: r2 = 32
    //     0x8d20f4: movz            x2, #0x20
    // 0x8d20f8: stur            x0, [fp, #-0x30]
    // 0x8d20fc: r0 = parse()
    //     0x8d20fc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d2100: ldur            x16, [fp, #-0x28]
    // 0x8d2104: ldur            lr, [fp, #-8]
    // 0x8d2108: stp             lr, x16, [SP, #8]
    // 0x8d210c: str             x0, [SP]
    // 0x8d2110: ldur            x3, [fp, #-0x10]
    // 0x8d2114: ldur            x5, [fp, #-0x18]
    // 0x8d2118: ldur            x6, [fp, #-0x20]
    // 0x8d211c: ldur            x7, [fp, #-0x30]
    // 0x8d2120: r1 = "secp192r1"
    //     0x8d2120: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b18] "secp192r1"
    //     0x8d2124: ldr             x1, [x1, #0xb18]
    // 0x8d2128: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp192r1 from Function '_make@1003226699': static.
    //     0x8d2128: add             x2, PP, #0x18, lsl #12  ; [pp+0x18b58] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp192r1 from Function '_make@1003226699': static. (0x7e54fb2d2148)
    //     0x8d212c: ldr             x2, [x2, #0xb58]
    // 0x8d2130: r0 = constructFpStandardCurve()
    //     0x8d2130: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d2134: LeaveFrame
    //     0x8d2134: mov             SP, fp
    //     0x8d2138: ldp             fp, lr, [SP], #0x10
    // 0x8d213c: ret
    //     0x8d213c: ret             
    // 0x8d2140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d2140: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d2144: b               #0x8d2078
  }
  [closure] static ECCurve_secp192r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d2148, size: 0x20
    // 0x8d2148: EnterFrame
    //     0x8d2148: stp             fp, lr, [SP, #-0x10]!
    //     0x8d214c: mov             fp, SP
    // 0x8d2150: r0 = ECCurve_secp192r1()
    //     0x8d2150: bl              #0x8d2168  ; AllocateECCurve_secp192r1Stub -> ECCurve_secp192r1 (size=0xc)
    // 0x8d2154: ldr             x1, [fp, #0x18]
    // 0x8d2158: StoreField: r0->field_7 = r1
    //     0x8d2158: stur            w1, [x0, #7]
    // 0x8d215c: LeaveFrame
    //     0x8d215c: mov             SP, fp
    //     0x8d2160: ldp             fp, lr, [SP], #0x10
    // 0x8d2164: ret
    //     0x8d2164: ret             
  }
}
