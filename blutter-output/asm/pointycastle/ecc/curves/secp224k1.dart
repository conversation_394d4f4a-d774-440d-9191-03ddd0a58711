// lib: impl.ec_domain_parameters.secp224k1, url: package:pointycastle/ecc/curves/secp224k1.dart

// class id: 1050998, size: 0x8
class :: {
}

// class id: 604, size: 0xc, field offset: 0xc
class ECCurve_secp224k1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf40

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d1e54, size: 0x58
    // 0x8d1e54: EnterFrame
    //     0x8d1e54: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1e58: mov             fp, SP
    // 0x8d1e5c: AllocStack(0x8)
    //     0x8d1e5c: sub             SP, SP, #8
    // 0x8d1e60: r0 = StaticFactoryConfig()
    //     0x8d1e60: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d1e64: mov             x3, x0
    // 0x8d1e68: r0 = "secp224k1"
    //     0x8d1e68: add             x0, PP, #0x18, lsl #12  ; [pp+0x18ae0] "secp224k1"
    //     0x8d1e6c: ldr             x0, [x0, #0xae0]
    // 0x8d1e70: stur            x3, [fp, #-8]
    // 0x8d1e74: StoreField: r3->field_b = r0
    //     0x8d1e74: stur            w0, [x3, #0xb]
    // 0x8d1e78: r1 = Function '<anonymous closure>': static.
    //     0x8d1e78: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ae8] AnonymousClosure: static (0x8d1eac), in [package:pointycastle/ecc/curves/secp224k1.dart] ECCurve_secp224k1::factoryConfig (0x8d1e54)
    //     0x8d1e7c: ldr             x1, [x1, #0xae8]
    // 0x8d1e80: r2 = Null
    //     0x8d1e80: mov             x2, NULL
    // 0x8d1e84: r0 = AllocateClosure()
    //     0x8d1e84: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d1e88: mov             x1, x0
    // 0x8d1e8c: ldur            x0, [fp, #-8]
    // 0x8d1e90: StoreField: r0->field_f = r1
    //     0x8d1e90: stur            w1, [x0, #0xf]
    // 0x8d1e94: r1 = ECDomainParameters
    //     0x8d1e94: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d1e98: ldr             x1, [x1, #0x6e8]
    // 0x8d1e9c: StoreField: r0->field_7 = r1
    //     0x8d1e9c: stur            w1, [x0, #7]
    // 0x8d1ea0: LeaveFrame
    //     0x8d1ea0: mov             SP, fp
    //     0x8d1ea4: ldp             fp, lr, [SP], #0x10
    // 0x8d1ea8: ret
    //     0x8d1ea8: ret             
  }
  [closure] static ECCurve_secp224k1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d1eac, size: 0x30
    // 0x8d1eac: EnterFrame
    //     0x8d1eac: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1eb0: mov             fp, SP
    // 0x8d1eb4: CheckStackOverflow
    //     0x8d1eb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d1eb8: cmp             SP, x16
    //     0x8d1ebc: b.ls            #0x8d1ed4
    // 0x8d1ec0: r1 = Null
    //     0x8d1ec0: mov             x1, NULL
    // 0x8d1ec4: r0 = ECCurve_secp224k1()
    //     0x8d1ec4: bl              #0x8d1edc  ; [package:pointycastle/ecc/curves/secp224k1.dart] ECCurve_secp224k1::ECCurve_secp224k1
    // 0x8d1ec8: LeaveFrame
    //     0x8d1ec8: mov             SP, fp
    //     0x8d1ecc: ldp             fp, lr, [SP], #0x10
    // 0x8d1ed0: ret
    //     0x8d1ed0: ret             
    // 0x8d1ed4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d1ed4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1ed8: b               #0x8d1ec0
  }
  factory ECCurve_secp224k1 ECCurve_secp224k1(dynamic) {
    // ** addr: 0x8d1edc, size: 0xd0
    // 0x8d1edc: EnterFrame
    //     0x8d1edc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1ee0: mov             fp, SP
    // 0x8d1ee4: AllocStack(0x40)
    //     0x8d1ee4: sub             SP, SP, #0x40
    // 0x8d1ee8: CheckStackOverflow
    //     0x8d1ee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d1eec: cmp             SP, x16
    //     0x8d1ef0: b.ls            #0x8d1fa4
    // 0x8d1ef4: r1 = "fffffffffffffffffffffffffffffffffffffffffffffffeffffe56d"
    //     0x8d1ef4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18af0] "fffffffffffffffffffffffffffffffffffffffffffffffeffffe56d"
    //     0x8d1ef8: ldr             x1, [x1, #0xaf0]
    // 0x8d1efc: r2 = 32
    //     0x8d1efc: movz            x2, #0x20
    // 0x8d1f00: r0 = parse()
    //     0x8d1f00: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1f04: r1 = "0"
    //     0x8d1f04: ldr             x1, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x8d1f08: r2 = 32
    //     0x8d1f08: movz            x2, #0x20
    // 0x8d1f0c: stur            x0, [fp, #-8]
    // 0x8d1f10: r0 = parse()
    //     0x8d1f10: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1f14: r1 = "5"
    //     0x8d1f14: add             x1, PP, #0x18, lsl #12  ; [pp+0x18af8] "5"
    //     0x8d1f18: ldr             x1, [x1, #0xaf8]
    // 0x8d1f1c: r2 = 32
    //     0x8d1f1c: movz            x2, #0x20
    // 0x8d1f20: stur            x0, [fp, #-0x10]
    // 0x8d1f24: r0 = parse()
    //     0x8d1f24: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1f28: r1 = "04a1455b334df099df30fc28a169a467e9e47075a90f7e650eb6b7a45c7e089fed7fba344282cafbd6f7e319f7c0b0bd59e2ca4bdb556d61a5"
    //     0x8d1f28: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b00] "04a1455b334df099df30fc28a169a467e9e47075a90f7e650eb6b7a45c7e089fed7fba344282cafbd6f7e319f7c0b0bd59e2ca4bdb556d61a5"
    //     0x8d1f2c: ldr             x1, [x1, #0xb00]
    // 0x8d1f30: r2 = 32
    //     0x8d1f30: movz            x2, #0x20
    // 0x8d1f34: stur            x0, [fp, #-0x18]
    // 0x8d1f38: r0 = parse()
    //     0x8d1f38: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1f3c: r1 = "10000000000000000000000000001dce8d2ec6184caf0a971769fb1f7"
    //     0x8d1f3c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18b08] "10000000000000000000000000001dce8d2ec6184caf0a971769fb1f7"
    //     0x8d1f40: ldr             x1, [x1, #0xb08]
    // 0x8d1f44: r2 = 32
    //     0x8d1f44: movz            x2, #0x20
    // 0x8d1f48: stur            x0, [fp, #-0x20]
    // 0x8d1f4c: r0 = parse()
    //     0x8d1f4c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1f50: r1 = "1"
    //     0x8d1f50: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d1f54: ldr             x1, [x1, #0x718]
    // 0x8d1f58: r2 = 32
    //     0x8d1f58: movz            x2, #0x20
    // 0x8d1f5c: stur            x0, [fp, #-0x28]
    // 0x8d1f60: r0 = parse()
    //     0x8d1f60: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1f64: ldur            x16, [fp, #-0x28]
    // 0x8d1f68: ldur            lr, [fp, #-8]
    // 0x8d1f6c: stp             lr, x16, [SP, #8]
    // 0x8d1f70: str             NULL, [SP]
    // 0x8d1f74: ldur            x3, [fp, #-0x10]
    // 0x8d1f78: ldur            x5, [fp, #-0x18]
    // 0x8d1f7c: ldur            x6, [fp, #-0x20]
    // 0x8d1f80: mov             x7, x0
    // 0x8d1f84: r1 = "secp224k1"
    //     0x8d1f84: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ae0] "secp224k1"
    //     0x8d1f88: ldr             x1, [x1, #0xae0]
    // 0x8d1f8c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_secp224k1 from Function '_make@1004009234': static.
    //     0x8d1f8c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18b10] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_secp224k1 from Function '_make@1004009234': static. (0x7e54fb2d1fac)
    //     0x8d1f90: ldr             x2, [x2, #0xb10]
    // 0x8d1f94: r0 = constructFpStandardCurve()
    //     0x8d1f94: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d1f98: LeaveFrame
    //     0x8d1f98: mov             SP, fp
    //     0x8d1f9c: ldp             fp, lr, [SP], #0x10
    // 0x8d1fa0: ret
    //     0x8d1fa0: ret             
    // 0x8d1fa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d1fa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1fa8: b               #0x8d1ef4
  }
  [closure] static ECCurve_secp224k1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d1fac, size: 0x20
    // 0x8d1fac: EnterFrame
    //     0x8d1fac: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1fb0: mov             fp, SP
    // 0x8d1fb4: r0 = ECCurve_secp224k1()
    //     0x8d1fb4: bl              #0x8d1fcc  ; AllocateECCurve_secp224k1Stub -> ECCurve_secp224k1 (size=0xc)
    // 0x8d1fb8: ldr             x1, [fp, #0x18]
    // 0x8d1fbc: StoreField: r0->field_7 = r1
    //     0x8d1fbc: stur            w1, [x0, #7]
    // 0x8d1fc0: LeaveFrame
    //     0x8d1fc0: mov             SP, fp
    //     0x8d1fc4: ldp             fp, lr, [SP], #0x10
    // 0x8d1fc8: ret
    //     0x8d1fc8: ret             
  }
}
