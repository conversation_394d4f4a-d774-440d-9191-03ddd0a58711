// lib: impl.ec_domain_parameters.secp224r1, url: package:pointycastle/ecc/curves/secp224r1.dart

// class id: 1050999, size: 0x8
class :: {
}

// class id: 603, size: 0xc, field offset: 0xc
class ECCurve_secp224r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf44

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d1cb8, size: 0x58
    // 0x8d1cb8: EnterFrame
    //     0x8d1cb8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1cbc: mov             fp, SP
    // 0x8d1cc0: AllocStack(0x8)
    //     0x8d1cc0: sub             SP, SP, #8
    // 0x8d1cc4: r0 = StaticFactoryConfig()
    //     0x8d1cc4: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d1cc8: mov             x3, x0
    // 0x8d1ccc: r0 = "secp224r1"
    //     0x8d1ccc: add             x0, PP, #0x18, lsl #12  ; [pp+0x18a98] "secp224r1"
    //     0x8d1cd0: ldr             x0, [x0, #0xa98]
    // 0x8d1cd4: stur            x3, [fp, #-8]
    // 0x8d1cd8: StoreField: r3->field_b = r0
    //     0x8d1cd8: stur            w0, [x3, #0xb]
    // 0x8d1cdc: r1 = Function '<anonymous closure>': static.
    //     0x8d1cdc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18aa0] AnonymousClosure: static (0x8d1d10), in [package:pointycastle/ecc/curves/secp224r1.dart] ECCurve_secp224r1::factoryConfig (0x8d1cb8)
    //     0x8d1ce0: ldr             x1, [x1, #0xaa0]
    // 0x8d1ce4: r2 = Null
    //     0x8d1ce4: mov             x2, NULL
    // 0x8d1ce8: r0 = AllocateClosure()
    //     0x8d1ce8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d1cec: mov             x1, x0
    // 0x8d1cf0: ldur            x0, [fp, #-8]
    // 0x8d1cf4: StoreField: r0->field_f = r1
    //     0x8d1cf4: stur            w1, [x0, #0xf]
    // 0x8d1cf8: r1 = ECDomainParameters
    //     0x8d1cf8: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d1cfc: ldr             x1, [x1, #0x6e8]
    // 0x8d1d00: StoreField: r0->field_7 = r1
    //     0x8d1d00: stur            w1, [x0, #7]
    // 0x8d1d04: LeaveFrame
    //     0x8d1d04: mov             SP, fp
    //     0x8d1d08: ldp             fp, lr, [SP], #0x10
    // 0x8d1d0c: ret
    //     0x8d1d0c: ret             
  }
  [closure] static ECCurve_secp224r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d1d10, size: 0x30
    // 0x8d1d10: EnterFrame
    //     0x8d1d10: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1d14: mov             fp, SP
    // 0x8d1d18: CheckStackOverflow
    //     0x8d1d18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d1d1c: cmp             SP, x16
    //     0x8d1d20: b.ls            #0x8d1d38
    // 0x8d1d24: r1 = Null
    //     0x8d1d24: mov             x1, NULL
    // 0x8d1d28: r0 = ECCurve_secp224r1()
    //     0x8d1d28: bl              #0x8d1d40  ; [package:pointycastle/ecc/curves/secp224r1.dart] ECCurve_secp224r1::ECCurve_secp224r1
    // 0x8d1d2c: LeaveFrame
    //     0x8d1d2c: mov             SP, fp
    //     0x8d1d30: ldp             fp, lr, [SP], #0x10
    // 0x8d1d34: ret
    //     0x8d1d34: ret             
    // 0x8d1d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d1d38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1d3c: b               #0x8d1d24
  }
  factory ECCurve_secp224r1 ECCurve_secp224r1(dynamic) {
    // ** addr: 0x8d1d40, size: 0xe8
    // 0x8d1d40: EnterFrame
    //     0x8d1d40: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1d44: mov             fp, SP
    // 0x8d1d48: AllocStack(0x48)
    //     0x8d1d48: sub             SP, SP, #0x48
    // 0x8d1d4c: CheckStackOverflow
    //     0x8d1d4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d1d50: cmp             SP, x16
    //     0x8d1d54: b.ls            #0x8d1e20
    // 0x8d1d58: r1 = "ffffffffffffffffffffffffffffffff000000000000000000000001"
    //     0x8d1d58: add             x1, PP, #0x18, lsl #12  ; [pp+0x18aa8] "ffffffffffffffffffffffffffffffff000000000000000000000001"
    //     0x8d1d5c: ldr             x1, [x1, #0xaa8]
    // 0x8d1d60: r2 = 32
    //     0x8d1d60: movz            x2, #0x20
    // 0x8d1d64: r0 = parse()
    //     0x8d1d64: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1d68: r1 = "fffffffffffffffffffffffffffffffefffffffffffffffffffffffe"
    //     0x8d1d68: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ab0] "fffffffffffffffffffffffffffffffefffffffffffffffffffffffe"
    //     0x8d1d6c: ldr             x1, [x1, #0xab0]
    // 0x8d1d70: r2 = 32
    //     0x8d1d70: movz            x2, #0x20
    // 0x8d1d74: stur            x0, [fp, #-8]
    // 0x8d1d78: r0 = parse()
    //     0x8d1d78: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1d7c: r1 = "b4050a850c04b3abf54132565044b0b7d7bfd8ba270b39432355ffb4"
    //     0x8d1d7c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ab8] "b4050a850c04b3abf54132565044b0b7d7bfd8ba270b39432355ffb4"
    //     0x8d1d80: ldr             x1, [x1, #0xab8]
    // 0x8d1d84: r2 = 32
    //     0x8d1d84: movz            x2, #0x20
    // 0x8d1d88: stur            x0, [fp, #-0x10]
    // 0x8d1d8c: r0 = parse()
    //     0x8d1d8c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1d90: r1 = "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34"
    //     0x8d1d90: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ac0] "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34"
    //     0x8d1d94: ldr             x1, [x1, #0xac0]
    // 0x8d1d98: r2 = 32
    //     0x8d1d98: movz            x2, #0x20
    // 0x8d1d9c: stur            x0, [fp, #-0x18]
    // 0x8d1da0: r0 = parse()
    //     0x8d1da0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1da4: r1 = "ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d"
    //     0x8d1da4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ac8] "ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d"
    //     0x8d1da8: ldr             x1, [x1, #0xac8]
    // 0x8d1dac: r2 = 32
    //     0x8d1dac: movz            x2, #0x20
    // 0x8d1db0: stur            x0, [fp, #-0x20]
    // 0x8d1db4: r0 = parse()
    //     0x8d1db4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1db8: r1 = "1"
    //     0x8d1db8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d1dbc: ldr             x1, [x1, #0x718]
    // 0x8d1dc0: r2 = 32
    //     0x8d1dc0: movz            x2, #0x20
    // 0x8d1dc4: stur            x0, [fp, #-0x28]
    // 0x8d1dc8: r0 = parse()
    //     0x8d1dc8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1dcc: r1 = "bd71344799d5c7fcdc45b59fa3b9ab8f6a948bc5"
    //     0x8d1dcc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18ad0] "bd71344799d5c7fcdc45b59fa3b9ab8f6a948bc5"
    //     0x8d1dd0: ldr             x1, [x1, #0xad0]
    // 0x8d1dd4: r2 = 32
    //     0x8d1dd4: movz            x2, #0x20
    // 0x8d1dd8: stur            x0, [fp, #-0x30]
    // 0x8d1ddc: r0 = parse()
    //     0x8d1ddc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1de0: ldur            x16, [fp, #-0x28]
    // 0x8d1de4: ldur            lr, [fp, #-8]
    // 0x8d1de8: stp             lr, x16, [SP, #8]
    // 0x8d1dec: str             x0, [SP]
    // 0x8d1df0: ldur            x3, [fp, #-0x10]
    // 0x8d1df4: ldur            x5, [fp, #-0x18]
    // 0x8d1df8: ldur            x6, [fp, #-0x20]
    // 0x8d1dfc: ldur            x7, [fp, #-0x30]
    // 0x8d1e00: r1 = "secp224r1"
    //     0x8d1e00: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a98] "secp224r1"
    //     0x8d1e04: ldr             x1, [x1, #0xa98]
    // 0x8d1e08: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp224r1 from Function '_make@1005309820': static.
    //     0x8d1e08: add             x2, PP, #0x18, lsl #12  ; [pp+0x18ad8] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp224r1 from Function '_make@1005309820': static. (0x7e54fb2d1e28)
    //     0x8d1e0c: ldr             x2, [x2, #0xad8]
    // 0x8d1e10: r0 = constructFpStandardCurve()
    //     0x8d1e10: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d1e14: LeaveFrame
    //     0x8d1e14: mov             SP, fp
    //     0x8d1e18: ldp             fp, lr, [SP], #0x10
    // 0x8d1e1c: ret
    //     0x8d1e1c: ret             
    // 0x8d1e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d1e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1e24: b               #0x8d1d58
  }
  [closure] static ECCurve_secp224r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d1e28, size: 0x20
    // 0x8d1e28: EnterFrame
    //     0x8d1e28: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1e2c: mov             fp, SP
    // 0x8d1e30: r0 = ECCurve_secp224r1()
    //     0x8d1e30: bl              #0x8d1e48  ; AllocateECCurve_secp224r1Stub -> ECCurve_secp224r1 (size=0xc)
    // 0x8d1e34: ldr             x1, [fp, #0x18]
    // 0x8d1e38: StoreField: r0->field_7 = r1
    //     0x8d1e38: stur            w1, [x0, #7]
    // 0x8d1e3c: LeaveFrame
    //     0x8d1e3c: mov             SP, fp
    //     0x8d1e40: ldp             fp, lr, [SP], #0x10
    // 0x8d1e44: ret
    //     0x8d1e44: ret             
  }
}
