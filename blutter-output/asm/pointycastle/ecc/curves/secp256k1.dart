// lib: impl.ec_domain_parameters.secp256k1, url: package:pointycastle/ecc/curves/secp256k1.dart

// class id: 1051000, size: 0x8
class :: {
}

// class id: 602, size: 0xc, field offset: 0xc
class ECCurve_secp256k1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf48

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d1b34, size: 0x58
    // 0x8d1b34: EnterFrame
    //     0x8d1b34: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1b38: mov             fp, SP
    // 0x8d1b3c: AllocStack(0x8)
    //     0x8d1b3c: sub             SP, SP, #8
    // 0x8d1b40: r0 = StaticFactoryConfig()
    //     0x8d1b40: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d1b44: mov             x3, x0
    // 0x8d1b48: r0 = "secp256k1"
    //     0x8d1b48: add             x0, PP, #0x18, lsl #12  ; [pp+0x18a60] "secp256k1"
    //     0x8d1b4c: ldr             x0, [x0, #0xa60]
    // 0x8d1b50: stur            x3, [fp, #-8]
    // 0x8d1b54: StoreField: r3->field_b = r0
    //     0x8d1b54: stur            w0, [x3, #0xb]
    // 0x8d1b58: r1 = Function '<anonymous closure>': static.
    //     0x8d1b58: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a68] AnonymousClosure: static (0x8d1b8c), in [package:pointycastle/ecc/curves/secp256k1.dart] ECCurve_secp256k1::factoryConfig (0x8d1b34)
    //     0x8d1b5c: ldr             x1, [x1, #0xa68]
    // 0x8d1b60: r2 = Null
    //     0x8d1b60: mov             x2, NULL
    // 0x8d1b64: r0 = AllocateClosure()
    //     0x8d1b64: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d1b68: mov             x1, x0
    // 0x8d1b6c: ldur            x0, [fp, #-8]
    // 0x8d1b70: StoreField: r0->field_f = r1
    //     0x8d1b70: stur            w1, [x0, #0xf]
    // 0x8d1b74: r1 = ECDomainParameters
    //     0x8d1b74: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d1b78: ldr             x1, [x1, #0x6e8]
    // 0x8d1b7c: StoreField: r0->field_7 = r1
    //     0x8d1b7c: stur            w1, [x0, #7]
    // 0x8d1b80: LeaveFrame
    //     0x8d1b80: mov             SP, fp
    //     0x8d1b84: ldp             fp, lr, [SP], #0x10
    // 0x8d1b88: ret
    //     0x8d1b88: ret             
  }
  [closure] static ECCurve_secp256k1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d1b8c, size: 0x30
    // 0x8d1b8c: EnterFrame
    //     0x8d1b8c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1b90: mov             fp, SP
    // 0x8d1b94: CheckStackOverflow
    //     0x8d1b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d1b98: cmp             SP, x16
    //     0x8d1b9c: b.ls            #0x8d1bb4
    // 0x8d1ba0: r1 = Null
    //     0x8d1ba0: mov             x1, NULL
    // 0x8d1ba4: r0 = ECCurve_secp256k1()
    //     0x8d1ba4: bl              #0x8d1bbc  ; [package:pointycastle/ecc/curves/secp256k1.dart] ECCurve_secp256k1::ECCurve_secp256k1
    // 0x8d1ba8: LeaveFrame
    //     0x8d1ba8: mov             SP, fp
    //     0x8d1bac: ldp             fp, lr, [SP], #0x10
    // 0x8d1bb0: ret
    //     0x8d1bb0: ret             
    // 0x8d1bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d1bb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1bb8: b               #0x8d1ba0
  }
  factory ECCurve_secp256k1 ECCurve_secp256k1(dynamic) {
    // ** addr: 0x8d1bbc, size: 0xd0
    // 0x8d1bbc: EnterFrame
    //     0x8d1bbc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1bc0: mov             fp, SP
    // 0x8d1bc4: AllocStack(0x40)
    //     0x8d1bc4: sub             SP, SP, #0x40
    // 0x8d1bc8: CheckStackOverflow
    //     0x8d1bc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d1bcc: cmp             SP, x16
    //     0x8d1bd0: b.ls            #0x8d1c84
    // 0x8d1bd4: r1 = "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"
    //     0x8d1bd4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a70] "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"
    //     0x8d1bd8: ldr             x1, [x1, #0xa70]
    // 0x8d1bdc: r2 = 32
    //     0x8d1bdc: movz            x2, #0x20
    // 0x8d1be0: r0 = parse()
    //     0x8d1be0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1be4: r1 = "0"
    //     0x8d1be4: ldr             x1, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x8d1be8: r2 = 32
    //     0x8d1be8: movz            x2, #0x20
    // 0x8d1bec: stur            x0, [fp, #-8]
    // 0x8d1bf0: r0 = parse()
    //     0x8d1bf0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1bf4: r1 = "7"
    //     0x8d1bf4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a78] "7"
    //     0x8d1bf8: ldr             x1, [x1, #0xa78]
    // 0x8d1bfc: r2 = 32
    //     0x8d1bfc: movz            x2, #0x20
    // 0x8d1c00: stur            x0, [fp, #-0x10]
    // 0x8d1c04: r0 = parse()
    //     0x8d1c04: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1c08: r1 = "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8"
    //     0x8d1c08: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a80] "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8"
    //     0x8d1c0c: ldr             x1, [x1, #0xa80]
    // 0x8d1c10: r2 = 32
    //     0x8d1c10: movz            x2, #0x20
    // 0x8d1c14: stur            x0, [fp, #-0x18]
    // 0x8d1c18: r0 = parse()
    //     0x8d1c18: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1c1c: r1 = "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"
    //     0x8d1c1c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a88] "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"
    //     0x8d1c20: ldr             x1, [x1, #0xa88]
    // 0x8d1c24: r2 = 32
    //     0x8d1c24: movz            x2, #0x20
    // 0x8d1c28: stur            x0, [fp, #-0x20]
    // 0x8d1c2c: r0 = parse()
    //     0x8d1c2c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1c30: r1 = "1"
    //     0x8d1c30: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d1c34: ldr             x1, [x1, #0x718]
    // 0x8d1c38: r2 = 32
    //     0x8d1c38: movz            x2, #0x20
    // 0x8d1c3c: stur            x0, [fp, #-0x28]
    // 0x8d1c40: r0 = parse()
    //     0x8d1c40: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1c44: ldur            x16, [fp, #-0x28]
    // 0x8d1c48: ldur            lr, [fp, #-8]
    // 0x8d1c4c: stp             lr, x16, [SP, #8]
    // 0x8d1c50: str             NULL, [SP]
    // 0x8d1c54: ldur            x3, [fp, #-0x10]
    // 0x8d1c58: ldur            x5, [fp, #-0x18]
    // 0x8d1c5c: ldur            x6, [fp, #-0x20]
    // 0x8d1c60: mov             x7, x0
    // 0x8d1c64: r1 = "secp256k1"
    //     0x8d1c64: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a60] "secp256k1"
    //     0x8d1c68: ldr             x1, [x1, #0xa60]
    // 0x8d1c6c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_secp256k1 from Function '_make@1006069956': static.
    //     0x8d1c6c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18a90] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) => ECCurve_secp256k1 from Function '_make@1006069956': static. (0x7e54fb2d1c8c)
    //     0x8d1c70: ldr             x2, [x2, #0xa90]
    // 0x8d1c74: r0 = constructFpStandardCurve()
    //     0x8d1c74: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d1c78: LeaveFrame
    //     0x8d1c78: mov             SP, fp
    //     0x8d1c7c: ldp             fp, lr, [SP], #0x10
    // 0x8d1c80: ret
    //     0x8d1c80: ret             
    // 0x8d1c84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d1c84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1c88: b               #0x8d1bd4
  }
  [closure] static ECCurve_secp256k1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>?) {
    // ** addr: 0x8d1c8c, size: 0x20
    // 0x8d1c8c: EnterFrame
    //     0x8d1c8c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1c90: mov             fp, SP
    // 0x8d1c94: r0 = ECCurve_secp256k1()
    //     0x8d1c94: bl              #0x8d1cac  ; AllocateECCurve_secp256k1Stub -> ECCurve_secp256k1 (size=0xc)
    // 0x8d1c98: ldr             x1, [fp, #0x18]
    // 0x8d1c9c: StoreField: r0->field_7 = r1
    //     0x8d1c9c: stur            w1, [x0, #7]
    // 0x8d1ca0: LeaveFrame
    //     0x8d1ca0: mov             SP, fp
    //     0x8d1ca4: ldp             fp, lr, [SP], #0x10
    // 0x8d1ca8: ret
    //     0x8d1ca8: ret             
  }
}
