// lib: impl.ec_domain_parameters.secp256r1, url: package:pointycastle/ecc/curves/secp256r1.dart

// class id: 1051001, size: 0x8
class :: {
}

// class id: 601, size: 0xc, field offset: 0xc
class ECCurve_secp256r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf4c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d1998, size: 0x58
    // 0x8d1998: EnterFrame
    //     0x8d1998: stp             fp, lr, [SP, #-0x10]!
    //     0x8d199c: mov             fp, SP
    // 0x8d19a0: AllocStack(0x8)
    //     0x8d19a0: sub             SP, SP, #8
    // 0x8d19a4: r0 = StaticFactoryConfig()
    //     0x8d19a4: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d19a8: mov             x3, x0
    // 0x8d19ac: r0 = "secp256r1"
    //     0x8d19ac: add             x0, PP, #0x18, lsl #12  ; [pp+0x18a18] "secp256r1"
    //     0x8d19b0: ldr             x0, [x0, #0xa18]
    // 0x8d19b4: stur            x3, [fp, #-8]
    // 0x8d19b8: StoreField: r3->field_b = r0
    //     0x8d19b8: stur            w0, [x3, #0xb]
    // 0x8d19bc: r1 = Function '<anonymous closure>': static.
    //     0x8d19bc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a20] AnonymousClosure: static (0x8d19f0), in [package:pointycastle/ecc/curves/secp256r1.dart] ECCurve_secp256r1::factoryConfig (0x8d1998)
    //     0x8d19c0: ldr             x1, [x1, #0xa20]
    // 0x8d19c4: r2 = Null
    //     0x8d19c4: mov             x2, NULL
    // 0x8d19c8: r0 = AllocateClosure()
    //     0x8d19c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d19cc: mov             x1, x0
    // 0x8d19d0: ldur            x0, [fp, #-8]
    // 0x8d19d4: StoreField: r0->field_f = r1
    //     0x8d19d4: stur            w1, [x0, #0xf]
    // 0x8d19d8: r1 = ECDomainParameters
    //     0x8d19d8: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d19dc: ldr             x1, [x1, #0x6e8]
    // 0x8d19e0: StoreField: r0->field_7 = r1
    //     0x8d19e0: stur            w1, [x0, #7]
    // 0x8d19e4: LeaveFrame
    //     0x8d19e4: mov             SP, fp
    //     0x8d19e8: ldp             fp, lr, [SP], #0x10
    // 0x8d19ec: ret
    //     0x8d19ec: ret             
  }
  [closure] static ECCurve_secp256r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d19f0, size: 0x30
    // 0x8d19f0: EnterFrame
    //     0x8d19f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d19f4: mov             fp, SP
    // 0x8d19f8: CheckStackOverflow
    //     0x8d19f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d19fc: cmp             SP, x16
    //     0x8d1a00: b.ls            #0x8d1a18
    // 0x8d1a04: r1 = Null
    //     0x8d1a04: mov             x1, NULL
    // 0x8d1a08: r0 = ECCurve_secp256r1()
    //     0x8d1a08: bl              #0x8d1a20  ; [package:pointycastle/ecc/curves/secp256r1.dart] ECCurve_secp256r1::ECCurve_secp256r1
    // 0x8d1a0c: LeaveFrame
    //     0x8d1a0c: mov             SP, fp
    //     0x8d1a10: ldp             fp, lr, [SP], #0x10
    // 0x8d1a14: ret
    //     0x8d1a14: ret             
    // 0x8d1a18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d1a18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1a1c: b               #0x8d1a04
  }
  factory ECCurve_secp256r1 ECCurve_secp256r1(dynamic) {
    // ** addr: 0x8d1a20, size: 0xe8
    // 0x8d1a20: EnterFrame
    //     0x8d1a20: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1a24: mov             fp, SP
    // 0x8d1a28: AllocStack(0x48)
    //     0x8d1a28: sub             SP, SP, #0x48
    // 0x8d1a2c: CheckStackOverflow
    //     0x8d1a2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d1a30: cmp             SP, x16
    //     0x8d1a34: b.ls            #0x8d1b00
    // 0x8d1a38: r1 = "ffffffff00000001000000000000000000000000ffffffffffffffffffffffff"
    //     0x8d1a38: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a28] "ffffffff00000001000000000000000000000000ffffffffffffffffffffffff"
    //     0x8d1a3c: ldr             x1, [x1, #0xa28]
    // 0x8d1a40: r2 = 32
    //     0x8d1a40: movz            x2, #0x20
    // 0x8d1a44: r0 = parse()
    //     0x8d1a44: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1a48: r1 = "ffffffff00000001000000000000000000000000fffffffffffffffffffffffc"
    //     0x8d1a48: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a30] "ffffffff00000001000000000000000000000000fffffffffffffffffffffffc"
    //     0x8d1a4c: ldr             x1, [x1, #0xa30]
    // 0x8d1a50: r2 = 32
    //     0x8d1a50: movz            x2, #0x20
    // 0x8d1a54: stur            x0, [fp, #-8]
    // 0x8d1a58: r0 = parse()
    //     0x8d1a58: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1a5c: r1 = "5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"
    //     0x8d1a5c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a38] "5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"
    //     0x8d1a60: ldr             x1, [x1, #0xa38]
    // 0x8d1a64: r2 = 32
    //     0x8d1a64: movz            x2, #0x20
    // 0x8d1a68: stur            x0, [fp, #-0x10]
    // 0x8d1a6c: r0 = parse()
    //     0x8d1a6c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1a70: r1 = "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"
    //     0x8d1a70: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a40] "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"
    //     0x8d1a74: ldr             x1, [x1, #0xa40]
    // 0x8d1a78: r2 = 32
    //     0x8d1a78: movz            x2, #0x20
    // 0x8d1a7c: stur            x0, [fp, #-0x18]
    // 0x8d1a80: r0 = parse()
    //     0x8d1a80: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1a84: r1 = "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"
    //     0x8d1a84: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a48] "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"
    //     0x8d1a88: ldr             x1, [x1, #0xa48]
    // 0x8d1a8c: r2 = 32
    //     0x8d1a8c: movz            x2, #0x20
    // 0x8d1a90: stur            x0, [fp, #-0x20]
    // 0x8d1a94: r0 = parse()
    //     0x8d1a94: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1a98: r1 = "1"
    //     0x8d1a98: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d1a9c: ldr             x1, [x1, #0x718]
    // 0x8d1aa0: r2 = 32
    //     0x8d1aa0: movz            x2, #0x20
    // 0x8d1aa4: stur            x0, [fp, #-0x28]
    // 0x8d1aa8: r0 = parse()
    //     0x8d1aa8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1aac: r1 = "c49d360886e704936a6678e1139d26b7819f7e90"
    //     0x8d1aac: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a50] "c49d360886e704936a6678e1139d26b7819f7e90"
    //     0x8d1ab0: ldr             x1, [x1, #0xa50]
    // 0x8d1ab4: r2 = 32
    //     0x8d1ab4: movz            x2, #0x20
    // 0x8d1ab8: stur            x0, [fp, #-0x30]
    // 0x8d1abc: r0 = parse()
    //     0x8d1abc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1ac0: ldur            x16, [fp, #-0x28]
    // 0x8d1ac4: ldur            lr, [fp, #-8]
    // 0x8d1ac8: stp             lr, x16, [SP, #8]
    // 0x8d1acc: str             x0, [SP]
    // 0x8d1ad0: ldur            x3, [fp, #-0x10]
    // 0x8d1ad4: ldur            x5, [fp, #-0x18]
    // 0x8d1ad8: ldur            x6, [fp, #-0x20]
    // 0x8d1adc: ldur            x7, [fp, #-0x30]
    // 0x8d1ae0: r1 = "secp256r1"
    //     0x8d1ae0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a18] "secp256r1"
    //     0x8d1ae4: ldr             x1, [x1, #0xa18]
    // 0x8d1ae8: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp256r1 from Function '_make@1007493561': static.
    //     0x8d1ae8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18a58] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp256r1 from Function '_make@1007493561': static. (0x7e54fb2d1b08)
    //     0x8d1aec: ldr             x2, [x2, #0xa58]
    // 0x8d1af0: r0 = constructFpStandardCurve()
    //     0x8d1af0: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d1af4: LeaveFrame
    //     0x8d1af4: mov             SP, fp
    //     0x8d1af8: ldp             fp, lr, [SP], #0x10
    // 0x8d1afc: ret
    //     0x8d1afc: ret             
    // 0x8d1b00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d1b00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1b04: b               #0x8d1a38
  }
  [closure] static ECCurve_secp256r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d1b08, size: 0x20
    // 0x8d1b08: EnterFrame
    //     0x8d1b08: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1b0c: mov             fp, SP
    // 0x8d1b10: r0 = ECCurve_secp256r1()
    //     0x8d1b10: bl              #0x8d1b28  ; AllocateECCurve_secp256r1Stub -> ECCurve_secp256r1 (size=0xc)
    // 0x8d1b14: ldr             x1, [fp, #0x18]
    // 0x8d1b18: StoreField: r0->field_7 = r1
    //     0x8d1b18: stur            w1, [x0, #7]
    // 0x8d1b1c: LeaveFrame
    //     0x8d1b1c: mov             SP, fp
    //     0x8d1b20: ldp             fp, lr, [SP], #0x10
    // 0x8d1b24: ret
    //     0x8d1b24: ret             
  }
}
