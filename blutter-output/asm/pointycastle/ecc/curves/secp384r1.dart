// lib: impl.ec_domain_parameters.secp384r1, url: package:pointycastle/ecc/curves/secp384r1.dart

// class id: 1051002, size: 0x8
class :: {
}

// class id: 600, size: 0xc, field offset: 0xc
class ECCurve_secp384r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf50

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8d17fc, size: 0x58
    // 0x8d17fc: EnterFrame
    //     0x8d17fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1800: mov             fp, SP
    // 0x8d1804: AllocStack(0x8)
    //     0x8d1804: sub             SP, SP, #8
    // 0x8d1808: r0 = StaticFactoryConfig()
    //     0x8d1808: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8d180c: mov             x3, x0
    // 0x8d1810: r0 = "secp384r1"
    //     0x8d1810: add             x0, PP, #0x18, lsl #12  ; [pp+0x189d0] "secp384r1"
    //     0x8d1814: ldr             x0, [x0, #0x9d0]
    // 0x8d1818: stur            x3, [fp, #-8]
    // 0x8d181c: StoreField: r3->field_b = r0
    //     0x8d181c: stur            w0, [x3, #0xb]
    // 0x8d1820: r1 = Function '<anonymous closure>': static.
    //     0x8d1820: add             x1, PP, #0x18, lsl #12  ; [pp+0x189d8] AnonymousClosure: static (0x8d1854), in [package:pointycastle/ecc/curves/secp384r1.dart] ECCurve_secp384r1::factoryConfig (0x8d17fc)
    //     0x8d1824: ldr             x1, [x1, #0x9d8]
    // 0x8d1828: r2 = Null
    //     0x8d1828: mov             x2, NULL
    // 0x8d182c: r0 = AllocateClosure()
    //     0x8d182c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8d1830: mov             x1, x0
    // 0x8d1834: ldur            x0, [fp, #-8]
    // 0x8d1838: StoreField: r0->field_f = r1
    //     0x8d1838: stur            w1, [x0, #0xf]
    // 0x8d183c: r1 = ECDomainParameters
    //     0x8d183c: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8d1840: ldr             x1, [x1, #0x6e8]
    // 0x8d1844: StoreField: r0->field_7 = r1
    //     0x8d1844: stur            w1, [x0, #7]
    // 0x8d1848: LeaveFrame
    //     0x8d1848: mov             SP, fp
    //     0x8d184c: ldp             fp, lr, [SP], #0x10
    // 0x8d1850: ret
    //     0x8d1850: ret             
  }
  [closure] static ECCurve_secp384r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8d1854, size: 0x30
    // 0x8d1854: EnterFrame
    //     0x8d1854: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1858: mov             fp, SP
    // 0x8d185c: CheckStackOverflow
    //     0x8d185c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d1860: cmp             SP, x16
    //     0x8d1864: b.ls            #0x8d187c
    // 0x8d1868: r1 = Null
    //     0x8d1868: mov             x1, NULL
    // 0x8d186c: r0 = ECCurve_secp384r1()
    //     0x8d186c: bl              #0x8d1884  ; [package:pointycastle/ecc/curves/secp384r1.dart] ECCurve_secp384r1::ECCurve_secp384r1
    // 0x8d1870: LeaveFrame
    //     0x8d1870: mov             SP, fp
    //     0x8d1874: ldp             fp, lr, [SP], #0x10
    // 0x8d1878: ret
    //     0x8d1878: ret             
    // 0x8d187c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d187c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1880: b               #0x8d1868
  }
  factory ECCurve_secp384r1 ECCurve_secp384r1(dynamic) {
    // ** addr: 0x8d1884, size: 0xe8
    // 0x8d1884: EnterFrame
    //     0x8d1884: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1888: mov             fp, SP
    // 0x8d188c: AllocStack(0x48)
    //     0x8d188c: sub             SP, SP, #0x48
    // 0x8d1890: CheckStackOverflow
    //     0x8d1890: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d1894: cmp             SP, x16
    //     0x8d1898: b.ls            #0x8d1964
    // 0x8d189c: r1 = "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff"
    //     0x8d189c: add             x1, PP, #0x18, lsl #12  ; [pp+0x189e0] "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff"
    //     0x8d18a0: ldr             x1, [x1, #0x9e0]
    // 0x8d18a4: r2 = 32
    //     0x8d18a4: movz            x2, #0x20
    // 0x8d18a8: r0 = parse()
    //     0x8d18a8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d18ac: r1 = "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffc"
    //     0x8d18ac: add             x1, PP, #0x18, lsl #12  ; [pp+0x189e8] "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffc"
    //     0x8d18b0: ldr             x1, [x1, #0x9e8]
    // 0x8d18b4: r2 = 32
    //     0x8d18b4: movz            x2, #0x20
    // 0x8d18b8: stur            x0, [fp, #-8]
    // 0x8d18bc: r0 = parse()
    //     0x8d18bc: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d18c0: r1 = "b3312fa7e23ee7e4988e056be3f82d19181d9c6efe8141120314088f5013875ac656398d8a2ed19d2a85c8edd3ec2aef"
    //     0x8d18c0: add             x1, PP, #0x18, lsl #12  ; [pp+0x189f0] "b3312fa7e23ee7e4988e056be3f82d19181d9c6efe8141120314088f5013875ac656398d8a2ed19d2a85c8edd3ec2aef"
    //     0x8d18c4: ldr             x1, [x1, #0x9f0]
    // 0x8d18c8: r2 = 32
    //     0x8d18c8: movz            x2, #0x20
    // 0x8d18cc: stur            x0, [fp, #-0x10]
    // 0x8d18d0: r0 = parse()
    //     0x8d18d0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d18d4: r1 = "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f"
    //     0x8d18d4: add             x1, PP, #0x18, lsl #12  ; [pp+0x189f8] "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f"
    //     0x8d18d8: ldr             x1, [x1, #0x9f8]
    // 0x8d18dc: r2 = 32
    //     0x8d18dc: movz            x2, #0x20
    // 0x8d18e0: stur            x0, [fp, #-0x18]
    // 0x8d18e4: r0 = parse()
    //     0x8d18e4: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d18e8: r1 = "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973"
    //     0x8d18e8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a00] "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973"
    //     0x8d18ec: ldr             x1, [x1, #0xa00]
    // 0x8d18f0: r2 = 32
    //     0x8d18f0: movz            x2, #0x20
    // 0x8d18f4: stur            x0, [fp, #-0x20]
    // 0x8d18f8: r0 = parse()
    //     0x8d18f8: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d18fc: r1 = "1"
    //     0x8d18fc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8d1900: ldr             x1, [x1, #0x718]
    // 0x8d1904: r2 = 32
    //     0x8d1904: movz            x2, #0x20
    // 0x8d1908: stur            x0, [fp, #-0x28]
    // 0x8d190c: r0 = parse()
    //     0x8d190c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1910: r1 = "a335926aa319a27a1d00896a6773a4827acdac73"
    //     0x8d1910: add             x1, PP, #0x18, lsl #12  ; [pp+0x18a08] "a335926aa319a27a1d00896a6773a4827acdac73"
    //     0x8d1914: ldr             x1, [x1, #0xa08]
    // 0x8d1918: r2 = 32
    //     0x8d1918: movz            x2, #0x20
    // 0x8d191c: stur            x0, [fp, #-0x30]
    // 0x8d1920: r0 = parse()
    //     0x8d1920: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8d1924: ldur            x16, [fp, #-0x28]
    // 0x8d1928: ldur            lr, [fp, #-8]
    // 0x8d192c: stp             lr, x16, [SP, #8]
    // 0x8d1930: str             x0, [SP]
    // 0x8d1934: ldur            x3, [fp, #-0x10]
    // 0x8d1938: ldur            x5, [fp, #-0x18]
    // 0x8d193c: ldur            x6, [fp, #-0x20]
    // 0x8d1940: ldur            x7, [fp, #-0x30]
    // 0x8d1944: r1 = "secp384r1"
    //     0x8d1944: add             x1, PP, #0x18, lsl #12  ; [pp+0x189d0] "secp384r1"
    //     0x8d1948: ldr             x1, [x1, #0x9d0]
    // 0x8d194c: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp384r1 from Function '_make@1008249435': static.
    //     0x8d194c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18a10] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp384r1 from Function '_make@1008249435': static. (0x7e54fb2d196c)
    //     0x8d1950: ldr             x2, [x2, #0xa10]
    // 0x8d1954: r0 = constructFpStandardCurve()
    //     0x8d1954: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8d1958: LeaveFrame
    //     0x8d1958: mov             SP, fp
    //     0x8d195c: ldp             fp, lr, [SP], #0x10
    // 0x8d1960: ret
    //     0x8d1960: ret             
    // 0x8d1964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d1964: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d1968: b               #0x8d189c
  }
  [closure] static ECCurve_secp384r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d196c, size: 0x20
    // 0x8d196c: EnterFrame
    //     0x8d196c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d1970: mov             fp, SP
    // 0x8d1974: r0 = ECCurve_secp384r1()
    //     0x8d1974: bl              #0x8d198c  ; AllocateECCurve_secp384r1Stub -> ECCurve_secp384r1 (size=0xc)
    // 0x8d1978: ldr             x1, [fp, #0x18]
    // 0x8d197c: StoreField: r0->field_7 = r1
    //     0x8d197c: stur            w1, [x0, #7]
    // 0x8d1980: LeaveFrame
    //     0x8d1980: mov             SP, fp
    //     0x8d1984: ldp             fp, lr, [SP], #0x10
    // 0x8d1988: ret
    //     0x8d1988: ret             
  }
}
