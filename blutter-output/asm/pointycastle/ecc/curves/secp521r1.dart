// lib: impl.ec_domain_parameters.secp521r1, url: package:pointycastle/ecc/curves/secp521r1.dart

// class id: 1051003, size: 0x8
class :: {
}

// class id: 599, size: 0xc, field offset: 0xc
class ECCurve_secp521r1 extends ECDomainParametersImpl {

  static late final FactoryConfig factoryConfig; // offset: 0xf54

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c938c, size: 0x58
    // 0x8c938c: EnterFrame
    //     0x8c938c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9390: mov             fp, SP
    // 0x8c9394: AllocStack(0x8)
    //     0x8c9394: sub             SP, SP, #8
    // 0x8c9398: r0 = StaticFactoryConfig()
    //     0x8c9398: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c939c: mov             x3, x0
    // 0x8c93a0: r0 = "secp521r1"
    //     0x8c93a0: add             x0, PP, #0x18, lsl #12  ; [pp+0x186d8] "secp521r1"
    //     0x8c93a4: ldr             x0, [x0, #0x6d8]
    // 0x8c93a8: stur            x3, [fp, #-8]
    // 0x8c93ac: StoreField: r3->field_b = r0
    //     0x8c93ac: stur            w0, [x3, #0xb]
    // 0x8c93b0: r1 = Function '<anonymous closure>': static.
    //     0x8c93b0: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e0] AnonymousClosure: static (0x8c93e4), in [package:pointycastle/ecc/curves/secp521r1.dart] ECCurve_secp521r1::factoryConfig (0x8c938c)
    //     0x8c93b4: ldr             x1, [x1, #0x6e0]
    // 0x8c93b8: r2 = Null
    //     0x8c93b8: mov             x2, NULL
    // 0x8c93bc: r0 = AllocateClosure()
    //     0x8c93bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c93c0: mov             x1, x0
    // 0x8c93c4: ldur            x0, [fp, #-8]
    // 0x8c93c8: StoreField: r0->field_f = r1
    //     0x8c93c8: stur            w1, [x0, #0xf]
    // 0x8c93cc: r1 = ECDomainParameters
    //     0x8c93cc: add             x1, PP, #0x18, lsl #12  ; [pp+0x186e8] Type: ECDomainParameters
    //     0x8c93d0: ldr             x1, [x1, #0x6e8]
    // 0x8c93d4: StoreField: r0->field_7 = r1
    //     0x8c93d4: stur            w1, [x0, #7]
    // 0x8c93d8: LeaveFrame
    //     0x8c93d8: mov             SP, fp
    //     0x8c93dc: ldp             fp, lr, [SP], #0x10
    // 0x8c93e0: ret
    //     0x8c93e0: ret             
  }
  [closure] static ECCurve_secp521r1 <anonymous closure>(dynamic) {
    // ** addr: 0x8c93e4, size: 0x30
    // 0x8c93e4: EnterFrame
    //     0x8c93e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c93e8: mov             fp, SP
    // 0x8c93ec: CheckStackOverflow
    //     0x8c93ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c93f0: cmp             SP, x16
    //     0x8c93f4: b.ls            #0x8c940c
    // 0x8c93f8: r1 = Null
    //     0x8c93f8: mov             x1, NULL
    // 0x8c93fc: r0 = ECCurve_secp521r1()
    //     0x8c93fc: bl              #0x8c9414  ; [package:pointycastle/ecc/curves/secp521r1.dart] ECCurve_secp521r1::ECCurve_secp521r1
    // 0x8c9400: LeaveFrame
    //     0x8c9400: mov             SP, fp
    //     0x8c9404: ldp             fp, lr, [SP], #0x10
    // 0x8c9408: ret
    //     0x8c9408: ret             
    // 0x8c940c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c940c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9410: b               #0x8c93f8
  }
  factory ECCurve_secp521r1 ECCurve_secp521r1(dynamic) {
    // ** addr: 0x8c9414, size: 0xe8
    // 0x8c9414: EnterFrame
    //     0x8c9414: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9418: mov             fp, SP
    // 0x8c941c: AllocStack(0x48)
    //     0x8c941c: sub             SP, SP, #0x48
    // 0x8c9420: CheckStackOverflow
    //     0x8c9420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9424: cmp             SP, x16
    //     0x8c9428: b.ls            #0x8c94f4
    // 0x8c942c: r1 = "1ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"
    //     0x8c942c: add             x1, PP, #0x18, lsl #12  ; [pp+0x186f0] "1ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"
    //     0x8c9430: ldr             x1, [x1, #0x6f0]
    // 0x8c9434: r2 = 32
    //     0x8c9434: movz            x2, #0x20
    // 0x8c9438: r0 = parse()
    //     0x8c9438: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8c943c: r1 = "1fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc"
    //     0x8c943c: add             x1, PP, #0x18, lsl #12  ; [pp+0x186f8] "1fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc"
    //     0x8c9440: ldr             x1, [x1, #0x6f8]
    // 0x8c9444: r2 = 32
    //     0x8c9444: movz            x2, #0x20
    // 0x8c9448: stur            x0, [fp, #-8]
    // 0x8c944c: r0 = parse()
    //     0x8c944c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8c9450: r1 = "51953eb9618e1c9a1f929a21a0b68540eea2da725b99b315f3b8b489918ef109e156193951ec7e937b1652c0bd3bb1bf073573df883d2c34f1ef451fd46b503f00"
    //     0x8c9450: add             x1, PP, #0x18, lsl #12  ; [pp+0x18700] "51953eb9618e1c9a1f929a21a0b68540eea2da725b99b315f3b8b489918ef109e156193951ec7e937b1652c0bd3bb1bf073573df883d2c34f1ef451fd46b503f00"
    //     0x8c9454: ldr             x1, [x1, #0x700]
    // 0x8c9458: r2 = 32
    //     0x8c9458: movz            x2, #0x20
    // 0x8c945c: stur            x0, [fp, #-0x10]
    // 0x8c9460: r0 = parse()
    //     0x8c9460: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8c9464: r1 = "0400c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650"
    //     0x8c9464: add             x1, PP, #0x18, lsl #12  ; [pp+0x18708] "0400c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650"
    //     0x8c9468: ldr             x1, [x1, #0x708]
    // 0x8c946c: r2 = 32
    //     0x8c946c: movz            x2, #0x20
    // 0x8c9470: stur            x0, [fp, #-0x18]
    // 0x8c9474: r0 = parse()
    //     0x8c9474: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8c9478: r1 = "1fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409"
    //     0x8c9478: add             x1, PP, #0x18, lsl #12  ; [pp+0x18710] "1fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409"
    //     0x8c947c: ldr             x1, [x1, #0x710]
    // 0x8c9480: r2 = 32
    //     0x8c9480: movz            x2, #0x20
    // 0x8c9484: stur            x0, [fp, #-0x20]
    // 0x8c9488: r0 = parse()
    //     0x8c9488: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8c948c: r1 = "1"
    //     0x8c948c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x8c9490: ldr             x1, [x1, #0x718]
    // 0x8c9494: r2 = 32
    //     0x8c9494: movz            x2, #0x20
    // 0x8c9498: stur            x0, [fp, #-0x28]
    // 0x8c949c: r0 = parse()
    //     0x8c949c: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8c94a0: r1 = "d09e8800291cb85396cc6717393284aaa0da64ba"
    //     0x8c94a0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18720] "d09e8800291cb85396cc6717393284aaa0da64ba"
    //     0x8c94a4: ldr             x1, [x1, #0x720]
    // 0x8c94a8: r2 = 32
    //     0x8c94a8: movz            x2, #0x20
    // 0x8c94ac: stur            x0, [fp, #-0x30]
    // 0x8c94b0: r0 = parse()
    //     0x8c94b0: bl              #0x8d0e18  ; [dart:core] _BigIntImpl::parse
    // 0x8c94b4: ldur            x16, [fp, #-0x28]
    // 0x8c94b8: ldur            lr, [fp, #-8]
    // 0x8c94bc: stp             lr, x16, [SP, #8]
    // 0x8c94c0: str             x0, [SP]
    // 0x8c94c4: ldur            x3, [fp, #-0x10]
    // 0x8c94c8: ldur            x5, [fp, #-0x18]
    // 0x8c94cc: ldur            x6, [fp, #-0x20]
    // 0x8c94d0: ldur            x7, [fp, #-0x30]
    // 0x8c94d4: r1 = "secp521r1"
    //     0x8c94d4: add             x1, PP, #0x18, lsl #12  ; [pp+0x186d8] "secp521r1"
    //     0x8c94d8: ldr             x1, [x1, #0x6d8]
    // 0x8c94dc: r2 = Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp521r1 from Function '_make@1009117017': static.
    //     0x8c94dc: add             x2, PP, #0x18, lsl #12  ; [pp+0x18728] Closure: (String, ECCurve, ECPoint, BigInt, BigInt, List<int>) => ECCurve_secp521r1 from Function '_make@1009117017': static. (0x7e54fb2d17d0)
    //     0x8c94e0: ldr             x2, [x2, #0x728]
    // 0x8c94e4: r0 = constructFpStandardCurve()
    //     0x8c94e4: bl              #0x8c9560  ; [package:pointycastle/src/ec_standard_curve_constructor.dart] ::constructFpStandardCurve
    // 0x8c94e8: LeaveFrame
    //     0x8c94e8: mov             SP, fp
    //     0x8c94ec: ldp             fp, lr, [SP], #0x10
    // 0x8c94f0: ret
    //     0x8c94f0: ret             
    // 0x8c94f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c94f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c94f8: b               #0x8c942c
  }
  [closure] static ECCurve_secp521r1 _make(dynamic, String, ECCurve, ECPoint, BigInt, BigInt, List<int>) {
    // ** addr: 0x8d17d0, size: 0x20
    // 0x8d17d0: EnterFrame
    //     0x8d17d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d17d4: mov             fp, SP
    // 0x8d17d8: r0 = ECCurve_secp521r1()
    //     0x8d17d8: bl              #0x8d17f0  ; AllocateECCurve_secp521r1Stub -> ECCurve_secp521r1 (size=0xc)
    // 0x8d17dc: ldr             x1, [fp, #0x18]
    // 0x8d17e0: StoreField: r0->field_7 = r1
    //     0x8d17e0: stur            w1, [x0, #7]
    // 0x8d17e4: LeaveFrame
    //     0x8d17e4: mov             SP, fp
    //     0x8d17e8: ldp             fp, lr, [SP], #0x10
    // 0x8d17ec: ret
    //     0x8d17ec: ret             
  }
}
