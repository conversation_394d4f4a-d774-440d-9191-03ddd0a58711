// lib: impl.ecc.ecc_base, url: package:pointycastle/ecc/ecc_base.dart

// class id: 1051004, size: 0x8
class :: {
}

// class id: 592, size: 0x10, field offset: 0x8
abstract class ECCurveBase extends Object
    implements ECCurve {

  _ decodePoint(/* No info */) {
    // ** addr: 0x8c9638, size: 0x2b8
    // 0x8c9638: EnterFrame
    //     0x8c9638: stp             fp, lr, [SP, #-0x10]!
    //     0x8c963c: mov             fp, SP
    // 0x8c9640: AllocStack(0x38)
    //     0x8c9640: sub             SP, SP, #0x38
    // 0x8c9644: SetupParameters(ECCurveBase this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8c9644: mov             x0, x1
    //     0x8c9648: stur            x1, [fp, #-8]
    //     0x8c964c: stur            x2, [fp, #-0x10]
    // 0x8c9650: CheckStackOverflow
    //     0x8c9650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9654: cmp             SP, x16
    //     0x8c9658: b.ls            #0x8c98e4
    // 0x8c965c: mov             x1, x0
    // 0x8c9660: r0 = fieldSize()
    //     0x8c9660: bl              #0x8d0990  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::fieldSize
    // 0x8c9664: add             x1, x0, #7
    // 0x8c9668: r0 = 8
    //     0x8c9668: movz            x0, #0x8
    // 0x8c966c: sdiv            x4, x1, x0
    // 0x8c9670: ldur            x6, [fp, #-0x10]
    // 0x8c9674: stur            x4, [fp, #-0x20]
    // 0x8c9678: LoadField: r0 = r6->field_13
    //     0x8c9678: ldur            w0, [x6, #0x13]
    // 0x8c967c: r2 = LoadInt32Instr(r0)
    //     0x8c967c: sbfx            x2, x0, #1, #0x1f
    // 0x8c9680: mov             x0, x2
    // 0x8c9684: r1 = 0
    //     0x8c9684: movz            x1, #0
    // 0x8c9688: cmp             x1, x0
    // 0x8c968c: b.hs            #0x8c98ec
    // 0x8c9690: ArrayLoad: r0 = r6[0]  ; List_1
    //     0x8c9690: ldrb            w0, [x6, #0x17]
    // 0x8c9694: stur            x0, [fp, #-0x30]
    // 0x8c9698: cmp             x0, #3
    // 0x8c969c: b.gt            #0x8c9730
    // 0x8c96a0: cmp             x0, #2
    // 0x8c96a4: b.gt            #0x8c96e0
    // 0x8c96a8: cmp             x0, #0
    // 0x8c96ac: b.gt            #0x8c96d0
    // 0x8c96b0: lsl             x1, x0, #1
    // 0x8c96b4: cbnz            w1, #0x8c97dc
    // 0x8c96b8: cmp             x2, #1
    // 0x8c96bc: b.ne            #0x8c97b4
    // 0x8c96c0: ldur            x7, [fp, #-8]
    // 0x8c96c4: LoadField: r0 = r7->field_13
    //     0x8c96c4: ldur            w0, [x7, #0x13]
    // 0x8c96c8: DecompressPointer r0
    //     0x8c96c8: add             x0, x0, HEAP, lsl #32
    // 0x8c96cc: b               #0x8c97a8
    // 0x8c96d0: ldur            x7, [fp, #-8]
    // 0x8c96d4: cmp             x0, #2
    // 0x8c96d8: b.lt            #0x8c97e8
    // 0x8c96dc: b               #0x8c96e4
    // 0x8c96e0: ldur            x7, [fp, #-8]
    // 0x8c96e4: add             x1, x4, #1
    // 0x8c96e8: cmp             x2, x1
    // 0x8c96ec: b.ne            #0x8c97f4
    // 0x8c96f0: r1 = 1
    //     0x8c96f0: movz            x1, #0x1
    // 0x8c96f4: ubfx            x0, x0, #0, #0x20
    // 0x8c96f8: and             x8, x0, x1
    // 0x8c96fc: mov             x1, x7
    // 0x8c9700: mov             x2, x6
    // 0x8c9704: mov             x5, x4
    // 0x8c9708: stur            x8, [fp, #-0x18]
    // 0x8c970c: r3 = 1
    //     0x8c970c: movz            x3, #0x1
    // 0x8c9710: r0 = _fromArray()
    //     0x8c9710: bl              #0x8d05ac  ; [package:pointycastle/ecc/ecc_base.dart] ECCurveBase::_fromArray
    // 0x8c9714: ldur            x1, [fp, #-0x18]
    // 0x8c9718: ubfx            x1, x1, #0, #0x20
    // 0x8c971c: mov             x2, x1
    // 0x8c9720: ldur            x1, [fp, #-8]
    // 0x8c9724: mov             x3, x0
    // 0x8c9728: r0 = decompressPoint()
    //     0x8c9728: bl              #0x8cf31c  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::decompressPoint
    // 0x8c972c: b               #0x8c97a8
    // 0x8c9730: cmp             x0, #6
    // 0x8c9734: b.gt            #0x8c974c
    // 0x8c9738: cmp             x0, #4
    // 0x8c973c: b.le            #0x8c9758
    // 0x8c9740: cmp             x0, #6
    // 0x8c9744: b.lt            #0x8c9820
    // 0x8c9748: b               #0x8c9758
    // 0x8c974c: lsl             x1, x0, #1
    // 0x8c9750: cmp             w1, #0xe
    // 0x8c9754: b.ne            #0x8c9858
    // 0x8c9758: lsl             x0, x4, #1
    // 0x8c975c: add             x1, x0, #1
    // 0x8c9760: cmp             x2, x1
    // 0x8c9764: b.ne            #0x8c982c
    // 0x8c9768: ldur            x1, [fp, #-8]
    // 0x8c976c: mov             x2, x6
    // 0x8c9770: mov             x5, x4
    // 0x8c9774: r3 = 1
    //     0x8c9774: movz            x3, #0x1
    // 0x8c9778: r0 = _fromArray()
    //     0x8c9778: bl              #0x8d05ac  ; [package:pointycastle/ecc/ecc_base.dart] ECCurveBase::_fromArray
    // 0x8c977c: ldur            x5, [fp, #-0x20]
    // 0x8c9780: stur            x0, [fp, #-0x28]
    // 0x8c9784: add             x3, x5, #1
    // 0x8c9788: ldur            x1, [fp, #-8]
    // 0x8c978c: ldur            x2, [fp, #-0x10]
    // 0x8c9790: r0 = _fromArray()
    //     0x8c9790: bl              #0x8d05ac  ; [package:pointycastle/ecc/ecc_base.dart] ECCurveBase::_fromArray
    // 0x8c9794: ldur            x1, [fp, #-8]
    // 0x8c9798: ldur            x2, [fp, #-0x28]
    // 0x8c979c: mov             x3, x0
    // 0x8c97a0: r5 = false
    //     0x8c97a0: add             x5, NULL, #0x30  ; false
    // 0x8c97a4: r0 = createPoint()
    //     0x8c97a4: bl              #0x8c98f0  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::createPoint
    // 0x8c97a8: LeaveFrame
    //     0x8c97a8: mov             SP, fp
    //     0x8c97ac: ldp             fp, lr, [SP], #0x10
    // 0x8c97b0: ret
    //     0x8c97b0: ret             
    // 0x8c97b4: r0 = ArgumentError()
    //     0x8c97b4: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c97b8: mov             x1, x0
    // 0x8c97bc: r0 = "Incorrect length for infinity encoding"
    //     0x8c97bc: add             x0, PP, #0x18, lsl #12  ; [pp+0x187a8] "Incorrect length for infinity encoding"
    //     0x8c97c0: ldr             x0, [x0, #0x7a8]
    // 0x8c97c4: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c97c4: stur            w0, [x1, #0x17]
    // 0x8c97c8: r2 = false
    //     0x8c97c8: add             x2, NULL, #0x30  ; false
    // 0x8c97cc: StoreField: r1->field_b = r2
    //     0x8c97cc: stur            w2, [x1, #0xb]
    // 0x8c97d0: mov             x0, x1
    // 0x8c97d4: r0 = Throw()
    //     0x8c97d4: bl              #0xec04b8  ; ThrowStub
    // 0x8c97d8: brk             #0
    // 0x8c97dc: r2 = false
    //     0x8c97dc: add             x2, NULL, #0x30  ; false
    // 0x8c97e0: mov             x3, x2
    // 0x8c97e4: b               #0x8c985c
    // 0x8c97e8: r2 = false
    //     0x8c97e8: add             x2, NULL, #0x30  ; false
    // 0x8c97ec: mov             x3, x2
    // 0x8c97f0: b               #0x8c985c
    // 0x8c97f4: r2 = false
    //     0x8c97f4: add             x2, NULL, #0x30  ; false
    // 0x8c97f8: r0 = ArgumentError()
    //     0x8c97f8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c97fc: mov             x1, x0
    // 0x8c9800: r0 = "Incorrect length for compressed encoding"
    //     0x8c9800: add             x0, PP, #0x18, lsl #12  ; [pp+0x187b0] "Incorrect length for compressed encoding"
    //     0x8c9804: ldr             x0, [x0, #0x7b0]
    // 0x8c9808: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c9808: stur            w0, [x1, #0x17]
    // 0x8c980c: r2 = false
    //     0x8c980c: add             x2, NULL, #0x30  ; false
    // 0x8c9810: StoreField: r1->field_b = r2
    //     0x8c9810: stur            w2, [x1, #0xb]
    // 0x8c9814: mov             x0, x1
    // 0x8c9818: r0 = Throw()
    //     0x8c9818: bl              #0xec04b8  ; ThrowStub
    // 0x8c981c: brk             #0
    // 0x8c9820: r2 = false
    //     0x8c9820: add             x2, NULL, #0x30  ; false
    // 0x8c9824: mov             x3, x2
    // 0x8c9828: b               #0x8c985c
    // 0x8c982c: r2 = false
    //     0x8c982c: add             x2, NULL, #0x30  ; false
    // 0x8c9830: r0 = ArgumentError()
    //     0x8c9830: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c9834: mov             x1, x0
    // 0x8c9838: r0 = "Incorrect length for uncompressed/hybrid encoding"
    //     0x8c9838: add             x0, PP, #0x18, lsl #12  ; [pp+0x187b8] "Incorrect length for uncompressed/hybrid encoding"
    //     0x8c983c: ldr             x0, [x0, #0x7b8]
    // 0x8c9840: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c9840: stur            w0, [x1, #0x17]
    // 0x8c9844: r3 = false
    //     0x8c9844: add             x3, NULL, #0x30  ; false
    // 0x8c9848: StoreField: r1->field_b = r3
    //     0x8c9848: stur            w3, [x1, #0xb]
    // 0x8c984c: mov             x0, x1
    // 0x8c9850: r0 = Throw()
    //     0x8c9850: bl              #0xec04b8  ; ThrowStub
    // 0x8c9854: brk             #0
    // 0x8c9858: r3 = false
    //     0x8c9858: add             x3, NULL, #0x30  ; false
    // 0x8c985c: r1 = Null
    //     0x8c985c: mov             x1, NULL
    // 0x8c9860: r2 = 4
    //     0x8c9860: movz            x2, #0x4
    // 0x8c9864: r0 = AllocateArray()
    //     0x8c9864: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c9868: stur            x0, [fp, #-8]
    // 0x8c986c: r16 = "Invalid point encoding 0x"
    //     0x8c986c: add             x16, PP, #0x18, lsl #12  ; [pp+0x187c0] "Invalid point encoding 0x"
    //     0x8c9870: ldr             x16, [x16, #0x7c0]
    // 0x8c9874: StoreField: r0->field_f = r16
    //     0x8c9874: stur            w16, [x0, #0xf]
    // 0x8c9878: ldur            x1, [fp, #-0x30]
    // 0x8c987c: lsl             x2, x1, #1
    // 0x8c9880: mov             x1, x2
    // 0x8c9884: r0 = _toPow2String()
    //     0x8c9884: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0x8c9888: ldur            x1, [fp, #-8]
    // 0x8c988c: ArrayStore: r1[1] = r0  ; List_4
    //     0x8c988c: add             x25, x1, #0x13
    //     0x8c9890: str             w0, [x25]
    //     0x8c9894: tbz             w0, #0, #0x8c98b0
    //     0x8c9898: ldurb           w16, [x1, #-1]
    //     0x8c989c: ldurb           w17, [x0, #-1]
    //     0x8c98a0: and             x16, x17, x16, lsr #2
    //     0x8c98a4: tst             x16, HEAP, lsr #32
    //     0x8c98a8: b.eq            #0x8c98b0
    //     0x8c98ac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8c98b0: ldur            x16, [fp, #-8]
    // 0x8c98b4: str             x16, [SP]
    // 0x8c98b8: r0 = _interpolate()
    //     0x8c98b8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c98bc: stur            x0, [fp, #-8]
    // 0x8c98c0: r0 = ArgumentError()
    //     0x8c98c0: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c98c4: mov             x1, x0
    // 0x8c98c8: ldur            x0, [fp, #-8]
    // 0x8c98cc: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c98cc: stur            w0, [x1, #0x17]
    // 0x8c98d0: r0 = false
    //     0x8c98d0: add             x0, NULL, #0x30  ; false
    // 0x8c98d4: StoreField: r1->field_b = r0
    //     0x8c98d4: stur            w0, [x1, #0xb]
    // 0x8c98d8: mov             x0, x1
    // 0x8c98dc: r0 = Throw()
    //     0x8c98dc: bl              #0xec04b8  ; ThrowStub
    // 0x8c98e0: brk             #0
    // 0x8c98e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c98e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c98e8: b               #0x8c965c
    // 0x8c98ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8c98ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _fromArray(/* No info */) {
    // ** addr: 0x8d05ac, size: 0x68
    // 0x8d05ac: EnterFrame
    //     0x8d05ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8d05b0: mov             fp, SP
    // 0x8d05b4: AllocStack(0x8)
    //     0x8d05b4: sub             SP, SP, #8
    // 0x8d05b8: SetupParameters(dynamic _ /* r2 => r3 */, dynamic _ /* r3 => r2 */)
    //     0x8d05b8: mov             x16, x3
    //     0x8d05bc: mov             x3, x2
    //     0x8d05c0: mov             x2, x16
    // 0x8d05c4: CheckStackOverflow
    //     0x8d05c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d05c8: cmp             SP, x16
    //     0x8d05cc: b.ls            #0x8d060c
    // 0x8d05d0: add             x4, x2, x5
    // 0x8d05d4: r0 = BoxInt64Instr(r4)
    //     0x8d05d4: sbfiz           x0, x4, #1, #0x1f
    //     0x8d05d8: cmp             x4, x0, asr #1
    //     0x8d05dc: b.eq            #0x8d05e8
    //     0x8d05e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d05e4: stur            x4, [x0, #7]
    // 0x8d05e8: str             x0, [SP]
    // 0x8d05ec: mov             x1, x3
    // 0x8d05f0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d05f0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d05f4: r0 = sublist()
    //     0x8d05f4: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0x8d05f8: mov             x1, x0
    // 0x8d05fc: r0 = decodeBigIntWithSign()
    //     0x8d05fc: bl              #0x8d0614  ; [package:pointycastle/src/utils.dart] ::decodeBigIntWithSign
    // 0x8d0600: LeaveFrame
    //     0x8d0600: mov             SP, fp
    //     0x8d0604: ldp             fp, lr, [SP], #0x10
    // 0x8d0608: ret
    //     0x8d0608: ret             
    // 0x8d060c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d060c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0610: b               #0x8d05d0
  }
  _ ECCurveBase(/* No info */) {
    // ** addr: 0x8d0d78, size: 0x94
    // 0x8d0d78: EnterFrame
    //     0x8d0d78: stp             fp, lr, [SP, #-0x10]!
    //     0x8d0d7c: mov             fp, SP
    // 0x8d0d80: AllocStack(0x10)
    //     0x8d0d80: sub             SP, SP, #0x10
    // 0x8d0d84: SetupParameters(ECCurveBase this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x8d0d84: mov             x0, x3
    //     0x8d0d88: stur            x3, [fp, #-0x10]
    //     0x8d0d8c: mov             x3, x1
    //     0x8d0d90: stur            x1, [fp, #-8]
    // 0x8d0d94: CheckStackOverflow
    //     0x8d0d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d0d98: cmp             SP, x16
    //     0x8d0d9c: b.ls            #0x8d0e04
    // 0x8d0da0: mov             x1, x3
    // 0x8d0da4: r0 = fromBigInteger()
    //     0x8d0da4: bl              #0x8cf2c0  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::fromBigInteger
    // 0x8d0da8: ldur            x3, [fp, #-8]
    // 0x8d0dac: StoreField: r3->field_7 = r0
    //     0x8d0dac: stur            w0, [x3, #7]
    //     0x8d0db0: ldurb           w16, [x3, #-1]
    //     0x8d0db4: ldurb           w17, [x0, #-1]
    //     0x8d0db8: and             x16, x17, x16, lsr #2
    //     0x8d0dbc: tst             x16, HEAP, lsr #32
    //     0x8d0dc0: b.eq            #0x8d0dc8
    //     0x8d0dc4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d0dc8: mov             x1, x3
    // 0x8d0dcc: ldur            x2, [fp, #-0x10]
    // 0x8d0dd0: r0 = fromBigInteger()
    //     0x8d0dd0: bl              #0x8cf2c0  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::fromBigInteger
    // 0x8d0dd4: ldur            x1, [fp, #-8]
    // 0x8d0dd8: StoreField: r1->field_b = r0
    //     0x8d0dd8: stur            w0, [x1, #0xb]
    //     0x8d0ddc: ldurb           w16, [x1, #-1]
    //     0x8d0de0: ldurb           w17, [x0, #-1]
    //     0x8d0de4: and             x16, x17, x16, lsr #2
    //     0x8d0de8: tst             x16, HEAP, lsr #32
    //     0x8d0dec: b.eq            #0x8d0df4
    //     0x8d0df0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d0df4: r0 = Null
    //     0x8d0df4: mov             x0, NULL
    // 0x8d0df8: LeaveFrame
    //     0x8d0df8: mov             SP, fp
    //     0x8d0dfc: ldp             fp, lr, [SP], #0x10
    // 0x8d0e00: ret
    //     0x8d0e00: ret             
    // 0x8d0e04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0e04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0e08: b               #0x8d0da0
  }
}

// class id: 594, size: 0x18, field offset: 0x8
abstract class ECPointBase extends Object
    implements ECPoint {

  ECPointBase? *(ECPointBase, BigInt?) {
    // ** addr: 0x8cf278, size: 0x60
    // 0x8cf278: EnterFrame
    //     0x8cf278: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf27c: mov             fp, SP
    // 0x8cf280: ldr             x0, [fp, #0x10]
    // 0x8cf284: r2 = Null
    //     0x8cf284: mov             x2, NULL
    // 0x8cf288: r1 = Null
    //     0x8cf288: mov             x1, NULL
    // 0x8cf28c: r4 = LoadClassIdInstr(r0)
    //     0x8cf28c: ldur            x4, [x0, #-1]
    //     0x8cf290: ubfx            x4, x4, #0xc, #0x14
    // 0x8cf294: r17 = 7196
    //     0x8cf294: movz            x17, #0x1c1c
    // 0x8cf298: cmp             x4, x17
    // 0x8cf29c: b.eq            #0x8cf2b4
    // 0x8cf2a0: r8 = BigInt?
    //     0x8cf2a0: add             x8, PP, #0x39, lsl #12  ; [pp+0x39390] Type: BigInt?
    //     0x8cf2a4: ldr             x8, [x8, #0x390]
    // 0x8cf2a8: r3 = Null
    //     0x8cf2a8: add             x3, PP, #0x39, lsl #12  ; [pp+0x39398] Null
    //     0x8cf2ac: ldr             x3, [x3, #0x398]
    // 0x8cf2b0: r0 = DefaultNullableTypeTest()
    //     0x8cf2b0: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x8cf2b4: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x8cf2b4: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x8cf2b8: r0 = Throw()
    //     0x8cf2b8: bl              #0xec04b8  ; ThrowStub
    // 0x8cf2bc: brk             #0
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3038, size: 0xe4
    // 0xbf3038: EnterFrame
    //     0xbf3038: stp             fp, lr, [SP, #-0x10]!
    //     0xbf303c: mov             fp, SP
    // 0xbf3040: AllocStack(0x10)
    //     0xbf3040: sub             SP, SP, #0x10
    // 0xbf3044: CheckStackOverflow
    //     0xbf3044: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3048: cmp             SP, x16
    //     0xbf304c: b.ls            #0xbf3114
    // 0xbf3050: ldr             x1, [fp, #0x10]
    // 0xbf3054: LoadField: r0 = r1->field_b
    //     0xbf3054: ldur            w0, [x1, #0xb]
    // 0xbf3058: DecompressPointer r0
    //     0xbf3058: add             x0, x0, HEAP, lsl #32
    // 0xbf305c: cmp             w0, NULL
    // 0xbf3060: b.ne            #0xbf3084
    // 0xbf3064: LoadField: r2 = r1->field_f
    //     0xbf3064: ldur            w2, [x1, #0xf]
    // 0xbf3068: DecompressPointer r2
    //     0xbf3068: add             x2, x2, HEAP, lsl #32
    // 0xbf306c: cmp             w2, NULL
    // 0xbf3070: b.ne            #0xbf3084
    // 0xbf3074: r0 = 0
    //     0xbf3074: movz            x0, #0
    // 0xbf3078: LeaveFrame
    //     0xbf3078: mov             SP, fp
    //     0xbf307c: ldp             fp, lr, [SP], #0x10
    // 0xbf3080: ret
    //     0xbf3080: ret             
    // 0xbf3084: r2 = LoadClassIdInstr(r0)
    //     0xbf3084: ldur            x2, [x0, #-1]
    //     0xbf3088: ubfx            x2, x2, #0xc, #0x14
    // 0xbf308c: str             x0, [SP]
    // 0xbf3090: mov             x0, x2
    // 0xbf3094: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3094: movz            x17, #0x64af
    //     0xbf3098: add             lr, x0, x17
    //     0xbf309c: ldr             lr, [x21, lr, lsl #3]
    //     0xbf30a0: blr             lr
    // 0xbf30a4: mov             x1, x0
    // 0xbf30a8: ldr             x0, [fp, #0x10]
    // 0xbf30ac: stur            x1, [fp, #-8]
    // 0xbf30b0: LoadField: r2 = r0->field_f
    //     0xbf30b0: ldur            w2, [x0, #0xf]
    // 0xbf30b4: DecompressPointer r2
    //     0xbf30b4: add             x2, x2, HEAP, lsl #32
    // 0xbf30b8: r0 = LoadClassIdInstr(r2)
    //     0xbf30b8: ldur            x0, [x2, #-1]
    //     0xbf30bc: ubfx            x0, x0, #0xc, #0x14
    // 0xbf30c0: str             x2, [SP]
    // 0xbf30c4: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf30c4: movz            x17, #0x64af
    //     0xbf30c8: add             lr, x0, x17
    //     0xbf30cc: ldr             lr, [x21, lr, lsl #3]
    //     0xbf30d0: blr             lr
    // 0xbf30d4: ldur            x2, [fp, #-8]
    // 0xbf30d8: r3 = LoadInt32Instr(r2)
    //     0xbf30d8: sbfx            x3, x2, #1, #0x1f
    //     0xbf30dc: tbz             w2, #0, #0xbf30e4
    //     0xbf30e0: ldur            x3, [x2, #7]
    // 0xbf30e4: r2 = LoadInt32Instr(r0)
    //     0xbf30e4: sbfx            x2, x0, #1, #0x1f
    //     0xbf30e8: tbz             w0, #0, #0xbf30f0
    //     0xbf30ec: ldur            x2, [x0, #7]
    // 0xbf30f0: eor             x4, x3, x2
    // 0xbf30f4: r0 = BoxInt64Instr(r4)
    //     0xbf30f4: sbfiz           x0, x4, #1, #0x1f
    //     0xbf30f8: cmp             x4, x0, asr #1
    //     0xbf30fc: b.eq            #0xbf3108
    //     0xbf3100: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3104: stur            x4, [x0, #7]
    // 0xbf3108: LeaveFrame
    //     0xbf3108: mov             SP, fp
    //     0xbf310c: ldp             fp, lr, [SP], #0x10
    // 0xbf3110: ret
    //     0xbf3110: ret             
    // 0xbf3114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf3118: b               #0xbf3050
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3f43c, size: 0x7c
    // 0xc3f43c: EnterFrame
    //     0xc3f43c: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f440: mov             fp, SP
    // 0xc3f444: AllocStack(0x8)
    //     0xc3f444: sub             SP, SP, #8
    // 0xc3f448: CheckStackOverflow
    //     0xc3f448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f44c: cmp             SP, x16
    //     0xc3f450: b.ls            #0xc3f4b0
    // 0xc3f454: r1 = Null
    //     0xc3f454: mov             x1, NULL
    // 0xc3f458: r2 = 10
    //     0xc3f458: movz            x2, #0xa
    // 0xc3f45c: r0 = AllocateArray()
    //     0xc3f45c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3f460: r16 = "("
    //     0xc3f460: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xc3f464: ldr             x16, [x16, #0xf08]
    // 0xc3f468: StoreField: r0->field_f = r16
    //     0xc3f468: stur            w16, [x0, #0xf]
    // 0xc3f46c: ldr             x1, [fp, #0x10]
    // 0xc3f470: LoadField: r2 = r1->field_b
    //     0xc3f470: ldur            w2, [x1, #0xb]
    // 0xc3f474: DecompressPointer r2
    //     0xc3f474: add             x2, x2, HEAP, lsl #32
    // 0xc3f478: StoreField: r0->field_13 = r2
    //     0xc3f478: stur            w2, [x0, #0x13]
    // 0xc3f47c: r16 = ","
    //     0xc3f47c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xc3f480: ldr             x16, [x16, #0x5f8]
    // 0xc3f484: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3f484: stur            w16, [x0, #0x17]
    // 0xc3f488: LoadField: r2 = r1->field_f
    //     0xc3f488: ldur            w2, [x1, #0xf]
    // 0xc3f48c: DecompressPointer r2
    //     0xc3f48c: add             x2, x2, HEAP, lsl #32
    // 0xc3f490: StoreField: r0->field_1b = r2
    //     0xc3f490: stur            w2, [x0, #0x1b]
    // 0xc3f494: r16 = ")"
    //     0xc3f494: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3f498: StoreField: r0->field_1f = r16
    //     0xc3f498: stur            w16, [x0, #0x1f]
    // 0xc3f49c: str             x0, [SP]
    // 0xc3f4a0: r0 = _interpolate()
    //     0xc3f4a0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3f4a4: LeaveFrame
    //     0xc3f4a4: mov             SP, fp
    //     0xc3f4a8: ldp             fp, lr, [SP], #0x10
    // 0xc3f4ac: ret
    //     0xc3f4ac: ret             
    // 0xc3f4b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f4b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f4b4: b               #0xc3f454
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d36c, size: 0x134
    // 0xd7d36c: EnterFrame
    //     0xd7d36c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7d370: mov             fp, SP
    // 0xd7d374: AllocStack(0x10)
    //     0xd7d374: sub             SP, SP, #0x10
    // 0xd7d378: CheckStackOverflow
    //     0xd7d378: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7d37c: cmp             SP, x16
    //     0xd7d380: b.ls            #0xd7d498
    // 0xd7d384: ldr             x1, [fp, #0x10]
    // 0xd7d388: cmp             w1, NULL
    // 0xd7d38c: b.ne            #0xd7d3a0
    // 0xd7d390: r0 = false
    //     0xd7d390: add             x0, NULL, #0x30  ; false
    // 0xd7d394: LeaveFrame
    //     0xd7d394: mov             SP, fp
    //     0xd7d398: ldp             fp, lr, [SP], #0x10
    // 0xd7d39c: ret
    //     0xd7d39c: ret             
    // 0xd7d3a0: r0 = 60
    //     0xd7d3a0: movz            x0, #0x3c
    // 0xd7d3a4: branchIfSmi(r1, 0xd7d3b0)
    //     0xd7d3a4: tbz             w1, #0, #0xd7d3b0
    // 0xd7d3a8: r0 = LoadClassIdInstr(r1)
    //     0xd7d3a8: ldur            x0, [x1, #-1]
    //     0xd7d3ac: ubfx            x0, x0, #0xc, #0x14
    // 0xd7d3b0: cmp             x0, #0x253
    // 0xd7d3b4: b.ne            #0xd7d488
    // 0xd7d3b8: ldr             x2, [fp, #0x18]
    // 0xd7d3bc: LoadField: r0 = r2->field_b
    //     0xd7d3bc: ldur            w0, [x2, #0xb]
    // 0xd7d3c0: DecompressPointer r0
    //     0xd7d3c0: add             x0, x0, HEAP, lsl #32
    // 0xd7d3c4: cmp             w0, NULL
    // 0xd7d3c8: b.ne            #0xd7d41c
    // 0xd7d3cc: LoadField: r3 = r2->field_f
    //     0xd7d3cc: ldur            w3, [x2, #0xf]
    // 0xd7d3d0: DecompressPointer r3
    //     0xd7d3d0: add             x3, x3, HEAP, lsl #32
    // 0xd7d3d4: cmp             w3, NULL
    // 0xd7d3d8: b.ne            #0xd7d41c
    // 0xd7d3dc: LoadField: r0 = r1->field_b
    //     0xd7d3dc: ldur            w0, [x1, #0xb]
    // 0xd7d3e0: DecompressPointer r0
    //     0xd7d3e0: add             x0, x0, HEAP, lsl #32
    // 0xd7d3e4: cmp             w0, NULL
    // 0xd7d3e8: b.ne            #0xd7d40c
    // 0xd7d3ec: LoadField: r0 = r1->field_f
    //     0xd7d3ec: ldur            w0, [x1, #0xf]
    // 0xd7d3f0: DecompressPointer r0
    //     0xd7d3f0: add             x0, x0, HEAP, lsl #32
    // 0xd7d3f4: cmp             w0, NULL
    // 0xd7d3f8: r16 = true
    //     0xd7d3f8: add             x16, NULL, #0x20  ; true
    // 0xd7d3fc: r17 = false
    //     0xd7d3fc: add             x17, NULL, #0x30  ; false
    // 0xd7d400: csel            x1, x16, x17, eq
    // 0xd7d404: mov             x0, x1
    // 0xd7d408: b               #0xd7d410
    // 0xd7d40c: r0 = false
    //     0xd7d40c: add             x0, NULL, #0x30  ; false
    // 0xd7d410: LeaveFrame
    //     0xd7d410: mov             SP, fp
    //     0xd7d414: ldp             fp, lr, [SP], #0x10
    // 0xd7d418: ret
    //     0xd7d418: ret             
    // 0xd7d41c: LoadField: r3 = r1->field_b
    //     0xd7d41c: ldur            w3, [x1, #0xb]
    // 0xd7d420: DecompressPointer r3
    //     0xd7d420: add             x3, x3, HEAP, lsl #32
    // 0xd7d424: r4 = LoadClassIdInstr(r0)
    //     0xd7d424: ldur            x4, [x0, #-1]
    //     0xd7d428: ubfx            x4, x4, #0xc, #0x14
    // 0xd7d42c: stp             x3, x0, [SP]
    // 0xd7d430: mov             x0, x4
    // 0xd7d434: mov             lr, x0
    // 0xd7d438: ldr             lr, [x21, lr, lsl #3]
    // 0xd7d43c: blr             lr
    // 0xd7d440: tbnz            w0, #4, #0xd7d478
    // 0xd7d444: ldr             x1, [fp, #0x18]
    // 0xd7d448: ldr             x0, [fp, #0x10]
    // 0xd7d44c: LoadField: r2 = r1->field_f
    //     0xd7d44c: ldur            w2, [x1, #0xf]
    // 0xd7d450: DecompressPointer r2
    //     0xd7d450: add             x2, x2, HEAP, lsl #32
    // 0xd7d454: LoadField: r1 = r0->field_f
    //     0xd7d454: ldur            w1, [x0, #0xf]
    // 0xd7d458: DecompressPointer r1
    //     0xd7d458: add             x1, x1, HEAP, lsl #32
    // 0xd7d45c: r0 = LoadClassIdInstr(r2)
    //     0xd7d45c: ldur            x0, [x2, #-1]
    //     0xd7d460: ubfx            x0, x0, #0xc, #0x14
    // 0xd7d464: stp             x1, x2, [SP]
    // 0xd7d468: mov             lr, x0
    // 0xd7d46c: ldr             lr, [x21, lr, lsl #3]
    // 0xd7d470: blr             lr
    // 0xd7d474: b               #0xd7d47c
    // 0xd7d478: r0 = false
    //     0xd7d478: add             x0, NULL, #0x30  ; false
    // 0xd7d47c: LeaveFrame
    //     0xd7d47c: mov             SP, fp
    //     0xd7d480: ldp             fp, lr, [SP], #0x10
    // 0xd7d484: ret
    //     0xd7d484: ret             
    // 0xd7d488: r0 = false
    //     0xd7d488: add             x0, NULL, #0x30  ; false
    // 0xd7d48c: LeaveFrame
    //     0xd7d48c: mov             SP, fp
    //     0xd7d490: ldp             fp, lr, [SP], #0x10
    // 0xd7d494: ret
    //     0xd7d494: ret             
    // 0xd7d498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7d498: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7d49c: b               #0xd7d384
  }
}

// class id: 596, size: 0x8, field offset: 0x8
abstract class ECFieldElementBase extends Object
    implements ECFieldElement {

  _ toString(/* No info */) {
    // ** addr: 0xc3f3fc, size: 0x40
    // 0xc3f3fc: EnterFrame
    //     0xc3f3fc: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f400: mov             fp, SP
    // 0xc3f404: AllocStack(0x8)
    //     0xc3f404: sub             SP, SP, #8
    // 0xc3f408: CheckStackOverflow
    //     0xc3f408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f40c: cmp             SP, x16
    //     0xc3f410: b.ls            #0xc3f434
    // 0xc3f414: ldr             x0, [fp, #0x10]
    // 0xc3f418: LoadField: r1 = r0->field_b
    //     0xc3f418: ldur            w1, [x0, #0xb]
    // 0xc3f41c: DecompressPointer r1
    //     0xc3f41c: add             x1, x1, HEAP, lsl #32
    // 0xc3f420: str             x1, [SP]
    // 0xc3f424: r0 = toString()
    //     0xc3f424: bl              #0xbff3f4  ; [dart:core] _BigIntImpl::toString
    // 0xc3f428: LeaveFrame
    //     0xc3f428: mov             SP, fp
    //     0xc3f42c: ldp             fp, lr, [SP], #0x10
    // 0xc3f430: ret
    //     0xc3f430: ret             
    // 0xc3f434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f438: b               #0xc3f414
  }
}

// class id: 598, size: 0xc, field offset: 0x8
abstract class ECDomainParametersImpl extends Object
    implements ECDomainParameters {
}
