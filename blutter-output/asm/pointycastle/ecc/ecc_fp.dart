// lib: impl.ecc.ecc_fp, url: package:pointycastle/ecc/ecc_fp.dart

// class id: 1051005, size: 0x8
class :: {

  static _ _testBit(/* No info */) {
    // ** addr: 0x8cf510, size: 0xa4
    // 0x8cf510: EnterFrame
    //     0x8cf510: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf514: mov             fp, SP
    // 0x8cf518: AllocStack(0x20)
    //     0x8cf518: sub             SP, SP, #0x20
    // 0x8cf51c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8cf51c: stur            x1, [fp, #-8]
    //     0x8cf520: stur            x2, [fp, #-0x10]
    // 0x8cf524: CheckStackOverflow
    //     0x8cf524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf528: cmp             SP, x16
    //     0x8cf52c: b.ls            #0x8cf5ac
    // 0x8cf530: r0 = InitLateStaticField(0x334) // [dart:core] _BigIntImpl::one
    //     0x8cf530: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8cf534: ldr             x0, [x0, #0x668]
    //     0x8cf538: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8cf53c: cmp             w0, w16
    //     0x8cf540: b.ne            #0x8cf550
    //     0x8cf544: add             x2, PP, #0x18, lsl #12  ; [pp+0x18820] Field <<EMAIL>>: static late final (offset: 0x334)
    //     0x8cf548: ldr             x2, [x2, #0x820]
    //     0x8cf54c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8cf550: mov             x1, x0
    // 0x8cf554: ldur            x2, [fp, #-0x10]
    // 0x8cf558: r0 = <<()
    //     0x8cf558: bl              #0x8cc42c  ; [dart:core] _BigIntImpl::<<
    // 0x8cf55c: ldur            x1, [fp, #-8]
    // 0x8cf560: mov             x2, x0
    // 0x8cf564: r0 = &()
    //     0x8cf564: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8cf568: stur            x0, [fp, #-8]
    // 0x8cf56c: r0 = InitLateStaticField(0x330) // [dart:core] _BigIntImpl::zero
    //     0x8cf56c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8cf570: ldr             x0, [x0, #0x660]
    //     0x8cf574: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8cf578: cmp             w0, w16
    //     0x8cf57c: b.ne            #0x8cf58c
    //     0x8cf580: add             x2, PP, #0x18, lsl #12  ; [pp+0x18818] Field <<EMAIL>>: static late final (offset: 0x330)
    //     0x8cf584: ldr             x2, [x2, #0x818]
    //     0x8cf588: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8cf58c: ldur            x16, [fp, #-8]
    // 0x8cf590: stp             x0, x16, [SP]
    // 0x8cf594: r0 = ==()
    //     0x8cf594: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8cf598: eor             x1, x0, #0x10
    // 0x8cf59c: mov             x0, x1
    // 0x8cf5a0: LeaveFrame
    //     0x8cf5a0: mov             SP, fp
    //     0x8cf5a4: ldp             fp, lr, [SP], #0x10
    // 0x8cf5a8: ret
    //     0x8cf5a8: ret             
    // 0x8cf5ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cf5ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cf5b0: b               #0x8cf530
  }
  static _ _lbit(/* No info */) {
    // ** addr: 0x8d0288, size: 0x2bc
    // 0x8d0288: EnterFrame
    //     0x8d0288: stp             fp, lr, [SP, #-0x10]!
    //     0x8d028c: mov             fp, SP
    // 0x8d0290: AllocStack(0x28)
    //     0x8d0290: sub             SP, SP, #0x28
    // 0x8d0294: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8d0294: stur            x1, [fp, #-8]
    // 0x8d0298: CheckStackOverflow
    //     0x8d0298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d029c: cmp             SP, x16
    //     0x8d02a0: b.ls            #0x8d0534
    // 0x8d02a4: r0 = InitLateStaticField(0x330) // [dart:core] _BigIntImpl::zero
    //     0x8d02a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d02a8: ldr             x0, [x0, #0x660]
    //     0x8d02ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d02b0: cmp             w0, w16
    //     0x8d02b4: b.ne            #0x8d02c4
    //     0x8d02b8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18818] Field <<EMAIL>>: static late final (offset: 0x330)
    //     0x8d02bc: ldr             x2, [x2, #0x818]
    //     0x8d02c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d02c4: stur            x0, [fp, #-0x10]
    // 0x8d02c8: ldur            x16, [fp, #-8]
    // 0x8d02cc: stp             x0, x16, [SP]
    // 0x8d02d0: r0 = ==()
    //     0x8d02d0: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8d02d4: tbnz            w0, #4, #0x8d02e8
    // 0x8d02d8: r0 = -1
    //     0x8d02d8: movn            x0, #0
    // 0x8d02dc: LeaveFrame
    //     0x8d02dc: mov             SP, fp
    //     0x8d02e0: ldp             fp, lr, [SP], #0x10
    // 0x8d02e4: ret
    //     0x8d02e4: ret             
    // 0x8d02e8: ldur            x3, [fp, #-8]
    // 0x8d02ec: r0 = 0
    //     0x8d02ec: movz            x0, #0
    // 0x8d02f0: stur            x3, [fp, #-8]
    // 0x8d02f4: stur            x0, [fp, #-0x18]
    // 0x8d02f8: CheckStackOverflow
    //     0x8d02f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d02fc: cmp             SP, x16
    //     0x8d0300: b.ls            #0x8d053c
    // 0x8d0304: r1 = Null
    //     0x8d0304: mov             x1, NULL
    // 0x8d0308: r2 = 4294967295
    //     0x8d0308: orr             x2, xzr, #0xffffffff
    // 0x8d030c: r0 = _BigIntImpl.from()
    //     0x8d030c: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8d0310: ldur            x1, [fp, #-8]
    // 0x8d0314: mov             x2, x0
    // 0x8d0318: r0 = &()
    //     0x8d0318: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8d031c: mov             x1, x0
    // 0x8d0320: ldur            x2, [fp, #-0x10]
    // 0x8d0324: r0 = compareTo()
    //     0x8d0324: bl              #0x5f6dc8  ; [dart:core] _BigIntImpl::compareTo
    // 0x8d0328: cbnz            x0, #0x8d0354
    // 0x8d032c: ldur            x0, [fp, #-0x18]
    // 0x8d0330: ldur            x1, [fp, #-8]
    // 0x8d0334: r2 = 32
    //     0x8d0334: movz            x2, #0x20
    // 0x8d0338: r0 = >>()
    //     0x8d0338: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8d033c: mov             x1, x0
    // 0x8d0340: ldur            x0, [fp, #-0x18]
    // 0x8d0344: add             x2, x0, #0x20
    // 0x8d0348: mov             x3, x1
    // 0x8d034c: mov             x0, x2
    // 0x8d0350: b               #0x8d02f0
    // 0x8d0354: ldur            x0, [fp, #-0x18]
    // 0x8d0358: r1 = Null
    //     0x8d0358: mov             x1, NULL
    // 0x8d035c: r2 = 65535
    //     0x8d035c: orr             x2, xzr, #0xffff
    // 0x8d0360: r0 = _BigIntImpl.from()
    //     0x8d0360: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8d0364: ldur            x1, [fp, #-8]
    // 0x8d0368: mov             x2, x0
    // 0x8d036c: r0 = &()
    //     0x8d036c: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8d0370: ldur            x16, [fp, #-0x10]
    // 0x8d0374: stp             x16, x0, [SP]
    // 0x8d0378: r0 = ==()
    //     0x8d0378: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8d037c: tbnz            w0, #4, #0x8d03a8
    // 0x8d0380: ldur            x0, [fp, #-0x18]
    // 0x8d0384: ldur            x1, [fp, #-8]
    // 0x8d0388: r2 = 16
    //     0x8d0388: movz            x2, #0x10
    // 0x8d038c: r0 = >>()
    //     0x8d038c: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8d0390: mov             x1, x0
    // 0x8d0394: ldur            x0, [fp, #-0x18]
    // 0x8d0398: add             x2, x0, #0x10
    // 0x8d039c: mov             x3, x1
    // 0x8d03a0: mov             x0, x2
    // 0x8d03a4: b               #0x8d03b0
    // 0x8d03a8: ldur            x0, [fp, #-0x18]
    // 0x8d03ac: ldur            x3, [fp, #-8]
    // 0x8d03b0: stur            x3, [fp, #-8]
    // 0x8d03b4: stur            x0, [fp, #-0x18]
    // 0x8d03b8: r1 = Null
    //     0x8d03b8: mov             x1, NULL
    // 0x8d03bc: r2 = 255
    //     0x8d03bc: movz            x2, #0xff
    // 0x8d03c0: r0 = _BigIntImpl.from()
    //     0x8d03c0: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8d03c4: ldur            x1, [fp, #-8]
    // 0x8d03c8: mov             x2, x0
    // 0x8d03cc: r0 = &()
    //     0x8d03cc: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8d03d0: ldur            x16, [fp, #-0x10]
    // 0x8d03d4: stp             x16, x0, [SP]
    // 0x8d03d8: r0 = ==()
    //     0x8d03d8: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8d03dc: tbnz            w0, #4, #0x8d0408
    // 0x8d03e0: ldur            x0, [fp, #-0x18]
    // 0x8d03e4: ldur            x1, [fp, #-8]
    // 0x8d03e8: r2 = 8
    //     0x8d03e8: movz            x2, #0x8
    // 0x8d03ec: r0 = >>()
    //     0x8d03ec: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8d03f0: mov             x1, x0
    // 0x8d03f4: ldur            x0, [fp, #-0x18]
    // 0x8d03f8: add             x2, x0, #8
    // 0x8d03fc: mov             x3, x1
    // 0x8d0400: mov             x0, x2
    // 0x8d0404: b               #0x8d0410
    // 0x8d0408: ldur            x0, [fp, #-0x18]
    // 0x8d040c: ldur            x3, [fp, #-8]
    // 0x8d0410: stur            x3, [fp, #-8]
    // 0x8d0414: stur            x0, [fp, #-0x18]
    // 0x8d0418: r1 = Null
    //     0x8d0418: mov             x1, NULL
    // 0x8d041c: r2 = 15
    //     0x8d041c: movz            x2, #0xf
    // 0x8d0420: r0 = _BigIntImpl.from()
    //     0x8d0420: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8d0424: ldur            x1, [fp, #-8]
    // 0x8d0428: mov             x2, x0
    // 0x8d042c: r0 = &()
    //     0x8d042c: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8d0430: ldur            x16, [fp, #-0x10]
    // 0x8d0434: stp             x16, x0, [SP]
    // 0x8d0438: r0 = ==()
    //     0x8d0438: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8d043c: tbnz            w0, #4, #0x8d0468
    // 0x8d0440: ldur            x0, [fp, #-0x18]
    // 0x8d0444: ldur            x1, [fp, #-8]
    // 0x8d0448: r2 = 4
    //     0x8d0448: movz            x2, #0x4
    // 0x8d044c: r0 = >>()
    //     0x8d044c: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8d0450: mov             x1, x0
    // 0x8d0454: ldur            x0, [fp, #-0x18]
    // 0x8d0458: add             x2, x0, #4
    // 0x8d045c: mov             x3, x1
    // 0x8d0460: mov             x0, x2
    // 0x8d0464: b               #0x8d0470
    // 0x8d0468: ldur            x0, [fp, #-0x18]
    // 0x8d046c: ldur            x3, [fp, #-8]
    // 0x8d0470: stur            x3, [fp, #-8]
    // 0x8d0474: stur            x0, [fp, #-0x18]
    // 0x8d0478: r1 = Null
    //     0x8d0478: mov             x1, NULL
    // 0x8d047c: r2 = 3
    //     0x8d047c: movz            x2, #0x3
    // 0x8d0480: r0 = _BigIntImpl.from()
    //     0x8d0480: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8d0484: ldur            x1, [fp, #-8]
    // 0x8d0488: mov             x2, x0
    // 0x8d048c: r0 = &()
    //     0x8d048c: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8d0490: ldur            x16, [fp, #-0x10]
    // 0x8d0494: stp             x16, x0, [SP]
    // 0x8d0498: r0 = ==()
    //     0x8d0498: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8d049c: tbnz            w0, #4, #0x8d04c4
    // 0x8d04a0: ldur            x0, [fp, #-0x18]
    // 0x8d04a4: ldur            x1, [fp, #-8]
    // 0x8d04a8: r2 = 2
    //     0x8d04a8: movz            x2, #0x2
    // 0x8d04ac: r0 = >>()
    //     0x8d04ac: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8d04b0: mov             x1, x0
    // 0x8d04b4: ldur            x0, [fp, #-0x18]
    // 0x8d04b8: add             x2, x0, #2
    // 0x8d04bc: mov             x0, x2
    // 0x8d04c0: b               #0x8d04cc
    // 0x8d04c4: ldur            x0, [fp, #-0x18]
    // 0x8d04c8: ldur            x1, [fp, #-8]
    // 0x8d04cc: stur            x1, [fp, #-8]
    // 0x8d04d0: stur            x0, [fp, #-0x18]
    // 0x8d04d4: r0 = InitLateStaticField(0x334) // [dart:core] _BigIntImpl::one
    //     0x8d04d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d04d8: ldr             x0, [x0, #0x668]
    //     0x8d04dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d04e0: cmp             w0, w16
    //     0x8d04e4: b.ne            #0x8d04f4
    //     0x8d04e8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18820] Field <<EMAIL>>: static late final (offset: 0x334)
    //     0x8d04ec: ldr             x2, [x2, #0x820]
    //     0x8d04f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d04f4: ldur            x1, [fp, #-8]
    // 0x8d04f8: mov             x2, x0
    // 0x8d04fc: r0 = &()
    //     0x8d04fc: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8d0500: ldur            x16, [fp, #-0x10]
    // 0x8d0504: stp             x16, x0, [SP]
    // 0x8d0508: r0 = ==()
    //     0x8d0508: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8d050c: tbnz            w0, #4, #0x8d0520
    // 0x8d0510: ldur            x1, [fp, #-0x18]
    // 0x8d0514: add             x2, x1, #1
    // 0x8d0518: mov             x0, x2
    // 0x8d051c: b               #0x8d0528
    // 0x8d0520: ldur            x1, [fp, #-0x18]
    // 0x8d0524: mov             x0, x1
    // 0x8d0528: LeaveFrame
    //     0x8d0528: mov             SP, fp
    //     0x8d052c: ldp             fp, lr, [SP], #0x10
    // 0x8d0530: ret
    //     0x8d0530: ret             
    // 0x8d0534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0534: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0538: b               #0x8d02a4
    // 0x8d053c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d053c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0540: b               #0x8d0304
  }
}

// class id: 593, size: 0x18, field offset: 0x10
class ECCurve extends ECCurveBase {

  _ createPoint(/* No info */) {
    // ** addr: 0x8c98f0, size: 0x7c
    // 0x8c98f0: EnterFrame
    //     0x8c98f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c98f4: mov             fp, SP
    // 0x8c98f8: AllocStack(0x18)
    //     0x8c98f8: sub             SP, SP, #0x18
    // 0x8c98fc: SetupParameters(ECCurve this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x8c98fc: mov             x0, x3
    //     0x8c9900: stur            x3, [fp, #-0x10]
    //     0x8c9904: mov             x3, x1
    //     0x8c9908: stur            x1, [fp, #-8]
    // 0x8c990c: CheckStackOverflow
    //     0x8c990c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9910: cmp             SP, x16
    //     0x8c9914: b.ls            #0x8c9964
    // 0x8c9918: mov             x1, x3
    // 0x8c991c: r0 = fromBigInteger()
    //     0x8c991c: bl              #0x8cf2c0  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::fromBigInteger
    // 0x8c9920: ldur            x1, [fp, #-8]
    // 0x8c9924: ldur            x2, [fp, #-0x10]
    // 0x8c9928: stur            x0, [fp, #-0x10]
    // 0x8c992c: r0 = fromBigInteger()
    //     0x8c992c: bl              #0x8cf2c0  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::fromBigInteger
    // 0x8c9930: stur            x0, [fp, #-0x18]
    // 0x8c9934: r0 = ECPoint()
    //     0x8c9934: bl              #0x8c996c  ; AllocateECPointStub -> ECPoint (size=0x18)
    // 0x8c9938: ldur            x1, [fp, #-8]
    // 0x8c993c: StoreField: r0->field_7 = r1
    //     0x8c993c: stur            w1, [x0, #7]
    // 0x8c9940: ldur            x1, [fp, #-0x10]
    // 0x8c9944: StoreField: r0->field_b = r1
    //     0x8c9944: stur            w1, [x0, #0xb]
    // 0x8c9948: ldur            x1, [fp, #-0x18]
    // 0x8c994c: StoreField: r0->field_f = r1
    //     0x8c994c: stur            w1, [x0, #0xf]
    // 0x8c9950: r1 = false
    //     0x8c9950: add             x1, NULL, #0x30  ; false
    // 0x8c9954: StoreField: r0->field_13 = r1
    //     0x8c9954: stur            w1, [x0, #0x13]
    // 0x8c9958: LeaveFrame
    //     0x8c9958: mov             SP, fp
    //     0x8c995c: ldp             fp, lr, [SP], #0x10
    // 0x8c9960: ret
    //     0x8c9960: ret             
    // 0x8c9964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9964: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9968: b               #0x8c9918
  }
  _ fromBigInteger(/* No info */) {
    // ** addr: 0x8cf2c0, size: 0x5c
    // 0x8cf2c0: EnterFrame
    //     0x8cf2c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf2c4: mov             fp, SP
    // 0x8cf2c8: AllocStack(0x10)
    //     0x8cf2c8: sub             SP, SP, #0x10
    // 0x8cf2cc: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8cf2cc: mov             x3, x2
    //     0x8cf2d0: stur            x2, [fp, #-0x10]
    // 0x8cf2d4: CheckStackOverflow
    //     0x8cf2d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf2d8: cmp             SP, x16
    //     0x8cf2dc: b.ls            #0x8cf314
    // 0x8cf2e0: LoadField: r2 = r1->field_f
    //     0x8cf2e0: ldur            w2, [x1, #0xf]
    // 0x8cf2e4: DecompressPointer r2
    //     0x8cf2e4: add             x2, x2, HEAP, lsl #32
    // 0x8cf2e8: stur            x2, [fp, #-8]
    // 0x8cf2ec: r0 = ECFieldElement()
    //     0x8cf2ec: bl              #0x8ca068  ; AllocateECFieldElementStub -> ECFieldElement (size=0x10)
    // 0x8cf2f0: mov             x1, x0
    // 0x8cf2f4: ldur            x2, [fp, #-8]
    // 0x8cf2f8: ldur            x3, [fp, #-0x10]
    // 0x8cf2fc: stur            x0, [fp, #-8]
    // 0x8cf300: r0 = ECFieldElement()
    //     0x8cf300: bl              #0x8c9f80  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::ECFieldElement
    // 0x8cf304: ldur            x0, [fp, #-8]
    // 0x8cf308: LeaveFrame
    //     0x8cf308: mov             SP, fp
    //     0x8cf30c: ldp             fp, lr, [SP], #0x10
    // 0x8cf310: ret
    //     0x8cf310: ret             
    // 0x8cf314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cf314: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cf318: b               #0x8cf2e0
  }
  _ decompressPoint(/* No info */) {
    // ** addr: 0x8cf31c, size: 0x1f4
    // 0x8cf31c: EnterFrame
    //     0x8cf31c: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf320: mov             fp, SP
    // 0x8cf324: AllocStack(0x28)
    //     0x8cf324: sub             SP, SP, #0x28
    // 0x8cf328: SetupParameters(ECCurve this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2 */)
    //     0x8cf328: mov             x0, x2
    //     0x8cf32c: stur            x2, [fp, #-0x10]
    //     0x8cf330: mov             x2, x3
    //     0x8cf334: mov             x3, x1
    //     0x8cf338: stur            x1, [fp, #-8]
    // 0x8cf33c: CheckStackOverflow
    //     0x8cf33c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf340: cmp             SP, x16
    //     0x8cf344: b.ls            #0x8cf508
    // 0x8cf348: mov             x1, x3
    // 0x8cf34c: r0 = fromBigInteger()
    //     0x8cf34c: bl              #0x8cf2c0  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::fromBigInteger
    // 0x8cf350: mov             x1, x0
    // 0x8cf354: mov             x2, x0
    // 0x8cf358: stur            x0, [fp, #-0x18]
    // 0x8cf35c: r0 = *()
    //     0x8cf35c: bl              #0x8c9d0c  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::*
    // 0x8cf360: mov             x4, x0
    // 0x8cf364: ldur            x3, [fp, #-8]
    // 0x8cf368: stur            x4, [fp, #-0x28]
    // 0x8cf36c: LoadField: r5 = r3->field_7
    //     0x8cf36c: ldur            w5, [x3, #7]
    // 0x8cf370: DecompressPointer r5
    //     0x8cf370: add             x5, x5, HEAP, lsl #32
    // 0x8cf374: mov             x0, x5
    // 0x8cf378: stur            x5, [fp, #-0x20]
    // 0x8cf37c: r2 = Null
    //     0x8cf37c: mov             x2, NULL
    // 0x8cf380: r1 = Null
    //     0x8cf380: mov             x1, NULL
    // 0x8cf384: r4 = LoadClassIdInstr(r0)
    //     0x8cf384: ldur            x4, [x0, #-1]
    //     0x8cf388: ubfx            x4, x4, #0xc, #0x14
    // 0x8cf38c: cmp             x4, #0x255
    // 0x8cf390: b.eq            #0x8cf3a8
    // 0x8cf394: r8 = ECFieldElement
    //     0x8cf394: add             x8, PP, #0x18, lsl #12  ; [pp+0x187e8] Type: ECFieldElement
    //     0x8cf398: ldr             x8, [x8, #0x7e8]
    // 0x8cf39c: r3 = Null
    //     0x8cf39c: add             x3, PP, #0x18, lsl #12  ; [pp+0x187f0] Null
    //     0x8cf3a0: ldr             x3, [x3, #0x7f0]
    // 0x8cf3a4: r0 = DefaultTypeTest()
    //     0x8cf3a4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8cf3a8: ldur            x1, [fp, #-0x28]
    // 0x8cf3ac: ldur            x2, [fp, #-0x20]
    // 0x8cf3b0: r0 = +()
    //     0x8cf3b0: bl              #0x8c9eb8  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::+
    // 0x8cf3b4: ldur            x1, [fp, #-0x18]
    // 0x8cf3b8: mov             x2, x0
    // 0x8cf3bc: r0 = *()
    //     0x8cf3bc: bl              #0x8c9d0c  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::*
    // 0x8cf3c0: mov             x4, x0
    // 0x8cf3c4: ldur            x3, [fp, #-8]
    // 0x8cf3c8: stur            x4, [fp, #-0x28]
    // 0x8cf3cc: LoadField: r5 = r3->field_b
    //     0x8cf3cc: ldur            w5, [x3, #0xb]
    // 0x8cf3d0: DecompressPointer r5
    //     0x8cf3d0: add             x5, x5, HEAP, lsl #32
    // 0x8cf3d4: mov             x0, x5
    // 0x8cf3d8: stur            x5, [fp, #-0x20]
    // 0x8cf3dc: r2 = Null
    //     0x8cf3dc: mov             x2, NULL
    // 0x8cf3e0: r1 = Null
    //     0x8cf3e0: mov             x1, NULL
    // 0x8cf3e4: r4 = LoadClassIdInstr(r0)
    //     0x8cf3e4: ldur            x4, [x0, #-1]
    //     0x8cf3e8: ubfx            x4, x4, #0xc, #0x14
    // 0x8cf3ec: cmp             x4, #0x255
    // 0x8cf3f0: b.eq            #0x8cf408
    // 0x8cf3f4: r8 = ECFieldElement
    //     0x8cf3f4: add             x8, PP, #0x18, lsl #12  ; [pp+0x187e8] Type: ECFieldElement
    //     0x8cf3f8: ldr             x8, [x8, #0x7e8]
    // 0x8cf3fc: r3 = Null
    //     0x8cf3fc: add             x3, PP, #0x18, lsl #12  ; [pp+0x18800] Null
    //     0x8cf400: ldr             x3, [x3, #0x800]
    // 0x8cf404: r0 = DefaultTypeTest()
    //     0x8cf404: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8cf408: ldur            x1, [fp, #-0x28]
    // 0x8cf40c: ldur            x2, [fp, #-0x20]
    // 0x8cf410: r0 = +()
    //     0x8cf410: bl              #0x8c9eb8  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::+
    // 0x8cf414: mov             x1, x0
    // 0x8cf418: r0 = sqrt()
    //     0x8cf418: bl              #0x8cf5b4  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::sqrt
    // 0x8cf41c: stur            x0, [fp, #-0x28]
    // 0x8cf420: cmp             w0, NULL
    // 0x8cf424: b.eq            #0x8cf4e0
    // 0x8cf428: ldur            x3, [fp, #-0x10]
    // 0x8cf42c: LoadField: r4 = r0->field_b
    //     0x8cf42c: ldur            w4, [x0, #0xb]
    // 0x8cf430: DecompressPointer r4
    //     0x8cf430: add             x4, x4, HEAP, lsl #32
    // 0x8cf434: mov             x1, x4
    // 0x8cf438: stur            x4, [fp, #-0x20]
    // 0x8cf43c: r2 = 0
    //     0x8cf43c: movz            x2, #0
    // 0x8cf440: r0 = _testBit()
    //     0x8cf440: bl              #0x8cf510  ; [package:pointycastle/ecc/ecc_fp.dart] ::_testBit
    // 0x8cf444: tst             x0, #0x10
    // 0x8cf448: cset            x2, eq
    // 0x8cf44c: lsl             x2, x2, #1
    // 0x8cf450: ldur            x3, [fp, #-0x10]
    // 0x8cf454: r0 = BoxInt64Instr(r3)
    //     0x8cf454: sbfiz           x0, x3, #1, #0x1f
    //     0x8cf458: cmp             x3, x0, asr #1
    //     0x8cf45c: b.eq            #0x8cf468
    //     0x8cf460: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8cf464: stur            x3, [x0, #7]
    // 0x8cf468: cmp             w2, w0
    // 0x8cf46c: b.eq            #0x8cf498
    // 0x8cf470: ldur            x0, [fp, #-8]
    // 0x8cf474: LoadField: r1 = r0->field_f
    //     0x8cf474: ldur            w1, [x0, #0xf]
    // 0x8cf478: DecompressPointer r1
    //     0x8cf478: add             x1, x1, HEAP, lsl #32
    // 0x8cf47c: ldur            x2, [fp, #-0x20]
    // 0x8cf480: r0 = -()
    //     0x8cf480: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cf484: ldur            x1, [fp, #-8]
    // 0x8cf488: mov             x2, x0
    // 0x8cf48c: r0 = fromBigInteger()
    //     0x8cf48c: bl              #0x8cf2c0  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::fromBigInteger
    // 0x8cf490: mov             x2, x0
    // 0x8cf494: b               #0x8cf49c
    // 0x8cf498: ldur            x2, [fp, #-0x28]
    // 0x8cf49c: ldur            x0, [fp, #-8]
    // 0x8cf4a0: ldur            x1, [fp, #-0x18]
    // 0x8cf4a4: stur            x2, [fp, #-0x20]
    // 0x8cf4a8: r0 = ECPoint()
    //     0x8cf4a8: bl              #0x8c996c  ; AllocateECPointStub -> ECPoint (size=0x18)
    // 0x8cf4ac: mov             x1, x0
    // 0x8cf4b0: ldur            x0, [fp, #-8]
    // 0x8cf4b4: StoreField: r1->field_7 = r0
    //     0x8cf4b4: stur            w0, [x1, #7]
    // 0x8cf4b8: ldur            x0, [fp, #-0x18]
    // 0x8cf4bc: StoreField: r1->field_b = r0
    //     0x8cf4bc: stur            w0, [x1, #0xb]
    // 0x8cf4c0: ldur            x0, [fp, #-0x20]
    // 0x8cf4c4: StoreField: r1->field_f = r0
    //     0x8cf4c4: stur            w0, [x1, #0xf]
    // 0x8cf4c8: r0 = true
    //     0x8cf4c8: add             x0, NULL, #0x20  ; true
    // 0x8cf4cc: StoreField: r1->field_13 = r0
    //     0x8cf4cc: stur            w0, [x1, #0x13]
    // 0x8cf4d0: mov             x0, x1
    // 0x8cf4d4: LeaveFrame
    //     0x8cf4d4: mov             SP, fp
    //     0x8cf4d8: ldp             fp, lr, [SP], #0x10
    // 0x8cf4dc: ret
    //     0x8cf4dc: ret             
    // 0x8cf4e0: r0 = ArgumentError()
    //     0x8cf4e0: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8cf4e4: mov             x1, x0
    // 0x8cf4e8: r0 = "Invalid point compression"
    //     0x8cf4e8: add             x0, PP, #0x18, lsl #12  ; [pp+0x18810] "Invalid point compression"
    //     0x8cf4ec: ldr             x0, [x0, #0x810]
    // 0x8cf4f0: ArrayStore: r1[0] = r0  ; List_4
    //     0x8cf4f0: stur            w0, [x1, #0x17]
    // 0x8cf4f4: r0 = false
    //     0x8cf4f4: add             x0, NULL, #0x30  ; false
    // 0x8cf4f8: StoreField: r1->field_b = r0
    //     0x8cf4f8: stur            w0, [x1, #0xb]
    // 0x8cf4fc: mov             x0, x1
    // 0x8cf500: r0 = Throw()
    //     0x8cf500: bl              #0xec04b8  ; ThrowStub
    // 0x8cf504: brk             #0
    // 0x8cf508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cf508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cf50c: b               #0x8cf348
  }
  get _ fieldSize(/* No info */) {
    // ** addr: 0x8d0990, size: 0x38
    // 0x8d0990: EnterFrame
    //     0x8d0990: stp             fp, lr, [SP, #-0x10]!
    //     0x8d0994: mov             fp, SP
    // 0x8d0998: CheckStackOverflow
    //     0x8d0998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d099c: cmp             SP, x16
    //     0x8d09a0: b.ls            #0x8d09c0
    // 0x8d09a4: LoadField: r0 = r1->field_f
    //     0x8d09a4: ldur            w0, [x1, #0xf]
    // 0x8d09a8: DecompressPointer r0
    //     0x8d09a8: add             x0, x0, HEAP, lsl #32
    // 0x8d09ac: mov             x1, x0
    // 0x8d09b0: r0 = bitLength()
    //     0x8d09b0: bl              #0x8cc650  ; [dart:core] _BigIntImpl::bitLength
    // 0x8d09b4: LeaveFrame
    //     0x8d09b4: mov             SP, fp
    //     0x8d09b8: ldp             fp, lr, [SP], #0x10
    // 0x8d09bc: ret
    //     0x8d09bc: ret             
    // 0x8d09c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d09c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d09c4: b               #0x8d09a4
  }
  _ ECCurve(/* No info */) {
    // ** addr: 0x8d0ccc, size: 0xac
    // 0x8d0ccc: EnterFrame
    //     0x8d0ccc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d0cd0: mov             fp, SP
    // 0x8d0cd4: AllocStack(0x10)
    //     0x8d0cd4: sub             SP, SP, #0x10
    // 0x8d0cd8: SetupParameters(ECCurve this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2 */, dynamic _ /* r5 => r3 */)
    //     0x8d0cd8: mov             x4, x1
    //     0x8d0cdc: mov             x0, x2
    //     0x8d0ce0: mov             x2, x3
    //     0x8d0ce4: mov             x3, x5
    //     0x8d0ce8: stur            x1, [fp, #-8]
    // 0x8d0cec: CheckStackOverflow
    //     0x8d0cec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d0cf0: cmp             SP, x16
    //     0x8d0cf4: b.ls            #0x8d0d70
    // 0x8d0cf8: StoreField: r4->field_f = r0
    //     0x8d0cf8: stur            w0, [x4, #0xf]
    //     0x8d0cfc: ldurb           w16, [x4, #-1]
    //     0x8d0d00: ldurb           w17, [x0, #-1]
    //     0x8d0d04: and             x16, x17, x16, lsr #2
    //     0x8d0d08: tst             x16, HEAP, lsr #32
    //     0x8d0d0c: b.eq            #0x8d0d14
    //     0x8d0d10: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8d0d14: mov             x1, x4
    // 0x8d0d18: r0 = ECCurveBase()
    //     0x8d0d18: bl              #0x8d0d78  ; [package:pointycastle/ecc/ecc_base.dart] ECCurveBase::ECCurveBase
    // 0x8d0d1c: r0 = ECPoint()
    //     0x8d0d1c: bl              #0x8c996c  ; AllocateECPointStub -> ECPoint (size=0x18)
    // 0x8d0d20: mov             x1, x0
    // 0x8d0d24: ldur            x2, [fp, #-8]
    // 0x8d0d28: r3 = Null
    //     0x8d0d28: mov             x3, NULL
    // 0x8d0d2c: r5 = Null
    //     0x8d0d2c: mov             x5, NULL
    // 0x8d0d30: stur            x0, [fp, #-0x10]
    // 0x8d0d34: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x8d0d34: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x8d0d38: r0 = ECPoint()
    //     0x8d0d38: bl              #0x8cf0bc  ; [package:pointycastle/ecc/ecc_fp.dart] ECPoint::ECPoint
    // 0x8d0d3c: ldur            x0, [fp, #-0x10]
    // 0x8d0d40: ldur            x1, [fp, #-8]
    // 0x8d0d44: StoreField: r1->field_13 = r0
    //     0x8d0d44: stur            w0, [x1, #0x13]
    //     0x8d0d48: ldurb           w16, [x1, #-1]
    //     0x8d0d4c: ldurb           w17, [x0, #-1]
    //     0x8d0d50: and             x16, x17, x16, lsr #2
    //     0x8d0d54: tst             x16, HEAP, lsr #32
    //     0x8d0d58: b.eq            #0x8d0d60
    //     0x8d0d5c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d0d60: r0 = Null
    //     0x8d0d60: mov             x0, NULL
    // 0x8d0d64: LeaveFrame
    //     0x8d0d64: mov             SP, fp
    //     0x8d0d68: ldp             fp, lr, [SP], #0x10
    // 0x8d0d6c: ret
    //     0x8d0d6c: ret             
    // 0x8d0d70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0d70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0d74: b               #0x8d0cf8
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf311c, size: 0xf0
    // 0xbf311c: EnterFrame
    //     0xbf311c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3120: mov             fp, SP
    // 0xbf3124: AllocStack(0x18)
    //     0xbf3124: sub             SP, SP, #0x18
    // 0xbf3128: CheckStackOverflow
    //     0xbf3128: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf312c: cmp             SP, x16
    //     0xbf3130: b.ls            #0xbf3204
    // 0xbf3134: ldr             x1, [fp, #0x10]
    // 0xbf3138: LoadField: r0 = r1->field_7
    //     0xbf3138: ldur            w0, [x1, #7]
    // 0xbf313c: DecompressPointer r0
    //     0xbf313c: add             x0, x0, HEAP, lsl #32
    // 0xbf3140: r2 = LoadClassIdInstr(r0)
    //     0xbf3140: ldur            x2, [x0, #-1]
    //     0xbf3144: ubfx            x2, x2, #0xc, #0x14
    // 0xbf3148: str             x0, [SP]
    // 0xbf314c: mov             x0, x2
    // 0xbf3150: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3150: movz            x17, #0x64af
    //     0xbf3154: add             lr, x0, x17
    //     0xbf3158: ldr             lr, [x21, lr, lsl #3]
    //     0xbf315c: blr             lr
    // 0xbf3160: mov             x2, x0
    // 0xbf3164: ldr             x1, [fp, #0x10]
    // 0xbf3168: stur            x2, [fp, #-8]
    // 0xbf316c: LoadField: r0 = r1->field_b
    //     0xbf316c: ldur            w0, [x1, #0xb]
    // 0xbf3170: DecompressPointer r0
    //     0xbf3170: add             x0, x0, HEAP, lsl #32
    // 0xbf3174: r3 = LoadClassIdInstr(r0)
    //     0xbf3174: ldur            x3, [x0, #-1]
    //     0xbf3178: ubfx            x3, x3, #0xc, #0x14
    // 0xbf317c: str             x0, [SP]
    // 0xbf3180: mov             x0, x3
    // 0xbf3184: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3184: movz            x17, #0x64af
    //     0xbf3188: add             lr, x0, x17
    //     0xbf318c: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3190: blr             lr
    // 0xbf3194: mov             x1, x0
    // 0xbf3198: ldur            x0, [fp, #-8]
    // 0xbf319c: r2 = LoadInt32Instr(r0)
    //     0xbf319c: sbfx            x2, x0, #1, #0x1f
    //     0xbf31a0: tbz             w0, #0, #0xbf31a8
    //     0xbf31a4: ldur            x2, [x0, #7]
    // 0xbf31a8: r0 = LoadInt32Instr(r1)
    //     0xbf31a8: sbfx            x0, x1, #1, #0x1f
    //     0xbf31ac: tbz             w1, #0, #0xbf31b4
    //     0xbf31b0: ldur            x0, [x1, #7]
    // 0xbf31b4: eor             x1, x2, x0
    // 0xbf31b8: ldr             x0, [fp, #0x10]
    // 0xbf31bc: stur            x1, [fp, #-0x10]
    // 0xbf31c0: LoadField: r2 = r0->field_f
    //     0xbf31c0: ldur            w2, [x0, #0xf]
    // 0xbf31c4: DecompressPointer r2
    //     0xbf31c4: add             x2, x2, HEAP, lsl #32
    // 0xbf31c8: str             x2, [SP]
    // 0xbf31cc: r0 = hashCode()
    //     0xbf31cc: bl              #0xbdd108  ; [dart:core] _BigIntImpl::hashCode
    // 0xbf31d0: r2 = LoadInt32Instr(r0)
    //     0xbf31d0: sbfx            x2, x0, #1, #0x1f
    //     0xbf31d4: tbz             w0, #0, #0xbf31dc
    //     0xbf31d8: ldur            x2, [x0, #7]
    // 0xbf31dc: ldur            x3, [fp, #-0x10]
    // 0xbf31e0: eor             x4, x3, x2
    // 0xbf31e4: r0 = BoxInt64Instr(r4)
    //     0xbf31e4: sbfiz           x0, x4, #1, #0x1f
    //     0xbf31e8: cmp             x4, x0, asr #1
    //     0xbf31ec: b.eq            #0xbf31f8
    //     0xbf31f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf31f4: stur            x4, [x0, #7]
    // 0xbf31f8: LeaveFrame
    //     0xbf31f8: mov             SP, fp
    //     0xbf31fc: ldp             fp, lr, [SP], #0x10
    // 0xbf3200: ret
    //     0xbf3200: ret             
    // 0xbf3204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3204: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf3208: b               #0xbf3134
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d4f0, size: 0x100
    // 0xd7d4f0: EnterFrame
    //     0xd7d4f0: stp             fp, lr, [SP, #-0x10]!
    //     0xd7d4f4: mov             fp, SP
    // 0xd7d4f8: AllocStack(0x10)
    //     0xd7d4f8: sub             SP, SP, #0x10
    // 0xd7d4fc: CheckStackOverflow
    //     0xd7d4fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7d500: cmp             SP, x16
    //     0xd7d504: b.ls            #0xd7d5e8
    // 0xd7d508: ldr             x0, [fp, #0x10]
    // 0xd7d50c: cmp             w0, NULL
    // 0xd7d510: b.ne            #0xd7d524
    // 0xd7d514: r0 = false
    //     0xd7d514: add             x0, NULL, #0x30  ; false
    // 0xd7d518: LeaveFrame
    //     0xd7d518: mov             SP, fp
    //     0xd7d51c: ldp             fp, lr, [SP], #0x10
    // 0xd7d520: ret
    //     0xd7d520: ret             
    // 0xd7d524: r1 = 60
    //     0xd7d524: movz            x1, #0x3c
    // 0xd7d528: branchIfSmi(r0, 0xd7d534)
    //     0xd7d528: tbz             w0, #0, #0xd7d534
    // 0xd7d52c: r1 = LoadClassIdInstr(r0)
    //     0xd7d52c: ldur            x1, [x0, #-1]
    //     0xd7d530: ubfx            x1, x1, #0xc, #0x14
    // 0xd7d534: cmp             x1, #0x251
    // 0xd7d538: b.ne            #0xd7d5d8
    // 0xd7d53c: ldr             x1, [fp, #0x18]
    // 0xd7d540: LoadField: r2 = r1->field_f
    //     0xd7d540: ldur            w2, [x1, #0xf]
    // 0xd7d544: DecompressPointer r2
    //     0xd7d544: add             x2, x2, HEAP, lsl #32
    // 0xd7d548: LoadField: r3 = r0->field_f
    //     0xd7d548: ldur            w3, [x0, #0xf]
    // 0xd7d54c: DecompressPointer r3
    //     0xd7d54c: add             x3, x3, HEAP, lsl #32
    // 0xd7d550: stp             x3, x2, [SP]
    // 0xd7d554: r0 = ==()
    //     0xd7d554: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0xd7d558: tbnz            w0, #4, #0xd7d5c8
    // 0xd7d55c: ldr             x2, [fp, #0x18]
    // 0xd7d560: ldr             x1, [fp, #0x10]
    // 0xd7d564: LoadField: r0 = r2->field_7
    //     0xd7d564: ldur            w0, [x2, #7]
    // 0xd7d568: DecompressPointer r0
    //     0xd7d568: add             x0, x0, HEAP, lsl #32
    // 0xd7d56c: LoadField: r3 = r1->field_7
    //     0xd7d56c: ldur            w3, [x1, #7]
    // 0xd7d570: DecompressPointer r3
    //     0xd7d570: add             x3, x3, HEAP, lsl #32
    // 0xd7d574: r4 = LoadClassIdInstr(r0)
    //     0xd7d574: ldur            x4, [x0, #-1]
    //     0xd7d578: ubfx            x4, x4, #0xc, #0x14
    // 0xd7d57c: stp             x3, x0, [SP]
    // 0xd7d580: mov             x0, x4
    // 0xd7d584: mov             lr, x0
    // 0xd7d588: ldr             lr, [x21, lr, lsl #3]
    // 0xd7d58c: blr             lr
    // 0xd7d590: tbnz            w0, #4, #0xd7d5c8
    // 0xd7d594: ldr             x1, [fp, #0x18]
    // 0xd7d598: ldr             x0, [fp, #0x10]
    // 0xd7d59c: LoadField: r2 = r1->field_b
    //     0xd7d59c: ldur            w2, [x1, #0xb]
    // 0xd7d5a0: DecompressPointer r2
    //     0xd7d5a0: add             x2, x2, HEAP, lsl #32
    // 0xd7d5a4: LoadField: r1 = r0->field_b
    //     0xd7d5a4: ldur            w1, [x0, #0xb]
    // 0xd7d5a8: DecompressPointer r1
    //     0xd7d5a8: add             x1, x1, HEAP, lsl #32
    // 0xd7d5ac: r0 = LoadClassIdInstr(r2)
    //     0xd7d5ac: ldur            x0, [x2, #-1]
    //     0xd7d5b0: ubfx            x0, x0, #0xc, #0x14
    // 0xd7d5b4: stp             x1, x2, [SP]
    // 0xd7d5b8: mov             lr, x0
    // 0xd7d5bc: ldr             lr, [x21, lr, lsl #3]
    // 0xd7d5c0: blr             lr
    // 0xd7d5c4: b               #0xd7d5cc
    // 0xd7d5c8: r0 = false
    //     0xd7d5c8: add             x0, NULL, #0x30  ; false
    // 0xd7d5cc: LeaveFrame
    //     0xd7d5cc: mov             SP, fp
    //     0xd7d5d0: ldp             fp, lr, [SP], #0x10
    // 0xd7d5d4: ret
    //     0xd7d5d4: ret             
    // 0xd7d5d8: r0 = false
    //     0xd7d5d8: add             x0, NULL, #0x30  ; false
    // 0xd7d5dc: LeaveFrame
    //     0xd7d5dc: mov             SP, fp
    //     0xd7d5e0: ldp             fp, lr, [SP], #0x10
    // 0xd7d5e4: ret
    //     0xd7d5e4: ret             
    // 0xd7d5e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7d5e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7d5ec: b               #0xd7d508
  }
}

// class id: 595, size: 0x18, field offset: 0x18
class ECPoint extends ECPointBase {

  ECPoint? -(ECPoint, ECPoint) {
    // ** addr: 0x8c9990, size: 0x4c
    // 0x8c9990: EnterFrame
    //     0x8c9990: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9994: mov             fp, SP
    // 0x8c9998: CheckStackOverflow
    //     0x8c9998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c999c: cmp             SP, x16
    //     0x8c99a0: b.ls            #0x8c99bc
    // 0x8c99a4: ldr             x1, [fp, #0x18]
    // 0x8c99a8: ldr             x2, [fp, #0x10]
    // 0x8c99ac: r0 = -()
    //     0x8c99ac: bl              #0x8c99c4  ; [package:pointycastle/ecc/ecc_fp.dart] ECPoint::-
    // 0x8c99b0: LeaveFrame
    //     0x8c99b0: mov             SP, fp
    //     0x8c99b4: ldp             fp, lr, [SP], #0x10
    // 0x8c99b8: ret
    //     0x8c99b8: ret             
    // 0x8c99bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c99bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c99c0: b               #0x8c99a4
  }
  ECPoint? -(ECPoint, ECPoint) {
    // ** addr: 0x8c99c4, size: 0xb8
    // 0x8c99c4: EnterFrame
    //     0x8c99c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c99c8: mov             fp, SP
    // 0x8c99cc: AllocStack(0x10)
    //     0x8c99cc: sub             SP, SP, #0x10
    // 0x8c99d0: SetupParameters(ECPoint this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8c99d0: mov             x4, x1
    //     0x8c99d4: mov             x3, x2
    //     0x8c99d8: stur            x1, [fp, #-8]
    //     0x8c99dc: stur            x2, [fp, #-0x10]
    // 0x8c99e0: CheckStackOverflow
    //     0x8c99e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c99e4: cmp             SP, x16
    //     0x8c99e8: b.ls            #0x8c9a74
    // 0x8c99ec: mov             x0, x3
    // 0x8c99f0: r2 = Null
    //     0x8c99f0: mov             x2, NULL
    // 0x8c99f4: r1 = Null
    //     0x8c99f4: mov             x1, NULL
    // 0x8c99f8: r4 = 60
    //     0x8c99f8: movz            x4, #0x3c
    // 0x8c99fc: branchIfSmi(r0, 0x8c9a08)
    //     0x8c99fc: tbz             w0, #0, #0x8c9a08
    // 0x8c9a00: r4 = LoadClassIdInstr(r0)
    //     0x8c9a00: ldur            x4, [x0, #-1]
    //     0x8c9a04: ubfx            x4, x4, #0xc, #0x14
    // 0x8c9a08: cmp             x4, #0x253
    // 0x8c9a0c: b.eq            #0x8c9a24
    // 0x8c9a10: r8 = ECPoint
    //     0x8c9a10: add             x8, PP, #0x31, lsl #12  ; [pp+0x311d0] Type: ECPoint
    //     0x8c9a14: ldr             x8, [x8, #0x1d0]
    // 0x8c9a18: r3 = Null
    //     0x8c9a18: add             x3, PP, #0x31, lsl #12  ; [pp+0x311d8] Null
    //     0x8c9a1c: ldr             x3, [x3, #0x1d8]
    // 0x8c9a20: r0 = DefaultTypeTest()
    //     0x8c9a20: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8c9a24: ldur            x1, [fp, #-0x10]
    // 0x8c9a28: LoadField: r0 = r1->field_b
    //     0x8c9a28: ldur            w0, [x1, #0xb]
    // 0x8c9a2c: DecompressPointer r0
    //     0x8c9a2c: add             x0, x0, HEAP, lsl #32
    // 0x8c9a30: cmp             w0, NULL
    // 0x8c9a34: b.ne            #0x8c9a58
    // 0x8c9a38: LoadField: r0 = r1->field_f
    //     0x8c9a38: ldur            w0, [x1, #0xf]
    // 0x8c9a3c: DecompressPointer r0
    //     0x8c9a3c: add             x0, x0, HEAP, lsl #32
    // 0x8c9a40: cmp             w0, NULL
    // 0x8c9a44: b.ne            #0x8c9a58
    // 0x8c9a48: ldur            x0, [fp, #-8]
    // 0x8c9a4c: LeaveFrame
    //     0x8c9a4c: mov             SP, fp
    //     0x8c9a50: ldp             fp, lr, [SP], #0x10
    // 0x8c9a54: ret
    //     0x8c9a54: ret             
    // 0x8c9a58: r0 = unary-()
    //     0x8c9a58: bl              #0x8cf014  ; [package:pointycastle/ecc/ecc_fp.dart] ECPoint::unary-
    // 0x8c9a5c: ldur            x1, [fp, #-8]
    // 0x8c9a60: mov             x2, x0
    // 0x8c9a64: r0 = +()
    //     0x8c9a64: bl              #0x8c9a7c  ; [package:pointycastle/ecc/ecc_fp.dart] ECPoint::+
    // 0x8c9a68: LeaveFrame
    //     0x8c9a68: mov             SP, fp
    //     0x8c9a6c: ldp             fp, lr, [SP], #0x10
    // 0x8c9a70: ret
    //     0x8c9a70: ret             
    // 0x8c9a74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9a74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9a78: b               #0x8c99ec
  }
  ECPoint? +(ECPoint, ECPoint?) {
    // ** addr: 0x8c9a7c, size: 0x290
    // 0x8c9a7c: EnterFrame
    //     0x8c9a7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9a80: mov             fp, SP
    // 0x8c9a84: AllocStack(0x38)
    //     0x8c9a84: sub             SP, SP, #0x38
    // 0x8c9a88: SetupParameters(ECPoint this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8c9a88: mov             x4, x1
    //     0x8c9a8c: mov             x3, x2
    //     0x8c9a90: stur            x1, [fp, #-8]
    //     0x8c9a94: stur            x2, [fp, #-0x10]
    // 0x8c9a98: CheckStackOverflow
    //     0x8c9a98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9a9c: cmp             SP, x16
    //     0x8c9aa0: b.ls            #0x8c9cf0
    // 0x8c9aa4: mov             x0, x3
    // 0x8c9aa8: r2 = Null
    //     0x8c9aa8: mov             x2, NULL
    // 0x8c9aac: r1 = Null
    //     0x8c9aac: mov             x1, NULL
    // 0x8c9ab0: r4 = 60
    //     0x8c9ab0: movz            x4, #0x3c
    // 0x8c9ab4: branchIfSmi(r0, 0x8c9ac0)
    //     0x8c9ab4: tbz             w0, #0, #0x8c9ac0
    // 0x8c9ab8: r4 = LoadClassIdInstr(r0)
    //     0x8c9ab8: ldur            x4, [x0, #-1]
    //     0x8c9abc: ubfx            x4, x4, #0xc, #0x14
    // 0x8c9ac0: cmp             x4, #0x253
    // 0x8c9ac4: b.eq            #0x8c9adc
    // 0x8c9ac8: r8 = ECPoint?
    //     0x8c9ac8: add             x8, PP, #0x31, lsl #12  ; [pp+0x311e8] Type: ECPoint?
    //     0x8c9acc: ldr             x8, [x8, #0x1e8]
    // 0x8c9ad0: r3 = Null
    //     0x8c9ad0: add             x3, PP, #0x31, lsl #12  ; [pp+0x311f0] Null
    //     0x8c9ad4: ldr             x3, [x3, #0x1f0]
    // 0x8c9ad8: r0 = DefaultNullableTypeTest()
    //     0x8c9ad8: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x8c9adc: ldur            x1, [fp, #-8]
    // 0x8c9ae0: LoadField: r2 = r1->field_b
    //     0x8c9ae0: ldur            w2, [x1, #0xb]
    // 0x8c9ae4: DecompressPointer r2
    //     0x8c9ae4: add             x2, x2, HEAP, lsl #32
    // 0x8c9ae8: stur            x2, [fp, #-0x20]
    // 0x8c9aec: cmp             w2, NULL
    // 0x8c9af0: b.ne            #0x8c9b14
    // 0x8c9af4: LoadField: r0 = r1->field_f
    //     0x8c9af4: ldur            w0, [x1, #0xf]
    // 0x8c9af8: DecompressPointer r0
    //     0x8c9af8: add             x0, x0, HEAP, lsl #32
    // 0x8c9afc: cmp             w0, NULL
    // 0x8c9b00: b.ne            #0x8c9b14
    // 0x8c9b04: ldur            x0, [fp, #-0x10]
    // 0x8c9b08: LeaveFrame
    //     0x8c9b08: mov             SP, fp
    //     0x8c9b0c: ldp             fp, lr, [SP], #0x10
    // 0x8c9b10: ret
    //     0x8c9b10: ret             
    // 0x8c9b14: ldur            x3, [fp, #-0x10]
    // 0x8c9b18: cmp             w3, NULL
    // 0x8c9b1c: b.eq            #0x8c9cf8
    // 0x8c9b20: LoadField: r4 = r3->field_b
    //     0x8c9b20: ldur            w4, [x3, #0xb]
    // 0x8c9b24: DecompressPointer r4
    //     0x8c9b24: add             x4, x4, HEAP, lsl #32
    // 0x8c9b28: stur            x4, [fp, #-0x18]
    // 0x8c9b2c: cmp             w4, NULL
    // 0x8c9b30: b.ne            #0x8c9b54
    // 0x8c9b34: LoadField: r0 = r3->field_f
    //     0x8c9b34: ldur            w0, [x3, #0xf]
    // 0x8c9b38: DecompressPointer r0
    //     0x8c9b38: add             x0, x0, HEAP, lsl #32
    // 0x8c9b3c: cmp             w0, NULL
    // 0x8c9b40: b.ne            #0x8c9b54
    // 0x8c9b44: mov             x0, x1
    // 0x8c9b48: LeaveFrame
    //     0x8c9b48: mov             SP, fp
    //     0x8c9b4c: ldp             fp, lr, [SP], #0x10
    // 0x8c9b50: ret
    //     0x8c9b50: ret             
    // 0x8c9b54: r0 = LoadClassIdInstr(r2)
    //     0x8c9b54: ldur            x0, [x2, #-1]
    //     0x8c9b58: ubfx            x0, x0, #0xc, #0x14
    // 0x8c9b5c: stp             x4, x2, [SP]
    // 0x8c9b60: mov             lr, x0
    // 0x8c9b64: ldr             lr, [x21, lr, lsl #3]
    // 0x8c9b68: blr             lr
    // 0x8c9b6c: tbnz            w0, #4, #0x8c9bdc
    // 0x8c9b70: ldur            x1, [fp, #-8]
    // 0x8c9b74: ldur            x0, [fp, #-0x10]
    // 0x8c9b78: LoadField: r2 = r1->field_f
    //     0x8c9b78: ldur            w2, [x1, #0xf]
    // 0x8c9b7c: DecompressPointer r2
    //     0x8c9b7c: add             x2, x2, HEAP, lsl #32
    // 0x8c9b80: LoadField: r3 = r0->field_f
    //     0x8c9b80: ldur            w3, [x0, #0xf]
    // 0x8c9b84: DecompressPointer r3
    //     0x8c9b84: add             x3, x3, HEAP, lsl #32
    // 0x8c9b88: r0 = LoadClassIdInstr(r2)
    //     0x8c9b88: ldur            x0, [x2, #-1]
    //     0x8c9b8c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c9b90: stp             x3, x2, [SP]
    // 0x8c9b94: mov             lr, x0
    // 0x8c9b98: ldr             lr, [x21, lr, lsl #3]
    // 0x8c9b9c: blr             lr
    // 0x8c9ba0: tbnz            w0, #4, #0x8c9bb8
    // 0x8c9ba4: ldur            x1, [fp, #-8]
    // 0x8c9ba8: r0 = twice()
    //     0x8c9ba8: bl              #0x8cecec  ; [package:pointycastle/ecc/ecc_fp.dart] ECPoint::twice
    // 0x8c9bac: LeaveFrame
    //     0x8c9bac: mov             SP, fp
    //     0x8c9bb0: ldp             fp, lr, [SP], #0x10
    // 0x8c9bb4: ret
    //     0x8c9bb4: ret             
    // 0x8c9bb8: ldur            x3, [fp, #-8]
    // 0x8c9bbc: LoadField: r0 = r3->field_7
    //     0x8c9bbc: ldur            w0, [x3, #7]
    // 0x8c9bc0: DecompressPointer r0
    //     0x8c9bc0: add             x0, x0, HEAP, lsl #32
    // 0x8c9bc4: LoadField: r1 = r0->field_13
    //     0x8c9bc4: ldur            w1, [x0, #0x13]
    // 0x8c9bc8: DecompressPointer r1
    //     0x8c9bc8: add             x1, x1, HEAP, lsl #32
    // 0x8c9bcc: mov             x0, x1
    // 0x8c9bd0: LeaveFrame
    //     0x8c9bd0: mov             SP, fp
    //     0x8c9bd4: ldp             fp, lr, [SP], #0x10
    // 0x8c9bd8: ret
    //     0x8c9bd8: ret             
    // 0x8c9bdc: ldur            x3, [fp, #-8]
    // 0x8c9be0: ldur            x0, [fp, #-0x10]
    // 0x8c9be4: ldur            x5, [fp, #-0x18]
    // 0x8c9be8: ldur            x4, [fp, #-0x20]
    // 0x8c9bec: LoadField: r1 = r0->field_f
    //     0x8c9bec: ldur            w1, [x0, #0xf]
    // 0x8c9bf0: DecompressPointer r1
    //     0x8c9bf0: add             x1, x1, HEAP, lsl #32
    // 0x8c9bf4: cmp             w1, NULL
    // 0x8c9bf8: b.eq            #0x8c9cfc
    // 0x8c9bfc: LoadField: r0 = r3->field_f
    //     0x8c9bfc: ldur            w0, [x3, #0xf]
    // 0x8c9c00: DecompressPointer r0
    //     0x8c9c00: add             x0, x0, HEAP, lsl #32
    // 0x8c9c04: stur            x0, [fp, #-0x10]
    // 0x8c9c08: cmp             w0, NULL
    // 0x8c9c0c: b.eq            #0x8c9d00
    // 0x8c9c10: mov             x2, x0
    // 0x8c9c14: r0 = -()
    //     0x8c9c14: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8c9c18: mov             x3, x0
    // 0x8c9c1c: ldur            x0, [fp, #-0x18]
    // 0x8c9c20: stur            x3, [fp, #-0x28]
    // 0x8c9c24: cmp             w0, NULL
    // 0x8c9c28: b.eq            #0x8c9d04
    // 0x8c9c2c: ldur            x4, [fp, #-0x20]
    // 0x8c9c30: cmp             w4, NULL
    // 0x8c9c34: b.eq            #0x8c9d08
    // 0x8c9c38: mov             x1, x0
    // 0x8c9c3c: mov             x2, x4
    // 0x8c9c40: r0 = -()
    //     0x8c9c40: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8c9c44: ldur            x1, [fp, #-0x28]
    // 0x8c9c48: mov             x2, x0
    // 0x8c9c4c: r0 = /()
    //     0x8c9c4c: bl              #0x8cc894  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::/
    // 0x8c9c50: mov             x1, x0
    // 0x8c9c54: stur            x0, [fp, #-0x28]
    // 0x8c9c58: r0 = square()
    //     0x8c9c58: bl              #0x8ca0f8  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::square
    // 0x8c9c5c: mov             x1, x0
    // 0x8c9c60: ldur            x2, [fp, #-0x20]
    // 0x8c9c64: r0 = -()
    //     0x8c9c64: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8c9c68: mov             x1, x0
    // 0x8c9c6c: ldur            x2, [fp, #-0x18]
    // 0x8c9c70: r0 = -()
    //     0x8c9c70: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8c9c74: ldur            x1, [fp, #-0x20]
    // 0x8c9c78: mov             x2, x0
    // 0x8c9c7c: stur            x0, [fp, #-0x18]
    // 0x8c9c80: r0 = -()
    //     0x8c9c80: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8c9c84: ldur            x1, [fp, #-0x28]
    // 0x8c9c88: mov             x2, x0
    // 0x8c9c8c: r0 = *()
    //     0x8c9c8c: bl              #0x8c9d0c  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::*
    // 0x8c9c90: mov             x1, x0
    // 0x8c9c94: ldur            x2, [fp, #-0x10]
    // 0x8c9c98: r0 = -()
    //     0x8c9c98: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8c9c9c: mov             x1, x0
    // 0x8c9ca0: ldur            x0, [fp, #-8]
    // 0x8c9ca4: stur            x1, [fp, #-0x28]
    // 0x8c9ca8: LoadField: r2 = r0->field_7
    //     0x8c9ca8: ldur            w2, [x0, #7]
    // 0x8c9cac: DecompressPointer r2
    //     0x8c9cac: add             x2, x2, HEAP, lsl #32
    // 0x8c9cb0: stur            x2, [fp, #-0x20]
    // 0x8c9cb4: LoadField: r3 = r0->field_13
    //     0x8c9cb4: ldur            w3, [x0, #0x13]
    // 0x8c9cb8: DecompressPointer r3
    //     0x8c9cb8: add             x3, x3, HEAP, lsl #32
    // 0x8c9cbc: stur            x3, [fp, #-0x10]
    // 0x8c9cc0: r0 = ECPoint()
    //     0x8c9cc0: bl              #0x8c996c  ; AllocateECPointStub -> ECPoint (size=0x18)
    // 0x8c9cc4: ldur            x1, [fp, #-0x20]
    // 0x8c9cc8: StoreField: r0->field_7 = r1
    //     0x8c9cc8: stur            w1, [x0, #7]
    // 0x8c9ccc: ldur            x1, [fp, #-0x18]
    // 0x8c9cd0: StoreField: r0->field_b = r1
    //     0x8c9cd0: stur            w1, [x0, #0xb]
    // 0x8c9cd4: ldur            x1, [fp, #-0x28]
    // 0x8c9cd8: StoreField: r0->field_f = r1
    //     0x8c9cd8: stur            w1, [x0, #0xf]
    // 0x8c9cdc: ldur            x1, [fp, #-0x10]
    // 0x8c9ce0: StoreField: r0->field_13 = r1
    //     0x8c9ce0: stur            w1, [x0, #0x13]
    // 0x8c9ce4: LeaveFrame
    //     0x8c9ce4: mov             SP, fp
    //     0x8c9ce8: ldp             fp, lr, [SP], #0x10
    // 0x8c9cec: ret
    //     0x8c9cec: ret             
    // 0x8c9cf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9cf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9cf4: b               #0x8c9aa4
    // 0x8c9cf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c9cf8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c9cfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c9cfc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c9d00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c9d00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c9d04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c9d04: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c9d08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c9d08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ twice(/* No info */) {
    // ** addr: 0x8cecec, size: 0x228
    // 0x8cecec: EnterFrame
    //     0x8cecec: stp             fp, lr, [SP, #-0x10]!
    //     0x8cecf0: mov             fp, SP
    // 0x8cecf4: AllocStack(0x40)
    //     0x8cecf4: sub             SP, SP, #0x40
    // 0x8cecf8: SetupParameters(ECPoint this /* r1 => r0, fp-0x20 */)
    //     0x8cecf8: mov             x0, x1
    //     0x8cecfc: stur            x1, [fp, #-0x20]
    // 0x8ced00: CheckStackOverflow
    //     0x8ced00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ced04: cmp             SP, x16
    //     0x8ced08: b.ls            #0x8cef00
    // 0x8ced0c: LoadField: r1 = r0->field_b
    //     0x8ced0c: ldur            w1, [x0, #0xb]
    // 0x8ced10: DecompressPointer r1
    //     0x8ced10: add             x1, x1, HEAP, lsl #32
    // 0x8ced14: stur            x1, [fp, #-0x18]
    // 0x8ced18: cmp             w1, NULL
    // 0x8ced1c: b.ne            #0x8ced3c
    // 0x8ced20: LoadField: r2 = r0->field_f
    //     0x8ced20: ldur            w2, [x0, #0xf]
    // 0x8ced24: DecompressPointer r2
    //     0x8ced24: add             x2, x2, HEAP, lsl #32
    // 0x8ced28: cmp             w2, NULL
    // 0x8ced2c: b.ne            #0x8ced3c
    // 0x8ced30: LeaveFrame
    //     0x8ced30: mov             SP, fp
    //     0x8ced34: ldp             fp, lr, [SP], #0x10
    // 0x8ced38: ret
    //     0x8ced38: ret             
    // 0x8ced3c: LoadField: r2 = r0->field_f
    //     0x8ced3c: ldur            w2, [x0, #0xf]
    // 0x8ced40: DecompressPointer r2
    //     0x8ced40: add             x2, x2, HEAP, lsl #32
    // 0x8ced44: stur            x2, [fp, #-0x10]
    // 0x8ced48: cmp             w2, NULL
    // 0x8ced4c: b.eq            #0x8cef08
    // 0x8ced50: LoadField: r3 = r2->field_b
    //     0x8ced50: ldur            w3, [x2, #0xb]
    // 0x8ced54: DecompressPointer r3
    //     0x8ced54: add             x3, x3, HEAP, lsl #32
    // 0x8ced58: stur            x3, [fp, #-8]
    // 0x8ced5c: r0 = InitLateStaticField(0x330) // [dart:core] _BigIntImpl::zero
    //     0x8ced5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ced60: ldr             x0, [x0, #0x660]
    //     0x8ced64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ced68: cmp             w0, w16
    //     0x8ced6c: b.ne            #0x8ced7c
    //     0x8ced70: add             x2, PP, #0x18, lsl #12  ; [pp+0x18818] Field <<EMAIL>>: static late final (offset: 0x330)
    //     0x8ced74: ldr             x2, [x2, #0x818]
    //     0x8ced78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8ced7c: ldur            x16, [fp, #-8]
    // 0x8ced80: stp             x0, x16, [SP]
    // 0x8ced84: r0 = ==()
    //     0x8ced84: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8ced88: tbnz            w0, #4, #0x8cedac
    // 0x8ced8c: ldur            x0, [fp, #-0x20]
    // 0x8ced90: LoadField: r1 = r0->field_7
    //     0x8ced90: ldur            w1, [x0, #7]
    // 0x8ced94: DecompressPointer r1
    //     0x8ced94: add             x1, x1, HEAP, lsl #32
    // 0x8ced98: LoadField: r0 = r1->field_13
    //     0x8ced98: ldur            w0, [x1, #0x13]
    // 0x8ced9c: DecompressPointer r0
    //     0x8ced9c: add             x0, x0, HEAP, lsl #32
    // 0x8ceda0: LeaveFrame
    //     0x8ceda0: mov             SP, fp
    //     0x8ceda4: ldp             fp, lr, [SP], #0x10
    // 0x8ceda8: ret
    //     0x8ceda8: ret             
    // 0x8cedac: ldur            x0, [fp, #-0x20]
    // 0x8cedb0: ldur            x1, [fp, #-0x18]
    // 0x8cedb4: LoadField: r2 = r0->field_7
    //     0x8cedb4: ldur            w2, [x0, #7]
    // 0x8cedb8: DecompressPointer r2
    //     0x8cedb8: add             x2, x2, HEAP, lsl #32
    // 0x8cedbc: stur            x2, [fp, #-8]
    // 0x8cedc0: r0 = InitLateStaticField(0x338) // [dart:core] _BigIntImpl::two
    //     0x8cedc0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8cedc4: ldr             x0, [x0, #0x670]
    //     0x8cedc8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8cedcc: cmp             w0, w16
    //     0x8cedd0: b.ne            #0x8cede0
    //     0x8cedd4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18878] Field <<EMAIL>>: static late final (offset: 0x338)
    //     0x8cedd8: ldr             x2, [x2, #0x878]
    //     0x8ceddc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8cede0: ldur            x1, [fp, #-8]
    // 0x8cede4: mov             x2, x0
    // 0x8cede8: r0 = fromBigInteger()
    //     0x8cede8: bl              #0x8cf2c0  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::fromBigInteger
    // 0x8cedec: r1 = Null
    //     0x8cedec: mov             x1, NULL
    // 0x8cedf0: r2 = 3
    //     0x8cedf0: movz            x2, #0x3
    // 0x8cedf4: stur            x0, [fp, #-0x28]
    // 0x8cedf8: r0 = _BigIntImpl.from()
    //     0x8cedf8: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8cedfc: ldur            x1, [fp, #-8]
    // 0x8cee00: mov             x2, x0
    // 0x8cee04: r0 = fromBigInteger()
    //     0x8cee04: bl              #0x8cf2c0  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::fromBigInteger
    // 0x8cee08: mov             x2, x0
    // 0x8cee0c: ldur            x0, [fp, #-0x18]
    // 0x8cee10: stur            x2, [fp, #-0x30]
    // 0x8cee14: cmp             w0, NULL
    // 0x8cee18: b.eq            #0x8cef0c
    // 0x8cee1c: mov             x1, x0
    // 0x8cee20: r0 = square()
    //     0x8cee20: bl              #0x8ca0f8  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::square
    // 0x8cee24: mov             x1, x0
    // 0x8cee28: ldur            x2, [fp, #-0x30]
    // 0x8cee2c: r0 = *()
    //     0x8cee2c: bl              #0x8c9d0c  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::*
    // 0x8cee30: mov             x1, x0
    // 0x8cee34: ldur            x0, [fp, #-8]
    // 0x8cee38: LoadField: r2 = r0->field_7
    //     0x8cee38: ldur            w2, [x0, #7]
    // 0x8cee3c: DecompressPointer r2
    //     0x8cee3c: add             x2, x2, HEAP, lsl #32
    // 0x8cee40: cmp             w2, NULL
    // 0x8cee44: b.eq            #0x8cef10
    // 0x8cee48: r0 = +()
    //     0x8cee48: bl              #0x8c9eb8  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::+
    // 0x8cee4c: ldur            x1, [fp, #-0x10]
    // 0x8cee50: ldur            x2, [fp, #-0x28]
    // 0x8cee54: stur            x0, [fp, #-0x30]
    // 0x8cee58: r0 = *()
    //     0x8cee58: bl              #0x8c9d0c  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::*
    // 0x8cee5c: ldur            x1, [fp, #-0x30]
    // 0x8cee60: mov             x2, x0
    // 0x8cee64: r0 = /()
    //     0x8cee64: bl              #0x8cc894  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::/
    // 0x8cee68: mov             x1, x0
    // 0x8cee6c: stur            x0, [fp, #-0x30]
    // 0x8cee70: r0 = square()
    //     0x8cee70: bl              #0x8ca0f8  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::square
    // 0x8cee74: ldur            x1, [fp, #-0x18]
    // 0x8cee78: ldur            x2, [fp, #-0x28]
    // 0x8cee7c: stur            x0, [fp, #-0x28]
    // 0x8cee80: r0 = *()
    //     0x8cee80: bl              #0x8c9d0c  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::*
    // 0x8cee84: ldur            x1, [fp, #-0x28]
    // 0x8cee88: mov             x2, x0
    // 0x8cee8c: r0 = -()
    //     0x8cee8c: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8cee90: ldur            x1, [fp, #-0x18]
    // 0x8cee94: mov             x2, x0
    // 0x8cee98: stur            x0, [fp, #-0x18]
    // 0x8cee9c: r0 = -()
    //     0x8cee9c: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8ceea0: ldur            x1, [fp, #-0x30]
    // 0x8ceea4: mov             x2, x0
    // 0x8ceea8: r0 = *()
    //     0x8ceea8: bl              #0x8c9d0c  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::*
    // 0x8ceeac: mov             x1, x0
    // 0x8ceeb0: ldur            x2, [fp, #-0x10]
    // 0x8ceeb4: r0 = -()
    //     0x8ceeb4: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8ceeb8: mov             x1, x0
    // 0x8ceebc: ldur            x0, [fp, #-0x20]
    // 0x8ceec0: stur            x1, [fp, #-0x28]
    // 0x8ceec4: LoadField: r2 = r0->field_13
    //     0x8ceec4: ldur            w2, [x0, #0x13]
    // 0x8ceec8: DecompressPointer r2
    //     0x8ceec8: add             x2, x2, HEAP, lsl #32
    // 0x8ceecc: stur            x2, [fp, #-0x10]
    // 0x8ceed0: r0 = ECPoint()
    //     0x8ceed0: bl              #0x8c996c  ; AllocateECPointStub -> ECPoint (size=0x18)
    // 0x8ceed4: ldur            x1, [fp, #-8]
    // 0x8ceed8: StoreField: r0->field_7 = r1
    //     0x8ceed8: stur            w1, [x0, #7]
    // 0x8ceedc: ldur            x1, [fp, #-0x18]
    // 0x8ceee0: StoreField: r0->field_b = r1
    //     0x8ceee0: stur            w1, [x0, #0xb]
    // 0x8ceee4: ldur            x1, [fp, #-0x28]
    // 0x8ceee8: StoreField: r0->field_f = r1
    //     0x8ceee8: stur            w1, [x0, #0xf]
    // 0x8ceeec: ldur            x1, [fp, #-0x10]
    // 0x8ceef0: StoreField: r0->field_13 = r1
    //     0x8ceef0: stur            w1, [x0, #0x13]
    // 0x8ceef4: LeaveFrame
    //     0x8ceef4: mov             SP, fp
    //     0x8ceef8: ldp             fp, lr, [SP], #0x10
    // 0x8ceefc: ret
    //     0x8ceefc: ret             
    // 0x8cef00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cef00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cef04: b               #0x8ced0c
    // 0x8cef08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8cef08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8cef0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8cef0c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8cef10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8cef10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ unary-(/* No info */) {
    // ** addr: 0x8cf014, size: 0xa8
    // 0x8cf014: EnterFrame
    //     0x8cf014: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf018: mov             fp, SP
    // 0x8cf01c: AllocStack(0x30)
    //     0x8cf01c: sub             SP, SP, #0x30
    // 0x8cf020: SetupParameters(ECPoint this /* r1 => r0, fp-0x18 */)
    //     0x8cf020: mov             x0, x1
    //     0x8cf024: stur            x1, [fp, #-0x18]
    // 0x8cf028: CheckStackOverflow
    //     0x8cf028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf02c: cmp             SP, x16
    //     0x8cf030: b.ls            #0x8cf0b0
    // 0x8cf034: LoadField: r2 = r0->field_7
    //     0x8cf034: ldur            w2, [x0, #7]
    // 0x8cf038: DecompressPointer r2
    //     0x8cf038: add             x2, x2, HEAP, lsl #32
    // 0x8cf03c: stur            x2, [fp, #-0x10]
    // 0x8cf040: LoadField: r3 = r0->field_b
    //     0x8cf040: ldur            w3, [x0, #0xb]
    // 0x8cf044: DecompressPointer r3
    //     0x8cf044: add             x3, x3, HEAP, lsl #32
    // 0x8cf048: stur            x3, [fp, #-8]
    // 0x8cf04c: LoadField: r1 = r0->field_f
    //     0x8cf04c: ldur            w1, [x0, #0xf]
    // 0x8cf050: DecompressPointer r1
    //     0x8cf050: add             x1, x1, HEAP, lsl #32
    // 0x8cf054: cmp             w1, NULL
    // 0x8cf058: b.eq            #0x8cf0b8
    // 0x8cf05c: r0 = unary-()
    //     0x8cf05c: bl              #0x8cf1a0  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::unary-
    // 0x8cf060: mov             x1, x0
    // 0x8cf064: ldur            x0, [fp, #-0x18]
    // 0x8cf068: stur            x1, [fp, #-0x28]
    // 0x8cf06c: LoadField: r2 = r0->field_13
    //     0x8cf06c: ldur            w2, [x0, #0x13]
    // 0x8cf070: DecompressPointer r2
    //     0x8cf070: add             x2, x2, HEAP, lsl #32
    // 0x8cf074: stur            x2, [fp, #-0x20]
    // 0x8cf078: r0 = ECPoint()
    //     0x8cf078: bl              #0x8c996c  ; AllocateECPointStub -> ECPoint (size=0x18)
    // 0x8cf07c: stur            x0, [fp, #-0x18]
    // 0x8cf080: ldur            x16, [fp, #-0x20]
    // 0x8cf084: str             x16, [SP]
    // 0x8cf088: mov             x1, x0
    // 0x8cf08c: ldur            x2, [fp, #-0x10]
    // 0x8cf090: ldur            x3, [fp, #-8]
    // 0x8cf094: ldur            x5, [fp, #-0x28]
    // 0x8cf098: r4 = const [0, 0x5, 0x1, 0x5, null]
    //     0x8cf098: ldr             x4, [PP, #0x718]  ; [pp+0x718] List(5) [0, 0x5, 0x1, 0x5, Null]
    // 0x8cf09c: r0 = ECPoint()
    //     0x8cf09c: bl              #0x8cf0bc  ; [package:pointycastle/ecc/ecc_fp.dart] ECPoint::ECPoint
    // 0x8cf0a0: ldur            x0, [fp, #-0x18]
    // 0x8cf0a4: LeaveFrame
    //     0x8cf0a4: mov             SP, fp
    //     0x8cf0a8: ldp             fp, lr, [SP], #0x10
    // 0x8cf0ac: ret
    //     0x8cf0ac: ret             
    // 0x8cf0b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cf0b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cf0b4: b               #0x8cf034
    // 0x8cf0b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8cf0b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ ECPoint(/* No info */) {
    // ** addr: 0x8cf0bc, size: 0xe4
    // 0x8cf0bc: EnterFrame
    //     0x8cf0bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf0c0: mov             fp, SP
    // 0x8cf0c4: mov             x0, x2
    // 0x8cf0c8: mov             x2, x3
    // 0x8cf0cc: mov             x3, x1
    // 0x8cf0d0: mov             x1, x5
    // 0x8cf0d4: LoadField: r5 = r4->field_13
    //     0x8cf0d4: ldur            w5, [x4, #0x13]
    // 0x8cf0d8: sub             x4, x5, #8
    // 0x8cf0dc: cmp             w4, #2
    // 0x8cf0e0: b.lt            #0x8cf0f4
    // 0x8cf0e4: add             x5, fp, w4, sxtw #2
    // 0x8cf0e8: ldr             x5, [x5, #8]
    // 0x8cf0ec: mov             x4, x5
    // 0x8cf0f0: b               #0x8cf0f8
    // 0x8cf0f4: r4 = false
    //     0x8cf0f4: add             x4, NULL, #0x30  ; false
    // 0x8cf0f8: StoreField: r3->field_7 = r0
    //     0x8cf0f8: stur            w0, [x3, #7]
    //     0x8cf0fc: ldurb           w16, [x3, #-1]
    //     0x8cf100: ldurb           w17, [x0, #-1]
    //     0x8cf104: and             x16, x17, x16, lsr #2
    //     0x8cf108: tst             x16, HEAP, lsr #32
    //     0x8cf10c: b.eq            #0x8cf114
    //     0x8cf110: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8cf114: mov             x0, x2
    // 0x8cf118: StoreField: r3->field_b = r0
    //     0x8cf118: stur            w0, [x3, #0xb]
    //     0x8cf11c: ldurb           w16, [x3, #-1]
    //     0x8cf120: ldurb           w17, [x0, #-1]
    //     0x8cf124: and             x16, x17, x16, lsr #2
    //     0x8cf128: tst             x16, HEAP, lsr #32
    //     0x8cf12c: b.eq            #0x8cf134
    //     0x8cf130: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8cf134: mov             x0, x1
    // 0x8cf138: StoreField: r3->field_f = r0
    //     0x8cf138: stur            w0, [x3, #0xf]
    //     0x8cf13c: ldurb           w16, [x3, #-1]
    //     0x8cf140: ldurb           w17, [x0, #-1]
    //     0x8cf144: and             x16, x17, x16, lsr #2
    //     0x8cf148: tst             x16, HEAP, lsr #32
    //     0x8cf14c: b.eq            #0x8cf154
    //     0x8cf150: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8cf154: StoreField: r3->field_13 = r4
    //     0x8cf154: stur            w4, [x3, #0x13]
    // 0x8cf158: cmp             w2, NULL
    // 0x8cf15c: b.ne            #0x8cf168
    // 0x8cf160: cmp             w1, NULL
    // 0x8cf164: b.ne            #0x8cf178
    // 0x8cf168: r0 = Null
    //     0x8cf168: mov             x0, NULL
    // 0x8cf16c: LeaveFrame
    //     0x8cf16c: mov             SP, fp
    //     0x8cf170: ldp             fp, lr, [SP], #0x10
    // 0x8cf174: ret
    //     0x8cf174: ret             
    // 0x8cf178: r0 = ArgumentError()
    //     0x8cf178: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8cf17c: mov             x1, x0
    // 0x8cf180: r0 = "Exactly one of the field elements is null"
    //     0x8cf180: add             x0, PP, #0x18, lsl #12  ; [pp+0x189a8] "Exactly one of the field elements is null"
    //     0x8cf184: ldr             x0, [x0, #0x9a8]
    // 0x8cf188: ArrayStore: r1[0] = r0  ; List_4
    //     0x8cf188: stur            w0, [x1, #0x17]
    // 0x8cf18c: r0 = false
    //     0x8cf18c: add             x0, NULL, #0x30  ; false
    // 0x8cf190: StoreField: r1->field_b = r0
    //     0x8cf190: stur            w0, [x1, #0xb]
    // 0x8cf194: mov             x0, x1
    // 0x8cf198: r0 = Throw()
    //     0x8cf198: bl              #0xec04b8  ; ThrowStub
    // 0x8cf19c: brk             #0
  }
  ECPoint? +(ECPoint, ECPoint?) {
    // ** addr: 0x8cf22c, size: 0x4c
    // 0x8cf22c: EnterFrame
    //     0x8cf22c: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf230: mov             fp, SP
    // 0x8cf234: CheckStackOverflow
    //     0x8cf234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf238: cmp             SP, x16
    //     0x8cf23c: b.ls            #0x8cf258
    // 0x8cf240: ldr             x1, [fp, #0x18]
    // 0x8cf244: ldr             x2, [fp, #0x10]
    // 0x8cf248: r0 = +()
    //     0x8cf248: bl              #0x8c9a7c  ; [package:pointycastle/ecc/ecc_fp.dart] ECPoint::+
    // 0x8cf24c: LeaveFrame
    //     0x8cf24c: mov             SP, fp
    //     0x8cf250: ldp             fp, lr, [SP], #0x10
    // 0x8cf254: ret
    //     0x8cf254: ret             
    // 0x8cf258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cf258: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cf25c: b               #0x8cf240
  }
}

// class id: 597, size: 0x10, field offset: 0x8
class ECFieldElement extends ECFieldElementBase {

  ECFieldElement *(ECFieldElement, ECFieldElement) {
    // ** addr: 0x8c9d0c, size: 0xc8
    // 0x8c9d0c: EnterFrame
    //     0x8c9d0c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9d10: mov             fp, SP
    // 0x8c9d14: AllocStack(0x18)
    //     0x8c9d14: sub             SP, SP, #0x18
    // 0x8c9d18: SetupParameters(ECFieldElement this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8c9d18: mov             x4, x1
    //     0x8c9d1c: mov             x3, x2
    //     0x8c9d20: stur            x1, [fp, #-8]
    //     0x8c9d24: stur            x2, [fp, #-0x10]
    // 0x8c9d28: CheckStackOverflow
    //     0x8c9d28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9d2c: cmp             SP, x16
    //     0x8c9d30: b.ls            #0x8c9dcc
    // 0x8c9d34: mov             x0, x3
    // 0x8c9d38: r2 = Null
    //     0x8c9d38: mov             x2, NULL
    // 0x8c9d3c: r1 = Null
    //     0x8c9d3c: mov             x1, NULL
    // 0x8c9d40: r4 = 60
    //     0x8c9d40: movz            x4, #0x3c
    // 0x8c9d44: branchIfSmi(r0, 0x8c9d50)
    //     0x8c9d44: tbz             w0, #0, #0x8c9d50
    // 0x8c9d48: r4 = LoadClassIdInstr(r0)
    //     0x8c9d48: ldur            x4, [x0, #-1]
    //     0x8c9d4c: ubfx            x4, x4, #0xc, #0x14
    // 0x8c9d50: cmp             x4, #0x255
    // 0x8c9d54: b.eq            #0x8c9d6c
    // 0x8c9d58: r8 = ECFieldElement
    //     0x8c9d58: add             x8, PP, #0x18, lsl #12  ; [pp+0x187e8] Type: ECFieldElement
    //     0x8c9d5c: ldr             x8, [x8, #0x7e8]
    // 0x8c9d60: r3 = Null
    //     0x8c9d60: add             x3, PP, #0x18, lsl #12  ; [pp+0x18988] Null
    //     0x8c9d64: ldr             x3, [x3, #0x988]
    // 0x8c9d68: r0 = DefaultTypeTest()
    //     0x8c9d68: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8c9d6c: ldur            x0, [fp, #-8]
    // 0x8c9d70: LoadField: r3 = r0->field_7
    //     0x8c9d70: ldur            w3, [x0, #7]
    // 0x8c9d74: DecompressPointer r3
    //     0x8c9d74: add             x3, x3, HEAP, lsl #32
    // 0x8c9d78: stur            x3, [fp, #-0x18]
    // 0x8c9d7c: LoadField: r1 = r0->field_b
    //     0x8c9d7c: ldur            w1, [x0, #0xb]
    // 0x8c9d80: DecompressPointer r1
    //     0x8c9d80: add             x1, x1, HEAP, lsl #32
    // 0x8c9d84: ldur            x0, [fp, #-0x10]
    // 0x8c9d88: LoadField: r2 = r0->field_b
    //     0x8c9d88: ldur            w2, [x0, #0xb]
    // 0x8c9d8c: DecompressPointer r2
    //     0x8c9d8c: add             x2, x2, HEAP, lsl #32
    // 0x8c9d90: r0 = *()
    //     0x8c9d90: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8c9d94: mov             x1, x0
    // 0x8c9d98: ldur            x2, [fp, #-0x18]
    // 0x8c9d9c: r0 = %()
    //     0x8c9d9c: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8c9da0: stur            x0, [fp, #-8]
    // 0x8c9da4: r0 = ECFieldElement()
    //     0x8c9da4: bl              #0x8ca068  ; AllocateECFieldElementStub -> ECFieldElement (size=0x10)
    // 0x8c9da8: mov             x1, x0
    // 0x8c9dac: ldur            x2, [fp, #-0x18]
    // 0x8c9db0: ldur            x3, [fp, #-8]
    // 0x8c9db4: stur            x0, [fp, #-8]
    // 0x8c9db8: r0 = ECFieldElement()
    //     0x8c9db8: bl              #0x8c9f80  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::ECFieldElement
    // 0x8c9dbc: ldur            x0, [fp, #-8]
    // 0x8c9dc0: LeaveFrame
    //     0x8c9dc0: mov             SP, fp
    //     0x8c9dc4: ldp             fp, lr, [SP], #0x10
    // 0x8c9dc8: ret
    //     0x8c9dc8: ret             
    // 0x8c9dcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9dcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9dd0: b               #0x8c9d34
  }
  ECFieldElement -(ECFieldElement, ECFieldElement) {
    // ** addr: 0x8c9dec, size: 0x4c
    // 0x8c9dec: EnterFrame
    //     0x8c9dec: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9df0: mov             fp, SP
    // 0x8c9df4: CheckStackOverflow
    //     0x8c9df4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9df8: cmp             SP, x16
    //     0x8c9dfc: b.ls            #0x8c9e18
    // 0x8c9e00: ldr             x1, [fp, #0x18]
    // 0x8c9e04: ldr             x2, [fp, #0x10]
    // 0x8c9e08: r0 = -()
    //     0x8c9e08: bl              #0x8cec24  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::-
    // 0x8c9e0c: LeaveFrame
    //     0x8c9e0c: mov             SP, fp
    //     0x8c9e10: ldp             fp, lr, [SP], #0x10
    // 0x8c9e14: ret
    //     0x8c9e14: ret             
    // 0x8c9e18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9e18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9e1c: b               #0x8c9e00
  }
  ECFieldElement *(ECFieldElement, ECFieldElement) {
    // ** addr: 0x8c9e38, size: 0x4c
    // 0x8c9e38: EnterFrame
    //     0x8c9e38: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9e3c: mov             fp, SP
    // 0x8c9e40: CheckStackOverflow
    //     0x8c9e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9e44: cmp             SP, x16
    //     0x8c9e48: b.ls            #0x8c9e64
    // 0x8c9e4c: ldr             x1, [fp, #0x18]
    // 0x8c9e50: ldr             x2, [fp, #0x10]
    // 0x8c9e54: r0 = *()
    //     0x8c9e54: bl              #0x8c9d0c  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::*
    // 0x8c9e58: LeaveFrame
    //     0x8c9e58: mov             SP, fp
    //     0x8c9e5c: ldp             fp, lr, [SP], #0x10
    // 0x8c9e60: ret
    //     0x8c9e60: ret             
    // 0x8c9e64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9e64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9e68: b               #0x8c9e4c
  }
  ECFieldElement +(ECFieldElement, ECFieldElement) {
    // ** addr: 0x8c9e84, size: 0x4c
    // 0x8c9e84: EnterFrame
    //     0x8c9e84: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9e88: mov             fp, SP
    // 0x8c9e8c: CheckStackOverflow
    //     0x8c9e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9e90: cmp             SP, x16
    //     0x8c9e94: b.ls            #0x8c9eb0
    // 0x8c9e98: ldr             x1, [fp, #0x18]
    // 0x8c9e9c: ldr             x2, [fp, #0x10]
    // 0x8c9ea0: r0 = +()
    //     0x8c9ea0: bl              #0x8c9eb8  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::+
    // 0x8c9ea4: LeaveFrame
    //     0x8c9ea4: mov             SP, fp
    //     0x8c9ea8: ldp             fp, lr, [SP], #0x10
    // 0x8c9eac: ret
    //     0x8c9eac: ret             
    // 0x8c9eb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9eb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9eb4: b               #0x8c9e98
  }
  ECFieldElement +(ECFieldElement, ECFieldElement) {
    // ** addr: 0x8c9eb8, size: 0xc8
    // 0x8c9eb8: EnterFrame
    //     0x8c9eb8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9ebc: mov             fp, SP
    // 0x8c9ec0: AllocStack(0x18)
    //     0x8c9ec0: sub             SP, SP, #0x18
    // 0x8c9ec4: SetupParameters(ECFieldElement this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8c9ec4: mov             x4, x1
    //     0x8c9ec8: mov             x3, x2
    //     0x8c9ecc: stur            x1, [fp, #-8]
    //     0x8c9ed0: stur            x2, [fp, #-0x10]
    // 0x8c9ed4: CheckStackOverflow
    //     0x8c9ed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9ed8: cmp             SP, x16
    //     0x8c9edc: b.ls            #0x8c9f78
    // 0x8c9ee0: mov             x0, x3
    // 0x8c9ee4: r2 = Null
    //     0x8c9ee4: mov             x2, NULL
    // 0x8c9ee8: r1 = Null
    //     0x8c9ee8: mov             x1, NULL
    // 0x8c9eec: r4 = 60
    //     0x8c9eec: movz            x4, #0x3c
    // 0x8c9ef0: branchIfSmi(r0, 0x8c9efc)
    //     0x8c9ef0: tbz             w0, #0, #0x8c9efc
    // 0x8c9ef4: r4 = LoadClassIdInstr(r0)
    //     0x8c9ef4: ldur            x4, [x0, #-1]
    //     0x8c9ef8: ubfx            x4, x4, #0xc, #0x14
    // 0x8c9efc: cmp             x4, #0x255
    // 0x8c9f00: b.eq            #0x8c9f18
    // 0x8c9f04: r8 = ECFieldElement
    //     0x8c9f04: add             x8, PP, #0x18, lsl #12  ; [pp+0x187e8] Type: ECFieldElement
    //     0x8c9f08: ldr             x8, [x8, #0x7e8]
    // 0x8c9f0c: r3 = Null
    //     0x8c9f0c: add             x3, PP, #0x18, lsl #12  ; [pp+0x18978] Null
    //     0x8c9f10: ldr             x3, [x3, #0x978]
    // 0x8c9f14: r0 = DefaultTypeTest()
    //     0x8c9f14: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8c9f18: ldur            x0, [fp, #-8]
    // 0x8c9f1c: LoadField: r3 = r0->field_7
    //     0x8c9f1c: ldur            w3, [x0, #7]
    // 0x8c9f20: DecompressPointer r3
    //     0x8c9f20: add             x3, x3, HEAP, lsl #32
    // 0x8c9f24: stur            x3, [fp, #-0x18]
    // 0x8c9f28: LoadField: r1 = r0->field_b
    //     0x8c9f28: ldur            w1, [x0, #0xb]
    // 0x8c9f2c: DecompressPointer r1
    //     0x8c9f2c: add             x1, x1, HEAP, lsl #32
    // 0x8c9f30: ldur            x0, [fp, #-0x10]
    // 0x8c9f34: LoadField: r2 = r0->field_b
    //     0x8c9f34: ldur            w2, [x0, #0xb]
    // 0x8c9f38: DecompressPointer r2
    //     0x8c9f38: add             x2, x2, HEAP, lsl #32
    // 0x8c9f3c: r0 = +()
    //     0x8c9f3c: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8c9f40: mov             x1, x0
    // 0x8c9f44: ldur            x2, [fp, #-0x18]
    // 0x8c9f48: r0 = %()
    //     0x8c9f48: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8c9f4c: stur            x0, [fp, #-8]
    // 0x8c9f50: r0 = ECFieldElement()
    //     0x8c9f50: bl              #0x8ca068  ; AllocateECFieldElementStub -> ECFieldElement (size=0x10)
    // 0x8c9f54: mov             x1, x0
    // 0x8c9f58: ldur            x2, [fp, #-0x18]
    // 0x8c9f5c: ldur            x3, [fp, #-8]
    // 0x8c9f60: stur            x0, [fp, #-8]
    // 0x8c9f64: r0 = ECFieldElement()
    //     0x8c9f64: bl              #0x8c9f80  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::ECFieldElement
    // 0x8c9f68: ldur            x0, [fp, #-8]
    // 0x8c9f6c: LeaveFrame
    //     0x8c9f6c: mov             SP, fp
    //     0x8c9f70: ldp             fp, lr, [SP], #0x10
    // 0x8c9f74: ret
    //     0x8c9f74: ret             
    // 0x8c9f78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9f78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9f7c: b               #0x8c9ee0
  }
  _ ECFieldElement(/* No info */) {
    // ** addr: 0x8c9f80, size: 0xa8
    // 0x8c9f80: EnterFrame
    //     0x8c9f80: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9f84: mov             fp, SP
    // 0x8c9f88: mov             x16, x3
    // 0x8c9f8c: mov             x3, x1
    // 0x8c9f90: mov             x1, x16
    // 0x8c9f94: CheckStackOverflow
    //     0x8c9f94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9f98: cmp             SP, x16
    //     0x8c9f9c: b.ls            #0x8ca020
    // 0x8c9fa0: mov             x0, x2
    // 0x8c9fa4: StoreField: r3->field_7 = r0
    //     0x8c9fa4: stur            w0, [x3, #7]
    //     0x8c9fa8: ldurb           w16, [x3, #-1]
    //     0x8c9fac: ldurb           w17, [x0, #-1]
    //     0x8c9fb0: and             x16, x17, x16, lsr #2
    //     0x8c9fb4: tst             x16, HEAP, lsr #32
    //     0x8c9fb8: b.eq            #0x8c9fc0
    //     0x8c9fbc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8c9fc0: mov             x0, x1
    // 0x8c9fc4: StoreField: r3->field_b = r0
    //     0x8c9fc4: stur            w0, [x3, #0xb]
    //     0x8c9fc8: ldurb           w16, [x3, #-1]
    //     0x8c9fcc: ldurb           w17, [x0, #-1]
    //     0x8c9fd0: and             x16, x17, x16, lsr #2
    //     0x8c9fd4: tst             x16, HEAP, lsr #32
    //     0x8c9fd8: b.eq            #0x8c9fe0
    //     0x8c9fdc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8c9fe0: r0 = >=()
    //     0x8c9fe0: bl              #0x8ca028  ; [dart:core] _BigIntImpl::>=
    // 0x8c9fe4: tbz             w0, #4, #0x8c9ff8
    // 0x8c9fe8: r0 = Null
    //     0x8c9fe8: mov             x0, NULL
    // 0x8c9fec: LeaveFrame
    //     0x8c9fec: mov             SP, fp
    //     0x8c9ff0: ldp             fp, lr, [SP], #0x10
    // 0x8c9ff4: ret
    //     0x8c9ff4: ret             
    // 0x8c9ff8: r0 = ArgumentError()
    //     0x8c9ff8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c9ffc: mov             x1, x0
    // 0x8ca000: r0 = "Value x must be smaller than q"
    //     0x8ca000: add             x0, PP, #0x18, lsl #12  ; [pp+0x187c8] "Value x must be smaller than q"
    //     0x8ca004: ldr             x0, [x0, #0x7c8]
    // 0x8ca008: ArrayStore: r1[0] = r0  ; List_4
    //     0x8ca008: stur            w0, [x1, #0x17]
    // 0x8ca00c: r0 = false
    //     0x8ca00c: add             x0, NULL, #0x30  ; false
    // 0x8ca010: StoreField: r1->field_b = r0
    //     0x8ca010: stur            w0, [x1, #0xb]
    // 0x8ca014: mov             x0, x1
    // 0x8ca018: r0 = Throw()
    //     0x8ca018: bl              #0xec04b8  ; ThrowStub
    // 0x8ca01c: brk             #0
    // 0x8ca020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ca020: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ca024: b               #0x8c9fa0
  }
  _ square(/* No info */) {
    // ** addr: 0x8ca0f8, size: 0x94
    // 0x8ca0f8: EnterFrame
    //     0x8ca0f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8ca0fc: mov             fp, SP
    // 0x8ca100: AllocStack(0x10)
    //     0x8ca100: sub             SP, SP, #0x10
    // 0x8ca104: CheckStackOverflow
    //     0x8ca104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ca108: cmp             SP, x16
    //     0x8ca10c: b.ls            #0x8ca184
    // 0x8ca110: LoadField: r3 = r1->field_7
    //     0x8ca110: ldur            w3, [x1, #7]
    // 0x8ca114: DecompressPointer r3
    //     0x8ca114: add             x3, x3, HEAP, lsl #32
    // 0x8ca118: stur            x3, [fp, #-0x10]
    // 0x8ca11c: LoadField: r0 = r1->field_b
    //     0x8ca11c: ldur            w0, [x1, #0xb]
    // 0x8ca120: DecompressPointer r0
    //     0x8ca120: add             x0, x0, HEAP, lsl #32
    // 0x8ca124: stur            x0, [fp, #-8]
    // 0x8ca128: r0 = InitLateStaticField(0x338) // [dart:core] _BigIntImpl::two
    //     0x8ca128: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ca12c: ldr             x0, [x0, #0x670]
    //     0x8ca130: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ca134: cmp             w0, w16
    //     0x8ca138: b.ne            #0x8ca148
    //     0x8ca13c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18878] Field <<EMAIL>>: static late final (offset: 0x338)
    //     0x8ca140: ldr             x2, [x2, #0x878]
    //     0x8ca144: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8ca148: ldur            x1, [fp, #-8]
    // 0x8ca14c: mov             x2, x0
    // 0x8ca150: ldur            x3, [fp, #-0x10]
    // 0x8ca154: r0 = modPow()
    //     0x8ca154: bl              #0x8ca18c  ; [dart:core] _BigIntImpl::modPow
    // 0x8ca158: stur            x0, [fp, #-8]
    // 0x8ca15c: r0 = ECFieldElement()
    //     0x8ca15c: bl              #0x8ca068  ; AllocateECFieldElementStub -> ECFieldElement (size=0x10)
    // 0x8ca160: mov             x1, x0
    // 0x8ca164: ldur            x2, [fp, #-0x10]
    // 0x8ca168: ldur            x3, [fp, #-8]
    // 0x8ca16c: stur            x0, [fp, #-8]
    // 0x8ca170: r0 = ECFieldElement()
    //     0x8ca170: bl              #0x8c9f80  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::ECFieldElement
    // 0x8ca174: ldur            x0, [fp, #-8]
    // 0x8ca178: LeaveFrame
    //     0x8ca178: mov             SP, fp
    //     0x8ca17c: ldp             fp, lr, [SP], #0x10
    // 0x8ca180: ret
    //     0x8ca180: ret             
    // 0x8ca184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ca184: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ca188: b               #0x8ca110
  }
  ECFieldElement /(ECFieldElement, ECFieldElement) {
    // ** addr: 0x8cc894, size: 0x8c
    // 0x8cc894: EnterFrame
    //     0x8cc894: stp             fp, lr, [SP, #-0x10]!
    //     0x8cc898: mov             fp, SP
    // 0x8cc89c: AllocStack(0x10)
    //     0x8cc89c: sub             SP, SP, #0x10
    // 0x8cc8a0: CheckStackOverflow
    //     0x8cc8a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cc8a4: cmp             SP, x16
    //     0x8cc8a8: b.ls            #0x8cc918
    // 0x8cc8ac: LoadField: r0 = r1->field_7
    //     0x8cc8ac: ldur            w0, [x1, #7]
    // 0x8cc8b0: DecompressPointer r0
    //     0x8cc8b0: add             x0, x0, HEAP, lsl #32
    // 0x8cc8b4: stur            x0, [fp, #-0x10]
    // 0x8cc8b8: LoadField: r3 = r1->field_b
    //     0x8cc8b8: ldur            w3, [x1, #0xb]
    // 0x8cc8bc: DecompressPointer r3
    //     0x8cc8bc: add             x3, x3, HEAP, lsl #32
    // 0x8cc8c0: stur            x3, [fp, #-8]
    // 0x8cc8c4: LoadField: r1 = r2->field_b
    //     0x8cc8c4: ldur            w1, [x2, #0xb]
    // 0x8cc8c8: DecompressPointer r1
    //     0x8cc8c8: add             x1, x1, HEAP, lsl #32
    // 0x8cc8cc: mov             x2, x0
    // 0x8cc8d0: r0 = modInverse()
    //     0x8cc8d0: bl              #0x8cc920  ; [dart:core] _BigIntImpl::modInverse
    // 0x8cc8d4: ldur            x1, [fp, #-8]
    // 0x8cc8d8: mov             x2, x0
    // 0x8cc8dc: r0 = *()
    //     0x8cc8dc: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cc8e0: mov             x1, x0
    // 0x8cc8e4: ldur            x2, [fp, #-0x10]
    // 0x8cc8e8: r0 = %()
    //     0x8cc8e8: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8cc8ec: stur            x0, [fp, #-8]
    // 0x8cc8f0: r0 = ECFieldElement()
    //     0x8cc8f0: bl              #0x8ca068  ; AllocateECFieldElementStub -> ECFieldElement (size=0x10)
    // 0x8cc8f4: mov             x1, x0
    // 0x8cc8f8: ldur            x2, [fp, #-0x10]
    // 0x8cc8fc: ldur            x3, [fp, #-8]
    // 0x8cc900: stur            x0, [fp, #-8]
    // 0x8cc904: r0 = ECFieldElement()
    //     0x8cc904: bl              #0x8c9f80  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::ECFieldElement
    // 0x8cc908: ldur            x0, [fp, #-8]
    // 0x8cc90c: LeaveFrame
    //     0x8cc90c: mov             SP, fp
    //     0x8cc910: ldp             fp, lr, [SP], #0x10
    // 0x8cc914: ret
    //     0x8cc914: ret             
    // 0x8cc918: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cc918: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cc91c: b               #0x8cc8ac
  }
  ECFieldElement -(ECFieldElement, ECFieldElement) {
    // ** addr: 0x8cec24, size: 0xc8
    // 0x8cec24: EnterFrame
    //     0x8cec24: stp             fp, lr, [SP, #-0x10]!
    //     0x8cec28: mov             fp, SP
    // 0x8cec2c: AllocStack(0x18)
    //     0x8cec2c: sub             SP, SP, #0x18
    // 0x8cec30: SetupParameters(ECFieldElement this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8cec30: mov             x4, x1
    //     0x8cec34: mov             x3, x2
    //     0x8cec38: stur            x1, [fp, #-8]
    //     0x8cec3c: stur            x2, [fp, #-0x10]
    // 0x8cec40: CheckStackOverflow
    //     0x8cec40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cec44: cmp             SP, x16
    //     0x8cec48: b.ls            #0x8cece4
    // 0x8cec4c: mov             x0, x3
    // 0x8cec50: r2 = Null
    //     0x8cec50: mov             x2, NULL
    // 0x8cec54: r1 = Null
    //     0x8cec54: mov             x1, NULL
    // 0x8cec58: r4 = 60
    //     0x8cec58: movz            x4, #0x3c
    // 0x8cec5c: branchIfSmi(r0, 0x8cec68)
    //     0x8cec5c: tbz             w0, #0, #0x8cec68
    // 0x8cec60: r4 = LoadClassIdInstr(r0)
    //     0x8cec60: ldur            x4, [x0, #-1]
    //     0x8cec64: ubfx            x4, x4, #0xc, #0x14
    // 0x8cec68: cmp             x4, #0x255
    // 0x8cec6c: b.eq            #0x8cec84
    // 0x8cec70: r8 = ECFieldElement
    //     0x8cec70: add             x8, PP, #0x18, lsl #12  ; [pp+0x187e8] Type: ECFieldElement
    //     0x8cec74: ldr             x8, [x8, #0x7e8]
    // 0x8cec78: r3 = Null
    //     0x8cec78: add             x3, PP, #0x31, lsl #12  ; [pp+0x31220] Null
    //     0x8cec7c: ldr             x3, [x3, #0x220]
    // 0x8cec80: r0 = DefaultTypeTest()
    //     0x8cec80: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8cec84: ldur            x0, [fp, #-8]
    // 0x8cec88: LoadField: r3 = r0->field_7
    //     0x8cec88: ldur            w3, [x0, #7]
    // 0x8cec8c: DecompressPointer r3
    //     0x8cec8c: add             x3, x3, HEAP, lsl #32
    // 0x8cec90: stur            x3, [fp, #-0x18]
    // 0x8cec94: LoadField: r1 = r0->field_b
    //     0x8cec94: ldur            w1, [x0, #0xb]
    // 0x8cec98: DecompressPointer r1
    //     0x8cec98: add             x1, x1, HEAP, lsl #32
    // 0x8cec9c: ldur            x0, [fp, #-0x10]
    // 0x8ceca0: LoadField: r2 = r0->field_b
    //     0x8ceca0: ldur            w2, [x0, #0xb]
    // 0x8ceca4: DecompressPointer r2
    //     0x8ceca4: add             x2, x2, HEAP, lsl #32
    // 0x8ceca8: r0 = -()
    //     0x8ceca8: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cecac: mov             x1, x0
    // 0x8cecb0: ldur            x2, [fp, #-0x18]
    // 0x8cecb4: r0 = %()
    //     0x8cecb4: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8cecb8: stur            x0, [fp, #-8]
    // 0x8cecbc: r0 = ECFieldElement()
    //     0x8cecbc: bl              #0x8ca068  ; AllocateECFieldElementStub -> ECFieldElement (size=0x10)
    // 0x8cecc0: mov             x1, x0
    // 0x8cecc4: ldur            x2, [fp, #-0x18]
    // 0x8cecc8: ldur            x3, [fp, #-8]
    // 0x8ceccc: stur            x0, [fp, #-8]
    // 0x8cecd0: r0 = ECFieldElement()
    //     0x8cecd0: bl              #0x8c9f80  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::ECFieldElement
    // 0x8cecd4: ldur            x0, [fp, #-8]
    // 0x8cecd8: LeaveFrame
    //     0x8cecd8: mov             SP, fp
    //     0x8cecdc: ldp             fp, lr, [SP], #0x10
    // 0x8cece0: ret
    //     0x8cece0: ret             
    // 0x8cece4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cece4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cece8: b               #0x8cec4c
  }
  _ unary-(/* No info */) {
    // ** addr: 0x8cf1a0, size: 0x74
    // 0x8cf1a0: EnterFrame
    //     0x8cf1a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf1a4: mov             fp, SP
    // 0x8cf1a8: AllocStack(0x10)
    //     0x8cf1a8: sub             SP, SP, #0x10
    // 0x8cf1ac: CheckStackOverflow
    //     0x8cf1ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf1b0: cmp             SP, x16
    //     0x8cf1b4: b.ls            #0x8cf20c
    // 0x8cf1b8: LoadField: r2 = r1->field_7
    //     0x8cf1b8: ldur            w2, [x1, #7]
    // 0x8cf1bc: DecompressPointer r2
    //     0x8cf1bc: add             x2, x2, HEAP, lsl #32
    // 0x8cf1c0: stur            x2, [fp, #-8]
    // 0x8cf1c4: LoadField: r0 = r1->field_b
    //     0x8cf1c4: ldur            w0, [x1, #0xb]
    // 0x8cf1c8: DecompressPointer r0
    //     0x8cf1c8: add             x0, x0, HEAP, lsl #32
    // 0x8cf1cc: mov             x1, x0
    // 0x8cf1d0: r0 = unary-()
    //     0x8cf1d0: bl              #0x5f6f24  ; [dart:core] _BigIntImpl::unary-
    // 0x8cf1d4: mov             x1, x0
    // 0x8cf1d8: ldur            x2, [fp, #-8]
    // 0x8cf1dc: r0 = %()
    //     0x8cf1dc: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8cf1e0: stur            x0, [fp, #-0x10]
    // 0x8cf1e4: r0 = ECFieldElement()
    //     0x8cf1e4: bl              #0x8ca068  ; AllocateECFieldElementStub -> ECFieldElement (size=0x10)
    // 0x8cf1e8: mov             x1, x0
    // 0x8cf1ec: ldur            x2, [fp, #-8]
    // 0x8cf1f0: ldur            x3, [fp, #-0x10]
    // 0x8cf1f4: stur            x0, [fp, #-8]
    // 0x8cf1f8: r0 = ECFieldElement()
    //     0x8cf1f8: bl              #0x8c9f80  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::ECFieldElement
    // 0x8cf1fc: ldur            x0, [fp, #-8]
    // 0x8cf200: LeaveFrame
    //     0x8cf200: mov             SP, fp
    //     0x8cf204: ldp             fp, lr, [SP], #0x10
    // 0x8cf208: ret
    //     0x8cf208: ret             
    // 0x8cf20c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cf20c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cf210: b               #0x8cf1b8
  }
  _ sqrt(/* No info */) {
    // ** addr: 0x8cf5b4, size: 0x47c
    // 0x8cf5b4: EnterFrame
    //     0x8cf5b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf5b8: mov             fp, SP
    // 0x8cf5bc: AllocStack(0x78)
    //     0x8cf5bc: sub             SP, SP, #0x78
    // 0x8cf5c0: SetupParameters(ECFieldElement this /* r1 => r0, fp-0x10 */)
    //     0x8cf5c0: mov             x0, x1
    //     0x8cf5c4: stur            x1, [fp, #-0x10]
    // 0x8cf5c8: CheckStackOverflow
    //     0x8cf5c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf5cc: cmp             SP, x16
    //     0x8cf5d0: b.ls            #0x8cfa10
    // 0x8cf5d4: LoadField: r3 = r0->field_7
    //     0x8cf5d4: ldur            w3, [x0, #7]
    // 0x8cf5d8: DecompressPointer r3
    //     0x8cf5d8: add             x3, x3, HEAP, lsl #32
    // 0x8cf5dc: mov             x1, x3
    // 0x8cf5e0: stur            x3, [fp, #-8]
    // 0x8cf5e4: r2 = 0
    //     0x8cf5e4: movz            x2, #0
    // 0x8cf5e8: r0 = _testBit()
    //     0x8cf5e8: bl              #0x8cf510  ; [package:pointycastle/ecc/ecc_fp.dart] ::_testBit
    // 0x8cf5ec: tbnz            w0, #4, #0x8cf9e0
    // 0x8cf5f0: ldur            x1, [fp, #-8]
    // 0x8cf5f4: r2 = 1
    //     0x8cf5f4: movz            x2, #0x1
    // 0x8cf5f8: r0 = _testBit()
    //     0x8cf5f8: bl              #0x8cf510  ; [package:pointycastle/ecc/ecc_fp.dart] ::_testBit
    // 0x8cf5fc: tbnz            w0, #4, #0x8cf6a8
    // 0x8cf600: ldur            x0, [fp, #-0x10]
    // 0x8cf604: LoadField: r3 = r0->field_b
    //     0x8cf604: ldur            w3, [x0, #0xb]
    // 0x8cf608: DecompressPointer r3
    //     0x8cf608: add             x3, x3, HEAP, lsl #32
    // 0x8cf60c: ldur            x1, [fp, #-8]
    // 0x8cf610: stur            x3, [fp, #-0x18]
    // 0x8cf614: r2 = 2
    //     0x8cf614: movz            x2, #0x2
    // 0x8cf618: r0 = >>()
    //     0x8cf618: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8cf61c: stur            x0, [fp, #-0x20]
    // 0x8cf620: r0 = InitLateStaticField(0x334) // [dart:core] _BigIntImpl::one
    //     0x8cf620: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8cf624: ldr             x0, [x0, #0x668]
    //     0x8cf628: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8cf62c: cmp             w0, w16
    //     0x8cf630: b.ne            #0x8cf640
    //     0x8cf634: add             x2, PP, #0x18, lsl #12  ; [pp+0x18820] Field <<EMAIL>>: static late final (offset: 0x334)
    //     0x8cf638: ldr             x2, [x2, #0x820]
    //     0x8cf63c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8cf640: ldur            x1, [fp, #-0x20]
    // 0x8cf644: mov             x2, x0
    // 0x8cf648: r0 = +()
    //     0x8cf648: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cf64c: ldur            x1, [fp, #-0x18]
    // 0x8cf650: mov             x2, x0
    // 0x8cf654: ldur            x3, [fp, #-8]
    // 0x8cf658: r0 = modPow()
    //     0x8cf658: bl              #0x8ca18c  ; [dart:core] _BigIntImpl::modPow
    // 0x8cf65c: stur            x0, [fp, #-0x18]
    // 0x8cf660: r0 = ECFieldElement()
    //     0x8cf660: bl              #0x8ca068  ; AllocateECFieldElementStub -> ECFieldElement (size=0x10)
    // 0x8cf664: mov             x1, x0
    // 0x8cf668: ldur            x2, [fp, #-8]
    // 0x8cf66c: ldur            x3, [fp, #-0x18]
    // 0x8cf670: stur            x0, [fp, #-0x18]
    // 0x8cf674: r0 = ECFieldElement()
    //     0x8cf674: bl              #0x8c9f80  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::ECFieldElement
    // 0x8cf678: ldur            x1, [fp, #-0x18]
    // 0x8cf67c: r0 = square()
    //     0x8cf67c: bl              #0x8ca0f8  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::square
    // 0x8cf680: ldur            x16, [fp, #-0x10]
    // 0x8cf684: stp             x16, x0, [SP]
    // 0x8cf688: r0 = ==()
    //     0x8cf688: bl              #0xd7d2b4  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::==
    // 0x8cf68c: tbnz            w0, #4, #0x8cf698
    // 0x8cf690: ldur            x0, [fp, #-0x18]
    // 0x8cf694: b               #0x8cf69c
    // 0x8cf698: r0 = Null
    //     0x8cf698: mov             x0, NULL
    // 0x8cf69c: LeaveFrame
    //     0x8cf69c: mov             SP, fp
    //     0x8cf6a0: ldp             fp, lr, [SP], #0x10
    // 0x8cf6a4: ret
    //     0x8cf6a4: ret             
    // 0x8cf6a8: ldur            x1, [fp, #-0x10]
    // 0x8cf6ac: r0 = InitLateStaticField(0x334) // [dart:core] _BigIntImpl::one
    //     0x8cf6ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8cf6b0: ldr             x0, [x0, #0x668]
    //     0x8cf6b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8cf6b8: cmp             w0, w16
    //     0x8cf6bc: b.ne            #0x8cf6cc
    //     0x8cf6c0: add             x2, PP, #0x18, lsl #12  ; [pp+0x18820] Field <<EMAIL>>: static late final (offset: 0x334)
    //     0x8cf6c4: ldr             x2, [x2, #0x820]
    //     0x8cf6c8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8cf6cc: ldur            x1, [fp, #-8]
    // 0x8cf6d0: mov             x2, x0
    // 0x8cf6d4: stur            x0, [fp, #-0x18]
    // 0x8cf6d8: r0 = -()
    //     0x8cf6d8: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cf6dc: mov             x1, x0
    // 0x8cf6e0: r2 = 1
    //     0x8cf6e0: movz            x2, #0x1
    // 0x8cf6e4: stur            x0, [fp, #-0x20]
    // 0x8cf6e8: r0 = >>()
    //     0x8cf6e8: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8cf6ec: mov             x4, x0
    // 0x8cf6f0: ldur            x0, [fp, #-0x10]
    // 0x8cf6f4: stur            x4, [fp, #-0x30]
    // 0x8cf6f8: LoadField: r5 = r0->field_b
    //     0x8cf6f8: ldur            w5, [x0, #0xb]
    // 0x8cf6fc: DecompressPointer r5
    //     0x8cf6fc: add             x5, x5, HEAP, lsl #32
    // 0x8cf700: mov             x1, x5
    // 0x8cf704: mov             x2, x4
    // 0x8cf708: ldur            x3, [fp, #-8]
    // 0x8cf70c: stur            x5, [fp, #-0x28]
    // 0x8cf710: r0 = modPow()
    //     0x8cf710: bl              #0x8ca18c  ; [dart:core] _BigIntImpl::modPow
    // 0x8cf714: ldur            x16, [fp, #-0x18]
    // 0x8cf718: stp             x16, x0, [SP]
    // 0x8cf71c: r0 = ==()
    //     0x8cf71c: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8cf720: tbz             w0, #4, #0x8cf734
    // 0x8cf724: r0 = Null
    //     0x8cf724: mov             x0, NULL
    // 0x8cf728: LeaveFrame
    //     0x8cf728: mov             SP, fp
    //     0x8cf72c: ldp             fp, lr, [SP], #0x10
    // 0x8cf730: ret
    //     0x8cf730: ret             
    // 0x8cf734: ldur            x0, [fp, #-8]
    // 0x8cf738: ldur            x1, [fp, #-0x20]
    // 0x8cf73c: r2 = 2
    //     0x8cf73c: movz            x2, #0x2
    // 0x8cf740: r0 = >>()
    //     0x8cf740: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8cf744: mov             x1, x0
    // 0x8cf748: r2 = 1
    //     0x8cf748: movz            x2, #0x1
    // 0x8cf74c: r0 = <<()
    //     0x8cf74c: bl              #0x8cc42c  ; [dart:core] _BigIntImpl::<<
    // 0x8cf750: mov             x1, x0
    // 0x8cf754: ldur            x2, [fp, #-0x18]
    // 0x8cf758: r0 = +()
    //     0x8cf758: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cf75c: ldur            x1, [fp, #-0x28]
    // 0x8cf760: r2 = 2
    //     0x8cf760: movz            x2, #0x2
    // 0x8cf764: stur            x0, [fp, #-0x38]
    // 0x8cf768: r0 = >>()
    //     0x8cf768: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8cf76c: mov             x1, x0
    // 0x8cf770: ldur            x2, [fp, #-8]
    // 0x8cf774: r0 = %()
    //     0x8cf774: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8cf778: r1 = Null
    //     0x8cf778: mov             x1, NULL
    // 0x8cf77c: stur            x0, [fp, #-0x40]
    // 0x8cf780: r0 = SecureRandom()
    //     0x8cf780: bl              #0x8d0544  ; [package:pointycastle/api.dart] SecureRandom::SecureRandom
    // 0x8cf784: mov             x2, x0
    // 0x8cf788: ldur            x0, [fp, #-8]
    // 0x8cf78c: stur            x2, [fp, #-0x58]
    // 0x8cf790: LoadField: r3 = r0->field_f
    //     0x8cf790: ldur            x3, [x0, #0xf]
    // 0x8cf794: stur            x3, [fp, #-0x50]
    // 0x8cf798: LoadField: r4 = r0->field_7
    //     0x8cf798: ldur            w4, [x0, #7]
    // 0x8cf79c: DecompressPointer r4
    //     0x8cf79c: add             x4, x4, HEAP, lsl #32
    // 0x8cf7a0: stur            x4, [fp, #-0x48]
    // 0x8cf7a4: CheckStackOverflow
    //     0x8cf7a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf7a8: cmp             SP, x16
    //     0x8cf7ac: b.ls            #0x8cfa18
    // 0x8cf7b0: CheckStackOverflow
    //     0x8cf7b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf7b4: cmp             SP, x16
    //     0x8cf7b8: b.ls            #0x8cfa20
    // 0x8cf7bc: mov             x1, x0
    // 0x8cf7c0: r0 = bitLength()
    //     0x8cf7c0: bl              #0x8cc650  ; [dart:core] _BigIntImpl::bitLength
    // 0x8cf7c4: ldur            x3, [fp, #-0x58]
    // 0x8cf7c8: r1 = LoadClassIdInstr(r3)
    //     0x8cf7c8: ldur            x1, [x3, #-1]
    //     0x8cf7cc: ubfx            x1, x1, #0xc, #0x14
    // 0x8cf7d0: mov             x2, x0
    // 0x8cf7d4: mov             x0, x1
    // 0x8cf7d8: mov             x1, x3
    // 0x8cf7dc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8cf7dc: sub             lr, x0, #1, lsl #12
    //     0x8cf7e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8cf7e4: blr             lr
    // 0x8cf7e8: mov             x1, x0
    // 0x8cf7ec: ldur            x2, [fp, #-8]
    // 0x8cf7f0: stur            x0, [fp, #-0x60]
    // 0x8cf7f4: r0 = compareTo()
    //     0x8cf7f4: bl              #0x5f6dc8  ; [dart:core] _BigIntImpl::compareTo
    // 0x8cf7f8: tbnz            x0, #0x3f, #0x8cf810
    // 0x8cf7fc: ldur            x0, [fp, #-8]
    // 0x8cf800: ldur            x2, [fp, #-0x58]
    // 0x8cf804: ldur            x3, [fp, #-0x50]
    // 0x8cf808: ldur            x4, [fp, #-0x48]
    // 0x8cf80c: b               #0x8cf7b0
    // 0x8cf810: ldur            x1, [fp, #-0x60]
    // 0x8cf814: ldur            x2, [fp, #-0x60]
    // 0x8cf818: r0 = *()
    //     0x8cf818: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cf81c: mov             x1, x0
    // 0x8cf820: ldur            x2, [fp, #-0x40]
    // 0x8cf824: r0 = -()
    //     0x8cf824: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cf828: mov             x1, x0
    // 0x8cf82c: ldur            x2, [fp, #-0x30]
    // 0x8cf830: ldur            x3, [fp, #-8]
    // 0x8cf834: r0 = modPow()
    //     0x8cf834: bl              #0x8ca18c  ; [dart:core] _BigIntImpl::modPow
    // 0x8cf838: mov             x1, x0
    // 0x8cf83c: ldur            x2, [fp, #-0x20]
    // 0x8cf840: r0 = compareTo()
    //     0x8cf840: bl              #0x5f6dc8  ; [dart:core] _BigIntImpl::compareTo
    // 0x8cf844: cbz             x0, #0x8cf85c
    // 0x8cf848: ldur            x0, [fp, #-8]
    // 0x8cf84c: ldur            x2, [fp, #-0x58]
    // 0x8cf850: ldur            x3, [fp, #-0x50]
    // 0x8cf854: ldur            x4, [fp, #-0x48]
    // 0x8cf858: b               #0x8cf7b0
    // 0x8cf85c: ldur            x0, [fp, #-0x50]
    // 0x8cf860: ldur            x1, [fp, #-0x10]
    // 0x8cf864: ldur            x2, [fp, #-8]
    // 0x8cf868: ldur            x3, [fp, #-0x60]
    // 0x8cf86c: ldur            x5, [fp, #-0x28]
    // 0x8cf870: ldur            x6, [fp, #-0x38]
    // 0x8cf874: r0 = _lucasSequence()
    //     0x8cf874: bl              #0x8cfa30  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::_lucasSequence
    // 0x8cf878: mov             x2, x0
    // 0x8cf87c: LoadField: r0 = r2->field_b
    //     0x8cf87c: ldur            w0, [x2, #0xb]
    // 0x8cf880: r3 = LoadInt32Instr(r0)
    //     0x8cf880: sbfx            x3, x0, #1, #0x1f
    // 0x8cf884: mov             x0, x3
    // 0x8cf888: r1 = 0
    //     0x8cf888: movz            x1, #0
    // 0x8cf88c: cmp             x1, x0
    // 0x8cf890: b.hs            #0x8cfa28
    // 0x8cf894: LoadField: r4 = r2->field_f
    //     0x8cf894: ldur            w4, [x2, #0xf]
    // 0x8cf898: DecompressPointer r4
    //     0x8cf898: add             x4, x4, HEAP, lsl #32
    // 0x8cf89c: LoadField: r5 = r4->field_f
    //     0x8cf89c: ldur            w5, [x4, #0xf]
    // 0x8cf8a0: DecompressPointer r5
    //     0x8cf8a0: add             x5, x5, HEAP, lsl #32
    // 0x8cf8a4: mov             x0, x3
    // 0x8cf8a8: stur            x5, [fp, #-0x68]
    // 0x8cf8ac: r1 = 1
    //     0x8cf8ac: movz            x1, #0x1
    // 0x8cf8b0: cmp             x1, x0
    // 0x8cf8b4: b.hs            #0x8cfa2c
    // 0x8cf8b8: LoadField: r0 = r4->field_13
    //     0x8cf8b8: ldur            w0, [x4, #0x13]
    // 0x8cf8bc: DecompressPointer r0
    //     0x8cf8bc: add             x0, x0, HEAP, lsl #32
    // 0x8cf8c0: mov             x1, x0
    // 0x8cf8c4: mov             x2, x0
    // 0x8cf8c8: stur            x0, [fp, #-0x60]
    // 0x8cf8cc: r0 = *()
    //     0x8cf8cc: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cf8d0: mov             x1, x0
    // 0x8cf8d4: ldur            x0, [fp, #-0x50]
    // 0x8cf8d8: cbz             x0, #0x8cfa00
    // 0x8cf8dc: ldur            x2, [fp, #-8]
    // 0x8cf8e0: r0 = _rem()
    //     0x8cf8e0: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8cf8e4: LoadField: r1 = r0->field_7
    //     0x8cf8e4: ldur            w1, [x0, #7]
    // 0x8cf8e8: DecompressPointer r1
    //     0x8cf8e8: add             x1, x1, HEAP, lsl #32
    // 0x8cf8ec: tbnz            w1, #4, #0x8cf91c
    // 0x8cf8f0: ldur            x3, [fp, #-0x48]
    // 0x8cf8f4: tbnz            w3, #4, #0x8cf908
    // 0x8cf8f8: mov             x1, x0
    // 0x8cf8fc: ldur            x2, [fp, #-8]
    // 0x8cf900: r0 = -()
    //     0x8cf900: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cf904: b               #0x8cf914
    // 0x8cf908: mov             x1, x0
    // 0x8cf90c: ldur            x2, [fp, #-8]
    // 0x8cf910: r0 = +()
    //     0x8cf910: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cf914: mov             x1, x0
    // 0x8cf918: b               #0x8cf920
    // 0x8cf91c: mov             x1, x0
    // 0x8cf920: ldur            x2, [fp, #-0x40]
    // 0x8cf924: r0 = compareTo()
    //     0x8cf924: bl              #0x5f6dc8  ; [dart:core] _BigIntImpl::compareTo
    // 0x8cf928: cbz             x0, #0x8cf984
    // 0x8cf92c: ldur            x1, [fp, #-0x68]
    // 0x8cf930: ldur            x2, [fp, #-0x18]
    // 0x8cf934: r0 = compareTo()
    //     0x8cf934: bl              #0x5f6dc8  ; [dart:core] _BigIntImpl::compareTo
    // 0x8cf938: cbnz            x0, #0x8cf950
    // 0x8cf93c: ldur            x0, [fp, #-8]
    // 0x8cf940: ldur            x2, [fp, #-0x58]
    // 0x8cf944: ldur            x3, [fp, #-0x50]
    // 0x8cf948: ldur            x4, [fp, #-0x48]
    // 0x8cf94c: b               #0x8cf7a4
    // 0x8cf950: ldur            x1, [fp, #-0x68]
    // 0x8cf954: ldur            x2, [fp, #-0x20]
    // 0x8cf958: r0 = compareTo()
    //     0x8cf958: bl              #0x5f6dc8  ; [dart:core] _BigIntImpl::compareTo
    // 0x8cf95c: cbnz            x0, #0x8cf974
    // 0x8cf960: ldur            x0, [fp, #-8]
    // 0x8cf964: ldur            x2, [fp, #-0x58]
    // 0x8cf968: ldur            x3, [fp, #-0x50]
    // 0x8cf96c: ldur            x4, [fp, #-0x48]
    // 0x8cf970: b               #0x8cf7a4
    // 0x8cf974: r0 = Null
    //     0x8cf974: mov             x0, NULL
    // 0x8cf978: LeaveFrame
    //     0x8cf978: mov             SP, fp
    //     0x8cf97c: ldp             fp, lr, [SP], #0x10
    // 0x8cf980: ret
    //     0x8cf980: ret             
    // 0x8cf984: ldur            x1, [fp, #-0x60]
    // 0x8cf988: r2 = 0
    //     0x8cf988: movz            x2, #0
    // 0x8cf98c: r0 = _testBit()
    //     0x8cf98c: bl              #0x8cf510  ; [package:pointycastle/ecc/ecc_fp.dart] ::_testBit
    // 0x8cf990: tbnz            w0, #4, #0x8cf9a8
    // 0x8cf994: ldur            x1, [fp, #-0x60]
    // 0x8cf998: ldur            x2, [fp, #-8]
    // 0x8cf99c: r0 = +()
    //     0x8cf99c: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cf9a0: mov             x1, x0
    // 0x8cf9a4: b               #0x8cf9ac
    // 0x8cf9a8: ldur            x1, [fp, #-0x60]
    // 0x8cf9ac: r2 = 1
    //     0x8cf9ac: movz            x2, #0x1
    // 0x8cf9b0: r0 = >>()
    //     0x8cf9b0: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8cf9b4: stur            x0, [fp, #-0x10]
    // 0x8cf9b8: r0 = ECFieldElement()
    //     0x8cf9b8: bl              #0x8ca068  ; AllocateECFieldElementStub -> ECFieldElement (size=0x10)
    // 0x8cf9bc: mov             x1, x0
    // 0x8cf9c0: ldur            x2, [fp, #-8]
    // 0x8cf9c4: ldur            x3, [fp, #-0x10]
    // 0x8cf9c8: stur            x0, [fp, #-8]
    // 0x8cf9cc: r0 = ECFieldElement()
    //     0x8cf9cc: bl              #0x8c9f80  ; [package:pointycastle/ecc/ecc_fp.dart] ECFieldElement::ECFieldElement
    // 0x8cf9d0: ldur            x0, [fp, #-8]
    // 0x8cf9d4: LeaveFrame
    //     0x8cf9d4: mov             SP, fp
    //     0x8cf9d8: ldp             fp, lr, [SP], #0x10
    // 0x8cf9dc: ret
    //     0x8cf9dc: ret             
    // 0x8cf9e0: r0 = UnimplementedError()
    //     0x8cf9e0: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0x8cf9e4: mov             x1, x0
    // 0x8cf9e8: r0 = "Not implemented yet"
    //     0x8cf9e8: add             x0, PP, #0x18, lsl #12  ; [pp+0x18850] "Not implemented yet"
    //     0x8cf9ec: ldr             x0, [x0, #0x850]
    // 0x8cf9f0: StoreField: r1->field_b = r0
    //     0x8cf9f0: stur            w0, [x1, #0xb]
    // 0x8cf9f4: mov             x0, x1
    // 0x8cf9f8: r0 = Throw()
    //     0x8cf9f8: bl              #0xec04b8  ; ThrowStub
    // 0x8cf9fc: brk             #0
    // 0x8cfa00: r0 = Instance_IntegerDivisionByZeroException
    //     0x8cfa00: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8cfa04: ldr             x0, [x0, #0x858]
    // 0x8cfa08: r0 = Throw()
    //     0x8cfa08: bl              #0xec04b8  ; ThrowStub
    // 0x8cfa0c: brk             #0
    // 0x8cfa10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cfa10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cfa14: b               #0x8cf5d4
    // 0x8cfa18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cfa18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cfa1c: b               #0x8cf7b0
    // 0x8cfa20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cfa20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cfa24: b               #0x8cf7bc
    // 0x8cfa28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8cfa28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8cfa2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8cfa2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _lucasSequence(/* No info */) {
    // ** addr: 0x8cfa30, size: 0x858
    // 0x8cfa30: EnterFrame
    //     0x8cfa30: stp             fp, lr, [SP, #-0x10]!
    //     0x8cfa34: mov             fp, SP
    // 0x8cfa38: AllocStack(0xa0)
    //     0x8cfa38: sub             SP, SP, #0xa0
    // 0x8cfa3c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r2, fp-0x18 */, dynamic _ /* r6 => r0, fp-0x20 */)
    //     0x8cfa3c: mov             x4, x2
    //     0x8cfa40: stur            x2, [fp, #-8]
    //     0x8cfa44: mov             x2, x5
    //     0x8cfa48: mov             x0, x6
    //     0x8cfa4c: stur            x3, [fp, #-0x10]
    //     0x8cfa50: stur            x5, [fp, #-0x18]
    //     0x8cfa54: stur            x6, [fp, #-0x20]
    // 0x8cfa58: CheckStackOverflow
    //     0x8cfa58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cfa5c: cmp             SP, x16
    //     0x8cfa60: b.ls            #0x8d0270
    // 0x8cfa64: mov             x1, x0
    // 0x8cfa68: r0 = bitLength()
    //     0x8cfa68: bl              #0x8cc650  ; [dart:core] _BigIntImpl::bitLength
    // 0x8cfa6c: ldur            x1, [fp, #-0x20]
    // 0x8cfa70: stur            x0, [fp, #-0x28]
    // 0x8cfa74: r0 = _lbit()
    //     0x8cfa74: bl              #0x8d0288  ; [package:pointycastle/ecc/ecc_fp.dart] ::_lbit
    // 0x8cfa78: stur            x0, [fp, #-0x30]
    // 0x8cfa7c: r0 = InitLateStaticField(0x334) // [dart:core] _BigIntImpl::one
    //     0x8cfa7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8cfa80: ldr             x0, [x0, #0x668]
    //     0x8cfa84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8cfa88: cmp             w0, w16
    //     0x8cfa8c: b.ne            #0x8cfa9c
    //     0x8cfa90: add             x2, PP, #0x18, lsl #12  ; [pp+0x18820] Field <<EMAIL>>: static late final (offset: 0x334)
    //     0x8cfa94: ldr             x2, [x2, #0x820]
    //     0x8cfa98: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8cfa9c: stur            x0, [fp, #-0x38]
    // 0x8cfaa0: r0 = InitLateStaticField(0x338) // [dart:core] _BigIntImpl::two
    //     0x8cfaa0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8cfaa4: ldr             x0, [x0, #0x670]
    //     0x8cfaa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8cfaac: cmp             w0, w16
    //     0x8cfab0: b.ne            #0x8cfac0
    //     0x8cfab4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18878] Field <<EMAIL>>: static late final (offset: 0x338)
    //     0x8cfab8: ldr             x2, [x2, #0x878]
    //     0x8cfabc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8cfac0: mov             x1, x0
    // 0x8cfac4: ldur            x0, [fp, #-0x28]
    // 0x8cfac8: sub             x2, x0, #1
    // 0x8cfacc: ldur            x0, [fp, #-0x30]
    // 0x8cfad0: add             x3, x0, #1
    // 0x8cfad4: ldur            x4, [fp, #-8]
    // 0x8cfad8: stur            x3, [fp, #-0x68]
    // 0x8cfadc: LoadField: r5 = r4->field_f
    //     0x8cfadc: ldur            x5, [x4, #0xf]
    // 0x8cfae0: stur            x5, [fp, #-0x60]
    // 0x8cfae4: LoadField: r6 = r4->field_7
    //     0x8cfae4: ldur            w6, [x4, #7]
    // 0x8cfae8: DecompressPointer r6
    //     0x8cfae8: add             x6, x6, HEAP, lsl #32
    // 0x8cfaec: stur            x6, [fp, #-0x58]
    // 0x8cfaf0: ldur            x10, [fp, #-0x38]
    // 0x8cfaf4: mov             x9, x1
    // 0x8cfaf8: ldur            x8, [fp, #-0x10]
    // 0x8cfafc: ldur            x1, [fp, #-0x38]
    // 0x8cfb00: mov             x7, x2
    // 0x8cfb04: ldur            x2, [fp, #-0x38]
    // 0x8cfb08: stur            x10, [fp, #-0x40]
    // 0x8cfb0c: stur            x9, [fp, #-0x48]
    // 0x8cfb10: stur            x8, [fp, #-0x50]
    // 0x8cfb14: stur            x7, [fp, #-0x28]
    // 0x8cfb18: CheckStackOverflow
    //     0x8cfb18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cfb1c: cmp             SP, x16
    //     0x8cfb20: b.ls            #0x8d0278
    // 0x8cfb24: cmp             x7, x3
    // 0x8cfb28: b.lt            #0x8cff28
    // 0x8cfb2c: r0 = *()
    //     0x8cfb2c: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfb30: mov             x1, x0
    // 0x8cfb34: ldur            x0, [fp, #-0x60]
    // 0x8cfb38: cbz             x0, #0x8d0230
    // 0x8cfb3c: ldur            x2, [fp, #-8]
    // 0x8cfb40: r0 = _rem()
    //     0x8cfb40: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8cfb44: LoadField: r1 = r0->field_7
    //     0x8cfb44: ldur            w1, [x0, #7]
    // 0x8cfb48: DecompressPointer r1
    //     0x8cfb48: add             x1, x1, HEAP, lsl #32
    // 0x8cfb4c: tbnz            w1, #4, #0x8cfb74
    // 0x8cfb50: ldur            x3, [fp, #-0x58]
    // 0x8cfb54: tbnz            w3, #4, #0x8cfb68
    // 0x8cfb58: mov             x1, x0
    // 0x8cfb5c: ldur            x2, [fp, #-8]
    // 0x8cfb60: r0 = -()
    //     0x8cfb60: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfb64: b               #0x8cfb74
    // 0x8cfb68: mov             x1, x0
    // 0x8cfb6c: ldur            x2, [fp, #-8]
    // 0x8cfb70: r0 = +()
    //     0x8cfb70: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cfb74: ldur            x1, [fp, #-0x38]
    // 0x8cfb78: ldur            x2, [fp, #-0x28]
    // 0x8cfb7c: stur            x0, [fp, #-0x70]
    // 0x8cfb80: r0 = <<()
    //     0x8cfb80: bl              #0x8cc42c  ; [dart:core] _BigIntImpl::<<
    // 0x8cfb84: ldur            x1, [fp, #-0x20]
    // 0x8cfb88: mov             x2, x0
    // 0x8cfb8c: r0 = &()
    //     0x8cfb8c: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8cfb90: stur            x0, [fp, #-0x78]
    // 0x8cfb94: r0 = InitLateStaticField(0x330) // [dart:core] _BigIntImpl::zero
    //     0x8cfb94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8cfb98: ldr             x0, [x0, #0x660]
    //     0x8cfb9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8cfba0: cmp             w0, w16
    //     0x8cfba4: b.ne            #0x8cfbb4
    //     0x8cfba8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18818] Field <<EMAIL>>: static late final (offset: 0x330)
    //     0x8cfbac: ldr             x2, [x2, #0x818]
    //     0x8cfbb0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8cfbb4: ldur            x16, [fp, #-0x78]
    // 0x8cfbb8: stp             x0, x16, [SP]
    // 0x8cfbbc: r0 = ==()
    //     0x8cfbbc: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8cfbc0: eor             x1, x0, #0x10
    // 0x8cfbc4: tbnz            w1, #4, #0x8cfd94
    // 0x8cfbc8: ldur            x0, [fp, #-0x60]
    // 0x8cfbcc: ldur            x1, [fp, #-0x70]
    // 0x8cfbd0: ldur            x2, [fp, #-0x18]
    // 0x8cfbd4: r0 = *()
    //     0x8cfbd4: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfbd8: mov             x1, x0
    // 0x8cfbdc: ldur            x0, [fp, #-0x60]
    // 0x8cfbe0: cbz             x0, #0x8d01f0
    // 0x8cfbe4: ldur            x2, [fp, #-8]
    // 0x8cfbe8: r0 = _rem()
    //     0x8cfbe8: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8cfbec: LoadField: r1 = r0->field_7
    //     0x8cfbec: ldur            w1, [x0, #7]
    // 0x8cfbf0: DecompressPointer r1
    //     0x8cfbf0: add             x1, x1, HEAP, lsl #32
    // 0x8cfbf4: tbnz            w1, #4, #0x8cfc24
    // 0x8cfbf8: ldur            x3, [fp, #-0x58]
    // 0x8cfbfc: tbnz            w3, #4, #0x8cfc10
    // 0x8cfc00: mov             x1, x0
    // 0x8cfc04: ldur            x2, [fp, #-8]
    // 0x8cfc08: r0 = -()
    //     0x8cfc08: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfc0c: b               #0x8cfc1c
    // 0x8cfc10: mov             x1, x0
    // 0x8cfc14: ldur            x2, [fp, #-8]
    // 0x8cfc18: r0 = +()
    //     0x8cfc18: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cfc1c: mov             x3, x0
    // 0x8cfc20: b               #0x8cfc28
    // 0x8cfc24: mov             x3, x0
    // 0x8cfc28: ldur            x0, [fp, #-0x60]
    // 0x8cfc2c: ldur            x1, [fp, #-0x40]
    // 0x8cfc30: ldur            x2, [fp, #-0x50]
    // 0x8cfc34: stur            x3, [fp, #-0x78]
    // 0x8cfc38: r0 = *()
    //     0x8cfc38: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfc3c: mov             x1, x0
    // 0x8cfc40: ldur            x0, [fp, #-0x60]
    // 0x8cfc44: cbz             x0, #0x8d01e0
    // 0x8cfc48: ldur            x2, [fp, #-8]
    // 0x8cfc4c: r0 = _rem()
    //     0x8cfc4c: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8cfc50: LoadField: r1 = r0->field_7
    //     0x8cfc50: ldur            w1, [x0, #7]
    // 0x8cfc54: DecompressPointer r1
    //     0x8cfc54: add             x1, x1, HEAP, lsl #32
    // 0x8cfc58: tbnz            w1, #4, #0x8cfc88
    // 0x8cfc5c: ldur            x3, [fp, #-0x58]
    // 0x8cfc60: tbnz            w3, #4, #0x8cfc74
    // 0x8cfc64: mov             x1, x0
    // 0x8cfc68: ldur            x2, [fp, #-8]
    // 0x8cfc6c: r0 = -()
    //     0x8cfc6c: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfc70: b               #0x8cfc80
    // 0x8cfc74: mov             x1, x0
    // 0x8cfc78: ldur            x2, [fp, #-8]
    // 0x8cfc7c: r0 = +()
    //     0x8cfc7c: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cfc80: mov             x3, x0
    // 0x8cfc84: b               #0x8cfc8c
    // 0x8cfc88: mov             x3, x0
    // 0x8cfc8c: ldur            x0, [fp, #-0x60]
    // 0x8cfc90: ldur            x1, [fp, #-0x50]
    // 0x8cfc94: ldur            x2, [fp, #-0x48]
    // 0x8cfc98: stur            x3, [fp, #-0x80]
    // 0x8cfc9c: r0 = *()
    //     0x8cfc9c: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfca0: ldur            x1, [fp, #-0x10]
    // 0x8cfca4: ldur            x2, [fp, #-0x70]
    // 0x8cfca8: stur            x0, [fp, #-0x88]
    // 0x8cfcac: r0 = *()
    //     0x8cfcac: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfcb0: ldur            x1, [fp, #-0x88]
    // 0x8cfcb4: mov             x2, x0
    // 0x8cfcb8: r0 = -()
    //     0x8cfcb8: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfcbc: mov             x1, x0
    // 0x8cfcc0: ldur            x0, [fp, #-0x60]
    // 0x8cfcc4: cbz             x0, #0x8d01d0
    // 0x8cfcc8: ldur            x2, [fp, #-8]
    // 0x8cfccc: r0 = _rem()
    //     0x8cfccc: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8cfcd0: LoadField: r1 = r0->field_7
    //     0x8cfcd0: ldur            w1, [x0, #7]
    // 0x8cfcd4: DecompressPointer r1
    //     0x8cfcd4: add             x1, x1, HEAP, lsl #32
    // 0x8cfcd8: tbnz            w1, #4, #0x8cfd08
    // 0x8cfcdc: ldur            x3, [fp, #-0x58]
    // 0x8cfce0: tbnz            w3, #4, #0x8cfcf4
    // 0x8cfce4: mov             x1, x0
    // 0x8cfce8: ldur            x2, [fp, #-8]
    // 0x8cfcec: r0 = -()
    //     0x8cfcec: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfcf0: b               #0x8cfd00
    // 0x8cfcf4: mov             x1, x0
    // 0x8cfcf8: ldur            x2, [fp, #-8]
    // 0x8cfcfc: r0 = +()
    //     0x8cfcfc: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cfd00: mov             x3, x0
    // 0x8cfd04: b               #0x8cfd0c
    // 0x8cfd08: mov             x3, x0
    // 0x8cfd0c: ldur            x0, [fp, #-0x60]
    // 0x8cfd10: ldur            x1, [fp, #-0x50]
    // 0x8cfd14: ldur            x2, [fp, #-0x50]
    // 0x8cfd18: stur            x3, [fp, #-0x88]
    // 0x8cfd1c: r0 = *()
    //     0x8cfd1c: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfd20: ldur            x1, [fp, #-0x78]
    // 0x8cfd24: r2 = 1
    //     0x8cfd24: movz            x2, #0x1
    // 0x8cfd28: stur            x0, [fp, #-0x90]
    // 0x8cfd2c: r0 = <<()
    //     0x8cfd2c: bl              #0x8cc42c  ; [dart:core] _BigIntImpl::<<
    // 0x8cfd30: ldur            x1, [fp, #-0x90]
    // 0x8cfd34: mov             x2, x0
    // 0x8cfd38: r0 = -()
    //     0x8cfd38: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfd3c: mov             x1, x0
    // 0x8cfd40: ldur            x0, [fp, #-0x60]
    // 0x8cfd44: cbz             x0, #0x8d01c0
    // 0x8cfd48: ldur            x2, [fp, #-8]
    // 0x8cfd4c: r0 = _rem()
    //     0x8cfd4c: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8cfd50: LoadField: r1 = r0->field_7
    //     0x8cfd50: ldur            w1, [x0, #7]
    // 0x8cfd54: DecompressPointer r1
    //     0x8cfd54: add             x1, x1, HEAP, lsl #32
    // 0x8cfd58: tbnz            w1, #4, #0x8cfd80
    // 0x8cfd5c: ldur            x3, [fp, #-0x58]
    // 0x8cfd60: tbnz            w3, #4, #0x8cfd74
    // 0x8cfd64: mov             x1, x0
    // 0x8cfd68: ldur            x2, [fp, #-8]
    // 0x8cfd6c: r0 = -()
    //     0x8cfd6c: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfd70: b               #0x8cfd80
    // 0x8cfd74: mov             x1, x0
    // 0x8cfd78: ldur            x2, [fp, #-8]
    // 0x8cfd7c: r0 = +()
    //     0x8cfd7c: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cfd80: ldur            x10, [fp, #-0x80]
    // 0x8cfd84: ldur            x9, [fp, #-0x88]
    // 0x8cfd88: mov             x8, x0
    // 0x8cfd8c: ldur            x2, [fp, #-0x78]
    // 0x8cfd90: b               #0x8cff04
    // 0x8cfd94: ldur            x0, [fp, #-0x60]
    // 0x8cfd98: ldur            x1, [fp, #-0x40]
    // 0x8cfd9c: ldur            x2, [fp, #-0x48]
    // 0x8cfda0: r0 = *()
    //     0x8cfda0: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfda4: mov             x1, x0
    // 0x8cfda8: ldur            x2, [fp, #-0x70]
    // 0x8cfdac: r0 = -()
    //     0x8cfdac: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfdb0: mov             x1, x0
    // 0x8cfdb4: ldur            x0, [fp, #-0x60]
    // 0x8cfdb8: cbz             x0, #0x8d0220
    // 0x8cfdbc: ldur            x2, [fp, #-8]
    // 0x8cfdc0: r0 = _rem()
    //     0x8cfdc0: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8cfdc4: LoadField: r1 = r0->field_7
    //     0x8cfdc4: ldur            w1, [x0, #7]
    // 0x8cfdc8: DecompressPointer r1
    //     0x8cfdc8: add             x1, x1, HEAP, lsl #32
    // 0x8cfdcc: tbnz            w1, #4, #0x8cfdfc
    // 0x8cfdd0: ldur            x3, [fp, #-0x58]
    // 0x8cfdd4: tbnz            w3, #4, #0x8cfde8
    // 0x8cfdd8: mov             x1, x0
    // 0x8cfddc: ldur            x2, [fp, #-8]
    // 0x8cfde0: r0 = -()
    //     0x8cfde0: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfde4: b               #0x8cfdf4
    // 0x8cfde8: mov             x1, x0
    // 0x8cfdec: ldur            x2, [fp, #-8]
    // 0x8cfdf0: r0 = +()
    //     0x8cfdf0: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cfdf4: mov             x3, x0
    // 0x8cfdf8: b               #0x8cfe00
    // 0x8cfdfc: mov             x3, x0
    // 0x8cfe00: ldur            x0, [fp, #-0x60]
    // 0x8cfe04: ldur            x1, [fp, #-0x50]
    // 0x8cfe08: ldur            x2, [fp, #-0x48]
    // 0x8cfe0c: stur            x3, [fp, #-0x78]
    // 0x8cfe10: r0 = *()
    //     0x8cfe10: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfe14: ldur            x1, [fp, #-0x10]
    // 0x8cfe18: ldur            x2, [fp, #-0x70]
    // 0x8cfe1c: stur            x0, [fp, #-0x80]
    // 0x8cfe20: r0 = *()
    //     0x8cfe20: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfe24: ldur            x1, [fp, #-0x80]
    // 0x8cfe28: mov             x2, x0
    // 0x8cfe2c: r0 = -()
    //     0x8cfe2c: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfe30: mov             x1, x0
    // 0x8cfe34: ldur            x0, [fp, #-0x60]
    // 0x8cfe38: cbz             x0, #0x8d0210
    // 0x8cfe3c: ldur            x2, [fp, #-8]
    // 0x8cfe40: r0 = _rem()
    //     0x8cfe40: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8cfe44: LoadField: r1 = r0->field_7
    //     0x8cfe44: ldur            w1, [x0, #7]
    // 0x8cfe48: DecompressPointer r1
    //     0x8cfe48: add             x1, x1, HEAP, lsl #32
    // 0x8cfe4c: tbnz            w1, #4, #0x8cfe7c
    // 0x8cfe50: ldur            x3, [fp, #-0x58]
    // 0x8cfe54: tbnz            w3, #4, #0x8cfe68
    // 0x8cfe58: mov             x1, x0
    // 0x8cfe5c: ldur            x2, [fp, #-8]
    // 0x8cfe60: r0 = -()
    //     0x8cfe60: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfe64: b               #0x8cfe74
    // 0x8cfe68: mov             x1, x0
    // 0x8cfe6c: ldur            x2, [fp, #-8]
    // 0x8cfe70: r0 = +()
    //     0x8cfe70: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cfe74: mov             x3, x0
    // 0x8cfe78: b               #0x8cfe80
    // 0x8cfe7c: mov             x3, x0
    // 0x8cfe80: ldur            x0, [fp, #-0x60]
    // 0x8cfe84: ldur            x1, [fp, #-0x48]
    // 0x8cfe88: ldur            x2, [fp, #-0x48]
    // 0x8cfe8c: stur            x3, [fp, #-0x80]
    // 0x8cfe90: r0 = *()
    //     0x8cfe90: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cfe94: ldur            x1, [fp, #-0x70]
    // 0x8cfe98: r2 = 1
    //     0x8cfe98: movz            x2, #0x1
    // 0x8cfe9c: stur            x0, [fp, #-0x88]
    // 0x8cfea0: r0 = <<()
    //     0x8cfea0: bl              #0x8cc42c  ; [dart:core] _BigIntImpl::<<
    // 0x8cfea4: ldur            x1, [fp, #-0x88]
    // 0x8cfea8: mov             x2, x0
    // 0x8cfeac: r0 = -()
    //     0x8cfeac: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfeb0: mov             x1, x0
    // 0x8cfeb4: ldur            x0, [fp, #-0x60]
    // 0x8cfeb8: cbz             x0, #0x8d0200
    // 0x8cfebc: ldur            x2, [fp, #-8]
    // 0x8cfec0: r0 = _rem()
    //     0x8cfec0: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8cfec4: LoadField: r1 = r0->field_7
    //     0x8cfec4: ldur            w1, [x0, #7]
    // 0x8cfec8: DecompressPointer r1
    //     0x8cfec8: add             x1, x1, HEAP, lsl #32
    // 0x8cfecc: tbnz            w1, #4, #0x8cfef4
    // 0x8cfed0: ldur            x3, [fp, #-0x58]
    // 0x8cfed4: tbnz            w3, #4, #0x8cfee8
    // 0x8cfed8: mov             x1, x0
    // 0x8cfedc: ldur            x2, [fp, #-8]
    // 0x8cfee0: r0 = -()
    //     0x8cfee0: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cfee4: b               #0x8cfef4
    // 0x8cfee8: mov             x1, x0
    // 0x8cfeec: ldur            x2, [fp, #-8]
    // 0x8cfef0: r0 = +()
    //     0x8cfef0: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8cfef4: ldur            x10, [fp, #-0x78]
    // 0x8cfef8: mov             x9, x0
    // 0x8cfefc: ldur            x8, [fp, #-0x80]
    // 0x8cff00: ldur            x2, [fp, #-0x70]
    // 0x8cff04: ldur            x0, [fp, #-0x28]
    // 0x8cff08: sub             x7, x0, #1
    // 0x8cff0c: ldur            x1, [fp, #-0x70]
    // 0x8cff10: ldur            x4, [fp, #-8]
    // 0x8cff14: ldur            x0, [fp, #-0x30]
    // 0x8cff18: ldur            x3, [fp, #-0x68]
    // 0x8cff1c: ldur            x6, [fp, #-0x58]
    // 0x8cff20: ldur            x5, [fp, #-0x60]
    // 0x8cff24: b               #0x8cfb08
    // 0x8cff28: r0 = *()
    //     0x8cff28: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cff2c: mov             x1, x0
    // 0x8cff30: ldur            x2, [fp, #-8]
    // 0x8cff34: r0 = %()
    //     0x8cff34: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8cff38: mov             x1, x0
    // 0x8cff3c: ldur            x2, [fp, #-0x18]
    // 0x8cff40: stur            x0, [fp, #-0x18]
    // 0x8cff44: r0 = *()
    //     0x8cff44: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cff48: mov             x1, x0
    // 0x8cff4c: ldur            x2, [fp, #-8]
    // 0x8cff50: r0 = %()
    //     0x8cff50: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8cff54: ldur            x1, [fp, #-0x40]
    // 0x8cff58: ldur            x2, [fp, #-0x48]
    // 0x8cff5c: stur            x0, [fp, #-0x20]
    // 0x8cff60: r0 = *()
    //     0x8cff60: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cff64: mov             x1, x0
    // 0x8cff68: ldur            x2, [fp, #-0x18]
    // 0x8cff6c: r0 = -()
    //     0x8cff6c: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cff70: mov             x1, x0
    // 0x8cff74: ldur            x2, [fp, #-8]
    // 0x8cff78: r0 = %()
    //     0x8cff78: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8cff7c: ldur            x1, [fp, #-0x50]
    // 0x8cff80: ldur            x2, [fp, #-0x48]
    // 0x8cff84: stur            x0, [fp, #-0x38]
    // 0x8cff88: r0 = *()
    //     0x8cff88: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cff8c: ldur            x1, [fp, #-0x10]
    // 0x8cff90: ldur            x2, [fp, #-0x18]
    // 0x8cff94: stur            x0, [fp, #-0x10]
    // 0x8cff98: r0 = *()
    //     0x8cff98: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cff9c: ldur            x1, [fp, #-0x10]
    // 0x8cffa0: mov             x2, x0
    // 0x8cffa4: r0 = -()
    //     0x8cffa4: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8cffa8: mov             x1, x0
    // 0x8cffac: ldur            x2, [fp, #-8]
    // 0x8cffb0: r0 = %()
    //     0x8cffb0: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8cffb4: ldur            x1, [fp, #-0x18]
    // 0x8cffb8: ldur            x2, [fp, #-0x20]
    // 0x8cffbc: stur            x0, [fp, #-0x10]
    // 0x8cffc0: r0 = *()
    //     0x8cffc0: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8cffc4: mov             x1, x0
    // 0x8cffc8: ldur            x2, [fp, #-8]
    // 0x8cffcc: r0 = %()
    //     0x8cffcc: bl              #0x8ca074  ; [dart:core] _BigIntImpl::%
    // 0x8cffd0: ldur            x8, [fp, #-0x38]
    // 0x8cffd4: ldur            x7, [fp, #-0x10]
    // 0x8cffd8: mov             x6, x0
    // 0x8cffdc: r5 = 1
    //     0x8cffdc: movz            x5, #0x1
    // 0x8cffe0: ldur            x4, [fp, #-0x30]
    // 0x8cffe4: ldur            x0, [fp, #-0x58]
    // 0x8cffe8: ldur            x3, [fp, #-0x60]
    // 0x8cffec: stur            x7, [fp, #-0x10]
    // 0x8cfff0: stur            x6, [fp, #-0x18]
    // 0x8cfff4: stur            x5, [fp, #-0x28]
    // 0x8cfff8: stur            x8, [fp, #-0x40]
    // 0x8cfffc: CheckStackOverflow
    //     0x8cfffc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d0000: cmp             SP, x16
    //     0x8d0004: b.ls            #0x8d0280
    // 0x8d0008: cmp             x5, x4
    // 0x8d000c: b.gt            #0x8d0164
    // 0x8d0010: mov             x1, x8
    // 0x8d0014: mov             x2, x7
    // 0x8d0018: r0 = *()
    //     0x8d0018: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8d001c: mov             x1, x0
    // 0x8d0020: ldur            x0, [fp, #-0x60]
    // 0x8d0024: cbz             x0, #0x8d0260
    // 0x8d0028: ldur            x2, [fp, #-8]
    // 0x8d002c: r0 = _rem()
    //     0x8d002c: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8d0030: LoadField: r1 = r0->field_7
    //     0x8d0030: ldur            w1, [x0, #7]
    // 0x8d0034: DecompressPointer r1
    //     0x8d0034: add             x1, x1, HEAP, lsl #32
    // 0x8d0038: tbnz            w1, #4, #0x8d0068
    // 0x8d003c: ldur            x3, [fp, #-0x58]
    // 0x8d0040: tbnz            w3, #4, #0x8d0054
    // 0x8d0044: mov             x1, x0
    // 0x8d0048: ldur            x2, [fp, #-8]
    // 0x8d004c: r0 = -()
    //     0x8d004c: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8d0050: b               #0x8d0060
    // 0x8d0054: mov             x1, x0
    // 0x8d0058: ldur            x2, [fp, #-8]
    // 0x8d005c: r0 = +()
    //     0x8d005c: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8d0060: mov             x8, x0
    // 0x8d0064: b               #0x8d006c
    // 0x8d0068: mov             x8, x0
    // 0x8d006c: ldur            x0, [fp, #-0x60]
    // 0x8d0070: ldur            x1, [fp, #-0x10]
    // 0x8d0074: ldur            x2, [fp, #-0x10]
    // 0x8d0078: stur            x8, [fp, #-0x20]
    // 0x8d007c: r0 = *()
    //     0x8d007c: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8d0080: ldur            x1, [fp, #-0x18]
    // 0x8d0084: r2 = 1
    //     0x8d0084: movz            x2, #0x1
    // 0x8d0088: stur            x0, [fp, #-0x38]
    // 0x8d008c: r0 = <<()
    //     0x8d008c: bl              #0x8cc42c  ; [dart:core] _BigIntImpl::<<
    // 0x8d0090: ldur            x1, [fp, #-0x38]
    // 0x8d0094: mov             x2, x0
    // 0x8d0098: r0 = -()
    //     0x8d0098: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8d009c: mov             x1, x0
    // 0x8d00a0: ldur            x0, [fp, #-0x60]
    // 0x8d00a4: cbz             x0, #0x8d0250
    // 0x8d00a8: ldur            x2, [fp, #-8]
    // 0x8d00ac: r0 = _rem()
    //     0x8d00ac: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8d00b0: LoadField: r1 = r0->field_7
    //     0x8d00b0: ldur            w1, [x0, #7]
    // 0x8d00b4: DecompressPointer r1
    //     0x8d00b4: add             x1, x1, HEAP, lsl #32
    // 0x8d00b8: tbnz            w1, #4, #0x8d00e8
    // 0x8d00bc: ldur            x3, [fp, #-0x58]
    // 0x8d00c0: tbnz            w3, #4, #0x8d00d4
    // 0x8d00c4: mov             x1, x0
    // 0x8d00c8: ldur            x2, [fp, #-8]
    // 0x8d00cc: r0 = -()
    //     0x8d00cc: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8d00d0: b               #0x8d00e0
    // 0x8d00d4: mov             x1, x0
    // 0x8d00d8: ldur            x2, [fp, #-8]
    // 0x8d00dc: r0 = +()
    //     0x8d00dc: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8d00e0: mov             x7, x0
    // 0x8d00e4: b               #0x8d00ec
    // 0x8d00e8: mov             x7, x0
    // 0x8d00ec: ldur            x0, [fp, #-0x60]
    // 0x8d00f0: ldur            x1, [fp, #-0x18]
    // 0x8d00f4: ldur            x2, [fp, #-0x18]
    // 0x8d00f8: stur            x7, [fp, #-0x38]
    // 0x8d00fc: r0 = *()
    //     0x8d00fc: bl              #0x664538  ; [dart:core] _BigIntImpl::*
    // 0x8d0100: mov             x1, x0
    // 0x8d0104: ldur            x0, [fp, #-0x60]
    // 0x8d0108: cbz             x0, #0x8d0240
    // 0x8d010c: ldur            x2, [fp, #-8]
    // 0x8d0110: r0 = _rem()
    //     0x8d0110: bl              #0x5f7188  ; [dart:core] _BigIntImpl::_rem
    // 0x8d0114: LoadField: r1 = r0->field_7
    //     0x8d0114: ldur            w1, [x0, #7]
    // 0x8d0118: DecompressPointer r1
    //     0x8d0118: add             x1, x1, HEAP, lsl #32
    // 0x8d011c: tbnz            w1, #4, #0x8d014c
    // 0x8d0120: ldur            x3, [fp, #-0x58]
    // 0x8d0124: tbnz            w3, #4, #0x8d0138
    // 0x8d0128: mov             x1, x0
    // 0x8d012c: ldur            x2, [fp, #-8]
    // 0x8d0130: r0 = -()
    //     0x8d0130: bl              #0x66249c  ; [dart:core] _BigIntImpl::-
    // 0x8d0134: b               #0x8d0144
    // 0x8d0138: mov             x1, x0
    // 0x8d013c: ldur            x2, [fp, #-8]
    // 0x8d0140: r0 = +()
    //     0x8d0140: bl              #0x665c68  ; [dart:core] _BigIntImpl::+
    // 0x8d0144: mov             x6, x0
    // 0x8d0148: b               #0x8d0150
    // 0x8d014c: mov             x6, x0
    // 0x8d0150: ldur            x0, [fp, #-0x28]
    // 0x8d0154: add             x5, x0, #1
    // 0x8d0158: ldur            x8, [fp, #-0x20]
    // 0x8d015c: ldur            x7, [fp, #-0x38]
    // 0x8d0160: b               #0x8cffe0
    // 0x8d0164: mov             x0, x7
    // 0x8d0168: r3 = 4
    //     0x8d0168: movz            x3, #0x4
    // 0x8d016c: mov             x2, x3
    // 0x8d0170: r1 = Null
    //     0x8d0170: mov             x1, NULL
    // 0x8d0174: r0 = AllocateArray()
    //     0x8d0174: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8d0178: mov             x2, x0
    // 0x8d017c: ldur            x0, [fp, #-0x40]
    // 0x8d0180: stur            x2, [fp, #-8]
    // 0x8d0184: StoreField: r2->field_f = r0
    //     0x8d0184: stur            w0, [x2, #0xf]
    // 0x8d0188: ldur            x0, [fp, #-0x10]
    // 0x8d018c: StoreField: r2->field_13 = r0
    //     0x8d018c: stur            w0, [x2, #0x13]
    // 0x8d0190: r1 = <BigInt>
    //     0x8d0190: add             x1, PP, #0xb, lsl #12  ; [pp+0xbdf0] TypeArguments: <BigInt>
    //     0x8d0194: ldr             x1, [x1, #0xdf0]
    // 0x8d0198: r0 = AllocateGrowableArray()
    //     0x8d0198: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8d019c: mov             x1, x0
    // 0x8d01a0: ldur            x0, [fp, #-8]
    // 0x8d01a4: StoreField: r1->field_f = r0
    //     0x8d01a4: stur            w0, [x1, #0xf]
    // 0x8d01a8: r0 = 4
    //     0x8d01a8: movz            x0, #0x4
    // 0x8d01ac: StoreField: r1->field_b = r0
    //     0x8d01ac: stur            w0, [x1, #0xb]
    // 0x8d01b0: mov             x0, x1
    // 0x8d01b4: LeaveFrame
    //     0x8d01b4: mov             SP, fp
    //     0x8d01b8: ldp             fp, lr, [SP], #0x10
    // 0x8d01bc: ret
    //     0x8d01bc: ret             
    // 0x8d01c0: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d01c0: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d01c4: ldr             x0, [x0, #0x858]
    // 0x8d01c8: r0 = Throw()
    //     0x8d01c8: bl              #0xec04b8  ; ThrowStub
    // 0x8d01cc: brk             #0
    // 0x8d01d0: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d01d0: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d01d4: ldr             x0, [x0, #0x858]
    // 0x8d01d8: r0 = Throw()
    //     0x8d01d8: bl              #0xec04b8  ; ThrowStub
    // 0x8d01dc: brk             #0
    // 0x8d01e0: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d01e0: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d01e4: ldr             x0, [x0, #0x858]
    // 0x8d01e8: r0 = Throw()
    //     0x8d01e8: bl              #0xec04b8  ; ThrowStub
    // 0x8d01ec: brk             #0
    // 0x8d01f0: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d01f0: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d01f4: ldr             x0, [x0, #0x858]
    // 0x8d01f8: r0 = Throw()
    //     0x8d01f8: bl              #0xec04b8  ; ThrowStub
    // 0x8d01fc: brk             #0
    // 0x8d0200: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d0200: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d0204: ldr             x0, [x0, #0x858]
    // 0x8d0208: r0 = Throw()
    //     0x8d0208: bl              #0xec04b8  ; ThrowStub
    // 0x8d020c: brk             #0
    // 0x8d0210: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d0210: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d0214: ldr             x0, [x0, #0x858]
    // 0x8d0218: r0 = Throw()
    //     0x8d0218: bl              #0xec04b8  ; ThrowStub
    // 0x8d021c: brk             #0
    // 0x8d0220: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d0220: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d0224: ldr             x0, [x0, #0x858]
    // 0x8d0228: r0 = Throw()
    //     0x8d0228: bl              #0xec04b8  ; ThrowStub
    // 0x8d022c: brk             #0
    // 0x8d0230: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d0230: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d0234: ldr             x0, [x0, #0x858]
    // 0x8d0238: r0 = Throw()
    //     0x8d0238: bl              #0xec04b8  ; ThrowStub
    // 0x8d023c: brk             #0
    // 0x8d0240: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d0240: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d0244: ldr             x0, [x0, #0x858]
    // 0x8d0248: r0 = Throw()
    //     0x8d0248: bl              #0xec04b8  ; ThrowStub
    // 0x8d024c: brk             #0
    // 0x8d0250: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d0250: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d0254: ldr             x0, [x0, #0x858]
    // 0x8d0258: r0 = Throw()
    //     0x8d0258: bl              #0xec04b8  ; ThrowStub
    // 0x8d025c: brk             #0
    // 0x8d0260: r0 = Instance_IntegerDivisionByZeroException
    //     0x8d0260: add             x0, PP, #0x18, lsl #12  ; [pp+0x18858] Obj!IntegerDivisionByZeroException@e2d0c1
    //     0x8d0264: ldr             x0, [x0, #0x858]
    // 0x8d0268: r0 = Throw()
    //     0x8d0268: bl              #0xec04b8  ; ThrowStub
    // 0x8d026c: brk             #0
    // 0x8d0270: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0270: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0274: b               #0x8cfa64
    // 0x8d0278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0278: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d027c: b               #0x8cfb24
    // 0x8d0280: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0280: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0284: b               #0x8d0008
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf2fa8, size: 0x90
    // 0xbf2fa8: EnterFrame
    //     0xbf2fa8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2fac: mov             fp, SP
    // 0xbf2fb0: AllocStack(0x10)
    //     0xbf2fb0: sub             SP, SP, #0x10
    // 0xbf2fb4: CheckStackOverflow
    //     0xbf2fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2fb8: cmp             SP, x16
    //     0xbf2fbc: b.ls            #0xbf3030
    // 0xbf2fc0: ldr             x0, [fp, #0x10]
    // 0xbf2fc4: LoadField: r1 = r0->field_7
    //     0xbf2fc4: ldur            w1, [x0, #7]
    // 0xbf2fc8: DecompressPointer r1
    //     0xbf2fc8: add             x1, x1, HEAP, lsl #32
    // 0xbf2fcc: str             x1, [SP]
    // 0xbf2fd0: r0 = hashCode()
    //     0xbf2fd0: bl              #0xbdd108  ; [dart:core] _BigIntImpl::hashCode
    // 0xbf2fd4: mov             x1, x0
    // 0xbf2fd8: ldr             x0, [fp, #0x10]
    // 0xbf2fdc: stur            x1, [fp, #-8]
    // 0xbf2fe0: LoadField: r2 = r0->field_b
    //     0xbf2fe0: ldur            w2, [x0, #0xb]
    // 0xbf2fe4: DecompressPointer r2
    //     0xbf2fe4: add             x2, x2, HEAP, lsl #32
    // 0xbf2fe8: str             x2, [SP]
    // 0xbf2fec: r0 = hashCode()
    //     0xbf2fec: bl              #0xbdd108  ; [dart:core] _BigIntImpl::hashCode
    // 0xbf2ff0: ldur            x2, [fp, #-8]
    // 0xbf2ff4: r3 = LoadInt32Instr(r2)
    //     0xbf2ff4: sbfx            x3, x2, #1, #0x1f
    //     0xbf2ff8: tbz             w2, #0, #0xbf3000
    //     0xbf2ffc: ldur            x3, [x2, #7]
    // 0xbf3000: r2 = LoadInt32Instr(r0)
    //     0xbf3000: sbfx            x2, x0, #1, #0x1f
    //     0xbf3004: tbz             w0, #0, #0xbf300c
    //     0xbf3008: ldur            x2, [x0, #7]
    // 0xbf300c: eor             x4, x3, x2
    // 0xbf3010: r0 = BoxInt64Instr(r4)
    //     0xbf3010: sbfiz           x0, x4, #1, #0x1f
    //     0xbf3014: cmp             x4, x0, asr #1
    //     0xbf3018: b.eq            #0xbf3024
    //     0xbf301c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3020: stur            x4, [x0, #7]
    // 0xbf3024: LeaveFrame
    //     0xbf3024: mov             SP, fp
    //     0xbf3028: ldp             fp, lr, [SP], #0x10
    // 0xbf302c: ret
    //     0xbf302c: ret             
    // 0xbf3030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3030: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf3034: b               #0xbf2fc0
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d2b4, size: 0xb8
    // 0xd7d2b4: EnterFrame
    //     0xd7d2b4: stp             fp, lr, [SP, #-0x10]!
    //     0xd7d2b8: mov             fp, SP
    // 0xd7d2bc: AllocStack(0x10)
    //     0xd7d2bc: sub             SP, SP, #0x10
    // 0xd7d2c0: CheckStackOverflow
    //     0xd7d2c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7d2c4: cmp             SP, x16
    //     0xd7d2c8: b.ls            #0xd7d364
    // 0xd7d2cc: ldr             x0, [fp, #0x10]
    // 0xd7d2d0: cmp             w0, NULL
    // 0xd7d2d4: b.ne            #0xd7d2e8
    // 0xd7d2d8: r0 = false
    //     0xd7d2d8: add             x0, NULL, #0x30  ; false
    // 0xd7d2dc: LeaveFrame
    //     0xd7d2dc: mov             SP, fp
    //     0xd7d2e0: ldp             fp, lr, [SP], #0x10
    // 0xd7d2e4: ret
    //     0xd7d2e4: ret             
    // 0xd7d2e8: r1 = 60
    //     0xd7d2e8: movz            x1, #0x3c
    // 0xd7d2ec: branchIfSmi(r0, 0xd7d2f8)
    //     0xd7d2ec: tbz             w0, #0, #0xd7d2f8
    // 0xd7d2f0: r1 = LoadClassIdInstr(r0)
    //     0xd7d2f0: ldur            x1, [x0, #-1]
    //     0xd7d2f4: ubfx            x1, x1, #0xc, #0x14
    // 0xd7d2f8: cmp             x1, #0x255
    // 0xd7d2fc: b.ne            #0xd7d354
    // 0xd7d300: ldr             x1, [fp, #0x18]
    // 0xd7d304: LoadField: r2 = r1->field_7
    //     0xd7d304: ldur            w2, [x1, #7]
    // 0xd7d308: DecompressPointer r2
    //     0xd7d308: add             x2, x2, HEAP, lsl #32
    // 0xd7d30c: LoadField: r3 = r0->field_7
    //     0xd7d30c: ldur            w3, [x0, #7]
    // 0xd7d310: DecompressPointer r3
    //     0xd7d310: add             x3, x3, HEAP, lsl #32
    // 0xd7d314: stp             x3, x2, [SP]
    // 0xd7d318: r0 = ==()
    //     0xd7d318: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0xd7d31c: tbnz            w0, #4, #0xd7d344
    // 0xd7d320: ldr             x1, [fp, #0x18]
    // 0xd7d324: ldr             x0, [fp, #0x10]
    // 0xd7d328: LoadField: r2 = r1->field_b
    //     0xd7d328: ldur            w2, [x1, #0xb]
    // 0xd7d32c: DecompressPointer r2
    //     0xd7d32c: add             x2, x2, HEAP, lsl #32
    // 0xd7d330: LoadField: r1 = r0->field_b
    //     0xd7d330: ldur            w1, [x0, #0xb]
    // 0xd7d334: DecompressPointer r1
    //     0xd7d334: add             x1, x1, HEAP, lsl #32
    // 0xd7d338: stp             x1, x2, [SP]
    // 0xd7d33c: r0 = ==()
    //     0xd7d33c: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0xd7d340: b               #0xd7d348
    // 0xd7d344: r0 = false
    //     0xd7d344: add             x0, NULL, #0x30  ; false
    // 0xd7d348: LeaveFrame
    //     0xd7d348: mov             SP, fp
    //     0xd7d34c: ldp             fp, lr, [SP], #0x10
    // 0xd7d350: ret
    //     0xd7d350: ret             
    // 0xd7d354: r0 = false
    //     0xd7d354: add             x0, NULL, #0x30  ; false
    // 0xd7d358: LeaveFrame
    //     0xd7d358: mov             SP, fp
    //     0xd7d35c: ldp             fp, lr, [SP], #0x10
    // 0xd7d360: ret
    //     0xd7d360: ret             
    // 0xd7d364: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7d364: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7d368: b               #0xd7d2cc
  }
}
