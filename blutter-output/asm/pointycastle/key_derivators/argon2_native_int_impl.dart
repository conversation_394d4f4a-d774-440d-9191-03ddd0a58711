// lib: , url: package:pointycastle/key_derivators/argon2_native_int_impl.dart

// class id: 1051006, size: 0x8
class :: {
}

// class id: 589, size: 0x8, field offset: 0x8
class Argon2BytesGenerator extends BaseKeyDerivator {

  static late final FactoryConfig factoryConfig; // offset: 0xf5c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c81a0, size: 0x58
    // 0x8c81a0: EnterFrame
    //     0x8c81a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c81a4: mov             fp, SP
    // 0x8c81a8: AllocStack(0x8)
    //     0x8c81a8: sub             SP, SP, #8
    // 0x8c81ac: r0 = StaticFactoryConfig()
    //     0x8c81ac: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c81b0: mov             x3, x0
    // 0x8c81b4: r0 = "argon2"
    //     0x8c81b4: add             x0, PP, #0x18, lsl #12  ; [pp+0x184d8] "argon2"
    //     0x8c81b8: ldr             x0, [x0, #0x4d8]
    // 0x8c81bc: stur            x3, [fp, #-8]
    // 0x8c81c0: StoreField: r3->field_b = r0
    //     0x8c81c0: stur            w0, [x3, #0xb]
    // 0x8c81c4: r1 = Function '<anonymous closure>': static.
    //     0x8c81c4: add             x1, PP, #0x18, lsl #12  ; [pp+0x184e0] AnonymousClosure: static (0x8c81f8), in [package:pointycastle/key_derivators/argon2_native_int_impl.dart] Argon2BytesGenerator::factoryConfig (0x8c81a0)
    //     0x8c81c8: ldr             x1, [x1, #0x4e0]
    // 0x8c81cc: r2 = Null
    //     0x8c81cc: mov             x2, NULL
    // 0x8c81d0: r0 = AllocateClosure()
    //     0x8c81d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c81d4: mov             x1, x0
    // 0x8c81d8: ldur            x0, [fp, #-8]
    // 0x8c81dc: StoreField: r0->field_f = r1
    //     0x8c81dc: stur            w1, [x0, #0xf]
    // 0x8c81e0: r1 = KeyDerivator
    //     0x8c81e0: add             x1, PP, #0x18, lsl #12  ; [pp+0x184b8] Type: KeyDerivator
    //     0x8c81e4: ldr             x1, [x1, #0x4b8]
    // 0x8c81e8: StoreField: r0->field_7 = r1
    //     0x8c81e8: stur            w1, [x0, #7]
    // 0x8c81ec: LeaveFrame
    //     0x8c81ec: mov             SP, fp
    //     0x8c81f0: ldp             fp, lr, [SP], #0x10
    // 0x8c81f4: ret
    //     0x8c81f4: ret             
  }
  [closure] static Argon2BytesGenerator <anonymous closure>(dynamic) {
    // ** addr: 0x8c81f8, size: 0x4c
    // 0x8c81f8: EnterFrame
    //     0x8c81f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c81fc: mov             fp, SP
    // 0x8c8200: CheckStackOverflow
    //     0x8c8200: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c8204: cmp             SP, x16
    //     0x8c8208: b.ls            #0x8c823c
    // 0x8c820c: r0 = InitLateStaticField(0x1734) // [package:pointycastle/src/platform_check/native.dart] PlatformIO::instance
    //     0x8c820c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8210: ldr             x0, [x0, #0x2e68]
    //     0x8c8214: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8218: cmp             w0, w16
    //     0x8c821c: b.ne            #0x8c822c
    //     0x8c8220: add             x2, PP, #0x18, lsl #12  ; [pp+0x180f8] Field <PlatformIO.instance>: static late final (offset: 0x1734)
    //     0x8c8224: ldr             x2, [x2, #0xf8]
    //     0x8c8228: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c822c: r0 = Argon2BytesGenerator()
    //     0x8c822c: bl              #0x8c8244  ; AllocateArgon2BytesGeneratorStub -> Argon2BytesGenerator (size=0x8)
    // 0x8c8230: LeaveFrame
    //     0x8c8230: mov             SP, fp
    //     0x8c8234: ldp             fp, lr, [SP], #0x10
    // 0x8c8238: ret
    //     0x8c8238: ret             
    // 0x8c823c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c823c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c8240: b               #0x8c820c
  }
}
