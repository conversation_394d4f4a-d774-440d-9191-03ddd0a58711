// lib: , url: package:pointycastle/key_derivators/concat_kdf.dart

// class id: 1051007, size: 0x8
class :: {
}

// class id: 588, size: 0x8, field offset: 0x8
class ConcatKDFDerivator extends BaseKeyDerivator {

  static late final FactoryConfig factoryConfig; // offset: 0x172c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c8074, size: 0x64
    // 0x8c8074: EnterFrame
    //     0x8c8074: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8078: mov             fp, SP
    // 0x8c807c: AllocStack(0x8)
    //     0x8c807c: sub             SP, SP, #8
    // 0x8c8080: CheckStackOverflow
    //     0x8c8080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c8084: cmp             SP, x16
    //     0x8c8088: b.ls            #0x8c80d0
    // 0x8c808c: r0 = DynamicFactoryConfig()
    //     0x8c808c: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c8090: r1 = Function '<anonymous closure>': static.
    //     0x8c8090: add             x1, PP, #0x18, lsl #12  ; [pp+0x184c0] AnonymousClosure: static (0x8c80d8), in [package:pointycastle/key_derivators/concat_kdf.dart] ConcatKDFDerivator::factoryConfig (0x8c8074)
    //     0x8c8094: ldr             x1, [x1, #0x4c0]
    // 0x8c8098: r2 = Null
    //     0x8c8098: mov             x2, NULL
    // 0x8c809c: stur            x0, [fp, #-8]
    // 0x8c80a0: r0 = AllocateClosure()
    //     0x8c80a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c80a4: ldur            x1, [fp, #-8]
    // 0x8c80a8: mov             x5, x0
    // 0x8c80ac: r2 = KeyDerivator
    //     0x8c80ac: add             x2, PP, #0x18, lsl #12  ; [pp+0x184b8] Type: KeyDerivator
    //     0x8c80b0: ldr             x2, [x2, #0x4b8]
    // 0x8c80b4: r3 = "/ConcatKDF"
    //     0x8c80b4: add             x3, PP, #0x18, lsl #12  ; [pp+0x184c8] "/ConcatKDF"
    //     0x8c80b8: ldr             x3, [x3, #0x4c8]
    // 0x8c80bc: r0 = DynamicFactoryConfig.suffix()
    //     0x8c80bc: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c80c0: ldur            x0, [fp, #-8]
    // 0x8c80c4: LeaveFrame
    //     0x8c80c4: mov             SP, fp
    //     0x8c80c8: ldp             fp, lr, [SP], #0x10
    // 0x8c80cc: ret
    //     0x8c80cc: ret             
    // 0x8c80d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c80d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c80d4: b               #0x8c808c
  }
  [closure] static (dynamic) => ConcatKDFDerivator <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c80d8, size: 0xa4
    // 0x8c80d8: EnterFrame
    //     0x8c80d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c80dc: mov             fp, SP
    // 0x8c80e0: AllocStack(0x20)
    //     0x8c80e0: sub             SP, SP, #0x20
    // 0x8c80e4: CheckStackOverflow
    //     0x8c80e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c80e8: cmp             SP, x16
    //     0x8c80ec: b.ls            #0x8c8170
    // 0x8c80f0: ldr             x1, [fp, #0x10]
    // 0x8c80f4: r0 = LoadClassIdInstr(r1)
    //     0x8c80f4: ldur            x0, [x1, #-1]
    //     0x8c80f8: ubfx            x0, x0, #0xc, #0x14
    // 0x8c80fc: r2 = 1
    //     0x8c80fc: movz            x2, #0x1
    // 0x8c8100: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c8100: sub             lr, x0, #0xfdd
    //     0x8c8104: ldr             lr, [x21, lr, lsl #3]
    //     0x8c8108: blr             lr
    // 0x8c810c: stur            x0, [fp, #-8]
    // 0x8c8110: cmp             w0, NULL
    // 0x8c8114: b.eq            #0x8c8178
    // 0x8c8118: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c8118: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c811c: ldr             x0, [x0, #0x2e38]
    //     0x8c8120: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8124: cmp             w0, w16
    //     0x8c8128: b.ne            #0x8c8138
    //     0x8c812c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c8130: ldr             x2, [x2, #0xf80]
    //     0x8c8134: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8138: r16 = <Digest>
    //     0x8c8138: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b8] TypeArguments: <Digest>
    //     0x8c813c: ldr             x16, [x16, #0x1b8]
    // 0x8c8140: stp             x0, x16, [SP, #8]
    // 0x8c8144: ldur            x16, [fp, #-8]
    // 0x8c8148: str             x16, [SP]
    // 0x8c814c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c814c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c8150: r0 = create()
    //     0x8c8150: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c8154: r1 = Function '<anonymous closure>': static.
    //     0x8c8154: add             x1, PP, #0x18, lsl #12  ; [pp+0x184d0] AnonymousClosure: static (0x8c817c), in [package:pointycastle/key_derivators/concat_kdf.dart] ConcatKDFDerivator::factoryConfig (0x8c8074)
    //     0x8c8158: ldr             x1, [x1, #0x4d0]
    // 0x8c815c: r2 = Null
    //     0x8c815c: mov             x2, NULL
    // 0x8c8160: r0 = AllocateClosure()
    //     0x8c8160: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c8164: LeaveFrame
    //     0x8c8164: mov             SP, fp
    //     0x8c8168: ldp             fp, lr, [SP], #0x10
    // 0x8c816c: ret
    //     0x8c816c: ret             
    // 0x8c8170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c8170: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c8174: b               #0x8c80f0
    // 0x8c8178: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c8178: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static ConcatKDFDerivator <anonymous closure>(dynamic) {
    // ** addr: 0x8c817c, size: 0x18
    // 0x8c817c: EnterFrame
    //     0x8c817c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8180: mov             fp, SP
    // 0x8c8184: r0 = ConcatKDFDerivator()
    //     0x8c8184: bl              #0x8c8194  ; AllocateConcatKDFDerivatorStub -> ConcatKDFDerivator (size=0x8)
    // 0x8c8188: LeaveFrame
    //     0x8c8188: mov             SP, fp
    //     0x8c818c: ldp             fp, lr, [SP], #0x10
    // 0x8c8190: ret
    //     0x8c8190: ret             
  }
}
