// lib: , url: package:pointycastle/key_derivators/ecdh_kdf.dart

// class id: 1051008, size: 0x8
class :: {
}

// class id: 587, size: 0x8, field offset: 0x8
class ECDHKeyDerivator extends BaseKeyDerivator {

  static late final FactoryConfig factoryConfig; // offset: 0x1730

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c7ff8, size: 0x58
    // 0x8c7ff8: EnterFrame
    //     0x8c7ff8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7ffc: mov             fp, SP
    // 0x8c8000: AllocStack(0x8)
    //     0x8c8000: sub             SP, SP, #8
    // 0x8c8004: r0 = StaticFactoryConfig()
    //     0x8c8004: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c8008: mov             x3, x0
    // 0x8c800c: r0 = "ECDH"
    //     0x8c800c: add             x0, PP, #0x18, lsl #12  ; [pp+0x184a8] "ECDH"
    //     0x8c8010: ldr             x0, [x0, #0x4a8]
    // 0x8c8014: stur            x3, [fp, #-8]
    // 0x8c8018: StoreField: r3->field_b = r0
    //     0x8c8018: stur            w0, [x3, #0xb]
    // 0x8c801c: r1 = Function '<anonymous closure>': static.
    //     0x8c801c: add             x1, PP, #0x18, lsl #12  ; [pp+0x184b0] AnonymousClosure: static (0x8c8050), in [package:pointycastle/key_derivators/ecdh_kdf.dart] ECDHKeyDerivator::factoryConfig (0x8c7ff8)
    //     0x8c8020: ldr             x1, [x1, #0x4b0]
    // 0x8c8024: r2 = Null
    //     0x8c8024: mov             x2, NULL
    // 0x8c8028: r0 = AllocateClosure()
    //     0x8c8028: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c802c: mov             x1, x0
    // 0x8c8030: ldur            x0, [fp, #-8]
    // 0x8c8034: StoreField: r0->field_f = r1
    //     0x8c8034: stur            w1, [x0, #0xf]
    // 0x8c8038: r1 = KeyDerivator
    //     0x8c8038: add             x1, PP, #0x18, lsl #12  ; [pp+0x184b8] Type: KeyDerivator
    //     0x8c803c: ldr             x1, [x1, #0x4b8]
    // 0x8c8040: StoreField: r0->field_7 = r1
    //     0x8c8040: stur            w1, [x0, #7]
    // 0x8c8044: LeaveFrame
    //     0x8c8044: mov             SP, fp
    //     0x8c8048: ldp             fp, lr, [SP], #0x10
    // 0x8c804c: ret
    //     0x8c804c: ret             
  }
  [closure] static ECDHKeyDerivator <anonymous closure>(dynamic) {
    // ** addr: 0x8c8050, size: 0x18
    // 0x8c8050: EnterFrame
    //     0x8c8050: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8054: mov             fp, SP
    // 0x8c8058: r0 = ECDHKeyDerivator()
    //     0x8c8058: bl              #0x8c8068  ; AllocateECDHKeyDerivatorStub -> ECDHKeyDerivator (size=0x8)
    // 0x8c805c: LeaveFrame
    //     0x8c805c: mov             SP, fp
    //     0x8c8060: ldp             fp, lr, [SP], #0x10
    // 0x8c8064: ret
    //     0x8c8064: ret             
  }
}
