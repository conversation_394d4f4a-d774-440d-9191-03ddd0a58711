// lib: impl.key_derivator.hkdf, url: package:pointycastle/key_derivators/hkdf.dart

// class id: 1051009, size: 0x8
class :: {
}

// class id: 586, size: 0xc, field offset: 0x8
class HKDFKeyDerivator extends BaseKeyDerivator {

  static late final FactoryConfig factoryConfig; // offset: 0xe3c
  static late final Map<String, int> _digestBlockLength; // offset: 0xe40

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c8250, size: 0x64
    // 0x8c8250: EnterFrame
    //     0x8c8250: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8254: mov             fp, SP
    // 0x8c8258: AllocStack(0x8)
    //     0x8c8258: sub             SP, SP, #8
    // 0x8c825c: CheckStackOverflow
    //     0x8c825c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c8260: cmp             SP, x16
    //     0x8c8264: b.ls            #0x8c82ac
    // 0x8c8268: r0 = DynamicFactoryConfig()
    //     0x8c8268: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c826c: r1 = Function '<anonymous closure>': static.
    //     0x8c826c: add             x1, PP, #0x18, lsl #12  ; [pp+0x184e8] AnonymousClosure: static (0x8c82b4), in [package:pointycastle/key_derivators/hkdf.dart] HKDFKeyDerivator::factoryConfig (0x8c8250)
    //     0x8c8270: ldr             x1, [x1, #0x4e8]
    // 0x8c8274: r2 = Null
    //     0x8c8274: mov             x2, NULL
    // 0x8c8278: stur            x0, [fp, #-8]
    // 0x8c827c: r0 = AllocateClosure()
    //     0x8c827c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c8280: ldur            x1, [fp, #-8]
    // 0x8c8284: mov             x5, x0
    // 0x8c8288: r2 = KeyDerivator
    //     0x8c8288: add             x2, PP, #0x18, lsl #12  ; [pp+0x184b8] Type: KeyDerivator
    //     0x8c828c: ldr             x2, [x2, #0x4b8]
    // 0x8c8290: r3 = "/HKDF"
    //     0x8c8290: add             x3, PP, #0x18, lsl #12  ; [pp+0x184f0] "/HKDF"
    //     0x8c8294: ldr             x3, [x3, #0x4f0]
    // 0x8c8298: r0 = DynamicFactoryConfig.suffix()
    //     0x8c8298: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c829c: ldur            x0, [fp, #-8]
    // 0x8c82a0: LeaveFrame
    //     0x8c82a0: mov             SP, fp
    //     0x8c82a4: ldp             fp, lr, [SP], #0x10
    // 0x8c82a8: ret
    //     0x8c82a8: ret             
    // 0x8c82ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c82ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c82b0: b               #0x8c8268
  }
  [closure] static (dynamic) => HKDFKeyDerivator <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c82b4, size: 0xec
    // 0x8c82b4: EnterFrame
    //     0x8c82b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c82b8: mov             fp, SP
    // 0x8c82bc: AllocStack(0x28)
    //     0x8c82bc: sub             SP, SP, #0x28
    // 0x8c82c0: SetupParameters()
    //     0x8c82c0: ldr             x0, [fp, #0x20]
    //     0x8c82c4: ldur            w1, [x0, #0x17]
    //     0x8c82c8: add             x1, x1, HEAP, lsl #32
    //     0x8c82cc: stur            x1, [fp, #-8]
    // 0x8c82d0: CheckStackOverflow
    //     0x8c82d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c82d4: cmp             SP, x16
    //     0x8c82d8: b.ls            #0x8c8394
    // 0x8c82dc: r1 = 1
    //     0x8c82dc: movz            x1, #0x1
    // 0x8c82e0: r0 = AllocateContext()
    //     0x8c82e0: bl              #0xec126c  ; AllocateContextStub
    // 0x8c82e4: mov             x3, x0
    // 0x8c82e8: ldur            x0, [fp, #-8]
    // 0x8c82ec: stur            x3, [fp, #-0x10]
    // 0x8c82f0: StoreField: r3->field_b = r0
    //     0x8c82f0: stur            w0, [x3, #0xb]
    // 0x8c82f4: ldr             x1, [fp, #0x10]
    // 0x8c82f8: r0 = LoadClassIdInstr(r1)
    //     0x8c82f8: ldur            x0, [x1, #-1]
    //     0x8c82fc: ubfx            x0, x0, #0xc, #0x14
    // 0x8c8300: r2 = 1
    //     0x8c8300: movz            x2, #0x1
    // 0x8c8304: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c8304: sub             lr, x0, #0xfdd
    //     0x8c8308: ldr             lr, [x21, lr, lsl #3]
    //     0x8c830c: blr             lr
    // 0x8c8310: stur            x0, [fp, #-8]
    // 0x8c8314: cmp             w0, NULL
    // 0x8c8318: b.eq            #0x8c839c
    // 0x8c831c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c831c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8320: ldr             x0, [x0, #0x2e38]
    //     0x8c8324: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8328: cmp             w0, w16
    //     0x8c832c: b.ne            #0x8c833c
    //     0x8c8330: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c8334: ldr             x2, [x2, #0xf80]
    //     0x8c8338: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c833c: r16 = <Digest>
    //     0x8c833c: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b8] TypeArguments: <Digest>
    //     0x8c8340: ldr             x16, [x16, #0x1b8]
    // 0x8c8344: stp             x0, x16, [SP, #8]
    // 0x8c8348: ldur            x16, [fp, #-8]
    // 0x8c834c: str             x16, [SP]
    // 0x8c8350: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c8350: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c8354: r0 = create()
    //     0x8c8354: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c8358: ldur            x2, [fp, #-0x10]
    // 0x8c835c: StoreField: r2->field_f = r0
    //     0x8c835c: stur            w0, [x2, #0xf]
    //     0x8c8360: tbz             w0, #0, #0x8c837c
    //     0x8c8364: ldurb           w16, [x2, #-1]
    //     0x8c8368: ldurb           w17, [x0, #-1]
    //     0x8c836c: and             x16, x17, x16, lsr #2
    //     0x8c8370: tst             x16, HEAP, lsr #32
    //     0x8c8374: b.eq            #0x8c837c
    //     0x8c8378: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c837c: r1 = Function '<anonymous closure>': static.
    //     0x8c837c: add             x1, PP, #0x18, lsl #12  ; [pp+0x184f8] AnonymousClosure: static (0x8c83a0), in [package:pointycastle/key_derivators/hkdf.dart] HKDFKeyDerivator::factoryConfig (0x8c8250)
    //     0x8c8380: ldr             x1, [x1, #0x4f8]
    // 0x8c8384: r0 = AllocateClosure()
    //     0x8c8384: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c8388: LeaveFrame
    //     0x8c8388: mov             SP, fp
    //     0x8c838c: ldp             fp, lr, [SP], #0x10
    // 0x8c8390: ret
    //     0x8c8390: ret             
    // 0x8c8394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c8394: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c8398: b               #0x8c82dc
    // 0x8c839c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c839c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static HKDFKeyDerivator <anonymous closure>(dynamic) {
    // ** addr: 0x8c83a0, size: 0x5c
    // 0x8c83a0: EnterFrame
    //     0x8c83a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c83a4: mov             fp, SP
    // 0x8c83a8: AllocStack(0x8)
    //     0x8c83a8: sub             SP, SP, #8
    // 0x8c83ac: SetupParameters()
    //     0x8c83ac: ldr             x0, [fp, #0x10]
    //     0x8c83b0: ldur            w1, [x0, #0x17]
    //     0x8c83b4: add             x1, x1, HEAP, lsl #32
    // 0x8c83b8: CheckStackOverflow
    //     0x8c83b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c83bc: cmp             SP, x16
    //     0x8c83c0: b.ls            #0x8c83f4
    // 0x8c83c4: LoadField: r2 = r1->field_f
    //     0x8c83c4: ldur            w2, [x1, #0xf]
    // 0x8c83c8: DecompressPointer r2
    //     0x8c83c8: add             x2, x2, HEAP, lsl #32
    // 0x8c83cc: stur            x2, [fp, #-8]
    // 0x8c83d0: r0 = HKDFKeyDerivator()
    //     0x8c83d0: bl              #0x8c893c  ; AllocateHKDFKeyDerivatorStub -> HKDFKeyDerivator (size=0xc)
    // 0x8c83d4: mov             x1, x0
    // 0x8c83d8: ldur            x2, [fp, #-8]
    // 0x8c83dc: stur            x0, [fp, #-8]
    // 0x8c83e0: r0 = HKDFKeyDerivator()
    //     0x8c83e0: bl              #0x8c83fc  ; [package:pointycastle/key_derivators/hkdf.dart] HKDFKeyDerivator::HKDFKeyDerivator
    // 0x8c83e4: ldur            x0, [fp, #-8]
    // 0x8c83e8: LeaveFrame
    //     0x8c83e8: mov             SP, fp
    //     0x8c83ec: ldp             fp, lr, [SP], #0x10
    // 0x8c83f0: ret
    //     0x8c83f0: ret             
    // 0x8c83f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c83f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c83f8: b               #0x8c83c4
  }
  _ HKDFKeyDerivator(/* No info */) {
    // ** addr: 0x8c83fc, size: 0xcc
    // 0x8c83fc: EnterFrame
    //     0x8c83fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8400: mov             fp, SP
    // 0x8c8404: AllocStack(0x18)
    //     0x8c8404: sub             SP, SP, #0x18
    // 0x8c8408: r0 = Sentinel
    //     0x8c8408: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c840c: mov             x3, x1
    // 0x8c8410: stur            x1, [fp, #-8]
    // 0x8c8414: stur            x2, [fp, #-0x10]
    // 0x8c8418: CheckStackOverflow
    //     0x8c8418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c841c: cmp             SP, x16
    //     0x8c8420: b.ls            #0x8c84b4
    // 0x8c8424: StoreField: r3->field_7 = r0
    //     0x8c8424: stur            w0, [x3, #7]
    // 0x8c8428: r0 = LoadClassIdInstr(r2)
    //     0x8c8428: ldur            x0, [x2, #-1]
    //     0x8c842c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c8430: mov             x1, x2
    // 0x8c8434: r0 = GDT[cid_x0 + 0xf02f]()
    //     0x8c8434: movz            x17, #0xf02f
    //     0x8c8438: add             lr, x0, x17
    //     0x8c843c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c8440: blr             lr
    // 0x8c8444: mov             x1, x0
    // 0x8c8448: r0 = _getBlockLengthFromDigest()
    //     0x8c8448: bl              #0x8c85fc  ; [package:pointycastle/key_derivators/hkdf.dart] HKDFKeyDerivator::_getBlockLengthFromDigest
    // 0x8c844c: stur            x0, [fp, #-0x18]
    // 0x8c8450: r0 = HMac()
    //     0x8c8450: bl              #0x8c78e4  ; AllocateHMacStub -> HMac (size=0x14)
    // 0x8c8454: mov             x1, x0
    // 0x8c8458: ldur            x2, [fp, #-0x10]
    // 0x8c845c: ldur            x3, [fp, #-0x18]
    // 0x8c8460: stur            x0, [fp, #-0x10]
    // 0x8c8464: r0 = HMac()
    //     0x8c8464: bl              #0x8c84c8  ; [package:pointycastle/macs/hmac.dart] HMac::HMac
    // 0x8c8468: ldur            x0, [fp, #-0x10]
    // 0x8c846c: ldur            x1, [fp, #-8]
    // 0x8c8470: StoreField: r1->field_7 = r0
    //     0x8c8470: stur            w0, [x1, #7]
    //     0x8c8474: ldurb           w16, [x1, #-1]
    //     0x8c8478: ldurb           w17, [x0, #-1]
    //     0x8c847c: and             x16, x17, x16, lsr #2
    //     0x8c8480: tst             x16, HEAP, lsr #32
    //     0x8c8484: b.eq            #0x8c848c
    //     0x8c8488: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c848c: ldur            x1, [fp, #-0x10]
    // 0x8c8490: LoadField: r2 = r1->field_b
    //     0x8c8490: ldur            w2, [x1, #0xb]
    // 0x8c8494: DecompressPointer r2
    //     0x8c8494: add             x2, x2, HEAP, lsl #32
    // 0x8c8498: r16 = Sentinel
    //     0x8c8498: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c849c: cmp             w2, w16
    // 0x8c84a0: b.eq            #0x8c84bc
    // 0x8c84a4: r0 = Null
    //     0x8c84a4: mov             x0, NULL
    // 0x8c84a8: LeaveFrame
    //     0x8c84a8: mov             SP, fp
    //     0x8c84ac: ldp             fp, lr, [SP], #0x10
    // 0x8c84b0: ret
    //     0x8c84b0: ret             
    // 0x8c84b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c84b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c84b8: b               #0x8c8424
    // 0x8c84bc: r9 = _digestSize
    //     0x8c84bc: add             x9, PP, #0x18, lsl #12  ; [pp+0x18500] Field <HMac._digestSize@948404795>: late (offset: 0xc)
    //     0x8c84c0: ldr             x9, [x9, #0x500]
    // 0x8c84c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8c84c4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  static _ _getBlockLengthFromDigest(/* No info */) {
    // ** addr: 0x8c85fc, size: 0xd0
    // 0x8c85fc: EnterFrame
    //     0x8c85fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8600: mov             fp, SP
    // 0x8c8604: AllocStack(0x28)
    //     0x8c8604: sub             SP, SP, #0x28
    // 0x8c8608: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c8608: stur            x1, [fp, #-8]
    // 0x8c860c: CheckStackOverflow
    //     0x8c860c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c8610: cmp             SP, x16
    //     0x8c8614: b.ls            #0x8c86c0
    // 0x8c8618: r1 = 1
    //     0x8c8618: movz            x1, #0x1
    // 0x8c861c: r0 = AllocateContext()
    //     0x8c861c: bl              #0xec126c  ; AllocateContextStub
    // 0x8c8620: mov             x1, x0
    // 0x8c8624: ldur            x0, [fp, #-8]
    // 0x8c8628: stur            x1, [fp, #-0x10]
    // 0x8c862c: StoreField: r1->field_f = r0
    //     0x8c862c: stur            w0, [x1, #0xf]
    // 0x8c8630: r0 = InitLateStaticField(0xe40) // [package:pointycastle/key_derivators/hkdf.dart] HKDFKeyDerivator::_digestBlockLength
    //     0x8c8630: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8634: ldr             x0, [x0, #0x1c80]
    //     0x8c8638: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c863c: cmp             w0, w16
    //     0x8c8640: b.ne            #0x8c8650
    //     0x8c8644: add             x2, PP, #0x18, lsl #12  ; [pp+0x18508] Field <HKDFKeyDerivator._digestBlockLength@941270195>: static late final (offset: 0xe40)
    //     0x8c8648: ldr             x2, [x2, #0x508]
    //     0x8c864c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8650: mov             x1, x0
    // 0x8c8654: r0 = entries()
    //     0x8c8654: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0x8c8658: ldur            x2, [fp, #-0x10]
    // 0x8c865c: r1 = Function '<anonymous closure>': static.
    //     0x8c865c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18510] AnonymousClosure: static (0x8c86cc), in [package:pointycastle/key_derivators/hkdf.dart] HKDFKeyDerivator::_getBlockLengthFromDigest (0x8c85fc)
    //     0x8c8660: ldr             x1, [x1, #0x510]
    // 0x8c8664: stur            x0, [fp, #-8]
    // 0x8c8668: r0 = AllocateClosure()
    //     0x8c8668: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c866c: r16 = <MapEntry<String, int>>
    //     0x8c866c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18518] TypeArguments: <MapEntry<String, int>>
    //     0x8c8670: ldr             x16, [x16, #0x518]
    // 0x8c8674: ldur            lr, [fp, #-8]
    // 0x8c8678: stp             lr, x16, [SP, #8]
    // 0x8c867c: str             x0, [SP]
    // 0x8c8680: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c8680: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c8684: r0 = IterableExtension.firstWhereOrNull()
    //     0x8c8684: bl              #0x7379ec  ; [package:collection/src/iterable_extensions.dart] ::IterableExtension.firstWhereOrNull
    // 0x8c8688: cmp             w0, NULL
    // 0x8c868c: b.ne            #0x8c8698
    // 0x8c8690: r1 = Null
    //     0x8c8690: mov             x1, NULL
    // 0x8c8694: b               #0x8c86a0
    // 0x8c8698: LoadField: r1 = r0->field_f
    //     0x8c8698: ldur            w1, [x0, #0xf]
    // 0x8c869c: DecompressPointer r1
    //     0x8c869c: add             x1, x1, HEAP, lsl #32
    // 0x8c86a0: cmp             w1, NULL
    // 0x8c86a4: b.eq            #0x8c86c8
    // 0x8c86a8: r0 = LoadInt32Instr(r1)
    //     0x8c86a8: sbfx            x0, x1, #1, #0x1f
    //     0x8c86ac: tbz             w1, #0, #0x8c86b4
    //     0x8c86b0: ldur            x0, [x1, #7]
    // 0x8c86b4: LeaveFrame
    //     0x8c86b4: mov             SP, fp
    //     0x8c86b8: ldp             fp, lr, [SP], #0x10
    // 0x8c86bc: ret
    //     0x8c86bc: ret             
    // 0x8c86c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c86c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c86c4: b               #0x8c8618
    // 0x8c86c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c86c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static bool <anonymous closure>(dynamic, MapEntry<String, int>) {
    // ** addr: 0x8c86cc, size: 0xb0
    // 0x8c86cc: EnterFrame
    //     0x8c86cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c86d0: mov             fp, SP
    // 0x8c86d4: AllocStack(0x20)
    //     0x8c86d4: sub             SP, SP, #0x20
    // 0x8c86d8: SetupParameters()
    //     0x8c86d8: ldr             x0, [fp, #0x18]
    //     0x8c86dc: ldur            w1, [x0, #0x17]
    //     0x8c86e0: add             x1, x1, HEAP, lsl #32
    //     0x8c86e4: stur            x1, [fp, #-8]
    // 0x8c86e8: CheckStackOverflow
    //     0x8c86e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c86ec: cmp             SP, x16
    //     0x8c86f0: b.ls            #0x8c8774
    // 0x8c86f4: ldr             x0, [fp, #0x10]
    // 0x8c86f8: LoadField: r2 = r0->field_b
    //     0x8c86f8: ldur            w2, [x0, #0xb]
    // 0x8c86fc: DecompressPointer r2
    //     0x8c86fc: add             x2, x2, HEAP, lsl #32
    // 0x8c8700: r0 = LoadClassIdInstr(r2)
    //     0x8c8700: ldur            x0, [x2, #-1]
    //     0x8c8704: ubfx            x0, x0, #0xc, #0x14
    // 0x8c8708: str             x2, [SP]
    // 0x8c870c: r0 = GDT[cid_x0 + -0xffe]()
    //     0x8c870c: sub             lr, x0, #0xffe
    //     0x8c8710: ldr             lr, [x21, lr, lsl #3]
    //     0x8c8714: blr             lr
    // 0x8c8718: mov             x1, x0
    // 0x8c871c: ldur            x0, [fp, #-8]
    // 0x8c8720: stur            x1, [fp, #-0x10]
    // 0x8c8724: LoadField: r2 = r0->field_f
    //     0x8c8724: ldur            w2, [x0, #0xf]
    // 0x8c8728: DecompressPointer r2
    //     0x8c8728: add             x2, x2, HEAP, lsl #32
    // 0x8c872c: r0 = LoadClassIdInstr(r2)
    //     0x8c872c: ldur            x0, [x2, #-1]
    //     0x8c8730: ubfx            x0, x0, #0xc, #0x14
    // 0x8c8734: str             x2, [SP]
    // 0x8c8738: r0 = GDT[cid_x0 + -0xffe]()
    //     0x8c8738: sub             lr, x0, #0xffe
    //     0x8c873c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c8740: blr             lr
    // 0x8c8744: mov             x1, x0
    // 0x8c8748: ldur            x0, [fp, #-0x10]
    // 0x8c874c: r2 = LoadClassIdInstr(r0)
    //     0x8c874c: ldur            x2, [x0, #-1]
    //     0x8c8750: ubfx            x2, x2, #0xc, #0x14
    // 0x8c8754: stp             x1, x0, [SP]
    // 0x8c8758: mov             x0, x2
    // 0x8c875c: mov             lr, x0
    // 0x8c8760: ldr             lr, [x21, lr, lsl #3]
    // 0x8c8764: blr             lr
    // 0x8c8768: LeaveFrame
    //     0x8c8768: mov             SP, fp
    //     0x8c876c: ldp             fp, lr, [SP], #0x10
    // 0x8c8770: ret
    //     0x8c8770: ret             
    // 0x8c8774: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c8774: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c8778: b               #0x8c86f4
  }
  static Map<String, int> _digestBlockLength() {
    // ** addr: 0x8c877c, size: 0x1c0
    // 0x8c877c: EnterFrame
    //     0x8c877c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8780: mov             fp, SP
    // 0x8c8784: AllocStack(0x10)
    //     0x8c8784: sub             SP, SP, #0x10
    // 0x8c8788: CheckStackOverflow
    //     0x8c8788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c878c: cmp             SP, x16
    //     0x8c8790: b.ls            #0x8c8934
    // 0x8c8794: r1 = Null
    //     0x8c8794: mov             x1, NULL
    // 0x8c8798: r2 = 76
    //     0x8c8798: movz            x2, #0x4c
    // 0x8c879c: r0 = AllocateArray()
    //     0x8c879c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c87a0: r16 = "GOST3411"
    //     0x8c87a0: add             x16, PP, #0x18, lsl #12  ; [pp+0x18520] "GOST3411"
    //     0x8c87a4: ldr             x16, [x16, #0x520]
    // 0x8c87a8: StoreField: r0->field_f = r16
    //     0x8c87a8: stur            w16, [x0, #0xf]
    // 0x8c87ac: r16 = 64
    //     0x8c87ac: movz            x16, #0x40
    // 0x8c87b0: StoreField: r0->field_13 = r16
    //     0x8c87b0: stur            w16, [x0, #0x13]
    // 0x8c87b4: r16 = "MD2"
    //     0x8c87b4: add             x16, PP, #0x18, lsl #12  ; [pp+0x181c0] "MD2"
    //     0x8c87b8: ldr             x16, [x16, #0x1c0]
    // 0x8c87bc: ArrayStore: r0[0] = r16  ; List_4
    //     0x8c87bc: stur            w16, [x0, #0x17]
    // 0x8c87c0: r16 = 32
    //     0x8c87c0: movz            x16, #0x20
    // 0x8c87c4: StoreField: r0->field_1b = r16
    //     0x8c87c4: stur            w16, [x0, #0x1b]
    // 0x8c87c8: r16 = "MD4"
    //     0x8c87c8: add             x16, PP, #0x18, lsl #12  ; [pp+0x181d0] "MD4"
    //     0x8c87cc: ldr             x16, [x16, #0x1d0]
    // 0x8c87d0: StoreField: r0->field_1f = r16
    //     0x8c87d0: stur            w16, [x0, #0x1f]
    // 0x8c87d4: r16 = 128
    //     0x8c87d4: movz            x16, #0x80
    // 0x8c87d8: StoreField: r0->field_23 = r16
    //     0x8c87d8: stur            w16, [x0, #0x23]
    // 0x8c87dc: r16 = "MD5"
    //     0x8c87dc: add             x16, PP, #0x18, lsl #12  ; [pp+0x181e0] "MD5"
    //     0x8c87e0: ldr             x16, [x16, #0x1e0]
    // 0x8c87e4: StoreField: r0->field_27 = r16
    //     0x8c87e4: stur            w16, [x0, #0x27]
    // 0x8c87e8: r16 = 128
    //     0x8c87e8: movz            x16, #0x80
    // 0x8c87ec: StoreField: r0->field_2b = r16
    //     0x8c87ec: stur            w16, [x0, #0x2b]
    // 0x8c87f0: r16 = "RIPEMD-128"
    //     0x8c87f0: add             x16, PP, #0x18, lsl #12  ; [pp+0x181f0] "RIPEMD-128"
    //     0x8c87f4: ldr             x16, [x16, #0x1f0]
    // 0x8c87f8: StoreField: r0->field_2f = r16
    //     0x8c87f8: stur            w16, [x0, #0x2f]
    // 0x8c87fc: r16 = 128
    //     0x8c87fc: movz            x16, #0x80
    // 0x8c8800: StoreField: r0->field_33 = r16
    //     0x8c8800: stur            w16, [x0, #0x33]
    // 0x8c8804: r16 = "RIPEMD-160"
    //     0x8c8804: add             x16, PP, #0x18, lsl #12  ; [pp+0x18200] "RIPEMD-160"
    //     0x8c8808: ldr             x16, [x16, #0x200]
    // 0x8c880c: StoreField: r0->field_37 = r16
    //     0x8c880c: stur            w16, [x0, #0x37]
    // 0x8c8810: r16 = 128
    //     0x8c8810: movz            x16, #0x80
    // 0x8c8814: StoreField: r0->field_3b = r16
    //     0x8c8814: stur            w16, [x0, #0x3b]
    // 0x8c8818: r16 = "SHA-1"
    //     0x8c8818: add             x16, PP, #0x18, lsl #12  ; [pp+0x18220] "SHA-1"
    //     0x8c881c: ldr             x16, [x16, #0x220]
    // 0x8c8820: StoreField: r0->field_3f = r16
    //     0x8c8820: stur            w16, [x0, #0x3f]
    // 0x8c8824: r16 = 128
    //     0x8c8824: movz            x16, #0x80
    // 0x8c8828: StoreField: r0->field_43 = r16
    //     0x8c8828: stur            w16, [x0, #0x43]
    // 0x8c882c: r16 = "SHA-224"
    //     0x8c882c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18230] "SHA-224"
    //     0x8c8830: ldr             x16, [x16, #0x230]
    // 0x8c8834: StoreField: r0->field_47 = r16
    //     0x8c8834: stur            w16, [x0, #0x47]
    // 0x8c8838: r16 = 128
    //     0x8c8838: movz            x16, #0x80
    // 0x8c883c: StoreField: r0->field_4b = r16
    //     0x8c883c: stur            w16, [x0, #0x4b]
    // 0x8c8840: r16 = "SHA-256"
    //     0x8c8840: add             x16, PP, #0x18, lsl #12  ; [pp+0x18240] "SHA-256"
    //     0x8c8844: ldr             x16, [x16, #0x240]
    // 0x8c8848: StoreField: r0->field_4f = r16
    //     0x8c8848: stur            w16, [x0, #0x4f]
    // 0x8c884c: r16 = 128
    //     0x8c884c: movz            x16, #0x80
    // 0x8c8850: StoreField: r0->field_53 = r16
    //     0x8c8850: stur            w16, [x0, #0x53]
    // 0x8c8854: r16 = "SHA-384"
    //     0x8c8854: add             x16, PP, #0x18, lsl #12  ; [pp+0x18250] "SHA-384"
    //     0x8c8858: ldr             x16, [x16, #0x250]
    // 0x8c885c: StoreField: r0->field_57 = r16
    //     0x8c885c: stur            w16, [x0, #0x57]
    // 0x8c8860: r16 = 256
    //     0x8c8860: movz            x16, #0x100
    // 0x8c8864: StoreField: r0->field_5b = r16
    //     0x8c8864: stur            w16, [x0, #0x5b]
    // 0x8c8868: r16 = "SHA-512"
    //     0x8c8868: add             x16, PP, #0x18, lsl #12  ; [pp+0x18260] "SHA-512"
    //     0x8c886c: ldr             x16, [x16, #0x260]
    // 0x8c8870: StoreField: r0->field_5f = r16
    //     0x8c8870: stur            w16, [x0, #0x5f]
    // 0x8c8874: r16 = 256
    //     0x8c8874: movz            x16, #0x100
    // 0x8c8878: StoreField: r0->field_63 = r16
    //     0x8c8878: stur            w16, [x0, #0x63]
    // 0x8c887c: r16 = "SHA-512/224"
    //     0x8c887c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18528] "SHA-512/224"
    //     0x8c8880: ldr             x16, [x16, #0x528]
    // 0x8c8884: StoreField: r0->field_67 = r16
    //     0x8c8884: stur            w16, [x0, #0x67]
    // 0x8c8888: r16 = 256
    //     0x8c8888: movz            x16, #0x100
    // 0x8c888c: StoreField: r0->field_6b = r16
    //     0x8c888c: stur            w16, [x0, #0x6b]
    // 0x8c8890: r16 = "SHA-512/256"
    //     0x8c8890: add             x16, PP, #0x18, lsl #12  ; [pp+0x18530] "SHA-512/256"
    //     0x8c8894: ldr             x16, [x16, #0x530]
    // 0x8c8898: StoreField: r0->field_6f = r16
    //     0x8c8898: stur            w16, [x0, #0x6f]
    // 0x8c889c: r16 = 256
    //     0x8c889c: movz            x16, #0x100
    // 0x8c88a0: StoreField: r0->field_73 = r16
    //     0x8c88a0: stur            w16, [x0, #0x73]
    // 0x8c88a4: r16 = "SHA3-224"
    //     0x8c88a4: add             x16, PP, #0x18, lsl #12  ; [pp+0x18538] "SHA3-224"
    //     0x8c88a8: ldr             x16, [x16, #0x538]
    // 0x8c88ac: StoreField: r0->field_77 = r16
    //     0x8c88ac: stur            w16, [x0, #0x77]
    // 0x8c88b0: r16 = 288
    //     0x8c88b0: movz            x16, #0x120
    // 0x8c88b4: StoreField: r0->field_7b = r16
    //     0x8c88b4: stur            w16, [x0, #0x7b]
    // 0x8c88b8: r16 = "SHA3-256"
    //     0x8c88b8: add             x16, PP, #0x18, lsl #12  ; [pp+0x18540] "SHA3-256"
    //     0x8c88bc: ldr             x16, [x16, #0x540]
    // 0x8c88c0: StoreField: r0->field_7f = r16
    //     0x8c88c0: stur            w16, [x0, #0x7f]
    // 0x8c88c4: r16 = 272
    //     0x8c88c4: movz            x16, #0x110
    // 0x8c88c8: StoreField: r0->field_83 = r16
    //     0x8c88c8: stur            w16, [x0, #0x83]
    // 0x8c88cc: r16 = "SHA3-384"
    //     0x8c88cc: add             x16, PP, #0x18, lsl #12  ; [pp+0x18548] "SHA3-384"
    //     0x8c88d0: ldr             x16, [x16, #0x548]
    // 0x8c88d4: StoreField: r0->field_87 = r16
    //     0x8c88d4: stur            w16, [x0, #0x87]
    // 0x8c88d8: r16 = 208
    //     0x8c88d8: movz            x16, #0xd0
    // 0x8c88dc: StoreField: r0->field_8b = r16
    //     0x8c88dc: stur            w16, [x0, #0x8b]
    // 0x8c88e0: r16 = "SHA3-512"
    //     0x8c88e0: add             x16, PP, #0x18, lsl #12  ; [pp+0x18550] "SHA3-512"
    //     0x8c88e4: ldr             x16, [x16, #0x550]
    // 0x8c88e8: StoreField: r0->field_8f = r16
    //     0x8c88e8: stur            w16, [x0, #0x8f]
    // 0x8c88ec: r16 = 144
    //     0x8c88ec: movz            x16, #0x90
    // 0x8c88f0: StoreField: r0->field_93 = r16
    //     0x8c88f0: stur            w16, [x0, #0x93]
    // 0x8c88f4: r16 = "Tiger"
    //     0x8c88f4: add             x16, PP, #0x18, lsl #12  ; [pp+0x18558] "Tiger"
    //     0x8c88f8: ldr             x16, [x16, #0x558]
    // 0x8c88fc: StoreField: r0->field_97 = r16
    //     0x8c88fc: stur            w16, [x0, #0x97]
    // 0x8c8900: r16 = 128
    //     0x8c8900: movz            x16, #0x80
    // 0x8c8904: StoreField: r0->field_9b = r16
    //     0x8c8904: stur            w16, [x0, #0x9b]
    // 0x8c8908: r16 = "Whirlpool"
    //     0x8c8908: add             x16, PP, #0x18, lsl #12  ; [pp+0x18560] "Whirlpool"
    //     0x8c890c: ldr             x16, [x16, #0x560]
    // 0x8c8910: StoreField: r0->field_9f = r16
    //     0x8c8910: stur            w16, [x0, #0x9f]
    // 0x8c8914: r16 = 128
    //     0x8c8914: movz            x16, #0x80
    // 0x8c8918: StoreField: r0->field_a3 = r16
    //     0x8c8918: stur            w16, [x0, #0xa3]
    // 0x8c891c: r16 = <String, int>
    //     0x8c891c: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0x8c8920: stp             x0, x16, [SP]
    // 0x8c8924: r0 = Map._fromLiteral()
    //     0x8c8924: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8c8928: LeaveFrame
    //     0x8c8928: mov             SP, fp
    //     0x8c892c: ldp             fp, lr, [SP], #0x10
    // 0x8c8930: ret
    //     0x8c8930: ret             
    // 0x8c8934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c8934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c8938: b               #0x8c8794
  }
}
