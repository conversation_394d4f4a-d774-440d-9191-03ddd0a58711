// lib: impl.key_derivator.pbkdf2, url: package:pointycastle/key_derivators/pbkdf2.dart

// class id: 1051010, size: 0x8
class :: {
}

// class id: 585, size: 0xc, field offset: 0x8
class PBKDF2KeyDerivator extends BaseKeyDerivator {

  static late final FactoryConfig factoryConfig; // offset: 0xe44

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c8a88, size: 0x64
    // 0x8c8a88: EnterFrame
    //     0x8c8a88: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8a8c: mov             fp, SP
    // 0x8c8a90: AllocStack(0x8)
    //     0x8c8a90: sub             SP, SP, #8
    // 0x8c8a94: CheckStackOverflow
    //     0x8c8a94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c8a98: cmp             SP, x16
    //     0x8c8a9c: b.ls            #0x8c8ae4
    // 0x8c8aa0: r0 = DynamicFactoryConfig()
    //     0x8c8aa0: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c8aa4: r1 = Function '<anonymous closure>': static.
    //     0x8c8aa4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18578] AnonymousClosure: static (0x8c8aec), in [package:pointycastle/key_derivators/pbkdf2.dart] PBKDF2KeyDerivator::factoryConfig (0x8c8a88)
    //     0x8c8aa8: ldr             x1, [x1, #0x578]
    // 0x8c8aac: r2 = Null
    //     0x8c8aac: mov             x2, NULL
    // 0x8c8ab0: stur            x0, [fp, #-8]
    // 0x8c8ab4: r0 = AllocateClosure()
    //     0x8c8ab4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c8ab8: ldur            x1, [fp, #-8]
    // 0x8c8abc: mov             x5, x0
    // 0x8c8ac0: r2 = KeyDerivator
    //     0x8c8ac0: add             x2, PP, #0x18, lsl #12  ; [pp+0x184b8] Type: KeyDerivator
    //     0x8c8ac4: ldr             x2, [x2, #0x4b8]
    // 0x8c8ac8: r3 = "/PBKDF2"
    //     0x8c8ac8: add             x3, PP, #0x18, lsl #12  ; [pp+0x18580] "/PBKDF2"
    //     0x8c8acc: ldr             x3, [x3, #0x580]
    // 0x8c8ad0: r0 = DynamicFactoryConfig.suffix()
    //     0x8c8ad0: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c8ad4: ldur            x0, [fp, #-8]
    // 0x8c8ad8: LeaveFrame
    //     0x8c8ad8: mov             SP, fp
    //     0x8c8adc: ldp             fp, lr, [SP], #0x10
    // 0x8c8ae0: ret
    //     0x8c8ae0: ret             
    // 0x8c8ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c8ae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c8ae8: b               #0x8c8aa0
  }
  [closure] static (dynamic) => PBKDF2KeyDerivator <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c8aec, size: 0x54
    // 0x8c8aec: EnterFrame
    //     0x8c8aec: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8af0: mov             fp, SP
    // 0x8c8af4: AllocStack(0x8)
    //     0x8c8af4: sub             SP, SP, #8
    // 0x8c8af8: SetupParameters()
    //     0x8c8af8: ldr             x0, [fp, #0x20]
    //     0x8c8afc: ldur            w1, [x0, #0x17]
    //     0x8c8b00: add             x1, x1, HEAP, lsl #32
    //     0x8c8b04: stur            x1, [fp, #-8]
    // 0x8c8b08: r1 = 1
    //     0x8c8b08: movz            x1, #0x1
    // 0x8c8b0c: r0 = AllocateContext()
    //     0x8c8b0c: bl              #0xec126c  ; AllocateContextStub
    // 0x8c8b10: mov             x1, x0
    // 0x8c8b14: ldur            x0, [fp, #-8]
    // 0x8c8b18: StoreField: r1->field_b = r0
    //     0x8c8b18: stur            w0, [x1, #0xb]
    // 0x8c8b1c: ldr             x0, [fp, #0x10]
    // 0x8c8b20: StoreField: r1->field_f = r0
    //     0x8c8b20: stur            w0, [x1, #0xf]
    // 0x8c8b24: mov             x2, x1
    // 0x8c8b28: r1 = Function '<anonymous closure>': static.
    //     0x8c8b28: add             x1, PP, #0x18, lsl #12  ; [pp+0x18588] AnonymousClosure: static (0x8c8b40), in [package:pointycastle/key_derivators/pbkdf2.dart] PBKDF2KeyDerivator::factoryConfig (0x8c8a88)
    //     0x8c8b2c: ldr             x1, [x1, #0x588]
    // 0x8c8b30: r0 = AllocateClosure()
    //     0x8c8b30: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c8b34: LeaveFrame
    //     0x8c8b34: mov             SP, fp
    //     0x8c8b38: ldp             fp, lr, [SP], #0x10
    // 0x8c8b3c: ret
    //     0x8c8b3c: ret             
  }
  [closure] static PBKDF2KeyDerivator <anonymous closure>(dynamic) {
    // ** addr: 0x8c8b40, size: 0x100
    // 0x8c8b40: EnterFrame
    //     0x8c8b40: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8b44: mov             fp, SP
    // 0x8c8b48: AllocStack(0x28)
    //     0x8c8b48: sub             SP, SP, #0x28
    // 0x8c8b4c: SetupParameters()
    //     0x8c8b4c: ldr             x0, [fp, #0x10]
    //     0x8c8b50: ldur            w1, [x0, #0x17]
    //     0x8c8b54: add             x1, x1, HEAP, lsl #32
    // 0x8c8b58: CheckStackOverflow
    //     0x8c8b58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c8b5c: cmp             SP, x16
    //     0x8c8b60: b.ls            #0x8c8c34
    // 0x8c8b64: LoadField: r0 = r1->field_f
    //     0x8c8b64: ldur            w0, [x1, #0xf]
    // 0x8c8b68: DecompressPointer r0
    //     0x8c8b68: add             x0, x0, HEAP, lsl #32
    // 0x8c8b6c: r1 = LoadClassIdInstr(r0)
    //     0x8c8b6c: ldur            x1, [x0, #-1]
    //     0x8c8b70: ubfx            x1, x1, #0xc, #0x14
    // 0x8c8b74: mov             x16, x0
    // 0x8c8b78: mov             x0, x1
    // 0x8c8b7c: mov             x1, x16
    // 0x8c8b80: r2 = 1
    //     0x8c8b80: movz            x2, #0x1
    // 0x8c8b84: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c8b84: sub             lr, x0, #0xfdd
    //     0x8c8b88: ldr             lr, [x21, lr, lsl #3]
    //     0x8c8b8c: blr             lr
    // 0x8c8b90: stur            x0, [fp, #-8]
    // 0x8c8b94: cmp             w0, NULL
    // 0x8c8b98: b.eq            #0x8c8c3c
    // 0x8c8b9c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c8b9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8ba0: ldr             x0, [x0, #0x2e38]
    //     0x8c8ba4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8ba8: cmp             w0, w16
    //     0x8c8bac: b.ne            #0x8c8bbc
    //     0x8c8bb0: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c8bb4: ldr             x2, [x2, #0xf80]
    //     0x8c8bb8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8bbc: r16 = <Mac>
    //     0x8c8bbc: add             x16, PP, #0x18, lsl #12  ; [pp+0x182a8] TypeArguments: <Mac>
    //     0x8c8bc0: ldr             x16, [x16, #0x2a8]
    // 0x8c8bc4: stp             x0, x16, [SP, #8]
    // 0x8c8bc8: ldur            x16, [fp, #-8]
    // 0x8c8bcc: str             x16, [SP]
    // 0x8c8bd0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c8bd0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c8bd4: r0 = create()
    //     0x8c8bd4: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c8bd8: stur            x0, [fp, #-8]
    // 0x8c8bdc: r0 = PBKDF2KeyDerivator()
    //     0x8c8bdc: bl              #0x8c8c40  ; AllocatePBKDF2KeyDerivatorStub -> PBKDF2KeyDerivator (size=0xc)
    // 0x8c8be0: mov             x2, x0
    // 0x8c8be4: ldur            x1, [fp, #-8]
    // 0x8c8be8: stur            x2, [fp, #-0x10]
    // 0x8c8bec: StoreField: r2->field_7 = r1
    //     0x8c8bec: stur            w1, [x2, #7]
    // 0x8c8bf0: r0 = LoadClassIdInstr(r1)
    //     0x8c8bf0: ldur            x0, [x1, #-1]
    //     0x8c8bf4: ubfx            x0, x0, #0xc, #0x14
    // 0x8c8bf8: r0 = GDT[cid_x0 + -0xfd6]()
    //     0x8c8bf8: sub             lr, x0, #0xfd6
    //     0x8c8bfc: ldr             lr, [x21, lr, lsl #3]
    //     0x8c8c00: blr             lr
    // 0x8c8c04: mov             x2, x0
    // 0x8c8c08: r0 = BoxInt64Instr(r2)
    //     0x8c8c08: sbfiz           x0, x2, #1, #0x1f
    //     0x8c8c0c: cmp             x2, x0, asr #1
    //     0x8c8c10: b.eq            #0x8c8c1c
    //     0x8c8c14: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c8c18: stur            x2, [x0, #7]
    // 0x8c8c1c: mov             x4, x0
    // 0x8c8c20: r0 = AllocateUint8Array()
    //     0x8c8c20: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c8c24: ldur            x0, [fp, #-0x10]
    // 0x8c8c28: LeaveFrame
    //     0x8c8c28: mov             SP, fp
    //     0x8c8c2c: ldp             fp, lr, [SP], #0x10
    // 0x8c8c30: ret
    //     0x8c8c30: ret             
    // 0x8c8c34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c8c34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c8c38: b               #0x8c8b64
    // 0x8c8c3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c8c3c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
