// lib: , url: package:pointycastle/key_derivators/pkcs12_parameter_generator.dart

// class id: 1051011, size: 0x8
class :: {
}

// class id: 582, size: 0xc, field offset: 0x8
class PKCS12ParametersGenerator extends Object
    implements PBEParametersGenerator {

  static late final FactoryConfig factoryConfig; // offset: 0xe4c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c7af4, size: 0x64
    // 0x8c7af4: EnterFrame
    //     0x8c7af4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7af8: mov             fp, SP
    // 0x8c7afc: AllocStack(0x8)
    //     0x8c7afc: sub             SP, SP, #8
    // 0x8c7b00: CheckStackOverflow
    //     0x8c7b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7b04: cmp             SP, x16
    //     0x8c7b08: b.ls            #0x8c7b50
    // 0x8c7b0c: r0 = DynamicFactoryConfig()
    //     0x8c7b0c: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c7b10: r1 = Function '<anonymous closure>': static.
    //     0x8c7b10: add             x1, PP, #0x18, lsl #12  ; [pp+0x18428] AnonymousClosure: static (0x8c7b58), in [package:pointycastle/key_derivators/pkcs12_parameter_generator.dart] PKCS12ParametersGenerator::factoryConfig (0x8c7af4)
    //     0x8c7b14: ldr             x1, [x1, #0x428]
    // 0x8c7b18: r2 = Null
    //     0x8c7b18: mov             x2, NULL
    // 0x8c7b1c: stur            x0, [fp, #-8]
    // 0x8c7b20: r0 = AllocateClosure()
    //     0x8c7b20: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c7b24: ldur            x1, [fp, #-8]
    // 0x8c7b28: mov             x5, x0
    // 0x8c7b2c: r2 = PBEParametersGenerator
    //     0x8c7b2c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18410] Type: PBEParametersGenerator
    //     0x8c7b30: ldr             x2, [x2, #0x410]
    // 0x8c7b34: r3 = "/PKCS12"
    //     0x8c7b34: add             x3, PP, #0x18, lsl #12  ; [pp+0x18430] "/PKCS12"
    //     0x8c7b38: ldr             x3, [x3, #0x430]
    // 0x8c7b3c: r0 = DynamicFactoryConfig.suffix()
    //     0x8c7b3c: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c7b40: ldur            x0, [fp, #-8]
    // 0x8c7b44: LeaveFrame
    //     0x8c7b44: mov             SP, fp
    //     0x8c7b48: ldp             fp, lr, [SP], #0x10
    // 0x8c7b4c: ret
    //     0x8c7b4c: ret             
    // 0x8c7b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7b54: b               #0x8c7b0c
  }
  [closure] static (dynamic) => PKCS12ParametersGenerator <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c7b58, size: 0x54
    // 0x8c7b58: EnterFrame
    //     0x8c7b58: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7b5c: mov             fp, SP
    // 0x8c7b60: AllocStack(0x8)
    //     0x8c7b60: sub             SP, SP, #8
    // 0x8c7b64: SetupParameters()
    //     0x8c7b64: ldr             x0, [fp, #0x20]
    //     0x8c7b68: ldur            w1, [x0, #0x17]
    //     0x8c7b6c: add             x1, x1, HEAP, lsl #32
    //     0x8c7b70: stur            x1, [fp, #-8]
    // 0x8c7b74: r1 = 1
    //     0x8c7b74: movz            x1, #0x1
    // 0x8c7b78: r0 = AllocateContext()
    //     0x8c7b78: bl              #0xec126c  ; AllocateContextStub
    // 0x8c7b7c: mov             x1, x0
    // 0x8c7b80: ldur            x0, [fp, #-8]
    // 0x8c7b84: StoreField: r1->field_b = r0
    //     0x8c7b84: stur            w0, [x1, #0xb]
    // 0x8c7b88: ldr             x0, [fp, #0x10]
    // 0x8c7b8c: StoreField: r1->field_f = r0
    //     0x8c7b8c: stur            w0, [x1, #0xf]
    // 0x8c7b90: mov             x2, x1
    // 0x8c7b94: r1 = Function '<anonymous closure>': static.
    //     0x8c7b94: add             x1, PP, #0x18, lsl #12  ; [pp+0x18438] AnonymousClosure: static (0x8c7bac), in [package:pointycastle/key_derivators/pkcs12_parameter_generator.dart] PKCS12ParametersGenerator::factoryConfig (0x8c7af4)
    //     0x8c7b98: ldr             x1, [x1, #0x438]
    // 0x8c7b9c: r0 = AllocateClosure()
    //     0x8c7b9c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c7ba0: LeaveFrame
    //     0x8c7ba0: mov             SP, fp
    //     0x8c7ba4: ldp             fp, lr, [SP], #0x10
    // 0x8c7ba8: ret
    //     0x8c7ba8: ret             
  }
  [closure] static PKCS12ParametersGenerator <anonymous closure>(dynamic) {
    // ** addr: 0x8c7bac, size: 0xcc
    // 0x8c7bac: EnterFrame
    //     0x8c7bac: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7bb0: mov             fp, SP
    // 0x8c7bb4: AllocStack(0x20)
    //     0x8c7bb4: sub             SP, SP, #0x20
    // 0x8c7bb8: SetupParameters()
    //     0x8c7bb8: ldr             x0, [fp, #0x10]
    //     0x8c7bbc: ldur            w1, [x0, #0x17]
    //     0x8c7bc0: add             x1, x1, HEAP, lsl #32
    // 0x8c7bc4: CheckStackOverflow
    //     0x8c7bc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7bc8: cmp             SP, x16
    //     0x8c7bcc: b.ls            #0x8c7c6c
    // 0x8c7bd0: LoadField: r0 = r1->field_f
    //     0x8c7bd0: ldur            w0, [x1, #0xf]
    // 0x8c7bd4: DecompressPointer r0
    //     0x8c7bd4: add             x0, x0, HEAP, lsl #32
    // 0x8c7bd8: r1 = LoadClassIdInstr(r0)
    //     0x8c7bd8: ldur            x1, [x0, #-1]
    //     0x8c7bdc: ubfx            x1, x1, #0xc, #0x14
    // 0x8c7be0: mov             x16, x0
    // 0x8c7be4: mov             x0, x1
    // 0x8c7be8: mov             x1, x16
    // 0x8c7bec: r2 = 1
    //     0x8c7bec: movz            x2, #0x1
    // 0x8c7bf0: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c7bf0: sub             lr, x0, #0xfdd
    //     0x8c7bf4: ldr             lr, [x21, lr, lsl #3]
    //     0x8c7bf8: blr             lr
    // 0x8c7bfc: stur            x0, [fp, #-8]
    // 0x8c7c00: cmp             w0, NULL
    // 0x8c7c04: b.eq            #0x8c7c74
    // 0x8c7c08: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c7c08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7c0c: ldr             x0, [x0, #0x2e38]
    //     0x8c7c10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7c14: cmp             w0, w16
    //     0x8c7c18: b.ne            #0x8c7c28
    //     0x8c7c1c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c7c20: ldr             x2, [x2, #0xf80]
    //     0x8c7c24: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7c28: r16 = <Digest>
    //     0x8c7c28: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b8] TypeArguments: <Digest>
    //     0x8c7c2c: ldr             x16, [x16, #0x1b8]
    // 0x8c7c30: stp             x0, x16, [SP, #8]
    // 0x8c7c34: ldur            x16, [fp, #-8]
    // 0x8c7c38: str             x16, [SP]
    // 0x8c7c3c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c7c3c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c7c40: r0 = create()
    //     0x8c7c40: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c7c44: stur            x0, [fp, #-8]
    // 0x8c7c48: r0 = PKCS12ParametersGenerator()
    //     0x8c7c48: bl              #0x8c7d10  ; AllocatePKCS12ParametersGeneratorStub -> PKCS12ParametersGenerator (size=0xc)
    // 0x8c7c4c: mov             x1, x0
    // 0x8c7c50: ldur            x2, [fp, #-8]
    // 0x8c7c54: stur            x0, [fp, #-8]
    // 0x8c7c58: r0 = PKCS12ParametersGenerator()
    //     0x8c7c58: bl              #0x8c7c78  ; [package:pointycastle/key_derivators/pkcs12_parameter_generator.dart] PKCS12ParametersGenerator::PKCS12ParametersGenerator
    // 0x8c7c5c: ldur            x0, [fp, #-8]
    // 0x8c7c60: LeaveFrame
    //     0x8c7c60: mov             SP, fp
    //     0x8c7c64: ldp             fp, lr, [SP], #0x10
    // 0x8c7c68: ret
    //     0x8c7c68: ret             
    // 0x8c7c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7c6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7c70: b               #0x8c7bd0
    // 0x8c7c74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c7c74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ PKCS12ParametersGenerator(/* No info */) {
    // ** addr: 0x8c7c78, size: 0x98
    // 0x8c7c78: EnterFrame
    //     0x8c7c78: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7c7c: mov             fp, SP
    // 0x8c7c80: AllocStack(0x8)
    //     0x8c7c80: sub             SP, SP, #8
    // 0x8c7c84: SetupParameters(PKCS12ParametersGenerator this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0x8c7c84: stur            x1, [fp, #-8]
    //     0x8c7c88: mov             x16, x2
    //     0x8c7c8c: mov             x2, x1
    //     0x8c7c90: mov             x1, x16
    // 0x8c7c94: CheckStackOverflow
    //     0x8c7c94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7c98: cmp             SP, x16
    //     0x8c7c9c: b.ls            #0x8c7d08
    // 0x8c7ca0: mov             x0, x1
    // 0x8c7ca4: StoreField: r2->field_7 = r0
    //     0x8c7ca4: stur            w0, [x2, #7]
    //     0x8c7ca8: ldurb           w16, [x2, #-1]
    //     0x8c7cac: ldurb           w17, [x0, #-1]
    //     0x8c7cb0: and             x16, x17, x16, lsr #2
    //     0x8c7cb4: tst             x16, HEAP, lsr #32
    //     0x8c7cb8: b.eq            #0x8c7cc0
    //     0x8c7cbc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c7cc0: r0 = LoadClassIdInstr(r1)
    //     0x8c7cc0: ldur            x0, [x1, #-1]
    //     0x8c7cc4: ubfx            x0, x0, #0xc, #0x14
    // 0x8c7cc8: r0 = GDT[cid_x0 + -0xdc0]()
    //     0x8c7cc8: sub             lr, x0, #0xdc0
    //     0x8c7ccc: ldr             lr, [x21, lr, lsl #3]
    //     0x8c7cd0: blr             lr
    // 0x8c7cd4: ldur            x0, [fp, #-8]
    // 0x8c7cd8: LoadField: r1 = r0->field_7
    //     0x8c7cd8: ldur            w1, [x0, #7]
    // 0x8c7cdc: DecompressPointer r1
    //     0x8c7cdc: add             x1, x1, HEAP, lsl #32
    // 0x8c7ce0: r0 = LoadClassIdInstr(r1)
    //     0x8c7ce0: ldur            x0, [x1, #-1]
    //     0x8c7ce4: ubfx            x0, x0, #0xc, #0x14
    // 0x8c7ce8: r0 = GDT[cid_x0 + 0xf047]()
    //     0x8c7ce8: movz            x17, #0xf047
    //     0x8c7cec: add             lr, x0, x17
    //     0x8c7cf0: ldr             lr, [x21, lr, lsl #3]
    //     0x8c7cf4: blr             lr
    // 0x8c7cf8: r0 = Null
    //     0x8c7cf8: mov             x0, NULL
    // 0x8c7cfc: LeaveFrame
    //     0x8c7cfc: mov             SP, fp
    //     0x8c7d00: ldp             fp, lr, [SP], #0x10
    // 0x8c7d04: ret
    //     0x8c7d04: ret             
    // 0x8c7d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7d08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7d0c: b               #0x8c7ca0
  }
}
