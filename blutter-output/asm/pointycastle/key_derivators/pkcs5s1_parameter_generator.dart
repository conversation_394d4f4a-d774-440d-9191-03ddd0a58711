// lib: , url: package:pointycastle/key_derivators/pkcs5s1_parameter_generator.dart

// class id: 1051012, size: 0x8
class :: {
}

// class id: 581, size: 0x8, field offset: 0x8
class PKCS5S1ParameterGenerator extends Object
    implements PBEParametersGenerator {

  static late final FactoryConfig factoryConfig; // offset: 0xe50

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c797c, size: 0x64
    // 0x8c797c: EnterFrame
    //     0x8c797c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7980: mov             fp, SP
    // 0x8c7984: AllocStack(0x8)
    //     0x8c7984: sub             SP, SP, #8
    // 0x8c7988: CheckStackOverflow
    //     0x8c7988: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c798c: cmp             SP, x16
    //     0x8c7990: b.ls            #0x8c79d8
    // 0x8c7994: r0 = DynamicFactoryConfig()
    //     0x8c7994: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c7998: r1 = Function '<anonymous closure>': static.
    //     0x8c7998: add             x1, PP, #0x18, lsl #12  ; [pp+0x18408] AnonymousClosure: static (0x8c79e0), in [package:pointycastle/key_derivators/pkcs5s1_parameter_generator.dart] PKCS5S1ParameterGenerator::factoryConfig (0x8c797c)
    //     0x8c799c: ldr             x1, [x1, #0x408]
    // 0x8c79a0: r2 = Null
    //     0x8c79a0: mov             x2, NULL
    // 0x8c79a4: stur            x0, [fp, #-8]
    // 0x8c79a8: r0 = AllocateClosure()
    //     0x8c79a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c79ac: ldur            x1, [fp, #-8]
    // 0x8c79b0: mov             x5, x0
    // 0x8c79b4: r2 = PBEParametersGenerator
    //     0x8c79b4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18410] Type: PBEParametersGenerator
    //     0x8c79b8: ldr             x2, [x2, #0x410]
    // 0x8c79bc: r3 = "/PKCS5S1"
    //     0x8c79bc: add             x3, PP, #0x18, lsl #12  ; [pp+0x18418] "/PKCS5S1"
    //     0x8c79c0: ldr             x3, [x3, #0x418]
    // 0x8c79c4: r0 = DynamicFactoryConfig.suffix()
    //     0x8c79c4: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c79c8: ldur            x0, [fp, #-8]
    // 0x8c79cc: LeaveFrame
    //     0x8c79cc: mov             SP, fp
    //     0x8c79d0: ldp             fp, lr, [SP], #0x10
    // 0x8c79d4: ret
    //     0x8c79d4: ret             
    // 0x8c79d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c79d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c79dc: b               #0x8c7994
  }
  [closure] static (dynamic) => PKCS5S1ParameterGenerator <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c79e0, size: 0x54
    // 0x8c79e0: EnterFrame
    //     0x8c79e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c79e4: mov             fp, SP
    // 0x8c79e8: AllocStack(0x8)
    //     0x8c79e8: sub             SP, SP, #8
    // 0x8c79ec: SetupParameters()
    //     0x8c79ec: ldr             x0, [fp, #0x20]
    //     0x8c79f0: ldur            w1, [x0, #0x17]
    //     0x8c79f4: add             x1, x1, HEAP, lsl #32
    //     0x8c79f8: stur            x1, [fp, #-8]
    // 0x8c79fc: r1 = 1
    //     0x8c79fc: movz            x1, #0x1
    // 0x8c7a00: r0 = AllocateContext()
    //     0x8c7a00: bl              #0xec126c  ; AllocateContextStub
    // 0x8c7a04: mov             x1, x0
    // 0x8c7a08: ldur            x0, [fp, #-8]
    // 0x8c7a0c: StoreField: r1->field_b = r0
    //     0x8c7a0c: stur            w0, [x1, #0xb]
    // 0x8c7a10: ldr             x0, [fp, #0x10]
    // 0x8c7a14: StoreField: r1->field_f = r0
    //     0x8c7a14: stur            w0, [x1, #0xf]
    // 0x8c7a18: mov             x2, x1
    // 0x8c7a1c: r1 = Function '<anonymous closure>': static.
    //     0x8c7a1c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18420] AnonymousClosure: static (0x8c7a34), in [package:pointycastle/key_derivators/pkcs5s1_parameter_generator.dart] PKCS5S1ParameterGenerator::factoryConfig (0x8c797c)
    //     0x8c7a20: ldr             x1, [x1, #0x420]
    // 0x8c7a24: r0 = AllocateClosure()
    //     0x8c7a24: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c7a28: LeaveFrame
    //     0x8c7a28: mov             SP, fp
    //     0x8c7a2c: ldp             fp, lr, [SP], #0x10
    // 0x8c7a30: ret
    //     0x8c7a30: ret             
  }
  [closure] static PKCS5S1ParameterGenerator <anonymous closure>(dynamic) {
    // ** addr: 0x8c7a34, size: 0xb4
    // 0x8c7a34: EnterFrame
    //     0x8c7a34: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7a38: mov             fp, SP
    // 0x8c7a3c: AllocStack(0x20)
    //     0x8c7a3c: sub             SP, SP, #0x20
    // 0x8c7a40: SetupParameters()
    //     0x8c7a40: ldr             x0, [fp, #0x10]
    //     0x8c7a44: ldur            w1, [x0, #0x17]
    //     0x8c7a48: add             x1, x1, HEAP, lsl #32
    // 0x8c7a4c: CheckStackOverflow
    //     0x8c7a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7a50: cmp             SP, x16
    //     0x8c7a54: b.ls            #0x8c7adc
    // 0x8c7a58: LoadField: r0 = r1->field_f
    //     0x8c7a58: ldur            w0, [x1, #0xf]
    // 0x8c7a5c: DecompressPointer r0
    //     0x8c7a5c: add             x0, x0, HEAP, lsl #32
    // 0x8c7a60: r1 = LoadClassIdInstr(r0)
    //     0x8c7a60: ldur            x1, [x0, #-1]
    //     0x8c7a64: ubfx            x1, x1, #0xc, #0x14
    // 0x8c7a68: mov             x16, x0
    // 0x8c7a6c: mov             x0, x1
    // 0x8c7a70: mov             x1, x16
    // 0x8c7a74: r2 = 1
    //     0x8c7a74: movz            x2, #0x1
    // 0x8c7a78: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c7a78: sub             lr, x0, #0xfdd
    //     0x8c7a7c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c7a80: blr             lr
    // 0x8c7a84: stur            x0, [fp, #-8]
    // 0x8c7a88: cmp             w0, NULL
    // 0x8c7a8c: b.eq            #0x8c7ae4
    // 0x8c7a90: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c7a90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7a94: ldr             x0, [x0, #0x2e38]
    //     0x8c7a98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7a9c: cmp             w0, w16
    //     0x8c7aa0: b.ne            #0x8c7ab0
    //     0x8c7aa4: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c7aa8: ldr             x2, [x2, #0xf80]
    //     0x8c7aac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7ab0: r16 = <Digest>
    //     0x8c7ab0: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b8] TypeArguments: <Digest>
    //     0x8c7ab4: ldr             x16, [x16, #0x1b8]
    // 0x8c7ab8: stp             x0, x16, [SP, #8]
    // 0x8c7abc: ldur            x16, [fp, #-8]
    // 0x8c7ac0: str             x16, [SP]
    // 0x8c7ac4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c7ac4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c7ac8: r0 = create()
    //     0x8c7ac8: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c7acc: r0 = PKCS5S1ParameterGenerator()
    //     0x8c7acc: bl              #0x8c7ae8  ; AllocatePKCS5S1ParameterGeneratorStub -> PKCS5S1ParameterGenerator (size=0x8)
    // 0x8c7ad0: LeaveFrame
    //     0x8c7ad0: mov             SP, fp
    //     0x8c7ad4: ldp             fp, lr, [SP], #0x10
    // 0x8c7ad8: ret
    //     0x8c7ad8: ret             
    // 0x8c7adc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7adc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7ae0: b               #0x8c7a58
    // 0x8c7ae4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c7ae4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
