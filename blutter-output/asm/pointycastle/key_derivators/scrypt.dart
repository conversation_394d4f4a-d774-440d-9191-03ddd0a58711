// lib: impl.key_derivator.scrypt, url: package:pointycastle/key_derivators/scrypt.dart

// class id: 1051013, size: 0x8
class :: {
}

// class id: 584, size: 0x8, field offset: 0x8
class Scrypt extends BaseKeyDerivator {

  static late final FactoryConfig factoryConfig; // offset: 0xe48

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c8948, size: 0x58
    // 0x8c8948: EnterFrame
    //     0x8c8948: stp             fp, lr, [SP, #-0x10]!
    //     0x8c894c: mov             fp, SP
    // 0x8c8950: AllocStack(0x8)
    //     0x8c8950: sub             SP, SP, #8
    // 0x8c8954: r0 = StaticFactoryConfig()
    //     0x8c8954: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c8958: mov             x3, x0
    // 0x8c895c: r0 = "scrypt"
    //     0x8c895c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18568] "scrypt"
    //     0x8c8960: ldr             x0, [x0, #0x568]
    // 0x8c8964: stur            x3, [fp, #-8]
    // 0x8c8968: StoreField: r3->field_b = r0
    //     0x8c8968: stur            w0, [x3, #0xb]
    // 0x8c896c: r1 = Function '<anonymous closure>': static.
    //     0x8c896c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18570] AnonymousClosure: static (0x8c89a0), in [package:pointycastle/key_derivators/scrypt.dart] Scrypt::factoryConfig (0x8c8948)
    //     0x8c8970: ldr             x1, [x1, #0x570]
    // 0x8c8974: r2 = Null
    //     0x8c8974: mov             x2, NULL
    // 0x8c8978: r0 = AllocateClosure()
    //     0x8c8978: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c897c: mov             x1, x0
    // 0x8c8980: ldur            x0, [fp, #-8]
    // 0x8c8984: StoreField: r0->field_f = r1
    //     0x8c8984: stur            w1, [x0, #0xf]
    // 0x8c8988: r1 = KeyDerivator
    //     0x8c8988: add             x1, PP, #0x18, lsl #12  ; [pp+0x184b8] Type: KeyDerivator
    //     0x8c898c: ldr             x1, [x1, #0x4b8]
    // 0x8c8990: StoreField: r0->field_7 = r1
    //     0x8c8990: stur            w1, [x0, #7]
    // 0x8c8994: LeaveFrame
    //     0x8c8994: mov             SP, fp
    //     0x8c8998: ldp             fp, lr, [SP], #0x10
    // 0x8c899c: ret
    //     0x8c899c: ret             
  }
  [closure] static Scrypt <anonymous closure>(dynamic) {
    // ** addr: 0x8c89a0, size: 0x40
    // 0x8c89a0: EnterFrame
    //     0x8c89a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c89a4: mov             fp, SP
    // 0x8c89a8: AllocStack(0x8)
    //     0x8c89a8: sub             SP, SP, #8
    // 0x8c89ac: CheckStackOverflow
    //     0x8c89ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c89b0: cmp             SP, x16
    //     0x8c89b4: b.ls            #0x8c89d8
    // 0x8c89b8: r0 = Scrypt()
    //     0x8c89b8: bl              #0x8c8a7c  ; AllocateScryptStub -> Scrypt (size=0x8)
    // 0x8c89bc: mov             x1, x0
    // 0x8c89c0: stur            x0, [fp, #-8]
    // 0x8c89c4: r0 = Scrypt()
    //     0x8c89c4: bl              #0x8c89e0  ; [package:pointycastle/key_derivators/scrypt.dart] Scrypt::Scrypt
    // 0x8c89c8: ldur            x0, [fp, #-8]
    // 0x8c89cc: LeaveFrame
    //     0x8c89cc: mov             SP, fp
    //     0x8c89d0: ldp             fp, lr, [SP], #0x10
    // 0x8c89d4: ret
    //     0x8c89d4: ret             
    // 0x8c89d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c89d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c89dc: b               #0x8c89b8
  }
  _ Scrypt(/* No info */) {
    // ** addr: 0x8c89e0, size: 0x9c
    // 0x8c89e0: EnterFrame
    //     0x8c89e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c89e4: mov             fp, SP
    // 0x8c89e8: mov             x0, x1
    // 0x8c89ec: r1 = <int>
    //     0x8c89ec: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8c89f0: r2 = 32
    //     0x8c89f0: movz            x2, #0x20
    // 0x8c89f4: r0 = AllocateArray()
    //     0x8c89f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c89f8: r1 = 0
    //     0x8c89f8: movz            x1, #0
    // 0x8c89fc: CheckStackOverflow
    //     0x8c89fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c8a00: cmp             SP, x16
    //     0x8c8a04: b.ls            #0x8c8a6c
    // 0x8c8a08: cmp             x1, #0x10
    // 0x8c8a0c: b.ge            #0x8c8a24
    // 0x8c8a10: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0x8c8a10: add             x2, x0, x1, lsl #2
    //     0x8c8a14: stur            wzr, [x2, #0xf]
    // 0x8c8a18: add             x2, x1, #1
    // 0x8c8a1c: mov             x1, x2
    // 0x8c8a20: b               #0x8c89fc
    // 0x8c8a24: r1 = <int>
    //     0x8c8a24: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8c8a28: r2 = 32
    //     0x8c8a28: movz            x2, #0x20
    // 0x8c8a2c: r0 = AllocateArray()
    //     0x8c8a2c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c8a30: r1 = 0
    //     0x8c8a30: movz            x1, #0
    // 0x8c8a34: CheckStackOverflow
    //     0x8c8a34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c8a38: cmp             SP, x16
    //     0x8c8a3c: b.ls            #0x8c8a74
    // 0x8c8a40: cmp             x1, #0x10
    // 0x8c8a44: b.ge            #0x8c8a5c
    // 0x8c8a48: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0x8c8a48: add             x2, x0, x1, lsl #2
    //     0x8c8a4c: stur            wzr, [x2, #0xf]
    // 0x8c8a50: add             x2, x1, #1
    // 0x8c8a54: mov             x1, x2
    // 0x8c8a58: b               #0x8c8a34
    // 0x8c8a5c: r0 = Null
    //     0x8c8a5c: mov             x0, NULL
    // 0x8c8a60: LeaveFrame
    //     0x8c8a60: mov             SP, fp
    //     0x8c8a64: ldp             fp, lr, [SP], #0x10
    // 0x8c8a68: ret
    //     0x8c8a68: ret             
    // 0x8c8a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c8a6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c8a70: b               #0x8c8a08
    // 0x8c8a74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c8a74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c8a78: b               #0x8c8a40
  }
}
