// lib: impl.key_generator.ec_key_generator, url: package:pointycastle/key_generators/ec_key_generator.dart

// class id: 1051014, size: 0x8
class :: {
}

// class id: 580, size: 0x8, field offset: 0x8
class ECKeyGenerator extends Object
    implements KeyGenerator {

  static late final FactoryConfig factoryConfig; // offset: 0xe54

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c7e24, size: 0x58
    // 0x8c7e24: EnterFrame
    //     0x8c7e24: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7e28: mov             fp, SP
    // 0x8c7e2c: AllocStack(0x8)
    //     0x8c7e2c: sub             SP, SP, #8
    // 0x8c7e30: r0 = StaticFactoryConfig()
    //     0x8c7e30: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c7e34: mov             x3, x0
    // 0x8c7e38: r0 = "EC"
    //     0x8c7e38: add             x0, PP, #0x18, lsl #12  ; [pp+0x18468] "EC"
    //     0x8c7e3c: ldr             x0, [x0, #0x468]
    // 0x8c7e40: stur            x3, [fp, #-8]
    // 0x8c7e44: StoreField: r3->field_b = r0
    //     0x8c7e44: stur            w0, [x3, #0xb]
    // 0x8c7e48: r1 = Function '<anonymous closure>': static.
    //     0x8c7e48: add             x1, PP, #0x18, lsl #12  ; [pp+0x18470] AnonymousClosure: static (0x8c7e7c), in [package:pointycastle/key_generators/ec_key_generator.dart] ECKeyGenerator::factoryConfig (0x8c7e24)
    //     0x8c7e4c: ldr             x1, [x1, #0x470]
    // 0x8c7e50: r2 = Null
    //     0x8c7e50: mov             x2, NULL
    // 0x8c7e54: r0 = AllocateClosure()
    //     0x8c7e54: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c7e58: mov             x1, x0
    // 0x8c7e5c: ldur            x0, [fp, #-8]
    // 0x8c7e60: StoreField: r0->field_f = r1
    //     0x8c7e60: stur            w1, [x0, #0xf]
    // 0x8c7e64: r1 = KeyGenerator
    //     0x8c7e64: add             x1, PP, #0x18, lsl #12  ; [pp+0x18460] Type: KeyGenerator
    //     0x8c7e68: ldr             x1, [x1, #0x460]
    // 0x8c7e6c: StoreField: r0->field_7 = r1
    //     0x8c7e6c: stur            w1, [x0, #7]
    // 0x8c7e70: LeaveFrame
    //     0x8c7e70: mov             SP, fp
    //     0x8c7e74: ldp             fp, lr, [SP], #0x10
    // 0x8c7e78: ret
    //     0x8c7e78: ret             
  }
  [closure] static ECKeyGenerator <anonymous closure>(dynamic) {
    // ** addr: 0x8c7e7c, size: 0x18
    // 0x8c7e7c: EnterFrame
    //     0x8c7e7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7e80: mov             fp, SP
    // 0x8c7e84: r0 = ECKeyGenerator()
    //     0x8c7e84: bl              #0x8c7e94  ; AllocateECKeyGeneratorStub -> ECKeyGenerator (size=0x8)
    // 0x8c7e88: LeaveFrame
    //     0x8c7e88: mov             SP, fp
    //     0x8c7e8c: ldp             fp, lr, [SP], #0x10
    // 0x8c7e90: ret
    //     0x8c7e90: ret             
  }
}
