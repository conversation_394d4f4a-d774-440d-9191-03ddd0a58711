// lib: impl.key_generator.rsa_key_generator, url: package:pointycastle/key_generators/rsa_key_generator.dart

// class id: 1051015, size: 0x8
class :: {
}

// class id: 579, size: 0x8, field offset: 0x8
class RSAKeyGenerator extends Object
    implements KeyGenerator {

  static late final FactoryConfig factoryConfig; // offset: 0xe58

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c7da8, size: 0x58
    // 0x8c7da8: EnterFrame
    //     0x8c7da8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7dac: mov             fp, SP
    // 0x8c7db0: AllocStack(0x8)
    //     0x8c7db0: sub             SP, SP, #8
    // 0x8c7db4: r0 = StaticFactoryConfig()
    //     0x8c7db4: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c7db8: mov             x3, x0
    // 0x8c7dbc: r0 = "RSA"
    //     0x8c7dbc: add             x0, PP, #0x18, lsl #12  ; [pp+0x18450] "RSA"
    //     0x8c7dc0: ldr             x0, [x0, #0x450]
    // 0x8c7dc4: stur            x3, [fp, #-8]
    // 0x8c7dc8: StoreField: r3->field_b = r0
    //     0x8c7dc8: stur            w0, [x3, #0xb]
    // 0x8c7dcc: r1 = Function '<anonymous closure>': static.
    //     0x8c7dcc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18458] AnonymousClosure: static (0x8c7e00), in [package:pointycastle/key_generators/rsa_key_generator.dart] RSAKeyGenerator::factoryConfig (0x8c7da8)
    //     0x8c7dd0: ldr             x1, [x1, #0x458]
    // 0x8c7dd4: r2 = Null
    //     0x8c7dd4: mov             x2, NULL
    // 0x8c7dd8: r0 = AllocateClosure()
    //     0x8c7dd8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c7ddc: mov             x1, x0
    // 0x8c7de0: ldur            x0, [fp, #-8]
    // 0x8c7de4: StoreField: r0->field_f = r1
    //     0x8c7de4: stur            w1, [x0, #0xf]
    // 0x8c7de8: r1 = KeyGenerator
    //     0x8c7de8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18460] Type: KeyGenerator
    //     0x8c7dec: ldr             x1, [x1, #0x460]
    // 0x8c7df0: StoreField: r0->field_7 = r1
    //     0x8c7df0: stur            w1, [x0, #7]
    // 0x8c7df4: LeaveFrame
    //     0x8c7df4: mov             SP, fp
    //     0x8c7df8: ldp             fp, lr, [SP], #0x10
    // 0x8c7dfc: ret
    //     0x8c7dfc: ret             
  }
  [closure] static RSAKeyGenerator <anonymous closure>(dynamic) {
    // ** addr: 0x8c7e00, size: 0x18
    // 0x8c7e00: EnterFrame
    //     0x8c7e00: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7e04: mov             fp, SP
    // 0x8c7e08: r0 = RSAKeyGenerator()
    //     0x8c7e08: bl              #0x8c7e18  ; AllocateRSAKeyGeneratorStub -> RSAKeyGenerator (size=0x8)
    // 0x8c7e0c: LeaveFrame
    //     0x8c7e0c: mov             SP, fp
    //     0x8c7e10: ldp             fp, lr, [SP], #0x10
    // 0x8c7e14: ret
    //     0x8c7e14: ret             
  }
}
