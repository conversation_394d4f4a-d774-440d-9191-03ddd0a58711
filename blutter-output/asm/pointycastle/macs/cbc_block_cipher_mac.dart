// lib: impl.mac.cbc_block_cipher_mac, url: package:pointycastle/macs/cbc_block_cipher_mac.dart

// class id: 1051016, size: 0x8
class :: {
}

// class id: 578, size: 0x10, field offset: 0x8
class CBCBlockCipherMac extends BaseMac {

  static late final FactoryConfig factoryConfig; // offset: 0xe68

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c6ff8, size: 0x98
    // 0x8c6ff8: EnterFrame
    //     0x8c6ff8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6ffc: mov             fp, SP
    // 0x8c7000: AllocStack(0x40)
    //     0x8c7000: sub             SP, SP, #0x40
    // 0x8c7004: CheckStackOverflow
    //     0x8c7004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7008: cmp             SP, x16
    //     0x8c700c: b.ls            #0x8c7088
    // 0x8c7010: r16 = "^(.+)/CBC_CMAC(/(.+))\?$"
    //     0x8c7010: add             x16, PP, #0x18, lsl #12  ; [pp+0x183b8] "^(.+)/CBC_CMAC(/(.+))\?$"
    //     0x8c7014: ldr             x16, [x16, #0x3b8]
    // 0x8c7018: stp             x16, NULL, [SP, #0x20]
    // 0x8c701c: r16 = false
    //     0x8c701c: add             x16, NULL, #0x30  ; false
    // 0x8c7020: r30 = true
    //     0x8c7020: add             lr, NULL, #0x20  ; true
    // 0x8c7024: stp             lr, x16, [SP, #0x10]
    // 0x8c7028: r16 = false
    //     0x8c7028: add             x16, NULL, #0x30  ; false
    // 0x8c702c: r30 = false
    //     0x8c702c: add             lr, NULL, #0x30  ; false
    // 0x8c7030: stp             lr, x16, [SP]
    // 0x8c7034: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8c7034: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8c7038: r0 = _RegExp()
    //     0x8c7038: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8c703c: stur            x0, [fp, #-8]
    // 0x8c7040: r0 = DynamicFactoryConfig()
    //     0x8c7040: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c7044: mov             x3, x0
    // 0x8c7048: ldur            x0, [fp, #-8]
    // 0x8c704c: stur            x3, [fp, #-0x10]
    // 0x8c7050: StoreField: r3->field_b = r0
    //     0x8c7050: stur            w0, [x3, #0xb]
    // 0x8c7054: r1 = Function '<anonymous closure>': static.
    //     0x8c7054: add             x1, PP, #0x18, lsl #12  ; [pp+0x183c0] AnonymousClosure: static (0x8c7090), in [package:pointycastle/macs/cbc_block_cipher_mac.dart] CBCBlockCipherMac::factoryConfig (0x8c6ff8)
    //     0x8c7058: ldr             x1, [x1, #0x3c0]
    // 0x8c705c: r2 = Null
    //     0x8c705c: mov             x2, NULL
    // 0x8c7060: r0 = AllocateClosure()
    //     0x8c7060: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c7064: mov             x1, x0
    // 0x8c7068: ldur            x0, [fp, #-0x10]
    // 0x8c706c: StoreField: r0->field_f = r1
    //     0x8c706c: stur            w1, [x0, #0xf]
    // 0x8c7070: r1 = Mac
    //     0x8c7070: add             x1, PP, #0x18, lsl #12  ; [pp+0x18398] Type: Mac
    //     0x8c7074: ldr             x1, [x1, #0x398]
    // 0x8c7078: StoreField: r0->field_7 = r1
    //     0x8c7078: stur            w1, [x0, #7]
    // 0x8c707c: LeaveFrame
    //     0x8c707c: mov             SP, fp
    //     0x8c7080: ldp             fp, lr, [SP], #0x10
    // 0x8c7084: ret
    //     0x8c7084: ret             
    // 0x8c7088: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7088: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c708c: b               #0x8c7010
  }
  [closure] static (dynamic) => CBCBlockCipherMac <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c7090, size: 0x54
    // 0x8c7090: EnterFrame
    //     0x8c7090: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7094: mov             fp, SP
    // 0x8c7098: AllocStack(0x8)
    //     0x8c7098: sub             SP, SP, #8
    // 0x8c709c: SetupParameters()
    //     0x8c709c: ldr             x0, [fp, #0x20]
    //     0x8c70a0: ldur            w1, [x0, #0x17]
    //     0x8c70a4: add             x1, x1, HEAP, lsl #32
    //     0x8c70a8: stur            x1, [fp, #-8]
    // 0x8c70ac: r1 = 1
    //     0x8c70ac: movz            x1, #0x1
    // 0x8c70b0: r0 = AllocateContext()
    //     0x8c70b0: bl              #0xec126c  ; AllocateContextStub
    // 0x8c70b4: mov             x1, x0
    // 0x8c70b8: ldur            x0, [fp, #-8]
    // 0x8c70bc: StoreField: r1->field_b = r0
    //     0x8c70bc: stur            w0, [x1, #0xb]
    // 0x8c70c0: ldr             x0, [fp, #0x10]
    // 0x8c70c4: StoreField: r1->field_f = r0
    //     0x8c70c4: stur            w0, [x1, #0xf]
    // 0x8c70c8: mov             x2, x1
    // 0x8c70cc: r1 = Function '<anonymous closure>': static.
    //     0x8c70cc: add             x1, PP, #0x18, lsl #12  ; [pp+0x183c8] AnonymousClosure: static (0x8c70e4), in [package:pointycastle/macs/cbc_block_cipher_mac.dart] CBCBlockCipherMac::factoryConfig (0x8c6ff8)
    //     0x8c70d0: ldr             x1, [x1, #0x3c8]
    // 0x8c70d4: r0 = AllocateClosure()
    //     0x8c70d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c70d8: LeaveFrame
    //     0x8c70d8: mov             SP, fp
    //     0x8c70dc: ldp             fp, lr, [SP], #0x10
    // 0x8c70e0: ret
    //     0x8c70e0: ret             
  }
  [closure] static CBCBlockCipherMac <anonymous closure>(dynamic) {
    // ** addr: 0x8c70e4, size: 0x194
    // 0x8c70e4: EnterFrame
    //     0x8c70e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c70e8: mov             fp, SP
    // 0x8c70ec: AllocStack(0x30)
    //     0x8c70ec: sub             SP, SP, #0x30
    // 0x8c70f0: SetupParameters()
    //     0x8c70f0: ldr             x0, [fp, #0x10]
    //     0x8c70f4: ldur            w1, [x0, #0x17]
    //     0x8c70f8: add             x1, x1, HEAP, lsl #32
    // 0x8c70fc: CheckStackOverflow
    //     0x8c70fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7100: cmp             SP, x16
    //     0x8c7104: b.ls            #0x8c7264
    // 0x8c7108: LoadField: r3 = r1->field_f
    //     0x8c7108: ldur            w3, [x1, #0xf]
    // 0x8c710c: DecompressPointer r3
    //     0x8c710c: add             x3, x3, HEAP, lsl #32
    // 0x8c7110: stur            x3, [fp, #-8]
    // 0x8c7114: r0 = LoadClassIdInstr(r3)
    //     0x8c7114: ldur            x0, [x3, #-1]
    //     0x8c7118: ubfx            x0, x0, #0xc, #0x14
    // 0x8c711c: mov             x1, x3
    // 0x8c7120: r2 = 1
    //     0x8c7120: movz            x2, #0x1
    // 0x8c7124: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c7124: sub             lr, x0, #0xfdd
    //     0x8c7128: ldr             lr, [x21, lr, lsl #3]
    //     0x8c712c: blr             lr
    // 0x8c7130: stur            x0, [fp, #-0x10]
    // 0x8c7134: cmp             w0, NULL
    // 0x8c7138: b.eq            #0x8c726c
    // 0x8c713c: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c713c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7140: ldr             x0, [x0, #0x2e38]
    //     0x8c7144: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7148: cmp             w0, w16
    //     0x8c714c: b.ne            #0x8c715c
    //     0x8c7150: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c7154: ldr             x2, [x2, #0xf80]
    //     0x8c7158: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c715c: stur            x0, [fp, #-0x18]
    // 0x8c7160: r16 = <BlockCipher>
    //     0x8c7160: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8c7164: ldr             x16, [x16, #0x88]
    // 0x8c7168: stp             x0, x16, [SP, #8]
    // 0x8c716c: ldur            x16, [fp, #-0x10]
    // 0x8c7170: str             x16, [SP]
    // 0x8c7174: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c7174: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c7178: r0 = create()
    //     0x8c7178: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c717c: mov             x3, x0
    // 0x8c7180: ldur            x2, [fp, #-8]
    // 0x8c7184: stur            x3, [fp, #-0x10]
    // 0x8c7188: r0 = LoadClassIdInstr(r2)
    //     0x8c7188: ldur            x0, [x2, #-1]
    //     0x8c718c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c7190: mov             x1, x2
    // 0x8c7194: r0 = GDT[cid_x0 + -0xfbd]()
    //     0x8c7194: sub             lr, x0, #0xfbd
    //     0x8c7198: ldr             lr, [x21, lr, lsl #3]
    //     0x8c719c: blr             lr
    // 0x8c71a0: cmp             x0, #3
    // 0x8c71a4: b.lt            #0x8c7240
    // 0x8c71a8: ldur            x3, [fp, #-8]
    // 0x8c71ac: r0 = LoadClassIdInstr(r3)
    //     0x8c71ac: ldur            x0, [x3, #-1]
    //     0x8c71b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8c71b4: mov             x1, x3
    // 0x8c71b8: r2 = 3
    //     0x8c71b8: movz            x2, #0x3
    // 0x8c71bc: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c71bc: sub             lr, x0, #0xfdd
    //     0x8c71c0: ldr             lr, [x21, lr, lsl #3]
    //     0x8c71c4: blr             lr
    // 0x8c71c8: cmp             w0, NULL
    // 0x8c71cc: b.eq            #0x8c7240
    // 0x8c71d0: ldur            x3, [fp, #-8]
    // 0x8c71d4: r0 = LoadClassIdInstr(r3)
    //     0x8c71d4: ldur            x0, [x3, #-1]
    //     0x8c71d8: ubfx            x0, x0, #0xc, #0x14
    // 0x8c71dc: mov             x1, x3
    // 0x8c71e0: r2 = 3
    //     0x8c71e0: movz            x2, #0x3
    // 0x8c71e4: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c71e4: sub             lr, x0, #0xfdd
    //     0x8c71e8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c71ec: blr             lr
    // 0x8c71f0: cmp             w0, NULL
    // 0x8c71f4: b.eq            #0x8c7270
    // 0x8c71f8: LoadField: r1 = r0->field_7
    //     0x8c71f8: ldur            w1, [x0, #7]
    // 0x8c71fc: cbz             w1, #0x8c7240
    // 0x8c7200: ldur            x1, [fp, #-8]
    // 0x8c7204: r0 = LoadClassIdInstr(r1)
    //     0x8c7204: ldur            x0, [x1, #-1]
    //     0x8c7208: ubfx            x0, x0, #0xc, #0x14
    // 0x8c720c: r2 = 3
    //     0x8c720c: movz            x2, #0x3
    // 0x8c7210: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c7210: sub             lr, x0, #0xfdd
    //     0x8c7214: ldr             lr, [x21, lr, lsl #3]
    //     0x8c7218: blr             lr
    // 0x8c721c: cmp             w0, NULL
    // 0x8c7220: b.eq            #0x8c7274
    // 0x8c7224: r16 = <Padding>
    //     0x8c7224: add             x16, PP, #0x18, lsl #12  ; [pp+0x18368] TypeArguments: <Padding>
    //     0x8c7228: ldr             x16, [x16, #0x368]
    // 0x8c722c: ldur            lr, [fp, #-0x18]
    // 0x8c7230: stp             lr, x16, [SP, #8]
    // 0x8c7234: str             x0, [SP]
    // 0x8c7238: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c7238: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c723c: r0 = create()
    //     0x8c723c: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c7240: r0 = CBCBlockCipherMac()
    //     0x8c7240: bl              #0x8c73e8  ; AllocateCBCBlockCipherMacStub -> CBCBlockCipherMac (size=0x10)
    // 0x8c7244: mov             x1, x0
    // 0x8c7248: ldur            x2, [fp, #-0x10]
    // 0x8c724c: stur            x0, [fp, #-8]
    // 0x8c7250: r0 = CBCBlockCipherMac.fromCipherAndPadding()
    //     0x8c7250: bl              #0x8c7278  ; [package:pointycastle/macs/cbc_block_cipher_mac.dart] CBCBlockCipherMac::CBCBlockCipherMac.fromCipherAndPadding
    // 0x8c7254: ldur            x0, [fp, #-8]
    // 0x8c7258: LeaveFrame
    //     0x8c7258: mov             SP, fp
    //     0x8c725c: ldp             fp, lr, [SP], #0x10
    // 0x8c7260: ret
    //     0x8c7260: ret             
    // 0x8c7264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7264: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7268: b               #0x8c7108
    // 0x8c726c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c726c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c7270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c7270: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c7274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c7274: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ CBCBlockCipherMac.fromCipherAndPadding(/* No info */) {
    // ** addr: 0x8c7278, size: 0x6c
    // 0x8c7278: EnterFrame
    //     0x8c7278: stp             fp, lr, [SP, #-0x10]!
    //     0x8c727c: mov             fp, SP
    // 0x8c7280: AllocStack(0x10)
    //     0x8c7280: sub             SP, SP, #0x10
    // 0x8c7284: SetupParameters(CBCBlockCipherMac this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8c7284: mov             x3, x1
    //     0x8c7288: stur            x1, [fp, #-8]
    //     0x8c728c: stur            x2, [fp, #-0x10]
    // 0x8c7290: CheckStackOverflow
    //     0x8c7290: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7294: cmp             SP, x16
    //     0x8c7298: b.ls            #0x8c72dc
    // 0x8c729c: r0 = LoadClassIdInstr(r2)
    //     0x8c729c: ldur            x0, [x2, #-1]
    //     0x8c72a0: ubfx            x0, x0, #0xc, #0x14
    // 0x8c72a4: mov             x1, x2
    // 0x8c72a8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c72a8: sub             lr, x0, #1, lsl #12
    //     0x8c72ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8c72b0: blr             lr
    // 0x8c72b4: lsl             x1, x0, #3
    // 0x8c72b8: r0 = 2
    //     0x8c72b8: movz            x0, #0x2
    // 0x8c72bc: sdiv            x3, x1, x0
    // 0x8c72c0: ldur            x1, [fp, #-8]
    // 0x8c72c4: ldur            x2, [fp, #-0x10]
    // 0x8c72c8: r0 = CBCBlockCipherMac()
    //     0x8c72c8: bl              #0x8c72e4  ; [package:pointycastle/macs/cbc_block_cipher_mac.dart] CBCBlockCipherMac::CBCBlockCipherMac
    // 0x8c72cc: r0 = Null
    //     0x8c72cc: mov             x0, NULL
    // 0x8c72d0: LeaveFrame
    //     0x8c72d0: mov             SP, fp
    //     0x8c72d4: ldp             fp, lr, [SP], #0x10
    // 0x8c72d8: ret
    //     0x8c72d8: ret             
    // 0x8c72dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c72dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c72e0: b               #0x8c729c
  }
  _ CBCBlockCipherMac(/* No info */) {
    // ** addr: 0x8c72e4, size: 0x104
    // 0x8c72e4: EnterFrame
    //     0x8c72e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c72e8: mov             fp, SP
    // 0x8c72ec: AllocStack(0x18)
    //     0x8c72ec: sub             SP, SP, #0x18
    // 0x8c72f0: SetupParameters(CBCBlockCipherMac this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x8c72f0: stur            x1, [fp, #-8]
    //     0x8c72f4: stur            x2, [fp, #-0x10]
    //     0x8c72f8: stur            x3, [fp, #-0x18]
    // 0x8c72fc: CheckStackOverflow
    //     0x8c72fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7300: cmp             SP, x16
    //     0x8c7304: b.ls            #0x8c73e0
    // 0x8c7308: r0 = CBCBlockCipher()
    //     0x8c7308: bl              #0x8c4af4  ; AllocateCBCBlockCipherStub -> CBCBlockCipher (size=0x1c)
    // 0x8c730c: mov             x1, x0
    // 0x8c7310: ldur            x2, [fp, #-0x10]
    // 0x8c7314: r0 = CBCBlockCipher()
    //     0x8c7314: bl              #0x8c4980  ; [package:pointycastle/block/modes/cbc.dart] CBCBlockCipher::CBCBlockCipher
    // 0x8c7318: ldur            x1, [fp, #-0x18]
    // 0x8c731c: r0 = 8
    //     0x8c731c: movz            x0, #0x8
    // 0x8c7320: sdiv            x2, x1, x0
    // 0x8c7324: ldur            x0, [fp, #-8]
    // 0x8c7328: StoreField: r0->field_7 = r2
    //     0x8c7328: stur            x2, [x0, #7]
    // 0x8c732c: tst             x1, #7
    // 0x8c7330: b.ne            #0x8c73b8
    // 0x8c7334: ldur            x2, [fp, #-0x10]
    // 0x8c7338: r0 = LoadClassIdInstr(r2)
    //     0x8c7338: ldur            x0, [x2, #-1]
    //     0x8c733c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c7340: mov             x1, x2
    // 0x8c7344: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c7344: sub             lr, x0, #1, lsl #12
    //     0x8c7348: ldr             lr, [x21, lr, lsl #3]
    //     0x8c734c: blr             lr
    // 0x8c7350: mov             x2, x0
    // 0x8c7354: r0 = BoxInt64Instr(r2)
    //     0x8c7354: sbfiz           x0, x2, #1, #0x1f
    //     0x8c7358: cmp             x2, x0, asr #1
    //     0x8c735c: b.eq            #0x8c7368
    //     0x8c7360: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c7364: stur            x2, [x0, #7]
    // 0x8c7368: mov             x4, x0
    // 0x8c736c: r0 = AllocateUint8Array()
    //     0x8c736c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c7370: ldur            x1, [fp, #-0x10]
    // 0x8c7374: r0 = LoadClassIdInstr(r1)
    //     0x8c7374: ldur            x0, [x1, #-1]
    //     0x8c7378: ubfx            x0, x0, #0xc, #0x14
    // 0x8c737c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c737c: sub             lr, x0, #1, lsl #12
    //     0x8c7380: ldr             lr, [x21, lr, lsl #3]
    //     0x8c7384: blr             lr
    // 0x8c7388: mov             x2, x0
    // 0x8c738c: r0 = BoxInt64Instr(r2)
    //     0x8c738c: sbfiz           x0, x2, #1, #0x1f
    //     0x8c7390: cmp             x2, x0, asr #1
    //     0x8c7394: b.eq            #0x8c73a0
    //     0x8c7398: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c739c: stur            x2, [x0, #7]
    // 0x8c73a0: mov             x4, x0
    // 0x8c73a4: r0 = AllocateUint8Array()
    //     0x8c73a4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c73a8: r0 = Null
    //     0x8c73a8: mov             x0, NULL
    // 0x8c73ac: LeaveFrame
    //     0x8c73ac: mov             SP, fp
    //     0x8c73b0: ldp             fp, lr, [SP], #0x10
    // 0x8c73b4: ret
    //     0x8c73b4: ret             
    // 0x8c73b8: r0 = ArgumentError()
    //     0x8c73b8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c73bc: mov             x1, x0
    // 0x8c73c0: r0 = "MAC size must be multiple of 8"
    //     0x8c73c0: add             x0, PP, #0x18, lsl #12  ; [pp+0x18070] "MAC size must be multiple of 8"
    //     0x8c73c4: ldr             x0, [x0, #0x70]
    // 0x8c73c8: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c73c8: stur            w0, [x1, #0x17]
    // 0x8c73cc: r0 = false
    //     0x8c73cc: add             x0, NULL, #0x30  ; false
    // 0x8c73d0: StoreField: r1->field_b = r0
    //     0x8c73d0: stur            w0, [x1, #0xb]
    // 0x8c73d4: mov             x0, x1
    // 0x8c73d8: r0 = Throw()
    //     0x8c73d8: bl              #0xec04b8  ; ThrowStub
    // 0x8c73dc: brk             #0
    // 0x8c73e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c73e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c73e4: b               #0x8c7308
  }
  get _ macSize(/* No info */) {
    // ** addr: 0xeb5dac, size: 0x8
    // 0xeb5dac: LoadField: r0 = r1->field_7
    //     0xeb5dac: ldur            x0, [x1, #7]
    // 0xeb5db0: ret
    //     0xeb5db0: ret             
  }
}
