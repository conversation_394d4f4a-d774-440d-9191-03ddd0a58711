// lib: impl.mac.cmac, url: package:pointycastle/macs/cmac.dart

// class id: 1051017, size: 0x8
class :: {
}

// class id: 577, size: 0x14, field offset: 0x8
class CMac extends BaseMac {

  static late final FactoryConfig factoryConfig; // offset: 0xe60

  _ CMac(/* No info */) {
    // ** addr: 0x8c44d8, size: 0x274
    // 0x8c44d8: EnterFrame
    //     0x8c44d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c44dc: mov             fp, SP
    // 0x8c44e0: AllocStack(0x28)
    //     0x8c44e0: sub             SP, SP, #0x28
    // 0x8c44e4: r0 = 8
    //     0x8c44e4: movz            x0, #0x8
    // 0x8c44e8: stur            x1, [fp, #-8]
    // 0x8c44ec: stur            x2, [fp, #-0x10]
    // 0x8c44f0: stur            x3, [fp, #-0x18]
    // 0x8c44f4: CheckStackOverflow
    //     0x8c44f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c44f8: cmp             SP, x16
    //     0x8c44fc: b.ls            #0x8c4744
    // 0x8c4500: sdiv            x4, x3, x0
    // 0x8c4504: StoreField: r1->field_b = r4
    //     0x8c4504: stur            x4, [x1, #0xb]
    // 0x8c4508: r0 = CBCBlockCipher()
    //     0x8c4508: bl              #0x8c4af4  ; AllocateCBCBlockCipherStub -> CBCBlockCipher (size=0x1c)
    // 0x8c450c: mov             x1, x0
    // 0x8c4510: ldur            x2, [fp, #-0x10]
    // 0x8c4514: stur            x0, [fp, #-0x20]
    // 0x8c4518: r0 = CBCBlockCipher()
    //     0x8c4518: bl              #0x8c4980  ; [package:pointycastle/block/modes/cbc.dart] CBCBlockCipher::CBCBlockCipher
    // 0x8c451c: ldur            x0, [fp, #-0x20]
    // 0x8c4520: ldur            x1, [fp, #-8]
    // 0x8c4524: StoreField: r1->field_7 = r0
    //     0x8c4524: stur            w0, [x1, #7]
    //     0x8c4528: ldurb           w16, [x1, #-1]
    //     0x8c452c: ldurb           w17, [x0, #-1]
    //     0x8c4530: and             x16, x17, x16, lsr #2
    //     0x8c4534: tst             x16, HEAP, lsr #32
    //     0x8c4538: b.eq            #0x8c4540
    //     0x8c453c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c4540: ldur            x2, [fp, #-0x18]
    // 0x8c4544: tst             x2, #7
    // 0x8c4548: b.ne            #0x8c4668
    // 0x8c454c: ldur            x0, [fp, #-0x20]
    // 0x8c4550: LoadField: r3 = r0->field_7
    //     0x8c4550: ldur            w3, [x0, #7]
    // 0x8c4554: DecompressPointer r3
    //     0x8c4554: add             x3, x3, HEAP, lsl #32
    // 0x8c4558: stur            x3, [fp, #-8]
    // 0x8c455c: r0 = LoadClassIdInstr(r3)
    //     0x8c455c: ldur            x0, [x3, #-1]
    //     0x8c4560: ubfx            x0, x0, #0xc, #0x14
    // 0x8c4564: mov             x1, x3
    // 0x8c4568: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c4568: sub             lr, x0, #1, lsl #12
    //     0x8c456c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c4570: blr             lr
    // 0x8c4574: lsl             x1, x0, #3
    // 0x8c4578: ldur            x0, [fp, #-0x18]
    // 0x8c457c: cmp             x0, x1
    // 0x8c4580: b.gt            #0x8c4690
    // 0x8c4584: ldur            x2, [fp, #-0x10]
    // 0x8c4588: r0 = LoadClassIdInstr(r2)
    //     0x8c4588: ldur            x0, [x2, #-1]
    //     0x8c458c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c4590: mov             x1, x2
    // 0x8c4594: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c4594: sub             lr, x0, #1, lsl #12
    //     0x8c4598: ldr             lr, [x21, lr, lsl #3]
    //     0x8c459c: blr             lr
    // 0x8c45a0: mov             x1, x0
    // 0x8c45a4: r0 = lookupPoly()
    //     0x8c45a4: bl              #0x8c474c  ; [package:pointycastle/macs/cmac.dart] CMac::lookupPoly
    // 0x8c45a8: ldur            x2, [fp, #-0x10]
    // 0x8c45ac: r0 = LoadClassIdInstr(r2)
    //     0x8c45ac: ldur            x0, [x2, #-1]
    //     0x8c45b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8c45b4: mov             x1, x2
    // 0x8c45b8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c45b8: sub             lr, x0, #1, lsl #12
    //     0x8c45bc: ldr             lr, [x21, lr, lsl #3]
    //     0x8c45c0: blr             lr
    // 0x8c45c4: mov             x2, x0
    // 0x8c45c8: r0 = BoxInt64Instr(r2)
    //     0x8c45c8: sbfiz           x0, x2, #1, #0x1f
    //     0x8c45cc: cmp             x2, x0, asr #1
    //     0x8c45d0: b.eq            #0x8c45dc
    //     0x8c45d4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c45d8: stur            x2, [x0, #7]
    // 0x8c45dc: mov             x4, x0
    // 0x8c45e0: r0 = AllocateUint8Array()
    //     0x8c45e0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c45e4: ldur            x2, [fp, #-0x10]
    // 0x8c45e8: r0 = LoadClassIdInstr(r2)
    //     0x8c45e8: ldur            x0, [x2, #-1]
    //     0x8c45ec: ubfx            x0, x0, #0xc, #0x14
    // 0x8c45f0: mov             x1, x2
    // 0x8c45f4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c45f4: sub             lr, x0, #1, lsl #12
    //     0x8c45f8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c45fc: blr             lr
    // 0x8c4600: mov             x2, x0
    // 0x8c4604: r0 = BoxInt64Instr(r2)
    //     0x8c4604: sbfiz           x0, x2, #1, #0x1f
    //     0x8c4608: cmp             x2, x0, asr #1
    //     0x8c460c: b.eq            #0x8c4618
    //     0x8c4610: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c4614: stur            x2, [x0, #7]
    // 0x8c4618: mov             x4, x0
    // 0x8c461c: r0 = AllocateUint8Array()
    //     0x8c461c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4620: ldur            x1, [fp, #-0x10]
    // 0x8c4624: r0 = LoadClassIdInstr(r1)
    //     0x8c4624: ldur            x0, [x1, #-1]
    //     0x8c4628: ubfx            x0, x0, #0xc, #0x14
    // 0x8c462c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c462c: sub             lr, x0, #1, lsl #12
    //     0x8c4630: ldr             lr, [x21, lr, lsl #3]
    //     0x8c4634: blr             lr
    // 0x8c4638: mov             x2, x0
    // 0x8c463c: r0 = BoxInt64Instr(r2)
    //     0x8c463c: sbfiz           x0, x2, #1, #0x1f
    //     0x8c4640: cmp             x2, x0, asr #1
    //     0x8c4644: b.eq            #0x8c4650
    //     0x8c4648: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c464c: stur            x2, [x0, #7]
    // 0x8c4650: mov             x4, x0
    // 0x8c4654: r0 = AllocateUint8Array()
    //     0x8c4654: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4658: r0 = Null
    //     0x8c4658: mov             x0, NULL
    // 0x8c465c: LeaveFrame
    //     0x8c465c: mov             SP, fp
    //     0x8c4660: ldp             fp, lr, [SP], #0x10
    // 0x8c4664: ret
    //     0x8c4664: ret             
    // 0x8c4668: r0 = ArgumentError()
    //     0x8c4668: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c466c: mov             x1, x0
    // 0x8c4670: r0 = "MAC size must be multiple of 8"
    //     0x8c4670: add             x0, PP, #0x18, lsl #12  ; [pp+0x18070] "MAC size must be multiple of 8"
    //     0x8c4674: ldr             x0, [x0, #0x70]
    // 0x8c4678: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c4678: stur            w0, [x1, #0x17]
    // 0x8c467c: r0 = false
    //     0x8c467c: add             x0, NULL, #0x30  ; false
    // 0x8c4680: StoreField: r1->field_b = r0
    //     0x8c4680: stur            w0, [x1, #0xb]
    // 0x8c4684: mov             x0, x1
    // 0x8c4688: r0 = Throw()
    //     0x8c4688: bl              #0xec04b8  ; ThrowStub
    // 0x8c468c: brk             #0
    // 0x8c4690: ldur            x3, [fp, #-8]
    // 0x8c4694: r0 = false
    //     0x8c4694: add             x0, NULL, #0x30  ; false
    // 0x8c4698: r1 = Null
    //     0x8c4698: mov             x1, NULL
    // 0x8c469c: r2 = 4
    //     0x8c469c: movz            x2, #0x4
    // 0x8c46a0: r0 = AllocateArray()
    //     0x8c46a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c46a4: mov             x2, x0
    // 0x8c46a8: stur            x2, [fp, #-0x10]
    // 0x8c46ac: r16 = "MAC size must be less or equal to "
    //     0x8c46ac: add             x16, PP, #0x18, lsl #12  ; [pp+0x18078] "MAC size must be less or equal to "
    //     0x8c46b0: ldr             x16, [x16, #0x78]
    // 0x8c46b4: StoreField: r2->field_f = r16
    //     0x8c46b4: stur            w16, [x2, #0xf]
    // 0x8c46b8: ldur            x1, [fp, #-8]
    // 0x8c46bc: r0 = LoadClassIdInstr(r1)
    //     0x8c46bc: ldur            x0, [x1, #-1]
    //     0x8c46c0: ubfx            x0, x0, #0xc, #0x14
    // 0x8c46c4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c46c4: sub             lr, x0, #1, lsl #12
    //     0x8c46c8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c46cc: blr             lr
    // 0x8c46d0: lsl             x2, x0, #3
    // 0x8c46d4: r0 = BoxInt64Instr(r2)
    //     0x8c46d4: sbfiz           x0, x2, #1, #0x1f
    //     0x8c46d8: cmp             x2, x0, asr #1
    //     0x8c46dc: b.eq            #0x8c46e8
    //     0x8c46e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c46e4: stur            x2, [x0, #7]
    // 0x8c46e8: ldur            x1, [fp, #-0x10]
    // 0x8c46ec: ArrayStore: r1[1] = r0  ; List_4
    //     0x8c46ec: add             x25, x1, #0x13
    //     0x8c46f0: str             w0, [x25]
    //     0x8c46f4: tbz             w0, #0, #0x8c4710
    //     0x8c46f8: ldurb           w16, [x1, #-1]
    //     0x8c46fc: ldurb           w17, [x0, #-1]
    //     0x8c4700: and             x16, x17, x16, lsr #2
    //     0x8c4704: tst             x16, HEAP, lsr #32
    //     0x8c4708: b.eq            #0x8c4710
    //     0x8c470c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8c4710: ldur            x16, [fp, #-0x10]
    // 0x8c4714: str             x16, [SP]
    // 0x8c4718: r0 = _interpolate()
    //     0x8c4718: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c471c: stur            x0, [fp, #-8]
    // 0x8c4720: r0 = ArgumentError()
    //     0x8c4720: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c4724: mov             x1, x0
    // 0x8c4728: ldur            x0, [fp, #-8]
    // 0x8c472c: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c472c: stur            w0, [x1, #0x17]
    // 0x8c4730: r0 = false
    //     0x8c4730: add             x0, NULL, #0x30  ; false
    // 0x8c4734: StoreField: r1->field_b = r0
    //     0x8c4734: stur            w0, [x1, #0xb]
    // 0x8c4738: mov             x0, x1
    // 0x8c473c: r0 = Throw()
    //     0x8c473c: bl              #0xec04b8  ; ThrowStub
    // 0x8c4740: brk             #0
    // 0x8c4744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4744: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c4748: b               #0x8c4500
  }
  static _ lookupPoly(/* No info */) {
    // ** addr: 0x8c474c, size: 0x234
    // 0x8c474c: EnterFrame
    //     0x8c474c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4750: mov             fp, SP
    // 0x8c4754: AllocStack(0x20)
    //     0x8c4754: sub             SP, SP, #0x20
    // 0x8c4758: CheckStackOverflow
    //     0x8c4758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c475c: cmp             SP, x16
    //     0x8c4760: b.ls            #0x8c4978
    // 0x8c4764: lsl             x3, x1, #3
    // 0x8c4768: stur            x3, [fp, #-0x10]
    // 0x8c476c: cmp             x3, #0x140
    // 0x8c4770: b.gt            #0x8c4828
    // 0x8c4774: cmp             x3, #0xc0
    // 0x8c4778: b.gt            #0x8c47e8
    // 0x8c477c: cmp             x3, #0x80
    // 0x8c4780: b.gt            #0x8c47c0
    // 0x8c4784: cmp             x3, #0x40
    // 0x8c4788: b.gt            #0x8c47b0
    // 0x8c478c: r0 = BoxInt64Instr(r3)
    //     0x8c478c: sbfiz           x0, x3, #1, #0x1f
    //     0x8c4790: cmp             x3, x0, asr #1
    //     0x8c4794: b.eq            #0x8c47a0
    //     0x8c4798: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c479c: stur            x3, [x0, #7]
    // 0x8c47a0: cmp             w0, #0x80
    // 0x8c47a4: b.ne            #0x8c4910
    // 0x8c47a8: r0 = 27
    //     0x8c47a8: movz            x0, #0x1b
    // 0x8c47ac: b               #0x8c48d4
    // 0x8c47b0: cmp             x3, #0x80
    // 0x8c47b4: b.lt            #0x8c4910
    // 0x8c47b8: r0 = 135
    //     0x8c47b8: movz            x0, #0x87
    // 0x8c47bc: b               #0x8c48d4
    // 0x8c47c0: cmp             x3, #0xa0
    // 0x8c47c4: b.lt            #0x8c4910
    // 0x8c47c8: cmp             x3, #0xa0
    // 0x8c47cc: b.gt            #0x8c47d8
    // 0x8c47d0: r0 = 45
    //     0x8c47d0: movz            x0, #0x2d
    // 0x8c47d4: b               #0x8c48d4
    // 0x8c47d8: cmp             x3, #0xc0
    // 0x8c47dc: b.lt            #0x8c4910
    // 0x8c47e0: r0 = 135
    //     0x8c47e0: movz            x0, #0x87
    // 0x8c47e4: b               #0x8c48d4
    // 0x8c47e8: cmp             x3, #0xe0
    // 0x8c47ec: b.lt            #0x8c4910
    // 0x8c47f0: cmp             x3, #0x100
    // 0x8c47f4: b.gt            #0x8c4818
    // 0x8c47f8: cmp             x3, #0xe0
    // 0x8c47fc: b.gt            #0x8c4808
    // 0x8c4800: r0 = 777
    //     0x8c4800: movz            x0, #0x309
    // 0x8c4804: b               #0x8c48d4
    // 0x8c4808: cmp             x3, #0x100
    // 0x8c480c: b.lt            #0x8c4910
    // 0x8c4810: r0 = 1061
    //     0x8c4810: movz            x0, #0x425
    // 0x8c4814: b               #0x8c48d4
    // 0x8c4818: cmp             x3, #0x140
    // 0x8c481c: b.lt            #0x8c4910
    // 0x8c4820: r0 = 27
    //     0x8c4820: movz            x0, #0x1b
    // 0x8c4824: b               #0x8c48d4
    // 0x8c4828: cmp             x3, #0x180
    // 0x8c482c: b.lt            #0x8c4910
    // 0x8c4830: cmp             x3, #0x200
    // 0x8c4834: b.gt            #0x8c4870
    // 0x8c4838: cmp             x3, #0x1c0
    // 0x8c483c: b.gt            #0x8c4860
    // 0x8c4840: cmp             x3, #0x180
    // 0x8c4844: b.gt            #0x8c4850
    // 0x8c4848: r0 = 4109
    //     0x8c4848: movz            x0, #0x100d
    // 0x8c484c: b               #0x8c48d4
    // 0x8c4850: cmp             x3, #0x1c0
    // 0x8c4854: b.lt            #0x8c4910
    // 0x8c4858: r0 = 2129
    //     0x8c4858: movz            x0, #0x851
    // 0x8c485c: b               #0x8c48d4
    // 0x8c4860: cmp             x3, #0x200
    // 0x8c4864: b.lt            #0x8c4910
    // 0x8c4868: r0 = 293
    //     0x8c4868: movz            x0, #0x125
    // 0x8c486c: b               #0x8c48d4
    // 0x8c4870: cmp             x3, #0x300
    // 0x8c4874: b.lt            #0x8c4910
    // 0x8c4878: cmp             x3, #0x400
    // 0x8c487c: b.gt            #0x8c48a8
    // 0x8c4880: cmp             x3, #0x300
    // 0x8c4884: b.gt            #0x8c4894
    // 0x8c4888: r0 = 655377
    //     0x8c4888: movz            x0, #0x11
    //     0x8c488c: movk            x0, #0xa, lsl #16
    // 0x8c4890: b               #0x8c48d4
    // 0x8c4894: cmp             x3, #0x400
    // 0x8c4898: b.lt            #0x8c4910
    // 0x8c489c: r0 = 524355
    //     0x8c489c: movz            x0, #0x43
    //     0x8c48a0: movk            x0, #0x8, lsl #16
    // 0x8c48a4: b               #0x8c48d4
    // 0x8c48a8: cmp             x3, #0x800
    // 0x8c48ac: b.lt            #0x8c4910
    // 0x8c48b0: r0 = BoxInt64Instr(r3)
    //     0x8c48b0: sbfiz           x0, x3, #1, #0x1f
    //     0x8c48b4: cmp             x3, x0, asr #1
    //     0x8c48b8: b.eq            #0x8c48c4
    //     0x8c48bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c48c0: stur            x3, [x0, #7]
    // 0x8c48c4: cmp             w0, #1, lsl #12
    // 0x8c48c8: b.ne            #0x8c4910
    // 0x8c48cc: r0 = 548865
    //     0x8c48cc: movz            x0, #0x6001
    //     0x8c48d0: movk            x0, #0x8, lsl #16
    // 0x8c48d4: stur            x0, [fp, #-8]
    // 0x8c48d8: r4 = 8
    //     0x8c48d8: movz            x4, #0x8
    // 0x8c48dc: r0 = AllocateUint8Array()
    //     0x8c48dc: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c48e0: mov             x1, x0
    // 0x8c48e4: ldur            x0, [fp, #-8]
    // 0x8c48e8: ArrayStore: r1[3] = r0  ; TypeUnknown_1
    //     0x8c48e8: strb            w0, [x1, #0x1a]
    // 0x8c48ec: asr             x2, x0, #8
    // 0x8c48f0: ArrayStore: r1[2] = r2  ; TypeUnknown_1
    //     0x8c48f0: strb            w2, [x1, #0x19]
    // 0x8c48f4: asr             x2, x0, #0x10
    // 0x8c48f8: ArrayStore: r1[1] = r2  ; TypeUnknown_1
    //     0x8c48f8: strb            w2, [x1, #0x18]
    // 0x8c48fc: ArrayStore: r1[0] = rZR  ; TypeUnknown_1
    //     0x8c48fc: strb            wzr, [x1, #0x17]
    // 0x8c4900: mov             x0, x1
    // 0x8c4904: LeaveFrame
    //     0x8c4904: mov             SP, fp
    //     0x8c4908: ldp             fp, lr, [SP], #0x10
    // 0x8c490c: ret
    //     0x8c490c: ret             
    // 0x8c4910: r1 = Null
    //     0x8c4910: mov             x1, NULL
    // 0x8c4914: r2 = 4
    //     0x8c4914: movz            x2, #0x4
    // 0x8c4918: r0 = AllocateArray()
    //     0x8c4918: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c491c: mov             x2, x0
    // 0x8c4920: r16 = "Unknown block size for CMAC: "
    //     0x8c4920: add             x16, PP, #0x18, lsl #12  ; [pp+0x18080] "Unknown block size for CMAC: "
    //     0x8c4924: ldr             x16, [x16, #0x80]
    // 0x8c4928: StoreField: r2->field_f = r16
    //     0x8c4928: stur            w16, [x2, #0xf]
    // 0x8c492c: ldur            x3, [fp, #-0x10]
    // 0x8c4930: r0 = BoxInt64Instr(r3)
    //     0x8c4930: sbfiz           x0, x3, #1, #0x1f
    //     0x8c4934: cmp             x3, x0, asr #1
    //     0x8c4938: b.eq            #0x8c4944
    //     0x8c493c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c4940: stur            x3, [x0, #7]
    // 0x8c4944: StoreField: r2->field_13 = r0
    //     0x8c4944: stur            w0, [x2, #0x13]
    // 0x8c4948: str             x2, [SP]
    // 0x8c494c: r0 = _interpolate()
    //     0x8c494c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c4950: stur            x0, [fp, #-0x18]
    // 0x8c4954: r0 = ArgumentError()
    //     0x8c4954: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c4958: mov             x1, x0
    // 0x8c495c: ldur            x0, [fp, #-0x18]
    // 0x8c4960: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c4960: stur            w0, [x1, #0x17]
    // 0x8c4964: r0 = false
    //     0x8c4964: add             x0, NULL, #0x30  ; false
    // 0x8c4968: StoreField: r1->field_b = r0
    //     0x8c4968: stur            w0, [x1, #0xb]
    // 0x8c496c: mov             x0, x1
    // 0x8c4970: r0 = Throw()
    //     0x8c4970: bl              #0xec04b8  ; ThrowStub
    // 0x8c4974: brk             #0
    // 0x8c4978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4978: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c497c: b               #0x8c4764
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c73f4, size: 0x64
    // 0x8c73f4: EnterFrame
    //     0x8c73f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c73f8: mov             fp, SP
    // 0x8c73fc: AllocStack(0x8)
    //     0x8c73fc: sub             SP, SP, #8
    // 0x8c7400: CheckStackOverflow
    //     0x8c7400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7404: cmp             SP, x16
    //     0x8c7408: b.ls            #0x8c7450
    // 0x8c740c: r0 = DynamicFactoryConfig()
    //     0x8c740c: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c7410: r1 = Function '<anonymous closure>': static.
    //     0x8c7410: add             x1, PP, #0x18, lsl #12  ; [pp+0x183d0] AnonymousClosure: static (0x8c7458), in [package:pointycastle/macs/cmac.dart] CMac::factoryConfig (0x8c73f4)
    //     0x8c7414: ldr             x1, [x1, #0x3d0]
    // 0x8c7418: r2 = Null
    //     0x8c7418: mov             x2, NULL
    // 0x8c741c: stur            x0, [fp, #-8]
    // 0x8c7420: r0 = AllocateClosure()
    //     0x8c7420: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c7424: ldur            x1, [fp, #-8]
    // 0x8c7428: mov             x5, x0
    // 0x8c742c: r2 = Mac
    //     0x8c742c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18398] Type: Mac
    //     0x8c7430: ldr             x2, [x2, #0x398]
    // 0x8c7434: r3 = "/CMAC"
    //     0x8c7434: add             x3, PP, #0x18, lsl #12  ; [pp+0x183d8] "/CMAC"
    //     0x8c7438: ldr             x3, [x3, #0x3d8]
    // 0x8c743c: r0 = DynamicFactoryConfig.suffix()
    //     0x8c743c: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c7440: ldur            x0, [fp, #-8]
    // 0x8c7444: LeaveFrame
    //     0x8c7444: mov             SP, fp
    //     0x8c7448: ldp             fp, lr, [SP], #0x10
    // 0x8c744c: ret
    //     0x8c744c: ret             
    // 0x8c7450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7450: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7454: b               #0x8c740c
  }
  [closure] static (dynamic) => CMac <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c7458, size: 0x54
    // 0x8c7458: EnterFrame
    //     0x8c7458: stp             fp, lr, [SP, #-0x10]!
    //     0x8c745c: mov             fp, SP
    // 0x8c7460: AllocStack(0x8)
    //     0x8c7460: sub             SP, SP, #8
    // 0x8c7464: SetupParameters()
    //     0x8c7464: ldr             x0, [fp, #0x20]
    //     0x8c7468: ldur            w1, [x0, #0x17]
    //     0x8c746c: add             x1, x1, HEAP, lsl #32
    //     0x8c7470: stur            x1, [fp, #-8]
    // 0x8c7474: r1 = 1
    //     0x8c7474: movz            x1, #0x1
    // 0x8c7478: r0 = AllocateContext()
    //     0x8c7478: bl              #0xec126c  ; AllocateContextStub
    // 0x8c747c: mov             x1, x0
    // 0x8c7480: ldur            x0, [fp, #-8]
    // 0x8c7484: StoreField: r1->field_b = r0
    //     0x8c7484: stur            w0, [x1, #0xb]
    // 0x8c7488: ldr             x0, [fp, #0x10]
    // 0x8c748c: StoreField: r1->field_f = r0
    //     0x8c748c: stur            w0, [x1, #0xf]
    // 0x8c7490: mov             x2, x1
    // 0x8c7494: r1 = Function '<anonymous closure>': static.
    //     0x8c7494: add             x1, PP, #0x18, lsl #12  ; [pp+0x183e0] AnonymousClosure: static (0x8c74ac), in [package:pointycastle/macs/cmac.dart] CMac::factoryConfig (0x8c73f4)
    //     0x8c7498: ldr             x1, [x1, #0x3e0]
    // 0x8c749c: r0 = AllocateClosure()
    //     0x8c749c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c74a0: LeaveFrame
    //     0x8c74a0: mov             SP, fp
    //     0x8c74a4: ldp             fp, lr, [SP], #0x10
    // 0x8c74a8: ret
    //     0x8c74a8: ret             
  }
  [closure] static CMac <anonymous closure>(dynamic) {
    // ** addr: 0x8c74ac, size: 0xcc
    // 0x8c74ac: EnterFrame
    //     0x8c74ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8c74b0: mov             fp, SP
    // 0x8c74b4: AllocStack(0x20)
    //     0x8c74b4: sub             SP, SP, #0x20
    // 0x8c74b8: SetupParameters()
    //     0x8c74b8: ldr             x0, [fp, #0x10]
    //     0x8c74bc: ldur            w1, [x0, #0x17]
    //     0x8c74c0: add             x1, x1, HEAP, lsl #32
    // 0x8c74c4: CheckStackOverflow
    //     0x8c74c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c74c8: cmp             SP, x16
    //     0x8c74cc: b.ls            #0x8c756c
    // 0x8c74d0: LoadField: r0 = r1->field_f
    //     0x8c74d0: ldur            w0, [x1, #0xf]
    // 0x8c74d4: DecompressPointer r0
    //     0x8c74d4: add             x0, x0, HEAP, lsl #32
    // 0x8c74d8: r1 = LoadClassIdInstr(r0)
    //     0x8c74d8: ldur            x1, [x0, #-1]
    //     0x8c74dc: ubfx            x1, x1, #0xc, #0x14
    // 0x8c74e0: mov             x16, x0
    // 0x8c74e4: mov             x0, x1
    // 0x8c74e8: mov             x1, x16
    // 0x8c74ec: r2 = 1
    //     0x8c74ec: movz            x2, #0x1
    // 0x8c74f0: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c74f0: sub             lr, x0, #0xfdd
    //     0x8c74f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8c74f8: blr             lr
    // 0x8c74fc: stur            x0, [fp, #-8]
    // 0x8c7500: cmp             w0, NULL
    // 0x8c7504: b.eq            #0x8c7574
    // 0x8c7508: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c7508: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c750c: ldr             x0, [x0, #0x2e38]
    //     0x8c7510: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7514: cmp             w0, w16
    //     0x8c7518: b.ne            #0x8c7528
    //     0x8c751c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c7520: ldr             x2, [x2, #0xf80]
    //     0x8c7524: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7528: r16 = <BlockCipher>
    //     0x8c7528: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8c752c: ldr             x16, [x16, #0x88]
    // 0x8c7530: stp             x0, x16, [SP, #8]
    // 0x8c7534: ldur            x16, [fp, #-8]
    // 0x8c7538: str             x16, [SP]
    // 0x8c753c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c753c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c7540: r0 = create()
    //     0x8c7540: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c7544: stur            x0, [fp, #-8]
    // 0x8c7548: r0 = CMac()
    //     0x8c7548: bl              #0x8c4b00  ; AllocateCMacStub -> CMac (size=0x14)
    // 0x8c754c: mov             x1, x0
    // 0x8c7550: ldur            x2, [fp, #-8]
    // 0x8c7554: stur            x0, [fp, #-8]
    // 0x8c7558: r0 = CMac.fromCipher()
    //     0x8c7558: bl              #0x8c7578  ; [package:pointycastle/macs/cmac.dart] CMac::CMac.fromCipher
    // 0x8c755c: ldur            x0, [fp, #-8]
    // 0x8c7560: LeaveFrame
    //     0x8c7560: mov             SP, fp
    //     0x8c7564: ldp             fp, lr, [SP], #0x10
    // 0x8c7568: ret
    //     0x8c7568: ret             
    // 0x8c756c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c756c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7570: b               #0x8c74d0
    // 0x8c7574: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c7574: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ CMac.fromCipher(/* No info */) {
    // ** addr: 0x8c7578, size: 0x64
    // 0x8c7578: EnterFrame
    //     0x8c7578: stp             fp, lr, [SP, #-0x10]!
    //     0x8c757c: mov             fp, SP
    // 0x8c7580: AllocStack(0x10)
    //     0x8c7580: sub             SP, SP, #0x10
    // 0x8c7584: SetupParameters(CMac this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8c7584: mov             x3, x1
    //     0x8c7588: stur            x1, [fp, #-8]
    //     0x8c758c: stur            x2, [fp, #-0x10]
    // 0x8c7590: CheckStackOverflow
    //     0x8c7590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7594: cmp             SP, x16
    //     0x8c7598: b.ls            #0x8c75d4
    // 0x8c759c: r0 = LoadClassIdInstr(r2)
    //     0x8c759c: ldur            x0, [x2, #-1]
    //     0x8c75a0: ubfx            x0, x0, #0xc, #0x14
    // 0x8c75a4: mov             x1, x2
    // 0x8c75a8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c75a8: sub             lr, x0, #1, lsl #12
    //     0x8c75ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8c75b0: blr             lr
    // 0x8c75b4: lsl             x3, x0, #3
    // 0x8c75b8: ldur            x1, [fp, #-8]
    // 0x8c75bc: ldur            x2, [fp, #-0x10]
    // 0x8c75c0: r0 = CMac()
    //     0x8c75c0: bl              #0x8c44d8  ; [package:pointycastle/macs/cmac.dart] CMac::CMac
    // 0x8c75c4: r0 = Null
    //     0x8c75c4: mov             x0, NULL
    // 0x8c75c8: LeaveFrame
    //     0x8c75c8: mov             SP, fp
    //     0x8c75cc: ldp             fp, lr, [SP], #0x10
    // 0x8c75d0: ret
    //     0x8c75d0: ret             
    // 0x8c75d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c75d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c75d8: b               #0x8c759c
  }
}
