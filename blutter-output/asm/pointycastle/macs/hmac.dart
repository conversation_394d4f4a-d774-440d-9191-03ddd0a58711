// lib: impl.mac.hmac, url: package:pointycastle/macs/hmac.dart

// class id: 1051018, size: 0x8
class :: {
}

// class id: 576, size: 0x14, field offset: 0x8
class HMac extends BaseMac {

  static late final FactoryConfig factoryConfig; // offset: 0xe5c
  late int _digestSize; // offset: 0xc

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c75dc, size: 0x64
    // 0x8c75dc: EnterFrame
    //     0x8c75dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c75e0: mov             fp, SP
    // 0x8c75e4: AllocStack(0x8)
    //     0x8c75e4: sub             SP, SP, #8
    // 0x8c75e8: CheckStackOverflow
    //     0x8c75e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c75ec: cmp             SP, x16
    //     0x8c75f0: b.ls            #0x8c7638
    // 0x8c75f4: r0 = DynamicFactoryConfig()
    //     0x8c75f4: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c75f8: r1 = Function '<anonymous closure>': static.
    //     0x8c75f8: add             x1, PP, #0x18, lsl #12  ; [pp+0x183e8] AnonymousClosure: static (0x8c7640), in [package:pointycastle/macs/hmac.dart] HMac::factoryConfig (0x8c75dc)
    //     0x8c75fc: ldr             x1, [x1, #0x3e8]
    // 0x8c7600: r2 = Null
    //     0x8c7600: mov             x2, NULL
    // 0x8c7604: stur            x0, [fp, #-8]
    // 0x8c7608: r0 = AllocateClosure()
    //     0x8c7608: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c760c: ldur            x1, [fp, #-8]
    // 0x8c7610: mov             x5, x0
    // 0x8c7614: r2 = Mac
    //     0x8c7614: add             x2, PP, #0x18, lsl #12  ; [pp+0x18398] Type: Mac
    //     0x8c7618: ldr             x2, [x2, #0x398]
    // 0x8c761c: r3 = "/HMAC"
    //     0x8c761c: add             x3, PP, #0x18, lsl #12  ; [pp+0x182a0] "/HMAC"
    //     0x8c7620: ldr             x3, [x3, #0x2a0]
    // 0x8c7624: r0 = DynamicFactoryConfig.suffix()
    //     0x8c7624: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c7628: ldur            x0, [fp, #-8]
    // 0x8c762c: LeaveFrame
    //     0x8c762c: mov             SP, fp
    //     0x8c7630: ldp             fp, lr, [SP], #0x10
    // 0x8c7634: ret
    //     0x8c7634: ret             
    // 0x8c7638: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7638: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c763c: b               #0x8c75f4
  }
  [closure] static (dynamic) => HMac <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c7640, size: 0x9c
    // 0x8c7640: EnterFrame
    //     0x8c7640: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7644: mov             fp, SP
    // 0x8c7648: AllocStack(0x10)
    //     0x8c7648: sub             SP, SP, #0x10
    // 0x8c764c: SetupParameters()
    //     0x8c764c: ldr             x0, [fp, #0x20]
    //     0x8c7650: ldur            w1, [x0, #0x17]
    //     0x8c7654: add             x1, x1, HEAP, lsl #32
    //     0x8c7658: stur            x1, [fp, #-8]
    // 0x8c765c: CheckStackOverflow
    //     0x8c765c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7660: cmp             SP, x16
    //     0x8c7664: b.ls            #0x8c76d4
    // 0x8c7668: r1 = 1
    //     0x8c7668: movz            x1, #0x1
    // 0x8c766c: r0 = AllocateContext()
    //     0x8c766c: bl              #0xec126c  ; AllocateContextStub
    // 0x8c7670: mov             x3, x0
    // 0x8c7674: ldur            x0, [fp, #-8]
    // 0x8c7678: stur            x3, [fp, #-0x10]
    // 0x8c767c: StoreField: r3->field_b = r0
    //     0x8c767c: stur            w0, [x3, #0xb]
    // 0x8c7680: ldr             x1, [fp, #0x10]
    // 0x8c7684: r0 = LoadClassIdInstr(r1)
    //     0x8c7684: ldur            x0, [x1, #-1]
    //     0x8c7688: ubfx            x0, x0, #0xc, #0x14
    // 0x8c768c: r2 = 1
    //     0x8c768c: movz            x2, #0x1
    // 0x8c7690: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c7690: sub             lr, x0, #0xfdd
    //     0x8c7694: ldr             lr, [x21, lr, lsl #3]
    //     0x8c7698: blr             lr
    // 0x8c769c: ldur            x2, [fp, #-0x10]
    // 0x8c76a0: StoreField: r2->field_f = r0
    //     0x8c76a0: stur            w0, [x2, #0xf]
    //     0x8c76a4: ldurb           w16, [x2, #-1]
    //     0x8c76a8: ldurb           w17, [x0, #-1]
    //     0x8c76ac: and             x16, x17, x16, lsr #2
    //     0x8c76b0: tst             x16, HEAP, lsr #32
    //     0x8c76b4: b.eq            #0x8c76bc
    //     0x8c76b8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c76bc: r1 = Function '<anonymous closure>': static.
    //     0x8c76bc: add             x1, PP, #0x18, lsl #12  ; [pp+0x183f0] AnonymousClosure: static (0x8c76dc), in [package:pointycastle/macs/hmac.dart] HMac::factoryConfig (0x8c75dc)
    //     0x8c76c0: ldr             x1, [x1, #0x3f0]
    // 0x8c76c4: r0 = AllocateClosure()
    //     0x8c76c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c76c8: LeaveFrame
    //     0x8c76c8: mov             SP, fp
    //     0x8c76cc: ldp             fp, lr, [SP], #0x10
    // 0x8c76d0: ret
    //     0x8c76d0: ret             
    // 0x8c76d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c76d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c76d8: b               #0x8c7668
  }
  [closure] static HMac <anonymous closure>(dynamic) {
    // ** addr: 0x8c76dc, size: 0xa8
    // 0x8c76dc: EnterFrame
    //     0x8c76dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c76e0: mov             fp, SP
    // 0x8c76e4: AllocStack(0x20)
    //     0x8c76e4: sub             SP, SP, #0x20
    // 0x8c76e8: SetupParameters()
    //     0x8c76e8: ldr             x0, [fp, #0x10]
    //     0x8c76ec: ldur            w1, [x0, #0x17]
    //     0x8c76f0: add             x1, x1, HEAP, lsl #32
    // 0x8c76f4: CheckStackOverflow
    //     0x8c76f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c76f8: cmp             SP, x16
    //     0x8c76fc: b.ls            #0x8c7778
    // 0x8c7700: LoadField: r0 = r1->field_f
    //     0x8c7700: ldur            w0, [x1, #0xf]
    // 0x8c7704: DecompressPointer r0
    //     0x8c7704: add             x0, x0, HEAP, lsl #32
    // 0x8c7708: stur            x0, [fp, #-8]
    // 0x8c770c: cmp             w0, NULL
    // 0x8c7710: b.eq            #0x8c7780
    // 0x8c7714: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c7714: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7718: ldr             x0, [x0, #0x2e38]
    //     0x8c771c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7720: cmp             w0, w16
    //     0x8c7724: b.ne            #0x8c7734
    //     0x8c7728: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c772c: ldr             x2, [x2, #0xf80]
    //     0x8c7730: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7734: r16 = <Digest>
    //     0x8c7734: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b8] TypeArguments: <Digest>
    //     0x8c7738: ldr             x16, [x16, #0x1b8]
    // 0x8c773c: stp             x0, x16, [SP, #8]
    // 0x8c7740: ldur            x16, [fp, #-8]
    // 0x8c7744: str             x16, [SP]
    // 0x8c7748: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c7748: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c774c: r0 = create()
    //     0x8c774c: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c7750: stur            x0, [fp, #-8]
    // 0x8c7754: r0 = HMac()
    //     0x8c7754: bl              #0x8c78e4  ; AllocateHMacStub -> HMac (size=0x14)
    // 0x8c7758: mov             x1, x0
    // 0x8c775c: ldur            x2, [fp, #-8]
    // 0x8c7760: stur            x0, [fp, #-8]
    // 0x8c7764: r0 = HMac.withDigest()
    //     0x8c7764: bl              #0x8c7784  ; [package:pointycastle/macs/hmac.dart] HMac::HMac.withDigest
    // 0x8c7768: ldur            x0, [fp, #-8]
    // 0x8c776c: LeaveFrame
    //     0x8c776c: mov             SP, fp
    //     0x8c7770: ldp             fp, lr, [SP], #0x10
    // 0x8c7774: ret
    //     0x8c7774: ret             
    // 0x8c7778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7778: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c777c: b               #0x8c7700
    // 0x8c7780: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c7780: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ HMac.withDigest(/* No info */) {
    // ** addr: 0x8c7784, size: 0x160
    // 0x8c7784: EnterFrame
    //     0x8c7784: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7788: mov             fp, SP
    // 0x8c778c: AllocStack(0x18)
    //     0x8c778c: sub             SP, SP, #0x18
    // 0x8c7790: r0 = Sentinel
    //     0x8c7790: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c7794: mov             x3, x1
    // 0x8c7798: stur            x1, [fp, #-8]
    // 0x8c779c: stur            x2, [fp, #-0x10]
    // 0x8c77a0: CheckStackOverflow
    //     0x8c77a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c77a4: cmp             SP, x16
    //     0x8c77a8: b.ls            #0x8c78dc
    // 0x8c77ac: StoreField: r3->field_b = r0
    //     0x8c77ac: stur            w0, [x3, #0xb]
    // 0x8c77b0: StoreField: r3->field_f = r0
    //     0x8c77b0: stur            w0, [x3, #0xf]
    // 0x8c77b4: mov             x0, x2
    // 0x8c77b8: StoreField: r3->field_7 = r0
    //     0x8c77b8: stur            w0, [x3, #7]
    //     0x8c77bc: ldurb           w16, [x3, #-1]
    //     0x8c77c0: ldurb           w17, [x0, #-1]
    //     0x8c77c4: and             x16, x17, x16, lsr #2
    //     0x8c77c8: tst             x16, HEAP, lsr #32
    //     0x8c77cc: b.eq            #0x8c77d4
    //     0x8c77d0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8c77d4: r0 = LoadClassIdInstr(r2)
    //     0x8c77d4: ldur            x0, [x2, #-1]
    //     0x8c77d8: ubfx            x0, x0, #0xc, #0x14
    // 0x8c77dc: mov             x1, x2
    // 0x8c77e0: r0 = GDT[cid_x0 + 0xf047]()
    //     0x8c77e0: movz            x17, #0xf047
    //     0x8c77e4: add             lr, x0, x17
    //     0x8c77e8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c77ec: blr             lr
    // 0x8c77f0: mov             x2, x0
    // 0x8c77f4: r0 = BoxInt64Instr(r2)
    //     0x8c77f4: sbfiz           x0, x2, #1, #0x1f
    //     0x8c77f8: cmp             x2, x0, asr #1
    //     0x8c77fc: b.eq            #0x8c7808
    //     0x8c7800: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c7804: stur            x2, [x0, #7]
    // 0x8c7808: ldur            x2, [fp, #-8]
    // 0x8c780c: StoreField: r2->field_f = r0
    //     0x8c780c: stur            w0, [x2, #0xf]
    //     0x8c7810: tbz             w0, #0, #0x8c782c
    //     0x8c7814: ldurb           w16, [x2, #-1]
    //     0x8c7818: ldurb           w17, [x0, #-1]
    //     0x8c781c: and             x16, x17, x16, lsr #2
    //     0x8c7820: tst             x16, HEAP, lsr #32
    //     0x8c7824: b.eq            #0x8c782c
    //     0x8c7828: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c782c: ldur            x1, [fp, #-0x10]
    // 0x8c7830: r0 = LoadClassIdInstr(r1)
    //     0x8c7830: ldur            x0, [x1, #-1]
    //     0x8c7834: ubfx            x0, x0, #0xc, #0x14
    // 0x8c7838: r0 = GDT[cid_x0 + -0xdc0]()
    //     0x8c7838: sub             lr, x0, #0xdc0
    //     0x8c783c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c7840: blr             lr
    // 0x8c7844: mov             x2, x0
    // 0x8c7848: stur            x2, [fp, #-0x18]
    // 0x8c784c: r0 = BoxInt64Instr(r2)
    //     0x8c784c: sbfiz           x0, x2, #1, #0x1f
    //     0x8c7850: cmp             x2, x0, asr #1
    //     0x8c7854: b.eq            #0x8c7860
    //     0x8c7858: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c785c: stur            x2, [x0, #7]
    // 0x8c7860: ldur            x1, [fp, #-8]
    // 0x8c7864: StoreField: r1->field_b = r0
    //     0x8c7864: stur            w0, [x1, #0xb]
    //     0x8c7868: tbz             w0, #0, #0x8c7884
    //     0x8c786c: ldurb           w16, [x1, #-1]
    //     0x8c7870: ldurb           w17, [x0, #-1]
    //     0x8c7874: and             x16, x17, x16, lsr #2
    //     0x8c7878: tst             x16, HEAP, lsr #32
    //     0x8c787c: b.eq            #0x8c7884
    //     0x8c7880: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c7884: LoadField: r0 = r1->field_f
    //     0x8c7884: ldur            w0, [x1, #0xf]
    // 0x8c7888: DecompressPointer r0
    //     0x8c7888: add             x0, x0, HEAP, lsl #32
    // 0x8c788c: mov             x4, x0
    // 0x8c7890: stur            x0, [fp, #-0x10]
    // 0x8c7894: r0 = AllocateUint8Array()
    //     0x8c7894: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c7898: ldur            x0, [fp, #-0x10]
    // 0x8c789c: r1 = LoadInt32Instr(r0)
    //     0x8c789c: sbfx            x1, x0, #1, #0x1f
    //     0x8c78a0: tbz             w0, #0, #0x8c78a8
    //     0x8c78a4: ldur            x1, [x0, #7]
    // 0x8c78a8: ldur            x0, [fp, #-0x18]
    // 0x8c78ac: add             x2, x1, x0
    // 0x8c78b0: r0 = BoxInt64Instr(r2)
    //     0x8c78b0: sbfiz           x0, x2, #1, #0x1f
    //     0x8c78b4: cmp             x2, x0, asr #1
    //     0x8c78b8: b.eq            #0x8c78c4
    //     0x8c78bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c78c0: stur            x2, [x0, #7]
    // 0x8c78c4: mov             x4, x0
    // 0x8c78c8: r0 = AllocateUint8Array()
    //     0x8c78c8: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c78cc: r0 = Null
    //     0x8c78cc: mov             x0, NULL
    // 0x8c78d0: LeaveFrame
    //     0x8c78d0: mov             SP, fp
    //     0x8c78d4: ldp             fp, lr, [SP], #0x10
    // 0x8c78d8: ret
    //     0x8c78d8: ret             
    // 0x8c78dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c78dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c78e0: b               #0x8c77ac
  }
  _ HMac(/* No info */) {
    // ** addr: 0x8c84c8, size: 0x134
    // 0x8c84c8: EnterFrame
    //     0x8c84c8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c84cc: mov             fp, SP
    // 0x8c84d0: AllocStack(0x18)
    //     0x8c84d0: sub             SP, SP, #0x18
    // 0x8c84d4: r0 = Sentinel
    //     0x8c84d4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c84d8: mov             x4, x1
    // 0x8c84dc: stur            x1, [fp, #-8]
    // 0x8c84e0: CheckStackOverflow
    //     0x8c84e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c84e4: cmp             SP, x16
    //     0x8c84e8: b.ls            #0x8c85f4
    // 0x8c84ec: StoreField: r4->field_b = r0
    //     0x8c84ec: stur            w0, [x4, #0xb]
    // 0x8c84f0: mov             x0, x2
    // 0x8c84f4: StoreField: r4->field_7 = r0
    //     0x8c84f4: stur            w0, [x4, #7]
    //     0x8c84f8: ldurb           w16, [x4, #-1]
    //     0x8c84fc: ldurb           w17, [x0, #-1]
    //     0x8c8500: and             x16, x17, x16, lsr #2
    //     0x8c8504: tst             x16, HEAP, lsr #32
    //     0x8c8508: b.eq            #0x8c8510
    //     0x8c850c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8c8510: r0 = BoxInt64Instr(r3)
    //     0x8c8510: sbfiz           x0, x3, #1, #0x1f
    //     0x8c8514: cmp             x3, x0, asr #1
    //     0x8c8518: b.eq            #0x8c8524
    //     0x8c851c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c8520: stur            x3, [x0, #7]
    // 0x8c8524: StoreField: r4->field_f = r0
    //     0x8c8524: stur            w0, [x4, #0xf]
    //     0x8c8528: tbz             w0, #0, #0x8c8544
    //     0x8c852c: ldurb           w16, [x4, #-1]
    //     0x8c8530: ldurb           w17, [x0, #-1]
    //     0x8c8534: and             x16, x17, x16, lsr #2
    //     0x8c8538: tst             x16, HEAP, lsr #32
    //     0x8c853c: b.eq            #0x8c8544
    //     0x8c8540: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8c8544: r0 = LoadClassIdInstr(r2)
    //     0x8c8544: ldur            x0, [x2, #-1]
    //     0x8c8548: ubfx            x0, x0, #0xc, #0x14
    // 0x8c854c: mov             x1, x2
    // 0x8c8550: r0 = GDT[cid_x0 + -0xdc0]()
    //     0x8c8550: sub             lr, x0, #0xdc0
    //     0x8c8554: ldr             lr, [x21, lr, lsl #3]
    //     0x8c8558: blr             lr
    // 0x8c855c: mov             x2, x0
    // 0x8c8560: stur            x2, [fp, #-0x18]
    // 0x8c8564: r0 = BoxInt64Instr(r2)
    //     0x8c8564: sbfiz           x0, x2, #1, #0x1f
    //     0x8c8568: cmp             x2, x0, asr #1
    //     0x8c856c: b.eq            #0x8c8578
    //     0x8c8570: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c8574: stur            x2, [x0, #7]
    // 0x8c8578: ldur            x1, [fp, #-8]
    // 0x8c857c: StoreField: r1->field_b = r0
    //     0x8c857c: stur            w0, [x1, #0xb]
    //     0x8c8580: tbz             w0, #0, #0x8c859c
    //     0x8c8584: ldurb           w16, [x1, #-1]
    //     0x8c8588: ldurb           w17, [x0, #-1]
    //     0x8c858c: and             x16, x17, x16, lsr #2
    //     0x8c8590: tst             x16, HEAP, lsr #32
    //     0x8c8594: b.eq            #0x8c859c
    //     0x8c8598: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c859c: LoadField: r0 = r1->field_f
    //     0x8c859c: ldur            w0, [x1, #0xf]
    // 0x8c85a0: DecompressPointer r0
    //     0x8c85a0: add             x0, x0, HEAP, lsl #32
    // 0x8c85a4: mov             x4, x0
    // 0x8c85a8: stur            x0, [fp, #-0x10]
    // 0x8c85ac: r0 = AllocateUint8Array()
    //     0x8c85ac: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c85b0: ldur            x0, [fp, #-0x10]
    // 0x8c85b4: r1 = LoadInt32Instr(r0)
    //     0x8c85b4: sbfx            x1, x0, #1, #0x1f
    //     0x8c85b8: tbz             w0, #0, #0x8c85c0
    //     0x8c85bc: ldur            x1, [x0, #7]
    // 0x8c85c0: ldur            x0, [fp, #-0x18]
    // 0x8c85c4: add             x2, x1, x0
    // 0x8c85c8: r0 = BoxInt64Instr(r2)
    //     0x8c85c8: sbfiz           x0, x2, #1, #0x1f
    //     0x8c85cc: cmp             x2, x0, asr #1
    //     0x8c85d0: b.eq            #0x8c85dc
    //     0x8c85d4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c85d8: stur            x2, [x0, #7]
    // 0x8c85dc: mov             x4, x0
    // 0x8c85e0: r0 = AllocateUint8Array()
    //     0x8c85e0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c85e4: r0 = Null
    //     0x8c85e4: mov             x0, NULL
    // 0x8c85e8: LeaveFrame
    //     0x8c85e8: mov             SP, fp
    //     0x8c85ec: ldp             fp, lr, [SP], #0x10
    // 0x8c85f0: ret
    //     0x8c85f0: ret             
    // 0x8c85f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c85f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c85f8: b               #0x8c84ec
  }
  get _ macSize(/* No info */) {
    // ** addr: 0xeb5db4, size: 0x38
    // 0xeb5db4: LoadField: r2 = r1->field_b
    //     0xeb5db4: ldur            w2, [x1, #0xb]
    // 0xeb5db8: DecompressPointer r2
    //     0xeb5db8: add             x2, x2, HEAP, lsl #32
    // 0xeb5dbc: r16 = Sentinel
    //     0xeb5dbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb5dc0: cmp             w2, w16
    // 0xeb5dc4: b.eq            #0xeb5dd8
    // 0xeb5dc8: r0 = LoadInt32Instr(r2)
    //     0xeb5dc8: sbfx            x0, x2, #1, #0x1f
    //     0xeb5dcc: tbz             w2, #0, #0xeb5dd4
    //     0xeb5dd0: ldur            x0, [x2, #7]
    // 0xeb5dd4: ret
    //     0xeb5dd4: ret             
    // 0xeb5dd8: EnterFrame
    //     0xeb5dd8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5ddc: mov             fp, SP
    // 0xeb5de0: r9 = _digestSize
    //     0xeb5de0: add             x9, PP, #0x18, lsl #12  ; [pp+0x18500] Field <HMac._digestSize@948404795>: late (offset: 0xc)
    //     0xeb5de4: ldr             x9, [x9, #0x500]
    // 0xeb5de8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb5de8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
