// lib: impl.mac.poly1305, url: package:pointycastle/macs/poly1305.dart

// class id: 1051019, size: 0x8
class :: {
}

// class id: 575, size: 0xc, field offset: 0x8
class Poly1305 extends BaseMac {

  static late final FactoryConfig factoryConfig; // offset: 0xe64

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c6d98, size: 0x64
    // 0x8c6d98: EnterFrame
    //     0x8c6d98: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6d9c: mov             fp, SP
    // 0x8c6da0: AllocStack(0x8)
    //     0x8c6da0: sub             SP, SP, #8
    // 0x8c6da4: CheckStackOverflow
    //     0x8c6da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6da8: cmp             SP, x16
    //     0x8c6dac: b.ls            #0x8c6df4
    // 0x8c6db0: r0 = DynamicFactoryConfig()
    //     0x8c6db0: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c6db4: r1 = Function '<anonymous closure>': static.
    //     0x8c6db4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18390] AnonymousClosure: static (0x8c6dfc), in [package:pointycastle/macs/poly1305.dart] Poly1305::factoryConfig (0x8c6d98)
    //     0x8c6db8: ldr             x1, [x1, #0x390]
    // 0x8c6dbc: r2 = Null
    //     0x8c6dbc: mov             x2, NULL
    // 0x8c6dc0: stur            x0, [fp, #-8]
    // 0x8c6dc4: r0 = AllocateClosure()
    //     0x8c6dc4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c6dc8: ldur            x1, [fp, #-8]
    // 0x8c6dcc: mov             x5, x0
    // 0x8c6dd0: r2 = Mac
    //     0x8c6dd0: add             x2, PP, #0x18, lsl #12  ; [pp+0x18398] Type: Mac
    //     0x8c6dd4: ldr             x2, [x2, #0x398]
    // 0x8c6dd8: r3 = "/Poly1305"
    //     0x8c6dd8: add             x3, PP, #0x18, lsl #12  ; [pp+0x183a0] "/Poly1305"
    //     0x8c6ddc: ldr             x3, [x3, #0x3a0]
    // 0x8c6de0: r0 = DynamicFactoryConfig.suffix()
    //     0x8c6de0: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c6de4: ldur            x0, [fp, #-8]
    // 0x8c6de8: LeaveFrame
    //     0x8c6de8: mov             SP, fp
    //     0x8c6dec: ldp             fp, lr, [SP], #0x10
    // 0x8c6df0: ret
    //     0x8c6df0: ret             
    // 0x8c6df4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6df4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6df8: b               #0x8c6db0
  }
  [closure] static (dynamic) => Poly1305 <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c6dfc, size: 0x54
    // 0x8c6dfc: EnterFrame
    //     0x8c6dfc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6e00: mov             fp, SP
    // 0x8c6e04: AllocStack(0x8)
    //     0x8c6e04: sub             SP, SP, #8
    // 0x8c6e08: SetupParameters()
    //     0x8c6e08: ldr             x0, [fp, #0x20]
    //     0x8c6e0c: ldur            w1, [x0, #0x17]
    //     0x8c6e10: add             x1, x1, HEAP, lsl #32
    //     0x8c6e14: stur            x1, [fp, #-8]
    // 0x8c6e18: r1 = 1
    //     0x8c6e18: movz            x1, #0x1
    // 0x8c6e1c: r0 = AllocateContext()
    //     0x8c6e1c: bl              #0xec126c  ; AllocateContextStub
    // 0x8c6e20: mov             x1, x0
    // 0x8c6e24: ldur            x0, [fp, #-8]
    // 0x8c6e28: StoreField: r1->field_b = r0
    //     0x8c6e28: stur            w0, [x1, #0xb]
    // 0x8c6e2c: ldr             x0, [fp, #0x10]
    // 0x8c6e30: StoreField: r1->field_f = r0
    //     0x8c6e30: stur            w0, [x1, #0xf]
    // 0x8c6e34: mov             x2, x1
    // 0x8c6e38: r1 = Function '<anonymous closure>': static.
    //     0x8c6e38: add             x1, PP, #0x18, lsl #12  ; [pp+0x183a8] AnonymousClosure: static (0x8c6e50), in [package:pointycastle/macs/poly1305.dart] Poly1305::factoryConfig (0x8c6d98)
    //     0x8c6e3c: ldr             x1, [x1, #0x3a8]
    // 0x8c6e40: r0 = AllocateClosure()
    //     0x8c6e40: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c6e44: LeaveFrame
    //     0x8c6e44: mov             SP, fp
    //     0x8c6e48: ldp             fp, lr, [SP], #0x10
    // 0x8c6e4c: ret
    //     0x8c6e4c: ret             
  }
  [closure] static Poly1305 <anonymous closure>(dynamic) {
    // ** addr: 0x8c6e50, size: 0xcc
    // 0x8c6e50: EnterFrame
    //     0x8c6e50: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6e54: mov             fp, SP
    // 0x8c6e58: AllocStack(0x20)
    //     0x8c6e58: sub             SP, SP, #0x20
    // 0x8c6e5c: SetupParameters()
    //     0x8c6e5c: ldr             x0, [fp, #0x10]
    //     0x8c6e60: ldur            w1, [x0, #0x17]
    //     0x8c6e64: add             x1, x1, HEAP, lsl #32
    // 0x8c6e68: CheckStackOverflow
    //     0x8c6e68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6e6c: cmp             SP, x16
    //     0x8c6e70: b.ls            #0x8c6f10
    // 0x8c6e74: LoadField: r0 = r1->field_f
    //     0x8c6e74: ldur            w0, [x1, #0xf]
    // 0x8c6e78: DecompressPointer r0
    //     0x8c6e78: add             x0, x0, HEAP, lsl #32
    // 0x8c6e7c: r1 = LoadClassIdInstr(r0)
    //     0x8c6e7c: ldur            x1, [x0, #-1]
    //     0x8c6e80: ubfx            x1, x1, #0xc, #0x14
    // 0x8c6e84: mov             x16, x0
    // 0x8c6e88: mov             x0, x1
    // 0x8c6e8c: mov             x1, x16
    // 0x8c6e90: r2 = 1
    //     0x8c6e90: movz            x2, #0x1
    // 0x8c6e94: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c6e94: sub             lr, x0, #0xfdd
    //     0x8c6e98: ldr             lr, [x21, lr, lsl #3]
    //     0x8c6e9c: blr             lr
    // 0x8c6ea0: stur            x0, [fp, #-8]
    // 0x8c6ea4: cmp             w0, NULL
    // 0x8c6ea8: b.eq            #0x8c6f18
    // 0x8c6eac: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c6eac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6eb0: ldr             x0, [x0, #0x2e38]
    //     0x8c6eb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6eb8: cmp             w0, w16
    //     0x8c6ebc: b.ne            #0x8c6ecc
    //     0x8c6ec0: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c6ec4: ldr             x2, [x2, #0xf80]
    //     0x8c6ec8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6ecc: r16 = <BlockCipher>
    //     0x8c6ecc: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8c6ed0: ldr             x16, [x16, #0x88]
    // 0x8c6ed4: stp             x0, x16, [SP, #8]
    // 0x8c6ed8: ldur            x16, [fp, #-8]
    // 0x8c6edc: str             x16, [SP]
    // 0x8c6ee0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c6ee0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c6ee4: r0 = create()
    //     0x8c6ee4: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c6ee8: stur            x0, [fp, #-8]
    // 0x8c6eec: r0 = Poly1305()
    //     0x8c6eec: bl              #0x8c6fec  ; AllocatePoly1305Stub -> Poly1305 (size=0xc)
    // 0x8c6ef0: mov             x1, x0
    // 0x8c6ef4: ldur            x2, [fp, #-8]
    // 0x8c6ef8: stur            x0, [fp, #-8]
    // 0x8c6efc: r0 = Poly1305.withCipher()
    //     0x8c6efc: bl              #0x8c6f1c  ; [package:pointycastle/macs/poly1305.dart] Poly1305::Poly1305.withCipher
    // 0x8c6f00: ldur            x0, [fp, #-8]
    // 0x8c6f04: LeaveFrame
    //     0x8c6f04: mov             SP, fp
    //     0x8c6f08: ldp             fp, lr, [SP], #0x10
    // 0x8c6f0c: ret
    //     0x8c6f0c: ret             
    // 0x8c6f10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6f10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6f14: b               #0x8c6e74
    // 0x8c6f18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c6f18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ Poly1305.withCipher(/* No info */) {
    // ** addr: 0x8c6f1c, size: 0xd0
    // 0x8c6f1c: EnterFrame
    //     0x8c6f1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6f20: mov             fp, SP
    // 0x8c6f24: AllocStack(0x8)
    //     0x8c6f24: sub             SP, SP, #8
    // 0x8c6f28: SetupParameters(Poly1305 this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0 */)
    //     0x8c6f28: mov             x0, x2
    //     0x8c6f2c: stur            x1, [fp, #-8]
    // 0x8c6f30: CheckStackOverflow
    //     0x8c6f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6f34: cmp             SP, x16
    //     0x8c6f38: b.ls            #0x8c6fe0
    // 0x8c6f3c: StoreField: r1->field_7 = r0
    //     0x8c6f3c: stur            w0, [x1, #7]
    //     0x8c6f40: ldurb           w16, [x1, #-1]
    //     0x8c6f44: ldurb           w17, [x0, #-1]
    //     0x8c6f48: and             x16, x17, x16, lsr #2
    //     0x8c6f4c: tst             x16, HEAP, lsr #32
    //     0x8c6f50: b.eq            #0x8c6f58
    //     0x8c6f54: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c6f58: r0 = InitLateStaticField(0x1734) // [package:pointycastle/src/platform_check/native.dart] PlatformIO::instance
    //     0x8c6f58: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6f5c: ldr             x0, [x0, #0x2e68]
    //     0x8c6f60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6f64: cmp             w0, w16
    //     0x8c6f68: b.ne            #0x8c6f78
    //     0x8c6f6c: add             x2, PP, #0x18, lsl #12  ; [pp+0x180f8] Field <PlatformIO.instance>: static late final (offset: 0x1734)
    //     0x8c6f70: ldr             x2, [x2, #0xf8]
    //     0x8c6f74: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6f78: ldur            x0, [fp, #-8]
    // 0x8c6f7c: LoadField: r1 = r0->field_7
    //     0x8c6f7c: ldur            w1, [x0, #7]
    // 0x8c6f80: DecompressPointer r1
    //     0x8c6f80: add             x1, x1, HEAP, lsl #32
    // 0x8c6f84: cmp             w1, NULL
    // 0x8c6f88: b.eq            #0x8c6fe8
    // 0x8c6f8c: r0 = LoadClassIdInstr(r1)
    //     0x8c6f8c: ldur            x0, [x1, #-1]
    //     0x8c6f90: ubfx            x0, x0, #0xc, #0x14
    // 0x8c6f94: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c6f94: sub             lr, x0, #1, lsl #12
    //     0x8c6f98: ldr             lr, [x21, lr, lsl #3]
    //     0x8c6f9c: blr             lr
    // 0x8c6fa0: cmp             x0, #0x10
    // 0x8c6fa4: b.ne            #0x8c6fb8
    // 0x8c6fa8: r0 = Null
    //     0x8c6fa8: mov             x0, NULL
    // 0x8c6fac: LeaveFrame
    //     0x8c6fac: mov             SP, fp
    //     0x8c6fb0: ldp             fp, lr, [SP], #0x10
    // 0x8c6fb4: ret
    //     0x8c6fb4: ret             
    // 0x8c6fb8: r0 = ArgumentError()
    //     0x8c6fb8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8c6fbc: mov             x1, x0
    // 0x8c6fc0: r0 = "Poly1305 requires a 128 bit block cipher."
    //     0x8c6fc0: add             x0, PP, #0x18, lsl #12  ; [pp+0x183b0] "Poly1305 requires a 128 bit block cipher."
    //     0x8c6fc4: ldr             x0, [x0, #0x3b0]
    // 0x8c6fc8: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c6fc8: stur            w0, [x1, #0x17]
    // 0x8c6fcc: r0 = false
    //     0x8c6fcc: add             x0, NULL, #0x30  ; false
    // 0x8c6fd0: StoreField: r1->field_b = r0
    //     0x8c6fd0: stur            w0, [x1, #0xb]
    // 0x8c6fd4: mov             x0, x1
    // 0x8c6fd8: r0 = Throw()
    //     0x8c6fd8: bl              #0xec04b8  ; ThrowStub
    // 0x8c6fdc: brk             #0
    // 0x8c6fe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6fe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6fe4: b               #0x8c6f3c
    // 0x8c6fe8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c6fe8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ macSize(/* No info */) {
    // ** addr: 0xeb5dec, size: 0x8
    // 0xeb5dec: r0 = 16
    //     0xeb5dec: movz            x0, #0x10
    // 0xeb5df0: ret
    //     0xeb5df0: ret             
  }
}
