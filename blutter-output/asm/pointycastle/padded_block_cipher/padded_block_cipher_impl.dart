// lib: impl.padded_block_cipher.padded_block_cipher_impl, url: package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart

// class id: 1051020, size: 0x8
class :: {
}

// class id: 573, size: 0x14, field offset: 0x8
class PaddedBlockCipherImpl extends Object
    implements PaddedBlockCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xe6c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c6a50, size: 0x98
    // 0x8c6a50: EnterFrame
    //     0x8c6a50: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6a54: mov             fp, SP
    // 0x8c6a58: AllocStack(0x40)
    //     0x8c6a58: sub             SP, SP, #0x40
    // 0x8c6a5c: CheckStackOverflow
    //     0x8c6a5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6a60: cmp             SP, x16
    //     0x8c6a64: b.ls            #0x8c6ae0
    // 0x8c6a68: r16 = "^(.+)/([^/]+)$"
    //     0x8c6a68: add             x16, PP, #0x18, lsl #12  ; [pp+0x18348] "^(.+)/([^/]+)$"
    //     0x8c6a6c: ldr             x16, [x16, #0x348]
    // 0x8c6a70: stp             x16, NULL, [SP, #0x20]
    // 0x8c6a74: r16 = false
    //     0x8c6a74: add             x16, NULL, #0x30  ; false
    // 0x8c6a78: r30 = true
    //     0x8c6a78: add             lr, NULL, #0x20  ; true
    // 0x8c6a7c: stp             lr, x16, [SP, #0x10]
    // 0x8c6a80: r16 = false
    //     0x8c6a80: add             x16, NULL, #0x30  ; false
    // 0x8c6a84: r30 = false
    //     0x8c6a84: add             lr, NULL, #0x30  ; false
    // 0x8c6a88: stp             lr, x16, [SP]
    // 0x8c6a8c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8c6a8c: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8c6a90: r0 = _RegExp()
    //     0x8c6a90: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8c6a94: stur            x0, [fp, #-8]
    // 0x8c6a98: r0 = DynamicFactoryConfig()
    //     0x8c6a98: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c6a9c: mov             x3, x0
    // 0x8c6aa0: ldur            x0, [fp, #-8]
    // 0x8c6aa4: stur            x3, [fp, #-0x10]
    // 0x8c6aa8: StoreField: r3->field_b = r0
    //     0x8c6aa8: stur            w0, [x3, #0xb]
    // 0x8c6aac: r1 = Function '<anonymous closure>': static.
    //     0x8c6aac: add             x1, PP, #0x18, lsl #12  ; [pp+0x18350] AnonymousClosure: static (0x8c6ae8), in [package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart] PaddedBlockCipherImpl::factoryConfig (0x8c6a50)
    //     0x8c6ab0: ldr             x1, [x1, #0x350]
    // 0x8c6ab4: r2 = Null
    //     0x8c6ab4: mov             x2, NULL
    // 0x8c6ab8: r0 = AllocateClosure()
    //     0x8c6ab8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c6abc: mov             x1, x0
    // 0x8c6ac0: ldur            x0, [fp, #-0x10]
    // 0x8c6ac4: StoreField: r0->field_f = r1
    //     0x8c6ac4: stur            w1, [x0, #0xf]
    // 0x8c6ac8: r1 = PaddedBlockCipher
    //     0x8c6ac8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18358] Type: PaddedBlockCipher
    //     0x8c6acc: ldr             x1, [x1, #0x358]
    // 0x8c6ad0: StoreField: r0->field_7 = r1
    //     0x8c6ad0: stur            w1, [x0, #7]
    // 0x8c6ad4: LeaveFrame
    //     0x8c6ad4: mov             SP, fp
    //     0x8c6ad8: ldp             fp, lr, [SP], #0x10
    // 0x8c6adc: ret
    //     0x8c6adc: ret             
    // 0x8c6ae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6ae0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6ae4: b               #0x8c6a68
  }
  [closure] static (dynamic) => PaddedBlockCipherImpl <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c6ae8, size: 0x54
    // 0x8c6ae8: EnterFrame
    //     0x8c6ae8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6aec: mov             fp, SP
    // 0x8c6af0: AllocStack(0x8)
    //     0x8c6af0: sub             SP, SP, #8
    // 0x8c6af4: SetupParameters()
    //     0x8c6af4: ldr             x0, [fp, #0x20]
    //     0x8c6af8: ldur            w1, [x0, #0x17]
    //     0x8c6afc: add             x1, x1, HEAP, lsl #32
    //     0x8c6b00: stur            x1, [fp, #-8]
    // 0x8c6b04: r1 = 1
    //     0x8c6b04: movz            x1, #0x1
    // 0x8c6b08: r0 = AllocateContext()
    //     0x8c6b08: bl              #0xec126c  ; AllocateContextStub
    // 0x8c6b0c: mov             x1, x0
    // 0x8c6b10: ldur            x0, [fp, #-8]
    // 0x8c6b14: StoreField: r1->field_b = r0
    //     0x8c6b14: stur            w0, [x1, #0xb]
    // 0x8c6b18: ldr             x0, [fp, #0x10]
    // 0x8c6b1c: StoreField: r1->field_f = r0
    //     0x8c6b1c: stur            w0, [x1, #0xf]
    // 0x8c6b20: mov             x2, x1
    // 0x8c6b24: r1 = Function '<anonymous closure>': static.
    //     0x8c6b24: add             x1, PP, #0x18, lsl #12  ; [pp+0x18360] AnonymousClosure: static (0x8c6b3c), in [package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart] PaddedBlockCipherImpl::factoryConfig (0x8c6a50)
    //     0x8c6b28: ldr             x1, [x1, #0x360]
    // 0x8c6b2c: r0 = AllocateClosure()
    //     0x8c6b2c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c6b30: LeaveFrame
    //     0x8c6b30: mov             SP, fp
    //     0x8c6b34: ldp             fp, lr, [SP], #0x10
    // 0x8c6b38: ret
    //     0x8c6b38: ret             
  }
  [closure] static PaddedBlockCipherImpl <anonymous closure>(dynamic) {
    // ** addr: 0x8c6b3c, size: 0x100
    // 0x8c6b3c: EnterFrame
    //     0x8c6b3c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6b40: mov             fp, SP
    // 0x8c6b44: AllocStack(0x28)
    //     0x8c6b44: sub             SP, SP, #0x28
    // 0x8c6b48: SetupParameters()
    //     0x8c6b48: ldr             x0, [fp, #0x10]
    //     0x8c6b4c: ldur            w1, [x0, #0x17]
    //     0x8c6b50: add             x1, x1, HEAP, lsl #32
    // 0x8c6b54: CheckStackOverflow
    //     0x8c6b54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6b58: cmp             SP, x16
    //     0x8c6b5c: b.ls            #0x8c6c2c
    // 0x8c6b60: LoadField: r3 = r1->field_f
    //     0x8c6b60: ldur            w3, [x1, #0xf]
    // 0x8c6b64: DecompressPointer r3
    //     0x8c6b64: add             x3, x3, HEAP, lsl #32
    // 0x8c6b68: stur            x3, [fp, #-8]
    // 0x8c6b6c: r0 = LoadClassIdInstr(r3)
    //     0x8c6b6c: ldur            x0, [x3, #-1]
    //     0x8c6b70: ubfx            x0, x0, #0xc, #0x14
    // 0x8c6b74: mov             x1, x3
    // 0x8c6b78: r2 = 2
    //     0x8c6b78: movz            x2, #0x2
    // 0x8c6b7c: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c6b7c: sub             lr, x0, #0xfdd
    //     0x8c6b80: ldr             lr, [x21, lr, lsl #3]
    //     0x8c6b84: blr             lr
    // 0x8c6b88: cmp             w0, NULL
    // 0x8c6b8c: b.eq            #0x8c6c34
    // 0x8c6b90: mov             x2, x0
    // 0x8c6b94: r1 = Null
    //     0x8c6b94: mov             x1, NULL
    // 0x8c6b98: r0 = Padding()
    //     0x8c6b98: bl              #0x8c6c48  ; [package:pointycastle/api.dart] Padding::Padding
    // 0x8c6b9c: mov             x3, x0
    // 0x8c6ba0: ldur            x1, [fp, #-8]
    // 0x8c6ba4: stur            x3, [fp, #-0x10]
    // 0x8c6ba8: r0 = LoadClassIdInstr(r1)
    //     0x8c6ba8: ldur            x0, [x1, #-1]
    //     0x8c6bac: ubfx            x0, x0, #0xc, #0x14
    // 0x8c6bb0: r2 = 1
    //     0x8c6bb0: movz            x2, #0x1
    // 0x8c6bb4: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c6bb4: sub             lr, x0, #0xfdd
    //     0x8c6bb8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c6bbc: blr             lr
    // 0x8c6bc0: stur            x0, [fp, #-8]
    // 0x8c6bc4: cmp             w0, NULL
    // 0x8c6bc8: b.eq            #0x8c6c38
    // 0x8c6bcc: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c6bcc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6bd0: ldr             x0, [x0, #0x2e38]
    //     0x8c6bd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6bd8: cmp             w0, w16
    //     0x8c6bdc: b.ne            #0x8c6bec
    //     0x8c6be0: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c6be4: ldr             x2, [x2, #0xf80]
    //     0x8c6be8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6bec: r16 = <BlockCipher>
    //     0x8c6bec: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8c6bf0: ldr             x16, [x16, #0x88]
    // 0x8c6bf4: stp             x0, x16, [SP, #8]
    // 0x8c6bf8: ldur            x16, [fp, #-8]
    // 0x8c6bfc: str             x16, [SP]
    // 0x8c6c00: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c6c00: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c6c04: r0 = create()
    //     0x8c6c04: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c6c08: stur            x0, [fp, #-8]
    // 0x8c6c0c: r0 = PaddedBlockCipherImpl()
    //     0x8c6c0c: bl              #0x8c6c3c  ; AllocatePaddedBlockCipherImplStub -> PaddedBlockCipherImpl (size=0x14)
    // 0x8c6c10: ldur            x1, [fp, #-0x10]
    // 0x8c6c14: StoreField: r0->field_7 = r1
    //     0x8c6c14: stur            w1, [x0, #7]
    // 0x8c6c18: ldur            x1, [fp, #-8]
    // 0x8c6c1c: StoreField: r0->field_b = r1
    //     0x8c6c1c: stur            w1, [x0, #0xb]
    // 0x8c6c20: LeaveFrame
    //     0x8c6c20: mov             SP, fp
    //     0x8c6c24: ldp             fp, lr, [SP], #0x10
    // 0x8c6c28: ret
    //     0x8c6c28: ret             
    // 0x8c6c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6c2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6c30: b               #0x8c6b60
    // 0x8c6c34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c6c34: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c6c38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c6c38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ reset(/* No info */) {
    // ** addr: 0xea0410, size: 0x58
    // 0xea0410: EnterFrame
    //     0xea0410: stp             fp, lr, [SP, #-0x10]!
    //     0xea0414: mov             fp, SP
    // 0xea0418: CheckStackOverflow
    //     0xea0418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea041c: cmp             SP, x16
    //     0xea0420: b.ls            #0xea0460
    // 0xea0424: StoreField: r1->field_f = rNULL
    //     0xea0424: stur            NULL, [x1, #0xf]
    // 0xea0428: LoadField: r0 = r1->field_b
    //     0xea0428: ldur            w0, [x1, #0xb]
    // 0xea042c: DecompressPointer r0
    //     0xea042c: add             x0, x0, HEAP, lsl #32
    // 0xea0430: r1 = LoadClassIdInstr(r0)
    //     0xea0430: ldur            x1, [x0, #-1]
    //     0xea0434: ubfx            x1, x1, #0xc, #0x14
    // 0xea0438: mov             x16, x0
    // 0xea043c: mov             x0, x1
    // 0xea0440: mov             x1, x16
    // 0xea0444: r0 = GDT[cid_x0 + -0xeaf]()
    //     0xea0444: sub             lr, x0, #0xeaf
    //     0xea0448: ldr             lr, [x21, lr, lsl #3]
    //     0xea044c: blr             lr
    // 0xea0450: r0 = Null
    //     0xea0450: mov             x0, NULL
    // 0xea0454: LeaveFrame
    //     0xea0454: mov             SP, fp
    //     0xea0458: ldp             fp, lr, [SP], #0x10
    // 0xea045c: ret
    //     0xea045c: ret             
    // 0xea0460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea0460: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea0464: b               #0xea0424
  }
  _ init(/* No info */) {
    // ** addr: 0xea1a30, size: 0xd0
    // 0xea1a30: EnterFrame
    //     0xea1a30: stp             fp, lr, [SP, #-0x10]!
    //     0xea1a34: mov             fp, SP
    // 0xea1a38: AllocStack(0x18)
    //     0xea1a38: sub             SP, SP, #0x18
    // 0xea1a3c: SetupParameters(PaddedBlockCipherImpl this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xea1a3c: mov             x5, x1
    //     0xea1a40: mov             x4, x2
    //     0xea1a44: stur            x1, [fp, #-8]
    //     0xea1a48: stur            x2, [fp, #-0x10]
    //     0xea1a4c: stur            x3, [fp, #-0x18]
    // 0xea1a50: CheckStackOverflow
    //     0xea1a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1a54: cmp             SP, x16
    //     0xea1a58: b.ls            #0xea1af8
    // 0xea1a5c: mov             x0, x3
    // 0xea1a60: r2 = Null
    //     0xea1a60: mov             x2, NULL
    // 0xea1a64: r1 = Null
    //     0xea1a64: mov             x1, NULL
    // 0xea1a68: r4 = 60
    //     0xea1a68: movz            x4, #0x3c
    // 0xea1a6c: branchIfSmi(r0, 0xea1a78)
    //     0xea1a6c: tbz             w0, #0, #0xea1a78
    // 0xea1a70: r4 = LoadClassIdInstr(r0)
    //     0xea1a70: ldur            x4, [x0, #-1]
    //     0xea1a74: ubfx            x4, x4, #0xc, #0x14
    // 0xea1a78: cmp             x4, #0x2a9
    // 0xea1a7c: b.eq            #0xea1a94
    // 0xea1a80: r8 = PaddedBlockCipherParameters<CipherParameters?, CipherParameters?>
    //     0xea1a80: add             x8, PP, #0x21, lsl #12  ; [pp+0x21c58] Type: PaddedBlockCipherParameters<CipherParameters?, CipherParameters?>
    //     0xea1a84: ldr             x8, [x8, #0xc58]
    // 0xea1a88: r3 = Null
    //     0xea1a88: add             x3, PP, #0x21, lsl #12  ; [pp+0x21c60] Null
    //     0xea1a8c: ldr             x3, [x3, #0xc60]
    // 0xea1a90: r0 = DefaultTypeTest()
    //     0xea1a90: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xea1a94: ldur            x4, [fp, #-8]
    // 0xea1a98: ldur            x2, [fp, #-0x10]
    // 0xea1a9c: StoreField: r4->field_f = r2
    //     0xea1a9c: stur            w2, [x4, #0xf]
    // 0xea1aa0: LoadField: r1 = r4->field_b
    //     0xea1aa0: ldur            w1, [x4, #0xb]
    // 0xea1aa4: DecompressPointer r1
    //     0xea1aa4: add             x1, x1, HEAP, lsl #32
    // 0xea1aa8: ldur            x0, [fp, #-0x18]
    // 0xea1aac: LoadField: r3 = r0->field_b
    //     0xea1aac: ldur            w3, [x0, #0xb]
    // 0xea1ab0: DecompressPointer r3
    //     0xea1ab0: add             x3, x3, HEAP, lsl #32
    // 0xea1ab4: r0 = LoadClassIdInstr(r1)
    //     0xea1ab4: ldur            x0, [x1, #-1]
    //     0xea1ab8: ubfx            x0, x0, #0xc, #0x14
    // 0xea1abc: r0 = GDT[cid_x0 + -0xeda]()
    //     0xea1abc: sub             lr, x0, #0xeda
    //     0xea1ac0: ldr             lr, [x21, lr, lsl #3]
    //     0xea1ac4: blr             lr
    // 0xea1ac8: ldur            x0, [fp, #-8]
    // 0xea1acc: LoadField: r1 = r0->field_7
    //     0xea1acc: ldur            w1, [x0, #7]
    // 0xea1ad0: DecompressPointer r1
    //     0xea1ad0: add             x1, x1, HEAP, lsl #32
    // 0xea1ad4: r0 = LoadClassIdInstr(r1)
    //     0xea1ad4: ldur            x0, [x1, #-1]
    //     0xea1ad8: ubfx            x0, x0, #0xc, #0x14
    // 0xea1adc: r0 = GDT[cid_x0 + -0xffb]()
    //     0xea1adc: sub             lr, x0, #0xffb
    //     0xea1ae0: ldr             lr, [x21, lr, lsl #3]
    //     0xea1ae4: blr             lr
    // 0xea1ae8: r0 = Null
    //     0xea1ae8: mov             x0, NULL
    // 0xea1aec: LeaveFrame
    //     0xea1aec: mov             SP, fp
    //     0xea1af0: ldp             fp, lr, [SP], #0x10
    // 0xea1af4: ret
    //     0xea1af4: ret             
    // 0xea1af8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1af8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1afc: b               #0xea1a5c
  }
  _ processBlock(/* No info */) {
    // ** addr: 0xeb377c, size: 0x50
    // 0xeb377c: EnterFrame
    //     0xeb377c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb3780: mov             fp, SP
    // 0xeb3784: CheckStackOverflow
    //     0xeb3784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb3788: cmp             SP, x16
    //     0xeb378c: b.ls            #0xeb37c4
    // 0xeb3790: LoadField: r0 = r1->field_b
    //     0xeb3790: ldur            w0, [x1, #0xb]
    // 0xeb3794: DecompressPointer r0
    //     0xeb3794: add             x0, x0, HEAP, lsl #32
    // 0xeb3798: r1 = LoadClassIdInstr(r0)
    //     0xeb3798: ldur            x1, [x0, #-1]
    //     0xeb379c: ubfx            x1, x1, #0xc, #0x14
    // 0xeb37a0: mov             x16, x0
    // 0xeb37a4: mov             x0, x1
    // 0xeb37a8: mov             x1, x16
    // 0xeb37ac: r0 = GDT[cid_x0 + -0xf69]()
    //     0xeb37ac: sub             lr, x0, #0xf69
    //     0xeb37b0: ldr             lr, [x21, lr, lsl #3]
    //     0xeb37b4: blr             lr
    // 0xeb37b8: LeaveFrame
    //     0xeb37b8: mov             SP, fp
    //     0xeb37bc: ldp             fp, lr, [SP], #0x10
    // 0xeb37c0: ret
    //     0xeb37c0: ret             
    // 0xeb37c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb37c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb37c8: b               #0xeb3790
  }
  _ process(/* No info */) {
    // ** addr: 0xeb5df4, size: 0x340
    // 0xeb5df4: EnterFrame
    //     0xeb5df4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5df8: mov             fp, SP
    // 0xeb5dfc: AllocStack(0x40)
    //     0xeb5dfc: sub             SP, SP, #0x40
    // 0xeb5e00: SetupParameters(PaddedBlockCipherImpl this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xeb5e00: mov             x3, x1
    //     0xeb5e04: stur            x1, [fp, #-0x18]
    //     0xeb5e08: stur            x2, [fp, #-0x20]
    // 0xeb5e0c: CheckStackOverflow
    //     0xeb5e0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5e10: cmp             SP, x16
    //     0xeb5e14: b.ls            #0xeb60c4
    // 0xeb5e18: LoadField: r4 = r2->field_13
    //     0xeb5e18: ldur            w4, [x2, #0x13]
    // 0xeb5e1c: stur            x4, [fp, #-0x10]
    // 0xeb5e20: LoadField: r5 = r3->field_b
    //     0xeb5e20: ldur            w5, [x3, #0xb]
    // 0xeb5e24: DecompressPointer r5
    //     0xeb5e24: add             x5, x5, HEAP, lsl #32
    // 0xeb5e28: stur            x5, [fp, #-8]
    // 0xeb5e2c: r0 = LoadClassIdInstr(r5)
    //     0xeb5e2c: ldur            x0, [x5, #-1]
    //     0xeb5e30: ubfx            x0, x0, #0xc, #0x14
    // 0xeb5e34: mov             x1, x5
    // 0xeb5e38: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb5e38: sub             lr, x0, #1, lsl #12
    //     0xeb5e3c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb5e40: blr             lr
    // 0xeb5e44: mov             x1, x0
    // 0xeb5e48: ldur            x0, [fp, #-0x10]
    // 0xeb5e4c: r2 = LoadInt32Instr(r0)
    //     0xeb5e4c: sbfx            x2, x0, #1, #0x1f
    // 0xeb5e50: stur            x2, [fp, #-0x30]
    // 0xeb5e54: add             x0, x2, x1
    // 0xeb5e58: sub             x3, x0, #1
    // 0xeb5e5c: ldur            x4, [fp, #-8]
    // 0xeb5e60: stur            x3, [fp, #-0x28]
    // 0xeb5e64: r0 = LoadClassIdInstr(r4)
    //     0xeb5e64: ldur            x0, [x4, #-1]
    //     0xeb5e68: ubfx            x0, x0, #0xc, #0x14
    // 0xeb5e6c: mov             x1, x4
    // 0xeb5e70: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb5e70: sub             lr, x0, #1, lsl #12
    //     0xeb5e74: ldr             lr, [x21, lr, lsl #3]
    //     0xeb5e78: blr             lr
    // 0xeb5e7c: mov             x1, x0
    // 0xeb5e80: ldur            x0, [fp, #-0x28]
    // 0xeb5e84: cbz             x1, #0xeb60cc
    // 0xeb5e88: sdiv            x2, x0, x1
    // 0xeb5e8c: ldur            x3, [fp, #-0x18]
    // 0xeb5e90: stur            x2, [fp, #-0x38]
    // 0xeb5e94: LoadField: r0 = r3->field_f
    //     0xeb5e94: ldur            w0, [x3, #0xf]
    // 0xeb5e98: DecompressPointer r0
    //     0xeb5e98: add             x0, x0, HEAP, lsl #32
    // 0xeb5e9c: cmp             w0, NULL
    // 0xeb5ea0: b.eq            #0xeb60e4
    // 0xeb5ea4: tbnz            w0, #4, #0xeb5f08
    // 0xeb5ea8: ldur            x5, [fp, #-8]
    // 0xeb5eac: ldur            x4, [fp, #-0x30]
    // 0xeb5eb0: r0 = LoadClassIdInstr(r5)
    //     0xeb5eb0: ldur            x0, [x5, #-1]
    //     0xeb5eb4: ubfx            x0, x0, #0xc, #0x14
    // 0xeb5eb8: mov             x1, x5
    // 0xeb5ebc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb5ebc: sub             lr, x0, #1, lsl #12
    //     0xeb5ec0: ldr             lr, [x21, lr, lsl #3]
    //     0xeb5ec4: blr             lr
    // 0xeb5ec8: ldur            x2, [fp, #-0x30]
    // 0xeb5ecc: add             x3, x2, x0
    // 0xeb5ed0: ldur            x2, [fp, #-8]
    // 0xeb5ed4: stur            x3, [fp, #-0x28]
    // 0xeb5ed8: r0 = LoadClassIdInstr(r2)
    //     0xeb5ed8: ldur            x0, [x2, #-1]
    //     0xeb5edc: ubfx            x0, x0, #0xc, #0x14
    // 0xeb5ee0: mov             x1, x2
    // 0xeb5ee4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb5ee4: sub             lr, x0, #1, lsl #12
    //     0xeb5ee8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb5eec: blr             lr
    // 0xeb5ef0: mov             x1, x0
    // 0xeb5ef4: ldur            x0, [fp, #-0x28]
    // 0xeb5ef8: cbz             x1, #0xeb60e8
    // 0xeb5efc: sdiv            x2, x0, x1
    // 0xeb5f00: mov             x4, x2
    // 0xeb5f04: b               #0xeb5f4c
    // 0xeb5f08: ldur            x3, [fp, #-8]
    // 0xeb5f0c: ldur            x2, [fp, #-0x30]
    // 0xeb5f10: r0 = LoadClassIdInstr(r3)
    //     0xeb5f10: ldur            x0, [x3, #-1]
    //     0xeb5f14: ubfx            x0, x0, #0xc, #0x14
    // 0xeb5f18: mov             x1, x3
    // 0xeb5f1c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb5f1c: sub             lr, x0, #1, lsl #12
    //     0xeb5f20: ldr             lr, [x21, lr, lsl #3]
    //     0xeb5f24: blr             lr
    // 0xeb5f28: mov             x1, x0
    // 0xeb5f2c: ldur            x0, [fp, #-0x30]
    // 0xeb5f30: cbz             x1, #0xeb6100
    // 0xeb5f34: sdiv            x3, x0, x1
    // 0xeb5f38: msub            x2, x3, x1, x0
    // 0xeb5f3c: cmp             x2, xzr
    // 0xeb5f40: b.lt            #0xeb6118
    // 0xeb5f44: cbnz            x2, #0xeb609c
    // 0xeb5f48: ldur            x4, [fp, #-0x38]
    // 0xeb5f4c: ldur            x3, [fp, #-0x38]
    // 0xeb5f50: ldur            x2, [fp, #-8]
    // 0xeb5f54: stur            x4, [fp, #-0x28]
    // 0xeb5f58: r0 = LoadClassIdInstr(r2)
    //     0xeb5f58: ldur            x0, [x2, #-1]
    //     0xeb5f5c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb5f60: mov             x1, x2
    // 0xeb5f64: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb5f64: sub             lr, x0, #1, lsl #12
    //     0xeb5f68: ldr             lr, [x21, lr, lsl #3]
    //     0xeb5f6c: blr             lr
    // 0xeb5f70: mov             x1, x0
    // 0xeb5f74: ldur            x0, [fp, #-0x28]
    // 0xeb5f78: mul             x2, x0, x1
    // 0xeb5f7c: r0 = BoxInt64Instr(r2)
    //     0xeb5f7c: sbfiz           x0, x2, #1, #0x1f
    //     0xeb5f80: cmp             x2, x0, asr #1
    //     0xeb5f84: b.eq            #0xeb5f90
    //     0xeb5f88: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5f8c: stur            x2, [x0, #7]
    // 0xeb5f90: mov             x4, x0
    // 0xeb5f94: r0 = AllocateUint8Array()
    //     0xeb5f94: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xeb5f98: mov             x2, x0
    // 0xeb5f9c: ldur            x0, [fp, #-0x38]
    // 0xeb5fa0: stur            x2, [fp, #-0x10]
    // 0xeb5fa4: sub             x3, x0, #1
    // 0xeb5fa8: stur            x3, [fp, #-0x30]
    // 0xeb5fac: r5 = 0
    //     0xeb5fac: movz            x5, #0
    // 0xeb5fb0: ldur            x4, [fp, #-8]
    // 0xeb5fb4: stur            x5, [fp, #-0x28]
    // 0xeb5fb8: CheckStackOverflow
    //     0xeb5fb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5fbc: cmp             SP, x16
    //     0xeb5fc0: b.ls            #0xeb612c
    // 0xeb5fc4: cmp             x5, x3
    // 0xeb5fc8: b.ge            #0xeb6028
    // 0xeb5fcc: r0 = LoadClassIdInstr(r4)
    //     0xeb5fcc: ldur            x0, [x4, #-1]
    //     0xeb5fd0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb5fd4: mov             x1, x4
    // 0xeb5fd8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb5fd8: sub             lr, x0, #1, lsl #12
    //     0xeb5fdc: ldr             lr, [x21, lr, lsl #3]
    //     0xeb5fe0: blr             lr
    // 0xeb5fe4: ldur            x4, [fp, #-0x28]
    // 0xeb5fe8: mul             x6, x4, x0
    // 0xeb5fec: ldur            x7, [fp, #-8]
    // 0xeb5ff0: r0 = LoadClassIdInstr(r7)
    //     0xeb5ff0: ldur            x0, [x7, #-1]
    //     0xeb5ff4: ubfx            x0, x0, #0xc, #0x14
    // 0xeb5ff8: mov             x1, x7
    // 0xeb5ffc: ldur            x2, [fp, #-0x20]
    // 0xeb6000: mov             x3, x6
    // 0xeb6004: ldur            x5, [fp, #-0x10]
    // 0xeb6008: r0 = GDT[cid_x0 + -0xf69]()
    //     0xeb6008: sub             lr, x0, #0xf69
    //     0xeb600c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb6010: blr             lr
    // 0xeb6014: ldur            x0, [fp, #-0x28]
    // 0xeb6018: add             x5, x0, #1
    // 0xeb601c: ldur            x3, [fp, #-0x30]
    // 0xeb6020: ldur            x2, [fp, #-0x10]
    // 0xeb6024: b               #0xeb5fb0
    // 0xeb6028: mov             x0, x3
    // 0xeb602c: ldur            x1, [fp, #-0x18]
    // 0xeb6030: r0 = blockSize()
    //     0xeb6030: bl              #0xeb6764  ; [package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart] PaddedBlockCipherImpl::blockSize
    // 0xeb6034: mov             x1, x0
    // 0xeb6038: ldur            x0, [fp, #-0x30]
    // 0xeb603c: mul             x4, x0, x1
    // 0xeb6040: ldur            x1, [fp, #-0x18]
    // 0xeb6044: ldur            x2, [fp, #-0x20]
    // 0xeb6048: mov             x3, x4
    // 0xeb604c: ldur            x5, [fp, #-0x10]
    // 0xeb6050: mov             x6, x4
    // 0xeb6054: stur            x4, [fp, #-0x28]
    // 0xeb6058: r0 = doFinal()
    //     0xeb6058: bl              #0xeb6134  ; [package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart] PaddedBlockCipherImpl::doFinal
    // 0xeb605c: mov             x1, x0
    // 0xeb6060: ldur            x0, [fp, #-0x28]
    // 0xeb6064: add             x2, x0, x1
    // 0xeb6068: r0 = BoxInt64Instr(r2)
    //     0xeb6068: sbfiz           x0, x2, #1, #0x1f
    //     0xeb606c: cmp             x2, x0, asr #1
    //     0xeb6070: b.eq            #0xeb607c
    //     0xeb6074: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb6078: stur            x2, [x0, #7]
    // 0xeb607c: str             x0, [SP]
    // 0xeb6080: ldur            x1, [fp, #-0x10]
    // 0xeb6084: r2 = 0
    //     0xeb6084: movz            x2, #0
    // 0xeb6088: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb6088: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb608c: r0 = sublist()
    //     0xeb608c: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xeb6090: LeaveFrame
    //     0xeb6090: mov             SP, fp
    //     0xeb6094: ldp             fp, lr, [SP], #0x10
    // 0xeb6098: ret
    //     0xeb6098: ret             
    // 0xeb609c: r0 = ArgumentError()
    //     0xeb609c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xeb60a0: mov             x1, x0
    // 0xeb60a4: r0 = "Input data length must be a multiple of cipher\'s block size"
    //     0xeb60a4: add             x0, PP, #0x21, lsl #12  ; [pp+0x21c50] "Input data length must be a multiple of cipher\'s block size"
    //     0xeb60a8: ldr             x0, [x0, #0xc50]
    // 0xeb60ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb60ac: stur            w0, [x1, #0x17]
    // 0xeb60b0: r0 = false
    //     0xeb60b0: add             x0, NULL, #0x30  ; false
    // 0xeb60b4: StoreField: r1->field_b = r0
    //     0xeb60b4: stur            w0, [x1, #0xb]
    // 0xeb60b8: mov             x0, x1
    // 0xeb60bc: r0 = Throw()
    //     0xeb60bc: bl              #0xec04b8  ; ThrowStub
    // 0xeb60c0: brk             #0
    // 0xeb60c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb60c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb60c8: b               #0xeb5e18
    // 0xeb60cc: stp             x0, x1, [SP, #-0x10]!
    // 0xeb60d0: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0xeb60d4: r4 = 0
    //     0xeb60d4: movz            x4, #0
    // 0xeb60d8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeb60dc: blr             lr
    // 0xeb60e0: brk             #0
    // 0xeb60e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb60e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeb60e8: stp             x0, x1, [SP, #-0x10]!
    // 0xeb60ec: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0xeb60f0: r4 = 0
    //     0xeb60f0: movz            x4, #0
    // 0xeb60f4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeb60f8: blr             lr
    // 0xeb60fc: brk             #0
    // 0xeb6100: stp             x0, x1, [SP, #-0x10]!
    // 0xeb6104: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0xeb6108: r4 = 0
    //     0xeb6108: movz            x4, #0
    // 0xeb610c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeb6110: blr             lr
    // 0xeb6114: brk             #0
    // 0xeb6118: cmp             x1, xzr
    // 0xeb611c: sub             x3, x2, x1
    // 0xeb6120: add             x2, x2, x1
    // 0xeb6124: csel            x2, x3, x2, lt
    // 0xeb6128: b               #0xeb5f44
    // 0xeb612c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb612c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6130: b               #0xeb5fc4
  }
  _ doFinal(/* No info */) {
    // ** addr: 0xeb6134, size: 0x45c
    // 0xeb6134: EnterFrame
    //     0xeb6134: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6138: mov             fp, SP
    // 0xeb613c: AllocStack(0x58)
    //     0xeb613c: sub             SP, SP, #0x58
    // 0xeb6140: SetupParameters(PaddedBlockCipherImpl this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, dynamic _ /* r3 => r2, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* r6 => r6, fp-0x30 */)
    //     0xeb6140: mov             x4, x1
    //     0xeb6144: stur            x2, [fp, #-0x18]
    //     0xeb6148: mov             x16, x3
    //     0xeb614c: mov             x3, x2
    //     0xeb6150: mov             x2, x16
    //     0xeb6154: stur            x1, [fp, #-0x10]
    //     0xeb6158: stur            x2, [fp, #-0x20]
    //     0xeb615c: stur            x5, [fp, #-0x28]
    //     0xeb6160: stur            x6, [fp, #-0x30]
    // 0xeb6164: CheckStackOverflow
    //     0xeb6164: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6168: cmp             SP, x16
    //     0xeb616c: b.ls            #0xeb6584
    // 0xeb6170: LoadField: r0 = r4->field_f
    //     0xeb6170: ldur            w0, [x4, #0xf]
    // 0xeb6174: DecompressPointer r0
    //     0xeb6174: add             x0, x0, HEAP, lsl #32
    // 0xeb6178: cmp             w0, NULL
    // 0xeb617c: b.eq            #0xeb658c
    // 0xeb6180: tbnz            w0, #4, #0xeb64bc
    // 0xeb6184: LoadField: r7 = r4->field_b
    //     0xeb6184: ldur            w7, [x4, #0xb]
    // 0xeb6188: DecompressPointer r7
    //     0xeb6188: add             x7, x7, HEAP, lsl #32
    // 0xeb618c: stur            x7, [fp, #-8]
    // 0xeb6190: r0 = LoadClassIdInstr(r7)
    //     0xeb6190: ldur            x0, [x7, #-1]
    //     0xeb6194: ubfx            x0, x0, #0xc, #0x14
    // 0xeb6198: mov             x1, x7
    // 0xeb619c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb619c: sub             lr, x0, #1, lsl #12
    //     0xeb61a0: ldr             lr, [x21, lr, lsl #3]
    //     0xeb61a4: blr             lr
    // 0xeb61a8: mov             x2, x0
    // 0xeb61ac: stur            x2, [fp, #-0x38]
    // 0xeb61b0: r0 = BoxInt64Instr(r2)
    //     0xeb61b0: sbfiz           x0, x2, #1, #0x1f
    //     0xeb61b4: cmp             x2, x0, asr #1
    //     0xeb61b8: b.eq            #0xeb61c4
    //     0xeb61bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb61c0: stur            x2, [x0, #7]
    // 0xeb61c4: mov             x4, x0
    // 0xeb61c8: r0 = AllocateUint8Array()
    //     0xeb61c8: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xeb61cc: ldur            x1, [fp, #-0x18]
    // 0xeb61d0: ldur            x2, [fp, #-0x20]
    // 0xeb61d4: stur            x0, [fp, #-0x40]
    // 0xeb61d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb61d8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb61dc: r0 = sublist()
    //     0xeb61dc: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xeb61e0: stur            x0, [fp, #-0x58]
    // 0xeb61e4: LoadField: r4 = r0->field_13
    //     0xeb61e4: ldur            w4, [x0, #0x13]
    // 0xeb61e8: stur            x4, [fp, #-0x50]
    // 0xeb61ec: r5 = LoadInt32Instr(r4)
    //     0xeb61ec: sbfx            x5, x4, #1, #0x1f
    // 0xeb61f0: stur            x5, [fp, #-0x48]
    // 0xeb61f4: tbz             x5, #0x3f, #0xeb6200
    // 0xeb61f8: ldur            x3, [fp, #-0x38]
    // 0xeb61fc: b               #0xeb620c
    // 0xeb6200: ldur            x3, [fp, #-0x38]
    // 0xeb6204: cmp             x5, x3
    // 0xeb6208: b.le            #0xeb621c
    // 0xeb620c: mov             x2, x4
    // 0xeb6210: r1 = 0
    //     0xeb6210: movz            x1, #0
    // 0xeb6214: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeb6214: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeb6218: r0 = checkValidRange()
    //     0xeb6218: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeb621c: ldur            x2, [fp, #-0x48]
    // 0xeb6220: cbnz            x2, #0xeb622c
    // 0xeb6224: ldur            x20, [fp, #-0x40]
    // 0xeb6228: b               #0xeb6358
    // 0xeb622c: ldur            x0, [fp, #-0x50]
    // 0xeb6230: cmp             w0, #0x800
    // 0xeb6234: b.ge            #0xeb6308
    // 0xeb6238: ldur            x1, [fp, #-0x58]
    // 0xeb623c: ldur            x20, [fp, #-0x40]
    // 0xeb6240: mov             x3, x0
    // 0xeb6244: add             x2, x1, #0x17
    // 0xeb6248: add             x0, x20, #0x17
    // 0xeb624c: cbz             x3, #0xeb6304
    // 0xeb6250: cmp             x0, x2
    // 0xeb6254: b.ls            #0xeb62bc
    // 0xeb6258: sxtw            x3, w3
    // 0xeb625c: add             x16, x2, x3, asr #1
    // 0xeb6260: cmp             x0, x16
    // 0xeb6264: b.hs            #0xeb62bc
    // 0xeb6268: mov             x2, x16
    // 0xeb626c: add             x0, x0, x3, asr #1
    // 0xeb6270: tbz             w3, #4, #0xeb627c
    // 0xeb6274: ldr             x16, [x2, #-8]!
    // 0xeb6278: str             x16, [x0, #-8]!
    // 0xeb627c: tbz             w3, #3, #0xeb6288
    // 0xeb6280: ldr             w16, [x2, #-4]!
    // 0xeb6284: str             w16, [x0, #-4]!
    // 0xeb6288: tbz             w3, #2, #0xeb6294
    // 0xeb628c: ldrh            w16, [x2, #-2]!
    // 0xeb6290: strh            w16, [x0, #-2]!
    // 0xeb6294: tbz             w3, #1, #0xeb62a0
    // 0xeb6298: ldrb            w16, [x2, #-1]!
    // 0xeb629c: strb            w16, [x0, #-1]!
    // 0xeb62a0: ands            w3, w3, #0xffffffe1
    // 0xeb62a4: b.eq            #0xeb6304
    // 0xeb62a8: ldp             x16, x17, [x2, #-0x10]!
    // 0xeb62ac: stp             x16, x17, [x0, #-0x10]!
    // 0xeb62b0: subs            w3, w3, #0x20
    // 0xeb62b4: b.ne            #0xeb62a8
    // 0xeb62b8: b               #0xeb6304
    // 0xeb62bc: tbz             w3, #4, #0xeb62c8
    // 0xeb62c0: ldr             x16, [x2], #8
    // 0xeb62c4: str             x16, [x0], #8
    // 0xeb62c8: tbz             w3, #3, #0xeb62d4
    // 0xeb62cc: ldr             w16, [x2], #4
    // 0xeb62d0: str             w16, [x0], #4
    // 0xeb62d4: tbz             w3, #2, #0xeb62e0
    // 0xeb62d8: ldrh            w16, [x2], #2
    // 0xeb62dc: strh            w16, [x0], #2
    // 0xeb62e0: tbz             w3, #1, #0xeb62ec
    // 0xeb62e4: ldrb            w16, [x2], #1
    // 0xeb62e8: strb            w16, [x0], #1
    // 0xeb62ec: ands            w3, w3, #0xffffffe1
    // 0xeb62f0: b.eq            #0xeb6304
    // 0xeb62f4: ldp             x16, x17, [x2], #0x10
    // 0xeb62f8: stp             x16, x17, [x0], #0x10
    // 0xeb62fc: subs            w3, w3, #0x20
    // 0xeb6300: b.ne            #0xeb62f4
    // 0xeb6304: b               #0xeb6358
    // 0xeb6308: ldur            x1, [fp, #-0x58]
    // 0xeb630c: ldur            x20, [fp, #-0x40]
    // 0xeb6310: LoadField: r0 = r20->field_7
    //     0xeb6310: ldur            x0, [x20, #7]
    // 0xeb6314: LoadField: r3 = r1->field_7
    //     0xeb6314: ldur            x3, [x1, #7]
    // 0xeb6318: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xeb6318: mov             x1, THR
    //     0xeb631c: ldr             x9, [x1, #0x658]
    //     0xeb6320: mov             x1, x3
    //     0xeb6324: mov             x17, fp
    //     0xeb6328: str             fp, [SP, #-8]!
    //     0xeb632c: mov             fp, SP
    //     0xeb6330: and             SP, SP, #0xfffffffffffffff0
    //     0xeb6334: mov             x19, sp
    //     0xeb6338: mov             sp, SP
    //     0xeb633c: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeb6340: blr             x9
    //     0xeb6344: movz            x16, #0x8
    //     0xeb6348: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xeb634c: mov             sp, x19
    //     0xeb6350: mov             SP, fp
    //     0xeb6354: ldr             fp, [SP], #8
    // 0xeb6358: ldur            x2, [fp, #-0x18]
    // 0xeb635c: ldur            x3, [fp, #-0x20]
    // 0xeb6360: ldur            x4, [fp, #-8]
    // 0xeb6364: LoadField: r0 = r2->field_13
    //     0xeb6364: ldur            w0, [x2, #0x13]
    // 0xeb6368: r1 = LoadInt32Instr(r0)
    //     0xeb6368: sbfx            x1, x0, #1, #0x1f
    // 0xeb636c: sub             x5, x1, x3
    // 0xeb6370: stur            x5, [fp, #-0x38]
    // 0xeb6374: r0 = LoadClassIdInstr(r4)
    //     0xeb6374: ldur            x0, [x4, #-1]
    //     0xeb6378: ubfx            x0, x0, #0xc, #0x14
    // 0xeb637c: mov             x1, x4
    // 0xeb6380: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb6380: sub             lr, x0, #1, lsl #12
    //     0xeb6384: ldr             lr, [x21, lr, lsl #3]
    //     0xeb6388: blr             lr
    // 0xeb638c: ldur            x3, [fp, #-0x38]
    // 0xeb6390: cmp             x3, x0
    // 0xeb6394: b.ge            #0xeb6408
    // 0xeb6398: ldur            x5, [fp, #-0x10]
    // 0xeb639c: ldur            x4, [fp, #-8]
    // 0xeb63a0: LoadField: r1 = r5->field_7
    //     0xeb63a0: ldur            w1, [x5, #7]
    // 0xeb63a4: DecompressPointer r1
    //     0xeb63a4: add             x1, x1, HEAP, lsl #32
    // 0xeb63a8: r0 = LoadClassIdInstr(r1)
    //     0xeb63a8: ldur            x0, [x1, #-1]
    //     0xeb63ac: ubfx            x0, x0, #0xc, #0x14
    // 0xeb63b0: ldur            x2, [fp, #-0x40]
    // 0xeb63b4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb63b4: sub             lr, x0, #1, lsl #12
    //     0xeb63b8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb63bc: blr             lr
    // 0xeb63c0: ldur            x1, [fp, #-0x10]
    // 0xeb63c4: ldur            x2, [fp, #-0x40]
    // 0xeb63c8: ldur            x5, [fp, #-0x28]
    // 0xeb63cc: ldur            x6, [fp, #-0x30]
    // 0xeb63d0: r3 = 0
    //     0xeb63d0: movz            x3, #0
    // 0xeb63d4: r0 = processBlock()
    //     0xeb63d4: bl              #0xeb377c  ; [package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart] PaddedBlockCipherImpl::processBlock
    // 0xeb63d8: ldur            x0, [fp, #-8]
    // 0xeb63dc: r1 = LoadClassIdInstr(r0)
    //     0xeb63dc: ldur            x1, [x0, #-1]
    //     0xeb63e0: ubfx            x1, x1, #0xc, #0x14
    // 0xeb63e4: mov             x16, x0
    // 0xeb63e8: mov             x0, x1
    // 0xeb63ec: mov             x1, x16
    // 0xeb63f0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb63f0: sub             lr, x0, #1, lsl #12
    //     0xeb63f4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb63f8: blr             lr
    // 0xeb63fc: LeaveFrame
    //     0xeb63fc: mov             SP, fp
    //     0xeb6400: ldp             fp, lr, [SP], #0x10
    // 0xeb6404: ret
    //     0xeb6404: ret             
    // 0xeb6408: ldur            x4, [fp, #-0x10]
    // 0xeb640c: ldur            x7, [fp, #-0x30]
    // 0xeb6410: ldur            x0, [fp, #-8]
    // 0xeb6414: mov             x1, x4
    // 0xeb6418: ldur            x2, [fp, #-0x18]
    // 0xeb641c: ldur            x3, [fp, #-0x20]
    // 0xeb6420: ldur            x5, [fp, #-0x28]
    // 0xeb6424: mov             x6, x7
    // 0xeb6428: r0 = processBlock()
    //     0xeb6428: bl              #0xeb377c  ; [package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart] PaddedBlockCipherImpl::processBlock
    // 0xeb642c: ldur            x4, [fp, #-0x10]
    // 0xeb6430: LoadField: r1 = r4->field_7
    //     0xeb6430: ldur            w1, [x4, #7]
    // 0xeb6434: DecompressPointer r1
    //     0xeb6434: add             x1, x1, HEAP, lsl #32
    // 0xeb6438: r0 = LoadClassIdInstr(r1)
    //     0xeb6438: ldur            x0, [x1, #-1]
    //     0xeb643c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb6440: ldur            x2, [fp, #-0x40]
    // 0xeb6444: r3 = 0
    //     0xeb6444: movz            x3, #0
    // 0xeb6448: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb6448: sub             lr, x0, #1, lsl #12
    //     0xeb644c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb6450: blr             lr
    // 0xeb6454: ldur            x2, [fp, #-8]
    // 0xeb6458: r0 = LoadClassIdInstr(r2)
    //     0xeb6458: ldur            x0, [x2, #-1]
    //     0xeb645c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb6460: mov             x1, x2
    // 0xeb6464: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb6464: sub             lr, x0, #1, lsl #12
    //     0xeb6468: ldr             lr, [x21, lr, lsl #3]
    //     0xeb646c: blr             lr
    // 0xeb6470: mov             x1, x0
    // 0xeb6474: ldur            x0, [fp, #-0x30]
    // 0xeb6478: add             x6, x0, x1
    // 0xeb647c: ldur            x1, [fp, #-0x10]
    // 0xeb6480: ldur            x2, [fp, #-0x40]
    // 0xeb6484: ldur            x5, [fp, #-0x28]
    // 0xeb6488: r3 = 0
    //     0xeb6488: movz            x3, #0
    // 0xeb648c: r0 = processBlock()
    //     0xeb648c: bl              #0xeb377c  ; [package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart] PaddedBlockCipherImpl::processBlock
    // 0xeb6490: ldur            x1, [fp, #-8]
    // 0xeb6494: r0 = LoadClassIdInstr(r1)
    //     0xeb6494: ldur            x0, [x1, #-1]
    //     0xeb6498: ubfx            x0, x0, #0xc, #0x14
    // 0xeb649c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb649c: sub             lr, x0, #1, lsl #12
    //     0xeb64a0: ldr             lr, [x21, lr, lsl #3]
    //     0xeb64a4: blr             lr
    // 0xeb64a8: lsl             x1, x0, #1
    // 0xeb64ac: mov             x0, x1
    // 0xeb64b0: LeaveFrame
    //     0xeb64b0: mov             SP, fp
    //     0xeb64b4: ldp             fp, lr, [SP], #0x10
    // 0xeb64b8: ret
    //     0xeb64b8: ret             
    // 0xeb64bc: mov             x7, x5
    // 0xeb64c0: mov             x0, x6
    // 0xeb64c4: mov             x1, x4
    // 0xeb64c8: ldur            x2, [fp, #-0x18]
    // 0xeb64cc: ldur            x3, [fp, #-0x20]
    // 0xeb64d0: mov             x5, x7
    // 0xeb64d4: mov             x6, x0
    // 0xeb64d8: r0 = processBlock()
    //     0xeb64d8: bl              #0xeb377c  ; [package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart] PaddedBlockCipherImpl::processBlock
    // 0xeb64dc: ldur            x0, [fp, #-0x10]
    // 0xeb64e0: LoadField: r3 = r0->field_7
    //     0xeb64e0: ldur            w3, [x0, #7]
    // 0xeb64e4: DecompressPointer r3
    //     0xeb64e4: add             x3, x3, HEAP, lsl #32
    // 0xeb64e8: ldur            x1, [fp, #-0x28]
    // 0xeb64ec: ldur            x2, [fp, #-0x30]
    // 0xeb64f0: stur            x3, [fp, #-8]
    // 0xeb64f4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb64f4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb64f8: r0 = sublist()
    //     0xeb64f8: bl              #0x6ea018  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0xeb64fc: ldur            x1, [fp, #-8]
    // 0xeb6500: r2 = LoadClassIdInstr(r1)
    //     0xeb6500: ldur            x2, [x1, #-1]
    //     0xeb6504: ubfx            x2, x2, #0xc, #0x14
    // 0xeb6508: mov             x16, x0
    // 0xeb650c: mov             x0, x2
    // 0xeb6510: mov             x2, x16
    // 0xeb6514: r0 = GDT[cid_x0 + -0xffd]()
    //     0xeb6514: sub             lr, x0, #0xffd
    //     0xeb6518: ldr             lr, [x21, lr, lsl #3]
    //     0xeb651c: blr             lr
    // 0xeb6520: mov             x2, x0
    // 0xeb6524: ldur            x0, [fp, #-0x10]
    // 0xeb6528: stur            x2, [fp, #-0x20]
    // 0xeb652c: LoadField: r1 = r0->field_b
    //     0xeb652c: ldur            w1, [x0, #0xb]
    // 0xeb6530: DecompressPointer r1
    //     0xeb6530: add             x1, x1, HEAP, lsl #32
    // 0xeb6534: r0 = LoadClassIdInstr(r1)
    //     0xeb6534: ldur            x0, [x1, #-1]
    //     0xeb6538: ubfx            x0, x0, #0xc, #0x14
    // 0xeb653c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb653c: sub             lr, x0, #1, lsl #12
    //     0xeb6540: ldr             lr, [x21, lr, lsl #3]
    //     0xeb6544: blr             lr
    // 0xeb6548: mov             x1, x0
    // 0xeb654c: ldur            x0, [fp, #-0x20]
    // 0xeb6550: sub             x4, x1, x0
    // 0xeb6554: ldur            x0, [fp, #-0x30]
    // 0xeb6558: stur            x4, [fp, #-0x38]
    // 0xeb655c: add             x2, x0, x4
    // 0xeb6560: ldur            x1, [fp, #-0x28]
    // 0xeb6564: LoadField: r0 = r1->field_13
    //     0xeb6564: ldur            w0, [x1, #0x13]
    // 0xeb6568: r3 = LoadInt32Instr(r0)
    //     0xeb6568: sbfx            x3, x0, #1, #0x1f
    // 0xeb656c: r5 = 0
    //     0xeb656c: movz            x5, #0
    // 0xeb6570: r0 = fillRange()
    //     0xeb6570: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0xeb6574: ldur            x0, [fp, #-0x38]
    // 0xeb6578: LeaveFrame
    //     0xeb6578: mov             SP, fp
    //     0xeb657c: ldp             fp, lr, [SP], #0x10
    // 0xeb6580: ret
    //     0xeb6580: ret             
    // 0xeb6584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6584: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6588: b               #0xeb6170
    // 0xeb658c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb658c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ blockSize(/* No info */) {
    // ** addr: 0xeb6764, size: 0x50
    // 0xeb6764: EnterFrame
    //     0xeb6764: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6768: mov             fp, SP
    // 0xeb676c: CheckStackOverflow
    //     0xeb676c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6770: cmp             SP, x16
    //     0xeb6774: b.ls            #0xeb67ac
    // 0xeb6778: LoadField: r0 = r1->field_b
    //     0xeb6778: ldur            w0, [x1, #0xb]
    // 0xeb677c: DecompressPointer r0
    //     0xeb677c: add             x0, x0, HEAP, lsl #32
    // 0xeb6780: r1 = LoadClassIdInstr(r0)
    //     0xeb6780: ldur            x1, [x0, #-1]
    //     0xeb6784: ubfx            x1, x1, #0xc, #0x14
    // 0xeb6788: mov             x16, x0
    // 0xeb678c: mov             x0, x1
    // 0xeb6790: mov             x1, x16
    // 0xeb6794: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb6794: sub             lr, x0, #1, lsl #12
    //     0xeb6798: ldr             lr, [x21, lr, lsl #3]
    //     0xeb679c: blr             lr
    // 0xeb67a0: LeaveFrame
    //     0xeb67a0: mov             SP, fp
    //     0xeb67a4: ldp             fp, lr, [SP], #0x10
    // 0xeb67a8: ret
    //     0xeb67a8: ret             
    // 0xeb67ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb67ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb67b0: b               #0xeb6778
  }
}
