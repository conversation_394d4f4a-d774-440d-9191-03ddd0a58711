// lib: impl.padding.iso7816d4, url: package:pointycastle/paddings/iso7816d4.dart

// class id: 1051021, size: 0x8
class :: {
}

// class id: 572, size: 0x8, field offset: 0x8
class ISO7816d4Padding extends BasePadding {

  static late final FactoryConfig factoryConfig; // offset: 0xe74

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c68f8, size: 0x58
    // 0x8c68f8: EnterFrame
    //     0x8c68f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c68fc: mov             fp, SP
    // 0x8c6900: AllocStack(0x8)
    //     0x8c6900: sub             SP, SP, #8
    // 0x8c6904: r0 = StaticFactoryConfig()
    //     0x8c6904: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c6908: mov             x3, x0
    // 0x8c690c: r0 = "ISO7816-4"
    //     0x8c690c: add             x0, PP, #0x18, lsl #12  ; [pp+0x18320] "ISO7816-4"
    //     0x8c6910: ldr             x0, [x0, #0x320]
    // 0x8c6914: stur            x3, [fp, #-8]
    // 0x8c6918: StoreField: r3->field_b = r0
    //     0x8c6918: stur            w0, [x3, #0xb]
    // 0x8c691c: r1 = Function '<anonymous closure>': static.
    //     0x8c691c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18328] AnonymousClosure: static (0x8c6950), in [package:pointycastle/paddings/iso7816d4.dart] ISO7816d4Padding::factoryConfig (0x8c68f8)
    //     0x8c6920: ldr             x1, [x1, #0x328]
    // 0x8c6924: r2 = Null
    //     0x8c6924: mov             x2, NULL
    // 0x8c6928: r0 = AllocateClosure()
    //     0x8c6928: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c692c: mov             x1, x0
    // 0x8c6930: ldur            x0, [fp, #-8]
    // 0x8c6934: StoreField: r0->field_f = r1
    //     0x8c6934: stur            w1, [x0, #0xf]
    // 0x8c6938: r1 = Padding
    //     0x8c6938: add             x1, PP, #0x18, lsl #12  ; [pp+0x18330] Type: Padding
    //     0x8c693c: ldr             x1, [x1, #0x330]
    // 0x8c6940: StoreField: r0->field_7 = r1
    //     0x8c6940: stur            w1, [x0, #7]
    // 0x8c6944: LeaveFrame
    //     0x8c6944: mov             SP, fp
    //     0x8c6948: ldp             fp, lr, [SP], #0x10
    // 0x8c694c: ret
    //     0x8c694c: ret             
  }
  [closure] static ISO7816d4Padding <anonymous closure>(dynamic) {
    // ** addr: 0x8c6950, size: 0x18
    // 0x8c6950: EnterFrame
    //     0x8c6950: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6954: mov             fp, SP
    // 0x8c6958: r0 = ISO7816d4Padding()
    //     0x8c6958: bl              #0x8c6968  ; AllocateISO7816d4PaddingStub -> ISO7816d4Padding (size=0x8)
    // 0x8c695c: LeaveFrame
    //     0x8c695c: mov             SP, fp
    //     0x8c6960: ldp             fp, lr, [SP], #0x10
    // 0x8c6964: ret
    //     0x8c6964: ret             
  }
  _ padCount(/* No info */) {
    // ** addr: 0xeb6590, size: 0xa8
    // 0xeb6590: EnterFrame
    //     0xeb6590: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6594: mov             fp, SP
    // 0xeb6598: LoadField: r0 = r2->field_13
    //     0xeb6598: ldur            w0, [x2, #0x13]
    // 0xeb659c: r3 = LoadInt32Instr(r0)
    //     0xeb659c: sbfx            x3, x0, #1, #0x1f
    // 0xeb65a0: sub             x0, x3, #1
    // 0xeb65a4: mov             x4, x0
    // 0xeb65a8: CheckStackOverflow
    //     0xeb65a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb65ac: cmp             SP, x16
    //     0xeb65b0: b.ls            #0xeb662c
    // 0xeb65b4: cmp             x4, #0
    // 0xeb65b8: b.le            #0xeb65d4
    // 0xeb65bc: ArrayLoad: r0 = r2[r4]  ; List_1
    //     0xeb65bc: add             x16, x2, x4
    //     0xeb65c0: ldrb            w0, [x16, #0x17]
    // 0xeb65c4: cbnz            x0, #0xeb65d4
    // 0xeb65c8: sub             x0, x4, #1
    // 0xeb65cc: mov             x4, x0
    // 0xeb65d0: b               #0xeb65a8
    // 0xeb65d4: mov             x0, x3
    // 0xeb65d8: mov             x1, x4
    // 0xeb65dc: cmp             x1, x0
    // 0xeb65e0: b.hs            #0xeb6634
    // 0xeb65e4: ArrayLoad: r0 = r2[r4]  ; List_1
    //     0xeb65e4: add             x16, x2, x4
    //     0xeb65e8: ldrb            w0, [x16, #0x17]
    // 0xeb65ec: cmp             x0, #0x80
    // 0xeb65f0: b.ne            #0xeb6604
    // 0xeb65f4: sub             x0, x3, x4
    // 0xeb65f8: LeaveFrame
    //     0xeb65f8: mov             SP, fp
    //     0xeb65fc: ldp             fp, lr, [SP], #0x10
    // 0xeb6600: ret
    //     0xeb6600: ret             
    // 0xeb6604: r0 = ArgumentError()
    //     0xeb6604: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xeb6608: mov             x1, x0
    // 0xeb660c: r0 = "pad block corrupted"
    //     0xeb660c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23660] "pad block corrupted"
    //     0xeb6610: ldr             x0, [x0, #0x660]
    // 0xeb6614: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb6614: stur            w0, [x1, #0x17]
    // 0xeb6618: r0 = false
    //     0xeb6618: add             x0, NULL, #0x30  ; false
    // 0xeb661c: StoreField: r1->field_b = r0
    //     0xeb661c: stur            w0, [x1, #0xb]
    // 0xeb6620: mov             x0, x1
    // 0xeb6624: r0 = Throw()
    //     0xeb6624: bl              #0xec04b8  ; ThrowStub
    // 0xeb6628: brk             #0
    // 0xeb662c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb662c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6630: b               #0xeb65b4
    // 0xeb6634: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb6634: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ addPadding(/* No info */) {
    // ** addr: 0xeb67b4, size: 0x84
    // 0xeb67b4: EnterFrame
    //     0xeb67b4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb67b8: mov             fp, SP
    // 0xeb67bc: r4 = 128
    //     0xeb67bc: movz            x4, #0x80
    // 0xeb67c0: mov             x16, x3
    // 0xeb67c4: mov             x3, x2
    // 0xeb67c8: mov             x2, x16
    // 0xeb67cc: LoadField: r5 = r3->field_13
    //     0xeb67cc: ldur            w5, [x3, #0x13]
    // 0xeb67d0: r6 = LoadInt32Instr(r5)
    //     0xeb67d0: sbfx            x6, x5, #1, #0x1f
    // 0xeb67d4: sub             x5, x6, x2
    // 0xeb67d8: mov             x0, x6
    // 0xeb67dc: mov             x1, x2
    // 0xeb67e0: cmp             x1, x0
    // 0xeb67e4: b.hs            #0xeb682c
    // 0xeb67e8: ArrayStore: r3[r2] = r4  ; TypeUnknown_1
    //     0xeb67e8: add             x1, x3, x2
    //     0xeb67ec: strb            w4, [x1, #0x17]
    // 0xeb67f0: add             x1, x2, #1
    // 0xeb67f4: CheckStackOverflow
    //     0xeb67f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb67f8: cmp             SP, x16
    //     0xeb67fc: b.ls            #0xeb6830
    // 0xeb6800: cmp             x1, x6
    // 0xeb6804: b.ge            #0xeb681c
    // 0xeb6808: ArrayStore: r3[r1] = rZR  ; TypeUnknown_1
    //     0xeb6808: add             x2, x3, x1
    //     0xeb680c: strb            wzr, [x2, #0x17]
    // 0xeb6810: add             x0, x1, #1
    // 0xeb6814: mov             x1, x0
    // 0xeb6818: b               #0xeb67f4
    // 0xeb681c: mov             x0, x5
    // 0xeb6820: LeaveFrame
    //     0xeb6820: mov             SP, fp
    //     0xeb6824: ldp             fp, lr, [SP], #0x10
    // 0xeb6828: ret
    //     0xeb6828: ret             
    // 0xeb682c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb682c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeb6830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6830: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6834: b               #0xeb6800
  }
}
