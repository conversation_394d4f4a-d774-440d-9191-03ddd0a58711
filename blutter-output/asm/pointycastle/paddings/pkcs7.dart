// lib: impl.padding.pkcs7, url: package:pointycastle/paddings/pkcs7.dart

// class id: 1051022, size: 0x8
class :: {
}

// class id: 571, size: 0x8, field offset: 0x8
class PKCS7Padding extends BasePadding {

  static late final FactoryConfig factoryConfig; // offset: 0xe70

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c6974, size: 0x58
    // 0x8c6974: EnterFrame
    //     0x8c6974: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6978: mov             fp, SP
    // 0x8c697c: AllocStack(0x8)
    //     0x8c697c: sub             SP, SP, #8
    // 0x8c6980: r0 = StaticFactoryConfig()
    //     0x8c6980: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c6984: mov             x3, x0
    // 0x8c6988: r0 = "PKCS7"
    //     0x8c6988: add             x0, PP, #0x17, lsl #12  ; [pp+0x17f60] "PKCS7"
    //     0x8c698c: ldr             x0, [x0, #0xf60]
    // 0x8c6990: stur            x3, [fp, #-8]
    // 0x8c6994: StoreField: r3->field_b = r0
    //     0x8c6994: stur            w0, [x3, #0xb]
    // 0x8c6998: r1 = Function '<anonymous closure>': static.
    //     0x8c6998: add             x1, PP, #0x18, lsl #12  ; [pp+0x18338] AnonymousClosure: static (0x8c69cc), in [package:pointycastle/paddings/pkcs7.dart] PKCS7Padding::factoryConfig (0x8c6974)
    //     0x8c699c: ldr             x1, [x1, #0x338]
    // 0x8c69a0: r2 = Null
    //     0x8c69a0: mov             x2, NULL
    // 0x8c69a4: r0 = AllocateClosure()
    //     0x8c69a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c69a8: mov             x1, x0
    // 0x8c69ac: ldur            x0, [fp, #-8]
    // 0x8c69b0: StoreField: r0->field_f = r1
    //     0x8c69b0: stur            w1, [x0, #0xf]
    // 0x8c69b4: r1 = Padding
    //     0x8c69b4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18330] Type: Padding
    //     0x8c69b8: ldr             x1, [x1, #0x330]
    // 0x8c69bc: StoreField: r0->field_7 = r1
    //     0x8c69bc: stur            w1, [x0, #7]
    // 0x8c69c0: LeaveFrame
    //     0x8c69c0: mov             SP, fp
    //     0x8c69c4: ldp             fp, lr, [SP], #0x10
    // 0x8c69c8: ret
    //     0x8c69c8: ret             
  }
  [closure] static PKCS7Padding <anonymous closure>(dynamic) {
    // ** addr: 0x8c69cc, size: 0x18
    // 0x8c69cc: EnterFrame
    //     0x8c69cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c69d0: mov             fp, SP
    // 0x8c69d4: r0 = PKCS7Padding()
    //     0x8c69d4: bl              #0x8c69e4  ; AllocatePKCS7PaddingStub -> PKCS7Padding (size=0x8)
    // 0x8c69d8: LeaveFrame
    //     0x8c69d8: mov             SP, fp
    //     0x8c69dc: ldp             fp, lr, [SP], #0x10
    // 0x8c69e0: ret
    //     0x8c69e0: ret             
  }
  _ padCount(/* No info */) {
    // ** addr: 0xeb6638, size: 0x12c
    // 0xeb6638: EnterFrame
    //     0xeb6638: stp             fp, lr, [SP, #-0x10]!
    //     0xeb663c: mov             fp, SP
    // 0xeb6640: r3 = 255
    //     0xeb6640: movz            x3, #0xff
    // 0xeb6644: LoadField: r0 = r2->field_13
    //     0xeb6644: ldur            w0, [x2, #0x13]
    // 0xeb6648: r4 = LoadInt32Instr(r0)
    //     0xeb6648: sbfx            x4, x0, #1, #0x1f
    // 0xeb664c: sub             x5, x4, #1
    // 0xeb6650: mov             x0, x4
    // 0xeb6654: mov             x1, x5
    // 0xeb6658: cmp             x1, x0
    // 0xeb665c: b.hs            #0xeb6754
    // 0xeb6660: ArrayLoad: r0 = r2[r5]  ; List_1
    //     0xeb6660: add             x16, x2, x5
    //     0xeb6664: ldrb            w0, [x16, #0x17]
    // 0xeb6668: ubfx            x0, x0, #0, #0x20
    // 0xeb666c: and             x5, x0, x3
    // 0xeb6670: mov             x0, x5
    // 0xeb6674: ubfx            x0, x0, #0, #0x20
    // 0xeb6678: cmp             x0, x4
    // 0xeb667c: b.gt            #0xeb66f8
    // 0xeb6680: mov             x0, x5
    // 0xeb6684: ubfx            x0, x0, #0, #0x20
    // 0xeb6688: cbz             x0, #0xeb66f8
    // 0xeb668c: r3 = 1
    //     0xeb668c: movz            x3, #0x1
    // 0xeb6690: CheckStackOverflow
    //     0xeb6690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6694: cmp             SP, x16
    //     0xeb6698: b.ls            #0xeb6758
    // 0xeb669c: mov             x0, x5
    // 0xeb66a0: ubfx            x0, x0, #0, #0x20
    // 0xeb66a4: cmp             x3, x0
    // 0xeb66a8: b.gt            #0xeb66e4
    // 0xeb66ac: sub             x6, x4, x3
    // 0xeb66b0: mov             x0, x4
    // 0xeb66b4: mov             x1, x6
    // 0xeb66b8: cmp             x1, x0
    // 0xeb66bc: b.hs            #0xeb6760
    // 0xeb66c0: ArrayLoad: r0 = r2[r6]  ; List_1
    //     0xeb66c0: add             x16, x2, x6
    //     0xeb66c4: ldrb            w0, [x16, #0x17]
    // 0xeb66c8: mov             x1, x5
    // 0xeb66cc: ubfx            x1, x1, #0, #0x20
    // 0xeb66d0: cmp             x0, x1
    // 0xeb66d4: b.ne            #0xeb6720
    // 0xeb66d8: add             x0, x3, #1
    // 0xeb66dc: mov             x3, x0
    // 0xeb66e0: b               #0xeb6690
    // 0xeb66e4: ubfx            x5, x5, #0, #0x20
    // 0xeb66e8: mov             x0, x5
    // 0xeb66ec: LeaveFrame
    //     0xeb66ec: mov             SP, fp
    //     0xeb66f0: ldp             fp, lr, [SP], #0x10
    // 0xeb66f4: ret
    //     0xeb66f4: ret             
    // 0xeb66f8: r0 = ArgumentError()
    //     0xeb66f8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xeb66fc: mov             x1, x0
    // 0xeb6700: r0 = "Invalid or corrupted pad block"
    //     0xeb6700: add             x0, PP, #0x23, lsl #12  ; [pp+0x23668] "Invalid or corrupted pad block"
    //     0xeb6704: ldr             x0, [x0, #0x668]
    // 0xeb6708: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb6708: stur            w0, [x1, #0x17]
    // 0xeb670c: r2 = false
    //     0xeb670c: add             x2, NULL, #0x30  ; false
    // 0xeb6710: StoreField: r1->field_b = r2
    //     0xeb6710: stur            w2, [x1, #0xb]
    // 0xeb6714: mov             x0, x1
    // 0xeb6718: r0 = Throw()
    //     0xeb6718: bl              #0xec04b8  ; ThrowStub
    // 0xeb671c: brk             #0
    // 0xeb6720: r0 = "Invalid or corrupted pad block"
    //     0xeb6720: add             x0, PP, #0x23, lsl #12  ; [pp+0x23668] "Invalid or corrupted pad block"
    //     0xeb6724: ldr             x0, [x0, #0x668]
    // 0xeb6728: r2 = false
    //     0xeb6728: add             x2, NULL, #0x30  ; false
    // 0xeb672c: r0 = ArgumentError()
    //     0xeb672c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xeb6730: mov             x1, x0
    // 0xeb6734: r0 = "Invalid or corrupted pad block"
    //     0xeb6734: add             x0, PP, #0x23, lsl #12  ; [pp+0x23668] "Invalid or corrupted pad block"
    //     0xeb6738: ldr             x0, [x0, #0x668]
    // 0xeb673c: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb673c: stur            w0, [x1, #0x17]
    // 0xeb6740: r0 = false
    //     0xeb6740: add             x0, NULL, #0x30  ; false
    // 0xeb6744: StoreField: r1->field_b = r0
    //     0xeb6744: stur            w0, [x1, #0xb]
    // 0xeb6748: mov             x0, x1
    // 0xeb674c: r0 = Throw()
    //     0xeb674c: bl              #0xec04b8  ; ThrowStub
    // 0xeb6750: brk             #0
    // 0xeb6754: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb6754: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeb6758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6758: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb675c: b               #0xeb669c
    // 0xeb6760: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb6760: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ addPadding(/* No info */) {
    // ** addr: 0xeb6838, size: 0x68
    // 0xeb6838: EnterFrame
    //     0xeb6838: stp             fp, lr, [SP, #-0x10]!
    //     0xeb683c: mov             fp, SP
    // 0xeb6840: LoadField: r4 = r2->field_13
    //     0xeb6840: ldur            w4, [x2, #0x13]
    // 0xeb6844: r5 = LoadInt32Instr(r4)
    //     0xeb6844: sbfx            x5, x4, #1, #0x1f
    // 0xeb6848: sub             x4, x5, x3
    // 0xeb684c: CheckStackOverflow
    //     0xeb684c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6850: cmp             SP, x16
    //     0xeb6854: b.ls            #0xeb6894
    // 0xeb6858: cmp             x3, x5
    // 0xeb685c: b.ge            #0xeb6884
    // 0xeb6860: mov             x0, x5
    // 0xeb6864: mov             x1, x3
    // 0xeb6868: cmp             x1, x0
    // 0xeb686c: b.hs            #0xeb689c
    // 0xeb6870: ArrayStore: r2[r3] = r4  ; TypeUnknown_1
    //     0xeb6870: add             x1, x2, x3
    //     0xeb6874: strb            w4, [x1, #0x17]
    // 0xeb6878: add             x0, x3, #1
    // 0xeb687c: mov             x3, x0
    // 0xeb6880: b               #0xeb684c
    // 0xeb6884: mov             x0, x4
    // 0xeb6888: LeaveFrame
    //     0xeb6888: mov             SP, fp
    //     0xeb688c: ldp             fp, lr, [SP], #0x10
    // 0xeb6890: ret
    //     0xeb6890: ret             
    // 0xeb6894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6894: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6898: b               #0xeb6858
    // 0xeb689c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb689c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
