// lib: impl.secure_random.auto_seed_block_ctr_random, url: package:pointycastle/random/auto_seed_block_ctr_random.dart

// class id: 1051023, size: 0x8
class :: {
}

// class id: 569, size: 0x18, field offset: 0x8
class AutoSeedBlockCtrRandom extends Object
    implements SecureRandom {

  static late final FactoryConfig factoryConfig; // offset: 0xe78
  late BlockCtrRandom _delegate; // offset: 0x8
  late int _autoReseedKeyLength; // offset: 0x14

  _ AutoSeedBlockCtrRandom(/* No info */) {
    // ** addr: 0x8c62f4, size: 0xac
    // 0x8c62f4: EnterFrame
    //     0x8c62f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c62f8: mov             fp, SP
    // 0x8c62fc: AllocStack(0x10)
    //     0x8c62fc: sub             SP, SP, #0x10
    // 0x8c6300: SetupParameters(AutoSeedBlockCtrRandom this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, [dynamic _ = true /* r4 */])
    //     0x8c6300: stur            x1, [fp, #-8]
    //     0x8c6304: stur            x2, [fp, #-0x10]
    //     0x8c6308: ldur            w0, [x4, #0x13]
    //     0x8c630c: sub             x3, x0, #4
    //     0x8c6310: cmp             w3, #2
    //     0x8c6314: b.lt            #0x8c6328
    //     0x8c6318: add             x0, fp, w3, sxtw #2
    //     0x8c631c: ldr             x0, [x0, #8]
    //     0x8c6320: mov             x4, x0
    //     0x8c6324: b               #0x8c632c
    //     0x8c6328: add             x4, NULL, #0x20  ; true
    //     0x8c632c: ldr             x3, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6330: add             x0, NULL, #0x30  ; false
    // 0x8c632c: r3 = Sentinel
    // 0x8c6330: r0 = false
    // 0x8c6334: CheckStackOverflow
    //     0x8c6334: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6338: cmp             SP, x16
    //     0x8c633c: b.ls            #0x8c6398
    // 0x8c6340: StoreField: r1->field_7 = r3
    //     0x8c6340: stur            w3, [x1, #7]
    // 0x8c6344: StoreField: r1->field_f = r0
    //     0x8c6344: stur            w0, [x1, #0xf]
    // 0x8c6348: StoreField: r1->field_13 = r3
    //     0x8c6348: stur            w3, [x1, #0x13]
    // 0x8c634c: StoreField: r1->field_b = r4
    //     0x8c634c: stur            w4, [x1, #0xb]
    // 0x8c6350: r0 = BlockCtrRandom()
    //     0x8c6350: bl              #0x8c64c8  ; AllocateBlockCtrRandomStub -> BlockCtrRandom (size=0x18)
    // 0x8c6354: mov             x1, x0
    // 0x8c6358: ldur            x2, [fp, #-0x10]
    // 0x8c635c: stur            x0, [fp, #-0x10]
    // 0x8c6360: r0 = BlockCtrRandom()
    //     0x8c6360: bl              #0x8c63a0  ; [package:pointycastle/random/block_ctr_random.dart] BlockCtrRandom::BlockCtrRandom
    // 0x8c6364: ldur            x0, [fp, #-0x10]
    // 0x8c6368: ldur            x1, [fp, #-8]
    // 0x8c636c: StoreField: r1->field_7 = r0
    //     0x8c636c: stur            w0, [x1, #7]
    //     0x8c6370: ldurb           w16, [x1, #-1]
    //     0x8c6374: ldurb           w17, [x0, #-1]
    //     0x8c6378: and             x16, x17, x16, lsr #2
    //     0x8c637c: tst             x16, HEAP, lsr #32
    //     0x8c6380: b.eq            #0x8c6388
    //     0x8c6384: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c6388: r0 = Null
    //     0x8c6388: mov             x0, NULL
    // 0x8c638c: LeaveFrame
    //     0x8c638c: mov             SP, fp
    //     0x8c6390: ldp             fp, lr, [SP], #0x10
    // 0x8c6394: ret
    //     0x8c6394: ret             
    // 0x8c6398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c639c: b               #0x8c6340
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c66b0, size: 0x98
    // 0x8c66b0: EnterFrame
    //     0x8c66b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c66b4: mov             fp, SP
    // 0x8c66b8: AllocStack(0x40)
    //     0x8c66b8: sub             SP, SP, #0x40
    // 0x8c66bc: CheckStackOverflow
    //     0x8c66bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c66c0: cmp             SP, x16
    //     0x8c66c4: b.ls            #0x8c6740
    // 0x8c66c8: r16 = "^(.*)/CTR/AUTO-SEED-PRNG$"
    //     0x8c66c8: add             x16, PP, #0x18, lsl #12  ; [pp+0x182f8] "^(.*)/CTR/AUTO-SEED-PRNG$"
    //     0x8c66cc: ldr             x16, [x16, #0x2f8]
    // 0x8c66d0: stp             x16, NULL, [SP, #0x20]
    // 0x8c66d4: r16 = false
    //     0x8c66d4: add             x16, NULL, #0x30  ; false
    // 0x8c66d8: r30 = true
    //     0x8c66d8: add             lr, NULL, #0x20  ; true
    // 0x8c66dc: stp             lr, x16, [SP, #0x10]
    // 0x8c66e0: r16 = false
    //     0x8c66e0: add             x16, NULL, #0x30  ; false
    // 0x8c66e4: r30 = false
    //     0x8c66e4: add             lr, NULL, #0x30  ; false
    // 0x8c66e8: stp             lr, x16, [SP]
    // 0x8c66ec: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8c66ec: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8c66f0: r0 = _RegExp()
    //     0x8c66f0: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8c66f4: stur            x0, [fp, #-8]
    // 0x8c66f8: r0 = DynamicFactoryConfig()
    //     0x8c66f8: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c66fc: mov             x3, x0
    // 0x8c6700: ldur            x0, [fp, #-8]
    // 0x8c6704: stur            x3, [fp, #-0x10]
    // 0x8c6708: StoreField: r3->field_b = r0
    //     0x8c6708: stur            w0, [x3, #0xb]
    // 0x8c670c: r1 = Function '<anonymous closure>': static.
    //     0x8c670c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18300] AnonymousClosure: static (0x8c6748), in [package:pointycastle/random/auto_seed_block_ctr_random.dart] AutoSeedBlockCtrRandom::factoryConfig (0x8c66b0)
    //     0x8c6710: ldr             x1, [x1, #0x300]
    // 0x8c6714: r2 = Null
    //     0x8c6714: mov             x2, NULL
    // 0x8c6718: r0 = AllocateClosure()
    //     0x8c6718: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c671c: mov             x1, x0
    // 0x8c6720: ldur            x0, [fp, #-0x10]
    // 0x8c6724: StoreField: r0->field_f = r1
    //     0x8c6724: stur            w1, [x0, #0xf]
    // 0x8c6728: r1 = SecureRandom
    //     0x8c6728: add             x1, PP, #0x18, lsl #12  ; [pp+0x182d8] Type: SecureRandom
    //     0x8c672c: ldr             x1, [x1, #0x2d8]
    // 0x8c6730: StoreField: r0->field_7 = r1
    //     0x8c6730: stur            w1, [x0, #7]
    // 0x8c6734: LeaveFrame
    //     0x8c6734: mov             SP, fp
    //     0x8c6738: ldp             fp, lr, [SP], #0x10
    // 0x8c673c: ret
    //     0x8c673c: ret             
    // 0x8c6740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6740: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6744: b               #0x8c66c8
  }
  [closure] static (dynamic) => AutoSeedBlockCtrRandom <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c6748, size: 0x54
    // 0x8c6748: EnterFrame
    //     0x8c6748: stp             fp, lr, [SP, #-0x10]!
    //     0x8c674c: mov             fp, SP
    // 0x8c6750: AllocStack(0x8)
    //     0x8c6750: sub             SP, SP, #8
    // 0x8c6754: SetupParameters()
    //     0x8c6754: ldr             x0, [fp, #0x20]
    //     0x8c6758: ldur            w1, [x0, #0x17]
    //     0x8c675c: add             x1, x1, HEAP, lsl #32
    //     0x8c6760: stur            x1, [fp, #-8]
    // 0x8c6764: r1 = 1
    //     0x8c6764: movz            x1, #0x1
    // 0x8c6768: r0 = AllocateContext()
    //     0x8c6768: bl              #0xec126c  ; AllocateContextStub
    // 0x8c676c: mov             x1, x0
    // 0x8c6770: ldur            x0, [fp, #-8]
    // 0x8c6774: StoreField: r1->field_b = r0
    //     0x8c6774: stur            w0, [x1, #0xb]
    // 0x8c6778: ldr             x0, [fp, #0x10]
    // 0x8c677c: StoreField: r1->field_f = r0
    //     0x8c677c: stur            w0, [x1, #0xf]
    // 0x8c6780: mov             x2, x1
    // 0x8c6784: r1 = Function '<anonymous closure>': static.
    //     0x8c6784: add             x1, PP, #0x18, lsl #12  ; [pp+0x18308] AnonymousClosure: static (0x8c679c), in [package:pointycastle/random/auto_seed_block_ctr_random.dart] AutoSeedBlockCtrRandom::factoryConfig (0x8c66b0)
    //     0x8c6788: ldr             x1, [x1, #0x308]
    // 0x8c678c: r0 = AllocateClosure()
    //     0x8c678c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c6790: LeaveFrame
    //     0x8c6790: mov             SP, fp
    //     0x8c6794: ldp             fp, lr, [SP], #0x10
    // 0x8c6798: ret
    //     0x8c6798: ret             
  }
  [closure] static AutoSeedBlockCtrRandom <anonymous closure>(dynamic) {
    // ** addr: 0x8c679c, size: 0xd0
    // 0x8c679c: EnterFrame
    //     0x8c679c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c67a0: mov             fp, SP
    // 0x8c67a4: AllocStack(0x20)
    //     0x8c67a4: sub             SP, SP, #0x20
    // 0x8c67a8: SetupParameters()
    //     0x8c67a8: ldr             x0, [fp, #0x10]
    //     0x8c67ac: ldur            w1, [x0, #0x17]
    //     0x8c67b0: add             x1, x1, HEAP, lsl #32
    // 0x8c67b4: CheckStackOverflow
    //     0x8c67b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c67b8: cmp             SP, x16
    //     0x8c67bc: b.ls            #0x8c6860
    // 0x8c67c0: LoadField: r0 = r1->field_f
    //     0x8c67c0: ldur            w0, [x1, #0xf]
    // 0x8c67c4: DecompressPointer r0
    //     0x8c67c4: add             x0, x0, HEAP, lsl #32
    // 0x8c67c8: r1 = LoadClassIdInstr(r0)
    //     0x8c67c8: ldur            x1, [x0, #-1]
    //     0x8c67cc: ubfx            x1, x1, #0xc, #0x14
    // 0x8c67d0: mov             x16, x0
    // 0x8c67d4: mov             x0, x1
    // 0x8c67d8: mov             x1, x16
    // 0x8c67dc: r2 = 1
    //     0x8c67dc: movz            x2, #0x1
    // 0x8c67e0: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c67e0: sub             lr, x0, #0xfdd
    //     0x8c67e4: ldr             lr, [x21, lr, lsl #3]
    //     0x8c67e8: blr             lr
    // 0x8c67ec: stur            x0, [fp, #-8]
    // 0x8c67f0: cmp             w0, NULL
    // 0x8c67f4: b.eq            #0x8c6868
    // 0x8c67f8: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c67f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c67fc: ldr             x0, [x0, #0x2e38]
    //     0x8c6800: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6804: cmp             w0, w16
    //     0x8c6808: b.ne            #0x8c6818
    //     0x8c680c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c6810: ldr             x2, [x2, #0xf80]
    //     0x8c6814: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6818: r16 = <BlockCipher>
    //     0x8c6818: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8c681c: ldr             x16, [x16, #0x88]
    // 0x8c6820: stp             x0, x16, [SP, #8]
    // 0x8c6824: ldur            x16, [fp, #-8]
    // 0x8c6828: str             x16, [SP]
    // 0x8c682c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c682c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c6830: r0 = create()
    //     0x8c6830: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c6834: stur            x0, [fp, #-8]
    // 0x8c6838: r0 = AutoSeedBlockCtrRandom()
    //     0x8c6838: bl              #0x8c64d4  ; AllocateAutoSeedBlockCtrRandomStub -> AutoSeedBlockCtrRandom (size=0x18)
    // 0x8c683c: mov             x1, x0
    // 0x8c6840: ldur            x2, [fp, #-8]
    // 0x8c6844: stur            x0, [fp, #-8]
    // 0x8c6848: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8c6848: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8c684c: r0 = AutoSeedBlockCtrRandom()
    //     0x8c684c: bl              #0x8c62f4  ; [package:pointycastle/random/auto_seed_block_ctr_random.dart] AutoSeedBlockCtrRandom::AutoSeedBlockCtrRandom
    // 0x8c6850: ldur            x0, [fp, #-8]
    // 0x8c6854: LeaveFrame
    //     0x8c6854: mov             SP, fp
    //     0x8c6858: ldp             fp, lr, [SP], #0x10
    // 0x8c685c: ret
    //     0x8c685c: ret             
    // 0x8c6860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6860: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6864: b               #0x8c67c0
    // 0x8c6868: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c6868: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ nextBigInteger(/* No info */) {
    // ** addr: 0xeb68a0, size: 0xc0
    // 0xeb68a0: EnterFrame
    //     0xeb68a0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb68a4: mov             fp, SP
    // 0xeb68a8: AllocStack(0x10)
    //     0xeb68a8: sub             SP, SP, #0x10
    // 0xeb68ac: SetupParameters(AutoSeedBlockCtrRandom this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xeb68ac: stur            x1, [fp, #-8]
    //     0xeb68b0: stur            x2, [fp, #-0x10]
    // 0xeb68b4: CheckStackOverflow
    //     0xeb68b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb68b8: cmp             SP, x16
    //     0xeb68bc: b.ls            #0xeb6958
    // 0xeb68c0: r1 = 2
    //     0xeb68c0: movz            x1, #0x2
    // 0xeb68c4: r0 = AllocateContext()
    //     0xeb68c4: bl              #0xec126c  ; AllocateContextStub
    // 0xeb68c8: mov             x2, x0
    // 0xeb68cc: ldur            x3, [fp, #-8]
    // 0xeb68d0: StoreField: r2->field_f = r3
    //     0xeb68d0: stur            w3, [x2, #0xf]
    // 0xeb68d4: ldur            x4, [fp, #-0x10]
    // 0xeb68d8: r0 = BoxInt64Instr(r4)
    //     0xeb68d8: sbfiz           x0, x4, #1, #0x1f
    //     0xeb68dc: cmp             x4, x0, asr #1
    //     0xeb68e0: b.eq            #0xeb68ec
    //     0xeb68e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb68e8: stur            x4, [x0, #7]
    // 0xeb68ec: StoreField: r2->field_13 = r0
    //     0xeb68ec: stur            w0, [x2, #0x13]
    // 0xeb68f0: r1 = Function '<anonymous closure>':.
    //     0xeb68f0: add             x1, PP, #0x21, lsl #12  ; [pp+0x21c08] AnonymousClosure: (0xeb6a38), in [package:pointycastle/random/auto_seed_block_ctr_random.dart] AutoSeedBlockCtrRandom::nextBigInteger (0xeb68a0)
    //     0xeb68f4: ldr             x1, [x1, #0xc08]
    // 0xeb68f8: r0 = AllocateClosure()
    //     0xeb68f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xeb68fc: ldur            x1, [fp, #-8]
    // 0xeb6900: mov             x2, x0
    // 0xeb6904: r0 = _autoReseedIfNeededAfter()
    //     0xeb6904: bl              #0xeb6960  ; [package:pointycastle/random/auto_seed_block_ctr_random.dart] AutoSeedBlockCtrRandom::_autoReseedIfNeededAfter
    // 0xeb6908: mov             x3, x0
    // 0xeb690c: r2 = Null
    //     0xeb690c: mov             x2, NULL
    // 0xeb6910: r1 = Null
    //     0xeb6910: mov             x1, NULL
    // 0xeb6914: stur            x3, [fp, #-8]
    // 0xeb6918: r4 = 60
    //     0xeb6918: movz            x4, #0x3c
    // 0xeb691c: branchIfSmi(r0, 0xeb6928)
    //     0xeb691c: tbz             w0, #0, #0xeb6928
    // 0xeb6920: r4 = LoadClassIdInstr(r0)
    //     0xeb6920: ldur            x4, [x0, #-1]
    //     0xeb6924: ubfx            x4, x4, #0xc, #0x14
    // 0xeb6928: r17 = 7196
    //     0xeb6928: movz            x17, #0x1c1c
    // 0xeb692c: cmp             x4, x17
    // 0xeb6930: b.eq            #0xeb6948
    // 0xeb6934: r8 = BigInt
    //     0xeb6934: add             x8, PP, #0x18, lsl #12  ; [pp+0x187d0] Type: BigInt
    //     0xeb6938: ldr             x8, [x8, #0x7d0]
    // 0xeb693c: r3 = Null
    //     0xeb693c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21c10] Null
    //     0xeb6940: ldr             x3, [x3, #0xc10]
    // 0xeb6944: r0 = BigInt()
    //     0xeb6944: bl              #0x665d98  ; IsType_BigInt_Stub
    // 0xeb6948: ldur            x0, [fp, #-8]
    // 0xeb694c: LeaveFrame
    //     0xeb694c: mov             SP, fp
    //     0xeb6950: ldp             fp, lr, [SP], #0x10
    // 0xeb6954: ret
    //     0xeb6954: ret             
    // 0xeb6958: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6958: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb695c: b               #0xeb68c0
  }
  _ _autoReseedIfNeededAfter(/* No info */) {
    // ** addr: 0xeb6960, size: 0xa4
    // 0xeb6960: EnterFrame
    //     0xeb6960: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6964: mov             fp, SP
    // 0xeb6968: AllocStack(0x18)
    //     0xeb6968: sub             SP, SP, #0x18
    // 0xeb696c: SetupParameters(AutoSeedBlockCtrRandom this /* r1 => r1, fp-0x8 */)
    //     0xeb696c: stur            x1, [fp, #-8]
    // 0xeb6970: CheckStackOverflow
    //     0xeb6970: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6974: cmp             SP, x16
    //     0xeb6978: b.ls            #0xeb69fc
    // 0xeb697c: LoadField: r0 = r1->field_f
    //     0xeb697c: ldur            w0, [x1, #0xf]
    // 0xeb6980: DecompressPointer r0
    //     0xeb6980: add             x0, x0, HEAP, lsl #32
    // 0xeb6984: tbnz            w0, #4, #0xeb69b0
    // 0xeb6988: str             x2, [SP]
    // 0xeb698c: r4 = 0
    //     0xeb698c: movz            x4, #0
    // 0xeb6990: ldr             x0, [SP]
    // 0xeb6994: r16 = UnlinkedCall_0x5f3c08
    //     0xeb6994: add             x16, PP, #0x21, lsl #12  ; [pp+0x21c28] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xeb6998: add             x16, x16, #0xc28
    // 0xeb699c: ldp             x5, lr, [x16]
    // 0xeb69a0: blr             lr
    // 0xeb69a4: LeaveFrame
    //     0xeb69a4: mov             SP, fp
    //     0xeb69a8: ldp             fp, lr, [SP], #0x10
    // 0xeb69ac: ret
    //     0xeb69ac: ret             
    // 0xeb69b0: r0 = true
    //     0xeb69b0: add             x0, NULL, #0x20  ; true
    // 0xeb69b4: StoreField: r1->field_f = r0
    //     0xeb69b4: stur            w0, [x1, #0xf]
    // 0xeb69b8: str             x2, [SP]
    // 0xeb69bc: r4 = 0
    //     0xeb69bc: movz            x4, #0
    // 0xeb69c0: ldr             x0, [SP]
    // 0xeb69c4: r16 = UnlinkedCall_0x5f3c08
    //     0xeb69c4: add             x16, PP, #0x21, lsl #12  ; [pp+0x21c38] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xeb69c8: add             x16, x16, #0xc38
    // 0xeb69cc: ldp             x5, lr, [x16]
    // 0xeb69d0: blr             lr
    // 0xeb69d4: ldur            x1, [fp, #-8]
    // 0xeb69d8: stur            x0, [fp, #-0x10]
    // 0xeb69dc: r0 = _doAutoReseed()
    //     0xeb69dc: bl              #0xeb6a04  ; [package:pointycastle/random/auto_seed_block_ctr_random.dart] AutoSeedBlockCtrRandom::_doAutoReseed
    // 0xeb69e0: ldur            x1, [fp, #-8]
    // 0xeb69e4: r2 = false
    //     0xeb69e4: add             x2, NULL, #0x30  ; false
    // 0xeb69e8: StoreField: r1->field_f = r2
    //     0xeb69e8: stur            w2, [x1, #0xf]
    // 0xeb69ec: ldur            x0, [fp, #-0x10]
    // 0xeb69f0: LeaveFrame
    //     0xeb69f0: mov             SP, fp
    //     0xeb69f4: ldp             fp, lr, [SP], #0x10
    // 0xeb69f8: ret
    //     0xeb69f8: ret             
    // 0xeb69fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb69fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6a00: b               #0xeb697c
  }
  _ _doAutoReseed(/* No info */) {
    // ** addr: 0xeb6a04, size: 0x34
    // 0xeb6a04: EnterFrame
    //     0xeb6a04: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6a08: mov             fp, SP
    // 0xeb6a0c: LoadField: r0 = r1->field_13
    //     0xeb6a0c: ldur            w0, [x1, #0x13]
    // 0xeb6a10: DecompressPointer r0
    //     0xeb6a10: add             x0, x0, HEAP, lsl #32
    // 0xeb6a14: r16 = Sentinel
    //     0xeb6a14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb6a18: cmp             w0, w16
    // 0xeb6a1c: b.eq            #0xeb6a2c
    // 0xeb6a20: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xeb6a20: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xeb6a24: r0 = Throw()
    //     0xeb6a24: bl              #0xec04b8  ; ThrowStub
    // 0xeb6a28: brk             #0
    // 0xeb6a2c: r9 = _autoReseedKeyLength
    //     0xeb6a2c: add             x9, PP, #0x21, lsl #12  ; [pp+0x21c48] Field <AutoSeedBlockCtrRandom._autoReseedKeyLength@955025418>: late (offset: 0x14)
    //     0xeb6a30: ldr             x9, [x9, #0xc48]
    // 0xeb6a34: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb6a34: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] BigInt <anonymous closure>(dynamic) {
    // ** addr: 0xeb6a38, size: 0x80
    // 0xeb6a38: EnterFrame
    //     0xeb6a38: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6a3c: mov             fp, SP
    // 0xeb6a40: ldr             x0, [fp, #0x10]
    // 0xeb6a44: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeb6a44: ldur            w1, [x0, #0x17]
    // 0xeb6a48: DecompressPointer r1
    //     0xeb6a48: add             x1, x1, HEAP, lsl #32
    // 0xeb6a4c: CheckStackOverflow
    //     0xeb6a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6a50: cmp             SP, x16
    //     0xeb6a54: b.ls            #0xeb6aa4
    // 0xeb6a58: LoadField: r0 = r1->field_f
    //     0xeb6a58: ldur            w0, [x1, #0xf]
    // 0xeb6a5c: DecompressPointer r0
    //     0xeb6a5c: add             x0, x0, HEAP, lsl #32
    // 0xeb6a60: LoadField: r2 = r0->field_7
    //     0xeb6a60: ldur            w2, [x0, #7]
    // 0xeb6a64: DecompressPointer r2
    //     0xeb6a64: add             x2, x2, HEAP, lsl #32
    // 0xeb6a68: r16 = Sentinel
    //     0xeb6a68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb6a6c: cmp             w2, w16
    // 0xeb6a70: b.eq            #0xeb6aac
    // 0xeb6a74: LoadField: r0 = r1->field_13
    //     0xeb6a74: ldur            w0, [x1, #0x13]
    // 0xeb6a78: DecompressPointer r0
    //     0xeb6a78: add             x0, x0, HEAP, lsl #32
    // 0xeb6a7c: r1 = LoadInt32Instr(r0)
    //     0xeb6a7c: sbfx            x1, x0, #1, #0x1f
    //     0xeb6a80: tbz             w0, #0, #0xeb6a88
    //     0xeb6a84: ldur            x1, [x0, #7]
    // 0xeb6a88: mov             x16, x1
    // 0xeb6a8c: mov             x1, x2
    // 0xeb6a90: mov             x2, x16
    // 0xeb6a94: r0 = nextBigInteger()
    //     0xeb6a94: bl              #0xeb6ab8  ; [package:pointycastle/src/impl/secure_random_base.dart] SecureRandomBase::nextBigInteger
    // 0xeb6a98: LeaveFrame
    //     0xeb6a98: mov             SP, fp
    //     0xeb6a9c: ldp             fp, lr, [SP], #0x10
    // 0xeb6aa0: ret
    //     0xeb6aa0: ret             
    // 0xeb6aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6aa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6aa8: b               #0xeb6a58
    // 0xeb6aac: r9 = _delegate
    //     0xeb6aac: add             x9, PP, #0x21, lsl #12  ; [pp+0x21c20] Field <AutoSeedBlockCtrRandom._delegate@955025418>: late (offset: 0x8)
    //     0xeb6ab0: ldr             x9, [x9, #0xc20]
    // 0xeb6ab4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb6ab4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
