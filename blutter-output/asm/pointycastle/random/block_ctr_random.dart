// lib: impl.secure_random.block_ctr_random, url: package:pointycastle/random/block_ctr_random.dart

// class id: 1051024, size: 0x8
class :: {
}

// class id: 568, size: 0x18, field offset: 0x8
class BlockCtrRandom extends SecureRandomBase
    implements SecureRandom {

  static late final FactoryConfig factoryConfig; // offset: 0xe7c
  late int _used; // offset: 0x14
  late Uint8List _output; // offset: 0x10
  late Uint8List _input; // offset: 0xc

  _ BlockCtrRandom(/* No info */) {
    // ** addr: 0x8c63a0, size: 0x128
    // 0x8c63a0: EnterFrame
    //     0x8c63a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c63a4: mov             fp, SP
    // 0x8c63a8: AllocStack(0x10)
    //     0x8c63a8: sub             SP, SP, #0x10
    // 0x8c63ac: r0 = Sentinel
    //     0x8c63ac: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c63b0: mov             x3, x1
    // 0x8c63b4: stur            x1, [fp, #-8]
    // 0x8c63b8: stur            x2, [fp, #-0x10]
    // 0x8c63bc: CheckStackOverflow
    //     0x8c63bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c63c0: cmp             SP, x16
    //     0x8c63c4: b.ls            #0x8c64c0
    // 0x8c63c8: StoreField: r3->field_b = r0
    //     0x8c63c8: stur            w0, [x3, #0xb]
    // 0x8c63cc: StoreField: r3->field_f = r0
    //     0x8c63cc: stur            w0, [x3, #0xf]
    // 0x8c63d0: StoreField: r3->field_13 = r0
    //     0x8c63d0: stur            w0, [x3, #0x13]
    // 0x8c63d4: mov             x0, x2
    // 0x8c63d8: StoreField: r3->field_7 = r0
    //     0x8c63d8: stur            w0, [x3, #7]
    //     0x8c63dc: ldurb           w16, [x3, #-1]
    //     0x8c63e0: ldurb           w17, [x0, #-1]
    //     0x8c63e4: and             x16, x17, x16, lsr #2
    //     0x8c63e8: tst             x16, HEAP, lsr #32
    //     0x8c63ec: b.eq            #0x8c63f4
    //     0x8c63f0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8c63f4: r0 = LoadClassIdInstr(r2)
    //     0x8c63f4: ldur            x0, [x2, #-1]
    //     0x8c63f8: ubfx            x0, x0, #0xc, #0x14
    // 0x8c63fc: mov             x1, x2
    // 0x8c6400: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c6400: sub             lr, x0, #1, lsl #12
    //     0x8c6404: ldr             lr, [x21, lr, lsl #3]
    //     0x8c6408: blr             lr
    // 0x8c640c: mov             x2, x0
    // 0x8c6410: r0 = BoxInt64Instr(r2)
    //     0x8c6410: sbfiz           x0, x2, #1, #0x1f
    //     0x8c6414: cmp             x2, x0, asr #1
    //     0x8c6418: b.eq            #0x8c6424
    //     0x8c641c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c6420: stur            x2, [x0, #7]
    // 0x8c6424: mov             x4, x0
    // 0x8c6428: r0 = AllocateUint8Array()
    //     0x8c6428: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c642c: ldur            x2, [fp, #-8]
    // 0x8c6430: StoreField: r2->field_b = r0
    //     0x8c6430: stur            w0, [x2, #0xb]
    //     0x8c6434: ldurb           w16, [x2, #-1]
    //     0x8c6438: ldurb           w17, [x0, #-1]
    //     0x8c643c: and             x16, x17, x16, lsr #2
    //     0x8c6440: tst             x16, HEAP, lsr #32
    //     0x8c6444: b.eq            #0x8c644c
    //     0x8c6448: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c644c: ldur            x1, [fp, #-0x10]
    // 0x8c6450: r0 = LoadClassIdInstr(r1)
    //     0x8c6450: ldur            x0, [x1, #-1]
    //     0x8c6454: ubfx            x0, x0, #0xc, #0x14
    // 0x8c6458: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c6458: sub             lr, x0, #1, lsl #12
    //     0x8c645c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c6460: blr             lr
    // 0x8c6464: mov             x2, x0
    // 0x8c6468: r0 = BoxInt64Instr(r2)
    //     0x8c6468: sbfiz           x0, x2, #1, #0x1f
    //     0x8c646c: cmp             x2, x0, asr #1
    //     0x8c6470: b.eq            #0x8c647c
    //     0x8c6474: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c6478: stur            x2, [x0, #7]
    // 0x8c647c: mov             x4, x0
    // 0x8c6480: stur            x0, [fp, #-0x10]
    // 0x8c6484: r0 = AllocateUint8Array()
    //     0x8c6484: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c6488: ldur            x1, [fp, #-8]
    // 0x8c648c: StoreField: r1->field_f = r0
    //     0x8c648c: stur            w0, [x1, #0xf]
    //     0x8c6490: ldurb           w16, [x1, #-1]
    //     0x8c6494: ldurb           w17, [x0, #-1]
    //     0x8c6498: and             x16, x17, x16, lsr #2
    //     0x8c649c: tst             x16, HEAP, lsr #32
    //     0x8c64a0: b.eq            #0x8c64a8
    //     0x8c64a4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c64a8: ldur            x2, [fp, #-0x10]
    // 0x8c64ac: StoreField: r1->field_13 = r2
    //     0x8c64ac: stur            w2, [x1, #0x13]
    // 0x8c64b0: r0 = Null
    //     0x8c64b0: mov             x0, NULL
    // 0x8c64b4: LeaveFrame
    //     0x8c64b4: mov             SP, fp
    //     0x8c64b8: ldp             fp, lr, [SP], #0x10
    // 0x8c64bc: ret
    //     0x8c64bc: ret             
    // 0x8c64c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c64c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c64c4: b               #0x8c63c8
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c64f8, size: 0x98
    // 0x8c64f8: EnterFrame
    //     0x8c64f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c64fc: mov             fp, SP
    // 0x8c6500: AllocStack(0x40)
    //     0x8c6500: sub             SP, SP, #0x40
    // 0x8c6504: CheckStackOverflow
    //     0x8c6504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6508: cmp             SP, x16
    //     0x8c650c: b.ls            #0x8c6588
    // 0x8c6510: r16 = "^(.*)/CTR/PRNG$"
    //     0x8c6510: add             x16, PP, #0x18, lsl #12  ; [pp+0x182e0] "^(.*)/CTR/PRNG$"
    //     0x8c6514: ldr             x16, [x16, #0x2e0]
    // 0x8c6518: stp             x16, NULL, [SP, #0x20]
    // 0x8c651c: r16 = false
    //     0x8c651c: add             x16, NULL, #0x30  ; false
    // 0x8c6520: r30 = true
    //     0x8c6520: add             lr, NULL, #0x20  ; true
    // 0x8c6524: stp             lr, x16, [SP, #0x10]
    // 0x8c6528: r16 = false
    //     0x8c6528: add             x16, NULL, #0x30  ; false
    // 0x8c652c: r30 = false
    //     0x8c652c: add             lr, NULL, #0x30  ; false
    // 0x8c6530: stp             lr, x16, [SP]
    // 0x8c6534: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8c6534: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8c6538: r0 = _RegExp()
    //     0x8c6538: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8c653c: stur            x0, [fp, #-8]
    // 0x8c6540: r0 = DynamicFactoryConfig()
    //     0x8c6540: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c6544: mov             x3, x0
    // 0x8c6548: ldur            x0, [fp, #-8]
    // 0x8c654c: stur            x3, [fp, #-0x10]
    // 0x8c6550: StoreField: r3->field_b = r0
    //     0x8c6550: stur            w0, [x3, #0xb]
    // 0x8c6554: r1 = Function '<anonymous closure>': static.
    //     0x8c6554: add             x1, PP, #0x18, lsl #12  ; [pp+0x182e8] AnonymousClosure: static (0x8c6590), in [package:pointycastle/random/block_ctr_random.dart] BlockCtrRandom::factoryConfig (0x8c64f8)
    //     0x8c6558: ldr             x1, [x1, #0x2e8]
    // 0x8c655c: r2 = Null
    //     0x8c655c: mov             x2, NULL
    // 0x8c6560: r0 = AllocateClosure()
    //     0x8c6560: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c6564: mov             x1, x0
    // 0x8c6568: ldur            x0, [fp, #-0x10]
    // 0x8c656c: StoreField: r0->field_f = r1
    //     0x8c656c: stur            w1, [x0, #0xf]
    // 0x8c6570: r1 = SecureRandom
    //     0x8c6570: add             x1, PP, #0x18, lsl #12  ; [pp+0x182d8] Type: SecureRandom
    //     0x8c6574: ldr             x1, [x1, #0x2d8]
    // 0x8c6578: StoreField: r0->field_7 = r1
    //     0x8c6578: stur            w1, [x0, #7]
    // 0x8c657c: LeaveFrame
    //     0x8c657c: mov             SP, fp
    //     0x8c6580: ldp             fp, lr, [SP], #0x10
    // 0x8c6584: ret
    //     0x8c6584: ret             
    // 0x8c6588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6588: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c658c: b               #0x8c6510
  }
  [closure] static (dynamic) => BlockCtrRandom <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c6590, size: 0x54
    // 0x8c6590: EnterFrame
    //     0x8c6590: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6594: mov             fp, SP
    // 0x8c6598: AllocStack(0x8)
    //     0x8c6598: sub             SP, SP, #8
    // 0x8c659c: SetupParameters()
    //     0x8c659c: ldr             x0, [fp, #0x20]
    //     0x8c65a0: ldur            w1, [x0, #0x17]
    //     0x8c65a4: add             x1, x1, HEAP, lsl #32
    //     0x8c65a8: stur            x1, [fp, #-8]
    // 0x8c65ac: r1 = 1
    //     0x8c65ac: movz            x1, #0x1
    // 0x8c65b0: r0 = AllocateContext()
    //     0x8c65b0: bl              #0xec126c  ; AllocateContextStub
    // 0x8c65b4: mov             x1, x0
    // 0x8c65b8: ldur            x0, [fp, #-8]
    // 0x8c65bc: StoreField: r1->field_b = r0
    //     0x8c65bc: stur            w0, [x1, #0xb]
    // 0x8c65c0: ldr             x0, [fp, #0x10]
    // 0x8c65c4: StoreField: r1->field_f = r0
    //     0x8c65c4: stur            w0, [x1, #0xf]
    // 0x8c65c8: mov             x2, x1
    // 0x8c65cc: r1 = Function '<anonymous closure>': static.
    //     0x8c65cc: add             x1, PP, #0x18, lsl #12  ; [pp+0x182f0] AnonymousClosure: static (0x8c65e4), in [package:pointycastle/random/block_ctr_random.dart] BlockCtrRandom::factoryConfig (0x8c64f8)
    //     0x8c65d0: ldr             x1, [x1, #0x2f0]
    // 0x8c65d4: r0 = AllocateClosure()
    //     0x8c65d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c65d8: LeaveFrame
    //     0x8c65d8: mov             SP, fp
    //     0x8c65dc: ldp             fp, lr, [SP], #0x10
    // 0x8c65e0: ret
    //     0x8c65e0: ret             
  }
  [closure] static BlockCtrRandom <anonymous closure>(dynamic) {
    // ** addr: 0x8c65e4, size: 0xcc
    // 0x8c65e4: EnterFrame
    //     0x8c65e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c65e8: mov             fp, SP
    // 0x8c65ec: AllocStack(0x20)
    //     0x8c65ec: sub             SP, SP, #0x20
    // 0x8c65f0: SetupParameters()
    //     0x8c65f0: ldr             x0, [fp, #0x10]
    //     0x8c65f4: ldur            w1, [x0, #0x17]
    //     0x8c65f8: add             x1, x1, HEAP, lsl #32
    // 0x8c65fc: CheckStackOverflow
    //     0x8c65fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6600: cmp             SP, x16
    //     0x8c6604: b.ls            #0x8c66a4
    // 0x8c6608: LoadField: r0 = r1->field_f
    //     0x8c6608: ldur            w0, [x1, #0xf]
    // 0x8c660c: DecompressPointer r0
    //     0x8c660c: add             x0, x0, HEAP, lsl #32
    // 0x8c6610: r1 = LoadClassIdInstr(r0)
    //     0x8c6610: ldur            x1, [x0, #-1]
    //     0x8c6614: ubfx            x1, x1, #0xc, #0x14
    // 0x8c6618: mov             x16, x0
    // 0x8c661c: mov             x0, x1
    // 0x8c6620: mov             x1, x16
    // 0x8c6624: r2 = 1
    //     0x8c6624: movz            x2, #0x1
    // 0x8c6628: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c6628: sub             lr, x0, #0xfdd
    //     0x8c662c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c6630: blr             lr
    // 0x8c6634: stur            x0, [fp, #-8]
    // 0x8c6638: cmp             w0, NULL
    // 0x8c663c: b.eq            #0x8c66ac
    // 0x8c6640: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c6640: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6644: ldr             x0, [x0, #0x2e38]
    //     0x8c6648: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c664c: cmp             w0, w16
    //     0x8c6650: b.ne            #0x8c6660
    //     0x8c6654: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c6658: ldr             x2, [x2, #0xf80]
    //     0x8c665c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6660: r16 = <BlockCipher>
    //     0x8c6660: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8c6664: ldr             x16, [x16, #0x88]
    // 0x8c6668: stp             x0, x16, [SP, #8]
    // 0x8c666c: ldur            x16, [fp, #-8]
    // 0x8c6670: str             x16, [SP]
    // 0x8c6674: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c6674: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c6678: r0 = create()
    //     0x8c6678: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c667c: stur            x0, [fp, #-8]
    // 0x8c6680: r0 = BlockCtrRandom()
    //     0x8c6680: bl              #0x8c64c8  ; AllocateBlockCtrRandomStub -> BlockCtrRandom (size=0x18)
    // 0x8c6684: mov             x1, x0
    // 0x8c6688: ldur            x2, [fp, #-8]
    // 0x8c668c: stur            x0, [fp, #-8]
    // 0x8c6690: r0 = BlockCtrRandom()
    //     0x8c6690: bl              #0x8c63a0  ; [package:pointycastle/random/block_ctr_random.dart] BlockCtrRandom::BlockCtrRandom
    // 0x8c6694: ldur            x0, [fp, #-8]
    // 0x8c6698: LeaveFrame
    //     0x8c6698: mov             SP, fp
    //     0x8c669c: ldp             fp, lr, [SP], #0x10
    // 0x8c66a0: ret
    //     0x8c66a0: ret             
    // 0x8c66a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c66a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c66a8: b               #0x8c6608
    // 0x8c66ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c66ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ nextUint8(/* No info */) {
    // ** addr: 0xeb6c50, size: 0x160
    // 0xeb6c50: EnterFrame
    //     0xeb6c50: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6c54: mov             fp, SP
    // 0xeb6c58: AllocStack(0x8)
    //     0xeb6c58: sub             SP, SP, #8
    // 0xeb6c5c: SetupParameters(BlockCtrRandom this /* r1 => r4, fp-0x8 */)
    //     0xeb6c5c: mov             x4, x1
    //     0xeb6c60: stur            x1, [fp, #-8]
    // 0xeb6c64: CheckStackOverflow
    //     0xeb6c64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6c68: cmp             SP, x16
    //     0xeb6c6c: b.ls            #0xeb6d80
    // 0xeb6c70: LoadField: r0 = r4->field_13
    //     0xeb6c70: ldur            w0, [x4, #0x13]
    // 0xeb6c74: DecompressPointer r0
    //     0xeb6c74: add             x0, x0, HEAP, lsl #32
    // 0xeb6c78: r16 = Sentinel
    //     0xeb6c78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb6c7c: cmp             w0, w16
    // 0xeb6c80: b.eq            #0xeb6d88
    // 0xeb6c84: LoadField: r5 = r4->field_f
    //     0xeb6c84: ldur            w5, [x4, #0xf]
    // 0xeb6c88: DecompressPointer r5
    //     0xeb6c88: add             x5, x5, HEAP, lsl #32
    // 0xeb6c8c: r16 = Sentinel
    //     0xeb6c8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb6c90: cmp             w5, w16
    // 0xeb6c94: b.eq            #0xeb6d94
    // 0xeb6c98: LoadField: r1 = r5->field_13
    //     0xeb6c98: ldur            w1, [x5, #0x13]
    // 0xeb6c9c: cmp             w0, w1
    // 0xeb6ca0: b.ne            #0xeb6cec
    // 0xeb6ca4: LoadField: r1 = r4->field_7
    //     0xeb6ca4: ldur            w1, [x4, #7]
    // 0xeb6ca8: DecompressPointer r1
    //     0xeb6ca8: add             x1, x1, HEAP, lsl #32
    // 0xeb6cac: LoadField: r2 = r4->field_b
    //     0xeb6cac: ldur            w2, [x4, #0xb]
    // 0xeb6cb0: DecompressPointer r2
    //     0xeb6cb0: add             x2, x2, HEAP, lsl #32
    // 0xeb6cb4: r16 = Sentinel
    //     0xeb6cb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb6cb8: cmp             w2, w16
    // 0xeb6cbc: b.eq            #0xeb6da0
    // 0xeb6cc0: r0 = LoadClassIdInstr(r1)
    //     0xeb6cc0: ldur            x0, [x1, #-1]
    //     0xeb6cc4: ubfx            x0, x0, #0xc, #0x14
    // 0xeb6cc8: r3 = 0
    //     0xeb6cc8: movz            x3, #0
    // 0xeb6ccc: r6 = 0
    //     0xeb6ccc: movz            x6, #0
    // 0xeb6cd0: r0 = GDT[cid_x0 + -0xf69]()
    //     0xeb6cd0: sub             lr, x0, #0xf69
    //     0xeb6cd4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb6cd8: blr             lr
    // 0xeb6cdc: ldur            x0, [fp, #-8]
    // 0xeb6ce0: StoreField: r0->field_13 = rZR
    //     0xeb6ce0: stur            wzr, [x0, #0x13]
    // 0xeb6ce4: mov             x1, x0
    // 0xeb6ce8: r0 = _incrementInput()
    //     0xeb6ce8: bl              #0xeb6db0  ; [package:pointycastle/random/block_ctr_random.dart] BlockCtrRandom::_incrementInput
    // 0xeb6cec: ldur            x2, [fp, #-8]
    // 0xeb6cf0: r3 = 255
    //     0xeb6cf0: movz            x3, #0xff
    // 0xeb6cf4: LoadField: r4 = r2->field_f
    //     0xeb6cf4: ldur            w4, [x2, #0xf]
    // 0xeb6cf8: DecompressPointer r4
    //     0xeb6cf8: add             x4, x4, HEAP, lsl #32
    // 0xeb6cfc: LoadField: r5 = r2->field_13
    //     0xeb6cfc: ldur            w5, [x2, #0x13]
    // 0xeb6d00: DecompressPointer r5
    //     0xeb6d00: add             x5, x5, HEAP, lsl #32
    // 0xeb6d04: r6 = LoadInt32Instr(r5)
    //     0xeb6d04: sbfx            x6, x5, #1, #0x1f
    //     0xeb6d08: tbz             w5, #0, #0xeb6d10
    //     0xeb6d0c: ldur            x6, [x5, #7]
    // 0xeb6d10: add             x5, x6, #1
    // 0xeb6d14: r0 = BoxInt64Instr(r5)
    //     0xeb6d14: sbfiz           x0, x5, #1, #0x1f
    //     0xeb6d18: cmp             x5, x0, asr #1
    //     0xeb6d1c: b.eq            #0xeb6d28
    //     0xeb6d20: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb6d24: stur            x5, [x0, #7]
    // 0xeb6d28: StoreField: r2->field_13 = r0
    //     0xeb6d28: stur            w0, [x2, #0x13]
    //     0xeb6d2c: tbz             w0, #0, #0xeb6d48
    //     0xeb6d30: ldurb           w16, [x2, #-1]
    //     0xeb6d34: ldurb           w17, [x0, #-1]
    //     0xeb6d38: and             x16, x17, x16, lsr #2
    //     0xeb6d3c: tst             x16, HEAP, lsr #32
    //     0xeb6d40: b.eq            #0xeb6d48
    //     0xeb6d44: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xeb6d48: LoadField: r2 = r4->field_13
    //     0xeb6d48: ldur            w2, [x4, #0x13]
    // 0xeb6d4c: r0 = LoadInt32Instr(r2)
    //     0xeb6d4c: sbfx            x0, x2, #1, #0x1f
    // 0xeb6d50: mov             x1, x6
    // 0xeb6d54: cmp             x1, x0
    // 0xeb6d58: b.hs            #0xeb6dac
    // 0xeb6d5c: ArrayLoad: r1 = r4[r6]  ; List_1
    //     0xeb6d5c: add             x16, x4, x6
    //     0xeb6d60: ldrb            w1, [x16, #0x17]
    // 0xeb6d64: ubfx            x1, x1, #0, #0x20
    // 0xeb6d68: and             x2, x1, x3
    // 0xeb6d6c: ubfx            x2, x2, #0, #0x20
    // 0xeb6d70: mov             x0, x2
    // 0xeb6d74: LeaveFrame
    //     0xeb6d74: mov             SP, fp
    //     0xeb6d78: ldp             fp, lr, [SP], #0x10
    // 0xeb6d7c: ret
    //     0xeb6d7c: ret             
    // 0xeb6d80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6d80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6d84: b               #0xeb6c70
    // 0xeb6d88: r9 = _used
    //     0xeb6d88: add             x9, PP, #0x20, lsl #12  ; [pp+0x209e0] Field <BlockCtrRandom._used@956370236>: late (offset: 0x14)
    //     0xeb6d8c: ldr             x9, [x9, #0x9e0]
    // 0xeb6d90: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb6d90: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb6d94: r9 = _output
    //     0xeb6d94: add             x9, PP, #0x20, lsl #12  ; [pp+0x209e8] Field <BlockCtrRandom._output@956370236>: late (offset: 0x10)
    //     0xeb6d98: ldr             x9, [x9, #0x9e8]
    // 0xeb6d9c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb6d9c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb6da0: r9 = _input
    //     0xeb6da0: add             x9, PP, #0x20, lsl #12  ; [pp+0x209f0] Field <BlockCtrRandom._input@956370236>: late (offset: 0xc)
    //     0xeb6da4: ldr             x9, [x9, #0x9f0]
    // 0xeb6da8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb6da8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb6dac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb6dac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _incrementInput(/* No info */) {
    // ** addr: 0xeb6db0, size: 0x98
    // 0xeb6db0: EnterFrame
    //     0xeb6db0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6db4: mov             fp, SP
    // 0xeb6db8: LoadField: r2 = r1->field_b
    //     0xeb6db8: ldur            w2, [x1, #0xb]
    // 0xeb6dbc: DecompressPointer r2
    //     0xeb6dbc: add             x2, x2, HEAP, lsl #32
    // 0xeb6dc0: r16 = Sentinel
    //     0xeb6dc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb6dc4: cmp             w2, w16
    // 0xeb6dc8: b.eq            #0xeb6e30
    // 0xeb6dcc: LoadField: r3 = r2->field_13
    //     0xeb6dcc: ldur            w3, [x2, #0x13]
    // 0xeb6dd0: r4 = LoadInt32Instr(r3)
    //     0xeb6dd0: sbfx            x4, x3, #1, #0x1f
    // 0xeb6dd4: mov             x3, x4
    // 0xeb6dd8: CheckStackOverflow
    //     0xeb6dd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6ddc: cmp             SP, x16
    //     0xeb6de0: b.ls            #0xeb6e3c
    // 0xeb6de4: sub             x5, x3, #1
    // 0xeb6de8: mov             x0, x4
    // 0xeb6dec: mov             x1, x5
    // 0xeb6df0: cmp             x1, x0
    // 0xeb6df4: b.hs            #0xeb6e44
    // 0xeb6df8: ArrayLoad: r1 = r2[r5]  ; List_1
    //     0xeb6df8: add             x16, x2, x5
    //     0xeb6dfc: ldrb            w1, [x16, #0x17]
    // 0xeb6e00: add             x3, x1, #1
    // 0xeb6e04: ArrayStore: r2[r5] = r3  ; TypeUnknown_1
    //     0xeb6e04: add             x1, x2, x5
    //     0xeb6e08: strb            w3, [x1, #0x17]
    // 0xeb6e0c: ArrayLoad: r1 = r2[r5]  ; List_1
    //     0xeb6e0c: add             x16, x2, x5
    //     0xeb6e10: ldrb            w1, [x16, #0x17]
    // 0xeb6e14: cbnz            x1, #0xeb6e20
    // 0xeb6e18: mov             x3, x5
    // 0xeb6e1c: b               #0xeb6dd8
    // 0xeb6e20: r0 = Null
    //     0xeb6e20: mov             x0, NULL
    // 0xeb6e24: LeaveFrame
    //     0xeb6e24: mov             SP, fp
    //     0xeb6e28: ldp             fp, lr, [SP], #0x10
    // 0xeb6e2c: ret
    //     0xeb6e2c: ret             
    // 0xeb6e30: r9 = _input
    //     0xeb6e30: add             x9, PP, #0x20, lsl #12  ; [pp+0x209f0] Field <BlockCtrRandom._input@956370236>: late (offset: 0xc)
    //     0xeb6e34: ldr             x9, [x9, #0x9f0]
    // 0xeb6e38: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb6e38: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb6e3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6e3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6e40: b               #0xeb6de4
    // 0xeb6e44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb6e44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
