// lib: impl.secure_random.fortuna_random, url: package:pointycastle/random/fortuna_random.dart

// class id: 1051025, size: 0x8
class :: {
}

// class id: 566, size: 0x10, field offset: 0x8
class FortunaRandom extends Object
    implements SecureRandom {

  static late final FactoryConfig factoryConfig; // offset: 0xe80
  late AutoSeedBlockCtrRandom _prng; // offset: 0xc

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c6188, size: 0x58
    // 0x8c6188: EnterFrame
    //     0x8c6188: stp             fp, lr, [SP, #-0x10]!
    //     0x8c618c: mov             fp, SP
    // 0x8c6190: AllocStack(0x8)
    //     0x8c6190: sub             SP, SP, #8
    // 0x8c6194: r0 = StaticFactoryConfig()
    //     0x8c6194: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c6198: mov             x3, x0
    // 0x8c619c: r0 = "Fortuna"
    //     0x8c619c: add             x0, PP, #0x18, lsl #12  ; [pp+0x182c8] "Fortuna"
    //     0x8c61a0: ldr             x0, [x0, #0x2c8]
    // 0x8c61a4: stur            x3, [fp, #-8]
    // 0x8c61a8: StoreField: r3->field_b = r0
    //     0x8c61a8: stur            w0, [x3, #0xb]
    // 0x8c61ac: r1 = Function '<anonymous closure>': static.
    //     0x8c61ac: add             x1, PP, #0x18, lsl #12  ; [pp+0x182d0] AnonymousClosure: static (0x8c61e0), in [package:pointycastle/random/fortuna_random.dart] FortunaRandom::factoryConfig (0x8c6188)
    //     0x8c61b0: ldr             x1, [x1, #0x2d0]
    // 0x8c61b4: r2 = Null
    //     0x8c61b4: mov             x2, NULL
    // 0x8c61b8: r0 = AllocateClosure()
    //     0x8c61b8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c61bc: mov             x1, x0
    // 0x8c61c0: ldur            x0, [fp, #-8]
    // 0x8c61c4: StoreField: r0->field_f = r1
    //     0x8c61c4: stur            w1, [x0, #0xf]
    // 0x8c61c8: r1 = SecureRandom
    //     0x8c61c8: add             x1, PP, #0x18, lsl #12  ; [pp+0x182d8] Type: SecureRandom
    //     0x8c61cc: ldr             x1, [x1, #0x2d8]
    // 0x8c61d0: StoreField: r0->field_7 = r1
    //     0x8c61d0: stur            w1, [x0, #7]
    // 0x8c61d4: LeaveFrame
    //     0x8c61d4: mov             SP, fp
    //     0x8c61d8: ldp             fp, lr, [SP], #0x10
    // 0x8c61dc: ret
    //     0x8c61dc: ret             
  }
  [closure] static FortunaRandom <anonymous closure>(dynamic) {
    // ** addr: 0x8c61e0, size: 0x40
    // 0x8c61e0: EnterFrame
    //     0x8c61e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c61e4: mov             fp, SP
    // 0x8c61e8: AllocStack(0x8)
    //     0x8c61e8: sub             SP, SP, #8
    // 0x8c61ec: CheckStackOverflow
    //     0x8c61ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c61f0: cmp             SP, x16
    //     0x8c61f4: b.ls            #0x8c6218
    // 0x8c61f8: r0 = FortunaRandom()
    //     0x8c61f8: bl              #0x8c64ec  ; AllocateFortunaRandomStub -> FortunaRandom (size=0x10)
    // 0x8c61fc: mov             x1, x0
    // 0x8c6200: stur            x0, [fp, #-8]
    // 0x8c6204: r0 = FortunaRandom()
    //     0x8c6204: bl              #0x8c6220  ; [package:pointycastle/random/fortuna_random.dart] FortunaRandom::FortunaRandom
    // 0x8c6208: ldur            x0, [fp, #-8]
    // 0x8c620c: LeaveFrame
    //     0x8c620c: mov             SP, fp
    //     0x8c6210: ldp             fp, lr, [SP], #0x10
    // 0x8c6214: ret
    //     0x8c6214: ret             
    // 0x8c6218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6218: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c621c: b               #0x8c61f8
  }
  _ FortunaRandom(/* No info */) {
    // ** addr: 0x8c6220, size: 0xd4
    // 0x8c6220: EnterFrame
    //     0x8c6220: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6224: mov             fp, SP
    // 0x8c6228: AllocStack(0x20)
    //     0x8c6228: sub             SP, SP, #0x20
    // 0x8c622c: r0 = Sentinel
    //     0x8c622c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c6230: stur            x1, [fp, #-8]
    // 0x8c6234: CheckStackOverflow
    //     0x8c6234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6238: cmp             SP, x16
    //     0x8c623c: b.ls            #0x8c62ec
    // 0x8c6240: StoreField: r1->field_b = r0
    //     0x8c6240: stur            w0, [x1, #0xb]
    // 0x8c6244: r0 = AESEngine()
    //     0x8c6244: bl              #0x8c64e0  ; AllocateAESEngineStub -> AESEngine (size=0x1c)
    // 0x8c6248: stur            x0, [fp, #-0x10]
    // 0x8c624c: StoreField: r0->field_7 = rZR
    //     0x8c624c: stur            xzr, [x0, #7]
    // 0x8c6250: r1 = Sentinel
    //     0x8c6250: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c6254: StoreField: r0->field_f = r1
    //     0x8c6254: stur            w1, [x0, #0xf]
    // 0x8c6258: r1 = false
    //     0x8c6258: add             x1, NULL, #0x30  ; false
    // 0x8c625c: StoreField: r0->field_13 = r1
    //     0x8c625c: stur            w1, [x0, #0x13]
    // 0x8c6260: r1 = <int>
    //     0x8c6260: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8c6264: r2 = 0
    //     0x8c6264: movz            x2, #0
    // 0x8c6268: r0 = AllocateArray()
    //     0x8c6268: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c626c: ldur            x1, [fp, #-0x10]
    // 0x8c6270: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c6270: stur            w0, [x1, #0x17]
    // 0x8c6274: mov             x0, x1
    // 0x8c6278: ldur            x2, [fp, #-8]
    // 0x8c627c: StoreField: r2->field_7 = r0
    //     0x8c627c: stur            w0, [x2, #7]
    //     0x8c6280: ldurb           w16, [x2, #-1]
    //     0x8c6284: ldurb           w17, [x0, #-1]
    //     0x8c6288: and             x16, x17, x16, lsr #2
    //     0x8c628c: tst             x16, HEAP, lsr #32
    //     0x8c6290: b.eq            #0x8c6298
    //     0x8c6294: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c6298: r0 = AutoSeedBlockCtrRandom()
    //     0x8c6298: bl              #0x8c64d4  ; AllocateAutoSeedBlockCtrRandomStub -> AutoSeedBlockCtrRandom (size=0x18)
    // 0x8c629c: stur            x0, [fp, #-0x18]
    // 0x8c62a0: r16 = false
    //     0x8c62a0: add             x16, NULL, #0x30  ; false
    // 0x8c62a4: str             x16, [SP]
    // 0x8c62a8: mov             x1, x0
    // 0x8c62ac: ldur            x2, [fp, #-0x10]
    // 0x8c62b0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8c62b0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8c62b4: r0 = AutoSeedBlockCtrRandom()
    //     0x8c62b4: bl              #0x8c62f4  ; [package:pointycastle/random/auto_seed_block_ctr_random.dart] AutoSeedBlockCtrRandom::AutoSeedBlockCtrRandom
    // 0x8c62b8: ldur            x0, [fp, #-0x18]
    // 0x8c62bc: ldur            x1, [fp, #-8]
    // 0x8c62c0: StoreField: r1->field_b = r0
    //     0x8c62c0: stur            w0, [x1, #0xb]
    //     0x8c62c4: ldurb           w16, [x1, #-1]
    //     0x8c62c8: ldurb           w17, [x0, #-1]
    //     0x8c62cc: and             x16, x17, x16, lsr #2
    //     0x8c62d0: tst             x16, HEAP, lsr #32
    //     0x8c62d4: b.eq            #0x8c62dc
    //     0x8c62d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c62dc: r0 = Null
    //     0x8c62dc: mov             x0, NULL
    // 0x8c62e0: LeaveFrame
    //     0x8c62e0: mov             SP, fp
    //     0x8c62e4: ldp             fp, lr, [SP], #0x10
    // 0x8c62e8: ret
    //     0x8c62e8: ret             
    // 0x8c62ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c62ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c62f0: b               #0x8c6240
  }
  _ nextBigInteger(/* No info */) {
    // ** addr: 0xeb6e48, size: 0x50
    // 0xeb6e48: EnterFrame
    //     0xeb6e48: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6e4c: mov             fp, SP
    // 0xeb6e50: CheckStackOverflow
    //     0xeb6e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6e54: cmp             SP, x16
    //     0xeb6e58: b.ls            #0xeb6e84
    // 0xeb6e5c: LoadField: r0 = r1->field_b
    //     0xeb6e5c: ldur            w0, [x1, #0xb]
    // 0xeb6e60: DecompressPointer r0
    //     0xeb6e60: add             x0, x0, HEAP, lsl #32
    // 0xeb6e64: r16 = Sentinel
    //     0xeb6e64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb6e68: cmp             w0, w16
    // 0xeb6e6c: b.eq            #0xeb6e8c
    // 0xeb6e70: mov             x1, x0
    // 0xeb6e74: r0 = nextBigInteger()
    //     0xeb6e74: bl              #0xeb68a0  ; [package:pointycastle/random/auto_seed_block_ctr_random.dart] AutoSeedBlockCtrRandom::nextBigInteger
    // 0xeb6e78: LeaveFrame
    //     0xeb6e78: mov             SP, fp
    //     0xeb6e7c: ldp             fp, lr, [SP], #0x10
    // 0xeb6e80: ret
    //     0xeb6e80: ret             
    // 0xeb6e84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6e84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6e88: b               #0xeb6e5c
    // 0xeb6e8c: r9 = _prng
    //     0xeb6e8c: add             x9, PP, #0x21, lsl #12  ; [pp+0x21c00] Field <FortunaRandom._prng@957160334>: late (offset: 0xc)
    //     0xeb6e90: ldr             x9, [x9, #0xc00]
    // 0xeb6e94: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb6e94: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
