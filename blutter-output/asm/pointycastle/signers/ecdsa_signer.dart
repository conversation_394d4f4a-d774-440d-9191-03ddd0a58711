// lib: impl.signer.ecdsa_signer, url: package:pointycastle/signers/ecdsa_signer.dart

// class id: 1051026, size: 0x8
class :: {
}

// class id: 565, size: 0x8, field offset: 0x8
class ECDSASigner extends Object
    implements Signer {

  static late final FactoryConfig factoryConfig; // offset: 0xe84

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c5e0c, size: 0x98
    // 0x8c5e0c: EnterFrame
    //     0x8c5e0c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5e10: mov             fp, SP
    // 0x8c5e14: AllocStack(0x40)
    //     0x8c5e14: sub             SP, SP, #0x40
    // 0x8c5e18: CheckStackOverflow
    //     0x8c5e18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5e1c: cmp             SP, x16
    //     0x8c5e20: b.ls            #0x8c5e9c
    // 0x8c5e24: r16 = "^(.+)/(DET-)\?ECDSA$"
    //     0x8c5e24: add             x16, PP, #0x18, lsl #12  ; [pp+0x18288] "^(.+)/(DET-)\?ECDSA$"
    //     0x8c5e28: ldr             x16, [x16, #0x288]
    // 0x8c5e2c: stp             x16, NULL, [SP, #0x20]
    // 0x8c5e30: r16 = false
    //     0x8c5e30: add             x16, NULL, #0x30  ; false
    // 0x8c5e34: r30 = true
    //     0x8c5e34: add             lr, NULL, #0x20  ; true
    // 0x8c5e38: stp             lr, x16, [SP, #0x10]
    // 0x8c5e3c: r16 = false
    //     0x8c5e3c: add             x16, NULL, #0x30  ; false
    // 0x8c5e40: r30 = false
    //     0x8c5e40: add             lr, NULL, #0x30  ; false
    // 0x8c5e44: stp             lr, x16, [SP]
    // 0x8c5e48: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8c5e48: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8c5e4c: r0 = _RegExp()
    //     0x8c5e4c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8c5e50: stur            x0, [fp, #-8]
    // 0x8c5e54: r0 = DynamicFactoryConfig()
    //     0x8c5e54: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c5e58: mov             x3, x0
    // 0x8c5e5c: ldur            x0, [fp, #-8]
    // 0x8c5e60: stur            x3, [fp, #-0x10]
    // 0x8c5e64: StoreField: r3->field_b = r0
    //     0x8c5e64: stur            w0, [x3, #0xb]
    // 0x8c5e68: r1 = Function '<anonymous closure>': static.
    //     0x8c5e68: add             x1, PP, #0x18, lsl #12  ; [pp+0x18290] AnonymousClosure: static (0x8c5ea4), in [package:pointycastle/signers/ecdsa_signer.dart] ECDSASigner::factoryConfig (0x8c5e0c)
    //     0x8c5e6c: ldr             x1, [x1, #0x290]
    // 0x8c5e70: r2 = Null
    //     0x8c5e70: mov             x2, NULL
    // 0x8c5e74: r0 = AllocateClosure()
    //     0x8c5e74: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5e78: mov             x1, x0
    // 0x8c5e7c: ldur            x0, [fp, #-0x10]
    // 0x8c5e80: StoreField: r0->field_f = r1
    //     0x8c5e80: stur            w1, [x0, #0xf]
    // 0x8c5e84: r1 = Signer
    //     0x8c5e84: add             x1, PP, #0x18, lsl #12  ; [pp+0x18188] Type: Signer
    //     0x8c5e88: ldr             x1, [x1, #0x188]
    // 0x8c5e8c: StoreField: r0->field_7 = r1
    //     0x8c5e8c: stur            w1, [x0, #7]
    // 0x8c5e90: LeaveFrame
    //     0x8c5e90: mov             SP, fp
    //     0x8c5e94: ldp             fp, lr, [SP], #0x10
    // 0x8c5e98: ret
    //     0x8c5e98: ret             
    // 0x8c5e9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5e9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5ea0: b               #0x8c5e24
  }
  [closure] static (dynamic) => ECDSASigner <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c5ea4, size: 0xd4
    // 0x8c5ea4: EnterFrame
    //     0x8c5ea4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5ea8: mov             fp, SP
    // 0x8c5eac: AllocStack(0x10)
    //     0x8c5eac: sub             SP, SP, #0x10
    // 0x8c5eb0: SetupParameters()
    //     0x8c5eb0: ldr             x0, [fp, #0x20]
    //     0x8c5eb4: ldur            w1, [x0, #0x17]
    //     0x8c5eb8: add             x1, x1, HEAP, lsl #32
    //     0x8c5ebc: stur            x1, [fp, #-8]
    // 0x8c5ec0: CheckStackOverflow
    //     0x8c5ec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5ec4: cmp             SP, x16
    //     0x8c5ec8: b.ls            #0x8c5f70
    // 0x8c5ecc: r1 = 2
    //     0x8c5ecc: movz            x1, #0x2
    // 0x8c5ed0: r0 = AllocateContext()
    //     0x8c5ed0: bl              #0xec126c  ; AllocateContextStub
    // 0x8c5ed4: mov             x3, x0
    // 0x8c5ed8: ldur            x0, [fp, #-8]
    // 0x8c5edc: stur            x3, [fp, #-0x10]
    // 0x8c5ee0: StoreField: r3->field_b = r0
    //     0x8c5ee0: stur            w0, [x3, #0xb]
    // 0x8c5ee4: ldr             x4, [fp, #0x10]
    // 0x8c5ee8: r0 = LoadClassIdInstr(r4)
    //     0x8c5ee8: ldur            x0, [x4, #-1]
    //     0x8c5eec: ubfx            x0, x0, #0xc, #0x14
    // 0x8c5ef0: mov             x1, x4
    // 0x8c5ef4: r2 = 1
    //     0x8c5ef4: movz            x2, #0x1
    // 0x8c5ef8: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c5ef8: sub             lr, x0, #0xfdd
    //     0x8c5efc: ldr             lr, [x21, lr, lsl #3]
    //     0x8c5f00: blr             lr
    // 0x8c5f04: ldur            x3, [fp, #-0x10]
    // 0x8c5f08: StoreField: r3->field_f = r0
    //     0x8c5f08: stur            w0, [x3, #0xf]
    //     0x8c5f0c: ldurb           w16, [x3, #-1]
    //     0x8c5f10: ldurb           w17, [x0, #-1]
    //     0x8c5f14: and             x16, x17, x16, lsr #2
    //     0x8c5f18: tst             x16, HEAP, lsr #32
    //     0x8c5f1c: b.eq            #0x8c5f24
    //     0x8c5f20: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8c5f24: ldr             x1, [fp, #0x10]
    // 0x8c5f28: r0 = LoadClassIdInstr(r1)
    //     0x8c5f28: ldur            x0, [x1, #-1]
    //     0x8c5f2c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c5f30: r2 = 2
    //     0x8c5f30: movz            x2, #0x2
    // 0x8c5f34: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c5f34: sub             lr, x0, #0xfdd
    //     0x8c5f38: ldr             lr, [x21, lr, lsl #3]
    //     0x8c5f3c: blr             lr
    // 0x8c5f40: cmp             w0, NULL
    // 0x8c5f44: r16 = true
    //     0x8c5f44: add             x16, NULL, #0x20  ; true
    // 0x8c5f48: r17 = false
    //     0x8c5f48: add             x17, NULL, #0x30  ; false
    // 0x8c5f4c: csel            x1, x16, x17, ne
    // 0x8c5f50: ldur            x2, [fp, #-0x10]
    // 0x8c5f54: StoreField: r2->field_13 = r1
    //     0x8c5f54: stur            w1, [x2, #0x13]
    // 0x8c5f58: r1 = Function '<anonymous closure>': static.
    //     0x8c5f58: add             x1, PP, #0x18, lsl #12  ; [pp+0x18298] AnonymousClosure: static (0x8c5f78), in [package:pointycastle/signers/ecdsa_signer.dart] ECDSASigner::factoryConfig (0x8c5e0c)
    //     0x8c5f5c: ldr             x1, [x1, #0x298]
    // 0x8c5f60: r0 = AllocateClosure()
    //     0x8c5f60: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5f64: LeaveFrame
    //     0x8c5f64: mov             SP, fp
    //     0x8c5f68: ldp             fp, lr, [SP], #0x10
    // 0x8c5f6c: ret
    //     0x8c5f6c: ret             
    // 0x8c5f70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5f70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5f74: b               #0x8c5ecc
  }
  [closure] static ECDSASigner <anonymous closure>(dynamic) {
    // ** addr: 0x8c5f78, size: 0xe0
    // 0x8c5f78: EnterFrame
    //     0x8c5f78: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5f7c: mov             fp, SP
    // 0x8c5f80: AllocStack(0x28)
    //     0x8c5f80: sub             SP, SP, #0x28
    // 0x8c5f84: SetupParameters()
    //     0x8c5f84: ldr             x0, [fp, #0x10]
    //     0x8c5f88: ldur            w1, [x0, #0x17]
    //     0x8c5f8c: add             x1, x1, HEAP, lsl #32
    //     0x8c5f90: stur            x1, [fp, #-0x10]
    // 0x8c5f94: CheckStackOverflow
    //     0x8c5f94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5f98: cmp             SP, x16
    //     0x8c5f9c: b.ls            #0x8c604c
    // 0x8c5fa0: LoadField: r0 = r1->field_f
    //     0x8c5fa0: ldur            w0, [x1, #0xf]
    // 0x8c5fa4: DecompressPointer r0
    //     0x8c5fa4: add             x0, x0, HEAP, lsl #32
    // 0x8c5fa8: stur            x0, [fp, #-8]
    // 0x8c5fac: cmp             w0, NULL
    // 0x8c5fb0: b.eq            #0x8c6054
    // 0x8c5fb4: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c5fb4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c5fb8: ldr             x0, [x0, #0x2e38]
    //     0x8c5fbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c5fc0: cmp             w0, w16
    //     0x8c5fc4: b.ne            #0x8c5fd4
    //     0x8c5fc8: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c5fcc: ldr             x2, [x2, #0xf80]
    //     0x8c5fd0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c5fd4: r16 = <Digest>
    //     0x8c5fd4: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b8] TypeArguments: <Digest>
    //     0x8c5fd8: ldr             x16, [x16, #0x1b8]
    // 0x8c5fdc: stp             x0, x16, [SP, #8]
    // 0x8c5fe0: ldur            x16, [fp, #-8]
    // 0x8c5fe4: str             x16, [SP]
    // 0x8c5fe8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c5fe8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c5fec: r0 = create()
    //     0x8c5fec: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c5ff0: ldur            x0, [fp, #-0x10]
    // 0x8c5ff4: LoadField: r1 = r0->field_13
    //     0x8c5ff4: ldur            w1, [x0, #0x13]
    // 0x8c5ff8: DecompressPointer r1
    //     0x8c5ff8: add             x1, x1, HEAP, lsl #32
    // 0x8c5ffc: tbnz            w1, #4, #0x8c603c
    // 0x8c6000: ldur            x0, [fp, #-8]
    // 0x8c6004: r1 = Null
    //     0x8c6004: mov             x1, NULL
    // 0x8c6008: r2 = 4
    //     0x8c6008: movz            x2, #0x4
    // 0x8c600c: r0 = AllocateArray()
    //     0x8c600c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c6010: mov             x1, x0
    // 0x8c6014: ldur            x0, [fp, #-8]
    // 0x8c6018: StoreField: r1->field_f = r0
    //     0x8c6018: stur            w0, [x1, #0xf]
    // 0x8c601c: r16 = "/HMAC"
    //     0x8c601c: add             x16, PP, #0x18, lsl #12  ; [pp+0x182a0] "/HMAC"
    //     0x8c6020: ldr             x16, [x16, #0x2a0]
    // 0x8c6024: StoreField: r1->field_13 = r16
    //     0x8c6024: stur            w16, [x1, #0x13]
    // 0x8c6028: str             x1, [SP]
    // 0x8c602c: r0 = _interpolate()
    //     0x8c602c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c6030: mov             x2, x0
    // 0x8c6034: r1 = Null
    //     0x8c6034: mov             x1, NULL
    // 0x8c6038: r0 = Mac()
    //     0x8c6038: bl              #0x8c6064  ; [package:pointycastle/api.dart] Mac::Mac
    // 0x8c603c: r0 = ECDSASigner()
    //     0x8c603c: bl              #0x8c6058  ; AllocateECDSASignerStub -> ECDSASigner (size=0x8)
    // 0x8c6040: LeaveFrame
    //     0x8c6040: mov             SP, fp
    //     0x8c6044: ldp             fp, lr, [SP], #0x10
    // 0x8c6048: ret
    //     0x8c6048: ret             
    // 0x8c604c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c604c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6050: b               #0x8c5fa0
    // 0x8c6054: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c6054: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
