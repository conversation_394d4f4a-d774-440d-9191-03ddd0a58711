// lib: impl.signer.pss_signer, url: package:pointycastle/signers/pss_signer.dart

// class id: 1051027, size: 0x8
class :: {
}

// class id: 564, size: 0x8, field offset: 0x8
class PSSSigner extends Object
    implements Signer {

  static late final FactoryConfig factoryConfig; // offset: 0xe90

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c5c10, size: 0x64
    // 0x8c5c10: EnterFrame
    //     0x8c5c10: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5c14: mov             fp, SP
    // 0x8c5c18: AllocStack(0x8)
    //     0x8c5c18: sub             SP, SP, #8
    // 0x8c5c1c: CheckStackOverflow
    //     0x8c5c1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5c20: cmp             SP, x16
    //     0x8c5c24: b.ls            #0x8c5c6c
    // 0x8c5c28: r0 = DynamicFactoryConfig()
    //     0x8c5c28: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c5c2c: r1 = Function '<anonymous closure>': static.
    //     0x8c5c2c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18270] AnonymousClosure: static (0x8c5c74), in [package:pointycastle/signers/pss_signer.dart] PSSSigner::factoryConfig (0x8c5c10)
    //     0x8c5c30: ldr             x1, [x1, #0x270]
    // 0x8c5c34: r2 = Null
    //     0x8c5c34: mov             x2, NULL
    // 0x8c5c38: stur            x0, [fp, #-8]
    // 0x8c5c3c: r0 = AllocateClosure()
    //     0x8c5c3c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5c40: ldur            x1, [fp, #-8]
    // 0x8c5c44: mov             x5, x0
    // 0x8c5c48: r2 = Signer
    //     0x8c5c48: add             x2, PP, #0x18, lsl #12  ; [pp+0x18188] Type: Signer
    //     0x8c5c4c: ldr             x2, [x2, #0x188]
    // 0x8c5c50: r3 = "/PSS"
    //     0x8c5c50: add             x3, PP, #0x18, lsl #12  ; [pp+0x18278] "/PSS"
    //     0x8c5c54: ldr             x3, [x3, #0x278]
    // 0x8c5c58: r0 = DynamicFactoryConfig.suffix()
    //     0x8c5c58: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c5c5c: ldur            x0, [fp, #-8]
    // 0x8c5c60: LeaveFrame
    //     0x8c5c60: mov             SP, fp
    //     0x8c5c64: ldp             fp, lr, [SP], #0x10
    // 0x8c5c68: ret
    //     0x8c5c68: ret             
    // 0x8c5c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5c6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5c70: b               #0x8c5c28
  }
  [closure] static (dynamic) => PSSSigner <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c5c74, size: 0x9c
    // 0x8c5c74: EnterFrame
    //     0x8c5c74: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5c78: mov             fp, SP
    // 0x8c5c7c: AllocStack(0x10)
    //     0x8c5c7c: sub             SP, SP, #0x10
    // 0x8c5c80: SetupParameters()
    //     0x8c5c80: ldr             x0, [fp, #0x20]
    //     0x8c5c84: ldur            w1, [x0, #0x17]
    //     0x8c5c88: add             x1, x1, HEAP, lsl #32
    //     0x8c5c8c: stur            x1, [fp, #-8]
    // 0x8c5c90: CheckStackOverflow
    //     0x8c5c90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5c94: cmp             SP, x16
    //     0x8c5c98: b.ls            #0x8c5d08
    // 0x8c5c9c: r1 = 1
    //     0x8c5c9c: movz            x1, #0x1
    // 0x8c5ca0: r0 = AllocateContext()
    //     0x8c5ca0: bl              #0xec126c  ; AllocateContextStub
    // 0x8c5ca4: mov             x3, x0
    // 0x8c5ca8: ldur            x0, [fp, #-8]
    // 0x8c5cac: stur            x3, [fp, #-0x10]
    // 0x8c5cb0: StoreField: r3->field_b = r0
    //     0x8c5cb0: stur            w0, [x3, #0xb]
    // 0x8c5cb4: ldr             x1, [fp, #0x10]
    // 0x8c5cb8: r0 = LoadClassIdInstr(r1)
    //     0x8c5cb8: ldur            x0, [x1, #-1]
    //     0x8c5cbc: ubfx            x0, x0, #0xc, #0x14
    // 0x8c5cc0: r2 = 1
    //     0x8c5cc0: movz            x2, #0x1
    // 0x8c5cc4: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c5cc4: sub             lr, x0, #0xfdd
    //     0x8c5cc8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c5ccc: blr             lr
    // 0x8c5cd0: ldur            x2, [fp, #-0x10]
    // 0x8c5cd4: StoreField: r2->field_f = r0
    //     0x8c5cd4: stur            w0, [x2, #0xf]
    //     0x8c5cd8: ldurb           w16, [x2, #-1]
    //     0x8c5cdc: ldurb           w17, [x0, #-1]
    //     0x8c5ce0: and             x16, x17, x16, lsr #2
    //     0x8c5ce4: tst             x16, HEAP, lsr #32
    //     0x8c5ce8: b.eq            #0x8c5cf0
    //     0x8c5cec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c5cf0: r1 = Function '<anonymous closure>': static.
    //     0x8c5cf0: add             x1, PP, #0x18, lsl #12  ; [pp+0x18280] AnonymousClosure: static (0x8c5d10), in [package:pointycastle/signers/pss_signer.dart] PSSSigner::factoryConfig (0x8c5c10)
    //     0x8c5cf4: ldr             x1, [x1, #0x280]
    // 0x8c5cf8: r0 = AllocateClosure()
    //     0x8c5cf8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5cfc: LeaveFrame
    //     0x8c5cfc: mov             SP, fp
    //     0x8c5d00: ldp             fp, lr, [SP], #0x10
    // 0x8c5d04: ret
    //     0x8c5d04: ret             
    // 0x8c5d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5d08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5d0c: b               #0x8c5c9c
  }
  [closure] static PSSSigner <anonymous closure>(dynamic) {
    // ** addr: 0x8c5d10, size: 0xf0
    // 0x8c5d10: EnterFrame
    //     0x8c5d10: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5d14: mov             fp, SP
    // 0x8c5d18: AllocStack(0x30)
    //     0x8c5d18: sub             SP, SP, #0x30
    // 0x8c5d1c: SetupParameters()
    //     0x8c5d1c: ldr             x0, [fp, #0x10]
    //     0x8c5d20: ldur            w1, [x0, #0x17]
    //     0x8c5d24: add             x1, x1, HEAP, lsl #32
    // 0x8c5d28: CheckStackOverflow
    //     0x8c5d28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5d2c: cmp             SP, x16
    //     0x8c5d30: b.ls            #0x8c5df4
    // 0x8c5d34: LoadField: r0 = r1->field_f
    //     0x8c5d34: ldur            w0, [x1, #0xf]
    // 0x8c5d38: DecompressPointer r0
    //     0x8c5d38: add             x0, x0, HEAP, lsl #32
    // 0x8c5d3c: stur            x0, [fp, #-8]
    // 0x8c5d40: cmp             w0, NULL
    // 0x8c5d44: b.eq            #0x8c5dfc
    // 0x8c5d48: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c5d48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c5d4c: ldr             x0, [x0, #0x2e38]
    //     0x8c5d50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c5d54: cmp             w0, w16
    //     0x8c5d58: b.ne            #0x8c5d68
    //     0x8c5d5c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c5d60: ldr             x2, [x2, #0xf80]
    //     0x8c5d64: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c5d68: stur            x0, [fp, #-0x10]
    // 0x8c5d6c: r16 = <Digest>
    //     0x8c5d6c: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b8] TypeArguments: <Digest>
    //     0x8c5d70: ldr             x16, [x16, #0x1b8]
    // 0x8c5d74: stp             x0, x16, [SP, #8]
    // 0x8c5d78: ldur            x16, [fp, #-8]
    // 0x8c5d7c: str             x16, [SP]
    // 0x8c5d80: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c5d80: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c5d84: r0 = create()
    //     0x8c5d84: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c5d88: stur            x0, [fp, #-0x18]
    // 0x8c5d8c: r16 = <Digest>
    //     0x8c5d8c: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b8] TypeArguments: <Digest>
    //     0x8c5d90: ldr             x16, [x16, #0x1b8]
    // 0x8c5d94: ldur            lr, [fp, #-0x10]
    // 0x8c5d98: stp             lr, x16, [SP, #8]
    // 0x8c5d9c: ldur            x16, [fp, #-8]
    // 0x8c5da0: str             x16, [SP]
    // 0x8c5da4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c5da4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c5da8: r0 = create()
    //     0x8c5da8: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c5dac: mov             x2, x0
    // 0x8c5db0: ldur            x1, [fp, #-0x18]
    // 0x8c5db4: stur            x2, [fp, #-8]
    // 0x8c5db8: r0 = LoadClassIdInstr(r1)
    //     0x8c5db8: ldur            x0, [x1, #-1]
    //     0x8c5dbc: ubfx            x0, x0, #0xc, #0x14
    // 0x8c5dc0: r0 = GDT[cid_x0 + -0xdc0]()
    //     0x8c5dc0: sub             lr, x0, #0xdc0
    //     0x8c5dc4: ldr             lr, [x21, lr, lsl #3]
    //     0x8c5dc8: blr             lr
    // 0x8c5dcc: ldur            x1, [fp, #-8]
    // 0x8c5dd0: r0 = LoadClassIdInstr(r1)
    //     0x8c5dd0: ldur            x0, [x1, #-1]
    //     0x8c5dd4: ubfx            x0, x0, #0xc, #0x14
    // 0x8c5dd8: r0 = GDT[cid_x0 + -0xdc0]()
    //     0x8c5dd8: sub             lr, x0, #0xdc0
    //     0x8c5ddc: ldr             lr, [x21, lr, lsl #3]
    //     0x8c5de0: blr             lr
    // 0x8c5de4: r0 = PSSSigner()
    //     0x8c5de4: bl              #0x8c5e00  ; AllocatePSSSignerStub -> PSSSigner (size=0x8)
    // 0x8c5de8: LeaveFrame
    //     0x8c5de8: mov             SP, fp
    //     0x8c5dec: ldp             fp, lr, [SP], #0x10
    // 0x8c5df0: ret
    //     0x8c5df0: ret             
    // 0x8c5df4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5df4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5df8: b               #0x8c5d34
    // 0x8c5dfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c5dfc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
