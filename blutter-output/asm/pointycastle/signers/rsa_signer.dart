// lib: impl.signer.rsa_signer, url: package:pointycastle/signers/rsa_signer.dart

// class id: 1051028, size: 0x8
class :: {
}

// class id: 563, size: 0x8, field offset: 0x8
class RSASigner extends Object
    implements Signer {

  static late final FactoryConfig factoryConfig; // offset: 0xe88
  static late final Map<String, String> _digestIdentifierHexes; // offset: 0xe8c

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c56e4, size: 0x64
    // 0x8c56e4: EnterFrame
    //     0x8c56e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c56e8: mov             fp, SP
    // 0x8c56ec: AllocStack(0x8)
    //     0x8c56ec: sub             SP, SP, #8
    // 0x8c56f0: CheckStackOverflow
    //     0x8c56f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c56f4: cmp             SP, x16
    //     0x8c56f8: b.ls            #0x8c5740
    // 0x8c56fc: r0 = DynamicFactoryConfig()
    //     0x8c56fc: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c5700: r1 = Function '<anonymous closure>': static.
    //     0x8c5700: add             x1, PP, #0x18, lsl #12  ; [pp+0x18180] AnonymousClosure: static (0x8c5748), in [package:pointycastle/signers/rsa_signer.dart] RSASigner::factoryConfig (0x8c56e4)
    //     0x8c5704: ldr             x1, [x1, #0x180]
    // 0x8c5708: r2 = Null
    //     0x8c5708: mov             x2, NULL
    // 0x8c570c: stur            x0, [fp, #-8]
    // 0x8c5710: r0 = AllocateClosure()
    //     0x8c5710: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5714: ldur            x1, [fp, #-8]
    // 0x8c5718: mov             x5, x0
    // 0x8c571c: r2 = Signer
    //     0x8c571c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18188] Type: Signer
    //     0x8c5720: ldr             x2, [x2, #0x188]
    // 0x8c5724: r3 = "/RSA"
    //     0x8c5724: add             x3, PP, #0x18, lsl #12  ; [pp+0x18190] "/RSA"
    //     0x8c5728: ldr             x3, [x3, #0x190]
    // 0x8c572c: r0 = DynamicFactoryConfig.suffix()
    //     0x8c572c: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c5730: ldur            x0, [fp, #-8]
    // 0x8c5734: LeaveFrame
    //     0x8c5734: mov             SP, fp
    //     0x8c5738: ldp             fp, lr, [SP], #0x10
    // 0x8c573c: ret
    //     0x8c573c: ret             
    // 0x8c5740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5740: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5744: b               #0x8c56fc
  }
  [closure] static (dynamic) => RSASigner <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c5748, size: 0x18c
    // 0x8c5748: EnterFrame
    //     0x8c5748: stp             fp, lr, [SP, #-0x10]!
    //     0x8c574c: mov             fp, SP
    // 0x8c5750: AllocStack(0x20)
    //     0x8c5750: sub             SP, SP, #0x20
    // 0x8c5754: SetupParameters()
    //     0x8c5754: ldr             x0, [fp, #0x20]
    //     0x8c5758: ldur            w1, [x0, #0x17]
    //     0x8c575c: add             x1, x1, HEAP, lsl #32
    //     0x8c5760: stur            x1, [fp, #-8]
    // 0x8c5764: CheckStackOverflow
    //     0x8c5764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5768: cmp             SP, x16
    //     0x8c576c: b.ls            #0x8c58c8
    // 0x8c5770: r1 = 2
    //     0x8c5770: movz            x1, #0x2
    // 0x8c5774: r0 = AllocateContext()
    //     0x8c5774: bl              #0xec126c  ; AllocateContextStub
    // 0x8c5778: mov             x3, x0
    // 0x8c577c: ldur            x0, [fp, #-8]
    // 0x8c5780: stur            x3, [fp, #-0x10]
    // 0x8c5784: StoreField: r3->field_b = r0
    //     0x8c5784: stur            w0, [x3, #0xb]
    // 0x8c5788: ldr             x1, [fp, #0x10]
    // 0x8c578c: r0 = LoadClassIdInstr(r1)
    //     0x8c578c: ldur            x0, [x1, #-1]
    //     0x8c5790: ubfx            x0, x0, #0xc, #0x14
    // 0x8c5794: r2 = 1
    //     0x8c5794: movz            x2, #0x1
    // 0x8c5798: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c5798: sub             lr, x0, #0xfdd
    //     0x8c579c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c57a0: blr             lr
    // 0x8c57a4: mov             x1, x0
    // 0x8c57a8: ldur            x2, [fp, #-0x10]
    // 0x8c57ac: stur            x1, [fp, #-8]
    // 0x8c57b0: StoreField: r2->field_f = r0
    //     0x8c57b0: stur            w0, [x2, #0xf]
    //     0x8c57b4: ldurb           w16, [x2, #-1]
    //     0x8c57b8: ldurb           w17, [x0, #-1]
    //     0x8c57bc: and             x16, x17, x16, lsr #2
    //     0x8c57c0: tst             x16, HEAP, lsr #32
    //     0x8c57c4: b.eq            #0x8c57cc
    //     0x8c57c8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c57cc: r0 = InitLateStaticField(0xe8c) // [package:pointycastle/signers/rsa_signer.dart] RSASigner::_digestIdentifierHexes
    //     0x8c57cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c57d0: ldr             x0, [x0, #0x1d18]
    //     0x8c57d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c57d8: cmp             w0, w16
    //     0x8c57dc: b.ne            #0x8c57ec
    //     0x8c57e0: add             x2, PP, #0x18, lsl #12  ; [pp+0x18198] Field <RSASigner._digestIdentifierHexes@959060559>: static late final (offset: 0xe8c)
    //     0x8c57e4: ldr             x2, [x2, #0x198]
    //     0x8c57e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c57ec: mov             x3, x0
    // 0x8c57f0: ldur            x0, [fp, #-8]
    // 0x8c57f4: stur            x3, [fp, #-0x18]
    // 0x8c57f8: cmp             w0, NULL
    // 0x8c57fc: b.eq            #0x8c58d0
    // 0x8c5800: mov             x1, x3
    // 0x8c5804: mov             x2, x0
    // 0x8c5808: r0 = _getValueOrData()
    //     0x8c5808: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8c580c: mov             x1, x0
    // 0x8c5810: ldur            x0, [fp, #-0x18]
    // 0x8c5814: LoadField: r2 = r0->field_f
    //     0x8c5814: ldur            w2, [x0, #0xf]
    // 0x8c5818: DecompressPointer r2
    //     0x8c5818: add             x2, x2, HEAP, lsl #32
    // 0x8c581c: cmp             w2, w1
    // 0x8c5820: b.ne            #0x8c5828
    // 0x8c5824: r1 = Null
    //     0x8c5824: mov             x1, NULL
    // 0x8c5828: ldur            x2, [fp, #-0x10]
    // 0x8c582c: mov             x0, x1
    // 0x8c5830: StoreField: r2->field_13 = r0
    //     0x8c5830: stur            w0, [x2, #0x13]
    //     0x8c5834: tbz             w0, #0, #0x8c5850
    //     0x8c5838: ldurb           w16, [x2, #-1]
    //     0x8c583c: ldurb           w17, [x0, #-1]
    //     0x8c5840: and             x16, x17, x16, lsr #2
    //     0x8c5844: tst             x16, HEAP, lsr #32
    //     0x8c5848: b.eq            #0x8c5850
    //     0x8c584c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c5850: cmp             w1, NULL
    // 0x8c5854: b.eq            #0x8c5870
    // 0x8c5858: r1 = Function '<anonymous closure>': static.
    //     0x8c5858: add             x1, PP, #0x18, lsl #12  ; [pp+0x181a0] AnonymousClosure: static (0x8c58d4), in [package:pointycastle/signers/rsa_signer.dart] RSASigner::factoryConfig (0x8c56e4)
    //     0x8c585c: ldr             x1, [x1, #0x1a0]
    // 0x8c5860: r0 = AllocateClosure()
    //     0x8c5860: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5864: LeaveFrame
    //     0x8c5864: mov             SP, fp
    //     0x8c5868: ldp             fp, lr, [SP], #0x10
    // 0x8c586c: ret
    //     0x8c586c: ret             
    // 0x8c5870: ldur            x0, [fp, #-8]
    // 0x8c5874: r1 = Null
    //     0x8c5874: mov             x1, NULL
    // 0x8c5878: r2 = 6
    //     0x8c5878: movz            x2, #0x6
    // 0x8c587c: r0 = AllocateArray()
    //     0x8c587c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c5880: r16 = "RSA signing with digest "
    //     0x8c5880: add             x16, PP, #0x18, lsl #12  ; [pp+0x181a8] "RSA signing with digest "
    //     0x8c5884: ldr             x16, [x16, #0x1a8]
    // 0x8c5888: StoreField: r0->field_f = r16
    //     0x8c5888: stur            w16, [x0, #0xf]
    // 0x8c588c: ldur            x1, [fp, #-8]
    // 0x8c5890: StoreField: r0->field_13 = r1
    //     0x8c5890: stur            w1, [x0, #0x13]
    // 0x8c5894: r16 = " is not supported"
    //     0x8c5894: add             x16, PP, #0x18, lsl #12  ; [pp+0x181b0] " is not supported"
    //     0x8c5898: ldr             x16, [x16, #0x1b0]
    // 0x8c589c: ArrayStore: r0[0] = r16  ; List_4
    //     0x8c589c: stur            w16, [x0, #0x17]
    // 0x8c58a0: str             x0, [SP]
    // 0x8c58a4: r0 = _interpolate()
    //     0x8c58a4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c58a8: stur            x0, [fp, #-8]
    // 0x8c58ac: r0 = RegistryFactoryException()
    //     0x8c58ac: bl              #0x8c37d8  ; AllocateRegistryFactoryExceptionStub -> RegistryFactoryException (size=0xc)
    // 0x8c58b0: mov             x1, x0
    // 0x8c58b4: ldur            x0, [fp, #-8]
    // 0x8c58b8: StoreField: r1->field_7 = r0
    //     0x8c58b8: stur            w0, [x1, #7]
    // 0x8c58bc: mov             x0, x1
    // 0x8c58c0: r0 = Throw()
    //     0x8c58c0: bl              #0xec04b8  ; ThrowStub
    // 0x8c58c4: brk             #0
    // 0x8c58c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c58c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c58cc: b               #0x8c5770
    // 0x8c58d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c58d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static RSASigner <anonymous closure>(dynamic) {
    // ** addr: 0x8c58d4, size: 0x74
    // 0x8c58d4: EnterFrame
    //     0x8c58d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c58d8: mov             fp, SP
    // 0x8c58dc: AllocStack(0x10)
    //     0x8c58dc: sub             SP, SP, #0x10
    // 0x8c58e0: SetupParameters()
    //     0x8c58e0: ldr             x0, [fp, #0x10]
    //     0x8c58e4: ldur            w3, [x0, #0x17]
    //     0x8c58e8: add             x3, x3, HEAP, lsl #32
    //     0x8c58ec: stur            x3, [fp, #-8]
    // 0x8c58f0: CheckStackOverflow
    //     0x8c58f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c58f4: cmp             SP, x16
    //     0x8c58f8: b.ls            #0x8c5940
    // 0x8c58fc: LoadField: r2 = r3->field_f
    //     0x8c58fc: ldur            w2, [x3, #0xf]
    // 0x8c5900: DecompressPointer r2
    //     0x8c5900: add             x2, x2, HEAP, lsl #32
    // 0x8c5904: r1 = Null
    //     0x8c5904: mov             x1, NULL
    // 0x8c5908: r0 = Digest()
    //     0x8c5908: bl              #0x8c5a54  ; [package:pointycastle/api.dart] Digest::Digest
    // 0x8c590c: ldur            x0, [fp, #-8]
    // 0x8c5910: LoadField: r2 = r0->field_13
    //     0x8c5910: ldur            w2, [x0, #0x13]
    // 0x8c5914: DecompressPointer r2
    //     0x8c5914: add             x2, x2, HEAP, lsl #32
    // 0x8c5918: stur            x2, [fp, #-0x10]
    // 0x8c591c: r0 = RSASigner()
    //     0x8c591c: bl              #0x8c5a48  ; AllocateRSASignerStub -> RSASigner (size=0x8)
    // 0x8c5920: mov             x1, x0
    // 0x8c5924: ldur            x2, [fp, #-0x10]
    // 0x8c5928: stur            x0, [fp, #-8]
    // 0x8c592c: r0 = _hexStringToBytes()
    //     0x8c592c: bl              #0x8c5948  ; [package:pointycastle/signers/rsa_signer.dart] RSASigner::_hexStringToBytes
    // 0x8c5930: ldur            x0, [fp, #-8]
    // 0x8c5934: LeaveFrame
    //     0x8c5934: mov             SP, fp
    //     0x8c5938: ldp             fp, lr, [SP], #0x10
    // 0x8c593c: ret
    //     0x8c593c: ret             
    // 0x8c5940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5940: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5944: b               #0x8c58fc
  }
  _ _hexStringToBytes(/* No info */) {
    // ** addr: 0x8c5948, size: 0x100
    // 0x8c5948: EnterFrame
    //     0x8c5948: stp             fp, lr, [SP, #-0x10]!
    //     0x8c594c: mov             fp, SP
    // 0x8c5950: AllocStack(0x38)
    //     0x8c5950: sub             SP, SP, #0x38
    // 0x8c5954: r0 = 2
    //     0x8c5954: movz            x0, #0x2
    // 0x8c5958: mov             x16, x2
    // 0x8c595c: mov             x2, x1
    // 0x8c5960: mov             x1, x16
    // 0x8c5964: stur            x1, [fp, #-0x18]
    // 0x8c5968: CheckStackOverflow
    //     0x8c5968: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c596c: cmp             SP, x16
    //     0x8c5970: b.ls            #0x8c5a34
    // 0x8c5974: LoadField: r2 = r1->field_7
    //     0x8c5974: ldur            w2, [x1, #7]
    // 0x8c5978: r3 = LoadInt32Instr(r2)
    //     0x8c5978: sbfx            x3, x2, #1, #0x1f
    // 0x8c597c: stur            x3, [fp, #-0x10]
    // 0x8c5980: sdiv            x2, x3, x0
    // 0x8c5984: stur            x2, [fp, #-8]
    // 0x8c5988: lsl             x4, x2, #1
    // 0x8c598c: r0 = AllocateUint8Array()
    //     0x8c598c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c5990: stur            x0, [fp, #-0x30]
    // 0x8c5994: r5 = 0
    //     0x8c5994: movz            x5, #0
    // 0x8c5998: ldur            x4, [fp, #-0x10]
    // 0x8c599c: stur            x5, [fp, #-0x28]
    // 0x8c59a0: CheckStackOverflow
    //     0x8c59a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c59a4: cmp             SP, x16
    //     0x8c59a8: b.ls            #0x8c5a3c
    // 0x8c59ac: cmp             x5, x4
    // 0x8c59b0: b.ge            #0x8c5a28
    // 0x8c59b4: add             x6, x5, #2
    // 0x8c59b8: stur            x6, [fp, #-0x20]
    // 0x8c59bc: lsl             x2, x6, #1
    // 0x8c59c0: mov             x1, x5
    // 0x8c59c4: mov             x3, x4
    // 0x8c59c8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x8c59c8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x8c59cc: r0 = checkValidRange()
    //     0x8c59cc: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x8c59d0: ldur            x1, [fp, #-0x18]
    // 0x8c59d4: ldur            x2, [fp, #-0x28]
    // 0x8c59d8: mov             x3, x0
    // 0x8c59dc: r0 = _substringUnchecked()
    //     0x8c59dc: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0x8c59e0: r16 = 32
    //     0x8c59e0: movz            x16, #0x20
    // 0x8c59e4: str             x16, [SP]
    // 0x8c59e8: mov             x1, x0
    // 0x8c59ec: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0x8c59ec: ldr             x4, [PP, #0xf20]  ; [pp+0xf20] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0x8c59f0: r0 = parse()
    //     0x8c59f0: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8c59f4: mov             x4, x0
    // 0x8c59f8: ldur            x3, [fp, #-0x28]
    // 0x8c59fc: r2 = 2
    //     0x8c59fc: movz            x2, #0x2
    // 0x8c5a00: sdiv            x5, x3, x2
    // 0x8c5a04: ldur            x0, [fp, #-8]
    // 0x8c5a08: mov             x1, x5
    // 0x8c5a0c: cmp             x1, x0
    // 0x8c5a10: b.hs            #0x8c5a44
    // 0x8c5a14: ldur            x0, [fp, #-0x30]
    // 0x8c5a18: ArrayStore: r0[r5] = r4  ; TypeUnknown_1
    //     0x8c5a18: add             x1, x0, x5
    //     0x8c5a1c: strb            w4, [x1, #0x17]
    // 0x8c5a20: ldur            x5, [fp, #-0x20]
    // 0x8c5a24: b               #0x8c5998
    // 0x8c5a28: LeaveFrame
    //     0x8c5a28: mov             SP, fp
    //     0x8c5a2c: ldp             fp, lr, [SP], #0x10
    // 0x8c5a30: ret
    //     0x8c5a30: ret             
    // 0x8c5a34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5a34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5a38: b               #0x8c5974
    // 0x8c5a3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5a3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5a40: b               #0x8c59ac
    // 0x8c5a44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8c5a44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static Map<String, String> _digestIdentifierHexes() {
    // ** addr: 0x8c5ac0, size: 0x150
    // 0x8c5ac0: EnterFrame
    //     0x8c5ac0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5ac4: mov             fp, SP
    // 0x8c5ac8: AllocStack(0x10)
    //     0x8c5ac8: sub             SP, SP, #0x10
    // 0x8c5acc: CheckStackOverflow
    //     0x8c5acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5ad0: cmp             SP, x16
    //     0x8c5ad4: b.ls            #0x8c5c08
    // 0x8c5ad8: r1 = Null
    //     0x8c5ad8: mov             x1, NULL
    // 0x8c5adc: r2 = 44
    //     0x8c5adc: movz            x2, #0x2c
    // 0x8c5ae0: r0 = AllocateArray()
    //     0x8c5ae0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c5ae4: r16 = "MD2"
    //     0x8c5ae4: add             x16, PP, #0x18, lsl #12  ; [pp+0x181c0] "MD2"
    //     0x8c5ae8: ldr             x16, [x16, #0x1c0]
    // 0x8c5aec: StoreField: r0->field_f = r16
    //     0x8c5aec: stur            w16, [x0, #0xf]
    // 0x8c5af0: r16 = "06082a864886f70d0202"
    //     0x8c5af0: add             x16, PP, #0x18, lsl #12  ; [pp+0x181c8] "06082a864886f70d0202"
    //     0x8c5af4: ldr             x16, [x16, #0x1c8]
    // 0x8c5af8: StoreField: r0->field_13 = r16
    //     0x8c5af8: stur            w16, [x0, #0x13]
    // 0x8c5afc: r16 = "MD4"
    //     0x8c5afc: add             x16, PP, #0x18, lsl #12  ; [pp+0x181d0] "MD4"
    //     0x8c5b00: ldr             x16, [x16, #0x1d0]
    // 0x8c5b04: ArrayStore: r0[0] = r16  ; List_4
    //     0x8c5b04: stur            w16, [x0, #0x17]
    // 0x8c5b08: r16 = "06082a864886f70d0204"
    //     0x8c5b08: add             x16, PP, #0x18, lsl #12  ; [pp+0x181d8] "06082a864886f70d0204"
    //     0x8c5b0c: ldr             x16, [x16, #0x1d8]
    // 0x8c5b10: StoreField: r0->field_1b = r16
    //     0x8c5b10: stur            w16, [x0, #0x1b]
    // 0x8c5b14: r16 = "MD5"
    //     0x8c5b14: add             x16, PP, #0x18, lsl #12  ; [pp+0x181e0] "MD5"
    //     0x8c5b18: ldr             x16, [x16, #0x1e0]
    // 0x8c5b1c: StoreField: r0->field_1f = r16
    //     0x8c5b1c: stur            w16, [x0, #0x1f]
    // 0x8c5b20: r16 = "06082a864886f70d0205"
    //     0x8c5b20: add             x16, PP, #0x18, lsl #12  ; [pp+0x181e8] "06082a864886f70d0205"
    //     0x8c5b24: ldr             x16, [x16, #0x1e8]
    // 0x8c5b28: StoreField: r0->field_23 = r16
    //     0x8c5b28: stur            w16, [x0, #0x23]
    // 0x8c5b2c: r16 = "RIPEMD-128"
    //     0x8c5b2c: add             x16, PP, #0x18, lsl #12  ; [pp+0x181f0] "RIPEMD-128"
    //     0x8c5b30: ldr             x16, [x16, #0x1f0]
    // 0x8c5b34: StoreField: r0->field_27 = r16
    //     0x8c5b34: stur            w16, [x0, #0x27]
    // 0x8c5b38: r16 = "06052b24030202"
    //     0x8c5b38: add             x16, PP, #0x18, lsl #12  ; [pp+0x181f8] "06052b24030202"
    //     0x8c5b3c: ldr             x16, [x16, #0x1f8]
    // 0x8c5b40: StoreField: r0->field_2b = r16
    //     0x8c5b40: stur            w16, [x0, #0x2b]
    // 0x8c5b44: r16 = "RIPEMD-160"
    //     0x8c5b44: add             x16, PP, #0x18, lsl #12  ; [pp+0x18200] "RIPEMD-160"
    //     0x8c5b48: ldr             x16, [x16, #0x200]
    // 0x8c5b4c: StoreField: r0->field_2f = r16
    //     0x8c5b4c: stur            w16, [x0, #0x2f]
    // 0x8c5b50: r16 = "06052b24030201"
    //     0x8c5b50: add             x16, PP, #0x18, lsl #12  ; [pp+0x18208] "06052b24030201"
    //     0x8c5b54: ldr             x16, [x16, #0x208]
    // 0x8c5b58: StoreField: r0->field_33 = r16
    //     0x8c5b58: stur            w16, [x0, #0x33]
    // 0x8c5b5c: r16 = "RIPEMD-256"
    //     0x8c5b5c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18210] "RIPEMD-256"
    //     0x8c5b60: ldr             x16, [x16, #0x210]
    // 0x8c5b64: StoreField: r0->field_37 = r16
    //     0x8c5b64: stur            w16, [x0, #0x37]
    // 0x8c5b68: r16 = "06052b24030203"
    //     0x8c5b68: add             x16, PP, #0x18, lsl #12  ; [pp+0x18218] "06052b24030203"
    //     0x8c5b6c: ldr             x16, [x16, #0x218]
    // 0x8c5b70: StoreField: r0->field_3b = r16
    //     0x8c5b70: stur            w16, [x0, #0x3b]
    // 0x8c5b74: r16 = "SHA-1"
    //     0x8c5b74: add             x16, PP, #0x18, lsl #12  ; [pp+0x18220] "SHA-1"
    //     0x8c5b78: ldr             x16, [x16, #0x220]
    // 0x8c5b7c: StoreField: r0->field_3f = r16
    //     0x8c5b7c: stur            w16, [x0, #0x3f]
    // 0x8c5b80: r16 = "06052b0e03021a"
    //     0x8c5b80: add             x16, PP, #0x18, lsl #12  ; [pp+0x18228] "06052b0e03021a"
    //     0x8c5b84: ldr             x16, [x16, #0x228]
    // 0x8c5b88: StoreField: r0->field_43 = r16
    //     0x8c5b88: stur            w16, [x0, #0x43]
    // 0x8c5b8c: r16 = "SHA-224"
    //     0x8c5b8c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18230] "SHA-224"
    //     0x8c5b90: ldr             x16, [x16, #0x230]
    // 0x8c5b94: StoreField: r0->field_47 = r16
    //     0x8c5b94: stur            w16, [x0, #0x47]
    // 0x8c5b98: r16 = "0609608648016503040204"
    //     0x8c5b98: add             x16, PP, #0x18, lsl #12  ; [pp+0x18238] "0609608648016503040204"
    //     0x8c5b9c: ldr             x16, [x16, #0x238]
    // 0x8c5ba0: StoreField: r0->field_4b = r16
    //     0x8c5ba0: stur            w16, [x0, #0x4b]
    // 0x8c5ba4: r16 = "SHA-256"
    //     0x8c5ba4: add             x16, PP, #0x18, lsl #12  ; [pp+0x18240] "SHA-256"
    //     0x8c5ba8: ldr             x16, [x16, #0x240]
    // 0x8c5bac: StoreField: r0->field_4f = r16
    //     0x8c5bac: stur            w16, [x0, #0x4f]
    // 0x8c5bb0: r16 = "0609608648016503040201"
    //     0x8c5bb0: add             x16, PP, #0x18, lsl #12  ; [pp+0x18248] "0609608648016503040201"
    //     0x8c5bb4: ldr             x16, [x16, #0x248]
    // 0x8c5bb8: StoreField: r0->field_53 = r16
    //     0x8c5bb8: stur            w16, [x0, #0x53]
    // 0x8c5bbc: r16 = "SHA-384"
    //     0x8c5bbc: add             x16, PP, #0x18, lsl #12  ; [pp+0x18250] "SHA-384"
    //     0x8c5bc0: ldr             x16, [x16, #0x250]
    // 0x8c5bc4: StoreField: r0->field_57 = r16
    //     0x8c5bc4: stur            w16, [x0, #0x57]
    // 0x8c5bc8: r16 = "0609608648016503040202"
    //     0x8c5bc8: add             x16, PP, #0x18, lsl #12  ; [pp+0x18258] "0609608648016503040202"
    //     0x8c5bcc: ldr             x16, [x16, #0x258]
    // 0x8c5bd0: StoreField: r0->field_5b = r16
    //     0x8c5bd0: stur            w16, [x0, #0x5b]
    // 0x8c5bd4: r16 = "SHA-512"
    //     0x8c5bd4: add             x16, PP, #0x18, lsl #12  ; [pp+0x18260] "SHA-512"
    //     0x8c5bd8: ldr             x16, [x16, #0x260]
    // 0x8c5bdc: StoreField: r0->field_5f = r16
    //     0x8c5bdc: stur            w16, [x0, #0x5f]
    // 0x8c5be0: r16 = "0609608648016503040203"
    //     0x8c5be0: add             x16, PP, #0x18, lsl #12  ; [pp+0x18268] "0609608648016503040203"
    //     0x8c5be4: ldr             x16, [x16, #0x268]
    // 0x8c5be8: StoreField: r0->field_63 = r16
    //     0x8c5be8: stur            w16, [x0, #0x63]
    // 0x8c5bec: r16 = <String, String>
    //     0x8c5bec: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0x8c5bf0: ldr             x16, [x16, #0x668]
    // 0x8c5bf4: stp             x0, x16, [SP]
    // 0x8c5bf8: r0 = Map._fromLiteral()
    //     0x8c5bf8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8c5bfc: LeaveFrame
    //     0x8c5bfc: mov             SP, fp
    //     0x8c5c00: ldp             fp, lr, [SP], #0x10
    // 0x8c5c04: ret
    //     0x8c5c04: ret             
    // 0x8c5c08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5c08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5c0c: b               #0x8c5ad8
  }
}
