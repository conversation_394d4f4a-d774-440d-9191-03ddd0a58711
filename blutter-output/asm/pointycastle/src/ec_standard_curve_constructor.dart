// lib: , url: package:pointycastle/src/ec_standard_curve_constructor.dart

// class id: 1051030, size: 0x8
class :: {

  static _ constructFpStandardCurve(/* No info */) {
    // ** addr: 0x8c9560, size: 0xd8
    // 0x8c9560: EnterFrame
    //     0x8c9560: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9564: mov             fp, SP
    // 0x8c9568: AllocStack(0x68)
    //     0x8c9568: sub             SP, SP, #0x68
    // 0x8c956c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r1, fp-0x28 */, dynamic _ /* r7 => r7, fp-0x30 */)
    //     0x8c956c: mov             x0, x1
    //     0x8c9570: stur            x1, [fp, #-8]
    //     0x8c9574: mov             x1, x6
    //     0x8c9578: stur            x2, [fp, #-0x10]
    //     0x8c957c: stur            x3, [fp, #-0x18]
    //     0x8c9580: stur            x5, [fp, #-0x20]
    //     0x8c9584: stur            x6, [fp, #-0x28]
    //     0x8c9588: stur            x7, [fp, #-0x30]
    // 0x8c958c: CheckStackOverflow
    //     0x8c958c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9590: cmp             SP, x16
    //     0x8c9594: b.ls            #0x8c9630
    // 0x8c9598: r0 = ECCurve()
    //     0x8c9598: bl              #0x8d0e0c  ; AllocateECCurveStub -> ECCurve (size=0x18)
    // 0x8c959c: mov             x1, x0
    // 0x8c95a0: ldr             x2, [fp, #0x18]
    // 0x8c95a4: ldur            x3, [fp, #-0x18]
    // 0x8c95a8: ldur            x5, [fp, #-0x20]
    // 0x8c95ac: stur            x0, [fp, #-0x18]
    // 0x8c95b0: r0 = ECCurve()
    //     0x8c95b0: bl              #0x8d0ccc  ; [package:pointycastle/ecc/ecc_fp.dart] ECCurve::ECCurve
    // 0x8c95b4: ldr             x1, [fp, #0x10]
    // 0x8c95b8: cmp             w1, NULL
    // 0x8c95bc: b.ne            #0x8c95c8
    // 0x8c95c0: r0 = Null
    //     0x8c95c0: mov             x0, NULL
    // 0x8c95c4: b               #0x8c95cc
    // 0x8c95c8: r0 = encodeBigInt()
    //     0x8c95c8: bl              #0x8d09c8  ; [package:pointycastle/src/utils.dart] ::encodeBigInt
    // 0x8c95cc: ldur            x1, [fp, #-0x28]
    // 0x8c95d0: stur            x0, [fp, #-0x20]
    // 0x8c95d4: r0 = encodeBigInt()
    //     0x8c95d4: bl              #0x8d09c8  ; [package:pointycastle/src/utils.dart] ::encodeBigInt
    // 0x8c95d8: ldur            x1, [fp, #-0x18]
    // 0x8c95dc: mov             x2, x0
    // 0x8c95e0: r0 = decodePoint()
    //     0x8c95e0: bl              #0x8c9638  ; [package:pointycastle/ecc/ecc_base.dart] ECCurveBase::decodePoint
    // 0x8c95e4: ldur            x16, [fp, #-0x10]
    // 0x8c95e8: ldur            lr, [fp, #-8]
    // 0x8c95ec: stp             lr, x16, [SP, #0x28]
    // 0x8c95f0: ldur            x16, [fp, #-0x18]
    // 0x8c95f4: stp             x0, x16, [SP, #0x18]
    // 0x8c95f8: ldr             x16, [fp, #0x20]
    // 0x8c95fc: ldur            lr, [fp, #-0x30]
    // 0x8c9600: stp             lr, x16, [SP, #8]
    // 0x8c9604: ldur            x16, [fp, #-0x20]
    // 0x8c9608: str             x16, [SP]
    // 0x8c960c: r4 = 0
    //     0x8c960c: movz            x4, #0
    // 0x8c9610: ldr             x0, [SP, #0x30]
    // 0x8c9614: r16 = UnlinkedCall_0x5f3c08
    //     0x8c9614: add             x16, PP, #0x18, lsl #12  ; [pp+0x18730] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8c9618: add             x16, x16, #0x730
    // 0x8c961c: ldp             x5, lr, [x16]
    // 0x8c9620: blr             lr
    // 0x8c9624: LeaveFrame
    //     0x8c9624: mov             SP, fp
    //     0x8c9628: ldp             fp, lr, [SP], #0x10
    // 0x8c962c: ret
    //     0x8c962c: ret             
    // 0x8c9630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9634: b               #0x8c9598
  }
}
