// lib: , url: package:pointycastle/src/impl/base_aead_block_cipher.dart

// class id: 1051031, size: 0x8
class :: {
}

// class id: 669, size: 0x30, field offset: 0x8
abstract class BaseAEADBlockCipher extends Object
    implements AEADBlockCipher {

  late Uint8List _initialAssociatedText; // offset: 0x1c
  late int _macSize; // offset: 0x10
  late Uint8List _nonce; // offset: 0x18
  late bool _forEncryption; // offset: 0xc
  late int _lastMacSizeBytesOff; // offset: 0x2c

  _ reset(/* No info */) {
    // ** addr: 0xe81e7c, size: 0xb0
    // 0xe81e7c: EnterFrame
    //     0xe81e7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe81e80: mov             fp, SP
    // 0xe81e84: AllocStack(0x10)
    //     0xe81e84: sub             SP, SP, #0x10
    // 0xe81e88: SetupParameters(BaseAEADBlockCipher this /* r1 => r1, fp-0x10 */)
    //     0xe81e88: stur            x1, [fp, #-0x10]
    // 0xe81e8c: CheckStackOverflow
    //     0xe81e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe81e90: cmp             SP, x16
    //     0xe81e94: b.ls            #0xe81f18
    // 0xe81e98: StoreField: r1->field_23 = rZR
    //     0xe81e98: stur            wzr, [x1, #0x23]
    // 0xe81e9c: StoreField: r1->field_2b = rZR
    //     0xe81e9c: stur            wzr, [x1, #0x2b]
    // 0xe81ea0: LoadField: r0 = r1->field_13
    //     0xe81ea0: ldur            w0, [x1, #0x13]
    // 0xe81ea4: DecompressPointer r0
    //     0xe81ea4: add             x0, x0, HEAP, lsl #32
    // 0xe81ea8: stur            x0, [fp, #-8]
    // 0xe81eac: cmp             w0, NULL
    // 0xe81eb0: b.ne            #0xe81ec4
    // 0xe81eb4: r0 = Null
    //     0xe81eb4: mov             x0, NULL
    // 0xe81eb8: LeaveFrame
    //     0xe81eb8: mov             SP, fp
    //     0xe81ebc: ldp             fp, lr, [SP], #0x10
    // 0xe81ec0: ret
    //     0xe81ec0: ret             
    // 0xe81ec4: r0 = KeyParameter()
    //     0xe81ec4: bl              #0x8c3010  ; AllocateKeyParameterStub -> KeyParameter (size=0xc)
    // 0xe81ec8: mov             x1, x0
    // 0xe81ecc: ldur            x0, [fp, #-8]
    // 0xe81ed0: StoreField: r1->field_7 = r0
    //     0xe81ed0: stur            w0, [x1, #7]
    // 0xe81ed4: mov             x2, x1
    // 0xe81ed8: ldur            x1, [fp, #-0x10]
    // 0xe81edc: r0 = prepare()
    //     0xe81edc: bl              #0xe82718  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::prepare
    // 0xe81ee0: ldur            x1, [fp, #-0x10]
    // 0xe81ee4: LoadField: r2 = r1->field_1b
    //     0xe81ee4: ldur            w2, [x1, #0x1b]
    // 0xe81ee8: DecompressPointer r2
    //     0xe81ee8: add             x2, x2, HEAP, lsl #32
    // 0xe81eec: r16 = Sentinel
    //     0xe81eec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe81ef0: cmp             w2, w16
    // 0xe81ef4: b.eq            #0xe81f20
    // 0xe81ef8: LoadField: r0 = r2->field_13
    //     0xe81ef8: ldur            w0, [x2, #0x13]
    // 0xe81efc: r5 = LoadInt32Instr(r0)
    //     0xe81efc: sbfx            x5, x0, #1, #0x1f
    // 0xe81f00: r3 = 0
    //     0xe81f00: movz            x3, #0
    // 0xe81f04: r0 = processAADBytes()
    //     0xe81f04: bl              #0xe81f2c  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::processAADBytes
    // 0xe81f08: r0 = Null
    //     0xe81f08: mov             x0, NULL
    // 0xe81f0c: LeaveFrame
    //     0xe81f0c: mov             SP, fp
    //     0xe81f10: ldp             fp, lr, [SP], #0x10
    // 0xe81f14: ret
    //     0xe81f14: ret             
    // 0xe81f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe81f18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe81f1c: b               #0xe81e98
    // 0xe81f20: r9 = _initialAssociatedText
    //     0xe81f20: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a08] Field <BaseAEADBlockCipher._initialAssociatedText@2660101045>: late (offset: 0x1c)
    //     0xe81f24: ldr             x9, [x9, #0xa08]
    // 0xe81f28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe81f28: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0xe8d028, size: 0x29c
    // 0xe8d028: EnterFrame
    //     0xe8d028: stp             fp, lr, [SP, #-0x10]!
    //     0xe8d02c: mov             fp, SP
    // 0xe8d030: AllocStack(0x20)
    //     0xe8d030: sub             SP, SP, #0x20
    // 0xe8d034: SetupParameters(BaseAEADBlockCipher this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xe8d034: stur            x1, [fp, #-0x10]
    //     0xe8d038: stur            x2, [fp, #-0x18]
    //     0xe8d03c: stur            x3, [fp, #-0x20]
    // 0xe8d040: CheckStackOverflow
    //     0xe8d040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8d044: cmp             SP, x16
    //     0xe8d048: b.ls            #0xe8d2b0
    // 0xe8d04c: StoreField: r1->field_b = r2
    //     0xe8d04c: stur            w2, [x1, #0xb]
    // 0xe8d050: r0 = LoadClassIdInstr(r3)
    //     0xe8d050: ldur            x0, [x3, #-1]
    //     0xe8d054: ubfx            x0, x0, #0xc, #0x14
    // 0xe8d058: cmp             x0, #0x2ad
    // 0xe8d05c: b.ne            #0xe8d0b0
    // 0xe8d060: r5 = 32
    //     0xe8d060: movz            x5, #0x20
    // 0xe8d064: LoadField: r4 = r3->field_13
    //     0xe8d064: ldur            w4, [x3, #0x13]
    // 0xe8d068: DecompressPointer r4
    //     0xe8d068: add             x4, x4, HEAP, lsl #32
    // 0xe8d06c: LoadField: r0 = r3->field_f
    //     0xe8d06c: ldur            w0, [x3, #0xf]
    // 0xe8d070: DecompressPointer r0
    //     0xe8d070: add             x0, x0, HEAP, lsl #32
    // 0xe8d074: StoreField: r1->field_1b = r0
    //     0xe8d074: stur            w0, [x1, #0x1b]
    //     0xe8d078: ldurb           w16, [x1, #-1]
    //     0xe8d07c: ldurb           w17, [x0, #-1]
    //     0xe8d080: and             x16, x17, x16, lsr #2
    //     0xe8d084: tst             x16, HEAP, lsr #32
    //     0xe8d088: b.eq            #0xe8d090
    //     0xe8d08c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8d090: StoreField: r1->field_f = r5
    //     0xe8d090: stur            w5, [x1, #0xf]
    // 0xe8d094: LoadField: r0 = r3->field_b
    //     0xe8d094: ldur            w0, [x3, #0xb]
    // 0xe8d098: DecompressPointer r0
    //     0xe8d098: add             x0, x0, HEAP, lsl #32
    // 0xe8d09c: mov             x3, x4
    // 0xe8d0a0: mov             x4, x0
    // 0xe8d0a4: mov             x0, x2
    // 0xe8d0a8: mov             x2, x1
    // 0xe8d0ac: b               #0xe8d110
    // 0xe8d0b0: r5 = 32
    //     0xe8d0b0: movz            x5, #0x20
    // 0xe8d0b4: cmp             x0, #0x2a8
    // 0xe8d0b8: b.ne            #0xe8d284
    // 0xe8d0bc: LoadField: r0 = r3->field_b
    //     0xe8d0bc: ldur            w0, [x3, #0xb]
    // 0xe8d0c0: DecompressPointer r0
    //     0xe8d0c0: add             x0, x0, HEAP, lsl #32
    // 0xe8d0c4: stur            x0, [fp, #-8]
    // 0xe8d0c8: r4 = 0
    //     0xe8d0c8: movz            x4, #0
    // 0xe8d0cc: r0 = AllocateUint8Array()
    //     0xe8d0cc: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8d0d0: ldur            x2, [fp, #-0x10]
    // 0xe8d0d4: StoreField: r2->field_1b = r0
    //     0xe8d0d4: stur            w0, [x2, #0x1b]
    //     0xe8d0d8: ldurb           w16, [x2, #-1]
    //     0xe8d0dc: ldurb           w17, [x0, #-1]
    //     0xe8d0e0: and             x16, x17, x16, lsr #2
    //     0xe8d0e4: tst             x16, HEAP, lsr #32
    //     0xe8d0e8: b.eq            #0xe8d0f0
    //     0xe8d0ec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe8d0f0: r0 = 32
    //     0xe8d0f0: movz            x0, #0x20
    // 0xe8d0f4: StoreField: r2->field_f = r0
    //     0xe8d0f4: stur            w0, [x2, #0xf]
    // 0xe8d0f8: ldur            x0, [fp, #-0x20]
    // 0xe8d0fc: LoadField: r1 = r0->field_f
    //     0xe8d0fc: ldur            w1, [x0, #0xf]
    // 0xe8d100: DecompressPointer r1
    //     0xe8d100: add             x1, x1, HEAP, lsl #32
    // 0xe8d104: mov             x4, x1
    // 0xe8d108: ldur            x3, [fp, #-8]
    // 0xe8d10c: ldur            x0, [fp, #-0x18]
    // 0xe8d110: stur            x4, [fp, #-8]
    // 0xe8d114: stur            x3, [fp, #-0x20]
    // 0xe8d118: tbnz            w0, #4, #0xe8d144
    // 0xe8d11c: LoadField: r1 = r2->field_7
    //     0xe8d11c: ldur            w1, [x2, #7]
    // 0xe8d120: DecompressPointer r1
    //     0xe8d120: add             x1, x1, HEAP, lsl #32
    // 0xe8d124: r0 = LoadClassIdInstr(r1)
    //     0xe8d124: ldur            x0, [x1, #-1]
    //     0xe8d128: ubfx            x0, x0, #0xc, #0x14
    // 0xe8d12c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe8d12c: sub             lr, x0, #1, lsl #12
    //     0xe8d130: ldr             lr, [x21, lr, lsl #3]
    //     0xe8d134: blr             lr
    // 0xe8d138: mov             x4, x0
    // 0xe8d13c: ldur            x2, [fp, #-0x10]
    // 0xe8d140: b               #0xe8d180
    // 0xe8d144: LoadField: r1 = r2->field_7
    //     0xe8d144: ldur            w1, [x2, #7]
    // 0xe8d148: DecompressPointer r1
    //     0xe8d148: add             x1, x1, HEAP, lsl #32
    // 0xe8d14c: r0 = LoadClassIdInstr(r1)
    //     0xe8d14c: ldur            x0, [x1, #-1]
    //     0xe8d150: ubfx            x0, x0, #0xc, #0x14
    // 0xe8d154: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe8d154: sub             lr, x0, #1, lsl #12
    //     0xe8d158: ldr             lr, [x21, lr, lsl #3]
    //     0xe8d15c: blr             lr
    // 0xe8d160: ldur            x2, [fp, #-0x10]
    // 0xe8d164: LoadField: r1 = r2->field_f
    //     0xe8d164: ldur            w1, [x2, #0xf]
    // 0xe8d168: DecompressPointer r1
    //     0xe8d168: add             x1, x1, HEAP, lsl #32
    // 0xe8d16c: r3 = LoadInt32Instr(r1)
    //     0xe8d16c: sbfx            x3, x1, #1, #0x1f
    //     0xe8d170: tbz             w1, #0, #0xe8d178
    //     0xe8d174: ldur            x3, [x1, #7]
    // 0xe8d178: add             x1, x0, x3
    // 0xe8d17c: mov             x4, x1
    // 0xe8d180: ldur            x3, [fp, #-0x20]
    // 0xe8d184: r0 = BoxInt64Instr(r4)
    //     0xe8d184: sbfiz           x0, x4, #1, #0x1f
    //     0xe8d188: cmp             x4, x0, asr #1
    //     0xe8d18c: b.eq            #0xe8d198
    //     0xe8d190: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe8d194: stur            x4, [x0, #7]
    // 0xe8d198: mov             x4, x0
    // 0xe8d19c: r0 = AllocateUint8Array()
    //     0xe8d19c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8d1a0: ldur            x1, [fp, #-0x10]
    // 0xe8d1a4: StoreField: r1->field_1f = r0
    //     0xe8d1a4: stur            w0, [x1, #0x1f]
    //     0xe8d1a8: ldurb           w16, [x1, #-1]
    //     0xe8d1ac: ldurb           w17, [x0, #-1]
    //     0xe8d1b0: and             x16, x17, x16, lsr #2
    //     0xe8d1b4: tst             x16, HEAP, lsr #32
    //     0xe8d1b8: b.eq            #0xe8d1c0
    //     0xe8d1bc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8d1c0: ldur            x0, [fp, #-0x20]
    // 0xe8d1c4: LoadField: r2 = r0->field_13
    //     0xe8d1c4: ldur            w2, [x0, #0x13]
    // 0xe8d1c8: cbz             w2, #0xe8d25c
    // 0xe8d1cc: ldur            x2, [fp, #-8]
    // 0xe8d1d0: ArrayStore: r1[0] = r0  ; List_4
    //     0xe8d1d0: stur            w0, [x1, #0x17]
    //     0xe8d1d4: ldurb           w16, [x1, #-1]
    //     0xe8d1d8: ldurb           w17, [x0, #-1]
    //     0xe8d1dc: and             x16, x17, x16, lsr #2
    //     0xe8d1e0: tst             x16, HEAP, lsr #32
    //     0xe8d1e4: b.eq            #0xe8d1ec
    //     0xe8d1e8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8d1ec: LoadField: r0 = r2->field_7
    //     0xe8d1ec: ldur            w0, [x2, #7]
    // 0xe8d1f0: DecompressPointer r0
    //     0xe8d1f0: add             x0, x0, HEAP, lsl #32
    // 0xe8d1f4: r16 = Sentinel
    //     0xe8d1f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8d1f8: cmp             w0, w16
    // 0xe8d1fc: b.eq            #0xe8d2b8
    // 0xe8d200: StoreField: r1->field_13 = r0
    //     0xe8d200: stur            w0, [x1, #0x13]
    //     0xe8d204: ldurb           w16, [x1, #-1]
    //     0xe8d208: ldurb           w17, [x0, #-1]
    //     0xe8d20c: and             x16, x17, x16, lsr #2
    //     0xe8d210: tst             x16, HEAP, lsr #32
    //     0xe8d214: b.eq            #0xe8d21c
    //     0xe8d218: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8d21c: LoadField: r4 = r1->field_f
    //     0xe8d21c: ldur            w4, [x1, #0xf]
    // 0xe8d220: DecompressPointer r4
    //     0xe8d220: add             x4, x4, HEAP, lsl #32
    // 0xe8d224: r0 = AllocateUint8Array()
    //     0xe8d224: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xe8d228: ldur            x1, [fp, #-0x10]
    // 0xe8d22c: StoreField: r1->field_27 = r0
    //     0xe8d22c: stur            w0, [x1, #0x27]
    //     0xe8d230: ldurb           w16, [x1, #-1]
    //     0xe8d234: ldurb           w17, [x0, #-1]
    //     0xe8d238: and             x16, x17, x16, lsr #2
    //     0xe8d23c: tst             x16, HEAP, lsr #32
    //     0xe8d240: b.eq            #0xe8d248
    //     0xe8d244: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe8d248: r0 = reset()
    //     0xe8d248: bl              #0xe81df4  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::reset
    // 0xe8d24c: r0 = Null
    //     0xe8d24c: mov             x0, NULL
    // 0xe8d250: LeaveFrame
    //     0xe8d250: mov             SP, fp
    //     0xe8d254: ldp             fp, lr, [SP], #0x10
    // 0xe8d258: ret
    //     0xe8d258: ret             
    // 0xe8d25c: r0 = ArgumentError()
    //     0xe8d25c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe8d260: mov             x1, x0
    // 0xe8d264: r0 = "IV must be at least 1 byte"
    //     0xe8d264: add             x0, PP, #0x20, lsl #12  ; [pp+0x20a70] "IV must be at least 1 byte"
    //     0xe8d268: ldr             x0, [x0, #0xa70]
    // 0xe8d26c: ArrayStore: r1[0] = r0  ; List_4
    //     0xe8d26c: stur            w0, [x1, #0x17]
    // 0xe8d270: r0 = false
    //     0xe8d270: add             x0, NULL, #0x30  ; false
    // 0xe8d274: StoreField: r1->field_b = r0
    //     0xe8d274: stur            w0, [x1, #0xb]
    // 0xe8d278: mov             x0, x1
    // 0xe8d27c: r0 = Throw()
    //     0xe8d27c: bl              #0xec04b8  ; ThrowStub
    // 0xe8d280: brk             #0
    // 0xe8d284: r0 = false
    //     0xe8d284: add             x0, NULL, #0x30  ; false
    // 0xe8d288: r0 = ArgumentError()
    //     0xe8d288: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xe8d28c: mov             x1, x0
    // 0xe8d290: r0 = "invalid parameters passed to AEADBlockCipher"
    //     0xe8d290: add             x0, PP, #0x20, lsl #12  ; [pp+0x20a78] "invalid parameters passed to AEADBlockCipher"
    //     0xe8d294: ldr             x0, [x0, #0xa78]
    // 0xe8d298: ArrayStore: r1[0] = r0  ; List_4
    //     0xe8d298: stur            w0, [x1, #0x17]
    // 0xe8d29c: r0 = false
    //     0xe8d29c: add             x0, NULL, #0x30  ; false
    // 0xe8d2a0: StoreField: r1->field_b = r0
    //     0xe8d2a0: stur            w0, [x1, #0xb]
    // 0xe8d2a4: mov             x0, x1
    // 0xe8d2a8: r0 = Throw()
    //     0xe8d2a8: bl              #0xec04b8  ; ThrowStub
    // 0xe8d2ac: brk             #0
    // 0xe8d2b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8d2b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8d2b4: b               #0xe8d04c
    // 0xe8d2b8: r9 = key
    //     0xe8d2b8: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a80] Field <KeyParameter.key>: late (offset: 0x8)
    //     0xe8d2bc: ldr             x9, [x9, #0xa80]
    // 0xe8d2c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe8d2c0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ process(/* No info */) {
    // ** addr: 0xeb3abc, size: 0xf0
    // 0xeb3abc: EnterFrame
    //     0xeb3abc: stp             fp, lr, [SP, #-0x10]!
    //     0xeb3ac0: mov             fp, SP
    // 0xeb3ac4: AllocStack(0x30)
    //     0xeb3ac4: sub             SP, SP, #0x30
    // 0xeb3ac8: SetupParameters(BaseAEADBlockCipher this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xeb3ac8: mov             x3, x1
    //     0xeb3acc: mov             x0, x2
    //     0xeb3ad0: stur            x1, [fp, #-0x10]
    //     0xeb3ad4: stur            x2, [fp, #-0x18]
    // 0xeb3ad8: CheckStackOverflow
    //     0xeb3ad8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb3adc: cmp             SP, x16
    //     0xeb3ae0: b.ls            #0xeb3ba4
    // 0xeb3ae4: LoadField: r1 = r0->field_13
    //     0xeb3ae4: ldur            w1, [x0, #0x13]
    // 0xeb3ae8: r4 = LoadInt32Instr(r1)
    //     0xeb3ae8: sbfx            x4, x1, #1, #0x1f
    // 0xeb3aec: mov             x1, x3
    // 0xeb3af0: mov             x2, x4
    // 0xeb3af4: stur            x4, [fp, #-8]
    // 0xeb3af8: r0 = getOutputSize()
    //     0xeb3af8: bl              #0xeb4e84  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::getOutputSize
    // 0xeb3afc: mov             x2, x0
    // 0xeb3b00: r0 = BoxInt64Instr(r2)
    //     0xeb3b00: sbfiz           x0, x2, #1, #0x1f
    //     0xeb3b04: cmp             x2, x0, asr #1
    //     0xeb3b08: b.eq            #0xeb3b14
    //     0xeb3b0c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3b10: stur            x2, [x0, #7]
    // 0xeb3b14: mov             x4, x0
    // 0xeb3b18: r0 = AllocateUint8Array()
    //     0xeb3b18: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xeb3b1c: ldur            x1, [fp, #-0x10]
    // 0xeb3b20: ldur            x2, [fp, #-0x18]
    // 0xeb3b24: ldur            x5, [fp, #-8]
    // 0xeb3b28: mov             x6, x0
    // 0xeb3b2c: r3 = 0
    //     0xeb3b2c: movz            x3, #0
    // 0xeb3b30: r7 = 0
    //     0xeb3b30: movz            x7, #0
    // 0xeb3b34: stur            x0, [fp, #-0x18]
    // 0xeb3b38: r0 = processBytes()
    //     0xeb3b38: bl              #0xeb435c  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::processBytes
    // 0xeb3b3c: ldur            x1, [fp, #-0x10]
    // 0xeb3b40: ldur            x2, [fp, #-0x18]
    // 0xeb3b44: mov             x3, x0
    // 0xeb3b48: stur            x0, [fp, #-8]
    // 0xeb3b4c: r0 = doFinal()
    //     0xeb3b4c: bl              #0xeb3bac  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::doFinal
    // 0xeb3b50: mov             x1, x0
    // 0xeb3b54: ldur            x0, [fp, #-8]
    // 0xeb3b58: add             x2, x0, x1
    // 0xeb3b5c: stur            x2, [fp, #-0x20]
    // 0xeb3b60: r0 = _ByteBuffer()
    //     0xeb3b60: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0xeb3b64: mov             x2, x0
    // 0xeb3b68: ldur            x0, [fp, #-0x18]
    // 0xeb3b6c: StoreField: r2->field_7 = r0
    //     0xeb3b6c: stur            w0, [x2, #7]
    // 0xeb3b70: ldur            x3, [fp, #-0x20]
    // 0xeb3b74: r0 = BoxInt64Instr(r3)
    //     0xeb3b74: sbfiz           x0, x3, #1, #0x1f
    //     0xeb3b78: cmp             x3, x0, asr #1
    //     0xeb3b7c: b.eq            #0xeb3b88
    //     0xeb3b80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3b84: stur            x3, [x0, #7]
    // 0xeb3b88: stp             x0, xzr, [SP]
    // 0xeb3b8c: mov             x1, x2
    // 0xeb3b90: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0xeb3b90: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0xeb3b94: r0 = asUint8List()
    //     0xeb3b94: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0xeb3b98: LeaveFrame
    //     0xeb3b98: mov             SP, fp
    //     0xeb3b9c: ldp             fp, lr, [SP], #0x10
    // 0xeb3ba0: ret
    //     0xeb3ba0: ret             
    // 0xeb3ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb3ba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb3ba8: b               #0xeb3ae4
  }
  _ validateMac(/* No info */) {
    // ** addr: 0xeb40a4, size: 0x118
    // 0xeb40a4: EnterFrame
    //     0xeb40a4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb40a8: mov             fp, SP
    // 0xeb40ac: CheckStackOverflow
    //     0xeb40ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb40b0: cmp             SP, x16
    //     0xeb40b4: b.ls            #0xeb4180
    // 0xeb40b8: LoadField: r0 = r1->field_b
    //     0xeb40b8: ldur            w0, [x1, #0xb]
    // 0xeb40bc: DecompressPointer r0
    //     0xeb40bc: add             x0, x0, HEAP, lsl #32
    // 0xeb40c0: r16 = Sentinel
    //     0xeb40c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb40c4: cmp             w0, w16
    // 0xeb40c8: b.eq            #0xeb4188
    // 0xeb40cc: tbnz            w0, #4, #0xeb40e0
    // 0xeb40d0: r0 = Null
    //     0xeb40d0: mov             x0, NULL
    // 0xeb40d4: LeaveFrame
    //     0xeb40d4: mov             SP, fp
    //     0xeb40d8: ldp             fp, lr, [SP], #0x10
    // 0xeb40dc: ret
    //     0xeb40dc: ret             
    // 0xeb40e0: LoadField: r0 = r1->field_2b
    //     0xeb40e0: ldur            w0, [x1, #0x2b]
    // 0xeb40e4: DecompressPointer r0
    //     0xeb40e4: add             x0, x0, HEAP, lsl #32
    // 0xeb40e8: r16 = Sentinel
    //     0xeb40e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb40ec: cmp             w0, w16
    // 0xeb40f0: b.eq            #0xeb4194
    // 0xeb40f4: LoadField: r2 = r1->field_f
    //     0xeb40f4: ldur            w2, [x1, #0xf]
    // 0xeb40f8: DecompressPointer r2
    //     0xeb40f8: add             x2, x2, HEAP, lsl #32
    // 0xeb40fc: r16 = Sentinel
    //     0xeb40fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb4100: cmp             w2, w16
    // 0xeb4104: b.eq            #0xeb41a0
    // 0xeb4108: r3 = LoadInt32Instr(r0)
    //     0xeb4108: sbfx            x3, x0, #1, #0x1f
    //     0xeb410c: tbz             w0, #0, #0xeb4114
    //     0xeb4110: ldur            x3, [x0, #7]
    // 0xeb4114: r0 = LoadInt32Instr(r2)
    //     0xeb4114: sbfx            x0, x2, #1, #0x1f
    //     0xeb4118: tbz             w2, #0, #0xeb4120
    //     0xeb411c: ldur            x0, [x2, #7]
    // 0xeb4120: cmp             x3, x0
    // 0xeb4124: b.ne            #0xeb4168
    // 0xeb4128: LoadField: r0 = r1->field_3f
    //     0xeb4128: ldur            w0, [x1, #0x3f]
    // 0xeb412c: DecompressPointer r0
    //     0xeb412c: add             x0, x0, HEAP, lsl #32
    // 0xeb4130: r16 = Sentinel
    //     0xeb4130: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb4134: cmp             w0, w16
    // 0xeb4138: b.eq            #0xeb41ac
    // 0xeb413c: LoadField: r2 = r1->field_27
    //     0xeb413c: ldur            w2, [x1, #0x27]
    // 0xeb4140: DecompressPointer r2
    //     0xeb4140: add             x2, x2, HEAP, lsl #32
    // 0xeb4144: cmp             w2, NULL
    // 0xeb4148: b.eq            #0xeb41b8
    // 0xeb414c: mov             x1, x0
    // 0xeb4150: r0 = constantTimeAreEqual()
    //     0xeb4150: bl              #0xeb41c8  ; [package:pointycastle/src/utils.dart] ::constantTimeAreEqual
    // 0xeb4154: tbnz            w0, #4, #0xeb4174
    // 0xeb4158: r0 = Null
    //     0xeb4158: mov             x0, NULL
    // 0xeb415c: LeaveFrame
    //     0xeb415c: mov             SP, fp
    //     0xeb4160: ldp             fp, lr, [SP], #0x10
    // 0xeb4164: ret
    //     0xeb4164: ret             
    // 0xeb4168: r0 = InvalidCipherTextException()
    //     0xeb4168: bl              #0xeb41bc  ; AllocateInvalidCipherTextExceptionStub -> InvalidCipherTextException (size=0x8)
    // 0xeb416c: r0 = Throw()
    //     0xeb416c: bl              #0xec04b8  ; ThrowStub
    // 0xeb4170: brk             #0
    // 0xeb4174: r0 = InvalidCipherTextException()
    //     0xeb4174: bl              #0xeb41bc  ; AllocateInvalidCipherTextExceptionStub -> InvalidCipherTextException (size=0x8)
    // 0xeb4178: r0 = Throw()
    //     0xeb4178: bl              #0xec04b8  ; ThrowStub
    // 0xeb417c: brk             #0
    // 0xeb4180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb4180: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb4184: b               #0xeb40b8
    // 0xeb4188: r9 = _forEncryption
    //     0xeb4188: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a48] Field <BaseAEADBlockCipher._forEncryption@2660101045>: late (offset: 0xc)
    //     0xeb418c: ldr             x9, [x9, #0xa48]
    // 0xeb4190: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4190: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4194: r9 = _lastMacSizeBytesOff
    //     0xeb4194: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a50] Field <BaseAEADBlockCipher._lastMacSizeBytesOff@2660101045>: late (offset: 0x2c)
    //     0xeb4198: ldr             x9, [x9, #0xa50]
    // 0xeb419c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb419c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb41a0: r9 = _macSize
    //     0xeb41a0: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a28] Field <BaseAEADBlockCipher._macSize@2660101045>: late (offset: 0x10)
    //     0xeb41a4: ldr             x9, [x9, #0xa28]
    // 0xeb41a8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb41a8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb41ac: r9 = _x
    //     0xeb41ac: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a10] Field <GCMBlockCipher._x@914399014>: late (offset: 0x40)
    //     0xeb41b0: ldr             x9, [x9, #0xa10]
    // 0xeb41b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb41b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb41b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb41b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ remainingInput(/* No info */) {
    // ** addr: 0xeb42ec, size: 0x70
    // 0xeb42ec: EnterFrame
    //     0xeb42ec: stp             fp, lr, [SP, #-0x10]!
    //     0xeb42f0: mov             fp, SP
    // 0xeb42f4: AllocStack(0x20)
    //     0xeb42f4: sub             SP, SP, #0x20
    // 0xeb42f8: SetupParameters(BaseAEADBlockCipher this /* r1 => r1, fp-0x10 */)
    //     0xeb42f8: stur            x1, [fp, #-0x10]
    // 0xeb42fc: CheckStackOverflow
    //     0xeb42fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb4300: cmp             SP, x16
    //     0xeb4304: b.ls            #0xeb4350
    // 0xeb4308: LoadField: r0 = r1->field_1f
    //     0xeb4308: ldur            w0, [x1, #0x1f]
    // 0xeb430c: DecompressPointer r0
    //     0xeb430c: add             x0, x0, HEAP, lsl #32
    // 0xeb4310: stur            x0, [fp, #-8]
    // 0xeb4314: cmp             w0, NULL
    // 0xeb4318: b.eq            #0xeb4358
    // 0xeb431c: r0 = _ByteBuffer()
    //     0xeb431c: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0xeb4320: mov             x1, x0
    // 0xeb4324: ldur            x0, [fp, #-8]
    // 0xeb4328: StoreField: r1->field_7 = r0
    //     0xeb4328: stur            w0, [x1, #7]
    // 0xeb432c: ldur            x0, [fp, #-0x10]
    // 0xeb4330: LoadField: r2 = r0->field_23
    //     0xeb4330: ldur            w2, [x0, #0x23]
    // 0xeb4334: DecompressPointer r2
    //     0xeb4334: add             x2, x2, HEAP, lsl #32
    // 0xeb4338: stp             x2, xzr, [SP]
    // 0xeb433c: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0xeb433c: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0xeb4340: r0 = asUint8List()
    //     0xeb4340: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0xeb4344: LeaveFrame
    //     0xeb4344: mov             SP, fp
    //     0xeb4348: ldp             fp, lr, [SP], #0x10
    // 0xeb434c: ret
    //     0xeb434c: ret             
    // 0xeb4350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb4350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb4354: b               #0xeb4308
    // 0xeb4358: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb4358: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ processBytes(/* No info */) {
    // ** addr: 0xeb435c, size: 0x630
    // 0xeb435c: EnterFrame
    //     0xeb435c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb4360: mov             fp, SP
    // 0xeb4364: AllocStack(0x58)
    //     0xeb4364: sub             SP, SP, #0x58
    // 0xeb4368: SetupParameters(BaseAEADBlockCipher this /* r1 => r11, fp-0x18 */, dynamic _ /* r2 => r10, fp-0x20 */, dynamic _ /* r5 => r8, fp-0x28 */, dynamic _ /* r6 => r4, fp-0x30 */)
    //     0xeb4368: mov             x11, x1
    //     0xeb436c: mov             x10, x2
    //     0xeb4370: mov             x8, x5
    //     0xeb4374: mov             x4, x6
    //     0xeb4378: stur            x1, [fp, #-0x18]
    //     0xeb437c: stur            x2, [fp, #-0x20]
    //     0xeb4380: stur            x5, [fp, #-0x28]
    //     0xeb4384: stur            x6, [fp, #-0x30]
    // 0xeb4388: CheckStackOverflow
    //     0xeb4388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb438c: cmp             SP, x16
    //     0xeb4390: b.ls            #0xeb4954
    // 0xeb4394: cbnz            x8, #0xeb43a8
    // 0xeb4398: r0 = 0
    //     0xeb4398: movz            x0, #0
    // 0xeb439c: LeaveFrame
    //     0xeb439c: mov             SP, fp
    //     0xeb43a0: ldp             fp, lr, [SP], #0x10
    // 0xeb43a4: ret
    //     0xeb43a4: ret             
    // 0xeb43a8: LoadField: r0 = r11->field_b
    //     0xeb43a8: ldur            w0, [x11, #0xb]
    // 0xeb43ac: DecompressPointer r0
    //     0xeb43ac: add             x0, x0, HEAP, lsl #32
    // 0xeb43b0: r16 = Sentinel
    //     0xeb43b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb43b4: cmp             w0, w16
    // 0xeb43b8: b.eq            #0xeb495c
    // 0xeb43bc: tbnz            w0, #4, #0xeb43e8
    // 0xeb43c0: mov             x1, x11
    // 0xeb43c4: mov             x2, x10
    // 0xeb43c8: mov             x5, x8
    // 0xeb43cc: mov             x6, x4
    // 0xeb43d0: r3 = 0
    //     0xeb43d0: movz            x3, #0
    // 0xeb43d4: r7 = 0
    //     0xeb43d4: movz            x7, #0
    // 0xeb43d8: r0 = _processCipherBytes()
    //     0xeb43d8: bl              #0xeb498c  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::_processCipherBytes
    // 0xeb43dc: LeaveFrame
    //     0xeb43dc: mov             SP, fp
    //     0xeb43e0: ldp             fp, lr, [SP], #0x10
    // 0xeb43e4: ret
    //     0xeb43e4: ret             
    // 0xeb43e8: LoadField: r2 = r11->field_2b
    //     0xeb43e8: ldur            w2, [x11, #0x2b]
    // 0xeb43ec: DecompressPointer r2
    //     0xeb43ec: add             x2, x2, HEAP, lsl #32
    // 0xeb43f0: r16 = Sentinel
    //     0xeb43f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb43f4: cmp             w2, w16
    // 0xeb43f8: b.eq            #0xeb4968
    // 0xeb43fc: r3 = LoadInt32Instr(r2)
    //     0xeb43fc: sbfx            x3, x2, #1, #0x1f
    //     0xeb4400: tbz             w2, #0, #0xeb4408
    //     0xeb4404: ldur            x3, [x2, #7]
    // 0xeb4408: add             x0, x3, x8
    // 0xeb440c: LoadField: r1 = r11->field_f
    //     0xeb440c: ldur            w1, [x11, #0xf]
    // 0xeb4410: DecompressPointer r1
    //     0xeb4410: add             x1, x1, HEAP, lsl #32
    // 0xeb4414: r16 = Sentinel
    //     0xeb4414: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb4418: cmp             w1, w16
    // 0xeb441c: b.eq            #0xeb4974
    // 0xeb4420: r5 = LoadInt32Instr(r1)
    //     0xeb4420: sbfx            x5, x1, #1, #0x1f
    //     0xeb4424: tbz             w1, #0, #0xeb442c
    //     0xeb4428: ldur            x5, [x1, #7]
    // 0xeb442c: sub             x9, x0, x5
    // 0xeb4430: stur            x9, [fp, #-0x10]
    // 0xeb4434: cmp             x9, #0
    // 0xeb4438: b.le            #0xeb47ac
    // 0xeb443c: cmp             x3, #0
    // 0xeb4440: b.le            #0xeb47a0
    // 0xeb4444: cmp             x3, x9
    // 0xeb4448: b.le            #0xeb446c
    // 0xeb444c: r0 = BoxInt64Instr(r9)
    //     0xeb444c: sbfiz           x0, x9, #1, #0x1f
    //     0xeb4450: cmp             x9, x0, asr #1
    //     0xeb4454: b.eq            #0xeb4460
    //     0xeb4458: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb445c: stur            x9, [x0, #7]
    // 0xeb4460: mov             x12, x0
    // 0xeb4464: d0 = 0.000000
    //     0xeb4464: eor             v0.16b, v0.16b, v0.16b
    // 0xeb4468: b               #0xeb4544
    // 0xeb446c: cmp             x3, x9
    // 0xeb4470: b.ge            #0xeb4480
    // 0xeb4474: mov             x12, x2
    // 0xeb4478: d0 = 0.000000
    //     0xeb4478: eor             v0.16b, v0.16b, v0.16b
    // 0xeb447c: b               #0xeb4544
    // 0xeb4480: r0 = BoxInt64Instr(r9)
    //     0xeb4480: sbfiz           x0, x9, #1, #0x1f
    //     0xeb4484: cmp             x9, x0, asr #1
    //     0xeb4488: b.eq            #0xeb4494
    //     0xeb448c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb4490: stur            x9, [x0, #7]
    // 0xeb4494: r1 = 60
    //     0xeb4494: movz            x1, #0x3c
    // 0xeb4498: branchIfSmi(r0, 0xeb44a4)
    //     0xeb4498: tbz             w0, #0, #0xeb44a4
    // 0xeb449c: r1 = LoadClassIdInstr(r0)
    //     0xeb449c: ldur            x1, [x0, #-1]
    //     0xeb44a0: ubfx            x1, x1, #0xc, #0x14
    // 0xeb44a4: cmp             x1, #0x3e
    // 0xeb44a8: b.ne            #0xeb453c
    // 0xeb44ac: r1 = 60
    //     0xeb44ac: movz            x1, #0x3c
    // 0xeb44b0: branchIfSmi(r2, 0xeb44bc)
    //     0xeb44b0: tbz             w2, #0, #0xeb44bc
    // 0xeb44b4: r1 = LoadClassIdInstr(r2)
    //     0xeb44b4: ldur            x1, [x2, #-1]
    //     0xeb44b8: ubfx            x1, x1, #0xc, #0x14
    // 0xeb44bc: cmp             x1, #0x3e
    // 0xeb44c0: b.ne            #0xeb44fc
    // 0xeb44c4: d0 = 0.000000
    //     0xeb44c4: eor             v0.16b, v0.16b, v0.16b
    // 0xeb44c8: scvtf           d1, x3
    // 0xeb44cc: fcmp            d1, d0
    // 0xeb44d0: b.ne            #0xeb4500
    // 0xeb44d4: add             x0, x3, x9
    // 0xeb44d8: mul             x1, x0, x3
    // 0xeb44dc: mul             x5, x1, x9
    // 0xeb44e0: r0 = BoxInt64Instr(r5)
    //     0xeb44e0: sbfiz           x0, x5, #1, #0x1f
    //     0xeb44e4: cmp             x5, x0, asr #1
    //     0xeb44e8: b.eq            #0xeb44f4
    //     0xeb44ec: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xeb44f0: stur            x5, [x0, #7]
    // 0xeb44f4: mov             x12, x0
    // 0xeb44f8: b               #0xeb4544
    // 0xeb44fc: d0 = 0.000000
    //     0xeb44fc: eor             v0.16b, v0.16b, v0.16b
    // 0xeb4500: cbnz            x3, #0xeb4520
    // 0xeb4504: LoadField: d1 = r0->field_7
    //     0xeb4504: ldur            d1, [x0, #7]
    // 0xeb4508: fcmp            d1, #0.0
    // 0xeb450c: b.vs            #0xeb4520
    // 0xeb4510: b.ne            #0xeb451c
    // 0xeb4514: r1 = 0.000000
    //     0xeb4514: fmov            x1, d1
    // 0xeb4518: cmp             x1, #0
    // 0xeb451c: b.lt            #0xeb452c
    // 0xeb4520: LoadField: d1 = r0->field_7
    //     0xeb4520: ldur            d1, [x0, #7]
    // 0xeb4524: fcmp            d1, d1
    // 0xeb4528: b.vc            #0xeb4534
    // 0xeb452c: mov             x12, x0
    // 0xeb4530: b               #0xeb4544
    // 0xeb4534: mov             x12, x2
    // 0xeb4538: b               #0xeb4544
    // 0xeb453c: d0 = 0.000000
    //     0xeb453c: eor             v0.16b, v0.16b, v0.16b
    // 0xeb4540: mov             x12, x2
    // 0xeb4544: stur            x12, [fp, #-8]
    // 0xeb4548: LoadField: r5 = r11->field_27
    //     0xeb4548: ldur            w5, [x11, #0x27]
    // 0xeb454c: DecompressPointer r5
    //     0xeb454c: add             x5, x5, HEAP, lsl #32
    // 0xeb4550: cmp             w5, NULL
    // 0xeb4554: b.eq            #0xeb4980
    // 0xeb4558: cmp             x3, x9
    // 0xeb455c: b.le            #0xeb4578
    // 0xeb4560: r0 = BoxInt64Instr(r9)
    //     0xeb4560: sbfiz           x0, x9, #1, #0x1f
    //     0xeb4564: cmp             x9, x0, asr #1
    //     0xeb4568: b.eq            #0xeb4574
    //     0xeb456c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb4570: stur            x9, [x0, #7]
    // 0xeb4574: b               #0xeb4634
    // 0xeb4578: cmp             x3, x9
    // 0xeb457c: b.ge            #0xeb4588
    // 0xeb4580: mov             x0, x2
    // 0xeb4584: b               #0xeb4634
    // 0xeb4588: r0 = BoxInt64Instr(r9)
    //     0xeb4588: sbfiz           x0, x9, #1, #0x1f
    //     0xeb458c: cmp             x9, x0, asr #1
    //     0xeb4590: b.eq            #0xeb459c
    //     0xeb4594: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xeb4598: stur            x9, [x0, #7]
    // 0xeb459c: r1 = 60
    //     0xeb459c: movz            x1, #0x3c
    // 0xeb45a0: branchIfSmi(r0, 0xeb45ac)
    //     0xeb45a0: tbz             w0, #0, #0xeb45ac
    // 0xeb45a4: r1 = LoadClassIdInstr(r0)
    //     0xeb45a4: ldur            x1, [x0, #-1]
    //     0xeb45a8: ubfx            x1, x1, #0xc, #0x14
    // 0xeb45ac: cmp             x1, #0x3e
    // 0xeb45b0: b.ne            #0xeb4630
    // 0xeb45b4: r1 = 60
    //     0xeb45b4: movz            x1, #0x3c
    // 0xeb45b8: branchIfSmi(r2, 0xeb45c4)
    //     0xeb45b8: tbz             w2, #0, #0xeb45c4
    // 0xeb45bc: r1 = LoadClassIdInstr(r2)
    //     0xeb45bc: ldur            x1, [x2, #-1]
    //     0xeb45c0: ubfx            x1, x1, #0xc, #0x14
    // 0xeb45c4: cmp             x1, #0x3e
    // 0xeb45c8: b.ne            #0xeb45fc
    // 0xeb45cc: scvtf           d1, x3
    // 0xeb45d0: fcmp            d1, d0
    // 0xeb45d4: b.ne            #0xeb45fc
    // 0xeb45d8: add             x0, x3, x9
    // 0xeb45dc: mul             x1, x0, x3
    // 0xeb45e0: mul             x2, x1, x9
    // 0xeb45e4: r0 = BoxInt64Instr(r2)
    //     0xeb45e4: sbfiz           x0, x2, #1, #0x1f
    //     0xeb45e8: cmp             x2, x0, asr #1
    //     0xeb45ec: b.eq            #0xeb45f8
    //     0xeb45f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb45f4: stur            x2, [x0, #7]
    // 0xeb45f8: b               #0xeb4634
    // 0xeb45fc: cbnz            x3, #0xeb461c
    // 0xeb4600: LoadField: d0 = r0->field_7
    //     0xeb4600: ldur            d0, [x0, #7]
    // 0xeb4604: fcmp            d0, #0.0
    // 0xeb4608: b.vs            #0xeb461c
    // 0xeb460c: b.ne            #0xeb4618
    // 0xeb4610: r1 = 0.000000
    //     0xeb4610: fmov            x1, d0
    // 0xeb4614: cmp             x1, #0
    // 0xeb4618: b.lt            #0xeb4634
    // 0xeb461c: LoadField: d0 = r0->field_7
    //     0xeb461c: ldur            d0, [x0, #7]
    // 0xeb4620: fcmp            d0, d0
    // 0xeb4624: b.vs            #0xeb4634
    // 0xeb4628: mov             x0, x2
    // 0xeb462c: b               #0xeb4634
    // 0xeb4630: mov             x0, x2
    // 0xeb4634: r1 = LoadInt32Instr(r0)
    //     0xeb4634: sbfx            x1, x0, #1, #0x1f
    //     0xeb4638: tbz             w0, #0, #0xeb4640
    //     0xeb463c: ldur            x1, [x0, #7]
    // 0xeb4640: mov             x2, x5
    // 0xeb4644: mov             x5, x1
    // 0xeb4648: mov             x1, x11
    // 0xeb464c: mov             x6, x4
    // 0xeb4650: r3 = 0
    //     0xeb4650: movz            x3, #0
    // 0xeb4654: r7 = 0
    //     0xeb4654: movz            x7, #0
    // 0xeb4658: r0 = _processCipherBytes()
    //     0xeb4658: bl              #0xeb498c  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::_processCipherBytes
    // 0xeb465c: mov             x3, x0
    // 0xeb4660: ldur            x0, [fp, #-8]
    // 0xeb4664: stur            x3, [fp, #-0x50]
    // 0xeb4668: r4 = LoadInt32Instr(r0)
    //     0xeb4668: sbfx            x4, x0, #1, #0x1f
    //     0xeb466c: tbz             w0, #0, #0xeb4674
    //     0xeb4670: ldur            x4, [x0, #7]
    // 0xeb4674: ldur            x0, [fp, #-0x10]
    // 0xeb4678: stur            x4, [fp, #-0x48]
    // 0xeb467c: sub             x5, x0, x4
    // 0xeb4680: ldur            x0, [fp, #-0x18]
    // 0xeb4684: stur            x5, [fp, #-0x40]
    // 0xeb4688: LoadField: r6 = r0->field_27
    //     0xeb4688: ldur            w6, [x0, #0x27]
    // 0xeb468c: DecompressPointer r6
    //     0xeb468c: add             x6, x6, HEAP, lsl #32
    // 0xeb4690: stur            x6, [fp, #-8]
    // 0xeb4694: cmp             w6, NULL
    // 0xeb4698: b.eq            #0xeb4984
    // 0xeb469c: LoadField: r1 = r0->field_f
    //     0xeb469c: ldur            w1, [x0, #0xf]
    // 0xeb46a0: DecompressPointer r1
    //     0xeb46a0: add             x1, x1, HEAP, lsl #32
    // 0xeb46a4: r2 = LoadInt32Instr(r1)
    //     0xeb46a4: sbfx            x2, x1, #1, #0x1f
    //     0xeb46a8: tbz             w1, #0, #0xeb46b0
    //     0xeb46ac: ldur            x2, [x1, #7]
    // 0xeb46b0: sub             x7, x2, x4
    // 0xeb46b4: mov             x1, x6
    // 0xeb46b8: mov             x2, x4
    // 0xeb46bc: stur            x7, [fp, #-0x38]
    // 0xeb46c0: r0 = skip()
    //     0xeb46c0: bl              #0xa5a124  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::skip
    // 0xeb46c4: mov             x5, x0
    // 0xeb46c8: ldur            x4, [fp, #-0x38]
    // 0xeb46cc: stur            x5, [fp, #-0x58]
    // 0xeb46d0: tbz             x4, #0x3f, #0xeb46dc
    // 0xeb46d4: ldur            x6, [fp, #-8]
    // 0xeb46d8: b               #0xeb46f0
    // 0xeb46dc: ldur            x6, [fp, #-8]
    // 0xeb46e0: LoadField: r0 = r6->field_13
    //     0xeb46e0: ldur            w0, [x6, #0x13]
    // 0xeb46e4: r1 = LoadInt32Instr(r0)
    //     0xeb46e4: sbfx            x1, x0, #1, #0x1f
    // 0xeb46e8: cmp             x4, x1
    // 0xeb46ec: b.le            #0xeb471c
    // 0xeb46f0: LoadField: r2 = r6->field_13
    //     0xeb46f0: ldur            w2, [x6, #0x13]
    // 0xeb46f4: r0 = BoxInt64Instr(r4)
    //     0xeb46f4: sbfiz           x0, x4, #1, #0x1f
    //     0xeb46f8: cmp             x4, x0, asr #1
    //     0xeb46fc: b.eq            #0xeb4708
    //     0xeb4700: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb4704: stur            x4, [x0, #7]
    // 0xeb4708: r3 = LoadInt32Instr(r2)
    //     0xeb4708: sbfx            x3, x2, #1, #0x1f
    // 0xeb470c: mov             x2, x0
    // 0xeb4710: r1 = 0
    //     0xeb4710: movz            x1, #0
    // 0xeb4714: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeb4714: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeb4718: r0 = checkValidRange()
    //     0xeb4718: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeb471c: ldur            x4, [fp, #-0x18]
    // 0xeb4720: ldur            x0, [fp, #-0x48]
    // 0xeb4724: ldur            x1, [fp, #-8]
    // 0xeb4728: ldur            x3, [fp, #-0x38]
    // 0xeb472c: ldur            x5, [fp, #-0x58]
    // 0xeb4730: r2 = 0
    //     0xeb4730: movz            x2, #0
    // 0xeb4734: r6 = 0
    //     0xeb4734: movz            x6, #0
    // 0xeb4738: r0 = _slowSetRange()
    //     0xeb4738: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xeb473c: ldur            x4, [fp, #-0x18]
    // 0xeb4740: LoadField: r0 = r4->field_2b
    //     0xeb4740: ldur            w0, [x4, #0x2b]
    // 0xeb4744: DecompressPointer r0
    //     0xeb4744: add             x0, x0, HEAP, lsl #32
    // 0xeb4748: r1 = LoadInt32Instr(r0)
    //     0xeb4748: sbfx            x1, x0, #1, #0x1f
    //     0xeb474c: tbz             w0, #0, #0xeb4754
    //     0xeb4750: ldur            x1, [x0, #7]
    // 0xeb4754: ldur            x0, [fp, #-0x48]
    // 0xeb4758: sub             x2, x1, x0
    // 0xeb475c: r0 = BoxInt64Instr(r2)
    //     0xeb475c: sbfiz           x0, x2, #1, #0x1f
    //     0xeb4760: cmp             x2, x0, asr #1
    //     0xeb4764: b.eq            #0xeb4770
    //     0xeb4768: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb476c: stur            x2, [x0, #7]
    // 0xeb4770: StoreField: r4->field_2b = r0
    //     0xeb4770: stur            w0, [x4, #0x2b]
    //     0xeb4774: tbz             w0, #0, #0xeb4790
    //     0xeb4778: ldurb           w16, [x4, #-1]
    //     0xeb477c: ldurb           w17, [x0, #-1]
    //     0xeb4780: and             x16, x17, x16, lsr #2
    //     0xeb4784: tst             x16, HEAP, lsr #32
    //     0xeb4788: b.eq            #0xeb4790
    //     0xeb478c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xeb4790: ldur            x7, [fp, #-0x50]
    // 0xeb4794: ldur            x8, [fp, #-0x40]
    // 0xeb4798: ldur            x0, [fp, #-0x50]
    // 0xeb479c: b               #0xeb47c0
    // 0xeb47a0: mov             x4, x11
    // 0xeb47a4: mov             x0, x9
    // 0xeb47a8: b               #0xeb47b4
    // 0xeb47ac: mov             x4, x11
    // 0xeb47b0: mov             x0, x9
    // 0xeb47b4: mov             x8, x0
    // 0xeb47b8: r7 = 0
    //     0xeb47b8: movz            x7, #0
    // 0xeb47bc: r0 = 0
    //     0xeb47bc: movz            x0, #0
    // 0xeb47c0: stur            x8, [fp, #-0x10]
    // 0xeb47c4: stur            x0, [fp, #-0x38]
    // 0xeb47c8: cmp             x8, #0
    // 0xeb47cc: b.le            #0xeb47fc
    // 0xeb47d0: mov             x1, x4
    // 0xeb47d4: ldur            x2, [fp, #-0x20]
    // 0xeb47d8: mov             x5, x8
    // 0xeb47dc: ldur            x6, [fp, #-0x30]
    // 0xeb47e0: r3 = 0
    //     0xeb47e0: movz            x3, #0
    // 0xeb47e4: r0 = _processCipherBytes()
    //     0xeb47e4: bl              #0xeb498c  ; [package:pointycastle/src/impl/base_aead_block_cipher.dart] BaseAEADBlockCipher::_processCipherBytes
    // 0xeb47e8: mov             x1, x0
    // 0xeb47ec: ldur            x0, [fp, #-0x38]
    // 0xeb47f0: add             x2, x0, x1
    // 0xeb47f4: mov             x5, x2
    // 0xeb47f8: b               #0xeb4800
    // 0xeb47fc: mov             x5, x0
    // 0xeb4800: ldur            x0, [fp, #-0x18]
    // 0xeb4804: ldur            x4, [fp, #-0x28]
    // 0xeb4808: ldur            x3, [fp, #-0x10]
    // 0xeb480c: stur            x5, [fp, #-0x48]
    // 0xeb4810: LoadField: r6 = r0->field_27
    //     0xeb4810: ldur            w6, [x0, #0x27]
    // 0xeb4814: DecompressPointer r6
    //     0xeb4814: add             x6, x6, HEAP, lsl #32
    // 0xeb4818: stur            x6, [fp, #-8]
    // 0xeb481c: cmp             w6, NULL
    // 0xeb4820: b.eq            #0xeb4988
    // 0xeb4824: LoadField: r1 = r0->field_2b
    //     0xeb4824: ldur            w1, [x0, #0x2b]
    // 0xeb4828: DecompressPointer r1
    //     0xeb4828: add             x1, x1, HEAP, lsl #32
    // 0xeb482c: r7 = LoadInt32Instr(r1)
    //     0xeb482c: sbfx            x7, x1, #1, #0x1f
    //     0xeb4830: tbz             w1, #0, #0xeb4838
    //     0xeb4834: ldur            x7, [x1, #7]
    // 0xeb4838: stur            x7, [fp, #-0x40]
    // 0xeb483c: add             x1, x7, x4
    // 0xeb4840: sub             x8, x1, x3
    // 0xeb4844: ldur            x1, [fp, #-0x20]
    // 0xeb4848: mov             x2, x3
    // 0xeb484c: stur            x8, [fp, #-0x38]
    // 0xeb4850: r0 = skip()
    //     0xeb4850: bl              #0xa5a124  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::skip
    // 0xeb4854: mov             x5, x0
    // 0xeb4858: ldur            x4, [fp, #-0x40]
    // 0xeb485c: stur            x5, [fp, #-0x20]
    // 0xeb4860: tbz             x4, #0x3f, #0xeb4870
    // 0xeb4864: ldur            x7, [fp, #-8]
    // 0xeb4868: ldur            x6, [fp, #-0x38]
    // 0xeb486c: b               #0xeb4898
    // 0xeb4870: ldur            x6, [fp, #-0x38]
    // 0xeb4874: cmp             x4, x6
    // 0xeb4878: b.le            #0xeb4884
    // 0xeb487c: ldur            x7, [fp, #-8]
    // 0xeb4880: b               #0xeb4898
    // 0xeb4884: ldur            x7, [fp, #-8]
    // 0xeb4888: LoadField: r0 = r7->field_13
    //     0xeb4888: ldur            w0, [x7, #0x13]
    // 0xeb488c: r1 = LoadInt32Instr(r0)
    //     0xeb488c: sbfx            x1, x0, #1, #0x1f
    // 0xeb4890: cmp             x6, x1
    // 0xeb4894: b.le            #0xeb48c4
    // 0xeb4898: LoadField: r2 = r7->field_13
    //     0xeb4898: ldur            w2, [x7, #0x13]
    // 0xeb489c: r0 = BoxInt64Instr(r6)
    //     0xeb489c: sbfiz           x0, x6, #1, #0x1f
    //     0xeb48a0: cmp             x6, x0, asr #1
    //     0xeb48a4: b.eq            #0xeb48b0
    //     0xeb48a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb48ac: stur            x6, [x0, #7]
    // 0xeb48b0: r3 = LoadInt32Instr(r2)
    //     0xeb48b0: sbfx            x3, x2, #1, #0x1f
    // 0xeb48b4: mov             x1, x4
    // 0xeb48b8: mov             x2, x0
    // 0xeb48bc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeb48bc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeb48c0: r0 = checkValidRange()
    //     0xeb48c0: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeb48c4: ldur            x0, [fp, #-0x18]
    // 0xeb48c8: ldur            x7, [fp, #-0x28]
    // 0xeb48cc: ldur            x4, [fp, #-0x10]
    // 0xeb48d0: ldur            x1, [fp, #-8]
    // 0xeb48d4: ldur            x2, [fp, #-0x40]
    // 0xeb48d8: ldur            x3, [fp, #-0x38]
    // 0xeb48dc: ldur            x5, [fp, #-0x20]
    // 0xeb48e0: r6 = 0
    //     0xeb48e0: movz            x6, #0
    // 0xeb48e4: r0 = _slowSetRange()
    //     0xeb48e4: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xeb48e8: ldur            x2, [fp, #-0x18]
    // 0xeb48ec: LoadField: r3 = r2->field_2b
    //     0xeb48ec: ldur            w3, [x2, #0x2b]
    // 0xeb48f0: DecompressPointer r3
    //     0xeb48f0: add             x3, x3, HEAP, lsl #32
    // 0xeb48f4: ldur            x5, [fp, #-0x28]
    // 0xeb48f8: ldur            x4, [fp, #-0x10]
    // 0xeb48fc: sub             x6, x5, x4
    // 0xeb4900: r4 = LoadInt32Instr(r3)
    //     0xeb4900: sbfx            x4, x3, #1, #0x1f
    //     0xeb4904: tbz             w3, #0, #0xeb490c
    //     0xeb4908: ldur            x4, [x3, #7]
    // 0xeb490c: add             x3, x4, x6
    // 0xeb4910: r0 = BoxInt64Instr(r3)
    //     0xeb4910: sbfiz           x0, x3, #1, #0x1f
    //     0xeb4914: cmp             x3, x0, asr #1
    //     0xeb4918: b.eq            #0xeb4924
    //     0xeb491c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb4920: stur            x3, [x0, #7]
    // 0xeb4924: StoreField: r2->field_2b = r0
    //     0xeb4924: stur            w0, [x2, #0x2b]
    //     0xeb4928: tbz             w0, #0, #0xeb4944
    //     0xeb492c: ldurb           w16, [x2, #-1]
    //     0xeb4930: ldurb           w17, [x0, #-1]
    //     0xeb4934: and             x16, x17, x16, lsr #2
    //     0xeb4938: tst             x16, HEAP, lsr #32
    //     0xeb493c: b.eq            #0xeb4944
    //     0xeb4940: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xeb4944: ldur            x0, [fp, #-0x48]
    // 0xeb4948: LeaveFrame
    //     0xeb4948: mov             SP, fp
    //     0xeb494c: ldp             fp, lr, [SP], #0x10
    // 0xeb4950: ret
    //     0xeb4950: ret             
    // 0xeb4954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb4954: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb4958: b               #0xeb4394
    // 0xeb495c: r9 = _forEncryption
    //     0xeb495c: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a48] Field <BaseAEADBlockCipher._forEncryption@2660101045>: late (offset: 0xc)
    //     0xeb4960: ldr             x9, [x9, #0xa48]
    // 0xeb4964: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4964: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4968: r9 = _lastMacSizeBytesOff
    //     0xeb4968: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a50] Field <BaseAEADBlockCipher._lastMacSizeBytesOff@2660101045>: late (offset: 0x2c)
    //     0xeb496c: ldr             x9, [x9, #0xa50]
    // 0xeb4970: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4970: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4974: r9 = _macSize
    //     0xeb4974: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a28] Field <BaseAEADBlockCipher._macSize@2660101045>: late (offset: 0x10)
    //     0xeb4978: ldr             x9, [x9, #0xa28]
    // 0xeb497c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb497c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4980: r0 = NullCastErrorSharedWithFPURegs()
    //     0xeb4980: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xeb4984: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb4984: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeb4988: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb4988: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _processCipherBytes(/* No info */) {
    // ** addr: 0xeb498c, size: 0x4f8
    // 0xeb498c: EnterFrame
    //     0xeb498c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb4990: mov             fp, SP
    // 0xeb4994: AllocStack(0x68)
    //     0xeb4994: sub             SP, SP, #0x68
    // 0xeb4998: SetupParameters(BaseAEADBlockCipher this /* r1 => r7, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */, dynamic _ /* r6 => r5, fp-0x28 */, dynamic _ /* r7 => r6, fp-0x30 */)
    //     0xeb4998: mov             x4, x2
    //     0xeb499c: stur            x2, [fp, #-0x18]
    //     0xeb49a0: mov             x2, x5
    //     0xeb49a4: stur            x5, [fp, #-0x20]
    //     0xeb49a8: mov             x5, x6
    //     0xeb49ac: stur            x6, [fp, #-0x28]
    //     0xeb49b0: mov             x6, x7
    //     0xeb49b4: stur            x7, [fp, #-0x30]
    //     0xeb49b8: mov             x7, x1
    //     0xeb49bc: stur            x1, [fp, #-0x10]
    // 0xeb49c0: CheckStackOverflow
    //     0xeb49c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb49c4: cmp             SP, x16
    //     0xeb49c8: b.ls            #0xeb4e5c
    // 0xeb49cc: cbnz            x2, #0xeb49e0
    // 0xeb49d0: r0 = 0
    //     0xeb49d0: movz            x0, #0
    // 0xeb49d4: LeaveFrame
    //     0xeb49d4: mov             SP, fp
    //     0xeb49d8: ldp             fp, lr, [SP], #0x10
    // 0xeb49dc: ret
    //     0xeb49dc: ret             
    // 0xeb49e0: LoadField: r0 = r7->field_23
    //     0xeb49e0: ldur            w0, [x7, #0x23]
    // 0xeb49e4: DecompressPointer r0
    //     0xeb49e4: add             x0, x0, HEAP, lsl #32
    // 0xeb49e8: cbz             w0, #0xeb4c48
    // 0xeb49ec: LoadField: r3 = r7->field_7
    //     0xeb49ec: ldur            w3, [x7, #7]
    // 0xeb49f0: DecompressPointer r3
    //     0xeb49f0: add             x3, x3, HEAP, lsl #32
    // 0xeb49f4: stur            x3, [fp, #-8]
    // 0xeb49f8: r0 = LoadClassIdInstr(r3)
    //     0xeb49f8: ldur            x0, [x3, #-1]
    //     0xeb49fc: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4a00: mov             x1, x3
    // 0xeb4a04: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4a04: sub             lr, x0, #1, lsl #12
    //     0xeb4a08: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4a0c: blr             lr
    // 0xeb4a10: ldur            x2, [fp, #-0x10]
    // 0xeb4a14: LoadField: r1 = r2->field_23
    //     0xeb4a14: ldur            w1, [x2, #0x23]
    // 0xeb4a18: DecompressPointer r1
    //     0xeb4a18: add             x1, x1, HEAP, lsl #32
    // 0xeb4a1c: cmp             w1, NULL
    // 0xeb4a20: b.eq            #0xeb4e64
    // 0xeb4a24: r3 = LoadInt32Instr(r1)
    //     0xeb4a24: sbfx            x3, x1, #1, #0x1f
    //     0xeb4a28: tbz             w1, #0, #0xeb4a30
    //     0xeb4a2c: ldur            x3, [x1, #7]
    // 0xeb4a30: ldur            x4, [fp, #-0x20]
    // 0xeb4a34: add             x1, x3, x4
    // 0xeb4a38: cmp             x0, x1
    // 0xeb4a3c: b.ge            #0xeb4a64
    // 0xeb4a40: ldur            x3, [fp, #-8]
    // 0xeb4a44: r0 = LoadClassIdInstr(r3)
    //     0xeb4a44: ldur            x0, [x3, #-1]
    //     0xeb4a48: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4a4c: mov             x1, x3
    // 0xeb4a50: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4a50: sub             lr, x0, #1, lsl #12
    //     0xeb4a54: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4a58: blr             lr
    // 0xeb4a5c: mov             x3, x0
    // 0xeb4a60: b               #0xeb4a68
    // 0xeb4a64: mov             x3, x1
    // 0xeb4a68: ldur            x0, [fp, #-0x10]
    // 0xeb4a6c: stur            x3, [fp, #-0x48]
    // 0xeb4a70: LoadField: r4 = r0->field_1f
    //     0xeb4a70: ldur            w4, [x0, #0x1f]
    // 0xeb4a74: DecompressPointer r4
    //     0xeb4a74: add             x4, x4, HEAP, lsl #32
    // 0xeb4a78: stur            x4, [fp, #-0x40]
    // 0xeb4a7c: cmp             w4, NULL
    // 0xeb4a80: b.eq            #0xeb4e68
    // 0xeb4a84: LoadField: r5 = r0->field_23
    //     0xeb4a84: ldur            w5, [x0, #0x23]
    // 0xeb4a88: DecompressPointer r5
    //     0xeb4a88: add             x5, x5, HEAP, lsl #32
    // 0xeb4a8c: stur            x5, [fp, #-0x38]
    // 0xeb4a90: cmp             w5, NULL
    // 0xeb4a94: b.eq            #0xeb4e6c
    // 0xeb4a98: ldur            x1, [fp, #-0x18]
    // 0xeb4a9c: r2 = 0
    //     0xeb4a9c: movz            x2, #0
    // 0xeb4aa0: r0 = skip()
    //     0xeb4aa0: bl              #0xa5a124  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::skip
    // 0xeb4aa4: mov             x4, x0
    // 0xeb4aa8: ldur            x0, [fp, #-0x38]
    // 0xeb4aac: stur            x4, [fp, #-0x58]
    // 0xeb4ab0: r5 = LoadInt32Instr(r0)
    //     0xeb4ab0: sbfx            x5, x0, #1, #0x1f
    //     0xeb4ab4: tbz             w0, #0, #0xeb4abc
    //     0xeb4ab8: ldur            x5, [x0, #7]
    // 0xeb4abc: stur            x5, [fp, #-0x50]
    // 0xeb4ac0: tbz             x5, #0x3f, #0xeb4ad0
    // 0xeb4ac4: ldur            x6, [fp, #-0x48]
    // 0xeb4ac8: ldur            x7, [fp, #-0x40]
    // 0xeb4acc: b               #0xeb4af8
    // 0xeb4ad0: ldur            x6, [fp, #-0x48]
    // 0xeb4ad4: cmp             x5, x6
    // 0xeb4ad8: b.le            #0xeb4ae4
    // 0xeb4adc: ldur            x7, [fp, #-0x40]
    // 0xeb4ae0: b               #0xeb4af8
    // 0xeb4ae4: ldur            x7, [fp, #-0x40]
    // 0xeb4ae8: LoadField: r0 = r7->field_13
    //     0xeb4ae8: ldur            w0, [x7, #0x13]
    // 0xeb4aec: r1 = LoadInt32Instr(r0)
    //     0xeb4aec: sbfx            x1, x0, #1, #0x1f
    // 0xeb4af0: cmp             x6, x1
    // 0xeb4af4: b.le            #0xeb4b24
    // 0xeb4af8: LoadField: r2 = r7->field_13
    //     0xeb4af8: ldur            w2, [x7, #0x13]
    // 0xeb4afc: r0 = BoxInt64Instr(r6)
    //     0xeb4afc: sbfiz           x0, x6, #1, #0x1f
    //     0xeb4b00: cmp             x6, x0, asr #1
    //     0xeb4b04: b.eq            #0xeb4b10
    //     0xeb4b08: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb4b0c: stur            x6, [x0, #7]
    // 0xeb4b10: r3 = LoadInt32Instr(r2)
    //     0xeb4b10: sbfx            x3, x2, #1, #0x1f
    // 0xeb4b14: mov             x1, x5
    // 0xeb4b18: mov             x2, x0
    // 0xeb4b1c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeb4b1c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeb4b20: r0 = checkValidRange()
    //     0xeb4b20: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeb4b24: ldur            x4, [fp, #-0x10]
    // 0xeb4b28: ldur            x7, [fp, #-0x20]
    // 0xeb4b2c: ldur            x0, [fp, #-0x48]
    // 0xeb4b30: ldur            x8, [fp, #-8]
    // 0xeb4b34: ldur            x1, [fp, #-0x40]
    // 0xeb4b38: ldur            x2, [fp, #-0x50]
    // 0xeb4b3c: mov             x3, x0
    // 0xeb4b40: ldur            x5, [fp, #-0x58]
    // 0xeb4b44: r6 = 0
    //     0xeb4b44: movz            x6, #0
    // 0xeb4b48: r0 = _slowSetRange()
    //     0xeb4b48: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xeb4b4c: ldur            x2, [fp, #-0x10]
    // 0xeb4b50: LoadField: r3 = r2->field_23
    //     0xeb4b50: ldur            w3, [x2, #0x23]
    // 0xeb4b54: DecompressPointer r3
    //     0xeb4b54: add             x3, x3, HEAP, lsl #32
    // 0xeb4b58: cmp             w3, NULL
    // 0xeb4b5c: b.eq            #0xeb4e70
    // 0xeb4b60: ldur            x4, [fp, #-0x48]
    // 0xeb4b64: r0 = BoxInt64Instr(r4)
    //     0xeb4b64: sbfiz           x0, x4, #1, #0x1f
    //     0xeb4b68: cmp             x4, x0, asr #1
    //     0xeb4b6c: b.eq            #0xeb4b78
    //     0xeb4b70: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb4b74: stur            x4, [x0, #7]
    // 0xeb4b78: r1 = LoadInt32Instr(r3)
    //     0xeb4b78: sbfx            x1, x3, #1, #0x1f
    //     0xeb4b7c: tbz             w3, #0, #0xeb4b84
    //     0xeb4b80: ldur            x1, [x3, #7]
    // 0xeb4b84: sub             x3, x4, x1
    // 0xeb4b88: ldur            x1, [fp, #-0x20]
    // 0xeb4b8c: sub             x5, x1, x3
    // 0xeb4b90: stur            x5, [fp, #-0x50]
    // 0xeb4b94: StoreField: r2->field_23 = r0
    //     0xeb4b94: stur            w0, [x2, #0x23]
    //     0xeb4b98: tbz             w0, #0, #0xeb4bb4
    //     0xeb4b9c: ldurb           w16, [x2, #-1]
    //     0xeb4ba0: ldurb           w17, [x0, #-1]
    //     0xeb4ba4: and             x16, x17, x16, lsr #2
    //     0xeb4ba8: tst             x16, HEAP, lsr #32
    //     0xeb4bac: b.eq            #0xeb4bb4
    //     0xeb4bb0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xeb4bb4: ldur            x3, [fp, #-8]
    // 0xeb4bb8: r0 = LoadClassIdInstr(r3)
    //     0xeb4bb8: ldur            x0, [x3, #-1]
    //     0xeb4bbc: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4bc0: mov             x1, x3
    // 0xeb4bc4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4bc4: sub             lr, x0, #1, lsl #12
    //     0xeb4bc8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4bcc: blr             lr
    // 0xeb4bd0: mov             x1, x0
    // 0xeb4bd4: ldur            x0, [fp, #-0x48]
    // 0xeb4bd8: cmp             x0, x1
    // 0xeb4bdc: b.ne            #0xeb4c3c
    // 0xeb4be0: ldur            x0, [fp, #-0x50]
    // 0xeb4be4: cmp             x0, #0
    // 0xeb4be8: b.le            #0xeb4c3c
    // 0xeb4bec: ldur            x4, [fp, #-0x10]
    // 0xeb4bf0: ldur            x7, [fp, #-8]
    // 0xeb4bf4: LoadField: r2 = r4->field_1f
    //     0xeb4bf4: ldur            w2, [x4, #0x1f]
    // 0xeb4bf8: DecompressPointer r2
    //     0xeb4bf8: add             x2, x2, HEAP, lsl #32
    // 0xeb4bfc: cmp             w2, NULL
    // 0xeb4c00: b.eq            #0xeb4e74
    // 0xeb4c04: mov             x1, x4
    // 0xeb4c08: ldur            x5, [fp, #-0x28]
    // 0xeb4c0c: ldur            x6, [fp, #-0x30]
    // 0xeb4c10: r3 = 0
    //     0xeb4c10: movz            x3, #0
    // 0xeb4c14: r0 = processBlock()
    //     0xeb4c14: bl              #0xeaea20  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::processBlock
    // 0xeb4c18: ldur            x2, [fp, #-0x10]
    // 0xeb4c1c: StoreField: r2->field_23 = rZR
    //     0xeb4c1c: stur            wzr, [x2, #0x23]
    // 0xeb4c20: ldur            x1, [fp, #-8]
    // 0xeb4c24: r0 = LoadClassIdInstr(r1)
    //     0xeb4c24: ldur            x0, [x1, #-1]
    //     0xeb4c28: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4c2c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4c2c: sub             lr, x0, #1, lsl #12
    //     0xeb4c30: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4c34: blr             lr
    // 0xeb4c38: b               #0xeb4c40
    // 0xeb4c3c: r0 = 0
    //     0xeb4c3c: movz            x0, #0
    // 0xeb4c40: ldur            x1, [fp, #-0x50]
    // 0xeb4c44: b               #0xeb4c50
    // 0xeb4c48: mov             x1, x2
    // 0xeb4c4c: r0 = 0
    //     0xeb4c4c: movz            x0, #0
    // 0xeb4c50: ldur            x2, [fp, #-0x10]
    // 0xeb4c54: LoadField: r3 = r2->field_7
    //     0xeb4c54: ldur            w3, [x2, #7]
    // 0xeb4c58: DecompressPointer r3
    //     0xeb4c58: add             x3, x3, HEAP, lsl #32
    // 0xeb4c5c: stur            x3, [fp, #-8]
    // 0xeb4c60: mov             x6, x1
    // 0xeb4c64: mov             x5, x0
    // 0xeb4c68: r7 = 0
    //     0xeb4c68: movz            x7, #0
    // 0xeb4c6c: ldur            x4, [fp, #-0x30]
    // 0xeb4c70: stur            x7, [fp, #-0x20]
    // 0xeb4c74: stur            x6, [fp, #-0x48]
    // 0xeb4c78: stur            x5, [fp, #-0x50]
    // 0xeb4c7c: CheckStackOverflow
    //     0xeb4c7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb4c80: cmp             SP, x16
    //     0xeb4c84: b.ls            #0xeb4e78
    // 0xeb4c88: r0 = LoadClassIdInstr(r3)
    //     0xeb4c88: ldur            x0, [x3, #-1]
    //     0xeb4c8c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4c90: mov             x1, x3
    // 0xeb4c94: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4c94: sub             lr, x0, #1, lsl #12
    //     0xeb4c98: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4c9c: blr             lr
    // 0xeb4ca0: mov             x1, x0
    // 0xeb4ca4: ldur            x0, [fp, #-0x48]
    // 0xeb4ca8: cmp             x0, x1
    // 0xeb4cac: b.le            #0xeb4d64
    // 0xeb4cb0: ldur            x7, [fp, #-0x30]
    // 0xeb4cb4: ldur            x9, [fp, #-0x20]
    // 0xeb4cb8: ldur            x8, [fp, #-0x50]
    // 0xeb4cbc: ldur            x4, [fp, #-8]
    // 0xeb4cc0: add             x6, x7, x8
    // 0xeb4cc4: ldur            x1, [fp, #-0x10]
    // 0xeb4cc8: ldur            x2, [fp, #-0x18]
    // 0xeb4ccc: mov             x3, x9
    // 0xeb4cd0: ldur            x5, [fp, #-0x28]
    // 0xeb4cd4: r0 = processBlock()
    //     0xeb4cd4: bl              #0xeaea20  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::processBlock
    // 0xeb4cd8: ldur            x2, [fp, #-8]
    // 0xeb4cdc: r0 = LoadClassIdInstr(r2)
    //     0xeb4cdc: ldur            x0, [x2, #-1]
    //     0xeb4ce0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4ce4: mov             x1, x2
    // 0xeb4ce8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4ce8: sub             lr, x0, #1, lsl #12
    //     0xeb4cec: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4cf0: blr             lr
    // 0xeb4cf4: ldur            x2, [fp, #-0x20]
    // 0xeb4cf8: add             x7, x2, x0
    // 0xeb4cfc: ldur            x2, [fp, #-8]
    // 0xeb4d00: stur            x7, [fp, #-0x60]
    // 0xeb4d04: r0 = LoadClassIdInstr(r2)
    //     0xeb4d04: ldur            x0, [x2, #-1]
    //     0xeb4d08: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4d0c: mov             x1, x2
    // 0xeb4d10: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4d10: sub             lr, x0, #1, lsl #12
    //     0xeb4d14: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4d18: blr             lr
    // 0xeb4d1c: ldur            x3, [fp, #-0x48]
    // 0xeb4d20: sub             x6, x3, x0
    // 0xeb4d24: ldur            x2, [fp, #-8]
    // 0xeb4d28: stur            x6, [fp, #-0x68]
    // 0xeb4d2c: r0 = LoadClassIdInstr(r2)
    //     0xeb4d2c: ldur            x0, [x2, #-1]
    //     0xeb4d30: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4d34: mov             x1, x2
    // 0xeb4d38: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4d38: sub             lr, x0, #1, lsl #12
    //     0xeb4d3c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4d40: blr             lr
    // 0xeb4d44: mov             x1, x0
    // 0xeb4d48: ldur            x0, [fp, #-0x50]
    // 0xeb4d4c: add             x5, x0, x1
    // 0xeb4d50: ldur            x7, [fp, #-0x60]
    // 0xeb4d54: ldur            x6, [fp, #-0x68]
    // 0xeb4d58: ldur            x2, [fp, #-0x10]
    // 0xeb4d5c: ldur            x3, [fp, #-8]
    // 0xeb4d60: b               #0xeb4c6c
    // 0xeb4d64: ldur            x2, [fp, #-0x20]
    // 0xeb4d68: mov             x3, x0
    // 0xeb4d6c: ldur            x0, [fp, #-0x50]
    // 0xeb4d70: cmp             x3, #0
    // 0xeb4d74: b.le            #0xeb4e4c
    // 0xeb4d78: ldur            x4, [fp, #-0x10]
    // 0xeb4d7c: LoadField: r5 = r4->field_1f
    //     0xeb4d7c: ldur            w5, [x4, #0x1f]
    // 0xeb4d80: DecompressPointer r5
    //     0xeb4d80: add             x5, x5, HEAP, lsl #32
    // 0xeb4d84: stur            x5, [fp, #-8]
    // 0xeb4d88: cmp             w5, NULL
    // 0xeb4d8c: b.eq            #0xeb4e80
    // 0xeb4d90: ldur            x1, [fp, #-0x18]
    // 0xeb4d94: r0 = skip()
    //     0xeb4d94: bl              #0xa5a124  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::skip
    // 0xeb4d98: mov             x5, x0
    // 0xeb4d9c: ldur            x4, [fp, #-0x48]
    // 0xeb4da0: stur            x5, [fp, #-0x18]
    // 0xeb4da4: tbz             x4, #0x3f, #0xeb4db0
    // 0xeb4da8: ldur            x6, [fp, #-8]
    // 0xeb4dac: b               #0xeb4dc4
    // 0xeb4db0: ldur            x6, [fp, #-8]
    // 0xeb4db4: LoadField: r0 = r6->field_13
    //     0xeb4db4: ldur            w0, [x6, #0x13]
    // 0xeb4db8: r1 = LoadInt32Instr(r0)
    //     0xeb4db8: sbfx            x1, x0, #1, #0x1f
    // 0xeb4dbc: cmp             x4, x1
    // 0xeb4dc0: b.le            #0xeb4df0
    // 0xeb4dc4: LoadField: r2 = r6->field_13
    //     0xeb4dc4: ldur            w2, [x6, #0x13]
    // 0xeb4dc8: r0 = BoxInt64Instr(r4)
    //     0xeb4dc8: sbfiz           x0, x4, #1, #0x1f
    //     0xeb4dcc: cmp             x4, x0, asr #1
    //     0xeb4dd0: b.eq            #0xeb4ddc
    //     0xeb4dd4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb4dd8: stur            x4, [x0, #7]
    // 0xeb4ddc: r3 = LoadInt32Instr(r2)
    //     0xeb4ddc: sbfx            x3, x2, #1, #0x1f
    // 0xeb4de0: mov             x2, x0
    // 0xeb4de4: r1 = 0
    //     0xeb4de4: movz            x1, #0
    // 0xeb4de8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xeb4de8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xeb4dec: r0 = checkValidRange()
    //     0xeb4dec: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xeb4df0: ldur            x4, [fp, #-0x10]
    // 0xeb4df4: ldur            x0, [fp, #-0x48]
    // 0xeb4df8: ldur            x1, [fp, #-8]
    // 0xeb4dfc: mov             x3, x0
    // 0xeb4e00: ldur            x5, [fp, #-0x18]
    // 0xeb4e04: r2 = 0
    //     0xeb4e04: movz            x2, #0
    // 0xeb4e08: r6 = 0
    //     0xeb4e08: movz            x6, #0
    // 0xeb4e0c: r0 = _slowSetRange()
    //     0xeb4e0c: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xeb4e10: ldur            x2, [fp, #-0x48]
    // 0xeb4e14: r0 = BoxInt64Instr(r2)
    //     0xeb4e14: sbfiz           x0, x2, #1, #0x1f
    //     0xeb4e18: cmp             x2, x0, asr #1
    //     0xeb4e1c: b.eq            #0xeb4e28
    //     0xeb4e20: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb4e24: stur            x2, [x0, #7]
    // 0xeb4e28: ldur            x1, [fp, #-0x10]
    // 0xeb4e2c: StoreField: r1->field_23 = r0
    //     0xeb4e2c: stur            w0, [x1, #0x23]
    //     0xeb4e30: tbz             w0, #0, #0xeb4e4c
    //     0xeb4e34: ldurb           w16, [x1, #-1]
    //     0xeb4e38: ldurb           w17, [x0, #-1]
    //     0xeb4e3c: and             x16, x17, x16, lsr #2
    //     0xeb4e40: tst             x16, HEAP, lsr #32
    //     0xeb4e44: b.eq            #0xeb4e4c
    //     0xeb4e48: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xeb4e4c: ldur            x0, [fp, #-0x50]
    // 0xeb4e50: LeaveFrame
    //     0xeb4e50: mov             SP, fp
    //     0xeb4e54: ldp             fp, lr, [SP], #0x10
    // 0xeb4e58: ret
    //     0xeb4e58: ret             
    // 0xeb4e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb4e5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb4e60: b               #0xeb49cc
    // 0xeb4e64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb4e64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeb4e68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb4e68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeb4e6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb4e6c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeb4e70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb4e70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeb4e74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb4e74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xeb4e78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb4e78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb4e7c: b               #0xeb4c88
    // 0xeb4e80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeb4e80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ getOutputSize(/* No info */) {
    // ** addr: 0xeb4e84, size: 0x160
    // 0xeb4e84: EnterFrame
    //     0xeb4e84: stp             fp, lr, [SP, #-0x10]!
    //     0xeb4e88: mov             fp, SP
    // 0xeb4e8c: AllocStack(0x18)
    //     0xeb4e8c: sub             SP, SP, #0x18
    // 0xeb4e90: CheckStackOverflow
    //     0xeb4e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb4e94: cmp             SP, x16
    //     0xeb4e98: b.ls            #0xeb4fa0
    // 0xeb4e9c: LoadField: r0 = r1->field_b
    //     0xeb4e9c: ldur            w0, [x1, #0xb]
    // 0xeb4ea0: DecompressPointer r0
    //     0xeb4ea0: add             x0, x0, HEAP, lsl #32
    // 0xeb4ea4: r16 = Sentinel
    //     0xeb4ea4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb4ea8: cmp             w0, w16
    // 0xeb4eac: b.eq            #0xeb4fa8
    // 0xeb4eb0: tbnz            w0, #4, #0xeb4edc
    // 0xeb4eb4: LoadField: r0 = r1->field_f
    //     0xeb4eb4: ldur            w0, [x1, #0xf]
    // 0xeb4eb8: DecompressPointer r0
    //     0xeb4eb8: add             x0, x0, HEAP, lsl #32
    // 0xeb4ebc: r16 = Sentinel
    //     0xeb4ebc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb4ec0: cmp             w0, w16
    // 0xeb4ec4: b.eq            #0xeb4fb4
    // 0xeb4ec8: r3 = LoadInt32Instr(r0)
    //     0xeb4ec8: sbfx            x3, x0, #1, #0x1f
    //     0xeb4ecc: tbz             w0, #0, #0xeb4ed4
    //     0xeb4ed0: ldur            x3, [x0, #7]
    // 0xeb4ed4: mov             x0, x3
    // 0xeb4ed8: b               #0xeb4f00
    // 0xeb4edc: LoadField: r0 = r1->field_f
    //     0xeb4edc: ldur            w0, [x1, #0xf]
    // 0xeb4ee0: DecompressPointer r0
    //     0xeb4ee0: add             x0, x0, HEAP, lsl #32
    // 0xeb4ee4: r16 = Sentinel
    //     0xeb4ee4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb4ee8: cmp             w0, w16
    // 0xeb4eec: b.eq            #0xeb4fc0
    // 0xeb4ef0: r3 = LoadInt32Instr(r0)
    //     0xeb4ef0: sbfx            x3, x0, #1, #0x1f
    //     0xeb4ef4: tbz             w0, #0, #0xeb4efc
    //     0xeb4ef8: ldur            x3, [x0, #7]
    // 0xeb4efc: neg             x0, x3
    // 0xeb4f00: add             x3, x2, x0
    // 0xeb4f04: stur            x3, [fp, #-0x10]
    // 0xeb4f08: LoadField: r2 = r1->field_7
    //     0xeb4f08: ldur            w2, [x1, #7]
    // 0xeb4f0c: DecompressPointer r2
    //     0xeb4f0c: add             x2, x2, HEAP, lsl #32
    // 0xeb4f10: stur            x2, [fp, #-8]
    // 0xeb4f14: r0 = LoadClassIdInstr(r2)
    //     0xeb4f14: ldur            x0, [x2, #-1]
    //     0xeb4f18: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4f1c: mov             x1, x2
    // 0xeb4f20: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4f20: sub             lr, x0, #1, lsl #12
    //     0xeb4f24: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4f28: blr             lr
    // 0xeb4f2c: mov             x1, x0
    // 0xeb4f30: ldur            x0, [fp, #-0x10]
    // 0xeb4f34: add             x2, x0, x1
    // 0xeb4f38: sub             x3, x2, #1
    // 0xeb4f3c: ldur            x2, [fp, #-8]
    // 0xeb4f40: stur            x3, [fp, #-0x10]
    // 0xeb4f44: r0 = LoadClassIdInstr(r2)
    //     0xeb4f44: ldur            x0, [x2, #-1]
    //     0xeb4f48: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4f4c: mov             x1, x2
    // 0xeb4f50: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4f50: sub             lr, x0, #1, lsl #12
    //     0xeb4f54: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4f58: blr             lr
    // 0xeb4f5c: mov             x1, x0
    // 0xeb4f60: ldur            x0, [fp, #-0x10]
    // 0xeb4f64: cbz             x1, #0xeb4fcc
    // 0xeb4f68: sdiv            x2, x0, x1
    // 0xeb4f6c: ldur            x1, [fp, #-8]
    // 0xeb4f70: stur            x2, [fp, #-0x18]
    // 0xeb4f74: r0 = LoadClassIdInstr(r1)
    //     0xeb4f74: ldur            x0, [x1, #-1]
    //     0xeb4f78: ubfx            x0, x0, #0xc, #0x14
    // 0xeb4f7c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb4f7c: sub             lr, x0, #1, lsl #12
    //     0xeb4f80: ldr             lr, [x21, lr, lsl #3]
    //     0xeb4f84: blr             lr
    // 0xeb4f88: ldur            x1, [fp, #-0x18]
    // 0xeb4f8c: mul             x2, x1, x0
    // 0xeb4f90: mov             x0, x2
    // 0xeb4f94: LeaveFrame
    //     0xeb4f94: mov             SP, fp
    //     0xeb4f98: ldp             fp, lr, [SP], #0x10
    // 0xeb4f9c: ret
    //     0xeb4f9c: ret             
    // 0xeb4fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb4fa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb4fa4: b               #0xeb4e9c
    // 0xeb4fa8: r9 = _forEncryption
    //     0xeb4fa8: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a48] Field <BaseAEADBlockCipher._forEncryption@2660101045>: late (offset: 0xc)
    //     0xeb4fac: ldr             x9, [x9, #0xa48]
    // 0xeb4fb0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4fb0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4fb4: r9 = _macSize
    //     0xeb4fb4: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a28] Field <BaseAEADBlockCipher._macSize@2660101045>: late (offset: 0x10)
    //     0xeb4fb8: ldr             x9, [x9, #0xa28]
    // 0xeb4fbc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4fbc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4fc0: r9 = _macSize
    //     0xeb4fc0: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a28] Field <BaseAEADBlockCipher._macSize@2660101045>: late (offset: 0x10)
    //     0xeb4fc4: ldr             x9, [x9, #0xa28]
    // 0xeb4fc8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xeb4fc8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xeb4fcc: stp             x0, x1, [SP, #-0x10]!
    // 0xeb4fd0: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0xeb4fd4: r4 = 0
    //     0xeb4fd4: movz            x4, #0
    // 0xeb4fd8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeb4fdc: blr             lr
    // 0xeb4fe0: brk             #0
  }
  get _ blockSize(/* No info */) {
    // ** addr: 0xeb5334, size: 0x50
    // 0xeb5334: EnterFrame
    //     0xeb5334: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5338: mov             fp, SP
    // 0xeb533c: CheckStackOverflow
    //     0xeb533c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5340: cmp             SP, x16
    //     0xeb5344: b.ls            #0xeb537c
    // 0xeb5348: LoadField: r0 = r1->field_7
    //     0xeb5348: ldur            w0, [x1, #7]
    // 0xeb534c: DecompressPointer r0
    //     0xeb534c: add             x0, x0, HEAP, lsl #32
    // 0xeb5350: r1 = LoadClassIdInstr(r0)
    //     0xeb5350: ldur            x1, [x0, #-1]
    //     0xeb5354: ubfx            x1, x1, #0xc, #0x14
    // 0xeb5358: mov             x16, x0
    // 0xeb535c: mov             x0, x1
    // 0xeb5360: mov             x1, x16
    // 0xeb5364: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb5364: sub             lr, x0, #1, lsl #12
    //     0xeb5368: ldr             lr, [x21, lr, lsl #3]
    //     0xeb536c: blr             lr
    // 0xeb5370: LeaveFrame
    //     0xeb5370: mov             SP, fp
    //     0xeb5374: ldp             fp, lr, [SP], #0x10
    // 0xeb5378: ret
    //     0xeb5378: ret             
    // 0xeb537c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb537c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5380: b               #0xeb5348
  }
}
