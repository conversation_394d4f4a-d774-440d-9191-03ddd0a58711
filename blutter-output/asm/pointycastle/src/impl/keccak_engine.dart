// lib: , url: package:pointycastle/src/impl/keccak_engine.dart

// class id: 1051040, size: 0x8
class :: {
}

// class id: 663, size: 0x18, field offset: 0x8
abstract class KeccakEngine extends BaseDigest {

  late int fixedOutputLength; // offset: 0x14
  late int _rate; // offset: 0x10

  get _ byteLength(/* No info */) {
    // ** addr: 0x869a98, size: 0x48
    // 0x869a98: EnterFrame
    //     0x869a98: stp             fp, lr, [SP, #-0x10]!
    //     0x869a9c: mov             fp, SP
    // 0x869aa0: r2 = 8
    //     0x869aa0: movz            x2, #0x8
    // 0x869aa4: LoadField: r3 = r1->field_f
    //     0x869aa4: ldur            w3, [x1, #0xf]
    // 0x869aa8: DecompressPointer r3
    //     0x869aa8: add             x3, x3, HEAP, lsl #32
    // 0x869aac: r16 = Sentinel
    //     0x869aac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x869ab0: cmp             w3, w16
    // 0x869ab4: b.eq            #0x869ad4
    // 0x869ab8: r1 = LoadInt32Instr(r3)
    //     0x869ab8: sbfx            x1, x3, #1, #0x1f
    //     0x869abc: tbz             w3, #0, #0x869ac4
    //     0x869ac0: ldur            x1, [x3, #7]
    // 0x869ac4: sdiv            x0, x1, x2
    // 0x869ac8: LeaveFrame
    //     0x869ac8: mov             SP, fp
    //     0x869acc: ldp             fp, lr, [SP], #0x10
    // 0x869ad0: ret
    //     0x869ad0: ret             
    // 0x869ad4: r9 = _rate
    //     0x869ad4: add             x9, PP, #0x20, lsl #12  ; [pp+0x20a00] Field <KeccakEngine._rate@2663293810>: late (offset: 0x10)
    //     0x869ad8: ldr             x9, [x9, #0xa00]
    // 0x869adc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x869adc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0x8d639c, size: 0x3c
    // 0x8d639c: EnterFrame
    //     0x8d639c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d63a0: mov             fp, SP
    // 0x8d63a4: r0 = 1600
    //     0x8d63a4: movz            x0, #0x640
    // 0x8d63a8: CheckStackOverflow
    //     0x8d63a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d63ac: cmp             SP, x16
    //     0x8d63b0: b.ls            #0x8d63d0
    // 0x8d63b4: lsl             x3, x2, #1
    // 0x8d63b8: sub             x2, x0, x3
    // 0x8d63bc: r0 = _initSponge()
    //     0x8d63bc: bl              #0x8d63d8  ; [package:pointycastle/src/impl/keccak_engine.dart] KeccakEngine::_initSponge
    // 0x8d63c0: r0 = Null
    //     0x8d63c0: mov             x0, NULL
    // 0x8d63c4: LeaveFrame
    //     0x8d63c4: mov             SP, fp
    //     0x8d63c8: ldp             fp, lr, [SP], #0x10
    // 0x8d63cc: ret
    //     0x8d63cc: ret             
    // 0x8d63d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d63d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d63d4: b               #0x8d63b4
  }
  _ _initSponge(/* No info */) {
    // ** addr: 0x8d63d8, size: 0x134
    // 0x8d63d8: EnterFrame
    //     0x8d63d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d63dc: mov             fp, SP
    // 0x8d63e0: AllocStack(0x10)
    //     0x8d63e0: sub             SP, SP, #0x10
    // 0x8d63e4: SetupParameters(KeccakEngine this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */)
    //     0x8d63e4: mov             x6, x1
    //     0x8d63e8: mov             x4, x2
    //     0x8d63ec: stur            x1, [fp, #-8]
    //     0x8d63f0: stur            x2, [fp, #-0x10]
    // 0x8d63f4: CheckStackOverflow
    //     0x8d63f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d63f8: cmp             SP, x16
    //     0x8d63fc: b.ls            #0x8d6504
    // 0x8d6400: cmp             x4, #0
    // 0x8d6404: b.le            #0x8d64e4
    // 0x8d6408: cmp             x4, #0x640
    // 0x8d640c: b.ge            #0x8d64e4
    // 0x8d6410: tst             x4, #0x3f
    // 0x8d6414: b.ne            #0x8d64e4
    // 0x8d6418: r0 = BoxInt64Instr(r4)
    //     0x8d6418: sbfiz           x0, x4, #1, #0x1f
    //     0x8d641c: cmp             x4, x0, asr #1
    //     0x8d6420: b.eq            #0x8d642c
    //     0x8d6424: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d6428: stur            x4, [x0, #7]
    // 0x8d642c: StoreField: r6->field_f = r0
    //     0x8d642c: stur            w0, [x6, #0xf]
    //     0x8d6430: tbz             w0, #0, #0x8d644c
    //     0x8d6434: ldurb           w16, [x6, #-1]
    //     0x8d6438: ldurb           w17, [x0, #-1]
    //     0x8d643c: and             x16, x17, x16, lsr #2
    //     0x8d6440: tst             x16, HEAP, lsr #32
    //     0x8d6444: b.eq            #0x8d644c
    //     0x8d6448: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8d644c: LoadField: r1 = r6->field_7
    //     0x8d644c: ldur            w1, [x6, #7]
    // 0x8d6450: DecompressPointer r1
    //     0x8d6450: add             x1, x1, HEAP, lsl #32
    // 0x8d6454: LoadField: r0 = r1->field_13
    //     0x8d6454: ldur            w0, [x1, #0x13]
    // 0x8d6458: r3 = LoadInt32Instr(r0)
    //     0x8d6458: sbfx            x3, x0, #1, #0x1f
    // 0x8d645c: r2 = 0
    //     0x8d645c: movz            x2, #0
    // 0x8d6460: r5 = 0
    //     0x8d6460: movz            x5, #0
    // 0x8d6464: r0 = fillRange()
    //     0x8d6464: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0x8d6468: ldur            x0, [fp, #-8]
    // 0x8d646c: LoadField: r1 = r0->field_b
    //     0x8d646c: ldur            w1, [x0, #0xb]
    // 0x8d6470: DecompressPointer r1
    //     0x8d6470: add             x1, x1, HEAP, lsl #32
    // 0x8d6474: LoadField: r2 = r1->field_13
    //     0x8d6474: ldur            w2, [x1, #0x13]
    // 0x8d6478: r3 = LoadInt32Instr(r2)
    //     0x8d6478: sbfx            x3, x2, #1, #0x1f
    // 0x8d647c: r2 = 0
    //     0x8d647c: movz            x2, #0
    // 0x8d6480: r5 = 0
    //     0x8d6480: movz            x5, #0
    // 0x8d6484: r0 = fillRange()
    //     0x8d6484: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0x8d6488: ldur            x0, [fp, #-0x10]
    // 0x8d648c: r1 = 1600
    //     0x8d648c: movz            x1, #0x640
    // 0x8d6490: sub             x2, x1, x0
    // 0x8d6494: r0 = 2
    //     0x8d6494: movz            x0, #0x2
    // 0x8d6498: sdiv            x3, x2, x0
    // 0x8d649c: r0 = BoxInt64Instr(r3)
    //     0x8d649c: sbfiz           x0, x3, #1, #0x1f
    //     0x8d64a0: cmp             x3, x0, asr #1
    //     0x8d64a4: b.eq            #0x8d64b0
    //     0x8d64a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d64ac: stur            x3, [x0, #7]
    // 0x8d64b0: ldur            x1, [fp, #-8]
    // 0x8d64b4: StoreField: r1->field_13 = r0
    //     0x8d64b4: stur            w0, [x1, #0x13]
    //     0x8d64b8: tbz             w0, #0, #0x8d64d4
    //     0x8d64bc: ldurb           w16, [x1, #-1]
    //     0x8d64c0: ldurb           w17, [x0, #-1]
    //     0x8d64c4: and             x16, x17, x16, lsr #2
    //     0x8d64c8: tst             x16, HEAP, lsr #32
    //     0x8d64cc: b.eq            #0x8d64d4
    //     0x8d64d0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d64d4: r0 = Null
    //     0x8d64d4: mov             x0, NULL
    // 0x8d64d8: LeaveFrame
    //     0x8d64d8: mov             SP, fp
    //     0x8d64dc: ldp             fp, lr, [SP], #0x10
    // 0x8d64e0: ret
    //     0x8d64e0: ret             
    // 0x8d64e4: r0 = StateError()
    //     0x8d64e4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8d64e8: mov             x1, x0
    // 0x8d64ec: r0 = "invalid rate value"
    //     0x8d64ec: add             x0, PP, #0x19, lsl #12  ; [pp+0x193d8] "invalid rate value"
    //     0x8d64f0: ldr             x0, [x0, #0x3d8]
    // 0x8d64f4: StoreField: r1->field_b = r0
    //     0x8d64f4: stur            w0, [x1, #0xb]
    // 0x8d64f8: mov             x0, x1
    // 0x8d64fc: r0 = Throw()
    //     0x8d64fc: bl              #0xec04b8  ; ThrowStub
    // 0x8d6500: brk             #0
    // 0x8d6504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d6504: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6508: b               #0x8d6400
  }
  get _ digestSize(/* No info */) {
    // ** addr: 0xe61db8, size: 0x48
    // 0xe61db8: EnterFrame
    //     0xe61db8: stp             fp, lr, [SP, #-0x10]!
    //     0xe61dbc: mov             fp, SP
    // 0xe61dc0: r2 = 8
    //     0xe61dc0: movz            x2, #0x8
    // 0xe61dc4: LoadField: r3 = r1->field_13
    //     0xe61dc4: ldur            w3, [x1, #0x13]
    // 0xe61dc8: DecompressPointer r3
    //     0xe61dc8: add             x3, x3, HEAP, lsl #32
    // 0xe61dcc: r16 = Sentinel
    //     0xe61dcc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe61dd0: cmp             w3, w16
    // 0xe61dd4: b.eq            #0xe61df4
    // 0xe61dd8: r1 = LoadInt32Instr(r3)
    //     0xe61dd8: sbfx            x1, x3, #1, #0x1f
    //     0xe61ddc: tbz             w3, #0, #0xe61de4
    //     0xe61de0: ldur            x1, [x3, #7]
    // 0xe61de4: sdiv            x0, x1, x2
    // 0xe61de8: LeaveFrame
    //     0xe61de8: mov             SP, fp
    //     0xe61dec: ldp             fp, lr, [SP], #0x10
    // 0xe61df0: ret
    //     0xe61df0: ret             
    // 0xe61df4: r9 = fixedOutputLength
    //     0xe61df4: add             x9, PP, #0x20, lsl #12  ; [pp+0x209f8] Field <KeccakEngine.fixedOutputLength>: late (offset: 0x14)
    //     0xe61df8: ldr             x9, [x9, #0x9f8]
    // 0xe61dfc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe61dfc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
