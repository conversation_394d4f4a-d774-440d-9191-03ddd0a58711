// lib: , url: package:pointycastle/src/impl/md4_family_digest.dart

// class id: 1051042, size: 0x8
class :: {
}

// class id: 651, size: 0x2c, field offset: 0x8
abstract class MD4FamilyDigest extends BaseDigest {

  late int bufferOffset; // offset: 0x28
  late int _wordBufferOffset; // offset: 0x10

  _ MD4FamilyDigest(/* No info */) {
    // ** addr: 0x8d5bec, size: 0x208
    // 0x8d5bec: EnterFrame
    //     0x8d5bec: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5bf0: mov             fp, SP
    // 0x8d5bf4: AllocStack(0x38)
    //     0x8d5bf4: sub             SP, SP, #0x38
    // 0x8d5bf8: SetupParameters(MD4FamilyDigest this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, [dynamic _ = Null /* r4, fp-0x8 */])
    //     0x8d5bf8: mov             x0, x2
    //     0x8d5bfc: stur            x1, [fp, #-0x10]
    //     0x8d5c00: stur            x2, [fp, #-0x18]
    //     0x8d5c04: stur            x3, [fp, #-0x20]
    //     0x8d5c08: stur            x5, [fp, #-0x28]
    //     0x8d5c0c: ldur            w2, [x4, #0x13]
    //     0x8d5c10: sub             x4, x2, #8
    //     0x8d5c14: cmp             w4, #2
    //     0x8d5c18: b.lt            #0x8d5c2c
    //     0x8d5c1c: add             x2, fp, w4, sxtw #2
    //     0x8d5c20: ldr             x2, [x2, #8]
    //     0x8d5c24: mov             x4, x2
    //     0x8d5c28: b               #0x8d5c30
    //     0x8d5c2c: mov             x4, NULL
    //     0x8d5c30: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5c34: stur            x4, [fp, #-8]
    // 0x8d5c30: r2 = Sentinel
    // 0x8d5c38: CheckStackOverflow
    //     0x8d5c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5c3c: cmp             SP, x16
    //     0x8d5c40: b.ls            #0x8d5ddc
    // 0x8d5c44: StoreField: r1->field_f = r2
    //     0x8d5c44: stur            w2, [x1, #0xf]
    // 0x8d5c48: StoreField: r1->field_27 = r2
    //     0x8d5c48: stur            w2, [x1, #0x27]
    // 0x8d5c4c: r0 = Register64()
    //     0x8d5c4c: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8d5c50: mov             x3, x0
    // 0x8d5c54: r0 = Sentinel
    //     0x8d5c54: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d5c58: stur            x3, [fp, #-0x30]
    // 0x8d5c5c: StoreField: r3->field_7 = r0
    //     0x8d5c5c: stur            w0, [x3, #7]
    // 0x8d5c60: StoreField: r3->field_b = r0
    //     0x8d5c60: stur            w0, [x3, #0xb]
    // 0x8d5c64: str             NULL, [SP]
    // 0x8d5c68: mov             x1, x3
    // 0x8d5c6c: r2 = 0
    //     0x8d5c6c: movz            x2, #0
    // 0x8d5c70: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d5c70: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d5c74: r0 = set()
    //     0x8d5c74: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d5c78: ldur            x0, [fp, #-0x30]
    // 0x8d5c7c: ldur            x1, [fp, #-0x10]
    // 0x8d5c80: StoreField: r1->field_7 = r0
    //     0x8d5c80: stur            w0, [x1, #7]
    //     0x8d5c84: ldurb           w16, [x1, #-1]
    //     0x8d5c88: ldurb           w17, [x0, #-1]
    //     0x8d5c8c: and             x16, x17, x16, lsr #2
    //     0x8d5c90: tst             x16, HEAP, lsr #32
    //     0x8d5c94: b.eq            #0x8d5c9c
    //     0x8d5c98: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d5c9c: r4 = 8
    //     0x8d5c9c: movz            x4, #0x8
    // 0x8d5ca0: r0 = AllocateUint8Array()
    //     0x8d5ca0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8d5ca4: ldur            x3, [fp, #-0x10]
    // 0x8d5ca8: StoreField: r3->field_b = r0
    //     0x8d5ca8: stur            w0, [x3, #0xb]
    //     0x8d5cac: ldurb           w16, [x3, #-1]
    //     0x8d5cb0: ldurb           w17, [x0, #-1]
    //     0x8d5cb4: and             x16, x17, x16, lsr #2
    //     0x8d5cb8: tst             x16, HEAP, lsr #32
    //     0x8d5cbc: b.eq            #0x8d5cc4
    //     0x8d5cc0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d5cc4: ldur            x0, [fp, #-0x18]
    // 0x8d5cc8: StoreField: r3->field_13 = r0
    //     0x8d5cc8: stur            w0, [x3, #0x13]
    //     0x8d5ccc: ldurb           w16, [x3, #-1]
    //     0x8d5cd0: ldurb           w17, [x0, #-1]
    //     0x8d5cd4: and             x16, x17, x16, lsr #2
    //     0x8d5cd8: tst             x16, HEAP, lsr #32
    //     0x8d5cdc: b.eq            #0x8d5ce4
    //     0x8d5ce0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d5ce4: ldur            x0, [fp, #-8]
    // 0x8d5ce8: cmp             w0, NULL
    // 0x8d5cec: b.ne            #0x8d5cf8
    // 0x8d5cf0: ldur            x1, [fp, #-0x20]
    // 0x8d5cf4: b               #0x8d5d04
    // 0x8d5cf8: r1 = LoadInt32Instr(r0)
    //     0x8d5cf8: sbfx            x1, x0, #1, #0x1f
    //     0x8d5cfc: tbz             w0, #0, #0x8d5d04
    //     0x8d5d00: ldur            x1, [x0, #7]
    // 0x8d5d04: ldur            x0, [fp, #-0x20]
    // 0x8d5d08: ArrayStore: r3[0] = r1  ; List_8
    //     0x8d5d08: stur            x1, [x3, #0x17]
    // 0x8d5d0c: lsl             x2, x0, #1
    // 0x8d5d10: r1 = <int>
    //     0x8d5d10: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8d5d14: r0 = AllocateArray()
    //     0x8d5d14: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8d5d18: ldur            x1, [fp, #-0x20]
    // 0x8d5d1c: r2 = 0
    //     0x8d5d1c: movz            x2, #0
    // 0x8d5d20: CheckStackOverflow
    //     0x8d5d20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5d24: cmp             SP, x16
    //     0x8d5d28: b.ls            #0x8d5de4
    // 0x8d5d2c: cmp             x2, x1
    // 0x8d5d30: b.ge            #0x8d5d48
    // 0x8d5d34: ArrayStore: r0[r2] = rZR  ; Unknown_4
    //     0x8d5d34: add             x3, x0, x2, lsl #2
    //     0x8d5d38: stur            wzr, [x3, #0xf]
    // 0x8d5d3c: add             x3, x2, #1
    // 0x8d5d40: mov             x2, x3
    // 0x8d5d44: b               #0x8d5d20
    // 0x8d5d48: ldur            x3, [fp, #-0x10]
    // 0x8d5d4c: ldur            x4, [fp, #-0x28]
    // 0x8d5d50: StoreField: r3->field_1f = r0
    //     0x8d5d50: stur            w0, [x3, #0x1f]
    //     0x8d5d54: ldurb           w16, [x3, #-1]
    //     0x8d5d58: ldurb           w17, [x0, #-1]
    //     0x8d5d5c: and             x16, x17, x16, lsr #2
    //     0x8d5d60: tst             x16, HEAP, lsr #32
    //     0x8d5d64: b.eq            #0x8d5d6c
    //     0x8d5d68: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8d5d6c: lsl             x2, x4, #1
    // 0x8d5d70: r1 = <int>
    //     0x8d5d70: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8d5d74: r0 = AllocateArray()
    //     0x8d5d74: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8d5d78: ldur            x1, [fp, #-0x28]
    // 0x8d5d7c: r2 = 0
    //     0x8d5d7c: movz            x2, #0
    // 0x8d5d80: CheckStackOverflow
    //     0x8d5d80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5d84: cmp             SP, x16
    //     0x8d5d88: b.ls            #0x8d5dec
    // 0x8d5d8c: cmp             x2, x1
    // 0x8d5d90: b.ge            #0x8d5da8
    // 0x8d5d94: ArrayStore: r0[r2] = rZR  ; Unknown_4
    //     0x8d5d94: add             x3, x0, x2, lsl #2
    //     0x8d5d98: stur            wzr, [x3, #0xf]
    // 0x8d5d9c: add             x3, x2, #1
    // 0x8d5da0: mov             x2, x3
    // 0x8d5da4: b               #0x8d5d80
    // 0x8d5da8: ldur            x1, [fp, #-0x10]
    // 0x8d5dac: StoreField: r1->field_23 = r0
    //     0x8d5dac: stur            w0, [x1, #0x23]
    //     0x8d5db0: ldurb           w16, [x1, #-1]
    //     0x8d5db4: ldurb           w17, [x0, #-1]
    //     0x8d5db8: and             x16, x17, x16, lsr #2
    //     0x8d5dbc: tst             x16, HEAP, lsr #32
    //     0x8d5dc0: b.eq            #0x8d5dc8
    //     0x8d5dc4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8d5dc8: r0 = reset()
    //     0x8d5dc8: bl              #0x8d5ea4  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::reset
    // 0x8d5dcc: r0 = Null
    //     0x8d5dcc: mov             x0, NULL
    // 0x8d5dd0: LeaveFrame
    //     0x8d5dd0: mov             SP, fp
    //     0x8d5dd4: ldp             fp, lr, [SP], #0x10
    // 0x8d5dd8: ret
    //     0x8d5dd8: ret             
    // 0x8d5ddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5ddc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5de0: b               #0x8d5c44
    // 0x8d5de4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5de4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5de8: b               #0x8d5d2c
    // 0x8d5dec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5dec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5df0: b               #0x8d5d8c
  }
  _ reset(/* No info */) {
    // ** addr: 0x8d5ea4, size: 0xb0
    // 0x8d5ea4: EnterFrame
    //     0x8d5ea4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5ea8: mov             fp, SP
    // 0x8d5eac: AllocStack(0x8)
    //     0x8d5eac: sub             SP, SP, #8
    // 0x8d5eb0: SetupParameters(MD4FamilyDigest this /* r1 => r0, fp-0x8 */)
    //     0x8d5eb0: mov             x0, x1
    //     0x8d5eb4: stur            x1, [fp, #-8]
    // 0x8d5eb8: CheckStackOverflow
    //     0x8d5eb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d5ebc: cmp             SP, x16
    //     0x8d5ec0: b.ls            #0x8d5f4c
    // 0x8d5ec4: LoadField: r1 = r0->field_7
    //     0x8d5ec4: ldur            w1, [x0, #7]
    // 0x8d5ec8: DecompressPointer r1
    //     0x8d5ec8: add             x1, x1, HEAP, lsl #32
    // 0x8d5ecc: r2 = 0
    //     0x8d5ecc: movz            x2, #0
    // 0x8d5ed0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8d5ed0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8d5ed4: r0 = set()
    //     0x8d5ed4: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d5ed8: ldur            x0, [fp, #-8]
    // 0x8d5edc: StoreField: r0->field_f = rZR
    //     0x8d5edc: stur            wzr, [x0, #0xf]
    // 0x8d5ee0: LoadField: r1 = r0->field_b
    //     0x8d5ee0: ldur            w1, [x0, #0xb]
    // 0x8d5ee4: DecompressPointer r1
    //     0x8d5ee4: add             x1, x1, HEAP, lsl #32
    // 0x8d5ee8: LoadField: r2 = r1->field_13
    //     0x8d5ee8: ldur            w2, [x1, #0x13]
    // 0x8d5eec: r3 = LoadInt32Instr(r2)
    //     0x8d5eec: sbfx            x3, x2, #1, #0x1f
    // 0x8d5ef0: r2 = 0
    //     0x8d5ef0: movz            x2, #0
    // 0x8d5ef4: r5 = 0
    //     0x8d5ef4: movz            x5, #0
    // 0x8d5ef8: r0 = fillRange()
    //     0x8d5ef8: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0x8d5efc: ldur            x0, [fp, #-8]
    // 0x8d5f00: StoreField: r0->field_27 = rZR
    //     0x8d5f00: stur            wzr, [x0, #0x27]
    // 0x8d5f04: LoadField: r1 = r0->field_23
    //     0x8d5f04: ldur            w1, [x0, #0x23]
    // 0x8d5f08: DecompressPointer r1
    //     0x8d5f08: add             x1, x1, HEAP, lsl #32
    // 0x8d5f0c: LoadField: r2 = r1->field_b
    //     0x8d5f0c: ldur            w2, [x1, #0xb]
    // 0x8d5f10: r3 = LoadInt32Instr(r2)
    //     0x8d5f10: sbfx            x3, x2, #1, #0x1f
    // 0x8d5f14: r2 = 0
    //     0x8d5f14: movz            x2, #0
    // 0x8d5f18: r5 = 0
    //     0x8d5f18: movz            x5, #0
    // 0x8d5f1c: r0 = fillRange()
    //     0x8d5f1c: bl              #0x6de84c  ; [dart:collection] ListBase::fillRange
    // 0x8d5f20: ldur            x1, [fp, #-8]
    // 0x8d5f24: r0 = LoadClassIdInstr(r1)
    //     0x8d5f24: ldur            x0, [x1, #-1]
    //     0x8d5f28: ubfx            x0, x0, #0xc, #0x14
    // 0x8d5f2c: r0 = GDT[cid_x0 + 0x2367]()
    //     0x8d5f2c: movz            x17, #0x2367
    //     0x8d5f30: add             lr, x0, x17
    //     0x8d5f34: ldr             lr, [x21, lr, lsl #3]
    //     0x8d5f38: blr             lr
    // 0x8d5f3c: r0 = Null
    //     0x8d5f3c: mov             x0, NULL
    // 0x8d5f40: LeaveFrame
    //     0x8d5f40: mov             SP, fp
    //     0x8d5f44: ldp             fp, lr, [SP], #0x10
    // 0x8d5f48: ret
    //     0x8d5f48: ret             
    // 0x8d5f4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5f4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5f50: b               #0x8d5ec4
  }
  _ doFinal(/* No info */) {
    // ** addr: 0x8e6fc4, size: 0xac
    // 0x8e6fc4: EnterFrame
    //     0x8e6fc4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6fc8: mov             fp, SP
    // 0x8e6fcc: AllocStack(0x28)
    //     0x8e6fcc: sub             SP, SP, #0x28
    // 0x8e6fd0: SetupParameters(MD4FamilyDigest this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8e6fd0: stur            x1, [fp, #-0x10]
    //     0x8e6fd4: stur            x2, [fp, #-0x18]
    // 0x8e6fd8: CheckStackOverflow
    //     0x8e6fd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6fdc: cmp             SP, x16
    //     0x8e6fe0: b.ls            #0x8e7068
    // 0x8e6fe4: LoadField: r0 = r1->field_7
    //     0x8e6fe4: ldur            w0, [x1, #7]
    // 0x8e6fe8: DecompressPointer r0
    //     0x8e6fe8: add             x0, x0, HEAP, lsl #32
    // 0x8e6fec: stur            x0, [fp, #-8]
    // 0x8e6ff0: r0 = Register64()
    //     0x8e6ff0: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e6ff4: mov             x3, x0
    // 0x8e6ff8: r0 = Sentinel
    //     0x8e6ff8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e6ffc: stur            x3, [fp, #-0x20]
    // 0x8e7000: StoreField: r3->field_7 = r0
    //     0x8e7000: stur            w0, [x3, #7]
    // 0x8e7004: StoreField: r3->field_b = r0
    //     0x8e7004: stur            w0, [x3, #0xb]
    // 0x8e7008: str             NULL, [SP]
    // 0x8e700c: mov             x1, x3
    // 0x8e7010: ldur            x2, [fp, #-8]
    // 0x8e7014: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e7014: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e7018: r0 = set()
    //     0x8e7018: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e701c: ldur            x1, [fp, #-0x20]
    // 0x8e7020: r2 = 3
    //     0x8e7020: movz            x2, #0x3
    // 0x8e7024: r0 = shiftl()
    //     0x8e7024: bl              #0x8e1fe0  ; [package:pointycastle/src/ufixnum.dart] Register64::shiftl
    // 0x8e7028: ldur            x1, [fp, #-0x10]
    // 0x8e702c: r0 = _processPadding()
    //     0x8e702c: bl              #0x8e8e3c  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::_processPadding
    // 0x8e7030: ldur            x1, [fp, #-0x10]
    // 0x8e7034: ldur            x2, [fp, #-0x20]
    // 0x8e7038: r0 = _processLength()
    //     0x8e7038: bl              #0x8e8ba0  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::_processLength
    // 0x8e703c: ldur            x1, [fp, #-0x10]
    // 0x8e7040: r0 = _doProcessBlock()
    //     0x8e7040: bl              #0x8e73a8  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::_doProcessBlock
    // 0x8e7044: ldur            x1, [fp, #-0x10]
    // 0x8e7048: ldur            x2, [fp, #-0x18]
    // 0x8e704c: r0 = _packState()
    //     0x8e704c: bl              #0x8e7070  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::_packState
    // 0x8e7050: ldur            x1, [fp, #-0x10]
    // 0x8e7054: r0 = reset()
    //     0x8e7054: bl              #0x8d5ea4  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::reset
    // 0x8e7058: r0 = 20
    //     0x8e7058: movz            x0, #0x14
    // 0x8e705c: LeaveFrame
    //     0x8e705c: mov             SP, fp
    //     0x8e7060: ldp             fp, lr, [SP], #0x10
    // 0x8e7064: ret
    //     0x8e7064: ret             
    // 0x8e7068: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e7068: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e706c: b               #0x8e6fe4
  }
  _ _packState(/* No info */) {
    // ** addr: 0x8e7070, size: 0x1f4
    // 0x8e7070: EnterFrame
    //     0x8e7070: stp             fp, lr, [SP, #-0x10]!
    //     0x8e7074: mov             fp, SP
    // 0x8e7078: AllocStack(0x58)
    //     0x8e7078: sub             SP, SP, #0x58
    // 0x8e707c: SetupParameters(dynamic _ /* r2 => r2, fp-0x48 */)
    //     0x8e707c: stur            x2, [fp, #-0x48]
    // 0x8e7080: CheckStackOverflow
    //     0x8e7080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e7084: cmp             SP, x16
    //     0x8e7088: b.ls            #0x8e724c
    // 0x8e708c: ArrayLoad: r3 = r1[0]  ; List_8
    //     0x8e708c: ldur            x3, [x1, #0x17]
    // 0x8e7090: stur            x3, [fp, #-0x40]
    // 0x8e7094: LoadField: r4 = r1->field_1f
    //     0x8e7094: ldur            w4, [x1, #0x1f]
    // 0x8e7098: DecompressPointer r4
    //     0x8e7098: add             x4, x4, HEAP, lsl #32
    // 0x8e709c: stur            x4, [fp, #-0x38]
    // 0x8e70a0: LoadField: r0 = r4->field_b
    //     0x8e70a0: ldur            w0, [x4, #0xb]
    // 0x8e70a4: r5 = LoadInt32Instr(r0)
    //     0x8e70a4: sbfx            x5, x0, #1, #0x1f
    // 0x8e70a8: stur            x5, [fp, #-0x30]
    // 0x8e70ac: LoadField: r6 = r1->field_13
    //     0x8e70ac: ldur            w6, [x1, #0x13]
    // 0x8e70b0: DecompressPointer r6
    //     0x8e70b0: add             x6, x6, HEAP, lsl #32
    // 0x8e70b4: stur            x6, [fp, #-0x28]
    // 0x8e70b8: LoadField: r7 = r2->field_13
    //     0x8e70b8: ldur            w7, [x2, #0x13]
    // 0x8e70bc: stur            x7, [fp, #-0x20]
    // 0x8e70c0: r8 = 0
    //     0x8e70c0: movz            x8, #0
    // 0x8e70c4: stur            x8, [fp, #-0x18]
    // 0x8e70c8: CheckStackOverflow
    //     0x8e70c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e70cc: cmp             SP, x16
    //     0x8e70d0: b.ls            #0x8e7254
    // 0x8e70d4: cmp             x8, x3
    // 0x8e70d8: b.ge            #0x8e723c
    // 0x8e70dc: mov             x0, x5
    // 0x8e70e0: mov             x1, x8
    // 0x8e70e4: cmp             x1, x0
    // 0x8e70e8: b.hs            #0x8e725c
    // 0x8e70ec: ArrayLoad: r0 = r4[r8]  ; Unknown_4
    //     0x8e70ec: add             x16, x4, x8, lsl #2
    //     0x8e70f0: ldur            w0, [x16, #0xf]
    // 0x8e70f4: DecompressPointer r0
    //     0x8e70f4: add             x0, x0, HEAP, lsl #32
    // 0x8e70f8: stur            x0, [fp, #-0x10]
    // 0x8e70fc: lsl             x1, x8, #2
    // 0x8e7100: stur            x1, [fp, #-8]
    // 0x8e7104: r0 = _ByteBuffer()
    //     0x8e7104: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x8e7108: mov             x1, x0
    // 0x8e710c: ldur            x0, [fp, #-0x48]
    // 0x8e7110: StoreField: r1->field_7 = r0
    //     0x8e7110: stur            w0, [x1, #7]
    // 0x8e7114: ldur            x16, [fp, #-0x20]
    // 0x8e7118: stp             x16, xzr, [SP]
    // 0x8e711c: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x8e711c: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x8e7120: r0 = asByteData()
    //     0x8e7120: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x8e7124: mov             x2, x0
    // 0x8e7128: LoadField: r3 = r2->field_13
    //     0x8e7128: ldur            w3, [x2, #0x13]
    // 0x8e712c: r4 = LoadInt32Instr(r3)
    //     0x8e712c: sbfx            x4, x3, #1, #0x1f
    // 0x8e7130: sub             x0, x4, #3
    // 0x8e7134: ldur            x1, [fp, #-8]
    // 0x8e7138: cmp             x1, x0
    // 0x8e713c: b.hs            #0x8e7260
    // 0x8e7140: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x8e7140: ldur            w1, [x2, #0x17]
    // 0x8e7144: DecompressPointer r1
    //     0x8e7144: add             x1, x1, HEAP, lsl #32
    // 0x8e7148: LoadField: r3 = r2->field_1b
    //     0x8e7148: ldur            w3, [x2, #0x1b]
    // 0x8e714c: r2 = LoadInt32Instr(r3)
    //     0x8e714c: sbfx            x2, x3, #1, #0x1f
    // 0x8e7150: ldur            x3, [fp, #-8]
    // 0x8e7154: add             x4, x2, x3
    // 0x8e7158: ldur            x2, [fp, #-0x28]
    // 0x8e715c: r16 = Instance_Endian
    //     0x8e715c: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e7160: ldr             x16, [x16, #0x8b8]
    // 0x8e7164: cmp             w2, w16
    // 0x8e7168: b.ne            #0x8e719c
    // 0x8e716c: ldur            x3, [fp, #-0x10]
    // 0x8e7170: r5 = LoadInt32Instr(r3)
    //     0x8e7170: sbfx            x5, x3, #1, #0x1f
    //     0x8e7174: tbz             w3, #0, #0x8e717c
    //     0x8e7178: ldur            x5, [x3, #7]
    // 0x8e717c: mov             x8, x5
    // 0x8e7180: r0 = 4278255360
    //     0x8e7180: movz            x0, #0xff00
    //     0x8e7184: movk            x0, #0xff00, lsl #16
    // 0x8e7188: r7 = 16711935
    //     0x8e7188: movz            x7, #0xff
    //     0x8e718c: movk            x7, #0xff, lsl #16
    // 0x8e7190: r6 = 4294901760
    //     0x8e7190: orr             x6, xzr, #0xffff0000
    // 0x8e7194: r5 = 65535
    //     0x8e7194: orr             x5, xzr, #0xffff
    // 0x8e7198: b               #0x8e720c
    // 0x8e719c: ldur            x3, [fp, #-0x10]
    // 0x8e71a0: r0 = 4278255360
    //     0x8e71a0: movz            x0, #0xff00
    //     0x8e71a4: movk            x0, #0xff00, lsl #16
    // 0x8e71a8: r7 = 16711935
    //     0x8e71a8: movz            x7, #0xff
    //     0x8e71ac: movk            x7, #0xff, lsl #16
    // 0x8e71b0: r6 = 4294901760
    //     0x8e71b0: orr             x6, xzr, #0xffff0000
    // 0x8e71b4: r5 = 65535
    //     0x8e71b4: orr             x5, xzr, #0xffff
    // 0x8e71b8: r8 = LoadInt32Instr(r3)
    //     0x8e71b8: sbfx            x8, x3, #1, #0x1f
    //     0x8e71bc: tbz             w3, #0, #0x8e71c4
    //     0x8e71c0: ldur            x8, [x3, #7]
    // 0x8e71c4: and             x3, x8, x0
    // 0x8e71c8: ubfx            x3, x3, #0, #0x20
    // 0x8e71cc: asr             x9, x3, #8
    // 0x8e71d0: and             x3, x8, x7
    // 0x8e71d4: ubfx            x3, x3, #0, #0x20
    // 0x8e71d8: lsl             x8, x3, #8
    // 0x8e71dc: orr             x3, x9, x8
    // 0x8e71e0: mov             x8, x3
    // 0x8e71e4: ubfx            x8, x8, #0, #0x20
    // 0x8e71e8: and             x9, x8, x6
    // 0x8e71ec: ubfx            x9, x9, #0, #0x20
    // 0x8e71f0: asr             x8, x9, #0x10
    // 0x8e71f4: ubfx            x3, x3, #0, #0x20
    // 0x8e71f8: and             x9, x3, x5
    // 0x8e71fc: ubfx            x9, x9, #0, #0x20
    // 0x8e7200: lsl             x3, x9, #0x10
    // 0x8e7204: orr             x9, x8, x3
    // 0x8e7208: mov             x8, x9
    // 0x8e720c: ldur            x3, [fp, #-0x18]
    // 0x8e7210: ubfx            x8, x8, #0, #0x20
    // 0x8e7214: LoadField: r9 = r1->field_7
    //     0x8e7214: ldur            x9, [x1, #7]
    // 0x8e7218: str             w8, [x9, x4]
    // 0x8e721c: add             x8, x3, #1
    // 0x8e7220: mov             x6, x2
    // 0x8e7224: ldur            x2, [fp, #-0x48]
    // 0x8e7228: ldur            x3, [fp, #-0x40]
    // 0x8e722c: ldur            x4, [fp, #-0x38]
    // 0x8e7230: ldur            x7, [fp, #-0x20]
    // 0x8e7234: ldur            x5, [fp, #-0x30]
    // 0x8e7238: b               #0x8e70c4
    // 0x8e723c: r0 = Null
    //     0x8e723c: mov             x0, NULL
    // 0x8e7240: LeaveFrame
    //     0x8e7240: mov             SP, fp
    //     0x8e7244: ldp             fp, lr, [SP], #0x10
    // 0x8e7248: ret
    //     0x8e7248: ret             
    // 0x8e724c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e724c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e7250: b               #0x8e708c
    // 0x8e7254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e7254: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e7258: b               #0x8e70d4
    // 0x8e725c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e725c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e7260: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e7260: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _doProcessBlock(/* No info */) {
    // ** addr: 0x8e73a8, size: 0x60
    // 0x8e73a8: EnterFrame
    //     0x8e73a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e73ac: mov             fp, SP
    // 0x8e73b0: AllocStack(0x8)
    //     0x8e73b0: sub             SP, SP, #8
    // 0x8e73b4: SetupParameters(MD4FamilyDigest this /* r1 => r0, fp-0x8 */)
    //     0x8e73b4: mov             x0, x1
    //     0x8e73b8: stur            x1, [fp, #-8]
    // 0x8e73bc: CheckStackOverflow
    //     0x8e73bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e73c0: cmp             SP, x16
    //     0x8e73c4: b.ls            #0x8e7400
    // 0x8e73c8: mov             x1, x0
    // 0x8e73cc: r0 = processBlock()
    //     0x8e73cc: bl              #0x8e7408  ; [package:pointycastle/digests/sha1.dart] SHA1Digest::processBlock
    // 0x8e73d0: ldur            x0, [fp, #-8]
    // 0x8e73d4: StoreField: r0->field_27 = rZR
    //     0x8e73d4: stur            wzr, [x0, #0x27]
    // 0x8e73d8: LoadField: r1 = r0->field_23
    //     0x8e73d8: ldur            w1, [x0, #0x23]
    // 0x8e73dc: DecompressPointer r1
    //     0x8e73dc: add             x1, x1, HEAP, lsl #32
    // 0x8e73e0: r2 = 0
    //     0x8e73e0: movz            x2, #0
    // 0x8e73e4: r3 = 16
    //     0x8e73e4: movz            x3, #0x10
    // 0x8e73e8: r5 = 0
    //     0x8e73e8: movz            x5, #0
    // 0x8e73ec: r0 = fillRange()
    //     0x8e73ec: bl              #0x6de84c  ; [dart:collection] ListBase::fillRange
    // 0x8e73f0: r0 = Null
    //     0x8e73f0: mov             x0, NULL
    // 0x8e73f4: LeaveFrame
    //     0x8e73f4: mov             SP, fp
    //     0x8e73f8: ldp             fp, lr, [SP], #0x10
    // 0x8e73fc: ret
    //     0x8e73fc: ret             
    // 0x8e7400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e7400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e7404: b               #0x8e73c8
  }
  _ _processLength(/* No info */) {
    // ** addr: 0x8e8ba0, size: 0x29c
    // 0x8e8ba0: EnterFrame
    //     0x8e8ba0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e8ba4: mov             fp, SP
    // 0x8e8ba8: AllocStack(0x20)
    //     0x8e8ba8: sub             SP, SP, #0x20
    // 0x8e8bac: SetupParameters(MD4FamilyDigest this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8e8bac: mov             x0, x1
    //     0x8e8bb0: stur            x1, [fp, #-8]
    //     0x8e8bb4: stur            x2, [fp, #-0x10]
    // 0x8e8bb8: CheckStackOverflow
    //     0x8e8bb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e8bbc: cmp             SP, x16
    //     0x8e8bc0: b.ls            #0x8e8de8
    // 0x8e8bc4: LoadField: r1 = r0->field_27
    //     0x8e8bc4: ldur            w1, [x0, #0x27]
    // 0x8e8bc8: DecompressPointer r1
    //     0x8e8bc8: add             x1, x1, HEAP, lsl #32
    // 0x8e8bcc: r16 = Sentinel
    //     0x8e8bcc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e8bd0: cmp             w1, w16
    // 0x8e8bd4: b.eq            #0x8e8df0
    // 0x8e8bd8: r3 = LoadInt32Instr(r1)
    //     0x8e8bd8: sbfx            x3, x1, #1, #0x1f
    //     0x8e8bdc: tbz             w1, #0, #0x8e8be4
    //     0x8e8be0: ldur            x3, [x1, #7]
    // 0x8e8be4: cmp             x3, #0xe
    // 0x8e8be8: b.le            #0x8e8bf4
    // 0x8e8bec: mov             x1, x0
    // 0x8e8bf0: r0 = _doProcessBlock()
    //     0x8e8bf0: bl              #0x8e73a8  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::_doProcessBlock
    // 0x8e8bf4: ldur            x0, [fp, #-8]
    // 0x8e8bf8: LoadField: r3 = r0->field_13
    //     0x8e8bf8: ldur            w3, [x0, #0x13]
    // 0x8e8bfc: DecompressPointer r3
    //     0x8e8bfc: add             x3, x3, HEAP, lsl #32
    // 0x8e8c00: stur            x3, [fp, #-0x18]
    // 0x8e8c04: r16 = Instance_Endian
    //     0x8e8c04: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e8c08: ldr             x16, [x16, #0x8b8]
    // 0x8e8c0c: cmp             w3, w16
    // 0x8e8c10: b.ne            #0x8e8ccc
    // 0x8e8c14: ldur            x2, [fp, #-0x10]
    // 0x8e8c18: LoadField: r3 = r0->field_23
    //     0x8e8c18: ldur            w3, [x0, #0x23]
    // 0x8e8c1c: DecompressPointer r3
    //     0x8e8c1c: add             x3, x3, HEAP, lsl #32
    // 0x8e8c20: LoadField: r4 = r2->field_b
    //     0x8e8c20: ldur            w4, [x2, #0xb]
    // 0x8e8c24: DecompressPointer r4
    //     0x8e8c24: add             x4, x4, HEAP, lsl #32
    // 0x8e8c28: r16 = Sentinel
    //     0x8e8c28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e8c2c: cmp             w4, w16
    // 0x8e8c30: b.eq            #0x8e8dfc
    // 0x8e8c34: LoadField: r0 = r3->field_b
    //     0x8e8c34: ldur            w0, [x3, #0xb]
    // 0x8e8c38: r5 = LoadInt32Instr(r0)
    //     0x8e8c38: sbfx            x5, x0, #1, #0x1f
    // 0x8e8c3c: mov             x0, x5
    // 0x8e8c40: r1 = 14
    //     0x8e8c40: movz            x1, #0xe
    // 0x8e8c44: cmp             x1, x0
    // 0x8e8c48: b.hs            #0x8e8e08
    // 0x8e8c4c: mov             x1, x3
    // 0x8e8c50: mov             x0, x4
    // 0x8e8c54: ArrayStore: r1[14] = r0  ; List_4
    //     0x8e8c54: add             x25, x1, #0x47
    //     0x8e8c58: str             w0, [x25]
    //     0x8e8c5c: tbz             w0, #0, #0x8e8c78
    //     0x8e8c60: ldurb           w16, [x1, #-1]
    //     0x8e8c64: ldurb           w17, [x0, #-1]
    //     0x8e8c68: and             x16, x17, x16, lsr #2
    //     0x8e8c6c: tst             x16, HEAP, lsr #32
    //     0x8e8c70: b.eq            #0x8e8c78
    //     0x8e8c74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e8c78: LoadField: r4 = r2->field_7
    //     0x8e8c78: ldur            w4, [x2, #7]
    // 0x8e8c7c: DecompressPointer r4
    //     0x8e8c7c: add             x4, x4, HEAP, lsl #32
    // 0x8e8c80: r16 = Sentinel
    //     0x8e8c80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e8c84: cmp             w4, w16
    // 0x8e8c88: b.eq            #0x8e8e0c
    // 0x8e8c8c: mov             x0, x5
    // 0x8e8c90: r1 = 15
    //     0x8e8c90: movz            x1, #0xf
    // 0x8e8c94: cmp             x1, x0
    // 0x8e8c98: b.hs            #0x8e8e18
    // 0x8e8c9c: mov             x1, x3
    // 0x8e8ca0: mov             x0, x4
    // 0x8e8ca4: ArrayStore: r1[15] = r0  ; List_4
    //     0x8e8ca4: add             x25, x1, #0x4b
    //     0x8e8ca8: str             w0, [x25]
    //     0x8e8cac: tbz             w0, #0, #0x8e8cc8
    //     0x8e8cb0: ldurb           w16, [x1, #-1]
    //     0x8e8cb4: ldurb           w17, [x0, #-1]
    //     0x8e8cb8: and             x16, x17, x16, lsr #2
    //     0x8e8cbc: tst             x16, HEAP, lsr #32
    //     0x8e8cc0: b.eq            #0x8e8cc8
    //     0x8e8cc4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e8cc8: b               #0x8e8d90
    // 0x8e8ccc: ldur            x2, [fp, #-0x10]
    // 0x8e8cd0: r16 = Instance_Endian
    //     0x8e8cd0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12390] Obj!Endian@e2cbb1
    //     0x8e8cd4: ldr             x16, [x16, #0x390]
    // 0x8e8cd8: cmp             w3, w16
    // 0x8e8cdc: b.ne            #0x8e8da0
    // 0x8e8ce0: LoadField: r3 = r0->field_23
    //     0x8e8ce0: ldur            w3, [x0, #0x23]
    // 0x8e8ce4: DecompressPointer r3
    //     0x8e8ce4: add             x3, x3, HEAP, lsl #32
    // 0x8e8ce8: LoadField: r4 = r2->field_7
    //     0x8e8ce8: ldur            w4, [x2, #7]
    // 0x8e8cec: DecompressPointer r4
    //     0x8e8cec: add             x4, x4, HEAP, lsl #32
    // 0x8e8cf0: r16 = Sentinel
    //     0x8e8cf0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e8cf4: cmp             w4, w16
    // 0x8e8cf8: b.eq            #0x8e8e1c
    // 0x8e8cfc: LoadField: r0 = r3->field_b
    //     0x8e8cfc: ldur            w0, [x3, #0xb]
    // 0x8e8d00: r5 = LoadInt32Instr(r0)
    //     0x8e8d00: sbfx            x5, x0, #1, #0x1f
    // 0x8e8d04: mov             x0, x5
    // 0x8e8d08: r1 = 14
    //     0x8e8d08: movz            x1, #0xe
    // 0x8e8d0c: cmp             x1, x0
    // 0x8e8d10: b.hs            #0x8e8e28
    // 0x8e8d14: mov             x1, x3
    // 0x8e8d18: mov             x0, x4
    // 0x8e8d1c: ArrayStore: r1[14] = r0  ; List_4
    //     0x8e8d1c: add             x25, x1, #0x47
    //     0x8e8d20: str             w0, [x25]
    //     0x8e8d24: tbz             w0, #0, #0x8e8d40
    //     0x8e8d28: ldurb           w16, [x1, #-1]
    //     0x8e8d2c: ldurb           w17, [x0, #-1]
    //     0x8e8d30: and             x16, x17, x16, lsr #2
    //     0x8e8d34: tst             x16, HEAP, lsr #32
    //     0x8e8d38: b.eq            #0x8e8d40
    //     0x8e8d3c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e8d40: LoadField: r4 = r2->field_b
    //     0x8e8d40: ldur            w4, [x2, #0xb]
    // 0x8e8d44: DecompressPointer r4
    //     0x8e8d44: add             x4, x4, HEAP, lsl #32
    // 0x8e8d48: r16 = Sentinel
    //     0x8e8d48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e8d4c: cmp             w4, w16
    // 0x8e8d50: b.eq            #0x8e8e2c
    // 0x8e8d54: mov             x0, x5
    // 0x8e8d58: r1 = 15
    //     0x8e8d58: movz            x1, #0xf
    // 0x8e8d5c: cmp             x1, x0
    // 0x8e8d60: b.hs            #0x8e8e38
    // 0x8e8d64: mov             x1, x3
    // 0x8e8d68: mov             x0, x4
    // 0x8e8d6c: ArrayStore: r1[15] = r0  ; List_4
    //     0x8e8d6c: add             x25, x1, #0x4b
    //     0x8e8d70: str             w0, [x25]
    //     0x8e8d74: tbz             w0, #0, #0x8e8d90
    //     0x8e8d78: ldurb           w16, [x1, #-1]
    //     0x8e8d7c: ldurb           w17, [x0, #-1]
    //     0x8e8d80: and             x16, x17, x16, lsr #2
    //     0x8e8d84: tst             x16, HEAP, lsr #32
    //     0x8e8d88: b.eq            #0x8e8d90
    //     0x8e8d8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e8d90: r0 = Null
    //     0x8e8d90: mov             x0, NULL
    // 0x8e8d94: LeaveFrame
    //     0x8e8d94: mov             SP, fp
    //     0x8e8d98: ldp             fp, lr, [SP], #0x10
    // 0x8e8d9c: ret
    //     0x8e8d9c: ret             
    // 0x8e8da0: r1 = Null
    //     0x8e8da0: mov             x1, NULL
    // 0x8e8da4: r2 = 4
    //     0x8e8da4: movz            x2, #0x4
    // 0x8e8da8: r0 = AllocateArray()
    //     0x8e8da8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8e8dac: r16 = "Invalid endianness: "
    //     0x8e8dac: add             x16, PP, #0x19, lsl #12  ; [pp+0x19c38] "Invalid endianness: "
    //     0x8e8db0: ldr             x16, [x16, #0xc38]
    // 0x8e8db4: StoreField: r0->field_f = r16
    //     0x8e8db4: stur            w16, [x0, #0xf]
    // 0x8e8db8: ldur            x1, [fp, #-0x18]
    // 0x8e8dbc: StoreField: r0->field_13 = r1
    //     0x8e8dbc: stur            w1, [x0, #0x13]
    // 0x8e8dc0: str             x0, [SP]
    // 0x8e8dc4: r0 = _interpolate()
    //     0x8e8dc4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8e8dc8: stur            x0, [fp, #-8]
    // 0x8e8dcc: r0 = StateError()
    //     0x8e8dcc: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8e8dd0: mov             x1, x0
    // 0x8e8dd4: ldur            x0, [fp, #-8]
    // 0x8e8dd8: StoreField: r1->field_b = r0
    //     0x8e8dd8: stur            w0, [x1, #0xb]
    // 0x8e8ddc: mov             x0, x1
    // 0x8e8de0: r0 = Throw()
    //     0x8e8de0: bl              #0xec04b8  ; ThrowStub
    // 0x8e8de4: brk             #0
    // 0x8e8de8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8de8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8dec: b               #0x8e8bc4
    // 0x8e8df0: r9 = bufferOffset
    //     0x8e8df0: add             x9, PP, #0x19, lsl #12  ; [pp+0x19c40] Field <MD4FamilyDigest.bufferOffset>: late (offset: 0x28)
    //     0x8e8df4: ldr             x9, [x9, #0xc40]
    // 0x8e8df8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e8df8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e8dfc: r9 = _lo32
    //     0x8e8dfc: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8e8e00: ldr             x9, [x9, #0x3a8]
    // 0x8e8e04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e8e04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e8e08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8e08: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8e0c: r9 = _hi32
    //     0x8e8e0c: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8e8e10: ldr             x9, [x9, #0x3a0]
    // 0x8e8e14: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e8e14: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e8e18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8e18: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8e1c: r9 = _hi32
    //     0x8e8e1c: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8e8e20: ldr             x9, [x9, #0x3a0]
    // 0x8e8e24: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e8e24: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e8e28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8e28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e8e2c: r9 = _lo32
    //     0x8e8e2c: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8e8e30: ldr             x9, [x9, #0x3a8]
    // 0x8e8e34: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e8e34: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e8e38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8e38: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _processPadding(/* No info */) {
    // ** addr: 0x8e8e3c, size: 0x12c
    // 0x8e8e3c: EnterFrame
    //     0x8e8e3c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e8e40: mov             fp, SP
    // 0x8e8e44: AllocStack(0x20)
    //     0x8e8e44: sub             SP, SP, #0x20
    // 0x8e8e48: SetupParameters(MD4FamilyDigest this /* r1 => r0, fp-0x8 */)
    //     0x8e8e48: mov             x0, x1
    //     0x8e8e4c: stur            x1, [fp, #-8]
    // 0x8e8e50: CheckStackOverflow
    //     0x8e8e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e8e54: cmp             SP, x16
    //     0x8e8e58: b.ls            #0x8e8f48
    // 0x8e8e5c: mov             x1, x0
    // 0x8e8e60: r2 = 128
    //     0x8e8e60: movz            x2, #0x80
    // 0x8e8e64: r0 = updateByte()
    //     0x8e8e64: bl              #0x8e9124  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::updateByte
    // 0x8e8e68: ldur            x2, [fp, #-8]
    // 0x8e8e6c: LoadField: r3 = r2->field_b
    //     0x8e8e6c: ldur            w3, [x2, #0xb]
    // 0x8e8e70: DecompressPointer r3
    //     0x8e8e70: add             x3, x3, HEAP, lsl #32
    // 0x8e8e74: stur            x3, [fp, #-0x20]
    // 0x8e8e78: LoadField: r0 = r3->field_13
    //     0x8e8e78: ldur            w0, [x3, #0x13]
    // 0x8e8e7c: r4 = LoadInt32Instr(r0)
    //     0x8e8e7c: sbfx            x4, x0, #1, #0x1f
    // 0x8e8e80: stur            x4, [fp, #-0x18]
    // 0x8e8e84: LoadField: r5 = r2->field_7
    //     0x8e8e84: ldur            w5, [x2, #7]
    // 0x8e8e88: DecompressPointer r5
    //     0x8e8e88: add             x5, x5, HEAP, lsl #32
    // 0x8e8e8c: stur            x5, [fp, #-0x10]
    // 0x8e8e90: CheckStackOverflow
    //     0x8e8e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e8e94: cmp             SP, x16
    //     0x8e8e98: b.ls            #0x8e8f50
    // 0x8e8e9c: LoadField: r0 = r2->field_f
    //     0x8e8e9c: ldur            w0, [x2, #0xf]
    // 0x8e8ea0: DecompressPointer r0
    //     0x8e8ea0: add             x0, x0, HEAP, lsl #32
    // 0x8e8ea4: r16 = Sentinel
    //     0x8e8ea4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e8ea8: cmp             w0, w16
    // 0x8e8eac: b.eq            #0x8e8f58
    // 0x8e8eb0: r6 = LoadInt32Instr(r0)
    //     0x8e8eb0: sbfx            x6, x0, #1, #0x1f
    //     0x8e8eb4: tbz             w0, #0, #0x8e8ebc
    //     0x8e8eb8: ldur            x6, [x0, #7]
    // 0x8e8ebc: cbz             x6, #0x8e8f38
    // 0x8e8ec0: add             x7, x6, #1
    // 0x8e8ec4: r0 = BoxInt64Instr(r7)
    //     0x8e8ec4: sbfiz           x0, x7, #1, #0x1f
    //     0x8e8ec8: cmp             x7, x0, asr #1
    //     0x8e8ecc: b.eq            #0x8e8ed8
    //     0x8e8ed0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e8ed4: stur            x7, [x0, #7]
    // 0x8e8ed8: StoreField: r2->field_f = r0
    //     0x8e8ed8: stur            w0, [x2, #0xf]
    //     0x8e8edc: tbz             w0, #0, #0x8e8ef8
    //     0x8e8ee0: ldurb           w16, [x2, #-1]
    //     0x8e8ee4: ldurb           w17, [x0, #-1]
    //     0x8e8ee8: and             x16, x17, x16, lsr #2
    //     0x8e8eec: tst             x16, HEAP, lsr #32
    //     0x8e8ef0: b.eq            #0x8e8ef8
    //     0x8e8ef4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8e8ef8: mov             x0, x4
    // 0x8e8efc: mov             x1, x6
    // 0x8e8f00: cmp             x1, x0
    // 0x8e8f04: b.hs            #0x8e8f64
    // 0x8e8f08: ArrayStore: r3[r6] = rZR  ; TypeUnknown_1
    //     0x8e8f08: add             x0, x3, x6
    //     0x8e8f0c: strb            wzr, [x0, #0x17]
    // 0x8e8f10: mov             x1, x2
    // 0x8e8f14: r0 = _processWordIfBufferFull()
    //     0x8e8f14: bl              #0x8e8f68  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::_processWordIfBufferFull
    // 0x8e8f18: ldur            x1, [fp, #-0x10]
    // 0x8e8f1c: r2 = 2
    //     0x8e8f1c: movz            x2, #0x2
    // 0x8e8f20: r0 = sum()
    //     0x8e8f20: bl              #0x8dec78  ; [package:pointycastle/src/ufixnum.dart] Register64::sum
    // 0x8e8f24: ldur            x2, [fp, #-8]
    // 0x8e8f28: ldur            x3, [fp, #-0x20]
    // 0x8e8f2c: ldur            x5, [fp, #-0x10]
    // 0x8e8f30: ldur            x4, [fp, #-0x18]
    // 0x8e8f34: b               #0x8e8e90
    // 0x8e8f38: r0 = Null
    //     0x8e8f38: mov             x0, NULL
    // 0x8e8f3c: LeaveFrame
    //     0x8e8f3c: mov             SP, fp
    //     0x8e8f40: ldp             fp, lr, [SP], #0x10
    // 0x8e8f44: ret
    //     0x8e8f44: ret             
    // 0x8e8f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8f48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8f4c: b               #0x8e8e5c
    // 0x8e8f50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8f50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8f54: b               #0x8e8e9c
    // 0x8e8f58: r9 = _wordBufferOffset
    //     0x8e8f58: add             x9, PP, #0x19, lsl #12  ; [pp+0x19c48] Field <MD4FamilyDigest._wordBufferOffset@2664461525>: late (offset: 0x10)
    //     0x8e8f5c: ldr             x9, [x9, #0xc48]
    // 0x8e8f60: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e8f60: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e8f64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e8f64: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _processWordIfBufferFull(/* No info */) {
    // ** addr: 0x8e8f68, size: 0x7c
    // 0x8e8f68: EnterFrame
    //     0x8e8f68: stp             fp, lr, [SP, #-0x10]!
    //     0x8e8f6c: mov             fp, SP
    // 0x8e8f70: AllocStack(0x8)
    //     0x8e8f70: sub             SP, SP, #8
    // 0x8e8f74: SetupParameters(MD4FamilyDigest this /* r1 => r0, fp-0x8 */)
    //     0x8e8f74: mov             x0, x1
    //     0x8e8f78: stur            x1, [fp, #-8]
    // 0x8e8f7c: CheckStackOverflow
    //     0x8e8f7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e8f80: cmp             SP, x16
    //     0x8e8f84: b.ls            #0x8e8fd0
    // 0x8e8f88: LoadField: r1 = r0->field_f
    //     0x8e8f88: ldur            w1, [x0, #0xf]
    // 0x8e8f8c: DecompressPointer r1
    //     0x8e8f8c: add             x1, x1, HEAP, lsl #32
    // 0x8e8f90: r16 = Sentinel
    //     0x8e8f90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e8f94: cmp             w1, w16
    // 0x8e8f98: b.eq            #0x8e8fd8
    // 0x8e8f9c: LoadField: r2 = r0->field_b
    //     0x8e8f9c: ldur            w2, [x0, #0xb]
    // 0x8e8fa0: DecompressPointer r2
    //     0x8e8fa0: add             x2, x2, HEAP, lsl #32
    // 0x8e8fa4: LoadField: r3 = r2->field_13
    //     0x8e8fa4: ldur            w3, [x2, #0x13]
    // 0x8e8fa8: cmp             w1, w3
    // 0x8e8fac: b.ne            #0x8e8fc0
    // 0x8e8fb0: mov             x1, x0
    // 0x8e8fb4: r0 = _processWord()
    //     0x8e8fb4: bl              #0x8e8fe4  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::_processWord
    // 0x8e8fb8: ldur            x1, [fp, #-8]
    // 0x8e8fbc: StoreField: r1->field_f = rZR
    //     0x8e8fbc: stur            wzr, [x1, #0xf]
    // 0x8e8fc0: r0 = Null
    //     0x8e8fc0: mov             x0, NULL
    // 0x8e8fc4: LeaveFrame
    //     0x8e8fc4: mov             SP, fp
    //     0x8e8fc8: ldp             fp, lr, [SP], #0x10
    // 0x8e8fcc: ret
    //     0x8e8fcc: ret             
    // 0x8e8fd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8fd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e8fd4: b               #0x8e8f88
    // 0x8e8fd8: r9 = _wordBufferOffset
    //     0x8e8fd8: add             x9, PP, #0x19, lsl #12  ; [pp+0x19c48] Field <MD4FamilyDigest._wordBufferOffset@2664461525>: late (offset: 0x10)
    //     0x8e8fdc: ldr             x9, [x9, #0xc48]
    // 0x8e8fe0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e8fe0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _processWord(/* No info */) {
    // ** addr: 0x8e8fe4, size: 0x140
    // 0x8e8fe4: EnterFrame
    //     0x8e8fe4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e8fe8: mov             fp, SP
    // 0x8e8fec: AllocStack(0x18)
    //     0x8e8fec: sub             SP, SP, #0x18
    // 0x8e8ff0: SetupParameters(MD4FamilyDigest this /* r1 => r4, fp-0x18 */)
    //     0x8e8ff0: mov             x4, x1
    //     0x8e8ff4: stur            x1, [fp, #-0x18]
    // 0x8e8ff8: CheckStackOverflow
    //     0x8e8ff8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e8ffc: cmp             SP, x16
    //     0x8e9000: b.ls            #0x8e910c
    // 0x8e9004: LoadField: r5 = r4->field_23
    //     0x8e9004: ldur            w5, [x4, #0x23]
    // 0x8e9008: DecompressPointer r5
    //     0x8e9008: add             x5, x5, HEAP, lsl #32
    // 0x8e900c: stur            x5, [fp, #-0x10]
    // 0x8e9010: LoadField: r0 = r4->field_27
    //     0x8e9010: ldur            w0, [x4, #0x27]
    // 0x8e9014: DecompressPointer r0
    //     0x8e9014: add             x0, x0, HEAP, lsl #32
    // 0x8e9018: r16 = Sentinel
    //     0x8e9018: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e901c: cmp             w0, w16
    // 0x8e9020: b.eq            #0x8e9114
    // 0x8e9024: r6 = LoadInt32Instr(r0)
    //     0x8e9024: sbfx            x6, x0, #1, #0x1f
    //     0x8e9028: tbz             w0, #0, #0x8e9030
    //     0x8e902c: ldur            x6, [x0, #7]
    // 0x8e9030: stur            x6, [fp, #-8]
    // 0x8e9034: add             x3, x6, #1
    // 0x8e9038: r0 = BoxInt64Instr(r3)
    //     0x8e9038: sbfiz           x0, x3, #1, #0x1f
    //     0x8e903c: cmp             x3, x0, asr #1
    //     0x8e9040: b.eq            #0x8e904c
    //     0x8e9044: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e9048: stur            x3, [x0, #7]
    // 0x8e904c: StoreField: r4->field_27 = r0
    //     0x8e904c: stur            w0, [x4, #0x27]
    //     0x8e9050: tbz             w0, #0, #0x8e906c
    //     0x8e9054: ldurb           w16, [x4, #-1]
    //     0x8e9058: ldurb           w17, [x0, #-1]
    //     0x8e905c: and             x16, x17, x16, lsr #2
    //     0x8e9060: tst             x16, HEAP, lsr #32
    //     0x8e9064: b.eq            #0x8e906c
    //     0x8e9068: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8e906c: LoadField: r3 = r4->field_13
    //     0x8e906c: ldur            w3, [x4, #0x13]
    // 0x8e9070: DecompressPointer r3
    //     0x8e9070: add             x3, x3, HEAP, lsl #32
    // 0x8e9074: mov             x1, x2
    // 0x8e9078: r2 = 0
    //     0x8e9078: movz            x2, #0
    // 0x8e907c: r0 = unpack32()
    //     0x8e907c: bl              #0x8e258c  ; [package:pointycastle/src/ufixnum.dart] ::unpack32
    // 0x8e9080: mov             x3, x0
    // 0x8e9084: ldur            x2, [fp, #-0x10]
    // 0x8e9088: LoadField: r0 = r2->field_b
    //     0x8e9088: ldur            w0, [x2, #0xb]
    // 0x8e908c: r1 = LoadInt32Instr(r0)
    //     0x8e908c: sbfx            x1, x0, #1, #0x1f
    // 0x8e9090: mov             x0, x1
    // 0x8e9094: ldur            x1, [fp, #-8]
    // 0x8e9098: cmp             x1, x0
    // 0x8e909c: b.hs            #0x8e9120
    // 0x8e90a0: r0 = BoxInt64Instr(r3)
    //     0x8e90a0: sbfiz           x0, x3, #1, #0x1f
    //     0x8e90a4: cmp             x3, x0, asr #1
    //     0x8e90a8: b.eq            #0x8e90b4
    //     0x8e90ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e90b0: stur            x3, [x0, #7]
    // 0x8e90b4: mov             x1, x2
    // 0x8e90b8: ldur            x2, [fp, #-8]
    // 0x8e90bc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8e90bc: add             x25, x1, x2, lsl #2
    //     0x8e90c0: add             x25, x25, #0xf
    //     0x8e90c4: str             w0, [x25]
    //     0x8e90c8: tbz             w0, #0, #0x8e90e4
    //     0x8e90cc: ldurb           w16, [x1, #-1]
    //     0x8e90d0: ldurb           w17, [x0, #-1]
    //     0x8e90d4: and             x16, x17, x16, lsr #2
    //     0x8e90d8: tst             x16, HEAP, lsr #32
    //     0x8e90dc: b.eq            #0x8e90e4
    //     0x8e90e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e90e4: ldur            x1, [fp, #-0x18]
    // 0x8e90e8: LoadField: r0 = r1->field_27
    //     0x8e90e8: ldur            w0, [x1, #0x27]
    // 0x8e90ec: DecompressPointer r0
    //     0x8e90ec: add             x0, x0, HEAP, lsl #32
    // 0x8e90f0: cmp             w0, #0x20
    // 0x8e90f4: b.ne            #0x8e90fc
    // 0x8e90f8: r0 = _doProcessBlock()
    //     0x8e90f8: bl              #0x8e73a8  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::_doProcessBlock
    // 0x8e90fc: r0 = Null
    //     0x8e90fc: mov             x0, NULL
    // 0x8e9100: LeaveFrame
    //     0x8e9100: mov             SP, fp
    //     0x8e9104: ldp             fp, lr, [SP], #0x10
    // 0x8e9108: ret
    //     0x8e9108: ret             
    // 0x8e910c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e910c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e9110: b               #0x8e9004
    // 0x8e9114: r9 = bufferOffset
    //     0x8e9114: add             x9, PP, #0x19, lsl #12  ; [pp+0x19c40] Field <MD4FamilyDigest.bufferOffset>: late (offset: 0x28)
    //     0x8e9118: ldr             x9, [x9, #0xc40]
    // 0x8e911c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e911c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e9120: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e9120: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ updateByte(/* No info */) {
    // ** addr: 0x8e9124, size: 0xf4
    // 0x8e9124: EnterFrame
    //     0x8e9124: stp             fp, lr, [SP, #-0x10]!
    //     0x8e9128: mov             fp, SP
    // 0x8e912c: AllocStack(0x8)
    //     0x8e912c: sub             SP, SP, #8
    // 0x8e9130: r3 = 255
    //     0x8e9130: movz            x3, #0xff
    // 0x8e9134: mov             x4, x1
    // 0x8e9138: stur            x1, [fp, #-8]
    // 0x8e913c: CheckStackOverflow
    //     0x8e913c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e9140: cmp             SP, x16
    //     0x8e9144: b.ls            #0x8e9200
    // 0x8e9148: LoadField: r5 = r4->field_b
    //     0x8e9148: ldur            w5, [x4, #0xb]
    // 0x8e914c: DecompressPointer r5
    //     0x8e914c: add             x5, x5, HEAP, lsl #32
    // 0x8e9150: LoadField: r0 = r4->field_f
    //     0x8e9150: ldur            w0, [x4, #0xf]
    // 0x8e9154: DecompressPointer r0
    //     0x8e9154: add             x0, x0, HEAP, lsl #32
    // 0x8e9158: r16 = Sentinel
    //     0x8e9158: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e915c: cmp             w0, w16
    // 0x8e9160: b.eq            #0x8e9208
    // 0x8e9164: r6 = LoadInt32Instr(r0)
    //     0x8e9164: sbfx            x6, x0, #1, #0x1f
    //     0x8e9168: tbz             w0, #0, #0x8e9170
    //     0x8e916c: ldur            x6, [x0, #7]
    // 0x8e9170: add             x7, x6, #1
    // 0x8e9174: r0 = BoxInt64Instr(r7)
    //     0x8e9174: sbfiz           x0, x7, #1, #0x1f
    //     0x8e9178: cmp             x7, x0, asr #1
    //     0x8e917c: b.eq            #0x8e9188
    //     0x8e9180: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e9184: stur            x7, [x0, #7]
    // 0x8e9188: StoreField: r4->field_f = r0
    //     0x8e9188: stur            w0, [x4, #0xf]
    //     0x8e918c: tbz             w0, #0, #0x8e91a8
    //     0x8e9190: ldurb           w16, [x4, #-1]
    //     0x8e9194: ldurb           w17, [x0, #-1]
    //     0x8e9198: and             x16, x17, x16, lsr #2
    //     0x8e919c: tst             x16, HEAP, lsr #32
    //     0x8e91a0: b.eq            #0x8e91a8
    //     0x8e91a4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8e91a8: ubfx            x2, x2, #0, #0x20
    // 0x8e91ac: and             x7, x2, x3
    // 0x8e91b0: LoadField: r0 = r5->field_13
    //     0x8e91b0: ldur            w0, [x5, #0x13]
    // 0x8e91b4: r1 = LoadInt32Instr(r0)
    //     0x8e91b4: sbfx            x1, x0, #1, #0x1f
    // 0x8e91b8: mov             x0, x1
    // 0x8e91bc: mov             x1, x6
    // 0x8e91c0: cmp             x1, x0
    // 0x8e91c4: b.hs            #0x8e9214
    // 0x8e91c8: ubfx            x7, x7, #0, #0x20
    // 0x8e91cc: ArrayStore: r5[r6] = r7  ; TypeUnknown_1
    //     0x8e91cc: add             x0, x5, x6
    //     0x8e91d0: strb            w7, [x0, #0x17]
    // 0x8e91d4: mov             x1, x4
    // 0x8e91d8: r0 = _processWordIfBufferFull()
    //     0x8e91d8: bl              #0x8e8f68  ; [package:pointycastle/src/impl/md4_family_digest.dart] MD4FamilyDigest::_processWordIfBufferFull
    // 0x8e91dc: ldur            x0, [fp, #-8]
    // 0x8e91e0: LoadField: r1 = r0->field_7
    //     0x8e91e0: ldur            w1, [x0, #7]
    // 0x8e91e4: DecompressPointer r1
    //     0x8e91e4: add             x1, x1, HEAP, lsl #32
    // 0x8e91e8: r2 = 2
    //     0x8e91e8: movz            x2, #0x2
    // 0x8e91ec: r0 = sum()
    //     0x8e91ec: bl              #0x8dec78  ; [package:pointycastle/src/ufixnum.dart] Register64::sum
    // 0x8e91f0: r0 = Null
    //     0x8e91f0: mov             x0, NULL
    // 0x8e91f4: LeaveFrame
    //     0x8e91f4: mov             SP, fp
    //     0x8e91f8: ldp             fp, lr, [SP], #0x10
    // 0x8e91fc: ret
    //     0x8e91fc: ret             
    // 0x8e9200: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e9200: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e9204: b               #0x8e9148
    // 0x8e9208: r9 = _wordBufferOffset
    //     0x8e9208: add             x9, PP, #0x19, lsl #12  ; [pp+0x19c48] Field <MD4FamilyDigest._wordBufferOffset@2664461525>: late (offset: 0x10)
    //     0x8e920c: ldr             x9, [x9, #0xc48]
    // 0x8e9210: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e9210: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e9214: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e9214: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
