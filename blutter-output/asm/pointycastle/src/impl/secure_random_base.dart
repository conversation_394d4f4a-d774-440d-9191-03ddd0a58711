// lib: , url: package:pointycastle/src/impl/secure_random_base.dart

// class id: 1051043, size: 0x8
class :: {
}

// class id: 567, size: 0x8, field offset: 0x8
abstract class SecureRandomBase extends Object
    implements SecureRandom {

  _ nextBigInteger(/* No info */) {
    // ** addr: 0xeb6ab8, size: 0x34
    // 0xeb6ab8: EnterFrame
    //     0xeb6ab8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6abc: mov             fp, SP
    // 0xeb6ac0: CheckStackOverflow
    //     0xeb6ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6ac4: cmp             SP, x16
    //     0xeb6ac8: b.ls            #0xeb6ae4
    // 0xeb6acc: r0 = _randomBits()
    //     0xeb6acc: bl              #0xeb6aec  ; [package:pointycastle/src/impl/secure_random_base.dart] SecureRandomBase::_randomBits
    // 0xeb6ad0: mov             x1, x0
    // 0xeb6ad4: r0 = decodeBigIntWithSign()
    //     0xeb6ad4: bl              #0x8d0614  ; [package:pointycastle/src/utils.dart] ::decodeBigIntWithSign
    // 0xeb6ad8: LeaveFrame
    //     0xeb6ad8: mov             SP, fp
    //     0xeb6adc: ldp             fp, lr, [SP], #0x10
    // 0xeb6ae0: ret
    //     0xeb6ae0: ret             
    // 0xeb6ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6ae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6ae8: b               #0xeb6acc
  }
  _ _randomBits(/* No info */) {
    // ** addr: 0xeb6aec, size: 0x164
    // 0xeb6aec: EnterFrame
    //     0xeb6aec: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6af0: mov             fp, SP
    // 0xeb6af4: AllocStack(0x28)
    //     0xeb6af4: sub             SP, SP, #0x28
    // 0xeb6af8: SetupParameters(SecureRandomBase this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xeb6af8: mov             x3, x1
    //     0xeb6afc: stur            x1, [fp, #-0x10]
    //     0xeb6b00: stur            x2, [fp, #-0x18]
    // 0xeb6b04: CheckStackOverflow
    //     0xeb6b04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6b08: cmp             SP, x16
    //     0xeb6b0c: b.ls            #0xeb6c1c
    // 0xeb6b10: tbnz            x2, #0x3f, #0xeb6bf4
    // 0xeb6b14: r5 = 8
    //     0xeb6b14: movz            x5, #0x8
    // 0xeb6b18: add             x0, x2, #7
    // 0xeb6b1c: sdiv            x6, x0, x5
    // 0xeb6b20: stur            x6, [fp, #-8]
    // 0xeb6b24: r0 = BoxInt64Instr(r6)
    //     0xeb6b24: sbfiz           x0, x6, #1, #0x1f
    //     0xeb6b28: cmp             x6, x0, asr #1
    //     0xeb6b2c: b.eq            #0xeb6b38
    //     0xeb6b30: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb6b34: stur            x6, [x0, #7]
    // 0xeb6b38: mov             x4, x0
    // 0xeb6b3c: r0 = AllocateUint8Array()
    //     0xeb6b3c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xeb6b40: mov             x2, x0
    // 0xeb6b44: ldur            x0, [fp, #-8]
    // 0xeb6b48: stur            x2, [fp, #-0x28]
    // 0xeb6b4c: cmp             x0, #0
    // 0xeb6b50: b.le            #0xeb6be4
    // 0xeb6b54: r3 = 0
    //     0xeb6b54: movz            x3, #0
    // 0xeb6b58: stur            x3, [fp, #-0x20]
    // 0xeb6b5c: CheckStackOverflow
    //     0xeb6b5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6b60: cmp             SP, x16
    //     0xeb6b64: b.ls            #0xeb6c24
    // 0xeb6b68: cmp             x3, x0
    // 0xeb6b6c: b.ge            #0xeb6b98
    // 0xeb6b70: ldur            x1, [fp, #-0x10]
    // 0xeb6b74: r0 = nextUint8()
    //     0xeb6b74: bl              #0xeb6c50  ; [package:pointycastle/random/block_ctr_random.dart] BlockCtrRandom::nextUint8
    // 0xeb6b78: mov             x1, x0
    // 0xeb6b7c: ldur            x0, [fp, #-0x20]
    // 0xeb6b80: ldur            x2, [fp, #-0x28]
    // 0xeb6b84: ArrayStore: r2[r0] = r1  ; TypeUnknown_1
    //     0xeb6b84: add             x3, x2, x0
    //     0xeb6b88: strb            w1, [x3, #0x17]
    // 0xeb6b8c: add             x3, x0, #1
    // 0xeb6b90: ldur            x0, [fp, #-8]
    // 0xeb6b94: b               #0xeb6b58
    // 0xeb6b98: ldur            x1, [fp, #-0x18]
    // 0xeb6b9c: r3 = 8
    //     0xeb6b9c: movz            x3, #0x8
    // 0xeb6ba0: r4 = 1
    //     0xeb6ba0: movz            x4, #0x1
    // 0xeb6ba4: lsl             x5, x0, #3
    // 0xeb6ba8: sub             x6, x5, x1
    // 0xeb6bac: r1 = 0
    //     0xeb6bac: movz            x1, #0
    // 0xeb6bb0: cmp             x1, x0
    // 0xeb6bb4: b.hs            #0xeb6c2c
    // 0xeb6bb8: ArrayLoad: r0 = r2[0]  ; List_1
    //     0xeb6bb8: ldrb            w0, [x2, #0x17]
    // 0xeb6bbc: sub             x1, x3, x6
    // 0xeb6bc0: tbnz            x1, #0x3f, #0xeb6c30
    // 0xeb6bc4: lsl             w3, w4, w1
    // 0xeb6bc8: cmp             x1, #0x1f
    // 0xeb6bcc: csel            x3, x3, xzr, le
    // 0xeb6bd0: sub             w1, w3, w4
    // 0xeb6bd4: ubfx            x0, x0, #0, #0x20
    // 0xeb6bd8: and             x3, x0, x1
    // 0xeb6bdc: ubfx            x3, x3, #0, #0x20
    // 0xeb6be0: ArrayStore: r2[0] = r3  ; TypeUnknown_1
    //     0xeb6be0: strb            w3, [x2, #0x17]
    // 0xeb6be4: mov             x0, x2
    // 0xeb6be8: LeaveFrame
    //     0xeb6be8: mov             SP, fp
    //     0xeb6bec: ldp             fp, lr, [SP], #0x10
    // 0xeb6bf0: ret
    //     0xeb6bf0: ret             
    // 0xeb6bf4: r0 = ArgumentError()
    //     0xeb6bf4: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xeb6bf8: mov             x1, x0
    // 0xeb6bfc: r0 = "numBits must be non-negative"
    //     0xeb6bfc: add             x0, PP, #0x20, lsl #12  ; [pp+0x209d8] "numBits must be non-negative"
    //     0xeb6c00: ldr             x0, [x0, #0x9d8]
    // 0xeb6c04: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb6c04: stur            w0, [x1, #0x17]
    // 0xeb6c08: r0 = false
    //     0xeb6c08: add             x0, NULL, #0x30  ; false
    // 0xeb6c0c: StoreField: r1->field_b = r0
    //     0xeb6c0c: stur            w0, [x1, #0xb]
    // 0xeb6c10: mov             x0, x1
    // 0xeb6c14: r0 = Throw()
    //     0xeb6c14: bl              #0xec04b8  ; ThrowStub
    // 0xeb6c18: brk             #0
    // 0xeb6c1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6c1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6c20: b               #0xeb6b10
    // 0xeb6c24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6c24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6c28: b               #0xeb6b68
    // 0xeb6c2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb6c2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeb6c30: str             x1, [THR, #0x7a8]  ; THR::
    // 0xeb6c34: stp             x2, x4, [SP, #-0x10]!
    // 0xeb6c38: stp             x0, x1, [SP, #-0x10]!
    // 0xeb6c3c: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xeb6c40: r4 = 0
    //     0xeb6c40: movz            x4, #0
    // 0xeb6c44: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeb6c48: blr             lr
    // 0xeb6c4c: brk             #0
  }
}
