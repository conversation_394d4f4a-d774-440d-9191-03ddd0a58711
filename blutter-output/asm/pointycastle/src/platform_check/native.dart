// lib: , url: package:pointycastle/src/platform_check/native.dart

// class id: 1051044, size: 0x8
class :: {
}

// class id: 552, size: 0x8, field offset: 0x8
//   const constructor, 
class PlatformIO extends Platform {

  static late final PlatformIO instance; // offset: 0x1734

  static PlatformIO instance() {
    // ** addr: 0x8c5010, size: 0x18
    // 0x8c5010: EnterFrame
    //     0x8c5010: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5014: mov             fp, SP
    // 0x8c5018: r0 = PlatformIO()
    //     0x8c5018: bl              #0x8c5028  ; AllocatePlatformIOStub -> PlatformIO (size=0x8)
    // 0x8c501c: LeaveFrame
    //     0x8c501c: mov             SP, fp
    //     0x8c5020: ldp             fp, lr, [SP], #0x10
    // 0x8c5024: ret
    //     0x8c5024: ret             
  }
}
