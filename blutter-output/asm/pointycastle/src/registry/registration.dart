// lib: , url: package:pointycastle/src/registry/registration.dart

// class id: 1051046, size: 0x8
class :: {

  static _ registerFactories(/* No info */) {
    // ** addr: 0x8c386c, size: 0xa0
    // 0x8c386c: EnterFrame
    //     0x8c386c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3870: mov             fp, SP
    // 0x8c3874: AllocStack(0x8)
    //     0x8c3874: sub             SP, SP, #8
    // 0x8c3878: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x8c3878: mov             x0, x1
    //     0x8c387c: stur            x1, [fp, #-8]
    // 0x8c3880: CheckStackOverflow
    //     0x8c3880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3884: cmp             SP, x16
    //     0x8c3888: b.ls            #0x8c3904
    // 0x8c388c: mov             x1, x0
    // 0x8c3890: r0 = _registerAsymmetricCiphers()
    //     0x8c3890: bl              #0x8e6a58  ; [package:pointycastle/src/registry/registration.dart] ::_registerAsymmetricCiphers
    // 0x8c3894: ldur            x1, [fp, #-8]
    // 0x8c3898: r0 = _registerBlockCiphers()
    //     0x8c3898: bl              #0x8e4b5c  ; [package:pointycastle/src/registry/registration.dart] ::_registerBlockCiphers
    // 0x8c389c: ldur            x1, [fp, #-8]
    // 0x8c38a0: r0 = _registerDigests()
    //     0x8c38a0: bl              #0x8d56dc  ; [package:pointycastle/src/registry/registration.dart] ::_registerDigests
    // 0x8c38a4: ldur            x1, [fp, #-8]
    // 0x8c38a8: r0 = _registerECCurves()
    //     0x8c38a8: bl              #0x8c8c4c  ; [package:pointycastle/src/registry/registration.dart] ::_registerECCurves
    // 0x8c38ac: ldur            x1, [fp, #-8]
    // 0x8c38b0: r0 = _registerKeyDerivators()
    //     0x8c38b0: bl              #0x8c7ea0  ; [package:pointycastle/src/registry/registration.dart] ::_registerKeyDerivators
    // 0x8c38b4: ldur            x1, [fp, #-8]
    // 0x8c38b8: r0 = _registerKeyGenerators()
    //     0x8c38b8: bl              #0x8c7d1c  ; [package:pointycastle/src/registry/registration.dart] ::_registerKeyGenerators
    // 0x8c38bc: ldur            x1, [fp, #-8]
    // 0x8c38c0: r0 = _registerPbeParameterGenerators()
    //     0x8c38c0: bl              #0x8c78f0  ; [package:pointycastle/src/registry/registration.dart] ::_registerPbeParameterGenerators
    // 0x8c38c4: ldur            x1, [fp, #-8]
    // 0x8c38c8: r0 = _registerMacs()
    //     0x8c38c8: bl              #0x8c6cb4  ; [package:pointycastle/src/registry/registration.dart] ::_registerMacs
    // 0x8c38cc: ldur            x1, [fp, #-8]
    // 0x8c38d0: r0 = _registerPaddedBlockCiphers()
    //     0x8c38d0: bl              #0x8c69f0  ; [package:pointycastle/src/registry/registration.dart] ::_registerPaddedBlockCiphers
    // 0x8c38d4: ldur            x1, [fp, #-8]
    // 0x8c38d8: r0 = _registerPaddings()
    //     0x8c38d8: bl              #0x8c686c  ; [package:pointycastle/src/registry/registration.dart] ::_registerPaddings
    // 0x8c38dc: ldur            x1, [fp, #-8]
    // 0x8c38e0: r0 = _registerRandoms()
    //     0x8c38e0: bl              #0x8c60d0  ; [package:pointycastle/src/registry/registration.dart] ::_registerRandoms
    // 0x8c38e4: ldur            x1, [fp, #-8]
    // 0x8c38e8: r0 = _registerSigners()
    //     0x8c38e8: bl              #0x8c562c  ; [package:pointycastle/src/registry/registration.dart] ::_registerSigners
    // 0x8c38ec: ldur            x1, [fp, #-8]
    // 0x8c38f0: r0 = _registerStreamCiphers()
    //     0x8c38f0: bl              #0x8c390c  ; [package:pointycastle/src/registry/registration.dart] ::_registerStreamCiphers
    // 0x8c38f4: r0 = Null
    //     0x8c38f4: mov             x0, NULL
    // 0x8c38f8: LeaveFrame
    //     0x8c38f8: mov             SP, fp
    //     0x8c38fc: ldp             fp, lr, [SP], #0x10
    // 0x8c3900: ret
    //     0x8c3900: ret             
    // 0x8c3904: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3904: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3908: b               #0x8c388c
  }
  static _ _registerStreamCiphers(/* No info */) {
    // ** addr: 0x8c390c, size: 0x194
    // 0x8c390c: EnterFrame
    //     0x8c390c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3910: mov             fp, SP
    // 0x8c3914: AllocStack(0x8)
    //     0x8c3914: sub             SP, SP, #8
    // 0x8c3918: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c3918: stur            x1, [fp, #-8]
    // 0x8c391c: CheckStackOverflow
    //     0x8c391c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3920: cmp             SP, x16
    //     0x8c3924: b.ls            #0x8c3a98
    // 0x8c3928: r0 = InitLateStaticField(0xe94) // [package:pointycastle/stream/ctr.dart] CTRStreamCipher::factoryConfig
    //     0x8c3928: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c392c: ldr             x0, [x0, #0x1d28]
    //     0x8c3930: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c3934: cmp             w0, w16
    //     0x8c3938: b.ne            #0x8c3948
    //     0x8c393c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17fd8] Field <CTRStreamCipher.factoryConfig>: static late final (offset: 0xe94)
    //     0x8c3940: ldr             x2, [x2, #0xfd8]
    //     0x8c3944: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c3948: ldur            x1, [fp, #-8]
    // 0x8c394c: mov             x2, x0
    // 0x8c3950: r0 = _addDynamicFactoryConfig()
    //     0x8c3950: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c3954: r0 = InitLateStaticField(0xe9c) // [package:pointycastle/stream/salsa20.dart] Salsa20Engine::factoryConfig
    //     0x8c3954: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c3958: ldr             x0, [x0, #0x1d38]
    //     0x8c395c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c3960: cmp             w0, w16
    //     0x8c3964: b.ne            #0x8c3974
    //     0x8c3968: add             x2, PP, #0x17, lsl #12  ; [pp+0x17fe0] Field <Salsa20Engine.factoryConfig>: static late final (offset: 0xe9c)
    //     0x8c396c: ldr             x2, [x2, #0xfe0]
    //     0x8c3970: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c3974: ldur            x1, [fp, #-8]
    // 0x8c3978: mov             x2, x0
    // 0x8c397c: r0 = _addStaticFactoryConfig()
    //     0x8c397c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c3980: r0 = InitLateStaticField(0xea0) // [package:pointycastle/stream/chacha20.dart] ChaCha20Engine::factoryConfig
    //     0x8c3980: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c3984: ldr             x0, [x0, #0x1d40]
    //     0x8c3988: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c398c: cmp             w0, w16
    //     0x8c3990: b.ne            #0x8c39a0
    //     0x8c3994: add             x2, PP, #0x17, lsl #12  ; [pp+0x17fe8] Field <ChaCha20Engine.factoryConfig>: static late final (offset: 0xea0)
    //     0x8c3998: ldr             x2, [x2, #0xfe8]
    //     0x8c399c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c39a0: ldur            x1, [fp, #-8]
    // 0x8c39a4: mov             x2, x0
    // 0x8c39a8: r0 = _addDynamicFactoryConfig()
    //     0x8c39a8: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c39ac: r0 = InitLateStaticField(0xea4) // [package:pointycastle/stream/chacha7539.dart] ChaCha7539Engine::factoryConfig
    //     0x8c39ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c39b0: ldr             x0, [x0, #0x1d48]
    //     0x8c39b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c39b8: cmp             w0, w16
    //     0x8c39bc: b.ne            #0x8c39cc
    //     0x8c39c0: add             x2, PP, #0x17, lsl #12  ; [pp+0x17ff0] Field <ChaCha7539Engine.factoryConfig>: static late final (offset: 0xea4)
    //     0x8c39c4: ldr             x2, [x2, #0xff0]
    //     0x8c39c8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c39cc: ldur            x1, [fp, #-8]
    // 0x8c39d0: mov             x2, x0
    // 0x8c39d4: r0 = _addDynamicFactoryConfig()
    //     0x8c39d4: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c39d8: r0 = InitLateStaticField(0xea8) // [package:pointycastle/stream/chacha20poly1305.dart] ChaCha20Poly1305::factoryConfig
    //     0x8c39d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c39dc: ldr             x0, [x0, #0x1d50]
    //     0x8c39e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c39e4: cmp             w0, w16
    //     0x8c39e8: b.ne            #0x8c39f8
    //     0x8c39ec: add             x2, PP, #0x17, lsl #12  ; [pp+0x17ff8] Field <ChaCha20Poly1305.factoryConfig>: static late final (offset: 0xea8)
    //     0x8c39f0: ldr             x2, [x2, #0xff8]
    //     0x8c39f4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c39f8: ldur            x1, [fp, #-8]
    // 0x8c39fc: mov             x2, x0
    // 0x8c3a00: r0 = _addStaticFactoryConfig()
    //     0x8c3a00: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c3a04: r0 = InitLateStaticField(0xe98) // [package:pointycastle/stream/sic.dart] SICStreamCipher::factoryConfig
    //     0x8c3a04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c3a08: ldr             x0, [x0, #0x1d30]
    //     0x8c3a0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c3a10: cmp             w0, w16
    //     0x8c3a14: b.ne            #0x8c3a24
    //     0x8c3a18: add             x2, PP, #0x18, lsl #12  ; [pp+0x18000] Field <SICStreamCipher.factoryConfig>: static late final (offset: 0xe98)
    //     0x8c3a1c: ldr             x2, [x2]
    //     0x8c3a20: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c3a24: ldur            x1, [fp, #-8]
    // 0x8c3a28: mov             x2, x0
    // 0x8c3a2c: r0 = _addDynamicFactoryConfig()
    //     0x8c3a2c: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c3a30: r0 = InitLateStaticField(0xeac) // [package:pointycastle/stream/eax.dart] EAX::factoryConfig
    //     0x8c3a30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c3a34: ldr             x0, [x0, #0x1d58]
    //     0x8c3a38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c3a3c: cmp             w0, w16
    //     0x8c3a40: b.ne            #0x8c3a50
    //     0x8c3a44: add             x2, PP, #0x18, lsl #12  ; [pp+0x18008] Field <EAX.factoryConfig>: static late final (offset: 0xeac)
    //     0x8c3a48: ldr             x2, [x2, #8]
    //     0x8c3a4c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c3a50: ldur            x1, [fp, #-8]
    // 0x8c3a54: mov             x2, x0
    // 0x8c3a58: r0 = _addDynamicFactoryConfig()
    //     0x8c3a58: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c3a5c: r0 = InitLateStaticField(0xeb0) // [package:pointycastle/stream/rc4_engine.dart] RC4Engine::factoryConfig
    //     0x8c3a5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c3a60: ldr             x0, [x0, #0x1d60]
    //     0x8c3a64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c3a68: cmp             w0, w16
    //     0x8c3a6c: b.ne            #0x8c3a7c
    //     0x8c3a70: add             x2, PP, #0x18, lsl #12  ; [pp+0x18010] Field <RC4Engine.factoryConfig>: static late final (offset: 0xeb0)
    //     0x8c3a74: ldr             x2, [x2, #0x10]
    //     0x8c3a78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c3a7c: ldur            x1, [fp, #-8]
    // 0x8c3a80: mov             x2, x0
    // 0x8c3a84: r0 = _addStaticFactoryConfig()
    //     0x8c3a84: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c3a88: r0 = Null
    //     0x8c3a88: mov             x0, NULL
    // 0x8c3a8c: LeaveFrame
    //     0x8c3a8c: mov             SP, fp
    //     0x8c3a90: ldp             fp, lr, [SP], #0x10
    // 0x8c3a94: ret
    //     0x8c3a94: ret             
    // 0x8c3a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3a98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3a9c: b               #0x8c3928
  }
  static _ _registerSigners(/* No info */) {
    // ** addr: 0x8c562c, size: 0xb8
    // 0x8c562c: EnterFrame
    //     0x8c562c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5630: mov             fp, SP
    // 0x8c5634: AllocStack(0x8)
    //     0x8c5634: sub             SP, SP, #8
    // 0x8c5638: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c5638: stur            x1, [fp, #-8]
    // 0x8c563c: CheckStackOverflow
    //     0x8c563c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5640: cmp             SP, x16
    //     0x8c5644: b.ls            #0x8c56dc
    // 0x8c5648: r0 = InitLateStaticField(0xe84) // [package:pointycastle/signers/ecdsa_signer.dart] ECDSASigner::factoryConfig
    //     0x8c5648: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c564c: ldr             x0, [x0, #0x1d08]
    //     0x8c5650: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c5654: cmp             w0, w16
    //     0x8c5658: b.ne            #0x8c5668
    //     0x8c565c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18168] Field <ECDSASigner.factoryConfig>: static late final (offset: 0xe84)
    //     0x8c5660: ldr             x2, [x2, #0x168]
    //     0x8c5664: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c5668: ldur            x1, [fp, #-8]
    // 0x8c566c: mov             x2, x0
    // 0x8c5670: r0 = _addDynamicFactoryConfig()
    //     0x8c5670: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c5674: r0 = InitLateStaticField(0xe90) // [package:pointycastle/signers/pss_signer.dart] PSSSigner::factoryConfig
    //     0x8c5674: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c5678: ldr             x0, [x0, #0x1d20]
    //     0x8c567c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c5680: cmp             w0, w16
    //     0x8c5684: b.ne            #0x8c5694
    //     0x8c5688: add             x2, PP, #0x18, lsl #12  ; [pp+0x18170] Field <PSSSigner.factoryConfig>: static late final (offset: 0xe90)
    //     0x8c568c: ldr             x2, [x2, #0x170]
    //     0x8c5690: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c5694: ldur            x1, [fp, #-8]
    // 0x8c5698: mov             x2, x0
    // 0x8c569c: r0 = _addDynamicFactoryConfig()
    //     0x8c569c: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c56a0: r0 = InitLateStaticField(0xe88) // [package:pointycastle/signers/rsa_signer.dart] RSASigner::factoryConfig
    //     0x8c56a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c56a4: ldr             x0, [x0, #0x1d10]
    //     0x8c56a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c56ac: cmp             w0, w16
    //     0x8c56b0: b.ne            #0x8c56c0
    //     0x8c56b4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18178] Field <RSASigner.factoryConfig>: static late final (offset: 0xe88)
    //     0x8c56b8: ldr             x2, [x2, #0x178]
    //     0x8c56bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c56c0: ldur            x1, [fp, #-8]
    // 0x8c56c4: mov             x2, x0
    // 0x8c56c8: r0 = _addDynamicFactoryConfig()
    //     0x8c56c8: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c56cc: r0 = Null
    //     0x8c56cc: mov             x0, NULL
    // 0x8c56d0: LeaveFrame
    //     0x8c56d0: mov             SP, fp
    //     0x8c56d4: ldp             fp, lr, [SP], #0x10
    // 0x8c56d8: ret
    //     0x8c56d8: ret             
    // 0x8c56dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c56dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c56e0: b               #0x8c5648
  }
  static _ _registerRandoms(/* No info */) {
    // ** addr: 0x8c60d0, size: 0xb8
    // 0x8c60d0: EnterFrame
    //     0x8c60d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c60d4: mov             fp, SP
    // 0x8c60d8: AllocStack(0x8)
    //     0x8c60d8: sub             SP, SP, #8
    // 0x8c60dc: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c60dc: stur            x1, [fp, #-8]
    // 0x8c60e0: CheckStackOverflow
    //     0x8c60e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c60e4: cmp             SP, x16
    //     0x8c60e8: b.ls            #0x8c6180
    // 0x8c60ec: r0 = InitLateStaticField(0xe78) // [package:pointycastle/random/auto_seed_block_ctr_random.dart] AutoSeedBlockCtrRandom::factoryConfig
    //     0x8c60ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c60f0: ldr             x0, [x0, #0x1cf0]
    //     0x8c60f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c60f8: cmp             w0, w16
    //     0x8c60fc: b.ne            #0x8c610c
    //     0x8c6100: add             x2, PP, #0x18, lsl #12  ; [pp+0x182b0] Field <AutoSeedBlockCtrRandom.factoryConfig>: static late final (offset: 0xe78)
    //     0x8c6104: ldr             x2, [x2, #0x2b0]
    //     0x8c6108: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c610c: ldur            x1, [fp, #-8]
    // 0x8c6110: mov             x2, x0
    // 0x8c6114: r0 = _addDynamicFactoryConfig()
    //     0x8c6114: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c6118: r0 = InitLateStaticField(0xe7c) // [package:pointycastle/random/block_ctr_random.dart] BlockCtrRandom::factoryConfig
    //     0x8c6118: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c611c: ldr             x0, [x0, #0x1cf8]
    //     0x8c6120: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6124: cmp             w0, w16
    //     0x8c6128: b.ne            #0x8c6138
    //     0x8c612c: add             x2, PP, #0x18, lsl #12  ; [pp+0x182b8] Field <BlockCtrRandom.factoryConfig>: static late final (offset: 0xe7c)
    //     0x8c6130: ldr             x2, [x2, #0x2b8]
    //     0x8c6134: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6138: ldur            x1, [fp, #-8]
    // 0x8c613c: mov             x2, x0
    // 0x8c6140: r0 = _addDynamicFactoryConfig()
    //     0x8c6140: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c6144: r0 = InitLateStaticField(0xe80) // [package:pointycastle/random/fortuna_random.dart] FortunaRandom::factoryConfig
    //     0x8c6144: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6148: ldr             x0, [x0, #0x1d00]
    //     0x8c614c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6150: cmp             w0, w16
    //     0x8c6154: b.ne            #0x8c6164
    //     0x8c6158: add             x2, PP, #0x18, lsl #12  ; [pp+0x182c0] Field <FortunaRandom.factoryConfig>: static late final (offset: 0xe80)
    //     0x8c615c: ldr             x2, [x2, #0x2c0]
    //     0x8c6160: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6164: ldur            x1, [fp, #-8]
    // 0x8c6168: mov             x2, x0
    // 0x8c616c: r0 = _addStaticFactoryConfig()
    //     0x8c616c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c6170: r0 = Null
    //     0x8c6170: mov             x0, NULL
    // 0x8c6174: LeaveFrame
    //     0x8c6174: mov             SP, fp
    //     0x8c6178: ldp             fp, lr, [SP], #0x10
    // 0x8c617c: ret
    //     0x8c617c: ret             
    // 0x8c6180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6180: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6184: b               #0x8c60ec
  }
  static _ _registerPaddings(/* No info */) {
    // ** addr: 0x8c686c, size: 0x8c
    // 0x8c686c: EnterFrame
    //     0x8c686c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6870: mov             fp, SP
    // 0x8c6874: AllocStack(0x8)
    //     0x8c6874: sub             SP, SP, #8
    // 0x8c6878: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c6878: stur            x1, [fp, #-8]
    // 0x8c687c: CheckStackOverflow
    //     0x8c687c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6880: cmp             SP, x16
    //     0x8c6884: b.ls            #0x8c68f0
    // 0x8c6888: r0 = InitLateStaticField(0xe70) // [package:pointycastle/paddings/pkcs7.dart] PKCS7Padding::factoryConfig
    //     0x8c6888: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c688c: ldr             x0, [x0, #0x1ce0]
    //     0x8c6890: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6894: cmp             w0, w16
    //     0x8c6898: b.ne            #0x8c68a8
    //     0x8c689c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18310] Field <PKCS7Padding.factoryConfig>: static late final (offset: 0xe70)
    //     0x8c68a0: ldr             x2, [x2, #0x310]
    //     0x8c68a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c68a8: ldur            x1, [fp, #-8]
    // 0x8c68ac: mov             x2, x0
    // 0x8c68b0: r0 = _addStaticFactoryConfig()
    //     0x8c68b0: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c68b4: r0 = InitLateStaticField(0xe74) // [package:pointycastle/paddings/iso7816d4.dart] ISO7816d4Padding::factoryConfig
    //     0x8c68b4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c68b8: ldr             x0, [x0, #0x1ce8]
    //     0x8c68bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c68c0: cmp             w0, w16
    //     0x8c68c4: b.ne            #0x8c68d4
    //     0x8c68c8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18318] Field <ISO7816d4Padding.factoryConfig>: static late final (offset: 0xe74)
    //     0x8c68cc: ldr             x2, [x2, #0x318]
    //     0x8c68d0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c68d4: ldur            x1, [fp, #-8]
    // 0x8c68d8: mov             x2, x0
    // 0x8c68dc: r0 = _addStaticFactoryConfig()
    //     0x8c68dc: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c68e0: r0 = Null
    //     0x8c68e0: mov             x0, NULL
    // 0x8c68e4: LeaveFrame
    //     0x8c68e4: mov             SP, fp
    //     0x8c68e8: ldp             fp, lr, [SP], #0x10
    // 0x8c68ec: ret
    //     0x8c68ec: ret             
    // 0x8c68f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c68f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c68f4: b               #0x8c6888
  }
  static _ _registerPaddedBlockCiphers(/* No info */) {
    // ** addr: 0x8c69f0, size: 0x60
    // 0x8c69f0: EnterFrame
    //     0x8c69f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c69f4: mov             fp, SP
    // 0x8c69f8: AllocStack(0x8)
    //     0x8c69f8: sub             SP, SP, #8
    // 0x8c69fc: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c69fc: stur            x1, [fp, #-8]
    // 0x8c6a00: CheckStackOverflow
    //     0x8c6a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6a04: cmp             SP, x16
    //     0x8c6a08: b.ls            #0x8c6a48
    // 0x8c6a0c: r0 = InitLateStaticField(0xe6c) // [package:pointycastle/padded_block_cipher/padded_block_cipher_impl.dart] PaddedBlockCipherImpl::factoryConfig
    //     0x8c6a0c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6a10: ldr             x0, [x0, #0x1cd8]
    //     0x8c6a14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6a18: cmp             w0, w16
    //     0x8c6a1c: b.ne            #0x8c6a2c
    //     0x8c6a20: add             x2, PP, #0x18, lsl #12  ; [pp+0x18340] Field <PaddedBlockCipherImpl.factoryConfig>: static late final (offset: 0xe6c)
    //     0x8c6a24: ldr             x2, [x2, #0x340]
    //     0x8c6a28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6a2c: ldur            x1, [fp, #-8]
    // 0x8c6a30: mov             x2, x0
    // 0x8c6a34: r0 = _addDynamicFactoryConfig()
    //     0x8c6a34: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c6a38: r0 = Null
    //     0x8c6a38: mov             x0, NULL
    // 0x8c6a3c: LeaveFrame
    //     0x8c6a3c: mov             SP, fp
    //     0x8c6a40: ldp             fp, lr, [SP], #0x10
    // 0x8c6a44: ret
    //     0x8c6a44: ret             
    // 0x8c6a48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6a48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6a4c: b               #0x8c6a0c
  }
  static _ _registerMacs(/* No info */) {
    // ** addr: 0x8c6cb4, size: 0xe4
    // 0x8c6cb4: EnterFrame
    //     0x8c6cb4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6cb8: mov             fp, SP
    // 0x8c6cbc: AllocStack(0x8)
    //     0x8c6cbc: sub             SP, SP, #8
    // 0x8c6cc0: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c6cc0: stur            x1, [fp, #-8]
    // 0x8c6cc4: CheckStackOverflow
    //     0x8c6cc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6cc8: cmp             SP, x16
    //     0x8c6ccc: b.ls            #0x8c6d90
    // 0x8c6cd0: r0 = InitLateStaticField(0xe5c) // [package:pointycastle/macs/hmac.dart] HMac::factoryConfig
    //     0x8c6cd0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6cd4: ldr             x0, [x0, #0x1cb8]
    //     0x8c6cd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6cdc: cmp             w0, w16
    //     0x8c6ce0: b.ne            #0x8c6cf0
    //     0x8c6ce4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18370] Field <HMac.factoryConfig>: static late final (offset: 0xe5c)
    //     0x8c6ce8: ldr             x2, [x2, #0x370]
    //     0x8c6cec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6cf0: ldur            x1, [fp, #-8]
    // 0x8c6cf4: mov             x2, x0
    // 0x8c6cf8: r0 = _addDynamicFactoryConfig()
    //     0x8c6cf8: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c6cfc: r0 = InitLateStaticField(0xe60) // [package:pointycastle/macs/cmac.dart] CMac::factoryConfig
    //     0x8c6cfc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6d00: ldr             x0, [x0, #0x1cc0]
    //     0x8c6d04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6d08: cmp             w0, w16
    //     0x8c6d0c: b.ne            #0x8c6d1c
    //     0x8c6d10: add             x2, PP, #0x18, lsl #12  ; [pp+0x18378] Field <CMac.factoryConfig>: static late final (offset: 0xe60)
    //     0x8c6d14: ldr             x2, [x2, #0x378]
    //     0x8c6d18: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6d1c: ldur            x1, [fp, #-8]
    // 0x8c6d20: mov             x2, x0
    // 0x8c6d24: r0 = _addDynamicFactoryConfig()
    //     0x8c6d24: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c6d28: r0 = InitLateStaticField(0xe68) // [package:pointycastle/macs/cbc_block_cipher_mac.dart] CBCBlockCipherMac::factoryConfig
    //     0x8c6d28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6d2c: ldr             x0, [x0, #0x1cd0]
    //     0x8c6d30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6d34: cmp             w0, w16
    //     0x8c6d38: b.ne            #0x8c6d48
    //     0x8c6d3c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18380] Field <CBCBlockCipherMac.factoryConfig>: static late final (offset: 0xe68)
    //     0x8c6d40: ldr             x2, [x2, #0x380]
    //     0x8c6d44: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6d48: ldur            x1, [fp, #-8]
    // 0x8c6d4c: mov             x2, x0
    // 0x8c6d50: r0 = _addDynamicFactoryConfig()
    //     0x8c6d50: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c6d54: r0 = InitLateStaticField(0xe64) // [package:pointycastle/macs/poly1305.dart] Poly1305::factoryConfig
    //     0x8c6d54: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c6d58: ldr             x0, [x0, #0x1cc8]
    //     0x8c6d5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c6d60: cmp             w0, w16
    //     0x8c6d64: b.ne            #0x8c6d74
    //     0x8c6d68: add             x2, PP, #0x18, lsl #12  ; [pp+0x18388] Field <Poly1305.factoryConfig>: static late final (offset: 0xe64)
    //     0x8c6d6c: ldr             x2, [x2, #0x388]
    //     0x8c6d70: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c6d74: ldur            x1, [fp, #-8]
    // 0x8c6d78: mov             x2, x0
    // 0x8c6d7c: r0 = _addDynamicFactoryConfig()
    //     0x8c6d7c: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c6d80: r0 = Null
    //     0x8c6d80: mov             x0, NULL
    // 0x8c6d84: LeaveFrame
    //     0x8c6d84: mov             SP, fp
    //     0x8c6d88: ldp             fp, lr, [SP], #0x10
    // 0x8c6d8c: ret
    //     0x8c6d8c: ret             
    // 0x8c6d90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6d90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6d94: b               #0x8c6cd0
  }
  static _ _registerPbeParameterGenerators(/* No info */) {
    // ** addr: 0x8c78f0, size: 0x8c
    // 0x8c78f0: EnterFrame
    //     0x8c78f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c78f4: mov             fp, SP
    // 0x8c78f8: AllocStack(0x8)
    //     0x8c78f8: sub             SP, SP, #8
    // 0x8c78fc: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c78fc: stur            x1, [fp, #-8]
    // 0x8c7900: CheckStackOverflow
    //     0x8c7900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7904: cmp             SP, x16
    //     0x8c7908: b.ls            #0x8c7974
    // 0x8c790c: r0 = InitLateStaticField(0xe4c) // [package:pointycastle/key_derivators/pkcs12_parameter_generator.dart] PKCS12ParametersGenerator::factoryConfig
    //     0x8c790c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7910: ldr             x0, [x0, #0x1c98]
    //     0x8c7914: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7918: cmp             w0, w16
    //     0x8c791c: b.ne            #0x8c792c
    //     0x8c7920: add             x2, PP, #0x18, lsl #12  ; [pp+0x183f8] Field <PKCS12ParametersGenerator.factoryConfig>: static late final (offset: 0xe4c)
    //     0x8c7924: ldr             x2, [x2, #0x3f8]
    //     0x8c7928: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c792c: ldur            x1, [fp, #-8]
    // 0x8c7930: mov             x2, x0
    // 0x8c7934: r0 = _addDynamicFactoryConfig()
    //     0x8c7934: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c7938: r0 = InitLateStaticField(0xe50) // [package:pointycastle/key_derivators/pkcs5s1_parameter_generator.dart] PKCS5S1ParameterGenerator::factoryConfig
    //     0x8c7938: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c793c: ldr             x0, [x0, #0x1ca0]
    //     0x8c7940: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7944: cmp             w0, w16
    //     0x8c7948: b.ne            #0x8c7958
    //     0x8c794c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18400] Field <PKCS5S1ParameterGenerator.factoryConfig>: static late final (offset: 0xe50)
    //     0x8c7950: ldr             x2, [x2, #0x400]
    //     0x8c7954: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7958: ldur            x1, [fp, #-8]
    // 0x8c795c: mov             x2, x0
    // 0x8c7960: r0 = _addDynamicFactoryConfig()
    //     0x8c7960: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c7964: r0 = Null
    //     0x8c7964: mov             x0, NULL
    // 0x8c7968: LeaveFrame
    //     0x8c7968: mov             SP, fp
    //     0x8c796c: ldp             fp, lr, [SP], #0x10
    // 0x8c7970: ret
    //     0x8c7970: ret             
    // 0x8c7974: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7974: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7978: b               #0x8c790c
  }
  static _ _registerKeyGenerators(/* No info */) {
    // ** addr: 0x8c7d1c, size: 0x8c
    // 0x8c7d1c: EnterFrame
    //     0x8c7d1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7d20: mov             fp, SP
    // 0x8c7d24: AllocStack(0x8)
    //     0x8c7d24: sub             SP, SP, #8
    // 0x8c7d28: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c7d28: stur            x1, [fp, #-8]
    // 0x8c7d2c: CheckStackOverflow
    //     0x8c7d2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7d30: cmp             SP, x16
    //     0x8c7d34: b.ls            #0x8c7da0
    // 0x8c7d38: r0 = InitLateStaticField(0xe54) // [package:pointycastle/key_generators/ec_key_generator.dart] ECKeyGenerator::factoryConfig
    //     0x8c7d38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7d3c: ldr             x0, [x0, #0x1ca8]
    //     0x8c7d40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7d44: cmp             w0, w16
    //     0x8c7d48: b.ne            #0x8c7d58
    //     0x8c7d4c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18440] Field <ECKeyGenerator.factoryConfig>: static late final (offset: 0xe54)
    //     0x8c7d50: ldr             x2, [x2, #0x440]
    //     0x8c7d54: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7d58: ldur            x1, [fp, #-8]
    // 0x8c7d5c: mov             x2, x0
    // 0x8c7d60: r0 = _addStaticFactoryConfig()
    //     0x8c7d60: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c7d64: r0 = InitLateStaticField(0xe58) // [package:pointycastle/key_generators/rsa_key_generator.dart] RSAKeyGenerator::factoryConfig
    //     0x8c7d64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7d68: ldr             x0, [x0, #0x1cb0]
    //     0x8c7d6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7d70: cmp             w0, w16
    //     0x8c7d74: b.ne            #0x8c7d84
    //     0x8c7d78: add             x2, PP, #0x18, lsl #12  ; [pp+0x18448] Field <RSAKeyGenerator.factoryConfig>: static late final (offset: 0xe58)
    //     0x8c7d7c: ldr             x2, [x2, #0x448]
    //     0x8c7d80: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7d84: ldur            x1, [fp, #-8]
    // 0x8c7d88: mov             x2, x0
    // 0x8c7d8c: r0 = _addStaticFactoryConfig()
    //     0x8c7d8c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c7d90: r0 = Null
    //     0x8c7d90: mov             x0, NULL
    // 0x8c7d94: LeaveFrame
    //     0x8c7d94: mov             SP, fp
    //     0x8c7d98: ldp             fp, lr, [SP], #0x10
    // 0x8c7d9c: ret
    //     0x8c7d9c: ret             
    // 0x8c7da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7da0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7da4: b               #0x8c7d38
  }
  static _ _registerKeyDerivators(/* No info */) {
    // ** addr: 0x8c7ea0, size: 0x158
    // 0x8c7ea0: EnterFrame
    //     0x8c7ea0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7ea4: mov             fp, SP
    // 0x8c7ea8: AllocStack(0x10)
    //     0x8c7ea8: sub             SP, SP, #0x10
    // 0x8c7eac: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c7eac: stur            x1, [fp, #-8]
    // 0x8c7eb0: CheckStackOverflow
    //     0x8c7eb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7eb4: cmp             SP, x16
    //     0x8c7eb8: b.ls            #0x8c7ff0
    // 0x8c7ebc: r0 = InitLateStaticField(0xe44) // [package:pointycastle/key_derivators/pbkdf2.dart] PBKDF2KeyDerivator::factoryConfig
    //     0x8c7ebc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7ec0: ldr             x0, [x0, #0x1c88]
    //     0x8c7ec4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7ec8: cmp             w0, w16
    //     0x8c7ecc: b.ne            #0x8c7edc
    //     0x8c7ed0: add             x2, PP, #0x18, lsl #12  ; [pp+0x18478] Field <PBKDF2KeyDerivator.factoryConfig>: static late final (offset: 0xe44)
    //     0x8c7ed4: ldr             x2, [x2, #0x478]
    //     0x8c7ed8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7edc: ldur            x1, [fp, #-8]
    // 0x8c7ee0: mov             x2, x0
    // 0x8c7ee4: r0 = _addDynamicFactoryConfig()
    //     0x8c7ee4: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c7ee8: r0 = InitLateStaticField(0xe48) // [package:pointycastle/key_derivators/scrypt.dart] Scrypt::factoryConfig
    //     0x8c7ee8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7eec: ldr             x0, [x0, #0x1c90]
    //     0x8c7ef0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7ef4: cmp             w0, w16
    //     0x8c7ef8: b.ne            #0x8c7f08
    //     0x8c7efc: add             x2, PP, #0x18, lsl #12  ; [pp+0x18480] Field <Scrypt.factoryConfig>: static late final (offset: 0xe48)
    //     0x8c7f00: ldr             x2, [x2, #0x480]
    //     0x8c7f04: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7f08: ldur            x1, [fp, #-8]
    // 0x8c7f0c: mov             x2, x0
    // 0x8c7f10: r0 = _addStaticFactoryConfig()
    //     0x8c7f10: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c7f14: r0 = InitLateStaticField(0xe3c) // [package:pointycastle/key_derivators/hkdf.dart] HKDFKeyDerivator::factoryConfig
    //     0x8c7f14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7f18: ldr             x0, [x0, #0x1c78]
    //     0x8c7f1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7f20: cmp             w0, w16
    //     0x8c7f24: b.ne            #0x8c7f34
    //     0x8c7f28: add             x2, PP, #0x18, lsl #12  ; [pp+0x18488] Field <HKDFKeyDerivator.factoryConfig>: static late final (offset: 0xe3c)
    //     0x8c7f2c: ldr             x2, [x2, #0x488]
    //     0x8c7f30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7f34: ldur            x1, [fp, #-8]
    // 0x8c7f38: mov             x2, x0
    // 0x8c7f3c: r0 = _addDynamicFactoryConfig()
    //     0x8c7f3c: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c7f40: r0 = InitLateStaticField(0xf5c) // [package:pointycastle/key_derivators/argon2_native_int_impl.dart] Argon2BytesGenerator::factoryConfig
    //     0x8c7f40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7f44: ldr             x0, [x0, #0x1eb8]
    //     0x8c7f48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7f4c: cmp             w0, w16
    //     0x8c7f50: b.ne            #0x8c7f60
    //     0x8c7f54: add             x2, PP, #0x18, lsl #12  ; [pp+0x18490] Field <Argon2BytesGenerator.factoryConfig>: static late final (offset: 0xf5c)
    //     0x8c7f58: ldr             x2, [x2, #0x490]
    //     0x8c7f5c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7f60: ldur            x1, [fp, #-8]
    // 0x8c7f64: mov             x2, x0
    // 0x8c7f68: r0 = _addStaticFactoryConfig()
    //     0x8c7f68: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c7f6c: r0 = InitLateStaticField(0x172c) // [package:pointycastle/key_derivators/concat_kdf.dart] ConcatKDFDerivator::factoryConfig
    //     0x8c7f6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7f70: ldr             x0, [x0, #0x2e58]
    //     0x8c7f74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7f78: cmp             w0, w16
    //     0x8c7f7c: b.ne            #0x8c7f8c
    //     0x8c7f80: add             x2, PP, #0x18, lsl #12  ; [pp+0x18498] Field <ConcatKDFDerivator.factoryConfig>: static late final (offset: 0x172c)
    //     0x8c7f84: ldr             x2, [x2, #0x498]
    //     0x8c7f88: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7f8c: ldur            x1, [fp, #-8]
    // 0x8c7f90: mov             x2, x0
    // 0x8c7f94: r0 = _addDynamicFactoryConfig()
    //     0x8c7f94: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8c7f98: r0 = InitLateStaticField(0x1730) // [package:pointycastle/key_derivators/ecdh_kdf.dart] ECDHKeyDerivator::factoryConfig
    //     0x8c7f98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c7f9c: ldr             x0, [x0, #0x2e60]
    //     0x8c7fa0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c7fa4: cmp             w0, w16
    //     0x8c7fa8: b.ne            #0x8c7fb8
    //     0x8c7fac: add             x2, PP, #0x18, lsl #12  ; [pp+0x184a0] Field <ECDHKeyDerivator.factoryConfig>: static late final (offset: 0x1730)
    //     0x8c7fb0: ldr             x2, [x2, #0x4a0]
    //     0x8c7fb4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c7fb8: ldur            x1, [fp, #-8]
    // 0x8c7fbc: mov             x2, x0
    // 0x8c7fc0: stur            x0, [fp, #-0x10]
    // 0x8c7fc4: r0 = _addStaticFactoryConfig()
    //     0x8c7fc4: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c7fc8: ldur            x1, [fp, #-8]
    // 0x8c7fcc: ldur            x2, [fp, #-0x10]
    // 0x8c7fd0: r0 = _addStaticFactoryConfig()
    //     0x8c7fd0: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c7fd4: ldur            x1, [fp, #-8]
    // 0x8c7fd8: ldur            x2, [fp, #-0x10]
    // 0x8c7fdc: r0 = _addStaticFactoryConfig()
    //     0x8c7fdc: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c7fe0: r0 = Null
    //     0x8c7fe0: mov             x0, NULL
    // 0x8c7fe4: LeaveFrame
    //     0x8c7fe4: mov             SP, fp
    //     0x8c7fe8: ldp             fp, lr, [SP], #0x10
    // 0x8c7fec: ret
    //     0x8c7fec: ret             
    // 0x8c7ff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7ff0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7ff4: b               #0x8c7ebc
  }
  static _ _registerECCurves(/* No info */) {
    // ** addr: 0x8c8c4c, size: 0x740
    // 0x8c8c4c: EnterFrame
    //     0x8c8c4c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c8c50: mov             fp, SP
    // 0x8c8c54: AllocStack(0x8)
    //     0x8c8c54: sub             SP, SP, #8
    // 0x8c8c58: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c8c58: stur            x1, [fp, #-8]
    // 0x8c8c5c: CheckStackOverflow
    //     0x8c8c5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c8c60: cmp             SP, x16
    //     0x8c8c64: b.ls            #0x8c9384
    // 0x8c8c68: r0 = InitLateStaticField(0xeb4) // [package:pointycastle/ecc/curves/brainpoolp160r1.dart] ECCurve_brainpoolp160r1::factoryConfig
    //     0x8c8c68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8c6c: ldr             x0, [x0, #0x1d68]
    //     0x8c8c70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8c74: cmp             w0, w16
    //     0x8c8c78: b.ne            #0x8c8c88
    //     0x8c8c7c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18590] Field <ECCurve_brainpoolp160r1.factoryConfig>: static late final (offset: 0xeb4)
    //     0x8c8c80: ldr             x2, [x2, #0x590]
    //     0x8c8c84: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8c88: ldur            x1, [fp, #-8]
    // 0x8c8c8c: mov             x2, x0
    // 0x8c8c90: r0 = _addStaticFactoryConfig()
    //     0x8c8c90: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8c94: r0 = InitLateStaticField(0xeb8) // [package:pointycastle/ecc/curves/brainpoolp160t1.dart] ECCurve_brainpoolp160t1::factoryConfig
    //     0x8c8c94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8c98: ldr             x0, [x0, #0x1d70]
    //     0x8c8c9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8ca0: cmp             w0, w16
    //     0x8c8ca4: b.ne            #0x8c8cb4
    //     0x8c8ca8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18598] Field <ECCurve_brainpoolp160t1.factoryConfig>: static late final (offset: 0xeb8)
    //     0x8c8cac: ldr             x2, [x2, #0x598]
    //     0x8c8cb0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8cb4: ldur            x1, [fp, #-8]
    // 0x8c8cb8: mov             x2, x0
    // 0x8c8cbc: r0 = _addStaticFactoryConfig()
    //     0x8c8cbc: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8cc0: r0 = InitLateStaticField(0xebc) // [package:pointycastle/ecc/curves/brainpoolp192r1.dart] ECCurve_brainpoolp192r1::factoryConfig
    //     0x8c8cc0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8cc4: ldr             x0, [x0, #0x1d78]
    //     0x8c8cc8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8ccc: cmp             w0, w16
    //     0x8c8cd0: b.ne            #0x8c8ce0
    //     0x8c8cd4: add             x2, PP, #0x18, lsl #12  ; [pp+0x185a0] Field <ECCurve_brainpoolp192r1.factoryConfig>: static late final (offset: 0xebc)
    //     0x8c8cd8: ldr             x2, [x2, #0x5a0]
    //     0x8c8cdc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8ce0: ldur            x1, [fp, #-8]
    // 0x8c8ce4: mov             x2, x0
    // 0x8c8ce8: r0 = _addStaticFactoryConfig()
    //     0x8c8ce8: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8cec: r0 = InitLateStaticField(0xec0) // [package:pointycastle/ecc/curves/brainpoolp192t1.dart] ECCurve_brainpoolp192t1::factoryConfig
    //     0x8c8cec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8cf0: ldr             x0, [x0, #0x1d80]
    //     0x8c8cf4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8cf8: cmp             w0, w16
    //     0x8c8cfc: b.ne            #0x8c8d0c
    //     0x8c8d00: add             x2, PP, #0x18, lsl #12  ; [pp+0x185a8] Field <ECCurve_brainpoolp192t1.factoryConfig>: static late final (offset: 0xec0)
    //     0x8c8d04: ldr             x2, [x2, #0x5a8]
    //     0x8c8d08: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8d0c: ldur            x1, [fp, #-8]
    // 0x8c8d10: mov             x2, x0
    // 0x8c8d14: r0 = _addStaticFactoryConfig()
    //     0x8c8d14: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8d18: r0 = InitLateStaticField(0xec4) // [package:pointycastle/ecc/curves/brainpoolp224r1.dart] ECCurve_brainpoolp224r1::factoryConfig
    //     0x8c8d18: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8d1c: ldr             x0, [x0, #0x1d88]
    //     0x8c8d20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8d24: cmp             w0, w16
    //     0x8c8d28: b.ne            #0x8c8d38
    //     0x8c8d2c: add             x2, PP, #0x18, lsl #12  ; [pp+0x185b0] Field <ECCurve_brainpoolp224r1.factoryConfig>: static late final (offset: 0xec4)
    //     0x8c8d30: ldr             x2, [x2, #0x5b0]
    //     0x8c8d34: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8d38: ldur            x1, [fp, #-8]
    // 0x8c8d3c: mov             x2, x0
    // 0x8c8d40: r0 = _addStaticFactoryConfig()
    //     0x8c8d40: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8d44: r0 = InitLateStaticField(0xec8) // [package:pointycastle/ecc/curves/brainpoolp224t1.dart] ECCurve_brainpoolp224t1::factoryConfig
    //     0x8c8d44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8d48: ldr             x0, [x0, #0x1d90]
    //     0x8c8d4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8d50: cmp             w0, w16
    //     0x8c8d54: b.ne            #0x8c8d64
    //     0x8c8d58: add             x2, PP, #0x18, lsl #12  ; [pp+0x185b8] Field <ECCurve_brainpoolp224t1.factoryConfig>: static late final (offset: 0xec8)
    //     0x8c8d5c: ldr             x2, [x2, #0x5b8]
    //     0x8c8d60: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8d64: ldur            x1, [fp, #-8]
    // 0x8c8d68: mov             x2, x0
    // 0x8c8d6c: r0 = _addStaticFactoryConfig()
    //     0x8c8d6c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8d70: r0 = InitLateStaticField(0xecc) // [package:pointycastle/ecc/curves/brainpoolp256r1.dart] ECCurve_brainpoolp256r1::factoryConfig
    //     0x8c8d70: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8d74: ldr             x0, [x0, #0x1d98]
    //     0x8c8d78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8d7c: cmp             w0, w16
    //     0x8c8d80: b.ne            #0x8c8d90
    //     0x8c8d84: add             x2, PP, #0x18, lsl #12  ; [pp+0x185c0] Field <ECCurve_brainpoolp256r1.factoryConfig>: static late final (offset: 0xecc)
    //     0x8c8d88: ldr             x2, [x2, #0x5c0]
    //     0x8c8d8c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8d90: ldur            x1, [fp, #-8]
    // 0x8c8d94: mov             x2, x0
    // 0x8c8d98: r0 = _addStaticFactoryConfig()
    //     0x8c8d98: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8d9c: r0 = InitLateStaticField(0xed0) // [package:pointycastle/ecc/curves/brainpoolp256t1.dart] ECCurve_brainpoolp256t1::factoryConfig
    //     0x8c8d9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8da0: ldr             x0, [x0, #0x1da0]
    //     0x8c8da4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8da8: cmp             w0, w16
    //     0x8c8dac: b.ne            #0x8c8dbc
    //     0x8c8db0: add             x2, PP, #0x18, lsl #12  ; [pp+0x185c8] Field <ECCurve_brainpoolp256t1.factoryConfig>: static late final (offset: 0xed0)
    //     0x8c8db4: ldr             x2, [x2, #0x5c8]
    //     0x8c8db8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8dbc: ldur            x1, [fp, #-8]
    // 0x8c8dc0: mov             x2, x0
    // 0x8c8dc4: r0 = _addStaticFactoryConfig()
    //     0x8c8dc4: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8dc8: r0 = InitLateStaticField(0xed4) // [package:pointycastle/ecc/curves/brainpoolp320r1.dart] ECCurve_brainpoolp320r1::factoryConfig
    //     0x8c8dc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8dcc: ldr             x0, [x0, #0x1da8]
    //     0x8c8dd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8dd4: cmp             w0, w16
    //     0x8c8dd8: b.ne            #0x8c8de8
    //     0x8c8ddc: add             x2, PP, #0x18, lsl #12  ; [pp+0x185d0] Field <ECCurve_brainpoolp320r1.factoryConfig>: static late final (offset: 0xed4)
    //     0x8c8de0: ldr             x2, [x2, #0x5d0]
    //     0x8c8de4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8de8: ldur            x1, [fp, #-8]
    // 0x8c8dec: mov             x2, x0
    // 0x8c8df0: r0 = _addStaticFactoryConfig()
    //     0x8c8df0: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8df4: r0 = InitLateStaticField(0xed8) // [package:pointycastle/ecc/curves/brainpoolp320t1.dart] ECCurve_brainpoolp320t1::factoryConfig
    //     0x8c8df4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8df8: ldr             x0, [x0, #0x1db0]
    //     0x8c8dfc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8e00: cmp             w0, w16
    //     0x8c8e04: b.ne            #0x8c8e14
    //     0x8c8e08: add             x2, PP, #0x18, lsl #12  ; [pp+0x185d8] Field <ECCurve_brainpoolp320t1.factoryConfig>: static late final (offset: 0xed8)
    //     0x8c8e0c: ldr             x2, [x2, #0x5d8]
    //     0x8c8e10: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8e14: ldur            x1, [fp, #-8]
    // 0x8c8e18: mov             x2, x0
    // 0x8c8e1c: r0 = _addStaticFactoryConfig()
    //     0x8c8e1c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8e20: r0 = InitLateStaticField(0xedc) // [package:pointycastle/ecc/curves/brainpoolp384r1.dart] ECCurve_brainpoolp384r1::factoryConfig
    //     0x8c8e20: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8e24: ldr             x0, [x0, #0x1db8]
    //     0x8c8e28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8e2c: cmp             w0, w16
    //     0x8c8e30: b.ne            #0x8c8e40
    //     0x8c8e34: add             x2, PP, #0x18, lsl #12  ; [pp+0x185e0] Field <ECCurve_brainpoolp384r1.factoryConfig>: static late final (offset: 0xedc)
    //     0x8c8e38: ldr             x2, [x2, #0x5e0]
    //     0x8c8e3c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8e40: ldur            x1, [fp, #-8]
    // 0x8c8e44: mov             x2, x0
    // 0x8c8e48: r0 = _addStaticFactoryConfig()
    //     0x8c8e48: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8e4c: r0 = InitLateStaticField(0xee0) // [package:pointycastle/ecc/curves/brainpoolp384t1.dart] ECCurve_brainpoolp384t1::factoryConfig
    //     0x8c8e4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8e50: ldr             x0, [x0, #0x1dc0]
    //     0x8c8e54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8e58: cmp             w0, w16
    //     0x8c8e5c: b.ne            #0x8c8e6c
    //     0x8c8e60: add             x2, PP, #0x18, lsl #12  ; [pp+0x185e8] Field <ECCurve_brainpoolp384t1.factoryConfig>: static late final (offset: 0xee0)
    //     0x8c8e64: ldr             x2, [x2, #0x5e8]
    //     0x8c8e68: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8e6c: ldur            x1, [fp, #-8]
    // 0x8c8e70: mov             x2, x0
    // 0x8c8e74: r0 = _addStaticFactoryConfig()
    //     0x8c8e74: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8e78: r0 = InitLateStaticField(0xee4) // [package:pointycastle/ecc/curves/brainpoolp512r1.dart] ECCurve_brainpoolp512r1::factoryConfig
    //     0x8c8e78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8e7c: ldr             x0, [x0, #0x1dc8]
    //     0x8c8e80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8e84: cmp             w0, w16
    //     0x8c8e88: b.ne            #0x8c8e98
    //     0x8c8e8c: add             x2, PP, #0x18, lsl #12  ; [pp+0x185f0] Field <ECCurve_brainpoolp512r1.factoryConfig>: static late final (offset: 0xee4)
    //     0x8c8e90: ldr             x2, [x2, #0x5f0]
    //     0x8c8e94: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8e98: ldur            x1, [fp, #-8]
    // 0x8c8e9c: mov             x2, x0
    // 0x8c8ea0: r0 = _addStaticFactoryConfig()
    //     0x8c8ea0: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8ea4: r0 = InitLateStaticField(0xee8) // [package:pointycastle/ecc/curves/brainpoolp512t1.dart] ECCurve_brainpoolp512t1::factoryConfig
    //     0x8c8ea4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8ea8: ldr             x0, [x0, #0x1dd0]
    //     0x8c8eac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8eb0: cmp             w0, w16
    //     0x8c8eb4: b.ne            #0x8c8ec4
    //     0x8c8eb8: add             x2, PP, #0x18, lsl #12  ; [pp+0x185f8] Field <ECCurve_brainpoolp512t1.factoryConfig>: static late final (offset: 0xee8)
    //     0x8c8ebc: ldr             x2, [x2, #0x5f8]
    //     0x8c8ec0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8ec4: ldur            x1, [fp, #-8]
    // 0x8c8ec8: mov             x2, x0
    // 0x8c8ecc: r0 = _addStaticFactoryConfig()
    //     0x8c8ecc: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8ed0: r0 = InitLateStaticField(0xeec) // [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_a.dart] ECCurve_gostr3410_2001_cryptopro_a::factoryConfig
    //     0x8c8ed0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8ed4: ldr             x0, [x0, #0x1dd8]
    //     0x8c8ed8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8edc: cmp             w0, w16
    //     0x8c8ee0: b.ne            #0x8c8ef0
    //     0x8c8ee4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18600] Field <ECCurve_gostr3410_2001_cryptopro_a.factoryConfig>: static late final (offset: 0xeec)
    //     0x8c8ee8: ldr             x2, [x2, #0x600]
    //     0x8c8eec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8ef0: ldur            x1, [fp, #-8]
    // 0x8c8ef4: mov             x2, x0
    // 0x8c8ef8: r0 = _addStaticFactoryConfig()
    //     0x8c8ef8: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8efc: r0 = InitLateStaticField(0xef0) // [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_b.dart] ECCurve_gostr3410_2001_cryptopro_b::factoryConfig
    //     0x8c8efc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8f00: ldr             x0, [x0, #0x1de0]
    //     0x8c8f04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8f08: cmp             w0, w16
    //     0x8c8f0c: b.ne            #0x8c8f1c
    //     0x8c8f10: add             x2, PP, #0x18, lsl #12  ; [pp+0x18608] Field <ECCurve_gostr3410_2001_cryptopro_b.factoryConfig>: static late final (offset: 0xef0)
    //     0x8c8f14: ldr             x2, [x2, #0x608]
    //     0x8c8f18: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8f1c: ldur            x1, [fp, #-8]
    // 0x8c8f20: mov             x2, x0
    // 0x8c8f24: r0 = _addStaticFactoryConfig()
    //     0x8c8f24: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8f28: r0 = InitLateStaticField(0xef4) // [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_c.dart] ECCurve_gostr3410_2001_cryptopro_c::factoryConfig
    //     0x8c8f28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8f2c: ldr             x0, [x0, #0x1de8]
    //     0x8c8f30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8f34: cmp             w0, w16
    //     0x8c8f38: b.ne            #0x8c8f48
    //     0x8c8f3c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18610] Field <ECCurve_gostr3410_2001_cryptopro_c.factoryConfig>: static late final (offset: 0xef4)
    //     0x8c8f40: ldr             x2, [x2, #0x610]
    //     0x8c8f44: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8f48: ldur            x1, [fp, #-8]
    // 0x8c8f4c: mov             x2, x0
    // 0x8c8f50: r0 = _addStaticFactoryConfig()
    //     0x8c8f50: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8f54: r0 = InitLateStaticField(0xef8) // [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_xcha.dart] ECCurve_gostr3410_2001_cryptopro_xcha::factoryConfig
    //     0x8c8f54: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8f58: ldr             x0, [x0, #0x1df0]
    //     0x8c8f5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8f60: cmp             w0, w16
    //     0x8c8f64: b.ne            #0x8c8f74
    //     0x8c8f68: add             x2, PP, #0x18, lsl #12  ; [pp+0x18618] Field <ECCurve_gostr3410_2001_cryptopro_xcha.factoryConfig>: static late final (offset: 0xef8)
    //     0x8c8f6c: ldr             x2, [x2, #0x618]
    //     0x8c8f70: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8f74: ldur            x1, [fp, #-8]
    // 0x8c8f78: mov             x2, x0
    // 0x8c8f7c: r0 = _addStaticFactoryConfig()
    //     0x8c8f7c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8f80: r0 = InitLateStaticField(0xefc) // [package:pointycastle/ecc/curves/gostr3410_2001_cryptopro_xchb.dart] ECCurve_gostr3410_2001_cryptopro_xchb::factoryConfig
    //     0x8c8f80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8f84: ldr             x0, [x0, #0x1df8]
    //     0x8c8f88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8f8c: cmp             w0, w16
    //     0x8c8f90: b.ne            #0x8c8fa0
    //     0x8c8f94: add             x2, PP, #0x18, lsl #12  ; [pp+0x18620] Field <ECCurve_gostr3410_2001_cryptopro_xchb.factoryConfig>: static late final (offset: 0xefc)
    //     0x8c8f98: ldr             x2, [x2, #0x620]
    //     0x8c8f9c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8fa0: ldur            x1, [fp, #-8]
    // 0x8c8fa4: mov             x2, x0
    // 0x8c8fa8: r0 = _addStaticFactoryConfig()
    //     0x8c8fa8: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8fac: r0 = InitLateStaticField(0xf00) // [package:pointycastle/ecc/curves/prime192v1.dart] ECCurve_prime192v1::factoryConfig
    //     0x8c8fac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8fb0: ldr             x0, [x0, #0x1e00]
    //     0x8c8fb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8fb8: cmp             w0, w16
    //     0x8c8fbc: b.ne            #0x8c8fcc
    //     0x8c8fc0: add             x2, PP, #0x18, lsl #12  ; [pp+0x18628] Field <ECCurve_prime192v1.factoryConfig>: static late final (offset: 0xf00)
    //     0x8c8fc4: ldr             x2, [x2, #0x628]
    //     0x8c8fc8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8fcc: ldur            x1, [fp, #-8]
    // 0x8c8fd0: mov             x2, x0
    // 0x8c8fd4: r0 = _addStaticFactoryConfig()
    //     0x8c8fd4: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c8fd8: r0 = InitLateStaticField(0xf04) // [package:pointycastle/ecc/curves/prime192v2.dart] ECCurve_prime192v2::factoryConfig
    //     0x8c8fd8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c8fdc: ldr             x0, [x0, #0x1e08]
    //     0x8c8fe0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c8fe4: cmp             w0, w16
    //     0x8c8fe8: b.ne            #0x8c8ff8
    //     0x8c8fec: add             x2, PP, #0x18, lsl #12  ; [pp+0x18630] Field <ECCurve_prime192v2.factoryConfig>: static late final (offset: 0xf04)
    //     0x8c8ff0: ldr             x2, [x2, #0x630]
    //     0x8c8ff4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c8ff8: ldur            x1, [fp, #-8]
    // 0x8c8ffc: mov             x2, x0
    // 0x8c9000: r0 = _addStaticFactoryConfig()
    //     0x8c9000: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9004: r0 = InitLateStaticField(0xf08) // [package:pointycastle/ecc/curves/prime192v3.dart] ECCurve_prime192v3::factoryConfig
    //     0x8c9004: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9008: ldr             x0, [x0, #0x1e10]
    //     0x8c900c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9010: cmp             w0, w16
    //     0x8c9014: b.ne            #0x8c9024
    //     0x8c9018: add             x2, PP, #0x18, lsl #12  ; [pp+0x18638] Field <ECCurve_prime192v3.factoryConfig>: static late final (offset: 0xf08)
    //     0x8c901c: ldr             x2, [x2, #0x638]
    //     0x8c9020: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9024: ldur            x1, [fp, #-8]
    // 0x8c9028: mov             x2, x0
    // 0x8c902c: r0 = _addStaticFactoryConfig()
    //     0x8c902c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9030: r0 = InitLateStaticField(0xf0c) // [package:pointycastle/ecc/curves/prime239v1.dart] ECCurve_prime239v1::factoryConfig
    //     0x8c9030: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9034: ldr             x0, [x0, #0x1e18]
    //     0x8c9038: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c903c: cmp             w0, w16
    //     0x8c9040: b.ne            #0x8c9050
    //     0x8c9044: add             x2, PP, #0x18, lsl #12  ; [pp+0x18640] Field <ECCurve_prime239v1.factoryConfig>: static late final (offset: 0xf0c)
    //     0x8c9048: ldr             x2, [x2, #0x640]
    //     0x8c904c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9050: ldur            x1, [fp, #-8]
    // 0x8c9054: mov             x2, x0
    // 0x8c9058: r0 = _addStaticFactoryConfig()
    //     0x8c9058: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c905c: r0 = InitLateStaticField(0xf10) // [package:pointycastle/ecc/curves/prime239v2.dart] ECCurve_prime239v2::factoryConfig
    //     0x8c905c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9060: ldr             x0, [x0, #0x1e20]
    //     0x8c9064: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9068: cmp             w0, w16
    //     0x8c906c: b.ne            #0x8c907c
    //     0x8c9070: add             x2, PP, #0x18, lsl #12  ; [pp+0x18648] Field <ECCurve_prime239v2.factoryConfig>: static late final (offset: 0xf10)
    //     0x8c9074: ldr             x2, [x2, #0x648]
    //     0x8c9078: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c907c: ldur            x1, [fp, #-8]
    // 0x8c9080: mov             x2, x0
    // 0x8c9084: r0 = _addStaticFactoryConfig()
    //     0x8c9084: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9088: r0 = InitLateStaticField(0xf14) // [package:pointycastle/ecc/curves/prime239v3.dart] ECCurve_prime239v3::factoryConfig
    //     0x8c9088: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c908c: ldr             x0, [x0, #0x1e28]
    //     0x8c9090: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9094: cmp             w0, w16
    //     0x8c9098: b.ne            #0x8c90a8
    //     0x8c909c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18650] Field <ECCurve_prime239v3.factoryConfig>: static late final (offset: 0xf14)
    //     0x8c90a0: ldr             x2, [x2, #0x650]
    //     0x8c90a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c90a8: ldur            x1, [fp, #-8]
    // 0x8c90ac: mov             x2, x0
    // 0x8c90b0: r0 = _addStaticFactoryConfig()
    //     0x8c90b0: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c90b4: r0 = InitLateStaticField(0xf18) // [package:pointycastle/ecc/curves/prime256v1.dart] ECCurve_prime256v1::factoryConfig
    //     0x8c90b4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c90b8: ldr             x0, [x0, #0x1e30]
    //     0x8c90bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c90c0: cmp             w0, w16
    //     0x8c90c4: b.ne            #0x8c90d4
    //     0x8c90c8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18658] Field <ECCurve_prime256v1.factoryConfig>: static late final (offset: 0xf18)
    //     0x8c90cc: ldr             x2, [x2, #0x658]
    //     0x8c90d0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c90d4: ldur            x1, [fp, #-8]
    // 0x8c90d8: mov             x2, x0
    // 0x8c90dc: r0 = _addStaticFactoryConfig()
    //     0x8c90dc: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c90e0: r0 = InitLateStaticField(0xf1c) // [package:pointycastle/ecc/curves/secp112r1.dart] ECCurve_secp112r1::factoryConfig
    //     0x8c90e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c90e4: ldr             x0, [x0, #0x1e38]
    //     0x8c90e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c90ec: cmp             w0, w16
    //     0x8c90f0: b.ne            #0x8c9100
    //     0x8c90f4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18660] Field <ECCurve_secp112r1.factoryConfig>: static late final (offset: 0xf1c)
    //     0x8c90f8: ldr             x2, [x2, #0x660]
    //     0x8c90fc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9100: ldur            x1, [fp, #-8]
    // 0x8c9104: mov             x2, x0
    // 0x8c9108: r0 = _addStaticFactoryConfig()
    //     0x8c9108: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c910c: r0 = InitLateStaticField(0xf20) // [package:pointycastle/ecc/curves/secp112r2.dart] ECCurve_secp112r2::factoryConfig
    //     0x8c910c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9110: ldr             x0, [x0, #0x1e40]
    //     0x8c9114: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9118: cmp             w0, w16
    //     0x8c911c: b.ne            #0x8c912c
    //     0x8c9120: add             x2, PP, #0x18, lsl #12  ; [pp+0x18668] Field <ECCurve_secp112r2.factoryConfig>: static late final (offset: 0xf20)
    //     0x8c9124: ldr             x2, [x2, #0x668]
    //     0x8c9128: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c912c: ldur            x1, [fp, #-8]
    // 0x8c9130: mov             x2, x0
    // 0x8c9134: r0 = _addStaticFactoryConfig()
    //     0x8c9134: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9138: r0 = InitLateStaticField(0xf24) // [package:pointycastle/ecc/curves/secp128r1.dart] ECCurve_secp128r1::factoryConfig
    //     0x8c9138: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c913c: ldr             x0, [x0, #0x1e48]
    //     0x8c9140: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9144: cmp             w0, w16
    //     0x8c9148: b.ne            #0x8c9158
    //     0x8c914c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18670] Field <ECCurve_secp128r1.factoryConfig>: static late final (offset: 0xf24)
    //     0x8c9150: ldr             x2, [x2, #0x670]
    //     0x8c9154: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9158: ldur            x1, [fp, #-8]
    // 0x8c915c: mov             x2, x0
    // 0x8c9160: r0 = _addStaticFactoryConfig()
    //     0x8c9160: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9164: r0 = InitLateStaticField(0xf28) // [package:pointycastle/ecc/curves/secp128r2.dart] ECCurve_secp128r2::factoryConfig
    //     0x8c9164: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9168: ldr             x0, [x0, #0x1e50]
    //     0x8c916c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9170: cmp             w0, w16
    //     0x8c9174: b.ne            #0x8c9184
    //     0x8c9178: add             x2, PP, #0x18, lsl #12  ; [pp+0x18678] Field <ECCurve_secp128r2.factoryConfig>: static late final (offset: 0xf28)
    //     0x8c917c: ldr             x2, [x2, #0x678]
    //     0x8c9180: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9184: ldur            x1, [fp, #-8]
    // 0x8c9188: mov             x2, x0
    // 0x8c918c: r0 = _addStaticFactoryConfig()
    //     0x8c918c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9190: r0 = InitLateStaticField(0xf2c) // [package:pointycastle/ecc/curves/secp160k1.dart] ECCurve_secp160k1::factoryConfig
    //     0x8c9190: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9194: ldr             x0, [x0, #0x1e58]
    //     0x8c9198: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c919c: cmp             w0, w16
    //     0x8c91a0: b.ne            #0x8c91b0
    //     0x8c91a4: add             x2, PP, #0x18, lsl #12  ; [pp+0x18680] Field <ECCurve_secp160k1.factoryConfig>: static late final (offset: 0xf2c)
    //     0x8c91a8: ldr             x2, [x2, #0x680]
    //     0x8c91ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c91b0: ldur            x1, [fp, #-8]
    // 0x8c91b4: mov             x2, x0
    // 0x8c91b8: r0 = _addStaticFactoryConfig()
    //     0x8c91b8: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c91bc: r0 = InitLateStaticField(0xf30) // [package:pointycastle/ecc/curves/secp160r1.dart] ECCurve_secp160r1::factoryConfig
    //     0x8c91bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c91c0: ldr             x0, [x0, #0x1e60]
    //     0x8c91c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c91c8: cmp             w0, w16
    //     0x8c91cc: b.ne            #0x8c91dc
    //     0x8c91d0: add             x2, PP, #0x18, lsl #12  ; [pp+0x18688] Field <ECCurve_secp160r1.factoryConfig>: static late final (offset: 0xf30)
    //     0x8c91d4: ldr             x2, [x2, #0x688]
    //     0x8c91d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c91dc: ldur            x1, [fp, #-8]
    // 0x8c91e0: mov             x2, x0
    // 0x8c91e4: r0 = _addStaticFactoryConfig()
    //     0x8c91e4: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c91e8: r0 = InitLateStaticField(0xf34) // [package:pointycastle/ecc/curves/secp160r2.dart] ECCurve_secp160r2::factoryConfig
    //     0x8c91e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c91ec: ldr             x0, [x0, #0x1e68]
    //     0x8c91f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c91f4: cmp             w0, w16
    //     0x8c91f8: b.ne            #0x8c9208
    //     0x8c91fc: add             x2, PP, #0x18, lsl #12  ; [pp+0x18690] Field <ECCurve_secp160r2.factoryConfig>: static late final (offset: 0xf34)
    //     0x8c9200: ldr             x2, [x2, #0x690]
    //     0x8c9204: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9208: ldur            x1, [fp, #-8]
    // 0x8c920c: mov             x2, x0
    // 0x8c9210: r0 = _addStaticFactoryConfig()
    //     0x8c9210: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9214: r0 = InitLateStaticField(0xf38) // [package:pointycastle/ecc/curves/secp192k1.dart] ECCurve_secp192k1::factoryConfig
    //     0x8c9214: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9218: ldr             x0, [x0, #0x1e70]
    //     0x8c921c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9220: cmp             w0, w16
    //     0x8c9224: b.ne            #0x8c9234
    //     0x8c9228: add             x2, PP, #0x18, lsl #12  ; [pp+0x18698] Field <ECCurve_secp192k1.factoryConfig>: static late final (offset: 0xf38)
    //     0x8c922c: ldr             x2, [x2, #0x698]
    //     0x8c9230: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9234: ldur            x1, [fp, #-8]
    // 0x8c9238: mov             x2, x0
    // 0x8c923c: r0 = _addStaticFactoryConfig()
    //     0x8c923c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9240: r0 = InitLateStaticField(0xf3c) // [package:pointycastle/ecc/curves/secp192r1.dart] ECCurve_secp192r1::factoryConfig
    //     0x8c9240: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9244: ldr             x0, [x0, #0x1e78]
    //     0x8c9248: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c924c: cmp             w0, w16
    //     0x8c9250: b.ne            #0x8c9260
    //     0x8c9254: add             x2, PP, #0x18, lsl #12  ; [pp+0x186a0] Field <ECCurve_secp192r1.factoryConfig>: static late final (offset: 0xf3c)
    //     0x8c9258: ldr             x2, [x2, #0x6a0]
    //     0x8c925c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9260: ldur            x1, [fp, #-8]
    // 0x8c9264: mov             x2, x0
    // 0x8c9268: r0 = _addStaticFactoryConfig()
    //     0x8c9268: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c926c: r0 = InitLateStaticField(0xf40) // [package:pointycastle/ecc/curves/secp224k1.dart] ECCurve_secp224k1::factoryConfig
    //     0x8c926c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9270: ldr             x0, [x0, #0x1e80]
    //     0x8c9274: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9278: cmp             w0, w16
    //     0x8c927c: b.ne            #0x8c928c
    //     0x8c9280: add             x2, PP, #0x18, lsl #12  ; [pp+0x186a8] Field <ECCurve_secp224k1.factoryConfig>: static late final (offset: 0xf40)
    //     0x8c9284: ldr             x2, [x2, #0x6a8]
    //     0x8c9288: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c928c: ldur            x1, [fp, #-8]
    // 0x8c9290: mov             x2, x0
    // 0x8c9294: r0 = _addStaticFactoryConfig()
    //     0x8c9294: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9298: r0 = InitLateStaticField(0xf44) // [package:pointycastle/ecc/curves/secp224r1.dart] ECCurve_secp224r1::factoryConfig
    //     0x8c9298: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c929c: ldr             x0, [x0, #0x1e88]
    //     0x8c92a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c92a4: cmp             w0, w16
    //     0x8c92a8: b.ne            #0x8c92b8
    //     0x8c92ac: add             x2, PP, #0x18, lsl #12  ; [pp+0x186b0] Field <ECCurve_secp224r1.factoryConfig>: static late final (offset: 0xf44)
    //     0x8c92b0: ldr             x2, [x2, #0x6b0]
    //     0x8c92b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c92b8: ldur            x1, [fp, #-8]
    // 0x8c92bc: mov             x2, x0
    // 0x8c92c0: r0 = _addStaticFactoryConfig()
    //     0x8c92c0: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c92c4: r0 = InitLateStaticField(0xf48) // [package:pointycastle/ecc/curves/secp256k1.dart] ECCurve_secp256k1::factoryConfig
    //     0x8c92c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c92c8: ldr             x0, [x0, #0x1e90]
    //     0x8c92cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c92d0: cmp             w0, w16
    //     0x8c92d4: b.ne            #0x8c92e4
    //     0x8c92d8: add             x2, PP, #0x18, lsl #12  ; [pp+0x186b8] Field <ECCurve_secp256k1.factoryConfig>: static late final (offset: 0xf48)
    //     0x8c92dc: ldr             x2, [x2, #0x6b8]
    //     0x8c92e0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c92e4: ldur            x1, [fp, #-8]
    // 0x8c92e8: mov             x2, x0
    // 0x8c92ec: r0 = _addStaticFactoryConfig()
    //     0x8c92ec: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c92f0: r0 = InitLateStaticField(0xf4c) // [package:pointycastle/ecc/curves/secp256r1.dart] ECCurve_secp256r1::factoryConfig
    //     0x8c92f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c92f4: ldr             x0, [x0, #0x1e98]
    //     0x8c92f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c92fc: cmp             w0, w16
    //     0x8c9300: b.ne            #0x8c9310
    //     0x8c9304: add             x2, PP, #0x18, lsl #12  ; [pp+0x186c0] Field <ECCurve_secp256r1.factoryConfig>: static late final (offset: 0xf4c)
    //     0x8c9308: ldr             x2, [x2, #0x6c0]
    //     0x8c930c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9310: ldur            x1, [fp, #-8]
    // 0x8c9314: mov             x2, x0
    // 0x8c9318: r0 = _addStaticFactoryConfig()
    //     0x8c9318: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c931c: r0 = InitLateStaticField(0xf50) // [package:pointycastle/ecc/curves/secp384r1.dart] ECCurve_secp384r1::factoryConfig
    //     0x8c931c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c9320: ldr             x0, [x0, #0x1ea0]
    //     0x8c9324: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9328: cmp             w0, w16
    //     0x8c932c: b.ne            #0x8c933c
    //     0x8c9330: add             x2, PP, #0x18, lsl #12  ; [pp+0x186c8] Field <ECCurve_secp384r1.factoryConfig>: static late final (offset: 0xf50)
    //     0x8c9334: ldr             x2, [x2, #0x6c8]
    //     0x8c9338: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c933c: ldur            x1, [fp, #-8]
    // 0x8c9340: mov             x2, x0
    // 0x8c9344: r0 = _addStaticFactoryConfig()
    //     0x8c9344: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9348: r0 = InitLateStaticField(0xf54) // [package:pointycastle/ecc/curves/secp521r1.dart] ECCurve_secp521r1::factoryConfig
    //     0x8c9348: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c934c: ldr             x0, [x0, #0x1ea8]
    //     0x8c9350: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c9354: cmp             w0, w16
    //     0x8c9358: b.ne            #0x8c9368
    //     0x8c935c: add             x2, PP, #0x18, lsl #12  ; [pp+0x186d0] Field <ECCurve_secp521r1.factoryConfig>: static late final (offset: 0xf54)
    //     0x8c9360: ldr             x2, [x2, #0x6d0]
    //     0x8c9364: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c9368: ldur            x1, [fp, #-8]
    // 0x8c936c: mov             x2, x0
    // 0x8c9370: r0 = _addStaticFactoryConfig()
    //     0x8c9370: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8c9374: r0 = Null
    //     0x8c9374: mov             x0, NULL
    // 0x8c9378: LeaveFrame
    //     0x8c9378: mov             SP, fp
    //     0x8c937c: ldp             fp, lr, [SP], #0x10
    // 0x8c9380: ret
    //     0x8c9380: ret             
    // 0x8c9384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9384: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9388: b               #0x8c8c68
  }
  static _ _registerDigests(/* No info */) {
    // ** addr: 0x8d56dc, size: 0x3d0
    // 0x8d56dc: EnterFrame
    //     0x8d56dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8d56e0: mov             fp, SP
    // 0x8d56e4: AllocStack(0x8)
    //     0x8d56e4: sub             SP, SP, #8
    // 0x8d56e8: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8d56e8: stur            x1, [fp, #-8]
    // 0x8d56ec: CheckStackOverflow
    //     0x8d56ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d56f0: cmp             SP, x16
    //     0x8d56f4: b.ls            #0x8d5aa4
    // 0x8d56f8: r0 = InitLateStaticField(0xdc4) // [package:pointycastle/digests/blake2b.dart] Blake2bDigest::factoryConfig
    //     0x8d56f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d56fc: ldr             x0, [x0, #0x1b88]
    //     0x8d5700: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5704: cmp             w0, w16
    //     0x8d5708: b.ne            #0x8d5718
    //     0x8d570c: add             x2, PP, #0x19, lsl #12  ; [pp+0x192d0] Field <Blake2bDigest.factoryConfig>: static late final (offset: 0xdc4)
    //     0x8d5710: ldr             x2, [x2, #0x2d0]
    //     0x8d5714: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5718: ldur            x1, [fp, #-8]
    // 0x8d571c: mov             x2, x0
    // 0x8d5720: r0 = _addStaticFactoryConfig()
    //     0x8d5720: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5724: r0 = InitLateStaticField(0xdec) // [package:pointycastle/digests/md2.dart] MD2Digest::factoryConfig
    //     0x8d5724: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5728: ldr             x0, [x0, #0x1bd8]
    //     0x8d572c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5730: cmp             w0, w16
    //     0x8d5734: b.ne            #0x8d5744
    //     0x8d5738: add             x2, PP, #0x19, lsl #12  ; [pp+0x192d8] Field <MD2Digest.factoryConfig>: static late final (offset: 0xdec)
    //     0x8d573c: ldr             x2, [x2, #0x2d8]
    //     0x8d5740: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5744: ldur            x1, [fp, #-8]
    // 0x8d5748: mov             x2, x0
    // 0x8d574c: r0 = _addStaticFactoryConfig()
    //     0x8d574c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5750: r0 = InitLateStaticField(0xdf0) // [package:pointycastle/digests/md4.dart] MD4Digest::factoryConfig
    //     0x8d5750: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5754: ldr             x0, [x0, #0x1be0]
    //     0x8d5758: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d575c: cmp             w0, w16
    //     0x8d5760: b.ne            #0x8d5770
    //     0x8d5764: add             x2, PP, #0x19, lsl #12  ; [pp+0x192e0] Field <MD4Digest.factoryConfig>: static late final (offset: 0xdf0)
    //     0x8d5768: ldr             x2, [x2, #0x2e0]
    //     0x8d576c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5770: ldur            x1, [fp, #-8]
    // 0x8d5774: mov             x2, x0
    // 0x8d5778: r0 = _addStaticFactoryConfig()
    //     0x8d5778: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d577c: r0 = InitLateStaticField(0xdf4) // [package:pointycastle/digests/md5.dart] MD5Digest::factoryConfig
    //     0x8d577c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5780: ldr             x0, [x0, #0x1be8]
    //     0x8d5784: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5788: cmp             w0, w16
    //     0x8d578c: b.ne            #0x8d579c
    //     0x8d5790: add             x2, PP, #0x19, lsl #12  ; [pp+0x192e8] Field <MD5Digest.factoryConfig>: static late final (offset: 0xdf4)
    //     0x8d5794: ldr             x2, [x2, #0x2e8]
    //     0x8d5798: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d579c: ldur            x1, [fp, #-8]
    // 0x8d57a0: mov             x2, x0
    // 0x8d57a4: r0 = _addStaticFactoryConfig()
    //     0x8d57a4: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d57a8: r0 = InitLateStaticField(0xdf8) // [package:pointycastle/digests/ripemd128.dart] RIPEMD128Digest::factoryConfig
    //     0x8d57a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d57ac: ldr             x0, [x0, #0x1bf0]
    //     0x8d57b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d57b4: cmp             w0, w16
    //     0x8d57b8: b.ne            #0x8d57c8
    //     0x8d57bc: add             x2, PP, #0x19, lsl #12  ; [pp+0x192f0] Field <RIPEMD128Digest.factoryConfig>: static late final (offset: 0xdf8)
    //     0x8d57c0: ldr             x2, [x2, #0x2f0]
    //     0x8d57c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d57c8: ldur            x1, [fp, #-8]
    // 0x8d57cc: mov             x2, x0
    // 0x8d57d0: r0 = _addStaticFactoryConfig()
    //     0x8d57d0: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d57d4: r0 = InitLateStaticField(0xdfc) // [package:pointycastle/digests/ripemd160.dart] RIPEMD160Digest::factoryConfig
    //     0x8d57d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d57d8: ldr             x0, [x0, #0x1bf8]
    //     0x8d57dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d57e0: cmp             w0, w16
    //     0x8d57e4: b.ne            #0x8d57f4
    //     0x8d57e8: add             x2, PP, #0x19, lsl #12  ; [pp+0x192f8] Field <RIPEMD160Digest.factoryConfig>: static late final (offset: 0xdfc)
    //     0x8d57ec: ldr             x2, [x2, #0x2f8]
    //     0x8d57f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d57f4: ldur            x1, [fp, #-8]
    // 0x8d57f8: mov             x2, x0
    // 0x8d57fc: r0 = _addStaticFactoryConfig()
    //     0x8d57fc: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5800: r0 = InitLateStaticField(0xe00) // [package:pointycastle/digests/ripemd256.dart] RIPEMD256Digest::factoryConfig
    //     0x8d5800: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5804: ldr             x0, [x0, #0x1c00]
    //     0x8d5808: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d580c: cmp             w0, w16
    //     0x8d5810: b.ne            #0x8d5820
    //     0x8d5814: add             x2, PP, #0x19, lsl #12  ; [pp+0x19300] Field <RIPEMD256Digest.factoryConfig>: static late final (offset: 0xe00)
    //     0x8d5818: ldr             x2, [x2, #0x300]
    //     0x8d581c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5820: ldur            x1, [fp, #-8]
    // 0x8d5824: mov             x2, x0
    // 0x8d5828: r0 = _addStaticFactoryConfig()
    //     0x8d5828: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d582c: r0 = InitLateStaticField(0xe04) // [package:pointycastle/digests/ripemd320.dart] RIPEMD320Digest::factoryConfig
    //     0x8d582c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5830: ldr             x0, [x0, #0x1c08]
    //     0x8d5834: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5838: cmp             w0, w16
    //     0x8d583c: b.ne            #0x8d584c
    //     0x8d5840: add             x2, PP, #0x19, lsl #12  ; [pp+0x19308] Field <RIPEMD320Digest.factoryConfig>: static late final (offset: 0xe04)
    //     0x8d5844: ldr             x2, [x2, #0x308]
    //     0x8d5848: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d584c: ldur            x1, [fp, #-8]
    // 0x8d5850: mov             x2, x0
    // 0x8d5854: r0 = _addStaticFactoryConfig()
    //     0x8d5854: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5858: r0 = InitLateStaticField(0xe08) // [package:pointycastle/digests/sha1.dart] SHA1Digest::factoryConfig
    //     0x8d5858: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d585c: ldr             x0, [x0, #0x1c10]
    //     0x8d5860: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5864: cmp             w0, w16
    //     0x8d5868: b.ne            #0x8d5878
    //     0x8d586c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19310] Field <SHA1Digest.factoryConfig>: static late final (offset: 0xe08)
    //     0x8d5870: ldr             x2, [x2, #0x310]
    //     0x8d5874: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5878: ldur            x1, [fp, #-8]
    // 0x8d587c: mov             x2, x0
    // 0x8d5880: r0 = _addStaticFactoryConfig()
    //     0x8d5880: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5884: r0 = InitLateStaticField(0xdd8) // [package:pointycastle/digests/sha3.dart] SHA3Digest::factoryConfig
    //     0x8d5884: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5888: ldr             x0, [x0, #0x1bb0]
    //     0x8d588c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5890: cmp             w0, w16
    //     0x8d5894: b.ne            #0x8d58a4
    //     0x8d5898: add             x2, PP, #0x19, lsl #12  ; [pp+0x19318] Field <SHA3Digest.factoryConfig>: static late final (offset: 0xdd8)
    //     0x8d589c: ldr             x2, [x2, #0x318]
    //     0x8d58a0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d58a4: ldur            x1, [fp, #-8]
    // 0x8d58a8: mov             x2, x0
    // 0x8d58ac: r0 = _addDynamicFactoryConfig()
    //     0x8d58ac: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8d58b0: r0 = InitLateStaticField(0xdd0) // [package:pointycastle/digests/keccak.dart] KeccakDigest::factoryConfig
    //     0x8d58b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d58b4: ldr             x0, [x0, #0x1ba0]
    //     0x8d58b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d58bc: cmp             w0, w16
    //     0x8d58c0: b.ne            #0x8d58d0
    //     0x8d58c4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19320] Field <KeccakDigest.factoryConfig>: static late final (offset: 0xdd0)
    //     0x8d58c8: ldr             x2, [x2, #0x320]
    //     0x8d58cc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d58d0: ldur            x1, [fp, #-8]
    // 0x8d58d4: mov             x2, x0
    // 0x8d58d8: r0 = _addDynamicFactoryConfig()
    //     0x8d58d8: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8d58dc: r0 = InitLateStaticField(0xe0c) // [package:pointycastle/digests/sha224.dart] SHA224Digest::factoryConfig
    //     0x8d58dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d58e0: ldr             x0, [x0, #0x1c18]
    //     0x8d58e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d58e8: cmp             w0, w16
    //     0x8d58ec: b.ne            #0x8d58fc
    //     0x8d58f0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19328] Field <SHA224Digest.factoryConfig>: static late final (offset: 0xe0c)
    //     0x8d58f4: ldr             x2, [x2, #0x328]
    //     0x8d58f8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d58fc: ldur            x1, [fp, #-8]
    // 0x8d5900: mov             x2, x0
    // 0x8d5904: r0 = _addStaticFactoryConfig()
    //     0x8d5904: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5908: r0 = InitLateStaticField(0xe10) // [package:pointycastle/digests/sha256.dart] SHA256Digest::factoryConfig
    //     0x8d5908: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d590c: ldr             x0, [x0, #0x1c20]
    //     0x8d5910: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5914: cmp             w0, w16
    //     0x8d5918: b.ne            #0x8d5928
    //     0x8d591c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19330] Field <SHA256Digest.factoryConfig>: static late final (offset: 0xe10)
    //     0x8d5920: ldr             x2, [x2, #0x330]
    //     0x8d5924: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5928: ldur            x1, [fp, #-8]
    // 0x8d592c: mov             x2, x0
    // 0x8d5930: r0 = _addStaticFactoryConfig()
    //     0x8d5930: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5934: r0 = InitLateStaticField(0xe14) // [package:pointycastle/digests/sha384.dart] SHA384Digest::factoryConfig
    //     0x8d5934: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5938: ldr             x0, [x0, #0x1c28]
    //     0x8d593c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5940: cmp             w0, w16
    //     0x8d5944: b.ne            #0x8d5954
    //     0x8d5948: add             x2, PP, #0x19, lsl #12  ; [pp+0x19338] Field <SHA384Digest.factoryConfig>: static late final (offset: 0xe14)
    //     0x8d594c: ldr             x2, [x2, #0x338]
    //     0x8d5950: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5954: ldur            x1, [fp, #-8]
    // 0x8d5958: mov             x2, x0
    // 0x8d595c: r0 = _addStaticFactoryConfig()
    //     0x8d595c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5960: r0 = InitLateStaticField(0xe20) // [package:pointycastle/digests/sha512.dart] SHA512Digest::factoryConfig
    //     0x8d5960: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5964: ldr             x0, [x0, #0x1c40]
    //     0x8d5968: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d596c: cmp             w0, w16
    //     0x8d5970: b.ne            #0x8d5980
    //     0x8d5974: add             x2, PP, #0x19, lsl #12  ; [pp+0x19340] Field <SHA512Digest.factoryConfig>: static late final (offset: 0xe20)
    //     0x8d5978: ldr             x2, [x2, #0x340]
    //     0x8d597c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5980: ldur            x1, [fp, #-8]
    // 0x8d5984: mov             x2, x0
    // 0x8d5988: r0 = _addStaticFactoryConfig()
    //     0x8d5988: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d598c: r0 = InitLateStaticField(0xe28) // [package:pointycastle/digests/sha512t.dart] SHA512tDigest::factoryConfig
    //     0x8d598c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5990: ldr             x0, [x0, #0x1c50]
    //     0x8d5994: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5998: cmp             w0, w16
    //     0x8d599c: b.ne            #0x8d59ac
    //     0x8d59a0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19348] Field <SHA512tDigest.factoryConfig>: static late final (offset: 0xe28)
    //     0x8d59a4: ldr             x2, [x2, #0x348]
    //     0x8d59a8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d59ac: ldur            x1, [fp, #-8]
    // 0x8d59b0: mov             x2, x0
    // 0x8d59b4: r0 = _addDynamicFactoryConfig()
    //     0x8d59b4: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8d59b8: r0 = InitLateStaticField(0xe30) // [package:pointycastle/digests/tiger.dart] TigerDigest::factoryConfig
    //     0x8d59b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d59bc: ldr             x0, [x0, #0x1c60]
    //     0x8d59c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d59c4: cmp             w0, w16
    //     0x8d59c8: b.ne            #0x8d59d8
    //     0x8d59cc: add             x2, PP, #0x19, lsl #12  ; [pp+0x19350] Field <TigerDigest.factoryConfig>: static late final (offset: 0xe30)
    //     0x8d59d0: ldr             x2, [x2, #0x350]
    //     0x8d59d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d59d8: ldur            x1, [fp, #-8]
    // 0x8d59dc: mov             x2, x0
    // 0x8d59e0: r0 = _addStaticFactoryConfig()
    //     0x8d59e0: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d59e4: r0 = InitLateStaticField(0xe34) // [package:pointycastle/digests/whirlpool.dart] WhirlpoolDigest::factoryConfig
    //     0x8d59e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d59e8: ldr             x0, [x0, #0x1c68]
    //     0x8d59ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d59f0: cmp             w0, w16
    //     0x8d59f4: b.ne            #0x8d5a04
    //     0x8d59f8: add             x2, PP, #0x19, lsl #12  ; [pp+0x19358] Field <WhirlpoolDigest.factoryConfig>: static late final (offset: 0xe34)
    //     0x8d59fc: ldr             x2, [x2, #0x358]
    //     0x8d5a00: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5a04: ldur            x1, [fp, #-8]
    // 0x8d5a08: mov             x2, x0
    // 0x8d5a0c: r0 = _addStaticFactoryConfig()
    //     0x8d5a0c: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5a10: r0 = InitLateStaticField(0xde0) // [package:pointycastle/digests/shake.dart] SHAKEDigest::factoryConfig
    //     0x8d5a10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5a14: ldr             x0, [x0, #0x1bc0]
    //     0x8d5a18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5a1c: cmp             w0, w16
    //     0x8d5a20: b.ne            #0x8d5a30
    //     0x8d5a24: add             x2, PP, #0x19, lsl #12  ; [pp+0x19360] Field <SHAKEDigest.factoryConfig>: static late final (offset: 0xde0)
    //     0x8d5a28: ldr             x2, [x2, #0x360]
    //     0x8d5a2c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5a30: ldur            x1, [fp, #-8]
    // 0x8d5a34: mov             x2, x0
    // 0x8d5a38: r0 = _addDynamicFactoryConfig()
    //     0x8d5a38: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8d5a3c: r0 = InitLateStaticField(0xde8) // [package:pointycastle/digests/cshake.dart] CSHAKEDigest::factoryConfig
    //     0x8d5a3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5a40: ldr             x0, [x0, #0x1bd0]
    //     0x8d5a44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5a48: cmp             w0, w16
    //     0x8d5a4c: b.ne            #0x8d5a5c
    //     0x8d5a50: add             x2, PP, #0x19, lsl #12  ; [pp+0x19368] Field <CSHAKEDigest.factoryConfig>: static late final (offset: 0xde8)
    //     0x8d5a54: ldr             x2, [x2, #0x368]
    //     0x8d5a58: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5a5c: ldur            x1, [fp, #-8]
    // 0x8d5a60: mov             x2, x0
    // 0x8d5a64: r0 = _addDynamicFactoryConfig()
    //     0x8d5a64: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8d5a68: r0 = InitLateStaticField(0xe38) // [package:pointycastle/digests/sm3.dart] SM3Digest::factoryConfig
    //     0x8d5a68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d5a6c: ldr             x0, [x0, #0x1c70]
    //     0x8d5a70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d5a74: cmp             w0, w16
    //     0x8d5a78: b.ne            #0x8d5a88
    //     0x8d5a7c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19370] Field <SM3Digest.factoryConfig>: static late final (offset: 0xe38)
    //     0x8d5a80: ldr             x2, [x2, #0x370]
    //     0x8d5a84: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d5a88: ldur            x1, [fp, #-8]
    // 0x8d5a8c: mov             x2, x0
    // 0x8d5a90: r0 = _addStaticFactoryConfig()
    //     0x8d5a90: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8d5a94: r0 = Null
    //     0x8d5a94: mov             x0, NULL
    // 0x8d5a98: LeaveFrame
    //     0x8d5a98: mov             SP, fp
    //     0x8d5a9c: ldp             fp, lr, [SP], #0x10
    // 0x8d5aa0: ret
    //     0x8d5aa0: ret             
    // 0x8d5aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d5aa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d5aa8: b               #0x8d56f8
  }
  static _ _registerBlockCiphers(/* No info */) {
    // ** addr: 0x8e4b5c, size: 0x270
    // 0x8e4b5c: EnterFrame
    //     0x8e4b5c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e4b60: mov             fp, SP
    // 0x8e4b64: AllocStack(0x8)
    //     0x8e4b64: sub             SP, SP, #8
    // 0x8e4b68: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8e4b68: stur            x1, [fp, #-8]
    // 0x8e4b6c: CheckStackOverflow
    //     0x8e4b6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e4b70: cmp             SP, x16
    //     0x8e4b74: b.ls            #0x8e4dc4
    // 0x8e4b78: r0 = InitLateStaticField(0xd54) // [package:pointycastle/block/aes.dart] AESEngine::factoryConfig
    //     0x8e4b78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4b7c: ldr             x0, [x0, #0x1aa8]
    //     0x8e4b80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4b84: cmp             w0, w16
    //     0x8e4b88: b.ne            #0x8e4b98
    //     0x8e4b8c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a10] Field <AESEngine.factoryConfig>: static late final (offset: 0xd54)
    //     0x8e4b90: ldr             x2, [x2, #0xa10]
    //     0x8e4b94: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4b98: ldur            x1, [fp, #-8]
    // 0x8e4b9c: mov             x2, x0
    // 0x8e4ba0: r0 = _addStaticFactoryConfig()
    //     0x8e4ba0: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8e4ba4: r0 = InitLateStaticField(0xd94) // [package:pointycastle/block/rc2_engine.dart] RC2Engine::factoryConfig
    //     0x8e4ba4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4ba8: ldr             x0, [x0, #0x1b28]
    //     0x8e4bac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4bb0: cmp             w0, w16
    //     0x8e4bb4: b.ne            #0x8e4bc4
    //     0x8e4bb8: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a18] Field <RC2Engine.factoryConfig>: static late final (offset: 0xd94)
    //     0x8e4bbc: ldr             x2, [x2, #0xa18]
    //     0x8e4bc0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4bc4: ldur            x1, [fp, #-8]
    // 0x8e4bc8: mov             x2, x0
    // 0x8e4bcc: r0 = _addStaticFactoryConfig()
    //     0x8e4bcc: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8e4bd0: r0 = InitLateStaticField(0xd58) // [package:pointycastle/block/desede_engine.dart] DESedeEngine::factoryConfig
    //     0x8e4bd0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4bd4: ldr             x0, [x0, #0x1ab0]
    //     0x8e4bd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4bdc: cmp             w0, w16
    //     0x8e4be0: b.ne            #0x8e4bf0
    //     0x8e4be4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a20] Field <DESedeEngine.factoryConfig>: static late final (offset: 0xd58)
    //     0x8e4be8: ldr             x2, [x2, #0xa20]
    //     0x8e4bec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4bf0: ldur            x1, [fp, #-8]
    // 0x8e4bf4: mov             x2, x0
    // 0x8e4bf8: r0 = _addStaticFactoryConfig()
    //     0x8e4bf8: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8e4bfc: r0 = InitLateStaticField(0xd9c) // [package:pointycastle/block/modes/cbc.dart] CBCBlockCipher::factoryConfig
    //     0x8e4bfc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4c00: ldr             x0, [x0, #0x1b38]
    //     0x8e4c04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4c08: cmp             w0, w16
    //     0x8e4c0c: b.ne            #0x8e4c1c
    //     0x8e4c10: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a28] Field <CBCBlockCipher.factoryConfig>: static late final (offset: 0xd9c)
    //     0x8e4c14: ldr             x2, [x2, #0xa28]
    //     0x8e4c18: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4c1c: ldur            x1, [fp, #-8]
    // 0x8e4c20: mov             x2, x0
    // 0x8e4c24: r0 = _addDynamicFactoryConfig()
    //     0x8e4c24: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4c28: r0 = InitLateStaticField(0xda0) // [package:pointycastle/block/modes/cfb.dart] CFBBlockCipher::factoryConfig
    //     0x8e4c28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4c2c: ldr             x0, [x0, #0x1b40]
    //     0x8e4c30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4c34: cmp             w0, w16
    //     0x8e4c38: b.ne            #0x8e4c48
    //     0x8e4c3c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a30] Field <CFBBlockCipher.factoryConfig>: static late final (offset: 0xda0)
    //     0x8e4c40: ldr             x2, [x2, #0xa30]
    //     0x8e4c44: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4c48: ldur            x1, [fp, #-8]
    // 0x8e4c4c: mov             x2, x0
    // 0x8e4c50: r0 = _addDynamicFactoryConfig()
    //     0x8e4c50: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4c54: r0 = InitLateStaticField(0xda4) // [package:pointycastle/block/modes/ctr.dart] CTRBlockCipher::factoryConfig
    //     0x8e4c54: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4c58: ldr             x0, [x0, #0x1b48]
    //     0x8e4c5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4c60: cmp             w0, w16
    //     0x8e4c64: b.ne            #0x8e4c74
    //     0x8e4c68: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a38] Field <CTRBlockCipher.factoryConfig>: static late final (offset: 0xda4)
    //     0x8e4c6c: ldr             x2, [x2, #0xa38]
    //     0x8e4c70: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4c74: ldur            x1, [fp, #-8]
    // 0x8e4c78: mov             x2, x0
    // 0x8e4c7c: r0 = _addDynamicFactoryConfig()
    //     0x8e4c7c: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4c80: r0 = InitLateStaticField(0xda8) // [package:pointycastle/block/modes/ecb.dart] ECBBlockCipher::factoryConfig
    //     0x8e4c80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4c84: ldr             x0, [x0, #0x1b50]
    //     0x8e4c88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4c8c: cmp             w0, w16
    //     0x8e4c90: b.ne            #0x8e4ca0
    //     0x8e4c94: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a40] Field <ECBBlockCipher.factoryConfig>: static late final (offset: 0xda8)
    //     0x8e4c98: ldr             x2, [x2, #0xa40]
    //     0x8e4c9c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4ca0: ldur            x1, [fp, #-8]
    // 0x8e4ca4: mov             x2, x0
    // 0x8e4ca8: r0 = _addDynamicFactoryConfig()
    //     0x8e4ca8: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4cac: r0 = InitLateStaticField(0xdac) // [package:pointycastle/block/modes/gctr.dart] GCTRBlockCipher::factoryConfig
    //     0x8e4cac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4cb0: ldr             x0, [x0, #0x1b58]
    //     0x8e4cb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4cb8: cmp             w0, w16
    //     0x8e4cbc: b.ne            #0x8e4ccc
    //     0x8e4cc0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a48] Field <GCTRBlockCipher.factoryConfig>: static late final (offset: 0xdac)
    //     0x8e4cc4: ldr             x2, [x2, #0xa48]
    //     0x8e4cc8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4ccc: ldur            x1, [fp, #-8]
    // 0x8e4cd0: mov             x2, x0
    // 0x8e4cd4: r0 = _addDynamicFactoryConfig()
    //     0x8e4cd4: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4cd8: r0 = InitLateStaticField(0xdb0) // [package:pointycastle/block/modes/ofb.dart] OFBBlockCipher::factoryConfig
    //     0x8e4cd8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4cdc: ldr             x0, [x0, #0x1b60]
    //     0x8e4ce0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4ce4: cmp             w0, w16
    //     0x8e4ce8: b.ne            #0x8e4cf8
    //     0x8e4cec: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a50] Field <OFBBlockCipher.factoryConfig>: static late final (offset: 0xdb0)
    //     0x8e4cf0: ldr             x2, [x2, #0xa50]
    //     0x8e4cf4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4cf8: ldur            x1, [fp, #-8]
    // 0x8e4cfc: mov             x2, x0
    // 0x8e4d00: r0 = _addDynamicFactoryConfig()
    //     0x8e4d00: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4d04: r0 = InitLateStaticField(0xdbc) // [package:pointycastle/block/modes/sic.dart] SICBlockCipher::factoryConfig
    //     0x8e4d04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4d08: ldr             x0, [x0, #0x1b78]
    //     0x8e4d0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4d10: cmp             w0, w16
    //     0x8e4d14: b.ne            #0x8e4d24
    //     0x8e4d18: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a58] Field <SICBlockCipher.factoryConfig>: static late final (offset: 0xdbc)
    //     0x8e4d1c: ldr             x2, [x2, #0xa58]
    //     0x8e4d20: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4d24: ldur            x1, [fp, #-8]
    // 0x8e4d28: mov             x2, x0
    // 0x8e4d2c: r0 = _addDynamicFactoryConfig()
    //     0x8e4d2c: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4d30: r0 = InitLateStaticField(0xdb4) // [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::factoryConfig
    //     0x8e4d30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4d34: ldr             x0, [x0, #0x1b68]
    //     0x8e4d38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4d3c: cmp             w0, w16
    //     0x8e4d40: b.ne            #0x8e4d50
    //     0x8e4d44: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a60] Field <GCMBlockCipher.factoryConfig>: static late final (offset: 0xdb4)
    //     0x8e4d48: ldr             x2, [x2, #0xa60]
    //     0x8e4d4c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4d50: ldur            x1, [fp, #-8]
    // 0x8e4d54: mov             x2, x0
    // 0x8e4d58: r0 = _addDynamicFactoryConfig()
    //     0x8e4d58: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4d5c: r0 = InitLateStaticField(0xdb8) // [package:pointycastle/block/modes/ccm.dart] CCMBlockCipher::factoryConfig
    //     0x8e4d5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4d60: ldr             x0, [x0, #0x1b70]
    //     0x8e4d64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4d68: cmp             w0, w16
    //     0x8e4d6c: b.ne            #0x8e4d7c
    //     0x8e4d70: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a68] Field <CCMBlockCipher.factoryConfig>: static late final (offset: 0xdb8)
    //     0x8e4d74: ldr             x2, [x2, #0xa68]
    //     0x8e4d78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4d7c: ldur            x1, [fp, #-8]
    // 0x8e4d80: mov             x2, x0
    // 0x8e4d84: r0 = _addDynamicFactoryConfig()
    //     0x8e4d84: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4d88: r0 = InitLateStaticField(0xdc0) // [package:pointycastle/block/modes/ige.dart] IGEBlockCipher::factoryConfig
    //     0x8e4d88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e4d8c: ldr             x0, [x0, #0x1b80]
    //     0x8e4d90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e4d94: cmp             w0, w16
    //     0x8e4d98: b.ne            #0x8e4da8
    //     0x8e4d9c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19a70] Field <IGEBlockCipher.factoryConfig>: static late final (offset: 0xdc0)
    //     0x8e4da0: ldr             x2, [x2, #0xa70]
    //     0x8e4da4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e4da8: ldur            x1, [fp, #-8]
    // 0x8e4dac: mov             x2, x0
    // 0x8e4db0: r0 = _addDynamicFactoryConfig()
    //     0x8e4db0: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e4db4: r0 = Null
    //     0x8e4db4: mov             x0, NULL
    // 0x8e4db8: LeaveFrame
    //     0x8e4db8: mov             SP, fp
    //     0x8e4dbc: ldp             fp, lr, [SP], #0x10
    // 0x8e4dc0: ret
    //     0x8e4dc0: ret             
    // 0x8e4dc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e4dc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e4dc8: b               #0x8e4b78
  }
  static _ _registerAsymmetricCiphers(/* No info */) {
    // ** addr: 0x8e6a58, size: 0xb8
    // 0x8e6a58: EnterFrame
    //     0x8e6a58: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6a5c: mov             fp, SP
    // 0x8e6a60: AllocStack(0x8)
    //     0x8e6a60: sub             SP, SP, #8
    // 0x8e6a64: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8e6a64: stur            x1, [fp, #-8]
    // 0x8e6a68: CheckStackOverflow
    //     0x8e6a68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6a6c: cmp             SP, x16
    //     0x8e6a70: b.ls            #0x8e6b08
    // 0x8e6a74: r0 = InitLateStaticField(0xd50) // [package:pointycastle/asymmetric/oaep.dart] OAEPEncoding::factoryConfig
    //     0x8e6a74: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e6a78: ldr             x0, [x0, #0x1aa0]
    //     0x8e6a7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6a80: cmp             w0, w16
    //     0x8e6a84: b.ne            #0x8e6a94
    //     0x8e6a88: add             x2, PP, #0x19, lsl #12  ; [pp+0x19bd0] Field <OAEPEncoding.factoryConfig>: static late final (offset: 0xd50)
    //     0x8e6a8c: ldr             x2, [x2, #0xbd0]
    //     0x8e6a90: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e6a94: ldur            x1, [fp, #-8]
    // 0x8e6a98: mov             x2, x0
    // 0x8e6a9c: r0 = _addDynamicFactoryConfig()
    //     0x8e6a9c: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e6aa0: r0 = InitLateStaticField(0xd48) // [package:pointycastle/asymmetric/pkcs1.dart] PKCS1Encoding::factoryConfig
    //     0x8e6aa0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e6aa4: ldr             x0, [x0, #0x1a90]
    //     0x8e6aa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6aac: cmp             w0, w16
    //     0x8e6ab0: b.ne            #0x8e6ac0
    //     0x8e6ab4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19bd8] Field <PKCS1Encoding.factoryConfig>: static late final (offset: 0xd48)
    //     0x8e6ab8: ldr             x2, [x2, #0xbd8]
    //     0x8e6abc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e6ac0: ldur            x1, [fp, #-8]
    // 0x8e6ac4: mov             x2, x0
    // 0x8e6ac8: r0 = _addDynamicFactoryConfig()
    //     0x8e6ac8: bl              #0x8c3b7c  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig
    // 0x8e6acc: r0 = InitLateStaticField(0xd4c) // [package:pointycastle/asymmetric/rsa.dart] RSAEngine::factoryConfig
    //     0x8e6acc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e6ad0: ldr             x0, [x0, #0x1a98]
    //     0x8e6ad4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6ad8: cmp             w0, w16
    //     0x8e6adc: b.ne            #0x8e6aec
    //     0x8e6ae0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19be0] Field <RSAEngine.factoryConfig>: static late final (offset: 0xd4c)
    //     0x8e6ae4: ldr             x2, [x2, #0xbe0]
    //     0x8e6ae8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e6aec: ldur            x1, [fp, #-8]
    // 0x8e6af0: mov             x2, x0
    // 0x8e6af4: r0 = _addStaticFactoryConfig()
    //     0x8e6af4: bl              #0x8c3aa0  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig
    // 0x8e6af8: r0 = Null
    //     0x8e6af8: mov             x0, NULL
    // 0x8e6afc: LeaveFrame
    //     0x8e6afc: mov             SP, fp
    //     0x8e6b00: ldp             fp, lr, [SP], #0x10
    // 0x8e6b04: ret
    //     0x8e6b04: ret             
    // 0x8e6b08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e6b08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e6b0c: b               #0x8e6a74
  }
}
