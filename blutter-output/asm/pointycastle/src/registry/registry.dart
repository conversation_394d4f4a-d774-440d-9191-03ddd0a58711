// lib: , url: package:pointycastle/src/registry/registry.dart

// class id: 1051047, size: 0x8
class :: {

  static late final FactoryRegistry registry; // offset: 0x171c
  static late final RegExp _specialRegExpChars; // offset: 0x1720

  static _ _escapeRegExp(/* No info */) {
    // ** addr: 0x8c3f50, size: 0x8c
    // 0x8c3f50: EnterFrame
    //     0x8c3f50: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3f54: mov             fp, SP
    // 0x8c3f58: AllocStack(0x18)
    //     0x8c3f58: sub             SP, SP, #0x18
    // 0x8c3f5c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8c3f5c: stur            x1, [fp, #-8]
    // 0x8c3f60: CheckStackOverflow
    //     0x8c3f60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3f64: cmp             SP, x16
    //     0x8c3f68: b.ls            #0x8c3fd4
    // 0x8c3f6c: r0 = InitLateStaticField(0x1720) // [package:pointycastle/src/registry/registry.dart] ::_specialRegExpChars
    //     0x8c3f6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c3f70: ldr             x0, [x0, #0x2e40]
    //     0x8c3f74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c3f78: cmp             w0, w16
    //     0x8c3f7c: b.ne            #0x8c3f8c
    //     0x8c3f80: add             x2, PP, #0x18, lsl #12  ; [pp+0x180a0] Field <::._specialRegExpChars@2655301108>: static late final (offset: 0x1720)
    //     0x8c3f84: ldr             x2, [x2, #0xa0]
    //     0x8c3f88: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c3f8c: r1 = Function '<anonymous closure>': static.
    //     0x8c3f8c: add             x1, PP, #0x18, lsl #12  ; [pp+0x180a8] AnonymousClosure: static (0x8c425c), in [package:pointycastle/src/registry/registry.dart] ::_escapeRegExp (0x8c3f50)
    //     0x8c3f90: ldr             x1, [x1, #0xa8]
    // 0x8c3f94: r2 = Null
    //     0x8c3f94: mov             x2, NULL
    // 0x8c3f98: stur            x0, [fp, #-0x10]
    // 0x8c3f9c: r0 = AllocateClosure()
    //     0x8c3f9c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c3fa0: r1 = Function '<anonymous closure>': static.
    //     0x8c3fa0: add             x1, PP, #0x18, lsl #12  ; [pp+0x180b0] AnonymousClosure: (0xebd554), in [package:flutter/src/services/restoration.dart] RestorationBucket::_visitChildren (0x69ffb0)
    //     0x8c3fa4: ldr             x1, [x1, #0xb0]
    // 0x8c3fa8: r2 = Null
    //     0x8c3fa8: mov             x2, NULL
    // 0x8c3fac: stur            x0, [fp, #-0x18]
    // 0x8c3fb0: r0 = AllocateClosure()
    //     0x8c3fb0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c3fb4: ldur            x1, [fp, #-8]
    // 0x8c3fb8: ldur            x2, [fp, #-0x10]
    // 0x8c3fbc: ldur            x3, [fp, #-0x18]
    // 0x8c3fc0: mov             x5, x0
    // 0x8c3fc4: r0 = splitMapJoin()
    //     0x8c3fc4: bl              #0x8c3fdc  ; [dart:core] _StringBase::splitMapJoin
    // 0x8c3fc8: LeaveFrame
    //     0x8c3fc8: mov             SP, fp
    //     0x8c3fcc: ldp             fp, lr, [SP], #0x10
    // 0x8c3fd0: ret
    //     0x8c3fd0: ret             
    // 0x8c3fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3fd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3fd8: b               #0x8c3f6c
  }
  [closure] static String <anonymous closure>(dynamic, Match) {
    // ** addr: 0x8c425c, size: 0x98
    // 0x8c425c: EnterFrame
    //     0x8c425c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4260: mov             fp, SP
    // 0x8c4264: AllocStack(0x10)
    //     0x8c4264: sub             SP, SP, #0x10
    // 0x8c4268: CheckStackOverflow
    //     0x8c4268: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c426c: cmp             SP, x16
    //     0x8c4270: b.ls            #0x8c42ec
    // 0x8c4274: r1 = Null
    //     0x8c4274: mov             x1, NULL
    // 0x8c4278: r2 = 4
    //     0x8c4278: movz            x2, #0x4
    // 0x8c427c: r0 = AllocateArray()
    //     0x8c427c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c4280: mov             x3, x0
    // 0x8c4284: stur            x3, [fp, #-8]
    // 0x8c4288: r16 = "\\"
    //     0x8c4288: ldr             x16, [PP, #0xc28]  ; [pp+0xc28] "\\"
    // 0x8c428c: StoreField: r3->field_f = r16
    //     0x8c428c: stur            w16, [x3, #0xf]
    // 0x8c4290: ldr             x1, [fp, #0x10]
    // 0x8c4294: r0 = LoadClassIdInstr(r1)
    //     0x8c4294: ldur            x0, [x1, #-1]
    //     0x8c4298: ubfx            x0, x0, #0xc, #0x14
    // 0x8c429c: r2 = 0
    //     0x8c429c: movz            x2, #0
    // 0x8c42a0: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c42a0: sub             lr, x0, #0xfdd
    //     0x8c42a4: ldr             lr, [x21, lr, lsl #3]
    //     0x8c42a8: blr             lr
    // 0x8c42ac: ldur            x1, [fp, #-8]
    // 0x8c42b0: ArrayStore: r1[1] = r0  ; List_4
    //     0x8c42b0: add             x25, x1, #0x13
    //     0x8c42b4: str             w0, [x25]
    //     0x8c42b8: tbz             w0, #0, #0x8c42d4
    //     0x8c42bc: ldurb           w16, [x1, #-1]
    //     0x8c42c0: ldurb           w17, [x0, #-1]
    //     0x8c42c4: and             x16, x17, x16, lsr #2
    //     0x8c42c8: tst             x16, HEAP, lsr #32
    //     0x8c42cc: b.eq            #0x8c42d4
    //     0x8c42d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8c42d4: ldur            x16, [fp, #-8]
    // 0x8c42d8: str             x16, [SP]
    // 0x8c42dc: r0 = _interpolate()
    //     0x8c42dc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c42e0: LeaveFrame
    //     0x8c42e0: mov             SP, fp
    //     0x8c42e4: ldp             fp, lr, [SP], #0x10
    // 0x8c42e8: ret
    //     0x8c42e8: ret             
    // 0x8c42ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c42ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c42f0: b               #0x8c4274
  }
  static RegExp _specialRegExpChars() {
    // ** addr: 0x8c42f4, size: 0x58
    // 0x8c42f4: EnterFrame
    //     0x8c42f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c42f8: mov             fp, SP
    // 0x8c42fc: AllocStack(0x30)
    //     0x8c42fc: sub             SP, SP, #0x30
    // 0x8c4300: CheckStackOverflow
    //     0x8c4300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c4304: cmp             SP, x16
    //     0x8c4308: b.ls            #0x8c4344
    // 0x8c430c: r16 = "([\\\\\\^\\$\\.\\|\\+\\[\\]\\(\\)\\{\\}])"
    //     0x8c430c: add             x16, PP, #0x18, lsl #12  ; [pp+0x180c8] "([\\\\\\^\\$\\.\\|\\+\\[\\]\\(\\)\\{\\}])"
    //     0x8c4310: ldr             x16, [x16, #0xc8]
    // 0x8c4314: stp             x16, NULL, [SP, #0x20]
    // 0x8c4318: r16 = false
    //     0x8c4318: add             x16, NULL, #0x30  ; false
    // 0x8c431c: r30 = true
    //     0x8c431c: add             lr, NULL, #0x20  ; true
    // 0x8c4320: stp             lr, x16, [SP, #0x10]
    // 0x8c4324: r16 = false
    //     0x8c4324: add             x16, NULL, #0x30  ; false
    // 0x8c4328: r30 = false
    //     0x8c4328: add             lr, NULL, #0x30  ; false
    // 0x8c432c: stp             lr, x16, [SP]
    // 0x8c4330: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8c4330: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8c4334: r0 = _RegExp()
    //     0x8c4334: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8c4338: LeaveFrame
    //     0x8c4338: mov             SP, fp
    //     0x8c433c: ldp             fp, lr, [SP], #0x10
    // 0x8c4340: ret
    //     0x8c4340: ret             
    // 0x8c4344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c4348: b               #0x8c430c
  }
  static FactoryRegistry registry() {
    // ** addr: 0x8e9224, size: 0x40
    // 0x8e9224: EnterFrame
    //     0x8e9224: stp             fp, lr, [SP, #-0x10]!
    //     0x8e9228: mov             fp, SP
    // 0x8e922c: AllocStack(0x8)
    //     0x8e922c: sub             SP, SP, #8
    // 0x8e9230: CheckStackOverflow
    //     0x8e9230: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e9234: cmp             SP, x16
    //     0x8e9238: b.ls            #0x8e925c
    // 0x8e923c: r0 = _RegistryImpl()
    //     0x8e923c: bl              #0x8e933c  ; Allocate_RegistryImplStub -> _RegistryImpl (size=0x18)
    // 0x8e9240: mov             x1, x0
    // 0x8e9244: stur            x0, [fp, #-8]
    // 0x8e9248: r0 = _RegistryImpl()
    //     0x8e9248: bl              #0x8e9264  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_RegistryImpl
    // 0x8e924c: ldur            x0, [fp, #-8]
    // 0x8e9250: LeaveFrame
    //     0x8e9250: mov             SP, fp
    //     0x8e9254: ldp             fp, lr, [SP], #0x10
    // 0x8e9258: ret
    //     0x8e9258: ret             
    // 0x8e925c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e925c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e9260: b               #0x8e923c
  }
}

// class id: 546, size: 0x18, field offset: 0x8
class _RegistryImpl extends Object
    implements FactoryRegistry {

  Y0 create<Y0>(_RegistryImpl, String) {
    // ** addr: 0x8c31fc, size: 0xc8
    // 0x8c31fc: EnterFrame
    //     0x8c31fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3200: mov             fp, SP
    // 0x8c3204: AllocStack(0x10)
    //     0x8c3204: sub             SP, SP, #0x10
    // 0x8c3208: SetupParameters()
    //     0x8c3208: ldur            w0, [x4, #0xf]
    //     0x8c320c: cbnz            w0, #0x8c3218
    //     0x8c3210: mov             x0, NULL
    //     0x8c3214: b               #0x8c3228
    //     0x8c3218: ldur            w0, [x4, #0x17]
    //     0x8c321c: add             x1, fp, w0, sxtw #2
    //     0x8c3220: ldr             x1, [x1, #0x10]
    //     0x8c3224: mov             x0, x1
    //     0x8c3228: stur            x0, [fp, #-8]
    // 0x8c322c: CheckStackOverflow
    //     0x8c322c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3230: cmp             SP, x16
    //     0x8c3234: b.ls            #0x8c32bc
    // 0x8c3238: mov             x1, x0
    // 0x8c323c: r2 = Null
    //     0x8c323c: mov             x2, NULL
    // 0x8c3240: r3 = Y0
    //     0x8c3240: add             x3, PP, #0x17, lsl #12  ; [pp+0x17f90] TypeParameter: Y0
    //     0x8c3244: ldr             x3, [x3, #0xf90]
    // 0x8c3248: r30 = InstantiateTypeNonNullableFunctionTypeParameterStub
    //     0x8c3248: ldr             lr, [PP, #0x24e0]  ; [pp+0x24e0] Stub: InstantiateTypeNonNullableFunctionTypeParameter (0x5e10cc)
    // 0x8c324c: LoadField: r30 = r30->field_7
    //     0x8c324c: ldur            lr, [lr, #7]
    // 0x8c3250: blr             lr
    // 0x8c3254: ldr             x1, [fp, #0x18]
    // 0x8c3258: mov             x2, x0
    // 0x8c325c: ldr             x3, [fp, #0x10]
    // 0x8c3260: r0 = getConstructor()
    //     0x8c3260: bl              #0x8c32c4  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::getConstructor
    // 0x8c3264: str             x0, [SP]
    // 0x8c3268: ClosureCall
    //     0x8c3268: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x8c326c: ldur            x2, [x0, #0x1f]
    //     0x8c3270: blr             x2
    // 0x8c3274: ldur            x1, [fp, #-8]
    // 0x8c3278: mov             x3, x0
    // 0x8c327c: r2 = Null
    //     0x8c327c: mov             x2, NULL
    // 0x8c3280: stur            x3, [fp, #-8]
    // 0x8c3284: cmp             w1, NULL
    // 0x8c3288: b.eq            #0x8c32ac
    // 0x8c328c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x8c328c: ldur            w4, [x1, #0x17]
    // 0x8c3290: DecompressPointer r4
    //     0x8c3290: add             x4, x4, HEAP, lsl #32
    // 0x8c3294: r8 = Y0
    //     0x8c3294: add             x8, PP, #0x17, lsl #12  ; [pp+0x17f90] TypeParameter: Y0
    //     0x8c3298: ldr             x8, [x8, #0xf90]
    // 0x8c329c: LoadField: r9 = r4->field_7
    //     0x8c329c: ldur            x9, [x4, #7]
    // 0x8c32a0: r3 = Null
    //     0x8c32a0: add             x3, PP, #0x17, lsl #12  ; [pp+0x17f98] Null
    //     0x8c32a4: ldr             x3, [x3, #0xf98]
    // 0x8c32a8: blr             x9
    // 0x8c32ac: ldur            x0, [fp, #-8]
    // 0x8c32b0: LeaveFrame
    //     0x8c32b0: mov             SP, fp
    //     0x8c32b4: ldp             fp, lr, [SP], #0x10
    // 0x8c32b8: ret
    //     0x8c32b8: ret             
    // 0x8c32bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c32bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c32c0: b               #0x8c3238
  }
  _ getConstructor(/* No info */) {
    // ** addr: 0x8c32c4, size: 0x1e0
    // 0x8c32c4: EnterFrame
    //     0x8c32c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c32c8: mov             fp, SP
    // 0x8c32cc: AllocStack(0x28)
    //     0x8c32cc: sub             SP, SP, #0x28
    // 0x8c32d0: SetupParameters(_RegistryImpl this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x8c32d0: mov             x4, x1
    //     0x8c32d4: mov             x0, x2
    //     0x8c32d8: stur            x1, [fp, #-0x10]
    //     0x8c32dc: stur            x2, [fp, #-0x18]
    //     0x8c32e0: stur            x3, [fp, #-0x20]
    // 0x8c32e4: CheckStackOverflow
    //     0x8c32e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c32e8: cmp             SP, x16
    //     0x8c32ec: b.ls            #0x8c3498
    // 0x8c32f0: LoadField: r5 = r4->field_f
    //     0x8c32f0: ldur            w5, [x4, #0xf]
    // 0x8c32f4: DecompressPointer r5
    //     0x8c32f4: add             x5, x5, HEAP, lsl #32
    // 0x8c32f8: stur            x5, [fp, #-8]
    // 0x8c32fc: r1 = Null
    //     0x8c32fc: mov             x1, NULL
    // 0x8c3300: r2 = 6
    //     0x8c3300: movz            x2, #0x6
    // 0x8c3304: r0 = AllocateArray()
    //     0x8c3304: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c3308: ldur            x2, [fp, #-0x18]
    // 0x8c330c: StoreField: r0->field_f = r2
    //     0x8c330c: stur            w2, [x0, #0xf]
    // 0x8c3310: r16 = "."
    //     0x8c3310: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x8c3314: StoreField: r0->field_13 = r16
    //     0x8c3314: stur            w16, [x0, #0x13]
    // 0x8c3318: ldur            x3, [fp, #-0x20]
    // 0x8c331c: ArrayStore: r0[0] = r3  ; List_4
    //     0x8c331c: stur            w3, [x0, #0x17]
    // 0x8c3320: str             x0, [SP]
    // 0x8c3324: r0 = _interpolate()
    //     0x8c3324: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c3328: ldur            x1, [fp, #-8]
    // 0x8c332c: mov             x2, x0
    // 0x8c3330: r0 = _getValueOrData()
    //     0x8c3330: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8c3334: mov             x1, x0
    // 0x8c3338: ldur            x0, [fp, #-8]
    // 0x8c333c: LoadField: r2 = r0->field_f
    //     0x8c333c: ldur            w2, [x0, #0xf]
    // 0x8c3340: DecompressPointer r2
    //     0x8c3340: add             x2, x2, HEAP, lsl #32
    // 0x8c3344: cmp             w2, w1
    // 0x8c3348: b.ne            #0x8c3350
    // 0x8c334c: r1 = Null
    //     0x8c334c: mov             x1, NULL
    // 0x8c3350: cmp             w1, NULL
    // 0x8c3354: b.ne            #0x8c3488
    // 0x8c3358: ldur            x1, [fp, #-0x10]
    // 0x8c335c: ldur            x2, [fp, #-0x18]
    // 0x8c3360: ldur            x3, [fp, #-0x20]
    // 0x8c3364: r0 = _createConstructor()
    //     0x8c3364: bl              #0x8c34a4  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_createConstructor
    // 0x8c3368: mov             x2, x0
    // 0x8c336c: ldur            x0, [fp, #-8]
    // 0x8c3370: stur            x2, [fp, #-0x10]
    // 0x8c3374: LoadField: r1 = r0->field_13
    //     0x8c3374: ldur            w1, [x0, #0x13]
    // 0x8c3378: r3 = LoadInt32Instr(r1)
    //     0x8c3378: sbfx            x3, x1, #1, #0x1f
    // 0x8c337c: asr             x1, x3, #1
    // 0x8c3380: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x8c3380: ldur            w3, [x0, #0x17]
    // 0x8c3384: r4 = LoadInt32Instr(r3)
    //     0x8c3384: sbfx            x4, x3, #1, #0x1f
    // 0x8c3388: sub             x3, x1, x4
    // 0x8c338c: cmp             x3, #0x19
    // 0x8c3390: b.le            #0x8c339c
    // 0x8c3394: mov             x1, x0
    // 0x8c3398: r0 = clear()
    //     0x8c3398: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x8c339c: ldur            x4, [fp, #-0x18]
    // 0x8c33a0: ldur            x5, [fp, #-0x20]
    // 0x8c33a4: ldur            x0, [fp, #-8]
    // 0x8c33a8: ldur            x3, [fp, #-0x10]
    // 0x8c33ac: r1 = Null
    //     0x8c33ac: mov             x1, NULL
    // 0x8c33b0: r2 = 6
    //     0x8c33b0: movz            x2, #0x6
    // 0x8c33b4: r0 = AllocateArray()
    //     0x8c33b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c33b8: mov             x1, x0
    // 0x8c33bc: ldur            x0, [fp, #-0x18]
    // 0x8c33c0: StoreField: r1->field_f = r0
    //     0x8c33c0: stur            w0, [x1, #0xf]
    // 0x8c33c4: r16 = "."
    //     0x8c33c4: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x8c33c8: StoreField: r1->field_13 = r16
    //     0x8c33c8: stur            w16, [x1, #0x13]
    // 0x8c33cc: ldur            x0, [fp, #-0x20]
    // 0x8c33d0: ArrayStore: r1[0] = r0  ; List_4
    //     0x8c33d0: stur            w0, [x1, #0x17]
    // 0x8c33d4: str             x1, [SP]
    // 0x8c33d8: r0 = _interpolate()
    //     0x8c33d8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c33dc: mov             x4, x0
    // 0x8c33e0: ldur            x3, [fp, #-0x10]
    // 0x8c33e4: stur            x4, [fp, #-0x20]
    // 0x8c33e8: cmp             w3, NULL
    // 0x8c33ec: b.eq            #0x8c34a0
    // 0x8c33f0: ldur            x5, [fp, #-8]
    // 0x8c33f4: LoadField: r6 = r5->field_7
    //     0x8c33f4: ldur            w6, [x5, #7]
    // 0x8c33f8: DecompressPointer r6
    //     0x8c33f8: add             x6, x6, HEAP, lsl #32
    // 0x8c33fc: mov             x0, x4
    // 0x8c3400: mov             x2, x6
    // 0x8c3404: stur            x6, [fp, #-0x18]
    // 0x8c3408: r1 = Null
    //     0x8c3408: mov             x1, NULL
    // 0x8c340c: cmp             w2, NULL
    // 0x8c3410: b.eq            #0x8c3430
    // 0x8c3414: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8c3414: ldur            w4, [x2, #0x17]
    // 0x8c3418: DecompressPointer r4
    //     0x8c3418: add             x4, x4, HEAP, lsl #32
    // 0x8c341c: r8 = X0
    //     0x8c341c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8c3420: LoadField: r9 = r4->field_7
    //     0x8c3420: ldur            x9, [x4, #7]
    // 0x8c3424: r3 = Null
    //     0x8c3424: add             x3, PP, #0x17, lsl #12  ; [pp+0x17fa8] Null
    //     0x8c3428: ldr             x3, [x3, #0xfa8]
    // 0x8c342c: blr             x9
    // 0x8c3430: ldur            x0, [fp, #-0x10]
    // 0x8c3434: ldur            x2, [fp, #-0x18]
    // 0x8c3438: r1 = Null
    //     0x8c3438: mov             x1, NULL
    // 0x8c343c: cmp             w2, NULL
    // 0x8c3440: b.eq            #0x8c3460
    // 0x8c3444: LoadField: r4 = r2->field_1b
    //     0x8c3444: ldur            w4, [x2, #0x1b]
    // 0x8c3448: DecompressPointer r4
    //     0x8c3448: add             x4, x4, HEAP, lsl #32
    // 0x8c344c: r8 = X1
    //     0x8c344c: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x8c3450: LoadField: r9 = r4->field_7
    //     0x8c3450: ldur            x9, [x4, #7]
    // 0x8c3454: r3 = Null
    //     0x8c3454: add             x3, PP, #0x17, lsl #12  ; [pp+0x17fb8] Null
    //     0x8c3458: ldr             x3, [x3, #0xfb8]
    // 0x8c345c: blr             x9
    // 0x8c3460: ldur            x1, [fp, #-8]
    // 0x8c3464: ldur            x2, [fp, #-0x20]
    // 0x8c3468: r0 = _hashCode()
    //     0x8c3468: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x8c346c: ldur            x1, [fp, #-8]
    // 0x8c3470: ldur            x2, [fp, #-0x20]
    // 0x8c3474: ldur            x3, [fp, #-0x10]
    // 0x8c3478: mov             x5, x0
    // 0x8c347c: r0 = _set()
    //     0x8c347c: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x8c3480: ldur            x0, [fp, #-0x10]
    // 0x8c3484: b               #0x8c348c
    // 0x8c3488: mov             x0, x1
    // 0x8c348c: LeaveFrame
    //     0x8c348c: mov             SP, fp
    //     0x8c3490: ldp             fp, lr, [SP], #0x10
    // 0x8c3494: ret
    //     0x8c3494: ret             
    // 0x8c3498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3498: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c349c: b               #0x8c32f0
    // 0x8c34a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c34a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _createConstructor(/* No info */) {
    // ** addr: 0x8c34a4, size: 0x274
    // 0x8c34a4: EnterFrame
    //     0x8c34a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c34a8: mov             fp, SP
    // 0x8c34ac: AllocStack(0x38)
    //     0x8c34ac: sub             SP, SP, #0x38
    // 0x8c34b0: SetupParameters(_RegistryImpl this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x8c34b0: mov             x0, x2
    //     0x8c34b4: stur            x2, [fp, #-0x10]
    //     0x8c34b8: mov             x2, x3
    //     0x8c34bc: stur            x3, [fp, #-0x18]
    //     0x8c34c0: mov             x3, x1
    //     0x8c34c4: stur            x1, [fp, #-8]
    // 0x8c34c8: CheckStackOverflow
    //     0x8c34c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c34cc: cmp             SP, x16
    //     0x8c34d0: b.ls            #0x8c36fc
    // 0x8c34d4: mov             x1, x3
    // 0x8c34d8: r0 = _checkInit()
    //     0x8c34d8: bl              #0x8c37e4  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_checkInit
    // 0x8c34dc: ldur            x0, [fp, #-8]
    // 0x8c34e0: LoadField: r3 = r0->field_7
    //     0x8c34e0: ldur            w3, [x0, #7]
    // 0x8c34e4: DecompressPointer r3
    //     0x8c34e4: add             x3, x3, HEAP, lsl #32
    // 0x8c34e8: mov             x1, x3
    // 0x8c34ec: ldur            x2, [fp, #-0x10]
    // 0x8c34f0: stur            x3, [fp, #-0x20]
    // 0x8c34f4: r0 = containsKey()
    //     0x8c34f4: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x8c34f8: tbnz            w0, #4, #0x8c35a8
    // 0x8c34fc: ldur            x0, [fp, #-0x20]
    // 0x8c3500: mov             x1, x0
    // 0x8c3504: ldur            x2, [fp, #-0x10]
    // 0x8c3508: r0 = _getValueOrData()
    //     0x8c3508: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8c350c: ldur            x3, [fp, #-0x20]
    // 0x8c3510: LoadField: r1 = r3->field_f
    //     0x8c3510: ldur            w1, [x3, #0xf]
    // 0x8c3514: DecompressPointer r1
    //     0x8c3514: add             x1, x1, HEAP, lsl #32
    // 0x8c3518: cmp             w1, w0
    // 0x8c351c: b.ne            #0x8c3528
    // 0x8c3520: r1 = Null
    //     0x8c3520: mov             x1, NULL
    // 0x8c3524: b               #0x8c352c
    // 0x8c3528: mov             x1, x0
    // 0x8c352c: cmp             w1, NULL
    // 0x8c3530: b.eq            #0x8c3704
    // 0x8c3534: r0 = LoadClassIdInstr(r1)
    //     0x8c3534: ldur            x0, [x1, #-1]
    //     0x8c3538: ubfx            x0, x0, #0xc, #0x14
    // 0x8c353c: ldur            x2, [fp, #-0x18]
    // 0x8c3540: r0 = GDT[cid_x0 + 0x55f]()
    //     0x8c3540: add             lr, x0, #0x55f
    //     0x8c3544: ldr             lr, [x21, lr, lsl #3]
    //     0x8c3548: blr             lr
    // 0x8c354c: tbnz            w0, #4, #0x8c35a8
    // 0x8c3550: ldur            x0, [fp, #-0x20]
    // 0x8c3554: mov             x1, x0
    // 0x8c3558: ldur            x2, [fp, #-0x10]
    // 0x8c355c: r0 = _getValueOrData()
    //     0x8c355c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8c3560: mov             x1, x0
    // 0x8c3564: ldur            x0, [fp, #-0x20]
    // 0x8c3568: LoadField: r2 = r0->field_f
    //     0x8c3568: ldur            w2, [x0, #0xf]
    // 0x8c356c: DecompressPointer r2
    //     0x8c356c: add             x2, x2, HEAP, lsl #32
    // 0x8c3570: cmp             w2, w1
    // 0x8c3574: b.ne            #0x8c357c
    // 0x8c3578: r1 = Null
    //     0x8c3578: mov             x1, NULL
    // 0x8c357c: cmp             w1, NULL
    // 0x8c3580: b.eq            #0x8c3708
    // 0x8c3584: r0 = LoadClassIdInstr(r1)
    //     0x8c3584: ldur            x0, [x1, #-1]
    //     0x8c3588: ubfx            x0, x0, #0xc, #0x14
    // 0x8c358c: ldur            x2, [fp, #-0x18]
    // 0x8c3590: r0 = GDT[cid_x0 + -0x114]()
    //     0x8c3590: sub             lr, x0, #0x114
    //     0x8c3594: ldr             lr, [x21, lr, lsl #3]
    //     0x8c3598: blr             lr
    // 0x8c359c: LeaveFrame
    //     0x8c359c: mov             SP, fp
    //     0x8c35a0: ldp             fp, lr, [SP], #0x10
    // 0x8c35a4: ret
    //     0x8c35a4: ret             
    // 0x8c35a8: ldur            x0, [fp, #-8]
    // 0x8c35ac: LoadField: r3 = r0->field_b
    //     0x8c35ac: ldur            w3, [x0, #0xb]
    // 0x8c35b0: DecompressPointer r3
    //     0x8c35b0: add             x3, x3, HEAP, lsl #32
    // 0x8c35b4: mov             x1, x3
    // 0x8c35b8: ldur            x2, [fp, #-0x10]
    // 0x8c35bc: stur            x3, [fp, #-0x20]
    // 0x8c35c0: r0 = containsKey()
    //     0x8c35c0: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x8c35c4: tbnz            w0, #4, #0x8c36d8
    // 0x8c35c8: ldur            x0, [fp, #-0x20]
    // 0x8c35cc: mov             x1, x0
    // 0x8c35d0: ldur            x2, [fp, #-0x10]
    // 0x8c35d4: r0 = _getValueOrData()
    //     0x8c35d4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8c35d8: mov             x1, x0
    // 0x8c35dc: ldur            x0, [fp, #-0x20]
    // 0x8c35e0: LoadField: r2 = r0->field_f
    //     0x8c35e0: ldur            w2, [x0, #0xf]
    // 0x8c35e4: DecompressPointer r2
    //     0x8c35e4: add             x2, x2, HEAP, lsl #32
    // 0x8c35e8: cmp             w2, w1
    // 0x8c35ec: b.ne            #0x8c35f4
    // 0x8c35f0: r1 = Null
    //     0x8c35f0: mov             x1, NULL
    // 0x8c35f4: cmp             w1, NULL
    // 0x8c35f8: b.eq            #0x8c370c
    // 0x8c35fc: r0 = LoadClassIdInstr(r1)
    //     0x8c35fc: ldur            x0, [x1, #-1]
    //     0x8c3600: ubfx            x0, x0, #0xc, #0x14
    // 0x8c3604: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x8c3604: movz            x17, #0xd35d
    //     0x8c3608: add             lr, x0, x17
    //     0x8c360c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c3610: blr             lr
    // 0x8c3614: mov             x2, x0
    // 0x8c3618: stur            x2, [fp, #-8]
    // 0x8c361c: CheckStackOverflow
    //     0x8c361c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3620: cmp             SP, x16
    //     0x8c3624: b.ls            #0x8c3710
    // 0x8c3628: r0 = LoadClassIdInstr(r2)
    //     0x8c3628: ldur            x0, [x2, #-1]
    //     0x8c362c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c3630: mov             x1, x2
    // 0x8c3634: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x8c3634: movz            x17, #0x292d
    //     0x8c3638: movk            x17, #0x1, lsl #16
    //     0x8c363c: add             lr, x0, x17
    //     0x8c3640: ldr             lr, [x21, lr, lsl #3]
    //     0x8c3644: blr             lr
    // 0x8c3648: tbnz            w0, #4, #0x8c36d8
    // 0x8c364c: ldur            x2, [fp, #-8]
    // 0x8c3650: r0 = LoadClassIdInstr(r2)
    //     0x8c3650: ldur            x0, [x2, #-1]
    //     0x8c3654: ubfx            x0, x0, #0xc, #0x14
    // 0x8c3658: mov             x1, x2
    // 0x8c365c: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x8c365c: movz            x17, #0x384d
    //     0x8c3660: movk            x17, #0x1, lsl #16
    //     0x8c3664: add             lr, x0, x17
    //     0x8c3668: ldr             lr, [x21, lr, lsl #3]
    //     0x8c366c: blr             lr
    // 0x8c3670: stur            x0, [fp, #-0x20]
    // 0x8c3674: LoadField: r1 = r0->field_b
    //     0x8c3674: ldur            w1, [x0, #0xb]
    // 0x8c3678: DecompressPointer r1
    //     0x8c3678: add             x1, x1, HEAP, lsl #32
    // 0x8c367c: ldur            x2, [fp, #-0x18]
    // 0x8c3680: r0 = firstMatch()
    //     0x8c3680: bl              #0x644328  ; [dart:core] _RegExp::firstMatch
    // 0x8c3684: cmp             w0, NULL
    // 0x8c3688: b.ne            #0x8c3694
    // 0x8c368c: r0 = Null
    //     0x8c368c: mov             x0, NULL
    // 0x8c3690: b               #0x8c36bc
    // 0x8c3694: ldur            x1, [fp, #-0x20]
    // 0x8c3698: LoadField: r2 = r1->field_f
    //     0x8c3698: ldur            w2, [x1, #0xf]
    // 0x8c369c: DecompressPointer r2
    //     0x8c369c: add             x2, x2, HEAP, lsl #32
    // 0x8c36a0: ldur            x16, [fp, #-0x18]
    // 0x8c36a4: stp             x16, x2, [SP, #8]
    // 0x8c36a8: str             x0, [SP]
    // 0x8c36ac: mov             x0, x2
    // 0x8c36b0: ClosureCall
    //     0x8c36b0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x8c36b4: ldur            x2, [x0, #0x1f]
    //     0x8c36b8: blr             x2
    // 0x8c36bc: cmp             w0, NULL
    // 0x8c36c0: b.ne            #0x8c36cc
    // 0x8c36c4: ldur            x2, [fp, #-8]
    // 0x8c36c8: b               #0x8c361c
    // 0x8c36cc: LeaveFrame
    //     0x8c36cc: mov             SP, fp
    //     0x8c36d0: ldp             fp, lr, [SP], #0x10
    // 0x8c36d4: ret
    //     0x8c36d4: ret             
    // 0x8c36d8: r0 = RegistryFactoryException()
    //     0x8c36d8: bl              #0x8c37d8  ; AllocateRegistryFactoryExceptionStub -> RegistryFactoryException (size=0xc)
    // 0x8c36dc: mov             x1, x0
    // 0x8c36e0: ldur            x2, [fp, #-0x18]
    // 0x8c36e4: ldur            x3, [fp, #-0x10]
    // 0x8c36e8: stur            x0, [fp, #-8]
    // 0x8c36ec: r0 = RegistryFactoryException.unknown()
    //     0x8c36ec: bl              #0x8c3738  ; [package:pointycastle/api.dart] RegistryFactoryException::RegistryFactoryException.unknown
    // 0x8c36f0: ldur            x0, [fp, #-8]
    // 0x8c36f4: r0 = Throw()
    //     0x8c36f4: bl              #0xec04b8  ; ThrowStub
    // 0x8c36f8: brk             #0
    // 0x8c36fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c36fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3700: b               #0x8c34d4
    // 0x8c3704: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c3704: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c3708: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c3708: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c370c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c370c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8c3710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3714: b               #0x8c3628
  }
  _ _checkInit(/* No info */) {
    // ** addr: 0x8c37e4, size: 0x3c
    // 0x8c37e4: EnterFrame
    //     0x8c37e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c37e8: mov             fp, SP
    // 0x8c37ec: CheckStackOverflow
    //     0x8c37ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c37f0: cmp             SP, x16
    //     0x8c37f4: b.ls            #0x8c3818
    // 0x8c37f8: LoadField: r0 = r1->field_13
    //     0x8c37f8: ldur            w0, [x1, #0x13]
    // 0x8c37fc: DecompressPointer r0
    //     0x8c37fc: add             x0, x0, HEAP, lsl #32
    // 0x8c3800: tbz             w0, #4, #0x8c3808
    // 0x8c3804: r0 = _initialize()
    //     0x8c3804: bl              #0x8c3820  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_initialize
    // 0x8c3808: r0 = Null
    //     0x8c3808: mov             x0, NULL
    // 0x8c380c: LeaveFrame
    //     0x8c380c: mov             SP, fp
    //     0x8c3810: ldp             fp, lr, [SP], #0x10
    // 0x8c3814: ret
    //     0x8c3814: ret             
    // 0x8c3818: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3818: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c381c: b               #0x8c37f8
  }
  _ _initialize(/* No info */) {
    // ** addr: 0x8c3820, size: 0x4c
    // 0x8c3820: EnterFrame
    //     0x8c3820: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3824: mov             fp, SP
    // 0x8c3828: AllocStack(0x8)
    //     0x8c3828: sub             SP, SP, #8
    // 0x8c382c: SetupParameters(_RegistryImpl this /* r1 => r0, fp-0x8 */)
    //     0x8c382c: mov             x0, x1
    //     0x8c3830: stur            x1, [fp, #-8]
    // 0x8c3834: CheckStackOverflow
    //     0x8c3834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3838: cmp             SP, x16
    //     0x8c383c: b.ls            #0x8c3864
    // 0x8c3840: mov             x1, x0
    // 0x8c3844: r0 = registerFactories()
    //     0x8c3844: bl              #0x8c386c  ; [package:pointycastle/src/registry/registration.dart] ::registerFactories
    // 0x8c3848: ldur            x2, [fp, #-8]
    // 0x8c384c: r1 = true
    //     0x8c384c: add             x1, NULL, #0x20  ; true
    // 0x8c3850: StoreField: r2->field_13 = r1
    //     0x8c3850: stur            w1, [x2, #0x13]
    // 0x8c3854: r0 = Null
    //     0x8c3854: mov             x0, NULL
    // 0x8c3858: LeaveFrame
    //     0x8c3858: mov             SP, fp
    //     0x8c385c: ldp             fp, lr, [SP], #0x10
    // 0x8c3860: ret
    //     0x8c3860: ret             
    // 0x8c3864: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3864: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3868: b               #0x8c3840
  }
  _ _addStaticFactoryConfig(/* No info */) {
    // ** addr: 0x8c3aa0, size: 0x9c
    // 0x8c3aa0: EnterFrame
    //     0x8c3aa0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3aa4: mov             fp, SP
    // 0x8c3aa8: AllocStack(0x18)
    //     0x8c3aa8: sub             SP, SP, #0x18
    // 0x8c3aac: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x8c3aac: mov             x0, x2
    //     0x8c3ab0: stur            x2, [fp, #-0x18]
    // 0x8c3ab4: CheckStackOverflow
    //     0x8c3ab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3ab8: cmp             SP, x16
    //     0x8c3abc: b.ls            #0x8c3b34
    // 0x8c3ac0: LoadField: r3 = r1->field_7
    //     0x8c3ac0: ldur            w3, [x1, #7]
    // 0x8c3ac4: DecompressPointer r3
    //     0x8c3ac4: add             x3, x3, HEAP, lsl #32
    // 0x8c3ac8: stur            x3, [fp, #-0x10]
    // 0x8c3acc: LoadField: r4 = r0->field_7
    //     0x8c3acc: ldur            w4, [x0, #7]
    // 0x8c3ad0: DecompressPointer r4
    //     0x8c3ad0: add             x4, x4, HEAP, lsl #32
    // 0x8c3ad4: stur            x4, [fp, #-8]
    // 0x8c3ad8: r1 = Function '<anonymous closure>':.
    //     0x8c3ad8: add             x1, PP, #0x18, lsl #12  ; [pp+0x18018] AnonymousClosure: (0x8c3b3c), in [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addStaticFactoryConfig (0x8c3aa0)
    //     0x8c3adc: ldr             x1, [x1, #0x18]
    // 0x8c3ae0: r2 = Null
    //     0x8c3ae0: mov             x2, NULL
    // 0x8c3ae4: r0 = AllocateClosure()
    //     0x8c3ae4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c3ae8: ldur            x1, [fp, #-0x10]
    // 0x8c3aec: ldur            x2, [fp, #-8]
    // 0x8c3af0: mov             x3, x0
    // 0x8c3af4: r0 = putIfAbsent()
    //     0x8c3af4: bl              #0x7661b4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::putIfAbsent
    // 0x8c3af8: mov             x1, x0
    // 0x8c3afc: ldur            x0, [fp, #-0x18]
    // 0x8c3b00: LoadField: r2 = r0->field_b
    //     0x8c3b00: ldur            w2, [x0, #0xb]
    // 0x8c3b04: DecompressPointer r2
    //     0x8c3b04: add             x2, x2, HEAP, lsl #32
    // 0x8c3b08: LoadField: r3 = r0->field_f
    //     0x8c3b08: ldur            w3, [x0, #0xf]
    // 0x8c3b0c: DecompressPointer r3
    //     0x8c3b0c: add             x3, x3, HEAP, lsl #32
    // 0x8c3b10: r0 = LoadClassIdInstr(r1)
    //     0x8c3b10: ldur            x0, [x1, #-1]
    //     0x8c3b14: ubfx            x0, x0, #0xc, #0x14
    // 0x8c3b18: r0 = GDT[cid_x0 + -0x10d]()
    //     0x8c3b18: sub             lr, x0, #0x10d
    //     0x8c3b1c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c3b20: blr             lr
    // 0x8c3b24: r0 = Null
    //     0x8c3b24: mov             x0, NULL
    // 0x8c3b28: LeaveFrame
    //     0x8c3b28: mov             SP, fp
    //     0x8c3b2c: ldp             fp, lr, [SP], #0x10
    // 0x8c3b30: ret
    //     0x8c3b30: ret             
    // 0x8c3b34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3b34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3b38: b               #0x8c3ac0
  }
  [closure] Map<String, (dynamic) => dynamic> <anonymous closure>(dynamic) {
    // ** addr: 0x8c3b3c, size: 0x40
    // 0x8c3b3c: EnterFrame
    //     0x8c3b3c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3b40: mov             fp, SP
    // 0x8c3b44: AllocStack(0x10)
    //     0x8c3b44: sub             SP, SP, #0x10
    // 0x8c3b48: CheckStackOverflow
    //     0x8c3b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3b4c: cmp             SP, x16
    //     0x8c3b50: b.ls            #0x8c3b74
    // 0x8c3b54: r16 = <String, (dynamic this) => dynamic>
    //     0x8c3b54: add             x16, PP, #0x18, lsl #12  ; [pp+0x18020] TypeArguments: <String, (dynamic this) => dynamic>
    //     0x8c3b58: ldr             x16, [x16, #0x20]
    // 0x8c3b5c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8c3b60: stp             lr, x16, [SP]
    // 0x8c3b64: r0 = Map._fromLiteral()
    //     0x8c3b64: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8c3b68: LeaveFrame
    //     0x8c3b68: mov             SP, fp
    //     0x8c3b6c: ldp             fp, lr, [SP], #0x10
    // 0x8c3b70: ret
    //     0x8c3b70: ret             
    // 0x8c3b74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3b74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3b78: b               #0x8c3b54
  }
  _ _addDynamicFactoryConfig(/* No info */) {
    // ** addr: 0x8c3b7c, size: 0x94
    // 0x8c3b7c: EnterFrame
    //     0x8c3b7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3b80: mov             fp, SP
    // 0x8c3b84: AllocStack(0x18)
    //     0x8c3b84: sub             SP, SP, #0x18
    // 0x8c3b88: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x8c3b88: mov             x0, x2
    //     0x8c3b8c: stur            x2, [fp, #-0x18]
    // 0x8c3b90: CheckStackOverflow
    //     0x8c3b90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3b94: cmp             SP, x16
    //     0x8c3b98: b.ls            #0x8c3c08
    // 0x8c3b9c: LoadField: r3 = r1->field_b
    //     0x8c3b9c: ldur            w3, [x1, #0xb]
    // 0x8c3ba0: DecompressPointer r3
    //     0x8c3ba0: add             x3, x3, HEAP, lsl #32
    // 0x8c3ba4: stur            x3, [fp, #-0x10]
    // 0x8c3ba8: LoadField: r4 = r0->field_7
    //     0x8c3ba8: ldur            w4, [x0, #7]
    // 0x8c3bac: DecompressPointer r4
    //     0x8c3bac: add             x4, x4, HEAP, lsl #32
    // 0x8c3bb0: stur            x4, [fp, #-8]
    // 0x8c3bb4: r1 = Function '<anonymous closure>':.
    //     0x8c3bb4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18028] AnonymousClosure: (0x8c3c10), in [package:pointycastle/src/registry/registry.dart] _RegistryImpl::_addDynamicFactoryConfig (0x8c3b7c)
    //     0x8c3bb8: ldr             x1, [x1, #0x28]
    // 0x8c3bbc: r2 = Null
    //     0x8c3bbc: mov             x2, NULL
    // 0x8c3bc0: r0 = AllocateClosure()
    //     0x8c3bc0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c3bc4: ldur            x1, [fp, #-0x10]
    // 0x8c3bc8: ldur            x2, [fp, #-8]
    // 0x8c3bcc: mov             x3, x0
    // 0x8c3bd0: r0 = putIfAbsent()
    //     0x8c3bd0: bl              #0x7661b4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::putIfAbsent
    // 0x8c3bd4: r1 = LoadClassIdInstr(r0)
    //     0x8c3bd4: ldur            x1, [x0, #-1]
    //     0x8c3bd8: ubfx            x1, x1, #0xc, #0x14
    // 0x8c3bdc: mov             x16, x0
    // 0x8c3be0: mov             x0, x1
    // 0x8c3be4: mov             x1, x16
    // 0x8c3be8: ldur            x2, [fp, #-0x18]
    // 0x8c3bec: r0 = GDT[cid_x0 + -0xb05]()
    //     0x8c3bec: sub             lr, x0, #0xb05
    //     0x8c3bf0: ldr             lr, [x21, lr, lsl #3]
    //     0x8c3bf4: blr             lr
    // 0x8c3bf8: r0 = Null
    //     0x8c3bf8: mov             x0, NULL
    // 0x8c3bfc: LeaveFrame
    //     0x8c3bfc: mov             SP, fp
    //     0x8c3c00: ldp             fp, lr, [SP], #0x10
    // 0x8c3c04: ret
    //     0x8c3c04: ret             
    // 0x8c3c08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3c08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3c0c: b               #0x8c3b9c
  }
  [closure] Set<DynamicFactoryConfig> <anonymous closure>(dynamic) {
    // ** addr: 0x8c3c10, size: 0x9c
    // 0x8c3c10: EnterFrame
    //     0x8c3c10: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3c14: mov             fp, SP
    // 0x8c3c18: AllocStack(0x10)
    //     0x8c3c18: sub             SP, SP, #0x10
    // 0x8c3c1c: CheckStackOverflow
    //     0x8c3c1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3c20: cmp             SP, x16
    //     0x8c3c24: b.ls            #0x8c3ca4
    // 0x8c3c28: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x8c3c28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c3c2c: ldr             x0, [x0, #0x778]
    //     0x8c3c30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c3c34: cmp             w0, w16
    //     0x8c3c38: b.ne            #0x8c3c44
    //     0x8c3c3c: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x8c3c40: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c3c44: r1 = <DynamicFactoryConfig>
    //     0x8c3c44: add             x1, PP, #0x18, lsl #12  ; [pp+0x18030] TypeArguments: <DynamicFactoryConfig>
    //     0x8c3c48: ldr             x1, [x1, #0x30]
    // 0x8c3c4c: stur            x0, [fp, #-8]
    // 0x8c3c50: r0 = _Set()
    //     0x8c3c50: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x8c3c54: mov             x1, x0
    // 0x8c3c58: ldur            x0, [fp, #-8]
    // 0x8c3c5c: stur            x1, [fp, #-0x10]
    // 0x8c3c60: StoreField: r1->field_1b = r0
    //     0x8c3c60: stur            w0, [x1, #0x1b]
    // 0x8c3c64: StoreField: r1->field_b = rZR
    //     0x8c3c64: stur            wzr, [x1, #0xb]
    // 0x8c3c68: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x8c3c68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c3c6c: ldr             x0, [x0, #0x780]
    //     0x8c3c70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c3c74: cmp             w0, w16
    //     0x8c3c78: b.ne            #0x8c3c84
    //     0x8c3c7c: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x8c3c80: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c3c84: mov             x1, x0
    // 0x8c3c88: ldur            x0, [fp, #-0x10]
    // 0x8c3c8c: StoreField: r0->field_f = r1
    //     0x8c3c8c: stur            w1, [x0, #0xf]
    // 0x8c3c90: StoreField: r0->field_13 = rZR
    //     0x8c3c90: stur            wzr, [x0, #0x13]
    // 0x8c3c94: ArrayStore: r0[0] = rZR  ; List_4
    //     0x8c3c94: stur            wzr, [x0, #0x17]
    // 0x8c3c98: LeaveFrame
    //     0x8c3c98: mov             SP, fp
    //     0x8c3c9c: ldp             fp, lr, [SP], #0x10
    // 0x8c3ca0: ret
    //     0x8c3ca0: ret             
    // 0x8c3ca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3ca4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3ca8: b               #0x8c3c28
  }
  _ _RegistryImpl(/* No info */) {
    // ** addr: 0x8e9264, size: 0xd8
    // 0x8e9264: EnterFrame
    //     0x8e9264: stp             fp, lr, [SP, #-0x10]!
    //     0x8e9268: mov             fp, SP
    // 0x8e926c: AllocStack(0x18)
    //     0x8e926c: sub             SP, SP, #0x18
    // 0x8e9270: r0 = false
    //     0x8e9270: add             x0, NULL, #0x30  ; false
    // 0x8e9274: stur            x1, [fp, #-8]
    // 0x8e9278: CheckStackOverflow
    //     0x8e9278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e927c: cmp             SP, x16
    //     0x8e9280: b.ls            #0x8e9334
    // 0x8e9284: StoreField: r1->field_13 = r0
    //     0x8e9284: stur            w0, [x1, #0x13]
    // 0x8e9288: r16 = <String, (dynamic this) => dynamic>
    //     0x8e9288: add             x16, PP, #0x18, lsl #12  ; [pp+0x18020] TypeArguments: <String, (dynamic this) => dynamic>
    //     0x8e928c: ldr             x16, [x16, #0x20]
    // 0x8e9290: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8e9294: stp             lr, x16, [SP]
    // 0x8e9298: r0 = Map._fromLiteral()
    //     0x8e9298: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8e929c: ldur            x1, [fp, #-8]
    // 0x8e92a0: StoreField: r1->field_f = r0
    //     0x8e92a0: stur            w0, [x1, #0xf]
    //     0x8e92a4: ldurb           w16, [x1, #-1]
    //     0x8e92a8: ldurb           w17, [x0, #-1]
    //     0x8e92ac: and             x16, x17, x16, lsr #2
    //     0x8e92b0: tst             x16, HEAP, lsr #32
    //     0x8e92b4: b.eq            #0x8e92bc
    //     0x8e92b8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e92bc: r16 = <Type, Map<String, (dynamic this) => dynamic>>
    //     0x8e92bc: add             x16, PP, #0x19, lsl #12  ; [pp+0x19c50] TypeArguments: <Type, Map<String, (dynamic this) => dynamic>>
    //     0x8e92c0: ldr             x16, [x16, #0xc50]
    // 0x8e92c4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8e92c8: stp             lr, x16, [SP]
    // 0x8e92cc: r0 = Map._fromLiteral()
    //     0x8e92cc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8e92d0: ldur            x1, [fp, #-8]
    // 0x8e92d4: StoreField: r1->field_7 = r0
    //     0x8e92d4: stur            w0, [x1, #7]
    //     0x8e92d8: ldurb           w16, [x1, #-1]
    //     0x8e92dc: ldurb           w17, [x0, #-1]
    //     0x8e92e0: and             x16, x17, x16, lsr #2
    //     0x8e92e4: tst             x16, HEAP, lsr #32
    //     0x8e92e8: b.eq            #0x8e92f0
    //     0x8e92ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e92f0: r16 = <Type, Set<DynamicFactoryConfig>>
    //     0x8e92f0: add             x16, PP, #0x19, lsl #12  ; [pp+0x19c58] TypeArguments: <Type, Set<DynamicFactoryConfig>>
    //     0x8e92f4: ldr             x16, [x16, #0xc58]
    // 0x8e92f8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8e92fc: stp             lr, x16, [SP]
    // 0x8e9300: r0 = Map._fromLiteral()
    //     0x8e9300: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8e9304: ldur            x1, [fp, #-8]
    // 0x8e9308: StoreField: r1->field_b = r0
    //     0x8e9308: stur            w0, [x1, #0xb]
    //     0x8e930c: ldurb           w16, [x1, #-1]
    //     0x8e9310: ldurb           w17, [x0, #-1]
    //     0x8e9314: and             x16, x17, x16, lsr #2
    //     0x8e9318: tst             x16, HEAP, lsr #32
    //     0x8e931c: b.eq            #0x8e9324
    //     0x8e9320: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e9324: r0 = Null
    //     0x8e9324: mov             x0, NULL
    // 0x8e9328: LeaveFrame
    //     0x8e9328: mov             SP, fp
    //     0x8e932c: ldp             fp, lr, [SP], #0x10
    // 0x8e9330: ret
    //     0x8e9330: ret             
    // 0x8e9334: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e9334: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e9338: b               #0x8e9284
  }
}

// class id: 547, size: 0xc, field offset: 0x8
abstract class FactoryConfig extends Object {
}

// class id: 548, size: 0x14, field offset: 0xc
class DynamicFactoryConfig extends FactoryConfig {

  _ DynamicFactoryConfig.suffix(/* No info */) {
    // ** addr: 0x8c3e1c, size: 0x134
    // 0x8c3e1c: EnterFrame
    //     0x8c3e1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3e20: mov             fp, SP
    // 0x8c3e24: AllocStack(0x58)
    //     0x8c3e24: sub             SP, SP, #0x58
    // 0x8c3e28: SetupParameters(DynamicFactoryConfig this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x8c3e28: mov             x0, x5
    //     0x8c3e2c: stur            x5, [fp, #-0x20]
    //     0x8c3e30: mov             x5, x1
    //     0x8c3e34: mov             x4, x2
    //     0x8c3e38: stur            x1, [fp, #-8]
    //     0x8c3e3c: stur            x2, [fp, #-0x10]
    //     0x8c3e40: stur            x3, [fp, #-0x18]
    // 0x8c3e44: CheckStackOverflow
    //     0x8c3e44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3e48: cmp             SP, x16
    //     0x8c3e4c: b.ls            #0x8c3f48
    // 0x8c3e50: r1 = Null
    //     0x8c3e50: mov             x1, NULL
    // 0x8c3e54: r2 = 6
    //     0x8c3e54: movz            x2, #0x6
    // 0x8c3e58: r0 = AllocateArray()
    //     0x8c3e58: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c3e5c: stur            x0, [fp, #-0x28]
    // 0x8c3e60: r16 = "^(.+)"
    //     0x8c3e60: add             x16, PP, #0x18, lsl #12  ; [pp+0x18090] "^(.+)"
    //     0x8c3e64: ldr             x16, [x16, #0x90]
    // 0x8c3e68: StoreField: r0->field_f = r16
    //     0x8c3e68: stur            w16, [x0, #0xf]
    // 0x8c3e6c: ldur            x1, [fp, #-0x18]
    // 0x8c3e70: r0 = _escapeRegExp()
    //     0x8c3e70: bl              #0x8c3f50  ; [package:pointycastle/src/registry/registry.dart] ::_escapeRegExp
    // 0x8c3e74: ldur            x1, [fp, #-0x28]
    // 0x8c3e78: ArrayStore: r1[1] = r0  ; List_4
    //     0x8c3e78: add             x25, x1, #0x13
    //     0x8c3e7c: str             w0, [x25]
    //     0x8c3e80: tbz             w0, #0, #0x8c3e9c
    //     0x8c3e84: ldurb           w16, [x1, #-1]
    //     0x8c3e88: ldurb           w17, [x0, #-1]
    //     0x8c3e8c: and             x16, x17, x16, lsr #2
    //     0x8c3e90: tst             x16, HEAP, lsr #32
    //     0x8c3e94: b.eq            #0x8c3e9c
    //     0x8c3e98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8c3e9c: ldur            x0, [fp, #-0x28]
    // 0x8c3ea0: r16 = "$"
    //     0x8c3ea0: add             x16, PP, #0x18, lsl #12  ; [pp+0x18098] "$"
    //     0x8c3ea4: ldr             x16, [x16, #0x98]
    // 0x8c3ea8: ArrayStore: r0[0] = r16  ; List_4
    //     0x8c3ea8: stur            w16, [x0, #0x17]
    // 0x8c3eac: str             x0, [SP]
    // 0x8c3eb0: r0 = _interpolate()
    //     0x8c3eb0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c3eb4: stp             x0, NULL, [SP, #0x20]
    // 0x8c3eb8: r16 = false
    //     0x8c3eb8: add             x16, NULL, #0x30  ; false
    // 0x8c3ebc: r30 = true
    //     0x8c3ebc: add             lr, NULL, #0x20  ; true
    // 0x8c3ec0: stp             lr, x16, [SP, #0x10]
    // 0x8c3ec4: r16 = false
    //     0x8c3ec4: add             x16, NULL, #0x30  ; false
    // 0x8c3ec8: r30 = false
    //     0x8c3ec8: add             lr, NULL, #0x30  ; false
    // 0x8c3ecc: stp             lr, x16, [SP]
    // 0x8c3ed0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8c3ed0: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8c3ed4: r0 = _RegExp()
    //     0x8c3ed4: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8c3ed8: ldur            x1, [fp, #-8]
    // 0x8c3edc: StoreField: r1->field_b = r0
    //     0x8c3edc: stur            w0, [x1, #0xb]
    //     0x8c3ee0: ldurb           w16, [x1, #-1]
    //     0x8c3ee4: ldurb           w17, [x0, #-1]
    //     0x8c3ee8: and             x16, x17, x16, lsr #2
    //     0x8c3eec: tst             x16, HEAP, lsr #32
    //     0x8c3ef0: b.eq            #0x8c3ef8
    //     0x8c3ef4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c3ef8: ldur            x0, [fp, #-0x20]
    // 0x8c3efc: StoreField: r1->field_f = r0
    //     0x8c3efc: stur            w0, [x1, #0xf]
    //     0x8c3f00: ldurb           w16, [x1, #-1]
    //     0x8c3f04: ldurb           w17, [x0, #-1]
    //     0x8c3f08: and             x16, x17, x16, lsr #2
    //     0x8c3f0c: tst             x16, HEAP, lsr #32
    //     0x8c3f10: b.eq            #0x8c3f18
    //     0x8c3f14: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c3f18: ldur            x0, [fp, #-0x10]
    // 0x8c3f1c: StoreField: r1->field_7 = r0
    //     0x8c3f1c: stur            w0, [x1, #7]
    //     0x8c3f20: ldurb           w16, [x1, #-1]
    //     0x8c3f24: ldurb           w17, [x0, #-1]
    //     0x8c3f28: and             x16, x17, x16, lsr #2
    //     0x8c3f2c: tst             x16, HEAP, lsr #32
    //     0x8c3f30: b.eq            #0x8c3f38
    //     0x8c3f34: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c3f38: r0 = Null
    //     0x8c3f38: mov             x0, NULL
    // 0x8c3f3c: LeaveFrame
    //     0x8c3f3c: mov             SP, fp
    //     0x8c3f40: ldp             fp, lr, [SP], #0x10
    // 0x8c3f44: ret
    //     0x8c3f44: ret             
    // 0x8c3f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3f48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3f4c: b               #0x8c3e50
  }
  _ DynamicFactoryConfig.prefix(/* No info */) {
    // ** addr: 0x8c5098, size: 0x134
    // 0x8c5098: EnterFrame
    //     0x8c5098: stp             fp, lr, [SP, #-0x10]!
    //     0x8c509c: mov             fp, SP
    // 0x8c50a0: AllocStack(0x58)
    //     0x8c50a0: sub             SP, SP, #0x58
    // 0x8c50a4: SetupParameters(DynamicFactoryConfig this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x8c50a4: mov             x0, x5
    //     0x8c50a8: stur            x5, [fp, #-0x20]
    //     0x8c50ac: mov             x5, x1
    //     0x8c50b0: mov             x4, x2
    //     0x8c50b4: stur            x1, [fp, #-8]
    //     0x8c50b8: stur            x2, [fp, #-0x10]
    //     0x8c50bc: stur            x3, [fp, #-0x18]
    // 0x8c50c0: CheckStackOverflow
    //     0x8c50c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c50c4: cmp             SP, x16
    //     0x8c50c8: b.ls            #0x8c51c4
    // 0x8c50cc: r1 = Null
    //     0x8c50cc: mov             x1, NULL
    // 0x8c50d0: r2 = 6
    //     0x8c50d0: movz            x2, #0x6
    // 0x8c50d4: r0 = AllocateArray()
    //     0x8c50d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c50d8: stur            x0, [fp, #-0x28]
    // 0x8c50dc: r16 = "^"
    //     0x8c50dc: add             x16, PP, #0x18, lsl #12  ; [pp+0x18118] "^"
    //     0x8c50e0: ldr             x16, [x16, #0x118]
    // 0x8c50e4: StoreField: r0->field_f = r16
    //     0x8c50e4: stur            w16, [x0, #0xf]
    // 0x8c50e8: ldur            x1, [fp, #-0x18]
    // 0x8c50ec: r0 = _escapeRegExp()
    //     0x8c50ec: bl              #0x8c3f50  ; [package:pointycastle/src/registry/registry.dart] ::_escapeRegExp
    // 0x8c50f0: ldur            x1, [fp, #-0x28]
    // 0x8c50f4: ArrayStore: r1[1] = r0  ; List_4
    //     0x8c50f4: add             x25, x1, #0x13
    //     0x8c50f8: str             w0, [x25]
    //     0x8c50fc: tbz             w0, #0, #0x8c5118
    //     0x8c5100: ldurb           w16, [x1, #-1]
    //     0x8c5104: ldurb           w17, [x0, #-1]
    //     0x8c5108: and             x16, x17, x16, lsr #2
    //     0x8c510c: tst             x16, HEAP, lsr #32
    //     0x8c5110: b.eq            #0x8c5118
    //     0x8c5114: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8c5118: ldur            x0, [fp, #-0x28]
    // 0x8c511c: r16 = "(.+)$"
    //     0x8c511c: add             x16, PP, #0x18, lsl #12  ; [pp+0x18120] "(.+)$"
    //     0x8c5120: ldr             x16, [x16, #0x120]
    // 0x8c5124: ArrayStore: r0[0] = r16  ; List_4
    //     0x8c5124: stur            w16, [x0, #0x17]
    // 0x8c5128: str             x0, [SP]
    // 0x8c512c: r0 = _interpolate()
    //     0x8c512c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8c5130: stp             x0, NULL, [SP, #0x20]
    // 0x8c5134: r16 = false
    //     0x8c5134: add             x16, NULL, #0x30  ; false
    // 0x8c5138: r30 = true
    //     0x8c5138: add             lr, NULL, #0x20  ; true
    // 0x8c513c: stp             lr, x16, [SP, #0x10]
    // 0x8c5140: r16 = false
    //     0x8c5140: add             x16, NULL, #0x30  ; false
    // 0x8c5144: r30 = false
    //     0x8c5144: add             lr, NULL, #0x30  ; false
    // 0x8c5148: stp             lr, x16, [SP]
    // 0x8c514c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8c514c: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8c5150: r0 = _RegExp()
    //     0x8c5150: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x8c5154: ldur            x1, [fp, #-8]
    // 0x8c5158: StoreField: r1->field_b = r0
    //     0x8c5158: stur            w0, [x1, #0xb]
    //     0x8c515c: ldurb           w16, [x1, #-1]
    //     0x8c5160: ldurb           w17, [x0, #-1]
    //     0x8c5164: and             x16, x17, x16, lsr #2
    //     0x8c5168: tst             x16, HEAP, lsr #32
    //     0x8c516c: b.eq            #0x8c5174
    //     0x8c5170: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c5174: ldur            x0, [fp, #-0x20]
    // 0x8c5178: StoreField: r1->field_f = r0
    //     0x8c5178: stur            w0, [x1, #0xf]
    //     0x8c517c: ldurb           w16, [x1, #-1]
    //     0x8c5180: ldurb           w17, [x0, #-1]
    //     0x8c5184: and             x16, x17, x16, lsr #2
    //     0x8c5188: tst             x16, HEAP, lsr #32
    //     0x8c518c: b.eq            #0x8c5194
    //     0x8c5190: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c5194: ldur            x0, [fp, #-0x10]
    // 0x8c5198: StoreField: r1->field_7 = r0
    //     0x8c5198: stur            w0, [x1, #7]
    //     0x8c519c: ldurb           w16, [x1, #-1]
    //     0x8c51a0: ldurb           w17, [x0, #-1]
    //     0x8c51a4: and             x16, x17, x16, lsr #2
    //     0x8c51a8: tst             x16, HEAP, lsr #32
    //     0x8c51ac: b.eq            #0x8c51b4
    //     0x8c51b0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c51b4: r0 = Null
    //     0x8c51b4: mov             x0, NULL
    // 0x8c51b8: LeaveFrame
    //     0x8c51b8: mov             SP, fp
    //     0x8c51bc: ldp             fp, lr, [SP], #0x10
    // 0x8c51c0: ret
    //     0x8c51c0: ret             
    // 0x8c51c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c51c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c51c8: b               #0x8c50cc
  }
}

// class id: 549, size: 0x14, field offset: 0xc
class StaticFactoryConfig extends FactoryConfig {
}

// class id: 550, size: 0x8, field offset: 0x8
abstract class FactoryRegistry extends Object {
}
