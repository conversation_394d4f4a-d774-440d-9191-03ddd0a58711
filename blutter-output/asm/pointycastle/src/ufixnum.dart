// lib: , url: package:pointycastle/src/ufixnum.dart

// class id: 1051048, size: 0x8
class :: {

  static late final List<int> _MASK32_HI_BITS; // offset: 0xf58

  static _ shiftl32(/* No info */) {
    // ** addr: 0x8df4f8, size: 0xe4
    // 0x8df4f8: EnterFrame
    //     0x8df4f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8df4fc: mov             fp, SP
    // 0x8df500: AllocStack(0x10)
    //     0x8df500: sub             SP, SP, #0x10
    // 0x8df504: r0 = 31
    //     0x8df504: movz            x0, #0x1f
    // 0x8df508: stur            x1, [fp, #-0x10]
    // 0x8df50c: CheckStackOverflow
    //     0x8df50c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8df510: cmp             SP, x16
    //     0x8df514: b.ls            #0x8df5b4
    // 0x8df518: ubfx            x2, x2, #0, #0x20
    // 0x8df51c: and             x3, x2, x0
    // 0x8df520: stur            x3, [fp, #-8]
    // 0x8df524: r0 = InitLateStaticField(0xf58) // [package:pointycastle/src/ufixnum.dart] ::_MASK32_HI_BITS
    //     0x8df524: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8df528: ldr             x0, [x0, #0x1eb0]
    //     0x8df52c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8df530: cmp             w0, w16
    //     0x8df534: b.ne            #0x8df544
    //     0x8df538: add             x2, PP, #0x19, lsl #12  ; [pp+0x194e0] Field <::._MASK32_HI_BITS@1011143242>: static late final (offset: 0xf58)
    //     0x8df53c: ldr             x2, [x2, #0x4e0]
    //     0x8df540: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8df544: mov             x2, x0
    // 0x8df548: LoadField: r3 = r2->field_b
    //     0x8df548: ldur            w3, [x2, #0xb]
    // 0x8df54c: r0 = LoadInt32Instr(r3)
    //     0x8df54c: sbfx            x0, x3, #1, #0x1f
    // 0x8df550: ldur            x3, [fp, #-8]
    // 0x8df554: ubfx            x3, x3, #0, #0x20
    // 0x8df558: mov             x1, x3
    // 0x8df55c: cmp             x1, x0
    // 0x8df560: b.hs            #0x8df5bc
    // 0x8df564: LoadField: r1 = r2->field_f
    //     0x8df564: ldur            w1, [x2, #0xf]
    // 0x8df568: DecompressPointer r1
    //     0x8df568: add             x1, x1, HEAP, lsl #32
    // 0x8df56c: ArrayLoad: r2 = r1[r3]  ; Unknown_4
    //     0x8df56c: add             x16, x1, x3, lsl #2
    //     0x8df570: ldur            w2, [x16, #0xf]
    // 0x8df574: DecompressPointer r2
    //     0x8df574: add             x2, x2, HEAP, lsl #32
    // 0x8df578: r1 = LoadInt32Instr(r2)
    //     0x8df578: sbfx            x1, x2, #1, #0x1f
    //     0x8df57c: tbz             w2, #0, #0x8df584
    //     0x8df580: ldur            x1, [x2, #7]
    // 0x8df584: ldur            x2, [fp, #-0x10]
    // 0x8df588: ubfx            x2, x2, #0, #0x20
    // 0x8df58c: and             x4, x2, x1
    // 0x8df590: tbnz            x3, #0x3f, #0x8df5c0
    // 0x8df594: lsl             w1, w4, w3
    // 0x8df598: cmp             x3, #0x1f
    // 0x8df59c: csel            x1, x1, xzr, le
    // 0x8df5a0: ubfx            x1, x1, #0, #0x20
    // 0x8df5a4: mov             x0, x1
    // 0x8df5a8: LeaveFrame
    //     0x8df5a8: mov             SP, fp
    //     0x8df5ac: ldp             fp, lr, [SP], #0x10
    // 0x8df5b0: ret
    //     0x8df5b0: ret             
    // 0x8df5b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8df5b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8df5b8: b               #0x8df518
    // 0x8df5bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8df5bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8df5c0: str             x3, [THR, #0x7a8]  ; THR::
    // 0x8df5c4: stp             x3, x4, [SP, #-0x10]!
    // 0x8df5c8: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x8df5cc: r4 = 0
    //     0x8df5cc: movz            x4, #0
    // 0x8df5d0: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x8df5d4: blr             lr
    // 0x8df5d8: brk             #0
  }
  static List<int> _MASK32_HI_BITS() {
    // ** addr: 0x8df5dc, size: 0x150
    // 0x8df5dc: EnterFrame
    //     0x8df5dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8df5e0: mov             fp, SP
    // 0x8df5e4: AllocStack(0x8)
    //     0x8df5e4: sub             SP, SP, #8
    // 0x8df5e8: r0 = 66
    //     0x8df5e8: movz            x0, #0x42
    // 0x8df5ec: mov             x2, x0
    // 0x8df5f0: r1 = <int>
    //     0x8df5f0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8df5f4: r0 = AllocateArray()
    //     0x8df5f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8df5f8: stur            x0, [fp, #-8]
    // 0x8df5fc: r16 = 4294967295
    //     0x8df5fc: add             x16, PP, #0x19, lsl #12  ; [pp+0x194e8] 0xffffffff
    //     0x8df600: ldr             x16, [x16, #0x4e8]
    // 0x8df604: StoreField: r0->field_f = r16
    //     0x8df604: stur            w16, [x0, #0xf]
    // 0x8df608: r16 = 2147483647
    //     0x8df608: add             x16, PP, #0x19, lsl #12  ; [pp+0x194f0] 0x7fffffff
    //     0x8df60c: ldr             x16, [x16, #0x4f0]
    // 0x8df610: StoreField: r0->field_13 = r16
    //     0x8df610: stur            w16, [x0, #0x13]
    // 0x8df614: r16 = 2147483646
    //     0x8df614: orr             x16, xzr, #0x7ffffffe
    // 0x8df618: ArrayStore: r0[0] = r16  ; List_4
    //     0x8df618: stur            w16, [x0, #0x17]
    // 0x8df61c: r16 = 1073741822
    //     0x8df61c: orr             x16, xzr, #0x3ffffffe
    // 0x8df620: StoreField: r0->field_1b = r16
    //     0x8df620: stur            w16, [x0, #0x1b]
    // 0x8df624: r16 = 536870910
    //     0x8df624: orr             x16, xzr, #0x1ffffffe
    // 0x8df628: StoreField: r0->field_1f = r16
    //     0x8df628: stur            w16, [x0, #0x1f]
    // 0x8df62c: r16 = 268435454
    //     0x8df62c: orr             x16, xzr, #0xffffffe
    // 0x8df630: StoreField: r0->field_23 = r16
    //     0x8df630: stur            w16, [x0, #0x23]
    // 0x8df634: r16 = 134217726
    //     0x8df634: orr             x16, xzr, #0x7fffffe
    // 0x8df638: StoreField: r0->field_27 = r16
    //     0x8df638: stur            w16, [x0, #0x27]
    // 0x8df63c: r16 = 67108862
    //     0x8df63c: orr             x16, xzr, #0x3fffffe
    // 0x8df640: StoreField: r0->field_2b = r16
    //     0x8df640: stur            w16, [x0, #0x2b]
    // 0x8df644: r16 = 33554430
    //     0x8df644: orr             x16, xzr, #0x1fffffe
    // 0x8df648: StoreField: r0->field_2f = r16
    //     0x8df648: stur            w16, [x0, #0x2f]
    // 0x8df64c: r16 = 16777214
    //     0x8df64c: orr             x16, xzr, #0xfffffe
    // 0x8df650: StoreField: r0->field_33 = r16
    //     0x8df650: stur            w16, [x0, #0x33]
    // 0x8df654: r16 = 8388606
    //     0x8df654: orr             x16, xzr, #0x7ffffe
    // 0x8df658: StoreField: r0->field_37 = r16
    //     0x8df658: stur            w16, [x0, #0x37]
    // 0x8df65c: r16 = 4194302
    //     0x8df65c: orr             x16, xzr, #0x3ffffe
    // 0x8df660: StoreField: r0->field_3b = r16
    //     0x8df660: stur            w16, [x0, #0x3b]
    // 0x8df664: r16 = 2097150
    //     0x8df664: orr             x16, xzr, #0x1ffffe
    // 0x8df668: StoreField: r0->field_3f = r16
    //     0x8df668: stur            w16, [x0, #0x3f]
    // 0x8df66c: r16 = 1048574
    //     0x8df66c: orr             x16, xzr, #0xffffe
    // 0x8df670: StoreField: r0->field_43 = r16
    //     0x8df670: stur            w16, [x0, #0x43]
    // 0x8df674: r16 = 524286
    //     0x8df674: orr             x16, xzr, #0x7fffe
    // 0x8df678: StoreField: r0->field_47 = r16
    //     0x8df678: stur            w16, [x0, #0x47]
    // 0x8df67c: r16 = 262142
    //     0x8df67c: orr             x16, xzr, #0x3fffe
    // 0x8df680: StoreField: r0->field_4b = r16
    //     0x8df680: stur            w16, [x0, #0x4b]
    // 0x8df684: r16 = 131070
    //     0x8df684: orr             x16, xzr, #0x1fffe
    // 0x8df688: StoreField: r0->field_4f = r16
    //     0x8df688: stur            w16, [x0, #0x4f]
    // 0x8df68c: r16 = 65534
    //     0x8df68c: orr             x16, xzr, #0xfffe
    // 0x8df690: StoreField: r0->field_53 = r16
    //     0x8df690: stur            w16, [x0, #0x53]
    // 0x8df694: r16 = 32766
    //     0x8df694: orr             x16, xzr, #0x7ffe
    // 0x8df698: StoreField: r0->field_57 = r16
    //     0x8df698: stur            w16, [x0, #0x57]
    // 0x8df69c: r16 = 16382
    //     0x8df69c: orr             x16, xzr, #0x3ffe
    // 0x8df6a0: StoreField: r0->field_5b = r16
    //     0x8df6a0: stur            w16, [x0, #0x5b]
    // 0x8df6a4: r16 = 8190
    //     0x8df6a4: orr             x16, xzr, #0x1ffe
    // 0x8df6a8: StoreField: r0->field_5f = r16
    //     0x8df6a8: stur            w16, [x0, #0x5f]
    // 0x8df6ac: r16 = 4094
    //     0x8df6ac: movz            x16, #0xffe
    // 0x8df6b0: StoreField: r0->field_63 = r16
    //     0x8df6b0: stur            w16, [x0, #0x63]
    // 0x8df6b4: r16 = 2046
    //     0x8df6b4: movz            x16, #0x7fe
    // 0x8df6b8: StoreField: r0->field_67 = r16
    //     0x8df6b8: stur            w16, [x0, #0x67]
    // 0x8df6bc: r16 = 1022
    //     0x8df6bc: movz            x16, #0x3fe
    // 0x8df6c0: StoreField: r0->field_6b = r16
    //     0x8df6c0: stur            w16, [x0, #0x6b]
    // 0x8df6c4: r16 = 510
    //     0x8df6c4: movz            x16, #0x1fe
    // 0x8df6c8: StoreField: r0->field_6f = r16
    //     0x8df6c8: stur            w16, [x0, #0x6f]
    // 0x8df6cc: r16 = 254
    //     0x8df6cc: movz            x16, #0xfe
    // 0x8df6d0: StoreField: r0->field_73 = r16
    //     0x8df6d0: stur            w16, [x0, #0x73]
    // 0x8df6d4: r16 = 126
    //     0x8df6d4: movz            x16, #0x7e
    // 0x8df6d8: StoreField: r0->field_77 = r16
    //     0x8df6d8: stur            w16, [x0, #0x77]
    // 0x8df6dc: r16 = 62
    //     0x8df6dc: movz            x16, #0x3e
    // 0x8df6e0: StoreField: r0->field_7b = r16
    //     0x8df6e0: stur            w16, [x0, #0x7b]
    // 0x8df6e4: r16 = 30
    //     0x8df6e4: movz            x16, #0x1e
    // 0x8df6e8: StoreField: r0->field_7f = r16
    //     0x8df6e8: stur            w16, [x0, #0x7f]
    // 0x8df6ec: r16 = 14
    //     0x8df6ec: movz            x16, #0xe
    // 0x8df6f0: StoreField: r0->field_83 = r16
    //     0x8df6f0: stur            w16, [x0, #0x83]
    // 0x8df6f4: r16 = 6
    //     0x8df6f4: movz            x16, #0x6
    // 0x8df6f8: StoreField: r0->field_87 = r16
    //     0x8df6f8: stur            w16, [x0, #0x87]
    // 0x8df6fc: r16 = 2
    //     0x8df6fc: movz            x16, #0x2
    // 0x8df700: StoreField: r0->field_8b = r16
    //     0x8df700: stur            w16, [x0, #0x8b]
    // 0x8df704: StoreField: r0->field_8f = rZR
    //     0x8df704: stur            wzr, [x0, #0x8f]
    // 0x8df708: r1 = <int>
    //     0x8df708: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8df70c: r0 = AllocateGrowableArray()
    //     0x8df70c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8df710: ldur            x1, [fp, #-8]
    // 0x8df714: StoreField: r0->field_f = r1
    //     0x8df714: stur            w1, [x0, #0xf]
    // 0x8df718: r1 = 66
    //     0x8df718: movz            x1, #0x42
    // 0x8df71c: StoreField: r0->field_b = r1
    //     0x8df71c: stur            w1, [x0, #0xb]
    // 0x8df720: LeaveFrame
    //     0x8df720: mov             SP, fp
    //     0x8df724: ldp             fp, lr, [SP], #0x10
    // 0x8df728: ret
    //     0x8df728: ret             
  }
  static _ unpack32(/* No info */) {
    // ** addr: 0x8e258c, size: 0x130
    // 0x8e258c: EnterFrame
    //     0x8e258c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2590: mov             fp, SP
    // 0x8e2594: AllocStack(0x28)
    //     0x8e2594: sub             SP, SP, #0x28
    // 0x8e2598: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x8e2598: mov             x0, x1
    //     0x8e259c: stur            x1, [fp, #-8]
    //     0x8e25a0: mov             x1, x2
    //     0x8e25a4: stur            x2, [fp, #-0x10]
    //     0x8e25a8: stur            x3, [fp, #-0x18]
    // 0x8e25ac: CheckStackOverflow
    //     0x8e25ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e25b0: cmp             SP, x16
    //     0x8e25b4: b.ls            #0x8e26ac
    // 0x8e25b8: cmp             w0, NULL
    // 0x8e25bc: b.eq            #0x8e26b4
    // 0x8e25c0: r0 = _ByteBuffer()
    //     0x8e25c0: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x8e25c4: mov             x1, x0
    // 0x8e25c8: ldur            x0, [fp, #-8]
    // 0x8e25cc: StoreField: r1->field_7 = r0
    //     0x8e25cc: stur            w0, [x1, #7]
    // 0x8e25d0: LoadField: r2 = r0->field_13
    //     0x8e25d0: ldur            w2, [x0, #0x13]
    // 0x8e25d4: stp             x2, xzr, [SP]
    // 0x8e25d8: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x8e25d8: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x8e25dc: r0 = asByteData()
    //     0x8e25dc: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x8e25e0: mov             x2, x0
    // 0x8e25e4: LoadField: r3 = r2->field_13
    //     0x8e25e4: ldur            w3, [x2, #0x13]
    // 0x8e25e8: r4 = LoadInt32Instr(r3)
    //     0x8e25e8: sbfx            x4, x3, #1, #0x1f
    // 0x8e25ec: sub             x0, x4, #3
    // 0x8e25f0: ldur            x1, [fp, #-0x10]
    // 0x8e25f4: cmp             x1, x0
    // 0x8e25f8: b.hs            #0x8e26b8
    // 0x8e25fc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x8e25fc: ldur            w1, [x2, #0x17]
    // 0x8e2600: DecompressPointer r1
    //     0x8e2600: add             x1, x1, HEAP, lsl #32
    // 0x8e2604: LoadField: r3 = r2->field_1b
    //     0x8e2604: ldur            w3, [x2, #0x1b]
    // 0x8e2608: r2 = LoadInt32Instr(r3)
    //     0x8e2608: sbfx            x2, x3, #1, #0x1f
    // 0x8e260c: ldur            x3, [fp, #-0x10]
    // 0x8e2610: add             x4, x2, x3
    // 0x8e2614: LoadField: r2 = r1->field_7
    //     0x8e2614: ldur            x2, [x1, #7]
    // 0x8e2618: ldr             w1, [x2, x4]
    // 0x8e261c: ldur            x2, [fp, #-0x18]
    // 0x8e2620: r16 = Instance_Endian
    //     0x8e2620: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e2624: ldr             x16, [x16, #0x8b8]
    // 0x8e2628: cmp             w2, w16
    // 0x8e262c: b.ne            #0x8e2640
    // 0x8e2630: mov             x2, x1
    // 0x8e2634: ubfx            x2, x2, #0, #0x20
    // 0x8e2638: mov             x0, x2
    // 0x8e263c: b               #0x8e26a0
    // 0x8e2640: r5 = 4278255360
    //     0x8e2640: movz            x5, #0xff00
    //     0x8e2644: movk            x5, #0xff00, lsl #16
    // 0x8e2648: r4 = 16711935
    //     0x8e2648: movz            x4, #0xff
    //     0x8e264c: movk            x4, #0xff, lsl #16
    // 0x8e2650: r3 = 4294901760
    //     0x8e2650: orr             x3, xzr, #0xffff0000
    // 0x8e2654: r2 = 65535
    //     0x8e2654: orr             x2, xzr, #0xffff
    // 0x8e2658: and             x6, x1, x5
    // 0x8e265c: ubfx            x6, x6, #0, #0x20
    // 0x8e2660: asr             x5, x6, #8
    // 0x8e2664: and             x6, x1, x4
    // 0x8e2668: ubfx            x6, x6, #0, #0x20
    // 0x8e266c: lsl             x1, x6, #8
    // 0x8e2670: orr             x4, x5, x1
    // 0x8e2674: mov             x1, x4
    // 0x8e2678: ubfx            x1, x1, #0, #0x20
    // 0x8e267c: and             x5, x1, x3
    // 0x8e2680: ubfx            x5, x5, #0, #0x20
    // 0x8e2684: asr             x1, x5, #0x10
    // 0x8e2688: ubfx            x4, x4, #0, #0x20
    // 0x8e268c: and             x3, x4, x2
    // 0x8e2690: ubfx            x3, x3, #0, #0x20
    // 0x8e2694: lsl             x2, x3, #0x10
    // 0x8e2698: orr             x3, x1, x2
    // 0x8e269c: mov             x0, x3
    // 0x8e26a0: LeaveFrame
    //     0x8e26a0: mov             SP, fp
    //     0x8e26a4: ldp             fp, lr, [SP], #0x10
    // 0x8e26a8: ret
    //     0x8e26a8: ret             
    // 0x8e26ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e26ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e26b0: b               #0x8e25b8
    // 0x8e26b4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x8e26b4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x8e26b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e26b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ pack32(/* No info */) {
    // ** addr: 0x8e7264, size: 0x144
    // 0x8e7264: EnterFrame
    //     0x8e7264: stp             fp, lr, [SP, #-0x10]!
    //     0x8e7268: mov             fp, SP
    // 0x8e726c: AllocStack(0x30)
    //     0x8e726c: sub             SP, SP, #0x30
    // 0x8e7270: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x8e7270: mov             x0, x1
    //     0x8e7274: stur            x1, [fp, #-8]
    //     0x8e7278: mov             x1, x3
    //     0x8e727c: stur            x2, [fp, #-0x10]
    //     0x8e7280: stur            x3, [fp, #-0x18]
    //     0x8e7284: stur            x5, [fp, #-0x20]
    // 0x8e7288: CheckStackOverflow
    //     0x8e7288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e728c: cmp             SP, x16
    //     0x8e7290: b.ls            #0x8e7398
    // 0x8e7294: cmp             w2, NULL
    // 0x8e7298: b.eq            #0x8e73a0
    // 0x8e729c: r0 = _ByteBuffer()
    //     0x8e729c: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x8e72a0: mov             x1, x0
    // 0x8e72a4: ldur            x0, [fp, #-0x10]
    // 0x8e72a8: StoreField: r1->field_7 = r0
    //     0x8e72a8: stur            w0, [x1, #7]
    // 0x8e72ac: LoadField: r2 = r0->field_13
    //     0x8e72ac: ldur            w2, [x0, #0x13]
    // 0x8e72b0: stp             x2, xzr, [SP]
    // 0x8e72b4: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x8e72b4: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x8e72b8: r0 = asByteData()
    //     0x8e72b8: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x8e72bc: mov             x2, x0
    // 0x8e72c0: LoadField: r3 = r2->field_13
    //     0x8e72c0: ldur            w3, [x2, #0x13]
    // 0x8e72c4: r4 = LoadInt32Instr(r3)
    //     0x8e72c4: sbfx            x4, x3, #1, #0x1f
    // 0x8e72c8: sub             x0, x4, #3
    // 0x8e72cc: ldur            x1, [fp, #-0x18]
    // 0x8e72d0: cmp             x1, x0
    // 0x8e72d4: b.hs            #0x8e73a4
    // 0x8e72d8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x8e72d8: ldur            w1, [x2, #0x17]
    // 0x8e72dc: DecompressPointer r1
    //     0x8e72dc: add             x1, x1, HEAP, lsl #32
    // 0x8e72e0: LoadField: r3 = r2->field_1b
    //     0x8e72e0: ldur            w3, [x2, #0x1b]
    // 0x8e72e4: r2 = LoadInt32Instr(r3)
    //     0x8e72e4: sbfx            x2, x3, #1, #0x1f
    // 0x8e72e8: ldur            x3, [fp, #-0x18]
    // 0x8e72ec: add             x4, x2, x3
    // 0x8e72f0: ldur            x2, [fp, #-0x20]
    // 0x8e72f4: r16 = Instance_Endian
    //     0x8e72f4: add             x16, PP, #0x19, lsl #12  ; [pp+0x198b8] Obj!Endian@e2cbc1
    //     0x8e72f8: ldr             x16, [x16, #0x8b8]
    // 0x8e72fc: cmp             w2, w16
    // 0x8e7300: b.ne            #0x8e730c
    // 0x8e7304: ldur            x2, [fp, #-8]
    // 0x8e7308: b               #0x8e737c
    // 0x8e730c: r6 = 4278255360
    //     0x8e730c: movz            x6, #0xff00
    //     0x8e7310: movk            x6, #0xff00, lsl #16
    // 0x8e7314: r5 = 16711935
    //     0x8e7314: movz            x5, #0xff
    //     0x8e7318: movk            x5, #0xff, lsl #16
    // 0x8e731c: r3 = 4294901760
    //     0x8e731c: orr             x3, xzr, #0xffff0000
    // 0x8e7320: r2 = 65535
    //     0x8e7320: orr             x2, xzr, #0xffff
    // 0x8e7324: ldur            x7, [fp, #-8]
    // 0x8e7328: ubfx            x7, x7, #0, #0x20
    // 0x8e732c: and             x8, x7, x6
    // 0x8e7330: ubfx            x8, x8, #0, #0x20
    // 0x8e7334: asr             x6, x8, #8
    // 0x8e7338: ldur            x7, [fp, #-8]
    // 0x8e733c: ubfx            x7, x7, #0, #0x20
    // 0x8e7340: and             x8, x7, x5
    // 0x8e7344: ubfx            x8, x8, #0, #0x20
    // 0x8e7348: lsl             x5, x8, #8
    // 0x8e734c: orr             x7, x6, x5
    // 0x8e7350: mov             x5, x7
    // 0x8e7354: ubfx            x5, x5, #0, #0x20
    // 0x8e7358: and             x6, x5, x3
    // 0x8e735c: ubfx            x6, x6, #0, #0x20
    // 0x8e7360: asr             x3, x6, #0x10
    // 0x8e7364: ubfx            x7, x7, #0, #0x20
    // 0x8e7368: and             x5, x7, x2
    // 0x8e736c: ubfx            x5, x5, #0, #0x20
    // 0x8e7370: lsl             x2, x5, #0x10
    // 0x8e7374: orr             x5, x3, x2
    // 0x8e7378: mov             x2, x5
    // 0x8e737c: ubfx            x2, x2, #0, #0x20
    // 0x8e7380: LoadField: r3 = r1->field_7
    //     0x8e7380: ldur            x3, [x1, #7]
    // 0x8e7384: str             w2, [x3, x4]
    // 0x8e7388: r0 = Null
    //     0x8e7388: mov             x0, NULL
    // 0x8e738c: LeaveFrame
    //     0x8e738c: mov             SP, fp
    //     0x8e7390: ldp             fp, lr, [SP], #0x10
    // 0x8e7394: ret
    //     0x8e7394: ret             
    // 0x8e7398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e7398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e739c: b               #0x8e7294
    // 0x8e73a0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x8e73a0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x8e73a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e73a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ rotr32(/* No info */) {
    // ** addr: 0xe84f3c, size: 0x64
    // 0xe84f3c: EnterFrame
    //     0xe84f3c: stp             fp, lr, [SP, #-0x10]!
    //     0xe84f40: mov             fp, SP
    // 0xe84f44: AllocStack(0x8)
    //     0xe84f44: sub             SP, SP, #8
    // 0xe84f48: r3 = 32
    //     0xe84f48: movz            x3, #0x20
    // 0xe84f4c: r0 = 31
    //     0xe84f4c: movz            x0, #0x1f
    // 0xe84f50: CheckStackOverflow
    //     0xe84f50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe84f54: cmp             SP, x16
    //     0xe84f58: b.ls            #0xe84f98
    // 0xe84f5c: ubfx            x2, x2, #0, #0x20
    // 0xe84f60: and             x4, x2, x0
    // 0xe84f64: mov             x0, x4
    // 0xe84f68: ubfx            x0, x0, #0, #0x20
    // 0xe84f6c: asr             x5, x1, x0
    // 0xe84f70: stur            x5, [fp, #-8]
    // 0xe84f74: ubfx            x4, x4, #0, #0x20
    // 0xe84f78: sub             x2, x3, x4
    // 0xe84f7c: r0 = shiftl32()
    //     0xe84f7c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0xe84f80: ldur            x1, [fp, #-8]
    // 0xe84f84: orr             x2, x1, x0
    // 0xe84f88: mov             x0, x2
    // 0xe84f8c: LeaveFrame
    //     0xe84f8c: mov             SP, fp
    //     0xe84f90: ldp             fp, lr, [SP], #0x10
    // 0xe84f94: ret
    //     0xe84f94: ret             
    // 0xe84f98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe84f98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe84f9c: b               #0xe84f5c
  }
  static _ shiftr32(/* No info */) {
    // ** addr: 0xe84fa0, size: 0x28
    // 0xe84fa0: EnterFrame
    //     0xe84fa0: stp             fp, lr, [SP, #-0x10]!
    //     0xe84fa4: mov             fp, SP
    // 0xe84fa8: r3 = 31
    //     0xe84fa8: movz            x3, #0x1f
    // 0xe84fac: ubfx            x2, x2, #0, #0x20
    // 0xe84fb0: and             x4, x2, x3
    // 0xe84fb4: ubfx            x4, x4, #0, #0x20
    // 0xe84fb8: asr             x0, x1, x4
    // 0xe84fbc: LeaveFrame
    //     0xe84fbc: mov             SP, fp
    //     0xe84fc0: ldp             fp, lr, [SP], #0x10
    // 0xe84fc4: ret
    //     0xe84fc4: ret             
  }
}

// class id: 544, size: 0xc, field offset: 0x8
class Register64List extends Object {

  Register64 [](Register64List, int) {
    // ** addr: 0x8d7060, size: 0xa8
    // 0x8d7060: EnterFrame
    //     0x8d7060: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7064: mov             fp, SP
    // 0x8d7068: ldr             x0, [fp, #0x10]
    // 0x8d706c: r2 = Null
    //     0x8d706c: mov             x2, NULL
    // 0x8d7070: r1 = Null
    //     0x8d7070: mov             x1, NULL
    // 0x8d7074: branchIfSmi(r0, 0x8d709c)
    //     0x8d7074: tbz             w0, #0, #0x8d709c
    // 0x8d7078: r4 = LoadClassIdInstr(r0)
    //     0x8d7078: ldur            x4, [x0, #-1]
    //     0x8d707c: ubfx            x4, x4, #0xc, #0x14
    // 0x8d7080: sub             x4, x4, #0x3c
    // 0x8d7084: cmp             x4, #1
    // 0x8d7088: b.ls            #0x8d709c
    // 0x8d708c: r8 = int
    //     0x8d708c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8d7090: r3 = Null
    //     0x8d7090: add             x3, PP, #0x21, lsl #12  ; [pp+0x21bf0] Null
    //     0x8d7094: ldr             x3, [x3, #0xbf0]
    // 0x8d7098: r0 = int()
    //     0x8d7098: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8d709c: ldr             x2, [fp, #0x18]
    // 0x8d70a0: LoadField: r3 = r2->field_7
    //     0x8d70a0: ldur            w3, [x2, #7]
    // 0x8d70a4: DecompressPointer r3
    //     0x8d70a4: add             x3, x3, HEAP, lsl #32
    // 0x8d70a8: LoadField: r2 = r3->field_b
    //     0x8d70a8: ldur            w2, [x3, #0xb]
    // 0x8d70ac: ldr             x4, [fp, #0x10]
    // 0x8d70b0: r5 = LoadInt32Instr(r4)
    //     0x8d70b0: sbfx            x5, x4, #1, #0x1f
    //     0x8d70b4: tbz             w4, #0, #0x8d70bc
    //     0x8d70b8: ldur            x5, [x4, #7]
    // 0x8d70bc: r0 = LoadInt32Instr(r2)
    //     0x8d70bc: sbfx            x0, x2, #1, #0x1f
    // 0x8d70c0: mov             x1, x5
    // 0x8d70c4: cmp             x1, x0
    // 0x8d70c8: b.hs            #0x8d70ec
    // 0x8d70cc: LoadField: r1 = r3->field_f
    //     0x8d70cc: ldur            w1, [x3, #0xf]
    // 0x8d70d0: DecompressPointer r1
    //     0x8d70d0: add             x1, x1, HEAP, lsl #32
    // 0x8d70d4: ArrayLoad: r0 = r1[r5]  ; Unknown_4
    //     0x8d70d4: add             x16, x1, x5, lsl #2
    //     0x8d70d8: ldur            w0, [x16, #0xf]
    // 0x8d70dc: DecompressPointer r0
    //     0x8d70dc: add             x0, x0, HEAP, lsl #32
    // 0x8d70e0: LeaveFrame
    //     0x8d70e0: mov             SP, fp
    //     0x8d70e4: ldp             fp, lr, [SP], #0x10
    // 0x8d70e8: ret
    //     0x8d70e8: ret             
    // 0x8d70ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d70ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ fillRange(/* No info */) {
    // ** addr: 0x8d7208, size: 0xbc
    // 0x8d7208: EnterFrame
    //     0x8d7208: stp             fp, lr, [SP, #-0x10]!
    //     0x8d720c: mov             fp, SP
    // 0x8d7210: AllocStack(0x20)
    //     0x8d7210: sub             SP, SP, #0x20
    // 0x8d7214: SetupParameters(dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x8d7214: mov             x3, x2
    //     0x8d7218: stur            x2, [fp, #-0x18]
    // 0x8d721c: CheckStackOverflow
    //     0x8d721c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7220: cmp             SP, x16
    //     0x8d7224: b.ls            #0x8d72b0
    // 0x8d7228: LoadField: r4 = r1->field_7
    //     0x8d7228: ldur            w4, [x1, #7]
    // 0x8d722c: DecompressPointer r4
    //     0x8d722c: add             x4, x4, HEAP, lsl #32
    // 0x8d7230: stur            x4, [fp, #-0x10]
    // 0x8d7234: r5 = 0
    //     0x8d7234: movz            x5, #0
    // 0x8d7238: stur            x5, [fp, #-8]
    // 0x8d723c: CheckStackOverflow
    //     0x8d723c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7240: cmp             SP, x16
    //     0x8d7244: b.ls            #0x8d72b8
    // 0x8d7248: cmp             x5, x3
    // 0x8d724c: b.ge            #0x8d72a0
    // 0x8d7250: LoadField: r0 = r4->field_b
    //     0x8d7250: ldur            w0, [x4, #0xb]
    // 0x8d7254: r1 = LoadInt32Instr(r0)
    //     0x8d7254: sbfx            x1, x0, #1, #0x1f
    // 0x8d7258: mov             x0, x1
    // 0x8d725c: mov             x1, x5
    // 0x8d7260: cmp             x1, x0
    // 0x8d7264: b.hs            #0x8d72c0
    // 0x8d7268: LoadField: r0 = r4->field_f
    //     0x8d7268: ldur            w0, [x4, #0xf]
    // 0x8d726c: DecompressPointer r0
    //     0x8d726c: add             x0, x0, HEAP, lsl #32
    // 0x8d7270: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x8d7270: add             x16, x0, x5, lsl #2
    //     0x8d7274: ldur            w1, [x16, #0xf]
    // 0x8d7278: DecompressPointer r1
    //     0x8d7278: add             x1, x1, HEAP, lsl #32
    // 0x8d727c: str             NULL, [SP]
    // 0x8d7280: r2 = 0
    //     0x8d7280: movz            x2, #0
    // 0x8d7284: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8d7284: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8d7288: r0 = set()
    //     0x8d7288: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8d728c: ldur            x1, [fp, #-8]
    // 0x8d7290: add             x5, x1, #1
    // 0x8d7294: ldur            x3, [fp, #-0x18]
    // 0x8d7298: ldur            x4, [fp, #-0x10]
    // 0x8d729c: b               #0x8d7238
    // 0x8d72a0: r0 = Null
    //     0x8d72a0: mov             x0, NULL
    // 0x8d72a4: LeaveFrame
    //     0x8d72a4: mov             SP, fp
    //     0x8d72a8: ldp             fp, lr, [SP], #0x10
    // 0x8d72ac: ret
    //     0x8d72ac: ret             
    // 0x8d72b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d72b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d72b4: b               #0x8d7228
    // 0x8d72b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d72b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d72bc: b               #0x8d7248
    // 0x8d72c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d72c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ Register64List.from(/* No info */) {
    // ** addr: 0x8e495c, size: 0x1f4
    // 0x8e495c: EnterFrame
    //     0x8e495c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e4960: mov             fp, SP
    // 0x8e4964: AllocStack(0x48)
    //     0x8e4964: sub             SP, SP, #0x48
    // 0x8e4968: SetupParameters(Register64List this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x8e4968: mov             x3, x1
    //     0x8e496c: mov             x0, x2
    //     0x8e4970: stur            x1, [fp, #-8]
    //     0x8e4974: stur            x2, [fp, #-0x10]
    // 0x8e4978: CheckStackOverflow
    //     0x8e4978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e497c: cmp             SP, x16
    //     0x8e4980: b.ls            #0x8e4b34
    // 0x8e4984: LoadField: r1 = r0->field_b
    //     0x8e4984: ldur            w1, [x0, #0xb]
    // 0x8e4988: r2 = LoadInt32Instr(r1)
    //     0x8e4988: sbfx            x2, x1, #1, #0x1f
    // 0x8e498c: r1 = <Register64>
    //     0x8e498c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19418] TypeArguments: <Register64>
    //     0x8e4990: ldr             x1, [x1, #0x418]
    // 0x8e4994: r0 = _GrowableList()
    //     0x8e4994: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8e4998: mov             x2, x0
    // 0x8e499c: stur            x2, [fp, #-0x20]
    // 0x8e49a0: r4 = 0
    //     0x8e49a0: movz            x4, #0
    // 0x8e49a4: ldur            x3, [fp, #-0x10]
    // 0x8e49a8: stur            x4, [fp, #-0x18]
    // 0x8e49ac: CheckStackOverflow
    //     0x8e49ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e49b0: cmp             SP, x16
    //     0x8e49b4: b.ls            #0x8e4b3c
    // 0x8e49b8: LoadField: r0 = r2->field_b
    //     0x8e49b8: ldur            w0, [x2, #0xb]
    // 0x8e49bc: r1 = LoadInt32Instr(r0)
    //     0x8e49bc: sbfx            x1, x0, #1, #0x1f
    // 0x8e49c0: cmp             x4, x1
    // 0x8e49c4: b.ge            #0x8e4b00
    // 0x8e49c8: LoadField: r0 = r3->field_b
    //     0x8e49c8: ldur            w0, [x3, #0xb]
    // 0x8e49cc: r1 = LoadInt32Instr(r0)
    //     0x8e49cc: sbfx            x1, x0, #1, #0x1f
    // 0x8e49d0: mov             x0, x1
    // 0x8e49d4: mov             x1, x4
    // 0x8e49d8: cmp             x1, x0
    // 0x8e49dc: b.hs            #0x8e4b44
    // 0x8e49e0: LoadField: r0 = r3->field_f
    //     0x8e49e0: ldur            w0, [x3, #0xf]
    // 0x8e49e4: DecompressPointer r0
    //     0x8e49e4: add             x0, x0, HEAP, lsl #32
    // 0x8e49e8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x8e49e8: add             x16, x0, x4, lsl #2
    //     0x8e49ec: ldur            w1, [x16, #0xf]
    // 0x8e49f0: DecompressPointer r1
    //     0x8e49f0: add             x1, x1, HEAP, lsl #32
    // 0x8e49f4: r0 = LoadClassIdInstr(r1)
    //     0x8e49f4: ldur            x0, [x1, #-1]
    //     0x8e49f8: ubfx            x0, x0, #0xc, #0x14
    // 0x8e49fc: stp             xzr, x1, [SP]
    // 0x8e4a00: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8e4a00: movz            x17, #0x3037
    //     0x8e4a04: movk            x17, #0x1, lsl #16
    //     0x8e4a08: add             lr, x0, x17
    //     0x8e4a0c: ldr             lr, [x21, lr, lsl #3]
    //     0x8e4a10: blr             lr
    // 0x8e4a14: mov             x3, x0
    // 0x8e4a18: ldur            x2, [fp, #-0x10]
    // 0x8e4a1c: stur            x3, [fp, #-0x28]
    // 0x8e4a20: LoadField: r0 = r2->field_b
    //     0x8e4a20: ldur            w0, [x2, #0xb]
    // 0x8e4a24: r1 = LoadInt32Instr(r0)
    //     0x8e4a24: sbfx            x1, x0, #1, #0x1f
    // 0x8e4a28: mov             x0, x1
    // 0x8e4a2c: ldur            x1, [fp, #-0x18]
    // 0x8e4a30: cmp             x1, x0
    // 0x8e4a34: b.hs            #0x8e4b48
    // 0x8e4a38: LoadField: r0 = r2->field_f
    //     0x8e4a38: ldur            w0, [x2, #0xf]
    // 0x8e4a3c: DecompressPointer r0
    //     0x8e4a3c: add             x0, x0, HEAP, lsl #32
    // 0x8e4a40: ldur            x1, [fp, #-0x18]
    // 0x8e4a44: ArrayLoad: r4 = r0[r1]  ; Unknown_4
    //     0x8e4a44: add             x16, x0, x1, lsl #2
    //     0x8e4a48: ldur            w4, [x16, #0xf]
    // 0x8e4a4c: DecompressPointer r4
    //     0x8e4a4c: add             x4, x4, HEAP, lsl #32
    // 0x8e4a50: r0 = LoadClassIdInstr(r4)
    //     0x8e4a50: ldur            x0, [x4, #-1]
    //     0x8e4a54: ubfx            x0, x0, #0xc, #0x14
    // 0x8e4a58: r16 = 2
    //     0x8e4a58: movz            x16, #0x2
    // 0x8e4a5c: stp             x16, x4, [SP]
    // 0x8e4a60: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8e4a60: movz            x17, #0x3037
    //     0x8e4a64: movk            x17, #0x1, lsl #16
    //     0x8e4a68: add             lr, x0, x17
    //     0x8e4a6c: ldr             lr, [x21, lr, lsl #3]
    //     0x8e4a70: blr             lr
    // 0x8e4a74: stur            x0, [fp, #-0x30]
    // 0x8e4a78: r0 = Register64()
    //     0x8e4a78: bl              #0x8d60f8  ; AllocateRegister64Stub -> Register64 (size=0x10)
    // 0x8e4a7c: mov             x3, x0
    // 0x8e4a80: r0 = Sentinel
    //     0x8e4a80: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e4a84: stur            x3, [fp, #-0x38]
    // 0x8e4a88: StoreField: r3->field_7 = r0
    //     0x8e4a88: stur            w0, [x3, #7]
    // 0x8e4a8c: StoreField: r3->field_b = r0
    //     0x8e4a8c: stur            w0, [x3, #0xb]
    // 0x8e4a90: ldur            x16, [fp, #-0x30]
    // 0x8e4a94: str             x16, [SP]
    // 0x8e4a98: mov             x1, x3
    // 0x8e4a9c: ldur            x2, [fp, #-0x28]
    // 0x8e4aa0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8e4aa0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8e4aa4: r0 = set()
    //     0x8e4aa4: bl              #0x8d5f54  ; [package:pointycastle/src/ufixnum.dart] Register64::set
    // 0x8e4aa8: ldur            x2, [fp, #-0x20]
    // 0x8e4aac: LoadField: r3 = r2->field_b
    //     0x8e4aac: ldur            w3, [x2, #0xb]
    // 0x8e4ab0: r0 = LoadInt32Instr(r3)
    //     0x8e4ab0: sbfx            x0, x3, #1, #0x1f
    // 0x8e4ab4: ldur            x1, [fp, #-0x18]
    // 0x8e4ab8: cmp             x1, x0
    // 0x8e4abc: b.hs            #0x8e4b4c
    // 0x8e4ac0: LoadField: r1 = r2->field_f
    //     0x8e4ac0: ldur            w1, [x2, #0xf]
    // 0x8e4ac4: DecompressPointer r1
    //     0x8e4ac4: add             x1, x1, HEAP, lsl #32
    // 0x8e4ac8: ldur            x0, [fp, #-0x38]
    // 0x8e4acc: ldur            x3, [fp, #-0x18]
    // 0x8e4ad0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8e4ad0: add             x25, x1, x3, lsl #2
    //     0x8e4ad4: add             x25, x25, #0xf
    //     0x8e4ad8: str             w0, [x25]
    //     0x8e4adc: tbz             w0, #0, #0x8e4af8
    //     0x8e4ae0: ldurb           w16, [x1, #-1]
    //     0x8e4ae4: ldurb           w17, [x0, #-1]
    //     0x8e4ae8: and             x16, x17, x16, lsr #2
    //     0x8e4aec: tst             x16, HEAP, lsr #32
    //     0x8e4af0: b.eq            #0x8e4af8
    //     0x8e4af4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8e4af8: add             x4, x3, #1
    // 0x8e4afc: b               #0x8e49a4
    // 0x8e4b00: ldur            x1, [fp, #-8]
    // 0x8e4b04: mov             x0, x2
    // 0x8e4b08: StoreField: r1->field_7 = r0
    //     0x8e4b08: stur            w0, [x1, #7]
    //     0x8e4b0c: ldurb           w16, [x1, #-1]
    //     0x8e4b10: ldurb           w17, [x0, #-1]
    //     0x8e4b14: and             x16, x17, x16, lsr #2
    //     0x8e4b18: tst             x16, HEAP, lsr #32
    //     0x8e4b1c: b.eq            #0x8e4b24
    //     0x8e4b20: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e4b24: r0 = Null
    //     0x8e4b24: mov             x0, NULL
    // 0x8e4b28: LeaveFrame
    //     0x8e4b28: mov             SP, fp
    //     0x8e4b2c: ldp             fp, lr, [SP], #0x10
    // 0x8e4b30: ret
    //     0x8e4b30: ret             
    // 0x8e4b34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e4b34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e4b38: b               #0x8e4984
    // 0x8e4b3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e4b3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e4b40: b               #0x8e49b8
    // 0x8e4b44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4b44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4b48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4b48: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8e4b4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8e4b4c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3f624, size: 0x1d0
    // 0xc3f624: EnterFrame
    //     0xc3f624: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f628: mov             fp, SP
    // 0xc3f62c: AllocStack(0x30)
    //     0xc3f62c: sub             SP, SP, #0x30
    // 0xc3f630: CheckStackOverflow
    //     0xc3f630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f634: cmp             SP, x16
    //     0xc3f638: b.ls            #0xc3f7c8
    // 0xc3f63c: r0 = StringBuffer()
    //     0xc3f63c: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc3f640: stur            x0, [fp, #-8]
    // 0xc3f644: r16 = "("
    //     0xc3f644: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xc3f648: ldr             x16, [x16, #0xf08]
    // 0xc3f64c: str             x16, [SP]
    // 0xc3f650: mov             x1, x0
    // 0xc3f654: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xc3f654: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xc3f658: r0 = StringBuffer()
    //     0xc3f658: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc3f65c: ldr             x0, [fp, #0x10]
    // 0xc3f660: LoadField: r3 = r0->field_7
    //     0xc3f660: ldur            w3, [x0, #7]
    // 0xc3f664: DecompressPointer r3
    //     0xc3f664: add             x3, x3, HEAP, lsl #32
    // 0xc3f668: stur            x3, [fp, #-0x18]
    // 0xc3f66c: r0 = 0
    //     0xc3f66c: movz            x0, #0
    // 0xc3f670: stur            x0, [fp, #-0x10]
    // 0xc3f674: CheckStackOverflow
    //     0xc3f674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f678: cmp             SP, x16
    //     0xc3f67c: b.ls            #0xc3f7d0
    // 0xc3f680: LoadField: r1 = r3->field_b
    //     0xc3f680: ldur            w1, [x3, #0xb]
    // 0xc3f684: r2 = LoadInt32Instr(r1)
    //     0xc3f684: sbfx            x2, x1, #1, #0x1f
    // 0xc3f688: cmp             x0, x2
    // 0xc3f68c: b.ge            #0xc3f7a4
    // 0xc3f690: cmp             x0, #0
    // 0xc3f694: b.le            #0xc3f6a4
    // 0xc3f698: ldur            x1, [fp, #-8]
    // 0xc3f69c: r2 = ", "
    //     0xc3f69c: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3f6a0: r0 = _writeString()
    //     0xc3f6a0: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc3f6a4: ldur            x3, [fp, #-0x10]
    // 0xc3f6a8: ldur            x2, [fp, #-0x18]
    // 0xc3f6ac: LoadField: r0 = r2->field_b
    //     0xc3f6ac: ldur            w0, [x2, #0xb]
    // 0xc3f6b0: r1 = LoadInt32Instr(r0)
    //     0xc3f6b0: sbfx            x1, x0, #1, #0x1f
    // 0xc3f6b4: mov             x0, x1
    // 0xc3f6b8: mov             x1, x3
    // 0xc3f6bc: cmp             x1, x0
    // 0xc3f6c0: b.hs            #0xc3f7d8
    // 0xc3f6c4: LoadField: r0 = r2->field_f
    //     0xc3f6c4: ldur            w0, [x2, #0xf]
    // 0xc3f6c8: DecompressPointer r0
    //     0xc3f6c8: add             x0, x0, HEAP, lsl #32
    // 0xc3f6cc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xc3f6cc: add             x16, x0, x3, lsl #2
    //     0xc3f6d0: ldur            w1, [x16, #0xf]
    // 0xc3f6d4: DecompressPointer r1
    //     0xc3f6d4: add             x1, x1, HEAP, lsl #32
    // 0xc3f6d8: stur            x1, [fp, #-0x20]
    // 0xc3f6dc: r0 = StringBuffer()
    //     0xc3f6dc: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc3f6e0: mov             x1, x0
    // 0xc3f6e4: stur            x0, [fp, #-0x28]
    // 0xc3f6e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc3f6e8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc3f6ec: r0 = StringBuffer()
    //     0xc3f6ec: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc3f6f0: ldur            x0, [fp, #-0x20]
    // 0xc3f6f4: LoadField: r1 = r0->field_7
    //     0xc3f6f4: ldur            w1, [x0, #7]
    // 0xc3f6f8: DecompressPointer r1
    //     0xc3f6f8: add             x1, x1, HEAP, lsl #32
    // 0xc3f6fc: r16 = Sentinel
    //     0xc3f6fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc3f700: cmp             w1, w16
    // 0xc3f704: b.eq            #0xc3f7dc
    // 0xc3f708: r3 = LoadInt32Instr(r1)
    //     0xc3f708: sbfx            x3, x1, #1, #0x1f
    //     0xc3f70c: tbz             w1, #0, #0xc3f714
    //     0xc3f710: ldur            x3, [x1, #7]
    // 0xc3f714: mov             x1, x0
    // 0xc3f718: ldur            x2, [fp, #-0x28]
    // 0xc3f71c: r0 = _padWrite()
    //     0xc3f71c: bl              #0xc3f578  ; [package:pointycastle/src/ufixnum.dart] Register64::_padWrite
    // 0xc3f720: ldur            x1, [fp, #-0x20]
    // 0xc3f724: LoadField: r0 = r1->field_b
    //     0xc3f724: ldur            w0, [x1, #0xb]
    // 0xc3f728: DecompressPointer r0
    //     0xc3f728: add             x0, x0, HEAP, lsl #32
    // 0xc3f72c: r16 = Sentinel
    //     0xc3f72c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc3f730: cmp             w0, w16
    // 0xc3f734: b.eq            #0xc3f7e8
    // 0xc3f738: r3 = LoadInt32Instr(r0)
    //     0xc3f738: sbfx            x3, x0, #1, #0x1f
    //     0xc3f73c: tbz             w0, #0, #0xc3f744
    //     0xc3f740: ldur            x3, [x0, #7]
    // 0xc3f744: ldur            x2, [fp, #-0x28]
    // 0xc3f748: r0 = _padWrite()
    //     0xc3f748: bl              #0xc3f578  ; [package:pointycastle/src/ufixnum.dart] Register64::_padWrite
    // 0xc3f74c: ldur            x16, [fp, #-0x28]
    // 0xc3f750: str             x16, [SP]
    // 0xc3f754: r0 = toString()
    //     0xc3f754: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc3f758: r1 = LoadClassIdInstr(r0)
    //     0xc3f758: ldur            x1, [x0, #-1]
    //     0xc3f75c: ubfx            x1, x1, #0xc, #0x14
    // 0xc3f760: str             x0, [SP]
    // 0xc3f764: mov             x0, x1
    // 0xc3f768: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc3f768: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc3f76c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc3f76c: movz            x17, #0x2b03
    //     0xc3f770: add             lr, x0, x17
    //     0xc3f774: ldr             lr, [x21, lr, lsl #3]
    //     0xc3f778: blr             lr
    // 0xc3f77c: LoadField: r1 = r0->field_7
    //     0xc3f77c: ldur            w1, [x0, #7]
    // 0xc3f780: cbz             w1, #0xc3f790
    // 0xc3f784: ldur            x1, [fp, #-8]
    // 0xc3f788: mov             x2, x0
    // 0xc3f78c: r0 = _writeString()
    //     0xc3f78c: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc3f790: ldur            x0, [fp, #-0x10]
    // 0xc3f794: add             x1, x0, #1
    // 0xc3f798: mov             x0, x1
    // 0xc3f79c: ldur            x3, [fp, #-0x18]
    // 0xc3f7a0: b               #0xc3f670
    // 0xc3f7a4: ldur            x1, [fp, #-8]
    // 0xc3f7a8: r2 = ")"
    //     0xc3f7a8: ldr             x2, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3f7ac: r0 = write()
    //     0xc3f7ac: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc3f7b0: ldur            x16, [fp, #-8]
    // 0xc3f7b4: str             x16, [SP]
    // 0xc3f7b8: r0 = toString()
    //     0xc3f7b8: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc3f7bc: LeaveFrame
    //     0xc3f7bc: mov             SP, fp
    //     0xc3f7c0: ldp             fp, lr, [SP], #0x10
    // 0xc3f7c4: ret
    //     0xc3f7c4: ret             
    // 0xc3f7c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f7c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f7cc: b               #0xc3f63c
    // 0xc3f7d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f7d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f7d4: b               #0xc3f680
    // 0xc3f7d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc3f7d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc3f7dc: r9 = _hi32
    //     0xc3f7dc: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0xc3f7e0: ldr             x9, [x9, #0x3a0]
    // 0xc3f7e4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc3f7e4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc3f7e8: r9 = _lo32
    //     0xc3f7e8: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0xc3f7ec: ldr             x9, [x9, #0x3a8]
    // 0xc3f7f0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc3f7f0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 545, size: 0x10, field offset: 0x8
class Register64 extends Object {

  late int _hi32; // offset: 0x8
  late int _lo32; // offset: 0xc

  int dyn:get:_lo32(Register64) {
    // ** addr: 0x8d5e0c, size: 0x48
    // 0x8d5e0c: ldr             x1, [SP]
    // 0x8d5e10: LoadField: r0 = r1->field_b
    //     0x8d5e10: ldur            w0, [x1, #0xb]
    // 0x8d5e14: DecompressPointer r0
    //     0x8d5e14: add             x0, x0, HEAP, lsl #32
    // 0x8d5e18: r16 = Sentinel
    //     0x8d5e18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d5e1c: cmp             w0, w16
    // 0x8d5e20: b.eq            #0x8d5e28
    // 0x8d5e24: ret
    //     0x8d5e24: ret             
    // 0x8d5e28: EnterFrame
    //     0x8d5e28: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5e2c: mov             fp, SP
    // 0x8d5e30: r9 = _lo32
    //     0x8d5e30: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8d5e34: ldr             x9, [x9, #0x3a8]
    // 0x8d5e38: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8d5e38: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  int dyn:get:_hi32(Register64) {
    // ** addr: 0x8d5e54, size: 0x48
    // 0x8d5e54: ldr             x1, [SP]
    // 0x8d5e58: LoadField: r0 = r1->field_7
    //     0x8d5e58: ldur            w0, [x1, #7]
    // 0x8d5e5c: DecompressPointer r0
    //     0x8d5e5c: add             x0, x0, HEAP, lsl #32
    // 0x8d5e60: r16 = Sentinel
    //     0x8d5e60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d5e64: cmp             w0, w16
    // 0x8d5e68: b.eq            #0x8d5e70
    // 0x8d5e6c: ret
    //     0x8d5e6c: ret             
    // 0x8d5e70: EnterFrame
    //     0x8d5e70: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5e74: mov             fp, SP
    // 0x8d5e78: r9 = _hi32
    //     0x8d5e78: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8d5e7c: ldr             x9, [x9, #0x3a0]
    // 0x8d5e80: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8d5e80: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ set(/* No info */) {
    // ** addr: 0x8d5f54, size: 0x1a4
    // 0x8d5f54: EnterFrame
    //     0x8d5f54: stp             fp, lr, [SP, #-0x10]!
    //     0x8d5f58: mov             fp, SP
    // 0x8d5f5c: AllocStack(0x10)
    //     0x8d5f5c: sub             SP, SP, #0x10
    // 0x8d5f60: SetupParameters(Register64 this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, [dynamic _ = Null /* r1 */])
    //     0x8d5f60: mov             x5, x1
    //     0x8d5f64: mov             x3, x2
    //     0x8d5f68: stur            x1, [fp, #-8]
    //     0x8d5f6c: stur            x2, [fp, #-0x10]
    //     0x8d5f70: ldur            w0, [x4, #0x13]
    //     0x8d5f74: sub             x1, x0, #4
    //     0x8d5f78: cmp             w1, #2
    //     0x8d5f7c: b.lt            #0x8d5f90
    //     0x8d5f80: add             x0, fp, w1, sxtw #2
    //     0x8d5f84: ldr             x0, [x0, #8]
    //     0x8d5f88: mov             x1, x0
    //     0x8d5f8c: b               #0x8d5f94
    //     0x8d5f90: mov             x1, NULL
    // 0x8d5f94: cmp             w1, NULL
    // 0x8d5f98: b.ne            #0x8d6084
    // 0x8d5f9c: r0 = 60
    //     0x8d5f9c: movz            x0, #0x3c
    // 0x8d5fa0: branchIfSmi(r3, 0x8d5fac)
    //     0x8d5fa0: tbz             w3, #0, #0x8d5fac
    // 0x8d5fa4: r0 = LoadClassIdInstr(r3)
    //     0x8d5fa4: ldur            x0, [x3, #-1]
    //     0x8d5fa8: ubfx            x0, x0, #0xc, #0x14
    // 0x8d5fac: cmp             x0, #0x221
    // 0x8d5fb0: b.ne            #0x8d6020
    // 0x8d5fb4: LoadField: r0 = r3->field_7
    //     0x8d5fb4: ldur            w0, [x3, #7]
    // 0x8d5fb8: DecompressPointer r0
    //     0x8d5fb8: add             x0, x0, HEAP, lsl #32
    // 0x8d5fbc: r16 = Sentinel
    //     0x8d5fbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d5fc0: cmp             w0, w16
    // 0x8d5fc4: b.eq            #0x8d60e0
    // 0x8d5fc8: StoreField: r5->field_7 = r0
    //     0x8d5fc8: stur            w0, [x5, #7]
    //     0x8d5fcc: tbz             w0, #0, #0x8d5fe8
    //     0x8d5fd0: ldurb           w16, [x5, #-1]
    //     0x8d5fd4: ldurb           w17, [x0, #-1]
    //     0x8d5fd8: and             x16, x17, x16, lsr #2
    //     0x8d5fdc: tst             x16, HEAP, lsr #32
    //     0x8d5fe0: b.eq            #0x8d5fe8
    //     0x8d5fe4: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x8d5fe8: LoadField: r0 = r3->field_b
    //     0x8d5fe8: ldur            w0, [x3, #0xb]
    // 0x8d5fec: DecompressPointer r0
    //     0x8d5fec: add             x0, x0, HEAP, lsl #32
    // 0x8d5ff0: r16 = Sentinel
    //     0x8d5ff0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8d5ff4: cmp             w0, w16
    // 0x8d5ff8: b.eq            #0x8d60ec
    // 0x8d5ffc: StoreField: r5->field_b = r0
    //     0x8d5ffc: stur            w0, [x5, #0xb]
    //     0x8d6000: tbz             w0, #0, #0x8d601c
    //     0x8d6004: ldurb           w16, [x5, #-1]
    //     0x8d6008: ldurb           w17, [x0, #-1]
    //     0x8d600c: and             x16, x17, x16, lsr #2
    //     0x8d6010: tst             x16, HEAP, lsr #32
    //     0x8d6014: b.eq            #0x8d601c
    //     0x8d6018: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x8d601c: b               #0x8d60d0
    // 0x8d6020: StoreField: r5->field_7 = rZR
    //     0x8d6020: stur            wzr, [x5, #7]
    // 0x8d6024: r3 as int
    //     0x8d6024: mov             x0, x3
    //     0x8d6028: mov             x2, NULL
    //     0x8d602c: mov             x1, NULL
    //     0x8d6030: tbz             w0, #0, #0x8d6058
    //     0x8d6034: ldur            x4, [x0, #-1]
    //     0x8d6038: ubfx            x4, x4, #0xc, #0x14
    //     0x8d603c: sub             x4, x4, #0x3c
    //     0x8d6040: cmp             x4, #1
    //     0x8d6044: b.ls            #0x8d6058
    //     0x8d6048: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    //     0x8d604c: add             x3, PP, #0x19, lsl #12  ; [pp+0x19390] Null
    //     0x8d6050: ldr             x3, [x3, #0x390]
    //     0x8d6054: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8d6058: ldur            x0, [fp, #-0x10]
    // 0x8d605c: ldur            x2, [fp, #-8]
    // 0x8d6060: StoreField: r2->field_b = r0
    //     0x8d6060: stur            w0, [x2, #0xb]
    //     0x8d6064: tbz             w0, #0, #0x8d6080
    //     0x8d6068: ldurb           w16, [x2, #-1]
    //     0x8d606c: ldurb           w17, [x0, #-1]
    //     0x8d6070: and             x16, x17, x16, lsr #2
    //     0x8d6074: tst             x16, HEAP, lsr #32
    //     0x8d6078: b.eq            #0x8d6080
    //     0x8d607c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8d6080: b               #0x8d60d0
    // 0x8d6084: mov             x2, x5
    // 0x8d6088: ldur            x0, [fp, #-0x10]
    // 0x8d608c: StoreField: r2->field_7 = r0
    //     0x8d608c: stur            w0, [x2, #7]
    //     0x8d6090: tbz             w0, #0, #0x8d60ac
    //     0x8d6094: ldurb           w16, [x2, #-1]
    //     0x8d6098: ldurb           w17, [x0, #-1]
    //     0x8d609c: and             x16, x17, x16, lsr #2
    //     0x8d60a0: tst             x16, HEAP, lsr #32
    //     0x8d60a4: b.eq            #0x8d60ac
    //     0x8d60a8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8d60ac: mov             x0, x1
    // 0x8d60b0: StoreField: r2->field_b = r0
    //     0x8d60b0: stur            w0, [x2, #0xb]
    //     0x8d60b4: tbz             w0, #0, #0x8d60d0
    //     0x8d60b8: ldurb           w16, [x2, #-1]
    //     0x8d60bc: ldurb           w17, [x0, #-1]
    //     0x8d60c0: and             x16, x17, x16, lsr #2
    //     0x8d60c4: tst             x16, HEAP, lsr #32
    //     0x8d60c8: b.eq            #0x8d60d0
    //     0x8d60cc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8d60d0: r0 = Null
    //     0x8d60d0: mov             x0, NULL
    // 0x8d60d4: LeaveFrame
    //     0x8d60d4: mov             SP, fp
    //     0x8d60d8: ldp             fp, lr, [SP], #0x10
    // 0x8d60dc: ret
    //     0x8d60dc: ret             
    // 0x8d60e0: r9 = _hi32
    //     0x8d60e0: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8d60e4: ldr             x9, [x9, #0x3a0]
    // 0x8d60e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8d60e8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8d60ec: r9 = _lo32
    //     0x8d60ec: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8d60f0: ldr             x9, [x9, #0x3a8]
    // 0x8d60f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8d60f4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ sum(/* No info */) {
    // ** addr: 0x8dec78, size: 0x390
    // 0x8dec78: EnterFrame
    //     0x8dec78: stp             fp, lr, [SP, #-0x10]!
    //     0x8dec7c: mov             fp, SP
    // 0x8dec80: AllocStack(0x28)
    //     0x8dec80: sub             SP, SP, #0x28
    // 0x8dec84: SetupParameters(Register64 this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8dec84: stur            x1, [fp, #-0x10]
    //     0x8dec88: stur            x2, [fp, #-0x18]
    // 0x8dec8c: CheckStackOverflow
    //     0x8dec8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8dec90: cmp             SP, x16
    //     0x8dec94: b.ls            #0x8def84
    // 0x8dec98: r0 = 60
    //     0x8dec98: movz            x0, #0x3c
    // 0x8dec9c: branchIfSmi(r2, 0x8deca8)
    //     0x8dec9c: tbz             w2, #0, #0x8deca8
    // 0x8deca0: r0 = LoadClassIdInstr(r2)
    //     0x8deca0: ldur            x0, [x2, #-1]
    //     0x8deca4: ubfx            x0, x0, #0xc, #0x14
    // 0x8deca8: sub             x16, x0, #0x3c
    // 0x8decac: cmp             x16, #1
    // 0x8decb0: b.hi            #0x8deddc
    // 0x8decb4: r0 = LoadInt32Instr(r2)
    //     0x8decb4: sbfx            x0, x2, #1, #0x1f
    //     0x8decb8: tbz             w2, #0, #0x8decc0
    //     0x8decbc: ldur            x0, [x2, #7]
    // 0x8decc0: LoadField: r2 = r1->field_b
    //     0x8decc0: ldur            w2, [x1, #0xb]
    // 0x8decc4: DecompressPointer r2
    //     0x8decc4: add             x2, x2, HEAP, lsl #32
    // 0x8decc8: r16 = Sentinel
    //     0x8decc8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8deccc: cmp             w2, w16
    // 0x8decd0: b.eq            #0x8def8c
    // 0x8decd4: r3 = LoadInt32Instr(r2)
    //     0x8decd4: sbfx            x3, x2, #1, #0x1f
    //     0x8decd8: tbz             w2, #0, #0x8dece0
    //     0x8decdc: ldur            x3, [x2, #7]
    // 0x8dece0: ubfx            x0, x0, #0, #0x20
    // 0x8dece4: add             x2, x3, x0
    // 0x8dece8: mov             x3, x2
    // 0x8decec: ubfx            x3, x3, #0, #0x20
    // 0x8decf0: lsl             w0, w3, #1
    // 0x8decf4: tst             x3, #0xc0000000
    // 0x8decf8: b.eq            #0x8ded28
    // 0x8decfc: r0 = inline_Allocate_Mint()
    //     0x8decfc: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0x8ded00: add             x0, x0, #0x10
    //     0x8ded04: cmp             x4, x0
    //     0x8ded08: b.ls            #0x8def98
    //     0x8ded0c: str             x0, [THR, #0x50]  ; THR::top
    //     0x8ded10: sub             x0, x0, #0xf
    //     0x8ded14: movz            x4, #0xd15c
    //     0x8ded18: movk            x4, #0x3, lsl #16
    //     0x8ded1c: stur            x4, [x0, #-1]
    // 0x8ded20: ubfx            x4, x3, #0, #0x20
    // 0x8ded24: StoreField: r0->field_7 = r4
    //     0x8ded24: stur            x4, [x0, #7]
    // 0x8ded28: StoreField: r1->field_b = r0
    //     0x8ded28: stur            w0, [x1, #0xb]
    //     0x8ded2c: tbz             w0, #0, #0x8ded48
    //     0x8ded30: ldurb           w16, [x1, #-1]
    //     0x8ded34: ldurb           w17, [x0, #-1]
    //     0x8ded38: and             x16, x17, x16, lsr #2
    //     0x8ded3c: tst             x16, HEAP, lsr #32
    //     0x8ded40: b.eq            #0x8ded48
    //     0x8ded44: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8ded48: ubfx            x3, x3, #0, #0x20
    // 0x8ded4c: cmp             x2, x3
    // 0x8ded50: b.eq            #0x8def74
    // 0x8ded54: LoadField: r0 = r1->field_7
    //     0x8ded54: ldur            w0, [x1, #7]
    // 0x8ded58: DecompressPointer r0
    //     0x8ded58: add             x0, x0, HEAP, lsl #32
    // 0x8ded5c: r16 = Sentinel
    //     0x8ded5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ded60: cmp             w0, w16
    // 0x8ded64: b.eq            #0x8defb0
    // 0x8ded68: r2 = LoadInt32Instr(r0)
    //     0x8ded68: sbfx            x2, x0, #1, #0x1f
    //     0x8ded6c: tbz             w0, #0, #0x8ded74
    //     0x8ded70: ldur            x2, [x0, #7]
    // 0x8ded74: add             x0, x2, #1
    // 0x8ded78: ubfx            x0, x0, #0, #0x20
    // 0x8ded7c: lsl             w2, w0, #1
    // 0x8ded80: tst             x0, #0xc0000000
    // 0x8ded84: b.eq            #0x8dedb4
    // 0x8ded88: r2 = inline_Allocate_Mint()
    //     0x8ded88: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x8ded8c: add             x2, x2, #0x10
    //     0x8ded90: cmp             x3, x2
    //     0x8ded94: b.ls            #0x8defbc
    //     0x8ded98: str             x2, [THR, #0x50]  ; THR::top
    //     0x8ded9c: sub             x2, x2, #0xf
    //     0x8deda0: movz            x3, #0xd15c
    //     0x8deda4: movk            x3, #0x3, lsl #16
    //     0x8deda8: stur            x3, [x2, #-1]
    // 0x8dedac: ubfx            x3, x0, #0, #0x20
    // 0x8dedb0: StoreField: r2->field_7 = r3
    //     0x8dedb0: stur            x3, [x2, #7]
    // 0x8dedb4: mov             x0, x2
    // 0x8dedb8: StoreField: r1->field_7 = r0
    //     0x8dedb8: stur            w0, [x1, #7]
    //     0x8dedbc: tbz             w0, #0, #0x8dedd8
    //     0x8dedc0: ldurb           w16, [x1, #-1]
    //     0x8dedc4: ldurb           w17, [x0, #-1]
    //     0x8dedc8: and             x16, x17, x16, lsr #2
    //     0x8dedcc: tst             x16, HEAP, lsr #32
    //     0x8dedd0: b.eq            #0x8dedd8
    //     0x8dedd4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8dedd8: b               #0x8def74
    // 0x8deddc: LoadField: r0 = r1->field_b
    //     0x8deddc: ldur            w0, [x1, #0xb]
    // 0x8dede0: DecompressPointer r0
    //     0x8dede0: add             x0, x0, HEAP, lsl #32
    // 0x8dede4: r16 = Sentinel
    //     0x8dede4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8dede8: cmp             w0, w16
    // 0x8dedec: b.eq            #0x8defd0
    // 0x8dedf0: stur            x0, [fp, #-8]
    // 0x8dedf4: str             x2, [SP]
    // 0x8dedf8: r4 = 0
    //     0x8dedf8: movz            x4, #0
    // 0x8dedfc: ldr             x0, [SP]
    // 0x8dee00: r16 = UnlinkedCall_0x5f3c08
    //     0x8dee00: add             x16, PP, #0x19, lsl #12  ; [pp+0x194c0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8dee04: add             x16, x16, #0x4c0
    // 0x8dee08: ldp             x5, lr, [x16]
    // 0x8dee0c: blr             lr
    // 0x8dee10: mov             x1, x0
    // 0x8dee14: ldur            x0, [fp, #-8]
    // 0x8dee18: r2 = LoadInt32Instr(r0)
    //     0x8dee18: sbfx            x2, x0, #1, #0x1f
    //     0x8dee1c: tbz             w0, #0, #0x8dee24
    //     0x8dee20: ldur            x2, [x0, #7]
    // 0x8dee24: r0 = LoadInt32Instr(r1)
    //     0x8dee24: sbfx            x0, x1, #1, #0x1f
    //     0x8dee28: tbz             w1, #0, #0x8dee30
    //     0x8dee2c: ldur            x0, [x1, #7]
    // 0x8dee30: add             x1, x2, x0
    // 0x8dee34: mov             x2, x1
    // 0x8dee38: ubfx            x2, x2, #0, #0x20
    // 0x8dee3c: lsl             w0, w2, #1
    // 0x8dee40: tst             x2, #0xc0000000
    // 0x8dee44: b.eq            #0x8dee74
    // 0x8dee48: r0 = inline_Allocate_Mint()
    //     0x8dee48: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x8dee4c: add             x0, x0, #0x10
    //     0x8dee50: cmp             x3, x0
    //     0x8dee54: b.ls            #0x8defdc
    //     0x8dee58: str             x0, [THR, #0x50]  ; THR::top
    //     0x8dee5c: sub             x0, x0, #0xf
    //     0x8dee60: movz            x3, #0xd15c
    //     0x8dee64: movk            x3, #0x3, lsl #16
    //     0x8dee68: stur            x3, [x0, #-1]
    // 0x8dee6c: ubfx            x3, x2, #0, #0x20
    // 0x8dee70: StoreField: r0->field_7 = r3
    //     0x8dee70: stur            x3, [x0, #7]
    // 0x8dee74: ldur            x3, [fp, #-0x10]
    // 0x8dee78: StoreField: r3->field_b = r0
    //     0x8dee78: stur            w0, [x3, #0xb]
    //     0x8dee7c: tbz             w0, #0, #0x8dee98
    //     0x8dee80: ldurb           w16, [x3, #-1]
    //     0x8dee84: ldurb           w17, [x0, #-1]
    //     0x8dee88: and             x16, x17, x16, lsr #2
    //     0x8dee8c: tst             x16, HEAP, lsr #32
    //     0x8dee90: b.eq            #0x8dee98
    //     0x8dee94: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8dee98: ubfx            x2, x2, #0, #0x20
    // 0x8dee9c: cmp             x1, x2
    // 0x8deea0: b.eq            #0x8deeac
    // 0x8deea4: r0 = 1
    //     0x8deea4: movz            x0, #0x1
    // 0x8deea8: b               #0x8deeb0
    // 0x8deeac: r0 = 0
    //     0x8deeac: movz            x0, #0
    // 0x8deeb0: stur            x0, [fp, #-0x20]
    // 0x8deeb4: LoadField: r1 = r3->field_7
    //     0x8deeb4: ldur            w1, [x3, #7]
    // 0x8deeb8: DecompressPointer r1
    //     0x8deeb8: add             x1, x1, HEAP, lsl #32
    // 0x8deebc: r16 = Sentinel
    //     0x8deebc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8deec0: cmp             w1, w16
    // 0x8deec4: b.eq            #0x8defec
    // 0x8deec8: stur            x1, [fp, #-8]
    // 0x8deecc: ldur            x16, [fp, #-0x18]
    // 0x8deed0: str             x16, [SP]
    // 0x8deed4: r4 = 0
    //     0x8deed4: movz            x4, #0
    // 0x8deed8: ldr             x0, [SP]
    // 0x8deedc: r16 = UnlinkedCall_0x5f3c08
    //     0x8deedc: add             x16, PP, #0x19, lsl #12  ; [pp+0x194d0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8deee0: add             x16, x16, #0x4d0
    // 0x8deee4: ldp             x5, lr, [x16]
    // 0x8deee8: blr             lr
    // 0x8deeec: ldur            x1, [fp, #-8]
    // 0x8deef0: r2 = LoadInt32Instr(r1)
    //     0x8deef0: sbfx            x2, x1, #1, #0x1f
    //     0x8deef4: tbz             w1, #0, #0x8deefc
    //     0x8deef8: ldur            x2, [x1, #7]
    // 0x8deefc: r1 = LoadInt32Instr(r0)
    //     0x8deefc: sbfx            x1, x0, #1, #0x1f
    //     0x8def00: tbz             w0, #0, #0x8def08
    //     0x8def04: ldur            x1, [x0, #7]
    // 0x8def08: add             w3, w2, w1
    // 0x8def0c: ldur            x1, [fp, #-0x20]
    // 0x8def10: ubfx            x1, x1, #0, #0x20
    // 0x8def14: add             w2, w3, w1
    // 0x8def18: lsl             w0, w2, #1
    // 0x8def1c: tst             x2, #0xc0000000
    // 0x8def20: b.eq            #0x8def50
    // 0x8def24: r0 = inline_Allocate_Mint()
    //     0x8def24: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8def28: add             x0, x0, #0x10
    //     0x8def2c: cmp             x1, x0
    //     0x8def30: b.ls            #0x8deff8
    //     0x8def34: str             x0, [THR, #0x50]  ; THR::top
    //     0x8def38: sub             x0, x0, #0xf
    //     0x8def3c: movz            x1, #0xd15c
    //     0x8def40: movk            x1, #0x3, lsl #16
    //     0x8def44: stur            x1, [x0, #-1]
    // 0x8def48: ubfx            x1, x2, #0, #0x20
    // 0x8def4c: StoreField: r0->field_7 = r1
    //     0x8def4c: stur            x1, [x0, #7]
    // 0x8def50: ldur            x1, [fp, #-0x10]
    // 0x8def54: StoreField: r1->field_7 = r0
    //     0x8def54: stur            w0, [x1, #7]
    //     0x8def58: tbz             w0, #0, #0x8def74
    //     0x8def5c: ldurb           w16, [x1, #-1]
    //     0x8def60: ldurb           w17, [x0, #-1]
    //     0x8def64: and             x16, x17, x16, lsr #2
    //     0x8def68: tst             x16, HEAP, lsr #32
    //     0x8def6c: b.eq            #0x8def74
    //     0x8def70: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8def74: r0 = Null
    //     0x8def74: mov             x0, NULL
    // 0x8def78: LeaveFrame
    //     0x8def78: mov             SP, fp
    //     0x8def7c: ldp             fp, lr, [SP], #0x10
    // 0x8def80: ret
    //     0x8def80: ret             
    // 0x8def84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8def84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8def88: b               #0x8dec98
    // 0x8def8c: r9 = _lo32
    //     0x8def8c: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8def90: ldr             x9, [x9, #0x3a8]
    // 0x8def94: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8def94: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8def98: stp             x2, x3, [SP, #-0x10]!
    // 0x8def9c: SaveReg r1
    //     0x8def9c: str             x1, [SP, #-8]!
    // 0x8defa0: r0 = AllocateMint()
    //     0x8defa0: bl              #0xec22a8  ; AllocateMintStub
    // 0x8defa4: RestoreReg r1
    //     0x8defa4: ldr             x1, [SP], #8
    // 0x8defa8: ldp             x2, x3, [SP], #0x10
    // 0x8defac: b               #0x8ded20
    // 0x8defb0: r9 = _hi32
    //     0x8defb0: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8defb4: ldr             x9, [x9, #0x3a0]
    // 0x8defb8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8defb8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8defbc: stp             x0, x1, [SP, #-0x10]!
    // 0x8defc0: r0 = AllocateMint()
    //     0x8defc0: bl              #0xec22a8  ; AllocateMintStub
    // 0x8defc4: mov             x2, x0
    // 0x8defc8: ldp             x0, x1, [SP], #0x10
    // 0x8defcc: b               #0x8dedac
    // 0x8defd0: r9 = _lo32
    //     0x8defd0: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8defd4: ldr             x9, [x9, #0x3a8]
    // 0x8defd8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8defd8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8defdc: stp             x1, x2, [SP, #-0x10]!
    // 0x8defe0: r0 = AllocateMint()
    //     0x8defe0: bl              #0xec22a8  ; AllocateMintStub
    // 0x8defe4: ldp             x1, x2, [SP], #0x10
    // 0x8defe8: b               #0x8dee6c
    // 0x8defec: r9 = _hi32
    //     0x8defec: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8deff0: ldr             x9, [x9, #0x3a0]
    // 0x8deff4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8deff4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8deff8: SaveReg r2
    //     0x8deff8: str             x2, [SP, #-8]!
    // 0x8deffc: r0 = AllocateMint()
    //     0x8deffc: bl              #0xec22a8  ; AllocateMintStub
    // 0x8df000: RestoreReg r2
    //     0x8df000: ldr             x2, [SP], #8
    // 0x8df004: b               #0x8def48
  }
  _ rotl(/* No info */) {
    // ** addr: 0x8df208, size: 0x2f0
    // 0x8df208: EnterFrame
    //     0x8df208: stp             fp, lr, [SP, #-0x10]!
    //     0x8df20c: mov             fp, SP
    // 0x8df210: AllocStack(0x20)
    //     0x8df210: sub             SP, SP, #0x20
    // 0x8df214: r0 = 63
    //     0x8df214: movz            x0, #0x3f
    // 0x8df218: mov             x3, x1
    // 0x8df21c: stur            x1, [fp, #-0x18]
    // 0x8df220: CheckStackOverflow
    //     0x8df220: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8df224: cmp             SP, x16
    //     0x8df228: b.ls            #0x8df464
    // 0x8df22c: ubfx            x2, x2, #0, #0x20
    // 0x8df230: and             x1, x2, x0
    // 0x8df234: mov             x0, x1
    // 0x8df238: ubfx            x0, x0, #0, #0x20
    // 0x8df23c: cbz             x0, #0x8df454
    // 0x8df240: mov             x0, x1
    // 0x8df244: ubfx            x0, x0, #0, #0x20
    // 0x8df248: cmp             x0, #0x20
    // 0x8df24c: b.lt            #0x8df2d0
    // 0x8df250: LoadField: r2 = r3->field_7
    //     0x8df250: ldur            w2, [x3, #7]
    // 0x8df254: DecompressPointer r2
    //     0x8df254: add             x2, x2, HEAP, lsl #32
    // 0x8df258: r16 = Sentinel
    //     0x8df258: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df25c: cmp             w2, w16
    // 0x8df260: b.eq            #0x8df46c
    // 0x8df264: LoadField: r0 = r3->field_b
    //     0x8df264: ldur            w0, [x3, #0xb]
    // 0x8df268: DecompressPointer r0
    //     0x8df268: add             x0, x0, HEAP, lsl #32
    // 0x8df26c: r16 = Sentinel
    //     0x8df26c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df270: cmp             w0, w16
    // 0x8df274: b.eq            #0x8df478
    // 0x8df278: StoreField: r3->field_7 = r0
    //     0x8df278: stur            w0, [x3, #7]
    //     0x8df27c: tbz             w0, #0, #0x8df298
    //     0x8df280: ldurb           w16, [x3, #-1]
    //     0x8df284: ldurb           w17, [x0, #-1]
    //     0x8df288: and             x16, x17, x16, lsr #2
    //     0x8df28c: tst             x16, HEAP, lsr #32
    //     0x8df290: b.eq            #0x8df298
    //     0x8df294: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8df298: mov             x0, x2
    // 0x8df29c: StoreField: r3->field_b = r0
    //     0x8df29c: stur            w0, [x3, #0xb]
    //     0x8df2a0: tbz             w0, #0, #0x8df2bc
    //     0x8df2a4: ldurb           w16, [x3, #-1]
    //     0x8df2a8: ldurb           w17, [x0, #-1]
    //     0x8df2ac: and             x16, x17, x16, lsr #2
    //     0x8df2b0: tst             x16, HEAP, lsr #32
    //     0x8df2b4: b.eq            #0x8df2bc
    //     0x8df2b8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8df2bc: mov             x0, x1
    // 0x8df2c0: ubfx            x0, x0, #0, #0x20
    // 0x8df2c4: sub             x1, x0, #0x20
    // 0x8df2c8: mov             x0, x1
    // 0x8df2cc: b               #0x8df2d8
    // 0x8df2d0: ubfx            x1, x1, #0, #0x20
    // 0x8df2d4: mov             x0, x1
    // 0x8df2d8: stur            x0, [fp, #-0x10]
    // 0x8df2dc: cbz             x0, #0x8df454
    // 0x8df2e0: LoadField: r1 = r3->field_7
    //     0x8df2e0: ldur            w1, [x3, #7]
    // 0x8df2e4: DecompressPointer r1
    //     0x8df2e4: add             x1, x1, HEAP, lsl #32
    // 0x8df2e8: r16 = Sentinel
    //     0x8df2e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df2ec: cmp             w1, w16
    // 0x8df2f0: b.eq            #0x8df484
    // 0x8df2f4: r4 = LoadInt32Instr(r1)
    //     0x8df2f4: sbfx            x4, x1, #1, #0x1f
    //     0x8df2f8: tbz             w1, #0, #0x8df300
    //     0x8df2fc: ldur            x4, [x1, #7]
    // 0x8df300: mov             x1, x4
    // 0x8df304: mov             x2, x0
    // 0x8df308: stur            x4, [fp, #-8]
    // 0x8df30c: r0 = shiftl32()
    //     0x8df30c: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8df310: mov             x2, x0
    // 0x8df314: r0 = BoxInt64Instr(r2)
    //     0x8df314: sbfiz           x0, x2, #1, #0x1f
    //     0x8df318: cmp             x2, x0, asr #1
    //     0x8df31c: b.eq            #0x8df328
    //     0x8df320: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8df324: stur            x2, [x0, #7]
    // 0x8df328: ldur            x3, [fp, #-0x18]
    // 0x8df32c: StoreField: r3->field_7 = r0
    //     0x8df32c: stur            w0, [x3, #7]
    //     0x8df330: tbz             w0, #0, #0x8df34c
    //     0x8df334: ldurb           w16, [x3, #-1]
    //     0x8df338: ldurb           w17, [x0, #-1]
    //     0x8df33c: and             x16, x17, x16, lsr #2
    //     0x8df340: tst             x16, HEAP, lsr #32
    //     0x8df344: b.eq            #0x8df34c
    //     0x8df348: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8df34c: LoadField: r0 = r3->field_b
    //     0x8df34c: ldur            w0, [x3, #0xb]
    // 0x8df350: DecompressPointer r0
    //     0x8df350: add             x0, x0, HEAP, lsl #32
    // 0x8df354: r16 = Sentinel
    //     0x8df354: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df358: cmp             w0, w16
    // 0x8df35c: b.eq            #0x8df490
    // 0x8df360: ldur            x4, [fp, #-0x10]
    // 0x8df364: r1 = 32
    //     0x8df364: movz            x1, #0x20
    // 0x8df368: sub             x5, x1, x4
    // 0x8df36c: stur            x5, [fp, #-0x20]
    // 0x8df370: r6 = LoadInt32Instr(r0)
    //     0x8df370: sbfx            x6, x0, #1, #0x1f
    //     0x8df374: tbz             w0, #0, #0x8df37c
    //     0x8df378: ldur            x6, [x0, #7]
    // 0x8df37c: cmp             x5, #0x3f
    // 0x8df380: b.hi            #0x8df49c
    // 0x8df384: asr             x0, x6, x5
    // 0x8df388: orr             x7, x2, x0
    // 0x8df38c: r0 = BoxInt64Instr(r7)
    //     0x8df38c: sbfiz           x0, x7, #1, #0x1f
    //     0x8df390: cmp             x7, x0, asr #1
    //     0x8df394: b.eq            #0x8df3a0
    //     0x8df398: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8df39c: stur            x7, [x0, #7]
    // 0x8df3a0: StoreField: r3->field_7 = r0
    //     0x8df3a0: stur            w0, [x3, #7]
    //     0x8df3a4: tbz             w0, #0, #0x8df3c0
    //     0x8df3a8: ldurb           w16, [x3, #-1]
    //     0x8df3ac: ldurb           w17, [x0, #-1]
    //     0x8df3b0: and             x16, x17, x16, lsr #2
    //     0x8df3b4: tst             x16, HEAP, lsr #32
    //     0x8df3b8: b.eq            #0x8df3c0
    //     0x8df3bc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8df3c0: mov             x1, x6
    // 0x8df3c4: mov             x2, x4
    // 0x8df3c8: r0 = shiftl32()
    //     0x8df3c8: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8df3cc: mov             x2, x0
    // 0x8df3d0: r0 = BoxInt64Instr(r2)
    //     0x8df3d0: sbfiz           x0, x2, #1, #0x1f
    //     0x8df3d4: cmp             x2, x0, asr #1
    //     0x8df3d8: b.eq            #0x8df3e4
    //     0x8df3dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8df3e0: stur            x2, [x0, #7]
    // 0x8df3e4: ldur            x3, [fp, #-0x18]
    // 0x8df3e8: StoreField: r3->field_b = r0
    //     0x8df3e8: stur            w0, [x3, #0xb]
    //     0x8df3ec: tbz             w0, #0, #0x8df408
    //     0x8df3f0: ldurb           w16, [x3, #-1]
    //     0x8df3f4: ldurb           w17, [x0, #-1]
    //     0x8df3f8: and             x16, x17, x16, lsr #2
    //     0x8df3fc: tst             x16, HEAP, lsr #32
    //     0x8df400: b.eq            #0x8df408
    //     0x8df404: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8df408: ldur            x4, [fp, #-0x20]
    // 0x8df40c: ldur            x5, [fp, #-8]
    // 0x8df410: cmp             x4, #0x3f
    // 0x8df414: b.hi            #0x8df4cc
    // 0x8df418: asr             x6, x5, x4
    // 0x8df41c: orr             x4, x2, x6
    // 0x8df420: r0 = BoxInt64Instr(r4)
    //     0x8df420: sbfiz           x0, x4, #1, #0x1f
    //     0x8df424: cmp             x4, x0, asr #1
    //     0x8df428: b.eq            #0x8df434
    //     0x8df42c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8df430: stur            x4, [x0, #7]
    // 0x8df434: StoreField: r3->field_b = r0
    //     0x8df434: stur            w0, [x3, #0xb]
    //     0x8df438: tbz             w0, #0, #0x8df454
    //     0x8df43c: ldurb           w16, [x3, #-1]
    //     0x8df440: ldurb           w17, [x0, #-1]
    //     0x8df444: and             x16, x17, x16, lsr #2
    //     0x8df448: tst             x16, HEAP, lsr #32
    //     0x8df44c: b.eq            #0x8df454
    //     0x8df450: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8df454: r0 = Null
    //     0x8df454: mov             x0, NULL
    // 0x8df458: LeaveFrame
    //     0x8df458: mov             SP, fp
    //     0x8df45c: ldp             fp, lr, [SP], #0x10
    // 0x8df460: ret
    //     0x8df460: ret             
    // 0x8df464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8df464: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8df468: b               #0x8df22c
    // 0x8df46c: r9 = _hi32
    //     0x8df46c: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8df470: ldr             x9, [x9, #0x3a0]
    // 0x8df474: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df474: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8df478: r9 = _lo32
    //     0x8df478: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8df47c: ldr             x9, [x9, #0x3a8]
    // 0x8df480: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df480: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8df484: r9 = _hi32
    //     0x8df484: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8df488: ldr             x9, [x9, #0x3a0]
    // 0x8df48c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df48c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8df490: r9 = _lo32
    //     0x8df490: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8df494: ldr             x9, [x9, #0x3a8]
    // 0x8df498: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df498: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8df49c: tbnz            x5, #0x3f, #0x8df4a8
    // 0x8df4a0: asr             x0, x6, #0x3f
    // 0x8df4a4: b               #0x8df388
    // 0x8df4a8: str             x5, [THR, #0x7a8]  ; THR::
    // 0x8df4ac: stp             x5, x6, [SP, #-0x10]!
    // 0x8df4b0: stp             x3, x4, [SP, #-0x10]!
    // 0x8df4b4: SaveReg r2
    //     0x8df4b4: str             x2, [SP, #-8]!
    // 0x8df4b8: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x8df4bc: r4 = 0
    //     0x8df4bc: movz            x4, #0
    // 0x8df4c0: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x8df4c4: blr             lr
    // 0x8df4c8: brk             #0
    // 0x8df4cc: tbnz            x4, #0x3f, #0x8df4d8
    // 0x8df4d0: asr             x6, x5, #0x3f
    // 0x8df4d4: b               #0x8df41c
    // 0x8df4d8: str             x4, [THR, #0x7a8]  ; THR::
    // 0x8df4dc: stp             x4, x5, [SP, #-0x10]!
    // 0x8df4e0: stp             x2, x3, [SP, #-0x10]!
    // 0x8df4e4: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x8df4e8: r4 = 0
    //     0x8df4e8: movz            x4, #0
    // 0x8df4ec: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x8df4f0: blr             lr
    // 0x8df4f4: brk             #0
  }
  _ not(/* No info */) {
    // ** addr: 0x8df72c, size: 0x148
    // 0x8df72c: EnterFrame
    //     0x8df72c: stp             fp, lr, [SP, #-0x10]!
    //     0x8df730: mov             fp, SP
    // 0x8df734: LoadField: r2 = r1->field_7
    //     0x8df734: ldur            w2, [x1, #7]
    // 0x8df738: DecompressPointer r2
    //     0x8df738: add             x2, x2, HEAP, lsl #32
    // 0x8df73c: r16 = Sentinel
    //     0x8df73c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df740: cmp             w2, w16
    // 0x8df744: b.eq            #0x8df83c
    // 0x8df748: r3 = LoadInt32Instr(r2)
    //     0x8df748: sbfx            x3, x2, #1, #0x1f
    //     0x8df74c: tbz             w2, #0, #0x8df754
    //     0x8df750: ldur            x3, [x2, #7]
    // 0x8df754: mvn             w2, w3
    // 0x8df758: lsl             w0, w2, #1
    // 0x8df75c: tst             x2, #0xc0000000
    // 0x8df760: b.eq            #0x8df790
    // 0x8df764: r0 = inline_Allocate_Mint()
    //     0x8df764: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x8df768: add             x0, x0, #0x10
    //     0x8df76c: cmp             x3, x0
    //     0x8df770: b.ls            #0x8df848
    //     0x8df774: str             x0, [THR, #0x50]  ; THR::top
    //     0x8df778: sub             x0, x0, #0xf
    //     0x8df77c: movz            x3, #0xd15c
    //     0x8df780: movk            x3, #0x3, lsl #16
    //     0x8df784: stur            x3, [x0, #-1]
    // 0x8df788: ubfx            x3, x2, #0, #0x20
    // 0x8df78c: StoreField: r0->field_7 = r3
    //     0x8df78c: stur            x3, [x0, #7]
    // 0x8df790: StoreField: r1->field_7 = r0
    //     0x8df790: stur            w0, [x1, #7]
    //     0x8df794: tbz             w0, #0, #0x8df7b0
    //     0x8df798: ldurb           w16, [x1, #-1]
    //     0x8df79c: ldurb           w17, [x0, #-1]
    //     0x8df7a0: and             x16, x17, x16, lsr #2
    //     0x8df7a4: tst             x16, HEAP, lsr #32
    //     0x8df7a8: b.eq            #0x8df7b0
    //     0x8df7ac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8df7b0: LoadField: r2 = r1->field_b
    //     0x8df7b0: ldur            w2, [x1, #0xb]
    // 0x8df7b4: DecompressPointer r2
    //     0x8df7b4: add             x2, x2, HEAP, lsl #32
    // 0x8df7b8: r16 = Sentinel
    //     0x8df7b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df7bc: cmp             w2, w16
    // 0x8df7c0: b.eq            #0x8df858
    // 0x8df7c4: r3 = LoadInt32Instr(r2)
    //     0x8df7c4: sbfx            x3, x2, #1, #0x1f
    //     0x8df7c8: tbz             w2, #0, #0x8df7d0
    //     0x8df7cc: ldur            x3, [x2, #7]
    // 0x8df7d0: mvn             w2, w3
    // 0x8df7d4: lsl             w0, w2, #1
    // 0x8df7d8: tst             x2, #0xc0000000
    // 0x8df7dc: b.eq            #0x8df80c
    // 0x8df7e0: r0 = inline_Allocate_Mint()
    //     0x8df7e0: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x8df7e4: add             x0, x0, #0x10
    //     0x8df7e8: cmp             x3, x0
    //     0x8df7ec: b.ls            #0x8df864
    //     0x8df7f0: str             x0, [THR, #0x50]  ; THR::top
    //     0x8df7f4: sub             x0, x0, #0xf
    //     0x8df7f8: movz            x3, #0xd15c
    //     0x8df7fc: movk            x3, #0x3, lsl #16
    //     0x8df800: stur            x3, [x0, #-1]
    // 0x8df804: ubfx            x3, x2, #0, #0x20
    // 0x8df808: StoreField: r0->field_7 = r3
    //     0x8df808: stur            x3, [x0, #7]
    // 0x8df80c: StoreField: r1->field_b = r0
    //     0x8df80c: stur            w0, [x1, #0xb]
    //     0x8df810: tbz             w0, #0, #0x8df82c
    //     0x8df814: ldurb           w16, [x1, #-1]
    //     0x8df818: ldurb           w17, [x0, #-1]
    //     0x8df81c: and             x16, x17, x16, lsr #2
    //     0x8df820: tst             x16, HEAP, lsr #32
    //     0x8df824: b.eq            #0x8df82c
    //     0x8df828: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8df82c: r0 = Null
    //     0x8df82c: mov             x0, NULL
    // 0x8df830: LeaveFrame
    //     0x8df830: mov             SP, fp
    //     0x8df834: ldp             fp, lr, [SP], #0x10
    // 0x8df838: ret
    //     0x8df838: ret             
    // 0x8df83c: r9 = _hi32
    //     0x8df83c: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8df840: ldr             x9, [x9, #0x3a0]
    // 0x8df844: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df844: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8df848: stp             x1, x2, [SP, #-0x10]!
    // 0x8df84c: r0 = AllocateMint()
    //     0x8df84c: bl              #0xec22a8  ; AllocateMintStub
    // 0x8df850: ldp             x1, x2, [SP], #0x10
    // 0x8df854: b               #0x8df788
    // 0x8df858: r9 = _lo32
    //     0x8df858: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8df85c: ldr             x9, [x9, #0x3a8]
    // 0x8df860: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df860: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8df864: stp             x1, x2, [SP, #-0x10]!
    // 0x8df868: r0 = AllocateMint()
    //     0x8df868: bl              #0xec22a8  ; AllocateMintStub
    // 0x8df86c: ldp             x1, x2, [SP], #0x10
    // 0x8df870: b               #0x8df804
  }
  _ and(/* No info */) {
    // ** addr: 0x8df874, size: 0x13c
    // 0x8df874: EnterFrame
    //     0x8df874: stp             fp, lr, [SP, #-0x10]!
    //     0x8df878: mov             fp, SP
    // 0x8df87c: mov             x3, x1
    // 0x8df880: LoadField: r4 = r3->field_7
    //     0x8df880: ldur            w4, [x3, #7]
    // 0x8df884: DecompressPointer r4
    //     0x8df884: add             x4, x4, HEAP, lsl #32
    // 0x8df888: r16 = Sentinel
    //     0x8df888: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df88c: cmp             w4, w16
    // 0x8df890: b.eq            #0x8df980
    // 0x8df894: LoadField: r5 = r2->field_7
    //     0x8df894: ldur            w5, [x2, #7]
    // 0x8df898: DecompressPointer r5
    //     0x8df898: add             x5, x5, HEAP, lsl #32
    // 0x8df89c: r16 = Sentinel
    //     0x8df89c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df8a0: cmp             w5, w16
    // 0x8df8a4: b.eq            #0x8df98c
    // 0x8df8a8: r6 = LoadInt32Instr(r4)
    //     0x8df8a8: sbfx            x6, x4, #1, #0x1f
    //     0x8df8ac: tbz             w4, #0, #0x8df8b4
    //     0x8df8b0: ldur            x6, [x4, #7]
    // 0x8df8b4: r4 = LoadInt32Instr(r5)
    //     0x8df8b4: sbfx            x4, x5, #1, #0x1f
    //     0x8df8b8: tbz             w5, #0, #0x8df8c0
    //     0x8df8bc: ldur            x4, [x5, #7]
    // 0x8df8c0: and             x5, x6, x4
    // 0x8df8c4: r0 = BoxInt64Instr(r5)
    //     0x8df8c4: sbfiz           x0, x5, #1, #0x1f
    //     0x8df8c8: cmp             x5, x0, asr #1
    //     0x8df8cc: b.eq            #0x8df8d8
    //     0x8df8d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8df8d4: stur            x5, [x0, #7]
    // 0x8df8d8: StoreField: r3->field_7 = r0
    //     0x8df8d8: stur            w0, [x3, #7]
    //     0x8df8dc: tbz             w0, #0, #0x8df8f8
    //     0x8df8e0: ldurb           w16, [x3, #-1]
    //     0x8df8e4: ldurb           w17, [x0, #-1]
    //     0x8df8e8: and             x16, x17, x16, lsr #2
    //     0x8df8ec: tst             x16, HEAP, lsr #32
    //     0x8df8f0: b.eq            #0x8df8f8
    //     0x8df8f4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8df8f8: LoadField: r4 = r3->field_b
    //     0x8df8f8: ldur            w4, [x3, #0xb]
    // 0x8df8fc: DecompressPointer r4
    //     0x8df8fc: add             x4, x4, HEAP, lsl #32
    // 0x8df900: r16 = Sentinel
    //     0x8df900: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df904: cmp             w4, w16
    // 0x8df908: b.eq            #0x8df998
    // 0x8df90c: LoadField: r5 = r2->field_b
    //     0x8df90c: ldur            w5, [x2, #0xb]
    // 0x8df910: DecompressPointer r5
    //     0x8df910: add             x5, x5, HEAP, lsl #32
    // 0x8df914: r16 = Sentinel
    //     0x8df914: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8df918: cmp             w5, w16
    // 0x8df91c: b.eq            #0x8df9a4
    // 0x8df920: r2 = LoadInt32Instr(r4)
    //     0x8df920: sbfx            x2, x4, #1, #0x1f
    //     0x8df924: tbz             w4, #0, #0x8df92c
    //     0x8df928: ldur            x2, [x4, #7]
    // 0x8df92c: r4 = LoadInt32Instr(r5)
    //     0x8df92c: sbfx            x4, x5, #1, #0x1f
    //     0x8df930: tbz             w5, #0, #0x8df938
    //     0x8df934: ldur            x4, [x5, #7]
    // 0x8df938: and             x5, x2, x4
    // 0x8df93c: r0 = BoxInt64Instr(r5)
    //     0x8df93c: sbfiz           x0, x5, #1, #0x1f
    //     0x8df940: cmp             x5, x0, asr #1
    //     0x8df944: b.eq            #0x8df950
    //     0x8df948: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8df94c: stur            x5, [x0, #7]
    // 0x8df950: StoreField: r3->field_b = r0
    //     0x8df950: stur            w0, [x3, #0xb]
    //     0x8df954: tbz             w0, #0, #0x8df970
    //     0x8df958: ldurb           w16, [x3, #-1]
    //     0x8df95c: ldurb           w17, [x0, #-1]
    //     0x8df960: and             x16, x17, x16, lsr #2
    //     0x8df964: tst             x16, HEAP, lsr #32
    //     0x8df968: b.eq            #0x8df970
    //     0x8df96c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8df970: r0 = Null
    //     0x8df970: mov             x0, NULL
    // 0x8df974: LeaveFrame
    //     0x8df974: mov             SP, fp
    //     0x8df978: ldp             fp, lr, [SP], #0x10
    // 0x8df97c: ret
    //     0x8df97c: ret             
    // 0x8df980: r9 = _hi32
    //     0x8df980: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8df984: ldr             x9, [x9, #0x3a0]
    // 0x8df988: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df988: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8df98c: r9 = _hi32
    //     0x8df98c: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8df990: ldr             x9, [x9, #0x3a0]
    // 0x8df994: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df994: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8df998: r9 = _lo32
    //     0x8df998: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8df99c: ldr             x9, [x9, #0x3a8]
    // 0x8df9a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df9a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8df9a4: r9 = _lo32
    //     0x8df9a4: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8df9a8: ldr             x9, [x9, #0x3a8]
    // 0x8df9ac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8df9ac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ shiftr(/* No info */) {
    // ** addr: 0x8dfb98, size: 0x258
    // 0x8dfb98: EnterFrame
    //     0x8dfb98: stp             fp, lr, [SP, #-0x10]!
    //     0x8dfb9c: mov             fp, SP
    // 0x8dfba0: AllocStack(0x18)
    //     0x8dfba0: sub             SP, SP, #0x18
    // 0x8dfba4: r0 = 63
    //     0x8dfba4: movz            x0, #0x3f
    // 0x8dfba8: mov             x3, x1
    // 0x8dfbac: stur            x1, [fp, #-0x18]
    // 0x8dfbb0: CheckStackOverflow
    //     0x8dfbb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8dfbb4: cmp             SP, x16
    //     0x8dfbb8: b.ls            #0x8dfd98
    // 0x8dfbbc: ubfx            x2, x2, #0, #0x20
    // 0x8dfbc0: and             x4, x2, x0
    // 0x8dfbc4: stur            x4, [fp, #-0x10]
    // 0x8dfbc8: mov             x0, x4
    // 0x8dfbcc: ubfx            x0, x0, #0, #0x20
    // 0x8dfbd0: cbz             x0, #0x8dfd88
    // 0x8dfbd4: mov             x0, x4
    // 0x8dfbd8: ubfx            x0, x0, #0, #0x20
    // 0x8dfbdc: cmp             x0, #0x20
    // 0x8dfbe0: b.lt            #0x8dfc58
    // 0x8dfbe4: LoadField: r0 = r3->field_7
    //     0x8dfbe4: ldur            w0, [x3, #7]
    // 0x8dfbe8: DecompressPointer r0
    //     0x8dfbe8: add             x0, x0, HEAP, lsl #32
    // 0x8dfbec: r16 = Sentinel
    //     0x8dfbec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8dfbf0: cmp             w0, w16
    // 0x8dfbf4: b.eq            #0x8dfda0
    // 0x8dfbf8: mov             x1, x4
    // 0x8dfbfc: ubfx            x1, x1, #0, #0x20
    // 0x8dfc00: sub             x2, x1, #0x20
    // 0x8dfc04: r1 = LoadInt32Instr(r0)
    //     0x8dfc04: sbfx            x1, x0, #1, #0x1f
    //     0x8dfc08: tbz             w0, #0, #0x8dfc10
    //     0x8dfc0c: ldur            x1, [x0, #7]
    // 0x8dfc10: cmp             x2, #0x3f
    // 0x8dfc14: b.hi            #0x8dfdac
    // 0x8dfc18: asr             x4, x1, x2
    // 0x8dfc1c: r0 = BoxInt64Instr(r4)
    //     0x8dfc1c: sbfiz           x0, x4, #1, #0x1f
    //     0x8dfc20: cmp             x4, x0, asr #1
    //     0x8dfc24: b.eq            #0x8dfc30
    //     0x8dfc28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8dfc2c: stur            x4, [x0, #7]
    // 0x8dfc30: StoreField: r3->field_b = r0
    //     0x8dfc30: stur            w0, [x3, #0xb]
    //     0x8dfc34: tbz             w0, #0, #0x8dfc50
    //     0x8dfc38: ldurb           w16, [x3, #-1]
    //     0x8dfc3c: ldurb           w17, [x0, #-1]
    //     0x8dfc40: and             x16, x17, x16, lsr #2
    //     0x8dfc44: tst             x16, HEAP, lsr #32
    //     0x8dfc48: b.eq            #0x8dfc50
    //     0x8dfc4c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8dfc50: StoreField: r3->field_7 = rZR
    //     0x8dfc50: stur            wzr, [x3, #7]
    // 0x8dfc54: b               #0x8dfd88
    // 0x8dfc58: r2 = 32
    //     0x8dfc58: movz            x2, #0x20
    // 0x8dfc5c: LoadField: r0 = r3->field_b
    //     0x8dfc5c: ldur            w0, [x3, #0xb]
    // 0x8dfc60: DecompressPointer r0
    //     0x8dfc60: add             x0, x0, HEAP, lsl #32
    // 0x8dfc64: r16 = Sentinel
    //     0x8dfc64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8dfc68: cmp             w0, w16
    // 0x8dfc6c: b.eq            #0x8dfdd8
    // 0x8dfc70: r1 = LoadInt32Instr(r0)
    //     0x8dfc70: sbfx            x1, x0, #1, #0x1f
    //     0x8dfc74: tbz             w0, #0, #0x8dfc7c
    //     0x8dfc78: ldur            x1, [x0, #7]
    // 0x8dfc7c: mov             x0, x4
    // 0x8dfc80: ubfx            x0, x0, #0, #0x20
    // 0x8dfc84: asr             x5, x1, x0
    // 0x8dfc88: stur            x5, [fp, #-8]
    // 0x8dfc8c: r0 = BoxInt64Instr(r5)
    //     0x8dfc8c: sbfiz           x0, x5, #1, #0x1f
    //     0x8dfc90: cmp             x5, x0, asr #1
    //     0x8dfc94: b.eq            #0x8dfca0
    //     0x8dfc98: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8dfc9c: stur            x5, [x0, #7]
    // 0x8dfca0: StoreField: r3->field_b = r0
    //     0x8dfca0: stur            w0, [x3, #0xb]
    //     0x8dfca4: tbz             w0, #0, #0x8dfcc0
    //     0x8dfca8: ldurb           w16, [x3, #-1]
    //     0x8dfcac: ldurb           w17, [x0, #-1]
    //     0x8dfcb0: and             x16, x17, x16, lsr #2
    //     0x8dfcb4: tst             x16, HEAP, lsr #32
    //     0x8dfcb8: b.eq            #0x8dfcc0
    //     0x8dfcbc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8dfcc0: LoadField: r0 = r3->field_7
    //     0x8dfcc0: ldur            w0, [x3, #7]
    // 0x8dfcc4: DecompressPointer r0
    //     0x8dfcc4: add             x0, x0, HEAP, lsl #32
    // 0x8dfcc8: r16 = Sentinel
    //     0x8dfcc8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8dfccc: cmp             w0, w16
    // 0x8dfcd0: b.eq            #0x8dfde4
    // 0x8dfcd4: mov             x1, x4
    // 0x8dfcd8: ubfx            x1, x1, #0, #0x20
    // 0x8dfcdc: sub             x6, x2, x1
    // 0x8dfce0: r1 = LoadInt32Instr(r0)
    //     0x8dfce0: sbfx            x1, x0, #1, #0x1f
    //     0x8dfce4: tbz             w0, #0, #0x8dfcec
    //     0x8dfce8: ldur            x1, [x0, #7]
    // 0x8dfcec: mov             x2, x6
    // 0x8dfcf0: r0 = shiftl32()
    //     0x8dfcf0: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8dfcf4: ldur            x2, [fp, #-8]
    // 0x8dfcf8: orr             x3, x2, x0
    // 0x8dfcfc: r0 = BoxInt64Instr(r3)
    //     0x8dfcfc: sbfiz           x0, x3, #1, #0x1f
    //     0x8dfd00: cmp             x3, x0, asr #1
    //     0x8dfd04: b.eq            #0x8dfd10
    //     0x8dfd08: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8dfd0c: stur            x3, [x0, #7]
    // 0x8dfd10: ldur            x2, [fp, #-0x18]
    // 0x8dfd14: StoreField: r2->field_b = r0
    //     0x8dfd14: stur            w0, [x2, #0xb]
    //     0x8dfd18: tbz             w0, #0, #0x8dfd34
    //     0x8dfd1c: ldurb           w16, [x2, #-1]
    //     0x8dfd20: ldurb           w17, [x0, #-1]
    //     0x8dfd24: and             x16, x17, x16, lsr #2
    //     0x8dfd28: tst             x16, HEAP, lsr #32
    //     0x8dfd2c: b.eq            #0x8dfd34
    //     0x8dfd30: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8dfd34: LoadField: r3 = r2->field_7
    //     0x8dfd34: ldur            w3, [x2, #7]
    // 0x8dfd38: DecompressPointer r3
    //     0x8dfd38: add             x3, x3, HEAP, lsl #32
    // 0x8dfd3c: r4 = LoadInt32Instr(r3)
    //     0x8dfd3c: sbfx            x4, x3, #1, #0x1f
    //     0x8dfd40: tbz             w3, #0, #0x8dfd48
    //     0x8dfd44: ldur            x4, [x3, #7]
    // 0x8dfd48: ldur            x3, [fp, #-0x10]
    // 0x8dfd4c: ubfx            x3, x3, #0, #0x20
    // 0x8dfd50: asr             x5, x4, x3
    // 0x8dfd54: r0 = BoxInt64Instr(r5)
    //     0x8dfd54: sbfiz           x0, x5, #1, #0x1f
    //     0x8dfd58: cmp             x5, x0, asr #1
    //     0x8dfd5c: b.eq            #0x8dfd68
    //     0x8dfd60: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8dfd64: stur            x5, [x0, #7]
    // 0x8dfd68: StoreField: r2->field_7 = r0
    //     0x8dfd68: stur            w0, [x2, #7]
    //     0x8dfd6c: tbz             w0, #0, #0x8dfd88
    //     0x8dfd70: ldurb           w16, [x2, #-1]
    //     0x8dfd74: ldurb           w17, [x0, #-1]
    //     0x8dfd78: and             x16, x17, x16, lsr #2
    //     0x8dfd7c: tst             x16, HEAP, lsr #32
    //     0x8dfd80: b.eq            #0x8dfd88
    //     0x8dfd84: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8dfd88: r0 = Null
    //     0x8dfd88: mov             x0, NULL
    // 0x8dfd8c: LeaveFrame
    //     0x8dfd8c: mov             SP, fp
    //     0x8dfd90: ldp             fp, lr, [SP], #0x10
    // 0x8dfd94: ret
    //     0x8dfd94: ret             
    // 0x8dfd98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8dfd98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8dfd9c: b               #0x8dfbbc
    // 0x8dfda0: r9 = _hi32
    //     0x8dfda0: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8dfda4: ldr             x9, [x9, #0x3a0]
    // 0x8dfda8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8dfda8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8dfdac: tbnz            x2, #0x3f, #0x8dfdb8
    // 0x8dfdb0: asr             x4, x1, #0x3f
    // 0x8dfdb4: b               #0x8dfc1c
    // 0x8dfdb8: str             x2, [THR, #0x7a8]  ; THR::
    // 0x8dfdbc: stp             x2, x3, [SP, #-0x10]!
    // 0x8dfdc0: SaveReg r1
    //     0x8dfdc0: str             x1, [SP, #-8]!
    // 0x8dfdc4: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x8dfdc8: r4 = 0
    //     0x8dfdc8: movz            x4, #0
    // 0x8dfdcc: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x8dfdd0: blr             lr
    // 0x8dfdd4: brk             #0
    // 0x8dfdd8: r9 = _lo32
    //     0x8dfdd8: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8dfddc: ldr             x9, [x9, #0x3a8]
    // 0x8dfde0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8dfde0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8dfde4: r9 = _hi32
    //     0x8dfde4: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8dfde8: ldr             x9, [x9, #0x3a0]
    // 0x8dfdec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8dfdec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ shiftl(/* No info */) {
    // ** addr: 0x8e1fe0, size: 0x15c
    // 0x8e1fe0: EnterFrame
    //     0x8e1fe0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e1fe4: mov             fp, SP
    // 0x8e1fe8: AllocStack(0x8)
    //     0x8e1fe8: sub             SP, SP, #8
    // 0x8e1fec: SetupParameters(Register64 this /* r1 => r0, fp-0x8 */)
    //     0x8e1fec: mov             x0, x1
    //     0x8e1ff0: stur            x1, [fp, #-8]
    // 0x8e1ff4: CheckStackOverflow
    //     0x8e1ff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e1ff8: cmp             SP, x16
    //     0x8e1ffc: b.ls            #0x8e211c
    // 0x8e2000: LoadField: r1 = r0->field_7
    //     0x8e2000: ldur            w1, [x0, #7]
    // 0x8e2004: DecompressPointer r1
    //     0x8e2004: add             x1, x1, HEAP, lsl #32
    // 0x8e2008: r16 = Sentinel
    //     0x8e2008: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e200c: cmp             w1, w16
    // 0x8e2010: b.eq            #0x8e2124
    // 0x8e2014: r2 = LoadInt32Instr(r1)
    //     0x8e2014: sbfx            x2, x1, #1, #0x1f
    //     0x8e2018: tbz             w1, #0, #0x8e2020
    //     0x8e201c: ldur            x2, [x1, #7]
    // 0x8e2020: mov             x1, x2
    // 0x8e2024: r2 = 3
    //     0x8e2024: movz            x2, #0x3
    // 0x8e2028: r0 = shiftl32()
    //     0x8e2028: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e202c: mov             x2, x0
    // 0x8e2030: r0 = BoxInt64Instr(r2)
    //     0x8e2030: sbfiz           x0, x2, #1, #0x1f
    //     0x8e2034: cmp             x2, x0, asr #1
    //     0x8e2038: b.eq            #0x8e2044
    //     0x8e203c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e2040: stur            x2, [x0, #7]
    // 0x8e2044: ldur            x3, [fp, #-8]
    // 0x8e2048: StoreField: r3->field_7 = r0
    //     0x8e2048: stur            w0, [x3, #7]
    //     0x8e204c: tbz             w0, #0, #0x8e2068
    //     0x8e2050: ldurb           w16, [x3, #-1]
    //     0x8e2054: ldurb           w17, [x0, #-1]
    //     0x8e2058: and             x16, x17, x16, lsr #2
    //     0x8e205c: tst             x16, HEAP, lsr #32
    //     0x8e2060: b.eq            #0x8e2068
    //     0x8e2064: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8e2068: LoadField: r0 = r3->field_b
    //     0x8e2068: ldur            w0, [x3, #0xb]
    // 0x8e206c: DecompressPointer r0
    //     0x8e206c: add             x0, x0, HEAP, lsl #32
    // 0x8e2070: r16 = Sentinel
    //     0x8e2070: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e2074: cmp             w0, w16
    // 0x8e2078: b.eq            #0x8e2130
    // 0x8e207c: r4 = LoadInt32Instr(r0)
    //     0x8e207c: sbfx            x4, x0, #1, #0x1f
    //     0x8e2080: tbz             w0, #0, #0x8e2088
    //     0x8e2084: ldur            x4, [x0, #7]
    // 0x8e2088: asr             x0, x4, #0x1d
    // 0x8e208c: orr             x5, x2, x0
    // 0x8e2090: r0 = BoxInt64Instr(r5)
    //     0x8e2090: sbfiz           x0, x5, #1, #0x1f
    //     0x8e2094: cmp             x5, x0, asr #1
    //     0x8e2098: b.eq            #0x8e20a4
    //     0x8e209c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e20a0: stur            x5, [x0, #7]
    // 0x8e20a4: StoreField: r3->field_7 = r0
    //     0x8e20a4: stur            w0, [x3, #7]
    //     0x8e20a8: tbz             w0, #0, #0x8e20c4
    //     0x8e20ac: ldurb           w16, [x3, #-1]
    //     0x8e20b0: ldurb           w17, [x0, #-1]
    //     0x8e20b4: and             x16, x17, x16, lsr #2
    //     0x8e20b8: tst             x16, HEAP, lsr #32
    //     0x8e20bc: b.eq            #0x8e20c4
    //     0x8e20c0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8e20c4: mov             x1, x4
    // 0x8e20c8: r2 = 3
    //     0x8e20c8: movz            x2, #0x3
    // 0x8e20cc: r0 = shiftl32()
    //     0x8e20cc: bl              #0x8df4f8  ; [package:pointycastle/src/ufixnum.dart] ::shiftl32
    // 0x8e20d0: mov             x2, x0
    // 0x8e20d4: r0 = BoxInt64Instr(r2)
    //     0x8e20d4: sbfiz           x0, x2, #1, #0x1f
    //     0x8e20d8: cmp             x2, x0, asr #1
    //     0x8e20dc: b.eq            #0x8e20e8
    //     0x8e20e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e20e4: stur            x2, [x0, #7]
    // 0x8e20e8: ldur            x1, [fp, #-8]
    // 0x8e20ec: StoreField: r1->field_b = r0
    //     0x8e20ec: stur            w0, [x1, #0xb]
    //     0x8e20f0: tbz             w0, #0, #0x8e210c
    //     0x8e20f4: ldurb           w16, [x1, #-1]
    //     0x8e20f8: ldurb           w17, [x0, #-1]
    //     0x8e20fc: and             x16, x17, x16, lsr #2
    //     0x8e2100: tst             x16, HEAP, lsr #32
    //     0x8e2104: b.eq            #0x8e210c
    //     0x8e2108: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e210c: r0 = Null
    //     0x8e210c: mov             x0, NULL
    // 0x8e2110: LeaveFrame
    //     0x8e2110: mov             SP, fp
    //     0x8e2114: ldp             fp, lr, [SP], #0x10
    // 0x8e2118: ret
    //     0x8e2118: ret             
    // 0x8e211c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e211c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e2120: b               #0x8e2000
    // 0x8e2124: r9 = _hi32
    //     0x8e2124: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8e2128: ldr             x9, [x9, #0x3a0]
    // 0x8e212c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e212c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e2130: r9 = _lo32
    //     0x8e2130: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8e2134: ldr             x9, [x9, #0x3a8]
    // 0x8e2138: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e2138: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ >(/* No info */) {
    // ** addr: 0x8e2210, size: 0xf8
    // 0x8e2210: EnterFrame
    //     0x8e2210: stp             fp, lr, [SP, #-0x10]!
    //     0x8e2214: mov             fp, SP
    // 0x8e2218: LoadField: r3 = r1->field_7
    //     0x8e2218: ldur            w3, [x1, #7]
    // 0x8e221c: DecompressPointer r3
    //     0x8e221c: add             x3, x3, HEAP, lsl #32
    // 0x8e2220: r16 = Sentinel
    //     0x8e2220: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e2224: cmp             w3, w16
    // 0x8e2228: b.eq            #0x8e22d8
    // 0x8e222c: LoadField: r4 = r2->field_7
    //     0x8e222c: ldur            w4, [x2, #7]
    // 0x8e2230: DecompressPointer r4
    //     0x8e2230: add             x4, x4, HEAP, lsl #32
    // 0x8e2234: r16 = Sentinel
    //     0x8e2234: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e2238: cmp             w4, w16
    // 0x8e223c: b.eq            #0x8e22e4
    // 0x8e2240: r5 = LoadInt32Instr(r3)
    //     0x8e2240: sbfx            x5, x3, #1, #0x1f
    //     0x8e2244: tbz             w3, #0, #0x8e224c
    //     0x8e2248: ldur            x5, [x3, #7]
    // 0x8e224c: r3 = LoadInt32Instr(r4)
    //     0x8e224c: sbfx            x3, x4, #1, #0x1f
    //     0x8e2250: tbz             w4, #0, #0x8e2258
    //     0x8e2254: ldur            x3, [x4, #7]
    // 0x8e2258: cmp             x5, x3
    // 0x8e225c: b.le            #0x8e2268
    // 0x8e2260: r0 = true
    //     0x8e2260: add             x0, NULL, #0x20  ; true
    // 0x8e2264: b               #0x8e22cc
    // 0x8e2268: cmp             x5, x3
    // 0x8e226c: b.ne            #0x8e22c8
    // 0x8e2270: LoadField: r3 = r1->field_b
    //     0x8e2270: ldur            w3, [x1, #0xb]
    // 0x8e2274: DecompressPointer r3
    //     0x8e2274: add             x3, x3, HEAP, lsl #32
    // 0x8e2278: r16 = Sentinel
    //     0x8e2278: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e227c: cmp             w3, w16
    // 0x8e2280: b.eq            #0x8e22f0
    // 0x8e2284: LoadField: r1 = r2->field_b
    //     0x8e2284: ldur            w1, [x2, #0xb]
    // 0x8e2288: DecompressPointer r1
    //     0x8e2288: add             x1, x1, HEAP, lsl #32
    // 0x8e228c: r16 = Sentinel
    //     0x8e228c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e2290: cmp             w1, w16
    // 0x8e2294: b.eq            #0x8e22fc
    // 0x8e2298: r2 = LoadInt32Instr(r3)
    //     0x8e2298: sbfx            x2, x3, #1, #0x1f
    //     0x8e229c: tbz             w3, #0, #0x8e22a4
    //     0x8e22a0: ldur            x2, [x3, #7]
    // 0x8e22a4: r3 = LoadInt32Instr(r1)
    //     0x8e22a4: sbfx            x3, x1, #1, #0x1f
    //     0x8e22a8: tbz             w1, #0, #0x8e22b0
    //     0x8e22ac: ldur            x3, [x1, #7]
    // 0x8e22b0: cmp             x2, x3
    // 0x8e22b4: r16 = true
    //     0x8e22b4: add             x16, NULL, #0x20  ; true
    // 0x8e22b8: r17 = false
    //     0x8e22b8: add             x17, NULL, #0x30  ; false
    // 0x8e22bc: csel            x1, x16, x17, gt
    // 0x8e22c0: mov             x0, x1
    // 0x8e22c4: b               #0x8e22cc
    // 0x8e22c8: r0 = false
    //     0x8e22c8: add             x0, NULL, #0x30  ; false
    // 0x8e22cc: LeaveFrame
    //     0x8e22cc: mov             SP, fp
    //     0x8e22d0: ldp             fp, lr, [SP], #0x10
    // 0x8e22d4: ret
    //     0x8e22d4: ret             
    // 0x8e22d8: r9 = _hi32
    //     0x8e22d8: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8e22dc: ldr             x9, [x9, #0x3a0]
    // 0x8e22e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e22e0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e22e4: r9 = _hi32
    //     0x8e22e4: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8e22e8: ldr             x9, [x9, #0x3a0]
    // 0x8e22ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e22ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e22f0: r9 = _lo32
    //     0x8e22f0: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8e22f4: ldr             x9, [x9, #0x3a8]
    // 0x8e22f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e22f8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e22fc: r9 = _lo32
    //     0x8e22fc: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8e2300: ldr             x9, [x9, #0x3a8]
    // 0x8e2304: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e2304: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ unpack(/* No info */) {
    // ** addr: 0x8e24ac, size: 0xe0
    // 0x8e24ac: EnterFrame
    //     0x8e24ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8e24b0: mov             fp, SP
    // 0x8e24b4: AllocStack(0x10)
    //     0x8e24b4: sub             SP, SP, #0x10
    // 0x8e24b8: SetupParameters(Register64 this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x8e24b8: mov             x4, x1
    //     0x8e24bc: mov             x0, x2
    //     0x8e24c0: stur            x1, [fp, #-8]
    //     0x8e24c4: stur            x2, [fp, #-0x10]
    // 0x8e24c8: CheckStackOverflow
    //     0x8e24c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e24cc: cmp             SP, x16
    //     0x8e24d0: b.ls            #0x8e2584
    // 0x8e24d4: mov             x1, x0
    // 0x8e24d8: r2 = 0
    //     0x8e24d8: movz            x2, #0
    // 0x8e24dc: r3 = Instance_Endian
    //     0x8e24dc: add             x3, PP, #0x12, lsl #12  ; [pp+0x12390] Obj!Endian@e2cbb1
    //     0x8e24e0: ldr             x3, [x3, #0x390]
    // 0x8e24e4: r0 = unpack32()
    //     0x8e24e4: bl              #0x8e258c  ; [package:pointycastle/src/ufixnum.dart] ::unpack32
    // 0x8e24e8: mov             x2, x0
    // 0x8e24ec: r0 = BoxInt64Instr(r2)
    //     0x8e24ec: sbfiz           x0, x2, #1, #0x1f
    //     0x8e24f0: cmp             x2, x0, asr #1
    //     0x8e24f4: b.eq            #0x8e2500
    //     0x8e24f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e24fc: stur            x2, [x0, #7]
    // 0x8e2500: ldur            x4, [fp, #-8]
    // 0x8e2504: StoreField: r4->field_7 = r0
    //     0x8e2504: stur            w0, [x4, #7]
    //     0x8e2508: tbz             w0, #0, #0x8e2524
    //     0x8e250c: ldurb           w16, [x4, #-1]
    //     0x8e2510: ldurb           w17, [x0, #-1]
    //     0x8e2514: and             x16, x17, x16, lsr #2
    //     0x8e2518: tst             x16, HEAP, lsr #32
    //     0x8e251c: b.eq            #0x8e2524
    //     0x8e2520: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8e2524: ldur            x1, [fp, #-0x10]
    // 0x8e2528: r2 = 4
    //     0x8e2528: movz            x2, #0x4
    // 0x8e252c: r3 = Instance_Endian
    //     0x8e252c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12390] Obj!Endian@e2cbb1
    //     0x8e2530: ldr             x3, [x3, #0x390]
    // 0x8e2534: r0 = unpack32()
    //     0x8e2534: bl              #0x8e258c  ; [package:pointycastle/src/ufixnum.dart] ::unpack32
    // 0x8e2538: mov             x2, x0
    // 0x8e253c: r0 = BoxInt64Instr(r2)
    //     0x8e253c: sbfiz           x0, x2, #1, #0x1f
    //     0x8e2540: cmp             x2, x0, asr #1
    //     0x8e2544: b.eq            #0x8e2550
    //     0x8e2548: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e254c: stur            x2, [x0, #7]
    // 0x8e2550: ldur            x1, [fp, #-8]
    // 0x8e2554: StoreField: r1->field_b = r0
    //     0x8e2554: stur            w0, [x1, #0xb]
    //     0x8e2558: tbz             w0, #0, #0x8e2574
    //     0x8e255c: ldurb           w16, [x1, #-1]
    //     0x8e2560: ldurb           w17, [x0, #-1]
    //     0x8e2564: and             x16, x17, x16, lsr #2
    //     0x8e2568: tst             x16, HEAP, lsr #32
    //     0x8e256c: b.eq            #0x8e2574
    //     0x8e2570: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8e2574: r0 = Null
    //     0x8e2574: mov             x0, NULL
    // 0x8e2578: LeaveFrame
    //     0x8e2578: mov             SP, fp
    //     0x8e257c: ldp             fp, lr, [SP], #0x10
    // 0x8e2580: ret
    //     0x8e2580: ret             
    // 0x8e2584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e2584: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e2588: b               #0x8e24d4
  }
  _ xor(/* No info */) {
    // ** addr: 0x8e26bc, size: 0x13c
    // 0x8e26bc: EnterFrame
    //     0x8e26bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8e26c0: mov             fp, SP
    // 0x8e26c4: mov             x3, x1
    // 0x8e26c8: LoadField: r4 = r3->field_7
    //     0x8e26c8: ldur            w4, [x3, #7]
    // 0x8e26cc: DecompressPointer r4
    //     0x8e26cc: add             x4, x4, HEAP, lsl #32
    // 0x8e26d0: r16 = Sentinel
    //     0x8e26d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e26d4: cmp             w4, w16
    // 0x8e26d8: b.eq            #0x8e27c8
    // 0x8e26dc: LoadField: r5 = r2->field_7
    //     0x8e26dc: ldur            w5, [x2, #7]
    // 0x8e26e0: DecompressPointer r5
    //     0x8e26e0: add             x5, x5, HEAP, lsl #32
    // 0x8e26e4: r16 = Sentinel
    //     0x8e26e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e26e8: cmp             w5, w16
    // 0x8e26ec: b.eq            #0x8e27d4
    // 0x8e26f0: r6 = LoadInt32Instr(r4)
    //     0x8e26f0: sbfx            x6, x4, #1, #0x1f
    //     0x8e26f4: tbz             w4, #0, #0x8e26fc
    //     0x8e26f8: ldur            x6, [x4, #7]
    // 0x8e26fc: r4 = LoadInt32Instr(r5)
    //     0x8e26fc: sbfx            x4, x5, #1, #0x1f
    //     0x8e2700: tbz             w5, #0, #0x8e2708
    //     0x8e2704: ldur            x4, [x5, #7]
    // 0x8e2708: eor             x5, x6, x4
    // 0x8e270c: r0 = BoxInt64Instr(r5)
    //     0x8e270c: sbfiz           x0, x5, #1, #0x1f
    //     0x8e2710: cmp             x5, x0, asr #1
    //     0x8e2714: b.eq            #0x8e2720
    //     0x8e2718: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e271c: stur            x5, [x0, #7]
    // 0x8e2720: StoreField: r3->field_7 = r0
    //     0x8e2720: stur            w0, [x3, #7]
    //     0x8e2724: tbz             w0, #0, #0x8e2740
    //     0x8e2728: ldurb           w16, [x3, #-1]
    //     0x8e272c: ldurb           w17, [x0, #-1]
    //     0x8e2730: and             x16, x17, x16, lsr #2
    //     0x8e2734: tst             x16, HEAP, lsr #32
    //     0x8e2738: b.eq            #0x8e2740
    //     0x8e273c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8e2740: LoadField: r4 = r3->field_b
    //     0x8e2740: ldur            w4, [x3, #0xb]
    // 0x8e2744: DecompressPointer r4
    //     0x8e2744: add             x4, x4, HEAP, lsl #32
    // 0x8e2748: r16 = Sentinel
    //     0x8e2748: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e274c: cmp             w4, w16
    // 0x8e2750: b.eq            #0x8e27e0
    // 0x8e2754: LoadField: r5 = r2->field_b
    //     0x8e2754: ldur            w5, [x2, #0xb]
    // 0x8e2758: DecompressPointer r5
    //     0x8e2758: add             x5, x5, HEAP, lsl #32
    // 0x8e275c: r16 = Sentinel
    //     0x8e275c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e2760: cmp             w5, w16
    // 0x8e2764: b.eq            #0x8e27ec
    // 0x8e2768: r2 = LoadInt32Instr(r4)
    //     0x8e2768: sbfx            x2, x4, #1, #0x1f
    //     0x8e276c: tbz             w4, #0, #0x8e2774
    //     0x8e2770: ldur            x2, [x4, #7]
    // 0x8e2774: r4 = LoadInt32Instr(r5)
    //     0x8e2774: sbfx            x4, x5, #1, #0x1f
    //     0x8e2778: tbz             w5, #0, #0x8e2780
    //     0x8e277c: ldur            x4, [x5, #7]
    // 0x8e2780: eor             x5, x2, x4
    // 0x8e2784: r0 = BoxInt64Instr(r5)
    //     0x8e2784: sbfiz           x0, x5, #1, #0x1f
    //     0x8e2788: cmp             x5, x0, asr #1
    //     0x8e278c: b.eq            #0x8e2798
    //     0x8e2790: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e2794: stur            x5, [x0, #7]
    // 0x8e2798: StoreField: r3->field_b = r0
    //     0x8e2798: stur            w0, [x3, #0xb]
    //     0x8e279c: tbz             w0, #0, #0x8e27b8
    //     0x8e27a0: ldurb           w16, [x3, #-1]
    //     0x8e27a4: ldurb           w17, [x0, #-1]
    //     0x8e27a8: and             x16, x17, x16, lsr #2
    //     0x8e27ac: tst             x16, HEAP, lsr #32
    //     0x8e27b0: b.eq            #0x8e27b8
    //     0x8e27b4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8e27b8: r0 = Null
    //     0x8e27b8: mov             x0, NULL
    // 0x8e27bc: LeaveFrame
    //     0x8e27bc: mov             SP, fp
    //     0x8e27c0: ldp             fp, lr, [SP], #0x10
    // 0x8e27c4: ret
    //     0x8e27c4: ret             
    // 0x8e27c8: r9 = _hi32
    //     0x8e27c8: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8e27cc: ldr             x9, [x9, #0x3a0]
    // 0x8e27d0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e27d0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e27d4: r9 = _hi32
    //     0x8e27d4: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0x8e27d8: ldr             x9, [x9, #0x3a0]
    // 0x8e27dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e27dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e27e0: r9 = _lo32
    //     0x8e27e0: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8e27e4: ldr             x9, [x9, #0x3a8]
    // 0x8e27e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e27e8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8e27ec: r9 = _lo32
    //     0x8e27ec: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0x8e27f0: ldr             x9, [x9, #0x3a8]
    // 0x8e27f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8e27f4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  int hashCode(Register64) {
    // ** addr: 0xbf320c, size: 0x8c
    // 0xbf320c: EnterFrame
    //     0xbf320c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3210: mov             fp, SP
    // 0xbf3214: CheckStackOverflow
    //     0xbf3214: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3218: cmp             SP, x16
    //     0xbf321c: b.ls            #0xbf3278
    // 0xbf3220: ldr             x0, [fp, #0x10]
    // 0xbf3224: LoadField: r1 = r0->field_7
    //     0xbf3224: ldur            w1, [x0, #7]
    // 0xbf3228: DecompressPointer r1
    //     0xbf3228: add             x1, x1, HEAP, lsl #32
    // 0xbf322c: r16 = Sentinel
    //     0xbf322c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbf3230: cmp             w1, w16
    // 0xbf3234: b.eq            #0xbf3280
    // 0xbf3238: LoadField: r2 = r0->field_b
    //     0xbf3238: ldur            w2, [x0, #0xb]
    // 0xbf323c: DecompressPointer r2
    //     0xbf323c: add             x2, x2, HEAP, lsl #32
    // 0xbf3240: r16 = Sentinel
    //     0xbf3240: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbf3244: cmp             w2, w16
    // 0xbf3248: b.eq            #0xbf328c
    // 0xbf324c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf324c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf3250: r0 = hash()
    //     0xbf3250: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf3254: mov             x2, x0
    // 0xbf3258: r0 = BoxInt64Instr(r2)
    //     0xbf3258: sbfiz           x0, x2, #1, #0x1f
    //     0xbf325c: cmp             x2, x0, asr #1
    //     0xbf3260: b.eq            #0xbf326c
    //     0xbf3264: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3268: stur            x2, [x0, #7]
    // 0xbf326c: LeaveFrame
    //     0xbf326c: mov             SP, fp
    //     0xbf3270: ldp             fp, lr, [SP], #0x10
    // 0xbf3274: ret
    //     0xbf3274: ret             
    // 0xbf3278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3278: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf327c: b               #0xbf3220
    // 0xbf3280: r9 = _hi32
    //     0xbf3280: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0xbf3284: ldr             x9, [x9, #0x3a0]
    // 0xbf3288: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbf3288: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbf328c: r9 = _lo32
    //     0xbf328c: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0xbf3290: ldr             x9, [x9, #0x3a8]
    // 0xbf3294: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbf3294: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3f4b8, size: 0xc0
    // 0xc3f4b8: EnterFrame
    //     0xc3f4b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f4bc: mov             fp, SP
    // 0xc3f4c0: AllocStack(0x10)
    //     0xc3f4c0: sub             SP, SP, #0x10
    // 0xc3f4c4: CheckStackOverflow
    //     0xc3f4c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f4c8: cmp             SP, x16
    //     0xc3f4cc: b.ls            #0xc3f558
    // 0xc3f4d0: r0 = StringBuffer()
    //     0xc3f4d0: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc3f4d4: mov             x1, x0
    // 0xc3f4d8: stur            x0, [fp, #-8]
    // 0xc3f4dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc3f4dc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc3f4e0: r0 = StringBuffer()
    //     0xc3f4e0: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc3f4e4: ldr             x0, [fp, #0x10]
    // 0xc3f4e8: LoadField: r1 = r0->field_7
    //     0xc3f4e8: ldur            w1, [x0, #7]
    // 0xc3f4ec: DecompressPointer r1
    //     0xc3f4ec: add             x1, x1, HEAP, lsl #32
    // 0xc3f4f0: r16 = Sentinel
    //     0xc3f4f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc3f4f4: cmp             w1, w16
    // 0xc3f4f8: b.eq            #0xc3f560
    // 0xc3f4fc: r3 = LoadInt32Instr(r1)
    //     0xc3f4fc: sbfx            x3, x1, #1, #0x1f
    //     0xc3f500: tbz             w1, #0, #0xc3f508
    //     0xc3f504: ldur            x3, [x1, #7]
    // 0xc3f508: mov             x1, x0
    // 0xc3f50c: ldur            x2, [fp, #-8]
    // 0xc3f510: r0 = _padWrite()
    //     0xc3f510: bl              #0xc3f578  ; [package:pointycastle/src/ufixnum.dart] Register64::_padWrite
    // 0xc3f514: ldr             x1, [fp, #0x10]
    // 0xc3f518: LoadField: r0 = r1->field_b
    //     0xc3f518: ldur            w0, [x1, #0xb]
    // 0xc3f51c: DecompressPointer r0
    //     0xc3f51c: add             x0, x0, HEAP, lsl #32
    // 0xc3f520: r16 = Sentinel
    //     0xc3f520: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc3f524: cmp             w0, w16
    // 0xc3f528: b.eq            #0xc3f56c
    // 0xc3f52c: r3 = LoadInt32Instr(r0)
    //     0xc3f52c: sbfx            x3, x0, #1, #0x1f
    //     0xc3f530: tbz             w0, #0, #0xc3f538
    //     0xc3f534: ldur            x3, [x0, #7]
    // 0xc3f538: ldur            x2, [fp, #-8]
    // 0xc3f53c: r0 = _padWrite()
    //     0xc3f53c: bl              #0xc3f578  ; [package:pointycastle/src/ufixnum.dart] Register64::_padWrite
    // 0xc3f540: ldur            x16, [fp, #-8]
    // 0xc3f544: str             x16, [SP]
    // 0xc3f548: r0 = toString()
    //     0xc3f548: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc3f54c: LeaveFrame
    //     0xc3f54c: mov             SP, fp
    //     0xc3f550: ldp             fp, lr, [SP], #0x10
    // 0xc3f554: ret
    //     0xc3f554: ret             
    // 0xc3f558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f558: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f55c: b               #0xc3f4d0
    // 0xc3f560: r9 = _hi32
    //     0xc3f560: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0xc3f564: ldr             x9, [x9, #0x3a0]
    // 0xc3f568: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc3f568: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc3f56c: r9 = _lo32
    //     0xc3f56c: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0xc3f570: ldr             x9, [x9, #0x3a8]
    // 0xc3f574: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc3f574: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _padWrite(/* No info */) {
    // ** addr: 0xc3f578, size: 0xac
    // 0xc3f578: EnterFrame
    //     0xc3f578: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f57c: mov             fp, SP
    // 0xc3f580: AllocStack(0x18)
    //     0xc3f580: sub             SP, SP, #0x18
    // 0xc3f584: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xc3f584: stur            x2, [fp, #-8]
    // 0xc3f588: CheckStackOverflow
    //     0xc3f588: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f58c: cmp             SP, x16
    //     0xc3f590: b.ls            #0xc3f614
    // 0xc3f594: r0 = BoxInt64Instr(r3)
    //     0xc3f594: sbfiz           x0, x3, #1, #0x1f
    //     0xc3f598: cmp             x3, x0, asr #1
    //     0xc3f59c: b.eq            #0xc3f5a8
    //     0xc3f5a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3f5a4: stur            x3, [x0, #7]
    // 0xc3f5a8: mov             x1, x0
    // 0xc3f5ac: r0 = _toPow2String()
    //     0xc3f5ac: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0xc3f5b0: stur            x0, [fp, #-0x18]
    // 0xc3f5b4: LoadField: r1 = r0->field_7
    //     0xc3f5b4: ldur            w1, [x0, #7]
    // 0xc3f5b8: r2 = LoadInt32Instr(r1)
    //     0xc3f5b8: sbfx            x2, x1, #1, #0x1f
    // 0xc3f5bc: r1 = 8
    //     0xc3f5bc: movz            x1, #0x8
    // 0xc3f5c0: sub             x3, x1, x2
    // 0xc3f5c4: stur            x3, [fp, #-0x10]
    // 0xc3f5c8: CheckStackOverflow
    //     0xc3f5c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f5cc: cmp             SP, x16
    //     0xc3f5d0: b.ls            #0xc3f61c
    // 0xc3f5d4: cmp             x3, #0
    // 0xc3f5d8: b.le            #0xc3f5f8
    // 0xc3f5dc: ldur            x1, [fp, #-8]
    // 0xc3f5e0: r2 = "0"
    //     0xc3f5e0: ldr             x2, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0xc3f5e4: r0 = _writeString()
    //     0xc3f5e4: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc3f5e8: ldur            x0, [fp, #-0x10]
    // 0xc3f5ec: sub             x3, x0, #1
    // 0xc3f5f0: ldur            x0, [fp, #-0x18]
    // 0xc3f5f4: b               #0xc3f5c4
    // 0xc3f5f8: ldur            x1, [fp, #-8]
    // 0xc3f5fc: ldur            x2, [fp, #-0x18]
    // 0xc3f600: r0 = write()
    //     0xc3f600: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc3f604: r0 = Null
    //     0xc3f604: mov             x0, NULL
    // 0xc3f608: LeaveFrame
    //     0xc3f608: mov             SP, fp
    //     0xc3f60c: ldp             fp, lr, [SP], #0x10
    // 0xc3f610: ret
    //     0xc3f610: ret             
    // 0xc3f614: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f614: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f618: b               #0xc3f594
    // 0xc3f61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f61c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f620: b               #0xc3f5d4
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d5f0, size: 0x12c
    // 0xd7d5f0: EnterFrame
    //     0xd7d5f0: stp             fp, lr, [SP, #-0x10]!
    //     0xd7d5f4: mov             fp, SP
    // 0xd7d5f8: ldr             x1, [fp, #0x10]
    // 0xd7d5fc: cmp             w1, NULL
    // 0xd7d600: b.ne            #0xd7d614
    // 0xd7d604: r0 = false
    //     0xd7d604: add             x0, NULL, #0x30  ; false
    // 0xd7d608: LeaveFrame
    //     0xd7d608: mov             SP, fp
    //     0xd7d60c: ldp             fp, lr, [SP], #0x10
    // 0xd7d610: ret
    //     0xd7d610: ret             
    // 0xd7d614: r2 = 60
    //     0xd7d614: movz            x2, #0x3c
    // 0xd7d618: branchIfSmi(r1, 0xd7d624)
    //     0xd7d618: tbz             w1, #0, #0xd7d624
    // 0xd7d61c: r2 = LoadClassIdInstr(r1)
    //     0xd7d61c: ldur            x2, [x1, #-1]
    //     0xd7d620: ubfx            x2, x2, #0xc, #0x14
    // 0xd7d624: cmp             x2, #0x221
    // 0xd7d628: b.ne            #0xd7d6dc
    // 0xd7d62c: ldr             x2, [fp, #0x18]
    // 0xd7d630: LoadField: r3 = r2->field_7
    //     0xd7d630: ldur            w3, [x2, #7]
    // 0xd7d634: DecompressPointer r3
    //     0xd7d634: add             x3, x3, HEAP, lsl #32
    // 0xd7d638: r16 = Sentinel
    //     0xd7d638: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd7d63c: cmp             w3, w16
    // 0xd7d640: b.eq            #0xd7d6ec
    // 0xd7d644: LoadField: r4 = r1->field_7
    //     0xd7d644: ldur            w4, [x1, #7]
    // 0xd7d648: DecompressPointer r4
    //     0xd7d648: add             x4, x4, HEAP, lsl #32
    // 0xd7d64c: r16 = Sentinel
    //     0xd7d64c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd7d650: cmp             w4, w16
    // 0xd7d654: b.eq            #0xd7d6f8
    // 0xd7d658: r5 = LoadInt32Instr(r3)
    //     0xd7d658: sbfx            x5, x3, #1, #0x1f
    //     0xd7d65c: tbz             w3, #0, #0xd7d664
    //     0xd7d660: ldur            x5, [x3, #7]
    // 0xd7d664: r3 = LoadInt32Instr(r4)
    //     0xd7d664: sbfx            x3, x4, #1, #0x1f
    //     0xd7d668: tbz             w4, #0, #0xd7d670
    //     0xd7d66c: ldur            x3, [x4, #7]
    // 0xd7d670: cmp             x5, x3
    // 0xd7d674: b.ne            #0xd7d6d0
    // 0xd7d678: LoadField: r3 = r2->field_b
    //     0xd7d678: ldur            w3, [x2, #0xb]
    // 0xd7d67c: DecompressPointer r3
    //     0xd7d67c: add             x3, x3, HEAP, lsl #32
    // 0xd7d680: r16 = Sentinel
    //     0xd7d680: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd7d684: cmp             w3, w16
    // 0xd7d688: b.eq            #0xd7d704
    // 0xd7d68c: LoadField: r2 = r1->field_b
    //     0xd7d68c: ldur            w2, [x1, #0xb]
    // 0xd7d690: DecompressPointer r2
    //     0xd7d690: add             x2, x2, HEAP, lsl #32
    // 0xd7d694: r16 = Sentinel
    //     0xd7d694: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd7d698: cmp             w2, w16
    // 0xd7d69c: b.eq            #0xd7d710
    // 0xd7d6a0: r1 = LoadInt32Instr(r3)
    //     0xd7d6a0: sbfx            x1, x3, #1, #0x1f
    //     0xd7d6a4: tbz             w3, #0, #0xd7d6ac
    //     0xd7d6a8: ldur            x1, [x3, #7]
    // 0xd7d6ac: r3 = LoadInt32Instr(r2)
    //     0xd7d6ac: sbfx            x3, x2, #1, #0x1f
    //     0xd7d6b0: tbz             w2, #0, #0xd7d6b8
    //     0xd7d6b4: ldur            x3, [x2, #7]
    // 0xd7d6b8: cmp             x1, x3
    // 0xd7d6bc: r16 = true
    //     0xd7d6bc: add             x16, NULL, #0x20  ; true
    // 0xd7d6c0: r17 = false
    //     0xd7d6c0: add             x17, NULL, #0x30  ; false
    // 0xd7d6c4: csel            x2, x16, x17, eq
    // 0xd7d6c8: mov             x1, x2
    // 0xd7d6cc: b               #0xd7d6d4
    // 0xd7d6d0: r1 = false
    //     0xd7d6d0: add             x1, NULL, #0x30  ; false
    // 0xd7d6d4: mov             x0, x1
    // 0xd7d6d8: b               #0xd7d6e0
    // 0xd7d6dc: r0 = false
    //     0xd7d6dc: add             x0, NULL, #0x30  ; false
    // 0xd7d6e0: LeaveFrame
    //     0xd7d6e0: mov             SP, fp
    //     0xd7d6e4: ldp             fp, lr, [SP], #0x10
    // 0xd7d6e8: ret
    //     0xd7d6e8: ret             
    // 0xd7d6ec: r9 = _hi32
    //     0xd7d6ec: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0xd7d6f0: ldr             x9, [x9, #0x3a0]
    // 0xd7d6f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd7d6f4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd7d6f8: r9 = _hi32
    //     0xd7d6f8: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a0] Field <Register64._hi32@1011143242>: late (offset: 0x8)
    //     0xd7d6fc: ldr             x9, [x9, #0x3a0]
    // 0xd7d700: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd7d700: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd7d704: r9 = _lo32
    //     0xd7d704: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0xd7d708: ldr             x9, [x9, #0x3a8]
    // 0xd7d70c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd7d70c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd7d710: r9 = _lo32
    //     0xd7d710: add             x9, PP, #0x19, lsl #12  ; [pp+0x193a8] Field <Register64._lo32@1011143242>: late (offset: 0xc)
    //     0xd7d714: ldr             x9, [x9, #0x3a8]
    // 0xd7d718: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd7d718: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
