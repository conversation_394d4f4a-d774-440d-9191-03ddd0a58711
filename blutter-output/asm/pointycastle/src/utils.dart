// lib: , url: package:pointycastle/src/utils.dart

// class id: 1051049, size: 0x8
class :: {

  static late final BigInt negativeFlag; // offset: 0x1728
  static late BigInt _byteMask; // offset: 0x1724

  static _ decodeBigIntWithSign(/* No info */) {
    // ** addr: 0x8d0614, size: 0x17c
    // 0x8d0614: EnterFrame
    //     0x8d0614: stp             fp, lr, [SP, #-0x10]!
    //     0x8d0618: mov             fp, SP
    // 0x8d061c: AllocStack(0x30)
    //     0x8d061c: sub             SP, SP, #0x30
    // 0x8d0620: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */)
    //     0x8d0620: mov             x2, x1
    //     0x8d0624: stur            x1, [fp, #-0x10]
    // 0x8d0628: CheckStackOverflow
    //     0x8d0628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d062c: cmp             SP, x16
    //     0x8d0630: b.ls            #0x8d0778
    // 0x8d0634: LoadField: r0 = r2->field_13
    //     0x8d0634: ldur            w0, [x2, #0x13]
    // 0x8d0638: r1 = LoadInt32Instr(r0)
    //     0x8d0638: sbfx            x1, x0, #1, #0x1f
    // 0x8d063c: stur            x1, [fp, #-8]
    // 0x8d0640: cmp             x1, #1
    // 0x8d0644: b.ne            #0x8d0670
    // 0x8d0648: mov             x0, x1
    // 0x8d064c: r1 = 0
    //     0x8d064c: movz            x1, #0
    // 0x8d0650: cmp             x1, x0
    // 0x8d0654: b.hs            #0x8d0780
    // 0x8d0658: ArrayLoad: r0 = r2[0]  ; List_1
    //     0x8d0658: ldrb            w0, [x2, #0x17]
    // 0x8d065c: mov             x2, x0
    // 0x8d0660: r1 = Null
    //     0x8d0660: mov             x1, NULL
    // 0x8d0664: r0 = _BigIntImpl.from()
    //     0x8d0664: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8d0668: mov             x1, x0
    // 0x8d066c: b               #0x8d071c
    // 0x8d0670: r0 = InitLateStaticField(0x330) // [dart:core] _BigIntImpl::zero
    //     0x8d0670: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d0674: ldr             x0, [x0, #0x660]
    //     0x8d0678: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d067c: cmp             w0, w16
    //     0x8d0680: b.ne            #0x8d0690
    //     0x8d0684: add             x2, PP, #0x18, lsl #12  ; [pp+0x18818] Field <<EMAIL>>: static late final (offset: 0x330)
    //     0x8d0688: ldr             x2, [x2, #0x818]
    //     0x8d068c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d0690: mov             x6, x0
    // 0x8d0694: r5 = 0
    //     0x8d0694: movz            x5, #0
    // 0x8d0698: ldur            x3, [fp, #-0x10]
    // 0x8d069c: ldur            x4, [fp, #-8]
    // 0x8d06a0: stur            x6, [fp, #-0x18]
    // 0x8d06a4: stur            x5, [fp, #-0x20]
    // 0x8d06a8: CheckStackOverflow
    //     0x8d06a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d06ac: cmp             SP, x16
    //     0x8d06b0: b.ls            #0x8d0784
    // 0x8d06b4: cmp             x5, x4
    // 0x8d06b8: b.ge            #0x8d0718
    // 0x8d06bc: sub             x0, x4, x5
    // 0x8d06c0: sub             x2, x0, #1
    // 0x8d06c4: mov             x0, x4
    // 0x8d06c8: mov             x1, x2
    // 0x8d06cc: cmp             x1, x0
    // 0x8d06d0: b.hs            #0x8d078c
    // 0x8d06d4: ArrayLoad: r0 = r3[r2]  ; List_1
    //     0x8d06d4: add             x16, x3, x2
    //     0x8d06d8: ldrb            w0, [x16, #0x17]
    // 0x8d06dc: mov             x2, x0
    // 0x8d06e0: r1 = Null
    //     0x8d06e0: mov             x1, NULL
    // 0x8d06e4: r0 = _BigIntImpl.from()
    //     0x8d06e4: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8d06e8: mov             x1, x0
    // 0x8d06ec: ldur            x0, [fp, #-0x20]
    // 0x8d06f0: lsl             x2, x0, #3
    // 0x8d06f4: r0 = <<()
    //     0x8d06f4: bl              #0x8cc42c  ; [dart:core] _BigIntImpl::<<
    // 0x8d06f8: ldur            x1, [fp, #-0x18]
    // 0x8d06fc: mov             x2, x0
    // 0x8d0700: r0 = |()
    //     0x8d0700: bl              #0x8d080c  ; [dart:core] _BigIntImpl::|
    // 0x8d0704: mov             x1, x0
    // 0x8d0708: ldur            x0, [fp, #-0x20]
    // 0x8d070c: add             x5, x0, #1
    // 0x8d0710: mov             x6, x1
    // 0x8d0714: b               #0x8d0698
    // 0x8d0718: ldur            x1, [fp, #-0x18]
    // 0x8d071c: stur            x1, [fp, #-0x10]
    // 0x8d0720: r0 = InitLateStaticField(0x330) // [dart:core] _BigIntImpl::zero
    //     0x8d0720: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d0724: ldr             x0, [x0, #0x660]
    //     0x8d0728: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d072c: cmp             w0, w16
    //     0x8d0730: b.ne            #0x8d0740
    //     0x8d0734: add             x2, PP, #0x18, lsl #12  ; [pp+0x18818] Field <<EMAIL>>: static late final (offset: 0x330)
    //     0x8d0738: ldr             x2, [x2, #0x818]
    //     0x8d073c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d0740: ldur            x16, [fp, #-0x10]
    // 0x8d0744: stp             x0, x16, [SP]
    // 0x8d0748: r0 = ==()
    //     0x8d0748: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8d074c: tbz             w0, #4, #0x8d0768
    // 0x8d0750: ldur            x1, [fp, #-0x10]
    // 0x8d0754: r0 = bitLength()
    //     0x8d0754: bl              #0x8cc650  ; [dart:core] _BigIntImpl::bitLength
    // 0x8d0758: ldur            x1, [fp, #-0x10]
    // 0x8d075c: mov             x2, x0
    // 0x8d0760: r0 = toUnsigned()
    //     0x8d0760: bl              #0x8d0790  ; [dart:core] _BigIntImpl::toUnsigned
    // 0x8d0764: b               #0x8d076c
    // 0x8d0768: ldur            x0, [fp, #-0x10]
    // 0x8d076c: LeaveFrame
    //     0x8d076c: mov             SP, fp
    //     0x8d0770: ldp             fp, lr, [SP], #0x10
    // 0x8d0774: ret
    //     0x8d0774: ret             
    // 0x8d0778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0778: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d077c: b               #0x8d0634
    // 0x8d0780: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d0780: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8d0784: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0784: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0788: b               #0x8d06b4
    // 0x8d078c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d078c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ encodeBigInt(/* No info */) {
    // ** addr: 0x8d09c8, size: 0x25c
    // 0x8d09c8: EnterFrame
    //     0x8d09c8: stp             fp, lr, [SP, #-0x10]!
    //     0x8d09cc: mov             fp, SP
    // 0x8d09d0: AllocStack(0x50)
    //     0x8d09d0: sub             SP, SP, #0x50
    // 0x8d09d4: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8d09d4: stur            x1, [fp, #-8]
    // 0x8d09d8: CheckStackOverflow
    //     0x8d09d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d09dc: cmp             SP, x16
    //     0x8d09e0: b.ls            #0x8d0c10
    // 0x8d09e4: r0 = InitLateStaticField(0x330) // [dart:core] _BigIntImpl::zero
    //     0x8d09e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d09e8: ldr             x0, [x0, #0x660]
    //     0x8d09ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d09f0: cmp             w0, w16
    //     0x8d09f4: b.ne            #0x8d0a04
    //     0x8d09f8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18818] Field <<EMAIL>>: static late final (offset: 0x330)
    //     0x8d09fc: ldr             x2, [x2, #0x818]
    //     0x8d0a00: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d0a04: stur            x0, [fp, #-0x10]
    // 0x8d0a08: ldur            x16, [fp, #-8]
    // 0x8d0a0c: stp             x0, x16, [SP]
    // 0x8d0a10: r0 = ==()
    //     0x8d0a10: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8d0a14: tbnz            w0, #4, #0x8d0a80
    // 0x8d0a18: r0 = 2
    //     0x8d0a18: movz            x0, #0x2
    // 0x8d0a1c: mov             x2, x0
    // 0x8d0a20: r1 = Null
    //     0x8d0a20: mov             x1, NULL
    // 0x8d0a24: r0 = AllocateArray()
    //     0x8d0a24: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8d0a28: stur            x0, [fp, #-0x18]
    // 0x8d0a2c: StoreField: r0->field_f = rZR
    //     0x8d0a2c: stur            wzr, [x0, #0xf]
    // 0x8d0a30: r1 = <int>
    //     0x8d0a30: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8d0a34: r0 = AllocateGrowableArray()
    //     0x8d0a34: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8d0a38: mov             x1, x0
    // 0x8d0a3c: ldur            x0, [fp, #-0x18]
    // 0x8d0a40: stur            x1, [fp, #-0x20]
    // 0x8d0a44: StoreField: r1->field_f = r0
    //     0x8d0a44: stur            w0, [x1, #0xf]
    // 0x8d0a48: r4 = 2
    //     0x8d0a48: movz            x4, #0x2
    // 0x8d0a4c: StoreField: r1->field_b = r4
    //     0x8d0a4c: stur            w4, [x1, #0xb]
    // 0x8d0a50: r0 = AllocateUint8Array()
    //     0x8d0a50: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8d0a54: mov             x1, x0
    // 0x8d0a58: ldur            x5, [fp, #-0x20]
    // 0x8d0a5c: r2 = 0
    //     0x8d0a5c: movz            x2, #0
    // 0x8d0a60: r3 = 1
    //     0x8d0a60: movz            x3, #0x1
    // 0x8d0a64: r6 = 0
    //     0x8d0a64: movz            x6, #0
    // 0x8d0a68: stur            x0, [fp, #-0x18]
    // 0x8d0a6c: r0 = _slowSetRange()
    //     0x8d0a6c: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x8d0a70: ldur            x0, [fp, #-0x18]
    // 0x8d0a74: LeaveFrame
    //     0x8d0a74: mov             SP, fp
    //     0x8d0a78: ldp             fp, lr, [SP], #0x10
    // 0x8d0a7c: ret
    //     0x8d0a7c: ret             
    // 0x8d0a80: ldur            x1, [fp, #-8]
    // 0x8d0a84: ldur            x2, [fp, #-0x10]
    // 0x8d0a88: r0 = >()
    //     0x8d0a88: bl              #0x8d0c24  ; [dart:core] _BigIntImpl::>
    // 0x8d0a8c: tbnz            w0, #4, #0x8d0b0c
    // 0x8d0a90: ldur            x1, [fp, #-8]
    // 0x8d0a94: r0 = bitLength()
    //     0x8d0a94: bl              #0x8cc650  ; [dart:core] _BigIntImpl::bitLength
    // 0x8d0a98: add             x1, x0, #7
    // 0x8d0a9c: asr             x0, x1, #3
    // 0x8d0aa0: stur            x0, [fp, #-0x28]
    // 0x8d0aa4: sub             x1, x0, #1
    // 0x8d0aa8: lsl             x2, x1, #3
    // 0x8d0aac: ldur            x1, [fp, #-8]
    // 0x8d0ab0: r0 = >>()
    //     0x8d0ab0: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8d0ab4: stur            x0, [fp, #-0x10]
    // 0x8d0ab8: r0 = InitLateStaticField(0x1728) // [package:pointycastle/src/utils.dart] ::negativeFlag
    //     0x8d0ab8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d0abc: ldr             x0, [x0, #0x2e50]
    //     0x8d0ac0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d0ac4: cmp             w0, w16
    //     0x8d0ac8: b.ne            #0x8d0ad8
    //     0x8d0acc: add             x2, PP, #0x18, lsl #12  ; [pp+0x18998] Field <::.negativeFlag>: static late final (offset: 0x1728)
    //     0x8d0ad0: ldr             x2, [x2, #0x998]
    //     0x8d0ad4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8d0ad8: ldur            x1, [fp, #-0x10]
    // 0x8d0adc: mov             x2, x0
    // 0x8d0ae0: stur            x0, [fp, #-0x10]
    // 0x8d0ae4: r0 = &()
    //     0x8d0ae4: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8d0ae8: ldur            x16, [fp, #-0x10]
    // 0x8d0aec: stp             x16, x0, [SP]
    // 0x8d0af0: r0 = ==()
    //     0x8d0af0: bl              #0xd2bc88  ; [dart:core] _BigIntImpl::==
    // 0x8d0af4: tst             x0, #0x10
    // 0x8d0af8: cset            x1, eq
    // 0x8d0afc: lsl             x1, x1, #1
    // 0x8d0b00: r0 = LoadInt32Instr(r1)
    //     0x8d0b00: sbfx            x0, x1, #1, #0x1f
    // 0x8d0b04: ldur            x2, [fp, #-0x28]
    // 0x8d0b08: b               #0x8d0b24
    // 0x8d0b0c: ldur            x1, [fp, #-8]
    // 0x8d0b10: r0 = bitLength()
    //     0x8d0b10: bl              #0x8cc650  ; [dart:core] _BigIntImpl::bitLength
    // 0x8d0b14: add             x1, x0, #8
    // 0x8d0b18: asr             x0, x1, #3
    // 0x8d0b1c: mov             x2, x0
    // 0x8d0b20: r0 = 0
    //     0x8d0b20: movz            x0, #0
    // 0x8d0b24: stur            x2, [fp, #-0x30]
    // 0x8d0b28: add             x3, x2, x0
    // 0x8d0b2c: stur            x3, [fp, #-0x28]
    // 0x8d0b30: r0 = BoxInt64Instr(r3)
    //     0x8d0b30: sbfiz           x0, x3, #1, #0x1f
    //     0x8d0b34: cmp             x3, x0, asr #1
    //     0x8d0b38: b.eq            #0x8d0b44
    //     0x8d0b3c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d0b40: stur            x3, [x0, #7]
    // 0x8d0b44: mov             x4, x0
    // 0x8d0b48: r0 = AllocateUint8Array()
    //     0x8d0b48: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8d0b4c: stur            x0, [fp, #-0x10]
    // 0x8d0b50: ldur            x4, [fp, #-8]
    // 0x8d0b54: r3 = 0
    //     0x8d0b54: movz            x3, #0
    // 0x8d0b58: ldur            x1, [fp, #-0x30]
    // 0x8d0b5c: ldur            x2, [fp, #-0x28]
    // 0x8d0b60: stur            x4, [fp, #-8]
    // 0x8d0b64: stur            x3, [fp, #-0x40]
    // 0x8d0b68: CheckStackOverflow
    //     0x8d0b68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d0b6c: cmp             SP, x16
    //     0x8d0b70: b.ls            #0x8d0c18
    // 0x8d0b74: cmp             x3, x1
    // 0x8d0b78: b.ge            #0x8d0c00
    // 0x8d0b7c: sub             x5, x2, x3
    // 0x8d0b80: sub             x6, x5, #1
    // 0x8d0b84: stur            x6, [fp, #-0x38]
    // 0x8d0b88: r0 = InitLateStaticField(0x1724) // [package:pointycastle/src/utils.dart] ::_byteMask
    //     0x8d0b88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8d0b8c: ldr             x0, [x0, #0x2e48]
    //     0x8d0b90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8d0b94: cmp             w0, w16
    //     0x8d0b98: b.ne            #0x8d0ba8
    //     0x8d0b9c: add             x2, PP, #0x18, lsl #12  ; [pp+0x189a0] Field <::._byteMask@2656469613>: static late (offset: 0x1724)
    //     0x8d0ba0: ldr             x2, [x2, #0x9a0]
    //     0x8d0ba4: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8d0ba8: ldur            x1, [fp, #-8]
    // 0x8d0bac: mov             x2, x0
    // 0x8d0bb0: r0 = &()
    //     0x8d0bb0: bl              #0x665464  ; [dart:core] _BigIntImpl::&
    // 0x8d0bb4: mov             x1, x0
    // 0x8d0bb8: r0 = toInt()
    //     0x8d0bb8: bl              #0x665290  ; [dart:core] _BigIntImpl::toInt
    // 0x8d0bbc: mov             x2, x0
    // 0x8d0bc0: ldur            x0, [fp, #-0x28]
    // 0x8d0bc4: ldur            x1, [fp, #-0x38]
    // 0x8d0bc8: cmp             x1, x0
    // 0x8d0bcc: b.hs            #0x8d0c20
    // 0x8d0bd0: ldur            x1, [fp, #-0x38]
    // 0x8d0bd4: ldur            x0, [fp, #-0x10]
    // 0x8d0bd8: ArrayStore: r0[r1] = r2  ; TypeUnknown_1
    //     0x8d0bd8: add             x3, x0, x1
    //     0x8d0bdc: strb            w2, [x3, #0x17]
    // 0x8d0be0: ldur            x1, [fp, #-8]
    // 0x8d0be4: r2 = 8
    //     0x8d0be4: movz            x2, #0x8
    // 0x8d0be8: r0 = >>()
    //     0x8d0be8: bl              #0x5f72e4  ; [dart:core] _BigIntImpl::>>
    // 0x8d0bec: ldur            x1, [fp, #-0x40]
    // 0x8d0bf0: add             x3, x1, #1
    // 0x8d0bf4: mov             x4, x0
    // 0x8d0bf8: ldur            x0, [fp, #-0x10]
    // 0x8d0bfc: b               #0x8d0b58
    // 0x8d0c00: ldur            x0, [fp, #-0x10]
    // 0x8d0c04: LeaveFrame
    //     0x8d0c04: mov             SP, fp
    //     0x8d0c08: ldp             fp, lr, [SP], #0x10
    // 0x8d0c0c: ret
    //     0x8d0c0c: ret             
    // 0x8d0c10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0c10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0c14: b               #0x8d09e4
    // 0x8d0c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0c1c: b               #0x8d0b74
    // 0x8d0c20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d0c20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static BigInt _byteMask() {
    // ** addr: 0x8d0c64, size: 0x34
    // 0x8d0c64: EnterFrame
    //     0x8d0c64: stp             fp, lr, [SP, #-0x10]!
    //     0x8d0c68: mov             fp, SP
    // 0x8d0c6c: CheckStackOverflow
    //     0x8d0c6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d0c70: cmp             SP, x16
    //     0x8d0c74: b.ls            #0x8d0c90
    // 0x8d0c78: r1 = Null
    //     0x8d0c78: mov             x1, NULL
    // 0x8d0c7c: r2 = 255
    //     0x8d0c7c: movz            x2, #0xff
    // 0x8d0c80: r0 = _BigIntImpl.from()
    //     0x8d0c80: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8d0c84: LeaveFrame
    //     0x8d0c84: mov             SP, fp
    //     0x8d0c88: ldp             fp, lr, [SP], #0x10
    // 0x8d0c8c: ret
    //     0x8d0c8c: ret             
    // 0x8d0c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0c90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0c94: b               #0x8d0c78
  }
  static BigInt negativeFlag() {
    // ** addr: 0x8d0c98, size: 0x34
    // 0x8d0c98: EnterFrame
    //     0x8d0c98: stp             fp, lr, [SP, #-0x10]!
    //     0x8d0c9c: mov             fp, SP
    // 0x8d0ca0: CheckStackOverflow
    //     0x8d0ca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d0ca4: cmp             SP, x16
    //     0x8d0ca8: b.ls            #0x8d0cc4
    // 0x8d0cac: r1 = Null
    //     0x8d0cac: mov             x1, NULL
    // 0x8d0cb0: r2 = 128
    //     0x8d0cb0: movz            x2, #0x80
    // 0x8d0cb4: r0 = _BigIntImpl.from()
    //     0x8d0cb4: bl              #0x8cef14  ; [dart:core] _BigIntImpl::_BigIntImpl.from
    // 0x8d0cb8: LeaveFrame
    //     0x8d0cb8: mov             SP, fp
    //     0x8d0cbc: ldp             fp, lr, [SP], #0x10
    // 0x8d0cc0: ret
    //     0x8d0cc0: ret             
    // 0x8d0cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d0cc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d0cc8: b               #0x8d0cac
  }
  static _ constantTimeAreEqual(/* No info */) {
    // ** addr: 0xeb41c8, size: 0x124
    // 0xeb41c8: EnterFrame
    //     0xeb41c8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb41cc: mov             fp, SP
    // 0xeb41d0: mov             x3, x1
    // 0xeb41d4: cmp             w3, w2
    // 0xeb41d8: b.ne            #0xeb41ec
    // 0xeb41dc: r0 = true
    //     0xeb41dc: add             x0, NULL, #0x20  ; true
    // 0xeb41e0: LeaveFrame
    //     0xeb41e0: mov             SP, fp
    //     0xeb41e4: ldp             fp, lr, [SP], #0x10
    // 0xeb41e8: ret
    //     0xeb41e8: ret             
    // 0xeb41ec: LoadField: r4 = r3->field_13
    //     0xeb41ec: ldur            w4, [x3, #0x13]
    // 0xeb41f0: LoadField: r5 = r2->field_13
    //     0xeb41f0: ldur            w5, [x2, #0x13]
    // 0xeb41f4: r6 = LoadInt32Instr(r4)
    //     0xeb41f4: sbfx            x6, x4, #1, #0x1f
    // 0xeb41f8: r4 = LoadInt32Instr(r5)
    //     0xeb41f8: sbfx            x4, x5, #1, #0x1f
    // 0xeb41fc: cmp             x6, x4
    // 0xeb4200: b.ge            #0xeb420c
    // 0xeb4204: mov             x5, x6
    // 0xeb4208: b               #0xeb4210
    // 0xeb420c: mov             x5, x4
    // 0xeb4210: eor             x7, x6, x4
    // 0xeb4214: mov             x8, x7
    // 0xeb4218: r7 = 0
    //     0xeb4218: movz            x7, #0
    // 0xeb421c: CheckStackOverflow
    //     0xeb421c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb4220: cmp             SP, x16
    //     0xeb4224: b.ls            #0xeb42d4
    // 0xeb4228: cmp             x7, x5
    // 0xeb422c: b.eq            #0xeb4278
    // 0xeb4230: mov             x0, x6
    // 0xeb4234: mov             x1, x7
    // 0xeb4238: cmp             x1, x0
    // 0xeb423c: b.hs            #0xeb42dc
    // 0xeb4240: ArrayLoad: r9 = r3[r7]  ; List_1
    //     0xeb4240: add             x16, x3, x7
    //     0xeb4244: ldrb            w9, [x16, #0x17]
    // 0xeb4248: mov             x0, x4
    // 0xeb424c: mov             x1, x7
    // 0xeb4250: cmp             x1, x0
    // 0xeb4254: b.hs            #0xeb42e0
    // 0xeb4258: ArrayLoad: r1 = r2[r7]  ; List_1
    //     0xeb4258: add             x16, x2, x7
    //     0xeb425c: ldrb            w1, [x16, #0x17]
    // 0xeb4260: eor             x10, x9, x1
    // 0xeb4264: orr             x0, x8, x10
    // 0xeb4268: add             x1, x7, #1
    // 0xeb426c: mov             x8, x0
    // 0xeb4270: mov             x7, x1
    // 0xeb4274: b               #0xeb421c
    // 0xeb4278: mov             x3, x8
    // 0xeb427c: mov             x1, x5
    // 0xeb4280: CheckStackOverflow
    //     0xeb4280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb4284: cmp             SP, x16
    //     0xeb4288: b.ls            #0xeb42e4
    // 0xeb428c: cmp             x1, x4
    // 0xeb4290: b.ge            #0xeb42b8
    // 0xeb4294: ArrayLoad: r5 = r2[r1]  ; List_1
    //     0xeb4294: add             x16, x2, x1
    //     0xeb4298: ldrb            w5, [x16, #0x17]
    // 0xeb429c: mvn             x6, x5
    // 0xeb42a0: eor             x7, x5, x6
    // 0xeb42a4: orr             x0, x3, x7
    // 0xeb42a8: add             x5, x1, #1
    // 0xeb42ac: mov             x3, x0
    // 0xeb42b0: mov             x1, x5
    // 0xeb42b4: b               #0xeb4280
    // 0xeb42b8: cbz             x3, #0xeb42c4
    // 0xeb42bc: r0 = false
    //     0xeb42bc: add             x0, NULL, #0x30  ; false
    // 0xeb42c0: b               #0xeb42c8
    // 0xeb42c4: r0 = true
    //     0xeb42c4: add             x0, NULL, #0x20  ; true
    // 0xeb42c8: LeaveFrame
    //     0xeb42c8: mov             SP, fp
    //     0xeb42cc: ldp             fp, lr, [SP], #0x10
    // 0xeb42d0: ret
    //     0xeb42d0: ret             
    // 0xeb42d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb42d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb42d8: b               #0xeb4228
    // 0xeb42dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb42dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeb42e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb42e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeb42e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb42e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb42e8: b               #0xeb428c
  }
}
