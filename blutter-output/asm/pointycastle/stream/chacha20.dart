// lib: impl.stream_cipher.chacha20, url: package:pointycastle/stream/chacha20.dart

// class id: 1051050, size: 0x8
class :: {
}

// class id: 559, size: 0x8, field offset: 0x8
class ChaCha20Engine extends BaseStreamCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xea0

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c52b0, size: 0x64
    // 0x8c52b0: EnterFrame
    //     0x8c52b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c52b4: mov             fp, SP
    // 0x8c52b8: AllocStack(0x8)
    //     0x8c52b8: sub             SP, SP, #8
    // 0x8c52bc: CheckStackOverflow
    //     0x8c52bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c52c0: cmp             SP, x16
    //     0x8c52c4: b.ls            #0x8c530c
    // 0x8c52c8: r0 = DynamicFactoryConfig()
    //     0x8c52c8: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c52cc: r1 = Function '<anonymous closure>': static.
    //     0x8c52cc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18128] AnonymousClosure: static (0x8c5314), in [package:pointycastle/stream/chacha20.dart] ChaCha20Engine::factoryConfig (0x8c52b0)
    //     0x8c52d0: ldr             x1, [x1, #0x128]
    // 0x8c52d4: r2 = Null
    //     0x8c52d4: mov             x2, NULL
    // 0x8c52d8: stur            x0, [fp, #-8]
    // 0x8c52dc: r0 = AllocateClosure()
    //     0x8c52dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c52e0: ldur            x1, [fp, #-8]
    // 0x8c52e4: mov             x5, x0
    // 0x8c52e8: r2 = StreamCipher
    //     0x8c52e8: add             x2, PP, #0x18, lsl #12  ; [pp+0x18048] Type: StreamCipher
    //     0x8c52ec: ldr             x2, [x2, #0x48]
    // 0x8c52f0: r3 = "ChaCha20/"
    //     0x8c52f0: add             x3, PP, #0x18, lsl #12  ; [pp+0x18130] "ChaCha20/"
    //     0x8c52f4: ldr             x3, [x3, #0x130]
    // 0x8c52f8: r0 = DynamicFactoryConfig.prefix()
    //     0x8c52f8: bl              #0x8c5098  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.prefix
    // 0x8c52fc: ldur            x0, [fp, #-8]
    // 0x8c5300: LeaveFrame
    //     0x8c5300: mov             SP, fp
    //     0x8c5304: ldp             fp, lr, [SP], #0x10
    // 0x8c5308: ret
    //     0x8c5308: ret             
    // 0x8c530c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c530c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5310: b               #0x8c52c8
  }
  [closure] static (dynamic) => ChaCha20Engine <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c5314, size: 0x54
    // 0x8c5314: EnterFrame
    //     0x8c5314: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5318: mov             fp, SP
    // 0x8c531c: AllocStack(0x8)
    //     0x8c531c: sub             SP, SP, #8
    // 0x8c5320: SetupParameters()
    //     0x8c5320: ldr             x0, [fp, #0x20]
    //     0x8c5324: ldur            w1, [x0, #0x17]
    //     0x8c5328: add             x1, x1, HEAP, lsl #32
    //     0x8c532c: stur            x1, [fp, #-8]
    // 0x8c5330: r1 = 1
    //     0x8c5330: movz            x1, #0x1
    // 0x8c5334: r0 = AllocateContext()
    //     0x8c5334: bl              #0xec126c  ; AllocateContextStub
    // 0x8c5338: mov             x1, x0
    // 0x8c533c: ldur            x0, [fp, #-8]
    // 0x8c5340: StoreField: r1->field_b = r0
    //     0x8c5340: stur            w0, [x1, #0xb]
    // 0x8c5344: ldr             x0, [fp, #0x10]
    // 0x8c5348: StoreField: r1->field_f = r0
    //     0x8c5348: stur            w0, [x1, #0xf]
    // 0x8c534c: mov             x2, x1
    // 0x8c5350: r1 = Function '<anonymous closure>': static.
    //     0x8c5350: add             x1, PP, #0x18, lsl #12  ; [pp+0x18138] AnonymousClosure: static (0x8c5368), in [package:pointycastle/stream/chacha20.dart] ChaCha20Engine::factoryConfig (0x8c52b0)
    //     0x8c5354: ldr             x1, [x1, #0x138]
    // 0x8c5358: r0 = AllocateClosure()
    //     0x8c5358: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c535c: LeaveFrame
    //     0x8c535c: mov             SP, fp
    //     0x8c5360: ldp             fp, lr, [SP], #0x10
    // 0x8c5364: ret
    //     0x8c5364: ret             
  }
  [closure] static ChaCha20Engine <anonymous closure>(dynamic) {
    // ** addr: 0x8c5368, size: 0x90
    // 0x8c5368: EnterFrame
    //     0x8c5368: stp             fp, lr, [SP, #-0x10]!
    //     0x8c536c: mov             fp, SP
    // 0x8c5370: AllocStack(0x8)
    //     0x8c5370: sub             SP, SP, #8
    // 0x8c5374: SetupParameters()
    //     0x8c5374: ldr             x0, [fp, #0x10]
    //     0x8c5378: ldur            w1, [x0, #0x17]
    //     0x8c537c: add             x1, x1, HEAP, lsl #32
    // 0x8c5380: CheckStackOverflow
    //     0x8c5380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5384: cmp             SP, x16
    //     0x8c5388: b.ls            #0x8c53ec
    // 0x8c538c: LoadField: r0 = r1->field_f
    //     0x8c538c: ldur            w0, [x1, #0xf]
    // 0x8c5390: DecompressPointer r0
    //     0x8c5390: add             x0, x0, HEAP, lsl #32
    // 0x8c5394: r1 = LoadClassIdInstr(r0)
    //     0x8c5394: ldur            x1, [x0, #-1]
    //     0x8c5398: ubfx            x1, x1, #0xc, #0x14
    // 0x8c539c: mov             x16, x0
    // 0x8c53a0: mov             x0, x1
    // 0x8c53a4: mov             x1, x16
    // 0x8c53a8: r2 = 1
    //     0x8c53a8: movz            x2, #0x1
    // 0x8c53ac: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c53ac: sub             lr, x0, #0xfdd
    //     0x8c53b0: ldr             lr, [x21, lr, lsl #3]
    //     0x8c53b4: blr             lr
    // 0x8c53b8: cmp             w0, NULL
    // 0x8c53bc: b.eq            #0x8c53f4
    // 0x8c53c0: mov             x1, x0
    // 0x8c53c4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8c53c4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8c53c8: r0 = parse()
    //     0x8c53c8: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8c53cc: r0 = ChaCha20Engine()
    //     0x8c53cc: bl              #0x8c53f8  ; AllocateChaCha20EngineStub -> ChaCha20Engine (size=0x8)
    // 0x8c53d0: mov             x1, x0
    // 0x8c53d4: stur            x0, [fp, #-8]
    // 0x8c53d8: r0 = Salsa20Engine()
    //     0x8c53d8: bl              #0x8c4f60  ; [package:pointycastle/stream/salsa20.dart] Salsa20Engine::Salsa20Engine
    // 0x8c53dc: ldur            x0, [fp, #-8]
    // 0x8c53e0: LeaveFrame
    //     0x8c53e0: mov             SP, fp
    //     0x8c53e4: ldp             fp, lr, [SP], #0x10
    // 0x8c53e8: ret
    //     0x8c53e8: ret             
    // 0x8c53ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c53ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c53f0: b               #0x8c538c
    // 0x8c53f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c53f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
