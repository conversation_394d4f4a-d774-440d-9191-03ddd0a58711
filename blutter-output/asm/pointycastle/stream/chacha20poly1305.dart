// lib: impl.stream_cipher.chacha20poly1305, url: package:pointycastle/stream/chacha20poly1305.dart

// class id: 1051051, size: 0x8
class :: {
}

// class id: 562, size: 0x8, field offset: 0x8
class ChaCha20Poly1305 extends BaseAEADCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xea8

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c4e9c, size: 0x58
    // 0x8c4e9c: EnterFrame
    //     0x8c4e9c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4ea0: mov             fp, SP
    // 0x8c4ea4: AllocStack(0x8)
    //     0x8c4ea4: sub             SP, SP, #8
    // 0x8c4ea8: r0 = StaticFactoryConfig()
    //     0x8c4ea8: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c4eac: mov             x3, x0
    // 0x8c4eb0: r0 = "ChaCha20-Poly1305"
    //     0x8c4eb0: add             x0, PP, #0x18, lsl #12  ; [pp+0x180e8] "ChaCha20-Poly1305"
    //     0x8c4eb4: ldr             x0, [x0, #0xe8]
    // 0x8c4eb8: stur            x3, [fp, #-8]
    // 0x8c4ebc: StoreField: r3->field_b = r0
    //     0x8c4ebc: stur            w0, [x3, #0xb]
    // 0x8c4ec0: r1 = Function '<anonymous closure>': static.
    //     0x8c4ec0: add             x1, PP, #0x18, lsl #12  ; [pp+0x180f0] AnonymousClosure: static (0x8c4ef4), in [package:pointycastle/stream/chacha20poly1305.dart] ChaCha20Poly1305::factoryConfig (0x8c4e9c)
    //     0x8c4ec4: ldr             x1, [x1, #0xf0]
    // 0x8c4ec8: r2 = Null
    //     0x8c4ec8: mov             x2, NULL
    // 0x8c4ecc: r0 = AllocateClosure()
    //     0x8c4ecc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c4ed0: mov             x1, x0
    // 0x8c4ed4: ldur            x0, [fp, #-8]
    // 0x8c4ed8: StoreField: r0->field_f = r1
    //     0x8c4ed8: stur            w1, [x0, #0xf]
    // 0x8c4edc: r1 = AEADCipher
    //     0x8c4edc: add             x1, PP, #0x18, lsl #12  ; [pp+0x18058] Type: AEADCipher
    //     0x8c4ee0: ldr             x1, [x1, #0x58]
    // 0x8c4ee4: StoreField: r0->field_7 = r1
    //     0x8c4ee4: stur            w1, [x0, #7]
    // 0x8c4ee8: LeaveFrame
    //     0x8c4ee8: mov             SP, fp
    //     0x8c4eec: ldp             fp, lr, [SP], #0x10
    // 0x8c4ef0: ret
    //     0x8c4ef0: ret             
  }
  [closure] static ChaCha20Poly1305 <anonymous closure>(dynamic) {
    // ** addr: 0x8c4ef4, size: 0x60
    // 0x8c4ef4: EnterFrame
    //     0x8c4ef4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4ef8: mov             fp, SP
    // 0x8c4efc: CheckStackOverflow
    //     0x8c4efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c4f00: cmp             SP, x16
    //     0x8c4f04: b.ls            #0x8c4f4c
    // 0x8c4f08: r0 = ChaCha7539Engine()
    //     0x8c4f08: bl              #0x8c5004  ; AllocateChaCha7539EngineStub -> ChaCha7539Engine (size=0x8)
    // 0x8c4f0c: mov             x1, x0
    // 0x8c4f10: r0 = Salsa20Engine()
    //     0x8c4f10: bl              #0x8c4f60  ; [package:pointycastle/stream/salsa20.dart] Salsa20Engine::Salsa20Engine
    // 0x8c4f14: r0 = InitLateStaticField(0x1734) // [package:pointycastle/src/platform_check/native.dart] PlatformIO::instance
    //     0x8c4f14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c4f18: ldr             x0, [x0, #0x2e68]
    //     0x8c4f1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c4f20: cmp             w0, w16
    //     0x8c4f24: b.ne            #0x8c4f34
    //     0x8c4f28: add             x2, PP, #0x18, lsl #12  ; [pp+0x180f8] Field <PlatformIO.instance>: static late final (offset: 0x1734)
    //     0x8c4f2c: ldr             x2, [x2, #0xf8]
    //     0x8c4f30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c4f34: r4 = 160
    //     0x8c4f34: movz            x4, #0xa0
    // 0x8c4f38: r0 = AllocateUint8Array()
    //     0x8c4f38: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4f3c: r0 = ChaCha20Poly1305()
    //     0x8c4f3c: bl              #0x8c4f54  ; AllocateChaCha20Poly1305Stub -> ChaCha20Poly1305 (size=0x8)
    // 0x8c4f40: LeaveFrame
    //     0x8c4f40: mov             SP, fp
    //     0x8c4f44: ldp             fp, lr, [SP], #0x10
    // 0x8c4f48: ret
    //     0x8c4f48: ret             
    // 0x8c4f4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4f4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c4f50: b               #0x8c4f08
  }
}
