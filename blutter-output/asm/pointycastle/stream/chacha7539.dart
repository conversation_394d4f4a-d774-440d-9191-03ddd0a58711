// lib: impl.stream_cipher.chacha7539, url: package:pointycastle/stream/chacha7539.dart

// class id: 1051052, size: 0x8
class :: {
}

// class id: 558, size: 0x8, field offset: 0x8
class ChaCha7539Engine extends BaseStreamCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xea4

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c5034, size: 0x64
    // 0x8c5034: EnterFrame
    //     0x8c5034: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5038: mov             fp, SP
    // 0x8c503c: AllocStack(0x8)
    //     0x8c503c: sub             SP, SP, #8
    // 0x8c5040: CheckStackOverflow
    //     0x8c5040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5044: cmp             SP, x16
    //     0x8c5048: b.ls            #0x8c5090
    // 0x8c504c: r0 = DynamicFactoryConfig()
    //     0x8c504c: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c5050: r1 = Function '<anonymous closure>': static.
    //     0x8c5050: add             x1, PP, #0x18, lsl #12  ; [pp+0x18100] AnonymousClosure: static (0x8c51cc), in [package:pointycastle/stream/chacha7539.dart] ChaCha7539Engine::factoryConfig (0x8c5034)
    //     0x8c5054: ldr             x1, [x1, #0x100]
    // 0x8c5058: r2 = Null
    //     0x8c5058: mov             x2, NULL
    // 0x8c505c: stur            x0, [fp, #-8]
    // 0x8c5060: r0 = AllocateClosure()
    //     0x8c5060: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5064: ldur            x1, [fp, #-8]
    // 0x8c5068: mov             x5, x0
    // 0x8c506c: r2 = StreamCipher
    //     0x8c506c: add             x2, PP, #0x18, lsl #12  ; [pp+0x18048] Type: StreamCipher
    //     0x8c5070: ldr             x2, [x2, #0x48]
    // 0x8c5074: r3 = "ChaCha7539/"
    //     0x8c5074: add             x3, PP, #0x18, lsl #12  ; [pp+0x18108] "ChaCha7539/"
    //     0x8c5078: ldr             x3, [x3, #0x108]
    // 0x8c507c: r0 = DynamicFactoryConfig.prefix()
    //     0x8c507c: bl              #0x8c5098  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.prefix
    // 0x8c5080: ldur            x0, [fp, #-8]
    // 0x8c5084: LeaveFrame
    //     0x8c5084: mov             SP, fp
    //     0x8c5088: ldp             fp, lr, [SP], #0x10
    // 0x8c508c: ret
    //     0x8c508c: ret             
    // 0x8c5090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5090: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5094: b               #0x8c504c
  }
  [closure] static (dynamic) => ChaCha7539Engine <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c51cc, size: 0x54
    // 0x8c51cc: EnterFrame
    //     0x8c51cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c51d0: mov             fp, SP
    // 0x8c51d4: AllocStack(0x8)
    //     0x8c51d4: sub             SP, SP, #8
    // 0x8c51d8: SetupParameters()
    //     0x8c51d8: ldr             x0, [fp, #0x20]
    //     0x8c51dc: ldur            w1, [x0, #0x17]
    //     0x8c51e0: add             x1, x1, HEAP, lsl #32
    //     0x8c51e4: stur            x1, [fp, #-8]
    // 0x8c51e8: r1 = 1
    //     0x8c51e8: movz            x1, #0x1
    // 0x8c51ec: r0 = AllocateContext()
    //     0x8c51ec: bl              #0xec126c  ; AllocateContextStub
    // 0x8c51f0: mov             x1, x0
    // 0x8c51f4: ldur            x0, [fp, #-8]
    // 0x8c51f8: StoreField: r1->field_b = r0
    //     0x8c51f8: stur            w0, [x1, #0xb]
    // 0x8c51fc: ldr             x0, [fp, #0x10]
    // 0x8c5200: StoreField: r1->field_f = r0
    //     0x8c5200: stur            w0, [x1, #0xf]
    // 0x8c5204: mov             x2, x1
    // 0x8c5208: r1 = Function '<anonymous closure>': static.
    //     0x8c5208: add             x1, PP, #0x18, lsl #12  ; [pp+0x18110] AnonymousClosure: static (0x8c5220), in [package:pointycastle/stream/chacha7539.dart] ChaCha7539Engine::factoryConfig (0x8c5034)
    //     0x8c520c: ldr             x1, [x1, #0x110]
    // 0x8c5210: r0 = AllocateClosure()
    //     0x8c5210: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5214: LeaveFrame
    //     0x8c5214: mov             SP, fp
    //     0x8c5218: ldp             fp, lr, [SP], #0x10
    // 0x8c521c: ret
    //     0x8c521c: ret             
  }
  [closure] static ChaCha7539Engine <anonymous closure>(dynamic) {
    // ** addr: 0x8c5220, size: 0x90
    // 0x8c5220: EnterFrame
    //     0x8c5220: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5224: mov             fp, SP
    // 0x8c5228: AllocStack(0x8)
    //     0x8c5228: sub             SP, SP, #8
    // 0x8c522c: SetupParameters()
    //     0x8c522c: ldr             x0, [fp, #0x10]
    //     0x8c5230: ldur            w1, [x0, #0x17]
    //     0x8c5234: add             x1, x1, HEAP, lsl #32
    // 0x8c5238: CheckStackOverflow
    //     0x8c5238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c523c: cmp             SP, x16
    //     0x8c5240: b.ls            #0x8c52a4
    // 0x8c5244: LoadField: r0 = r1->field_f
    //     0x8c5244: ldur            w0, [x1, #0xf]
    // 0x8c5248: DecompressPointer r0
    //     0x8c5248: add             x0, x0, HEAP, lsl #32
    // 0x8c524c: r1 = LoadClassIdInstr(r0)
    //     0x8c524c: ldur            x1, [x0, #-1]
    //     0x8c5250: ubfx            x1, x1, #0xc, #0x14
    // 0x8c5254: mov             x16, x0
    // 0x8c5258: mov             x0, x1
    // 0x8c525c: mov             x1, x16
    // 0x8c5260: r2 = 1
    //     0x8c5260: movz            x2, #0x1
    // 0x8c5264: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c5264: sub             lr, x0, #0xfdd
    //     0x8c5268: ldr             lr, [x21, lr, lsl #3]
    //     0x8c526c: blr             lr
    // 0x8c5270: cmp             w0, NULL
    // 0x8c5274: b.eq            #0x8c52ac
    // 0x8c5278: mov             x1, x0
    // 0x8c527c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8c527c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8c5280: r0 = parse()
    //     0x8c5280: bl              #0x6062cc  ; [dart:core] int::parse
    // 0x8c5284: r0 = ChaCha7539Engine()
    //     0x8c5284: bl              #0x8c5004  ; AllocateChaCha7539EngineStub -> ChaCha7539Engine (size=0x8)
    // 0x8c5288: mov             x1, x0
    // 0x8c528c: stur            x0, [fp, #-8]
    // 0x8c5290: r0 = Salsa20Engine()
    //     0x8c5290: bl              #0x8c4f60  ; [package:pointycastle/stream/salsa20.dart] Salsa20Engine::Salsa20Engine
    // 0x8c5294: ldur            x0, [fp, #-8]
    // 0x8c5298: LeaveFrame
    //     0x8c5298: mov             SP, fp
    //     0x8c529c: ldp             fp, lr, [SP], #0x10
    // 0x8c52a0: ret
    //     0x8c52a0: ret             
    // 0x8c52a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c52a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c52a8: b               #0x8c5244
    // 0x8c52ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c52ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
