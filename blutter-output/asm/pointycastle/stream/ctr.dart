// lib: impl.stream_cipher.ctr, url: package:pointycastle/stream/ctr.dart

// class id: 1051053, size: 0x8
class :: {
}

// class id: 557, size: 0x1c, field offset: 0x1c
class CTRStreamCipher extends SICStreamCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xe94

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c54a8, size: 0x64
    // 0x8c54a8: EnterFrame
    //     0x8c54a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c54ac: mov             fp, SP
    // 0x8c54b0: AllocStack(0x8)
    //     0x8c54b0: sub             SP, SP, #8
    // 0x8c54b4: CheckStackOverflow
    //     0x8c54b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c54b8: cmp             SP, x16
    //     0x8c54bc: b.ls            #0x8c5504
    // 0x8c54c0: r0 = DynamicFactoryConfig()
    //     0x8c54c0: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c54c4: r1 = Function '<anonymous closure>': static.
    //     0x8c54c4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18150] AnonymousClosure: static (0x8c550c), in [package:pointycastle/stream/ctr.dart] CTRStreamCipher::factoryConfig (0x8c54a8)
    //     0x8c54c8: ldr             x1, [x1, #0x150]
    // 0x8c54cc: r2 = Null
    //     0x8c54cc: mov             x2, NULL
    // 0x8c54d0: stur            x0, [fp, #-8]
    // 0x8c54d4: r0 = AllocateClosure()
    //     0x8c54d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c54d8: ldur            x1, [fp, #-8]
    // 0x8c54dc: mov             x5, x0
    // 0x8c54e0: r2 = StreamCipher
    //     0x8c54e0: add             x2, PP, #0x18, lsl #12  ; [pp+0x18048] Type: StreamCipher
    //     0x8c54e4: ldr             x2, [x2, #0x48]
    // 0x8c54e8: r3 = "/CTR"
    //     0x8c54e8: add             x3, PP, #0x18, lsl #12  ; [pp+0x18158] "/CTR"
    //     0x8c54ec: ldr             x3, [x3, #0x158]
    // 0x8c54f0: r0 = DynamicFactoryConfig.suffix()
    //     0x8c54f0: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c54f4: ldur            x0, [fp, #-8]
    // 0x8c54f8: LeaveFrame
    //     0x8c54f8: mov             SP, fp
    //     0x8c54fc: ldp             fp, lr, [SP], #0x10
    // 0x8c5500: ret
    //     0x8c5500: ret             
    // 0x8c5504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5504: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5508: b               #0x8c54c0
  }
  [closure] static (dynamic) => CTRStreamCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c550c, size: 0x54
    // 0x8c550c: EnterFrame
    //     0x8c550c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5510: mov             fp, SP
    // 0x8c5514: AllocStack(0x8)
    //     0x8c5514: sub             SP, SP, #8
    // 0x8c5518: SetupParameters()
    //     0x8c5518: ldr             x0, [fp, #0x20]
    //     0x8c551c: ldur            w1, [x0, #0x17]
    //     0x8c5520: add             x1, x1, HEAP, lsl #32
    //     0x8c5524: stur            x1, [fp, #-8]
    // 0x8c5528: r1 = 1
    //     0x8c5528: movz            x1, #0x1
    // 0x8c552c: r0 = AllocateContext()
    //     0x8c552c: bl              #0xec126c  ; AllocateContextStub
    // 0x8c5530: mov             x1, x0
    // 0x8c5534: ldur            x0, [fp, #-8]
    // 0x8c5538: StoreField: r1->field_b = r0
    //     0x8c5538: stur            w0, [x1, #0xb]
    // 0x8c553c: ldr             x0, [fp, #0x10]
    // 0x8c5540: StoreField: r1->field_f = r0
    //     0x8c5540: stur            w0, [x1, #0xf]
    // 0x8c5544: mov             x2, x1
    // 0x8c5548: r1 = Function '<anonymous closure>': static.
    //     0x8c5548: add             x1, PP, #0x18, lsl #12  ; [pp+0x18160] AnonymousClosure: static (0x8c5560), in [package:pointycastle/stream/ctr.dart] CTRStreamCipher::factoryConfig (0x8c54a8)
    //     0x8c554c: ldr             x1, [x1, #0x160]
    // 0x8c5550: r0 = AllocateClosure()
    //     0x8c5550: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5554: LeaveFrame
    //     0x8c5554: mov             SP, fp
    //     0x8c5558: ldp             fp, lr, [SP], #0x10
    // 0x8c555c: ret
    //     0x8c555c: ret             
  }
  [closure] static CTRStreamCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8c5560, size: 0xcc
    // 0x8c5560: EnterFrame
    //     0x8c5560: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5564: mov             fp, SP
    // 0x8c5568: AllocStack(0x20)
    //     0x8c5568: sub             SP, SP, #0x20
    // 0x8c556c: SetupParameters()
    //     0x8c556c: ldr             x0, [fp, #0x10]
    //     0x8c5570: ldur            w1, [x0, #0x17]
    //     0x8c5574: add             x1, x1, HEAP, lsl #32
    // 0x8c5578: CheckStackOverflow
    //     0x8c5578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c557c: cmp             SP, x16
    //     0x8c5580: b.ls            #0x8c5620
    // 0x8c5584: LoadField: r0 = r1->field_f
    //     0x8c5584: ldur            w0, [x1, #0xf]
    // 0x8c5588: DecompressPointer r0
    //     0x8c5588: add             x0, x0, HEAP, lsl #32
    // 0x8c558c: r1 = LoadClassIdInstr(r0)
    //     0x8c558c: ldur            x1, [x0, #-1]
    //     0x8c5590: ubfx            x1, x1, #0xc, #0x14
    // 0x8c5594: mov             x16, x0
    // 0x8c5598: mov             x0, x1
    // 0x8c559c: mov             x1, x16
    // 0x8c55a0: r2 = 1
    //     0x8c55a0: movz            x2, #0x1
    // 0x8c55a4: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c55a4: sub             lr, x0, #0xfdd
    //     0x8c55a8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c55ac: blr             lr
    // 0x8c55b0: stur            x0, [fp, #-8]
    // 0x8c55b4: cmp             w0, NULL
    // 0x8c55b8: b.eq            #0x8c5628
    // 0x8c55bc: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c55bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c55c0: ldr             x0, [x0, #0x2e38]
    //     0x8c55c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c55c8: cmp             w0, w16
    //     0x8c55cc: b.ne            #0x8c55dc
    //     0x8c55d0: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c55d4: ldr             x2, [x2, #0xf80]
    //     0x8c55d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c55dc: r16 = <BlockCipher>
    //     0x8c55dc: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8c55e0: ldr             x16, [x16, #0x88]
    // 0x8c55e4: stp             x0, x16, [SP, #8]
    // 0x8c55e8: ldur            x16, [fp, #-8]
    // 0x8c55ec: str             x16, [SP]
    // 0x8c55f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c55f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c55f4: r0 = create()
    //     0x8c55f4: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c55f8: stur            x0, [fp, #-8]
    // 0x8c55fc: r0 = CTRStreamCipher()
    //     0x8c55fc: bl              #0x8c4c88  ; AllocateCTRStreamCipherStub -> CTRStreamCipher (size=0x1c)
    // 0x8c5600: mov             x1, x0
    // 0x8c5604: ldur            x2, [fp, #-8]
    // 0x8c5608: stur            x0, [fp, #-8]
    // 0x8c560c: r0 = SICStreamCipher()
    //     0x8c560c: bl              #0x8c4b0c  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::SICStreamCipher
    // 0x8c5610: ldur            x0, [fp, #-8]
    // 0x8c5614: LeaveFrame
    //     0x8c5614: mov             SP, fp
    //     0x8c5618: ldp             fp, lr, [SP], #0x10
    // 0x8c561c: ret
    //     0x8c561c: ret             
    // 0x8c5620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5620: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5624: b               #0x8c5584
    // 0x8c5628: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c5628: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
