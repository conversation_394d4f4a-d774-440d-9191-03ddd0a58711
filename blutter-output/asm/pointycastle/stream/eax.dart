// lib: impl.stream_cipher.eax, url: package:pointycastle/stream/eax.dart

// class id: 1051054, size: 0x8
class :: {
}

// class id: 561, size: 0x8, field offset: 0x8
class EAX extends BaseAEADCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xeac

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c3db8, size: 0x64
    // 0x8c3db8: EnterFrame
    //     0x8c3db8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3dbc: mov             fp, SP
    // 0x8c3dc0: AllocStack(0x8)
    //     0x8c3dc0: sub             SP, SP, #8
    // 0x8c3dc4: CheckStackOverflow
    //     0x8c3dc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c3dc8: cmp             SP, x16
    //     0x8c3dcc: b.ls            #0x8c3e14
    // 0x8c3dd0: r0 = DynamicFactoryConfig()
    //     0x8c3dd0: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c3dd4: r1 = Function '<anonymous closure>': static.
    //     0x8c3dd4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18050] AnonymousClosure: static (0x8c4358), in [package:pointycastle/stream/eax.dart] EAX::factoryConfig (0x8c3db8)
    //     0x8c3dd8: ldr             x1, [x1, #0x50]
    // 0x8c3ddc: r2 = Null
    //     0x8c3ddc: mov             x2, NULL
    // 0x8c3de0: stur            x0, [fp, #-8]
    // 0x8c3de4: r0 = AllocateClosure()
    //     0x8c3de4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c3de8: ldur            x1, [fp, #-8]
    // 0x8c3dec: mov             x5, x0
    // 0x8c3df0: r2 = AEADCipher
    //     0x8c3df0: add             x2, PP, #0x18, lsl #12  ; [pp+0x18058] Type: AEADCipher
    //     0x8c3df4: ldr             x2, [x2, #0x58]
    // 0x8c3df8: r3 = "/EAX"
    //     0x8c3df8: add             x3, PP, #0x18, lsl #12  ; [pp+0x18060] "/EAX"
    //     0x8c3dfc: ldr             x3, [x3, #0x60]
    // 0x8c3e00: r0 = DynamicFactoryConfig.suffix()
    //     0x8c3e00: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c3e04: ldur            x0, [fp, #-8]
    // 0x8c3e08: LeaveFrame
    //     0x8c3e08: mov             SP, fp
    //     0x8c3e0c: ldp             fp, lr, [SP], #0x10
    // 0x8c3e10: ret
    //     0x8c3e10: ret             
    // 0x8c3e14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3e14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c3e18: b               #0x8c3dd0
  }
  [closure] static (dynamic) => EAX <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c4358, size: 0x54
    // 0x8c4358: EnterFrame
    //     0x8c4358: stp             fp, lr, [SP, #-0x10]!
    //     0x8c435c: mov             fp, SP
    // 0x8c4360: AllocStack(0x8)
    //     0x8c4360: sub             SP, SP, #8
    // 0x8c4364: SetupParameters()
    //     0x8c4364: ldr             x0, [fp, #0x20]
    //     0x8c4368: ldur            w1, [x0, #0x17]
    //     0x8c436c: add             x1, x1, HEAP, lsl #32
    //     0x8c4370: stur            x1, [fp, #-8]
    // 0x8c4374: r1 = 1
    //     0x8c4374: movz            x1, #0x1
    // 0x8c4378: r0 = AllocateContext()
    //     0x8c4378: bl              #0xec126c  ; AllocateContextStub
    // 0x8c437c: mov             x1, x0
    // 0x8c4380: ldur            x0, [fp, #-8]
    // 0x8c4384: StoreField: r1->field_b = r0
    //     0x8c4384: stur            w0, [x1, #0xb]
    // 0x8c4388: ldr             x0, [fp, #0x10]
    // 0x8c438c: StoreField: r1->field_f = r0
    //     0x8c438c: stur            w0, [x1, #0xf]
    // 0x8c4390: mov             x2, x1
    // 0x8c4394: r1 = Function '<anonymous closure>': static.
    //     0x8c4394: add             x1, PP, #0x18, lsl #12  ; [pp+0x18068] AnonymousClosure: static (0x8c43ac), in [package:pointycastle/stream/eax.dart] EAX::factoryConfig (0x8c3db8)
    //     0x8c4398: ldr             x1, [x1, #0x68]
    // 0x8c439c: r0 = AllocateClosure()
    //     0x8c439c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c43a0: LeaveFrame
    //     0x8c43a0: mov             SP, fp
    //     0x8c43a4: ldp             fp, lr, [SP], #0x10
    // 0x8c43a8: ret
    //     0x8c43a8: ret             
  }
  [closure] static EAX <anonymous closure>(dynamic) {
    // ** addr: 0x8c43ac, size: 0x98
    // 0x8c43ac: EnterFrame
    //     0x8c43ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8c43b0: mov             fp, SP
    // 0x8c43b4: AllocStack(0x8)
    //     0x8c43b4: sub             SP, SP, #8
    // 0x8c43b8: SetupParameters()
    //     0x8c43b8: ldr             x0, [fp, #0x10]
    //     0x8c43bc: ldur            w1, [x0, #0x17]
    //     0x8c43c0: add             x1, x1, HEAP, lsl #32
    // 0x8c43c4: CheckStackOverflow
    //     0x8c43c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c43c8: cmp             SP, x16
    //     0x8c43cc: b.ls            #0x8c4438
    // 0x8c43d0: LoadField: r0 = r1->field_f
    //     0x8c43d0: ldur            w0, [x1, #0xf]
    // 0x8c43d4: DecompressPointer r0
    //     0x8c43d4: add             x0, x0, HEAP, lsl #32
    // 0x8c43d8: r1 = LoadClassIdInstr(r0)
    //     0x8c43d8: ldur            x1, [x0, #-1]
    //     0x8c43dc: ubfx            x1, x1, #0xc, #0x14
    // 0x8c43e0: mov             x16, x0
    // 0x8c43e4: mov             x0, x1
    // 0x8c43e8: mov             x1, x16
    // 0x8c43ec: r2 = 1
    //     0x8c43ec: movz            x2, #0x1
    // 0x8c43f0: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c43f0: sub             lr, x0, #0xfdd
    //     0x8c43f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8c43f8: blr             lr
    // 0x8c43fc: cmp             w0, NULL
    // 0x8c4400: b.eq            #0x8c4440
    // 0x8c4404: mov             x2, x0
    // 0x8c4408: r1 = Null
    //     0x8c4408: mov             x1, NULL
    // 0x8c440c: r0 = BlockCipher()
    //     0x8c440c: bl              #0x8c4ca0  ; [package:pointycastle/api.dart] BlockCipher::BlockCipher
    // 0x8c4410: stur            x0, [fp, #-8]
    // 0x8c4414: r0 = EAX()
    //     0x8c4414: bl              #0x8c4c94  ; AllocateEAXStub -> EAX (size=0x8)
    // 0x8c4418: mov             x1, x0
    // 0x8c441c: ldur            x2, [fp, #-8]
    // 0x8c4420: stur            x0, [fp, #-8]
    // 0x8c4424: r0 = EAX()
    //     0x8c4424: bl              #0x8c4444  ; [package:pointycastle/stream/eax.dart] EAX::EAX
    // 0x8c4428: ldur            x0, [fp, #-8]
    // 0x8c442c: LeaveFrame
    //     0x8c442c: mov             SP, fp
    //     0x8c4430: ldp             fp, lr, [SP], #0x10
    // 0x8c4434: ret
    //     0x8c4434: ret             
    // 0x8c4438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c443c: b               #0x8c43d0
    // 0x8c4440: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c4440: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ EAX(/* No info */) {
    // ** addr: 0x8c4444, size: 0x94
    // 0x8c4444: EnterFrame
    //     0x8c4444: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4448: mov             fp, SP
    // 0x8c444c: AllocStack(0x10)
    //     0x8c444c: sub             SP, SP, #0x10
    // 0x8c4450: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8c4450: stur            x2, [fp, #-8]
    // 0x8c4454: CheckStackOverflow
    //     0x8c4454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c4458: cmp             SP, x16
    //     0x8c445c: b.ls            #0x8c44d0
    // 0x8c4460: r0 = CTRStreamCipher()
    //     0x8c4460: bl              #0x8c4c88  ; AllocateCTRStreamCipherStub -> CTRStreamCipher (size=0x1c)
    // 0x8c4464: mov             x1, x0
    // 0x8c4468: ldur            x2, [fp, #-8]
    // 0x8c446c: r0 = SICStreamCipher()
    //     0x8c446c: bl              #0x8c4b0c  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::SICStreamCipher
    // 0x8c4470: ldur            x2, [fp, #-8]
    // 0x8c4474: r0 = LoadClassIdInstr(r2)
    //     0x8c4474: ldur            x0, [x2, #-1]
    //     0x8c4478: ubfx            x0, x0, #0xc, #0x14
    // 0x8c447c: mov             x1, x2
    // 0x8c4480: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c4480: sub             lr, x0, #1, lsl #12
    //     0x8c4484: ldr             lr, [x21, lr, lsl #3]
    //     0x8c4488: blr             lr
    // 0x8c448c: lsl             x3, x0, #3
    // 0x8c4490: stur            x3, [fp, #-0x10]
    // 0x8c4494: r0 = CMac()
    //     0x8c4494: bl              #0x8c4b00  ; AllocateCMacStub -> CMac (size=0x14)
    // 0x8c4498: mov             x1, x0
    // 0x8c449c: ldur            x2, [fp, #-8]
    // 0x8c44a0: ldur            x3, [fp, #-0x10]
    // 0x8c44a4: r0 = CMac()
    //     0x8c44a4: bl              #0x8c44d8  ; [package:pointycastle/macs/cmac.dart] CMac::CMac
    // 0x8c44a8: ldur            x1, [fp, #-8]
    // 0x8c44ac: r0 = LoadClassIdInstr(r1)
    //     0x8c44ac: ldur            x0, [x1, #-1]
    //     0x8c44b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8c44b4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c44b4: sub             lr, x0, #1, lsl #12
    //     0x8c44b8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c44bc: blr             lr
    // 0x8c44c0: r0 = Null
    //     0x8c44c0: mov             x0, NULL
    // 0x8c44c4: LeaveFrame
    //     0x8c44c4: mov             SP, fp
    //     0x8c44c8: ldp             fp, lr, [SP], #0x10
    // 0x8c44cc: ret
    //     0x8c44cc: ret             
    // 0x8c44d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c44d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c44d4: b               #0x8c4460
  }
}
