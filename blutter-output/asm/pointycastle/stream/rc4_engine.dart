// lib: impl.stream_cipher.rc4_engine, url: package:pointycastle/stream/rc4_engine.dart

// class id: 1051055, size: 0x8
class :: {
}

// class id: 555, size: 0x8, field offset: 0x8
class RC4Engine extends BaseStreamCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xeb0

  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c3d30, size: 0x58
    // 0x8c3d30: EnterFrame
    //     0x8c3d30: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3d34: mov             fp, SP
    // 0x8c3d38: AllocStack(0x8)
    //     0x8c3d38: sub             SP, SP, #8
    // 0x8c3d3c: r0 = StaticFactoryConfig()
    //     0x8c3d3c: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c3d40: mov             x3, x0
    // 0x8c3d44: r0 = "RC4"
    //     0x8c3d44: add             x0, PP, #0x18, lsl #12  ; [pp+0x18038] "RC4"
    //     0x8c3d48: ldr             x0, [x0, #0x38]
    // 0x8c3d4c: stur            x3, [fp, #-8]
    // 0x8c3d50: StoreField: r3->field_b = r0
    //     0x8c3d50: stur            w0, [x3, #0xb]
    // 0x8c3d54: r1 = Function '<anonymous closure>': static.
    //     0x8c3d54: add             x1, PP, #0x18, lsl #12  ; [pp+0x18040] AnonymousClosure: static (0x8c3d94), in [package:pointycastle/stream/rc4_engine.dart] RC4Engine::factoryConfig (0x8c3d30)
    //     0x8c3d58: ldr             x1, [x1, #0x40]
    // 0x8c3d5c: r2 = Null
    //     0x8c3d5c: mov             x2, NULL
    // 0x8c3d60: r0 = AllocateClosure()
    //     0x8c3d60: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c3d64: mov             x1, x0
    // 0x8c3d68: ldur            x0, [fp, #-8]
    // 0x8c3d6c: StoreField: r0->field_f = r1
    //     0x8c3d6c: stur            w1, [x0, #0xf]
    // 0x8c3d70: r1 = StreamCipher
    //     0x8c3d70: add             x1, PP, #0x18, lsl #12  ; [pp+0x18048] Type: StreamCipher
    //     0x8c3d74: ldr             x1, [x1, #0x48]
    // 0x8c3d78: StoreField: r0->field_7 = r1
    //     0x8c3d78: stur            w1, [x0, #7]
    // 0x8c3d7c: LeaveFrame
    //     0x8c3d7c: mov             SP, fp
    //     0x8c3d80: ldp             fp, lr, [SP], #0x10
    // 0x8c3d84: ret
    //     0x8c3d84: ret             
  }
  [closure] static RC4Engine <anonymous closure>(dynamic) {
    // ** addr: 0x8c3d94, size: 0x18
    // 0x8c3d94: EnterFrame
    //     0x8c3d94: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3d98: mov             fp, SP
    // 0x8c3d9c: r0 = RC4Engine()
    //     0x8c3d9c: bl              #0x8c3dac  ; AllocateRC4EngineStub -> RC4Engine (size=0x8)
    // 0x8c3da0: LeaveFrame
    //     0x8c3da0: mov             SP, fp
    //     0x8c3da4: ldp             fp, lr, [SP], #0x10
    // 0x8c3da8: ret
    //     0x8c3da8: ret             
  }
}
