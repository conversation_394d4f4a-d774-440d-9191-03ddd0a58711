// lib: impl.stream_cipher.salsa20, url: package:pointycastle/stream/salsa20.dart

// class id: 1051056, size: 0x8
class :: {
}

// class id: 554, size: 0x8, field offset: 0x8
class Salsa20Engine extends BaseStreamCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xe9c

  _ Salsa20Engine(/* No info */) {
    // ** addr: 0x8c4f60, size: 0xa4
    // 0x8c4f60: EnterFrame
    //     0x8c4f60: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4f64: mov             fp, SP
    // 0x8c4f68: mov             x0, x1
    // 0x8c4f6c: r1 = <int>
    //     0x8c4f6c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8c4f70: r2 = 32
    //     0x8c4f70: movz            x2, #0x20
    // 0x8c4f74: r0 = AllocateArray()
    //     0x8c4f74: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c4f78: r1 = 0
    //     0x8c4f78: movz            x1, #0
    // 0x8c4f7c: CheckStackOverflow
    //     0x8c4f7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c4f80: cmp             SP, x16
    //     0x8c4f84: b.ls            #0x8c4ff4
    // 0x8c4f88: cmp             x1, #0x10
    // 0x8c4f8c: b.ge            #0x8c4fa4
    // 0x8c4f90: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0x8c4f90: add             x2, x0, x1, lsl #2
    //     0x8c4f94: stur            wzr, [x2, #0xf]
    // 0x8c4f98: add             x2, x1, #1
    // 0x8c4f9c: mov             x1, x2
    // 0x8c4fa0: b               #0x8c4f7c
    // 0x8c4fa4: r1 = <int>
    //     0x8c4fa4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8c4fa8: r2 = 32
    //     0x8c4fa8: movz            x2, #0x20
    // 0x8c4fac: r0 = AllocateArray()
    //     0x8c4fac: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8c4fb0: r1 = 0
    //     0x8c4fb0: movz            x1, #0
    // 0x8c4fb4: CheckStackOverflow
    //     0x8c4fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c4fb8: cmp             SP, x16
    //     0x8c4fbc: b.ls            #0x8c4ffc
    // 0x8c4fc0: cmp             x1, #0x10
    // 0x8c4fc4: b.ge            #0x8c4fdc
    // 0x8c4fc8: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0x8c4fc8: add             x2, x0, x1, lsl #2
    //     0x8c4fcc: stur            wzr, [x2, #0xf]
    // 0x8c4fd0: add             x2, x1, #1
    // 0x8c4fd4: mov             x1, x2
    // 0x8c4fd8: b               #0x8c4fb4
    // 0x8c4fdc: r4 = 128
    //     0x8c4fdc: movz            x4, #0x80
    // 0x8c4fe0: r0 = AllocateUint8Array()
    //     0x8c4fe0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4fe4: r0 = Null
    //     0x8c4fe4: mov             x0, NULL
    // 0x8c4fe8: LeaveFrame
    //     0x8c4fe8: mov             SP, fp
    //     0x8c4fec: ldp             fp, lr, [SP], #0x10
    // 0x8c4ff0: ret
    //     0x8c4ff0: ret             
    // 0x8c4ff4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4ff4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c4ff8: b               #0x8c4f88
    // 0x8c4ffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4ffc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5000: b               #0x8c4fc0
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c5404, size: 0x58
    // 0x8c5404: EnterFrame
    //     0x8c5404: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5408: mov             fp, SP
    // 0x8c540c: AllocStack(0x8)
    //     0x8c540c: sub             SP, SP, #8
    // 0x8c5410: r0 = StaticFactoryConfig()
    //     0x8c5410: bl              #0x8c3d88  ; AllocateStaticFactoryConfigStub -> StaticFactoryConfig (size=0x14)
    // 0x8c5414: mov             x3, x0
    // 0x8c5418: r0 = "Salsa20"
    //     0x8c5418: add             x0, PP, #0x18, lsl #12  ; [pp+0x18140] "Salsa20"
    //     0x8c541c: ldr             x0, [x0, #0x140]
    // 0x8c5420: stur            x3, [fp, #-8]
    // 0x8c5424: StoreField: r3->field_b = r0
    //     0x8c5424: stur            w0, [x3, #0xb]
    // 0x8c5428: r1 = Function '<anonymous closure>': static.
    //     0x8c5428: add             x1, PP, #0x18, lsl #12  ; [pp+0x18148] AnonymousClosure: static (0x8c545c), in [package:pointycastle/stream/salsa20.dart] Salsa20Engine::factoryConfig (0x8c5404)
    //     0x8c542c: ldr             x1, [x1, #0x148]
    // 0x8c5430: r2 = Null
    //     0x8c5430: mov             x2, NULL
    // 0x8c5434: r0 = AllocateClosure()
    //     0x8c5434: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c5438: mov             x1, x0
    // 0x8c543c: ldur            x0, [fp, #-8]
    // 0x8c5440: StoreField: r0->field_f = r1
    //     0x8c5440: stur            w1, [x0, #0xf]
    // 0x8c5444: r1 = StreamCipher
    //     0x8c5444: add             x1, PP, #0x18, lsl #12  ; [pp+0x18048] Type: StreamCipher
    //     0x8c5448: ldr             x1, [x1, #0x48]
    // 0x8c544c: StoreField: r0->field_7 = r1
    //     0x8c544c: stur            w1, [x0, #7]
    // 0x8c5450: LeaveFrame
    //     0x8c5450: mov             SP, fp
    //     0x8c5454: ldp             fp, lr, [SP], #0x10
    // 0x8c5458: ret
    //     0x8c5458: ret             
  }
  [closure] static Salsa20Engine <anonymous closure>(dynamic) {
    // ** addr: 0x8c545c, size: 0x40
    // 0x8c545c: EnterFrame
    //     0x8c545c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5460: mov             fp, SP
    // 0x8c5464: AllocStack(0x8)
    //     0x8c5464: sub             SP, SP, #8
    // 0x8c5468: CheckStackOverflow
    //     0x8c5468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c546c: cmp             SP, x16
    //     0x8c5470: b.ls            #0x8c5494
    // 0x8c5474: r0 = Salsa20Engine()
    //     0x8c5474: bl              #0x8c549c  ; AllocateSalsa20EngineStub -> Salsa20Engine (size=0x8)
    // 0x8c5478: mov             x1, x0
    // 0x8c547c: stur            x0, [fp, #-8]
    // 0x8c5480: r0 = Salsa20Engine()
    //     0x8c5480: bl              #0x8c4f60  ; [package:pointycastle/stream/salsa20.dart] Salsa20Engine::Salsa20Engine
    // 0x8c5484: ldur            x0, [fp, #-8]
    // 0x8c5488: LeaveFrame
    //     0x8c5488: mov             SP, fp
    //     0x8c548c: ldp             fp, lr, [SP], #0x10
    // 0x8c5490: ret
    //     0x8c5490: ret             
    // 0x8c5494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5498: b               #0x8c5474
  }
}
