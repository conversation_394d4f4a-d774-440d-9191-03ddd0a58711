// lib: impl.stream_cipher.sic, url: package:pointycastle/stream/sic.dart

// class id: 1051057, size: 0x8
class :: {
}

// class id: 556, size: 0x1c, field offset: 0x8
class SICStreamCipher extends BaseStreamCipher {

  static late final FactoryConfig factoryConfig; // offset: 0xe98
  late Uint8List _iv; // offset: 0xc
  late Uint8List _counter; // offset: 0x10
  late Uint8List _counterOut; // offset: 0x14
  late int _consumed; // offset: 0x18

  _ SICStreamCipher(/* No info */) {
    // ** addr: 0x8c4b0c, size: 0x17c
    // 0x8c4b0c: EnterFrame
    //     0x8c4b0c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4b10: mov             fp, SP
    // 0x8c4b14: AllocStack(0x10)
    //     0x8c4b14: sub             SP, SP, #0x10
    // 0x8c4b18: r0 = Sentinel
    //     0x8c4b18: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c4b1c: mov             x3, x1
    // 0x8c4b20: stur            x1, [fp, #-8]
    // 0x8c4b24: stur            x2, [fp, #-0x10]
    // 0x8c4b28: CheckStackOverflow
    //     0x8c4b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c4b2c: cmp             SP, x16
    //     0x8c4b30: b.ls            #0x8c4c80
    // 0x8c4b34: StoreField: r3->field_b = r0
    //     0x8c4b34: stur            w0, [x3, #0xb]
    // 0x8c4b38: StoreField: r3->field_f = r0
    //     0x8c4b38: stur            w0, [x3, #0xf]
    // 0x8c4b3c: StoreField: r3->field_13 = r0
    //     0x8c4b3c: stur            w0, [x3, #0x13]
    // 0x8c4b40: ArrayStore: r3[0] = r0  ; List_4
    //     0x8c4b40: stur            w0, [x3, #0x17]
    // 0x8c4b44: mov             x0, x2
    // 0x8c4b48: StoreField: r3->field_7 = r0
    //     0x8c4b48: stur            w0, [x3, #7]
    //     0x8c4b4c: ldurb           w16, [x3, #-1]
    //     0x8c4b50: ldurb           w17, [x0, #-1]
    //     0x8c4b54: and             x16, x17, x16, lsr #2
    //     0x8c4b58: tst             x16, HEAP, lsr #32
    //     0x8c4b5c: b.eq            #0x8c4b64
    //     0x8c4b60: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8c4b64: r0 = LoadClassIdInstr(r2)
    //     0x8c4b64: ldur            x0, [x2, #-1]
    //     0x8c4b68: ubfx            x0, x0, #0xc, #0x14
    // 0x8c4b6c: mov             x1, x2
    // 0x8c4b70: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c4b70: sub             lr, x0, #1, lsl #12
    //     0x8c4b74: ldr             lr, [x21, lr, lsl #3]
    //     0x8c4b78: blr             lr
    // 0x8c4b7c: mov             x2, x0
    // 0x8c4b80: r0 = BoxInt64Instr(r2)
    //     0x8c4b80: sbfiz           x0, x2, #1, #0x1f
    //     0x8c4b84: cmp             x2, x0, asr #1
    //     0x8c4b88: b.eq            #0x8c4b94
    //     0x8c4b8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c4b90: stur            x2, [x0, #7]
    // 0x8c4b94: mov             x4, x0
    // 0x8c4b98: r0 = AllocateUint8Array()
    //     0x8c4b98: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4b9c: ldur            x2, [fp, #-8]
    // 0x8c4ba0: StoreField: r2->field_b = r0
    //     0x8c4ba0: stur            w0, [x2, #0xb]
    //     0x8c4ba4: ldurb           w16, [x2, #-1]
    //     0x8c4ba8: ldurb           w17, [x0, #-1]
    //     0x8c4bac: and             x16, x17, x16, lsr #2
    //     0x8c4bb0: tst             x16, HEAP, lsr #32
    //     0x8c4bb4: b.eq            #0x8c4bbc
    //     0x8c4bb8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c4bbc: ldur            x3, [fp, #-0x10]
    // 0x8c4bc0: r0 = LoadClassIdInstr(r3)
    //     0x8c4bc0: ldur            x0, [x3, #-1]
    //     0x8c4bc4: ubfx            x0, x0, #0xc, #0x14
    // 0x8c4bc8: mov             x1, x3
    // 0x8c4bcc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c4bcc: sub             lr, x0, #1, lsl #12
    //     0x8c4bd0: ldr             lr, [x21, lr, lsl #3]
    //     0x8c4bd4: blr             lr
    // 0x8c4bd8: mov             x2, x0
    // 0x8c4bdc: r0 = BoxInt64Instr(r2)
    //     0x8c4bdc: sbfiz           x0, x2, #1, #0x1f
    //     0x8c4be0: cmp             x2, x0, asr #1
    //     0x8c4be4: b.eq            #0x8c4bf0
    //     0x8c4be8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c4bec: stur            x2, [x0, #7]
    // 0x8c4bf0: mov             x4, x0
    // 0x8c4bf4: r0 = AllocateUint8Array()
    //     0x8c4bf4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4bf8: ldur            x2, [fp, #-8]
    // 0x8c4bfc: StoreField: r2->field_f = r0
    //     0x8c4bfc: stur            w0, [x2, #0xf]
    //     0x8c4c00: ldurb           w16, [x2, #-1]
    //     0x8c4c04: ldurb           w17, [x0, #-1]
    //     0x8c4c08: and             x16, x17, x16, lsr #2
    //     0x8c4c0c: tst             x16, HEAP, lsr #32
    //     0x8c4c10: b.eq            #0x8c4c18
    //     0x8c4c14: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8c4c18: ldur            x1, [fp, #-0x10]
    // 0x8c4c1c: r0 = LoadClassIdInstr(r1)
    //     0x8c4c1c: ldur            x0, [x1, #-1]
    //     0x8c4c20: ubfx            x0, x0, #0xc, #0x14
    // 0x8c4c24: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8c4c24: sub             lr, x0, #1, lsl #12
    //     0x8c4c28: ldr             lr, [x21, lr, lsl #3]
    //     0x8c4c2c: blr             lr
    // 0x8c4c30: mov             x2, x0
    // 0x8c4c34: r0 = BoxInt64Instr(r2)
    //     0x8c4c34: sbfiz           x0, x2, #1, #0x1f
    //     0x8c4c38: cmp             x2, x0, asr #1
    //     0x8c4c3c: b.eq            #0x8c4c48
    //     0x8c4c40: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c4c44: stur            x2, [x0, #7]
    // 0x8c4c48: mov             x4, x0
    // 0x8c4c4c: r0 = AllocateUint8Array()
    //     0x8c4c4c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8c4c50: ldur            x1, [fp, #-8]
    // 0x8c4c54: StoreField: r1->field_13 = r0
    //     0x8c4c54: stur            w0, [x1, #0x13]
    //     0x8c4c58: ldurb           w16, [x1, #-1]
    //     0x8c4c5c: ldurb           w17, [x0, #-1]
    //     0x8c4c60: and             x16, x17, x16, lsr #2
    //     0x8c4c64: tst             x16, HEAP, lsr #32
    //     0x8c4c68: b.eq            #0x8c4c70
    //     0x8c4c6c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8c4c70: r0 = Null
    //     0x8c4c70: mov             x0, NULL
    // 0x8c4c74: LeaveFrame
    //     0x8c4c74: mov             SP, fp
    //     0x8c4c78: ldp             fp, lr, [SP], #0x10
    // 0x8c4c7c: ret
    //     0x8c4c7c: ret             
    // 0x8c4c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4c80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c4c84: b               #0x8c4b34
  }
  static FactoryConfig factoryConfig() {
    // ** addr: 0x8c4d0c, size: 0x64
    // 0x8c4d0c: EnterFrame
    //     0x8c4d0c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4d10: mov             fp, SP
    // 0x8c4d14: AllocStack(0x8)
    //     0x8c4d14: sub             SP, SP, #8
    // 0x8c4d18: CheckStackOverflow
    //     0x8c4d18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c4d1c: cmp             SP, x16
    //     0x8c4d20: b.ls            #0x8c4d68
    // 0x8c4d24: r0 = DynamicFactoryConfig()
    //     0x8c4d24: bl              #0x8c434c  ; AllocateDynamicFactoryConfigStub -> DynamicFactoryConfig (size=0x14)
    // 0x8c4d28: r1 = Function '<anonymous closure>': static.
    //     0x8c4d28: add             x1, PP, #0x18, lsl #12  ; [pp+0x180d0] AnonymousClosure: static (0x8c4d70), in [package:pointycastle/stream/sic.dart] SICStreamCipher::factoryConfig (0x8c4d0c)
    //     0x8c4d2c: ldr             x1, [x1, #0xd0]
    // 0x8c4d30: r2 = Null
    //     0x8c4d30: mov             x2, NULL
    // 0x8c4d34: stur            x0, [fp, #-8]
    // 0x8c4d38: r0 = AllocateClosure()
    //     0x8c4d38: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c4d3c: ldur            x1, [fp, #-8]
    // 0x8c4d40: mov             x5, x0
    // 0x8c4d44: r2 = StreamCipher
    //     0x8c4d44: add             x2, PP, #0x18, lsl #12  ; [pp+0x18048] Type: StreamCipher
    //     0x8c4d48: ldr             x2, [x2, #0x48]
    // 0x8c4d4c: r3 = "/SIC"
    //     0x8c4d4c: add             x3, PP, #0x18, lsl #12  ; [pp+0x180d8] "/SIC"
    //     0x8c4d50: ldr             x3, [x3, #0xd8]
    // 0x8c4d54: r0 = DynamicFactoryConfig.suffix()
    //     0x8c4d54: bl              #0x8c3e1c  ; [package:pointycastle/src/registry/registry.dart] DynamicFactoryConfig::DynamicFactoryConfig.suffix
    // 0x8c4d58: ldur            x0, [fp, #-8]
    // 0x8c4d5c: LeaveFrame
    //     0x8c4d5c: mov             SP, fp
    //     0x8c4d60: ldp             fp, lr, [SP], #0x10
    // 0x8c4d64: ret
    //     0x8c4d64: ret             
    // 0x8c4d68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4d68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c4d6c: b               #0x8c4d24
  }
  [closure] static (dynamic) => SICStreamCipher <anonymous closure>(dynamic, String, Match) {
    // ** addr: 0x8c4d70, size: 0x54
    // 0x8c4d70: EnterFrame
    //     0x8c4d70: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4d74: mov             fp, SP
    // 0x8c4d78: AllocStack(0x8)
    //     0x8c4d78: sub             SP, SP, #8
    // 0x8c4d7c: SetupParameters()
    //     0x8c4d7c: ldr             x0, [fp, #0x20]
    //     0x8c4d80: ldur            w1, [x0, #0x17]
    //     0x8c4d84: add             x1, x1, HEAP, lsl #32
    //     0x8c4d88: stur            x1, [fp, #-8]
    // 0x8c4d8c: r1 = 1
    //     0x8c4d8c: movz            x1, #0x1
    // 0x8c4d90: r0 = AllocateContext()
    //     0x8c4d90: bl              #0xec126c  ; AllocateContextStub
    // 0x8c4d94: mov             x1, x0
    // 0x8c4d98: ldur            x0, [fp, #-8]
    // 0x8c4d9c: StoreField: r1->field_b = r0
    //     0x8c4d9c: stur            w0, [x1, #0xb]
    // 0x8c4da0: ldr             x0, [fp, #0x10]
    // 0x8c4da4: StoreField: r1->field_f = r0
    //     0x8c4da4: stur            w0, [x1, #0xf]
    // 0x8c4da8: mov             x2, x1
    // 0x8c4dac: r1 = Function '<anonymous closure>': static.
    //     0x8c4dac: add             x1, PP, #0x18, lsl #12  ; [pp+0x180e0] AnonymousClosure: static (0x8c4dc4), in [package:pointycastle/stream/sic.dart] SICStreamCipher::factoryConfig (0x8c4d0c)
    //     0x8c4db0: ldr             x1, [x1, #0xe0]
    // 0x8c4db4: r0 = AllocateClosure()
    //     0x8c4db4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c4db8: LeaveFrame
    //     0x8c4db8: mov             SP, fp
    //     0x8c4dbc: ldp             fp, lr, [SP], #0x10
    // 0x8c4dc0: ret
    //     0x8c4dc0: ret             
  }
  [closure] static SICStreamCipher <anonymous closure>(dynamic) {
    // ** addr: 0x8c4dc4, size: 0xcc
    // 0x8c4dc4: EnterFrame
    //     0x8c4dc4: stp             fp, lr, [SP, #-0x10]!
    //     0x8c4dc8: mov             fp, SP
    // 0x8c4dcc: AllocStack(0x20)
    //     0x8c4dcc: sub             SP, SP, #0x20
    // 0x8c4dd0: SetupParameters()
    //     0x8c4dd0: ldr             x0, [fp, #0x10]
    //     0x8c4dd4: ldur            w1, [x0, #0x17]
    //     0x8c4dd8: add             x1, x1, HEAP, lsl #32
    // 0x8c4ddc: CheckStackOverflow
    //     0x8c4ddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c4de0: cmp             SP, x16
    //     0x8c4de4: b.ls            #0x8c4e84
    // 0x8c4de8: LoadField: r0 = r1->field_f
    //     0x8c4de8: ldur            w0, [x1, #0xf]
    // 0x8c4dec: DecompressPointer r0
    //     0x8c4dec: add             x0, x0, HEAP, lsl #32
    // 0x8c4df0: r1 = LoadClassIdInstr(r0)
    //     0x8c4df0: ldur            x1, [x0, #-1]
    //     0x8c4df4: ubfx            x1, x1, #0xc, #0x14
    // 0x8c4df8: mov             x16, x0
    // 0x8c4dfc: mov             x0, x1
    // 0x8c4e00: mov             x1, x16
    // 0x8c4e04: r2 = 1
    //     0x8c4e04: movz            x2, #0x1
    // 0x8c4e08: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x8c4e08: sub             lr, x0, #0xfdd
    //     0x8c4e0c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c4e10: blr             lr
    // 0x8c4e14: stur            x0, [fp, #-8]
    // 0x8c4e18: cmp             w0, NULL
    // 0x8c4e1c: b.eq            #0x8c4e8c
    // 0x8c4e20: r0 = InitLateStaticField(0x171c) // [package:pointycastle/src/registry/registry.dart] ::registry
    //     0x8c4e20: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c4e24: ldr             x0, [x0, #0x2e38]
    //     0x8c4e28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c4e2c: cmp             w0, w16
    //     0x8c4e30: b.ne            #0x8c4e40
    //     0x8c4e34: add             x2, PP, #0x17, lsl #12  ; [pp+0x17f80] Field <::.registry>: static late final (offset: 0x171c)
    //     0x8c4e38: ldr             x2, [x2, #0xf80]
    //     0x8c4e3c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c4e40: r16 = <BlockCipher>
    //     0x8c4e40: add             x16, PP, #0x18, lsl #12  ; [pp+0x18088] TypeArguments: <BlockCipher>
    //     0x8c4e44: ldr             x16, [x16, #0x88]
    // 0x8c4e48: stp             x0, x16, [SP, #8]
    // 0x8c4e4c: ldur            x16, [fp, #-8]
    // 0x8c4e50: str             x16, [SP]
    // 0x8c4e54: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c4e54: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c4e58: r0 = create()
    //     0x8c4e58: bl              #0x8c31fc  ; [package:pointycastle/src/registry/registry.dart] _RegistryImpl::create
    // 0x8c4e5c: stur            x0, [fp, #-8]
    // 0x8c4e60: r0 = SICStreamCipher()
    //     0x8c4e60: bl              #0x8c4e90  ; AllocateSICStreamCipherStub -> SICStreamCipher (size=0x1c)
    // 0x8c4e64: mov             x1, x0
    // 0x8c4e68: ldur            x2, [fp, #-8]
    // 0x8c4e6c: stur            x0, [fp, #-8]
    // 0x8c4e70: r0 = SICStreamCipher()
    //     0x8c4e70: bl              #0x8c4b0c  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::SICStreamCipher
    // 0x8c4e74: ldur            x0, [fp, #-8]
    // 0x8c4e78: LeaveFrame
    //     0x8c4e78: mov             SP, fp
    //     0x8c4e7c: ldp             fp, lr, [SP], #0x10
    // 0x8c4e80: ret
    //     0x8c4e80: ret             
    // 0x8c4e84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c4e84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c4e88: b               #0x8c4de8
    // 0x8c4e8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8c4e8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ reset(/* No info */) {
    // ** addr: 0xe7dbc8, size: 0x258
    // 0xe7dbc8: EnterFrame
    //     0xe7dbc8: stp             fp, lr, [SP, #-0x10]!
    //     0xe7dbcc: mov             fp, SP
    // 0xe7dbd0: AllocStack(0x28)
    //     0xe7dbd0: sub             SP, SP, #0x28
    // 0xe7dbd4: SetupParameters(SICStreamCipher this /* r1 => r2, fp-0x8 */)
    //     0xe7dbd4: mov             x2, x1
    //     0xe7dbd8: stur            x1, [fp, #-8]
    // 0xe7dbdc: CheckStackOverflow
    //     0xe7dbdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7dbe0: cmp             SP, x16
    //     0xe7dbe4: b.ls            #0xe7ddf4
    // 0xe7dbe8: LoadField: r1 = r2->field_7
    //     0xe7dbe8: ldur            w1, [x2, #7]
    // 0xe7dbec: DecompressPointer r1
    //     0xe7dbec: add             x1, x1, HEAP, lsl #32
    // 0xe7dbf0: r0 = LoadClassIdInstr(r1)
    //     0xe7dbf0: ldur            x0, [x1, #-1]
    //     0xe7dbf4: ubfx            x0, x0, #0xc, #0x14
    // 0xe7dbf8: r0 = GDT[cid_x0 + -0xeaf]()
    //     0xe7dbf8: sub             lr, x0, #0xeaf
    //     0xe7dbfc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7dc00: blr             lr
    // 0xe7dc04: ldur            x0, [fp, #-8]
    // 0xe7dc08: LoadField: r4 = r0->field_f
    //     0xe7dc08: ldur            w4, [x0, #0xf]
    // 0xe7dc0c: DecompressPointer r4
    //     0xe7dc0c: add             x4, x4, HEAP, lsl #32
    // 0xe7dc10: r16 = Sentinel
    //     0xe7dc10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7dc14: cmp             w4, w16
    // 0xe7dc18: b.eq            #0xe7ddfc
    // 0xe7dc1c: stur            x4, [fp, #-0x28]
    // 0xe7dc20: LoadField: r5 = r0->field_b
    //     0xe7dc20: ldur            w5, [x0, #0xb]
    // 0xe7dc24: DecompressPointer r5
    //     0xe7dc24: add             x5, x5, HEAP, lsl #32
    // 0xe7dc28: r16 = Sentinel
    //     0xe7dc28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7dc2c: cmp             w5, w16
    // 0xe7dc30: b.eq            #0xe7de08
    // 0xe7dc34: stur            x5, [fp, #-0x20]
    // 0xe7dc38: LoadField: r6 = r5->field_13
    //     0xe7dc38: ldur            w6, [x5, #0x13]
    // 0xe7dc3c: stur            x6, [fp, #-0x18]
    // 0xe7dc40: r7 = LoadInt32Instr(r6)
    //     0xe7dc40: sbfx            x7, x6, #1, #0x1f
    // 0xe7dc44: stur            x7, [fp, #-0x10]
    // 0xe7dc48: tbnz            x7, #0x3f, #0xe7dc5c
    // 0xe7dc4c: LoadField: r1 = r4->field_13
    //     0xe7dc4c: ldur            w1, [x4, #0x13]
    // 0xe7dc50: r2 = LoadInt32Instr(r1)
    //     0xe7dc50: sbfx            x2, x1, #1, #0x1f
    // 0xe7dc54: cmp             x7, x2
    // 0xe7dc58: b.le            #0xe7dc74
    // 0xe7dc5c: LoadField: r1 = r4->field_13
    //     0xe7dc5c: ldur            w1, [x4, #0x13]
    // 0xe7dc60: r3 = LoadInt32Instr(r1)
    //     0xe7dc60: sbfx            x3, x1, #1, #0x1f
    // 0xe7dc64: mov             x2, x6
    // 0xe7dc68: r1 = 0
    //     0xe7dc68: movz            x1, #0
    // 0xe7dc6c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe7dc6c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe7dc70: r0 = checkValidRange()
    //     0xe7dc70: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe7dc74: ldur            x2, [fp, #-0x10]
    // 0xe7dc78: cbz             x2, #0xe7dda4
    // 0xe7dc7c: ldur            x0, [fp, #-0x18]
    // 0xe7dc80: cmp             w0, #0x800
    // 0xe7dc84: b.ge            #0xe7dd58
    // 0xe7dc88: ldur            x1, [fp, #-0x28]
    // 0xe7dc8c: ldur            x3, [fp, #-0x20]
    // 0xe7dc90: mov             x4, x0
    // 0xe7dc94: add             x2, x3, #0x17
    // 0xe7dc98: add             x0, x1, #0x17
    // 0xe7dc9c: cbz             x4, #0xe7dd54
    // 0xe7dca0: cmp             x0, x2
    // 0xe7dca4: b.ls            #0xe7dd0c
    // 0xe7dca8: sxtw            x4, w4
    // 0xe7dcac: add             x16, x2, x4, asr #1
    // 0xe7dcb0: cmp             x0, x16
    // 0xe7dcb4: b.hs            #0xe7dd0c
    // 0xe7dcb8: mov             x2, x16
    // 0xe7dcbc: add             x0, x0, x4, asr #1
    // 0xe7dcc0: tbz             w4, #4, #0xe7dccc
    // 0xe7dcc4: ldr             x16, [x2, #-8]!
    // 0xe7dcc8: str             x16, [x0, #-8]!
    // 0xe7dccc: tbz             w4, #3, #0xe7dcd8
    // 0xe7dcd0: ldr             w16, [x2, #-4]!
    // 0xe7dcd4: str             w16, [x0, #-4]!
    // 0xe7dcd8: tbz             w4, #2, #0xe7dce4
    // 0xe7dcdc: ldrh            w16, [x2, #-2]!
    // 0xe7dce0: strh            w16, [x0, #-2]!
    // 0xe7dce4: tbz             w4, #1, #0xe7dcf0
    // 0xe7dce8: ldrb            w16, [x2, #-1]!
    // 0xe7dcec: strb            w16, [x0, #-1]!
    // 0xe7dcf0: ands            w4, w4, #0xffffffe1
    // 0xe7dcf4: b.eq            #0xe7dd54
    // 0xe7dcf8: ldp             x16, x17, [x2, #-0x10]!
    // 0xe7dcfc: stp             x16, x17, [x0, #-0x10]!
    // 0xe7dd00: subs            w4, w4, #0x20
    // 0xe7dd04: b.ne            #0xe7dcf8
    // 0xe7dd08: b               #0xe7dd54
    // 0xe7dd0c: tbz             w4, #4, #0xe7dd18
    // 0xe7dd10: ldr             x16, [x2], #8
    // 0xe7dd14: str             x16, [x0], #8
    // 0xe7dd18: tbz             w4, #3, #0xe7dd24
    // 0xe7dd1c: ldr             w16, [x2], #4
    // 0xe7dd20: str             w16, [x0], #4
    // 0xe7dd24: tbz             w4, #2, #0xe7dd30
    // 0xe7dd28: ldrh            w16, [x2], #2
    // 0xe7dd2c: strh            w16, [x0], #2
    // 0xe7dd30: tbz             w4, #1, #0xe7dd3c
    // 0xe7dd34: ldrb            w16, [x2], #1
    // 0xe7dd38: strb            w16, [x0], #1
    // 0xe7dd3c: ands            w4, w4, #0xffffffe1
    // 0xe7dd40: b.eq            #0xe7dd54
    // 0xe7dd44: ldp             x16, x17, [x2], #0x10
    // 0xe7dd48: stp             x16, x17, [x0], #0x10
    // 0xe7dd4c: subs            w4, w4, #0x20
    // 0xe7dd50: b.ne            #0xe7dd44
    // 0xe7dd54: b               #0xe7dda4
    // 0xe7dd58: ldur            x1, [fp, #-0x28]
    // 0xe7dd5c: ldur            x3, [fp, #-0x20]
    // 0xe7dd60: LoadField: r0 = r1->field_7
    //     0xe7dd60: ldur            x0, [x1, #7]
    // 0xe7dd64: LoadField: r1 = r3->field_7
    //     0xe7dd64: ldur            x1, [x3, #7]
    // 0xe7dd68: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe7dd68: mov             x3, THR
    //     0xe7dd6c: ldr             x9, [x3, #0x658]
    //     0xe7dd70: mov             x17, fp
    //     0xe7dd74: str             fp, [SP, #-8]!
    //     0xe7dd78: mov             fp, SP
    //     0xe7dd7c: and             SP, SP, #0xfffffffffffffff0
    //     0xe7dd80: mov             x19, sp
    //     0xe7dd84: mov             sp, SP
    //     0xe7dd88: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7dd8c: blr             x9
    //     0xe7dd90: movz            x16, #0x8
    //     0xe7dd94: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe7dd98: mov             sp, x19
    //     0xe7dd9c: mov             SP, fp
    //     0xe7dda0: ldr             fp, [SP], #8
    // 0xe7dda4: ldur            x0, [fp, #-8]
    // 0xe7dda8: LoadField: r1 = r0->field_13
    //     0xe7dda8: ldur            w1, [x0, #0x13]
    // 0xe7ddac: DecompressPointer r1
    //     0xe7ddac: add             x1, x1, HEAP, lsl #32
    // 0xe7ddb0: r16 = Sentinel
    //     0xe7ddb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7ddb4: cmp             w1, w16
    // 0xe7ddb8: b.eq            #0xe7de14
    // 0xe7ddbc: LoadField: r2 = r1->field_13
    //     0xe7ddbc: ldur            w2, [x1, #0x13]
    // 0xe7ddc0: r3 = LoadInt32Instr(r2)
    //     0xe7ddc0: sbfx            x3, x2, #1, #0x1f
    // 0xe7ddc4: r2 = 0
    //     0xe7ddc4: movz            x2, #0
    // 0xe7ddc8: r5 = 0
    //     0xe7ddc8: movz            x5, #0
    // 0xe7ddcc: r0 = fillRange()
    //     0xe7ddcc: bl              #0x6de658  ; [dart:typed_data] __Int8List&_TypedList&_IntListMixin::fillRange
    // 0xe7ddd0: ldur            x1, [fp, #-8]
    // 0xe7ddd4: LoadField: r2 = r1->field_13
    //     0xe7ddd4: ldur            w2, [x1, #0x13]
    // 0xe7ddd8: DecompressPointer r2
    //     0xe7ddd8: add             x2, x2, HEAP, lsl #32
    // 0xe7dddc: LoadField: r3 = r2->field_13
    //     0xe7dddc: ldur            w3, [x2, #0x13]
    // 0xe7dde0: ArrayStore: r1[0] = r3  ; List_4
    //     0xe7dde0: stur            w3, [x1, #0x17]
    // 0xe7dde4: r0 = Null
    //     0xe7dde4: mov             x0, NULL
    // 0xe7dde8: LeaveFrame
    //     0xe7dde8: mov             SP, fp
    //     0xe7ddec: ldp             fp, lr, [SP], #0x10
    // 0xe7ddf0: ret
    //     0xe7ddf0: ret             
    // 0xe7ddf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ddf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ddf8: b               #0xe7dbe8
    // 0xe7ddfc: r9 = _counter
    //     0xe7ddfc: add             x9, PP, #0x21, lsl #12  ; [pp+0x21e10] Field <SICStreamCipher._counter@966045914>: late (offset: 0x10)
    //     0xe7de00: ldr             x9, [x9, #0xe10]
    // 0xe7de04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7de04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe7de08: r9 = _iv
    //     0xe7de08: add             x9, PP, #0x21, lsl #12  ; [pp+0x21e08] Field <SICStreamCipher._iv@966045914>: late (offset: 0xc)
    //     0xe7de0c: ldr             x9, [x9, #0xe08]
    // 0xe7de10: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7de10: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe7de14: r9 = _counterOut
    //     0xe7de14: add             x9, PP, #0x21, lsl #12  ; [pp+0x21e18] Field <SICStreamCipher._counterOut@966045914>: late (offset: 0x14)
    //     0xe7de18: ldr             x9, [x9, #0xe18]
    // 0xe7de1c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7de1c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) {
    // ** addr: 0xe83000, size: 0x25c
    // 0xe83000: EnterFrame
    //     0xe83000: stp             fp, lr, [SP, #-0x10]!
    //     0xe83004: mov             fp, SP
    // 0xe83008: AllocStack(0x30)
    //     0xe83008: sub             SP, SP, #0x30
    // 0xe8300c: SetupParameters(SICStreamCipher this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xe8300c: mov             x4, x1
    //     0xe83010: mov             x3, x2
    //     0xe83014: stur            x1, [fp, #-8]
    //     0xe83018: stur            x2, [fp, #-0x10]
    // 0xe8301c: CheckStackOverflow
    //     0xe8301c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe83020: cmp             SP, x16
    //     0xe83024: b.ls            #0xe83248
    // 0xe83028: mov             x0, x3
    // 0xe8302c: r2 = Null
    //     0xe8302c: mov             x2, NULL
    // 0xe83030: r1 = Null
    //     0xe83030: mov             x1, NULL
    // 0xe83034: r4 = 60
    //     0xe83034: movz            x4, #0x3c
    // 0xe83038: branchIfSmi(r0, 0xe83044)
    //     0xe83038: tbz             w0, #0, #0xe83044
    // 0xe8303c: r4 = LoadClassIdInstr(r0)
    //     0xe8303c: ldur            x4, [x0, #-1]
    //     0xe83040: ubfx            x4, x4, #0xc, #0x14
    // 0xe83044: cmp             x4, #0x2a8
    // 0xe83048: b.eq            #0xe83060
    // 0xe8304c: r8 = ParametersWithIV<CipherParameters?>
    //     0xe8304c: add             x8, PP, #0x21, lsl #12  ; [pp+0x21ce8] Type: ParametersWithIV<CipherParameters?>
    //     0xe83050: ldr             x8, [x8, #0xce8]
    // 0xe83054: r3 = Null
    //     0xe83054: add             x3, PP, #0x21, lsl #12  ; [pp+0x21df8] Null
    //     0xe83058: ldr             x3, [x3, #0xdf8]
    // 0xe8305c: r0 = DefaultTypeTest()
    //     0xe8305c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xe83060: ldur            x0, [fp, #-8]
    // 0xe83064: LoadField: r4 = r0->field_b
    //     0xe83064: ldur            w4, [x0, #0xb]
    // 0xe83068: DecompressPointer r4
    //     0xe83068: add             x4, x4, HEAP, lsl #32
    // 0xe8306c: r16 = Sentinel
    //     0xe8306c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe83070: cmp             w4, w16
    // 0xe83074: b.eq            #0xe83250
    // 0xe83078: ldur            x5, [fp, #-0x10]
    // 0xe8307c: stur            x4, [fp, #-0x30]
    // 0xe83080: LoadField: r6 = r5->field_b
    //     0xe83080: ldur            w6, [x5, #0xb]
    // 0xe83084: DecompressPointer r6
    //     0xe83084: add             x6, x6, HEAP, lsl #32
    // 0xe83088: stur            x6, [fp, #-0x28]
    // 0xe8308c: LoadField: r7 = r6->field_13
    //     0xe8308c: ldur            w7, [x6, #0x13]
    // 0xe83090: stur            x7, [fp, #-0x20]
    // 0xe83094: r8 = LoadInt32Instr(r7)
    //     0xe83094: sbfx            x8, x7, #1, #0x1f
    // 0xe83098: stur            x8, [fp, #-0x18]
    // 0xe8309c: tbnz            x8, #0x3f, #0xe830b0
    // 0xe830a0: LoadField: r1 = r4->field_13
    //     0xe830a0: ldur            w1, [x4, #0x13]
    // 0xe830a4: r2 = LoadInt32Instr(r1)
    //     0xe830a4: sbfx            x2, x1, #1, #0x1f
    // 0xe830a8: cmp             x8, x2
    // 0xe830ac: b.le            #0xe830c8
    // 0xe830b0: LoadField: r1 = r4->field_13
    //     0xe830b0: ldur            w1, [x4, #0x13]
    // 0xe830b4: r3 = LoadInt32Instr(r1)
    //     0xe830b4: sbfx            x3, x1, #1, #0x1f
    // 0xe830b8: mov             x2, x7
    // 0xe830bc: r1 = 0
    //     0xe830bc: movz            x1, #0
    // 0xe830c0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe830c0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe830c4: r0 = checkValidRange()
    //     0xe830c4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xe830c8: ldur            x2, [fp, #-0x18]
    // 0xe830cc: cbz             x2, #0xe831f8
    // 0xe830d0: ldur            x0, [fp, #-0x20]
    // 0xe830d4: cmp             w0, #0x800
    // 0xe830d8: b.ge            #0xe831ac
    // 0xe830dc: ldur            x3, [fp, #-0x28]
    // 0xe830e0: ldur            x1, [fp, #-0x30]
    // 0xe830e4: mov             x4, x0
    // 0xe830e8: add             x2, x3, #0x17
    // 0xe830ec: add             x0, x1, #0x17
    // 0xe830f0: cbz             x4, #0xe831a8
    // 0xe830f4: cmp             x0, x2
    // 0xe830f8: b.ls            #0xe83160
    // 0xe830fc: sxtw            x4, w4
    // 0xe83100: add             x16, x2, x4, asr #1
    // 0xe83104: cmp             x0, x16
    // 0xe83108: b.hs            #0xe83160
    // 0xe8310c: mov             x2, x16
    // 0xe83110: add             x0, x0, x4, asr #1
    // 0xe83114: tbz             w4, #4, #0xe83120
    // 0xe83118: ldr             x16, [x2, #-8]!
    // 0xe8311c: str             x16, [x0, #-8]!
    // 0xe83120: tbz             w4, #3, #0xe8312c
    // 0xe83124: ldr             w16, [x2, #-4]!
    // 0xe83128: str             w16, [x0, #-4]!
    // 0xe8312c: tbz             w4, #2, #0xe83138
    // 0xe83130: ldrh            w16, [x2, #-2]!
    // 0xe83134: strh            w16, [x0, #-2]!
    // 0xe83138: tbz             w4, #1, #0xe83144
    // 0xe8313c: ldrb            w16, [x2, #-1]!
    // 0xe83140: strb            w16, [x0, #-1]!
    // 0xe83144: ands            w4, w4, #0xffffffe1
    // 0xe83148: b.eq            #0xe831a8
    // 0xe8314c: ldp             x16, x17, [x2, #-0x10]!
    // 0xe83150: stp             x16, x17, [x0, #-0x10]!
    // 0xe83154: subs            w4, w4, #0x20
    // 0xe83158: b.ne            #0xe8314c
    // 0xe8315c: b               #0xe831a8
    // 0xe83160: tbz             w4, #4, #0xe8316c
    // 0xe83164: ldr             x16, [x2], #8
    // 0xe83168: str             x16, [x0], #8
    // 0xe8316c: tbz             w4, #3, #0xe83178
    // 0xe83170: ldr             w16, [x2], #4
    // 0xe83174: str             w16, [x0], #4
    // 0xe83178: tbz             w4, #2, #0xe83184
    // 0xe8317c: ldrh            w16, [x2], #2
    // 0xe83180: strh            w16, [x0], #2
    // 0xe83184: tbz             w4, #1, #0xe83190
    // 0xe83188: ldrb            w16, [x2], #1
    // 0xe8318c: strb            w16, [x0], #1
    // 0xe83190: ands            w4, w4, #0xffffffe1
    // 0xe83194: b.eq            #0xe831a8
    // 0xe83198: ldp             x16, x17, [x2], #0x10
    // 0xe8319c: stp             x16, x17, [x0], #0x10
    // 0xe831a0: subs            w4, w4, #0x20
    // 0xe831a4: b.ne            #0xe83198
    // 0xe831a8: b               #0xe831f8
    // 0xe831ac: ldur            x3, [fp, #-0x28]
    // 0xe831b0: ldur            x1, [fp, #-0x30]
    // 0xe831b4: LoadField: r0 = r1->field_7
    //     0xe831b4: ldur            x0, [x1, #7]
    // 0xe831b8: LoadField: r1 = r3->field_7
    //     0xe831b8: ldur            x1, [x3, #7]
    // 0xe831bc: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xe831bc: mov             x3, THR
    //     0xe831c0: ldr             x9, [x3, #0x658]
    //     0xe831c4: mov             x17, fp
    //     0xe831c8: str             fp, [SP, #-8]!
    //     0xe831cc: mov             fp, SP
    //     0xe831d0: and             SP, SP, #0xfffffffffffffff0
    //     0xe831d4: mov             x19, sp
    //     0xe831d8: mov             sp, SP
    //     0xe831dc: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe831e0: blr             x9
    //     0xe831e4: movz            x16, #0x8
    //     0xe831e8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xe831ec: mov             sp, x19
    //     0xe831f0: mov             SP, fp
    //     0xe831f4: ldr             fp, [SP], #8
    // 0xe831f8: ldur            x0, [fp, #-8]
    // 0xe831fc: ldur            x2, [fp, #-0x10]
    // 0xe83200: mov             x1, x0
    // 0xe83204: r0 = reset()
    //     0xe83204: bl              #0xe7dbc8  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::reset
    // 0xe83208: ldur            x0, [fp, #-8]
    // 0xe8320c: LoadField: r1 = r0->field_7
    //     0xe8320c: ldur            w1, [x0, #7]
    // 0xe83210: DecompressPointer r1
    //     0xe83210: add             x1, x1, HEAP, lsl #32
    // 0xe83214: ldur            x0, [fp, #-0x10]
    // 0xe83218: LoadField: r3 = r0->field_f
    //     0xe83218: ldur            w3, [x0, #0xf]
    // 0xe8321c: DecompressPointer r3
    //     0xe8321c: add             x3, x3, HEAP, lsl #32
    // 0xe83220: r0 = LoadClassIdInstr(r1)
    //     0xe83220: ldur            x0, [x1, #-1]
    //     0xe83224: ubfx            x0, x0, #0xc, #0x14
    // 0xe83228: r2 = true
    //     0xe83228: add             x2, NULL, #0x20  ; true
    // 0xe8322c: r0 = GDT[cid_x0 + -0xeda]()
    //     0xe8322c: sub             lr, x0, #0xeda
    //     0xe83230: ldr             lr, [x21, lr, lsl #3]
    //     0xe83234: blr             lr
    // 0xe83238: r0 = Null
    //     0xe83238: mov             x0, NULL
    // 0xe8323c: LeaveFrame
    //     0xe8323c: mov             SP, fp
    //     0xe83240: ldp             fp, lr, [SP], #0x10
    // 0xe83244: ret
    //     0xe83244: ret             
    // 0xe83248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe83248: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8324c: b               #0xe83028
    // 0xe83250: r9 = _iv
    //     0xe83250: add             x9, PP, #0x21, lsl #12  ; [pp+0x21e08] Field <SICStreamCipher._iv@966045914>: late (offset: 0xc)
    //     0xe83254: ldr             x9, [x9, #0xe08]
    // 0xe83258: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe83258: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ processBytes(/* No info */) {
    // ** addr: 0xea1bdc, size: 0x1cc
    // 0xea1bdc: EnterFrame
    //     0xea1bdc: stp             fp, lr, [SP, #-0x10]!
    //     0xea1be0: mov             fp, SP
    // 0xea1be4: AllocStack(0x58)
    //     0xea1be4: sub             SP, SP, #0x58
    // 0xea1be8: SetupParameters(SICStreamCipher this /* r1 => r4, fp-0x30 */, dynamic _ /* r2 => r2, fp-0x38 */, dynamic _ /* r3 => r3, fp-0x40 */, dynamic _ /* r5 => r5, fp-0x48 */, dynamic _ /* r6 => r6, fp-0x50 */, dynamic _ /* r7 => r7, fp-0x58 */)
    //     0xea1be8: mov             x4, x1
    //     0xea1bec: stur            x1, [fp, #-0x30]
    //     0xea1bf0: stur            x2, [fp, #-0x38]
    //     0xea1bf4: stur            x3, [fp, #-0x40]
    //     0xea1bf8: stur            x5, [fp, #-0x48]
    //     0xea1bfc: stur            x6, [fp, #-0x50]
    //     0xea1c00: stur            x7, [fp, #-0x58]
    // 0xea1c04: CheckStackOverflow
    //     0xea1c04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1c08: cmp             SP, x16
    //     0xea1c0c: b.ls            #0xea1d74
    // 0xea1c10: LoadField: r0 = r2->field_13
    //     0xea1c10: ldur            w0, [x2, #0x13]
    // 0xea1c14: r8 = LoadInt32Instr(r0)
    //     0xea1c14: sbfx            x8, x0, #1, #0x1f
    // 0xea1c18: stur            x8, [fp, #-0x28]
    // 0xea1c1c: LoadField: r0 = r6->field_13
    //     0xea1c1c: ldur            w0, [x6, #0x13]
    // 0xea1c20: r9 = LoadInt32Instr(r0)
    //     0xea1c20: sbfx            x9, x0, #1, #0x1f
    // 0xea1c24: stur            x9, [fp, #-0x20]
    // 0xea1c28: r10 = 0
    //     0xea1c28: movz            x10, #0
    // 0xea1c2c: stur            x10, [fp, #-0x18]
    // 0xea1c30: CheckStackOverflow
    //     0xea1c30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1c34: cmp             SP, x16
    //     0xea1c38: b.ls            #0xea1d7c
    // 0xea1c3c: cmp             x10, x5
    // 0xea1c40: b.ge            #0xea1d64
    // 0xea1c44: add             x11, x7, x10
    // 0xea1c48: stur            x11, [fp, #-0x10]
    // 0xea1c4c: add             x12, x3, x10
    // 0xea1c50: mov             x0, x8
    // 0xea1c54: mov             x1, x12
    // 0xea1c58: cmp             x1, x0
    // 0xea1c5c: b.hs            #0xea1d84
    // 0xea1c60: ArrayLoad: r0 = r2[r12]  ; List_1
    //     0xea1c60: add             x16, x2, x12
    //     0xea1c64: ldrb            w0, [x16, #0x17]
    // 0xea1c68: mov             x1, x4
    // 0xea1c6c: stur            x0, [fp, #-8]
    // 0xea1c70: r0 = _feedCounterIfNeeded()
    //     0xea1c70: bl              #0xea1da8  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::_feedCounterIfNeeded
    // 0xea1c74: ldur            x2, [fp, #-8]
    // 0xea1c78: ubfx            x2, x2, #0, #0x20
    // 0xea1c7c: r3 = 255
    //     0xea1c7c: movz            x3, #0xff
    // 0xea1c80: and             x4, x2, x3
    // 0xea1c84: ldur            x2, [fp, #-0x30]
    // 0xea1c88: LoadField: r5 = r2->field_13
    //     0xea1c88: ldur            w5, [x2, #0x13]
    // 0xea1c8c: DecompressPointer r5
    //     0xea1c8c: add             x5, x5, HEAP, lsl #32
    // 0xea1c90: r16 = Sentinel
    //     0xea1c90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea1c94: cmp             w5, w16
    // 0xea1c98: b.eq            #0xea1d88
    // 0xea1c9c: ArrayLoad: r6 = r2[0]  ; List_4
    //     0xea1c9c: ldur            w6, [x2, #0x17]
    // 0xea1ca0: DecompressPointer r6
    //     0xea1ca0: add             x6, x6, HEAP, lsl #32
    // 0xea1ca4: r16 = Sentinel
    //     0xea1ca4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea1ca8: cmp             w6, w16
    // 0xea1cac: b.eq            #0xea1d94
    // 0xea1cb0: r7 = LoadInt32Instr(r6)
    //     0xea1cb0: sbfx            x7, x6, #1, #0x1f
    //     0xea1cb4: tbz             w6, #0, #0xea1cbc
    //     0xea1cb8: ldur            x7, [x6, #7]
    // 0xea1cbc: add             x6, x7, #1
    // 0xea1cc0: r0 = BoxInt64Instr(r6)
    //     0xea1cc0: sbfiz           x0, x6, #1, #0x1f
    //     0xea1cc4: cmp             x6, x0, asr #1
    //     0xea1cc8: b.eq            #0xea1cd4
    //     0xea1ccc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea1cd0: stur            x6, [x0, #7]
    // 0xea1cd4: ArrayStore: r2[0] = r0  ; List_4
    //     0xea1cd4: stur            w0, [x2, #0x17]
    //     0xea1cd8: tbz             w0, #0, #0xea1cf4
    //     0xea1cdc: ldurb           w16, [x2, #-1]
    //     0xea1ce0: ldurb           w17, [x0, #-1]
    //     0xea1ce4: and             x16, x17, x16, lsr #2
    //     0xea1ce8: tst             x16, HEAP, lsr #32
    //     0xea1cec: b.eq            #0xea1cf4
    //     0xea1cf0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xea1cf4: LoadField: r6 = r5->field_13
    //     0xea1cf4: ldur            w6, [x5, #0x13]
    // 0xea1cf8: r0 = LoadInt32Instr(r6)
    //     0xea1cf8: sbfx            x0, x6, #1, #0x1f
    // 0xea1cfc: mov             x1, x7
    // 0xea1d00: cmp             x1, x0
    // 0xea1d04: b.hs            #0xea1da0
    // 0xea1d08: ArrayLoad: r6 = r5[r7]  ; List_1
    //     0xea1d08: add             x16, x5, x7
    //     0xea1d0c: ldrb            w6, [x16, #0x17]
    // 0xea1d10: ubfx            x4, x4, #0, #0x20
    // 0xea1d14: eor             x5, x4, x6
    // 0xea1d18: ldur            x0, [fp, #-0x20]
    // 0xea1d1c: ldur            x1, [fp, #-0x10]
    // 0xea1d20: cmp             x1, x0
    // 0xea1d24: b.hs            #0xea1da4
    // 0xea1d28: ldur            x1, [fp, #-0x50]
    // 0xea1d2c: ldur            x4, [fp, #-0x10]
    // 0xea1d30: ArrayStore: r1[r4] = r5  ; TypeUnknown_1
    //     0xea1d30: add             x6, x1, x4
    //     0xea1d34: strb            w5, [x6, #0x17]
    // 0xea1d38: ldur            x4, [fp, #-0x18]
    // 0xea1d3c: add             x10, x4, #1
    // 0xea1d40: mov             x4, x2
    // 0xea1d44: ldur            x2, [fp, #-0x38]
    // 0xea1d48: ldur            x3, [fp, #-0x40]
    // 0xea1d4c: ldur            x5, [fp, #-0x48]
    // 0xea1d50: mov             x6, x1
    // 0xea1d54: ldur            x7, [fp, #-0x58]
    // 0xea1d58: ldur            x8, [fp, #-0x28]
    // 0xea1d5c: ldur            x9, [fp, #-0x20]
    // 0xea1d60: b               #0xea1c2c
    // 0xea1d64: r0 = Null
    //     0xea1d64: mov             x0, NULL
    // 0xea1d68: LeaveFrame
    //     0xea1d68: mov             SP, fp
    //     0xea1d6c: ldp             fp, lr, [SP], #0x10
    // 0xea1d70: ret
    //     0xea1d70: ret             
    // 0xea1d74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1d74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1d78: b               #0xea1c10
    // 0xea1d7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1d7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1d80: b               #0xea1c3c
    // 0xea1d84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea1d84: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea1d88: r9 = _counterOut
    //     0xea1d88: add             x9, PP, #0x21, lsl #12  ; [pp+0x21e18] Field <SICStreamCipher._counterOut@966045914>: late (offset: 0x14)
    //     0xea1d8c: ldr             x9, [x9, #0xe18]
    // 0xea1d90: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea1d90: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea1d94: r9 = _consumed
    //     0xea1d94: add             x9, PP, #0x23, lsl #12  ; [pp+0x237a0] Field <SICStreamCipher._consumed@966045914>: late (offset: 0x18)
    //     0xea1d98: ldr             x9, [x9, #0x7a0]
    // 0xea1d9c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea1d9c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea1da0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea1da0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xea1da4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xea1da4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _feedCounterIfNeeded(/* No info */) {
    // ** addr: 0xea1da8, size: 0x8c
    // 0xea1da8: EnterFrame
    //     0xea1da8: stp             fp, lr, [SP, #-0x10]!
    //     0xea1dac: mov             fp, SP
    // 0xea1db0: CheckStackOverflow
    //     0xea1db0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1db4: cmp             SP, x16
    //     0xea1db8: b.ls            #0xea1e14
    // 0xea1dbc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xea1dbc: ldur            w0, [x1, #0x17]
    // 0xea1dc0: DecompressPointer r0
    //     0xea1dc0: add             x0, x0, HEAP, lsl #32
    // 0xea1dc4: r16 = Sentinel
    //     0xea1dc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea1dc8: cmp             w0, w16
    // 0xea1dcc: b.eq            #0xea1e1c
    // 0xea1dd0: LoadField: r2 = r1->field_13
    //     0xea1dd0: ldur            w2, [x1, #0x13]
    // 0xea1dd4: DecompressPointer r2
    //     0xea1dd4: add             x2, x2, HEAP, lsl #32
    // 0xea1dd8: r16 = Sentinel
    //     0xea1dd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea1ddc: cmp             w2, w16
    // 0xea1de0: b.eq            #0xea1e28
    // 0xea1de4: LoadField: r3 = r2->field_13
    //     0xea1de4: ldur            w3, [x2, #0x13]
    // 0xea1de8: r2 = LoadInt32Instr(r0)
    //     0xea1de8: sbfx            x2, x0, #1, #0x1f
    //     0xea1dec: tbz             w0, #0, #0xea1df4
    //     0xea1df0: ldur            x2, [x0, #7]
    // 0xea1df4: r0 = LoadInt32Instr(r3)
    //     0xea1df4: sbfx            x0, x3, #1, #0x1f
    // 0xea1df8: cmp             x2, x0
    // 0xea1dfc: b.lt            #0xea1e04
    // 0xea1e00: r0 = _feedCounter()
    //     0xea1e00: bl              #0xea1e34  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::_feedCounter
    // 0xea1e04: r0 = Null
    //     0xea1e04: mov             x0, NULL
    // 0xea1e08: LeaveFrame
    //     0xea1e08: mov             SP, fp
    //     0xea1e0c: ldp             fp, lr, [SP], #0x10
    // 0xea1e10: ret
    //     0xea1e10: ret             
    // 0xea1e14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1e14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1e18: b               #0xea1dbc
    // 0xea1e1c: r9 = _consumed
    //     0xea1e1c: add             x9, PP, #0x23, lsl #12  ; [pp+0x237a0] Field <SICStreamCipher._consumed@966045914>: late (offset: 0x18)
    //     0xea1e20: ldr             x9, [x9, #0x7a0]
    // 0xea1e24: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea1e24: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea1e28: r9 = _counterOut
    //     0xea1e28: add             x9, PP, #0x21, lsl #12  ; [pp+0x21e18] Field <SICStreamCipher._counterOut@966045914>: late (offset: 0x14)
    //     0xea1e2c: ldr             x9, [x9, #0xe18]
    // 0xea1e30: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea1e30: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _feedCounter(/* No info */) {
    // ** addr: 0xea1e34, size: 0xac
    // 0xea1e34: EnterFrame
    //     0xea1e34: stp             fp, lr, [SP, #-0x10]!
    //     0xea1e38: mov             fp, SP
    // 0xea1e3c: AllocStack(0x8)
    //     0xea1e3c: sub             SP, SP, #8
    // 0xea1e40: SetupParameters(SICStreamCipher this /* r1 => r4, fp-0x8 */)
    //     0xea1e40: mov             x4, x1
    //     0xea1e44: stur            x1, [fp, #-8]
    // 0xea1e48: CheckStackOverflow
    //     0xea1e48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1e4c: cmp             SP, x16
    //     0xea1e50: b.ls            #0xea1ec0
    // 0xea1e54: LoadField: r1 = r4->field_7
    //     0xea1e54: ldur            w1, [x4, #7]
    // 0xea1e58: DecompressPointer r1
    //     0xea1e58: add             x1, x1, HEAP, lsl #32
    // 0xea1e5c: LoadField: r2 = r4->field_f
    //     0xea1e5c: ldur            w2, [x4, #0xf]
    // 0xea1e60: DecompressPointer r2
    //     0xea1e60: add             x2, x2, HEAP, lsl #32
    // 0xea1e64: r16 = Sentinel
    //     0xea1e64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea1e68: cmp             w2, w16
    // 0xea1e6c: b.eq            #0xea1ec8
    // 0xea1e70: LoadField: r5 = r4->field_13
    //     0xea1e70: ldur            w5, [x4, #0x13]
    // 0xea1e74: DecompressPointer r5
    //     0xea1e74: add             x5, x5, HEAP, lsl #32
    // 0xea1e78: r16 = Sentinel
    //     0xea1e78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea1e7c: cmp             w5, w16
    // 0xea1e80: b.eq            #0xea1ed4
    // 0xea1e84: r0 = LoadClassIdInstr(r1)
    //     0xea1e84: ldur            x0, [x1, #-1]
    //     0xea1e88: ubfx            x0, x0, #0xc, #0x14
    // 0xea1e8c: r3 = 0
    //     0xea1e8c: movz            x3, #0
    // 0xea1e90: r6 = 0
    //     0xea1e90: movz            x6, #0
    // 0xea1e94: r0 = GDT[cid_x0 + -0xf69]()
    //     0xea1e94: sub             lr, x0, #0xf69
    //     0xea1e98: ldr             lr, [x21, lr, lsl #3]
    //     0xea1e9c: blr             lr
    // 0xea1ea0: ldur            x1, [fp, #-8]
    // 0xea1ea4: r0 = _incrementCounter()
    //     0xea1ea4: bl              #0xea1ee0  ; [package:pointycastle/stream/sic.dart] SICStreamCipher::_incrementCounter
    // 0xea1ea8: ldur            x1, [fp, #-8]
    // 0xea1eac: ArrayStore: r1[0] = rZR  ; List_4
    //     0xea1eac: stur            wzr, [x1, #0x17]
    // 0xea1eb0: r0 = Null
    //     0xea1eb0: mov             x0, NULL
    // 0xea1eb4: LeaveFrame
    //     0xea1eb4: mov             SP, fp
    //     0xea1eb8: ldp             fp, lr, [SP], #0x10
    // 0xea1ebc: ret
    //     0xea1ebc: ret             
    // 0xea1ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1ec0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1ec4: b               #0xea1e54
    // 0xea1ec8: r9 = _counter
    //     0xea1ec8: add             x9, PP, #0x21, lsl #12  ; [pp+0x21e10] Field <SICStreamCipher._counter@966045914>: late (offset: 0x10)
    //     0xea1ecc: ldr             x9, [x9, #0xe10]
    // 0xea1ed0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea1ed0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea1ed4: r9 = _counterOut
    //     0xea1ed4: add             x9, PP, #0x21, lsl #12  ; [pp+0x21e18] Field <SICStreamCipher._counterOut@966045914>: late (offset: 0x14)
    //     0xea1ed8: ldr             x9, [x9, #0xe18]
    // 0xea1edc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea1edc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _incrementCounter(/* No info */) {
    // ** addr: 0xea1ee0, size: 0x88
    // 0xea1ee0: EnterFrame
    //     0xea1ee0: stp             fp, lr, [SP, #-0x10]!
    //     0xea1ee4: mov             fp, SP
    // 0xea1ee8: LoadField: r2 = r1->field_f
    //     0xea1ee8: ldur            w2, [x1, #0xf]
    // 0xea1eec: DecompressPointer r2
    //     0xea1eec: add             x2, x2, HEAP, lsl #32
    // 0xea1ef0: r16 = Sentinel
    //     0xea1ef0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xea1ef4: cmp             w2, w16
    // 0xea1ef8: b.eq            #0xea1f54
    // 0xea1efc: LoadField: r1 = r2->field_13
    //     0xea1efc: ldur            w1, [x2, #0x13]
    // 0xea1f00: r3 = LoadInt32Instr(r1)
    //     0xea1f00: sbfx            x3, x1, #1, #0x1f
    // 0xea1f04: sub             x1, x3, #1
    // 0xea1f08: CheckStackOverflow
    //     0xea1f08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea1f0c: cmp             SP, x16
    //     0xea1f10: b.ls            #0xea1f60
    // 0xea1f14: tbnz            x1, #0x3f, #0xea1f44
    // 0xea1f18: ArrayLoad: r3 = r2[r1]  ; List_1
    //     0xea1f18: add             x16, x2, x1
    //     0xea1f1c: ldrb            w3, [x16, #0x17]
    // 0xea1f20: add             x4, x3, #1
    // 0xea1f24: ArrayStore: r2[r1] = r4  ; TypeUnknown_1
    //     0xea1f24: add             x3, x2, x1
    //     0xea1f28: strb            w4, [x3, #0x17]
    // 0xea1f2c: ArrayLoad: r3 = r2[r1]  ; List_1
    //     0xea1f2c: add             x16, x2, x1
    //     0xea1f30: ldrb            w3, [x16, #0x17]
    // 0xea1f34: cbnz            x3, #0xea1f44
    // 0xea1f38: sub             x0, x1, #1
    // 0xea1f3c: mov             x1, x0
    // 0xea1f40: b               #0xea1f08
    // 0xea1f44: r0 = Null
    //     0xea1f44: mov             x0, NULL
    // 0xea1f48: LeaveFrame
    //     0xea1f48: mov             SP, fp
    //     0xea1f4c: ldp             fp, lr, [SP], #0x10
    // 0xea1f50: ret
    //     0xea1f50: ret             
    // 0xea1f54: r9 = _counter
    //     0xea1f54: add             x9, PP, #0x21, lsl #12  ; [pp+0x21e10] Field <SICStreamCipher._counter@966045914>: late (offset: 0x10)
    //     0xea1f58: ldr             x9, [x9, #0xe10]
    // 0xea1f5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xea1f5c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xea1f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea1f60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea1f64: b               #0xea1f14
  }
}
