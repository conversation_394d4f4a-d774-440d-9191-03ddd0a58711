// lib: , url: package:qr/src/bit_buffer.dart

// class id: 1051062, size: 0x8
class :: {
}

// class id: 5813, size: 0x14, field offset: 0x8
class QrBitBuffer extends _BoolList&Object&ListMixin {

  bool [](QrBitBuffer, int) {
    // ** addr: 0x66c644, size: 0xf8
    // 0x66c644: EnterFrame
    //     0x66c644: stp             fp, lr, [SP, #-0x10]!
    //     0x66c648: mov             fp, SP
    // 0x66c64c: ldr             x0, [fp, #0x10]
    // 0x66c650: r2 = Null
    //     0x66c650: mov             x2, NULL
    // 0x66c654: r1 = Null
    //     0x66c654: mov             x1, NULL
    // 0x66c658: branchIfSmi(r0, 0x66c680)
    //     0x66c658: tbz             w0, #0, #0x66c680
    // 0x66c65c: r4 = LoadClassIdInstr(r0)
    //     0x66c65c: ldur            x4, [x0, #-1]
    //     0x66c660: ubfx            x4, x4, #0xc, #0x14
    // 0x66c664: sub             x4, x4, #0x3c
    // 0x66c668: cmp             x4, #1
    // 0x66c66c: b.ls            #0x66c680
    // 0x66c670: r8 = int
    //     0x66c670: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x66c674: r3 = Null
    //     0x66c674: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b220] Null
    //     0x66c678: ldr             x3, [x3, #0x220]
    // 0x66c67c: r0 = int()
    //     0x66c67c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x66c680: ldr             x2, [fp, #0x10]
    // 0x66c684: r3 = LoadInt32Instr(r2)
    //     0x66c684: sbfx            x3, x2, #1, #0x1f
    //     0x66c688: tbz             w2, #0, #0x66c690
    //     0x66c68c: ldur            x3, [x2, #7]
    // 0x66c690: r2 = 8
    //     0x66c690: movz            x2, #0x8
    // 0x66c694: sdiv            x4, x3, x2
    // 0x66c698: ldr             x2, [fp, #0x18]
    // 0x66c69c: LoadField: r5 = r2->field_7
    //     0x66c69c: ldur            w5, [x2, #7]
    // 0x66c6a0: DecompressPointer r5
    //     0x66c6a0: add             x5, x5, HEAP, lsl #32
    // 0x66c6a4: LoadField: r2 = r5->field_b
    //     0x66c6a4: ldur            w2, [x5, #0xb]
    // 0x66c6a8: r0 = LoadInt32Instr(r2)
    //     0x66c6a8: sbfx            x0, x2, #1, #0x1f
    // 0x66c6ac: mov             x1, x4
    // 0x66c6b0: cmp             x1, x0
    // 0x66c6b4: b.hs            #0x66c720
    // 0x66c6b8: LoadField: r1 = r5->field_f
    //     0x66c6b8: ldur            w1, [x5, #0xf]
    // 0x66c6bc: DecompressPointer r1
    //     0x66c6bc: add             x1, x1, HEAP, lsl #32
    // 0x66c6c0: ArrayLoad: r2 = r1[r4]  ; Unknown_4
    //     0x66c6c0: add             x16, x1, x4, lsl #2
    //     0x66c6c4: ldur            w2, [x16, #0xf]
    // 0x66c6c8: DecompressPointer r2
    //     0x66c6c8: add             x2, x2, HEAP, lsl #32
    // 0x66c6cc: ubfx            x3, x3, #0, #0x20
    // 0x66c6d0: r1 = 7
    //     0x66c6d0: movz            x1, #0x7
    // 0x66c6d4: and             x4, x3, x1
    // 0x66c6d8: ubfx            x4, x4, #0, #0x20
    // 0x66c6dc: r1 = 7
    //     0x66c6dc: movz            x1, #0x7
    // 0x66c6e0: sub             x3, x1, x4
    // 0x66c6e4: r1 = LoadInt32Instr(r2)
    //     0x66c6e4: sbfx            x1, x2, #1, #0x1f
    //     0x66c6e8: tbz             w2, #0, #0x66c6f0
    //     0x66c6ec: ldur            x1, [x2, #7]
    // 0x66c6f0: asr             x2, x1, x3
    // 0x66c6f4: ubfx            x2, x2, #0, #0x20
    // 0x66c6f8: r1 = 1
    //     0x66c6f8: movz            x1, #0x1
    // 0x66c6fc: and             x3, x2, x1
    // 0x66c700: ubfx            x3, x3, #0, #0x20
    // 0x66c704: cmp             x3, #1
    // 0x66c708: r16 = true
    //     0x66c708: add             x16, NULL, #0x20  ; true
    // 0x66c70c: r17 = false
    //     0x66c70c: add             x17, NULL, #0x30  ; false
    // 0x66c710: csel            x0, x16, x17, eq
    // 0x66c714: LeaveFrame
    //     0x66c714: mov             SP, fp
    //     0x66c718: ldp             fp, lr, [SP], #0x10
    // 0x66c71c: ret
    //     0x66c71c: ret             
    // 0x66c720: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x66c720: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  set _ length=(/* No info */) {
    // ** addr: 0x6715a0, size: 0x28
    // 0x6715a0: EnterFrame
    //     0x6715a0: stp             fp, lr, [SP, #-0x10]!
    //     0x6715a4: mov             fp, SP
    // 0x6715a8: r0 = UnsupportedError()
    //     0x6715a8: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x6715ac: mov             x1, x0
    // 0x6715b0: r0 = "Cannot change"
    //     0x6715b0: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5b218] "Cannot change"
    //     0x6715b4: ldr             x0, [x0, #0x218]
    // 0x6715b8: StoreField: r1->field_b = r0
    //     0x6715b8: stur            w0, [x1, #0xb]
    // 0x6715bc: mov             x0, x1
    // 0x6715c0: r0 = Throw()
    //     0x6715c0: bl              #0xec04b8  ; ThrowStub
    // 0x6715c4: brk             #0
  }
  _ []=(/* No info */) {
    // ** addr: 0x67270c, size: 0x5c
    // 0x67270c: EnterFrame
    //     0x67270c: stp             fp, lr, [SP, #-0x10]!
    //     0x672710: mov             fp, SP
    // 0x672714: ldr             x0, [fp, #0x10]
    // 0x672718: r2 = Null
    //     0x672718: mov             x2, NULL
    // 0x67271c: r1 = Null
    //     0x67271c: mov             x1, NULL
    // 0x672720: r4 = 60
    //     0x672720: movz            x4, #0x3c
    // 0x672724: branchIfSmi(r0, 0x672730)
    //     0x672724: tbz             w0, #0, #0x672730
    // 0x672728: r4 = LoadClassIdInstr(r0)
    //     0x672728: ldur            x4, [x0, #-1]
    //     0x67272c: ubfx            x4, x4, #0xc, #0x14
    // 0x672730: cmp             x4, #0x3f
    // 0x672734: b.eq            #0x672748
    // 0x672738: r8 = bool
    //     0x672738: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x67273c: r3 = Null
    //     0x67273c: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b230] Null
    //     0x672740: ldr             x3, [x3, #0x230]
    // 0x672744: r0 = bool()
    //     0x672744: bl              #0xed4390  ; IsType_bool_Stub
    // 0x672748: r0 = UnsupportedError()
    //     0x672748: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x67274c: mov             x1, x0
    // 0x672750: r0 = "cannot change"
    //     0x672750: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5b240] "cannot change"
    //     0x672754: ldr             x0, [x0, #0x240]
    // 0x672758: StoreField: r1->field_b = r0
    //     0x672758: stur            w0, [x1, #0xb]
    // 0x67275c: mov             x0, x1
    // 0x672760: r0 = Throw()
    //     0x672760: bl              #0xec04b8  ; ThrowStub
    // 0x672764: brk             #0
  }
  bool [](QrBitBuffer, int) {
    // ** addr: 0x6738a0, size: 0xac
    // 0x6738a0: EnterFrame
    //     0x6738a0: stp             fp, lr, [SP, #-0x10]!
    //     0x6738a4: mov             fp, SP
    // 0x6738a8: r5 = 8
    //     0x6738a8: movz            x5, #0x8
    // 0x6738ac: r4 = 7
    //     0x6738ac: movz            x4, #0x7
    // 0x6738b0: r3 = 7
    //     0x6738b0: movz            x3, #0x7
    // 0x6738b4: r2 = 1
    //     0x6738b4: movz            x2, #0x1
    // 0x6738b8: ldr             x6, [fp, #0x10]
    // 0x6738bc: r7 = LoadInt32Instr(r6)
    //     0x6738bc: sbfx            x7, x6, #1, #0x1f
    //     0x6738c0: tbz             w6, #0, #0x6738c8
    //     0x6738c4: ldur            x7, [x6, #7]
    // 0x6738c8: sdiv            x6, x7, x5
    // 0x6738cc: ldr             x5, [fp, #0x18]
    // 0x6738d0: LoadField: r8 = r5->field_7
    //     0x6738d0: ldur            w8, [x5, #7]
    // 0x6738d4: DecompressPointer r8
    //     0x6738d4: add             x8, x8, HEAP, lsl #32
    // 0x6738d8: LoadField: r5 = r8->field_b
    //     0x6738d8: ldur            w5, [x8, #0xb]
    // 0x6738dc: r0 = LoadInt32Instr(r5)
    //     0x6738dc: sbfx            x0, x5, #1, #0x1f
    // 0x6738e0: mov             x1, x6
    // 0x6738e4: cmp             x1, x0
    // 0x6738e8: b.hs            #0x673948
    // 0x6738ec: LoadField: r1 = r8->field_f
    //     0x6738ec: ldur            w1, [x8, #0xf]
    // 0x6738f0: DecompressPointer r1
    //     0x6738f0: add             x1, x1, HEAP, lsl #32
    // 0x6738f4: ArrayLoad: r5 = r1[r6]  ; Unknown_4
    //     0x6738f4: add             x16, x1, x6, lsl #2
    //     0x6738f8: ldur            w5, [x16, #0xf]
    // 0x6738fc: DecompressPointer r5
    //     0x6738fc: add             x5, x5, HEAP, lsl #32
    // 0x673900: ubfx            x7, x7, #0, #0x20
    // 0x673904: and             x1, x7, x3
    // 0x673908: ubfx            x1, x1, #0, #0x20
    // 0x67390c: sub             x3, x4, x1
    // 0x673910: r1 = LoadInt32Instr(r5)
    //     0x673910: sbfx            x1, x5, #1, #0x1f
    //     0x673914: tbz             w5, #0, #0x67391c
    //     0x673918: ldur            x1, [x5, #7]
    // 0x67391c: asr             x4, x1, x3
    // 0x673920: ubfx            x4, x4, #0, #0x20
    // 0x673924: and             x1, x4, x2
    // 0x673928: ubfx            x1, x1, #0, #0x20
    // 0x67392c: cmp             x1, #1
    // 0x673930: r16 = true
    //     0x673930: add             x16, NULL, #0x20  ; true
    // 0x673934: r17 = false
    //     0x673934: add             x17, NULL, #0x30  ; false
    // 0x673938: csel            x0, x16, x17, eq
    // 0x67393c: LeaveFrame
    //     0x67393c: mov             SP, fp
    //     0x673940: ldp             fp, lr, [SP], #0x10
    // 0x673944: ret
    //     0x673944: ret             
    // 0x673948: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x673948: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ put(/* No info */) {
    // ** addr: 0xa4504c, size: 0xf0
    // 0xa4504c: EnterFrame
    //     0xa4504c: stp             fp, lr, [SP, #-0x10]!
    //     0xa45050: mov             fp, SP
    // 0xa45054: AllocStack(0x20)
    //     0xa45054: sub             SP, SP, #0x20
    // 0xa45058: SetupParameters(QrBitBuffer this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xa45058: mov             x4, x1
    //     0xa4505c: mov             x0, x2
    //     0xa45060: stur            x1, [fp, #-0x10]
    //     0xa45064: stur            x2, [fp, #-0x18]
    //     0xa45068: stur            x3, [fp, #-0x20]
    // 0xa4506c: CheckStackOverflow
    //     0xa4506c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45070: cmp             SP, x16
    //     0xa45074: b.ls            #0xa450fc
    // 0xa45078: r6 = 0
    //     0xa45078: movz            x6, #0
    // 0xa4507c: r5 = 1
    //     0xa4507c: movz            x5, #0x1
    // 0xa45080: stur            x6, [fp, #-8]
    // 0xa45084: CheckStackOverflow
    //     0xa45084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45088: cmp             SP, x16
    //     0xa4508c: b.ls            #0xa45104
    // 0xa45090: cmp             x6, x3
    // 0xa45094: b.ge            #0xa450ec
    // 0xa45098: sub             x1, x3, x6
    // 0xa4509c: sub             x2, x1, #1
    // 0xa450a0: cmp             x2, #0x3f
    // 0xa450a4: b.hi            #0xa4510c
    // 0xa450a8: asr             x1, x0, x2
    // 0xa450ac: ubfx            x1, x1, #0, #0x20
    // 0xa450b0: and             x2, x1, x5
    // 0xa450b4: ubfx            x2, x2, #0, #0x20
    // 0xa450b8: cmp             x2, #1
    // 0xa450bc: r16 = true
    //     0xa450bc: add             x16, NULL, #0x20  ; true
    // 0xa450c0: r17 = false
    //     0xa450c0: add             x17, NULL, #0x30  ; false
    // 0xa450c4: csel            x1, x16, x17, eq
    // 0xa450c8: mov             x2, x1
    // 0xa450cc: mov             x1, x4
    // 0xa450d0: r0 = putBit()
    //     0xa450d0: bl              #0xa4513c  ; [package:qr/src/bit_buffer.dart] QrBitBuffer::putBit
    // 0xa450d4: ldur            x1, [fp, #-8]
    // 0xa450d8: add             x6, x1, #1
    // 0xa450dc: ldur            x4, [fp, #-0x10]
    // 0xa450e0: ldur            x0, [fp, #-0x18]
    // 0xa450e4: ldur            x3, [fp, #-0x20]
    // 0xa450e8: b               #0xa4507c
    // 0xa450ec: r0 = Null
    //     0xa450ec: mov             x0, NULL
    // 0xa450f0: LeaveFrame
    //     0xa450f0: mov             SP, fp
    //     0xa450f4: ldp             fp, lr, [SP], #0x10
    // 0xa450f8: ret
    //     0xa450f8: ret             
    // 0xa450fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa450fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45100: b               #0xa45078
    // 0xa45104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa45104: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45108: b               #0xa45090
    // 0xa4510c: tbnz            x2, #0x3f, #0xa45118
    // 0xa45110: asr             x1, x0, #0x3f
    // 0xa45114: b               #0xa450ac
    // 0xa45118: str             x2, [THR, #0x7a8]  ; THR::
    // 0xa4511c: stp             x5, x6, [SP, #-0x10]!
    // 0xa45120: stp             x3, x4, [SP, #-0x10]!
    // 0xa45124: stp             x0, x2, [SP, #-0x10]!
    // 0xa45128: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa4512c: r4 = 0
    //     0xa4512c: movz            x4, #0
    // 0xa45130: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa45134: blr             lr
    // 0xa45138: brk             #0
  }
  _ putBit(/* No info */) {
    // ** addr: 0xa4513c, size: 0x178
    // 0xa4513c: EnterFrame
    //     0xa4513c: stp             fp, lr, [SP, #-0x10]!
    //     0xa45140: mov             fp, SP
    // 0xa45144: AllocStack(0x28)
    //     0xa45144: sub             SP, SP, #0x28
    // 0xa45148: r0 = 8
    //     0xa45148: movz            x0, #0x8
    // 0xa4514c: mov             x3, x1
    // 0xa45150: stur            x1, [fp, #-0x20]
    // 0xa45154: stur            x2, [fp, #-0x28]
    // 0xa45158: CheckStackOverflow
    //     0xa45158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4515c: cmp             SP, x16
    //     0xa45160: b.ls            #0xa452a8
    // 0xa45164: LoadField: r1 = r3->field_b
    //     0xa45164: ldur            x1, [x3, #0xb]
    // 0xa45168: sdiv            x4, x1, x0
    // 0xa4516c: stur            x4, [fp, #-0x18]
    // 0xa45170: LoadField: r0 = r3->field_7
    //     0xa45170: ldur            w0, [x3, #7]
    // 0xa45174: DecompressPointer r0
    //     0xa45174: add             x0, x0, HEAP, lsl #32
    // 0xa45178: stur            x0, [fp, #-0x10]
    // 0xa4517c: LoadField: r1 = r0->field_b
    //     0xa4517c: ldur            w1, [x0, #0xb]
    // 0xa45180: r5 = LoadInt32Instr(r1)
    //     0xa45180: sbfx            x5, x1, #1, #0x1f
    // 0xa45184: stur            x5, [fp, #-8]
    // 0xa45188: cmp             x5, x4
    // 0xa4518c: b.gt            #0xa451dc
    // 0xa45190: LoadField: r1 = r0->field_f
    //     0xa45190: ldur            w1, [x0, #0xf]
    // 0xa45194: DecompressPointer r1
    //     0xa45194: add             x1, x1, HEAP, lsl #32
    // 0xa45198: LoadField: r6 = r1->field_b
    //     0xa45198: ldur            w6, [x1, #0xb]
    // 0xa4519c: r1 = LoadInt32Instr(r6)
    //     0xa4519c: sbfx            x1, x6, #1, #0x1f
    // 0xa451a0: cmp             x5, x1
    // 0xa451a4: b.ne            #0xa451b0
    // 0xa451a8: mov             x1, x0
    // 0xa451ac: r0 = _growToNextCapacity()
    //     0xa451ac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa451b0: ldur            x2, [fp, #-0x10]
    // 0xa451b4: ldur            x3, [fp, #-8]
    // 0xa451b8: add             x4, x3, #1
    // 0xa451bc: lsl             x5, x4, #1
    // 0xa451c0: StoreField: r2->field_b = r5
    //     0xa451c0: stur            w5, [x2, #0xb]
    // 0xa451c4: LoadField: r5 = r2->field_f
    //     0xa451c4: ldur            w5, [x2, #0xf]
    // 0xa451c8: DecompressPointer r5
    //     0xa451c8: add             x5, x5, HEAP, lsl #32
    // 0xa451cc: ArrayStore: r5[r3] = rZR  ; Unknown_4
    //     0xa451cc: add             x6, x5, x3, lsl #2
    //     0xa451d0: stur            wzr, [x6, #0xf]
    // 0xa451d4: mov             x0, x4
    // 0xa451d8: b               #0xa451e8
    // 0xa451dc: mov             x2, x0
    // 0xa451e0: r3 = LoadInt32Instr(r1)
    //     0xa451e0: sbfx            x3, x1, #1, #0x1f
    // 0xa451e4: mov             x0, x3
    // 0xa451e8: ldur            x3, [fp, #-0x28]
    // 0xa451ec: tbnz            w3, #4, #0xa45288
    // 0xa451f0: ldur            x3, [fp, #-0x20]
    // 0xa451f4: ldur            x4, [fp, #-0x18]
    // 0xa451f8: r6 = 128
    //     0xa451f8: movz            x6, #0x80
    // 0xa451fc: r5 = 7
    //     0xa451fc: movz            x5, #0x7
    // 0xa45200: mov             x1, x4
    // 0xa45204: cmp             x1, x0
    // 0xa45208: b.hs            #0xa452b0
    // 0xa4520c: LoadField: r7 = r2->field_f
    //     0xa4520c: ldur            w7, [x2, #0xf]
    // 0xa45210: DecompressPointer r7
    //     0xa45210: add             x7, x7, HEAP, lsl #32
    // 0xa45214: ArrayLoad: r2 = r7[r4]  ; Unknown_4
    //     0xa45214: add             x16, x7, x4, lsl #2
    //     0xa45218: ldur            w2, [x16, #0xf]
    // 0xa4521c: DecompressPointer r2
    //     0xa4521c: add             x2, x2, HEAP, lsl #32
    // 0xa45220: LoadField: r8 = r3->field_b
    //     0xa45220: ldur            x8, [x3, #0xb]
    // 0xa45224: ubfx            x8, x8, #0, #0x20
    // 0xa45228: and             x9, x8, x5
    // 0xa4522c: ubfx            x9, x9, #0, #0x20
    // 0xa45230: asr             x5, x6, x9
    // 0xa45234: r6 = LoadInt32Instr(r2)
    //     0xa45234: sbfx            x6, x2, #1, #0x1f
    //     0xa45238: tbz             w2, #0, #0xa45240
    //     0xa4523c: ldur            x6, [x2, #7]
    // 0xa45240: orr             x2, x6, x5
    // 0xa45244: r0 = BoxInt64Instr(r2)
    //     0xa45244: sbfiz           x0, x2, #1, #0x1f
    //     0xa45248: cmp             x2, x0, asr #1
    //     0xa4524c: b.eq            #0xa45258
    //     0xa45250: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa45254: stur            x2, [x0, #7]
    // 0xa45258: mov             x1, x7
    // 0xa4525c: ArrayStore: r1[r4] = r0  ; List_4
    //     0xa4525c: add             x25, x1, x4, lsl #2
    //     0xa45260: add             x25, x25, #0xf
    //     0xa45264: str             w0, [x25]
    //     0xa45268: tbz             w0, #0, #0xa45284
    //     0xa4526c: ldurb           w16, [x1, #-1]
    //     0xa45270: ldurb           w17, [x0, #-1]
    //     0xa45274: and             x16, x17, x16, lsr #2
    //     0xa45278: tst             x16, HEAP, lsr #32
    //     0xa4527c: b.eq            #0xa45284
    //     0xa45280: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa45284: b               #0xa4528c
    // 0xa45288: ldur            x3, [fp, #-0x20]
    // 0xa4528c: LoadField: r1 = r3->field_b
    //     0xa4528c: ldur            x1, [x3, #0xb]
    // 0xa45290: add             x2, x1, #1
    // 0xa45294: StoreField: r3->field_b = r2
    //     0xa45294: stur            x2, [x3, #0xb]
    // 0xa45298: r0 = Null
    //     0xa45298: mov             x0, NULL
    // 0xa4529c: LeaveFrame
    //     0xa4529c: mov             SP, fp
    //     0xa452a0: ldp             fp, lr, [SP], #0x10
    // 0xa452a4: ret
    //     0xa452a4: ret             
    // 0xa452a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa452a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa452ac: b               #0xa45164
    // 0xa452b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa452b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
