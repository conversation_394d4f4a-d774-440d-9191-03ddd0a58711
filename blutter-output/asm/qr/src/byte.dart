// lib: , url: package:qr/src/byte.dart

// class id: 1051063, size: 0x8
class :: {
}

// class id: 540, size: 0x14, field offset: 0x8
class QrByte extends Object
    implements QrDatum {

  factory _ QrByte(/* No info */) {
    // ** addr: 0xa4554c, size: 0x50
    // 0xa4554c: EnterFrame
    //     0xa4554c: stp             fp, lr, [SP, #-0x10]!
    //     0xa45550: mov             fp, SP
    // 0xa45554: AllocStack(0x8)
    //     0xa45554: sub             SP, SP, #8
    // 0xa45558: CheckStackOverflow
    //     0xa45558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4555c: cmp             SP, x16
    //     0xa45560: b.ls            #0xa45594
    // 0xa45564: r1 = Instance_Utf8Encoder
    //     0xa45564: ldr             x1, [PP, #0xe08]  ; [pp+0xe08] Obj!Utf8Encoder@e2cd81
    // 0xa45568: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa45568: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4556c: r0 = convert()
    //     0xa4556c: bl              #0xcf8df0  ; [dart:convert] Utf8Encoder::convert
    // 0xa45570: stur            x0, [fp, #-8]
    // 0xa45574: r0 = QrByte()
    //     0xa45574: bl              #0xa4559c  ; AllocateQrByteStub -> QrByte (size=0x14)
    // 0xa45578: r1 = 4
    //     0xa45578: movz            x1, #0x4
    // 0xa4557c: StoreField: r0->field_7 = r1
    //     0xa4557c: stur            x1, [x0, #7]
    // 0xa45580: ldur            x1, [fp, #-8]
    // 0xa45584: StoreField: r0->field_f = r1
    //     0xa45584: stur            w1, [x0, #0xf]
    // 0xa45588: LeaveFrame
    //     0xa45588: mov             SP, fp
    //     0xa4558c: ldp             fp, lr, [SP], #0x10
    // 0xa45590: ret
    //     0xa45590: ret             
    // 0xa45594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa45594: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45598: b               #0xa45564
  }
}

// class id: 541, size: 0x8, field offset: 0x8
abstract class QrDatum extends Object {
}
