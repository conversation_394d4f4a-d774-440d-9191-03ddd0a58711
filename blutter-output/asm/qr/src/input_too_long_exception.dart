// lib: , url: package:qr/src/input_too_long_exception.dart

// class id: 1051064, size: 0x8
class :: {
}

// class id: 539, size: 0xc, field offset: 0x8
class InputTooLongException extends Object
    implements Exception {

  factory _ InputTooLongException(/* No info */) {
    // ** addr: 0xa48968, size: 0xb0
    // 0xa48968: EnterFrame
    //     0xa48968: stp             fp, lr, [SP, #-0x10]!
    //     0xa4896c: mov             fp, SP
    // 0xa48970: AllocStack(0x20)
    //     0xa48970: sub             SP, SP, #0x20
    // 0xa48974: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xa48974: mov             x0, x2
    //     0xa48978: stur            x2, [fp, #-8]
    //     0xa4897c: stur            x3, [fp, #-0x10]
    // 0xa48980: CheckStackOverflow
    //     0xa48980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48984: cmp             SP, x16
    //     0xa48988: b.ls            #0xa48a10
    // 0xa4898c: r1 = Null
    //     0xa4898c: mov             x1, NULL
    // 0xa48990: r2 = 8
    //     0xa48990: movz            x2, #0x8
    // 0xa48994: r0 = AllocateArray()
    //     0xa48994: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa48998: mov             x2, x0
    // 0xa4899c: r16 = "Input too long. "
    //     0xa4899c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d90] "Input too long. "
    //     0xa489a0: ldr             x16, [x16, #0xd90]
    // 0xa489a4: StoreField: r2->field_f = r16
    //     0xa489a4: stur            w16, [x2, #0xf]
    // 0xa489a8: ldur            x3, [fp, #-8]
    // 0xa489ac: r0 = BoxInt64Instr(r3)
    //     0xa489ac: sbfiz           x0, x3, #1, #0x1f
    //     0xa489b0: cmp             x3, x0, asr #1
    //     0xa489b4: b.eq            #0xa489c0
    //     0xa489b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa489bc: stur            x3, [x0, #7]
    // 0xa489c0: StoreField: r2->field_13 = r0
    //     0xa489c0: stur            w0, [x2, #0x13]
    // 0xa489c4: r16 = " > "
    //     0xa489c4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d98] " > "
    //     0xa489c8: ldr             x16, [x16, #0xd98]
    // 0xa489cc: ArrayStore: r2[0] = r16  ; List_4
    //     0xa489cc: stur            w16, [x2, #0x17]
    // 0xa489d0: ldur            x3, [fp, #-0x10]
    // 0xa489d4: r0 = BoxInt64Instr(r3)
    //     0xa489d4: sbfiz           x0, x3, #1, #0x1f
    //     0xa489d8: cmp             x3, x0, asr #1
    //     0xa489dc: b.eq            #0xa489e8
    //     0xa489e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa489e4: stur            x3, [x0, #7]
    // 0xa489e8: StoreField: r2->field_1b = r0
    //     0xa489e8: stur            w0, [x2, #0x1b]
    // 0xa489ec: str             x2, [SP]
    // 0xa489f0: r0 = _interpolate()
    //     0xa489f0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa489f4: stur            x0, [fp, #-0x18]
    // 0xa489f8: r0 = InputTooLongException()
    //     0xa489f8: bl              #0xa48a18  ; AllocateInputTooLongExceptionStub -> InputTooLongException (size=0xc)
    // 0xa489fc: ldur            x1, [fp, #-0x18]
    // 0xa48a00: StoreField: r0->field_7 = r1
    //     0xa48a00: stur            w1, [x0, #7]
    // 0xa48a04: LeaveFrame
    //     0xa48a04: mov             SP, fp
    //     0xa48a08: ldp             fp, lr, [SP], #0x10
    // 0xa48a0c: ret
    //     0xa48a0c: ret             
    // 0xa48a10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48a10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48a14: b               #0xa4898c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3f7f4, size: 0x5c
    // 0xc3f7f4: EnterFrame
    //     0xc3f7f4: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f7f8: mov             fp, SP
    // 0xc3f7fc: AllocStack(0x8)
    //     0xc3f7fc: sub             SP, SP, #8
    // 0xc3f800: CheckStackOverflow
    //     0xc3f800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f804: cmp             SP, x16
    //     0xc3f808: b.ls            #0xc3f848
    // 0xc3f80c: r1 = Null
    //     0xc3f80c: mov             x1, NULL
    // 0xc3f810: r2 = 4
    //     0xc3f810: movz            x2, #0x4
    // 0xc3f814: r0 = AllocateArray()
    //     0xc3f814: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3f818: r16 = "QrInputTooLongException: "
    //     0xc3f818: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b210] "QrInputTooLongException: "
    //     0xc3f81c: ldr             x16, [x16, #0x210]
    // 0xc3f820: StoreField: r0->field_f = r16
    //     0xc3f820: stur            w16, [x0, #0xf]
    // 0xc3f824: ldr             x1, [fp, #0x10]
    // 0xc3f828: LoadField: r2 = r1->field_7
    //     0xc3f828: ldur            w2, [x1, #7]
    // 0xc3f82c: DecompressPointer r2
    //     0xc3f82c: add             x2, x2, HEAP, lsl #32
    // 0xc3f830: StoreField: r0->field_13 = r2
    //     0xc3f830: stur            w2, [x0, #0x13]
    // 0xc3f834: str             x0, [SP]
    // 0xc3f838: r0 = _interpolate()
    //     0xc3f838: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3f83c: LeaveFrame
    //     0xc3f83c: mov             SP, fp
    //     0xc3f840: ldp             fp, lr, [SP], #0x10
    // 0xc3f844: ret
    //     0xc3f844: ret             
    // 0xc3f848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f848: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f84c: b               #0xc3f80c
  }
}
