// lib: , url: package:qr/src/math.dart

// class id: 1051065, size: 0x8
class :: {

  static late final Uint8List _logTable; // offset: 0x1738
  static late final Uint8List _expTable; // offset: 0x173c

  static int glog(int) {
    // ** addr: 0xa49768, size: 0xe8
    // 0xa49768: EnterFrame
    //     0xa49768: stp             fp, lr, [SP, #-0x10]!
    //     0xa4976c: mov             fp, SP
    // 0xa49770: AllocStack(0x18)
    //     0xa49770: sub             SP, SP, #0x18
    // 0xa49774: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xa49774: stur            x1, [fp, #-8]
    // 0xa49778: CheckStackOverflow
    //     0xa49778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4977c: cmp             SP, x16
    //     0xa49780: b.ls            #0xa49844
    // 0xa49784: cmp             x1, #1
    // 0xa49788: b.lt            #0xa497e4
    // 0xa4978c: r0 = InitLateStaticField(0x1738) // [package:qr/src/math.dart] ::_logTable
    //     0xa4978c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49790: ldr             x0, [x0, #0x2e70]
    //     0xa49794: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa49798: cmp             w0, w16
    //     0xa4979c: b.ne            #0xa497ac
    //     0xa497a0: add             x2, PP, #0x57, lsl #12  ; [pp+0x57da0] Field <::._logTable@2681014454>: static late final (offset: 0x1738)
    //     0xa497a4: ldr             x2, [x2, #0xda0]
    //     0xa497a8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa497ac: mov             x2, x0
    // 0xa497b0: LoadField: r0 = r2->field_13
    //     0xa497b0: ldur            w0, [x2, #0x13]
    // 0xa497b4: r1 = LoadInt32Instr(r0)
    //     0xa497b4: sbfx            x1, x0, #1, #0x1f
    // 0xa497b8: mov             x0, x1
    // 0xa497bc: ldur            x1, [fp, #-8]
    // 0xa497c0: cmp             x1, x0
    // 0xa497c4: b.hs            #0xa4984c
    // 0xa497c8: ldur            x0, [fp, #-8]
    // 0xa497cc: ArrayLoad: r1 = r2[r0]  ; List_1
    //     0xa497cc: add             x16, x2, x0
    //     0xa497d0: ldrb            w1, [x16, #0x17]
    // 0xa497d4: mov             x0, x1
    // 0xa497d8: LeaveFrame
    //     0xa497d8: mov             SP, fp
    //     0xa497dc: ldp             fp, lr, [SP], #0x10
    // 0xa497e0: ret
    //     0xa497e0: ret             
    // 0xa497e4: mov             x0, x1
    // 0xa497e8: r1 = Null
    //     0xa497e8: mov             x1, NULL
    // 0xa497ec: r2 = 6
    //     0xa497ec: movz            x2, #0x6
    // 0xa497f0: r0 = AllocateArray()
    //     0xa497f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa497f4: r16 = "glog("
    //     0xa497f4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57db0] "glog("
    //     0xa497f8: ldr             x16, [x16, #0xdb0]
    // 0xa497fc: StoreField: r0->field_f = r16
    //     0xa497fc: stur            w16, [x0, #0xf]
    // 0xa49800: ldur            x1, [fp, #-8]
    // 0xa49804: lsl             x2, x1, #1
    // 0xa49808: StoreField: r0->field_13 = r2
    //     0xa49808: stur            w2, [x0, #0x13]
    // 0xa4980c: r16 = ")"
    //     0xa4980c: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xa49810: ArrayStore: r0[0] = r16  ; List_4
    //     0xa49810: stur            w16, [x0, #0x17]
    // 0xa49814: str             x0, [SP]
    // 0xa49818: r0 = _interpolate()
    //     0xa49818: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa4981c: stur            x0, [fp, #-0x10]
    // 0xa49820: r0 = ArgumentError()
    //     0xa49820: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xa49824: mov             x1, x0
    // 0xa49828: ldur            x0, [fp, #-0x10]
    // 0xa4982c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4982c: stur            w0, [x1, #0x17]
    // 0xa49830: r0 = false
    //     0xa49830: add             x0, NULL, #0x30  ; false
    // 0xa49834: StoreField: r1->field_b = r0
    //     0xa49834: stur            w0, [x1, #0xb]
    // 0xa49838: mov             x0, x1
    // 0xa4983c: r0 = Throw()
    //     0xa4983c: bl              #0xec04b8  ; ThrowStub
    // 0xa49840: brk             #0
    // 0xa49844: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49844: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49848: b               #0xa49784
    // 0xa4984c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4984c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static Uint8List _expTable() {
    // ** addr: 0xa49850, size: 0xfc
    // 0xa49850: EnterFrame
    //     0xa49850: stp             fp, lr, [SP, #-0x10]!
    //     0xa49854: mov             fp, SP
    // 0xa49858: r4 = 512
    //     0xa49858: movz            x4, #0x200
    // 0xa4985c: r0 = AllocateUint8Array()
    //     0xa4985c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xa49860: r2 = 0
    //     0xa49860: movz            x2, #0
    // 0xa49864: r1 = 1
    //     0xa49864: movz            x1, #0x1
    // 0xa49868: CheckStackOverflow
    //     0xa49868: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4986c: cmp             SP, x16
    //     0xa49870: b.ls            #0xa49910
    // 0xa49874: cmp             x2, #8
    // 0xa49878: b.ge            #0xa4989c
    // 0xa4987c: cmp             x2, #0x3f
    // 0xa49880: b.hi            #0xa49918
    // 0xa49884: lsl             x3, x1, x2
    // 0xa49888: ArrayStore: r0[r2] = r3  ; TypeUnknown_1
    //     0xa49888: add             x4, x0, x2
    //     0xa4988c: strb            w3, [x4, #0x17]
    // 0xa49890: add             x3, x2, #1
    // 0xa49894: mov             x2, x3
    // 0xa49898: b               #0xa49868
    // 0xa4989c: r1 = 8
    //     0xa4989c: movz            x1, #0x8
    // 0xa498a0: CheckStackOverflow
    //     0xa498a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa498a4: cmp             SP, x16
    //     0xa498a8: b.ls            #0xa49944
    // 0xa498ac: cmp             x1, #0x100
    // 0xa498b0: b.ge            #0xa49904
    // 0xa498b4: sub             x2, x1, #4
    // 0xa498b8: ArrayLoad: r3 = r0[r2]  ; List_1
    //     0xa498b8: add             x16, x0, x2
    //     0xa498bc: ldrb            w3, [x16, #0x17]
    // 0xa498c0: sub             x2, x1, #5
    // 0xa498c4: ArrayLoad: r4 = r0[r2]  ; List_1
    //     0xa498c4: add             x16, x0, x2
    //     0xa498c8: ldrb            w4, [x16, #0x17]
    // 0xa498cc: eor             x2, x3, x4
    // 0xa498d0: sub             x3, x1, #6
    // 0xa498d4: ArrayLoad: r4 = r0[r3]  ; List_1
    //     0xa498d4: add             x16, x0, x3
    //     0xa498d8: ldrb            w4, [x16, #0x17]
    // 0xa498dc: eor             x3, x2, x4
    // 0xa498e0: sub             x2, x1, #8
    // 0xa498e4: ArrayLoad: r4 = r0[r2]  ; List_1
    //     0xa498e4: add             x16, x0, x2
    //     0xa498e8: ldrb            w4, [x16, #0x17]
    // 0xa498ec: eor             x2, x3, x4
    // 0xa498f0: ArrayStore: r0[r1] = r2  ; TypeUnknown_1
    //     0xa498f0: add             x3, x0, x1
    //     0xa498f4: strb            w2, [x3, #0x17]
    // 0xa498f8: add             x2, x1, #1
    // 0xa498fc: mov             x1, x2
    // 0xa49900: b               #0xa498a0
    // 0xa49904: LeaveFrame
    //     0xa49904: mov             SP, fp
    //     0xa49908: ldp             fp, lr, [SP], #0x10
    // 0xa4990c: ret
    //     0xa4990c: ret             
    // 0xa49910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49910: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49914: b               #0xa49874
    // 0xa49918: tbnz            x2, #0x3f, #0xa49924
    // 0xa4991c: mov             x3, xzr
    // 0xa49920: b               #0xa49888
    // 0xa49924: str             x2, [THR, #0x7a8]  ; THR::
    // 0xa49928: stp             x1, x2, [SP, #-0x10]!
    // 0xa4992c: SaveReg r0
    //     0xa4992c: str             x0, [SP, #-8]!
    // 0xa49930: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa49934: r4 = 0
    //     0xa49934: movz            x4, #0
    // 0xa49938: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa4993c: blr             lr
    // 0xa49940: brk             #0
    // 0xa49944: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49944: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49948: b               #0xa498ac
  }
  static Uint8List _logTable() {
    // ** addr: 0xa4994c, size: 0x2c
    // 0xa4994c: EnterFrame
    //     0xa4994c: stp             fp, lr, [SP, #-0x10]!
    //     0xa49950: mov             fp, SP
    // 0xa49954: CheckStackOverflow
    //     0xa49954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49958: cmp             SP, x16
    //     0xa4995c: b.ls            #0xa49970
    // 0xa49960: r0 = _createLogTable()
    //     0xa49960: bl              #0xa49978  ; [package:qr/src/math.dart] ::_createLogTable
    // 0xa49964: LeaveFrame
    //     0xa49964: mov             SP, fp
    //     0xa49968: ldp             fp, lr, [SP], #0x10
    // 0xa4996c: ret
    //     0xa4996c: ret             
    // 0xa49970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49970: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49974: b               #0xa49960
  }
  static Uint8List _createLogTable() {
    // ** addr: 0xa49978, size: 0xbc
    // 0xa49978: EnterFrame
    //     0xa49978: stp             fp, lr, [SP, #-0x10]!
    //     0xa4997c: mov             fp, SP
    // 0xa49980: AllocStack(0x10)
    //     0xa49980: sub             SP, SP, #0x10
    // 0xa49984: CheckStackOverflow
    //     0xa49984: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49988: cmp             SP, x16
    //     0xa4998c: b.ls            #0xa49a20
    // 0xa49990: r4 = 512
    //     0xa49990: movz            x4, #0x200
    // 0xa49994: r0 = AllocateUint8Array()
    //     0xa49994: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xa49998: stur            x0, [fp, #-0x10]
    // 0xa4999c: r1 = 0
    //     0xa4999c: movz            x1, #0
    // 0xa499a0: stur            x1, [fp, #-8]
    // 0xa499a4: CheckStackOverflow
    //     0xa499a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa499a8: cmp             SP, x16
    //     0xa499ac: b.ls            #0xa49a28
    // 0xa499b0: cmp             x1, #0xff
    // 0xa499b4: b.ge            #0xa49a14
    // 0xa499b8: r0 = InitLateStaticField(0x173c) // [package:qr/src/math.dart] ::_expTable
    //     0xa499b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa499bc: ldr             x0, [x0, #0x2e78]
    //     0xa499c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa499c4: cmp             w0, w16
    //     0xa499c8: b.ne            #0xa499d8
    //     0xa499cc: add             x2, PP, #0x57, lsl #12  ; [pp+0x57da8] Field <::._expTable@2681014454>: static late final (offset: 0x173c)
    //     0xa499d0: ldr             x2, [x2, #0xda8]
    //     0xa499d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa499d8: mov             x2, x0
    // 0xa499dc: LoadField: r3 = r2->field_13
    //     0xa499dc: ldur            w3, [x2, #0x13]
    // 0xa499e0: r0 = LoadInt32Instr(r3)
    //     0xa499e0: sbfx            x0, x3, #1, #0x1f
    // 0xa499e4: ldur            x1, [fp, #-8]
    // 0xa499e8: cmp             x1, x0
    // 0xa499ec: b.hs            #0xa49a30
    // 0xa499f0: ldur            x1, [fp, #-8]
    // 0xa499f4: ArrayLoad: r3 = r2[r1]  ; List_1
    //     0xa499f4: add             x16, x2, x1
    //     0xa499f8: ldrb            w3, [x16, #0x17]
    // 0xa499fc: ldur            x0, [fp, #-0x10]
    // 0xa49a00: ArrayStore: r0[r3] = r1  ; TypeUnknown_1
    //     0xa49a00: add             x2, x0, x3
    //     0xa49a04: strb            w1, [x2, #0x17]
    // 0xa49a08: add             x2, x1, #1
    // 0xa49a0c: mov             x1, x2
    // 0xa49a10: b               #0xa499a0
    // 0xa49a14: LeaveFrame
    //     0xa49a14: mov             SP, fp
    //     0xa49a18: ldp             fp, lr, [SP], #0x10
    // 0xa49a1c: ret
    //     0xa49a1c: ret             
    // 0xa49a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49a20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49a24: b               #0xa49990
    // 0xa49a28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49a28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49a2c: b               #0xa499b0
    // 0xa49a30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa49a30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
