// lib: , url: package:qr/src/polynomial.dart

// class id: 1051066, size: 0x8
class :: {
}

// class id: 538, size: 0xc, field offset: 0x8
class QrPolynomial extends Object {

  _ mod(/* No info */) {
    // ** addr: 0xa493d8, size: 0x2f0
    // 0xa493d8: EnterFrame
    //     0xa493d8: stp             fp, lr, [SP, #-0x10]!
    //     0xa493dc: mov             fp, SP
    // 0xa493e0: AllocStack(0x60)
    //     0xa493e0: sub             SP, SP, #0x60
    // 0xa493e4: SetupParameters(QrPolynomial this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x30 */)
    //     0xa493e4: mov             x0, x1
    //     0xa493e8: stur            x2, [fp, #-0x30]
    // 0xa493ec: CheckStackOverflow
    //     0xa493ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa493f0: cmp             SP, x16
    //     0xa493f4: b.ls            #0xa49694
    // 0xa493f8: LoadField: r3 = r0->field_7
    //     0xa493f8: ldur            w3, [x0, #7]
    // 0xa493fc: DecompressPointer r3
    //     0xa493fc: add             x3, x3, HEAP, lsl #32
    // 0xa49400: stur            x3, [fp, #-0x28]
    // 0xa49404: LoadField: r4 = r3->field_13
    //     0xa49404: ldur            w4, [x3, #0x13]
    // 0xa49408: stur            x4, [fp, #-0x20]
    // 0xa4940c: LoadField: r5 = r2->field_7
    //     0xa4940c: ldur            w5, [x2, #7]
    // 0xa49410: DecompressPointer r5
    //     0xa49410: add             x5, x5, HEAP, lsl #32
    // 0xa49414: stur            x5, [fp, #-0x18]
    // 0xa49418: LoadField: r1 = r5->field_13
    //     0xa49418: ldur            w1, [x5, #0x13]
    // 0xa4941c: r6 = LoadInt32Instr(r4)
    //     0xa4941c: sbfx            x6, x4, #1, #0x1f
    // 0xa49420: stur            x6, [fp, #-0x10]
    // 0xa49424: r7 = LoadInt32Instr(r1)
    //     0xa49424: sbfx            x7, x1, #1, #0x1f
    // 0xa49428: stur            x7, [fp, #-8]
    // 0xa4942c: sub             x1, x6, x7
    // 0xa49430: tbz             x1, #0x3f, #0xa49440
    // 0xa49434: LeaveFrame
    //     0xa49434: mov             SP, fp
    //     0xa49438: ldp             fp, lr, [SP], #0x10
    // 0xa4943c: ret
    //     0xa4943c: ret             
    // 0xa49440: mov             x0, x6
    // 0xa49444: r1 = 0
    //     0xa49444: movz            x1, #0
    // 0xa49448: cmp             x1, x0
    // 0xa4944c: b.hs            #0xa4969c
    // 0xa49450: ArrayLoad: r1 = r3[0]  ; List_1
    //     0xa49450: ldrb            w1, [x3, #0x17]
    // 0xa49454: r0 = glog()
    //     0xa49454: bl              #0xa49768  ; [package:qr/src/math.dart] ::glog
    // 0xa49458: mov             x2, x0
    // 0xa4945c: ldur            x0, [fp, #-8]
    // 0xa49460: r1 = 0
    //     0xa49460: movz            x1, #0
    // 0xa49464: stur            x2, [fp, #-0x38]
    // 0xa49468: cmp             x1, x0
    // 0xa4946c: b.hs            #0xa496a0
    // 0xa49470: ldur            x0, [fp, #-0x18]
    // 0xa49474: ArrayLoad: r1 = r0[0]  ; List_1
    //     0xa49474: ldrb            w1, [x0, #0x17]
    // 0xa49478: r0 = glog()
    //     0xa49478: bl              #0xa49768  ; [package:qr/src/math.dart] ::glog
    // 0xa4947c: mov             x1, x0
    // 0xa49480: ldur            x0, [fp, #-0x38]
    // 0xa49484: sub             x2, x0, x1
    // 0xa49488: ldur            x4, [fp, #-0x20]
    // 0xa4948c: stur            x2, [fp, #-0x40]
    // 0xa49490: r0 = AllocateUint8Array()
    //     0xa49490: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xa49494: mov             x2, x0
    // 0xa49498: stur            x2, [fp, #-0x20]
    // 0xa4949c: ldur            x0, [fp, #-0x28]
    // 0xa494a0: ldur            x3, [fp, #-0x10]
    // 0xa494a4: r1 = 0
    //     0xa494a4: movz            x1, #0
    // 0xa494a8: CheckStackOverflow
    //     0xa494a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa494ac: cmp             SP, x16
    //     0xa494b0: b.ls            #0xa496a4
    // 0xa494b4: cmp             x1, x3
    // 0xa494b8: b.ge            #0xa494d8
    // 0xa494bc: ArrayLoad: r4 = r0[r1]  ; List_1
    //     0xa494bc: add             x16, x0, x1
    //     0xa494c0: ldrb            w4, [x16, #0x17]
    // 0xa494c4: ArrayStore: r2[r1] = r4  ; TypeUnknown_1
    //     0xa494c4: add             x5, x2, x1
    //     0xa494c8: strb            w4, [x5, #0x17]
    // 0xa494cc: add             x4, x1, #1
    // 0xa494d0: mov             x1, x4
    // 0xa494d4: b               #0xa494a8
    // 0xa494d8: r7 = 0
    //     0xa494d8: movz            x7, #0
    // 0xa494dc: ldur            x4, [fp, #-0x40]
    // 0xa494e0: ldur            x5, [fp, #-0x18]
    // 0xa494e4: ldur            x6, [fp, #-8]
    // 0xa494e8: stur            x7, [fp, #-0x50]
    // 0xa494ec: CheckStackOverflow
    //     0xa494ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa494f0: cmp             SP, x16
    //     0xa494f4: b.ls            #0xa496ac
    // 0xa494f8: cmp             x7, x6
    // 0xa494fc: b.ge            #0xa49608
    // 0xa49500: mov             x0, x3
    // 0xa49504: mov             x1, x7
    // 0xa49508: cmp             x1, x0
    // 0xa4950c: b.hs            #0xa496b4
    // 0xa49510: ArrayLoad: r0 = r2[r7]  ; List_1
    //     0xa49510: add             x16, x2, x7
    //     0xa49514: ldrb            w0, [x16, #0x17]
    // 0xa49518: stur            x0, [fp, #-0x48]
    // 0xa4951c: ArrayLoad: r1 = r5[r7]  ; List_1
    //     0xa4951c: add             x16, x5, x7
    //     0xa49520: ldrb            w1, [x16, #0x17]
    // 0xa49524: stur            x1, [fp, #-0x38]
    // 0xa49528: cmp             x1, #1
    // 0xa4952c: b.lt            #0xa49634
    // 0xa49530: r0 = InitLateStaticField(0x1738) // [package:qr/src/math.dart] ::_logTable
    //     0xa49530: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49534: ldr             x0, [x0, #0x2e70]
    //     0xa49538: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4953c: cmp             w0, w16
    //     0xa49540: b.ne            #0xa49550
    //     0xa49544: add             x2, PP, #0x57, lsl #12  ; [pp+0x57da0] Field <::._logTable@2681014454>: static late final (offset: 0x1738)
    //     0xa49548: ldr             x2, [x2, #0xda0]
    //     0xa4954c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa49550: mov             x2, x0
    // 0xa49554: LoadField: r0 = r2->field_13
    //     0xa49554: ldur            w0, [x2, #0x13]
    // 0xa49558: r1 = LoadInt32Instr(r0)
    //     0xa49558: sbfx            x1, x0, #1, #0x1f
    // 0xa4955c: mov             x0, x1
    // 0xa49560: ldur            x1, [fp, #-0x38]
    // 0xa49564: cmp             x1, x0
    // 0xa49568: b.hs            #0xa496b8
    // 0xa4956c: ldur            x0, [fp, #-0x38]
    // 0xa49570: ArrayLoad: r1 = r2[r0]  ; List_1
    //     0xa49570: add             x16, x2, x0
    //     0xa49574: ldrb            w1, [x16, #0x17]
    // 0xa49578: ldur            x0, [fp, #-0x40]
    // 0xa4957c: add             x2, x1, x0
    // 0xa49580: stur            x2, [fp, #-0x58]
    // 0xa49584: r0 = InitLateStaticField(0x173c) // [package:qr/src/math.dart] ::_expTable
    //     0xa49584: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49588: ldr             x0, [x0, #0x2e78]
    //     0xa4958c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa49590: cmp             w0, w16
    //     0xa49594: b.ne            #0xa495a4
    //     0xa49598: add             x2, PP, #0x57, lsl #12  ; [pp+0x57da8] Field <::._expTable@2681014454>: static late final (offset: 0x173c)
    //     0xa4959c: ldr             x2, [x2, #0xda8]
    //     0xa495a0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa495a4: mov             x3, x0
    // 0xa495a8: ldur            x0, [fp, #-0x58]
    // 0xa495ac: r2 = 255
    //     0xa495ac: movz            x2, #0xff
    // 0xa495b0: sdiv            x1, x0, x2
    // 0xa495b4: msub            x4, x1, x2, x0
    // 0xa495b8: cmp             x4, xzr
    // 0xa495bc: b.lt            #0xa496bc
    // 0xa495c0: LoadField: r0 = r3->field_13
    //     0xa495c0: ldur            w0, [x3, #0x13]
    // 0xa495c4: r1 = LoadInt32Instr(r0)
    //     0xa495c4: sbfx            x1, x0, #1, #0x1f
    // 0xa495c8: mov             x0, x1
    // 0xa495cc: mov             x1, x4
    // 0xa495d0: cmp             x1, x0
    // 0xa495d4: b.hs            #0xa496c4
    // 0xa495d8: ArrayLoad: r0 = r3[r4]  ; List_1
    //     0xa495d8: add             x16, x3, x4
    //     0xa495dc: ldrb            w0, [x16, #0x17]
    // 0xa495e0: ldur            x1, [fp, #-0x48]
    // 0xa495e4: eor             x3, x1, x0
    // 0xa495e8: ldur            x1, [fp, #-0x50]
    // 0xa495ec: ldur            x0, [fp, #-0x20]
    // 0xa495f0: ArrayStore: r0[r1] = r3  ; TypeUnknown_1
    //     0xa495f0: add             x4, x0, x1
    //     0xa495f4: strb            w3, [x4, #0x17]
    // 0xa495f8: add             x7, x1, #1
    // 0xa495fc: mov             x2, x0
    // 0xa49600: ldur            x3, [fp, #-0x10]
    // 0xa49604: b               #0xa494dc
    // 0xa49608: mov             x0, x2
    // 0xa4960c: mov             x2, x0
    // 0xa49610: r1 = Null
    //     0xa49610: mov             x1, NULL
    // 0xa49614: r3 = 0
    //     0xa49614: movz            x3, #0
    // 0xa49618: r0 = QrPolynomial()
    //     0xa49618: bl              #0xa49a34  ; [package:qr/src/polynomial.dart] QrPolynomial::QrPolynomial
    // 0xa4961c: mov             x1, x0
    // 0xa49620: ldur            x2, [fp, #-0x30]
    // 0xa49624: r0 = mod()
    //     0xa49624: bl              #0xa493d8  ; [package:qr/src/polynomial.dart] QrPolynomial::mod
    // 0xa49628: LeaveFrame
    //     0xa49628: mov             SP, fp
    //     0xa4962c: ldp             fp, lr, [SP], #0x10
    // 0xa49630: ret
    //     0xa49630: ret             
    // 0xa49634: mov             x0, x1
    // 0xa49638: r1 = Null
    //     0xa49638: mov             x1, NULL
    // 0xa4963c: r2 = 6
    //     0xa4963c: movz            x2, #0x6
    // 0xa49640: r0 = AllocateArray()
    //     0xa49640: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa49644: r16 = "glog("
    //     0xa49644: add             x16, PP, #0x57, lsl #12  ; [pp+0x57db0] "glog("
    //     0xa49648: ldr             x16, [x16, #0xdb0]
    // 0xa4964c: StoreField: r0->field_f = r16
    //     0xa4964c: stur            w16, [x0, #0xf]
    // 0xa49650: ldur            x1, [fp, #-0x38]
    // 0xa49654: lsl             x2, x1, #1
    // 0xa49658: StoreField: r0->field_13 = r2
    //     0xa49658: stur            w2, [x0, #0x13]
    // 0xa4965c: r16 = ")"
    //     0xa4965c: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xa49660: ArrayStore: r0[0] = r16  ; List_4
    //     0xa49660: stur            w16, [x0, #0x17]
    // 0xa49664: str             x0, [SP]
    // 0xa49668: r0 = _interpolate()
    //     0xa49668: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa4966c: stur            x0, [fp, #-0x18]
    // 0xa49670: r0 = ArgumentError()
    //     0xa49670: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xa49674: mov             x1, x0
    // 0xa49678: ldur            x0, [fp, #-0x18]
    // 0xa4967c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4967c: stur            w0, [x1, #0x17]
    // 0xa49680: r0 = false
    //     0xa49680: add             x0, NULL, #0x30  ; false
    // 0xa49684: StoreField: r1->field_b = r0
    //     0xa49684: stur            w0, [x1, #0xb]
    // 0xa49688: mov             x0, x1
    // 0xa4968c: r0 = Throw()
    //     0xa4968c: bl              #0xec04b8  ; ThrowStub
    // 0xa49690: brk             #0
    // 0xa49694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49694: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49698: b               #0xa493f8
    // 0xa4969c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4969c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa496a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa496a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa496a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa496a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa496a8: b               #0xa494b4
    // 0xa496ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa496ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa496b0: b               #0xa494f8
    // 0xa496b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa496b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa496b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa496b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa496bc: add             x4, x4, x2
    // 0xa496c0: b               #0xa495c0
    // 0xa496c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa496c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  int [](QrPolynomial, int) {
    // ** addr: 0xa496e0, size: 0xa0
    // 0xa496e0: EnterFrame
    //     0xa496e0: stp             fp, lr, [SP, #-0x10]!
    //     0xa496e4: mov             fp, SP
    // 0xa496e8: ldr             x0, [fp, #0x10]
    // 0xa496ec: r2 = Null
    //     0xa496ec: mov             x2, NULL
    // 0xa496f0: r1 = Null
    //     0xa496f0: mov             x1, NULL
    // 0xa496f4: branchIfSmi(r0, 0xa4971c)
    //     0xa496f4: tbz             w0, #0, #0xa4971c
    // 0xa496f8: r4 = LoadClassIdInstr(r0)
    //     0xa496f8: ldur            x4, [x0, #-1]
    //     0xa496fc: ubfx            x4, x4, #0xc, #0x14
    // 0xa49700: sub             x4, x4, #0x3c
    // 0xa49704: cmp             x4, #1
    // 0xa49708: b.ls            #0xa4971c
    // 0xa4970c: r8 = int
    //     0xa4970c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa49710: r3 = Null
    //     0xa49710: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5af18] Null
    //     0xa49714: ldr             x3, [x3, #0xf18]
    // 0xa49718: r0 = int()
    //     0xa49718: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa4971c: ldr             x2, [fp, #0x18]
    // 0xa49720: LoadField: r3 = r2->field_7
    //     0xa49720: ldur            w3, [x2, #7]
    // 0xa49724: DecompressPointer r3
    //     0xa49724: add             x3, x3, HEAP, lsl #32
    // 0xa49728: LoadField: r2 = r3->field_13
    //     0xa49728: ldur            w2, [x3, #0x13]
    // 0xa4972c: ldr             x4, [fp, #0x10]
    // 0xa49730: r5 = LoadInt32Instr(r4)
    //     0xa49730: sbfx            x5, x4, #1, #0x1f
    //     0xa49734: tbz             w4, #0, #0xa4973c
    //     0xa49738: ldur            x5, [x4, #7]
    // 0xa4973c: r0 = LoadInt32Instr(r2)
    //     0xa4973c: sbfx            x0, x2, #1, #0x1f
    // 0xa49740: mov             x1, x5
    // 0xa49744: cmp             x1, x0
    // 0xa49748: b.hs            #0xa49764
    // 0xa4974c: ArrayLoad: r1 = r3[r5]  ; List_1
    //     0xa4974c: add             x16, x3, x5
    //     0xa49750: ldrb            w1, [x16, #0x17]
    // 0xa49754: lsl             x0, x1, #1
    // 0xa49758: LeaveFrame
    //     0xa49758: mov             SP, fp
    //     0xa4975c: ldp             fp, lr, [SP], #0x10
    // 0xa49760: ret
    //     0xa49760: ret             
    // 0xa49764: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa49764: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  factory _ QrPolynomial(/* No info */) {
    // ** addr: 0xa49a34, size: 0x22c
    // 0xa49a34: EnterFrame
    //     0xa49a34: stp             fp, lr, [SP, #-0x10]!
    //     0xa49a38: mov             fp, SP
    // 0xa49a3c: AllocStack(0x38)
    //     0xa49a3c: sub             SP, SP, #0x38
    // 0xa49a40: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa49a40: stur            x2, [fp, #-0x10]
    //     0xa49a44: stur            x3, [fp, #-0x18]
    // 0xa49a48: CheckStackOverflow
    //     0xa49a48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49a4c: cmp             SP, x16
    //     0xa49a50: b.ls            #0xa49c44
    // 0xa49a54: r1 = 0
    //     0xa49a54: movz            x1, #0
    // 0xa49a58: stur            x1, [fp, #-8]
    // 0xa49a5c: CheckStackOverflow
    //     0xa49a5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49a60: cmp             SP, x16
    //     0xa49a64: b.ls            #0xa49c4c
    // 0xa49a68: r0 = LoadClassIdInstr(r2)
    //     0xa49a68: ldur            x0, [x2, #-1]
    //     0xa49a6c: ubfx            x0, x0, #0xc, #0x14
    // 0xa49a70: str             x2, [SP]
    // 0xa49a74: r0 = GDT[cid_x0 + 0xc834]()
    //     0xa49a74: movz            x17, #0xc834
    //     0xa49a78: add             lr, x0, x17
    //     0xa49a7c: ldr             lr, [x21, lr, lsl #3]
    //     0xa49a80: blr             lr
    // 0xa49a84: r1 = LoadInt32Instr(r0)
    //     0xa49a84: sbfx            x1, x0, #1, #0x1f
    // 0xa49a88: ldur            x2, [fp, #-8]
    // 0xa49a8c: cmp             x2, x1
    // 0xa49a90: b.ge            #0xa49af4
    // 0xa49a94: ldur            x3, [fp, #-0x10]
    // 0xa49a98: r0 = BoxInt64Instr(r2)
    //     0xa49a98: sbfiz           x0, x2, #1, #0x1f
    //     0xa49a9c: cmp             x2, x0, asr #1
    //     0xa49aa0: b.eq            #0xa49aac
    //     0xa49aa4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa49aa8: stur            x2, [x0, #7]
    // 0xa49aac: r1 = LoadClassIdInstr(r3)
    //     0xa49aac: ldur            x1, [x3, #-1]
    //     0xa49ab0: ubfx            x1, x1, #0xc, #0x14
    // 0xa49ab4: stp             x0, x3, [SP]
    // 0xa49ab8: mov             x0, x1
    // 0xa49abc: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa49abc: movz            x17, #0x3037
    //     0xa49ac0: movk            x17, #0x1, lsl #16
    //     0xa49ac4: add             lr, x0, x17
    //     0xa49ac8: ldr             lr, [x21, lr, lsl #3]
    //     0xa49acc: blr             lr
    // 0xa49ad0: cbnz            w0, #0xa49aec
    // 0xa49ad4: ldur            x1, [fp, #-8]
    // 0xa49ad8: add             x0, x1, #1
    // 0xa49adc: mov             x1, x0
    // 0xa49ae0: ldur            x2, [fp, #-0x10]
    // 0xa49ae4: ldur            x3, [fp, #-0x18]
    // 0xa49ae8: b               #0xa49a58
    // 0xa49aec: ldur            x1, [fp, #-8]
    // 0xa49af0: b               #0xa49af8
    // 0xa49af4: mov             x1, x2
    // 0xa49af8: ldur            x2, [fp, #-0x10]
    // 0xa49afc: ldur            x3, [fp, #-0x18]
    // 0xa49b00: r0 = LoadClassIdInstr(r2)
    //     0xa49b00: ldur            x0, [x2, #-1]
    //     0xa49b04: ubfx            x0, x0, #0xc, #0x14
    // 0xa49b08: str             x2, [SP]
    // 0xa49b0c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xa49b0c: movz            x17, #0xc834
    //     0xa49b10: add             lr, x0, x17
    //     0xa49b14: ldr             lr, [x21, lr, lsl #3]
    //     0xa49b18: blr             lr
    // 0xa49b1c: r1 = LoadInt32Instr(r0)
    //     0xa49b1c: sbfx            x1, x0, #1, #0x1f
    // 0xa49b20: ldur            x2, [fp, #-8]
    // 0xa49b24: sub             x0, x1, x2
    // 0xa49b28: ldur            x1, [fp, #-0x18]
    // 0xa49b2c: add             x3, x0, x1
    // 0xa49b30: stur            x3, [fp, #-0x20]
    // 0xa49b34: r0 = BoxInt64Instr(r3)
    //     0xa49b34: sbfiz           x0, x3, #1, #0x1f
    //     0xa49b38: cmp             x3, x0, asr #1
    //     0xa49b3c: b.eq            #0xa49b48
    //     0xa49b40: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa49b44: stur            x3, [x0, #7]
    // 0xa49b48: mov             x4, x0
    // 0xa49b4c: r0 = AllocateUint8Array()
    //     0xa49b4c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xa49b50: mov             x1, x0
    // 0xa49b54: stur            x1, [fp, #-0x28]
    // 0xa49b58: r4 = 0
    //     0xa49b58: movz            x4, #0
    // 0xa49b5c: ldur            x3, [fp, #-0x10]
    // 0xa49b60: ldur            x2, [fp, #-8]
    // 0xa49b64: stur            x4, [fp, #-0x18]
    // 0xa49b68: CheckStackOverflow
    //     0xa49b68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49b6c: cmp             SP, x16
    //     0xa49b70: b.ls            #0xa49c54
    // 0xa49b74: r0 = LoadClassIdInstr(r3)
    //     0xa49b74: ldur            x0, [x3, #-1]
    //     0xa49b78: ubfx            x0, x0, #0xc, #0x14
    // 0xa49b7c: str             x3, [SP]
    // 0xa49b80: r0 = GDT[cid_x0 + 0xc834]()
    //     0xa49b80: movz            x17, #0xc834
    //     0xa49b84: add             lr, x0, x17
    //     0xa49b88: ldr             lr, [x21, lr, lsl #3]
    //     0xa49b8c: blr             lr
    // 0xa49b90: r1 = LoadInt32Instr(r0)
    //     0xa49b90: sbfx            x1, x0, #1, #0x1f
    // 0xa49b94: ldur            x2, [fp, #-8]
    // 0xa49b98: sub             x0, x1, x2
    // 0xa49b9c: ldur            x3, [fp, #-0x18]
    // 0xa49ba0: cmp             x3, x0
    // 0xa49ba4: b.ge            #0xa49c28
    // 0xa49ba8: ldur            x5, [fp, #-0x10]
    // 0xa49bac: ldur            x4, [fp, #-0x28]
    // 0xa49bb0: add             x6, x3, x2
    // 0xa49bb4: r0 = BoxInt64Instr(r6)
    //     0xa49bb4: sbfiz           x0, x6, #1, #0x1f
    //     0xa49bb8: cmp             x6, x0, asr #1
    //     0xa49bbc: b.eq            #0xa49bc8
    //     0xa49bc0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa49bc4: stur            x6, [x0, #7]
    // 0xa49bc8: r1 = LoadClassIdInstr(r5)
    //     0xa49bc8: ldur            x1, [x5, #-1]
    //     0xa49bcc: ubfx            x1, x1, #0xc, #0x14
    // 0xa49bd0: stp             x0, x5, [SP]
    // 0xa49bd4: mov             x0, x1
    // 0xa49bd8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa49bd8: movz            x17, #0x3037
    //     0xa49bdc: movk            x17, #0x1, lsl #16
    //     0xa49be0: add             lr, x0, x17
    //     0xa49be4: ldr             lr, [x21, lr, lsl #3]
    //     0xa49be8: blr             lr
    // 0xa49bec: mov             x2, x0
    // 0xa49bf0: ldur            x0, [fp, #-0x20]
    // 0xa49bf4: ldur            x1, [fp, #-0x18]
    // 0xa49bf8: cmp             x1, x0
    // 0xa49bfc: b.hs            #0xa49c5c
    // 0xa49c00: r0 = LoadInt32Instr(r2)
    //     0xa49c00: sbfx            x0, x2, #1, #0x1f
    //     0xa49c04: tbz             w2, #0, #0xa49c0c
    //     0xa49c08: ldur            x0, [x2, #7]
    // 0xa49c0c: ldur            x1, [fp, #-0x18]
    // 0xa49c10: ldur            x2, [fp, #-0x28]
    // 0xa49c14: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa49c14: add             x3, x2, x1
    //     0xa49c18: strb            w0, [x3, #0x17]
    // 0xa49c1c: add             x4, x1, #1
    // 0xa49c20: mov             x1, x2
    // 0xa49c24: b               #0xa49b5c
    // 0xa49c28: ldur            x2, [fp, #-0x28]
    // 0xa49c2c: r0 = QrPolynomial()
    //     0xa49c2c: bl              #0xa49c60  ; AllocateQrPolynomialStub -> QrPolynomial (size=0xc)
    // 0xa49c30: ldur            x1, [fp, #-0x28]
    // 0xa49c34: StoreField: r0->field_7 = r1
    //     0xa49c34: stur            w1, [x0, #7]
    // 0xa49c38: LeaveFrame
    //     0xa49c38: mov             SP, fp
    //     0xa49c3c: ldp             fp, lr, [SP], #0x10
    // 0xa49c40: ret
    //     0xa49c40: ret             
    // 0xa49c44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49c44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49c48: b               #0xa49a54
    // 0xa49c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49c4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49c50: b               #0xa49a68
    // 0xa49c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49c54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49c58: b               #0xa49b74
    // 0xa49c5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa49c5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ multiply(/* No info */) {
    // ** addr: 0xa49df8, size: 0x348
    // 0xa49df8: EnterFrame
    //     0xa49df8: stp             fp, lr, [SP, #-0x10]!
    //     0xa49dfc: mov             fp, SP
    // 0xa49e00: AllocStack(0x70)
    //     0xa49e00: sub             SP, SP, #0x70
    // 0xa49e04: CheckStackOverflow
    //     0xa49e04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49e08: cmp             SP, x16
    //     0xa49e0c: b.ls            #0xa4a110
    // 0xa49e10: LoadField: r3 = r1->field_7
    //     0xa49e10: ldur            w3, [x1, #7]
    // 0xa49e14: DecompressPointer r3
    //     0xa49e14: add             x3, x3, HEAP, lsl #32
    // 0xa49e18: stur            x3, [fp, #-0x28]
    // 0xa49e1c: LoadField: r0 = r3->field_13
    //     0xa49e1c: ldur            w0, [x3, #0x13]
    // 0xa49e20: LoadField: r5 = r2->field_7
    //     0xa49e20: ldur            w5, [x2, #7]
    // 0xa49e24: DecompressPointer r5
    //     0xa49e24: add             x5, x5, HEAP, lsl #32
    // 0xa49e28: stur            x5, [fp, #-0x20]
    // 0xa49e2c: LoadField: r1 = r5->field_13
    //     0xa49e2c: ldur            w1, [x5, #0x13]
    // 0xa49e30: r2 = LoadInt32Instr(r0)
    //     0xa49e30: sbfx            x2, x0, #1, #0x1f
    // 0xa49e34: stur            x2, [fp, #-0x18]
    // 0xa49e38: r6 = LoadInt32Instr(r1)
    //     0xa49e38: sbfx            x6, x1, #1, #0x1f
    // 0xa49e3c: stur            x6, [fp, #-0x10]
    // 0xa49e40: add             x0, x2, x6
    // 0xa49e44: sub             x7, x0, #1
    // 0xa49e48: stur            x7, [fp, #-8]
    // 0xa49e4c: r0 = BoxInt64Instr(r7)
    //     0xa49e4c: sbfiz           x0, x7, #1, #0x1f
    //     0xa49e50: cmp             x7, x0, asr #1
    //     0xa49e54: b.eq            #0xa49e60
    //     0xa49e58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa49e5c: stur            x7, [x0, #7]
    // 0xa49e60: mov             x4, x0
    // 0xa49e64: r0 = AllocateUint8Array()
    //     0xa49e64: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xa49e68: mov             x2, x0
    // 0xa49e6c: stur            x2, [fp, #-0x58]
    // 0xa49e70: r7 = 0
    //     0xa49e70: movz            x7, #0
    // 0xa49e74: ldur            x3, [fp, #-0x28]
    // 0xa49e78: ldur            x4, [fp, #-0x20]
    // 0xa49e7c: ldur            x5, [fp, #-0x18]
    // 0xa49e80: ldur            x6, [fp, #-0x10]
    // 0xa49e84: stur            x7, [fp, #-0x50]
    // 0xa49e88: CheckStackOverflow
    //     0xa49e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49e8c: cmp             SP, x16
    //     0xa49e90: b.ls            #0xa4a118
    // 0xa49e94: cmp             x7, x5
    // 0xa49e98: b.ge            #0xa4a030
    // 0xa49e9c: r8 = 0
    //     0xa49e9c: movz            x8, #0
    // 0xa49ea0: stur            x8, [fp, #-0x48]
    // 0xa49ea4: CheckStackOverflow
    //     0xa49ea4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49ea8: cmp             SP, x16
    //     0xa49eac: b.ls            #0xa4a120
    // 0xa49eb0: cmp             x8, x6
    // 0xa49eb4: b.ge            #0xa4a018
    // 0xa49eb8: add             x9, x7, x8
    // 0xa49ebc: ldur            x0, [fp, #-8]
    // 0xa49ec0: mov             x1, x9
    // 0xa49ec4: stur            x9, [fp, #-0x40]
    // 0xa49ec8: cmp             x1, x0
    // 0xa49ecc: b.hs            #0xa4a128
    // 0xa49ed0: ArrayLoad: r0 = r2[r9]  ; List_1
    //     0xa49ed0: add             x16, x2, x9
    //     0xa49ed4: ldrb            w0, [x16, #0x17]
    // 0xa49ed8: stur            x0, [fp, #-0x38]
    // 0xa49edc: ArrayLoad: r1 = r3[r7]  ; List_1
    //     0xa49edc: add             x16, x3, x7
    //     0xa49ee0: ldrb            w1, [x16, #0x17]
    // 0xa49ee4: stur            x1, [fp, #-0x30]
    // 0xa49ee8: cmp             x1, #1
    // 0xa49eec: b.lt            #0xa4a0ac
    // 0xa49ef0: r0 = InitLateStaticField(0x1738) // [package:qr/src/math.dart] ::_logTable
    //     0xa49ef0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49ef4: ldr             x0, [x0, #0x2e70]
    //     0xa49ef8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa49efc: cmp             w0, w16
    //     0xa49f00: b.ne            #0xa49f10
    //     0xa49f04: add             x2, PP, #0x57, lsl #12  ; [pp+0x57da0] Field <::._logTable@2681014454>: static late final (offset: 0x1738)
    //     0xa49f08: ldr             x2, [x2, #0xda0]
    //     0xa49f0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa49f10: mov             x2, x0
    // 0xa49f14: LoadField: r0 = r2->field_13
    //     0xa49f14: ldur            w0, [x2, #0x13]
    // 0xa49f18: r3 = LoadInt32Instr(r0)
    //     0xa49f18: sbfx            x3, x0, #1, #0x1f
    // 0xa49f1c: mov             x0, x3
    // 0xa49f20: ldur            x1, [fp, #-0x30]
    // 0xa49f24: cmp             x1, x0
    // 0xa49f28: b.hs            #0xa4a12c
    // 0xa49f2c: ldur            x0, [fp, #-0x30]
    // 0xa49f30: ArrayLoad: r4 = r2[r0]  ; List_1
    //     0xa49f30: add             x16, x2, x0
    //     0xa49f34: ldrb            w4, [x16, #0x17]
    // 0xa49f38: ldur            x6, [fp, #-0x48]
    // 0xa49f3c: ldur            x5, [fp, #-0x20]
    // 0xa49f40: ArrayLoad: r7 = r5[r6]  ; List_1
    //     0xa49f40: add             x16, x5, x6
    //     0xa49f44: ldrb            w7, [x16, #0x17]
    // 0xa49f48: stur            x7, [fp, #-0x68]
    // 0xa49f4c: cmp             x7, #1
    // 0xa49f50: b.lt            #0xa4a050
    // 0xa49f54: ldur            x9, [fp, #-0x40]
    // 0xa49f58: ldur            x8, [fp, #-0x58]
    // 0xa49f5c: ldur            x10, [fp, #-0x38]
    // 0xa49f60: mov             x0, x3
    // 0xa49f64: mov             x1, x7
    // 0xa49f68: cmp             x1, x0
    // 0xa49f6c: b.hs            #0xa4a130
    // 0xa49f70: ArrayLoad: r0 = r2[r7]  ; List_1
    //     0xa49f70: add             x16, x2, x7
    //     0xa49f74: ldrb            w0, [x16, #0x17]
    // 0xa49f78: add             x1, x4, x0
    // 0xa49f7c: stur            x1, [fp, #-0x60]
    // 0xa49f80: r0 = InitLateStaticField(0x173c) // [package:qr/src/math.dart] ::_expTable
    //     0xa49f80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49f84: ldr             x0, [x0, #0x2e78]
    //     0xa49f88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa49f8c: cmp             w0, w16
    //     0xa49f90: b.ne            #0xa49fa0
    //     0xa49f94: add             x2, PP, #0x57, lsl #12  ; [pp+0x57da8] Field <::._expTable@2681014454>: static late final (offset: 0x173c)
    //     0xa49f98: ldr             x2, [x2, #0xda8]
    //     0xa49f9c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa49fa0: mov             x3, x0
    // 0xa49fa4: ldur            x0, [fp, #-0x60]
    // 0xa49fa8: r2 = 255
    //     0xa49fa8: movz            x2, #0xff
    // 0xa49fac: sdiv            x1, x0, x2
    // 0xa49fb0: msub            x4, x1, x2, x0
    // 0xa49fb4: cmp             x4, xzr
    // 0xa49fb8: b.lt            #0xa4a134
    // 0xa49fbc: LoadField: r0 = r3->field_13
    //     0xa49fbc: ldur            w0, [x3, #0x13]
    // 0xa49fc0: r1 = LoadInt32Instr(r0)
    //     0xa49fc0: sbfx            x1, x0, #1, #0x1f
    // 0xa49fc4: mov             x0, x1
    // 0xa49fc8: mov             x1, x4
    // 0xa49fcc: cmp             x1, x0
    // 0xa49fd0: b.hs            #0xa4a13c
    // 0xa49fd4: ArrayLoad: r0 = r3[r4]  ; List_1
    //     0xa49fd4: add             x16, x3, x4
    //     0xa49fd8: ldrb            w0, [x16, #0x17]
    // 0xa49fdc: ldur            x1, [fp, #-0x38]
    // 0xa49fe0: eor             x3, x1, x0
    // 0xa49fe4: ldur            x1, [fp, #-0x40]
    // 0xa49fe8: ldur            x0, [fp, #-0x58]
    // 0xa49fec: ArrayStore: r0[r1] = r3  ; TypeUnknown_1
    //     0xa49fec: add             x4, x0, x1
    //     0xa49ff0: strb            w3, [x4, #0x17]
    // 0xa49ff4: ldur            x1, [fp, #-0x48]
    // 0xa49ff8: add             x8, x1, #1
    // 0xa49ffc: ldur            x7, [fp, #-0x50]
    // 0xa4a000: ldur            x3, [fp, #-0x28]
    // 0xa4a004: ldur            x4, [fp, #-0x20]
    // 0xa4a008: mov             x2, x0
    // 0xa4a00c: ldur            x5, [fp, #-0x18]
    // 0xa4a010: ldur            x6, [fp, #-0x10]
    // 0xa4a014: b               #0xa49ea0
    // 0xa4a018: mov             x1, x7
    // 0xa4a01c: mov             x0, x2
    // 0xa4a020: r2 = 255
    //     0xa4a020: movz            x2, #0xff
    // 0xa4a024: add             x7, x1, #1
    // 0xa4a028: mov             x2, x0
    // 0xa4a02c: b               #0xa49e74
    // 0xa4a030: mov             x0, x2
    // 0xa4a034: mov             x2, x0
    // 0xa4a038: r1 = Null
    //     0xa4a038: mov             x1, NULL
    // 0xa4a03c: r3 = 0
    //     0xa4a03c: movz            x3, #0
    // 0xa4a040: r0 = QrPolynomial()
    //     0xa4a040: bl              #0xa49a34  ; [package:qr/src/polynomial.dart] QrPolynomial::QrPolynomial
    // 0xa4a044: LeaveFrame
    //     0xa4a044: mov             SP, fp
    //     0xa4a048: ldp             fp, lr, [SP], #0x10
    // 0xa4a04c: ret
    //     0xa4a04c: ret             
    // 0xa4a050: r1 = Null
    //     0xa4a050: mov             x1, NULL
    // 0xa4a054: r2 = 6
    //     0xa4a054: movz            x2, #0x6
    // 0xa4a058: r0 = AllocateArray()
    //     0xa4a058: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa4a05c: r16 = "glog("
    //     0xa4a05c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57db0] "glog("
    //     0xa4a060: ldr             x16, [x16, #0xdb0]
    // 0xa4a064: StoreField: r0->field_f = r16
    //     0xa4a064: stur            w16, [x0, #0xf]
    // 0xa4a068: ldur            x1, [fp, #-0x68]
    // 0xa4a06c: lsl             x2, x1, #1
    // 0xa4a070: StoreField: r0->field_13 = r2
    //     0xa4a070: stur            w2, [x0, #0x13]
    // 0xa4a074: r16 = ")"
    //     0xa4a074: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xa4a078: ArrayStore: r0[0] = r16  ; List_4
    //     0xa4a078: stur            w16, [x0, #0x17]
    // 0xa4a07c: str             x0, [SP]
    // 0xa4a080: r0 = _interpolate()
    //     0xa4a080: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa4a084: stur            x0, [fp, #-0x20]
    // 0xa4a088: r0 = ArgumentError()
    //     0xa4a088: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xa4a08c: mov             x1, x0
    // 0xa4a090: ldur            x0, [fp, #-0x20]
    // 0xa4a094: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4a094: stur            w0, [x1, #0x17]
    // 0xa4a098: r3 = false
    //     0xa4a098: add             x3, NULL, #0x30  ; false
    // 0xa4a09c: StoreField: r1->field_b = r3
    //     0xa4a09c: stur            w3, [x1, #0xb]
    // 0xa4a0a0: mov             x0, x1
    // 0xa4a0a4: r0 = Throw()
    //     0xa4a0a4: bl              #0xec04b8  ; ThrowStub
    // 0xa4a0a8: brk             #0
    // 0xa4a0ac: mov             x0, x1
    // 0xa4a0b0: r3 = false
    //     0xa4a0b0: add             x3, NULL, #0x30  ; false
    // 0xa4a0b4: r1 = Null
    //     0xa4a0b4: mov             x1, NULL
    // 0xa4a0b8: r2 = 6
    //     0xa4a0b8: movz            x2, #0x6
    // 0xa4a0bc: r0 = AllocateArray()
    //     0xa4a0bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa4a0c0: r16 = "glog("
    //     0xa4a0c0: add             x16, PP, #0x57, lsl #12  ; [pp+0x57db0] "glog("
    //     0xa4a0c4: ldr             x16, [x16, #0xdb0]
    // 0xa4a0c8: StoreField: r0->field_f = r16
    //     0xa4a0c8: stur            w16, [x0, #0xf]
    // 0xa4a0cc: ldur            x1, [fp, #-0x30]
    // 0xa4a0d0: lsl             x2, x1, #1
    // 0xa4a0d4: StoreField: r0->field_13 = r2
    //     0xa4a0d4: stur            w2, [x0, #0x13]
    // 0xa4a0d8: r16 = ")"
    //     0xa4a0d8: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xa4a0dc: ArrayStore: r0[0] = r16  ; List_4
    //     0xa4a0dc: stur            w16, [x0, #0x17]
    // 0xa4a0e0: str             x0, [SP]
    // 0xa4a0e4: r0 = _interpolate()
    //     0xa4a0e4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa4a0e8: stur            x0, [fp, #-0x20]
    // 0xa4a0ec: r0 = ArgumentError()
    //     0xa4a0ec: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xa4a0f0: mov             x1, x0
    // 0xa4a0f4: ldur            x0, [fp, #-0x20]
    // 0xa4a0f8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4a0f8: stur            w0, [x1, #0x17]
    // 0xa4a0fc: r0 = false
    //     0xa4a0fc: add             x0, NULL, #0x30  ; false
    // 0xa4a100: StoreField: r1->field_b = r0
    //     0xa4a100: stur            w0, [x1, #0xb]
    // 0xa4a104: mov             x0, x1
    // 0xa4a108: r0 = Throw()
    //     0xa4a108: bl              #0xec04b8  ; ThrowStub
    // 0xa4a10c: brk             #0
    // 0xa4a110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a110: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a114: b               #0xa49e10
    // 0xa4a118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a118: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a11c: b               #0xa49e94
    // 0xa4a120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a124: b               #0xa49eb0
    // 0xa4a128: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4a128: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa4a12c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4a12c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa4a130: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4a130: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa4a134: add             x4, x4, x2
    // 0xa4a138: b               #0xa49fbc
    // 0xa4a13c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4a13c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
