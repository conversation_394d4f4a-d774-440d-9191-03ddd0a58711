// lib: , url: package:qr/src/qr_code.dart

// class id: 1051067, size: 0x8
class :: {

  static _ _createData(/* No info */) {
    // ** addr: 0xa482cc, size: 0x69c
    // 0xa482cc: EnterFrame
    //     0xa482cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa482d0: mov             fp, SP
    // 0xa482d4: AllocStack(0x68)
    //     0xa482d4: sub             SP, SP, #0x68
    // 0xa482d8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa482d8: mov             x0, x1
    //     0xa482dc: stur            x1, [fp, #-8]
    //     0xa482e0: stur            x2, [fp, #-0x10]
    // 0xa482e4: CheckStackOverflow
    //     0xa482e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa482e8: cmp             SP, x16
    //     0xa482ec: b.ls            #0xa4886c
    // 0xa482f0: mov             x1, x0
    // 0xa482f4: r0 = getRSBlocks()
    //     0xa482f4: bl              #0xa452c0  ; [package:qr/src/rs_block.dart] QrRsBlock::getRSBlocks
    // 0xa482f8: stur            x0, [fp, #-0x18]
    // 0xa482fc: r0 = QrBitBuffer()
    //     0xa482fc: bl              #0xa452b4  ; AllocateQrBitBufferStub -> QrBitBuffer (size=0x14)
    // 0xa48300: stur            x0, [fp, #-0x20]
    // 0xa48304: StoreField: r0->field_b = rZR
    //     0xa48304: stur            xzr, [x0, #0xb]
    // 0xa48308: r1 = <int>
    //     0xa48308: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xa4830c: r2 = 0
    //     0xa4830c: movz            x2, #0
    // 0xa48310: r0 = _GrowableList()
    //     0xa48310: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa48314: mov             x4, x0
    // 0xa48318: ldur            x3, [fp, #-0x20]
    // 0xa4831c: stur            x4, [fp, #-0x40]
    // 0xa48320: StoreField: r3->field_7 = r0
    //     0xa48320: stur            w0, [x3, #7]
    //     0xa48324: ldurb           w16, [x3, #-1]
    //     0xa48328: ldurb           w17, [x0, #-1]
    //     0xa4832c: and             x16, x17, x16, lsr #2
    //     0xa48330: tst             x16, HEAP, lsr #32
    //     0xa48334: b.eq            #0xa4833c
    //     0xa48338: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa4833c: r9 = 0
    //     0xa4833c: movz            x9, #0
    // 0xa48340: ldur            x5, [fp, #-8]
    // 0xa48344: ldur            x0, [fp, #-0x10]
    // 0xa48348: r8 = 4
    //     0xa48348: movz            x8, #0x4
    // 0xa4834c: r7 = 4
    //     0xa4834c: movz            x7, #0x4
    // 0xa48350: r6 = 1
    //     0xa48350: movz            x6, #0x1
    // 0xa48354: stur            x9, [fp, #-0x38]
    // 0xa48358: CheckStackOverflow
    //     0xa48358: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4835c: cmp             SP, x16
    //     0xa48360: b.ls            #0xa48874
    // 0xa48364: LoadField: r1 = r0->field_b
    //     0xa48364: ldur            w1, [x0, #0xb]
    // 0xa48368: r2 = LoadInt32Instr(r1)
    //     0xa48368: sbfx            x2, x1, #1, #0x1f
    // 0xa4836c: cmp             x9, x2
    // 0xa48370: b.ge            #0xa485d4
    // 0xa48374: LoadField: r1 = r0->field_f
    //     0xa48374: ldur            w1, [x0, #0xf]
    // 0xa48378: DecompressPointer r1
    //     0xa48378: add             x1, x1, HEAP, lsl #32
    // 0xa4837c: ArrayLoad: r10 = r1[r9]  ; Unknown_4
    //     0xa4837c: add             x16, x1, x9, lsl #2
    //     0xa48380: ldur            w10, [x16, #0xf]
    // 0xa48384: DecompressPointer r10
    //     0xa48384: add             x10, x10, HEAP, lsl #32
    // 0xa48388: stur            x10, [fp, #-0x30]
    // 0xa4838c: r11 = 0
    //     0xa4838c: movz            x11, #0
    // 0xa48390: stur            x11, [fp, #-0x28]
    // 0xa48394: CheckStackOverflow
    //     0xa48394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48398: cmp             SP, x16
    //     0xa4839c: b.ls            #0xa4887c
    // 0xa483a0: cmp             x11, #4
    // 0xa483a4: b.ge            #0xa48414
    // 0xa483a8: sub             x1, x8, x11
    // 0xa483ac: sub             x2, x1, #1
    // 0xa483b0: tbnz            x2, #0x3f, #0xa48884
    // 0xa483b4: lsr             w1, w7, w2
    // 0xa483b8: cmp             x2, #0x1f
    // 0xa483bc: csel            x1, x1, xzr, le
    // 0xa483c0: and             x2, x1, x6
    // 0xa483c4: ubfx            x2, x2, #0, #0x20
    // 0xa483c8: cmp             x2, #1
    // 0xa483cc: r16 = true
    //     0xa483cc: add             x16, NULL, #0x20  ; true
    // 0xa483d0: r17 = false
    //     0xa483d0: add             x17, NULL, #0x30  ; false
    // 0xa483d4: csel            x1, x16, x17, eq
    // 0xa483d8: mov             x2, x1
    // 0xa483dc: mov             x1, x3
    // 0xa483e0: r0 = putBit()
    //     0xa483e0: bl              #0xa4513c  ; [package:qr/src/bit_buffer.dart] QrBitBuffer::putBit
    // 0xa483e4: ldur            x0, [fp, #-0x28]
    // 0xa483e8: add             x11, x0, #1
    // 0xa483ec: ldur            x5, [fp, #-8]
    // 0xa483f0: ldur            x0, [fp, #-0x10]
    // 0xa483f4: ldur            x3, [fp, #-0x20]
    // 0xa483f8: ldur            x9, [fp, #-0x38]
    // 0xa483fc: ldur            x4, [fp, #-0x40]
    // 0xa48400: ldur            x10, [fp, #-0x30]
    // 0xa48404: r8 = 4
    //     0xa48404: movz            x8, #0x4
    // 0xa48408: r7 = 4
    //     0xa48408: movz            x7, #0x4
    // 0xa4840c: r6 = 1
    //     0xa4840c: movz            x6, #0x1
    // 0xa48410: b               #0xa48390
    // 0xa48414: mov             x0, x5
    // 0xa48418: mov             x1, x10
    // 0xa4841c: LoadField: r3 = r1->field_f
    //     0xa4841c: ldur            w3, [x1, #0xf]
    // 0xa48420: DecompressPointer r3
    //     0xa48420: add             x3, x3, HEAP, lsl #32
    // 0xa48424: stur            x3, [fp, #-0x58]
    // 0xa48428: LoadField: r4 = r3->field_13
    //     0xa48428: ldur            w4, [x3, #0x13]
    // 0xa4842c: stur            x4, [fp, #-0x30]
    // 0xa48430: cmp             x0, #1
    // 0xa48434: b.lt            #0xa48448
    // 0xa48438: cmp             x0, #0xa
    // 0xa4843c: b.ge            #0xa48448
    // 0xa48440: r5 = 8
    //     0xa48440: movz            x5, #0x8
    // 0xa48444: b               #0xa48464
    // 0xa48448: cmp             x0, #0x1b
    // 0xa4844c: b.ge            #0xa48458
    // 0xa48450: r5 = 16
    //     0xa48450: movz            x5, #0x10
    // 0xa48454: b               #0xa48464
    // 0xa48458: cmp             x0, #0x29
    // 0xa4845c: b.ge            #0xa487f0
    // 0xa48460: r5 = 16
    //     0xa48460: movz            x5, #0x10
    // 0xa48464: stur            x5, [fp, #-0x50]
    // 0xa48468: r6 = LoadInt32Instr(r4)
    //     0xa48468: sbfx            x6, x4, #1, #0x1f
    // 0xa4846c: stur            x6, [fp, #-0x48]
    // 0xa48470: r8 = 0
    //     0xa48470: movz            x8, #0
    // 0xa48474: r7 = 1
    //     0xa48474: movz            x7, #0x1
    // 0xa48478: stur            x8, [fp, #-0x28]
    // 0xa4847c: CheckStackOverflow
    //     0xa4847c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48480: cmp             SP, x16
    //     0xa48484: b.ls            #0xa488b4
    // 0xa48488: cmp             x8, x5
    // 0xa4848c: b.ge            #0xa484ec
    // 0xa48490: sub             x1, x5, x8
    // 0xa48494: sub             x2, x1, #1
    // 0xa48498: tbnz            x2, #0x3f, #0xa488bc
    // 0xa4849c: lsr             w1, w6, w2
    // 0xa484a0: cmp             x2, #0x1f
    // 0xa484a4: csel            x1, x1, xzr, le
    // 0xa484a8: and             x2, x1, x7
    // 0xa484ac: ubfx            x2, x2, #0, #0x20
    // 0xa484b0: cmp             x2, #1
    // 0xa484b4: r16 = true
    //     0xa484b4: add             x16, NULL, #0x20  ; true
    // 0xa484b8: r17 = false
    //     0xa484b8: add             x17, NULL, #0x30  ; false
    // 0xa484bc: csel            x1, x16, x17, eq
    // 0xa484c0: mov             x2, x1
    // 0xa484c4: ldur            x1, [fp, #-0x20]
    // 0xa484c8: r0 = putBit()
    //     0xa484c8: bl              #0xa4513c  ; [package:qr/src/bit_buffer.dart] QrBitBuffer::putBit
    // 0xa484cc: ldur            x0, [fp, #-0x28]
    // 0xa484d0: add             x8, x0, #1
    // 0xa484d4: ldur            x0, [fp, #-8]
    // 0xa484d8: ldur            x3, [fp, #-0x58]
    // 0xa484dc: ldur            x5, [fp, #-0x50]
    // 0xa484e0: ldur            x4, [fp, #-0x30]
    // 0xa484e4: ldur            x6, [fp, #-0x48]
    // 0xa484e8: b               #0xa48474
    // 0xa484ec: mov             x0, x4
    // 0xa484f0: r3 = LoadInt32Instr(r0)
    //     0xa484f0: sbfx            x3, x0, #1, #0x1f
    // 0xa484f4: stur            x3, [fp, #-0x60]
    // 0xa484f8: r1 = -1
    //     0xa484f8: movn            x1, #0
    // 0xa484fc: ldur            x0, [fp, #-0x58]
    // 0xa48500: r5 = 8
    //     0xa48500: movz            x5, #0x8
    // 0xa48504: r4 = 1
    //     0xa48504: movz            x4, #0x1
    // 0xa48508: CheckStackOverflow
    //     0xa48508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4850c: cmp             SP, x16
    //     0xa48510: b.ls            #0xa488e4
    // 0xa48514: add             x6, x1, #1
    // 0xa48518: stur            x6, [fp, #-0x50]
    // 0xa4851c: cmp             x6, x3
    // 0xa48520: b.ge            #0xa485c0
    // 0xa48524: ArrayLoad: r1 = r0[r6]  ; List_1
    //     0xa48524: add             x16, x0, x6
    //     0xa48528: ldrb            w1, [x16, #0x17]
    // 0xa4852c: mov             x7, x1
    // 0xa48530: ubfx            x7, x7, #0, #0x20
    // 0xa48534: stur            x7, [fp, #-0x48]
    // 0xa48538: r8 = 0
    //     0xa48538: movz            x8, #0
    // 0xa4853c: stur            x8, [fp, #-0x28]
    // 0xa48540: CheckStackOverflow
    //     0xa48540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48544: cmp             SP, x16
    //     0xa48548: b.ls            #0xa488ec
    // 0xa4854c: cmp             x8, #8
    // 0xa48550: b.ge            #0xa485b4
    // 0xa48554: sub             x1, x5, x8
    // 0xa48558: sub             x2, x1, #1
    // 0xa4855c: tbnz            x2, #0x3f, #0xa488f4
    // 0xa48560: lsr             w1, w7, w2
    // 0xa48564: cmp             x2, #0x1f
    // 0xa48568: csel            x1, x1, xzr, le
    // 0xa4856c: and             x2, x1, x4
    // 0xa48570: ubfx            x2, x2, #0, #0x20
    // 0xa48574: cmp             x2, #1
    // 0xa48578: r16 = true
    //     0xa48578: add             x16, NULL, #0x20  ; true
    // 0xa4857c: r17 = false
    //     0xa4857c: add             x17, NULL, #0x30  ; false
    // 0xa48580: csel            x1, x16, x17, eq
    // 0xa48584: mov             x2, x1
    // 0xa48588: ldur            x1, [fp, #-0x20]
    // 0xa4858c: r0 = putBit()
    //     0xa4858c: bl              #0xa4513c  ; [package:qr/src/bit_buffer.dart] QrBitBuffer::putBit
    // 0xa48590: ldur            x0, [fp, #-0x28]
    // 0xa48594: add             x8, x0, #1
    // 0xa48598: ldur            x0, [fp, #-0x58]
    // 0xa4859c: ldur            x6, [fp, #-0x50]
    // 0xa485a0: ldur            x3, [fp, #-0x60]
    // 0xa485a4: ldur            x7, [fp, #-0x48]
    // 0xa485a8: r5 = 8
    //     0xa485a8: movz            x5, #0x8
    // 0xa485ac: r4 = 1
    //     0xa485ac: movz            x4, #0x1
    // 0xa485b0: b               #0xa4853c
    // 0xa485b4: ldur            x1, [fp, #-0x50]
    // 0xa485b8: ldur            x3, [fp, #-0x60]
    // 0xa485bc: b               #0xa484fc
    // 0xa485c0: ldur            x0, [fp, #-0x38]
    // 0xa485c4: add             x9, x0, #1
    // 0xa485c8: ldur            x3, [fp, #-0x20]
    // 0xa485cc: ldur            x4, [fp, #-0x40]
    // 0xa485d0: b               #0xa48340
    // 0xa485d4: ldur            x0, [fp, #-0x18]
    // 0xa485d8: LoadField: r1 = r0->field_b
    //     0xa485d8: ldur            w1, [x0, #0xb]
    // 0xa485dc: r2 = LoadInt32Instr(r1)
    //     0xa485dc: sbfx            x2, x1, #1, #0x1f
    // 0xa485e0: LoadField: r1 = r0->field_f
    //     0xa485e0: ldur            w1, [x0, #0xf]
    // 0xa485e4: DecompressPointer r1
    //     0xa485e4: add             x1, x1, HEAP, lsl #32
    // 0xa485e8: r4 = 0
    //     0xa485e8: movz            x4, #0
    // 0xa485ec: r3 = 0
    //     0xa485ec: movz            x3, #0
    // 0xa485f0: CheckStackOverflow
    //     0xa485f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa485f4: cmp             SP, x16
    //     0xa485f8: b.ls            #0xa4891c
    // 0xa485fc: cmp             x3, x2
    // 0xa48600: b.ge            #0xa48628
    // 0xa48604: ArrayLoad: r5 = r1[r3]  ; Unknown_4
    //     0xa48604: add             x16, x1, x3, lsl #2
    //     0xa48608: ldur            w5, [x16, #0xf]
    // 0xa4860c: DecompressPointer r5
    //     0xa4860c: add             x5, x5, HEAP, lsl #32
    // 0xa48610: LoadField: r6 = r5->field_f
    //     0xa48610: ldur            x6, [x5, #0xf]
    // 0xa48614: add             x5, x4, x6
    // 0xa48618: add             x6, x3, #1
    // 0xa4861c: mov             x4, x5
    // 0xa48620: mov             x3, x6
    // 0xa48624: b               #0xa485f0
    // 0xa48628: ldur            x5, [fp, #-0x20]
    // 0xa4862c: lsl             x6, x4, #3
    // 0xa48630: stur            x6, [fp, #-0x28]
    // 0xa48634: LoadField: r2 = r5->field_b
    //     0xa48634: ldur            x2, [x5, #0xb]
    // 0xa48638: cmp             x2, x6
    // 0xa4863c: b.gt            #0xa48858
    // 0xa48640: add             x1, x2, #4
    // 0xa48644: cmp             x1, x6
    // 0xa48648: b.gt            #0xa4865c
    // 0xa4864c: mov             x1, x5
    // 0xa48650: r2 = 0
    //     0xa48650: movz            x2, #0
    // 0xa48654: r3 = 4
    //     0xa48654: movz            x3, #0x4
    // 0xa48658: r0 = put()
    //     0xa48658: bl              #0xa4504c  ; [package:qr/src/bit_buffer.dart] QrBitBuffer::put
    // 0xa4865c: ldur            x0, [fp, #-0x20]
    // 0xa48660: ldur            x3, [fp, #-0x40]
    // 0xa48664: r2 = 8
    //     0xa48664: movz            x2, #0x8
    // 0xa48668: CheckStackOverflow
    //     0xa48668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4866c: cmp             SP, x16
    //     0xa48670: b.ls            #0xa48924
    // 0xa48674: LoadField: r1 = r0->field_b
    //     0xa48674: ldur            x1, [x0, #0xb]
    // 0xa48678: tst             x1, #7
    // 0xa4867c: b.eq            #0xa48704
    // 0xa48680: sdiv            x4, x1, x2
    // 0xa48684: LoadField: r1 = r3->field_b
    //     0xa48684: ldur            w1, [x3, #0xb]
    // 0xa48688: r5 = LoadInt32Instr(r1)
    //     0xa48688: sbfx            x5, x1, #1, #0x1f
    // 0xa4868c: stur            x5, [fp, #-0x38]
    // 0xa48690: cmp             x5, x4
    // 0xa48694: b.gt            #0xa486e0
    // 0xa48698: LoadField: r1 = r3->field_f
    //     0xa48698: ldur            w1, [x3, #0xf]
    // 0xa4869c: DecompressPointer r1
    //     0xa4869c: add             x1, x1, HEAP, lsl #32
    // 0xa486a0: LoadField: r4 = r1->field_b
    //     0xa486a0: ldur            w4, [x1, #0xb]
    // 0xa486a4: r1 = LoadInt32Instr(r4)
    //     0xa486a4: sbfx            x1, x4, #1, #0x1f
    // 0xa486a8: cmp             x5, x1
    // 0xa486ac: b.ne            #0xa486b8
    // 0xa486b0: mov             x1, x3
    // 0xa486b4: r0 = _growToNextCapacity()
    //     0xa486b4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa486b8: ldur            x0, [fp, #-0x40]
    // 0xa486bc: ldur            x1, [fp, #-0x38]
    // 0xa486c0: add             x2, x1, #1
    // 0xa486c4: lsl             x3, x2, #1
    // 0xa486c8: StoreField: r0->field_b = r3
    //     0xa486c8: stur            w3, [x0, #0xb]
    // 0xa486cc: LoadField: r2 = r0->field_f
    //     0xa486cc: ldur            w2, [x0, #0xf]
    // 0xa486d0: DecompressPointer r2
    //     0xa486d0: add             x2, x2, HEAP, lsl #32
    // 0xa486d4: ArrayStore: r2[r1] = rZR  ; Unknown_4
    //     0xa486d4: add             x3, x2, x1, lsl #2
    //     0xa486d8: stur            wzr, [x3, #0xf]
    // 0xa486dc: b               #0xa486e4
    // 0xa486e0: mov             x0, x3
    // 0xa486e4: ldur            x3, [fp, #-0x20]
    // 0xa486e8: LoadField: r1 = r3->field_b
    //     0xa486e8: ldur            x1, [x3, #0xb]
    // 0xa486ec: add             x2, x1, #1
    // 0xa486f0: StoreField: r3->field_b = r2
    //     0xa486f0: stur            x2, [x3, #0xb]
    // 0xa486f4: mov             x16, x0
    // 0xa486f8: mov             x0, x3
    // 0xa486fc: mov             x3, x16
    // 0xa48700: b               #0xa48664
    // 0xa48704: mov             x3, x0
    // 0xa48708: r1 = 0
    //     0xa48708: movz            x1, #0
    // 0xa4870c: ldur            x4, [fp, #-0x28]
    // 0xa48710: r0 = 8
    //     0xa48710: movz            x0, #0x8
    // 0xa48714: r5 = 1
    //     0xa48714: movz            x5, #0x1
    // 0xa48718: CheckStackOverflow
    //     0xa48718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4871c: cmp             SP, x16
    //     0xa48720: b.ls            #0xa4892c
    // 0xa48724: LoadField: r2 = r3->field_b
    //     0xa48724: ldur            x2, [x3, #0xb]
    // 0xa48728: cmp             x2, x4
    // 0xa4872c: b.ge            #0xa487d8
    // 0xa48730: add             x6, x1, #1
    // 0xa48734: stur            x6, [fp, #-0x50]
    // 0xa48738: tbnz            w1, #0, #0xa48744
    // 0xa4873c: r7 = 236
    //     0xa4873c: movz            x7, #0xec
    // 0xa48740: b               #0xa48748
    // 0xa48744: r7 = 17
    //     0xa48744: movz            x7, #0x11
    // 0xa48748: stur            x7, [fp, #-0x48]
    // 0xa4874c: r8 = 0
    //     0xa4874c: movz            x8, #0
    // 0xa48750: stur            x8, [fp, #-0x38]
    // 0xa48754: CheckStackOverflow
    //     0xa48754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48758: cmp             SP, x16
    //     0xa4875c: b.ls            #0xa48934
    // 0xa48760: cmp             x8, #8
    // 0xa48764: b.ge            #0xa487cc
    // 0xa48768: sub             x1, x0, x8
    // 0xa4876c: sub             x2, x1, #1
    // 0xa48770: mov             x1, x7
    // 0xa48774: ubfx            x1, x1, #0, #0x20
    // 0xa48778: tbnz            x2, #0x3f, #0xa4893c
    // 0xa4877c: lsr             w9, w1, w2
    // 0xa48780: cmp             x2, #0x1f
    // 0xa48784: csel            x9, x9, xzr, le
    // 0xa48788: and             x1, x9, x5
    // 0xa4878c: ubfx            x1, x1, #0, #0x20
    // 0xa48790: cmp             x1, #1
    // 0xa48794: r16 = true
    //     0xa48794: add             x16, NULL, #0x20  ; true
    // 0xa48798: r17 = false
    //     0xa48798: add             x17, NULL, #0x30  ; false
    // 0xa4879c: csel            x2, x16, x17, eq
    // 0xa487a0: mov             x1, x3
    // 0xa487a4: r0 = putBit()
    //     0xa487a4: bl              #0xa4513c  ; [package:qr/src/bit_buffer.dart] QrBitBuffer::putBit
    // 0xa487a8: ldur            x0, [fp, #-0x38]
    // 0xa487ac: add             x8, x0, #1
    // 0xa487b0: ldur            x3, [fp, #-0x20]
    // 0xa487b4: ldur            x6, [fp, #-0x50]
    // 0xa487b8: ldur            x7, [fp, #-0x48]
    // 0xa487bc: ldur            x4, [fp, #-0x28]
    // 0xa487c0: r0 = 8
    //     0xa487c0: movz            x0, #0x8
    // 0xa487c4: r5 = 1
    //     0xa487c4: movz            x5, #0x1
    // 0xa487c8: b               #0xa48750
    // 0xa487cc: ldur            x1, [fp, #-0x50]
    // 0xa487d0: ldur            x3, [fp, #-0x20]
    // 0xa487d4: b               #0xa4870c
    // 0xa487d8: ldur            x1, [fp, #-0x20]
    // 0xa487dc: ldur            x2, [fp, #-0x18]
    // 0xa487e0: r0 = _createBytes()
    //     0xa487e0: bl              #0xa48a24  ; [package:qr/src/qr_code.dart] ::_createBytes
    // 0xa487e4: LeaveFrame
    //     0xa487e4: mov             SP, fp
    //     0xa487e8: ldp             fp, lr, [SP], #0x10
    // 0xa487ec: ret
    //     0xa487ec: ret             
    // 0xa487f0: r1 = Null
    //     0xa487f0: mov             x1, NULL
    // 0xa487f4: r2 = 4
    //     0xa487f4: movz            x2, #0x4
    // 0xa487f8: r0 = AllocateArray()
    //     0xa487f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa487fc: mov             x2, x0
    // 0xa48800: r16 = "type:"
    //     0xa48800: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d88] "type:"
    //     0xa48804: ldr             x16, [x16, #0xd88]
    // 0xa48808: StoreField: r2->field_f = r16
    //     0xa48808: stur            w16, [x2, #0xf]
    // 0xa4880c: ldur            x3, [fp, #-8]
    // 0xa48810: r0 = BoxInt64Instr(r3)
    //     0xa48810: sbfiz           x0, x3, #1, #0x1f
    //     0xa48814: cmp             x3, x0, asr #1
    //     0xa48818: b.eq            #0xa48824
    //     0xa4881c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48820: stur            x3, [x0, #7]
    // 0xa48824: StoreField: r2->field_13 = r0
    //     0xa48824: stur            w0, [x2, #0x13]
    // 0xa48828: str             x2, [SP]
    // 0xa4882c: r0 = _interpolate()
    //     0xa4882c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa48830: stur            x0, [fp, #-0x10]
    // 0xa48834: r0 = ArgumentError()
    //     0xa48834: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xa48838: mov             x1, x0
    // 0xa4883c: ldur            x0, [fp, #-0x10]
    // 0xa48840: ArrayStore: r1[0] = r0  ; List_4
    //     0xa48840: stur            w0, [x1, #0x17]
    // 0xa48844: r0 = false
    //     0xa48844: add             x0, NULL, #0x30  ; false
    // 0xa48848: StoreField: r1->field_b = r0
    //     0xa48848: stur            w0, [x1, #0xb]
    // 0xa4884c: mov             x0, x1
    // 0xa48850: r0 = Throw()
    //     0xa48850: bl              #0xec04b8  ; ThrowStub
    // 0xa48854: brk             #0
    // 0xa48858: ldur            x3, [fp, #-0x28]
    // 0xa4885c: r1 = Null
    //     0xa4885c: mov             x1, NULL
    // 0xa48860: r0 = InputTooLongException()
    //     0xa48860: bl              #0xa48968  ; [package:qr/src/input_too_long_exception.dart] InputTooLongException::InputTooLongException
    // 0xa48864: r0 = Throw()
    //     0xa48864: bl              #0xec04b8  ; ThrowStub
    // 0xa48868: brk             #0
    // 0xa4886c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4886c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48870: b               #0xa482f0
    // 0xa48874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48878: b               #0xa48364
    // 0xa4887c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4887c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48880: b               #0xa483a0
    // 0xa48884: str             x2, [THR, #0x7a8]  ; THR::
    // 0xa48888: stp             x10, x11, [SP, #-0x10]!
    // 0xa4888c: stp             x8, x9, [SP, #-0x10]!
    // 0xa48890: stp             x6, x7, [SP, #-0x10]!
    // 0xa48894: stp             x4, x5, [SP, #-0x10]!
    // 0xa48898: stp             x2, x3, [SP, #-0x10]!
    // 0xa4889c: SaveReg r0
    //     0xa4889c: str             x0, [SP, #-8]!
    // 0xa488a0: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa488a4: r4 = 0
    //     0xa488a4: movz            x4, #0
    // 0xa488a8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa488ac: blr             lr
    // 0xa488b0: brk             #0
    // 0xa488b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa488b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa488b8: b               #0xa48488
    // 0xa488bc: str             x2, [THR, #0x7a8]  ; THR::
    // 0xa488c0: stp             x7, x8, [SP, #-0x10]!
    // 0xa488c4: stp             x5, x6, [SP, #-0x10]!
    // 0xa488c8: stp             x3, x4, [SP, #-0x10]!
    // 0xa488cc: stp             x0, x2, [SP, #-0x10]!
    // 0xa488d0: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa488d4: r4 = 0
    //     0xa488d4: movz            x4, #0
    // 0xa488d8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa488dc: blr             lr
    // 0xa488e0: brk             #0
    // 0xa488e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa488e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa488e8: b               #0xa48514
    // 0xa488ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa488ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa488f0: b               #0xa4854c
    // 0xa488f4: str             x2, [THR, #0x7a8]  ; THR::
    // 0xa488f8: stp             x7, x8, [SP, #-0x10]!
    // 0xa488fc: stp             x5, x6, [SP, #-0x10]!
    // 0xa48900: stp             x3, x4, [SP, #-0x10]!
    // 0xa48904: stp             x0, x2, [SP, #-0x10]!
    // 0xa48908: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa4890c: r4 = 0
    //     0xa4890c: movz            x4, #0
    // 0xa48910: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa48914: blr             lr
    // 0xa48918: brk             #0
    // 0xa4891c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4891c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48920: b               #0xa485fc
    // 0xa48924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48928: b               #0xa48674
    // 0xa4892c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4892c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48930: b               #0xa48724
    // 0xa48934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48938: b               #0xa48760
    // 0xa4893c: str             x2, [THR, #0x7a8]  ; THR::
    // 0xa48940: stp             x7, x8, [SP, #-0x10]!
    // 0xa48944: stp             x5, x6, [SP, #-0x10]!
    // 0xa48948: stp             x3, x4, [SP, #-0x10]!
    // 0xa4894c: stp             x1, x2, [SP, #-0x10]!
    // 0xa48950: SaveReg r0
    //     0xa48950: str             x0, [SP, #-8]!
    // 0xa48954: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa48958: r4 = 0
    //     0xa48958: movz            x4, #0
    // 0xa4895c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa48960: blr             lr
    // 0xa48964: brk             #0
  }
  static _ _createBytes(/* No info */) {
    // ** addr: 0xa48a24, size: 0x9b4
    // 0xa48a24: EnterFrame
    //     0xa48a24: stp             fp, lr, [SP, #-0x10]!
    //     0xa48a28: mov             fp, SP
    // 0xa48a2c: AllocStack(0x98)
    //     0xa48a2c: sub             SP, SP, #0x98
    // 0xa48a30: SetupParameters(dynamic _ /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xa48a30: mov             x3, x1
    //     0xa48a34: mov             x0, x2
    //     0xa48a38: stur            x1, [fp, #-0x10]
    //     0xa48a3c: stur            x2, [fp, #-0x18]
    // 0xa48a40: CheckStackOverflow
    //     0xa48a40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48a44: cmp             SP, x16
    //     0xa48a48: b.ls            #0xa49374
    // 0xa48a4c: LoadField: r4 = r0->field_b
    //     0xa48a4c: ldur            w4, [x0, #0xb]
    // 0xa48a50: mov             x2, x4
    // 0xa48a54: stur            x4, [fp, #-8]
    // 0xa48a58: r1 = <List<int>?>
    //     0xa48a58: add             x1, PP, #0xd, lsl #12  ; [pp+0xdd40] TypeArguments: <List<int>?>
    //     0xa48a5c: ldr             x1, [x1, #0xd40]
    // 0xa48a60: r0 = AllocateArray()
    //     0xa48a60: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa48a64: ldur            x2, [fp, #-8]
    // 0xa48a68: r1 = <List<int>?>
    //     0xa48a68: add             x1, PP, #0xd, lsl #12  ; [pp+0xdd40] TypeArguments: <List<int>?>
    //     0xa48a6c: ldr             x1, [x1, #0xd40]
    // 0xa48a70: stur            x0, [fp, #-0x20]
    // 0xa48a74: r0 = AllocateArray()
    //     0xa48a74: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa48a78: mov             x3, x0
    // 0xa48a7c: ldur            x2, [fp, #-8]
    // 0xa48a80: stur            x3, [fp, #-0x68]
    // 0xa48a84: r4 = LoadInt32Instr(r2)
    //     0xa48a84: sbfx            x4, x2, #1, #0x1f
    // 0xa48a88: ldur            x0, [fp, #-0x10]
    // 0xa48a8c: stur            x4, [fp, #-0x60]
    // 0xa48a90: LoadField: r5 = r0->field_7
    //     0xa48a90: ldur            w5, [x0, #7]
    // 0xa48a94: DecompressPointer r5
    //     0xa48a94: add             x5, x5, HEAP, lsl #32
    // 0xa48a98: stur            x5, [fp, #-0x58]
    // 0xa48a9c: r10 = 0
    //     0xa48a9c: movz            x10, #0
    // 0xa48aa0: r9 = 0
    //     0xa48aa0: movz            x9, #0
    // 0xa48aa4: r8 = 0
    //     0xa48aa4: movz            x8, #0
    // 0xa48aa8: r7 = 0
    //     0xa48aa8: movz            x7, #0
    // 0xa48aac: ldur            x6, [fp, #-0x18]
    // 0xa48ab0: d0 = 0.000000
    //     0xa48ab0: eor             v0.16b, v0.16b, v0.16b
    // 0xa48ab4: stur            x10, [fp, #-0x38]
    // 0xa48ab8: stur            x9, [fp, #-0x40]
    // 0xa48abc: stur            x8, [fp, #-0x48]
    // 0xa48ac0: stur            x7, [fp, #-0x50]
    // 0xa48ac4: CheckStackOverflow
    //     0xa48ac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48ac8: cmp             SP, x16
    //     0xa48acc: b.ls            #0xa4937c
    // 0xa48ad0: LoadField: r0 = r6->field_b
    //     0xa48ad0: ldur            w0, [x6, #0xb]
    // 0xa48ad4: r1 = LoadInt32Instr(r0)
    //     0xa48ad4: sbfx            x1, x0, #1, #0x1f
    // 0xa48ad8: cmp             x7, x1
    // 0xa48adc: b.ge            #0xa48f70
    // 0xa48ae0: LoadField: r0 = r6->field_f
    //     0xa48ae0: ldur            w0, [x6, #0xf]
    // 0xa48ae4: DecompressPointer r0
    //     0xa48ae4: add             x0, x0, HEAP, lsl #32
    // 0xa48ae8: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xa48ae8: add             x16, x0, x7, lsl #2
    //     0xa48aec: ldur            w1, [x16, #0xf]
    // 0xa48af0: DecompressPointer r1
    //     0xa48af0: add             x1, x1, HEAP, lsl #32
    // 0xa48af4: LoadField: r11 = r1->field_f
    //     0xa48af4: ldur            x11, [x1, #0xf]
    // 0xa48af8: stur            x11, [fp, #-0x30]
    // 0xa48afc: LoadField: r0 = r1->field_7
    //     0xa48afc: ldur            x0, [x1, #7]
    // 0xa48b00: sub             x12, x0, x11
    // 0xa48b04: stur            x12, [fp, #-0x28]
    // 0xa48b08: r13 = LoadInt32Instr(r9)
    //     0xa48b08: sbfx            x13, x9, #1, #0x1f
    //     0xa48b0c: tbz             w9, #0, #0xa48b14
    //     0xa48b10: ldur            x13, [x9, #7]
    // 0xa48b14: cmp             x13, x11
    // 0xa48b18: b.le            #0xa48b28
    // 0xa48b1c: mov             x2, x8
    // 0xa48b20: mov             x3, x12
    // 0xa48b24: b               #0xa48c40
    // 0xa48b28: cmp             x13, x11
    // 0xa48b2c: b.ge            #0xa48b54
    // 0xa48b30: r0 = BoxInt64Instr(r11)
    //     0xa48b30: sbfiz           x0, x11, #1, #0x1f
    //     0xa48b34: cmp             x11, x0, asr #1
    //     0xa48b38: b.eq            #0xa48b44
    //     0xa48b3c: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xa48b40: stur            x11, [x0, #7]
    // 0xa48b44: mov             x9, x0
    // 0xa48b48: mov             x2, x8
    // 0xa48b4c: mov             x3, x12
    // 0xa48b50: b               #0xa48c40
    // 0xa48b54: r0 = BoxInt64Instr(r11)
    //     0xa48b54: sbfiz           x0, x11, #1, #0x1f
    //     0xa48b58: cmp             x11, x0, asr #1
    //     0xa48b5c: b.eq            #0xa48b68
    //     0xa48b60: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xa48b64: stur            x11, [x0, #7]
    // 0xa48b68: mov             x1, x0
    // 0xa48b6c: stur            x1, [fp, #-0x10]
    // 0xa48b70: r0 = 60
    //     0xa48b70: movz            x0, #0x3c
    // 0xa48b74: branchIfSmi(r1, 0xa48b80)
    //     0xa48b74: tbz             w1, #0, #0xa48b80
    // 0xa48b78: r0 = LoadClassIdInstr(r1)
    //     0xa48b78: ldur            x0, [x1, #-1]
    //     0xa48b7c: ubfx            x0, x0, #0xc, #0x14
    // 0xa48b80: cmp             x0, #0x3e
    // 0xa48b84: b.ne            #0xa48bfc
    // 0xa48b88: r0 = 60
    //     0xa48b88: movz            x0, #0x3c
    // 0xa48b8c: branchIfSmi(r9, 0xa48b98)
    //     0xa48b8c: tbz             w9, #0, #0xa48b98
    // 0xa48b90: r0 = LoadClassIdInstr(r9)
    //     0xa48b90: ldur            x0, [x9, #-1]
    //     0xa48b94: ubfx            x0, x0, #0xc, #0x14
    // 0xa48b98: cmp             x0, #0x3e
    // 0xa48b9c: b.ne            #0xa48bd4
    // 0xa48ba0: scvtf           d1, x13
    // 0xa48ba4: fcmp            d1, d0
    // 0xa48ba8: b.ne            #0xa48bd4
    // 0xa48bac: add             x9, x13, x11
    // 0xa48bb0: r0 = BoxInt64Instr(r9)
    //     0xa48bb0: sbfiz           x0, x9, #1, #0x1f
    //     0xa48bb4: cmp             x9, x0, asr #1
    //     0xa48bb8: b.eq            #0xa48bc4
    //     0xa48bbc: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xa48bc0: stur            x9, [x0, #7]
    // 0xa48bc4: mov             x9, x0
    // 0xa48bc8: mov             x2, x8
    // 0xa48bcc: mov             x3, x12
    // 0xa48bd0: b               #0xa48c40
    // 0xa48bd4: LoadField: d1 = r1->field_7
    //     0xa48bd4: ldur            d1, [x1, #7]
    // 0xa48bd8: fcmp            d1, d1
    // 0xa48bdc: b.vc            #0xa48bf0
    // 0xa48be0: mov             x9, x1
    // 0xa48be4: mov             x2, x8
    // 0xa48be8: mov             x3, x12
    // 0xa48bec: b               #0xa48c40
    // 0xa48bf0: mov             x2, x8
    // 0xa48bf4: mov             x3, x12
    // 0xa48bf8: b               #0xa48c40
    // 0xa48bfc: cbnz            x11, #0xa48c34
    // 0xa48c00: r0 = 60
    //     0xa48c00: movz            x0, #0x3c
    // 0xa48c04: branchIfSmi(r9, 0xa48c10)
    //     0xa48c04: tbz             w9, #0, #0xa48c10
    // 0xa48c08: r0 = LoadClassIdInstr(r9)
    //     0xa48c08: ldur            x0, [x9, #-1]
    //     0xa48c0c: ubfx            x0, x0, #0xc, #0x14
    // 0xa48c10: str             x9, [SP]
    // 0xa48c14: r0 = GDT[cid_x0 + -0xfb8]()
    //     0xa48c14: sub             lr, x0, #0xfb8
    //     0xa48c18: ldr             lr, [x21, lr, lsl #3]
    //     0xa48c1c: blr             lr
    // 0xa48c20: tbnz            w0, #4, #0xa48c34
    // 0xa48c24: ldur            x9, [fp, #-0x10]
    // 0xa48c28: ldur            x2, [fp, #-0x48]
    // 0xa48c2c: ldur            x3, [fp, #-0x28]
    // 0xa48c30: b               #0xa48c40
    // 0xa48c34: ldur            x9, [fp, #-0x40]
    // 0xa48c38: ldur            x2, [fp, #-0x48]
    // 0xa48c3c: ldur            x3, [fp, #-0x28]
    // 0xa48c40: stur            x9, [fp, #-0x70]
    // 0xa48c44: r4 = LoadInt32Instr(r2)
    //     0xa48c44: sbfx            x4, x2, #1, #0x1f
    //     0xa48c48: tbz             w2, #0, #0xa48c50
    //     0xa48c4c: ldur            x4, [x2, #7]
    // 0xa48c50: cmp             x4, x3
    // 0xa48c54: b.le            #0xa48c60
    // 0xa48c58: mov             x8, x2
    // 0xa48c5c: b               #0xa48d58
    // 0xa48c60: cmp             x4, x3
    // 0xa48c64: b.ge            #0xa48c84
    // 0xa48c68: r0 = BoxInt64Instr(r3)
    //     0xa48c68: sbfiz           x0, x3, #1, #0x1f
    //     0xa48c6c: cmp             x3, x0, asr #1
    //     0xa48c70: b.eq            #0xa48c7c
    //     0xa48c74: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48c78: stur            x3, [x0, #7]
    // 0xa48c7c: mov             x8, x0
    // 0xa48c80: b               #0xa48d58
    // 0xa48c84: r0 = BoxInt64Instr(r3)
    //     0xa48c84: sbfiz           x0, x3, #1, #0x1f
    //     0xa48c88: cmp             x3, x0, asr #1
    //     0xa48c8c: b.eq            #0xa48c98
    //     0xa48c90: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48c94: stur            x3, [x0, #7]
    // 0xa48c98: mov             x1, x0
    // 0xa48c9c: stur            x1, [fp, #-0x10]
    // 0xa48ca0: r0 = 60
    //     0xa48ca0: movz            x0, #0x3c
    // 0xa48ca4: branchIfSmi(r1, 0xa48cb0)
    //     0xa48ca4: tbz             w1, #0, #0xa48cb0
    // 0xa48ca8: r0 = LoadClassIdInstr(r1)
    //     0xa48ca8: ldur            x0, [x1, #-1]
    //     0xa48cac: ubfx            x0, x0, #0xc, #0x14
    // 0xa48cb0: cmp             x0, #0x3e
    // 0xa48cb4: b.ne            #0xa48d20
    // 0xa48cb8: r0 = 60
    //     0xa48cb8: movz            x0, #0x3c
    // 0xa48cbc: branchIfSmi(r2, 0xa48cc8)
    //     0xa48cbc: tbz             w2, #0, #0xa48cc8
    // 0xa48cc0: r0 = LoadClassIdInstr(r2)
    //     0xa48cc0: ldur            x0, [x2, #-1]
    //     0xa48cc4: ubfx            x0, x0, #0xc, #0x14
    // 0xa48cc8: cmp             x0, #0x3e
    // 0xa48ccc: b.ne            #0xa48d00
    // 0xa48cd0: d0 = 0.000000
    //     0xa48cd0: eor             v0.16b, v0.16b, v0.16b
    // 0xa48cd4: scvtf           d1, x4
    // 0xa48cd8: fcmp            d1, d0
    // 0xa48cdc: b.ne            #0xa48d04
    // 0xa48ce0: add             x2, x4, x3
    // 0xa48ce4: r0 = BoxInt64Instr(r2)
    //     0xa48ce4: sbfiz           x0, x2, #1, #0x1f
    //     0xa48ce8: cmp             x2, x0, asr #1
    //     0xa48cec: b.eq            #0xa48cf8
    //     0xa48cf0: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xa48cf4: stur            x2, [x0, #7]
    // 0xa48cf8: mov             x8, x0
    // 0xa48cfc: b               #0xa48d58
    // 0xa48d00: d0 = 0.000000
    //     0xa48d00: eor             v0.16b, v0.16b, v0.16b
    // 0xa48d04: LoadField: d1 = r1->field_7
    //     0xa48d04: ldur            d1, [x1, #7]
    // 0xa48d08: fcmp            d1, d1
    // 0xa48d0c: b.vc            #0xa48d18
    // 0xa48d10: mov             x8, x1
    // 0xa48d14: b               #0xa48d58
    // 0xa48d18: mov             x8, x2
    // 0xa48d1c: b               #0xa48d58
    // 0xa48d20: d0 = 0.000000
    //     0xa48d20: eor             v0.16b, v0.16b, v0.16b
    // 0xa48d24: cbnz            x3, #0xa48d54
    // 0xa48d28: r0 = 60
    //     0xa48d28: movz            x0, #0x3c
    // 0xa48d2c: branchIfSmi(r2, 0xa48d38)
    //     0xa48d2c: tbz             w2, #0, #0xa48d38
    // 0xa48d30: r0 = LoadClassIdInstr(r2)
    //     0xa48d30: ldur            x0, [x2, #-1]
    //     0xa48d34: ubfx            x0, x0, #0xc, #0x14
    // 0xa48d38: str             x2, [SP]
    // 0xa48d3c: r0 = GDT[cid_x0 + -0xfb8]()
    //     0xa48d3c: sub             lr, x0, #0xfb8
    //     0xa48d40: ldr             lr, [x21, lr, lsl #3]
    //     0xa48d44: blr             lr
    // 0xa48d48: tbnz            w0, #4, #0xa48d54
    // 0xa48d4c: ldur            x8, [fp, #-0x10]
    // 0xa48d50: b               #0xa48d58
    // 0xa48d54: ldur            x8, [fp, #-0x48]
    // 0xa48d58: ldur            x3, [fp, #-0x50]
    // 0xa48d5c: ldur            x5, [fp, #-0x30]
    // 0xa48d60: ldur            x2, [fp, #-0x58]
    // 0xa48d64: stur            x8, [fp, #-0x10]
    // 0xa48d68: r0 = BoxInt64Instr(r5)
    //     0xa48d68: sbfiz           x0, x5, #1, #0x1f
    //     0xa48d6c: cmp             x5, x0, asr #1
    //     0xa48d70: b.eq            #0xa48d7c
    //     0xa48d74: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48d78: stur            x5, [x0, #7]
    // 0xa48d7c: mov             x4, x0
    // 0xa48d80: ldur            x0, [fp, #-0x60]
    // 0xa48d84: mov             x1, x3
    // 0xa48d88: cmp             x1, x0
    // 0xa48d8c: b.hs            #0xa49384
    // 0xa48d90: r0 = AllocateUint8Array()
    //     0xa48d90: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xa48d94: ldur            x1, [fp, #-0x20]
    // 0xa48d98: mov             x3, x0
    // 0xa48d9c: ldur            x2, [fp, #-0x50]
    // 0xa48da0: stur            x3, [fp, #-0x80]
    // 0xa48da4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa48da4: add             x25, x1, x2, lsl #2
    //     0xa48da8: add             x25, x25, #0xf
    //     0xa48dac: str             w0, [x25]
    //     0xa48db0: tbz             w0, #0, #0xa48dcc
    //     0xa48db4: ldurb           w16, [x1, #-1]
    //     0xa48db8: ldurb           w17, [x0, #-1]
    //     0xa48dbc: and             x16, x17, x16, lsr #2
    //     0xa48dc0: tst             x16, HEAP, lsr #32
    //     0xa48dc4: b.eq            #0xa48dcc
    //     0xa48dc8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa48dcc: ldur            x4, [fp, #-0x58]
    // 0xa48dd0: LoadField: r0 = r4->field_b
    //     0xa48dd0: ldur            w0, [x4, #0xb]
    // 0xa48dd4: r5 = LoadInt32Instr(r0)
    //     0xa48dd4: sbfx            x5, x0, #1, #0x1f
    // 0xa48dd8: LoadField: r6 = r4->field_f
    //     0xa48dd8: ldur            w6, [x4, #0xf]
    // 0xa48ddc: DecompressPointer r6
    //     0xa48ddc: add             x6, x6, HEAP, lsl #32
    // 0xa48de0: ldur            x9, [fp, #-0x38]
    // 0xa48de4: ldur            x7, [fp, #-0x30]
    // 0xa48de8: r10 = 0
    //     0xa48de8: movz            x10, #0
    // 0xa48dec: r8 = 255
    //     0xa48dec: movz            x8, #0xff
    // 0xa48df0: CheckStackOverflow
    //     0xa48df0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48df4: cmp             SP, x16
    //     0xa48df8: b.ls            #0xa49388
    // 0xa48dfc: cmp             x10, x7
    // 0xa48e00: b.ge            #0xa48e4c
    // 0xa48e04: add             x11, x10, x9
    // 0xa48e08: mov             x0, x5
    // 0xa48e0c: mov             x1, x11
    // 0xa48e10: cmp             x1, x0
    // 0xa48e14: b.hs            #0xa49390
    // 0xa48e18: ArrayLoad: r0 = r6[r11]  ; Unknown_4
    //     0xa48e18: add             x16, x6, x11, lsl #2
    //     0xa48e1c: ldur            w0, [x16, #0xf]
    // 0xa48e20: DecompressPointer r0
    //     0xa48e20: add             x0, x0, HEAP, lsl #32
    // 0xa48e24: r1 = LoadInt32Instr(r0)
    //     0xa48e24: sbfx            x1, x0, #1, #0x1f
    //     0xa48e28: tbz             w0, #0, #0xa48e30
    //     0xa48e2c: ldur            x1, [x0, #7]
    // 0xa48e30: and             x0, x1, x8
    // 0xa48e34: ubfx            x0, x0, #0, #0x20
    // 0xa48e38: ArrayStore: r3[r10] = r0  ; TypeUnknown_1
    //     0xa48e38: add             x1, x3, x10
    //     0xa48e3c: strb            w0, [x1, #0x17]
    // 0xa48e40: add             x0, x10, #1
    // 0xa48e44: mov             x10, x0
    // 0xa48e48: b               #0xa48df0
    // 0xa48e4c: add             x10, x9, x7
    // 0xa48e50: ldur            x1, [fp, #-0x28]
    // 0xa48e54: stur            x10, [fp, #-0x78]
    // 0xa48e58: r0 = _errorCorrectPolynomial()
    //     0xa48e58: bl              #0xa49c6c  ; [package:qr/src/qr_code.dart] ::_errorCorrectPolynomial
    // 0xa48e5c: stur            x0, [fp, #-0x88]
    // 0xa48e60: LoadField: r1 = r0->field_7
    //     0xa48e60: ldur            w1, [x0, #7]
    // 0xa48e64: DecompressPointer r1
    //     0xa48e64: add             x1, x1, HEAP, lsl #32
    // 0xa48e68: LoadField: r2 = r1->field_13
    //     0xa48e68: ldur            w2, [x1, #0x13]
    // 0xa48e6c: r1 = LoadInt32Instr(r2)
    //     0xa48e6c: sbfx            x1, x2, #1, #0x1f
    // 0xa48e70: sub             x4, x1, #1
    // 0xa48e74: ldur            x2, [fp, #-0x80]
    // 0xa48e78: mov             x3, x4
    // 0xa48e7c: stur            x4, [fp, #-0x28]
    // 0xa48e80: r1 = Null
    //     0xa48e80: mov             x1, NULL
    // 0xa48e84: r0 = QrPolynomial()
    //     0xa48e84: bl              #0xa49a34  ; [package:qr/src/polynomial.dart] QrPolynomial::QrPolynomial
    // 0xa48e88: mov             x1, x0
    // 0xa48e8c: ldur            x2, [fp, #-0x88]
    // 0xa48e90: r0 = mod()
    //     0xa48e90: bl              #0xa493d8  ; [package:qr/src/polynomial.dart] QrPolynomial::mod
    // 0xa48e94: mov             x1, x0
    // 0xa48e98: ldur            x0, [fp, #-0x28]
    // 0xa48e9c: stur            x1, [fp, #-0x80]
    // 0xa48ea0: lsl             x4, x0, #1
    // 0xa48ea4: r0 = AllocateUint8Array()
    //     0xa48ea4: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xa48ea8: ldur            x1, [fp, #-0x68]
    // 0xa48eac: mov             x3, x0
    // 0xa48eb0: ldur            x2, [fp, #-0x50]
    // 0xa48eb4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa48eb4: add             x25, x1, x2, lsl #2
    //     0xa48eb8: add             x25, x25, #0xf
    //     0xa48ebc: str             w0, [x25]
    //     0xa48ec0: tbz             w0, #0, #0xa48edc
    //     0xa48ec4: ldurb           w16, [x1, #-1]
    //     0xa48ec8: ldurb           w17, [x0, #-1]
    //     0xa48ecc: and             x16, x17, x16, lsr #2
    //     0xa48ed0: tst             x16, HEAP, lsr #32
    //     0xa48ed4: b.eq            #0xa48edc
    //     0xa48ed8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa48edc: ldur            x0, [fp, #-0x80]
    // 0xa48ee0: LoadField: r4 = r0->field_7
    //     0xa48ee0: ldur            w4, [x0, #7]
    // 0xa48ee4: DecompressPointer r4
    //     0xa48ee4: add             x4, x4, HEAP, lsl #32
    // 0xa48ee8: LoadField: r0 = r4->field_13
    //     0xa48ee8: ldur            w0, [x4, #0x13]
    // 0xa48eec: r5 = LoadInt32Instr(r0)
    //     0xa48eec: sbfx            x5, x0, #1, #0x1f
    // 0xa48ef0: ldur            x6, [fp, #-0x28]
    // 0xa48ef4: r7 = 0
    //     0xa48ef4: movz            x7, #0
    // 0xa48ef8: CheckStackOverflow
    //     0xa48ef8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48efc: cmp             SP, x16
    //     0xa48f00: b.ls            #0xa49394
    // 0xa48f04: cmp             x7, x6
    // 0xa48f08: b.ge            #0xa48f4c
    // 0xa48f0c: add             x0, x7, x5
    // 0xa48f10: sub             x8, x0, x6
    // 0xa48f14: tbnz            x8, #0x3f, #0xa48f34
    // 0xa48f18: mov             x0, x5
    // 0xa48f1c: mov             x1, x8
    // 0xa48f20: cmp             x1, x0
    // 0xa48f24: b.hs            #0xa4939c
    // 0xa48f28: ArrayLoad: r0 = r4[r8]  ; List_1
    //     0xa48f28: add             x16, x4, x8
    //     0xa48f2c: ldrb            w0, [x16, #0x17]
    // 0xa48f30: b               #0xa48f38
    // 0xa48f34: r0 = 0
    //     0xa48f34: movz            x0, #0
    // 0xa48f38: ArrayStore: r3[r7] = r0  ; TypeUnknown_1
    //     0xa48f38: add             x1, x3, x7
    //     0xa48f3c: strb            w0, [x1, #0x17]
    // 0xa48f40: add             x0, x7, #1
    // 0xa48f44: mov             x7, x0
    // 0xa48f48: b               #0xa48ef8
    // 0xa48f4c: add             x7, x2, #1
    // 0xa48f50: ldur            x10, [fp, #-0x78]
    // 0xa48f54: ldur            x9, [fp, #-0x70]
    // 0xa48f58: ldur            x8, [fp, #-0x10]
    // 0xa48f5c: ldur            x5, [fp, #-0x58]
    // 0xa48f60: ldur            x2, [fp, #-8]
    // 0xa48f64: ldur            x3, [fp, #-0x68]
    // 0xa48f68: ldur            x4, [fp, #-0x60]
    // 0xa48f6c: b               #0xa48aac
    // 0xa48f70: mov             x3, x9
    // 0xa48f74: mov             x0, x2
    // 0xa48f78: r1 = <int>
    //     0xa48f78: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xa48f7c: r2 = 0
    //     0xa48f7c: movz            x2, #0
    // 0xa48f80: r0 = _GrowableList()
    //     0xa48f80: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa48f84: mov             x2, x0
    // 0xa48f88: ldur            x0, [fp, #-0x40]
    // 0xa48f8c: stur            x2, [fp, #-0x58]
    // 0xa48f90: r3 = LoadInt32Instr(r0)
    //     0xa48f90: sbfx            x3, x0, #1, #0x1f
    //     0xa48f94: tbz             w0, #0, #0xa48f9c
    //     0xa48f98: ldur            x3, [x0, #7]
    // 0xa48f9c: ldur            x4, [fp, #-8]
    // 0xa48fa0: stur            x3, [fp, #-0x50]
    // 0xa48fa4: r5 = LoadInt32Instr(r4)
    //     0xa48fa4: sbfx            x5, x4, #1, #0x1f
    // 0xa48fa8: stur            x5, [fp, #-0x38]
    // 0xa48fac: r8 = 0
    //     0xa48fac: movz            x8, #0
    // 0xa48fb0: ldur            x6, [fp, #-0x18]
    // 0xa48fb4: ldur            x7, [fp, #-0x20]
    // 0xa48fb8: stur            x8, [fp, #-0x30]
    // 0xa48fbc: CheckStackOverflow
    //     0xa48fbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48fc0: cmp             SP, x16
    //     0xa48fc4: b.ls            #0xa493a0
    // 0xa48fc8: cmp             x8, x3
    // 0xa48fcc: b.ge            #0xa4917c
    // 0xa48fd0: r0 = BoxInt64Instr(r8)
    //     0xa48fd0: sbfiz           x0, x8, #1, #0x1f
    //     0xa48fd4: cmp             x8, x0, asr #1
    //     0xa48fd8: b.eq            #0xa48fe4
    //     0xa48fdc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48fe0: stur            x8, [x0, #7]
    // 0xa48fe4: mov             x9, x0
    // 0xa48fe8: stur            x9, [fp, #-0x10]
    // 0xa48fec: r10 = 0
    //     0xa48fec: movz            x10, #0
    // 0xa48ff0: stur            x10, [fp, #-0x28]
    // 0xa48ff4: CheckStackOverflow
    //     0xa48ff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48ff8: cmp             SP, x16
    //     0xa48ffc: b.ls            #0xa493a8
    // 0xa49000: LoadField: r0 = r6->field_b
    //     0xa49000: ldur            w0, [x6, #0xb]
    // 0xa49004: r1 = LoadInt32Instr(r0)
    //     0xa49004: sbfx            x1, x0, #1, #0x1f
    // 0xa49008: cmp             x10, x1
    // 0xa4900c: b.ge            #0xa49164
    // 0xa49010: mov             x0, x5
    // 0xa49014: mov             x1, x10
    // 0xa49018: cmp             x1, x0
    // 0xa4901c: b.hs            #0xa493b0
    // 0xa49020: ArrayLoad: r0 = r7[r10]  ; Unknown_4
    //     0xa49020: add             x16, x7, x10, lsl #2
    //     0xa49024: ldur            w0, [x16, #0xf]
    // 0xa49028: DecompressPointer r0
    //     0xa49028: add             x0, x0, HEAP, lsl #32
    // 0xa4902c: cmp             w0, NULL
    // 0xa49030: b.eq            #0xa493b4
    // 0xa49034: r1 = LoadClassIdInstr(r0)
    //     0xa49034: ldur            x1, [x0, #-1]
    //     0xa49038: ubfx            x1, x1, #0xc, #0x14
    // 0xa4903c: str             x0, [SP]
    // 0xa49040: mov             x0, x1
    // 0xa49044: r0 = GDT[cid_x0 + 0xc834]()
    //     0xa49044: movz            x17, #0xc834
    //     0xa49048: add             lr, x0, x17
    //     0xa4904c: ldr             lr, [x21, lr, lsl #3]
    //     0xa49050: blr             lr
    // 0xa49054: r1 = LoadInt32Instr(r0)
    //     0xa49054: sbfx            x1, x0, #1, #0x1f
    //     0xa49058: tbz             w0, #0, #0xa49060
    //     0xa4905c: ldur            x1, [x0, #7]
    // 0xa49060: ldur            x2, [fp, #-0x30]
    // 0xa49064: cmp             x2, x1
    // 0xa49068: b.ge            #0xa49138
    // 0xa4906c: ldur            x1, [fp, #-0x58]
    // 0xa49070: ldur            x4, [fp, #-0x28]
    // 0xa49074: ldur            x3, [fp, #-0x20]
    // 0xa49078: ArrayLoad: r0 = r3[r4]  ; Unknown_4
    //     0xa49078: add             x16, x3, x4, lsl #2
    //     0xa4907c: ldur            w0, [x16, #0xf]
    // 0xa49080: DecompressPointer r0
    //     0xa49080: add             x0, x0, HEAP, lsl #32
    // 0xa49084: cmp             w0, NULL
    // 0xa49088: b.eq            #0xa493b8
    // 0xa4908c: r5 = LoadClassIdInstr(r0)
    //     0xa4908c: ldur            x5, [x0, #-1]
    //     0xa49090: ubfx            x5, x5, #0xc, #0x14
    // 0xa49094: ldur            x16, [fp, #-0x10]
    // 0xa49098: stp             x16, x0, [SP]
    // 0xa4909c: mov             x0, x5
    // 0xa490a0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa490a0: movz            x17, #0x3037
    //     0xa490a4: movk            x17, #0x1, lsl #16
    //     0xa490a8: add             lr, x0, x17
    //     0xa490ac: ldr             lr, [x21, lr, lsl #3]
    //     0xa490b0: blr             lr
    // 0xa490b4: mov             x2, x0
    // 0xa490b8: ldur            x0, [fp, #-0x58]
    // 0xa490bc: stur            x2, [fp, #-0x40]
    // 0xa490c0: LoadField: r1 = r0->field_b
    //     0xa490c0: ldur            w1, [x0, #0xb]
    // 0xa490c4: LoadField: r3 = r0->field_f
    //     0xa490c4: ldur            w3, [x0, #0xf]
    // 0xa490c8: DecompressPointer r3
    //     0xa490c8: add             x3, x3, HEAP, lsl #32
    // 0xa490cc: LoadField: r4 = r3->field_b
    //     0xa490cc: ldur            w4, [x3, #0xb]
    // 0xa490d0: r3 = LoadInt32Instr(r1)
    //     0xa490d0: sbfx            x3, x1, #1, #0x1f
    // 0xa490d4: stur            x3, [fp, #-0x60]
    // 0xa490d8: r1 = LoadInt32Instr(r4)
    //     0xa490d8: sbfx            x1, x4, #1, #0x1f
    // 0xa490dc: cmp             x3, x1
    // 0xa490e0: b.ne            #0xa490ec
    // 0xa490e4: mov             x1, x0
    // 0xa490e8: r0 = _growToNextCapacity()
    //     0xa490e8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa490ec: ldur            x2, [fp, #-0x58]
    // 0xa490f0: ldur            x3, [fp, #-0x60]
    // 0xa490f4: add             x0, x3, #1
    // 0xa490f8: lsl             x1, x0, #1
    // 0xa490fc: StoreField: r2->field_b = r1
    //     0xa490fc: stur            w1, [x2, #0xb]
    // 0xa49100: LoadField: r1 = r2->field_f
    //     0xa49100: ldur            w1, [x2, #0xf]
    // 0xa49104: DecompressPointer r1
    //     0xa49104: add             x1, x1, HEAP, lsl #32
    // 0xa49108: ldur            x0, [fp, #-0x40]
    // 0xa4910c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4910c: add             x25, x1, x3, lsl #2
    //     0xa49110: add             x25, x25, #0xf
    //     0xa49114: str             w0, [x25]
    //     0xa49118: tbz             w0, #0, #0xa49134
    //     0xa4911c: ldurb           w16, [x1, #-1]
    //     0xa49120: ldurb           w17, [x0, #-1]
    //     0xa49124: and             x16, x17, x16, lsr #2
    //     0xa49128: tst             x16, HEAP, lsr #32
    //     0xa4912c: b.eq            #0xa49134
    //     0xa49130: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa49134: b               #0xa4913c
    // 0xa49138: ldur            x2, [fp, #-0x58]
    // 0xa4913c: ldur            x0, [fp, #-0x28]
    // 0xa49140: add             x10, x0, #1
    // 0xa49144: ldur            x6, [fp, #-0x18]
    // 0xa49148: ldur            x8, [fp, #-0x30]
    // 0xa4914c: ldur            x4, [fp, #-8]
    // 0xa49150: ldur            x7, [fp, #-0x20]
    // 0xa49154: ldur            x5, [fp, #-0x38]
    // 0xa49158: ldur            x3, [fp, #-0x50]
    // 0xa4915c: ldur            x9, [fp, #-0x10]
    // 0xa49160: b               #0xa48ff0
    // 0xa49164: mov             x0, x8
    // 0xa49168: add             x8, x0, #1
    // 0xa4916c: ldur            x4, [fp, #-8]
    // 0xa49170: ldur            x5, [fp, #-0x38]
    // 0xa49174: ldur            x3, [fp, #-0x50]
    // 0xa49178: b               #0xa48fb0
    // 0xa4917c: ldur            x1, [fp, #-0x48]
    // 0xa49180: mov             x0, x4
    // 0xa49184: r3 = LoadInt32Instr(r1)
    //     0xa49184: sbfx            x3, x1, #1, #0x1f
    //     0xa49188: tbz             w1, #0, #0xa49190
    //     0xa4918c: ldur            x3, [x1, #7]
    // 0xa49190: stur            x3, [fp, #-0x50]
    // 0xa49194: r4 = LoadInt32Instr(r0)
    //     0xa49194: sbfx            x4, x0, #1, #0x1f
    // 0xa49198: stur            x4, [fp, #-0x38]
    // 0xa4919c: r7 = 0
    //     0xa4919c: movz            x7, #0
    // 0xa491a0: ldur            x5, [fp, #-0x18]
    // 0xa491a4: ldur            x6, [fp, #-0x68]
    // 0xa491a8: stur            x7, [fp, #-0x30]
    // 0xa491ac: CheckStackOverflow
    //     0xa491ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa491b0: cmp             SP, x16
    //     0xa491b4: b.ls            #0xa493bc
    // 0xa491b8: cmp             x7, x3
    // 0xa491bc: b.ge            #0xa49364
    // 0xa491c0: r0 = BoxInt64Instr(r7)
    //     0xa491c0: sbfiz           x0, x7, #1, #0x1f
    //     0xa491c4: cmp             x7, x0, asr #1
    //     0xa491c8: b.eq            #0xa491d4
    //     0xa491cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa491d0: stur            x7, [x0, #7]
    // 0xa491d4: mov             x8, x0
    // 0xa491d8: stur            x8, [fp, #-8]
    // 0xa491dc: r9 = 0
    //     0xa491dc: movz            x9, #0
    // 0xa491e0: stur            x9, [fp, #-0x28]
    // 0xa491e4: CheckStackOverflow
    //     0xa491e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa491e8: cmp             SP, x16
    //     0xa491ec: b.ls            #0xa493c4
    // 0xa491f0: LoadField: r0 = r5->field_b
    //     0xa491f0: ldur            w0, [x5, #0xb]
    // 0xa491f4: r1 = LoadInt32Instr(r0)
    //     0xa491f4: sbfx            x1, x0, #1, #0x1f
    // 0xa491f8: cmp             x9, x1
    // 0xa491fc: b.ge            #0xa49350
    // 0xa49200: mov             x0, x4
    // 0xa49204: mov             x1, x9
    // 0xa49208: cmp             x1, x0
    // 0xa4920c: b.hs            #0xa493cc
    // 0xa49210: ArrayLoad: r0 = r6[r9]  ; Unknown_4
    //     0xa49210: add             x16, x6, x9, lsl #2
    //     0xa49214: ldur            w0, [x16, #0xf]
    // 0xa49218: DecompressPointer r0
    //     0xa49218: add             x0, x0, HEAP, lsl #32
    // 0xa4921c: cmp             w0, NULL
    // 0xa49220: b.eq            #0xa493d0
    // 0xa49224: r1 = LoadClassIdInstr(r0)
    //     0xa49224: ldur            x1, [x0, #-1]
    //     0xa49228: ubfx            x1, x1, #0xc, #0x14
    // 0xa4922c: str             x0, [SP]
    // 0xa49230: mov             x0, x1
    // 0xa49234: r0 = GDT[cid_x0 + 0xc834]()
    //     0xa49234: movz            x17, #0xc834
    //     0xa49238: add             lr, x0, x17
    //     0xa4923c: ldr             lr, [x21, lr, lsl #3]
    //     0xa49240: blr             lr
    // 0xa49244: r1 = LoadInt32Instr(r0)
    //     0xa49244: sbfx            x1, x0, #1, #0x1f
    //     0xa49248: tbz             w0, #0, #0xa49250
    //     0xa4924c: ldur            x1, [x0, #7]
    // 0xa49250: ldur            x2, [fp, #-0x30]
    // 0xa49254: cmp             x2, x1
    // 0xa49258: b.ge            #0xa49328
    // 0xa4925c: ldur            x1, [fp, #-0x58]
    // 0xa49260: ldur            x4, [fp, #-0x28]
    // 0xa49264: ldur            x3, [fp, #-0x68]
    // 0xa49268: ArrayLoad: r0 = r3[r4]  ; Unknown_4
    //     0xa49268: add             x16, x3, x4, lsl #2
    //     0xa4926c: ldur            w0, [x16, #0xf]
    // 0xa49270: DecompressPointer r0
    //     0xa49270: add             x0, x0, HEAP, lsl #32
    // 0xa49274: cmp             w0, NULL
    // 0xa49278: b.eq            #0xa493d4
    // 0xa4927c: r5 = LoadClassIdInstr(r0)
    //     0xa4927c: ldur            x5, [x0, #-1]
    //     0xa49280: ubfx            x5, x5, #0xc, #0x14
    // 0xa49284: ldur            x16, [fp, #-8]
    // 0xa49288: stp             x16, x0, [SP]
    // 0xa4928c: mov             x0, x5
    // 0xa49290: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa49290: movz            x17, #0x3037
    //     0xa49294: movk            x17, #0x1, lsl #16
    //     0xa49298: add             lr, x0, x17
    //     0xa4929c: ldr             lr, [x21, lr, lsl #3]
    //     0xa492a0: blr             lr
    // 0xa492a4: mov             x2, x0
    // 0xa492a8: ldur            x0, [fp, #-0x58]
    // 0xa492ac: stur            x2, [fp, #-0x10]
    // 0xa492b0: LoadField: r1 = r0->field_b
    //     0xa492b0: ldur            w1, [x0, #0xb]
    // 0xa492b4: LoadField: r3 = r0->field_f
    //     0xa492b4: ldur            w3, [x0, #0xf]
    // 0xa492b8: DecompressPointer r3
    //     0xa492b8: add             x3, x3, HEAP, lsl #32
    // 0xa492bc: LoadField: r4 = r3->field_b
    //     0xa492bc: ldur            w4, [x3, #0xb]
    // 0xa492c0: r3 = LoadInt32Instr(r1)
    //     0xa492c0: sbfx            x3, x1, #1, #0x1f
    // 0xa492c4: stur            x3, [fp, #-0x60]
    // 0xa492c8: r1 = LoadInt32Instr(r4)
    //     0xa492c8: sbfx            x1, x4, #1, #0x1f
    // 0xa492cc: cmp             x3, x1
    // 0xa492d0: b.ne            #0xa492dc
    // 0xa492d4: mov             x1, x0
    // 0xa492d8: r0 = _growToNextCapacity()
    //     0xa492d8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa492dc: ldur            x2, [fp, #-0x58]
    // 0xa492e0: ldur            x3, [fp, #-0x60]
    // 0xa492e4: add             x4, x3, #1
    // 0xa492e8: lsl             x5, x4, #1
    // 0xa492ec: StoreField: r2->field_b = r5
    //     0xa492ec: stur            w5, [x2, #0xb]
    // 0xa492f0: LoadField: r1 = r2->field_f
    //     0xa492f0: ldur            w1, [x2, #0xf]
    // 0xa492f4: DecompressPointer r1
    //     0xa492f4: add             x1, x1, HEAP, lsl #32
    // 0xa492f8: ldur            x0, [fp, #-0x10]
    // 0xa492fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa492fc: add             x25, x1, x3, lsl #2
    //     0xa49300: add             x25, x25, #0xf
    //     0xa49304: str             w0, [x25]
    //     0xa49308: tbz             w0, #0, #0xa49324
    //     0xa4930c: ldurb           w16, [x1, #-1]
    //     0xa49310: ldurb           w17, [x0, #-1]
    //     0xa49314: and             x16, x17, x16, lsr #2
    //     0xa49318: tst             x16, HEAP, lsr #32
    //     0xa4931c: b.eq            #0xa49324
    //     0xa49320: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa49324: b               #0xa4932c
    // 0xa49328: ldur            x2, [fp, #-0x58]
    // 0xa4932c: ldur            x1, [fp, #-0x28]
    // 0xa49330: add             x9, x1, #1
    // 0xa49334: ldur            x5, [fp, #-0x18]
    // 0xa49338: ldur            x7, [fp, #-0x30]
    // 0xa4933c: ldur            x6, [fp, #-0x68]
    // 0xa49340: ldur            x4, [fp, #-0x38]
    // 0xa49344: ldur            x3, [fp, #-0x50]
    // 0xa49348: ldur            x8, [fp, #-8]
    // 0xa4934c: b               #0xa491e0
    // 0xa49350: mov             x1, x7
    // 0xa49354: add             x7, x1, #1
    // 0xa49358: ldur            x4, [fp, #-0x38]
    // 0xa4935c: ldur            x3, [fp, #-0x50]
    // 0xa49360: b               #0xa491a0
    // 0xa49364: mov             x0, x2
    // 0xa49368: LeaveFrame
    //     0xa49368: mov             SP, fp
    //     0xa4936c: ldp             fp, lr, [SP], #0x10
    // 0xa49370: ret
    //     0xa49370: ret             
    // 0xa49374: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49374: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49378: b               #0xa48a4c
    // 0xa4937c: r0 = StackOverflowSharedWithFPURegs()
    //     0xa4937c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa49380: b               #0xa48ad0
    // 0xa49384: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa49384: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa49388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4938c: b               #0xa48dfc
    // 0xa49390: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa49390: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa49394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49394: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49398: b               #0xa48f04
    // 0xa4939c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4939c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa493a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa493a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa493a4: b               #0xa48fc8
    // 0xa493a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa493a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa493ac: b               #0xa49000
    // 0xa493b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa493b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa493b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa493b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa493b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa493b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa493bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa493bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa493c0: b               #0xa491b8
    // 0xa493c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa493c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa493c8: b               #0xa491f0
    // 0xa493cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa493cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa493d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa493d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa493d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa493d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _errorCorrectPolynomial(/* No info */) {
    // ** addr: 0xa49c6c, size: 0x18c
    // 0xa49c6c: EnterFrame
    //     0xa49c6c: stp             fp, lr, [SP, #-0x10]!
    //     0xa49c70: mov             fp, SP
    // 0xa49c74: AllocStack(0x28)
    //     0xa49c74: sub             SP, SP, #0x28
    // 0xa49c78: r0 = 2
    //     0xa49c78: movz            x0, #0x2
    // 0xa49c7c: mov             x3, x1
    // 0xa49c80: stur            x1, [fp, #-8]
    // 0xa49c84: CheckStackOverflow
    //     0xa49c84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49c88: cmp             SP, x16
    //     0xa49c8c: b.ls            #0xa49ddc
    // 0xa49c90: mov             x2, x0
    // 0xa49c94: r1 = Null
    //     0xa49c94: mov             x1, NULL
    // 0xa49c98: r0 = AllocateArray()
    //     0xa49c98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa49c9c: stur            x0, [fp, #-0x10]
    // 0xa49ca0: r16 = 2
    //     0xa49ca0: movz            x16, #0x2
    // 0xa49ca4: StoreField: r0->field_f = r16
    //     0xa49ca4: stur            w16, [x0, #0xf]
    // 0xa49ca8: r1 = <int>
    //     0xa49ca8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xa49cac: r0 = AllocateGrowableArray()
    //     0xa49cac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa49cb0: mov             x1, x0
    // 0xa49cb4: ldur            x0, [fp, #-0x10]
    // 0xa49cb8: StoreField: r1->field_f = r0
    //     0xa49cb8: stur            w0, [x1, #0xf]
    // 0xa49cbc: r0 = 2
    //     0xa49cbc: movz            x0, #0x2
    // 0xa49cc0: StoreField: r1->field_b = r0
    //     0xa49cc0: stur            w0, [x1, #0xb]
    // 0xa49cc4: mov             x2, x1
    // 0xa49cc8: r1 = Null
    //     0xa49cc8: mov             x1, NULL
    // 0xa49ccc: r3 = 0
    //     0xa49ccc: movz            x3, #0
    // 0xa49cd0: r0 = QrPolynomial()
    //     0xa49cd0: bl              #0xa49a34  ; [package:qr/src/polynomial.dart] QrPolynomial::QrPolynomial
    // 0xa49cd4: mov             x2, x0
    // 0xa49cd8: r1 = 0
    //     0xa49cd8: movz            x1, #0
    // 0xa49cdc: ldur            x0, [fp, #-8]
    // 0xa49ce0: stur            x2, [fp, #-0x10]
    // 0xa49ce4: stur            x1, [fp, #-0x18]
    // 0xa49ce8: CheckStackOverflow
    //     0xa49ce8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49cec: cmp             SP, x16
    //     0xa49cf0: b.ls            #0xa49de4
    // 0xa49cf4: cmp             x1, x0
    // 0xa49cf8: b.ge            #0xa49dcc
    // 0xa49cfc: r0 = InitLateStaticField(0x173c) // [package:qr/src/math.dart] ::_expTable
    //     0xa49cfc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49d00: ldr             x0, [x0, #0x2e78]
    //     0xa49d04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa49d08: cmp             w0, w16
    //     0xa49d0c: b.ne            #0xa49d1c
    //     0xa49d10: add             x2, PP, #0x57, lsl #12  ; [pp+0x57da8] Field <::._expTable@2681014454>: static late final (offset: 0x173c)
    //     0xa49d14: ldr             x2, [x2, #0xda8]
    //     0xa49d18: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa49d1c: mov             x2, x0
    // 0xa49d20: ldur            x4, [fp, #-0x18]
    // 0xa49d24: r3 = 255
    //     0xa49d24: movz            x3, #0xff
    // 0xa49d28: sdiv            x0, x4, x3
    // 0xa49d2c: msub            x5, x0, x3, x4
    // 0xa49d30: cmp             x5, xzr
    // 0xa49d34: b.lt            #0xa49dec
    // 0xa49d38: LoadField: r0 = r2->field_13
    //     0xa49d38: ldur            w0, [x2, #0x13]
    // 0xa49d3c: r1 = LoadInt32Instr(r0)
    //     0xa49d3c: sbfx            x1, x0, #1, #0x1f
    // 0xa49d40: mov             x0, x1
    // 0xa49d44: mov             x1, x5
    // 0xa49d48: cmp             x1, x0
    // 0xa49d4c: b.hs            #0xa49df4
    // 0xa49d50: ArrayLoad: r0 = r2[r5]  ; List_1
    //     0xa49d50: add             x16, x2, x5
    //     0xa49d54: ldrb            w0, [x16, #0x17]
    // 0xa49d58: stur            x0, [fp, #-0x20]
    // 0xa49d5c: r1 = Null
    //     0xa49d5c: mov             x1, NULL
    // 0xa49d60: r2 = 4
    //     0xa49d60: movz            x2, #0x4
    // 0xa49d64: r0 = AllocateArray()
    //     0xa49d64: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa49d68: stur            x0, [fp, #-0x28]
    // 0xa49d6c: r16 = 2
    //     0xa49d6c: movz            x16, #0x2
    // 0xa49d70: StoreField: r0->field_f = r16
    //     0xa49d70: stur            w16, [x0, #0xf]
    // 0xa49d74: ldur            x1, [fp, #-0x20]
    // 0xa49d78: lsl             x2, x1, #1
    // 0xa49d7c: StoreField: r0->field_13 = r2
    //     0xa49d7c: stur            w2, [x0, #0x13]
    // 0xa49d80: r1 = <int>
    //     0xa49d80: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xa49d84: r0 = AllocateGrowableArray()
    //     0xa49d84: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa49d88: mov             x1, x0
    // 0xa49d8c: ldur            x0, [fp, #-0x28]
    // 0xa49d90: StoreField: r1->field_f = r0
    //     0xa49d90: stur            w0, [x1, #0xf]
    // 0xa49d94: r0 = 4
    //     0xa49d94: movz            x0, #0x4
    // 0xa49d98: StoreField: r1->field_b = r0
    //     0xa49d98: stur            w0, [x1, #0xb]
    // 0xa49d9c: mov             x2, x1
    // 0xa49da0: r1 = Null
    //     0xa49da0: mov             x1, NULL
    // 0xa49da4: r3 = 0
    //     0xa49da4: movz            x3, #0
    // 0xa49da8: r0 = QrPolynomial()
    //     0xa49da8: bl              #0xa49a34  ; [package:qr/src/polynomial.dart] QrPolynomial::QrPolynomial
    // 0xa49dac: ldur            x1, [fp, #-0x10]
    // 0xa49db0: mov             x2, x0
    // 0xa49db4: r0 = multiply()
    //     0xa49db4: bl              #0xa49df8  ; [package:qr/src/polynomial.dart] QrPolynomial::multiply
    // 0xa49db8: ldur            x1, [fp, #-0x18]
    // 0xa49dbc: add             x3, x1, #1
    // 0xa49dc0: mov             x2, x0
    // 0xa49dc4: mov             x1, x3
    // 0xa49dc8: b               #0xa49cdc
    // 0xa49dcc: ldur            x0, [fp, #-0x10]
    // 0xa49dd0: LeaveFrame
    //     0xa49dd0: mov             SP, fp
    //     0xa49dd4: ldp             fp, lr, [SP], #0x10
    // 0xa49dd8: ret
    //     0xa49dd8: ret             
    // 0xa49ddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49ddc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49de0: b               #0xa49c90
    // 0xa49de4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49de4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49de8: b               #0xa49cf4
    // 0xa49dec: add             x5, x5, x3
    // 0xa49df0: b               #0xa49d38
    // 0xa49df4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa49df4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 537, size: 0x28, field offset: 0x8
class QrCode extends Object {

  factory _ QrCode.fromData(/* No info */) {
    // ** addr: 0xa44888, size: 0xac
    // 0xa44888: EnterFrame
    //     0xa44888: stp             fp, lr, [SP, #-0x10]!
    //     0xa4488c: mov             fp, SP
    // 0xa44890: AllocStack(0x20)
    //     0xa44890: sub             SP, SP, #0x20
    // 0xa44894: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xa44894: mov             x0, x2
    //     0xa44898: stur            x2, [fp, #-8]
    // 0xa4489c: CheckStackOverflow
    //     0xa4489c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa448a0: cmp             SP, x16
    //     0xa448a4: b.ls            #0xa4492c
    // 0xa448a8: mov             x2, x0
    // 0xa448ac: r1 = Null
    //     0xa448ac: mov             x1, NULL
    // 0xa448b0: r0 = QrByte()
    //     0xa448b0: bl              #0xa4554c  ; [package:qr/src/byte.dart] QrByte::QrByte
    // 0xa448b4: r1 = Null
    //     0xa448b4: mov             x1, NULL
    // 0xa448b8: r2 = 2
    //     0xa448b8: movz            x2, #0x2
    // 0xa448bc: stur            x0, [fp, #-0x10]
    // 0xa448c0: r0 = AllocateArray()
    //     0xa448c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa448c4: mov             x2, x0
    // 0xa448c8: ldur            x0, [fp, #-0x10]
    // 0xa448cc: stur            x2, [fp, #-0x18]
    // 0xa448d0: StoreField: r2->field_f = r0
    //     0xa448d0: stur            w0, [x2, #0xf]
    // 0xa448d4: r1 = <QrDatum>
    //     0xa448d4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57df8] TypeArguments: <QrDatum>
    //     0xa448d8: ldr             x1, [x1, #0xdf8]
    // 0xa448dc: r0 = AllocateGrowableArray()
    //     0xa448dc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa448e0: mov             x1, x0
    // 0xa448e4: ldur            x0, [fp, #-0x18]
    // 0xa448e8: StoreField: r1->field_f = r0
    //     0xa448e8: stur            w0, [x1, #0xf]
    // 0xa448ec: r0 = 2
    //     0xa448ec: movz            x0, #0x2
    // 0xa448f0: StoreField: r1->field_b = r0
    //     0xa448f0: stur            w0, [x1, #0xb]
    // 0xa448f4: r0 = _calculateTypeNumberFromData()
    //     0xa448f4: bl              #0xa44b54  ; [package:qr/src/qr_code.dart] QrCode::_calculateTypeNumberFromData
    // 0xa448f8: stur            x0, [fp, #-0x20]
    // 0xa448fc: r0 = QrCode()
    //     0xa448fc: bl              #0xa44b48  ; AllocateQrCodeStub -> QrCode (size=0x28)
    // 0xa44900: mov             x1, x0
    // 0xa44904: ldur            x2, [fp, #-0x20]
    // 0xa44908: stur            x0, [fp, #-0x10]
    // 0xa4490c: r0 = QrCode()
    //     0xa4490c: bl              #0xa44a80  ; [package:qr/src/qr_code.dart] QrCode::QrCode
    // 0xa44910: ldur            x1, [fp, #-0x10]
    // 0xa44914: ldur            x2, [fp, #-8]
    // 0xa44918: r0 = addData()
    //     0xa44918: bl              #0xa44934  ; [package:qr/src/qr_code.dart] QrCode::addData
    // 0xa4491c: ldur            x0, [fp, #-0x10]
    // 0xa44920: LeaveFrame
    //     0xa44920: mov             SP, fp
    //     0xa44924: ldp             fp, lr, [SP], #0x10
    // 0xa44928: ret
    //     0xa44928: ret             
    // 0xa4492c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4492c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44930: b               #0xa448a8
  }
  _ addData(/* No info */) {
    // ** addr: 0xa44934, size: 0x4c
    // 0xa44934: EnterFrame
    //     0xa44934: stp             fp, lr, [SP, #-0x10]!
    //     0xa44938: mov             fp, SP
    // 0xa4493c: AllocStack(0x8)
    //     0xa4493c: sub             SP, SP, #8
    // 0xa44940: SetupParameters(QrCode this /* r1 => r0, fp-0x8 */)
    //     0xa44940: mov             x0, x1
    //     0xa44944: stur            x1, [fp, #-8]
    // 0xa44948: CheckStackOverflow
    //     0xa44948: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4494c: cmp             SP, x16
    //     0xa44950: b.ls            #0xa44978
    // 0xa44954: r1 = Null
    //     0xa44954: mov             x1, NULL
    // 0xa44958: r0 = QrByte()
    //     0xa44958: bl              #0xa4554c  ; [package:qr/src/byte.dart] QrByte::QrByte
    // 0xa4495c: ldur            x1, [fp, #-8]
    // 0xa44960: mov             x2, x0
    // 0xa44964: r0 = _addToList()
    //     0xa44964: bl              #0xa44980  ; [package:qr/src/qr_code.dart] QrCode::_addToList
    // 0xa44968: r0 = Null
    //     0xa44968: mov             x0, NULL
    // 0xa4496c: LeaveFrame
    //     0xa4496c: mov             SP, fp
    //     0xa44970: ldp             fp, lr, [SP], #0x10
    // 0xa44974: ret
    //     0xa44974: ret             
    // 0xa44978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44978: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4497c: b               #0xa44954
  }
  _ _addToList(/* No info */) {
    // ** addr: 0xa44980, size: 0x100
    // 0xa44980: EnterFrame
    //     0xa44980: stp             fp, lr, [SP, #-0x10]!
    //     0xa44984: mov             fp, SP
    // 0xa44988: AllocStack(0x20)
    //     0xa44988: sub             SP, SP, #0x20
    // 0xa4498c: SetupParameters(QrCode this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xa4498c: mov             x4, x1
    //     0xa44990: mov             x3, x2
    //     0xa44994: stur            x1, [fp, #-0x10]
    //     0xa44998: stur            x2, [fp, #-0x18]
    // 0xa4499c: CheckStackOverflow
    //     0xa4499c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa449a0: cmp             SP, x16
    //     0xa449a4: b.ls            #0xa44a78
    // 0xa449a8: LoadField: r5 = r4->field_23
    //     0xa449a8: ldur            w5, [x4, #0x23]
    // 0xa449ac: DecompressPointer r5
    //     0xa449ac: add             x5, x5, HEAP, lsl #32
    // 0xa449b0: stur            x5, [fp, #-8]
    // 0xa449b4: LoadField: r2 = r5->field_7
    //     0xa449b4: ldur            w2, [x5, #7]
    // 0xa449b8: DecompressPointer r2
    //     0xa449b8: add             x2, x2, HEAP, lsl #32
    // 0xa449bc: mov             x0, x3
    // 0xa449c0: r1 = Null
    //     0xa449c0: mov             x1, NULL
    // 0xa449c4: cmp             w2, NULL
    // 0xa449c8: b.eq            #0xa449e8
    // 0xa449cc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa449cc: ldur            w4, [x2, #0x17]
    // 0xa449d0: DecompressPointer r4
    //     0xa449d0: add             x4, x4, HEAP, lsl #32
    // 0xa449d4: r8 = X0
    //     0xa449d4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa449d8: LoadField: r9 = r4->field_7
    //     0xa449d8: ldur            x9, [x4, #7]
    // 0xa449dc: r3 = Null
    //     0xa449dc: add             x3, PP, #0x57, lsl #12  ; [pp+0x57e00] Null
    //     0xa449e0: ldr             x3, [x3, #0xe00]
    // 0xa449e4: blr             x9
    // 0xa449e8: ldur            x0, [fp, #-8]
    // 0xa449ec: LoadField: r1 = r0->field_b
    //     0xa449ec: ldur            w1, [x0, #0xb]
    // 0xa449f0: LoadField: r2 = r0->field_f
    //     0xa449f0: ldur            w2, [x0, #0xf]
    // 0xa449f4: DecompressPointer r2
    //     0xa449f4: add             x2, x2, HEAP, lsl #32
    // 0xa449f8: LoadField: r3 = r2->field_b
    //     0xa449f8: ldur            w3, [x2, #0xb]
    // 0xa449fc: r2 = LoadInt32Instr(r1)
    //     0xa449fc: sbfx            x2, x1, #1, #0x1f
    // 0xa44a00: stur            x2, [fp, #-0x20]
    // 0xa44a04: r1 = LoadInt32Instr(r3)
    //     0xa44a04: sbfx            x1, x3, #1, #0x1f
    // 0xa44a08: cmp             x2, x1
    // 0xa44a0c: b.ne            #0xa44a18
    // 0xa44a10: mov             x1, x0
    // 0xa44a14: r0 = _growToNextCapacity()
    //     0xa44a14: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa44a18: ldur            x4, [fp, #-0x10]
    // 0xa44a1c: ldur            x2, [fp, #-8]
    // 0xa44a20: ldur            x3, [fp, #-0x20]
    // 0xa44a24: add             x5, x3, #1
    // 0xa44a28: lsl             x6, x5, #1
    // 0xa44a2c: StoreField: r2->field_b = r6
    //     0xa44a2c: stur            w6, [x2, #0xb]
    // 0xa44a30: LoadField: r1 = r2->field_f
    //     0xa44a30: ldur            w1, [x2, #0xf]
    // 0xa44a34: DecompressPointer r1
    //     0xa44a34: add             x1, x1, HEAP, lsl #32
    // 0xa44a38: ldur            x0, [fp, #-0x18]
    // 0xa44a3c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa44a3c: add             x25, x1, x3, lsl #2
    //     0xa44a40: add             x25, x25, #0xf
    //     0xa44a44: str             w0, [x25]
    //     0xa44a48: tbz             w0, #0, #0xa44a64
    //     0xa44a4c: ldurb           w16, [x1, #-1]
    //     0xa44a50: ldurb           w17, [x0, #-1]
    //     0xa44a54: and             x16, x17, x16, lsr #2
    //     0xa44a58: tst             x16, HEAP, lsr #32
    //     0xa44a5c: b.eq            #0xa44a64
    //     0xa44a60: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa44a64: StoreField: r4->field_1f = rNULL
    //     0xa44a64: stur            NULL, [x4, #0x1f]
    // 0xa44a68: r0 = Null
    //     0xa44a68: mov             x0, NULL
    // 0xa44a6c: LeaveFrame
    //     0xa44a6c: mov             SP, fp
    //     0xa44a70: ldp             fp, lr, [SP], #0x10
    // 0xa44a74: ret
    //     0xa44a74: ret             
    // 0xa44a78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44a78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44a7c: b               #0xa449a8
  }
  _ QrCode(/* No info */) {
    // ** addr: 0xa44a80, size: 0xc8
    // 0xa44a80: EnterFrame
    //     0xa44a80: stp             fp, lr, [SP, #-0x10]!
    //     0xa44a84: mov             fp, SP
    // 0xa44a88: AllocStack(0x18)
    //     0xa44a88: sub             SP, SP, #0x18
    // 0xa44a8c: SetupParameters(QrCode this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa44a8c: mov             x3, x1
    //     0xa44a90: mov             x0, x2
    //     0xa44a94: stur            x1, [fp, #-8]
    //     0xa44a98: stur            x2, [fp, #-0x10]
    // 0xa44a9c: CheckStackOverflow
    //     0xa44a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44aa0: cmp             SP, x16
    //     0xa44aa4: b.ls            #0xa44b40
    // 0xa44aa8: r1 = <QrDatum>
    //     0xa44aa8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57df8] TypeArguments: <QrDatum>
    //     0xa44aac: ldr             x1, [x1, #0xdf8]
    // 0xa44ab0: r2 = 0
    //     0xa44ab0: movz            x2, #0
    // 0xa44ab4: r0 = _GrowableList()
    //     0xa44ab4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa44ab8: ldur            x1, [fp, #-8]
    // 0xa44abc: StoreField: r1->field_23 = r0
    //     0xa44abc: stur            w0, [x1, #0x23]
    //     0xa44ac0: ldurb           w16, [x1, #-1]
    //     0xa44ac4: ldurb           w17, [x0, #-1]
    //     0xa44ac8: and             x16, x17, x16, lsr #2
    //     0xa44acc: tst             x16, HEAP, lsr #32
    //     0xa44ad0: b.eq            #0xa44ad8
    //     0xa44ad4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa44ad8: ldur            x0, [fp, #-0x10]
    // 0xa44adc: StoreField: r1->field_7 = r0
    //     0xa44adc: stur            x0, [x1, #7]
    // 0xa44ae0: r4 = 1
    //     0xa44ae0: movz            x4, #0x1
    // 0xa44ae4: StoreField: r1->field_f = r4
    //     0xa44ae4: stur            x4, [x1, #0xf]
    // 0xa44ae8: lsl             x2, x0, #2
    // 0xa44aec: add             x3, x2, #0x11
    // 0xa44af0: ArrayStore: r1[0] = r3  ; List_8
    //     0xa44af0: stur            x3, [x1, #0x17]
    // 0xa44af4: mov             x1, x0
    // 0xa44af8: mov             x2, x4
    // 0xa44afc: r3 = 40
    //     0xa44afc: movz            x3, #0x28
    // 0xa44b00: r5 = "typeNumber"
    //     0xa44b00: add             x5, PP, #0x57, lsl #12  ; [pp+0x57e10] "typeNumber"
    //     0xa44b04: ldr             x5, [x5, #0xe10]
    // 0xa44b08: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xa44b08: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xa44b0c: r0 = checkValueInInterval()
    //     0xa44b0c: bl              #0x64422c  ; [dart:core] RangeError::checkValueInInterval
    // 0xa44b10: r16 = "errorCorrectLevel"
    //     0xa44b10: add             x16, PP, #0x57, lsl #12  ; [pp+0x57e18] "errorCorrectLevel"
    //     0xa44b14: ldr             x16, [x16, #0xe18]
    // 0xa44b18: str             x16, [SP]
    // 0xa44b1c: r1 = 1
    //     0xa44b1c: movz            x1, #0x1
    // 0xa44b20: r2 = const [0x1, 0, 0x3, 0x2]
    //     0xa44b20: add             x2, PP, #0x57, lsl #12  ; [pp+0x57e20] List<int>(4)
    //     0xa44b24: ldr             x2, [x2, #0xe20]
    // 0xa44b28: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xa44b28: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xa44b2c: r0 = checkValidIndex()
    //     0xa44b2c: bl              #0x6732dc  ; [dart:core] RangeError::checkValidIndex
    // 0xa44b30: r0 = Null
    //     0xa44b30: mov             x0, NULL
    // 0xa44b34: LeaveFrame
    //     0xa44b34: mov             SP, fp
    //     0xa44b38: ldp             fp, lr, [SP], #0x10
    // 0xa44b3c: ret
    //     0xa44b3c: ret             
    // 0xa44b40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44b40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44b44: b               #0xa44aa8
  }
  static _ _calculateTypeNumberFromData(/* No info */) {
    // ** addr: 0xa44b54, size: 0x4d8
    // 0xa44b54: EnterFrame
    //     0xa44b54: stp             fp, lr, [SP, #-0x10]!
    //     0xa44b58: mov             fp, SP
    // 0xa44b5c: AllocStack(0x60)
    //     0xa44b5c: sub             SP, SP, #0x60
    // 0xa44b60: SetupParameters(dynamic _ /* r1 => r0, fp-0x10 */)
    //     0xa44b60: mov             x0, x1
    //     0xa44b64: stur            x1, [fp, #-0x10]
    // 0xa44b68: CheckStackOverflow
    //     0xa44b68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44b6c: cmp             SP, x16
    //     0xa44b70: b.ls            #0xa44f6c
    // 0xa44b74: r2 = 1
    //     0xa44b74: movz            x2, #0x1
    // 0xa44b78: stur            x2, [fp, #-8]
    // 0xa44b7c: CheckStackOverflow
    //     0xa44b7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44b80: cmp             SP, x16
    //     0xa44b84: b.ls            #0xa44f74
    // 0xa44b88: cmp             x2, #0x28
    // 0xa44b8c: b.ge            #0xa44ef4
    // 0xa44b90: mov             x1, x2
    // 0xa44b94: r0 = getRSBlocks()
    //     0xa44b94: bl              #0xa452c0  ; [package:qr/src/rs_block.dart] QrRsBlock::getRSBlocks
    // 0xa44b98: stur            x0, [fp, #-0x18]
    // 0xa44b9c: r0 = QrBitBuffer()
    //     0xa44b9c: bl              #0xa452b4  ; AllocateQrBitBufferStub -> QrBitBuffer (size=0x14)
    // 0xa44ba0: stur            x0, [fp, #-0x20]
    // 0xa44ba4: StoreField: r0->field_b = rZR
    //     0xa44ba4: stur            xzr, [x0, #0xb]
    // 0xa44ba8: r1 = <int>
    //     0xa44ba8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xa44bac: r2 = 0
    //     0xa44bac: movz            x2, #0
    // 0xa44bb0: r0 = _GrowableList()
    //     0xa44bb0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa44bb4: ldur            x3, [fp, #-0x20]
    // 0xa44bb8: StoreField: r3->field_7 = r0
    //     0xa44bb8: stur            w0, [x3, #7]
    //     0xa44bbc: ldurb           w16, [x3, #-1]
    //     0xa44bc0: ldurb           w17, [x0, #-1]
    //     0xa44bc4: and             x16, x17, x16, lsr #2
    //     0xa44bc8: tst             x16, HEAP, lsr #32
    //     0xa44bcc: b.eq            #0xa44bd4
    //     0xa44bd0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa44bd4: ldur            x0, [fp, #-0x18]
    // 0xa44bd8: LoadField: r1 = r0->field_b
    //     0xa44bd8: ldur            w1, [x0, #0xb]
    // 0xa44bdc: r2 = LoadInt32Instr(r1)
    //     0xa44bdc: sbfx            x2, x1, #1, #0x1f
    // 0xa44be0: LoadField: r1 = r0->field_f
    //     0xa44be0: ldur            w1, [x0, #0xf]
    // 0xa44be4: DecompressPointer r1
    //     0xa44be4: add             x1, x1, HEAP, lsl #32
    // 0xa44be8: r4 = 0
    //     0xa44be8: movz            x4, #0
    // 0xa44bec: r0 = 0
    //     0xa44bec: movz            x0, #0
    // 0xa44bf0: stur            x4, [fp, #-0x38]
    // 0xa44bf4: CheckStackOverflow
    //     0xa44bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44bf8: cmp             SP, x16
    //     0xa44bfc: b.ls            #0xa44f7c
    // 0xa44c00: cmp             x0, x2
    // 0xa44c04: b.ge            #0xa44c2c
    // 0xa44c08: ArrayLoad: r5 = r1[r0]  ; Unknown_4
    //     0xa44c08: add             x16, x1, x0, lsl #2
    //     0xa44c0c: ldur            w5, [x16, #0xf]
    // 0xa44c10: DecompressPointer r5
    //     0xa44c10: add             x5, x5, HEAP, lsl #32
    // 0xa44c14: LoadField: r6 = r5->field_f
    //     0xa44c14: ldur            x6, [x5, #0xf]
    // 0xa44c18: add             x5, x4, x6
    // 0xa44c1c: add             x6, x0, #1
    // 0xa44c20: mov             x4, x5
    // 0xa44c24: mov             x0, x6
    // 0xa44c28: b               #0xa44bf0
    // 0xa44c2c: r9 = 0
    //     0xa44c2c: movz            x9, #0
    // 0xa44c30: ldur            x0, [fp, #-0x10]
    // 0xa44c34: ldur            x8, [fp, #-8]
    // 0xa44c38: r7 = 4
    //     0xa44c38: movz            x7, #0x4
    // 0xa44c3c: r6 = 4
    //     0xa44c3c: movz            x6, #0x4
    // 0xa44c40: r5 = 1
    //     0xa44c40: movz            x5, #0x1
    // 0xa44c44: stur            x9, [fp, #-0x30]
    // 0xa44c48: CheckStackOverflow
    //     0xa44c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44c4c: cmp             SP, x16
    //     0xa44c50: b.ls            #0xa44f84
    // 0xa44c54: LoadField: r1 = r0->field_b
    //     0xa44c54: ldur            w1, [x0, #0xb]
    // 0xa44c58: r2 = LoadInt32Instr(r1)
    //     0xa44c58: sbfx            x2, x1, #1, #0x1f
    // 0xa44c5c: cmp             x9, x2
    // 0xa44c60: b.ge            #0xa44ec4
    // 0xa44c64: LoadField: r1 = r0->field_f
    //     0xa44c64: ldur            w1, [x0, #0xf]
    // 0xa44c68: DecompressPointer r1
    //     0xa44c68: add             x1, x1, HEAP, lsl #32
    // 0xa44c6c: ArrayLoad: r10 = r1[r9]  ; Unknown_4
    //     0xa44c6c: add             x16, x1, x9, lsl #2
    //     0xa44c70: ldur            w10, [x16, #0xf]
    // 0xa44c74: DecompressPointer r10
    //     0xa44c74: add             x10, x10, HEAP, lsl #32
    // 0xa44c78: stur            x10, [fp, #-0x18]
    // 0xa44c7c: r11 = 0
    //     0xa44c7c: movz            x11, #0
    // 0xa44c80: stur            x11, [fp, #-0x28]
    // 0xa44c84: CheckStackOverflow
    //     0xa44c84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44c88: cmp             SP, x16
    //     0xa44c8c: b.ls            #0xa44f8c
    // 0xa44c90: cmp             x11, #4
    // 0xa44c94: b.ge            #0xa44d04
    // 0xa44c98: sub             x1, x7, x11
    // 0xa44c9c: sub             x2, x1, #1
    // 0xa44ca0: tbnz            x2, #0x3f, #0xa44f94
    // 0xa44ca4: lsr             w1, w6, w2
    // 0xa44ca8: cmp             x2, #0x1f
    // 0xa44cac: csel            x1, x1, xzr, le
    // 0xa44cb0: and             x2, x1, x5
    // 0xa44cb4: ubfx            x2, x2, #0, #0x20
    // 0xa44cb8: cmp             x2, #1
    // 0xa44cbc: r16 = true
    //     0xa44cbc: add             x16, NULL, #0x20  ; true
    // 0xa44cc0: r17 = false
    //     0xa44cc0: add             x17, NULL, #0x30  ; false
    // 0xa44cc4: csel            x1, x16, x17, eq
    // 0xa44cc8: mov             x2, x1
    // 0xa44ccc: mov             x1, x3
    // 0xa44cd0: r0 = putBit()
    //     0xa44cd0: bl              #0xa4513c  ; [package:qr/src/bit_buffer.dart] QrBitBuffer::putBit
    // 0xa44cd4: ldur            x0, [fp, #-0x28]
    // 0xa44cd8: add             x11, x0, #1
    // 0xa44cdc: ldur            x0, [fp, #-0x10]
    // 0xa44ce0: ldur            x8, [fp, #-8]
    // 0xa44ce4: ldur            x3, [fp, #-0x20]
    // 0xa44ce8: ldur            x4, [fp, #-0x38]
    // 0xa44cec: ldur            x9, [fp, #-0x30]
    // 0xa44cf0: ldur            x10, [fp, #-0x18]
    // 0xa44cf4: r7 = 4
    //     0xa44cf4: movz            x7, #0x4
    // 0xa44cf8: r6 = 4
    //     0xa44cf8: movz            x6, #0x4
    // 0xa44cfc: r5 = 1
    //     0xa44cfc: movz            x5, #0x1
    // 0xa44d00: b               #0xa44c80
    // 0xa44d04: mov             x0, x8
    // 0xa44d08: mov             x1, x10
    // 0xa44d0c: LoadField: r3 = r1->field_f
    //     0xa44d0c: ldur            w3, [x1, #0xf]
    // 0xa44d10: DecompressPointer r3
    //     0xa44d10: add             x3, x3, HEAP, lsl #32
    // 0xa44d14: stur            x3, [fp, #-0x50]
    // 0xa44d18: LoadField: r4 = r3->field_13
    //     0xa44d18: ldur            w4, [x3, #0x13]
    // 0xa44d1c: stur            x4, [fp, #-0x18]
    // 0xa44d20: cmp             x0, #1
    // 0xa44d24: b.lt            #0xa44d38
    // 0xa44d28: cmp             x0, #0xa
    // 0xa44d2c: b.ge            #0xa44d38
    // 0xa44d30: r5 = 8
    //     0xa44d30: movz            x5, #0x8
    // 0xa44d34: b               #0xa44d54
    // 0xa44d38: cmp             x0, #0x1b
    // 0xa44d3c: b.ge            #0xa44d48
    // 0xa44d40: r5 = 16
    //     0xa44d40: movz            x5, #0x10
    // 0xa44d44: b               #0xa44d54
    // 0xa44d48: cmp             x0, #0x29
    // 0xa44d4c: b.ge            #0xa44f04
    // 0xa44d50: r5 = 16
    //     0xa44d50: movz            x5, #0x10
    // 0xa44d54: stur            x5, [fp, #-0x48]
    // 0xa44d58: r6 = LoadInt32Instr(r4)
    //     0xa44d58: sbfx            x6, x4, #1, #0x1f
    // 0xa44d5c: stur            x6, [fp, #-0x40]
    // 0xa44d60: r8 = 0
    //     0xa44d60: movz            x8, #0
    // 0xa44d64: r7 = 1
    //     0xa44d64: movz            x7, #0x1
    // 0xa44d68: stur            x8, [fp, #-0x28]
    // 0xa44d6c: CheckStackOverflow
    //     0xa44d6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44d70: cmp             SP, x16
    //     0xa44d74: b.ls            #0xa44fc4
    // 0xa44d78: cmp             x8, x5
    // 0xa44d7c: b.ge            #0xa44ddc
    // 0xa44d80: sub             x1, x5, x8
    // 0xa44d84: sub             x2, x1, #1
    // 0xa44d88: tbnz            x2, #0x3f, #0xa44fcc
    // 0xa44d8c: lsr             w1, w6, w2
    // 0xa44d90: cmp             x2, #0x1f
    // 0xa44d94: csel            x1, x1, xzr, le
    // 0xa44d98: and             x2, x1, x7
    // 0xa44d9c: ubfx            x2, x2, #0, #0x20
    // 0xa44da0: cmp             x2, #1
    // 0xa44da4: r16 = true
    //     0xa44da4: add             x16, NULL, #0x20  ; true
    // 0xa44da8: r17 = false
    //     0xa44da8: add             x17, NULL, #0x30  ; false
    // 0xa44dac: csel            x1, x16, x17, eq
    // 0xa44db0: mov             x2, x1
    // 0xa44db4: ldur            x1, [fp, #-0x20]
    // 0xa44db8: r0 = putBit()
    //     0xa44db8: bl              #0xa4513c  ; [package:qr/src/bit_buffer.dart] QrBitBuffer::putBit
    // 0xa44dbc: ldur            x0, [fp, #-0x28]
    // 0xa44dc0: add             x8, x0, #1
    // 0xa44dc4: ldur            x0, [fp, #-8]
    // 0xa44dc8: ldur            x3, [fp, #-0x50]
    // 0xa44dcc: ldur            x5, [fp, #-0x48]
    // 0xa44dd0: ldur            x4, [fp, #-0x18]
    // 0xa44dd4: ldur            x6, [fp, #-0x40]
    // 0xa44dd8: b               #0xa44d64
    // 0xa44ddc: mov             x0, x4
    // 0xa44de0: r3 = LoadInt32Instr(r0)
    //     0xa44de0: sbfx            x3, x0, #1, #0x1f
    // 0xa44de4: stur            x3, [fp, #-0x58]
    // 0xa44de8: r1 = -1
    //     0xa44de8: movn            x1, #0
    // 0xa44dec: ldur            x0, [fp, #-0x50]
    // 0xa44df0: r5 = 8
    //     0xa44df0: movz            x5, #0x8
    // 0xa44df4: r4 = 1
    //     0xa44df4: movz            x4, #0x1
    // 0xa44df8: CheckStackOverflow
    //     0xa44df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44dfc: cmp             SP, x16
    //     0xa44e00: b.ls            #0xa44ff4
    // 0xa44e04: add             x6, x1, #1
    // 0xa44e08: stur            x6, [fp, #-0x48]
    // 0xa44e0c: cmp             x6, x3
    // 0xa44e10: b.ge            #0xa44eb0
    // 0xa44e14: ArrayLoad: r1 = r0[r6]  ; List_1
    //     0xa44e14: add             x16, x0, x6
    //     0xa44e18: ldrb            w1, [x16, #0x17]
    // 0xa44e1c: mov             x7, x1
    // 0xa44e20: ubfx            x7, x7, #0, #0x20
    // 0xa44e24: stur            x7, [fp, #-0x40]
    // 0xa44e28: r8 = 0
    //     0xa44e28: movz            x8, #0
    // 0xa44e2c: stur            x8, [fp, #-0x28]
    // 0xa44e30: CheckStackOverflow
    //     0xa44e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa44e34: cmp             SP, x16
    //     0xa44e38: b.ls            #0xa44ffc
    // 0xa44e3c: cmp             x8, #8
    // 0xa44e40: b.ge            #0xa44ea4
    // 0xa44e44: sub             x1, x5, x8
    // 0xa44e48: sub             x2, x1, #1
    // 0xa44e4c: tbnz            x2, #0x3f, #0xa45004
    // 0xa44e50: lsr             w1, w7, w2
    // 0xa44e54: cmp             x2, #0x1f
    // 0xa44e58: csel            x1, x1, xzr, le
    // 0xa44e5c: and             x2, x1, x4
    // 0xa44e60: ubfx            x2, x2, #0, #0x20
    // 0xa44e64: cmp             x2, #1
    // 0xa44e68: r16 = true
    //     0xa44e68: add             x16, NULL, #0x20  ; true
    // 0xa44e6c: r17 = false
    //     0xa44e6c: add             x17, NULL, #0x30  ; false
    // 0xa44e70: csel            x1, x16, x17, eq
    // 0xa44e74: mov             x2, x1
    // 0xa44e78: ldur            x1, [fp, #-0x20]
    // 0xa44e7c: r0 = putBit()
    //     0xa44e7c: bl              #0xa4513c  ; [package:qr/src/bit_buffer.dart] QrBitBuffer::putBit
    // 0xa44e80: ldur            x0, [fp, #-0x28]
    // 0xa44e84: add             x8, x0, #1
    // 0xa44e88: ldur            x0, [fp, #-0x50]
    // 0xa44e8c: ldur            x6, [fp, #-0x48]
    // 0xa44e90: ldur            x3, [fp, #-0x58]
    // 0xa44e94: ldur            x7, [fp, #-0x40]
    // 0xa44e98: r5 = 8
    //     0xa44e98: movz            x5, #0x8
    // 0xa44e9c: r4 = 1
    //     0xa44e9c: movz            x4, #0x1
    // 0xa44ea0: b               #0xa44e2c
    // 0xa44ea4: ldur            x1, [fp, #-0x48]
    // 0xa44ea8: ldur            x3, [fp, #-0x58]
    // 0xa44eac: b               #0xa44dec
    // 0xa44eb0: ldur            x0, [fp, #-0x30]
    // 0xa44eb4: add             x9, x0, #1
    // 0xa44eb8: ldur            x3, [fp, #-0x20]
    // 0xa44ebc: ldur            x4, [fp, #-0x38]
    // 0xa44ec0: b               #0xa44c30
    // 0xa44ec4: mov             x0, x3
    // 0xa44ec8: mov             x1, x4
    // 0xa44ecc: LoadField: r2 = r0->field_b
    //     0xa44ecc: ldur            x2, [x0, #0xb]
    // 0xa44ed0: lsl             x0, x1, #3
    // 0xa44ed4: cmp             x2, x0
    // 0xa44ed8: b.le            #0xa44eec
    // 0xa44edc: ldur            x0, [fp, #-8]
    // 0xa44ee0: add             x2, x0, #1
    // 0xa44ee4: ldur            x0, [fp, #-0x10]
    // 0xa44ee8: b               #0xa44b78
    // 0xa44eec: ldur            x0, [fp, #-8]
    // 0xa44ef0: b               #0xa44ef8
    // 0xa44ef4: mov             x0, x2
    // 0xa44ef8: LeaveFrame
    //     0xa44ef8: mov             SP, fp
    //     0xa44efc: ldp             fp, lr, [SP], #0x10
    // 0xa44f00: ret
    //     0xa44f00: ret             
    // 0xa44f04: r1 = Null
    //     0xa44f04: mov             x1, NULL
    // 0xa44f08: r2 = 4
    //     0xa44f08: movz            x2, #0x4
    // 0xa44f0c: r0 = AllocateArray()
    //     0xa44f0c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa44f10: mov             x2, x0
    // 0xa44f14: r16 = "type:"
    //     0xa44f14: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d88] "type:"
    //     0xa44f18: ldr             x16, [x16, #0xd88]
    // 0xa44f1c: StoreField: r2->field_f = r16
    //     0xa44f1c: stur            w16, [x2, #0xf]
    // 0xa44f20: ldur            x3, [fp, #-8]
    // 0xa44f24: r0 = BoxInt64Instr(r3)
    //     0xa44f24: sbfiz           x0, x3, #1, #0x1f
    //     0xa44f28: cmp             x3, x0, asr #1
    //     0xa44f2c: b.eq            #0xa44f38
    //     0xa44f30: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa44f34: stur            x3, [x0, #7]
    // 0xa44f38: StoreField: r2->field_13 = r0
    //     0xa44f38: stur            w0, [x2, #0x13]
    // 0xa44f3c: str             x2, [SP]
    // 0xa44f40: r0 = _interpolate()
    //     0xa44f40: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa44f44: stur            x0, [fp, #-0x10]
    // 0xa44f48: r0 = ArgumentError()
    //     0xa44f48: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xa44f4c: mov             x1, x0
    // 0xa44f50: ldur            x0, [fp, #-0x10]
    // 0xa44f54: ArrayStore: r1[0] = r0  ; List_4
    //     0xa44f54: stur            w0, [x1, #0x17]
    // 0xa44f58: r0 = false
    //     0xa44f58: add             x0, NULL, #0x30  ; false
    // 0xa44f5c: StoreField: r1->field_b = r0
    //     0xa44f5c: stur            w0, [x1, #0xb]
    // 0xa44f60: mov             x0, x1
    // 0xa44f64: r0 = Throw()
    //     0xa44f64: bl              #0xec04b8  ; ThrowStub
    // 0xa44f68: brk             #0
    // 0xa44f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44f6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44f70: b               #0xa44b74
    // 0xa44f74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44f74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44f78: b               #0xa44b88
    // 0xa44f7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44f7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44f80: b               #0xa44c00
    // 0xa44f84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44f84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44f88: b               #0xa44c54
    // 0xa44f8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44f8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44f90: b               #0xa44c90
    // 0xa44f94: str             x2, [THR, #0x7a8]  ; THR::
    // 0xa44f98: stp             x10, x11, [SP, #-0x10]!
    // 0xa44f9c: stp             x8, x9, [SP, #-0x10]!
    // 0xa44fa0: stp             x6, x7, [SP, #-0x10]!
    // 0xa44fa4: stp             x4, x5, [SP, #-0x10]!
    // 0xa44fa8: stp             x2, x3, [SP, #-0x10]!
    // 0xa44fac: SaveReg r0
    //     0xa44fac: str             x0, [SP, #-8]!
    // 0xa44fb0: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa44fb4: r4 = 0
    //     0xa44fb4: movz            x4, #0
    // 0xa44fb8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa44fbc: blr             lr
    // 0xa44fc0: brk             #0
    // 0xa44fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44fc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44fc8: b               #0xa44d78
    // 0xa44fcc: str             x2, [THR, #0x7a8]  ; THR::
    // 0xa44fd0: stp             x7, x8, [SP, #-0x10]!
    // 0xa44fd4: stp             x5, x6, [SP, #-0x10]!
    // 0xa44fd8: stp             x3, x4, [SP, #-0x10]!
    // 0xa44fdc: stp             x0, x2, [SP, #-0x10]!
    // 0xa44fe0: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa44fe4: r4 = 0
    //     0xa44fe4: movz            x4, #0
    // 0xa44fe8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa44fec: blr             lr
    // 0xa44ff0: brk             #0
    // 0xa44ff4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44ff4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44ff8: b               #0xa44e04
    // 0xa44ffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44ffc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45000: b               #0xa44e3c
    // 0xa45004: str             x2, [THR, #0x7a8]  ; THR::
    // 0xa45008: stp             x7, x8, [SP, #-0x10]!
    // 0xa4500c: stp             x5, x6, [SP, #-0x10]!
    // 0xa45010: stp             x3, x4, [SP, #-0x10]!
    // 0xa45014: stp             x0, x2, [SP, #-0x10]!
    // 0xa45018: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa4501c: r4 = 0
    //     0xa4501c: movz            x4, #0
    // 0xa45020: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa45024: blr             lr
    // 0xa45028: brk             #0
  }
  get _ dataCache(/* No info */) {
    // ** addr: 0xa48248, size: 0x84
    // 0xa48248: EnterFrame
    //     0xa48248: stp             fp, lr, [SP, #-0x10]!
    //     0xa4824c: mov             fp, SP
    // 0xa48250: AllocStack(0x8)
    //     0xa48250: sub             SP, SP, #8
    // 0xa48254: SetupParameters(QrCode this /* r1 => r0, fp-0x8 */)
    //     0xa48254: mov             x0, x1
    //     0xa48258: stur            x1, [fp, #-8]
    // 0xa4825c: CheckStackOverflow
    //     0xa4825c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48260: cmp             SP, x16
    //     0xa48264: b.ls            #0xa482c4
    // 0xa48268: LoadField: r1 = r0->field_1f
    //     0xa48268: ldur            w1, [x0, #0x1f]
    // 0xa4826c: DecompressPointer r1
    //     0xa4826c: add             x1, x1, HEAP, lsl #32
    // 0xa48270: cmp             w1, NULL
    // 0xa48274: b.ne            #0xa482b4
    // 0xa48278: LoadField: r1 = r0->field_7
    //     0xa48278: ldur            x1, [x0, #7]
    // 0xa4827c: LoadField: r2 = r0->field_23
    //     0xa4827c: ldur            w2, [x0, #0x23]
    // 0xa48280: DecompressPointer r2
    //     0xa48280: add             x2, x2, HEAP, lsl #32
    // 0xa48284: r0 = _createData()
    //     0xa48284: bl              #0xa482cc  ; [package:qr/src/qr_code.dart] ::_createData
    // 0xa48288: mov             x1, x0
    // 0xa4828c: ldur            x2, [fp, #-8]
    // 0xa48290: StoreField: r2->field_1f = r0
    //     0xa48290: stur            w0, [x2, #0x1f]
    //     0xa48294: ldurb           w16, [x2, #-1]
    //     0xa48298: ldurb           w17, [x0, #-1]
    //     0xa4829c: and             x16, x17, x16, lsr #2
    //     0xa482a0: tst             x16, HEAP, lsr #32
    //     0xa482a4: b.eq            #0xa482ac
    //     0xa482a8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa482ac: mov             x0, x1
    // 0xa482b0: b               #0xa482b8
    // 0xa482b4: mov             x0, x1
    // 0xa482b8: LeaveFrame
    //     0xa482b8: mov             SP, fp
    //     0xa482bc: ldp             fp, lr, [SP], #0x10
    // 0xa482c0: ret
    //     0xa482c0: ret             
    // 0xa482c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa482c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa482c8: b               #0xa48268
  }
}
