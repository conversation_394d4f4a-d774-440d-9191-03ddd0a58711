// lib: , url: package:qr/src/qr_image.dart

// class id: 1051068, size: 0x8
class :: {

  static _ _lostPoint(/* No info */) {
    // ** addr: 0xa45ec4, size: 0x7c4
    // 0xa45ec4: EnterFrame
    //     0xa45ec4: stp             fp, lr, [SP, #-0x10]!
    //     0xa45ec8: mov             fp, SP
    // 0xa45ecc: AllocStack(0x58)
    //     0xa45ecc: sub             SP, SP, #0x58
    // 0xa45ed0: SetupParameters(dynamic _ /* r1 => r0, fp-0x20 */)
    //     0xa45ed0: mov             x0, x1
    //     0xa45ed4: stur            x1, [fp, #-0x20]
    // 0xa45ed8: CheckStackOverflow
    //     0xa45ed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45edc: cmp             SP, x16
    //     0xa45ee0: b.ls            #0xa4660c
    // 0xa45ee4: LoadField: r4 = r0->field_7
    //     0xa45ee4: ldur            x4, [x0, #7]
    // 0xa45ee8: stur            x4, [fp, #-0x18]
    // 0xa45eec: d0 = 0.000000
    //     0xa45eec: eor             v0.16b, v0.16b, v0.16b
    // 0xa45ef0: r5 = 0
    //     0xa45ef0: movz            x5, #0
    // 0xa45ef4: stur            x5, [fp, #-0x10]
    // 0xa45ef8: CheckStackOverflow
    //     0xa45ef8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45efc: cmp             SP, x16
    //     0xa45f00: b.ls            #0xa46614
    // 0xa45f04: cmp             x5, x4
    // 0xa45f08: b.ge            #0xa46098
    // 0xa45f0c: r6 = 0
    //     0xa45f0c: movz            x6, #0
    // 0xa45f10: stur            x6, [fp, #-8]
    // 0xa45f14: stur            d0, [fp, #-0x50]
    // 0xa45f18: CheckStackOverflow
    //     0xa45f18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45f1c: cmp             SP, x16
    //     0xa45f20: b.ls            #0xa4661c
    // 0xa45f24: cmp             x6, x4
    // 0xa45f28: b.ge            #0xa46084
    // 0xa45f2c: mov             x1, x0
    // 0xa45f30: mov             x2, x5
    // 0xa45f34: mov             x3, x6
    // 0xa45f38: r0 = isDark()
    //     0xa45f38: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa45f3c: stur            x0, [fp, #-0x48]
    // 0xa45f40: r1 = 0
    //     0xa45f40: movz            x1, #0
    // 0xa45f44: r7 = -1
    //     0xa45f44: movn            x7, #0
    // 0xa45f48: ldur            x4, [fp, #-0x18]
    // 0xa45f4c: ldur            x5, [fp, #-0x10]
    // 0xa45f50: ldur            x6, [fp, #-8]
    // 0xa45f54: stur            x7, [fp, #-0x40]
    // 0xa45f58: CheckStackOverflow
    //     0xa45f58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45f5c: cmp             SP, x16
    //     0xa45f60: b.ls            #0xa46624
    // 0xa45f64: cmp             x7, #1
    // 0xa45f68: b.gt            #0xa46044
    // 0xa45f6c: add             x8, x5, x7
    // 0xa45f70: stur            x8, [fp, #-0x38]
    // 0xa45f74: tbnz            x8, #0x3f, #0xa45f80
    // 0xa45f78: cmp             x4, x8
    // 0xa45f7c: b.gt            #0xa45f8c
    // 0xa45f80: mov             x2, x1
    // 0xa45f84: mov             x1, x7
    // 0xa45f88: b               #0xa46038
    // 0xa45f8c: mov             x10, x1
    // 0xa45f90: r9 = -1
    //     0xa45f90: movn            x9, #0
    // 0xa45f94: stur            x10, [fp, #-0x28]
    // 0xa45f98: stur            x9, [fp, #-0x30]
    // 0xa45f9c: CheckStackOverflow
    //     0xa45f9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45fa0: cmp             SP, x16
    //     0xa45fa4: b.ls            #0xa4662c
    // 0xa45fa8: cmp             x9, #1
    // 0xa45fac: b.gt            #0xa4602c
    // 0xa45fb0: add             x3, x6, x9
    // 0xa45fb4: tbnz            x3, #0x3f, #0xa45fc0
    // 0xa45fb8: cmp             x4, x3
    // 0xa45fbc: b.gt            #0xa45fc8
    // 0xa45fc0: mov             x1, x9
    // 0xa45fc4: b               #0xa46010
    // 0xa45fc8: cbnz            x7, #0xa45fd8
    // 0xa45fcc: cbnz            x9, #0xa45fd8
    // 0xa45fd0: mov             x1, x9
    // 0xa45fd4: b               #0xa46010
    // 0xa45fd8: ldur            x1, [fp, #-0x20]
    // 0xa45fdc: mov             x2, x8
    // 0xa45fe0: r0 = isDark()
    //     0xa45fe0: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa45fe4: mov             x1, x0
    // 0xa45fe8: ldur            x0, [fp, #-0x48]
    // 0xa45fec: cmp             w0, w1
    // 0xa45ff0: b.ne            #0xa46004
    // 0xa45ff4: ldur            x1, [fp, #-0x28]
    // 0xa45ff8: add             x2, x1, #1
    // 0xa45ffc: mov             x1, x2
    // 0xa46000: b               #0xa46008
    // 0xa46004: ldur            x1, [fp, #-0x28]
    // 0xa46008: mov             x10, x1
    // 0xa4600c: ldur            x1, [fp, #-0x30]
    // 0xa46010: add             x9, x1, #1
    // 0xa46014: ldur            x4, [fp, #-0x18]
    // 0xa46018: ldur            x5, [fp, #-0x10]
    // 0xa4601c: ldur            x6, [fp, #-8]
    // 0xa46020: ldur            x7, [fp, #-0x40]
    // 0xa46024: ldur            x8, [fp, #-0x38]
    // 0xa46028: b               #0xa45f94
    // 0xa4602c: mov             x1, x10
    // 0xa46030: mov             x2, x1
    // 0xa46034: ldur            x1, [fp, #-0x40]
    // 0xa46038: add             x7, x1, #1
    // 0xa4603c: mov             x1, x2
    // 0xa46040: b               #0xa45f48
    // 0xa46044: cmp             x1, #5
    // 0xa46048: b.le            #0xa46068
    // 0xa4604c: ldur            d0, [fp, #-0x50]
    // 0xa46050: add             x0, x1, #3
    // 0xa46054: sub             x1, x0, #5
    // 0xa46058: scvtf           d1, x1
    // 0xa4605c: fadd            d2, d0, d1
    // 0xa46060: mov             v0.16b, v2.16b
    // 0xa46064: b               #0xa4606c
    // 0xa46068: ldur            d0, [fp, #-0x50]
    // 0xa4606c: ldur            x0, [fp, #-8]
    // 0xa46070: add             x6, x0, #1
    // 0xa46074: ldur            x0, [fp, #-0x20]
    // 0xa46078: ldur            x4, [fp, #-0x18]
    // 0xa4607c: ldur            x5, [fp, #-0x10]
    // 0xa46080: b               #0xa45f10
    // 0xa46084: mov             x0, x5
    // 0xa46088: add             x5, x0, #1
    // 0xa4608c: ldur            x0, [fp, #-0x20]
    // 0xa46090: ldur            x4, [fp, #-0x18]
    // 0xa46094: b               #0xa45ef4
    // 0xa46098: mov             x0, x4
    // 0xa4609c: sub             x4, x0, #1
    // 0xa460a0: stur            x4, [fp, #-0x30]
    // 0xa460a4: r5 = 0
    //     0xa460a4: movz            x5, #0
    // 0xa460a8: stur            x5, [fp, #-0x28]
    // 0xa460ac: CheckStackOverflow
    //     0xa460ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa460b0: cmp             SP, x16
    //     0xa460b4: b.ls            #0xa46634
    // 0xa460b8: cmp             x5, x4
    // 0xa460bc: b.ge            #0xa461f8
    // 0xa460c0: add             x6, x5, #1
    // 0xa460c4: stur            x6, [fp, #-0x10]
    // 0xa460c8: r7 = 0
    //     0xa460c8: movz            x7, #0
    // 0xa460cc: stur            x7, [fp, #-8]
    // 0xa460d0: stur            d0, [fp, #-0x50]
    // 0xa460d4: CheckStackOverflow
    //     0xa460d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa460d8: cmp             SP, x16
    //     0xa460dc: b.ls            #0xa4663c
    // 0xa460e0: cmp             x7, x4
    // 0xa460e4: b.ge            #0xa461e0
    // 0xa460e8: ldur            x1, [fp, #-0x20]
    // 0xa460ec: mov             x2, x5
    // 0xa460f0: mov             x3, x7
    // 0xa460f4: r0 = isDark()
    //     0xa460f4: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa460f8: tst             x0, #0x10
    // 0xa460fc: cset            x4, eq
    // 0xa46100: lsl             x4, x4, #1
    // 0xa46104: ldur            x1, [fp, #-0x20]
    // 0xa46108: ldur            x2, [fp, #-0x10]
    // 0xa4610c: ldur            x3, [fp, #-8]
    // 0xa46110: stur            x4, [fp, #-0x48]
    // 0xa46114: r0 = isDark()
    //     0xa46114: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa46118: tbnz            w0, #4, #0xa46130
    // 0xa4611c: ldur            x0, [fp, #-0x48]
    // 0xa46120: r1 = LoadInt32Instr(r0)
    //     0xa46120: sbfx            x1, x0, #1, #0x1f
    // 0xa46124: add             x0, x1, #1
    // 0xa46128: mov             x4, x0
    // 0xa4612c: b               #0xa4613c
    // 0xa46130: ldur            x0, [fp, #-0x48]
    // 0xa46134: r1 = LoadInt32Instr(r0)
    //     0xa46134: sbfx            x1, x0, #1, #0x1f
    // 0xa46138: mov             x4, x1
    // 0xa4613c: ldur            x0, [fp, #-8]
    // 0xa46140: stur            x4, [fp, #-0x40]
    // 0xa46144: add             x5, x0, #1
    // 0xa46148: ldur            x1, [fp, #-0x20]
    // 0xa4614c: ldur            x2, [fp, #-0x28]
    // 0xa46150: mov             x3, x5
    // 0xa46154: stur            x5, [fp, #-0x38]
    // 0xa46158: r0 = isDark()
    //     0xa46158: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa4615c: tbnz            w0, #4, #0xa46170
    // 0xa46160: ldur            x0, [fp, #-0x40]
    // 0xa46164: add             x1, x0, #1
    // 0xa46168: mov             x0, x1
    // 0xa4616c: b               #0xa46174
    // 0xa46170: ldur            x0, [fp, #-0x40]
    // 0xa46174: ldur            x1, [fp, #-0x20]
    // 0xa46178: ldur            x2, [fp, #-0x10]
    // 0xa4617c: ldur            x3, [fp, #-0x38]
    // 0xa46180: stur            x0, [fp, #-8]
    // 0xa46184: r0 = isDark()
    //     0xa46184: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa46188: tbnz            w0, #4, #0xa4619c
    // 0xa4618c: ldur            x0, [fp, #-8]
    // 0xa46190: add             x1, x0, #1
    // 0xa46194: mov             x0, x1
    // 0xa46198: b               #0xa461a0
    // 0xa4619c: ldur            x0, [fp, #-8]
    // 0xa461a0: cbz             x0, #0xa461ac
    // 0xa461a4: cmp             x0, #4
    // 0xa461a8: b.ne            #0xa461c0
    // 0xa461ac: ldur            d0, [fp, #-0x50]
    // 0xa461b0: d1 = 3.000000
    //     0xa461b0: fmov            d1, #3.00000000
    // 0xa461b4: fadd            d2, d0, d1
    // 0xa461b8: mov             v0.16b, v2.16b
    // 0xa461bc: b               #0xa461c8
    // 0xa461c0: ldur            d0, [fp, #-0x50]
    // 0xa461c4: d1 = 3.000000
    //     0xa461c4: fmov            d1, #3.00000000
    // 0xa461c8: ldur            x7, [fp, #-0x38]
    // 0xa461cc: ldur            x0, [fp, #-0x18]
    // 0xa461d0: ldur            x5, [fp, #-0x28]
    // 0xa461d4: ldur            x4, [fp, #-0x30]
    // 0xa461d8: ldur            x6, [fp, #-0x10]
    // 0xa461dc: b               #0xa460cc
    // 0xa461e0: mov             x0, x5
    // 0xa461e4: d1 = 3.000000
    //     0xa461e4: fmov            d1, #3.00000000
    // 0xa461e8: add             x5, x0, #1
    // 0xa461ec: ldur            x0, [fp, #-0x18]
    // 0xa461f0: ldur            x4, [fp, #-0x30]
    // 0xa461f4: b               #0xa460a8
    // 0xa461f8: sub             x4, x0, #6
    // 0xa461fc: stur            x4, [fp, #-0x28]
    // 0xa46200: r5 = 0
    //     0xa46200: movz            x5, #0
    // 0xa46204: stur            x5, [fp, #-0x10]
    // 0xa46208: CheckStackOverflow
    //     0xa46208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4620c: cmp             SP, x16
    //     0xa46210: b.ls            #0xa46644
    // 0xa46214: cmp             x5, x0
    // 0xa46218: b.ge            #0xa46374
    // 0xa4621c: r6 = 0
    //     0xa4621c: movz            x6, #0
    // 0xa46220: stur            x6, [fp, #-8]
    // 0xa46224: stur            d0, [fp, #-0x50]
    // 0xa46228: CheckStackOverflow
    //     0xa46228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4622c: cmp             SP, x16
    //     0xa46230: b.ls            #0xa4664c
    // 0xa46234: cmp             x6, x4
    // 0xa46238: b.ge            #0xa4635c
    // 0xa4623c: ldur            x1, [fp, #-0x20]
    // 0xa46240: mov             x2, x5
    // 0xa46244: mov             x3, x6
    // 0xa46248: r0 = isDark()
    //     0xa46248: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa4624c: tbnz            w0, #4, #0xa4633c
    // 0xa46250: ldur            x0, [fp, #-8]
    // 0xa46254: add             x3, x0, #1
    // 0xa46258: ldur            x1, [fp, #-0x20]
    // 0xa4625c: ldur            x2, [fp, #-0x10]
    // 0xa46260: r0 = isDark()
    //     0xa46260: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa46264: tbz             w0, #4, #0xa46330
    // 0xa46268: ldur            x0, [fp, #-8]
    // 0xa4626c: add             x3, x0, #2
    // 0xa46270: ldur            x1, [fp, #-0x20]
    // 0xa46274: ldur            x2, [fp, #-0x10]
    // 0xa46278: r0 = isDark()
    //     0xa46278: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa4627c: tbnz            w0, #4, #0xa46324
    // 0xa46280: ldur            x0, [fp, #-8]
    // 0xa46284: add             x3, x0, #3
    // 0xa46288: ldur            x1, [fp, #-0x20]
    // 0xa4628c: ldur            x2, [fp, #-0x10]
    // 0xa46290: r0 = isDark()
    //     0xa46290: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa46294: tbnz            w0, #4, #0xa46318
    // 0xa46298: ldur            x0, [fp, #-8]
    // 0xa4629c: add             x3, x0, #4
    // 0xa462a0: ldur            x1, [fp, #-0x20]
    // 0xa462a4: ldur            x2, [fp, #-0x10]
    // 0xa462a8: r0 = isDark()
    //     0xa462a8: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa462ac: tbnz            w0, #4, #0xa4630c
    // 0xa462b0: ldur            x0, [fp, #-8]
    // 0xa462b4: add             x3, x0, #5
    // 0xa462b8: ldur            x1, [fp, #-0x20]
    // 0xa462bc: ldur            x2, [fp, #-0x10]
    // 0xa462c0: r0 = isDark()
    //     0xa462c0: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa462c4: tbz             w0, #4, #0xa46300
    // 0xa462c8: ldur            x0, [fp, #-8]
    // 0xa462cc: add             x3, x0, #6
    // 0xa462d0: ldur            x1, [fp, #-0x20]
    // 0xa462d4: ldur            x2, [fp, #-0x10]
    // 0xa462d8: r0 = isDark()
    //     0xa462d8: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa462dc: tbnz            w0, #4, #0xa462f4
    // 0xa462e0: ldur            d0, [fp, #-0x50]
    // 0xa462e4: d1 = 40.000000
    //     0xa462e4: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa462e8: fadd            d2, d0, d1
    // 0xa462ec: mov             v0.16b, v2.16b
    // 0xa462f0: b               #0xa46344
    // 0xa462f4: ldur            d0, [fp, #-0x50]
    // 0xa462f8: d1 = 40.000000
    //     0xa462f8: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa462fc: b               #0xa46344
    // 0xa46300: ldur            d0, [fp, #-0x50]
    // 0xa46304: d1 = 40.000000
    //     0xa46304: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa46308: b               #0xa46344
    // 0xa4630c: ldur            d0, [fp, #-0x50]
    // 0xa46310: d1 = 40.000000
    //     0xa46310: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa46314: b               #0xa46344
    // 0xa46318: ldur            d0, [fp, #-0x50]
    // 0xa4631c: d1 = 40.000000
    //     0xa4631c: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa46320: b               #0xa46344
    // 0xa46324: ldur            d0, [fp, #-0x50]
    // 0xa46328: d1 = 40.000000
    //     0xa46328: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa4632c: b               #0xa46344
    // 0xa46330: ldur            d0, [fp, #-0x50]
    // 0xa46334: d1 = 40.000000
    //     0xa46334: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa46338: b               #0xa46344
    // 0xa4633c: ldur            d0, [fp, #-0x50]
    // 0xa46340: d1 = 40.000000
    //     0xa46340: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa46344: ldur            x0, [fp, #-8]
    // 0xa46348: add             x6, x0, #1
    // 0xa4634c: ldur            x0, [fp, #-0x18]
    // 0xa46350: ldur            x5, [fp, #-0x10]
    // 0xa46354: ldur            x4, [fp, #-0x28]
    // 0xa46358: b               #0xa46220
    // 0xa4635c: mov             x0, x5
    // 0xa46360: d1 = 40.000000
    //     0xa46360: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa46364: add             x5, x0, #1
    // 0xa46368: ldur            x0, [fp, #-0x18]
    // 0xa4636c: ldur            x4, [fp, #-0x28]
    // 0xa46370: b               #0xa46204
    // 0xa46374: d1 = 40.000000
    //     0xa46374: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa46378: sub             x4, x0, #6
    // 0xa4637c: stur            x4, [fp, #-0x28]
    // 0xa46380: r5 = 0
    //     0xa46380: movz            x5, #0
    // 0xa46384: stur            x5, [fp, #-0x10]
    // 0xa46388: stur            d0, [fp, #-0x58]
    // 0xa4638c: CheckStackOverflow
    //     0xa4638c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46390: cmp             SP, x16
    //     0xa46394: b.ls            #0xa46654
    // 0xa46398: cmp             x5, x0
    // 0xa4639c: b.ge            #0xa464f4
    // 0xa463a0: r6 = 0
    //     0xa463a0: movz            x6, #0
    // 0xa463a4: stur            x6, [fp, #-8]
    // 0xa463a8: stur            d0, [fp, #-0x50]
    // 0xa463ac: CheckStackOverflow
    //     0xa463ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa463b0: cmp             SP, x16
    //     0xa463b4: b.ls            #0xa4665c
    // 0xa463b8: cmp             x6, x4
    // 0xa463bc: b.ge            #0xa464e0
    // 0xa463c0: ldur            x1, [fp, #-0x20]
    // 0xa463c4: mov             x2, x6
    // 0xa463c8: mov             x3, x5
    // 0xa463cc: r0 = isDark()
    //     0xa463cc: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa463d0: tbnz            w0, #4, #0xa464c0
    // 0xa463d4: ldur            x0, [fp, #-8]
    // 0xa463d8: add             x2, x0, #1
    // 0xa463dc: ldur            x1, [fp, #-0x20]
    // 0xa463e0: ldur            x3, [fp, #-0x10]
    // 0xa463e4: r0 = isDark()
    //     0xa463e4: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa463e8: tbz             w0, #4, #0xa464b4
    // 0xa463ec: ldur            x0, [fp, #-8]
    // 0xa463f0: add             x2, x0, #2
    // 0xa463f4: ldur            x1, [fp, #-0x20]
    // 0xa463f8: ldur            x3, [fp, #-0x10]
    // 0xa463fc: r0 = isDark()
    //     0xa463fc: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa46400: tbnz            w0, #4, #0xa464a8
    // 0xa46404: ldur            x0, [fp, #-8]
    // 0xa46408: add             x2, x0, #3
    // 0xa4640c: ldur            x1, [fp, #-0x20]
    // 0xa46410: ldur            x3, [fp, #-0x10]
    // 0xa46414: r0 = isDark()
    //     0xa46414: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa46418: tbnz            w0, #4, #0xa4649c
    // 0xa4641c: ldur            x0, [fp, #-8]
    // 0xa46420: add             x2, x0, #4
    // 0xa46424: ldur            x1, [fp, #-0x20]
    // 0xa46428: ldur            x3, [fp, #-0x10]
    // 0xa4642c: r0 = isDark()
    //     0xa4642c: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa46430: tbnz            w0, #4, #0xa46490
    // 0xa46434: ldur            x0, [fp, #-8]
    // 0xa46438: add             x2, x0, #5
    // 0xa4643c: ldur            x1, [fp, #-0x20]
    // 0xa46440: ldur            x3, [fp, #-0x10]
    // 0xa46444: r0 = isDark()
    //     0xa46444: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa46448: tbz             w0, #4, #0xa46484
    // 0xa4644c: ldur            x0, [fp, #-8]
    // 0xa46450: add             x2, x0, #6
    // 0xa46454: ldur            x1, [fp, #-0x20]
    // 0xa46458: ldur            x3, [fp, #-0x10]
    // 0xa4645c: r0 = isDark()
    //     0xa4645c: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa46460: tbnz            w0, #4, #0xa46478
    // 0xa46464: ldur            d0, [fp, #-0x50]
    // 0xa46468: d1 = 40.000000
    //     0xa46468: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa4646c: fadd            d2, d0, d1
    // 0xa46470: mov             v0.16b, v2.16b
    // 0xa46474: b               #0xa464c8
    // 0xa46478: ldur            d0, [fp, #-0x50]
    // 0xa4647c: d1 = 40.000000
    //     0xa4647c: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa46480: b               #0xa464c8
    // 0xa46484: ldur            d0, [fp, #-0x50]
    // 0xa46488: d1 = 40.000000
    //     0xa46488: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa4648c: b               #0xa464c8
    // 0xa46490: ldur            d0, [fp, #-0x50]
    // 0xa46494: d1 = 40.000000
    //     0xa46494: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa46498: b               #0xa464c8
    // 0xa4649c: ldur            d0, [fp, #-0x50]
    // 0xa464a0: d1 = 40.000000
    //     0xa464a0: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa464a4: b               #0xa464c8
    // 0xa464a8: ldur            d0, [fp, #-0x50]
    // 0xa464ac: d1 = 40.000000
    //     0xa464ac: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa464b0: b               #0xa464c8
    // 0xa464b4: ldur            d0, [fp, #-0x50]
    // 0xa464b8: d1 = 40.000000
    //     0xa464b8: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa464bc: b               #0xa464c8
    // 0xa464c0: ldur            d0, [fp, #-0x50]
    // 0xa464c4: d1 = 40.000000
    //     0xa464c4: ldr             d1, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xa464c8: ldur            x0, [fp, #-8]
    // 0xa464cc: add             x6, x0, #1
    // 0xa464d0: ldur            x0, [fp, #-0x18]
    // 0xa464d4: ldur            x5, [fp, #-0x10]
    // 0xa464d8: ldur            x4, [fp, #-0x28]
    // 0xa464dc: b               #0xa463a4
    // 0xa464e0: mov             x0, x5
    // 0xa464e4: add             x5, x0, #1
    // 0xa464e8: ldur            x0, [fp, #-0x18]
    // 0xa464ec: ldur            x4, [fp, #-0x28]
    // 0xa464f0: b               #0xa46384
    // 0xa464f4: r4 = 0
    //     0xa464f4: movz            x4, #0
    // 0xa464f8: r1 = 0
    //     0xa464f8: movz            x1, #0
    // 0xa464fc: ldur            x0, [fp, #-0x18]
    // 0xa46500: stur            x4, [fp, #-0x28]
    // 0xa46504: CheckStackOverflow
    //     0xa46504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46508: cmp             SP, x16
    //     0xa4650c: b.ls            #0xa46664
    // 0xa46510: cmp             x4, x0
    // 0xa46514: b.ge            #0xa46598
    // 0xa46518: mov             x5, x1
    // 0xa4651c: r6 = 0
    //     0xa4651c: movz            x6, #0
    // 0xa46520: stur            x6, [fp, #-8]
    // 0xa46524: stur            x5, [fp, #-0x10]
    // 0xa46528: CheckStackOverflow
    //     0xa46528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4652c: cmp             SP, x16
    //     0xa46530: b.ls            #0xa4666c
    // 0xa46534: cmp             x6, x0
    // 0xa46538: b.ge            #0xa46580
    // 0xa4653c: ldur            x1, [fp, #-0x20]
    // 0xa46540: mov             x2, x6
    // 0xa46544: mov             x3, x4
    // 0xa46548: r0 = isDark()
    //     0xa46548: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0xa4654c: tbnz            w0, #4, #0xa46560
    // 0xa46550: ldur            x0, [fp, #-0x10]
    // 0xa46554: add             x1, x0, #1
    // 0xa46558: mov             x5, x1
    // 0xa4655c: b               #0xa46568
    // 0xa46560: ldur            x0, [fp, #-0x10]
    // 0xa46564: mov             x5, x0
    // 0xa46568: ldur            x0, [fp, #-8]
    // 0xa4656c: add             x6, x0, #1
    // 0xa46570: ldur            x0, [fp, #-0x18]
    // 0xa46574: ldur            d0, [fp, #-0x58]
    // 0xa46578: ldur            x4, [fp, #-0x28]
    // 0xa4657c: b               #0xa46520
    // 0xa46580: mov             x1, x4
    // 0xa46584: mov             x0, x5
    // 0xa46588: add             x4, x1, #1
    // 0xa4658c: mov             x1, x0
    // 0xa46590: ldur            d0, [fp, #-0x58]
    // 0xa46594: b               #0xa464fc
    // 0xa46598: d1 = 50.000000
    //     0xa46598: ldr             d1, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0xa4659c: r16 = 100
    //     0xa4659c: movz            x16, #0x64
    // 0xa465a0: mul             x2, x1, x16
    // 0xa465a4: scvtf           d2, x2
    // 0xa465a8: scvtf           d3, x0
    // 0xa465ac: fdiv            d4, d2, d3
    // 0xa465b0: fdiv            d2, d4, d3
    // 0xa465b4: fsub            d3, d2, d1
    // 0xa465b8: r1 = inline_Allocate_Double()
    //     0xa465b8: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa465bc: add             x1, x1, #0x10
    //     0xa465c0: cmp             x0, x1
    //     0xa465c4: b.ls            #0xa46674
    //     0xa465c8: str             x1, [THR, #0x50]  ; THR::top
    //     0xa465cc: sub             x1, x1, #0xf
    //     0xa465d0: movz            x0, #0xe15c
    //     0xa465d4: movk            x0, #0x3, lsl #16
    //     0xa465d8: stur            x0, [x1, #-1]
    // 0xa465dc: StoreField: r1->field_7 = d3
    //     0xa465dc: stur            d3, [x1, #7]
    // 0xa465e0: r0 = abs()
    //     0xa465e0: bl              #0xebca64  ; [dart:core] _Double::abs
    // 0xa465e4: LoadField: d1 = r0->field_7
    //     0xa465e4: ldur            d1, [x0, #7]
    // 0xa465e8: d2 = 5.000000
    //     0xa465e8: fmov            d2, #5.00000000
    // 0xa465ec: fdiv            d3, d1, d2
    // 0xa465f0: d1 = 10.000000
    //     0xa465f0: fmov            d1, #10.00000000
    // 0xa465f4: fmul            d2, d3, d1
    // 0xa465f8: ldur            d1, [fp, #-0x58]
    // 0xa465fc: fadd            d0, d1, d2
    // 0xa46600: LeaveFrame
    //     0xa46600: mov             SP, fp
    //     0xa46604: ldp             fp, lr, [SP], #0x10
    // 0xa46608: ret
    //     0xa46608: ret             
    // 0xa4660c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4660c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa46610: b               #0xa45ee4
    // 0xa46614: r0 = StackOverflowSharedWithFPURegs()
    //     0xa46614: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46618: b               #0xa45f04
    // 0xa4661c: r0 = StackOverflowSharedWithFPURegs()
    //     0xa4661c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46620: b               #0xa45f24
    // 0xa46624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa46624: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa46628: b               #0xa45f64
    // 0xa4662c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4662c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa46630: b               #0xa45fa8
    // 0xa46634: r0 = StackOverflowSharedWithFPURegs()
    //     0xa46634: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46638: b               #0xa460b8
    // 0xa4663c: r0 = StackOverflowSharedWithFPURegs()
    //     0xa4663c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46640: b               #0xa460e0
    // 0xa46644: r0 = StackOverflowSharedWithFPURegs()
    //     0xa46644: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46648: b               #0xa46214
    // 0xa4664c: r0 = StackOverflowSharedWithFPURegs()
    //     0xa4664c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46650: b               #0xa46234
    // 0xa46654: r0 = StackOverflowSharedWithFPURegs()
    //     0xa46654: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46658: b               #0xa46398
    // 0xa4665c: r0 = StackOverflowSharedWithFPURegs()
    //     0xa4665c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46660: b               #0xa463b8
    // 0xa46664: r0 = StackOverflowSharedWithFPURegs()
    //     0xa46664: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46668: b               #0xa46510
    // 0xa4666c: r0 = StackOverflowSharedWithFPURegs()
    //     0xa4666c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa46670: b               #0xa46534
    // 0xa46674: stp             q0, q3, [SP, #-0x20]!
    // 0xa46678: r0 = AllocateDouble()
    //     0xa46678: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4667c: mov             x1, x0
    // 0xa46680: ldp             q0, q3, [SP], #0x20
    // 0xa46684: b               #0xa465dc
  }
  static _ _mask(/* No info */) {
    // ** addr: 0xa46b20, size: 0x2b8
    // 0xa46b20: EnterFrame
    //     0xa46b20: stp             fp, lr, [SP, #-0x10]!
    //     0xa46b24: mov             fp, SP
    // 0xa46b28: AllocStack(0x18)
    //     0xa46b28: sub             SP, SP, #0x18
    // 0xa46b2c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xa46b2c: mov             x0, x1
    //     0xa46b30: stur            x1, [fp, #-8]
    // 0xa46b34: CheckStackOverflow
    //     0xa46b34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46b38: cmp             SP, x16
    //     0xa46b3c: b.ls            #0xa46da8
    // 0xa46b40: cbnz            x0, #0xa46b7c
    // 0xa46b44: r1 = 1
    //     0xa46b44: movz            x1, #0x1
    // 0xa46b48: mov             x0, x2
    // 0xa46b4c: ubfx            x0, x0, #0, #0x20
    // 0xa46b50: mov             x2, x3
    // 0xa46b54: ubfx            x2, x2, #0, #0x20
    // 0xa46b58: add             w3, w0, w2
    // 0xa46b5c: and             x0, x3, x1
    // 0xa46b60: ubfx            x0, x0, #0, #0x20
    // 0xa46b64: cbz             x0, #0xa46b70
    // 0xa46b68: r1 = false
    //     0xa46b68: add             x1, NULL, #0x30  ; false
    // 0xa46b6c: b               #0xa46b74
    // 0xa46b70: r1 = true
    //     0xa46b70: add             x1, NULL, #0x20  ; true
    // 0xa46b74: mov             x0, x1
    // 0xa46b78: b               #0xa46d34
    // 0xa46b7c: r1 = 1
    //     0xa46b7c: movz            x1, #0x1
    // 0xa46b80: cmp             x0, #1
    // 0xa46b84: b.ne            #0xa46bac
    // 0xa46b88: mov             x0, x2
    // 0xa46b8c: ubfx            x0, x0, #0, #0x20
    // 0xa46b90: and             x2, x0, x1
    // 0xa46b94: ubfx            x2, x2, #0, #0x20
    // 0xa46b98: cbz             x2, #0xa46ba4
    // 0xa46b9c: r0 = false
    //     0xa46b9c: add             x0, NULL, #0x30  ; false
    // 0xa46ba0: b               #0xa46ba8
    // 0xa46ba4: r0 = true
    //     0xa46ba4: add             x0, NULL, #0x20  ; true
    // 0xa46ba8: b               #0xa46d34
    // 0xa46bac: cmp             x0, #2
    // 0xa46bb0: b.ne            #0xa46be0
    // 0xa46bb4: r4 = 3
    //     0xa46bb4: movz            x4, #0x3
    // 0xa46bb8: sdiv            x1, x3, x4
    // 0xa46bbc: msub            x0, x1, x4, x3
    // 0xa46bc0: cmp             x0, xzr
    // 0xa46bc4: b.lt            #0xa46db0
    // 0xa46bc8: cbz             x0, #0xa46bd4
    // 0xa46bcc: r1 = false
    //     0xa46bcc: add             x1, NULL, #0x30  ; false
    // 0xa46bd0: b               #0xa46bd8
    // 0xa46bd4: r1 = true
    //     0xa46bd4: add             x1, NULL, #0x20  ; true
    // 0xa46bd8: mov             x0, x1
    // 0xa46bdc: b               #0xa46d34
    // 0xa46be0: r4 = 3
    //     0xa46be0: movz            x4, #0x3
    // 0xa46be4: cmp             x0, #3
    // 0xa46be8: b.ne            #0xa46c14
    // 0xa46bec: add             x0, x2, x3
    // 0xa46bf0: sdiv            x2, x0, x4
    // 0xa46bf4: msub            x1, x2, x4, x0
    // 0xa46bf8: cmp             x1, xzr
    // 0xa46bfc: b.lt            #0xa46db8
    // 0xa46c00: cbz             x1, #0xa46c0c
    // 0xa46c04: r0 = false
    //     0xa46c04: add             x0, NULL, #0x30  ; false
    // 0xa46c08: b               #0xa46c10
    // 0xa46c0c: r0 = true
    //     0xa46c0c: add             x0, NULL, #0x20  ; true
    // 0xa46c10: b               #0xa46d34
    // 0xa46c14: cmp             x0, #4
    // 0xa46c18: b.ne            #0xa46c54
    // 0xa46c1c: r0 = 2
    //     0xa46c1c: movz            x0, #0x2
    // 0xa46c20: sdiv            x5, x2, x0
    // 0xa46c24: sdiv            x0, x3, x4
    // 0xa46c28: ubfx            x5, x5, #0, #0x20
    // 0xa46c2c: ubfx            x0, x0, #0, #0x20
    // 0xa46c30: add             w2, w5, w0
    // 0xa46c34: and             x0, x2, x1
    // 0xa46c38: ubfx            x0, x0, #0, #0x20
    // 0xa46c3c: cbz             x0, #0xa46c48
    // 0xa46c40: r1 = false
    //     0xa46c40: add             x1, NULL, #0x30  ; false
    // 0xa46c44: b               #0xa46c4c
    // 0xa46c48: r1 = true
    //     0xa46c48: add             x1, NULL, #0x20  ; true
    // 0xa46c4c: mov             x0, x1
    // 0xa46c50: b               #0xa46d34
    // 0xa46c54: cmp             x0, #5
    // 0xa46c58: b.ne            #0xa46c9c
    // 0xa46c5c: mul             x0, x2, x3
    // 0xa46c60: mov             x2, x0
    // 0xa46c64: ubfx            x2, x2, #0, #0x20
    // 0xa46c68: and             x3, x2, x1
    // 0xa46c6c: sdiv            x2, x0, x4
    // 0xa46c70: msub            x1, x2, x4, x0
    // 0xa46c74: cmp             x1, xzr
    // 0xa46c78: b.lt            #0xa46dc0
    // 0xa46c7c: ubfx            x3, x3, #0, #0x20
    // 0xa46c80: add             x0, x3, x1
    // 0xa46c84: cbz             x0, #0xa46c90
    // 0xa46c88: r1 = false
    //     0xa46c88: add             x1, NULL, #0x30  ; false
    // 0xa46c8c: b               #0xa46c94
    // 0xa46c90: r1 = true
    //     0xa46c90: add             x1, NULL, #0x20  ; true
    // 0xa46c94: mov             x0, x1
    // 0xa46c98: b               #0xa46d34
    // 0xa46c9c: cmp             x0, #6
    // 0xa46ca0: b.ne            #0xa46ce8
    // 0xa46ca4: mul             x0, x2, x3
    // 0xa46ca8: mov             x2, x0
    // 0xa46cac: ubfx            x2, x2, #0, #0x20
    // 0xa46cb0: and             x3, x2, x1
    // 0xa46cb4: sdiv            x5, x0, x4
    // 0xa46cb8: msub            x2, x5, x4, x0
    // 0xa46cbc: cmp             x2, xzr
    // 0xa46cc0: b.lt            #0xa46dc8
    // 0xa46cc4: ubfx            x2, x2, #0, #0x20
    // 0xa46cc8: add             w0, w3, w2
    // 0xa46ccc: and             x2, x0, x1
    // 0xa46cd0: ubfx            x2, x2, #0, #0x20
    // 0xa46cd4: cbz             x2, #0xa46ce0
    // 0xa46cd8: r0 = false
    //     0xa46cd8: add             x0, NULL, #0x30  ; false
    // 0xa46cdc: b               #0xa46ce4
    // 0xa46ce0: r0 = true
    //     0xa46ce0: add             x0, NULL, #0x20  ; true
    // 0xa46ce4: b               #0xa46d34
    // 0xa46ce8: cmp             x0, #7
    // 0xa46cec: b.ne            #0xa46d40
    // 0xa46cf0: mul             x0, x2, x3
    // 0xa46cf4: sdiv            x6, x0, x4
    // 0xa46cf8: msub            x5, x6, x4, x0
    // 0xa46cfc: cmp             x5, xzr
    // 0xa46d00: b.lt            #0xa46dd0
    // 0xa46d04: ubfx            x2, x2, #0, #0x20
    // 0xa46d08: ubfx            x3, x3, #0, #0x20
    // 0xa46d0c: add             w0, w2, w3
    // 0xa46d10: and             x2, x0, x1
    // 0xa46d14: ubfx            x5, x5, #0, #0x20
    // 0xa46d18: add             w0, w5, w2
    // 0xa46d1c: and             x2, x0, x1
    // 0xa46d20: ubfx            x2, x2, #0, #0x20
    // 0xa46d24: cbz             x2, #0xa46d30
    // 0xa46d28: r0 = false
    //     0xa46d28: add             x0, NULL, #0x30  ; false
    // 0xa46d2c: b               #0xa46d34
    // 0xa46d30: r0 = true
    //     0xa46d30: add             x0, NULL, #0x20  ; true
    // 0xa46d34: LeaveFrame
    //     0xa46d34: mov             SP, fp
    //     0xa46d38: ldp             fp, lr, [SP], #0x10
    // 0xa46d3c: ret
    //     0xa46d3c: ret             
    // 0xa46d40: r1 = Null
    //     0xa46d40: mov             x1, NULL
    // 0xa46d44: r2 = 4
    //     0xa46d44: movz            x2, #0x4
    // 0xa46d48: r0 = AllocateArray()
    //     0xa46d48: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa46d4c: mov             x2, x0
    // 0xa46d50: r16 = "bad maskPattern:"
    //     0xa46d50: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d68] "bad maskPattern:"
    //     0xa46d54: ldr             x16, [x16, #0xd68]
    // 0xa46d58: StoreField: r2->field_f = r16
    //     0xa46d58: stur            w16, [x2, #0xf]
    // 0xa46d5c: ldur            x3, [fp, #-8]
    // 0xa46d60: r0 = BoxInt64Instr(r3)
    //     0xa46d60: sbfiz           x0, x3, #1, #0x1f
    //     0xa46d64: cmp             x3, x0, asr #1
    //     0xa46d68: b.eq            #0xa46d74
    //     0xa46d6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa46d70: stur            x3, [x0, #7]
    // 0xa46d74: StoreField: r2->field_13 = r0
    //     0xa46d74: stur            w0, [x2, #0x13]
    // 0xa46d78: str             x2, [SP]
    // 0xa46d7c: r0 = _interpolate()
    //     0xa46d7c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa46d80: stur            x0, [fp, #-0x10]
    // 0xa46d84: r0 = ArgumentError()
    //     0xa46d84: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xa46d88: mov             x1, x0
    // 0xa46d8c: ldur            x0, [fp, #-0x10]
    // 0xa46d90: ArrayStore: r1[0] = r0  ; List_4
    //     0xa46d90: stur            w0, [x1, #0x17]
    // 0xa46d94: r0 = false
    //     0xa46d94: add             x0, NULL, #0x30  ; false
    // 0xa46d98: StoreField: r1->field_b = r0
    //     0xa46d98: stur            w0, [x1, #0xb]
    // 0xa46d9c: mov             x0, x1
    // 0xa46da0: r0 = Throw()
    //     0xa46da0: bl              #0xec04b8  ; ThrowStub
    // 0xa46da4: brk             #0
    // 0xa46da8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa46da8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa46dac: b               #0xa46b40
    // 0xa46db0: add             x0, x0, x4
    // 0xa46db4: b               #0xa46bc8
    // 0xa46db8: add             x1, x1, x4
    // 0xa46dbc: b               #0xa46c00
    // 0xa46dc0: add             x1, x1, x4
    // 0xa46dc4: b               #0xa46c7c
    // 0xa46dc8: add             x2, x2, x4
    // 0xa46dcc: b               #0xa46cc4
    // 0xa46dd0: add             x5, x5, x4
    // 0xa46dd4: b               #0xa46d04
  }
}

// class id: 536, size: 0x2c, field offset: 0x8
class QrImage extends Object {

  _ isDark(/* No info */) {
    // ** addr: 0x7d51fc, size: 0x150
    // 0x7d51fc: EnterFrame
    //     0x7d51fc: stp             fp, lr, [SP, #-0x10]!
    //     0x7d5200: mov             fp, SP
    // 0x7d5204: AllocStack(0x20)
    //     0x7d5204: sub             SP, SP, #0x20
    // 0x7d5208: SetupParameters(dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x7d5208: stur            x3, [fp, #-0x10]
    // 0x7d520c: CheckStackOverflow
    //     0x7d520c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d5210: cmp             SP, x16
    //     0x7d5214: b.ls            #0x7d533c
    // 0x7d5218: tbnz            x2, #0x3f, #0x7d52b4
    // 0x7d521c: LoadField: r0 = r1->field_7
    //     0x7d521c: ldur            x0, [x1, #7]
    // 0x7d5220: cmp             x0, x2
    // 0x7d5224: b.le            #0x7d52b4
    // 0x7d5228: tbnz            x3, #0x3f, #0x7d52b4
    // 0x7d522c: cmp             x0, x3
    // 0x7d5230: b.le            #0x7d52b4
    // 0x7d5234: LoadField: r4 = r1->field_27
    //     0x7d5234: ldur            w4, [x1, #0x27]
    // 0x7d5238: DecompressPointer r4
    //     0x7d5238: add             x4, x4, HEAP, lsl #32
    // 0x7d523c: LoadField: r0 = r4->field_b
    //     0x7d523c: ldur            w0, [x4, #0xb]
    // 0x7d5240: r1 = LoadInt32Instr(r0)
    //     0x7d5240: sbfx            x1, x0, #1, #0x1f
    // 0x7d5244: mov             x0, x1
    // 0x7d5248: mov             x1, x2
    // 0x7d524c: cmp             x1, x0
    // 0x7d5250: b.hs            #0x7d5344
    // 0x7d5254: LoadField: r0 = r4->field_f
    //     0x7d5254: ldur            w0, [x4, #0xf]
    // 0x7d5258: DecompressPointer r0
    //     0x7d5258: add             x0, x0, HEAP, lsl #32
    // 0x7d525c: ArrayLoad: r4 = r0[r2]  ; Unknown_4
    //     0x7d525c: add             x16, x0, x2, lsl #2
    //     0x7d5260: ldur            w4, [x16, #0xf]
    // 0x7d5264: DecompressPointer r4
    //     0x7d5264: add             x4, x4, HEAP, lsl #32
    // 0x7d5268: r0 = BoxInt64Instr(r3)
    //     0x7d5268: sbfiz           x0, x3, #1, #0x1f
    //     0x7d526c: cmp             x3, x0, asr #1
    //     0x7d5270: b.eq            #0x7d527c
    //     0x7d5274: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d5278: stur            x3, [x0, #7]
    // 0x7d527c: r1 = LoadClassIdInstr(r4)
    //     0x7d527c: ldur            x1, [x4, #-1]
    //     0x7d5280: ubfx            x1, x1, #0xc, #0x14
    // 0x7d5284: stp             x0, x4, [SP]
    // 0x7d5288: mov             x0, x1
    // 0x7d528c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x7d528c: movz            x17, #0x3037
    //     0x7d5290: movk            x17, #0x1, lsl #16
    //     0x7d5294: add             lr, x0, x17
    //     0x7d5298: ldr             lr, [x21, lr, lsl #3]
    //     0x7d529c: blr             lr
    // 0x7d52a0: cmp             w0, NULL
    // 0x7d52a4: b.eq            #0x7d5348
    // 0x7d52a8: LeaveFrame
    //     0x7d52a8: mov             SP, fp
    //     0x7d52ac: ldp             fp, lr, [SP], #0x10
    // 0x7d52b0: ret
    //     0x7d52b0: ret             
    // 0x7d52b4: r0 = BoxInt64Instr(r2)
    //     0x7d52b4: sbfiz           x0, x2, #1, #0x1f
    //     0x7d52b8: cmp             x2, x0, asr #1
    //     0x7d52bc: b.eq            #0x7d52c8
    //     0x7d52c0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d52c4: stur            x2, [x0, #7]
    // 0x7d52c8: r1 = Null
    //     0x7d52c8: mov             x1, NULL
    // 0x7d52cc: r2 = 6
    //     0x7d52cc: movz            x2, #0x6
    // 0x7d52d0: stur            x0, [fp, #-8]
    // 0x7d52d4: r0 = AllocateArray()
    //     0x7d52d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7d52d8: mov             x2, x0
    // 0x7d52dc: ldur            x0, [fp, #-8]
    // 0x7d52e0: StoreField: r2->field_f = r0
    //     0x7d52e0: stur            w0, [x2, #0xf]
    // 0x7d52e4: r16 = " , "
    //     0x7d52e4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d60] " , "
    //     0x7d52e8: ldr             x16, [x16, #0xd60]
    // 0x7d52ec: StoreField: r2->field_13 = r16
    //     0x7d52ec: stur            w16, [x2, #0x13]
    // 0x7d52f0: ldur            x3, [fp, #-0x10]
    // 0x7d52f4: r0 = BoxInt64Instr(r3)
    //     0x7d52f4: sbfiz           x0, x3, #1, #0x1f
    //     0x7d52f8: cmp             x3, x0, asr #1
    //     0x7d52fc: b.eq            #0x7d5308
    //     0x7d5300: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d5304: stur            x3, [x0, #7]
    // 0x7d5308: ArrayStore: r2[0] = r0  ; List_4
    //     0x7d5308: stur            w0, [x2, #0x17]
    // 0x7d530c: str             x2, [SP]
    // 0x7d5310: r0 = _interpolate()
    //     0x7d5310: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7d5314: stur            x0, [fp, #-8]
    // 0x7d5318: r0 = ArgumentError()
    //     0x7d5318: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x7d531c: mov             x1, x0
    // 0x7d5320: ldur            x0, [fp, #-8]
    // 0x7d5324: ArrayStore: r1[0] = r0  ; List_4
    //     0x7d5324: stur            w0, [x1, #0x17]
    // 0x7d5328: r0 = false
    //     0x7d5328: add             x0, NULL, #0x30  ; false
    // 0x7d532c: StoreField: r1->field_b = r0
    //     0x7d532c: stur            w0, [x1, #0xb]
    // 0x7d5330: mov             x0, x1
    // 0x7d5334: r0 = Throw()
    //     0x7d5334: bl              #0xec04b8  ; ThrowStub
    // 0x7d5338: brk             #0
    // 0x7d533c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d533c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d5340: b               #0x7d5218
    // 0x7d5344: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7d5344: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7d5348: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d5348: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  factory _ QrImage(/* No info */) {
    // ** addr: 0xa45cc0, size: 0x150
    // 0xa45cc0: EnterFrame
    //     0xa45cc0: stp             fp, lr, [SP, #-0x10]!
    //     0xa45cc4: mov             fp, SP
    // 0xa45cc8: AllocStack(0x40)
    //     0xa45cc8: sub             SP, SP, #0x40
    // 0xa45ccc: SetupParameters(dynamic _ /* r2 => r0, fp-0x28 */)
    //     0xa45ccc: mov             x0, x2
    //     0xa45cd0: stur            x2, [fp, #-0x28]
    // 0xa45cd4: CheckStackOverflow
    //     0xa45cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45cd8: cmp             SP, x16
    //     0xa45cdc: b.ls            #0xa45dfc
    // 0xa45ce0: ArrayLoad: r3 = r0[0]  ; List_8
    //     0xa45ce0: ldur            x3, [x0, #0x17]
    // 0xa45ce4: stur            x3, [fp, #-0x20]
    // 0xa45ce8: LoadField: r4 = r0->field_7
    //     0xa45ce8: ldur            x4, [x0, #7]
    // 0xa45cec: stur            x4, [fp, #-0x18]
    // 0xa45cf0: d0 = 0.000000
    //     0xa45cf0: eor             v0.16b, v0.16b, v0.16b
    // 0xa45cf4: r6 = Null
    //     0xa45cf4: mov             x6, NULL
    // 0xa45cf8: r5 = 0
    //     0xa45cf8: movz            x5, #0
    // 0xa45cfc: stur            x6, [fp, #-8]
    // 0xa45d00: stur            x5, [fp, #-0x10]
    // 0xa45d04: stur            d0, [fp, #-0x40]
    // 0xa45d08: CheckStackOverflow
    //     0xa45d08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45d0c: cmp             SP, x16
    //     0xa45d10: b.ls            #0xa45e04
    // 0xa45d14: cmp             x5, #8
    // 0xa45d18: b.ge            #0xa45dc0
    // 0xa45d1c: r1 = <List<bool?>>
    //     0xa45d1c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d58] TypeArguments: <List<bool?>>
    //     0xa45d20: ldr             x1, [x1, #0xd58]
    // 0xa45d24: r2 = 0
    //     0xa45d24: movz            x2, #0
    // 0xa45d28: r0 = _GrowableList()
    //     0xa45d28: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa45d2c: stur            x0, [fp, #-0x30]
    // 0xa45d30: r0 = QrImage()
    //     0xa45d30: bl              #0xa4a140  ; AllocateQrImageStub -> QrImage (size=0x2c)
    // 0xa45d34: mov             x2, x0
    // 0xa45d38: ldur            x0, [fp, #-0x30]
    // 0xa45d3c: stur            x2, [fp, #-0x38]
    // 0xa45d40: StoreField: r2->field_27 = r0
    //     0xa45d40: stur            w0, [x2, #0x27]
    // 0xa45d44: ldur            x0, [fp, #-0x10]
    // 0xa45d48: StoreField: r2->field_1f = r0
    //     0xa45d48: stur            x0, [x2, #0x1f]
    // 0xa45d4c: ldur            x3, [fp, #-0x20]
    // 0xa45d50: StoreField: r2->field_7 = r3
    //     0xa45d50: stur            x3, [x2, #7]
    // 0xa45d54: ldur            x4, [fp, #-0x18]
    // 0xa45d58: StoreField: r2->field_f = r4
    //     0xa45d58: stur            x4, [x2, #0xf]
    // 0xa45d5c: r5 = 1
    //     0xa45d5c: movz            x5, #0x1
    // 0xa45d60: ArrayStore: r2[0] = r5  ; List_8
    //     0xa45d60: stur            x5, [x2, #0x17]
    // 0xa45d64: ldur            x1, [fp, #-0x28]
    // 0xa45d68: r0 = dataCache()
    //     0xa45d68: bl              #0xa48248  ; [package:qr/src/qr_code.dart] QrCode::dataCache
    // 0xa45d6c: ldur            x1, [fp, #-0x38]
    // 0xa45d70: ldur            x2, [fp, #-0x10]
    // 0xa45d74: mov             x3, x0
    // 0xa45d78: r5 = true
    //     0xa45d78: add             x5, NULL, #0x20  ; true
    // 0xa45d7c: r0 = _makeImpl()
    //     0xa45d7c: bl              #0xa46688  ; [package:qr/src/qr_image.dart] QrImage::_makeImpl
    // 0xa45d80: ldur            x1, [fp, #-0x38]
    // 0xa45d84: r0 = _lostPoint()
    //     0xa45d84: bl              #0xa45ec4  ; [package:qr/src/qr_image.dart] ::_lostPoint
    // 0xa45d88: ldur            x0, [fp, #-0x10]
    // 0xa45d8c: cbz             x0, #0xa45d9c
    // 0xa45d90: ldur            d1, [fp, #-0x40]
    // 0xa45d94: fcmp            d1, d0
    // 0xa45d98: b.le            #0xa45da4
    // 0xa45d9c: ldur            x6, [fp, #-0x38]
    // 0xa45da0: b               #0xa45dac
    // 0xa45da4: mov             v0.16b, v1.16b
    // 0xa45da8: ldur            x6, [fp, #-8]
    // 0xa45dac: add             x5, x0, #1
    // 0xa45db0: ldur            x0, [fp, #-0x28]
    // 0xa45db4: ldur            x3, [fp, #-0x20]
    // 0xa45db8: ldur            x4, [fp, #-0x18]
    // 0xa45dbc: b               #0xa45cfc
    // 0xa45dc0: mov             x0, x6
    // 0xa45dc4: cmp             w0, NULL
    // 0xa45dc8: b.eq            #0xa45e0c
    // 0xa45dcc: LoadField: r3 = r0->field_1f
    //     0xa45dcc: ldur            x3, [x0, #0x1f]
    // 0xa45dd0: stur            x3, [fp, #-0x10]
    // 0xa45dd4: r0 = QrImage()
    //     0xa45dd4: bl              #0xa4a140  ; AllocateQrImageStub -> QrImage (size=0x2c)
    // 0xa45dd8: mov             x1, x0
    // 0xa45ddc: ldur            x2, [fp, #-0x28]
    // 0xa45de0: ldur            x3, [fp, #-0x10]
    // 0xa45de4: stur            x0, [fp, #-8]
    // 0xa45de8: r0 = QrImage.withMaskPattern()
    //     0xa45de8: bl              #0xa45e10  ; [package:qr/src/qr_image.dart] QrImage::QrImage.withMaskPattern
    // 0xa45dec: ldur            x0, [fp, #-8]
    // 0xa45df0: LeaveFrame
    //     0xa45df0: mov             SP, fp
    //     0xa45df4: ldp             fp, lr, [SP], #0x10
    // 0xa45df8: ret
    //     0xa45df8: ret             
    // 0xa45dfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa45dfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45e00: b               #0xa45ce0
    // 0xa45e04: r0 = StackOverflowSharedWithFPURegs()
    //     0xa45e04: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa45e08: b               #0xa45d14
    // 0xa45e0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa45e0c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ QrImage.withMaskPattern(/* No info */) {
    // ** addr: 0xa45e10, size: 0xb4
    // 0xa45e10: EnterFrame
    //     0xa45e10: stp             fp, lr, [SP, #-0x10]!
    //     0xa45e14: mov             fp, SP
    // 0xa45e18: AllocStack(0x18)
    //     0xa45e18: sub             SP, SP, #0x18
    // 0xa45e1c: SetupParameters(QrImage this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xa45e1c: mov             x4, x1
    //     0xa45e20: mov             x0, x3
    //     0xa45e24: stur            x3, [fp, #-0x18]
    //     0xa45e28: mov             x3, x2
    //     0xa45e2c: stur            x1, [fp, #-8]
    //     0xa45e30: stur            x2, [fp, #-0x10]
    // 0xa45e34: CheckStackOverflow
    //     0xa45e34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45e38: cmp             SP, x16
    //     0xa45e3c: b.ls            #0xa45ebc
    // 0xa45e40: r1 = <List<bool?>>
    //     0xa45e40: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d58] TypeArguments: <List<bool?>>
    //     0xa45e44: ldr             x1, [x1, #0xd58]
    // 0xa45e48: r2 = 0
    //     0xa45e48: movz            x2, #0
    // 0xa45e4c: r0 = _GrowableList()
    //     0xa45e4c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa45e50: ldur            x2, [fp, #-8]
    // 0xa45e54: StoreField: r2->field_27 = r0
    //     0xa45e54: stur            w0, [x2, #0x27]
    //     0xa45e58: ldurb           w16, [x2, #-1]
    //     0xa45e5c: ldurb           w17, [x0, #-1]
    //     0xa45e60: and             x16, x17, x16, lsr #2
    //     0xa45e64: tst             x16, HEAP, lsr #32
    //     0xa45e68: b.eq            #0xa45e70
    //     0xa45e6c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa45e70: ldur            x0, [fp, #-0x18]
    // 0xa45e74: StoreField: r2->field_1f = r0
    //     0xa45e74: stur            x0, [x2, #0x1f]
    // 0xa45e78: ldur            x1, [fp, #-0x10]
    // 0xa45e7c: ArrayLoad: r3 = r1[0]  ; List_8
    //     0xa45e7c: ldur            x3, [x1, #0x17]
    // 0xa45e80: StoreField: r2->field_7 = r3
    //     0xa45e80: stur            x3, [x2, #7]
    // 0xa45e84: LoadField: r3 = r1->field_7
    //     0xa45e84: ldur            x3, [x1, #7]
    // 0xa45e88: StoreField: r2->field_f = r3
    //     0xa45e88: stur            x3, [x2, #0xf]
    // 0xa45e8c: r3 = 1
    //     0xa45e8c: movz            x3, #0x1
    // 0xa45e90: ArrayStore: r2[0] = r3  ; List_8
    //     0xa45e90: stur            x3, [x2, #0x17]
    // 0xa45e94: r0 = dataCache()
    //     0xa45e94: bl              #0xa48248  ; [package:qr/src/qr_code.dart] QrCode::dataCache
    // 0xa45e98: ldur            x1, [fp, #-8]
    // 0xa45e9c: ldur            x2, [fp, #-0x18]
    // 0xa45ea0: mov             x3, x0
    // 0xa45ea4: r5 = false
    //     0xa45ea4: add             x5, NULL, #0x30  ; false
    // 0xa45ea8: r0 = _makeImpl()
    //     0xa45ea8: bl              #0xa46688  ; [package:qr/src/qr_image.dart] QrImage::_makeImpl
    // 0xa45eac: r0 = Null
    //     0xa45eac: mov             x0, NULL
    // 0xa45eb0: LeaveFrame
    //     0xa45eb0: mov             SP, fp
    //     0xa45eb4: ldp             fp, lr, [SP], #0x10
    // 0xa45eb8: ret
    //     0xa45eb8: ret             
    // 0xa45ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa45ebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45ec0: b               #0xa45e40
  }
  _ _makeImpl(/* No info */) {
    // ** addr: 0xa46688, size: 0xe4
    // 0xa46688: EnterFrame
    //     0xa46688: stp             fp, lr, [SP, #-0x10]!
    //     0xa4668c: mov             fp, SP
    // 0xa46690: AllocStack(0x28)
    //     0xa46690: sub             SP, SP, #0x28
    // 0xa46694: SetupParameters(QrImage this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r3, fp-0x20 */)
    //     0xa46694: mov             x4, x1
    //     0xa46698: mov             x0, x2
    //     0xa4669c: stur            x2, [fp, #-0x10]
    //     0xa466a0: mov             x2, x3
    //     0xa466a4: stur            x3, [fp, #-0x18]
    //     0xa466a8: mov             x3, x5
    //     0xa466ac: stur            x1, [fp, #-8]
    //     0xa466b0: stur            x5, [fp, #-0x20]
    // 0xa466b4: CheckStackOverflow
    //     0xa466b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa466b8: cmp             SP, x16
    //     0xa466bc: b.ls            #0xa46764
    // 0xa466c0: mov             x1, x4
    // 0xa466c4: r0 = _resetModules()
    //     0xa466c4: bl              #0xa480c8  ; [package:qr/src/qr_image.dart] QrImage::_resetModules
    // 0xa466c8: ldur            x1, [fp, #-8]
    // 0xa466cc: r2 = 0
    //     0xa466cc: movz            x2, #0
    // 0xa466d0: r3 = 0
    //     0xa466d0: movz            x3, #0
    // 0xa466d4: r0 = _setupPositionProbePattern()
    //     0xa466d4: bl              #0xa47e7c  ; [package:qr/src/qr_image.dart] QrImage::_setupPositionProbePattern
    // 0xa466d8: ldur            x0, [fp, #-8]
    // 0xa466dc: LoadField: r1 = r0->field_7
    //     0xa466dc: ldur            x1, [x0, #7]
    // 0xa466e0: sub             x4, x1, #7
    // 0xa466e4: mov             x1, x0
    // 0xa466e8: mov             x2, x4
    // 0xa466ec: stur            x4, [fp, #-0x28]
    // 0xa466f0: r3 = 0
    //     0xa466f0: movz            x3, #0
    // 0xa466f4: r0 = _setupPositionProbePattern()
    //     0xa466f4: bl              #0xa47e7c  ; [package:qr/src/qr_image.dart] QrImage::_setupPositionProbePattern
    // 0xa466f8: ldur            x1, [fp, #-8]
    // 0xa466fc: ldur            x3, [fp, #-0x28]
    // 0xa46700: r2 = 0
    //     0xa46700: movz            x2, #0
    // 0xa46704: r0 = _setupPositionProbePattern()
    //     0xa46704: bl              #0xa47e7c  ; [package:qr/src/qr_image.dart] QrImage::_setupPositionProbePattern
    // 0xa46708: ldur            x1, [fp, #-8]
    // 0xa4670c: r0 = _setupPositionAdjustPattern()
    //     0xa4670c: bl              #0xa47b00  ; [package:qr/src/qr_image.dart] QrImage::_setupPositionAdjustPattern
    // 0xa46710: ldur            x1, [fp, #-8]
    // 0xa46714: r0 = _setupTimingPattern()
    //     0xa46714: bl              #0xa47874  ; [package:qr/src/qr_image.dart] QrImage::_setupTimingPattern
    // 0xa46718: ldur            x1, [fp, #-8]
    // 0xa4671c: ldur            x2, [fp, #-0x10]
    // 0xa46720: ldur            x3, [fp, #-0x20]
    // 0xa46724: r0 = _setupTypeInfo()
    //     0xa46724: bl              #0xa47224  ; [package:qr/src/qr_image.dart] QrImage::_setupTypeInfo
    // 0xa46728: ldur            x0, [fp, #-8]
    // 0xa4672c: LoadField: r1 = r0->field_f
    //     0xa4672c: ldur            x1, [x0, #0xf]
    // 0xa46730: cmp             x1, #7
    // 0xa46734: b.lt            #0xa46744
    // 0xa46738: mov             x1, x0
    // 0xa4673c: ldur            x2, [fp, #-0x20]
    // 0xa46740: r0 = _setupTypeNumber()
    //     0xa46740: bl              #0xa46dd8  ; [package:qr/src/qr_image.dart] QrImage::_setupTypeNumber
    // 0xa46744: ldur            x1, [fp, #-8]
    // 0xa46748: ldur            x2, [fp, #-0x18]
    // 0xa4674c: ldur            x3, [fp, #-0x10]
    // 0xa46750: r0 = _mapData()
    //     0xa46750: bl              #0xa4676c  ; [package:qr/src/qr_image.dart] QrImage::_mapData
    // 0xa46754: r0 = Null
    //     0xa46754: mov             x0, NULL
    // 0xa46758: LeaveFrame
    //     0xa46758: mov             SP, fp
    //     0xa4675c: ldp             fp, lr, [SP], #0x10
    // 0xa46760: ret
    //     0xa46760: ret             
    // 0xa46764: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa46764: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa46768: b               #0xa466c0
  }
  _ _mapData(/* No info */) {
    // ** addr: 0xa4676c, size: 0x3b4
    // 0xa4676c: EnterFrame
    //     0xa4676c: stp             fp, lr, [SP, #-0x10]!
    //     0xa46770: mov             fp, SP
    // 0xa46774: AllocStack(0x80)
    //     0xa46774: sub             SP, SP, #0x80
    // 0xa46778: SetupParameters(dynamic _ /* r2 => r3, fp-0x58 */, dynamic _ /* r3 => r2, fp-0x60 */)
    //     0xa46778: stur            x2, [fp, #-0x58]
    //     0xa4677c: mov             x16, x3
    //     0xa46780: mov             x3, x2
    //     0xa46784: mov             x2, x16
    //     0xa46788: stur            x2, [fp, #-0x60]
    // 0xa4678c: CheckStackOverflow
    //     0xa4678c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46790: cmp             SP, x16
    //     0xa46794: b.ls            #0xa46ac4
    // 0xa46798: LoadField: r4 = r1->field_7
    //     0xa46798: ldur            x4, [x1, #7]
    // 0xa4679c: stur            x4, [fp, #-0x50]
    // 0xa467a0: sub             x0, x4, #1
    // 0xa467a4: LoadField: r5 = r1->field_27
    //     0xa467a4: ldur            w5, [x1, #0x27]
    // 0xa467a8: DecompressPointer r5
    //     0xa467a8: add             x5, x5, HEAP, lsl #32
    // 0xa467ac: stur            x5, [fp, #-0x48]
    // 0xa467b0: mov             x7, x0
    // 0xa467b4: r8 = -1
    //     0xa467b4: movn            x8, #0
    // 0xa467b8: r6 = 7
    //     0xa467b8: movz            x6, #0x7
    // 0xa467bc: r1 = 0
    //     0xa467bc: movz            x1, #0
    // 0xa467c0: stur            x8, [fp, #-0x40]
    // 0xa467c4: CheckStackOverflow
    //     0xa467c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa467c8: cmp             SP, x16
    //     0xa467cc: b.ls            #0xa46acc
    // 0xa467d0: cmp             x0, #0
    // 0xa467d4: b.le            #0xa46ab4
    // 0xa467d8: cmp             x0, #6
    // 0xa467dc: b.ne            #0xa467e8
    // 0xa467e0: r9 = 5
    //     0xa467e0: movz            x9, #0x5
    // 0xa467e4: b               #0xa467ec
    // 0xa467e8: mov             x9, x0
    // 0xa467ec: stur            x9, [fp, #-0x38]
    // 0xa467f0: mov             x0, x1
    // 0xa467f4: mov             x1, x6
    // 0xa467f8: mov             x6, x7
    // 0xa467fc: stur            x6, [fp, #-0x30]
    // 0xa46800: CheckStackOverflow
    //     0xa46800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46804: cmp             SP, x16
    //     0xa46808: b.ls            #0xa46ad4
    // 0xa4680c: mov             x11, x1
    // 0xa46810: mov             x10, x0
    // 0xa46814: r7 = 0
    //     0xa46814: movz            x7, #0
    // 0xa46818: stur            x11, [fp, #-0x18]
    // 0xa4681c: stur            x10, [fp, #-0x20]
    // 0xa46820: stur            x7, [fp, #-0x28]
    // 0xa46824: CheckStackOverflow
    //     0xa46824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46828: cmp             SP, x16
    //     0xa4682c: b.ls            #0xa46adc
    // 0xa46830: cmp             x7, #2
    // 0xa46834: b.ge            #0xa46a40
    // 0xa46838: LoadField: r0 = r5->field_b
    //     0xa46838: ldur            w0, [x5, #0xb]
    // 0xa4683c: r1 = LoadInt32Instr(r0)
    //     0xa4683c: sbfx            x1, x0, #1, #0x1f
    // 0xa46840: mov             x0, x1
    // 0xa46844: mov             x1, x6
    // 0xa46848: cmp             x1, x0
    // 0xa4684c: b.hs            #0xa46ae4
    // 0xa46850: LoadField: r0 = r5->field_f
    //     0xa46850: ldur            w0, [x5, #0xf]
    // 0xa46854: DecompressPointer r0
    //     0xa46854: add             x0, x0, HEAP, lsl #32
    // 0xa46858: ArrayLoad: r12 = r0[r6]  ; Unknown_4
    //     0xa46858: add             x16, x0, x6, lsl #2
    //     0xa4685c: ldur            w12, [x16, #0xf]
    // 0xa46860: DecompressPointer r12
    //     0xa46860: add             x12, x12, HEAP, lsl #32
    // 0xa46864: sub             x13, x9, x7
    // 0xa46868: stur            x13, [fp, #-0x10]
    // 0xa4686c: r0 = BoxInt64Instr(r13)
    //     0xa4686c: sbfiz           x0, x13, #1, #0x1f
    //     0xa46870: cmp             x13, x0, asr #1
    //     0xa46874: b.eq            #0xa46880
    //     0xa46878: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4687c: stur            x13, [x0, #7]
    // 0xa46880: mov             x1, x0
    // 0xa46884: stur            x1, [fp, #-8]
    // 0xa46888: r0 = LoadClassIdInstr(r12)
    //     0xa46888: ldur            x0, [x12, #-1]
    //     0xa4688c: ubfx            x0, x0, #0xc, #0x14
    // 0xa46890: stp             x1, x12, [SP]
    // 0xa46894: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa46894: movz            x17, #0x3037
    //     0xa46898: movk            x17, #0x1, lsl #16
    //     0xa4689c: add             lr, x0, x17
    //     0xa468a0: ldr             lr, [x21, lr, lsl #3]
    //     0xa468a4: blr             lr
    // 0xa468a8: cmp             w0, NULL
    // 0xa468ac: b.ne            #0xa46a08
    // 0xa468b0: ldur            x4, [fp, #-0x58]
    // 0xa468b4: ldur            x5, [fp, #-0x20]
    // 0xa468b8: LoadField: r0 = r4->field_b
    //     0xa468b8: ldur            w0, [x4, #0xb]
    // 0xa468bc: r1 = LoadInt32Instr(r0)
    //     0xa468bc: sbfx            x1, x0, #1, #0x1f
    // 0xa468c0: cmp             x5, x1
    // 0xa468c4: b.ge            #0xa46930
    // 0xa468c8: ldur            x7, [fp, #-0x18]
    // 0xa468cc: r6 = 1
    //     0xa468cc: movz            x6, #0x1
    // 0xa468d0: mov             x0, x1
    // 0xa468d4: mov             x1, x5
    // 0xa468d8: cmp             x1, x0
    // 0xa468dc: b.hs            #0xa46ae8
    // 0xa468e0: LoadField: r0 = r4->field_f
    //     0xa468e0: ldur            w0, [x4, #0xf]
    // 0xa468e4: DecompressPointer r0
    //     0xa468e4: add             x0, x0, HEAP, lsl #32
    // 0xa468e8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa468e8: add             x16, x0, x5, lsl #2
    //     0xa468ec: ldur            w1, [x16, #0xf]
    // 0xa468f0: DecompressPointer r1
    //     0xa468f0: add             x1, x1, HEAP, lsl #32
    // 0xa468f4: r0 = LoadInt32Instr(r1)
    //     0xa468f4: sbfx            x0, x1, #1, #0x1f
    //     0xa468f8: tbz             w1, #0, #0xa46900
    //     0xa468fc: ldur            x0, [x1, #7]
    // 0xa46900: cmp             x7, #0x3f
    // 0xa46904: b.hi            #0xa46aec
    // 0xa46908: asr             x1, x0, x7
    // 0xa4690c: ubfx            x1, x1, #0, #0x20
    // 0xa46910: and             x0, x1, x6
    // 0xa46914: ubfx            x0, x0, #0, #0x20
    // 0xa46918: cmp             x0, #1
    // 0xa4691c: r16 = true
    //     0xa4691c: add             x16, NULL, #0x20  ; true
    // 0xa46920: r17 = false
    //     0xa46920: add             x17, NULL, #0x30  ; false
    // 0xa46924: csel            x1, x16, x17, eq
    // 0xa46928: mov             x0, x1
    // 0xa4692c: b               #0xa4693c
    // 0xa46930: ldur            x7, [fp, #-0x18]
    // 0xa46934: r6 = 1
    //     0xa46934: movz            x6, #0x1
    // 0xa46938: r0 = false
    //     0xa46938: add             x0, NULL, #0x30  ; false
    // 0xa4693c: ldur            x1, [fp, #-0x60]
    // 0xa46940: ldur            x2, [fp, #-0x30]
    // 0xa46944: ldur            x3, [fp, #-0x10]
    // 0xa46948: stur            x0, [fp, #-0x68]
    // 0xa4694c: r0 = _mask()
    //     0xa4694c: bl              #0xa46b20  ; [package:qr/src/qr_image.dart] ::_mask
    // 0xa46950: tbnz            w0, #4, #0xa46964
    // 0xa46954: ldur            x0, [fp, #-0x68]
    // 0xa46958: eor             x1, x0, #0x10
    // 0xa4695c: mov             x5, x1
    // 0xa46960: b               #0xa4696c
    // 0xa46964: ldur            x0, [fp, #-0x68]
    // 0xa46968: mov             x5, x0
    // 0xa4696c: ldur            x4, [fp, #-0x30]
    // 0xa46970: ldur            x2, [fp, #-0x18]
    // 0xa46974: ldur            x3, [fp, #-0x48]
    // 0xa46978: LoadField: r0 = r3->field_b
    //     0xa46978: ldur            w0, [x3, #0xb]
    // 0xa4697c: r1 = LoadInt32Instr(r0)
    //     0xa4697c: sbfx            x1, x0, #1, #0x1f
    // 0xa46980: mov             x0, x1
    // 0xa46984: mov             x1, x4
    // 0xa46988: cmp             x1, x0
    // 0xa4698c: b.hs            #0xa46b1c
    // 0xa46990: LoadField: r0 = r3->field_f
    //     0xa46990: ldur            w0, [x3, #0xf]
    // 0xa46994: DecompressPointer r0
    //     0xa46994: add             x0, x0, HEAP, lsl #32
    // 0xa46998: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa46998: add             x16, x0, x4, lsl #2
    //     0xa4699c: ldur            w1, [x16, #0xf]
    // 0xa469a0: DecompressPointer r1
    //     0xa469a0: add             x1, x1, HEAP, lsl #32
    // 0xa469a4: r0 = LoadClassIdInstr(r1)
    //     0xa469a4: ldur            x0, [x1, #-1]
    //     0xa469a8: ubfx            x0, x0, #0xc, #0x14
    // 0xa469ac: ldur            x16, [fp, #-8]
    // 0xa469b0: stp             x16, x1, [SP, #8]
    // 0xa469b4: str             x5, [SP]
    // 0xa469b8: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa469b8: movz            x17, #0x310f
    //     0xa469bc: movk            x17, #0x1, lsl #16
    //     0xa469c0: add             lr, x0, x17
    //     0xa469c4: ldr             lr, [x21, lr, lsl #3]
    //     0xa469c8: blr             lr
    // 0xa469cc: ldur            x1, [fp, #-0x18]
    // 0xa469d0: sub             x2, x1, #1
    // 0xa469d4: cmn             x2, #1
    // 0xa469d8: b.ne            #0xa469f0
    // 0xa469dc: ldur            x3, [fp, #-0x20]
    // 0xa469e0: add             x4, x3, #1
    // 0xa469e4: mov             x2, x4
    // 0xa469e8: r4 = 7
    //     0xa469e8: movz            x4, #0x7
    // 0xa469ec: b               #0xa469fc
    // 0xa469f0: ldur            x3, [fp, #-0x20]
    // 0xa469f4: mov             x4, x2
    // 0xa469f8: mov             x2, x3
    // 0xa469fc: mov             x11, x4
    // 0xa46a00: mov             x10, x2
    // 0xa46a04: b               #0xa46a18
    // 0xa46a08: ldur            x1, [fp, #-0x18]
    // 0xa46a0c: ldur            x3, [fp, #-0x20]
    // 0xa46a10: mov             x11, x1
    // 0xa46a14: mov             x10, x3
    // 0xa46a18: ldur            x2, [fp, #-0x28]
    // 0xa46a1c: add             x7, x2, #1
    // 0xa46a20: ldur            x3, [fp, #-0x58]
    // 0xa46a24: ldur            x2, [fp, #-0x60]
    // 0xa46a28: ldur            x4, [fp, #-0x50]
    // 0xa46a2c: ldur            x8, [fp, #-0x40]
    // 0xa46a30: ldur            x9, [fp, #-0x38]
    // 0xa46a34: ldur            x6, [fp, #-0x30]
    // 0xa46a38: ldur            x5, [fp, #-0x48]
    // 0xa46a3c: b               #0xa46818
    // 0xa46a40: mov             x4, x8
    // 0xa46a44: mov             x2, x6
    // 0xa46a48: mov             x1, x11
    // 0xa46a4c: mov             x3, x10
    // 0xa46a50: add             x6, x2, x4
    // 0xa46a54: tbnz            x6, #0x3f, #0xa46a84
    // 0xa46a58: ldur            x2, [fp, #-0x50]
    // 0xa46a5c: cmp             x2, x6
    // 0xa46a60: b.le            #0xa46a88
    // 0xa46a64: mov             x0, x3
    // 0xa46a68: ldur            x3, [fp, #-0x58]
    // 0xa46a6c: mov             x8, x4
    // 0xa46a70: mov             x4, x2
    // 0xa46a74: ldur            x2, [fp, #-0x60]
    // 0xa46a78: ldur            x9, [fp, #-0x38]
    // 0xa46a7c: ldur            x5, [fp, #-0x48]
    // 0xa46a80: b               #0xa467fc
    // 0xa46a84: ldur            x2, [fp, #-0x50]
    // 0xa46a88: ldur            x5, [fp, #-0x38]
    // 0xa46a8c: sub             x7, x6, x4
    // 0xa46a90: neg             x8, x4
    // 0xa46a94: sub             x0, x5, #2
    // 0xa46a98: mov             x6, x1
    // 0xa46a9c: mov             x1, x3
    // 0xa46aa0: ldur            x3, [fp, #-0x58]
    // 0xa46aa4: mov             x4, x2
    // 0xa46aa8: ldur            x2, [fp, #-0x60]
    // 0xa46aac: ldur            x5, [fp, #-0x48]
    // 0xa46ab0: b               #0xa467c0
    // 0xa46ab4: r0 = Null
    //     0xa46ab4: mov             x0, NULL
    // 0xa46ab8: LeaveFrame
    //     0xa46ab8: mov             SP, fp
    //     0xa46abc: ldp             fp, lr, [SP], #0x10
    // 0xa46ac0: ret
    //     0xa46ac0: ret             
    // 0xa46ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa46ac4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa46ac8: b               #0xa46798
    // 0xa46acc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa46acc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa46ad0: b               #0xa467d0
    // 0xa46ad4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa46ad4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa46ad8: b               #0xa4680c
    // 0xa46adc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa46adc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa46ae0: b               #0xa46830
    // 0xa46ae4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa46ae4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa46ae8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa46ae8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa46aec: tbnz            x7, #0x3f, #0xa46af8
    // 0xa46af0: asr             x1, x0, #0x3f
    // 0xa46af4: b               #0xa4690c
    // 0xa46af8: str             x7, [THR, #0x7a8]  ; THR::
    // 0xa46afc: stp             x6, x7, [SP, #-0x10]!
    // 0xa46b00: stp             x4, x5, [SP, #-0x10]!
    // 0xa46b04: SaveReg r0
    //     0xa46b04: str             x0, [SP, #-8]!
    // 0xa46b08: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa46b0c: r4 = 0
    //     0xa46b0c: movz            x4, #0
    // 0xa46b10: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa46b14: blr             lr
    // 0xa46b18: brk             #0
    // 0xa46b1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa46b1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _setupTypeNumber(/* No info */) {
    // ** addr: 0xa46dd8, size: 0x2f4
    // 0xa46dd8: EnterFrame
    //     0xa46dd8: stp             fp, lr, [SP, #-0x10]!
    //     0xa46ddc: mov             fp, SP
    // 0xa46de0: AllocStack(0x48)
    //     0xa46de0: sub             SP, SP, #0x48
    // 0xa46de4: SetupParameters(QrImage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa46de4: mov             x0, x1
    //     0xa46de8: stur            x1, [fp, #-8]
    //     0xa46dec: stur            x2, [fp, #-0x10]
    // 0xa46df0: CheckStackOverflow
    //     0xa46df0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46df4: cmp             SP, x16
    //     0xa46df8: b.ls            #0xa47034
    // 0xa46dfc: LoadField: r1 = r0->field_f
    //     0xa46dfc: ldur            x1, [x0, #0xf]
    // 0xa46e00: r0 = bchTypeNumber()
    //     0xa46e00: bl              #0xa470cc  ; [package:qr/src/util.dart] ::bchTypeNumber
    // 0xa46e04: mov             x2, x0
    // 0xa46e08: ldur            x0, [fp, #-8]
    // 0xa46e0c: stur            x2, [fp, #-0x30]
    // 0xa46e10: LoadField: r3 = r0->field_27
    //     0xa46e10: ldur            w3, [x0, #0x27]
    // 0xa46e14: DecompressPointer r3
    //     0xa46e14: add             x3, x3, HEAP, lsl #32
    // 0xa46e18: stur            x3, [fp, #-0x28]
    // 0xa46e1c: LoadField: r4 = r0->field_7
    //     0xa46e1c: ldur            x4, [x0, #7]
    // 0xa46e20: stur            x4, [fp, #-0x20]
    // 0xa46e24: r8 = 0
    //     0xa46e24: movz            x8, #0
    // 0xa46e28: ldur            x5, [fp, #-0x10]
    // 0xa46e2c: r7 = 3
    //     0xa46e2c: movz            x7, #0x3
    // 0xa46e30: r6 = 1
    //     0xa46e30: movz            x6, #0x1
    // 0xa46e34: stur            x8, [fp, #-0x18]
    // 0xa46e38: CheckStackOverflow
    //     0xa46e38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46e3c: cmp             SP, x16
    //     0xa46e40: b.ls            #0xa4703c
    // 0xa46e44: cmp             x8, #0x12
    // 0xa46e48: b.ge            #0xa46f24
    // 0xa46e4c: tbz             w5, #4, #0xa46e80
    // 0xa46e50: cmp             x8, #0x3f
    // 0xa46e54: b.hi            #0xa47044
    // 0xa46e58: asr             x0, x2, x8
    // 0xa46e5c: ubfx            x0, x0, #0, #0x20
    // 0xa46e60: and             x1, x0, x6
    // 0xa46e64: ubfx            x1, x1, #0, #0x20
    // 0xa46e68: cmp             x1, #1
    // 0xa46e6c: r16 = true
    //     0xa46e6c: add             x16, NULL, #0x20  ; true
    // 0xa46e70: r17 = false
    //     0xa46e70: add             x17, NULL, #0x30  ; false
    // 0xa46e74: csel            x0, x16, x17, eq
    // 0xa46e78: mov             x9, x0
    // 0xa46e7c: b               #0xa46e84
    // 0xa46e80: r9 = false
    //     0xa46e80: add             x9, NULL, #0x30  ; false
    // 0xa46e84: sdiv            x10, x8, x7
    // 0xa46e88: LoadField: r0 = r3->field_b
    //     0xa46e88: ldur            w0, [x3, #0xb]
    // 0xa46e8c: r1 = LoadInt32Instr(r0)
    //     0xa46e8c: sbfx            x1, x0, #1, #0x1f
    // 0xa46e90: mov             x0, x1
    // 0xa46e94: mov             x1, x10
    // 0xa46e98: cmp             x1, x0
    // 0xa46e9c: b.hs            #0xa47078
    // 0xa46ea0: LoadField: r0 = r3->field_f
    //     0xa46ea0: ldur            w0, [x3, #0xf]
    // 0xa46ea4: DecompressPointer r0
    //     0xa46ea4: add             x0, x0, HEAP, lsl #32
    // 0xa46ea8: ArrayLoad: r11 = r0[r10]  ; Unknown_4
    //     0xa46ea8: add             x16, x0, x10, lsl #2
    //     0xa46eac: ldur            w11, [x16, #0xf]
    // 0xa46eb0: DecompressPointer r11
    //     0xa46eb0: add             x11, x11, HEAP, lsl #32
    // 0xa46eb4: sdiv            x1, x8, x7
    // 0xa46eb8: msub            x0, x1, x7, x8
    // 0xa46ebc: cmp             x0, xzr
    // 0xa46ec0: b.lt            #0xa4707c
    // 0xa46ec4: add             x1, x0, x4
    // 0xa46ec8: sub             x0, x1, #8
    // 0xa46ecc: sub             x10, x0, #3
    // 0xa46ed0: r0 = BoxInt64Instr(r10)
    //     0xa46ed0: sbfiz           x0, x10, #1, #0x1f
    //     0xa46ed4: cmp             x10, x0, asr #1
    //     0xa46ed8: b.eq            #0xa46ee4
    //     0xa46edc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa46ee0: stur            x10, [x0, #7]
    // 0xa46ee4: r1 = LoadClassIdInstr(r11)
    //     0xa46ee4: ldur            x1, [x11, #-1]
    //     0xa46ee8: ubfx            x1, x1, #0xc, #0x14
    // 0xa46eec: stp             x0, x11, [SP, #8]
    // 0xa46ef0: str             x9, [SP]
    // 0xa46ef4: mov             x0, x1
    // 0xa46ef8: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa46ef8: movz            x17, #0x310f
    //     0xa46efc: movk            x17, #0x1, lsl #16
    //     0xa46f00: add             lr, x0, x17
    //     0xa46f04: ldr             lr, [x21, lr, lsl #3]
    //     0xa46f08: blr             lr
    // 0xa46f0c: ldur            x0, [fp, #-0x18]
    // 0xa46f10: add             x8, x0, #1
    // 0xa46f14: ldur            x2, [fp, #-0x30]
    // 0xa46f18: ldur            x3, [fp, #-0x28]
    // 0xa46f1c: ldur            x4, [fp, #-0x20]
    // 0xa46f20: b               #0xa46e28
    // 0xa46f24: r8 = 0
    //     0xa46f24: movz            x8, #0
    // 0xa46f28: ldur            x5, [fp, #-0x10]
    // 0xa46f2c: ldur            x2, [fp, #-0x30]
    // 0xa46f30: ldur            x3, [fp, #-0x28]
    // 0xa46f34: ldur            x4, [fp, #-0x20]
    // 0xa46f38: r7 = 3
    //     0xa46f38: movz            x7, #0x3
    // 0xa46f3c: r6 = 1
    //     0xa46f3c: movz            x6, #0x1
    // 0xa46f40: stur            x8, [fp, #-0x18]
    // 0xa46f44: CheckStackOverflow
    //     0xa46f44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa46f48: cmp             SP, x16
    //     0xa46f4c: b.ls            #0xa47084
    // 0xa46f50: cmp             x8, #0x12
    // 0xa46f54: b.ge            #0xa47024
    // 0xa46f58: tbz             w5, #4, #0xa46f8c
    // 0xa46f5c: cmp             x8, #0x3f
    // 0xa46f60: b.hi            #0xa4708c
    // 0xa46f64: asr             x0, x2, x8
    // 0xa46f68: ubfx            x0, x0, #0, #0x20
    // 0xa46f6c: and             x1, x0, x6
    // 0xa46f70: ubfx            x1, x1, #0, #0x20
    // 0xa46f74: cmp             x1, #1
    // 0xa46f78: r16 = true
    //     0xa46f78: add             x16, NULL, #0x20  ; true
    // 0xa46f7c: r17 = false
    //     0xa46f7c: add             x17, NULL, #0x30  ; false
    // 0xa46f80: csel            x0, x16, x17, eq
    // 0xa46f84: mov             x9, x0
    // 0xa46f88: b               #0xa46f90
    // 0xa46f8c: r9 = false
    //     0xa46f8c: add             x9, NULL, #0x30  ; false
    // 0xa46f90: sdiv            x1, x8, x7
    // 0xa46f94: msub            x0, x1, x7, x8
    // 0xa46f98: cmp             x0, xzr
    // 0xa46f9c: b.lt            #0xa470c0
    // 0xa46fa0: add             x1, x0, x4
    // 0xa46fa4: sub             x0, x1, #8
    // 0xa46fa8: sub             x10, x0, #3
    // 0xa46fac: LoadField: r0 = r3->field_b
    //     0xa46fac: ldur            w0, [x3, #0xb]
    // 0xa46fb0: r1 = LoadInt32Instr(r0)
    //     0xa46fb0: sbfx            x1, x0, #1, #0x1f
    // 0xa46fb4: mov             x0, x1
    // 0xa46fb8: mov             x1, x10
    // 0xa46fbc: cmp             x1, x0
    // 0xa46fc0: b.hs            #0xa470c8
    // 0xa46fc4: LoadField: r0 = r3->field_f
    //     0xa46fc4: ldur            w0, [x3, #0xf]
    // 0xa46fc8: DecompressPointer r0
    //     0xa46fc8: add             x0, x0, HEAP, lsl #32
    // 0xa46fcc: ArrayLoad: r11 = r0[r10]  ; Unknown_4
    //     0xa46fcc: add             x16, x0, x10, lsl #2
    //     0xa46fd0: ldur            w11, [x16, #0xf]
    // 0xa46fd4: DecompressPointer r11
    //     0xa46fd4: add             x11, x11, HEAP, lsl #32
    // 0xa46fd8: sdiv            x10, x8, x7
    // 0xa46fdc: r0 = BoxInt64Instr(r10)
    //     0xa46fdc: sbfiz           x0, x10, #1, #0x1f
    //     0xa46fe0: cmp             x10, x0, asr #1
    //     0xa46fe4: b.eq            #0xa46ff0
    //     0xa46fe8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa46fec: stur            x10, [x0, #7]
    // 0xa46ff0: r1 = LoadClassIdInstr(r11)
    //     0xa46ff0: ldur            x1, [x11, #-1]
    //     0xa46ff4: ubfx            x1, x1, #0xc, #0x14
    // 0xa46ff8: stp             x0, x11, [SP, #8]
    // 0xa46ffc: str             x9, [SP]
    // 0xa47000: mov             x0, x1
    // 0xa47004: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa47004: movz            x17, #0x310f
    //     0xa47008: movk            x17, #0x1, lsl #16
    //     0xa4700c: add             lr, x0, x17
    //     0xa47010: ldr             lr, [x21, lr, lsl #3]
    //     0xa47014: blr             lr
    // 0xa47018: ldur            x1, [fp, #-0x18]
    // 0xa4701c: add             x8, x1, #1
    // 0xa47020: b               #0xa46f28
    // 0xa47024: r0 = Null
    //     0xa47024: mov             x0, NULL
    // 0xa47028: LeaveFrame
    //     0xa47028: mov             SP, fp
    //     0xa4702c: ldp             fp, lr, [SP], #0x10
    // 0xa47030: ret
    //     0xa47030: ret             
    // 0xa47034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47038: b               #0xa46dfc
    // 0xa4703c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4703c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47040: b               #0xa46e44
    // 0xa47044: tbnz            x8, #0x3f, #0xa47050
    // 0xa47048: asr             x0, x2, #0x3f
    // 0xa4704c: b               #0xa46e5c
    // 0xa47050: str             x8, [THR, #0x7a8]  ; THR::
    // 0xa47054: stp             x7, x8, [SP, #-0x10]!
    // 0xa47058: stp             x5, x6, [SP, #-0x10]!
    // 0xa4705c: stp             x3, x4, [SP, #-0x10]!
    // 0xa47060: SaveReg r2
    //     0xa47060: str             x2, [SP, #-8]!
    // 0xa47064: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa47068: r4 = 0
    //     0xa47068: movz            x4, #0
    // 0xa4706c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa47070: blr             lr
    // 0xa47074: brk             #0
    // 0xa47078: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47078: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa4707c: add             x0, x0, x7
    // 0xa47080: b               #0xa46ec4
    // 0xa47084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47084: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47088: b               #0xa46f50
    // 0xa4708c: tbnz            x8, #0x3f, #0xa47098
    // 0xa47090: asr             x0, x2, #0x3f
    // 0xa47094: b               #0xa46f68
    // 0xa47098: str             x8, [THR, #0x7a8]  ; THR::
    // 0xa4709c: stp             x7, x8, [SP, #-0x10]!
    // 0xa470a0: stp             x5, x6, [SP, #-0x10]!
    // 0xa470a4: stp             x3, x4, [SP, #-0x10]!
    // 0xa470a8: SaveReg r2
    //     0xa470a8: str             x2, [SP, #-8]!
    // 0xa470ac: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa470b0: r4 = 0
    //     0xa470b0: movz            x4, #0
    // 0xa470b4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa470b8: blr             lr
    // 0xa470bc: brk             #0
    // 0xa470c0: add             x0, x0, x7
    // 0xa470c4: b               #0xa46fa0
    // 0xa470c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa470c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _setupTypeInfo(/* No info */) {
    // ** addr: 0xa47224, size: 0x4f0
    // 0xa47224: EnterFrame
    //     0xa47224: stp             fp, lr, [SP, #-0x10]!
    //     0xa47228: mov             fp, SP
    // 0xa4722c: AllocStack(0x50)
    //     0xa4722c: sub             SP, SP, #0x50
    // 0xa47230: SetupParameters(QrImage this /* r1 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xa47230: mov             x0, x1
    //     0xa47234: stur            x1, [fp, #-8]
    //     0xa47238: stur            x3, [fp, #-0x10]
    // 0xa4723c: CheckStackOverflow
    //     0xa4723c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47240: cmp             SP, x16
    //     0xa47244: b.ls            #0xa4767c
    // 0xa47248: orr             x1, x2, #8
    // 0xa4724c: r0 = bchTypeInfo()
    //     0xa4724c: bl              #0xa47714  ; [package:qr/src/util.dart] ::bchTypeInfo
    // 0xa47250: mov             x2, x0
    // 0xa47254: ldur            x0, [fp, #-8]
    // 0xa47258: stur            x2, [fp, #-0x38]
    // 0xa4725c: LoadField: r3 = r0->field_27
    //     0xa4725c: ldur            w3, [x0, #0x27]
    // 0xa47260: DecompressPointer r3
    //     0xa47260: add             x3, x3, HEAP, lsl #32
    // 0xa47264: stur            x3, [fp, #-0x30]
    // 0xa47268: LoadField: r4 = r0->field_7
    //     0xa47268: ldur            x4, [x0, #7]
    // 0xa4726c: stur            x4, [fp, #-0x28]
    // 0xa47270: sub             x5, x4, #0xf
    // 0xa47274: stur            x5, [fp, #-0x20]
    // 0xa47278: r8 = 0
    //     0xa47278: movz            x8, #0
    // 0xa4727c: ldur            x6, [fp, #-0x10]
    // 0xa47280: r7 = 1
    //     0xa47280: movz            x7, #0x1
    // 0xa47284: stur            x8, [fp, #-0x18]
    // 0xa47288: CheckStackOverflow
    //     0xa47288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4728c: cmp             SP, x16
    //     0xa47290: b.ls            #0xa47684
    // 0xa47294: cmp             x8, #0xf
    // 0xa47298: b.ge            #0xa4741c
    // 0xa4729c: tbz             w6, #4, #0xa472d0
    // 0xa472a0: cmp             x8, #0x3f
    // 0xa472a4: b.hi            #0xa4768c
    // 0xa472a8: asr             x0, x2, x8
    // 0xa472ac: ubfx            x0, x0, #0, #0x20
    // 0xa472b0: and             x1, x0, x7
    // 0xa472b4: ubfx            x1, x1, #0, #0x20
    // 0xa472b8: cmp             x1, #1
    // 0xa472bc: r16 = true
    //     0xa472bc: add             x16, NULL, #0x20  ; true
    // 0xa472c0: r17 = false
    //     0xa472c0: add             x17, NULL, #0x30  ; false
    // 0xa472c4: csel            x0, x16, x17, eq
    // 0xa472c8: mov             x9, x0
    // 0xa472cc: b               #0xa472d4
    // 0xa472d0: r9 = false
    //     0xa472d0: add             x9, NULL, #0x30  ; false
    // 0xa472d4: cmp             x8, #6
    // 0xa472d8: b.ge            #0xa47334
    // 0xa472dc: LoadField: r0 = r3->field_b
    //     0xa472dc: ldur            w0, [x3, #0xb]
    // 0xa472e0: r1 = LoadInt32Instr(r0)
    //     0xa472e0: sbfx            x1, x0, #1, #0x1f
    // 0xa472e4: mov             x0, x1
    // 0xa472e8: mov             x1, x8
    // 0xa472ec: cmp             x1, x0
    // 0xa472f0: b.hs            #0xa476c0
    // 0xa472f4: LoadField: r0 = r3->field_f
    //     0xa472f4: ldur            w0, [x3, #0xf]
    // 0xa472f8: DecompressPointer r0
    //     0xa472f8: add             x0, x0, HEAP, lsl #32
    // 0xa472fc: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xa472fc: add             x16, x0, x8, lsl #2
    //     0xa47300: ldur            w1, [x16, #0xf]
    // 0xa47304: DecompressPointer r1
    //     0xa47304: add             x1, x1, HEAP, lsl #32
    // 0xa47308: r0 = LoadClassIdInstr(r1)
    //     0xa47308: ldur            x0, [x1, #-1]
    //     0xa4730c: ubfx            x0, x0, #0xc, #0x14
    // 0xa47310: r16 = 16
    //     0xa47310: movz            x16, #0x10
    // 0xa47314: stp             x16, x1, [SP, #8]
    // 0xa47318: str             x9, [SP]
    // 0xa4731c: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa4731c: movz            x17, #0x310f
    //     0xa47320: movk            x17, #0x1, lsl #16
    //     0xa47324: add             lr, x0, x17
    //     0xa47328: ldr             lr, [x21, lr, lsl #3]
    //     0xa4732c: blr             lr
    // 0xa47330: b               #0xa47400
    // 0xa47334: mov             x2, x8
    // 0xa47338: cmp             x2, #8
    // 0xa4733c: b.ge            #0xa473a0
    // 0xa47340: ldur            x3, [fp, #-0x30]
    // 0xa47344: add             x4, x2, #1
    // 0xa47348: LoadField: r0 = r3->field_b
    //     0xa47348: ldur            w0, [x3, #0xb]
    // 0xa4734c: r1 = LoadInt32Instr(r0)
    //     0xa4734c: sbfx            x1, x0, #1, #0x1f
    // 0xa47350: mov             x0, x1
    // 0xa47354: mov             x1, x4
    // 0xa47358: cmp             x1, x0
    // 0xa4735c: b.hs            #0xa476c4
    // 0xa47360: LoadField: r0 = r3->field_f
    //     0xa47360: ldur            w0, [x3, #0xf]
    // 0xa47364: DecompressPointer r0
    //     0xa47364: add             x0, x0, HEAP, lsl #32
    // 0xa47368: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa47368: add             x16, x0, x4, lsl #2
    //     0xa4736c: ldur            w1, [x16, #0xf]
    // 0xa47370: DecompressPointer r1
    //     0xa47370: add             x1, x1, HEAP, lsl #32
    // 0xa47374: r0 = LoadClassIdInstr(r1)
    //     0xa47374: ldur            x0, [x1, #-1]
    //     0xa47378: ubfx            x0, x0, #0xc, #0x14
    // 0xa4737c: r16 = 16
    //     0xa4737c: movz            x16, #0x10
    // 0xa47380: stp             x16, x1, [SP, #8]
    // 0xa47384: str             x9, [SP]
    // 0xa47388: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa47388: movz            x17, #0x310f
    //     0xa4738c: movk            x17, #0x1, lsl #16
    //     0xa47390: add             lr, x0, x17
    //     0xa47394: ldr             lr, [x21, lr, lsl #3]
    //     0xa47398: blr             lr
    // 0xa4739c: b               #0xa47400
    // 0xa473a0: ldur            x3, [fp, #-0x30]
    // 0xa473a4: ldur            x4, [fp, #-0x20]
    // 0xa473a8: add             x5, x4, x2
    // 0xa473ac: LoadField: r0 = r3->field_b
    //     0xa473ac: ldur            w0, [x3, #0xb]
    // 0xa473b0: r1 = LoadInt32Instr(r0)
    //     0xa473b0: sbfx            x1, x0, #1, #0x1f
    // 0xa473b4: mov             x0, x1
    // 0xa473b8: mov             x1, x5
    // 0xa473bc: cmp             x1, x0
    // 0xa473c0: b.hs            #0xa476c8
    // 0xa473c4: LoadField: r0 = r3->field_f
    //     0xa473c4: ldur            w0, [x3, #0xf]
    // 0xa473c8: DecompressPointer r0
    //     0xa473c8: add             x0, x0, HEAP, lsl #32
    // 0xa473cc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa473cc: add             x16, x0, x5, lsl #2
    //     0xa473d0: ldur            w1, [x16, #0xf]
    // 0xa473d4: DecompressPointer r1
    //     0xa473d4: add             x1, x1, HEAP, lsl #32
    // 0xa473d8: r0 = LoadClassIdInstr(r1)
    //     0xa473d8: ldur            x0, [x1, #-1]
    //     0xa473dc: ubfx            x0, x0, #0xc, #0x14
    // 0xa473e0: r16 = 16
    //     0xa473e0: movz            x16, #0x10
    // 0xa473e4: stp             x16, x1, [SP, #8]
    // 0xa473e8: str             x9, [SP]
    // 0xa473ec: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa473ec: movz            x17, #0x310f
    //     0xa473f0: movk            x17, #0x1, lsl #16
    //     0xa473f4: add             lr, x0, x17
    //     0xa473f8: ldr             lr, [x21, lr, lsl #3]
    //     0xa473fc: blr             lr
    // 0xa47400: ldur            x0, [fp, #-0x18]
    // 0xa47404: add             x8, x0, #1
    // 0xa47408: ldur            x2, [fp, #-0x38]
    // 0xa4740c: ldur            x3, [fp, #-0x30]
    // 0xa47410: ldur            x4, [fp, #-0x28]
    // 0xa47414: ldur            x5, [fp, #-0x20]
    // 0xa47418: b               #0xa4727c
    // 0xa4741c: r7 = 0
    //     0xa4741c: movz            x7, #0
    // 0xa47420: ldur            x5, [fp, #-0x10]
    // 0xa47424: ldur            x3, [fp, #-0x38]
    // 0xa47428: ldur            x2, [fp, #-0x30]
    // 0xa4742c: ldur            x4, [fp, #-0x28]
    // 0xa47430: r6 = 1
    //     0xa47430: movz            x6, #0x1
    // 0xa47434: stur            x7, [fp, #-0x18]
    // 0xa47438: CheckStackOverflow
    //     0xa47438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4743c: cmp             SP, x16
    //     0xa47440: b.ls            #0xa476cc
    // 0xa47444: cmp             x7, #0xf
    // 0xa47448: b.ge            #0xa47604
    // 0xa4744c: tbz             w5, #4, #0xa47480
    // 0xa47450: cmp             x7, #0x3f
    // 0xa47454: b.hi            #0xa476d4
    // 0xa47458: asr             x0, x3, x7
    // 0xa4745c: ubfx            x0, x0, #0, #0x20
    // 0xa47460: and             x1, x0, x6
    // 0xa47464: ubfx            x1, x1, #0, #0x20
    // 0xa47468: cmp             x1, #1
    // 0xa4746c: r16 = true
    //     0xa4746c: add             x16, NULL, #0x20  ; true
    // 0xa47470: r17 = false
    //     0xa47470: add             x17, NULL, #0x30  ; false
    // 0xa47474: csel            x0, x16, x17, eq
    // 0xa47478: mov             x8, x0
    // 0xa4747c: b               #0xa47484
    // 0xa47480: r8 = false
    //     0xa47480: add             x8, NULL, #0x30  ; false
    // 0xa47484: cmp             x7, #8
    // 0xa47488: b.ge            #0xa474fc
    // 0xa4748c: LoadField: r0 = r2->field_b
    //     0xa4748c: ldur            w0, [x2, #0xb]
    // 0xa47490: r1 = LoadInt32Instr(r0)
    //     0xa47490: sbfx            x1, x0, #1, #0x1f
    // 0xa47494: mov             x0, x1
    // 0xa47498: r1 = 8
    //     0xa47498: movz            x1, #0x8
    // 0xa4749c: cmp             x1, x0
    // 0xa474a0: b.hs            #0xa47704
    // 0xa474a4: LoadField: r0 = r2->field_f
    //     0xa474a4: ldur            w0, [x2, #0xf]
    // 0xa474a8: DecompressPointer r0
    //     0xa474a8: add             x0, x0, HEAP, lsl #32
    // 0xa474ac: LoadField: r9 = r0->field_2f
    //     0xa474ac: ldur            w9, [x0, #0x2f]
    // 0xa474b0: DecompressPointer r9
    //     0xa474b0: add             x9, x9, HEAP, lsl #32
    // 0xa474b4: sub             x0, x4, x7
    // 0xa474b8: sub             x10, x0, #1
    // 0xa474bc: r0 = BoxInt64Instr(r10)
    //     0xa474bc: sbfiz           x0, x10, #1, #0x1f
    //     0xa474c0: cmp             x10, x0, asr #1
    //     0xa474c4: b.eq            #0xa474d0
    //     0xa474c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa474cc: stur            x10, [x0, #7]
    // 0xa474d0: r1 = LoadClassIdInstr(r9)
    //     0xa474d0: ldur            x1, [x9, #-1]
    //     0xa474d4: ubfx            x1, x1, #0xc, #0x14
    // 0xa474d8: stp             x0, x9, [SP, #8]
    // 0xa474dc: str             x8, [SP]
    // 0xa474e0: mov             x0, x1
    // 0xa474e4: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa474e4: movz            x17, #0x310f
    //     0xa474e8: movk            x17, #0x1, lsl #16
    //     0xa474ec: add             lr, x0, x17
    //     0xa474f0: ldr             lr, [x21, lr, lsl #3]
    //     0xa474f4: blr             lr
    // 0xa474f8: b               #0xa475f8
    // 0xa474fc: mov             x2, x7
    // 0xa47500: cmp             x2, #9
    // 0xa47504: b.ge            #0xa47584
    // 0xa47508: ldur            x3, [fp, #-0x30]
    // 0xa4750c: r4 = 15
    //     0xa4750c: movz            x4, #0xf
    // 0xa47510: LoadField: r0 = r3->field_b
    //     0xa47510: ldur            w0, [x3, #0xb]
    // 0xa47514: r1 = LoadInt32Instr(r0)
    //     0xa47514: sbfx            x1, x0, #1, #0x1f
    // 0xa47518: mov             x0, x1
    // 0xa4751c: r1 = 8
    //     0xa4751c: movz            x1, #0x8
    // 0xa47520: cmp             x1, x0
    // 0xa47524: b.hs            #0xa47708
    // 0xa47528: LoadField: r0 = r3->field_f
    //     0xa47528: ldur            w0, [x3, #0xf]
    // 0xa4752c: DecompressPointer r0
    //     0xa4752c: add             x0, x0, HEAP, lsl #32
    // 0xa47530: LoadField: r5 = r0->field_2f
    //     0xa47530: ldur            w5, [x0, #0x2f]
    // 0xa47534: DecompressPointer r5
    //     0xa47534: add             x5, x5, HEAP, lsl #32
    // 0xa47538: sub             x0, x4, x2
    // 0xa4753c: sub             x1, x0, #1
    // 0xa47540: add             x6, x1, #1
    // 0xa47544: r0 = BoxInt64Instr(r6)
    //     0xa47544: sbfiz           x0, x6, #1, #0x1f
    //     0xa47548: cmp             x6, x0, asr #1
    //     0xa4754c: b.eq            #0xa47558
    //     0xa47550: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47554: stur            x6, [x0, #7]
    // 0xa47558: r1 = LoadClassIdInstr(r5)
    //     0xa47558: ldur            x1, [x5, #-1]
    //     0xa4755c: ubfx            x1, x1, #0xc, #0x14
    // 0xa47560: stp             x0, x5, [SP, #8]
    // 0xa47564: str             x8, [SP]
    // 0xa47568: mov             x0, x1
    // 0xa4756c: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa4756c: movz            x17, #0x310f
    //     0xa47570: movk            x17, #0x1, lsl #16
    //     0xa47574: add             lr, x0, x17
    //     0xa47578: ldr             lr, [x21, lr, lsl #3]
    //     0xa4757c: blr             lr
    // 0xa47580: b               #0xa475f8
    // 0xa47584: ldur            x3, [fp, #-0x30]
    // 0xa47588: r4 = 15
    //     0xa47588: movz            x4, #0xf
    // 0xa4758c: LoadField: r0 = r3->field_b
    //     0xa4758c: ldur            w0, [x3, #0xb]
    // 0xa47590: r1 = LoadInt32Instr(r0)
    //     0xa47590: sbfx            x1, x0, #1, #0x1f
    // 0xa47594: mov             x0, x1
    // 0xa47598: r1 = 8
    //     0xa47598: movz            x1, #0x8
    // 0xa4759c: cmp             x1, x0
    // 0xa475a0: b.hs            #0xa4770c
    // 0xa475a4: LoadField: r0 = r3->field_f
    //     0xa475a4: ldur            w0, [x3, #0xf]
    // 0xa475a8: DecompressPointer r0
    //     0xa475a8: add             x0, x0, HEAP, lsl #32
    // 0xa475ac: LoadField: r5 = r0->field_2f
    //     0xa475ac: ldur            w5, [x0, #0x2f]
    // 0xa475b0: DecompressPointer r5
    //     0xa475b0: add             x5, x5, HEAP, lsl #32
    // 0xa475b4: sub             x0, x4, x2
    // 0xa475b8: sub             x6, x0, #1
    // 0xa475bc: r0 = BoxInt64Instr(r6)
    //     0xa475bc: sbfiz           x0, x6, #1, #0x1f
    //     0xa475c0: cmp             x6, x0, asr #1
    //     0xa475c4: b.eq            #0xa475d0
    //     0xa475c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa475cc: stur            x6, [x0, #7]
    // 0xa475d0: r1 = LoadClassIdInstr(r5)
    //     0xa475d0: ldur            x1, [x5, #-1]
    //     0xa475d4: ubfx            x1, x1, #0xc, #0x14
    // 0xa475d8: stp             x0, x5, [SP, #8]
    // 0xa475dc: str             x8, [SP]
    // 0xa475e0: mov             x0, x1
    // 0xa475e4: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa475e4: movz            x17, #0x310f
    //     0xa475e8: movk            x17, #0x1, lsl #16
    //     0xa475ec: add             lr, x0, x17
    //     0xa475f0: ldr             lr, [x21, lr, lsl #3]
    //     0xa475f4: blr             lr
    // 0xa475f8: ldur            x0, [fp, #-0x18]
    // 0xa475fc: add             x7, x0, #1
    // 0xa47600: b               #0xa47420
    // 0xa47604: mov             x3, x5
    // 0xa47608: mov             x0, x4
    // 0xa4760c: sub             x4, x0, #8
    // 0xa47610: LoadField: r0 = r2->field_b
    //     0xa47610: ldur            w0, [x2, #0xb]
    // 0xa47614: r1 = LoadInt32Instr(r0)
    //     0xa47614: sbfx            x1, x0, #1, #0x1f
    // 0xa47618: mov             x0, x1
    // 0xa4761c: mov             x1, x4
    // 0xa47620: cmp             x1, x0
    // 0xa47624: b.hs            #0xa47710
    // 0xa47628: LoadField: r0 = r2->field_f
    //     0xa47628: ldur            w0, [x2, #0xf]
    // 0xa4762c: DecompressPointer r0
    //     0xa4762c: add             x0, x0, HEAP, lsl #32
    // 0xa47630: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa47630: add             x16, x0, x4, lsl #2
    //     0xa47634: ldur            w1, [x16, #0xf]
    // 0xa47638: DecompressPointer r1
    //     0xa47638: add             x1, x1, HEAP, lsl #32
    // 0xa4763c: eor             x0, x3, #0x10
    // 0xa47640: r2 = LoadClassIdInstr(r1)
    //     0xa47640: ldur            x2, [x1, #-1]
    //     0xa47644: ubfx            x2, x2, #0xc, #0x14
    // 0xa47648: r16 = 16
    //     0xa47648: movz            x16, #0x10
    // 0xa4764c: stp             x16, x1, [SP, #8]
    // 0xa47650: str             x0, [SP]
    // 0xa47654: mov             x0, x2
    // 0xa47658: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa47658: movz            x17, #0x310f
    //     0xa4765c: movk            x17, #0x1, lsl #16
    //     0xa47660: add             lr, x0, x17
    //     0xa47664: ldr             lr, [x21, lr, lsl #3]
    //     0xa47668: blr             lr
    // 0xa4766c: r0 = Null
    //     0xa4766c: mov             x0, NULL
    // 0xa47670: LeaveFrame
    //     0xa47670: mov             SP, fp
    //     0xa47674: ldp             fp, lr, [SP], #0x10
    // 0xa47678: ret
    //     0xa47678: ret             
    // 0xa4767c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4767c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47680: b               #0xa47248
    // 0xa47684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47688: b               #0xa47294
    // 0xa4768c: tbnz            x8, #0x3f, #0xa47698
    // 0xa47690: asr             x0, x2, #0x3f
    // 0xa47694: b               #0xa472ac
    // 0xa47698: str             x8, [THR, #0x7a8]  ; THR::
    // 0xa4769c: stp             x7, x8, [SP, #-0x10]!
    // 0xa476a0: stp             x5, x6, [SP, #-0x10]!
    // 0xa476a4: stp             x3, x4, [SP, #-0x10]!
    // 0xa476a8: SaveReg r2
    //     0xa476a8: str             x2, [SP, #-8]!
    // 0xa476ac: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa476b0: r4 = 0
    //     0xa476b0: movz            x4, #0
    // 0xa476b4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa476b8: blr             lr
    // 0xa476bc: brk             #0
    // 0xa476c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa476c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa476c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa476c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa476c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa476c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa476cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa476cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa476d0: b               #0xa47444
    // 0xa476d4: tbnz            x7, #0x3f, #0xa476e0
    // 0xa476d8: asr             x0, x3, #0x3f
    // 0xa476dc: b               #0xa4745c
    // 0xa476e0: str             x7, [THR, #0x7a8]  ; THR::
    // 0xa476e4: stp             x6, x7, [SP, #-0x10]!
    // 0xa476e8: stp             x4, x5, [SP, #-0x10]!
    // 0xa476ec: stp             x2, x3, [SP, #-0x10]!
    // 0xa476f0: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa476f4: r4 = 0
    //     0xa476f4: movz            x4, #0
    // 0xa476f8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa476fc: blr             lr
    // 0xa47700: brk             #0
    // 0xa47704: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47704: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa47708: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47708: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa4770c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4770c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa47710: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47710: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _setupTimingPattern(/* No info */) {
    // ** addr: 0xa47874, size: 0x28c
    // 0xa47874: EnterFrame
    //     0xa47874: stp             fp, lr, [SP, #-0x10]!
    //     0xa47878: mov             fp, SP
    // 0xa4787c: AllocStack(0x38)
    //     0xa4787c: sub             SP, SP, #0x38
    // 0xa47880: CheckStackOverflow
    //     0xa47880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47884: cmp             SP, x16
    //     0xa47888: b.ls            #0xa47ad8
    // 0xa4788c: LoadField: r0 = r1->field_7
    //     0xa4788c: ldur            x0, [x1, #7]
    // 0xa47890: sub             x2, x0, #8
    // 0xa47894: stur            x2, [fp, #-0x18]
    // 0xa47898: LoadField: r3 = r1->field_27
    //     0xa47898: ldur            w3, [x1, #0x27]
    // 0xa4789c: DecompressPointer r3
    //     0xa4789c: add             x3, x3, HEAP, lsl #32
    // 0xa478a0: stur            x3, [fp, #-0x10]
    // 0xa478a4: r4 = 8
    //     0xa478a4: movz            x4, #0x8
    // 0xa478a8: stur            x4, [fp, #-8]
    // 0xa478ac: CheckStackOverflow
    //     0xa478ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa478b0: cmp             SP, x16
    //     0xa478b4: b.ls            #0xa47ae0
    // 0xa478b8: cmp             x4, x2
    // 0xa478bc: b.ge            #0xa479b0
    // 0xa478c0: LoadField: r0 = r3->field_b
    //     0xa478c0: ldur            w0, [x3, #0xb]
    // 0xa478c4: r1 = LoadInt32Instr(r0)
    //     0xa478c4: sbfx            x1, x0, #1, #0x1f
    // 0xa478c8: mov             x0, x1
    // 0xa478cc: mov             x1, x4
    // 0xa478d0: cmp             x1, x0
    // 0xa478d4: b.hs            #0xa47ae8
    // 0xa478d8: LoadField: r0 = r3->field_f
    //     0xa478d8: ldur            w0, [x3, #0xf]
    // 0xa478dc: DecompressPointer r0
    //     0xa478dc: add             x0, x0, HEAP, lsl #32
    // 0xa478e0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa478e0: add             x16, x0, x4, lsl #2
    //     0xa478e4: ldur            w1, [x16, #0xf]
    // 0xa478e8: DecompressPointer r1
    //     0xa478e8: add             x1, x1, HEAP, lsl #32
    // 0xa478ec: r0 = LoadClassIdInstr(r1)
    //     0xa478ec: ldur            x0, [x1, #-1]
    //     0xa478f0: ubfx            x0, x0, #0xc, #0x14
    // 0xa478f4: r16 = 12
    //     0xa478f4: movz            x16, #0xc
    // 0xa478f8: stp             x16, x1, [SP]
    // 0xa478fc: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa478fc: movz            x17, #0x3037
    //     0xa47900: movk            x17, #0x1, lsl #16
    //     0xa47904: add             lr, x0, x17
    //     0xa47908: ldr             lr, [x21, lr, lsl #3]
    //     0xa4790c: blr             lr
    // 0xa47910: cmp             w0, NULL
    // 0xa47914: b.ne            #0xa4799c
    // 0xa47918: ldur            x4, [fp, #-8]
    // 0xa4791c: ldur            x2, [fp, #-0x10]
    // 0xa47920: r3 = 1
    //     0xa47920: movz            x3, #0x1
    // 0xa47924: LoadField: r0 = r2->field_b
    //     0xa47924: ldur            w0, [x2, #0xb]
    // 0xa47928: r1 = LoadInt32Instr(r0)
    //     0xa47928: sbfx            x1, x0, #1, #0x1f
    // 0xa4792c: mov             x0, x1
    // 0xa47930: mov             x1, x4
    // 0xa47934: cmp             x1, x0
    // 0xa47938: b.hs            #0xa47aec
    // 0xa4793c: LoadField: r0 = r2->field_f
    //     0xa4793c: ldur            w0, [x2, #0xf]
    // 0xa47940: DecompressPointer r0
    //     0xa47940: add             x0, x0, HEAP, lsl #32
    // 0xa47944: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa47944: add             x16, x0, x4, lsl #2
    //     0xa47948: ldur            w1, [x16, #0xf]
    // 0xa4794c: DecompressPointer r1
    //     0xa4794c: add             x1, x1, HEAP, lsl #32
    // 0xa47950: mov             x0, x4
    // 0xa47954: ubfx            x0, x0, #0, #0x20
    // 0xa47958: and             x5, x0, x3
    // 0xa4795c: ubfx            x5, x5, #0, #0x20
    // 0xa47960: cbz             x5, #0xa4796c
    // 0xa47964: r0 = false
    //     0xa47964: add             x0, NULL, #0x30  ; false
    // 0xa47968: b               #0xa47970
    // 0xa4796c: r0 = true
    //     0xa4796c: add             x0, NULL, #0x20  ; true
    // 0xa47970: r5 = LoadClassIdInstr(r1)
    //     0xa47970: ldur            x5, [x1, #-1]
    //     0xa47974: ubfx            x5, x5, #0xc, #0x14
    // 0xa47978: r16 = 12
    //     0xa47978: movz            x16, #0xc
    // 0xa4797c: stp             x16, x1, [SP, #8]
    // 0xa47980: str             x0, [SP]
    // 0xa47984: mov             x0, x5
    // 0xa47988: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa47988: movz            x17, #0x310f
    //     0xa4798c: movk            x17, #0x1, lsl #16
    //     0xa47990: add             lr, x0, x17
    //     0xa47994: ldr             lr, [x21, lr, lsl #3]
    //     0xa47998: blr             lr
    // 0xa4799c: ldur            x0, [fp, #-8]
    // 0xa479a0: add             x4, x0, #1
    // 0xa479a4: ldur            x2, [fp, #-0x18]
    // 0xa479a8: ldur            x3, [fp, #-0x10]
    // 0xa479ac: b               #0xa478a8
    // 0xa479b0: r4 = 8
    //     0xa479b0: movz            x4, #0x8
    // 0xa479b4: ldur            x3, [fp, #-0x18]
    // 0xa479b8: ldur            x2, [fp, #-0x10]
    // 0xa479bc: stur            x4, [fp, #-8]
    // 0xa479c0: CheckStackOverflow
    //     0xa479c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa479c4: cmp             SP, x16
    //     0xa479c8: b.ls            #0xa47af0
    // 0xa479cc: cmp             x4, x3
    // 0xa479d0: b.ge            #0xa47ac8
    // 0xa479d4: LoadField: r0 = r2->field_b
    //     0xa479d4: ldur            w0, [x2, #0xb]
    // 0xa479d8: r1 = LoadInt32Instr(r0)
    //     0xa479d8: sbfx            x1, x0, #1, #0x1f
    // 0xa479dc: mov             x0, x1
    // 0xa479e0: r1 = 6
    //     0xa479e0: movz            x1, #0x6
    // 0xa479e4: cmp             x1, x0
    // 0xa479e8: b.hs            #0xa47af8
    // 0xa479ec: LoadField: r0 = r2->field_f
    //     0xa479ec: ldur            w0, [x2, #0xf]
    // 0xa479f0: DecompressPointer r0
    //     0xa479f0: add             x0, x0, HEAP, lsl #32
    // 0xa479f4: LoadField: r5 = r0->field_27
    //     0xa479f4: ldur            w5, [x0, #0x27]
    // 0xa479f8: DecompressPointer r5
    //     0xa479f8: add             x5, x5, HEAP, lsl #32
    // 0xa479fc: r0 = BoxInt64Instr(r4)
    //     0xa479fc: sbfiz           x0, x4, #1, #0x1f
    //     0xa47a00: cmp             x4, x0, asr #1
    //     0xa47a04: b.eq            #0xa47a10
    //     0xa47a08: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47a0c: stur            x4, [x0, #7]
    // 0xa47a10: mov             x1, x0
    // 0xa47a14: stur            x1, [fp, #-0x20]
    // 0xa47a18: r0 = LoadClassIdInstr(r5)
    //     0xa47a18: ldur            x0, [x5, #-1]
    //     0xa47a1c: ubfx            x0, x0, #0xc, #0x14
    // 0xa47a20: stp             x1, x5, [SP]
    // 0xa47a24: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa47a24: movz            x17, #0x3037
    //     0xa47a28: movk            x17, #0x1, lsl #16
    //     0xa47a2c: add             lr, x0, x17
    //     0xa47a30: ldr             lr, [x21, lr, lsl #3]
    //     0xa47a34: blr             lr
    // 0xa47a38: cmp             w0, NULL
    // 0xa47a3c: b.ne            #0xa47abc
    // 0xa47a40: ldur            x2, [fp, #-0x10]
    // 0xa47a44: r3 = 1
    //     0xa47a44: movz            x3, #0x1
    // 0xa47a48: LoadField: r0 = r2->field_b
    //     0xa47a48: ldur            w0, [x2, #0xb]
    // 0xa47a4c: r1 = LoadInt32Instr(r0)
    //     0xa47a4c: sbfx            x1, x0, #1, #0x1f
    // 0xa47a50: mov             x0, x1
    // 0xa47a54: r1 = 6
    //     0xa47a54: movz            x1, #0x6
    // 0xa47a58: cmp             x1, x0
    // 0xa47a5c: b.hs            #0xa47afc
    // 0xa47a60: LoadField: r0 = r2->field_f
    //     0xa47a60: ldur            w0, [x2, #0xf]
    // 0xa47a64: DecompressPointer r0
    //     0xa47a64: add             x0, x0, HEAP, lsl #32
    // 0xa47a68: LoadField: r1 = r0->field_27
    //     0xa47a68: ldur            w1, [x0, #0x27]
    // 0xa47a6c: DecompressPointer r1
    //     0xa47a6c: add             x1, x1, HEAP, lsl #32
    // 0xa47a70: ldur            x0, [fp, #-8]
    // 0xa47a74: ubfx            x0, x0, #0, #0x20
    // 0xa47a78: and             x4, x0, x3
    // 0xa47a7c: ubfx            x4, x4, #0, #0x20
    // 0xa47a80: cbz             x4, #0xa47a8c
    // 0xa47a84: r0 = false
    //     0xa47a84: add             x0, NULL, #0x30  ; false
    // 0xa47a88: b               #0xa47a90
    // 0xa47a8c: r0 = true
    //     0xa47a8c: add             x0, NULL, #0x20  ; true
    // 0xa47a90: r4 = LoadClassIdInstr(r1)
    //     0xa47a90: ldur            x4, [x1, #-1]
    //     0xa47a94: ubfx            x4, x4, #0xc, #0x14
    // 0xa47a98: ldur            x16, [fp, #-0x20]
    // 0xa47a9c: stp             x16, x1, [SP, #8]
    // 0xa47aa0: str             x0, [SP]
    // 0xa47aa4: mov             x0, x4
    // 0xa47aa8: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa47aa8: movz            x17, #0x310f
    //     0xa47aac: movk            x17, #0x1, lsl #16
    //     0xa47ab0: add             lr, x0, x17
    //     0xa47ab4: ldr             lr, [x21, lr, lsl #3]
    //     0xa47ab8: blr             lr
    // 0xa47abc: ldur            x1, [fp, #-8]
    // 0xa47ac0: add             x4, x1, #1
    // 0xa47ac4: b               #0xa479b4
    // 0xa47ac8: r0 = Null
    //     0xa47ac8: mov             x0, NULL
    // 0xa47acc: LeaveFrame
    //     0xa47acc: mov             SP, fp
    //     0xa47ad0: ldp             fp, lr, [SP], #0x10
    // 0xa47ad4: ret
    //     0xa47ad4: ret             
    // 0xa47ad8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47ad8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47adc: b               #0xa4788c
    // 0xa47ae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47ae0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47ae4: b               #0xa478b8
    // 0xa47ae8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47ae8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa47aec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47aec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa47af0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47af0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47af4: b               #0xa479cc
    // 0xa47af8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47af8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa47afc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47afc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _setupPositionAdjustPattern(/* No info */) {
    // ** addr: 0xa47b00, size: 0x344
    // 0xa47b00: EnterFrame
    //     0xa47b00: stp             fp, lr, [SP, #-0x10]!
    //     0xa47b04: mov             fp, SP
    // 0xa47b08: AllocStack(0x80)
    //     0xa47b08: sub             SP, SP, #0x80
    // 0xa47b0c: SetupParameters(QrImage this /* r1 => r0, fp-0x8 */)
    //     0xa47b0c: mov             x0, x1
    //     0xa47b10: stur            x1, [fp, #-8]
    // 0xa47b14: CheckStackOverflow
    //     0xa47b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47b18: cmp             SP, x16
    //     0xa47b1c: b.ls            #0xa47e10
    // 0xa47b20: LoadField: r1 = r0->field_f
    //     0xa47b20: ldur            x1, [x0, #0xf]
    // 0xa47b24: r0 = patternPosition()
    //     0xa47b24: bl              #0xa47e44  ; [package:qr/src/util.dart] ::patternPosition
    // 0xa47b28: mov             x2, x0
    // 0xa47b2c: stur            x2, [fp, #-0x38]
    // 0xa47b30: LoadField: r0 = r2->field_b
    //     0xa47b30: ldur            w0, [x2, #0xb]
    // 0xa47b34: r3 = LoadInt32Instr(r0)
    //     0xa47b34: sbfx            x3, x0, #1, #0x1f
    // 0xa47b38: ldur            x0, [fp, #-8]
    // 0xa47b3c: stur            x3, [fp, #-0x30]
    // 0xa47b40: LoadField: r4 = r0->field_27
    //     0xa47b40: ldur            w4, [x0, #0x27]
    // 0xa47b44: DecompressPointer r4
    //     0xa47b44: add             x4, x4, HEAP, lsl #32
    // 0xa47b48: stur            x4, [fp, #-0x28]
    // 0xa47b4c: r5 = 0
    //     0xa47b4c: movz            x5, #0
    // 0xa47b50: stur            x5, [fp, #-0x20]
    // 0xa47b54: CheckStackOverflow
    //     0xa47b54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47b58: cmp             SP, x16
    //     0xa47b5c: b.ls            #0xa47e18
    // 0xa47b60: cmp             x5, x3
    // 0xa47b64: b.ge            #0xa47e00
    // 0xa47b68: r6 = 0
    //     0xa47b68: movz            x6, #0
    // 0xa47b6c: stur            x6, [fp, #-0x18]
    // 0xa47b70: CheckStackOverflow
    //     0xa47b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47b74: cmp             SP, x16
    //     0xa47b78: b.ls            #0xa47e20
    // 0xa47b7c: cmp             x6, x3
    // 0xa47b80: b.ge            #0xa47de8
    // 0xa47b84: ArrayLoad: r0 = r2[r5]  ; Unknown_4
    //     0xa47b84: add             x16, x2, x5, lsl #2
    //     0xa47b88: ldur            w0, [x16, #0xf]
    // 0xa47b8c: DecompressPointer r0
    //     0xa47b8c: add             x0, x0, HEAP, lsl #32
    // 0xa47b90: ArrayLoad: r7 = r2[r6]  ; Unknown_4
    //     0xa47b90: add             x16, x2, x6, lsl #2
    //     0xa47b94: ldur            w7, [x16, #0xf]
    // 0xa47b98: DecompressPointer r7
    //     0xa47b98: add             x7, x7, HEAP, lsl #32
    // 0xa47b9c: stur            x7, [fp, #-8]
    // 0xa47ba0: LoadField: r1 = r4->field_b
    //     0xa47ba0: ldur            w1, [x4, #0xb]
    // 0xa47ba4: r8 = LoadInt32Instr(r0)
    //     0xa47ba4: sbfx            x8, x0, #1, #0x1f
    //     0xa47ba8: tbz             w0, #0, #0xa47bb0
    //     0xa47bac: ldur            x8, [x0, #7]
    // 0xa47bb0: stur            x8, [fp, #-0x10]
    // 0xa47bb4: r0 = LoadInt32Instr(r1)
    //     0xa47bb4: sbfx            x0, x1, #1, #0x1f
    // 0xa47bb8: mov             x1, x8
    // 0xa47bbc: cmp             x1, x0
    // 0xa47bc0: b.hs            #0xa47e28
    // 0xa47bc4: LoadField: r0 = r4->field_f
    //     0xa47bc4: ldur            w0, [x4, #0xf]
    // 0xa47bc8: DecompressPointer r0
    //     0xa47bc8: add             x0, x0, HEAP, lsl #32
    // 0xa47bcc: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xa47bcc: add             x16, x0, x8, lsl #2
    //     0xa47bd0: ldur            w1, [x16, #0xf]
    // 0xa47bd4: DecompressPointer r1
    //     0xa47bd4: add             x1, x1, HEAP, lsl #32
    // 0xa47bd8: r0 = LoadClassIdInstr(r1)
    //     0xa47bd8: ldur            x0, [x1, #-1]
    //     0xa47bdc: ubfx            x0, x0, #0xc, #0x14
    // 0xa47be0: stp             x7, x1, [SP]
    // 0xa47be4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa47be4: movz            x17, #0x3037
    //     0xa47be8: movk            x17, #0x1, lsl #16
    //     0xa47bec: add             lr, x0, x17
    //     0xa47bf0: ldr             lr, [x21, lr, lsl #3]
    //     0xa47bf4: blr             lr
    // 0xa47bf8: cmp             w0, NULL
    // 0xa47bfc: b.ne            #0xa47dcc
    // 0xa47c00: ldur            x0, [fp, #-8]
    // 0xa47c04: r2 = LoadInt32Instr(r0)
    //     0xa47c04: sbfx            x2, x0, #1, #0x1f
    //     0xa47c08: tbz             w0, #0, #0xa47c10
    //     0xa47c0c: ldur            x2, [x0, #7]
    // 0xa47c10: stur            x2, [fp, #-0x68]
    // 0xa47c14: r3 = LoadInt32Instr(r0)
    //     0xa47c14: sbfx            x3, x0, #1, #0x1f
    //     0xa47c18: tbz             w0, #0, #0xa47c20
    //     0xa47c1c: ldur            x3, [x0, #7]
    // 0xa47c20: stur            x3, [fp, #-0x60]
    // 0xa47c24: r6 = -2
    //     0xa47c24: orr             x6, xzr, #0xfffffffffffffffe
    // 0xa47c28: ldur            x4, [fp, #-0x28]
    // 0xa47c2c: ldur            x5, [fp, #-0x10]
    // 0xa47c30: stur            x6, [fp, #-0x58]
    // 0xa47c34: CheckStackOverflow
    //     0xa47c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47c38: cmp             SP, x16
    //     0xa47c3c: b.ls            #0xa47e2c
    // 0xa47c40: cmp             x6, #2
    // 0xa47c44: b.gt            #0xa47dcc
    // 0xa47c48: add             x7, x5, x6
    // 0xa47c4c: stur            x7, [fp, #-0x50]
    // 0xa47c50: add             x8, x5, x6
    // 0xa47c54: stur            x8, [fp, #-0x48]
    // 0xa47c58: r9 = -2
    //     0xa47c58: orr             x9, xzr, #0xfffffffffffffffe
    // 0xa47c5c: stur            x9, [fp, #-0x40]
    // 0xa47c60: CheckStackOverflow
    //     0xa47c60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47c64: cmp             SP, x16
    //     0xa47c68: b.ls            #0xa47e34
    // 0xa47c6c: cmp             x9, #2
    // 0xa47c70: b.gt            #0xa47db8
    // 0xa47c74: cmn             x6, #2
    // 0xa47c78: b.eq            #0xa47c9c
    // 0xa47c7c: cmp             x6, #2
    // 0xa47c80: b.eq            #0xa47c9c
    // 0xa47c84: cmn             x9, #2
    // 0xa47c88: b.eq            #0xa47c9c
    // 0xa47c8c: cmp             x9, #2
    // 0xa47c90: b.eq            #0xa47c9c
    // 0xa47c94: cbnz            x6, #0xa47d10
    // 0xa47c98: cbnz            x9, #0xa47d10
    // 0xa47c9c: LoadField: r0 = r4->field_b
    //     0xa47c9c: ldur            w0, [x4, #0xb]
    // 0xa47ca0: r1 = LoadInt32Instr(r0)
    //     0xa47ca0: sbfx            x1, x0, #1, #0x1f
    // 0xa47ca4: mov             x0, x1
    // 0xa47ca8: mov             x1, x8
    // 0xa47cac: cmp             x1, x0
    // 0xa47cb0: b.hs            #0xa47e3c
    // 0xa47cb4: LoadField: r0 = r4->field_f
    //     0xa47cb4: ldur            w0, [x4, #0xf]
    // 0xa47cb8: DecompressPointer r0
    //     0xa47cb8: add             x0, x0, HEAP, lsl #32
    // 0xa47cbc: ArrayLoad: r10 = r0[r8]  ; Unknown_4
    //     0xa47cbc: add             x16, x0, x8, lsl #2
    //     0xa47cc0: ldur            w10, [x16, #0xf]
    // 0xa47cc4: DecompressPointer r10
    //     0xa47cc4: add             x10, x10, HEAP, lsl #32
    // 0xa47cc8: add             x11, x3, x9
    // 0xa47ccc: r0 = BoxInt64Instr(r11)
    //     0xa47ccc: sbfiz           x0, x11, #1, #0x1f
    //     0xa47cd0: cmp             x11, x0, asr #1
    //     0xa47cd4: b.eq            #0xa47ce0
    //     0xa47cd8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47cdc: stur            x11, [x0, #7]
    // 0xa47ce0: r1 = LoadClassIdInstr(r10)
    //     0xa47ce0: ldur            x1, [x10, #-1]
    //     0xa47ce4: ubfx            x1, x1, #0xc, #0x14
    // 0xa47ce8: stp             x0, x10, [SP, #8]
    // 0xa47cec: r16 = true
    //     0xa47cec: add             x16, NULL, #0x20  ; true
    // 0xa47cf0: str             x16, [SP]
    // 0xa47cf4: mov             x0, x1
    // 0xa47cf8: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa47cf8: movz            x17, #0x310f
    //     0xa47cfc: movk            x17, #0x1, lsl #16
    //     0xa47d00: add             lr, x0, x17
    //     0xa47d04: ldr             lr, [x21, lr, lsl #3]
    //     0xa47d08: blr             lr
    // 0xa47d0c: b               #0xa47d90
    // 0xa47d10: ldur            x3, [fp, #-0x28]
    // 0xa47d14: ldur            x5, [fp, #-0x40]
    // 0xa47d18: ldur            x4, [fp, #-0x50]
    // 0xa47d1c: ldur            x2, [fp, #-0x68]
    // 0xa47d20: LoadField: r0 = r3->field_b
    //     0xa47d20: ldur            w0, [x3, #0xb]
    // 0xa47d24: r1 = LoadInt32Instr(r0)
    //     0xa47d24: sbfx            x1, x0, #1, #0x1f
    // 0xa47d28: mov             x0, x1
    // 0xa47d2c: mov             x1, x4
    // 0xa47d30: cmp             x1, x0
    // 0xa47d34: b.hs            #0xa47e40
    // 0xa47d38: LoadField: r0 = r3->field_f
    //     0xa47d38: ldur            w0, [x3, #0xf]
    // 0xa47d3c: DecompressPointer r0
    //     0xa47d3c: add             x0, x0, HEAP, lsl #32
    // 0xa47d40: ArrayLoad: r6 = r0[r4]  ; Unknown_4
    //     0xa47d40: add             x16, x0, x4, lsl #2
    //     0xa47d44: ldur            w6, [x16, #0xf]
    // 0xa47d48: DecompressPointer r6
    //     0xa47d48: add             x6, x6, HEAP, lsl #32
    // 0xa47d4c: add             x7, x2, x5
    // 0xa47d50: r0 = BoxInt64Instr(r7)
    //     0xa47d50: sbfiz           x0, x7, #1, #0x1f
    //     0xa47d54: cmp             x7, x0, asr #1
    //     0xa47d58: b.eq            #0xa47d64
    //     0xa47d5c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47d60: stur            x7, [x0, #7]
    // 0xa47d64: r1 = LoadClassIdInstr(r6)
    //     0xa47d64: ldur            x1, [x6, #-1]
    //     0xa47d68: ubfx            x1, x1, #0xc, #0x14
    // 0xa47d6c: stp             x0, x6, [SP, #8]
    // 0xa47d70: r16 = false
    //     0xa47d70: add             x16, NULL, #0x30  ; false
    // 0xa47d74: str             x16, [SP]
    // 0xa47d78: mov             x0, x1
    // 0xa47d7c: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa47d7c: movz            x17, #0x310f
    //     0xa47d80: movk            x17, #0x1, lsl #16
    //     0xa47d84: add             lr, x0, x17
    //     0xa47d88: ldr             lr, [x21, lr, lsl #3]
    //     0xa47d8c: blr             lr
    // 0xa47d90: ldur            x1, [fp, #-0x40]
    // 0xa47d94: add             x9, x1, #1
    // 0xa47d98: ldur            x4, [fp, #-0x28]
    // 0xa47d9c: ldur            x6, [fp, #-0x58]
    // 0xa47da0: ldur            x7, [fp, #-0x50]
    // 0xa47da4: ldur            x8, [fp, #-0x48]
    // 0xa47da8: ldur            x5, [fp, #-0x10]
    // 0xa47dac: ldur            x2, [fp, #-0x68]
    // 0xa47db0: ldur            x3, [fp, #-0x60]
    // 0xa47db4: b               #0xa47c5c
    // 0xa47db8: mov             x1, x6
    // 0xa47dbc: add             x6, x1, #1
    // 0xa47dc0: ldur            x2, [fp, #-0x68]
    // 0xa47dc4: ldur            x3, [fp, #-0x60]
    // 0xa47dc8: b               #0xa47c28
    // 0xa47dcc: ldur            x1, [fp, #-0x18]
    // 0xa47dd0: add             x6, x1, #1
    // 0xa47dd4: ldur            x2, [fp, #-0x38]
    // 0xa47dd8: ldur            x5, [fp, #-0x20]
    // 0xa47ddc: ldur            x4, [fp, #-0x28]
    // 0xa47de0: ldur            x3, [fp, #-0x30]
    // 0xa47de4: b               #0xa47b6c
    // 0xa47de8: mov             x1, x5
    // 0xa47dec: add             x5, x1, #1
    // 0xa47df0: ldur            x2, [fp, #-0x38]
    // 0xa47df4: ldur            x4, [fp, #-0x28]
    // 0xa47df8: ldur            x3, [fp, #-0x30]
    // 0xa47dfc: b               #0xa47b50
    // 0xa47e00: r0 = Null
    //     0xa47e00: mov             x0, NULL
    // 0xa47e04: LeaveFrame
    //     0xa47e04: mov             SP, fp
    //     0xa47e08: ldp             fp, lr, [SP], #0x10
    // 0xa47e0c: ret
    //     0xa47e0c: ret             
    // 0xa47e10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47e10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47e14: b               #0xa47b20
    // 0xa47e18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47e18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47e1c: b               #0xa47b60
    // 0xa47e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47e24: b               #0xa47b7c
    // 0xa47e28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47e28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa47e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47e2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47e30: b               #0xa47c40
    // 0xa47e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47e34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47e38: b               #0xa47c6c
    // 0xa47e3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47e3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa47e40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47e40: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _setupPositionProbePattern(/* No info */) {
    // ** addr: 0xa47e7c, size: 0x24c
    // 0xa47e7c: EnterFrame
    //     0xa47e7c: stp             fp, lr, [SP, #-0x10]!
    //     0xa47e80: mov             fp, SP
    // 0xa47e84: AllocStack(0x50)
    //     0xa47e84: sub             SP, SP, #0x50
    // 0xa47e88: SetupParameters(dynamic _ /* r2 => r2, fp-0x30 */, dynamic _ /* r3 => r3, fp-0x38 */)
    //     0xa47e88: stur            x2, [fp, #-0x30]
    //     0xa47e8c: stur            x3, [fp, #-0x38]
    // 0xa47e90: CheckStackOverflow
    //     0xa47e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47e94: cmp             SP, x16
    //     0xa47e98: b.ls            #0xa480a8
    // 0xa47e9c: LoadField: r4 = r1->field_7
    //     0xa47e9c: ldur            x4, [x1, #7]
    // 0xa47ea0: stur            x4, [fp, #-0x28]
    // 0xa47ea4: LoadField: r5 = r1->field_27
    //     0xa47ea4: ldur            w5, [x1, #0x27]
    // 0xa47ea8: DecompressPointer r5
    //     0xa47ea8: add             x5, x5, HEAP, lsl #32
    // 0xa47eac: stur            x5, [fp, #-0x20]
    // 0xa47eb0: r6 = -1
    //     0xa47eb0: movn            x6, #0
    // 0xa47eb4: stur            x6, [fp, #-0x18]
    // 0xa47eb8: CheckStackOverflow
    //     0xa47eb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47ebc: cmp             SP, x16
    //     0xa47ec0: b.ls            #0xa480b0
    // 0xa47ec4: cmp             x6, #7
    // 0xa47ec8: b.gt            #0xa48098
    // 0xa47ecc: add             x7, x2, x6
    // 0xa47ed0: stur            x7, [fp, #-0x10]
    // 0xa47ed4: cmn             x7, #1
    // 0xa47ed8: b.le            #0xa47ee4
    // 0xa47edc: cmp             x4, x7
    // 0xa47ee0: b.gt            #0xa47eec
    // 0xa47ee4: mov             x1, x6
    // 0xa47ee8: b               #0xa48080
    // 0xa47eec: r8 = -1
    //     0xa47eec: movn            x8, #0
    // 0xa47ef0: stur            x8, [fp, #-8]
    // 0xa47ef4: CheckStackOverflow
    //     0xa47ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47ef8: cmp             SP, x16
    //     0xa47efc: b.ls            #0xa480b8
    // 0xa47f00: cmp             x8, #7
    // 0xa47f04: b.gt            #0xa4807c
    // 0xa47f08: add             x9, x3, x8
    // 0xa47f0c: cmn             x9, #1
    // 0xa47f10: b.le            #0xa47f1c
    // 0xa47f14: cmp             x4, x9
    // 0xa47f18: b.gt            #0xa47f24
    // 0xa47f1c: mov             x1, x8
    // 0xa47f20: b               #0xa4805c
    // 0xa47f24: tbnz            x6, #0x3f, #0xa47f3c
    // 0xa47f28: cmp             x6, #6
    // 0xa47f2c: b.gt            #0xa47f3c
    // 0xa47f30: cbz             x8, #0xa47f74
    // 0xa47f34: cmp             x8, #6
    // 0xa47f38: b.eq            #0xa47f74
    // 0xa47f3c: tbnz            x8, #0x3f, #0xa47f54
    // 0xa47f40: cmp             x8, #6
    // 0xa47f44: b.gt            #0xa47f54
    // 0xa47f48: cbz             x6, #0xa47f74
    // 0xa47f4c: cmp             x6, #6
    // 0xa47f50: b.eq            #0xa47f74
    // 0xa47f54: cmp             x6, #2
    // 0xa47f58: b.lt            #0xa47fe4
    // 0xa47f5c: cmp             x6, #4
    // 0xa47f60: b.gt            #0xa47fe4
    // 0xa47f64: cmp             x8, #2
    // 0xa47f68: b.lt            #0xa47fe4
    // 0xa47f6c: cmp             x8, #4
    // 0xa47f70: b.gt            #0xa47fe4
    // 0xa47f74: LoadField: r0 = r5->field_b
    //     0xa47f74: ldur            w0, [x5, #0xb]
    // 0xa47f78: r1 = LoadInt32Instr(r0)
    //     0xa47f78: sbfx            x1, x0, #1, #0x1f
    // 0xa47f7c: mov             x0, x1
    // 0xa47f80: mov             x1, x7
    // 0xa47f84: cmp             x1, x0
    // 0xa47f88: b.hs            #0xa480c0
    // 0xa47f8c: LoadField: r0 = r5->field_f
    //     0xa47f8c: ldur            w0, [x5, #0xf]
    // 0xa47f90: DecompressPointer r0
    //     0xa47f90: add             x0, x0, HEAP, lsl #32
    // 0xa47f94: ArrayLoad: r10 = r0[r7]  ; Unknown_4
    //     0xa47f94: add             x16, x0, x7, lsl #2
    //     0xa47f98: ldur            w10, [x16, #0xf]
    // 0xa47f9c: DecompressPointer r10
    //     0xa47f9c: add             x10, x10, HEAP, lsl #32
    // 0xa47fa0: r0 = BoxInt64Instr(r9)
    //     0xa47fa0: sbfiz           x0, x9, #1, #0x1f
    //     0xa47fa4: cmp             x9, x0, asr #1
    //     0xa47fa8: b.eq            #0xa47fb4
    //     0xa47fac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47fb0: stur            x9, [x0, #7]
    // 0xa47fb4: r1 = LoadClassIdInstr(r10)
    //     0xa47fb4: ldur            x1, [x10, #-1]
    //     0xa47fb8: ubfx            x1, x1, #0xc, #0x14
    // 0xa47fbc: stp             x0, x10, [SP, #8]
    // 0xa47fc0: r16 = true
    //     0xa47fc0: add             x16, NULL, #0x20  ; true
    // 0xa47fc4: str             x16, [SP]
    // 0xa47fc8: mov             x0, x1
    // 0xa47fcc: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa47fcc: movz            x17, #0x310f
    //     0xa47fd0: movk            x17, #0x1, lsl #16
    //     0xa47fd4: add             lr, x0, x17
    //     0xa47fd8: ldr             lr, [x21, lr, lsl #3]
    //     0xa47fdc: blr             lr
    // 0xa47fe0: b               #0xa48058
    // 0xa47fe4: ldur            x3, [fp, #-0x10]
    // 0xa47fe8: ldur            x2, [fp, #-0x20]
    // 0xa47fec: LoadField: r0 = r2->field_b
    //     0xa47fec: ldur            w0, [x2, #0xb]
    // 0xa47ff0: r1 = LoadInt32Instr(r0)
    //     0xa47ff0: sbfx            x1, x0, #1, #0x1f
    // 0xa47ff4: mov             x0, x1
    // 0xa47ff8: mov             x1, x3
    // 0xa47ffc: cmp             x1, x0
    // 0xa48000: b.hs            #0xa480c4
    // 0xa48004: LoadField: r0 = r2->field_f
    //     0xa48004: ldur            w0, [x2, #0xf]
    // 0xa48008: DecompressPointer r0
    //     0xa48008: add             x0, x0, HEAP, lsl #32
    // 0xa4800c: ArrayLoad: r4 = r0[r3]  ; Unknown_4
    //     0xa4800c: add             x16, x0, x3, lsl #2
    //     0xa48010: ldur            w4, [x16, #0xf]
    // 0xa48014: DecompressPointer r4
    //     0xa48014: add             x4, x4, HEAP, lsl #32
    // 0xa48018: r0 = BoxInt64Instr(r9)
    //     0xa48018: sbfiz           x0, x9, #1, #0x1f
    //     0xa4801c: cmp             x9, x0, asr #1
    //     0xa48020: b.eq            #0xa4802c
    //     0xa48024: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48028: stur            x9, [x0, #7]
    // 0xa4802c: r1 = LoadClassIdInstr(r4)
    //     0xa4802c: ldur            x1, [x4, #-1]
    //     0xa48030: ubfx            x1, x1, #0xc, #0x14
    // 0xa48034: stp             x0, x4, [SP, #8]
    // 0xa48038: r16 = false
    //     0xa48038: add             x16, NULL, #0x30  ; false
    // 0xa4803c: str             x16, [SP]
    // 0xa48040: mov             x0, x1
    // 0xa48044: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xa48044: movz            x17, #0x310f
    //     0xa48048: movk            x17, #0x1, lsl #16
    //     0xa4804c: add             lr, x0, x17
    //     0xa48050: ldr             lr, [x21, lr, lsl #3]
    //     0xa48054: blr             lr
    // 0xa48058: ldur            x1, [fp, #-8]
    // 0xa4805c: add             x8, x1, #1
    // 0xa48060: ldur            x2, [fp, #-0x30]
    // 0xa48064: ldur            x3, [fp, #-0x38]
    // 0xa48068: ldur            x6, [fp, #-0x18]
    // 0xa4806c: ldur            x7, [fp, #-0x10]
    // 0xa48070: ldur            x4, [fp, #-0x28]
    // 0xa48074: ldur            x5, [fp, #-0x20]
    // 0xa48078: b               #0xa47ef0
    // 0xa4807c: ldur            x1, [fp, #-0x18]
    // 0xa48080: add             x6, x1, #1
    // 0xa48084: ldur            x2, [fp, #-0x30]
    // 0xa48088: ldur            x3, [fp, #-0x38]
    // 0xa4808c: ldur            x4, [fp, #-0x28]
    // 0xa48090: ldur            x5, [fp, #-0x20]
    // 0xa48094: b               #0xa47eb4
    // 0xa48098: r0 = Null
    //     0xa48098: mov             x0, NULL
    // 0xa4809c: LeaveFrame
    //     0xa4809c: mov             SP, fp
    //     0xa480a0: ldp             fp, lr, [SP], #0x10
    // 0xa480a4: ret
    //     0xa480a4: ret             
    // 0xa480a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa480a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa480ac: b               #0xa47e9c
    // 0xa480b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa480b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa480b4: b               #0xa47ec4
    // 0xa480b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa480b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa480bc: b               #0xa47f00
    // 0xa480c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa480c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa480c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa480c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _resetModules(/* No info */) {
    // ** addr: 0xa480c8, size: 0x180
    // 0xa480c8: EnterFrame
    //     0xa480c8: stp             fp, lr, [SP, #-0x10]!
    //     0xa480cc: mov             fp, SP
    // 0xa480d0: AllocStack(0x38)
    //     0xa480d0: sub             SP, SP, #0x38
    // 0xa480d4: SetupParameters(QrImage this /* r1 => r0, fp-0x10 */)
    //     0xa480d4: mov             x0, x1
    //     0xa480d8: stur            x1, [fp, #-0x10]
    // 0xa480dc: CheckStackOverflow
    //     0xa480dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa480e0: cmp             SP, x16
    //     0xa480e4: b.ls            #0xa48238
    // 0xa480e8: LoadField: r2 = r0->field_27
    //     0xa480e8: ldur            w2, [x0, #0x27]
    // 0xa480ec: DecompressPointer r2
    //     0xa480ec: add             x2, x2, HEAP, lsl #32
    // 0xa480f0: mov             x1, x2
    // 0xa480f4: stur            x2, [fp, #-8]
    // 0xa480f8: r0 = clear()
    //     0xa480f8: bl              #0xbd9e80  ; [dart:core] _GrowableList::clear
    // 0xa480fc: ldur            x0, [fp, #-0x10]
    // 0xa48100: LoadField: r3 = r0->field_7
    //     0xa48100: ldur            x3, [x0, #7]
    // 0xa48104: stur            x3, [fp, #-0x28]
    // 0xa48108: r0 = BoxInt64Instr(r3)
    //     0xa48108: sbfiz           x0, x3, #1, #0x1f
    //     0xa4810c: cmp             x3, x0, asr #1
    //     0xa48110: b.eq            #0xa4811c
    //     0xa48114: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48118: stur            x3, [x0, #7]
    // 0xa4811c: mov             x4, x0
    // 0xa48120: ldur            x0, [fp, #-8]
    // 0xa48124: stur            x4, [fp, #-0x20]
    // 0xa48128: LoadField: r5 = r0->field_7
    //     0xa48128: ldur            w5, [x0, #7]
    // 0xa4812c: DecompressPointer r5
    //     0xa4812c: add             x5, x5, HEAP, lsl #32
    // 0xa48130: stur            x5, [fp, #-0x10]
    // 0xa48134: r6 = 0
    //     0xa48134: movz            x6, #0
    // 0xa48138: stur            x6, [fp, #-0x18]
    // 0xa4813c: CheckStackOverflow
    //     0xa4813c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48140: cmp             SP, x16
    //     0xa48144: b.ls            #0xa48240
    // 0xa48148: cmp             x6, x3
    // 0xa4814c: b.ge            #0xa48228
    // 0xa48150: mov             x2, x4
    // 0xa48154: r1 = <bool?>
    //     0xa48154: add             x1, PP, #9, lsl #12  ; [pp+0x9018] TypeArguments: <bool?>
    //     0xa48158: ldr             x1, [x1, #0x18]
    // 0xa4815c: r0 = AllocateArray()
    //     0xa4815c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa48160: ldur            x2, [fp, #-0x10]
    // 0xa48164: mov             x3, x0
    // 0xa48168: r1 = Null
    //     0xa48168: mov             x1, NULL
    // 0xa4816c: stur            x3, [fp, #-0x30]
    // 0xa48170: cmp             w2, NULL
    // 0xa48174: b.eq            #0xa48194
    // 0xa48178: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa48178: ldur            w4, [x2, #0x17]
    // 0xa4817c: DecompressPointer r4
    //     0xa4817c: add             x4, x4, HEAP, lsl #32
    // 0xa48180: r8 = X0
    //     0xa48180: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa48184: LoadField: r9 = r4->field_7
    //     0xa48184: ldur            x9, [x4, #7]
    // 0xa48188: r3 = Null
    //     0xa48188: add             x3, PP, #0x57, lsl #12  ; [pp+0x57d78] Null
    //     0xa4818c: ldr             x3, [x3, #0xd78]
    // 0xa48190: blr             x9
    // 0xa48194: ldur            x0, [fp, #-8]
    // 0xa48198: LoadField: r1 = r0->field_b
    //     0xa48198: ldur            w1, [x0, #0xb]
    // 0xa4819c: LoadField: r2 = r0->field_f
    //     0xa4819c: ldur            w2, [x0, #0xf]
    // 0xa481a0: DecompressPointer r2
    //     0xa481a0: add             x2, x2, HEAP, lsl #32
    // 0xa481a4: LoadField: r3 = r2->field_b
    //     0xa481a4: ldur            w3, [x2, #0xb]
    // 0xa481a8: r2 = LoadInt32Instr(r1)
    //     0xa481a8: sbfx            x2, x1, #1, #0x1f
    // 0xa481ac: stur            x2, [fp, #-0x38]
    // 0xa481b0: r1 = LoadInt32Instr(r3)
    //     0xa481b0: sbfx            x1, x3, #1, #0x1f
    // 0xa481b4: cmp             x2, x1
    // 0xa481b8: b.ne            #0xa481c4
    // 0xa481bc: mov             x1, x0
    // 0xa481c0: r0 = _growToNextCapacity()
    //     0xa481c0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa481c4: ldur            x2, [fp, #-8]
    // 0xa481c8: ldur            x4, [fp, #-0x18]
    // 0xa481cc: ldur            x3, [fp, #-0x38]
    // 0xa481d0: add             x5, x3, #1
    // 0xa481d4: lsl             x6, x5, #1
    // 0xa481d8: StoreField: r2->field_b = r6
    //     0xa481d8: stur            w6, [x2, #0xb]
    // 0xa481dc: LoadField: r1 = r2->field_f
    //     0xa481dc: ldur            w1, [x2, #0xf]
    // 0xa481e0: DecompressPointer r1
    //     0xa481e0: add             x1, x1, HEAP, lsl #32
    // 0xa481e4: ldur            x0, [fp, #-0x30]
    // 0xa481e8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa481e8: add             x25, x1, x3, lsl #2
    //     0xa481ec: add             x25, x25, #0xf
    //     0xa481f0: str             w0, [x25]
    //     0xa481f4: tbz             w0, #0, #0xa48210
    //     0xa481f8: ldurb           w16, [x1, #-1]
    //     0xa481fc: ldurb           w17, [x0, #-1]
    //     0xa48200: and             x16, x17, x16, lsr #2
    //     0xa48204: tst             x16, HEAP, lsr #32
    //     0xa48208: b.eq            #0xa48210
    //     0xa4820c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa48210: add             x6, x4, #1
    // 0xa48214: mov             x0, x2
    // 0xa48218: ldur            x3, [fp, #-0x28]
    // 0xa4821c: ldur            x5, [fp, #-0x10]
    // 0xa48220: ldur            x4, [fp, #-0x20]
    // 0xa48224: b               #0xa48138
    // 0xa48228: r0 = Null
    //     0xa48228: mov             x0, NULL
    // 0xa4822c: LeaveFrame
    //     0xa4822c: mov             SP, fp
    //     0xa48230: ldp             fp, lr, [SP], #0x10
    // 0xa48234: ret
    //     0xa48234: ret             
    // 0xa48238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48238: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4823c: b               #0xa480e8
    // 0xa48240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48244: b               #0xa48148
  }
}
