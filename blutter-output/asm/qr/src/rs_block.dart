// lib: , url: package:qr/src/rs_block.dart

// class id: 1051069, size: 0x8
class :: {

  static _ _getRsBlockTable(/* No info */) {
    // ** addr: 0xa45508, size: 0x44
    // 0xa45508: EnterFrame
    //     0xa45508: stp             fp, lr, [SP, #-0x10]!
    //     0xa4550c: mov             fp, SP
    // 0xa45510: r2 = const [_ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:3, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6]
    //     0xa45510: add             x2, PP, #0x57, lsl #12  ; [pp+0x57dc0] List<List<int>>(160)
    //     0xa45514: ldr             x2, [x2, #0xdc0]
    // 0xa45518: sub             x3, x1, #1
    // 0xa4551c: lsl             x4, x3, #2
    // 0xa45520: mov             x1, x4
    // 0xa45524: r0 = 160
    //     0xa45524: movz            x0, #0xa0
    // 0xa45528: cmp             x1, x0
    // 0xa4552c: b.hs            #0xa45548
    // 0xa45530: ArrayLoad: r0 = r2[r4]  ; Unknown_4
    //     0xa45530: add             x16, x2, x4, lsl #2
    //     0xa45534: ldur            w0, [x16, #0xf]
    // 0xa45538: DecompressPointer r0
    //     0xa45538: add             x0, x0, HEAP, lsl #32
    // 0xa4553c: LeaveFrame
    //     0xa4553c: mov             SP, fp
    //     0xa45540: ldp             fp, lr, [SP], #0x10
    // 0xa45544: ret
    //     0xa45544: ret             
    // 0xa45548: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa45548: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 535, size: 0x18, field offset: 0x8
class QrRsBlock extends Object {

  static _ getRSBlocks(/* No info */) {
    // ** addr: 0xa452c0, size: 0x21c
    // 0xa452c0: EnterFrame
    //     0xa452c0: stp             fp, lr, [SP, #-0x10]!
    //     0xa452c4: mov             fp, SP
    // 0xa452c8: AllocStack(0x58)
    //     0xa452c8: sub             SP, SP, #0x58
    // 0xa452cc: CheckStackOverflow
    //     0xa452cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa452d0: cmp             SP, x16
    //     0xa452d4: b.ls            #0xa454b8
    // 0xa452d8: r0 = _getRsBlockTable()
    //     0xa452d8: bl              #0xa45508  ; [package:qr/src/rs_block.dart] ::_getRsBlockTable
    // 0xa452dc: stur            x0, [fp, #-0x18]
    // 0xa452e0: LoadField: r1 = r0->field_b
    //     0xa452e0: ldur            w1, [x0, #0xb]
    // 0xa452e4: r3 = LoadInt32Instr(r1)
    //     0xa452e4: sbfx            x3, x1, #1, #0x1f
    // 0xa452e8: stur            x3, [fp, #-0x10]
    // 0xa452ec: r1 = 3
    //     0xa452ec: movz            x1, #0x3
    // 0xa452f0: sdiv            x4, x3, x1
    // 0xa452f4: stur            x4, [fp, #-8]
    // 0xa452f8: r1 = <QrRsBlock>
    //     0xa452f8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57db8] TypeArguments: <QrRsBlock>
    //     0xa452fc: ldr             x1, [x1, #0xdb8]
    // 0xa45300: r2 = 0
    //     0xa45300: movz            x2, #0
    // 0xa45304: r0 = _GrowableList()
    //     0xa45304: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa45308: mov             x2, x0
    // 0xa4530c: stur            x2, [fp, #-0x48]
    // 0xa45310: r5 = 0
    //     0xa45310: movz            x5, #0
    // 0xa45314: ldur            x3, [fp, #-0x18]
    // 0xa45318: ldur            x4, [fp, #-8]
    // 0xa4531c: stur            x5, [fp, #-0x40]
    // 0xa45320: CheckStackOverflow
    //     0xa45320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45324: cmp             SP, x16
    //     0xa45328: b.ls            #0xa454c0
    // 0xa4532c: cmp             x5, x4
    // 0xa45330: b.ge            #0xa454a8
    // 0xa45334: r16 = 3
    //     0xa45334: movz            x16, #0x3
    // 0xa45338: mul             x6, x5, x16
    // 0xa4533c: ldur            x0, [fp, #-0x10]
    // 0xa45340: mov             x1, x6
    // 0xa45344: cmp             x1, x0
    // 0xa45348: b.hs            #0xa454c8
    // 0xa4534c: ArrayLoad: r7 = r3[r6]  ; Unknown_4
    //     0xa4534c: add             x16, x3, x6, lsl #2
    //     0xa45350: ldur            w7, [x16, #0xf]
    // 0xa45354: DecompressPointer r7
    //     0xa45354: add             x7, x7, HEAP, lsl #32
    // 0xa45358: add             x8, x6, #1
    // 0xa4535c: ldur            x0, [fp, #-0x10]
    // 0xa45360: mov             x1, x8
    // 0xa45364: cmp             x1, x0
    // 0xa45368: b.hs            #0xa454cc
    // 0xa4536c: ArrayLoad: r9 = r3[r8]  ; Unknown_4
    //     0xa4536c: add             x16, x3, x8, lsl #2
    //     0xa45370: ldur            w9, [x16, #0xf]
    // 0xa45374: DecompressPointer r9
    //     0xa45374: add             x9, x9, HEAP, lsl #32
    // 0xa45378: add             x8, x6, #2
    // 0xa4537c: ldur            x0, [fp, #-0x10]
    // 0xa45380: mov             x1, x8
    // 0xa45384: cmp             x1, x0
    // 0xa45388: b.hs            #0xa454d0
    // 0xa4538c: ArrayLoad: r0 = r3[r8]  ; Unknown_4
    //     0xa4538c: add             x16, x3, x8, lsl #2
    //     0xa45390: ldur            w0, [x16, #0xf]
    // 0xa45394: DecompressPointer r0
    //     0xa45394: add             x0, x0, HEAP, lsl #32
    // 0xa45398: r1 = LoadInt32Instr(r7)
    //     0xa45398: sbfx            x1, x7, #1, #0x1f
    //     0xa4539c: tbz             w7, #0, #0xa453a4
    //     0xa453a0: ldur            x1, [x7, #7]
    // 0xa453a4: stur            x1, [fp, #-0x38]
    // 0xa453a8: r6 = LoadInt32Instr(r9)
    //     0xa453a8: sbfx            x6, x9, #1, #0x1f
    //     0xa453ac: tbz             w9, #0, #0xa453b4
    //     0xa453b0: ldur            x6, [x9, #7]
    // 0xa453b4: stur            x6, [fp, #-0x30]
    // 0xa453b8: r7 = LoadInt32Instr(r0)
    //     0xa453b8: sbfx            x7, x0, #1, #0x1f
    //     0xa453bc: tbz             w0, #0, #0xa453c4
    //     0xa453c0: ldur            x7, [x0, #7]
    // 0xa453c4: stur            x7, [fp, #-0x28]
    // 0xa453c8: r0 = 0
    //     0xa453c8: movz            x0, #0
    // 0xa453cc: stur            x0, [fp, #-0x20]
    // 0xa453d0: CheckStackOverflow
    //     0xa453d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa453d4: cmp             SP, x16
    //     0xa453d8: b.ls            #0xa454d4
    // 0xa453dc: cmp             x0, x1
    // 0xa453e0: b.ge            #0xa4549c
    // 0xa453e4: r0 = QrRsBlock()
    //     0xa453e4: bl              #0xa454fc  ; AllocateQrRsBlockStub -> QrRsBlock (size=0x18)
    // 0xa453e8: mov             x2, x0
    // 0xa453ec: ldur            x0, [fp, #-0x30]
    // 0xa453f0: stur            x2, [fp, #-0x58]
    // 0xa453f4: StoreField: r2->field_7 = r0
    //     0xa453f4: stur            x0, [x2, #7]
    // 0xa453f8: ldur            x3, [fp, #-0x28]
    // 0xa453fc: StoreField: r2->field_f = r3
    //     0xa453fc: stur            x3, [x2, #0xf]
    // 0xa45400: ldur            x4, [fp, #-0x48]
    // 0xa45404: LoadField: r1 = r4->field_b
    //     0xa45404: ldur            w1, [x4, #0xb]
    // 0xa45408: LoadField: r5 = r4->field_f
    //     0xa45408: ldur            w5, [x4, #0xf]
    // 0xa4540c: DecompressPointer r5
    //     0xa4540c: add             x5, x5, HEAP, lsl #32
    // 0xa45410: LoadField: r6 = r5->field_b
    //     0xa45410: ldur            w6, [x5, #0xb]
    // 0xa45414: r5 = LoadInt32Instr(r1)
    //     0xa45414: sbfx            x5, x1, #1, #0x1f
    // 0xa45418: stur            x5, [fp, #-0x50]
    // 0xa4541c: r1 = LoadInt32Instr(r6)
    //     0xa4541c: sbfx            x1, x6, #1, #0x1f
    // 0xa45420: cmp             x5, x1
    // 0xa45424: b.ne            #0xa45430
    // 0xa45428: mov             x1, x4
    // 0xa4542c: r0 = _growToNextCapacity()
    //     0xa4542c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa45430: ldur            x2, [fp, #-0x48]
    // 0xa45434: ldur            x4, [fp, #-0x20]
    // 0xa45438: ldur            x3, [fp, #-0x50]
    // 0xa4543c: add             x5, x3, #1
    // 0xa45440: lsl             x6, x5, #1
    // 0xa45444: StoreField: r2->field_b = r6
    //     0xa45444: stur            w6, [x2, #0xb]
    // 0xa45448: LoadField: r1 = r2->field_f
    //     0xa45448: ldur            w1, [x2, #0xf]
    // 0xa4544c: DecompressPointer r1
    //     0xa4544c: add             x1, x1, HEAP, lsl #32
    // 0xa45450: ldur            x0, [fp, #-0x58]
    // 0xa45454: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa45454: add             x25, x1, x3, lsl #2
    //     0xa45458: add             x25, x25, #0xf
    //     0xa4545c: str             w0, [x25]
    //     0xa45460: tbz             w0, #0, #0xa4547c
    //     0xa45464: ldurb           w16, [x1, #-1]
    //     0xa45468: ldurb           w17, [x0, #-1]
    //     0xa4546c: and             x16, x17, x16, lsr #2
    //     0xa45470: tst             x16, HEAP, lsr #32
    //     0xa45474: b.eq            #0xa4547c
    //     0xa45478: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4547c: add             x0, x4, #1
    // 0xa45480: ldur            x3, [fp, #-0x18]
    // 0xa45484: ldur            x4, [fp, #-8]
    // 0xa45488: ldur            x5, [fp, #-0x40]
    // 0xa4548c: ldur            x1, [fp, #-0x38]
    // 0xa45490: ldur            x6, [fp, #-0x30]
    // 0xa45494: ldur            x7, [fp, #-0x28]
    // 0xa45498: b               #0xa453cc
    // 0xa4549c: mov             x1, x5
    // 0xa454a0: add             x5, x1, #1
    // 0xa454a4: b               #0xa45314
    // 0xa454a8: mov             x0, x2
    // 0xa454ac: LeaveFrame
    //     0xa454ac: mov             SP, fp
    //     0xa454b0: ldp             fp, lr, [SP], #0x10
    // 0xa454b4: ret
    //     0xa454b4: ret             
    // 0xa454b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa454b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa454bc: b               #0xa452d8
    // 0xa454c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa454c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa454c4: b               #0xa4532c
    // 0xa454c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa454c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa454cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa454cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa454d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa454d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa454d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa454d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa454d8: b               #0xa453dc
  }
}
