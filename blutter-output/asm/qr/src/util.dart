// lib: , url: package:qr/src/util.dart

// class id: 1051070, size: 0x8
class :: {

  static int bchTypeNumber(int) {
    // ** addr: 0xa470cc, size: 0x158
    // 0xa470cc: EnterFrame
    //     0xa470cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa470d0: mov             fp, SP
    // 0xa470d4: lsl             x2, x1, #0xc
    // 0xa470d8: mov             x3, x2
    // 0xa470dc: r1 = 7973
    //     0xa470dc: movz            x1, #0x1f25
    // 0xa470e0: CheckStackOverflow
    //     0xa470e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa470e4: cmp             SP, x16
    //     0xa470e8: b.ls            #0xa471d0
    // 0xa470ec: mov             x5, x3
    // 0xa470f0: r4 = 0
    //     0xa470f0: movz            x4, #0
    // 0xa470f4: CheckStackOverflow
    //     0xa470f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa470f8: cmp             SP, x16
    //     0xa470fc: b.ls            #0xa471d8
    // 0xa47100: cbz             x5, #0xa47118
    // 0xa47104: add             x0, x4, #1
    // 0xa47108: asr             x6, x5, #1
    // 0xa4710c: mov             x5, x6
    // 0xa47110: mov             x4, x0
    // 0xa47114: b               #0xa470f4
    // 0xa47118: r6 = 7973
    //     0xa47118: movz            x6, #0x1f25
    // 0xa4711c: r5 = 0
    //     0xa4711c: movz            x5, #0
    // 0xa47120: CheckStackOverflow
    //     0xa47120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47124: cmp             SP, x16
    //     0xa47128: b.ls            #0xa471e0
    // 0xa4712c: cbz             x6, #0xa47144
    // 0xa47130: add             x0, x5, #1
    // 0xa47134: asr             x7, x6, #1
    // 0xa47138: mov             x6, x7
    // 0xa4713c: mov             x5, x0
    // 0xa47140: b               #0xa47120
    // 0xa47144: sub             x6, x4, x5
    // 0xa47148: tbnz            x6, #0x3f, #0xa471c0
    // 0xa4714c: mov             x5, x3
    // 0xa47150: r4 = 0
    //     0xa47150: movz            x4, #0
    // 0xa47154: CheckStackOverflow
    //     0xa47154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47158: cmp             SP, x16
    //     0xa4715c: b.ls            #0xa471e8
    // 0xa47160: cbz             x5, #0xa47178
    // 0xa47164: add             x0, x4, #1
    // 0xa47168: asr             x6, x5, #1
    // 0xa4716c: mov             x5, x6
    // 0xa47170: mov             x4, x0
    // 0xa47174: b               #0xa47154
    // 0xa47178: r6 = 7973
    //     0xa47178: movz            x6, #0x1f25
    // 0xa4717c: r5 = 0
    //     0xa4717c: movz            x5, #0
    // 0xa47180: CheckStackOverflow
    //     0xa47180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47184: cmp             SP, x16
    //     0xa47188: b.ls            #0xa471f0
    // 0xa4718c: cbz             x6, #0xa471a4
    // 0xa47190: add             x0, x5, #1
    // 0xa47194: asr             x7, x6, #1
    // 0xa47198: mov             x6, x7
    // 0xa4719c: mov             x5, x0
    // 0xa471a0: b               #0xa47180
    // 0xa471a4: sub             x6, x4, x5
    // 0xa471a8: cmp             x6, #0x3f
    // 0xa471ac: b.hi            #0xa471f8
    // 0xa471b0: lsl             x4, x1, x6
    // 0xa471b4: eor             x0, x3, x4
    // 0xa471b8: mov             x3, x0
    // 0xa471bc: b               #0xa470e0
    // 0xa471c0: orr             x0, x2, x3
    // 0xa471c4: LeaveFrame
    //     0xa471c4: mov             SP, fp
    //     0xa471c8: ldp             fp, lr, [SP], #0x10
    // 0xa471cc: ret
    //     0xa471cc: ret             
    // 0xa471d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa471d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa471d4: b               #0xa470ec
    // 0xa471d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa471d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa471dc: b               #0xa47100
    // 0xa471e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa471e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa471e4: b               #0xa4712c
    // 0xa471e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa471e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa471ec: b               #0xa47160
    // 0xa471f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa471f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa471f4: b               #0xa4718c
    // 0xa471f8: tbnz            x6, #0x3f, #0xa47204
    // 0xa471fc: mov             x4, xzr
    // 0xa47200: b               #0xa471b4
    // 0xa47204: str             x6, [THR, #0x7a8]  ; THR::
    // 0xa47208: stp             x3, x6, [SP, #-0x10]!
    // 0xa4720c: stp             x1, x2, [SP, #-0x10]!
    // 0xa47210: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa47214: r4 = 0
    //     0xa47214: movz            x4, #0
    // 0xa47218: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa4721c: blr             lr
    // 0xa47220: brk             #0
  }
  static int bchTypeInfo(int) {
    // ** addr: 0xa47714, size: 0x160
    // 0xa47714: EnterFrame
    //     0xa47714: stp             fp, lr, [SP, #-0x10]!
    //     0xa47718: mov             fp, SP
    // 0xa4771c: lsl             x2, x1, #0xa
    // 0xa47720: mov             x3, x2
    // 0xa47724: r1 = 1335
    //     0xa47724: movz            x1, #0x537
    // 0xa47728: CheckStackOverflow
    //     0xa47728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4772c: cmp             SP, x16
    //     0xa47730: b.ls            #0xa47820
    // 0xa47734: mov             x5, x3
    // 0xa47738: r4 = 0
    //     0xa47738: movz            x4, #0
    // 0xa4773c: CheckStackOverflow
    //     0xa4773c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47740: cmp             SP, x16
    //     0xa47744: b.ls            #0xa47828
    // 0xa47748: cbz             x5, #0xa47760
    // 0xa4774c: add             x0, x4, #1
    // 0xa47750: asr             x6, x5, #1
    // 0xa47754: mov             x5, x6
    // 0xa47758: mov             x4, x0
    // 0xa4775c: b               #0xa4773c
    // 0xa47760: r6 = 1335
    //     0xa47760: movz            x6, #0x537
    // 0xa47764: r5 = 0
    //     0xa47764: movz            x5, #0
    // 0xa47768: CheckStackOverflow
    //     0xa47768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4776c: cmp             SP, x16
    //     0xa47770: b.ls            #0xa47830
    // 0xa47774: cbz             x6, #0xa4778c
    // 0xa47778: add             x0, x5, #1
    // 0xa4777c: asr             x7, x6, #1
    // 0xa47780: mov             x6, x7
    // 0xa47784: mov             x5, x0
    // 0xa47788: b               #0xa47768
    // 0xa4778c: sub             x6, x4, x5
    // 0xa47790: tbnz            x6, #0x3f, #0xa47808
    // 0xa47794: mov             x5, x3
    // 0xa47798: r4 = 0
    //     0xa47798: movz            x4, #0
    // 0xa4779c: CheckStackOverflow
    //     0xa4779c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa477a0: cmp             SP, x16
    //     0xa477a4: b.ls            #0xa47838
    // 0xa477a8: cbz             x5, #0xa477c0
    // 0xa477ac: add             x0, x4, #1
    // 0xa477b0: asr             x6, x5, #1
    // 0xa477b4: mov             x5, x6
    // 0xa477b8: mov             x4, x0
    // 0xa477bc: b               #0xa4779c
    // 0xa477c0: r6 = 1335
    //     0xa477c0: movz            x6, #0x537
    // 0xa477c4: r5 = 0
    //     0xa477c4: movz            x5, #0
    // 0xa477c8: CheckStackOverflow
    //     0xa477c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa477cc: cmp             SP, x16
    //     0xa477d0: b.ls            #0xa47840
    // 0xa477d4: cbz             x6, #0xa477ec
    // 0xa477d8: add             x0, x5, #1
    // 0xa477dc: asr             x7, x6, #1
    // 0xa477e0: mov             x6, x7
    // 0xa477e4: mov             x5, x0
    // 0xa477e8: b               #0xa477c8
    // 0xa477ec: sub             x6, x4, x5
    // 0xa477f0: cmp             x6, #0x3f
    // 0xa477f4: b.hi            #0xa47848
    // 0xa477f8: lsl             x4, x1, x6
    // 0xa477fc: eor             x0, x3, x4
    // 0xa47800: mov             x3, x0
    // 0xa47804: b               #0xa47728
    // 0xa47808: orr             x1, x2, x3
    // 0xa4780c: r16 = 21522
    //     0xa4780c: movz            x16, #0x5412
    // 0xa47810: eor             x0, x1, x16
    // 0xa47814: LeaveFrame
    //     0xa47814: mov             SP, fp
    //     0xa47818: ldp             fp, lr, [SP], #0x10
    // 0xa4781c: ret
    //     0xa4781c: ret             
    // 0xa47820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47820: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47824: b               #0xa47734
    // 0xa47828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47828: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4782c: b               #0xa47748
    // 0xa47830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47830: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47834: b               #0xa47774
    // 0xa47838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47838: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4783c: b               #0xa477a8
    // 0xa47840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa47840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa47844: b               #0xa477d4
    // 0xa47848: tbnz            x6, #0x3f, #0xa47854
    // 0xa4784c: mov             x4, xzr
    // 0xa47850: b               #0xa477fc
    // 0xa47854: str             x6, [THR, #0x7a8]  ; THR::
    // 0xa47858: stp             x3, x6, [SP, #-0x10]!
    // 0xa4785c: stp             x1, x2, [SP, #-0x10]!
    // 0xa47860: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa47864: r4 = 0
    //     0xa47864: movz            x4, #0
    // 0xa47868: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa4786c: blr             lr
    // 0xa47870: brk             #0
  }
  static _ patternPosition(/* No info */) {
    // ** addr: 0xa47e44, size: 0x38
    // 0xa47e44: r2 = const [_ImmutableList len:0, _ImmutableList len:2, _ImmutableList len:2, _ImmutableList len:2, _ImmutableList len:2, _ImmutableList len:2, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:3, _ImmutableList len:4, _ImmutableList len:4, _ImmutableList len:4, _ImmutableList len:4, _ImmutableList len:4, _ImmutableList len:4, _ImmutableList len:4, _ImmutableList len:5, _ImmutableList len:5, _ImmutableList len:5, _ImmutableList len:5, _ImmutableList len:5, _ImmutableList len:5, _ImmutableList len:5, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:6, _ImmutableList len:7, _ImmutableList len:7, _ImmutableList len:7, _ImmutableList len:7, _ImmutableList len:7, _ImmutableList len:7]
    //     0xa47e44: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d70] List<List<int>>(40)
    //     0xa47e48: ldr             x2, [x2, #0xd70]
    // 0xa47e4c: sub             x3, x1, #1
    // 0xa47e50: mov             x1, x3
    // 0xa47e54: r0 = 40
    //     0xa47e54: movz            x0, #0x28
    // 0xa47e58: cmp             x1, x0
    // 0xa47e5c: b.hs            #0xa47e70
    // 0xa47e60: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0xa47e60: add             x16, x2, x3, lsl #2
    //     0xa47e64: ldur            w0, [x16, #0xf]
    // 0xa47e68: DecompressPointer r0
    //     0xa47e68: add             x0, x0, HEAP, lsl #32
    // 0xa47e6c: ret
    //     0xa47e6c: ret             
    // 0xa47e70: EnterFrame
    //     0xa47e70: stp             fp, lr, [SP, #-0x10]!
    //     0xa47e74: mov             fp, SP
    // 0xa47e78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa47e78: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
