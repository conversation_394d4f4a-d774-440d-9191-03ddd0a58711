// lib: , url: package:qr_flutter/src/paint_cache.dart

// class id: 1051071, size: 0x8
class :: {
}

// class id: 534, size: 0x10, field offset: 0x8
class PaintCache extends Object {

  _ firstPaint(/* No info */) {
    // ** addr: 0x7d5354, size: 0xcc
    // 0x7d5354: EnterFrame
    //     0x7d5354: stp             fp, lr, [SP, #-0x10]!
    //     0x7d5358: mov             fp, SP
    // 0x7d535c: AllocStack(0x8)
    //     0x7d535c: sub             SP, SP, #8
    // 0x7d5360: SetupParameters({dynamic position = Null /* r3 */})
    //     0x7d5360: ldur            w0, [x4, #0x13]
    //     0x7d5364: ldur            w3, [x4, #0x1f]
    //     0x7d5368: add             x3, x3, HEAP, lsl #32
    //     0x7d536c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38468] "position"
    //     0x7d5370: ldr             x16, [x16, #0x468]
    //     0x7d5374: cmp             w3, w16
    //     0x7d5378: b.ne            #0x7d5398
    //     0x7d537c: ldur            w3, [x4, #0x23]
    //     0x7d5380: add             x3, x3, HEAP, lsl #32
    //     0x7d5384: sub             w4, w0, w3
    //     0x7d5388: add             x0, fp, w4, sxtw #2
    //     0x7d538c: ldr             x0, [x0, #8]
    //     0x7d5390: mov             x3, x0
    //     0x7d5394: b               #0x7d539c
    //     0x7d5398: mov             x3, NULL
    // 0x7d539c: CheckStackOverflow
    //     0x7d539c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d53a0: cmp             SP, x16
    //     0x7d53a4: b.ls            #0x7d5418
    // 0x7d53a8: r16 = Instance_QrCodeElement
    //     0x7d53a8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d08] Obj!QrCodeElement@e2e3c1
    //     0x7d53ac: ldr             x16, [x16, #0xd08]
    // 0x7d53b0: cmp             w2, w16
    // 0x7d53b4: b.ne            #0x7d53cc
    // 0x7d53b8: LoadField: r0 = r1->field_7
    //     0x7d53b8: ldur            w0, [x1, #7]
    // 0x7d53bc: DecompressPointer r0
    //     0x7d53bc: add             x0, x0, HEAP, lsl #32
    // 0x7d53c0: mov             x1, x0
    // 0x7d53c4: r0 = first()
    //     0x7d53c4: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x7d53c8: b               #0x7d540c
    // 0x7d53cc: LoadField: r0 = r1->field_b
    //     0x7d53cc: ldur            w0, [x1, #0xb]
    // 0x7d53d0: DecompressPointer r0
    //     0x7d53d0: add             x0, x0, HEAP, lsl #32
    // 0x7d53d4: stur            x0, [fp, #-8]
    // 0x7d53d8: r0 = _cacheKey()
    //     0x7d53d8: bl              #0x7d5420  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::_cacheKey
    // 0x7d53dc: ldur            x1, [fp, #-8]
    // 0x7d53e0: mov             x2, x0
    // 0x7d53e4: r0 = _getValueOrData()
    //     0x7d53e4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7d53e8: ldur            x1, [fp, #-8]
    // 0x7d53ec: LoadField: r2 = r1->field_f
    //     0x7d53ec: ldur            w2, [x1, #0xf]
    // 0x7d53f0: DecompressPointer r2
    //     0x7d53f0: add             x2, x2, HEAP, lsl #32
    // 0x7d53f4: cmp             w2, w0
    // 0x7d53f8: b.ne            #0x7d5404
    // 0x7d53fc: r1 = Null
    //     0x7d53fc: mov             x1, NULL
    // 0x7d5400: b               #0x7d5408
    // 0x7d5404: mov             x1, x0
    // 0x7d5408: mov             x0, x1
    // 0x7d540c: LeaveFrame
    //     0x7d540c: mov             SP, fp
    //     0x7d5410: ldp             fp, lr, [SP], #0x10
    // 0x7d5414: ret
    //     0x7d5414: ret             
    // 0x7d5418: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d5418: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d541c: b               #0x7d53a8
  }
  _ _cacheKey(/* No info */) {
    // ** addr: 0x7d5420, size: 0x8c
    // 0x7d5420: EnterFrame
    //     0x7d5420: stp             fp, lr, [SP, #-0x10]!
    //     0x7d5424: mov             fp, SP
    // 0x7d5428: AllocStack(0x18)
    //     0x7d5428: sub             SP, SP, #0x18
    // 0x7d542c: SetupParameters(PaintCache this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r1 */)
    //     0x7d542c: mov             x0, x1
    //     0x7d5430: mov             x1, x3
    //     0x7d5434: stur            x2, [fp, #-8]
    // 0x7d5438: CheckStackOverflow
    //     0x7d5438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d543c: cmp             SP, x16
    //     0x7d5440: b.ls            #0x7d54a4
    // 0x7d5444: cmp             w1, NULL
    // 0x7d5448: b.eq            #0x7d5458
    // 0x7d544c: r0 = _enumToString()
    //     0x7d544c: bl              #0xc4e568  ; [package:qr_flutter/src/types.dart] FinderPatternPosition::_enumToString
    // 0x7d5450: mov             x3, x0
    // 0x7d5454: b               #0x7d5460
    // 0x7d5458: r3 = "any"
    //     0x7d5458: add             x3, PP, #0x57, lsl #12  ; [pp+0x57d38] "any"
    //     0x7d545c: ldr             x3, [x3, #0xd38]
    // 0x7d5460: ldur            x0, [fp, #-8]
    // 0x7d5464: stur            x3, [fp, #-0x10]
    // 0x7d5468: r1 = Null
    //     0x7d5468: mov             x1, NULL
    // 0x7d546c: r2 = 6
    //     0x7d546c: movz            x2, #0x6
    // 0x7d5470: r0 = AllocateArray()
    //     0x7d5470: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7d5474: mov             x1, x0
    // 0x7d5478: ldur            x0, [fp, #-8]
    // 0x7d547c: StoreField: r1->field_f = r0
    //     0x7d547c: stur            w0, [x1, #0xf]
    // 0x7d5480: r16 = ":"
    //     0x7d5480: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0x7d5484: StoreField: r1->field_13 = r16
    //     0x7d5484: stur            w16, [x1, #0x13]
    // 0x7d5488: ldur            x0, [fp, #-0x10]
    // 0x7d548c: ArrayStore: r1[0] = r0  ; List_4
    //     0x7d548c: stur            w0, [x1, #0x17]
    // 0x7d5490: str             x1, [SP]
    // 0x7d5494: r0 = _interpolate()
    //     0x7d5494: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7d5498: LeaveFrame
    //     0x7d5498: mov             SP, fp
    //     0x7d549c: ldp             fp, lr, [SP], #0x10
    // 0x7d54a0: ret
    //     0x7d54a0: ret             
    // 0x7d54a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d54a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d54a8: b               #0x7d5444
  }
  _ cache(/* No info */) {
    // ** addr: 0xa45b58, size: 0x168
    // 0xa45b58: EnterFrame
    //     0xa45b58: stp             fp, lr, [SP, #-0x10]!
    //     0xa45b5c: mov             fp, SP
    // 0xa45b60: AllocStack(0x18)
    //     0xa45b60: sub             SP, SP, #0x18
    // 0xa45b64: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2 */, {dynamic position = Null /* r0 */})
    //     0xa45b64: stur            x2, [fp, #-0x10]
    //     0xa45b68: mov             x16, x3
    //     0xa45b6c: mov             x3, x2
    //     0xa45b70: mov             x2, x16
    //     0xa45b74: ldur            w0, [x4, #0x13]
    //     0xa45b78: ldur            w5, [x4, #0x1f]
    //     0xa45b7c: add             x5, x5, HEAP, lsl #32
    //     0xa45b80: add             x16, PP, #0x38, lsl #12  ; [pp+0x38468] "position"
    //     0xa45b84: ldr             x16, [x16, #0x468]
    //     0xa45b88: cmp             w5, w16
    //     0xa45b8c: b.ne            #0xa45ba8
    //     0xa45b90: ldur            w5, [x4, #0x23]
    //     0xa45b94: add             x5, x5, HEAP, lsl #32
    //     0xa45b98: sub             w4, w0, w5
    //     0xa45b9c: add             x0, fp, w4, sxtw #2
    //     0xa45ba0: ldr             x0, [x0, #8]
    //     0xa45ba4: b               #0xa45bac
    //     0xa45ba8: mov             x0, NULL
    // 0xa45bac: CheckStackOverflow
    //     0xa45bac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45bb0: cmp             SP, x16
    //     0xa45bb4: b.ls            #0xa45cb8
    // 0xa45bb8: r16 = Instance_QrCodeElement
    //     0xa45bb8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d08] Obj!QrCodeElement@e2e3c1
    //     0xa45bbc: ldr             x16, [x16, #0xd08]
    // 0xa45bc0: cmp             w2, w16
    // 0xa45bc4: b.ne            #0xa45c84
    // 0xa45bc8: LoadField: r4 = r1->field_7
    //     0xa45bc8: ldur            w4, [x1, #7]
    // 0xa45bcc: DecompressPointer r4
    //     0xa45bcc: add             x4, x4, HEAP, lsl #32
    // 0xa45bd0: stur            x4, [fp, #-8]
    // 0xa45bd4: LoadField: r2 = r4->field_7
    //     0xa45bd4: ldur            w2, [x4, #7]
    // 0xa45bd8: DecompressPointer r2
    //     0xa45bd8: add             x2, x2, HEAP, lsl #32
    // 0xa45bdc: mov             x0, x3
    // 0xa45be0: r1 = Null
    //     0xa45be0: mov             x1, NULL
    // 0xa45be4: cmp             w2, NULL
    // 0xa45be8: b.eq            #0xa45c08
    // 0xa45bec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa45bec: ldur            w4, [x2, #0x17]
    // 0xa45bf0: DecompressPointer r4
    //     0xa45bf0: add             x4, x4, HEAP, lsl #32
    // 0xa45bf4: r8 = X0
    //     0xa45bf4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa45bf8: LoadField: r9 = r4->field_7
    //     0xa45bf8: ldur            x9, [x4, #7]
    // 0xa45bfc: r3 = Null
    //     0xa45bfc: add             x3, PP, #0x57, lsl #12  ; [pp+0x57d48] Null
    //     0xa45c00: ldr             x3, [x3, #0xd48]
    // 0xa45c04: blr             x9
    // 0xa45c08: ldur            x0, [fp, #-8]
    // 0xa45c0c: LoadField: r1 = r0->field_b
    //     0xa45c0c: ldur            w1, [x0, #0xb]
    // 0xa45c10: LoadField: r2 = r0->field_f
    //     0xa45c10: ldur            w2, [x0, #0xf]
    // 0xa45c14: DecompressPointer r2
    //     0xa45c14: add             x2, x2, HEAP, lsl #32
    // 0xa45c18: LoadField: r3 = r2->field_b
    //     0xa45c18: ldur            w3, [x2, #0xb]
    // 0xa45c1c: r2 = LoadInt32Instr(r1)
    //     0xa45c1c: sbfx            x2, x1, #1, #0x1f
    // 0xa45c20: stur            x2, [fp, #-0x18]
    // 0xa45c24: r1 = LoadInt32Instr(r3)
    //     0xa45c24: sbfx            x1, x3, #1, #0x1f
    // 0xa45c28: cmp             x2, x1
    // 0xa45c2c: b.ne            #0xa45c38
    // 0xa45c30: mov             x1, x0
    // 0xa45c34: r0 = _growToNextCapacity()
    //     0xa45c34: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa45c38: ldur            x0, [fp, #-8]
    // 0xa45c3c: ldur            x2, [fp, #-0x18]
    // 0xa45c40: add             x1, x2, #1
    // 0xa45c44: lsl             x3, x1, #1
    // 0xa45c48: StoreField: r0->field_b = r3
    //     0xa45c48: stur            w3, [x0, #0xb]
    // 0xa45c4c: LoadField: r1 = r0->field_f
    //     0xa45c4c: ldur            w1, [x0, #0xf]
    // 0xa45c50: DecompressPointer r1
    //     0xa45c50: add             x1, x1, HEAP, lsl #32
    // 0xa45c54: ldur            x0, [fp, #-0x10]
    // 0xa45c58: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa45c58: add             x25, x1, x2, lsl #2
    //     0xa45c5c: add             x25, x25, #0xf
    //     0xa45c60: str             w0, [x25]
    //     0xa45c64: tbz             w0, #0, #0xa45c80
    //     0xa45c68: ldurb           w16, [x1, #-1]
    //     0xa45c6c: ldurb           w17, [x0, #-1]
    //     0xa45c70: and             x16, x17, x16, lsr #2
    //     0xa45c74: tst             x16, HEAP, lsr #32
    //     0xa45c78: b.eq            #0xa45c80
    //     0xa45c7c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa45c80: b               #0xa45ca8
    // 0xa45c84: LoadField: r4 = r1->field_b
    //     0xa45c84: ldur            w4, [x1, #0xb]
    // 0xa45c88: DecompressPointer r4
    //     0xa45c88: add             x4, x4, HEAP, lsl #32
    // 0xa45c8c: mov             x3, x0
    // 0xa45c90: stur            x4, [fp, #-8]
    // 0xa45c94: r0 = _cacheKey()
    //     0xa45c94: bl              #0x7d5420  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::_cacheKey
    // 0xa45c98: ldur            x1, [fp, #-8]
    // 0xa45c9c: mov             x2, x0
    // 0xa45ca0: ldur            x3, [fp, #-0x10]
    // 0xa45ca4: r0 = []=()
    //     0xa45ca4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa45ca8: r0 = Null
    //     0xa45ca8: mov             x0, NULL
    // 0xa45cac: LeaveFrame
    //     0xa45cac: mov             SP, fp
    //     0xa45cb0: ldp             fp, lr, [SP], #0x10
    // 0xa45cb4: ret
    //     0xa45cb4: ret             
    // 0xa45cb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa45cb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45cbc: b               #0xa45bb8
  }
  _ PaintCache(/* No info */) {
    // ** addr: 0xa4a1f8, size: 0x9c
    // 0xa4a1f8: EnterFrame
    //     0xa4a1f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4a1fc: mov             fp, SP
    // 0xa4a200: AllocStack(0x18)
    //     0xa4a200: sub             SP, SP, #0x18
    // 0xa4a204: SetupParameters(PaintCache this /* r1 => r0, fp-0x8 */)
    //     0xa4a204: mov             x0, x1
    //     0xa4a208: stur            x1, [fp, #-8]
    // 0xa4a20c: CheckStackOverflow
    //     0xa4a20c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4a210: cmp             SP, x16
    //     0xa4a214: b.ls            #0xa4a28c
    // 0xa4a218: r1 = <Paint>
    //     0xa4a218: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d678] TypeArguments: <Paint>
    //     0xa4a21c: ldr             x1, [x1, #0x678]
    // 0xa4a220: r2 = 0
    //     0xa4a220: movz            x2, #0
    // 0xa4a224: r0 = _GrowableList()
    //     0xa4a224: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa4a228: ldur            x1, [fp, #-8]
    // 0xa4a22c: StoreField: r1->field_7 = r0
    //     0xa4a22c: stur            w0, [x1, #7]
    //     0xa4a230: ldurb           w16, [x1, #-1]
    //     0xa4a234: ldurb           w17, [x0, #-1]
    //     0xa4a238: and             x16, x17, x16, lsr #2
    //     0xa4a23c: tst             x16, HEAP, lsr #32
    //     0xa4a240: b.eq            #0xa4a248
    //     0xa4a244: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa4a248: r16 = <String, Paint>
    //     0xa4a248: add             x16, PP, #0x57, lsl #12  ; [pp+0x57dc8] TypeArguments: <String, Paint>
    //     0xa4a24c: ldr             x16, [x16, #0xdc8]
    // 0xa4a250: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa4a254: stp             lr, x16, [SP]
    // 0xa4a258: r0 = Map._fromLiteral()
    //     0xa4a258: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa4a25c: ldur            x1, [fp, #-8]
    // 0xa4a260: StoreField: r1->field_b = r0
    //     0xa4a260: stur            w0, [x1, #0xb]
    //     0xa4a264: ldurb           w16, [x1, #-1]
    //     0xa4a268: ldurb           w17, [x0, #-1]
    //     0xa4a26c: and             x16, x17, x16, lsr #2
    //     0xa4a270: tst             x16, HEAP, lsr #32
    //     0xa4a274: b.eq            #0xa4a27c
    //     0xa4a278: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa4a27c: r0 = Null
    //     0xa4a27c: mov             x0, NULL
    // 0xa4a280: LeaveFrame
    //     0xa4a280: mov             SP, fp
    //     0xa4a284: ldp             fp, lr, [SP], #0x10
    // 0xa4a288: ret
    //     0xa4a288: ret             
    // 0xa4a28c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a28c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a290: b               #0xa4a218
  }
}
