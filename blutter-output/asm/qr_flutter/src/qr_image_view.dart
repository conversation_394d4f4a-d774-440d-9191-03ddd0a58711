// lib: , url: package:qr_flutter/src/qr_image_view.dart

// class id: 1051072, size: 0x8
class :: {
}

// class id: 4101, size: 0x1c, field offset: 0x14
class _QrImageViewState extends State<dynamic> {

  late QrValidationResult _validationResult; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xa445bc, size: 0xfc
    // 0xa445bc: EnterFrame
    //     0xa445bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa445c0: mov             fp, SP
    // 0xa445c4: AllocStack(0x10)
    //     0xa445c4: sub             SP, SP, #0x10
    // 0xa445c8: SetupParameters(_QrImageViewState this /* r1 => r1, fp-0x8 */)
    //     0xa445c8: stur            x1, [fp, #-8]
    // 0xa445cc: CheckStackOverflow
    //     0xa445cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa445d0: cmp             SP, x16
    //     0xa445d4: b.ls            #0xa446ac
    // 0xa445d8: r1 = 1
    //     0xa445d8: movz            x1, #0x1
    // 0xa445dc: r0 = AllocateContext()
    //     0xa445dc: bl              #0xec126c  ; AllocateContextStub
    // 0xa445e0: mov             x2, x0
    // 0xa445e4: ldur            x0, [fp, #-8]
    // 0xa445e8: stur            x2, [fp, #-0x10]
    // 0xa445ec: StoreField: r2->field_f = r0
    //     0xa445ec: stur            w0, [x2, #0xf]
    // 0xa445f0: LoadField: r1 = r0->field_b
    //     0xa445f0: ldur            w1, [x0, #0xb]
    // 0xa445f4: DecompressPointer r1
    //     0xa445f4: add             x1, x1, HEAP, lsl #32
    // 0xa445f8: cmp             w1, NULL
    // 0xa445fc: b.eq            #0xa446b4
    // 0xa44600: LoadField: r3 = r1->field_b
    //     0xa44600: ldur            w3, [x1, #0xb]
    // 0xa44604: DecompressPointer r3
    //     0xa44604: add             x3, x3, HEAP, lsl #32
    // 0xa44608: mov             x1, x3
    // 0xa4460c: r0 = validate()
    //     0xa4460c: bl              #0xa446dc  ; [package:qr_flutter/src/validator.dart] QrValidator::validate
    // 0xa44610: mov             x2, x0
    // 0xa44614: ldur            x1, [fp, #-8]
    // 0xa44618: ArrayStore: r1[0] = r0  ; List_4
    //     0xa44618: stur            w0, [x1, #0x17]
    //     0xa4461c: ldurb           w16, [x1, #-1]
    //     0xa44620: ldurb           w17, [x0, #-1]
    //     0xa44624: and             x16, x17, x16, lsr #2
    //     0xa44628: tst             x16, HEAP, lsr #32
    //     0xa4462c: b.eq            #0xa44634
    //     0xa44630: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa44634: LoadField: r0 = r2->field_7
    //     0xa44634: ldur            w0, [x2, #7]
    // 0xa44638: DecompressPointer r0
    //     0xa44638: add             x0, x0, HEAP, lsl #32
    // 0xa4463c: r16 = Instance_QrValidationStatus
    //     0xa4463c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ce8] Obj!QrValidationStatus@e2e261
    //     0xa44640: ldr             x16, [x16, #0xce8]
    // 0xa44644: cmp             w0, w16
    // 0xa44648: b.ne            #0xa44658
    // 0xa4464c: LoadField: r0 = r2->field_b
    //     0xa4464c: ldur            w0, [x2, #0xb]
    // 0xa44650: DecompressPointer r0
    //     0xa44650: add             x0, x0, HEAP, lsl #32
    // 0xa44654: b               #0xa4465c
    // 0xa44658: r0 = Null
    //     0xa44658: mov             x0, NULL
    // 0xa4465c: StoreField: r1->field_13 = r0
    //     0xa4465c: stur            w0, [x1, #0x13]
    //     0xa44660: ldurb           w16, [x1, #-1]
    //     0xa44664: ldurb           w17, [x0, #-1]
    //     0xa44668: and             x16, x17, x16, lsr #2
    //     0xa4466c: tst             x16, HEAP, lsr #32
    //     0xa44670: b.eq            #0xa44678
    //     0xa44674: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa44678: ldur            x2, [fp, #-0x10]
    // 0xa4467c: r1 = Function '<anonymous closure>':.
    //     0xa4467c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57cf0] AnonymousClosure: (0xa455a8), in [package:qr_flutter/src/qr_image_view.dart] _QrImageViewState::build (0xa445bc)
    //     0xa44680: ldr             x1, [x1, #0xcf0]
    // 0xa44684: r0 = AllocateClosure()
    //     0xa44684: bl              #0xec1630  ; AllocateClosureStub
    // 0xa44688: r1 = <BoxConstraints>
    //     0xa44688: add             x1, PP, #0x37, lsl #12  ; [pp+0x37fa8] TypeArguments: <BoxConstraints>
    //     0xa4468c: ldr             x1, [x1, #0xfa8]
    // 0xa44690: stur            x0, [fp, #-8]
    // 0xa44694: r0 = LayoutBuilder()
    //     0xa44694: bl              #0x9f1460  ; AllocateLayoutBuilderStub -> LayoutBuilder (size=0x14)
    // 0xa44698: ldur            x1, [fp, #-8]
    // 0xa4469c: StoreField: r0->field_f = r1
    //     0xa4469c: stur            w1, [x0, #0xf]
    // 0xa446a0: LeaveFrame
    //     0xa446a0: mov             SP, fp
    //     0xa446a4: ldp             fp, lr, [SP], #0x10
    // 0xa446a8: ret
    //     0xa446a8: ret             
    // 0xa446ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa446ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa446b0: b               #0xa445d8
    // 0xa446b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa446b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, BoxConstraints) {
    // ** addr: 0xa455a8, size: 0xa8
    // 0xa455a8: EnterFrame
    //     0xa455a8: stp             fp, lr, [SP, #-0x10]!
    //     0xa455ac: mov             fp, SP
    // 0xa455b0: ldr             x0, [fp, #0x20]
    // 0xa455b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa455b4: ldur            w1, [x0, #0x17]
    // 0xa455b8: DecompressPointer r1
    //     0xa455b8: add             x1, x1, HEAP, lsl #32
    // 0xa455bc: CheckStackOverflow
    //     0xa455bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa455c0: cmp             SP, x16
    //     0xa455c4: b.ls            #0xa45638
    // 0xa455c8: LoadField: r0 = r1->field_f
    //     0xa455c8: ldur            w0, [x1, #0xf]
    // 0xa455cc: DecompressPointer r0
    //     0xa455cc: add             x0, x0, HEAP, lsl #32
    // 0xa455d0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa455d0: ldur            w1, [x0, #0x17]
    // 0xa455d4: DecompressPointer r1
    //     0xa455d4: add             x1, x1, HEAP, lsl #32
    // 0xa455d8: r16 = Sentinel
    //     0xa455d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa455dc: cmp             w1, w16
    // 0xa455e0: b.eq            #0xa45640
    // 0xa455e4: LoadField: r2 = r1->field_7
    //     0xa455e4: ldur            w2, [x1, #7]
    // 0xa455e8: DecompressPointer r2
    //     0xa455e8: add             x2, x2, HEAP, lsl #32
    // 0xa455ec: r16 = Instance_QrValidationStatus
    //     0xa455ec: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ce8] Obj!QrValidationStatus@e2e261
    //     0xa455f0: ldr             x16, [x16, #0xce8]
    // 0xa455f4: cmp             w2, w16
    // 0xa455f8: b.eq            #0xa45610
    // 0xa455fc: mov             x1, x0
    // 0xa45600: r0 = _errorWidget()
    //     0xa45600: bl              #0xa4a2ac  ; [package:qr_flutter/src/qr_image_view.dart] _QrImageViewState::_errorWidget
    // 0xa45604: LeaveFrame
    //     0xa45604: mov             SP, fp
    //     0xa45608: ldp             fp, lr, [SP], #0x10
    // 0xa4560c: ret
    //     0xa4560c: ret             
    // 0xa45610: LoadField: r1 = r0->field_b
    //     0xa45610: ldur            w1, [x0, #0xb]
    // 0xa45614: DecompressPointer r1
    //     0xa45614: add             x1, x1, HEAP, lsl #32
    // 0xa45618: cmp             w1, NULL
    // 0xa4561c: b.eq            #0xa4564c
    // 0xa45620: mov             x1, x0
    // 0xa45624: r2 = Null
    //     0xa45624: mov             x2, NULL
    // 0xa45628: r0 = _qrWidget()
    //     0xa45628: bl              #0xa45650  ; [package:qr_flutter/src/qr_image_view.dart] _QrImageViewState::_qrWidget
    // 0xa4562c: LeaveFrame
    //     0xa4562c: mov             SP, fp
    //     0xa45630: ldp             fp, lr, [SP], #0x10
    // 0xa45634: ret
    //     0xa45634: ret             
    // 0xa45638: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa45638: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4563c: b               #0xa455c8
    // 0xa45640: r9 = _validationResult
    //     0xa45640: add             x9, PP, #0x57, lsl #12  ; [pp+0x57cf8] Field <_QrImageViewState@2039394010._validationResult@2039394010>: late (offset: 0x18)
    //     0xa45644: ldr             x9, [x9, #0xcf8]
    // 0xa45648: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa45648: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4564c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4564c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _qrWidget(/* No info */) {
    // ** addr: 0xa45650, size: 0xf4
    // 0xa45650: EnterFrame
    //     0xa45650: stp             fp, lr, [SP, #-0x10]!
    //     0xa45654: mov             fp, SP
    // 0xa45658: AllocStack(0x18)
    //     0xa45658: sub             SP, SP, #0x18
    // 0xa4565c: SetupParameters(_QrImageViewState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa4565c: stur            x1, [fp, #-0x10]
    //     0xa45660: stur            x2, [fp, #-0x18]
    // 0xa45664: CheckStackOverflow
    //     0xa45664: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa45668: cmp             SP, x16
    //     0xa4566c: b.ls            #0xa45730
    // 0xa45670: LoadField: r3 = r1->field_13
    //     0xa45670: ldur            w3, [x1, #0x13]
    // 0xa45674: DecompressPointer r3
    //     0xa45674: add             x3, x3, HEAP, lsl #32
    // 0xa45678: stur            x3, [fp, #-8]
    // 0xa4567c: cmp             w3, NULL
    // 0xa45680: b.eq            #0xa45738
    // 0xa45684: LoadField: r0 = r1->field_b
    //     0xa45684: ldur            w0, [x1, #0xb]
    // 0xa45688: DecompressPointer r0
    //     0xa45688: add             x0, x0, HEAP, lsl #32
    // 0xa4568c: cmp             w0, NULL
    // 0xa45690: b.eq            #0xa4573c
    // 0xa45694: r0 = QrPainter()
    //     0xa45694: bl              #0xa4a2a0  ; AllocateQrPainterStub -> QrPainter (size=0x4c)
    // 0xa45698: mov             x1, x0
    // 0xa4569c: ldur            x2, [fp, #-0x18]
    // 0xa456a0: ldur            x3, [fp, #-8]
    // 0xa456a4: stur            x0, [fp, #-8]
    // 0xa456a8: r0 = QrPainter.withQr()
    //     0xa456a8: bl              #0xa45750  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::QrPainter.withQr
    // 0xa456ac: ldur            x0, [fp, #-0x10]
    // 0xa456b0: LoadField: r1 = r0->field_b
    //     0xa456b0: ldur            w1, [x0, #0xb]
    // 0xa456b4: DecompressPointer r1
    //     0xa456b4: add             x1, x1, HEAP, lsl #32
    // 0xa456b8: cmp             w1, NULL
    // 0xa456bc: b.eq            #0xa45740
    // 0xa456c0: r0 = CustomPaint()
    //     0xa456c0: bl              #0x9d4558  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0xa456c4: mov             x1, x0
    // 0xa456c8: ldur            x0, [fp, #-8]
    // 0xa456cc: stur            x1, [fp, #-0x10]
    // 0xa456d0: StoreField: r1->field_f = r0
    //     0xa456d0: stur            w0, [x1, #0xf]
    // 0xa456d4: r0 = Instance_Size
    //     0xa456d4: add             x0, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0xa456d8: ldr             x0, [x0, #0xa20]
    // 0xa456dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa456dc: stur            w0, [x1, #0x17]
    // 0xa456e0: r0 = false
    //     0xa456e0: add             x0, NULL, #0x30  ; false
    // 0xa456e4: StoreField: r1->field_1b = r0
    //     0xa456e4: stur            w0, [x1, #0x1b]
    // 0xa456e8: StoreField: r1->field_1f = r0
    //     0xa456e8: stur            w0, [x1, #0x1f]
    // 0xa456ec: r0 = _QrContentView()
    //     0xa456ec: bl              #0xa45744  ; Allocate_QrContentViewStub -> _QrContentView (size=0x24)
    // 0xa456f0: d0 = 250.000000
    //     0xa456f0: add             x17, PP, #0x47, lsl #12  ; [pp+0x47b80] IMM: double(250) from 0x406f400000000000
    //     0xa456f4: ldr             d0, [x17, #0xb80]
    // 0xa456f8: StoreField: r0->field_b = d0
    //     0xa456f8: stur            d0, [x0, #0xb]
    // 0xa456fc: ldur            x1, [fp, #-0x10]
    // 0xa45700: StoreField: r0->field_1b = r1
    //     0xa45700: stur            w1, [x0, #0x1b]
    // 0xa45704: r1 = Instance_Color
    //     0xa45704: ldr             x1, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xa45708: StoreField: r0->field_13 = r1
    //     0xa45708: stur            w1, [x0, #0x13]
    // 0xa4570c: r1 = Instance_EdgeInsets
    //     0xa4570c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36068] Obj!EdgeInsets@e12401
    //     0xa45710: ldr             x1, [x1, #0x68]
    // 0xa45714: ArrayStore: r0[0] = r1  ; List_4
    //     0xa45714: stur            w1, [x0, #0x17]
    // 0xa45718: r1 = "qr code"
    //     0xa45718: add             x1, PP, #0x47, lsl #12  ; [pp+0x47b68] "qr code"
    //     0xa4571c: ldr             x1, [x1, #0xb68]
    // 0xa45720: StoreField: r0->field_1f = r1
    //     0xa45720: stur            w1, [x0, #0x1f]
    // 0xa45724: LeaveFrame
    //     0xa45724: mov             SP, fp
    //     0xa45728: ldp             fp, lr, [SP], #0x10
    // 0xa4572c: ret
    //     0xa4572c: ret             
    // 0xa45730: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa45730: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45734: b               #0xa45670
    // 0xa45738: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa45738: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4573c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4573c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa45740: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa45740: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _errorWidget(/* No info */) {
    // ** addr: 0xa4a2ac, size: 0xa8
    // 0xa4a2ac: EnterFrame
    //     0xa4a2ac: stp             fp, lr, [SP, #-0x10]!
    //     0xa4a2b0: mov             fp, SP
    // 0xa4a2b4: AllocStack(0x10)
    //     0xa4a2b4: sub             SP, SP, #0x10
    // 0xa4a2b8: SetupParameters(_QrImageViewState this /* r1 => r1, fp-0x8 */)
    //     0xa4a2b8: stur            x1, [fp, #-8]
    // 0xa4a2bc: CheckStackOverflow
    //     0xa4a2bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4a2c0: cmp             SP, x16
    //     0xa4a2c4: b.ls            #0xa4a344
    // 0xa4a2c8: LoadField: r0 = r1->field_b
    //     0xa4a2c8: ldur            w0, [x1, #0xb]
    // 0xa4a2cc: DecompressPointer r0
    //     0xa4a2cc: add             x0, x0, HEAP, lsl #32
    // 0xa4a2d0: cmp             w0, NULL
    // 0xa4a2d4: b.eq            #0xa4a34c
    // 0xa4a2d8: r0 = Container()
    //     0xa4a2d8: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4a2dc: mov             x1, x0
    // 0xa4a2e0: stur            x0, [fp, #-0x10]
    // 0xa4a2e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa4a2e4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa4a2e8: r0 = Container()
    //     0xa4a2e8: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4a2ec: ldur            x0, [fp, #-8]
    // 0xa4a2f0: LoadField: r1 = r0->field_b
    //     0xa4a2f0: ldur            w1, [x0, #0xb]
    // 0xa4a2f4: DecompressPointer r1
    //     0xa4a2f4: add             x1, x1, HEAP, lsl #32
    // 0xa4a2f8: cmp             w1, NULL
    // 0xa4a2fc: b.eq            #0xa4a350
    // 0xa4a300: r0 = _QrContentView()
    //     0xa4a300: bl              #0xa45744  ; Allocate_QrContentViewStub -> _QrContentView (size=0x24)
    // 0xa4a304: d0 = 250.000000
    //     0xa4a304: add             x17, PP, #0x47, lsl #12  ; [pp+0x47b80] IMM: double(250) from 0x406f400000000000
    //     0xa4a308: ldr             d0, [x17, #0xb80]
    // 0xa4a30c: StoreField: r0->field_b = d0
    //     0xa4a30c: stur            d0, [x0, #0xb]
    // 0xa4a310: ldur            x1, [fp, #-0x10]
    // 0xa4a314: StoreField: r0->field_1b = r1
    //     0xa4a314: stur            w1, [x0, #0x1b]
    // 0xa4a318: r1 = Instance_Color
    //     0xa4a318: ldr             x1, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xa4a31c: StoreField: r0->field_13 = r1
    //     0xa4a31c: stur            w1, [x0, #0x13]
    // 0xa4a320: r1 = Instance_EdgeInsets
    //     0xa4a320: add             x1, PP, #0x36, lsl #12  ; [pp+0x36068] Obj!EdgeInsets@e12401
    //     0xa4a324: ldr             x1, [x1, #0x68]
    // 0xa4a328: ArrayStore: r0[0] = r1  ; List_4
    //     0xa4a328: stur            w1, [x0, #0x17]
    // 0xa4a32c: r1 = "qr code"
    //     0xa4a32c: add             x1, PP, #0x47, lsl #12  ; [pp+0x47b68] "qr code"
    //     0xa4a330: ldr             x1, [x1, #0xb68]
    // 0xa4a334: StoreField: r0->field_1f = r1
    //     0xa4a334: stur            w1, [x0, #0x1f]
    // 0xa4a338: LeaveFrame
    //     0xa4a338: mov             SP, fp
    //     0xa4a33c: ldp             fp, lr, [SP], #0x10
    // 0xa4a340: ret
    //     0xa4a340: ret             
    // 0xa4a344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a348: b               #0xa4a2c8
    // 0xa4a34c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a34c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a350: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a350: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4702, size: 0x5c, field offset: 0xc
class QrImageView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94bb4, size: 0x2c
    // 0xa94bb4: EnterFrame
    //     0xa94bb4: stp             fp, lr, [SP, #-0x10]!
    //     0xa94bb8: mov             fp, SP
    // 0xa94bbc: mov             x0, x1
    // 0xa94bc0: r1 = <QrImageView>
    //     0xa94bc0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51538] TypeArguments: <QrImageView>
    //     0xa94bc4: ldr             x1, [x1, #0x538]
    // 0xa94bc8: r0 = _QrImageViewState()
    //     0xa94bc8: bl              #0xa94be0  ; Allocate_QrImageViewStateStub -> _QrImageViewState (size=0x1c)
    // 0xa94bcc: r1 = Sentinel
    //     0xa94bcc: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa94bd0: ArrayStore: r0[0] = r1  ; List_4
    //     0xa94bd0: stur            w1, [x0, #0x17]
    // 0xa94bd4: LeaveFrame
    //     0xa94bd4: mov             SP, fp
    //     0xa94bd8: ldp             fp, lr, [SP], #0x10
    // 0xa94bdc: ret
    //     0xa94bdc: ret             
  }
  _ QrImageView(/* No info */) {
    // ** addr: 0xb92e58, size: 0xac
    // 0xb92e58: r13 = Instance_EdgeInsets
    //     0xb92e58: add             x13, PP, #0x36, lsl #12  ; [pp+0x36068] Obj!EdgeInsets@e12401
    //     0xb92e5c: ldr             x13, [x13, #0x68]
    // 0xb92e60: r12 = Instance_Color
    //     0xb92e60: ldr             x12, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xb92e64: r11 = Instance_Color
    //     0xb92e64: ldr             x11, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0xb92e68: r10 = true
    //     0xb92e68: add             x10, NULL, #0x20  ; true
    // 0xb92e6c: r9 = Instance_QrEmbeddedImageStyle
    //     0xb92e6c: add             x9, PP, #0x47, lsl #12  ; [pp+0x47b60] Obj!QrEmbeddedImageStyle@e0c0f1
    //     0xb92e70: ldr             x9, [x9, #0xb60]
    // 0xb92e74: r8 = "qr code"
    //     0xb92e74: add             x8, PP, #0x47, lsl #12  ; [pp+0x47b68] "qr code"
    //     0xb92e78: ldr             x8, [x8, #0xb68]
    // 0xb92e7c: r7 = Instance_QrEyeStyle
    //     0xb92e7c: add             x7, PP, #0x47, lsl #12  ; [pp+0x47b70] Obj!QrEyeStyle@e0c141
    //     0xb92e80: ldr             x7, [x7, #0xb70]
    // 0xb92e84: r6 = Instance_QrDataModuleStyle
    //     0xb92e84: add             x6, PP, #0x47, lsl #12  ; [pp+0x47b78] Obj!QrDataModuleStyle@e0c121
    //     0xb92e88: ldr             x6, [x6, #0xb78]
    // 0xb92e8c: r5 = false
    //     0xb92e8c: add             x5, NULL, #0x30  ; false
    // 0xb92e90: d0 = 250.000000
    //     0xb92e90: add             x17, PP, #0x47, lsl #12  ; [pp+0x47b80] IMM: double(250) from 0x406f400000000000
    //     0xb92e94: ldr             d0, [x17, #0xb80]
    // 0xb92e98: r4 = -1
    //     0xb92e98: movn            x4, #0
    // 0xb92e9c: r3 = 1
    //     0xb92e9c: movz            x3, #0x1
    // 0xb92ea0: mov             x0, x2
    // 0xb92ea4: StoreField: r1->field_2f = d0
    //     0xb92ea4: stur            d0, [x1, #0x2f]
    // 0xb92ea8: StoreField: r1->field_2b = r13
    //     0xb92ea8: stur            w13, [x1, #0x2b]
    // 0xb92eac: StoreField: r1->field_f = r12
    //     0xb92eac: stur            w12, [x1, #0xf]
    // 0xb92eb0: StoreField: r1->field_13 = r11
    //     0xb92eb0: stur            w11, [x1, #0x13]
    // 0xb92eb4: StoreField: r1->field_1b = r4
    //     0xb92eb4: stur            x4, [x1, #0x1b]
    // 0xb92eb8: StoreField: r1->field_23 = r3
    //     0xb92eb8: stur            x3, [x1, #0x23]
    // 0xb92ebc: StoreField: r1->field_3b = r10
    //     0xb92ebc: stur            w10, [x1, #0x3b]
    // 0xb92ec0: StoreField: r1->field_3f = r10
    //     0xb92ec0: stur            w10, [x1, #0x3f]
    // 0xb92ec4: StoreField: r1->field_47 = r9
    //     0xb92ec4: stur            w9, [x1, #0x47]
    // 0xb92ec8: StoreField: r1->field_4f = r8
    //     0xb92ec8: stur            w8, [x1, #0x4f]
    // 0xb92ecc: StoreField: r1->field_53 = r7
    //     0xb92ecc: stur            w7, [x1, #0x53]
    // 0xb92ed0: StoreField: r1->field_57 = r6
    //     0xb92ed0: stur            w6, [x1, #0x57]
    // 0xb92ed4: StoreField: r1->field_4b = r5
    //     0xb92ed4: stur            w5, [x1, #0x4b]
    // 0xb92ed8: StoreField: r1->field_b = r0
    //     0xb92ed8: stur            w0, [x1, #0xb]
    //     0xb92edc: ldurb           w16, [x1, #-1]
    //     0xb92ee0: ldurb           w17, [x0, #-1]
    //     0xb92ee4: and             x16, x17, x16, lsr #2
    //     0xb92ee8: tst             x16, HEAP, lsr #32
    //     0xb92eec: b.eq            #0xb92efc
    //     0xb92ef0: str             lr, [SP, #-8]!
    //     0xb92ef4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    //     0xb92ef8: ldr             lr, [SP], #8
    // 0xb92efc: r0 = Null
    //     0xb92efc: mov             x0, NULL
    // 0xb92f00: ret
    //     0xb92f00: ret             
  }
}

// class id: 4921, size: 0x24, field offset: 0xc
//   const constructor, 
class _QrContentView extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbb7110, size: 0xbc
    // 0xbb7110: EnterFrame
    //     0xbb7110: stp             fp, lr, [SP, #-0x10]!
    //     0xbb7114: mov             fp, SP
    // 0xbb7118: AllocStack(0x30)
    //     0xbb7118: sub             SP, SP, #0x30
    // 0xbb711c: CheckStackOverflow
    //     0xbb711c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb7120: cmp             SP, x16
    //     0xbb7124: b.ls            #0xbb71c4
    // 0xbb7128: LoadField: r0 = r1->field_1b
    //     0xbb7128: ldur            w0, [x1, #0x1b]
    // 0xbb712c: DecompressPointer r0
    //     0xbb712c: add             x0, x0, HEAP, lsl #32
    // 0xbb7130: stur            x0, [fp, #-8]
    // 0xbb7134: r0 = Padding()
    //     0xbb7134: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb7138: mov             x1, x0
    // 0xbb713c: r0 = Instance_EdgeInsets
    //     0xbb713c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36068] Obj!EdgeInsets@e12401
    //     0xbb7140: ldr             x0, [x0, #0x68]
    // 0xbb7144: stur            x1, [fp, #-0x10]
    // 0xbb7148: StoreField: r1->field_f = r0
    //     0xbb7148: stur            w0, [x1, #0xf]
    // 0xbb714c: ldur            x0, [fp, #-8]
    // 0xbb7150: StoreField: r1->field_b = r0
    //     0xbb7150: stur            w0, [x1, #0xb]
    // 0xbb7154: r0 = Container()
    //     0xbb7154: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb7158: stur            x0, [fp, #-8]
    // 0xbb715c: r16 = 250.000000
    //     0xbb715c: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b200] 250
    //     0xbb7160: ldr             x16, [x16, #0x200]
    // 0xbb7164: r30 = 250.000000
    //     0xbb7164: add             lr, PP, #0x5b, lsl #12  ; [pp+0x5b200] 250
    //     0xbb7168: ldr             lr, [lr, #0x200]
    // 0xbb716c: stp             lr, x16, [SP, #0x10]
    // 0xbb7170: r16 = Instance_Color
    //     0xbb7170: ldr             x16, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xbb7174: ldur            lr, [fp, #-0x10]
    // 0xbb7178: stp             lr, x16, [SP]
    // 0xbb717c: mov             x1, x0
    // 0xbb7180: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xbb7180: add             x4, PP, #0x5b, lsl #12  ; [pp+0x5b208] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbb7184: ldr             x4, [x4, #0x208]
    // 0xbb7188: r0 = Container()
    //     0xbb7188: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb718c: r0 = Semantics()
    //     0xbb718c: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xbb7190: stur            x0, [fp, #-0x10]
    // 0xbb7194: r16 = "qr code"
    //     0xbb7194: add             x16, PP, #0x47, lsl #12  ; [pp+0x47b68] "qr code"
    //     0xbb7198: ldr             x16, [x16, #0xb68]
    // 0xbb719c: ldur            lr, [fp, #-8]
    // 0xbb71a0: stp             lr, x16, [SP]
    // 0xbb71a4: mov             x1, x0
    // 0xbb71a8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, label, 0x1, null]
    //     0xbb71a8: add             x4, PP, #0x3a, lsl #12  ; [pp+0x3a100] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "label", 0x1, Null]
    //     0xbb71ac: ldr             x4, [x4, #0x100]
    // 0xbb71b0: r0 = Semantics()
    //     0xbb71b0: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xbb71b4: ldur            x0, [fp, #-0x10]
    // 0xbb71b8: LeaveFrame
    //     0xbb71b8: mov             SP, fp
    //     0xbb71bc: ldp             fp, lr, [SP], #0x10
    // 0xbb71c0: ret
    //     0xbb71c0: ret             
    // 0xbb71c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb71c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb71c8: b               #0xbb7128
  }
}
