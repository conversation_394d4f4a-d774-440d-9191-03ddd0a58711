// lib: , url: package:qr_flutter/src/qr_painter.dart

// class id: 1051073, size: 0x8
class :: {
}

// class id: 533, size: 0x2c, field offset: 0x8
class _PaintMetrics extends Object {

  late final double _inset; // offset: 0x28
  late final double _pixelSize; // offset: 0x20
  late final double _innerContentSize; // offset: 0x24

  _ _PaintMetrics(/* No info */) {
    // ** addr: 0x7d5a44, size: 0x4c
    // 0x7d5a44: EnterFrame
    //     0x7d5a44: stp             fp, lr, [SP, #-0x10]!
    //     0x7d5a48: mov             fp, SP
    // 0x7d5a4c: r0 = Sentinel
    //     0x7d5a4c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d5a50: CheckStackOverflow
    //     0x7d5a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d5a54: cmp             SP, x16
    //     0x7d5a58: b.ls            #0x7d5a88
    // 0x7d5a5c: StoreField: r1->field_1f = r0
    //     0x7d5a5c: stur            w0, [x1, #0x1f]
    // 0x7d5a60: StoreField: r1->field_23 = r0
    //     0x7d5a60: stur            w0, [x1, #0x23]
    // 0x7d5a64: StoreField: r1->field_27 = r0
    //     0x7d5a64: stur            w0, [x1, #0x27]
    // 0x7d5a68: StoreField: r1->field_f = d0
    //     0x7d5a68: stur            d0, [x1, #0xf]
    // 0x7d5a6c: ArrayStore: r1[0] = rZR  ; List_8
    //     0x7d5a6c: stur            xzr, [x1, #0x17]
    // 0x7d5a70: StoreField: r1->field_7 = r2
    //     0x7d5a70: stur            x2, [x1, #7]
    // 0x7d5a74: r0 = _calculateMetrics()
    //     0x7d5a74: bl              #0x7d5a90  ; [package:qr_flutter/src/qr_painter.dart] _PaintMetrics::_calculateMetrics
    // 0x7d5a78: r0 = Null
    //     0x7d5a78: mov             x0, NULL
    // 0x7d5a7c: LeaveFrame
    //     0x7d5a7c: mov             SP, fp
    //     0x7d5a80: ldp             fp, lr, [SP], #0x10
    // 0x7d5a84: ret
    //     0x7d5a84: ret             
    // 0x7d5a88: r0 = StackOverflowSharedWithFPURegs()
    //     0x7d5a88: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7d5a8c: b               #0x7d5a5c
  }
  _ _calculateMetrics(/* No info */) {
    // ** addr: 0x7d5a90, size: 0x290
    // 0x7d5a90: EnterFrame
    //     0x7d5a90: stp             fp, lr, [SP, #-0x10]!
    //     0x7d5a94: mov             fp, SP
    // 0x7d5a98: AllocStack(0x30)
    //     0x7d5a98: sub             SP, SP, #0x30
    // 0x7d5a9c: d1 = 2.000000
    //     0x7d5a9c: fmov            d1, #2.00000000
    // 0x7d5aa0: d0 = 0.000000
    //     0x7d5aa0: eor             v0.16b, v0.16b, v0.16b
    // 0x7d5aa4: stur            x1, [fp, #-8]
    // 0x7d5aa8: CheckStackOverflow
    //     0x7d5aa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d5aac: cmp             SP, x16
    //     0x7d5ab0: b.ls            #0x7d5cc0
    // 0x7d5ab4: LoadField: r0 = r1->field_7
    //     0x7d5ab4: ldur            x0, [x1, #7]
    // 0x7d5ab8: sub             x2, x0, #1
    // 0x7d5abc: scvtf           d2, x2
    // 0x7d5ac0: fmul            d3, d2, d0
    // 0x7d5ac4: stur            d3, [fp, #-0x20]
    // 0x7d5ac8: LoadField: d2 = r1->field_f
    //     0x7d5ac8: ldur            d2, [x1, #0xf]
    // 0x7d5acc: stur            d2, [fp, #-0x18]
    // 0x7d5ad0: fsub            d0, d2, d3
    // 0x7d5ad4: scvtf           d4, x0
    // 0x7d5ad8: stur            d4, [fp, #-0x10]
    // 0x7d5adc: fdiv            d5, d0, d4
    // 0x7d5ae0: fmul            d0, d5, d1
    // 0x7d5ae4: stp             fp, lr, [SP, #-0x10]!
    // 0x7d5ae8: mov             fp, SP
    // 0x7d5aec: CallRuntime_LibcRound(double) -> double
    //     0x7d5aec: and             SP, SP, #0xfffffffffffffff0
    //     0x7d5af0: mov             sp, SP
    //     0x7d5af4: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x7d5af8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x7d5afc: blr             x16
    //     0x7d5b00: movz            x16, #0x8
    //     0x7d5b04: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x7d5b08: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x7d5b0c: sub             sp, x16, #1, lsl #12
    //     0x7d5b10: mov             SP, fp
    //     0x7d5b14: ldp             fp, lr, [SP], #0x10
    // 0x7d5b18: mov             v1.16b, v0.16b
    // 0x7d5b1c: d0 = 2.000000
    //     0x7d5b1c: fmov            d0, #2.00000000
    // 0x7d5b20: fdiv            d2, d1, d0
    // 0x7d5b24: ldur            x0, [fp, #-8]
    // 0x7d5b28: stur            d2, [fp, #-0x28]
    // 0x7d5b2c: LoadField: r1 = r0->field_1f
    //     0x7d5b2c: ldur            w1, [x0, #0x1f]
    // 0x7d5b30: DecompressPointer r1
    //     0x7d5b30: add             x1, x1, HEAP, lsl #32
    // 0x7d5b34: r16 = Sentinel
    //     0x7d5b34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d5b38: cmp             w1, w16
    // 0x7d5b3c: b.ne            #0x7d5b4c
    // 0x7d5b40: mov             x1, x0
    // 0x7d5b44: mov             v0.16b, v2.16b
    // 0x7d5b48: b               #0x7d5b64
    // 0x7d5b4c: r16 = "_pixelSize@2040312174"
    //     0x7d5b4c: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b1e8] "_pixelSize@2040312174"
    //     0x7d5b50: ldr             x16, [x16, #0x1e8]
    // 0x7d5b54: str             x16, [SP]
    // 0x7d5b58: r0 = _throwFieldAlreadyInitialized()
    //     0x7d5b58: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x7d5b5c: ldur            x1, [fp, #-8]
    // 0x7d5b60: ldur            d0, [fp, #-0x28]
    // 0x7d5b64: ldur            d1, [fp, #-0x20]
    // 0x7d5b68: ldur            d2, [fp, #-0x10]
    // 0x7d5b6c: r0 = inline_Allocate_Double()
    //     0x7d5b6c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x7d5b70: add             x0, x0, #0x10
    //     0x7d5b74: cmp             x2, x0
    //     0x7d5b78: b.ls            #0x7d5cc8
    //     0x7d5b7c: str             x0, [THR, #0x50]  ; THR::top
    //     0x7d5b80: sub             x0, x0, #0xf
    //     0x7d5b84: movz            x2, #0xe15c
    //     0x7d5b88: movk            x2, #0x3, lsl #16
    //     0x7d5b8c: stur            x2, [x0, #-1]
    // 0x7d5b90: StoreField: r0->field_7 = d0
    //     0x7d5b90: stur            d0, [x0, #7]
    // 0x7d5b94: StoreField: r1->field_1f = r0
    //     0x7d5b94: stur            w0, [x1, #0x1f]
    //     0x7d5b98: ldurb           w16, [x1, #-1]
    //     0x7d5b9c: ldurb           w17, [x0, #-1]
    //     0x7d5ba0: and             x16, x17, x16, lsr #2
    //     0x7d5ba4: tst             x16, HEAP, lsr #32
    //     0x7d5ba8: b.eq            #0x7d5bb0
    //     0x7d5bac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7d5bb0: fmul            d3, d0, d2
    // 0x7d5bb4: fadd            d0, d3, d1
    // 0x7d5bb8: stur            d0, [fp, #-0x10]
    // 0x7d5bbc: LoadField: r0 = r1->field_23
    //     0x7d5bbc: ldur            w0, [x1, #0x23]
    // 0x7d5bc0: DecompressPointer r0
    //     0x7d5bc0: add             x0, x0, HEAP, lsl #32
    // 0x7d5bc4: r16 = Sentinel
    //     0x7d5bc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d5bc8: cmp             w0, w16
    // 0x7d5bcc: b.eq            #0x7d5be8
    // 0x7d5bd0: r16 = "_innerContentSize@2040312174"
    //     0x7d5bd0: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b1f0] "_innerContentSize@2040312174"
    //     0x7d5bd4: ldr             x16, [x16, #0x1f0]
    // 0x7d5bd8: str             x16, [SP]
    // 0x7d5bdc: r0 = _throwFieldAlreadyInitialized()
    //     0x7d5bdc: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x7d5be0: ldur            x1, [fp, #-8]
    // 0x7d5be4: ldur            d0, [fp, #-0x10]
    // 0x7d5be8: ldur            d2, [fp, #-0x18]
    // 0x7d5bec: d1 = 2.000000
    //     0x7d5bec: fmov            d1, #2.00000000
    // 0x7d5bf0: r0 = inline_Allocate_Double()
    //     0x7d5bf0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x7d5bf4: add             x0, x0, #0x10
    //     0x7d5bf8: cmp             x2, x0
    //     0x7d5bfc: b.ls            #0x7d5ce8
    //     0x7d5c00: str             x0, [THR, #0x50]  ; THR::top
    //     0x7d5c04: sub             x0, x0, #0xf
    //     0x7d5c08: movz            x2, #0xe15c
    //     0x7d5c0c: movk            x2, #0x3, lsl #16
    //     0x7d5c10: stur            x2, [x0, #-1]
    // 0x7d5c14: StoreField: r0->field_7 = d0
    //     0x7d5c14: stur            d0, [x0, #7]
    // 0x7d5c18: StoreField: r1->field_23 = r0
    //     0x7d5c18: stur            w0, [x1, #0x23]
    //     0x7d5c1c: ldurb           w16, [x1, #-1]
    //     0x7d5c20: ldurb           w17, [x0, #-1]
    //     0x7d5c24: and             x16, x17, x16, lsr #2
    //     0x7d5c28: tst             x16, HEAP, lsr #32
    //     0x7d5c2c: b.eq            #0x7d5c34
    //     0x7d5c30: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7d5c34: fsub            d3, d2, d0
    // 0x7d5c38: fdiv            d0, d3, d1
    // 0x7d5c3c: stur            d0, [fp, #-0x10]
    // 0x7d5c40: LoadField: r0 = r1->field_27
    //     0x7d5c40: ldur            w0, [x1, #0x27]
    // 0x7d5c44: DecompressPointer r0
    //     0x7d5c44: add             x0, x0, HEAP, lsl #32
    // 0x7d5c48: r16 = Sentinel
    //     0x7d5c48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d5c4c: cmp             w0, w16
    // 0x7d5c50: b.eq            #0x7d5c6c
    // 0x7d5c54: r16 = "_inset@2040312174"
    //     0x7d5c54: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b1f8] "_inset@2040312174"
    //     0x7d5c58: ldr             x16, [x16, #0x1f8]
    // 0x7d5c5c: str             x16, [SP]
    // 0x7d5c60: r0 = _throwFieldAlreadyInitialized()
    //     0x7d5c60: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x7d5c64: ldur            x1, [fp, #-8]
    // 0x7d5c68: ldur            d0, [fp, #-0x10]
    // 0x7d5c6c: r0 = inline_Allocate_Double()
    //     0x7d5c6c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x7d5c70: add             x0, x0, #0x10
    //     0x7d5c74: cmp             x2, x0
    //     0x7d5c78: b.ls            #0x7d5d08
    //     0x7d5c7c: str             x0, [THR, #0x50]  ; THR::top
    //     0x7d5c80: sub             x0, x0, #0xf
    //     0x7d5c84: movz            x2, #0xe15c
    //     0x7d5c88: movk            x2, #0x3, lsl #16
    //     0x7d5c8c: stur            x2, [x0, #-1]
    // 0x7d5c90: StoreField: r0->field_7 = d0
    //     0x7d5c90: stur            d0, [x0, #7]
    // 0x7d5c94: StoreField: r1->field_27 = r0
    //     0x7d5c94: stur            w0, [x1, #0x27]
    //     0x7d5c98: ldurb           w16, [x1, #-1]
    //     0x7d5c9c: ldurb           w17, [x0, #-1]
    //     0x7d5ca0: and             x16, x17, x16, lsr #2
    //     0x7d5ca4: tst             x16, HEAP, lsr #32
    //     0x7d5ca8: b.eq            #0x7d5cb0
    //     0x7d5cac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7d5cb0: r0 = Null
    //     0x7d5cb0: mov             x0, NULL
    // 0x7d5cb4: LeaveFrame
    //     0x7d5cb4: mov             SP, fp
    //     0x7d5cb8: ldp             fp, lr, [SP], #0x10
    // 0x7d5cbc: ret
    //     0x7d5cbc: ret             
    // 0x7d5cc0: r0 = StackOverflowSharedWithFPURegs()
    //     0x7d5cc0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7d5cc4: b               #0x7d5ab4
    // 0x7d5cc8: stp             q1, q2, [SP, #-0x20]!
    // 0x7d5ccc: SaveReg d0
    //     0x7d5ccc: str             q0, [SP, #-0x10]!
    // 0x7d5cd0: SaveReg r1
    //     0x7d5cd0: str             x1, [SP, #-8]!
    // 0x7d5cd4: r0 = AllocateDouble()
    //     0x7d5cd4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7d5cd8: RestoreReg r1
    //     0x7d5cd8: ldr             x1, [SP], #8
    // 0x7d5cdc: RestoreReg d0
    //     0x7d5cdc: ldr             q0, [SP], #0x10
    // 0x7d5ce0: ldp             q1, q2, [SP], #0x20
    // 0x7d5ce4: b               #0x7d5b90
    // 0x7d5ce8: stp             q1, q2, [SP, #-0x20]!
    // 0x7d5cec: SaveReg d0
    //     0x7d5cec: str             q0, [SP, #-0x10]!
    // 0x7d5cf0: SaveReg r1
    //     0x7d5cf0: str             x1, [SP, #-8]!
    // 0x7d5cf4: r0 = AllocateDouble()
    //     0x7d5cf4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7d5cf8: RestoreReg r1
    //     0x7d5cf8: ldr             x1, [SP], #8
    // 0x7d5cfc: RestoreReg d0
    //     0x7d5cfc: ldr             q0, [SP], #0x10
    // 0x7d5d00: ldp             q1, q2, [SP], #0x20
    // 0x7d5d04: b               #0x7d5c14
    // 0x7d5d08: SaveReg d0
    //     0x7d5d08: str             q0, [SP, #-0x10]!
    // 0x7d5d0c: SaveReg r1
    //     0x7d5d0c: str             x1, [SP, #-8]!
    // 0x7d5d10: r0 = AllocateDouble()
    //     0x7d5d10: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7d5d14: RestoreReg r1
    //     0x7d5d14: ldr             x1, [SP], #8
    // 0x7d5d18: RestoreReg d0
    //     0x7d5d18: ldr             q0, [SP], #0x10
    // 0x7d5d1c: b               #0x7d5c90
  }
}

// class id: 5454, size: 0x4c, field offset: 0xc
class QrPainter extends CustomPainter {

  late final int _calcVersion; // offset: 0x44
  late QrImage _qrImage; // offset: 0x40

  _ paint(/* No info */) {
    // ** addr: 0x7d46e8, size: 0x670
    // 0x7d46e8: EnterFrame
    //     0x7d46e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7d46ec: mov             fp, SP
    // 0x7d46f0: AllocStack(0x108)
    //     0x7d46f0: sub             SP, SP, #0x108
    // 0x7d46f4: SetupParameters(QrPainter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x7d46f4: mov             x0, x3
    //     0x7d46f8: stur            x3, [fp, #-0x18]
    //     0x7d46fc: mov             x3, x2
    //     0x7d4700: stur            x2, [fp, #-0x10]
    //     0x7d4704: mov             x2, x1
    //     0x7d4708: stur            x1, [fp, #-8]
    // 0x7d470c: CheckStackOverflow
    //     0x7d470c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d4710: cmp             SP, x16
    //     0x7d4714: b.ls            #0x7d4d24
    // 0x7d4718: mov             x1, x0
    // 0x7d471c: r0 = shortestSide()
    //     0x7d471c: bl              #0x7d5d2c  ; [dart:ui] Size::shortestSide
    // 0x7d4720: mov             v1.16b, v0.16b
    // 0x7d4724: d0 = 0.000000
    //     0x7d4724: eor             v0.16b, v0.16b, v0.16b
    // 0x7d4728: fcmp            d1, d0
    // 0x7d472c: b.ne            #0x7d4770
    // 0x7d4730: r0 = InitLateStaticField(0x674) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x7d4730: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7d4734: ldr             x0, [x0, #0xce8]
    //     0x7d4738: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7d473c: cmp             w0, w16
    //     0x7d4740: b.ne            #0x7d474c
    //     0x7d4744: ldr             x2, [PP, #0x490]  ; [pp+0x490] Field <::.debugPrint>: static late (offset: 0x674)
    //     0x7d4748: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x7d474c: str             NULL, [SP]
    // 0x7d4750: r1 = "[QR] WARN: width or height is zero. You should set a \'size\' value or nest this painter in a Widget that defines a non-zero size"
    //     0x7d4750: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b1a8] "[QR] WARN: width or height is zero. You should set a \'size\' value or nest this painter in a Widget that defines a non-zero size"
    //     0x7d4754: ldr             x1, [x1, #0x1a8]
    // 0x7d4758: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x7d4758: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x7d475c: r0 = debugPrintThrottled()
    //     0x7d475c: bl              #0x63fa18  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x7d4760: r0 = Null
    //     0x7d4760: mov             x0, NULL
    // 0x7d4764: LeaveFrame
    //     0x7d4764: mov             SP, fp
    //     0x7d4768: ldp             fp, lr, [SP], #0x10
    // 0x7d476c: ret
    //     0x7d476c: ret             
    // 0x7d4770: ldur            x0, [fp, #-8]
    // 0x7d4774: ldur            x1, [fp, #-0x18]
    // 0x7d4778: r0 = shortestSide()
    //     0x7d4778: bl              #0x7d5d2c  ; [dart:ui] Size::shortestSide
    // 0x7d477c: ldur            x1, [fp, #-8]
    // 0x7d4780: stur            d0, [fp, #-0xa8]
    // 0x7d4784: LoadField: r0 = r1->field_3b
    //     0x7d4784: ldur            w0, [x1, #0x3b]
    // 0x7d4788: DecompressPointer r0
    //     0x7d4788: add             x0, x0, HEAP, lsl #32
    // 0x7d478c: ArrayLoad: r2 = r0[0]  ; List_8
    //     0x7d478c: ldur            x2, [x0, #0x17]
    // 0x7d4790: stur            x2, [fp, #-0x20]
    // 0x7d4794: r0 = _PaintMetrics()
    //     0x7d4794: bl              #0x7d5d20  ; Allocate_PaintMetricsStub -> _PaintMetrics (size=0x2c)
    // 0x7d4798: mov             x1, x0
    // 0x7d479c: ldur            d0, [fp, #-0xa8]
    // 0x7d47a0: ldur            x2, [fp, #-0x20]
    // 0x7d47a4: stur            x0, [fp, #-0x28]
    // 0x7d47a8: r0 = _PaintMetrics()
    //     0x7d47a8: bl              #0x7d5a44  ; [package:qr_flutter/src/qr_painter.dart] _PaintMetrics::_PaintMetrics
    // 0x7d47ac: ldur            x1, [fp, #-8]
    // 0x7d47b0: ldur            x3, [fp, #-0x10]
    // 0x7d47b4: ldur            x5, [fp, #-0x28]
    // 0x7d47b8: r2 = Instance_FinderPatternPosition
    //     0x7d47b8: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b1b0] Obj!FinderPatternPosition@e2e321
    //     0x7d47bc: ldr             x2, [x2, #0x1b0]
    // 0x7d47c0: r0 = _drawFinderPatternItem()
    //     0x7d47c0: bl              #0x7d55dc  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_drawFinderPatternItem
    // 0x7d47c4: ldur            x1, [fp, #-8]
    // 0x7d47c8: ldur            x3, [fp, #-0x10]
    // 0x7d47cc: ldur            x5, [fp, #-0x28]
    // 0x7d47d0: r2 = Instance_FinderPatternPosition
    //     0x7d47d0: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b1b8] Obj!FinderPatternPosition@e2e301
    //     0x7d47d4: ldr             x2, [x2, #0x1b8]
    // 0x7d47d8: r0 = _drawFinderPatternItem()
    //     0x7d47d8: bl              #0x7d55dc  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_drawFinderPatternItem
    // 0x7d47dc: ldur            x1, [fp, #-8]
    // 0x7d47e0: ldur            x3, [fp, #-0x10]
    // 0x7d47e4: ldur            x5, [fp, #-0x28]
    // 0x7d47e8: r2 = Instance_FinderPatternPosition
    //     0x7d47e8: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5b1c0] Obj!FinderPatternPosition@e2e2e1
    //     0x7d47ec: ldr             x2, [x2, #0x1c0]
    // 0x7d47f0: r0 = _drawFinderPatternItem()
    //     0x7d47f0: bl              #0x7d55dc  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_drawFinderPatternItem
    // 0x7d47f4: ldur            x2, [fp, #-8]
    // 0x7d47f8: LoadField: r3 = r2->field_2b
    //     0x7d47f8: ldur            w3, [x2, #0x2b]
    // 0x7d47fc: DecompressPointer r3
    //     0x7d47fc: add             x3, x3, HEAP, lsl #32
    // 0x7d4800: stur            x3, [fp, #-0x30]
    // 0x7d4804: cmp             w3, NULL
    // 0x7d4808: b.eq            #0x7d48f0
    // 0x7d480c: ldur            x4, [fp, #-0x18]
    // 0x7d4810: LoadField: r5 = r3->field_f
    //     0x7d4810: ldur            x5, [x3, #0xf]
    // 0x7d4814: r0 = BoxInt64Instr(r5)
    //     0x7d4814: sbfiz           x0, x5, #1, #0x1f
    //     0x7d4818: cmp             x5, x0, asr #1
    //     0x7d481c: b.eq            #0x7d4828
    //     0x7d4820: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d4824: stur            x5, [x0, #7]
    // 0x7d4828: str             x0, [SP]
    // 0x7d482c: r0 = toDouble()
    //     0x7d482c: bl              #0xebfc34  ; [dart:core] _IntegerImplementation::toDouble
    // 0x7d4830: mov             x3, x0
    // 0x7d4834: ldur            x2, [fp, #-0x30]
    // 0x7d4838: stur            x3, [fp, #-0x38]
    // 0x7d483c: ArrayLoad: r4 = r2[0]  ; List_8
    //     0x7d483c: ldur            x4, [x2, #0x17]
    // 0x7d4840: r0 = BoxInt64Instr(r4)
    //     0x7d4840: sbfiz           x0, x4, #1, #0x1f
    //     0x7d4844: cmp             x4, x0, asr #1
    //     0x7d4848: b.eq            #0x7d4854
    //     0x7d484c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d4850: stur            x4, [x0, #7]
    // 0x7d4854: str             x0, [SP]
    // 0x7d4858: r0 = toDouble()
    //     0x7d4858: bl              #0xebfc34  ; [dart:core] _IntegerImplementation::toDouble
    // 0x7d485c: mov             x1, x0
    // 0x7d4860: ldur            x0, [fp, #-0x38]
    // 0x7d4864: stur            x1, [fp, #-0x40]
    // 0x7d4868: LoadField: d0 = r0->field_7
    //     0x7d4868: ldur            d0, [x0, #7]
    // 0x7d486c: stur            d0, [fp, #-0xa8]
    // 0x7d4870: r0 = Size()
    //     0x7d4870: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x7d4874: ldur            d0, [fp, #-0xa8]
    // 0x7d4878: StoreField: r0->field_7 = d0
    //     0x7d4878: stur            d0, [x0, #7]
    // 0x7d487c: ldur            x1, [fp, #-0x40]
    // 0x7d4880: LoadField: d0 = r1->field_7
    //     0x7d4880: ldur            d0, [x1, #7]
    // 0x7d4884: StoreField: r0->field_f = d0
    //     0x7d4884: stur            d0, [x0, #0xf]
    // 0x7d4888: ldur            x1, [fp, #-8]
    // 0x7d488c: ldur            x2, [fp, #-0x18]
    // 0x7d4890: mov             x3, x0
    // 0x7d4894: r0 = _scaledAspectSize()
    //     0x7d4894: bl              #0x7d54ac  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_scaledAspectSize
    // 0x7d4898: mov             x1, x0
    // 0x7d489c: ldur            x0, [fp, #-0x18]
    // 0x7d48a0: stur            x1, [fp, #-0x38]
    // 0x7d48a4: LoadField: d0 = r0->field_7
    //     0x7d48a4: ldur            d0, [x0, #7]
    // 0x7d48a8: LoadField: d1 = r1->field_7
    //     0x7d48a8: ldur            d1, [x1, #7]
    // 0x7d48ac: fsub            d2, d0, d1
    // 0x7d48b0: d0 = 2.000000
    //     0x7d48b0: fmov            d0, #2.00000000
    // 0x7d48b4: fdiv            d1, d2, d0
    // 0x7d48b8: stur            d1, [fp, #-0xb0]
    // 0x7d48bc: LoadField: d2 = r0->field_f
    //     0x7d48bc: ldur            d2, [x0, #0xf]
    // 0x7d48c0: LoadField: d3 = r1->field_f
    //     0x7d48c0: ldur            d3, [x1, #0xf]
    // 0x7d48c4: fsub            d4, d2, d3
    // 0x7d48c8: fdiv            d2, d4, d0
    // 0x7d48cc: stur            d2, [fp, #-0xa8]
    // 0x7d48d0: r0 = Offset()
    //     0x7d48d0: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d48d4: ldur            d0, [fp, #-0xb0]
    // 0x7d48d8: StoreField: r0->field_7 = d0
    //     0x7d48d8: stur            d0, [x0, #7]
    // 0x7d48dc: ldur            d0, [fp, #-0xa8]
    // 0x7d48e0: StoreField: r0->field_f = d0
    //     0x7d48e0: stur            d0, [x0, #0xf]
    // 0x7d48e4: ldur            x5, [fp, #-0x38]
    // 0x7d48e8: mov             x3, x0
    // 0x7d48ec: b               #0x7d48f8
    // 0x7d48f0: r5 = Null
    //     0x7d48f0: mov             x5, NULL
    // 0x7d48f4: r3 = Null
    //     0x7d48f4: mov             x3, NULL
    // 0x7d48f8: ldur            x0, [fp, #-8]
    // 0x7d48fc: stur            x5, [fp, #-0x38]
    // 0x7d4900: stur            x3, [fp, #-0x40]
    // 0x7d4904: LoadField: r4 = r0->field_47
    //     0x7d4904: ldur            w4, [x0, #0x47]
    // 0x7d4908: DecompressPointer r4
    //     0x7d4908: add             x4, x4, HEAP, lsl #32
    // 0x7d490c: mov             x1, x4
    // 0x7d4910: stur            x4, [fp, #-0x18]
    // 0x7d4914: r2 = Instance_QrCodeElement
    //     0x7d4914: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d08] Obj!QrCodeElement@e2e3c1
    //     0x7d4918: ldr             x2, [x2, #0xd08]
    // 0x7d491c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7d491c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7d4920: r0 = firstPaint()
    //     0x7d4920: bl              #0x7d5354  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::firstPaint
    // 0x7d4924: stur            x0, [fp, #-0x48]
    // 0x7d4928: cmp             w0, NULL
    // 0x7d492c: b.eq            #0x7d4d2c
    // 0x7d4930: ldur            x1, [fp, #-8]
    // 0x7d4934: r0 = _priorityColor()
    //     0x7d4934: bl              #0x7d534c  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_priorityColor
    // 0x7d4938: ldur            x1, [fp, #-0x48]
    // 0x7d493c: r2 = Instance_Color
    //     0x7d493c: ldr             x2, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0x7d4940: r0 = color=()
    //     0x7d4940: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0x7d4944: ldur            x1, [fp, #-0x18]
    // 0x7d4948: r2 = Instance_QrCodeElement
    //     0x7d4948: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d10] Obj!QrCodeElement@e2e3a1
    //     0x7d494c: ldr             x2, [x2, #0xd10]
    // 0x7d4950: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7d4950: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7d4954: r0 = firstPaint()
    //     0x7d4954: bl              #0x7d5354  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::firstPaint
    // 0x7d4958: stur            x0, [fp, #-0x18]
    // 0x7d495c: cmp             w0, NULL
    // 0x7d4960: b.eq            #0x7d4d30
    // 0x7d4964: mov             x1, x0
    // 0x7d4968: r2 = Instance_Color
    //     0x7d4968: add             x2, PP, #0x4e, lsl #12  ; [pp+0x4ed60] Obj!Color@e28111
    //     0x7d496c: ldr             x2, [x2, #0xd60]
    // 0x7d4970: r0 = color=()
    //     0x7d4970: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0x7d4974: r0 = Radius()
    //     0x7d4974: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x7d4978: stur            x0, [fp, #-0x50]
    // 0x7d497c: StoreField: r0->field_7 = rZR
    //     0x7d497c: stur            xzr, [x0, #7]
    // 0x7d4980: StoreField: r0->field_f = rZR
    //     0x7d4980: stur            xzr, [x0, #0xf]
    // 0x7d4984: r1 = Instance_QrDataModuleStyle
    //     0x7d4984: add             x1, PP, #0x47, lsl #12  ; [pp+0x47b78] Obj!QrDataModuleStyle@e0c121
    //     0x7d4988: ldr             x1, [x1, #0xb78]
    // 0x7d498c: r0 = minIntrinsicWidth()
    //     0x7d498c: bl              #0xdb1254  ; [package:flutter/src/rendering/table.dart] FlexColumnWidth::minIntrinsicWidth
    // 0x7d4990: r4 = 0
    //     0x7d4990: movz            x4, #0
    // 0x7d4994: ldur            x0, [fp, #-8]
    // 0x7d4998: stur            x4, [fp, #-0x78]
    // 0x7d499c: CheckStackOverflow
    //     0x7d499c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d49a0: cmp             SP, x16
    //     0x7d49a4: b.ls            #0x7d4d34
    // 0x7d49a8: LoadField: r1 = r0->field_3b
    //     0x7d49a8: ldur            w1, [x0, #0x3b]
    // 0x7d49ac: DecompressPointer r1
    //     0x7d49ac: add             x1, x1, HEAP, lsl #32
    // 0x7d49b0: ArrayLoad: r2 = r1[0]  ; List_8
    //     0x7d49b0: ldur            x2, [x1, #0x17]
    // 0x7d49b4: cmp             x4, x2
    // 0x7d49b8: b.ge            #0x7d4ce4
    // 0x7d49bc: sub             x5, x4, #1
    // 0x7d49c0: stur            x5, [fp, #-0x70]
    // 0x7d49c4: add             x6, x4, #1
    // 0x7d49c8: stur            x6, [fp, #-0x68]
    // 0x7d49cc: cmp             x4, #7
    // 0x7d49d0: r16 = true
    //     0x7d49d0: add             x16, NULL, #0x20  ; true
    // 0x7d49d4: r17 = false
    //     0x7d49d4: add             x17, NULL, #0x30  ; false
    // 0x7d49d8: csel            x7, x16, x17, lt
    // 0x7d49dc: stur            x7, [fp, #-0x60]
    // 0x7d49e0: cmp             x4, #7
    // 0x7d49e4: r16 = true
    //     0x7d49e4: add             x16, NULL, #0x20  ; true
    // 0x7d49e8: r17 = false
    //     0x7d49e8: add             x17, NULL, #0x30  ; false
    // 0x7d49ec: csel            x8, x16, x17, lt
    // 0x7d49f0: stur            x8, [fp, #-0x58]
    // 0x7d49f4: r10 = 0
    //     0x7d49f4: movz            x10, #0
    // 0x7d49f8: stur            x10, [fp, #-0x20]
    // 0x7d49fc: CheckStackOverflow
    //     0x7d49fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d4a00: cmp             SP, x16
    //     0x7d4a04: b.ls            #0x7d4d3c
    // 0x7d4a08: LoadField: r1 = r0->field_3b
    //     0x7d4a08: ldur            w1, [x0, #0x3b]
    // 0x7d4a0c: DecompressPointer r1
    //     0x7d4a0c: add             x1, x1, HEAP, lsl #32
    // 0x7d4a10: ArrayLoad: r2 = r1[0]  ; List_8
    //     0x7d4a10: ldur            x2, [x1, #0x17]
    // 0x7d4a14: cmp             x10, x2
    // 0x7d4a18: b.ge            #0x7d4cd8
    // 0x7d4a1c: cmp             x10, #7
    // 0x7d4a20: b.ge            #0x7d4a2c
    // 0x7d4a24: mov             x1, x8
    // 0x7d4a28: b               #0x7d4a30
    // 0x7d4a2c: r1 = false
    //     0x7d4a2c: add             x1, NULL, #0x30  ; false
    // 0x7d4a30: cmp             x10, #7
    // 0x7d4a34: b.ge            #0x7d4a54
    // 0x7d4a38: sub             x3, x2, #7
    // 0x7d4a3c: cmp             x4, x3
    // 0x7d4a40: r16 = true
    //     0x7d4a40: add             x16, NULL, #0x20  ; true
    // 0x7d4a44: r17 = false
    //     0x7d4a44: add             x17, NULL, #0x30  ; false
    // 0x7d4a48: csel            x9, x16, x17, ge
    // 0x7d4a4c: mov             x3, x9
    // 0x7d4a50: b               #0x7d4a58
    // 0x7d4a54: r3 = false
    //     0x7d4a54: add             x3, NULL, #0x30  ; false
    // 0x7d4a58: sub             x9, x2, #7
    // 0x7d4a5c: cmp             x10, x9
    // 0x7d4a60: b.lt            #0x7d4a6c
    // 0x7d4a64: mov             x2, x7
    // 0x7d4a68: b               #0x7d4a70
    // 0x7d4a6c: r2 = false
    //     0x7d4a6c: add             x2, NULL, #0x30  ; false
    // 0x7d4a70: tbz             w1, #4, #0x7d4a7c
    // 0x7d4a74: tbz             w3, #4, #0x7d4a7c
    // 0x7d4a78: tbnz            w2, #4, #0x7d4a84
    // 0x7d4a7c: mov             x0, x10
    // 0x7d4a80: b               #0x7d4cb8
    // 0x7d4a84: LoadField: r1 = r0->field_3f
    //     0x7d4a84: ldur            w1, [x0, #0x3f]
    // 0x7d4a88: DecompressPointer r1
    //     0x7d4a88: add             x1, x1, HEAP, lsl #32
    // 0x7d4a8c: r16 = Sentinel
    //     0x7d4a8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d4a90: cmp             w1, w16
    // 0x7d4a94: b.eq            #0x7d4d44
    // 0x7d4a98: mov             x2, x10
    // 0x7d4a9c: mov             x3, x4
    // 0x7d4aa0: r0 = isDark()
    //     0x7d4aa0: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0x7d4aa4: tbnz            w0, #4, #0x7d4ab0
    // 0x7d4aa8: ldur            x4, [fp, #-0x48]
    // 0x7d4aac: b               #0x7d4ab4
    // 0x7d4ab0: ldur            x4, [fp, #-0x18]
    // 0x7d4ab4: stur            x4, [fp, #-0x80]
    // 0x7d4ab8: tbz             w0, #4, #0x7d4ac4
    // 0x7d4abc: ldur            x0, [fp, #-0x20]
    // 0x7d4ac0: b               #0x7d4cb8
    // 0x7d4ac4: ldur            x1, [fp, #-8]
    // 0x7d4ac8: ldur            x2, [fp, #-0x28]
    // 0x7d4acc: ldur            x3, [fp, #-0x78]
    // 0x7d4ad0: ldur            x5, [fp, #-0x20]
    // 0x7d4ad4: r0 = _createDataModuleRect()
    //     0x7d4ad4: bl              #0x7d4fa4  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_createDataModuleRect
    // 0x7d4ad8: d0 = 0.000000
    //     0x7d4ad8: eor             v0.16b, v0.16b, v0.16b
    // 0x7d4adc: stur            x0, [fp, #-0x88]
    // 0x7d4ae0: fcmp            d0, d0
    // 0x7d4ae4: b.le            #0x7d4ca4
    // 0x7d4ae8: ldur            x4, [fp, #-0x20]
    // 0x7d4aec: ldur            x1, [fp, #-8]
    // 0x7d4af0: ldur            x2, [fp, #-0x70]
    // 0x7d4af4: mov             x3, x4
    // 0x7d4af8: r0 = _isDarkOnSide()
    //     0x7d4af8: bl              #0x7d4ee0  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_isDarkOnSide
    // 0x7d4afc: mov             x4, x0
    // 0x7d4b00: ldur            x0, [fp, #-0x20]
    // 0x7d4b04: stur            x4, [fp, #-0x90]
    // 0x7d4b08: sub             x3, x0, #1
    // 0x7d4b0c: ldur            x1, [fp, #-8]
    // 0x7d4b10: ldur            x2, [fp, #-0x78]
    // 0x7d4b14: r0 = _isDarkOnSide()
    //     0x7d4b14: bl              #0x7d4ee0  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_isDarkOnSide
    // 0x7d4b18: ldur            x1, [fp, #-8]
    // 0x7d4b1c: ldur            x2, [fp, #-0x68]
    // 0x7d4b20: ldur            x3, [fp, #-0x20]
    // 0x7d4b24: stur            x0, [fp, #-0x98]
    // 0x7d4b28: r0 = _isDarkOnSide()
    //     0x7d4b28: bl              #0x7d4ee0  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_isDarkOnSide
    // 0x7d4b2c: mov             x4, x0
    // 0x7d4b30: ldur            x0, [fp, #-0x20]
    // 0x7d4b34: stur            x4, [fp, #-0xa0]
    // 0x7d4b38: add             x3, x0, #1
    // 0x7d4b3c: ldur            x1, [fp, #-8]
    // 0x7d4b40: ldur            x2, [fp, #-0x78]
    // 0x7d4b44: r0 = _isDarkOnSide()
    //     0x7d4b44: bl              #0x7d4ee0  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_isDarkOnSide
    // 0x7d4b48: mov             x1, x0
    // 0x7d4b4c: ldur            x0, [fp, #-0x98]
    // 0x7d4b50: tbnz            w0, #4, #0x7d4b5c
    // 0x7d4b54: ldur            x2, [fp, #-0x90]
    // 0x7d4b58: b               #0x7d4b64
    // 0x7d4b5c: ldur            x2, [fp, #-0x90]
    // 0x7d4b60: tbnz            w2, #4, #0x7d4b70
    // 0x7d4b64: r3 = Instance_Radius
    //     0x7d4b64: add             x3, PP, #0x1d, lsl #12  ; [pp+0x1d018] Obj!Radius@e2bdb1
    //     0x7d4b68: ldr             x3, [x3, #0x18]
    // 0x7d4b6c: b               #0x7d4b74
    // 0x7d4b70: ldur            x3, [fp, #-0x50]
    // 0x7d4b74: tbnz            w0, #4, #0x7d4b80
    // 0x7d4b78: ldur            x0, [fp, #-0xa0]
    // 0x7d4b7c: b               #0x7d4b88
    // 0x7d4b80: ldur            x0, [fp, #-0xa0]
    // 0x7d4b84: tbnz            w0, #4, #0x7d4b94
    // 0x7d4b88: r4 = Instance_Radius
    //     0x7d4b88: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d018] Obj!Radius@e2bdb1
    //     0x7d4b8c: ldr             x4, [x4, #0x18]
    // 0x7d4b90: b               #0x7d4b98
    // 0x7d4b94: ldur            x4, [fp, #-0x50]
    // 0x7d4b98: tbz             w1, #4, #0x7d4ba0
    // 0x7d4b9c: tbnz            w2, #4, #0x7d4bac
    // 0x7d4ba0: r2 = Instance_Radius
    //     0x7d4ba0: add             x2, PP, #0x1d, lsl #12  ; [pp+0x1d018] Obj!Radius@e2bdb1
    //     0x7d4ba4: ldr             x2, [x2, #0x18]
    // 0x7d4ba8: b               #0x7d4bb0
    // 0x7d4bac: ldur            x2, [fp, #-0x50]
    // 0x7d4bb0: tbz             w1, #4, #0x7d4bb8
    // 0x7d4bb4: tbnz            w0, #4, #0x7d4bc4
    // 0x7d4bb8: r1 = Instance_Radius
    //     0x7d4bb8: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d018] Obj!Radius@e2bdb1
    //     0x7d4bbc: ldr             x1, [x1, #0x18]
    // 0x7d4bc0: b               #0x7d4bc8
    // 0x7d4bc4: ldur            x1, [fp, #-0x50]
    // 0x7d4bc8: ldur            x0, [fp, #-0x88]
    // 0x7d4bcc: LoadField: d0 = r0->field_f
    //     0x7d4bcc: ldur            d0, [x0, #0xf]
    // 0x7d4bd0: stur            d0, [fp, #-0x100]
    // 0x7d4bd4: LoadField: d1 = r0->field_7
    //     0x7d4bd4: ldur            d1, [x0, #7]
    // 0x7d4bd8: stur            d1, [fp, #-0xf8]
    // 0x7d4bdc: ArrayLoad: d2 = r0[0]  ; List_8
    //     0x7d4bdc: ldur            d2, [x0, #0x17]
    // 0x7d4be0: stur            d2, [fp, #-0xf0]
    // 0x7d4be4: LoadField: d3 = r0->field_1f
    //     0x7d4be4: ldur            d3, [x0, #0x1f]
    // 0x7d4be8: stur            d3, [fp, #-0xe8]
    // 0x7d4bec: LoadField: d4 = r3->field_7
    //     0x7d4bec: ldur            d4, [x3, #7]
    // 0x7d4bf0: stur            d4, [fp, #-0xe0]
    // 0x7d4bf4: LoadField: d5 = r3->field_f
    //     0x7d4bf4: ldur            d5, [x3, #0xf]
    // 0x7d4bf8: stur            d5, [fp, #-0xd8]
    // 0x7d4bfc: LoadField: d6 = r4->field_7
    //     0x7d4bfc: ldur            d6, [x4, #7]
    // 0x7d4c00: stur            d6, [fp, #-0xd0]
    // 0x7d4c04: LoadField: d7 = r4->field_f
    //     0x7d4c04: ldur            d7, [x4, #0xf]
    // 0x7d4c08: stur            d7, [fp, #-0xc8]
    // 0x7d4c0c: LoadField: d8 = r2->field_7
    //     0x7d4c0c: ldur            d8, [x2, #7]
    // 0x7d4c10: stur            d8, [fp, #-0xc0]
    // 0x7d4c14: LoadField: d9 = r2->field_f
    //     0x7d4c14: ldur            d9, [x2, #0xf]
    // 0x7d4c18: stur            d9, [fp, #-0xb8]
    // 0x7d4c1c: LoadField: d10 = r1->field_7
    //     0x7d4c1c: ldur            d10, [x1, #7]
    // 0x7d4c20: stur            d10, [fp, #-0xb0]
    // 0x7d4c24: LoadField: d11 = r1->field_f
    //     0x7d4c24: ldur            d11, [x1, #0xf]
    // 0x7d4c28: stur            d11, [fp, #-0xa8]
    // 0x7d4c2c: r0 = RRect()
    //     0x7d4c2c: bl              #0x789ce8  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x7d4c30: ldur            d0, [fp, #-0xf8]
    // 0x7d4c34: StoreField: r0->field_7 = d0
    //     0x7d4c34: stur            d0, [x0, #7]
    // 0x7d4c38: ldur            d0, [fp, #-0x100]
    // 0x7d4c3c: StoreField: r0->field_f = d0
    //     0x7d4c3c: stur            d0, [x0, #0xf]
    // 0x7d4c40: ldur            d0, [fp, #-0xf0]
    // 0x7d4c44: ArrayStore: r0[0] = d0  ; List_8
    //     0x7d4c44: stur            d0, [x0, #0x17]
    // 0x7d4c48: ldur            d0, [fp, #-0xe8]
    // 0x7d4c4c: StoreField: r0->field_1f = d0
    //     0x7d4c4c: stur            d0, [x0, #0x1f]
    // 0x7d4c50: ldur            d0, [fp, #-0xe0]
    // 0x7d4c54: StoreField: r0->field_27 = d0
    //     0x7d4c54: stur            d0, [x0, #0x27]
    // 0x7d4c58: ldur            d0, [fp, #-0xd8]
    // 0x7d4c5c: StoreField: r0->field_2f = d0
    //     0x7d4c5c: stur            d0, [x0, #0x2f]
    // 0x7d4c60: ldur            d0, [fp, #-0xd0]
    // 0x7d4c64: StoreField: r0->field_37 = d0
    //     0x7d4c64: stur            d0, [x0, #0x37]
    // 0x7d4c68: ldur            d0, [fp, #-0xc8]
    // 0x7d4c6c: StoreField: r0->field_3f = d0
    //     0x7d4c6c: stur            d0, [x0, #0x3f]
    // 0x7d4c70: ldur            d0, [fp, #-0xb0]
    // 0x7d4c74: StoreField: r0->field_47 = d0
    //     0x7d4c74: stur            d0, [x0, #0x47]
    // 0x7d4c78: ldur            d0, [fp, #-0xa8]
    // 0x7d4c7c: StoreField: r0->field_4f = d0
    //     0x7d4c7c: stur            d0, [x0, #0x4f]
    // 0x7d4c80: ldur            d0, [fp, #-0xc0]
    // 0x7d4c84: StoreField: r0->field_57 = d0
    //     0x7d4c84: stur            d0, [x0, #0x57]
    // 0x7d4c88: ldur            d0, [fp, #-0xb8]
    // 0x7d4c8c: StoreField: r0->field_5f = d0
    //     0x7d4c8c: stur            d0, [x0, #0x5f]
    // 0x7d4c90: ldur            x1, [fp, #-0x10]
    // 0x7d4c94: mov             x2, x0
    // 0x7d4c98: ldur            x3, [fp, #-0x80]
    // 0x7d4c9c: r0 = drawRRect()
    //     0x7d4c9c: bl              #0x7899c4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x7d4ca0: b               #0x7d4cb4
    // 0x7d4ca4: ldur            x1, [fp, #-0x10]
    // 0x7d4ca8: mov             x2, x0
    // 0x7d4cac: ldur            x3, [fp, #-0x80]
    // 0x7d4cb0: r0 = drawRect()
    //     0x7d4cb0: bl              #0x78be20  ; [dart:ui] _NativeCanvas::drawRect
    // 0x7d4cb4: ldur            x0, [fp, #-0x20]
    // 0x7d4cb8: add             x10, x0, #1
    // 0x7d4cbc: ldur            x0, [fp, #-8]
    // 0x7d4cc0: ldur            x4, [fp, #-0x78]
    // 0x7d4cc4: ldur            x5, [fp, #-0x70]
    // 0x7d4cc8: ldur            x6, [fp, #-0x68]
    // 0x7d4ccc: ldur            x7, [fp, #-0x60]
    // 0x7d4cd0: ldur            x8, [fp, #-0x58]
    // 0x7d4cd4: b               #0x7d49f8
    // 0x7d4cd8: mov             x0, x4
    // 0x7d4cdc: add             x4, x0, #1
    // 0x7d4ce0: b               #0x7d4994
    // 0x7d4ce4: ldur            x0, [fp, #-0x30]
    // 0x7d4ce8: cmp             w0, NULL
    // 0x7d4cec: b.eq            #0x7d4d14
    // 0x7d4cf0: ldur            x5, [fp, #-0x38]
    // 0x7d4cf4: ldur            x3, [fp, #-0x40]
    // 0x7d4cf8: cmp             w3, NULL
    // 0x7d4cfc: b.eq            #0x7d4d50
    // 0x7d4d00: cmp             w5, NULL
    // 0x7d4d04: b.eq            #0x7d4d54
    // 0x7d4d08: ldur            x1, [fp, #-8]
    // 0x7d4d0c: ldur            x2, [fp, #-0x10]
    // 0x7d4d10: r0 = _drawImageOverlay()
    //     0x7d4d10: bl              #0x7d4d58  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_drawImageOverlay
    // 0x7d4d14: r0 = Null
    //     0x7d4d14: mov             x0, NULL
    // 0x7d4d18: LeaveFrame
    //     0x7d4d18: mov             SP, fp
    //     0x7d4d1c: ldp             fp, lr, [SP], #0x10
    // 0x7d4d20: ret
    //     0x7d4d20: ret             
    // 0x7d4d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d4d24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d4d28: b               #0x7d4718
    // 0x7d4d2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d4d2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7d4d30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d4d30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7d4d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d4d34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d4d38: b               #0x7d49a8
    // 0x7d4d3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d4d3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d4d40: b               #0x7d4a08
    // 0x7d4d44: r9 = _qrImage
    //     0x7d4d44: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1c8] Field <QrPainter._qrImage@2040312174>: late (offset: 0x40)
    //     0x7d4d48: ldr             x9, [x9, #0x1c8]
    // 0x7d4d4c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7d4d4c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7d4d50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d4d50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7d4d54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d4d54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _drawImageOverlay(/* No info */) {
    // ** addr: 0x7d4d58, size: 0x188
    // 0x7d4d58: EnterFrame
    //     0x7d4d58: stp             fp, lr, [SP, #-0x10]!
    //     0x7d4d5c: mov             fp, SP
    // 0x7d4d60: AllocStack(0x50)
    //     0x7d4d60: sub             SP, SP, #0x50
    // 0x7d4d64: SetupParameters(QrPainter this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0x7d4d64: stur            x1, [fp, #-8]
    //     0x7d4d68: mov             x16, x3
    //     0x7d4d6c: mov             x3, x1
    //     0x7d4d70: mov             x1, x16
    //     0x7d4d74: mov             x0, x2
    //     0x7d4d78: stur            x2, [fp, #-0x10]
    //     0x7d4d7c: mov             x2, x5
    //     0x7d4d80: stur            x1, [fp, #-0x18]
    //     0x7d4d84: stur            x5, [fp, #-0x20]
    // 0x7d4d88: CheckStackOverflow
    //     0x7d4d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d4d8c: cmp             SP, x16
    //     0x7d4d90: b.ls            #0x7d4ed4
    // 0x7d4d94: r16 = 136
    //     0x7d4d94: movz            x16, #0x88
    // 0x7d4d98: stp             x16, NULL, [SP]
    // 0x7d4d9c: r0 = ByteData()
    //     0x7d4d9c: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0x7d4da0: stur            x0, [fp, #-0x28]
    // 0x7d4da4: r0 = Paint()
    //     0x7d4da4: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0x7d4da8: mov             x2, x0
    // 0x7d4dac: ldur            x0, [fp, #-0x28]
    // 0x7d4db0: stur            x2, [fp, #-0x30]
    // 0x7d4db4: StoreField: r2->field_7 = r0
    //     0x7d4db4: stur            w0, [x2, #7]
    // 0x7d4db8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7d4db8: ldur            w1, [x0, #0x17]
    // 0x7d4dbc: DecompressPointer r1
    //     0x7d4dbc: add             x1, x1, HEAP, lsl #32
    // 0x7d4dc0: LoadField: r0 = r1->field_7
    //     0x7d4dc0: ldur            x0, [x1, #7]
    // 0x7d4dc4: str             wzr, [x0]
    // 0x7d4dc8: LoadField: r0 = r1->field_7
    //     0x7d4dc8: ldur            x0, [x1, #7]
    // 0x7d4dcc: r1 = 3
    //     0x7d4dcc: movz            x1, #0x3
    // 0x7d4dd0: str             w1, [x0, #0x30]
    // 0x7d4dd4: ldur            x0, [fp, #-8]
    // 0x7d4dd8: LoadField: r3 = r0->field_2b
    //     0x7d4dd8: ldur            w3, [x0, #0x2b]
    // 0x7d4ddc: DecompressPointer r3
    //     0x7d4ddc: add             x3, x3, HEAP, lsl #32
    // 0x7d4de0: stur            x3, [fp, #-0x28]
    // 0x7d4de4: cmp             w3, NULL
    // 0x7d4de8: b.eq            #0x7d4edc
    // 0x7d4dec: LoadField: r4 = r3->field_f
    //     0x7d4dec: ldur            x4, [x3, #0xf]
    // 0x7d4df0: r0 = BoxInt64Instr(r4)
    //     0x7d4df0: sbfiz           x0, x4, #1, #0x1f
    //     0x7d4df4: cmp             x4, x0, asr #1
    //     0x7d4df8: b.eq            #0x7d4e04
    //     0x7d4dfc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d4e00: stur            x4, [x0, #7]
    // 0x7d4e04: stp             x0, NULL, [SP]
    // 0x7d4e08: r0 = _Double.fromInteger()
    //     0x7d4e08: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x7d4e0c: mov             x3, x0
    // 0x7d4e10: ldur            x2, [fp, #-0x28]
    // 0x7d4e14: stur            x3, [fp, #-8]
    // 0x7d4e18: ArrayLoad: r4 = r2[0]  ; List_8
    //     0x7d4e18: ldur            x4, [x2, #0x17]
    // 0x7d4e1c: r0 = BoxInt64Instr(r4)
    //     0x7d4e1c: sbfiz           x0, x4, #1, #0x1f
    //     0x7d4e20: cmp             x4, x0, asr #1
    //     0x7d4e24: b.eq            #0x7d4e30
    //     0x7d4e28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7d4e2c: stur            x4, [x0, #7]
    // 0x7d4e30: stp             x0, NULL, [SP]
    // 0x7d4e34: r0 = _Double.fromInteger()
    //     0x7d4e34: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x7d4e38: mov             x1, x0
    // 0x7d4e3c: ldur            x0, [fp, #-8]
    // 0x7d4e40: stur            x1, [fp, #-0x38]
    // 0x7d4e44: LoadField: d0 = r0->field_7
    //     0x7d4e44: ldur            d0, [x0, #7]
    // 0x7d4e48: stur            d0, [fp, #-0x40]
    // 0x7d4e4c: r0 = Size()
    //     0x7d4e4c: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x7d4e50: ldur            d0, [fp, #-0x40]
    // 0x7d4e54: stur            x0, [fp, #-8]
    // 0x7d4e58: StoreField: r0->field_7 = d0
    //     0x7d4e58: stur            d0, [x0, #7]
    // 0x7d4e5c: ldur            x1, [fp, #-0x38]
    // 0x7d4e60: LoadField: d0 = r1->field_7
    //     0x7d4e60: ldur            d0, [x1, #7]
    // 0x7d4e64: StoreField: r0->field_f = d0
    //     0x7d4e64: stur            d0, [x0, #0xf]
    // 0x7d4e68: mov             x2, x0
    // 0x7d4e6c: r1 = Instance_Offset
    //     0x7d4e6c: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7d4e70: r0 = &()
    //     0x7d4e70: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7d4e74: ldur            x2, [fp, #-8]
    // 0x7d4e78: mov             x3, x0
    // 0x7d4e7c: r1 = Instance_Alignment
    //     0x7d4e7c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x7d4e80: ldr             x1, [x1, #0x898]
    // 0x7d4e84: r0 = inscribe()
    //     0x7d4e84: bl              #0x7914b8  ; [package:flutter/src/painting/alignment.dart] Alignment::inscribe
    // 0x7d4e88: ldur            x1, [fp, #-0x18]
    // 0x7d4e8c: ldur            x2, [fp, #-0x20]
    // 0x7d4e90: stur            x0, [fp, #-8]
    // 0x7d4e94: r0 = &()
    //     0x7d4e94: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7d4e98: ldur            x2, [fp, #-0x20]
    // 0x7d4e9c: mov             x3, x0
    // 0x7d4ea0: r1 = Instance_Alignment
    //     0x7d4ea0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0x7d4ea4: ldr             x1, [x1, #0x898]
    // 0x7d4ea8: r0 = inscribe()
    //     0x7d4ea8: bl              #0x7914b8  ; [package:flutter/src/painting/alignment.dart] Alignment::inscribe
    // 0x7d4eac: ldur            x1, [fp, #-0x10]
    // 0x7d4eb0: ldur            x2, [fp, #-0x28]
    // 0x7d4eb4: ldur            x3, [fp, #-8]
    // 0x7d4eb8: mov             x5, x0
    // 0x7d4ebc: ldur            x6, [fp, #-0x30]
    // 0x7d4ec0: r0 = drawImageRect()
    //     0x7d4ec0: bl              #0x79a0a4  ; [dart:ui] _NativeCanvas::drawImageRect
    // 0x7d4ec4: r0 = Null
    //     0x7d4ec4: mov             x0, NULL
    // 0x7d4ec8: LeaveFrame
    //     0x7d4ec8: mov             SP, fp
    //     0x7d4ecc: ldp             fp, lr, [SP], #0x10
    // 0x7d4ed0: ret
    //     0x7d4ed0: ret             
    // 0x7d4ed4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d4ed4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d4ed8: b               #0x7d4d94
    // 0x7d4edc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d4edc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _isDarkOnSide(/* No info */) {
    // ** addr: 0x7d4ee0, size: 0xc4
    // 0x7d4ee0: EnterFrame
    //     0x7d4ee0: stp             fp, lr, [SP, #-0x10]!
    //     0x7d4ee4: mov             fp, SP
    // 0x7d4ee8: mov             x16, x3
    // 0x7d4eec: mov             x3, x2
    // 0x7d4ef0: mov             x2, x16
    // 0x7d4ef4: CheckStackOverflow
    //     0x7d4ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d4ef8: cmp             SP, x16
    //     0x7d4efc: b.ls            #0x7d4f90
    // 0x7d4f00: LoadField: r0 = r1->field_3f
    //     0x7d4f00: ldur            w0, [x1, #0x3f]
    // 0x7d4f04: DecompressPointer r0
    //     0x7d4f04: add             x0, x0, HEAP, lsl #32
    // 0x7d4f08: r16 = Sentinel
    //     0x7d4f08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d4f0c: cmp             w0, w16
    // 0x7d4f10: b.eq            #0x7d4f98
    // 0x7d4f14: LoadField: r1 = r0->field_7
    //     0x7d4f14: ldur            x1, [x0, #7]
    // 0x7d4f18: sub             x4, x1, #1
    // 0x7d4f1c: tbnz            x3, #0x3f, #0x7d4f34
    // 0x7d4f20: cmp             x3, x4
    // 0x7d4f24: r16 = true
    //     0x7d4f24: add             x16, NULL, #0x20  ; true
    // 0x7d4f28: r17 = false
    //     0x7d4f28: add             x17, NULL, #0x30  ; false
    // 0x7d4f2c: csel            x1, x16, x17, le
    // 0x7d4f30: b               #0x7d4f38
    // 0x7d4f34: r1 = false
    //     0x7d4f34: add             x1, NULL, #0x30  ; false
    // 0x7d4f38: tbnz            x2, #0x3f, #0x7d4f54
    // 0x7d4f3c: cmp             x2, x4
    // 0x7d4f40: r16 = true
    //     0x7d4f40: add             x16, NULL, #0x20  ; true
    // 0x7d4f44: r17 = false
    //     0x7d4f44: add             x17, NULL, #0x30  ; false
    // 0x7d4f48: csel            x5, x16, x17, le
    // 0x7d4f4c: mov             x4, x5
    // 0x7d4f50: b               #0x7d4f58
    // 0x7d4f54: r4 = false
    //     0x7d4f54: add             x4, NULL, #0x30  ; false
    // 0x7d4f58: tbnz            w1, #4, #0x7d4f80
    // 0x7d4f5c: tbnz            w4, #4, #0x7d4f80
    // 0x7d4f60: mov             x1, x0
    // 0x7d4f64: r0 = isDark()
    //     0x7d4f64: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0x7d4f68: tbnz            w0, #4, #0x7d4f74
    // 0x7d4f6c: r1 = true
    //     0x7d4f6c: add             x1, NULL, #0x20  ; true
    // 0x7d4f70: b               #0x7d4f78
    // 0x7d4f74: r1 = false
    //     0x7d4f74: add             x1, NULL, #0x30  ; false
    // 0x7d4f78: mov             x0, x1
    // 0x7d4f7c: b               #0x7d4f84
    // 0x7d4f80: r0 = false
    //     0x7d4f80: add             x0, NULL, #0x30  ; false
    // 0x7d4f84: LeaveFrame
    //     0x7d4f84: mov             SP, fp
    //     0x7d4f88: ldp             fp, lr, [SP], #0x10
    // 0x7d4f8c: ret
    //     0x7d4f8c: ret             
    // 0x7d4f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d4f90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d4f94: b               #0x7d4f00
    // 0x7d4f98: r9 = _qrImage
    //     0x7d4f98: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1c8] Field <QrPainter._qrImage@2040312174>: late (offset: 0x40)
    //     0x7d4f9c: ldr             x9, [x9, #0x1c8]
    // 0x7d4fa0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7d4fa0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _createDataModuleRect(/* No info */) {
    // ** addr: 0x7d4fa4, size: 0x170
    // 0x7d4fa4: EnterFrame
    //     0x7d4fa4: stp             fp, lr, [SP, #-0x10]!
    //     0x7d4fa8: mov             fp, SP
    // 0x7d4fac: AllocStack(0x40)
    //     0x7d4fac: sub             SP, SP, #0x40
    // 0x7d4fb0: d0 = 0.000000
    //     0x7d4fb0: eor             v0.16b, v0.16b, v0.16b
    // 0x7d4fb4: mov             x7, x1
    // 0x7d4fb8: mov             x6, x2
    // 0x7d4fbc: mov             x4, x3
    // 0x7d4fc0: mov             x0, x5
    // 0x7d4fc4: stur            x1, [fp, #-8]
    // 0x7d4fc8: stur            x2, [fp, #-0x10]
    // 0x7d4fcc: stur            x3, [fp, #-0x18]
    // 0x7d4fd0: stur            x5, [fp, #-0x20]
    // 0x7d4fd4: CheckStackOverflow
    //     0x7d4fd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d4fd8: cmp             SP, x16
    //     0x7d4fdc: b.ls            #0x7d50f4
    // 0x7d4fe0: LoadField: r1 = r6->field_27
    //     0x7d4fe0: ldur            w1, [x6, #0x27]
    // 0x7d4fe4: DecompressPointer r1
    //     0x7d4fe4: add             x1, x1, HEAP, lsl #32
    // 0x7d4fe8: r16 = Sentinel
    //     0x7d4fe8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d4fec: cmp             w1, w16
    // 0x7d4ff0: b.eq            #0x7d50fc
    // 0x7d4ff4: LoadField: r2 = r6->field_1f
    //     0x7d4ff4: ldur            w2, [x6, #0x1f]
    // 0x7d4ff8: DecompressPointer r2
    //     0x7d4ff8: add             x2, x2, HEAP, lsl #32
    // 0x7d4ffc: r16 = Sentinel
    //     0x7d4ffc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d5000: cmp             w2, w16
    // 0x7d5004: b.eq            #0x7d5108
    // 0x7d5008: LoadField: d1 = r2->field_7
    //     0x7d5008: ldur            d1, [x2, #7]
    // 0x7d500c: fadd            d2, d1, d0
    // 0x7d5010: scvtf           d0, x4
    // 0x7d5014: fmul            d1, d0, d2
    // 0x7d5018: LoadField: d0 = r1->field_7
    //     0x7d5018: ldur            d0, [x1, #7]
    // 0x7d501c: fadd            d3, d0, d1
    // 0x7d5020: stur            d3, [fp, #-0x30]
    // 0x7d5024: scvtf           d1, x0
    // 0x7d5028: fmul            d4, d1, d2
    // 0x7d502c: fadd            d1, d0, d4
    // 0x7d5030: stur            d1, [fp, #-0x28]
    // 0x7d5034: LoadField: r1 = r7->field_3b
    //     0x7d5034: ldur            w1, [x7, #0x3b]
    // 0x7d5038: DecompressPointer r1
    //     0x7d5038: add             x1, x1, HEAP, lsl #32
    // 0x7d503c: ArrayLoad: r5 = r1[0]  ; List_8
    //     0x7d503c: ldur            x5, [x1, #0x17]
    // 0x7d5040: mov             x1, x7
    // 0x7d5044: mov             x2, x4
    // 0x7d5048: mov             x3, x0
    // 0x7d504c: r0 = _hasAdjacentHorizontalPixel()
    //     0x7d504c: bl              #0x7d5188  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_hasAdjacentHorizontalPixel
    // 0x7d5050: tbnz            w0, #4, #0x7d505c
    // 0x7d5054: d0 = 0.500000
    //     0x7d5054: fmov            d0, #0.50000000
    // 0x7d5058: b               #0x7d5060
    // 0x7d505c: d0 = 0.000000
    //     0x7d505c: eor             v0.16b, v0.16b, v0.16b
    // 0x7d5060: ldur            x1, [fp, #-8]
    // 0x7d5064: stur            d0, [fp, #-0x38]
    // 0x7d5068: LoadField: r0 = r1->field_3b
    //     0x7d5068: ldur            w0, [x1, #0x3b]
    // 0x7d506c: DecompressPointer r0
    //     0x7d506c: add             x0, x0, HEAP, lsl #32
    // 0x7d5070: ArrayLoad: r5 = r0[0]  ; List_8
    //     0x7d5070: ldur            x5, [x0, #0x17]
    // 0x7d5074: ldur            x2, [fp, #-0x18]
    // 0x7d5078: ldur            x3, [fp, #-0x20]
    // 0x7d507c: r0 = _hasAdjacentVerticalPixel()
    //     0x7d507c: bl              #0x7d5114  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_hasAdjacentVerticalPixel
    // 0x7d5080: tbnz            w0, #4, #0x7d508c
    // 0x7d5084: d3 = 0.500000
    //     0x7d5084: fmov            d3, #0.50000000
    // 0x7d5088: b               #0x7d5090
    // 0x7d508c: d3 = 0.000000
    //     0x7d508c: eor             v3.16b, v3.16b, v3.16b
    // 0x7d5090: ldur            x0, [fp, #-0x10]
    // 0x7d5094: ldur            d1, [fp, #-0x30]
    // 0x7d5098: ldur            d2, [fp, #-0x28]
    // 0x7d509c: ldur            d0, [fp, #-0x38]
    // 0x7d50a0: LoadField: r1 = r0->field_1f
    //     0x7d50a0: ldur            w1, [x0, #0x1f]
    // 0x7d50a4: DecompressPointer r1
    //     0x7d50a4: add             x1, x1, HEAP, lsl #32
    // 0x7d50a8: LoadField: d4 = r1->field_7
    //     0x7d50a8: ldur            d4, [x1, #7]
    // 0x7d50ac: fadd            d5, d4, d0
    // 0x7d50b0: fadd            d0, d4, d3
    // 0x7d50b4: fadd            d3, d1, d5
    // 0x7d50b8: stur            d3, [fp, #-0x40]
    // 0x7d50bc: fadd            d4, d2, d0
    // 0x7d50c0: stur            d4, [fp, #-0x38]
    // 0x7d50c4: r0 = Rect()
    //     0x7d50c4: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7d50c8: ldur            d0, [fp, #-0x30]
    // 0x7d50cc: StoreField: r0->field_7 = d0
    //     0x7d50cc: stur            d0, [x0, #7]
    // 0x7d50d0: ldur            d0, [fp, #-0x28]
    // 0x7d50d4: StoreField: r0->field_f = d0
    //     0x7d50d4: stur            d0, [x0, #0xf]
    // 0x7d50d8: ldur            d0, [fp, #-0x40]
    // 0x7d50dc: ArrayStore: r0[0] = d0  ; List_8
    //     0x7d50dc: stur            d0, [x0, #0x17]
    // 0x7d50e0: ldur            d0, [fp, #-0x38]
    // 0x7d50e4: StoreField: r0->field_1f = d0
    //     0x7d50e4: stur            d0, [x0, #0x1f]
    // 0x7d50e8: LeaveFrame
    //     0x7d50e8: mov             SP, fp
    //     0x7d50ec: ldp             fp, lr, [SP], #0x10
    // 0x7d50f0: ret
    //     0x7d50f0: ret             
    // 0x7d50f4: r0 = StackOverflowSharedWithFPURegs()
    //     0x7d50f4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7d50f8: b               #0x7d4fe0
    // 0x7d50fc: r9 = _inset
    //     0x7d50fc: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1d0] Field <_PaintMetrics@2040312174._inset@2040312174>: late final (offset: 0x28)
    //     0x7d5100: ldr             x9, [x9, #0x1d0]
    // 0x7d5104: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x7d5104: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x7d5108: r9 = _pixelSize
    //     0x7d5108: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1d8] Field <_PaintMetrics@2040312174._pixelSize@2040312174>: late final (offset: 0x20)
    //     0x7d510c: ldr             x9, [x9, #0x1d8]
    // 0x7d5110: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x7d5110: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
  }
  _ _hasAdjacentVerticalPixel(/* No info */) {
    // ** addr: 0x7d5114, size: 0x74
    // 0x7d5114: EnterFrame
    //     0x7d5114: stp             fp, lr, [SP, #-0x10]!
    //     0x7d5118: mov             fp, SP
    // 0x7d511c: mov             x0, x2
    // 0x7d5120: CheckStackOverflow
    //     0x7d5120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d5124: cmp             SP, x16
    //     0x7d5128: b.ls            #0x7d5174
    // 0x7d512c: add             x2, x3, #1
    // 0x7d5130: cmp             x2, x5
    // 0x7d5134: b.lt            #0x7d5148
    // 0x7d5138: r0 = false
    //     0x7d5138: add             x0, NULL, #0x30  ; false
    // 0x7d513c: LeaveFrame
    //     0x7d513c: mov             SP, fp
    //     0x7d5140: ldp             fp, lr, [SP], #0x10
    // 0x7d5144: ret
    //     0x7d5144: ret             
    // 0x7d5148: LoadField: r3 = r1->field_3f
    //     0x7d5148: ldur            w3, [x1, #0x3f]
    // 0x7d514c: DecompressPointer r3
    //     0x7d514c: add             x3, x3, HEAP, lsl #32
    // 0x7d5150: r16 = Sentinel
    //     0x7d5150: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d5154: cmp             w3, w16
    // 0x7d5158: b.eq            #0x7d517c
    // 0x7d515c: mov             x1, x3
    // 0x7d5160: mov             x3, x0
    // 0x7d5164: r0 = isDark()
    //     0x7d5164: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0x7d5168: LeaveFrame
    //     0x7d5168: mov             SP, fp
    //     0x7d516c: ldp             fp, lr, [SP], #0x10
    // 0x7d5170: ret
    //     0x7d5170: ret             
    // 0x7d5174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d5174: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d5178: b               #0x7d512c
    // 0x7d517c: r9 = _qrImage
    //     0x7d517c: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1c8] Field <QrPainter._qrImage@2040312174>: late (offset: 0x40)
    //     0x7d5180: ldr             x9, [x9, #0x1c8]
    // 0x7d5184: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7d5184: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _hasAdjacentHorizontalPixel(/* No info */) {
    // ** addr: 0x7d5188, size: 0x74
    // 0x7d5188: EnterFrame
    //     0x7d5188: stp             fp, lr, [SP, #-0x10]!
    //     0x7d518c: mov             fp, SP
    // 0x7d5190: mov             x0, x2
    // 0x7d5194: mov             x2, x3
    // 0x7d5198: CheckStackOverflow
    //     0x7d5198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d519c: cmp             SP, x16
    //     0x7d51a0: b.ls            #0x7d51e8
    // 0x7d51a4: add             x3, x0, #1
    // 0x7d51a8: cmp             x3, x5
    // 0x7d51ac: b.lt            #0x7d51c0
    // 0x7d51b0: r0 = false
    //     0x7d51b0: add             x0, NULL, #0x30  ; false
    // 0x7d51b4: LeaveFrame
    //     0x7d51b4: mov             SP, fp
    //     0x7d51b8: ldp             fp, lr, [SP], #0x10
    // 0x7d51bc: ret
    //     0x7d51bc: ret             
    // 0x7d51c0: LoadField: r0 = r1->field_3f
    //     0x7d51c0: ldur            w0, [x1, #0x3f]
    // 0x7d51c4: DecompressPointer r0
    //     0x7d51c4: add             x0, x0, HEAP, lsl #32
    // 0x7d51c8: r16 = Sentinel
    //     0x7d51c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d51cc: cmp             w0, w16
    // 0x7d51d0: b.eq            #0x7d51f0
    // 0x7d51d4: mov             x1, x0
    // 0x7d51d8: r0 = isDark()
    //     0x7d51d8: bl              #0x7d51fc  ; [package:qr/src/qr_image.dart] QrImage::isDark
    // 0x7d51dc: LeaveFrame
    //     0x7d51dc: mov             SP, fp
    //     0x7d51e0: ldp             fp, lr, [SP], #0x10
    // 0x7d51e4: ret
    //     0x7d51e4: ret             
    // 0x7d51e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d51e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d51ec: b               #0x7d51a4
    // 0x7d51f0: r9 = _qrImage
    //     0x7d51f0: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1c8] Field <QrPainter._qrImage@2040312174>: late (offset: 0x40)
    //     0x7d51f4: ldr             x9, [x9, #0x1c8]
    // 0x7d51f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7d51f8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _priorityColor(/* No info */) {
    // ** addr: 0x7d534c, size: 0x8
    // 0x7d534c: r0 = Instance_Color
    //     0x7d534c: ldr             x0, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0x7d5350: ret
    //     0x7d5350: ret             
  }
  _ _scaledAspectSize(/* No info */) {
    // ** addr: 0x7d54ac, size: 0x98
    // 0x7d54ac: EnterFrame
    //     0x7d54ac: stp             fp, lr, [SP, #-0x10]!
    //     0x7d54b0: mov             fp, SP
    // 0x7d54b4: AllocStack(0x18)
    //     0x7d54b4: sub             SP, SP, #0x18
    // 0x7d54b8: SetupParameters(QrPainter this /* r1 => r2 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r0, fp-0x8 */)
    //     0x7d54b8: mov             x16, x2
    //     0x7d54bc: mov             x2, x1
    //     0x7d54c0: mov             x1, x16
    //     0x7d54c4: mov             x0, x3
    //     0x7d54c8: stur            x3, [fp, #-8]
    // 0x7d54cc: CheckStackOverflow
    //     0x7d54cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d54d0: cmp             SP, x16
    //     0x7d54d4: b.ls            #0x7d553c
    // 0x7d54d8: r0 = shortestSide()
    //     0x7d54d8: bl              #0x7d5d2c  ; [dart:ui] Size::shortestSide
    // 0x7d54dc: mov             v1.16b, v0.16b
    // 0x7d54e0: d0 = 0.250000
    //     0x7d54e0: fmov            d0, #0.25000000
    // 0x7d54e4: fmul            d2, d1, d0
    // 0x7d54e8: ldur            x1, [fp, #-8]
    // 0x7d54ec: stur            d2, [fp, #-0x10]
    // 0x7d54f0: r0 = longestSide()
    //     0x7d54f0: bl              #0x7d5544  ; [dart:ui] Size::longestSide
    // 0x7d54f4: mov             v1.16b, v0.16b
    // 0x7d54f8: ldur            d0, [fp, #-0x10]
    // 0x7d54fc: fdiv            d2, d0, d1
    // 0x7d5500: ldur            x0, [fp, #-8]
    // 0x7d5504: LoadField: d0 = r0->field_7
    //     0x7d5504: ldur            d0, [x0, #7]
    // 0x7d5508: fmul            d1, d2, d0
    // 0x7d550c: stur            d1, [fp, #-0x18]
    // 0x7d5510: LoadField: d0 = r0->field_f
    //     0x7d5510: ldur            d0, [x0, #0xf]
    // 0x7d5514: fmul            d3, d2, d0
    // 0x7d5518: stur            d3, [fp, #-0x10]
    // 0x7d551c: r0 = Size()
    //     0x7d551c: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x7d5520: ldur            d0, [fp, #-0x18]
    // 0x7d5524: StoreField: r0->field_7 = d0
    //     0x7d5524: stur            d0, [x0, #7]
    // 0x7d5528: ldur            d0, [fp, #-0x10]
    // 0x7d552c: StoreField: r0->field_f = d0
    //     0x7d552c: stur            d0, [x0, #0xf]
    // 0x7d5530: LeaveFrame
    //     0x7d5530: mov             SP, fp
    //     0x7d5534: ldp             fp, lr, [SP], #0x10
    // 0x7d5538: ret
    //     0x7d5538: ret             
    // 0x7d553c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d553c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d5540: b               #0x7d54d8
  }
  _ _drawFinderPatternItem(/* No info */) {
    // ** addr: 0x7d55dc, size: 0x468
    // 0x7d55dc: EnterFrame
    //     0x7d55dc: stp             fp, lr, [SP, #-0x10]!
    //     0x7d55e0: mov             fp, SP
    // 0x7d55e4: AllocStack(0x78)
    //     0x7d55e4: sub             SP, SP, #0x78
    // 0x7d55e8: d2 = 0.000000
    //     0x7d55e8: eor             v2.16b, v2.16b, v2.16b
    // 0x7d55ec: d1 = 7.000000
    //     0x7d55ec: fmov            d1, #7.00000000
    // 0x7d55f0: d0 = 2.000000
    //     0x7d55f0: fmov            d0, #2.00000000
    // 0x7d55f4: mov             x0, x3
    // 0x7d55f8: stur            x3, [fp, #-0x18]
    // 0x7d55fc: mov             x3, x2
    // 0x7d5600: stur            x2, [fp, #-0x10]
    // 0x7d5604: stur            x5, [fp, #-0x20]
    // 0x7d5608: CheckStackOverflow
    //     0x7d5608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d560c: cmp             SP, x16
    //     0x7d5610: b.ls            #0x7d5a04
    // 0x7d5614: LoadField: r2 = r5->field_1f
    //     0x7d5614: ldur            w2, [x5, #0x1f]
    // 0x7d5618: DecompressPointer r2
    //     0x7d5618: add             x2, x2, HEAP, lsl #32
    // 0x7d561c: r16 = Sentinel
    //     0x7d561c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d5620: cmp             w2, w16
    // 0x7d5624: b.eq            #0x7d5a0c
    // 0x7d5628: LoadField: d3 = r2->field_7
    //     0x7d5628: ldur            d3, [x2, #7]
    // 0x7d562c: fmul            d4, d3, d1
    // 0x7d5630: fadd            d1, d4, d2
    // 0x7d5634: fsub            d4, d1, d3
    // 0x7d5638: stur            d4, [fp, #-0x60]
    // 0x7d563c: fdiv            d1, d3, d0
    // 0x7d5640: stur            d1, [fp, #-0x58]
    // 0x7d5644: LoadField: r2 = r5->field_27
    //     0x7d5644: ldur            w2, [x5, #0x27]
    // 0x7d5648: DecompressPointer r2
    //     0x7d5648: add             x2, x2, HEAP, lsl #32
    // 0x7d564c: r16 = Sentinel
    //     0x7d564c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d5650: cmp             w2, w16
    // 0x7d5654: b.eq            #0x7d5a18
    // 0x7d5658: LoadField: r4 = r5->field_23
    //     0x7d5658: ldur            w4, [x5, #0x23]
    // 0x7d565c: DecompressPointer r4
    //     0x7d565c: add             x4, x4, HEAP, lsl #32
    // 0x7d5660: r16 = Sentinel
    //     0x7d5660: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7d5664: cmp             w4, w16
    // 0x7d5668: b.eq            #0x7d5a24
    // 0x7d566c: LoadField: d3 = r2->field_7
    //     0x7d566c: ldur            d3, [x2, #7]
    // 0x7d5670: LoadField: d5 = r4->field_7
    //     0x7d5670: ldur            d5, [x4, #7]
    // 0x7d5674: fadd            d6, d3, d5
    // 0x7d5678: fadd            d5, d4, d1
    // 0x7d567c: fsub            d7, d6, d5
    // 0x7d5680: r16 = Instance_FinderPatternPosition
    //     0x7d5680: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b1b0] Obj!FinderPatternPosition@e2e321
    //     0x7d5684: ldr             x16, [x16, #0x1b0]
    // 0x7d5688: cmp             w3, w16
    // 0x7d568c: b.ne            #0x7d569c
    // 0x7d5690: fadd            d5, d3, d1
    // 0x7d5694: mov             v3.16b, v5.16b
    // 0x7d5698: b               #0x7d56c4
    // 0x7d569c: r16 = Instance_FinderPatternPosition
    //     0x7d569c: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b1b8] Obj!FinderPatternPosition@e2e301
    //     0x7d56a0: ldr             x16, [x16, #0x1b8]
    // 0x7d56a4: cmp             w3, w16
    // 0x7d56a8: b.ne            #0x7d56b8
    // 0x7d56ac: fadd            d5, d3, d1
    // 0x7d56b0: mov             v3.16b, v7.16b
    // 0x7d56b4: b               #0x7d56c4
    // 0x7d56b8: fadd            d5, d3, d1
    // 0x7d56bc: mov             v3.16b, v5.16b
    // 0x7d56c0: mov             v5.16b, v7.16b
    // 0x7d56c4: stur            d5, [fp, #-0x48]
    // 0x7d56c8: stur            d3, [fp, #-0x50]
    // 0x7d56cc: LoadField: r4 = r1->field_47
    //     0x7d56cc: ldur            w4, [x1, #0x47]
    // 0x7d56d0: DecompressPointer r4
    //     0x7d56d0: add             x4, x4, HEAP, lsl #32
    // 0x7d56d4: stur            x4, [fp, #-8]
    // 0x7d56d8: str             x3, [SP]
    // 0x7d56dc: mov             x1, x4
    // 0x7d56e0: r2 = Instance_QrCodeElement
    //     0x7d56e0: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d20] Obj!QrCodeElement@e2e381
    //     0x7d56e4: ldr             x2, [x2, #0xd20]
    // 0x7d56e8: r4 = const [0, 0x3, 0x1, 0x2, position, 0x2, null]
    //     0x7d56e8: add             x4, PP, #0x4a, lsl #12  ; [pp+0x4ab68] List(7) [0, 0x3, 0x1, 0x2, "position", 0x2, Null]
    //     0x7d56ec: ldr             x4, [x4, #0xb68]
    // 0x7d56f0: r0 = firstPaint()
    //     0x7d56f0: bl              #0x7d5354  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::firstPaint
    // 0x7d56f4: mov             x3, x0
    // 0x7d56f8: stur            x3, [fp, #-0x28]
    // 0x7d56fc: cmp             w3, NULL
    // 0x7d5700: b.eq            #0x7d5a30
    // 0x7d5704: ldur            x4, [fp, #-0x20]
    // 0x7d5708: LoadField: r2 = r4->field_1f
    //     0x7d5708: ldur            w2, [x4, #0x1f]
    // 0x7d570c: DecompressPointer r2
    //     0x7d570c: add             x2, x2, HEAP, lsl #32
    // 0x7d5710: LoadField: r5 = r3->field_7
    //     0x7d5710: ldur            w5, [x3, #7]
    // 0x7d5714: DecompressPointer r5
    //     0x7d5714: add             x5, x5, HEAP, lsl #32
    // 0x7d5718: LoadField: r0 = r5->field_13
    //     0x7d5718: ldur            w0, [x5, #0x13]
    // 0x7d571c: r1 = LoadInt32Instr(r0)
    //     0x7d571c: sbfx            x1, x0, #1, #0x1f
    // 0x7d5720: sub             x0, x1, #3
    // 0x7d5724: r1 = 32
    //     0x7d5724: movz            x1, #0x20
    // 0x7d5728: cmp             x1, x0
    // 0x7d572c: b.hs            #0x7d5a34
    // 0x7d5730: ArrayLoad: r0 = r5[0]  ; List_4
    //     0x7d5730: ldur            w0, [x5, #0x17]
    // 0x7d5734: DecompressPointer r0
    //     0x7d5734: add             x0, x0, HEAP, lsl #32
    // 0x7d5738: LoadField: r1 = r5->field_1b
    //     0x7d5738: ldur            w1, [x5, #0x1b]
    // 0x7d573c: r5 = LoadInt32Instr(r1)
    //     0x7d573c: sbfx            x5, x1, #1, #0x1f
    // 0x7d5740: add             x1, x5, #0x20
    // 0x7d5744: LoadField: d0 = r2->field_7
    //     0x7d5744: ldur            d0, [x2, #7]
    // 0x7d5748: fcvt            s1, d0
    // 0x7d574c: LoadField: r2 = r0->field_7
    //     0x7d574c: ldur            x2, [x0, #7]
    // 0x7d5750: str             s1, [x2, x1]
    // 0x7d5754: mov             x1, x3
    // 0x7d5758: r2 = Instance_Color
    //     0x7d5758: ldr             x2, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0x7d575c: r0 = color=()
    //     0x7d575c: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0x7d5760: ldur            x16, [fp, #-0x10]
    // 0x7d5764: str             x16, [SP]
    // 0x7d5768: ldur            x1, [fp, #-8]
    // 0x7d576c: r2 = Instance_QrCodeElement
    //     0x7d576c: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d28] Obj!QrCodeElement@e2e361
    //     0x7d5770: ldr             x2, [x2, #0xd28]
    // 0x7d5774: r4 = const [0, 0x3, 0x1, 0x2, position, 0x2, null]
    //     0x7d5774: add             x4, PP, #0x4a, lsl #12  ; [pp+0x4ab68] List(7) [0, 0x3, 0x1, 0x2, "position", 0x2, Null]
    //     0x7d5778: ldr             x4, [x4, #0xb68]
    // 0x7d577c: r0 = firstPaint()
    //     0x7d577c: bl              #0x7d5354  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::firstPaint
    // 0x7d5780: mov             x3, x0
    // 0x7d5784: stur            x3, [fp, #-0x30]
    // 0x7d5788: cmp             w3, NULL
    // 0x7d578c: b.eq            #0x7d5a38
    // 0x7d5790: ldur            x4, [fp, #-0x20]
    // 0x7d5794: LoadField: r2 = r4->field_1f
    //     0x7d5794: ldur            w2, [x4, #0x1f]
    // 0x7d5798: DecompressPointer r2
    //     0x7d5798: add             x2, x2, HEAP, lsl #32
    // 0x7d579c: LoadField: r5 = r3->field_7
    //     0x7d579c: ldur            w5, [x3, #7]
    // 0x7d57a0: DecompressPointer r5
    //     0x7d57a0: add             x5, x5, HEAP, lsl #32
    // 0x7d57a4: LoadField: r0 = r5->field_13
    //     0x7d57a4: ldur            w0, [x5, #0x13]
    // 0x7d57a8: r1 = LoadInt32Instr(r0)
    //     0x7d57a8: sbfx            x1, x0, #1, #0x1f
    // 0x7d57ac: sub             x0, x1, #3
    // 0x7d57b0: r1 = 32
    //     0x7d57b0: movz            x1, #0x20
    // 0x7d57b4: cmp             x1, x0
    // 0x7d57b8: b.hs            #0x7d5a3c
    // 0x7d57bc: ArrayLoad: r0 = r5[0]  ; List_4
    //     0x7d57bc: ldur            w0, [x5, #0x17]
    // 0x7d57c0: DecompressPointer r0
    //     0x7d57c0: add             x0, x0, HEAP, lsl #32
    // 0x7d57c4: LoadField: r1 = r5->field_1b
    //     0x7d57c4: ldur            w1, [x5, #0x1b]
    // 0x7d57c8: r5 = LoadInt32Instr(r1)
    //     0x7d57c8: sbfx            x5, x1, #1, #0x1f
    // 0x7d57cc: add             x1, x5, #0x20
    // 0x7d57d0: LoadField: d0 = r2->field_7
    //     0x7d57d0: ldur            d0, [x2, #7]
    // 0x7d57d4: fcvt            s1, d0
    // 0x7d57d8: LoadField: r2 = r0->field_7
    //     0x7d57d8: ldur            x2, [x0, #7]
    // 0x7d57dc: str             s1, [x2, x1]
    // 0x7d57e0: mov             x1, x3
    // 0x7d57e4: r2 = Instance_Color
    //     0x7d57e4: add             x2, PP, #0x4e, lsl #12  ; [pp+0x4ed60] Obj!Color@e28111
    //     0x7d57e8: ldr             x2, [x2, #0xd60]
    // 0x7d57ec: r0 = color=()
    //     0x7d57ec: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0x7d57f0: ldur            x16, [fp, #-0x10]
    // 0x7d57f4: str             x16, [SP]
    // 0x7d57f8: ldur            x1, [fp, #-8]
    // 0x7d57fc: r2 = Instance_QrCodeElement
    //     0x7d57fc: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d30] Obj!QrCodeElement@e2e341
    //     0x7d5800: ldr             x2, [x2, #0xd30]
    // 0x7d5804: r4 = const [0, 0x3, 0x1, 0x2, position, 0x2, null]
    //     0x7d5804: add             x4, PP, #0x4a, lsl #12  ; [pp+0x4ab68] List(7) [0, 0x3, 0x1, 0x2, "position", 0x2, Null]
    //     0x7d5808: ldr             x4, [x4, #0xb68]
    // 0x7d580c: r0 = firstPaint()
    //     0x7d580c: bl              #0x7d5354  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::firstPaint
    // 0x7d5810: stur            x0, [fp, #-8]
    // 0x7d5814: cmp             w0, NULL
    // 0x7d5818: b.eq            #0x7d5a40
    // 0x7d581c: mov             x1, x0
    // 0x7d5820: r2 = Instance_Color
    //     0x7d5820: ldr             x2, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0x7d5824: r0 = color=()
    //     0x7d5824: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0x7d5828: ldur            d0, [fp, #-0x60]
    // 0x7d582c: ldur            d1, [fp, #-0x48]
    // 0x7d5830: fadd            d2, d1, d0
    // 0x7d5834: ldur            d3, [fp, #-0x50]
    // 0x7d5838: stur            d2, [fp, #-0x70]
    // 0x7d583c: fadd            d4, d3, d0
    // 0x7d5840: stur            d4, [fp, #-0x68]
    // 0x7d5844: r0 = Rect()
    //     0x7d5844: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7d5848: ldur            d0, [fp, #-0x48]
    // 0x7d584c: stur            x0, [fp, #-0x10]
    // 0x7d5850: StoreField: r0->field_7 = d0
    //     0x7d5850: stur            d0, [x0, #7]
    // 0x7d5854: ldur            d1, [fp, #-0x50]
    // 0x7d5858: StoreField: r0->field_f = d1
    //     0x7d5858: stur            d1, [x0, #0xf]
    // 0x7d585c: ldur            d2, [fp, #-0x70]
    // 0x7d5860: ArrayStore: r0[0] = d2  ; List_8
    //     0x7d5860: stur            d2, [x0, #0x17]
    // 0x7d5864: ldur            d2, [fp, #-0x68]
    // 0x7d5868: StoreField: r0->field_1f = d2
    //     0x7d5868: stur            d2, [x0, #0x1f]
    // 0x7d586c: ldur            x1, [fp, #-0x20]
    // 0x7d5870: LoadField: r2 = r1->field_1f
    //     0x7d5870: ldur            w2, [x1, #0x1f]
    // 0x7d5874: DecompressPointer r2
    //     0x7d5874: add             x2, x2, HEAP, lsl #32
    // 0x7d5878: LoadField: d2 = r2->field_7
    //     0x7d5878: ldur            d2, [x2, #7]
    // 0x7d587c: d3 = 2.000000
    //     0x7d587c: fmov            d3, #2.00000000
    // 0x7d5880: fmul            d4, d2, d3
    // 0x7d5884: ldur            d5, [fp, #-0x60]
    // 0x7d5888: fsub            d6, d5, d4
    // 0x7d588c: stur            d6, [fp, #-0x70]
    // 0x7d5890: fadd            d4, d0, d2
    // 0x7d5894: stur            d4, [fp, #-0x68]
    // 0x7d5898: fadd            d0, d1, d2
    // 0x7d589c: stur            d0, [fp, #-0x60]
    // 0x7d58a0: fadd            d1, d4, d6
    // 0x7d58a4: stur            d1, [fp, #-0x50]
    // 0x7d58a8: fadd            d2, d0, d6
    // 0x7d58ac: stur            d2, [fp, #-0x48]
    // 0x7d58b0: r0 = Rect()
    //     0x7d58b0: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7d58b4: ldur            d0, [fp, #-0x68]
    // 0x7d58b8: stur            x0, [fp, #-0x20]
    // 0x7d58bc: StoreField: r0->field_7 = d0
    //     0x7d58bc: stur            d0, [x0, #7]
    // 0x7d58c0: ldur            d1, [fp, #-0x60]
    // 0x7d58c4: StoreField: r0->field_f = d1
    //     0x7d58c4: stur            d1, [x0, #0xf]
    // 0x7d58c8: ldur            d2, [fp, #-0x50]
    // 0x7d58cc: ArrayStore: r0[0] = d2  ; List_8
    //     0x7d58cc: stur            d2, [x0, #0x17]
    // 0x7d58d0: ldur            d2, [fp, #-0x48]
    // 0x7d58d4: StoreField: r0->field_1f = d2
    //     0x7d58d4: stur            d2, [x0, #0x1f]
    // 0x7d58d8: ldur            d3, [fp, #-0x58]
    // 0x7d58dc: d2 = 2.000000
    //     0x7d58dc: fmov            d2, #2.00000000
    // 0x7d58e0: fmul            d4, d3, d2
    // 0x7d58e4: ldur            d2, [fp, #-0x70]
    // 0x7d58e8: fsub            d5, d2, d4
    // 0x7d58ec: fadd            d2, d0, d3
    // 0x7d58f0: stur            d2, [fp, #-0x70]
    // 0x7d58f4: fadd            d0, d1, d3
    // 0x7d58f8: stur            d0, [fp, #-0x68]
    // 0x7d58fc: fadd            d1, d2, d5
    // 0x7d5900: stur            d1, [fp, #-0x50]
    // 0x7d5904: fadd            d3, d0, d5
    // 0x7d5908: stur            d3, [fp, #-0x48]
    // 0x7d590c: r0 = Rect()
    //     0x7d590c: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7d5910: ldur            d0, [fp, #-0x70]
    // 0x7d5914: stur            x0, [fp, #-0x38]
    // 0x7d5918: StoreField: r0->field_7 = d0
    //     0x7d5918: stur            d0, [x0, #7]
    // 0x7d591c: ldur            d0, [fp, #-0x68]
    // 0x7d5920: StoreField: r0->field_f = d0
    //     0x7d5920: stur            d0, [x0, #0xf]
    // 0x7d5924: ldur            d0, [fp, #-0x50]
    // 0x7d5928: ArrayStore: r0[0] = d0  ; List_8
    //     0x7d5928: stur            d0, [x0, #0x17]
    // 0x7d592c: ldur            d0, [fp, #-0x48]
    // 0x7d5930: StoreField: r0->field_1f = d0
    //     0x7d5930: stur            d0, [x0, #0x1f]
    // 0x7d5934: d0 = 0.000000
    //     0x7d5934: eor             v0.16b, v0.16b, v0.16b
    // 0x7d5938: fcmp            d0, d0
    // 0x7d593c: b.le            #0x7d59c4
    // 0x7d5940: r0 = Radius()
    //     0x7d5940: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x7d5944: stur            x0, [fp, #-0x40]
    // 0x7d5948: StoreField: r0->field_7 = rZR
    //     0x7d5948: stur            xzr, [x0, #7]
    // 0x7d594c: StoreField: r0->field_f = rZR
    //     0x7d594c: stur            xzr, [x0, #0xf]
    // 0x7d5950: r0 = RRect()
    //     0x7d5950: bl              #0x789ce8  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x7d5954: mov             x1, x0
    // 0x7d5958: ldur            x2, [fp, #-0x10]
    // 0x7d595c: ldur            x3, [fp, #-0x40]
    // 0x7d5960: stur            x0, [fp, #-0x40]
    // 0x7d5964: r0 = RRect.fromRectAndRadius()
    //     0x7d5964: bl              #0x789854  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x7d5968: ldur            x1, [fp, #-0x18]
    // 0x7d596c: ldur            x2, [fp, #-0x40]
    // 0x7d5970: ldur            x3, [fp, #-0x28]
    // 0x7d5974: r0 = drawRRect()
    //     0x7d5974: bl              #0x7899c4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x7d5978: ldur            x1, [fp, #-0x18]
    // 0x7d597c: ldur            x2, [fp, #-0x20]
    // 0x7d5980: ldur            x3, [fp, #-0x30]
    // 0x7d5984: r0 = drawRect()
    //     0x7d5984: bl              #0x78be20  ; [dart:ui] _NativeCanvas::drawRect
    // 0x7d5988: r0 = Radius()
    //     0x7d5988: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x7d598c: stur            x0, [fp, #-0x40]
    // 0x7d5990: StoreField: r0->field_7 = rZR
    //     0x7d5990: stur            xzr, [x0, #7]
    // 0x7d5994: StoreField: r0->field_f = rZR
    //     0x7d5994: stur            xzr, [x0, #0xf]
    // 0x7d5998: r0 = RRect()
    //     0x7d5998: bl              #0x789ce8  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x7d599c: mov             x1, x0
    // 0x7d59a0: ldur            x2, [fp, #-0x38]
    // 0x7d59a4: ldur            x3, [fp, #-0x40]
    // 0x7d59a8: stur            x0, [fp, #-0x40]
    // 0x7d59ac: r0 = RRect.fromRectAndRadius()
    //     0x7d59ac: bl              #0x789854  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x7d59b0: ldur            x1, [fp, #-0x18]
    // 0x7d59b4: ldur            x2, [fp, #-0x40]
    // 0x7d59b8: ldur            x3, [fp, #-8]
    // 0x7d59bc: r0 = drawRRect()
    //     0x7d59bc: bl              #0x7899c4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x7d59c0: b               #0x7d59f4
    // 0x7d59c4: ldur            x1, [fp, #-0x18]
    // 0x7d59c8: ldur            x2, [fp, #-0x10]
    // 0x7d59cc: ldur            x3, [fp, #-0x28]
    // 0x7d59d0: r0 = drawRect()
    //     0x7d59d0: bl              #0x78be20  ; [dart:ui] _NativeCanvas::drawRect
    // 0x7d59d4: ldur            x1, [fp, #-0x18]
    // 0x7d59d8: ldur            x2, [fp, #-0x20]
    // 0x7d59dc: ldur            x3, [fp, #-0x30]
    // 0x7d59e0: r0 = drawRect()
    //     0x7d59e0: bl              #0x78be20  ; [dart:ui] _NativeCanvas::drawRect
    // 0x7d59e4: ldur            x1, [fp, #-0x18]
    // 0x7d59e8: ldur            x2, [fp, #-0x38]
    // 0x7d59ec: ldur            x3, [fp, #-8]
    // 0x7d59f0: r0 = drawRect()
    //     0x7d59f0: bl              #0x78be20  ; [dart:ui] _NativeCanvas::drawRect
    // 0x7d59f4: r0 = Null
    //     0x7d59f4: mov             x0, NULL
    // 0x7d59f8: LeaveFrame
    //     0x7d59f8: mov             SP, fp
    //     0x7d59fc: ldp             fp, lr, [SP], #0x10
    // 0x7d5a00: ret
    //     0x7d5a00: ret             
    // 0x7d5a04: r0 = StackOverflowSharedWithFPURegs()
    //     0x7d5a04: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7d5a08: b               #0x7d5614
    // 0x7d5a0c: r9 = _pixelSize
    //     0x7d5a0c: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1d8] Field <_PaintMetrics@2040312174._pixelSize@2040312174>: late final (offset: 0x20)
    //     0x7d5a10: ldr             x9, [x9, #0x1d8]
    // 0x7d5a14: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x7d5a14: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x7d5a18: r9 = _inset
    //     0x7d5a18: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1d0] Field <_PaintMetrics@2040312174._inset@2040312174>: late final (offset: 0x28)
    //     0x7d5a1c: ldr             x9, [x9, #0x1d0]
    // 0x7d5a20: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x7d5a20: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x7d5a24: r9 = _innerContentSize
    //     0x7d5a24: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1e0] Field <_PaintMetrics@2040312174._innerContentSize@2040312174>: late final (offset: 0x24)
    //     0x7d5a28: ldr             x9, [x9, #0x1e0]
    // 0x7d5a2c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x7d5a2c: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x7d5a30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d5a30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7d5a34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7d5a34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7d5a38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d5a38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7d5a3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7d5a3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7d5a40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d5a40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ shouldRepaint(/* No info */) {
    // ** addr: 0x8a1194, size: 0xd4
    // 0x8a1194: EnterFrame
    //     0x8a1194: stp             fp, lr, [SP, #-0x10]!
    //     0x8a1198: mov             fp, SP
    // 0x8a119c: r3 = LoadClassIdInstr(r2)
    //     0x8a119c: ldur            x3, [x2, #-1]
    //     0x8a11a0: ubfx            x3, x3, #0xc, #0x14
    // 0x8a11a4: r17 = 5454
    //     0x8a11a4: movz            x17, #0x154e
    // 0x8a11a8: cmp             x3, x17
    // 0x8a11ac: b.ne            #0x8a1240
    // 0x8a11b0: LoadField: r3 = r1->field_43
    //     0x8a11b0: ldur            w3, [x1, #0x43]
    // 0x8a11b4: DecompressPointer r3
    //     0x8a11b4: add             x3, x3, HEAP, lsl #32
    // 0x8a11b8: r16 = Sentinel
    //     0x8a11b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8a11bc: cmp             w3, w16
    // 0x8a11c0: b.eq            #0x8a1250
    // 0x8a11c4: LoadField: r4 = r2->field_43
    //     0x8a11c4: ldur            w4, [x2, #0x43]
    // 0x8a11c8: DecompressPointer r4
    //     0x8a11c8: add             x4, x4, HEAP, lsl #32
    // 0x8a11cc: r16 = Sentinel
    //     0x8a11cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8a11d0: cmp             w4, w16
    // 0x8a11d4: b.eq            #0x8a125c
    // 0x8a11d8: r5 = LoadInt32Instr(r3)
    //     0x8a11d8: sbfx            x5, x3, #1, #0x1f
    //     0x8a11dc: tbz             w3, #0, #0x8a11e4
    //     0x8a11e0: ldur            x5, [x3, #7]
    // 0x8a11e4: r3 = LoadInt32Instr(r4)
    //     0x8a11e4: sbfx            x3, x4, #1, #0x1f
    //     0x8a11e8: tbz             w4, #0, #0x8a11f0
    //     0x8a11ec: ldur            x3, [x4, #7]
    // 0x8a11f0: cmp             x5, x3
    // 0x8a11f4: b.ne            #0x8a1228
    // 0x8a11f8: LoadField: r3 = r1->field_3b
    //     0x8a11f8: ldur            w3, [x1, #0x3b]
    // 0x8a11fc: DecompressPointer r3
    //     0x8a11fc: add             x3, x3, HEAP, lsl #32
    // 0x8a1200: LoadField: r4 = r2->field_3b
    //     0x8a1200: ldur            w4, [x2, #0x3b]
    // 0x8a1204: DecompressPointer r4
    //     0x8a1204: add             x4, x4, HEAP, lsl #32
    // 0x8a1208: cmp             w3, w4
    // 0x8a120c: b.ne            #0x8a1228
    // 0x8a1210: LoadField: r3 = r1->field_2b
    //     0x8a1210: ldur            w3, [x1, #0x2b]
    // 0x8a1214: DecompressPointer r3
    //     0x8a1214: add             x3, x3, HEAP, lsl #32
    // 0x8a1218: LoadField: r1 = r2->field_2b
    //     0x8a1218: ldur            w1, [x2, #0x2b]
    // 0x8a121c: DecompressPointer r1
    //     0x8a121c: add             x1, x1, HEAP, lsl #32
    // 0x8a1220: cmp             w3, w1
    // 0x8a1224: b.eq            #0x8a1230
    // 0x8a1228: r0 = true
    //     0x8a1228: add             x0, NULL, #0x20  ; true
    // 0x8a122c: b               #0x8a1234
    // 0x8a1230: r0 = false
    //     0x8a1230: add             x0, NULL, #0x30  ; false
    // 0x8a1234: LeaveFrame
    //     0x8a1234: mov             SP, fp
    //     0x8a1238: ldp             fp, lr, [SP], #0x10
    // 0x8a123c: ret
    //     0x8a123c: ret             
    // 0x8a1240: r0 = true
    //     0x8a1240: add             x0, NULL, #0x20  ; true
    // 0x8a1244: LeaveFrame
    //     0x8a1244: mov             SP, fp
    //     0x8a1248: ldp             fp, lr, [SP], #0x10
    // 0x8a124c: ret
    //     0x8a124c: ret             
    // 0x8a1250: r9 = _calcVersion
    //     0x8a1250: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1a0] Field <QrPainter._calcVersion@2040312174>: late final (offset: 0x44)
    //     0x8a1254: ldr             x9, [x9, #0x1a0]
    // 0x8a1258: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8a1258: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8a125c: r9 = _calcVersion
    //     0x8a125c: add             x9, PP, #0x5b, lsl #12  ; [pp+0x5b1a0] Field <QrPainter._calcVersion@2040312174>: late final (offset: 0x44)
    //     0x8a1260: ldr             x9, [x9, #0x1a0]
    // 0x8a1264: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8a1264: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ QrPainter.withQr(/* No info */) {
    // ** addr: 0xa45750, size: 0x194
    // 0xa45750: EnterFrame
    //     0xa45750: stp             fp, lr, [SP, #-0x10]!
    //     0xa45754: mov             fp, SP
    // 0xa45758: AllocStack(0x30)
    //     0xa45758: sub             SP, SP, #0x30
    // 0xa4575c: r0 = Sentinel
    //     0xa4575c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa45760: stur            x1, [fp, #-8]
    // 0xa45764: mov             x16, x3
    // 0xa45768: mov             x3, x1
    // 0xa4576c: mov             x1, x16
    // 0xa45770: stur            x2, [fp, #-0x10]
    // 0xa45774: stur            x1, [fp, #-0x18]
    // 0xa45778: CheckStackOverflow
    //     0xa45778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4577c: cmp             SP, x16
    //     0xa45780: b.ls            #0xa458dc
    // 0xa45784: StoreField: r3->field_3f = r0
    //     0xa45784: stur            w0, [x3, #0x3f]
    // 0xa45788: StoreField: r3->field_43 = r0
    //     0xa45788: stur            w0, [x3, #0x43]
    // 0xa4578c: r0 = PaintCache()
    //     0xa4578c: bl              #0xa4a294  ; AllocatePaintCacheStub -> PaintCache (size=0x10)
    // 0xa45790: mov             x1, x0
    // 0xa45794: stur            x0, [fp, #-0x20]
    // 0xa45798: r0 = PaintCache()
    //     0xa45798: bl              #0xa4a1f8  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::PaintCache
    // 0xa4579c: ldur            x0, [fp, #-0x20]
    // 0xa457a0: ldur            x1, [fp, #-8]
    // 0xa457a4: StoreField: r1->field_47 = r0
    //     0xa457a4: stur            w0, [x1, #0x47]
    //     0xa457a8: ldurb           w16, [x1, #-1]
    //     0xa457ac: ldurb           w17, [x0, #-1]
    //     0xa457b0: and             x16, x17, x16, lsr #2
    //     0xa457b4: tst             x16, HEAP, lsr #32
    //     0xa457b8: b.eq            #0xa457c0
    //     0xa457bc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa457c0: r0 = Instance_Color
    //     0xa457c0: ldr             x0, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0xa457c4: StoreField: r1->field_1b = r0
    //     0xa457c4: stur            w0, [x1, #0x1b]
    // 0xa457c8: r0 = Instance_Color
    //     0xa457c8: add             x0, PP, #0x4e, lsl #12  ; [pp+0x4ed60] Obj!Color@e28111
    //     0xa457cc: ldr             x0, [x0, #0xd60]
    // 0xa457d0: StoreField: r1->field_23 = r0
    //     0xa457d0: stur            w0, [x1, #0x23]
    // 0xa457d4: r0 = true
    //     0xa457d4: add             x0, NULL, #0x20  ; true
    // 0xa457d8: StoreField: r1->field_27 = r0
    //     0xa457d8: stur            w0, [x1, #0x27]
    // 0xa457dc: ldur            x0, [fp, #-0x10]
    // 0xa457e0: StoreField: r1->field_2b = r0
    //     0xa457e0: stur            w0, [x1, #0x2b]
    //     0xa457e4: ldurb           w16, [x1, #-1]
    //     0xa457e8: ldurb           w17, [x0, #-1]
    //     0xa457ec: and             x16, x17, x16, lsr #2
    //     0xa457f0: tst             x16, HEAP, lsr #32
    //     0xa457f4: b.eq            #0xa457fc
    //     0xa457f8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa457fc: r0 = Instance_QrEmbeddedImageStyle
    //     0xa457fc: add             x0, PP, #0x47, lsl #12  ; [pp+0x47b60] Obj!QrEmbeddedImageStyle@e0c0f1
    //     0xa45800: ldr             x0, [x0, #0xb60]
    // 0xa45804: StoreField: r1->field_2f = r0
    //     0xa45804: stur            w0, [x1, #0x2f]
    // 0xa45808: r0 = Instance_QrEyeStyle
    //     0xa45808: add             x0, PP, #0x47, lsl #12  ; [pp+0x47b70] Obj!QrEyeStyle@e0c141
    //     0xa4580c: ldr             x0, [x0, #0xb70]
    // 0xa45810: StoreField: r1->field_33 = r0
    //     0xa45810: stur            w0, [x1, #0x33]
    // 0xa45814: r0 = Instance_QrDataModuleStyle
    //     0xa45814: add             x0, PP, #0x47, lsl #12  ; [pp+0x47b78] Obj!QrDataModuleStyle@e0c121
    //     0xa45818: ldr             x0, [x0, #0xb78]
    // 0xa4581c: StoreField: r1->field_37 = r0
    //     0xa4581c: stur            w0, [x1, #0x37]
    // 0xa45820: ldur            x0, [fp, #-0x18]
    // 0xa45824: StoreField: r1->field_3b = r0
    //     0xa45824: stur            w0, [x1, #0x3b]
    //     0xa45828: ldurb           w16, [x1, #-1]
    //     0xa4582c: ldurb           w17, [x0, #-1]
    //     0xa45830: and             x16, x17, x16, lsr #2
    //     0xa45834: tst             x16, HEAP, lsr #32
    //     0xa45838: b.eq            #0xa45840
    //     0xa4583c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa45840: ldur            x0, [fp, #-0x18]
    // 0xa45844: LoadField: r2 = r0->field_7
    //     0xa45844: ldur            x2, [x0, #7]
    // 0xa45848: stur            x2, [fp, #-0x28]
    // 0xa4584c: StoreField: r1->field_b = r2
    //     0xa4584c: stur            x2, [x1, #0xb]
    // 0xa45850: r0 = 1
    //     0xa45850: movz            x0, #0x1
    // 0xa45854: StoreField: r1->field_13 = r0
    //     0xa45854: stur            x0, [x1, #0x13]
    // 0xa45858: LoadField: r0 = r1->field_43
    //     0xa45858: ldur            w0, [x1, #0x43]
    // 0xa4585c: DecompressPointer r0
    //     0xa4585c: add             x0, x0, HEAP, lsl #32
    // 0xa45860: r16 = Sentinel
    //     0xa45860: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa45864: cmp             w0, w16
    // 0xa45868: b.ne            #0xa45878
    // 0xa4586c: mov             x3, x2
    // 0xa45870: mov             x2, x1
    // 0xa45874: b               #0xa45890
    // 0xa45878: r16 = "_calcVersion@2040312174"
    //     0xa45878: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d00] "_calcVersion@2040312174"
    //     0xa4587c: ldr             x16, [x16, #0xd00]
    // 0xa45880: str             x16, [SP]
    // 0xa45884: r0 = _throwFieldAlreadyInitialized()
    //     0xa45884: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xa45888: ldur            x2, [fp, #-8]
    // 0xa4588c: ldur            x3, [fp, #-0x28]
    // 0xa45890: r0 = BoxInt64Instr(r3)
    //     0xa45890: sbfiz           x0, x3, #1, #0x1f
    //     0xa45894: cmp             x3, x0, asr #1
    //     0xa45898: b.eq            #0xa458a4
    //     0xa4589c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa458a0: stur            x3, [x0, #7]
    // 0xa458a4: StoreField: r2->field_43 = r0
    //     0xa458a4: stur            w0, [x2, #0x43]
    //     0xa458a8: tbz             w0, #0, #0xa458c4
    //     0xa458ac: ldurb           w16, [x2, #-1]
    //     0xa458b0: ldurb           w17, [x0, #-1]
    //     0xa458b4: and             x16, x17, x16, lsr #2
    //     0xa458b8: tst             x16, HEAP, lsr #32
    //     0xa458bc: b.eq            #0xa458c4
    //     0xa458c0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa458c4: mov             x1, x2
    // 0xa458c8: r0 = _initPaints()
    //     0xa458c8: bl              #0xa458e4  ; [package:qr_flutter/src/qr_painter.dart] QrPainter::_initPaints
    // 0xa458cc: r0 = Null
    //     0xa458cc: mov             x0, NULL
    // 0xa458d0: LeaveFrame
    //     0xa458d0: mov             SP, fp
    //     0xa458d4: ldp             fp, lr, [SP], #0x10
    // 0xa458d8: ret
    //     0xa458d8: ret             
    // 0xa458dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa458dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa458e0: b               #0xa45784
  }
  _ _initPaints(/* No info */) {
    // ** addr: 0xa458e4, size: 0x274
    // 0xa458e4: EnterFrame
    //     0xa458e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa458e8: mov             fp, SP
    // 0xa458ec: AllocStack(0x40)
    //     0xa458ec: sub             SP, SP, #0x40
    // 0xa458f0: SetupParameters(QrPainter this /* r1 => r0, fp-0x8 */)
    //     0xa458f0: mov             x0, x1
    //     0xa458f4: stur            x1, [fp, #-8]
    // 0xa458f8: CheckStackOverflow
    //     0xa458f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa458fc: cmp             SP, x16
    //     0xa45900: b.ls            #0xa45b48
    // 0xa45904: LoadField: r2 = r0->field_3b
    //     0xa45904: ldur            w2, [x0, #0x3b]
    // 0xa45908: DecompressPointer r2
    //     0xa45908: add             x2, x2, HEAP, lsl #32
    // 0xa4590c: r1 = Null
    //     0xa4590c: mov             x1, NULL
    // 0xa45910: r0 = QrImage()
    //     0xa45910: bl              #0xa45cc0  ; [package:qr/src/qr_image.dart] QrImage::QrImage
    // 0xa45914: ldur            x1, [fp, #-8]
    // 0xa45918: StoreField: r1->field_3f = r0
    //     0xa45918: stur            w0, [x1, #0x3f]
    //     0xa4591c: ldurb           w16, [x1, #-1]
    //     0xa45920: ldurb           w17, [x0, #-1]
    //     0xa45924: and             x16, x17, x16, lsr #2
    //     0xa45928: tst             x16, HEAP, lsr #32
    //     0xa4592c: b.eq            #0xa45934
    //     0xa45930: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa45934: LoadField: r0 = r1->field_47
    //     0xa45934: ldur            w0, [x1, #0x47]
    // 0xa45938: DecompressPointer r0
    //     0xa45938: add             x0, x0, HEAP, lsl #32
    // 0xa4593c: stur            x0, [fp, #-0x10]
    // 0xa45940: r16 = 136
    //     0xa45940: movz            x16, #0x88
    // 0xa45944: stp             x16, NULL, [SP]
    // 0xa45948: r0 = ByteData()
    //     0xa45948: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xa4594c: stur            x0, [fp, #-8]
    // 0xa45950: r0 = Paint()
    //     0xa45950: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xa45954: mov             x1, x0
    // 0xa45958: ldur            x0, [fp, #-8]
    // 0xa4595c: StoreField: r1->field_7 = r0
    //     0xa4595c: stur            w0, [x1, #7]
    // 0xa45960: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa45960: ldur            w2, [x0, #0x17]
    // 0xa45964: DecompressPointer r2
    //     0xa45964: add             x2, x2, HEAP, lsl #32
    // 0xa45968: LoadField: r0 = r2->field_7
    //     0xa45968: ldur            x0, [x2, #7]
    // 0xa4596c: str             wzr, [x0, #0x1c]
    // 0xa45970: mov             x2, x1
    // 0xa45974: ldur            x1, [fp, #-0x10]
    // 0xa45978: r3 = Instance_QrCodeElement
    //     0xa45978: add             x3, PP, #0x57, lsl #12  ; [pp+0x57d08] Obj!QrCodeElement@e2e3c1
    //     0xa4597c: ldr             x3, [x3, #0xd08]
    // 0xa45980: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xa45980: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xa45984: r0 = cache()
    //     0xa45984: bl              #0xa45b58  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::cache
    // 0xa45988: r16 = 136
    //     0xa45988: movz            x16, #0x88
    // 0xa4598c: stp             x16, NULL, [SP]
    // 0xa45990: r0 = ByteData()
    //     0xa45990: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xa45994: stur            x0, [fp, #-8]
    // 0xa45998: r0 = Paint()
    //     0xa45998: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xa4599c: mov             x1, x0
    // 0xa459a0: ldur            x0, [fp, #-8]
    // 0xa459a4: StoreField: r1->field_7 = r0
    //     0xa459a4: stur            w0, [x1, #7]
    // 0xa459a8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa459a8: ldur            w2, [x0, #0x17]
    // 0xa459ac: DecompressPointer r2
    //     0xa459ac: add             x2, x2, HEAP, lsl #32
    // 0xa459b0: LoadField: r0 = r2->field_7
    //     0xa459b0: ldur            x0, [x2, #7]
    // 0xa459b4: str             wzr, [x0, #0x1c]
    // 0xa459b8: mov             x2, x1
    // 0xa459bc: ldur            x1, [fp, #-0x10]
    // 0xa459c0: r3 = Instance_QrCodeElement
    //     0xa459c0: add             x3, PP, #0x57, lsl #12  ; [pp+0x57d10] Obj!QrCodeElement@e2e3a1
    //     0xa459c4: ldr             x3, [x3, #0xd10]
    // 0xa459c8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xa459c8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xa459cc: r0 = cache()
    //     0xa459cc: bl              #0xa45b58  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::cache
    // 0xa459d0: ldur            x1, [fp, #-0x10]
    // 0xa459d4: LoadField: r0 = r1->field_b
    //     0xa459d4: ldur            w0, [x1, #0xb]
    // 0xa459d8: DecompressPointer r0
    //     0xa459d8: add             x0, x0, HEAP, lsl #32
    // 0xa459dc: stur            x0, [fp, #-0x20]
    // 0xa459e0: r3 = 0
    //     0xa459e0: movz            x3, #0
    // 0xa459e4: r2 = const [Instance of 'FinderPatternPosition', Instance of 'FinderPatternPosition', Instance of 'FinderPatternPosition']
    //     0xa459e4: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d18] List<FinderPatternPosition>(3)
    //     0xa459e8: ldr             x2, [x2, #0xd18]
    // 0xa459ec: CheckStackOverflow
    //     0xa459ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa459f0: cmp             SP, x16
    //     0xa459f4: b.ls            #0xa45b50
    // 0xa459f8: cmp             x3, #3
    // 0xa459fc: b.ge            #0xa45b38
    // 0xa45a00: ArrayLoad: r4 = r2[r3]  ; Unknown_4
    //     0xa45a00: add             x16, x2, x3, lsl #2
    //     0xa45a04: ldur            w4, [x16, #0xf]
    // 0xa45a08: DecompressPointer r4
    //     0xa45a08: add             x4, x4, HEAP, lsl #32
    // 0xa45a0c: stur            x4, [fp, #-8]
    // 0xa45a10: add             x5, x3, #1
    // 0xa45a14: stur            x5, [fp, #-0x18]
    // 0xa45a18: r16 = 136
    //     0xa45a18: movz            x16, #0x88
    // 0xa45a1c: stp             x16, NULL, [SP]
    // 0xa45a20: r0 = ByteData()
    //     0xa45a20: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xa45a24: stur            x0, [fp, #-0x28]
    // 0xa45a28: r0 = Paint()
    //     0xa45a28: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xa45a2c: mov             x4, x0
    // 0xa45a30: ldur            x0, [fp, #-0x28]
    // 0xa45a34: stur            x4, [fp, #-0x30]
    // 0xa45a38: StoreField: r4->field_7 = r0
    //     0xa45a38: stur            w0, [x4, #7]
    // 0xa45a3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa45a3c: ldur            w1, [x0, #0x17]
    // 0xa45a40: DecompressPointer r1
    //     0xa45a40: add             x1, x1, HEAP, lsl #32
    // 0xa45a44: LoadField: r0 = r1->field_7
    //     0xa45a44: ldur            x0, [x1, #7]
    // 0xa45a48: r5 = 1
    //     0xa45a48: movz            x5, #0x1
    // 0xa45a4c: str             w5, [x0, #0x1c]
    // 0xa45a50: ldur            x1, [fp, #-0x10]
    // 0xa45a54: ldur            x3, [fp, #-8]
    // 0xa45a58: r2 = Instance_QrCodeElement
    //     0xa45a58: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d20] Obj!QrCodeElement@e2e381
    //     0xa45a5c: ldr             x2, [x2, #0xd20]
    // 0xa45a60: r0 = _cacheKey()
    //     0xa45a60: bl              #0x7d5420  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::_cacheKey
    // 0xa45a64: ldur            x1, [fp, #-0x20]
    // 0xa45a68: mov             x2, x0
    // 0xa45a6c: ldur            x3, [fp, #-0x30]
    // 0xa45a70: r0 = []=()
    //     0xa45a70: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa45a74: r16 = 136
    //     0xa45a74: movz            x16, #0x88
    // 0xa45a78: stp             x16, NULL, [SP]
    // 0xa45a7c: r0 = ByteData()
    //     0xa45a7c: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xa45a80: stur            x0, [fp, #-0x28]
    // 0xa45a84: r0 = Paint()
    //     0xa45a84: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xa45a88: mov             x4, x0
    // 0xa45a8c: ldur            x0, [fp, #-0x28]
    // 0xa45a90: stur            x4, [fp, #-0x30]
    // 0xa45a94: StoreField: r4->field_7 = r0
    //     0xa45a94: stur            w0, [x4, #7]
    // 0xa45a98: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa45a98: ldur            w1, [x0, #0x17]
    // 0xa45a9c: DecompressPointer r1
    //     0xa45a9c: add             x1, x1, HEAP, lsl #32
    // 0xa45aa0: LoadField: r0 = r1->field_7
    //     0xa45aa0: ldur            x0, [x1, #7]
    // 0xa45aa4: r5 = 1
    //     0xa45aa4: movz            x5, #0x1
    // 0xa45aa8: str             w5, [x0, #0x1c]
    // 0xa45aac: ldur            x1, [fp, #-0x10]
    // 0xa45ab0: ldur            x3, [fp, #-8]
    // 0xa45ab4: r2 = Instance_QrCodeElement
    //     0xa45ab4: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d28] Obj!QrCodeElement@e2e361
    //     0xa45ab8: ldr             x2, [x2, #0xd28]
    // 0xa45abc: r0 = _cacheKey()
    //     0xa45abc: bl              #0x7d5420  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::_cacheKey
    // 0xa45ac0: ldur            x1, [fp, #-0x20]
    // 0xa45ac4: mov             x2, x0
    // 0xa45ac8: ldur            x3, [fp, #-0x30]
    // 0xa45acc: r0 = []=()
    //     0xa45acc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa45ad0: r16 = 136
    //     0xa45ad0: movz            x16, #0x88
    // 0xa45ad4: stp             x16, NULL, [SP]
    // 0xa45ad8: r0 = ByteData()
    //     0xa45ad8: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xa45adc: stur            x0, [fp, #-0x28]
    // 0xa45ae0: r0 = Paint()
    //     0xa45ae0: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xa45ae4: mov             x4, x0
    // 0xa45ae8: ldur            x0, [fp, #-0x28]
    // 0xa45aec: stur            x4, [fp, #-0x30]
    // 0xa45af0: StoreField: r4->field_7 = r0
    //     0xa45af0: stur            w0, [x4, #7]
    // 0xa45af4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa45af4: ldur            w1, [x0, #0x17]
    // 0xa45af8: DecompressPointer r1
    //     0xa45af8: add             x1, x1, HEAP, lsl #32
    // 0xa45afc: LoadField: r0 = r1->field_7
    //     0xa45afc: ldur            x0, [x1, #7]
    // 0xa45b00: str             wzr, [x0, #0x1c]
    // 0xa45b04: ldur            x1, [fp, #-0x10]
    // 0xa45b08: ldur            x3, [fp, #-8]
    // 0xa45b0c: r2 = Instance_QrCodeElement
    //     0xa45b0c: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d30] Obj!QrCodeElement@e2e341
    //     0xa45b10: ldr             x2, [x2, #0xd30]
    // 0xa45b14: r0 = _cacheKey()
    //     0xa45b14: bl              #0x7d5420  ; [package:qr_flutter/src/paint_cache.dart] PaintCache::_cacheKey
    // 0xa45b18: ldur            x1, [fp, #-0x20]
    // 0xa45b1c: mov             x2, x0
    // 0xa45b20: ldur            x3, [fp, #-0x30]
    // 0xa45b24: r0 = []=()
    //     0xa45b24: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa45b28: ldur            x3, [fp, #-0x18]
    // 0xa45b2c: ldur            x1, [fp, #-0x10]
    // 0xa45b30: ldur            x0, [fp, #-0x20]
    // 0xa45b34: b               #0xa459e4
    // 0xa45b38: r0 = Null
    //     0xa45b38: mov             x0, NULL
    // 0xa45b3c: LeaveFrame
    //     0xa45b3c: mov             SP, fp
    //     0xa45b40: ldp             fp, lr, [SP], #0x10
    // 0xa45b44: ret
    //     0xa45b44: ret             
    // 0xa45b48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa45b48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45b4c: b               #0xa45904
    // 0xa45b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa45b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa45b54: b               #0xa459f8
  }
}
