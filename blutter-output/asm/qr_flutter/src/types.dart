// lib: , url: package:qr_flutter/src/types.dart

// class id: 1051074, size: 0x8
class :: {
}

// class id: 530, size: 0x2c, field offset: 0x8
//   const constructor, 
class QrEmbeddedImageStyle extends Object {

  bool field_10;
  _Double field_14;
  EmbeddedImageShape field_1c;
  _Mint field_20;

  _ ==(/* No info */) {
    // ** addr: 0xd7d794, size: 0x3c
    // 0xd7d794: ldr             x1, [SP]
    // 0xd7d798: cmp             w1, NULL
    // 0xd7d79c: b.ne            #0xd7d7a8
    // 0xd7d7a0: r0 = false
    //     0xd7d7a0: add             x0, NULL, #0x30  ; false
    // 0xd7d7a4: ret
    //     0xd7d7a4: ret             
    // 0xd7d7a8: r2 = 60
    //     0xd7d7a8: movz            x2, #0x3c
    // 0xd7d7ac: branchIfSmi(r1, 0xd7d7b8)
    //     0xd7d7ac: tbz             w1, #0, #0xd7d7b8
    // 0xd7d7b0: r2 = LoadClassIdInstr(r1)
    //     0xd7d7b0: ldur            x2, [x1, #-1]
    //     0xd7d7b4: ubfx            x2, x2, #0xc, #0x14
    // 0xd7d7b8: cmp             x2, #0x212
    // 0xd7d7bc: b.ne            #0xd7d7c8
    // 0xd7d7c0: r0 = true
    //     0xd7d7c0: add             x0, NULL, #0x20  ; true
    // 0xd7d7c4: ret
    //     0xd7d7c4: ret             
    // 0xd7d7c8: r0 = false
    //     0xd7d7c8: add             x0, NULL, #0x30  ; false
    // 0xd7d7cc: ret
    //     0xd7d7cc: ret             
  }
}

// class id: 531, size: 0x20, field offset: 0x8
//   const constructor, 
class QrDataModuleStyle extends Object {

  QrDataModuleShape field_8;
  _Mint field_10;
  bool field_18;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf32e4, size: 0x4c
    // 0xbf32e4: EnterFrame
    //     0xbf32e4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf32e8: mov             fp, SP
    // 0xbf32ec: AllocStack(0x8)
    //     0xbf32ec: sub             SP, SP, #8
    // 0xbf32f0: CheckStackOverflow
    //     0xbf32f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf32f4: cmp             SP, x16
    //     0xbf32f8: b.ls            #0xbf3328
    // 0xbf32fc: r16 = Instance_QrDataModuleShape
    //     0xbf32fc: add             x16, PP, #0x51, lsl #12  ; [pp+0x51518] Obj!QrDataModuleShape@e2e2a1
    //     0xbf3300: ldr             x16, [x16, #0x518]
    // 0xbf3304: str             x16, [SP]
    // 0xbf3308: r0 = _getHash()
    //     0xbf3308: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0xbf330c: r1 = LoadInt32Instr(r0)
    //     0xbf330c: sbfx            x1, x0, #1, #0x1f
    // 0xbf3310: r16 = 2011
    //     0xbf3310: movz            x16, #0x7db
    // 0xbf3314: eor             x2, x1, x16
    // 0xbf3318: lsl             x0, x2, #1
    // 0xbf331c: LeaveFrame
    //     0xbf331c: mov             SP, fp
    //     0xbf3320: ldp             fp, lr, [SP], #0x10
    // 0xbf3324: ret
    //     0xbf3324: ret             
    // 0xbf3328: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3328: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf332c: b               #0xbf32fc
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d758, size: 0x3c
    // 0xd7d758: ldr             x1, [SP]
    // 0xd7d75c: cmp             w1, NULL
    // 0xd7d760: b.ne            #0xd7d76c
    // 0xd7d764: r0 = false
    //     0xd7d764: add             x0, NULL, #0x30  ; false
    // 0xd7d768: ret
    //     0xd7d768: ret             
    // 0xd7d76c: r2 = 60
    //     0xd7d76c: movz            x2, #0x3c
    // 0xd7d770: branchIfSmi(r1, 0xd7d77c)
    //     0xd7d770: tbz             w1, #0, #0xd7d77c
    // 0xd7d774: r2 = LoadClassIdInstr(r1)
    //     0xd7d774: ldur            x2, [x1, #-1]
    //     0xd7d778: ubfx            x2, x2, #0xc, #0x14
    // 0xd7d77c: cmp             x2, #0x213
    // 0xd7d780: b.ne            #0xd7d78c
    // 0xd7d784: r0 = true
    //     0xd7d784: add             x0, NULL, #0x20  ; true
    // 0xd7d788: ret
    //     0xd7d788: ret             
    // 0xd7d78c: r0 = false
    //     0xd7d78c: add             x0, NULL, #0x30  ; false
    // 0xd7d790: ret
    //     0xd7d790: ret             
  }
}

// class id: 532, size: 0x18, field offset: 0x8
//   const constructor, 
class QrEyeStyle extends Object {

  QrEyeShape field_8;
  _Mint field_10;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3298, size: 0x4c
    // 0xbf3298: EnterFrame
    //     0xbf3298: stp             fp, lr, [SP, #-0x10]!
    //     0xbf329c: mov             fp, SP
    // 0xbf32a0: AllocStack(0x8)
    //     0xbf32a0: sub             SP, SP, #8
    // 0xbf32a4: CheckStackOverflow
    //     0xbf32a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf32a8: cmp             SP, x16
    //     0xbf32ac: b.ls            #0xbf32dc
    // 0xbf32b0: r16 = Instance_QrEyeShape
    //     0xbf32b0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51528] Obj!QrEyeShape@e2e2c1
    //     0xbf32b4: ldr             x16, [x16, #0x528]
    // 0xbf32b8: str             x16, [SP]
    // 0xbf32bc: r0 = _getHash()
    //     0xbf32bc: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0xbf32c0: r1 = LoadInt32Instr(r0)
    //     0xbf32c0: sbfx            x1, x0, #1, #0x1f
    // 0xbf32c4: r16 = 2011
    //     0xbf32c4: movz            x16, #0x7db
    // 0xbf32c8: eor             x2, x1, x16
    // 0xbf32cc: lsl             x0, x2, #1
    // 0xbf32d0: LeaveFrame
    //     0xbf32d0: mov             SP, fp
    //     0xbf32d4: ldp             fp, lr, [SP], #0x10
    // 0xbf32d8: ret
    //     0xbf32d8: ret             
    // 0xbf32dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf32dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf32e0: b               #0xbf32b0
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d71c, size: 0x3c
    // 0xd7d71c: ldr             x1, [SP]
    // 0xd7d720: cmp             w1, NULL
    // 0xd7d724: b.ne            #0xd7d730
    // 0xd7d728: r0 = false
    //     0xd7d728: add             x0, NULL, #0x30  ; false
    // 0xd7d72c: ret
    //     0xd7d72c: ret             
    // 0xd7d730: r2 = 60
    //     0xd7d730: movz            x2, #0x3c
    // 0xd7d734: branchIfSmi(r1, 0xd7d740)
    //     0xd7d734: tbz             w1, #0, #0xd7d740
    // 0xd7d738: r2 = LoadClassIdInstr(r1)
    //     0xd7d738: ldur            x2, [x1, #-1]
    //     0xd7d73c: ubfx            x2, x2, #0xc, #0x14
    // 0xd7d740: cmp             x2, #0x214
    // 0xd7d744: b.ne            #0xd7d750
    // 0xd7d748: r0 = true
    //     0xd7d748: add             x0, NULL, #0x20  ; true
    // 0xd7d74c: ret
    //     0xd7d74c: ret             
    // 0xd7d750: r0 = false
    //     0xd7d750: add             x0, NULL, #0x30  ; false
    // 0xd7d754: ret
    //     0xd7d754: ret             
  }
}

// class id: 6779, size: 0x14, field offset: 0x14
enum EmbeddedImageShape extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e694, size: 0x64
    // 0xc4e694: EnterFrame
    //     0xc4e694: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e698: mov             fp, SP
    // 0xc4e69c: AllocStack(0x10)
    //     0xc4e69c: sub             SP, SP, #0x10
    // 0xc4e6a0: SetupParameters(EmbeddedImageShape this /* r1 => r0, fp-0x8 */)
    //     0xc4e6a0: mov             x0, x1
    //     0xc4e6a4: stur            x1, [fp, #-8]
    // 0xc4e6a8: CheckStackOverflow
    //     0xc4e6a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e6ac: cmp             SP, x16
    //     0xc4e6b0: b.ls            #0xc4e6f0
    // 0xc4e6b4: r1 = Null
    //     0xc4e6b4: mov             x1, NULL
    // 0xc4e6b8: r2 = 4
    //     0xc4e6b8: movz            x2, #0x4
    // 0xc4e6bc: r0 = AllocateArray()
    //     0xc4e6bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e6c0: r16 = "EmbeddedImageShape."
    //     0xc4e6c0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51520] "EmbeddedImageShape."
    //     0xc4e6c4: ldr             x16, [x16, #0x520]
    // 0xc4e6c8: StoreField: r0->field_f = r16
    //     0xc4e6c8: stur            w16, [x0, #0xf]
    // 0xc4e6cc: ldur            x1, [fp, #-8]
    // 0xc4e6d0: LoadField: r2 = r1->field_f
    //     0xc4e6d0: ldur            w2, [x1, #0xf]
    // 0xc4e6d4: DecompressPointer r2
    //     0xc4e6d4: add             x2, x2, HEAP, lsl #32
    // 0xc4e6d8: StoreField: r0->field_13 = r2
    //     0xc4e6d8: stur            w2, [x0, #0x13]
    // 0xc4e6dc: str             x0, [SP]
    // 0xc4e6e0: r0 = _interpolate()
    //     0xc4e6e0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e6e4: LeaveFrame
    //     0xc4e6e4: mov             SP, fp
    //     0xc4e6e8: ldp             fp, lr, [SP], #0x10
    // 0xc4e6ec: ret
    //     0xc4e6ec: ret             
    // 0xc4e6f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e6f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e6f4: b               #0xc4e6b4
  }
}

// class id: 6780, size: 0x14, field offset: 0x14
enum QrDataModuleShape extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e630, size: 0x64
    // 0xc4e630: EnterFrame
    //     0xc4e630: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e634: mov             fp, SP
    // 0xc4e638: AllocStack(0x10)
    //     0xc4e638: sub             SP, SP, #0x10
    // 0xc4e63c: SetupParameters(QrDataModuleShape this /* r1 => r0, fp-0x8 */)
    //     0xc4e63c: mov             x0, x1
    //     0xc4e640: stur            x1, [fp, #-8]
    // 0xc4e644: CheckStackOverflow
    //     0xc4e644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e648: cmp             SP, x16
    //     0xc4e64c: b.ls            #0xc4e68c
    // 0xc4e650: r1 = Null
    //     0xc4e650: mov             x1, NULL
    // 0xc4e654: r2 = 4
    //     0xc4e654: movz            x2, #0x4
    // 0xc4e658: r0 = AllocateArray()
    //     0xc4e658: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e65c: r16 = "QrDataModuleShape."
    //     0xc4e65c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51510] "QrDataModuleShape."
    //     0xc4e660: ldr             x16, [x16, #0x510]
    // 0xc4e664: StoreField: r0->field_f = r16
    //     0xc4e664: stur            w16, [x0, #0xf]
    // 0xc4e668: ldur            x1, [fp, #-8]
    // 0xc4e66c: LoadField: r2 = r1->field_f
    //     0xc4e66c: ldur            w2, [x1, #0xf]
    // 0xc4e670: DecompressPointer r2
    //     0xc4e670: add             x2, x2, HEAP, lsl #32
    // 0xc4e674: StoreField: r0->field_13 = r2
    //     0xc4e674: stur            w2, [x0, #0x13]
    // 0xc4e678: str             x0, [SP]
    // 0xc4e67c: r0 = _interpolate()
    //     0xc4e67c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e680: LeaveFrame
    //     0xc4e680: mov             SP, fp
    //     0xc4e684: ldp             fp, lr, [SP], #0x10
    // 0xc4e688: ret
    //     0xc4e688: ret             
    // 0xc4e68c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e68c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e690: b               #0xc4e650
  }
}

// class id: 6781, size: 0x14, field offset: 0x14
enum QrEyeShape extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e5cc, size: 0x64
    // 0xc4e5cc: EnterFrame
    //     0xc4e5cc: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e5d0: mov             fp, SP
    // 0xc4e5d4: AllocStack(0x10)
    //     0xc4e5d4: sub             SP, SP, #0x10
    // 0xc4e5d8: SetupParameters(QrEyeShape this /* r1 => r0, fp-0x8 */)
    //     0xc4e5d8: mov             x0, x1
    //     0xc4e5dc: stur            x1, [fp, #-8]
    // 0xc4e5e0: CheckStackOverflow
    //     0xc4e5e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e5e4: cmp             SP, x16
    //     0xc4e5e8: b.ls            #0xc4e628
    // 0xc4e5ec: r1 = Null
    //     0xc4e5ec: mov             x1, NULL
    // 0xc4e5f0: r2 = 4
    //     0xc4e5f0: movz            x2, #0x4
    // 0xc4e5f4: r0 = AllocateArray()
    //     0xc4e5f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e5f8: r16 = "QrEyeShape."
    //     0xc4e5f8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51530] "QrEyeShape."
    //     0xc4e5fc: ldr             x16, [x16, #0x530]
    // 0xc4e600: StoreField: r0->field_f = r16
    //     0xc4e600: stur            w16, [x0, #0xf]
    // 0xc4e604: ldur            x1, [fp, #-8]
    // 0xc4e608: LoadField: r2 = r1->field_f
    //     0xc4e608: ldur            w2, [x1, #0xf]
    // 0xc4e60c: DecompressPointer r2
    //     0xc4e60c: add             x2, x2, HEAP, lsl #32
    // 0xc4e610: StoreField: r0->field_13 = r2
    //     0xc4e610: stur            w2, [x0, #0x13]
    // 0xc4e614: str             x0, [SP]
    // 0xc4e618: r0 = _interpolate()
    //     0xc4e618: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e61c: LeaveFrame
    //     0xc4e61c: mov             SP, fp
    //     0xc4e620: ldp             fp, lr, [SP], #0x10
    // 0xc4e624: ret
    //     0xc4e624: ret             
    // 0xc4e628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e628: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e62c: b               #0xc4e5ec
  }
}

// class id: 6782, size: 0x14, field offset: 0x14
enum FinderPatternPosition extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e568, size: 0x64
    // 0xc4e568: EnterFrame
    //     0xc4e568: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e56c: mov             fp, SP
    // 0xc4e570: AllocStack(0x10)
    //     0xc4e570: sub             SP, SP, #0x10
    // 0xc4e574: SetupParameters(FinderPatternPosition this /* r1 => r0, fp-0x8 */)
    //     0xc4e574: mov             x0, x1
    //     0xc4e578: stur            x1, [fp, #-8]
    // 0xc4e57c: CheckStackOverflow
    //     0xc4e57c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e580: cmp             SP, x16
    //     0xc4e584: b.ls            #0xc4e5c4
    // 0xc4e588: r1 = Null
    //     0xc4e588: mov             x1, NULL
    // 0xc4e58c: r2 = 4
    //     0xc4e58c: movz            x2, #0x4
    // 0xc4e590: r0 = AllocateArray()
    //     0xc4e590: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e594: r16 = "FinderPatternPosition."
    //     0xc4e594: add             x16, PP, #0x57, lsl #12  ; [pp+0x57d40] "FinderPatternPosition."
    //     0xc4e598: ldr             x16, [x16, #0xd40]
    // 0xc4e59c: StoreField: r0->field_f = r16
    //     0xc4e59c: stur            w16, [x0, #0xf]
    // 0xc4e5a0: ldur            x1, [fp, #-8]
    // 0xc4e5a4: LoadField: r2 = r1->field_f
    //     0xc4e5a4: ldur            w2, [x1, #0xf]
    // 0xc4e5a8: DecompressPointer r2
    //     0xc4e5a8: add             x2, x2, HEAP, lsl #32
    // 0xc4e5ac: StoreField: r0->field_13 = r2
    //     0xc4e5ac: stur            w2, [x0, #0x13]
    // 0xc4e5b0: str             x0, [SP]
    // 0xc4e5b4: r0 = _interpolate()
    //     0xc4e5b4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e5b8: LeaveFrame
    //     0xc4e5b8: mov             SP, fp
    //     0xc4e5bc: ldp             fp, lr, [SP], #0x10
    // 0xc4e5c0: ret
    //     0xc4e5c0: ret             
    // 0xc4e5c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e5c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e5c8: b               #0xc4e588
  }
}

// class id: 6783, size: 0x14, field offset: 0x14
enum QrCodeElement extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e504, size: 0x64
    // 0xc4e504: EnterFrame
    //     0xc4e504: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e508: mov             fp, SP
    // 0xc4e50c: AllocStack(0x10)
    //     0xc4e50c: sub             SP, SP, #0x10
    // 0xc4e510: SetupParameters(QrCodeElement this /* r1 => r0, fp-0x8 */)
    //     0xc4e510: mov             x0, x1
    //     0xc4e514: stur            x1, [fp, #-8]
    // 0xc4e518: CheckStackOverflow
    //     0xc4e518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e51c: cmp             SP, x16
    //     0xc4e520: b.ls            #0xc4e560
    // 0xc4e524: r1 = Null
    //     0xc4e524: mov             x1, NULL
    // 0xc4e528: r2 = 4
    //     0xc4e528: movz            x2, #0x4
    // 0xc4e52c: r0 = AllocateArray()
    //     0xc4e52c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e530: r16 = "QrCodeElement."
    //     0xc4e530: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b198] "QrCodeElement."
    //     0xc4e534: ldr             x16, [x16, #0x198]
    // 0xc4e538: StoreField: r0->field_f = r16
    //     0xc4e538: stur            w16, [x0, #0xf]
    // 0xc4e53c: ldur            x1, [fp, #-8]
    // 0xc4e540: LoadField: r2 = r1->field_f
    //     0xc4e540: ldur            w2, [x1, #0xf]
    // 0xc4e544: DecompressPointer r2
    //     0xc4e544: add             x2, x2, HEAP, lsl #32
    // 0xc4e548: StoreField: r0->field_13 = r2
    //     0xc4e548: stur            w2, [x0, #0x13]
    // 0xc4e54c: str             x0, [SP]
    // 0xc4e550: r0 = _interpolate()
    //     0xc4e550: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e554: LeaveFrame
    //     0xc4e554: mov             SP, fp
    //     0xc4e558: ldp             fp, lr, [SP], #0x10
    // 0xc4e55c: ret
    //     0xc4e55c: ret             
    // 0xc4e560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e560: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e564: b               #0xc4e524
  }
}
