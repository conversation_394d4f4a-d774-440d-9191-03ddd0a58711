// lib: , url: package:qr_flutter/src/validator.dart

// class id: 1051075, size: 0x8
class :: {
}

// class id: 528, size: 0x14, field offset: 0x8
class QrValidationResult extends Object {
}

// class id: 529, size: 0x8, field offset: 0x8
abstract class QrValidator extends Object {

  static _ validate(/* No info */) {
    // ** addr: 0xa446dc, size: 0x1a0
    // 0xa446dc: EnterFrame
    //     0xa446dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa446e0: mov             fp, SP
    // 0xa446e4: AllocStack(0x48)
    //     0xa446e4: sub             SP, SP, #0x48
    // 0xa446e8: SetupParameters(dynamic _ /* r1 => r2 */)
    //     0xa446e8: mov             x2, x1
    // 0xa446ec: CheckStackOverflow
    //     0xa446ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa446f0: cmp             SP, x16
    //     0xa446f4: b.ls            #0xa44874
    // 0xa446f8: r1 = Null
    //     0xa446f8: mov             x1, NULL
    // 0xa446fc: r0 = QrCode.fromData()
    //     0xa446fc: bl              #0xa44888  ; [package:qr/src/qr_code.dart] QrCode::QrCode.fromData
    // 0xa44700: stur            x0, [fp, #-0x40]
    // 0xa44704: r0 = QrValidationResult()
    //     0xa44704: bl              #0xa4487c  ; AllocateQrValidationResultStub -> QrValidationResult (size=0x14)
    // 0xa44708: mov             x1, x0
    // 0xa4470c: r0 = Instance_QrValidationStatus
    //     0xa4470c: add             x0, PP, #0x57, lsl #12  ; [pp+0x57ce8] Obj!QrValidationStatus@e2e261
    //     0xa44710: ldr             x0, [x0, #0xce8]
    // 0xa44714: StoreField: r1->field_7 = r0
    //     0xa44714: stur            w0, [x1, #7]
    // 0xa44718: ldur            x0, [fp, #-0x40]
    // 0xa4471c: StoreField: r1->field_b = r0
    //     0xa4471c: stur            w0, [x1, #0xb]
    // 0xa44720: mov             x0, x1
    // 0xa44724: LeaveFrame
    //     0xa44724: mov             SP, fp
    //     0xa44728: ldp             fp, lr, [SP], #0x10
    // 0xa4472c: ret
    //     0xa4472c: ret             
    // 0xa44730: sub             SP, fp, #0x48
    // 0xa44734: mov             x3, x1
    // 0xa44738: stur            x0, [fp, #-0x40]
    // 0xa4473c: stur            x1, [fp, #-0x48]
    // 0xa44740: r1 = 60
    //     0xa44740: movz            x1, #0x3c
    // 0xa44744: branchIfSmi(r0, 0xa44750)
    //     0xa44744: tbz             w0, #0, #0xa44750
    // 0xa44748: r1 = LoadClassIdInstr(r0)
    //     0xa44748: ldur            x1, [x0, #-1]
    //     0xa4474c: ubfx            x1, x1, #0xc, #0x14
    // 0xa44750: cmp             x1, #0x21b
    // 0xa44754: b.ne            #0xa44784
    // 0xa44758: r0 = QrValidationResult()
    //     0xa44758: bl              #0xa4487c  ; AllocateQrValidationResultStub -> QrValidationResult (size=0x14)
    // 0xa4475c: mov             x1, x0
    // 0xa44760: r0 = Instance_QrValidationStatus
    //     0xa44760: add             x0, PP, #0x57, lsl #12  ; [pp+0x57dd0] Obj!QrValidationStatus@e2e241
    //     0xa44764: ldr             x0, [x0, #0xdd0]
    // 0xa44768: StoreField: r1->field_7 = r0
    //     0xa44768: stur            w0, [x1, #7]
    // 0xa4476c: ldur            x4, [fp, #-0x40]
    // 0xa44770: StoreField: r1->field_f = r4
    //     0xa44770: stur            w4, [x1, #0xf]
    // 0xa44774: mov             x0, x1
    // 0xa44778: LeaveFrame
    //     0xa44778: mov             SP, fp
    //     0xa4477c: ldp             fp, lr, [SP], #0x10
    // 0xa44780: ret
    //     0xa44780: ret             
    // 0xa44784: mov             x4, x0
    // 0xa44788: mov             x0, x4
    // 0xa4478c: r2 = Null
    //     0xa4478c: mov             x2, NULL
    // 0xa44790: r1 = Null
    //     0xa44790: mov             x1, NULL
    // 0xa44794: cmp             w0, NULL
    // 0xa44798: b.eq            #0xa44824
    // 0xa4479c: branchIfSmi(r0, 0xa44824)
    //     0xa4479c: tbz             w0, #0, #0xa44824
    // 0xa447a0: r3 = LoadClassIdInstr(r0)
    //     0xa447a0: ldur            x3, [x0, #-1]
    //     0xa447a4: ubfx            x3, x3, #0xc, #0x14
    // 0xa447a8: r4 = LoadClassIdInstr(r0)
    //     0xa447a8: ldur            x4, [x0, #-1]
    //     0xa447ac: ubfx            x4, x4, #0xc, #0x14
    // 0xa447b0: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xa447b4: ldr             x3, [x3, #0x18]
    // 0xa447b8: ldr             x3, [x3, x4, lsl #3]
    // 0xa447bc: LoadField: r3 = r3->field_2b
    //     0xa447bc: ldur            w3, [x3, #0x2b]
    // 0xa447c0: DecompressPointer r3
    //     0xa447c0: add             x3, x3, HEAP, lsl #32
    // 0xa447c4: cmp             w3, NULL
    // 0xa447c8: b.eq            #0xa44824
    // 0xa447cc: LoadField: r3 = r3->field_f
    //     0xa447cc: ldur            w3, [x3, #0xf]
    // 0xa447d0: lsr             x3, x3, #3
    // 0xa447d4: r17 = 6724
    //     0xa447d4: movz            x17, #0x1a44
    // 0xa447d8: cmp             x3, x17
    // 0xa447dc: b.eq            #0xa4482c
    // 0xa447e0: r3 = SubtypeTestCache
    //     0xa447e0: add             x3, PP, #0x57, lsl #12  ; [pp+0x57dd8] SubtypeTestCache
    //     0xa447e4: ldr             x3, [x3, #0xdd8]
    // 0xa447e8: r30 = Subtype1TestCacheStub
    //     0xa447e8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xa447ec: LoadField: r30 = r30->field_7
    //     0xa447ec: ldur            lr, [lr, #7]
    // 0xa447f0: blr             lr
    // 0xa447f4: cmp             w7, NULL
    // 0xa447f8: b.eq            #0xa44804
    // 0xa447fc: tbnz            w7, #4, #0xa44824
    // 0xa44800: b               #0xa4482c
    // 0xa44804: r8 = Exception
    //     0xa44804: add             x8, PP, #0x57, lsl #12  ; [pp+0x57de0] Type: Exception
    //     0xa44808: ldr             x8, [x8, #0xde0]
    // 0xa4480c: r3 = SubtypeTestCache
    //     0xa4480c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57de8] SubtypeTestCache
    //     0xa44810: ldr             x3, [x3, #0xde8]
    // 0xa44814: r30 = InstanceOfStub
    //     0xa44814: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xa44818: LoadField: r30 = r30->field_7
    //     0xa44818: ldur            lr, [lr, #7]
    // 0xa4481c: blr             lr
    // 0xa44820: b               #0xa44830
    // 0xa44824: r0 = false
    //     0xa44824: add             x0, NULL, #0x30  ; false
    // 0xa44828: b               #0xa44830
    // 0xa4482c: r0 = true
    //     0xa4482c: add             x0, NULL, #0x20  ; true
    // 0xa44830: tbnz            w0, #4, #0xa44864
    // 0xa44834: ldur            x0, [fp, #-0x40]
    // 0xa44838: r0 = QrValidationResult()
    //     0xa44838: bl              #0xa4487c  ; AllocateQrValidationResultStub -> QrValidationResult (size=0x14)
    // 0xa4483c: mov             x1, x0
    // 0xa44840: r0 = Instance_QrValidationStatus
    //     0xa44840: add             x0, PP, #0x57, lsl #12  ; [pp+0x57df0] Obj!QrValidationStatus@e2e221
    //     0xa44844: ldr             x0, [x0, #0xdf0]
    // 0xa44848: StoreField: r1->field_7 = r0
    //     0xa44848: stur            w0, [x1, #7]
    // 0xa4484c: ldur            x0, [fp, #-0x40]
    // 0xa44850: StoreField: r1->field_f = r0
    //     0xa44850: stur            w0, [x1, #0xf]
    // 0xa44854: mov             x0, x1
    // 0xa44858: LeaveFrame
    //     0xa44858: mov             SP, fp
    //     0xa4485c: ldp             fp, lr, [SP], #0x10
    // 0xa44860: ret
    //     0xa44860: ret             
    // 0xa44864: ldur            x0, [fp, #-0x40]
    // 0xa44868: ldur            x1, [fp, #-0x48]
    // 0xa4486c: r0 = ReThrow()
    //     0xa4486c: bl              #0xec048c  ; ReThrowStub
    // 0xa44870: brk             #0
    // 0xa44874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa44874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa44878: b               #0xa446f8
  }
}

// class id: 6778, size: 0x14, field offset: 0x14
enum QrValidationStatus extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e6f8, size: 0x64
    // 0xc4e6f8: EnterFrame
    //     0xc4e6f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e6fc: mov             fp, SP
    // 0xc4e700: AllocStack(0x10)
    //     0xc4e700: sub             SP, SP, #0x10
    // 0xc4e704: SetupParameters(QrValidationStatus this /* r1 => r0, fp-0x8 */)
    //     0xc4e704: mov             x0, x1
    //     0xc4e708: stur            x1, [fp, #-8]
    // 0xc4e70c: CheckStackOverflow
    //     0xc4e70c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e710: cmp             SP, x16
    //     0xc4e714: b.ls            #0xc4e754
    // 0xc4e718: r1 = Null
    //     0xc4e718: mov             x1, NULL
    // 0xc4e71c: r2 = 4
    //     0xc4e71c: movz            x2, #0x4
    // 0xc4e720: r0 = AllocateArray()
    //     0xc4e720: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e724: r16 = "QrValidationStatus."
    //     0xc4e724: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b190] "QrValidationStatus."
    //     0xc4e728: ldr             x16, [x16, #0x190]
    // 0xc4e72c: StoreField: r0->field_f = r16
    //     0xc4e72c: stur            w16, [x0, #0xf]
    // 0xc4e730: ldur            x1, [fp, #-8]
    // 0xc4e734: LoadField: r2 = r1->field_f
    //     0xc4e734: ldur            w2, [x1, #0xf]
    // 0xc4e738: DecompressPointer r2
    //     0xc4e738: add             x2, x2, HEAP, lsl #32
    // 0xc4e73c: StoreField: r0->field_13 = r2
    //     0xc4e73c: stur            w2, [x0, #0x13]
    // 0xc4e740: str             x0, [SP]
    // 0xc4e744: r0 = _interpolate()
    //     0xc4e744: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e748: LeaveFrame
    //     0xc4e748: mov             SP, fp
    //     0xc4e74c: ldp             fp, lr, [SP], #0x10
    // 0xc4e750: ret
    //     0xc4e750: ret             
    // 0xc4e754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e754: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e758: b               #0xc4e718
  }
}
