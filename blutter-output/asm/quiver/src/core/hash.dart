// lib: , url: package:quiver/src/core/hash.dart

// class id: 1051076, size: 0x8
class :: {

  static _ hashObjects(/* No info */) {
    // ** addr: 0xbee028, size: 0xa4
    // 0xbee028: EnterFrame
    //     0xbee028: stp             fp, lr, [SP, #-0x10]!
    //     0xbee02c: mov             fp, SP
    // 0xbee030: AllocStack(0x28)
    //     0xbee030: sub             SP, SP, #0x28
    // 0xbee034: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xbee034: mov             x0, x1
    //     0xbee038: stur            x1, [fp, #-8]
    // 0xbee03c: CheckStackOverflow
    //     0xbee03c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbee040: cmp             SP, x16
    //     0xbee044: b.ls            #0xbee0c4
    // 0xbee048: r1 = Function '<anonymous closure>': static.
    //     0xbee048: add             x1, PP, #0x51, lsl #12  ; [pp+0x51eb8] AnonymousClosure: static (0xbee0cc), in [package:quiver/src/core/hash.dart] ::hashObjects (0xbee028)
    //     0xbee04c: ldr             x1, [x1, #0xeb8]
    // 0xbee050: r2 = Null
    //     0xbee050: mov             x2, NULL
    // 0xbee054: r0 = AllocateClosure()
    //     0xbee054: bl              #0xec1630  ; AllocateClosureStub
    // 0xbee058: r16 = <int>
    //     0xbee058: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbee05c: ldur            lr, [fp, #-8]
    // 0xbee060: stp             lr, x16, [SP, #0x10]
    // 0xbee064: stp             x0, xzr, [SP]
    // 0xbee068: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xbee068: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xbee06c: r0 = fold()
    //     0xbee06c: bl              #0x895b28  ; [dart:collection] ListBase::fold
    // 0xbee070: r1 = LoadInt32Instr(r0)
    //     0xbee070: sbfx            x1, x0, #1, #0x1f
    //     0xbee074: tbz             w0, #0, #0xbee07c
    //     0xbee078: ldur            x1, [x0, #7]
    // 0xbee07c: r2 = 67108863
    //     0xbee07c: orr             x2, xzr, #0x3ffffff
    // 0xbee080: and             x3, x1, x2
    // 0xbee084: lsl             w2, w3, #3
    // 0xbee088: add             w3, w1, w2
    // 0xbee08c: r1 = 536870911
    //     0xbee08c: orr             x1, xzr, #0x1fffffff
    // 0xbee090: and             x2, x3, x1
    // 0xbee094: lsr             w3, w2, #0xb
    // 0xbee098: eor             x4, x2, x3
    // 0xbee09c: r2 = 16383
    //     0xbee09c: orr             x2, xzr, #0x3fff
    // 0xbee0a0: and             x3, x4, x2
    // 0xbee0a4: lsl             w2, w3, #0xf
    // 0xbee0a8: add             w3, w4, w2
    // 0xbee0ac: and             x2, x3, x1
    // 0xbee0b0: ubfx            x2, x2, #0, #0x20
    // 0xbee0b4: mov             x0, x2
    // 0xbee0b8: LeaveFrame
    //     0xbee0b8: mov             SP, fp
    //     0xbee0bc: ldp             fp, lr, [SP], #0x10
    // 0xbee0c0: ret
    //     0xbee0c0: ret             
    // 0xbee0c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbee0c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbee0c8: b               #0xbee048
  }
  [closure] static int <anonymous closure>(dynamic, int, dynamic) {
    // ** addr: 0xbee0cc, size: 0xac
    // 0xbee0cc: EnterFrame
    //     0xbee0cc: stp             fp, lr, [SP, #-0x10]!
    //     0xbee0d0: mov             fp, SP
    // 0xbee0d4: AllocStack(0x8)
    //     0xbee0d4: sub             SP, SP, #8
    // 0xbee0d8: CheckStackOverflow
    //     0xbee0d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbee0dc: cmp             SP, x16
    //     0xbee0e0: b.ls            #0xbee170
    // 0xbee0e4: ldr             x0, [fp, #0x10]
    // 0xbee0e8: r1 = 60
    //     0xbee0e8: movz            x1, #0x3c
    // 0xbee0ec: branchIfSmi(r0, 0xbee0f8)
    //     0xbee0ec: tbz             w0, #0, #0xbee0f8
    // 0xbee0f0: r1 = LoadClassIdInstr(r0)
    //     0xbee0f0: ldur            x1, [x0, #-1]
    //     0xbee0f4: ubfx            x1, x1, #0xc, #0x14
    // 0xbee0f8: str             x0, [SP]
    // 0xbee0fc: mov             x0, x1
    // 0xbee100: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee100: movz            x17, #0x64af
    //     0xbee104: add             lr, x0, x17
    //     0xbee108: ldr             lr, [x21, lr, lsl #3]
    //     0xbee10c: blr             lr
    // 0xbee110: ldr             x1, [fp, #0x18]
    // 0xbee114: r2 = LoadInt32Instr(r1)
    //     0xbee114: sbfx            x2, x1, #1, #0x1f
    //     0xbee118: tbz             w1, #0, #0xbee120
    //     0xbee11c: ldur            x2, [x1, #7]
    // 0xbee120: r1 = LoadInt32Instr(r0)
    //     0xbee120: sbfx            x1, x0, #1, #0x1f
    //     0xbee124: tbz             w0, #0, #0xbee12c
    //     0xbee128: ldur            x1, [x0, #7]
    // 0xbee12c: add             w3, w2, w1
    // 0xbee130: r1 = 536870911
    //     0xbee130: orr             x1, xzr, #0x1fffffff
    // 0xbee134: and             x2, x3, x1
    // 0xbee138: r3 = 524287
    //     0xbee138: orr             x3, xzr, #0x7ffff
    // 0xbee13c: and             x4, x2, x3
    // 0xbee140: lsl             w3, w4, #0xa
    // 0xbee144: add             w4, w2, w3
    // 0xbee148: and             x2, x4, x1
    // 0xbee14c: mov             x1, x2
    // 0xbee150: ubfx            x1, x1, #0, #0x20
    // 0xbee154: asr             x3, x1, #6
    // 0xbee158: ubfx            x2, x2, #0, #0x20
    // 0xbee15c: eor             x1, x2, x3
    // 0xbee160: lsl             x0, x1, #1
    // 0xbee164: LeaveFrame
    //     0xbee164: mov             SP, fp
    //     0xbee168: ldp             fp, lr, [SP], #0x10
    // 0xbee16c: ret
    //     0xbee16c: ret             
    // 0xbee170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbee170: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbee174: b               #0xbee0e4
  }
}
