// lib: , url: package:quiver/src/iterables/enumerate.dart

// class id: 1051077, size: 0x8
class :: {

  static Iterable<IndexedValue<Y0>> enumerate<Y0>(Iterable<Y0>) {
    // ** addr: 0x739600, size: 0x5c
    // 0x739600: EnterFrame
    //     0x739600: stp             fp, lr, [SP, #-0x10]!
    //     0x739604: mov             fp, SP
    // 0x739608: LoadField: r0 = r4->field_f
    //     0x739608: ldur            w0, [x4, #0xf]
    // 0x73960c: cbnz            w0, #0x739618
    // 0x739610: r1 = Null
    //     0x739610: mov             x1, NULL
    // 0x739614: b               #0x739624
    // 0x739618: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x739618: ldur            w0, [x4, #0x17]
    // 0x73961c: add             x1, fp, w0, sxtw #2
    // 0x739620: ldr             x1, [x1, #0x10]
    // 0x739624: ldr             x0, [fp, #0x10]
    // 0x739628: r2 = Null
    //     0x739628: mov             x2, NULL
    // 0x73962c: r3 = <IndexedValue<Y0>, Y0>
    //     0x73962c: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c5a0] TypeArguments: <IndexedValue<Y0>, Y0>
    //     0x739630: ldr             x3, [x3, #0x5a0]
    // 0x739634: r30 = InstantiateTypeArgumentsStub
    //     0x739634: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x739638: LoadField: r30 = r30->field_7
    //     0x739638: ldur            lr, [lr, #7]
    // 0x73963c: blr             lr
    // 0x739640: mov             x1, x0
    // 0x739644: r0 = EnumerateIterable()
    //     0x739644: bl              #0x73965c  ; AllocateEnumerateIterableStub -> EnumerateIterable<C1X0> (size=0x10)
    // 0x739648: ldr             x1, [fp, #0x10]
    // 0x73964c: StoreField: r0->field_b = r1
    //     0x73964c: stur            w1, [x0, #0xb]
    // 0x739650: LeaveFrame
    //     0x739650: mov             SP, fp
    //     0x739654: ldp             fp, lr, [SP], #0x10
    // 0x739658: ret
    //     0x739658: ret             
  }
}

// class id: 527, size: 0x18, field offset: 0x8
class IndexedValue<X0> extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3330, size: 0x94
    // 0xbf3330: EnterFrame
    //     0xbf3330: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3334: mov             fp, SP
    // 0xbf3338: AllocStack(0x10)
    //     0xbf3338: sub             SP, SP, #0x10
    // 0xbf333c: CheckStackOverflow
    //     0xbf333c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3340: cmp             SP, x16
    //     0xbf3344: b.ls            #0xbf33bc
    // 0xbf3348: ldr             x0, [fp, #0x10]
    // 0xbf334c: LoadField: r1 = r0->field_b
    //     0xbf334c: ldur            x1, [x0, #0xb]
    // 0xbf3350: r16 = 31
    //     0xbf3350: movz            x16, #0x1f
    // 0xbf3354: mul             x2, x1, x16
    // 0xbf3358: stur            x2, [fp, #-8]
    // 0xbf335c: LoadField: r1 = r0->field_13
    //     0xbf335c: ldur            w1, [x0, #0x13]
    // 0xbf3360: DecompressPointer r1
    //     0xbf3360: add             x1, x1, HEAP, lsl #32
    // 0xbf3364: r0 = 60
    //     0xbf3364: movz            x0, #0x3c
    // 0xbf3368: branchIfSmi(r1, 0xbf3374)
    //     0xbf3368: tbz             w1, #0, #0xbf3374
    // 0xbf336c: r0 = LoadClassIdInstr(r1)
    //     0xbf336c: ldur            x0, [x1, #-1]
    //     0xbf3370: ubfx            x0, x0, #0xc, #0x14
    // 0xbf3374: str             x1, [SP]
    // 0xbf3378: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3378: movz            x17, #0x64af
    //     0xbf337c: add             lr, x0, x17
    //     0xbf3380: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3384: blr             lr
    // 0xbf3388: r2 = LoadInt32Instr(r0)
    //     0xbf3388: sbfx            x2, x0, #1, #0x1f
    //     0xbf338c: tbz             w0, #0, #0xbf3394
    //     0xbf3390: ldur            x2, [x0, #7]
    // 0xbf3394: ldur            x3, [fp, #-8]
    // 0xbf3398: add             x4, x3, x2
    // 0xbf339c: r0 = BoxInt64Instr(r4)
    //     0xbf339c: sbfiz           x0, x4, #1, #0x1f
    //     0xbf33a0: cmp             x4, x0, asr #1
    //     0xbf33a4: b.eq            #0xbf33b0
    //     0xbf33a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf33ac: stur            x4, [x0, #7]
    // 0xbf33b0: LeaveFrame
    //     0xbf33b0: mov             SP, fp
    //     0xbf33b4: ldp             fp, lr, [SP], #0x10
    // 0xbf33b8: ret
    //     0xbf33b8: ret             
    // 0xbf33bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf33bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf33c0: b               #0xbf3348
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3f850, size: 0x8c
    // 0xc3f850: EnterFrame
    //     0xc3f850: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f854: mov             fp, SP
    // 0xc3f858: AllocStack(0x8)
    //     0xc3f858: sub             SP, SP, #8
    // 0xc3f85c: CheckStackOverflow
    //     0xc3f85c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f860: cmp             SP, x16
    //     0xc3f864: b.ls            #0xc3f8d4
    // 0xc3f868: r1 = Null
    //     0xc3f868: mov             x1, NULL
    // 0xc3f86c: r2 = 10
    //     0xc3f86c: movz            x2, #0xa
    // 0xc3f870: r0 = AllocateArray()
    //     0xc3f870: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3f874: mov             x2, x0
    // 0xc3f878: r16 = "("
    //     0xc3f878: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0xc3f87c: ldr             x16, [x16, #0xf08]
    // 0xc3f880: StoreField: r2->field_f = r16
    //     0xc3f880: stur            w16, [x2, #0xf]
    // 0xc3f884: ldr             x3, [fp, #0x10]
    // 0xc3f888: LoadField: r4 = r3->field_b
    //     0xc3f888: ldur            x4, [x3, #0xb]
    // 0xc3f88c: r0 = BoxInt64Instr(r4)
    //     0xc3f88c: sbfiz           x0, x4, #1, #0x1f
    //     0xc3f890: cmp             x4, x0, asr #1
    //     0xc3f894: b.eq            #0xc3f8a0
    //     0xc3f898: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3f89c: stur            x4, [x0, #7]
    // 0xc3f8a0: StoreField: r2->field_13 = r0
    //     0xc3f8a0: stur            w0, [x2, #0x13]
    // 0xc3f8a4: r16 = ", "
    //     0xc3f8a4: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc3f8a8: ArrayStore: r2[0] = r16  ; List_4
    //     0xc3f8a8: stur            w16, [x2, #0x17]
    // 0xc3f8ac: LoadField: r0 = r3->field_13
    //     0xc3f8ac: ldur            w0, [x3, #0x13]
    // 0xc3f8b0: DecompressPointer r0
    //     0xc3f8b0: add             x0, x0, HEAP, lsl #32
    // 0xc3f8b4: StoreField: r2->field_1b = r0
    //     0xc3f8b4: stur            w0, [x2, #0x1b]
    // 0xc3f8b8: r16 = ")"
    //     0xc3f8b8: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3f8bc: StoreField: r2->field_1f = r16
    //     0xc3f8bc: stur            w16, [x2, #0x1f]
    // 0xc3f8c0: str             x2, [SP]
    // 0xc3f8c4: r0 = _interpolate()
    //     0xc3f8c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3f8c8: LeaveFrame
    //     0xc3f8c8: mov             SP, fp
    //     0xc3f8cc: ldp             fp, lr, [SP], #0x10
    // 0xc3f8d0: ret
    //     0xc3f8d0: ret             
    // 0xc3f8d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f8d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f8d8: b               #0xc3f868
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d7d0, size: 0xb0
    // 0xd7d7d0: EnterFrame
    //     0xd7d7d0: stp             fp, lr, [SP, #-0x10]!
    //     0xd7d7d4: mov             fp, SP
    // 0xd7d7d8: AllocStack(0x10)
    //     0xd7d7d8: sub             SP, SP, #0x10
    // 0xd7d7dc: CheckStackOverflow
    //     0xd7d7dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7d7e0: cmp             SP, x16
    //     0xd7d7e4: b.ls            #0xd7d878
    // 0xd7d7e8: ldr             x0, [fp, #0x10]
    // 0xd7d7ec: cmp             w0, NULL
    // 0xd7d7f0: b.ne            #0xd7d804
    // 0xd7d7f4: r0 = false
    //     0xd7d7f4: add             x0, NULL, #0x30  ; false
    // 0xd7d7f8: LeaveFrame
    //     0xd7d7f8: mov             SP, fp
    //     0xd7d7fc: ldp             fp, lr, [SP], #0x10
    // 0xd7d800: ret
    //     0xd7d800: ret             
    // 0xd7d804: r1 = 60
    //     0xd7d804: movz            x1, #0x3c
    // 0xd7d808: branchIfSmi(r0, 0xd7d814)
    //     0xd7d808: tbz             w0, #0, #0xd7d814
    // 0xd7d80c: r1 = LoadClassIdInstr(r0)
    //     0xd7d80c: ldur            x1, [x0, #-1]
    //     0xd7d810: ubfx            x1, x1, #0xc, #0x14
    // 0xd7d814: cmp             x1, #0x20f
    // 0xd7d818: b.ne            #0xd7d868
    // 0xd7d81c: ldr             x1, [fp, #0x18]
    // 0xd7d820: LoadField: r2 = r0->field_b
    //     0xd7d820: ldur            x2, [x0, #0xb]
    // 0xd7d824: LoadField: r3 = r1->field_b
    //     0xd7d824: ldur            x3, [x1, #0xb]
    // 0xd7d828: cmp             x2, x3
    // 0xd7d82c: b.ne            #0xd7d868
    // 0xd7d830: LoadField: r2 = r0->field_13
    //     0xd7d830: ldur            w2, [x0, #0x13]
    // 0xd7d834: DecompressPointer r2
    //     0xd7d834: add             x2, x2, HEAP, lsl #32
    // 0xd7d838: LoadField: r0 = r1->field_13
    //     0xd7d838: ldur            w0, [x1, #0x13]
    // 0xd7d83c: DecompressPointer r0
    //     0xd7d83c: add             x0, x0, HEAP, lsl #32
    // 0xd7d840: r1 = 60
    //     0xd7d840: movz            x1, #0x3c
    // 0xd7d844: branchIfSmi(r2, 0xd7d850)
    //     0xd7d844: tbz             w2, #0, #0xd7d850
    // 0xd7d848: r1 = LoadClassIdInstr(r2)
    //     0xd7d848: ldur            x1, [x2, #-1]
    //     0xd7d84c: ubfx            x1, x1, #0xc, #0x14
    // 0xd7d850: stp             x0, x2, [SP]
    // 0xd7d854: mov             x0, x1
    // 0xd7d858: mov             lr, x0
    // 0xd7d85c: ldr             lr, [x21, lr, lsl #3]
    // 0xd7d860: blr             lr
    // 0xd7d864: b               #0xd7d86c
    // 0xd7d868: r0 = false
    //     0xd7d868: add             x0, NULL, #0x30  ; false
    // 0xd7d86c: LeaveFrame
    //     0xd7d86c: mov             SP, fp
    //     0xd7d870: ldp             fp, lr, [SP], #0x10
    // 0xd7d874: ret
    //     0xd7d874: ret             
    // 0xd7d878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7d878: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7d87c: b               #0xd7d7e8
  }
}

// class id: 7209, size: 0x10, field offset: 0xc
class EnumerateIterable<C1X0> extends Iterable<C1X0> {

  get _ iterator(/* No info */) {
    // ** addr: 0x888dec, size: 0x7c
    // 0x888dec: EnterFrame
    //     0x888dec: stp             fp, lr, [SP, #-0x10]!
    //     0x888df0: mov             fp, SP
    // 0x888df4: AllocStack(0x8)
    //     0x888df4: sub             SP, SP, #8
    // 0x888df8: CheckStackOverflow
    //     0x888df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x888dfc: cmp             SP, x16
    //     0x888e00: b.ls            #0x888e60
    // 0x888e04: LoadField: r2 = r1->field_7
    //     0x888e04: ldur            w2, [x1, #7]
    // 0x888e08: DecompressPointer r2
    //     0x888e08: add             x2, x2, HEAP, lsl #32
    // 0x888e0c: stur            x2, [fp, #-8]
    // 0x888e10: LoadField: r0 = r1->field_b
    //     0x888e10: ldur            w0, [x1, #0xb]
    // 0x888e14: DecompressPointer r0
    //     0x888e14: add             x0, x0, HEAP, lsl #32
    // 0x888e18: r1 = LoadClassIdInstr(r0)
    //     0x888e18: ldur            x1, [x0, #-1]
    //     0x888e1c: ubfx            x1, x1, #0xc, #0x14
    // 0x888e20: mov             x16, x0
    // 0x888e24: mov             x0, x1
    // 0x888e28: mov             x1, x16
    // 0x888e2c: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x888e2c: movz            x17, #0xd35d
    //     0x888e30: add             lr, x0, x17
    //     0x888e34: ldr             lr, [x21, lr, lsl #3]
    //     0x888e38: blr             lr
    // 0x888e3c: ldur            x1, [fp, #-8]
    // 0x888e40: stur            x0, [fp, #-8]
    // 0x888e44: r0 = EnumerateIterator()
    //     0x888e44: bl              #0x888e68  ; AllocateEnumerateIteratorStub -> EnumerateIterator<C1X0> (size=0x1c)
    // 0x888e48: StoreField: r0->field_f = rZR
    //     0x888e48: stur            xzr, [x0, #0xf]
    // 0x888e4c: ldur            x1, [fp, #-8]
    // 0x888e50: StoreField: r0->field_b = r1
    //     0x888e50: stur            w1, [x0, #0xb]
    // 0x888e54: LeaveFrame
    //     0x888e54: mov             SP, fp
    //     0x888e58: ldp             fp, lr, [SP], #0x10
    // 0x888e5c: ret
    //     0x888e5c: ret             
    // 0x888e60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x888e60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x888e64: b               #0x888e04
  }
  _ elementAt(/* No info */) {
    // ** addr: 0x892e3c, size: 0xb4
    // 0x892e3c: EnterFrame
    //     0x892e3c: stp             fp, lr, [SP, #-0x10]!
    //     0x892e40: mov             fp, SP
    // 0x892e44: AllocStack(0x18)
    //     0x892e44: sub             SP, SP, #0x18
    // 0x892e48: SetupParameters(EnumerateIterable<C1X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x892e48: mov             x4, x1
    //     0x892e4c: mov             x0, x2
    //     0x892e50: stur            x1, [fp, #-8]
    //     0x892e54: stur            x2, [fp, #-0x10]
    // 0x892e58: CheckStackOverflow
    //     0x892e58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x892e5c: cmp             SP, x16
    //     0x892e60: b.ls            #0x892ee8
    // 0x892e64: LoadField: r2 = r4->field_7
    //     0x892e64: ldur            w2, [x4, #7]
    // 0x892e68: DecompressPointer r2
    //     0x892e68: add             x2, x2, HEAP, lsl #32
    // 0x892e6c: r1 = Null
    //     0x892e6c: mov             x1, NULL
    // 0x892e70: r3 = <C1X0>
    //     0x892e70: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af98] TypeArguments: <C1X0>
    //     0x892e74: ldr             x3, [x3, #0xf98]
    // 0x892e78: r0 = Null
    //     0x892e78: mov             x0, NULL
    // 0x892e7c: cmp             x2, x0
    // 0x892e80: b.eq            #0x892e90
    // 0x892e84: r30 = InstantiateTypeArgumentsStub
    //     0x892e84: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x892e88: LoadField: r30 = r30->field_7
    //     0x892e88: ldur            lr, [lr, #7]
    // 0x892e8c: blr             lr
    // 0x892e90: mov             x3, x0
    // 0x892e94: ldur            x0, [fp, #-8]
    // 0x892e98: stur            x3, [fp, #-0x18]
    // 0x892e9c: LoadField: r1 = r0->field_b
    //     0x892e9c: ldur            w1, [x0, #0xb]
    // 0x892ea0: DecompressPointer r1
    //     0x892ea0: add             x1, x1, HEAP, lsl #32
    // 0x892ea4: r0 = LoadClassIdInstr(r1)
    //     0x892ea4: ldur            x0, [x1, #-1]
    //     0x892ea8: ubfx            x0, x0, #0xc, #0x14
    // 0x892eac: ldur            x2, [fp, #-0x10]
    // 0x892eb0: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x892eb0: movz            x17, #0xd28f
    //     0x892eb4: add             lr, x0, x17
    //     0x892eb8: ldr             lr, [x21, lr, lsl #3]
    //     0x892ebc: blr             lr
    // 0x892ec0: ldur            x1, [fp, #-0x18]
    // 0x892ec4: stur            x0, [fp, #-8]
    // 0x892ec8: r0 = IndexedValue()
    //     0x892ec8: bl              #0x674480  ; AllocateIndexedValueStub -> IndexedValue<X0> (size=0x18)
    // 0x892ecc: ldur            x1, [fp, #-0x10]
    // 0x892ed0: StoreField: r0->field_b = r1
    //     0x892ed0: stur            x1, [x0, #0xb]
    // 0x892ed4: ldur            x1, [fp, #-8]
    // 0x892ed8: StoreField: r0->field_13 = r1
    //     0x892ed8: stur            w1, [x0, #0x13]
    // 0x892edc: LeaveFrame
    //     0x892edc: mov             SP, fp
    //     0x892ee0: ldp             fp, lr, [SP], #0x10
    // 0x892ee4: ret
    //     0x892ee4: ret             
    // 0x892ee8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x892ee8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x892eec: b               #0x892e64
  }
  get _ first(/* No info */) {
    // ** addr: 0x89393c, size: 0xa4
    // 0x89393c: EnterFrame
    //     0x89393c: stp             fp, lr, [SP, #-0x10]!
    //     0x893940: mov             fp, SP
    // 0x893944: AllocStack(0x10)
    //     0x893944: sub             SP, SP, #0x10
    // 0x893948: SetupParameters(EnumerateIterable<C1X0> this /* r1 => r0, fp-0x8 */)
    //     0x893948: mov             x0, x1
    //     0x89394c: stur            x1, [fp, #-8]
    // 0x893950: CheckStackOverflow
    //     0x893950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x893954: cmp             SP, x16
    //     0x893958: b.ls            #0x8939d8
    // 0x89395c: LoadField: r2 = r0->field_7
    //     0x89395c: ldur            w2, [x0, #7]
    // 0x893960: DecompressPointer r2
    //     0x893960: add             x2, x2, HEAP, lsl #32
    // 0x893964: r1 = Null
    //     0x893964: mov             x1, NULL
    // 0x893968: r3 = <C1X0>
    //     0x893968: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af98] TypeArguments: <C1X0>
    //     0x89396c: ldr             x3, [x3, #0xf98]
    // 0x893970: r0 = Null
    //     0x893970: mov             x0, NULL
    // 0x893974: cmp             x2, x0
    // 0x893978: b.eq            #0x893988
    // 0x89397c: r30 = InstantiateTypeArgumentsStub
    //     0x89397c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x893980: LoadField: r30 = r30->field_7
    //     0x893980: ldur            lr, [lr, #7]
    // 0x893984: blr             lr
    // 0x893988: mov             x2, x0
    // 0x89398c: ldur            x0, [fp, #-8]
    // 0x893990: stur            x2, [fp, #-0x10]
    // 0x893994: LoadField: r1 = r0->field_b
    //     0x893994: ldur            w1, [x0, #0xb]
    // 0x893998: DecompressPointer r1
    //     0x893998: add             x1, x1, HEAP, lsl #32
    // 0x89399c: r0 = LoadClassIdInstr(r1)
    //     0x89399c: ldur            x0, [x1, #-1]
    //     0x8939a0: ubfx            x0, x0, #0xc, #0x14
    // 0x8939a4: r0 = GDT[cid_x0 + 0xd20f]()
    //     0x8939a4: movz            x17, #0xd20f
    //     0x8939a8: add             lr, x0, x17
    //     0x8939ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8939b0: blr             lr
    // 0x8939b4: ldur            x1, [fp, #-0x10]
    // 0x8939b8: stur            x0, [fp, #-8]
    // 0x8939bc: r0 = IndexedValue()
    //     0x8939bc: bl              #0x674480  ; AllocateIndexedValueStub -> IndexedValue<X0> (size=0x18)
    // 0x8939c0: StoreField: r0->field_b = rZR
    //     0x8939c0: stur            xzr, [x0, #0xb]
    // 0x8939c4: ldur            x1, [fp, #-8]
    // 0x8939c8: StoreField: r0->field_13 = r1
    //     0x8939c8: stur            w1, [x0, #0x13]
    // 0x8939cc: LeaveFrame
    //     0x8939cc: mov             SP, fp
    //     0x8939d0: ldp             fp, lr, [SP], #0x10
    // 0x8939d4: ret
    //     0x8939d4: ret             
    // 0x8939d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8939d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8939dc: b               #0x89395c
  }
}

// class id: 7370, size: 0x1c, field offset: 0xc
class EnumerateIterator<C1X0> extends Iterator<C1X0> {

  get _ current(/* No info */) {
    // ** addr: 0x5f6c90, size: 0x58
    // 0x5f6c90: EnterFrame
    //     0x5f6c90: stp             fp, lr, [SP, #-0x10]!
    //     0x5f6c94: mov             fp, SP
    // 0x5f6c98: AllocStack(0x8)
    //     0x5f6c98: sub             SP, SP, #8
    // 0x5f6c9c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x5f6c9c: ldur            w3, [x1, #0x17]
    // 0x5f6ca0: DecompressPointer r3
    //     0x5f6ca0: add             x3, x3, HEAP, lsl #32
    // 0x5f6ca4: stur            x3, [fp, #-8]
    // 0x5f6ca8: cmp             w3, NULL
    // 0x5f6cac: b.ne            #0x5f6cd8
    // 0x5f6cb0: LoadField: r2 = r1->field_7
    //     0x5f6cb0: ldur            w2, [x1, #7]
    // 0x5f6cb4: DecompressPointer r2
    //     0x5f6cb4: add             x2, x2, HEAP, lsl #32
    // 0x5f6cb8: mov             x0, x3
    // 0x5f6cbc: r1 = Null
    //     0x5f6cbc: mov             x1, NULL
    // 0x5f6cc0: r8 = IndexedValue<C1X0>
    //     0x5f6cc0: add             x8, PP, #0x58, lsl #12  ; [pp+0x58338] Type: IndexedValue<C1X0>
    //     0x5f6cc4: ldr             x8, [x8, #0x338]
    // 0x5f6cc8: LoadField: r9 = r8->field_7
    //     0x5f6cc8: ldur            x9, [x8, #7]
    // 0x5f6ccc: r3 = Null
    //     0x5f6ccc: add             x3, PP, #0x58, lsl #12  ; [pp+0x58340] Null
    //     0x5f6cd0: ldr             x3, [x3, #0x340]
    // 0x5f6cd4: blr             x9
    // 0x5f6cd8: ldur            x0, [fp, #-8]
    // 0x5f6cdc: LeaveFrame
    //     0x5f6cdc: mov             SP, fp
    //     0x5f6ce0: ldp             fp, lr, [SP], #0x10
    // 0x5f6ce4: ret
    //     0x5f6ce4: ret             
  }
  _ moveNext(/* No info */) {
    // ** addr: 0x674354, size: 0x12c
    // 0x674354: EnterFrame
    //     0x674354: stp             fp, lr, [SP, #-0x10]!
    //     0x674358: mov             fp, SP
    // 0x67435c: AllocStack(0x20)
    //     0x67435c: sub             SP, SP, #0x20
    // 0x674360: SetupParameters(EnumerateIterator<C1X0> this /* r1 => r2, fp-0x10 */)
    //     0x674360: mov             x2, x1
    //     0x674364: stur            x1, [fp, #-0x10]
    // 0x674368: CheckStackOverflow
    //     0x674368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67436c: cmp             SP, x16
    //     0x674370: b.ls            #0x674478
    // 0x674374: LoadField: r3 = r2->field_b
    //     0x674374: ldur            w3, [x2, #0xb]
    // 0x674378: DecompressPointer r3
    //     0x674378: add             x3, x3, HEAP, lsl #32
    // 0x67437c: stur            x3, [fp, #-8]
    // 0x674380: r0 = LoadClassIdInstr(r3)
    //     0x674380: ldur            x0, [x3, #-1]
    //     0x674384: ubfx            x0, x0, #0xc, #0x14
    // 0x674388: mov             x1, x3
    // 0x67438c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x67438c: movz            x17, #0x292d
    //     0x674390: movk            x17, #0x1, lsl #16
    //     0x674394: add             lr, x0, x17
    //     0x674398: ldr             lr, [x21, lr, lsl #3]
    //     0x67439c: blr             lr
    // 0x6743a0: tbnz            w0, #4, #0x674460
    // 0x6743a4: ldur            x0, [fp, #-0x10]
    // 0x6743a8: ldur            x4, [fp, #-8]
    // 0x6743ac: LoadField: r2 = r0->field_7
    //     0x6743ac: ldur            w2, [x0, #7]
    // 0x6743b0: DecompressPointer r2
    //     0x6743b0: add             x2, x2, HEAP, lsl #32
    // 0x6743b4: r1 = Null
    //     0x6743b4: mov             x1, NULL
    // 0x6743b8: r3 = <C1X0>
    //     0x6743b8: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af98] TypeArguments: <C1X0>
    //     0x6743bc: ldr             x3, [x3, #0xf98]
    // 0x6743c0: r0 = Null
    //     0x6743c0: mov             x0, NULL
    // 0x6743c4: cmp             x2, x0
    // 0x6743c8: b.eq            #0x6743d8
    // 0x6743cc: r30 = InstantiateTypeArgumentsStub
    //     0x6743cc: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x6743d0: LoadField: r30 = r30->field_7
    //     0x6743d0: ldur            lr, [lr, #7]
    // 0x6743d4: blr             lr
    // 0x6743d8: mov             x3, x0
    // 0x6743dc: ldur            x2, [fp, #-0x10]
    // 0x6743e0: stur            x3, [fp, #-0x20]
    // 0x6743e4: LoadField: r4 = r2->field_f
    //     0x6743e4: ldur            x4, [x2, #0xf]
    // 0x6743e8: stur            x4, [fp, #-0x18]
    // 0x6743ec: add             x0, x4, #1
    // 0x6743f0: StoreField: r2->field_f = r0
    //     0x6743f0: stur            x0, [x2, #0xf]
    // 0x6743f4: ldur            x1, [fp, #-8]
    // 0x6743f8: r0 = LoadClassIdInstr(r1)
    //     0x6743f8: ldur            x0, [x1, #-1]
    //     0x6743fc: ubfx            x0, x0, #0xc, #0x14
    // 0x674400: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x674400: movz            x17, #0x384d
    //     0x674404: movk            x17, #0x1, lsl #16
    //     0x674408: add             lr, x0, x17
    //     0x67440c: ldr             lr, [x21, lr, lsl #3]
    //     0x674410: blr             lr
    // 0x674414: ldur            x1, [fp, #-0x20]
    // 0x674418: stur            x0, [fp, #-8]
    // 0x67441c: r0 = IndexedValue()
    //     0x67441c: bl              #0x674480  ; AllocateIndexedValueStub -> IndexedValue<X0> (size=0x18)
    // 0x674420: ldur            x1, [fp, #-0x18]
    // 0x674424: StoreField: r0->field_b = r1
    //     0x674424: stur            x1, [x0, #0xb]
    // 0x674428: ldur            x1, [fp, #-8]
    // 0x67442c: StoreField: r0->field_13 = r1
    //     0x67442c: stur            w1, [x0, #0x13]
    // 0x674430: ldur            x1, [fp, #-0x10]
    // 0x674434: ArrayStore: r1[0] = r0  ; List_4
    //     0x674434: stur            w0, [x1, #0x17]
    //     0x674438: ldurb           w16, [x1, #-1]
    //     0x67443c: ldurb           w17, [x0, #-1]
    //     0x674440: and             x16, x17, x16, lsr #2
    //     0x674444: tst             x16, HEAP, lsr #32
    //     0x674448: b.eq            #0x674450
    //     0x67444c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x674450: r0 = true
    //     0x674450: add             x0, NULL, #0x20  ; true
    // 0x674454: LeaveFrame
    //     0x674454: mov             SP, fp
    //     0x674458: ldp             fp, lr, [SP], #0x10
    // 0x67445c: ret
    //     0x67445c: ret             
    // 0x674460: ldur            x1, [fp, #-0x10]
    // 0x674464: ArrayStore: r1[0] = rNULL  ; List_4
    //     0x674464: stur            NULL, [x1, #0x17]
    // 0x674468: r0 = false
    //     0x674468: add             x0, NULL, #0x30  ; false
    // 0x67446c: LeaveFrame
    //     0x67446c: mov             SP, fp
    //     0x674470: ldp             fp, lr, [SP], #0x10
    // 0x674474: ret
    //     0x674474: ret             
    // 0x674478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x674478: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67447c: b               #0x674374
  }
}
