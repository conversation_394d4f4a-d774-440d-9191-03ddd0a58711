// lib: , url: package:quiver/src/iterables/min_max.dart

// class id: 1051078, size: 0x8
class :: {

  static Y0? max<Y0>(Iterable<Y0>) {
    // ** addr: 0x738f14, size: 0x160
    // 0x738f14: EnterFrame
    //     0x738f14: stp             fp, lr, [SP, #-0x10]!
    //     0x738f18: mov             fp, SP
    // 0x738f1c: AllocStack(0x30)
    //     0x738f1c: sub             SP, SP, #0x30
    // 0x738f20: SetupParameters()
    //     0x738f20: ldur            w0, [x4, #0xf]
    //     0x738f24: cbnz            w0, #0x738f30
    //     0x738f28: mov             x3, NULL
    //     0x738f2c: b               #0x738f40
    //     0x738f30: ldur            w0, [x4, #0x17]
    //     0x738f34: add             x1, fp, w0, sxtw #2
    //     0x738f38: ldr             x1, [x1, #0x10]
    //     0x738f3c: mov             x3, x1
    //     0x738f40: ldr             x2, [fp, #0x10]
    //     0x738f44: stur            x3, [fp, #-8]
    // 0x738f48: CheckStackOverflow
    //     0x738f48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x738f4c: cmp             SP, x16
    //     0x738f50: b.ls            #0x73906c
    // 0x738f54: r0 = LoadClassIdInstr(r2)
    //     0x738f54: ldur            x0, [x2, #-1]
    //     0x738f58: ubfx            x0, x0, #0xc, #0x14
    // 0x738f5c: mov             x1, x2
    // 0x738f60: r0 = GDT[cid_x0 + 0xe879]()
    //     0x738f60: movz            x17, #0xe879
    //     0x738f64: add             lr, x0, x17
    //     0x738f68: ldr             lr, [x21, lr, lsl #3]
    //     0x738f6c: blr             lr
    // 0x738f70: tbnz            w0, #4, #0x738f84
    // 0x738f74: r0 = Null
    //     0x738f74: mov             x0, NULL
    // 0x738f78: LeaveFrame
    //     0x738f78: mov             SP, fp
    //     0x738f7c: ldp             fp, lr, [SP], #0x10
    // 0x738f80: ret
    //     0x738f80: ret             
    // 0x738f84: ldr             x1, [fp, #0x10]
    // 0x738f88: ldur            x0, [fp, #-8]
    // 0x738f8c: r2 = Closure: <Y0>(Y0, Y0) => int from Function '_compareAny@1179212406': static.
    //     0x738f8c: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4c568] Closure: <Y0>(Y0, Y0) => int from Function '_compareAny@1179212406': static. (0x7e54fb139104)
    //     0x738f90: ldr             x2, [x2, #0x568]
    // 0x738f94: LoadField: r3 = r2->field_13
    //     0x738f94: ldur            w3, [x2, #0x13]
    // 0x738f98: DecompressPointer r3
    //     0x738f98: add             x3, x3, HEAP, lsl #32
    // 0x738f9c: stur            x3, [fp, #-0x20]
    // 0x738fa0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x738fa0: ldur            w4, [x2, #0x17]
    // 0x738fa4: DecompressPointer r4
    //     0x738fa4: add             x4, x4, HEAP, lsl #32
    // 0x738fa8: stur            x4, [fp, #-0x18]
    // 0x738fac: LoadField: r5 = r2->field_7
    //     0x738fac: ldur            w5, [x2, #7]
    // 0x738fb0: DecompressPointer r5
    //     0x738fb0: add             x5, x5, HEAP, lsl #32
    // 0x738fb4: stur            x5, [fp, #-0x10]
    // 0x738fb8: r16 = Closure: <Y0>(Y0, Y0) => int from Function '_compareAny@1179212406': static.
    //     0x738fb8: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c568] Closure: <Y0>(Y0, Y0) => int from Function '_compareAny@1179212406': static. (0x7e54fb139104)
    //     0x738fbc: ldr             x16, [x16, #0x568]
    // 0x738fc0: stp             x0, x16, [SP]
    // 0x738fc4: r0 = _boundsCheckForPartialInstantiation()
    //     0x738fc4: bl              #0x6022c8  ; [dart:_internal] ::_boundsCheckForPartialInstantiation
    // 0x738fc8: ldur            x1, [fp, #-0x20]
    // 0x738fcc: ldur            x2, [fp, #-0x18]
    // 0x738fd0: ldur            x3, [fp, #-0x10]
    // 0x738fd4: r0 = AllocateClosureTA()
    //     0x738fd4: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x738fd8: mov             x1, x0
    // 0x738fdc: ldur            x0, [fp, #-8]
    // 0x738fe0: stur            x1, [fp, #-0x10]
    // 0x738fe4: StoreField: r1->field_f = r0
    //     0x738fe4: stur            w0, [x1, #0xf]
    // 0x738fe8: r2 = Closure: <Y0>(Y0, Y0) => int from Function '_compareAny@1179212406': static.
    //     0x738fe8: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4c568] Closure: <Y0>(Y0, Y0) => int from Function '_compareAny@1179212406': static. (0x7e54fb139104)
    //     0x738fec: ldr             x2, [x2, #0x568]
    // 0x738ff0: LoadField: r3 = r2->field_b
    //     0x738ff0: ldur            w3, [x2, #0xb]
    // 0x738ff4: DecompressPointer r3
    //     0x738ff4: add             x3, x3, HEAP, lsl #32
    // 0x738ff8: StoreField: r1->field_b = r3
    //     0x738ff8: stur            w3, [x1, #0xb]
    // 0x738ffc: r1 = 1
    //     0x738ffc: movz            x1, #0x1
    // 0x739000: r0 = AllocateContext()
    //     0x739000: bl              #0xec126c  ; AllocateContextStub
    // 0x739004: mov             x1, x0
    // 0x739008: ldur            x0, [fp, #-0x10]
    // 0x73900c: StoreField: r1->field_f = r0
    //     0x73900c: stur            w0, [x1, #0xf]
    // 0x739010: mov             x2, x1
    // 0x739014: r1 = Function '<anonymous closure>': static.
    //     0x739014: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c570] AnonymousClosure: static (0x739074), of [package:supercharged_dart/supercharged_dart.dart] 
    //     0x739018: ldr             x1, [x1, #0x570]
    // 0x73901c: r0 = AllocateClosure()
    //     0x73901c: bl              #0xec1630  ; AllocateClosureStub
    // 0x739020: mov             x1, x0
    // 0x739024: ldur            x0, [fp, #-8]
    // 0x739028: StoreField: r1->field_b = r0
    //     0x739028: stur            w0, [x1, #0xb]
    // 0x73902c: ldr             x0, [fp, #0x10]
    // 0x739030: r2 = LoadClassIdInstr(r0)
    //     0x739030: ldur            x2, [x0, #-1]
    //     0x739034: ubfx            x2, x2, #0xc, #0x14
    // 0x739038: mov             x16, x1
    // 0x73903c: mov             x1, x2
    // 0x739040: mov             x2, x16
    // 0x739044: mov             x16, x0
    // 0x739048: mov             x0, x1
    // 0x73904c: mov             x1, x16
    // 0x739050: r0 = GDT[cid_x0 + 0xe7f9]()
    //     0x739050: movz            x17, #0xe7f9
    //     0x739054: add             lr, x0, x17
    //     0x739058: ldr             lr, [x21, lr, lsl #3]
    //     0x73905c: blr             lr
    // 0x739060: LeaveFrame
    //     0x739060: mov             SP, fp
    //     0x739064: ldp             fp, lr, [SP], #0x10
    // 0x739068: ret
    //     0x739068: ret             
    // 0x73906c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73906c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x739070: b               #0x738f54
  }
  [closure] static int _compareAny<Y0>(dynamic, Y0, Y0) {
    // ** addr: 0x739104, size: 0x9c
    // 0x739104: EnterFrame
    //     0x739104: stp             fp, lr, [SP, #-0x10]!
    //     0x739108: mov             fp, SP
    // 0x73910c: AllocStack(0x18)
    //     0x73910c: sub             SP, SP, #0x18
    // 0x739110: SetupParameters()
    //     0x739110: ldur            w0, [x4, #0xf]
    //     0x739114: cbnz            w0, #0x739120
    //     0x739118: mov             x1, NULL
    //     0x73911c: b               #0x73912c
    //     0x739120: ldur            w0, [x4, #0x17]
    //     0x739124: add             x1, fp, w0, sxtw #2
    //     0x739128: ldr             x1, [x1, #0x10]
    //     0x73912c: ldr             x0, [fp, #0x20]
    //     0x739130: ldur            w2, [x0, #0xf]
    //     0x739134: add             x2, x2, HEAP, lsl #32
    //     0x739138: ldr             x16, [THR, #0x98]  ; THR::empty_type_arguments
    //     0x73913c: cmp             w2, w16
    //     0x739140: b.ne            #0x73914c
    //     0x739144: mov             x0, x1
    //     0x739148: b               #0x739150
    //     0x73914c: mov             x0, x2
    // 0x739150: CheckStackOverflow
    //     0x739150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x739154: cmp             SP, x16
    //     0x739158: b.ls            #0x739198
    // 0x73915c: ldr             x16, [fp, #0x18]
    // 0x739160: stp             x16, x0, [SP, #8]
    // 0x739164: ldr             x16, [fp, #0x10]
    // 0x739168: str             x16, [SP]
    // 0x73916c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x73916c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x739170: r0 = _compareAny()
    //     0x739170: bl              #0x7391a0  ; [package:quiver/src/iterables/min_max.dart] ::_compareAny
    // 0x739174: mov             x2, x0
    // 0x739178: r0 = BoxInt64Instr(r2)
    //     0x739178: sbfiz           x0, x2, #1, #0x1f
    //     0x73917c: cmp             x2, x0, asr #1
    //     0x739180: b.eq            #0x73918c
    //     0x739184: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x739188: stur            x2, [x0, #7]
    // 0x73918c: LeaveFrame
    //     0x73918c: mov             SP, fp
    //     0x739190: ldp             fp, lr, [SP], #0x10
    // 0x739194: ret
    //     0x739194: ret             
    // 0x739198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x739198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73919c: b               #0x73915c
  }
  static _ _compareAny(/* No info */) {
    // ** addr: 0x7391a0, size: 0x74
    // 0x7391a0: EnterFrame
    //     0x7391a0: stp             fp, lr, [SP, #-0x10]!
    //     0x7391a4: mov             fp, SP
    // 0x7391a8: CheckStackOverflow
    //     0x7391a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7391ac: cmp             SP, x16
    //     0x7391b0: b.ls            #0x73920c
    // 0x7391b4: ldr             x0, [fp, #0x18]
    // 0x7391b8: r2 = Null
    //     0x7391b8: mov             x2, NULL
    // 0x7391bc: r1 = Null
    //     0x7391bc: mov             x1, NULL
    // 0x7391c0: branchIfSmi(r0, 0x7391d4)
    //     0x7391c0: tbz             w0, #0, #0x7391d4
    // 0x7391c4: r8 = Comparable
    //     0x7391c4: ldr             x8, [PP, #0xa08]  ; [pp+0xa08] Type: Comparable
    // 0x7391c8: r3 = Null
    //     0x7391c8: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c578] Null
    //     0x7391cc: ldr             x3, [x3, #0x578]
    // 0x7391d0: r0 = Comparable()
    //     0x7391d0: bl              #0x6c35c4  ; IsType_Comparable_Stub
    // 0x7391d4: ldr             x0, [fp, #0x10]
    // 0x7391d8: r2 = Null
    //     0x7391d8: mov             x2, NULL
    // 0x7391dc: r1 = Null
    //     0x7391dc: mov             x1, NULL
    // 0x7391e0: branchIfSmi(r0, 0x7391f4)
    //     0x7391e0: tbz             w0, #0, #0x7391f4
    // 0x7391e4: r8 = Comparable
    //     0x7391e4: ldr             x8, [PP, #0xa08]  ; [pp+0xa08] Type: Comparable
    // 0x7391e8: r3 = Null
    //     0x7391e8: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c588] Null
    //     0x7391ec: ldr             x3, [x3, #0x588]
    // 0x7391f0: r0 = Comparable()
    //     0x7391f0: bl              #0x6c35c4  ; IsType_Comparable_Stub
    // 0x7391f4: ldr             x1, [fp, #0x18]
    // 0x7391f8: ldr             x2, [fp, #0x10]
    // 0x7391fc: r0 = compare()
    //     0x7391fc: bl              #0x6c352c  ; [dart:core] Comparable::compare
    // 0x739200: LeaveFrame
    //     0x739200: mov             SP, fp
    //     0x739204: ldp             fp, lr, [SP], #0x10
    // 0x739208: ret
    //     0x739208: ret             
    // 0x73920c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73920c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x739210: b               #0x7391b4
  }
}
