// lib: , url: package:quiver/src/iterables/partition.dart

// class id: 1051079, size: 0x8
class :: {

  static Iterable<List<Y0>> partition<Y0>(Iterable<Y0>, int) {
    // ** addr: 0xc284e8, size: 0x124
    // 0xc284e8: EnterFrame
    //     0xc284e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc284ec: mov             fp, SP
    // 0xc284f0: AllocStack(0x10)
    //     0xc284f0: sub             SP, SP, #0x10
    // 0xc284f4: SetupParameters()
    //     0xc284f4: ldur            w0, [x4, #0xf]
    //     0xc284f8: cbnz            w0, #0xc28504
    //     0xc284fc: mov             x2, NULL
    //     0xc28500: b               #0xc28514
    //     0xc28504: ldur            w0, [x4, #0x17]
    //     0xc28508: add             x1, fp, w0, sxtw #2
    //     0xc2850c: ldr             x1, [x1, #0x10]
    //     0xc28510: mov             x2, x1
    //     0xc28514: ldr             x1, [fp, #0x18]
    //     0xc28518: stur            x2, [fp, #-8]
    // 0xc2851c: CheckStackOverflow
    //     0xc2851c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc28520: cmp             SP, x16
    //     0xc28524: b.ls            #0xc28604
    // 0xc28528: LoadField: r0 = r1->field_b
    //     0xc28528: ldur            w0, [x1, #0xb]
    // 0xc2852c: DecompressPointer r0
    //     0xc2852c: add             x0, x0, HEAP, lsl #32
    // 0xc28530: r3 = LoadClassIdInstr(r0)
    //     0xc28530: ldur            x3, [x0, #-1]
    //     0xc28534: ubfx            x3, x3, #0xc, #0x14
    // 0xc28538: str             x0, [SP]
    // 0xc2853c: mov             x0, x3
    // 0xc28540: r0 = GDT[cid_x0 + 0xc834]()
    //     0xc28540: movz            x17, #0xc834
    //     0xc28544: add             lr, x0, x17
    //     0xc28548: ldr             lr, [x21, lr, lsl #3]
    //     0xc2854c: blr             lr
    // 0xc28550: cbnz            w0, #0xc28580
    // 0xc28554: ldur            x1, [fp, #-8]
    // 0xc28558: r2 = Null
    //     0xc28558: mov             x2, NULL
    // 0xc2855c: r3 = <List<Y0>>
    //     0xc2855c: add             x3, PP, #0x51, lsl #12  ; [pp+0x51e00] TypeArguments: <List<Y0>>
    //     0xc28560: ldr             x3, [x3, #0xe00]
    // 0xc28564: r30 = InstantiateTypeArgumentsStub
    //     0xc28564: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xc28568: LoadField: r30 = r30->field_7
    //     0xc28568: ldur            lr, [lr, #7]
    // 0xc2856c: blr             lr
    // 0xc28570: mov             x1, x0
    // 0xc28574: r2 = 0
    //     0xc28574: movz            x2, #0
    // 0xc28578: r0 = _GrowableList()
    //     0xc28578: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc2857c: b               #0xc285cc
    // 0xc28580: ldr             x0, [fp, #0x18]
    // 0xc28584: ldr             x4, [fp, #0x10]
    // 0xc28588: ldur            x1, [fp, #-8]
    // 0xc2858c: r2 = Null
    //     0xc2858c: mov             x2, NULL
    // 0xc28590: r3 = <List<Y0>, Y0>
    //     0xc28590: add             x3, PP, #0x51, lsl #12  ; [pp+0x51e08] TypeArguments: <List<Y0>, Y0>
    //     0xc28594: ldr             x3, [x3, #0xe08]
    // 0xc28598: r30 = InstantiateTypeArgumentsStub
    //     0xc28598: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xc2859c: LoadField: r30 = r30->field_7
    //     0xc2859c: ldur            lr, [lr, #7]
    // 0xc285a0: blr             lr
    // 0xc285a4: mov             x1, x0
    // 0xc285a8: r0 = _Partition()
    //     0xc285a8: bl              #0xc2860c  ; Allocate_PartitionStub -> _Partition<C1X0> (size=0x18)
    // 0xc285ac: mov             x1, x0
    // 0xc285b0: ldr             x0, [fp, #0x18]
    // 0xc285b4: StoreField: r1->field_b = r0
    //     0xc285b4: stur            w0, [x1, #0xb]
    // 0xc285b8: ldr             x0, [fp, #0x10]
    // 0xc285bc: StoreField: r1->field_f = r0
    //     0xc285bc: stur            x0, [x1, #0xf]
    // 0xc285c0: cmp             x0, #0
    // 0xc285c4: b.le            #0xc285d8
    // 0xc285c8: mov             x0, x1
    // 0xc285cc: LeaveFrame
    //     0xc285cc: mov             SP, fp
    //     0xc285d0: ldp             fp, lr, [SP], #0x10
    // 0xc285d4: ret
    //     0xc285d4: ret             
    // 0xc285d8: lsl             x1, x0, #1
    // 0xc285dc: stur            x1, [fp, #-8]
    // 0xc285e0: r0 = ArgumentError()
    //     0xc285e0: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xc285e4: mov             x1, x0
    // 0xc285e8: ldur            x0, [fp, #-8]
    // 0xc285ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xc285ec: stur            w0, [x1, #0x17]
    // 0xc285f0: r0 = false
    //     0xc285f0: add             x0, NULL, #0x30  ; false
    // 0xc285f4: StoreField: r1->field_b = r0
    //     0xc285f4: stur            w0, [x1, #0xb]
    // 0xc285f8: mov             x0, x1
    // 0xc285fc: r0 = Throw()
    //     0xc285fc: bl              #0xec04b8  ; ThrowStub
    // 0xc28600: brk             #0
    // 0xc28604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc28604: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc28608: b               #0xc28528
  }
}

// class id: 526, size: 0x1c, field offset: 0x8
class _PartitionIterator<X0> extends Object
    implements Iterator<X0> {

  get _ current(/* No info */) {
    // ** addr: 0x6d65b0, size: 0x58
    // 0x6d65b0: EnterFrame
    //     0x6d65b0: stp             fp, lr, [SP, #-0x10]!
    //     0x6d65b4: mov             fp, SP
    // 0x6d65b8: AllocStack(0x8)
    //     0x6d65b8: sub             SP, SP, #8
    // 0x6d65bc: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x6d65bc: ldur            w3, [x1, #0x17]
    // 0x6d65c0: DecompressPointer r3
    //     0x6d65c0: add             x3, x3, HEAP, lsl #32
    // 0x6d65c4: stur            x3, [fp, #-8]
    // 0x6d65c8: cmp             w3, NULL
    // 0x6d65cc: b.ne            #0x6d65f8
    // 0x6d65d0: LoadField: r2 = r1->field_7
    //     0x6d65d0: ldur            w2, [x1, #7]
    // 0x6d65d4: DecompressPointer r2
    //     0x6d65d4: add             x2, x2, HEAP, lsl #32
    // 0x6d65d8: mov             x0, x3
    // 0x6d65dc: r1 = Null
    //     0x6d65dc: mov             x1, NULL
    // 0x6d65e0: r8 = List<X0>
    //     0x6d65e0: add             x8, PP, #0x29, lsl #12  ; [pp+0x29488] Type: List<X0>
    //     0x6d65e4: ldr             x8, [x8, #0x488]
    // 0x6d65e8: LoadField: r9 = r8->field_7
    //     0x6d65e8: ldur            x9, [x8, #7]
    // 0x6d65ec: r3 = Null
    //     0x6d65ec: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b2d0] Null
    //     0x6d65f0: ldr             x3, [x3, #0x2d0]
    // 0x6d65f4: blr             x9
    // 0x6d65f8: ldur            x0, [fp, #-8]
    // 0x6d65fc: LeaveFrame
    //     0x6d65fc: mov             SP, fp
    //     0x6d6600: ldp             fp, lr, [SP], #0x10
    // 0x6d6604: ret
    //     0x6d6604: ret             
  }
  _ moveNext(/* No info */) {
    // ** addr: 0x74ae88, size: 0x2cc
    // 0x74ae88: EnterFrame
    //     0x74ae88: stp             fp, lr, [SP, #-0x10]!
    //     0x74ae8c: mov             fp, SP
    // 0x74ae90: AllocStack(0x60)
    //     0x74ae90: sub             SP, SP, #0x60
    // 0x74ae94: SetupParameters(_PartitionIterator<X0> this /* r1 => r0, fp-0x10 */)
    //     0x74ae94: mov             x0, x1
    //     0x74ae98: stur            x1, [fp, #-0x10]
    // 0x74ae9c: CheckStackOverflow
    //     0x74ae9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74aea0: cmp             SP, x16
    //     0x74aea4: b.ls            #0x74b144
    // 0x74aea8: LoadField: r3 = r0->field_7
    //     0x74aea8: ldur            w3, [x0, #7]
    // 0x74aeac: DecompressPointer r3
    //     0x74aeac: add             x3, x3, HEAP, lsl #32
    // 0x74aeb0: mov             x1, x3
    // 0x74aeb4: stur            x3, [fp, #-8]
    // 0x74aeb8: r2 = 0
    //     0x74aeb8: movz            x2, #0
    // 0x74aebc: r0 = _GrowableList()
    //     0x74aebc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x74aec0: mov             x2, x0
    // 0x74aec4: ldur            x1, [fp, #-0x10]
    // 0x74aec8: stur            x2, [fp, #-0x48]
    // 0x74aecc: LoadField: r3 = r1->field_f
    //     0x74aecc: ldur            x3, [x1, #0xf]
    // 0x74aed0: stur            x3, [fp, #-0x40]
    // 0x74aed4: LoadField: r4 = r1->field_b
    //     0x74aed4: ldur            w4, [x1, #0xb]
    // 0x74aed8: DecompressPointer r4
    //     0x74aed8: add             x4, x4, HEAP, lsl #32
    // 0x74aedc: stur            x4, [fp, #-0x38]
    // 0x74aee0: LoadField: r5 = r4->field_b
    //     0x74aee0: ldur            w5, [x4, #0xb]
    // 0x74aee4: DecompressPointer r5
    //     0x74aee4: add             x5, x5, HEAP, lsl #32
    // 0x74aee8: stur            x5, [fp, #-0x30]
    // 0x74aeec: LoadField: r6 = r4->field_f
    //     0x74aeec: ldur            x6, [x4, #0xf]
    // 0x74aef0: stur            x6, [fp, #-0x28]
    // 0x74aef4: LoadField: r7 = r4->field_7
    //     0x74aef4: ldur            w7, [x4, #7]
    // 0x74aef8: DecompressPointer r7
    //     0x74aef8: add             x7, x7, HEAP, lsl #32
    // 0x74aefc: stur            x7, [fp, #-0x20]
    // 0x74af00: r8 = 0
    //     0x74af00: movz            x8, #0
    // 0x74af04: stur            x8, [fp, #-0x18]
    // 0x74af08: CheckStackOverflow
    //     0x74af08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74af0c: cmp             SP, x16
    //     0x74af10: b.ls            #0x74b14c
    // 0x74af14: cmp             x8, x3
    // 0x74af18: b.ge            #0x74b0d4
    // 0x74af1c: r0 = LoadClassIdInstr(r5)
    //     0x74af1c: ldur            x0, [x5, #-1]
    //     0x74af20: ubfx            x0, x0, #0xc, #0x14
    // 0x74af24: str             x5, [SP]
    // 0x74af28: r0 = GDT[cid_x0 + 0xc834]()
    //     0x74af28: movz            x17, #0xc834
    //     0x74af2c: add             lr, x0, x17
    //     0x74af30: ldr             lr, [x21, lr, lsl #3]
    //     0x74af34: blr             lr
    // 0x74af38: r1 = LoadInt32Instr(r0)
    //     0x74af38: sbfx            x1, x0, #1, #0x1f
    //     0x74af3c: tbz             w0, #0, #0x74af44
    //     0x74af40: ldur            x1, [x0, #7]
    // 0x74af44: ldur            x3, [fp, #-0x28]
    // 0x74af48: cmp             x3, x1
    // 0x74af4c: b.ne            #0x74b124
    // 0x74af50: ldur            x4, [fp, #-0x38]
    // 0x74af54: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x74af54: ldur            x2, [x4, #0x17]
    // 0x74af58: cmp             x2, x1
    // 0x74af5c: b.ge            #0x74b0c0
    // 0x74af60: ldur            x5, [fp, #-0x30]
    // 0x74af64: r0 = LoadClassIdInstr(r5)
    //     0x74af64: ldur            x0, [x5, #-1]
    //     0x74af68: ubfx            x0, x0, #0xc, #0x14
    // 0x74af6c: mov             x1, x5
    // 0x74af70: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x74af70: movz            x17, #0xd28f
    //     0x74af74: add             lr, x0, x17
    //     0x74af78: ldr             lr, [x21, lr, lsl #3]
    //     0x74af7c: blr             lr
    // 0x74af80: mov             x4, x0
    // 0x74af84: ldur            x3, [fp, #-0x38]
    // 0x74af88: stur            x4, [fp, #-0x50]
    // 0x74af8c: StoreField: r3->field_1f = r0
    //     0x74af8c: stur            w0, [x3, #0x1f]
    //     0x74af90: tbz             w0, #0, #0x74afac
    //     0x74af94: ldurb           w16, [x3, #-1]
    //     0x74af98: ldurb           w17, [x0, #-1]
    //     0x74af9c: and             x16, x17, x16, lsr #2
    //     0x74afa0: tst             x16, HEAP, lsr #32
    //     0x74afa4: b.eq            #0x74afac
    //     0x74afa8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x74afac: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x74afac: ldur            x0, [x3, #0x17]
    // 0x74afb0: add             x1, x0, #1
    // 0x74afb4: ArrayStore: r3[0] = r1  ; List_8
    //     0x74afb4: stur            x1, [x3, #0x17]
    // 0x74afb8: cmp             w4, NULL
    // 0x74afbc: b.ne            #0x74aff0
    // 0x74afc0: mov             x0, x4
    // 0x74afc4: ldur            x2, [fp, #-0x20]
    // 0x74afc8: r1 = Null
    //     0x74afc8: mov             x1, NULL
    // 0x74afcc: cmp             w2, NULL
    // 0x74afd0: b.eq            #0x74aff0
    // 0x74afd4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x74afd4: ldur            w4, [x2, #0x17]
    // 0x74afd8: DecompressPointer r4
    //     0x74afd8: add             x4, x4, HEAP, lsl #32
    // 0x74afdc: r8 = X0
    //     0x74afdc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x74afe0: LoadField: r9 = r4->field_7
    //     0x74afe0: ldur            x9, [x4, #7]
    // 0x74afe4: r3 = Null
    //     0x74afe4: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b2b0] Null
    //     0x74afe8: ldr             x3, [x3, #0x2b0]
    // 0x74afec: blr             x9
    // 0x74aff0: ldur            x3, [fp, #-0x48]
    // 0x74aff4: ldur            x0, [fp, #-0x50]
    // 0x74aff8: ldur            x2, [fp, #-8]
    // 0x74affc: r1 = Null
    //     0x74affc: mov             x1, NULL
    // 0x74b000: cmp             w2, NULL
    // 0x74b004: b.eq            #0x74b024
    // 0x74b008: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x74b008: ldur            w4, [x2, #0x17]
    // 0x74b00c: DecompressPointer r4
    //     0x74b00c: add             x4, x4, HEAP, lsl #32
    // 0x74b010: r8 = X0
    //     0x74b010: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x74b014: LoadField: r9 = r4->field_7
    //     0x74b014: ldur            x9, [x4, #7]
    // 0x74b018: r3 = Null
    //     0x74b018: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b2c0] Null
    //     0x74b01c: ldr             x3, [x3, #0x2c0]
    // 0x74b020: blr             x9
    // 0x74b024: ldur            x0, [fp, #-0x48]
    // 0x74b028: LoadField: r1 = r0->field_b
    //     0x74b028: ldur            w1, [x0, #0xb]
    // 0x74b02c: LoadField: r2 = r0->field_f
    //     0x74b02c: ldur            w2, [x0, #0xf]
    // 0x74b030: DecompressPointer r2
    //     0x74b030: add             x2, x2, HEAP, lsl #32
    // 0x74b034: LoadField: r3 = r2->field_b
    //     0x74b034: ldur            w3, [x2, #0xb]
    // 0x74b038: r2 = LoadInt32Instr(r1)
    //     0x74b038: sbfx            x2, x1, #1, #0x1f
    // 0x74b03c: stur            x2, [fp, #-0x58]
    // 0x74b040: r1 = LoadInt32Instr(r3)
    //     0x74b040: sbfx            x1, x3, #1, #0x1f
    // 0x74b044: cmp             x2, x1
    // 0x74b048: b.ne            #0x74b054
    // 0x74b04c: mov             x1, x0
    // 0x74b050: r0 = _growToNextCapacity()
    //     0x74b050: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x74b054: ldur            x2, [fp, #-0x48]
    // 0x74b058: ldur            x4, [fp, #-0x18]
    // 0x74b05c: ldur            x3, [fp, #-0x58]
    // 0x74b060: add             x0, x3, #1
    // 0x74b064: lsl             x1, x0, #1
    // 0x74b068: StoreField: r2->field_b = r1
    //     0x74b068: stur            w1, [x2, #0xb]
    // 0x74b06c: LoadField: r1 = r2->field_f
    //     0x74b06c: ldur            w1, [x2, #0xf]
    // 0x74b070: DecompressPointer r1
    //     0x74b070: add             x1, x1, HEAP, lsl #32
    // 0x74b074: ldur            x0, [fp, #-0x50]
    // 0x74b078: ArrayStore: r1[r3] = r0  ; List_4
    //     0x74b078: add             x25, x1, x3, lsl #2
    //     0x74b07c: add             x25, x25, #0xf
    //     0x74b080: str             w0, [x25]
    //     0x74b084: tbz             w0, #0, #0x74b0a0
    //     0x74b088: ldurb           w16, [x1, #-1]
    //     0x74b08c: ldurb           w17, [x0, #-1]
    //     0x74b090: and             x16, x17, x16, lsr #2
    //     0x74b094: tst             x16, HEAP, lsr #32
    //     0x74b098: b.eq            #0x74b0a0
    //     0x74b09c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x74b0a0: add             x8, x4, #1
    // 0x74b0a4: ldur            x1, [fp, #-0x10]
    // 0x74b0a8: ldur            x3, [fp, #-0x40]
    // 0x74b0ac: ldur            x4, [fp, #-0x38]
    // 0x74b0b0: ldur            x7, [fp, #-0x20]
    // 0x74b0b4: ldur            x5, [fp, #-0x30]
    // 0x74b0b8: ldur            x6, [fp, #-0x28]
    // 0x74b0bc: b               #0x74af04
    // 0x74b0c0: ldur            x2, [fp, #-0x48]
    // 0x74b0c4: mov             x0, x4
    // 0x74b0c8: ldur            x4, [fp, #-0x18]
    // 0x74b0cc: StoreField: r0->field_1f = rNULL
    //     0x74b0cc: stur            NULL, [x0, #0x1f]
    // 0x74b0d0: b               #0x74b0d8
    // 0x74b0d4: mov             x4, x8
    // 0x74b0d8: cmp             x4, #0
    // 0x74b0dc: b.gt            #0x74b0e4
    // 0x74b0e0: r2 = Null
    //     0x74b0e0: mov             x2, NULL
    // 0x74b0e4: ldur            x1, [fp, #-0x10]
    // 0x74b0e8: mov             x0, x2
    // 0x74b0ec: ArrayStore: r1[0] = r0  ; List_4
    //     0x74b0ec: stur            w0, [x1, #0x17]
    //     0x74b0f0: ldurb           w16, [x1, #-1]
    //     0x74b0f4: ldurb           w17, [x0, #-1]
    //     0x74b0f8: and             x16, x17, x16, lsr #2
    //     0x74b0fc: tst             x16, HEAP, lsr #32
    //     0x74b100: b.eq            #0x74b108
    //     0x74b104: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x74b108: cmp             w2, NULL
    // 0x74b10c: r16 = true
    //     0x74b10c: add             x16, NULL, #0x20  ; true
    // 0x74b110: r17 = false
    //     0x74b110: add             x17, NULL, #0x30  ; false
    // 0x74b114: csel            x0, x16, x17, ne
    // 0x74b118: LeaveFrame
    //     0x74b118: mov             SP, fp
    //     0x74b11c: ldp             fp, lr, [SP], #0x10
    // 0x74b120: ret
    //     0x74b120: ret             
    // 0x74b124: ldur            x0, [fp, #-0x30]
    // 0x74b128: r0 = ConcurrentModificationError()
    //     0x74b128: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x74b12c: mov             x1, x0
    // 0x74b130: ldur            x0, [fp, #-0x30]
    // 0x74b134: StoreField: r1->field_b = r0
    //     0x74b134: stur            w0, [x1, #0xb]
    // 0x74b138: mov             x0, x1
    // 0x74b13c: r0 = Throw()
    //     0x74b13c: bl              #0xec04b8  ; ThrowStub
    // 0x74b140: brk             #0
    // 0x74b144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74b144: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74b148: b               #0x74aea8
    // 0x74b14c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74b14c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74b150: b               #0x74af14
  }
}

// class id: 7208, size: 0x18, field offset: 0xc
class _Partition<C1X0> extends Iterable<C1X0> {

  get _ iterator(/* No info */) {
    // ** addr: 0x888e74, size: 0xa4
    // 0x888e74: EnterFrame
    //     0x888e74: stp             fp, lr, [SP, #-0x10]!
    //     0x888e78: mov             fp, SP
    // 0x888e7c: AllocStack(0x20)
    //     0x888e7c: sub             SP, SP, #0x20
    // 0x888e80: SetupParameters(_Partition<C1X0> this /* r1 => r0, fp-0x8 */)
    //     0x888e80: mov             x0, x1
    //     0x888e84: stur            x1, [fp, #-8]
    // 0x888e88: CheckStackOverflow
    //     0x888e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x888e8c: cmp             SP, x16
    //     0x888e90: b.ls            #0x888f10
    // 0x888e94: LoadField: r2 = r0->field_7
    //     0x888e94: ldur            w2, [x0, #7]
    // 0x888e98: DecompressPointer r2
    //     0x888e98: add             x2, x2, HEAP, lsl #32
    // 0x888e9c: r1 = Null
    //     0x888e9c: mov             x1, NULL
    // 0x888ea0: r3 = <C1X0>
    //     0x888ea0: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af98] TypeArguments: <C1X0>
    //     0x888ea4: ldr             x3, [x3, #0xf98]
    // 0x888ea8: r0 = Null
    //     0x888ea8: mov             x0, NULL
    // 0x888eac: cmp             x2, x0
    // 0x888eb0: b.eq            #0x888ec0
    // 0x888eb4: r30 = InstantiateTypeArgumentsStub
    //     0x888eb4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x888eb8: LoadField: r30 = r30->field_7
    //     0x888eb8: ldur            lr, [lr, #7]
    // 0x888ebc: blr             lr
    // 0x888ec0: mov             x2, x0
    // 0x888ec4: ldur            x0, [fp, #-8]
    // 0x888ec8: stur            x2, [fp, #-0x10]
    // 0x888ecc: LoadField: r1 = r0->field_b
    //     0x888ecc: ldur            w1, [x0, #0xb]
    // 0x888ed0: DecompressPointer r1
    //     0x888ed0: add             x1, x1, HEAP, lsl #32
    // 0x888ed4: r0 = iterator()
    //     0x888ed4: bl              #0x8873a8  ; [dart:_internal] ListIterable::iterator
    // 0x888ed8: mov             x2, x0
    // 0x888edc: ldur            x0, [fp, #-8]
    // 0x888ee0: stur            x2, [fp, #-0x20]
    // 0x888ee4: LoadField: r3 = r0->field_f
    //     0x888ee4: ldur            x3, [x0, #0xf]
    // 0x888ee8: ldur            x1, [fp, #-0x10]
    // 0x888eec: stur            x3, [fp, #-0x18]
    // 0x888ef0: r0 = _PartitionIterator()
    //     0x888ef0: bl              #0x888f18  ; Allocate_PartitionIteratorStub -> _PartitionIterator<X0> (size=0x1c)
    // 0x888ef4: ldur            x1, [fp, #-0x20]
    // 0x888ef8: StoreField: r0->field_b = r1
    //     0x888ef8: stur            w1, [x0, #0xb]
    // 0x888efc: ldur            x1, [fp, #-0x18]
    // 0x888f00: StoreField: r0->field_f = r1
    //     0x888f00: stur            x1, [x0, #0xf]
    // 0x888f04: LeaveFrame
    //     0x888f04: mov             SP, fp
    //     0x888f08: ldp             fp, lr, [SP], #0x10
    // 0x888f0c: ret
    //     0x888f0c: ret             
    // 0x888f10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x888f10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x888f14: b               #0x888e94
  }
}
