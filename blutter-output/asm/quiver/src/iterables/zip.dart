// lib: , url: package:quiver/src/iterables/zip.dart

// class id: 1051080, size: 0x8
class :: {

  static Iterable<List<Y0>> zip<Y0>(Iterable<Iterable<Y0>>) {
    // ** addr: 0xc6ca24, size: 0x28c
    // 0xc6ca24: EnterFrame
    //     0xc6ca24: stp             fp, lr, [SP, #-0x10]!
    //     0xc6ca28: mov             fp, SP
    // 0xc6ca2c: AllocStack(0x60)
    //     0xc6ca2c: sub             SP, SP, #0x60
    // 0xc6ca30: SetupParameters(dynamic _ /* r5, fp-0x18 */)
    //     0xc6ca30: stur            NULL, [fp, #-8]
    //     0xc6ca34: movz            x0, #0
    //     0xc6ca38: add             x5, fp, w0, sxtw #2
    //     0xc6ca3c: ldr             x5, [x5, #0x10]
    //     0xc6ca40: stur            x5, [fp, #-0x18]
    // 0xc6ca44: LoadField: r1 = r4->field_f
    //     0xc6ca44: ldur            w1, [x4, #0xf]
    // 0xc6ca48: cbnz            w1, #0xc6ca54
    // 0xc6ca4c: r4 = Null
    //     0xc6ca4c: mov             x4, NULL
    // 0xc6ca50: b               #0xc6ca64
    // 0xc6ca54: ArrayLoad: r1 = r4[0]  ; List_4
    //     0xc6ca54: ldur            w1, [x4, #0x17]
    // 0xc6ca58: add             x2, fp, w1, sxtw #2
    // 0xc6ca5c: ldr             x2, [x2, #0x10]
    // 0xc6ca60: mov             x4, x2
    // 0xc6ca64: stur            x4, [fp, #-0x10]
    // 0xc6ca68: CheckStackOverflow
    //     0xc6ca68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6ca6c: cmp             SP, x16
    //     0xc6ca70: b.ls            #0xc6cc98
    // 0xc6ca74: mov             x1, x4
    // 0xc6ca78: r2 = Null
    //     0xc6ca78: mov             x2, NULL
    // 0xc6ca7c: r3 = <List<Y0>>
    //     0xc6ca7c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41d50] TypeArguments: <List<Y0>>
    //     0xc6ca80: ldr             x3, [x3, #0xd50]
    // 0xc6ca84: r30 = InstantiateTypeArgumentsStub
    //     0xc6ca84: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xc6ca88: LoadField: r30 = r30->field_7
    //     0xc6ca88: ldur            lr, [lr, #7]
    // 0xc6ca8c: blr             lr
    // 0xc6ca90: mov             x1, x0
    // 0xc6ca94: stur            x1, [fp, #-0x20]
    // 0xc6ca98: r0 = InitAsync()
    //     0xc6ca98: bl              #0x7348c0  ; InitAsyncStub
    // 0xc6ca9c: r0 = Null
    //     0xc6ca9c: mov             x0, NULL
    // 0xc6caa0: r0 = SuspendSyncStarAtStart()
    //     0xc6caa0: bl              #0x734738  ; SuspendSyncStarAtStartStub
    // 0xc6caa4: ldur            x0, [fp, #-0x18]
    // 0xc6caa8: LoadField: r1 = r0->field_b
    //     0xc6caa8: ldur            w1, [x0, #0xb]
    // 0xc6caac: cbnz            w1, #0xc6cac0
    // 0xc6cab0: r0 = false
    //     0xc6cab0: add             x0, NULL, #0x30  ; false
    // 0xc6cab4: LeaveFrame
    //     0xc6cab4: mov             SP, fp
    //     0xc6cab8: ldp             fp, lr, [SP], #0x10
    // 0xc6cabc: ret
    //     0xc6cabc: ret             
    // 0xc6cac0: ldur            x4, [fp, #-0x10]
    // 0xc6cac4: mov             x1, x4
    // 0xc6cac8: r2 = Null
    //     0xc6cac8: mov             x2, NULL
    // 0xc6cacc: r3 = <Iterator<Y0>>
    //     0xc6cacc: add             x3, PP, #0x41, lsl #12  ; [pp+0x41d58] TypeArguments: <Iterator<Y0>>
    //     0xc6cad0: ldr             x3, [x3, #0xd58]
    // 0xc6cad4: r30 = InstantiateTypeArgumentsStub
    //     0xc6cad4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xc6cad8: LoadField: r30 = r30->field_7
    //     0xc6cad8: ldur            lr, [lr, #7]
    // 0xc6cadc: blr             lr
    // 0xc6cae0: r1 = Function '<anonymous closure>': static.
    //     0xc6cae0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d60] AnonymousClosure: static (0xc6ccf8), in [package:quiver/src/iterables/zip.dart] ::zip (0xc6ca24)
    //     0xc6cae4: ldr             x1, [x1, #0xd60]
    // 0xc6cae8: r2 = Null
    //     0xc6cae8: mov             x2, NULL
    // 0xc6caec: stur            x0, [fp, #-0x20]
    // 0xc6caf0: r0 = AllocateClosure()
    //     0xc6caf0: bl              #0xec1630  ; AllocateClosureStub
    // 0xc6caf4: ldur            x1, [fp, #-0x10]
    // 0xc6caf8: StoreField: r0->field_b = r1
    //     0xc6caf8: stur            w1, [x0, #0xb]
    // 0xc6cafc: ldur            x16, [fp, #-0x20]
    // 0xc6cb00: ldur            lr, [fp, #-0x18]
    // 0xc6cb04: stp             lr, x16, [SP, #8]
    // 0xc6cb08: str             x0, [SP]
    // 0xc6cb0c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc6cb0c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc6cb10: r0 = map()
    //     0xc6cb10: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xc6cb14: LoadField: r1 = r0->field_7
    //     0xc6cb14: ldur            w1, [x0, #7]
    // 0xc6cb18: DecompressPointer r1
    //     0xc6cb18: add             x1, x1, HEAP, lsl #32
    // 0xc6cb1c: mov             x2, x0
    // 0xc6cb20: r0 = _List.of()
    //     0xc6cb20: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0xc6cb24: mov             x2, x0
    // 0xc6cb28: stur            x2, [fp, #-0x20]
    // 0xc6cb2c: LoadField: r0 = r2->field_b
    //     0xc6cb2c: ldur            w0, [x2, #0xb]
    // 0xc6cb30: r3 = LoadInt32Instr(r0)
    //     0xc6cb30: sbfx            x3, x0, #1, #0x1f
    // 0xc6cb34: stur            x3, [fp, #-0x30]
    // 0xc6cb38: LoadField: r4 = r2->field_7
    //     0xc6cb38: ldur            w4, [x2, #7]
    // 0xc6cb3c: DecompressPointer r4
    //     0xc6cb3c: add             x4, x4, HEAP, lsl #32
    // 0xc6cb40: stur            x4, [fp, #-0x18]
    // 0xc6cb44: ldur            x5, [fp, #-0x10]
    // 0xc6cb48: CheckStackOverflow
    //     0xc6cb48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6cb4c: cmp             SP, x16
    //     0xc6cb50: b.ls            #0xc6cca0
    // 0xc6cb54: r6 = 0
    //     0xc6cb54: movz            x6, #0
    // 0xc6cb58: stur            x6, [fp, #-0x28]
    // 0xc6cb5c: CheckStackOverflow
    //     0xc6cb5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6cb60: cmp             SP, x16
    //     0xc6cb64: b.ls            #0xc6cca8
    // 0xc6cb68: cmp             x6, x3
    // 0xc6cb6c: b.ge            #0xc6cbc8
    // 0xc6cb70: ArrayLoad: r1 = r2[r6]  ; Unknown_4
    //     0xc6cb70: add             x16, x2, x6, lsl #2
    //     0xc6cb74: ldur            w1, [x16, #0xf]
    // 0xc6cb78: DecompressPointer r1
    //     0xc6cb78: add             x1, x1, HEAP, lsl #32
    // 0xc6cb7c: r0 = LoadClassIdInstr(r1)
    //     0xc6cb7c: ldur            x0, [x1, #-1]
    //     0xc6cb80: ubfx            x0, x0, #0xc, #0x14
    // 0xc6cb84: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xc6cb84: movz            x17, #0x292d
    //     0xc6cb88: movk            x17, #0x1, lsl #16
    //     0xc6cb8c: add             lr, x0, x17
    //     0xc6cb90: ldr             lr, [x21, lr, lsl #3]
    //     0xc6cb94: blr             lr
    // 0xc6cb98: tbnz            w0, #4, #0xc6cbb8
    // 0xc6cb9c: ldur            x0, [fp, #-0x28]
    // 0xc6cba0: add             x6, x0, #1
    // 0xc6cba4: ldur            x5, [fp, #-0x10]
    // 0xc6cba8: ldur            x4, [fp, #-0x18]
    // 0xc6cbac: ldur            x2, [fp, #-0x20]
    // 0xc6cbb0: ldur            x3, [fp, #-0x30]
    // 0xc6cbb4: b               #0xc6cb58
    // 0xc6cbb8: r0 = false
    //     0xc6cbb8: add             x0, NULL, #0x30  ; false
    // 0xc6cbbc: LeaveFrame
    //     0xc6cbbc: mov             SP, fp
    //     0xc6cbc0: ldp             fp, lr, [SP], #0x10
    // 0xc6cbc4: ret
    //     0xc6cbc4: ret             
    // 0xc6cbc8: mov             x3, x5
    // 0xc6cbcc: mov             x0, x2
    // 0xc6cbd0: r4 = 0
    //     0xc6cbd0: movz            x4, #0
    // 0xc6cbd4: add             x1, fp, w4, sxtw #2
    // 0xc6cbd8: LoadField: r1 = r1->field_fffffff8
    //     0xc6cbd8: ldur            x1, [x1, #-8]
    // 0xc6cbdc: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xc6cbdc: ldur            w5, [x1, #0x17]
    // 0xc6cbe0: DecompressPointer r5
    //     0xc6cbe0: add             x5, x5, HEAP, lsl #32
    // 0xc6cbe4: stur            x5, [fp, #-0x38]
    // 0xc6cbe8: r1 = Function '<anonymous closure>': static.
    //     0xc6cbe8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d68] AnonymousClosure: static (0xc6ccb0), in [package:quiver/src/iterables/zip.dart] ::zip (0xc6ca24)
    //     0xc6cbec: ldr             x1, [x1, #0xd68]
    // 0xc6cbf0: r2 = Null
    //     0xc6cbf0: mov             x2, NULL
    // 0xc6cbf4: r0 = AllocateClosure()
    //     0xc6cbf4: bl              #0xec1630  ; AllocateClosureStub
    // 0xc6cbf8: mov             x4, x0
    // 0xc6cbfc: ldur            x0, [fp, #-0x10]
    // 0xc6cc00: stur            x4, [fp, #-0x40]
    // 0xc6cc04: StoreField: r4->field_b = r0
    //     0xc6cc04: stur            w0, [x4, #0xb]
    // 0xc6cc08: ldur            x2, [fp, #-0x18]
    // 0xc6cc0c: mov             x1, x0
    // 0xc6cc10: r3 = <Y0, X0, Y0>
    //     0xc6cc10: ldr             x3, [PP, #0xea0]  ; [pp+0xea0] TypeArguments: <Y0, X0, Y0>
    // 0xc6cc14: r0 = Null
    //     0xc6cc14: mov             x0, NULL
    // 0xc6cc18: cmp             x2, x0
    // 0xc6cc1c: b.ne            #0xc6cc28
    // 0xc6cc20: cmp             x1, x0
    // 0xc6cc24: b.eq            #0xc6cc34
    // 0xc6cc28: r30 = InstantiateTypeArgumentsStub
    //     0xc6cc28: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xc6cc2c: LoadField: r30 = r30->field_7
    //     0xc6cc2c: ldur            lr, [lr, #7]
    // 0xc6cc30: blr             lr
    // 0xc6cc34: mov             x1, x0
    // 0xc6cc38: stur            x0, [fp, #-0x48]
    // 0xc6cc3c: r0 = MappedListIterable()
    //     0xc6cc3c: bl              #0x7abee0  ; AllocateMappedListIterableStub -> MappedListIterable<C1X0, C1X1> (size=0x14)
    // 0xc6cc40: mov             x1, x0
    // 0xc6cc44: ldur            x0, [fp, #-0x20]
    // 0xc6cc48: StoreField: r1->field_b = r0
    //     0xc6cc48: stur            w0, [x1, #0xb]
    // 0xc6cc4c: ldur            x2, [fp, #-0x40]
    // 0xc6cc50: StoreField: r1->field_f = r2
    //     0xc6cc50: stur            w2, [x1, #0xf]
    // 0xc6cc54: mov             x2, x1
    // 0xc6cc58: ldur            x1, [fp, #-0x48]
    // 0xc6cc5c: r0 = _List.of()
    //     0xc6cc5c: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0xc6cc60: ldur            x1, [fp, #-0x38]
    // 0xc6cc64: ArrayStore: r1[0] = r0  ; List_4
    //     0xc6cc64: stur            w0, [x1, #0x17]
    //     0xc6cc68: ldurb           w16, [x1, #-1]
    //     0xc6cc6c: ldurb           w17, [x0, #-1]
    //     0xc6cc70: and             x16, x17, x16, lsr #2
    //     0xc6cc74: tst             x16, HEAP, lsr #32
    //     0xc6cc78: b.eq            #0xc6cc80
    //     0xc6cc7c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6cc80: r0 = true
    //     0xc6cc80: add             x0, NULL, #0x20  ; true
    // 0xc6cc84: r0 = SuspendSyncStarAtYield()
    //     0xc6cc84: bl              #0x7345b4  ; SuspendSyncStarAtYieldStub
    // 0xc6cc88: ldur            x4, [fp, #-0x18]
    // 0xc6cc8c: ldur            x2, [fp, #-0x20]
    // 0xc6cc90: ldur            x3, [fp, #-0x30]
    // 0xc6cc94: b               #0xc6cb44
    // 0xc6cc98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6cc98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6cc9c: b               #0xc6ca74
    // 0xc6cca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6cca0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6cca4: b               #0xc6cb54
    // 0xc6cca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6cca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6ccac: b               #0xc6cb68
  }
  [closure] static Y0 <anonymous closure>(dynamic, Iterator<Y0>) {
    // ** addr: 0xc6ccb0, size: 0x48
    // 0xc6ccb0: EnterFrame
    //     0xc6ccb0: stp             fp, lr, [SP, #-0x10]!
    //     0xc6ccb4: mov             fp, SP
    // 0xc6ccb8: CheckStackOverflow
    //     0xc6ccb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6ccbc: cmp             SP, x16
    //     0xc6ccc0: b.ls            #0xc6ccf0
    // 0xc6ccc4: ldr             x1, [fp, #0x10]
    // 0xc6ccc8: r0 = LoadClassIdInstr(r1)
    //     0xc6ccc8: ldur            x0, [x1, #-1]
    //     0xc6cccc: ubfx            x0, x0, #0xc, #0x14
    // 0xc6ccd0: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xc6ccd0: movz            x17, #0x384d
    //     0xc6ccd4: movk            x17, #0x1, lsl #16
    //     0xc6ccd8: add             lr, x0, x17
    //     0xc6ccdc: ldr             lr, [x21, lr, lsl #3]
    //     0xc6cce0: blr             lr
    // 0xc6cce4: LeaveFrame
    //     0xc6cce4: mov             SP, fp
    //     0xc6cce8: ldp             fp, lr, [SP], #0x10
    // 0xc6ccec: ret
    //     0xc6ccec: ret             
    // 0xc6ccf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6ccf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6ccf4: b               #0xc6ccc4
  }
  [closure] static Iterator<Y0> <anonymous closure>(dynamic, Iterable<Y0>) {
    // ** addr: 0xc6ccf8, size: 0x44
    // 0xc6ccf8: EnterFrame
    //     0xc6ccf8: stp             fp, lr, [SP, #-0x10]!
    //     0xc6ccfc: mov             fp, SP
    // 0xc6cd00: CheckStackOverflow
    //     0xc6cd00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6cd04: cmp             SP, x16
    //     0xc6cd08: b.ls            #0xc6cd34
    // 0xc6cd0c: ldr             x1, [fp, #0x10]
    // 0xc6cd10: r0 = LoadClassIdInstr(r1)
    //     0xc6cd10: ldur            x0, [x1, #-1]
    //     0xc6cd14: ubfx            x0, x0, #0xc, #0x14
    // 0xc6cd18: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xc6cd18: movz            x17, #0xd35d
    //     0xc6cd1c: add             lr, x0, x17
    //     0xc6cd20: ldr             lr, [x21, lr, lsl #3]
    //     0xc6cd24: blr             lr
    // 0xc6cd28: LeaveFrame
    //     0xc6cd28: mov             SP, fp
    //     0xc6cd2c: ldp             fp, lr, [SP], #0x10
    // 0xc6cd30: ret
    //     0xc6cd30: ret             
    // 0xc6cd34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6cd34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6cd38: b               #0xc6cd0c
  }
}
