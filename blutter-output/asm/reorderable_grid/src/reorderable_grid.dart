// lib: , url: package:reorderable_grid/src/reorderable_grid.dart

// class id: 1051081, size: 0x8
class :: {

  static _ _overlayOrigin(/* No info */) {
    // ** addr: 0xbb8810, size: 0xa0
    // 0xbb8810: EnterFrame
    //     0xbb8810: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8814: mov             fp, SP
    // 0xbb8818: AllocStack(0x8)
    //     0xbb8818: sub             SP, SP, #8
    // 0xbb881c: CheckStackOverflow
    //     0xbb881c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8820: cmp             SP, x16
    //     0xbb8824: b.ls            #0xbb88a0
    // 0xbb8828: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb8828: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb882c: r0 = of()
    //     0xbb882c: bl              #0x6a5014  ; [package:flutter/src/widgets/overlay.dart] Overlay::of
    // 0xbb8830: LoadField: r1 = r0->field_f
    //     0xbb8830: ldur            w1, [x0, #0xf]
    // 0xbb8834: DecompressPointer r1
    //     0xbb8834: add             x1, x1, HEAP, lsl #32
    // 0xbb8838: cmp             w1, NULL
    // 0xbb883c: b.eq            #0xbb88a8
    // 0xbb8840: r0 = renderObject()
    //     0xbb8840: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xbb8844: mov             x3, x0
    // 0xbb8848: stur            x3, [fp, #-8]
    // 0xbb884c: cmp             w3, NULL
    // 0xbb8850: b.eq            #0xbb88ac
    // 0xbb8854: mov             x0, x3
    // 0xbb8858: r2 = Null
    //     0xbb8858: mov             x2, NULL
    // 0xbb885c: r1 = Null
    //     0xbb885c: mov             x1, NULL
    // 0xbb8860: r4 = LoadClassIdInstr(r0)
    //     0xbb8860: ldur            x4, [x0, #-1]
    //     0xbb8864: ubfx            x4, x4, #0xc, #0x14
    // 0xbb8868: sub             x4, x4, #0xbba
    // 0xbb886c: cmp             x4, #0x9a
    // 0xbb8870: b.ls            #0xbb8884
    // 0xbb8874: r8 = RenderBox
    //     0xbb8874: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0xbb8878: r3 = Null
    //     0xbb8878: add             x3, PP, #0x51, lsl #12  ; [pp+0x51418] Null
    //     0xbb887c: ldr             x3, [x3, #0x418]
    // 0xbb8880: r0 = RenderBox()
    //     0xbb8880: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0xbb8884: ldur            x1, [fp, #-8]
    // 0xbb8888: r2 = Instance_Offset
    //     0xbb8888: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xbb888c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb888c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb8890: r0 = localToGlobal()
    //     0xbb8890: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0xbb8894: LeaveFrame
    //     0xbb8894: mov             SP, fp
    //     0xbb8898: ldp             fp, lr, [SP], #0x10
    // 0xbb889c: ret
    //     0xbb889c: ret             
    // 0xbb88a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb88a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb88a4: b               #0xbb8828
    // 0xbb88a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb88a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb88ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb88ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3511, size: 0x44, field offset: 0x8
class _DragInfo extends Drag {

  late CapturedThemes capturedThemes; // offset: 0x38
  late SliverReorderableGridState listState; // offset: 0x20
  late int index; // offset: 0x24
  late Size itemSize; // offset: 0x34
  late Offset dragPosition; // offset: 0x2c
  late Offset dragOffset; // offset: 0x30
  late Widget child; // offset: 0x28

  _ dispose(/* No info */) {
    // ** addr: 0x9a1644, size: 0x44
    // 0x9a1644: EnterFrame
    //     0x9a1644: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1648: mov             fp, SP
    // 0x9a164c: CheckStackOverflow
    //     0x9a164c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1650: cmp             SP, x16
    //     0x9a1654: b.ls            #0x9a1680
    // 0x9a1658: LoadField: r0 = r1->field_3f
    //     0x9a1658: ldur            w0, [x1, #0x3f]
    // 0x9a165c: DecompressPointer r0
    //     0x9a165c: add             x0, x0, HEAP, lsl #32
    // 0x9a1660: cmp             w0, NULL
    // 0x9a1664: b.eq            #0x9a1670
    // 0x9a1668: mov             x1, x0
    // 0x9a166c: r0 = dispose()
    //     0x9a166c: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0x9a1670: r0 = Null
    //     0x9a1670: mov             x0, NULL
    // 0x9a1674: LeaveFrame
    //     0x9a1674: mov             SP, fp
    //     0x9a1678: ldp             fp, lr, [SP], #0x10
    // 0x9a167c: ret
    //     0x9a167c: ret             
    // 0x9a1680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1680: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1684: b               #0x9a1658
  }
  _ startDrag(/* No info */) {
    // ** addr: 0xbb8038, size: 0xd0
    // 0xbb8038: EnterFrame
    //     0xbb8038: stp             fp, lr, [SP, #-0x10]!
    //     0xbb803c: mov             fp, SP
    // 0xbb8040: AllocStack(0x28)
    //     0xbb8040: sub             SP, SP, #0x28
    // 0xbb8044: SetupParameters(_DragInfo this /* r1 => r1, fp-0x8 */)
    //     0xbb8044: stur            x1, [fp, #-8]
    // 0xbb8048: CheckStackOverflow
    //     0xbb8048: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb804c: cmp             SP, x16
    //     0xbb8050: b.ls            #0xbb8100
    // 0xbb8054: r1 = 1
    //     0xbb8054: movz            x1, #0x1
    // 0xbb8058: r0 = AllocateContext()
    //     0xbb8058: bl              #0xec126c  ; AllocateContextStub
    // 0xbb805c: mov             x2, x0
    // 0xbb8060: ldur            x0, [fp, #-8]
    // 0xbb8064: stur            x2, [fp, #-0x18]
    // 0xbb8068: StoreField: r2->field_f = r0
    //     0xbb8068: stur            w0, [x2, #0xf]
    // 0xbb806c: LoadField: r3 = r0->field_1b
    //     0xbb806c: ldur            w3, [x0, #0x1b]
    // 0xbb8070: DecompressPointer r3
    //     0xbb8070: add             x3, x3, HEAP, lsl #32
    // 0xbb8074: stur            x3, [fp, #-0x10]
    // 0xbb8078: r1 = <double>
    //     0xbb8078: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xbb807c: r0 = AnimationController()
    //     0xbb807c: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0xbb8080: stur            x0, [fp, #-0x20]
    // 0xbb8084: r16 = Instance_Duration
    //     0xbb8084: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xbb8088: ldr             x16, [x16, #0xd90]
    // 0xbb808c: str             x16, [SP]
    // 0xbb8090: mov             x1, x0
    // 0xbb8094: ldur            x2, [fp, #-0x10]
    // 0xbb8098: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0xbb8098: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0xbb809c: ldr             x4, [x4, #0x408]
    // 0xbb80a0: r0 = AnimationController()
    //     0xbb80a0: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0xbb80a4: ldur            x2, [fp, #-0x18]
    // 0xbb80a8: r1 = Function '<anonymous closure>':.
    //     0xbb80a8: add             x1, PP, #0x51, lsl #12  ; [pp+0x514a8] AnonymousClosure: (0xbb8108), in [package:reorderable_grid/src/reorderable_grid.dart] _DragInfo::startDrag (0xbb8038)
    //     0xbb80ac: ldr             x1, [x1, #0x4a8]
    // 0xbb80b0: r0 = AllocateClosure()
    //     0xbb80b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb80b4: ldur            x1, [fp, #-0x20]
    // 0xbb80b8: mov             x2, x0
    // 0xbb80bc: r0 = addStatusListener()
    //     0xbb80bc: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0xbb80c0: ldur            x1, [fp, #-0x20]
    // 0xbb80c4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb80c4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb80c8: r0 = forward()
    //     0xbb80c8: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xbb80cc: ldur            x0, [fp, #-0x20]
    // 0xbb80d0: ldur            x1, [fp, #-8]
    // 0xbb80d4: StoreField: r1->field_3f = r0
    //     0xbb80d4: stur            w0, [x1, #0x3f]
    //     0xbb80d8: ldurb           w16, [x1, #-1]
    //     0xbb80dc: ldurb           w17, [x0, #-1]
    //     0xbb80e0: and             x16, x17, x16, lsr #2
    //     0xbb80e4: tst             x16, HEAP, lsr #32
    //     0xbb80e8: b.eq            #0xbb80f0
    //     0xbb80ec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbb80f0: r0 = Null
    //     0xbb80f0: mov             x0, NULL
    // 0xbb80f4: LeaveFrame
    //     0xbb80f4: mov             SP, fp
    //     0xbb80f8: ldp             fp, lr, [SP], #0x10
    // 0xbb80fc: ret
    //     0xbb80fc: ret             
    // 0xbb8100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8104: b               #0xbb8054
  }
  [closure] void <anonymous closure>(dynamic, AnimationStatus) {
    // ** addr: 0xbb8108, size: 0x58
    // 0xbb8108: EnterFrame
    //     0xbb8108: stp             fp, lr, [SP, #-0x10]!
    //     0xbb810c: mov             fp, SP
    // 0xbb8110: ldr             x0, [fp, #0x18]
    // 0xbb8114: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb8114: ldur            w1, [x0, #0x17]
    // 0xbb8118: DecompressPointer r1
    //     0xbb8118: add             x1, x1, HEAP, lsl #32
    // 0xbb811c: CheckStackOverflow
    //     0xbb811c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8120: cmp             SP, x16
    //     0xbb8124: b.ls            #0xbb8158
    // 0xbb8128: ldr             x0, [fp, #0x10]
    // 0xbb812c: r16 = Instance_AnimationStatus
    //     0xbb812c: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0xbb8130: cmp             w0, w16
    // 0xbb8134: b.ne            #0xbb8148
    // 0xbb8138: LoadField: r0 = r1->field_f
    //     0xbb8138: ldur            w0, [x1, #0xf]
    // 0xbb813c: DecompressPointer r0
    //     0xbb813c: add             x0, x0, HEAP, lsl #32
    // 0xbb8140: mov             x1, x0
    // 0xbb8144: r0 = _dropCompleted()
    //     0xbb8144: bl              #0xbb8160  ; [package:reorderable_grid/src/reorderable_grid.dart] _DragInfo::_dropCompleted
    // 0xbb8148: r0 = Null
    //     0xbb8148: mov             x0, NULL
    // 0xbb814c: LeaveFrame
    //     0xbb814c: mov             SP, fp
    //     0xbb8150: ldp             fp, lr, [SP], #0x10
    // 0xbb8154: ret
    //     0xbb8154: ret             
    // 0xbb8158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8158: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb815c: b               #0xbb8128
  }
  _ _dropCompleted(/* No info */) {
    // ** addr: 0xbb8160, size: 0x78
    // 0xbb8160: EnterFrame
    //     0xbb8160: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8164: mov             fp, SP
    // 0xbb8168: AllocStack(0x8)
    //     0xbb8168: sub             SP, SP, #8
    // 0xbb816c: SetupParameters(_DragInfo this /* r1 => r0, fp-0x8 */)
    //     0xbb816c: mov             x0, x1
    //     0xbb8170: stur            x1, [fp, #-8]
    // 0xbb8174: CheckStackOverflow
    //     0xbb8174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8178: cmp             SP, x16
    //     0xbb817c: b.ls            #0xbb81cc
    // 0xbb8180: LoadField: r1 = r0->field_3f
    //     0xbb8180: ldur            w1, [x0, #0x3f]
    // 0xbb8184: DecompressPointer r1
    //     0xbb8184: add             x1, x1, HEAP, lsl #32
    // 0xbb8188: cmp             w1, NULL
    // 0xbb818c: b.eq            #0xbb8198
    // 0xbb8190: r0 = dispose()
    //     0xbb8190: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xbb8194: ldur            x0, [fp, #-8]
    // 0xbb8198: StoreField: r0->field_3f = rNULL
    //     0xbb8198: stur            NULL, [x0, #0x3f]
    // 0xbb819c: LoadField: r1 = r0->field_13
    //     0xbb819c: ldur            w1, [x0, #0x13]
    // 0xbb81a0: DecompressPointer r1
    //     0xbb81a0: add             x1, x1, HEAP, lsl #32
    // 0xbb81a4: cmp             w1, NULL
    // 0xbb81a8: b.eq            #0xbb81d4
    // 0xbb81ac: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbb81ac: ldur            w0, [x1, #0x17]
    // 0xbb81b0: DecompressPointer r0
    //     0xbb81b0: add             x0, x0, HEAP, lsl #32
    // 0xbb81b4: mov             x1, x0
    // 0xbb81b8: r0 = _dropCompleted()
    //     0xbb81b8: bl              #0xbb81d8  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dropCompleted
    // 0xbb81bc: r0 = Null
    //     0xbb81bc: mov             x0, NULL
    // 0xbb81c0: LeaveFrame
    //     0xbb81c0: mov             SP, fp
    //     0xbb81c4: ldp             fp, lr, [SP], #0x10
    // 0xbb81c8: ret
    //     0xbb81c8: ret             
    // 0xbb81cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb81cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb81d0: b               #0xbb8180
    // 0xbb81d4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbb81d4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _DragInfo(/* No info */) {
    // ** addr: 0xbb8288, size: 0x360
    // 0xbb8288: EnterFrame
    //     0xbb8288: stp             fp, lr, [SP, #-0x10]!
    //     0xbb828c: mov             fp, SP
    // 0xbb8290: AllocStack(0x20)
    //     0xbb8290: sub             SP, SP, #0x20
    // 0xbb8294: r0 = Sentinel
    //     0xbb8294: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb8298: mov             x4, x3
    // 0xbb829c: stur            x3, [fp, #-0x18]
    // 0xbb82a0: mov             x3, x5
    // 0xbb82a4: mov             x5, x2
    // 0xbb82a8: stur            x2, [fp, #-0x10]
    // 0xbb82ac: mov             x2, x6
    // 0xbb82b0: mov             x6, x1
    // 0xbb82b4: stur            x1, [fp, #-8]
    // 0xbb82b8: mov             x1, x7
    // 0xbb82bc: CheckStackOverflow
    //     0xbb82bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb82c0: cmp             SP, x16
    //     0xbb82c4: b.ls            #0xbb85bc
    // 0xbb82c8: StoreField: r6->field_1f = r0
    //     0xbb82c8: stur            w0, [x6, #0x1f]
    // 0xbb82cc: StoreField: r6->field_23 = r0
    //     0xbb82cc: stur            w0, [x6, #0x23]
    // 0xbb82d0: StoreField: r6->field_27 = r0
    //     0xbb82d0: stur            w0, [x6, #0x27]
    // 0xbb82d4: StoreField: r6->field_2b = r0
    //     0xbb82d4: stur            w0, [x6, #0x2b]
    // 0xbb82d8: StoreField: r6->field_2f = r0
    //     0xbb82d8: stur            w0, [x6, #0x2f]
    // 0xbb82dc: StoreField: r6->field_33 = r0
    //     0xbb82dc: stur            w0, [x6, #0x33]
    // 0xbb82e0: StoreField: r6->field_37 = r0
    //     0xbb82e0: stur            w0, [x6, #0x37]
    // 0xbb82e4: ldr             x0, [fp, #0x20]
    // 0xbb82e8: StoreField: r6->field_7 = r0
    //     0xbb82e8: stur            w0, [x6, #7]
    //     0xbb82ec: ldurb           w16, [x6, #-1]
    //     0xbb82f0: ldurb           w17, [x0, #-1]
    //     0xbb82f4: and             x16, x17, x16, lsr #2
    //     0xbb82f8: tst             x16, HEAP, lsr #32
    //     0xbb82fc: b.eq            #0xbb8304
    //     0xbb8300: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xbb8304: mov             x0, x1
    // 0xbb8308: StoreField: r6->field_b = r0
    //     0xbb8308: stur            w0, [x6, #0xb]
    //     0xbb830c: ldurb           w16, [x6, #-1]
    //     0xbb8310: ldurb           w17, [x0, #-1]
    //     0xbb8314: and             x16, x17, x16, lsr #2
    //     0xbb8318: tst             x16, HEAP, lsr #32
    //     0xbb831c: b.eq            #0xbb8324
    //     0xbb8320: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xbb8324: mov             x0, x3
    // 0xbb8328: StoreField: r6->field_f = r0
    //     0xbb8328: stur            w0, [x6, #0xf]
    //     0xbb832c: ldurb           w16, [x6, #-1]
    //     0xbb8330: ldurb           w17, [x0, #-1]
    //     0xbb8334: and             x16, x17, x16, lsr #2
    //     0xbb8338: tst             x16, HEAP, lsr #32
    //     0xbb833c: b.eq            #0xbb8344
    //     0xbb8340: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xbb8344: mov             x0, x2
    // 0xbb8348: StoreField: r6->field_13 = r0
    //     0xbb8348: stur            w0, [x6, #0x13]
    //     0xbb834c: ldurb           w16, [x6, #-1]
    //     0xbb8350: ldurb           w17, [x0, #-1]
    //     0xbb8354: and             x16, x17, x16, lsr #2
    //     0xbb8358: tst             x16, HEAP, lsr #32
    //     0xbb835c: b.eq            #0xbb8364
    //     0xbb8360: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xbb8364: ldr             x0, [fp, #0x18]
    // 0xbb8368: ArrayStore: r6[0] = r0  ; List_4
    //     0xbb8368: stur            w0, [x6, #0x17]
    //     0xbb836c: ldurb           w16, [x6, #-1]
    //     0xbb8370: ldurb           w17, [x0, #-1]
    //     0xbb8374: and             x16, x17, x16, lsr #2
    //     0xbb8378: tst             x16, HEAP, lsr #32
    //     0xbb837c: b.eq            #0xbb8384
    //     0xbb8380: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xbb8384: ldr             x0, [fp, #0x10]
    // 0xbb8388: StoreField: r6->field_1b = r0
    //     0xbb8388: stur            w0, [x6, #0x1b]
    //     0xbb838c: ldurb           w16, [x6, #-1]
    //     0xbb8390: ldurb           w17, [x0, #-1]
    //     0xbb8394: and             x16, x17, x16, lsr #2
    //     0xbb8398: tst             x16, HEAP, lsr #32
    //     0xbb839c: b.eq            #0xbb83a4
    //     0xbb83a0: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xbb83a4: LoadField: r1 = r4->field_f
    //     0xbb83a4: ldur            w1, [x4, #0xf]
    // 0xbb83a8: DecompressPointer r1
    //     0xbb83a8: add             x1, x1, HEAP, lsl #32
    // 0xbb83ac: cmp             w1, NULL
    // 0xbb83b0: b.eq            #0xbb85c4
    // 0xbb83b4: r0 = renderObject()
    //     0xbb83b4: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xbb83b8: mov             x3, x0
    // 0xbb83bc: stur            x3, [fp, #-0x20]
    // 0xbb83c0: cmp             w3, NULL
    // 0xbb83c4: b.eq            #0xbb85c8
    // 0xbb83c8: mov             x0, x3
    // 0xbb83cc: r2 = Null
    //     0xbb83cc: mov             x2, NULL
    // 0xbb83d0: r1 = Null
    //     0xbb83d0: mov             x1, NULL
    // 0xbb83d4: r4 = LoadClassIdInstr(r0)
    //     0xbb83d4: ldur            x4, [x0, #-1]
    //     0xbb83d8: ubfx            x4, x4, #0xc, #0x14
    // 0xbb83dc: sub             x4, x4, #0xbba
    // 0xbb83e0: cmp             x4, #0x9a
    // 0xbb83e4: b.ls            #0xbb83f8
    // 0xbb83e8: r8 = RenderBox
    //     0xbb83e8: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0xbb83ec: r3 = Null
    //     0xbb83ec: add             x3, PP, #0x51, lsl #12  ; [pp+0x514b0] Null
    //     0xbb83f0: ldr             x3, [x3, #0x4b0]
    // 0xbb83f4: r0 = RenderBox()
    //     0xbb83f4: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0xbb83f8: ldur            x3, [fp, #-0x18]
    // 0xbb83fc: LoadField: r0 = r3->field_13
    //     0xbb83fc: ldur            w0, [x3, #0x13]
    // 0xbb8400: DecompressPointer r0
    //     0xbb8400: add             x0, x0, HEAP, lsl #32
    // 0xbb8404: r16 = Sentinel
    //     0xbb8404: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb8408: cmp             w0, w16
    // 0xbb840c: b.eq            #0xbb85cc
    // 0xbb8410: ldur            x4, [fp, #-8]
    // 0xbb8414: StoreField: r4->field_1f = r0
    //     0xbb8414: stur            w0, [x4, #0x1f]
    //     0xbb8418: ldurb           w16, [x4, #-1]
    //     0xbb841c: ldurb           w17, [x0, #-1]
    //     0xbb8420: and             x16, x17, x16, lsr #2
    //     0xbb8424: tst             x16, HEAP, lsr #32
    //     0xbb8428: b.eq            #0xbb8430
    //     0xbb842c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xbb8430: LoadField: r2 = r3->field_b
    //     0xbb8430: ldur            w2, [x3, #0xb]
    // 0xbb8434: DecompressPointer r2
    //     0xbb8434: add             x2, x2, HEAP, lsl #32
    // 0xbb8438: cmp             w2, NULL
    // 0xbb843c: b.eq            #0xbb85d8
    // 0xbb8440: LoadField: r5 = r2->field_b
    //     0xbb8440: ldur            x5, [x2, #0xb]
    // 0xbb8444: r0 = BoxInt64Instr(r5)
    //     0xbb8444: sbfiz           x0, x5, #1, #0x1f
    //     0xbb8448: cmp             x5, x0, asr #1
    //     0xbb844c: b.eq            #0xbb8458
    //     0xbb8450: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb8454: stur            x5, [x0, #7]
    // 0xbb8458: StoreField: r4->field_23 = r0
    //     0xbb8458: stur            w0, [x4, #0x23]
    //     0xbb845c: tbz             w0, #0, #0xbb8478
    //     0xbb8460: ldurb           w16, [x4, #-1]
    //     0xbb8464: ldurb           w17, [x0, #-1]
    //     0xbb8468: and             x16, x17, x16, lsr #2
    //     0xbb846c: tst             x16, HEAP, lsr #32
    //     0xbb8470: b.eq            #0xbb8478
    //     0xbb8474: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xbb8478: LoadField: r0 = r2->field_13
    //     0xbb8478: ldur            w0, [x2, #0x13]
    // 0xbb847c: DecompressPointer r0
    //     0xbb847c: add             x0, x0, HEAP, lsl #32
    // 0xbb8480: StoreField: r4->field_27 = r0
    //     0xbb8480: stur            w0, [x4, #0x27]
    //     0xbb8484: ldurb           w16, [x4, #-1]
    //     0xbb8488: ldurb           w17, [x0, #-1]
    //     0xbb848c: and             x16, x17, x16, lsr #2
    //     0xbb8490: tst             x16, HEAP, lsr #32
    //     0xbb8494: b.eq            #0xbb849c
    //     0xbb8498: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xbb849c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xbb849c: ldur            w0, [x2, #0x17]
    // 0xbb84a0: DecompressPointer r0
    //     0xbb84a0: add             x0, x0, HEAP, lsl #32
    // 0xbb84a4: StoreField: r4->field_37 = r0
    //     0xbb84a4: stur            w0, [x4, #0x37]
    //     0xbb84a8: ldurb           w16, [x4, #-1]
    //     0xbb84ac: ldurb           w17, [x0, #-1]
    //     0xbb84b0: and             x16, x17, x16, lsr #2
    //     0xbb84b4: tst             x16, HEAP, lsr #32
    //     0xbb84b8: b.eq            #0xbb84c0
    //     0xbb84bc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xbb84c0: ldur            x0, [fp, #-0x10]
    // 0xbb84c4: StoreField: r4->field_2b = r0
    //     0xbb84c4: stur            w0, [x4, #0x2b]
    //     0xbb84c8: ldurb           w16, [x4, #-1]
    //     0xbb84cc: ldurb           w17, [x0, #-1]
    //     0xbb84d0: and             x16, x17, x16, lsr #2
    //     0xbb84d4: tst             x16, HEAP, lsr #32
    //     0xbb84d8: b.eq            #0xbb84e0
    //     0xbb84dc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xbb84e0: ldur            x1, [fp, #-0x20]
    // 0xbb84e4: ldur            x2, [fp, #-0x10]
    // 0xbb84e8: r0 = globalToLocal()
    //     0xbb84e8: bl              #0x6a9a0c  ; [package:flutter/src/rendering/box.dart] RenderBox::globalToLocal
    // 0xbb84ec: ldur            x2, [fp, #-8]
    // 0xbb84f0: StoreField: r2->field_2f = r0
    //     0xbb84f0: stur            w0, [x2, #0x2f]
    //     0xbb84f4: ldurb           w16, [x2, #-1]
    //     0xbb84f8: ldurb           w17, [x0, #-1]
    //     0xbb84fc: and             x16, x17, x16, lsr #2
    //     0xbb8500: tst             x16, HEAP, lsr #32
    //     0xbb8504: b.eq            #0xbb850c
    //     0xbb8508: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbb850c: ldur            x0, [fp, #-0x18]
    // 0xbb8510: LoadField: r1 = r0->field_f
    //     0xbb8510: ldur            w1, [x0, #0xf]
    // 0xbb8514: DecompressPointer r1
    //     0xbb8514: add             x1, x1, HEAP, lsl #32
    // 0xbb8518: cmp             w1, NULL
    // 0xbb851c: b.eq            #0xbb85dc
    // 0xbb8520: r0 = renderObject()
    //     0xbb8520: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xbb8524: r1 = LoadClassIdInstr(r0)
    //     0xbb8524: ldur            x1, [x0, #-1]
    //     0xbb8528: ubfx            x1, x1, #0xc, #0x14
    // 0xbb852c: sub             x16, x1, #0xbba
    // 0xbb8530: cmp             x16, #0x9a
    // 0xbb8534: b.hi            #0xbb8544
    // 0xbb8538: mov             x1, x0
    // 0xbb853c: r0 = size()
    //     0xbb853c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xbb8540: b               #0xbb8548
    // 0xbb8544: r0 = Null
    //     0xbb8544: mov             x0, NULL
    // 0xbb8548: ldur            x2, [fp, #-8]
    // 0xbb854c: ldur            x1, [fp, #-0x18]
    // 0xbb8550: cmp             w0, NULL
    // 0xbb8554: b.eq            #0xbb85e0
    // 0xbb8558: StoreField: r2->field_33 = r0
    //     0xbb8558: stur            w0, [x2, #0x33]
    //     0xbb855c: ldurb           w16, [x2, #-1]
    //     0xbb8560: ldurb           w17, [x0, #-1]
    //     0xbb8564: and             x16, x17, x16, lsr #2
    //     0xbb8568: tst             x16, HEAP, lsr #32
    //     0xbb856c: b.eq            #0xbb8574
    //     0xbb8570: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbb8574: LoadField: r0 = r1->field_f
    //     0xbb8574: ldur            w0, [x1, #0xf]
    // 0xbb8578: DecompressPointer r0
    //     0xbb8578: add             x0, x0, HEAP, lsl #32
    // 0xbb857c: cmp             w0, NULL
    // 0xbb8580: b.eq            #0xbb85e4
    // 0xbb8584: mov             x1, x0
    // 0xbb8588: r0 = of()
    //     0xbb8588: bl              #0x9a7dc8  ; [package:flutter/src/widgets/scrollable.dart] Scrollable::of
    // 0xbb858c: ldur            x1, [fp, #-8]
    // 0xbb8590: StoreField: r1->field_3b = r0
    //     0xbb8590: stur            w0, [x1, #0x3b]
    //     0xbb8594: ldurb           w16, [x1, #-1]
    //     0xbb8598: ldurb           w17, [x0, #-1]
    //     0xbb859c: and             x16, x17, x16, lsr #2
    //     0xbb85a0: tst             x16, HEAP, lsr #32
    //     0xbb85a4: b.eq            #0xbb85ac
    //     0xbb85a8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbb85ac: r0 = Null
    //     0xbb85ac: mov             x0, NULL
    // 0xbb85b0: LeaveFrame
    //     0xbb85b0: mov             SP, fp
    //     0xbb85b4: ldp             fp, lr, [SP], #0x10
    // 0xbb85b8: ret
    //     0xbb85b8: ret             
    // 0xbb85bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb85bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb85c0: b               #0xbb82c8
    // 0xbb85c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb85c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb85c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb85c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb85cc: r9 = _listState
    //     0xbb85cc: add             x9, PP, #0x51, lsl #12  ; [pp+0x514a0] Field <_ReorderableItemState@2201143295._listState@2201143295>: late (offset: 0x14)
    //     0xbb85d0: ldr             x9, [x9, #0x4a0]
    // 0xbb85d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb85d4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb85d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb85d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb85dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb85dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb85e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb85e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb85e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb85e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget createProxy(dynamic, BuildContext) {
    // ** addr: 0xbb85f4, size: 0x3c
    // 0xbb85f4: EnterFrame
    //     0xbb85f4: stp             fp, lr, [SP, #-0x10]!
    //     0xbb85f8: mov             fp, SP
    // 0xbb85fc: ldr             x0, [fp, #0x18]
    // 0xbb8600: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb8600: ldur            w1, [x0, #0x17]
    // 0xbb8604: DecompressPointer r1
    //     0xbb8604: add             x1, x1, HEAP, lsl #32
    // 0xbb8608: CheckStackOverflow
    //     0xbb8608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb860c: cmp             SP, x16
    //     0xbb8610: b.ls            #0xbb8628
    // 0xbb8614: ldr             x2, [fp, #0x10]
    // 0xbb8618: r0 = createProxy()
    //     0xbb8618: bl              #0xbb8630  ; [package:reorderable_grid/src/reorderable_grid.dart] _DragInfo::createProxy
    // 0xbb861c: LeaveFrame
    //     0xbb861c: mov             SP, fp
    //     0xbb8620: ldp             fp, lr, [SP], #0x10
    // 0xbb8624: ret
    //     0xbb8624: ret             
    // 0xbb8628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8628: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb862c: b               #0xbb8614
  }
  _ createProxy(/* No info */) {
    // ** addr: 0xbb8630, size: 0x1d4
    // 0xbb8630: EnterFrame
    //     0xbb8630: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8634: mov             fp, SP
    // 0xbb8638: AllocStack(0x48)
    //     0xbb8638: sub             SP, SP, #0x48
    // 0xbb863c: SetupParameters(_DragInfo this /* r1 => r3, fp-0x30 */, dynamic _ /* r2 => r0, fp-0x38 */)
    //     0xbb863c: mov             x3, x1
    //     0xbb8640: mov             x0, x2
    //     0xbb8644: stur            x1, [fp, #-0x30]
    //     0xbb8648: stur            x2, [fp, #-0x38]
    // 0xbb864c: CheckStackOverflow
    //     0xbb864c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8650: cmp             SP, x16
    //     0xbb8654: b.ls            #0xbb87a4
    // 0xbb8658: LoadField: r4 = r3->field_37
    //     0xbb8658: ldur            w4, [x3, #0x37]
    // 0xbb865c: DecompressPointer r4
    //     0xbb865c: add             x4, x4, HEAP, lsl #32
    // 0xbb8660: r16 = Sentinel
    //     0xbb8660: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb8664: cmp             w4, w16
    // 0xbb8668: b.eq            #0xbb87ac
    // 0xbb866c: stur            x4, [fp, #-0x28]
    // 0xbb8670: LoadField: r5 = r3->field_1f
    //     0xbb8670: ldur            w5, [x3, #0x1f]
    // 0xbb8674: DecompressPointer r5
    //     0xbb8674: add             x5, x5, HEAP, lsl #32
    // 0xbb8678: r16 = Sentinel
    //     0xbb8678: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb867c: cmp             w5, w16
    // 0xbb8680: b.eq            #0xbb87b8
    // 0xbb8684: stur            x5, [fp, #-0x20]
    // 0xbb8688: LoadField: r6 = r3->field_23
    //     0xbb8688: ldur            w6, [x3, #0x23]
    // 0xbb868c: DecompressPointer r6
    //     0xbb868c: add             x6, x6, HEAP, lsl #32
    // 0xbb8690: r16 = Sentinel
    //     0xbb8690: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb8694: cmp             w6, w16
    // 0xbb8698: b.eq            #0xbb87c4
    // 0xbb869c: stur            x6, [fp, #-0x18]
    // 0xbb86a0: LoadField: r7 = r3->field_33
    //     0xbb86a0: ldur            w7, [x3, #0x33]
    // 0xbb86a4: DecompressPointer r7
    //     0xbb86a4: add             x7, x7, HEAP, lsl #32
    // 0xbb86a8: r16 = Sentinel
    //     0xbb86a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb86ac: cmp             w7, w16
    // 0xbb86b0: b.eq            #0xbb87d0
    // 0xbb86b4: stur            x7, [fp, #-0x10]
    // 0xbb86b8: LoadField: r8 = r3->field_3f
    //     0xbb86b8: ldur            w8, [x3, #0x3f]
    // 0xbb86bc: DecompressPointer r8
    //     0xbb86bc: add             x8, x8, HEAP, lsl #32
    // 0xbb86c0: stur            x8, [fp, #-8]
    // 0xbb86c4: cmp             w8, NULL
    // 0xbb86c8: b.eq            #0xbb87dc
    // 0xbb86cc: LoadField: r1 = r3->field_2b
    //     0xbb86cc: ldur            w1, [x3, #0x2b]
    // 0xbb86d0: DecompressPointer r1
    //     0xbb86d0: add             x1, x1, HEAP, lsl #32
    // 0xbb86d4: r16 = Sentinel
    //     0xbb86d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb86d8: cmp             w1, w16
    // 0xbb86dc: b.eq            #0xbb87e0
    // 0xbb86e0: LoadField: r2 = r3->field_2f
    //     0xbb86e0: ldur            w2, [x3, #0x2f]
    // 0xbb86e4: DecompressPointer r2
    //     0xbb86e4: add             x2, x2, HEAP, lsl #32
    // 0xbb86e8: r16 = Sentinel
    //     0xbb86e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb86ec: cmp             w2, w16
    // 0xbb86f0: b.eq            #0xbb87ec
    // 0xbb86f4: r0 = -()
    //     0xbb86f4: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xbb86f8: ldur            x1, [fp, #-0x38]
    // 0xbb86fc: stur            x0, [fp, #-0x38]
    // 0xbb8700: r0 = _overlayOrigin()
    //     0xbb8700: bl              #0xbb8810  ; [package:reorderable_grid/src/reorderable_grid.dart] ::_overlayOrigin
    // 0xbb8704: ldur            x1, [fp, #-0x38]
    // 0xbb8708: mov             x2, x0
    // 0xbb870c: r0 = -()
    //     0xbb870c: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xbb8710: mov             x1, x0
    // 0xbb8714: ldur            x0, [fp, #-0x30]
    // 0xbb8718: stur            x1, [fp, #-0x48]
    // 0xbb871c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbb871c: ldur            w2, [x0, #0x17]
    // 0xbb8720: DecompressPointer r2
    //     0xbb8720: add             x2, x2, HEAP, lsl #32
    // 0xbb8724: stur            x2, [fp, #-0x40]
    // 0xbb8728: LoadField: r3 = r0->field_27
    //     0xbb8728: ldur            w3, [x0, #0x27]
    // 0xbb872c: DecompressPointer r3
    //     0xbb872c: add             x3, x3, HEAP, lsl #32
    // 0xbb8730: r16 = Sentinel
    //     0xbb8730: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb8734: cmp             w3, w16
    // 0xbb8738: b.eq            #0xbb87f8
    // 0xbb873c: stur            x3, [fp, #-0x38]
    // 0xbb8740: r0 = _DragItemProxy()
    //     0xbb8740: bl              #0xbb8804  ; Allocate_DragItemProxyStub -> _DragItemProxy (size=0x2c)
    // 0xbb8744: mov             x1, x0
    // 0xbb8748: ldur            x0, [fp, #-0x20]
    // 0xbb874c: StoreField: r1->field_b = r0
    //     0xbb874c: stur            w0, [x1, #0xb]
    // 0xbb8750: ldur            x0, [fp, #-0x18]
    // 0xbb8754: r2 = LoadInt32Instr(r0)
    //     0xbb8754: sbfx            x2, x0, #1, #0x1f
    //     0xbb8758: tbz             w0, #0, #0xbb8760
    //     0xbb875c: ldur            x2, [x0, #7]
    // 0xbb8760: StoreField: r1->field_f = r2
    //     0xbb8760: stur            x2, [x1, #0xf]
    // 0xbb8764: ldur            x0, [fp, #-0x38]
    // 0xbb8768: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb8768: stur            w0, [x1, #0x17]
    // 0xbb876c: ldur            x0, [fp, #-0x48]
    // 0xbb8770: StoreField: r1->field_1b = r0
    //     0xbb8770: stur            w0, [x1, #0x1b]
    // 0xbb8774: ldur            x0, [fp, #-0x10]
    // 0xbb8778: StoreField: r1->field_1f = r0
    //     0xbb8778: stur            w0, [x1, #0x1f]
    // 0xbb877c: ldur            x0, [fp, #-8]
    // 0xbb8780: StoreField: r1->field_23 = r0
    //     0xbb8780: stur            w0, [x1, #0x23]
    // 0xbb8784: ldur            x0, [fp, #-0x40]
    // 0xbb8788: StoreField: r1->field_27 = r0
    //     0xbb8788: stur            w0, [x1, #0x27]
    // 0xbb878c: mov             x2, x1
    // 0xbb8790: ldur            x1, [fp, #-0x28]
    // 0xbb8794: r0 = wrap()
    //     0xbb8794: bl              #0x6a4dec  ; [package:flutter/src/widgets/inherited_theme.dart] CapturedThemes::wrap
    // 0xbb8798: LeaveFrame
    //     0xbb8798: mov             SP, fp
    //     0xbb879c: ldp             fp, lr, [SP], #0x10
    // 0xbb87a0: ret
    //     0xbb87a0: ret             
    // 0xbb87a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb87a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb87a8: b               #0xbb8658
    // 0xbb87ac: r9 = capturedThemes
    //     0xbb87ac: add             x9, PP, #0x51, lsl #12  ; [pp+0x513e0] Field <<EMAIL>>: late (offset: 0x38)
    //     0xbb87b0: ldr             x9, [x9, #0x3e0]
    // 0xbb87b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb87b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb87b8: r9 = listState
    //     0xbb87b8: add             x9, PP, #0x51, lsl #12  ; [pp+0x513e8] Field <<EMAIL>>: late (offset: 0x20)
    //     0xbb87bc: ldr             x9, [x9, #0x3e8]
    // 0xbb87c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb87c0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb87c4: r9 = index
    //     0xbb87c4: add             x9, PP, #0x51, lsl #12  ; [pp+0x513f0] Field <<EMAIL>>: late (offset: 0x24)
    //     0xbb87c8: ldr             x9, [x9, #0x3f0]
    // 0xbb87cc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb87cc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb87d0: r9 = itemSize
    //     0xbb87d0: add             x9, PP, #0x51, lsl #12  ; [pp+0x513f8] Field <<EMAIL>>: late (offset: 0x34)
    //     0xbb87d4: ldr             x9, [x9, #0x3f8]
    // 0xbb87d8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb87d8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb87dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb87dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb87e0: r9 = dragPosition
    //     0xbb87e0: add             x9, PP, #0x51, lsl #12  ; [pp+0x51400] Field <<EMAIL>>: late (offset: 0x2c)
    //     0xbb87e4: ldr             x9, [x9, #0x400]
    // 0xbb87e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb87e8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb87ec: r9 = dragOffset
    //     0xbb87ec: add             x9, PP, #0x51, lsl #12  ; [pp+0x51408] Field <<EMAIL>>: late (offset: 0x30)
    //     0xbb87f0: ldr             x9, [x9, #0x408]
    // 0xbb87f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb87f4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb87f8: r9 = child
    //     0xbb87f8: add             x9, PP, #0x51, lsl #12  ; [pp+0x51410] Field <<EMAIL>>: late (offset: 0x28)
    //     0xbb87fc: ldr             x9, [x9, #0x410]
    // 0xbb8800: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb8800: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ cancel(/* No info */) {
    // ** addr: 0xd23378, size: 0x78
    // 0xd23378: EnterFrame
    //     0xd23378: stp             fp, lr, [SP, #-0x10]!
    //     0xd2337c: mov             fp, SP
    // 0xd23380: AllocStack(0x8)
    //     0xd23380: sub             SP, SP, #8
    // 0xd23384: SetupParameters(_DragInfo this /* r1 => r0, fp-0x8 */)
    //     0xd23384: mov             x0, x1
    //     0xd23388: stur            x1, [fp, #-8]
    // 0xd2338c: CheckStackOverflow
    //     0xd2338c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd23390: cmp             SP, x16
    //     0xd23394: b.ls            #0xd233e4
    // 0xd23398: LoadField: r1 = r0->field_3f
    //     0xd23398: ldur            w1, [x0, #0x3f]
    // 0xd2339c: DecompressPointer r1
    //     0xd2339c: add             x1, x1, HEAP, lsl #32
    // 0xd233a0: cmp             w1, NULL
    // 0xd233a4: b.eq            #0xd233b0
    // 0xd233a8: r0 = dispose()
    //     0xd233a8: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xd233ac: ldur            x0, [fp, #-8]
    // 0xd233b0: StoreField: r0->field_3f = rNULL
    //     0xd233b0: stur            NULL, [x0, #0x3f]
    // 0xd233b4: LoadField: r1 = r0->field_f
    //     0xd233b4: ldur            w1, [x0, #0xf]
    // 0xd233b8: DecompressPointer r1
    //     0xd233b8: add             x1, x1, HEAP, lsl #32
    // 0xd233bc: cmp             w1, NULL
    // 0xd233c0: b.eq            #0xd233ec
    // 0xd233c4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd233c4: ldur            w0, [x1, #0x17]
    // 0xd233c8: DecompressPointer r0
    //     0xd233c8: add             x0, x0, HEAP, lsl #32
    // 0xd233cc: mov             x1, x0
    // 0xd233d0: r0 = _dragReset()
    //     0xd233d0: bl              #0x9a12ec  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragReset
    // 0xd233d4: r0 = Null
    //     0xd233d4: mov             x0, NULL
    // 0xd233d8: LeaveFrame
    //     0xd233d8: mov             SP, fp
    //     0xd233dc: ldp             fp, lr, [SP], #0x10
    // 0xd233e0: ret
    //     0xd233e0: ret             
    // 0xd233e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd233e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd233e8: b               #0xd23398
    // 0xd233ec: r0 = NullErrorSharedWithoutFPURegs()
    //     0xd233ec: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ end(/* No info */) {
    // ** addr: 0xd2e070, size: 0x78
    // 0xd2e070: EnterFrame
    //     0xd2e070: stp             fp, lr, [SP, #-0x10]!
    //     0xd2e074: mov             fp, SP
    // 0xd2e078: AllocStack(0x8)
    //     0xd2e078: sub             SP, SP, #8
    // 0xd2e07c: SetupParameters(_DragInfo this /* r1 => r0, fp-0x8 */)
    //     0xd2e07c: mov             x0, x1
    //     0xd2e080: stur            x1, [fp, #-8]
    // 0xd2e084: CheckStackOverflow
    //     0xd2e084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2e088: cmp             SP, x16
    //     0xd2e08c: b.ls            #0xd2e0d8
    // 0xd2e090: LoadField: r1 = r0->field_3f
    //     0xd2e090: ldur            w1, [x0, #0x3f]
    // 0xd2e094: DecompressPointer r1
    //     0xd2e094: add             x1, x1, HEAP, lsl #32
    // 0xd2e098: cmp             w1, NULL
    // 0xd2e09c: b.eq            #0xd2e0e0
    // 0xd2e0a0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xd2e0a0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xd2e0a4: r0 = reverse()
    //     0xd2e0a4: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0xd2e0a8: ldur            x2, [fp, #-8]
    // 0xd2e0ac: LoadField: r0 = r2->field_b
    //     0xd2e0ac: ldur            w0, [x2, #0xb]
    // 0xd2e0b0: DecompressPointer r0
    //     0xd2e0b0: add             x0, x0, HEAP, lsl #32
    // 0xd2e0b4: cmp             w0, NULL
    // 0xd2e0b8: b.eq            #0xd2e0e4
    // 0xd2e0bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd2e0bc: ldur            w1, [x0, #0x17]
    // 0xd2e0c0: DecompressPointer r1
    //     0xd2e0c0: add             x1, x1, HEAP, lsl #32
    // 0xd2e0c4: r0 = _dragEnd()
    //     0xd2e0c4: bl              #0xbb8924  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragEnd
    // 0xd2e0c8: r0 = Null
    //     0xd2e0c8: mov             x0, NULL
    // 0xd2e0cc: LeaveFrame
    //     0xd2e0cc: mov             SP, fp
    //     0xd2e0d0: ldp             fp, lr, [SP], #0x10
    // 0xd2e0d4: ret
    //     0xd2e0d4: ret             
    // 0xd2e0d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2e0d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd2e0dc: b               #0xd2e090
    // 0xd2e0e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd2e0e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd2e0e4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xd2e0e4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ update(/* No info */) {
    // ** addr: 0xd8d7ec, size: 0xc0
    // 0xd8d7ec: EnterFrame
    //     0xd8d7ec: stp             fp, lr, [SP, #-0x10]!
    //     0xd8d7f0: mov             fp, SP
    // 0xd8d7f4: AllocStack(0x10)
    //     0xd8d7f4: sub             SP, SP, #0x10
    // 0xd8d7f8: SetupParameters(_DragInfo this /* r1 => r0, fp-0x10 */)
    //     0xd8d7f8: mov             x0, x1
    //     0xd8d7fc: stur            x1, [fp, #-0x10]
    // 0xd8d800: CheckStackOverflow
    //     0xd8d800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8d804: cmp             SP, x16
    //     0xd8d808: b.ls            #0xd8d894
    // 0xd8d80c: LoadField: r1 = r0->field_2b
    //     0xd8d80c: ldur            w1, [x0, #0x2b]
    // 0xd8d810: DecompressPointer r1
    //     0xd8d810: add             x1, x1, HEAP, lsl #32
    // 0xd8d814: r16 = Sentinel
    //     0xd8d814: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd8d818: cmp             w1, w16
    // 0xd8d81c: b.eq            #0xd8d89c
    // 0xd8d820: LoadField: r3 = r2->field_b
    //     0xd8d820: ldur            w3, [x2, #0xb]
    // 0xd8d824: DecompressPointer r3
    //     0xd8d824: add             x3, x3, HEAP, lsl #32
    // 0xd8d828: mov             x2, x3
    // 0xd8d82c: stur            x3, [fp, #-8]
    // 0xd8d830: r0 = +()
    //     0xd8d830: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0xd8d834: mov             x1, x0
    // 0xd8d838: ldur            x2, [fp, #-0x10]
    // 0xd8d83c: StoreField: r2->field_2b = r0
    //     0xd8d83c: stur            w0, [x2, #0x2b]
    //     0xd8d840: ldurb           w16, [x2, #-1]
    //     0xd8d844: ldurb           w17, [x0, #-1]
    //     0xd8d848: and             x16, x17, x16, lsr #2
    //     0xd8d84c: tst             x16, HEAP, lsr #32
    //     0xd8d850: b.eq            #0xd8d858
    //     0xd8d854: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xd8d858: LoadField: r0 = r2->field_7
    //     0xd8d858: ldur            w0, [x2, #7]
    // 0xd8d85c: DecompressPointer r0
    //     0xd8d85c: add             x0, x0, HEAP, lsl #32
    // 0xd8d860: cmp             w0, NULL
    // 0xd8d864: b.eq            #0xd8d8a8
    // 0xd8d868: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xd8d868: ldur            w3, [x0, #0x17]
    // 0xd8d86c: DecompressPointer r3
    //     0xd8d86c: add             x3, x3, HEAP, lsl #32
    // 0xd8d870: mov             x16, x1
    // 0xd8d874: mov             x1, x3
    // 0xd8d878: mov             x3, x16
    // 0xd8d87c: ldur            x5, [fp, #-8]
    // 0xd8d880: r0 = _dragUpdate()
    //     0xd8d880: bl              #0xbb8a9c  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragUpdate
    // 0xd8d884: r0 = Null
    //     0xd8d884: mov             x0, NULL
    // 0xd8d888: LeaveFrame
    //     0xd8d888: mov             SP, fp
    //     0xd8d88c: ldp             fp, lr, [SP], #0x10
    // 0xd8d890: ret
    //     0xd8d890: ret             
    // 0xd8d894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8d894: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8d898: b               #0xd8d80c
    // 0xd8d89c: r9 = dragPosition
    //     0xd8d89c: add             x9, PP, #0x51, lsl #12  ; [pp+0x51400] Field <<EMAIL>>: late (offset: 0x2c)
    //     0xd8d8a0: ldr             x9, [x9, #0x400]
    // 0xd8d8a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd8d8a4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd8d8a8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xd8d8a8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}

// class id: 3575, size: 0x20, field offset: 0x10
//   const constructor, 
class _ReorderableItemGlobalKey extends GlobalObjectKey<dynamic> {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbeb404, size: 0x84
    // 0xbeb404: EnterFrame
    //     0xbeb404: stp             fp, lr, [SP, #-0x10]!
    //     0xbeb408: mov             fp, SP
    // 0xbeb40c: AllocStack(0x8)
    //     0xbeb40c: sub             SP, SP, #8
    // 0xbeb410: CheckStackOverflow
    //     0xbeb410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeb414: cmp             SP, x16
    //     0xbeb418: b.ls            #0xbeb480
    // 0xbeb41c: ldr             x0, [fp, #0x10]
    // 0xbeb420: LoadField: r2 = r0->field_f
    //     0xbeb420: ldur            w2, [x0, #0xf]
    // 0xbeb424: DecompressPointer r2
    //     0xbeb424: add             x2, x2, HEAP, lsl #32
    // 0xbeb428: LoadField: r3 = r0->field_13
    //     0xbeb428: ldur            x3, [x0, #0x13]
    // 0xbeb42c: LoadField: r4 = r0->field_1b
    //     0xbeb42c: ldur            w4, [x0, #0x1b]
    // 0xbeb430: DecompressPointer r4
    //     0xbeb430: add             x4, x4, HEAP, lsl #32
    // 0xbeb434: r0 = BoxInt64Instr(r3)
    //     0xbeb434: sbfiz           x0, x3, #1, #0x1f
    //     0xbeb438: cmp             x3, x0, asr #1
    //     0xbeb43c: b.eq            #0xbeb448
    //     0xbeb440: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbeb444: stur            x3, [x0, #7]
    // 0xbeb448: str             x4, [SP]
    // 0xbeb44c: mov             x1, x2
    // 0xbeb450: mov             x2, x0
    // 0xbeb454: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbeb454: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbeb458: r0 = hash()
    //     0xbeb458: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbeb45c: mov             x2, x0
    // 0xbeb460: r0 = BoxInt64Instr(r2)
    //     0xbeb460: sbfiz           x0, x2, #1, #0x1f
    //     0xbeb464: cmp             x2, x0, asr #1
    //     0xbeb468: b.eq            #0xbeb474
    //     0xbeb46c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbeb470: stur            x2, [x0, #7]
    // 0xbeb474: LeaveFrame
    //     0xbeb474: mov             SP, fp
    //     0xbeb478: ldp             fp, lr, [SP], #0x10
    // 0xbeb47c: ret
    //     0xbeb47c: ret             
    // 0xbeb480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeb480: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeb484: b               #0xbeb41c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd5c060, size: 0x11c
    // 0xd5c060: EnterFrame
    //     0xd5c060: stp             fp, lr, [SP, #-0x10]!
    //     0xd5c064: mov             fp, SP
    // 0xd5c068: AllocStack(0x10)
    //     0xd5c068: sub             SP, SP, #0x10
    // 0xd5c06c: CheckStackOverflow
    //     0xd5c06c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd5c070: cmp             SP, x16
    //     0xd5c074: b.ls            #0xd5c174
    // 0xd5c078: ldr             x0, [fp, #0x10]
    // 0xd5c07c: cmp             w0, NULL
    // 0xd5c080: b.ne            #0xd5c094
    // 0xd5c084: r0 = false
    //     0xd5c084: add             x0, NULL, #0x30  ; false
    // 0xd5c088: LeaveFrame
    //     0xd5c088: mov             SP, fp
    //     0xd5c08c: ldp             fp, lr, [SP], #0x10
    // 0xd5c090: ret
    //     0xd5c090: ret             
    // 0xd5c094: str             x0, [SP]
    // 0xd5c098: r0 = runtimeType()
    //     0xd5c098: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd5c09c: r1 = LoadClassIdInstr(r0)
    //     0xd5c09c: ldur            x1, [x0, #-1]
    //     0xd5c0a0: ubfx            x1, x1, #0xc, #0x14
    // 0xd5c0a4: r16 = _ReorderableItemGlobalKey
    //     0xd5c0a4: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b180] Type: _ReorderableItemGlobalKey
    //     0xd5c0a8: ldr             x16, [x16, #0x180]
    // 0xd5c0ac: stp             x16, x0, [SP]
    // 0xd5c0b0: mov             x0, x1
    // 0xd5c0b4: mov             lr, x0
    // 0xd5c0b8: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c0bc: blr             lr
    // 0xd5c0c0: tbz             w0, #4, #0xd5c0d4
    // 0xd5c0c4: r0 = false
    //     0xd5c0c4: add             x0, NULL, #0x30  ; false
    // 0xd5c0c8: LeaveFrame
    //     0xd5c0c8: mov             SP, fp
    //     0xd5c0cc: ldp             fp, lr, [SP], #0x10
    // 0xd5c0d0: ret
    //     0xd5c0d0: ret             
    // 0xd5c0d4: ldr             x1, [fp, #0x10]
    // 0xd5c0d8: r0 = 60
    //     0xd5c0d8: movz            x0, #0x3c
    // 0xd5c0dc: branchIfSmi(r1, 0xd5c0e8)
    //     0xd5c0dc: tbz             w1, #0, #0xd5c0e8
    // 0xd5c0e0: r0 = LoadClassIdInstr(r1)
    //     0xd5c0e0: ldur            x0, [x1, #-1]
    //     0xd5c0e4: ubfx            x0, x0, #0xc, #0x14
    // 0xd5c0e8: cmp             x0, #0xdf7
    // 0xd5c0ec: b.ne            #0xd5c164
    // 0xd5c0f0: ldr             x2, [fp, #0x18]
    // 0xd5c0f4: LoadField: r0 = r1->field_f
    //     0xd5c0f4: ldur            w0, [x1, #0xf]
    // 0xd5c0f8: DecompressPointer r0
    //     0xd5c0f8: add             x0, x0, HEAP, lsl #32
    // 0xd5c0fc: LoadField: r3 = r2->field_f
    //     0xd5c0fc: ldur            w3, [x2, #0xf]
    // 0xd5c100: DecompressPointer r3
    //     0xd5c100: add             x3, x3, HEAP, lsl #32
    // 0xd5c104: r4 = LoadClassIdInstr(r0)
    //     0xd5c104: ldur            x4, [x0, #-1]
    //     0xd5c108: ubfx            x4, x4, #0xc, #0x14
    // 0xd5c10c: stp             x3, x0, [SP]
    // 0xd5c110: mov             x0, x4
    // 0xd5c114: mov             lr, x0
    // 0xd5c118: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c11c: blr             lr
    // 0xd5c120: tbnz            w0, #4, #0xd5c164
    // 0xd5c124: ldr             x2, [fp, #0x18]
    // 0xd5c128: ldr             x1, [fp, #0x10]
    // 0xd5c12c: LoadField: r3 = r1->field_13
    //     0xd5c12c: ldur            x3, [x1, #0x13]
    // 0xd5c130: LoadField: r4 = r2->field_13
    //     0xd5c130: ldur            x4, [x2, #0x13]
    // 0xd5c134: cmp             x3, x4
    // 0xd5c138: b.ne            #0xd5c164
    // 0xd5c13c: LoadField: r3 = r1->field_1b
    //     0xd5c13c: ldur            w3, [x1, #0x1b]
    // 0xd5c140: DecompressPointer r3
    //     0xd5c140: add             x3, x3, HEAP, lsl #32
    // 0xd5c144: LoadField: r1 = r2->field_1b
    //     0xd5c144: ldur            w1, [x2, #0x1b]
    // 0xd5c148: DecompressPointer r1
    //     0xd5c148: add             x1, x1, HEAP, lsl #32
    // 0xd5c14c: cmp             w3, w1
    // 0xd5c150: r16 = true
    //     0xd5c150: add             x16, NULL, #0x20  ; true
    // 0xd5c154: r17 = false
    //     0xd5c154: add             x17, NULL, #0x30  ; false
    // 0xd5c158: csel            x2, x16, x17, eq
    // 0xd5c15c: mov             x0, x2
    // 0xd5c160: b               #0xd5c168
    // 0xd5c164: r0 = false
    //     0xd5c164: add             x0, NULL, #0x30  ; false
    // 0xd5c168: LeaveFrame
    //     0xd5c168: mov             SP, fp
    //     0xd5c16c: ldp             fp, lr, [SP], #0x10
    // 0xd5c170: ret
    //     0xd5c170: ret             
    // 0xd5c174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd5c174: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd5c178: b               #0xd5c078
  }
}

// class id: 4098, size: 0x28, field offset: 0x14
class _ReorderableItemState extends State<dynamic> {

  late SliverReorderableGridState _listState; // offset: 0x14

  _ deactivate(/* No info */) {
    // ** addr: 0x92bbd0, size: 0x6c
    // 0x92bbd0: EnterFrame
    //     0x92bbd0: stp             fp, lr, [SP, #-0x10]!
    //     0x92bbd4: mov             fp, SP
    // 0x92bbd8: mov             x3, x1
    // 0x92bbdc: CheckStackOverflow
    //     0x92bbdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92bbe0: cmp             SP, x16
    //     0x92bbe4: b.ls            #0x92bc24
    // 0x92bbe8: LoadField: r1 = r3->field_13
    //     0x92bbe8: ldur            w1, [x3, #0x13]
    // 0x92bbec: DecompressPointer r1
    //     0x92bbec: add             x1, x1, HEAP, lsl #32
    // 0x92bbf0: r16 = Sentinel
    //     0x92bbf0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x92bbf4: cmp             w1, w16
    // 0x92bbf8: b.eq            #0x92bc2c
    // 0x92bbfc: LoadField: r0 = r3->field_b
    //     0x92bbfc: ldur            w0, [x3, #0xb]
    // 0x92bc00: DecompressPointer r0
    //     0x92bc00: add             x0, x0, HEAP, lsl #32
    // 0x92bc04: cmp             w0, NULL
    // 0x92bc08: b.eq            #0x92bc38
    // 0x92bc0c: LoadField: r2 = r0->field_b
    //     0x92bc0c: ldur            x2, [x0, #0xb]
    // 0x92bc10: r0 = _unregisterItem()
    //     0x92bc10: bl              #0x92b9a8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_unregisterItem
    // 0x92bc14: r0 = Null
    //     0x92bc14: mov             x0, NULL
    // 0x92bc18: LeaveFrame
    //     0x92bc18: mov             SP, fp
    //     0x92bc1c: ldp             fp, lr, [SP], #0x10
    // 0x92bc20: ret
    //     0x92bc20: ret             
    // 0x92bc24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92bc24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92bc28: b               #0x92bbe8
    // 0x92bc2c: r9 = _listState
    //     0x92bc2c: add             x9, PP, #0x51, lsl #12  ; [pp+0x514a0] Field <_ReorderableItemState@2201143295._listState@2201143295>: late (offset: 0x14)
    //     0x92bc30: ldr             x9, [x9, #0x4a0]
    // 0x92bc34: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x92bc34: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x92bc38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92bc38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x97e748, size: 0x78
    // 0x97e748: EnterFrame
    //     0x97e748: stp             fp, lr, [SP, #-0x10]!
    //     0x97e74c: mov             fp, SP
    // 0x97e750: AllocStack(0x8)
    //     0x97e750: sub             SP, SP, #8
    // 0x97e754: SetupParameters(_ReorderableItemState this /* r1 => r2, fp-0x8 */)
    //     0x97e754: mov             x2, x1
    //     0x97e758: stur            x1, [fp, #-8]
    // 0x97e75c: CheckStackOverflow
    //     0x97e75c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e760: cmp             SP, x16
    //     0x97e764: b.ls            #0x97e7b4
    // 0x97e768: LoadField: r1 = r2->field_f
    //     0x97e768: ldur            w1, [x2, #0xf]
    // 0x97e76c: DecompressPointer r1
    //     0x97e76c: add             x1, x1, HEAP, lsl #32
    // 0x97e770: cmp             w1, NULL
    // 0x97e774: b.eq            #0x97e7bc
    // 0x97e778: r0 = of()
    //     0x97e778: bl              #0x97ea20  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGrid::of
    // 0x97e77c: mov             x1, x0
    // 0x97e780: ldur            x2, [fp, #-8]
    // 0x97e784: StoreField: r2->field_13 = r0
    //     0x97e784: stur            w0, [x2, #0x13]
    //     0x97e788: ldurb           w16, [x2, #-1]
    //     0x97e78c: ldurb           w17, [x0, #-1]
    //     0x97e790: and             x16, x17, x16, lsr #2
    //     0x97e794: tst             x16, HEAP, lsr #32
    //     0x97e798: b.eq            #0x97e7a0
    //     0x97e79c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x97e7a0: r0 = _registerItem()
    //     0x97e7a0: bl              #0x97e7c0  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_registerItem
    // 0x97e7a4: r0 = Null
    //     0x97e7a4: mov             x0, NULL
    // 0x97e7a8: LeaveFrame
    //     0x97e7a8: mov             SP, fp
    //     0x97e7ac: ldp             fp, lr, [SP], #0x10
    // 0x97e7b0: ret
    //     0x97e7b0: ret             
    // 0x97e7b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e7b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e7b8: b               #0x97e768
    // 0x97e7bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97e7bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ rebuild(/* No info */) {
    // ** addr: 0x97e908, size: 0x64
    // 0x97e908: EnterFrame
    //     0x97e908: stp             fp, lr, [SP, #-0x10]!
    //     0x97e90c: mov             fp, SP
    // 0x97e910: AllocStack(0x8)
    //     0x97e910: sub             SP, SP, #8
    // 0x97e914: SetupParameters(_ReorderableItemState this /* r1 => r0, fp-0x8 */)
    //     0x97e914: mov             x0, x1
    //     0x97e918: stur            x1, [fp, #-8]
    // 0x97e91c: CheckStackOverflow
    //     0x97e91c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e920: cmp             SP, x16
    //     0x97e924: b.ls            #0x97e964
    // 0x97e928: LoadField: r1 = r0->field_f
    //     0x97e928: ldur            w1, [x0, #0xf]
    // 0x97e92c: DecompressPointer r1
    //     0x97e92c: add             x1, x1, HEAP, lsl #32
    // 0x97e930: cmp             w1, NULL
    // 0x97e934: b.eq            #0x97e954
    // 0x97e938: r1 = Function '<anonymous closure>':.
    //     0x97e938: add             x1, PP, #0x51, lsl #12  ; [pp+0x514c0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x97e93c: ldr             x1, [x1, #0x4c0]
    // 0x97e940: r2 = Null
    //     0x97e940: mov             x2, NULL
    // 0x97e944: r0 = AllocateClosure()
    //     0x97e944: bl              #0xec1630  ; AllocateClosureStub
    // 0x97e948: ldur            x1, [fp, #-8]
    // 0x97e94c: mov             x2, x0
    // 0x97e950: r0 = setState()
    //     0x97e950: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x97e954: r0 = Null
    //     0x97e954: mov             x0, NULL
    // 0x97e958: LeaveFrame
    //     0x97e958: mov             SP, fp
    //     0x97e95c: ldp             fp, lr, [SP], #0x10
    // 0x97e960: ret
    //     0x97e960: ret             
    // 0x97e964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e964: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e968: b               #0x97e928
  }
  [closure] void rebuild(dynamic) {
    // ** addr: 0x97e96c, size: 0x38
    // 0x97e96c: EnterFrame
    //     0x97e96c: stp             fp, lr, [SP, #-0x10]!
    //     0x97e970: mov             fp, SP
    // 0x97e974: ldr             x0, [fp, #0x10]
    // 0x97e978: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97e978: ldur            w1, [x0, #0x17]
    // 0x97e97c: DecompressPointer r1
    //     0x97e97c: add             x1, x1, HEAP, lsl #32
    // 0x97e980: CheckStackOverflow
    //     0x97e980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e984: cmp             SP, x16
    //     0x97e988: b.ls            #0x97e99c
    // 0x97e98c: r0 = rebuild()
    //     0x97e98c: bl              #0x97e908  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::rebuild
    // 0x97e990: LeaveFrame
    //     0x97e990: mov             SP, fp
    //     0x97e994: ldp             fp, lr, [SP], #0x10
    // 0x97e998: ret
    //     0x97e998: ret             
    // 0x97e99c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e99c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e9a0: b               #0x97e98c
  }
  set _ dragging=(/* No info */) {
    // ** addr: 0x97e9a4, size: 0x7c
    // 0x97e9a4: EnterFrame
    //     0x97e9a4: stp             fp, lr, [SP, #-0x10]!
    //     0x97e9a8: mov             fp, SP
    // 0x97e9ac: AllocStack(0x8)
    //     0x97e9ac: sub             SP, SP, #8
    // 0x97e9b0: SetupParameters(_ReorderableItemState this /* r1 => r1, fp-0x8 */)
    //     0x97e9b0: stur            x1, [fp, #-8]
    // 0x97e9b4: CheckStackOverflow
    //     0x97e9b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e9b8: cmp             SP, x16
    //     0x97e9bc: b.ls            #0x97ea18
    // 0x97e9c0: r1 = 2
    //     0x97e9c0: movz            x1, #0x2
    // 0x97e9c4: r0 = AllocateContext()
    //     0x97e9c4: bl              #0xec126c  ; AllocateContextStub
    // 0x97e9c8: mov             x1, x0
    // 0x97e9cc: ldur            x0, [fp, #-8]
    // 0x97e9d0: StoreField: r1->field_f = r0
    //     0x97e9d0: stur            w0, [x1, #0xf]
    // 0x97e9d4: r2 = true
    //     0x97e9d4: add             x2, NULL, #0x20  ; true
    // 0x97e9d8: StoreField: r1->field_13 = r2
    //     0x97e9d8: stur            w2, [x1, #0x13]
    // 0x97e9dc: LoadField: r2 = r0->field_f
    //     0x97e9dc: ldur            w2, [x0, #0xf]
    // 0x97e9e0: DecompressPointer r2
    //     0x97e9e0: add             x2, x2, HEAP, lsl #32
    // 0x97e9e4: cmp             w2, NULL
    // 0x97e9e8: b.eq            #0x97ea08
    // 0x97e9ec: mov             x2, x1
    // 0x97e9f0: r1 = Function '<anonymous closure>':.
    //     0x97e9f0: add             x1, PP, #0x51, lsl #12  ; [pp+0x514c8] AnonymousClosure: (0x94549c), in [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::dragging= (0x945420)
    //     0x97e9f4: ldr             x1, [x1, #0x4c8]
    // 0x97e9f8: r0 = AllocateClosure()
    //     0x97e9f8: bl              #0xec1630  ; AllocateClosureStub
    // 0x97e9fc: ldur            x1, [fp, #-8]
    // 0x97ea00: mov             x2, x0
    // 0x97ea04: r0 = setState()
    //     0x97ea04: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x97ea08: r0 = Null
    //     0x97ea08: mov             x0, NULL
    // 0x97ea0c: LeaveFrame
    //     0x97ea0c: mov             SP, fp
    //     0x97ea10: ldp             fp, lr, [SP], #0x10
    // 0x97ea14: ret
    //     0x97ea14: ret             
    // 0x97ea18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97ea18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ea1c: b               #0x97e9c0
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a1688, size: 0x11c
    // 0x9a1688: EnterFrame
    //     0x9a1688: stp             fp, lr, [SP, #-0x10]!
    //     0x9a168c: mov             fp, SP
    // 0x9a1690: AllocStack(0x10)
    //     0x9a1690: sub             SP, SP, #0x10
    // 0x9a1694: SetupParameters(_ReorderableItemState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a1694: mov             x4, x1
    //     0x9a1698: mov             x3, x2
    //     0x9a169c: stur            x1, [fp, #-8]
    //     0x9a16a0: stur            x2, [fp, #-0x10]
    // 0x9a16a4: CheckStackOverflow
    //     0x9a16a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a16a8: cmp             SP, x16
    //     0x9a16ac: b.ls            #0x9a178c
    // 0x9a16b0: mov             x0, x3
    // 0x9a16b4: r2 = Null
    //     0x9a16b4: mov             x2, NULL
    // 0x9a16b8: r1 = Null
    //     0x9a16b8: mov             x1, NULL
    // 0x9a16bc: r4 = 60
    //     0x9a16bc: movz            x4, #0x3c
    // 0x9a16c0: branchIfSmi(r0, 0x9a16cc)
    //     0x9a16c0: tbz             w0, #0, #0x9a16cc
    // 0x9a16c4: r4 = LoadClassIdInstr(r0)
    //     0x9a16c4: ldur            x4, [x0, #-1]
    //     0x9a16c8: ubfx            x4, x4, #0xc, #0x14
    // 0x9a16cc: r17 = 4700
    //     0x9a16cc: movz            x17, #0x125c
    // 0x9a16d0: cmp             x4, x17
    // 0x9a16d4: b.eq            #0x9a16ec
    // 0x9a16d8: r8 = _ReorderableItem
    //     0x9a16d8: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d360] Type: _ReorderableItem
    //     0x9a16dc: ldr             x8, [x8, #0x360]
    // 0x9a16e0: r3 = Null
    //     0x9a16e0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d368] Null
    //     0x9a16e4: ldr             x3, [x3, #0x368]
    // 0x9a16e8: r0 = _ReorderableItem()
    //     0x9a16e8: bl              #0x92bc60  ; IsType__ReorderableItem_Stub
    // 0x9a16ec: ldur            x3, [fp, #-8]
    // 0x9a16f0: LoadField: r2 = r3->field_7
    //     0x9a16f0: ldur            w2, [x3, #7]
    // 0x9a16f4: DecompressPointer r2
    //     0x9a16f4: add             x2, x2, HEAP, lsl #32
    // 0x9a16f8: ldur            x0, [fp, #-0x10]
    // 0x9a16fc: r1 = Null
    //     0x9a16fc: mov             x1, NULL
    // 0x9a1700: cmp             w2, NULL
    // 0x9a1704: b.eq            #0x9a1728
    // 0x9a1708: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a1708: ldur            w4, [x2, #0x17]
    // 0x9a170c: DecompressPointer r4
    //     0x9a170c: add             x4, x4, HEAP, lsl #32
    // 0x9a1710: r8 = X0 bound StatefulWidget
    //     0x9a1710: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a1714: ldr             x8, [x8, #0x7f8]
    // 0x9a1718: LoadField: r9 = r4->field_7
    //     0x9a1718: ldur            x9, [x4, #7]
    // 0x9a171c: r3 = Null
    //     0x9a171c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d378] Null
    //     0x9a1720: ldr             x3, [x3, #0x378]
    // 0x9a1724: blr             x9
    // 0x9a1728: ldur            x0, [fp, #-0x10]
    // 0x9a172c: LoadField: r2 = r0->field_b
    //     0x9a172c: ldur            x2, [x0, #0xb]
    // 0x9a1730: ldur            x0, [fp, #-8]
    // 0x9a1734: LoadField: r1 = r0->field_b
    //     0x9a1734: ldur            w1, [x0, #0xb]
    // 0x9a1738: DecompressPointer r1
    //     0x9a1738: add             x1, x1, HEAP, lsl #32
    // 0x9a173c: cmp             w1, NULL
    // 0x9a1740: b.eq            #0x9a1794
    // 0x9a1744: LoadField: r3 = r1->field_b
    //     0x9a1744: ldur            x3, [x1, #0xb]
    // 0x9a1748: cmp             x2, x3
    // 0x9a174c: b.eq            #0x9a177c
    // 0x9a1750: LoadField: r1 = r0->field_13
    //     0x9a1750: ldur            w1, [x0, #0x13]
    // 0x9a1754: DecompressPointer r1
    //     0x9a1754: add             x1, x1, HEAP, lsl #32
    // 0x9a1758: r16 = Sentinel
    //     0x9a1758: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a175c: cmp             w1, w16
    // 0x9a1760: b.eq            #0x9a1798
    // 0x9a1764: mov             x3, x0
    // 0x9a1768: r0 = _unregisterItem()
    //     0x9a1768: bl              #0x92b9a8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_unregisterItem
    // 0x9a176c: ldur            x2, [fp, #-8]
    // 0x9a1770: LoadField: r1 = r2->field_13
    //     0x9a1770: ldur            w1, [x2, #0x13]
    // 0x9a1774: DecompressPointer r1
    //     0x9a1774: add             x1, x1, HEAP, lsl #32
    // 0x9a1778: r0 = _registerItem()
    //     0x9a1778: bl              #0x97e7c0  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_registerItem
    // 0x9a177c: r0 = Null
    //     0x9a177c: mov             x0, NULL
    // 0x9a1780: LeaveFrame
    //     0x9a1780: mov             SP, fp
    //     0x9a1784: ldp             fp, lr, [SP], #0x10
    // 0x9a1788: ret
    //     0x9a1788: ret             
    // 0x9a178c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a178c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1790: b               #0x9a16b0
    // 0x9a1794: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1794: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a1798: r9 = _listState
    //     0x9a1798: add             x9, PP, #0x51, lsl #12  ; [pp+0x514a0] Field <_ReorderableItemState@2201143295._listState@2201143295>: late (offset: 0x14)
    //     0x9a179c: ldr             x9, [x9, #0x4a0]
    // 0x9a17a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a17a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa4af44, size: 0xe8
    // 0xa4af44: EnterFrame
    //     0xa4af44: stp             fp, lr, [SP, #-0x10]!
    //     0xa4af48: mov             fp, SP
    // 0xa4af4c: AllocStack(0x18)
    //     0xa4af4c: sub             SP, SP, #0x18
    // 0xa4af50: SetupParameters(_ReorderableItemState this /* r1 => r0, fp-0x8 */)
    //     0xa4af50: mov             x0, x1
    //     0xa4af54: stur            x1, [fp, #-8]
    // 0xa4af58: CheckStackOverflow
    //     0xa4af58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4af5c: cmp             SP, x16
    //     0xa4af60: b.ls            #0xa4b014
    // 0xa4af64: LoadField: r1 = r0->field_23
    //     0xa4af64: ldur            w1, [x0, #0x23]
    // 0xa4af68: DecompressPointer r1
    //     0xa4af68: add             x1, x1, HEAP, lsl #32
    // 0xa4af6c: tbnz            w1, #4, #0xa4af80
    // 0xa4af70: r0 = Instance_SizedBox
    //     0xa4af70: ldr             x0, [PP, #0x4c90]  ; [pp+0x4c90] Obj!SizedBox@e1df81
    // 0xa4af74: LeaveFrame
    //     0xa4af74: mov             SP, fp
    //     0xa4af78: ldp             fp, lr, [SP], #0x10
    // 0xa4af7c: ret
    //     0xa4af7c: ret             
    // 0xa4af80: LoadField: r1 = r0->field_13
    //     0xa4af80: ldur            w1, [x0, #0x13]
    // 0xa4af84: DecompressPointer r1
    //     0xa4af84: add             x1, x1, HEAP, lsl #32
    // 0xa4af88: r16 = Sentinel
    //     0xa4af88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4af8c: cmp             w1, w16
    // 0xa4af90: b.eq            #0xa4b01c
    // 0xa4af94: mov             x2, x0
    // 0xa4af98: r0 = _registerItem()
    //     0xa4af98: bl              #0x97e7c0  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_registerItem
    // 0xa4af9c: ldur            x1, [fp, #-8]
    // 0xa4afa0: r0 = offset()
    //     0xa4afa0: bl              #0x94597c  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::offset
    // 0xa4afa4: LoadField: d0 = r0->field_7
    //     0xa4afa4: ldur            d0, [x0, #7]
    // 0xa4afa8: ldur            x1, [fp, #-8]
    // 0xa4afac: stur            d0, [fp, #-0x18]
    // 0xa4afb0: r0 = offset()
    //     0xa4afb0: bl              #0x94597c  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::offset
    // 0xa4afb4: LoadField: d1 = r0->field_f
    //     0xa4afb4: ldur            d1, [x0, #0xf]
    // 0xa4afb8: ldur            d0, [fp, #-0x18]
    // 0xa4afbc: r1 = Null
    //     0xa4afbc: mov             x1, NULL
    // 0xa4afc0: r0 = Matrix4.translationValues()
    //     0xa4afc0: bl              #0x78ff30  ; [package:vector_math/vector_math_64.dart] Matrix4::Matrix4.translationValues
    // 0xa4afc4: mov             x1, x0
    // 0xa4afc8: ldur            x0, [fp, #-8]
    // 0xa4afcc: stur            x1, [fp, #-0x10]
    // 0xa4afd0: LoadField: r2 = r0->field_b
    //     0xa4afd0: ldur            w2, [x0, #0xb]
    // 0xa4afd4: DecompressPointer r2
    //     0xa4afd4: add             x2, x2, HEAP, lsl #32
    // 0xa4afd8: cmp             w2, NULL
    // 0xa4afdc: b.eq            #0xa4b028
    // 0xa4afe0: LoadField: r0 = r2->field_13
    //     0xa4afe0: ldur            w0, [x2, #0x13]
    // 0xa4afe4: DecompressPointer r0
    //     0xa4afe4: add             x0, x0, HEAP, lsl #32
    // 0xa4afe8: stur            x0, [fp, #-8]
    // 0xa4afec: r0 = Transform()
    //     0xa4afec: bl              #0x9d3c68  ; AllocateTransformStub -> Transform (size=0x24)
    // 0xa4aff0: ldur            x1, [fp, #-0x10]
    // 0xa4aff4: StoreField: r0->field_f = r1
    //     0xa4aff4: stur            w1, [x0, #0xf]
    // 0xa4aff8: r1 = true
    //     0xa4aff8: add             x1, NULL, #0x20  ; true
    // 0xa4affc: StoreField: r0->field_1b = r1
    //     0xa4affc: stur            w1, [x0, #0x1b]
    // 0xa4b000: ldur            x1, [fp, #-8]
    // 0xa4b004: StoreField: r0->field_b = r1
    //     0xa4b004: stur            w1, [x0, #0xb]
    // 0xa4b008: LeaveFrame
    //     0xa4b008: mov             SP, fp
    //     0xa4b00c: ldp             fp, lr, [SP], #0x10
    // 0xa4b010: ret
    //     0xa4b010: ret             
    // 0xa4b014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4b014: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4b018: b               #0xa4af64
    // 0xa4b01c: r9 = _listState
    //     0xa4b01c: add             x9, PP, #0x51, lsl #12  ; [pp+0x514a0] Field <_ReorderableItemState@2201143295._listState@2201143295>: late (offset: 0x14)
    //     0xa4b020: ldr             x9, [x9, #0x4a0]
    // 0xa4b024: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4b024: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4b028: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b028: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83c78, size: 0x8c
    // 0xa83c78: EnterFrame
    //     0xa83c78: stp             fp, lr, [SP, #-0x10]!
    //     0xa83c7c: mov             fp, SP
    // 0xa83c80: AllocStack(0x8)
    //     0xa83c80: sub             SP, SP, #8
    // 0xa83c84: SetupParameters(_ReorderableItemState this /* r1 => r3, fp-0x8 */)
    //     0xa83c84: mov             x3, x1
    //     0xa83c88: stur            x1, [fp, #-8]
    // 0xa83c8c: CheckStackOverflow
    //     0xa83c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83c90: cmp             SP, x16
    //     0xa83c94: b.ls            #0xa83cec
    // 0xa83c98: LoadField: r1 = r3->field_1f
    //     0xa83c98: ldur            w1, [x3, #0x1f]
    // 0xa83c9c: DecompressPointer r1
    //     0xa83c9c: add             x1, x1, HEAP, lsl #32
    // 0xa83ca0: cmp             w1, NULL
    // 0xa83ca4: b.eq            #0xa83cb0
    // 0xa83ca8: r0 = dispose()
    //     0xa83ca8: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa83cac: ldur            x3, [fp, #-8]
    // 0xa83cb0: LoadField: r1 = r3->field_13
    //     0xa83cb0: ldur            w1, [x3, #0x13]
    // 0xa83cb4: DecompressPointer r1
    //     0xa83cb4: add             x1, x1, HEAP, lsl #32
    // 0xa83cb8: r16 = Sentinel
    //     0xa83cb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa83cbc: cmp             w1, w16
    // 0xa83cc0: b.eq            #0xa83cf4
    // 0xa83cc4: LoadField: r0 = r3->field_b
    //     0xa83cc4: ldur            w0, [x3, #0xb]
    // 0xa83cc8: DecompressPointer r0
    //     0xa83cc8: add             x0, x0, HEAP, lsl #32
    // 0xa83ccc: cmp             w0, NULL
    // 0xa83cd0: b.eq            #0xa83d00
    // 0xa83cd4: LoadField: r2 = r0->field_b
    //     0xa83cd4: ldur            x2, [x0, #0xb]
    // 0xa83cd8: r0 = _unregisterItem()
    //     0xa83cd8: bl              #0x92b9a8  ; [package:flutter/src/widgets/reorderable_list.dart] SliverReorderableListState::_unregisterItem
    // 0xa83cdc: r0 = Null
    //     0xa83cdc: mov             x0, NULL
    // 0xa83ce0: LeaveFrame
    //     0xa83ce0: mov             SP, fp
    //     0xa83ce4: ldp             fp, lr, [SP], #0x10
    // 0xa83ce8: ret
    //     0xa83ce8: ret             
    // 0xa83cec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83cec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83cf0: b               #0xa83c98
    // 0xa83cf4: r9 = _listState
    //     0xa83cf4: add             x9, PP, #0x51, lsl #12  ; [pp+0x514a0] Field <_ReorderableItemState@2201143295._listState@2201143295>: late (offset: 0x14)
    //     0xa83cf8: ldr             x9, [x9, #0x4a0]
    // 0xa83cfc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa83cfc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa83d00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa83d00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ updateForGap(/* No info */) {
    // ** addr: 0xbb79b0, size: 0x268
    // 0xbb79b0: EnterFrame
    //     0xbb79b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb79b4: mov             fp, SP
    // 0xbb79b8: AllocStack(0x30)
    //     0xbb79b8: sub             SP, SP, #0x30
    // 0xbb79bc: SetupParameters(_ReorderableItemState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbb79bc: mov             x0, x1
    //     0xbb79c0: stur            x1, [fp, #-8]
    //     0xbb79c4: stur            x2, [fp, #-0x10]
    // 0xbb79c8: CheckStackOverflow
    //     0xbb79c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb79cc: cmp             SP, x16
    //     0xbb79d0: b.ls            #0xbb7bfc
    // 0xbb79d4: r1 = 1
    //     0xbb79d4: movz            x1, #0x1
    // 0xbb79d8: r0 = AllocateContext()
    //     0xbb79d8: bl              #0xec126c  ; AllocateContextStub
    // 0xbb79dc: mov             x3, x0
    // 0xbb79e0: ldur            x0, [fp, #-8]
    // 0xbb79e4: stur            x3, [fp, #-0x18]
    // 0xbb79e8: StoreField: r3->field_f = r0
    //     0xbb79e8: stur            w0, [x3, #0xf]
    // 0xbb79ec: LoadField: r1 = r0->field_f
    //     0xbb79ec: ldur            w1, [x0, #0xf]
    // 0xbb79f0: DecompressPointer r1
    //     0xbb79f0: add             x1, x1, HEAP, lsl #32
    // 0xbb79f4: cmp             w1, NULL
    // 0xbb79f8: b.ne            #0xbb7a0c
    // 0xbb79fc: r0 = Null
    //     0xbb79fc: mov             x0, NULL
    // 0xbb7a00: LeaveFrame
    //     0xbb7a00: mov             SP, fp
    //     0xbb7a04: ldp             fp, lr, [SP], #0x10
    // 0xbb7a08: ret
    //     0xbb7a08: ret             
    // 0xbb7a0c: LoadField: r1 = r0->field_13
    //     0xbb7a0c: ldur            w1, [x0, #0x13]
    // 0xbb7a10: DecompressPointer r1
    //     0xbb7a10: add             x1, x1, HEAP, lsl #32
    // 0xbb7a14: r16 = Sentinel
    //     0xbb7a14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb7a18: cmp             w1, w16
    // 0xbb7a1c: b.eq            #0xbb7c04
    // 0xbb7a20: LoadField: r2 = r0->field_b
    //     0xbb7a20: ldur            w2, [x0, #0xb]
    // 0xbb7a24: DecompressPointer r2
    //     0xbb7a24: add             x2, x2, HEAP, lsl #32
    // 0xbb7a28: cmp             w2, NULL
    // 0xbb7a2c: b.eq            #0xbb7c10
    // 0xbb7a30: LoadField: r4 = r2->field_b
    //     0xbb7a30: ldur            x4, [x2, #0xb]
    // 0xbb7a34: mov             x2, x4
    // 0xbb7a38: r0 = _calculateNextDragOffset()
    //     0xbb7a38: bl              #0xbb7c18  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_calculateNextDragOffset
    // 0xbb7a3c: ldur            x2, [fp, #-8]
    // 0xbb7a40: stur            x0, [fp, #-0x20]
    // 0xbb7a44: LoadField: r1 = r2->field_1b
    //     0xbb7a44: ldur            w1, [x2, #0x1b]
    // 0xbb7a48: DecompressPointer r1
    //     0xbb7a48: add             x1, x1, HEAP, lsl #32
    // 0xbb7a4c: stp             x1, x0, [SP]
    // 0xbb7a50: r0 = ==()
    //     0xbb7a50: bl              #0xd38618  ; [dart:ui] Offset::==
    // 0xbb7a54: tbnz            w0, #4, #0xbb7a68
    // 0xbb7a58: r0 = Null
    //     0xbb7a58: mov             x0, NULL
    // 0xbb7a5c: LeaveFrame
    //     0xbb7a5c: mov             SP, fp
    //     0xbb7a60: ldp             fp, lr, [SP], #0x10
    // 0xbb7a64: ret
    //     0xbb7a64: ret             
    // 0xbb7a68: ldur            x2, [fp, #-8]
    // 0xbb7a6c: ldur            x1, [fp, #-0x10]
    // 0xbb7a70: ldur            x0, [fp, #-0x20]
    // 0xbb7a74: StoreField: r2->field_1b = r0
    //     0xbb7a74: stur            w0, [x2, #0x1b]
    //     0xbb7a78: ldurb           w16, [x2, #-1]
    //     0xbb7a7c: ldurb           w17, [x0, #-1]
    //     0xbb7a80: and             x16, x17, x16, lsr #2
    //     0xbb7a84: tst             x16, HEAP, lsr #32
    //     0xbb7a88: b.eq            #0xbb7a90
    //     0xbb7a8c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbb7a90: tbnz            w1, #4, #0xbb7b9c
    // 0xbb7a94: LoadField: r0 = r2->field_1f
    //     0xbb7a94: ldur            w0, [x2, #0x1f]
    // 0xbb7a98: DecompressPointer r0
    //     0xbb7a98: add             x0, x0, HEAP, lsl #32
    // 0xbb7a9c: cmp             w0, NULL
    // 0xbb7aa0: b.ne            #0xbb7b48
    // 0xbb7aa4: LoadField: r0 = r2->field_13
    //     0xbb7aa4: ldur            w0, [x2, #0x13]
    // 0xbb7aa8: DecompressPointer r0
    //     0xbb7aa8: add             x0, x0, HEAP, lsl #32
    // 0xbb7aac: stur            x0, [fp, #-0x10]
    // 0xbb7ab0: r1 = <double>
    //     0xbb7ab0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xbb7ab4: r0 = AnimationController()
    //     0xbb7ab4: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0xbb7ab8: stur            x0, [fp, #-0x20]
    // 0xbb7abc: r16 = Instance_Duration
    //     0xbb7abc: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xbb7ac0: ldr             x16, [x16, #0xd90]
    // 0xbb7ac4: str             x16, [SP]
    // 0xbb7ac8: mov             x1, x0
    // 0xbb7acc: ldur            x2, [fp, #-0x10]
    // 0xbb7ad0: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0xbb7ad0: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0xbb7ad4: ldr             x4, [x4, #0x408]
    // 0xbb7ad8: r0 = AnimationController()
    //     0xbb7ad8: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0xbb7adc: ldur            x2, [fp, #-8]
    // 0xbb7ae0: r1 = Function 'rebuild':.
    //     0xbb7ae0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51490] AnonymousClosure: (0x97e96c), in [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::rebuild (0x97e908)
    //     0xbb7ae4: ldr             x1, [x1, #0x490]
    // 0xbb7ae8: r0 = AllocateClosure()
    //     0xbb7ae8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb7aec: ldur            x1, [fp, #-0x20]
    // 0xbb7af0: mov             x2, x0
    // 0xbb7af4: r0 = addActionListener()
    //     0xbb7af4: bl              #0xc680d4  ; [package:flutter/src/widgets/actions.dart] Action::addActionListener
    // 0xbb7af8: ldur            x2, [fp, #-0x18]
    // 0xbb7afc: r1 = Function '<anonymous closure>':.
    //     0xbb7afc: add             x1, PP, #0x51, lsl #12  ; [pp+0x51498] AnonymousClosure: (0x945a68), in [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::updateForGap (0x9454c4)
    //     0xbb7b00: ldr             x1, [x1, #0x498]
    // 0xbb7b04: r0 = AllocateClosure()
    //     0xbb7b04: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb7b08: ldur            x1, [fp, #-0x20]
    // 0xbb7b0c: mov             x2, x0
    // 0xbb7b10: r0 = addStatusListener()
    //     0xbb7b10: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0xbb7b14: ldur            x1, [fp, #-0x20]
    // 0xbb7b18: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb7b18: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb7b1c: r0 = forward()
    //     0xbb7b1c: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xbb7b20: ldur            x0, [fp, #-0x20]
    // 0xbb7b24: ldur            x2, [fp, #-8]
    // 0xbb7b28: StoreField: r2->field_1f = r0
    //     0xbb7b28: stur            w0, [x2, #0x1f]
    //     0xbb7b2c: ldurb           w16, [x2, #-1]
    //     0xbb7b30: ldurb           w17, [x0, #-1]
    //     0xbb7b34: and             x16, x17, x16, lsr #2
    //     0xbb7b38: tst             x16, HEAP, lsr #32
    //     0xbb7b3c: b.eq            #0xbb7b44
    //     0xbb7b40: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbb7b44: b               #0xbb7b94
    // 0xbb7b48: mov             x1, x2
    // 0xbb7b4c: r0 = offset()
    //     0xbb7b4c: bl              #0x94597c  ; [package:flutter/src/widgets/reorderable_list.dart] _ReorderableItemState::offset
    // 0xbb7b50: ldur            x2, [fp, #-8]
    // 0xbb7b54: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb7b54: stur            w0, [x2, #0x17]
    //     0xbb7b58: ldurb           w16, [x2, #-1]
    //     0xbb7b5c: ldurb           w17, [x0, #-1]
    //     0xbb7b60: and             x16, x17, x16, lsr #2
    //     0xbb7b64: tst             x16, HEAP, lsr #32
    //     0xbb7b68: b.eq            #0xbb7b70
    //     0xbb7b6c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbb7b70: LoadField: r1 = r2->field_1f
    //     0xbb7b70: ldur            w1, [x2, #0x1f]
    // 0xbb7b74: DecompressPointer r1
    //     0xbb7b74: add             x1, x1, HEAP, lsl #32
    // 0xbb7b78: cmp             w1, NULL
    // 0xbb7b7c: b.eq            #0xbb7c14
    // 0xbb7b80: r16 = 0.000000
    //     0xbb7b80: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xbb7b84: str             x16, [SP]
    // 0xbb7b88: r4 = const [0, 0x2, 0x1, 0x1, from, 0x1, null]
    //     0xbb7b88: add             x4, PP, #0x3a, lsl #12  ; [pp+0x3a278] List(7) [0, 0x2, 0x1, 0x1, "from", 0x1, Null]
    //     0xbb7b8c: ldr             x4, [x4, #0x278]
    // 0xbb7b90: r0 = forward()
    //     0xbb7b90: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xbb7b94: ldur            x1, [fp, #-8]
    // 0xbb7b98: b               #0xbb7be8
    // 0xbb7b9c: mov             x0, x2
    // 0xbb7ba0: LoadField: r1 = r0->field_1f
    //     0xbb7ba0: ldur            w1, [x0, #0x1f]
    // 0xbb7ba4: DecompressPointer r1
    //     0xbb7ba4: add             x1, x1, HEAP, lsl #32
    // 0xbb7ba8: cmp             w1, NULL
    // 0xbb7bac: b.eq            #0xbb7bc0
    // 0xbb7bb0: r0 = dispose()
    //     0xbb7bb0: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xbb7bb4: ldur            x1, [fp, #-8]
    // 0xbb7bb8: StoreField: r1->field_1f = rNULL
    //     0xbb7bb8: stur            NULL, [x1, #0x1f]
    // 0xbb7bbc: b               #0xbb7bc4
    // 0xbb7bc0: mov             x1, x0
    // 0xbb7bc4: LoadField: r0 = r1->field_1b
    //     0xbb7bc4: ldur            w0, [x1, #0x1b]
    // 0xbb7bc8: DecompressPointer r0
    //     0xbb7bc8: add             x0, x0, HEAP, lsl #32
    // 0xbb7bcc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb7bcc: stur            w0, [x1, #0x17]
    //     0xbb7bd0: ldurb           w16, [x1, #-1]
    //     0xbb7bd4: ldurb           w17, [x0, #-1]
    //     0xbb7bd8: and             x16, x17, x16, lsr #2
    //     0xbb7bdc: tst             x16, HEAP, lsr #32
    //     0xbb7be0: b.eq            #0xbb7be8
    //     0xbb7be4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbb7be8: r0 = rebuild()
    //     0xbb7be8: bl              #0x97e908  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::rebuild
    // 0xbb7bec: r0 = Null
    //     0xbb7bec: mov             x0, NULL
    // 0xbb7bf0: LeaveFrame
    //     0xbb7bf0: mov             SP, fp
    //     0xbb7bf4: ldp             fp, lr, [SP], #0x10
    // 0xbb7bf8: ret
    //     0xbb7bf8: ret             
    // 0xbb7bfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7bfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb7c00: b               #0xbb79d4
    // 0xbb7c04: r9 = _listState
    //     0xbb7c04: add             x9, PP, #0x51, lsl #12  ; [pp+0x514a0] Field <_ReorderableItemState@2201143295._listState@2201143295>: late (offset: 0x14)
    //     0xbb7c08: ldr             x9, [x9, #0x4a0]
    // 0xbb7c0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb7c0c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb7c10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7c10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7c14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7c14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4099, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class _SliverReorderableGridState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6fa0c4, size: 0x184
    // 0x6fa0c4: EnterFrame
    //     0x6fa0c4: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa0c8: mov             fp, SP
    // 0x6fa0cc: AllocStack(0x20)
    //     0x6fa0cc: sub             SP, SP, #0x20
    // 0x6fa0d0: SetupParameters(_SliverReorderableGridState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6fa0d0: mov             x0, x1
    //     0x6fa0d4: stur            x1, [fp, #-8]
    //     0x6fa0d8: stur            x2, [fp, #-0x10]
    // 0x6fa0dc: CheckStackOverflow
    //     0x6fa0dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa0e0: cmp             SP, x16
    //     0x6fa0e4: b.ls            #0x6fa238
    // 0x6fa0e8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6fa0e8: ldur            w1, [x0, #0x17]
    // 0x6fa0ec: DecompressPointer r1
    //     0x6fa0ec: add             x1, x1, HEAP, lsl #32
    // 0x6fa0f0: cmp             w1, NULL
    // 0x6fa0f4: b.ne            #0x6fa100
    // 0x6fa0f8: mov             x1, x0
    // 0x6fa0fc: r0 = _updateTickerModeNotifier()
    //     0x6fa0fc: bl              #0x6fa26c  ; [package:reorderable_grid/src/reorderable_grid.dart] _SliverReorderableGridState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6fa100: ldur            x0, [fp, #-8]
    // 0x6fa104: LoadField: r1 = r0->field_13
    //     0x6fa104: ldur            w1, [x0, #0x13]
    // 0x6fa108: DecompressPointer r1
    //     0x6fa108: add             x1, x1, HEAP, lsl #32
    // 0x6fa10c: cmp             w1, NULL
    // 0x6fa110: b.ne            #0x6fa1a8
    // 0x6fa114: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x6fa114: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fa118: ldr             x0, [x0, #0x778]
    //     0x6fa11c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fa120: cmp             w0, w16
    //     0x6fa124: b.ne            #0x6fa130
    //     0x6fa128: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x6fa12c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fa130: r1 = <_WidgetTicker>
    //     0x6fa130: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c0] TypeArguments: <_WidgetTicker>
    //     0x6fa134: ldr             x1, [x1, #0x8c0]
    // 0x6fa138: stur            x0, [fp, #-0x18]
    // 0x6fa13c: r0 = _Set()
    //     0x6fa13c: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x6fa140: mov             x1, x0
    // 0x6fa144: ldur            x0, [fp, #-0x18]
    // 0x6fa148: stur            x1, [fp, #-0x20]
    // 0x6fa14c: StoreField: r1->field_1b = r0
    //     0x6fa14c: stur            w0, [x1, #0x1b]
    // 0x6fa150: StoreField: r1->field_b = rZR
    //     0x6fa150: stur            wzr, [x1, #0xb]
    // 0x6fa154: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x6fa154: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fa158: ldr             x0, [x0, #0x780]
    //     0x6fa15c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fa160: cmp             w0, w16
    //     0x6fa164: b.ne            #0x6fa170
    //     0x6fa168: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x6fa16c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fa170: mov             x1, x0
    // 0x6fa174: ldur            x0, [fp, #-0x20]
    // 0x6fa178: StoreField: r0->field_f = r1
    //     0x6fa178: stur            w1, [x0, #0xf]
    // 0x6fa17c: StoreField: r0->field_13 = rZR
    //     0x6fa17c: stur            wzr, [x0, #0x13]
    // 0x6fa180: ArrayStore: r0[0] = rZR  ; List_4
    //     0x6fa180: stur            wzr, [x0, #0x17]
    // 0x6fa184: ldur            x1, [fp, #-8]
    // 0x6fa188: StoreField: r1->field_13 = r0
    //     0x6fa188: stur            w0, [x1, #0x13]
    //     0x6fa18c: ldurb           w16, [x1, #-1]
    //     0x6fa190: ldurb           w17, [x0, #-1]
    //     0x6fa194: and             x16, x17, x16, lsr #2
    //     0x6fa198: tst             x16, HEAP, lsr #32
    //     0x6fa19c: b.eq            #0x6fa1a4
    //     0x6fa1a0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6fa1a4: b               #0x6fa1ac
    // 0x6fa1a8: mov             x1, x0
    // 0x6fa1ac: ldur            x0, [fp, #-0x10]
    // 0x6fa1b0: r0 = _WidgetTicker()
    //     0x6fa1b0: bl              #0x6f03ec  ; Allocate_WidgetTickerStub -> _WidgetTicker (size=0x20)
    // 0x6fa1b4: mov             x3, x0
    // 0x6fa1b8: ldur            x2, [fp, #-8]
    // 0x6fa1bc: stur            x3, [fp, #-0x18]
    // 0x6fa1c0: StoreField: r3->field_1b = r2
    //     0x6fa1c0: stur            w2, [x3, #0x1b]
    // 0x6fa1c4: r0 = false
    //     0x6fa1c4: add             x0, NULL, #0x30  ; false
    // 0x6fa1c8: StoreField: r3->field_b = r0
    //     0x6fa1c8: stur            w0, [x3, #0xb]
    // 0x6fa1cc: ldur            x0, [fp, #-0x10]
    // 0x6fa1d0: StoreField: r3->field_13 = r0
    //     0x6fa1d0: stur            w0, [x3, #0x13]
    // 0x6fa1d4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6fa1d4: ldur            w1, [x2, #0x17]
    // 0x6fa1d8: DecompressPointer r1
    //     0x6fa1d8: add             x1, x1, HEAP, lsl #32
    // 0x6fa1dc: cmp             w1, NULL
    // 0x6fa1e0: b.eq            #0x6fa240
    // 0x6fa1e4: r0 = LoadClassIdInstr(r1)
    //     0x6fa1e4: ldur            x0, [x1, #-1]
    //     0x6fa1e8: ubfx            x0, x0, #0xc, #0x14
    // 0x6fa1ec: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6fa1ec: movz            x17, #0x276f
    //     0x6fa1f0: movk            x17, #0x1, lsl #16
    //     0x6fa1f4: add             lr, x0, x17
    //     0x6fa1f8: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa1fc: blr             lr
    // 0x6fa200: eor             x2, x0, #0x10
    // 0x6fa204: ldur            x1, [fp, #-0x18]
    // 0x6fa208: r0 = muted=()
    //     0x6fa208: bl              #0x6efbf4  ; [package:flutter/src/scheduler/ticker.dart] Ticker::muted=
    // 0x6fa20c: ldur            x0, [fp, #-8]
    // 0x6fa210: LoadField: r1 = r0->field_13
    //     0x6fa210: ldur            w1, [x0, #0x13]
    // 0x6fa214: DecompressPointer r1
    //     0x6fa214: add             x1, x1, HEAP, lsl #32
    // 0x6fa218: cmp             w1, NULL
    // 0x6fa21c: b.eq            #0x6fa244
    // 0x6fa220: ldur            x2, [fp, #-0x18]
    // 0x6fa224: r0 = add()
    //     0x6fa224: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x6fa228: ldur            x0, [fp, #-0x18]
    // 0x6fa22c: LeaveFrame
    //     0x6fa22c: mov             SP, fp
    //     0x6fa230: ldp             fp, lr, [SP], #0x10
    // 0x6fa234: ret
    //     0x6fa234: ret             
    // 0x6fa238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa238: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa23c: b               #0x6fa0e8
    // 0x6fa240: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa240: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fa244: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa244: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6fa26c, size: 0x124
    // 0x6fa26c: EnterFrame
    //     0x6fa26c: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa270: mov             fp, SP
    // 0x6fa274: AllocStack(0x18)
    //     0x6fa274: sub             SP, SP, #0x18
    // 0x6fa278: SetupParameters(_SliverReorderableGridState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6fa278: mov             x2, x1
    //     0x6fa27c: stur            x1, [fp, #-8]
    // 0x6fa280: CheckStackOverflow
    //     0x6fa280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa284: cmp             SP, x16
    //     0x6fa288: b.ls            #0x6fa384
    // 0x6fa28c: LoadField: r1 = r2->field_f
    //     0x6fa28c: ldur            w1, [x2, #0xf]
    // 0x6fa290: DecompressPointer r1
    //     0x6fa290: add             x1, x1, HEAP, lsl #32
    // 0x6fa294: cmp             w1, NULL
    // 0x6fa298: b.eq            #0x6fa38c
    // 0x6fa29c: r0 = getNotifier()
    //     0x6fa29c: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6fa2a0: mov             x3, x0
    // 0x6fa2a4: ldur            x0, [fp, #-8]
    // 0x6fa2a8: stur            x3, [fp, #-0x18]
    // 0x6fa2ac: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6fa2ac: ldur            w4, [x0, #0x17]
    // 0x6fa2b0: DecompressPointer r4
    //     0x6fa2b0: add             x4, x4, HEAP, lsl #32
    // 0x6fa2b4: stur            x4, [fp, #-0x10]
    // 0x6fa2b8: cmp             w3, w4
    // 0x6fa2bc: b.ne            #0x6fa2d0
    // 0x6fa2c0: r0 = Null
    //     0x6fa2c0: mov             x0, NULL
    // 0x6fa2c4: LeaveFrame
    //     0x6fa2c4: mov             SP, fp
    //     0x6fa2c8: ldp             fp, lr, [SP], #0x10
    // 0x6fa2cc: ret
    //     0x6fa2cc: ret             
    // 0x6fa2d0: cmp             w4, NULL
    // 0x6fa2d4: b.eq            #0x6fa318
    // 0x6fa2d8: mov             x2, x0
    // 0x6fa2dc: r1 = Function '_updateTickers@364311458':.
    //     0x6fa2dc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c68] AnonymousClosure: (0x6fa390), in [package:reorderable_grid/src/reorderable_grid.dart] _SliverReorderableGridState&State&TickerProviderStateMixin::_updateTickers (0x6fa3c8)
    //     0x6fa2e0: ldr             x1, [x1, #0xc68]
    // 0x6fa2e4: r0 = AllocateClosure()
    //     0x6fa2e4: bl              #0xec1630  ; AllocateClosureStub
    // 0x6fa2e8: ldur            x1, [fp, #-0x10]
    // 0x6fa2ec: r2 = LoadClassIdInstr(r1)
    //     0x6fa2ec: ldur            x2, [x1, #-1]
    //     0x6fa2f0: ubfx            x2, x2, #0xc, #0x14
    // 0x6fa2f4: mov             x16, x0
    // 0x6fa2f8: mov             x0, x2
    // 0x6fa2fc: mov             x2, x16
    // 0x6fa300: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6fa300: movz            x17, #0xbf5c
    //     0x6fa304: add             lr, x0, x17
    //     0x6fa308: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa30c: blr             lr
    // 0x6fa310: ldur            x0, [fp, #-8]
    // 0x6fa314: ldur            x3, [fp, #-0x18]
    // 0x6fa318: mov             x2, x0
    // 0x6fa31c: r1 = Function '_updateTickers@364311458':.
    //     0x6fa31c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c68] AnonymousClosure: (0x6fa390), in [package:reorderable_grid/src/reorderable_grid.dart] _SliverReorderableGridState&State&TickerProviderStateMixin::_updateTickers (0x6fa3c8)
    //     0x6fa320: ldr             x1, [x1, #0xc68]
    // 0x6fa324: r0 = AllocateClosure()
    //     0x6fa324: bl              #0xec1630  ; AllocateClosureStub
    // 0x6fa328: ldur            x3, [fp, #-0x18]
    // 0x6fa32c: r1 = LoadClassIdInstr(r3)
    //     0x6fa32c: ldur            x1, [x3, #-1]
    //     0x6fa330: ubfx            x1, x1, #0xc, #0x14
    // 0x6fa334: mov             x2, x0
    // 0x6fa338: mov             x0, x1
    // 0x6fa33c: mov             x1, x3
    // 0x6fa340: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6fa340: movz            x17, #0xc407
    //     0x6fa344: add             lr, x0, x17
    //     0x6fa348: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa34c: blr             lr
    // 0x6fa350: ldur            x0, [fp, #-0x18]
    // 0x6fa354: ldur            x1, [fp, #-8]
    // 0x6fa358: ArrayStore: r1[0] = r0  ; List_4
    //     0x6fa358: stur            w0, [x1, #0x17]
    //     0x6fa35c: ldurb           w16, [x1, #-1]
    //     0x6fa360: ldurb           w17, [x0, #-1]
    //     0x6fa364: and             x16, x17, x16, lsr #2
    //     0x6fa368: tst             x16, HEAP, lsr #32
    //     0x6fa36c: b.eq            #0x6fa374
    //     0x6fa370: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6fa374: r0 = Null
    //     0x6fa374: mov             x0, NULL
    // 0x6fa378: LeaveFrame
    //     0x6fa378: mov             SP, fp
    //     0x6fa37c: ldp             fp, lr, [SP], #0x10
    // 0x6fa380: ret
    //     0x6fa380: ret             
    // 0x6fa384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa384: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa388: b               #0x6fa28c
    // 0x6fa38c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa38c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTickers(dynamic) {
    // ** addr: 0x6fa390, size: 0x38
    // 0x6fa390: EnterFrame
    //     0x6fa390: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa394: mov             fp, SP
    // 0x6fa398: ldr             x0, [fp, #0x10]
    // 0x6fa39c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6fa39c: ldur            w1, [x0, #0x17]
    // 0x6fa3a0: DecompressPointer r1
    //     0x6fa3a0: add             x1, x1, HEAP, lsl #32
    // 0x6fa3a4: CheckStackOverflow
    //     0x6fa3a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa3a8: cmp             SP, x16
    //     0x6fa3ac: b.ls            #0x6fa3c0
    // 0x6fa3b0: r0 = _updateTickers()
    //     0x6fa3b0: bl              #0x6fa3c8  ; [package:reorderable_grid/src/reorderable_grid.dart] _SliverReorderableGridState&State&TickerProviderStateMixin::_updateTickers
    // 0x6fa3b4: LeaveFrame
    //     0x6fa3b4: mov             SP, fp
    //     0x6fa3b8: ldp             fp, lr, [SP], #0x10
    // 0x6fa3bc: ret
    //     0x6fa3bc: ret             
    // 0x6fa3c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa3c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa3c4: b               #0x6fa3b0
  }
  _ _updateTickers(/* No info */) {
    // ** addr: 0x6fa3c8, size: 0x164
    // 0x6fa3c8: EnterFrame
    //     0x6fa3c8: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa3cc: mov             fp, SP
    // 0x6fa3d0: AllocStack(0x20)
    //     0x6fa3d0: sub             SP, SP, #0x20
    // 0x6fa3d4: SetupParameters(_SliverReorderableGridState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6fa3d4: mov             x2, x1
    //     0x6fa3d8: stur            x1, [fp, #-8]
    // 0x6fa3dc: CheckStackOverflow
    //     0x6fa3dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa3e0: cmp             SP, x16
    //     0x6fa3e4: b.ls            #0x6fa514
    // 0x6fa3e8: LoadField: r0 = r2->field_13
    //     0x6fa3e8: ldur            w0, [x2, #0x13]
    // 0x6fa3ec: DecompressPointer r0
    //     0x6fa3ec: add             x0, x0, HEAP, lsl #32
    // 0x6fa3f0: cmp             w0, NULL
    // 0x6fa3f4: b.eq            #0x6fa504
    // 0x6fa3f8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6fa3f8: ldur            w1, [x2, #0x17]
    // 0x6fa3fc: DecompressPointer r1
    //     0x6fa3fc: add             x1, x1, HEAP, lsl #32
    // 0x6fa400: cmp             w1, NULL
    // 0x6fa404: b.eq            #0x6fa51c
    // 0x6fa408: r0 = LoadClassIdInstr(r1)
    //     0x6fa408: ldur            x0, [x1, #-1]
    //     0x6fa40c: ubfx            x0, x0, #0xc, #0x14
    // 0x6fa410: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6fa410: movz            x17, #0x276f
    //     0x6fa414: movk            x17, #0x1, lsl #16
    //     0x6fa418: add             lr, x0, x17
    //     0x6fa41c: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa420: blr             lr
    // 0x6fa424: eor             x2, x0, #0x10
    // 0x6fa428: ldur            x0, [fp, #-8]
    // 0x6fa42c: stur            x2, [fp, #-0x10]
    // 0x6fa430: LoadField: r1 = r0->field_13
    //     0x6fa430: ldur            w1, [x0, #0x13]
    // 0x6fa434: DecompressPointer r1
    //     0x6fa434: add             x1, x1, HEAP, lsl #32
    // 0x6fa438: cmp             w1, NULL
    // 0x6fa43c: b.eq            #0x6fa520
    // 0x6fa440: r0 = iterator()
    //     0x6fa440: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x6fa444: stur            x0, [fp, #-0x18]
    // 0x6fa448: LoadField: r2 = r0->field_7
    //     0x6fa448: ldur            w2, [x0, #7]
    // 0x6fa44c: DecompressPointer r2
    //     0x6fa44c: add             x2, x2, HEAP, lsl #32
    // 0x6fa450: stur            x2, [fp, #-8]
    // 0x6fa454: ldur            x3, [fp, #-0x10]
    // 0x6fa458: CheckStackOverflow
    //     0x6fa458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa45c: cmp             SP, x16
    //     0x6fa460: b.ls            #0x6fa524
    // 0x6fa464: mov             x1, x0
    // 0x6fa468: r0 = moveNext()
    //     0x6fa468: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x6fa46c: tbnz            w0, #4, #0x6fa504
    // 0x6fa470: ldur            x3, [fp, #-0x18]
    // 0x6fa474: LoadField: r4 = r3->field_33
    //     0x6fa474: ldur            w4, [x3, #0x33]
    // 0x6fa478: DecompressPointer r4
    //     0x6fa478: add             x4, x4, HEAP, lsl #32
    // 0x6fa47c: stur            x4, [fp, #-0x20]
    // 0x6fa480: cmp             w4, NULL
    // 0x6fa484: b.ne            #0x6fa4b8
    // 0x6fa488: mov             x0, x4
    // 0x6fa48c: ldur            x2, [fp, #-8]
    // 0x6fa490: r1 = Null
    //     0x6fa490: mov             x1, NULL
    // 0x6fa494: cmp             w2, NULL
    // 0x6fa498: b.eq            #0x6fa4b8
    // 0x6fa49c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6fa49c: ldur            w4, [x2, #0x17]
    // 0x6fa4a0: DecompressPointer r4
    //     0x6fa4a0: add             x4, x4, HEAP, lsl #32
    // 0x6fa4a4: r8 = X0
    //     0x6fa4a4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6fa4a8: LoadField: r9 = r4->field_7
    //     0x6fa4a8: ldur            x9, [x4, #7]
    // 0x6fa4ac: r3 = Null
    //     0x6fa4ac: add             x3, PP, #0x57, lsl #12  ; [pp+0x57c58] Null
    //     0x6fa4b0: ldr             x3, [x3, #0xc58]
    // 0x6fa4b4: blr             x9
    // 0x6fa4b8: ldur            x2, [fp, #-0x10]
    // 0x6fa4bc: ldur            x0, [fp, #-0x20]
    // 0x6fa4c0: LoadField: r1 = r0->field_b
    //     0x6fa4c0: ldur            w1, [x0, #0xb]
    // 0x6fa4c4: DecompressPointer r1
    //     0x6fa4c4: add             x1, x1, HEAP, lsl #32
    // 0x6fa4c8: cmp             w2, w1
    // 0x6fa4cc: b.eq            #0x6fa4f8
    // 0x6fa4d0: StoreField: r0->field_b = r2
    //     0x6fa4d0: stur            w2, [x0, #0xb]
    // 0x6fa4d4: tbnz            w2, #4, #0x6fa4e4
    // 0x6fa4d8: mov             x1, x0
    // 0x6fa4dc: r0 = unscheduleTick()
    //     0x6fa4dc: bl              #0x656684  ; [package:flutter/src/scheduler/ticker.dart] Ticker::unscheduleTick
    // 0x6fa4e0: b               #0x6fa4f8
    // 0x6fa4e4: mov             x1, x0
    // 0x6fa4e8: r0 = shouldScheduleTick()
    //     0x6fa4e8: bl              #0x655b90  ; [package:flutter/src/scheduler/ticker.dart] Ticker::shouldScheduleTick
    // 0x6fa4ec: tbnz            w0, #4, #0x6fa4f8
    // 0x6fa4f0: ldur            x1, [fp, #-0x20]
    // 0x6fa4f4: r0 = scheduleTick()
    //     0x6fa4f4: bl              #0x655924  ; [package:flutter/src/scheduler/ticker.dart] Ticker::scheduleTick
    // 0x6fa4f8: ldur            x0, [fp, #-0x18]
    // 0x6fa4fc: ldur            x2, [fp, #-8]
    // 0x6fa500: b               #0x6fa454
    // 0x6fa504: r0 = Null
    //     0x6fa504: mov             x0, NULL
    // 0x6fa508: LeaveFrame
    //     0x6fa508: mov             SP, fp
    //     0x6fa50c: ldp             fp, lr, [SP], #0x10
    // 0x6fa510: ret
    //     0x6fa510: ret             
    // 0x6fa514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa514: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa518: b               #0x6fa3e8
    // 0x6fa51c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa51c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fa520: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa520: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fa524: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa524: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa528: b               #0x6fa464
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83be4, size: 0x94
    // 0xa83be4: EnterFrame
    //     0xa83be4: stp             fp, lr, [SP, #-0x10]!
    //     0xa83be8: mov             fp, SP
    // 0xa83bec: AllocStack(0x10)
    //     0xa83bec: sub             SP, SP, #0x10
    // 0xa83bf0: SetupParameters(_SliverReorderableGridState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa83bf0: mov             x0, x1
    //     0xa83bf4: stur            x1, [fp, #-0x10]
    // 0xa83bf8: CheckStackOverflow
    //     0xa83bf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83bfc: cmp             SP, x16
    //     0xa83c00: b.ls            #0xa83c70
    // 0xa83c04: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa83c04: ldur            w3, [x0, #0x17]
    // 0xa83c08: DecompressPointer r3
    //     0xa83c08: add             x3, x3, HEAP, lsl #32
    // 0xa83c0c: stur            x3, [fp, #-8]
    // 0xa83c10: cmp             w3, NULL
    // 0xa83c14: b.ne            #0xa83c20
    // 0xa83c18: mov             x1, x0
    // 0xa83c1c: b               #0xa83c5c
    // 0xa83c20: mov             x2, x0
    // 0xa83c24: r1 = Function '_updateTickers@364311458':.
    //     0xa83c24: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c68] AnonymousClosure: (0x6fa390), in [package:reorderable_grid/src/reorderable_grid.dart] _SliverReorderableGridState&State&TickerProviderStateMixin::_updateTickers (0x6fa3c8)
    //     0xa83c28: ldr             x1, [x1, #0xc68]
    // 0xa83c2c: r0 = AllocateClosure()
    //     0xa83c2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa83c30: ldur            x1, [fp, #-8]
    // 0xa83c34: r2 = LoadClassIdInstr(r1)
    //     0xa83c34: ldur            x2, [x1, #-1]
    //     0xa83c38: ubfx            x2, x2, #0xc, #0x14
    // 0xa83c3c: mov             x16, x0
    // 0xa83c40: mov             x0, x2
    // 0xa83c44: mov             x2, x16
    // 0xa83c48: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa83c48: movz            x17, #0xbf5c
    //     0xa83c4c: add             lr, x0, x17
    //     0xa83c50: ldr             lr, [x21, lr, lsl #3]
    //     0xa83c54: blr             lr
    // 0xa83c58: ldur            x1, [fp, #-0x10]
    // 0xa83c5c: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa83c5c: stur            NULL, [x1, #0x17]
    // 0xa83c60: r0 = Null
    //     0xa83c60: mov             x0, NULL
    // 0xa83c64: LeaveFrame
    //     0xa83c64: mov             SP, fp
    //     0xa83c68: ldp             fp, lr, [SP], #0x10
    // 0xa83c6c: ret
    //     0xa83c6c: ret             
    // 0xa83c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83c70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83c74: b               #0xa83c04
  }
  _ activate(/* No info */) {
    // ** addr: 0xa85dfc, size: 0x48
    // 0xa85dfc: EnterFrame
    //     0xa85dfc: stp             fp, lr, [SP, #-0x10]!
    //     0xa85e00: mov             fp, SP
    // 0xa85e04: AllocStack(0x8)
    //     0xa85e04: sub             SP, SP, #8
    // 0xa85e08: SetupParameters(_SliverReorderableGridState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa85e08: mov             x0, x1
    //     0xa85e0c: stur            x1, [fp, #-8]
    // 0xa85e10: CheckStackOverflow
    //     0xa85e10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85e14: cmp             SP, x16
    //     0xa85e18: b.ls            #0xa85e3c
    // 0xa85e1c: mov             x1, x0
    // 0xa85e20: r0 = _updateTickerModeNotifier()
    //     0xa85e20: bl              #0x6fa26c  ; [package:reorderable_grid/src/reorderable_grid.dart] _SliverReorderableGridState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa85e24: ldur            x1, [fp, #-8]
    // 0xa85e28: r0 = _updateTickers()
    //     0xa85e28: bl              #0x6fa3c8  ; [package:reorderable_grid/src/reorderable_grid.dart] _SliverReorderableGridState&State&TickerProviderStateMixin::_updateTickers
    // 0xa85e2c: r0 = Null
    //     0xa85e2c: mov             x0, NULL
    // 0xa85e30: LeaveFrame
    //     0xa85e30: mov             SP, fp
    //     0xa85e34: ldp             fp, lr, [SP], #0x10
    // 0xa85e38: ret
    //     0xa85e38: ret             
    // 0xa85e3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85e3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85e40: b               #0xa85e1c
  }
}

// class id: 4100, size: 0x3c, field offset: 0x1c
class SliverReorderableGridState extends _SliverReorderableGridState&State&TickerProviderStateMixin {

  _ _registerItem(/* No info */) {
    // ** addr: 0x97e7c0, size: 0x148
    // 0x97e7c0: EnterFrame
    //     0x97e7c0: stp             fp, lr, [SP, #-0x10]!
    //     0x97e7c4: mov             fp, SP
    // 0x97e7c8: AllocStack(0x10)
    //     0x97e7c8: sub             SP, SP, #0x10
    // 0x97e7cc: SetupParameters(SliverReorderableGridState this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */)
    //     0x97e7cc: mov             x5, x1
    //     0x97e7d0: mov             x4, x2
    //     0x97e7d4: stur            x1, [fp, #-8]
    //     0x97e7d8: stur            x2, [fp, #-0x10]
    // 0x97e7dc: CheckStackOverflow
    //     0x97e7dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97e7e0: cmp             SP, x16
    //     0x97e7e4: b.ls            #0x97e8ec
    // 0x97e7e8: LoadField: r2 = r5->field_1b
    //     0x97e7e8: ldur            w2, [x5, #0x1b]
    // 0x97e7ec: DecompressPointer r2
    //     0x97e7ec: add             x2, x2, HEAP, lsl #32
    // 0x97e7f0: LoadField: r0 = r4->field_b
    //     0x97e7f0: ldur            w0, [x4, #0xb]
    // 0x97e7f4: DecompressPointer r0
    //     0x97e7f4: add             x0, x0, HEAP, lsl #32
    // 0x97e7f8: cmp             w0, NULL
    // 0x97e7fc: b.eq            #0x97e8f4
    // 0x97e800: LoadField: r3 = r0->field_b
    //     0x97e800: ldur            x3, [x0, #0xb]
    // 0x97e804: r0 = BoxInt64Instr(r3)
    //     0x97e804: sbfiz           x0, x3, #1, #0x1f
    //     0x97e808: cmp             x3, x0, asr #1
    //     0x97e80c: b.eq            #0x97e818
    //     0x97e810: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x97e814: stur            x3, [x0, #7]
    // 0x97e818: mov             x1, x2
    // 0x97e81c: mov             x2, x0
    // 0x97e820: mov             x3, x4
    // 0x97e824: r0 = []=()
    //     0x97e824: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97e828: ldur            x3, [fp, #-0x10]
    // 0x97e82c: LoadField: r0 = r3->field_b
    //     0x97e82c: ldur            w0, [x3, #0xb]
    // 0x97e830: DecompressPointer r0
    //     0x97e830: add             x0, x0, HEAP, lsl #32
    // 0x97e834: cmp             w0, NULL
    // 0x97e838: b.eq            #0x97e8f8
    // 0x97e83c: LoadField: r2 = r0->field_b
    //     0x97e83c: ldur            x2, [x0, #0xb]
    // 0x97e840: ldur            x0, [fp, #-8]
    // 0x97e844: LoadField: r1 = r0->field_27
    //     0x97e844: ldur            w1, [x0, #0x27]
    // 0x97e848: DecompressPointer r1
    //     0x97e848: add             x1, x1, HEAP, lsl #32
    // 0x97e84c: cmp             w1, NULL
    // 0x97e850: b.ne            #0x97e85c
    // 0x97e854: r4 = Null
    //     0x97e854: mov             x4, NULL
    // 0x97e858: b               #0x97e874
    // 0x97e85c: LoadField: r0 = r1->field_23
    //     0x97e85c: ldur            w0, [x1, #0x23]
    // 0x97e860: DecompressPointer r0
    //     0x97e860: add             x0, x0, HEAP, lsl #32
    // 0x97e864: r16 = Sentinel
    //     0x97e864: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97e868: cmp             w0, w16
    // 0x97e86c: b.eq            #0x97e8fc
    // 0x97e870: mov             x4, x0
    // 0x97e874: r0 = BoxInt64Instr(r2)
    //     0x97e874: sbfiz           x0, x2, #1, #0x1f
    //     0x97e878: cmp             x2, x0, asr #1
    //     0x97e87c: b.eq            #0x97e888
    //     0x97e880: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x97e884: stur            x2, [x0, #7]
    // 0x97e888: cmp             w0, w4
    // 0x97e88c: b.eq            #0x97e8c8
    // 0x97e890: and             w16, w0, w4
    // 0x97e894: branchIfSmi(r16, 0x97e8dc)
    //     0x97e894: tbz             w16, #0, #0x97e8dc
    // 0x97e898: r16 = LoadClassIdInstr(r0)
    //     0x97e898: ldur            x16, [x0, #-1]
    //     0x97e89c: ubfx            x16, x16, #0xc, #0x14
    // 0x97e8a0: cmp             x16, #0x3d
    // 0x97e8a4: b.ne            #0x97e8dc
    // 0x97e8a8: r16 = LoadClassIdInstr(r4)
    //     0x97e8a8: ldur            x16, [x4, #-1]
    //     0x97e8ac: ubfx            x16, x16, #0xc, #0x14
    // 0x97e8b0: cmp             x16, #0x3d
    // 0x97e8b4: b.ne            #0x97e8dc
    // 0x97e8b8: LoadField: r16 = r0->field_7
    //     0x97e8b8: ldur            x16, [x0, #7]
    // 0x97e8bc: LoadField: r17 = r4->field_7
    //     0x97e8bc: ldur            x17, [x4, #7]
    // 0x97e8c0: cmp             x16, x17
    // 0x97e8c4: b.ne            #0x97e8dc
    // 0x97e8c8: mov             x1, x3
    // 0x97e8cc: r2 = true
    //     0x97e8cc: add             x2, NULL, #0x20  ; true
    // 0x97e8d0: r0 = dragging=()
    //     0x97e8d0: bl              #0x97e9a4  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::dragging=
    // 0x97e8d4: ldur            x1, [fp, #-0x10]
    // 0x97e8d8: r0 = rebuild()
    //     0x97e8d8: bl              #0x97e908  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::rebuild
    // 0x97e8dc: r0 = Null
    //     0x97e8dc: mov             x0, NULL
    // 0x97e8e0: LeaveFrame
    //     0x97e8e0: mov             SP, fp
    //     0x97e8e4: ldp             fp, lr, [SP], #0x10
    // 0x97e8e8: ret
    //     0x97e8e8: ret             
    // 0x97e8ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97e8ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97e8f0: b               #0x97e7e8
    // 0x97e8f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97e8f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97e8f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97e8f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97e8fc: r9 = index
    //     0x97e8fc: add             x9, PP, #0x51, lsl #12  ; [pp+0x513f0] Field <<EMAIL>>: late (offset: 0x24)
    //     0x97e900: ldr             x9, [x9, #0x3f0]
    // 0x97e904: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x97e904: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a1204, size: 0xe8
    // 0x9a1204: EnterFrame
    //     0x9a1204: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1208: mov             fp, SP
    // 0x9a120c: AllocStack(0x10)
    //     0x9a120c: sub             SP, SP, #0x10
    // 0x9a1210: SetupParameters(SliverReorderableGridState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a1210: mov             x4, x1
    //     0x9a1214: mov             x3, x2
    //     0x9a1218: stur            x1, [fp, #-8]
    //     0x9a121c: stur            x2, [fp, #-0x10]
    // 0x9a1220: CheckStackOverflow
    //     0x9a1220: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1224: cmp             SP, x16
    //     0x9a1228: b.ls            #0x9a12e0
    // 0x9a122c: mov             x0, x3
    // 0x9a1230: r2 = Null
    //     0x9a1230: mov             x2, NULL
    // 0x9a1234: r1 = Null
    //     0x9a1234: mov             x1, NULL
    // 0x9a1238: r4 = 60
    //     0x9a1238: movz            x4, #0x3c
    // 0x9a123c: branchIfSmi(r0, 0x9a1248)
    //     0x9a123c: tbz             w0, #0, #0x9a1248
    // 0x9a1240: r4 = LoadClassIdInstr(r0)
    //     0x9a1240: ldur            x4, [x0, #-1]
    //     0x9a1244: ubfx            x4, x4, #0xc, #0x14
    // 0x9a1248: r17 = 4701
    //     0x9a1248: movz            x17, #0x125d
    // 0x9a124c: cmp             x4, x17
    // 0x9a1250: b.eq            #0x9a1268
    // 0x9a1254: r8 = SliverReorderableGrid
    //     0x9a1254: add             x8, PP, #0x57, lsl #12  ; [pp+0x57c80] Type: SliverReorderableGrid
    //     0x9a1258: ldr             x8, [x8, #0xc80]
    // 0x9a125c: r3 = Null
    //     0x9a125c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57c88] Null
    //     0x9a1260: ldr             x3, [x3, #0xc88]
    // 0x9a1264: r0 = SliverReorderableGrid()
    //     0x9a1264: bl              #0x6fa248  ; IsType_SliverReorderableGrid_Stub
    // 0x9a1268: ldur            x3, [fp, #-8]
    // 0x9a126c: LoadField: r2 = r3->field_7
    //     0x9a126c: ldur            w2, [x3, #7]
    // 0x9a1270: DecompressPointer r2
    //     0x9a1270: add             x2, x2, HEAP, lsl #32
    // 0x9a1274: ldur            x0, [fp, #-0x10]
    // 0x9a1278: r1 = Null
    //     0x9a1278: mov             x1, NULL
    // 0x9a127c: cmp             w2, NULL
    // 0x9a1280: b.eq            #0x9a12a4
    // 0x9a1284: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a1284: ldur            w4, [x2, #0x17]
    // 0x9a1288: DecompressPointer r4
    //     0x9a1288: add             x4, x4, HEAP, lsl #32
    // 0x9a128c: r8 = X0 bound StatefulWidget
    //     0x9a128c: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a1290: ldr             x8, [x8, #0x7f8]
    // 0x9a1294: LoadField: r9 = r4->field_7
    //     0x9a1294: ldur            x9, [x4, #7]
    // 0x9a1298: r3 = Null
    //     0x9a1298: add             x3, PP, #0x57, lsl #12  ; [pp+0x57c98] Null
    //     0x9a129c: ldr             x3, [x3, #0xc98]
    // 0x9a12a0: blr             x9
    // 0x9a12a4: ldur            x1, [fp, #-8]
    // 0x9a12a8: LoadField: r0 = r1->field_b
    //     0x9a12a8: ldur            w0, [x1, #0xb]
    // 0x9a12ac: DecompressPointer r0
    //     0x9a12ac: add             x0, x0, HEAP, lsl #32
    // 0x9a12b0: cmp             w0, NULL
    // 0x9a12b4: b.eq            #0x9a12e8
    // 0x9a12b8: LoadField: r2 = r0->field_f
    //     0x9a12b8: ldur            x2, [x0, #0xf]
    // 0x9a12bc: ldur            x0, [fp, #-0x10]
    // 0x9a12c0: LoadField: r3 = r0->field_f
    //     0x9a12c0: ldur            x3, [x0, #0xf]
    // 0x9a12c4: cmp             x2, x3
    // 0x9a12c8: b.eq            #0x9a12d0
    // 0x9a12cc: r0 = _dragReset()
    //     0x9a12cc: bl              #0x9a12ec  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragReset
    // 0x9a12d0: r0 = Null
    //     0x9a12d0: mov             x0, NULL
    // 0x9a12d4: LeaveFrame
    //     0x9a12d4: mov             SP, fp
    //     0x9a12d8: ldp             fp, lr, [SP], #0x10
    // 0x9a12dc: ret
    //     0x9a12dc: ret             
    // 0x9a12e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a12e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a12e4: b               #0x9a122c
    // 0x9a12e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a12e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _dragReset(/* No info */) {
    // ** addr: 0x9a12ec, size: 0x64
    // 0x9a12ec: EnterFrame
    //     0x9a12ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9a12f0: mov             fp, SP
    // 0x9a12f4: AllocStack(0x8)
    //     0x9a12f4: sub             SP, SP, #8
    // 0x9a12f8: SetupParameters(SliverReorderableGridState this /* r1 => r1, fp-0x8 */)
    //     0x9a12f8: stur            x1, [fp, #-8]
    // 0x9a12fc: CheckStackOverflow
    //     0x9a12fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1300: cmp             SP, x16
    //     0x9a1304: b.ls            #0x9a1348
    // 0x9a1308: r1 = 1
    //     0x9a1308: movz            x1, #0x1
    // 0x9a130c: r0 = AllocateContext()
    //     0x9a130c: bl              #0xec126c  ; AllocateContextStub
    // 0x9a1310: mov             x1, x0
    // 0x9a1314: ldur            x0, [fp, #-8]
    // 0x9a1318: StoreField: r1->field_f = r0
    //     0x9a1318: stur            w0, [x1, #0xf]
    // 0x9a131c: mov             x2, x1
    // 0x9a1320: r1 = Function '<anonymous closure>':.
    //     0x9a1320: add             x1, PP, #0x51, lsl #12  ; [pp+0x514d0] AnonymousClosure: (0x9a1350), in [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragReset (0x9a12ec)
    //     0x9a1324: ldr             x1, [x1, #0x4d0]
    // 0x9a1328: r0 = AllocateClosure()
    //     0x9a1328: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a132c: ldur            x1, [fp, #-8]
    // 0x9a1330: mov             x2, x0
    // 0x9a1334: r0 = setState()
    //     0x9a1334: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9a1338: r0 = Null
    //     0x9a1338: mov             x0, NULL
    // 0x9a133c: LeaveFrame
    //     0x9a133c: mov             SP, fp
    //     0x9a1340: ldp             fp, lr, [SP], #0x10
    // 0x9a1344: ret
    //     0x9a1344: ret             
    // 0x9a1348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1348: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a134c: b               #0x9a1308
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9a1350, size: 0x1ac
    // 0x9a1350: EnterFrame
    //     0x9a1350: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1354: mov             fp, SP
    // 0x9a1358: AllocStack(0x10)
    //     0x9a1358: sub             SP, SP, #0x10
    // 0x9a135c: SetupParameters()
    //     0x9a135c: ldr             x0, [fp, #0x10]
    //     0x9a1360: ldur            w3, [x0, #0x17]
    //     0x9a1364: add             x3, x3, HEAP, lsl #32
    //     0x9a1368: stur            x3, [fp, #-8]
    // 0x9a136c: CheckStackOverflow
    //     0x9a136c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1370: cmp             SP, x16
    //     0x9a1374: b.ls            #0x9a14ec
    // 0x9a1378: LoadField: r0 = r3->field_f
    //     0x9a1378: ldur            w0, [x3, #0xf]
    // 0x9a137c: DecompressPointer r0
    //     0x9a137c: add             x0, x0, HEAP, lsl #32
    // 0x9a1380: LoadField: r1 = r0->field_27
    //     0x9a1380: ldur            w1, [x0, #0x27]
    // 0x9a1384: DecompressPointer r1
    //     0x9a1384: add             x1, x1, HEAP, lsl #32
    // 0x9a1388: cmp             w1, NULL
    // 0x9a138c: b.eq            #0x9a14dc
    // 0x9a1390: LoadField: r2 = r0->field_23
    //     0x9a1390: ldur            w2, [x0, #0x23]
    // 0x9a1394: DecompressPointer r2
    //     0x9a1394: add             x2, x2, HEAP, lsl #32
    // 0x9a1398: cmp             w2, NULL
    // 0x9a139c: b.eq            #0x9a143c
    // 0x9a13a0: LoadField: r1 = r0->field_1b
    //     0x9a13a0: ldur            w1, [x0, #0x1b]
    // 0x9a13a4: DecompressPointer r1
    //     0x9a13a4: add             x1, x1, HEAP, lsl #32
    // 0x9a13a8: r0 = containsKey()
    //     0x9a13a8: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x9a13ac: tbnz            w0, #4, #0x9a1434
    // 0x9a13b0: ldur            x0, [fp, #-8]
    // 0x9a13b4: LoadField: r1 = r0->field_f
    //     0x9a13b4: ldur            w1, [x0, #0xf]
    // 0x9a13b8: DecompressPointer r1
    //     0x9a13b8: add             x1, x1, HEAP, lsl #32
    // 0x9a13bc: LoadField: r3 = r1->field_1b
    //     0x9a13bc: ldur            w3, [x1, #0x1b]
    // 0x9a13c0: DecompressPointer r3
    //     0x9a13c0: add             x3, x3, HEAP, lsl #32
    // 0x9a13c4: stur            x3, [fp, #-0x10]
    // 0x9a13c8: LoadField: r2 = r1->field_23
    //     0x9a13c8: ldur            w2, [x1, #0x23]
    // 0x9a13cc: DecompressPointer r2
    //     0x9a13cc: add             x2, x2, HEAP, lsl #32
    // 0x9a13d0: cmp             w2, NULL
    // 0x9a13d4: b.eq            #0x9a14f4
    // 0x9a13d8: mov             x1, x3
    // 0x9a13dc: r0 = _getValueOrData()
    //     0x9a13dc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x9a13e0: mov             x1, x0
    // 0x9a13e4: ldur            x0, [fp, #-0x10]
    // 0x9a13e8: LoadField: r2 = r0->field_f
    //     0x9a13e8: ldur            w2, [x0, #0xf]
    // 0x9a13ec: DecompressPointer r2
    //     0x9a13ec: add             x2, x2, HEAP, lsl #32
    // 0x9a13f0: cmp             w2, w1
    // 0x9a13f4: b.ne            #0x9a1400
    // 0x9a13f8: r2 = Null
    //     0x9a13f8: mov             x2, NULL
    // 0x9a13fc: b               #0x9a1404
    // 0x9a1400: mov             x2, x1
    // 0x9a1404: ldur            x0, [fp, #-8]
    // 0x9a1408: r1 = false
    //     0x9a1408: add             x1, NULL, #0x30  ; false
    // 0x9a140c: cmp             w2, NULL
    // 0x9a1410: b.eq            #0x9a14f8
    // 0x9a1414: StoreField: r2->field_23 = r1
    //     0x9a1414: stur            w1, [x2, #0x23]
    // 0x9a1418: mov             x1, x2
    // 0x9a141c: r0 = rebuild()
    //     0x9a141c: bl              #0x97e908  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::rebuild
    // 0x9a1420: ldur            x0, [fp, #-8]
    // 0x9a1424: LoadField: r1 = r0->field_f
    //     0x9a1424: ldur            w1, [x0, #0xf]
    // 0x9a1428: DecompressPointer r1
    //     0x9a1428: add             x1, x1, HEAP, lsl #32
    // 0x9a142c: StoreField: r1->field_23 = rNULL
    //     0x9a142c: stur            NULL, [x1, #0x23]
    // 0x9a1430: b               #0x9a1440
    // 0x9a1434: ldur            x0, [fp, #-8]
    // 0x9a1438: b               #0x9a1440
    // 0x9a143c: mov             x0, x3
    // 0x9a1440: LoadField: r1 = r0->field_f
    //     0x9a1440: ldur            w1, [x0, #0xf]
    // 0x9a1444: DecompressPointer r1
    //     0x9a1444: add             x1, x1, HEAP, lsl #32
    // 0x9a1448: LoadField: r2 = r1->field_27
    //     0x9a1448: ldur            w2, [x1, #0x27]
    // 0x9a144c: DecompressPointer r2
    //     0x9a144c: add             x2, x2, HEAP, lsl #32
    // 0x9a1450: cmp             w2, NULL
    // 0x9a1454: b.eq            #0x9a1464
    // 0x9a1458: mov             x1, x2
    // 0x9a145c: r0 = dispose()
    //     0x9a145c: bl              #0x9a1644  ; [package:reorderable_grid/src/reorderable_grid.dart] _DragInfo::dispose
    // 0x9a1460: ldur            x0, [fp, #-8]
    // 0x9a1464: LoadField: r1 = r0->field_f
    //     0x9a1464: ldur            w1, [x0, #0xf]
    // 0x9a1468: DecompressPointer r1
    //     0x9a1468: add             x1, x1, HEAP, lsl #32
    // 0x9a146c: StoreField: r1->field_27 = rNULL
    //     0x9a146c: stur            NULL, [x1, #0x27]
    // 0x9a1470: r0 = _resetItemGap()
    //     0x9a1470: bl              #0x9a14fc  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_resetItemGap
    // 0x9a1474: ldur            x0, [fp, #-8]
    // 0x9a1478: LoadField: r1 = r0->field_f
    //     0x9a1478: ldur            w1, [x0, #0xf]
    // 0x9a147c: DecompressPointer r1
    //     0x9a147c: add             x1, x1, HEAP, lsl #32
    // 0x9a1480: LoadField: r2 = r1->field_33
    //     0x9a1480: ldur            w2, [x1, #0x33]
    // 0x9a1484: DecompressPointer r2
    //     0x9a1484: add             x2, x2, HEAP, lsl #32
    // 0x9a1488: cmp             w2, NULL
    // 0x9a148c: b.eq            #0x9a149c
    // 0x9a1490: mov             x1, x2
    // 0x9a1494: r0 = dispose()
    //     0x9a1494: bl              #0x7f95b4  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::dispose
    // 0x9a1498: ldur            x0, [fp, #-8]
    // 0x9a149c: LoadField: r1 = r0->field_f
    //     0x9a149c: ldur            w1, [x0, #0xf]
    // 0x9a14a0: DecompressPointer r1
    //     0x9a14a0: add             x1, x1, HEAP, lsl #32
    // 0x9a14a4: StoreField: r1->field_33 = rNULL
    //     0x9a14a4: stur            NULL, [x1, #0x33]
    // 0x9a14a8: LoadField: r2 = r1->field_1f
    //     0x9a14a8: ldur            w2, [x1, #0x1f]
    // 0x9a14ac: DecompressPointer r2
    //     0x9a14ac: add             x2, x2, HEAP, lsl #32
    // 0x9a14b0: cmp             w2, NULL
    // 0x9a14b4: b.ne            #0x9a14c0
    // 0x9a14b8: mov             x1, x0
    // 0x9a14bc: b               #0x9a14cc
    // 0x9a14c0: mov             x1, x2
    // 0x9a14c4: r0 = remove()
    //     0x9a14c4: bl              #0x64e5d0  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::remove
    // 0x9a14c8: ldur            x1, [fp, #-8]
    // 0x9a14cc: LoadField: r2 = r1->field_f
    //     0x9a14cc: ldur            w2, [x1, #0xf]
    // 0x9a14d0: DecompressPointer r2
    //     0x9a14d0: add             x2, x2, HEAP, lsl #32
    // 0x9a14d4: StoreField: r2->field_1f = rNULL
    //     0x9a14d4: stur            NULL, [x2, #0x1f]
    // 0x9a14d8: StoreField: r2->field_2f = rNULL
    //     0x9a14d8: stur            NULL, [x2, #0x2f]
    // 0x9a14dc: r0 = Null
    //     0x9a14dc: mov             x0, NULL
    // 0x9a14e0: LeaveFrame
    //     0x9a14e0: mov             SP, fp
    //     0x9a14e4: ldp             fp, lr, [SP], #0x10
    // 0x9a14e8: ret
    //     0x9a14e8: ret             
    // 0x9a14ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a14ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a14f0: b               #0x9a1378
    // 0x9a14f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a14f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a14f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a14f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _resetItemGap(/* No info */) {
    // ** addr: 0x9a14fc, size: 0x148
    // 0x9a14fc: EnterFrame
    //     0x9a14fc: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1500: mov             fp, SP
    // 0x9a1504: AllocStack(0x18)
    //     0x9a1504: sub             SP, SP, #0x18
    // 0x9a1508: CheckStackOverflow
    //     0x9a1508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a150c: cmp             SP, x16
    //     0x9a1510: b.ls            #0x9a1634
    // 0x9a1514: LoadField: r0 = r1->field_1b
    //     0x9a1514: ldur            w0, [x1, #0x1b]
    // 0x9a1518: DecompressPointer r0
    //     0x9a1518: add             x0, x0, HEAP, lsl #32
    // 0x9a151c: stur            x0, [fp, #-8]
    // 0x9a1520: LoadField: r2 = r0->field_7
    //     0x9a1520: ldur            w2, [x0, #7]
    // 0x9a1524: DecompressPointer r2
    //     0x9a1524: add             x2, x2, HEAP, lsl #32
    // 0x9a1528: r1 = Null
    //     0x9a1528: mov             x1, NULL
    // 0x9a152c: r3 = <X1>
    //     0x9a152c: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x9a1530: r0 = Null
    //     0x9a1530: mov             x0, NULL
    // 0x9a1534: cmp             x2, x0
    // 0x9a1538: b.eq            #0x9a1548
    // 0x9a153c: r30 = InstantiateTypeArgumentsStub
    //     0x9a153c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x9a1540: LoadField: r30 = r30->field_7
    //     0x9a1540: ldur            lr, [lr, #7]
    // 0x9a1544: blr             lr
    // 0x9a1548: mov             x1, x0
    // 0x9a154c: r0 = _CompactIterable()
    //     0x9a154c: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x9a1550: mov             x1, x0
    // 0x9a1554: ldur            x0, [fp, #-8]
    // 0x9a1558: StoreField: r1->field_b = r0
    //     0x9a1558: stur            w0, [x1, #0xb]
    // 0x9a155c: r0 = -1
    //     0x9a155c: movn            x0, #0
    // 0x9a1560: StoreField: r1->field_f = r0
    //     0x9a1560: stur            x0, [x1, #0xf]
    // 0x9a1564: r0 = 2
    //     0x9a1564: movz            x0, #0x2
    // 0x9a1568: ArrayStore: r1[0] = r0  ; List_8
    //     0x9a1568: stur            x0, [x1, #0x17]
    // 0x9a156c: r0 = iterator()
    //     0x9a156c: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x9a1570: stur            x0, [fp, #-0x10]
    // 0x9a1574: LoadField: r2 = r0->field_7
    //     0x9a1574: ldur            w2, [x0, #7]
    // 0x9a1578: DecompressPointer r2
    //     0x9a1578: add             x2, x2, HEAP, lsl #32
    // 0x9a157c: stur            x2, [fp, #-8]
    // 0x9a1580: CheckStackOverflow
    //     0x9a1580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1584: cmp             SP, x16
    //     0x9a1588: b.ls            #0x9a163c
    // 0x9a158c: mov             x1, x0
    // 0x9a1590: r0 = moveNext()
    //     0x9a1590: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x9a1594: tbnz            w0, #4, #0x9a1624
    // 0x9a1598: ldur            x3, [fp, #-0x10]
    // 0x9a159c: LoadField: r4 = r3->field_33
    //     0x9a159c: ldur            w4, [x3, #0x33]
    // 0x9a15a0: DecompressPointer r4
    //     0x9a15a0: add             x4, x4, HEAP, lsl #32
    // 0x9a15a4: stur            x4, [fp, #-0x18]
    // 0x9a15a8: cmp             w4, NULL
    // 0x9a15ac: b.ne            #0x9a15e0
    // 0x9a15b0: mov             x0, x4
    // 0x9a15b4: ldur            x2, [fp, #-8]
    // 0x9a15b8: r1 = Null
    //     0x9a15b8: mov             x1, NULL
    // 0x9a15bc: cmp             w2, NULL
    // 0x9a15c0: b.eq            #0x9a15e0
    // 0x9a15c4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a15c4: ldur            w4, [x2, #0x17]
    // 0x9a15c8: DecompressPointer r4
    //     0x9a15c8: add             x4, x4, HEAP, lsl #32
    // 0x9a15cc: r8 = X0
    //     0x9a15cc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9a15d0: LoadField: r9 = r4->field_7
    //     0x9a15d0: ldur            x9, [x4, #7]
    // 0x9a15d4: r3 = Null
    //     0x9a15d4: add             x3, PP, #0x51, lsl #12  ; [pp+0x514d8] Null
    //     0x9a15d8: ldr             x3, [x3, #0x4d8]
    // 0x9a15dc: blr             x9
    // 0x9a15e0: ldur            x0, [fp, #-0x18]
    // 0x9a15e4: LoadField: r1 = r0->field_1f
    //     0x9a15e4: ldur            w1, [x0, #0x1f]
    // 0x9a15e8: DecompressPointer r1
    //     0x9a15e8: add             x1, x1, HEAP, lsl #32
    // 0x9a15ec: cmp             w1, NULL
    // 0x9a15f0: b.eq            #0x9a1604
    // 0x9a15f4: r0 = dispose()
    //     0x9a15f4: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0x9a15f8: ldur            x1, [fp, #-0x18]
    // 0x9a15fc: StoreField: r1->field_1f = rNULL
    //     0x9a15fc: stur            NULL, [x1, #0x1f]
    // 0x9a1600: b               #0x9a1608
    // 0x9a1604: mov             x1, x0
    // 0x9a1608: r0 = Instance_Offset
    //     0x9a1608: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x9a160c: ArrayStore: r1[0] = r0  ; List_4
    //     0x9a160c: stur            w0, [x1, #0x17]
    // 0x9a1610: StoreField: r1->field_1b = r0
    //     0x9a1610: stur            w0, [x1, #0x1b]
    // 0x9a1614: r0 = rebuild()
    //     0x9a1614: bl              #0x97e908  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::rebuild
    // 0x9a1618: ldur            x0, [fp, #-0x10]
    // 0x9a161c: ldur            x2, [fp, #-8]
    // 0x9a1620: b               #0x9a1580
    // 0x9a1624: r0 = Null
    //     0x9a1624: mov             x0, NULL
    // 0x9a1628: LeaveFrame
    //     0x9a1628: mov             SP, fp
    //     0x9a162c: ldp             fp, lr, [SP], #0x10
    // 0x9a1630: ret
    //     0x9a1630: ret             
    // 0x9a1634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1634: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1638: b               #0x9a1514
    // 0x9a163c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a163c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1640: b               #0x9a158c
  }
  _ build(/* No info */) {
    // ** addr: 0xa4a354, size: 0xc4
    // 0xa4a354: EnterFrame
    //     0xa4a354: stp             fp, lr, [SP, #-0x10]!
    //     0xa4a358: mov             fp, SP
    // 0xa4a35c: AllocStack(0x20)
    //     0xa4a35c: sub             SP, SP, #0x20
    // 0xa4a360: SetupParameters(SliverReorderableGridState this /* r1 => r0 */)
    //     0xa4a360: mov             x0, x1
    // 0xa4a364: LoadField: r3 = r0->field_b
    //     0xa4a364: ldur            w3, [x0, #0xb]
    // 0xa4a368: DecompressPointer r3
    //     0xa4a368: add             x3, x3, HEAP, lsl #32
    // 0xa4a36c: stur            x3, [fp, #-0x10]
    // 0xa4a370: cmp             w3, NULL
    // 0xa4a374: b.eq            #0xa4a414
    // 0xa4a378: LoadField: r4 = r3->field_f
    //     0xa4a378: ldur            x4, [x3, #0xf]
    // 0xa4a37c: mov             x2, x0
    // 0xa4a380: stur            x4, [fp, #-8]
    // 0xa4a384: r1 = Function '_itemBuilder@2201143295':.
    //     0xa4a384: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c78] AnonymousClosure: (0xa4a424), in [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_itemBuilder (0xa4a464)
    //     0xa4a388: ldr             x1, [x1, #0xc78]
    // 0xa4a38c: r0 = AllocateClosure()
    //     0xa4a38c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4a390: stur            x0, [fp, #-0x18]
    // 0xa4a394: r0 = SliverChildBuilderDelegate()
    //     0xa4a394: bl              #0x9d3320  ; AllocateSliverChildBuilderDelegateStub -> SliverChildBuilderDelegate (size=0x2c)
    // 0xa4a398: mov             x2, x0
    // 0xa4a39c: ldur            x0, [fp, #-0x18]
    // 0xa4a3a0: stur            x2, [fp, #-0x20]
    // 0xa4a3a4: StoreField: r2->field_7 = r0
    //     0xa4a3a4: stur            w0, [x2, #7]
    // 0xa4a3a8: ldur            x3, [fp, #-8]
    // 0xa4a3ac: r0 = BoxInt64Instr(r3)
    //     0xa4a3ac: sbfiz           x0, x3, #1, #0x1f
    //     0xa4a3b0: cmp             x3, x0, asr #1
    //     0xa4a3b4: b.eq            #0xa4a3c0
    //     0xa4a3b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4a3bc: stur            x3, [x0, #7]
    // 0xa4a3c0: StoreField: r2->field_b = r0
    //     0xa4a3c0: stur            w0, [x2, #0xb]
    // 0xa4a3c4: r0 = true
    //     0xa4a3c4: add             x0, NULL, #0x20  ; true
    // 0xa4a3c8: StoreField: r2->field_f = r0
    //     0xa4a3c8: stur            w0, [x2, #0xf]
    // 0xa4a3cc: StoreField: r2->field_13 = r0
    //     0xa4a3cc: stur            w0, [x2, #0x13]
    // 0xa4a3d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4a3d0: stur            w0, [x2, #0x17]
    // 0xa4a3d4: r0 = Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static.
    //     0xa4a3d4: add             x0, PP, #0x26, lsl #12  ; [pp+0x26ef8] Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static. (0x7e54fb8bd554)
    //     0xa4a3d8: ldr             x0, [x0, #0xef8]
    // 0xa4a3dc: StoreField: r2->field_23 = r0
    //     0xa4a3dc: stur            w0, [x2, #0x23]
    // 0xa4a3e0: StoreField: r2->field_1b = rZR
    //     0xa4a3e0: stur            xzr, [x2, #0x1b]
    // 0xa4a3e4: ldur            x0, [fp, #-0x10]
    // 0xa4a3e8: LoadField: r1 = r0->field_23
    //     0xa4a3e8: ldur            w1, [x0, #0x23]
    // 0xa4a3ec: DecompressPointer r1
    //     0xa4a3ec: add             x1, x1, HEAP, lsl #32
    // 0xa4a3f0: stur            x1, [fp, #-0x18]
    // 0xa4a3f4: r0 = SliverGrid()
    //     0xa4a3f4: bl              #0xa4a418  ; AllocateSliverGridStub -> SliverGrid (size=0x14)
    // 0xa4a3f8: ldur            x1, [fp, #-0x18]
    // 0xa4a3fc: StoreField: r0->field_f = r1
    //     0xa4a3fc: stur            w1, [x0, #0xf]
    // 0xa4a400: ldur            x1, [fp, #-0x20]
    // 0xa4a404: StoreField: r0->field_b = r1
    //     0xa4a404: stur            w1, [x0, #0xb]
    // 0xa4a408: LeaveFrame
    //     0xa4a408: mov             SP, fp
    //     0xa4a40c: ldp             fp, lr, [SP], #0x10
    // 0xa4a410: ret
    //     0xa4a410: ret             
    // 0xa4a414: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a414: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget _itemBuilder(dynamic, BuildContext, int) {
    // ** addr: 0xa4a424, size: 0x40
    // 0xa4a424: EnterFrame
    //     0xa4a424: stp             fp, lr, [SP, #-0x10]!
    //     0xa4a428: mov             fp, SP
    // 0xa4a42c: ldr             x0, [fp, #0x20]
    // 0xa4a430: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4a430: ldur            w1, [x0, #0x17]
    // 0xa4a434: DecompressPointer r1
    //     0xa4a434: add             x1, x1, HEAP, lsl #32
    // 0xa4a438: CheckStackOverflow
    //     0xa4a438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4a43c: cmp             SP, x16
    //     0xa4a440: b.ls            #0xa4a45c
    // 0xa4a444: ldr             x2, [fp, #0x18]
    // 0xa4a448: ldr             x3, [fp, #0x10]
    // 0xa4a44c: r0 = _itemBuilder()
    //     0xa4a44c: bl              #0xa4a464  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_itemBuilder
    // 0xa4a450: LeaveFrame
    //     0xa4a450: mov             SP, fp
    //     0xa4a454: ldp             fp, lr, [SP], #0x10
    // 0xa4a458: ret
    //     0xa4a458: ret             
    // 0xa4a45c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a45c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a460: b               #0xa4a444
  }
  _ _itemBuilder(/* No info */) {
    // ** addr: 0xa4a464, size: 0x244
    // 0xa4a464: EnterFrame
    //     0xa4a464: stp             fp, lr, [SP, #-0x10]!
    //     0xa4a468: mov             fp, SP
    // 0xa4a46c: AllocStack(0x40)
    //     0xa4a46c: sub             SP, SP, #0x40
    // 0xa4a470: SetupParameters(SliverReorderableGridState this /* r1 => r5, fp-0x18 */, dynamic _ /* r2 => r4, fp-0x20 */, dynamic _ /* r3 => r0, fp-0x28 */)
    //     0xa4a470: mov             x5, x1
    //     0xa4a474: mov             x4, x2
    //     0xa4a478: mov             x0, x3
    //     0xa4a47c: stur            x1, [fp, #-0x18]
    //     0xa4a480: stur            x2, [fp, #-0x20]
    //     0xa4a484: stur            x3, [fp, #-0x28]
    // 0xa4a488: CheckStackOverflow
    //     0xa4a488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4a48c: cmp             SP, x16
    //     0xa4a490: b.ls            #0xa4a650
    // 0xa4a494: LoadField: r1 = r5->field_27
    //     0xa4a494: ldur            w1, [x5, #0x27]
    // 0xa4a498: DecompressPointer r1
    //     0xa4a498: add             x1, x1, HEAP, lsl #32
    // 0xa4a49c: cmp             w1, NULL
    // 0xa4a4a0: b.eq            #0xa4a568
    // 0xa4a4a4: LoadField: r2 = r5->field_b
    //     0xa4a4a4: ldur            w2, [x5, #0xb]
    // 0xa4a4a8: DecompressPointer r2
    //     0xa4a4a8: add             x2, x2, HEAP, lsl #32
    // 0xa4a4ac: cmp             w2, NULL
    // 0xa4a4b0: b.eq            #0xa4a658
    // 0xa4a4b4: LoadField: r3 = r2->field_f
    //     0xa4a4b4: ldur            x3, [x2, #0xf]
    // 0xa4a4b8: r2 = LoadInt32Instr(r0)
    //     0xa4a4b8: sbfx            x2, x0, #1, #0x1f
    //     0xa4a4bc: tbz             w0, #0, #0xa4a4c4
    //     0xa4a4c0: ldur            x2, [x0, #7]
    // 0xa4a4c4: cmp             x2, x3
    // 0xa4a4c8: b.lt            #0xa4a568
    // 0xa4a4cc: LoadField: r0 = r1->field_33
    //     0xa4a4cc: ldur            w0, [x1, #0x33]
    // 0xa4a4d0: DecompressPointer r0
    //     0xa4a4d0: add             x0, x0, HEAP, lsl #32
    // 0xa4a4d4: r16 = Sentinel
    //     0xa4a4d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4a4d8: cmp             w0, w16
    // 0xa4a4dc: b.eq            #0xa4a65c
    // 0xa4a4e0: stur            x0, [fp, #-0x10]
    // 0xa4a4e4: LoadField: d0 = r0->field_7
    //     0xa4a4e4: ldur            d0, [x0, #7]
    // 0xa4a4e8: r1 = inline_Allocate_Double()
    //     0xa4a4e8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xa4a4ec: add             x1, x1, #0x10
    //     0xa4a4f0: cmp             x2, x1
    //     0xa4a4f4: b.ls            #0xa4a668
    //     0xa4a4f8: str             x1, [THR, #0x50]  ; THR::top
    //     0xa4a4fc: sub             x1, x1, #0xf
    //     0xa4a500: movz            x2, #0xe15c
    //     0xa4a504: movk            x2, #0x3, lsl #16
    //     0xa4a508: stur            x2, [x1, #-1]
    // 0xa4a50c: StoreField: r1->field_7 = d0
    //     0xa4a50c: stur            d0, [x1, #7]
    // 0xa4a510: stur            x1, [fp, #-8]
    // 0xa4a514: r0 = SizedBox()
    //     0xa4a514: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa4a518: mov             x1, x0
    // 0xa4a51c: ldur            x0, [fp, #-8]
    // 0xa4a520: StoreField: r1->field_f = r0
    //     0xa4a520: stur            w0, [x1, #0xf]
    // 0xa4a524: ldur            x0, [fp, #-0x10]
    // 0xa4a528: LoadField: d0 = r0->field_f
    //     0xa4a528: ldur            d0, [x0, #0xf]
    // 0xa4a52c: r0 = inline_Allocate_Double()
    //     0xa4a52c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa4a530: add             x0, x0, #0x10
    //     0xa4a534: cmp             x2, x0
    //     0xa4a538: b.ls            #0xa4a684
    //     0xa4a53c: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4a540: sub             x0, x0, #0xf
    //     0xa4a544: movz            x2, #0xe15c
    //     0xa4a548: movk            x2, #0x3, lsl #16
    //     0xa4a54c: stur            x2, [x0, #-1]
    // 0xa4a550: StoreField: r0->field_7 = d0
    //     0xa4a550: stur            d0, [x0, #7]
    // 0xa4a554: StoreField: r1->field_13 = r0
    //     0xa4a554: stur            w0, [x1, #0x13]
    // 0xa4a558: mov             x0, x1
    // 0xa4a55c: LeaveFrame
    //     0xa4a55c: mov             SP, fp
    //     0xa4a560: ldp             fp, lr, [SP], #0x10
    // 0xa4a564: ret
    //     0xa4a564: ret             
    // 0xa4a568: LoadField: r1 = r5->field_b
    //     0xa4a568: ldur            w1, [x5, #0xb]
    // 0xa4a56c: DecompressPointer r1
    //     0xa4a56c: add             x1, x1, HEAP, lsl #32
    // 0xa4a570: cmp             w1, NULL
    // 0xa4a574: b.eq            #0xa4a69c
    // 0xa4a578: LoadField: r2 = r1->field_b
    //     0xa4a578: ldur            w2, [x1, #0xb]
    // 0xa4a57c: DecompressPointer r2
    //     0xa4a57c: add             x2, x2, HEAP, lsl #32
    // 0xa4a580: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa4a580: ldur            w1, [x2, #0x17]
    // 0xa4a584: DecompressPointer r1
    //     0xa4a584: add             x1, x1, HEAP, lsl #32
    // 0xa4a588: mov             x2, x4
    // 0xa4a58c: mov             x3, x0
    // 0xa4a590: r0 = _itemBuilder()
    //     0xa4a590: bl              #0xa4a724  ; [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridViewState::_itemBuilder
    // 0xa4a594: ldur            x1, [fp, #-0x20]
    // 0xa4a598: stur            x0, [fp, #-8]
    // 0xa4a59c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa4a59c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa4a5a0: r0 = of()
    //     0xa4a5a0: bl              #0x6a5014  ; [package:flutter/src/widgets/overlay.dart] Overlay::of
    // 0xa4a5a4: mov             x2, x0
    // 0xa4a5a8: ldur            x0, [fp, #-8]
    // 0xa4a5ac: stur            x2, [fp, #-0x30]
    // 0xa4a5b0: LoadField: r3 = r0->field_7
    //     0xa4a5b0: ldur            w3, [x0, #7]
    // 0xa4a5b4: DecompressPointer r3
    //     0xa4a5b4: add             x3, x3, HEAP, lsl #32
    // 0xa4a5b8: stur            x3, [fp, #-0x10]
    // 0xa4a5bc: cmp             w3, NULL
    // 0xa4a5c0: b.eq            #0xa4a6a0
    // 0xa4a5c4: r1 = <State<StatefulWidget>>
    //     0xa4a5c4: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa4a5c8: r0 = _ReorderableItemGlobalKey()
    //     0xa4a5c8: bl              #0xa4a718  ; Allocate_ReorderableItemGlobalKeyStub -> _ReorderableItemGlobalKey (size=0x20)
    // 0xa4a5cc: mov             x3, x0
    // 0xa4a5d0: ldur            x0, [fp, #-0x10]
    // 0xa4a5d4: stur            x3, [fp, #-0x40]
    // 0xa4a5d8: StoreField: r3->field_f = r0
    //     0xa4a5d8: stur            w0, [x3, #0xf]
    // 0xa4a5dc: ldur            x1, [fp, #-0x28]
    // 0xa4a5e0: r4 = LoadInt32Instr(r1)
    //     0xa4a5e0: sbfx            x4, x1, #1, #0x1f
    //     0xa4a5e4: tbz             w1, #0, #0xa4a5ec
    //     0xa4a5e8: ldur            x4, [x1, #7]
    // 0xa4a5ec: stur            x4, [fp, #-0x38]
    // 0xa4a5f0: StoreField: r3->field_13 = r4
    //     0xa4a5f0: stur            x4, [x3, #0x13]
    // 0xa4a5f4: ldur            x1, [fp, #-0x18]
    // 0xa4a5f8: StoreField: r3->field_1b = r1
    //     0xa4a5f8: stur            w1, [x3, #0x1b]
    // 0xa4a5fc: StoreField: r3->field_b = r0
    //     0xa4a5fc: stur            w0, [x3, #0xb]
    // 0xa4a600: ldur            x0, [fp, #-0x30]
    // 0xa4a604: LoadField: r2 = r0->field_f
    //     0xa4a604: ldur            w2, [x0, #0xf]
    // 0xa4a608: DecompressPointer r2
    //     0xa4a608: add             x2, x2, HEAP, lsl #32
    // 0xa4a60c: cmp             w2, NULL
    // 0xa4a610: b.eq            #0xa4a6a4
    // 0xa4a614: ldur            x1, [fp, #-0x20]
    // 0xa4a618: r0 = capture()
    //     0xa4a618: bl              #0x6a4738  ; [package:flutter/src/widgets/inherited_theme.dart] InheritedTheme::capture
    // 0xa4a61c: stur            x0, [fp, #-0x10]
    // 0xa4a620: r0 = _ReorderableItem()
    //     0xa4a620: bl              #0xa4a70c  ; Allocate_ReorderableItemStub -> _ReorderableItem (size=0x1c)
    // 0xa4a624: ldur            x1, [fp, #-0x38]
    // 0xa4a628: StoreField: r0->field_b = r1
    //     0xa4a628: stur            x1, [x0, #0xb]
    // 0xa4a62c: ldur            x1, [fp, #-8]
    // 0xa4a630: StoreField: r0->field_13 = r1
    //     0xa4a630: stur            w1, [x0, #0x13]
    // 0xa4a634: ldur            x1, [fp, #-0x10]
    // 0xa4a638: ArrayStore: r0[0] = r1  ; List_4
    //     0xa4a638: stur            w1, [x0, #0x17]
    // 0xa4a63c: ldur            x1, [fp, #-0x40]
    // 0xa4a640: StoreField: r0->field_7 = r1
    //     0xa4a640: stur            w1, [x0, #7]
    // 0xa4a644: LeaveFrame
    //     0xa4a644: mov             SP, fp
    //     0xa4a648: ldp             fp, lr, [SP], #0x10
    // 0xa4a64c: ret
    //     0xa4a64c: ret             
    // 0xa4a650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a650: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a654: b               #0xa4a494
    // 0xa4a658: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a658: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a65c: r9 = itemSize
    //     0xa4a65c: add             x9, PP, #0x51, lsl #12  ; [pp+0x513f8] Field <<EMAIL>>: late (offset: 0x34)
    //     0xa4a660: ldr             x9, [x9, #0x3f8]
    // 0xa4a664: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4a664: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4a668: SaveReg d0
    //     0xa4a668: str             q0, [SP, #-0x10]!
    // 0xa4a66c: SaveReg r0
    //     0xa4a66c: str             x0, [SP, #-8]!
    // 0xa4a670: r0 = AllocateDouble()
    //     0xa4a670: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4a674: mov             x1, x0
    // 0xa4a678: RestoreReg r0
    //     0xa4a678: ldr             x0, [SP], #8
    // 0xa4a67c: RestoreReg d0
    //     0xa4a67c: ldr             q0, [SP], #0x10
    // 0xa4a680: b               #0xa4a50c
    // 0xa4a684: SaveReg d0
    //     0xa4a684: str             q0, [SP, #-0x10]!
    // 0xa4a688: SaveReg r1
    //     0xa4a688: str             x1, [SP, #-8]!
    // 0xa4a68c: r0 = AllocateDouble()
    //     0xa4a68c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4a690: RestoreReg r1
    //     0xa4a690: ldr             x1, [SP], #8
    // 0xa4a694: RestoreReg d0
    //     0xa4a694: ldr             q0, [SP], #0x10
    // 0xa4a698: b               #0xa4a550
    // 0xa4a69c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a69c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a6a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a6a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a6a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a6a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83b90, size: 0x54
    // 0xa83b90: EnterFrame
    //     0xa83b90: stp             fp, lr, [SP, #-0x10]!
    //     0xa83b94: mov             fp, SP
    // 0xa83b98: AllocStack(0x8)
    //     0xa83b98: sub             SP, SP, #8
    // 0xa83b9c: SetupParameters(SliverReorderableGridState this /* r1 => r0, fp-0x8 */)
    //     0xa83b9c: mov             x0, x1
    //     0xa83ba0: stur            x1, [fp, #-8]
    // 0xa83ba4: CheckStackOverflow
    //     0xa83ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83ba8: cmp             SP, x16
    //     0xa83bac: b.ls            #0xa83bdc
    // 0xa83bb0: LoadField: r1 = r0->field_27
    //     0xa83bb0: ldur            w1, [x0, #0x27]
    // 0xa83bb4: DecompressPointer r1
    //     0xa83bb4: add             x1, x1, HEAP, lsl #32
    // 0xa83bb8: cmp             w1, NULL
    // 0xa83bbc: b.eq            #0xa83bc4
    // 0xa83bc0: r0 = dispose()
    //     0xa83bc0: bl              #0x9a1644  ; [package:reorderable_grid/src/reorderable_grid.dart] _DragInfo::dispose
    // 0xa83bc4: ldur            x1, [fp, #-8]
    // 0xa83bc8: r0 = dispose()
    //     0xa83bc8: bl              #0xa83be4  ; [package:reorderable_grid/src/reorderable_grid.dart] _SliverReorderableGridState&State&TickerProviderStateMixin::dispose
    // 0xa83bcc: r0 = Null
    //     0xa83bcc: mov             x0, NULL
    // 0xa83bd0: LeaveFrame
    //     0xa83bd0: mov             SP, fp
    //     0xa83bd4: ldp             fp, lr, [SP], #0x10
    // 0xa83bd8: ret
    //     0xa83bd8: ret             
    // 0xa83bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83bdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83be0: b               #0xa83bb0
  }
  _ startItemDragReorder(/* No info */) {
    // ** addr: 0xbb735c, size: 0x98
    // 0xbb735c: EnterFrame
    //     0xbb735c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb7360: mov             fp, SP
    // 0xbb7364: AllocStack(0x20)
    //     0xbb7364: sub             SP, SP, #0x20
    // 0xbb7368: SetupParameters(SliverReorderableGridState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xbb7368: stur            x1, [fp, #-8]
    //     0xbb736c: stur            x2, [fp, #-0x10]
    //     0xbb7370: stur            x3, [fp, #-0x18]
    //     0xbb7374: stur            x5, [fp, #-0x20]
    // 0xbb7378: CheckStackOverflow
    //     0xbb7378: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb737c: cmp             SP, x16
    //     0xbb7380: b.ls            #0xbb73ec
    // 0xbb7384: r1 = 4
    //     0xbb7384: movz            x1, #0x4
    // 0xbb7388: r0 = AllocateContext()
    //     0xbb7388: bl              #0xec126c  ; AllocateContextStub
    // 0xbb738c: mov             x2, x0
    // 0xbb7390: ldur            x3, [fp, #-8]
    // 0xbb7394: StoreField: r2->field_f = r3
    //     0xbb7394: stur            w3, [x2, #0xf]
    // 0xbb7398: ldur            x0, [fp, #-0x10]
    // 0xbb739c: StoreField: r2->field_13 = r0
    //     0xbb739c: stur            w0, [x2, #0x13]
    // 0xbb73a0: ldur            x4, [fp, #-0x18]
    // 0xbb73a4: r0 = BoxInt64Instr(r4)
    //     0xbb73a4: sbfiz           x0, x4, #1, #0x1f
    //     0xbb73a8: cmp             x4, x0, asr #1
    //     0xbb73ac: b.eq            #0xbb73b8
    //     0xbb73b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb73b4: stur            x4, [x0, #7]
    // 0xbb73b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb73b8: stur            w0, [x2, #0x17]
    // 0xbb73bc: ldur            x0, [fp, #-0x20]
    // 0xbb73c0: StoreField: r2->field_1b = r0
    //     0xbb73c0: stur            w0, [x2, #0x1b]
    // 0xbb73c4: r1 = Function '<anonymous closure>':.
    //     0xbb73c4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51390] AnonymousClosure: (0xbb73f4), in [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::startItemDragReorder (0xbb735c)
    //     0xbb73c8: ldr             x1, [x1, #0x390]
    // 0xbb73cc: r0 = AllocateClosure()
    //     0xbb73cc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb73d0: ldur            x1, [fp, #-8]
    // 0xbb73d4: mov             x2, x0
    // 0xbb73d8: r0 = setState()
    //     0xbb73d8: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb73dc: r0 = Null
    //     0xbb73dc: mov             x0, NULL
    // 0xbb73e0: LeaveFrame
    //     0xbb73e0: mov             SP, fp
    //     0xbb73e4: ldp             fp, lr, [SP], #0x10
    // 0xbb73e8: ret
    //     0xbb73e8: ret             
    // 0xbb73ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb73ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb73f0: b               #0xbb7384
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb73f4, size: 0x158
    // 0xbb73f4: EnterFrame
    //     0xbb73f4: stp             fp, lr, [SP, #-0x10]!
    //     0xbb73f8: mov             fp, SP
    // 0xbb73fc: AllocStack(0x18)
    //     0xbb73fc: sub             SP, SP, #0x18
    // 0xbb7400: SetupParameters()
    //     0xbb7400: ldr             x0, [fp, #0x10]
    //     0xbb7404: ldur            w2, [x0, #0x17]
    //     0xbb7408: add             x2, x2, HEAP, lsl #32
    //     0xbb740c: stur            x2, [fp, #-8]
    // 0xbb7410: CheckStackOverflow
    //     0xbb7410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb7414: cmp             SP, x16
    //     0xbb7418: b.ls            #0xbb7544
    // 0xbb741c: LoadField: r1 = r2->field_f
    //     0xbb741c: ldur            w1, [x2, #0xf]
    // 0xbb7420: DecompressPointer r1
    //     0xbb7420: add             x1, x1, HEAP, lsl #32
    // 0xbb7424: LoadField: r0 = r1->field_27
    //     0xbb7424: ldur            w0, [x1, #0x27]
    // 0xbb7428: DecompressPointer r0
    //     0xbb7428: add             x0, x0, HEAP, lsl #32
    // 0xbb742c: cmp             w0, NULL
    // 0xbb7430: b.eq            #0xbb7438
    // 0xbb7434: r0 = _dragReset()
    //     0xbb7434: bl              #0x9a12ec  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragReset
    // 0xbb7438: ldur            x0, [fp, #-8]
    // 0xbb743c: LoadField: r1 = r0->field_f
    //     0xbb743c: ldur            w1, [x0, #0xf]
    // 0xbb7440: DecompressPointer r1
    //     0xbb7440: add             x1, x1, HEAP, lsl #32
    // 0xbb7444: LoadField: r2 = r1->field_1b
    //     0xbb7444: ldur            w2, [x1, #0x1b]
    // 0xbb7448: DecompressPointer r2
    //     0xbb7448: add             x2, x2, HEAP, lsl #32
    // 0xbb744c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb744c: ldur            w1, [x0, #0x17]
    // 0xbb7450: DecompressPointer r1
    //     0xbb7450: add             x1, x1, HEAP, lsl #32
    // 0xbb7454: mov             x16, x1
    // 0xbb7458: mov             x1, x2
    // 0xbb745c: mov             x2, x16
    // 0xbb7460: r0 = containsKey()
    //     0xbb7460: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xbb7464: tbnz            w0, #4, #0xbb7524
    // 0xbb7468: ldur            x3, [fp, #-8]
    // 0xbb746c: LoadField: r4 = r3->field_f
    //     0xbb746c: ldur            w4, [x3, #0xf]
    // 0xbb7470: DecompressPointer r4
    //     0xbb7470: add             x4, x4, HEAP, lsl #32
    // 0xbb7474: stur            x4, [fp, #-0x18]
    // 0xbb7478: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbb7478: ldur            w0, [x3, #0x17]
    // 0xbb747c: DecompressPointer r0
    //     0xbb747c: add             x0, x0, HEAP, lsl #32
    // 0xbb7480: StoreField: r4->field_23 = r0
    //     0xbb7480: stur            w0, [x4, #0x23]
    //     0xbb7484: tbz             w0, #0, #0xbb74a0
    //     0xbb7488: ldurb           w16, [x4, #-1]
    //     0xbb748c: ldurb           w17, [x0, #-1]
    //     0xbb7490: and             x16, x17, x16, lsr #2
    //     0xbb7494: tst             x16, HEAP, lsr #32
    //     0xbb7498: b.eq            #0xbb74a0
    //     0xbb749c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xbb74a0: LoadField: r0 = r3->field_1b
    //     0xbb74a0: ldur            w0, [x3, #0x1b]
    // 0xbb74a4: DecompressPointer r0
    //     0xbb74a4: add             x0, x0, HEAP, lsl #32
    // 0xbb74a8: mov             x2, x4
    // 0xbb74ac: stur            x0, [fp, #-0x10]
    // 0xbb74b0: r1 = Function '_dragStart@2201143295':.
    //     0xbb74b0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51398] AnonymousClosure: (0xbb754c), in [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragStart (0xbb7588)
    //     0xbb74b4: ldr             x1, [x1, #0x398]
    // 0xbb74b8: r0 = AllocateClosure()
    //     0xbb74b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb74bc: ldur            x3, [fp, #-0x10]
    // 0xbb74c0: ArrayStore: r3[0] = r0  ; List_4
    //     0xbb74c0: stur            w0, [x3, #0x17]
    //     0xbb74c4: ldurb           w16, [x3, #-1]
    //     0xbb74c8: ldurb           w17, [x0, #-1]
    //     0xbb74cc: and             x16, x17, x16, lsr #2
    //     0xbb74d0: tst             x16, HEAP, lsr #32
    //     0xbb74d4: b.eq            #0xbb74dc
    //     0xbb74d8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xbb74dc: ldur            x0, [fp, #-8]
    // 0xbb74e0: LoadField: r2 = r0->field_13
    //     0xbb74e0: ldur            w2, [x0, #0x13]
    // 0xbb74e4: DecompressPointer r2
    //     0xbb74e4: add             x2, x2, HEAP, lsl #32
    // 0xbb74e8: mov             x1, x3
    // 0xbb74ec: r0 = addPointer()
    //     0xbb74ec: bl              #0x802d08  ; [package:flutter/src/gestures/recognizer.dart] GestureRecognizer::addPointer
    // 0xbb74f0: ldur            x0, [fp, #-0x10]
    // 0xbb74f4: ldur            x1, [fp, #-0x18]
    // 0xbb74f8: StoreField: r1->field_33 = r0
    //     0xbb74f8: stur            w0, [x1, #0x33]
    //     0xbb74fc: ldurb           w16, [x1, #-1]
    //     0xbb7500: ldurb           w17, [x0, #-1]
    //     0xbb7504: and             x16, x17, x16, lsr #2
    //     0xbb7508: tst             x16, HEAP, lsr #32
    //     0xbb750c: b.eq            #0xbb7514
    //     0xbb7510: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbb7514: r0 = Null
    //     0xbb7514: mov             x0, NULL
    // 0xbb7518: LeaveFrame
    //     0xbb7518: mov             SP, fp
    //     0xbb751c: ldp             fp, lr, [SP], #0x10
    // 0xbb7520: ret
    //     0xbb7520: ret             
    // 0xbb7524: r0 = _Exception()
    //     0xbb7524: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xbb7528: mov             x1, x0
    // 0xbb752c: r0 = "Attempting to start a drag on a non-visible item"
    //     0xbb752c: add             x0, PP, #0x4f, lsl #12  ; [pp+0x4fcc8] "Attempting to start a drag on a non-visible item"
    //     0xbb7530: ldr             x0, [x0, #0xcc8]
    // 0xbb7534: StoreField: r1->field_7 = r0
    //     0xbb7534: stur            w0, [x1, #7]
    // 0xbb7538: mov             x0, x1
    // 0xbb753c: r0 = Throw()
    //     0xbb753c: bl              #0xec04b8  ; ThrowStub
    // 0xbb7540: brk             #0
    // 0xbb7544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7544: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb7548: b               #0xbb741c
  }
  [closure] Drag? _dragStart(dynamic, Offset) {
    // ** addr: 0xbb754c, size: 0x3c
    // 0xbb754c: EnterFrame
    //     0xbb754c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb7550: mov             fp, SP
    // 0xbb7554: ldr             x0, [fp, #0x18]
    // 0xbb7558: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb7558: ldur            w1, [x0, #0x17]
    // 0xbb755c: DecompressPointer r1
    //     0xbb755c: add             x1, x1, HEAP, lsl #32
    // 0xbb7560: CheckStackOverflow
    //     0xbb7560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb7564: cmp             SP, x16
    //     0xbb7568: b.ls            #0xbb7580
    // 0xbb756c: ldr             x2, [fp, #0x10]
    // 0xbb7570: r0 = _dragStart()
    //     0xbb7570: bl              #0xbb7588  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragStart
    // 0xbb7574: LeaveFrame
    //     0xbb7574: mov             SP, fp
    //     0xbb7578: ldp             fp, lr, [SP], #0x10
    // 0xbb757c: ret
    //     0xbb757c: ret             
    // 0xbb7580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7580: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb7584: b               #0xbb756c
  }
  _ _dragStart(/* No info */) {
    // ** addr: 0xbb7588, size: 0x428
    // 0xbb7588: EnterFrame
    //     0xbb7588: stp             fp, lr, [SP, #-0x10]!
    //     0xbb758c: mov             fp, SP
    // 0xbb7590: AllocStack(0x70)
    //     0xbb7590: sub             SP, SP, #0x70
    // 0xbb7594: SetupParameters(SliverReorderableGridState this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0xbb7594: mov             x4, x1
    //     0xbb7598: mov             x0, x2
    //     0xbb759c: stur            x1, [fp, #-0x18]
    //     0xbb75a0: stur            x2, [fp, #-0x20]
    // 0xbb75a4: CheckStackOverflow
    //     0xbb75a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb75a8: cmp             SP, x16
    //     0xbb75ac: b.ls            #0xbb7984
    // 0xbb75b0: LoadField: r5 = r4->field_1b
    //     0xbb75b0: ldur            w5, [x4, #0x1b]
    // 0xbb75b4: DecompressPointer r5
    //     0xbb75b4: add             x5, x5, HEAP, lsl #32
    // 0xbb75b8: stur            x5, [fp, #-0x10]
    // 0xbb75bc: LoadField: r6 = r5->field_7
    //     0xbb75bc: ldur            w6, [x5, #7]
    // 0xbb75c0: DecompressPointer r6
    //     0xbb75c0: add             x6, x6, HEAP, lsl #32
    // 0xbb75c4: mov             x2, x6
    // 0xbb75c8: stur            x6, [fp, #-8]
    // 0xbb75cc: r1 = Null
    //     0xbb75cc: mov             x1, NULL
    // 0xbb75d0: r3 = <X1>
    //     0xbb75d0: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xbb75d4: r0 = Null
    //     0xbb75d4: mov             x0, NULL
    // 0xbb75d8: cmp             x2, x0
    // 0xbb75dc: b.eq            #0xbb75ec
    // 0xbb75e0: r30 = InstantiateTypeArgumentsStub
    //     0xbb75e0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xbb75e4: LoadField: r30 = r30->field_7
    //     0xbb75e4: ldur            lr, [lr, #7]
    // 0xbb75e8: blr             lr
    // 0xbb75ec: mov             x1, x0
    // 0xbb75f0: r0 = _CompactIterable()
    //     0xbb75f0: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xbb75f4: mov             x3, x0
    // 0xbb75f8: ldur            x0, [fp, #-0x10]
    // 0xbb75fc: stur            x3, [fp, #-0x28]
    // 0xbb7600: StoreField: r3->field_b = r0
    //     0xbb7600: stur            w0, [x3, #0xb]
    // 0xbb7604: r4 = -1
    //     0xbb7604: movn            x4, #0
    // 0xbb7608: StoreField: r3->field_f = r4
    //     0xbb7608: stur            x4, [x3, #0xf]
    // 0xbb760c: r5 = 2
    //     0xbb760c: movz            x5, #0x2
    // 0xbb7610: ArrayStore: r3[0] = r5  ; List_8
    //     0xbb7610: stur            x5, [x3, #0x17]
    // 0xbb7614: r1 = Function '<anonymous closure>':.
    //     0xbb7614: add             x1, PP, #0x51, lsl #12  ; [pp+0x513a0] AnonymousClosure: static (0xc4169c), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::onTertiaryFixedVariant (0x636dc4)
    //     0xbb7618: ldr             x1, [x1, #0x3a0]
    // 0xbb761c: r2 = Null
    //     0xbb761c: mov             x2, NULL
    // 0xbb7620: r0 = AllocateClosure()
    //     0xbb7620: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb7624: ldur            x1, [fp, #-0x28]
    // 0xbb7628: mov             x2, x0
    // 0xbb762c: r0 = any()
    //     0xbb762c: bl              #0x7586dc  ; [dart:core] Iterable::any
    // 0xbb7630: tbnz            w0, #4, #0xbb7644
    // 0xbb7634: r0 = Null
    //     0xbb7634: mov             x0, NULL
    // 0xbb7638: LeaveFrame
    //     0xbb7638: mov             SP, fp
    //     0xbb763c: ldp             fp, lr, [SP], #0x10
    // 0xbb7640: ret
    //     0xbb7640: ret             
    // 0xbb7644: ldur            x3, [fp, #-0x18]
    // 0xbb7648: ldur            x0, [fp, #-0x10]
    // 0xbb764c: LoadField: r2 = r3->field_23
    //     0xbb764c: ldur            w2, [x3, #0x23]
    // 0xbb7650: DecompressPointer r2
    //     0xbb7650: add             x2, x2, HEAP, lsl #32
    // 0xbb7654: cmp             w2, NULL
    // 0xbb7658: b.eq            #0xbb798c
    // 0xbb765c: mov             x1, x0
    // 0xbb7660: r0 = _getValueOrData()
    //     0xbb7660: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xbb7664: mov             x1, x0
    // 0xbb7668: ldur            x0, [fp, #-0x10]
    // 0xbb766c: LoadField: r2 = r0->field_f
    //     0xbb766c: ldur            w2, [x0, #0xf]
    // 0xbb7670: DecompressPointer r2
    //     0xbb7670: add             x2, x2, HEAP, lsl #32
    // 0xbb7674: cmp             w2, w1
    // 0xbb7678: b.ne            #0xbb7684
    // 0xbb767c: r4 = Null
    //     0xbb767c: mov             x4, NULL
    // 0xbb7680: b               #0xbb7688
    // 0xbb7684: mov             x4, x1
    // 0xbb7688: ldur            x3, [fp, #-0x18]
    // 0xbb768c: stur            x4, [fp, #-0x28]
    // 0xbb7690: cmp             w4, NULL
    // 0xbb7694: b.eq            #0xbb7990
    // 0xbb7698: mov             x1, x4
    // 0xbb769c: r2 = true
    //     0xbb769c: add             x2, NULL, #0x20  ; true
    // 0xbb76a0: r0 = dragging=()
    //     0xbb76a0: bl              #0x97e9a4  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::dragging=
    // 0xbb76a4: ldur            x1, [fp, #-0x28]
    // 0xbb76a8: r0 = rebuild()
    //     0xbb76a8: bl              #0x97e908  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::rebuild
    // 0xbb76ac: ldur            x3, [fp, #-0x18]
    // 0xbb76b0: LoadField: r2 = r3->field_b
    //     0xbb76b0: ldur            w2, [x3, #0xb]
    // 0xbb76b4: DecompressPointer r2
    //     0xbb76b4: add             x2, x2, HEAP, lsl #32
    // 0xbb76b8: cmp             w2, NULL
    // 0xbb76bc: b.eq            #0xbb7994
    // 0xbb76c0: ldur            x4, [fp, #-0x28]
    // 0xbb76c4: LoadField: r0 = r4->field_b
    //     0xbb76c4: ldur            w0, [x4, #0xb]
    // 0xbb76c8: DecompressPointer r0
    //     0xbb76c8: add             x0, x0, HEAP, lsl #32
    // 0xbb76cc: cmp             w0, NULL
    // 0xbb76d0: b.eq            #0xbb7998
    // 0xbb76d4: LoadField: r5 = r0->field_b
    //     0xbb76d4: ldur            x5, [x0, #0xb]
    // 0xbb76d8: r0 = BoxInt64Instr(r5)
    //     0xbb76d8: sbfiz           x0, x5, #1, #0x1f
    //     0xbb76dc: cmp             x5, x0, asr #1
    //     0xbb76e0: b.eq            #0xbb76ec
    //     0xbb76e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb76e8: stur            x5, [x0, #7]
    // 0xbb76ec: StoreField: r3->field_2b = r0
    //     0xbb76ec: stur            w0, [x3, #0x2b]
    //     0xbb76f0: tbz             w0, #0, #0xbb770c
    //     0xbb76f4: ldurb           w16, [x3, #-1]
    //     0xbb76f8: ldurb           w17, [x0, #-1]
    //     0xbb76fc: and             x16, x17, x16, lsr #2
    //     0xbb7700: tst             x16, HEAP, lsr #32
    //     0xbb7704: b.eq            #0xbb770c
    //     0xbb7708: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xbb770c: LoadField: r0 = r2->field_1f
    //     0xbb770c: ldur            w0, [x2, #0x1f]
    // 0xbb7710: DecompressPointer r0
    //     0xbb7710: add             x0, x0, HEAP, lsl #32
    // 0xbb7714: mov             x2, x3
    // 0xbb7718: stur            x0, [fp, #-0x30]
    // 0xbb771c: r1 = Function '_dragUpdate@2201143295':.
    //     0xbb771c: add             x1, PP, #0x51, lsl #12  ; [pp+0x513a8] AnonymousClosure: (0xbb8a58), in [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragUpdate (0xbb8a9c)
    //     0xbb7720: ldr             x1, [x1, #0x3a8]
    // 0xbb7724: r0 = AllocateClosure()
    //     0xbb7724: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb7728: ldur            x2, [fp, #-0x18]
    // 0xbb772c: r1 = Function '_dragCancel@2201143295':.
    //     0xbb772c: add             x1, PP, #0x51, lsl #12  ; [pp+0x513b0] AnonymousClosure: (0xbb8a1c), of [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState
    //     0xbb7730: ldr             x1, [x1, #0x3b0]
    // 0xbb7734: stur            x0, [fp, #-0x38]
    // 0xbb7738: r0 = AllocateClosure()
    //     0xbb7738: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb773c: ldur            x2, [fp, #-0x18]
    // 0xbb7740: r1 = Function '_dragEnd@2201143295':.
    //     0xbb7740: add             x1, PP, #0x51, lsl #12  ; [pp+0x513b8] AnonymousClosure: (0xbb88e8), in [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragEnd (0xbb8924)
    //     0xbb7744: ldr             x1, [x1, #0x3b8]
    // 0xbb7748: stur            x0, [fp, #-0x40]
    // 0xbb774c: r0 = AllocateClosure()
    //     0xbb774c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb7750: ldur            x2, [fp, #-0x18]
    // 0xbb7754: r1 = Function '_dropCompleted@2201143295':.
    //     0xbb7754: add             x1, PP, #0x51, lsl #12  ; [pp+0x513c0] AnonymousClosure: (0xbb88b0), in [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dropCompleted (0xbb81d8)
    //     0xbb7758: ldr             x1, [x1, #0x3c0]
    // 0xbb775c: stur            x0, [fp, #-0x48]
    // 0xbb7760: r0 = AllocateClosure()
    //     0xbb7760: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb7764: stur            x0, [fp, #-0x50]
    // 0xbb7768: r0 = _DragInfo()
    //     0xbb7768: bl              #0xbb85e8  ; Allocate_DragInfoStub -> _DragInfo (size=0x44)
    // 0xbb776c: stur            x0, [fp, #-0x58]
    // 0xbb7770: ldur            x16, [fp, #-0x38]
    // 0xbb7774: ldur            lr, [fp, #-0x30]
    // 0xbb7778: stp             lr, x16, [SP, #8]
    // 0xbb777c: ldur            x16, [fp, #-0x18]
    // 0xbb7780: str             x16, [SP]
    // 0xbb7784: mov             x1, x0
    // 0xbb7788: ldur            x2, [fp, #-0x20]
    // 0xbb778c: ldur            x3, [fp, #-0x28]
    // 0xbb7790: ldur            x5, [fp, #-0x40]
    // 0xbb7794: ldur            x6, [fp, #-0x50]
    // 0xbb7798: ldur            x7, [fp, #-0x48]
    // 0xbb779c: r0 = _DragInfo()
    //     0xbb779c: bl              #0xbb8288  ; [package:reorderable_grid/src/reorderable_grid.dart] _DragInfo::_DragInfo
    // 0xbb77a0: ldur            x0, [fp, #-0x58]
    // 0xbb77a4: ldur            x2, [fp, #-0x18]
    // 0xbb77a8: StoreField: r2->field_27 = r0
    //     0xbb77a8: stur            w0, [x2, #0x27]
    //     0xbb77ac: ldurb           w16, [x2, #-1]
    //     0xbb77b0: ldurb           w17, [x0, #-1]
    //     0xbb77b4: and             x16, x17, x16, lsr #2
    //     0xbb77b8: tst             x16, HEAP, lsr #32
    //     0xbb77bc: b.eq            #0xbb77c4
    //     0xbb77c0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbb77c4: ldur            x1, [fp, #-0x58]
    // 0xbb77c8: r0 = startDrag()
    //     0xbb77c8: bl              #0xbb8038  ; [package:reorderable_grid/src/reorderable_grid.dart] _DragInfo::startDrag
    // 0xbb77cc: ldur            x0, [fp, #-0x18]
    // 0xbb77d0: LoadField: r1 = r0->field_f
    //     0xbb77d0: ldur            w1, [x0, #0xf]
    // 0xbb77d4: DecompressPointer r1
    //     0xbb77d4: add             x1, x1, HEAP, lsl #32
    // 0xbb77d8: cmp             w1, NULL
    // 0xbb77dc: b.eq            #0xbb799c
    // 0xbb77e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb77e0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb77e4: r0 = of()
    //     0xbb77e4: bl              #0x6a5014  ; [package:flutter/src/widgets/overlay.dart] Overlay::of
    // 0xbb77e8: mov             x3, x0
    // 0xbb77ec: ldur            x0, [fp, #-0x18]
    // 0xbb77f0: stur            x3, [fp, #-0x20]
    // 0xbb77f4: LoadField: r2 = r0->field_27
    //     0xbb77f4: ldur            w2, [x0, #0x27]
    // 0xbb77f8: DecompressPointer r2
    //     0xbb77f8: add             x2, x2, HEAP, lsl #32
    // 0xbb77fc: cmp             w2, NULL
    // 0xbb7800: b.eq            #0xbb79a0
    // 0xbb7804: r1 = Function 'createProxy':.
    //     0xbb7804: add             x1, PP, #0x51, lsl #12  ; [pp+0x513c8] AnonymousClosure: (0xbb85f4), in [package:reorderable_grid/src/reorderable_grid.dart] _DragInfo::createProxy (0xbb8630)
    //     0xbb7808: ldr             x1, [x1, #0x3c8]
    // 0xbb780c: r0 = AllocateClosure()
    //     0xbb780c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb7810: stur            x0, [fp, #-0x30]
    // 0xbb7814: r0 = OverlayEntry()
    //     0xbb7814: bl              #0x6a5798  ; AllocateOverlayEntryStub -> OverlayEntry (size=0x28)
    // 0xbb7818: mov             x1, x0
    // 0xbb781c: ldur            x2, [fp, #-0x30]
    // 0xbb7820: stur            x0, [fp, #-0x30]
    // 0xbb7824: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb7824: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb7828: r0 = OverlayEntry()
    //     0xbb7828: bl              #0x6a55c8  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::OverlayEntry
    // 0xbb782c: ldur            x0, [fp, #-0x30]
    // 0xbb7830: ldur            x3, [fp, #-0x18]
    // 0xbb7834: StoreField: r3->field_1f = r0
    //     0xbb7834: stur            w0, [x3, #0x1f]
    //     0xbb7838: ldurb           w16, [x3, #-1]
    //     0xbb783c: ldurb           w17, [x0, #-1]
    //     0xbb7840: and             x16, x17, x16, lsr #2
    //     0xbb7844: tst             x16, HEAP, lsr #32
    //     0xbb7848: b.eq            #0xbb7850
    //     0xbb784c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xbb7850: ldur            x1, [fp, #-0x20]
    // 0xbb7854: ldur            x2, [fp, #-0x30]
    // 0xbb7858: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb7858: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb785c: r0 = insert()
    //     0xbb785c: bl              #0x6a4e34  ; [package:flutter/src/widgets/overlay.dart] OverlayState::insert
    // 0xbb7860: ldur            x2, [fp, #-8]
    // 0xbb7864: r1 = Null
    //     0xbb7864: mov             x1, NULL
    // 0xbb7868: r3 = <X1>
    //     0xbb7868: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xbb786c: r0 = Null
    //     0xbb786c: mov             x0, NULL
    // 0xbb7870: cmp             x2, x0
    // 0xbb7874: b.eq            #0xbb7884
    // 0xbb7878: r30 = InstantiateTypeArgumentsStub
    //     0xbb7878: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xbb787c: LoadField: r30 = r30->field_7
    //     0xbb787c: ldur            lr, [lr, #7]
    // 0xbb7880: blr             lr
    // 0xbb7884: mov             x1, x0
    // 0xbb7888: r0 = _CompactIterable()
    //     0xbb7888: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xbb788c: mov             x1, x0
    // 0xbb7890: ldur            x0, [fp, #-0x10]
    // 0xbb7894: StoreField: r1->field_b = r0
    //     0xbb7894: stur            w0, [x1, #0xb]
    // 0xbb7898: r0 = -1
    //     0xbb7898: movn            x0, #0
    // 0xbb789c: StoreField: r1->field_f = r0
    //     0xbb789c: stur            x0, [x1, #0xf]
    // 0xbb78a0: r0 = 2
    //     0xbb78a0: movz            x0, #0x2
    // 0xbb78a4: ArrayStore: r1[0] = r0  ; List_8
    //     0xbb78a4: stur            x0, [x1, #0x17]
    // 0xbb78a8: r0 = iterator()
    //     0xbb78a8: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0xbb78ac: stur            x0, [fp, #-0x10]
    // 0xbb78b0: LoadField: r2 = r0->field_7
    //     0xbb78b0: ldur            w2, [x0, #7]
    // 0xbb78b4: DecompressPointer r2
    //     0xbb78b4: add             x2, x2, HEAP, lsl #32
    // 0xbb78b8: stur            x2, [fp, #-8]
    // 0xbb78bc: ldur            x3, [fp, #-0x18]
    // 0xbb78c0: ldur            x4, [fp, #-0x28]
    // 0xbb78c4: CheckStackOverflow
    //     0xbb78c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb78c8: cmp             SP, x16
    //     0xbb78cc: b.ls            #0xbb79a4
    // 0xbb78d0: mov             x1, x0
    // 0xbb78d4: r0 = moveNext()
    //     0xbb78d4: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xbb78d8: tbnz            w0, #4, #0xbb796c
    // 0xbb78dc: ldur            x3, [fp, #-0x10]
    // 0xbb78e0: LoadField: r4 = r3->field_33
    //     0xbb78e0: ldur            w4, [x3, #0x33]
    // 0xbb78e4: DecompressPointer r4
    //     0xbb78e4: add             x4, x4, HEAP, lsl #32
    // 0xbb78e8: stur            x4, [fp, #-0x20]
    // 0xbb78ec: cmp             w4, NULL
    // 0xbb78f0: b.ne            #0xbb7924
    // 0xbb78f4: mov             x0, x4
    // 0xbb78f8: ldur            x2, [fp, #-8]
    // 0xbb78fc: r1 = Null
    //     0xbb78fc: mov             x1, NULL
    // 0xbb7900: cmp             w2, NULL
    // 0xbb7904: b.eq            #0xbb7924
    // 0xbb7908: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbb7908: ldur            w4, [x2, #0x17]
    // 0xbb790c: DecompressPointer r4
    //     0xbb790c: add             x4, x4, HEAP, lsl #32
    // 0xbb7910: r8 = X0
    //     0xbb7910: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xbb7914: LoadField: r9 = r4->field_7
    //     0xbb7914: ldur            x9, [x4, #7]
    // 0xbb7918: r3 = Null
    //     0xbb7918: add             x3, PP, #0x51, lsl #12  ; [pp+0x513d0] Null
    //     0xbb791c: ldr             x3, [x3, #0x3d0]
    // 0xbb7920: blr             x9
    // 0xbb7924: ldur            x1, [fp, #-0x20]
    // 0xbb7928: ldur            x0, [fp, #-0x28]
    // 0xbb792c: cmp             w1, w0
    // 0xbb7930: b.eq            #0xbb7960
    // 0xbb7934: LoadField: r2 = r1->field_f
    //     0xbb7934: ldur            w2, [x1, #0xf]
    // 0xbb7938: DecompressPointer r2
    //     0xbb7938: add             x2, x2, HEAP, lsl #32
    // 0xbb793c: cmp             w2, NULL
    // 0xbb7940: b.eq            #0xbb7960
    // 0xbb7944: ldur            x3, [fp, #-0x18]
    // 0xbb7948: LoadField: r2 = r3->field_2b
    //     0xbb7948: ldur            w2, [x3, #0x2b]
    // 0xbb794c: DecompressPointer r2
    //     0xbb794c: add             x2, x2, HEAP, lsl #32
    // 0xbb7950: cmp             w2, NULL
    // 0xbb7954: b.eq            #0xbb79ac
    // 0xbb7958: r2 = false
    //     0xbb7958: add             x2, NULL, #0x30  ; false
    // 0xbb795c: r0 = updateForGap()
    //     0xbb795c: bl              #0xbb79b0  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::updateForGap
    // 0xbb7960: ldur            x0, [fp, #-0x10]
    // 0xbb7964: ldur            x2, [fp, #-8]
    // 0xbb7968: b               #0xbb78bc
    // 0xbb796c: ldur            x1, [fp, #-0x18]
    // 0xbb7970: LoadField: r0 = r1->field_27
    //     0xbb7970: ldur            w0, [x1, #0x27]
    // 0xbb7974: DecompressPointer r0
    //     0xbb7974: add             x0, x0, HEAP, lsl #32
    // 0xbb7978: LeaveFrame
    //     0xbb7978: mov             SP, fp
    //     0xbb797c: ldp             fp, lr, [SP], #0x10
    // 0xbb7980: ret
    //     0xbb7980: ret             
    // 0xbb7984: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7984: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb7988: b               #0xbb75b0
    // 0xbb798c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb798c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7990: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7990: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7994: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7994: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7998: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7998: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb799c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb799c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb79a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb79a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb79a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb79a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb79a8: b               #0xbb78d0
    // 0xbb79ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb79ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _calculateNextDragOffset(/* No info */) {
    // ** addr: 0xbb7c18, size: 0x31c
    // 0xbb7c18: EnterFrame
    //     0xbb7c18: stp             fp, lr, [SP, #-0x10]!
    //     0xbb7c1c: mov             fp, SP
    // 0xbb7c20: AllocStack(0x30)
    //     0xbb7c20: sub             SP, SP, #0x30
    // 0xbb7c24: SetupParameters(SliverReorderableGridState this /* r1 => r3, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */)
    //     0xbb7c24: mov             x3, x1
    //     0xbb7c28: stur            x1, [fp, #-0x20]
    //     0xbb7c2c: stur            x2, [fp, #-0x28]
    // 0xbb7c30: CheckStackOverflow
    //     0xbb7c30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb7c34: cmp             SP, x16
    //     0xbb7c38: b.ls            #0xbb7f1c
    // 0xbb7c3c: LoadField: r4 = r3->field_23
    //     0xbb7c3c: ldur            w4, [x3, #0x23]
    // 0xbb7c40: DecompressPointer r4
    //     0xbb7c40: add             x4, x4, HEAP, lsl #32
    // 0xbb7c44: stur            x4, [fp, #-0x18]
    // 0xbb7c48: cmp             w4, NULL
    // 0xbb7c4c: b.eq            #0xbb7f24
    // 0xbb7c50: LoadField: r5 = r3->field_2b
    //     0xbb7c50: ldur            w5, [x3, #0x2b]
    // 0xbb7c54: DecompressPointer r5
    //     0xbb7c54: add             x5, x5, HEAP, lsl #32
    // 0xbb7c58: stur            x5, [fp, #-0x10]
    // 0xbb7c5c: cmp             w5, NULL
    // 0xbb7c60: b.eq            #0xbb7f28
    // 0xbb7c64: r6 = LoadInt32Instr(r4)
    //     0xbb7c64: sbfx            x6, x4, #1, #0x1f
    //     0xbb7c68: tbz             w4, #0, #0xbb7c70
    //     0xbb7c6c: ldur            x6, [x4, #7]
    // 0xbb7c70: r7 = LoadInt32Instr(r5)
    //     0xbb7c70: sbfx            x7, x5, #1, #0x1f
    //     0xbb7c74: tbz             w5, #0, #0xbb7c7c
    //     0xbb7c78: ldur            x7, [x5, #7]
    // 0xbb7c7c: cmp             x6, x7
    // 0xbb7c80: b.le            #0xbb7c90
    // 0xbb7c84: mov             x8, x5
    // 0xbb7c88: d0 = 0.000000
    //     0xbb7c88: eor             v0.16b, v0.16b, v0.16b
    // 0xbb7c8c: b               #0xbb7d54
    // 0xbb7c90: cmp             x6, x7
    // 0xbb7c94: b.ge            #0xbb7ca4
    // 0xbb7c98: mov             x8, x4
    // 0xbb7c9c: d0 = 0.000000
    //     0xbb7c9c: eor             v0.16b, v0.16b, v0.16b
    // 0xbb7ca0: b               #0xbb7d54
    // 0xbb7ca4: r0 = 60
    //     0xbb7ca4: movz            x0, #0x3c
    // 0xbb7ca8: branchIfSmi(r5, 0xbb7cb4)
    //     0xbb7ca8: tbz             w5, #0, #0xbb7cb4
    // 0xbb7cac: r0 = LoadClassIdInstr(r5)
    //     0xbb7cac: ldur            x0, [x5, #-1]
    //     0xbb7cb0: ubfx            x0, x0, #0xc, #0x14
    // 0xbb7cb4: cmp             x0, #0x3e
    // 0xbb7cb8: b.ne            #0xbb7d4c
    // 0xbb7cbc: r0 = 60
    //     0xbb7cbc: movz            x0, #0x3c
    // 0xbb7cc0: branchIfSmi(r4, 0xbb7ccc)
    //     0xbb7cc0: tbz             w4, #0, #0xbb7ccc
    // 0xbb7cc4: r0 = LoadClassIdInstr(r4)
    //     0xbb7cc4: ldur            x0, [x4, #-1]
    //     0xbb7cc8: ubfx            x0, x0, #0xc, #0x14
    // 0xbb7ccc: cmp             x0, #0x3e
    // 0xbb7cd0: b.ne            #0xbb7d0c
    // 0xbb7cd4: d0 = 0.000000
    //     0xbb7cd4: eor             v0.16b, v0.16b, v0.16b
    // 0xbb7cd8: scvtf           d1, x6
    // 0xbb7cdc: fcmp            d1, d0
    // 0xbb7ce0: b.ne            #0xbb7d10
    // 0xbb7ce4: add             x0, x6, x7
    // 0xbb7ce8: mul             x1, x0, x6
    // 0xbb7cec: mul             x8, x1, x7
    // 0xbb7cf0: r0 = BoxInt64Instr(r8)
    //     0xbb7cf0: sbfiz           x0, x8, #1, #0x1f
    //     0xbb7cf4: cmp             x8, x0, asr #1
    //     0xbb7cf8: b.eq            #0xbb7d04
    //     0xbb7cfc: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0xbb7d00: stur            x8, [x0, #7]
    // 0xbb7d04: mov             x8, x0
    // 0xbb7d08: b               #0xbb7d54
    // 0xbb7d0c: d0 = 0.000000
    //     0xbb7d0c: eor             v0.16b, v0.16b, v0.16b
    // 0xbb7d10: cbnz            x6, #0xbb7d30
    // 0xbb7d14: LoadField: d1 = r5->field_7
    //     0xbb7d14: ldur            d1, [x5, #7]
    // 0xbb7d18: fcmp            d1, #0.0
    // 0xbb7d1c: b.vs            #0xbb7d30
    // 0xbb7d20: b.ne            #0xbb7d2c
    // 0xbb7d24: r0 = 0.000000
    //     0xbb7d24: fmov            x0, d1
    // 0xbb7d28: cmp             x0, #0
    // 0xbb7d2c: b.lt            #0xbb7d3c
    // 0xbb7d30: LoadField: d1 = r5->field_7
    //     0xbb7d30: ldur            d1, [x5, #7]
    // 0xbb7d34: fcmp            d1, d1
    // 0xbb7d38: b.vc            #0xbb7d44
    // 0xbb7d3c: mov             x8, x5
    // 0xbb7d40: b               #0xbb7d54
    // 0xbb7d44: mov             x8, x4
    // 0xbb7d48: b               #0xbb7d54
    // 0xbb7d4c: d0 = 0.000000
    //     0xbb7d4c: eor             v0.16b, v0.16b, v0.16b
    // 0xbb7d50: mov             x8, x4
    // 0xbb7d54: stur            x8, [fp, #-8]
    // 0xbb7d58: cmp             x6, x7
    // 0xbb7d5c: b.le            #0xbb7d70
    // 0xbb7d60: mov             x0, x2
    // 0xbb7d64: mov             x2, x4
    // 0xbb7d68: mov             x1, x8
    // 0xbb7d6c: b               #0xbb7e60
    // 0xbb7d70: cmp             x6, x7
    // 0xbb7d74: b.ge            #0xbb7d88
    // 0xbb7d78: mov             x0, x2
    // 0xbb7d7c: mov             x2, x5
    // 0xbb7d80: mov             x1, x8
    // 0xbb7d84: b               #0xbb7e60
    // 0xbb7d88: r0 = 60
    //     0xbb7d88: movz            x0, #0x3c
    // 0xbb7d8c: branchIfSmi(r5, 0xbb7d98)
    //     0xbb7d8c: tbz             w5, #0, #0xbb7d98
    // 0xbb7d90: r0 = LoadClassIdInstr(r5)
    //     0xbb7d90: ldur            x0, [x5, #-1]
    //     0xbb7d94: ubfx            x0, x0, #0xc, #0x14
    // 0xbb7d98: cmp             x0, #0x3e
    // 0xbb7d9c: b.ne            #0xbb7e1c
    // 0xbb7da0: r0 = 60
    //     0xbb7da0: movz            x0, #0x3c
    // 0xbb7da4: branchIfSmi(r4, 0xbb7db0)
    //     0xbb7da4: tbz             w4, #0, #0xbb7db0
    // 0xbb7da8: r0 = LoadClassIdInstr(r4)
    //     0xbb7da8: ldur            x0, [x4, #-1]
    //     0xbb7dac: ubfx            x0, x0, #0xc, #0x14
    // 0xbb7db0: cmp             x0, #0x3e
    // 0xbb7db4: b.ne            #0xbb7df0
    // 0xbb7db8: scvtf           d1, x6
    // 0xbb7dbc: fcmp            d1, d0
    // 0xbb7dc0: b.ne            #0xbb7df0
    // 0xbb7dc4: add             x4, x6, x7
    // 0xbb7dc8: r0 = BoxInt64Instr(r4)
    //     0xbb7dc8: sbfiz           x0, x4, #1, #0x1f
    //     0xbb7dcc: cmp             x4, x0, asr #1
    //     0xbb7dd0: b.eq            #0xbb7ddc
    //     0xbb7dd4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb7dd8: stur            x4, [x0, #7]
    // 0xbb7ddc: mov             x16, x2
    // 0xbb7de0: mov             x2, x0
    // 0xbb7de4: mov             x0, x16
    // 0xbb7de8: mov             x1, x8
    // 0xbb7dec: b               #0xbb7e60
    // 0xbb7df0: LoadField: d0 = r5->field_7
    //     0xbb7df0: ldur            d0, [x5, #7]
    // 0xbb7df4: fcmp            d0, d0
    // 0xbb7df8: b.vc            #0xbb7e0c
    // 0xbb7dfc: mov             x0, x2
    // 0xbb7e00: mov             x2, x5
    // 0xbb7e04: mov             x1, x8
    // 0xbb7e08: b               #0xbb7e60
    // 0xbb7e0c: mov             x0, x2
    // 0xbb7e10: mov             x2, x4
    // 0xbb7e14: mov             x1, x8
    // 0xbb7e18: b               #0xbb7e60
    // 0xbb7e1c: cbnz            x7, #0xbb7e54
    // 0xbb7e20: r0 = 60
    //     0xbb7e20: movz            x0, #0x3c
    // 0xbb7e24: branchIfSmi(r4, 0xbb7e30)
    //     0xbb7e24: tbz             w4, #0, #0xbb7e30
    // 0xbb7e28: r0 = LoadClassIdInstr(r4)
    //     0xbb7e28: ldur            x0, [x4, #-1]
    //     0xbb7e2c: ubfx            x0, x0, #0xc, #0x14
    // 0xbb7e30: str             x4, [SP]
    // 0xbb7e34: r0 = GDT[cid_x0 + -0xfb8]()
    //     0xbb7e34: sub             lr, x0, #0xfb8
    //     0xbb7e38: ldr             lr, [x21, lr, lsl #3]
    //     0xbb7e3c: blr             lr
    // 0xbb7e40: tbnz            w0, #4, #0xbb7e54
    // 0xbb7e44: ldur            x2, [fp, #-0x10]
    // 0xbb7e48: ldur            x0, [fp, #-0x28]
    // 0xbb7e4c: ldur            x1, [fp, #-8]
    // 0xbb7e50: b               #0xbb7e60
    // 0xbb7e54: ldur            x2, [fp, #-0x18]
    // 0xbb7e58: ldur            x0, [fp, #-0x28]
    // 0xbb7e5c: ldur            x1, [fp, #-8]
    // 0xbb7e60: r3 = LoadInt32Instr(r1)
    //     0xbb7e60: sbfx            x3, x1, #1, #0x1f
    //     0xbb7e64: tbz             w1, #0, #0xbb7e6c
    //     0xbb7e68: ldur            x3, [x1, #7]
    // 0xbb7e6c: cmp             x0, x3
    // 0xbb7e70: b.lt            #0xbb7e88
    // 0xbb7e74: r1 = LoadInt32Instr(r2)
    //     0xbb7e74: sbfx            x1, x2, #1, #0x1f
    //     0xbb7e78: tbz             w2, #0, #0xbb7e80
    //     0xbb7e7c: ldur            x1, [x2, #7]
    // 0xbb7e80: cmp             x0, x1
    // 0xbb7e84: b.le            #0xbb7e98
    // 0xbb7e88: r0 = Instance_Offset
    //     0xbb7e88: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xbb7e8c: LeaveFrame
    //     0xbb7e8c: mov             SP, fp
    //     0xbb7e90: ldp             fp, lr, [SP], #0x10
    // 0xbb7e94: ret
    //     0xbb7e94: ret             
    // 0xbb7e98: ldur            x3, [fp, #-0x20]
    // 0xbb7e9c: LoadField: r1 = r3->field_2b
    //     0xbb7e9c: ldur            w1, [x3, #0x2b]
    // 0xbb7ea0: DecompressPointer r1
    //     0xbb7ea0: add             x1, x1, HEAP, lsl #32
    // 0xbb7ea4: cmp             w1, NULL
    // 0xbb7ea8: b.eq            #0xbb7f2c
    // 0xbb7eac: LoadField: r2 = r3->field_23
    //     0xbb7eac: ldur            w2, [x3, #0x23]
    // 0xbb7eb0: DecompressPointer r2
    //     0xbb7eb0: add             x2, x2, HEAP, lsl #32
    // 0xbb7eb4: cmp             w2, NULL
    // 0xbb7eb8: b.eq            #0xbb7f30
    // 0xbb7ebc: r4 = LoadInt32Instr(r1)
    //     0xbb7ebc: sbfx            x4, x1, #1, #0x1f
    //     0xbb7ec0: tbz             w1, #0, #0xbb7ec8
    //     0xbb7ec4: ldur            x4, [x1, #7]
    // 0xbb7ec8: r1 = LoadInt32Instr(r2)
    //     0xbb7ec8: sbfx            x1, x2, #1, #0x1f
    //     0xbb7ecc: tbz             w2, #0, #0xbb7ed4
    //     0xbb7ed0: ldur            x1, [x2, #7]
    // 0xbb7ed4: cmp             x4, x1
    // 0xbb7ed8: b.le            #0xbb7ee4
    // 0xbb7edc: r1 = -1
    //     0xbb7edc: movn            x1, #0
    // 0xbb7ee0: b               #0xbb7ee8
    // 0xbb7ee4: r1 = 1
    //     0xbb7ee4: movz            x1, #0x1
    // 0xbb7ee8: add             x2, x0, x1
    // 0xbb7eec: mov             x1, x3
    // 0xbb7ef0: r0 = _itemOffsetAt()
    //     0xbb7ef0: bl              #0xbb7f34  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_itemOffsetAt
    // 0xbb7ef4: ldur            x1, [fp, #-0x20]
    // 0xbb7ef8: ldur            x2, [fp, #-0x28]
    // 0xbb7efc: stur            x0, [fp, #-8]
    // 0xbb7f00: r0 = _itemOffsetAt()
    //     0xbb7f00: bl              #0xbb7f34  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_itemOffsetAt
    // 0xbb7f04: ldur            x1, [fp, #-8]
    // 0xbb7f08: mov             x2, x0
    // 0xbb7f0c: r0 = -()
    //     0xbb7f0c: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xbb7f10: LeaveFrame
    //     0xbb7f10: mov             SP, fp
    //     0xbb7f14: ldp             fp, lr, [SP], #0x10
    // 0xbb7f18: ret
    //     0xbb7f18: ret             
    // 0xbb7f1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7f1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb7f20: b               #0xbb7c3c
    // 0xbb7f24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7f24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7f28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7f28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7f2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7f2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7f30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7f30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _itemOffsetAt(/* No info */) {
    // ** addr: 0xbb7f34, size: 0x104
    // 0xbb7f34: EnterFrame
    //     0xbb7f34: stp             fp, lr, [SP, #-0x10]!
    //     0xbb7f38: mov             fp, SP
    // 0xbb7f3c: AllocStack(0x8)
    //     0xbb7f3c: sub             SP, SP, #8
    // 0xbb7f40: CheckStackOverflow
    //     0xbb7f40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb7f44: cmp             SP, x16
    //     0xbb7f48: b.ls            #0xbb802c
    // 0xbb7f4c: LoadField: r3 = r1->field_1b
    //     0xbb7f4c: ldur            w3, [x1, #0x1b]
    // 0xbb7f50: DecompressPointer r3
    //     0xbb7f50: add             x3, x3, HEAP, lsl #32
    // 0xbb7f54: stur            x3, [fp, #-8]
    // 0xbb7f58: r0 = BoxInt64Instr(r2)
    //     0xbb7f58: sbfiz           x0, x2, #1, #0x1f
    //     0xbb7f5c: cmp             x2, x0, asr #1
    //     0xbb7f60: b.eq            #0xbb7f6c
    //     0xbb7f64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb7f68: stur            x2, [x0, #7]
    // 0xbb7f6c: mov             x1, x3
    // 0xbb7f70: mov             x2, x0
    // 0xbb7f74: r0 = _getValueOrData()
    //     0xbb7f74: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xbb7f78: mov             x1, x0
    // 0xbb7f7c: ldur            x0, [fp, #-8]
    // 0xbb7f80: LoadField: r2 = r0->field_f
    //     0xbb7f80: ldur            w2, [x0, #0xf]
    // 0xbb7f84: DecompressPointer r2
    //     0xbb7f84: add             x2, x2, HEAP, lsl #32
    // 0xbb7f88: cmp             w2, w1
    // 0xbb7f8c: b.ne            #0xbb7f98
    // 0xbb7f90: r0 = Null
    //     0xbb7f90: mov             x0, NULL
    // 0xbb7f94: b               #0xbb7f9c
    // 0xbb7f98: mov             x0, x1
    // 0xbb7f9c: cmp             w0, NULL
    // 0xbb7fa0: b.ne            #0xbb7fac
    // 0xbb7fa4: r3 = Null
    //     0xbb7fa4: mov             x3, NULL
    // 0xbb7fa8: b               #0xbb7fc4
    // 0xbb7fac: LoadField: r1 = r0->field_f
    //     0xbb7fac: ldur            w1, [x0, #0xf]
    // 0xbb7fb0: DecompressPointer r1
    //     0xbb7fb0: add             x1, x1, HEAP, lsl #32
    // 0xbb7fb4: cmp             w1, NULL
    // 0xbb7fb8: b.eq            #0xbb8034
    // 0xbb7fbc: r0 = renderObject()
    //     0xbb7fbc: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xbb7fc0: mov             x3, x0
    // 0xbb7fc4: mov             x0, x3
    // 0xbb7fc8: stur            x3, [fp, #-8]
    // 0xbb7fcc: r2 = Null
    //     0xbb7fcc: mov             x2, NULL
    // 0xbb7fd0: r1 = Null
    //     0xbb7fd0: mov             x1, NULL
    // 0xbb7fd4: r4 = LoadClassIdInstr(r0)
    //     0xbb7fd4: ldur            x4, [x0, #-1]
    //     0xbb7fd8: ubfx            x4, x4, #0xc, #0x14
    // 0xbb7fdc: sub             x4, x4, #0xbba
    // 0xbb7fe0: cmp             x4, #0x9a
    // 0xbb7fe4: b.ls            #0xbb7ff8
    // 0xbb7fe8: r8 = RenderBox?
    //     0xbb7fe8: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0xbb7fec: r3 = Null
    //     0xbb7fec: add             x3, PP, #0x51, lsl #12  ; [pp+0x51430] Null
    //     0xbb7ff0: ldr             x3, [x3, #0x430]
    // 0xbb7ff4: r0 = RenderBox?()
    //     0xbb7ff4: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0xbb7ff8: ldur            x1, [fp, #-8]
    // 0xbb7ffc: cmp             w1, NULL
    // 0xbb8000: b.ne            #0xbb8014
    // 0xbb8004: r0 = Instance_Offset
    //     0xbb8004: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xbb8008: LeaveFrame
    //     0xbb8008: mov             SP, fp
    //     0xbb800c: ldp             fp, lr, [SP], #0x10
    // 0xbb8010: ret
    //     0xbb8010: ret             
    // 0xbb8014: r2 = Instance_Offset
    //     0xbb8014: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xbb8018: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb8018: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb801c: r0 = localToGlobal()
    //     0xbb801c: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0xbb8020: LeaveFrame
    //     0xbb8020: mov             SP, fp
    //     0xbb8024: ldp             fp, lr, [SP], #0x10
    // 0xbb8028: ret
    //     0xbb8028: ret             
    // 0xbb802c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb802c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8030: b               #0xbb7f4c
    // 0xbb8034: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb8034: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _dropCompleted(/* No info */) {
    // ** addr: 0xbb81d8, size: 0xb0
    // 0xbb81d8: EnterFrame
    //     0xbb81d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbb81dc: mov             fp, SP
    // 0xbb81e0: AllocStack(0x8)
    //     0xbb81e0: sub             SP, SP, #8
    // 0xbb81e4: SetupParameters(SliverReorderableGridState this /* r1 => r0, fp-0x8 */)
    //     0xbb81e4: mov             x0, x1
    //     0xbb81e8: stur            x1, [fp, #-8]
    // 0xbb81ec: CheckStackOverflow
    //     0xbb81ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb81f0: cmp             SP, x16
    //     0xbb81f4: b.ls            #0xbb8274
    // 0xbb81f8: LoadField: r2 = r0->field_23
    //     0xbb81f8: ldur            w2, [x0, #0x23]
    // 0xbb81fc: DecompressPointer r2
    //     0xbb81fc: add             x2, x2, HEAP, lsl #32
    // 0xbb8200: cmp             w2, NULL
    // 0xbb8204: b.eq            #0xbb827c
    // 0xbb8208: LoadField: r3 = r0->field_2b
    //     0xbb8208: ldur            w3, [x0, #0x2b]
    // 0xbb820c: DecompressPointer r3
    //     0xbb820c: add             x3, x3, HEAP, lsl #32
    // 0xbb8210: cmp             w3, NULL
    // 0xbb8214: b.eq            #0xbb8280
    // 0xbb8218: r1 = LoadInt32Instr(r2)
    //     0xbb8218: sbfx            x1, x2, #1, #0x1f
    //     0xbb821c: tbz             w2, #0, #0xbb8224
    //     0xbb8220: ldur            x1, [x2, #7]
    // 0xbb8224: r4 = LoadInt32Instr(r3)
    //     0xbb8224: sbfx            x4, x3, #1, #0x1f
    //     0xbb8228: tbz             w3, #0, #0xbb8230
    //     0xbb822c: ldur            x4, [x3, #7]
    // 0xbb8230: cmp             x1, x4
    // 0xbb8234: b.eq            #0xbb825c
    // 0xbb8238: LoadField: r1 = r0->field_b
    //     0xbb8238: ldur            w1, [x0, #0xb]
    // 0xbb823c: DecompressPointer r1
    //     0xbb823c: add             x1, x1, HEAP, lsl #32
    // 0xbb8240: cmp             w1, NULL
    // 0xbb8244: b.eq            #0xbb8284
    // 0xbb8248: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xbb8248: ldur            w4, [x1, #0x17]
    // 0xbb824c: DecompressPointer r4
    //     0xbb824c: add             x4, x4, HEAP, lsl #32
    // 0xbb8250: ArrayLoad: r1 = r4[0]  ; List_4
    //     0xbb8250: ldur            w1, [x4, #0x17]
    // 0xbb8254: DecompressPointer r1
    //     0xbb8254: add             x1, x1, HEAP, lsl #32
    // 0xbb8258: r0 = reorder()
    //     0xbb8258: bl              #0xa4ac4c  ; [package:nuonline/app/modules/menu/controllers/menu_controller.dart] MenuController::reorder
    // 0xbb825c: ldur            x1, [fp, #-8]
    // 0xbb8260: r0 = _dragReset()
    //     0xbb8260: bl              #0x9a12ec  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragReset
    // 0xbb8264: r0 = Null
    //     0xbb8264: mov             x0, NULL
    // 0xbb8268: LeaveFrame
    //     0xbb8268: mov             SP, fp
    //     0xbb826c: ldp             fp, lr, [SP], #0x10
    // 0xbb8270: ret
    //     0xbb8270: ret             
    // 0xbb8274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8274: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8278: b               #0xbb81f8
    // 0xbb827c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb827c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb8280: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb8280: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb8284: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb8284: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _dropCompleted(dynamic) {
    // ** addr: 0xbb88b0, size: 0x38
    // 0xbb88b0: EnterFrame
    //     0xbb88b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb88b4: mov             fp, SP
    // 0xbb88b8: ldr             x0, [fp, #0x10]
    // 0xbb88bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb88bc: ldur            w1, [x0, #0x17]
    // 0xbb88c0: DecompressPointer r1
    //     0xbb88c0: add             x1, x1, HEAP, lsl #32
    // 0xbb88c4: CheckStackOverflow
    //     0xbb88c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb88c8: cmp             SP, x16
    //     0xbb88cc: b.ls            #0xbb88e0
    // 0xbb88d0: r0 = _dropCompleted()
    //     0xbb88d0: bl              #0xbb81d8  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dropCompleted
    // 0xbb88d4: LeaveFrame
    //     0xbb88d4: mov             SP, fp
    //     0xbb88d8: ldp             fp, lr, [SP], #0x10
    // 0xbb88dc: ret
    //     0xbb88dc: ret             
    // 0xbb88e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb88e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb88e4: b               #0xbb88d0
  }
  [closure] void _dragEnd(dynamic, _DragInfo) {
    // ** addr: 0xbb88e8, size: 0x3c
    // 0xbb88e8: EnterFrame
    //     0xbb88e8: stp             fp, lr, [SP, #-0x10]!
    //     0xbb88ec: mov             fp, SP
    // 0xbb88f0: ldr             x0, [fp, #0x18]
    // 0xbb88f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb88f4: ldur            w1, [x0, #0x17]
    // 0xbb88f8: DecompressPointer r1
    //     0xbb88f8: add             x1, x1, HEAP, lsl #32
    // 0xbb88fc: CheckStackOverflow
    //     0xbb88fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8900: cmp             SP, x16
    //     0xbb8904: b.ls            #0xbb891c
    // 0xbb8908: ldr             x2, [fp, #0x10]
    // 0xbb890c: r0 = _dragEnd()
    //     0xbb890c: bl              #0xbb8924  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragEnd
    // 0xbb8910: LeaveFrame
    //     0xbb8910: mov             SP, fp
    //     0xbb8914: ldp             fp, lr, [SP], #0x10
    // 0xbb8918: ret
    //     0xbb8918: ret             
    // 0xbb891c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb891c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8920: b               #0xbb8908
  }
  _ _dragEnd(/* No info */) {
    // ** addr: 0xbb8924, size: 0x64
    // 0xbb8924: EnterFrame
    //     0xbb8924: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8928: mov             fp, SP
    // 0xbb892c: AllocStack(0x8)
    //     0xbb892c: sub             SP, SP, #8
    // 0xbb8930: SetupParameters(SliverReorderableGridState this /* r1 => r1, fp-0x8 */)
    //     0xbb8930: stur            x1, [fp, #-8]
    // 0xbb8934: CheckStackOverflow
    //     0xbb8934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8938: cmp             SP, x16
    //     0xbb893c: b.ls            #0xbb8980
    // 0xbb8940: r1 = 1
    //     0xbb8940: movz            x1, #0x1
    // 0xbb8944: r0 = AllocateContext()
    //     0xbb8944: bl              #0xec126c  ; AllocateContextStub
    // 0xbb8948: mov             x1, x0
    // 0xbb894c: ldur            x0, [fp, #-8]
    // 0xbb8950: StoreField: r1->field_f = r0
    //     0xbb8950: stur            w0, [x1, #0xf]
    // 0xbb8954: mov             x2, x1
    // 0xbb8958: r1 = Function '<anonymous closure>':.
    //     0xbb8958: add             x1, PP, #0x51, lsl #12  ; [pp+0x51428] AnonymousClosure: (0xbb8988), in [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragEnd (0xbb8924)
    //     0xbb895c: ldr             x1, [x1, #0x428]
    // 0xbb8960: r0 = AllocateClosure()
    //     0xbb8960: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb8964: ldur            x1, [fp, #-8]
    // 0xbb8968: mov             x2, x0
    // 0xbb896c: r0 = setState()
    //     0xbb896c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb8970: r0 = Null
    //     0xbb8970: mov             x0, NULL
    // 0xbb8974: LeaveFrame
    //     0xbb8974: mov             SP, fp
    //     0xbb8978: ldp             fp, lr, [SP], #0x10
    // 0xbb897c: ret
    //     0xbb897c: ret             
    // 0xbb8980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8980: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8984: b               #0xbb8940
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb8988, size: 0x94
    // 0xbb8988: EnterFrame
    //     0xbb8988: stp             fp, lr, [SP, #-0x10]!
    //     0xbb898c: mov             fp, SP
    // 0xbb8990: AllocStack(0x8)
    //     0xbb8990: sub             SP, SP, #8
    // 0xbb8994: SetupParameters()
    //     0xbb8994: ldr             x0, [fp, #0x10]
    //     0xbb8998: ldur            w1, [x0, #0x17]
    //     0xbb899c: add             x1, x1, HEAP, lsl #32
    // 0xbb89a0: CheckStackOverflow
    //     0xbb89a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb89a4: cmp             SP, x16
    //     0xbb89a8: b.ls            #0xbb8a10
    // 0xbb89ac: LoadField: r0 = r1->field_f
    //     0xbb89ac: ldur            w0, [x1, #0xf]
    // 0xbb89b0: DecompressPointer r0
    //     0xbb89b0: add             x0, x0, HEAP, lsl #32
    // 0xbb89b4: stur            x0, [fp, #-8]
    // 0xbb89b8: LoadField: r1 = r0->field_2b
    //     0xbb89b8: ldur            w1, [x0, #0x2b]
    // 0xbb89bc: DecompressPointer r1
    //     0xbb89bc: add             x1, x1, HEAP, lsl #32
    // 0xbb89c0: cmp             w1, NULL
    // 0xbb89c4: b.eq            #0xbb8a18
    // 0xbb89c8: r2 = LoadInt32Instr(r1)
    //     0xbb89c8: sbfx            x2, x1, #1, #0x1f
    //     0xbb89cc: tbz             w1, #0, #0xbb89d4
    //     0xbb89d0: ldur            x2, [x1, #7]
    // 0xbb89d4: mov             x1, x0
    // 0xbb89d8: r0 = _itemOffsetAt()
    //     0xbb89d8: bl              #0xbb7f34  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_itemOffsetAt
    // 0xbb89dc: mov             x2, x0
    // 0xbb89e0: ldur            x1, [fp, #-8]
    // 0xbb89e4: StoreField: r1->field_2f = r0
    //     0xbb89e4: stur            w0, [x1, #0x2f]
    //     0xbb89e8: ldurb           w16, [x1, #-1]
    //     0xbb89ec: ldurb           w17, [x0, #-1]
    //     0xbb89f0: and             x16, x17, x16, lsr #2
    //     0xbb89f4: tst             x16, HEAP, lsr #32
    //     0xbb89f8: b.eq            #0xbb8a00
    //     0xbb89fc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbb8a00: mov             x0, x2
    // 0xbb8a04: LeaveFrame
    //     0xbb8a04: mov             SP, fp
    //     0xbb8a08: ldp             fp, lr, [SP], #0x10
    // 0xbb8a0c: ret
    //     0xbb8a0c: ret             
    // 0xbb8a10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8a10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8a14: b               #0xbb89ac
    // 0xbb8a18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb8a18: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _dragCancel(dynamic, _DragInfo) {
    // ** addr: 0xbb8a1c, size: 0x3c
    // 0xbb8a1c: EnterFrame
    //     0xbb8a1c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8a20: mov             fp, SP
    // 0xbb8a24: ldr             x0, [fp, #0x18]
    // 0xbb8a28: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb8a28: ldur            w1, [x0, #0x17]
    // 0xbb8a2c: DecompressPointer r1
    //     0xbb8a2c: add             x1, x1, HEAP, lsl #32
    // 0xbb8a30: CheckStackOverflow
    //     0xbb8a30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8a34: cmp             SP, x16
    //     0xbb8a38: b.ls            #0xbb8a50
    // 0xbb8a3c: r0 = _dragReset()
    //     0xbb8a3c: bl              #0x9a12ec  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragReset
    // 0xbb8a40: r0 = Null
    //     0xbb8a40: mov             x0, NULL
    // 0xbb8a44: LeaveFrame
    //     0xbb8a44: mov             SP, fp
    //     0xbb8a48: ldp             fp, lr, [SP], #0x10
    // 0xbb8a4c: ret
    //     0xbb8a4c: ret             
    // 0xbb8a50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8a50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8a54: b               #0xbb8a3c
  }
  [closure] void _dragUpdate(dynamic, _DragInfo, Offset, Offset) {
    // ** addr: 0xbb8a58, size: 0x44
    // 0xbb8a58: EnterFrame
    //     0xbb8a58: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8a5c: mov             fp, SP
    // 0xbb8a60: ldr             x0, [fp, #0x28]
    // 0xbb8a64: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb8a64: ldur            w1, [x0, #0x17]
    // 0xbb8a68: DecompressPointer r1
    //     0xbb8a68: add             x1, x1, HEAP, lsl #32
    // 0xbb8a6c: CheckStackOverflow
    //     0xbb8a6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8a70: cmp             SP, x16
    //     0xbb8a74: b.ls            #0xbb8a94
    // 0xbb8a78: ldr             x2, [fp, #0x20]
    // 0xbb8a7c: ldr             x3, [fp, #0x18]
    // 0xbb8a80: ldr             x5, [fp, #0x10]
    // 0xbb8a84: r0 = _dragUpdate()
    //     0xbb8a84: bl              #0xbb8a9c  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragUpdate
    // 0xbb8a88: LeaveFrame
    //     0xbb8a88: mov             SP, fp
    //     0xbb8a8c: ldp             fp, lr, [SP], #0x10
    // 0xbb8a90: ret
    //     0xbb8a90: ret             
    // 0xbb8a94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8a94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8a98: b               #0xbb8a78
  }
  _ _dragUpdate(/* No info */) {
    // ** addr: 0xbb8a9c, size: 0x64
    // 0xbb8a9c: EnterFrame
    //     0xbb8a9c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8aa0: mov             fp, SP
    // 0xbb8aa4: AllocStack(0x8)
    //     0xbb8aa4: sub             SP, SP, #8
    // 0xbb8aa8: SetupParameters(SliverReorderableGridState this /* r1 => r1, fp-0x8 */)
    //     0xbb8aa8: stur            x1, [fp, #-8]
    // 0xbb8aac: CheckStackOverflow
    //     0xbb8aac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8ab0: cmp             SP, x16
    //     0xbb8ab4: b.ls            #0xbb8af8
    // 0xbb8ab8: r1 = 1
    //     0xbb8ab8: movz            x1, #0x1
    // 0xbb8abc: r0 = AllocateContext()
    //     0xbb8abc: bl              #0xec126c  ; AllocateContextStub
    // 0xbb8ac0: mov             x1, x0
    // 0xbb8ac4: ldur            x0, [fp, #-8]
    // 0xbb8ac8: StoreField: r1->field_f = r0
    //     0xbb8ac8: stur            w0, [x1, #0xf]
    // 0xbb8acc: mov             x2, x1
    // 0xbb8ad0: r1 = Function '<anonymous closure>':.
    //     0xbb8ad0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51440] AnonymousClosure: (0xbb8b00), in [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragUpdate (0xbb8a9c)
    //     0xbb8ad4: ldr             x1, [x1, #0x440]
    // 0xbb8ad8: r0 = AllocateClosure()
    //     0xbb8ad8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb8adc: ldur            x1, [fp, #-8]
    // 0xbb8ae0: mov             x2, x0
    // 0xbb8ae4: r0 = setState()
    //     0xbb8ae4: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbb8ae8: r0 = Null
    //     0xbb8ae8: mov             x0, NULL
    // 0xbb8aec: LeaveFrame
    //     0xbb8aec: mov             SP, fp
    //     0xbb8af0: ldp             fp, lr, [SP], #0x10
    // 0xbb8af4: ret
    //     0xbb8af4: ret             
    // 0xbb8af8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8af8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8afc: b               #0xbb8ab8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb8b00, size: 0x84
    // 0xbb8b00: EnterFrame
    //     0xbb8b00: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8b04: mov             fp, SP
    // 0xbb8b08: AllocStack(0x8)
    //     0xbb8b08: sub             SP, SP, #8
    // 0xbb8b0c: SetupParameters()
    //     0xbb8b0c: ldr             x0, [fp, #0x10]
    //     0xbb8b10: ldur            w2, [x0, #0x17]
    //     0xbb8b14: add             x2, x2, HEAP, lsl #32
    //     0xbb8b18: stur            x2, [fp, #-8]
    // 0xbb8b1c: CheckStackOverflow
    //     0xbb8b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8b20: cmp             SP, x16
    //     0xbb8b24: b.ls            #0xbb8b7c
    // 0xbb8b28: LoadField: r0 = r2->field_f
    //     0xbb8b28: ldur            w0, [x2, #0xf]
    // 0xbb8b2c: DecompressPointer r0
    //     0xbb8b2c: add             x0, x0, HEAP, lsl #32
    // 0xbb8b30: LoadField: r1 = r0->field_1f
    //     0xbb8b30: ldur            w1, [x0, #0x1f]
    // 0xbb8b34: DecompressPointer r1
    //     0xbb8b34: add             x1, x1, HEAP, lsl #32
    // 0xbb8b38: cmp             w1, NULL
    // 0xbb8b3c: b.ne            #0xbb8b48
    // 0xbb8b40: mov             x0, x2
    // 0xbb8b44: b               #0xbb8b50
    // 0xbb8b48: r0 = markNeedsBuild()
    //     0xbb8b48: bl              #0x650960  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::markNeedsBuild
    // 0xbb8b4c: ldur            x0, [fp, #-8]
    // 0xbb8b50: LoadField: r1 = r0->field_f
    //     0xbb8b50: ldur            w1, [x0, #0xf]
    // 0xbb8b54: DecompressPointer r1
    //     0xbb8b54: add             x1, x1, HEAP, lsl #32
    // 0xbb8b58: r0 = _dragUpdateItems()
    //     0xbb8b58: bl              #0xbb90dc  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragUpdateItems
    // 0xbb8b5c: ldur            x0, [fp, #-8]
    // 0xbb8b60: LoadField: r1 = r0->field_f
    //     0xbb8b60: ldur            w1, [x0, #0xf]
    // 0xbb8b64: DecompressPointer r1
    //     0xbb8b64: add             x1, x1, HEAP, lsl #32
    // 0xbb8b68: r0 = _autoScrollIfNecessary()
    //     0xbb8b68: bl              #0xbb8b84  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_autoScrollIfNecessary
    // 0xbb8b6c: r0 = Null
    //     0xbb8b6c: mov             x0, NULL
    // 0xbb8b70: LeaveFrame
    //     0xbb8b70: mov             SP, fp
    //     0xbb8b74: ldp             fp, lr, [SP], #0x10
    // 0xbb8b78: ret
    //     0xbb8b78: ret             
    // 0xbb8b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb8b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb8b80: b               #0xbb8b28
  }
  _ _autoScrollIfNecessary(/* No info */) async {
    // ** addr: 0xbb8b84, size: 0x558
    // 0xbb8b84: EnterFrame
    //     0xbb8b84: stp             fp, lr, [SP, #-0x10]!
    //     0xbb8b88: mov             fp, SP
    // 0xbb8b8c: AllocStack(0x30)
    //     0xbb8b8c: sub             SP, SP, #0x30
    // 0xbb8b90: SetupParameters(SliverReorderableGridState this /* r1 => r1, fp-0x10 */)
    //     0xbb8b90: stur            NULL, [fp, #-8]
    //     0xbb8b94: stur            x1, [fp, #-0x10]
    // 0xbb8b98: CheckStackOverflow
    //     0xbb8b98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb8b9c: cmp             SP, x16
    //     0xbb8ba0: b.ls            #0xbb904c
    // 0xbb8ba4: InitAsync() -> Future<void?>
    //     0xbb8ba4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbb8ba8: bl              #0x661298  ; InitAsyncStub
    // 0xbb8bac: ldur            x0, [fp, #-0x10]
    // 0xbb8bb0: LoadField: r1 = r0->field_37
    //     0xbb8bb0: ldur            w1, [x0, #0x37]
    // 0xbb8bb4: DecompressPointer r1
    //     0xbb8bb4: add             x1, x1, HEAP, lsl #32
    // 0xbb8bb8: tbz             w1, #4, #0xbb8bf8
    // 0xbb8bbc: LoadField: r1 = r0->field_27
    //     0xbb8bbc: ldur            w1, [x0, #0x27]
    // 0xbb8bc0: DecompressPointer r1
    //     0xbb8bc0: add             x1, x1, HEAP, lsl #32
    // 0xbb8bc4: cmp             w1, NULL
    // 0xbb8bc8: b.eq            #0xbb8bf8
    // 0xbb8bcc: LoadField: r2 = r1->field_3b
    //     0xbb8bcc: ldur            w2, [x1, #0x3b]
    // 0xbb8bd0: DecompressPointer r2
    //     0xbb8bd0: add             x2, x2, HEAP, lsl #32
    // 0xbb8bd4: cmp             w2, NULL
    // 0xbb8bd8: b.eq            #0xbb8bf8
    // 0xbb8bdc: LoadField: r1 = r0->field_b
    //     0xbb8bdc: ldur            w1, [x0, #0xb]
    // 0xbb8be0: DecompressPointer r1
    //     0xbb8be0: add             x1, x1, HEAP, lsl #32
    // 0xbb8be4: cmp             w1, NULL
    // 0xbb8be8: b.eq            #0xbb9054
    // 0xbb8bec: LoadField: r3 = r1->field_27
    //     0xbb8bec: ldur            w3, [x1, #0x27]
    // 0xbb8bf0: DecompressPointer r3
    //     0xbb8bf0: add             x3, x3, HEAP, lsl #32
    // 0xbb8bf4: tbz             w3, #4, #0xbb8c00
    // 0xbb8bf8: r0 = Null
    //     0xbb8bf8: mov             x0, NULL
    // 0xbb8bfc: r0 = ReturnAsyncNotFuture()
    //     0xbb8bfc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbb8c00: LoadField: r3 = r2->field_2b
    //     0xbb8c00: ldur            w3, [x2, #0x2b]
    // 0xbb8c04: DecompressPointer r3
    //     0xbb8c04: add             x3, x3, HEAP, lsl #32
    // 0xbb8c08: stur            x3, [fp, #-0x18]
    // 0xbb8c0c: cmp             w3, NULL
    // 0xbb8c10: b.eq            #0xbb9058
    // 0xbb8c14: LoadField: r1 = r2->field_f
    //     0xbb8c14: ldur            w1, [x2, #0xf]
    // 0xbb8c18: DecompressPointer r1
    //     0xbb8c18: add             x1, x1, HEAP, lsl #32
    // 0xbb8c1c: cmp             w1, NULL
    // 0xbb8c20: b.eq            #0xbb905c
    // 0xbb8c24: r0 = renderObject()
    //     0xbb8c24: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xbb8c28: mov             x3, x0
    // 0xbb8c2c: stur            x3, [fp, #-0x20]
    // 0xbb8c30: cmp             w3, NULL
    // 0xbb8c34: b.eq            #0xbb9060
    // 0xbb8c38: mov             x0, x3
    // 0xbb8c3c: r2 = Null
    //     0xbb8c3c: mov             x2, NULL
    // 0xbb8c40: r1 = Null
    //     0xbb8c40: mov             x1, NULL
    // 0xbb8c44: r4 = LoadClassIdInstr(r0)
    //     0xbb8c44: ldur            x4, [x0, #-1]
    //     0xbb8c48: ubfx            x4, x4, #0xc, #0x14
    // 0xbb8c4c: sub             x4, x4, #0xbba
    // 0xbb8c50: cmp             x4, #0x9a
    // 0xbb8c54: b.ls            #0xbb8c68
    // 0xbb8c58: r8 = RenderBox
    //     0xbb8c58: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0xbb8c5c: r3 = Null
    //     0xbb8c5c: add             x3, PP, #0x51, lsl #12  ; [pp+0x51448] Null
    //     0xbb8c60: ldr             x3, [x3, #0x448]
    // 0xbb8c64: r0 = RenderBox()
    //     0xbb8c64: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0xbb8c68: ldur            x1, [fp, #-0x20]
    // 0xbb8c6c: r2 = Instance_Offset
    //     0xbb8c6c: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xbb8c70: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb8c70: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb8c74: r0 = localToGlobal()
    //     0xbb8c74: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0xbb8c78: LoadField: d0 = r0->field_f
    //     0xbb8c78: ldur            d0, [x0, #0xf]
    // 0xbb8c7c: ldur            x1, [fp, #-0x20]
    // 0xbb8c80: stur            d0, [fp, #-0x28]
    // 0xbb8c84: r0 = size()
    //     0xbb8c84: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xbb8c88: LoadField: d0 = r0->field_f
    //     0xbb8c88: ldur            d0, [x0, #0xf]
    // 0xbb8c8c: ldur            d1, [fp, #-0x28]
    // 0xbb8c90: fadd            d2, d1, d0
    // 0xbb8c94: ldur            x0, [fp, #-0x10]
    // 0xbb8c98: stur            d2, [fp, #-0x30]
    // 0xbb8c9c: LoadField: r1 = r0->field_27
    //     0xbb8c9c: ldur            w1, [x0, #0x27]
    // 0xbb8ca0: DecompressPointer r1
    //     0xbb8ca0: add             x1, x1, HEAP, lsl #32
    // 0xbb8ca4: cmp             w1, NULL
    // 0xbb8ca8: b.eq            #0xbb9064
    // 0xbb8cac: LoadField: r2 = r1->field_2b
    //     0xbb8cac: ldur            w2, [x1, #0x2b]
    // 0xbb8cb0: DecompressPointer r2
    //     0xbb8cb0: add             x2, x2, HEAP, lsl #32
    // 0xbb8cb4: r16 = Sentinel
    //     0xbb8cb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb8cb8: cmp             w2, w16
    // 0xbb8cbc: b.eq            #0xbb9068
    // 0xbb8cc0: LoadField: r3 = r1->field_2f
    //     0xbb8cc0: ldur            w3, [x1, #0x2f]
    // 0xbb8cc4: DecompressPointer r3
    //     0xbb8cc4: add             x3, x3, HEAP, lsl #32
    // 0xbb8cc8: r16 = Sentinel
    //     0xbb8cc8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb8ccc: cmp             w3, w16
    // 0xbb8cd0: b.eq            #0xbb9074
    // 0xbb8cd4: mov             x1, x2
    // 0xbb8cd8: mov             x2, x3
    // 0xbb8cdc: r0 = -()
    //     0xbb8cdc: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xbb8ce0: LoadField: d0 = r0->field_f
    //     0xbb8ce0: ldur            d0, [x0, #0xf]
    // 0xbb8ce4: ldur            x0, [fp, #-0x10]
    // 0xbb8ce8: LoadField: r1 = r0->field_27
    //     0xbb8ce8: ldur            w1, [x0, #0x27]
    // 0xbb8cec: DecompressPointer r1
    //     0xbb8cec: add             x1, x1, HEAP, lsl #32
    // 0xbb8cf0: cmp             w1, NULL
    // 0xbb8cf4: b.eq            #0xbb9080
    // 0xbb8cf8: LoadField: r2 = r1->field_33
    //     0xbb8cf8: ldur            w2, [x1, #0x33]
    // 0xbb8cfc: DecompressPointer r2
    //     0xbb8cfc: add             x2, x2, HEAP, lsl #32
    // 0xbb8d00: r16 = Sentinel
    //     0xbb8d00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb8d04: cmp             w2, w16
    // 0xbb8d08: b.eq            #0xbb9084
    // 0xbb8d0c: LoadField: d1 = r2->field_f
    //     0xbb8d0c: ldur            d1, [x2, #0xf]
    // 0xbb8d10: fadd            d2, d0, d1
    // 0xbb8d14: ldur            d1, [fp, #-0x28]
    // 0xbb8d18: fcmp            d1, d0
    // 0xbb8d1c: b.le            #0xbb8e14
    // 0xbb8d20: ldur            x2, [fp, #-0x18]
    // 0xbb8d24: LoadField: r1 = r2->field_3f
    //     0xbb8d24: ldur            w1, [x2, #0x3f]
    // 0xbb8d28: DecompressPointer r1
    //     0xbb8d28: add             x1, x1, HEAP, lsl #32
    // 0xbb8d2c: cmp             w1, NULL
    // 0xbb8d30: b.eq            #0xbb9090
    // 0xbb8d34: LoadField: r3 = r2->field_2f
    //     0xbb8d34: ldur            w3, [x2, #0x2f]
    // 0xbb8d38: DecompressPointer r3
    //     0xbb8d38: add             x3, x3, HEAP, lsl #32
    // 0xbb8d3c: cmp             w3, NULL
    // 0xbb8d40: b.eq            #0xbb9094
    // 0xbb8d44: LoadField: d3 = r1->field_7
    //     0xbb8d44: ldur            d3, [x1, #7]
    // 0xbb8d48: LoadField: d4 = r3->field_7
    //     0xbb8d48: ldur            d4, [x3, #7]
    // 0xbb8d4c: fcmp            d3, d4
    // 0xbb8d50: b.le            #0xbb8e04
    // 0xbb8d54: d5 = 20.000000
    //     0xbb8d54: fmov            d5, #20.00000000
    // 0xbb8d58: fsub            d2, d1, d0
    // 0xbb8d5c: fcmp            d2, d5
    // 0xbb8d60: b.le            #0xbb8d6c
    // 0xbb8d64: d0 = 0.000000
    //     0xbb8d64: eor             v0.16b, v0.16b, v0.16b
    // 0xbb8d68: b               #0xbb8d94
    // 0xbb8d6c: fcmp            d5, d2
    // 0xbb8d70: b.le            #0xbb8d80
    // 0xbb8d74: d2 = 20.000000
    //     0xbb8d74: fmov            d2, #20.00000000
    // 0xbb8d78: d0 = 0.000000
    //     0xbb8d78: eor             v0.16b, v0.16b, v0.16b
    // 0xbb8d7c: b               #0xbb8d94
    // 0xbb8d80: d0 = 0.000000
    //     0xbb8d80: eor             v0.16b, v0.16b, v0.16b
    // 0xbb8d84: fcmp            d2, d0
    // 0xbb8d88: b.ne            #0xbb8d94
    // 0xbb8d8c: fadd            d1, d2, d5
    // 0xbb8d90: mov             v2.16b, v1.16b
    // 0xbb8d94: d1 = 10.000000
    //     0xbb8d94: fmov            d1, #10.00000000
    // 0xbb8d98: fdiv            d5, d2, d1
    // 0xbb8d9c: fsub            d1, d3, d5
    // 0xbb8da0: fcmp            d4, d1
    // 0xbb8da4: b.le            #0xbb8db0
    // 0xbb8da8: LoadField: d1 = r3->field_7
    //     0xbb8da8: ldur            d1, [x3, #7]
    // 0xbb8dac: b               #0xbb8dd8
    // 0xbb8db0: fcmp            d1, d4
    // 0xbb8db4: b.gt            #0xbb8dd8
    // 0xbb8db8: fcmp            d4, d0
    // 0xbb8dbc: b.ne            #0xbb8dcc
    // 0xbb8dc0: fadd            d2, d4, d1
    // 0xbb8dc4: mov             v1.16b, v2.16b
    // 0xbb8dc8: b               #0xbb8dd8
    // 0xbb8dcc: fcmp            d1, d1
    // 0xbb8dd0: b.vs            #0xbb8dd8
    // 0xbb8dd4: LoadField: d1 = r3->field_7
    //     0xbb8dd4: ldur            d1, [x3, #7]
    // 0xbb8dd8: r1 = inline_Allocate_Double()
    //     0xbb8dd8: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xbb8ddc: add             x1, x1, #0x10
    //     0xbb8de0: cmp             x3, x1
    //     0xbb8de4: b.ls            #0xbb9098
    //     0xbb8de8: str             x1, [THR, #0x50]  ; THR::top
    //     0xbb8dec: sub             x1, x1, #0xf
    //     0xbb8df0: movz            x3, #0xe15c
    //     0xbb8df4: movk            x3, #0x3, lsl #16
    //     0xbb8df8: stur            x3, [x1, #-1]
    // 0xbb8dfc: StoreField: r1->field_7 = d1
    //     0xbb8dfc: stur            d1, [x1, #7]
    // 0xbb8e00: b               #0xbb8f30
    // 0xbb8e04: d0 = 0.000000
    //     0xbb8e04: eor             v0.16b, v0.16b, v0.16b
    // 0xbb8e08: d1 = 10.000000
    //     0xbb8e08: fmov            d1, #10.00000000
    // 0xbb8e0c: d5 = 20.000000
    //     0xbb8e0c: fmov            d5, #20.00000000
    // 0xbb8e10: b               #0xbb8e24
    // 0xbb8e14: ldur            x2, [fp, #-0x18]
    // 0xbb8e18: d0 = 0.000000
    //     0xbb8e18: eor             v0.16b, v0.16b, v0.16b
    // 0xbb8e1c: d1 = 10.000000
    //     0xbb8e1c: fmov            d1, #10.00000000
    // 0xbb8e20: d5 = 20.000000
    //     0xbb8e20: fmov            d5, #20.00000000
    // 0xbb8e24: ldur            d3, [fp, #-0x30]
    // 0xbb8e28: fcmp            d2, d3
    // 0xbb8e2c: b.le            #0xbb8f2c
    // 0xbb8e30: LoadField: r1 = r2->field_3f
    //     0xbb8e30: ldur            w1, [x2, #0x3f]
    // 0xbb8e34: DecompressPointer r1
    //     0xbb8e34: add             x1, x1, HEAP, lsl #32
    // 0xbb8e38: cmp             w1, NULL
    // 0xbb8e3c: b.eq            #0xbb90b4
    // 0xbb8e40: LoadField: r3 = r2->field_33
    //     0xbb8e40: ldur            w3, [x2, #0x33]
    // 0xbb8e44: DecompressPointer r3
    //     0xbb8e44: add             x3, x3, HEAP, lsl #32
    // 0xbb8e48: cmp             w3, NULL
    // 0xbb8e4c: b.eq            #0xbb90b8
    // 0xbb8e50: LoadField: d4 = r1->field_7
    //     0xbb8e50: ldur            d4, [x1, #7]
    // 0xbb8e54: LoadField: d6 = r3->field_7
    //     0xbb8e54: ldur            d6, [x3, #7]
    // 0xbb8e58: fcmp            d6, d4
    // 0xbb8e5c: b.le            #0xbb8f2c
    // 0xbb8e60: fsub            d7, d2, d3
    // 0xbb8e64: fcmp            d7, d5
    // 0xbb8e68: b.le            #0xbb8e74
    // 0xbb8e6c: mov             v2.16b, v7.16b
    // 0xbb8e70: b               #0xbb8e98
    // 0xbb8e74: fcmp            d5, d7
    // 0xbb8e78: b.le            #0xbb8e84
    // 0xbb8e7c: d2 = 20.000000
    //     0xbb8e7c: fmov            d2, #20.00000000
    // 0xbb8e80: b               #0xbb8e98
    // 0xbb8e84: fcmp            d7, d0
    // 0xbb8e88: b.ne            #0xbb8e94
    // 0xbb8e8c: fadd            d2, d7, d5
    // 0xbb8e90: b               #0xbb8e98
    // 0xbb8e94: mov             v2.16b, v7.16b
    // 0xbb8e98: fdiv            d3, d2, d1
    // 0xbb8e9c: fadd            d1, d4, d3
    // 0xbb8ea0: fcmp            d6, d1
    // 0xbb8ea4: b.gt            #0xbb8f00
    // 0xbb8ea8: fcmp            d1, d6
    // 0xbb8eac: b.le            #0xbb8eb8
    // 0xbb8eb0: LoadField: d1 = r3->field_7
    //     0xbb8eb0: ldur            d1, [x3, #7]
    // 0xbb8eb4: b               #0xbb8f00
    // 0xbb8eb8: fcmp            d6, d0
    // 0xbb8ebc: b.ne            #0xbb8ed4
    // 0xbb8ec0: fadd            d2, d6, d1
    // 0xbb8ec4: fmul            d3, d2, d6
    // 0xbb8ec8: fmul            d2, d3, d1
    // 0xbb8ecc: mov             v1.16b, v2.16b
    // 0xbb8ed0: b               #0xbb8f00
    // 0xbb8ed4: fcmp            d6, d0
    // 0xbb8ed8: b.ne            #0xbb8ef4
    // 0xbb8edc: fcmp            d1, #0.0
    // 0xbb8ee0: b.vs            #0xbb8ef4
    // 0xbb8ee4: b.ne            #0xbb8ef0
    // 0xbb8ee8: r1 = 0.000000
    //     0xbb8ee8: fmov            x1, d1
    // 0xbb8eec: cmp             x1, #0
    // 0xbb8ef0: b.lt            #0xbb8f00
    // 0xbb8ef4: fcmp            d1, d1
    // 0xbb8ef8: b.vs            #0xbb8f00
    // 0xbb8efc: LoadField: d1 = r3->field_7
    //     0xbb8efc: ldur            d1, [x3, #7]
    // 0xbb8f00: r1 = inline_Allocate_Double()
    //     0xbb8f00: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xbb8f04: add             x1, x1, #0x10
    //     0xbb8f08: cmp             x3, x1
    //     0xbb8f0c: b.ls            #0xbb90bc
    //     0xbb8f10: str             x1, [THR, #0x50]  ; THR::top
    //     0xbb8f14: sub             x1, x1, #0xf
    //     0xbb8f18: movz            x3, #0xe15c
    //     0xbb8f1c: movk            x3, #0x3, lsl #16
    //     0xbb8f20: stur            x3, [x1, #-1]
    // 0xbb8f24: StoreField: r1->field_7 = d1
    //     0xbb8f24: stur            d1, [x1, #7]
    // 0xbb8f28: b               #0xbb8f30
    // 0xbb8f2c: r1 = Null
    //     0xbb8f2c: mov             x1, NULL
    // 0xbb8f30: cmp             w1, NULL
    // 0xbb8f34: b.eq            #0xbb9044
    // 0xbb8f38: LoadField: r3 = r2->field_3f
    //     0xbb8f38: ldur            w3, [x2, #0x3f]
    // 0xbb8f3c: DecompressPointer r3
    //     0xbb8f3c: add             x3, x3, HEAP, lsl #32
    // 0xbb8f40: cmp             w3, NULL
    // 0xbb8f44: b.eq            #0xbb90d8
    // 0xbb8f48: LoadField: d1 = r1->field_7
    //     0xbb8f48: ldur            d1, [x1, #7]
    // 0xbb8f4c: LoadField: d2 = r3->field_7
    //     0xbb8f4c: ldur            d2, [x3, #7]
    // 0xbb8f50: fsub            d3, d1, d2
    // 0xbb8f54: fcmp            d3, d0
    // 0xbb8f58: b.ne            #0xbb8f6c
    // 0xbb8f5c: d2 = 1.000000
    //     0xbb8f5c: fmov            d2, #1.00000000
    // 0xbb8f60: fcmp            d0, d2
    // 0xbb8f64: b.lt            #0xbb9044
    // 0xbb8f68: b               #0xbb8f90
    // 0xbb8f6c: d2 = 1.000000
    //     0xbb8f6c: fmov            d2, #1.00000000
    // 0xbb8f70: fcmp            d0, d3
    // 0xbb8f74: b.le            #0xbb8f88
    // 0xbb8f78: fneg            d0, d3
    // 0xbb8f7c: fcmp            d0, d2
    // 0xbb8f80: b.lt            #0xbb9044
    // 0xbb8f84: b               #0xbb8f90
    // 0xbb8f88: fcmp            d3, d2
    // 0xbb8f8c: b.lt            #0xbb9044
    // 0xbb8f90: r1 = true
    //     0xbb8f90: add             x1, NULL, #0x20  ; true
    // 0xbb8f94: StoreField: r0->field_37 = r1
    //     0xbb8f94: stur            w1, [x0, #0x37]
    // 0xbb8f98: r1 = LoadClassIdInstr(r2)
    //     0xbb8f98: ldur            x1, [x2, #-1]
    //     0xbb8f9c: ubfx            x1, x1, #0xc, #0x14
    // 0xbb8fa0: cmp             x1, #0xe46
    // 0xbb8fa4: b.ne            #0xbb8fdc
    // 0xbb8fa8: LoadField: r3 = r2->field_6b
    //     0xbb8fa8: ldur            w3, [x2, #0x6b]
    // 0xbb8fac: DecompressPointer r3
    //     0xbb8fac: add             x3, x3, HEAP, lsl #32
    // 0xbb8fb0: mov             x1, x3
    // 0xbb8fb4: mov             v0.16b, v1.16b
    // 0xbb8fb8: stur            x3, [fp, #-0x20]
    // 0xbb8fbc: r0 = unnestOffset()
    //     0xbb8fbc: bl              #0x67b614  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::unnestOffset
    // 0xbb8fc0: ldur            x1, [fp, #-0x20]
    // 0xbb8fc4: r2 = Instance__Linear
    //     0xbb8fc4: ldr             x2, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xbb8fc8: r3 = Instance_Duration
    //     0xbb8fc8: add             x3, PP, #0x51, lsl #12  ; [pp+0x51458] Obj!Duration@e3a311
    //     0xbb8fcc: ldr             x3, [x3, #0x458]
    // 0xbb8fd0: r0 = animateTo()
    //     0xbb8fd0: bl              #0x678c70  ; [package:flutter/src/widgets/nested_scroll_view.dart] _NestedScrollCoordinator::animateTo
    // 0xbb8fd4: mov             x2, x0
    // 0xbb8fd8: b               #0xbb9008
    // 0xbb8fdc: r0 = LoadClassIdInstr(r2)
    //     0xbb8fdc: ldur            x0, [x2, #-1]
    //     0xbb8fe0: ubfx            x0, x0, #0xc, #0x14
    // 0xbb8fe4: mov             x1, x2
    // 0xbb8fe8: mov             v0.16b, v1.16b
    // 0xbb8fec: r2 = Instance__Linear
    //     0xbb8fec: ldr             x2, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xbb8ff0: r3 = Instance_Duration
    //     0xbb8ff0: add             x3, PP, #0x51, lsl #12  ; [pp+0x51458] Obj!Duration@e3a311
    //     0xbb8ff4: ldr             x3, [x3, #0x458]
    // 0xbb8ff8: r0 = GDT[cid_x0 + -0xfe5]()
    //     0xbb8ff8: sub             lr, x0, #0xfe5
    //     0xbb8ffc: ldr             lr, [x21, lr, lsl #3]
    //     0xbb9000: blr             lr
    // 0xbb9004: mov             x2, x0
    // 0xbb9008: ldur            x1, [fp, #-0x10]
    // 0xbb900c: mov             x0, x2
    // 0xbb9010: stur            x2, [fp, #-0x18]
    // 0xbb9014: r0 = Await()
    //     0xbb9014: bl              #0x661044  ; AwaitStub
    // 0xbb9018: ldur            x0, [fp, #-0x10]
    // 0xbb901c: r1 = false
    //     0xbb901c: add             x1, NULL, #0x30  ; false
    // 0xbb9020: StoreField: r0->field_37 = r1
    //     0xbb9020: stur            w1, [x0, #0x37]
    // 0xbb9024: LoadField: r1 = r0->field_27
    //     0xbb9024: ldur            w1, [x0, #0x27]
    // 0xbb9028: DecompressPointer r1
    //     0xbb9028: add             x1, x1, HEAP, lsl #32
    // 0xbb902c: cmp             w1, NULL
    // 0xbb9030: b.eq            #0xbb9044
    // 0xbb9034: mov             x1, x0
    // 0xbb9038: r0 = _dragUpdateItems()
    //     0xbb9038: bl              #0xbb90dc  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_dragUpdateItems
    // 0xbb903c: ldur            x1, [fp, #-0x10]
    // 0xbb9040: r0 = _autoScrollIfNecessary()
    //     0xbb9040: bl              #0xbb8b84  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::_autoScrollIfNecessary
    // 0xbb9044: r0 = Null
    //     0xbb9044: mov             x0, NULL
    // 0xbb9048: r0 = ReturnAsyncNotFuture()
    //     0xbb9048: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbb904c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb904c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb9050: b               #0xbb8ba4
    // 0xbb9054: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb9054: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb9058: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb9058: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb905c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb905c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb9060: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb9060: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb9064: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbb9064: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbb9068: r9 = dragPosition
    //     0xbb9068: add             x9, PP, #0x51, lsl #12  ; [pp+0x51400] Field <<EMAIL>>: late (offset: 0x2c)
    //     0xbb906c: ldr             x9, [x9, #0x400]
    // 0xbb9070: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xbb9070: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xbb9074: r9 = dragOffset
    //     0xbb9074: add             x9, PP, #0x51, lsl #12  ; [pp+0x51408] Field <<EMAIL>>: late (offset: 0x30)
    //     0xbb9078: ldr             x9, [x9, #0x408]
    // 0xbb907c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xbb907c: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xbb9080: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbb9080: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbb9084: r9 = itemSize
    //     0xbb9084: add             x9, PP, #0x51, lsl #12  ; [pp+0x513f8] Field <<EMAIL>>: late (offset: 0x34)
    //     0xbb9088: ldr             x9, [x9, #0x3f8]
    // 0xbb908c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xbb908c: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xbb9090: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbb9090: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbb9094: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbb9094: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbb9098: stp             q0, q1, [SP, #-0x20]!
    // 0xbb909c: stp             x0, x2, [SP, #-0x10]!
    // 0xbb90a0: r0 = AllocateDouble()
    //     0xbb90a0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb90a4: mov             x1, x0
    // 0xbb90a8: ldp             x0, x2, [SP], #0x10
    // 0xbb90ac: ldp             q0, q1, [SP], #0x20
    // 0xbb90b0: b               #0xbb8dfc
    // 0xbb90b4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbb90b4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbb90b8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbb90b8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xbb90bc: stp             q0, q1, [SP, #-0x20]!
    // 0xbb90c0: stp             x0, x2, [SP, #-0x10]!
    // 0xbb90c4: r0 = AllocateDouble()
    //     0xbb90c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb90c8: mov             x1, x0
    // 0xbb90cc: ldp             x0, x2, [SP], #0x10
    // 0xbb90d0: ldp             q0, q1, [SP], #0x20
    // 0xbb90d4: b               #0xbb8f24
    // 0xbb90d8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbb90d8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _dragUpdateItems(/* No info */) {
    // ** addr: 0xbb90dc, size: 0x494
    // 0xbb90dc: EnterFrame
    //     0xbb90dc: stp             fp, lr, [SP, #-0x10]!
    //     0xbb90e0: mov             fp, SP
    // 0xbb90e4: AllocStack(0x50)
    //     0xbb90e4: sub             SP, SP, #0x50
    // 0xbb90e8: SetupParameters(SliverReorderableGridState this /* r1 => r0, fp-0x18 */)
    //     0xbb90e8: mov             x0, x1
    //     0xbb90ec: stur            x1, [fp, #-0x18]
    // 0xbb90f0: CheckStackOverflow
    //     0xbb90f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb90f4: cmp             SP, x16
    //     0xbb90f8: b.ls            #0xbb9520
    // 0xbb90fc: LoadField: r3 = r0->field_2b
    //     0xbb90fc: ldur            w3, [x0, #0x2b]
    // 0xbb9100: DecompressPointer r3
    //     0xbb9100: add             x3, x3, HEAP, lsl #32
    // 0xbb9104: stur            x3, [fp, #-0x10]
    // 0xbb9108: cmp             w3, NULL
    // 0xbb910c: b.eq            #0xbb9528
    // 0xbb9110: LoadField: r1 = r0->field_27
    //     0xbb9110: ldur            w1, [x0, #0x27]
    // 0xbb9114: DecompressPointer r1
    //     0xbb9114: add             x1, x1, HEAP, lsl #32
    // 0xbb9118: cmp             w1, NULL
    // 0xbb911c: b.eq            #0xbb952c
    // 0xbb9120: LoadField: r4 = r1->field_33
    //     0xbb9120: ldur            w4, [x1, #0x33]
    // 0xbb9124: DecompressPointer r4
    //     0xbb9124: add             x4, x4, HEAP, lsl #32
    // 0xbb9128: r16 = Sentinel
    //     0xbb9128: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb912c: cmp             w4, w16
    // 0xbb9130: b.eq            #0xbb9530
    // 0xbb9134: stur            x4, [fp, #-8]
    // 0xbb9138: LoadField: r2 = r1->field_2b
    //     0xbb9138: ldur            w2, [x1, #0x2b]
    // 0xbb913c: DecompressPointer r2
    //     0xbb913c: add             x2, x2, HEAP, lsl #32
    // 0xbb9140: r16 = Sentinel
    //     0xbb9140: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb9144: cmp             w2, w16
    // 0xbb9148: b.eq            #0xbb953c
    // 0xbb914c: LoadField: r5 = r1->field_2f
    //     0xbb914c: ldur            w5, [x1, #0x2f]
    // 0xbb9150: DecompressPointer r5
    //     0xbb9150: add             x5, x5, HEAP, lsl #32
    // 0xbb9154: r16 = Sentinel
    //     0xbb9154: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb9158: cmp             w5, w16
    // 0xbb915c: b.eq            #0xbb9548
    // 0xbb9160: mov             x1, x2
    // 0xbb9164: mov             x2, x5
    // 0xbb9168: r0 = -()
    //     0xbb9168: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xbb916c: ldur            x1, [fp, #-8]
    // 0xbb9170: mov             x2, x0
    // 0xbb9174: r0 = center()
    //     0xbb9174: bl              #0x7dc26c  ; [dart:ui] Size::center
    // 0xbb9178: mov             x4, x0
    // 0xbb917c: ldur            x0, [fp, #-0x18]
    // 0xbb9180: stur            x4, [fp, #-0x28]
    // 0xbb9184: LoadField: r5 = r0->field_1b
    //     0xbb9184: ldur            w5, [x0, #0x1b]
    // 0xbb9188: DecompressPointer r5
    //     0xbb9188: add             x5, x5, HEAP, lsl #32
    // 0xbb918c: stur            x5, [fp, #-0x20]
    // 0xbb9190: LoadField: r6 = r5->field_7
    //     0xbb9190: ldur            w6, [x5, #7]
    // 0xbb9194: DecompressPointer r6
    //     0xbb9194: add             x6, x6, HEAP, lsl #32
    // 0xbb9198: mov             x2, x6
    // 0xbb919c: stur            x6, [fp, #-8]
    // 0xbb91a0: r1 = Null
    //     0xbb91a0: mov             x1, NULL
    // 0xbb91a4: r3 = <X1>
    //     0xbb91a4: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xbb91a8: r0 = Null
    //     0xbb91a8: mov             x0, NULL
    // 0xbb91ac: cmp             x2, x0
    // 0xbb91b0: b.eq            #0xbb91c0
    // 0xbb91b4: r30 = InstantiateTypeArgumentsStub
    //     0xbb91b4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xbb91b8: LoadField: r30 = r30->field_7
    //     0xbb91b8: ldur            lr, [lr, #7]
    // 0xbb91bc: blr             lr
    // 0xbb91c0: mov             x1, x0
    // 0xbb91c4: r0 = _CompactIterable()
    //     0xbb91c4: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xbb91c8: mov             x1, x0
    // 0xbb91cc: ldur            x0, [fp, #-0x20]
    // 0xbb91d0: StoreField: r1->field_b = r0
    //     0xbb91d0: stur            w0, [x1, #0xb]
    // 0xbb91d4: r2 = -1
    //     0xbb91d4: movn            x2, #0
    // 0xbb91d8: StoreField: r1->field_f = r2
    //     0xbb91d8: stur            x2, [x1, #0xf]
    // 0xbb91dc: r3 = 2
    //     0xbb91dc: movz            x3, #0x2
    // 0xbb91e0: ArrayStore: r1[0] = r3  ; List_8
    //     0xbb91e0: stur            x3, [x1, #0x17]
    // 0xbb91e4: r0 = iterator()
    //     0xbb91e4: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0xbb91e8: mov             x2, x0
    // 0xbb91ec: ldur            x0, [fp, #-0x28]
    // 0xbb91f0: stur            x2, [fp, #-0x30]
    // 0xbb91f4: LoadField: d0 = r0->field_7
    //     0xbb91f4: ldur            d0, [x0, #7]
    // 0xbb91f8: stur            d0, [fp, #-0x50]
    // 0xbb91fc: LoadField: d1 = r0->field_f
    //     0xbb91fc: ldur            d1, [x0, #0xf]
    // 0xbb9200: stur            d1, [fp, #-0x48]
    // 0xbb9204: LoadField: r0 = r2->field_7
    //     0xbb9204: ldur            w0, [x2, #7]
    // 0xbb9208: DecompressPointer r0
    //     0xbb9208: add             x0, x0, HEAP, lsl #32
    // 0xbb920c: stur            x0, [fp, #-0x28]
    // 0xbb9210: CheckStackOverflow
    //     0xbb9210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb9214: cmp             SP, x16
    //     0xbb9218: b.ls            #0xbb9554
    // 0xbb921c: mov             x1, x2
    // 0xbb9220: r0 = moveNext()
    //     0xbb9220: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xbb9224: tbnz            w0, #4, #0xbb937c
    // 0xbb9228: ldur            x3, [fp, #-0x30]
    // 0xbb922c: LoadField: r4 = r3->field_33
    //     0xbb922c: ldur            w4, [x3, #0x33]
    // 0xbb9230: DecompressPointer r4
    //     0xbb9230: add             x4, x4, HEAP, lsl #32
    // 0xbb9234: stur            x4, [fp, #-0x38]
    // 0xbb9238: cmp             w4, NULL
    // 0xbb923c: b.ne            #0xbb9270
    // 0xbb9240: mov             x0, x4
    // 0xbb9244: ldur            x2, [fp, #-0x28]
    // 0xbb9248: r1 = Null
    //     0xbb9248: mov             x1, NULL
    // 0xbb924c: cmp             w2, NULL
    // 0xbb9250: b.eq            #0xbb9270
    // 0xbb9254: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbb9254: ldur            w4, [x2, #0x17]
    // 0xbb9258: DecompressPointer r4
    //     0xbb9258: add             x4, x4, HEAP, lsl #32
    // 0xbb925c: r8 = X0
    //     0xbb925c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xbb9260: LoadField: r9 = r4->field_7
    //     0xbb9260: ldur            x9, [x4, #7]
    // 0xbb9264: r3 = Null
    //     0xbb9264: add             x3, PP, #0x51, lsl #12  ; [pp+0x51460] Null
    //     0xbb9268: ldr             x3, [x3, #0x460]
    // 0xbb926c: blr             x9
    // 0xbb9270: ldur            x0, [fp, #-0x38]
    // 0xbb9274: LoadField: r1 = r0->field_f
    //     0xbb9274: ldur            w1, [x0, #0xf]
    // 0xbb9278: DecompressPointer r1
    //     0xbb9278: add             x1, x1, HEAP, lsl #32
    // 0xbb927c: cmp             w1, NULL
    // 0xbb9280: b.ne            #0xbb9290
    // 0xbb9284: ldur            d0, [fp, #-0x48]
    // 0xbb9288: ldur            d1, [fp, #-0x50]
    // 0xbb928c: b               #0xbb9364
    // 0xbb9290: ldur            d0, [fp, #-0x50]
    // 0xbb9294: r0 = renderObject()
    //     0xbb9294: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xbb9298: mov             x3, x0
    // 0xbb929c: stur            x3, [fp, #-0x40]
    // 0xbb92a0: cmp             w3, NULL
    // 0xbb92a4: b.eq            #0xbb955c
    // 0xbb92a8: mov             x0, x3
    // 0xbb92ac: r2 = Null
    //     0xbb92ac: mov             x2, NULL
    // 0xbb92b0: r1 = Null
    //     0xbb92b0: mov             x1, NULL
    // 0xbb92b4: r4 = LoadClassIdInstr(r0)
    //     0xbb92b4: ldur            x4, [x0, #-1]
    //     0xbb92b8: ubfx            x4, x4, #0xc, #0x14
    // 0xbb92bc: sub             x4, x4, #0xbba
    // 0xbb92c0: cmp             x4, #0x9a
    // 0xbb92c4: b.ls            #0xbb92d8
    // 0xbb92c8: r8 = RenderBox
    //     0xbb92c8: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0xbb92cc: r3 = Null
    //     0xbb92cc: add             x3, PP, #0x51, lsl #12  ; [pp+0x51470] Null
    //     0xbb92d0: ldr             x3, [x3, #0x470]
    // 0xbb92d4: r0 = RenderBox()
    //     0xbb92d4: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0xbb92d8: ldur            x1, [fp, #-0x40]
    // 0xbb92dc: r2 = Instance_Offset
    //     0xbb92dc: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xbb92e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb92e0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb92e4: r0 = localToGlobal()
    //     0xbb92e4: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0xbb92e8: ldur            x1, [fp, #-0x40]
    // 0xbb92ec: stur            x0, [fp, #-0x40]
    // 0xbb92f0: r0 = size()
    //     0xbb92f0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xbb92f4: ldur            x1, [fp, #-0x40]
    // 0xbb92f8: mov             x2, x0
    // 0xbb92fc: r0 = &()
    //     0xbb92fc: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0xbb9300: LoadField: d0 = r0->field_7
    //     0xbb9300: ldur            d0, [x0, #7]
    // 0xbb9304: ldur            d1, [fp, #-0x50]
    // 0xbb9308: fcmp            d1, d0
    // 0xbb930c: b.lt            #0xbb9360
    // 0xbb9310: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xbb9310: ldur            d0, [x0, #0x17]
    // 0xbb9314: fcmp            d0, d1
    // 0xbb9318: b.le            #0xbb9358
    // 0xbb931c: ldur            d0, [fp, #-0x48]
    // 0xbb9320: LoadField: d2 = r0->field_f
    //     0xbb9320: ldur            d2, [x0, #0xf]
    // 0xbb9324: fcmp            d0, d2
    // 0xbb9328: b.lt            #0xbb9364
    // 0xbb932c: LoadField: d2 = r0->field_1f
    //     0xbb932c: ldur            d2, [x0, #0x1f]
    // 0xbb9330: fcmp            d2, d0
    // 0xbb9334: b.le            #0xbb9364
    // 0xbb9338: ldur            x0, [fp, #-0x38]
    // 0xbb933c: LoadField: r1 = r0->field_b
    //     0xbb933c: ldur            w1, [x0, #0xb]
    // 0xbb9340: DecompressPointer r1
    //     0xbb9340: add             x1, x1, HEAP, lsl #32
    // 0xbb9344: cmp             w1, NULL
    // 0xbb9348: b.eq            #0xbb9560
    // 0xbb934c: LoadField: r0 = r1->field_b
    //     0xbb934c: ldur            x0, [x1, #0xb]
    // 0xbb9350: mov             x2, x0
    // 0xbb9354: b               #0xbb9390
    // 0xbb9358: ldur            d0, [fp, #-0x48]
    // 0xbb935c: b               #0xbb9364
    // 0xbb9360: ldur            d0, [fp, #-0x48]
    // 0xbb9364: ldur            x2, [fp, #-0x30]
    // 0xbb9368: ldur            x0, [fp, #-0x28]
    // 0xbb936c: mov             v31.16b, v1.16b
    // 0xbb9370: mov             v1.16b, v0.16b
    // 0xbb9374: mov             v0.16b, v31.16b
    // 0xbb9378: b               #0xbb9210
    // 0xbb937c: ldur            x0, [fp, #-0x10]
    // 0xbb9380: r1 = LoadInt32Instr(r0)
    //     0xbb9380: sbfx            x1, x0, #1, #0x1f
    //     0xbb9384: tbz             w0, #0, #0xbb938c
    //     0xbb9388: ldur            x1, [x0, #7]
    // 0xbb938c: mov             x2, x1
    // 0xbb9390: ldur            x4, [fp, #-0x18]
    // 0xbb9394: LoadField: r3 = r4->field_2b
    //     0xbb9394: ldur            w3, [x4, #0x2b]
    // 0xbb9398: DecompressPointer r3
    //     0xbb9398: add             x3, x3, HEAP, lsl #32
    // 0xbb939c: r0 = BoxInt64Instr(r2)
    //     0xbb939c: sbfiz           x0, x2, #1, #0x1f
    //     0xbb93a0: cmp             x2, x0, asr #1
    //     0xbb93a4: b.eq            #0xbb93b0
    //     0xbb93a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb93ac: stur            x2, [x0, #7]
    // 0xbb93b0: cmp             w0, w3
    // 0xbb93b4: b.eq            #0xbb93f0
    // 0xbb93b8: and             w16, w0, w3
    // 0xbb93bc: branchIfSmi(r16, 0xbb9400)
    //     0xbb93bc: tbz             w16, #0, #0xbb9400
    // 0xbb93c0: r16 = LoadClassIdInstr(r0)
    //     0xbb93c0: ldur            x16, [x0, #-1]
    //     0xbb93c4: ubfx            x16, x16, #0xc, #0x14
    // 0xbb93c8: cmp             x16, #0x3d
    // 0xbb93cc: b.ne            #0xbb9400
    // 0xbb93d0: r16 = LoadClassIdInstr(r3)
    //     0xbb93d0: ldur            x16, [x3, #-1]
    //     0xbb93d4: ubfx            x16, x16, #0xc, #0x14
    // 0xbb93d8: cmp             x16, #0x3d
    // 0xbb93dc: b.ne            #0xbb9400
    // 0xbb93e0: LoadField: r16 = r0->field_7
    //     0xbb93e0: ldur            x16, [x0, #7]
    // 0xbb93e4: LoadField: r17 = r3->field_7
    //     0xbb93e4: ldur            x17, [x3, #7]
    // 0xbb93e8: cmp             x16, x17
    // 0xbb93ec: b.ne            #0xbb9400
    // 0xbb93f0: r0 = Null
    //     0xbb93f0: mov             x0, NULL
    // 0xbb93f4: LeaveFrame
    //     0xbb93f4: mov             SP, fp
    //     0xbb93f8: ldp             fp, lr, [SP], #0x10
    // 0xbb93fc: ret
    //     0xbb93fc: ret             
    // 0xbb9400: ldur            x5, [fp, #-0x20]
    // 0xbb9404: StoreField: r4->field_2b = r0
    //     0xbb9404: stur            w0, [x4, #0x2b]
    //     0xbb9408: tbz             w0, #0, #0xbb9424
    //     0xbb940c: ldurb           w16, [x4, #-1]
    //     0xbb9410: ldurb           w17, [x0, #-1]
    //     0xbb9414: and             x16, x17, x16, lsr #2
    //     0xbb9418: tst             x16, HEAP, lsr #32
    //     0xbb941c: b.eq            #0xbb9424
    //     0xbb9420: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xbb9424: ldur            x2, [fp, #-8]
    // 0xbb9428: r1 = Null
    //     0xbb9428: mov             x1, NULL
    // 0xbb942c: r3 = <X1>
    //     0xbb942c: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xbb9430: r0 = Null
    //     0xbb9430: mov             x0, NULL
    // 0xbb9434: cmp             x2, x0
    // 0xbb9438: b.eq            #0xbb9448
    // 0xbb943c: r30 = InstantiateTypeArgumentsStub
    //     0xbb943c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xbb9440: LoadField: r30 = r30->field_7
    //     0xbb9440: ldur            lr, [lr, #7]
    // 0xbb9444: blr             lr
    // 0xbb9448: mov             x1, x0
    // 0xbb944c: r0 = _CompactIterable()
    //     0xbb944c: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xbb9450: mov             x1, x0
    // 0xbb9454: ldur            x0, [fp, #-0x20]
    // 0xbb9458: StoreField: r1->field_b = r0
    //     0xbb9458: stur            w0, [x1, #0xb]
    // 0xbb945c: r0 = -1
    //     0xbb945c: movn            x0, #0
    // 0xbb9460: StoreField: r1->field_f = r0
    //     0xbb9460: stur            x0, [x1, #0xf]
    // 0xbb9464: r0 = 2
    //     0xbb9464: movz            x0, #0x2
    // 0xbb9468: ArrayStore: r1[0] = r0  ; List_8
    //     0xbb9468: stur            x0, [x1, #0x17]
    // 0xbb946c: r0 = iterator()
    //     0xbb946c: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0xbb9470: stur            x0, [fp, #-0x10]
    // 0xbb9474: LoadField: r2 = r0->field_7
    //     0xbb9474: ldur            w2, [x0, #7]
    // 0xbb9478: DecompressPointer r2
    //     0xbb9478: add             x2, x2, HEAP, lsl #32
    // 0xbb947c: stur            x2, [fp, #-8]
    // 0xbb9480: ldur            x3, [fp, #-0x18]
    // 0xbb9484: CheckStackOverflow
    //     0xbb9484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb9488: cmp             SP, x16
    //     0xbb948c: b.ls            #0xbb9564
    // 0xbb9490: mov             x1, x0
    // 0xbb9494: r0 = moveNext()
    //     0xbb9494: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xbb9498: tbnz            w0, #4, #0xbb9510
    // 0xbb949c: ldur            x3, [fp, #-0x10]
    // 0xbb94a0: LoadField: r4 = r3->field_33
    //     0xbb94a0: ldur            w4, [x3, #0x33]
    // 0xbb94a4: DecompressPointer r4
    //     0xbb94a4: add             x4, x4, HEAP, lsl #32
    // 0xbb94a8: stur            x4, [fp, #-0x20]
    // 0xbb94ac: cmp             w4, NULL
    // 0xbb94b0: b.ne            #0xbb94e4
    // 0xbb94b4: mov             x0, x4
    // 0xbb94b8: ldur            x2, [fp, #-8]
    // 0xbb94bc: r1 = Null
    //     0xbb94bc: mov             x1, NULL
    // 0xbb94c0: cmp             w2, NULL
    // 0xbb94c4: b.eq            #0xbb94e4
    // 0xbb94c8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbb94c8: ldur            w4, [x2, #0x17]
    // 0xbb94cc: DecompressPointer r4
    //     0xbb94cc: add             x4, x4, HEAP, lsl #32
    // 0xbb94d0: r8 = X0
    //     0xbb94d0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xbb94d4: LoadField: r9 = r4->field_7
    //     0xbb94d4: ldur            x9, [x4, #7]
    // 0xbb94d8: r3 = Null
    //     0xbb94d8: add             x3, PP, #0x51, lsl #12  ; [pp+0x51480] Null
    //     0xbb94dc: ldr             x3, [x3, #0x480]
    // 0xbb94e0: blr             x9
    // 0xbb94e4: ldur            x0, [fp, #-0x18]
    // 0xbb94e8: LoadField: r1 = r0->field_2b
    //     0xbb94e8: ldur            w1, [x0, #0x2b]
    // 0xbb94ec: DecompressPointer r1
    //     0xbb94ec: add             x1, x1, HEAP, lsl #32
    // 0xbb94f0: cmp             w1, NULL
    // 0xbb94f4: b.eq            #0xbb956c
    // 0xbb94f8: ldur            x1, [fp, #-0x20]
    // 0xbb94fc: r2 = true
    //     0xbb94fc: add             x2, NULL, #0x20  ; true
    // 0xbb9500: r0 = updateForGap()
    //     0xbb9500: bl              #0xbb79b0  ; [package:reorderable_grid/src/reorderable_grid.dart] _ReorderableItemState::updateForGap
    // 0xbb9504: ldur            x0, [fp, #-0x10]
    // 0xbb9508: ldur            x2, [fp, #-8]
    // 0xbb950c: b               #0xbb9480
    // 0xbb9510: r0 = Null
    //     0xbb9510: mov             x0, NULL
    // 0xbb9514: LeaveFrame
    //     0xbb9514: mov             SP, fp
    //     0xbb9518: ldp             fp, lr, [SP], #0x10
    // 0xbb951c: ret
    //     0xbb951c: ret             
    // 0xbb9520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb9520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb9524: b               #0xbb90fc
    // 0xbb9528: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb9528: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb952c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb952c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb9530: r9 = itemSize
    //     0xbb9530: add             x9, PP, #0x51, lsl #12  ; [pp+0x513f8] Field <<EMAIL>>: late (offset: 0x34)
    //     0xbb9534: ldr             x9, [x9, #0x3f8]
    // 0xbb9538: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb9538: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb953c: r9 = dragPosition
    //     0xbb953c: add             x9, PP, #0x51, lsl #12  ; [pp+0x51400] Field <<EMAIL>>: late (offset: 0x2c)
    //     0xbb9540: ldr             x9, [x9, #0x400]
    // 0xbb9544: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb9544: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb9548: r9 = dragOffset
    //     0xbb9548: add             x9, PP, #0x51, lsl #12  ; [pp+0x51408] Field <<EMAIL>>: late (offset: 0x30)
    //     0xbb954c: ldr             x9, [x9, #0x408]
    // 0xbb9550: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb9550: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb9554: r0 = StackOverflowSharedWithFPURegs()
    //     0xbb9554: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xbb9558: b               #0xbb921c
    // 0xbb955c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb955c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb9560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb9560: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb9564: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb9564: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb9568: b               #0xbb9490
    // 0xbb956c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb956c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4700, size: 0x1c, field offset: 0xc
//   const constructor, 
class _ReorderableItem extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94c78, size: 0x40
    // 0xa94c78: EnterFrame
    //     0xa94c78: stp             fp, lr, [SP, #-0x10]!
    //     0xa94c7c: mov             fp, SP
    // 0xa94c80: mov             x0, x1
    // 0xa94c84: r1 = <_ReorderableItem>
    //     0xa94c84: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b188] TypeArguments: <_ReorderableItem>
    //     0xa94c88: ldr             x1, [x1, #0x188]
    // 0xa94c8c: r0 = _ReorderableItemState()
    //     0xa94c8c: bl              #0xa94cb8  ; Allocate_ReorderableItemStateStub -> _ReorderableItemState (size=0x28)
    // 0xa94c90: r1 = Sentinel
    //     0xa94c90: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa94c94: StoreField: r0->field_13 = r1
    //     0xa94c94: stur            w1, [x0, #0x13]
    // 0xa94c98: r1 = Instance_Offset
    //     0xa94c98: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xa94c9c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa94c9c: stur            w1, [x0, #0x17]
    // 0xa94ca0: StoreField: r0->field_1b = r1
    //     0xa94ca0: stur            w1, [x0, #0x1b]
    // 0xa94ca4: r1 = false
    //     0xa94ca4: add             x1, NULL, #0x30  ; false
    // 0xa94ca8: StoreField: r0->field_23 = r1
    //     0xa94ca8: stur            w1, [x0, #0x23]
    // 0xa94cac: LeaveFrame
    //     0xa94cac: mov             SP, fp
    //     0xa94cb0: ldp             fp, lr, [SP], #0x10
    // 0xa94cb4: ret
    //     0xa94cb4: ret             
  }
}

// class id: 4701, size: 0x34, field offset: 0xc
//   const constructor, 
class SliverReorderableGrid extends StatefulWidget {

  static _ of(/* No info */) {
    // ** addr: 0x97ea20, size: 0x4c
    // 0x97ea20: EnterFrame
    //     0x97ea20: stp             fp, lr, [SP, #-0x10]!
    //     0x97ea24: mov             fp, SP
    // 0x97ea28: AllocStack(0x10)
    //     0x97ea28: sub             SP, SP, #0x10
    // 0x97ea2c: CheckStackOverflow
    //     0x97ea2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97ea30: cmp             SP, x16
    //     0x97ea34: b.ls            #0x97ea60
    // 0x97ea38: r16 = <SliverReorderableGridState>
    //     0x97ea38: add             x16, PP, #0x51, lsl #12  ; [pp+0x514e8] TypeArguments: <SliverReorderableGridState>
    //     0x97ea3c: ldr             x16, [x16, #0x4e8]
    // 0x97ea40: stp             x1, x16, [SP]
    // 0x97ea44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x97ea44: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x97ea48: r0 = findAncestorStateOfType()
    //     0x97ea48: bl              #0x6a4a9c  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorStateOfType
    // 0x97ea4c: cmp             w0, NULL
    // 0x97ea50: b.eq            #0x97ea68
    // 0x97ea54: LeaveFrame
    //     0x97ea54: mov             SP, fp
    //     0x97ea58: ldp             fp, lr, [SP], #0x10
    // 0x97ea5c: ret
    //     0x97ea5c: ret             
    // 0x97ea60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97ea60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ea64: b               #0x97ea38
    // 0x97ea68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97ea68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ createState(/* No info */) {
    // ** addr: 0xa94bec, size: 0x80
    // 0xa94bec: EnterFrame
    //     0xa94bec: stp             fp, lr, [SP, #-0x10]!
    //     0xa94bf0: mov             fp, SP
    // 0xa94bf4: AllocStack(0x18)
    //     0xa94bf4: sub             SP, SP, #0x18
    // 0xa94bf8: CheckStackOverflow
    //     0xa94bf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa94bfc: cmp             SP, x16
    //     0xa94c00: b.ls            #0xa94c64
    // 0xa94c04: r1 = <SliverReorderableGrid>
    //     0xa94c04: add             x1, PP, #0x51, lsl #12  ; [pp+0x514f0] TypeArguments: <SliverReorderableGrid>
    //     0xa94c08: ldr             x1, [x1, #0x4f0]
    // 0xa94c0c: r0 = SliverReorderableGridState()
    //     0xa94c0c: bl              #0xa94c6c  ; AllocateSliverReorderableGridStateStub -> SliverReorderableGridState (size=0x3c)
    // 0xa94c10: mov             x1, x0
    // 0xa94c14: r0 = false
    //     0xa94c14: add             x0, NULL, #0x30  ; false
    // 0xa94c18: stur            x1, [fp, #-8]
    // 0xa94c1c: StoreField: r1->field_37 = r0
    //     0xa94c1c: stur            w0, [x1, #0x37]
    // 0xa94c20: r16 = <int, _ReorderableItemState>
    //     0xa94c20: add             x16, PP, #0x51, lsl #12  ; [pp+0x514f8] TypeArguments: <int, _ReorderableItemState>
    //     0xa94c24: ldr             x16, [x16, #0x4f8]
    // 0xa94c28: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa94c2c: stp             lr, x16, [SP]
    // 0xa94c30: r0 = Map._fromLiteral()
    //     0xa94c30: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa94c34: ldur            x1, [fp, #-8]
    // 0xa94c38: StoreField: r1->field_1b = r0
    //     0xa94c38: stur            w0, [x1, #0x1b]
    //     0xa94c3c: ldurb           w16, [x1, #-1]
    //     0xa94c40: ldurb           w17, [x0, #-1]
    //     0xa94c44: and             x16, x17, x16, lsr #2
    //     0xa94c48: tst             x16, HEAP, lsr #32
    //     0xa94c4c: b.eq            #0xa94c54
    //     0xa94c50: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa94c54: mov             x0, x1
    // 0xa94c58: LeaveFrame
    //     0xa94c58: mov             SP, fp
    //     0xa94c5c: ldp             fp, lr, [SP], #0x10
    // 0xa94c60: ret
    //     0xa94c60: ret             
    // 0xa94c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa94c64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa94c68: b               #0xa94c04
  }
  static _ maybeOf(/* No info */) {
    // ** addr: 0xbb9570, size: 0x40
    // 0xbb9570: EnterFrame
    //     0xbb9570: stp             fp, lr, [SP, #-0x10]!
    //     0xbb9574: mov             fp, SP
    // 0xbb9578: AllocStack(0x10)
    //     0xbb9578: sub             SP, SP, #0x10
    // 0xbb957c: CheckStackOverflow
    //     0xbb957c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb9580: cmp             SP, x16
    //     0xbb9584: b.ls            #0xbb95a8
    // 0xbb9588: r16 = <SliverReorderableGridState>
    //     0xbb9588: add             x16, PP, #0x51, lsl #12  ; [pp+0x514e8] TypeArguments: <SliverReorderableGridState>
    //     0xbb958c: ldr             x16, [x16, #0x4e8]
    // 0xbb9590: stp             x1, x16, [SP]
    // 0xbb9594: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbb9594: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbb9598: r0 = findAncestorStateOfType()
    //     0xbb9598: bl              #0x6a4a9c  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorStateOfType
    // 0xbb959c: LeaveFrame
    //     0xbb959c: mov             SP, fp
    //     0xbb95a0: ldp             fp, lr, [SP], #0x10
    // 0xbb95a4: ret
    //     0xbb95a4: ret             
    // 0xbb95a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb95a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb95ac: b               #0xbb9588
  }
}

// class id: 4918, size: 0x2c, field offset: 0xc
//   const constructor, 
class _DragItemProxy extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbb95b0, size: 0x14c
    // 0xbb95b0: EnterFrame
    //     0xbb95b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb95b4: mov             fp, SP
    // 0xbb95b8: AllocStack(0x48)
    //     0xbb95b8: sub             SP, SP, #0x48
    // 0xbb95bc: SetupParameters(_DragItemProxy this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbb95bc: mov             x0, x1
    //     0xbb95c0: stur            x1, [fp, #-8]
    //     0xbb95c4: mov             x1, x2
    //     0xbb95c8: stur            x2, [fp, #-0x10]
    // 0xbb95cc: CheckStackOverflow
    //     0xbb95cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb95d0: cmp             SP, x16
    //     0xbb95d4: b.ls            #0xbb96f0
    // 0xbb95d8: r1 = 2
    //     0xbb95d8: movz            x1, #0x2
    // 0xbb95dc: r0 = AllocateContext()
    //     0xbb95dc: bl              #0xec126c  ; AllocateContextStub
    // 0xbb95e0: mov             x2, x0
    // 0xbb95e4: ldur            x0, [fp, #-8]
    // 0xbb95e8: stur            x2, [fp, #-0x20]
    // 0xbb95ec: StoreField: r2->field_f = r0
    //     0xbb95ec: stur            w0, [x2, #0xf]
    // 0xbb95f0: LoadField: r3 = r0->field_27
    //     0xbb95f0: ldur            w3, [x0, #0x27]
    // 0xbb95f4: DecompressPointer r3
    //     0xbb95f4: add             x3, x3, HEAP, lsl #32
    // 0xbb95f8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xbb95f8: ldur            w4, [x0, #0x17]
    // 0xbb95fc: DecompressPointer r4
    //     0xbb95fc: add             x4, x4, HEAP, lsl #32
    // 0xbb9600: LoadField: r5 = r0->field_f
    //     0xbb9600: ldur            x5, [x0, #0xf]
    // 0xbb9604: LoadField: r6 = r0->field_23
    //     0xbb9604: ldur            w6, [x0, #0x23]
    // 0xbb9608: DecompressPointer r6
    //     0xbb9608: add             x6, x6, HEAP, lsl #32
    // 0xbb960c: stur            x6, [fp, #-0x18]
    // 0xbb9610: cmp             w3, NULL
    // 0xbb9614: b.eq            #0xbb96f8
    // 0xbb9618: r0 = BoxInt64Instr(r5)
    //     0xbb9618: sbfiz           x0, x5, #1, #0x1f
    //     0xbb961c: cmp             x5, x0, asr #1
    //     0xbb9620: b.eq            #0xbb962c
    //     0xbb9624: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbb9628: stur            x5, [x0, #7]
    // 0xbb962c: stp             x4, x3, [SP, #0x10]
    // 0xbb9630: stp             x6, x0, [SP]
    // 0xbb9634: mov             x0, x3
    // 0xbb9638: ClosureCall
    //     0xbb9638: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbb963c: ldur            x2, [x0, #0x1f]
    //     0xbb9640: blr             x2
    // 0xbb9644: ldur            x1, [fp, #-0x10]
    // 0xbb9648: stur            x0, [fp, #-8]
    // 0xbb964c: r0 = _overlayOrigin()
    //     0xbb964c: bl              #0xbb8810  ; [package:reorderable_grid/src/reorderable_grid.dart] ::_overlayOrigin
    // 0xbb9650: ldur            x2, [fp, #-0x20]
    // 0xbb9654: StoreField: r2->field_13 = r0
    //     0xbb9654: stur            w0, [x2, #0x13]
    //     0xbb9658: ldurb           w16, [x2, #-1]
    //     0xbb965c: ldurb           w17, [x0, #-1]
    //     0xbb9660: and             x16, x17, x16, lsr #2
    //     0xbb9664: tst             x16, HEAP, lsr #32
    //     0xbb9668: b.eq            #0xbb9670
    //     0xbb966c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbb9670: ldur            x1, [fp, #-0x10]
    // 0xbb9674: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb9674: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb9678: r0 = _of()
    //     0xbb9678: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbb967c: mov             x1, x0
    // 0xbb9680: r2 = true
    //     0xbb9680: add             x2, NULL, #0x20  ; true
    // 0xbb9684: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb9684: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb9688: r0 = removePadding()
    //     0xbb9688: bl              #0x9e8cac  ; [package:flutter/src/widgets/media_query.dart] MediaQueryData::removePadding
    // 0xbb968c: ldur            x2, [fp, #-0x20]
    // 0xbb9690: r1 = Function '<anonymous closure>':.
    //     0xbb9690: add             x1, PP, #0x57, lsl #12  ; [pp+0x57c70] AnonymousClosure: (0xbb96fc), in [package:reorderable_grid/src/reorderable_grid.dart] _DragItemProxy::build (0xbb95b0)
    //     0xbb9694: ldr             x1, [x1, #0xc70]
    // 0xbb9698: stur            x0, [fp, #-0x10]
    // 0xbb969c: r0 = AllocateClosure()
    //     0xbb969c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb96a0: stur            x0, [fp, #-0x20]
    // 0xbb96a4: r0 = AnimatedBuilder()
    //     0xbb96a4: bl              #0x7e5888  ; AllocateAnimatedBuilderStub -> AnimatedBuilder (size=0x18)
    // 0xbb96a8: mov             x2, x0
    // 0xbb96ac: ldur            x0, [fp, #-0x20]
    // 0xbb96b0: stur            x2, [fp, #-0x28]
    // 0xbb96b4: StoreField: r2->field_f = r0
    //     0xbb96b4: stur            w0, [x2, #0xf]
    // 0xbb96b8: ldur            x0, [fp, #-8]
    // 0xbb96bc: StoreField: r2->field_13 = r0
    //     0xbb96bc: stur            w0, [x2, #0x13]
    // 0xbb96c0: ldur            x0, [fp, #-0x18]
    // 0xbb96c4: StoreField: r2->field_b = r0
    //     0xbb96c4: stur            w0, [x2, #0xb]
    // 0xbb96c8: r1 = <_MediaQueryAspect>
    //     0xbb96c8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d80] TypeArguments: <_MediaQueryAspect>
    //     0xbb96cc: ldr             x1, [x1, #0xd80]
    // 0xbb96d0: r0 = MediaQuery()
    //     0xbb96d0: bl              #0x9e6f0c  ; AllocateMediaQueryStub -> MediaQuery (size=0x18)
    // 0xbb96d4: ldur            x1, [fp, #-0x10]
    // 0xbb96d8: StoreField: r0->field_13 = r1
    //     0xbb96d8: stur            w1, [x0, #0x13]
    // 0xbb96dc: ldur            x1, [fp, #-0x28]
    // 0xbb96e0: StoreField: r0->field_b = r1
    //     0xbb96e0: stur            w1, [x0, #0xb]
    // 0xbb96e4: LeaveFrame
    //     0xbb96e4: mov             SP, fp
    //     0xbb96e8: ldp             fp, lr, [SP], #0x10
    // 0xbb96ec: ret
    //     0xbb96ec: ret             
    // 0xbb96f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb96f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb96f4: b               #0xbb95d8
    // 0xbb96f8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbb96f8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] Positioned <anonymous closure>(dynamic, BuildContext, Widget?) {
    // ** addr: 0xbb96fc, size: 0x26c
    // 0xbb96fc: EnterFrame
    //     0xbb96fc: stp             fp, lr, [SP, #-0x10]!
    //     0xbb9700: mov             fp, SP
    // 0xbb9704: AllocStack(0x30)
    //     0xbb9704: sub             SP, SP, #0x30
    // 0xbb9708: SetupParameters()
    //     0xbb9708: ldr             x0, [fp, #0x20]
    //     0xbb970c: ldur            w3, [x0, #0x17]
    //     0xbb9710: add             x3, x3, HEAP, lsl #32
    //     0xbb9714: stur            x3, [fp, #-0x10]
    // 0xbb9718: CheckStackOverflow
    //     0xbb9718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb971c: cmp             SP, x16
    //     0xbb9720: b.ls            #0xbb98ec
    // 0xbb9724: LoadField: r0 = r3->field_f
    //     0xbb9724: ldur            w0, [x3, #0xf]
    // 0xbb9728: DecompressPointer r0
    //     0xbb9728: add             x0, x0, HEAP, lsl #32
    // 0xbb972c: LoadField: r4 = r0->field_1b
    //     0xbb972c: ldur            w4, [x0, #0x1b]
    // 0xbb9730: DecompressPointer r4
    //     0xbb9730: add             x4, x4, HEAP, lsl #32
    // 0xbb9734: stur            x4, [fp, #-8]
    // 0xbb9738: LoadField: r1 = r0->field_b
    //     0xbb9738: ldur            w1, [x0, #0xb]
    // 0xbb973c: DecompressPointer r1
    //     0xbb973c: add             x1, x1, HEAP, lsl #32
    // 0xbb9740: LoadField: r0 = r1->field_2f
    //     0xbb9740: ldur            w0, [x1, #0x2f]
    // 0xbb9744: DecompressPointer r0
    //     0xbb9744: add             x0, x0, HEAP, lsl #32
    // 0xbb9748: cmp             w0, NULL
    // 0xbb974c: b.eq            #0xbb97b4
    // 0xbb9750: LoadField: r2 = r3->field_13
    //     0xbb9750: ldur            w2, [x3, #0x13]
    // 0xbb9754: DecompressPointer r2
    //     0xbb9754: add             x2, x2, HEAP, lsl #32
    // 0xbb9758: mov             x1, x0
    // 0xbb975c: r0 = -()
    //     0xbb975c: bl              #0x618980  ; [dart:ui] Offset::-
    // 0xbb9760: mov             x2, x0
    // 0xbb9764: ldur            x0, [fp, #-0x10]
    // 0xbb9768: stur            x2, [fp, #-0x18]
    // 0xbb976c: LoadField: r1 = r0->field_f
    //     0xbb976c: ldur            w1, [x0, #0xf]
    // 0xbb9770: DecompressPointer r1
    //     0xbb9770: add             x1, x1, HEAP, lsl #32
    // 0xbb9774: LoadField: r3 = r1->field_23
    //     0xbb9774: ldur            w3, [x1, #0x23]
    // 0xbb9778: DecompressPointer r3
    //     0xbb9778: add             x3, x3, HEAP, lsl #32
    // 0xbb977c: LoadField: r1 = r3->field_37
    //     0xbb977c: ldur            w1, [x3, #0x37]
    // 0xbb9780: DecompressPointer r1
    //     0xbb9780: add             x1, x1, HEAP, lsl #32
    // 0xbb9784: r16 = Sentinel
    //     0xbb9784: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb9788: cmp             w1, w16
    // 0xbb978c: b.eq            #0xbb98f4
    // 0xbb9790: LoadField: d0 = r1->field_7
    //     0xbb9790: ldur            d0, [x1, #7]
    // 0xbb9794: r1 = Instance_Cubic
    //     0xbb9794: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0xbb9798: ldr             x1, [x1, #0xb28]
    // 0xbb979c: r0 = transform()
    //     0xbb979c: bl              #0xcd6c40  ; [package:flutter/src/animation/curves.dart] Curve::transform
    // 0xbb97a0: ldur            x1, [fp, #-0x18]
    // 0xbb97a4: ldur            x2, [fp, #-8]
    // 0xbb97a8: r0 = lerp()
    //     0xbb97a8: bl              #0x7f5018  ; [dart:ui] Offset::lerp
    // 0xbb97ac: mov             x2, x0
    // 0xbb97b0: b               #0xbb97b8
    // 0xbb97b4: ldur            x2, [fp, #-8]
    // 0xbb97b8: ldr             x1, [fp, #0x10]
    // 0xbb97bc: ldur            x0, [fp, #-0x10]
    // 0xbb97c0: LoadField: d0 = r2->field_7
    //     0xbb97c0: ldur            d0, [x2, #7]
    // 0xbb97c4: stur            d0, [fp, #-0x30]
    // 0xbb97c8: LoadField: d1 = r2->field_f
    //     0xbb97c8: ldur            d1, [x2, #0xf]
    // 0xbb97cc: stur            d1, [fp, #-0x28]
    // 0xbb97d0: LoadField: r2 = r0->field_f
    //     0xbb97d0: ldur            w2, [x0, #0xf]
    // 0xbb97d4: DecompressPointer r2
    //     0xbb97d4: add             x2, x2, HEAP, lsl #32
    // 0xbb97d8: LoadField: r0 = r2->field_1f
    //     0xbb97d8: ldur            w0, [x2, #0x1f]
    // 0xbb97dc: DecompressPointer r0
    //     0xbb97dc: add             x0, x0, HEAP, lsl #32
    // 0xbb97e0: LoadField: d2 = r0->field_7
    //     0xbb97e0: ldur            d2, [x0, #7]
    // 0xbb97e4: LoadField: d3 = r0->field_f
    //     0xbb97e4: ldur            d3, [x0, #0xf]
    // 0xbb97e8: stur            d3, [fp, #-0x20]
    // 0xbb97ec: r0 = inline_Allocate_Double()
    //     0xbb97ec: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbb97f0: add             x0, x0, #0x10
    //     0xbb97f4: cmp             x2, x0
    //     0xbb97f8: b.ls            #0xbb98fc
    //     0xbb97fc: str             x0, [THR, #0x50]  ; THR::top
    //     0xbb9800: sub             x0, x0, #0xf
    //     0xbb9804: movz            x2, #0xe15c
    //     0xbb9808: movk            x2, #0x3, lsl #16
    //     0xbb980c: stur            x2, [x0, #-1]
    // 0xbb9810: StoreField: r0->field_7 = d2
    //     0xbb9810: stur            d2, [x0, #7]
    // 0xbb9814: stur            x0, [fp, #-8]
    // 0xbb9818: r0 = SizedBox()
    //     0xbb9818: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbb981c: mov             x2, x0
    // 0xbb9820: ldur            x0, [fp, #-8]
    // 0xbb9824: stur            x2, [fp, #-0x10]
    // 0xbb9828: StoreField: r2->field_f = r0
    //     0xbb9828: stur            w0, [x2, #0xf]
    // 0xbb982c: ldur            d0, [fp, #-0x20]
    // 0xbb9830: r0 = inline_Allocate_Double()
    //     0xbb9830: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbb9834: add             x0, x0, #0x10
    //     0xbb9838: cmp             x1, x0
    //     0xbb983c: b.ls            #0xbb991c
    //     0xbb9840: str             x0, [THR, #0x50]  ; THR::top
    //     0xbb9844: sub             x0, x0, #0xf
    //     0xbb9848: movz            x1, #0xe15c
    //     0xbb984c: movk            x1, #0x3, lsl #16
    //     0xbb9850: stur            x1, [x0, #-1]
    // 0xbb9854: StoreField: r0->field_7 = d0
    //     0xbb9854: stur            d0, [x0, #7]
    // 0xbb9858: StoreField: r2->field_13 = r0
    //     0xbb9858: stur            w0, [x2, #0x13]
    // 0xbb985c: ldr             x0, [fp, #0x10]
    // 0xbb9860: StoreField: r2->field_b = r0
    //     0xbb9860: stur            w0, [x2, #0xb]
    // 0xbb9864: ldur            d0, [fp, #-0x30]
    // 0xbb9868: r0 = inline_Allocate_Double()
    //     0xbb9868: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbb986c: add             x0, x0, #0x10
    //     0xbb9870: cmp             x1, x0
    //     0xbb9874: b.ls            #0xbb9934
    //     0xbb9878: str             x0, [THR, #0x50]  ; THR::top
    //     0xbb987c: sub             x0, x0, #0xf
    //     0xbb9880: movz            x1, #0xe15c
    //     0xbb9884: movk            x1, #0x3, lsl #16
    //     0xbb9888: stur            x1, [x0, #-1]
    // 0xbb988c: StoreField: r0->field_7 = d0
    //     0xbb988c: stur            d0, [x0, #7]
    // 0xbb9890: stur            x0, [fp, #-8]
    // 0xbb9894: r1 = <StackParentData>
    //     0xbb9894: add             x1, PP, #0x25, lsl #12  ; [pp+0x25780] TypeArguments: <StackParentData>
    //     0xbb9898: ldr             x1, [x1, #0x780]
    // 0xbb989c: r0 = Positioned()
    //     0xbb989c: bl              #0x9f19f8  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xbb98a0: ldur            x1, [fp, #-8]
    // 0xbb98a4: StoreField: r0->field_13 = r1
    //     0xbb98a4: stur            w1, [x0, #0x13]
    // 0xbb98a8: ldur            d0, [fp, #-0x28]
    // 0xbb98ac: r1 = inline_Allocate_Double()
    //     0xbb98ac: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xbb98b0: add             x1, x1, #0x10
    //     0xbb98b4: cmp             x2, x1
    //     0xbb98b8: b.ls            #0xbb994c
    //     0xbb98bc: str             x1, [THR, #0x50]  ; THR::top
    //     0xbb98c0: sub             x1, x1, #0xf
    //     0xbb98c4: movz            x2, #0xe15c
    //     0xbb98c8: movk            x2, #0x3, lsl #16
    //     0xbb98cc: stur            x2, [x1, #-1]
    // 0xbb98d0: StoreField: r1->field_7 = d0
    //     0xbb98d0: stur            d0, [x1, #7]
    // 0xbb98d4: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb98d4: stur            w1, [x0, #0x17]
    // 0xbb98d8: ldur            x1, [fp, #-0x10]
    // 0xbb98dc: StoreField: r0->field_b = r1
    //     0xbb98dc: stur            w1, [x0, #0xb]
    // 0xbb98e0: LeaveFrame
    //     0xbb98e0: mov             SP, fp
    //     0xbb98e4: ldp             fp, lr, [SP], #0x10
    // 0xbb98e8: ret
    //     0xbb98e8: ret             
    // 0xbb98ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb98ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb98f0: b               #0xbb9724
    // 0xbb98f4: r9 = _value
    //     0xbb98f4: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0xbb98f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb98f8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbb98fc: stp             q2, q3, [SP, #-0x20]!
    // 0xbb9900: stp             q0, q1, [SP, #-0x20]!
    // 0xbb9904: SaveReg r1
    //     0xbb9904: str             x1, [SP, #-8]!
    // 0xbb9908: r0 = AllocateDouble()
    //     0xbb9908: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb990c: RestoreReg r1
    //     0xbb990c: ldr             x1, [SP], #8
    // 0xbb9910: ldp             q0, q1, [SP], #0x20
    // 0xbb9914: ldp             q2, q3, [SP], #0x20
    // 0xbb9918: b               #0xbb9810
    // 0xbb991c: SaveReg d0
    //     0xbb991c: str             q0, [SP, #-0x10]!
    // 0xbb9920: SaveReg r2
    //     0xbb9920: str             x2, [SP, #-8]!
    // 0xbb9924: r0 = AllocateDouble()
    //     0xbb9924: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb9928: RestoreReg r2
    //     0xbb9928: ldr             x2, [SP], #8
    // 0xbb992c: RestoreReg d0
    //     0xbb992c: ldr             q0, [SP], #0x10
    // 0xbb9930: b               #0xbb9854
    // 0xbb9934: SaveReg d0
    //     0xbb9934: str             q0, [SP, #-0x10]!
    // 0xbb9938: SaveReg r2
    //     0xbb9938: str             x2, [SP, #-8]!
    // 0xbb993c: r0 = AllocateDouble()
    //     0xbb993c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb9940: RestoreReg r2
    //     0xbb9940: ldr             x2, [SP], #8
    // 0xbb9944: RestoreReg d0
    //     0xbb9944: ldr             q0, [SP], #0x10
    // 0xbb9948: b               #0xbb988c
    // 0xbb994c: SaveReg d0
    //     0xbb994c: str             q0, [SP, #-0x10]!
    // 0xbb9950: SaveReg r0
    //     0xbb9950: str             x0, [SP, #-8]!
    // 0xbb9954: r0 = AllocateDouble()
    //     0xbb9954: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb9958: mov             x1, x0
    // 0xbb995c: RestoreReg r0
    //     0xbb995c: ldr             x0, [SP], #8
    // 0xbb9960: RestoreReg d0
    //     0xbb9960: ldr             q0, [SP], #0x10
    // 0xbb9964: b               #0xbb98d0
  }
}

// class id: 4919, size: 0x1c, field offset: 0xc
//   const constructor, 
class ReorderableGridDragStartListener extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbb71cc, size: 0x80
    // 0xbb71cc: EnterFrame
    //     0xbb71cc: stp             fp, lr, [SP, #-0x10]!
    //     0xbb71d0: mov             fp, SP
    // 0xbb71d4: AllocStack(0x18)
    //     0xbb71d4: sub             SP, SP, #0x18
    // 0xbb71d8: SetupParameters(ReorderableGridDragStartListener this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbb71d8: stur            x1, [fp, #-8]
    //     0xbb71dc: stur            x2, [fp, #-0x10]
    // 0xbb71e0: r1 = 2
    //     0xbb71e0: movz            x1, #0x2
    // 0xbb71e4: r0 = AllocateContext()
    //     0xbb71e4: bl              #0xec126c  ; AllocateContextStub
    // 0xbb71e8: mov             x1, x0
    // 0xbb71ec: ldur            x0, [fp, #-8]
    // 0xbb71f0: stur            x1, [fp, #-0x18]
    // 0xbb71f4: StoreField: r1->field_f = r0
    //     0xbb71f4: stur            w0, [x1, #0xf]
    // 0xbb71f8: ldur            x2, [fp, #-0x10]
    // 0xbb71fc: StoreField: r1->field_13 = r2
    //     0xbb71fc: stur            w2, [x1, #0x13]
    // 0xbb7200: LoadField: r2 = r0->field_b
    //     0xbb7200: ldur            w2, [x0, #0xb]
    // 0xbb7204: DecompressPointer r2
    //     0xbb7204: add             x2, x2, HEAP, lsl #32
    // 0xbb7208: stur            x2, [fp, #-0x10]
    // 0xbb720c: r0 = Listener()
    //     0xbb720c: bl              #0x9daab0  ; AllocateListenerStub -> Listener (size=0x38)
    // 0xbb7210: ldur            x2, [fp, #-0x18]
    // 0xbb7214: r1 = Function '<anonymous closure>':.
    //     0xbb7214: add             x1, PP, #0x51, lsl #12  ; [pp+0x51388] AnonymousClosure: (0xbb724c), in [package:reorderable_grid/src/reorderable_grid.dart] ReorderableGridDragStartListener::build (0xbb71cc)
    //     0xbb7218: ldr             x1, [x1, #0x388]
    // 0xbb721c: stur            x0, [fp, #-8]
    // 0xbb7220: r0 = AllocateClosure()
    //     0xbb7220: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb7224: mov             x1, x0
    // 0xbb7228: ldur            x0, [fp, #-8]
    // 0xbb722c: StoreField: r0->field_f = r1
    //     0xbb722c: stur            w1, [x0, #0xf]
    // 0xbb7230: r1 = Instance_HitTestBehavior
    //     0xbb7230: ldr             x1, [PP, #0x6c40]  ; [pp+0x6c40] Obj!HitTestBehavior@e358a1
    // 0xbb7234: StoreField: r0->field_33 = r1
    //     0xbb7234: stur            w1, [x0, #0x33]
    // 0xbb7238: ldur            x1, [fp, #-0x10]
    // 0xbb723c: StoreField: r0->field_b = r1
    //     0xbb723c: stur            w1, [x0, #0xb]
    // 0xbb7240: LeaveFrame
    //     0xbb7240: mov             SP, fp
    //     0xbb7244: ldp             fp, lr, [SP], #0x10
    // 0xbb7248: ret
    //     0xbb7248: ret             
  }
  [closure] void <anonymous closure>(dynamic, PointerDownEvent) {
    // ** addr: 0xbb724c, size: 0x54
    // 0xbb724c: EnterFrame
    //     0xbb724c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb7250: mov             fp, SP
    // 0xbb7254: ldr             x0, [fp, #0x18]
    // 0xbb7258: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb7258: ldur            w1, [x0, #0x17]
    // 0xbb725c: DecompressPointer r1
    //     0xbb725c: add             x1, x1, HEAP, lsl #32
    // 0xbb7260: CheckStackOverflow
    //     0xbb7260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb7264: cmp             SP, x16
    //     0xbb7268: b.ls            #0xbb7298
    // 0xbb726c: LoadField: r0 = r1->field_f
    //     0xbb726c: ldur            w0, [x1, #0xf]
    // 0xbb7270: DecompressPointer r0
    //     0xbb7270: add             x0, x0, HEAP, lsl #32
    // 0xbb7274: LoadField: r2 = r1->field_13
    //     0xbb7274: ldur            w2, [x1, #0x13]
    // 0xbb7278: DecompressPointer r2
    //     0xbb7278: add             x2, x2, HEAP, lsl #32
    // 0xbb727c: mov             x1, x0
    // 0xbb7280: ldr             x3, [fp, #0x10]
    // 0xbb7284: r0 = _startDragging()
    //     0xbb7284: bl              #0xbb72a0  ; [package:reorderable_grid/src/reorderable_grid.dart] ReorderableGridDragStartListener::_startDragging
    // 0xbb7288: r0 = Null
    //     0xbb7288: mov             x0, NULL
    // 0xbb728c: LeaveFrame
    //     0xbb728c: mov             SP, fp
    //     0xbb7290: ldp             fp, lr, [SP], #0x10
    // 0xbb7294: ret
    //     0xbb7294: ret             
    // 0xbb7298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7298: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb729c: b               #0xbb726c
  }
  _ _startDragging(/* No info */) {
    // ** addr: 0xbb72a0, size: 0xbc
    // 0xbb72a0: EnterFrame
    //     0xbb72a0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb72a4: mov             fp, SP
    // 0xbb72a8: AllocStack(0x20)
    //     0xbb72a8: sub             SP, SP, #0x20
    // 0xbb72ac: SetupParameters(ReorderableGridDragStartListener this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0xbb72ac: mov             x0, x1
    //     0xbb72b0: stur            x1, [fp, #-8]
    //     0xbb72b4: mov             x1, x2
    //     0xbb72b8: mov             x2, x3
    //     0xbb72bc: stur            x3, [fp, #-0x10]
    // 0xbb72c0: CheckStackOverflow
    //     0xbb72c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb72c4: cmp             SP, x16
    //     0xbb72c8: b.ls            #0xbb7354
    // 0xbb72cc: r0 = maybeOf()
    //     0xbb72cc: bl              #0xbb9570  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGrid::maybeOf
    // 0xbb72d0: stur            x0, [fp, #-0x20]
    // 0xbb72d4: cmp             w0, NULL
    // 0xbb72d8: b.eq            #0xbb7344
    // 0xbb72dc: ldur            x1, [fp, #-8]
    // 0xbb72e0: LoadField: r3 = r1->field_f
    //     0xbb72e0: ldur            x3, [x1, #0xf]
    // 0xbb72e4: stur            x3, [fp, #-0x18]
    // 0xbb72e8: r2 = LoadClassIdInstr(r1)
    //     0xbb72e8: ldur            x2, [x1, #-1]
    //     0xbb72ec: ubfx            x2, x2, #0xc, #0x14
    // 0xbb72f0: r17 = 4919
    //     0xbb72f0: movz            x17, #0x1337
    // 0xbb72f4: cmp             x2, x17
    // 0xbb72f8: b.ne            #0xbb7314
    // 0xbb72fc: r0 = ImmediateMultiDragGestureRecognizer()
    //     0xbb72fc: bl              #0xaa6c20  ; AllocateImmediateMultiDragGestureRecognizerStub -> ImmediateMultiDragGestureRecognizer (size=0x20)
    // 0xbb7300: mov             x1, x0
    // 0xbb7304: stur            x0, [fp, #-8]
    // 0xbb7308: r0 = MultiDragGestureRecognizer()
    //     0xbb7308: bl              #0xaa6b78  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::MultiDragGestureRecognizer
    // 0xbb730c: ldur            x5, [fp, #-8]
    // 0xbb7310: b               #0xbb7334
    // 0xbb7314: r0 = DelayedMultiDragGestureRecognizer()
    //     0xbb7314: bl              #0xaa6b6c  ; AllocateDelayedMultiDragGestureRecognizerStub -> DelayedMultiDragGestureRecognizer (size=0x24)
    // 0xbb7318: mov             x2, x0
    // 0xbb731c: r0 = Instance_Duration
    //     0xbb731c: ldr             x0, [PP, #0x4fa0]  ; [pp+0x4fa0] Obj!Duration@e3a0b1
    // 0xbb7320: stur            x2, [fp, #-8]
    // 0xbb7324: StoreField: r2->field_1f = r0
    //     0xbb7324: stur            w0, [x2, #0x1f]
    // 0xbb7328: mov             x1, x2
    // 0xbb732c: r0 = MultiDragGestureRecognizer()
    //     0xbb732c: bl              #0xaa6b78  ; [package:flutter/src/gestures/multidrag.dart] MultiDragGestureRecognizer::MultiDragGestureRecognizer
    // 0xbb7330: ldur            x5, [fp, #-8]
    // 0xbb7334: ldur            x1, [fp, #-0x20]
    // 0xbb7338: ldur            x2, [fp, #-0x10]
    // 0xbb733c: ldur            x3, [fp, #-0x18]
    // 0xbb7340: r0 = startItemDragReorder()
    //     0xbb7340: bl              #0xbb735c  ; [package:reorderable_grid/src/reorderable_grid.dart] SliverReorderableGridState::startItemDragReorder
    // 0xbb7344: r0 = Null
    //     0xbb7344: mov             x0, NULL
    // 0xbb7348: LeaveFrame
    //     0xbb7348: mov             SP, fp
    //     0xbb734c: ldp             fp, lr, [SP], #0x10
    // 0xbb7350: ret
    //     0xbb7350: ret             
    // 0xbb7354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb7358: b               #0xbb72cc
  }
}

// class id: 4920, size: 0x1c, field offset: 0x1c
//   const constructor, 
class ReorderableGridDelayedDragStartListener extends ReorderableGridDragStartListener {
}
