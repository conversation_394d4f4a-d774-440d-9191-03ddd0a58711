// lib: , url: package:reorderable_grid/src/reorderable_grid_view.dart

// class id: 1051082, size: 0x8
class :: {
}

// class id: 3574, size: 0x18, field offset: 0x10
//   const constructor, 
class _ReorderableGridViewChildGlobalKey extends GlobalObjectKey<dynamic> {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbeb488, size: 0x5c
    // 0xbeb488: EnterFrame
    //     0xbeb488: stp             fp, lr, [SP, #-0x10]!
    //     0xbeb48c: mov             fp, SP
    // 0xbeb490: CheckStackOverflow
    //     0xbeb490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeb494: cmp             SP, x16
    //     0xbeb498: b.ls            #0xbeb4dc
    // 0xbeb49c: ldr             x0, [fp, #0x10]
    // 0xbeb4a0: LoadField: r1 = r0->field_f
    //     0xbeb4a0: ldur            w1, [x0, #0xf]
    // 0xbeb4a4: DecompressPointer r1
    //     0xbeb4a4: add             x1, x1, HEAP, lsl #32
    // 0xbeb4a8: LoadField: r2 = r0->field_13
    //     0xbeb4a8: ldur            w2, [x0, #0x13]
    // 0xbeb4ac: DecompressPointer r2
    //     0xbeb4ac: add             x2, x2, HEAP, lsl #32
    // 0xbeb4b0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbeb4b0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbeb4b4: r0 = hash()
    //     0xbeb4b4: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbeb4b8: mov             x2, x0
    // 0xbeb4bc: r0 = BoxInt64Instr(r2)
    //     0xbeb4bc: sbfiz           x0, x2, #1, #0x1f
    //     0xbeb4c0: cmp             x2, x0, asr #1
    //     0xbeb4c4: b.eq            #0xbeb4d0
    //     0xbeb4c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbeb4cc: stur            x2, [x0, #7]
    // 0xbeb4d0: LeaveFrame
    //     0xbeb4d0: mov             SP, fp
    //     0xbeb4d4: ldp             fp, lr, [SP], #0x10
    // 0xbeb4d8: ret
    //     0xbeb4d8: ret             
    // 0xbeb4dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeb4dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeb4e0: b               #0xbeb49c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd5c17c, size: 0x10c
    // 0xd5c17c: EnterFrame
    //     0xd5c17c: stp             fp, lr, [SP, #-0x10]!
    //     0xd5c180: mov             fp, SP
    // 0xd5c184: AllocStack(0x10)
    //     0xd5c184: sub             SP, SP, #0x10
    // 0xd5c188: CheckStackOverflow
    //     0xd5c188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd5c18c: cmp             SP, x16
    //     0xd5c190: b.ls            #0xd5c280
    // 0xd5c194: ldr             x0, [fp, #0x10]
    // 0xd5c198: cmp             w0, NULL
    // 0xd5c19c: b.ne            #0xd5c1b0
    // 0xd5c1a0: r0 = false
    //     0xd5c1a0: add             x0, NULL, #0x30  ; false
    // 0xd5c1a4: LeaveFrame
    //     0xd5c1a4: mov             SP, fp
    //     0xd5c1a8: ldp             fp, lr, [SP], #0x10
    // 0xd5c1ac: ret
    //     0xd5c1ac: ret             
    // 0xd5c1b0: str             x0, [SP]
    // 0xd5c1b4: r0 = runtimeType()
    //     0xd5c1b4: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd5c1b8: r1 = LoadClassIdInstr(r0)
    //     0xd5c1b8: ldur            x1, [x0, #-1]
    //     0xd5c1bc: ubfx            x1, x1, #0xc, #0x14
    // 0xd5c1c0: r16 = _ReorderableGridViewChildGlobalKey
    //     0xd5c1c0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51380] Type: _ReorderableGridViewChildGlobalKey
    //     0xd5c1c4: ldr             x16, [x16, #0x380]
    // 0xd5c1c8: stp             x16, x0, [SP]
    // 0xd5c1cc: mov             x0, x1
    // 0xd5c1d0: mov             lr, x0
    // 0xd5c1d4: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c1d8: blr             lr
    // 0xd5c1dc: tbz             w0, #4, #0xd5c1f0
    // 0xd5c1e0: r0 = false
    //     0xd5c1e0: add             x0, NULL, #0x30  ; false
    // 0xd5c1e4: LeaveFrame
    //     0xd5c1e4: mov             SP, fp
    //     0xd5c1e8: ldp             fp, lr, [SP], #0x10
    // 0xd5c1ec: ret
    //     0xd5c1ec: ret             
    // 0xd5c1f0: ldr             x1, [fp, #0x10]
    // 0xd5c1f4: r0 = 60
    //     0xd5c1f4: movz            x0, #0x3c
    // 0xd5c1f8: branchIfSmi(r1, 0xd5c204)
    //     0xd5c1f8: tbz             w1, #0, #0xd5c204
    // 0xd5c1fc: r0 = LoadClassIdInstr(r1)
    //     0xd5c1fc: ldur            x0, [x1, #-1]
    //     0xd5c200: ubfx            x0, x0, #0xc, #0x14
    // 0xd5c204: cmp             x0, #0xdf6
    // 0xd5c208: b.ne            #0xd5c270
    // 0xd5c20c: ldr             x2, [fp, #0x18]
    // 0xd5c210: LoadField: r0 = r1->field_f
    //     0xd5c210: ldur            w0, [x1, #0xf]
    // 0xd5c214: DecompressPointer r0
    //     0xd5c214: add             x0, x0, HEAP, lsl #32
    // 0xd5c218: LoadField: r3 = r2->field_f
    //     0xd5c218: ldur            w3, [x2, #0xf]
    // 0xd5c21c: DecompressPointer r3
    //     0xd5c21c: add             x3, x3, HEAP, lsl #32
    // 0xd5c220: r4 = LoadClassIdInstr(r0)
    //     0xd5c220: ldur            x4, [x0, #-1]
    //     0xd5c224: ubfx            x4, x4, #0xc, #0x14
    // 0xd5c228: stp             x3, x0, [SP]
    // 0xd5c22c: mov             x0, x4
    // 0xd5c230: mov             lr, x0
    // 0xd5c234: ldr             lr, [x21, lr, lsl #3]
    // 0xd5c238: blr             lr
    // 0xd5c23c: tbnz            w0, #4, #0xd5c270
    // 0xd5c240: ldr             x2, [fp, #0x18]
    // 0xd5c244: ldr             x1, [fp, #0x10]
    // 0xd5c248: LoadField: r3 = r1->field_13
    //     0xd5c248: ldur            w3, [x1, #0x13]
    // 0xd5c24c: DecompressPointer r3
    //     0xd5c24c: add             x3, x3, HEAP, lsl #32
    // 0xd5c250: LoadField: r1 = r2->field_13
    //     0xd5c250: ldur            w1, [x2, #0x13]
    // 0xd5c254: DecompressPointer r1
    //     0xd5c254: add             x1, x1, HEAP, lsl #32
    // 0xd5c258: cmp             w3, w1
    // 0xd5c25c: r16 = true
    //     0xd5c25c: add             x16, NULL, #0x20  ; true
    // 0xd5c260: r17 = false
    //     0xd5c260: add             x17, NULL, #0x30  ; false
    // 0xd5c264: csel            x2, x16, x17, eq
    // 0xd5c268: mov             x0, x2
    // 0xd5c26c: b               #0xd5c274
    // 0xd5c270: r0 = false
    //     0xd5c270: add             x0, NULL, #0x30  ; false
    // 0xd5c274: LeaveFrame
    //     0xd5c274: mov             SP, fp
    //     0xd5c278: ldp             fp, lr, [SP], #0x10
    // 0xd5c27c: ret
    //     0xd5c27c: ret             
    // 0xd5c280: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd5c280: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd5c284: b               #0xd5c194
  }
}

// class id: 4097, size: 0x14, field offset: 0x14
class ReorderableGridViewState extends State<dynamic> {

  [closure] Widget _itemBuilder(dynamic, BuildContext, int) {
    // ** addr: 0xa4a6a8, size: 0x40
    // 0xa4a6a8: EnterFrame
    //     0xa4a6a8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4a6ac: mov             fp, SP
    // 0xa4a6b0: ldr             x0, [fp, #0x20]
    // 0xa4a6b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4a6b4: ldur            w1, [x0, #0x17]
    // 0xa4a6b8: DecompressPointer r1
    //     0xa4a6b8: add             x1, x1, HEAP, lsl #32
    // 0xa4a6bc: CheckStackOverflow
    //     0xa4a6bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4a6c0: cmp             SP, x16
    //     0xa4a6c4: b.ls            #0xa4a6e0
    // 0xa4a6c8: ldr             x2, [fp, #0x18]
    // 0xa4a6cc: ldr             x3, [fp, #0x10]
    // 0xa4a6d0: r0 = _itemBuilder()
    //     0xa4a6d0: bl              #0xa4a724  ; [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridViewState::_itemBuilder
    // 0xa4a6d4: LeaveFrame
    //     0xa4a6d4: mov             SP, fp
    //     0xa4a6d8: ldp             fp, lr, [SP], #0x10
    // 0xa4a6dc: ret
    //     0xa4a6dc: ret             
    // 0xa4a6e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a6e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a6e4: b               #0xa4a6c8
  }
  _ _itemBuilder(/* No info */) {
    // ** addr: 0xa4a724, size: 0x1a4
    // 0xa4a724: EnterFrame
    //     0xa4a724: stp             fp, lr, [SP, #-0x10]!
    //     0xa4a728: mov             fp, SP
    // 0xa4a72c: AllocStack(0x48)
    //     0xa4a72c: sub             SP, SP, #0x48
    // 0xa4a730: SetupParameters(ReorderableGridViewState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa4a730: stur            x1, [fp, #-8]
    //     0xa4a734: mov             x16, x2
    //     0xa4a738: mov             x2, x1
    //     0xa4a73c: mov             x1, x16
    //     0xa4a740: stur            x1, [fp, #-0x10]
    //     0xa4a744: stur            x3, [fp, #-0x18]
    // 0xa4a748: CheckStackOverflow
    //     0xa4a748: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4a74c: cmp             SP, x16
    //     0xa4a750: b.ls            #0xa4a8b4
    // 0xa4a754: LoadField: r0 = r2->field_b
    //     0xa4a754: ldur            w0, [x2, #0xb]
    // 0xa4a758: DecompressPointer r0
    //     0xa4a758: add             x0, x0, HEAP, lsl #32
    // 0xa4a75c: cmp             w0, NULL
    // 0xa4a760: b.eq            #0xa4a8bc
    // 0xa4a764: LoadField: r4 = r0->field_47
    //     0xa4a764: ldur            w4, [x0, #0x47]
    // 0xa4a768: DecompressPointer r4
    //     0xa4a768: add             x4, x4, HEAP, lsl #32
    // 0xa4a76c: stp             x1, x4, [SP, #8]
    // 0xa4a770: str             x3, [SP]
    // 0xa4a774: mov             x0, x4
    // 0xa4a778: ClosureCall
    //     0xa4a778: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa4a77c: ldur            x2, [x0, #0x1f]
    //     0xa4a780: blr             x2
    // 0xa4a784: mov             x4, x0
    // 0xa4a788: ldur            x0, [fp, #-0x18]
    // 0xa4a78c: stur            x4, [fp, #-0x28]
    // 0xa4a790: r5 = LoadInt32Instr(r0)
    //     0xa4a790: sbfx            x5, x0, #1, #0x1f
    //     0xa4a794: tbz             w0, #0, #0xa4a79c
    //     0xa4a798: ldur            x5, [x0, #7]
    // 0xa4a79c: ldur            x1, [fp, #-8]
    // 0xa4a7a0: mov             x2, x4
    // 0xa4a7a4: mov             x3, x5
    // 0xa4a7a8: stur            x5, [fp, #-0x20]
    // 0xa4a7ac: r0 = _wrapWithSemantics()
    //     0xa4a7ac: bl              #0xa4a8ec  ; [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridViewState::_wrapWithSemantics
    // 0xa4a7b0: mov             x2, x0
    // 0xa4a7b4: ldur            x1, [fp, #-0x28]
    // 0xa4a7b8: stur            x2, [fp, #-0x18]
    // 0xa4a7bc: r0 = LoadClassIdInstr(r1)
    //     0xa4a7bc: ldur            x0, [x1, #-1]
    //     0xa4a7c0: ubfx            x0, x0, #0xc, #0x14
    // 0xa4a7c4: r0 = GDT[cid_x0 + 0xaa0c]()
    //     0xa4a7c4: movz            x17, #0xaa0c
    //     0xa4a7c8: add             lr, x0, x17
    //     0xa4a7cc: ldr             lr, [x21, lr, lsl #3]
    //     0xa4a7d0: blr             lr
    // 0xa4a7d4: stur            x0, [fp, #-0x28]
    // 0xa4a7d8: cmp             w0, NULL
    // 0xa4a7dc: b.eq            #0xa4a8c0
    // 0xa4a7e0: r1 = <State<StatefulWidget>>
    //     0xa4a7e0: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa4a7e4: r0 = _ReorderableGridViewChildGlobalKey()
    //     0xa4a7e4: bl              #0xa4a8e0  ; Allocate_ReorderableGridViewChildGlobalKeyStub -> _ReorderableGridViewChildGlobalKey (size=0x18)
    // 0xa4a7e8: mov             x2, x0
    // 0xa4a7ec: ldur            x0, [fp, #-0x28]
    // 0xa4a7f0: stur            x2, [fp, #-0x30]
    // 0xa4a7f4: StoreField: r2->field_f = r0
    //     0xa4a7f4: stur            w0, [x2, #0xf]
    // 0xa4a7f8: ldur            x1, [fp, #-8]
    // 0xa4a7fc: StoreField: r2->field_13 = r1
    //     0xa4a7fc: stur            w1, [x2, #0x13]
    // 0xa4a800: StoreField: r2->field_b = r0
    //     0xa4a800: stur            w0, [x2, #0xb]
    // 0xa4a804: LoadField: r0 = r1->field_b
    //     0xa4a804: ldur            w0, [x1, #0xb]
    // 0xa4a808: DecompressPointer r0
    //     0xa4a808: add             x0, x0, HEAP, lsl #32
    // 0xa4a80c: cmp             w0, NULL
    // 0xa4a810: b.eq            #0xa4a8c4
    // 0xa4a814: ldur            x1, [fp, #-0x10]
    // 0xa4a818: r0 = of()
    //     0xa4a818: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa4a81c: LoadField: r1 = r0->field_23
    //     0xa4a81c: ldur            w1, [x0, #0x23]
    // 0xa4a820: DecompressPointer r1
    //     0xa4a820: add             x1, x1, HEAP, lsl #32
    // 0xa4a824: LoadField: r0 = r1->field_7
    //     0xa4a824: ldur            x0, [x1, #7]
    // 0xa4a828: cmp             x0, #2
    // 0xa4a82c: b.gt            #0xa4a874
    // 0xa4a830: ldur            x1, [fp, #-0x18]
    // 0xa4a834: ldur            x0, [fp, #-0x30]
    // 0xa4a838: ldur            x2, [fp, #-0x20]
    // 0xa4a83c: r0 = ReorderableGridDelayedDragStartListener()
    //     0xa4a83c: bl              #0xa4a8d4  ; AllocateReorderableGridDelayedDragStartListenerStub -> ReorderableGridDelayedDragStartListener (size=0x1c)
    // 0xa4a840: mov             x1, x0
    // 0xa4a844: ldur            x0, [fp, #-0x18]
    // 0xa4a848: StoreField: r1->field_b = r0
    //     0xa4a848: stur            w0, [x1, #0xb]
    // 0xa4a84c: ldur            x2, [fp, #-0x20]
    // 0xa4a850: StoreField: r1->field_f = r2
    //     0xa4a850: stur            x2, [x1, #0xf]
    // 0xa4a854: r3 = true
    //     0xa4a854: add             x3, NULL, #0x20  ; true
    // 0xa4a858: ArrayStore: r1[0] = r3  ; List_4
    //     0xa4a858: stur            w3, [x1, #0x17]
    // 0xa4a85c: ldur            x4, [fp, #-0x30]
    // 0xa4a860: StoreField: r1->field_7 = r4
    //     0xa4a860: stur            w4, [x1, #7]
    // 0xa4a864: mov             x0, x1
    // 0xa4a868: LeaveFrame
    //     0xa4a868: mov             SP, fp
    //     0xa4a86c: ldp             fp, lr, [SP], #0x10
    // 0xa4a870: ret
    //     0xa4a870: ret             
    // 0xa4a874: ldur            x0, [fp, #-0x18]
    // 0xa4a878: ldur            x4, [fp, #-0x30]
    // 0xa4a87c: ldur            x2, [fp, #-0x20]
    // 0xa4a880: r3 = true
    //     0xa4a880: add             x3, NULL, #0x20  ; true
    // 0xa4a884: r0 = ReorderableGridDragStartListener()
    //     0xa4a884: bl              #0xa4a8c8  ; AllocateReorderableGridDragStartListenerStub -> ReorderableGridDragStartListener (size=0x1c)
    // 0xa4a888: ldur            x1, [fp, #-0x18]
    // 0xa4a88c: StoreField: r0->field_b = r1
    //     0xa4a88c: stur            w1, [x0, #0xb]
    // 0xa4a890: ldur            x1, [fp, #-0x20]
    // 0xa4a894: StoreField: r0->field_f = r1
    //     0xa4a894: stur            x1, [x0, #0xf]
    // 0xa4a898: r1 = true
    //     0xa4a898: add             x1, NULL, #0x20  ; true
    // 0xa4a89c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa4a89c: stur            w1, [x0, #0x17]
    // 0xa4a8a0: ldur            x1, [fp, #-0x30]
    // 0xa4a8a4: StoreField: r0->field_7 = r1
    //     0xa4a8a4: stur            w1, [x0, #7]
    // 0xa4a8a8: LeaveFrame
    //     0xa4a8a8: mov             SP, fp
    //     0xa4a8ac: ldp             fp, lr, [SP], #0x10
    // 0xa4a8b0: ret
    //     0xa4a8b0: ret             
    // 0xa4a8b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a8b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a8b8: b               #0xa4a754
    // 0xa4a8bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a8bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a8c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a8c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a8c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a8c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _wrapWithSemantics(/* No info */) {
    // ** addr: 0xa4a8ec, size: 0x244
    // 0xa4a8ec: EnterFrame
    //     0xa4a8ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa4a8f0: mov             fp, SP
    // 0xa4a8f4: AllocStack(0x40)
    //     0xa4a8f4: sub             SP, SP, #0x40
    // 0xa4a8f8: SetupParameters(ReorderableGridViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa4a8f8: stur            x1, [fp, #-8]
    //     0xa4a8fc: stur            x2, [fp, #-0x10]
    //     0xa4a900: stur            x3, [fp, #-0x18]
    // 0xa4a904: CheckStackOverflow
    //     0xa4a904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4a908: cmp             SP, x16
    //     0xa4a90c: b.ls            #0xa4ab1c
    // 0xa4a910: r1 = 3
    //     0xa4a910: movz            x1, #0x3
    // 0xa4a914: r0 = AllocateContext()
    //     0xa4a914: bl              #0xec126c  ; AllocateContextStub
    // 0xa4a918: mov             x4, x0
    // 0xa4a91c: ldur            x3, [fp, #-8]
    // 0xa4a920: stur            x4, [fp, #-0x20]
    // 0xa4a924: StoreField: r4->field_f = r3
    //     0xa4a924: stur            w3, [x4, #0xf]
    // 0xa4a928: ldur            x2, [fp, #-0x18]
    // 0xa4a92c: r0 = BoxInt64Instr(r2)
    //     0xa4a92c: sbfiz           x0, x2, #1, #0x1f
    //     0xa4a930: cmp             x2, x0, asr #1
    //     0xa4a934: b.eq            #0xa4a940
    //     0xa4a938: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4a93c: stur            x2, [x0, #7]
    // 0xa4a940: StoreField: r4->field_13 = r0
    //     0xa4a940: stur            w0, [x4, #0x13]
    // 0xa4a944: mov             x2, x4
    // 0xa4a948: r1 = Function 'reorder':.
    //     0xa4a948: add             x1, PP, #0x47, lsl #12  ; [pp+0x479a8] AnonymousClosure: (0xa4aeb4), in [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridViewState::_wrapWithSemantics (0xa4a8ec)
    //     0xa4a94c: ldr             x1, [x1, #0x9a8]
    // 0xa4a950: r0 = AllocateClosure()
    //     0xa4a950: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4a954: ldur            x2, [fp, #-0x20]
    // 0xa4a958: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4a958: stur            w0, [x2, #0x17]
    // 0xa4a95c: r16 = <CustomSemanticsAction, (dynamic this) => void?>
    //     0xa4a95c: ldr             x16, [PP, #0x2a80]  ; [pp+0x2a80] TypeArguments: <CustomSemanticsAction, (dynamic this) => void?>
    // 0xa4a960: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa4a964: stp             lr, x16, [SP]
    // 0xa4a968: r0 = Map._fromLiteral()
    //     0xa4a968: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa4a96c: mov             x2, x0
    // 0xa4a970: ldur            x0, [fp, #-8]
    // 0xa4a974: stur            x2, [fp, #-0x28]
    // 0xa4a978: LoadField: r1 = r0->field_f
    //     0xa4a978: ldur            w1, [x0, #0xf]
    // 0xa4a97c: DecompressPointer r1
    //     0xa4a97c: add             x1, x1, HEAP, lsl #32
    // 0xa4a980: cmp             w1, NULL
    // 0xa4a984: b.eq            #0xa4ab24
    // 0xa4a988: r0 = of()
    //     0xa4a988: bl              #0x9179e4  ; [package:flutter/src/material/material_localizations.dart] MaterialLocalizations::of
    // 0xa4a98c: ldur            x2, [fp, #-0x20]
    // 0xa4a990: LoadField: r0 = r2->field_13
    //     0xa4a990: ldur            w0, [x2, #0x13]
    // 0xa4a994: DecompressPointer r0
    //     0xa4a994: add             x0, x0, HEAP, lsl #32
    // 0xa4a998: r1 = LoadInt32Instr(r0)
    //     0xa4a998: sbfx            x1, x0, #1, #0x1f
    //     0xa4a99c: tbz             w0, #0, #0xa4a9a4
    //     0xa4a9a0: ldur            x1, [x0, #7]
    // 0xa4a9a4: cmp             x1, #0
    // 0xa4a9a8: b.le            #0xa4aa34
    // 0xa4a9ac: ldur            x0, [fp, #-8]
    // 0xa4a9b0: r0 = CustomSemanticsAction()
    //     0xa4a9b0: bl              #0x6cb468  ; AllocateCustomSemanticsActionStub -> CustomSemanticsAction (size=0x14)
    // 0xa4a9b4: mov             x3, x0
    // 0xa4a9b8: r0 = "Move to the start"
    //     0xa4a9b8: add             x0, PP, #0x47, lsl #12  ; [pp+0x479b0] "Move to the start"
    //     0xa4a9bc: ldr             x0, [x0, #0x9b0]
    // 0xa4a9c0: stur            x3, [fp, #-0x30]
    // 0xa4a9c4: StoreField: r3->field_7 = r0
    //     0xa4a9c4: stur            w0, [x3, #7]
    // 0xa4a9c8: ldur            x2, [fp, #-0x20]
    // 0xa4a9cc: r1 = Function 'moveToStart':.
    //     0xa4a9cc: add             x1, PP, #0x47, lsl #12  ; [pp+0x479b8] AnonymousClosure: (0xa4ae2c), in [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridViewState::_wrapWithSemantics (0xa4a8ec)
    //     0xa4a9d0: ldr             x1, [x1, #0x9b8]
    // 0xa4a9d4: r0 = AllocateClosure()
    //     0xa4a9d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4a9d8: ldur            x1, [fp, #-0x28]
    // 0xa4a9dc: ldur            x2, [fp, #-0x30]
    // 0xa4a9e0: mov             x3, x0
    // 0xa4a9e4: r0 = []=()
    //     0xa4a9e4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa4a9e8: ldur            x0, [fp, #-8]
    // 0xa4a9ec: LoadField: r1 = r0->field_b
    //     0xa4a9ec: ldur            w1, [x0, #0xb]
    // 0xa4a9f0: DecompressPointer r1
    //     0xa4a9f0: add             x1, x1, HEAP, lsl #32
    // 0xa4a9f4: cmp             w1, NULL
    // 0xa4a9f8: b.eq            #0xa4ab28
    // 0xa4a9fc: r0 = CustomSemanticsAction()
    //     0xa4a9fc: bl              #0x6cb468  ; AllocateCustomSemanticsActionStub -> CustomSemanticsAction (size=0x14)
    // 0xa4aa00: mov             x3, x0
    // 0xa4aa04: r0 = "Move up"
    //     0xa4aa04: add             x0, PP, #0x47, lsl #12  ; [pp+0x479c0] "Move up"
    //     0xa4aa08: ldr             x0, [x0, #0x9c0]
    // 0xa4aa0c: stur            x3, [fp, #-0x30]
    // 0xa4aa10: StoreField: r3->field_7 = r0
    //     0xa4aa10: stur            w0, [x3, #7]
    // 0xa4aa14: ldur            x2, [fp, #-0x20]
    // 0xa4aa18: r1 = Function 'moveBefore':.
    //     0xa4aa18: add             x1, PP, #0x47, lsl #12  ; [pp+0x479c8] AnonymousClosure: (0xa4ad78), in [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridViewState::_wrapWithSemantics (0xa4a8ec)
    //     0xa4aa1c: ldr             x1, [x1, #0x9c8]
    // 0xa4aa20: r0 = AllocateClosure()
    //     0xa4aa20: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4aa24: ldur            x1, [fp, #-0x28]
    // 0xa4aa28: ldur            x2, [fp, #-0x30]
    // 0xa4aa2c: mov             x3, x0
    // 0xa4aa30: r0 = []=()
    //     0xa4aa30: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa4aa34: ldur            x0, [fp, #-8]
    // 0xa4aa38: ldur            x2, [fp, #-0x20]
    // 0xa4aa3c: LoadField: r1 = r2->field_13
    //     0xa4aa3c: ldur            w1, [x2, #0x13]
    // 0xa4aa40: DecompressPointer r1
    //     0xa4aa40: add             x1, x1, HEAP, lsl #32
    // 0xa4aa44: LoadField: r3 = r0->field_b
    //     0xa4aa44: ldur            w3, [x0, #0xb]
    // 0xa4aa48: DecompressPointer r3
    //     0xa4aa48: add             x3, x3, HEAP, lsl #32
    // 0xa4aa4c: cmp             w3, NULL
    // 0xa4aa50: b.eq            #0xa4ab2c
    // 0xa4aa54: LoadField: r0 = r3->field_4f
    //     0xa4aa54: ldur            x0, [x3, #0x4f]
    // 0xa4aa58: sub             x3, x0, #1
    // 0xa4aa5c: r0 = LoadInt32Instr(r1)
    //     0xa4aa5c: sbfx            x0, x1, #1, #0x1f
    //     0xa4aa60: tbz             w1, #0, #0xa4aa68
    //     0xa4aa64: ldur            x0, [x1, #7]
    // 0xa4aa68: cmp             x0, x3
    // 0xa4aa6c: b.ge            #0xa4aae0
    // 0xa4aa70: r0 = CustomSemanticsAction()
    //     0xa4aa70: bl              #0x6cb468  ; AllocateCustomSemanticsActionStub -> CustomSemanticsAction (size=0x14)
    // 0xa4aa74: mov             x3, x0
    // 0xa4aa78: r0 = "Move down"
    //     0xa4aa78: add             x0, PP, #0x47, lsl #12  ; [pp+0x479d0] "Move down"
    //     0xa4aa7c: ldr             x0, [x0, #0x9d0]
    // 0xa4aa80: stur            x3, [fp, #-8]
    // 0xa4aa84: StoreField: r3->field_7 = r0
    //     0xa4aa84: stur            w0, [x3, #7]
    // 0xa4aa88: ldur            x2, [fp, #-0x20]
    // 0xa4aa8c: r1 = Function 'moveAfter':.
    //     0xa4aa8c: add             x1, PP, #0x47, lsl #12  ; [pp+0x479d8] AnonymousClosure: (0xa4acc4), in [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridViewState::_wrapWithSemantics (0xa4a8ec)
    //     0xa4aa90: ldr             x1, [x1, #0x9d8]
    // 0xa4aa94: r0 = AllocateClosure()
    //     0xa4aa94: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4aa98: ldur            x1, [fp, #-0x28]
    // 0xa4aa9c: ldur            x2, [fp, #-8]
    // 0xa4aaa0: mov             x3, x0
    // 0xa4aaa4: r0 = []=()
    //     0xa4aaa4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa4aaa8: r0 = CustomSemanticsAction()
    //     0xa4aaa8: bl              #0x6cb468  ; AllocateCustomSemanticsActionStub -> CustomSemanticsAction (size=0x14)
    // 0xa4aaac: mov             x3, x0
    // 0xa4aab0: r0 = "Move to the end"
    //     0xa4aab0: add             x0, PP, #0x47, lsl #12  ; [pp+0x479e0] "Move to the end"
    //     0xa4aab4: ldr             x0, [x0, #0x9e0]
    // 0xa4aab8: stur            x3, [fp, #-8]
    // 0xa4aabc: StoreField: r3->field_7 = r0
    //     0xa4aabc: stur            w0, [x3, #7]
    // 0xa4aac0: ldur            x2, [fp, #-0x20]
    // 0xa4aac4: r1 = Function 'moveToEnd':.
    //     0xa4aac4: add             x1, PP, #0x47, lsl #12  ; [pp+0x479e8] AnonymousClosure: (0xa4ab3c), in [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridViewState::_wrapWithSemantics (0xa4a8ec)
    //     0xa4aac8: ldr             x1, [x1, #0x9e8]
    // 0xa4aacc: r0 = AllocateClosure()
    //     0xa4aacc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4aad0: ldur            x1, [fp, #-0x28]
    // 0xa4aad4: ldur            x2, [fp, #-8]
    // 0xa4aad8: mov             x3, x0
    // 0xa4aadc: r0 = []=()
    //     0xa4aadc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa4aae0: r0 = Semantics()
    //     0xa4aae0: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xa4aae4: stur            x0, [fp, #-8]
    // 0xa4aae8: ldur            x16, [fp, #-0x28]
    // 0xa4aaec: ldur            lr, [fp, #-0x10]
    // 0xa4aaf0: stp             lr, x16, [SP]
    // 0xa4aaf4: mov             x1, x0
    // 0xa4aaf8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, customSemanticsActions, 0x1, null]
    //     0xa4aaf8: add             x4, PP, #0x47, lsl #12  ; [pp+0x479f0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "customSemanticsActions", 0x1, Null]
    //     0xa4aafc: ldr             x4, [x4, #0x9f0]
    // 0xa4ab00: r0 = Semantics()
    //     0xa4ab00: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xa4ab04: r0 = MergeSemantics()
    //     0xa4ab04: bl              #0xa4ab30  ; AllocateMergeSemanticsStub -> MergeSemantics (size=0x10)
    // 0xa4ab08: ldur            x1, [fp, #-8]
    // 0xa4ab0c: StoreField: r0->field_b = r1
    //     0xa4ab0c: stur            w1, [x0, #0xb]
    // 0xa4ab10: LeaveFrame
    //     0xa4ab10: mov             SP, fp
    //     0xa4ab14: ldp             fp, lr, [SP], #0x10
    // 0xa4ab18: ret
    //     0xa4ab18: ret             
    // 0xa4ab1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ab1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ab20: b               #0xa4a910
    // 0xa4ab24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ab24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ab28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ab28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ab2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ab2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void moveToEnd(dynamic) {
    // ** addr: 0xa4ab3c, size: 0xd0
    // 0xa4ab3c: EnterFrame
    //     0xa4ab3c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ab40: mov             fp, SP
    // 0xa4ab44: ldr             x0, [fp, #0x10]
    // 0xa4ab48: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4ab48: ldur            w1, [x0, #0x17]
    // 0xa4ab4c: DecompressPointer r1
    //     0xa4ab4c: add             x1, x1, HEAP, lsl #32
    // 0xa4ab50: CheckStackOverflow
    //     0xa4ab50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4ab54: cmp             SP, x16
    //     0xa4ab58: b.ls            #0xa4abfc
    // 0xa4ab5c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa4ab5c: ldur            w0, [x1, #0x17]
    // 0xa4ab60: DecompressPointer r0
    //     0xa4ab60: add             x0, x0, HEAP, lsl #32
    // 0xa4ab64: LoadField: r2 = r1->field_13
    //     0xa4ab64: ldur            w2, [x1, #0x13]
    // 0xa4ab68: DecompressPointer r2
    //     0xa4ab68: add             x2, x2, HEAP, lsl #32
    // 0xa4ab6c: LoadField: r3 = r1->field_f
    //     0xa4ab6c: ldur            w3, [x1, #0xf]
    // 0xa4ab70: DecompressPointer r3
    //     0xa4ab70: add             x3, x3, HEAP, lsl #32
    // 0xa4ab74: LoadField: r1 = r3->field_b
    //     0xa4ab74: ldur            w1, [x3, #0xb]
    // 0xa4ab78: DecompressPointer r1
    //     0xa4ab78: add             x1, x1, HEAP, lsl #32
    // 0xa4ab7c: cmp             w1, NULL
    // 0xa4ab80: b.eq            #0xa4ac04
    // 0xa4ab84: LoadField: r3 = r1->field_4f
    //     0xa4ab84: ldur            x3, [x1, #0x4f]
    // 0xa4ab88: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4ab88: ldur            w1, [x0, #0x17]
    // 0xa4ab8c: DecompressPointer r1
    //     0xa4ab8c: add             x1, x1, HEAP, lsl #32
    // 0xa4ab90: r0 = LoadInt32Instr(r2)
    //     0xa4ab90: sbfx            x0, x2, #1, #0x1f
    //     0xa4ab94: tbz             w2, #0, #0xa4ab9c
    //     0xa4ab98: ldur            x0, [x2, #7]
    // 0xa4ab9c: cmp             x0, x3
    // 0xa4aba0: b.eq            #0xa4abec
    // 0xa4aba4: LoadField: r0 = r1->field_f
    //     0xa4aba4: ldur            w0, [x1, #0xf]
    // 0xa4aba8: DecompressPointer r0
    //     0xa4aba8: add             x0, x0, HEAP, lsl #32
    // 0xa4abac: LoadField: r1 = r0->field_b
    //     0xa4abac: ldur            w1, [x0, #0xb]
    // 0xa4abb0: DecompressPointer r1
    //     0xa4abb0: add             x1, x1, HEAP, lsl #32
    // 0xa4abb4: cmp             w1, NULL
    // 0xa4abb8: b.eq            #0xa4ac08
    // 0xa4abbc: LoadField: r0 = r1->field_57
    //     0xa4abbc: ldur            w0, [x1, #0x57]
    // 0xa4abc0: DecompressPointer r0
    //     0xa4abc0: add             x0, x0, HEAP, lsl #32
    // 0xa4abc4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa4abc4: ldur            w4, [x0, #0x17]
    // 0xa4abc8: DecompressPointer r4
    //     0xa4abc8: add             x4, x4, HEAP, lsl #32
    // 0xa4abcc: r0 = BoxInt64Instr(r3)
    //     0xa4abcc: sbfiz           x0, x3, #1, #0x1f
    //     0xa4abd0: cmp             x3, x0, asr #1
    //     0xa4abd4: b.eq            #0xa4abe0
    //     0xa4abd8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4abdc: stur            x3, [x0, #7]
    // 0xa4abe0: mov             x1, x4
    // 0xa4abe4: mov             x3, x0
    // 0xa4abe8: r0 = reorder()
    //     0xa4abe8: bl              #0xa4ac4c  ; [package:nuonline/app/modules/menu/controllers/menu_controller.dart] MenuController::reorder
    // 0xa4abec: r0 = Null
    //     0xa4abec: mov             x0, NULL
    // 0xa4abf0: LeaveFrame
    //     0xa4abf0: mov             SP, fp
    //     0xa4abf4: ldp             fp, lr, [SP], #0x10
    // 0xa4abf8: ret
    //     0xa4abf8: ret             
    // 0xa4abfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4abfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ac00: b               #0xa4ab5c
    // 0xa4ac04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ac04: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ac08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ac08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void moveAfter(dynamic) {
    // ** addr: 0xa4acc4, size: 0xb4
    // 0xa4acc4: EnterFrame
    //     0xa4acc4: stp             fp, lr, [SP, #-0x10]!
    //     0xa4acc8: mov             fp, SP
    // 0xa4accc: ldr             x0, [fp, #0x10]
    // 0xa4acd0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4acd0: ldur            w1, [x0, #0x17]
    // 0xa4acd4: DecompressPointer r1
    //     0xa4acd4: add             x1, x1, HEAP, lsl #32
    // 0xa4acd8: CheckStackOverflow
    //     0xa4acd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4acdc: cmp             SP, x16
    //     0xa4ace0: b.ls            #0xa4ad6c
    // 0xa4ace4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa4ace4: ldur            w0, [x1, #0x17]
    // 0xa4ace8: DecompressPointer r0
    //     0xa4ace8: add             x0, x0, HEAP, lsl #32
    // 0xa4acec: LoadField: r2 = r1->field_13
    //     0xa4acec: ldur            w2, [x1, #0x13]
    // 0xa4acf0: DecompressPointer r2
    //     0xa4acf0: add             x2, x2, HEAP, lsl #32
    // 0xa4acf4: r1 = LoadInt32Instr(r2)
    //     0xa4acf4: sbfx            x1, x2, #1, #0x1f
    //     0xa4acf8: tbz             w2, #0, #0xa4ad00
    //     0xa4acfc: ldur            x1, [x2, #7]
    // 0xa4ad00: add             x3, x1, #2
    // 0xa4ad04: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa4ad04: ldur            w4, [x0, #0x17]
    // 0xa4ad08: DecompressPointer r4
    //     0xa4ad08: add             x4, x4, HEAP, lsl #32
    // 0xa4ad0c: cmp             x1, x3
    // 0xa4ad10: b.eq            #0xa4ad5c
    // 0xa4ad14: LoadField: r0 = r4->field_f
    //     0xa4ad14: ldur            w0, [x4, #0xf]
    // 0xa4ad18: DecompressPointer r0
    //     0xa4ad18: add             x0, x0, HEAP, lsl #32
    // 0xa4ad1c: LoadField: r1 = r0->field_b
    //     0xa4ad1c: ldur            w1, [x0, #0xb]
    // 0xa4ad20: DecompressPointer r1
    //     0xa4ad20: add             x1, x1, HEAP, lsl #32
    // 0xa4ad24: cmp             w1, NULL
    // 0xa4ad28: b.eq            #0xa4ad74
    // 0xa4ad2c: LoadField: r0 = r1->field_57
    //     0xa4ad2c: ldur            w0, [x1, #0x57]
    // 0xa4ad30: DecompressPointer r0
    //     0xa4ad30: add             x0, x0, HEAP, lsl #32
    // 0xa4ad34: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa4ad34: ldur            w4, [x0, #0x17]
    // 0xa4ad38: DecompressPointer r4
    //     0xa4ad38: add             x4, x4, HEAP, lsl #32
    // 0xa4ad3c: r0 = BoxInt64Instr(r3)
    //     0xa4ad3c: sbfiz           x0, x3, #1, #0x1f
    //     0xa4ad40: cmp             x3, x0, asr #1
    //     0xa4ad44: b.eq            #0xa4ad50
    //     0xa4ad48: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4ad4c: stur            x3, [x0, #7]
    // 0xa4ad50: mov             x1, x4
    // 0xa4ad54: mov             x3, x0
    // 0xa4ad58: r0 = reorder()
    //     0xa4ad58: bl              #0xa4ac4c  ; [package:nuonline/app/modules/menu/controllers/menu_controller.dart] MenuController::reorder
    // 0xa4ad5c: r0 = Null
    //     0xa4ad5c: mov             x0, NULL
    // 0xa4ad60: LeaveFrame
    //     0xa4ad60: mov             SP, fp
    //     0xa4ad64: ldp             fp, lr, [SP], #0x10
    // 0xa4ad68: ret
    //     0xa4ad68: ret             
    // 0xa4ad6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ad6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ad70: b               #0xa4ace4
    // 0xa4ad74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ad74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void moveBefore(dynamic) {
    // ** addr: 0xa4ad78, size: 0xb4
    // 0xa4ad78: EnterFrame
    //     0xa4ad78: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ad7c: mov             fp, SP
    // 0xa4ad80: ldr             x0, [fp, #0x10]
    // 0xa4ad84: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4ad84: ldur            w1, [x0, #0x17]
    // 0xa4ad88: DecompressPointer r1
    //     0xa4ad88: add             x1, x1, HEAP, lsl #32
    // 0xa4ad8c: CheckStackOverflow
    //     0xa4ad8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4ad90: cmp             SP, x16
    //     0xa4ad94: b.ls            #0xa4ae20
    // 0xa4ad98: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa4ad98: ldur            w0, [x1, #0x17]
    // 0xa4ad9c: DecompressPointer r0
    //     0xa4ad9c: add             x0, x0, HEAP, lsl #32
    // 0xa4ada0: LoadField: r2 = r1->field_13
    //     0xa4ada0: ldur            w2, [x1, #0x13]
    // 0xa4ada4: DecompressPointer r2
    //     0xa4ada4: add             x2, x2, HEAP, lsl #32
    // 0xa4ada8: r1 = LoadInt32Instr(r2)
    //     0xa4ada8: sbfx            x1, x2, #1, #0x1f
    //     0xa4adac: tbz             w2, #0, #0xa4adb4
    //     0xa4adb0: ldur            x1, [x2, #7]
    // 0xa4adb4: sub             x3, x1, #1
    // 0xa4adb8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa4adb8: ldur            w4, [x0, #0x17]
    // 0xa4adbc: DecompressPointer r4
    //     0xa4adbc: add             x4, x4, HEAP, lsl #32
    // 0xa4adc0: cmp             x1, x3
    // 0xa4adc4: b.eq            #0xa4ae10
    // 0xa4adc8: LoadField: r0 = r4->field_f
    //     0xa4adc8: ldur            w0, [x4, #0xf]
    // 0xa4adcc: DecompressPointer r0
    //     0xa4adcc: add             x0, x0, HEAP, lsl #32
    // 0xa4add0: LoadField: r1 = r0->field_b
    //     0xa4add0: ldur            w1, [x0, #0xb]
    // 0xa4add4: DecompressPointer r1
    //     0xa4add4: add             x1, x1, HEAP, lsl #32
    // 0xa4add8: cmp             w1, NULL
    // 0xa4addc: b.eq            #0xa4ae28
    // 0xa4ade0: LoadField: r0 = r1->field_57
    //     0xa4ade0: ldur            w0, [x1, #0x57]
    // 0xa4ade4: DecompressPointer r0
    //     0xa4ade4: add             x0, x0, HEAP, lsl #32
    // 0xa4ade8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa4ade8: ldur            w4, [x0, #0x17]
    // 0xa4adec: DecompressPointer r4
    //     0xa4adec: add             x4, x4, HEAP, lsl #32
    // 0xa4adf0: r0 = BoxInt64Instr(r3)
    //     0xa4adf0: sbfiz           x0, x3, #1, #0x1f
    //     0xa4adf4: cmp             x3, x0, asr #1
    //     0xa4adf8: b.eq            #0xa4ae04
    //     0xa4adfc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4ae00: stur            x3, [x0, #7]
    // 0xa4ae04: mov             x1, x4
    // 0xa4ae08: mov             x3, x0
    // 0xa4ae0c: r0 = reorder()
    //     0xa4ae0c: bl              #0xa4ac4c  ; [package:nuonline/app/modules/menu/controllers/menu_controller.dart] MenuController::reorder
    // 0xa4ae10: r0 = Null
    //     0xa4ae10: mov             x0, NULL
    // 0xa4ae14: LeaveFrame
    //     0xa4ae14: mov             SP, fp
    //     0xa4ae18: ldp             fp, lr, [SP], #0x10
    // 0xa4ae1c: ret
    //     0xa4ae1c: ret             
    // 0xa4ae20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ae20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ae24: b               #0xa4ad98
    // 0xa4ae28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ae28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void moveToStart(dynamic) {
    // ** addr: 0xa4ae2c, size: 0x88
    // 0xa4ae2c: EnterFrame
    //     0xa4ae2c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ae30: mov             fp, SP
    // 0xa4ae34: ldr             x0, [fp, #0x10]
    // 0xa4ae38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4ae38: ldur            w1, [x0, #0x17]
    // 0xa4ae3c: DecompressPointer r1
    //     0xa4ae3c: add             x1, x1, HEAP, lsl #32
    // 0xa4ae40: CheckStackOverflow
    //     0xa4ae40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4ae44: cmp             SP, x16
    //     0xa4ae48: b.ls            #0xa4aea8
    // 0xa4ae4c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa4ae4c: ldur            w0, [x1, #0x17]
    // 0xa4ae50: DecompressPointer r0
    //     0xa4ae50: add             x0, x0, HEAP, lsl #32
    // 0xa4ae54: LoadField: r2 = r1->field_13
    //     0xa4ae54: ldur            w2, [x1, #0x13]
    // 0xa4ae58: DecompressPointer r2
    //     0xa4ae58: add             x2, x2, HEAP, lsl #32
    // 0xa4ae5c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4ae5c: ldur            w1, [x0, #0x17]
    // 0xa4ae60: DecompressPointer r1
    //     0xa4ae60: add             x1, x1, HEAP, lsl #32
    // 0xa4ae64: cbz             w2, #0xa4ae98
    // 0xa4ae68: LoadField: r0 = r1->field_f
    //     0xa4ae68: ldur            w0, [x1, #0xf]
    // 0xa4ae6c: DecompressPointer r0
    //     0xa4ae6c: add             x0, x0, HEAP, lsl #32
    // 0xa4ae70: LoadField: r1 = r0->field_b
    //     0xa4ae70: ldur            w1, [x0, #0xb]
    // 0xa4ae74: DecompressPointer r1
    //     0xa4ae74: add             x1, x1, HEAP, lsl #32
    // 0xa4ae78: cmp             w1, NULL
    // 0xa4ae7c: b.eq            #0xa4aeb0
    // 0xa4ae80: LoadField: r0 = r1->field_57
    //     0xa4ae80: ldur            w0, [x1, #0x57]
    // 0xa4ae84: DecompressPointer r0
    //     0xa4ae84: add             x0, x0, HEAP, lsl #32
    // 0xa4ae88: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4ae88: ldur            w1, [x0, #0x17]
    // 0xa4ae8c: DecompressPointer r1
    //     0xa4ae8c: add             x1, x1, HEAP, lsl #32
    // 0xa4ae90: r3 = 0
    //     0xa4ae90: movz            x3, #0
    // 0xa4ae94: r0 = reorder()
    //     0xa4ae94: bl              #0xa4ac4c  ; [package:nuonline/app/modules/menu/controllers/menu_controller.dart] MenuController::reorder
    // 0xa4ae98: r0 = Null
    //     0xa4ae98: mov             x0, NULL
    // 0xa4ae9c: LeaveFrame
    //     0xa4ae9c: mov             SP, fp
    //     0xa4aea0: ldp             fp, lr, [SP], #0x10
    // 0xa4aea4: ret
    //     0xa4aea4: ret             
    // 0xa4aea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4aea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4aeac: b               #0xa4ae4c
    // 0xa4aeb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4aeb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void reorder(dynamic, int, int) {
    // ** addr: 0xa4aeb4, size: 0x90
    // 0xa4aeb4: EnterFrame
    //     0xa4aeb4: stp             fp, lr, [SP, #-0x10]!
    //     0xa4aeb8: mov             fp, SP
    // 0xa4aebc: ldr             x0, [fp, #0x20]
    // 0xa4aec0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4aec0: ldur            w1, [x0, #0x17]
    // 0xa4aec4: DecompressPointer r1
    //     0xa4aec4: add             x1, x1, HEAP, lsl #32
    // 0xa4aec8: CheckStackOverflow
    //     0xa4aec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4aecc: cmp             SP, x16
    //     0xa4aed0: b.ls            #0xa4af38
    // 0xa4aed4: ldr             x2, [fp, #0x18]
    // 0xa4aed8: r0 = LoadInt32Instr(r2)
    //     0xa4aed8: sbfx            x0, x2, #1, #0x1f
    //     0xa4aedc: tbz             w2, #0, #0xa4aee4
    //     0xa4aee0: ldur            x0, [x2, #7]
    // 0xa4aee4: ldr             x3, [fp, #0x10]
    // 0xa4aee8: r4 = LoadInt32Instr(r3)
    //     0xa4aee8: sbfx            x4, x3, #1, #0x1f
    //     0xa4aeec: tbz             w3, #0, #0xa4aef4
    //     0xa4aef0: ldur            x4, [x3, #7]
    // 0xa4aef4: cmp             x0, x4
    // 0xa4aef8: b.eq            #0xa4af28
    // 0xa4aefc: LoadField: r0 = r1->field_f
    //     0xa4aefc: ldur            w0, [x1, #0xf]
    // 0xa4af00: DecompressPointer r0
    //     0xa4af00: add             x0, x0, HEAP, lsl #32
    // 0xa4af04: LoadField: r1 = r0->field_b
    //     0xa4af04: ldur            w1, [x0, #0xb]
    // 0xa4af08: DecompressPointer r1
    //     0xa4af08: add             x1, x1, HEAP, lsl #32
    // 0xa4af0c: cmp             w1, NULL
    // 0xa4af10: b.eq            #0xa4af40
    // 0xa4af14: LoadField: r0 = r1->field_57
    //     0xa4af14: ldur            w0, [x1, #0x57]
    // 0xa4af18: DecompressPointer r0
    //     0xa4af18: add             x0, x0, HEAP, lsl #32
    // 0xa4af1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4af1c: ldur            w1, [x0, #0x17]
    // 0xa4af20: DecompressPointer r1
    //     0xa4af20: add             x1, x1, HEAP, lsl #32
    // 0xa4af24: r0 = reorder()
    //     0xa4af24: bl              #0xa4ac4c  ; [package:nuonline/app/modules/menu/controllers/menu_controller.dart] MenuController::reorder
    // 0xa4af28: r0 = Null
    //     0xa4af28: mov             x0, NULL
    // 0xa4af2c: LeaveFrame
    //     0xa4af2c: mov             SP, fp
    //     0xa4af30: ldp             fp, lr, [SP], #0x10
    // 0xa4af34: ret
    //     0xa4af34: ret             
    // 0xa4af38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4af38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4af3c: b               #0xa4aed4
    // 0xa4af40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4af40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa4b02c, size: 0x174
    // 0xa4b02c: EnterFrame
    //     0xa4b02c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4b030: mov             fp, SP
    // 0xa4b034: AllocStack(0x30)
    //     0xa4b034: sub             SP, SP, #0x30
    // 0xa4b038: SetupParameters(ReorderableGridViewState this /* r1 => r0 */)
    //     0xa4b038: mov             x0, x1
    // 0xa4b03c: LoadField: r1 = r0->field_b
    //     0xa4b03c: ldur            w1, [x0, #0xb]
    // 0xa4b040: DecompressPointer r1
    //     0xa4b040: add             x1, x1, HEAP, lsl #32
    // 0xa4b044: cmp             w1, NULL
    // 0xa4b048: b.eq            #0xa4b19c
    // 0xa4b04c: LoadField: r3 = r1->field_b
    //     0xa4b04c: ldur            w3, [x1, #0xb]
    // 0xa4b050: DecompressPointer r3
    //     0xa4b050: add             x3, x3, HEAP, lsl #32
    // 0xa4b054: stur            x3, [fp, #-0x20]
    // 0xa4b058: LoadField: r4 = r1->field_4f
    //     0xa4b058: ldur            x4, [x1, #0x4f]
    // 0xa4b05c: stur            x4, [fp, #-0x18]
    // 0xa4b060: LoadField: r5 = r1->field_57
    //     0xa4b060: ldur            w5, [x1, #0x57]
    // 0xa4b064: DecompressPointer r5
    //     0xa4b064: add             x5, x5, HEAP, lsl #32
    // 0xa4b068: stur            x5, [fp, #-0x10]
    // 0xa4b06c: LoadField: r6 = r1->field_5f
    //     0xa4b06c: ldur            w6, [x1, #0x5f]
    // 0xa4b070: DecompressPointer r6
    //     0xa4b070: add             x6, x6, HEAP, lsl #32
    // 0xa4b074: mov             x2, x0
    // 0xa4b078: stur            x6, [fp, #-8]
    // 0xa4b07c: r1 = Function '_itemBuilder@2202143101':.
    //     0xa4b07c: add             x1, PP, #0x47, lsl #12  ; [pp+0x479a0] AnonymousClosure: (0xa4a6a8), in [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridViewState::_itemBuilder (0xa4a724)
    //     0xa4b080: ldr             x1, [x1, #0x9a0]
    // 0xa4b084: r0 = AllocateClosure()
    //     0xa4b084: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4b088: stur            x0, [fp, #-0x28]
    // 0xa4b08c: r0 = SliverReorderableGrid()
    //     0xa4b08c: bl              #0xa4b1a0  ; AllocateSliverReorderableGridStub -> SliverReorderableGrid (size=0x34)
    // 0xa4b090: mov             x1, x0
    // 0xa4b094: ldur            x0, [fp, #-0x28]
    // 0xa4b098: stur            x1, [fp, #-0x30]
    // 0xa4b09c: StoreField: r1->field_b = r0
    //     0xa4b09c: stur            w0, [x1, #0xb]
    // 0xa4b0a0: ldur            x0, [fp, #-0x18]
    // 0xa4b0a4: StoreField: r1->field_f = r0
    //     0xa4b0a4: stur            x0, [x1, #0xf]
    // 0xa4b0a8: ldur            x0, [fp, #-0x10]
    // 0xa4b0ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4b0ac: stur            w0, [x1, #0x17]
    // 0xa4b0b0: ldur            x0, [fp, #-0x20]
    // 0xa4b0b4: StoreField: r1->field_23 = r0
    //     0xa4b0b4: stur            w0, [x1, #0x23]
    // 0xa4b0b8: r0 = false
    //     0xa4b0b8: add             x0, NULL, #0x30  ; false
    // 0xa4b0bc: StoreField: r1->field_2b = r0
    //     0xa4b0bc: stur            w0, [x1, #0x2b]
    // 0xa4b0c0: ldur            x2, [fp, #-8]
    // 0xa4b0c4: StoreField: r1->field_1f = r2
    //     0xa4b0c4: stur            w2, [x1, #0x1f]
    // 0xa4b0c8: StoreField: r1->field_27 = r0
    //     0xa4b0c8: stur            w0, [x1, #0x27]
    // 0xa4b0cc: r2 = Instance_Axis
    //     0xa4b0cc: ldr             x2, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa4b0d0: StoreField: r1->field_2f = r2
    //     0xa4b0d0: stur            w2, [x1, #0x2f]
    // 0xa4b0d4: r0 = SliverPadding()
    //     0xa4b0d4: bl              #0xa01298  ; AllocateSliverPaddingStub -> SliverPadding (size=0x14)
    // 0xa4b0d8: mov             x3, x0
    // 0xa4b0dc: r0 = Instance_EdgeInsets
    //     0xa4b0dc: add             x0, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xa4b0e0: ldr             x0, [x0, #0x360]
    // 0xa4b0e4: stur            x3, [fp, #-8]
    // 0xa4b0e8: StoreField: r3->field_f = r0
    //     0xa4b0e8: stur            w0, [x3, #0xf]
    // 0xa4b0ec: ldur            x0, [fp, #-0x30]
    // 0xa4b0f0: StoreField: r3->field_b = r0
    //     0xa4b0f0: stur            w0, [x3, #0xb]
    // 0xa4b0f4: r1 = Null
    //     0xa4b0f4: mov             x1, NULL
    // 0xa4b0f8: r2 = 2
    //     0xa4b0f8: movz            x2, #0x2
    // 0xa4b0fc: r0 = AllocateArray()
    //     0xa4b0fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa4b100: mov             x2, x0
    // 0xa4b104: ldur            x0, [fp, #-8]
    // 0xa4b108: stur            x2, [fp, #-0x10]
    // 0xa4b10c: StoreField: r2->field_f = r0
    //     0xa4b10c: stur            w0, [x2, #0xf]
    // 0xa4b110: r1 = <Widget>
    //     0xa4b110: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa4b114: r0 = AllocateGrowableArray()
    //     0xa4b114: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa4b118: mov             x1, x0
    // 0xa4b11c: ldur            x0, [fp, #-0x10]
    // 0xa4b120: stur            x1, [fp, #-8]
    // 0xa4b124: StoreField: r1->field_f = r0
    //     0xa4b124: stur            w0, [x1, #0xf]
    // 0xa4b128: r0 = 2
    //     0xa4b128: movz            x0, #0x2
    // 0xa4b12c: StoreField: r1->field_b = r0
    //     0xa4b12c: stur            w0, [x1, #0xb]
    // 0xa4b130: r0 = CustomScrollView()
    //     0xa4b130: bl              #0xa0128c  ; AllocateCustomScrollViewStub -> CustomScrollView (size=0x54)
    // 0xa4b134: ldur            x1, [fp, #-8]
    // 0xa4b138: StoreField: r0->field_4f = r1
    //     0xa4b138: stur            w1, [x0, #0x4f]
    // 0xa4b13c: r1 = Instance_Axis
    //     0xa4b13c: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa4b140: StoreField: r0->field_b = r1
    //     0xa4b140: stur            w1, [x0, #0xb]
    // 0xa4b144: r1 = false
    //     0xa4b144: add             x1, NULL, #0x30  ; false
    // 0xa4b148: StoreField: r0->field_f = r1
    //     0xa4b148: stur            w1, [x0, #0xf]
    // 0xa4b14c: r1 = true
    //     0xa4b14c: add             x1, NULL, #0x20  ; true
    // 0xa4b150: StoreField: r0->field_23 = r1
    //     0xa4b150: stur            w1, [x0, #0x23]
    // 0xa4b154: StoreField: r0->field_2b = rZR
    //     0xa4b154: stur            xzr, [x0, #0x2b]
    // 0xa4b158: r1 = Instance_DragStartBehavior
    //     0xa4b158: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xa4b15c: StoreField: r0->field_3b = r1
    //     0xa4b15c: stur            w1, [x0, #0x3b]
    // 0xa4b160: r1 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xa4b160: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xa4b164: ldr             x1, [x1, #0xf00]
    // 0xa4b168: StoreField: r0->field_3f = r1
    //     0xa4b168: stur            w1, [x0, #0x3f]
    // 0xa4b16c: r1 = Instance_Clip
    //     0xa4b16c: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa4b170: ldr             x1, [x1, #0x7c0]
    // 0xa4b174: StoreField: r0->field_47 = r1
    //     0xa4b174: stur            w1, [x0, #0x47]
    // 0xa4b178: r1 = Instance_HitTestBehavior
    //     0xa4b178: add             x1, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xa4b17c: ldr             x1, [x1, #0x1c8]
    // 0xa4b180: StoreField: r0->field_4b = r1
    //     0xa4b180: stur            w1, [x0, #0x4b]
    // 0xa4b184: r1 = Instance_NeverScrollableScrollPhysics
    //     0xa4b184: add             x1, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xa4b188: ldr             x1, [x1, #0x290]
    // 0xa4b18c: StoreField: r0->field_1b = r1
    //     0xa4b18c: stur            w1, [x0, #0x1b]
    // 0xa4b190: LeaveFrame
    //     0xa4b190: mov             SP, fp
    //     0xa4b194: ldp             fp, lr, [SP], #0x10
    // 0xa4b198: ret
    //     0xa4b198: ret             
    // 0xa4b19c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b19c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4699, size: 0x68, field offset: 0xc
//   const constructor, 
class ReorderableGridView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94cc4, size: 0x24
    // 0xa94cc4: EnterFrame
    //     0xa94cc4: stp             fp, lr, [SP, #-0x10]!
    //     0xa94cc8: mov             fp, SP
    // 0xa94ccc: mov             x0, x1
    // 0xa94cd0: r1 = <ReorderableGridView>
    //     0xa94cd0: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f070] TypeArguments: <ReorderableGridView>
    //     0xa94cd4: ldr             x1, [x1, #0x70]
    // 0xa94cd8: r0 = ReorderableGridViewState()
    //     0xa94cd8: bl              #0xa94ce8  ; AllocateReorderableGridViewStateStub -> ReorderableGridViewState (size=0x14)
    // 0xa94cdc: LeaveFrame
    //     0xa94cdc: mov             SP, fp
    //     0xa94ce0: ldp             fp, lr, [SP], #0x10
    // 0xa94ce4: ret
    //     0xa94ce4: ret             
  }
  _ ReorderableGridView.count(/* No info */) {
    // ** addr: 0xba1c3c, size: 0x184
    // 0xba1c3c: EnterFrame
    //     0xba1c3c: stp             fp, lr, [SP, #-0x10]!
    //     0xba1c40: mov             fp, SP
    // 0xba1c44: AllocStack(0x38)
    //     0xba1c44: sub             SP, SP, #0x38
    // 0xba1c48: SetupParameters(ReorderableGridView this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */, dynamic _ /* r6 => r0, fp-0x28 */, dynamic _ /* d0 => d0, fp-0x38 */)
    //     0xba1c48: mov             x4, x1
    //     0xba1c4c: stur            x1, [fp, #-8]
    //     0xba1c50: mov             x1, x5
    //     0xba1c54: mov             x0, x6
    //     0xba1c58: stur            x2, [fp, #-0x10]
    //     0xba1c5c: stur            x3, [fp, #-0x18]
    //     0xba1c60: stur            x5, [fp, #-0x20]
    //     0xba1c64: stur            x6, [fp, #-0x28]
    //     0xba1c68: stur            d0, [fp, #-0x38]
    // 0xba1c6c: r1 = 1
    //     0xba1c6c: movz            x1, #0x1
    // 0xba1c70: r0 = AllocateContext()
    //     0xba1c70: bl              #0xec126c  ; AllocateContextStub
    // 0xba1c74: mov             x2, x0
    // 0xba1c78: ldur            x1, [fp, #-0x10]
    // 0xba1c7c: stur            x2, [fp, #-0x30]
    // 0xba1c80: StoreField: r2->field_f = r1
    //     0xba1c80: stur            w1, [x2, #0xf]
    // 0xba1c84: ldur            x3, [fp, #-8]
    // 0xba1c88: r0 = Instance_Axis
    //     0xba1c88: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xba1c8c: StoreField: r3->field_f = r0
    //     0xba1c8c: stur            w0, [x3, #0xf]
    // 0xba1c90: r0 = false
    //     0xba1c90: add             x0, NULL, #0x30  ; false
    // 0xba1c94: StoreField: r3->field_13 = r0
    //     0xba1c94: stur            w0, [x3, #0x13]
    // 0xba1c98: r0 = Instance_NeverScrollableScrollPhysics
    //     0xba1c98: add             x0, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xba1c9c: ldr             x0, [x0, #0x290]
    // 0xba1ca0: StoreField: r3->field_1b = r0
    //     0xba1ca0: stur            w0, [x3, #0x1b]
    // 0xba1ca4: r0 = true
    //     0xba1ca4: add             x0, NULL, #0x20  ; true
    // 0xba1ca8: StoreField: r3->field_1f = r0
    //     0xba1ca8: stur            w0, [x3, #0x1f]
    // 0xba1cac: r0 = Instance_EdgeInsets
    //     0xba1cac: add             x0, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xba1cb0: ldr             x0, [x0, #0x360]
    // 0xba1cb4: StoreField: r3->field_43 = r0
    //     0xba1cb4: stur            w0, [x3, #0x43]
    // 0xba1cb8: ldur            x0, [fp, #-0x20]
    // 0xba1cbc: StoreField: r3->field_57 = r0
    //     0xba1cbc: stur            w0, [x3, #0x57]
    //     0xba1cc0: ldurb           w16, [x3, #-1]
    //     0xba1cc4: ldurb           w17, [x0, #-1]
    //     0xba1cc8: and             x16, x17, x16, lsr #2
    //     0xba1ccc: tst             x16, HEAP, lsr #32
    //     0xba1cd0: b.eq            #0xba1cd8
    //     0xba1cd4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xba1cd8: r0 = Closure: (int) => bool from Function '_defaultItemDragEnable@2202143101': static.
    //     0xba1cd8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33d38] Closure: (int) => bool from Function '_defaultItemDragEnable@2202143101': static. (0x7e54fb8bbd8c)
    //     0xba1cdc: ldr             x0, [x0, #0xd38]
    // 0xba1ce0: StoreField: r3->field_4b = r0
    //     0xba1ce0: stur            w0, [x3, #0x4b]
    // 0xba1ce4: r0 = Instance_DragStartBehavior
    //     0xba1ce4: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xba1ce8: StoreField: r3->field_2f = r0
    //     0xba1ce8: stur            w0, [x3, #0x2f]
    // 0xba1cec: r0 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xba1cec: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xba1cf0: ldr             x0, [x0, #0xf00]
    // 0xba1cf4: StoreField: r3->field_33 = r0
    //     0xba1cf4: stur            w0, [x3, #0x33]
    // 0xba1cf8: r0 = Instance_Clip
    //     0xba1cf8: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xba1cfc: ldr             x0, [x0, #0x7c0]
    // 0xba1d00: StoreField: r3->field_3b = r0
    //     0xba1d00: stur            w0, [x3, #0x3b]
    // 0xba1d04: StoreField: r3->field_23 = rZR
    //     0xba1d04: stur            xzr, [x3, #0x23]
    // 0xba1d08: ldur            x0, [fp, #-0x28]
    // 0xba1d0c: StoreField: r3->field_5f = r0
    //     0xba1d0c: stur            w0, [x3, #0x5f]
    //     0xba1d10: ldurb           w16, [x3, #-1]
    //     0xba1d14: ldurb           w17, [x0, #-1]
    //     0xba1d18: and             x16, x17, x16, lsr #2
    //     0xba1d1c: tst             x16, HEAP, lsr #32
    //     0xba1d20: b.eq            #0xba1d28
    //     0xba1d24: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xba1d28: r0 = SliverGridDelegateWithFixedCrossAxisCount()
    //     0xba1d28: bl              #0xae2d20  ; AllocateSliverGridDelegateWithFixedCrossAxisCountStub -> SliverGridDelegateWithFixedCrossAxisCount (size=0x2c)
    // 0xba1d2c: mov             x1, x0
    // 0xba1d30: ldur            x0, [fp, #-0x18]
    // 0xba1d34: StoreField: r1->field_7 = r0
    //     0xba1d34: stur            x0, [x1, #7]
    // 0xba1d38: d0 = 16.000000
    //     0xba1d38: fmov            d0, #16.00000000
    // 0xba1d3c: StoreField: r1->field_f = d0
    //     0xba1d3c: stur            d0, [x1, #0xf]
    // 0xba1d40: ArrayStore: r1[0] = d0  ; List_8
    //     0xba1d40: stur            d0, [x1, #0x17]
    // 0xba1d44: ldur            d0, [fp, #-0x38]
    // 0xba1d48: StoreField: r1->field_1f = d0
    //     0xba1d48: stur            d0, [x1, #0x1f]
    // 0xba1d4c: mov             x0, x1
    // 0xba1d50: ldur            x3, [fp, #-8]
    // 0xba1d54: StoreField: r3->field_b = r0
    //     0xba1d54: stur            w0, [x3, #0xb]
    //     0xba1d58: ldurb           w16, [x3, #-1]
    //     0xba1d5c: ldurb           w17, [x0, #-1]
    //     0xba1d60: and             x16, x17, x16, lsr #2
    //     0xba1d64: tst             x16, HEAP, lsr #32
    //     0xba1d68: b.eq            #0xba1d70
    //     0xba1d6c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xba1d70: ldur            x2, [fp, #-0x30]
    // 0xba1d74: r1 = Function '<anonymous closure>':.
    //     0xba1d74: add             x1, PP, #0x33, lsl #12  ; [pp+0x33d40] AnonymousClosure: (0xba1dc0), in [package:reorderable_grid/src/reorderable_grid_view.dart] ReorderableGridView::ReorderableGridView.count (0xba1c3c)
    //     0xba1d78: ldr             x1, [x1, #0xd40]
    // 0xba1d7c: r0 = AllocateClosure()
    //     0xba1d7c: bl              #0xec1630  ; AllocateClosureStub
    // 0xba1d80: ldur            x1, [fp, #-8]
    // 0xba1d84: StoreField: r1->field_47 = r0
    //     0xba1d84: stur            w0, [x1, #0x47]
    //     0xba1d88: ldurb           w16, [x1, #-1]
    //     0xba1d8c: ldurb           w17, [x0, #-1]
    //     0xba1d90: and             x16, x17, x16, lsr #2
    //     0xba1d94: tst             x16, HEAP, lsr #32
    //     0xba1d98: b.eq            #0xba1da0
    //     0xba1d9c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xba1da0: ldur            x2, [fp, #-0x10]
    // 0xba1da4: LoadField: r3 = r2->field_b
    //     0xba1da4: ldur            w3, [x2, #0xb]
    // 0xba1da8: r2 = LoadInt32Instr(r3)
    //     0xba1da8: sbfx            x2, x3, #1, #0x1f
    // 0xba1dac: StoreField: r1->field_4f = r2
    //     0xba1dac: stur            x2, [x1, #0x4f]
    // 0xba1db0: r0 = Null
    //     0xba1db0: mov             x0, NULL
    // 0xba1db4: LeaveFrame
    //     0xba1db4: mov             SP, fp
    //     0xba1db8: ldp             fp, lr, [SP], #0x10
    // 0xba1dbc: ret
    //     0xba1dbc: ret             
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xba1dc0, size: 0x5c
    // 0xba1dc0: ldr             x2, [SP, #0x10]
    // 0xba1dc4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xba1dc4: ldur            w3, [x2, #0x17]
    // 0xba1dc8: DecompressPointer r3
    //     0xba1dc8: add             x3, x3, HEAP, lsl #32
    // 0xba1dcc: LoadField: r2 = r3->field_f
    //     0xba1dcc: ldur            w2, [x3, #0xf]
    // 0xba1dd0: DecompressPointer r2
    //     0xba1dd0: add             x2, x2, HEAP, lsl #32
    // 0xba1dd4: LoadField: r3 = r2->field_b
    //     0xba1dd4: ldur            w3, [x2, #0xb]
    // 0xba1dd8: ldr             x4, [SP]
    // 0xba1ddc: r5 = LoadInt32Instr(r4)
    //     0xba1ddc: sbfx            x5, x4, #1, #0x1f
    //     0xba1de0: tbz             w4, #0, #0xba1de8
    //     0xba1de4: ldur            x5, [x4, #7]
    // 0xba1de8: r0 = LoadInt32Instr(r3)
    //     0xba1de8: sbfx            x0, x3, #1, #0x1f
    // 0xba1dec: mov             x1, x5
    // 0xba1df0: cmp             x1, x0
    // 0xba1df4: b.hs            #0xba1e10
    // 0xba1df8: LoadField: r1 = r2->field_f
    //     0xba1df8: ldur            w1, [x2, #0xf]
    // 0xba1dfc: DecompressPointer r1
    //     0xba1dfc: add             x1, x1, HEAP, lsl #32
    // 0xba1e00: ArrayLoad: r0 = r1[r5]  ; Unknown_4
    //     0xba1e00: add             x16, x1, x5, lsl #2
    //     0xba1e04: ldur            w0, [x16, #0xf]
    // 0xba1e08: DecompressPointer r0
    //     0xba1e08: add             x0, x0, HEAP, lsl #32
    // 0xba1e0c: ret
    //     0xba1e0c: ret             
    // 0xba1e10: EnterFrame
    //     0xba1e10: stp             fp, lr, [SP, #-0x10]!
    //     0xba1e14: mov             fp, SP
    // 0xba1e18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba1e18: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
