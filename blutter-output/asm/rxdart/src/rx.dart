// lib: , url: package:rxdart/src/rx.dart

// class id: 1051083, size: 0x8
class :: {
}

// class id: 525, size: 0x8, field offset: 0x8
abstract class Rx extends Object {

  static _ defer(/* No info */) {
    // ** addr: 0x8b35fc, size: 0x48
    // 0x8b35fc: EnterFrame
    //     0x8b35fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b3600: mov             fp, SP
    // 0x8b3604: LoadField: r0 = r4->field_f
    //     0x8b3604: ldur            w0, [x4, #0xf]
    // 0x8b3608: cbnz            w0, #0x8b3614
    // 0x8b360c: r1 = Null
    //     0x8b360c: mov             x1, NULL
    // 0x8b3610: b               #0x8b3620
    // 0x8b3614: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x8b3614: ldur            w0, [x4, #0x17]
    // 0x8b3618: add             x1, fp, w0, sxtw #2
    // 0x8b361c: ldr             x1, [x1, #0x10]
    // 0x8b3620: ldr             x0, [fp, #0x10]
    // 0x8b3624: r0 = DeferStream()
    //     0x8b3624: bl              #0x8b3644  ; AllocateDeferStreamStub -> DeferStream<X0> (size=0x14)
    // 0x8b3628: r1 = true
    //     0x8b3628: add             x1, NULL, #0x20  ; true
    // 0x8b362c: StoreField: r0->field_f = r1
    //     0x8b362c: stur            w1, [x0, #0xf]
    // 0x8b3630: ldr             x1, [fp, #0x10]
    // 0x8b3634: StoreField: r0->field_b = r1
    //     0x8b3634: stur            w1, [x0, #0xb]
    // 0x8b3638: LeaveFrame
    //     0x8b3638: mov             SP, fp
    //     0x8b363c: ldp             fp, lr, [SP], #0x10
    // 0x8b3640: ret
    //     0x8b3640: ret             
  }
}
