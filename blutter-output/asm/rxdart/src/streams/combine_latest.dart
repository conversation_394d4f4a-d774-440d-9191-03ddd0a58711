// lib: , url: package:rxdart/src/streams/combine_latest.dart

// class id: 1051084, size: 0x8
class :: {
}

// class id: 6672, size: 0x10, field offset: 0x10
class CombineLatestStream<C1X0, C1X1> extends StreamView<C1X0> {

  static CombineLatestStream<dynamic, Y2> combine2<Y0, Y1, Y2>(Stream<Y0>, Stream<Y1>, (dynamic, Y0, Y1) => Y2) {
    // ** addr: 0x8b6db0, size: 0x130
    // 0x8b6db0: EnterFrame
    //     0x8b6db0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b6db4: mov             fp, SP
    // 0x8b6db8: AllocStack(0x28)
    //     0x8b6db8: sub             SP, SP, #0x28
    // 0x8b6dbc: SetupParameters()
    //     0x8b6dbc: ldur            w0, [x4, #0xf]
    //     0x8b6dc0: cbnz            w0, #0x8b6dcc
    //     0x8b6dc4: mov             x3, NULL
    //     0x8b6dc8: b               #0x8b6ddc
    //     0x8b6dcc: ldur            w0, [x4, #0x17]
    //     0x8b6dd0: add             x1, fp, w0, sxtw #2
    //     0x8b6dd4: ldr             x1, [x1, #0x10]
    //     0x8b6dd8: mov             x3, x1
    //     0x8b6ddc: ldr             x2, [fp, #0x20]
    //     0x8b6de0: ldr             x1, [fp, #0x18]
    //     0x8b6de4: ldr             x0, [fp, #0x10]
    //     0x8b6de8: stur            x3, [fp, #-8]
    // 0x8b6dec: CheckStackOverflow
    //     0x8b6dec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6df0: cmp             SP, x16
    //     0x8b6df4: b.ls            #0x8b6ed8
    // 0x8b6df8: r1 = 1
    //     0x8b6df8: movz            x1, #0x1
    // 0x8b6dfc: r0 = AllocateContext()
    //     0x8b6dfc: bl              #0xec126c  ; AllocateContextStub
    // 0x8b6e00: mov             x4, x0
    // 0x8b6e04: ldr             x0, [fp, #0x10]
    // 0x8b6e08: stur            x4, [fp, #-0x10]
    // 0x8b6e0c: StoreField: r4->field_f = r0
    //     0x8b6e0c: stur            w0, [x4, #0xf]
    // 0x8b6e10: ldur            x1, [fp, #-8]
    // 0x8b6e14: r2 = Null
    //     0x8b6e14: mov             x2, NULL
    // 0x8b6e18: r3 = <Y2, dynamic, Y2>
    //     0x8b6e18: add             x3, PP, #0xe, lsl #12  ; [pp+0xea08] TypeArguments: <Y2, dynamic, Y2>
    //     0x8b6e1c: ldr             x3, [x3, #0xa08]
    // 0x8b6e20: r0 = Null
    //     0x8b6e20: mov             x0, NULL
    // 0x8b6e24: cmp             x2, x0
    // 0x8b6e28: b.ne            #0x8b6e34
    // 0x8b6e2c: cmp             x1, x0
    // 0x8b6e30: b.eq            #0x8b6e40
    // 0x8b6e34: r30 = InstantiateTypeArgumentsStub
    //     0x8b6e34: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8b6e38: LoadField: r30 = r30->field_7
    //     0x8b6e38: ldur            lr, [lr, #7]
    // 0x8b6e3c: blr             lr
    // 0x8b6e40: r1 = Null
    //     0x8b6e40: mov             x1, NULL
    // 0x8b6e44: r2 = 4
    //     0x8b6e44: movz            x2, #0x4
    // 0x8b6e48: stur            x0, [fp, #-0x18]
    // 0x8b6e4c: r0 = AllocateArray()
    //     0x8b6e4c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8b6e50: mov             x2, x0
    // 0x8b6e54: ldr             x0, [fp, #0x20]
    // 0x8b6e58: stur            x2, [fp, #-0x20]
    // 0x8b6e5c: StoreField: r2->field_f = r0
    //     0x8b6e5c: stur            w0, [x2, #0xf]
    // 0x8b6e60: ldr             x0, [fp, #0x18]
    // 0x8b6e64: StoreField: r2->field_13 = r0
    //     0x8b6e64: stur            w0, [x2, #0x13]
    // 0x8b6e68: r1 = <Stream>
    //     0x8b6e68: add             x1, PP, #0xe, lsl #12  ; [pp+0xea10] TypeArguments: <Stream>
    //     0x8b6e6c: ldr             x1, [x1, #0xa10]
    // 0x8b6e70: r0 = AllocateGrowableArray()
    //     0x8b6e70: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8b6e74: mov             x3, x0
    // 0x8b6e78: ldur            x0, [fp, #-0x20]
    // 0x8b6e7c: stur            x3, [fp, #-0x28]
    // 0x8b6e80: StoreField: r3->field_f = r0
    //     0x8b6e80: stur            w0, [x3, #0xf]
    // 0x8b6e84: r0 = 4
    //     0x8b6e84: movz            x0, #0x4
    // 0x8b6e88: StoreField: r3->field_b = r0
    //     0x8b6e88: stur            w0, [x3, #0xb]
    // 0x8b6e8c: ldur            x2, [fp, #-0x10]
    // 0x8b6e90: r1 = Function '<anonymous closure>': static.
    //     0x8b6e90: add             x1, PP, #0xe, lsl #12  ; [pp+0xea18] AnonymousClosure: static (0x8b80b4), in [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::combine2 (0x8b6db0)
    //     0x8b6e94: ldr             x1, [x1, #0xa18]
    // 0x8b6e98: r0 = AllocateClosure()
    //     0x8b6e98: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b6e9c: mov             x2, x0
    // 0x8b6ea0: ldur            x0, [fp, #-8]
    // 0x8b6ea4: stur            x2, [fp, #-0x10]
    // 0x8b6ea8: StoreField: r2->field_b = r0
    //     0x8b6ea8: stur            w0, [x2, #0xb]
    // 0x8b6eac: ldur            x1, [fp, #-0x18]
    // 0x8b6eb0: r0 = CombineLatestStream()
    //     0x8b6eb0: bl              #0x8b80a8  ; AllocateCombineLatestStreamStub -> CombineLatestStream<C1X0, C1X1> (size=0x10)
    // 0x8b6eb4: mov             x1, x0
    // 0x8b6eb8: ldur            x2, [fp, #-0x28]
    // 0x8b6ebc: ldur            x3, [fp, #-0x10]
    // 0x8b6ec0: stur            x0, [fp, #-8]
    // 0x8b6ec4: r0 = CombineLatestStream()
    //     0x8b6ec4: bl              #0x8b6ee0  ; [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::CombineLatestStream
    // 0x8b6ec8: ldur            x0, [fp, #-8]
    // 0x8b6ecc: LeaveFrame
    //     0x8b6ecc: mov             SP, fp
    //     0x8b6ed0: ldp             fp, lr, [SP], #0x10
    // 0x8b6ed4: ret
    //     0x8b6ed4: ret             
    // 0x8b6ed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b6ed8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b6edc: b               #0x8b6df8
  }
  _ CombineLatestStream(/* No info */) {
    // ** addr: 0x8b6ee0, size: 0xc0
    // 0x8b6ee0: EnterFrame
    //     0x8b6ee0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b6ee4: mov             fp, SP
    // 0x8b6ee8: AllocStack(0x30)
    //     0x8b6ee8: sub             SP, SP, #0x30
    // 0x8b6eec: SetupParameters(CombineLatestStream<C1X0, C1X1> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x8b6eec: mov             x5, x1
    //     0x8b6ef0: mov             x4, x2
    //     0x8b6ef4: mov             x0, x3
    //     0x8b6ef8: stur            x1, [fp, #-8]
    //     0x8b6efc: stur            x2, [fp, #-0x10]
    //     0x8b6f00: stur            x3, [fp, #-0x18]
    // 0x8b6f04: CheckStackOverflow
    //     0x8b6f04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6f08: cmp             SP, x16
    //     0x8b6f0c: b.ls            #0x8b6f98
    // 0x8b6f10: LoadField: r2 = r5->field_7
    //     0x8b6f10: ldur            w2, [x5, #7]
    // 0x8b6f14: DecompressPointer r2
    //     0x8b6f14: add             x2, x2, HEAP, lsl #32
    // 0x8b6f18: r1 = Null
    //     0x8b6f18: mov             x1, NULL
    // 0x8b6f1c: r3 = <C1X0, C1X1>
    //     0x8b6f1c: ldr             x3, [PP, #0xa38]  ; [pp+0xa38] TypeArguments: <C1X0, C1X1>
    // 0x8b6f20: r0 = Null
    //     0x8b6f20: mov             x0, NULL
    // 0x8b6f24: cmp             x2, x0
    // 0x8b6f28: b.eq            #0x8b6f38
    // 0x8b6f2c: r30 = InstantiateTypeArgumentsStub
    //     0x8b6f2c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8b6f30: LoadField: r30 = r30->field_7
    //     0x8b6f30: ldur            lr, [lr, #7]
    // 0x8b6f34: blr             lr
    // 0x8b6f38: ldur            x16, [fp, #-0x10]
    // 0x8b6f3c: stp             x16, x0, [SP, #8]
    // 0x8b6f40: ldur            x16, [fp, #-0x18]
    // 0x8b6f44: str             x16, [SP]
    // 0x8b6f48: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x8b6f48: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x8b6f4c: r0 = _buildController()
    //     0x8b6f4c: bl              #0x8b6fa0  ; [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::_buildController
    // 0x8b6f50: stur            x0, [fp, #-0x10]
    // 0x8b6f54: LoadField: r1 = r0->field_7
    //     0x8b6f54: ldur            w1, [x0, #7]
    // 0x8b6f58: DecompressPointer r1
    //     0x8b6f58: add             x1, x1, HEAP, lsl #32
    // 0x8b6f5c: r0 = _ControllerStream()
    //     0x8b6f5c: bl              #0x696858  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0x8b6f60: ldur            x1, [fp, #-0x10]
    // 0x8b6f64: StoreField: r0->field_b = r1
    //     0x8b6f64: stur            w1, [x0, #0xb]
    // 0x8b6f68: ldur            x1, [fp, #-8]
    // 0x8b6f6c: StoreField: r1->field_b = r0
    //     0x8b6f6c: stur            w0, [x1, #0xb]
    //     0x8b6f70: ldurb           w16, [x1, #-1]
    //     0x8b6f74: ldurb           w17, [x0, #-1]
    //     0x8b6f78: and             x16, x17, x16, lsr #2
    //     0x8b6f7c: tst             x16, HEAP, lsr #32
    //     0x8b6f80: b.eq            #0x8b6f88
    //     0x8b6f84: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b6f88: r0 = Null
    //     0x8b6f88: mov             x0, NULL
    // 0x8b6f8c: LeaveFrame
    //     0x8b6f8c: mov             SP, fp
    //     0x8b6f90: ldp             fp, lr, [SP], #0x10
    // 0x8b6f94: ret
    //     0x8b6f94: ret             
    // 0x8b6f98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b6f98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b6f9c: b               #0x8b6f10
  }
  static StreamController<Y1> _buildController<Y0, Y1>(Iterable<Stream<Y0>>, (dynamic, List<Y0>) => Y1) {
    // ** addr: 0x8b6fa0, size: 0x1d8
    // 0x8b6fa0: EnterFrame
    //     0x8b6fa0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b6fa4: mov             fp, SP
    // 0x8b6fa8: AllocStack(0x20)
    //     0x8b6fa8: sub             SP, SP, #0x20
    // 0x8b6fac: SetupParameters()
    //     0x8b6fac: ldur            w0, [x4, #0xf]
    //     0x8b6fb0: cbnz            w0, #0x8b6fbc
    //     0x8b6fb4: mov             x2, NULL
    //     0x8b6fb8: b               #0x8b6fcc
    //     0x8b6fbc: ldur            w0, [x4, #0x17]
    //     0x8b6fc0: add             x1, fp, w0, sxtw #2
    //     0x8b6fc4: ldr             x1, [x1, #0x10]
    //     0x8b6fc8: mov             x2, x1
    //     0x8b6fcc: ldr             x1, [fp, #0x18]
    //     0x8b6fd0: ldr             x0, [fp, #0x10]
    //     0x8b6fd4: stur            x2, [fp, #-8]
    // 0x8b6fd8: CheckStackOverflow
    //     0x8b6fd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6fdc: cmp             SP, x16
    //     0x8b6fe0: b.ls            #0x8b7170
    // 0x8b6fe4: r1 = 5
    //     0x8b6fe4: movz            x1, #0x5
    // 0x8b6fe8: r0 = AllocateContext()
    //     0x8b6fe8: bl              #0xec126c  ; AllocateContextStub
    // 0x8b6fec: mov             x4, x0
    // 0x8b6ff0: ldr             x0, [fp, #0x18]
    // 0x8b6ff4: stur            x4, [fp, #-0x10]
    // 0x8b6ff8: StoreField: r4->field_f = r0
    //     0x8b6ff8: stur            w0, [x4, #0xf]
    // 0x8b6ffc: ldr             x0, [fp, #0x10]
    // 0x8b7000: StoreField: r4->field_13 = r0
    //     0x8b7000: stur            w0, [x4, #0x13]
    // 0x8b7004: ldur            x1, [fp, #-8]
    // 0x8b7008: r2 = Null
    //     0x8b7008: mov             x2, NULL
    // 0x8b700c: r3 = <Y1>
    //     0x8b700c: add             x3, PP, #0xe, lsl #12  ; [pp+0xea50] TypeArguments: <Y1>
    //     0x8b7010: ldr             x3, [x3, #0xa50]
    // 0x8b7014: r0 = Null
    //     0x8b7014: mov             x0, NULL
    // 0x8b7018: cmp             x2, x0
    // 0x8b701c: b.ne            #0x8b7028
    // 0x8b7020: cmp             x1, x0
    // 0x8b7024: b.eq            #0x8b7034
    // 0x8b7028: r30 = InstantiateTypeArgumentsStub
    //     0x8b7028: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8b702c: LoadField: r30 = r30->field_7
    //     0x8b702c: ldur            lr, [lr, #7]
    // 0x8b7030: blr             lr
    // 0x8b7034: r16 = true
    //     0x8b7034: add             x16, NULL, #0x20  ; true
    // 0x8b7038: str             x16, [SP]
    // 0x8b703c: mov             x1, x0
    // 0x8b7040: r4 = const [0, 0x2, 0x1, 0x1, sync, 0x1, null]
    //     0x8b7040: add             x4, PP, #0xc, lsl #12  ; [pp+0xcfb0] List(7) [0, 0x2, 0x1, 0x1, "sync", 0x1, Null]
    //     0x8b7044: ldr             x4, [x4, #0xfb0]
    // 0x8b7048: r0 = StreamController()
    //     0x8b7048: bl              #0x696864  ; [dart:async] StreamController::StreamController
    // 0x8b704c: mov             x4, x0
    // 0x8b7050: ldur            x3, [fp, #-0x10]
    // 0x8b7054: stur            x4, [fp, #-0x18]
    // 0x8b7058: ArrayStore: r3[0] = r0  ; List_4
    //     0x8b7058: stur            w0, [x3, #0x17]
    //     0x8b705c: ldurb           w16, [x3, #-1]
    //     0x8b7060: ldurb           w17, [x0, #-1]
    //     0x8b7064: and             x16, x17, x16, lsr #2
    //     0x8b7068: tst             x16, HEAP, lsr #32
    //     0x8b706c: b.eq            #0x8b7074
    //     0x8b7070: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8b7074: r0 = Sentinel
    //     0x8b7074: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b7078: StoreField: r3->field_1b = r0
    //     0x8b7078: stur            w0, [x3, #0x1b]
    // 0x8b707c: StoreField: r3->field_1f = rNULL
    //     0x8b707c: stur            NULL, [x3, #0x1f]
    // 0x8b7080: mov             x2, x3
    // 0x8b7084: r1 = Function '<anonymous closure>': static.
    //     0x8b7084: add             x1, PP, #0xe, lsl #12  ; [pp+0xea58] AnonymousClosure: static (0x8b7704), in [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::_buildController (0x8b6fa0)
    //     0x8b7088: ldr             x1, [x1, #0xa58]
    // 0x8b708c: r0 = AllocateClosure()
    //     0x8b708c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b7090: ldur            x3, [fp, #-8]
    // 0x8b7094: StoreField: r0->field_b = r3
    //     0x8b7094: stur            w3, [x0, #0xb]
    // 0x8b7098: ldur            x4, [fp, #-0x18]
    // 0x8b709c: StoreField: r4->field_1b = r0
    //     0x8b709c: stur            w0, [x4, #0x1b]
    //     0x8b70a0: ldurb           w16, [x4, #-1]
    //     0x8b70a4: ldurb           w17, [x0, #-1]
    //     0x8b70a8: and             x16, x17, x16, lsr #2
    //     0x8b70ac: tst             x16, HEAP, lsr #32
    //     0x8b70b0: b.eq            #0x8b70b8
    //     0x8b70b4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8b70b8: ldur            x2, [fp, #-0x10]
    // 0x8b70bc: r1 = Function '<anonymous closure>': static.
    //     0x8b70bc: add             x1, PP, #0xe, lsl #12  ; [pp+0xea60] AnonymousClosure: static (0x8b759c), in [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::_buildController (0x8b6fa0)
    //     0x8b70c0: ldr             x1, [x1, #0xa60]
    // 0x8b70c4: r0 = AllocateClosure()
    //     0x8b70c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b70c8: ldur            x3, [fp, #-8]
    // 0x8b70cc: StoreField: r0->field_b = r3
    //     0x8b70cc: stur            w3, [x0, #0xb]
    // 0x8b70d0: ldur            x4, [fp, #-0x18]
    // 0x8b70d4: StoreField: r4->field_1f = r0
    //     0x8b70d4: stur            w0, [x4, #0x1f]
    //     0x8b70d8: ldurb           w16, [x4, #-1]
    //     0x8b70dc: ldurb           w17, [x0, #-1]
    //     0x8b70e0: and             x16, x17, x16, lsr #2
    //     0x8b70e4: tst             x16, HEAP, lsr #32
    //     0x8b70e8: b.eq            #0x8b70f0
    //     0x8b70ec: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8b70f0: ldur            x2, [fp, #-0x10]
    // 0x8b70f4: r1 = Function '<anonymous closure>': static.
    //     0x8b70f4: add             x1, PP, #0xe, lsl #12  ; [pp+0xea68] AnonymousClosure: static (0x8b743c), in [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::_buildController (0x8b6fa0)
    //     0x8b70f8: ldr             x1, [x1, #0xa68]
    // 0x8b70fc: r0 = AllocateClosure()
    //     0x8b70fc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b7100: ldur            x3, [fp, #-8]
    // 0x8b7104: StoreField: r0->field_b = r3
    //     0x8b7104: stur            w3, [x0, #0xb]
    // 0x8b7108: ldur            x4, [fp, #-0x18]
    // 0x8b710c: StoreField: r4->field_23 = r0
    //     0x8b710c: stur            w0, [x4, #0x23]
    //     0x8b7110: ldurb           w16, [x4, #-1]
    //     0x8b7114: ldurb           w17, [x0, #-1]
    //     0x8b7118: and             x16, x17, x16, lsr #2
    //     0x8b711c: tst             x16, HEAP, lsr #32
    //     0x8b7120: b.eq            #0x8b7128
    //     0x8b7124: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8b7128: ldur            x2, [fp, #-0x10]
    // 0x8b712c: r1 = Function '<anonymous closure>': static.
    //     0x8b712c: add             x1, PP, #0xe, lsl #12  ; [pp+0xea70] AnonymousClosure: static (0x8b7178), in [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::_buildController (0x8b6fa0)
    //     0x8b7130: ldr             x1, [x1, #0xa70]
    // 0x8b7134: r0 = AllocateClosure()
    //     0x8b7134: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b7138: ldur            x1, [fp, #-8]
    // 0x8b713c: StoreField: r0->field_b = r1
    //     0x8b713c: stur            w1, [x0, #0xb]
    // 0x8b7140: ldur            x1, [fp, #-0x18]
    // 0x8b7144: StoreField: r1->field_27 = r0
    //     0x8b7144: stur            w0, [x1, #0x27]
    //     0x8b7148: ldurb           w16, [x1, #-1]
    //     0x8b714c: ldurb           w17, [x0, #-1]
    //     0x8b7150: and             x16, x17, x16, lsr #2
    //     0x8b7154: tst             x16, HEAP, lsr #32
    //     0x8b7158: b.eq            #0x8b7160
    //     0x8b715c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b7160: mov             x0, x1
    // 0x8b7164: LeaveFrame
    //     0x8b7164: mov             SP, fp
    //     0x8b7168: ldp             fp, lr, [SP], #0x10
    // 0x8b716c: ret
    //     0x8b716c: ret             
    // 0x8b7170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7170: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7174: b               #0x8b6fe4
  }
  [closure] static Future<void>? <anonymous closure>(dynamic) {
    // ** addr: 0x8b7178, size: 0x74
    // 0x8b7178: EnterFrame
    //     0x8b7178: stp             fp, lr, [SP, #-0x10]!
    //     0x8b717c: mov             fp, SP
    // 0x8b7180: AllocStack(0x10)
    //     0x8b7180: sub             SP, SP, #0x10
    // 0x8b7184: SetupParameters()
    //     0x8b7184: ldr             x0, [fp, #0x10]
    //     0x8b7188: ldur            w1, [x0, #0x17]
    //     0x8b718c: add             x1, x1, HEAP, lsl #32
    //     0x8b7190: stur            x1, [fp, #-8]
    // 0x8b7194: CheckStackOverflow
    //     0x8b7194: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7198: cmp             SP, x16
    //     0x8b719c: b.ls            #0x8b71e4
    // 0x8b71a0: StoreField: r1->field_1f = rNULL
    //     0x8b71a0: stur            NULL, [x1, #0x1f]
    // 0x8b71a4: LoadField: r0 = r1->field_1b
    //     0x8b71a4: ldur            w0, [x1, #0x1b]
    // 0x8b71a8: DecompressPointer r0
    //     0x8b71a8: add             x0, x0, HEAP, lsl #32
    // 0x8b71ac: r16 = Sentinel
    //     0x8b71ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b71b0: cmp             w0, w16
    // 0x8b71b4: b.ne            #0x8b71c8
    // 0x8b71b8: r16 = "subscriptions"
    //     0x8b71b8: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0x8b71bc: ldr             x16, [x16, #0xa78]
    // 0x8b71c0: str             x16, [SP]
    // 0x8b71c4: r0 = _throwLocalNotInitialized()
    //     0x8b71c4: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8b71c8: ldur            x0, [fp, #-8]
    // 0x8b71cc: LoadField: r1 = r0->field_1b
    //     0x8b71cc: ldur            w1, [x0, #0x1b]
    // 0x8b71d0: DecompressPointer r1
    //     0x8b71d0: add             x1, x1, HEAP, lsl #32
    // 0x8b71d4: r0 = StreamSubscriptionsIterableExtension.cancelAll()
    //     0x8b71d4: bl              #0x8b71ec  ; [package:rxdart/src/utils/subscription.dart] ::StreamSubscriptionsIterableExtension.cancelAll
    // 0x8b71d8: LeaveFrame
    //     0x8b71d8: mov             SP, fp
    //     0x8b71dc: ldp             fp, lr, [SP], #0x10
    // 0x8b71e0: ret
    //     0x8b71e0: ret             
    // 0x8b71e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b71e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b71e8: b               #0x8b71a0
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x8b743c, size: 0x74
    // 0x8b743c: EnterFrame
    //     0x8b743c: stp             fp, lr, [SP, #-0x10]!
    //     0x8b7440: mov             fp, SP
    // 0x8b7444: AllocStack(0x10)
    //     0x8b7444: sub             SP, SP, #0x10
    // 0x8b7448: SetupParameters()
    //     0x8b7448: ldr             x0, [fp, #0x10]
    //     0x8b744c: ldur            w1, [x0, #0x17]
    //     0x8b7450: add             x1, x1, HEAP, lsl #32
    //     0x8b7454: stur            x1, [fp, #-8]
    // 0x8b7458: CheckStackOverflow
    //     0x8b7458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b745c: cmp             SP, x16
    //     0x8b7460: b.ls            #0x8b74a8
    // 0x8b7464: LoadField: r0 = r1->field_1b
    //     0x8b7464: ldur            w0, [x1, #0x1b]
    // 0x8b7468: DecompressPointer r0
    //     0x8b7468: add             x0, x0, HEAP, lsl #32
    // 0x8b746c: r16 = Sentinel
    //     0x8b746c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b7470: cmp             w0, w16
    // 0x8b7474: b.ne            #0x8b7488
    // 0x8b7478: r16 = "subscriptions"
    //     0x8b7478: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0x8b747c: ldr             x16, [x16, #0xa78]
    // 0x8b7480: str             x16, [SP]
    // 0x8b7484: r0 = _throwLocalNotInitialized()
    //     0x8b7484: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8b7488: ldur            x0, [fp, #-8]
    // 0x8b748c: LoadField: r1 = r0->field_1b
    //     0x8b748c: ldur            w1, [x0, #0x1b]
    // 0x8b7490: DecompressPointer r1
    //     0x8b7490: add             x1, x1, HEAP, lsl #32
    // 0x8b7494: r0 = StreamSubscriptionsIterableExtensions.resumeAll()
    //     0x8b7494: bl              #0x8b74b0  ; [package:rxdart/src/utils/subscription.dart] ::StreamSubscriptionsIterableExtensions.resumeAll
    // 0x8b7498: r0 = Null
    //     0x8b7498: mov             x0, NULL
    // 0x8b749c: LeaveFrame
    //     0x8b749c: mov             SP, fp
    //     0x8b74a0: ldp             fp, lr, [SP], #0x10
    // 0x8b74a4: ret
    //     0x8b74a4: ret             
    // 0x8b74a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b74a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b74ac: b               #0x8b7464
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x8b759c, size: 0x74
    // 0x8b759c: EnterFrame
    //     0x8b759c: stp             fp, lr, [SP, #-0x10]!
    //     0x8b75a0: mov             fp, SP
    // 0x8b75a4: AllocStack(0x10)
    //     0x8b75a4: sub             SP, SP, #0x10
    // 0x8b75a8: SetupParameters()
    //     0x8b75a8: ldr             x0, [fp, #0x10]
    //     0x8b75ac: ldur            w1, [x0, #0x17]
    //     0x8b75b0: add             x1, x1, HEAP, lsl #32
    //     0x8b75b4: stur            x1, [fp, #-8]
    // 0x8b75b8: CheckStackOverflow
    //     0x8b75b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b75bc: cmp             SP, x16
    //     0x8b75c0: b.ls            #0x8b7608
    // 0x8b75c4: LoadField: r0 = r1->field_1b
    //     0x8b75c4: ldur            w0, [x1, #0x1b]
    // 0x8b75c8: DecompressPointer r0
    //     0x8b75c8: add             x0, x0, HEAP, lsl #32
    // 0x8b75cc: r16 = Sentinel
    //     0x8b75cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b75d0: cmp             w0, w16
    // 0x8b75d4: b.ne            #0x8b75e8
    // 0x8b75d8: r16 = "subscriptions"
    //     0x8b75d8: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0x8b75dc: ldr             x16, [x16, #0xa78]
    // 0x8b75e0: str             x16, [SP]
    // 0x8b75e4: r0 = _throwLocalNotInitialized()
    //     0x8b75e4: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8b75e8: ldur            x0, [fp, #-8]
    // 0x8b75ec: LoadField: r1 = r0->field_1b
    //     0x8b75ec: ldur            w1, [x0, #0x1b]
    // 0x8b75f0: DecompressPointer r1
    //     0x8b75f0: add             x1, x1, HEAP, lsl #32
    // 0x8b75f4: r0 = StreamSubscriptionsIterableExtensions.pauseAll()
    //     0x8b75f4: bl              #0x8b7610  ; [package:rxdart/src/utils/subscription.dart] ::StreamSubscriptionsIterableExtensions.pauseAll
    // 0x8b75f8: r0 = Null
    //     0x8b75f8: mov             x0, NULL
    // 0x8b75fc: LeaveFrame
    //     0x8b75fc: mov             SP, fp
    //     0x8b7600: ldp             fp, lr, [SP], #0x10
    // 0x8b7604: ret
    //     0x8b7604: ret             
    // 0x8b7608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b760c: b               #0x8b75c4
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x8b7704, size: 0x1e4
    // 0x8b7704: EnterFrame
    //     0x8b7704: stp             fp, lr, [SP, #-0x10]!
    //     0x8b7708: mov             fp, SP
    // 0x8b770c: AllocStack(0x40)
    //     0x8b770c: sub             SP, SP, #0x40
    // 0x8b7710: SetupParameters()
    //     0x8b7710: ldr             x0, [fp, #0x10]
    //     0x8b7714: ldur            w1, [x0, #0x17]
    //     0x8b7718: add             x1, x1, HEAP, lsl #32
    //     0x8b771c: stur            x1, [fp, #-0x10]
    // 0x8b7720: CheckStackOverflow
    //     0x8b7720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7724: cmp             SP, x16
    //     0x8b7728: b.ls            #0x8b78e0
    // 0x8b772c: LoadField: r2 = r0->field_b
    //     0x8b772c: ldur            w2, [x0, #0xb]
    // 0x8b7730: DecompressPointer r2
    //     0x8b7730: add             x2, x2, HEAP, lsl #32
    // 0x8b7734: stur            x2, [fp, #-8]
    // 0x8b7738: r1 = 3
    //     0x8b7738: movz            x1, #0x3
    // 0x8b773c: r0 = AllocateContext()
    //     0x8b773c: bl              #0xec126c  ; AllocateContextStub
    // 0x8b7740: mov             x3, x0
    // 0x8b7744: ldur            x0, [fp, #-0x10]
    // 0x8b7748: stur            x3, [fp, #-0x18]
    // 0x8b774c: StoreField: r3->field_b = r0
    //     0x8b774c: stur            w0, [x3, #0xb]
    // 0x8b7750: StoreField: r3->field_f = rZR
    //     0x8b7750: stur            wzr, [x3, #0xf]
    // 0x8b7754: StoreField: r3->field_13 = rZR
    //     0x8b7754: stur            wzr, [x3, #0x13]
    // 0x8b7758: mov             x2, x3
    // 0x8b775c: r1 = Function 'onDone': static.
    //     0x8b775c: add             x1, PP, #0xe, lsl #12  ; [pp+0xeab8] AnonymousClosure: static (0x8b7fbc), in [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::_buildController (0x8b6fa0)
    //     0x8b7760: ldr             x1, [x1, #0xab8]
    // 0x8b7764: r0 = AllocateClosure()
    //     0x8b7764: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b7768: mov             x1, x0
    // 0x8b776c: ldur            x0, [fp, #-8]
    // 0x8b7770: StoreField: r1->field_b = r0
    //     0x8b7770: stur            w0, [x1, #0xb]
    // 0x8b7774: ldur            x4, [fp, #-0x18]
    // 0x8b7778: ArrayStore: r4[0] = r1  ; List_4
    //     0x8b7778: stur            w1, [x4, #0x17]
    // 0x8b777c: mov             x1, x0
    // 0x8b7780: r2 = Null
    //     0x8b7780: mov             x2, NULL
    // 0x8b7784: r3 = <Stream<Y0>, StreamSubscription<Y0>>
    //     0x8b7784: add             x3, PP, #0xe, lsl #12  ; [pp+0xeac0] TypeArguments: <Stream<Y0>, StreamSubscription<Y0>>
    //     0x8b7788: ldr             x3, [x3, #0xac0]
    // 0x8b778c: r30 = InstantiateTypeArgumentsStub
    //     0x8b778c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8b7790: LoadField: r30 = r30->field_7
    //     0x8b7790: ldur            lr, [lr, #7]
    // 0x8b7794: blr             lr
    // 0x8b7798: mov             x3, x0
    // 0x8b779c: ldur            x0, [fp, #-0x10]
    // 0x8b77a0: stur            x3, [fp, #-0x28]
    // 0x8b77a4: LoadField: r4 = r0->field_f
    //     0x8b77a4: ldur            w4, [x0, #0xf]
    // 0x8b77a8: DecompressPointer r4
    //     0x8b77a8: add             x4, x4, HEAP, lsl #32
    // 0x8b77ac: ldur            x2, [fp, #-0x18]
    // 0x8b77b0: stur            x4, [fp, #-0x20]
    // 0x8b77b4: r1 = Function '<anonymous closure>': static.
    //     0x8b77b4: add             x1, PP, #0xe, lsl #12  ; [pp+0xeac8] AnonymousClosure: static (0x8b7b30), in [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::_buildController (0x8b6fa0)
    //     0x8b77b8: ldr             x1, [x1, #0xac8]
    // 0x8b77bc: r0 = AllocateClosure()
    //     0x8b77bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b77c0: ldur            x1, [fp, #-8]
    // 0x8b77c4: StoreField: r0->field_b = r1
    //     0x8b77c4: stur            w1, [x0, #0xb]
    // 0x8b77c8: ldur            x16, [fp, #-0x28]
    // 0x8b77cc: ldur            lr, [fp, #-0x20]
    // 0x8b77d0: stp             lr, x16, [SP, #8]
    // 0x8b77d4: str             x0, [SP]
    // 0x8b77d8: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x8b77d8: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x8b77dc: r0 = MapNotNullIterableExtension.mapIndexed()
    //     0x8b77dc: bl              #0x8b78e8  ; [package:rxdart/src/utils/collection_extensions.dart] ::MapNotNullIterableExtension.mapIndexed
    // 0x8b77e0: LoadField: r1 = r0->field_7
    //     0x8b77e0: ldur            w1, [x0, #7]
    // 0x8b77e4: DecompressPointer r1
    //     0x8b77e4: add             x1, x1, HEAP, lsl #32
    // 0x8b77e8: mov             x2, x0
    // 0x8b77ec: r0 = _List.of()
    //     0x8b77ec: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x8b77f0: mov             x2, x0
    // 0x8b77f4: ldur            x1, [fp, #-0x10]
    // 0x8b77f8: StoreField: r1->field_1b = r0
    //     0x8b77f8: stur            w0, [x1, #0x1b]
    //     0x8b77fc: ldurb           w16, [x1, #-1]
    //     0x8b7800: ldurb           w17, [x0, #-1]
    //     0x8b7804: and             x16, x17, x16, lsr #2
    //     0x8b7808: tst             x16, HEAP, lsr #32
    //     0x8b780c: b.eq            #0x8b7814
    //     0x8b7810: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b7814: str             x2, [SP]
    // 0x8b7818: r0 = length()
    //     0x8b7818: bl              #0xa96414  ; [dart:core] _GrowableList::length
    // 0x8b781c: cbnz            w0, #0x8b7834
    // 0x8b7820: ldur            x0, [fp, #-0x10]
    // 0x8b7824: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b7824: ldur            w1, [x0, #0x17]
    // 0x8b7828: DecompressPointer r1
    //     0x8b7828: add             x1, x1, HEAP, lsl #32
    // 0x8b782c: r0 = close()
    //     0x8b782c: bl              #0xc67e4c  ; [dart:async] _StreamController::close
    // 0x8b7830: b               #0x8b78d0
    // 0x8b7834: ldur            x0, [fp, #-0x10]
    // 0x8b7838: ldur            x1, [fp, #-8]
    // 0x8b783c: r2 = Null
    //     0x8b783c: mov             x2, NULL
    // 0x8b7840: r3 = <Y0?>
    //     0x8b7840: add             x3, PP, #0xe, lsl #12  ; [pp+0xead0] TypeArguments: <Y0?>
    //     0x8b7844: ldr             x3, [x3, #0xad0]
    // 0x8b7848: r0 = Null
    //     0x8b7848: mov             x0, NULL
    // 0x8b784c: cmp             x2, x0
    // 0x8b7850: b.ne            #0x8b785c
    // 0x8b7854: cmp             x1, x0
    // 0x8b7858: b.eq            #0x8b7868
    // 0x8b785c: r30 = InstantiateTypeArgumentsMayShareFunctionTAStub
    //     0x8b785c: ldr             lr, [PP, #0x3f8]  ; [pp+0x3f8] Stub: InstantiateTypeArgumentsMayShareFunctionTA (0x5e0c48)
    // 0x8b7860: LoadField: r30 = r30->field_7
    //     0x8b7860: ldur            lr, [lr, #7]
    // 0x8b7864: blr             lr
    // 0x8b7868: mov             x1, x0
    // 0x8b786c: ldur            x0, [fp, #-0x10]
    // 0x8b7870: stur            x1, [fp, #-8]
    // 0x8b7874: LoadField: r2 = r0->field_1b
    //     0x8b7874: ldur            w2, [x0, #0x1b]
    // 0x8b7878: DecompressPointer r2
    //     0x8b7878: add             x2, x2, HEAP, lsl #32
    // 0x8b787c: r16 = Sentinel
    //     0x8b787c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b7880: cmp             w2, w16
    // 0x8b7884: b.ne            #0x8b7898
    // 0x8b7888: r16 = "subscriptions"
    //     0x8b7888: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0x8b788c: ldr             x16, [x16, #0xa78]
    // 0x8b7890: str             x16, [SP]
    // 0x8b7894: r0 = _throwLocalNotInitialized()
    //     0x8b7894: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8b7898: ldur            x0, [fp, #-0x10]
    // 0x8b789c: LoadField: r1 = r0->field_1b
    //     0x8b789c: ldur            w1, [x0, #0x1b]
    // 0x8b78a0: DecompressPointer r1
    //     0x8b78a0: add             x1, x1, HEAP, lsl #32
    // 0x8b78a4: LoadField: r2 = r1->field_b
    //     0x8b78a4: ldur            w2, [x1, #0xb]
    // 0x8b78a8: ldur            x1, [fp, #-8]
    // 0x8b78ac: r0 = AllocateArray()
    //     0x8b78ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8b78b0: ldur            x1, [fp, #-0x10]
    // 0x8b78b4: StoreField: r1->field_1f = r0
    //     0x8b78b4: stur            w0, [x1, #0x1f]
    //     0x8b78b8: ldurb           w16, [x1, #-1]
    //     0x8b78bc: ldurb           w17, [x0, #-1]
    //     0x8b78c0: and             x16, x17, x16, lsr #2
    //     0x8b78c4: tst             x16, HEAP, lsr #32
    //     0x8b78c8: b.eq            #0x8b78d0
    //     0x8b78cc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b78d0: r0 = Null
    //     0x8b78d0: mov             x0, NULL
    // 0x8b78d4: LeaveFrame
    //     0x8b78d4: mov             SP, fp
    //     0x8b78d8: ldp             fp, lr, [SP], #0x10
    // 0x8b78dc: ret
    //     0x8b78dc: ret             
    // 0x8b78e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b78e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b78e4: b               #0x8b772c
  }
  [closure] static StreamSubscription<Y0> <anonymous closure>(dynamic, int, Stream<Y0>) {
    // ** addr: 0x8b7b30, size: 0xec
    // 0x8b7b30: EnterFrame
    //     0x8b7b30: stp             fp, lr, [SP, #-0x10]!
    //     0x8b7b34: mov             fp, SP
    // 0x8b7b38: AllocStack(0x28)
    //     0x8b7b38: sub             SP, SP, #0x28
    // 0x8b7b3c: SetupParameters()
    //     0x8b7b3c: ldr             x0, [fp, #0x20]
    //     0x8b7b40: ldur            w1, [x0, #0x17]
    //     0x8b7b44: add             x1, x1, HEAP, lsl #32
    //     0x8b7b48: stur            x1, [fp, #-8]
    // 0x8b7b4c: CheckStackOverflow
    //     0x8b7b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7b50: cmp             SP, x16
    //     0x8b7b54: b.ls            #0x8b7c14
    // 0x8b7b58: r1 = 2
    //     0x8b7b58: movz            x1, #0x2
    // 0x8b7b5c: r0 = AllocateContext()
    //     0x8b7b5c: bl              #0xec126c  ; AllocateContextStub
    // 0x8b7b60: mov             x1, x0
    // 0x8b7b64: ldur            x0, [fp, #-8]
    // 0x8b7b68: StoreField: r1->field_b = r0
    //     0x8b7b68: stur            w0, [x1, #0xb]
    // 0x8b7b6c: ldr             x2, [fp, #0x18]
    // 0x8b7b70: StoreField: r1->field_f = r2
    //     0x8b7b70: stur            w2, [x1, #0xf]
    // 0x8b7b74: ldr             x2, [fp, #0x20]
    // 0x8b7b78: LoadField: r3 = r2->field_b
    //     0x8b7b78: ldur            w3, [x2, #0xb]
    // 0x8b7b7c: DecompressPointer r3
    //     0x8b7b7c: add             x3, x3, HEAP, lsl #32
    // 0x8b7b80: stur            x3, [fp, #-0x10]
    // 0x8b7b84: r2 = false
    //     0x8b7b84: add             x2, NULL, #0x30  ; false
    // 0x8b7b88: StoreField: r1->field_13 = r2
    //     0x8b7b88: stur            w2, [x1, #0x13]
    // 0x8b7b8c: mov             x2, x1
    // 0x8b7b90: r1 = Function '<anonymous closure>': static.
    //     0x8b7b90: add             x1, PP, #0xe, lsl #12  ; [pp+0xead8] AnonymousClosure: static (0x8b7c1c), in [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::_buildController (0x8b6fa0)
    //     0x8b7b94: ldr             x1, [x1, #0xad8]
    // 0x8b7b98: r0 = AllocateClosure()
    //     0x8b7b98: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b7b9c: mov             x3, x0
    // 0x8b7ba0: ldur            x0, [fp, #-0x10]
    // 0x8b7ba4: stur            x3, [fp, #-0x18]
    // 0x8b7ba8: StoreField: r3->field_b = r0
    //     0x8b7ba8: stur            w0, [x3, #0xb]
    // 0x8b7bac: ldur            x0, [fp, #-8]
    // 0x8b7bb0: LoadField: r1 = r0->field_b
    //     0x8b7bb0: ldur            w1, [x0, #0xb]
    // 0x8b7bb4: DecompressPointer r1
    //     0x8b7bb4: add             x1, x1, HEAP, lsl #32
    // 0x8b7bb8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x8b7bb8: ldur            w2, [x1, #0x17]
    // 0x8b7bbc: DecompressPointer r2
    //     0x8b7bbc: add             x2, x2, HEAP, lsl #32
    // 0x8b7bc0: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x8b7bc0: ldur            w4, [x0, #0x17]
    // 0x8b7bc4: DecompressPointer r4
    //     0x8b7bc4: add             x4, x4, HEAP, lsl #32
    // 0x8b7bc8: stur            x4, [fp, #-0x10]
    // 0x8b7bcc: r1 = Function 'addError':.
    //     0x8b7bcc: add             x1, PP, #0xe, lsl #12  ; [pp+0xeae0] AnonymousClosure: (0x7216b4), in [dart:async] _StreamController::addError (0xcf039c)
    //     0x8b7bd0: ldr             x1, [x1, #0xae0]
    // 0x8b7bd4: r0 = AllocateClosure()
    //     0x8b7bd4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b7bd8: ldr             x1, [fp, #0x10]
    // 0x8b7bdc: r2 = LoadClassIdInstr(r1)
    //     0x8b7bdc: ldur            x2, [x1, #-1]
    //     0x8b7be0: ubfx            x2, x2, #0xc, #0x14
    // 0x8b7be4: ldur            x16, [fp, #-0x10]
    // 0x8b7be8: stp             x16, x0, [SP]
    // 0x8b7bec: mov             x0, x2
    // 0x8b7bf0: ldur            x2, [fp, #-0x18]
    // 0x8b7bf4: r4 = const [0, 0x4, 0x2, 0x2, onDone, 0x3, onError, 0x2, null]
    //     0x8b7bf4: add             x4, PP, #0xc, lsl #12  ; [pp+0xc168] List(9) [0, 0x4, 0x2, 0x2, "onDone", 0x3, "onError", 0x2, Null]
    //     0x8b7bf8: ldr             x4, [x4, #0x168]
    // 0x8b7bfc: r0 = GDT[cid_x0 + 0x64e]()
    //     0x8b7bfc: add             lr, x0, #0x64e
    //     0x8b7c00: ldr             lr, [x21, lr, lsl #3]
    //     0x8b7c04: blr             lr
    // 0x8b7c08: LeaveFrame
    //     0x8b7c08: mov             SP, fp
    //     0x8b7c0c: ldp             fp, lr, [SP], #0x10
    // 0x8b7c10: ret
    //     0x8b7c10: ret             
    // 0x8b7c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7c14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7c18: b               #0x8b7b58
  }
  [closure] static void <anonymous closure>(dynamic, Y0) {
    // ** addr: 0x8b7c1c, size: 0x2b0
    // 0x8b7c1c: EnterFrame
    //     0x8b7c1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8b7c20: mov             fp, SP
    // 0x8b7c24: AllocStack(0x80)
    //     0x8b7c24: sub             SP, SP, #0x80
    // 0x8b7c28: SetupParameters()
    //     0x8b7c28: ldr             x0, [fp, #0x18]
    //     0x8b7c2c: ldur            w3, [x0, #0x17]
    //     0x8b7c30: add             x3, x3, HEAP, lsl #32
    //     0x8b7c34: stur            x3, [fp, #-0x70]
    // 0x8b7c38: CheckStackOverflow
    //     0x8b7c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7c3c: cmp             SP, x16
    //     0x8b7c40: b.ls            #0x8b7ebc
    // 0x8b7c44: LoadField: r4 = r0->field_b
    //     0x8b7c44: ldur            w4, [x0, #0xb]
    // 0x8b7c48: DecompressPointer r4
    //     0x8b7c48: add             x4, x4, HEAP, lsl #32
    // 0x8b7c4c: stur            x4, [fp, #-0x68]
    // 0x8b7c50: LoadField: r5 = r3->field_b
    //     0x8b7c50: ldur            w5, [x3, #0xb]
    // 0x8b7c54: DecompressPointer r5
    //     0x8b7c54: add             x5, x5, HEAP, lsl #32
    // 0x8b7c58: stur            x5, [fp, #-0x60]
    // 0x8b7c5c: LoadField: r6 = r5->field_b
    //     0x8b7c5c: ldur            w6, [x5, #0xb]
    // 0x8b7c60: DecompressPointer r6
    //     0x8b7c60: add             x6, x6, HEAP, lsl #32
    // 0x8b7c64: stur            x6, [fp, #-0x58]
    // 0x8b7c68: LoadField: r7 = r6->field_1f
    //     0x8b7c68: ldur            w7, [x6, #0x1f]
    // 0x8b7c6c: DecompressPointer r7
    //     0x8b7c6c: add             x7, x7, HEAP, lsl #32
    // 0x8b7c70: stur            x7, [fp, #-0x50]
    // 0x8b7c74: cmp             w7, NULL
    // 0x8b7c78: b.ne            #0x8b7c8c
    // 0x8b7c7c: r0 = Null
    //     0x8b7c7c: mov             x0, NULL
    // 0x8b7c80: LeaveFrame
    //     0x8b7c80: mov             SP, fp
    //     0x8b7c84: ldp             fp, lr, [SP], #0x10
    // 0x8b7c88: ret
    //     0x8b7c88: ret             
    // 0x8b7c8c: LoadField: r8 = r3->field_f
    //     0x8b7c8c: ldur            w8, [x3, #0xf]
    // 0x8b7c90: DecompressPointer r8
    //     0x8b7c90: add             x8, x8, HEAP, lsl #32
    // 0x8b7c94: stur            x8, [fp, #-0x48]
    // 0x8b7c98: LoadField: r2 = r7->field_7
    //     0x8b7c98: ldur            w2, [x7, #7]
    // 0x8b7c9c: DecompressPointer r2
    //     0x8b7c9c: add             x2, x2, HEAP, lsl #32
    // 0x8b7ca0: ldr             x0, [fp, #0x10]
    // 0x8b7ca4: r1 = Null
    //     0x8b7ca4: mov             x1, NULL
    // 0x8b7ca8: cmp             w2, NULL
    // 0x8b7cac: b.eq            #0x8b7ccc
    // 0x8b7cb0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8b7cb0: ldur            w4, [x2, #0x17]
    // 0x8b7cb4: DecompressPointer r4
    //     0x8b7cb4: add             x4, x4, HEAP, lsl #32
    // 0x8b7cb8: r8 = X0
    //     0x8b7cb8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8b7cbc: LoadField: r9 = r4->field_7
    //     0x8b7cbc: ldur            x9, [x4, #7]
    // 0x8b7cc0: r3 = Null
    //     0x8b7cc0: add             x3, PP, #0xe, lsl #12  ; [pp+0xeae8] Null
    //     0x8b7cc4: ldr             x3, [x3, #0xae8]
    // 0x8b7cc8: blr             x9
    // 0x8b7ccc: ldur            x2, [fp, #-0x50]
    // 0x8b7cd0: LoadField: r0 = r2->field_b
    //     0x8b7cd0: ldur            w0, [x2, #0xb]
    // 0x8b7cd4: ldur            x1, [fp, #-0x48]
    // 0x8b7cd8: r3 = LoadInt32Instr(r1)
    //     0x8b7cd8: sbfx            x3, x1, #1, #0x1f
    //     0x8b7cdc: tbz             w1, #0, #0x8b7ce4
    //     0x8b7ce0: ldur            x3, [x1, #7]
    // 0x8b7ce4: r1 = LoadInt32Instr(r0)
    //     0x8b7ce4: sbfx            x1, x0, #1, #0x1f
    // 0x8b7ce8: mov             x0, x1
    // 0x8b7cec: mov             x1, x3
    // 0x8b7cf0: cmp             x1, x0
    // 0x8b7cf4: b.hs            #0x8b7ec4
    // 0x8b7cf8: mov             x1, x2
    // 0x8b7cfc: ldr             x0, [fp, #0x10]
    // 0x8b7d00: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8b7d00: add             x25, x1, x3, lsl #2
    //     0x8b7d04: add             x25, x25, #0xf
    //     0x8b7d08: str             w0, [x25]
    //     0x8b7d0c: tbz             w0, #0, #0x8b7d28
    //     0x8b7d10: ldurb           w16, [x1, #-1]
    //     0x8b7d14: ldurb           w17, [x0, #-1]
    //     0x8b7d18: and             x16, x17, x16, lsr #2
    //     0x8b7d1c: tst             x16, HEAP, lsr #32
    //     0x8b7d20: b.eq            #0x8b7d28
    //     0x8b7d24: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b7d28: ldur            x2, [fp, #-0x70]
    // 0x8b7d2c: LoadField: r0 = r2->field_13
    //     0x8b7d2c: ldur            w0, [x2, #0x13]
    // 0x8b7d30: DecompressPointer r0
    //     0x8b7d30: add             x0, x0, HEAP, lsl #32
    // 0x8b7d34: tbz             w0, #4, #0x8b7d94
    // 0x8b7d38: ldur            x3, [fp, #-0x60]
    // 0x8b7d3c: r0 = true
    //     0x8b7d3c: add             x0, NULL, #0x20  ; true
    // 0x8b7d40: StoreField: r2->field_13 = r0
    //     0x8b7d40: stur            w0, [x2, #0x13]
    // 0x8b7d44: LoadField: r0 = r3->field_f
    //     0x8b7d44: ldur            w0, [x3, #0xf]
    // 0x8b7d48: DecompressPointer r0
    //     0x8b7d48: add             x0, x0, HEAP, lsl #32
    // 0x8b7d4c: r1 = LoadInt32Instr(r0)
    //     0x8b7d4c: sbfx            x1, x0, #1, #0x1f
    //     0x8b7d50: tbz             w0, #0, #0x8b7d58
    //     0x8b7d54: ldur            x1, [x0, #7]
    // 0x8b7d58: add             x4, x1, #1
    // 0x8b7d5c: r0 = BoxInt64Instr(r4)
    //     0x8b7d5c: sbfiz           x0, x4, #1, #0x1f
    //     0x8b7d60: cmp             x4, x0, asr #1
    //     0x8b7d64: b.eq            #0x8b7d70
    //     0x8b7d68: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b7d6c: stur            x4, [x0, #7]
    // 0x8b7d70: StoreField: r3->field_f = r0
    //     0x8b7d70: stur            w0, [x3, #0xf]
    //     0x8b7d74: tbz             w0, #0, #0x8b7d90
    //     0x8b7d78: ldurb           w16, [x3, #-1]
    //     0x8b7d7c: ldurb           w17, [x0, #-1]
    //     0x8b7d80: and             x16, x17, x16, lsr #2
    //     0x8b7d84: tst             x16, HEAP, lsr #32
    //     0x8b7d88: b.eq            #0x8b7d90
    //     0x8b7d8c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8b7d90: b               #0x8b7d98
    // 0x8b7d94: ldur            x3, [fp, #-0x60]
    // 0x8b7d98: ldur            x0, [fp, #-0x58]
    // 0x8b7d9c: LoadField: r1 = r3->field_f
    //     0x8b7d9c: ldur            w1, [x3, #0xf]
    // 0x8b7da0: DecompressPointer r1
    //     0x8b7da0: add             x1, x1, HEAP, lsl #32
    // 0x8b7da4: stur            x1, [fp, #-0x48]
    // 0x8b7da8: LoadField: r3 = r0->field_1b
    //     0x8b7da8: ldur            w3, [x0, #0x1b]
    // 0x8b7dac: DecompressPointer r3
    //     0x8b7dac: add             x3, x3, HEAP, lsl #32
    // 0x8b7db0: r16 = Sentinel
    //     0x8b7db0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b7db4: cmp             w3, w16
    // 0x8b7db8: b.ne            #0x8b7dcc
    // 0x8b7dbc: r16 = "subscriptions"
    //     0x8b7dbc: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0x8b7dc0: ldr             x16, [x16, #0xa78]
    // 0x8b7dc4: str             x16, [SP]
    // 0x8b7dc8: r0 = _throwLocalNotInitialized()
    //     0x8b7dc8: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8b7dcc: ldur            x0, [fp, #-0x58]
    // 0x8b7dd0: ldur            x1, [fp, #-0x48]
    // 0x8b7dd4: LoadField: r2 = r0->field_1b
    //     0x8b7dd4: ldur            w2, [x0, #0x1b]
    // 0x8b7dd8: DecompressPointer r2
    //     0x8b7dd8: add             x2, x2, HEAP, lsl #32
    // 0x8b7ddc: LoadField: r3 = r2->field_b
    //     0x8b7ddc: ldur            w3, [x2, #0xb]
    // 0x8b7de0: cmp             w1, w3
    // 0x8b7de4: b.ne            #0x8b7e68
    // 0x8b7de8: LoadField: r3 = r0->field_13
    //     0x8b7de8: ldur            w3, [x0, #0x13]
    // 0x8b7dec: DecompressPointer r3
    //     0x8b7dec: add             x3, x3, HEAP, lsl #32
    // 0x8b7df0: stur            x3, [fp, #-0x50]
    // 0x8b7df4: LoadField: r4 = r0->field_1f
    //     0x8b7df4: ldur            w4, [x0, #0x1f]
    // 0x8b7df8: DecompressPointer r4
    //     0x8b7df8: add             x4, x4, HEAP, lsl #32
    // 0x8b7dfc: stur            x4, [fp, #-0x48]
    // 0x8b7e00: cmp             w4, NULL
    // 0x8b7e04: b.eq            #0x8b7ec8
    // 0x8b7e08: r16 = false
    //     0x8b7e08: add             x16, NULL, #0x30  ; false
    // 0x8b7e0c: str             x16, [SP]
    // 0x8b7e10: ldur            x1, [fp, #-0x68]
    // 0x8b7e14: mov             x2, x4
    // 0x8b7e18: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0x8b7e18: add             x4, PP, #0xb, lsl #12  ; [pp+0xbcc0] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0x8b7e1c: ldr             x4, [x4, #0xcc0]
    // 0x8b7e20: r0 = List.from()
    //     0x8b7e20: bl              #0x6b9500  ; [dart:core] List::List.from
    // 0x8b7e24: stur            x0, [fp, #-0x60]
    // 0x8b7e28: ldur            x16, [fp, #-0x68]
    // 0x8b7e2c: stp             x0, x16, [SP]
    // 0x8b7e30: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8b7e30: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8b7e34: r0 = makeFixedListUnmodifiable()
    //     0x8b7e34: bl              #0x8b7f28  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0x8b7e38: ldur            x16, [fp, #-0x50]
    // 0x8b7e3c: stp             x0, x16, [SP]
    // 0x8b7e40: ldur            x0, [fp, #-0x50]
    // 0x8b7e44: ClosureCall
    //     0x8b7e44: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x8b7e48: ldur            x2, [x0, #0x1f]
    //     0x8b7e4c: blr             x2
    // 0x8b7e50: ldur            x1, [fp, #-0x58]
    // 0x8b7e54: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x8b7e54: ldur            w2, [x1, #0x17]
    // 0x8b7e58: DecompressPointer r2
    //     0x8b7e58: add             x2, x2, HEAP, lsl #32
    // 0x8b7e5c: mov             x1, x2
    // 0x8b7e60: mov             x2, x0
    // 0x8b7e64: r0 = add()
    //     0x8b7e64: bl              #0xccb4dc  ; [dart:async] _StreamController::add
    // 0x8b7e68: r0 = Null
    //     0x8b7e68: mov             x0, NULL
    // 0x8b7e6c: LeaveFrame
    //     0x8b7e6c: mov             SP, fp
    //     0x8b7e70: ldp             fp, lr, [SP], #0x10
    // 0x8b7e74: ret
    //     0x8b7e74: ret             
    // 0x8b7e78: sub             SP, fp, #0x80
    // 0x8b7e7c: mov             x2, x0
    // 0x8b7e80: ldur            x0, [fp, #-0x18]
    // 0x8b7e84: LoadField: r3 = r0->field_b
    //     0x8b7e84: ldur            w3, [x0, #0xb]
    // 0x8b7e88: DecompressPointer r3
    //     0x8b7e88: add             x3, x3, HEAP, lsl #32
    // 0x8b7e8c: LoadField: r0 = r3->field_b
    //     0x8b7e8c: ldur            w0, [x3, #0xb]
    // 0x8b7e90: DecompressPointer r0
    //     0x8b7e90: add             x0, x0, HEAP, lsl #32
    // 0x8b7e94: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x8b7e94: ldur            w3, [x0, #0x17]
    // 0x8b7e98: DecompressPointer r3
    //     0x8b7e98: add             x3, x3, HEAP, lsl #32
    // 0x8b7e9c: str             x1, [SP]
    // 0x8b7ea0: mov             x1, x3
    // 0x8b7ea4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8b7ea4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8b7ea8: r0 = addError()
    //     0x8b7ea8: bl              #0xcf039c  ; [dart:async] _StreamController::addError
    // 0x8b7eac: r0 = Null
    //     0x8b7eac: mov             x0, NULL
    // 0x8b7eb0: LeaveFrame
    //     0x8b7eb0: mov             SP, fp
    //     0x8b7eb4: ldp             fp, lr, [SP], #0x10
    // 0x8b7eb8: ret
    //     0x8b7eb8: ret             
    // 0x8b7ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7ebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7ec0: b               #0x8b7c44
    // 0x8b7ec4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b7ec4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b7ec8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8b7ec8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static void onDone(dynamic) {
    // ** addr: 0x8b7fbc, size: 0xec
    // 0x8b7fbc: EnterFrame
    //     0x8b7fbc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b7fc0: mov             fp, SP
    // 0x8b7fc4: AllocStack(0x18)
    //     0x8b7fc4: sub             SP, SP, #0x18
    // 0x8b7fc8: SetupParameters()
    //     0x8b7fc8: ldr             x0, [fp, #0x10]
    //     0x8b7fcc: ldur            w2, [x0, #0x17]
    //     0x8b7fd0: add             x2, x2, HEAP, lsl #32
    // 0x8b7fd4: CheckStackOverflow
    //     0x8b7fd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7fd8: cmp             SP, x16
    //     0x8b7fdc: b.ls            #0x8b80a0
    // 0x8b7fe0: LoadField: r0 = r2->field_13
    //     0x8b7fe0: ldur            w0, [x2, #0x13]
    // 0x8b7fe4: DecompressPointer r0
    //     0x8b7fe4: add             x0, x0, HEAP, lsl #32
    // 0x8b7fe8: r1 = LoadInt32Instr(r0)
    //     0x8b7fe8: sbfx            x1, x0, #1, #0x1f
    //     0x8b7fec: tbz             w0, #0, #0x8b7ff4
    //     0x8b7ff0: ldur            x1, [x0, #7]
    // 0x8b7ff4: add             x3, x1, #1
    // 0x8b7ff8: stur            x3, [fp, #-0x10]
    // 0x8b7ffc: r0 = BoxInt64Instr(r3)
    //     0x8b7ffc: sbfiz           x0, x3, #1, #0x1f
    //     0x8b8000: cmp             x3, x0, asr #1
    //     0x8b8004: b.eq            #0x8b8010
    //     0x8b8008: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b800c: stur            x3, [x0, #7]
    // 0x8b8010: StoreField: r2->field_13 = r0
    //     0x8b8010: stur            w0, [x2, #0x13]
    //     0x8b8014: tbz             w0, #0, #0x8b8030
    //     0x8b8018: ldurb           w16, [x2, #-1]
    //     0x8b801c: ldurb           w17, [x0, #-1]
    //     0x8b8020: and             x16, x17, x16, lsr #2
    //     0x8b8024: tst             x16, HEAP, lsr #32
    //     0x8b8028: b.eq            #0x8b8030
    //     0x8b802c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8b8030: LoadField: r0 = r2->field_b
    //     0x8b8030: ldur            w0, [x2, #0xb]
    // 0x8b8034: DecompressPointer r0
    //     0x8b8034: add             x0, x0, HEAP, lsl #32
    // 0x8b8038: stur            x0, [fp, #-8]
    // 0x8b803c: LoadField: r1 = r0->field_1b
    //     0x8b803c: ldur            w1, [x0, #0x1b]
    // 0x8b8040: DecompressPointer r1
    //     0x8b8040: add             x1, x1, HEAP, lsl #32
    // 0x8b8044: r16 = Sentinel
    //     0x8b8044: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8b8048: cmp             w1, w16
    // 0x8b804c: b.ne            #0x8b8060
    // 0x8b8050: r16 = "subscriptions"
    //     0x8b8050: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0x8b8054: ldr             x16, [x16, #0xa78]
    // 0x8b8058: str             x16, [SP]
    // 0x8b805c: r0 = _throwLocalNotInitialized()
    //     0x8b805c: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8b8060: ldur            x0, [fp, #-0x10]
    // 0x8b8064: ldur            x1, [fp, #-8]
    // 0x8b8068: LoadField: r2 = r1->field_1b
    //     0x8b8068: ldur            w2, [x1, #0x1b]
    // 0x8b806c: DecompressPointer r2
    //     0x8b806c: add             x2, x2, HEAP, lsl #32
    // 0x8b8070: LoadField: r3 = r2->field_b
    //     0x8b8070: ldur            w3, [x2, #0xb]
    // 0x8b8074: r2 = LoadInt32Instr(r3)
    //     0x8b8074: sbfx            x2, x3, #1, #0x1f
    // 0x8b8078: cmp             x0, x2
    // 0x8b807c: b.ne            #0x8b8090
    // 0x8b8080: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8b8080: ldur            w0, [x1, #0x17]
    // 0x8b8084: DecompressPointer r0
    //     0x8b8084: add             x0, x0, HEAP, lsl #32
    // 0x8b8088: mov             x1, x0
    // 0x8b808c: r0 = close()
    //     0x8b808c: bl              #0xc67e4c  ; [dart:async] _StreamController::close
    // 0x8b8090: r0 = Null
    //     0x8b8090: mov             x0, NULL
    // 0x8b8094: LeaveFrame
    //     0x8b8094: mov             SP, fp
    //     0x8b8098: ldp             fp, lr, [SP], #0x10
    // 0x8b809c: ret
    //     0x8b809c: ret             
    // 0x8b80a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b80a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b80a4: b               #0x8b7fe0
  }
  [closure] static Y2 <anonymous closure>(dynamic, List<dynamic>) {
    // ** addr: 0x8b80b4, size: 0x134
    // 0x8b80b4: EnterFrame
    //     0x8b80b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8b80b8: mov             fp, SP
    // 0x8b80bc: AllocStack(0x30)
    //     0x8b80bc: sub             SP, SP, #0x30
    // 0x8b80c0: SetupParameters()
    //     0x8b80c0: ldr             x0, [fp, #0x18]
    //     0x8b80c4: ldur            w1, [x0, #0x17]
    //     0x8b80c8: add             x1, x1, HEAP, lsl #32
    // 0x8b80cc: CheckStackOverflow
    //     0x8b80cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b80d0: cmp             SP, x16
    //     0x8b80d4: b.ls            #0x8b81e0
    // 0x8b80d8: LoadField: r2 = r0->field_b
    //     0x8b80d8: ldur            w2, [x0, #0xb]
    // 0x8b80dc: DecompressPointer r2
    //     0x8b80dc: add             x2, x2, HEAP, lsl #32
    // 0x8b80e0: stur            x2, [fp, #-0x10]
    // 0x8b80e4: LoadField: r3 = r1->field_f
    //     0x8b80e4: ldur            w3, [x1, #0xf]
    // 0x8b80e8: DecompressPointer r3
    //     0x8b80e8: add             x3, x3, HEAP, lsl #32
    // 0x8b80ec: ldr             x1, [fp, #0x10]
    // 0x8b80f0: stur            x3, [fp, #-8]
    // 0x8b80f4: r0 = LoadClassIdInstr(r1)
    //     0x8b80f4: ldur            x0, [x1, #-1]
    //     0x8b80f8: ubfx            x0, x0, #0xc, #0x14
    // 0x8b80fc: stp             xzr, x1, [SP]
    // 0x8b8100: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b8100: movz            x17, #0x3037
    //     0x8b8104: movk            x17, #0x1, lsl #16
    //     0x8b8108: add             lr, x0, x17
    //     0x8b810c: ldr             lr, [x21, lr, lsl #3]
    //     0x8b8110: blr             lr
    // 0x8b8114: ldur            x1, [fp, #-0x10]
    // 0x8b8118: mov             x3, x0
    // 0x8b811c: r2 = Null
    //     0x8b811c: mov             x2, NULL
    // 0x8b8120: stur            x3, [fp, #-0x18]
    // 0x8b8124: cmp             w1, NULL
    // 0x8b8128: b.eq            #0x8b814c
    // 0x8b812c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x8b812c: ldur            w4, [x1, #0x17]
    // 0x8b8130: DecompressPointer r4
    //     0x8b8130: add             x4, x4, HEAP, lsl #32
    // 0x8b8134: r8 = Y0
    //     0x8b8134: add             x8, PP, #0xe, lsl #12  ; [pp+0xea20] TypeParameter: Y0
    //     0x8b8138: ldr             x8, [x8, #0xa20]
    // 0x8b813c: LoadField: r9 = r4->field_7
    //     0x8b813c: ldur            x9, [x4, #7]
    // 0x8b8140: r3 = Null
    //     0x8b8140: add             x3, PP, #0xe, lsl #12  ; [pp+0xea28] Null
    //     0x8b8144: ldr             x3, [x3, #0xa28]
    // 0x8b8148: blr             x9
    // 0x8b814c: ldr             x0, [fp, #0x10]
    // 0x8b8150: r1 = LoadClassIdInstr(r0)
    //     0x8b8150: ldur            x1, [x0, #-1]
    //     0x8b8154: ubfx            x1, x1, #0xc, #0x14
    // 0x8b8158: r16 = 2
    //     0x8b8158: movz            x16, #0x2
    // 0x8b815c: stp             x16, x0, [SP]
    // 0x8b8160: mov             x0, x1
    // 0x8b8164: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b8164: movz            x17, #0x3037
    //     0x8b8168: movk            x17, #0x1, lsl #16
    //     0x8b816c: add             lr, x0, x17
    //     0x8b8170: ldr             lr, [x21, lr, lsl #3]
    //     0x8b8174: blr             lr
    // 0x8b8178: ldur            x1, [fp, #-0x10]
    // 0x8b817c: mov             x3, x0
    // 0x8b8180: r2 = Null
    //     0x8b8180: mov             x2, NULL
    // 0x8b8184: stur            x3, [fp, #-0x10]
    // 0x8b8188: cmp             w1, NULL
    // 0x8b818c: b.eq            #0x8b81b0
    // 0x8b8190: LoadField: r4 = r1->field_1b
    //     0x8b8190: ldur            w4, [x1, #0x1b]
    // 0x8b8194: DecompressPointer r4
    //     0x8b8194: add             x4, x4, HEAP, lsl #32
    // 0x8b8198: r8 = Y1
    //     0x8b8198: add             x8, PP, #0xe, lsl #12  ; [pp+0xea38] TypeParameter: Y1
    //     0x8b819c: ldr             x8, [x8, #0xa38]
    // 0x8b81a0: LoadField: r9 = r4->field_7
    //     0x8b81a0: ldur            x9, [x4, #7]
    // 0x8b81a4: r3 = Null
    //     0x8b81a4: add             x3, PP, #0xe, lsl #12  ; [pp+0xea40] Null
    //     0x8b81a8: ldr             x3, [x3, #0xa40]
    // 0x8b81ac: blr             x9
    // 0x8b81b0: ldur            x16, [fp, #-8]
    // 0x8b81b4: ldur            lr, [fp, #-0x18]
    // 0x8b81b8: stp             lr, x16, [SP, #8]
    // 0x8b81bc: ldur            x16, [fp, #-0x10]
    // 0x8b81c0: str             x16, [SP]
    // 0x8b81c4: ldur            x0, [fp, #-8]
    // 0x8b81c8: ClosureCall
    //     0x8b81c8: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x8b81cc: ldur            x2, [x0, #0x1f]
    //     0x8b81d0: blr             x2
    // 0x8b81d4: LeaveFrame
    //     0x8b81d4: mov             SP, fp
    //     0x8b81d8: ldp             fp, lr, [SP], #0x10
    // 0x8b81dc: ret
    //     0x8b81dc: ret             
    // 0x8b81e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b81e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b81e4: b               #0x8b80d8
  }
  static CombineLatestStream<dynamic, Y5> combine5<Y0, Y1, Y2, Y3, Y4, Y5>(Stream<Y0>, Stream<Y1>, Stream<Y2>, Stream<Y3>, Stream<Y4>, (dynamic, Y0, Y1, Y2, Y3, Y4) => Y5) {
    // ** addr: 0x8b81e8, size: 0x154
    // 0x8b81e8: EnterFrame
    //     0x8b81e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8b81ec: mov             fp, SP
    // 0x8b81f0: AllocStack(0x28)
    //     0x8b81f0: sub             SP, SP, #0x28
    // 0x8b81f4: SetupParameters()
    //     0x8b81f4: ldur            w0, [x4, #0xf]
    //     0x8b81f8: cbnz            w0, #0x8b8204
    //     0x8b81fc: mov             x6, NULL
    //     0x8b8200: b               #0x8b8214
    //     0x8b8204: ldur            w0, [x4, #0x17]
    //     0x8b8208: add             x1, fp, w0, sxtw #2
    //     0x8b820c: ldr             x1, [x1, #0x10]
    //     0x8b8210: mov             x6, x1
    //     0x8b8214: ldr             x5, [fp, #0x38]
    //     0x8b8218: ldr             x4, [fp, #0x30]
    //     0x8b821c: ldr             x3, [fp, #0x28]
    //     0x8b8220: ldr             x2, [fp, #0x20]
    //     0x8b8224: ldr             x1, [fp, #0x18]
    //     0x8b8228: ldr             x0, [fp, #0x10]
    //     0x8b822c: stur            x6, [fp, #-8]
    // 0x8b8230: CheckStackOverflow
    //     0x8b8230: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b8234: cmp             SP, x16
    //     0x8b8238: b.ls            #0x8b8334
    // 0x8b823c: r1 = 1
    //     0x8b823c: movz            x1, #0x1
    // 0x8b8240: r0 = AllocateContext()
    //     0x8b8240: bl              #0xec126c  ; AllocateContextStub
    // 0x8b8244: mov             x4, x0
    // 0x8b8248: ldr             x0, [fp, #0x10]
    // 0x8b824c: stur            x4, [fp, #-0x10]
    // 0x8b8250: StoreField: r4->field_f = r0
    //     0x8b8250: stur            w0, [x4, #0xf]
    // 0x8b8254: ldur            x1, [fp, #-8]
    // 0x8b8258: r2 = Null
    //     0x8b8258: mov             x2, NULL
    // 0x8b825c: r3 = <Y5, dynamic, Y5>
    //     0x8b825c: add             x3, PP, #0xe, lsl #12  ; [pp+0xeb50] TypeArguments: <Y5, dynamic, Y5>
    //     0x8b8260: ldr             x3, [x3, #0xb50]
    // 0x8b8264: r0 = Null
    //     0x8b8264: mov             x0, NULL
    // 0x8b8268: cmp             x2, x0
    // 0x8b826c: b.ne            #0x8b8278
    // 0x8b8270: cmp             x1, x0
    // 0x8b8274: b.eq            #0x8b8284
    // 0x8b8278: r30 = InstantiateTypeArgumentsStub
    //     0x8b8278: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8b827c: LoadField: r30 = r30->field_7
    //     0x8b827c: ldur            lr, [lr, #7]
    // 0x8b8280: blr             lr
    // 0x8b8284: r1 = Null
    //     0x8b8284: mov             x1, NULL
    // 0x8b8288: r2 = 10
    //     0x8b8288: movz            x2, #0xa
    // 0x8b828c: stur            x0, [fp, #-0x18]
    // 0x8b8290: r0 = AllocateArray()
    //     0x8b8290: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8b8294: mov             x2, x0
    // 0x8b8298: ldr             x0, [fp, #0x38]
    // 0x8b829c: stur            x2, [fp, #-0x20]
    // 0x8b82a0: StoreField: r2->field_f = r0
    //     0x8b82a0: stur            w0, [x2, #0xf]
    // 0x8b82a4: ldr             x0, [fp, #0x30]
    // 0x8b82a8: StoreField: r2->field_13 = r0
    //     0x8b82a8: stur            w0, [x2, #0x13]
    // 0x8b82ac: ldr             x0, [fp, #0x28]
    // 0x8b82b0: ArrayStore: r2[0] = r0  ; List_4
    //     0x8b82b0: stur            w0, [x2, #0x17]
    // 0x8b82b4: ldr             x0, [fp, #0x20]
    // 0x8b82b8: StoreField: r2->field_1b = r0
    //     0x8b82b8: stur            w0, [x2, #0x1b]
    // 0x8b82bc: ldr             x0, [fp, #0x18]
    // 0x8b82c0: StoreField: r2->field_1f = r0
    //     0x8b82c0: stur            w0, [x2, #0x1f]
    // 0x8b82c4: r1 = <Stream>
    //     0x8b82c4: add             x1, PP, #0xe, lsl #12  ; [pp+0xea10] TypeArguments: <Stream>
    //     0x8b82c8: ldr             x1, [x1, #0xa10]
    // 0x8b82cc: r0 = AllocateGrowableArray()
    //     0x8b82cc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8b82d0: mov             x3, x0
    // 0x8b82d4: ldur            x0, [fp, #-0x20]
    // 0x8b82d8: stur            x3, [fp, #-0x28]
    // 0x8b82dc: StoreField: r3->field_f = r0
    //     0x8b82dc: stur            w0, [x3, #0xf]
    // 0x8b82e0: r0 = 10
    //     0x8b82e0: movz            x0, #0xa
    // 0x8b82e4: StoreField: r3->field_b = r0
    //     0x8b82e4: stur            w0, [x3, #0xb]
    // 0x8b82e8: ldur            x2, [fp, #-0x10]
    // 0x8b82ec: r1 = Function '<anonymous closure>': static.
    //     0x8b82ec: add             x1, PP, #0xe, lsl #12  ; [pp+0xeb58] AnonymousClosure: static (0x8b833c), in [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::combine5 (0x8b81e8)
    //     0x8b82f0: ldr             x1, [x1, #0xb58]
    // 0x8b82f4: r0 = AllocateClosure()
    //     0x8b82f4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b82f8: mov             x2, x0
    // 0x8b82fc: ldur            x0, [fp, #-8]
    // 0x8b8300: stur            x2, [fp, #-0x10]
    // 0x8b8304: StoreField: r2->field_b = r0
    //     0x8b8304: stur            w0, [x2, #0xb]
    // 0x8b8308: ldur            x1, [fp, #-0x18]
    // 0x8b830c: r0 = CombineLatestStream()
    //     0x8b830c: bl              #0x8b80a8  ; AllocateCombineLatestStreamStub -> CombineLatestStream<C1X0, C1X1> (size=0x10)
    // 0x8b8310: mov             x1, x0
    // 0x8b8314: ldur            x2, [fp, #-0x28]
    // 0x8b8318: ldur            x3, [fp, #-0x10]
    // 0x8b831c: stur            x0, [fp, #-8]
    // 0x8b8320: r0 = CombineLatestStream()
    //     0x8b8320: bl              #0x8b6ee0  ; [package:rxdart/src/streams/combine_latest.dart] CombineLatestStream::CombineLatestStream
    // 0x8b8324: ldur            x0, [fp, #-8]
    // 0x8b8328: LeaveFrame
    //     0x8b8328: mov             SP, fp
    //     0x8b832c: ldp             fp, lr, [SP], #0x10
    // 0x8b8330: ret
    //     0x8b8330: ret             
    // 0x8b8334: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b8334: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b8338: b               #0x8b823c
  }
  [closure] static Y5 <anonymous closure>(dynamic, List<dynamic>) {
    // ** addr: 0x8b833c, size: 0x264
    // 0x8b833c: EnterFrame
    //     0x8b833c: stp             fp, lr, [SP, #-0x10]!
    //     0x8b8340: mov             fp, SP
    // 0x8b8344: AllocStack(0x60)
    //     0x8b8344: sub             SP, SP, #0x60
    // 0x8b8348: SetupParameters()
    //     0x8b8348: ldr             x0, [fp, #0x18]
    //     0x8b834c: ldur            w1, [x0, #0x17]
    //     0x8b8350: add             x1, x1, HEAP, lsl #32
    // 0x8b8354: CheckStackOverflow
    //     0x8b8354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b8358: cmp             SP, x16
    //     0x8b835c: b.ls            #0x8b8598
    // 0x8b8360: LoadField: r2 = r0->field_b
    //     0x8b8360: ldur            w2, [x0, #0xb]
    // 0x8b8364: DecompressPointer r2
    //     0x8b8364: add             x2, x2, HEAP, lsl #32
    // 0x8b8368: stur            x2, [fp, #-0x10]
    // 0x8b836c: LoadField: r3 = r1->field_f
    //     0x8b836c: ldur            w3, [x1, #0xf]
    // 0x8b8370: DecompressPointer r3
    //     0x8b8370: add             x3, x3, HEAP, lsl #32
    // 0x8b8374: ldr             x1, [fp, #0x10]
    // 0x8b8378: stur            x3, [fp, #-8]
    // 0x8b837c: r0 = LoadClassIdInstr(r1)
    //     0x8b837c: ldur            x0, [x1, #-1]
    //     0x8b8380: ubfx            x0, x0, #0xc, #0x14
    // 0x8b8384: stp             xzr, x1, [SP]
    // 0x8b8388: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b8388: movz            x17, #0x3037
    //     0x8b838c: movk            x17, #0x1, lsl #16
    //     0x8b8390: add             lr, x0, x17
    //     0x8b8394: ldr             lr, [x21, lr, lsl #3]
    //     0x8b8398: blr             lr
    // 0x8b839c: ldur            x1, [fp, #-0x10]
    // 0x8b83a0: mov             x3, x0
    // 0x8b83a4: r2 = Null
    //     0x8b83a4: mov             x2, NULL
    // 0x8b83a8: stur            x3, [fp, #-0x18]
    // 0x8b83ac: cmp             w1, NULL
    // 0x8b83b0: b.eq            #0x8b83d4
    // 0x8b83b4: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x8b83b4: ldur            w4, [x1, #0x17]
    // 0x8b83b8: DecompressPointer r4
    //     0x8b83b8: add             x4, x4, HEAP, lsl #32
    // 0x8b83bc: r8 = Y0
    //     0x8b83bc: add             x8, PP, #0xe, lsl #12  ; [pp+0xeb60] TypeParameter: Y0
    //     0x8b83c0: ldr             x8, [x8, #0xb60]
    // 0x8b83c4: LoadField: r9 = r4->field_7
    //     0x8b83c4: ldur            x9, [x4, #7]
    // 0x8b83c8: r3 = Null
    //     0x8b83c8: add             x3, PP, #0xe, lsl #12  ; [pp+0xeb68] Null
    //     0x8b83cc: ldr             x3, [x3, #0xb68]
    // 0x8b83d0: blr             x9
    // 0x8b83d4: ldr             x1, [fp, #0x10]
    // 0x8b83d8: r0 = LoadClassIdInstr(r1)
    //     0x8b83d8: ldur            x0, [x1, #-1]
    //     0x8b83dc: ubfx            x0, x0, #0xc, #0x14
    // 0x8b83e0: r16 = 2
    //     0x8b83e0: movz            x16, #0x2
    // 0x8b83e4: stp             x16, x1, [SP]
    // 0x8b83e8: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b83e8: movz            x17, #0x3037
    //     0x8b83ec: movk            x17, #0x1, lsl #16
    //     0x8b83f0: add             lr, x0, x17
    //     0x8b83f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8b83f8: blr             lr
    // 0x8b83fc: ldur            x1, [fp, #-0x10]
    // 0x8b8400: mov             x3, x0
    // 0x8b8404: r2 = Null
    //     0x8b8404: mov             x2, NULL
    // 0x8b8408: stur            x3, [fp, #-0x20]
    // 0x8b840c: cmp             w1, NULL
    // 0x8b8410: b.eq            #0x8b8434
    // 0x8b8414: LoadField: r4 = r1->field_1b
    //     0x8b8414: ldur            w4, [x1, #0x1b]
    // 0x8b8418: DecompressPointer r4
    //     0x8b8418: add             x4, x4, HEAP, lsl #32
    // 0x8b841c: r8 = Y1
    //     0x8b841c: add             x8, PP, #0xe, lsl #12  ; [pp+0xeb78] TypeParameter: Y1
    //     0x8b8420: ldr             x8, [x8, #0xb78]
    // 0x8b8424: LoadField: r9 = r4->field_7
    //     0x8b8424: ldur            x9, [x4, #7]
    // 0x8b8428: r3 = Null
    //     0x8b8428: add             x3, PP, #0xe, lsl #12  ; [pp+0xeb80] Null
    //     0x8b842c: ldr             x3, [x3, #0xb80]
    // 0x8b8430: blr             x9
    // 0x8b8434: ldr             x1, [fp, #0x10]
    // 0x8b8438: r0 = LoadClassIdInstr(r1)
    //     0x8b8438: ldur            x0, [x1, #-1]
    //     0x8b843c: ubfx            x0, x0, #0xc, #0x14
    // 0x8b8440: r16 = 4
    //     0x8b8440: movz            x16, #0x4
    // 0x8b8444: stp             x16, x1, [SP]
    // 0x8b8448: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b8448: movz            x17, #0x3037
    //     0x8b844c: movk            x17, #0x1, lsl #16
    //     0x8b8450: add             lr, x0, x17
    //     0x8b8454: ldr             lr, [x21, lr, lsl #3]
    //     0x8b8458: blr             lr
    // 0x8b845c: ldur            x1, [fp, #-0x10]
    // 0x8b8460: mov             x3, x0
    // 0x8b8464: r2 = Null
    //     0x8b8464: mov             x2, NULL
    // 0x8b8468: stur            x3, [fp, #-0x28]
    // 0x8b846c: cmp             w1, NULL
    // 0x8b8470: b.eq            #0x8b8494
    // 0x8b8474: LoadField: r4 = r1->field_1f
    //     0x8b8474: ldur            w4, [x1, #0x1f]
    // 0x8b8478: DecompressPointer r4
    //     0x8b8478: add             x4, x4, HEAP, lsl #32
    // 0x8b847c: r8 = Y2
    //     0x8b847c: add             x8, PP, #0xe, lsl #12  ; [pp+0xeb90] TypeParameter: Y2
    //     0x8b8480: ldr             x8, [x8, #0xb90]
    // 0x8b8484: LoadField: r9 = r4->field_7
    //     0x8b8484: ldur            x9, [x4, #7]
    // 0x8b8488: r3 = Null
    //     0x8b8488: add             x3, PP, #0xe, lsl #12  ; [pp+0xeb98] Null
    //     0x8b848c: ldr             x3, [x3, #0xb98]
    // 0x8b8490: blr             x9
    // 0x8b8494: ldr             x1, [fp, #0x10]
    // 0x8b8498: r0 = LoadClassIdInstr(r1)
    //     0x8b8498: ldur            x0, [x1, #-1]
    //     0x8b849c: ubfx            x0, x0, #0xc, #0x14
    // 0x8b84a0: r16 = 6
    //     0x8b84a0: movz            x16, #0x6
    // 0x8b84a4: stp             x16, x1, [SP]
    // 0x8b84a8: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b84a8: movz            x17, #0x3037
    //     0x8b84ac: movk            x17, #0x1, lsl #16
    //     0x8b84b0: add             lr, x0, x17
    //     0x8b84b4: ldr             lr, [x21, lr, lsl #3]
    //     0x8b84b8: blr             lr
    // 0x8b84bc: ldur            x1, [fp, #-0x10]
    // 0x8b84c0: mov             x3, x0
    // 0x8b84c4: r2 = Null
    //     0x8b84c4: mov             x2, NULL
    // 0x8b84c8: stur            x3, [fp, #-0x30]
    // 0x8b84cc: cmp             w1, NULL
    // 0x8b84d0: b.eq            #0x8b84f4
    // 0x8b84d4: LoadField: r4 = r1->field_23
    //     0x8b84d4: ldur            w4, [x1, #0x23]
    // 0x8b84d8: DecompressPointer r4
    //     0x8b84d8: add             x4, x4, HEAP, lsl #32
    // 0x8b84dc: r8 = Y3
    //     0x8b84dc: add             x8, PP, #0xe, lsl #12  ; [pp+0xeba8] TypeParameter: Y3
    //     0x8b84e0: ldr             x8, [x8, #0xba8]
    // 0x8b84e4: LoadField: r9 = r4->field_7
    //     0x8b84e4: ldur            x9, [x4, #7]
    // 0x8b84e8: r3 = Null
    //     0x8b84e8: add             x3, PP, #0xe, lsl #12  ; [pp+0xebb0] Null
    //     0x8b84ec: ldr             x3, [x3, #0xbb0]
    // 0x8b84f0: blr             x9
    // 0x8b84f4: ldr             x0, [fp, #0x10]
    // 0x8b84f8: r1 = LoadClassIdInstr(r0)
    //     0x8b84f8: ldur            x1, [x0, #-1]
    //     0x8b84fc: ubfx            x1, x1, #0xc, #0x14
    // 0x8b8500: r16 = 8
    //     0x8b8500: movz            x16, #0x8
    // 0x8b8504: stp             x16, x0, [SP]
    // 0x8b8508: mov             x0, x1
    // 0x8b850c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b850c: movz            x17, #0x3037
    //     0x8b8510: movk            x17, #0x1, lsl #16
    //     0x8b8514: add             lr, x0, x17
    //     0x8b8518: ldr             lr, [x21, lr, lsl #3]
    //     0x8b851c: blr             lr
    // 0x8b8520: ldur            x1, [fp, #-0x10]
    // 0x8b8524: mov             x3, x0
    // 0x8b8528: r2 = Null
    //     0x8b8528: mov             x2, NULL
    // 0x8b852c: stur            x3, [fp, #-0x10]
    // 0x8b8530: cmp             w1, NULL
    // 0x8b8534: b.eq            #0x8b8558
    // 0x8b8538: LoadField: r4 = r1->field_27
    //     0x8b8538: ldur            w4, [x1, #0x27]
    // 0x8b853c: DecompressPointer r4
    //     0x8b853c: add             x4, x4, HEAP, lsl #32
    // 0x8b8540: r8 = Y4
    //     0x8b8540: add             x8, PP, #0xe, lsl #12  ; [pp+0xebc0] TypeParameter: Y4
    //     0x8b8544: ldr             x8, [x8, #0xbc0]
    // 0x8b8548: LoadField: r9 = r4->field_7
    //     0x8b8548: ldur            x9, [x4, #7]
    // 0x8b854c: r3 = Null
    //     0x8b854c: add             x3, PP, #0xe, lsl #12  ; [pp+0xebc8] Null
    //     0x8b8550: ldr             x3, [x3, #0xbc8]
    // 0x8b8554: blr             x9
    // 0x8b8558: ldur            x16, [fp, #-8]
    // 0x8b855c: ldur            lr, [fp, #-0x18]
    // 0x8b8560: stp             lr, x16, [SP, #0x20]
    // 0x8b8564: ldur            x16, [fp, #-0x20]
    // 0x8b8568: ldur            lr, [fp, #-0x28]
    // 0x8b856c: stp             lr, x16, [SP, #0x10]
    // 0x8b8570: ldur            x16, [fp, #-0x30]
    // 0x8b8574: ldur            lr, [fp, #-0x10]
    // 0x8b8578: stp             lr, x16, [SP]
    // 0x8b857c: ldur            x0, [fp, #-8]
    // 0x8b8580: ClosureCall
    //     0x8b8580: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x6, 0x6, 0x6, Null]
    //     0x8b8584: ldur            x2, [x0, #0x1f]
    //     0x8b8588: blr             x2
    // 0x8b858c: LeaveFrame
    //     0x8b858c: mov             SP, fp
    //     0x8b8590: ldp             fp, lr, [SP], #0x10
    // 0x8b8594: ret
    //     0x8b8594: ret             
    // 0x8b8598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b8598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b859c: b               #0x8b8360
  }
}
