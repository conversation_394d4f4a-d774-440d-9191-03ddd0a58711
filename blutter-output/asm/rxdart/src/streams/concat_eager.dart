// lib: , url: package:rxdart/src/streams/concat_eager.dart

// class id: 1051085, size: 0x8
class :: {
}

// class id: 6671, size: 0x10, field offset: 0x10
class ConcatEagerStream<X0> extends StreamView<X0> {

  _ ConcatEagerStream(/* No info */) {
    // ** addr: 0xe5e40c, size: 0x80
    // 0xe5e40c: EnterFrame
    //     0xe5e40c: stp             fp, lr, [SP, #-0x10]!
    //     0xe5e410: mov             fp, SP
    // 0xe5e414: AllocStack(0x20)
    //     0xe5e414: sub             SP, SP, #0x20
    // 0xe5e418: SetupParameters(ConcatEagerStream<X0> this /* r1 => r1, fp-0x8 */)
    //     0xe5e418: stur            x1, [fp, #-8]
    // 0xe5e41c: CheckStackOverflow
    //     0xe5e41c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5e420: cmp             SP, x16
    //     0xe5e424: b.ls            #0xe5e484
    // 0xe5e428: LoadField: r0 = r1->field_7
    //     0xe5e428: ldur            w0, [x1, #7]
    // 0xe5e42c: DecompressPointer r0
    //     0xe5e42c: add             x0, x0, HEAP, lsl #32
    // 0xe5e430: stp             x2, x0, [SP]
    // 0xe5e434: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe5e434: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe5e438: r0 = _buildController()
    //     0xe5e438: bl              #0xe5e48c  ; [package:rxdart/src/streams/concat_eager.dart] ConcatEagerStream::_buildController
    // 0xe5e43c: stur            x0, [fp, #-0x10]
    // 0xe5e440: LoadField: r1 = r0->field_7
    //     0xe5e440: ldur            w1, [x0, #7]
    // 0xe5e444: DecompressPointer r1
    //     0xe5e444: add             x1, x1, HEAP, lsl #32
    // 0xe5e448: r0 = _ControllerStream()
    //     0xe5e448: bl              #0x696858  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0xe5e44c: ldur            x1, [fp, #-0x10]
    // 0xe5e450: StoreField: r0->field_b = r1
    //     0xe5e450: stur            w1, [x0, #0xb]
    // 0xe5e454: ldur            x1, [fp, #-8]
    // 0xe5e458: StoreField: r1->field_b = r0
    //     0xe5e458: stur            w0, [x1, #0xb]
    //     0xe5e45c: ldurb           w16, [x1, #-1]
    //     0xe5e460: ldurb           w17, [x0, #-1]
    //     0xe5e464: and             x16, x17, x16, lsr #2
    //     0xe5e468: tst             x16, HEAP, lsr #32
    //     0xe5e46c: b.eq            #0xe5e474
    //     0xe5e470: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe5e474: r0 = Null
    //     0xe5e474: mov             x0, NULL
    // 0xe5e478: LeaveFrame
    //     0xe5e478: mov             SP, fp
    //     0xe5e47c: ldp             fp, lr, [SP], #0x10
    // 0xe5e480: ret
    //     0xe5e480: ret             
    // 0xe5e484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5e484: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5e488: b               #0xe5e428
  }
  static StreamController<Y0> _buildController<Y0>(Iterable<Stream<Y0>>) {
    // ** addr: 0xe5e48c, size: 0x198
    // 0xe5e48c: EnterFrame
    //     0xe5e48c: stp             fp, lr, [SP, #-0x10]!
    //     0xe5e490: mov             fp, SP
    // 0xe5e494: AllocStack(0x20)
    //     0xe5e494: sub             SP, SP, #0x20
    // 0xe5e498: SetupParameters()
    //     0xe5e498: ldur            w0, [x4, #0xf]
    //     0xe5e49c: cbnz            w0, #0xe5e4a8
    //     0xe5e4a0: mov             x1, NULL
    //     0xe5e4a4: b               #0xe5e4b4
    //     0xe5e4a8: ldur            w0, [x4, #0x17]
    //     0xe5e4ac: add             x1, fp, w0, sxtw #2
    //     0xe5e4b0: ldr             x1, [x1, #0x10]
    //     0xe5e4b4: ldr             x0, [fp, #0x10]
    //     0xe5e4b8: stur            x1, [fp, #-8]
    // 0xe5e4bc: CheckStackOverflow
    //     0xe5e4bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5e4c0: cmp             SP, x16
    //     0xe5e4c4: b.ls            #0xe5e61c
    // 0xe5e4c8: r1 = 4
    //     0xe5e4c8: movz            x1, #0x4
    // 0xe5e4cc: r0 = AllocateContext()
    //     0xe5e4cc: bl              #0xec126c  ; AllocateContextStub
    // 0xe5e4d0: mov             x2, x0
    // 0xe5e4d4: ldr             x0, [fp, #0x10]
    // 0xe5e4d8: stur            x2, [fp, #-0x10]
    // 0xe5e4dc: StoreField: r2->field_f = r0
    //     0xe5e4dc: stur            w0, [x2, #0xf]
    // 0xe5e4e0: r16 = true
    //     0xe5e4e0: add             x16, NULL, #0x20  ; true
    // 0xe5e4e4: str             x16, [SP]
    // 0xe5e4e8: ldur            x1, [fp, #-8]
    // 0xe5e4ec: r4 = const [0, 0x2, 0x1, 0x1, sync, 0x1, null]
    //     0xe5e4ec: add             x4, PP, #0xc, lsl #12  ; [pp+0xcfb0] List(7) [0, 0x2, 0x1, 0x1, "sync", 0x1, Null]
    //     0xe5e4f0: ldr             x4, [x4, #0xfb0]
    // 0xe5e4f4: r0 = StreamController()
    //     0xe5e4f4: bl              #0x696864  ; [dart:async] StreamController::StreamController
    // 0xe5e4f8: mov             x4, x0
    // 0xe5e4fc: ldur            x3, [fp, #-0x10]
    // 0xe5e500: stur            x4, [fp, #-0x18]
    // 0xe5e504: StoreField: r3->field_13 = r0
    //     0xe5e504: stur            w0, [x3, #0x13]
    //     0xe5e508: ldurb           w16, [x3, #-1]
    //     0xe5e50c: ldurb           w17, [x0, #-1]
    //     0xe5e510: and             x16, x17, x16, lsr #2
    //     0xe5e514: tst             x16, HEAP, lsr #32
    //     0xe5e518: b.eq            #0xe5e520
    //     0xe5e51c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe5e520: r0 = Sentinel
    //     0xe5e520: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe5e524: ArrayStore: r3[0] = r0  ; List_4
    //     0xe5e524: stur            w0, [x3, #0x17]
    // 0xe5e528: StoreField: r3->field_1b = rNULL
    //     0xe5e528: stur            NULL, [x3, #0x1b]
    // 0xe5e52c: mov             x2, x3
    // 0xe5e530: r1 = Function '<anonymous closure>': static.
    //     0xe5e530: add             x1, PP, #0x38, lsl #12  ; [pp+0x38660] AnonymousClosure: static (0xe5e76c), in [package:rxdart/src/streams/concat_eager.dart] ConcatEagerStream::_buildController (0xe5e48c)
    //     0xe5e534: ldr             x1, [x1, #0x660]
    // 0xe5e538: r0 = AllocateClosure()
    //     0xe5e538: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5e53c: ldur            x3, [fp, #-8]
    // 0xe5e540: StoreField: r0->field_b = r3
    //     0xe5e540: stur            w3, [x0, #0xb]
    // 0xe5e544: ldur            x4, [fp, #-0x18]
    // 0xe5e548: StoreField: r4->field_1b = r0
    //     0xe5e548: stur            w0, [x4, #0x1b]
    //     0xe5e54c: ldurb           w16, [x4, #-1]
    //     0xe5e550: ldurb           w17, [x0, #-1]
    //     0xe5e554: and             x16, x17, x16, lsr #2
    //     0xe5e558: tst             x16, HEAP, lsr #32
    //     0xe5e55c: b.eq            #0xe5e564
    //     0xe5e560: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe5e564: ldur            x2, [fp, #-0x10]
    // 0xe5e568: r1 = Function '<anonymous closure>': static.
    //     0xe5e568: add             x1, PP, #0x38, lsl #12  ; [pp+0x38668] AnonymousClosure: static (0xe5e700), in [package:rxdart/src/streams/concat_eager.dart] ConcatEagerStream::_buildController (0xe5e48c)
    //     0xe5e56c: ldr             x1, [x1, #0x668]
    // 0xe5e570: r0 = AllocateClosure()
    //     0xe5e570: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5e574: ldur            x3, [fp, #-8]
    // 0xe5e578: StoreField: r0->field_b = r3
    //     0xe5e578: stur            w3, [x0, #0xb]
    // 0xe5e57c: ldur            x4, [fp, #-0x18]
    // 0xe5e580: StoreField: r4->field_1f = r0
    //     0xe5e580: stur            w0, [x4, #0x1f]
    //     0xe5e584: ldurb           w16, [x4, #-1]
    //     0xe5e588: ldurb           w17, [x0, #-1]
    //     0xe5e58c: and             x16, x17, x16, lsr #2
    //     0xe5e590: tst             x16, HEAP, lsr #32
    //     0xe5e594: b.eq            #0xe5e59c
    //     0xe5e598: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe5e59c: ldur            x2, [fp, #-0x10]
    // 0xe5e5a0: r1 = Function '<anonymous closure>': static.
    //     0xe5e5a0: add             x1, PP, #0x38, lsl #12  ; [pp+0x38670] AnonymousClosure: static (0xe5e698), in [package:rxdart/src/streams/concat_eager.dart] ConcatEagerStream::_buildController (0xe5e48c)
    //     0xe5e5a4: ldr             x1, [x1, #0x670]
    // 0xe5e5a8: r0 = AllocateClosure()
    //     0xe5e5a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5e5ac: ldur            x3, [fp, #-8]
    // 0xe5e5b0: StoreField: r0->field_b = r3
    //     0xe5e5b0: stur            w3, [x0, #0xb]
    // 0xe5e5b4: ldur            x4, [fp, #-0x18]
    // 0xe5e5b8: StoreField: r4->field_23 = r0
    //     0xe5e5b8: stur            w0, [x4, #0x23]
    //     0xe5e5bc: ldurb           w16, [x4, #-1]
    //     0xe5e5c0: ldurb           w17, [x0, #-1]
    //     0xe5e5c4: and             x16, x17, x16, lsr #2
    //     0xe5e5c8: tst             x16, HEAP, lsr #32
    //     0xe5e5cc: b.eq            #0xe5e5d4
    //     0xe5e5d0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xe5e5d4: ldur            x2, [fp, #-0x10]
    // 0xe5e5d8: r1 = Function '<anonymous closure>': static.
    //     0xe5e5d8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38678] AnonymousClosure: static (0xe5e624), in [package:rxdart/src/streams/concat_eager.dart] ConcatEagerStream::_buildController (0xe5e48c)
    //     0xe5e5dc: ldr             x1, [x1, #0x678]
    // 0xe5e5e0: r0 = AllocateClosure()
    //     0xe5e5e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5e5e4: ldur            x1, [fp, #-8]
    // 0xe5e5e8: StoreField: r0->field_b = r1
    //     0xe5e5e8: stur            w1, [x0, #0xb]
    // 0xe5e5ec: ldur            x1, [fp, #-0x18]
    // 0xe5e5f0: StoreField: r1->field_27 = r0
    //     0xe5e5f0: stur            w0, [x1, #0x27]
    //     0xe5e5f4: ldurb           w16, [x1, #-1]
    //     0xe5e5f8: ldurb           w17, [x0, #-1]
    //     0xe5e5fc: and             x16, x17, x16, lsr #2
    //     0xe5e600: tst             x16, HEAP, lsr #32
    //     0xe5e604: b.eq            #0xe5e60c
    //     0xe5e608: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe5e60c: mov             x0, x1
    // 0xe5e610: LeaveFrame
    //     0xe5e610: mov             SP, fp
    //     0xe5e614: ldp             fp, lr, [SP], #0x10
    // 0xe5e618: ret
    //     0xe5e618: ret             
    // 0xe5e61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5e61c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5e620: b               #0xe5e4c8
  }
  [closure] static Future<void>? <anonymous closure>(dynamic) {
    // ** addr: 0xe5e624, size: 0x74
    // 0xe5e624: EnterFrame
    //     0xe5e624: stp             fp, lr, [SP, #-0x10]!
    //     0xe5e628: mov             fp, SP
    // 0xe5e62c: AllocStack(0x10)
    //     0xe5e62c: sub             SP, SP, #0x10
    // 0xe5e630: SetupParameters()
    //     0xe5e630: ldr             x0, [fp, #0x10]
    //     0xe5e634: ldur            w1, [x0, #0x17]
    //     0xe5e638: add             x1, x1, HEAP, lsl #32
    //     0xe5e63c: stur            x1, [fp, #-8]
    // 0xe5e640: CheckStackOverflow
    //     0xe5e640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5e644: cmp             SP, x16
    //     0xe5e648: b.ls            #0xe5e690
    // 0xe5e64c: StoreField: r1->field_1b = rNULL
    //     0xe5e64c: stur            NULL, [x1, #0x1b]
    // 0xe5e650: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xe5e650: ldur            w0, [x1, #0x17]
    // 0xe5e654: DecompressPointer r0
    //     0xe5e654: add             x0, x0, HEAP, lsl #32
    // 0xe5e658: r16 = Sentinel
    //     0xe5e658: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe5e65c: cmp             w0, w16
    // 0xe5e660: b.ne            #0xe5e674
    // 0xe5e664: r16 = "subscriptions"
    //     0xe5e664: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0xe5e668: ldr             x16, [x16, #0xa78]
    // 0xe5e66c: str             x16, [SP]
    // 0xe5e670: r0 = _throwLocalNotInitialized()
    //     0xe5e670: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xe5e674: ldur            x0, [fp, #-8]
    // 0xe5e678: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe5e678: ldur            w1, [x0, #0x17]
    // 0xe5e67c: DecompressPointer r1
    //     0xe5e67c: add             x1, x1, HEAP, lsl #32
    // 0xe5e680: r0 = StreamSubscriptionsIterableExtension.cancelAll()
    //     0xe5e680: bl              #0x8b71ec  ; [package:rxdart/src/utils/subscription.dart] ::StreamSubscriptionsIterableExtension.cancelAll
    // 0xe5e684: LeaveFrame
    //     0xe5e684: mov             SP, fp
    //     0xe5e688: ldp             fp, lr, [SP], #0x10
    // 0xe5e68c: ret
    //     0xe5e68c: ret             
    // 0xe5e690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5e690: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5e694: b               #0xe5e64c
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xe5e698, size: 0x68
    // 0xe5e698: EnterFrame
    //     0xe5e698: stp             fp, lr, [SP, #-0x10]!
    //     0xe5e69c: mov             fp, SP
    // 0xe5e6a0: ldr             x0, [fp, #0x10]
    // 0xe5e6a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe5e6a4: ldur            w1, [x0, #0x17]
    // 0xe5e6a8: DecompressPointer r1
    //     0xe5e6a8: add             x1, x1, HEAP, lsl #32
    // 0xe5e6ac: CheckStackOverflow
    //     0xe5e6ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5e6b0: cmp             SP, x16
    //     0xe5e6b4: b.ls            #0xe5e6f8
    // 0xe5e6b8: LoadField: r0 = r1->field_1b
    //     0xe5e6b8: ldur            w0, [x1, #0x1b]
    // 0xe5e6bc: DecompressPointer r0
    //     0xe5e6bc: add             x0, x0, HEAP, lsl #32
    // 0xe5e6c0: cmp             w0, NULL
    // 0xe5e6c4: b.eq            #0xe5e6e8
    // 0xe5e6c8: r1 = LoadClassIdInstr(r0)
    //     0xe5e6c8: ldur            x1, [x0, #-1]
    //     0xe5e6cc: ubfx            x1, x1, #0xc, #0x14
    // 0xe5e6d0: mov             x16, x0
    // 0xe5e6d4: mov             x0, x1
    // 0xe5e6d8: mov             x1, x16
    // 0xe5e6dc: r0 = GDT[cid_x0 + 0x3e9]()
    //     0xe5e6dc: add             lr, x0, #0x3e9
    //     0xe5e6e0: ldr             lr, [x21, lr, lsl #3]
    //     0xe5e6e4: blr             lr
    // 0xe5e6e8: r0 = Null
    //     0xe5e6e8: mov             x0, NULL
    // 0xe5e6ec: LeaveFrame
    //     0xe5e6ec: mov             SP, fp
    //     0xe5e6f0: ldp             fp, lr, [SP], #0x10
    // 0xe5e6f4: ret
    //     0xe5e6f4: ret             
    // 0xe5e6f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5e6f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5e6fc: b               #0xe5e6b8
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xe5e700, size: 0x6c
    // 0xe5e700: EnterFrame
    //     0xe5e700: stp             fp, lr, [SP, #-0x10]!
    //     0xe5e704: mov             fp, SP
    // 0xe5e708: ldr             x0, [fp, #0x10]
    // 0xe5e70c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe5e70c: ldur            w1, [x0, #0x17]
    // 0xe5e710: DecompressPointer r1
    //     0xe5e710: add             x1, x1, HEAP, lsl #32
    // 0xe5e714: CheckStackOverflow
    //     0xe5e714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5e718: cmp             SP, x16
    //     0xe5e71c: b.ls            #0xe5e764
    // 0xe5e720: LoadField: r0 = r1->field_1b
    //     0xe5e720: ldur            w0, [x1, #0x1b]
    // 0xe5e724: DecompressPointer r0
    //     0xe5e724: add             x0, x0, HEAP, lsl #32
    // 0xe5e728: cmp             w0, NULL
    // 0xe5e72c: b.eq            #0xe5e754
    // 0xe5e730: r1 = LoadClassIdInstr(r0)
    //     0xe5e730: ldur            x1, [x0, #-1]
    //     0xe5e734: ubfx            x1, x1, #0xc, #0x14
    // 0xe5e738: mov             x16, x0
    // 0xe5e73c: mov             x0, x1
    // 0xe5e740: mov             x1, x16
    // 0xe5e744: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe5e744: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe5e748: r0 = GDT[cid_x0 + 0x40b]()
    //     0xe5e748: add             lr, x0, #0x40b
    //     0xe5e74c: ldr             lr, [x21, lr, lsl #3]
    //     0xe5e750: blr             lr
    // 0xe5e754: r0 = Null
    //     0xe5e754: mov             x0, NULL
    // 0xe5e758: LeaveFrame
    //     0xe5e758: mov             SP, fp
    //     0xe5e75c: ldp             fp, lr, [SP], #0x10
    // 0xe5e760: ret
    //     0xe5e760: ret             
    // 0xe5e764: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5e764: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5e768: b               #0xe5e720
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xe5e76c, size: 0x1fc
    // 0xe5e76c: EnterFrame
    //     0xe5e76c: stp             fp, lr, [SP, #-0x10]!
    //     0xe5e770: mov             fp, SP
    // 0xe5e774: AllocStack(0x30)
    //     0xe5e774: sub             SP, SP, #0x30
    // 0xe5e778: SetupParameters()
    //     0xe5e778: ldr             x0, [fp, #0x10]
    //     0xe5e77c: ldur            w1, [x0, #0x17]
    //     0xe5e780: add             x1, x1, HEAP, lsl #32
    //     0xe5e784: stur            x1, [fp, #-0x10]
    // 0xe5e788: CheckStackOverflow
    //     0xe5e788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5e78c: cmp             SP, x16
    //     0xe5e790: b.ls            #0xe5e95c
    // 0xe5e794: LoadField: r2 = r0->field_b
    //     0xe5e794: ldur            w2, [x0, #0xb]
    // 0xe5e798: DecompressPointer r2
    //     0xe5e798: add             x2, x2, HEAP, lsl #32
    // 0xe5e79c: stur            x2, [fp, #-8]
    // 0xe5e7a0: r1 = 2
    //     0xe5e7a0: movz            x1, #0x2
    // 0xe5e7a4: r0 = AllocateContext()
    //     0xe5e7a4: bl              #0xec126c  ; AllocateContextStub
    // 0xe5e7a8: mov             x3, x0
    // 0xe5e7ac: ldur            x0, [fp, #-0x10]
    // 0xe5e7b0: stur            x3, [fp, #-0x18]
    // 0xe5e7b4: StoreField: r3->field_b = r0
    //     0xe5e7b4: stur            w0, [x3, #0xb]
    // 0xe5e7b8: r1 = <Completer<void?>>
    //     0xe5e7b8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38680] TypeArguments: <Completer<void?>>
    //     0xe5e7bc: ldr             x1, [x1, #0x680]
    // 0xe5e7c0: r2 = 0
    //     0xe5e7c0: movz            x2, #0
    // 0xe5e7c4: r0 = _GrowableList()
    //     0xe5e7c4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe5e7c8: ldur            x3, [fp, #-0x18]
    // 0xe5e7cc: StoreField: r3->field_f = r0
    //     0xe5e7cc: stur            w0, [x3, #0xf]
    //     0xe5e7d0: ldurb           w16, [x3, #-1]
    //     0xe5e7d4: ldurb           w17, [x0, #-1]
    //     0xe5e7d8: and             x16, x17, x16, lsr #2
    //     0xe5e7dc: tst             x16, HEAP, lsr #32
    //     0xe5e7e0: b.eq            #0xe5e7e8
    //     0xe5e7e4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe5e7e8: mov             x2, x3
    // 0xe5e7ec: r1 = Function 'onDone': static.
    //     0xe5e7ec: add             x1, PP, #0x38, lsl #12  ; [pp+0x38688] AnonymousClosure: static (0xe5edec), in [package:rxdart/src/streams/concat_eager.dart] ConcatEagerStream::_buildController (0xe5e48c)
    //     0xe5e7f0: ldr             x1, [x1, #0x688]
    // 0xe5e7f4: r0 = AllocateClosure()
    //     0xe5e7f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5e7f8: ldur            x3, [fp, #-8]
    // 0xe5e7fc: StoreField: r0->field_b = r3
    //     0xe5e7fc: stur            w3, [x0, #0xb]
    // 0xe5e800: ldur            x2, [fp, #-0x18]
    // 0xe5e804: StoreField: r2->field_13 = r0
    //     0xe5e804: stur            w0, [x2, #0x13]
    //     0xe5e808: ldurb           w16, [x2, #-1]
    //     0xe5e80c: ldurb           w17, [x0, #-1]
    //     0xe5e810: and             x16, x17, x16, lsr #2
    //     0xe5e814: tst             x16, HEAP, lsr #32
    //     0xe5e818: b.eq            #0xe5e820
    //     0xe5e81c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe5e820: r1 = Function 'createSubscription': static.
    //     0xe5e820: add             x1, PP, #0x38, lsl #12  ; [pp+0x38690] AnonymousClosure: static (0xe5e968), in [package:rxdart/src/streams/concat_eager.dart] ConcatEagerStream::_buildController (0xe5e48c)
    //     0xe5e824: ldr             x1, [x1, #0x690]
    // 0xe5e828: r0 = AllocateClosure()
    //     0xe5e828: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5e82c: ldur            x1, [fp, #-8]
    // 0xe5e830: stur            x0, [fp, #-0x18]
    // 0xe5e834: StoreField: r0->field_b = r1
    //     0xe5e834: stur            w1, [x0, #0xb]
    // 0xe5e838: r2 = Null
    //     0xe5e838: mov             x2, NULL
    // 0xe5e83c: r3 = <Stream<Y0>, StreamSubscription<Y0>>
    //     0xe5e83c: add             x3, PP, #0x38, lsl #12  ; [pp+0x38698] TypeArguments: <Stream<Y0>, StreamSubscription<Y0>>
    //     0xe5e840: ldr             x3, [x3, #0x698]
    // 0xe5e844: r30 = InstantiateTypeArgumentsStub
    //     0xe5e844: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe5e848: LoadField: r30 = r30->field_7
    //     0xe5e848: ldur            lr, [lr, #7]
    // 0xe5e84c: blr             lr
    // 0xe5e850: mov             x1, x0
    // 0xe5e854: ldur            x0, [fp, #-0x10]
    // 0xe5e858: LoadField: r2 = r0->field_f
    //     0xe5e858: ldur            w2, [x0, #0xf]
    // 0xe5e85c: DecompressPointer r2
    //     0xe5e85c: add             x2, x2, HEAP, lsl #32
    // 0xe5e860: stp             x2, x1, [SP, #8]
    // 0xe5e864: ldur            x16, [fp, #-0x18]
    // 0xe5e868: str             x16, [SP]
    // 0xe5e86c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xe5e86c: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xe5e870: r0 = MapNotNullIterableExtension.mapIndexed()
    //     0xe5e870: bl              #0x8b78e8  ; [package:rxdart/src/utils/collection_extensions.dart] ::MapNotNullIterableExtension.mapIndexed
    // 0xe5e874: LoadField: r1 = r0->field_7
    //     0xe5e874: ldur            w1, [x0, #7]
    // 0xe5e878: DecompressPointer r1
    //     0xe5e878: add             x1, x1, HEAP, lsl #32
    // 0xe5e87c: mov             x2, x0
    // 0xe5e880: r0 = _List.of()
    //     0xe5e880: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0xe5e884: mov             x2, x0
    // 0xe5e888: ldur            x1, [fp, #-0x10]
    // 0xe5e88c: ArrayStore: r1[0] = r0  ; List_4
    //     0xe5e88c: stur            w0, [x1, #0x17]
    //     0xe5e890: ldurb           w16, [x1, #-1]
    //     0xe5e894: ldurb           w17, [x0, #-1]
    //     0xe5e898: and             x16, x17, x16, lsr #2
    //     0xe5e89c: tst             x16, HEAP, lsr #32
    //     0xe5e8a0: b.eq            #0xe5e8a8
    //     0xe5e8a4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xe5e8a8: str             x2, [SP]
    // 0xe5e8ac: r0 = length()
    //     0xe5e8ac: bl              #0xa96414  ; [dart:core] _GrowableList::length
    // 0xe5e8b0: cbnz            w0, #0xe5e8c8
    // 0xe5e8b4: ldur            x0, [fp, #-0x10]
    // 0xe5e8b8: LoadField: r1 = r0->field_13
    //     0xe5e8b8: ldur            w1, [x0, #0x13]
    // 0xe5e8bc: DecompressPointer r1
    //     0xe5e8bc: add             x1, x1, HEAP, lsl #32
    // 0xe5e8c0: r0 = close()
    //     0xe5e8c0: bl              #0xc67e4c  ; [dart:async] _StreamController::close
    // 0xe5e8c4: b               #0xe5e940
    // 0xe5e8c8: ldur            x0, [fp, #-0x10]
    // 0xe5e8cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe5e8cc: ldur            w1, [x0, #0x17]
    // 0xe5e8d0: DecompressPointer r1
    //     0xe5e8d0: add             x1, x1, HEAP, lsl #32
    // 0xe5e8d4: r16 = Sentinel
    //     0xe5e8d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe5e8d8: cmp             w1, w16
    // 0xe5e8dc: b.ne            #0xe5e8f0
    // 0xe5e8e0: r16 = "subscriptions"
    //     0xe5e8e0: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0xe5e8e4: ldr             x16, [x16, #0xa78]
    // 0xe5e8e8: str             x16, [SP]
    // 0xe5e8ec: r0 = _throwLocalNotInitialized()
    //     0xe5e8ec: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xe5e8f0: ldur            x2, [fp, #-0x10]
    // 0xe5e8f4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe5e8f4: ldur            w3, [x2, #0x17]
    // 0xe5e8f8: DecompressPointer r3
    //     0xe5e8f8: add             x3, x3, HEAP, lsl #32
    // 0xe5e8fc: LoadField: r0 = r3->field_b
    //     0xe5e8fc: ldur            w0, [x3, #0xb]
    // 0xe5e900: r1 = LoadInt32Instr(r0)
    //     0xe5e900: sbfx            x1, x0, #1, #0x1f
    // 0xe5e904: cmp             x1, #0
    // 0xe5e908: b.le            #0xe5e950
    // 0xe5e90c: mov             x0, x1
    // 0xe5e910: r1 = 0
    //     0xe5e910: movz            x1, #0
    // 0xe5e914: cmp             x1, x0
    // 0xe5e918: b.hs            #0xe5e964
    // 0xe5e91c: LoadField: r0 = r3->field_f
    //     0xe5e91c: ldur            w0, [x3, #0xf]
    // 0xe5e920: DecompressPointer r0
    //     0xe5e920: add             x0, x0, HEAP, lsl #32
    // 0xe5e924: StoreField: r2->field_1b = r0
    //     0xe5e924: stur            w0, [x2, #0x1b]
    //     0xe5e928: ldurb           w16, [x2, #-1]
    //     0xe5e92c: ldurb           w17, [x0, #-1]
    //     0xe5e930: and             x16, x17, x16, lsr #2
    //     0xe5e934: tst             x16, HEAP, lsr #32
    //     0xe5e938: b.eq            #0xe5e940
    //     0xe5e93c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe5e940: r0 = Null
    //     0xe5e940: mov             x0, NULL
    // 0xe5e944: LeaveFrame
    //     0xe5e944: mov             SP, fp
    //     0xe5e948: ldp             fp, lr, [SP], #0x10
    // 0xe5e94c: ret
    //     0xe5e94c: ret             
    // 0xe5e950: r0 = noElement()
    //     0xe5e950: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xe5e954: r0 = Throw()
    //     0xe5e954: bl              #0xec04b8  ; ThrowStub
    // 0xe5e958: brk             #0
    // 0xe5e95c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5e95c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5e960: b               #0xe5e794
    // 0xe5e964: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe5e964: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static StreamSubscription<Y0> createSubscription(dynamic, int, Stream<Y0>) {
    // ** addr: 0xe5e968, size: 0x264
    // 0xe5e968: EnterFrame
    //     0xe5e968: stp             fp, lr, [SP, #-0x10]!
    //     0xe5e96c: mov             fp, SP
    // 0xe5e970: AllocStack(0x40)
    //     0xe5e970: sub             SP, SP, #0x40
    // 0xe5e974: SetupParameters()
    //     0xe5e974: ldr             x0, [fp, #0x20]
    //     0xe5e978: ldur            w3, [x0, #0x17]
    //     0xe5e97c: add             x3, x3, HEAP, lsl #32
    //     0xe5e980: stur            x3, [fp, #-0x18]
    // 0xe5e984: CheckStackOverflow
    //     0xe5e984: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5e988: cmp             SP, x16
    //     0xe5e98c: b.ls            #0xe5ebc4
    // 0xe5e990: LoadField: r4 = r0->field_b
    //     0xe5e990: ldur            w4, [x0, #0xb]
    // 0xe5e994: DecompressPointer r4
    //     0xe5e994: add             x4, x4, HEAP, lsl #32
    // 0xe5e998: stur            x4, [fp, #-0x10]
    // 0xe5e99c: LoadField: r0 = r3->field_b
    //     0xe5e99c: ldur            w0, [x3, #0xb]
    // 0xe5e9a0: DecompressPointer r0
    //     0xe5e9a0: add             x0, x0, HEAP, lsl #32
    // 0xe5e9a4: LoadField: r5 = r0->field_13
    //     0xe5e9a4: ldur            w5, [x0, #0x13]
    // 0xe5e9a8: DecompressPointer r5
    //     0xe5e9a8: add             x5, x5, HEAP, lsl #32
    // 0xe5e9ac: mov             x2, x5
    // 0xe5e9b0: stur            x5, [fp, #-8]
    // 0xe5e9b4: r1 = Function 'add':.
    //     0xe5e9b4: add             x1, PP, #0x10, lsl #12  ; [pp+0x10fa0] AnonymousClosure: (0x68a5b8), in [dart:async] _StreamController::add (0xccb4dc)
    //     0xe5e9b8: ldr             x1, [x1, #0xfa0]
    // 0xe5e9bc: r0 = AllocateClosure()
    //     0xe5e9bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5e9c0: ldur            x1, [fp, #-0x10]
    // 0xe5e9c4: mov             x3, x0
    // 0xe5e9c8: r2 = Null
    //     0xe5e9c8: mov             x2, NULL
    // 0xe5e9cc: stur            x3, [fp, #-0x10]
    // 0xe5e9d0: r8 = (dynamic this, Y0) => void?
    //     0xe5e9d0: add             x8, PP, #0x38, lsl #12  ; [pp+0x386a0] FunctionType: (dynamic this, Y0) => void?
    //     0xe5e9d4: ldr             x8, [x8, #0x6a0]
    // 0xe5e9d8: LoadField: r9 = r8->field_7
    //     0xe5e9d8: ldur            x9, [x8, #7]
    // 0xe5e9dc: r3 = Null
    //     0xe5e9dc: add             x3, PP, #0x38, lsl #12  ; [pp+0x386a8] Null
    //     0xe5e9e0: ldr             x3, [x3, #0x6a8]
    // 0xe5e9e4: blr             x9
    // 0xe5e9e8: ldur            x0, [fp, #-0x18]
    // 0xe5e9ec: LoadField: r1 = r0->field_13
    //     0xe5e9ec: ldur            w1, [x0, #0x13]
    // 0xe5e9f0: DecompressPointer r1
    //     0xe5e9f0: add             x1, x1, HEAP, lsl #32
    // 0xe5e9f4: stur            x1, [fp, #-0x28]
    // 0xe5e9f8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xe5e9f8: ldur            w2, [x1, #0x17]
    // 0xe5e9fc: DecompressPointer r2
    //     0xe5e9fc: add             x2, x2, HEAP, lsl #32
    // 0xe5ea00: stur            x2, [fp, #-0x20]
    // 0xe5ea04: r1 = 1
    //     0xe5ea04: movz            x1, #0x1
    // 0xe5ea08: r0 = AllocateContext()
    //     0xe5ea08: bl              #0xec126c  ; AllocateContextStub
    // 0xe5ea0c: mov             x1, x0
    // 0xe5ea10: ldur            x0, [fp, #-0x20]
    // 0xe5ea14: StoreField: r1->field_b = r0
    //     0xe5ea14: stur            w0, [x1, #0xb]
    // 0xe5ea18: ldr             x0, [fp, #0x18]
    // 0xe5ea1c: StoreField: r1->field_f = r0
    //     0xe5ea1c: stur            w0, [x1, #0xf]
    // 0xe5ea20: ldur            x2, [fp, #-0x28]
    // 0xe5ea24: LoadField: r3 = r2->field_b
    //     0xe5ea24: ldur            w3, [x2, #0xb]
    // 0xe5ea28: DecompressPointer r3
    //     0xe5ea28: add             x3, x3, HEAP, lsl #32
    // 0xe5ea2c: mov             x2, x1
    // 0xe5ea30: stur            x3, [fp, #-0x20]
    // 0xe5ea34: r1 = Function '<anonymous closure>': static.
    //     0xe5ea34: add             x1, PP, #0x38, lsl #12  ; [pp+0x386b8] AnonymousClosure: static (0xe5ebcc), in [package:rxdart/src/streams/concat_eager.dart] ConcatEagerStream::_buildController (0xe5e48c)
    //     0xe5ea38: ldr             x1, [x1, #0x6b8]
    // 0xe5ea3c: r0 = AllocateClosure()
    //     0xe5ea3c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5ea40: mov             x3, x0
    // 0xe5ea44: ldur            x0, [fp, #-0x20]
    // 0xe5ea48: stur            x3, [fp, #-0x28]
    // 0xe5ea4c: StoreField: r3->field_b = r0
    //     0xe5ea4c: stur            w0, [x3, #0xb]
    // 0xe5ea50: ldur            x2, [fp, #-8]
    // 0xe5ea54: r1 = Function 'addError':.
    //     0xe5ea54: add             x1, PP, #0xe, lsl #12  ; [pp+0xeae0] AnonymousClosure: (0x7216b4), in [dart:async] _StreamController::addError (0xcf039c)
    //     0xe5ea58: ldr             x1, [x1, #0xae0]
    // 0xe5ea5c: r0 = AllocateClosure()
    //     0xe5ea5c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5ea60: ldr             x1, [fp, #0x10]
    // 0xe5ea64: r2 = LoadClassIdInstr(r1)
    //     0xe5ea64: ldur            x2, [x1, #-1]
    //     0xe5ea68: ubfx            x2, x2, #0xc, #0x14
    // 0xe5ea6c: ldur            x16, [fp, #-0x28]
    // 0xe5ea70: stp             x16, x0, [SP]
    // 0xe5ea74: mov             x0, x2
    // 0xe5ea78: ldur            x2, [fp, #-0x10]
    // 0xe5ea7c: r4 = const [0, 0x4, 0x2, 0x2, onDone, 0x3, onError, 0x2, null]
    //     0xe5ea7c: add             x4, PP, #0xc, lsl #12  ; [pp+0xc168] List(9) [0, 0x4, 0x2, 0x2, "onDone", 0x3, "onError", 0x2, Null]
    //     0xe5ea80: ldr             x4, [x4, #0x168]
    // 0xe5ea84: r0 = GDT[cid_x0 + 0x64e]()
    //     0xe5ea84: add             lr, x0, #0x64e
    //     0xe5ea88: ldr             lr, [x21, lr, lsl #3]
    //     0xe5ea8c: blr             lr
    // 0xe5ea90: mov             x2, x0
    // 0xe5ea94: ldr             x0, [fp, #0x18]
    // 0xe5ea98: stur            x2, [fp, #-8]
    // 0xe5ea9c: r1 = LoadInt32Instr(r0)
    //     0xe5ea9c: sbfx            x1, x0, #1, #0x1f
    //     0xe5eaa0: tbz             w0, #0, #0xe5eaa8
    //     0xe5eaa4: ldur            x1, [x0, #7]
    // 0xe5eaa8: cmp             x1, #0
    // 0xe5eaac: b.le            #0xe5ebb4
    // 0xe5eab0: ldur            x0, [fp, #-0x18]
    // 0xe5eab4: r1 = <void?>
    //     0xe5eab4: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe5eab8: r0 = Completer.sync()
    //     0xe5eab8: bl              #0x6972a8  ; [dart:async] Completer::Completer.sync
    // 0xe5eabc: mov             x3, x0
    // 0xe5eac0: ldur            x0, [fp, #-0x18]
    // 0xe5eac4: stur            x3, [fp, #-0x20]
    // 0xe5eac8: LoadField: r4 = r0->field_f
    //     0xe5eac8: ldur            w4, [x0, #0xf]
    // 0xe5eacc: DecompressPointer r4
    //     0xe5eacc: add             x4, x4, HEAP, lsl #32
    // 0xe5ead0: stur            x4, [fp, #-0x10]
    // 0xe5ead4: LoadField: r2 = r4->field_7
    //     0xe5ead4: ldur            w2, [x4, #7]
    // 0xe5ead8: DecompressPointer r2
    //     0xe5ead8: add             x2, x2, HEAP, lsl #32
    // 0xe5eadc: mov             x0, x3
    // 0xe5eae0: r1 = Null
    //     0xe5eae0: mov             x1, NULL
    // 0xe5eae4: cmp             w2, NULL
    // 0xe5eae8: b.eq            #0xe5eb08
    // 0xe5eaec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe5eaec: ldur            w4, [x2, #0x17]
    // 0xe5eaf0: DecompressPointer r4
    //     0xe5eaf0: add             x4, x4, HEAP, lsl #32
    // 0xe5eaf4: r8 = X0
    //     0xe5eaf4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe5eaf8: LoadField: r9 = r4->field_7
    //     0xe5eaf8: ldur            x9, [x4, #7]
    // 0xe5eafc: r3 = Null
    //     0xe5eafc: add             x3, PP, #0x38, lsl #12  ; [pp+0x386c0] Null
    //     0xe5eb00: ldr             x3, [x3, #0x6c0]
    // 0xe5eb04: blr             x9
    // 0xe5eb08: ldur            x0, [fp, #-0x10]
    // 0xe5eb0c: LoadField: r1 = r0->field_b
    //     0xe5eb0c: ldur            w1, [x0, #0xb]
    // 0xe5eb10: LoadField: r2 = r0->field_f
    //     0xe5eb10: ldur            w2, [x0, #0xf]
    // 0xe5eb14: DecompressPointer r2
    //     0xe5eb14: add             x2, x2, HEAP, lsl #32
    // 0xe5eb18: LoadField: r3 = r2->field_b
    //     0xe5eb18: ldur            w3, [x2, #0xb]
    // 0xe5eb1c: r2 = LoadInt32Instr(r1)
    //     0xe5eb1c: sbfx            x2, x1, #1, #0x1f
    // 0xe5eb20: stur            x2, [fp, #-0x30]
    // 0xe5eb24: r1 = LoadInt32Instr(r3)
    //     0xe5eb24: sbfx            x1, x3, #1, #0x1f
    // 0xe5eb28: cmp             x2, x1
    // 0xe5eb2c: b.ne            #0xe5eb38
    // 0xe5eb30: mov             x1, x0
    // 0xe5eb34: r0 = _growToNextCapacity()
    //     0xe5eb34: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe5eb38: ldur            x4, [fp, #-8]
    // 0xe5eb3c: ldur            x3, [fp, #-0x20]
    // 0xe5eb40: ldur            x0, [fp, #-0x10]
    // 0xe5eb44: ldur            x2, [fp, #-0x30]
    // 0xe5eb48: add             x1, x2, #1
    // 0xe5eb4c: lsl             x5, x1, #1
    // 0xe5eb50: StoreField: r0->field_b = r5
    //     0xe5eb50: stur            w5, [x0, #0xb]
    // 0xe5eb54: LoadField: r1 = r0->field_f
    //     0xe5eb54: ldur            w1, [x0, #0xf]
    // 0xe5eb58: DecompressPointer r1
    //     0xe5eb58: add             x1, x1, HEAP, lsl #32
    // 0xe5eb5c: mov             x0, x3
    // 0xe5eb60: ArrayStore: r1[r2] = r0  ; List_4
    //     0xe5eb60: add             x25, x1, x2, lsl #2
    //     0xe5eb64: add             x25, x25, #0xf
    //     0xe5eb68: str             w0, [x25]
    //     0xe5eb6c: tbz             w0, #0, #0xe5eb88
    //     0xe5eb70: ldurb           w16, [x1, #-1]
    //     0xe5eb74: ldurb           w17, [x0, #-1]
    //     0xe5eb78: and             x16, x17, x16, lsr #2
    //     0xe5eb7c: tst             x16, HEAP, lsr #32
    //     0xe5eb80: b.eq            #0xe5eb88
    //     0xe5eb84: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe5eb88: LoadField: r0 = r3->field_b
    //     0xe5eb88: ldur            w0, [x3, #0xb]
    // 0xe5eb8c: DecompressPointer r0
    //     0xe5eb8c: add             x0, x0, HEAP, lsl #32
    // 0xe5eb90: r1 = LoadClassIdInstr(r4)
    //     0xe5eb90: ldur            x1, [x4, #-1]
    //     0xe5eb94: ubfx            x1, x1, #0xc, #0x14
    // 0xe5eb98: str             x0, [SP]
    // 0xe5eb9c: mov             x0, x1
    // 0xe5eba0: mov             x1, x4
    // 0xe5eba4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe5eba4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe5eba8: r0 = GDT[cid_x0 + 0x40b]()
    //     0xe5eba8: add             lr, x0, #0x40b
    //     0xe5ebac: ldr             lr, [x21, lr, lsl #3]
    //     0xe5ebb0: blr             lr
    // 0xe5ebb4: ldur            x0, [fp, #-8]
    // 0xe5ebb8: LeaveFrame
    //     0xe5ebb8: mov             SP, fp
    //     0xe5ebbc: ldp             fp, lr, [SP], #0x10
    // 0xe5ebc0: ret
    //     0xe5ebc0: ret             
    // 0xe5ebc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5ebc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5ebc8: b               #0xe5e990
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xe5ebcc, size: 0x220
    // 0xe5ebcc: EnterFrame
    //     0xe5ebcc: stp             fp, lr, [SP, #-0x10]!
    //     0xe5ebd0: mov             fp, SP
    // 0xe5ebd4: AllocStack(0x28)
    //     0xe5ebd4: sub             SP, SP, #0x28
    // 0xe5ebd8: SetupParameters()
    //     0xe5ebd8: ldr             x0, [fp, #0x10]
    //     0xe5ebdc: ldur            w1, [x0, #0x17]
    //     0xe5ebe0: add             x1, x1, HEAP, lsl #32
    //     0xe5ebe4: stur            x1, [fp, #-0x20]
    // 0xe5ebe8: CheckStackOverflow
    //     0xe5ebe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5ebec: cmp             SP, x16
    //     0xe5ebf0: b.ls            #0xe5eddc
    // 0xe5ebf4: LoadField: r0 = r1->field_f
    //     0xe5ebf4: ldur            w0, [x1, #0xf]
    // 0xe5ebf8: DecompressPointer r0
    //     0xe5ebf8: add             x0, x0, HEAP, lsl #32
    // 0xe5ebfc: stur            x0, [fp, #-0x18]
    // 0xe5ec00: LoadField: r2 = r1->field_b
    //     0xe5ec00: ldur            w2, [x1, #0xb]
    // 0xe5ec04: DecompressPointer r2
    //     0xe5ec04: add             x2, x2, HEAP, lsl #32
    // 0xe5ec08: stur            x2, [fp, #-0x10]
    // 0xe5ec0c: LoadField: r3 = r2->field_b
    //     0xe5ec0c: ldur            w3, [x2, #0xb]
    // 0xe5ec10: DecompressPointer r3
    //     0xe5ec10: add             x3, x3, HEAP, lsl #32
    // 0xe5ec14: stur            x3, [fp, #-8]
    // 0xe5ec18: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xe5ec18: ldur            w4, [x3, #0x17]
    // 0xe5ec1c: DecompressPointer r4
    //     0xe5ec1c: add             x4, x4, HEAP, lsl #32
    // 0xe5ec20: r16 = Sentinel
    //     0xe5ec20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe5ec24: cmp             w4, w16
    // 0xe5ec28: b.ne            #0xe5ec3c
    // 0xe5ec2c: r16 = "subscriptions"
    //     0xe5ec2c: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0xe5ec30: ldr             x16, [x16, #0xa78]
    // 0xe5ec34: str             x16, [SP]
    // 0xe5ec38: r0 = _throwLocalNotInitialized()
    //     0xe5ec38: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xe5ec3c: ldur            x0, [fp, #-0x18]
    // 0xe5ec40: ldur            x2, [fp, #-8]
    // 0xe5ec44: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xe5ec44: ldur            w1, [x2, #0x17]
    // 0xe5ec48: DecompressPointer r1
    //     0xe5ec48: add             x1, x1, HEAP, lsl #32
    // 0xe5ec4c: LoadField: r3 = r1->field_b
    //     0xe5ec4c: ldur            w3, [x1, #0xb]
    // 0xe5ec50: r4 = LoadInt32Instr(r3)
    //     0xe5ec50: sbfx            x4, x3, #1, #0x1f
    // 0xe5ec54: sub             x3, x4, #1
    // 0xe5ec58: r4 = LoadInt32Instr(r0)
    //     0xe5ec58: sbfx            x4, x0, #1, #0x1f
    //     0xe5ec5c: tbz             w0, #0, #0xe5ec64
    //     0xe5ec60: ldur            x4, [x0, #7]
    // 0xe5ec64: cmp             x4, x3
    // 0xe5ec68: b.ge            #0xe5ed64
    // 0xe5ec6c: ldur            x3, [fp, #-0x20]
    // 0xe5ec70: ldur            x0, [fp, #-0x10]
    // 0xe5ec74: LoadField: r4 = r0->field_f
    //     0xe5ec74: ldur            w4, [x0, #0xf]
    // 0xe5ec78: DecompressPointer r4
    //     0xe5ec78: add             x4, x4, HEAP, lsl #32
    // 0xe5ec7c: LoadField: r0 = r3->field_f
    //     0xe5ec7c: ldur            w0, [x3, #0xf]
    // 0xe5ec80: DecompressPointer r0
    //     0xe5ec80: add             x0, x0, HEAP, lsl #32
    // 0xe5ec84: LoadField: r1 = r4->field_b
    //     0xe5ec84: ldur            w1, [x4, #0xb]
    // 0xe5ec88: r5 = LoadInt32Instr(r0)
    //     0xe5ec88: sbfx            x5, x0, #1, #0x1f
    //     0xe5ec8c: tbz             w0, #0, #0xe5ec94
    //     0xe5ec90: ldur            x5, [x0, #7]
    // 0xe5ec94: r0 = LoadInt32Instr(r1)
    //     0xe5ec94: sbfx            x0, x1, #1, #0x1f
    // 0xe5ec98: mov             x1, x5
    // 0xe5ec9c: cmp             x1, x0
    // 0xe5eca0: b.hs            #0xe5ede4
    // 0xe5eca4: LoadField: r0 = r4->field_f
    //     0xe5eca4: ldur            w0, [x4, #0xf]
    // 0xe5eca8: DecompressPointer r0
    //     0xe5eca8: add             x0, x0, HEAP, lsl #32
    // 0xe5ecac: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xe5ecac: add             x16, x0, x5, lsl #2
    //     0xe5ecb0: ldur            w1, [x16, #0xf]
    // 0xe5ecb4: DecompressPointer r1
    //     0xe5ecb4: add             x1, x1, HEAP, lsl #32
    // 0xe5ecb8: r0 = LoadClassIdInstr(r1)
    //     0xe5ecb8: ldur            x0, [x1, #-1]
    //     0xe5ecbc: ubfx            x0, x0, #0xc, #0x14
    // 0xe5ecc0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe5ecc0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe5ecc4: r0 = GDT[cid_x0 + -0xfbb]()
    //     0xe5ecc4: sub             lr, x0, #0xfbb
    //     0xe5ecc8: ldr             lr, [x21, lr, lsl #3]
    //     0xe5eccc: blr             lr
    // 0xe5ecd0: ldur            x0, [fp, #-8]
    // 0xe5ecd4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe5ecd4: ldur            w1, [x0, #0x17]
    // 0xe5ecd8: DecompressPointer r1
    //     0xe5ecd8: add             x1, x1, HEAP, lsl #32
    // 0xe5ecdc: r16 = Sentinel
    //     0xe5ecdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe5ece0: cmp             w1, w16
    // 0xe5ece4: b.ne            #0xe5ecf8
    // 0xe5ece8: r16 = "subscriptions"
    //     0xe5ece8: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0xe5ecec: ldr             x16, [x16, #0xa78]
    // 0xe5ecf0: str             x16, [SP]
    // 0xe5ecf4: r0 = _throwLocalNotInitialized()
    //     0xe5ecf4: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xe5ecf8: ldur            x0, [fp, #-0x20]
    // 0xe5ecfc: ldur            x2, [fp, #-8]
    // 0xe5ed00: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe5ed00: ldur            w3, [x2, #0x17]
    // 0xe5ed04: DecompressPointer r3
    //     0xe5ed04: add             x3, x3, HEAP, lsl #32
    // 0xe5ed08: LoadField: r1 = r0->field_f
    //     0xe5ed08: ldur            w1, [x0, #0xf]
    // 0xe5ed0c: DecompressPointer r1
    //     0xe5ed0c: add             x1, x1, HEAP, lsl #32
    // 0xe5ed10: r0 = LoadInt32Instr(r1)
    //     0xe5ed10: sbfx            x0, x1, #1, #0x1f
    //     0xe5ed14: tbz             w1, #0, #0xe5ed1c
    //     0xe5ed18: ldur            x0, [x1, #7]
    // 0xe5ed1c: add             x4, x0, #1
    // 0xe5ed20: LoadField: r0 = r3->field_b
    //     0xe5ed20: ldur            w0, [x3, #0xb]
    // 0xe5ed24: r1 = LoadInt32Instr(r0)
    //     0xe5ed24: sbfx            x1, x0, #1, #0x1f
    // 0xe5ed28: mov             x0, x1
    // 0xe5ed2c: mov             x1, x4
    // 0xe5ed30: cmp             x1, x0
    // 0xe5ed34: b.hs            #0xe5ede8
    // 0xe5ed38: ArrayLoad: r0 = r3[r4]  ; Unknown_4
    //     0xe5ed38: add             x16, x3, x4, lsl #2
    //     0xe5ed3c: ldur            w0, [x16, #0xf]
    // 0xe5ed40: DecompressPointer r0
    //     0xe5ed40: add             x0, x0, HEAP, lsl #32
    // 0xe5ed44: StoreField: r2->field_1b = r0
    //     0xe5ed44: stur            w0, [x2, #0x1b]
    //     0xe5ed48: ldurb           w16, [x2, #-1]
    //     0xe5ed4c: ldurb           w17, [x0, #-1]
    //     0xe5ed50: and             x16, x17, x16, lsr #2
    //     0xe5ed54: tst             x16, HEAP, lsr #32
    //     0xe5ed58: b.eq            #0xe5ed60
    //     0xe5ed5c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe5ed60: b               #0xe5edcc
    // 0xe5ed64: ldur            x0, [fp, #-0x20]
    // 0xe5ed68: LoadField: r3 = r0->field_f
    //     0xe5ed68: ldur            w3, [x0, #0xf]
    // 0xe5ed6c: DecompressPointer r3
    //     0xe5ed6c: add             x3, x3, HEAP, lsl #32
    // 0xe5ed70: stur            x3, [fp, #-0x10]
    // 0xe5ed74: r16 = Sentinel
    //     0xe5ed74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe5ed78: cmp             w1, w16
    // 0xe5ed7c: b.ne            #0xe5ed90
    // 0xe5ed80: r16 = "subscriptions"
    //     0xe5ed80: add             x16, PP, #0xe, lsl #12  ; [pp+0xea78] "subscriptions"
    //     0xe5ed84: ldr             x16, [x16, #0xa78]
    // 0xe5ed88: str             x16, [SP]
    // 0xe5ed8c: r0 = _throwLocalNotInitialized()
    //     0xe5ed8c: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xe5ed90: ldur            x0, [fp, #-8]
    // 0xe5ed94: ldur            x1, [fp, #-0x10]
    // 0xe5ed98: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xe5ed98: ldur            w2, [x0, #0x17]
    // 0xe5ed9c: DecompressPointer r2
    //     0xe5ed9c: add             x2, x2, HEAP, lsl #32
    // 0xe5eda0: LoadField: r3 = r2->field_b
    //     0xe5eda0: ldur            w3, [x2, #0xb]
    // 0xe5eda4: r2 = LoadInt32Instr(r3)
    //     0xe5eda4: sbfx            x2, x3, #1, #0x1f
    // 0xe5eda8: sub             x3, x2, #1
    // 0xe5edac: r2 = LoadInt32Instr(r1)
    //     0xe5edac: sbfx            x2, x1, #1, #0x1f
    //     0xe5edb0: tbz             w1, #0, #0xe5edb8
    //     0xe5edb4: ldur            x2, [x1, #7]
    // 0xe5edb8: cmp             x2, x3
    // 0xe5edbc: b.ne            #0xe5edcc
    // 0xe5edc0: LoadField: r1 = r0->field_13
    //     0xe5edc0: ldur            w1, [x0, #0x13]
    // 0xe5edc4: DecompressPointer r1
    //     0xe5edc4: add             x1, x1, HEAP, lsl #32
    // 0xe5edc8: r0 = close()
    //     0xe5edc8: bl              #0xc67e4c  ; [dart:async] _StreamController::close
    // 0xe5edcc: r0 = Null
    //     0xe5edcc: mov             x0, NULL
    // 0xe5edd0: LeaveFrame
    //     0xe5edd0: mov             SP, fp
    //     0xe5edd4: ldp             fp, lr, [SP], #0x10
    // 0xe5edd8: ret
    //     0xe5edd8: ret             
    // 0xe5eddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5eddc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5ede0: b               #0xe5ebf4
    // 0xe5ede4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe5ede4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe5ede8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe5ede8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static (dynamic) => void onDone(dynamic, int) {
    // ** addr: 0xe5edec, size: 0x6c
    // 0xe5edec: EnterFrame
    //     0xe5edec: stp             fp, lr, [SP, #-0x10]!
    //     0xe5edf0: mov             fp, SP
    // 0xe5edf4: AllocStack(0x8)
    //     0xe5edf4: sub             SP, SP, #8
    // 0xe5edf8: SetupParameters()
    //     0xe5edf8: ldr             x0, [fp, #0x18]
    //     0xe5edfc: ldur            w1, [x0, #0x17]
    //     0xe5ee00: add             x1, x1, HEAP, lsl #32
    //     0xe5ee04: stur            x1, [fp, #-8]
    // 0xe5ee08: r1 = 1
    //     0xe5ee08: movz            x1, #0x1
    // 0xe5ee0c: r0 = AllocateContext()
    //     0xe5ee0c: bl              #0xec126c  ; AllocateContextStub
    // 0xe5ee10: mov             x1, x0
    // 0xe5ee14: ldur            x0, [fp, #-8]
    // 0xe5ee18: StoreField: r1->field_b = r0
    //     0xe5ee18: stur            w0, [x1, #0xb]
    // 0xe5ee1c: ldr             x0, [fp, #0x10]
    // 0xe5ee20: StoreField: r1->field_f = r0
    //     0xe5ee20: stur            w0, [x1, #0xf]
    // 0xe5ee24: ldr             x0, [fp, #0x18]
    // 0xe5ee28: LoadField: r3 = r0->field_b
    //     0xe5ee28: ldur            w3, [x0, #0xb]
    // 0xe5ee2c: DecompressPointer r3
    //     0xe5ee2c: add             x3, x3, HEAP, lsl #32
    // 0xe5ee30: mov             x2, x1
    // 0xe5ee34: stur            x3, [fp, #-8]
    // 0xe5ee38: r1 = Function '<anonymous closure>': static.
    //     0xe5ee38: add             x1, PP, #0x38, lsl #12  ; [pp+0x386b8] AnonymousClosure: static (0xe5ebcc), in [package:rxdart/src/streams/concat_eager.dart] ConcatEagerStream::_buildController (0xe5e48c)
    //     0xe5ee3c: ldr             x1, [x1, #0x6b8]
    // 0xe5ee40: r0 = AllocateClosure()
    //     0xe5ee40: bl              #0xec1630  ; AllocateClosureStub
    // 0xe5ee44: ldur            x1, [fp, #-8]
    // 0xe5ee48: StoreField: r0->field_b = r1
    //     0xe5ee48: stur            w1, [x0, #0xb]
    // 0xe5ee4c: LeaveFrame
    //     0xe5ee4c: mov             SP, fp
    //     0xe5ee50: ldp             fp, lr, [SP], #0x10
    // 0xe5ee54: ret
    //     0xe5ee54: ret             
  }
}
