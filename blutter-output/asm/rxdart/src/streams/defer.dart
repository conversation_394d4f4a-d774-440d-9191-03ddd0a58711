// lib: , url: package:rxdart/src/streams/defer.dart

// class id: 1051086, size: 0x8
class :: {
}

// class id: 6633, size: 0x14, field offset: 0xc
class DeferStream<X0> extends Stream<X0> {

  _ listen(/* No info */) {
    // ** addr: 0xd10cf4, size: 0x1dc
    // 0xd10cf4: EnterFrame
    //     0xd10cf4: stp             fp, lr, [SP, #-0x10]!
    //     0xd10cf8: mov             fp, SP
    // 0xd10cfc: AllocStack(0xc0)
    //     0xd10cfc: sub             SP, SP, #0xc0
    // 0xd10d00: SetupParameters(DeferStream<X0> this /* r1 => r1, fp-0xa0 */, dynamic _ /* r2 => r2, fp-0xa8 */, {dynamic cancelOnError = Null /* r5, fp-0x98 */, dynamic onDone = Null /* r6, fp-0x90 */, dynamic onError = Null /* r3, fp-0x88 */})
    //     0xd10d00: stur            x1, [fp, #-0xa0]
    //     0xd10d04: stur            x2, [fp, #-0xa8]
    //     0xd10d08: ldur            w0, [x4, #0x13]
    //     0xd10d0c: ldur            w3, [x4, #0x1f]
    //     0xd10d10: add             x3, x3, HEAP, lsl #32
    //     0xd10d14: add             x16, PP, #0xc, lsl #12  ; [pp+0xc128] "cancelOnError"
    //     0xd10d18: ldr             x16, [x16, #0x128]
    //     0xd10d1c: cmp             w3, w16
    //     0xd10d20: b.ne            #0xd10d44
    //     0xd10d24: ldur            w3, [x4, #0x23]
    //     0xd10d28: add             x3, x3, HEAP, lsl #32
    //     0xd10d2c: sub             w5, w0, w3
    //     0xd10d30: add             x3, fp, w5, sxtw #2
    //     0xd10d34: ldr             x3, [x3, #8]
    //     0xd10d38: mov             x5, x3
    //     0xd10d3c: movz            x3, #0x1
    //     0xd10d40: b               #0xd10d4c
    //     0xd10d44: mov             x5, NULL
    //     0xd10d48: movz            x3, #0
    //     0xd10d4c: stur            x5, [fp, #-0x98]
    //     0xd10d50: lsl             x6, x3, #1
    //     0xd10d54: lsl             w7, w6, #1
    //     0xd10d58: add             w8, w7, #8
    //     0xd10d5c: add             x16, x4, w8, sxtw #1
    //     0xd10d60: ldur            w9, [x16, #0xf]
    //     0xd10d64: add             x9, x9, HEAP, lsl #32
    //     0xd10d68: add             x16, PP, #0xc, lsl #12  ; [pp+0xc130] "onDone"
    //     0xd10d6c: ldr             x16, [x16, #0x130]
    //     0xd10d70: cmp             w9, w16
    //     0xd10d74: b.ne            #0xd10da8
    //     0xd10d78: add             w3, w7, #0xa
    //     0xd10d7c: add             x16, x4, w3, sxtw #1
    //     0xd10d80: ldur            w7, [x16, #0xf]
    //     0xd10d84: add             x7, x7, HEAP, lsl #32
    //     0xd10d88: sub             w3, w0, w7
    //     0xd10d8c: add             x7, fp, w3, sxtw #2
    //     0xd10d90: ldr             x7, [x7, #8]
    //     0xd10d94: add             w3, w6, #2
    //     0xd10d98: sbfx            x6, x3, #1, #0x1f
    //     0xd10d9c: mov             x3, x6
    //     0xd10da0: mov             x6, x7
    //     0xd10da4: b               #0xd10dac
    //     0xd10da8: mov             x6, NULL
    //     0xd10dac: stur            x6, [fp, #-0x90]
    //     0xd10db0: lsl             x7, x3, #1
    //     0xd10db4: lsl             w3, w7, #1
    //     0xd10db8: add             w7, w3, #8
    //     0xd10dbc: add             x16, x4, w7, sxtw #1
    //     0xd10dc0: ldur            w8, [x16, #0xf]
    //     0xd10dc4: add             x8, x8, HEAP, lsl #32
    //     0xd10dc8: ldr             x16, [PP, #0xb48]  ; [pp+0xb48] "onError"
    //     0xd10dcc: cmp             w8, w16
    //     0xd10dd0: b.ne            #0xd10df8
    //     0xd10dd4: add             w7, w3, #0xa
    //     0xd10dd8: add             x16, x4, w7, sxtw #1
    //     0xd10ddc: ldur            w3, [x16, #0xf]
    //     0xd10de0: add             x3, x3, HEAP, lsl #32
    //     0xd10de4: sub             w4, w0, w3
    //     0xd10de8: add             x0, fp, w4, sxtw #2
    //     0xd10dec: ldr             x0, [x0, #8]
    //     0xd10df0: mov             x3, x0
    //     0xd10df4: b               #0xd10dfc
    //     0xd10df8: mov             x3, NULL
    //     0xd10dfc: stur            x3, [fp, #-0x88]
    // 0xd10e00: CheckStackOverflow
    //     0xd10e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd10e04: cmp             SP, x16
    //     0xd10e08: b.ls            #0xd10ec8
    // 0xd10e0c: LoadField: r4 = r1->field_b
    //     0xd10e0c: ldur            w4, [x1, #0xb]
    // 0xd10e10: DecompressPointer r4
    //     0xd10e10: add             x4, x4, HEAP, lsl #32
    // 0xd10e14: stur            x4, [fp, #-0x80]
    // 0xd10e18: str             x4, [SP]
    // 0xd10e1c: mov             x0, x4
    // 0xd10e20: ClosureCall
    //     0xd10e20: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xd10e24: ldur            x2, [x0, #0x1f]
    //     0xd10e28: blr             x2
    // 0xd10e2c: r1 = LoadClassIdInstr(r0)
    //     0xd10e2c: ldur            x1, [x0, #-1]
    //     0xd10e30: ubfx            x1, x1, #0xc, #0x14
    // 0xd10e34: ldur            x16, [fp, #-0x88]
    // 0xd10e38: ldur            lr, [fp, #-0x90]
    // 0xd10e3c: stp             lr, x16, [SP, #8]
    // 0xd10e40: ldur            x16, [fp, #-0x98]
    // 0xd10e44: str             x16, [SP]
    // 0xd10e48: mov             x16, x0
    // 0xd10e4c: mov             x0, x1
    // 0xd10e50: mov             x1, x16
    // 0xd10e54: ldur            x2, [fp, #-0xa8]
    // 0xd10e58: r4 = const [0, 0x5, 0x3, 0x2, cancelOnError, 0x4, onDone, 0x3, onError, 0x2, null]
    //     0xd10e58: add             x4, PP, #0xd, lsl #12  ; [pp+0xdf78] List(11) [0, 0x5, 0x3, 0x2, "cancelOnError", 0x4, "onDone", 0x3, "onError", 0x2, Null]
    //     0xd10e5c: ldr             x4, [x4, #0xf78]
    // 0xd10e60: r0 = GDT[cid_x0 + 0x64e]()
    //     0xd10e60: add             lr, x0, #0x64e
    //     0xd10e64: ldr             lr, [x21, lr, lsl #3]
    //     0xd10e68: blr             lr
    // 0xd10e6c: LeaveFrame
    //     0xd10e6c: mov             SP, fp
    //     0xd10e70: ldp             fp, lr, [SP], #0x10
    // 0xd10e74: ret
    //     0xd10e74: ret             
    // 0xd10e78: sub             SP, fp, #0xc0
    // 0xd10e7c: mov             x2, x0
    // 0xd10e80: ldur            x0, [fp, #-8]
    // 0xd10e84: mov             x3, x1
    // 0xd10e88: LoadField: r1 = r0->field_7
    //     0xd10e88: ldur            w1, [x0, #7]
    // 0xd10e8c: DecompressPointer r1
    //     0xd10e8c: add             x1, x1, HEAP, lsl #32
    // 0xd10e90: r0 = Stream.error()
    //     0xd10e90: bl              #0xd10ed0  ; [dart:async] Stream::Stream.error
    // 0xd10e94: ldur            x16, [fp, #-0x18]
    // 0xd10e98: ldur            lr, [fp, #-0x20]
    // 0xd10e9c: stp             lr, x16, [SP, #8]
    // 0xd10ea0: ldur            x16, [fp, #-0x28]
    // 0xd10ea4: str             x16, [SP]
    // 0xd10ea8: mov             x1, x0
    // 0xd10eac: ldur            x2, [fp, #-0x10]
    // 0xd10eb0: r4 = const [0, 0x5, 0x3, 0x2, cancelOnError, 0x4, onDone, 0x3, onError, 0x2, null]
    //     0xd10eb0: add             x4, PP, #0xd, lsl #12  ; [pp+0xdf78] List(11) [0, 0x5, 0x3, 0x2, "cancelOnError", 0x4, "onDone", 0x3, "onError", 0x2, Null]
    //     0xd10eb4: ldr             x4, [x4, #0xf78]
    // 0xd10eb8: r0 = listen()
    //     0xd10eb8: bl              #0xd0b0b4  ; [dart:async] _StreamImpl::listen
    // 0xd10ebc: LeaveFrame
    //     0xd10ebc: mov             SP, fp
    //     0xd10ec0: ldp             fp, lr, [SP], #0x10
    // 0xd10ec4: ret
    //     0xd10ec4: ret             
    // 0xd10ec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd10ec8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd10ecc: b               #0xd10e0c
  }
}
