// lib: , url: package:rxdart/src/streams/never.dart

// class id: 1051087, size: 0x8
class :: {
}

// class id: 6632, size: 0x10, field offset: 0xc
class NeverStream<X0> extends Stream<X0> {

  _ listen(/* No info */) {
    // ** addr: 0xd10f60, size: 0x16c
    // 0xd10f60: EnterFrame
    //     0xd10f60: stp             fp, lr, [SP, #-0x10]!
    //     0xd10f64: mov             fp, SP
    // 0xd10f68: AllocStack(0x40)
    //     0xd10f68: sub             SP, SP, #0x40
    // 0xd10f6c: SetupParameters(dynamic _ /* r2 => r2, fp-0x28 */, {dynamic cancelOnError = Null /* r5, fp-0x20 */, dynamic onDone = Null /* r6, fp-0x18 */, dynamic onError = Null /* r0, fp-0x10 */})
    //     0xd10f6c: stur            x2, [fp, #-0x28]
    //     0xd10f70: ldur            w0, [x4, #0x13]
    //     0xd10f74: ldur            w3, [x4, #0x1f]
    //     0xd10f78: add             x3, x3, HEAP, lsl #32
    //     0xd10f7c: add             x16, PP, #0xc, lsl #12  ; [pp+0xc128] "cancelOnError"
    //     0xd10f80: ldr             x16, [x16, #0x128]
    //     0xd10f84: cmp             w3, w16
    //     0xd10f88: b.ne            #0xd10fac
    //     0xd10f8c: ldur            w3, [x4, #0x23]
    //     0xd10f90: add             x3, x3, HEAP, lsl #32
    //     0xd10f94: sub             w5, w0, w3
    //     0xd10f98: add             x3, fp, w5, sxtw #2
    //     0xd10f9c: ldr             x3, [x3, #8]
    //     0xd10fa0: mov             x5, x3
    //     0xd10fa4: movz            x3, #0x1
    //     0xd10fa8: b               #0xd10fb4
    //     0xd10fac: mov             x5, NULL
    //     0xd10fb0: movz            x3, #0
    //     0xd10fb4: stur            x5, [fp, #-0x20]
    //     0xd10fb8: lsl             x6, x3, #1
    //     0xd10fbc: lsl             w7, w6, #1
    //     0xd10fc0: add             w8, w7, #8
    //     0xd10fc4: add             x16, x4, w8, sxtw #1
    //     0xd10fc8: ldur            w9, [x16, #0xf]
    //     0xd10fcc: add             x9, x9, HEAP, lsl #32
    //     0xd10fd0: add             x16, PP, #0xc, lsl #12  ; [pp+0xc130] "onDone"
    //     0xd10fd4: ldr             x16, [x16, #0x130]
    //     0xd10fd8: cmp             w9, w16
    //     0xd10fdc: b.ne            #0xd11010
    //     0xd10fe0: add             w3, w7, #0xa
    //     0xd10fe4: add             x16, x4, w3, sxtw #1
    //     0xd10fe8: ldur            w7, [x16, #0xf]
    //     0xd10fec: add             x7, x7, HEAP, lsl #32
    //     0xd10ff0: sub             w3, w0, w7
    //     0xd10ff4: add             x7, fp, w3, sxtw #2
    //     0xd10ff8: ldr             x7, [x7, #8]
    //     0xd10ffc: add             w3, w6, #2
    //     0xd11000: sbfx            x6, x3, #1, #0x1f
    //     0xd11004: mov             x3, x6
    //     0xd11008: mov             x6, x7
    //     0xd1100c: b               #0xd11014
    //     0xd11010: mov             x6, NULL
    //     0xd11014: stur            x6, [fp, #-0x18]
    //     0xd11018: lsl             x7, x3, #1
    //     0xd1101c: lsl             w3, w7, #1
    //     0xd11020: add             w7, w3, #8
    //     0xd11024: add             x16, x4, w7, sxtw #1
    //     0xd11028: ldur            w8, [x16, #0xf]
    //     0xd1102c: add             x8, x8, HEAP, lsl #32
    //     0xd11030: ldr             x16, [PP, #0xb48]  ; [pp+0xb48] "onError"
    //     0xd11034: cmp             w8, w16
    //     0xd11038: b.ne            #0xd1105c
    //     0xd1103c: add             w7, w3, #0xa
    //     0xd11040: add             x16, x4, w7, sxtw #1
    //     0xd11044: ldur            w3, [x16, #0xf]
    //     0xd11048: add             x3, x3, HEAP, lsl #32
    //     0xd1104c: sub             w4, w0, w3
    //     0xd11050: add             x0, fp, w4, sxtw #2
    //     0xd11054: ldr             x0, [x0, #8]
    //     0xd11058: b               #0xd11060
    //     0xd1105c: mov             x0, NULL
    //     0xd11060: stur            x0, [fp, #-0x10]
    // 0xd11064: CheckStackOverflow
    //     0xd11064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd11068: cmp             SP, x16
    //     0xd1106c: b.ls            #0xd110c4
    // 0xd11070: LoadField: r3 = r1->field_b
    //     0xd11070: ldur            w3, [x1, #0xb]
    // 0xd11074: DecompressPointer r3
    //     0xd11074: add             x3, x3, HEAP, lsl #32
    // 0xd11078: stur            x3, [fp, #-8]
    // 0xd1107c: LoadField: r1 = r3->field_7
    //     0xd1107c: ldur            w1, [x3, #7]
    // 0xd11080: DecompressPointer r1
    //     0xd11080: add             x1, x1, HEAP, lsl #32
    // 0xd11084: r0 = _ControllerStream()
    //     0xd11084: bl              #0x696858  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0xd11088: mov             x1, x0
    // 0xd1108c: ldur            x0, [fp, #-8]
    // 0xd11090: StoreField: r1->field_b = r0
    //     0xd11090: stur            w0, [x1, #0xb]
    // 0xd11094: ldur            x16, [fp, #-0x10]
    // 0xd11098: ldur            lr, [fp, #-0x18]
    // 0xd1109c: stp             lr, x16, [SP, #8]
    // 0xd110a0: ldur            x16, [fp, #-0x20]
    // 0xd110a4: str             x16, [SP]
    // 0xd110a8: ldur            x2, [fp, #-0x28]
    // 0xd110ac: r4 = const [0, 0x5, 0x3, 0x2, cancelOnError, 0x4, onDone, 0x3, onError, 0x2, null]
    //     0xd110ac: add             x4, PP, #0xd, lsl #12  ; [pp+0xdf78] List(11) [0, 0x5, 0x3, 0x2, "cancelOnError", 0x4, "onDone", 0x3, "onError", 0x2, Null]
    //     0xd110b0: ldr             x4, [x4, #0xf78]
    // 0xd110b4: r0 = listen()
    //     0xd110b4: bl              #0xd0b0b4  ; [dart:async] _StreamImpl::listen
    // 0xd110b8: LeaveFrame
    //     0xd110b8: mov             SP, fp
    //     0xd110bc: ldp             fp, lr, [SP], #0x10
    // 0xd110c0: ret
    //     0xd110c0: ret             
    // 0xd110c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd110c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd110c8: b               #0xd11070
  }
}
