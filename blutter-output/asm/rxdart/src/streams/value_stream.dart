// lib: , url: package:rxdart/src/streams/value_stream.dart

// class id: 1051089, size: 0x8
class :: {
}

// class id: 523, size: 0xc, field offset: 0x8
abstract class ValueStream<X0> extends Object
    implements Stream<X0> {
}

// class id: 6777, size: 0x14, field offset: 0x14
enum _MissingCase extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e75c, size: 0x64
    // 0xc4e75c: EnterFrame
    //     0xc4e75c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e760: mov             fp, SP
    // 0xc4e764: AllocStack(0x10)
    //     0xc4e764: sub             SP, SP, #0x10
    // 0xc4e768: SetupParameters(_MissingCase this /* r1 => r0, fp-0x8 */)
    //     0xc4e768: mov             x0, x1
    //     0xc4e76c: stur            x1, [fp, #-8]
    // 0xc4e770: CheckStackOverflow
    //     0xc4e770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e774: cmp             SP, x16
    //     0xc4e778: b.ls            #0xc4e7b8
    // 0xc4e77c: r1 = Null
    //     0xc4e77c: mov             x1, NULL
    // 0xc4e780: r2 = 4
    //     0xc4e780: movz            x2, #0x4
    // 0xc4e784: r0 = AllocateArray()
    //     0xc4e784: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e788: r16 = "_MissingCase."
    //     0xc4e788: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f08] "_MissingCase."
    //     0xc4e78c: ldr             x16, [x16, #0xf08]
    // 0xc4e790: StoreField: r0->field_f = r16
    //     0xc4e790: stur            w16, [x0, #0xf]
    // 0xc4e794: ldur            x1, [fp, #-8]
    // 0xc4e798: LoadField: r2 = r1->field_f
    //     0xc4e798: ldur            w2, [x1, #0xf]
    // 0xc4e79c: DecompressPointer r2
    //     0xc4e79c: add             x2, x2, HEAP, lsl #32
    // 0xc4e7a0: StoreField: r0->field_13 = r2
    //     0xc4e7a0: stur            w2, [x0, #0x13]
    // 0xc4e7a4: str             x0, [SP]
    // 0xc4e7a8: r0 = _interpolate()
    //     0xc4e7a8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e7ac: LeaveFrame
    //     0xc4e7ac: mov             SP, fp
    //     0xc4e7b0: ldp             fp, lr, [SP], #0x10
    // 0xc4e7b4: ret
    //     0xc4e7b4: ret             
    // 0xc4e7b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e7b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e7bc: b               #0xc4e77c
  }
}

// class id: 7342, size: 0x10, field offset: 0xc
class ValueStreamError extends Error {

  factory _ ValueStreamError.hasNoValue(/* No info */) {
    // ** addr: 0x8af5ec, size: 0x24
    // 0x8af5ec: EnterFrame
    //     0x8af5ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8af5f0: mov             fp, SP
    // 0x8af5f4: r0 = ValueStreamError()
    //     0x8af5f4: bl              #0x8af610  ; AllocateValueStreamErrorStub -> ValueStreamError (size=0x10)
    // 0x8af5f8: r1 = Instance__MissingCase
    //     0x8af5f8: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf70] Obj!_MissingCase@e2e201
    //     0x8af5fc: ldr             x1, [x1, #0xf70]
    // 0x8af600: StoreField: r0->field_b = r1
    //     0x8af600: stur            w1, [x0, #0xb]
    // 0x8af604: LeaveFrame
    //     0x8af604: mov             SP, fp
    //     0x8af608: ldp             fp, lr, [SP], #0x10
    // 0x8af60c: ret
    //     0x8af60c: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xbfd9e0, size: 0x30
    // 0xbfd9e0: ldr             x1, [SP]
    // 0xbfd9e4: LoadField: r2 = r1->field_b
    //     0xbfd9e4: ldur            w2, [x1, #0xb]
    // 0xbfd9e8: DecompressPointer r2
    //     0xbfd9e8: add             x2, x2, HEAP, lsl #32
    // 0xbfd9ec: LoadField: r1 = r2->field_7
    //     0xbfd9ec: ldur            x1, [x2, #7]
    // 0xbfd9f0: cmp             x1, #0
    // 0xbfd9f4: b.gt            #0xbfda04
    // 0xbfd9f8: r0 = "ValueStream has no value. You should check ValueStream.hasValue before accessing ValueStream.value, or use ValueStream.valueOrNull instead."
    //     0xbfd9f8: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c8f8] "ValueStream has no value. You should check ValueStream.hasValue before accessing ValueStream.value, or use ValueStream.valueOrNull instead."
    //     0xbfd9fc: ldr             x0, [x0, #0x8f8]
    // 0xbfda00: ret
    //     0xbfda00: ret             
    // 0xbfda04: r0 = "ValueStream has no error. You should check ValueStream.hasError before accessing ValueStream.error, or use ValueStream.errorOrNull instead."
    //     0xbfda04: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c900] "ValueStream has no error. You should check ValueStream.hasError before accessing ValueStream.error, or use ValueStream.errorOrNull instead."
    //     0xbfda08: ldr             x0, [x0, #0x900]
    // 0xbfda0c: ret
    //     0xbfda0c: ret             
  }
}
