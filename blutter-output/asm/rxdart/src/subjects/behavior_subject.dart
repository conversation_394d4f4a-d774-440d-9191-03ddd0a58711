// lib: , url: package:rxdart/src/subjects/behavior_subject.dart

// class id: 1051090, size: 0x8
class :: {
}

// class id: 522, size: 0x18, field offset: 0x8
class _Wrapper<X0> extends Object {

  _ setValue(/* No info */) {
    // ** addr: 0xaa9410, size: 0x90
    // 0xaa9410: EnterFrame
    //     0xaa9410: stp             fp, lr, [SP, #-0x10]!
    //     0xaa9414: mov             fp, SP
    // 0xaa9418: AllocStack(0x10)
    //     0xaa9418: sub             SP, SP, #0x10
    // 0xaa941c: SetupParameters(_Wrapper<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xaa941c: mov             x4, x1
    //     0xaa9420: mov             x3, x2
    //     0xaa9424: stur            x1, [fp, #-8]
    //     0xaa9428: stur            x2, [fp, #-0x10]
    // 0xaa942c: LoadField: r2 = r4->field_7
    //     0xaa942c: ldur            w2, [x4, #7]
    // 0xaa9430: DecompressPointer r2
    //     0xaa9430: add             x2, x2, HEAP, lsl #32
    // 0xaa9434: mov             x0, x3
    // 0xaa9438: r1 = Null
    //     0xaa9438: mov             x1, NULL
    // 0xaa943c: cmp             w2, NULL
    // 0xaa9440: b.eq            #0xaa9460
    // 0xaa9444: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xaa9444: ldur            w4, [x2, #0x17]
    // 0xaa9448: DecompressPointer r4
    //     0xaa9448: add             x4, x4, HEAP, lsl #32
    // 0xaa944c: r8 = X0
    //     0xaa944c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xaa9450: LoadField: r9 = r4->field_7
    //     0xaa9450: ldur            x9, [x4, #7]
    // 0xaa9454: r3 = Null
    //     0xaa9454: add             x3, PP, #0xe, lsl #12  ; [pp+0xef20] Null
    //     0xaa9458: ldr             x3, [x3, #0xf20]
    // 0xaa945c: blr             x9
    // 0xaa9460: ldur            x0, [fp, #-0x10]
    // 0xaa9464: ldur            x1, [fp, #-8]
    // 0xaa9468: StoreField: r1->field_f = r0
    //     0xaa9468: stur            w0, [x1, #0xf]
    //     0xaa946c: tbz             w0, #0, #0xaa9488
    //     0xaa9470: ldurb           w16, [x1, #-1]
    //     0xaa9474: ldurb           w17, [x0, #-1]
    //     0xaa9478: and             x16, x17, x16, lsr #2
    //     0xaa947c: tst             x16, HEAP, lsr #32
    //     0xaa9480: b.eq            #0xaa9488
    //     0xaa9484: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaa9488: r2 = true
    //     0xaa9488: add             x2, NULL, #0x20  ; true
    // 0xaa948c: StoreField: r1->field_b = r2
    //     0xaa948c: stur            w2, [x1, #0xb]
    // 0xaa9490: r0 = Null
    //     0xaa9490: mov             x0, NULL
    // 0xaa9494: LeaveFrame
    //     0xaa9494: mov             SP, fp
    //     0xaa9498: ldp             fp, lr, [SP], #0x10
    // 0xaa949c: ret
    //     0xaa949c: ret             
  }
  _ setError(/* No info */) {
    // ** addr: 0xced1e4, size: 0x64
    // 0xced1e4: EnterFrame
    //     0xced1e4: stp             fp, lr, [SP, #-0x10]!
    //     0xced1e8: mov             fp, SP
    // 0xced1ec: AllocStack(0x18)
    //     0xced1ec: sub             SP, SP, #0x18
    // 0xced1f0: SetupParameters(_Wrapper<X0> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xced1f0: stur            x1, [fp, #-8]
    //     0xced1f4: stur            x2, [fp, #-0x10]
    //     0xced1f8: stur            x3, [fp, #-0x18]
    // 0xced1fc: r0 = ErrorAndStackTrace()
    //     0xced1fc: bl              #0xced248  ; AllocateErrorAndStackTraceStub -> ErrorAndStackTrace (size=0x10)
    // 0xced200: ldur            x1, [fp, #-0x10]
    // 0xced204: StoreField: r0->field_7 = r1
    //     0xced204: stur            w1, [x0, #7]
    // 0xced208: ldur            x1, [fp, #-0x18]
    // 0xced20c: StoreField: r0->field_b = r1
    //     0xced20c: stur            w1, [x0, #0xb]
    // 0xced210: ldur            x1, [fp, #-8]
    // 0xced214: StoreField: r1->field_13 = r0
    //     0xced214: stur            w0, [x1, #0x13]
    //     0xced218: ldurb           w16, [x1, #-1]
    //     0xced21c: ldurb           w17, [x0, #-1]
    //     0xced220: and             x16, x17, x16, lsr #2
    //     0xced224: tst             x16, HEAP, lsr #32
    //     0xced228: b.eq            #0xced230
    //     0xced22c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xced230: r2 = false
    //     0xced230: add             x2, NULL, #0x30  ; false
    // 0xced234: StoreField: r1->field_b = r2
    //     0xced234: stur            w2, [x1, #0xb]
    // 0xced238: r0 = Null
    //     0xced238: mov             x0, NULL
    // 0xced23c: LeaveFrame
    //     0xced23c: mov             SP, fp
    //     0xced240: ldp             fp, lr, [SP], #0x10
    // 0xced244: ret
    //     0xced244: ret             
  }
}

// class id: 6631, size: 0x10, field offset: 0xc
class _BehaviorSubjectStream<X0> extends Stream<X0>
    implements ValueStream<X0> {

  _ ==(/* No info */) {
    // ** addr: 0xd2d7d0, size: 0x74
    // 0xd2d7d0: ldr             x1, [SP]
    // 0xd2d7d4: cmp             w1, NULL
    // 0xd2d7d8: b.ne            #0xd2d7e4
    // 0xd2d7dc: r0 = false
    //     0xd2d7dc: add             x0, NULL, #0x30  ; false
    // 0xd2d7e0: ret
    //     0xd2d7e0: ret             
    // 0xd2d7e4: ldr             x2, [SP, #8]
    // 0xd2d7e8: cmp             w2, w1
    // 0xd2d7ec: b.ne            #0xd2d7f8
    // 0xd2d7f0: r0 = true
    //     0xd2d7f0: add             x0, NULL, #0x20  ; true
    // 0xd2d7f4: ret
    //     0xd2d7f4: ret             
    // 0xd2d7f8: r3 = 60
    //     0xd2d7f8: movz            x3, #0x3c
    // 0xd2d7fc: branchIfSmi(r1, 0xd2d808)
    //     0xd2d7fc: tbz             w1, #0, #0xd2d808
    // 0xd2d800: r3 = LoadClassIdInstr(r1)
    //     0xd2d800: ldur            x3, [x1, #-1]
    //     0xd2d804: ubfx            x3, x3, #0xc, #0x14
    // 0xd2d808: r17 = 6631
    //     0xd2d808: movz            x17, #0x19e7
    // 0xd2d80c: cmp             x3, x17
    // 0xd2d810: b.ne            #0xd2d83c
    // 0xd2d814: LoadField: r3 = r1->field_b
    //     0xd2d814: ldur            w3, [x1, #0xb]
    // 0xd2d818: DecompressPointer r3
    //     0xd2d818: add             x3, x3, HEAP, lsl #32
    // 0xd2d81c: LoadField: r1 = r2->field_b
    //     0xd2d81c: ldur            w1, [x2, #0xb]
    // 0xd2d820: DecompressPointer r1
    //     0xd2d820: add             x1, x1, HEAP, lsl #32
    // 0xd2d824: cmp             w3, w1
    // 0xd2d828: r16 = true
    //     0xd2d828: add             x16, NULL, #0x20  ; true
    // 0xd2d82c: r17 = false
    //     0xd2d82c: add             x17, NULL, #0x30  ; false
    // 0xd2d830: csel            x2, x16, x17, eq
    // 0xd2d834: mov             x0, x2
    // 0xd2d838: b               #0xd2d840
    // 0xd2d83c: r0 = false
    //     0xd2d83c: add             x0, NULL, #0x30  ; false
    // 0xd2d840: ret
    //     0xd2d840: ret             
  }
}

// class id: 6670, size: 0x1c, field offset: 0x18
class BehaviorSubject<X0> extends Subject<X0>
    implements ValueStream<X0> {

  get _ value(/* No info */) {
    // ** addr: 0x8af554, size: 0x98
    // 0x8af554: EnterFrame
    //     0x8af554: stp             fp, lr, [SP, #-0x10]!
    //     0x8af558: mov             fp, SP
    // 0x8af55c: AllocStack(0x8)
    //     0x8af55c: sub             SP, SP, #8
    // 0x8af560: CheckStackOverflow
    //     0x8af560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8af564: cmp             SP, x16
    //     0x8af568: b.ls            #0x8af5e4
    // 0x8af56c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8af56c: ldur            w0, [x1, #0x17]
    // 0x8af570: DecompressPointer r0
    //     0x8af570: add             x0, x0, HEAP, lsl #32
    // 0x8af574: LoadField: r3 = r0->field_f
    //     0x8af574: ldur            w3, [x0, #0xf]
    // 0x8af578: DecompressPointer r3
    //     0x8af578: add             x3, x3, HEAP, lsl #32
    // 0x8af57c: stur            x3, [fp, #-8]
    // 0x8af580: r16 = Instance__Empty
    //     0x8af580: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf58] Obj!_Empty@e0c0e1
    //     0x8af584: ldr             x16, [x16, #0xf58]
    // 0x8af588: cmp             w3, w16
    // 0x8af58c: b.eq            #0x8af5d4
    // 0x8af590: LoadField: r2 = r1->field_7
    //     0x8af590: ldur            w2, [x1, #7]
    // 0x8af594: DecompressPointer r2
    //     0x8af594: add             x2, x2, HEAP, lsl #32
    // 0x8af598: mov             x0, x3
    // 0x8af59c: r1 = Null
    //     0x8af59c: mov             x1, NULL
    // 0x8af5a0: cmp             w2, NULL
    // 0x8af5a4: b.eq            #0x8af5c4
    // 0x8af5a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8af5a8: ldur            w4, [x2, #0x17]
    // 0x8af5ac: DecompressPointer r4
    //     0x8af5ac: add             x4, x4, HEAP, lsl #32
    // 0x8af5b0: r8 = X0
    //     0x8af5b0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8af5b4: LoadField: r9 = r4->field_7
    //     0x8af5b4: ldur            x9, [x4, #7]
    // 0x8af5b8: r3 = Null
    //     0x8af5b8: add             x3, PP, #0xd, lsl #12  ; [pp+0xdf60] Null
    //     0x8af5bc: ldr             x3, [x3, #0xf60]
    // 0x8af5c0: blr             x9
    // 0x8af5c4: ldur            x0, [fp, #-8]
    // 0x8af5c8: LeaveFrame
    //     0x8af5c8: mov             SP, fp
    //     0x8af5cc: ldp             fp, lr, [SP], #0x10
    // 0x8af5d0: ret
    //     0x8af5d0: ret             
    // 0x8af5d4: r1 = Null
    //     0x8af5d4: mov             x1, NULL
    // 0x8af5d8: r0 = ValueStreamError.hasNoValue()
    //     0x8af5d8: bl              #0x8af5ec  ; [package:rxdart/src/streams/value_stream.dart] ValueStreamError::ValueStreamError.hasNoValue
    // 0x8af5dc: r0 = Throw()
    //     0x8af5dc: bl              #0xec04b8  ; ThrowStub
    // 0x8af5e0: brk             #0
    // 0x8af5e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8af5e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8af5e8: b               #0x8af56c
  }
  get _ hasValue(/* No info */) {
    // ** addr: 0x8af61c, size: 0x2c
    // 0x8af61c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x8af61c: ldur            w2, [x1, #0x17]
    // 0x8af620: DecompressPointer r2
    //     0x8af620: add             x2, x2, HEAP, lsl #32
    // 0x8af624: LoadField: r1 = r2->field_f
    //     0x8af624: ldur            w1, [x2, #0xf]
    // 0x8af628: DecompressPointer r1
    //     0x8af628: add             x1, x1, HEAP, lsl #32
    // 0x8af62c: r16 = Instance__Empty
    //     0x8af62c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf58] Obj!_Empty@e0c0e1
    //     0x8af630: ldr             x16, [x16, #0xf58]
    // 0x8af634: cmp             w1, w16
    // 0x8af638: r16 = true
    //     0x8af638: add             x16, NULL, #0x20  ; true
    // 0x8af63c: r17 = false
    //     0x8af63c: add             x17, NULL, #0x30  ; false
    // 0x8af640: csel            x0, x16, x17, ne
    // 0x8af644: ret
    //     0x8af644: ret             
  }
  factory _ BehaviorSubject(/* No info */) {
    // ** addr: 0x8b3488, size: 0x168
    // 0x8b3488: EnterFrame
    //     0x8b3488: stp             fp, lr, [SP, #-0x10]!
    //     0x8b348c: mov             fp, SP
    // 0x8b3490: AllocStack(0x30)
    //     0x8b3490: sub             SP, SP, #0x30
    // 0x8b3494: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, {dynamic onListen = Null /* r3 */, dynamic sync = false /* r1 */})
    //     0x8b3494: mov             x0, x1
    //     0x8b3498: stur            x1, [fp, #-8]
    //     0x8b349c: ldur            w1, [x4, #0x13]
    //     0x8b34a0: ldur            w2, [x4, #0x1f]
    //     0x8b34a4: add             x2, x2, HEAP, lsl #32
    //     0x8b34a8: ldr             x16, [PP, #0x3688]  ; [pp+0x3688] "onListen"
    //     0x8b34ac: cmp             w2, w16
    //     0x8b34b0: b.ne            #0x8b34d4
    //     0x8b34b4: ldur            w2, [x4, #0x23]
    //     0x8b34b8: add             x2, x2, HEAP, lsl #32
    //     0x8b34bc: sub             w3, w1, w2
    //     0x8b34c0: add             x2, fp, w3, sxtw #2
    //     0x8b34c4: ldr             x2, [x2, #8]
    //     0x8b34c8: mov             x3, x2
    //     0x8b34cc: movz            x2, #0x1
    //     0x8b34d0: b               #0x8b34dc
    //     0x8b34d4: mov             x3, NULL
    //     0x8b34d8: movz            x2, #0
    //     0x8b34dc: lsl             x5, x2, #1
    //     0x8b34e0: lsl             w2, w5, #1
    //     0x8b34e4: add             w5, w2, #8
    //     0x8b34e8: add             x16, x4, w5, sxtw #1
    //     0x8b34ec: ldur            w6, [x16, #0xf]
    //     0x8b34f0: add             x6, x6, HEAP, lsl #32
    //     0x8b34f4: ldr             x16, [PP, #0x36a0]  ; [pp+0x36a0] "sync"
    //     0x8b34f8: cmp             w6, w16
    //     0x8b34fc: b.ne            #0x8b3520
    //     0x8b3500: add             w5, w2, #0xa
    //     0x8b3504: add             x16, x4, w5, sxtw #1
    //     0x8b3508: ldur            w2, [x16, #0xf]
    //     0x8b350c: add             x2, x2, HEAP, lsl #32
    //     0x8b3510: sub             w4, w1, w2
    //     0x8b3514: add             x1, fp, w4, sxtw #2
    //     0x8b3518: ldr             x1, [x1, #8]
    //     0x8b351c: b               #0x8b3524
    //     0x8b3520: add             x1, NULL, #0x30  ; false
    // 0x8b3524: CheckStackOverflow
    //     0x8b3524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b3528: cmp             SP, x16
    //     0x8b352c: b.ls            #0x8b35e8
    // 0x8b3530: stp             NULL, x3, [SP, #8]
    // 0x8b3534: str             x1, [SP]
    // 0x8b3538: mov             x1, x0
    // 0x8b353c: r4 = const [0, 0x4, 0x3, 0x1, onCancel, 0x2, onListen, 0x1, sync, 0x3, null]
    //     0x8b353c: add             x4, PP, #0xf, lsl #12  ; [pp+0xf148] List(11) [0, 0x4, 0x3, 0x1, "onCancel", 0x2, "onListen", 0x1, "sync", 0x3, Null]
    //     0x8b3540: ldr             x4, [x4, #0x148]
    // 0x8b3544: r0 = StreamController.broadcast()
    //     0x8b3544: bl              #0x83522c  ; [dart:async] StreamController::StreamController.broadcast
    // 0x8b3548: ldur            x1, [fp, #-8]
    // 0x8b354c: stur            x0, [fp, #-0x10]
    // 0x8b3550: r0 = _Wrapper()
    //     0x8b3550: bl              #0x8b3650  ; Allocate_WrapperStub -> _Wrapper<X0> (size=0x18)
    // 0x8b3554: mov             x1, x0
    // 0x8b3558: r0 = Instance__Empty
    //     0x8b3558: add             x0, PP, #0xd, lsl #12  ; [pp+0xdf58] Obj!_Empty@e0c0e1
    //     0x8b355c: ldr             x0, [x0, #0xf58]
    // 0x8b3560: stur            x1, [fp, #-0x18]
    // 0x8b3564: StoreField: r1->field_f = r0
    //     0x8b3564: stur            w0, [x1, #0xf]
    // 0x8b3568: r0 = false
    //     0x8b3568: add             x0, NULL, #0x30  ; false
    // 0x8b356c: StoreField: r1->field_b = r0
    //     0x8b356c: stur            w0, [x1, #0xb]
    // 0x8b3570: r1 = 2
    //     0x8b3570: movz            x1, #0x2
    // 0x8b3574: r0 = AllocateContext()
    //     0x8b3574: bl              #0xec126c  ; AllocateContextStub
    // 0x8b3578: mov             x1, x0
    // 0x8b357c: ldur            x0, [fp, #-0x18]
    // 0x8b3580: StoreField: r1->field_f = r0
    //     0x8b3580: stur            w0, [x1, #0xf]
    // 0x8b3584: ldur            x3, [fp, #-0x10]
    // 0x8b3588: StoreField: r1->field_13 = r3
    //     0x8b3588: stur            w3, [x1, #0x13]
    // 0x8b358c: mov             x2, x1
    // 0x8b3590: r1 = Function '<anonymous closure>': static.
    //     0x8b3590: add             x1, PP, #0xf, lsl #12  ; [pp+0xf150] AnonymousClosure: static (0x8b365c), of [package:rxdart/src/subjects/behavior_subject.dart] BehaviorSubject<X0>
    //     0x8b3594: ldr             x1, [x1, #0x150]
    // 0x8b3598: r0 = AllocateClosure()
    //     0x8b3598: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b359c: ldur            x1, [fp, #-8]
    // 0x8b35a0: StoreField: r0->field_b = r1
    //     0x8b35a0: stur            w1, [x0, #0xb]
    // 0x8b35a4: stp             x0, x1, [SP]
    // 0x8b35a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8b35a8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8b35ac: r0 = defer()
    //     0x8b35ac: bl              #0x8b35fc  ; [package:rxdart/src/rx.dart] Rx::defer
    // 0x8b35b0: ldur            x1, [fp, #-8]
    // 0x8b35b4: stur            x0, [fp, #-8]
    // 0x8b35b8: r0 = BehaviorSubject()
    //     0x8b35b8: bl              #0x8b35f0  ; AllocateBehaviorSubjectStub -> BehaviorSubject<X0> (size=0x1c)
    // 0x8b35bc: ldur            x1, [fp, #-0x18]
    // 0x8b35c0: ArrayStore: r0[0] = r1  ; List_4
    //     0x8b35c0: stur            w1, [x0, #0x17]
    // 0x8b35c4: r1 = false
    //     0x8b35c4: add             x1, NULL, #0x30  ; false
    // 0x8b35c8: StoreField: r0->field_13 = r1
    //     0x8b35c8: stur            w1, [x0, #0x13]
    // 0x8b35cc: ldur            x1, [fp, #-0x10]
    // 0x8b35d0: StoreField: r0->field_f = r1
    //     0x8b35d0: stur            w1, [x0, #0xf]
    // 0x8b35d4: ldur            x1, [fp, #-8]
    // 0x8b35d8: StoreField: r0->field_b = r1
    //     0x8b35d8: stur            w1, [x0, #0xb]
    // 0x8b35dc: LeaveFrame
    //     0x8b35dc: mov             SP, fp
    //     0x8b35e0: ldp             fp, lr, [SP], #0x10
    // 0x8b35e4: ret
    //     0x8b35e4: ret             
    // 0x8b35e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b35e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b35ec: b               #0x8b3530
  }
  [closure] static Stream<Y0> <anonymous closure>(dynamic) {
    // ** addr: 0x8b365c, size: 0x234
    // 0x8b365c: EnterFrame
    //     0x8b365c: stp             fp, lr, [SP, #-0x10]!
    //     0x8b3660: mov             fp, SP
    // 0x8b3664: AllocStack(0x40)
    //     0x8b3664: sub             SP, SP, #0x40
    // 0x8b3668: SetupParameters()
    //     0x8b3668: ldr             x0, [fp, #0x10]
    //     0x8b366c: ldur            w1, [x0, #0x17]
    //     0x8b3670: add             x1, x1, HEAP, lsl #32
    // 0x8b3674: CheckStackOverflow
    //     0x8b3674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b3678: cmp             SP, x16
    //     0x8b367c: b.ls            #0x8b3888
    // 0x8b3680: LoadField: r2 = r0->field_b
    //     0x8b3680: ldur            w2, [x0, #0xb]
    // 0x8b3684: DecompressPointer r2
    //     0x8b3684: add             x2, x2, HEAP, lsl #32
    // 0x8b3688: stur            x2, [fp, #-0x18]
    // 0x8b368c: LoadField: r0 = r1->field_f
    //     0x8b368c: ldur            w0, [x1, #0xf]
    // 0x8b3690: DecompressPointer r0
    //     0x8b3690: add             x0, x0, HEAP, lsl #32
    // 0x8b3694: LoadField: r3 = r0->field_13
    //     0x8b3694: ldur            w3, [x0, #0x13]
    // 0x8b3698: DecompressPointer r3
    //     0x8b3698: add             x3, x3, HEAP, lsl #32
    // 0x8b369c: stur            x3, [fp, #-0x10]
    // 0x8b36a0: cmp             w3, NULL
    // 0x8b36a4: b.eq            #0x8b3768
    // 0x8b36a8: LoadField: r4 = r0->field_b
    //     0x8b36a8: ldur            w4, [x0, #0xb]
    // 0x8b36ac: DecompressPointer r4
    //     0x8b36ac: add             x4, x4, HEAP, lsl #32
    // 0x8b36b0: tbz             w4, #4, #0x8b3768
    // 0x8b36b4: LoadField: r0 = r1->field_13
    //     0x8b36b4: ldur            w0, [x1, #0x13]
    // 0x8b36b8: DecompressPointer r0
    //     0x8b36b8: add             x0, x0, HEAP, lsl #32
    // 0x8b36bc: stur            x0, [fp, #-8]
    // 0x8b36c0: LoadField: r1 = r0->field_7
    //     0x8b36c0: ldur            w1, [x0, #7]
    // 0x8b36c4: DecompressPointer r1
    //     0x8b36c4: add             x1, x1, HEAP, lsl #32
    // 0x8b36c8: r0 = _BroadcastStream()
    //     0x8b36c8: bl              #0x836570  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x8b36cc: mov             x4, x0
    // 0x8b36d0: ldur            x0, [fp, #-8]
    // 0x8b36d4: stur            x4, [fp, #-0x20]
    // 0x8b36d8: StoreField: r4->field_b = r0
    //     0x8b36d8: stur            w0, [x4, #0xb]
    // 0x8b36dc: ldur            x1, [fp, #-0x18]
    // 0x8b36e0: r2 = Null
    //     0x8b36e0: mov             x2, NULL
    // 0x8b36e4: r3 = <Y0, Y0>
    //     0x8b36e4: add             x3, PP, #0xf, lsl #12  ; [pp+0xf158] TypeArguments: <Y0, Y0>
    //     0x8b36e8: ldr             x3, [x3, #0x158]
    // 0x8b36ec: r0 = Null
    //     0x8b36ec: mov             x0, NULL
    // 0x8b36f0: cmp             x2, x0
    // 0x8b36f4: b.ne            #0x8b3700
    // 0x8b36f8: cmp             x1, x0
    // 0x8b36fc: b.eq            #0x8b370c
    // 0x8b3700: r30 = InstantiateTypeArgumentsStub
    //     0x8b3700: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8b3704: LoadField: r30 = r30->field_7
    //     0x8b3704: ldur            lr, [lr, #7]
    // 0x8b3708: blr             lr
    // 0x8b370c: mov             x1, x0
    // 0x8b3710: ldur            x0, [fp, #-0x10]
    // 0x8b3714: LoadField: r2 = r0->field_7
    //     0x8b3714: ldur            w2, [x0, #7]
    // 0x8b3718: DecompressPointer r2
    //     0x8b3718: add             x2, x2, HEAP, lsl #32
    // 0x8b371c: stur            x2, [fp, #-0x28]
    // 0x8b3720: LoadField: r3 = r0->field_b
    //     0x8b3720: ldur            w3, [x0, #0xb]
    // 0x8b3724: DecompressPointer r3
    //     0x8b3724: add             x3, x3, HEAP, lsl #32
    // 0x8b3728: stur            x3, [fp, #-8]
    // 0x8b372c: r0 = StartWithErrorStreamTransformer()
    //     0x8b372c: bl              #0x8b3890  ; AllocateStartWithErrorStreamTransformerStub -> StartWithErrorStreamTransformer<C1X0> (size=0x14)
    // 0x8b3730: mov             x1, x0
    // 0x8b3734: ldur            x0, [fp, #-0x28]
    // 0x8b3738: StoreField: r1->field_b = r0
    //     0x8b3738: stur            w0, [x1, #0xb]
    // 0x8b373c: ldur            x0, [fp, #-8]
    // 0x8b3740: StoreField: r1->field_f = r0
    //     0x8b3740: stur            w0, [x1, #0xf]
    // 0x8b3744: ldur            x16, [fp, #-0x18]
    // 0x8b3748: ldur            lr, [fp, #-0x20]
    // 0x8b374c: stp             lr, x16, [SP, #8]
    // 0x8b3750: str             x1, [SP]
    // 0x8b3754: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8b3754: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8b3758: r0 = transform()
    //     0x8b3758: bl              #0x7031c8  ; [dart:async] Stream::transform
    // 0x8b375c: LeaveFrame
    //     0x8b375c: mov             SP, fp
    //     0x8b3760: ldp             fp, lr, [SP], #0x10
    // 0x8b3764: ret
    //     0x8b3764: ret             
    // 0x8b3768: LoadField: r2 = r0->field_f
    //     0x8b3768: ldur            w2, [x0, #0xf]
    // 0x8b376c: DecompressPointer r2
    //     0x8b376c: add             x2, x2, HEAP, lsl #32
    // 0x8b3770: stur            x2, [fp, #-0x10]
    // 0x8b3774: r16 = Instance__Empty
    //     0x8b3774: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf58] Obj!_Empty@e0c0e1
    //     0x8b3778: ldr             x16, [x16, #0xf58]
    // 0x8b377c: cmp             w2, w16
    // 0x8b3780: b.eq            #0x8b385c
    // 0x8b3784: LoadField: r3 = r0->field_b
    //     0x8b3784: ldur            w3, [x0, #0xb]
    // 0x8b3788: DecompressPointer r3
    //     0x8b3788: add             x3, x3, HEAP, lsl #32
    // 0x8b378c: tbnz            w3, #4, #0x8b385c
    // 0x8b3790: LoadField: r0 = r1->field_13
    //     0x8b3790: ldur            w0, [x1, #0x13]
    // 0x8b3794: DecompressPointer r0
    //     0x8b3794: add             x0, x0, HEAP, lsl #32
    // 0x8b3798: stur            x0, [fp, #-8]
    // 0x8b379c: LoadField: r1 = r0->field_7
    //     0x8b379c: ldur            w1, [x0, #7]
    // 0x8b37a0: DecompressPointer r1
    //     0x8b37a0: add             x1, x1, HEAP, lsl #32
    // 0x8b37a4: r0 = _BroadcastStream()
    //     0x8b37a4: bl              #0x836570  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x8b37a8: mov             x4, x0
    // 0x8b37ac: ldur            x0, [fp, #-8]
    // 0x8b37b0: stur            x4, [fp, #-0x20]
    // 0x8b37b4: StoreField: r4->field_b = r0
    //     0x8b37b4: stur            w0, [x4, #0xb]
    // 0x8b37b8: ldur            x1, [fp, #-0x18]
    // 0x8b37bc: r2 = Null
    //     0x8b37bc: mov             x2, NULL
    // 0x8b37c0: r3 = <Y0, Y0>
    //     0x8b37c0: add             x3, PP, #0xf, lsl #12  ; [pp+0xf158] TypeArguments: <Y0, Y0>
    //     0x8b37c4: ldr             x3, [x3, #0x158]
    // 0x8b37c8: r0 = Null
    //     0x8b37c8: mov             x0, NULL
    // 0x8b37cc: cmp             x2, x0
    // 0x8b37d0: b.ne            #0x8b37dc
    // 0x8b37d4: cmp             x1, x0
    // 0x8b37d8: b.eq            #0x8b37e8
    // 0x8b37dc: r30 = InstantiateTypeArgumentsStub
    //     0x8b37dc: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8b37e0: LoadField: r30 = r30->field_7
    //     0x8b37e0: ldur            lr, [lr, #7]
    // 0x8b37e4: blr             lr
    // 0x8b37e8: mov             x3, x0
    // 0x8b37ec: ldur            x0, [fp, #-0x10]
    // 0x8b37f0: ldur            x1, [fp, #-0x18]
    // 0x8b37f4: r2 = Null
    //     0x8b37f4: mov             x2, NULL
    // 0x8b37f8: stur            x3, [fp, #-8]
    // 0x8b37fc: cmp             w1, NULL
    // 0x8b3800: b.eq            #0x8b3824
    // 0x8b3804: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x8b3804: ldur            w4, [x1, #0x17]
    // 0x8b3808: DecompressPointer r4
    //     0x8b3808: add             x4, x4, HEAP, lsl #32
    // 0x8b380c: r8 = Y0
    //     0x8b380c: add             x8, PP, #0xf, lsl #12  ; [pp+0xf160] TypeParameter: Y0
    //     0x8b3810: ldr             x8, [x8, #0x160]
    // 0x8b3814: LoadField: r9 = r4->field_7
    //     0x8b3814: ldur            x9, [x4, #7]
    // 0x8b3818: r3 = Null
    //     0x8b3818: add             x3, PP, #0xf, lsl #12  ; [pp+0xf168] Null
    //     0x8b381c: ldr             x3, [x3, #0x168]
    // 0x8b3820: blr             x9
    // 0x8b3824: ldur            x1, [fp, #-8]
    // 0x8b3828: r0 = StartWithStreamTransformer()
    //     0x8b3828: bl              #0x8a7914  ; AllocateStartWithStreamTransformerStub -> StartWithStreamTransformer<C1X0> (size=0x10)
    // 0x8b382c: mov             x1, x0
    // 0x8b3830: ldur            x0, [fp, #-0x10]
    // 0x8b3834: StoreField: r1->field_b = r0
    //     0x8b3834: stur            w0, [x1, #0xb]
    // 0x8b3838: ldur            x16, [fp, #-0x18]
    // 0x8b383c: ldur            lr, [fp, #-0x20]
    // 0x8b3840: stp             lr, x16, [SP, #8]
    // 0x8b3844: str             x1, [SP]
    // 0x8b3848: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8b3848: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8b384c: r0 = transform()
    //     0x8b384c: bl              #0x7031c8  ; [dart:async] Stream::transform
    // 0x8b3850: LeaveFrame
    //     0x8b3850: mov             SP, fp
    //     0x8b3854: ldp             fp, lr, [SP], #0x10
    // 0x8b3858: ret
    //     0x8b3858: ret             
    // 0x8b385c: LoadField: r0 = r1->field_13
    //     0x8b385c: ldur            w0, [x1, #0x13]
    // 0x8b3860: DecompressPointer r0
    //     0x8b3860: add             x0, x0, HEAP, lsl #32
    // 0x8b3864: stur            x0, [fp, #-8]
    // 0x8b3868: LoadField: r1 = r0->field_7
    //     0x8b3868: ldur            w1, [x0, #7]
    // 0x8b386c: DecompressPointer r1
    //     0x8b386c: add             x1, x1, HEAP, lsl #32
    // 0x8b3870: r0 = _BroadcastStream()
    //     0x8b3870: bl              #0x836570  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x8b3874: ldur            x1, [fp, #-8]
    // 0x8b3878: StoreField: r0->field_b = r1
    //     0x8b3878: stur            w1, [x0, #0xb]
    // 0x8b387c: LeaveFrame
    //     0x8b387c: mov             SP, fp
    //     0x8b3880: ldp             fp, lr, [SP], #0x10
    // 0x8b3884: ret
    //     0x8b3884: ret             
    // 0x8b3888: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b3888: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b388c: b               #0x8b3680
  }
  factory _ BehaviorSubject.seeded(/* No info */) {
    // ** addr: 0x8ba334, size: 0xd4
    // 0x8ba334: EnterFrame
    //     0x8ba334: stp             fp, lr, [SP, #-0x10]!
    //     0x8ba338: mov             fp, SP
    // 0x8ba33c: AllocStack(0x30)
    //     0x8ba33c: sub             SP, SP, #0x30
    // 0x8ba340: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8ba340: mov             x0, x1
    //     0x8ba344: stur            x1, [fp, #-8]
    //     0x8ba348: stur            x2, [fp, #-0x10]
    // 0x8ba34c: CheckStackOverflow
    //     0x8ba34c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ba350: cmp             SP, x16
    //     0x8ba354: b.ls            #0x8ba400
    // 0x8ba358: mov             x1, x0
    // 0x8ba35c: r0 = _AsyncBroadcastStreamController()
    //     0x8ba35c: bl              #0x835374  ; Allocate_AsyncBroadcastStreamControllerStub -> _AsyncBroadcastStreamController<X0> (size=0x2c)
    // 0x8ba360: stur            x0, [fp, #-0x18]
    // 0x8ba364: StoreField: r0->field_13 = rZR
    //     0x8ba364: stur            xzr, [x0, #0x13]
    // 0x8ba368: ldur            x1, [fp, #-8]
    // 0x8ba36c: r0 = _Wrapper()
    //     0x8ba36c: bl              #0x8b3650  ; Allocate_WrapperStub -> _Wrapper<X0> (size=0x18)
    // 0x8ba370: mov             x1, x0
    // 0x8ba374: ldur            x0, [fp, #-0x10]
    // 0x8ba378: stur            x1, [fp, #-0x20]
    // 0x8ba37c: StoreField: r1->field_f = r0
    //     0x8ba37c: stur            w0, [x1, #0xf]
    // 0x8ba380: r0 = true
    //     0x8ba380: add             x0, NULL, #0x20  ; true
    // 0x8ba384: StoreField: r1->field_b = r0
    //     0x8ba384: stur            w0, [x1, #0xb]
    // 0x8ba388: r1 = 2
    //     0x8ba388: movz            x1, #0x2
    // 0x8ba38c: r0 = AllocateContext()
    //     0x8ba38c: bl              #0xec126c  ; AllocateContextStub
    // 0x8ba390: mov             x1, x0
    // 0x8ba394: ldur            x0, [fp, #-0x20]
    // 0x8ba398: StoreField: r1->field_f = r0
    //     0x8ba398: stur            w0, [x1, #0xf]
    // 0x8ba39c: ldur            x3, [fp, #-0x18]
    // 0x8ba3a0: StoreField: r1->field_13 = r3
    //     0x8ba3a0: stur            w3, [x1, #0x13]
    // 0x8ba3a4: mov             x2, x1
    // 0x8ba3a8: r1 = Function '<anonymous closure>': static.
    //     0x8ba3a8: add             x1, PP, #0xf, lsl #12  ; [pp+0xf150] AnonymousClosure: static (0x8b365c), of [package:rxdart/src/subjects/behavior_subject.dart] BehaviorSubject<X0>
    //     0x8ba3ac: ldr             x1, [x1, #0x150]
    // 0x8ba3b0: r0 = AllocateClosure()
    //     0x8ba3b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ba3b4: ldur            x1, [fp, #-8]
    // 0x8ba3b8: StoreField: r0->field_b = r1
    //     0x8ba3b8: stur            w1, [x0, #0xb]
    // 0x8ba3bc: stp             x0, x1, [SP]
    // 0x8ba3c0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8ba3c0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8ba3c4: r0 = defer()
    //     0x8ba3c4: bl              #0x8b35fc  ; [package:rxdart/src/rx.dart] Rx::defer
    // 0x8ba3c8: ldur            x1, [fp, #-8]
    // 0x8ba3cc: stur            x0, [fp, #-8]
    // 0x8ba3d0: r0 = BehaviorSubject()
    //     0x8ba3d0: bl              #0x8b35f0  ; AllocateBehaviorSubjectStub -> BehaviorSubject<X0> (size=0x1c)
    // 0x8ba3d4: ldur            x1, [fp, #-0x20]
    // 0x8ba3d8: ArrayStore: r0[0] = r1  ; List_4
    //     0x8ba3d8: stur            w1, [x0, #0x17]
    // 0x8ba3dc: r1 = false
    //     0x8ba3dc: add             x1, NULL, #0x30  ; false
    // 0x8ba3e0: StoreField: r0->field_13 = r1
    //     0x8ba3e0: stur            w1, [x0, #0x13]
    // 0x8ba3e4: ldur            x1, [fp, #-0x18]
    // 0x8ba3e8: StoreField: r0->field_f = r1
    //     0x8ba3e8: stur            w1, [x0, #0xf]
    // 0x8ba3ec: ldur            x1, [fp, #-8]
    // 0x8ba3f0: StoreField: r0->field_b = r1
    //     0x8ba3f0: stur            w1, [x0, #0xb]
    // 0x8ba3f4: LeaveFrame
    //     0x8ba3f4: mov             SP, fp
    //     0x8ba3f8: ldp             fp, lr, [SP], #0x10
    // 0x8ba3fc: ret
    //     0x8ba3fc: ret             
    // 0x8ba400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ba400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ba404: b               #0x8ba358
  }
  _ onAddError(/* No info */) {
    // ** addr: 0xd1fea4, size: 0x3c
    // 0xd1fea4: EnterFrame
    //     0xd1fea4: stp             fp, lr, [SP, #-0x10]!
    //     0xd1fea8: mov             fp, SP
    // 0xd1feac: CheckStackOverflow
    //     0xd1feac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd1feb0: cmp             SP, x16
    //     0xd1feb4: b.ls            #0xd1fed8
    // 0xd1feb8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd1feb8: ldur            w0, [x1, #0x17]
    // 0xd1febc: DecompressPointer r0
    //     0xd1febc: add             x0, x0, HEAP, lsl #32
    // 0xd1fec0: mov             x1, x0
    // 0xd1fec4: r0 = setError()
    //     0xd1fec4: bl              #0xced1e4  ; [package:rxdart/src/subjects/behavior_subject.dart] _Wrapper::setError
    // 0xd1fec8: r0 = Null
    //     0xd1fec8: mov             x0, NULL
    // 0xd1fecc: LeaveFrame
    //     0xd1fecc: mov             SP, fp
    //     0xd1fed0: ldp             fp, lr, [SP], #0x10
    // 0xd1fed4: ret
    //     0xd1fed4: ret             
    // 0xd1fed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd1fed8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd1fedc: b               #0xd1feb8
  }
  get _ stream(/* No info */) {
    // ** addr: 0xd21688, size: 0x34
    // 0xd21688: EnterFrame
    //     0xd21688: stp             fp, lr, [SP, #-0x10]!
    //     0xd2168c: mov             fp, SP
    // 0xd21690: AllocStack(0x8)
    //     0xd21690: sub             SP, SP, #8
    // 0xd21694: SetupParameters(BehaviorSubject<X0> this /* r1 => r0, fp-0x8 */)
    //     0xd21694: mov             x0, x1
    //     0xd21698: stur            x1, [fp, #-8]
    // 0xd2169c: LoadField: r1 = r0->field_7
    //     0xd2169c: ldur            w1, [x0, #7]
    // 0xd216a0: DecompressPointer r1
    //     0xd216a0: add             x1, x1, HEAP, lsl #32
    // 0xd216a4: r0 = _BehaviorSubjectStream()
    //     0xd216a4: bl              #0x8b551c  ; Allocate_BehaviorSubjectStreamStub -> _BehaviorSubjectStream<X0> (size=0x10)
    // 0xd216a8: ldur            x1, [fp, #-8]
    // 0xd216ac: StoreField: r0->field_b = r1
    //     0xd216ac: stur            w1, [x0, #0xb]
    // 0xd216b0: LeaveFrame
    //     0xd216b0: mov             SP, fp
    //     0xd216b4: ldp             fp, lr, [SP], #0x10
    // 0xd216b8: ret
    //     0xd216b8: ret             
  }
}
