// lib: , url: package:rxdart/src/subjects/publish_subject.dart

// class id: 1051091, size: 0x8
class :: {
}

// class id: 6669, size: 0x18, field offset: 0x18
class PublishSubject<X0> extends Subject<X0> {

  factory _ PublishSubject(/* No info */) {
    // ** addr: 0x8b33bc, size: 0xc0
    // 0x8b33bc: EnterFrame
    //     0x8b33bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b33c0: mov             fp, SP
    // 0x8b33c4: AllocStack(0x30)
    //     0x8b33c4: sub             SP, SP, #0x30
    // 0x8b33c8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, {dynamic sync = false /* r1 */})
    //     0x8b33c8: mov             x0, x1
    //     0x8b33cc: stur            x1, [fp, #-8]
    //     0x8b33d0: ldur            w1, [x4, #0x13]
    //     0x8b33d4: ldur            w2, [x4, #0x1f]
    //     0x8b33d8: add             x2, x2, HEAP, lsl #32
    //     0x8b33dc: ldr             x16, [PP, #0x36a0]  ; [pp+0x36a0] "sync"
    //     0x8b33e0: cmp             w2, w16
    //     0x8b33e4: b.ne            #0x8b3400
    //     0x8b33e8: ldur            w2, [x4, #0x23]
    //     0x8b33ec: add             x2, x2, HEAP, lsl #32
    //     0x8b33f0: sub             w3, w1, w2
    //     0x8b33f4: add             x1, fp, w3, sxtw #2
    //     0x8b33f8: ldr             x1, [x1, #8]
    //     0x8b33fc: b               #0x8b3404
    //     0x8b3400: add             x1, NULL, #0x30  ; false
    // 0x8b3404: CheckStackOverflow
    //     0x8b3404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b3408: cmp             SP, x16
    //     0x8b340c: b.ls            #0x8b3474
    // 0x8b3410: stp             NULL, NULL, [SP, #8]
    // 0x8b3414: str             x1, [SP]
    // 0x8b3418: mov             x1, x0
    // 0x8b341c: r4 = const [0, 0x4, 0x3, 0x1, onCancel, 0x2, onListen, 0x1, sync, 0x3, null]
    //     0x8b341c: add             x4, PP, #0xf, lsl #12  ; [pp+0xf148] List(11) [0, 0x4, 0x3, 0x1, "onCancel", 0x2, "onListen", 0x1, "sync", 0x3, Null]
    //     0x8b3420: ldr             x4, [x4, #0x148]
    // 0x8b3424: r0 = StreamController.broadcast()
    //     0x8b3424: bl              #0x83522c  ; [dart:async] StreamController::StreamController.broadcast
    // 0x8b3428: stur            x0, [fp, #-0x10]
    // 0x8b342c: LoadField: r1 = r0->field_7
    //     0x8b342c: ldur            w1, [x0, #7]
    // 0x8b3430: DecompressPointer r1
    //     0x8b3430: add             x1, x1, HEAP, lsl #32
    // 0x8b3434: r0 = _BroadcastStream()
    //     0x8b3434: bl              #0x836570  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x8b3438: mov             x2, x0
    // 0x8b343c: ldur            x0, [fp, #-0x10]
    // 0x8b3440: stur            x2, [fp, #-0x18]
    // 0x8b3444: StoreField: r2->field_b = r0
    //     0x8b3444: stur            w0, [x2, #0xb]
    // 0x8b3448: ldur            x1, [fp, #-8]
    // 0x8b344c: r0 = PublishSubject()
    //     0x8b344c: bl              #0x8b347c  ; AllocatePublishSubjectStub -> PublishSubject<X0> (size=0x18)
    // 0x8b3450: r1 = false
    //     0x8b3450: add             x1, NULL, #0x30  ; false
    // 0x8b3454: StoreField: r0->field_13 = r1
    //     0x8b3454: stur            w1, [x0, #0x13]
    // 0x8b3458: ldur            x1, [fp, #-0x10]
    // 0x8b345c: StoreField: r0->field_f = r1
    //     0x8b345c: stur            w1, [x0, #0xf]
    // 0x8b3460: ldur            x1, [fp, #-0x18]
    // 0x8b3464: StoreField: r0->field_b = r1
    //     0x8b3464: stur            w1, [x0, #0xb]
    // 0x8b3468: LeaveFrame
    //     0x8b3468: mov             SP, fp
    //     0x8b346c: ldp             fp, lr, [SP], #0x10
    // 0x8b3470: ret
    //     0x8b3470: ret             
    // 0x8b3474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b3474: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b3478: b               #0x8b3410
  }
}
