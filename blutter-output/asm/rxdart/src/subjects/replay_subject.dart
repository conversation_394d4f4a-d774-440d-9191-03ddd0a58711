// lib: , url: package:rxdart/src/subjects/replay_subject.dart

// class id: 1051092, size: 0x8
class :: {
}

// class id: 521, size: 0x14, field offset: 0x8
class _Event<X0> extends Object {

  factory _ _Event.data(/* No info */) {
    // ** addr: 0xaa94a0, size: 0x2c
    // 0xaa94a0: EnterFrame
    //     0xaa94a0: stp             fp, lr, [SP, #-0x10]!
    //     0xaa94a4: mov             fp, SP
    // 0xaa94a8: AllocStack(0x8)
    //     0xaa94a8: sub             SP, SP, #8
    // 0xaa94ac: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xaa94ac: mov             x0, x1
    //     0xaa94b0: stur            x2, [fp, #-8]
    // 0xaa94b4: r0 = _Event()
    //     0xaa94b4: bl              #0xaa94cc  ; Allocate_EventStub -> _Event<X0> (size=0x14)
    // 0xaa94b8: ldur            x1, [fp, #-8]
    // 0xaa94bc: StoreField: r0->field_b = r1
    //     0xaa94bc: stur            w1, [x0, #0xb]
    // 0xaa94c0: LeaveFrame
    //     0xaa94c0: mov             SP, fp
    //     0xaa94c4: ldp             fp, lr, [SP], #0x10
    // 0xaa94c8: ret
    //     0xaa94c8: ret             
  }
  factory _ _Event.error(/* No info */) {
    // ** addr: 0xd1ffc0, size: 0x38
    // 0xd1ffc0: EnterFrame
    //     0xd1ffc0: stp             fp, lr, [SP, #-0x10]!
    //     0xd1ffc4: mov             fp, SP
    // 0xd1ffc8: AllocStack(0x8)
    //     0xd1ffc8: sub             SP, SP, #8
    // 0xd1ffcc: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xd1ffcc: mov             x0, x1
    //     0xd1ffd0: stur            x2, [fp, #-8]
    // 0xd1ffd4: r0 = _Event()
    //     0xd1ffd4: bl              #0xaa94cc  ; Allocate_EventStub -> _Event<X0> (size=0x14)
    // 0xd1ffd8: r1 = Instance__Empty
    //     0xd1ffd8: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf58] Obj!_Empty@e0c0e1
    //     0xd1ffdc: ldr             x1, [x1, #0xf58]
    // 0xd1ffe0: StoreField: r0->field_b = r1
    //     0xd1ffe0: stur            w1, [x0, #0xb]
    // 0xd1ffe4: ldur            x1, [fp, #-8]
    // 0xd1ffe8: StoreField: r0->field_f = r1
    //     0xd1ffe8: stur            w1, [x0, #0xf]
    // 0xd1ffec: LeaveFrame
    //     0xd1ffec: mov             SP, fp
    //     0xd1fff0: ldp             fp, lr, [SP], #0x10
    // 0xd1fff4: ret
    //     0xd1fff4: ret             
  }
}

// class id: 6630, size: 0x10, field offset: 0xc
class _ReplaySubjectStream<X0> extends Stream<X0>
    implements ReplayStream<X0> {

  _ ==(/* No info */) {
    // ** addr: 0xd2d844, size: 0x74
    // 0xd2d844: ldr             x1, [SP]
    // 0xd2d848: cmp             w1, NULL
    // 0xd2d84c: b.ne            #0xd2d858
    // 0xd2d850: r0 = false
    //     0xd2d850: add             x0, NULL, #0x30  ; false
    // 0xd2d854: ret
    //     0xd2d854: ret             
    // 0xd2d858: ldr             x2, [SP, #8]
    // 0xd2d85c: cmp             w2, w1
    // 0xd2d860: b.ne            #0xd2d86c
    // 0xd2d864: r0 = true
    //     0xd2d864: add             x0, NULL, #0x20  ; true
    // 0xd2d868: ret
    //     0xd2d868: ret             
    // 0xd2d86c: r3 = 60
    //     0xd2d86c: movz            x3, #0x3c
    // 0xd2d870: branchIfSmi(r1, 0xd2d87c)
    //     0xd2d870: tbz             w1, #0, #0xd2d87c
    // 0xd2d874: r3 = LoadClassIdInstr(r1)
    //     0xd2d874: ldur            x3, [x1, #-1]
    //     0xd2d878: ubfx            x3, x3, #0xc, #0x14
    // 0xd2d87c: r17 = 6630
    //     0xd2d87c: movz            x17, #0x19e6
    // 0xd2d880: cmp             x3, x17
    // 0xd2d884: b.ne            #0xd2d8b0
    // 0xd2d888: LoadField: r3 = r1->field_b
    //     0xd2d888: ldur            w3, [x1, #0xb]
    // 0xd2d88c: DecompressPointer r3
    //     0xd2d88c: add             x3, x3, HEAP, lsl #32
    // 0xd2d890: LoadField: r1 = r2->field_b
    //     0xd2d890: ldur            w1, [x2, #0xb]
    // 0xd2d894: DecompressPointer r1
    //     0xd2d894: add             x1, x1, HEAP, lsl #32
    // 0xd2d898: cmp             w3, w1
    // 0xd2d89c: r16 = true
    //     0xd2d89c: add             x16, NULL, #0x20  ; true
    // 0xd2d8a0: r17 = false
    //     0xd2d8a0: add             x17, NULL, #0x30  ; false
    // 0xd2d8a4: csel            x2, x16, x17, eq
    // 0xd2d8a8: mov             x0, x2
    // 0xd2d8ac: b               #0xd2d8b4
    // 0xd2d8b0: r0 = false
    //     0xd2d8b0: add             x0, NULL, #0x30  ; false
    // 0xd2d8b4: ret
    //     0xd2d8b4: ret             
  }
}

// class id: 6668, size: 0x20, field offset: 0x18
class ReplaySubject<X0> extends Subject<X0>
    implements ReplayStream<X0> {

  _ onAddError(/* No info */) {
    // ** addr: 0xd1fee0, size: 0xe0
    // 0xd1fee0: EnterFrame
    //     0xd1fee0: stp             fp, lr, [SP, #-0x10]!
    //     0xd1fee4: mov             fp, SP
    // 0xd1fee8: AllocStack(0x38)
    //     0xd1fee8: sub             SP, SP, #0x38
    // 0xd1feec: SetupParameters(ReplaySubject<X0> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xd1feec: mov             x4, x1
    //     0xd1fef0: stur            x1, [fp, #-0x10]
    //     0xd1fef4: stur            x2, [fp, #-0x18]
    //     0xd1fef8: stur            x3, [fp, #-0x20]
    // 0xd1fefc: CheckStackOverflow
    //     0xd1fefc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd1ff00: cmp             SP, x16
    //     0xd1ff04: b.ls            #0xd1ffb8
    // 0xd1ff08: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xd1ff08: ldur            w5, [x4, #0x17]
    // 0xd1ff0c: DecompressPointer r5
    //     0xd1ff0c: add             x5, x5, HEAP, lsl #32
    // 0xd1ff10: stur            x5, [fp, #-8]
    // 0xd1ff14: ArrayLoad: r0 = r5[0]  ; List_8
    //     0xd1ff14: ldur            x0, [x5, #0x17]
    // 0xd1ff18: LoadField: r1 = r5->field_f
    //     0xd1ff18: ldur            x1, [x5, #0xf]
    // 0xd1ff1c: sub             x6, x0, x1
    // 0xd1ff20: LoadField: r0 = r5->field_b
    //     0xd1ff20: ldur            w0, [x5, #0xb]
    // 0xd1ff24: DecompressPointer r0
    //     0xd1ff24: add             x0, x0, HEAP, lsl #32
    // 0xd1ff28: LoadField: r1 = r0->field_b
    //     0xd1ff28: ldur            w1, [x0, #0xb]
    // 0xd1ff2c: r0 = LoadInt32Instr(r1)
    //     0xd1ff2c: sbfx            x0, x1, #1, #0x1f
    // 0xd1ff30: sub             x1, x0, #1
    // 0xd1ff34: and             x7, x6, x1
    // 0xd1ff38: r0 = BoxInt64Instr(r7)
    //     0xd1ff38: sbfiz           x0, x7, #1, #0x1f
    //     0xd1ff3c: cmp             x7, x0, asr #1
    //     0xd1ff40: b.eq            #0xd1ff4c
    //     0xd1ff44: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd1ff48: stur            x7, [x0, #7]
    // 0xd1ff4c: stp             NULL, x0, [SP]
    // 0xd1ff50: r0 = ==()
    //     0xd1ff50: bl              #0xd81764  ; [dart:core] _IntegerImplementation::==
    // 0xd1ff54: tbnz            w0, #4, #0xd1ff60
    // 0xd1ff58: ldur            x1, [fp, #-8]
    // 0xd1ff5c: r0 = removeFirst()
    //     0xd1ff5c: bl              #0x61a918  ; [dart:collection] ListQueue::removeFirst
    // 0xd1ff60: ldur            x2, [fp, #-0x10]
    // 0xd1ff64: ldur            x1, [fp, #-0x18]
    // 0xd1ff68: ldur            x0, [fp, #-0x20]
    // 0xd1ff6c: LoadField: r3 = r2->field_7
    //     0xd1ff6c: ldur            w3, [x2, #7]
    // 0xd1ff70: DecompressPointer r3
    //     0xd1ff70: add             x3, x3, HEAP, lsl #32
    // 0xd1ff74: stur            x3, [fp, #-0x28]
    // 0xd1ff78: r0 = ErrorAndStackTrace()
    //     0xd1ff78: bl              #0xced248  ; AllocateErrorAndStackTraceStub -> ErrorAndStackTrace (size=0x10)
    // 0xd1ff7c: mov             x1, x0
    // 0xd1ff80: ldur            x0, [fp, #-0x18]
    // 0xd1ff84: StoreField: r1->field_7 = r0
    //     0xd1ff84: stur            w0, [x1, #7]
    // 0xd1ff88: ldur            x0, [fp, #-0x20]
    // 0xd1ff8c: StoreField: r1->field_b = r0
    //     0xd1ff8c: stur            w0, [x1, #0xb]
    // 0xd1ff90: mov             x2, x1
    // 0xd1ff94: ldur            x1, [fp, #-0x28]
    // 0xd1ff98: r0 = _Event.error()
    //     0xd1ff98: bl              #0xd1ffc0  ; [package:rxdart/src/subjects/replay_subject.dart] _Event::_Event.error
    // 0xd1ff9c: ldur            x1, [fp, #-8]
    // 0xd1ffa0: mov             x2, x0
    // 0xd1ffa4: r0 = add()
    //     0xd1ffa4: bl              #0x64c4d8  ; [dart:collection] ListQueue::add
    // 0xd1ffa8: r0 = Null
    //     0xd1ffa8: mov             x0, NULL
    // 0xd1ffac: LeaveFrame
    //     0xd1ffac: mov             SP, fp
    //     0xd1ffb0: ldp             fp, lr, [SP], #0x10
    // 0xd1ffb4: ret
    //     0xd1ffb4: ret             
    // 0xd1ffb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd1ffb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd1ffbc: b               #0xd1ff08
  }
  get _ stream(/* No info */) {
    // ** addr: 0xd216f0, size: 0x34
    // 0xd216f0: EnterFrame
    //     0xd216f0: stp             fp, lr, [SP, #-0x10]!
    //     0xd216f4: mov             fp, SP
    // 0xd216f8: AllocStack(0x8)
    //     0xd216f8: sub             SP, SP, #8
    // 0xd216fc: SetupParameters(ReplaySubject<X0> this /* r1 => r0, fp-0x8 */)
    //     0xd216fc: mov             x0, x1
    //     0xd21700: stur            x1, [fp, #-8]
    // 0xd21704: LoadField: r1 = r0->field_7
    //     0xd21704: ldur            w1, [x0, #7]
    // 0xd21708: DecompressPointer r1
    //     0xd21708: add             x1, x1, HEAP, lsl #32
    // 0xd2170c: r0 = _ReplaySubjectStream()
    //     0xd2170c: bl              #0xd21724  ; Allocate_ReplaySubjectStreamStub -> _ReplaySubjectStream<X0> (size=0x10)
    // 0xd21710: ldur            x1, [fp, #-8]
    // 0xd21714: StoreField: r0->field_b = r1
    //     0xd21714: stur            w1, [x0, #0xb]
    // 0xd21718: LeaveFrame
    //     0xd21718: mov             SP, fp
    //     0xd2171c: ldp             fp, lr, [SP], #0x10
    // 0xd21720: ret
    //     0xd21720: ret             
  }
  factory _ ReplaySubject(/* No info */) {
    // ** addr: 0xe5ee70, size: 0x110
    // 0xe5ee70: EnterFrame
    //     0xe5ee70: stp             fp, lr, [SP, #-0x10]!
    //     0xe5ee74: mov             fp, SP
    // 0xe5ee78: AllocStack(0x30)
    //     0xe5ee78: sub             SP, SP, #0x30
    // 0xe5ee7c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xe5ee7c: stur            x1, [fp, #-8]
    // 0xe5ee80: CheckStackOverflow
    //     0xe5ee80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5ee84: cmp             SP, x16
    //     0xe5ee88: b.ls            #0xe5ef78
    // 0xe5ee8c: r1 = 3
    //     0xe5ee8c: movz            x1, #0x3
    // 0xe5ee90: r0 = AllocateContext()
    //     0xe5ee90: bl              #0xec126c  ; AllocateContextStub
    // 0xe5ee94: mov             x2, x0
    // 0xe5ee98: ldur            x0, [fp, #-8]
    // 0xe5ee9c: stur            x2, [fp, #-0x10]
    // 0xe5eea0: StoreField: r2->field_f = r0
    //     0xe5eea0: stur            w0, [x2, #0xf]
    // 0xe5eea4: mov             x1, x0
    // 0xe5eea8: r0 = _AsyncBroadcastStreamController()
    //     0xe5eea8: bl              #0x835374  ; Allocate_AsyncBroadcastStreamControllerStub -> _AsyncBroadcastStreamController<X0> (size=0x2c)
    // 0xe5eeac: stur            x0, [fp, #-0x18]
    // 0xe5eeb0: StoreField: r0->field_13 = rZR
    //     0xe5eeb0: stur            xzr, [x0, #0x13]
    // 0xe5eeb4: ldur            x4, [fp, #-0x10]
    // 0xe5eeb8: StoreField: r4->field_13 = r0
    //     0xe5eeb8: stur            w0, [x4, #0x13]
    // 0xe5eebc: ldur            x2, [fp, #-8]
    // 0xe5eec0: r1 = Null
    //     0xe5eec0: mov             x1, NULL
    // 0xe5eec4: r3 = <_Event<X0>>
    //     0xe5eec4: add             x3, PP, #0x38, lsl #12  ; [pp+0x386d0] TypeArguments: <_Event<X0>>
    //     0xe5eec8: ldr             x3, [x3, #0x6d0]
    // 0xe5eecc: r30 = InstantiateTypeArgumentsStub
    //     0xe5eecc: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe5eed0: LoadField: r30 = r30->field_7
    //     0xe5eed0: ldur            lr, [lr, #7]
    // 0xe5eed4: blr             lr
    // 0xe5eed8: mov             x1, x0
    // 0xe5eedc: r0 = ListQueue()
    //     0xe5eedc: bl              #0x61b3b8  ; AllocateListQueueStub -> ListQueue<X0> (size=0x28)
    // 0xe5eee0: mov             x1, x0
    // 0xe5eee4: stur            x0, [fp, #-8]
    // 0xe5eee8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe5eee8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe5eeec: r0 = ListQueue()
    //     0xe5eeec: bl              #0x61b248  ; [dart:collection] ListQueue::ListQueue
    // 0xe5eef0: ldur            x0, [fp, #-8]
    // 0xe5eef4: ldur            x2, [fp, #-0x10]
    // 0xe5eef8: ArrayStore: r2[0] = r0  ; List_4
    //     0xe5eef8: stur            w0, [x2, #0x17]
    //     0xe5eefc: ldurb           w16, [x2, #-1]
    //     0xe5ef00: ldurb           w17, [x0, #-1]
    //     0xe5ef04: and             x16, x17, x16, lsr #2
    //     0xe5ef08: tst             x16, HEAP, lsr #32
    //     0xe5ef0c: b.eq            #0xe5ef14
    //     0xe5ef10: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xe5ef14: LoadField: r0 = r2->field_f
    //     0xe5ef14: ldur            w0, [x2, #0xf]
    // 0xe5ef18: DecompressPointer r0
    //     0xe5ef18: add             x0, x0, HEAP, lsl #32
    // 0xe5ef1c: mov             x3, x0
    // 0xe5ef20: stur            x0, [fp, #-0x20]
    // 0xe5ef24: r1 = Function '<anonymous closure>': static.
    //     0xe5ef24: add             x1, PP, #0x38, lsl #12  ; [pp+0x386d8] AnonymousClosure: static (0xe5ef8c), in [package:rxdart/src/subjects/replay_subject.dart] ReplaySubject::ReplaySubject (0xe5ee70)
    //     0xe5ef28: ldr             x1, [x1, #0x6d8]
    // 0xe5ef2c: r0 = AllocateClosureTA()
    //     0xe5ef2c: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xe5ef30: ldur            x16, [fp, #-0x20]
    // 0xe5ef34: stp             x0, x16, [SP]
    // 0xe5ef38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe5ef38: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe5ef3c: r0 = defer()
    //     0xe5ef3c: bl              #0x8b35fc  ; [package:rxdart/src/rx.dart] Rx::defer
    // 0xe5ef40: ldur            x1, [fp, #-0x20]
    // 0xe5ef44: stur            x0, [fp, #-0x10]
    // 0xe5ef48: r0 = ReplaySubject()
    //     0xe5ef48: bl              #0xe5ef80  ; AllocateReplaySubjectStub -> ReplaySubject<X0> (size=0x20)
    // 0xe5ef4c: ldur            x1, [fp, #-8]
    // 0xe5ef50: ArrayStore: r0[0] = r1  ; List_4
    //     0xe5ef50: stur            w1, [x0, #0x17]
    // 0xe5ef54: r1 = false
    //     0xe5ef54: add             x1, NULL, #0x30  ; false
    // 0xe5ef58: StoreField: r0->field_13 = r1
    //     0xe5ef58: stur            w1, [x0, #0x13]
    // 0xe5ef5c: ldur            x1, [fp, #-0x18]
    // 0xe5ef60: StoreField: r0->field_f = r1
    //     0xe5ef60: stur            w1, [x0, #0xf]
    // 0xe5ef64: ldur            x1, [fp, #-0x10]
    // 0xe5ef68: StoreField: r0->field_b = r1
    //     0xe5ef68: stur            w1, [x0, #0xb]
    // 0xe5ef6c: LeaveFrame
    //     0xe5ef6c: mov             SP, fp
    //     0xe5ef70: ldp             fp, lr, [SP], #0x10
    // 0xe5ef74: ret
    //     0xe5ef74: ret             
    // 0xe5ef78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5ef78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5ef7c: b               #0xe5ee8c
  }
  [closure] static Stream<X0> <anonymous closure>(dynamic) {
    // ** addr: 0xe5ef8c, size: 0xfc
    // 0xe5ef8c: EnterFrame
    //     0xe5ef8c: stp             fp, lr, [SP, #-0x10]!
    //     0xe5ef90: mov             fp, SP
    // 0xe5ef94: AllocStack(0x48)
    //     0xe5ef94: sub             SP, SP, #0x48
    // 0xe5ef98: SetupParameters()
    //     0xe5ef98: ldr             x0, [fp, #0x10]
    //     0xe5ef9c: ldur            w4, [x0, #0x17]
    //     0xe5efa0: add             x4, x4, HEAP, lsl #32
    //     0xe5efa4: stur            x4, [fp, #-8]
    // 0xe5efa8: CheckStackOverflow
    //     0xe5efa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5efac: cmp             SP, x16
    //     0xe5efb0: b.ls            #0xe5f080
    // 0xe5efb4: LoadField: r2 = r4->field_f
    //     0xe5efb4: ldur            w2, [x4, #0xf]
    // 0xe5efb8: DecompressPointer r2
    //     0xe5efb8: add             x2, x2, HEAP, lsl #32
    // 0xe5efbc: r1 = Null
    //     0xe5efbc: mov             x1, NULL
    // 0xe5efc0: r3 = <Stream<X0>>
    //     0xe5efc0: add             x3, PP, #0x38, lsl #12  ; [pp+0x386e0] TypeArguments: <Stream<X0>>
    //     0xe5efc4: ldr             x3, [x3, #0x6e0]
    // 0xe5efc8: r30 = InstantiateTypeArgumentsStub
    //     0xe5efc8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe5efcc: LoadField: r30 = r30->field_7
    //     0xe5efcc: ldur            lr, [lr, #7]
    // 0xe5efd0: blr             lr
    // 0xe5efd4: ldur            x2, [fp, #-8]
    // 0xe5efd8: stur            x0, [fp, #-0x10]
    // 0xe5efdc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xe5efdc: ldur            w1, [x2, #0x17]
    // 0xe5efe0: DecompressPointer r1
    //     0xe5efe0: add             x1, x1, HEAP, lsl #32
    // 0xe5efe4: r16 = false
    //     0xe5efe4: add             x16, NULL, #0x30  ; false
    // 0xe5efe8: str             x16, [SP]
    // 0xe5efec: r4 = const [0, 0x2, 0x1, 0x1, growable, 0x1, null]
    //     0xe5efec: ldr             x4, [PP, #0x1050]  ; [pp+0x1050] List(7) [0, 0x2, 0x1, 0x1, "growable", 0x1, Null]
    // 0xe5eff0: r0 = toList()
    //     0xe5eff0: bl              #0x863118  ; [dart:collection] ListQueue::toList
    // 0xe5eff4: stur            x0, [fp, #-0x18]
    // 0xe5eff8: LoadField: r1 = r0->field_7
    //     0xe5eff8: ldur            w1, [x0, #7]
    // 0xe5effc: DecompressPointer r1
    //     0xe5effc: add             x1, x1, HEAP, lsl #32
    // 0xe5f000: r0 = ReversedListIterable()
    //     0xe5f000: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xe5f004: mov             x2, x0
    // 0xe5f008: ldur            x0, [fp, #-0x18]
    // 0xe5f00c: stur            x2, [fp, #-0x20]
    // 0xe5f010: StoreField: r2->field_b = r0
    //     0xe5f010: stur            w0, [x2, #0xb]
    // 0xe5f014: ldur            x0, [fp, #-8]
    // 0xe5f018: LoadField: r3 = r0->field_13
    //     0xe5f018: ldur            w3, [x0, #0x13]
    // 0xe5f01c: DecompressPointer r3
    //     0xe5f01c: add             x3, x3, HEAP, lsl #32
    // 0xe5f020: stur            x3, [fp, #-0x18]
    // 0xe5f024: LoadField: r1 = r3->field_7
    //     0xe5f024: ldur            w1, [x3, #7]
    // 0xe5f028: DecompressPointer r1
    //     0xe5f028: add             x1, x1, HEAP, lsl #32
    // 0xe5f02c: r0 = _BroadcastStream()
    //     0xe5f02c: bl              #0x836570  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0xe5f030: mov             x4, x0
    // 0xe5f034: ldur            x0, [fp, #-0x18]
    // 0xe5f038: stur            x4, [fp, #-0x28]
    // 0xe5f03c: StoreField: r4->field_b = r0
    //     0xe5f03c: stur            w0, [x4, #0xb]
    // 0xe5f040: ldur            x2, [fp, #-8]
    // 0xe5f044: LoadField: r3 = r2->field_f
    //     0xe5f044: ldur            w3, [x2, #0xf]
    // 0xe5f048: DecompressPointer r3
    //     0xe5f048: add             x3, x3, HEAP, lsl #32
    // 0xe5f04c: r1 = Function '<anonymous closure>': static.
    //     0xe5f04c: add             x1, PP, #0x38, lsl #12  ; [pp+0x386e8] AnonymousClosure: static (0xe5f088), in [package:rxdart/src/subjects/replay_subject.dart] ReplaySubject::ReplaySubject (0xe5ee70)
    //     0xe5f050: ldr             x1, [x1, #0x6e8]
    // 0xe5f054: r0 = AllocateClosureTA()
    //     0xe5f054: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xe5f058: ldur            x16, [fp, #-0x10]
    // 0xe5f05c: ldur            lr, [fp, #-0x20]
    // 0xe5f060: stp             lr, x16, [SP, #0x10]
    // 0xe5f064: ldur            x16, [fp, #-0x28]
    // 0xe5f068: stp             x0, x16, [SP]
    // 0xe5f06c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe5f06c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe5f070: r0 = fold()
    //     0xe5f070: bl              #0x7d8444  ; [dart:_internal] ListIterable::fold
    // 0xe5f074: LeaveFrame
    //     0xe5f074: mov             SP, fp
    //     0xe5f078: ldp             fp, lr, [SP], #0x10
    // 0xe5f07c: ret
    //     0xe5f07c: ret             
    // 0xe5f080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5f080: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5f084: b               #0xe5efb4
  }
  [closure] static Stream<X0> <anonymous closure>(dynamic, Stream<X0>, _Event<X0>) {
    // ** addr: 0xe5f088, size: 0x180
    // 0xe5f088: EnterFrame
    //     0xe5f088: stp             fp, lr, [SP, #-0x10]!
    //     0xe5f08c: mov             fp, SP
    // 0xe5f090: AllocStack(0x38)
    //     0xe5f090: sub             SP, SP, #0x38
    // 0xe5f094: SetupParameters()
    //     0xe5f094: ldr             x0, [fp, #0x20]
    //     0xe5f098: ldur            w1, [x0, #0x17]
    //     0xe5f09c: add             x1, x1, HEAP, lsl #32
    // 0xe5f0a0: CheckStackOverflow
    //     0xe5f0a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5f0a4: cmp             SP, x16
    //     0xe5f0a8: b.ls            #0xe5f200
    // 0xe5f0ac: ldr             x0, [fp, #0x10]
    // 0xe5f0b0: LoadField: r4 = r0->field_f
    //     0xe5f0b0: ldur            w4, [x0, #0xf]
    // 0xe5f0b4: DecompressPointer r4
    //     0xe5f0b4: add             x4, x4, HEAP, lsl #32
    // 0xe5f0b8: stur            x4, [fp, #-0x10]
    // 0xe5f0bc: cmp             w4, NULL
    // 0xe5f0c0: b.eq            #0xe5f150
    // 0xe5f0c4: LoadField: r0 = r1->field_f
    //     0xe5f0c4: ldur            w0, [x1, #0xf]
    // 0xe5f0c8: DecompressPointer r0
    //     0xe5f0c8: add             x0, x0, HEAP, lsl #32
    // 0xe5f0cc: mov             x2, x0
    // 0xe5f0d0: stur            x0, [fp, #-8]
    // 0xe5f0d4: r1 = Null
    //     0xe5f0d4: mov             x1, NULL
    // 0xe5f0d8: r3 = <X0, X0>
    //     0xe5f0d8: ldr             x3, [PP, #0x1970]  ; [pp+0x1970] TypeArguments: <X0, X0>
    // 0xe5f0dc: r0 = Null
    //     0xe5f0dc: mov             x0, NULL
    // 0xe5f0e0: cmp             x2, x0
    // 0xe5f0e4: b.eq            #0xe5f0f4
    // 0xe5f0e8: r30 = InstantiateTypeArgumentsStub
    //     0xe5f0e8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe5f0ec: LoadField: r30 = r30->field_7
    //     0xe5f0ec: ldur            lr, [lr, #7]
    // 0xe5f0f0: blr             lr
    // 0xe5f0f4: mov             x1, x0
    // 0xe5f0f8: ldur            x0, [fp, #-0x10]
    // 0xe5f0fc: LoadField: r2 = r0->field_7
    //     0xe5f0fc: ldur            w2, [x0, #7]
    // 0xe5f100: DecompressPointer r2
    //     0xe5f100: add             x2, x2, HEAP, lsl #32
    // 0xe5f104: stur            x2, [fp, #-0x20]
    // 0xe5f108: LoadField: r3 = r0->field_b
    //     0xe5f108: ldur            w3, [x0, #0xb]
    // 0xe5f10c: DecompressPointer r3
    //     0xe5f10c: add             x3, x3, HEAP, lsl #32
    // 0xe5f110: stur            x3, [fp, #-0x18]
    // 0xe5f114: r0 = StartWithErrorStreamTransformer()
    //     0xe5f114: bl              #0x8b3890  ; AllocateStartWithErrorStreamTransformerStub -> StartWithErrorStreamTransformer<C1X0> (size=0x14)
    // 0xe5f118: mov             x1, x0
    // 0xe5f11c: ldur            x0, [fp, #-0x20]
    // 0xe5f120: StoreField: r1->field_b = r0
    //     0xe5f120: stur            w0, [x1, #0xb]
    // 0xe5f124: ldur            x0, [fp, #-0x18]
    // 0xe5f128: StoreField: r1->field_f = r0
    //     0xe5f128: stur            w0, [x1, #0xf]
    // 0xe5f12c: ldur            x16, [fp, #-8]
    // 0xe5f130: ldr             lr, [fp, #0x18]
    // 0xe5f134: stp             lr, x16, [SP, #8]
    // 0xe5f138: str             x1, [SP]
    // 0xe5f13c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe5f13c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe5f140: r0 = transform()
    //     0xe5f140: bl              #0x7031c8  ; [dart:async] Stream::transform
    // 0xe5f144: LeaveFrame
    //     0xe5f144: mov             SP, fp
    //     0xe5f148: ldp             fp, lr, [SP], #0x10
    // 0xe5f14c: ret
    //     0xe5f14c: ret             
    // 0xe5f150: LoadField: r4 = r1->field_f
    //     0xe5f150: ldur            w4, [x1, #0xf]
    // 0xe5f154: DecompressPointer r4
    //     0xe5f154: add             x4, x4, HEAP, lsl #32
    // 0xe5f158: mov             x2, x4
    // 0xe5f15c: stur            x4, [fp, #-8]
    // 0xe5f160: r1 = Null
    //     0xe5f160: mov             x1, NULL
    // 0xe5f164: r3 = <X0, X0>
    //     0xe5f164: ldr             x3, [PP, #0x1970]  ; [pp+0x1970] TypeArguments: <X0, X0>
    // 0xe5f168: r0 = Null
    //     0xe5f168: mov             x0, NULL
    // 0xe5f16c: cmp             x2, x0
    // 0xe5f170: b.eq            #0xe5f180
    // 0xe5f174: r30 = InstantiateTypeArgumentsStub
    //     0xe5f174: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe5f178: LoadField: r30 = r30->field_7
    //     0xe5f178: ldur            lr, [lr, #7]
    // 0xe5f17c: blr             lr
    // 0xe5f180: mov             x3, x0
    // 0xe5f184: ldr             x0, [fp, #0x10]
    // 0xe5f188: stur            x3, [fp, #-0x18]
    // 0xe5f18c: LoadField: r4 = r0->field_b
    //     0xe5f18c: ldur            w4, [x0, #0xb]
    // 0xe5f190: DecompressPointer r4
    //     0xe5f190: add             x4, x4, HEAP, lsl #32
    // 0xe5f194: mov             x0, x4
    // 0xe5f198: ldur            x2, [fp, #-8]
    // 0xe5f19c: stur            x4, [fp, #-0x10]
    // 0xe5f1a0: r1 = Null
    //     0xe5f1a0: mov             x1, NULL
    // 0xe5f1a4: cmp             w2, NULL
    // 0xe5f1a8: b.eq            #0xe5f1c8
    // 0xe5f1ac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe5f1ac: ldur            w4, [x2, #0x17]
    // 0xe5f1b0: DecompressPointer r4
    //     0xe5f1b0: add             x4, x4, HEAP, lsl #32
    // 0xe5f1b4: r8 = X0
    //     0xe5f1b4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xe5f1b8: LoadField: r9 = r4->field_7
    //     0xe5f1b8: ldur            x9, [x4, #7]
    // 0xe5f1bc: r3 = Null
    //     0xe5f1bc: add             x3, PP, #0x38, lsl #12  ; [pp+0x386f0] Null
    //     0xe5f1c0: ldr             x3, [x3, #0x6f0]
    // 0xe5f1c4: blr             x9
    // 0xe5f1c8: ldur            x1, [fp, #-0x18]
    // 0xe5f1cc: r0 = StartWithStreamTransformer()
    //     0xe5f1cc: bl              #0x8a7914  ; AllocateStartWithStreamTransformerStub -> StartWithStreamTransformer<C1X0> (size=0x10)
    // 0xe5f1d0: mov             x1, x0
    // 0xe5f1d4: ldur            x0, [fp, #-0x10]
    // 0xe5f1d8: StoreField: r1->field_b = r0
    //     0xe5f1d8: stur            w0, [x1, #0xb]
    // 0xe5f1dc: ldur            x16, [fp, #-8]
    // 0xe5f1e0: ldr             lr, [fp, #0x18]
    // 0xe5f1e4: stp             lr, x16, [SP, #8]
    // 0xe5f1e8: str             x1, [SP]
    // 0xe5f1ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe5f1ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe5f1f0: r0 = transform()
    //     0xe5f1f0: bl              #0x7031c8  ; [dart:async] Stream::transform
    // 0xe5f1f4: LeaveFrame
    //     0xe5f1f4: mov             SP, fp
    //     0xe5f1f8: ldp             fp, lr, [SP], #0x10
    // 0xe5f1fc: ret
    //     0xe5f1fc: ret             
    // 0xe5f200: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5f200: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5f204: b               #0xe5f0ac
  }
}
