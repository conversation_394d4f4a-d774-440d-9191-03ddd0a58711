// lib: , url: package:rxdart/src/subjects/subject.dart

// class id: 1051093, size: 0x8
class :: {
}

// class id: 6629, size: 0x10, field offset: 0xc
class _SubjectStream<X0> extends Stream<X0> {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbdd3a4, size: 0x54
    // 0xbdd3a4: EnterFrame
    //     0xbdd3a4: stp             fp, lr, [SP, #-0x10]!
    //     0xbdd3a8: mov             fp, SP
    // 0xbdd3ac: AllocStack(0x8)
    //     0xbdd3ac: sub             SP, SP, #8
    // 0xbdd3b0: CheckStackOverflow
    //     0xbdd3b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdd3b4: cmp             SP, x16
    //     0xbdd3b8: b.ls            #0xbdd3f0
    // 0xbdd3bc: ldr             x0, [fp, #0x10]
    // 0xbdd3c0: LoadField: r1 = r0->field_b
    //     0xbdd3c0: ldur            w1, [x0, #0xb]
    // 0xbdd3c4: DecompressPointer r1
    //     0xbdd3c4: add             x1, x1, HEAP, lsl #32
    // 0xbdd3c8: str             x1, [SP]
    // 0xbdd3cc: r0 = _getHash()
    //     0xbdd3cc: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0xbdd3d0: r1 = LoadInt32Instr(r0)
    //     0xbdd3d0: sbfx            x1, x0, #1, #0x1f
    // 0xbdd3d4: r16 = 892482866
    //     0xbdd3d4: movz            x16, #0x3532
    //     0xbdd3d8: movk            x16, #0x3532, lsl #16
    // 0xbdd3dc: eor             x2, x1, x16
    // 0xbdd3e0: lsl             x0, x2, #1
    // 0xbdd3e4: LeaveFrame
    //     0xbdd3e4: mov             SP, fp
    //     0xbdd3e8: ldp             fp, lr, [SP], #0x10
    // 0xbdd3ec: ret
    //     0xbdd3ec: ret             
    // 0xbdd3f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdd3f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdd3f4: b               #0xbdd3bc
  }
  _ listen(/* No info */) {
    // ** addr: 0xd110cc, size: 0x134
    // 0xd110cc: EnterFrame
    //     0xd110cc: stp             fp, lr, [SP, #-0x10]!
    //     0xd110d0: mov             fp, SP
    // 0xd110d4: AllocStack(0x18)
    //     0xd110d4: sub             SP, SP, #0x18
    // 0xd110d8: SetupParameters({dynamic cancelOnError = Null /* r5 */, dynamic onDone = Null /* r6 */, dynamic onError = Null /* r0 */})
    //     0xd110d8: ldur            w0, [x4, #0x13]
    //     0xd110dc: ldur            w3, [x4, #0x1f]
    //     0xd110e0: add             x3, x3, HEAP, lsl #32
    //     0xd110e4: add             x16, PP, #0xc, lsl #12  ; [pp+0xc128] "cancelOnError"
    //     0xd110e8: ldr             x16, [x16, #0x128]
    //     0xd110ec: cmp             w3, w16
    //     0xd110f0: b.ne            #0xd11114
    //     0xd110f4: ldur            w3, [x4, #0x23]
    //     0xd110f8: add             x3, x3, HEAP, lsl #32
    //     0xd110fc: sub             w5, w0, w3
    //     0xd11100: add             x3, fp, w5, sxtw #2
    //     0xd11104: ldr             x3, [x3, #8]
    //     0xd11108: mov             x5, x3
    //     0xd1110c: movz            x3, #0x1
    //     0xd11110: b               #0xd1111c
    //     0xd11114: mov             x5, NULL
    //     0xd11118: movz            x3, #0
    //     0xd1111c: lsl             x6, x3, #1
    //     0xd11120: lsl             w7, w6, #1
    //     0xd11124: add             w8, w7, #8
    //     0xd11128: add             x16, x4, w8, sxtw #1
    //     0xd1112c: ldur            w9, [x16, #0xf]
    //     0xd11130: add             x9, x9, HEAP, lsl #32
    //     0xd11134: add             x16, PP, #0xc, lsl #12  ; [pp+0xc130] "onDone"
    //     0xd11138: ldr             x16, [x16, #0x130]
    //     0xd1113c: cmp             w9, w16
    //     0xd11140: b.ne            #0xd11174
    //     0xd11144: add             w3, w7, #0xa
    //     0xd11148: add             x16, x4, w3, sxtw #1
    //     0xd1114c: ldur            w7, [x16, #0xf]
    //     0xd11150: add             x7, x7, HEAP, lsl #32
    //     0xd11154: sub             w3, w0, w7
    //     0xd11158: add             x7, fp, w3, sxtw #2
    //     0xd1115c: ldr             x7, [x7, #8]
    //     0xd11160: add             w3, w6, #2
    //     0xd11164: sbfx            x6, x3, #1, #0x1f
    //     0xd11168: mov             x3, x6
    //     0xd1116c: mov             x6, x7
    //     0xd11170: b               #0xd11178
    //     0xd11174: mov             x6, NULL
    //     0xd11178: lsl             x7, x3, #1
    //     0xd1117c: lsl             w3, w7, #1
    //     0xd11180: add             w7, w3, #8
    //     0xd11184: add             x16, x4, w7, sxtw #1
    //     0xd11188: ldur            w8, [x16, #0xf]
    //     0xd1118c: add             x8, x8, HEAP, lsl #32
    //     0xd11190: ldr             x16, [PP, #0xb48]  ; [pp+0xb48] "onError"
    //     0xd11194: cmp             w8, w16
    //     0xd11198: b.ne            #0xd111bc
    //     0xd1119c: add             w7, w3, #0xa
    //     0xd111a0: add             x16, x4, w7, sxtw #1
    //     0xd111a4: ldur            w3, [x16, #0xf]
    //     0xd111a8: add             x3, x3, HEAP, lsl #32
    //     0xd111ac: sub             w4, w0, w3
    //     0xd111b0: add             x0, fp, w4, sxtw #2
    //     0xd111b4: ldr             x0, [x0, #8]
    //     0xd111b8: b               #0xd111c0
    //     0xd111bc: mov             x0, NULL
    // 0xd111c0: CheckStackOverflow
    //     0xd111c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd111c4: cmp             SP, x16
    //     0xd111c8: b.ls            #0xd111f8
    // 0xd111cc: LoadField: r3 = r1->field_b
    //     0xd111cc: ldur            w3, [x1, #0xb]
    // 0xd111d0: DecompressPointer r3
    //     0xd111d0: add             x3, x3, HEAP, lsl #32
    // 0xd111d4: stp             x6, x0, [SP, #8]
    // 0xd111d8: str             x5, [SP]
    // 0xd111dc: mov             x1, x3
    // 0xd111e0: r4 = const [0, 0x5, 0x3, 0x2, cancelOnError, 0x4, onDone, 0x3, onError, 0x2, null]
    //     0xd111e0: add             x4, PP, #0xd, lsl #12  ; [pp+0xdf78] List(11) [0, 0x5, 0x3, 0x2, "cancelOnError", 0x4, "onDone", 0x3, "onError", 0x2, Null]
    //     0xd111e4: ldr             x4, [x4, #0xf78]
    // 0xd111e8: r0 = listen()
    //     0xd111e8: bl              #0xd0b238  ; [dart:async] StreamView::listen
    // 0xd111ec: LeaveFrame
    //     0xd111ec: mov             SP, fp
    //     0xd111f0: ldp             fp, lr, [SP], #0x10
    // 0xd111f4: ret
    //     0xd111f4: ret             
    // 0xd111f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd111f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd111fc: b               #0xd111cc
  }
  _ ==(/* No info */) {
    // ** addr: 0xd2d8b8, size: 0x74
    // 0xd2d8b8: ldr             x1, [SP]
    // 0xd2d8bc: cmp             w1, NULL
    // 0xd2d8c0: b.ne            #0xd2d8cc
    // 0xd2d8c4: r0 = false
    //     0xd2d8c4: add             x0, NULL, #0x30  ; false
    // 0xd2d8c8: ret
    //     0xd2d8c8: ret             
    // 0xd2d8cc: ldr             x2, [SP, #8]
    // 0xd2d8d0: cmp             w2, w1
    // 0xd2d8d4: b.ne            #0xd2d8e0
    // 0xd2d8d8: r0 = true
    //     0xd2d8d8: add             x0, NULL, #0x20  ; true
    // 0xd2d8dc: ret
    //     0xd2d8dc: ret             
    // 0xd2d8e0: r3 = 60
    //     0xd2d8e0: movz            x3, #0x3c
    // 0xd2d8e4: branchIfSmi(r1, 0xd2d8f0)
    //     0xd2d8e4: tbz             w1, #0, #0xd2d8f0
    // 0xd2d8e8: r3 = LoadClassIdInstr(r1)
    //     0xd2d8e8: ldur            x3, [x1, #-1]
    //     0xd2d8ec: ubfx            x3, x3, #0xc, #0x14
    // 0xd2d8f0: r17 = 6629
    //     0xd2d8f0: movz            x17, #0x19e5
    // 0xd2d8f4: cmp             x3, x17
    // 0xd2d8f8: b.ne            #0xd2d924
    // 0xd2d8fc: LoadField: r3 = r1->field_b
    //     0xd2d8fc: ldur            w3, [x1, #0xb]
    // 0xd2d900: DecompressPointer r3
    //     0xd2d900: add             x3, x3, HEAP, lsl #32
    // 0xd2d904: LoadField: r1 = r2->field_b
    //     0xd2d904: ldur            w1, [x2, #0xb]
    // 0xd2d908: DecompressPointer r1
    //     0xd2d908: add             x1, x1, HEAP, lsl #32
    // 0xd2d90c: cmp             w3, w1
    // 0xd2d910: r16 = true
    //     0xd2d910: add             x16, NULL, #0x20  ; true
    // 0xd2d914: r17 = false
    //     0xd2d914: add             x17, NULL, #0x30  ; false
    // 0xd2d918: csel            x2, x16, x17, eq
    // 0xd2d91c: mov             x0, x2
    // 0xd2d920: b               #0xd2d928
    // 0xd2d924: r0 = false
    //     0xd2d924: add             x0, NULL, #0x30  ; false
    // 0xd2d928: ret
    //     0xd2d928: ret             
  }
}

// class id: 6667, size: 0x18, field offset: 0x10
abstract class Subject<X0> extends StreamView<X0>
    implements StreamController<X0> {

  dynamic add(dynamic) {
    // ** addr: 0x68a1e4, size: 0x24
    // 0x68a1e4: EnterFrame
    //     0x68a1e4: stp             fp, lr, [SP, #-0x10]!
    //     0x68a1e8: mov             fp, SP
    // 0x68a1ec: ldr             x2, [fp, #0x10]
    // 0x68a1f0: r1 = Function 'add':.
    //     0x68a1f0: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c8e0] AnonymousClosure: (0x68a2ec), in [package:rxdart/src/subjects/subject.dart] Subject::add (0xc7588c)
    //     0x68a1f4: ldr             x1, [x1, #0x8e0]
    // 0x68a1f8: r0 = AllocateClosure()
    //     0x68a1f8: bl              #0xec1630  ; AllocateClosureStub
    // 0x68a1fc: LeaveFrame
    //     0x68a1fc: mov             SP, fp
    //     0x68a200: ldp             fp, lr, [SP], #0x10
    // 0x68a204: ret
    //     0x68a204: ret             
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x68a2ec, size: 0x3c
    // 0x68a2ec: EnterFrame
    //     0x68a2ec: stp             fp, lr, [SP, #-0x10]!
    //     0x68a2f0: mov             fp, SP
    // 0x68a2f4: ldr             x0, [fp, #0x18]
    // 0x68a2f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68a2f8: ldur            w1, [x0, #0x17]
    // 0x68a2fc: DecompressPointer r1
    //     0x68a2fc: add             x1, x1, HEAP, lsl #32
    // 0x68a300: CheckStackOverflow
    //     0x68a300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68a304: cmp             SP, x16
    //     0x68a308: b.ls            #0x68a320
    // 0x68a30c: ldr             x2, [fp, #0x10]
    // 0x68a310: r0 = add()
    //     0x68a310: bl              #0xc7588c  ; [package:rxdart/src/subjects/subject.dart] Subject::add
    // 0x68a314: LeaveFrame
    //     0x68a314: mov             SP, fp
    //     0x68a318: ldp             fp, lr, [SP], #0x10
    // 0x68a31c: ret
    //     0x68a31c: ret             
    // 0x68a320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68a320: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68a324: b               #0x68a30c
  }
  [closure] void addError(dynamic, Object, [StackTrace?]) {
    // ** addr: 0x8bc990, size: 0x78
    // 0x8bc990: EnterFrame
    //     0x8bc990: stp             fp, lr, [SP, #-0x10]!
    //     0x8bc994: mov             fp, SP
    // 0x8bc998: AllocStack(0x8)
    //     0x8bc998: sub             SP, SP, #8
    // 0x8bc99c: SetupParameters(Subject<X0> this /* r0 */, dynamic _ /* r2 */, [dynamic _ = Null /* r1 */])
    //     0x8bc99c: ldur            w0, [x4, #0x13]
    //     0x8bc9a0: sub             x1, x0, #4
    //     0x8bc9a4: add             x0, fp, w1, sxtw #2
    //     0x8bc9a8: ldr             x0, [x0, #0x18]
    //     0x8bc9ac: add             x2, fp, w1, sxtw #2
    //     0x8bc9b0: ldr             x2, [x2, #0x10]
    //     0x8bc9b4: cmp             w1, #2
    //     0x8bc9b8: b.lt            #0x8bc9cc
    //     0x8bc9bc: add             x3, fp, w1, sxtw #2
    //     0x8bc9c0: ldr             x3, [x3, #8]
    //     0x8bc9c4: mov             x1, x3
    //     0x8bc9c8: b               #0x8bc9d0
    //     0x8bc9cc: mov             x1, NULL
    //     0x8bc9d0: ldur            w3, [x0, #0x17]
    //     0x8bc9d4: add             x3, x3, HEAP, lsl #32
    // 0x8bc9d8: CheckStackOverflow
    //     0x8bc9d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bc9dc: cmp             SP, x16
    //     0x8bc9e0: b.ls            #0x8bca00
    // 0x8bc9e4: str             x1, [SP]
    // 0x8bc9e8: mov             x1, x3
    // 0x8bc9ec: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8bc9ec: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8bc9f0: r0 = addError()
    //     0x8bc9f0: bl              #0xced000  ; [package:rxdart/src/subjects/subject.dart] Subject::addError
    // 0x8bc9f4: LeaveFrame
    //     0x8bc9f4: mov             SP, fp
    //     0x8bc9f8: ldp             fp, lr, [SP], #0x10
    // 0x8bc9fc: ret
    //     0x8bc9fc: ret             
    // 0x8bca00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bca00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bca04: b               #0x8bc9e4
  }
  _ _add(/* No info */) {
    // ** addr: 0xaa9290, size: 0x144
    // 0xaa9290: EnterFrame
    //     0xaa9290: stp             fp, lr, [SP, #-0x10]!
    //     0xaa9294: mov             fp, SP
    // 0xaa9298: AllocStack(0x38)
    //     0xaa9298: sub             SP, SP, #0x38
    // 0xaa929c: SetupParameters(Subject<X0> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xaa929c: mov             x4, x1
    //     0xaa92a0: mov             x3, x2
    //     0xaa92a4: stur            x1, [fp, #-0x10]
    //     0xaa92a8: stur            x2, [fp, #-0x18]
    // 0xaa92ac: CheckStackOverflow
    //     0xaa92ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa92b0: cmp             SP, x16
    //     0xaa92b4: b.ls            #0xaa93cc
    // 0xaa92b8: LoadField: r5 = r4->field_7
    //     0xaa92b8: ldur            w5, [x4, #7]
    // 0xaa92bc: DecompressPointer r5
    //     0xaa92bc: add             x5, x5, HEAP, lsl #32
    // 0xaa92c0: mov             x0, x3
    // 0xaa92c4: mov             x2, x5
    // 0xaa92c8: stur            x5, [fp, #-8]
    // 0xaa92cc: r1 = Null
    //     0xaa92cc: mov             x1, NULL
    // 0xaa92d0: cmp             w2, NULL
    // 0xaa92d4: b.eq            #0xaa92f4
    // 0xaa92d8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xaa92d8: ldur            w4, [x2, #0x17]
    // 0xaa92dc: DecompressPointer r4
    //     0xaa92dc: add             x4, x4, HEAP, lsl #32
    // 0xaa92e0: r8 = X0
    //     0xaa92e0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xaa92e4: LoadField: r9 = r4->field_7
    //     0xaa92e4: ldur            x9, [x4, #7]
    // 0xaa92e8: r3 = Null
    //     0xaa92e8: add             x3, PP, #0xe, lsl #12  ; [pp+0xef10] Null
    //     0xaa92ec: ldr             x3, [x3, #0xf10]
    // 0xaa92f0: blr             x9
    // 0xaa92f4: ldur            x0, [fp, #-0x10]
    // 0xaa92f8: LoadField: r2 = r0->field_f
    //     0xaa92f8: ldur            w2, [x0, #0xf]
    // 0xaa92fc: DecompressPointer r2
    //     0xaa92fc: add             x2, x2, HEAP, lsl #32
    // 0xaa9300: stur            x2, [fp, #-0x28]
    // 0xaa9304: LoadField: r1 = r2->field_13
    //     0xaa9304: ldur            x1, [x2, #0x13]
    // 0xaa9308: tbnz            w1, #2, #0xaa93b0
    // 0xaa930c: r1 = LoadClassIdInstr(r0)
    //     0xaa930c: ldur            x1, [x0, #-1]
    //     0xaa9310: ubfx            x1, x1, #0xc, #0x14
    // 0xaa9314: r17 = 6668
    //     0xaa9314: movz            x17, #0x1a0c
    // 0xaa9318: cmp             x1, x17
    // 0xaa931c: b.ne            #0xaa9394
    // 0xaa9320: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xaa9320: ldur            w3, [x0, #0x17]
    // 0xaa9324: DecompressPointer r3
    //     0xaa9324: add             x3, x3, HEAP, lsl #32
    // 0xaa9328: stur            x3, [fp, #-0x20]
    // 0xaa932c: ArrayLoad: r0 = r3[0]  ; List_8
    //     0xaa932c: ldur            x0, [x3, #0x17]
    // 0xaa9330: LoadField: r1 = r3->field_f
    //     0xaa9330: ldur            x1, [x3, #0xf]
    // 0xaa9334: sub             x4, x0, x1
    // 0xaa9338: LoadField: r0 = r3->field_b
    //     0xaa9338: ldur            w0, [x3, #0xb]
    // 0xaa933c: DecompressPointer r0
    //     0xaa933c: add             x0, x0, HEAP, lsl #32
    // 0xaa9340: LoadField: r1 = r0->field_b
    //     0xaa9340: ldur            w1, [x0, #0xb]
    // 0xaa9344: r0 = LoadInt32Instr(r1)
    //     0xaa9344: sbfx            x0, x1, #1, #0x1f
    // 0xaa9348: sub             x1, x0, #1
    // 0xaa934c: and             x5, x4, x1
    // 0xaa9350: r0 = BoxInt64Instr(r5)
    //     0xaa9350: sbfiz           x0, x5, #1, #0x1f
    //     0xaa9354: cmp             x5, x0, asr #1
    //     0xaa9358: b.eq            #0xaa9364
    //     0xaa935c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaa9360: stur            x5, [x0, #7]
    // 0xaa9364: stp             NULL, x0, [SP]
    // 0xaa9368: r0 = ==()
    //     0xaa9368: bl              #0xd81764  ; [dart:core] _IntegerImplementation::==
    // 0xaa936c: tbnz            w0, #4, #0xaa9378
    // 0xaa9370: ldur            x1, [fp, #-0x20]
    // 0xaa9374: r0 = removeFirst()
    //     0xaa9374: bl              #0x61a918  ; [dart:collection] ListQueue::removeFirst
    // 0xaa9378: ldur            x1, [fp, #-8]
    // 0xaa937c: ldur            x2, [fp, #-0x18]
    // 0xaa9380: r0 = _Event.data()
    //     0xaa9380: bl              #0xaa94a0  ; [package:rxdart/src/subjects/replay_subject.dart] _Event::_Event.data
    // 0xaa9384: ldur            x1, [fp, #-0x20]
    // 0xaa9388: mov             x2, x0
    // 0xaa938c: r0 = add()
    //     0xaa938c: bl              #0x64c4d8  ; [dart:collection] ListQueue::add
    // 0xaa9390: b               #0xaa93b0
    // 0xaa9394: r17 = 6669
    //     0xaa9394: movz            x17, #0x1a0d
    // 0xaa9398: cmp             x1, x17
    // 0xaa939c: b.eq            #0xaa93b0
    // 0xaa93a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa93a0: ldur            w1, [x0, #0x17]
    // 0xaa93a4: DecompressPointer r1
    //     0xaa93a4: add             x1, x1, HEAP, lsl #32
    // 0xaa93a8: ldur            x2, [fp, #-0x18]
    // 0xaa93ac: r0 = setValue()
    //     0xaa93ac: bl              #0xaa9410  ; [package:rxdart/src/subjects/behavior_subject.dart] _Wrapper::setValue
    // 0xaa93b0: ldur            x1, [fp, #-0x28]
    // 0xaa93b4: ldur            x2, [fp, #-0x18]
    // 0xaa93b8: r0 = add()
    //     0xaa93b8: bl              #0xc7541c  ; [dart:async] _BroadcastStreamController::add
    // 0xaa93bc: r0 = Null
    //     0xaa93bc: mov             x0, NULL
    // 0xaa93c0: LeaveFrame
    //     0xaa93c0: mov             SP, fp
    //     0xaa93c4: ldp             fp, lr, [SP], #0x10
    // 0xaa93c8: ret
    //     0xaa93c8: ret             
    // 0xaa93cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa93cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa93d0: b               #0xaa92b8
  }
  [closure] void _add(dynamic, Object?) {
    // ** addr: 0xaa93d4, size: 0x3c
    // 0xaa93d4: EnterFrame
    //     0xaa93d4: stp             fp, lr, [SP, #-0x10]!
    //     0xaa93d8: mov             fp, SP
    // 0xaa93dc: ldr             x0, [fp, #0x18]
    // 0xaa93e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa93e0: ldur            w1, [x0, #0x17]
    // 0xaa93e4: DecompressPointer r1
    //     0xaa93e4: add             x1, x1, HEAP, lsl #32
    // 0xaa93e8: CheckStackOverflow
    //     0xaa93e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa93ec: cmp             SP, x16
    //     0xaa93f0: b.ls            #0xaa9408
    // 0xaa93f4: ldr             x2, [fp, #0x10]
    // 0xaa93f8: r0 = _add()
    //     0xaa93f8: bl              #0xaa9290  ; [package:rxdart/src/subjects/subject.dart] Subject::_add
    // 0xaa93fc: LeaveFrame
    //     0xaa93fc: mov             SP, fp
    //     0xaa9400: ldp             fp, lr, [SP], #0x10
    // 0xaa9404: ret
    //     0xaa9404: ret             
    // 0xaa9408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa9408: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa940c: b               #0xaa93f4
  }
  _ close(/* No info */) {
    // ** addr: 0xc4ffc4, size: 0x64
    // 0xc4ffc4: EnterFrame
    //     0xc4ffc4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ffc8: mov             fp, SP
    // 0xc4ffcc: CheckStackOverflow
    //     0xc4ffcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ffd0: cmp             SP, x16
    //     0xc4ffd4: b.ls            #0xc50020
    // 0xc4ffd8: LoadField: r0 = r1->field_13
    //     0xc4ffd8: ldur            w0, [x1, #0x13]
    // 0xc4ffdc: DecompressPointer r0
    //     0xc4ffdc: add             x0, x0, HEAP, lsl #32
    // 0xc4ffe0: tbz             w0, #4, #0xc50000
    // 0xc4ffe4: LoadField: r0 = r1->field_f
    //     0xc4ffe4: ldur            w0, [x1, #0xf]
    // 0xc4ffe8: DecompressPointer r0
    //     0xc4ffe8: add             x0, x0, HEAP, lsl #32
    // 0xc4ffec: mov             x1, x0
    // 0xc4fff0: r0 = close()
    //     0xc4fff0: bl              #0xc4f240  ; [dart:async] _BroadcastStreamController::close
    // 0xc4fff4: LeaveFrame
    //     0xc4fff4: mov             SP, fp
    //     0xc4fff8: ldp             fp, lr, [SP], #0x10
    // 0xc4fffc: ret
    //     0xc4fffc: ret             
    // 0xc50000: r0 = StateError()
    //     0xc50000: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xc50004: mov             x1, x0
    // 0xc50008: r0 = "You cannot close the subject while items are being added from addStream"
    //     0xc50008: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c8e8] "You cannot close the subject while items are being added from addStream"
    //     0xc5000c: ldr             x0, [x0, #0x8e8]
    // 0xc50010: StoreField: r1->field_b = r0
    //     0xc50010: stur            w0, [x1, #0xb]
    // 0xc50014: mov             x0, x1
    // 0xc50018: r0 = Throw()
    //     0xc50018: bl              #0xec04b8  ; ThrowStub
    // 0xc5001c: brk             #0
    // 0xc50020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc50020: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc50024: b               #0xc4ffd8
  }
  _ add(/* No info */) {
    // ** addr: 0xc7588c, size: 0xac
    // 0xc7588c: EnterFrame
    //     0xc7588c: stp             fp, lr, [SP, #-0x10]!
    //     0xc75890: mov             fp, SP
    // 0xc75894: AllocStack(0x10)
    //     0xc75894: sub             SP, SP, #0x10
    // 0xc75898: SetupParameters(Subject<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xc75898: mov             x4, x1
    //     0xc7589c: mov             x3, x2
    //     0xc758a0: stur            x1, [fp, #-8]
    //     0xc758a4: stur            x2, [fp, #-0x10]
    // 0xc758a8: CheckStackOverflow
    //     0xc758a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc758ac: cmp             SP, x16
    //     0xc758b0: b.ls            #0xc75930
    // 0xc758b4: LoadField: r2 = r4->field_7
    //     0xc758b4: ldur            w2, [x4, #7]
    // 0xc758b8: DecompressPointer r2
    //     0xc758b8: add             x2, x2, HEAP, lsl #32
    // 0xc758bc: mov             x0, x3
    // 0xc758c0: r1 = Null
    //     0xc758c0: mov             x1, NULL
    // 0xc758c4: cmp             w2, NULL
    // 0xc758c8: b.eq            #0xc758e8
    // 0xc758cc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc758cc: ldur            w4, [x2, #0x17]
    // 0xc758d0: DecompressPointer r4
    //     0xc758d0: add             x4, x4, HEAP, lsl #32
    // 0xc758d4: r8 = X0
    //     0xc758d4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xc758d8: LoadField: r9 = r4->field_7
    //     0xc758d8: ldur            x9, [x4, #7]
    // 0xc758dc: r3 = Null
    //     0xc758dc: add             x3, PP, #0xe, lsl #12  ; [pp+0xef70] Null
    //     0xc758e0: ldr             x3, [x3, #0xf70]
    // 0xc758e4: blr             x9
    // 0xc758e8: ldur            x1, [fp, #-8]
    // 0xc758ec: LoadField: r0 = r1->field_13
    //     0xc758ec: ldur            w0, [x1, #0x13]
    // 0xc758f0: DecompressPointer r0
    //     0xc758f0: add             x0, x0, HEAP, lsl #32
    // 0xc758f4: tbz             w0, #4, #0xc75910
    // 0xc758f8: ldur            x2, [fp, #-0x10]
    // 0xc758fc: r0 = _add()
    //     0xc758fc: bl              #0xaa9290  ; [package:rxdart/src/subjects/subject.dart] Subject::_add
    // 0xc75900: r0 = Null
    //     0xc75900: mov             x0, NULL
    // 0xc75904: LeaveFrame
    //     0xc75904: mov             SP, fp
    //     0xc75908: ldp             fp, lr, [SP], #0x10
    // 0xc7590c: ret
    //     0xc7590c: ret             
    // 0xc75910: r0 = StateError()
    //     0xc75910: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xc75914: mov             x1, x0
    // 0xc75918: r0 = "You cannot add items while items are being added from addStream"
    //     0xc75918: add             x0, PP, #0xe, lsl #12  ; [pp+0xef08] "You cannot add items while items are being added from addStream"
    //     0xc7591c: ldr             x0, [x0, #0xf08]
    // 0xc75920: StoreField: r1->field_b = r0
    //     0xc75920: stur            w0, [x1, #0xb]
    // 0xc75924: mov             x0, x1
    // 0xc75928: r0 = Throw()
    //     0xc75928: bl              #0xec04b8  ; ThrowStub
    // 0xc7592c: brk             #0
    // 0xc75930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc75930: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc75934: b               #0xc758b4
  }
  dynamic addError(dynamic) {
    // ** addr: 0xcecfac, size: 0x24
    // 0xcecfac: EnterFrame
    //     0xcecfac: stp             fp, lr, [SP, #-0x10]!
    //     0xcecfb0: mov             fp, SP
    // 0xcecfb4: ldr             x2, [fp, #0x10]
    // 0xcecfb8: r1 = Function 'addError':.
    //     0xcecfb8: add             x1, PP, #0xe, lsl #12  ; [pp+0xe798] AnonymousClosure: (0x8bc990), in [package:rxdart/src/subjects/subject.dart] Subject::addError (0xced000)
    //     0xcecfbc: ldr             x1, [x1, #0x798]
    // 0xcecfc0: r0 = AllocateClosure()
    //     0xcecfc0: bl              #0xec1630  ; AllocateClosureStub
    // 0xcecfc4: LeaveFrame
    //     0xcecfc4: mov             SP, fp
    //     0xcecfc8: ldp             fp, lr, [SP], #0x10
    // 0xcecfcc: ret
    //     0xcecfcc: ret             
  }
  get _ isClosed(/* No info */) {
    // ** addr: 0xcecfd0, size: 0x30
    // 0xcecfd0: r2 = 4
    //     0xcecfd0: movz            x2, #0x4
    // 0xcecfd4: LoadField: r3 = r1->field_f
    //     0xcecfd4: ldur            w3, [x1, #0xf]
    // 0xcecfd8: DecompressPointer r3
    //     0xcecfd8: add             x3, x3, HEAP, lsl #32
    // 0xcecfdc: LoadField: r1 = r3->field_13
    //     0xcecfdc: ldur            x1, [x3, #0x13]
    // 0xcecfe0: ubfx            x1, x1, #0, #0x20
    // 0xcecfe4: and             x3, x1, x2
    // 0xcecfe8: ubfx            x3, x3, #0, #0x20
    // 0xcecfec: cbnz            x3, #0xcecff8
    // 0xcecff0: r0 = false
    //     0xcecff0: add             x0, NULL, #0x30  ; false
    // 0xcecff4: b               #0xcecffc
    // 0xcecff8: r0 = true
    //     0xcecff8: add             x0, NULL, #0x20  ; true
    // 0xcecffc: ret
    //     0xcecffc: ret             
  }
  _ addError(/* No info */) {
    // ** addr: 0xced000, size: 0x88
    // 0xced000: EnterFrame
    //     0xced000: stp             fp, lr, [SP, #-0x10]!
    //     0xced004: mov             fp, SP
    // 0xced008: AllocStack(0x8)
    //     0xced008: sub             SP, SP, #8
    // 0xced00c: SetupParameters([dynamic _ = Null /* r0 */])
    //     0xced00c: ldur            w0, [x4, #0x13]
    //     0xced010: sub             x3, x0, #4
    //     0xced014: cmp             w3, #2
    //     0xced018: b.lt            #0xced028
    //     0xced01c: add             x0, fp, w3, sxtw #2
    //     0xced020: ldr             x0, [x0, #8]
    //     0xced024: b               #0xced02c
    //     0xced028: mov             x0, NULL
    // 0xced02c: CheckStackOverflow
    //     0xced02c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xced030: cmp             SP, x16
    //     0xced034: b.ls            #0xced080
    // 0xced038: LoadField: r3 = r1->field_13
    //     0xced038: ldur            w3, [x1, #0x13]
    // 0xced03c: DecompressPointer r3
    //     0xced03c: add             x3, x3, HEAP, lsl #32
    // 0xced040: tbz             w3, #4, #0xced060
    // 0xced044: str             x0, [SP]
    // 0xced048: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xced048: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xced04c: r0 = _addError()
    //     0xced04c: bl              #0xced088  ; [package:rxdart/src/subjects/subject.dart] Subject::_addError
    // 0xced050: r0 = Null
    //     0xced050: mov             x0, NULL
    // 0xced054: LeaveFrame
    //     0xced054: mov             SP, fp
    //     0xced058: ldp             fp, lr, [SP], #0x10
    // 0xced05c: ret
    //     0xced05c: ret             
    // 0xced060: r0 = StateError()
    //     0xced060: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xced064: mov             x1, x0
    // 0xced068: r0 = "You cannot add an error while items are being added from addStream"
    //     0xced068: add             x0, PP, #0xe, lsl #12  ; [pp+0xe7a0] "You cannot add an error while items are being added from addStream"
    //     0xced06c: ldr             x0, [x0, #0x7a0]
    // 0xced070: StoreField: r1->field_b = r0
    //     0xced070: stur            w0, [x1, #0xb]
    // 0xced074: mov             x0, x1
    // 0xced078: r0 = Throw()
    //     0xced078: bl              #0xec04b8  ; ThrowStub
    // 0xced07c: brk             #0
    // 0xced080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xced080: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xced084: b               #0xced038
  }
  _ _addError(/* No info */) {
    // ** addr: 0xced088, size: 0xe4
    // 0xced088: EnterFrame
    //     0xced088: stp             fp, lr, [SP, #-0x10]!
    //     0xced08c: mov             fp, SP
    // 0xced090: AllocStack(0x20)
    //     0xced090: sub             SP, SP, #0x20
    // 0xced094: SetupParameters(Subject<X0> this /* r2 => r0, fp-0x18 */, [dynamic _ = Null /* r4, fp-0x10 */])
    //     0xced094: mov             x0, x2
    //     0xced098: stur            x2, [fp, #-0x18]
    //     0xced09c: ldur            w2, [x4, #0x13]
    //     0xced0a0: sub             x3, x2, #4
    //     0xced0a4: cmp             w3, #2
    //     0xced0a8: b.lt            #0xced0bc
    //     0xced0ac: add             x2, fp, w3, sxtw #2
    //     0xced0b0: ldr             x2, [x2, #8]
    //     0xced0b4: mov             x4, x2
    //     0xced0b8: b               #0xced0c0
    //     0xced0bc: mov             x4, NULL
    //     0xced0c0: stur            x4, [fp, #-0x10]
    // 0xced0c4: CheckStackOverflow
    //     0xced0c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xced0c8: cmp             SP, x16
    //     0xced0cc: b.ls            #0xced164
    // 0xced0d0: LoadField: r5 = r1->field_f
    //     0xced0d0: ldur            w5, [x1, #0xf]
    // 0xced0d4: DecompressPointer r5
    //     0xced0d4: add             x5, x5, HEAP, lsl #32
    // 0xced0d8: stur            x5, [fp, #-8]
    // 0xced0dc: LoadField: r2 = r5->field_13
    //     0xced0dc: ldur            x2, [x5, #0x13]
    // 0xced0e0: tbnz            w2, #2, #0xced13c
    // 0xced0e4: r2 = LoadClassIdInstr(r1)
    //     0xced0e4: ldur            x2, [x1, #-1]
    //     0xced0e8: ubfx            x2, x2, #0xc, #0x14
    // 0xced0ec: r17 = 6669
    //     0xced0ec: movz            x17, #0x1a0d
    // 0xced0f0: cmp             x2, x17
    // 0xced0f4: b.eq            #0xced13c
    // 0xced0f8: r17 = 6670
    //     0xced0f8: movz            x17, #0x1a0e
    // 0xced0fc: cmp             x2, x17
    // 0xced100: b.ne            #0xced120
    // 0xced104: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xced104: ldur            w2, [x1, #0x17]
    // 0xced108: DecompressPointer r2
    //     0xced108: add             x2, x2, HEAP, lsl #32
    // 0xced10c: mov             x1, x2
    // 0xced110: mov             x2, x0
    // 0xced114: mov             x3, x4
    // 0xced118: r0 = setError()
    //     0xced118: bl              #0xced1e4  ; [package:rxdart/src/subjects/behavior_subject.dart] _Wrapper::setError
    // 0xced11c: b               #0xced13c
    // 0xced120: r0 = LoadClassIdInstr(r1)
    //     0xced120: ldur            x0, [x1, #-1]
    //     0xced124: ubfx            x0, x0, #0xc, #0x14
    // 0xced128: ldur            x2, [fp, #-0x18]
    // 0xced12c: ldur            x3, [fp, #-0x10]
    // 0xced130: r0 = GDT[cid_x0 + 0x41a]()
    //     0xced130: add             lr, x0, #0x41a
    //     0xced134: ldr             lr, [x21, lr, lsl #3]
    //     0xced138: blr             lr
    // 0xced13c: ldur            x16, [fp, #-0x10]
    // 0xced140: str             x16, [SP]
    // 0xced144: ldur            x1, [fp, #-8]
    // 0xced148: ldur            x2, [fp, #-0x18]
    // 0xced14c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xced14c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xced150: r0 = addError()
    //     0xced150: bl              #0xcecdc0  ; [dart:async] _BroadcastStreamController::addError
    // 0xced154: r0 = Null
    //     0xced154: mov             x0, NULL
    // 0xced158: LeaveFrame
    //     0xced158: mov             SP, fp
    //     0xced15c: ldp             fp, lr, [SP], #0x10
    // 0xced160: ret
    //     0xced160: ret             
    // 0xced164: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xced164: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xced168: b               #0xced0d0
  }
  [closure] void _addError(dynamic, Object, [StackTrace?]) {
    // ** addr: 0xced16c, size: 0x78
    // 0xced16c: EnterFrame
    //     0xced16c: stp             fp, lr, [SP, #-0x10]!
    //     0xced170: mov             fp, SP
    // 0xced174: AllocStack(0x8)
    //     0xced174: sub             SP, SP, #8
    // 0xced178: SetupParameters(Subject<X0> this /* r0 */, dynamic _ /* r2 */, [dynamic _ = Null /* r1 */])
    //     0xced178: ldur            w0, [x4, #0x13]
    //     0xced17c: sub             x1, x0, #4
    //     0xced180: add             x0, fp, w1, sxtw #2
    //     0xced184: ldr             x0, [x0, #0x18]
    //     0xced188: add             x2, fp, w1, sxtw #2
    //     0xced18c: ldr             x2, [x2, #0x10]
    //     0xced190: cmp             w1, #2
    //     0xced194: b.lt            #0xced1a8
    //     0xced198: add             x3, fp, w1, sxtw #2
    //     0xced19c: ldr             x3, [x3, #8]
    //     0xced1a0: mov             x1, x3
    //     0xced1a4: b               #0xced1ac
    //     0xced1a8: mov             x1, NULL
    //     0xced1ac: ldur            w3, [x0, #0x17]
    //     0xced1b0: add             x3, x3, HEAP, lsl #32
    // 0xced1b4: CheckStackOverflow
    //     0xced1b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xced1b8: cmp             SP, x16
    //     0xced1bc: b.ls            #0xced1dc
    // 0xced1c0: str             x1, [SP]
    // 0xced1c4: mov             x1, x3
    // 0xced1c8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xced1c8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xced1cc: r0 = _addError()
    //     0xced1cc: bl              #0xced088  ; [package:rxdart/src/subjects/subject.dart] Subject::_addError
    // 0xced1d0: LeaveFrame
    //     0xced1d0: mov             SP, fp
    //     0xced1d4: ldp             fp, lr, [SP], #0x10
    // 0xced1d8: ret
    //     0xced1d8: ret             
    // 0xced1dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xced1dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xced1e0: b               #0xced1c0
  }
  set _ onListen=(/* No info */) {
    // ** addr: 0xcf1cbc, size: 0x38
    // 0xcf1cbc: mov             x0, x2
    // 0xcf1cc0: LoadField: r2 = r1->field_f
    //     0xcf1cc0: ldur            w2, [x1, #0xf]
    // 0xcf1cc4: DecompressPointer r2
    //     0xcf1cc4: add             x2, x2, HEAP, lsl #32
    // 0xcf1cc8: StoreField: r2->field_b = r0
    //     0xcf1cc8: stur            w0, [x2, #0xb]
    //     0xcf1ccc: ldurb           w16, [x2, #-1]
    //     0xcf1cd0: ldurb           w17, [x0, #-1]
    //     0xcf1cd4: and             x16, x17, x16, lsr #2
    //     0xcf1cd8: tst             x16, HEAP, lsr #32
    //     0xcf1cdc: b.eq            #0xcf1cec
    //     0xcf1ce0: str             lr, [SP, #-8]!
    //     0xcf1ce4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    //     0xcf1ce8: ldr             lr, [SP], #8
    // 0xcf1cec: r0 = Null
    //     0xcf1cec: mov             x0, NULL
    // 0xcf1cf0: ret
    //     0xcf1cf0: ret             
  }
  set _ onCancel=(/* No info */) {
    // ** addr: 0xcf1cf4, size: 0x38
    // 0xcf1cf4: mov             x0, x2
    // 0xcf1cf8: LoadField: r2 = r1->field_f
    //     0xcf1cf8: ldur            w2, [x1, #0xf]
    // 0xcf1cfc: DecompressPointer r2
    //     0xcf1cfc: add             x2, x2, HEAP, lsl #32
    // 0xcf1d00: StoreField: r2->field_f = r0
    //     0xcf1d00: stur            w0, [x2, #0xf]
    //     0xcf1d04: ldurb           w16, [x2, #-1]
    //     0xcf1d08: ldurb           w17, [x0, #-1]
    //     0xcf1d0c: and             x16, x17, x16, lsr #2
    //     0xcf1d10: tst             x16, HEAP, lsr #32
    //     0xcf1d14: b.eq            #0xcf1d24
    //     0xcf1d18: str             lr, [SP, #-8]!
    //     0xcf1d1c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    //     0xcf1d20: ldr             lr, [SP], #8
    // 0xcf1d24: r0 = Null
    //     0xcf1d24: mov             x0, NULL
    // 0xcf1d28: ret
    //     0xcf1d28: ret             
  }
  set _ onResume=(/* No info */) {
    // ** addr: 0xcf4dac, size: 0x28
    // 0xcf4dac: EnterFrame
    //     0xcf4dac: stp             fp, lr, [SP, #-0x10]!
    //     0xcf4db0: mov             fp, SP
    // 0xcf4db4: r0 = UnsupportedError()
    //     0xcf4db4: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0xcf4db8: mov             x1, x0
    // 0xcf4dbc: r0 = "Subjects do not support resume callbacks"
    //     0xcf4dbc: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c8f0] "Subjects do not support resume callbacks"
    //     0xcf4dc0: ldr             x0, [x0, #0x8f0]
    // 0xcf4dc4: StoreField: r1->field_b = r0
    //     0xcf4dc4: stur            w0, [x1, #0xb]
    // 0xcf4dc8: mov             x0, x1
    // 0xcf4dcc: r0 = Throw()
    //     0xcf4dcc: bl              #0xec04b8  ; ThrowStub
    // 0xcf4dd0: brk             #0
  }
  _ addStream(/* No info */) {
    // ** addr: 0xcf51c8, size: 0x1fc
    // 0xcf51c8: EnterFrame
    //     0xcf51c8: stp             fp, lr, [SP, #-0x10]!
    //     0xcf51cc: mov             fp, SP
    // 0xcf51d0: AllocStack(0x48)
    //     0xcf51d0: sub             SP, SP, #0x48
    // 0xcf51d4: SetupParameters(Subject<X0> this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, {dynamic cancelOnError = Null /* r1, fp-0x8 */})
    //     0xcf51d4: mov             x0, x2
    //     0xcf51d8: stur            x2, [fp, #-0x18]
    //     0xcf51dc: mov             x2, x1
    //     0xcf51e0: stur            x1, [fp, #-0x10]
    //     0xcf51e4: ldur            w1, [x4, #0x13]
    //     0xcf51e8: ldur            w3, [x4, #0x1f]
    //     0xcf51ec: add             x3, x3, HEAP, lsl #32
    //     0xcf51f0: add             x16, PP, #0xc, lsl #12  ; [pp+0xc128] "cancelOnError"
    //     0xcf51f4: ldr             x16, [x16, #0x128]
    //     0xcf51f8: cmp             w3, w16
    //     0xcf51fc: b.ne            #0xcf5218
    //     0xcf5200: ldur            w3, [x4, #0x23]
    //     0xcf5204: add             x3, x3, HEAP, lsl #32
    //     0xcf5208: sub             w4, w1, w3
    //     0xcf520c: add             x1, fp, w4, sxtw #2
    //     0xcf5210: ldr             x1, [x1, #8]
    //     0xcf5214: b               #0xcf521c
    //     0xcf5218: mov             x1, NULL
    //     0xcf521c: stur            x1, [fp, #-8]
    // 0xcf5220: CheckStackOverflow
    //     0xcf5220: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcf5224: cmp             SP, x16
    //     0xcf5228: b.ls            #0xcf53bc
    // 0xcf522c: r1 = 3
    //     0xcf522c: movz            x1, #0x3
    // 0xcf5230: r0 = AllocateContext()
    //     0xcf5230: bl              #0xec126c  ; AllocateContextStub
    // 0xcf5234: mov             x4, x0
    // 0xcf5238: ldur            x3, [fp, #-0x10]
    // 0xcf523c: stur            x4, [fp, #-0x20]
    // 0xcf5240: StoreField: r4->field_f = r3
    //     0xcf5240: stur            w3, [x4, #0xf]
    // 0xcf5244: LoadField: r2 = r3->field_7
    //     0xcf5244: ldur            w2, [x3, #7]
    // 0xcf5248: DecompressPointer r2
    //     0xcf5248: add             x2, x2, HEAP, lsl #32
    // 0xcf524c: ldur            x0, [fp, #-0x18]
    // 0xcf5250: r1 = Null
    //     0xcf5250: mov             x1, NULL
    // 0xcf5254: r8 = Stream<X0>
    //     0xcf5254: add             x8, PP, #0xc, lsl #12  ; [pp+0xcf10] Type: Stream<X0>
    //     0xcf5258: ldr             x8, [x8, #0xf10]
    // 0xcf525c: LoadField: r9 = r8->field_7
    //     0xcf525c: ldur            x9, [x8, #7]
    // 0xcf5260: r3 = Null
    //     0xcf5260: add             x3, PP, #0xe, lsl #12  ; [pp+0xeed8] Null
    //     0xcf5264: ldr             x3, [x3, #0xed8]
    // 0xcf5268: blr             x9
    // 0xcf526c: ldur            x2, [fp, #-0x10]
    // 0xcf5270: LoadField: r0 = r2->field_13
    //     0xcf5270: ldur            w0, [x2, #0x13]
    // 0xcf5274: DecompressPointer r0
    //     0xcf5274: add             x0, x0, HEAP, lsl #32
    // 0xcf5278: tbz             w0, #4, #0xcf539c
    // 0xcf527c: ldur            x3, [fp, #-8]
    // 0xcf5280: ldur            x0, [fp, #-0x20]
    // 0xcf5284: r1 = true
    //     0xcf5284: add             x1, NULL, #0x20  ; true
    // 0xcf5288: StoreField: r2->field_13 = r1
    //     0xcf5288: stur            w1, [x2, #0x13]
    // 0xcf528c: r1 = <void?>
    //     0xcf528c: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xcf5290: r0 = _Future()
    //     0xcf5290: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xcf5294: stur            x0, [fp, #-0x28]
    // 0xcf5298: StoreField: r0->field_b = rZR
    //     0xcf5298: stur            xzr, [x0, #0xb]
    // 0xcf529c: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0xcf529c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xcf52a0: ldr             x0, [x0, #0x7a0]
    //     0xcf52a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xcf52a8: cmp             w0, w16
    //     0xcf52ac: b.ne            #0xcf52b8
    //     0xcf52b0: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0xcf52b4: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xcf52b8: mov             x1, x0
    // 0xcf52bc: ldur            x0, [fp, #-0x28]
    // 0xcf52c0: StoreField: r0->field_13 = r1
    //     0xcf52c0: stur            w1, [x0, #0x13]
    // 0xcf52c4: r1 = <void?>
    //     0xcf52c4: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xcf52c8: r0 = _AsyncCompleter()
    //     0xcf52c8: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0xcf52cc: mov             x1, x0
    // 0xcf52d0: ldur            x0, [fp, #-0x28]
    // 0xcf52d4: StoreField: r1->field_b = r0
    //     0xcf52d4: stur            w0, [x1, #0xb]
    // 0xcf52d8: ldur            x3, [fp, #-0x20]
    // 0xcf52dc: StoreField: r3->field_13 = r1
    //     0xcf52dc: stur            w1, [x3, #0x13]
    // 0xcf52e0: mov             x2, x3
    // 0xcf52e4: r1 = Function 'complete':.
    //     0xcf52e4: add             x1, PP, #0xe, lsl #12  ; [pp+0xeee8] AnonymousClosure: (0xcf5468), in [package:rxdart/src/subjects/subject.dart] Subject::addStream (0xcf51c8)
    //     0xcf52e8: ldr             x1, [x1, #0xee8]
    // 0xcf52ec: r0 = AllocateClosure()
    //     0xcf52ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xcf52f0: ldur            x2, [fp, #-0x20]
    // 0xcf52f4: stur            x0, [fp, #-0x30]
    // 0xcf52f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xcf52f8: stur            w0, [x2, #0x17]
    // 0xcf52fc: ldur            x3, [fp, #-8]
    // 0xcf5300: r16 = true
    //     0xcf5300: add             x16, NULL, #0x20  ; true
    // 0xcf5304: cmp             w3, w16
    // 0xcf5308: b.ne            #0xcf5320
    // 0xcf530c: r1 = Function '<anonymous closure>':.
    //     0xcf530c: add             x1, PP, #0xe, lsl #12  ; [pp+0xeef0] AnonymousClosure: (0xcf53c4), in [package:rxdart/src/subjects/subject.dart] Subject::addStream (0xcf51c8)
    //     0xcf5310: ldr             x1, [x1, #0xef0]
    // 0xcf5314: r0 = AllocateClosure()
    //     0xcf5314: bl              #0xec1630  ; AllocateClosureStub
    // 0xcf5318: mov             x3, x0
    // 0xcf531c: b               #0xcf5334
    // 0xcf5320: ldur            x2, [fp, #-0x10]
    // 0xcf5324: r1 = Function '_addError@795337007':.
    //     0xcf5324: add             x1, PP, #0xe, lsl #12  ; [pp+0xeef8] AnonymousClosure: (0xced16c), in [package:rxdart/src/subjects/subject.dart] Subject::_addError (0xced088)
    //     0xcf5328: ldr             x1, [x1, #0xef8]
    // 0xcf532c: r0 = AllocateClosure()
    //     0xcf532c: bl              #0xec1630  ; AllocateClosureStub
    // 0xcf5330: mov             x3, x0
    // 0xcf5334: ldur            x0, [fp, #-0x18]
    // 0xcf5338: ldur            x2, [fp, #-0x10]
    // 0xcf533c: stur            x3, [fp, #-0x20]
    // 0xcf5340: r1 = Function '_add@795337007':.
    //     0xcf5340: add             x1, PP, #0xe, lsl #12  ; [pp+0xef00] AnonymousClosure: (0xaa93d4), in [package:rxdart/src/subjects/subject.dart] Subject::_add (0xaa9290)
    //     0xcf5344: ldr             x1, [x1, #0xf00]
    // 0xcf5348: r0 = AllocateClosure()
    //     0xcf5348: bl              #0xec1630  ; AllocateClosureStub
    // 0xcf534c: ldur            x1, [fp, #-0x18]
    // 0xcf5350: r2 = LoadClassIdInstr(r1)
    //     0xcf5350: ldur            x2, [x1, #-1]
    //     0xcf5354: ubfx            x2, x2, #0xc, #0x14
    // 0xcf5358: ldur            x16, [fp, #-0x20]
    // 0xcf535c: ldur            lr, [fp, #-0x30]
    // 0xcf5360: stp             lr, x16, [SP, #8]
    // 0xcf5364: ldur            x16, [fp, #-8]
    // 0xcf5368: str             x16, [SP]
    // 0xcf536c: mov             x16, x0
    // 0xcf5370: mov             x0, x2
    // 0xcf5374: mov             x2, x16
    // 0xcf5378: r4 = const [0, 0x5, 0x3, 0x2, cancelOnError, 0x4, onDone, 0x3, onError, 0x2, null]
    //     0xcf5378: add             x4, PP, #0xd, lsl #12  ; [pp+0xdf78] List(11) [0, 0x5, 0x3, 0x2, "cancelOnError", 0x4, "onDone", 0x3, "onError", 0x2, Null]
    //     0xcf537c: ldr             x4, [x4, #0xf78]
    // 0xcf5380: r0 = GDT[cid_x0 + 0x64e]()
    //     0xcf5380: add             lr, x0, #0x64e
    //     0xcf5384: ldr             lr, [x21, lr, lsl #3]
    //     0xcf5388: blr             lr
    // 0xcf538c: ldur            x0, [fp, #-0x28]
    // 0xcf5390: LeaveFrame
    //     0xcf5390: mov             SP, fp
    //     0xcf5394: ldp             fp, lr, [SP], #0x10
    // 0xcf5398: ret
    //     0xcf5398: ret             
    // 0xcf539c: r0 = StateError()
    //     0xcf539c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xcf53a0: mov             x1, x0
    // 0xcf53a4: r0 = "You cannot add items while items are being added from addStream"
    //     0xcf53a4: add             x0, PP, #0xe, lsl #12  ; [pp+0xef08] "You cannot add items while items are being added from addStream"
    //     0xcf53a8: ldr             x0, [x0, #0xf08]
    // 0xcf53ac: StoreField: r1->field_b = r0
    //     0xcf53ac: stur            w0, [x1, #0xb]
    // 0xcf53b0: mov             x0, x1
    // 0xcf53b4: r0 = Throw()
    //     0xcf53b4: bl              #0xec04b8  ; ThrowStub
    // 0xcf53b8: brk             #0
    // 0xcf53bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcf53bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcf53c0: b               #0xcf522c
  }
  [closure] Null <anonymous closure>(dynamic, Object, StackTrace) {
    // ** addr: 0xcf53c4, size: 0xa4
    // 0xcf53c4: EnterFrame
    //     0xcf53c4: stp             fp, lr, [SP, #-0x10]!
    //     0xcf53c8: mov             fp, SP
    // 0xcf53cc: AllocStack(0x10)
    //     0xcf53cc: sub             SP, SP, #0x10
    // 0xcf53d0: SetupParameters()
    //     0xcf53d0: ldr             x0, [fp, #0x20]
    //     0xcf53d4: ldur            w3, [x0, #0x17]
    //     0xcf53d8: add             x3, x3, HEAP, lsl #32
    //     0xcf53dc: stur            x3, [fp, #-8]
    // 0xcf53e0: CheckStackOverflow
    //     0xcf53e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcf53e4: cmp             SP, x16
    //     0xcf53e8: b.ls            #0xcf5460
    // 0xcf53ec: LoadField: r1 = r3->field_f
    //     0xcf53ec: ldur            w1, [x3, #0xf]
    // 0xcf53f0: DecompressPointer r1
    //     0xcf53f0: add             x1, x1, HEAP, lsl #32
    // 0xcf53f4: ldr             x16, [fp, #0x10]
    // 0xcf53f8: str             x16, [SP]
    // 0xcf53fc: ldr             x2, [fp, #0x18]
    // 0xcf5400: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xcf5400: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xcf5404: r0 = _addError()
    //     0xcf5404: bl              #0xced088  ; [package:rxdart/src/subjects/subject.dart] Subject::_addError
    // 0xcf5408: ldur            x0, [fp, #-8]
    // 0xcf540c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xcf540c: ldur            w1, [x0, #0x17]
    // 0xcf5410: DecompressPointer r1
    //     0xcf5410: add             x1, x1, HEAP, lsl #32
    // 0xcf5414: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xcf5414: ldur            w0, [x1, #0x17]
    // 0xcf5418: DecompressPointer r0
    //     0xcf5418: add             x0, x0, HEAP, lsl #32
    // 0xcf541c: LoadField: r1 = r0->field_13
    //     0xcf541c: ldur            w1, [x0, #0x13]
    // 0xcf5420: DecompressPointer r1
    //     0xcf5420: add             x1, x1, HEAP, lsl #32
    // 0xcf5424: LoadField: r2 = r1->field_b
    //     0xcf5424: ldur            w2, [x1, #0xb]
    // 0xcf5428: DecompressPointer r2
    //     0xcf5428: add             x2, x2, HEAP, lsl #32
    // 0xcf542c: LoadField: r3 = r2->field_b
    //     0xcf542c: ldur            x3, [x2, #0xb]
    // 0xcf5430: tst             x3, #0x1e
    // 0xcf5434: b.ne            #0xcf5450
    // 0xcf5438: r2 = false
    //     0xcf5438: add             x2, NULL, #0x30  ; false
    // 0xcf543c: LoadField: r3 = r0->field_f
    //     0xcf543c: ldur            w3, [x0, #0xf]
    // 0xcf5440: DecompressPointer r3
    //     0xcf5440: add             x3, x3, HEAP, lsl #32
    // 0xcf5444: StoreField: r3->field_13 = r2
    //     0xcf5444: stur            w2, [x3, #0x13]
    // 0xcf5448: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xcf5448: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xcf544c: r0 = complete()
    //     0xcf544c: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0xcf5450: r0 = Null
    //     0xcf5450: mov             x0, NULL
    // 0xcf5454: LeaveFrame
    //     0xcf5454: mov             SP, fp
    //     0xcf5458: ldp             fp, lr, [SP], #0x10
    // 0xcf545c: ret
    //     0xcf545c: ret             
    // 0xcf5460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcf5460: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcf5464: b               #0xcf53ec
  }
  [closure] void complete(dynamic) {
    // ** addr: 0xcf5468, size: 0x70
    // 0xcf5468: EnterFrame
    //     0xcf5468: stp             fp, lr, [SP, #-0x10]!
    //     0xcf546c: mov             fp, SP
    // 0xcf5470: ldr             x0, [fp, #0x10]
    // 0xcf5474: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xcf5474: ldur            w1, [x0, #0x17]
    // 0xcf5478: DecompressPointer r1
    //     0xcf5478: add             x1, x1, HEAP, lsl #32
    // 0xcf547c: CheckStackOverflow
    //     0xcf547c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcf5480: cmp             SP, x16
    //     0xcf5484: b.ls            #0xcf54d0
    // 0xcf5488: LoadField: r0 = r1->field_13
    //     0xcf5488: ldur            w0, [x1, #0x13]
    // 0xcf548c: DecompressPointer r0
    //     0xcf548c: add             x0, x0, HEAP, lsl #32
    // 0xcf5490: LoadField: r2 = r0->field_b
    //     0xcf5490: ldur            w2, [x0, #0xb]
    // 0xcf5494: DecompressPointer r2
    //     0xcf5494: add             x2, x2, HEAP, lsl #32
    // 0xcf5498: LoadField: r3 = r2->field_b
    //     0xcf5498: ldur            x3, [x2, #0xb]
    // 0xcf549c: tst             x3, #0x1e
    // 0xcf54a0: b.ne            #0xcf54c0
    // 0xcf54a4: r2 = false
    //     0xcf54a4: add             x2, NULL, #0x30  ; false
    // 0xcf54a8: LoadField: r3 = r1->field_f
    //     0xcf54a8: ldur            w3, [x1, #0xf]
    // 0xcf54ac: DecompressPointer r3
    //     0xcf54ac: add             x3, x3, HEAP, lsl #32
    // 0xcf54b0: StoreField: r3->field_13 = r2
    //     0xcf54b0: stur            w2, [x3, #0x13]
    // 0xcf54b4: mov             x1, x0
    // 0xcf54b8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xcf54b8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xcf54bc: r0 = complete()
    //     0xcf54bc: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0xcf54c0: r0 = Null
    //     0xcf54c0: mov             x0, NULL
    // 0xcf54c4: LeaveFrame
    //     0xcf54c4: mov             SP, fp
    //     0xcf54c8: ldp             fp, lr, [SP], #0x10
    // 0xcf54cc: ret
    //     0xcf54cc: ret             
    // 0xcf54d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcf54d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcf54d4: b               #0xcf5488
  }
  get _ stream(/* No info */) {
    // ** addr: 0xd216bc, size: 0x34
    // 0xd216bc: EnterFrame
    //     0xd216bc: stp             fp, lr, [SP, #-0x10]!
    //     0xd216c0: mov             fp, SP
    // 0xd216c4: AllocStack(0x8)
    //     0xd216c4: sub             SP, SP, #8
    // 0xd216c8: SetupParameters(Subject<X0> this /* r1 => r0, fp-0x8 */)
    //     0xd216c8: mov             x0, x1
    //     0xd216cc: stur            x1, [fp, #-8]
    // 0xd216d0: LoadField: r1 = r0->field_7
    //     0xd216d0: ldur            w1, [x0, #7]
    // 0xd216d4: DecompressPointer r1
    //     0xd216d4: add             x1, x1, HEAP, lsl #32
    // 0xd216d8: r0 = _SubjectStream()
    //     0xd216d8: bl              #0x8b33b0  ; Allocate_SubjectStreamStub -> _SubjectStream<X0> (size=0x10)
    // 0xd216dc: ldur            x1, [fp, #-8]
    // 0xd216e0: StoreField: r0->field_b = r1
    //     0xd216e0: stur            w1, [x0, #0xb]
    // 0xd216e4: LeaveFrame
    //     0xd216e4: mov             SP, fp
    //     0xd216e8: ldp             fp, lr, [SP], #0x10
    // 0xd216ec: ret
    //     0xd216ec: ret             
  }
  get _ hasListener(/* No info */) {
    // ** addr: 0xd2d6d0, size: 0x38
    // 0xd2d6d0: EnterFrame
    //     0xd2d6d0: stp             fp, lr, [SP, #-0x10]!
    //     0xd2d6d4: mov             fp, SP
    // 0xd2d6d8: CheckStackOverflow
    //     0xd2d6d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd2d6dc: cmp             SP, x16
    //     0xd2d6e0: b.ls            #0xd2d700
    // 0xd2d6e4: LoadField: r0 = r1->field_f
    //     0xd2d6e4: ldur            w0, [x1, #0xf]
    // 0xd2d6e8: DecompressPointer r0
    //     0xd2d6e8: add             x0, x0, HEAP, lsl #32
    // 0xd2d6ec: mov             x1, x0
    // 0xd2d6f0: r0 = hasQuery()
    //     0xd2d6f0: bl              #0xd644e4  ; [dart:core] _Uri::hasQuery
    // 0xd2d6f4: LeaveFrame
    //     0xd2d6f4: mov             SP, fp
    //     0xd2d6f8: ldp             fp, lr, [SP], #0x10
    // 0xd2d6fc: ret
    //     0xd2d6fc: ret             
    // 0xd2d700: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd2d700: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd2d704: b               #0xd2d6e4
  }
}
