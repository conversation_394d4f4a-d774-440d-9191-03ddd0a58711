// lib: , url: package:rxdart/src/transformers/backpressure/backpressure.dart

// class id: 1051094, size: 0x8
class :: {
}

// class id: 520, size: 0x50, field offset: 0x10
class _BackpressureStreamSink<X0, X1> extends ForwardingSink<X0, X1> {

  [closure] void onDone(dynamic) {
    // ** addr: 0xab0a38, size: 0x38
    // 0xab0a38: EnterFrame
    //     0xab0a38: stp             fp, lr, [SP, #-0x10]!
    //     0xab0a3c: mov             fp, SP
    // 0xab0a40: ldr             x0, [fp, #0x10]
    // 0xab0a44: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab0a44: ldur            w1, [x0, #0x17]
    // 0xab0a48: DecompressPointer r1
    //     0xab0a48: add             x1, x1, HEAP, lsl #32
    // 0xab0a4c: CheckStackOverflow
    //     0xab0a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab0a50: cmp             SP, x16
    //     0xab0a54: b.ls            #0xab0a68
    // 0xab0a58: r0 = onDone()
    //     0xab0a58: bl              #0xab0a70  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::onDone
    // 0xab0a5c: LeaveFrame
    //     0xab0a5c: mov             SP, fp
    //     0xab0a60: ldp             fp, lr, [SP], #0x10
    // 0xab0a64: ret
    //     0xab0a64: ret             
    // 0xab0a68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab0a68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab0a6c: b               #0xab0a58
  }
  _ onDone(/* No info */) {
    // ** addr: 0xab0a70, size: 0x114
    // 0xab0a70: EnterFrame
    //     0xab0a70: stp             fp, lr, [SP, #-0x10]!
    //     0xab0a74: mov             fp, SP
    // 0xab0a78: AllocStack(0x10)
    //     0xab0a78: sub             SP, SP, #0x10
    // 0xab0a7c: r0 = true
    //     0xab0a7c: add             x0, NULL, #0x20  ; true
    // 0xab0a80: mov             x3, x1
    // 0xab0a84: stur            x1, [fp, #-8]
    // 0xab0a88: CheckStackOverflow
    //     0xab0a88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab0a8c: cmp             SP, x16
    //     0xab0a90: b.ls            #0xab0b7c
    // 0xab0a94: StoreField: r3->field_47 = r0
    //     0xab0a94: stur            w0, [x3, #0x47]
    // 0xab0a98: LoadField: r2 = r3->field_b
    //     0xab0a98: ldur            w2, [x3, #0xb]
    // 0xab0a9c: DecompressPointer r2
    //     0xab0a9c: add             x2, x2, HEAP, lsl #32
    // 0xab0aa0: cmp             w2, NULL
    // 0xab0aa4: b.eq            #0xab0b34
    // 0xab0aa8: r16 = true
    //     0xab0aa8: add             x16, NULL, #0x20  ; true
    // 0xab0aac: str             x16, [SP]
    // 0xab0ab0: mov             x1, x3
    // 0xab0ab4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xab0ab4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xab0ab8: r0 = resolveWindowEnd()
    //     0xab0ab8: bl              #0xab0ef0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::resolveWindowEnd
    // 0xab0abc: ldur            x0, [fp, #-8]
    // 0xab0ac0: LoadField: r1 = r0->field_33
    //     0xab0ac0: ldur            w1, [x0, #0x33]
    // 0xab0ac4: DecompressPointer r1
    //     0xab0ac4: add             x1, x1, HEAP, lsl #32
    // 0xab0ac8: r0 = clear()
    //     0xab0ac8: bl              #0xab0b84  ; [dart:collection] DoubleLinkedQueue::clear
    // 0xab0acc: ldur            x2, [fp, #-8]
    // 0xab0ad0: LoadField: r1 = r2->field_4b
    //     0xab0ad0: ldur            w1, [x2, #0x4b]
    // 0xab0ad4: DecompressPointer r1
    //     0xab0ad4: add             x1, x1, HEAP, lsl #32
    // 0xab0ad8: cmp             w1, NULL
    // 0xab0adc: b.ne            #0xab0ae8
    // 0xab0ae0: mov             x0, x2
    // 0xab0ae4: b               #0xab0b00
    // 0xab0ae8: r0 = LoadClassIdInstr(r1)
    //     0xab0ae8: ldur            x0, [x1, #-1]
    //     0xab0aec: ubfx            x0, x0, #0xc, #0x14
    // 0xab0af0: r0 = GDT[cid_x0 + -0x37]()
    //     0xab0af0: sub             lr, x0, #0x37
    //     0xab0af4: ldr             lr, [x21, lr, lsl #3]
    //     0xab0af8: blr             lr
    // 0xab0afc: ldur            x0, [fp, #-8]
    // 0xab0b00: LoadField: r1 = r0->field_b
    //     0xab0b00: ldur            w1, [x0, #0xb]
    // 0xab0b04: DecompressPointer r1
    //     0xab0b04: add             x1, x1, HEAP, lsl #32
    // 0xab0b08: cmp             w1, NULL
    // 0xab0b0c: b.eq            #0xab0b54
    // 0xab0b10: r0 = LoadClassIdInstr(r1)
    //     0xab0b10: ldur            x0, [x1, #-1]
    //     0xab0b14: ubfx            x0, x0, #0xc, #0x14
    // 0xab0b18: r0 = GDT[cid_x0 + 0xf47]()
    //     0xab0b18: add             lr, x0, #0xf47
    //     0xab0b1c: ldr             lr, [x21, lr, lsl #3]
    //     0xab0b20: blr             lr
    // 0xab0b24: r0 = Null
    //     0xab0b24: mov             x0, NULL
    // 0xab0b28: LeaveFrame
    //     0xab0b28: mov             SP, fp
    //     0xab0b2c: ldp             fp, lr, [SP], #0x10
    // 0xab0b30: ret
    //     0xab0b30: ret             
    // 0xab0b34: r0 = StateError()
    //     0xab0b34: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab0b38: mov             x1, x0
    // 0xab0b3c: r0 = "Must call setSink(sink) before accessing!"
    //     0xab0b3c: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab0b40: ldr             x0, [x0, #0xcd0]
    // 0xab0b44: StoreField: r1->field_b = r0
    //     0xab0b44: stur            w0, [x1, #0xb]
    // 0xab0b48: mov             x0, x1
    // 0xab0b4c: r0 = Throw()
    //     0xab0b4c: bl              #0xec04b8  ; ThrowStub
    // 0xab0b50: brk             #0
    // 0xab0b54: r0 = "Must call setSink(sink) before accessing!"
    //     0xab0b54: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab0b58: ldr             x0, [x0, #0xcd0]
    // 0xab0b5c: r0 = StateError()
    //     0xab0b5c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab0b60: mov             x1, x0
    // 0xab0b64: r0 = "Must call setSink(sink) before accessing!"
    //     0xab0b64: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab0b68: ldr             x0, [x0, #0xcd0]
    // 0xab0b6c: StoreField: r1->field_b = r0
    //     0xab0b6c: stur            w0, [x1, #0xb]
    // 0xab0b70: mov             x0, x1
    // 0xab0b74: r0 = Throw()
    //     0xab0b74: bl              #0xec04b8  ; ThrowStub
    // 0xab0b78: brk             #0
    // 0xab0b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab0b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab0b80: b               #0xab0a94
  }
  _ resolveWindowEnd(/* No info */) {
    // ** addr: 0xab0ef0, size: 0x198
    // 0xab0ef0: EnterFrame
    //     0xab0ef0: stp             fp, lr, [SP, #-0x10]!
    //     0xab0ef4: mov             fp, SP
    // 0xab0ef8: AllocStack(0x30)
    //     0xab0ef8: sub             SP, SP, #0x30
    // 0xab0efc: SetupParameters(_BackpressureStreamSink<X0, X1> this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, [dynamic _ = false /* r4, fp-0x8 */])
    //     0xab0efc: mov             x3, x1
    //     0xab0f00: stur            x1, [fp, #-0x10]
    //     0xab0f04: stur            x2, [fp, #-0x18]
    //     0xab0f08: ldur            w0, [x4, #0x13]
    //     0xab0f0c: sub             x1, x0, #4
    //     0xab0f10: cmp             w1, #2
    //     0xab0f14: b.lt            #0xab0f28
    //     0xab0f18: add             x0, fp, w1, sxtw #2
    //     0xab0f1c: ldr             x0, [x0, #8]
    //     0xab0f20: mov             x4, x0
    //     0xab0f24: b               #0xab0f2c
    //     0xab0f28: add             x4, NULL, #0x30  ; false
    //     0xab0f2c: stur            x4, [fp, #-8]
    // 0xab0f30: CheckStackOverflow
    //     0xab0f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab0f34: cmp             SP, x16
    //     0xab0f38: b.ls            #0xab107c
    // 0xab0f3c: tbnz            w4, #4, #0xab0f78
    // 0xab0f40: LoadField: r1 = r3->field_4b
    //     0xab0f40: ldur            w1, [x3, #0x4b]
    // 0xab0f44: DecompressPointer r1
    //     0xab0f44: add             x1, x1, HEAP, lsl #32
    // 0xab0f48: cmp             w1, NULL
    // 0xab0f4c: b.ne            #0xab0f58
    // 0xab0f50: mov             x0, x3
    // 0xab0f54: b               #0xab0f70
    // 0xab0f58: r0 = LoadClassIdInstr(r1)
    //     0xab0f58: ldur            x0, [x1, #-1]
    //     0xab0f5c: ubfx            x0, x0, #0xc, #0x14
    // 0xab0f60: r0 = GDT[cid_x0 + -0x37]()
    //     0xab0f60: sub             lr, x0, #0x37
    //     0xab0f64: ldr             lr, [x21, lr, lsl #3]
    //     0xab0f68: blr             lr
    // 0xab0f6c: ldur            x0, [fp, #-0x10]
    // 0xab0f70: StoreField: r0->field_4b = rNULL
    //     0xab0f70: stur            NULL, [x0, #0x4b]
    // 0xab0f74: b               #0xab0f7c
    // 0xab0f78: mov             x0, x3
    // 0xab0f7c: ldur            x1, [fp, #-8]
    // 0xab0f80: tbnz            w1, #4, #0xab0f94
    // 0xab0f84: r0 = Null
    //     0xab0f84: mov             x0, NULL
    // 0xab0f88: LeaveFrame
    //     0xab0f88: mov             SP, fp
    //     0xab0f8c: ldp             fp, lr, [SP], #0x10
    // 0xab0f90: ret
    //     0xab0f90: ret             
    // 0xab0f94: LoadField: r1 = r0->field_43
    //     0xab0f94: ldur            w1, [x0, #0x43]
    // 0xab0f98: DecompressPointer r1
    //     0xab0f98: add             x1, x1, HEAP, lsl #32
    // 0xab0f9c: tbnz            w1, #4, #0xab106c
    // 0xab0fa0: LoadField: r2 = r0->field_33
    //     0xab0fa0: ldur            w2, [x0, #0x33]
    // 0xab0fa4: DecompressPointer r2
    //     0xab0fa4: add             x2, x2, HEAP, lsl #32
    // 0xab0fa8: stur            x2, [fp, #-0x20]
    // 0xab0fac: LoadField: r1 = r2->field_b
    //     0xab0fac: ldur            w1, [x2, #0xb]
    // 0xab0fb0: DecompressPointer r1
    //     0xab0fb0: add             x1, x1, HEAP, lsl #32
    // 0xab0fb4: LoadField: r3 = r1->field_f
    //     0xab0fb4: ldur            w3, [x1, #0xf]
    // 0xab0fb8: DecompressPointer r3
    //     0xab0fb8: add             x3, x3, HEAP, lsl #32
    // 0xab0fbc: cmp             w3, w1
    // 0xab0fc0: b.eq            #0xab106c
    // 0xab0fc4: ldur            x3, [fp, #-0x18]
    // 0xab0fc8: LoadField: r4 = r0->field_1b
    //     0xab0fc8: ldur            w4, [x0, #0x1b]
    // 0xab0fcc: DecompressPointer r4
    //     0xab0fcc: add             x4, x4, HEAP, lsl #32
    // 0xab0fd0: mov             x1, x0
    // 0xab0fd4: stur            x4, [fp, #-8]
    // 0xab0fd8: r0 = unmodifiableQueue()
    //     0xab0fd8: bl              #0xab12c0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::unmodifiableQueue
    // 0xab0fdc: mov             x1, x0
    // 0xab0fe0: ldur            x0, [fp, #-8]
    // 0xab0fe4: cmp             w0, NULL
    // 0xab0fe8: b.eq            #0xab1084
    // 0xab0fec: stp             x1, x0, [SP]
    // 0xab0ff0: ClosureCall
    //     0xab0ff0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xab0ff4: ldur            x2, [x0, #0x1f]
    //     0xab0ff8: blr             x2
    // 0xab0ffc: ldur            x1, [fp, #-0x18]
    // 0xab1000: r2 = LoadClassIdInstr(r1)
    //     0xab1000: ldur            x2, [x1, #-1]
    //     0xab1004: ubfx            x2, x2, #0xc, #0x14
    // 0xab1008: mov             x16, x0
    // 0xab100c: mov             x0, x2
    // 0xab1010: mov             x2, x16
    // 0xab1014: r0 = GDT[cid_x0 + 0xdbe]()
    //     0xab1014: add             lr, x0, #0xdbe
    //     0xab1018: ldr             lr, [x21, lr, lsl #3]
    //     0xab101c: blr             lr
    // 0xab1020: ldur            x1, [fp, #-0x20]
    // 0xab1024: LoadField: r0 = r1->field_f
    //     0xab1024: ldur            x0, [x1, #0xf]
    // 0xab1028: cmp             x0, #1
    // 0xab102c: b.ge            #0xab103c
    // 0xab1030: r2 = 1
    //     0xab1030: movz            x2, #0x1
    // 0xab1034: sub             x3, x2, x0
    // 0xab1038: b               #0xab1040
    // 0xab103c: r3 = 0
    //     0xab103c: movz            x3, #0
    // 0xab1040: ldur            x2, [fp, #-0x10]
    // 0xab1044: StoreField: r2->field_3b = r3
    //     0xab1044: stur            x3, [x2, #0x3b]
    // 0xab1048: cmp             x0, #1
    // 0xab104c: b.le            #0xab1068
    // 0xab1050: LoadField: r0 = r2->field_7
    //     0xab1050: ldur            w0, [x2, #7]
    // 0xab1054: DecompressPointer r0
    //     0xab1054: add             x0, x0, HEAP, lsl #32
    // 0xab1058: stp             x1, x0, [SP]
    // 0xab105c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab105c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab1060: r0 = RemoveFirstElementsQueueExtension.removeFirstElements()
    //     0xab1060: bl              #0xab1088  ; [package:rxdart/src/utils/collection_extensions.dart] ::RemoveFirstElementsQueueExtension.removeFirstElements
    // 0xab1064: b               #0xab106c
    // 0xab1068: r0 = clear()
    //     0xab1068: bl              #0xab0b84  ; [dart:collection] DoubleLinkedQueue::clear
    // 0xab106c: r0 = Null
    //     0xab106c: mov             x0, NULL
    // 0xab1070: LeaveFrame
    //     0xab1070: mov             SP, fp
    //     0xab1074: ldp             fp, lr, [SP], #0x10
    // 0xab1078: ret
    //     0xab1078: ret             
    // 0xab107c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab107c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1080: b               #0xab0f3c
    // 0xab1084: r0 = NullErrorSharedWithoutFPURegs()
    //     0xab1084: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  get _ unmodifiableQueue(/* No info */) {
    // ** addr: 0xab12c0, size: 0x68
    // 0xab12c0: EnterFrame
    //     0xab12c0: stp             fp, lr, [SP, #-0x10]!
    //     0xab12c4: mov             fp, SP
    // 0xab12c8: AllocStack(0x18)
    //     0xab12c8: sub             SP, SP, #0x18
    // 0xab12cc: CheckStackOverflow
    //     0xab12cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab12d0: cmp             SP, x16
    //     0xab12d4: b.ls            #0xab1320
    // 0xab12d8: LoadField: r0 = r1->field_7
    //     0xab12d8: ldur            w0, [x1, #7]
    // 0xab12dc: DecompressPointer r0
    //     0xab12dc: add             x0, x0, HEAP, lsl #32
    // 0xab12e0: stur            x0, [fp, #-8]
    // 0xab12e4: LoadField: r2 = r1->field_33
    //     0xab12e4: ldur            w2, [x1, #0x33]
    // 0xab12e8: DecompressPointer r2
    //     0xab12e8: add             x2, x2, HEAP, lsl #32
    // 0xab12ec: r16 = false
    //     0xab12ec: add             x16, NULL, #0x30  ; false
    // 0xab12f0: str             x16, [SP]
    // 0xab12f4: mov             x1, x0
    // 0xab12f8: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0xab12f8: add             x4, PP, #0xb, lsl #12  ; [pp+0xbcc0] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0xab12fc: ldr             x4, [x4, #0xcc0]
    // 0xab1300: r0 = List.from()
    //     0xab1300: bl              #0x6b9500  ; [dart:core] List::List.from
    // 0xab1304: ldur            x16, [fp, #-8]
    // 0xab1308: stp             x0, x16, [SP]
    // 0xab130c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab130c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab1310: r0 = makeFixedListUnmodifiable()
    //     0xab1310: bl              #0x8b7f28  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0xab1314: LeaveFrame
    //     0xab1314: mov             SP, fp
    //     0xab1318: ldp             fp, lr, [SP], #0x10
    // 0xab131c: ret
    //     0xab131c: ret             
    // 0xab1320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1320: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1324: b               #0xab12d8
  }
  [closure] void onError(dynamic, Object, StackTrace) {
    // ** addr: 0xab1410, size: 0x40
    // 0xab1410: EnterFrame
    //     0xab1410: stp             fp, lr, [SP, #-0x10]!
    //     0xab1414: mov             fp, SP
    // 0xab1418: ldr             x0, [fp, #0x20]
    // 0xab141c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab141c: ldur            w1, [x0, #0x17]
    // 0xab1420: DecompressPointer r1
    //     0xab1420: add             x1, x1, HEAP, lsl #32
    // 0xab1424: CheckStackOverflow
    //     0xab1424: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1428: cmp             SP, x16
    //     0xab142c: b.ls            #0xab1448
    // 0xab1430: ldr             x2, [fp, #0x18]
    // 0xab1434: ldr             x3, [fp, #0x10]
    // 0xab1438: r0 = onError()
    //     0xab1438: bl              #0xab1450  ; [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onError
    // 0xab143c: LeaveFrame
    //     0xab143c: mov             SP, fp
    //     0xab1440: ldp             fp, lr, [SP], #0x10
    // 0xab1444: ret
    //     0xab1444: ret             
    // 0xab1448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab144c: b               #0xab1430
  }
  [closure] void onData(dynamic, Object?) {
    // ** addr: 0xab1558, size: 0x3c
    // 0xab1558: EnterFrame
    //     0xab1558: stp             fp, lr, [SP, #-0x10]!
    //     0xab155c: mov             fp, SP
    // 0xab1560: ldr             x0, [fp, #0x18]
    // 0xab1564: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab1564: ldur            w1, [x0, #0x17]
    // 0xab1568: DecompressPointer r1
    //     0xab1568: add             x1, x1, HEAP, lsl #32
    // 0xab156c: CheckStackOverflow
    //     0xab156c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1570: cmp             SP, x16
    //     0xab1574: b.ls            #0xab158c
    // 0xab1578: ldr             x2, [fp, #0x10]
    // 0xab157c: r0 = onData()
    //     0xab157c: bl              #0xab1594  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::onData
    // 0xab1580: LeaveFrame
    //     0xab1580: mov             SP, fp
    //     0xab1584: ldp             fp, lr, [SP], #0x10
    // 0xab1588: ret
    //     0xab1588: ret             
    // 0xab158c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab158c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1590: b               #0xab1578
  }
  _ onData(/* No info */) {
    // ** addr: 0xab1594, size: 0x12c
    // 0xab1594: EnterFrame
    //     0xab1594: stp             fp, lr, [SP, #-0x10]!
    //     0xab1598: mov             fp, SP
    // 0xab159c: AllocStack(0x10)
    //     0xab159c: sub             SP, SP, #0x10
    // 0xab15a0: SetupParameters(_BackpressureStreamSink<X0, X1> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xab15a0: mov             x4, x1
    //     0xab15a4: mov             x3, x2
    //     0xab15a8: stur            x1, [fp, #-8]
    //     0xab15ac: stur            x2, [fp, #-0x10]
    // 0xab15b0: CheckStackOverflow
    //     0xab15b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab15b4: cmp             SP, x16
    //     0xab15b8: b.ls            #0xab16b8
    // 0xab15bc: LoadField: r2 = r4->field_7
    //     0xab15bc: ldur            w2, [x4, #7]
    // 0xab15c0: DecompressPointer r2
    //     0xab15c0: add             x2, x2, HEAP, lsl #32
    // 0xab15c4: mov             x0, x3
    // 0xab15c8: r1 = Null
    //     0xab15c8: mov             x1, NULL
    // 0xab15cc: cmp             w2, NULL
    // 0xab15d0: b.eq            #0xab15f0
    // 0xab15d4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xab15d4: ldur            w4, [x2, #0x17]
    // 0xab15d8: DecompressPointer r4
    //     0xab15d8: add             x4, x4, HEAP, lsl #32
    // 0xab15dc: r8 = X0
    //     0xab15dc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xab15e0: LoadField: r9 = r4->field_7
    //     0xab15e0: ldur            x9, [x4, #7]
    // 0xab15e4: r3 = Null
    //     0xab15e4: add             x3, PP, #0xe, lsl #12  ; [pp+0xedc0] Null
    //     0xab15e8: ldr             x3, [x3, #0xdc0]
    // 0xab15ec: blr             x9
    // 0xab15f0: ldur            x4, [fp, #-8]
    // 0xab15f4: r0 = true
    //     0xab15f4: add             x0, NULL, #0x20  ; true
    // 0xab15f8: StoreField: r4->field_43 = r0
    //     0xab15f8: stur            w0, [x4, #0x43]
    // 0xab15fc: LoadField: r3 = r4->field_b
    //     0xab15fc: ldur            w3, [x4, #0xb]
    // 0xab1600: DecompressPointer r3
    //     0xab1600: add             x3, x3, HEAP, lsl #32
    // 0xab1604: cmp             w3, NULL
    // 0xab1608: b.eq            #0xab1670
    // 0xab160c: mov             x1, x4
    // 0xab1610: ldur            x2, [fp, #-0x10]
    // 0xab1614: r0 = maybeCreateWindow()
    //     0xab1614: bl              #0xab1a38  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::maybeCreateWindow
    // 0xab1618: ldur            x0, [fp, #-8]
    // 0xab161c: LoadField: r1 = r0->field_3b
    //     0xab161c: ldur            x1, [x0, #0x3b]
    // 0xab1620: cbnz            x1, #0xab1634
    // 0xab1624: LoadField: r1 = r0->field_33
    //     0xab1624: ldur            w1, [x0, #0x33]
    // 0xab1628: DecompressPointer r1
    //     0xab1628: add             x1, x1, HEAP, lsl #32
    // 0xab162c: ldur            x2, [fp, #-0x10]
    // 0xab1630: r0 = add()
    //     0xab1630: bl              #0xab1750  ; [dart:collection] DoubleLinkedQueue::add
    // 0xab1634: ldur            x1, [fp, #-8]
    // 0xab1638: LoadField: r0 = r1->field_3b
    //     0xab1638: ldur            x0, [x1, #0x3b]
    // 0xab163c: cmp             x0, #0
    // 0xab1640: b.le            #0xab164c
    // 0xab1644: sub             x2, x0, #1
    // 0xab1648: StoreField: r1->field_3b = r2
    //     0xab1648: stur            x2, [x1, #0x3b]
    // 0xab164c: LoadField: r2 = r1->field_b
    //     0xab164c: ldur            w2, [x1, #0xb]
    // 0xab1650: DecompressPointer r2
    //     0xab1650: add             x2, x2, HEAP, lsl #32
    // 0xab1654: cmp             w2, NULL
    // 0xab1658: b.eq            #0xab1690
    // 0xab165c: r0 = maybeCloseWindow()
    //     0xab165c: bl              #0xab16c0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::maybeCloseWindow
    // 0xab1660: r0 = Null
    //     0xab1660: mov             x0, NULL
    // 0xab1664: LeaveFrame
    //     0xab1664: mov             SP, fp
    //     0xab1668: ldp             fp, lr, [SP], #0x10
    // 0xab166c: ret
    //     0xab166c: ret             
    // 0xab1670: r0 = StateError()
    //     0xab1670: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab1674: mov             x1, x0
    // 0xab1678: r0 = "Must call setSink(sink) before accessing!"
    //     0xab1678: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab167c: ldr             x0, [x0, #0xcd0]
    // 0xab1680: StoreField: r1->field_b = r0
    //     0xab1680: stur            w0, [x1, #0xb]
    // 0xab1684: mov             x0, x1
    // 0xab1688: r0 = Throw()
    //     0xab1688: bl              #0xec04b8  ; ThrowStub
    // 0xab168c: brk             #0
    // 0xab1690: r0 = "Must call setSink(sink) before accessing!"
    //     0xab1690: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab1694: ldr             x0, [x0, #0xcd0]
    // 0xab1698: r0 = StateError()
    //     0xab1698: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab169c: mov             x1, x0
    // 0xab16a0: r0 = "Must call setSink(sink) before accessing!"
    //     0xab16a0: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab16a4: ldr             x0, [x0, #0xcd0]
    // 0xab16a8: StoreField: r1->field_b = r0
    //     0xab16a8: stur            w0, [x1, #0xb]
    // 0xab16ac: mov             x0, x1
    // 0xab16b0: r0 = Throw()
    //     0xab16b0: bl              #0xec04b8  ; ThrowStub
    // 0xab16b4: brk             #0
    // 0xab16b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab16b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab16bc: b               #0xab15bc
  }
  _ maybeCloseWindow(/* No info */) {
    // ** addr: 0xab16c0, size: 0x90
    // 0xab16c0: EnterFrame
    //     0xab16c0: stp             fp, lr, [SP, #-0x10]!
    //     0xab16c4: mov             fp, SP
    // 0xab16c8: AllocStack(0x28)
    //     0xab16c8: sub             SP, SP, #0x28
    // 0xab16cc: SetupParameters(_BackpressureStreamSink<X0, X1> this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xab16cc: mov             x0, x1
    //     0xab16d0: stur            x1, [fp, #-0x10]
    //     0xab16d4: stur            x2, [fp, #-0x18]
    // 0xab16d8: CheckStackOverflow
    //     0xab16d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab16dc: cmp             SP, x16
    //     0xab16e0: b.ls            #0xab1744
    // 0xab16e4: LoadField: r3 = r0->field_27
    //     0xab16e4: ldur            w3, [x0, #0x27]
    // 0xab16e8: DecompressPointer r3
    //     0xab16e8: add             x3, x3, HEAP, lsl #32
    // 0xab16ec: mov             x1, x0
    // 0xab16f0: stur            x3, [fp, #-8]
    // 0xab16f4: r0 = unmodifiableQueue()
    //     0xab16f4: bl              #0xab12c0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::unmodifiableQueue
    // 0xab16f8: mov             x1, x0
    // 0xab16fc: ldur            x0, [fp, #-8]
    // 0xab1700: cmp             w0, NULL
    // 0xab1704: b.eq            #0xab174c
    // 0xab1708: stp             x1, x0, [SP]
    // 0xab170c: ClosureCall
    //     0xab170c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xab1710: ldur            x2, [x0, #0x1f]
    //     0xab1714: blr             x2
    // 0xab1718: r16 = true
    //     0xab1718: add             x16, NULL, #0x20  ; true
    // 0xab171c: cmp             w0, w16
    // 0xab1720: b.ne            #0xab1734
    // 0xab1724: ldur            x1, [fp, #-0x10]
    // 0xab1728: ldur            x2, [fp, #-0x18]
    // 0xab172c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab172c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab1730: r0 = resolveWindowEnd()
    //     0xab1730: bl              #0xab0ef0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::resolveWindowEnd
    // 0xab1734: r0 = Null
    //     0xab1734: mov             x0, NULL
    // 0xab1738: LeaveFrame
    //     0xab1738: mov             SP, fp
    //     0xab173c: ldp             fp, lr, [SP], #0x10
    // 0xab1740: ret
    //     0xab1740: ret             
    // 0xab1744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1744: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1748: b               #0xab16e4
    // 0xab174c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xab174c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ maybeCreateWindow(/* No info */) {
    // ** addr: 0xab1a38, size: 0x16c
    // 0xab1a38: EnterFrame
    //     0xab1a38: stp             fp, lr, [SP, #-0x10]!
    //     0xab1a3c: mov             fp, SP
    // 0xab1a40: AllocStack(0x18)
    //     0xab1a40: sub             SP, SP, #0x18
    // 0xab1a44: SetupParameters(_BackpressureStreamSink<X0, X1> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xab1a44: mov             x4, x1
    //     0xab1a48: stur            x1, [fp, #-8]
    //     0xab1a4c: stur            x2, [fp, #-0x10]
    //     0xab1a50: stur            x3, [fp, #-0x18]
    // 0xab1a54: CheckStackOverflow
    //     0xab1a54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1a58: cmp             SP, x16
    //     0xab1a5c: b.ls            #0xab1b9c
    // 0xab1a60: LoadField: r0 = r4->field_f
    //     0xab1a60: ldur            w0, [x4, #0xf]
    // 0xab1a64: DecompressPointer r0
    //     0xab1a64: add             x0, x0, HEAP, lsl #32
    // 0xab1a68: LoadField: r1 = r0->field_7
    //     0xab1a68: ldur            x1, [x0, #7]
    // 0xab1a6c: cmp             x1, #1
    // 0xab1a70: b.gt            #0xab1b34
    // 0xab1a74: cmp             x1, #0
    // 0xab1a78: b.gt            #0xab1ae0
    // 0xab1a7c: LoadField: r1 = r4->field_4b
    //     0xab1a7c: ldur            w1, [x4, #0x4b]
    // 0xab1a80: DecompressPointer r1
    //     0xab1a80: add             x1, x1, HEAP, lsl #32
    // 0xab1a84: cmp             w1, NULL
    // 0xab1a88: b.ne            #0xab1a94
    // 0xab1a8c: mov             x0, x4
    // 0xab1a90: b               #0xab1aac
    // 0xab1a94: r0 = LoadClassIdInstr(r1)
    //     0xab1a94: ldur            x0, [x1, #-1]
    //     0xab1a98: ubfx            x0, x0, #0xc, #0x14
    // 0xab1a9c: r0 = GDT[cid_x0 + -0x37]()
    //     0xab1a9c: sub             lr, x0, #0x37
    //     0xab1aa0: ldr             lr, [x21, lr, lsl #3]
    //     0xab1aa4: blr             lr
    // 0xab1aa8: ldur            x0, [fp, #-8]
    // 0xab1aac: mov             x1, x0
    // 0xab1ab0: ldur            x2, [fp, #-0x10]
    // 0xab1ab4: ldur            x3, [fp, #-0x18]
    // 0xab1ab8: r0 = singleWindow()
    //     0xab1ab8: bl              #0xab1db0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::singleWindow
    // 0xab1abc: ldur            x4, [fp, #-8]
    // 0xab1ac0: StoreField: r4->field_4b = r0
    //     0xab1ac0: stur            w0, [x4, #0x4b]
    //     0xab1ac4: ldurb           w16, [x4, #-1]
    //     0xab1ac8: ldurb           w17, [x0, #-1]
    //     0xab1acc: and             x16, x17, x16, lsr #2
    //     0xab1ad0: tst             x16, HEAP, lsr #32
    //     0xab1ad4: b.eq            #0xab1adc
    //     0xab1ad8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xab1adc: b               #0xab1b8c
    // 0xab1ae0: LoadField: r0 = r4->field_4b
    //     0xab1ae0: ldur            w0, [x4, #0x4b]
    // 0xab1ae4: DecompressPointer r0
    //     0xab1ae4: add             x0, x0, HEAP, lsl #32
    // 0xab1ae8: cmp             w0, NULL
    // 0xab1aec: b.eq            #0xab1b00
    // 0xab1af0: r0 = Null
    //     0xab1af0: mov             x0, NULL
    // 0xab1af4: LeaveFrame
    //     0xab1af4: mov             SP, fp
    //     0xab1af8: ldp             fp, lr, [SP], #0x10
    // 0xab1afc: ret
    //     0xab1afc: ret             
    // 0xab1b00: mov             x1, x4
    // 0xab1b04: ldur            x2, [fp, #-0x10]
    // 0xab1b08: ldur            x3, [fp, #-0x18]
    // 0xab1b0c: r0 = singleWindow()
    //     0xab1b0c: bl              #0xab1db0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::singleWindow
    // 0xab1b10: ldur            x4, [fp, #-8]
    // 0xab1b14: StoreField: r4->field_4b = r0
    //     0xab1b14: stur            w0, [x4, #0x4b]
    //     0xab1b18: ldurb           w16, [x4, #-1]
    //     0xab1b1c: ldurb           w17, [x0, #-1]
    //     0xab1b20: and             x16, x17, x16, lsr #2
    //     0xab1b24: tst             x16, HEAP, lsr #32
    //     0xab1b28: b.eq            #0xab1b30
    //     0xab1b2c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xab1b30: b               #0xab1b8c
    // 0xab1b34: cmp             x1, #2
    // 0xab1b38: b.gt            #0xab1b8c
    // 0xab1b3c: LoadField: r0 = r4->field_4b
    //     0xab1b3c: ldur            w0, [x4, #0x4b]
    // 0xab1b40: DecompressPointer r0
    //     0xab1b40: add             x0, x0, HEAP, lsl #32
    // 0xab1b44: cmp             w0, NULL
    // 0xab1b48: b.eq            #0xab1b5c
    // 0xab1b4c: r0 = Null
    //     0xab1b4c: mov             x0, NULL
    // 0xab1b50: LeaveFrame
    //     0xab1b50: mov             SP, fp
    //     0xab1b54: ldp             fp, lr, [SP], #0x10
    // 0xab1b58: ret
    //     0xab1b58: ret             
    // 0xab1b5c: mov             x1, x4
    // 0xab1b60: ldur            x2, [fp, #-0x10]
    // 0xab1b64: ldur            x3, [fp, #-0x18]
    // 0xab1b68: r0 = multiWindow()
    //     0xab1b68: bl              #0xab1ba4  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::multiWindow
    // 0xab1b6c: ldur            x1, [fp, #-8]
    // 0xab1b70: StoreField: r1->field_4b = r0
    //     0xab1b70: stur            w0, [x1, #0x4b]
    //     0xab1b74: ldurb           w16, [x1, #-1]
    //     0xab1b78: ldurb           w17, [x0, #-1]
    //     0xab1b7c: and             x16, x17, x16, lsr #2
    //     0xab1b80: tst             x16, HEAP, lsr #32
    //     0xab1b84: b.eq            #0xab1b8c
    //     0xab1b88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab1b8c: r0 = Null
    //     0xab1b8c: mov             x0, NULL
    // 0xab1b90: LeaveFrame
    //     0xab1b90: mov             SP, fp
    //     0xab1b94: ldp             fp, lr, [SP], #0x10
    // 0xab1b98: ret
    //     0xab1b98: ret             
    // 0xab1b9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1b9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1ba0: b               #0xab1a60
  }
  _ multiWindow(/* No info */) {
    // ** addr: 0xab1ba4, size: 0xd0
    // 0xab1ba4: EnterFrame
    //     0xab1ba4: stp             fp, lr, [SP, #-0x10]!
    //     0xab1ba8: mov             fp, SP
    // 0xab1bac: AllocStack(0x30)
    //     0xab1bac: sub             SP, SP, #0x30
    // 0xab1bb0: SetupParameters(_BackpressureStreamSink<X0, X1> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xab1bb0: stur            x1, [fp, #-8]
    //     0xab1bb4: stur            x2, [fp, #-0x10]
    //     0xab1bb8: stur            x3, [fp, #-0x18]
    // 0xab1bbc: CheckStackOverflow
    //     0xab1bbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1bc0: cmp             SP, x16
    //     0xab1bc4: b.ls            #0xab1c6c
    // 0xab1bc8: r1 = 2
    //     0xab1bc8: movz            x1, #0x2
    // 0xab1bcc: r0 = AllocateContext()
    //     0xab1bcc: bl              #0xec126c  ; AllocateContextStub
    // 0xab1bd0: ldur            x1, [fp, #-8]
    // 0xab1bd4: stur            x0, [fp, #-0x20]
    // 0xab1bd8: StoreField: r0->field_f = r1
    //     0xab1bd8: stur            w1, [x0, #0xf]
    // 0xab1bdc: ldur            x3, [fp, #-0x18]
    // 0xab1be0: StoreField: r0->field_13 = r3
    //     0xab1be0: stur            w3, [x0, #0x13]
    // 0xab1be4: ldur            x2, [fp, #-0x10]
    // 0xab1be8: r0 = buildStream()
    //     0xab1be8: bl              #0xab1c74  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::buildStream
    // 0xab1bec: mov             x1, x0
    // 0xab1bf0: ldur            x2, [fp, #-0x20]
    // 0xab1bf4: stur            x1, [fp, #-8]
    // 0xab1bf8: LoadField: r0 = r2->field_13
    //     0xab1bf8: ldur            w0, [x2, #0x13]
    // 0xab1bfc: DecompressPointer r0
    //     0xab1bfc: add             x0, x0, HEAP, lsl #32
    // 0xab1c00: r3 = LoadClassIdInstr(r0)
    //     0xab1c00: ldur            x3, [x0, #-1]
    //     0xab1c04: ubfx            x3, x3, #0xc, #0x14
    // 0xab1c08: str             x0, [SP]
    // 0xab1c0c: mov             x0, x3
    // 0xab1c10: r0 = GDT[cid_x0 + 0x9ba]()
    //     0xab1c10: add             lr, x0, #0x9ba
    //     0xab1c14: ldr             lr, [x21, lr, lsl #3]
    //     0xab1c18: blr             lr
    // 0xab1c1c: ldur            x2, [fp, #-0x20]
    // 0xab1c20: r1 = Function '<anonymous closure>':.
    //     0xab1c20: add             x1, PP, #0xe, lsl #12  ; [pp+0xee40] AnonymousClosure: (0xab1d5c), in [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::multiWindow (0xab1ba4)
    //     0xab1c24: ldr             x1, [x1, #0xe40]
    // 0xab1c28: stur            x0, [fp, #-0x10]
    // 0xab1c2c: r0 = AllocateClosure()
    //     0xab1c2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xab1c30: ldur            x2, [fp, #-0x20]
    // 0xab1c34: r1 = Function '<anonymous closure>':.
    //     0xab1c34: add             x1, PP, #0xe, lsl #12  ; [pp+0xee48] AnonymousClosure: (0xab1d08), in [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::multiWindow (0xab1ba4)
    //     0xab1c38: ldr             x1, [x1, #0xe48]
    // 0xab1c3c: stur            x0, [fp, #-0x18]
    // 0xab1c40: r0 = AllocateClosure()
    //     0xab1c40: bl              #0xec1630  ; AllocateClosureStub
    // 0xab1c44: ldur            x16, [fp, #-0x10]
    // 0xab1c48: stp             x0, x16, [SP]
    // 0xab1c4c: ldur            x1, [fp, #-8]
    // 0xab1c50: ldur            x2, [fp, #-0x18]
    // 0xab1c54: r4 = const [0, 0x4, 0x2, 0x2, onDone, 0x3, onError, 0x2, null]
    //     0xab1c54: add             x4, PP, #0xc, lsl #12  ; [pp+0xc168] List(9) [0, 0x4, 0x2, 0x2, "onDone", 0x3, "onError", 0x2, Null]
    //     0xab1c58: ldr             x4, [x4, #0x168]
    // 0xab1c5c: r0 = listen()
    //     0xab1c5c: bl              #0xd10f60  ; [package:rxdart/src/streams/never.dart] NeverStream::listen
    // 0xab1c60: LeaveFrame
    //     0xab1c60: mov             SP, fp
    //     0xab1c64: ldp             fp, lr, [SP], #0x10
    // 0xab1c68: ret
    //     0xab1c68: ret             
    // 0xab1c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1c6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1c70: b               #0xab1bc8
  }
  _ buildStream(/* No info */) {
    // ** addr: 0xab1c74, size: 0x94
    // 0xab1c74: EnterFrame
    //     0xab1c74: stp             fp, lr, [SP, #-0x10]!
    //     0xab1c78: mov             fp, SP
    // 0xab1c7c: AllocStack(0x20)
    //     0xab1c7c: sub             SP, SP, #0x20
    // 0xab1c80: SetupParameters(_BackpressureStreamSink<X0, X1> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xab1c80: mov             x4, x1
    //     0xab1c84: stur            x1, [fp, #-8]
    //     0xab1c88: stur            x2, [fp, #-0x10]
    // 0xab1c8c: CheckStackOverflow
    //     0xab1c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1c90: cmp             SP, x16
    //     0xab1c94: b.ls            #0xab1cfc
    // 0xab1c98: LoadField: r1 = r4->field_4b
    //     0xab1c98: ldur            w1, [x4, #0x4b]
    // 0xab1c9c: DecompressPointer r1
    //     0xab1c9c: add             x1, x1, HEAP, lsl #32
    // 0xab1ca0: cmp             w1, NULL
    // 0xab1ca4: b.ne            #0xab1cb0
    // 0xab1ca8: mov             x0, x4
    // 0xab1cac: b               #0xab1cc8
    // 0xab1cb0: r0 = LoadClassIdInstr(r1)
    //     0xab1cb0: ldur            x0, [x1, #-1]
    //     0xab1cb4: ubfx            x0, x0, #0xc, #0x14
    // 0xab1cb8: r0 = GDT[cid_x0 + -0x37]()
    //     0xab1cb8: sub             lr, x0, #0x37
    //     0xab1cbc: ldr             lr, [x21, lr, lsl #3]
    //     0xab1cc0: blr             lr
    // 0xab1cc4: ldur            x0, [fp, #-8]
    // 0xab1cc8: LoadField: r1 = r0->field_13
    //     0xab1cc8: ldur            w1, [x0, #0x13]
    // 0xab1ccc: DecompressPointer r1
    //     0xab1ccc: add             x1, x1, HEAP, lsl #32
    // 0xab1cd0: cmp             w1, NULL
    // 0xab1cd4: b.eq            #0xab1d04
    // 0xab1cd8: ldur            x16, [fp, #-0x10]
    // 0xab1cdc: stp             x16, x1, [SP]
    // 0xab1ce0: mov             x0, x1
    // 0xab1ce4: ClosureCall
    //     0xab1ce4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xab1ce8: ldur            x2, [x0, #0x1f]
    //     0xab1cec: blr             x2
    // 0xab1cf0: LeaveFrame
    //     0xab1cf0: mov             SP, fp
    //     0xab1cf4: ldp             fp, lr, [SP], #0x10
    // 0xab1cf8: ret
    //     0xab1cf8: ret             
    // 0xab1cfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1cfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1d00: b               #0xab1c98
    // 0xab1d04: r0 = NullErrorSharedWithoutFPURegs()
    //     0xab1d04: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xab1d08, size: 0x54
    // 0xab1d08: EnterFrame
    //     0xab1d08: stp             fp, lr, [SP, #-0x10]!
    //     0xab1d0c: mov             fp, SP
    // 0xab1d10: ldr             x0, [fp, #0x10]
    // 0xab1d14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab1d14: ldur            w1, [x0, #0x17]
    // 0xab1d18: DecompressPointer r1
    //     0xab1d18: add             x1, x1, HEAP, lsl #32
    // 0xab1d1c: CheckStackOverflow
    //     0xab1d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1d20: cmp             SP, x16
    //     0xab1d24: b.ls            #0xab1d54
    // 0xab1d28: LoadField: r0 = r1->field_f
    //     0xab1d28: ldur            w0, [x1, #0xf]
    // 0xab1d2c: DecompressPointer r0
    //     0xab1d2c: add             x0, x0, HEAP, lsl #32
    // 0xab1d30: LoadField: r2 = r1->field_13
    //     0xab1d30: ldur            w2, [x1, #0x13]
    // 0xab1d34: DecompressPointer r2
    //     0xab1d34: add             x2, x2, HEAP, lsl #32
    // 0xab1d38: mov             x1, x0
    // 0xab1d3c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab1d3c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab1d40: r0 = resolveWindowEnd()
    //     0xab1d40: bl              #0xab0ef0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::resolveWindowEnd
    // 0xab1d44: r0 = Null
    //     0xab1d44: mov             x0, NULL
    // 0xab1d48: LeaveFrame
    //     0xab1d48: mov             SP, fp
    //     0xab1d4c: ldp             fp, lr, [SP], #0x10
    // 0xab1d50: ret
    //     0xab1d50: ret             
    // 0xab1d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1d54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1d58: b               #0xab1d28
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xab1d5c, size: 0x54
    // 0xab1d5c: EnterFrame
    //     0xab1d5c: stp             fp, lr, [SP, #-0x10]!
    //     0xab1d60: mov             fp, SP
    // 0xab1d64: ldr             x0, [fp, #0x18]
    // 0xab1d68: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab1d68: ldur            w1, [x0, #0x17]
    // 0xab1d6c: DecompressPointer r1
    //     0xab1d6c: add             x1, x1, HEAP, lsl #32
    // 0xab1d70: CheckStackOverflow
    //     0xab1d70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1d74: cmp             SP, x16
    //     0xab1d78: b.ls            #0xab1da8
    // 0xab1d7c: LoadField: r0 = r1->field_f
    //     0xab1d7c: ldur            w0, [x1, #0xf]
    // 0xab1d80: DecompressPointer r0
    //     0xab1d80: add             x0, x0, HEAP, lsl #32
    // 0xab1d84: LoadField: r2 = r1->field_13
    //     0xab1d84: ldur            w2, [x1, #0x13]
    // 0xab1d88: DecompressPointer r2
    //     0xab1d88: add             x2, x2, HEAP, lsl #32
    // 0xab1d8c: mov             x1, x0
    // 0xab1d90: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab1d90: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab1d94: r0 = resolveWindowEnd()
    //     0xab1d94: bl              #0xab0ef0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::resolveWindowEnd
    // 0xab1d98: r0 = Null
    //     0xab1d98: mov             x0, NULL
    // 0xab1d9c: LeaveFrame
    //     0xab1d9c: mov             SP, fp
    //     0xab1da0: ldp             fp, lr, [SP], #0x10
    // 0xab1da4: ret
    //     0xab1da4: ret             
    // 0xab1da8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1da8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1dac: b               #0xab1d7c
  }
  _ singleWindow(/* No info */) {
    // ** addr: 0xab1db0, size: 0xc4
    // 0xab1db0: EnterFrame
    //     0xab1db0: stp             fp, lr, [SP, #-0x10]!
    //     0xab1db4: mov             fp, SP
    // 0xab1db8: AllocStack(0x30)
    //     0xab1db8: sub             SP, SP, #0x30
    // 0xab1dbc: SetupParameters(_BackpressureStreamSink<X0, X1> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xab1dbc: stur            x1, [fp, #-8]
    //     0xab1dc0: stur            x2, [fp, #-0x10]
    //     0xab1dc4: stur            x3, [fp, #-0x18]
    // 0xab1dc8: CheckStackOverflow
    //     0xab1dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1dcc: cmp             SP, x16
    //     0xab1dd0: b.ls            #0xab1e6c
    // 0xab1dd4: r1 = 2
    //     0xab1dd4: movz            x1, #0x2
    // 0xab1dd8: r0 = AllocateContext()
    //     0xab1dd8: bl              #0xec126c  ; AllocateContextStub
    // 0xab1ddc: ldur            x1, [fp, #-8]
    // 0xab1de0: stur            x0, [fp, #-0x20]
    // 0xab1de4: StoreField: r0->field_f = r1
    //     0xab1de4: stur            w1, [x0, #0xf]
    // 0xab1de8: ldur            x3, [fp, #-0x18]
    // 0xab1dec: StoreField: r0->field_13 = r3
    //     0xab1dec: stur            w3, [x0, #0x13]
    // 0xab1df0: ldur            x2, [fp, #-0x10]
    // 0xab1df4: r0 = buildStream()
    //     0xab1df4: bl              #0xab1c74  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::buildStream
    // 0xab1df8: mov             x1, x0
    // 0xab1dfc: r0 = take()
    //     0xab1dfc: bl              #0xab1e74  ; [dart:async] Stream::take
    // 0xab1e00: mov             x1, x0
    // 0xab1e04: ldur            x2, [fp, #-0x20]
    // 0xab1e08: stur            x1, [fp, #-8]
    // 0xab1e0c: LoadField: r0 = r2->field_13
    //     0xab1e0c: ldur            w0, [x2, #0x13]
    // 0xab1e10: DecompressPointer r0
    //     0xab1e10: add             x0, x0, HEAP, lsl #32
    // 0xab1e14: r3 = LoadClassIdInstr(r0)
    //     0xab1e14: ldur            x3, [x0, #-1]
    //     0xab1e18: ubfx            x3, x3, #0xc, #0x14
    // 0xab1e1c: str             x0, [SP]
    // 0xab1e20: mov             x0, x3
    // 0xab1e24: r0 = GDT[cid_x0 + 0x9ba]()
    //     0xab1e24: add             lr, x0, #0x9ba
    //     0xab1e28: ldr             lr, [x21, lr, lsl #3]
    //     0xab1e2c: blr             lr
    // 0xab1e30: ldur            x2, [fp, #-0x20]
    // 0xab1e34: r1 = Function '<anonymous closure>':.
    //     0xab1e34: add             x1, PP, #0xe, lsl #12  ; [pp+0xee50] AnonymousClosure: (0xab1ee4), in [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::singleWindow (0xab1db0)
    //     0xab1e38: ldr             x1, [x1, #0xe50]
    // 0xab1e3c: stur            x0, [fp, #-0x10]
    // 0xab1e40: r0 = AllocateClosure()
    //     0xab1e40: bl              #0xec1630  ; AllocateClosureStub
    // 0xab1e44: ldur            x16, [fp, #-0x10]
    // 0xab1e48: stp             x0, x16, [SP]
    // 0xab1e4c: ldur            x1, [fp, #-8]
    // 0xab1e50: r2 = Null
    //     0xab1e50: mov             x2, NULL
    // 0xab1e54: r4 = const [0, 0x4, 0x2, 0x2, onDone, 0x3, onError, 0x2, null]
    //     0xab1e54: add             x4, PP, #0xc, lsl #12  ; [pp+0xc168] List(9) [0, 0x4, 0x2, 0x2, "onDone", 0x3, "onError", 0x2, Null]
    //     0xab1e58: ldr             x4, [x4, #0x168]
    // 0xab1e5c: r0 = listen()
    //     0xab1e5c: bl              #0xd0b8d8  ; [dart:async] _ForwardingStream::listen
    // 0xab1e60: LeaveFrame
    //     0xab1e60: mov             SP, fp
    //     0xab1e64: ldp             fp, lr, [SP], #0x10
    // 0xab1e68: ret
    //     0xab1e68: ret             
    // 0xab1e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1e6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1e70: b               #0xab1dd4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xab1ee4, size: 0x64
    // 0xab1ee4: EnterFrame
    //     0xab1ee4: stp             fp, lr, [SP, #-0x10]!
    //     0xab1ee8: mov             fp, SP
    // 0xab1eec: AllocStack(0x8)
    //     0xab1eec: sub             SP, SP, #8
    // 0xab1ef0: SetupParameters()
    //     0xab1ef0: ldr             x0, [fp, #0x10]
    //     0xab1ef4: ldur            w1, [x0, #0x17]
    //     0xab1ef8: add             x1, x1, HEAP, lsl #32
    // 0xab1efc: CheckStackOverflow
    //     0xab1efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1f00: cmp             SP, x16
    //     0xab1f04: b.ls            #0xab1f40
    // 0xab1f08: LoadField: r0 = r1->field_f
    //     0xab1f08: ldur            w0, [x1, #0xf]
    // 0xab1f0c: DecompressPointer r0
    //     0xab1f0c: add             x0, x0, HEAP, lsl #32
    // 0xab1f10: LoadField: r2 = r1->field_13
    //     0xab1f10: ldur            w2, [x1, #0x13]
    // 0xab1f14: DecompressPointer r2
    //     0xab1f14: add             x2, x2, HEAP, lsl #32
    // 0xab1f18: LoadField: r1 = r0->field_47
    //     0xab1f18: ldur            w1, [x0, #0x47]
    // 0xab1f1c: DecompressPointer r1
    //     0xab1f1c: add             x1, x1, HEAP, lsl #32
    // 0xab1f20: str             x1, [SP]
    // 0xab1f24: mov             x1, x0
    // 0xab1f28: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xab1f28: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xab1f2c: r0 = resolveWindowEnd()
    //     0xab1f2c: bl              #0xab0ef0  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::resolveWindowEnd
    // 0xab1f30: r0 = Null
    //     0xab1f30: mov             x0, NULL
    // 0xab1f34: LeaveFrame
    //     0xab1f34: mov             SP, fp
    //     0xab1f38: ldp             fp, lr, [SP], #0x10
    // 0xab1f3c: ret
    //     0xab1f3c: ret             
    // 0xab1f40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1f40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1f44: b               #0xab1f08
  }
  _ _BackpressureStreamSink(/* No info */) {
    // ** addr: 0xcce1bc, size: 0x184
    // 0xcce1bc: EnterFrame
    //     0xcce1bc: stp             fp, lr, [SP, #-0x10]!
    //     0xcce1c0: mov             fp, SP
    // 0xcce1c4: AllocStack(0x30)
    //     0xcce1c4: sub             SP, SP, #0x30
    // 0xcce1c8: r0 = false
    //     0xcce1c8: add             x0, NULL, #0x30  ; false
    // 0xcce1cc: mov             x4, x2
    // 0xcce1d0: stur            x2, [fp, #-0x18]
    // 0xcce1d4: mov             x2, x5
    // 0xcce1d8: stur            x5, [fp, #-0x28]
    // 0xcce1dc: mov             x5, x1
    // 0xcce1e0: stur            x1, [fp, #-0x10]
    // 0xcce1e4: stur            x3, [fp, #-0x20]
    // 0xcce1e8: StoreField: r5->field_3b = rZR
    //     0xcce1e8: stur            xzr, [x5, #0x3b]
    // 0xcce1ec: StoreField: r5->field_43 = r0
    //     0xcce1ec: stur            w0, [x5, #0x43]
    // 0xcce1f0: StoreField: r5->field_47 = r0
    //     0xcce1f0: stur            w0, [x5, #0x47]
    // 0xcce1f4: LoadField: r6 = r5->field_7
    //     0xcce1f4: ldur            w6, [x5, #7]
    // 0xcce1f8: DecompressPointer r6
    //     0xcce1f8: add             x6, x6, HEAP, lsl #32
    // 0xcce1fc: mov             x1, x6
    // 0xcce200: stur            x6, [fp, #-8]
    // 0xcce204: r0 = DoubleLinkedQueue()
    //     0xcce204: bl              #0xcce34c  ; AllocateDoubleLinkedQueueStub -> DoubleLinkedQueue<X0> (size=0x18)
    // 0xcce208: stur            x0, [fp, #-0x30]
    // 0xcce20c: StoreField: r0->field_f = rZR
    //     0xcce20c: stur            xzr, [x0, #0xf]
    // 0xcce210: ldur            x1, [fp, #-8]
    // 0xcce214: r0 = _DoubleLinkedQueueSentinel()
    //     0xcce214: bl              #0xcce340  ; Allocate_DoubleLinkedQueueSentinelStub -> _DoubleLinkedQueueSentinel<X0> (size=0x14)
    // 0xcce218: ldur            x2, [fp, #-8]
    // 0xcce21c: mov             x3, x0
    // 0xcce220: r1 = Null
    //     0xcce220: mov             x1, NULL
    // 0xcce224: stur            x3, [fp, #-8]
    // 0xcce228: r8 = _DoubleLinkedQueueEntry<X0>?
    //     0xcce228: add             x8, PP, #0xe, lsl #12  ; [pp+0xec68] Type: _DoubleLinkedQueueEntry<X0>?
    //     0xcce22c: ldr             x8, [x8, #0xc68]
    // 0xcce230: LoadField: r9 = r8->field_7
    //     0xcce230: ldur            x9, [x8, #7]
    // 0xcce234: r3 = Null
    //     0xcce234: add             x3, PP, #0xe, lsl #12  ; [pp+0xec70] Null
    //     0xcce238: ldr             x3, [x3, #0xc70]
    // 0xcce23c: blr             x9
    // 0xcce240: ldur            x0, [fp, #-8]
    // 0xcce244: ldur            x1, [fp, #-8]
    // 0xcce248: StoreField: r1->field_b = r0
    //     0xcce248: stur            w0, [x1, #0xb]
    //     0xcce24c: ldurb           w16, [x1, #-1]
    //     0xcce250: ldurb           w17, [x0, #-1]
    //     0xcce254: and             x16, x17, x16, lsr #2
    //     0xcce258: tst             x16, HEAP, lsr #32
    //     0xcce25c: b.eq            #0xcce264
    //     0xcce260: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcce264: mov             x0, x1
    // 0xcce268: StoreField: r1->field_f = r0
    //     0xcce268: stur            w0, [x1, #0xf]
    //     0xcce26c: ldurb           w16, [x1, #-1]
    //     0xcce270: ldurb           w17, [x0, #-1]
    //     0xcce274: and             x16, x17, x16, lsr #2
    //     0xcce278: tst             x16, HEAP, lsr #32
    //     0xcce27c: b.eq            #0xcce284
    //     0xcce280: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcce284: ldur            x0, [fp, #-0x30]
    // 0xcce288: StoreField: r0->field_b = r1
    //     0xcce288: stur            w1, [x0, #0xb]
    // 0xcce28c: ldur            x1, [fp, #-0x10]
    // 0xcce290: StoreField: r1->field_33 = r0
    //     0xcce290: stur            w0, [x1, #0x33]
    //     0xcce294: ldurb           w16, [x1, #-1]
    //     0xcce298: ldurb           w17, [x0, #-1]
    //     0xcce29c: and             x16, x17, x16, lsr #2
    //     0xcce2a0: tst             x16, HEAP, lsr #32
    //     0xcce2a4: b.eq            #0xcce2ac
    //     0xcce2a8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcce2ac: r2 = Instance_WindowStrategy
    //     0xcce2ac: add             x2, PP, #0xe, lsl #12  ; [pp+0xec80] Obj!WindowStrategy@e2e1e1
    //     0xcce2b0: ldr             x2, [x2, #0xc80]
    // 0xcce2b4: StoreField: r1->field_f = r2
    //     0xcce2b4: stur            w2, [x1, #0xf]
    // 0xcce2b8: ldur            x0, [fp, #-0x18]
    // 0xcce2bc: StoreField: r1->field_13 = r0
    //     0xcce2bc: stur            w0, [x1, #0x13]
    //     0xcce2c0: ldurb           w16, [x1, #-1]
    //     0xcce2c4: ldurb           w17, [x0, #-1]
    //     0xcce2c8: and             x16, x17, x16, lsr #2
    //     0xcce2cc: tst             x16, HEAP, lsr #32
    //     0xcce2d0: b.eq            #0xcce2d8
    //     0xcce2d4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcce2d8: ldur            x0, [fp, #-0x20]
    // 0xcce2dc: StoreField: r1->field_1b = r0
    //     0xcce2dc: stur            w0, [x1, #0x1b]
    //     0xcce2e0: ldurb           w16, [x1, #-1]
    //     0xcce2e4: ldurb           w17, [x0, #-1]
    //     0xcce2e8: and             x16, x17, x16, lsr #2
    //     0xcce2ec: tst             x16, HEAP, lsr #32
    //     0xcce2f0: b.eq            #0xcce2f8
    //     0xcce2f4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcce2f8: r2 = 1
    //     0xcce2f8: movz            x2, #0x1
    // 0xcce2fc: StoreField: r1->field_1f = r2
    //     0xcce2fc: stur            x2, [x1, #0x1f]
    // 0xcce300: ldur            x0, [fp, #-0x28]
    // 0xcce304: StoreField: r1->field_27 = r0
    //     0xcce304: stur            w0, [x1, #0x27]
    //     0xcce308: ldurb           w16, [x1, #-1]
    //     0xcce30c: ldurb           w17, [x0, #-1]
    //     0xcce310: and             x16, x17, x16, lsr #2
    //     0xcce314: tst             x16, HEAP, lsr #32
    //     0xcce318: b.eq            #0xcce320
    //     0xcce31c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcce320: r2 = true
    //     0xcce320: add             x2, NULL, #0x20  ; true
    // 0xcce324: StoreField: r1->field_2b = r2
    //     0xcce324: stur            w2, [x1, #0x2b]
    // 0xcce328: r2 = false
    //     0xcce328: add             x2, NULL, #0x30  ; false
    // 0xcce32c: StoreField: r1->field_2f = r2
    //     0xcce32c: stur            w2, [x1, #0x2f]
    // 0xcce330: r0 = Null
    //     0xcce330: mov             x0, NULL
    // 0xcce334: LeaveFrame
    //     0xcce334: mov             SP, fp
    //     0xcce338: ldp             fp, lr, [SP], #0x10
    // 0xcce33c: ret
    //     0xcce33c: ret             
  }
}

// class id: 6615, size: 0x34, field offset: 0xc
abstract class BackpressureStreamTransformer<X0, X1> extends StreamTransformerBase<X0, X1> {

  _ bind(/* No info */) {
    // ** addr: 0xccda18, size: 0xac
    // 0xccda18: EnterFrame
    //     0xccda18: stp             fp, lr, [SP, #-0x10]!
    //     0xccda1c: mov             fp, SP
    // 0xccda20: AllocStack(0x38)
    //     0xccda20: sub             SP, SP, #0x38
    // 0xccda24: SetupParameters(BackpressureStreamTransformer<X0, X1> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xccda24: mov             x0, x2
    //     0xccda28: stur            x1, [fp, #-8]
    //     0xccda2c: stur            x2, [fp, #-0x10]
    // 0xccda30: CheckStackOverflow
    //     0xccda30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xccda34: cmp             SP, x16
    //     0xccda38: b.ls            #0xccdabc
    // 0xccda3c: r1 = 1
    //     0xccda3c: movz            x1, #0x1
    // 0xccda40: r0 = AllocateContext()
    //     0xccda40: bl              #0xec126c  ; AllocateContextStub
    // 0xccda44: mov             x3, x0
    // 0xccda48: ldur            x0, [fp, #-8]
    // 0xccda4c: stur            x3, [fp, #-0x20]
    // 0xccda50: StoreField: r3->field_f = r0
    //     0xccda50: stur            w0, [x3, #0xf]
    // 0xccda54: LoadField: r4 = r0->field_7
    //     0xccda54: ldur            w4, [x0, #7]
    // 0xccda58: DecompressPointer r4
    //     0xccda58: add             x4, x4, HEAP, lsl #32
    // 0xccda5c: ldur            x0, [fp, #-0x10]
    // 0xccda60: mov             x2, x4
    // 0xccda64: stur            x4, [fp, #-0x18]
    // 0xccda68: r1 = Null
    //     0xccda68: mov             x1, NULL
    // 0xccda6c: r8 = Stream<X0>
    //     0xccda6c: add             x8, PP, #0xc, lsl #12  ; [pp+0xcf10] Type: Stream<X0>
    //     0xccda70: ldr             x8, [x8, #0xf10]
    // 0xccda74: LoadField: r9 = r8->field_7
    //     0xccda74: ldur            x9, [x8, #7]
    // 0xccda78: r3 = Null
    //     0xccda78: add             x3, PP, #0xe, lsl #12  ; [pp+0xec50] Null
    //     0xccda7c: ldr             x3, [x3, #0xc50]
    // 0xccda80: blr             x9
    // 0xccda84: ldur            x2, [fp, #-0x20]
    // 0xccda88: ldur            x3, [fp, #-0x18]
    // 0xccda8c: r1 = Function '<anonymous closure>':.
    //     0xccda8c: add             x1, PP, #0xe, lsl #12  ; [pp+0xec60] AnonymousClosure: (0xcce130), in [package:rxdart/src/transformers/backpressure/backpressure.dart] BackpressureStreamTransformer::bind (0xccda18)
    //     0xccda90: ldr             x1, [x1, #0xc60]
    // 0xccda94: r0 = AllocateClosureTA()
    //     0xccda94: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xccda98: ldur            x16, [fp, #-0x18]
    // 0xccda9c: ldur            lr, [fp, #-0x10]
    // 0xccdaa0: stp             lr, x16, [SP, #8]
    // 0xccdaa4: str             x0, [SP]
    // 0xccdaa8: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xccdaa8: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xccdaac: r0 = forwardStream()
    //     0xccdaac: bl              #0xccdac4  ; [package:rxdart/src/utils/forwarding_stream.dart] ::forwardStream
    // 0xccdab0: LeaveFrame
    //     0xccdab0: mov             SP, fp
    //     0xccdab4: ldp             fp, lr, [SP], #0x10
    // 0xccdab8: ret
    //     0xccdab8: ret             
    // 0xccdabc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xccdabc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xccdac0: b               #0xccda3c
  }
  [closure] _BackpressureStreamSink<X0, X1> <anonymous closure>(dynamic) {
    // ** addr: 0xcce130, size: 0x8c
    // 0xcce130: EnterFrame
    //     0xcce130: stp             fp, lr, [SP, #-0x10]!
    //     0xcce134: mov             fp, SP
    // 0xcce138: AllocStack(0x18)
    //     0xcce138: sub             SP, SP, #0x18
    // 0xcce13c: SetupParameters()
    //     0xcce13c: ldr             x0, [fp, #0x10]
    //     0xcce140: ldur            w1, [x0, #0x17]
    //     0xcce144: add             x1, x1, HEAP, lsl #32
    // 0xcce148: CheckStackOverflow
    //     0xcce148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcce14c: cmp             SP, x16
    //     0xcce150: b.ls            #0xcce1b4
    // 0xcce154: LoadField: r0 = r1->field_f
    //     0xcce154: ldur            w0, [x1, #0xf]
    // 0xcce158: DecompressPointer r0
    //     0xcce158: add             x0, x0, HEAP, lsl #32
    // 0xcce15c: LoadField: r2 = r0->field_f
    //     0xcce15c: ldur            w2, [x0, #0xf]
    // 0xcce160: DecompressPointer r2
    //     0xcce160: add             x2, x2, HEAP, lsl #32
    // 0xcce164: stur            x2, [fp, #-0x18]
    // 0xcce168: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xcce168: ldur            w3, [x0, #0x17]
    // 0xcce16c: DecompressPointer r3
    //     0xcce16c: add             x3, x3, HEAP, lsl #32
    // 0xcce170: stur            x3, [fp, #-0x10]
    // 0xcce174: LoadField: r5 = r0->field_27
    //     0xcce174: ldur            w5, [x0, #0x27]
    // 0xcce178: DecompressPointer r5
    //     0xcce178: add             x5, x5, HEAP, lsl #32
    // 0xcce17c: stur            x5, [fp, #-8]
    // 0xcce180: LoadField: r1 = r0->field_7
    //     0xcce180: ldur            w1, [x0, #7]
    // 0xcce184: DecompressPointer r1
    //     0xcce184: add             x1, x1, HEAP, lsl #32
    // 0xcce188: r0 = _BackpressureStreamSink()
    //     0xcce188: bl              #0xcce358  ; Allocate_BackpressureStreamSinkStub -> _BackpressureStreamSink<X0, X1> (size=0x50)
    // 0xcce18c: mov             x1, x0
    // 0xcce190: ldur            x2, [fp, #-0x18]
    // 0xcce194: ldur            x3, [fp, #-0x10]
    // 0xcce198: ldur            x5, [fp, #-8]
    // 0xcce19c: stur            x0, [fp, #-8]
    // 0xcce1a0: r0 = _BackpressureStreamSink()
    //     0xcce1a0: bl              #0xcce1bc  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::_BackpressureStreamSink
    // 0xcce1a4: ldur            x0, [fp, #-8]
    // 0xcce1a8: LeaveFrame
    //     0xcce1a8: mov             SP, fp
    //     0xcce1ac: ldp             fp, lr, [SP], #0x10
    // 0xcce1b0: ret
    //     0xcce1b0: ret             
    // 0xcce1b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcce1b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcce1b8: b               #0xcce154
  }
}

// class id: 6776, size: 0x14, field offset: 0x14
enum WindowStrategy extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e7c0, size: 0x64
    // 0xc4e7c0: EnterFrame
    //     0xc4e7c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e7c4: mov             fp, SP
    // 0xc4e7c8: AllocStack(0x10)
    //     0xc4e7c8: sub             SP, SP, #0x10
    // 0xc4e7cc: SetupParameters(WindowStrategy this /* r1 => r0, fp-0x8 */)
    //     0xc4e7cc: mov             x0, x1
    //     0xc4e7d0: stur            x1, [fp, #-8]
    // 0xc4e7d4: CheckStackOverflow
    //     0xc4e7d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e7d8: cmp             SP, x16
    //     0xc4e7dc: b.ls            #0xc4e81c
    // 0xc4e7e0: r1 = Null
    //     0xc4e7e0: mov             x1, NULL
    // 0xc4e7e4: r2 = 4
    //     0xc4e7e4: movz            x2, #0x4
    // 0xc4e7e8: r0 = AllocateArray()
    //     0xc4e7e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e7ec: r16 = "WindowStrategy."
    //     0xc4e7ec: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f00] "WindowStrategy."
    //     0xc4e7f0: ldr             x16, [x16, #0xf00]
    // 0xc4e7f4: StoreField: r0->field_f = r16
    //     0xc4e7f4: stur            w16, [x0, #0xf]
    // 0xc4e7f8: ldur            x1, [fp, #-8]
    // 0xc4e7fc: LoadField: r2 = r1->field_f
    //     0xc4e7fc: ldur            w2, [x1, #0xf]
    // 0xc4e800: DecompressPointer r2
    //     0xc4e800: add             x2, x2, HEAP, lsl #32
    // 0xc4e804: StoreField: r0->field_13 = r2
    //     0xc4e804: stur            w2, [x0, #0x13]
    // 0xc4e808: str             x0, [SP]
    // 0xc4e80c: r0 = _interpolate()
    //     0xc4e80c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e810: LeaveFrame
    //     0xc4e810: mov             SP, fp
    //     0xc4e814: ldp             fp, lr, [SP], #0x10
    // 0xc4e818: ret
    //     0xc4e818: ret             
    // 0xc4e81c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e81c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e820: b               #0xc4e7e0
  }
}
