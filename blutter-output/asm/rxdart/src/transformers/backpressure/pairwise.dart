// lib: , url: package:rxdart/src/transformers/backpressure/pairwise.dart

// class id: 1051095, size: 0x8
class :: {

  static Stream<List<Y0>> PairwiseExtension.pairwise<Y0>(Stream<Y0>) {
    // ** addr: 0x8b85a0, size: 0x80
    // 0x8b85a0: EnterFrame
    //     0x8b85a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b85a4: mov             fp, SP
    // 0x8b85a8: AllocStack(0x8)
    //     0x8b85a8: sub             SP, SP, #8
    // 0x8b85ac: SetupParameters()
    //     0x8b85ac: ldur            w0, [x4, #0xf]
    //     0x8b85b0: cbnz            w0, #0x8b85bc
    //     0x8b85b4: mov             x1, NULL
    //     0x8b85b8: b               #0x8b85c8
    //     0x8b85bc: ldur            w0, [x4, #0x17]
    //     0x8b85c0: add             x1, fp, w0, sxtw #2
    //     0x8b85c4: ldr             x1, [x1, #0x10]
    // 0x8b85c8: CheckStackOverflow
    //     0x8b85c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b85cc: cmp             SP, x16
    //     0x8b85d0: b.ls            #0x8b8618
    // 0x8b85d4: r2 = Null
    //     0x8b85d4: mov             x2, NULL
    // 0x8b85d8: r3 = <Y0, List<Y0>, Y0>
    //     0x8b85d8: add             x3, PP, #0xe, lsl #12  ; [pp+0xec48] TypeArguments: <Y0, List<Y0>, Y0>
    //     0x8b85dc: ldr             x3, [x3, #0xc48]
    // 0x8b85e0: r30 = InstantiateTypeArgumentsStub
    //     0x8b85e0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8b85e4: LoadField: r30 = r30->field_7
    //     0x8b85e4: ldur            lr, [lr, #7]
    // 0x8b85e8: blr             lr
    // 0x8b85ec: mov             x1, x0
    // 0x8b85f0: r0 = PairwiseStreamTransformer()
    //     0x8b85f0: bl              #0x8b87e0  ; AllocatePairwiseStreamTransformerStub -> PairwiseStreamTransformer<C2X0> (size=0x34)
    // 0x8b85f4: mov             x1, x0
    // 0x8b85f8: stur            x0, [fp, #-8]
    // 0x8b85fc: r0 = PairwiseStreamTransformer()
    //     0x8b85fc: bl              #0x8b8620  ; [package:rxdart/src/transformers/backpressure/pairwise.dart] PairwiseStreamTransformer::PairwiseStreamTransformer
    // 0x8b8600: ldur            x1, [fp, #-8]
    // 0x8b8604: ldr             x2, [fp, #0x10]
    // 0x8b8608: r0 = bind()
    //     0x8b8608: bl              #0xccda18  ; [package:rxdart/src/transformers/backpressure/backpressure.dart] BackpressureStreamTransformer::bind
    // 0x8b860c: LeaveFrame
    //     0x8b860c: mov             SP, fp
    //     0x8b8610: ldp             fp, lr, [SP], #0x10
    // 0x8b8614: ret
    //     0x8b8614: ret             
    // 0x8b8618: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b8618: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b861c: b               #0x8b85d4
  }
}

// class id: 6616, size: 0x34, field offset: 0x34
class PairwiseStreamTransformer<C2X0> extends BackpressureStreamTransformer<C2X0, dynamic> {

  _ PairwiseStreamTransformer(/* No info */) {
    // ** addr: 0x8b8620, size: 0x104
    // 0x8b8620: EnterFrame
    //     0x8b8620: stp             fp, lr, [SP, #-0x10]!
    //     0x8b8624: mov             fp, SP
    // 0x8b8628: AllocStack(0x18)
    //     0x8b8628: sub             SP, SP, #0x18
    // 0x8b862c: SetupParameters(PairwiseStreamTransformer<C2X0> this /* r1 => r1, fp-0x8 */)
    //     0x8b862c: stur            x1, [fp, #-8]
    // 0x8b8630: r1 = 1
    //     0x8b8630: movz            x1, #0x1
    // 0x8b8634: r0 = AllocateContext()
    //     0x8b8634: bl              #0xec126c  ; AllocateContextStub
    // 0x8b8638: mov             x4, x0
    // 0x8b863c: ldur            x0, [fp, #-8]
    // 0x8b8640: stur            x4, [fp, #-0x18]
    // 0x8b8644: StoreField: r4->field_f = r0
    //     0x8b8644: stur            w0, [x4, #0xf]
    // 0x8b8648: LoadField: r5 = r0->field_7
    //     0x8b8648: ldur            w5, [x0, #7]
    // 0x8b864c: DecompressPointer r5
    //     0x8b864c: add             x5, x5, HEAP, lsl #32
    // 0x8b8650: stur            x5, [fp, #-0x10]
    // 0x8b8654: r1 = Instance_WindowStrategy
    //     0x8b8654: add             x1, PP, #0xe, lsl #12  ; [pp+0xec80] Obj!WindowStrategy@e2e1e1
    //     0x8b8658: ldr             x1, [x1, #0xc80]
    // 0x8b865c: StoreField: r0->field_b = r1
    //     0x8b865c: stur            w1, [x0, #0xb]
    // 0x8b8660: mov             x2, x4
    // 0x8b8664: mov             x3, x5
    // 0x8b8668: r1 = Function '<anonymous closure>':.
    //     0x8b8668: add             x1, PP, #0xe, lsl #12  ; [pp+0xeec0] AnonymousClosure: (0x8b8788), in [package:rxdart/src/transformers/backpressure/pairwise.dart] PairwiseStreamTransformer::PairwiseStreamTransformer (0x8b8620)
    //     0x8b866c: ldr             x1, [x1, #0xec0]
    // 0x8b8670: r0 = AllocateClosureTA()
    //     0x8b8670: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x8b8674: ldur            x4, [fp, #-8]
    // 0x8b8678: StoreField: r4->field_f = r0
    //     0x8b8678: stur            w0, [x4, #0xf]
    //     0x8b867c: ldurb           w16, [x4, #-1]
    //     0x8b8680: ldurb           w17, [x0, #-1]
    //     0x8b8684: and             x16, x17, x16, lsr #2
    //     0x8b8688: tst             x16, HEAP, lsr #32
    //     0x8b868c: b.eq            #0x8b8694
    //     0x8b8690: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8b8694: ldur            x2, [fp, #-0x18]
    // 0x8b8698: ldur            x3, [fp, #-0x10]
    // 0x8b869c: r1 = Function '<anonymous closure>':.
    //     0x8b869c: add             x1, PP, #0xe, lsl #12  ; [pp+0xeec8] AnonymousClosure: (0xebd554), in [package:flutter/src/services/restoration.dart] RestorationBucket::_visitChildren (0x69ffb0)
    //     0x8b86a0: ldr             x1, [x1, #0xec8]
    // 0x8b86a4: r0 = AllocateClosureTA()
    //     0x8b86a4: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x8b86a8: ldur            x4, [fp, #-8]
    // 0x8b86ac: ArrayStore: r4[0] = r0  ; List_4
    //     0x8b86ac: stur            w0, [x4, #0x17]
    //     0x8b86b0: ldurb           w16, [x4, #-1]
    //     0x8b86b4: ldurb           w17, [x0, #-1]
    //     0x8b86b8: and             x16, x17, x16, lsr #2
    //     0x8b86bc: tst             x16, HEAP, lsr #32
    //     0x8b86c0: b.eq            #0x8b86c8
    //     0x8b86c4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8b86c8: r0 = 1
    //     0x8b86c8: movz            x0, #0x1
    // 0x8b86cc: StoreField: r4->field_1f = r0
    //     0x8b86cc: stur            x0, [x4, #0x1f]
    // 0x8b86d0: ldur            x2, [fp, #-0x18]
    // 0x8b86d4: ldur            x3, [fp, #-0x10]
    // 0x8b86d8: r1 = Function '<anonymous closure>':.
    //     0x8b86d8: add             x1, PP, #0xe, lsl #12  ; [pp+0xeed0] AnonymousClosure: (0x8b8724), in [package:rxdart/src/transformers/backpressure/pairwise.dart] PairwiseStreamTransformer::PairwiseStreamTransformer (0x8b8620)
    //     0x8b86dc: ldr             x1, [x1, #0xed0]
    // 0x8b86e0: r0 = AllocateClosureTA()
    //     0x8b86e0: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x8b86e4: ldur            x1, [fp, #-8]
    // 0x8b86e8: StoreField: r1->field_27 = r0
    //     0x8b86e8: stur            w0, [x1, #0x27]
    //     0x8b86ec: ldurb           w16, [x1, #-1]
    //     0x8b86f0: ldurb           w17, [x0, #-1]
    //     0x8b86f4: and             x16, x17, x16, lsr #2
    //     0x8b86f8: tst             x16, HEAP, lsr #32
    //     0x8b86fc: b.eq            #0x8b8704
    //     0x8b8700: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b8704: r2 = true
    //     0x8b8704: add             x2, NULL, #0x20  ; true
    // 0x8b8708: StoreField: r1->field_2b = r2
    //     0x8b8708: stur            w2, [x1, #0x2b]
    // 0x8b870c: r2 = false
    //     0x8b870c: add             x2, NULL, #0x30  ; false
    // 0x8b8710: StoreField: r1->field_2f = r2
    //     0x8b8710: stur            w2, [x1, #0x2f]
    // 0x8b8714: r0 = Null
    //     0x8b8714: mov             x0, NULL
    // 0x8b8718: LeaveFrame
    //     0x8b8718: mov             SP, fp
    //     0x8b871c: ldp             fp, lr, [SP], #0x10
    // 0x8b8720: ret
    //     0x8b8720: ret             
  }
  [closure] bool <anonymous closure>(dynamic, List<C2X0>) {
    // ** addr: 0x8b8724, size: 0x64
    // 0x8b8724: EnterFrame
    //     0x8b8724: stp             fp, lr, [SP, #-0x10]!
    //     0x8b8728: mov             fp, SP
    // 0x8b872c: AllocStack(0x8)
    //     0x8b872c: sub             SP, SP, #8
    // 0x8b8730: CheckStackOverflow
    //     0x8b8730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b8734: cmp             SP, x16
    //     0x8b8738: b.ls            #0x8b8780
    // 0x8b873c: ldr             x0, [fp, #0x10]
    // 0x8b8740: r1 = LoadClassIdInstr(r0)
    //     0x8b8740: ldur            x1, [x0, #-1]
    //     0x8b8744: ubfx            x1, x1, #0xc, #0x14
    // 0x8b8748: str             x0, [SP]
    // 0x8b874c: mov             x0, x1
    // 0x8b8750: r0 = GDT[cid_x0 + 0xc834]()
    //     0x8b8750: movz            x17, #0xc834
    //     0x8b8754: add             lr, x0, x17
    //     0x8b8758: ldr             lr, [x21, lr, lsl #3]
    //     0x8b875c: blr             lr
    // 0x8b8760: cmp             w0, #4
    // 0x8b8764: r16 = true
    //     0x8b8764: add             x16, NULL, #0x20  ; true
    // 0x8b8768: r17 = false
    //     0x8b8768: add             x17, NULL, #0x30  ; false
    // 0x8b876c: csel            x1, x16, x17, eq
    // 0x8b8770: mov             x0, x1
    // 0x8b8774: LeaveFrame
    //     0x8b8774: mov             SP, fp
    //     0x8b8778: ldp             fp, lr, [SP], #0x10
    // 0x8b877c: ret
    //     0x8b877c: ret             
    // 0x8b8780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b8780: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b8784: b               #0x8b873c
  }
  [closure] NeverStream<void> <anonymous closure>(dynamic, C2X0) {
    // ** addr: 0x8b8788, size: 0x4c
    // 0x8b8788: EnterFrame
    //     0x8b8788: stp             fp, lr, [SP, #-0x10]!
    //     0x8b878c: mov             fp, SP
    // 0x8b8790: AllocStack(0x8)
    //     0x8b8790: sub             SP, SP, #8
    // 0x8b8794: CheckStackOverflow
    //     0x8b8794: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b8798: cmp             SP, x16
    //     0x8b879c: b.ls            #0x8b87cc
    // 0x8b87a0: r1 = <void?>
    //     0x8b87a0: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8b87a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8b87a4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8b87a8: r0 = StreamController()
    //     0x8b87a8: bl              #0x696864  ; [dart:async] StreamController::StreamController
    // 0x8b87ac: r1 = <void?>
    //     0x8b87ac: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8b87b0: stur            x0, [fp, #-8]
    // 0x8b87b4: r0 = NeverStream()
    //     0x8b87b4: bl              #0x8b87d4  ; AllocateNeverStreamStub -> NeverStream<X0> (size=0x10)
    // 0x8b87b8: ldur            x1, [fp, #-8]
    // 0x8b87bc: StoreField: r0->field_b = r1
    //     0x8b87bc: stur            w1, [x0, #0xb]
    // 0x8b87c0: LeaveFrame
    //     0x8b87c0: mov             SP, fp
    //     0x8b87c4: ldp             fp, lr, [SP], #0x10
    // 0x8b87c8: ret
    //     0x8b87c8: ret             
    // 0x8b87cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b87cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b87d0: b               #0x8b87a0
  }
}
