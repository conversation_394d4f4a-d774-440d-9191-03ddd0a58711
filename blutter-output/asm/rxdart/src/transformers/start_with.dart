// lib: , url: package:rxdart/src/transformers/start_with.dart

// class id: 1051096, size: 0x8
class :: {

  static Stream<Y0> StartWithExtension.startWith<Y0>(Stream<Y0>, Y0) {
    // ** addr: 0x8a7884, size: 0x90
    // 0x8a7884: EnterFrame
    //     0x8a7884: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7888: mov             fp, SP
    // 0x8a788c: LoadField: r0 = r4->field_f
    //     0x8a788c: ldur            w0, [x4, #0xf]
    // 0x8a7890: cbnz            w0, #0x8a789c
    // 0x8a7894: r1 = Null
    //     0x8a7894: mov             x1, NULL
    // 0x8a7898: b               #0x8a78a8
    // 0x8a789c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x8a789c: ldur            w0, [x4, #0x17]
    // 0x8a78a0: add             x1, fp, w0, sxtw #2
    // 0x8a78a4: ldr             x1, [x1, #0x10]
    // 0x8a78a8: ldr             x0, [fp, #0x10]
    // 0x8a78ac: CheckStackOverflow
    //     0x8a78ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a78b0: cmp             SP, x16
    //     0x8a78b4: b.ls            #0x8a790c
    // 0x8a78b8: r2 = Null
    //     0x8a78b8: mov             x2, NULL
    // 0x8a78bc: r3 = <Y0, Y0>
    //     0x8a78bc: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3cf38] TypeArguments: <Y0, Y0>
    //     0x8a78c0: ldr             x3, [x3, #0xf38]
    // 0x8a78c4: r0 = Null
    //     0x8a78c4: mov             x0, NULL
    // 0x8a78c8: cmp             x2, x0
    // 0x8a78cc: b.ne            #0x8a78d8
    // 0x8a78d0: cmp             x1, x0
    // 0x8a78d4: b.eq            #0x8a78e4
    // 0x8a78d8: r30 = InstantiateTypeArgumentsStub
    //     0x8a78d8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8a78dc: LoadField: r30 = r30->field_7
    //     0x8a78dc: ldur            lr, [lr, #7]
    // 0x8a78e0: blr             lr
    // 0x8a78e4: mov             x1, x0
    // 0x8a78e8: r0 = StartWithStreamTransformer()
    //     0x8a78e8: bl              #0x8a7914  ; AllocateStartWithStreamTransformerStub -> StartWithStreamTransformer<C1X0> (size=0x10)
    // 0x8a78ec: mov             x1, x0
    // 0x8a78f0: ldr             x0, [fp, #0x10]
    // 0x8a78f4: StoreField: r1->field_b = r0
    //     0x8a78f4: stur            w0, [x1, #0xb]
    // 0x8a78f8: ldr             x2, [fp, #0x18]
    // 0x8a78fc: r0 = bind()
    //     0x8a78fc: bl              #0xcce4b4  ; [package:rxdart/src/transformers/start_with.dart] StartWithStreamTransformer::bind
    // 0x8a7900: LeaveFrame
    //     0x8a7900: mov             SP, fp
    //     0x8a7904: ldp             fp, lr, [SP], #0x10
    // 0x8a7908: ret
    //     0x8a7908: ret             
    // 0x8a790c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a790c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a7910: b               #0x8a78b8
  }
}

// class id: 519, size: 0x14, field offset: 0x10
class _StartWithStreamSink<C1X0> extends ForwardingSink<C1X0, dynamic> {

  [closure] void onDone(dynamic) {
    // ** addr: 0xab1328, size: 0x38
    // 0xab1328: EnterFrame
    //     0xab1328: stp             fp, lr, [SP, #-0x10]!
    //     0xab132c: mov             fp, SP
    // 0xab1330: ldr             x0, [fp, #0x10]
    // 0xab1334: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab1334: ldur            w1, [x0, #0x17]
    // 0xab1338: DecompressPointer r1
    //     0xab1338: add             x1, x1, HEAP, lsl #32
    // 0xab133c: CheckStackOverflow
    //     0xab133c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1340: cmp             SP, x16
    //     0xab1344: b.ls            #0xab1358
    // 0xab1348: r0 = onDone()
    //     0xab1348: bl              #0xab1360  ; [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onDone
    // 0xab134c: LeaveFrame
    //     0xab134c: mov             SP, fp
    //     0xab1350: ldp             fp, lr, [SP], #0x10
    // 0xab1354: ret
    //     0xab1354: ret             
    // 0xab1358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1358: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab135c: b               #0xab1348
  }
  _ onDone(/* No info */) {
    // ** addr: 0xab1360, size: 0x78
    // 0xab1360: EnterFrame
    //     0xab1360: stp             fp, lr, [SP, #-0x10]!
    //     0xab1364: mov             fp, SP
    // 0xab1368: CheckStackOverflow
    //     0xab1368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab136c: cmp             SP, x16
    //     0xab1370: b.ls            #0xab13d0
    // 0xab1374: LoadField: r0 = r1->field_b
    //     0xab1374: ldur            w0, [x1, #0xb]
    // 0xab1378: DecompressPointer r0
    //     0xab1378: add             x0, x0, HEAP, lsl #32
    // 0xab137c: cmp             w0, NULL
    // 0xab1380: b.eq            #0xab13b0
    // 0xab1384: r1 = LoadClassIdInstr(r0)
    //     0xab1384: ldur            x1, [x0, #-1]
    //     0xab1388: ubfx            x1, x1, #0xc, #0x14
    // 0xab138c: mov             x16, x0
    // 0xab1390: mov             x0, x1
    // 0xab1394: mov             x1, x16
    // 0xab1398: r0 = GDT[cid_x0 + 0xf47]()
    //     0xab1398: add             lr, x0, #0xf47
    //     0xab139c: ldr             lr, [x21, lr, lsl #3]
    //     0xab13a0: blr             lr
    // 0xab13a4: LeaveFrame
    //     0xab13a4: mov             SP, fp
    //     0xab13a8: ldp             fp, lr, [SP], #0x10
    // 0xab13ac: ret
    //     0xab13ac: ret             
    // 0xab13b0: r0 = StateError()
    //     0xab13b0: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab13b4: mov             x1, x0
    // 0xab13b8: r0 = "Must call setSink(sink) before accessing!"
    //     0xab13b8: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab13bc: ldr             x0, [x0, #0xcd0]
    // 0xab13c0: StoreField: r1->field_b = r0
    //     0xab13c0: stur            w0, [x1, #0xb]
    // 0xab13c4: mov             x0, x1
    // 0xab13c8: r0 = Throw()
    //     0xab13c8: bl              #0xec04b8  ; ThrowStub
    // 0xab13cc: brk             #0
    // 0xab13d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab13d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab13d4: b               #0xab1374
  }
  _ onError(/* No info */) {
    // ** addr: 0xab1450, size: 0x88
    // 0xab1450: EnterFrame
    //     0xab1450: stp             fp, lr, [SP, #-0x10]!
    //     0xab1454: mov             fp, SP
    // 0xab1458: AllocStack(0x8)
    //     0xab1458: sub             SP, SP, #8
    // 0xab145c: CheckStackOverflow
    //     0xab145c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1460: cmp             SP, x16
    //     0xab1464: b.ls            #0xab14d0
    // 0xab1468: LoadField: r0 = r1->field_b
    //     0xab1468: ldur            w0, [x1, #0xb]
    // 0xab146c: DecompressPointer r0
    //     0xab146c: add             x0, x0, HEAP, lsl #32
    // 0xab1470: cmp             w0, NULL
    // 0xab1474: b.eq            #0xab14b0
    // 0xab1478: r1 = LoadClassIdInstr(r0)
    //     0xab1478: ldur            x1, [x0, #-1]
    //     0xab147c: ubfx            x1, x1, #0xc, #0x14
    // 0xab1480: str             x3, [SP]
    // 0xab1484: mov             x16, x0
    // 0xab1488: mov             x0, x1
    // 0xab148c: mov             x1, x16
    // 0xab1490: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xab1490: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xab1494: r0 = GDT[cid_x0 + 0x9b0]()
    //     0xab1494: add             lr, x0, #0x9b0
    //     0xab1498: ldr             lr, [x21, lr, lsl #3]
    //     0xab149c: blr             lr
    // 0xab14a0: r0 = Null
    //     0xab14a0: mov             x0, NULL
    // 0xab14a4: LeaveFrame
    //     0xab14a4: mov             SP, fp
    //     0xab14a8: ldp             fp, lr, [SP], #0x10
    // 0xab14ac: ret
    //     0xab14ac: ret             
    // 0xab14b0: r0 = StateError()
    //     0xab14b0: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab14b4: mov             x1, x0
    // 0xab14b8: r0 = "Must call setSink(sink) before accessing!"
    //     0xab14b8: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab14bc: ldr             x0, [x0, #0xcd0]
    // 0xab14c0: StoreField: r1->field_b = r0
    //     0xab14c0: stur            w0, [x1, #0xb]
    // 0xab14c4: mov             x0, x1
    // 0xab14c8: r0 = Throw()
    //     0xab14c8: bl              #0xec04b8  ; ThrowStub
    // 0xab14cc: brk             #0
    // 0xab14d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab14d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab14d4: b               #0xab1468
  }
  [closure] void onError(dynamic, Object, StackTrace) {
    // ** addr: 0xab14d8, size: 0x40
    // 0xab14d8: EnterFrame
    //     0xab14d8: stp             fp, lr, [SP, #-0x10]!
    //     0xab14dc: mov             fp, SP
    // 0xab14e0: ldr             x0, [fp, #0x20]
    // 0xab14e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab14e4: ldur            w1, [x0, #0x17]
    // 0xab14e8: DecompressPointer r1
    //     0xab14e8: add             x1, x1, HEAP, lsl #32
    // 0xab14ec: CheckStackOverflow
    //     0xab14ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab14f0: cmp             SP, x16
    //     0xab14f4: b.ls            #0xab1510
    // 0xab14f8: ldr             x2, [fp, #0x18]
    // 0xab14fc: ldr             x3, [fp, #0x10]
    // 0xab1500: r0 = onError()
    //     0xab1500: bl              #0xab1450  ; [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onError
    // 0xab1504: LeaveFrame
    //     0xab1504: mov             SP, fp
    //     0xab1508: ldp             fp, lr, [SP], #0x10
    // 0xab150c: ret
    //     0xab150c: ret             
    // 0xab1510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1510: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1514: b               #0xab14f8
  }
  [closure] void onData(dynamic, Object?) {
    // ** addr: 0xab1f48, size: 0x3c
    // 0xab1f48: EnterFrame
    //     0xab1f48: stp             fp, lr, [SP, #-0x10]!
    //     0xab1f4c: mov             fp, SP
    // 0xab1f50: ldr             x0, [fp, #0x18]
    // 0xab1f54: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab1f54: ldur            w1, [x0, #0x17]
    // 0xab1f58: DecompressPointer r1
    //     0xab1f58: add             x1, x1, HEAP, lsl #32
    // 0xab1f5c: CheckStackOverflow
    //     0xab1f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1f60: cmp             SP, x16
    //     0xab1f64: b.ls            #0xab1f7c
    // 0xab1f68: ldr             x2, [fp, #0x10]
    // 0xab1f6c: r0 = onData()
    //     0xab1f6c: bl              #0xab1f84  ; [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onData
    // 0xab1f70: LeaveFrame
    //     0xab1f70: mov             SP, fp
    //     0xab1f74: ldp             fp, lr, [SP], #0x10
    // 0xab1f78: ret
    //     0xab1f78: ret             
    // 0xab1f7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1f7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1f80: b               #0xab1f68
  }
  _ onData(/* No info */) {
    // ** addr: 0xab1f84, size: 0xc4
    // 0xab1f84: EnterFrame
    //     0xab1f84: stp             fp, lr, [SP, #-0x10]!
    //     0xab1f88: mov             fp, SP
    // 0xab1f8c: AllocStack(0x10)
    //     0xab1f8c: sub             SP, SP, #0x10
    // 0xab1f90: SetupParameters(_StartWithStreamSink<C1X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xab1f90: mov             x4, x1
    //     0xab1f94: mov             x3, x2
    //     0xab1f98: stur            x1, [fp, #-8]
    //     0xab1f9c: stur            x2, [fp, #-0x10]
    // 0xab1fa0: CheckStackOverflow
    //     0xab1fa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1fa4: cmp             SP, x16
    //     0xab1fa8: b.ls            #0xab2040
    // 0xab1fac: LoadField: r2 = r4->field_7
    //     0xab1fac: ldur            w2, [x4, #7]
    // 0xab1fb0: DecompressPointer r2
    //     0xab1fb0: add             x2, x2, HEAP, lsl #32
    // 0xab1fb4: mov             x0, x3
    // 0xab1fb8: r1 = Null
    //     0xab1fb8: mov             x1, NULL
    // 0xab1fbc: cmp             w2, NULL
    // 0xab1fc0: b.eq            #0xab1fe4
    // 0xab1fc4: LoadField: r4 = r2->field_1b
    //     0xab1fc4: ldur            w4, [x2, #0x1b]
    // 0xab1fc8: DecompressPointer r4
    //     0xab1fc8: add             x4, x4, HEAP, lsl #32
    // 0xab1fcc: r8 = C1X0
    //     0xab1fcc: add             x8, PP, #0xc, lsl #12  ; [pp+0xc1e8] TypeParameter: C1X0
    //     0xab1fd0: ldr             x8, [x8, #0x1e8]
    // 0xab1fd4: LoadField: r9 = r4->field_7
    //     0xab1fd4: ldur            x9, [x4, #7]
    // 0xab1fd8: r3 = Null
    //     0xab1fd8: add             x3, PP, #0xe, lsl #12  ; [pp+0xee58] Null
    //     0xab1fdc: ldr             x3, [x3, #0xe58]
    // 0xab1fe0: blr             x9
    // 0xab1fe4: ldur            x0, [fp, #-8]
    // 0xab1fe8: LoadField: r1 = r0->field_b
    //     0xab1fe8: ldur            w1, [x0, #0xb]
    // 0xab1fec: DecompressPointer r1
    //     0xab1fec: add             x1, x1, HEAP, lsl #32
    // 0xab1ff0: cmp             w1, NULL
    // 0xab1ff4: b.eq            #0xab2020
    // 0xab1ff8: r0 = LoadClassIdInstr(r1)
    //     0xab1ff8: ldur            x0, [x1, #-1]
    //     0xab1ffc: ubfx            x0, x0, #0xc, #0x14
    // 0xab2000: ldur            x2, [fp, #-0x10]
    // 0xab2004: r0 = GDT[cid_x0 + 0xdbe]()
    //     0xab2004: add             lr, x0, #0xdbe
    //     0xab2008: ldr             lr, [x21, lr, lsl #3]
    //     0xab200c: blr             lr
    // 0xab2010: r0 = Null
    //     0xab2010: mov             x0, NULL
    // 0xab2014: LeaveFrame
    //     0xab2014: mov             SP, fp
    //     0xab2018: ldp             fp, lr, [SP], #0x10
    // 0xab201c: ret
    //     0xab201c: ret             
    // 0xab2020: r0 = StateError()
    //     0xab2020: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab2024: mov             x1, x0
    // 0xab2028: r0 = "Must call setSink(sink) before accessing!"
    //     0xab2028: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab202c: ldr             x0, [x0, #0xcd0]
    // 0xab2030: StoreField: r1->field_b = r0
    //     0xab2030: stur            w0, [x1, #0xb]
    // 0xab2034: mov             x0, x1
    // 0xab2038: r0 = Throw()
    //     0xab2038: bl              #0xec04b8  ; ThrowStub
    // 0xab203c: brk             #0
    // 0xab2040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2040: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2044: b               #0xab1fac
  }
}

// class id: 6614, size: 0x10, field offset: 0xc
class StartWithStreamTransformer<C1X0> extends StreamTransformerBase<C1X0, dynamic> {

  _ bind(/* No info */) {
    // ** addr: 0xcce4b4, size: 0xac
    // 0xcce4b4: EnterFrame
    //     0xcce4b4: stp             fp, lr, [SP, #-0x10]!
    //     0xcce4b8: mov             fp, SP
    // 0xcce4bc: AllocStack(0x38)
    //     0xcce4bc: sub             SP, SP, #0x38
    // 0xcce4c0: SetupParameters(StartWithStreamTransformer<C1X0> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xcce4c0: mov             x0, x2
    //     0xcce4c4: stur            x1, [fp, #-8]
    //     0xcce4c8: stur            x2, [fp, #-0x10]
    // 0xcce4cc: CheckStackOverflow
    //     0xcce4cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcce4d0: cmp             SP, x16
    //     0xcce4d4: b.ls            #0xcce558
    // 0xcce4d8: r1 = 1
    //     0xcce4d8: movz            x1, #0x1
    // 0xcce4dc: r0 = AllocateContext()
    //     0xcce4dc: bl              #0xec126c  ; AllocateContextStub
    // 0xcce4e0: mov             x3, x0
    // 0xcce4e4: ldur            x0, [fp, #-8]
    // 0xcce4e8: stur            x3, [fp, #-0x20]
    // 0xcce4ec: StoreField: r3->field_f = r0
    //     0xcce4ec: stur            w0, [x3, #0xf]
    // 0xcce4f0: LoadField: r4 = r0->field_7
    //     0xcce4f0: ldur            w4, [x0, #7]
    // 0xcce4f4: DecompressPointer r4
    //     0xcce4f4: add             x4, x4, HEAP, lsl #32
    // 0xcce4f8: ldur            x0, [fp, #-0x10]
    // 0xcce4fc: mov             x2, x4
    // 0xcce500: stur            x4, [fp, #-0x18]
    // 0xcce504: r1 = Null
    //     0xcce504: mov             x1, NULL
    // 0xcce508: r8 = Stream<C1X0>
    //     0xcce508: add             x8, PP, #0x12, lsl #12  ; [pp+0x12788] Type: Stream<C1X0>
    //     0xcce50c: ldr             x8, [x8, #0x788]
    // 0xcce510: LoadField: r9 = r8->field_7
    //     0xcce510: ldur            x9, [x8, #7]
    // 0xcce514: r3 = Null
    //     0xcce514: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c8c8] Null
    //     0xcce518: ldr             x3, [x3, #0x8c8]
    // 0xcce51c: blr             x9
    // 0xcce520: ldur            x2, [fp, #-0x20]
    // 0xcce524: ldur            x3, [fp, #-0x18]
    // 0xcce528: r1 = Function '<anonymous closure>':.
    //     0xcce528: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c8d8] AnonymousClosure: (0xcce560), in [package:rxdart/src/transformers/start_with.dart] StartWithStreamTransformer::bind (0xcce4b4)
    //     0xcce52c: ldr             x1, [x1, #0x8d8]
    // 0xcce530: r0 = AllocateClosureTA()
    //     0xcce530: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xcce534: ldur            x16, [fp, #-0x18]
    // 0xcce538: ldur            lr, [fp, #-0x10]
    // 0xcce53c: stp             lr, x16, [SP, #8]
    // 0xcce540: str             x0, [SP]
    // 0xcce544: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xcce544: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xcce548: r0 = forwardStream()
    //     0xcce548: bl              #0xccdac4  ; [package:rxdart/src/utils/forwarding_stream.dart] ::forwardStream
    // 0xcce54c: LeaveFrame
    //     0xcce54c: mov             SP, fp
    //     0xcce550: ldp             fp, lr, [SP], #0x10
    // 0xcce554: ret
    //     0xcce554: ret             
    // 0xcce558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcce558: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcce55c: b               #0xcce4d8
  }
  [closure] _StartWithStreamSink<C1X0> <anonymous closure>(dynamic) {
    // ** addr: 0xcce560, size: 0x4c
    // 0xcce560: EnterFrame
    //     0xcce560: stp             fp, lr, [SP, #-0x10]!
    //     0xcce564: mov             fp, SP
    // 0xcce568: AllocStack(0x8)
    //     0xcce568: sub             SP, SP, #8
    // 0xcce56c: SetupParameters()
    //     0xcce56c: ldr             x0, [fp, #0x10]
    //     0xcce570: ldur            w1, [x0, #0x17]
    //     0xcce574: add             x1, x1, HEAP, lsl #32
    // 0xcce578: LoadField: r0 = r1->field_f
    //     0xcce578: ldur            w0, [x1, #0xf]
    // 0xcce57c: DecompressPointer r0
    //     0xcce57c: add             x0, x0, HEAP, lsl #32
    // 0xcce580: LoadField: r1 = r0->field_7
    //     0xcce580: ldur            w1, [x0, #7]
    // 0xcce584: DecompressPointer r1
    //     0xcce584: add             x1, x1, HEAP, lsl #32
    // 0xcce588: LoadField: r2 = r0->field_b
    //     0xcce588: ldur            w2, [x0, #0xb]
    // 0xcce58c: DecompressPointer r2
    //     0xcce58c: add             x2, x2, HEAP, lsl #32
    // 0xcce590: stur            x2, [fp, #-8]
    // 0xcce594: r0 = _StartWithStreamSink()
    //     0xcce594: bl              #0xcce5ac  ; Allocate_StartWithStreamSinkStub -> _StartWithStreamSink<C1X0> (size=0x14)
    // 0xcce598: ldur            x1, [fp, #-8]
    // 0xcce59c: StoreField: r0->field_f = r1
    //     0xcce59c: stur            w1, [x0, #0xf]
    // 0xcce5a0: LeaveFrame
    //     0xcce5a0: mov             SP, fp
    //     0xcce5a4: ldp             fp, lr, [SP], #0x10
    // 0xcce5a8: ret
    //     0xcce5a8: ret             
  }
}
