// lib: , url: package:rxdart/src/transformers/start_with_error.dart

// class id: 1051097, size: 0x8
class :: {
}

// class id: 518, size: 0x18, field offset: 0x10
class _StartWithErrorStreamSink<C1X0> extends ForwardingSink<C1X0, dynamic> {

  [closure] void onDone(dynamic) {
    // ** addr: 0xab13d8, size: 0x38
    // 0xab13d8: EnterFrame
    //     0xab13d8: stp             fp, lr, [SP, #-0x10]!
    //     0xab13dc: mov             fp, SP
    // 0xab13e0: ldr             x0, [fp, #0x10]
    // 0xab13e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab13e4: ldur            w1, [x0, #0x17]
    // 0xab13e8: DecompressPointer r1
    //     0xab13e8: add             x1, x1, HEAP, lsl #32
    // 0xab13ec: CheckStackOverflow
    //     0xab13ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab13f0: cmp             SP, x16
    //     0xab13f4: b.ls            #0xab1408
    // 0xab13f8: r0 = onDone()
    //     0xab13f8: bl              #0xab1360  ; [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onDone
    // 0xab13fc: LeaveFrame
    //     0xab13fc: mov             SP, fp
    //     0xab1400: ldp             fp, lr, [SP], #0x10
    // 0xab1404: ret
    //     0xab1404: ret             
    // 0xab1408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1408: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab140c: b               #0xab13f8
  }
  [closure] void onError(dynamic, Object, StackTrace) {
    // ** addr: 0xab1518, size: 0x40
    // 0xab1518: EnterFrame
    //     0xab1518: stp             fp, lr, [SP, #-0x10]!
    //     0xab151c: mov             fp, SP
    // 0xab1520: ldr             x0, [fp, #0x20]
    // 0xab1524: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab1524: ldur            w1, [x0, #0x17]
    // 0xab1528: DecompressPointer r1
    //     0xab1528: add             x1, x1, HEAP, lsl #32
    // 0xab152c: CheckStackOverflow
    //     0xab152c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1530: cmp             SP, x16
    //     0xab1534: b.ls            #0xab1550
    // 0xab1538: ldr             x2, [fp, #0x18]
    // 0xab153c: ldr             x3, [fp, #0x10]
    // 0xab1540: r0 = onError()
    //     0xab1540: bl              #0xab1450  ; [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onError
    // 0xab1544: LeaveFrame
    //     0xab1544: mov             SP, fp
    //     0xab1548: ldp             fp, lr, [SP], #0x10
    // 0xab154c: ret
    //     0xab154c: ret             
    // 0xab1550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1550: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1554: b               #0xab1538
  }
  [closure] void onData(dynamic, Object?) {
    // ** addr: 0xab2048, size: 0x3c
    // 0xab2048: EnterFrame
    //     0xab2048: stp             fp, lr, [SP, #-0x10]!
    //     0xab204c: mov             fp, SP
    // 0xab2050: ldr             x0, [fp, #0x18]
    // 0xab2054: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab2054: ldur            w1, [x0, #0x17]
    // 0xab2058: DecompressPointer r1
    //     0xab2058: add             x1, x1, HEAP, lsl #32
    // 0xab205c: CheckStackOverflow
    //     0xab205c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2060: cmp             SP, x16
    //     0xab2064: b.ls            #0xab207c
    // 0xab2068: ldr             x2, [fp, #0x10]
    // 0xab206c: r0 = onData()
    //     0xab206c: bl              #0xab2084  ; [package:rxdart/src/transformers/start_with_error.dart] _StartWithErrorStreamSink::onData
    // 0xab2070: LeaveFrame
    //     0xab2070: mov             SP, fp
    //     0xab2074: ldp             fp, lr, [SP], #0x10
    // 0xab2078: ret
    //     0xab2078: ret             
    // 0xab207c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab207c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2080: b               #0xab2068
  }
  _ onData(/* No info */) {
    // ** addr: 0xab2084, size: 0xc4
    // 0xab2084: EnterFrame
    //     0xab2084: stp             fp, lr, [SP, #-0x10]!
    //     0xab2088: mov             fp, SP
    // 0xab208c: AllocStack(0x10)
    //     0xab208c: sub             SP, SP, #0x10
    // 0xab2090: SetupParameters(_StartWithErrorStreamSink<C1X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xab2090: mov             x4, x1
    //     0xab2094: mov             x3, x2
    //     0xab2098: stur            x1, [fp, #-8]
    //     0xab209c: stur            x2, [fp, #-0x10]
    // 0xab20a0: CheckStackOverflow
    //     0xab20a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab20a4: cmp             SP, x16
    //     0xab20a8: b.ls            #0xab2140
    // 0xab20ac: LoadField: r2 = r4->field_7
    //     0xab20ac: ldur            w2, [x4, #7]
    // 0xab20b0: DecompressPointer r2
    //     0xab20b0: add             x2, x2, HEAP, lsl #32
    // 0xab20b4: mov             x0, x3
    // 0xab20b8: r1 = Null
    //     0xab20b8: mov             x1, NULL
    // 0xab20bc: cmp             w2, NULL
    // 0xab20c0: b.eq            #0xab20e4
    // 0xab20c4: LoadField: r4 = r2->field_1b
    //     0xab20c4: ldur            w4, [x2, #0x1b]
    // 0xab20c8: DecompressPointer r4
    //     0xab20c8: add             x4, x4, HEAP, lsl #32
    // 0xab20cc: r8 = C1X0
    //     0xab20cc: add             x8, PP, #0xc, lsl #12  ; [pp+0xc1e8] TypeParameter: C1X0
    //     0xab20d0: ldr             x8, [x8, #0x1e8]
    // 0xab20d4: LoadField: r9 = r4->field_7
    //     0xab20d4: ldur            x9, [x4, #7]
    // 0xab20d8: r3 = Null
    //     0xab20d8: add             x3, PP, #0xe, lsl #12  ; [pp+0xee68] Null
    //     0xab20dc: ldr             x3, [x3, #0xe68]
    // 0xab20e0: blr             x9
    // 0xab20e4: ldur            x0, [fp, #-8]
    // 0xab20e8: LoadField: r1 = r0->field_b
    //     0xab20e8: ldur            w1, [x0, #0xb]
    // 0xab20ec: DecompressPointer r1
    //     0xab20ec: add             x1, x1, HEAP, lsl #32
    // 0xab20f0: cmp             w1, NULL
    // 0xab20f4: b.eq            #0xab2120
    // 0xab20f8: r0 = LoadClassIdInstr(r1)
    //     0xab20f8: ldur            x0, [x1, #-1]
    //     0xab20fc: ubfx            x0, x0, #0xc, #0x14
    // 0xab2100: ldur            x2, [fp, #-0x10]
    // 0xab2104: r0 = GDT[cid_x0 + 0xdbe]()
    //     0xab2104: add             lr, x0, #0xdbe
    //     0xab2108: ldr             lr, [x21, lr, lsl #3]
    //     0xab210c: blr             lr
    // 0xab2110: r0 = Null
    //     0xab2110: mov             x0, NULL
    // 0xab2114: LeaveFrame
    //     0xab2114: mov             SP, fp
    //     0xab2118: ldp             fp, lr, [SP], #0x10
    // 0xab211c: ret
    //     0xab211c: ret             
    // 0xab2120: r0 = StateError()
    //     0xab2120: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab2124: mov             x1, x0
    // 0xab2128: r0 = "Must call setSink(sink) before accessing!"
    //     0xab2128: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab212c: ldr             x0, [x0, #0xcd0]
    // 0xab2130: StoreField: r1->field_b = r0
    //     0xab2130: stur            w0, [x1, #0xb]
    // 0xab2134: mov             x0, x1
    // 0xab2138: r0 = Throw()
    //     0xab2138: bl              #0xec04b8  ; ThrowStub
    // 0xab213c: brk             #0
    // 0xab2140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2140: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2144: b               #0xab20ac
  }
}

// class id: 6613, size: 0x14, field offset: 0xc
class StartWithErrorStreamTransformer<C1X0> extends StreamTransformerBase<C1X0, dynamic> {

  _ bind(/* No info */) {
    // ** addr: 0xcce69c, size: 0xac
    // 0xcce69c: EnterFrame
    //     0xcce69c: stp             fp, lr, [SP, #-0x10]!
    //     0xcce6a0: mov             fp, SP
    // 0xcce6a4: AllocStack(0x38)
    //     0xcce6a4: sub             SP, SP, #0x38
    // 0xcce6a8: SetupParameters(StartWithErrorStreamTransformer<C1X0> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xcce6a8: mov             x0, x2
    //     0xcce6ac: stur            x1, [fp, #-8]
    //     0xcce6b0: stur            x2, [fp, #-0x10]
    // 0xcce6b4: CheckStackOverflow
    //     0xcce6b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcce6b8: cmp             SP, x16
    //     0xcce6bc: b.ls            #0xcce740
    // 0xcce6c0: r1 = 1
    //     0xcce6c0: movz            x1, #0x1
    // 0xcce6c4: r0 = AllocateContext()
    //     0xcce6c4: bl              #0xec126c  ; AllocateContextStub
    // 0xcce6c8: mov             x3, x0
    // 0xcce6cc: ldur            x0, [fp, #-8]
    // 0xcce6d0: stur            x3, [fp, #-0x20]
    // 0xcce6d4: StoreField: r3->field_f = r0
    //     0xcce6d4: stur            w0, [x3, #0xf]
    // 0xcce6d8: LoadField: r4 = r0->field_7
    //     0xcce6d8: ldur            w4, [x0, #7]
    // 0xcce6dc: DecompressPointer r4
    //     0xcce6dc: add             x4, x4, HEAP, lsl #32
    // 0xcce6e0: ldur            x0, [fp, #-0x10]
    // 0xcce6e4: mov             x2, x4
    // 0xcce6e8: stur            x4, [fp, #-0x18]
    // 0xcce6ec: r1 = Null
    //     0xcce6ec: mov             x1, NULL
    // 0xcce6f0: r8 = Stream<C1X0>
    //     0xcce6f0: add             x8, PP, #0x12, lsl #12  ; [pp+0x12788] Type: Stream<C1X0>
    //     0xcce6f4: ldr             x8, [x8, #0x788]
    // 0xcce6f8: LoadField: r9 = r8->field_7
    //     0xcce6f8: ldur            x9, [x8, #7]
    // 0xcce6fc: r3 = Null
    //     0xcce6fc: add             x3, PP, #0x12, lsl #12  ; [pp+0x12790] Null
    //     0xcce700: ldr             x3, [x3, #0x790]
    // 0xcce704: blr             x9
    // 0xcce708: ldur            x2, [fp, #-0x20]
    // 0xcce70c: ldur            x3, [fp, #-0x18]
    // 0xcce710: r1 = Function '<anonymous closure>':.
    //     0xcce710: add             x1, PP, #0x12, lsl #12  ; [pp+0x127a0] AnonymousClosure: (0xcce748), in [package:rxdart/src/transformers/start_with_error.dart] StartWithErrorStreamTransformer::bind (0xcce69c)
    //     0xcce714: ldr             x1, [x1, #0x7a0]
    // 0xcce718: r0 = AllocateClosureTA()
    //     0xcce718: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xcce71c: ldur            x16, [fp, #-0x18]
    // 0xcce720: ldur            lr, [fp, #-0x10]
    // 0xcce724: stp             lr, x16, [SP, #8]
    // 0xcce728: str             x0, [SP]
    // 0xcce72c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xcce72c: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xcce730: r0 = forwardStream()
    //     0xcce730: bl              #0xccdac4  ; [package:rxdart/src/utils/forwarding_stream.dart] ::forwardStream
    // 0xcce734: LeaveFrame
    //     0xcce734: mov             SP, fp
    //     0xcce738: ldp             fp, lr, [SP], #0x10
    // 0xcce73c: ret
    //     0xcce73c: ret             
    // 0xcce740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcce740: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcce744: b               #0xcce6c0
  }
  [closure] _StartWithErrorStreamSink<C1X0> <anonymous closure>(dynamic) {
    // ** addr: 0xcce748, size: 0x60
    // 0xcce748: EnterFrame
    //     0xcce748: stp             fp, lr, [SP, #-0x10]!
    //     0xcce74c: mov             fp, SP
    // 0xcce750: AllocStack(0x10)
    //     0xcce750: sub             SP, SP, #0x10
    // 0xcce754: SetupParameters()
    //     0xcce754: ldr             x0, [fp, #0x10]
    //     0xcce758: ldur            w1, [x0, #0x17]
    //     0xcce75c: add             x1, x1, HEAP, lsl #32
    // 0xcce760: LoadField: r0 = r1->field_f
    //     0xcce760: ldur            w0, [x1, #0xf]
    // 0xcce764: DecompressPointer r0
    //     0xcce764: add             x0, x0, HEAP, lsl #32
    // 0xcce768: LoadField: r1 = r0->field_7
    //     0xcce768: ldur            w1, [x0, #7]
    // 0xcce76c: DecompressPointer r1
    //     0xcce76c: add             x1, x1, HEAP, lsl #32
    // 0xcce770: LoadField: r2 = r0->field_b
    //     0xcce770: ldur            w2, [x0, #0xb]
    // 0xcce774: DecompressPointer r2
    //     0xcce774: add             x2, x2, HEAP, lsl #32
    // 0xcce778: stur            x2, [fp, #-0x10]
    // 0xcce77c: LoadField: r3 = r0->field_f
    //     0xcce77c: ldur            w3, [x0, #0xf]
    // 0xcce780: DecompressPointer r3
    //     0xcce780: add             x3, x3, HEAP, lsl #32
    // 0xcce784: stur            x3, [fp, #-8]
    // 0xcce788: r0 = _StartWithErrorStreamSink()
    //     0xcce788: bl              #0xcce7a8  ; Allocate_StartWithErrorStreamSinkStub -> _StartWithErrorStreamSink<C1X0> (size=0x18)
    // 0xcce78c: ldur            x1, [fp, #-0x10]
    // 0xcce790: StoreField: r0->field_f = r1
    //     0xcce790: stur            w1, [x0, #0xf]
    // 0xcce794: ldur            x1, [fp, #-8]
    // 0xcce798: StoreField: r0->field_13 = r1
    //     0xcce798: stur            w1, [x0, #0x13]
    // 0xcce79c: LeaveFrame
    //     0xcce79c: mov             SP, fp
    //     0xcce7a0: ldp             fp, lr, [SP], #0x10
    // 0xcce7a4: ret
    //     0xcce7a4: ret             
  }
}
