// lib: , url: package:rxdart/src/utils/collection_extensions.dart

// class id: 1051098, size: 0x8
class :: {

  static Iterable<Y1> MapNotNullIterableExtension.mapIndexed<Y0, Y1>(Iterable<Y0>, (dynamic, int, Y0) => Y1) {
    // ** addr: 0x8b78e8, size: 0x248
    // 0x8b78e8: EnterFrame
    //     0x8b78e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8b78ec: mov             fp, SP
    // 0x8b78f0: AllocStack(0x60)
    //     0x8b78f0: sub             SP, SP, #0x60
    // 0x8b78f4: SetupParameters(dynamic _ /* r5, fp-0x18 */, dynamic _ /* r6, fp-0x10 */)
    //     0x8b78f4: stur            NULL, [fp, #-8]
    //     0x8b78f8: movz            x0, #0
    //     0x8b78fc: add             x5, fp, w0, sxtw #2
    //     0x8b7900: ldr             x5, [x5, #0x18]
    //     0x8b7904: stur            x5, [fp, #-0x18]
    //     0x8b7908: add             x6, fp, w0, sxtw #2
    //     0x8b790c: ldr             x6, [x6, #0x10]
    //     0x8b7910: stur            x6, [fp, #-0x10]
    // 0x8b7914: LoadField: r1 = r4->field_f
    //     0x8b7914: ldur            w1, [x4, #0xf]
    // 0x8b7918: cbnz            w1, #0x8b7924
    // 0x8b791c: r1 = Null
    //     0x8b791c: mov             x1, NULL
    // 0x8b7920: b               #0x8b7934
    // 0x8b7924: ArrayLoad: r1 = r4[0]  ; List_4
    //     0x8b7924: ldur            w1, [x4, #0x17]
    // 0x8b7928: add             x2, fp, w1, sxtw #2
    // 0x8b792c: ldr             x2, [x2, #0x10]
    // 0x8b7930: mov             x1, x2
    // 0x8b7934: CheckStackOverflow
    //     0x8b7934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7938: cmp             SP, x16
    //     0x8b793c: b.ls            #0x8b7b20
    // 0x8b7940: r2 = Null
    //     0x8b7940: mov             x2, NULL
    // 0x8b7944: r3 = <Y1>
    //     0x8b7944: add             x3, PP, #0xe, lsl #12  ; [pp+0xeb38] TypeArguments: <Y1>
    //     0x8b7948: ldr             x3, [x3, #0xb38]
    // 0x8b794c: r0 = Null
    //     0x8b794c: mov             x0, NULL
    // 0x8b7950: cmp             x2, x0
    // 0x8b7954: b.ne            #0x8b7960
    // 0x8b7958: cmp             x1, x0
    // 0x8b795c: b.eq            #0x8b796c
    // 0x8b7960: r30 = InstantiateTypeArgumentsStub
    //     0x8b7960: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8b7964: LoadField: r30 = r30->field_7
    //     0x8b7964: ldur            lr, [lr, #7]
    // 0x8b7968: blr             lr
    // 0x8b796c: mov             x1, x0
    // 0x8b7970: stur            x1, [fp, #-0x20]
    // 0x8b7974: r0 = InitAsync()
    //     0x8b7974: bl              #0x7348c0  ; InitAsyncStub
    // 0x8b7978: r0 = Null
    //     0x8b7978: mov             x0, NULL
    // 0x8b797c: r0 = SuspendSyncStarAtStart()
    //     0x8b797c: bl              #0x734738  ; SuspendSyncStarAtStartStub
    // 0x8b7980: ldur            x1, [fp, #-0x18]
    // 0x8b7984: LoadField: r2 = r1->field_7
    //     0x8b7984: ldur            w2, [x1, #7]
    // 0x8b7988: DecompressPointer r2
    //     0x8b7988: add             x2, x2, HEAP, lsl #32
    // 0x8b798c: stur            x2, [fp, #-0x20]
    // 0x8b7990: LoadField: r0 = r1->field_b
    //     0x8b7990: ldur            w0, [x1, #0xb]
    // 0x8b7994: r3 = LoadInt32Instr(r0)
    //     0x8b7994: sbfx            x3, x0, #1, #0x1f
    // 0x8b7998: stur            x3, [fp, #-0x30]
    // 0x8b799c: r4 = 0
    //     0x8b799c: movz            x4, #0
    // 0x8b79a0: stur            x4, [fp, #-0x28]
    // 0x8b79a4: CheckStackOverflow
    //     0x8b79a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b79a8: cmp             SP, x16
    //     0x8b79ac: b.ls            #0x8b7b28
    // 0x8b79b0: r0 = LoadClassIdInstr(r1)
    //     0x8b79b0: ldur            x0, [x1, #-1]
    //     0x8b79b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8b79b8: str             x1, [SP]
    // 0x8b79bc: r0 = GDT[cid_x0 + 0xc834]()
    //     0x8b79bc: movz            x17, #0xc834
    //     0x8b79c0: add             lr, x0, x17
    //     0x8b79c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8b79c8: blr             lr
    // 0x8b79cc: r1 = LoadInt32Instr(r0)
    //     0x8b79cc: sbfx            x1, x0, #1, #0x1f
    //     0x8b79d0: tbz             w0, #0, #0x8b79d8
    //     0x8b79d4: ldur            x1, [x0, #7]
    // 0x8b79d8: ldur            x3, [fp, #-0x30]
    // 0x8b79dc: cmp             x3, x1
    // 0x8b79e0: b.ne            #0x8b7b00
    // 0x8b79e4: ldur            x4, [fp, #-0x28]
    // 0x8b79e8: cmp             x4, x1
    // 0x8b79ec: b.ge            #0x8b7af0
    // 0x8b79f0: ldur            x5, [fp, #-0x18]
    // 0x8b79f4: r0 = LoadClassIdInstr(r5)
    //     0x8b79f4: ldur            x0, [x5, #-1]
    //     0x8b79f8: ubfx            x0, x0, #0xc, #0x14
    // 0x8b79fc: mov             x1, x5
    // 0x8b7a00: mov             x2, x4
    // 0x8b7a04: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x8b7a04: movz            x17, #0xd28f
    //     0x8b7a08: add             lr, x0, x17
    //     0x8b7a0c: ldr             lr, [x21, lr, lsl #3]
    //     0x8b7a10: blr             lr
    // 0x8b7a14: mov             x4, x0
    // 0x8b7a18: ldur            x3, [fp, #-0x28]
    // 0x8b7a1c: stur            x4, [fp, #-0x40]
    // 0x8b7a20: add             x5, x3, #1
    // 0x8b7a24: stur            x5, [fp, #-0x38]
    // 0x8b7a28: cmp             w4, NULL
    // 0x8b7a2c: b.ne            #0x8b7a60
    // 0x8b7a30: mov             x0, x4
    // 0x8b7a34: ldur            x2, [fp, #-0x20]
    // 0x8b7a38: r1 = Null
    //     0x8b7a38: mov             x1, NULL
    // 0x8b7a3c: cmp             w2, NULL
    // 0x8b7a40: b.eq            #0x8b7a60
    // 0x8b7a44: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8b7a44: ldur            w4, [x2, #0x17]
    // 0x8b7a48: DecompressPointer r4
    //     0x8b7a48: add             x4, x4, HEAP, lsl #32
    // 0x8b7a4c: r8 = X0
    //     0x8b7a4c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8b7a50: LoadField: r9 = r4->field_7
    //     0x8b7a50: ldur            x9, [x4, #7]
    // 0x8b7a54: r3 = Null
    //     0x8b7a54: add             x3, PP, #0xe, lsl #12  ; [pp+0xeb40] Null
    //     0x8b7a58: ldr             x3, [x3, #0xb40]
    // 0x8b7a5c: blr             x9
    // 0x8b7a60: ldur            x2, [fp, #-0x28]
    // 0x8b7a64: r3 = 0
    //     0x8b7a64: movz            x3, #0
    // 0x8b7a68: add             x0, fp, w3, sxtw #2
    // 0x8b7a6c: LoadField: r0 = r0->field_fffffff8
    //     0x8b7a6c: ldur            x0, [x0, #-8]
    // 0x8b7a70: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x8b7a70: ldur            w4, [x0, #0x17]
    // 0x8b7a74: DecompressPointer r4
    //     0x8b7a74: add             x4, x4, HEAP, lsl #32
    // 0x8b7a78: stur            x4, [fp, #-0x48]
    // 0x8b7a7c: r0 = BoxInt64Instr(r2)
    //     0x8b7a7c: sbfiz           x0, x2, #1, #0x1f
    //     0x8b7a80: cmp             x2, x0, asr #1
    //     0x8b7a84: b.eq            #0x8b7a90
    //     0x8b7a88: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b7a8c: stur            x2, [x0, #7]
    // 0x8b7a90: ldur            x16, [fp, #-0x10]
    // 0x8b7a94: stp             x0, x16, [SP, #8]
    // 0x8b7a98: ldur            x16, [fp, #-0x40]
    // 0x8b7a9c: str             x16, [SP]
    // 0x8b7aa0: ldur            x0, [fp, #-0x10]
    // 0x8b7aa4: ClosureCall
    //     0x8b7aa4: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x8b7aa8: ldur            x2, [x0, #0x1f]
    //     0x8b7aac: blr             x2
    // 0x8b7ab0: ldur            x1, [fp, #-0x48]
    // 0x8b7ab4: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b7ab4: stur            w0, [x1, #0x17]
    //     0x8b7ab8: tbz             w0, #0, #0x8b7ad4
    //     0x8b7abc: ldurb           w16, [x1, #-1]
    //     0x8b7ac0: ldurb           w17, [x0, #-1]
    //     0x8b7ac4: and             x16, x17, x16, lsr #2
    //     0x8b7ac8: tst             x16, HEAP, lsr #32
    //     0x8b7acc: b.eq            #0x8b7ad4
    //     0x8b7ad0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8b7ad4: r0 = true
    //     0x8b7ad4: add             x0, NULL, #0x20  ; true
    // 0x8b7ad8: r0 = SuspendSyncStarAtYield()
    //     0x8b7ad8: bl              #0x7345b4  ; SuspendSyncStarAtYieldStub
    // 0x8b7adc: ldur            x4, [fp, #-0x38]
    // 0x8b7ae0: ldur            x1, [fp, #-0x18]
    // 0x8b7ae4: ldur            x2, [fp, #-0x20]
    // 0x8b7ae8: ldur            x3, [fp, #-0x30]
    // 0x8b7aec: b               #0x8b79a0
    // 0x8b7af0: r0 = false
    //     0x8b7af0: add             x0, NULL, #0x30  ; false
    // 0x8b7af4: LeaveFrame
    //     0x8b7af4: mov             SP, fp
    //     0x8b7af8: ldp             fp, lr, [SP], #0x10
    // 0x8b7afc: ret
    //     0x8b7afc: ret             
    // 0x8b7b00: ldur            x0, [fp, #-0x18]
    // 0x8b7b04: r0 = ConcurrentModificationError()
    //     0x8b7b04: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x8b7b08: mov             x1, x0
    // 0x8b7b0c: ldur            x0, [fp, #-0x18]
    // 0x8b7b10: StoreField: r1->field_b = r0
    //     0x8b7b10: stur            w0, [x1, #0xb]
    // 0x8b7b14: mov             x0, x1
    // 0x8b7b18: r0 = Throw()
    //     0x8b7b18: bl              #0xec04b8  ; ThrowStub
    // 0x8b7b1c: brk             #0
    // 0x8b7b20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7b20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7b24: b               #0x8b7940
    // 0x8b7b28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7b28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7b2c: b               #0x8b79b0
  }
  static _ RemoveFirstElementsQueueExtension.removeFirstElements(/* No info */) {
    // ** addr: 0xab1088, size: 0xf4
    // 0xab1088: EnterFrame
    //     0xab1088: stp             fp, lr, [SP, #-0x10]!
    //     0xab108c: mov             fp, SP
    // 0xab1090: AllocStack(0x18)
    //     0xab1090: sub             SP, SP, #0x18
    // 0xab1094: CheckStackOverflow
    //     0xab1094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab1098: cmp             SP, x16
    //     0xab109c: b.ls            #0xab1168
    // 0xab10a0: ldr             x3, [fp, #0x10]
    // 0xab10a4: LoadField: r4 = r3->field_b
    //     0xab10a4: ldur            w4, [x3, #0xb]
    // 0xab10a8: DecompressPointer r4
    //     0xab10a8: add             x4, x4, HEAP, lsl #32
    // 0xab10ac: stur            x4, [fp, #-0x18]
    // 0xab10b0: r5 = 0
    //     0xab10b0: movz            x5, #0
    // 0xab10b4: stur            x5, [fp, #-0x10]
    // 0xab10b8: CheckStackOverflow
    //     0xab10b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab10bc: cmp             SP, x16
    //     0xab10c0: b.ls            #0xab1170
    // 0xab10c4: cmp             x5, #1
    // 0xab10c8: b.ge            #0xab114c
    // 0xab10cc: LoadField: r6 = r4->field_f
    //     0xab10cc: ldur            w6, [x4, #0xf]
    // 0xab10d0: DecompressPointer r6
    //     0xab10d0: add             x6, x6, HEAP, lsl #32
    // 0xab10d4: stur            x6, [fp, #-8]
    // 0xab10d8: cmp             w6, NULL
    // 0xab10dc: b.eq            #0xab1178
    // 0xab10e0: r0 = LoadClassIdInstr(r6)
    //     0xab10e0: ldur            x0, [x6, #-1]
    //     0xab10e4: ubfx            x0, x0, #0xc, #0x14
    // 0xab10e8: r17 = 6482
    //     0xab10e8: movz            x17, #0x1952
    // 0xab10ec: cmp             x0, x17
    // 0xab10f0: b.eq            #0xab115c
    // 0xab10f4: LoadField: r2 = r6->field_7
    //     0xab10f4: ldur            w2, [x6, #7]
    // 0xab10f8: DecompressPointer r2
    //     0xab10f8: add             x2, x2, HEAP, lsl #32
    // 0xab10fc: r0 = Null
    //     0xab10fc: mov             x0, NULL
    // 0xab1100: r1 = Null
    //     0xab1100: mov             x1, NULL
    // 0xab1104: r8 = DoubleLinkedQueue<X0>?
    //     0xab1104: add             x8, PP, #0xe, lsl #12  ; [pp+0xed58] Type: DoubleLinkedQueue<X0>?
    //     0xab1108: ldr             x8, [x8, #0xd58]
    // 0xab110c: LoadField: r9 = r8->field_7
    //     0xab110c: ldur            x9, [x8, #7]
    // 0xab1110: r3 = Null
    //     0xab1110: add             x3, PP, #0xe, lsl #12  ; [pp+0xed80] Null
    //     0xab1114: ldr             x3, [x3, #0xd80]
    // 0xab1118: blr             x9
    // 0xab111c: ldur            x1, [fp, #-8]
    // 0xab1120: StoreField: r1->field_13 = rNULL
    //     0xab1120: stur            NULL, [x1, #0x13]
    // 0xab1124: r0 = _unlink()
    //     0xab1124: bl              #0xab117c  ; [dart:collection] _DoubleLinkedQueueEntry::_unlink
    // 0xab1128: ldr             x0, [fp, #0x10]
    // 0xab112c: LoadField: r1 = r0->field_f
    //     0xab112c: ldur            x1, [x0, #0xf]
    // 0xab1130: sub             x2, x1, #1
    // 0xab1134: StoreField: r0->field_f = r2
    //     0xab1134: stur            x2, [x0, #0xf]
    // 0xab1138: ldur            x1, [fp, #-0x10]
    // 0xab113c: add             x5, x1, #1
    // 0xab1140: mov             x3, x0
    // 0xab1144: ldur            x4, [fp, #-0x18]
    // 0xab1148: b               #0xab10b4
    // 0xab114c: r0 = Null
    //     0xab114c: mov             x0, NULL
    // 0xab1150: LeaveFrame
    //     0xab1150: mov             SP, fp
    //     0xab1154: ldp             fp, lr, [SP], #0x10
    // 0xab1158: ret
    //     0xab1158: ret             
    // 0xab115c: r0 = noElement()
    //     0xab115c: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xab1160: r0 = Throw()
    //     0xab1160: bl              #0xec04b8  ; ThrowStub
    // 0xab1164: brk             #0
    // 0xab1168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab116c: b               #0xab10a0
    // 0xab1170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab1170: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab1174: b               #0xab10c4
    // 0xab1178: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab1178: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
