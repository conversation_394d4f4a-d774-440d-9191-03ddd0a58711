// lib: , url: package:rxdart/src/utils/empty.dart

// class id: 1051099, size: 0x8
class :: {
}

// class id: 516, size: 0x8, field offset: 0x8
//   const constructor, 
class _Empty extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc3f8dc, size: 0xc
    // 0xc3f8dc: r0 = "<<EMPTY>>"
    //     0xc3f8dc: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c8a8] "<<EMPTY>>"
    //     0xc3f8e0: ldr             x0, [x0, #0x8a8]
    // 0xc3f8e4: ret
    //     0xc3f8e4: ret             
  }
}
