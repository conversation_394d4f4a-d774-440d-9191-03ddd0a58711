// lib: , url: package:rxdart/src/utils/error_and_stacktrace.dart

// class id: 1051100, size: 0x8
class :: {
}

// class id: 515, size: 0x10, field offset: 0x8
class ErrorAndStackTrace extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf33c4, size: 0xbc
    // 0xbf33c4: EnterFrame
    //     0xbf33c4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf33c8: mov             fp, SP
    // 0xbf33cc: AllocStack(0x10)
    //     0xbf33cc: sub             SP, SP, #0x10
    // 0xbf33d0: CheckStackOverflow
    //     0xbf33d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf33d4: cmp             SP, x16
    //     0xbf33d8: b.ls            #0xbf3478
    // 0xbf33dc: ldr             x1, [fp, #0x10]
    // 0xbf33e0: LoadField: r0 = r1->field_7
    //     0xbf33e0: ldur            w0, [x1, #7]
    // 0xbf33e4: DecompressPointer r0
    //     0xbf33e4: add             x0, x0, HEAP, lsl #32
    // 0xbf33e8: r2 = 60
    //     0xbf33e8: movz            x2, #0x3c
    // 0xbf33ec: branchIfSmi(r0, 0xbf33f8)
    //     0xbf33ec: tbz             w0, #0, #0xbf33f8
    // 0xbf33f0: r2 = LoadClassIdInstr(r0)
    //     0xbf33f0: ldur            x2, [x0, #-1]
    //     0xbf33f4: ubfx            x2, x2, #0xc, #0x14
    // 0xbf33f8: str             x0, [SP]
    // 0xbf33fc: mov             x0, x2
    // 0xbf3400: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3400: movz            x17, #0x64af
    //     0xbf3404: add             lr, x0, x17
    //     0xbf3408: ldr             lr, [x21, lr, lsl #3]
    //     0xbf340c: blr             lr
    // 0xbf3410: mov             x1, x0
    // 0xbf3414: ldr             x0, [fp, #0x10]
    // 0xbf3418: stur            x1, [fp, #-8]
    // 0xbf341c: LoadField: r2 = r0->field_b
    //     0xbf341c: ldur            w2, [x0, #0xb]
    // 0xbf3420: DecompressPointer r2
    //     0xbf3420: add             x2, x2, HEAP, lsl #32
    // 0xbf3424: r0 = LoadClassIdInstr(r2)
    //     0xbf3424: ldur            x0, [x2, #-1]
    //     0xbf3428: ubfx            x0, x0, #0xc, #0x14
    // 0xbf342c: str             x2, [SP]
    // 0xbf3430: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3430: movz            x17, #0x64af
    //     0xbf3434: add             lr, x0, x17
    //     0xbf3438: ldr             lr, [x21, lr, lsl #3]
    //     0xbf343c: blr             lr
    // 0xbf3440: ldur            x2, [fp, #-8]
    // 0xbf3444: r3 = LoadInt32Instr(r2)
    //     0xbf3444: sbfx            x3, x2, #1, #0x1f
    //     0xbf3448: tbz             w2, #0, #0xbf3450
    //     0xbf344c: ldur            x3, [x2, #7]
    // 0xbf3450: r2 = LoadInt32Instr(r0)
    //     0xbf3450: sbfx            x2, x0, #1, #0x1f
    // 0xbf3454: eor             x4, x3, x2
    // 0xbf3458: r0 = BoxInt64Instr(r4)
    //     0xbf3458: sbfiz           x0, x4, #1, #0x1f
    //     0xbf345c: cmp             x4, x0, asr #1
    //     0xbf3460: b.eq            #0xbf346c
    //     0xbf3464: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3468: stur            x4, [x0, #7]
    // 0xbf346c: LeaveFrame
    //     0xbf346c: mov             SP, fp
    //     0xbf3470: ldp             fp, lr, [SP], #0x10
    // 0xbf3474: ret
    //     0xbf3474: ret             
    // 0xbf3478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3478: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf347c: b               #0xbf33dc
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3f8e8, size: 0x80
    // 0xc3f8e8: EnterFrame
    //     0xc3f8e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f8ec: mov             fp, SP
    // 0xc3f8f0: AllocStack(0x8)
    //     0xc3f8f0: sub             SP, SP, #8
    // 0xc3f8f4: CheckStackOverflow
    //     0xc3f8f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f8f8: cmp             SP, x16
    //     0xc3f8fc: b.ls            #0xc3f960
    // 0xc3f900: r1 = Null
    //     0xc3f900: mov             x1, NULL
    // 0xc3f904: r2 = 10
    //     0xc3f904: movz            x2, #0xa
    // 0xc3f908: r0 = AllocateArray()
    //     0xc3f908: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3f90c: r16 = "ErrorAndStackTrace{error: "
    //     0xc3f90c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c8b8] "ErrorAndStackTrace{error: "
    //     0xc3f910: ldr             x16, [x16, #0x8b8]
    // 0xc3f914: StoreField: r0->field_f = r16
    //     0xc3f914: stur            w16, [x0, #0xf]
    // 0xc3f918: ldr             x1, [fp, #0x10]
    // 0xc3f91c: LoadField: r2 = r1->field_7
    //     0xc3f91c: ldur            w2, [x1, #7]
    // 0xc3f920: DecompressPointer r2
    //     0xc3f920: add             x2, x2, HEAP, lsl #32
    // 0xc3f924: StoreField: r0->field_13 = r2
    //     0xc3f924: stur            w2, [x0, #0x13]
    // 0xc3f928: r16 = ", stacktrace: "
    //     0xc3f928: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c8c0] ", stacktrace: "
    //     0xc3f92c: ldr             x16, [x16, #0x8c0]
    // 0xc3f930: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3f930: stur            w16, [x0, #0x17]
    // 0xc3f934: LoadField: r2 = r1->field_b
    //     0xc3f934: ldur            w2, [x1, #0xb]
    // 0xc3f938: DecompressPointer r2
    //     0xc3f938: add             x2, x2, HEAP, lsl #32
    // 0xc3f93c: StoreField: r0->field_1b = r2
    //     0xc3f93c: stur            w2, [x0, #0x1b]
    // 0xc3f940: r16 = "}"
    //     0xc3f940: add             x16, PP, #0x12, lsl #12  ; [pp+0x12240] "}"
    //     0xc3f944: ldr             x16, [x16, #0x240]
    // 0xc3f948: StoreField: r0->field_1f = r16
    //     0xc3f948: stur            w16, [x0, #0x1f]
    // 0xc3f94c: str             x0, [SP]
    // 0xc3f950: r0 = _interpolate()
    //     0xc3f950: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3f954: LeaveFrame
    //     0xc3f954: mov             SP, fp
    //     0xc3f958: ldp             fp, lr, [SP], #0x10
    // 0xc3f95c: ret
    //     0xc3f95c: ret             
    // 0xc3f960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3f960: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3f964: b               #0xc3f900
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d880, size: 0x108
    // 0xd7d880: EnterFrame
    //     0xd7d880: stp             fp, lr, [SP, #-0x10]!
    //     0xd7d884: mov             fp, SP
    // 0xd7d888: AllocStack(0x10)
    //     0xd7d888: sub             SP, SP, #0x10
    // 0xd7d88c: CheckStackOverflow
    //     0xd7d88c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7d890: cmp             SP, x16
    //     0xd7d894: b.ls            #0xd7d980
    // 0xd7d898: ldr             x0, [fp, #0x10]
    // 0xd7d89c: cmp             w0, NULL
    // 0xd7d8a0: b.ne            #0xd7d8b4
    // 0xd7d8a4: r0 = false
    //     0xd7d8a4: add             x0, NULL, #0x30  ; false
    // 0xd7d8a8: LeaveFrame
    //     0xd7d8a8: mov             SP, fp
    //     0xd7d8ac: ldp             fp, lr, [SP], #0x10
    // 0xd7d8b0: ret
    //     0xd7d8b0: ret             
    // 0xd7d8b4: ldr             x1, [fp, #0x18]
    // 0xd7d8b8: cmp             w1, w0
    // 0xd7d8bc: b.ne            #0xd7d8c8
    // 0xd7d8c0: r0 = true
    //     0xd7d8c0: add             x0, NULL, #0x20  ; true
    // 0xd7d8c4: b               #0xd7d974
    // 0xd7d8c8: r2 = 60
    //     0xd7d8c8: movz            x2, #0x3c
    // 0xd7d8cc: branchIfSmi(r0, 0xd7d8d8)
    //     0xd7d8cc: tbz             w0, #0, #0xd7d8d8
    // 0xd7d8d0: r2 = LoadClassIdInstr(r0)
    //     0xd7d8d0: ldur            x2, [x0, #-1]
    //     0xd7d8d4: ubfx            x2, x2, #0xc, #0x14
    // 0xd7d8d8: cmp             x2, #0x203
    // 0xd7d8dc: b.ne            #0xd7d970
    // 0xd7d8e0: r16 = ErrorAndStackTrace
    //     0xd7d8e0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c8b0] Type: ErrorAndStackTrace
    //     0xd7d8e4: ldr             x16, [x16, #0x8b0]
    // 0xd7d8e8: r30 = ErrorAndStackTrace
    //     0xd7d8e8: add             lr, PP, #0x1c, lsl #12  ; [pp+0x1c8b0] Type: ErrorAndStackTrace
    //     0xd7d8ec: ldr             lr, [lr, #0x8b0]
    // 0xd7d8f0: stp             lr, x16, [SP]
    // 0xd7d8f4: r0 = ==()
    //     0xd7d8f4: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7d8f8: tbnz            w0, #4, #0xd7d970
    // 0xd7d8fc: ldr             x2, [fp, #0x18]
    // 0xd7d900: ldr             x1, [fp, #0x10]
    // 0xd7d904: LoadField: r0 = r2->field_7
    //     0xd7d904: ldur            w0, [x2, #7]
    // 0xd7d908: DecompressPointer r0
    //     0xd7d908: add             x0, x0, HEAP, lsl #32
    // 0xd7d90c: LoadField: r3 = r1->field_7
    //     0xd7d90c: ldur            w3, [x1, #7]
    // 0xd7d910: DecompressPointer r3
    //     0xd7d910: add             x3, x3, HEAP, lsl #32
    // 0xd7d914: r4 = 60
    //     0xd7d914: movz            x4, #0x3c
    // 0xd7d918: branchIfSmi(r0, 0xd7d924)
    //     0xd7d918: tbz             w0, #0, #0xd7d924
    // 0xd7d91c: r4 = LoadClassIdInstr(r0)
    //     0xd7d91c: ldur            x4, [x0, #-1]
    //     0xd7d920: ubfx            x4, x4, #0xc, #0x14
    // 0xd7d924: stp             x3, x0, [SP]
    // 0xd7d928: mov             x0, x4
    // 0xd7d92c: mov             lr, x0
    // 0xd7d930: ldr             lr, [x21, lr, lsl #3]
    // 0xd7d934: blr             lr
    // 0xd7d938: tbnz            w0, #4, #0xd7d970
    // 0xd7d93c: ldr             x1, [fp, #0x18]
    // 0xd7d940: ldr             x0, [fp, #0x10]
    // 0xd7d944: LoadField: r2 = r1->field_b
    //     0xd7d944: ldur            w2, [x1, #0xb]
    // 0xd7d948: DecompressPointer r2
    //     0xd7d948: add             x2, x2, HEAP, lsl #32
    // 0xd7d94c: LoadField: r1 = r0->field_b
    //     0xd7d94c: ldur            w1, [x0, #0xb]
    // 0xd7d950: DecompressPointer r1
    //     0xd7d950: add             x1, x1, HEAP, lsl #32
    // 0xd7d954: r0 = LoadClassIdInstr(r2)
    //     0xd7d954: ldur            x0, [x2, #-1]
    //     0xd7d958: ubfx            x0, x0, #0xc, #0x14
    // 0xd7d95c: stp             x1, x2, [SP]
    // 0xd7d960: mov             lr, x0
    // 0xd7d964: ldr             lr, [x21, lr, lsl #3]
    // 0xd7d968: blr             lr
    // 0xd7d96c: b               #0xd7d974
    // 0xd7d970: r0 = false
    //     0xd7d970: add             x0, NULL, #0x30  ; false
    // 0xd7d974: LeaveFrame
    //     0xd7d974: mov             SP, fp
    //     0xd7d978: ldp             fp, lr, [SP], #0x10
    // 0xd7d97c: ret
    //     0xd7d97c: ret             
    // 0xd7d980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7d980: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7d984: b               #0xd7d898
  }
}
