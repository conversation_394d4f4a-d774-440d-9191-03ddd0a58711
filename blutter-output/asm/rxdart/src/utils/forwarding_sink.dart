// lib: , url: package:rxdart/src/utils/forwarding_sink.dart

// class id: 1051101, size: 0x8
class :: {
}

// class id: 517, size: 0x10, field offset: 0x8
abstract class ForwardingSink<X0, X1> extends Object {

  get _ sink(/* No info */) {
    // ** addr: 0xab0228, size: 0x44
    // 0xab0228: EnterFrame
    //     0xab0228: stp             fp, lr, [SP, #-0x10]!
    //     0xab022c: mov             fp, SP
    // 0xab0230: LoadField: r0 = r1->field_b
    //     0xab0230: ldur            w0, [x1, #0xb]
    // 0xab0234: DecompressPointer r0
    //     0xab0234: add             x0, x0, HEAP, lsl #32
    // 0xab0238: cmp             w0, NULL
    // 0xab023c: b.eq            #0xab024c
    // 0xab0240: LeaveFrame
    //     0xab0240: mov             SP, fp
    //     0xab0244: ldp             fp, lr, [SP], #0x10
    // 0xab0248: ret
    //     0xab0248: ret             
    // 0xab024c: r0 = StateError()
    //     0xab024c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab0250: mov             x1, x0
    // 0xab0254: r0 = "Must call setSink(sink) before accessing!"
    //     0xab0254: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab0258: ldr             x0, [x0, #0xcd0]
    // 0xab025c: StoreField: r1->field_b = r0
    //     0xab025c: stur            w0, [x1, #0xb]
    // 0xab0260: mov             x0, x1
    // 0xab0264: r0 = Throw()
    //     0xab0264: bl              #0xec04b8  ; ThrowStub
    // 0xab0268: brk             #0
  }
  _ setSink(/* No info */) {
    // ** addr: 0xab026c, size: 0x78
    // 0xab026c: EnterFrame
    //     0xab026c: stp             fp, lr, [SP, #-0x10]!
    //     0xab0270: mov             fp, SP
    // 0xab0274: AllocStack(0x10)
    //     0xab0274: sub             SP, SP, #0x10
    // 0xab0278: SetupParameters(ForwardingSink<X0, X1> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xab0278: mov             x4, x1
    //     0xab027c: mov             x3, x2
    //     0xab0280: stur            x1, [fp, #-8]
    //     0xab0284: stur            x2, [fp, #-0x10]
    // 0xab0288: LoadField: r2 = r4->field_7
    //     0xab0288: ldur            w2, [x4, #7]
    // 0xab028c: DecompressPointer r2
    //     0xab028c: add             x2, x2, HEAP, lsl #32
    // 0xab0290: mov             x0, x3
    // 0xab0294: r1 = Null
    //     0xab0294: mov             x1, NULL
    // 0xab0298: r8 = EventSink<X1>
    //     0xab0298: add             x8, PP, #0xe, lsl #12  ; [pp+0xee78] Type: EventSink<X1>
    //     0xab029c: ldr             x8, [x8, #0xe78]
    // 0xab02a0: LoadField: r9 = r8->field_7
    //     0xab02a0: ldur            x9, [x8, #7]
    // 0xab02a4: r3 = Null
    //     0xab02a4: add             x3, PP, #0xe, lsl #12  ; [pp+0xee80] Null
    //     0xab02a8: ldr             x3, [x3, #0xe80]
    // 0xab02ac: blr             x9
    // 0xab02b0: ldur            x0, [fp, #-0x10]
    // 0xab02b4: ldur            x1, [fp, #-8]
    // 0xab02b8: StoreField: r1->field_b = r0
    //     0xab02b8: stur            w0, [x1, #0xb]
    //     0xab02bc: ldurb           w16, [x1, #-1]
    //     0xab02c0: ldurb           w17, [x0, #-1]
    //     0xab02c4: and             x16, x17, x16, lsr #2
    //     0xab02c8: tst             x16, HEAP, lsr #32
    //     0xab02cc: b.eq            #0xab02d4
    //     0xab02d0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab02d4: ldur            x0, [fp, #-0x10]
    // 0xab02d8: LeaveFrame
    //     0xab02d8: mov             SP, fp
    //     0xab02dc: ldp             fp, lr, [SP], #0x10
    // 0xab02e0: ret
    //     0xab02e0: ret             
  }
}
