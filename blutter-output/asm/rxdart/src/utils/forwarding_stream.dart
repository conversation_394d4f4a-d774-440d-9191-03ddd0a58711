// lib: , url: package:rxdart/src/utils/forwarding_stream.dart

// class id: 1051102, size: 0x8
class :: {

  [closure] static ForwardingSink<Y0, Y1> #sink#initializer(dynamic) {
    // ** addr: 0xaafa00, size: 0x50
    // 0xaafa00: EnterFrame
    //     0xaafa00: stp             fp, lr, [SP, #-0x10]!
    //     0xaafa04: mov             fp, SP
    // 0xaafa08: AllocStack(0x8)
    //     0xaafa08: sub             SP, SP, #8
    // 0xaafa0c: SetupParameters()
    //     0xaafa0c: ldr             x0, [fp, #0x10]
    //     0xaafa10: ldur            w1, [x0, #0x17]
    //     0xaafa14: add             x1, x1, HEAP, lsl #32
    // 0xaafa18: CheckStackOverflow
    //     0xaafa18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaafa1c: cmp             SP, x16
    //     0xaafa20: b.ls            #0xaafa48
    // 0xaafa24: LoadField: r0 = r1->field_13
    //     0xaafa24: ldur            w0, [x1, #0x13]
    // 0xaafa28: DecompressPointer r0
    //     0xaafa28: add             x0, x0, HEAP, lsl #32
    // 0xaafa2c: str             x0, [SP]
    // 0xaafa30: ClosureCall
    //     0xaafa30: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xaafa34: ldur            x2, [x0, #0x1f]
    //     0xaafa38: blr             x2
    // 0xaafa3c: LeaveFrame
    //     0xaafa3c: mov             SP, fp
    //     0xaafa40: ldp             fp, lr, [SP], #0x10
    // 0xaafa44: ret
    //     0xaafa44: ret             
    // 0xaafa48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaafa48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaafa4c: b               #0xaafa24
  }
  static Stream<Y1> _forward<Y0, Y1>(Stream<Y0>, (dynamic) => ForwardingSink<Y0, Y1>) {
    // ** addr: 0xaafa50, size: 0x234
    // 0xaafa50: EnterFrame
    //     0xaafa50: stp             fp, lr, [SP, #-0x10]!
    //     0xaafa54: mov             fp, SP
    // 0xaafa58: AllocStack(0x20)
    //     0xaafa58: sub             SP, SP, #0x20
    // 0xaafa5c: SetupParameters()
    //     0xaafa5c: ldur            w0, [x4, #0xf]
    //     0xaafa60: cbnz            w0, #0xaafa6c
    //     0xaafa64: mov             x2, NULL
    //     0xaafa68: b               #0xaafa7c
    //     0xaafa6c: ldur            w0, [x4, #0x17]
    //     0xaafa70: add             x1, fp, w0, sxtw #2
    //     0xaafa74: ldr             x1, [x1, #0x10]
    //     0xaafa78: mov             x2, x1
    //     0xaafa7c: ldr             x1, [fp, #0x18]
    //     0xaafa80: ldr             x0, [fp, #0x10]
    //     0xaafa84: stur            x2, [fp, #-8]
    // 0xaafa88: CheckStackOverflow
    //     0xaafa88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaafa8c: cmp             SP, x16
    //     0xaafa90: b.ls            #0xaafc7c
    // 0xaafa94: r1 = 7
    //     0xaafa94: movz            x1, #0x7
    // 0xaafa98: r0 = AllocateContext()
    //     0xaafa98: bl              #0xec126c  ; AllocateContextStub
    // 0xaafa9c: mov             x2, x0
    // 0xaafaa0: ldr             x1, [fp, #0x18]
    // 0xaafaa4: stur            x2, [fp, #-0x10]
    // 0xaafaa8: StoreField: r2->field_f = r1
    //     0xaafaa8: stur            w1, [x2, #0xf]
    // 0xaafaac: ldr             x0, [fp, #0x10]
    // 0xaafab0: StoreField: r2->field_13 = r0
    //     0xaafab0: stur            w0, [x2, #0x13]
    // 0xaafab4: r0 = LoadClassIdInstr(r1)
    //     0xaafab4: ldur            x0, [x1, #-1]
    //     0xaafab8: ubfx            x0, x0, #0xc, #0x14
    // 0xaafabc: r0 = GDT[cid_x0 + 0x93e]()
    //     0xaafabc: add             lr, x0, #0x93e
    //     0xaafac0: ldr             lr, [x21, lr, lsl #3]
    //     0xaafac4: blr             lr
    // 0xaafac8: tbnz            w0, #4, #0xaafb1c
    // 0xaafacc: ldur            x1, [fp, #-8]
    // 0xaafad0: r2 = Null
    //     0xaafad0: mov             x2, NULL
    // 0xaafad4: r3 = <Y1>
    //     0xaafad4: add             x3, PP, #0xe, lsl #12  ; [pp+0xec88] TypeArguments: <Y1>
    //     0xaafad8: ldr             x3, [x3, #0xc88]
    // 0xaafadc: r0 = Null
    //     0xaafadc: mov             x0, NULL
    // 0xaafae0: cmp             x2, x0
    // 0xaafae4: b.ne            #0xaafaf0
    // 0xaafae8: cmp             x1, x0
    // 0xaafaec: b.eq            #0xaafafc
    // 0xaafaf0: r30 = InstantiateTypeArgumentsStub
    //     0xaafaf0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xaafaf4: LoadField: r30 = r30->field_7
    //     0xaafaf4: ldur            lr, [lr, #7]
    // 0xaafaf8: blr             lr
    // 0xaafafc: r16 = true
    //     0xaafafc: add             x16, NULL, #0x20  ; true
    // 0xaafb00: str             x16, [SP]
    // 0xaafb04: mov             x1, x0
    // 0xaafb08: r4 = const [0, 0x2, 0x1, 0x1, sync, 0x1, null]
    //     0xaafb08: add             x4, PP, #0xc, lsl #12  ; [pp+0xcfb0] List(7) [0, 0x2, 0x1, 0x1, "sync", 0x1, Null]
    //     0xaafb0c: ldr             x4, [x4, #0xfb0]
    // 0xaafb10: r0 = StreamController.broadcast()
    //     0xaafb10: bl              #0x83522c  ; [dart:async] StreamController::StreamController.broadcast
    // 0xaafb14: mov             x5, x0
    // 0xaafb18: b               #0xaafb68
    // 0xaafb1c: ldur            x1, [fp, #-8]
    // 0xaafb20: r2 = Null
    //     0xaafb20: mov             x2, NULL
    // 0xaafb24: r3 = <Y1>
    //     0xaafb24: add             x3, PP, #0xe, lsl #12  ; [pp+0xec88] TypeArguments: <Y1>
    //     0xaafb28: ldr             x3, [x3, #0xc88]
    // 0xaafb2c: r0 = Null
    //     0xaafb2c: mov             x0, NULL
    // 0xaafb30: cmp             x2, x0
    // 0xaafb34: b.ne            #0xaafb40
    // 0xaafb38: cmp             x1, x0
    // 0xaafb3c: b.eq            #0xaafb4c
    // 0xaafb40: r30 = InstantiateTypeArgumentsStub
    //     0xaafb40: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xaafb44: LoadField: r30 = r30->field_7
    //     0xaafb44: ldur            lr, [lr, #7]
    // 0xaafb48: blr             lr
    // 0xaafb4c: r16 = true
    //     0xaafb4c: add             x16, NULL, #0x20  ; true
    // 0xaafb50: str             x16, [SP]
    // 0xaafb54: mov             x1, x0
    // 0xaafb58: r4 = const [0, 0x2, 0x1, 0x1, sync, 0x1, null]
    //     0xaafb58: add             x4, PP, #0xc, lsl #12  ; [pp+0xcfb0] List(7) [0, 0x2, 0x1, 0x1, "sync", 0x1, Null]
    //     0xaafb5c: ldr             x4, [x4, #0xfb0]
    // 0xaafb60: r0 = StreamController()
    //     0xaafb60: bl              #0x696864  ; [dart:async] StreamController::StreamController
    // 0xaafb64: mov             x5, x0
    // 0xaafb68: ldur            x4, [fp, #-8]
    // 0xaafb6c: ldur            x3, [fp, #-0x10]
    // 0xaafb70: r1 = false
    //     0xaafb70: add             x1, NULL, #0x30  ; false
    // 0xaafb74: mov             x0, x5
    // 0xaafb78: stur            x5, [fp, #-0x18]
    // 0xaafb7c: ArrayStore: r3[0] = r0  ; List_4
    //     0xaafb7c: stur            w0, [x3, #0x17]
    //     0xaafb80: ldurb           w16, [x3, #-1]
    //     0xaafb84: ldurb           w17, [x0, #-1]
    //     0xaafb88: and             x16, x17, x16, lsr #2
    //     0xaafb8c: tst             x16, HEAP, lsr #32
    //     0xaafb90: b.eq            #0xaafb98
    //     0xaafb94: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaafb98: StoreField: r3->field_1b = rNULL
    //     0xaafb98: stur            NULL, [x3, #0x1b]
    // 0xaafb9c: StoreField: r3->field_1f = r1
    //     0xaafb9c: stur            w1, [x3, #0x1f]
    // 0xaafba0: mov             x2, x3
    // 0xaafba4: r1 = Function '#sink#initializer': static.
    //     0xaafba4: add             x1, PP, #0xe, lsl #12  ; [pp+0xec90] AnonymousClosure: static (0xaafa00), in [package:rxdart/src/utils/forwarding_stream.dart] ::_forward (0xaafa50)
    //     0xaafba8: ldr             x1, [x1, #0xc90]
    // 0xaafbac: r0 = AllocateClosure()
    //     0xaafbac: bl              #0xec1630  ; AllocateClosureStub
    // 0xaafbb0: ldur            x3, [fp, #-8]
    // 0xaafbb4: StoreField: r0->field_b = r3
    //     0xaafbb4: stur            w3, [x0, #0xb]
    // 0xaafbb8: ldur            x4, [fp, #-0x10]
    // 0xaafbbc: StoreField: r4->field_23 = r0
    //     0xaafbbc: stur            w0, [x4, #0x23]
    //     0xaafbc0: ldurb           w16, [x4, #-1]
    //     0xaafbc4: ldurb           w17, [x0, #-1]
    //     0xaafbc8: and             x16, x17, x16, lsr #2
    //     0xaafbcc: tst             x16, HEAP, lsr #32
    //     0xaafbd0: b.eq            #0xaafbd8
    //     0xaafbd4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xaafbd8: r0 = Sentinel
    //     0xaafbd8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaafbdc: StoreField: r4->field_27 = r0
    //     0xaafbdc: stur            w0, [x4, #0x27]
    // 0xaafbe0: mov             x2, x4
    // 0xaafbe4: r1 = Function '<anonymous closure>': static.
    //     0xaafbe4: add             x1, PP, #0xe, lsl #12  ; [pp+0xec98] AnonymousClosure: static (0xaaff90), in [package:rxdart/src/utils/forwarding_stream.dart] ::_forward (0xaafa50)
    //     0xaafbe8: ldr             x1, [x1, #0xc98]
    // 0xaafbec: r0 = AllocateClosure()
    //     0xaafbec: bl              #0xec1630  ; AllocateClosureStub
    // 0xaafbf0: ldur            x3, [fp, #-8]
    // 0xaafbf4: StoreField: r0->field_b = r3
    //     0xaafbf4: stur            w3, [x0, #0xb]
    // 0xaafbf8: ldur            x4, [fp, #-0x18]
    // 0xaafbfc: r1 = LoadClassIdInstr(r4)
    //     0xaafbfc: ldur            x1, [x4, #-1]
    //     0xaafc00: ubfx            x1, x1, #0xc, #0x14
    // 0xaafc04: mov             x2, x0
    // 0xaafc08: mov             x0, x1
    // 0xaafc0c: mov             x1, x4
    // 0xaafc10: r0 = GDT[cid_x0 + 0x8fc]()
    //     0xaafc10: add             lr, x0, #0x8fc
    //     0xaafc14: ldr             lr, [x21, lr, lsl #3]
    //     0xaafc18: blr             lr
    // 0xaafc1c: ldur            x2, [fp, #-0x10]
    // 0xaafc20: r1 = Function '<anonymous closure>': static.
    //     0xaafc20: add             x1, PP, #0xe, lsl #12  ; [pp+0xeca0] AnonymousClosure: static (0xaafc84), in [package:rxdart/src/utils/forwarding_stream.dart] ::_forward (0xaafa50)
    //     0xaafc24: ldr             x1, [x1, #0xca0]
    // 0xaafc28: r0 = AllocateClosure()
    //     0xaafc28: bl              #0xec1630  ; AllocateClosureStub
    // 0xaafc2c: mov             x1, x0
    // 0xaafc30: ldur            x0, [fp, #-8]
    // 0xaafc34: StoreField: r1->field_b = r0
    //     0xaafc34: stur            w0, [x1, #0xb]
    // 0xaafc38: ldur            x3, [fp, #-0x18]
    // 0xaafc3c: r0 = LoadClassIdInstr(r3)
    //     0xaafc3c: ldur            x0, [x3, #-1]
    //     0xaafc40: ubfx            x0, x0, #0xc, #0x14
    // 0xaafc44: mov             x2, x1
    // 0xaafc48: mov             x1, x3
    // 0xaafc4c: r0 = GDT[cid_x0 + 0x8f9]()
    //     0xaafc4c: add             lr, x0, #0x8f9
    //     0xaafc50: ldr             lr, [x21, lr, lsl #3]
    //     0xaafc54: blr             lr
    // 0xaafc58: ldur            x1, [fp, #-0x18]
    // 0xaafc5c: r0 = LoadClassIdInstr(r1)
    //     0xaafc5c: ldur            x0, [x1, #-1]
    //     0xaafc60: ubfx            x0, x0, #0xc, #0x14
    // 0xaafc64: r0 = GDT[cid_x0 + 0x39d]()
    //     0xaafc64: add             lr, x0, #0x39d
    //     0xaafc68: ldr             lr, [x21, lr, lsl #3]
    //     0xaafc6c: blr             lr
    // 0xaafc70: LeaveFrame
    //     0xaafc70: mov             SP, fp
    //     0xaafc74: ldp             fp, lr, [SP], #0x10
    // 0xaafc78: ret
    //     0xaafc78: ret             
    // 0xaafc7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaafc7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaafc80: b               #0xaafa94
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xaafc84, size: 0x190
    // 0xaafc84: EnterFrame
    //     0xaafc84: stp             fp, lr, [SP, #-0x10]!
    //     0xaafc88: mov             fp, SP
    // 0xaafc8c: AllocStack(0x18)
    //     0xaafc8c: sub             SP, SP, #0x18
    // 0xaafc90: SetupParameters()
    //     0xaafc90: add             x0, NULL, #0x20  ; true
    //     0xaafc94: ldr             x1, [fp, #0x10]
    //     0xaafc98: ldur            w2, [x1, #0x17]
    //     0xaafc9c: add             x2, x2, HEAP, lsl #32
    //     0xaafca0: stur            x2, [fp, #-8]
    // 0xaafc90: r0 = true
    // 0xaafca4: CheckStackOverflow
    //     0xaafca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaafca8: cmp             SP, x16
    //     0xaafcac: b.ls            #0xaafe0c
    // 0xaafcb0: StoreField: r2->field_1f = r0
    //     0xaafcb0: stur            w0, [x2, #0x1f]
    // 0xaafcb4: LoadField: r1 = r2->field_1b
    //     0xaafcb4: ldur            w1, [x2, #0x1b]
    // 0xaafcb8: DecompressPointer r1
    //     0xaafcb8: add             x1, x1, HEAP, lsl #32
    // 0xaafcbc: cmp             w1, NULL
    // 0xaafcc0: b.ne            #0xaafcd0
    // 0xaafcc4: mov             x1, x2
    // 0xaafcc8: r2 = Null
    //     0xaafcc8: mov             x2, NULL
    // 0xaafccc: b               #0xaafcec
    // 0xaafcd0: r0 = LoadClassIdInstr(r1)
    //     0xaafcd0: ldur            x0, [x1, #-1]
    //     0xaafcd4: ubfx            x0, x0, #0xc, #0x14
    // 0xaafcd8: r0 = GDT[cid_x0 + -0x37]()
    //     0xaafcd8: sub             lr, x0, #0x37
    //     0xaafcdc: ldr             lr, [x21, lr, lsl #3]
    //     0xaafce0: blr             lr
    // 0xaafce4: mov             x2, x0
    // 0xaafce8: ldur            x1, [fp, #-8]
    // 0xaafcec: stur            x2, [fp, #-0x10]
    // 0xaafcf0: StoreField: r1->field_1b = rNULL
    //     0xaafcf0: stur            NULL, [x1, #0x1b]
    // 0xaafcf4: LoadField: r0 = r1->field_27
    //     0xaafcf4: ldur            w0, [x1, #0x27]
    // 0xaafcf8: DecompressPointer r0
    //     0xaafcf8: add             x0, x0, HEAP, lsl #32
    // 0xaafcfc: r16 = Sentinel
    //     0xaafcfc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaafd00: cmp             w0, w16
    // 0xaafd04: b.ne            #0xaafd88
    // 0xaafd08: LoadField: r0 = r1->field_23
    //     0xaafd08: ldur            w0, [x1, #0x23]
    // 0xaafd0c: DecompressPointer r0
    //     0xaafd0c: add             x0, x0, HEAP, lsl #32
    // 0xaafd10: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xaafd10: ldur            w3, [x0, #0x17]
    // 0xaafd14: DecompressPointer r3
    //     0xaafd14: add             x3, x3, HEAP, lsl #32
    // 0xaafd18: LoadField: r0 = r3->field_13
    //     0xaafd18: ldur            w0, [x3, #0x13]
    // 0xaafd1c: DecompressPointer r0
    //     0xaafd1c: add             x0, x0, HEAP, lsl #32
    // 0xaafd20: str             x0, [SP]
    // 0xaafd24: ClosureCall
    //     0xaafd24: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xaafd28: ldur            x2, [x0, #0x1f]
    //     0xaafd2c: blr             x2
    // 0xaafd30: ldur            x1, [fp, #-8]
    // 0xaafd34: LoadField: r2 = r1->field_27
    //     0xaafd34: ldur            w2, [x1, #0x27]
    // 0xaafd38: DecompressPointer r2
    //     0xaafd38: add             x2, x2, HEAP, lsl #32
    // 0xaafd3c: r16 = Sentinel
    //     0xaafd3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaafd40: cmp             w2, w16
    // 0xaafd44: b.ne            #0xaafd70
    // 0xaafd48: StoreField: r1->field_27 = r0
    //     0xaafd48: stur            w0, [x1, #0x27]
    //     0xaafd4c: tbz             w0, #0, #0xaafd68
    //     0xaafd50: ldurb           w16, [x1, #-1]
    //     0xaafd54: ldurb           w17, [x0, #-1]
    //     0xaafd58: and             x16, x17, x16, lsr #2
    //     0xaafd5c: tst             x16, HEAP, lsr #32
    //     0xaafd60: b.eq            #0xaafd68
    //     0xaafd64: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaafd68: mov             x0, x1
    // 0xaafd6c: b               #0xaafd8c
    // 0xaafd70: r16 = "sink"
    //     0xaafd70: add             x16, PP, #0xe, lsl #12  ; [pp+0xeca8] "sink"
    //     0xaafd74: ldr             x16, [x16, #0xca8]
    // 0xaafd78: str             x16, [SP]
    // 0xaafd7c: r0 = _throwLocalAssignedDuringInitialization()
    //     0xaafd7c: bl              #0x64155c  ; [dart:_internal] LateError::_throwLocalAssignedDuringInitialization
    // 0xaafd80: ldur            x0, [fp, #-8]
    // 0xaafd84: b               #0xaafd8c
    // 0xaafd88: ldur            x0, [fp, #-8]
    // 0xaafd8c: LoadField: r1 = r0->field_27
    //     0xaafd8c: ldur            w1, [x0, #0x27]
    // 0xaafd90: DecompressPointer r1
    //     0xaafd90: add             x1, x1, HEAP, lsl #32
    // 0xaafd94: r0 = LoadClassIdInstr(r1)
    //     0xaafd94: ldur            x0, [x1, #-1]
    //     0xaafd98: ubfx            x0, x0, #0xc, #0x14
    // 0xaafd9c: cmp             x0, #0x206
    // 0xaafda0: b.ne            #0xaafdac
    // 0xaafda4: r2 = Null
    //     0xaafda4: mov             x2, NULL
    // 0xaafda8: b               #0xaafdf8
    // 0xaafdac: cmp             x0, #0x207
    // 0xaafdb0: b.ne            #0xaafdbc
    // 0xaafdb4: r2 = Null
    //     0xaafdb4: mov             x2, NULL
    // 0xaafdb8: b               #0xaafdf8
    // 0xaafdbc: LoadField: r0 = r1->field_4b
    //     0xaafdbc: ldur            w0, [x1, #0x4b]
    // 0xaafdc0: DecompressPointer r0
    //     0xaafdc0: add             x0, x0, HEAP, lsl #32
    // 0xaafdc4: cmp             w0, NULL
    // 0xaafdc8: b.ne            #0xaafdd4
    // 0xaafdcc: r0 = Null
    //     0xaafdcc: mov             x0, NULL
    // 0xaafdd0: b               #0xaafdf4
    // 0xaafdd4: r1 = LoadClassIdInstr(r0)
    //     0xaafdd4: ldur            x1, [x0, #-1]
    //     0xaafdd8: ubfx            x1, x1, #0xc, #0x14
    // 0xaafddc: mov             x16, x0
    // 0xaafde0: mov             x0, x1
    // 0xaafde4: mov             x1, x16
    // 0xaafde8: r0 = GDT[cid_x0 + -0x37]()
    //     0xaafde8: sub             lr, x0, #0x37
    //     0xaafdec: ldr             lr, [x21, lr, lsl #3]
    //     0xaafdf0: blr             lr
    // 0xaafdf4: mov             x2, x0
    // 0xaafdf8: ldur            x1, [fp, #-0x10]
    // 0xaafdfc: r0 = waitTwoFutures()
    //     0xaafdfc: bl              #0xaafe14  ; [package:rxdart/src/utils/future.dart] ::waitTwoFutures
    // 0xaafe00: LeaveFrame
    //     0xaafe00: mov             SP, fp
    //     0xaafe04: ldp             fp, lr, [SP], #0x10
    // 0xaafe08: ret
    //     0xaafe08: ret             
    // 0xaafe0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaafe0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaafe10: b               #0xaafcb0
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xaaff90, size: 0x298
    // 0xaaff90: EnterFrame
    //     0xaaff90: stp             fp, lr, [SP, #-0x10]!
    //     0xaaff94: mov             fp, SP
    // 0xaaff98: AllocStack(0x20)
    //     0xaaff98: sub             SP, SP, #0x20
    // 0xaaff9c: SetupParameters()
    //     0xaaff9c: ldr             x0, [fp, #0x10]
    //     0xaaffa0: ldur            w3, [x0, #0x17]
    //     0xaaffa4: add             x3, x3, HEAP, lsl #32
    //     0xaaffa8: stur            x3, [fp, #-0x10]
    // 0xaaffac: CheckStackOverflow
    //     0xaaffac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaffb0: cmp             SP, x16
    //     0xaaffb4: b.ls            #0xab0220
    // 0xaaffb8: LoadField: r4 = r0->field_b
    //     0xaaffb8: ldur            w4, [x0, #0xb]
    // 0xaaffbc: DecompressPointer r4
    //     0xaaffbc: add             x4, x4, HEAP, lsl #32
    // 0xaaffc0: mov             x2, x3
    // 0xaaffc4: stur            x4, [fp, #-8]
    // 0xaaffc8: r1 = Function 'listenToUpstream': static.
    //     0xaaffc8: add             x1, PP, #0xe, lsl #12  ; [pp+0xecc8] AnonymousClosure: static (0xab02e4), in [package:rxdart/src/utils/forwarding_stream.dart] ::_forward (0xaafa50)
    //     0xaaffcc: ldr             x1, [x1, #0xcc8]
    // 0xaaffd0: r0 = AllocateClosure()
    //     0xaaffd0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaaffd4: mov             x1, x0
    // 0xaaffd8: ldur            x0, [fp, #-8]
    // 0xaaffdc: stur            x1, [fp, #-0x18]
    // 0xaaffe0: StoreField: r1->field_b = r0
    //     0xaaffe0: stur            w0, [x1, #0xb]
    // 0xaaffe4: ldur            x2, [fp, #-0x10]
    // 0xaaffe8: LoadField: r0 = r2->field_27
    //     0xaaffe8: ldur            w0, [x2, #0x27]
    // 0xaaffec: DecompressPointer r0
    //     0xaaffec: add             x0, x0, HEAP, lsl #32
    // 0xaafff0: r16 = Sentinel
    //     0xaafff0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaafff4: cmp             w0, w16
    // 0xaafff8: b.ne            #0xab007c
    // 0xaafffc: LoadField: r0 = r2->field_23
    //     0xaafffc: ldur            w0, [x2, #0x23]
    // 0xab0000: DecompressPointer r0
    //     0xab0000: add             x0, x0, HEAP, lsl #32
    // 0xab0004: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xab0004: ldur            w3, [x0, #0x17]
    // 0xab0008: DecompressPointer r3
    //     0xab0008: add             x3, x3, HEAP, lsl #32
    // 0xab000c: LoadField: r0 = r3->field_13
    //     0xab000c: ldur            w0, [x3, #0x13]
    // 0xab0010: DecompressPointer r0
    //     0xab0010: add             x0, x0, HEAP, lsl #32
    // 0xab0014: str             x0, [SP]
    // 0xab0018: ClosureCall
    //     0xab0018: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xab001c: ldur            x2, [x0, #0x1f]
    //     0xab0020: blr             x2
    // 0xab0024: ldur            x1, [fp, #-0x10]
    // 0xab0028: LoadField: r2 = r1->field_27
    //     0xab0028: ldur            w2, [x1, #0x27]
    // 0xab002c: DecompressPointer r2
    //     0xab002c: add             x2, x2, HEAP, lsl #32
    // 0xab0030: r16 = Sentinel
    //     0xab0030: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab0034: cmp             w2, w16
    // 0xab0038: b.ne            #0xab0064
    // 0xab003c: StoreField: r1->field_27 = r0
    //     0xab003c: stur            w0, [x1, #0x27]
    //     0xab0040: tbz             w0, #0, #0xab005c
    //     0xab0044: ldurb           w16, [x1, #-1]
    //     0xab0048: ldurb           w17, [x0, #-1]
    //     0xab004c: and             x16, x17, x16, lsr #2
    //     0xab0050: tst             x16, HEAP, lsr #32
    //     0xab0054: b.eq            #0xab005c
    //     0xab0058: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab005c: mov             x0, x1
    // 0xab0060: b               #0xab0080
    // 0xab0064: r16 = "sink"
    //     0xab0064: add             x16, PP, #0xe, lsl #12  ; [pp+0xeca8] "sink"
    //     0xab0068: ldr             x16, [x16, #0xca8]
    // 0xab006c: str             x16, [SP]
    // 0xab0070: r0 = _throwLocalAssignedDuringInitialization()
    //     0xab0070: bl              #0x64155c  ; [dart:_internal] LateError::_throwLocalAssignedDuringInitialization
    // 0xab0074: ldur            x0, [fp, #-0x10]
    // 0xab0078: b               #0xab0080
    // 0xab007c: ldur            x0, [fp, #-0x10]
    // 0xab0080: LoadField: r1 = r0->field_27
    //     0xab0080: ldur            w1, [x0, #0x27]
    // 0xab0084: DecompressPointer r1
    //     0xab0084: add             x1, x1, HEAP, lsl #32
    // 0xab0088: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xab0088: ldur            w2, [x0, #0x17]
    // 0xab008c: DecompressPointer r2
    //     0xab008c: add             x2, x2, HEAP, lsl #32
    // 0xab0090: r0 = setSink()
    //     0xab0090: bl              #0xab026c  ; [package:rxdart/src/utils/forwarding_sink.dart] ForwardingSink::setSink
    // 0xab0094: ldur            x1, [fp, #-0x10]
    // 0xab0098: LoadField: r0 = r1->field_27
    //     0xab0098: ldur            w0, [x1, #0x27]
    // 0xab009c: DecompressPointer r0
    //     0xab009c: add             x0, x0, HEAP, lsl #32
    // 0xab00a0: r16 = Sentinel
    //     0xab00a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab00a4: cmp             w0, w16
    // 0xab00a8: b.ne            #0xab012c
    // 0xab00ac: LoadField: r0 = r1->field_23
    //     0xab00ac: ldur            w0, [x1, #0x23]
    // 0xab00b0: DecompressPointer r0
    //     0xab00b0: add             x0, x0, HEAP, lsl #32
    // 0xab00b4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xab00b4: ldur            w2, [x0, #0x17]
    // 0xab00b8: DecompressPointer r2
    //     0xab00b8: add             x2, x2, HEAP, lsl #32
    // 0xab00bc: LoadField: r0 = r2->field_13
    //     0xab00bc: ldur            w0, [x2, #0x13]
    // 0xab00c0: DecompressPointer r0
    //     0xab00c0: add             x0, x0, HEAP, lsl #32
    // 0xab00c4: str             x0, [SP]
    // 0xab00c8: ClosureCall
    //     0xab00c8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xab00cc: ldur            x2, [x0, #0x1f]
    //     0xab00d0: blr             x2
    // 0xab00d4: ldur            x1, [fp, #-0x10]
    // 0xab00d8: LoadField: r2 = r1->field_27
    //     0xab00d8: ldur            w2, [x1, #0x27]
    // 0xab00dc: DecompressPointer r2
    //     0xab00dc: add             x2, x2, HEAP, lsl #32
    // 0xab00e0: r16 = Sentinel
    //     0xab00e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab00e4: cmp             w2, w16
    // 0xab00e8: b.ne            #0xab0114
    // 0xab00ec: StoreField: r1->field_27 = r0
    //     0xab00ec: stur            w0, [x1, #0x27]
    //     0xab00f0: tbz             w0, #0, #0xab010c
    //     0xab00f4: ldurb           w16, [x1, #-1]
    //     0xab00f8: ldurb           w17, [x0, #-1]
    //     0xab00fc: and             x16, x17, x16, lsr #2
    //     0xab0100: tst             x16, HEAP, lsr #32
    //     0xab0104: b.eq            #0xab010c
    //     0xab0108: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab010c: mov             x0, x1
    // 0xab0110: b               #0xab0130
    // 0xab0114: r16 = "sink"
    //     0xab0114: add             x16, PP, #0xe, lsl #12  ; [pp+0xeca8] "sink"
    //     0xab0118: ldr             x16, [x16, #0xca8]
    // 0xab011c: str             x16, [SP]
    // 0xab0120: r0 = _throwLocalAssignedDuringInitialization()
    //     0xab0120: bl              #0x64155c  ; [dart:_internal] LateError::_throwLocalAssignedDuringInitialization
    // 0xab0124: ldur            x0, [fp, #-0x10]
    // 0xab0128: b               #0xab0130
    // 0xab012c: ldur            x0, [fp, #-0x10]
    // 0xab0130: LoadField: r2 = r0->field_27
    //     0xab0130: ldur            w2, [x0, #0x27]
    // 0xab0134: DecompressPointer r2
    //     0xab0134: add             x2, x2, HEAP, lsl #32
    // 0xab0138: stur            x2, [fp, #-8]
    // 0xab013c: r0 = LoadClassIdInstr(r2)
    //     0xab013c: ldur            x0, [x2, #-1]
    //     0xab0140: ubfx            x0, x0, #0xc, #0x14
    // 0xab0144: cmp             x0, #0x206
    // 0xab0148: b.ne            #0xab0194
    // 0xab014c: mov             x1, x2
    // 0xab0150: r0 = sink()
    //     0xab0150: bl              #0xab0228  ; [package:rxdart/src/utils/forwarding_sink.dart] ForwardingSink::sink
    // 0xab0154: ldur            x1, [fp, #-8]
    // 0xab0158: LoadField: r2 = r1->field_f
    //     0xab0158: ldur            w2, [x1, #0xf]
    // 0xab015c: DecompressPointer r2
    //     0xab015c: add             x2, x2, HEAP, lsl #32
    // 0xab0160: LoadField: r3 = r1->field_13
    //     0xab0160: ldur            w3, [x1, #0x13]
    // 0xab0164: DecompressPointer r3
    //     0xab0164: add             x3, x3, HEAP, lsl #32
    // 0xab0168: r1 = LoadClassIdInstr(r0)
    //     0xab0168: ldur            x1, [x0, #-1]
    //     0xab016c: ubfx            x1, x1, #0xc, #0x14
    // 0xab0170: str             x3, [SP]
    // 0xab0174: mov             x16, x0
    // 0xab0178: mov             x0, x1
    // 0xab017c: mov             x1, x16
    // 0xab0180: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xab0180: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xab0184: r0 = GDT[cid_x0 + 0x9b0]()
    //     0xab0184: add             lr, x0, #0x9b0
    //     0xab0188: ldr             lr, [x21, lr, lsl #3]
    //     0xab018c: blr             lr
    // 0xab0190: b               #0xab01d8
    // 0xab0194: mov             x1, x2
    // 0xab0198: cmp             x0, #0x207
    // 0xab019c: b.ne            #0xab01d8
    // 0xab01a0: LoadField: r0 = r1->field_b
    //     0xab01a0: ldur            w0, [x1, #0xb]
    // 0xab01a4: DecompressPointer r0
    //     0xab01a4: add             x0, x0, HEAP, lsl #32
    // 0xab01a8: cmp             w0, NULL
    // 0xab01ac: b.eq            #0xab0200
    // 0xab01b0: LoadField: r2 = r1->field_f
    //     0xab01b0: ldur            w2, [x1, #0xf]
    // 0xab01b4: DecompressPointer r2
    //     0xab01b4: add             x2, x2, HEAP, lsl #32
    // 0xab01b8: r1 = LoadClassIdInstr(r0)
    //     0xab01b8: ldur            x1, [x0, #-1]
    //     0xab01bc: ubfx            x1, x1, #0xc, #0x14
    // 0xab01c0: mov             x16, x0
    // 0xab01c4: mov             x0, x1
    // 0xab01c8: mov             x1, x16
    // 0xab01cc: r0 = GDT[cid_x0 + 0xdbe]()
    //     0xab01cc: add             lr, x0, #0xdbe
    //     0xab01d0: ldr             lr, [x21, lr, lsl #3]
    //     0xab01d4: blr             lr
    // 0xab01d8: ldur            x16, [fp, #-0x18]
    // 0xab01dc: str             x16, [SP]
    // 0xab01e0: ldur            x0, [fp, #-0x18]
    // 0xab01e4: ClosureCall
    //     0xab01e4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xab01e8: ldur            x2, [x0, #0x1f]
    //     0xab01ec: blr             x2
    // 0xab01f0: r0 = Null
    //     0xab01f0: mov             x0, NULL
    // 0xab01f4: LeaveFrame
    //     0xab01f4: mov             SP, fp
    //     0xab01f8: ldp             fp, lr, [SP], #0x10
    // 0xab01fc: ret
    //     0xab01fc: ret             
    // 0xab0200: r0 = StateError()
    //     0xab0200: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab0204: mov             x1, x0
    // 0xab0208: r0 = "Must call setSink(sink) before accessing!"
    //     0xab0208: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xab020c: ldr             x0, [x0, #0xcd0]
    // 0xab0210: StoreField: r1->field_b = r0
    //     0xab0210: stur            w0, [x1, #0xb]
    // 0xab0214: mov             x0, x1
    // 0xab0218: r0 = Throw()
    //     0xab0218: bl              #0xec04b8  ; ThrowStub
    // 0xab021c: brk             #0
    // 0xab0220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab0220: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab0224: b               #0xaaffb8
  }
  [closure] static void listenToUpstream(dynamic, [void]) {
    // ** addr: 0xab02e4, size: 0x4a4
    // 0xab02e4: EnterFrame
    //     0xab02e4: stp             fp, lr, [SP, #-0x10]!
    //     0xab02e8: mov             fp, SP
    // 0xab02ec: AllocStack(0x40)
    //     0xab02ec: sub             SP, SP, #0x40
    // 0xab02f0: SetupParameters(dynamic _ /* r0 */)
    //     0xab02f0: ldur            w0, [x4, #0x13]
    //     0xab02f4: sub             x1, x0, #2
    //     0xab02f8: add             x0, fp, w1, sxtw #2
    //     0xab02fc: ldr             x0, [x0, #0x10]
    //     0xab0300: ldur            w2, [x0, #0x17]
    //     0xab0304: add             x2, x2, HEAP, lsl #32
    //     0xab0308: stur            x2, [fp, #-0x18]
    // 0xab030c: CheckStackOverflow
    //     0xab030c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab0310: cmp             SP, x16
    //     0xab0314: b.ls            #0xab0780
    // 0xab0318: LoadField: r1 = r0->field_b
    //     0xab0318: ldur            w1, [x0, #0xb]
    // 0xab031c: DecompressPointer r1
    //     0xab031c: add             x1, x1, HEAP, lsl #32
    // 0xab0320: stur            x1, [fp, #-0x10]
    // 0xab0324: LoadField: r0 = r2->field_1f
    //     0xab0324: ldur            w0, [x2, #0x1f]
    // 0xab0328: DecompressPointer r0
    //     0xab0328: add             x0, x0, HEAP, lsl #32
    // 0xab032c: tbnz            w0, #4, #0xab0340
    // 0xab0330: r0 = Null
    //     0xab0330: mov             x0, NULL
    // 0xab0334: LeaveFrame
    //     0xab0334: mov             SP, fp
    //     0xab0338: ldp             fp, lr, [SP], #0x10
    // 0xab033c: ret
    //     0xab033c: ret             
    // 0xab0340: LoadField: r3 = r2->field_f
    //     0xab0340: ldur            w3, [x2, #0xf]
    // 0xab0344: DecompressPointer r3
    //     0xab0344: add             x3, x3, HEAP, lsl #32
    // 0xab0348: stur            x3, [fp, #-8]
    // 0xab034c: LoadField: r0 = r2->field_27
    //     0xab034c: ldur            w0, [x2, #0x27]
    // 0xab0350: DecompressPointer r0
    //     0xab0350: add             x0, x0, HEAP, lsl #32
    // 0xab0354: r16 = Sentinel
    //     0xab0354: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab0358: cmp             w0, w16
    // 0xab035c: b.ne            #0xab03e0
    // 0xab0360: LoadField: r0 = r2->field_23
    //     0xab0360: ldur            w0, [x2, #0x23]
    // 0xab0364: DecompressPointer r0
    //     0xab0364: add             x0, x0, HEAP, lsl #32
    // 0xab0368: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xab0368: ldur            w4, [x0, #0x17]
    // 0xab036c: DecompressPointer r4
    //     0xab036c: add             x4, x4, HEAP, lsl #32
    // 0xab0370: LoadField: r0 = r4->field_13
    //     0xab0370: ldur            w0, [x4, #0x13]
    // 0xab0374: DecompressPointer r0
    //     0xab0374: add             x0, x0, HEAP, lsl #32
    // 0xab0378: str             x0, [SP]
    // 0xab037c: ClosureCall
    //     0xab037c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xab0380: ldur            x2, [x0, #0x1f]
    //     0xab0384: blr             x2
    // 0xab0388: ldur            x2, [fp, #-0x18]
    // 0xab038c: LoadField: r1 = r2->field_27
    //     0xab038c: ldur            w1, [x2, #0x27]
    // 0xab0390: DecompressPointer r1
    //     0xab0390: add             x1, x1, HEAP, lsl #32
    // 0xab0394: r16 = Sentinel
    //     0xab0394: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab0398: cmp             w1, w16
    // 0xab039c: b.ne            #0xab03c8
    // 0xab03a0: StoreField: r2->field_27 = r0
    //     0xab03a0: stur            w0, [x2, #0x27]
    //     0xab03a4: tbz             w0, #0, #0xab03c0
    //     0xab03a8: ldurb           w16, [x2, #-1]
    //     0xab03ac: ldurb           w17, [x0, #-1]
    //     0xab03b0: and             x16, x17, x16, lsr #2
    //     0xab03b4: tst             x16, HEAP, lsr #32
    //     0xab03b8: b.eq            #0xab03c0
    //     0xab03bc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xab03c0: mov             x0, x2
    // 0xab03c4: b               #0xab03e4
    // 0xab03c8: r16 = "sink"
    //     0xab03c8: add             x16, PP, #0xe, lsl #12  ; [pp+0xeca8] "sink"
    //     0xab03cc: ldr             x16, [x16, #0xca8]
    // 0xab03d0: str             x16, [SP]
    // 0xab03d4: r0 = _throwLocalAssignedDuringInitialization()
    //     0xab03d4: bl              #0x64155c  ; [dart:_internal] LateError::_throwLocalAssignedDuringInitialization
    // 0xab03d8: ldur            x0, [fp, #-0x18]
    // 0xab03dc: b               #0xab03e4
    // 0xab03e0: ldur            x0, [fp, #-0x18]
    // 0xab03e4: LoadField: r3 = r0->field_27
    //     0xab03e4: ldur            w3, [x0, #0x27]
    // 0xab03e8: DecompressPointer r3
    //     0xab03e8: add             x3, x3, HEAP, lsl #32
    // 0xab03ec: stur            x3, [fp, #-0x20]
    // 0xab03f0: r1 = LoadClassIdInstr(r3)
    //     0xab03f0: ldur            x1, [x3, #-1]
    //     0xab03f4: ubfx            x1, x1, #0xc, #0x14
    // 0xab03f8: cmp             x1, #0x206
    // 0xab03fc: b.ne            #0xab0418
    // 0xab0400: mov             x2, x3
    // 0xab0404: r1 = Function 'onData':.
    //     0xab0404: add             x1, PP, #0xe, lsl #12  ; [pp+0xecd8] AnonymousClosure: (0xab2048), in [package:rxdart/src/transformers/start_with_error.dart] _StartWithErrorStreamSink::onData (0xab2084)
    //     0xab0408: ldr             x1, [x1, #0xcd8]
    // 0xab040c: r0 = AllocateClosure()
    //     0xab040c: bl              #0xec1630  ; AllocateClosureStub
    // 0xab0410: mov             x4, x0
    // 0xab0414: b               #0xab044c
    // 0xab0418: cmp             x1, #0x207
    // 0xab041c: b.ne            #0xab0438
    // 0xab0420: ldur            x2, [fp, #-0x20]
    // 0xab0424: r1 = Function 'onData':.
    //     0xab0424: add             x1, PP, #0xe, lsl #12  ; [pp+0xece0] AnonymousClosure: (0xab1f48), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onData (0xab1f84)
    //     0xab0428: ldr             x1, [x1, #0xce0]
    // 0xab042c: r0 = AllocateClosure()
    //     0xab042c: bl              #0xec1630  ; AllocateClosureStub
    // 0xab0430: mov             x4, x0
    // 0xab0434: b               #0xab044c
    // 0xab0438: ldur            x2, [fp, #-0x20]
    // 0xab043c: r1 = Function 'onData':.
    //     0xab043c: add             x1, PP, #0xe, lsl #12  ; [pp+0xece8] AnonymousClosure: (0xab1558), in [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::onData (0xab1594)
    //     0xab0440: ldr             x1, [x1, #0xce8]
    // 0xab0444: r0 = AllocateClosure()
    //     0xab0444: bl              #0xec1630  ; AllocateClosureStub
    // 0xab0448: mov             x4, x0
    // 0xab044c: ldur            x3, [fp, #-0x20]
    // 0xab0450: mov             x0, x4
    // 0xab0454: ldur            x1, [fp, #-0x10]
    // 0xab0458: stur            x4, [fp, #-0x28]
    // 0xab045c: r2 = Null
    //     0xab045c: mov             x2, NULL
    // 0xab0460: r8 = (dynamic this, Y0) => void?
    //     0xab0460: add             x8, PP, #0xe, lsl #12  ; [pp+0xecf0] FunctionType: (dynamic this, Y0) => void?
    //     0xab0464: ldr             x8, [x8, #0xcf0]
    // 0xab0468: LoadField: r9 = r8->field_7
    //     0xab0468: ldur            x9, [x8, #7]
    // 0xab046c: r3 = Null
    //     0xab046c: add             x3, PP, #0xe, lsl #12  ; [pp+0xecf8] Null
    //     0xab0470: ldr             x3, [x3, #0xcf8]
    // 0xab0474: blr             x9
    // 0xab0478: ldur            x0, [fp, #-0x20]
    // 0xab047c: r16 = Sentinel
    //     0xab047c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab0480: cmp             w0, w16
    // 0xab0484: b.ne            #0xab050c
    // 0xab0488: ldur            x2, [fp, #-0x18]
    // 0xab048c: LoadField: r0 = r2->field_23
    //     0xab048c: ldur            w0, [x2, #0x23]
    // 0xab0490: DecompressPointer r0
    //     0xab0490: add             x0, x0, HEAP, lsl #32
    // 0xab0494: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab0494: ldur            w1, [x0, #0x17]
    // 0xab0498: DecompressPointer r1
    //     0xab0498: add             x1, x1, HEAP, lsl #32
    // 0xab049c: LoadField: r0 = r1->field_13
    //     0xab049c: ldur            w0, [x1, #0x13]
    // 0xab04a0: DecompressPointer r0
    //     0xab04a0: add             x0, x0, HEAP, lsl #32
    // 0xab04a4: str             x0, [SP]
    // 0xab04a8: ClosureCall
    //     0xab04a8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xab04ac: ldur            x2, [x0, #0x1f]
    //     0xab04b0: blr             x2
    // 0xab04b4: ldur            x2, [fp, #-0x18]
    // 0xab04b8: LoadField: r1 = r2->field_27
    //     0xab04b8: ldur            w1, [x2, #0x27]
    // 0xab04bc: DecompressPointer r1
    //     0xab04bc: add             x1, x1, HEAP, lsl #32
    // 0xab04c0: r16 = Sentinel
    //     0xab04c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab04c4: cmp             w1, w16
    // 0xab04c8: b.ne            #0xab04f4
    // 0xab04cc: StoreField: r2->field_27 = r0
    //     0xab04cc: stur            w0, [x2, #0x27]
    //     0xab04d0: tbz             w0, #0, #0xab04ec
    //     0xab04d4: ldurb           w16, [x2, #-1]
    //     0xab04d8: ldurb           w17, [x0, #-1]
    //     0xab04dc: and             x16, x17, x16, lsr #2
    //     0xab04e0: tst             x16, HEAP, lsr #32
    //     0xab04e4: b.eq            #0xab04ec
    //     0xab04e8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xab04ec: mov             x0, x2
    // 0xab04f0: b               #0xab0510
    // 0xab04f4: r16 = "sink"
    //     0xab04f4: add             x16, PP, #0xe, lsl #12  ; [pp+0xeca8] "sink"
    //     0xab04f8: ldr             x16, [x16, #0xca8]
    // 0xab04fc: str             x16, [SP]
    // 0xab0500: r0 = _throwLocalAssignedDuringInitialization()
    //     0xab0500: bl              #0x64155c  ; [dart:_internal] LateError::_throwLocalAssignedDuringInitialization
    // 0xab0504: ldur            x0, [fp, #-0x18]
    // 0xab0508: b               #0xab0510
    // 0xab050c: ldur            x0, [fp, #-0x18]
    // 0xab0510: LoadField: r3 = r0->field_27
    //     0xab0510: ldur            w3, [x0, #0x27]
    // 0xab0514: DecompressPointer r3
    //     0xab0514: add             x3, x3, HEAP, lsl #32
    // 0xab0518: stur            x3, [fp, #-0x20]
    // 0xab051c: r1 = LoadClassIdInstr(r3)
    //     0xab051c: ldur            x1, [x3, #-1]
    //     0xab0520: ubfx            x1, x1, #0xc, #0x14
    // 0xab0524: cmp             x1, #0x206
    // 0xab0528: b.ne            #0xab0544
    // 0xab052c: mov             x2, x3
    // 0xab0530: r1 = Function 'onError':.
    //     0xab0530: add             x1, PP, #0xe, lsl #12  ; [pp+0xed08] AnonymousClosure: (0xab1518), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onError (0xab1450)
    //     0xab0534: ldr             x1, [x1, #0xd08]
    // 0xab0538: r0 = AllocateClosure()
    //     0xab0538: bl              #0xec1630  ; AllocateClosureStub
    // 0xab053c: mov             x1, x0
    // 0xab0540: b               #0xab0578
    // 0xab0544: cmp             x1, #0x207
    // 0xab0548: b.ne            #0xab0564
    // 0xab054c: ldur            x2, [fp, #-0x20]
    // 0xab0550: r1 = Function 'onError':.
    //     0xab0550: add             x1, PP, #0xe, lsl #12  ; [pp+0xed10] AnonymousClosure: (0xab14d8), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onError (0xab1450)
    //     0xab0554: ldr             x1, [x1, #0xd10]
    // 0xab0558: r0 = AllocateClosure()
    //     0xab0558: bl              #0xec1630  ; AllocateClosureStub
    // 0xab055c: mov             x1, x0
    // 0xab0560: b               #0xab0578
    // 0xab0564: ldur            x2, [fp, #-0x20]
    // 0xab0568: r1 = Function 'onError':.
    //     0xab0568: add             x1, PP, #0xe, lsl #12  ; [pp+0xed18] AnonymousClosure: (0xab1410), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onError (0xab1450)
    //     0xab056c: ldr             x1, [x1, #0xd18]
    // 0xab0570: r0 = AllocateClosure()
    //     0xab0570: bl              #0xec1630  ; AllocateClosureStub
    // 0xab0574: mov             x1, x0
    // 0xab0578: ldur            x0, [fp, #-0x20]
    // 0xab057c: stur            x1, [fp, #-0x30]
    // 0xab0580: r16 = Sentinel
    //     0xab0580: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab0584: cmp             w0, w16
    // 0xab0588: b.ne            #0xab0610
    // 0xab058c: ldur            x2, [fp, #-0x18]
    // 0xab0590: LoadField: r0 = r2->field_23
    //     0xab0590: ldur            w0, [x2, #0x23]
    // 0xab0594: DecompressPointer r0
    //     0xab0594: add             x0, x0, HEAP, lsl #32
    // 0xab0598: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xab0598: ldur            w3, [x0, #0x17]
    // 0xab059c: DecompressPointer r3
    //     0xab059c: add             x3, x3, HEAP, lsl #32
    // 0xab05a0: LoadField: r0 = r3->field_13
    //     0xab05a0: ldur            w0, [x3, #0x13]
    // 0xab05a4: DecompressPointer r0
    //     0xab05a4: add             x0, x0, HEAP, lsl #32
    // 0xab05a8: str             x0, [SP]
    // 0xab05ac: ClosureCall
    //     0xab05ac: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xab05b0: ldur            x2, [x0, #0x1f]
    //     0xab05b4: blr             x2
    // 0xab05b8: ldur            x2, [fp, #-0x18]
    // 0xab05bc: LoadField: r1 = r2->field_27
    //     0xab05bc: ldur            w1, [x2, #0x27]
    // 0xab05c0: DecompressPointer r1
    //     0xab05c0: add             x1, x1, HEAP, lsl #32
    // 0xab05c4: r16 = Sentinel
    //     0xab05c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab05c8: cmp             w1, w16
    // 0xab05cc: b.ne            #0xab05f8
    // 0xab05d0: StoreField: r2->field_27 = r0
    //     0xab05d0: stur            w0, [x2, #0x27]
    //     0xab05d4: tbz             w0, #0, #0xab05f0
    //     0xab05d8: ldurb           w16, [x2, #-1]
    //     0xab05dc: ldurb           w17, [x0, #-1]
    //     0xab05e0: and             x16, x17, x16, lsr #2
    //     0xab05e4: tst             x16, HEAP, lsr #32
    //     0xab05e8: b.eq            #0xab05f0
    //     0xab05ec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xab05f0: mov             x0, x2
    // 0xab05f4: b               #0xab0614
    // 0xab05f8: r16 = "sink"
    //     0xab05f8: add             x16, PP, #0xe, lsl #12  ; [pp+0xeca8] "sink"
    //     0xab05fc: ldr             x16, [x16, #0xca8]
    // 0xab0600: str             x16, [SP]
    // 0xab0604: r0 = _throwLocalAssignedDuringInitialization()
    //     0xab0604: bl              #0x64155c  ; [dart:_internal] LateError::_throwLocalAssignedDuringInitialization
    // 0xab0608: ldur            x0, [fp, #-0x18]
    // 0xab060c: b               #0xab0614
    // 0xab0610: ldur            x0, [fp, #-0x18]
    // 0xab0614: LoadField: r2 = r0->field_27
    //     0xab0614: ldur            w2, [x0, #0x27]
    // 0xab0618: DecompressPointer r2
    //     0xab0618: add             x2, x2, HEAP, lsl #32
    // 0xab061c: r1 = LoadClassIdInstr(r2)
    //     0xab061c: ldur            x1, [x2, #-1]
    //     0xab0620: ubfx            x1, x1, #0xc, #0x14
    // 0xab0624: cmp             x1, #0x206
    // 0xab0628: b.ne            #0xab063c
    // 0xab062c: r1 = Function 'onDone':.
    //     0xab062c: add             x1, PP, #0xe, lsl #12  ; [pp+0xed20] AnonymousClosure: (0xab13d8), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onDone (0xab1360)
    //     0xab0630: ldr             x1, [x1, #0xd20]
    // 0xab0634: r0 = AllocateClosure()
    //     0xab0634: bl              #0xec1630  ; AllocateClosureStub
    // 0xab0638: b               #0xab0660
    // 0xab063c: cmp             x1, #0x207
    // 0xab0640: b.ne            #0xab0654
    // 0xab0644: r1 = Function 'onDone':.
    //     0xab0644: add             x1, PP, #0xe, lsl #12  ; [pp+0xed28] AnonymousClosure: (0xab1328), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onDone (0xab1360)
    //     0xab0648: ldr             x1, [x1, #0xd28]
    // 0xab064c: r0 = AllocateClosure()
    //     0xab064c: bl              #0xec1630  ; AllocateClosureStub
    // 0xab0650: b               #0xab0660
    // 0xab0654: r1 = Function 'onDone':.
    //     0xab0654: add             x1, PP, #0xe, lsl #12  ; [pp+0xed30] AnonymousClosure: (0xab0a38), in [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::onDone (0xab0a70)
    //     0xab0658: ldr             x1, [x1, #0xd30]
    // 0xab065c: r0 = AllocateClosure()
    //     0xab065c: bl              #0xec1630  ; AllocateClosureStub
    // 0xab0660: ldur            x3, [fp, #-0x18]
    // 0xab0664: ldur            x1, [fp, #-8]
    // 0xab0668: r2 = LoadClassIdInstr(r1)
    //     0xab0668: ldur            x2, [x1, #-1]
    //     0xab066c: ubfx            x2, x2, #0xc, #0x14
    // 0xab0670: ldur            x16, [fp, #-0x30]
    // 0xab0674: stp             x0, x16, [SP]
    // 0xab0678: mov             x0, x2
    // 0xab067c: ldur            x2, [fp, #-0x28]
    // 0xab0680: r4 = const [0, 0x4, 0x2, 0x2, onDone, 0x3, onError, 0x2, null]
    //     0xab0680: add             x4, PP, #0xc, lsl #12  ; [pp+0xc168] List(9) [0, 0x4, 0x2, 0x2, "onDone", 0x3, "onError", 0x2, Null]
    //     0xab0684: ldr             x4, [x4, #0x168]
    // 0xab0688: r0 = GDT[cid_x0 + 0x64e]()
    //     0xab0688: add             lr, x0, #0x64e
    //     0xab068c: ldr             lr, [x21, lr, lsl #3]
    //     0xab0690: blr             lr
    // 0xab0694: ldur            x2, [fp, #-0x18]
    // 0xab0698: StoreField: r2->field_1b = r0
    //     0xab0698: stur            w0, [x2, #0x1b]
    //     0xab069c: ldurb           w16, [x2, #-1]
    //     0xab06a0: ldurb           w17, [x0, #-1]
    //     0xab06a4: and             x16, x17, x16, lsr #2
    //     0xab06a8: tst             x16, HEAP, lsr #32
    //     0xab06ac: b.eq            #0xab06b4
    //     0xab06b0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xab06b4: LoadField: r1 = r2->field_f
    //     0xab06b4: ldur            w1, [x2, #0xf]
    // 0xab06b8: DecompressPointer r1
    //     0xab06b8: add             x1, x1, HEAP, lsl #32
    // 0xab06bc: r0 = LoadClassIdInstr(r1)
    //     0xab06bc: ldur            x0, [x1, #-1]
    //     0xab06c0: ubfx            x0, x0, #0xc, #0x14
    // 0xab06c4: r0 = GDT[cid_x0 + 0x93e]()
    //     0xab06c4: add             lr, x0, #0x93e
    //     0xab06c8: ldr             lr, [x21, lr, lsl #3]
    //     0xab06cc: blr             lr
    // 0xab06d0: tbz             w0, #4, #0xab0770
    // 0xab06d4: ldur            x0, [fp, #-0x18]
    // 0xab06d8: ldur            x3, [fp, #-0x10]
    // 0xab06dc: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xab06dc: ldur            w4, [x0, #0x17]
    // 0xab06e0: DecompressPointer r4
    //     0xab06e0: add             x4, x4, HEAP, lsl #32
    // 0xab06e4: mov             x2, x0
    // 0xab06e8: stur            x4, [fp, #-8]
    // 0xab06ec: r1 = Function '<anonymous closure>': static.
    //     0xab06ec: add             x1, PP, #0xe, lsl #12  ; [pp+0xed38] AnonymousClosure: static (0xab08dc), in [package:rxdart/src/utils/forwarding_stream.dart] ::_forward (0xaafa50)
    //     0xab06f0: ldr             x1, [x1, #0xd38]
    // 0xab06f4: r0 = AllocateClosure()
    //     0xab06f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xab06f8: ldur            x3, [fp, #-0x10]
    // 0xab06fc: StoreField: r0->field_b = r3
    //     0xab06fc: stur            w3, [x0, #0xb]
    // 0xab0700: ldur            x4, [fp, #-8]
    // 0xab0704: r1 = LoadClassIdInstr(r4)
    //     0xab0704: ldur            x1, [x4, #-1]
    //     0xab0708: ubfx            x1, x1, #0xc, #0x14
    // 0xab070c: mov             x2, x0
    // 0xab0710: mov             x0, x1
    // 0xab0714: mov             x1, x4
    // 0xab0718: r0 = GDT[cid_x0 + 0x990]()
    //     0xab0718: add             lr, x0, #0x990
    //     0xab071c: ldr             lr, [x21, lr, lsl #3]
    //     0xab0720: blr             lr
    // 0xab0724: ldur            x2, [fp, #-0x18]
    // 0xab0728: r1 = Function '<anonymous closure>': static.
    //     0xab0728: add             x1, PP, #0xe, lsl #12  ; [pp+0xed40] AnonymousClosure: static (0xab0788), in [package:rxdart/src/utils/forwarding_stream.dart] ::_forward (0xaafa50)
    //     0xab072c: ldr             x1, [x1, #0xd40]
    // 0xab0730: r0 = AllocateClosure()
    //     0xab0730: bl              #0xec1630  ; AllocateClosureStub
    // 0xab0734: mov             x1, x0
    // 0xab0738: ldur            x0, [fp, #-0x10]
    // 0xab073c: StoreField: r1->field_b = r0
    //     0xab073c: stur            w0, [x1, #0xb]
    // 0xab0740: ldur            x0, [fp, #-8]
    // 0xab0744: r2 = LoadClassIdInstr(r0)
    //     0xab0744: ldur            x2, [x0, #-1]
    //     0xab0748: ubfx            x2, x2, #0xc, #0x14
    // 0xab074c: mov             x16, x1
    // 0xab0750: mov             x1, x2
    // 0xab0754: mov             x2, x16
    // 0xab0758: mov             x16, x0
    // 0xab075c: mov             x0, x1
    // 0xab0760: mov             x1, x16
    // 0xab0764: r0 = GDT[cid_x0 + 0x89e]()
    //     0xab0764: add             lr, x0, #0x89e
    //     0xab0768: ldr             lr, [x21, lr, lsl #3]
    //     0xab076c: blr             lr
    // 0xab0770: r0 = Null
    //     0xab0770: mov             x0, NULL
    // 0xab0774: LeaveFrame
    //     0xab0774: mov             SP, fp
    //     0xab0778: ldp             fp, lr, [SP], #0x10
    // 0xab077c: ret
    //     0xab077c: ret             
    // 0xab0780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab0780: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab0784: b               #0xab0318
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xab0788, size: 0x154
    // 0xab0788: EnterFrame
    //     0xab0788: stp             fp, lr, [SP, #-0x10]!
    //     0xab078c: mov             fp, SP
    // 0xab0790: AllocStack(0x10)
    //     0xab0790: sub             SP, SP, #0x10
    // 0xab0794: SetupParameters()
    //     0xab0794: ldr             x0, [fp, #0x10]
    //     0xab0798: ldur            w2, [x0, #0x17]
    //     0xab079c: add             x2, x2, HEAP, lsl #32
    //     0xab07a0: stur            x2, [fp, #-8]
    // 0xab07a4: CheckStackOverflow
    //     0xab07a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab07a8: cmp             SP, x16
    //     0xab07ac: b.ls            #0xab08d0
    // 0xab07b0: LoadField: r1 = r2->field_1b
    //     0xab07b0: ldur            w1, [x2, #0x1b]
    // 0xab07b4: DecompressPointer r1
    //     0xab07b4: add             x1, x1, HEAP, lsl #32
    // 0xab07b8: cmp             w1, NULL
    // 0xab07bc: b.eq            #0xab08d8
    // 0xab07c0: r0 = LoadClassIdInstr(r1)
    //     0xab07c0: ldur            x0, [x1, #-1]
    //     0xab07c4: ubfx            x0, x0, #0xc, #0x14
    // 0xab07c8: r0 = GDT[cid_x0 + 0x3e9]()
    //     0xab07c8: add             lr, x0, #0x3e9
    //     0xab07cc: ldr             lr, [x21, lr, lsl #3]
    //     0xab07d0: blr             lr
    // 0xab07d4: ldur            x1, [fp, #-8]
    // 0xab07d8: LoadField: r0 = r1->field_27
    //     0xab07d8: ldur            w0, [x1, #0x27]
    // 0xab07dc: DecompressPointer r0
    //     0xab07dc: add             x0, x0, HEAP, lsl #32
    // 0xab07e0: r16 = Sentinel
    //     0xab07e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab07e4: cmp             w0, w16
    // 0xab07e8: b.ne            #0xab086c
    // 0xab07ec: LoadField: r0 = r1->field_23
    //     0xab07ec: ldur            w0, [x1, #0x23]
    // 0xab07f0: DecompressPointer r0
    //     0xab07f0: add             x0, x0, HEAP, lsl #32
    // 0xab07f4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xab07f4: ldur            w2, [x0, #0x17]
    // 0xab07f8: DecompressPointer r2
    //     0xab07f8: add             x2, x2, HEAP, lsl #32
    // 0xab07fc: LoadField: r0 = r2->field_13
    //     0xab07fc: ldur            w0, [x2, #0x13]
    // 0xab0800: DecompressPointer r0
    //     0xab0800: add             x0, x0, HEAP, lsl #32
    // 0xab0804: str             x0, [SP]
    // 0xab0808: ClosureCall
    //     0xab0808: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xab080c: ldur            x2, [x0, #0x1f]
    //     0xab0810: blr             x2
    // 0xab0814: ldur            x1, [fp, #-8]
    // 0xab0818: LoadField: r2 = r1->field_27
    //     0xab0818: ldur            w2, [x1, #0x27]
    // 0xab081c: DecompressPointer r2
    //     0xab081c: add             x2, x2, HEAP, lsl #32
    // 0xab0820: r16 = Sentinel
    //     0xab0820: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab0824: cmp             w2, w16
    // 0xab0828: b.ne            #0xab0854
    // 0xab082c: StoreField: r1->field_27 = r0
    //     0xab082c: stur            w0, [x1, #0x27]
    //     0xab0830: tbz             w0, #0, #0xab084c
    //     0xab0834: ldurb           w16, [x1, #-1]
    //     0xab0838: ldurb           w17, [x0, #-1]
    //     0xab083c: and             x16, x17, x16, lsr #2
    //     0xab0840: tst             x16, HEAP, lsr #32
    //     0xab0844: b.eq            #0xab084c
    //     0xab0848: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab084c: mov             x0, x1
    // 0xab0850: b               #0xab0870
    // 0xab0854: r16 = "sink"
    //     0xab0854: add             x16, PP, #0xe, lsl #12  ; [pp+0xeca8] "sink"
    //     0xab0858: ldr             x16, [x16, #0xca8]
    // 0xab085c: str             x16, [SP]
    // 0xab0860: r0 = _throwLocalAssignedDuringInitialization()
    //     0xab0860: bl              #0x64155c  ; [dart:_internal] LateError::_throwLocalAssignedDuringInitialization
    // 0xab0864: ldur            x0, [fp, #-8]
    // 0xab0868: b               #0xab0870
    // 0xab086c: ldur            x0, [fp, #-8]
    // 0xab0870: LoadField: r1 = r0->field_27
    //     0xab0870: ldur            w1, [x0, #0x27]
    // 0xab0874: DecompressPointer r1
    //     0xab0874: add             x1, x1, HEAP, lsl #32
    // 0xab0878: r0 = LoadClassIdInstr(r1)
    //     0xab0878: ldur            x0, [x1, #-1]
    //     0xab087c: ubfx            x0, x0, #0xc, #0x14
    // 0xab0880: cmp             x0, #0x206
    // 0xab0884: b.eq            #0xab08c0
    // 0xab0888: cmp             x0, #0x207
    // 0xab088c: b.eq            #0xab08c0
    // 0xab0890: LoadField: r0 = r1->field_4b
    //     0xab0890: ldur            w0, [x1, #0x4b]
    // 0xab0894: DecompressPointer r0
    //     0xab0894: add             x0, x0, HEAP, lsl #32
    // 0xab0898: cmp             w0, NULL
    // 0xab089c: b.eq            #0xab08c0
    // 0xab08a0: r1 = LoadClassIdInstr(r0)
    //     0xab08a0: ldur            x1, [x0, #-1]
    //     0xab08a4: ubfx            x1, x1, #0xc, #0x14
    // 0xab08a8: mov             x16, x0
    // 0xab08ac: mov             x0, x1
    // 0xab08b0: mov             x1, x16
    // 0xab08b4: r0 = GDT[cid_x0 + 0x3e9]()
    //     0xab08b4: add             lr, x0, #0x3e9
    //     0xab08b8: ldr             lr, [x21, lr, lsl #3]
    //     0xab08bc: blr             lr
    // 0xab08c0: r0 = Null
    //     0xab08c0: mov             x0, NULL
    // 0xab08c4: LeaveFrame
    //     0xab08c4: mov             SP, fp
    //     0xab08c8: ldp             fp, lr, [SP], #0x10
    // 0xab08cc: ret
    //     0xab08cc: ret             
    // 0xab08d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab08d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab08d4: b               #0xab07b0
    // 0xab08d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab08d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xab08dc, size: 0x15c
    // 0xab08dc: EnterFrame
    //     0xab08dc: stp             fp, lr, [SP, #-0x10]!
    //     0xab08e0: mov             fp, SP
    // 0xab08e4: AllocStack(0x10)
    //     0xab08e4: sub             SP, SP, #0x10
    // 0xab08e8: SetupParameters()
    //     0xab08e8: ldr             x0, [fp, #0x10]
    //     0xab08ec: ldur            w2, [x0, #0x17]
    //     0xab08f0: add             x2, x2, HEAP, lsl #32
    //     0xab08f4: stur            x2, [fp, #-8]
    // 0xab08f8: CheckStackOverflow
    //     0xab08f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab08fc: cmp             SP, x16
    //     0xab0900: b.ls            #0xab0a2c
    // 0xab0904: LoadField: r1 = r2->field_1b
    //     0xab0904: ldur            w1, [x2, #0x1b]
    // 0xab0908: DecompressPointer r1
    //     0xab0908: add             x1, x1, HEAP, lsl #32
    // 0xab090c: cmp             w1, NULL
    // 0xab0910: b.eq            #0xab0a34
    // 0xab0914: r0 = LoadClassIdInstr(r1)
    //     0xab0914: ldur            x0, [x1, #-1]
    //     0xab0918: ubfx            x0, x0, #0xc, #0x14
    // 0xab091c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab091c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab0920: r0 = GDT[cid_x0 + 0x40b]()
    //     0xab0920: add             lr, x0, #0x40b
    //     0xab0924: ldr             lr, [x21, lr, lsl #3]
    //     0xab0928: blr             lr
    // 0xab092c: ldur            x1, [fp, #-8]
    // 0xab0930: LoadField: r0 = r1->field_27
    //     0xab0930: ldur            w0, [x1, #0x27]
    // 0xab0934: DecompressPointer r0
    //     0xab0934: add             x0, x0, HEAP, lsl #32
    // 0xab0938: r16 = Sentinel
    //     0xab0938: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab093c: cmp             w0, w16
    // 0xab0940: b.ne            #0xab09c4
    // 0xab0944: LoadField: r0 = r1->field_23
    //     0xab0944: ldur            w0, [x1, #0x23]
    // 0xab0948: DecompressPointer r0
    //     0xab0948: add             x0, x0, HEAP, lsl #32
    // 0xab094c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xab094c: ldur            w2, [x0, #0x17]
    // 0xab0950: DecompressPointer r2
    //     0xab0950: add             x2, x2, HEAP, lsl #32
    // 0xab0954: LoadField: r0 = r2->field_13
    //     0xab0954: ldur            w0, [x2, #0x13]
    // 0xab0958: DecompressPointer r0
    //     0xab0958: add             x0, x0, HEAP, lsl #32
    // 0xab095c: str             x0, [SP]
    // 0xab0960: ClosureCall
    //     0xab0960: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xab0964: ldur            x2, [x0, #0x1f]
    //     0xab0968: blr             x2
    // 0xab096c: ldur            x1, [fp, #-8]
    // 0xab0970: LoadField: r2 = r1->field_27
    //     0xab0970: ldur            w2, [x1, #0x27]
    // 0xab0974: DecompressPointer r2
    //     0xab0974: add             x2, x2, HEAP, lsl #32
    // 0xab0978: r16 = Sentinel
    //     0xab0978: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab097c: cmp             w2, w16
    // 0xab0980: b.ne            #0xab09ac
    // 0xab0984: StoreField: r1->field_27 = r0
    //     0xab0984: stur            w0, [x1, #0x27]
    //     0xab0988: tbz             w0, #0, #0xab09a4
    //     0xab098c: ldurb           w16, [x1, #-1]
    //     0xab0990: ldurb           w17, [x0, #-1]
    //     0xab0994: and             x16, x17, x16, lsr #2
    //     0xab0998: tst             x16, HEAP, lsr #32
    //     0xab099c: b.eq            #0xab09a4
    //     0xab09a0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab09a4: mov             x0, x1
    // 0xab09a8: b               #0xab09c8
    // 0xab09ac: r16 = "sink"
    //     0xab09ac: add             x16, PP, #0xe, lsl #12  ; [pp+0xeca8] "sink"
    //     0xab09b0: ldr             x16, [x16, #0xca8]
    // 0xab09b4: str             x16, [SP]
    // 0xab09b8: r0 = _throwLocalAssignedDuringInitialization()
    //     0xab09b8: bl              #0x64155c  ; [dart:_internal] LateError::_throwLocalAssignedDuringInitialization
    // 0xab09bc: ldur            x0, [fp, #-8]
    // 0xab09c0: b               #0xab09c8
    // 0xab09c4: ldur            x0, [fp, #-8]
    // 0xab09c8: LoadField: r1 = r0->field_27
    //     0xab09c8: ldur            w1, [x0, #0x27]
    // 0xab09cc: DecompressPointer r1
    //     0xab09cc: add             x1, x1, HEAP, lsl #32
    // 0xab09d0: r0 = LoadClassIdInstr(r1)
    //     0xab09d0: ldur            x0, [x1, #-1]
    //     0xab09d4: ubfx            x0, x0, #0xc, #0x14
    // 0xab09d8: cmp             x0, #0x206
    // 0xab09dc: b.eq            #0xab0a1c
    // 0xab09e0: cmp             x0, #0x207
    // 0xab09e4: b.eq            #0xab0a1c
    // 0xab09e8: LoadField: r0 = r1->field_4b
    //     0xab09e8: ldur            w0, [x1, #0x4b]
    // 0xab09ec: DecompressPointer r0
    //     0xab09ec: add             x0, x0, HEAP, lsl #32
    // 0xab09f0: cmp             w0, NULL
    // 0xab09f4: b.eq            #0xab0a1c
    // 0xab09f8: r1 = LoadClassIdInstr(r0)
    //     0xab09f8: ldur            x1, [x0, #-1]
    //     0xab09fc: ubfx            x1, x1, #0xc, #0x14
    // 0xab0a00: mov             x16, x0
    // 0xab0a04: mov             x0, x1
    // 0xab0a08: mov             x1, x16
    // 0xab0a0c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab0a0c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab0a10: r0 = GDT[cid_x0 + 0x40b]()
    //     0xab0a10: add             lr, x0, #0x40b
    //     0xab0a14: ldr             lr, [x21, lr, lsl #3]
    //     0xab0a18: blr             lr
    // 0xab0a1c: r0 = Null
    //     0xab0a1c: mov             x0, NULL
    // 0xab0a20: LeaveFrame
    //     0xab0a20: mov             SP, fp
    //     0xab0a24: ldp             fp, lr, [SP], #0x10
    // 0xab0a28: ret
    //     0xab0a28: ret             
    // 0xab0a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab0a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab0a30: b               #0xab0904
    // 0xab0a34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab0a34: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static Stream<Y1> forwardStream<Y0, Y1>(Stream<Y0>, (dynamic) => ForwardingSink<Y0, Y1>) {
    // ** addr: 0xccdac4, size: 0xac
    // 0xccdac4: EnterFrame
    //     0xccdac4: stp             fp, lr, [SP, #-0x10]!
    //     0xccdac8: mov             fp, SP
    // 0xccdacc: AllocStack(0x20)
    //     0xccdacc: sub             SP, SP, #0x20
    // 0xccdad0: SetupParameters()
    //     0xccdad0: ldur            w0, [x4, #0xf]
    //     0xccdad4: cbnz            w0, #0xccdae0
    //     0xccdad8: mov             x3, NULL
    //     0xccdadc: b               #0xccdaf0
    //     0xccdae0: ldur            w0, [x4, #0x17]
    //     0xccdae4: add             x1, fp, w0, sxtw #2
    //     0xccdae8: ldr             x1, [x1, #0x10]
    //     0xccdaec: mov             x3, x1
    //     0xccdaf0: ldr             x2, [fp, #0x18]
    //     0xccdaf4: stur            x3, [fp, #-8]
    // 0xccdaf8: CheckStackOverflow
    //     0xccdaf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xccdafc: cmp             SP, x16
    //     0xccdb00: b.ls            #0xccdb68
    // 0xccdb04: r0 = LoadClassIdInstr(r2)
    //     0xccdb04: ldur            x0, [x2, #-1]
    //     0xccdb08: ubfx            x0, x0, #0xc, #0x14
    // 0xccdb0c: mov             x1, x2
    // 0xccdb10: r0 = GDT[cid_x0 + 0x93e]()
    //     0xccdb10: add             lr, x0, #0x93e
    //     0xccdb14: ldr             lr, [x21, lr, lsl #3]
    //     0xccdb18: blr             lr
    // 0xccdb1c: tbnz            w0, #4, #0xccdb40
    // 0xccdb20: ldur            x16, [fp, #-8]
    // 0xccdb24: ldr             lr, [fp, #0x18]
    // 0xccdb28: stp             lr, x16, [SP, #8]
    // 0xccdb2c: ldr             x16, [fp, #0x10]
    // 0xccdb30: str             x16, [SP]
    // 0xccdb34: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xccdb34: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xccdb38: r0 = _forwardMulti()
    //     0xccdb38: bl              #0xccdb70  ; [package:rxdart/src/utils/forwarding_stream.dart] ::_forwardMulti
    // 0xccdb3c: b               #0xccdb5c
    // 0xccdb40: ldur            x16, [fp, #-8]
    // 0xccdb44: ldr             lr, [fp, #0x18]
    // 0xccdb48: stp             lr, x16, [SP, #8]
    // 0xccdb4c: ldr             x16, [fp, #0x10]
    // 0xccdb50: str             x16, [SP]
    // 0xccdb54: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xccdb54: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xccdb58: r0 = _forward()
    //     0xccdb58: bl              #0xaafa50  ; [package:rxdart/src/utils/forwarding_stream.dart] ::_forward
    // 0xccdb5c: LeaveFrame
    //     0xccdb5c: mov             SP, fp
    //     0xccdb60: ldp             fp, lr, [SP], #0x10
    // 0xccdb64: ret
    //     0xccdb64: ret             
    // 0xccdb68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xccdb68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xccdb6c: b               #0xccdb04
  }
  static Stream<Y1> _forwardMulti<Y0, Y1>(Stream<Y0>, (dynamic) => ForwardingSink<Y0, Y1>) {
    // ** addr: 0xccdb70, size: 0xe4
    // 0xccdb70: EnterFrame
    //     0xccdb70: stp             fp, lr, [SP, #-0x10]!
    //     0xccdb74: mov             fp, SP
    // 0xccdb78: AllocStack(0x18)
    //     0xccdb78: sub             SP, SP, #0x18
    // 0xccdb7c: SetupParameters()
    //     0xccdb7c: ldur            w0, [x4, #0xf]
    //     0xccdb80: cbnz            w0, #0xccdb8c
    //     0xccdb84: mov             x2, NULL
    //     0xccdb88: b               #0xccdb9c
    //     0xccdb8c: ldur            w0, [x4, #0x17]
    //     0xccdb90: add             x1, fp, w0, sxtw #2
    //     0xccdb94: ldr             x1, [x1, #0x10]
    //     0xccdb98: mov             x2, x1
    //     0xccdb9c: ldr             x1, [fp, #0x18]
    //     0xccdba0: ldr             x0, [fp, #0x10]
    //     0xccdba4: stur            x2, [fp, #-8]
    // 0xccdba8: CheckStackOverflow
    //     0xccdba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xccdbac: cmp             SP, x16
    //     0xccdbb0: b.ls            #0xccdc4c
    // 0xccdbb4: r1 = 2
    //     0xccdbb4: movz            x1, #0x2
    // 0xccdbb8: r0 = AllocateContext()
    //     0xccdbb8: bl              #0xec126c  ; AllocateContextStub
    // 0xccdbbc: mov             x4, x0
    // 0xccdbc0: ldr             x0, [fp, #0x18]
    // 0xccdbc4: stur            x4, [fp, #-0x10]
    // 0xccdbc8: StoreField: r4->field_f = r0
    //     0xccdbc8: stur            w0, [x4, #0xf]
    // 0xccdbcc: ldr             x0, [fp, #0x10]
    // 0xccdbd0: StoreField: r4->field_13 = r0
    //     0xccdbd0: stur            w0, [x4, #0x13]
    // 0xccdbd4: ldur            x1, [fp, #-8]
    // 0xccdbd8: r2 = Null
    //     0xccdbd8: mov             x2, NULL
    // 0xccdbdc: r3 = <Y1>
    //     0xccdbdc: add             x3, PP, #0xe, lsl #12  ; [pp+0xec88] TypeArguments: <Y1>
    //     0xccdbe0: ldr             x3, [x3, #0xc88]
    // 0xccdbe4: r0 = Null
    //     0xccdbe4: mov             x0, NULL
    // 0xccdbe8: cmp             x2, x0
    // 0xccdbec: b.ne            #0xccdbf8
    // 0xccdbf0: cmp             x1, x0
    // 0xccdbf4: b.eq            #0xccdc04
    // 0xccdbf8: r30 = InstantiateTypeArgumentsStub
    //     0xccdbf8: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xccdbfc: LoadField: r30 = r30->field_7
    //     0xccdbfc: ldur            lr, [lr, #7]
    // 0xccdc00: blr             lr
    // 0xccdc04: ldur            x2, [fp, #-0x10]
    // 0xccdc08: r1 = Function '<anonymous closure>': static.
    //     0xccdc08: add             x1, PP, #0xe, lsl #12  ; [pp+0xee90] AnonymousClosure: static (0xccdc54), in [package:rxdart/src/utils/forwarding_stream.dart] ::_forwardMulti (0xccdb70)
    //     0xccdc0c: ldr             x1, [x1, #0xe90]
    // 0xccdc10: stur            x0, [fp, #-0x10]
    // 0xccdc14: r0 = AllocateClosure()
    //     0xccdc14: bl              #0xec1630  ; AllocateClosureStub
    // 0xccdc18: mov             x1, x0
    // 0xccdc1c: ldur            x0, [fp, #-8]
    // 0xccdc20: StoreField: r1->field_b = r0
    //     0xccdc20: stur            w0, [x1, #0xb]
    // 0xccdc24: r16 = true
    //     0xccdc24: add             x16, NULL, #0x20  ; true
    // 0xccdc28: str             x16, [SP]
    // 0xccdc2c: mov             x2, x1
    // 0xccdc30: ldur            x1, [fp, #-0x10]
    // 0xccdc34: r4 = const [0, 0x3, 0x1, 0x2, isBroadcast, 0x2, null]
    //     0xccdc34: add             x4, PP, #0xe, lsl #12  ; [pp+0xee98] List(7) [0, 0x3, 0x1, 0x2, "isBroadcast", 0x2, Null]
    //     0xccdc38: ldr             x4, [x4, #0xe98]
    // 0xccdc3c: r0 = Stream.multi()
    //     0xccdc3c: bl              #0x726af8  ; [dart:async] Stream::Stream.multi
    // 0xccdc40: LeaveFrame
    //     0xccdc40: mov             SP, fp
    //     0xccdc44: ldp             fp, lr, [SP], #0x10
    // 0xccdc48: ret
    //     0xccdc48: ret             
    // 0xccdc4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xccdc4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xccdc50: b               #0xccdbb4
  }
  [closure] static void <anonymous closure>(dynamic, MultiStreamController<Y1>) {
    // ** addr: 0xccdc54, size: 0x3d8
    // 0xccdc54: EnterFrame
    //     0xccdc54: stp             fp, lr, [SP, #-0x10]!
    //     0xccdc58: mov             fp, SP
    // 0xccdc5c: AllocStack(0x48)
    //     0xccdc5c: sub             SP, SP, #0x48
    // 0xccdc60: SetupParameters()
    //     0xccdc60: ldr             x0, [fp, #0x18]
    //     0xccdc64: ldur            w1, [x0, #0x17]
    //     0xccdc68: add             x1, x1, HEAP, lsl #32
    //     0xccdc6c: stur            x1, [fp, #-0x10]
    // 0xccdc70: CheckStackOverflow
    //     0xccdc70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xccdc74: cmp             SP, x16
    //     0xccdc78: b.ls            #0xcce024
    // 0xccdc7c: LoadField: r2 = r0->field_b
    //     0xccdc7c: ldur            w2, [x0, #0xb]
    // 0xccdc80: DecompressPointer r2
    //     0xccdc80: add             x2, x2, HEAP, lsl #32
    // 0xccdc84: stur            x2, [fp, #-8]
    // 0xccdc88: r1 = 3
    //     0xccdc88: movz            x1, #0x3
    // 0xccdc8c: r0 = AllocateContext()
    //     0xccdc8c: bl              #0xec126c  ; AllocateContextStub
    // 0xccdc90: mov             x2, x0
    // 0xccdc94: ldur            x1, [fp, #-0x10]
    // 0xccdc98: stur            x2, [fp, #-0x18]
    // 0xccdc9c: StoreField: r2->field_b = r1
    //     0xccdc9c: stur            w1, [x2, #0xb]
    // 0xccdca0: LoadField: r0 = r1->field_13
    //     0xccdca0: ldur            w0, [x1, #0x13]
    // 0xccdca4: DecompressPointer r0
    //     0xccdca4: add             x0, x0, HEAP, lsl #32
    // 0xccdca8: str             x0, [SP]
    // 0xccdcac: ClosureCall
    //     0xccdcac: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xccdcb0: ldur            x2, [x0, #0x1f]
    //     0xccdcb4: blr             x2
    // 0xccdcb8: mov             x5, x0
    // 0xccdcbc: ldur            x4, [fp, #-0x18]
    // 0xccdcc0: stur            x5, [fp, #-0x20]
    // 0xccdcc4: StoreField: r4->field_f = r0
    //     0xccdcc4: stur            w0, [x4, #0xf]
    //     0xccdcc8: tbz             w0, #0, #0xccdce4
    //     0xccdccc: ldurb           w16, [x4, #-1]
    //     0xccdcd0: ldurb           w17, [x0, #-1]
    //     0xccdcd4: and             x16, x17, x16, lsr #2
    //     0xccdcd8: tst             x16, HEAP, lsr #32
    //     0xccdcdc: b.eq            #0xccdce4
    //     0xccdce0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xccdce4: ldur            x1, [fp, #-8]
    // 0xccdce8: r2 = Null
    //     0xccdce8: mov             x2, NULL
    // 0xccdcec: r3 = <Y1>
    //     0xccdcec: add             x3, PP, #0xe, lsl #12  ; [pp+0xec88] TypeArguments: <Y1>
    //     0xccdcf0: ldr             x3, [x3, #0xc88]
    // 0xccdcf4: r0 = Null
    //     0xccdcf4: mov             x0, NULL
    // 0xccdcf8: cmp             x2, x0
    // 0xccdcfc: b.ne            #0xccdd08
    // 0xccdd00: cmp             x1, x0
    // 0xccdd04: b.eq            #0xccdd14
    // 0xccdd08: r30 = InstantiateTypeArgumentsStub
    //     0xccdd08: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xccdd0c: LoadField: r30 = r30->field_7
    //     0xccdd0c: ldur            lr, [lr, #7]
    // 0xccdd10: blr             lr
    // 0xccdd14: mov             x1, x0
    // 0xccdd18: r0 = _MultiControllerSink()
    //     0xccdd18: bl              #0xcce02c  ; Allocate_MultiControllerSinkStub -> _MultiControllerSink<X0> (size=0x10)
    // 0xccdd1c: mov             x1, x0
    // 0xccdd20: ldr             x0, [fp, #0x10]
    // 0xccdd24: StoreField: r1->field_b = r0
    //     0xccdd24: stur            w0, [x1, #0xb]
    // 0xccdd28: mov             x2, x1
    // 0xccdd2c: ldur            x1, [fp, #-0x20]
    // 0xccdd30: r0 = setSink()
    //     0xccdd30: bl              #0xab026c  ; [package:rxdart/src/utils/forwarding_sink.dart] ForwardingSink::setSink
    // 0xccdd34: ldur            x3, [fp, #-0x18]
    // 0xccdd38: StoreField: r3->field_13 = rNULL
    //     0xccdd38: stur            NULL, [x3, #0x13]
    // 0xccdd3c: r0 = false
    //     0xccdd3c: add             x0, NULL, #0x30  ; false
    // 0xccdd40: ArrayStore: r3[0] = r0  ; List_4
    //     0xccdd40: stur            w0, [x3, #0x17]
    // 0xccdd44: ldur            x4, [fp, #-0x20]
    // 0xccdd48: r5 = LoadClassIdInstr(r4)
    //     0xccdd48: ldur            x5, [x4, #-1]
    //     0xccdd4c: ubfx            x5, x5, #0xc, #0x14
    // 0xccdd50: stur            x5, [fp, #-0x28]
    // 0xccdd54: cmp             x5, #0x206
    // 0xccdd58: b.ne            #0xccdda0
    // 0xccdd5c: LoadField: r1 = r4->field_b
    //     0xccdd5c: ldur            w1, [x4, #0xb]
    // 0xccdd60: DecompressPointer r1
    //     0xccdd60: add             x1, x1, HEAP, lsl #32
    // 0xccdd64: cmp             w1, NULL
    // 0xccdd68: b.eq            #0xccdfdc
    // 0xccdd6c: LoadField: r2 = r4->field_f
    //     0xccdd6c: ldur            w2, [x4, #0xf]
    // 0xccdd70: DecompressPointer r2
    //     0xccdd70: add             x2, x2, HEAP, lsl #32
    // 0xccdd74: LoadField: r0 = r4->field_13
    //     0xccdd74: ldur            w0, [x4, #0x13]
    // 0xccdd78: DecompressPointer r0
    //     0xccdd78: add             x0, x0, HEAP, lsl #32
    // 0xccdd7c: r6 = LoadClassIdInstr(r1)
    //     0xccdd7c: ldur            x6, [x1, #-1]
    //     0xccdd80: ubfx            x6, x6, #0xc, #0x14
    // 0xccdd84: str             x0, [SP]
    // 0xccdd88: mov             x0, x6
    // 0xccdd8c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xccdd8c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xccdd90: r0 = GDT[cid_x0 + 0x9b0]()
    //     0xccdd90: add             lr, x0, #0x9b0
    //     0xccdd94: ldr             lr, [x21, lr, lsl #3]
    //     0xccdd98: blr             lr
    // 0xccdd9c: b               #0xccdddc
    // 0xccdda0: mov             x3, x5
    // 0xccdda4: cmp             x3, #0x207
    // 0xccdda8: b.ne            #0xccdddc
    // 0xccddac: ldur            x4, [fp, #-0x20]
    // 0xccddb0: LoadField: r1 = r4->field_b
    //     0xccddb0: ldur            w1, [x4, #0xb]
    // 0xccddb4: DecompressPointer r1
    //     0xccddb4: add             x1, x1, HEAP, lsl #32
    // 0xccddb8: cmp             w1, NULL
    // 0xccddbc: b.eq            #0xccdffc
    // 0xccddc0: LoadField: r2 = r4->field_f
    //     0xccddc0: ldur            w2, [x4, #0xf]
    // 0xccddc4: DecompressPointer r2
    //     0xccddc4: add             x2, x2, HEAP, lsl #32
    // 0xccddc8: r0 = LoadClassIdInstr(r1)
    //     0xccddc8: ldur            x0, [x1, #-1]
    //     0xccddcc: ubfx            x0, x0, #0xc, #0x14
    // 0xccddd0: r0 = GDT[cid_x0 + 0xdbe]()
    //     0xccddd0: add             lr, x0, #0xdbe
    //     0xccddd4: ldr             lr, [x21, lr, lsl #3]
    //     0xccddd8: blr             lr
    // 0xccdddc: ldur            x0, [fp, #-0x18]
    // 0xccdde0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xccdde0: ldur            w1, [x0, #0x17]
    // 0xccdde4: DecompressPointer r1
    //     0xccdde4: add             x1, x1, HEAP, lsl #32
    // 0xccdde8: tbnz            w1, #4, #0xccddf4
    // 0xccddec: mov             x2, x0
    // 0xccddf0: b               #0xccdf88
    // 0xccddf4: ldur            x1, [fp, #-0x10]
    // 0xccddf8: ldur            x3, [fp, #-0x28]
    // 0xccddfc: LoadField: r4 = r1->field_f
    //     0xccddfc: ldur            w4, [x1, #0xf]
    // 0xccde00: DecompressPointer r4
    //     0xccde00: add             x4, x4, HEAP, lsl #32
    // 0xccde04: stur            x4, [fp, #-0x30]
    // 0xccde08: cmp             x3, #0x206
    // 0xccde0c: b.ne            #0xccde28
    // 0xccde10: ldur            x2, [fp, #-0x20]
    // 0xccde14: r1 = Function 'onData':.
    //     0xccde14: add             x1, PP, #0xe, lsl #12  ; [pp+0xecd8] AnonymousClosure: (0xab2048), in [package:rxdart/src/transformers/start_with_error.dart] _StartWithErrorStreamSink::onData (0xab2084)
    //     0xccde18: ldr             x1, [x1, #0xcd8]
    // 0xccde1c: r0 = AllocateClosure()
    //     0xccde1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xccde20: mov             x4, x0
    // 0xccde24: b               #0xccde60
    // 0xccde28: mov             x0, x3
    // 0xccde2c: cmp             x0, #0x207
    // 0xccde30: b.ne            #0xccde4c
    // 0xccde34: ldur            x2, [fp, #-0x20]
    // 0xccde38: r1 = Function 'onData':.
    //     0xccde38: add             x1, PP, #0xe, lsl #12  ; [pp+0xece0] AnonymousClosure: (0xab1f48), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onData (0xab1f84)
    //     0xccde3c: ldr             x1, [x1, #0xce0]
    // 0xccde40: r0 = AllocateClosure()
    //     0xccde40: bl              #0xec1630  ; AllocateClosureStub
    // 0xccde44: mov             x4, x0
    // 0xccde48: b               #0xccde60
    // 0xccde4c: ldur            x2, [fp, #-0x20]
    // 0xccde50: r1 = Function 'onData':.
    //     0xccde50: add             x1, PP, #0xe, lsl #12  ; [pp+0xece8] AnonymousClosure: (0xab1558), in [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::onData (0xab1594)
    //     0xccde54: ldr             x1, [x1, #0xce8]
    // 0xccde58: r0 = AllocateClosure()
    //     0xccde58: bl              #0xec1630  ; AllocateClosureStub
    // 0xccde5c: mov             x4, x0
    // 0xccde60: ldur            x3, [fp, #-0x28]
    // 0xccde64: mov             x0, x4
    // 0xccde68: ldur            x1, [fp, #-8]
    // 0xccde6c: stur            x4, [fp, #-0x10]
    // 0xccde70: r2 = Null
    //     0xccde70: mov             x2, NULL
    // 0xccde74: r8 = (dynamic this, Y0) => void?
    //     0xccde74: add             x8, PP, #0xe, lsl #12  ; [pp+0xecf0] FunctionType: (dynamic this, Y0) => void?
    //     0xccde78: ldr             x8, [x8, #0xcf0]
    // 0xccde7c: LoadField: r9 = r8->field_7
    //     0xccde7c: ldur            x9, [x8, #7]
    // 0xccde80: r3 = Null
    //     0xccde80: add             x3, PP, #0xe, lsl #12  ; [pp+0xeea0] Null
    //     0xccde84: ldr             x3, [x3, #0xea0]
    // 0xccde88: blr             x9
    // 0xccde8c: ldur            x0, [fp, #-0x28]
    // 0xccde90: cmp             x0, #0x206
    // 0xccde94: b.ne            #0xccdeb0
    // 0xccde98: ldur            x2, [fp, #-0x20]
    // 0xccde9c: r1 = Function 'onError':.
    //     0xccde9c: add             x1, PP, #0xe, lsl #12  ; [pp+0xed08] AnonymousClosure: (0xab1518), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onError (0xab1450)
    //     0xccdea0: ldr             x1, [x1, #0xd08]
    // 0xccdea4: r0 = AllocateClosure()
    //     0xccdea4: bl              #0xec1630  ; AllocateClosureStub
    // 0xccdea8: mov             x3, x0
    // 0xccdeac: b               #0xccdee4
    // 0xccdeb0: cmp             x0, #0x207
    // 0xccdeb4: b.ne            #0xccded0
    // 0xccdeb8: ldur            x2, [fp, #-0x20]
    // 0xccdebc: r1 = Function 'onError':.
    //     0xccdebc: add             x1, PP, #0xe, lsl #12  ; [pp+0xed10] AnonymousClosure: (0xab14d8), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onError (0xab1450)
    //     0xccdec0: ldr             x1, [x1, #0xd10]
    // 0xccdec4: r0 = AllocateClosure()
    //     0xccdec4: bl              #0xec1630  ; AllocateClosureStub
    // 0xccdec8: mov             x3, x0
    // 0xccdecc: b               #0xccdee4
    // 0xccded0: ldur            x2, [fp, #-0x20]
    // 0xccded4: r1 = Function 'onError':.
    //     0xccded4: add             x1, PP, #0xe, lsl #12  ; [pp+0xed18] AnonymousClosure: (0xab1410), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onError (0xab1450)
    //     0xccded8: ldr             x1, [x1, #0xd18]
    // 0xccdedc: r0 = AllocateClosure()
    //     0xccdedc: bl              #0xec1630  ; AllocateClosureStub
    // 0xccdee0: mov             x3, x0
    // 0xccdee4: ldur            x0, [fp, #-0x28]
    // 0xccdee8: stur            x3, [fp, #-0x38]
    // 0xccdeec: cmp             x0, #0x206
    // 0xccdef0: b.ne            #0xccdf08
    // 0xccdef4: ldur            x2, [fp, #-0x20]
    // 0xccdef8: r1 = Function 'onDone':.
    //     0xccdef8: add             x1, PP, #0xe, lsl #12  ; [pp+0xed20] AnonymousClosure: (0xab13d8), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onDone (0xab1360)
    //     0xccdefc: ldr             x1, [x1, #0xd20]
    // 0xccdf00: r0 = AllocateClosure()
    //     0xccdf00: bl              #0xec1630  ; AllocateClosureStub
    // 0xccdf04: b               #0xccdf34
    // 0xccdf08: cmp             x0, #0x207
    // 0xccdf0c: b.ne            #0xccdf24
    // 0xccdf10: ldur            x2, [fp, #-0x20]
    // 0xccdf14: r1 = Function 'onDone':.
    //     0xccdf14: add             x1, PP, #0xe, lsl #12  ; [pp+0xed28] AnonymousClosure: (0xab1328), in [package:rxdart/src/transformers/start_with.dart] _StartWithStreamSink::onDone (0xab1360)
    //     0xccdf18: ldr             x1, [x1, #0xd28]
    // 0xccdf1c: r0 = AllocateClosure()
    //     0xccdf1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xccdf20: b               #0xccdf34
    // 0xccdf24: ldur            x2, [fp, #-0x20]
    // 0xccdf28: r1 = Function 'onDone':.
    //     0xccdf28: add             x1, PP, #0xe, lsl #12  ; [pp+0xed30] AnonymousClosure: (0xab0a38), in [package:rxdart/src/transformers/backpressure/backpressure.dart] _BackpressureStreamSink::onDone (0xab0a70)
    //     0xccdf2c: ldr             x1, [x1, #0xd30]
    // 0xccdf30: r0 = AllocateClosure()
    //     0xccdf30: bl              #0xec1630  ; AllocateClosureStub
    // 0xccdf34: ldur            x3, [fp, #-0x18]
    // 0xccdf38: ldur            x1, [fp, #-0x30]
    // 0xccdf3c: r2 = LoadClassIdInstr(r1)
    //     0xccdf3c: ldur            x2, [x1, #-1]
    //     0xccdf40: ubfx            x2, x2, #0xc, #0x14
    // 0xccdf44: ldur            x16, [fp, #-0x38]
    // 0xccdf48: stp             x0, x16, [SP]
    // 0xccdf4c: mov             x0, x2
    // 0xccdf50: ldur            x2, [fp, #-0x10]
    // 0xccdf54: r4 = const [0, 0x4, 0x2, 0x2, onDone, 0x3, onError, 0x2, null]
    //     0xccdf54: add             x4, PP, #0xc, lsl #12  ; [pp+0xc168] List(9) [0, 0x4, 0x2, 0x2, "onDone", 0x3, "onError", 0x2, Null]
    //     0xccdf58: ldr             x4, [x4, #0x168]
    // 0xccdf5c: r0 = GDT[cid_x0 + 0x64e]()
    //     0xccdf5c: add             lr, x0, #0x64e
    //     0xccdf60: ldr             lr, [x21, lr, lsl #3]
    //     0xccdf64: blr             lr
    // 0xccdf68: ldur            x2, [fp, #-0x18]
    // 0xccdf6c: StoreField: r2->field_13 = r0
    //     0xccdf6c: stur            w0, [x2, #0x13]
    //     0xccdf70: ldurb           w16, [x2, #-1]
    //     0xccdf74: ldurb           w17, [x0, #-1]
    //     0xccdf78: and             x16, x17, x16, lsr #2
    //     0xccdf7c: tst             x16, HEAP, lsr #32
    //     0xccdf80: b.eq            #0xccdf88
    //     0xccdf84: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xccdf88: ldr             x0, [fp, #0x10]
    // 0xccdf8c: ldur            x3, [fp, #-8]
    // 0xccdf90: r1 = Function '<anonymous closure>': static.
    //     0xccdf90: add             x1, PP, #0xe, lsl #12  ; [pp+0xeeb0] AnonymousClosure: static (0xcce038), in [package:rxdart/src/utils/forwarding_stream.dart] ::_forwardMulti (0xccdb70)
    //     0xccdf94: ldr             x1, [x1, #0xeb0]
    // 0xccdf98: r0 = AllocateClosure()
    //     0xccdf98: bl              #0xec1630  ; AllocateClosureStub
    // 0xccdf9c: mov             x1, x0
    // 0xccdfa0: ldur            x0, [fp, #-8]
    // 0xccdfa4: StoreField: r1->field_b = r0
    //     0xccdfa4: stur            w0, [x1, #0xb]
    // 0xccdfa8: mov             x0, x1
    // 0xccdfac: ldr             x1, [fp, #0x10]
    // 0xccdfb0: StoreField: r1->field_27 = r0
    //     0xccdfb0: stur            w0, [x1, #0x27]
    //     0xccdfb4: ldurb           w16, [x1, #-1]
    //     0xccdfb8: ldurb           w17, [x0, #-1]
    //     0xccdfbc: and             x16, x17, x16, lsr #2
    //     0xccdfc0: tst             x16, HEAP, lsr #32
    //     0xccdfc4: b.eq            #0xccdfcc
    //     0xccdfc8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xccdfcc: r0 = Null
    //     0xccdfcc: mov             x0, NULL
    // 0xccdfd0: LeaveFrame
    //     0xccdfd0: mov             SP, fp
    //     0xccdfd4: ldp             fp, lr, [SP], #0x10
    // 0xccdfd8: ret
    //     0xccdfd8: ret             
    // 0xccdfdc: r0 = StateError()
    //     0xccdfdc: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xccdfe0: mov             x1, x0
    // 0xccdfe4: r0 = "Must call setSink(sink) before accessing!"
    //     0xccdfe4: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xccdfe8: ldr             x0, [x0, #0xcd0]
    // 0xccdfec: StoreField: r1->field_b = r0
    //     0xccdfec: stur            w0, [x1, #0xb]
    // 0xccdff0: mov             x0, x1
    // 0xccdff4: r0 = Throw()
    //     0xccdff4: bl              #0xec04b8  ; ThrowStub
    // 0xccdff8: brk             #0
    // 0xccdffc: r0 = "Must call setSink(sink) before accessing!"
    //     0xccdffc: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xcce000: ldr             x0, [x0, #0xcd0]
    // 0xcce004: r0 = StateError()
    //     0xcce004: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xcce008: mov             x1, x0
    // 0xcce00c: r0 = "Must call setSink(sink) before accessing!"
    //     0xcce00c: add             x0, PP, #0xe, lsl #12  ; [pp+0xecd0] "Must call setSink(sink) before accessing!"
    //     0xcce010: ldr             x0, [x0, #0xcd0]
    // 0xcce014: StoreField: r1->field_b = r0
    //     0xcce014: stur            w0, [x1, #0xb]
    // 0xcce018: mov             x0, x1
    // 0xcce01c: r0 = Throw()
    //     0xcce01c: bl              #0xec04b8  ; ThrowStub
    // 0xcce020: brk             #0
    // 0xcce024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcce024: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcce028: b               #0xccdc7c
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xcce038, size: 0xf8
    // 0xcce038: EnterFrame
    //     0xcce038: stp             fp, lr, [SP, #-0x10]!
    //     0xcce03c: mov             fp, SP
    // 0xcce040: AllocStack(0x10)
    //     0xcce040: sub             SP, SP, #0x10
    // 0xcce044: SetupParameters()
    //     0xcce044: add             x0, NULL, #0x20  ; true
    //     0xcce048: ldr             x1, [fp, #0x10]
    //     0xcce04c: ldur            w2, [x1, #0x17]
    //     0xcce050: add             x2, x2, HEAP, lsl #32
    //     0xcce054: stur            x2, [fp, #-8]
    // 0xcce044: r0 = true
    // 0xcce058: CheckStackOverflow
    //     0xcce058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcce05c: cmp             SP, x16
    //     0xcce060: b.ls            #0xcce128
    // 0xcce064: ArrayStore: r2[0] = r0  ; List_4
    //     0xcce064: stur            w0, [x2, #0x17]
    // 0xcce068: LoadField: r1 = r2->field_13
    //     0xcce068: ldur            w1, [x2, #0x13]
    // 0xcce06c: DecompressPointer r1
    //     0xcce06c: add             x1, x1, HEAP, lsl #32
    // 0xcce070: cmp             w1, NULL
    // 0xcce074: b.ne            #0xcce084
    // 0xcce078: mov             x0, x2
    // 0xcce07c: r2 = Null
    //     0xcce07c: mov             x2, NULL
    // 0xcce080: b               #0xcce0a0
    // 0xcce084: r0 = LoadClassIdInstr(r1)
    //     0xcce084: ldur            x0, [x1, #-1]
    //     0xcce088: ubfx            x0, x0, #0xc, #0x14
    // 0xcce08c: r0 = GDT[cid_x0 + -0x37]()
    //     0xcce08c: sub             lr, x0, #0x37
    //     0xcce090: ldr             lr, [x21, lr, lsl #3]
    //     0xcce094: blr             lr
    // 0xcce098: mov             x2, x0
    // 0xcce09c: ldur            x0, [fp, #-8]
    // 0xcce0a0: stur            x2, [fp, #-0x10]
    // 0xcce0a4: StoreField: r0->field_13 = rNULL
    //     0xcce0a4: stur            NULL, [x0, #0x13]
    // 0xcce0a8: LoadField: r1 = r0->field_f
    //     0xcce0a8: ldur            w1, [x0, #0xf]
    // 0xcce0ac: DecompressPointer r1
    //     0xcce0ac: add             x1, x1, HEAP, lsl #32
    // 0xcce0b0: r0 = LoadClassIdInstr(r1)
    //     0xcce0b0: ldur            x0, [x1, #-1]
    //     0xcce0b4: ubfx            x0, x0, #0xc, #0x14
    // 0xcce0b8: cmp             x0, #0x206
    // 0xcce0bc: b.ne            #0xcce0c8
    // 0xcce0c0: r2 = Null
    //     0xcce0c0: mov             x2, NULL
    // 0xcce0c4: b               #0xcce114
    // 0xcce0c8: cmp             x0, #0x207
    // 0xcce0cc: b.ne            #0xcce0d8
    // 0xcce0d0: r2 = Null
    //     0xcce0d0: mov             x2, NULL
    // 0xcce0d4: b               #0xcce114
    // 0xcce0d8: LoadField: r0 = r1->field_4b
    //     0xcce0d8: ldur            w0, [x1, #0x4b]
    // 0xcce0dc: DecompressPointer r0
    //     0xcce0dc: add             x0, x0, HEAP, lsl #32
    // 0xcce0e0: cmp             w0, NULL
    // 0xcce0e4: b.ne            #0xcce0f0
    // 0xcce0e8: r0 = Null
    //     0xcce0e8: mov             x0, NULL
    // 0xcce0ec: b               #0xcce110
    // 0xcce0f0: r1 = LoadClassIdInstr(r0)
    //     0xcce0f0: ldur            x1, [x0, #-1]
    //     0xcce0f4: ubfx            x1, x1, #0xc, #0x14
    // 0xcce0f8: mov             x16, x0
    // 0xcce0fc: mov             x0, x1
    // 0xcce100: mov             x1, x16
    // 0xcce104: r0 = GDT[cid_x0 + -0x37]()
    //     0xcce104: sub             lr, x0, #0x37
    //     0xcce108: ldr             lr, [x21, lr, lsl #3]
    //     0xcce10c: blr             lr
    // 0xcce110: mov             x2, x0
    // 0xcce114: ldur            x1, [fp, #-0x10]
    // 0xcce118: r0 = waitTwoFutures()
    //     0xcce118: bl              #0xaafe14  ; [package:rxdart/src/utils/future.dart] ::waitTwoFutures
    // 0xcce11c: LeaveFrame
    //     0xcce11c: mov             SP, fp
    //     0xcce120: ldp             fp, lr, [SP], #0x10
    // 0xcce124: ret
    //     0xcce124: ret             
    // 0xcce128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcce128: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcce12c: b               #0xcce064
  }
}

// class id: 514, size: 0x10, field offset: 0x8
class _MultiControllerSink<X0> extends Object
    implements EventSink<X0> {

  dynamic add(dynamic) {
    // ** addr: 0x74f5b0, size: 0x24
    // 0x74f5b0: EnterFrame
    //     0x74f5b0: stp             fp, lr, [SP, #-0x10]!
    //     0x74f5b4: mov             fp, SP
    // 0x74f5b8: ldr             x2, [fp, #0x10]
    // 0x74f5bc: r1 = Function 'add':.
    //     0x74f5bc: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c890] AnonymousClosure: (0x74f5d4), in [package:rxdart/src/utils/forwarding_stream.dart] _MultiControllerSink::add (0xd492f0)
    //     0x74f5c0: ldr             x1, [x1, #0x890]
    // 0x74f5c4: r0 = AllocateClosure()
    //     0x74f5c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x74f5c8: LeaveFrame
    //     0x74f5c8: mov             SP, fp
    //     0x74f5cc: ldp             fp, lr, [SP], #0x10
    // 0x74f5d0: ret
    //     0x74f5d0: ret             
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x74f5d4, size: 0x3c
    // 0x74f5d4: EnterFrame
    //     0x74f5d4: stp             fp, lr, [SP, #-0x10]!
    //     0x74f5d8: mov             fp, SP
    // 0x74f5dc: ldr             x0, [fp, #0x18]
    // 0x74f5e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74f5e0: ldur            w1, [x0, #0x17]
    // 0x74f5e4: DecompressPointer r1
    //     0x74f5e4: add             x1, x1, HEAP, lsl #32
    // 0x74f5e8: CheckStackOverflow
    //     0x74f5e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74f5ec: cmp             SP, x16
    //     0x74f5f0: b.ls            #0x74f608
    // 0x74f5f4: ldr             x2, [fp, #0x10]
    // 0x74f5f8: r0 = add()
    //     0x74f5f8: bl              #0xd492f0  ; [package:rxdart/src/utils/forwarding_stream.dart] _MultiControllerSink::add
    // 0x74f5fc: LeaveFrame
    //     0x74f5fc: mov             SP, fp
    //     0x74f600: ldp             fp, lr, [SP], #0x10
    // 0x74f604: ret
    //     0x74f604: ret             
    // 0x74f608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74f608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74f60c: b               #0x74f5f4
  }
  _ close(/* No info */) {
    // ** addr: 0xd45748, size: 0x3c
    // 0xd45748: EnterFrame
    //     0xd45748: stp             fp, lr, [SP, #-0x10]!
    //     0xd4574c: mov             fp, SP
    // 0xd45750: CheckStackOverflow
    //     0xd45750: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd45754: cmp             SP, x16
    //     0xd45758: b.ls            #0xd4577c
    // 0xd4575c: LoadField: r0 = r1->field_b
    //     0xd4575c: ldur            w0, [x1, #0xb]
    // 0xd45760: DecompressPointer r0
    //     0xd45760: add             x0, x0, HEAP, lsl #32
    // 0xd45764: mov             x1, x0
    // 0xd45768: r0 = closeSync()
    //     0xd45768: bl              #0x7270ac  ; [dart:async] _MultiStreamController::closeSync
    // 0xd4576c: r0 = Null
    //     0xd4576c: mov             x0, NULL
    // 0xd45770: LeaveFrame
    //     0xd45770: mov             SP, fp
    //     0xd45774: ldp             fp, lr, [SP], #0x10
    // 0xd45778: ret
    //     0xd45778: ret             
    // 0xd4577c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd4577c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd45780: b               #0xd4575c
  }
  _ add(/* No info */) {
    // ** addr: 0xd492f0, size: 0x88
    // 0xd492f0: EnterFrame
    //     0xd492f0: stp             fp, lr, [SP, #-0x10]!
    //     0xd492f4: mov             fp, SP
    // 0xd492f8: AllocStack(0x10)
    //     0xd492f8: sub             SP, SP, #0x10
    // 0xd492fc: SetupParameters(_MultiControllerSink<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xd492fc: mov             x4, x1
    //     0xd49300: mov             x3, x2
    //     0xd49304: stur            x1, [fp, #-8]
    //     0xd49308: stur            x2, [fp, #-0x10]
    // 0xd4930c: CheckStackOverflow
    //     0xd4930c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd49310: cmp             SP, x16
    //     0xd49314: b.ls            #0xd49370
    // 0xd49318: LoadField: r2 = r4->field_7
    //     0xd49318: ldur            w2, [x4, #7]
    // 0xd4931c: DecompressPointer r2
    //     0xd4931c: add             x2, x2, HEAP, lsl #32
    // 0xd49320: mov             x0, x3
    // 0xd49324: r1 = Null
    //     0xd49324: mov             x1, NULL
    // 0xd49328: cmp             w2, NULL
    // 0xd4932c: b.eq            #0xd4934c
    // 0xd49330: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd49330: ldur            w4, [x2, #0x17]
    // 0xd49334: DecompressPointer r4
    //     0xd49334: add             x4, x4, HEAP, lsl #32
    // 0xd49338: r8 = X0
    //     0xd49338: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xd4933c: LoadField: r9 = r4->field_7
    //     0xd4933c: ldur            x9, [x4, #7]
    // 0xd49340: r3 = Null
    //     0xd49340: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c898] Null
    //     0xd49344: ldr             x3, [x3, #0x898]
    // 0xd49348: blr             x9
    // 0xd4934c: ldur            x0, [fp, #-8]
    // 0xd49350: LoadField: r1 = r0->field_b
    //     0xd49350: ldur            w1, [x0, #0xb]
    // 0xd49354: DecompressPointer r1
    //     0xd49354: add             x1, x1, HEAP, lsl #32
    // 0xd49358: ldur            x2, [fp, #-0x10]
    // 0xd4935c: r0 = addSync()
    //     0xd4935c: bl              #0x72711c  ; [dart:async] _MultiStreamController::addSync
    // 0xd49360: r0 = Null
    //     0xd49360: mov             x0, NULL
    // 0xd49364: LeaveFrame
    //     0xd49364: mov             SP, fp
    //     0xd49368: ldp             fp, lr, [SP], #0x10
    // 0xd4936c: ret
    //     0xd4936c: ret             
    // 0xd49370: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd49370: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd49374: b               #0xd49318
  }
  dynamic addError(dynamic) {
    // ** addr: 0xd6343c, size: 0x24
    // 0xd6343c: EnterFrame
    //     0xd6343c: stp             fp, lr, [SP, #-0x10]!
    //     0xd63440: mov             fp, SP
    // 0xd63444: ldr             x2, [fp, #0x10]
    // 0xd63448: r1 = Function 'addError':.
    //     0xd63448: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c888] AnonymousClosure: (0xd63460), in [package:rxdart/src/utils/forwarding_stream.dart] _MultiControllerSink::addError (0xd639f0)
    //     0xd6344c: ldr             x1, [x1, #0x888]
    // 0xd63450: r0 = AllocateClosure()
    //     0xd63450: bl              #0xec1630  ; AllocateClosureStub
    // 0xd63454: LeaveFrame
    //     0xd63454: mov             SP, fp
    //     0xd63458: ldp             fp, lr, [SP], #0x10
    // 0xd6345c: ret
    //     0xd6345c: ret             
  }
  [closure] void addError(dynamic, Object, [StackTrace?]) {
    // ** addr: 0xd63460, size: 0x78
    // 0xd63460: EnterFrame
    //     0xd63460: stp             fp, lr, [SP, #-0x10]!
    //     0xd63464: mov             fp, SP
    // 0xd63468: AllocStack(0x8)
    //     0xd63468: sub             SP, SP, #8
    // 0xd6346c: SetupParameters(_MultiControllerSink<X0> this /* r0 */, dynamic _ /* r2 */, [dynamic _ = Null /* r1 */])
    //     0xd6346c: ldur            w0, [x4, #0x13]
    //     0xd63470: sub             x1, x0, #4
    //     0xd63474: add             x0, fp, w1, sxtw #2
    //     0xd63478: ldr             x0, [x0, #0x18]
    //     0xd6347c: add             x2, fp, w1, sxtw #2
    //     0xd63480: ldr             x2, [x2, #0x10]
    //     0xd63484: cmp             w1, #2
    //     0xd63488: b.lt            #0xd6349c
    //     0xd6348c: add             x3, fp, w1, sxtw #2
    //     0xd63490: ldr             x3, [x3, #8]
    //     0xd63494: mov             x1, x3
    //     0xd63498: b               #0xd634a0
    //     0xd6349c: mov             x1, NULL
    //     0xd634a0: ldur            w3, [x0, #0x17]
    //     0xd634a4: add             x3, x3, HEAP, lsl #32
    // 0xd634a8: CheckStackOverflow
    //     0xd634a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd634ac: cmp             SP, x16
    //     0xd634b0: b.ls            #0xd634d0
    // 0xd634b4: str             x1, [SP]
    // 0xd634b8: mov             x1, x3
    // 0xd634bc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xd634bc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xd634c0: r0 = addError()
    //     0xd634c0: bl              #0xd639f0  ; [package:rxdart/src/utils/forwarding_stream.dart] _MultiControllerSink::addError
    // 0xd634c4: LeaveFrame
    //     0xd634c4: mov             SP, fp
    //     0xd634c8: ldp             fp, lr, [SP], #0x10
    // 0xd634cc: ret
    //     0xd634cc: ret             
    // 0xd634d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd634d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd634d4: b               #0xd634b4
  }
  _ addError(/* No info */) {
    // ** addr: 0xd639f0, size: 0x60
    // 0xd639f0: EnterFrame
    //     0xd639f0: stp             fp, lr, [SP, #-0x10]!
    //     0xd639f4: mov             fp, SP
    // 0xd639f8: LoadField: r0 = r4->field_13
    //     0xd639f8: ldur            w0, [x4, #0x13]
    // 0xd639fc: sub             x3, x0, #4
    // 0xd63a00: cmp             w3, #2
    // 0xd63a04: b.lt            #0xd63a18
    // 0xd63a08: add             x0, fp, w3, sxtw #2
    // 0xd63a0c: ldr             x0, [x0, #8]
    // 0xd63a10: mov             x3, x0
    // 0xd63a14: b               #0xd63a1c
    // 0xd63a18: r3 = Null
    //     0xd63a18: mov             x3, NULL
    // 0xd63a1c: CheckStackOverflow
    //     0xd63a1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd63a20: cmp             SP, x16
    //     0xd63a24: b.ls            #0xd63a48
    // 0xd63a28: LoadField: r0 = r1->field_b
    //     0xd63a28: ldur            w0, [x1, #0xb]
    // 0xd63a2c: DecompressPointer r0
    //     0xd63a2c: add             x0, x0, HEAP, lsl #32
    // 0xd63a30: mov             x1, x0
    // 0xd63a34: r0 = addErrorSync()
    //     0xd63a34: bl              #0x727030  ; [dart:async] _MultiStreamController::addErrorSync
    // 0xd63a38: r0 = Null
    //     0xd63a38: mov             x0, NULL
    // 0xd63a3c: LeaveFrame
    //     0xd63a3c: mov             SP, fp
    //     0xd63a40: ldp             fp, lr, [SP], #0x10
    // 0xd63a44: ret
    //     0xd63a44: ret             
    // 0xd63a48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd63a48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd63a4c: b               #0xd63a28
  }
}
