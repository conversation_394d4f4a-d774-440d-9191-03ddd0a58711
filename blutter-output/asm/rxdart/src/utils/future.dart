// lib: , url: package:rxdart/src/utils/future.dart

// class id: 1051103, size: 0x8
class :: {

  static _ waitFuturesList(/* No info */) {
    // ** addr: 0x8b7380, size: 0xbc
    // 0x8b7380: EnterFrame
    //     0x8b7380: stp             fp, lr, [SP, #-0x10]!
    //     0x8b7384: mov             fp, SP
    // 0x8b7388: AllocStack(0x18)
    //     0x8b7388: sub             SP, SP, #0x18
    // 0x8b738c: SetupParameters(dynamic _ /* r1 => r2 */)
    //     0x8b738c: mov             x2, x1
    // 0x8b7390: CheckStackOverflow
    //     0x8b7390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7394: cmp             SP, x16
    //     0x8b7398: b.ls            #0x8b7430
    // 0x8b739c: LoadField: r0 = r2->field_b
    //     0x8b739c: ldur            w0, [x2, #0xb]
    // 0x8b73a0: r1 = LoadInt32Instr(r0)
    //     0x8b73a0: sbfx            x1, x0, #1, #0x1f
    // 0x8b73a4: cmp             x1, #0
    // 0x8b73a8: b.gt            #0x8b73c0
    // 0x8b73ac: cbnz            w0, #0x8b73f8
    // 0x8b73b0: r0 = Null
    //     0x8b73b0: mov             x0, NULL
    // 0x8b73b4: LeaveFrame
    //     0x8b73b4: mov             SP, fp
    //     0x8b73b8: ldp             fp, lr, [SP], #0x10
    // 0x8b73bc: ret
    //     0x8b73bc: ret             
    // 0x8b73c0: cmp             w0, #2
    // 0x8b73c4: b.ne            #0x8b73f8
    // 0x8b73c8: mov             x0, x1
    // 0x8b73cc: r1 = 0
    //     0x8b73cc: movz            x1, #0
    // 0x8b73d0: cmp             x1, x0
    // 0x8b73d4: b.hs            #0x8b7438
    // 0x8b73d8: LoadField: r0 = r2->field_f
    //     0x8b73d8: ldur            w0, [x2, #0xf]
    // 0x8b73dc: DecompressPointer r0
    //     0x8b73dc: add             x0, x0, HEAP, lsl #32
    // 0x8b73e0: LoadField: r1 = r0->field_f
    //     0x8b73e0: ldur            w1, [x0, #0xf]
    // 0x8b73e4: DecompressPointer r1
    //     0x8b73e4: add             x1, x1, HEAP, lsl #32
    // 0x8b73e8: mov             x0, x1
    // 0x8b73ec: LeaveFrame
    //     0x8b73ec: mov             SP, fp
    //     0x8b73f0: ldp             fp, lr, [SP], #0x10
    // 0x8b73f4: ret
    //     0x8b73f4: ret             
    // 0x8b73f8: r16 = <void?>
    //     0x8b73f8: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8b73fc: stp             x2, x16, [SP]
    // 0x8b7400: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8b7400: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8b7404: r0 = wait()
    //     0x8b7404: bl              #0x678258  ; [dart:async] Future::wait
    // 0x8b7408: r16 = <void?>
    //     0x8b7408: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8b740c: stp             x0, x16, [SP, #8]
    // 0x8b7410: r16 = Closure: (Object?) => void from Function '_ignore@804008561': static.
    //     0x8b7410: add             x16, PP, #0xe, lsl #12  ; [pp+0xea90] Closure: (Object?) => void from Function '_ignore@804008561': static. (0x7e54fb8b8ce0)
    //     0x8b7414: ldr             x16, [x16, #0xa90]
    // 0x8b7418: str             x16, [SP]
    // 0x8b741c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8b741c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8b7420: r0 = then()
    //     0x8b7420: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8b7424: LeaveFrame
    //     0x8b7424: mov             SP, fp
    //     0x8b7428: ldp             fp, lr, [SP], #0x10
    // 0x8b742c: ret
    //     0x8b742c: ret             
    // 0x8b7430: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7430: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7434: b               #0x8b739c
    // 0x8b7438: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b7438: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ waitTwoFutures(/* No info */) {
    // ** addr: 0xaafe14, size: 0x17c
    // 0xaafe14: EnterFrame
    //     0xaafe14: stp             fp, lr, [SP, #-0x10]!
    //     0xaafe18: mov             fp, SP
    // 0xaafe1c: AllocStack(0x30)
    //     0xaafe1c: sub             SP, SP, #0x30
    // 0xaafe20: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xaafe20: mov             x4, x1
    //     0xaafe24: mov             x3, x2
    //     0xaafe28: stur            x1, [fp, #-8]
    //     0xaafe2c: stur            x2, [fp, #-0x10]
    // 0xaafe30: CheckStackOverflow
    //     0xaafe30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaafe34: cmp             SP, x16
    //     0xaafe38: b.ls            #0xaaff88
    // 0xaafe3c: cmp             w4, NULL
    // 0xaafe40: b.ne            #0xaafe4c
    // 0xaafe44: mov             x0, x3
    // 0xaafe48: b               #0xaaff7c
    // 0xaafe4c: mov             x0, x3
    // 0xaafe50: r2 = Null
    //     0xaafe50: mov             x2, NULL
    // 0xaafe54: r1 = Null
    //     0xaafe54: mov             x1, NULL
    // 0xaafe58: cmp             w0, NULL
    // 0xaafe5c: b.eq            #0xaafee0
    // 0xaafe60: branchIfSmi(r0, 0xaafee0)
    //     0xaafe60: tbz             w0, #0, #0xaafee0
    // 0xaafe64: r4 = LoadClassIdInstr(r0)
    //     0xaafe64: ldur            x4, [x0, #-1]
    //     0xaafe68: ubfx            x4, x4, #0xc, #0x14
    // 0xaafe6c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xaafe70: ldr             x3, [x3, #0x18]
    // 0xaafe74: ldr             x3, [x3, x4, lsl #3]
    // 0xaafe78: LoadField: r3 = r3->field_2b
    //     0xaafe78: ldur            w3, [x3, #0x2b]
    // 0xaafe7c: DecompressPointer r3
    //     0xaafe7c: add             x3, x3, HEAP, lsl #32
    // 0xaafe80: cmp             w3, NULL
    // 0xaafe84: b.eq            #0xaafee0
    // 0xaafe88: LoadField: r3 = r3->field_f
    //     0xaafe88: ldur            w3, [x3, #0xf]
    // 0xaafe8c: lsr             x3, x3, #3
    // 0xaafe90: r17 = 6686
    //     0xaafe90: movz            x17, #0x1a1e
    // 0xaafe94: cmp             x3, x17
    // 0xaafe98: b.eq            #0xaafee8
    // 0xaafe9c: r3 = SubtypeTestCache
    //     0xaafe9c: add             x3, PP, #0xe, lsl #12  ; [pp+0xecb0] SubtypeTestCache
    //     0xaafea0: ldr             x3, [x3, #0xcb0]
    // 0xaafea4: r30 = Subtype1TestCacheStub
    //     0xaafea4: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xaafea8: LoadField: r30 = r30->field_7
    //     0xaafea8: ldur            lr, [lr, #7]
    // 0xaafeac: blr             lr
    // 0xaafeb0: cmp             w7, NULL
    // 0xaafeb4: b.eq            #0xaafec0
    // 0xaafeb8: tbnz            w7, #4, #0xaafee0
    // 0xaafebc: b               #0xaafee8
    // 0xaafec0: r8 = Future<void?>
    //     0xaafec0: add             x8, PP, #0xe, lsl #12  ; [pp+0xecb8] Type: Future<void?>
    //     0xaafec4: ldr             x8, [x8, #0xcb8]
    // 0xaafec8: r3 = SubtypeTestCache
    //     0xaafec8: add             x3, PP, #0xe, lsl #12  ; [pp+0xecc0] SubtypeTestCache
    //     0xaafecc: ldr             x3, [x3, #0xcc0]
    // 0xaafed0: r30 = InstanceOfStub
    //     0xaafed0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xaafed4: LoadField: r30 = r30->field_7
    //     0xaafed4: ldur            lr, [lr, #7]
    // 0xaafed8: blr             lr
    // 0xaafedc: b               #0xaafeec
    // 0xaafee0: r0 = false
    //     0xaafee0: add             x0, NULL, #0x30  ; false
    // 0xaafee4: b               #0xaafeec
    // 0xaafee8: r0 = true
    //     0xaafee8: add             x0, NULL, #0x20  ; true
    // 0xaafeec: tbnz            w0, #4, #0xaaff70
    // 0xaafef0: ldur            x3, [fp, #-8]
    // 0xaafef4: ldur            x0, [fp, #-0x10]
    // 0xaafef8: r4 = 4
    //     0xaafef8: movz            x4, #0x4
    // 0xaafefc: mov             x2, x4
    // 0xaaff00: r1 = Null
    //     0xaaff00: mov             x1, NULL
    // 0xaaff04: r0 = AllocateArray()
    //     0xaaff04: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaaff08: mov             x2, x0
    // 0xaaff0c: ldur            x0, [fp, #-8]
    // 0xaaff10: stur            x2, [fp, #-0x18]
    // 0xaaff14: StoreField: r2->field_f = r0
    //     0xaaff14: stur            w0, [x2, #0xf]
    // 0xaaff18: ldur            x0, [fp, #-0x10]
    // 0xaaff1c: StoreField: r2->field_13 = r0
    //     0xaaff1c: stur            w0, [x2, #0x13]
    // 0xaaff20: r1 = <Future<void?>>
    //     0xaaff20: ldr             x1, [PP, #0x33f8]  ; [pp+0x33f8] TypeArguments: <Future<void?>>
    // 0xaaff24: r0 = AllocateGrowableArray()
    //     0xaaff24: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaaff28: mov             x1, x0
    // 0xaaff2c: ldur            x0, [fp, #-0x18]
    // 0xaaff30: StoreField: r1->field_f = r0
    //     0xaaff30: stur            w0, [x1, #0xf]
    // 0xaaff34: r0 = 4
    //     0xaaff34: movz            x0, #0x4
    // 0xaaff38: StoreField: r1->field_b = r0
    //     0xaaff38: stur            w0, [x1, #0xb]
    // 0xaaff3c: r16 = <void?>
    //     0xaaff3c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xaaff40: stp             x1, x16, [SP]
    // 0xaaff44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaaff44: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaaff48: r0 = wait()
    //     0xaaff48: bl              #0x678258  ; [dart:async] Future::wait
    // 0xaaff4c: r16 = <void?>
    //     0xaaff4c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xaaff50: stp             x0, x16, [SP, #8]
    // 0xaaff54: r16 = Closure: (Object?) => void from Function '_ignore@804008561': static.
    //     0xaaff54: add             x16, PP, #0xe, lsl #12  ; [pp+0xea90] Closure: (Object?) => void from Function '_ignore@804008561': static. (0x7e54fb8b8ce0)
    //     0xaaff58: ldr             x16, [x16, #0xa90]
    // 0xaaff5c: str             x16, [SP]
    // 0xaaff60: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaaff60: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaaff64: r0 = then()
    //     0xaaff64: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xaaff68: mov             x1, x0
    // 0xaaff6c: b               #0xaaff78
    // 0xaaff70: ldur            x0, [fp, #-8]
    // 0xaaff74: mov             x1, x0
    // 0xaaff78: mov             x0, x1
    // 0xaaff7c: LeaveFrame
    //     0xaaff7c: mov             SP, fp
    //     0xaaff80: ldp             fp, lr, [SP], #0x10
    // 0xaaff84: ret
    //     0xaaff84: ret             
    // 0xaaff88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaff88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaff8c: b               #0xaafe3c
  }
}
