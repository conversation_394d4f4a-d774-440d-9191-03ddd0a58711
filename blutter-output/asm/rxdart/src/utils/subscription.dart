// lib: , url: package:rxdart/src/utils/subscription.dart

// class id: 1051104, size: 0x8
class :: {

  static _ StreamSubscriptionsIterableExtension.cancelAll(/* No info */) {
    // ** addr: 0x8b71ec, size: 0x194
    // 0x8b71ec: EnterFrame
    //     0x8b71ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8b71f0: mov             fp, SP
    // 0x8b71f4: AllocStack(0x38)
    //     0x8b71f4: sub             SP, SP, #0x38
    // 0x8b71f8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x8b71f8: mov             x0, x1
    //     0x8b71fc: stur            x1, [fp, #-8]
    // 0x8b7200: CheckStackOverflow
    //     0x8b7200: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7204: cmp             SP, x16
    //     0x8b7208: b.ls            #0x8b7370
    // 0x8b720c: r1 = <Future<void?>>
    //     0x8b720c: ldr             x1, [PP, #0x33f8]  ; [pp+0x33f8] TypeArguments: <Future<void?>>
    // 0x8b7210: r2 = 0
    //     0x8b7210: movz            x2, #0
    // 0x8b7214: r0 = _GrowableList()
    //     0x8b7214: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8b7218: mov             x4, x0
    // 0x8b721c: ldur            x3, [fp, #-8]
    // 0x8b7220: stur            x4, [fp, #-0x30]
    // 0x8b7224: LoadField: r5 = r3->field_7
    //     0x8b7224: ldur            w5, [x3, #7]
    // 0x8b7228: DecompressPointer r5
    //     0x8b7228: add             x5, x5, HEAP, lsl #32
    // 0x8b722c: stur            x5, [fp, #-0x28]
    // 0x8b7230: LoadField: r0 = r3->field_b
    //     0x8b7230: ldur            w0, [x3, #0xb]
    // 0x8b7234: r6 = LoadInt32Instr(r0)
    //     0x8b7234: sbfx            x6, x0, #1, #0x1f
    // 0x8b7238: stur            x6, [fp, #-0x20]
    // 0x8b723c: r0 = 0
    //     0x8b723c: movz            x0, #0
    // 0x8b7240: CheckStackOverflow
    //     0x8b7240: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7244: cmp             SP, x16
    //     0x8b7248: b.ls            #0x8b7378
    // 0x8b724c: cmp             x0, x6
    // 0x8b7250: b.ge            #0x8b7358
    // 0x8b7254: ArrayLoad: r7 = r3[r0]  ; Unknown_4
    //     0x8b7254: add             x16, x3, x0, lsl #2
    //     0x8b7258: ldur            w7, [x16, #0xf]
    // 0x8b725c: DecompressPointer r7
    //     0x8b725c: add             x7, x7, HEAP, lsl #32
    // 0x8b7260: stur            x7, [fp, #-0x18]
    // 0x8b7264: add             x8, x0, #1
    // 0x8b7268: stur            x8, [fp, #-0x10]
    // 0x8b726c: cmp             w7, NULL
    // 0x8b7270: b.ne            #0x8b72a4
    // 0x8b7274: mov             x0, x7
    // 0x8b7278: mov             x2, x5
    // 0x8b727c: r1 = Null
    //     0x8b727c: mov             x1, NULL
    // 0x8b7280: cmp             w2, NULL
    // 0x8b7284: b.eq            #0x8b72a4
    // 0x8b7288: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8b7288: ldur            w4, [x2, #0x17]
    // 0x8b728c: DecompressPointer r4
    //     0x8b728c: add             x4, x4, HEAP, lsl #32
    // 0x8b7290: r8 = X0
    //     0x8b7290: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8b7294: LoadField: r9 = r4->field_7
    //     0x8b7294: ldur            x9, [x4, #7]
    // 0x8b7298: r3 = Null
    //     0x8b7298: add             x3, PP, #0xe, lsl #12  ; [pp+0xea80] Null
    //     0x8b729c: ldr             x3, [x3, #0xa80]
    // 0x8b72a0: blr             x9
    // 0x8b72a4: ldur            x2, [fp, #-0x30]
    // 0x8b72a8: ldur            x1, [fp, #-0x18]
    // 0x8b72ac: r0 = LoadClassIdInstr(r1)
    //     0x8b72ac: ldur            x0, [x1, #-1]
    //     0x8b72b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8b72b4: r0 = GDT[cid_x0 + -0x37]()
    //     0x8b72b4: sub             lr, x0, #0x37
    //     0x8b72b8: ldr             lr, [x21, lr, lsl #3]
    //     0x8b72bc: blr             lr
    // 0x8b72c0: mov             x2, x0
    // 0x8b72c4: ldur            x0, [fp, #-0x30]
    // 0x8b72c8: stur            x2, [fp, #-0x18]
    // 0x8b72cc: LoadField: r1 = r0->field_b
    //     0x8b72cc: ldur            w1, [x0, #0xb]
    // 0x8b72d0: LoadField: r3 = r0->field_f
    //     0x8b72d0: ldur            w3, [x0, #0xf]
    // 0x8b72d4: DecompressPointer r3
    //     0x8b72d4: add             x3, x3, HEAP, lsl #32
    // 0x8b72d8: LoadField: r4 = r3->field_b
    //     0x8b72d8: ldur            w4, [x3, #0xb]
    // 0x8b72dc: r3 = LoadInt32Instr(r1)
    //     0x8b72dc: sbfx            x3, x1, #1, #0x1f
    // 0x8b72e0: stur            x3, [fp, #-0x38]
    // 0x8b72e4: r1 = LoadInt32Instr(r4)
    //     0x8b72e4: sbfx            x1, x4, #1, #0x1f
    // 0x8b72e8: cmp             x3, x1
    // 0x8b72ec: b.ne            #0x8b72f8
    // 0x8b72f0: mov             x1, x0
    // 0x8b72f4: r0 = _growToNextCapacity()
    //     0x8b72f4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8b72f8: ldur            x2, [fp, #-0x30]
    // 0x8b72fc: ldur            x3, [fp, #-0x38]
    // 0x8b7300: add             x0, x3, #1
    // 0x8b7304: lsl             x1, x0, #1
    // 0x8b7308: StoreField: r2->field_b = r1
    //     0x8b7308: stur            w1, [x2, #0xb]
    // 0x8b730c: LoadField: r1 = r2->field_f
    //     0x8b730c: ldur            w1, [x2, #0xf]
    // 0x8b7310: DecompressPointer r1
    //     0x8b7310: add             x1, x1, HEAP, lsl #32
    // 0x8b7314: ldur            x0, [fp, #-0x18]
    // 0x8b7318: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8b7318: add             x25, x1, x3, lsl #2
    //     0x8b731c: add             x25, x25, #0xf
    //     0x8b7320: str             w0, [x25]
    //     0x8b7324: tbz             w0, #0, #0x8b7340
    //     0x8b7328: ldurb           w16, [x1, #-1]
    //     0x8b732c: ldurb           w17, [x0, #-1]
    //     0x8b7330: and             x16, x17, x16, lsr #2
    //     0x8b7334: tst             x16, HEAP, lsr #32
    //     0x8b7338: b.eq            #0x8b7340
    //     0x8b733c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b7340: ldur            x0, [fp, #-0x10]
    // 0x8b7344: ldur            x3, [fp, #-8]
    // 0x8b7348: mov             x4, x2
    // 0x8b734c: ldur            x5, [fp, #-0x28]
    // 0x8b7350: ldur            x6, [fp, #-0x20]
    // 0x8b7354: b               #0x8b7240
    // 0x8b7358: mov             x2, x4
    // 0x8b735c: mov             x1, x2
    // 0x8b7360: r0 = waitFuturesList()
    //     0x8b7360: bl              #0x8b7380  ; [package:rxdart/src/utils/future.dart] ::waitFuturesList
    // 0x8b7364: LeaveFrame
    //     0x8b7364: mov             SP, fp
    //     0x8b7368: ldp             fp, lr, [SP], #0x10
    // 0x8b736c: ret
    //     0x8b736c: ret             
    // 0x8b7370: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7370: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7374: b               #0x8b720c
    // 0x8b7378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b737c: b               #0x8b724c
  }
  static _ StreamSubscriptionsIterableExtensions.resumeAll(/* No info */) {
    // ** addr: 0x8b74b0, size: 0xec
    // 0x8b74b0: EnterFrame
    //     0x8b74b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b74b4: mov             fp, SP
    // 0x8b74b8: AllocStack(0x28)
    //     0x8b74b8: sub             SP, SP, #0x28
    // 0x8b74bc: SetupParameters(dynamic _ /* r1 => r3, fp-0x28 */)
    //     0x8b74bc: mov             x3, x1
    //     0x8b74c0: stur            x1, [fp, #-0x28]
    // 0x8b74c4: CheckStackOverflow
    //     0x8b74c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b74c8: cmp             SP, x16
    //     0x8b74cc: b.ls            #0x8b758c
    // 0x8b74d0: LoadField: r4 = r3->field_7
    //     0x8b74d0: ldur            w4, [x3, #7]
    // 0x8b74d4: DecompressPointer r4
    //     0x8b74d4: add             x4, x4, HEAP, lsl #32
    // 0x8b74d8: stur            x4, [fp, #-0x20]
    // 0x8b74dc: LoadField: r0 = r3->field_b
    //     0x8b74dc: ldur            w0, [x3, #0xb]
    // 0x8b74e0: r5 = LoadInt32Instr(r0)
    //     0x8b74e0: sbfx            x5, x0, #1, #0x1f
    // 0x8b74e4: stur            x5, [fp, #-0x18]
    // 0x8b74e8: r0 = 0
    //     0x8b74e8: movz            x0, #0
    // 0x8b74ec: CheckStackOverflow
    //     0x8b74ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b74f0: cmp             SP, x16
    //     0x8b74f4: b.ls            #0x8b7594
    // 0x8b74f8: cmp             x0, x5
    // 0x8b74fc: b.ge            #0x8b757c
    // 0x8b7500: ArrayLoad: r6 = r3[r0]  ; Unknown_4
    //     0x8b7500: add             x16, x3, x0, lsl #2
    //     0x8b7504: ldur            w6, [x16, #0xf]
    // 0x8b7508: DecompressPointer r6
    //     0x8b7508: add             x6, x6, HEAP, lsl #32
    // 0x8b750c: stur            x6, [fp, #-0x10]
    // 0x8b7510: add             x7, x0, #1
    // 0x8b7514: stur            x7, [fp, #-8]
    // 0x8b7518: cmp             w6, NULL
    // 0x8b751c: b.ne            #0x8b7550
    // 0x8b7520: mov             x0, x6
    // 0x8b7524: mov             x2, x4
    // 0x8b7528: r1 = Null
    //     0x8b7528: mov             x1, NULL
    // 0x8b752c: cmp             w2, NULL
    // 0x8b7530: b.eq            #0x8b7550
    // 0x8b7534: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8b7534: ldur            w4, [x2, #0x17]
    // 0x8b7538: DecompressPointer r4
    //     0x8b7538: add             x4, x4, HEAP, lsl #32
    // 0x8b753c: r8 = X0
    //     0x8b753c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8b7540: LoadField: r9 = r4->field_7
    //     0x8b7540: ldur            x9, [x4, #7]
    // 0x8b7544: r3 = Null
    //     0x8b7544: add             x3, PP, #0xe, lsl #12  ; [pp+0xea98] Null
    //     0x8b7548: ldr             x3, [x3, #0xa98]
    // 0x8b754c: blr             x9
    // 0x8b7550: ldur            x1, [fp, #-0x10]
    // 0x8b7554: r0 = LoadClassIdInstr(r1)
    //     0x8b7554: ldur            x0, [x1, #-1]
    //     0x8b7558: ubfx            x0, x0, #0xc, #0x14
    // 0x8b755c: r0 = GDT[cid_x0 + 0x3e9]()
    //     0x8b755c: add             lr, x0, #0x3e9
    //     0x8b7560: ldr             lr, [x21, lr, lsl #3]
    //     0x8b7564: blr             lr
    // 0x8b7568: ldur            x0, [fp, #-8]
    // 0x8b756c: ldur            x3, [fp, #-0x28]
    // 0x8b7570: ldur            x4, [fp, #-0x20]
    // 0x8b7574: ldur            x5, [fp, #-0x18]
    // 0x8b7578: b               #0x8b74ec
    // 0x8b757c: r0 = Null
    //     0x8b757c: mov             x0, NULL
    // 0x8b7580: LeaveFrame
    //     0x8b7580: mov             SP, fp
    //     0x8b7584: ldp             fp, lr, [SP], #0x10
    // 0x8b7588: ret
    //     0x8b7588: ret             
    // 0x8b758c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b758c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7590: b               #0x8b74d0
    // 0x8b7594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b7594: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7598: b               #0x8b74f8
  }
  static _ StreamSubscriptionsIterableExtensions.pauseAll(/* No info */) {
    // ** addr: 0x8b7610, size: 0xf4
    // 0x8b7610: EnterFrame
    //     0x8b7610: stp             fp, lr, [SP, #-0x10]!
    //     0x8b7614: mov             fp, SP
    // 0x8b7618: AllocStack(0x30)
    //     0x8b7618: sub             SP, SP, #0x30
    // 0x8b761c: SetupParameters(dynamic _ /* r1 => r3, fp-0x28 */)
    //     0x8b761c: mov             x3, x1
    //     0x8b7620: stur            x1, [fp, #-0x28]
    // 0x8b7624: CheckStackOverflow
    //     0x8b7624: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7628: cmp             SP, x16
    //     0x8b762c: b.ls            #0x8b76f4
    // 0x8b7630: LoadField: r4 = r3->field_7
    //     0x8b7630: ldur            w4, [x3, #7]
    // 0x8b7634: DecompressPointer r4
    //     0x8b7634: add             x4, x4, HEAP, lsl #32
    // 0x8b7638: stur            x4, [fp, #-0x20]
    // 0x8b763c: LoadField: r0 = r3->field_b
    //     0x8b763c: ldur            w0, [x3, #0xb]
    // 0x8b7640: r5 = LoadInt32Instr(r0)
    //     0x8b7640: sbfx            x5, x0, #1, #0x1f
    // 0x8b7644: stur            x5, [fp, #-0x18]
    // 0x8b7648: r0 = 0
    //     0x8b7648: movz            x0, #0
    // 0x8b764c: CheckStackOverflow
    //     0x8b764c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b7650: cmp             SP, x16
    //     0x8b7654: b.ls            #0x8b76fc
    // 0x8b7658: cmp             x0, x5
    // 0x8b765c: b.ge            #0x8b76e4
    // 0x8b7660: ArrayLoad: r6 = r3[r0]  ; Unknown_4
    //     0x8b7660: add             x16, x3, x0, lsl #2
    //     0x8b7664: ldur            w6, [x16, #0xf]
    // 0x8b7668: DecompressPointer r6
    //     0x8b7668: add             x6, x6, HEAP, lsl #32
    // 0x8b766c: stur            x6, [fp, #-0x10]
    // 0x8b7670: add             x7, x0, #1
    // 0x8b7674: stur            x7, [fp, #-8]
    // 0x8b7678: cmp             w6, NULL
    // 0x8b767c: b.ne            #0x8b76b0
    // 0x8b7680: mov             x0, x6
    // 0x8b7684: mov             x2, x4
    // 0x8b7688: r1 = Null
    //     0x8b7688: mov             x1, NULL
    // 0x8b768c: cmp             w2, NULL
    // 0x8b7690: b.eq            #0x8b76b0
    // 0x8b7694: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8b7694: ldur            w4, [x2, #0x17]
    // 0x8b7698: DecompressPointer r4
    //     0x8b7698: add             x4, x4, HEAP, lsl #32
    // 0x8b769c: r8 = X0
    //     0x8b769c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8b76a0: LoadField: r9 = r4->field_7
    //     0x8b76a0: ldur            x9, [x4, #7]
    // 0x8b76a4: r3 = Null
    //     0x8b76a4: add             x3, PP, #0xe, lsl #12  ; [pp+0xeaa8] Null
    //     0x8b76a8: ldr             x3, [x3, #0xaa8]
    // 0x8b76ac: blr             x9
    // 0x8b76b0: ldur            x1, [fp, #-0x10]
    // 0x8b76b4: r0 = LoadClassIdInstr(r1)
    //     0x8b76b4: ldur            x0, [x1, #-1]
    //     0x8b76b8: ubfx            x0, x0, #0xc, #0x14
    // 0x8b76bc: str             NULL, [SP]
    // 0x8b76c0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8b76c0: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8b76c4: r0 = GDT[cid_x0 + 0x40b]()
    //     0x8b76c4: add             lr, x0, #0x40b
    //     0x8b76c8: ldr             lr, [x21, lr, lsl #3]
    //     0x8b76cc: blr             lr
    // 0x8b76d0: ldur            x0, [fp, #-8]
    // 0x8b76d4: ldur            x3, [fp, #-0x28]
    // 0x8b76d8: ldur            x4, [fp, #-0x20]
    // 0x8b76dc: ldur            x5, [fp, #-0x18]
    // 0x8b76e0: b               #0x8b764c
    // 0x8b76e4: r0 = Null
    //     0x8b76e4: mov             x0, NULL
    // 0x8b76e8: LeaveFrame
    //     0x8b76e8: mov             SP, fp
    //     0x8b76ec: ldp             fp, lr, [SP], #0x10
    // 0x8b76f0: ret
    //     0x8b76f0: ret             
    // 0x8b76f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b76f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b76f8: b               #0x8b7630
    // 0x8b76fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b76fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b7700: b               #0x8b7658
  }
}
