// lib: screenshot, url: package:screenshot/screenshot.dart

// class id: 1051105, size: 0x8
class :: {
}

// class id: 513, size: 0xc, field offset: 0x8
class ScreenshotController extends Object {

  late GlobalKey<State<StatefulWidget>> _containerKey; // offset: 0x8

  _ capture(/* No info */) {
    // ** addr: 0xa39a30, size: 0x70
    // 0xa39a30: EnterFrame
    //     0xa39a30: stp             fp, lr, [SP, #-0x10]!
    //     0xa39a34: mov             fp, SP
    // 0xa39a38: AllocStack(0x10)
    //     0xa39a38: sub             SP, SP, #0x10
    // 0xa39a3c: SetupParameters(ScreenshotController this /* r1 => r1, fp-0x8 */)
    //     0xa39a3c: stur            x1, [fp, #-8]
    // 0xa39a40: CheckStackOverflow
    //     0xa39a40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa39a44: cmp             SP, x16
    //     0xa39a48: b.ls            #0xa39a98
    // 0xa39a4c: r1 = 1
    //     0xa39a4c: movz            x1, #0x1
    // 0xa39a50: r0 = AllocateContext()
    //     0xa39a50: bl              #0xec126c  ; AllocateContextStub
    // 0xa39a54: mov             x1, x0
    // 0xa39a58: ldur            x0, [fp, #-8]
    // 0xa39a5c: StoreField: r1->field_f = r0
    //     0xa39a5c: stur            w0, [x1, #0xf]
    // 0xa39a60: mov             x2, x1
    // 0xa39a64: r1 = Function '<anonymous closure>':.
    //     0xa39a64: add             x1, PP, #0x40, lsl #12  ; [pp+0x401d0] AnonymousClosure: (0xa39aa0), in [package:screenshot/screenshot.dart] ScreenshotController::capture (0xa39a30)
    //     0xa39a68: ldr             x1, [x1, #0x1d0]
    // 0xa39a6c: r0 = AllocateClosure()
    //     0xa39a6c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa39a70: str             x0, [SP]
    // 0xa39a74: r1 = <Uint8List?>
    //     0xa39a74: add             x1, PP, #0x40, lsl #12  ; [pp+0x401d8] TypeArguments: <Uint8List?>
    //     0xa39a78: ldr             x1, [x1, #0x1d8]
    // 0xa39a7c: r2 = Instance_Duration
    //     0xa39a7c: add             x2, PP, #0x11, lsl #12  ; [pp+0x11bf8] Obj!Duration@e3a0d1
    //     0xa39a80: ldr             x2, [x2, #0xbf8]
    // 0xa39a84: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xa39a84: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xa39a88: r0 = Future.delayed()
    //     0xa39a88: bl              #0x657b70  ; [dart:async] Future::Future.delayed
    // 0xa39a8c: LeaveFrame
    //     0xa39a8c: mov             SP, fp
    //     0xa39a90: ldp             fp, lr, [SP], #0x10
    // 0xa39a94: ret
    //     0xa39a94: ret             
    // 0xa39a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa39a98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa39a9c: b               #0xa39a4c
  }
  [closure] Future<Uint8List?> <anonymous closure>(dynamic) async {
    // ** addr: 0xa39aa0, size: 0x10c
    // 0xa39aa0: EnterFrame
    //     0xa39aa0: stp             fp, lr, [SP, #-0x10]!
    //     0xa39aa4: mov             fp, SP
    // 0xa39aa8: AllocStack(0x20)
    //     0xa39aa8: sub             SP, SP, #0x20
    // 0xa39aac: SetupParameters(ScreenshotController this /* r1 */)
    //     0xa39aac: stur            NULL, [fp, #-8]
    //     0xa39ab0: movz            x0, #0
    //     0xa39ab4: add             x1, fp, w0, sxtw #2
    //     0xa39ab8: ldr             x1, [x1, #0x10]
    //     0xa39abc: ldur            w2, [x1, #0x17]
    //     0xa39ac0: add             x2, x2, HEAP, lsl #32
    //     0xa39ac4: stur            x2, [fp, #-0x10]
    // 0xa39ac8: CheckStackOverflow
    //     0xa39ac8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa39acc: cmp             SP, x16
    //     0xa39ad0: b.ls            #0xa39ba4
    // 0xa39ad4: InitAsync() -> Future<Uint8List?>
    //     0xa39ad4: add             x0, PP, #0x40, lsl #12  ; [pp+0x401d8] TypeArguments: <Uint8List?>
    //     0xa39ad8: ldr             x0, [x0, #0x1d8]
    //     0xa39adc: bl              #0x661298  ; InitAsyncStub
    // 0xa39ae0: ldur            x0, [fp, #-0x10]
    // 0xa39ae4: LoadField: r1 = r0->field_f
    //     0xa39ae4: ldur            w1, [x0, #0xf]
    // 0xa39ae8: DecompressPointer r1
    //     0xa39ae8: add             x1, x1, HEAP, lsl #32
    // 0xa39aec: r2 = Null
    //     0xa39aec: mov             x2, NULL
    // 0xa39af0: r0 = captureAsUiImage()
    //     0xa39af0: bl              #0xa3a1d0  ; [package:screenshot/screenshot.dart] ScreenshotController::captureAsUiImage
    // 0xa39af4: mov             x1, x0
    // 0xa39af8: stur            x1, [fp, #-0x18]
    // 0xa39afc: r0 = Await()
    //     0xa39afc: bl              #0x661044  ; AwaitStub
    // 0xa39b00: stur            x0, [fp, #-0x18]
    // 0xa39b04: cmp             w0, NULL
    // 0xa39b08: b.ne            #0xa39b18
    // 0xa39b0c: mov             x2, x0
    // 0xa39b10: r3 = Null
    //     0xa39b10: mov             x3, NULL
    // 0xa39b14: b               #0xa39b28
    // 0xa39b18: mov             x1, x0
    // 0xa39b1c: r0 = toByteData()
    //     0xa39b1c: bl              #0xa39bac  ; [dart:ui] Image::toByteData
    // 0xa39b20: mov             x3, x0
    // 0xa39b24: ldur            x2, [fp, #-0x18]
    // 0xa39b28: mov             x0, x3
    // 0xa39b2c: stur            x3, [fp, #-0x20]
    // 0xa39b30: r1 = <ByteData?>
    //     0xa39b30: ldr             x1, [PP, #0x3a8]  ; [pp+0x3a8] TypeArguments: <ByteData?>
    // 0xa39b34: r0 = AwaitWithTypeCheck()
    //     0xa39b34: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0xa39b38: ldur            x1, [fp, #-0x18]
    // 0xa39b3c: stur            x0, [fp, #-0x10]
    // 0xa39b40: cmp             w1, NULL
    // 0xa39b44: b.ne            #0xa39b50
    // 0xa39b48: mov             x1, x0
    // 0xa39b4c: b               #0xa39b58
    // 0xa39b50: r0 = dispose()
    //     0xa39b50: bl              #0x7615d4  ; [dart:ui] Image::dispose
    // 0xa39b54: ldur            x1, [fp, #-0x10]
    // 0xa39b58: cmp             w1, NULL
    // 0xa39b5c: b.ne            #0xa39b68
    // 0xa39b60: r0 = Null
    //     0xa39b60: mov             x0, NULL
    // 0xa39b64: b               #0xa39ba0
    // 0xa39b68: r0 = LoadClassIdInstr(r1)
    //     0xa39b68: ldur            x0, [x1, #-1]
    //     0xa39b6c: ubfx            x0, x0, #0xc, #0x14
    // 0xa39b70: r0 = GDT[cid_x0 + -0xf60]()
    //     0xa39b70: sub             lr, x0, #0xf60
    //     0xa39b74: ldr             lr, [x21, lr, lsl #3]
    //     0xa39b78: blr             lr
    // 0xa39b7c: r1 = LoadClassIdInstr(r0)
    //     0xa39b7c: ldur            x1, [x0, #-1]
    //     0xa39b80: ubfx            x1, x1, #0xc, #0x14
    // 0xa39b84: mov             x16, x0
    // 0xa39b88: mov             x0, x1
    // 0xa39b8c: mov             x1, x16
    // 0xa39b90: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa39b90: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa39b94: r0 = GDT[cid_x0 + -0x1000]()
    //     0xa39b94: sub             lr, x0, #1, lsl #12
    //     0xa39b98: ldr             lr, [x21, lr, lsl #3]
    //     0xa39b9c: blr             lr
    // 0xa39ba0: r0 = ReturnAsyncNotFuture()
    //     0xa39ba0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa39ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa39ba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa39ba8: b               #0xa39ad4
  }
  _ captureAsUiImage(/* No info */) {
    // ** addr: 0xa3a1d0, size: 0x6c
    // 0xa3a1d0: EnterFrame
    //     0xa3a1d0: stp             fp, lr, [SP, #-0x10]!
    //     0xa3a1d4: mov             fp, SP
    // 0xa3a1d8: AllocStack(0x10)
    //     0xa3a1d8: sub             SP, SP, #0x10
    // 0xa3a1dc: SetupParameters(ScreenshotController this /* r1 => r1, fp-0x8 */)
    //     0xa3a1dc: stur            x1, [fp, #-8]
    // 0xa3a1e0: CheckStackOverflow
    //     0xa3a1e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3a1e4: cmp             SP, x16
    //     0xa3a1e8: b.ls            #0xa3a234
    // 0xa3a1ec: r1 = 2
    //     0xa3a1ec: movz            x1, #0x2
    // 0xa3a1f0: r0 = AllocateContext()
    //     0xa3a1f0: bl              #0xec126c  ; AllocateContextStub
    // 0xa3a1f4: mov             x1, x0
    // 0xa3a1f8: ldur            x0, [fp, #-8]
    // 0xa3a1fc: StoreField: r1->field_f = r0
    //     0xa3a1fc: stur            w0, [x1, #0xf]
    // 0xa3a200: mov             x2, x1
    // 0xa3a204: r1 = Function '<anonymous closure>':.
    //     0xa3a204: add             x1, PP, #0x40, lsl #12  ; [pp+0x40208] AnonymousClosure: (0xa3a26c), in [package:screenshot/screenshot.dart] ScreenshotController::captureAsUiImage (0xa3a1d0)
    //     0xa3a208: ldr             x1, [x1, #0x208]
    // 0xa3a20c: r0 = AllocateClosure()
    //     0xa3a20c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa3a210: str             x0, [SP]
    // 0xa3a214: r1 = <Image?>
    //     0xa3a214: add             x1, PP, #0x40, lsl #12  ; [pp+0x40210] TypeArguments: <Image?>
    //     0xa3a218: ldr             x1, [x1, #0x210]
    // 0xa3a21c: r2 = Instance_Duration
    //     0xa3a21c: ldr             x2, [PP, #0x1d38]  ; [pp+0x1d38] Obj!Duration@e3a081
    // 0xa3a220: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xa3a220: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xa3a224: r0 = Future.delayed()
    //     0xa3a224: bl              #0x657b70  ; [dart:async] Future::Future.delayed
    // 0xa3a228: LeaveFrame
    //     0xa3a228: mov             SP, fp
    //     0xa3a22c: ldp             fp, lr, [SP], #0x10
    // 0xa3a230: ret
    //     0xa3a230: ret             
    // 0xa3a234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3a234: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3a238: b               #0xa3a1ec
  }
  [closure] Future<Image?> <anonymous closure>(dynamic) async {
    // ** addr: 0xa3a26c, size: 0x200
    // 0xa3a26c: EnterFrame
    //     0xa3a26c: stp             fp, lr, [SP, #-0x10]!
    //     0xa3a270: mov             fp, SP
    // 0xa3a274: AllocStack(0x90)
    //     0xa3a274: sub             SP, SP, #0x90
    // 0xa3a278: SetupParameters(ScreenshotController this /* r1, fp-0x88 */)
    //     0xa3a278: stur            NULL, [fp, #-8]
    //     0xa3a27c: movz            x0, #0
    //     0xa3a280: add             x1, fp, w0, sxtw #2
    //     0xa3a284: ldr             x1, [x1, #0x10]
    //     0xa3a288: stur            x1, [fp, #-0x88]
    //     0xa3a28c: ldur            w2, [x1, #0x17]
    //     0xa3a290: add             x2, x2, HEAP, lsl #32
    //     0xa3a294: stur            x2, [fp, #-0x80]
    // 0xa3a298: CheckStackOverflow
    //     0xa3a298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3a29c: cmp             SP, x16
    //     0xa3a2a0: b.ls            #0xa3a438
    // 0xa3a2a4: InitAsync() -> Future<Image?>
    //     0xa3a2a4: add             x0, PP, #0x40, lsl #12  ; [pp+0x40210] TypeArguments: <Image?>
    //     0xa3a2a8: ldr             x0, [x0, #0x210]
    //     0xa3a2ac: bl              #0x661298  ; InitAsyncStub
    // 0xa3a2b0: ldur            x0, [fp, #-0x80]
    // 0xa3a2b4: LoadField: r1 = r0->field_f
    //     0xa3a2b4: ldur            w1, [x0, #0xf]
    // 0xa3a2b8: DecompressPointer r1
    //     0xa3a2b8: add             x1, x1, HEAP, lsl #32
    // 0xa3a2bc: LoadField: r2 = r1->field_7
    //     0xa3a2bc: ldur            w2, [x1, #7]
    // 0xa3a2c0: DecompressPointer r2
    //     0xa3a2c0: add             x2, x2, HEAP, lsl #32
    // 0xa3a2c4: r16 = Sentinel
    //     0xa3a2c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa3a2c8: cmp             w2, w16
    // 0xa3a2cc: b.eq            #0xa3a440
    // 0xa3a2d0: mov             x1, x2
    // 0xa3a2d4: stur            x2, [fp, #-0x88]
    // 0xa3a2d8: r0 = _currentElement()
    //     0xa3a2d8: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0xa3a2dc: cmp             w0, NULL
    // 0xa3a2e0: b.ne            #0xa3a2ec
    // 0xa3a2e4: r3 = Null
    //     0xa3a2e4: mov             x3, NULL
    // 0xa3a2e8: b               #0xa3a2f8
    // 0xa3a2ec: mov             x1, x0
    // 0xa3a2f0: r0 = findRenderObject()
    //     0xa3a2f0: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0xa3a2f4: mov             x3, x0
    // 0xa3a2f8: stur            x3, [fp, #-0x88]
    // 0xa3a2fc: cmp             w3, NULL
    // 0xa3a300: b.ne            #0xa3a30c
    // 0xa3a304: r0 = Null
    //     0xa3a304: mov             x0, NULL
    // 0xa3a308: r0 = ReturnAsyncNotFuture()
    //     0xa3a308: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa3a30c: ldur            x4, [fp, #-0x80]
    // 0xa3a310: mov             x0, x3
    // 0xa3a314: r2 = Null
    //     0xa3a314: mov             x2, NULL
    // 0xa3a318: r1 = Null
    //     0xa3a318: mov             x1, NULL
    // 0xa3a31c: r4 = LoadClassIdInstr(r0)
    //     0xa3a31c: ldur            x4, [x0, #-1]
    //     0xa3a320: ubfx            x4, x4, #0xc, #0x14
    // 0xa3a324: cmp             x4, #0xc30
    // 0xa3a328: b.eq            #0xa3a340
    // 0xa3a32c: r8 = RenderRepaintBoundary
    //     0xa3a32c: add             x8, PP, #0x40, lsl #12  ; [pp+0x40218] Type: RenderRepaintBoundary
    //     0xa3a330: ldr             x8, [x8, #0x218]
    // 0xa3a334: r3 = Null
    //     0xa3a334: add             x3, PP, #0x40, lsl #12  ; [pp+0x40220] Null
    //     0xa3a338: ldr             x3, [x3, #0x220]
    // 0xa3a33c: r0 = DefaultTypeTest()
    //     0xa3a33c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xa3a340: ldur            x0, [fp, #-0x80]
    // 0xa3a344: LoadField: r1 = r0->field_f
    //     0xa3a344: ldur            w1, [x0, #0xf]
    // 0xa3a348: DecompressPointer r1
    //     0xa3a348: add             x1, x1, HEAP, lsl #32
    // 0xa3a34c: LoadField: r2 = r1->field_7
    //     0xa3a34c: ldur            w2, [x1, #7]
    // 0xa3a350: DecompressPointer r2
    //     0xa3a350: add             x2, x2, HEAP, lsl #32
    // 0xa3a354: r16 = Sentinel
    //     0xa3a354: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa3a358: cmp             w2, w16
    // 0xa3a35c: b.eq            #0xa3a44c
    // 0xa3a360: mov             x1, x2
    // 0xa3a364: stur            x2, [fp, #-0x90]
    // 0xa3a368: r0 = _currentElement()
    //     0xa3a368: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0xa3a36c: mov             x2, x0
    // 0xa3a370: ldur            x0, [fp, #-0x80]
    // 0xa3a374: stur            x2, [fp, #-0x90]
    // 0xa3a378: LoadField: r1 = r0->field_13
    //     0xa3a378: ldur            w1, [x0, #0x13]
    // 0xa3a37c: DecompressPointer r1
    //     0xa3a37c: add             x1, x1, HEAP, lsl #32
    // 0xa3a380: cmp             w1, NULL
    // 0xa3a384: b.ne            #0xa3a3fc
    // 0xa3a388: cmp             w2, NULL
    // 0xa3a38c: b.eq            #0xa3a3f4
    // 0xa3a390: mov             x1, x2
    // 0xa3a394: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa3a394: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa3a398: r0 = _of()
    //     0xa3a398: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa3a39c: LoadField: d0 = r0->field_b
    //     0xa3a39c: ldur            d0, [x0, #0xb]
    // 0xa3a3a0: r1 = inline_Allocate_Double()
    //     0xa3a3a0: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa3a3a4: add             x1, x1, #0x10
    //     0xa3a3a8: cmp             x0, x1
    //     0xa3a3ac: b.ls            #0xa3a458
    //     0xa3a3b0: str             x1, [THR, #0x50]  ; THR::top
    //     0xa3a3b4: sub             x1, x1, #0xf
    //     0xa3a3b8: movz            x0, #0xe15c
    //     0xa3a3bc: movk            x0, #0x3, lsl #16
    //     0xa3a3c0: stur            x0, [x1, #-1]
    // 0xa3a3c4: StoreField: r1->field_7 = d0
    //     0xa3a3c4: stur            d0, [x1, #7]
    // 0xa3a3c8: mov             x0, x1
    // 0xa3a3cc: ldur            x2, [fp, #-0x80]
    // 0xa3a3d0: StoreField: r2->field_13 = r0
    //     0xa3a3d0: stur            w0, [x2, #0x13]
    //     0xa3a3d4: ldurb           w16, [x2, #-1]
    //     0xa3a3d8: ldurb           w17, [x0, #-1]
    //     0xa3a3dc: and             x16, x17, x16, lsr #2
    //     0xa3a3e0: tst             x16, HEAP, lsr #32
    //     0xa3a3e4: b.eq            #0xa3a3ec
    //     0xa3a3e8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa3a3ec: mov             x0, x1
    // 0xa3a3f0: b               #0xa3a400
    // 0xa3a3f4: mov             x0, x1
    // 0xa3a3f8: b               #0xa3a400
    // 0xa3a3fc: mov             x0, x1
    // 0xa3a400: cmp             w0, NULL
    // 0xa3a404: b.ne            #0xa3a410
    // 0xa3a408: d0 = 1.000000
    //     0xa3a408: fmov            d0, #1.00000000
    // 0xa3a40c: b               #0xa3a414
    // 0xa3a410: LoadField: d0 = r0->field_7
    //     0xa3a410: ldur            d0, [x0, #7]
    // 0xa3a414: ldur            x1, [fp, #-0x88]
    // 0xa3a418: r0 = toImage()
    //     0xa3a418: bl              #0xa3a46c  ; [package:flutter/src/rendering/proxy_box.dart] RenderRepaintBoundary::toImage
    // 0xa3a41c: mov             x1, x0
    // 0xa3a420: stur            x1, [fp, #-0x80]
    // 0xa3a424: r0 = Await()
    //     0xa3a424: bl              #0x661044  ; AwaitStub
    // 0xa3a428: r0 = ReturnAsync()
    //     0xa3a428: b               #0x6576a4  ; ReturnAsyncStub
    // 0xa3a42c: sub             SP, fp, #0x90
    // 0xa3a430: r0 = Throw()
    //     0xa3a430: bl              #0xec04b8  ; ThrowStub
    // 0xa3a434: brk             #0
    // 0xa3a438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3a438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3a43c: b               #0xa3a2a4
    // 0xa3a440: r9 = _containerKey
    //     0xa3a440: add             x9, PP, #0x40, lsl #12  ; [pp+0x40230] Field <ScreenshotController._containerKey@2045074191>: late (offset: 0x8)
    //     0xa3a444: ldr             x9, [x9, #0x230]
    // 0xa3a448: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa3a448: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa3a44c: r9 = _containerKey
    //     0xa3a44c: add             x9, PP, #0x40, lsl #12  ; [pp+0x40230] Field <ScreenshotController._containerKey@2045074191>: late (offset: 0x8)
    //     0xa3a450: ldr             x9, [x9, #0x230]
    // 0xa3a454: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa3a454: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa3a458: SaveReg d0
    //     0xa3a458: str             q0, [SP, #-0x10]!
    // 0xa3a45c: r0 = AllocateDouble()
    //     0xa3a45c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa3a460: mov             x1, x0
    // 0xa3a464: RestoreReg d0
    //     0xa3a464: ldr             q0, [SP], #0x10
    // 0xa3a468: b               #0xa3a3c4
  }
}

// class id: 4095, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class _ScreenshotState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ dispose(/* No info */) {
    // ** addr: 0xa83d04, size: 0x94
    // 0xa83d04: EnterFrame
    //     0xa83d04: stp             fp, lr, [SP, #-0x10]!
    //     0xa83d08: mov             fp, SP
    // 0xa83d0c: AllocStack(0x10)
    //     0xa83d0c: sub             SP, SP, #0x10
    // 0xa83d10: SetupParameters(_ScreenshotState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa83d10: mov             x0, x1
    //     0xa83d14: stur            x1, [fp, #-0x10]
    // 0xa83d18: CheckStackOverflow
    //     0xa83d18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83d1c: cmp             SP, x16
    //     0xa83d20: b.ls            #0xa83d90
    // 0xa83d24: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa83d24: ldur            w3, [x0, #0x17]
    // 0xa83d28: DecompressPointer r3
    //     0xa83d28: add             x3, x3, HEAP, lsl #32
    // 0xa83d2c: stur            x3, [fp, #-8]
    // 0xa83d30: cmp             w3, NULL
    // 0xa83d34: b.ne            #0xa83d40
    // 0xa83d38: mov             x1, x0
    // 0xa83d3c: b               #0xa83d7c
    // 0xa83d40: mov             x2, x0
    // 0xa83d44: r1 = Function '_updateTickers@364311458':.
    //     0xa83d44: add             x1, PP, #0x51, lsl #12  ; [pp+0x51508] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa83d48: ldr             x1, [x1, #0x508]
    // 0xa83d4c: r0 = AllocateClosure()
    //     0xa83d4c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa83d50: ldur            x1, [fp, #-8]
    // 0xa83d54: r2 = LoadClassIdInstr(r1)
    //     0xa83d54: ldur            x2, [x1, #-1]
    //     0xa83d58: ubfx            x2, x2, #0xc, #0x14
    // 0xa83d5c: mov             x16, x0
    // 0xa83d60: mov             x0, x2
    // 0xa83d64: mov             x2, x16
    // 0xa83d68: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa83d68: movz            x17, #0xbf5c
    //     0xa83d6c: add             lr, x0, x17
    //     0xa83d70: ldr             lr, [x21, lr, lsl #3]
    //     0xa83d74: blr             lr
    // 0xa83d78: ldur            x1, [fp, #-0x10]
    // 0xa83d7c: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa83d7c: stur            NULL, [x1, #0x17]
    // 0xa83d80: r0 = Null
    //     0xa83d80: mov             x0, NULL
    // 0xa83d84: LeaveFrame
    //     0xa83d84: mov             SP, fp
    //     0xa83d88: ldp             fp, lr, [SP], #0x10
    // 0xa83d8c: ret
    //     0xa83d8c: ret             
    // 0xa83d90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83d90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83d94: b               #0xa83d24
  }
  _ activate(/* No info */) {
    // ** addr: 0xa85e44, size: 0x30
    // 0xa85e44: EnterFrame
    //     0xa85e44: stp             fp, lr, [SP, #-0x10]!
    //     0xa85e48: mov             fp, SP
    // 0xa85e4c: CheckStackOverflow
    //     0xa85e4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85e50: cmp             SP, x16
    //     0xa85e54: b.ls            #0xa85e6c
    // 0xa85e58: r0 = _updateTickerModeNotifier()
    //     0xa85e58: bl              #0xa85e74  ; [package:screenshot/screenshot.dart] _ScreenshotState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa85e5c: r0 = Null
    //     0xa85e5c: mov             x0, NULL
    // 0xa85e60: LeaveFrame
    //     0xa85e60: mov             SP, fp
    //     0xa85e64: ldp             fp, lr, [SP], #0x10
    // 0xa85e68: ret
    //     0xa85e68: ret             
    // 0xa85e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85e6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85e70: b               #0xa85e58
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0xa85e74, size: 0x124
    // 0xa85e74: EnterFrame
    //     0xa85e74: stp             fp, lr, [SP, #-0x10]!
    //     0xa85e78: mov             fp, SP
    // 0xa85e7c: AllocStack(0x18)
    //     0xa85e7c: sub             SP, SP, #0x18
    // 0xa85e80: SetupParameters(_ScreenshotState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0xa85e80: mov             x2, x1
    //     0xa85e84: stur            x1, [fp, #-8]
    // 0xa85e88: CheckStackOverflow
    //     0xa85e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85e8c: cmp             SP, x16
    //     0xa85e90: b.ls            #0xa85f8c
    // 0xa85e94: LoadField: r1 = r2->field_f
    //     0xa85e94: ldur            w1, [x2, #0xf]
    // 0xa85e98: DecompressPointer r1
    //     0xa85e98: add             x1, x1, HEAP, lsl #32
    // 0xa85e9c: cmp             w1, NULL
    // 0xa85ea0: b.eq            #0xa85f94
    // 0xa85ea4: r0 = getNotifier()
    //     0xa85ea4: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0xa85ea8: mov             x3, x0
    // 0xa85eac: ldur            x0, [fp, #-8]
    // 0xa85eb0: stur            x3, [fp, #-0x18]
    // 0xa85eb4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa85eb4: ldur            w4, [x0, #0x17]
    // 0xa85eb8: DecompressPointer r4
    //     0xa85eb8: add             x4, x4, HEAP, lsl #32
    // 0xa85ebc: stur            x4, [fp, #-0x10]
    // 0xa85ec0: cmp             w3, w4
    // 0xa85ec4: b.ne            #0xa85ed8
    // 0xa85ec8: r0 = Null
    //     0xa85ec8: mov             x0, NULL
    // 0xa85ecc: LeaveFrame
    //     0xa85ecc: mov             SP, fp
    //     0xa85ed0: ldp             fp, lr, [SP], #0x10
    // 0xa85ed4: ret
    //     0xa85ed4: ret             
    // 0xa85ed8: cmp             w4, NULL
    // 0xa85edc: b.eq            #0xa85f20
    // 0xa85ee0: mov             x2, x0
    // 0xa85ee4: r1 = Function '_updateTickers@364311458':.
    //     0xa85ee4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51508] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa85ee8: ldr             x1, [x1, #0x508]
    // 0xa85eec: r0 = AllocateClosure()
    //     0xa85eec: bl              #0xec1630  ; AllocateClosureStub
    // 0xa85ef0: ldur            x1, [fp, #-0x10]
    // 0xa85ef4: r2 = LoadClassIdInstr(r1)
    //     0xa85ef4: ldur            x2, [x1, #-1]
    //     0xa85ef8: ubfx            x2, x2, #0xc, #0x14
    // 0xa85efc: mov             x16, x0
    // 0xa85f00: mov             x0, x2
    // 0xa85f04: mov             x2, x16
    // 0xa85f08: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa85f08: movz            x17, #0xbf5c
    //     0xa85f0c: add             lr, x0, x17
    //     0xa85f10: ldr             lr, [x21, lr, lsl #3]
    //     0xa85f14: blr             lr
    // 0xa85f18: ldur            x0, [fp, #-8]
    // 0xa85f1c: ldur            x3, [fp, #-0x18]
    // 0xa85f20: mov             x2, x0
    // 0xa85f24: r1 = Function '_updateTickers@364311458':.
    //     0xa85f24: add             x1, PP, #0x51, lsl #12  ; [pp+0x51508] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa85f28: ldr             x1, [x1, #0x508]
    // 0xa85f2c: r0 = AllocateClosure()
    //     0xa85f2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa85f30: ldur            x3, [fp, #-0x18]
    // 0xa85f34: r1 = LoadClassIdInstr(r3)
    //     0xa85f34: ldur            x1, [x3, #-1]
    //     0xa85f38: ubfx            x1, x1, #0xc, #0x14
    // 0xa85f3c: mov             x2, x0
    // 0xa85f40: mov             x0, x1
    // 0xa85f44: mov             x1, x3
    // 0xa85f48: r0 = GDT[cid_x0 + 0xc407]()
    //     0xa85f48: movz            x17, #0xc407
    //     0xa85f4c: add             lr, x0, x17
    //     0xa85f50: ldr             lr, [x21, lr, lsl #3]
    //     0xa85f54: blr             lr
    // 0xa85f58: ldur            x0, [fp, #-0x18]
    // 0xa85f5c: ldur            x1, [fp, #-8]
    // 0xa85f60: ArrayStore: r1[0] = r0  ; List_4
    //     0xa85f60: stur            w0, [x1, #0x17]
    //     0xa85f64: ldurb           w16, [x1, #-1]
    //     0xa85f68: ldurb           w17, [x0, #-1]
    //     0xa85f6c: and             x16, x17, x16, lsr #2
    //     0xa85f70: tst             x16, HEAP, lsr #32
    //     0xa85f74: b.eq            #0xa85f7c
    //     0xa85f78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa85f7c: r0 = Null
    //     0xa85f7c: mov             x0, NULL
    // 0xa85f80: LeaveFrame
    //     0xa85f80: mov             SP, fp
    //     0xa85f84: ldp             fp, lr, [SP], #0x10
    // 0xa85f88: ret
    //     0xa85f88: ret             
    // 0xa85f8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85f8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85f90: b               #0xa85e94
    // 0xa85f94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa85f94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4096, size: 0x20, field offset: 0x1c
class ScreenshotState extends _ScreenshotState&State&TickerProviderStateMixin {

  late ScreenshotController _controller; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x97ea6c, size: 0x50
    // 0x97ea6c: LoadField: r2 = r1->field_b
    //     0x97ea6c: ldur            w2, [x1, #0xb]
    // 0x97ea70: DecompressPointer r2
    //     0x97ea70: add             x2, x2, HEAP, lsl #32
    // 0x97ea74: cmp             w2, NULL
    // 0x97ea78: b.eq            #0x97eab0
    // 0x97ea7c: LoadField: r0 = r2->field_f
    //     0x97ea7c: ldur            w0, [x2, #0xf]
    // 0x97ea80: DecompressPointer r0
    //     0x97ea80: add             x0, x0, HEAP, lsl #32
    // 0x97ea84: StoreField: r1->field_1b = r0
    //     0x97ea84: stur            w0, [x1, #0x1b]
    //     0x97ea88: ldurb           w16, [x1, #-1]
    //     0x97ea8c: ldurb           w17, [x0, #-1]
    //     0x97ea90: and             x16, x17, x16, lsr #2
    //     0x97ea94: tst             x16, HEAP, lsr #32
    //     0x97ea98: b.eq            #0x97eaa8
    //     0x97ea9c: str             lr, [SP, #-8]!
    //     0x97eaa0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    //     0x97eaa4: ldr             lr, [SP], #8
    // 0x97eaa8: r0 = Null
    //     0x97eaa8: mov             x0, NULL
    // 0x97eaac: ret
    //     0x97eaac: ret             
    // 0x97eab0: EnterFrame
    //     0x97eab0: stp             fp, lr, [SP, #-0x10]!
    //     0x97eab4: mov             fp, SP
    // 0x97eab8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97eab8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa4b1ac, size: 0x90
    // 0xa4b1ac: EnterFrame
    //     0xa4b1ac: stp             fp, lr, [SP, #-0x10]!
    //     0xa4b1b0: mov             fp, SP
    // 0xa4b1b4: AllocStack(0x10)
    //     0xa4b1b4: sub             SP, SP, #0x10
    // 0xa4b1b8: LoadField: r0 = r1->field_1b
    //     0xa4b1b8: ldur            w0, [x1, #0x1b]
    // 0xa4b1bc: DecompressPointer r0
    //     0xa4b1bc: add             x0, x0, HEAP, lsl #32
    // 0xa4b1c0: r16 = Sentinel
    //     0xa4b1c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4b1c4: cmp             w0, w16
    // 0xa4b1c8: b.eq            #0xa4b220
    // 0xa4b1cc: LoadField: r2 = r0->field_7
    //     0xa4b1cc: ldur            w2, [x0, #7]
    // 0xa4b1d0: DecompressPointer r2
    //     0xa4b1d0: add             x2, x2, HEAP, lsl #32
    // 0xa4b1d4: r16 = Sentinel
    //     0xa4b1d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4b1d8: cmp             w2, w16
    // 0xa4b1dc: b.eq            #0xa4b22c
    // 0xa4b1e0: stur            x2, [fp, #-0x10]
    // 0xa4b1e4: LoadField: r0 = r1->field_b
    //     0xa4b1e4: ldur            w0, [x1, #0xb]
    // 0xa4b1e8: DecompressPointer r0
    //     0xa4b1e8: add             x0, x0, HEAP, lsl #32
    // 0xa4b1ec: cmp             w0, NULL
    // 0xa4b1f0: b.eq            #0xa4b238
    // 0xa4b1f4: LoadField: r1 = r0->field_b
    //     0xa4b1f4: ldur            w1, [x0, #0xb]
    // 0xa4b1f8: DecompressPointer r1
    //     0xa4b1f8: add             x1, x1, HEAP, lsl #32
    // 0xa4b1fc: stur            x1, [fp, #-8]
    // 0xa4b200: r0 = RepaintBoundary()
    //     0xa4b200: bl              #0x9dae3c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0xa4b204: ldur            x1, [fp, #-8]
    // 0xa4b208: StoreField: r0->field_b = r1
    //     0xa4b208: stur            w1, [x0, #0xb]
    // 0xa4b20c: ldur            x1, [fp, #-0x10]
    // 0xa4b210: StoreField: r0->field_7 = r1
    //     0xa4b210: stur            w1, [x0, #7]
    // 0xa4b214: LeaveFrame
    //     0xa4b214: mov             SP, fp
    //     0xa4b218: ldp             fp, lr, [SP], #0x10
    // 0xa4b21c: ret
    //     0xa4b21c: ret             
    // 0xa4b220: r9 = _controller
    //     0xa4b220: add             x9, PP, #0x51, lsl #12  ; [pp+0x51500] Field <ScreenshotState._controller@2045074191>: late (offset: 0x1c)
    //     0xa4b224: ldr             x9, [x9, #0x500]
    // 0xa4b228: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4b228: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4b22c: r9 = _containerKey
    //     0xa4b22c: add             x9, PP, #0x40, lsl #12  ; [pp+0x40230] Field <ScreenshotController._containerKey@2045074191>: late (offset: 0x8)
    //     0xa4b230: ldr             x9, [x9, #0x230]
    // 0xa4b234: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4b234: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4b238: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b238: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4698, size: 0x14, field offset: 0xc
//   const constructor, 
class Screenshot extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94cf4, size: 0x2c
    // 0xa94cf4: EnterFrame
    //     0xa94cf4: stp             fp, lr, [SP, #-0x10]!
    //     0xa94cf8: mov             fp, SP
    // 0xa94cfc: mov             x0, x1
    // 0xa94d00: r1 = <Screenshot>
    //     0xa94d00: add             x1, PP, #0x47, lsl #12  ; [pp+0x47ab8] TypeArguments: <Screenshot>
    //     0xa94d04: ldr             x1, [x1, #0xab8]
    // 0xa94d08: r0 = ScreenshotState()
    //     0xa94d08: bl              #0xa94d20  ; AllocateScreenshotStateStub -> ScreenshotState (size=0x20)
    // 0xa94d0c: r1 = Sentinel
    //     0xa94d0c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa94d10: StoreField: r0->field_1b = r1
    //     0xa94d10: stur            w1, [x0, #0x1b]
    // 0xa94d14: LeaveFrame
    //     0xa94d14: mov             SP, fp
    //     0xa94d18: ldp             fp, lr, [SP], #0x10
    // 0xa94d1c: ret
    //     0xa94d1c: ret             
  }
}
