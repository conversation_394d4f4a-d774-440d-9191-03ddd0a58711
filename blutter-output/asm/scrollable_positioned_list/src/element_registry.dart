// lib: , url: package:scrollable_positioned_list/src/element_registry.dart

// class id: 1051106, size: 0x8
class :: {
}

// class id: 4094, size: 0x18, field offset: 0x14
class _RegistryWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xa4b23c, size: 0x50
    // 0xa4b23c: EnterFrame
    //     0xa4b23c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4b240: mov             fp, SP
    // 0xa4b244: AllocStack(0x10)
    //     0xa4b244: sub             SP, SP, #0x10
    // 0xa4b248: SetupParameters(_RegistryWidgetState this /* r1 => r1, fp-0x10 */)
    //     0xa4b248: stur            x1, [fp, #-0x10]
    // 0xa4b24c: LoadField: r0 = r1->field_b
    //     0xa4b24c: ldur            w0, [x1, #0xb]
    // 0xa4b250: DecompressPointer r0
    //     0xa4b250: add             x0, x0, HEAP, lsl #32
    // 0xa4b254: cmp             w0, NULL
    // 0xa4b258: b.eq            #0xa4b288
    // 0xa4b25c: LoadField: r2 = r0->field_b
    //     0xa4b25c: ldur            w2, [x0, #0xb]
    // 0xa4b260: DecompressPointer r2
    //     0xa4b260: add             x2, x2, HEAP, lsl #32
    // 0xa4b264: stur            x2, [fp, #-8]
    // 0xa4b268: r0 = _InheritedRegistryWidget()
    //     0xa4b268: bl              #0xa4b2b0  ; Allocate_InheritedRegistryWidgetStub -> _InheritedRegistryWidget (size=0x14)
    // 0xa4b26c: ldur            x1, [fp, #-0x10]
    // 0xa4b270: StoreField: r0->field_f = r1
    //     0xa4b270: stur            w1, [x0, #0xf]
    // 0xa4b274: ldur            x1, [fp, #-8]
    // 0xa4b278: StoreField: r0->field_b = r1
    //     0xa4b278: stur            w1, [x0, #0xb]
    // 0xa4b27c: LeaveFrame
    //     0xa4b27c: mov             SP, fp
    //     0xa4b280: ldp             fp, lr, [SP], #0x10
    // 0xa4b284: ret
    //     0xa4b284: ret             
    // 0xa4b288: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b288: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _RegistryWidgetState(/* No info */) {
    // ** addr: 0xa94d74, size: 0xc0
    // 0xa94d74: EnterFrame
    //     0xa94d74: stp             fp, lr, [SP, #-0x10]!
    //     0xa94d78: mov             fp, SP
    // 0xa94d7c: AllocStack(0x18)
    //     0xa94d7c: sub             SP, SP, #0x18
    // 0xa94d80: SetupParameters(_RegistryWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xa94d80: stur            x1, [fp, #-8]
    // 0xa94d84: CheckStackOverflow
    //     0xa94d84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa94d88: cmp             SP, x16
    //     0xa94d8c: b.ls            #0xa94e2c
    // 0xa94d90: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xa94d90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa94d94: ldr             x0, [x0, #0x778]
    //     0xa94d98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa94d9c: cmp             w0, w16
    //     0xa94da0: b.ne            #0xa94dac
    //     0xa94da4: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xa94da8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa94dac: r1 = <Element>
    //     0xa94dac: ldr             x1, [PP, #0x2158]  ; [pp+0x2158] TypeArguments: <Element>
    // 0xa94db0: stur            x0, [fp, #-0x10]
    // 0xa94db4: r0 = _Set()
    //     0xa94db4: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xa94db8: mov             x1, x0
    // 0xa94dbc: ldur            x0, [fp, #-0x10]
    // 0xa94dc0: stur            x1, [fp, #-0x18]
    // 0xa94dc4: StoreField: r1->field_1b = r0
    //     0xa94dc4: stur            w0, [x1, #0x1b]
    // 0xa94dc8: StoreField: r1->field_b = rZR
    //     0xa94dc8: stur            wzr, [x1, #0xb]
    // 0xa94dcc: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xa94dcc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa94dd0: ldr             x0, [x0, #0x780]
    //     0xa94dd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa94dd8: cmp             w0, w16
    //     0xa94ddc: b.ne            #0xa94de8
    //     0xa94de0: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xa94de4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa94de8: mov             x1, x0
    // 0xa94dec: ldur            x0, [fp, #-0x18]
    // 0xa94df0: StoreField: r0->field_f = r1
    //     0xa94df0: stur            w1, [x0, #0xf]
    // 0xa94df4: StoreField: r0->field_13 = rZR
    //     0xa94df4: stur            wzr, [x0, #0x13]
    // 0xa94df8: ArrayStore: r0[0] = rZR  ; List_4
    //     0xa94df8: stur            wzr, [x0, #0x17]
    // 0xa94dfc: ldur            x1, [fp, #-8]
    // 0xa94e00: StoreField: r1->field_13 = r0
    //     0xa94e00: stur            w0, [x1, #0x13]
    //     0xa94e04: ldurb           w16, [x1, #-1]
    //     0xa94e08: ldurb           w17, [x0, #-1]
    //     0xa94e0c: and             x16, x17, x16, lsr #2
    //     0xa94e10: tst             x16, HEAP, lsr #32
    //     0xa94e14: b.eq            #0xa94e1c
    //     0xa94e18: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa94e1c: r0 = Null
    //     0xa94e1c: mov             x0, NULL
    // 0xa94e20: LeaveFrame
    //     0xa94e20: mov             SP, fp
    //     0xa94e24: ldp             fp, lr, [SP], #0x10
    // 0xa94e28: ret
    //     0xa94e28: ret             
    // 0xa94e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa94e2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa94e30: b               #0xa94d90
  }
}

// class id: 4398, size: 0x44, field offset: 0x40
class _RegisteredElement extends ProxyElement {

  late _RegistryWidgetState _registryWidgetState; // offset: 0x40

  _ mount(/* No info */) {
    // ** addr: 0x8916ec, size: 0xe0
    // 0x8916ec: EnterFrame
    //     0x8916ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8916f0: mov             fp, SP
    // 0x8916f4: AllocStack(0x18)
    //     0x8916f4: sub             SP, SP, #0x18
    // 0x8916f8: SetupParameters(_RegisteredElement this /* r1 => r0, fp-0x8 */)
    //     0x8916f8: mov             x0, x1
    //     0x8916fc: stur            x1, [fp, #-8]
    // 0x891700: CheckStackOverflow
    //     0x891700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x891704: cmp             SP, x16
    //     0x891708: b.ls            #0x8917bc
    // 0x89170c: mov             x1, x0
    // 0x891710: r0 = mount()
    //     0x891710: bl              #0x89190c  ; [package:flutter/src/widgets/framework.dart] ComponentElement::mount
    // 0x891714: r16 = <_InheritedRegistryWidget>
    //     0x891714: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5af10] TypeArguments: <_InheritedRegistryWidget>
    //     0x891718: ldr             x16, [x16, #0xf10]
    // 0x89171c: ldur            lr, [fp, #-8]
    // 0x891720: stp             lr, x16, [SP]
    // 0x891724: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x891724: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x891728: r0 = dependOnInheritedWidgetOfExactType()
    //     0x891728: bl              #0x6396d8  ; [package:flutter/src/widgets/framework.dart] Element::dependOnInheritedWidgetOfExactType
    // 0x89172c: cmp             w0, NULL
    // 0x891730: b.eq            #0x8917c4
    // 0x891734: LoadField: r1 = r0->field_f
    //     0x891734: ldur            w1, [x0, #0xf]
    // 0x891738: DecompressPointer r1
    //     0x891738: add             x1, x1, HEAP, lsl #32
    // 0x89173c: mov             x0, x1
    // 0x891740: ldur            x3, [fp, #-8]
    // 0x891744: StoreField: r3->field_3f = r0
    //     0x891744: stur            w0, [x3, #0x3f]
    //     0x891748: ldurb           w16, [x3, #-1]
    //     0x89174c: ldurb           w17, [x0, #-1]
    //     0x891750: and             x16, x17, x16, lsr #2
    //     0x891754: tst             x16, HEAP, lsr #32
    //     0x891758: b.eq            #0x891760
    //     0x89175c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x891760: LoadField: r0 = r1->field_13
    //     0x891760: ldur            w0, [x1, #0x13]
    // 0x891764: DecompressPointer r0
    //     0x891764: add             x0, x0, HEAP, lsl #32
    // 0x891768: mov             x1, x0
    // 0x89176c: mov             x2, x3
    // 0x891770: r0 = add()
    //     0x891770: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x891774: ldur            x0, [fp, #-8]
    // 0x891778: LoadField: r1 = r0->field_3f
    //     0x891778: ldur            w1, [x0, #0x3f]
    // 0x89177c: DecompressPointer r1
    //     0x89177c: add             x1, x1, HEAP, lsl #32
    // 0x891780: LoadField: r0 = r1->field_b
    //     0x891780: ldur            w0, [x1, #0xb]
    // 0x891784: DecompressPointer r0
    //     0x891784: add             x0, x0, HEAP, lsl #32
    // 0x891788: cmp             w0, NULL
    // 0x89178c: b.eq            #0x8917c8
    // 0x891790: LoadField: r2 = r0->field_f
    //     0x891790: ldur            w2, [x0, #0xf]
    // 0x891794: DecompressPointer r2
    //     0x891794: add             x2, x2, HEAP, lsl #32
    // 0x891798: LoadField: r0 = r1->field_13
    //     0x891798: ldur            w0, [x1, #0x13]
    // 0x89179c: DecompressPointer r0
    //     0x89179c: add             x0, x0, HEAP, lsl #32
    // 0x8917a0: mov             x1, x2
    // 0x8917a4: mov             x2, x0
    // 0x8917a8: r0 = value=()
    //     0x8917a8: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x8917ac: r0 = Null
    //     0x8917ac: mov             x0, NULL
    // 0x8917b0: LeaveFrame
    //     0x8917b0: mov             SP, fp
    //     0x8917b4: ldp             fp, lr, [SP], #0x10
    // 0x8917b8: ret
    //     0x8917b8: ret             
    // 0x8917bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8917bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8917c0: b               #0x89170c
    // 0x8917c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8917c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8917c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8917c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x89bb38, size: 0xe0
    // 0x89bb38: EnterFrame
    //     0x89bb38: stp             fp, lr, [SP, #-0x10]!
    //     0x89bb3c: mov             fp, SP
    // 0x89bb40: AllocStack(0x18)
    //     0x89bb40: sub             SP, SP, #0x18
    // 0x89bb44: SetupParameters(_RegisteredElement this /* r1 => r0, fp-0x8 */)
    //     0x89bb44: mov             x0, x1
    //     0x89bb48: stur            x1, [fp, #-8]
    // 0x89bb4c: CheckStackOverflow
    //     0x89bb4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89bb50: cmp             SP, x16
    //     0x89bb54: b.ls            #0x89bc08
    // 0x89bb58: mov             x1, x0
    // 0x89bb5c: r0 = markNeedsBuild()
    //     0x89bb5c: bl              #0x898be8  ; [package:flutter/src/widgets/framework.dart] Element::markNeedsBuild
    // 0x89bb60: r16 = <_InheritedRegistryWidget>
    //     0x89bb60: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5af10] TypeArguments: <_InheritedRegistryWidget>
    //     0x89bb64: ldr             x16, [x16, #0xf10]
    // 0x89bb68: ldur            lr, [fp, #-8]
    // 0x89bb6c: stp             lr, x16, [SP]
    // 0x89bb70: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x89bb70: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x89bb74: r0 = dependOnInheritedWidgetOfExactType()
    //     0x89bb74: bl              #0x6396d8  ; [package:flutter/src/widgets/framework.dart] Element::dependOnInheritedWidgetOfExactType
    // 0x89bb78: cmp             w0, NULL
    // 0x89bb7c: b.eq            #0x89bc10
    // 0x89bb80: LoadField: r1 = r0->field_f
    //     0x89bb80: ldur            w1, [x0, #0xf]
    // 0x89bb84: DecompressPointer r1
    //     0x89bb84: add             x1, x1, HEAP, lsl #32
    // 0x89bb88: mov             x0, x1
    // 0x89bb8c: ldur            x3, [fp, #-8]
    // 0x89bb90: StoreField: r3->field_3f = r0
    //     0x89bb90: stur            w0, [x3, #0x3f]
    //     0x89bb94: ldurb           w16, [x3, #-1]
    //     0x89bb98: ldurb           w17, [x0, #-1]
    //     0x89bb9c: and             x16, x17, x16, lsr #2
    //     0x89bba0: tst             x16, HEAP, lsr #32
    //     0x89bba4: b.eq            #0x89bbac
    //     0x89bba8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x89bbac: LoadField: r0 = r1->field_13
    //     0x89bbac: ldur            w0, [x1, #0x13]
    // 0x89bbb0: DecompressPointer r0
    //     0x89bbb0: add             x0, x0, HEAP, lsl #32
    // 0x89bbb4: mov             x1, x0
    // 0x89bbb8: mov             x2, x3
    // 0x89bbbc: r0 = add()
    //     0x89bbbc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x89bbc0: ldur            x0, [fp, #-8]
    // 0x89bbc4: LoadField: r1 = r0->field_3f
    //     0x89bbc4: ldur            w1, [x0, #0x3f]
    // 0x89bbc8: DecompressPointer r1
    //     0x89bbc8: add             x1, x1, HEAP, lsl #32
    // 0x89bbcc: LoadField: r0 = r1->field_b
    //     0x89bbcc: ldur            w0, [x1, #0xb]
    // 0x89bbd0: DecompressPointer r0
    //     0x89bbd0: add             x0, x0, HEAP, lsl #32
    // 0x89bbd4: cmp             w0, NULL
    // 0x89bbd8: b.eq            #0x89bc14
    // 0x89bbdc: LoadField: r2 = r0->field_f
    //     0x89bbdc: ldur            w2, [x0, #0xf]
    // 0x89bbe0: DecompressPointer r2
    //     0x89bbe0: add             x2, x2, HEAP, lsl #32
    // 0x89bbe4: LoadField: r0 = r1->field_13
    //     0x89bbe4: ldur            w0, [x1, #0x13]
    // 0x89bbe8: DecompressPointer r0
    //     0x89bbe8: add             x0, x0, HEAP, lsl #32
    // 0x89bbec: mov             x1, x2
    // 0x89bbf0: mov             x2, x0
    // 0x89bbf4: r0 = value=()
    //     0x89bbf4: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x89bbf8: r0 = Null
    //     0x89bbf8: mov             x0, NULL
    // 0x89bbfc: LeaveFrame
    //     0x89bbfc: mov             SP, fp
    //     0x89bc00: ldp             fp, lr, [SP], #0x10
    // 0x89bc04: ret
    //     0x89bc04: ret             
    // 0x89bc08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89bc08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89bc0c: b               #0x89bb58
    // 0x89bc10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x89bc10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x89bc14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x89bc14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ unmount(/* No info */) {
    // ** addr: 0xccd348, size: 0xac
    // 0xccd348: EnterFrame
    //     0xccd348: stp             fp, lr, [SP, #-0x10]!
    //     0xccd34c: mov             fp, SP
    // 0xccd350: AllocStack(0x8)
    //     0xccd350: sub             SP, SP, #8
    // 0xccd354: SetupParameters(_RegisteredElement this /* r1 => r0, fp-0x8 */)
    //     0xccd354: mov             x0, x1
    //     0xccd358: stur            x1, [fp, #-8]
    // 0xccd35c: CheckStackOverflow
    //     0xccd35c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xccd360: cmp             SP, x16
    //     0xccd364: b.ls            #0xccd3dc
    // 0xccd368: LoadField: r1 = r0->field_3f
    //     0xccd368: ldur            w1, [x0, #0x3f]
    // 0xccd36c: DecompressPointer r1
    //     0xccd36c: add             x1, x1, HEAP, lsl #32
    // 0xccd370: r16 = Sentinel
    //     0xccd370: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xccd374: cmp             w1, w16
    // 0xccd378: b.eq            #0xccd3e4
    // 0xccd37c: LoadField: r2 = r1->field_13
    //     0xccd37c: ldur            w2, [x1, #0x13]
    // 0xccd380: DecompressPointer r2
    //     0xccd380: add             x2, x2, HEAP, lsl #32
    // 0xccd384: mov             x1, x2
    // 0xccd388: mov             x2, x0
    // 0xccd38c: r0 = remove()
    //     0xccd38c: bl              #0xd89c04  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0xccd390: ldur            x0, [fp, #-8]
    // 0xccd394: LoadField: r1 = r0->field_3f
    //     0xccd394: ldur            w1, [x0, #0x3f]
    // 0xccd398: DecompressPointer r1
    //     0xccd398: add             x1, x1, HEAP, lsl #32
    // 0xccd39c: LoadField: r2 = r1->field_b
    //     0xccd39c: ldur            w2, [x1, #0xb]
    // 0xccd3a0: DecompressPointer r2
    //     0xccd3a0: add             x2, x2, HEAP, lsl #32
    // 0xccd3a4: cmp             w2, NULL
    // 0xccd3a8: b.eq            #0xccd3f0
    // 0xccd3ac: LoadField: r3 = r2->field_f
    //     0xccd3ac: ldur            w3, [x2, #0xf]
    // 0xccd3b0: DecompressPointer r3
    //     0xccd3b0: add             x3, x3, HEAP, lsl #32
    // 0xccd3b4: LoadField: r2 = r1->field_13
    //     0xccd3b4: ldur            w2, [x1, #0x13]
    // 0xccd3b8: DecompressPointer r2
    //     0xccd3b8: add             x2, x2, HEAP, lsl #32
    // 0xccd3bc: mov             x1, x3
    // 0xccd3c0: r0 = value=()
    //     0xccd3c0: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xccd3c4: ldur            x1, [fp, #-8]
    // 0xccd3c8: r0 = unmount()
    //     0xccd3c8: bl              #0xcce364  ; [package:flutter/src/widgets/framework.dart] Element::unmount
    // 0xccd3cc: r0 = Null
    //     0xccd3cc: mov             x0, NULL
    // 0xccd3d0: LeaveFrame
    //     0xccd3d0: mov             SP, fp
    //     0xccd3d4: ldp             fp, lr, [SP], #0x10
    // 0xccd3d8: ret
    //     0xccd3d8: ret             
    // 0xccd3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xccd3dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xccd3e0: b               #0xccd368
    // 0xccd3e4: r9 = _registryWidgetState
    //     0xccd3e4: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5af08] Field <_RegisteredElement@2687318784._registryWidgetState@2687318784>: late (offset: 0x40)
    //     0xccd3e8: ldr             x9, [x9, #0xf08]
    // 0xccd3ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xccd3ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xccd3f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xccd3f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4599, size: 0x10, field offset: 0x10
//   const constructor, 
class RegisteredElementWidget extends ProxyWidget {

  _ createElement(/* No info */) {
    // ** addr: 0xbbd968, size: 0x54
    // 0xbbd968: EnterFrame
    //     0xbbd968: stp             fp, lr, [SP, #-0x10]!
    //     0xbbd96c: mov             fp, SP
    // 0xbbd970: AllocStack(0x8)
    //     0xbbd970: sub             SP, SP, #8
    // 0xbbd974: SetupParameters(RegisteredElementWidget this /* r1 => r1, fp-0x8 */)
    //     0xbbd974: stur            x1, [fp, #-8]
    // 0xbbd978: r0 = _RegisteredElement()
    //     0xbbd978: bl              #0xbbd9bc  ; Allocate_RegisteredElementStub -> _RegisteredElement (size=0x44)
    // 0xbbd97c: r1 = Sentinel
    //     0xbbd97c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbd980: StoreField: r0->field_3f = r1
    //     0xbbd980: stur            w1, [x0, #0x3f]
    // 0xbbd984: StoreField: r0->field_13 = r1
    //     0xbbd984: stur            w1, [x0, #0x13]
    // 0xbbd988: r1 = Instance__ElementLifecycle
    //     0xbbd988: add             x1, PP, #0xd, lsl #12  ; [pp+0xda00] Obj!_ElementLifecycle@e343c1
    //     0xbbd98c: ldr             x1, [x1, #0xa00]
    // 0xbbd990: StoreField: r0->field_23 = r1
    //     0xbbd990: stur            w1, [x0, #0x23]
    // 0xbbd994: r1 = false
    //     0xbbd994: add             x1, NULL, #0x30  ; false
    // 0xbbd998: StoreField: r0->field_2f = r1
    //     0xbbd998: stur            w1, [x0, #0x2f]
    // 0xbbd99c: r2 = true
    //     0xbbd99c: add             x2, NULL, #0x20  ; true
    // 0xbbd9a0: StoreField: r0->field_33 = r2
    //     0xbbd9a0: stur            w2, [x0, #0x33]
    // 0xbbd9a4: StoreField: r0->field_37 = r1
    //     0xbbd9a4: stur            w1, [x0, #0x37]
    // 0xbbd9a8: ldur            x1, [fp, #-8]
    // 0xbbd9ac: ArrayStore: r0[0] = r1  ; List_4
    //     0xbbd9ac: stur            w1, [x0, #0x17]
    // 0xbbd9b0: LeaveFrame
    //     0xbbd9b0: mov             SP, fp
    //     0xbbd9b4: ldp             fp, lr, [SP], #0x10
    // 0xbbd9b8: ret
    //     0xbbd9b8: ret             
  }
}

// class id: 4612, size: 0x14, field offset: 0x10
//   const constructor, 
class _InheritedRegistryWidget extends InheritedWidget {
}

// class id: 4697, size: 0x14, field offset: 0xc
//   const constructor, 
class RegistryWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94d2c, size: 0x48
    // 0xa94d2c: EnterFrame
    //     0xa94d2c: stp             fp, lr, [SP, #-0x10]!
    //     0xa94d30: mov             fp, SP
    // 0xa94d34: AllocStack(0x8)
    //     0xa94d34: sub             SP, SP, #8
    // 0xa94d38: CheckStackOverflow
    //     0xa94d38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa94d3c: cmp             SP, x16
    //     0xa94d40: b.ls            #0xa94d6c
    // 0xa94d44: r1 = <RegistryWidget>
    //     0xa94d44: add             x1, PP, #0x57, lsl #12  ; [pp+0x579c8] TypeArguments: <RegistryWidget>
    //     0xa94d48: ldr             x1, [x1, #0x9c8]
    // 0xa94d4c: r0 = _RegistryWidgetState()
    //     0xa94d4c: bl              #0xa94e34  ; Allocate_RegistryWidgetStateStub -> _RegistryWidgetState (size=0x18)
    // 0xa94d50: mov             x1, x0
    // 0xa94d54: stur            x0, [fp, #-8]
    // 0xa94d58: r0 = _RegistryWidgetState()
    //     0xa94d58: bl              #0xa94d74  ; [package:scrollable_positioned_list/src/element_registry.dart] _RegistryWidgetState::_RegistryWidgetState
    // 0xa94d5c: ldur            x0, [fp, #-8]
    // 0xa94d60: LeaveFrame
    //     0xa94d60: mov             SP, fp
    //     0xa94d64: ldp             fp, lr, [SP], #0x10
    // 0xa94d68: ret
    //     0xa94d68: ret             
    // 0xa94d6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa94d6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa94d70: b               #0xa94d44
  }
}
