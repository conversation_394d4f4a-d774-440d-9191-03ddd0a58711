// lib: , url: package:scrollable_positioned_list/src/item_positions_listener.dart

// class id: 1051107, size: 0x8
class :: {
}

// class id: 511, size: 0x20, field offset: 0x8
//   const constructor, 
class ItemPosition extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3480, size: 0x150
    // 0xbf3480: EnterFrame
    //     0xbf3480: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3484: mov             fp, SP
    // 0xbf3488: AllocStack(0x8)
    //     0xbf3488: sub             SP, SP, #8
    // 0xbf348c: CheckStackOverflow
    //     0xbf348c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3490: cmp             SP, x16
    //     0xbf3494: b.ls            #0xbf35c8
    // 0xbf3498: ldr             x2, [fp, #0x10]
    // 0xbf349c: LoadField: r3 = r2->field_7
    //     0xbf349c: ldur            x3, [x2, #7]
    // 0xbf34a0: r0 = BoxInt64Instr(r3)
    //     0xbf34a0: sbfiz           x0, x3, #1, #0x1f
    //     0xbf34a4: cmp             x3, x0, asr #1
    //     0xbf34a8: b.eq            #0xbf34b4
    //     0xbf34ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf34b0: stur            x3, [x0, #7]
    // 0xbf34b4: r1 = 60
    //     0xbf34b4: movz            x1, #0x3c
    // 0xbf34b8: branchIfSmi(r0, 0xbf34c4)
    //     0xbf34b8: tbz             w0, #0, #0xbf34c4
    // 0xbf34bc: r1 = LoadClassIdInstr(r0)
    //     0xbf34bc: ldur            x1, [x0, #-1]
    //     0xbf34c0: ubfx            x1, x1, #0xc, #0x14
    // 0xbf34c4: str             x0, [SP]
    // 0xbf34c8: mov             x0, x1
    // 0xbf34cc: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf34cc: movz            x17, #0x64af
    //     0xbf34d0: add             lr, x0, x17
    //     0xbf34d4: ldr             lr, [x21, lr, lsl #3]
    //     0xbf34d8: blr             lr
    // 0xbf34dc: r2 = LoadInt32Instr(r0)
    //     0xbf34dc: sbfx            x2, x0, #1, #0x1f
    //     0xbf34e0: tbz             w0, #0, #0xbf34e8
    //     0xbf34e4: ldur            x2, [x0, #7]
    // 0xbf34e8: add             x3, x2, #7
    // 0xbf34ec: r16 = 31
    //     0xbf34ec: movz            x16, #0x1f
    // 0xbf34f0: mul             x2, x3, x16
    // 0xbf34f4: ldr             x3, [fp, #0x10]
    // 0xbf34f8: LoadField: d0 = r3->field_f
    //     0xbf34f8: ldur            d0, [x3, #0xf]
    // 0xbf34fc: mov             x16, v0.d[0]
    // 0xbf3500: and             x16, x16, #0x7ff0000000000000
    // 0xbf3504: r17 = 9218868437227405312
    //     0xbf3504: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf3508: cmp             x16, x17
    // 0xbf350c: b.eq            #0xbf353c
    // 0xbf3510: fcvtzs          x16, d0
    // 0xbf3514: scvtf           d1, x16
    // 0xbf3518: fcmp            d1, d0
    // 0xbf351c: b.ne            #0xbf353c
    // 0xbf3520: r17 = 11601
    //     0xbf3520: movz            x17, #0x2d51
    // 0xbf3524: mul             x4, x16, x17
    // 0xbf3528: umulh           x16, x16, x17
    // 0xbf352c: eor             x4, x4, x16
    // 0xbf3530: r4 = 0
    //     0xbf3530: eor             x4, x4, x4, lsr #32
    // 0xbf3534: and             x4, x4, #0x3fffffff
    // 0xbf3538: b               #0xbf3548
    // 0xbf353c: r4 = 0.000000
    //     0xbf353c: fmov            x4, d0
    // 0xbf3540: r4 = 0
    //     0xbf3540: eor             x4, x4, x4, lsr #32
    // 0xbf3544: and             x4, x4, #0x3fffffff
    // 0xbf3548: add             x5, x2, x4
    // 0xbf354c: r16 = 31
    //     0xbf354c: movz            x16, #0x1f
    // 0xbf3550: mul             x2, x5, x16
    // 0xbf3554: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xbf3554: ldur            d0, [x3, #0x17]
    // 0xbf3558: mov             x16, v0.d[0]
    // 0xbf355c: and             x16, x16, #0x7ff0000000000000
    // 0xbf3560: r17 = 9218868437227405312
    //     0xbf3560: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf3564: cmp             x16, x17
    // 0xbf3568: b.eq            #0xbf3598
    // 0xbf356c: fcvtzs          x16, d0
    // 0xbf3570: scvtf           d1, x16
    // 0xbf3574: fcmp            d1, d0
    // 0xbf3578: b.ne            #0xbf3598
    // 0xbf357c: r17 = 11601
    //     0xbf357c: movz            x17, #0x2d51
    // 0xbf3580: mul             x3, x16, x17
    // 0xbf3584: umulh           x16, x16, x17
    // 0xbf3588: eor             x3, x3, x16
    // 0xbf358c: r3 = 0
    //     0xbf358c: eor             x3, x3, x3, lsr #32
    // 0xbf3590: and             x3, x3, #0x3fffffff
    // 0xbf3594: b               #0xbf35a4
    // 0xbf3598: r3 = 0.000000
    //     0xbf3598: fmov            x3, d0
    // 0xbf359c: r3 = 0
    //     0xbf359c: eor             x3, x3, x3, lsr #32
    // 0xbf35a0: and             x3, x3, #0x3fffffff
    // 0xbf35a4: add             x4, x2, x3
    // 0xbf35a8: r0 = BoxInt64Instr(r4)
    //     0xbf35a8: sbfiz           x0, x4, #1, #0x1f
    //     0xbf35ac: cmp             x4, x0, asr #1
    //     0xbf35b0: b.eq            #0xbf35bc
    //     0xbf35b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf35b8: stur            x4, [x0, #7]
    // 0xbf35bc: LeaveFrame
    //     0xbf35bc: mov             SP, fp
    //     0xbf35c0: ldp             fp, lr, [SP], #0x10
    // 0xbf35c4: ret
    //     0xbf35c4: ret             
    // 0xbf35c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf35c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf35cc: b               #0xbf3498
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3f968, size: 0x120
    // 0xc3f968: EnterFrame
    //     0xc3f968: stp             fp, lr, [SP, #-0x10]!
    //     0xc3f96c: mov             fp, SP
    // 0xc3f970: AllocStack(0x8)
    //     0xc3f970: sub             SP, SP, #8
    // 0xc3f974: CheckStackOverflow
    //     0xc3f974: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3f978: cmp             SP, x16
    //     0xc3f97c: b.ls            #0xc3fa50
    // 0xc3f980: r1 = Null
    //     0xc3f980: mov             x1, NULL
    // 0xc3f984: r2 = 14
    //     0xc3f984: movz            x2, #0xe
    // 0xc3f988: r0 = AllocateArray()
    //     0xc3f988: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3f98c: mov             x2, x0
    // 0xc3f990: r16 = "ItemPosition(index: "
    //     0xc3f990: add             x16, PP, #0x57, lsl #12  ; [pp+0x57b98] "ItemPosition(index: "
    //     0xc3f994: ldr             x16, [x16, #0xb98]
    // 0xc3f998: StoreField: r2->field_f = r16
    //     0xc3f998: stur            w16, [x2, #0xf]
    // 0xc3f99c: ldr             x3, [fp, #0x10]
    // 0xc3f9a0: LoadField: r4 = r3->field_7
    //     0xc3f9a0: ldur            x4, [x3, #7]
    // 0xc3f9a4: r0 = BoxInt64Instr(r4)
    //     0xc3f9a4: sbfiz           x0, x4, #1, #0x1f
    //     0xc3f9a8: cmp             x4, x0, asr #1
    //     0xc3f9ac: b.eq            #0xc3f9b8
    //     0xc3f9b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3f9b4: stur            x4, [x0, #7]
    // 0xc3f9b8: StoreField: r2->field_13 = r0
    //     0xc3f9b8: stur            w0, [x2, #0x13]
    // 0xc3f9bc: r16 = ", itemLeadingEdge: "
    //     0xc3f9bc: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ba0] ", itemLeadingEdge: "
    //     0xc3f9c0: ldr             x16, [x16, #0xba0]
    // 0xc3f9c4: ArrayStore: r2[0] = r16  ; List_4
    //     0xc3f9c4: stur            w16, [x2, #0x17]
    // 0xc3f9c8: LoadField: d0 = r3->field_f
    //     0xc3f9c8: ldur            d0, [x3, #0xf]
    // 0xc3f9cc: r0 = inline_Allocate_Double()
    //     0xc3f9cc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc3f9d0: add             x0, x0, #0x10
    //     0xc3f9d4: cmp             x1, x0
    //     0xc3f9d8: b.ls            #0xc3fa58
    //     0xc3f9dc: str             x0, [THR, #0x50]  ; THR::top
    //     0xc3f9e0: sub             x0, x0, #0xf
    //     0xc3f9e4: movz            x1, #0xe15c
    //     0xc3f9e8: movk            x1, #0x3, lsl #16
    //     0xc3f9ec: stur            x1, [x0, #-1]
    // 0xc3f9f0: StoreField: r0->field_7 = d0
    //     0xc3f9f0: stur            d0, [x0, #7]
    // 0xc3f9f4: StoreField: r2->field_1b = r0
    //     0xc3f9f4: stur            w0, [x2, #0x1b]
    // 0xc3f9f8: r16 = ", itemTrailingEdge: "
    //     0xc3f9f8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ba8] ", itemTrailingEdge: "
    //     0xc3f9fc: ldr             x16, [x16, #0xba8]
    // 0xc3fa00: StoreField: r2->field_1f = r16
    //     0xc3fa00: stur            w16, [x2, #0x1f]
    // 0xc3fa04: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc3fa04: ldur            d0, [x3, #0x17]
    // 0xc3fa08: r0 = inline_Allocate_Double()
    //     0xc3fa08: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc3fa0c: add             x0, x0, #0x10
    //     0xc3fa10: cmp             x1, x0
    //     0xc3fa14: b.ls            #0xc3fa70
    //     0xc3fa18: str             x0, [THR, #0x50]  ; THR::top
    //     0xc3fa1c: sub             x0, x0, #0xf
    //     0xc3fa20: movz            x1, #0xe15c
    //     0xc3fa24: movk            x1, #0x3, lsl #16
    //     0xc3fa28: stur            x1, [x0, #-1]
    // 0xc3fa2c: StoreField: r0->field_7 = d0
    //     0xc3fa2c: stur            d0, [x0, #7]
    // 0xc3fa30: StoreField: r2->field_23 = r0
    //     0xc3fa30: stur            w0, [x2, #0x23]
    // 0xc3fa34: r16 = ")"
    //     0xc3fa34: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc3fa38: StoreField: r2->field_27 = r16
    //     0xc3fa38: stur            w16, [x2, #0x27]
    // 0xc3fa3c: str             x2, [SP]
    // 0xc3fa40: r0 = _interpolate()
    //     0xc3fa40: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3fa44: LeaveFrame
    //     0xc3fa44: mov             SP, fp
    //     0xc3fa48: ldp             fp, lr, [SP], #0x10
    // 0xc3fa4c: ret
    //     0xc3fa4c: ret             
    // 0xc3fa50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3fa50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3fa54: b               #0xc3f980
    // 0xc3fa58: SaveReg d0
    //     0xc3fa58: str             q0, [SP, #-0x10]!
    // 0xc3fa5c: stp             x2, x3, [SP, #-0x10]!
    // 0xc3fa60: r0 = AllocateDouble()
    //     0xc3fa60: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3fa64: ldp             x2, x3, [SP], #0x10
    // 0xc3fa68: RestoreReg d0
    //     0xc3fa68: ldr             q0, [SP], #0x10
    // 0xc3fa6c: b               #0xc3f9f0
    // 0xc3fa70: SaveReg d0
    //     0xc3fa70: str             q0, [SP, #-0x10]!
    // 0xc3fa74: SaveReg r2
    //     0xc3fa74: str             x2, [SP, #-8]!
    // 0xc3fa78: r0 = AllocateDouble()
    //     0xc3fa78: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc3fa7c: RestoreReg r2
    //     0xc3fa7c: ldr             x2, [SP], #8
    // 0xc3fa80: RestoreReg d0
    //     0xc3fa80: ldr             q0, [SP], #0x10
    // 0xc3fa84: b               #0xc3fa2c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7d988, size: 0x114
    // 0xd7d988: EnterFrame
    //     0xd7d988: stp             fp, lr, [SP, #-0x10]!
    //     0xd7d98c: mov             fp, SP
    // 0xd7d990: AllocStack(0x10)
    //     0xd7d990: sub             SP, SP, #0x10
    // 0xd7d994: CheckStackOverflow
    //     0xd7d994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7d998: cmp             SP, x16
    //     0xd7d99c: b.ls            #0xd7da94
    // 0xd7d9a0: ldr             x0, [fp, #0x10]
    // 0xd7d9a4: cmp             w0, NULL
    // 0xd7d9a8: b.ne            #0xd7d9bc
    // 0xd7d9ac: r0 = false
    //     0xd7d9ac: add             x0, NULL, #0x30  ; false
    // 0xd7d9b0: LeaveFrame
    //     0xd7d9b0: mov             SP, fp
    //     0xd7d9b4: ldp             fp, lr, [SP], #0x10
    // 0xd7d9b8: ret
    //     0xd7d9b8: ret             
    // 0xd7d9bc: str             x0, [SP]
    // 0xd7d9c0: r0 = runtimeType()
    //     0xd7d9c0: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7d9c4: r1 = LoadClassIdInstr(r0)
    //     0xd7d9c4: ldur            x1, [x0, #-1]
    //     0xd7d9c8: ubfx            x1, x1, #0xc, #0x14
    // 0xd7d9cc: r16 = ItemPosition
    //     0xd7d9cc: add             x16, PP, #0x57, lsl #12  ; [pp+0x57bb0] Type: ItemPosition
    //     0xd7d9d0: ldr             x16, [x16, #0xbb0]
    // 0xd7d9d4: stp             x16, x0, [SP]
    // 0xd7d9d8: mov             x0, x1
    // 0xd7d9dc: mov             lr, x0
    // 0xd7d9e0: ldr             lr, [x21, lr, lsl #3]
    // 0xd7d9e4: blr             lr
    // 0xd7d9e8: tbz             w0, #4, #0xd7d9fc
    // 0xd7d9ec: r0 = false
    //     0xd7d9ec: add             x0, NULL, #0x30  ; false
    // 0xd7d9f0: LeaveFrame
    //     0xd7d9f0: mov             SP, fp
    //     0xd7d9f4: ldp             fp, lr, [SP], #0x10
    // 0xd7d9f8: ret
    //     0xd7d9f8: ret             
    // 0xd7d9fc: ldr             x4, [fp, #0x18]
    // 0xd7da00: ldr             x3, [fp, #0x10]
    // 0xd7da04: mov             x0, x3
    // 0xd7da08: r2 = Null
    //     0xd7da08: mov             x2, NULL
    // 0xd7da0c: r1 = Null
    //     0xd7da0c: mov             x1, NULL
    // 0xd7da10: r4 = 60
    //     0xd7da10: movz            x4, #0x3c
    // 0xd7da14: branchIfSmi(r0, 0xd7da20)
    //     0xd7da14: tbz             w0, #0, #0xd7da20
    // 0xd7da18: r4 = LoadClassIdInstr(r0)
    //     0xd7da18: ldur            x4, [x0, #-1]
    //     0xd7da1c: ubfx            x4, x4, #0xc, #0x14
    // 0xd7da20: cmp             x4, #0x1ff
    // 0xd7da24: b.eq            #0xd7da3c
    // 0xd7da28: r8 = ItemPosition
    //     0xd7da28: add             x8, PP, #0x57, lsl #12  ; [pp+0x57bb0] Type: ItemPosition
    //     0xd7da2c: ldr             x8, [x8, #0xbb0]
    // 0xd7da30: r3 = Null
    //     0xd7da30: add             x3, PP, #0x57, lsl #12  ; [pp+0x57bb8] Null
    //     0xd7da34: ldr             x3, [x3, #0xbb8]
    // 0xd7da38: r0 = ItemPosition()
    //     0xd7da38: bl              #0x83d174  ; IsType_ItemPosition_Stub
    // 0xd7da3c: ldr             x1, [fp, #0x10]
    // 0xd7da40: LoadField: r2 = r1->field_7
    //     0xd7da40: ldur            x2, [x1, #7]
    // 0xd7da44: ldr             x3, [fp, #0x18]
    // 0xd7da48: LoadField: r4 = r3->field_7
    //     0xd7da48: ldur            x4, [x3, #7]
    // 0xd7da4c: cmp             x2, x4
    // 0xd7da50: b.ne            #0xd7da84
    // 0xd7da54: LoadField: d0 = r1->field_f
    //     0xd7da54: ldur            d0, [x1, #0xf]
    // 0xd7da58: LoadField: d1 = r3->field_f
    //     0xd7da58: ldur            d1, [x3, #0xf]
    // 0xd7da5c: fcmp            d0, d1
    // 0xd7da60: b.ne            #0xd7da84
    // 0xd7da64: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xd7da64: ldur            d0, [x1, #0x17]
    // 0xd7da68: ArrayLoad: d1 = r3[0]  ; List_8
    //     0xd7da68: ldur            d1, [x3, #0x17]
    // 0xd7da6c: fcmp            d0, d1
    // 0xd7da70: r16 = true
    //     0xd7da70: add             x16, NULL, #0x20  ; true
    // 0xd7da74: r17 = false
    //     0xd7da74: add             x17, NULL, #0x30  ; false
    // 0xd7da78: csel            x1, x16, x17, eq
    // 0xd7da7c: mov             x0, x1
    // 0xd7da80: b               #0xd7da88
    // 0xd7da84: r0 = false
    //     0xd7da84: add             x0, NULL, #0x30  ; false
    // 0xd7da88: LeaveFrame
    //     0xd7da88: mov             SP, fp
    //     0xd7da8c: ldp             fp, lr, [SP], #0x10
    // 0xd7da90: ret
    //     0xd7da90: ret             
    // 0xd7da94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7da94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7da98: b               #0xd7d9a0
  }
}

// class id: 512, size: 0x8, field offset: 0x8
abstract class ItemPositionsListener extends Object {

  factory _ ItemPositionsListener.create(/* No info */) {
    // ** addr: 0x83d024, size: 0x40
    // 0x83d024: EnterFrame
    //     0x83d024: stp             fp, lr, [SP, #-0x10]!
    //     0x83d028: mov             fp, SP
    // 0x83d02c: AllocStack(0x8)
    //     0x83d02c: sub             SP, SP, #8
    // 0x83d030: CheckStackOverflow
    //     0x83d030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83d034: cmp             SP, x16
    //     0x83d038: b.ls            #0x83d05c
    // 0x83d03c: r0 = ItemPositionsNotifier()
    //     0x83d03c: bl              #0x83d24c  ; AllocateItemPositionsNotifierStub -> ItemPositionsNotifier (size=0xc)
    // 0x83d040: mov             x1, x0
    // 0x83d044: stur            x0, [fp, #-8]
    // 0x83d048: r0 = ItemPositionsNotifier()
    //     0x83d048: bl              #0x83d0b8  ; [package:scrollable_positioned_list/src/item_positions_notifier.dart] ItemPositionsNotifier::ItemPositionsNotifier
    // 0x83d04c: ldur            x0, [fp, #-8]
    // 0x83d050: LeaveFrame
    //     0x83d050: mov             SP, fp
    //     0x83d054: ldp             fp, lr, [SP], #0x10
    // 0x83d058: ret
    //     0x83d058: ret             
    // 0x83d05c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83d05c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83d060: b               #0x83d03c
  }
}
