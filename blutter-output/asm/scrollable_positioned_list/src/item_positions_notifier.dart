// lib: , url: package:scrollable_positioned_list/src/item_positions_notifier.dart

// class id: 1051108, size: 0x8
class :: {
}

// class id: 510, size: 0xc, field offset: 0x8
class ItemPositionsNotifier extends Object
    implements ItemPositionsListener {

  _ ItemPositionsNotifier(/* No info */) {
    // ** addr: 0x83d0b8, size: 0xbc
    // 0x83d0b8: EnterFrame
    //     0x83d0b8: stp             fp, lr, [SP, #-0x10]!
    //     0x83d0bc: mov             fp, SP
    // 0x83d0c0: AllocStack(0x18)
    //     0x83d0c0: sub             SP, SP, #0x18
    // 0x83d0c4: SetupParameters(ItemPositionsNotifier this /* r1 => r0, fp-0x8 */)
    //     0x83d0c4: mov             x0, x1
    //     0x83d0c8: stur            x1, [fp, #-8]
    // 0x83d0cc: CheckStackOverflow
    //     0x83d0cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83d0d0: cmp             SP, x16
    //     0x83d0d4: b.ls            #0x83d16c
    // 0x83d0d8: r1 = <ItemPosition>
    //     0x83d0d8: add             x1, PP, #0x31, lsl #12  ; [pp+0x316e8] TypeArguments: <ItemPosition>
    //     0x83d0dc: ldr             x1, [x1, #0x6e8]
    // 0x83d0e0: r2 = 0
    //     0x83d0e0: movz            x2, #0
    // 0x83d0e4: r0 = _GrowableList()
    //     0x83d0e4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x83d0e8: r1 = <Iterable<ItemPosition>>
    //     0x83d0e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f280] TypeArguments: <Iterable<ItemPosition>>
    //     0x83d0ec: ldr             x1, [x1, #0x280]
    // 0x83d0f0: stur            x0, [fp, #-0x10]
    // 0x83d0f4: r0 = ValueNotifier()
    //     0x83d0f4: bl              #0x65a810  ; AllocateValueNotifierStub -> ValueNotifier<X0> (size=0x2c)
    // 0x83d0f8: mov             x1, x0
    // 0x83d0fc: ldur            x0, [fp, #-0x10]
    // 0x83d100: stur            x1, [fp, #-0x18]
    // 0x83d104: StoreField: r1->field_27 = r0
    //     0x83d104: stur            w0, [x1, #0x27]
    // 0x83d108: StoreField: r1->field_7 = rZR
    //     0x83d108: stur            xzr, [x1, #7]
    // 0x83d10c: StoreField: r1->field_13 = rZR
    //     0x83d10c: stur            xzr, [x1, #0x13]
    // 0x83d110: StoreField: r1->field_1b = rZR
    //     0x83d110: stur            xzr, [x1, #0x1b]
    // 0x83d114: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x83d114: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x83d118: ldr             x0, [x0, #0xca8]
    //     0x83d11c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x83d120: cmp             w0, w16
    //     0x83d124: b.ne            #0x83d130
    //     0x83d128: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0x83d12c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x83d130: mov             x1, x0
    // 0x83d134: ldur            x0, [fp, #-0x18]
    // 0x83d138: StoreField: r0->field_f = r1
    //     0x83d138: stur            w1, [x0, #0xf]
    // 0x83d13c: ldur            x1, [fp, #-8]
    // 0x83d140: StoreField: r1->field_7 = r0
    //     0x83d140: stur            w0, [x1, #7]
    //     0x83d144: ldurb           w16, [x1, #-1]
    //     0x83d148: ldurb           w17, [x0, #-1]
    //     0x83d14c: and             x16, x17, x16, lsr #2
    //     0x83d150: tst             x16, HEAP, lsr #32
    //     0x83d154: b.eq            #0x83d15c
    //     0x83d158: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x83d15c: r0 = Null
    //     0x83d15c: mov             x0, NULL
    // 0x83d160: LeaveFrame
    //     0x83d160: mov             SP, fp
    //     0x83d164: ldp             fp, lr, [SP], #0x10
    // 0x83d168: ret
    //     0x83d168: ret             
    // 0x83d16c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83d16c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83d170: b               #0x83d0d8
  }
}
