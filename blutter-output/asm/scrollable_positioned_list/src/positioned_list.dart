// lib: , url: package:scrollable_positioned_list/src/positioned_list.dart

// class id: 1051109, size: 0x8
class :: {
}

// class id: 4093, size: 0x24, field offset: 0x14
class _PositionedListState extends State<dynamic> {

  late final ScrollController scrollController; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x97eae0, size: 0xcc
    // 0x97eae0: EnterFrame
    //     0x97eae0: stp             fp, lr, [SP, #-0x10]!
    //     0x97eae4: mov             fp, SP
    // 0x97eae8: AllocStack(0x18)
    //     0x97eae8: sub             SP, SP, #0x18
    // 0x97eaec: SetupParameters(_PositionedListState this /* r1 => r2, fp-0x10 */)
    //     0x97eaec: mov             x2, x1
    //     0x97eaf0: stur            x1, [fp, #-0x10]
    // 0x97eaf4: CheckStackOverflow
    //     0x97eaf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97eaf8: cmp             SP, x16
    //     0x97eafc: b.ls            #0x97eba0
    // 0x97eb00: LoadField: r0 = r2->field_b
    //     0x97eb00: ldur            w0, [x2, #0xb]
    // 0x97eb04: DecompressPointer r0
    //     0x97eb04: add             x0, x0, HEAP, lsl #32
    // 0x97eb08: cmp             w0, NULL
    // 0x97eb0c: b.eq            #0x97eba8
    // 0x97eb10: LoadField: r1 = r0->field_1b
    //     0x97eb10: ldur            w1, [x0, #0x1b]
    // 0x97eb14: DecompressPointer r1
    //     0x97eb14: add             x1, x1, HEAP, lsl #32
    // 0x97eb18: stur            x1, [fp, #-8]
    // 0x97eb1c: LoadField: r0 = r2->field_1b
    //     0x97eb1c: ldur            w0, [x2, #0x1b]
    // 0x97eb20: DecompressPointer r0
    //     0x97eb20: add             x0, x0, HEAP, lsl #32
    // 0x97eb24: r16 = Sentinel
    //     0x97eb24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97eb28: cmp             w0, w16
    // 0x97eb2c: b.ne            #0x97eb38
    // 0x97eb30: mov             x3, x2
    // 0x97eb34: b               #0x97eb4c
    // 0x97eb38: r16 = "scrollController"
    //     0x97eb38: add             x16, PP, #0x51, lsl #12  ; [pp+0x51230] "scrollController"
    //     0x97eb3c: ldr             x16, [x16, #0x230]
    // 0x97eb40: str             x16, [SP]
    // 0x97eb44: r0 = _throwFieldAlreadyInitialized()
    //     0x97eb44: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x97eb48: ldur            x3, [fp, #-0x10]
    // 0x97eb4c: ldur            x0, [fp, #-8]
    // 0x97eb50: StoreField: r3->field_1b = r0
    //     0x97eb50: stur            w0, [x3, #0x1b]
    //     0x97eb54: ldurb           w16, [x3, #-1]
    //     0x97eb58: ldurb           w17, [x0, #-1]
    //     0x97eb5c: and             x16, x17, x16, lsr #2
    //     0x97eb60: tst             x16, HEAP, lsr #32
    //     0x97eb64: b.eq            #0x97eb6c
    //     0x97eb68: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x97eb6c: mov             x2, x3
    // 0x97eb70: r1 = Function '_schedulePositionNotificationUpdate@2689248967':.
    //     0x97eb70: add             x1, PP, #0x51, lsl #12  ; [pp+0x51228] AnonymousClosure: (0x97f58c), in [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_schedulePositionNotificationUpdate (0x97ebd0)
    //     0x97eb74: ldr             x1, [x1, #0x228]
    // 0x97eb78: r0 = AllocateClosure()
    //     0x97eb78: bl              #0xec1630  ; AllocateClosureStub
    // 0x97eb7c: ldur            x1, [fp, #-8]
    // 0x97eb80: mov             x2, x0
    // 0x97eb84: r0 = addListener()
    //     0x97eb84: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x97eb88: ldur            x1, [fp, #-0x10]
    // 0x97eb8c: r0 = _schedulePositionNotificationUpdate()
    //     0x97eb8c: bl              #0x97ebd0  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_schedulePositionNotificationUpdate
    // 0x97eb90: r0 = Null
    //     0x97eb90: mov             x0, NULL
    // 0x97eb94: LeaveFrame
    //     0x97eb94: mov             SP, fp
    //     0x97eb98: ldp             fp, lr, [SP], #0x10
    // 0x97eb9c: ret
    //     0x97eb9c: ret             
    // 0x97eba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97eba0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97eba4: b               #0x97eb00
    // 0x97eba8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97eba8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _schedulePositionNotificationUpdate(/* No info */) {
    // ** addr: 0x97ebd0, size: 0x144
    // 0x97ebd0: EnterFrame
    //     0x97ebd0: stp             fp, lr, [SP, #-0x10]!
    //     0x97ebd4: mov             fp, SP
    // 0x97ebd8: AllocStack(0x18)
    //     0x97ebd8: sub             SP, SP, #0x18
    // 0x97ebdc: SetupParameters(_PositionedListState this /* r1 => r1, fp-0x8 */)
    //     0x97ebdc: stur            x1, [fp, #-8]
    // 0x97ebe0: CheckStackOverflow
    //     0x97ebe0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97ebe4: cmp             SP, x16
    //     0x97ebe8: b.ls            #0x97ed08
    // 0x97ebec: r1 = 1
    //     0x97ebec: movz            x1, #0x1
    // 0x97ebf0: r0 = AllocateContext()
    //     0x97ebf0: bl              #0xec126c  ; AllocateContextStub
    // 0x97ebf4: mov             x1, x0
    // 0x97ebf8: ldur            x0, [fp, #-8]
    // 0x97ebfc: StoreField: r1->field_f = r0
    //     0x97ebfc: stur            w0, [x1, #0xf]
    // 0x97ec00: LoadField: r2 = r0->field_1f
    //     0x97ec00: ldur            w2, [x0, #0x1f]
    // 0x97ec04: DecompressPointer r2
    //     0x97ec04: add             x2, x2, HEAP, lsl #32
    // 0x97ec08: tbz             w2, #4, #0x97ecf8
    // 0x97ec0c: r2 = true
    //     0x97ec0c: add             x2, NULL, #0x20  ; true
    // 0x97ec10: StoreField: r0->field_1f = r2
    //     0x97ec10: stur            w2, [x0, #0x1f]
    // 0x97ec14: r0 = LoadStaticField(0x958)
    //     0x97ec14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x97ec18: ldr             x0, [x0, #0x12b0]
    // 0x97ec1c: cmp             w0, NULL
    // 0x97ec20: b.eq            #0x97ed10
    // 0x97ec24: LoadField: r3 = r0->field_53
    //     0x97ec24: ldur            w3, [x0, #0x53]
    // 0x97ec28: DecompressPointer r3
    //     0x97ec28: add             x3, x3, HEAP, lsl #32
    // 0x97ec2c: stur            x3, [fp, #-0x10]
    // 0x97ec30: LoadField: r0 = r3->field_7
    //     0x97ec30: ldur            w0, [x3, #7]
    // 0x97ec34: DecompressPointer r0
    //     0x97ec34: add             x0, x0, HEAP, lsl #32
    // 0x97ec38: mov             x2, x1
    // 0x97ec3c: stur            x0, [fp, #-8]
    // 0x97ec40: r1 = Function '<anonymous closure>':.
    //     0x97ec40: add             x1, PP, #0x51, lsl #12  ; [pp+0x511c0] AnonymousClosure: (0x97ed14), in [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_schedulePositionNotificationUpdate (0x97ebd0)
    //     0x97ec44: ldr             x1, [x1, #0x1c0]
    // 0x97ec48: r0 = AllocateClosure()
    //     0x97ec48: bl              #0xec1630  ; AllocateClosureStub
    // 0x97ec4c: ldur            x2, [fp, #-8]
    // 0x97ec50: mov             x3, x0
    // 0x97ec54: r1 = Null
    //     0x97ec54: mov             x1, NULL
    // 0x97ec58: stur            x3, [fp, #-8]
    // 0x97ec5c: cmp             w2, NULL
    // 0x97ec60: b.eq            #0x97ec80
    // 0x97ec64: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x97ec64: ldur            w4, [x2, #0x17]
    // 0x97ec68: DecompressPointer r4
    //     0x97ec68: add             x4, x4, HEAP, lsl #32
    // 0x97ec6c: r8 = X0
    //     0x97ec6c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x97ec70: LoadField: r9 = r4->field_7
    //     0x97ec70: ldur            x9, [x4, #7]
    // 0x97ec74: r3 = Null
    //     0x97ec74: add             x3, PP, #0x51, lsl #12  ; [pp+0x511c8] Null
    //     0x97ec78: ldr             x3, [x3, #0x1c8]
    // 0x97ec7c: blr             x9
    // 0x97ec80: ldur            x0, [fp, #-0x10]
    // 0x97ec84: LoadField: r1 = r0->field_b
    //     0x97ec84: ldur            w1, [x0, #0xb]
    // 0x97ec88: LoadField: r2 = r0->field_f
    //     0x97ec88: ldur            w2, [x0, #0xf]
    // 0x97ec8c: DecompressPointer r2
    //     0x97ec8c: add             x2, x2, HEAP, lsl #32
    // 0x97ec90: LoadField: r3 = r2->field_b
    //     0x97ec90: ldur            w3, [x2, #0xb]
    // 0x97ec94: r2 = LoadInt32Instr(r1)
    //     0x97ec94: sbfx            x2, x1, #1, #0x1f
    // 0x97ec98: stur            x2, [fp, #-0x18]
    // 0x97ec9c: r1 = LoadInt32Instr(r3)
    //     0x97ec9c: sbfx            x1, x3, #1, #0x1f
    // 0x97eca0: cmp             x2, x1
    // 0x97eca4: b.ne            #0x97ecb0
    // 0x97eca8: mov             x1, x0
    // 0x97ecac: r0 = _growToNextCapacity()
    //     0x97ecac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x97ecb0: ldur            x2, [fp, #-0x10]
    // 0x97ecb4: ldur            x3, [fp, #-0x18]
    // 0x97ecb8: add             x4, x3, #1
    // 0x97ecbc: lsl             x5, x4, #1
    // 0x97ecc0: StoreField: r2->field_b = r5
    //     0x97ecc0: stur            w5, [x2, #0xb]
    // 0x97ecc4: LoadField: r1 = r2->field_f
    //     0x97ecc4: ldur            w1, [x2, #0xf]
    // 0x97ecc8: DecompressPointer r1
    //     0x97ecc8: add             x1, x1, HEAP, lsl #32
    // 0x97eccc: ldur            x0, [fp, #-8]
    // 0x97ecd0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x97ecd0: add             x25, x1, x3, lsl #2
    //     0x97ecd4: add             x25, x25, #0xf
    //     0x97ecd8: str             w0, [x25]
    //     0x97ecdc: tbz             w0, #0, #0x97ecf8
    //     0x97ece0: ldurb           w16, [x1, #-1]
    //     0x97ece4: ldurb           w17, [x0, #-1]
    //     0x97ece8: and             x16, x17, x16, lsr #2
    //     0x97ecec: tst             x16, HEAP, lsr #32
    //     0x97ecf0: b.eq            #0x97ecf8
    //     0x97ecf4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97ecf8: r0 = Null
    //     0x97ecf8: mov             x0, NULL
    // 0x97ecfc: LeaveFrame
    //     0x97ecfc: mov             SP, fp
    //     0x97ed00: ldp             fp, lr, [SP], #0x10
    // 0x97ed04: ret
    //     0x97ed04: ret             
    // 0x97ed08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97ed08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ed0c: b               #0x97ebec
    // 0x97ed10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97ed10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x97ed14, size: 0x7e4
    // 0x97ed14: EnterFrame
    //     0x97ed14: stp             fp, lr, [SP, #-0x10]!
    //     0x97ed18: mov             fp, SP
    // 0x97ed1c: AllocStack(0x60)
    //     0x97ed1c: sub             SP, SP, #0x60
    // 0x97ed20: SetupParameters()
    //     0x97ed20: ldr             x0, [fp, #0x18]
    //     0x97ed24: ldur            w3, [x0, #0x17]
    //     0x97ed28: add             x3, x3, HEAP, lsl #32
    //     0x97ed2c: stur            x3, [fp, #-0x10]
    // 0x97ed30: CheckStackOverflow
    //     0x97ed30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97ed34: cmp             SP, x16
    //     0x97ed38: b.ls            #0x97f480
    // 0x97ed3c: LoadField: r0 = r3->field_f
    //     0x97ed3c: ldur            w0, [x3, #0xf]
    // 0x97ed40: DecompressPointer r0
    //     0x97ed40: add             x0, x0, HEAP, lsl #32
    // 0x97ed44: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97ed44: ldur            w1, [x0, #0x17]
    // 0x97ed48: DecompressPointer r1
    //     0x97ed48: add             x1, x1, HEAP, lsl #32
    // 0x97ed4c: LoadField: r4 = r1->field_27
    //     0x97ed4c: ldur            w4, [x1, #0x27]
    // 0x97ed50: DecompressPointer r4
    //     0x97ed50: add             x4, x4, HEAP, lsl #32
    // 0x97ed54: stur            x4, [fp, #-8]
    // 0x97ed58: cmp             w4, NULL
    // 0x97ed5c: b.ne            #0x97ed78
    // 0x97ed60: r5 = false
    //     0x97ed60: add             x5, NULL, #0x30  ; false
    // 0x97ed64: StoreField: r0->field_1f = r5
    //     0x97ed64: stur            w5, [x0, #0x1f]
    // 0x97ed68: r0 = Null
    //     0x97ed68: mov             x0, NULL
    // 0x97ed6c: LeaveFrame
    //     0x97ed6c: mov             SP, fp
    //     0x97ed70: ldp             fp, lr, [SP], #0x10
    // 0x97ed74: ret
    //     0x97ed74: ret             
    // 0x97ed78: r5 = false
    //     0x97ed78: add             x5, NULL, #0x30  ; false
    // 0x97ed7c: r1 = <ItemPosition>
    //     0x97ed7c: add             x1, PP, #0x31, lsl #12  ; [pp+0x316e8] TypeArguments: <ItemPosition>
    //     0x97ed80: ldr             x1, [x1, #0x6e8]
    // 0x97ed84: r2 = 0
    //     0x97ed84: movz            x2, #0
    // 0x97ed88: r0 = _GrowableList()
    //     0x97ed88: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x97ed8c: ldur            x1, [fp, #-8]
    // 0x97ed90: stur            x0, [fp, #-8]
    // 0x97ed94: r0 = iterator()
    //     0x97ed94: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x97ed98: stur            x0, [fp, #-0x28]
    // 0x97ed9c: LoadField: r2 = r0->field_7
    //     0x97ed9c: ldur            w2, [x0, #7]
    // 0x97eda0: DecompressPointer r2
    //     0x97eda0: add             x2, x2, HEAP, lsl #32
    // 0x97eda4: stur            x2, [fp, #-0x20]
    // 0x97eda8: ldur            x3, [fp, #-8]
    // 0x97edac: r5 = Null
    //     0x97edac: mov             x5, NULL
    // 0x97edb0: ldur            x4, [fp, #-0x10]
    // 0x97edb4: stur            x5, [fp, #-0x18]
    // 0x97edb8: CheckStackOverflow
    //     0x97edb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97edbc: cmp             SP, x16
    //     0x97edc0: b.ls            #0x97f488
    // 0x97edc4: mov             x1, x0
    // 0x97edc8: r0 = moveNext()
    //     0x97edc8: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x97edcc: tbnz            w0, #4, #0x97f2bc
    // 0x97edd0: ldur            x3, [fp, #-0x28]
    // 0x97edd4: LoadField: r4 = r3->field_33
    //     0x97edd4: ldur            w4, [x3, #0x33]
    // 0x97edd8: DecompressPointer r4
    //     0x97edd8: add             x4, x4, HEAP, lsl #32
    // 0x97eddc: stur            x4, [fp, #-0x30]
    // 0x97ede0: cmp             w4, NULL
    // 0x97ede4: b.ne            #0x97ee18
    // 0x97ede8: mov             x0, x4
    // 0x97edec: ldur            x2, [fp, #-0x20]
    // 0x97edf0: r1 = Null
    //     0x97edf0: mov             x1, NULL
    // 0x97edf4: cmp             w2, NULL
    // 0x97edf8: b.eq            #0x97ee18
    // 0x97edfc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x97edfc: ldur            w4, [x2, #0x17]
    // 0x97ee00: DecompressPointer r4
    //     0x97ee00: add             x4, x4, HEAP, lsl #32
    // 0x97ee04: r8 = X0
    //     0x97ee04: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x97ee08: LoadField: r9 = r4->field_7
    //     0x97ee08: ldur            x9, [x4, #7]
    // 0x97ee0c: r3 = Null
    //     0x97ee0c: add             x3, PP, #0x51, lsl #12  ; [pp+0x511d8] Null
    //     0x97ee10: ldr             x3, [x3, #0x1d8]
    // 0x97ee14: blr             x9
    // 0x97ee18: ldur            x3, [fp, #-0x18]
    // 0x97ee1c: ldur            x2, [fp, #-0x30]
    // 0x97ee20: r0 = LoadClassIdInstr(r2)
    //     0x97ee20: ldur            x0, [x2, #-1]
    //     0x97ee24: ubfx            x0, x0, #0xc, #0x14
    // 0x97ee28: mov             x1, x2
    // 0x97ee2c: r0 = GDT[cid_x0 + 0xe16]()
    //     0x97ee2c: add             lr, x0, #0xe16
    //     0x97ee30: ldr             lr, [x21, lr, lsl #3]
    //     0x97ee34: blr             lr
    // 0x97ee38: mov             x3, x0
    // 0x97ee3c: r2 = Null
    //     0x97ee3c: mov             x2, NULL
    // 0x97ee40: r1 = Null
    //     0x97ee40: mov             x1, NULL
    // 0x97ee44: stur            x3, [fp, #-0x38]
    // 0x97ee48: r4 = LoadClassIdInstr(r0)
    //     0x97ee48: ldur            x4, [x0, #-1]
    //     0x97ee4c: ubfx            x4, x4, #0xc, #0x14
    // 0x97ee50: sub             x4, x4, #0xbba
    // 0x97ee54: cmp             x4, #0x9a
    // 0x97ee58: b.ls            #0x97ee6c
    // 0x97ee5c: r8 = RenderBox
    //     0x97ee5c: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x97ee60: r3 = Null
    //     0x97ee60: add             x3, PP, #0x51, lsl #12  ; [pp+0x511e8] Null
    //     0x97ee64: ldr             x3, [x3, #0x1e8]
    // 0x97ee68: r0 = RenderBox()
    //     0x97ee68: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x97ee6c: ldur            x0, [fp, #-0x18]
    // 0x97ee70: cmp             w0, NULL
    // 0x97ee74: b.ne            #0x97eecc
    // 0x97ee78: ldur            x1, [fp, #-0x38]
    // 0x97ee7c: r0 = maybeOf()
    //     0x97ee7c: bl              #0x6a6bc0  ; [package:flutter/src/rendering/viewport.dart] RenderAbstractViewport::maybeOf
    // 0x97ee80: mov             x3, x0
    // 0x97ee84: stur            x3, [fp, #-0x40]
    // 0x97ee88: cmp             w3, NULL
    // 0x97ee8c: b.eq            #0x97f490
    // 0x97ee90: mov             x0, x3
    // 0x97ee94: r2 = Null
    //     0x97ee94: mov             x2, NULL
    // 0x97ee98: r1 = Null
    //     0x97ee98: mov             x1, NULL
    // 0x97ee9c: r4 = LoadClassIdInstr(r0)
    //     0x97ee9c: ldur            x4, [x0, #-1]
    //     0x97eea0: ubfx            x4, x4, #0xc, #0x14
    // 0x97eea4: sub             x4, x4, #0xbcc
    // 0x97eea8: cmp             x4, #4
    // 0x97eeac: b.ls            #0x97eec4
    // 0x97eeb0: r8 = RenderViewportBase<ContainerParentDataMixin<RenderSliver>>?
    //     0x97eeb0: add             x8, PP, #0x51, lsl #12  ; [pp+0x511f8] Type: RenderViewportBase<ContainerParentDataMixin<RenderSliver>>?
    //     0x97eeb4: ldr             x8, [x8, #0x1f8]
    // 0x97eeb8: r3 = Null
    //     0x97eeb8: add             x3, PP, #0x51, lsl #12  ; [pp+0x51200] Null
    //     0x97eebc: ldr             x3, [x3, #0x200]
    // 0x97eec0: r0 = DefaultNullableTypeTest()
    //     0x97eec0: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x97eec4: ldur            x2, [fp, #-0x40]
    // 0x97eec8: b               #0x97eed0
    // 0x97eecc: mov             x2, x0
    // 0x97eed0: stur            x2, [fp, #-0x18]
    // 0x97eed4: r0 = LoadClassIdInstr(r2)
    //     0x97eed4: ldur            x0, [x2, #-1]
    //     0x97eed8: ubfx            x0, x0, #0xc, #0x14
    // 0x97eedc: sub             x16, x0, #0xbce
    // 0x97eee0: cmp             x16, #2
    // 0x97eee4: b.hi            #0x97ef08
    // 0x97eee8: cmp             x0, #0xbce
    // 0x97eeec: b.eq            #0x97ef00
    // 0x97eef0: cmp             x0, #0xbcf
    // 0x97eef4: b.ne            #0x97ef00
    // 0x97eef8: LoadField: d0 = r2->field_a7
    //     0x97eef8: ldur            d0, [x2, #0xa7]
    // 0x97eefc: b               #0x97ef0c
    // 0x97ef00: LoadField: d0 = r2->field_8f
    //     0x97ef00: ldur            d0, [x2, #0x8f]
    // 0x97ef04: b               #0x97ef0c
    // 0x97ef08: d0 = 0.000000
    //     0x97ef08: eor             v0.16b, v0.16b, v0.16b
    // 0x97ef0c: cmp             x0, #0xbcc
    // 0x97ef10: b.ne            #0x97ef20
    // 0x97ef14: LoadField: r0 = r2->field_97
    //     0x97ef14: ldur            w0, [x2, #0x97]
    // 0x97ef18: DecompressPointer r0
    //     0x97ef18: add             x0, x0, HEAP, lsl #32
    // 0x97ef1c: LoadField: d0 = r0->field_7
    //     0x97ef1c: ldur            d0, [x0, #7]
    // 0x97ef20: ldur            x3, [fp, #-0x38]
    // 0x97ef24: ldur            x1, [fp, #-0x30]
    // 0x97ef28: stur            d0, [fp, #-0x50]
    // 0x97ef2c: r0 = LoadClassIdInstr(r1)
    //     0x97ef2c: ldur            x0, [x1, #-1]
    //     0x97ef30: ubfx            x0, x0, #0xc, #0x14
    // 0x97ef34: r0 = GDT[cid_x0 + 0xee4]()
    //     0x97ef34: add             lr, x0, #0xee4
    //     0x97ef38: ldr             lr, [x21, lr, lsl #3]
    //     0x97ef3c: blr             lr
    // 0x97ef40: r1 = LoadClassIdInstr(r0)
    //     0x97ef40: ldur            x1, [x0, #-1]
    //     0x97ef44: ubfx            x1, x1, #0xc, #0x14
    // 0x97ef48: mov             x16, x0
    // 0x97ef4c: mov             x0, x1
    // 0x97ef50: mov             x1, x16
    // 0x97ef54: r0 = GDT[cid_x0 + 0xaa0c]()
    //     0x97ef54: movz            x17, #0xaa0c
    //     0x97ef58: add             lr, x0, x17
    //     0x97ef5c: ldr             lr, [x21, lr, lsl #3]
    //     0x97ef60: blr             lr
    // 0x97ef64: mov             x3, x0
    // 0x97ef68: r2 = Null
    //     0x97ef68: mov             x2, NULL
    // 0x97ef6c: r1 = Null
    //     0x97ef6c: mov             x1, NULL
    // 0x97ef70: stur            x3, [fp, #-0x30]
    // 0x97ef74: r8 = ValueKey<int>
    //     0x97ef74: add             x8, PP, #0x51, lsl #12  ; [pp+0x51210] Type: ValueKey<int>
    //     0x97ef78: ldr             x8, [x8, #0x210]
    // 0x97ef7c: r3 = Null
    //     0x97ef7c: add             x3, PP, #0x51, lsl #12  ; [pp+0x51218] Null
    //     0x97ef80: ldr             x3, [x3, #0x218]
    // 0x97ef84: r0 = ValueKey<int>()
    //     0x97ef84: bl              #0x97f504  ; IsType_ValueKey<int>_Stub
    // 0x97ef88: ldur            x0, [fp, #-0x38]
    // 0x97ef8c: LoadField: r1 = r0->field_53
    //     0x97ef8c: ldur            w1, [x0, #0x53]
    // 0x97ef90: DecompressPointer r1
    //     0x97ef90: add             x1, x1, HEAP, lsl #32
    // 0x97ef94: cmp             w1, NULL
    // 0x97ef98: b.ne            #0x97efa4
    // 0x97ef9c: ldur            x2, [fp, #-8]
    // 0x97efa0: b               #0x97f2a8
    // 0x97efa4: ldur            x3, [fp, #-0x10]
    // 0x97efa8: LoadField: r1 = r3->field_f
    //     0x97efa8: ldur            w1, [x3, #0xf]
    // 0x97efac: DecompressPointer r1
    //     0x97efac: add             x1, x1, HEAP, lsl #32
    // 0x97efb0: LoadField: r2 = r1->field_b
    //     0x97efb0: ldur            w2, [x1, #0xb]
    // 0x97efb4: DecompressPointer r2
    //     0x97efb4: add             x2, x2, HEAP, lsl #32
    // 0x97efb8: cmp             w2, NULL
    // 0x97efbc: b.eq            #0x97f494
    // 0x97efc0: ldur            x1, [fp, #-0x18]
    // 0x97efc4: mov             x2, x0
    // 0x97efc8: d0 = 0.000000
    //     0x97efc8: eor             v0.16b, v0.16b, v0.16b
    // 0x97efcc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x97efcc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x97efd0: r0 = getOffsetToReveal()
    //     0x97efd0: bl              #0xda8a78  ; [package:flutter/src/rendering/viewport.dart] RenderViewportBase::getOffsetToReveal
    // 0x97efd4: LoadField: d0 = r0->field_7
    //     0x97efd4: ldur            d0, [x0, #7]
    // 0x97efd8: mov             x0, v0.d[0]
    // 0x97efdc: and             x0, x0, #0x7fffffffffffffff
    // 0x97efe0: r17 = 9218868437227405312
    //     0x97efe0: orr             x17, xzr, #0x7ff0000000000000
    // 0x97efe4: cmp             x0, x17
    // 0x97efe8: b.eq            #0x97f2a4
    // 0x97efec: fcmp            d0, d0
    // 0x97eff0: b.vc            #0x97effc
    // 0x97eff4: ldur            x2, [fp, #-8]
    // 0x97eff8: b               #0x97f2a8
    // 0x97effc: ldur            x5, [fp, #-0x18]
    // 0x97f000: LoadField: r0 = r5->field_73
    //     0x97f000: ldur            w0, [x5, #0x73]
    // 0x97f004: DecompressPointer r0
    //     0x97f004: add             x0, x0, HEAP, lsl #32
    // 0x97f008: LoadField: r1 = r0->field_3f
    //     0x97f008: ldur            w1, [x0, #0x3f]
    // 0x97f00c: DecompressPointer r1
    //     0x97f00c: add             x1, x1, HEAP, lsl #32
    // 0x97f010: cmp             w1, NULL
    // 0x97f014: b.eq            #0x97f498
    // 0x97f018: LoadField: d1 = r1->field_7
    //     0x97f018: ldur            d1, [x1, #7]
    // 0x97f01c: fsub            d2, d0, d1
    // 0x97f020: LoadField: r0 = r5->field_53
    //     0x97f020: ldur            w0, [x5, #0x53]
    // 0x97f024: DecompressPointer r0
    //     0x97f024: add             x0, x0, HEAP, lsl #32
    // 0x97f028: cmp             w0, NULL
    // 0x97f02c: b.eq            #0x97f3cc
    // 0x97f030: ldur            x2, [fp, #-0x10]
    // 0x97f034: ldur            x1, [fp, #-0x38]
    // 0x97f038: ldur            d0, [fp, #-0x50]
    // 0x97f03c: ldur            x3, [fp, #-0x30]
    // 0x97f040: LoadField: d1 = r0->field_f
    //     0x97f040: ldur            d1, [x0, #0xf]
    // 0x97f044: fmul            d3, d0, d1
    // 0x97f048: fadd            d1, d2, d3
    // 0x97f04c: stur            d1, [fp, #-0x50]
    // 0x97f050: LoadField: r0 = r3->field_b
    //     0x97f050: ldur            w0, [x3, #0xb]
    // 0x97f054: DecompressPointer r0
    //     0x97f054: add             x0, x0, HEAP, lsl #32
    // 0x97f058: mov             v0.16b, v1.16b
    // 0x97f05c: stur            x0, [fp, #-0x40]
    // 0x97f060: stp             fp, lr, [SP, #-0x10]!
    // 0x97f064: mov             fp, SP
    // 0x97f068: CallRuntime_LibcRound(double) -> double
    //     0x97f068: and             SP, SP, #0xfffffffffffffff0
    //     0x97f06c: mov             sp, SP
    //     0x97f070: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x97f074: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x97f078: blr             x16
    //     0x97f07c: movz            x16, #0x8
    //     0x97f080: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x97f084: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x97f088: sub             sp, x16, #1, lsl #12
    //     0x97f08c: mov             SP, fp
    //     0x97f090: ldp             fp, lr, [SP], #0x10
    // 0x97f094: fcmp            d0, d0
    // 0x97f098: b.vs            #0x97f49c
    // 0x97f09c: fcvtzs          x0, d0
    // 0x97f0a0: asr             x16, x0, #0x1e
    // 0x97f0a4: cmp             x16, x0, asr #63
    // 0x97f0a8: b.ne            #0x97f49c
    // 0x97f0ac: lsl             x0, x0, #1
    // 0x97f0b0: ldur            x2, [fp, #-0x10]
    // 0x97f0b4: stur            x0, [fp, #-0x30]
    // 0x97f0b8: LoadField: r1 = r2->field_f
    //     0x97f0b8: ldur            w1, [x2, #0xf]
    // 0x97f0bc: DecompressPointer r1
    //     0x97f0bc: add             x1, x1, HEAP, lsl #32
    // 0x97f0c0: LoadField: r3 = r1->field_1b
    //     0x97f0c0: ldur            w3, [x1, #0x1b]
    // 0x97f0c4: DecompressPointer r3
    //     0x97f0c4: add             x3, x3, HEAP, lsl #32
    // 0x97f0c8: r16 = Sentinel
    //     0x97f0c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97f0cc: cmp             w3, w16
    // 0x97f0d0: b.eq            #0x97f4b8
    // 0x97f0d4: LoadField: r1 = r3->field_3b
    //     0x97f0d4: ldur            w1, [x3, #0x3b]
    // 0x97f0d8: DecompressPointer r1
    //     0x97f0d8: add             x1, x1, HEAP, lsl #32
    // 0x97f0dc: r0 = single()
    //     0x97f0dc: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x97f0e0: LoadField: r1 = r0->field_43
    //     0x97f0e0: ldur            w1, [x0, #0x43]
    // 0x97f0e4: DecompressPointer r1
    //     0x97f0e4: add             x1, x1, HEAP, lsl #32
    // 0x97f0e8: cmp             w1, NULL
    // 0x97f0ec: b.eq            #0x97f4c4
    // 0x97f0f0: ldur            x0, [fp, #-0x30]
    // 0x97f0f4: r2 = LoadInt32Instr(r0)
    //     0x97f0f4: sbfx            x2, x0, #1, #0x1f
    //     0x97f0f8: tbz             w0, #0, #0x97f100
    //     0x97f0fc: ldur            x2, [x0, #7]
    // 0x97f100: scvtf           d0, x2
    // 0x97f104: LoadField: d1 = r1->field_7
    //     0x97f104: ldur            d1, [x1, #7]
    // 0x97f108: fdiv            d2, d0, d1
    // 0x97f10c: ldur            x0, [fp, #-0x38]
    // 0x97f110: stur            d2, [fp, #-0x58]
    // 0x97f114: LoadField: r1 = r0->field_53
    //     0x97f114: ldur            w1, [x0, #0x53]
    // 0x97f118: DecompressPointer r1
    //     0x97f118: add             x1, x1, HEAP, lsl #32
    // 0x97f11c: cmp             w1, NULL
    // 0x97f120: b.eq            #0x97f318
    // 0x97f124: ldur            x0, [fp, #-0x10]
    // 0x97f128: ldur            x3, [fp, #-8]
    // 0x97f12c: ldur            d0, [fp, #-0x50]
    // 0x97f130: ldur            x2, [fp, #-0x40]
    // 0x97f134: LoadField: d1 = r1->field_f
    //     0x97f134: ldur            d1, [x1, #0xf]
    // 0x97f138: fadd            d3, d0, d1
    // 0x97f13c: mov             v0.16b, v3.16b
    // 0x97f140: stp             fp, lr, [SP, #-0x10]!
    // 0x97f144: mov             fp, SP
    // 0x97f148: CallRuntime_LibcRound(double) -> double
    //     0x97f148: and             SP, SP, #0xfffffffffffffff0
    //     0x97f14c: mov             sp, SP
    //     0x97f150: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x97f154: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x97f158: blr             x16
    //     0x97f15c: movz            x16, #0x8
    //     0x97f160: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x97f164: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x97f168: sub             sp, x16, #1, lsl #12
    //     0x97f16c: mov             SP, fp
    //     0x97f170: ldp             fp, lr, [SP], #0x10
    // 0x97f174: fcmp            d0, d0
    // 0x97f178: b.vs            #0x97f4c8
    // 0x97f17c: fcvtzs          x0, d0
    // 0x97f180: asr             x16, x0, #0x1e
    // 0x97f184: cmp             x16, x0, asr #63
    // 0x97f188: b.ne            #0x97f4c8
    // 0x97f18c: lsl             x0, x0, #1
    // 0x97f190: ldur            x2, [fp, #-0x10]
    // 0x97f194: stur            x0, [fp, #-0x30]
    // 0x97f198: LoadField: r1 = r2->field_f
    //     0x97f198: ldur            w1, [x2, #0xf]
    // 0x97f19c: DecompressPointer r1
    //     0x97f19c: add             x1, x1, HEAP, lsl #32
    // 0x97f1a0: LoadField: r3 = r1->field_1b
    //     0x97f1a0: ldur            w3, [x1, #0x1b]
    // 0x97f1a4: DecompressPointer r3
    //     0x97f1a4: add             x3, x3, HEAP, lsl #32
    // 0x97f1a8: r16 = Sentinel
    //     0x97f1a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97f1ac: cmp             w3, w16
    // 0x97f1b0: b.eq            #0x97f4e4
    // 0x97f1b4: LoadField: r1 = r3->field_3b
    //     0x97f1b4: ldur            w1, [x3, #0x3b]
    // 0x97f1b8: DecompressPointer r1
    //     0x97f1b8: add             x1, x1, HEAP, lsl #32
    // 0x97f1bc: r0 = single()
    //     0x97f1bc: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x97f1c0: LoadField: r1 = r0->field_43
    //     0x97f1c0: ldur            w1, [x0, #0x43]
    // 0x97f1c4: DecompressPointer r1
    //     0x97f1c4: add             x1, x1, HEAP, lsl #32
    // 0x97f1c8: cmp             w1, NULL
    // 0x97f1cc: b.eq            #0x97f4f0
    // 0x97f1d0: ldur            x0, [fp, #-0x30]
    // 0x97f1d4: r2 = LoadInt32Instr(r0)
    //     0x97f1d4: sbfx            x2, x0, #1, #0x1f
    //     0x97f1d8: tbz             w0, #0, #0x97f1e0
    //     0x97f1dc: ldur            x2, [x0, #7]
    // 0x97f1e0: scvtf           d0, x2
    // 0x97f1e4: LoadField: d1 = r1->field_7
    //     0x97f1e4: ldur            d1, [x1, #7]
    // 0x97f1e8: fdiv            d2, d0, d1
    // 0x97f1ec: ldur            x0, [fp, #-0x40]
    // 0x97f1f0: stur            d2, [fp, #-0x50]
    // 0x97f1f4: r1 = LoadInt32Instr(r0)
    //     0x97f1f4: sbfx            x1, x0, #1, #0x1f
    //     0x97f1f8: tbz             w0, #0, #0x97f200
    //     0x97f1fc: ldur            x1, [x0, #7]
    // 0x97f200: stur            x1, [fp, #-0x48]
    // 0x97f204: r0 = ItemPosition()
    //     0x97f204: bl              #0x97f4f8  ; AllocateItemPositionStub -> ItemPosition (size=0x20)
    // 0x97f208: mov             x2, x0
    // 0x97f20c: ldur            x0, [fp, #-0x48]
    // 0x97f210: stur            x2, [fp, #-0x30]
    // 0x97f214: StoreField: r2->field_7 = r0
    //     0x97f214: stur            x0, [x2, #7]
    // 0x97f218: ldur            d0, [fp, #-0x58]
    // 0x97f21c: StoreField: r2->field_f = d0
    //     0x97f21c: stur            d0, [x2, #0xf]
    // 0x97f220: ldur            d0, [fp, #-0x50]
    // 0x97f224: ArrayStore: r2[0] = d0  ; List_8
    //     0x97f224: stur            d0, [x2, #0x17]
    // 0x97f228: ldur            x0, [fp, #-8]
    // 0x97f22c: LoadField: r1 = r0->field_b
    //     0x97f22c: ldur            w1, [x0, #0xb]
    // 0x97f230: LoadField: r3 = r0->field_f
    //     0x97f230: ldur            w3, [x0, #0xf]
    // 0x97f234: DecompressPointer r3
    //     0x97f234: add             x3, x3, HEAP, lsl #32
    // 0x97f238: LoadField: r4 = r3->field_b
    //     0x97f238: ldur            w4, [x3, #0xb]
    // 0x97f23c: r3 = LoadInt32Instr(r1)
    //     0x97f23c: sbfx            x3, x1, #1, #0x1f
    // 0x97f240: stur            x3, [fp, #-0x48]
    // 0x97f244: r1 = LoadInt32Instr(r4)
    //     0x97f244: sbfx            x1, x4, #1, #0x1f
    // 0x97f248: cmp             x3, x1
    // 0x97f24c: b.ne            #0x97f258
    // 0x97f250: mov             x1, x0
    // 0x97f254: r0 = _growToNextCapacity()
    //     0x97f254: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x97f258: ldur            x2, [fp, #-8]
    // 0x97f25c: ldur            x3, [fp, #-0x48]
    // 0x97f260: add             x0, x3, #1
    // 0x97f264: lsl             x1, x0, #1
    // 0x97f268: StoreField: r2->field_b = r1
    //     0x97f268: stur            w1, [x2, #0xb]
    // 0x97f26c: LoadField: r1 = r2->field_f
    //     0x97f26c: ldur            w1, [x2, #0xf]
    // 0x97f270: DecompressPointer r1
    //     0x97f270: add             x1, x1, HEAP, lsl #32
    // 0x97f274: ldur            x0, [fp, #-0x30]
    // 0x97f278: ArrayStore: r1[r3] = r0  ; List_4
    //     0x97f278: add             x25, x1, x3, lsl #2
    //     0x97f27c: add             x25, x25, #0xf
    //     0x97f280: str             w0, [x25]
    //     0x97f284: tbz             w0, #0, #0x97f2a0
    //     0x97f288: ldurb           w16, [x1, #-1]
    //     0x97f28c: ldurb           w17, [x0, #-1]
    //     0x97f290: and             x16, x17, x16, lsr #2
    //     0x97f294: tst             x16, HEAP, lsr #32
    //     0x97f298: b.eq            #0x97f2a0
    //     0x97f29c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97f2a0: b               #0x97f2a8
    // 0x97f2a4: ldur            x2, [fp, #-8]
    // 0x97f2a8: ldur            x5, [fp, #-0x18]
    // 0x97f2ac: mov             x3, x2
    // 0x97f2b0: ldur            x0, [fp, #-0x28]
    // 0x97f2b4: ldur            x2, [fp, #-0x20]
    // 0x97f2b8: b               #0x97edb0
    // 0x97f2bc: ldur            x0, [fp, #-0x10]
    // 0x97f2c0: ldur            x2, [fp, #-8]
    // 0x97f2c4: LoadField: r1 = r0->field_f
    //     0x97f2c4: ldur            w1, [x0, #0xf]
    // 0x97f2c8: DecompressPointer r1
    //     0x97f2c8: add             x1, x1, HEAP, lsl #32
    // 0x97f2cc: LoadField: r3 = r1->field_b
    //     0x97f2cc: ldur            w3, [x1, #0xb]
    // 0x97f2d0: DecompressPointer r3
    //     0x97f2d0: add             x3, x3, HEAP, lsl #32
    // 0x97f2d4: cmp             w3, NULL
    // 0x97f2d8: b.eq            #0x97f4f4
    // 0x97f2dc: LoadField: r1 = r3->field_1f
    //     0x97f2dc: ldur            w1, [x3, #0x1f]
    // 0x97f2e0: DecompressPointer r1
    //     0x97f2e0: add             x1, x1, HEAP, lsl #32
    // 0x97f2e4: LoadField: r3 = r1->field_7
    //     0x97f2e4: ldur            w3, [x1, #7]
    // 0x97f2e8: DecompressPointer r3
    //     0x97f2e8: add             x3, x3, HEAP, lsl #32
    // 0x97f2ec: mov             x1, x3
    // 0x97f2f0: r0 = value=()
    //     0x97f2f0: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x97f2f4: ldur            x0, [fp, #-0x10]
    // 0x97f2f8: LoadField: r1 = r0->field_f
    //     0x97f2f8: ldur            w1, [x0, #0xf]
    // 0x97f2fc: DecompressPointer r1
    //     0x97f2fc: add             x1, x1, HEAP, lsl #32
    // 0x97f300: r0 = false
    //     0x97f300: add             x0, NULL, #0x30  ; false
    // 0x97f304: StoreField: r1->field_1f = r0
    //     0x97f304: stur            w0, [x1, #0x1f]
    // 0x97f308: r0 = Null
    //     0x97f308: mov             x0, NULL
    // 0x97f30c: LeaveFrame
    //     0x97f30c: mov             SP, fp
    //     0x97f310: ldp             fp, lr, [SP], #0x10
    // 0x97f314: ret
    //     0x97f314: ret             
    // 0x97f318: r1 = Null
    //     0x97f318: mov             x1, NULL
    // 0x97f31c: r2 = 8
    //     0x97f31c: movz            x2, #0x8
    // 0x97f320: r0 = AllocateArray()
    //     0x97f320: bl              #0xec22fc  ; AllocateArrayStub
    // 0x97f324: stur            x0, [fp, #-8]
    // 0x97f328: r16 = "RenderBox was not laid out: "
    //     0x97f328: ldr             x16, [PP, #0x44c0]  ; [pp+0x44c0] "RenderBox was not laid out: "
    // 0x97f32c: StoreField: r0->field_f = r16
    //     0x97f32c: stur            w16, [x0, #0xf]
    // 0x97f330: ldur            x16, [fp, #-0x38]
    // 0x97f334: str             x16, [SP]
    // 0x97f338: r0 = runtimeType()
    //     0x97f338: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x97f33c: ldur            x1, [fp, #-8]
    // 0x97f340: ArrayStore: r1[1] = r0  ; List_4
    //     0x97f340: add             x25, x1, #0x13
    //     0x97f344: str             w0, [x25]
    //     0x97f348: tbz             w0, #0, #0x97f364
    //     0x97f34c: ldurb           w16, [x1, #-1]
    //     0x97f350: ldurb           w17, [x0, #-1]
    //     0x97f354: and             x16, x17, x16, lsr #2
    //     0x97f358: tst             x16, HEAP, lsr #32
    //     0x97f35c: b.eq            #0x97f364
    //     0x97f360: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97f364: ldur            x0, [fp, #-8]
    // 0x97f368: r16 = "#"
    //     0x97f368: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x97f36c: ArrayStore: r0[0] = r16  ; List_4
    //     0x97f36c: stur            w16, [x0, #0x17]
    // 0x97f370: ldur            x1, [fp, #-0x38]
    // 0x97f374: r0 = shortHash()
    //     0x97f374: bl              #0x67f178  ; [package:flutter/src/foundation/diagnostics.dart] ::shortHash
    // 0x97f378: ldur            x1, [fp, #-8]
    // 0x97f37c: ArrayStore: r1[3] = r0  ; List_4
    //     0x97f37c: add             x25, x1, #0x1b
    //     0x97f380: str             w0, [x25]
    //     0x97f384: tbz             w0, #0, #0x97f3a0
    //     0x97f388: ldurb           w16, [x1, #-1]
    //     0x97f38c: ldurb           w17, [x0, #-1]
    //     0x97f390: and             x16, x17, x16, lsr #2
    //     0x97f394: tst             x16, HEAP, lsr #32
    //     0x97f398: b.eq            #0x97f3a0
    //     0x97f39c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97f3a0: ldur            x16, [fp, #-8]
    // 0x97f3a4: str             x16, [SP]
    // 0x97f3a8: r0 = _interpolate()
    //     0x97f3a8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x97f3ac: stur            x0, [fp, #-8]
    // 0x97f3b0: r0 = StateError()
    //     0x97f3b0: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x97f3b4: mov             x1, x0
    // 0x97f3b8: ldur            x0, [fp, #-8]
    // 0x97f3bc: StoreField: r1->field_b = r0
    //     0x97f3bc: stur            w0, [x1, #0xb]
    // 0x97f3c0: mov             x0, x1
    // 0x97f3c4: r0 = Throw()
    //     0x97f3c4: bl              #0xec04b8  ; ThrowStub
    // 0x97f3c8: brk             #0
    // 0x97f3cc: r1 = Null
    //     0x97f3cc: mov             x1, NULL
    // 0x97f3d0: r2 = 8
    //     0x97f3d0: movz            x2, #0x8
    // 0x97f3d4: r0 = AllocateArray()
    //     0x97f3d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x97f3d8: stur            x0, [fp, #-8]
    // 0x97f3dc: r16 = "RenderBox was not laid out: "
    //     0x97f3dc: ldr             x16, [PP, #0x44c0]  ; [pp+0x44c0] "RenderBox was not laid out: "
    // 0x97f3e0: StoreField: r0->field_f = r16
    //     0x97f3e0: stur            w16, [x0, #0xf]
    // 0x97f3e4: ldur            x16, [fp, #-0x18]
    // 0x97f3e8: str             x16, [SP]
    // 0x97f3ec: r0 = runtimeType()
    //     0x97f3ec: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x97f3f0: ldur            x1, [fp, #-8]
    // 0x97f3f4: ArrayStore: r1[1] = r0  ; List_4
    //     0x97f3f4: add             x25, x1, #0x13
    //     0x97f3f8: str             w0, [x25]
    //     0x97f3fc: tbz             w0, #0, #0x97f418
    //     0x97f400: ldurb           w16, [x1, #-1]
    //     0x97f404: ldurb           w17, [x0, #-1]
    //     0x97f408: and             x16, x17, x16, lsr #2
    //     0x97f40c: tst             x16, HEAP, lsr #32
    //     0x97f410: b.eq            #0x97f418
    //     0x97f414: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97f418: ldur            x0, [fp, #-8]
    // 0x97f41c: r16 = "#"
    //     0x97f41c: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x97f420: ArrayStore: r0[0] = r16  ; List_4
    //     0x97f420: stur            w16, [x0, #0x17]
    // 0x97f424: ldur            x1, [fp, #-0x18]
    // 0x97f428: r0 = shortHash()
    //     0x97f428: bl              #0x67f178  ; [package:flutter/src/foundation/diagnostics.dart] ::shortHash
    // 0x97f42c: ldur            x1, [fp, #-8]
    // 0x97f430: ArrayStore: r1[3] = r0  ; List_4
    //     0x97f430: add             x25, x1, #0x1b
    //     0x97f434: str             w0, [x25]
    //     0x97f438: tbz             w0, #0, #0x97f454
    //     0x97f43c: ldurb           w16, [x1, #-1]
    //     0x97f440: ldurb           w17, [x0, #-1]
    //     0x97f444: and             x16, x17, x16, lsr #2
    //     0x97f448: tst             x16, HEAP, lsr #32
    //     0x97f44c: b.eq            #0x97f454
    //     0x97f450: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97f454: ldur            x16, [fp, #-8]
    // 0x97f458: str             x16, [SP]
    // 0x97f45c: r0 = _interpolate()
    //     0x97f45c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x97f460: stur            x0, [fp, #-8]
    // 0x97f464: r0 = StateError()
    //     0x97f464: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x97f468: mov             x1, x0
    // 0x97f46c: ldur            x0, [fp, #-8]
    // 0x97f470: StoreField: r1->field_b = r0
    //     0x97f470: stur            w0, [x1, #0xb]
    // 0x97f474: mov             x0, x1
    // 0x97f478: r0 = Throw()
    //     0x97f478: bl              #0xec04b8  ; ThrowStub
    // 0x97f47c: brk             #0
    // 0x97f480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97f480: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97f484: b               #0x97ed3c
    // 0x97f488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97f488: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97f48c: b               #0x97edc4
    // 0x97f490: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f490: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97f494: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f494: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97f498: r0 = NullCastErrorSharedWithFPURegs()
    //     0x97f498: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x97f49c: SaveReg d0
    //     0x97f49c: str             q0, [SP, #-0x10]!
    // 0x97f4a0: r0 = 74
    //     0x97f4a0: movz            x0, #0x4a
    // 0x97f4a4: r30 = DoubleToIntegerStub
    //     0x97f4a4: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x97f4a8: LoadField: r30 = r30->field_7
    //     0x97f4a8: ldur            lr, [lr, #7]
    // 0x97f4ac: blr             lr
    // 0x97f4b0: RestoreReg d0
    //     0x97f4b0: ldr             q0, [SP], #0x10
    // 0x97f4b4: b               #0x97f0b0
    // 0x97f4b8: r9 = scrollController
    //     0x97f4b8: add             x9, PP, #0x51, lsl #12  ; [pp+0x51190] Field <<EMAIL>>: late final (offset: 0x1c)
    //     0x97f4bc: ldr             x9, [x9, #0x190]
    // 0x97f4c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x97f4c0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x97f4c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f4c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97f4c8: SaveReg d0
    //     0x97f4c8: str             q0, [SP, #-0x10]!
    // 0x97f4cc: r0 = 74
    //     0x97f4cc: movz            x0, #0x4a
    // 0x97f4d0: r30 = DoubleToIntegerStub
    //     0x97f4d0: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x97f4d4: LoadField: r30 = r30->field_7
    //     0x97f4d4: ldur            lr, [lr, #7]
    // 0x97f4d8: blr             lr
    // 0x97f4dc: RestoreReg d0
    //     0x97f4dc: ldr             q0, [SP], #0x10
    // 0x97f4e0: b               #0x97f190
    // 0x97f4e4: r9 = scrollController
    //     0x97f4e4: add             x9, PP, #0x51, lsl #12  ; [pp+0x51190] Field <<EMAIL>>: late final (offset: 0x1c)
    //     0x97f4e8: ldr             x9, [x9, #0x190]
    // 0x97f4ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x97f4ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x97f4f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f4f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97f4f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f4f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _schedulePositionNotificationUpdate(dynamic) {
    // ** addr: 0x97f58c, size: 0x38
    // 0x97f58c: EnterFrame
    //     0x97f58c: stp             fp, lr, [SP, #-0x10]!
    //     0x97f590: mov             fp, SP
    // 0x97f594: ldr             x0, [fp, #0x10]
    // 0x97f598: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97f598: ldur            w1, [x0, #0x17]
    // 0x97f59c: DecompressPointer r1
    //     0x97f59c: add             x1, x1, HEAP, lsl #32
    // 0x97f5a0: CheckStackOverflow
    //     0x97f5a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97f5a4: cmp             SP, x16
    //     0x97f5a8: b.ls            #0x97f5bc
    // 0x97f5ac: r0 = _schedulePositionNotificationUpdate()
    //     0x97f5ac: bl              #0x97ebd0  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_schedulePositionNotificationUpdate
    // 0x97f5b0: LeaveFrame
    //     0x97f5b0: mov             SP, fp
    //     0x97f5b4: ldp             fp, lr, [SP], #0x10
    // 0x97f5b8: ret
    //     0x97f5b8: ret             
    // 0x97f5bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97f5bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97f5c0: b               #0x97f5ac
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a17a4, size: 0xc0
    // 0x9a17a4: EnterFrame
    //     0x9a17a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9a17a8: mov             fp, SP
    // 0x9a17ac: AllocStack(0x10)
    //     0x9a17ac: sub             SP, SP, #0x10
    // 0x9a17b0: SetupParameters(_PositionedListState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a17b0: mov             x4, x1
    //     0x9a17b4: mov             x3, x2
    //     0x9a17b8: stur            x1, [fp, #-8]
    //     0x9a17bc: stur            x2, [fp, #-0x10]
    // 0x9a17c0: CheckStackOverflow
    //     0x9a17c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a17c4: cmp             SP, x16
    //     0x9a17c8: b.ls            #0x9a185c
    // 0x9a17cc: mov             x0, x3
    // 0x9a17d0: r2 = Null
    //     0x9a17d0: mov             x2, NULL
    // 0x9a17d4: r1 = Null
    //     0x9a17d4: mov             x1, NULL
    // 0x9a17d8: r4 = 60
    //     0x9a17d8: movz            x4, #0x3c
    // 0x9a17dc: branchIfSmi(r0, 0x9a17e8)
    //     0x9a17dc: tbz             w0, #0, #0x9a17e8
    // 0x9a17e0: r4 = LoadClassIdInstr(r0)
    //     0x9a17e0: ldur            x4, [x0, #-1]
    //     0x9a17e4: ubfx            x4, x4, #0xc, #0x14
    // 0x9a17e8: r17 = 4696
    //     0x9a17e8: movz            x17, #0x1258
    // 0x9a17ec: cmp             x4, x17
    // 0x9a17f0: b.eq            #0x9a1808
    // 0x9a17f4: r8 = PositionedList
    //     0x9a17f4: add             x8, PP, #0x51, lsl #12  ; [pp+0x51198] Type: PositionedList
    //     0x9a17f8: ldr             x8, [x8, #0x198]
    // 0x9a17fc: r3 = Null
    //     0x9a17fc: add             x3, PP, #0x51, lsl #12  ; [pp+0x511a0] Null
    //     0x9a1800: ldr             x3, [x3, #0x1a0]
    // 0x9a1804: r0 = PositionedList()
    //     0x9a1804: bl              #0x97ebac  ; IsType_PositionedList_Stub
    // 0x9a1808: ldur            x3, [fp, #-8]
    // 0x9a180c: LoadField: r2 = r3->field_7
    //     0x9a180c: ldur            w2, [x3, #7]
    // 0x9a1810: DecompressPointer r2
    //     0x9a1810: add             x2, x2, HEAP, lsl #32
    // 0x9a1814: ldur            x0, [fp, #-0x10]
    // 0x9a1818: r1 = Null
    //     0x9a1818: mov             x1, NULL
    // 0x9a181c: cmp             w2, NULL
    // 0x9a1820: b.eq            #0x9a1844
    // 0x9a1824: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a1824: ldur            w4, [x2, #0x17]
    // 0x9a1828: DecompressPointer r4
    //     0x9a1828: add             x4, x4, HEAP, lsl #32
    // 0x9a182c: r8 = X0 bound StatefulWidget
    //     0x9a182c: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a1830: ldr             x8, [x8, #0x7f8]
    // 0x9a1834: LoadField: r9 = r4->field_7
    //     0x9a1834: ldur            x9, [x4, #7]
    // 0x9a1838: r3 = Null
    //     0x9a1838: add             x3, PP, #0x51, lsl #12  ; [pp+0x511b0] Null
    //     0x9a183c: ldr             x3, [x3, #0x1b0]
    // 0x9a1840: blr             x9
    // 0x9a1844: ldur            x1, [fp, #-8]
    // 0x9a1848: r0 = _schedulePositionNotificationUpdate()
    //     0x9a1848: bl              #0x97ebd0  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_schedulePositionNotificationUpdate
    // 0x9a184c: r0 = Null
    //     0x9a184c: mov             x0, NULL
    // 0x9a1850: LeaveFrame
    //     0x9a1850: mov             SP, fp
    //     0x9a1854: ldp             fp, lr, [SP], #0x10
    // 0x9a1858: ret
    //     0x9a1858: ret             
    // 0x9a185c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a185c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1860: b               #0x9a17cc
  }
  _ build(/* No info */) {
    // ** addr: 0xa4b2bc, size: 0x658
    // 0xa4b2bc: EnterFrame
    //     0xa4b2bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa4b2c0: mov             fp, SP
    // 0xa4b2c4: AllocStack(0x68)
    //     0xa4b2c4: sub             SP, SP, #0x68
    // 0xa4b2c8: SetupParameters(_PositionedListState this /* r1 => r1, fp-0x8 */)
    //     0xa4b2c8: stur            x1, [fp, #-8]
    // 0xa4b2cc: CheckStackOverflow
    //     0xa4b2cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4b2d0: cmp             SP, x16
    //     0xa4b2d4: b.ls            #0xa4b8d0
    // 0xa4b2d8: r1 = 1
    //     0xa4b2d8: movz            x1, #0x1
    // 0xa4b2dc: r0 = AllocateContext()
    //     0xa4b2dc: bl              #0xec126c  ; AllocateContextStub
    // 0xa4b2e0: mov             x3, x0
    // 0xa4b2e4: ldur            x0, [fp, #-8]
    // 0xa4b2e8: stur            x3, [fp, #-0x30]
    // 0xa4b2ec: StoreField: r3->field_f = r0
    //     0xa4b2ec: stur            w0, [x3, #0xf]
    // 0xa4b2f0: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa4b2f0: ldur            w4, [x0, #0x17]
    // 0xa4b2f4: DecompressPointer r4
    //     0xa4b2f4: add             x4, x4, HEAP, lsl #32
    // 0xa4b2f8: stur            x4, [fp, #-0x28]
    // 0xa4b2fc: LoadField: r1 = r0->field_b
    //     0xa4b2fc: ldur            w1, [x0, #0xb]
    // 0xa4b300: DecompressPointer r1
    //     0xa4b300: add             x1, x1, HEAP, lsl #32
    // 0xa4b304: cmp             w1, NULL
    // 0xa4b308: b.eq            #0xa4b8d8
    // 0xa4b30c: LoadField: d0 = r1->field_2b
    //     0xa4b30c: ldur            d0, [x1, #0x2b]
    // 0xa4b310: stur            d0, [fp, #-0x68]
    // 0xa4b314: LoadField: r5 = r0->field_13
    //     0xa4b314: ldur            w5, [x0, #0x13]
    // 0xa4b318: DecompressPointer r5
    //     0xa4b318: add             x5, x5, HEAP, lsl #32
    // 0xa4b31c: stur            x5, [fp, #-0x20]
    // 0xa4b320: LoadField: r6 = r0->field_1b
    //     0xa4b320: ldur            w6, [x0, #0x1b]
    // 0xa4b324: DecompressPointer r6
    //     0xa4b324: add             x6, x6, HEAP, lsl #32
    // 0xa4b328: r16 = Sentinel
    //     0xa4b328: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4b32c: cmp             w6, w16
    // 0xa4b330: b.eq            #0xa4b8dc
    // 0xa4b334: stur            x6, [fp, #-0x18]
    // 0xa4b338: LoadField: d1 = r1->field_43
    //     0xa4b338: ldur            d1, [x1, #0x43]
    // 0xa4b33c: stur            d1, [fp, #-0x60]
    // 0xa4b340: LoadField: r7 = r1->field_b
    //     0xa4b340: ldur            x7, [x1, #0xb]
    // 0xa4b344: stur            x7, [fp, #-0x10]
    // 0xa4b348: r1 = <Widget>
    //     0xa4b348: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa4b34c: r2 = 0
    //     0xa4b34c: movz            x2, #0
    // 0xa4b350: r0 = _GrowableList()
    //     0xa4b350: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa4b354: mov             x2, x0
    // 0xa4b358: ldur            x0, [fp, #-8]
    // 0xa4b35c: stur            x2, [fp, #-0x38]
    // 0xa4b360: LoadField: r1 = r0->field_b
    //     0xa4b360: ldur            w1, [x0, #0xb]
    // 0xa4b364: DecompressPointer r1
    //     0xa4b364: add             x1, x1, HEAP, lsl #32
    // 0xa4b368: cmp             w1, NULL
    // 0xa4b36c: b.eq            #0xa4b8e8
    // 0xa4b370: LoadField: r3 = r1->field_23
    //     0xa4b370: ldur            x3, [x1, #0x23]
    // 0xa4b374: cmp             x3, #0
    // 0xa4b378: b.le            #0xa4b4e0
    // 0xa4b37c: mov             x1, x0
    // 0xa4b380: r0 = _leadingSliverPadding()
    //     0xa4b380: bl              #0xa4b92c  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_leadingSliverPadding
    // 0xa4b384: mov             x3, x0
    // 0xa4b388: ldur            x0, [fp, #-8]
    // 0xa4b38c: stur            x3, [fp, #-0x48]
    // 0xa4b390: LoadField: r1 = r0->field_b
    //     0xa4b390: ldur            w1, [x0, #0xb]
    // 0xa4b394: DecompressPointer r1
    //     0xa4b394: add             x1, x1, HEAP, lsl #32
    // 0xa4b398: cmp             w1, NULL
    // 0xa4b39c: b.eq            #0xa4b8ec
    // 0xa4b3a0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4b3a0: ldur            w2, [x1, #0x17]
    // 0xa4b3a4: DecompressPointer r2
    //     0xa4b3a4: add             x2, x2, HEAP, lsl #32
    // 0xa4b3a8: cmp             w2, NULL
    // 0xa4b3ac: b.ne            #0xa4b3bc
    // 0xa4b3b0: LoadField: r2 = r1->field_23
    //     0xa4b3b0: ldur            x2, [x1, #0x23]
    // 0xa4b3b4: mov             x5, x2
    // 0xa4b3b8: b               #0xa4b3c8
    // 0xa4b3bc: LoadField: r2 = r1->field_23
    //     0xa4b3bc: ldur            x2, [x1, #0x23]
    // 0xa4b3c0: lsl             x1, x2, #1
    // 0xa4b3c4: mov             x5, x1
    // 0xa4b3c8: ldur            x4, [fp, #-0x38]
    // 0xa4b3cc: ldur            x2, [fp, #-0x30]
    // 0xa4b3d0: stur            x5, [fp, #-0x40]
    // 0xa4b3d4: r1 = Function '<anonymous closure>':.
    //     0xa4b3d4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51178] AnonymousClosure: (0xa4bc8c), in [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::build (0xa4b2bc)
    //     0xa4b3d8: ldr             x1, [x1, #0x178]
    // 0xa4b3dc: r0 = AllocateClosure()
    //     0xa4b3dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4b3e0: stur            x0, [fp, #-0x50]
    // 0xa4b3e4: r0 = SliverChildBuilderDelegate()
    //     0xa4b3e4: bl              #0x9d3320  ; AllocateSliverChildBuilderDelegateStub -> SliverChildBuilderDelegate (size=0x2c)
    // 0xa4b3e8: mov             x2, x0
    // 0xa4b3ec: ldur            x0, [fp, #-0x50]
    // 0xa4b3f0: stur            x2, [fp, #-0x58]
    // 0xa4b3f4: StoreField: r2->field_7 = r0
    //     0xa4b3f4: stur            w0, [x2, #7]
    // 0xa4b3f8: ldur            x3, [fp, #-0x40]
    // 0xa4b3fc: r0 = BoxInt64Instr(r3)
    //     0xa4b3fc: sbfiz           x0, x3, #1, #0x1f
    //     0xa4b400: cmp             x3, x0, asr #1
    //     0xa4b404: b.eq            #0xa4b410
    //     0xa4b408: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4b40c: stur            x3, [x0, #7]
    // 0xa4b410: StoreField: r2->field_b = r0
    //     0xa4b410: stur            w0, [x2, #0xb]
    // 0xa4b414: r0 = true
    //     0xa4b414: add             x0, NULL, #0x20  ; true
    // 0xa4b418: StoreField: r2->field_f = r0
    //     0xa4b418: stur            w0, [x2, #0xf]
    // 0xa4b41c: StoreField: r2->field_13 = r0
    //     0xa4b41c: stur            w0, [x2, #0x13]
    // 0xa4b420: r1 = false
    //     0xa4b420: add             x1, NULL, #0x30  ; false
    // 0xa4b424: ArrayStore: r2[0] = r1  ; List_4
    //     0xa4b424: stur            w1, [x2, #0x17]
    // 0xa4b428: r3 = Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static.
    //     0xa4b428: add             x3, PP, #0x26, lsl #12  ; [pp+0x26ef8] Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static. (0x7e54fb8bd554)
    //     0xa4b42c: ldr             x3, [x3, #0xef8]
    // 0xa4b430: StoreField: r2->field_23 = r3
    //     0xa4b430: stur            w3, [x2, #0x23]
    // 0xa4b434: StoreField: r2->field_1b = rZR
    //     0xa4b434: stur            xzr, [x2, #0x1b]
    // 0xa4b438: r0 = SliverList()
    //     0xa4b438: bl              #0xa1c300  ; AllocateSliverListStub -> SliverList (size=0x10)
    // 0xa4b43c: mov             x1, x0
    // 0xa4b440: ldur            x0, [fp, #-0x58]
    // 0xa4b444: stur            x1, [fp, #-0x50]
    // 0xa4b448: StoreField: r1->field_b = r0
    //     0xa4b448: stur            w0, [x1, #0xb]
    // 0xa4b44c: r0 = SliverPadding()
    //     0xa4b44c: bl              #0xa01298  ; AllocateSliverPaddingStub -> SliverPadding (size=0x14)
    // 0xa4b450: mov             x2, x0
    // 0xa4b454: ldur            x0, [fp, #-0x48]
    // 0xa4b458: stur            x2, [fp, #-0x58]
    // 0xa4b45c: StoreField: r2->field_f = r0
    //     0xa4b45c: stur            w0, [x2, #0xf]
    // 0xa4b460: ldur            x0, [fp, #-0x50]
    // 0xa4b464: StoreField: r2->field_b = r0
    //     0xa4b464: stur            w0, [x2, #0xb]
    // 0xa4b468: ldur            x0, [fp, #-0x38]
    // 0xa4b46c: LoadField: r1 = r0->field_b
    //     0xa4b46c: ldur            w1, [x0, #0xb]
    // 0xa4b470: LoadField: r3 = r0->field_f
    //     0xa4b470: ldur            w3, [x0, #0xf]
    // 0xa4b474: DecompressPointer r3
    //     0xa4b474: add             x3, x3, HEAP, lsl #32
    // 0xa4b478: LoadField: r4 = r3->field_b
    //     0xa4b478: ldur            w4, [x3, #0xb]
    // 0xa4b47c: r3 = LoadInt32Instr(r1)
    //     0xa4b47c: sbfx            x3, x1, #1, #0x1f
    // 0xa4b480: stur            x3, [fp, #-0x40]
    // 0xa4b484: r1 = LoadInt32Instr(r4)
    //     0xa4b484: sbfx            x1, x4, #1, #0x1f
    // 0xa4b488: cmp             x3, x1
    // 0xa4b48c: b.ne            #0xa4b498
    // 0xa4b490: mov             x1, x0
    // 0xa4b494: r0 = _growToNextCapacity()
    //     0xa4b494: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4b498: ldur            x2, [fp, #-0x38]
    // 0xa4b49c: ldur            x3, [fp, #-0x40]
    // 0xa4b4a0: add             x0, x3, #1
    // 0xa4b4a4: lsl             x1, x0, #1
    // 0xa4b4a8: StoreField: r2->field_b = r1
    //     0xa4b4a8: stur            w1, [x2, #0xb]
    // 0xa4b4ac: LoadField: r1 = r2->field_f
    //     0xa4b4ac: ldur            w1, [x2, #0xf]
    // 0xa4b4b0: DecompressPointer r1
    //     0xa4b4b0: add             x1, x1, HEAP, lsl #32
    // 0xa4b4b4: ldur            x0, [fp, #-0x58]
    // 0xa4b4b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4b4b8: add             x25, x1, x3, lsl #2
    //     0xa4b4bc: add             x25, x25, #0xf
    //     0xa4b4c0: str             w0, [x25]
    //     0xa4b4c4: tbz             w0, #0, #0xa4b4e0
    //     0xa4b4c8: ldurb           w16, [x1, #-1]
    //     0xa4b4cc: ldurb           w17, [x0, #-1]
    //     0xa4b4d0: and             x16, x17, x16, lsr #2
    //     0xa4b4d4: tst             x16, HEAP, lsr #32
    //     0xa4b4d8: b.eq            #0xa4b4e0
    //     0xa4b4dc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4b4e0: ldur            x0, [fp, #-8]
    // 0xa4b4e4: mov             x1, x0
    // 0xa4b4e8: r0 = _leadingSliverPadding()
    //     0xa4b4e8: bl              #0xa4b92c  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_leadingSliverPadding
    // 0xa4b4ec: mov             x3, x0
    // 0xa4b4f0: ldur            x0, [fp, #-8]
    // 0xa4b4f4: stur            x3, [fp, #-0x48]
    // 0xa4b4f8: LoadField: r1 = r0->field_b
    //     0xa4b4f8: ldur            w1, [x0, #0xb]
    // 0xa4b4fc: DecompressPointer r1
    //     0xa4b4fc: add             x1, x1, HEAP, lsl #32
    // 0xa4b500: cmp             w1, NULL
    // 0xa4b504: b.eq            #0xa4b8f0
    // 0xa4b508: LoadField: r2 = r1->field_b
    //     0xa4b508: ldur            x2, [x1, #0xb]
    // 0xa4b50c: cbz             x2, #0xa4b518
    // 0xa4b510: r6 = 1
    //     0xa4b510: movz            x6, #0x1
    // 0xa4b514: b               #0xa4b51c
    // 0xa4b518: r6 = 0
    //     0xa4b518: movz            x6, #0
    // 0xa4b51c: ldur            x5, [fp, #-0x20]
    // 0xa4b520: ldur            x4, [fp, #-0x38]
    // 0xa4b524: ldur            x2, [fp, #-0x30]
    // 0xa4b528: stur            x6, [fp, #-0x40]
    // 0xa4b52c: r1 = Function '<anonymous closure>':.
    //     0xa4b52c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51180] AnonymousClosure: (0xa4bbe4), in [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::build (0xa4b2bc)
    //     0xa4b530: ldr             x1, [x1, #0x180]
    // 0xa4b534: r0 = AllocateClosure()
    //     0xa4b534: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4b538: stur            x0, [fp, #-0x50]
    // 0xa4b53c: r0 = SliverChildBuilderDelegate()
    //     0xa4b53c: bl              #0x9d3320  ; AllocateSliverChildBuilderDelegateStub -> SliverChildBuilderDelegate (size=0x2c)
    // 0xa4b540: mov             x1, x0
    // 0xa4b544: ldur            x0, [fp, #-0x50]
    // 0xa4b548: stur            x1, [fp, #-0x58]
    // 0xa4b54c: StoreField: r1->field_7 = r0
    //     0xa4b54c: stur            w0, [x1, #7]
    // 0xa4b550: ldur            x0, [fp, #-0x40]
    // 0xa4b554: lsl             x2, x0, #1
    // 0xa4b558: StoreField: r1->field_b = r2
    //     0xa4b558: stur            w2, [x1, #0xb]
    // 0xa4b55c: r0 = true
    //     0xa4b55c: add             x0, NULL, #0x20  ; true
    // 0xa4b560: StoreField: r1->field_f = r0
    //     0xa4b560: stur            w0, [x1, #0xf]
    // 0xa4b564: StoreField: r1->field_13 = r0
    //     0xa4b564: stur            w0, [x1, #0x13]
    // 0xa4b568: r2 = false
    //     0xa4b568: add             x2, NULL, #0x30  ; false
    // 0xa4b56c: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4b56c: stur            w2, [x1, #0x17]
    // 0xa4b570: r3 = Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static.
    //     0xa4b570: add             x3, PP, #0x26, lsl #12  ; [pp+0x26ef8] Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static. (0x7e54fb8bd554)
    //     0xa4b574: ldr             x3, [x3, #0xef8]
    // 0xa4b578: StoreField: r1->field_23 = r3
    //     0xa4b578: stur            w3, [x1, #0x23]
    // 0xa4b57c: StoreField: r1->field_1b = rZR
    //     0xa4b57c: stur            xzr, [x1, #0x1b]
    // 0xa4b580: r0 = SliverList()
    //     0xa4b580: bl              #0xa1c300  ; AllocateSliverListStub -> SliverList (size=0x10)
    // 0xa4b584: mov             x1, x0
    // 0xa4b588: ldur            x0, [fp, #-0x58]
    // 0xa4b58c: stur            x1, [fp, #-0x50]
    // 0xa4b590: StoreField: r1->field_b = r0
    //     0xa4b590: stur            w0, [x1, #0xb]
    // 0xa4b594: r0 = SliverPadding()
    //     0xa4b594: bl              #0xa01298  ; AllocateSliverPaddingStub -> SliverPadding (size=0x14)
    // 0xa4b598: mov             x2, x0
    // 0xa4b59c: ldur            x0, [fp, #-0x48]
    // 0xa4b5a0: stur            x2, [fp, #-0x58]
    // 0xa4b5a4: StoreField: r2->field_f = r0
    //     0xa4b5a4: stur            w0, [x2, #0xf]
    // 0xa4b5a8: ldur            x0, [fp, #-0x50]
    // 0xa4b5ac: StoreField: r2->field_b = r0
    //     0xa4b5ac: stur            w0, [x2, #0xb]
    // 0xa4b5b0: ldur            x0, [fp, #-0x20]
    // 0xa4b5b4: StoreField: r2->field_7 = r0
    //     0xa4b5b4: stur            w0, [x2, #7]
    // 0xa4b5b8: ldur            x3, [fp, #-0x38]
    // 0xa4b5bc: LoadField: r1 = r3->field_b
    //     0xa4b5bc: ldur            w1, [x3, #0xb]
    // 0xa4b5c0: LoadField: r4 = r3->field_f
    //     0xa4b5c0: ldur            w4, [x3, #0xf]
    // 0xa4b5c4: DecompressPointer r4
    //     0xa4b5c4: add             x4, x4, HEAP, lsl #32
    // 0xa4b5c8: LoadField: r5 = r4->field_b
    //     0xa4b5c8: ldur            w5, [x4, #0xb]
    // 0xa4b5cc: r4 = LoadInt32Instr(r1)
    //     0xa4b5cc: sbfx            x4, x1, #1, #0x1f
    // 0xa4b5d0: stur            x4, [fp, #-0x40]
    // 0xa4b5d4: r1 = LoadInt32Instr(r5)
    //     0xa4b5d4: sbfx            x1, x5, #1, #0x1f
    // 0xa4b5d8: cmp             x4, x1
    // 0xa4b5dc: b.ne            #0xa4b5e8
    // 0xa4b5e0: mov             x1, x3
    // 0xa4b5e4: r0 = _growToNextCapacity()
    //     0xa4b5e4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4b5e8: ldur            x4, [fp, #-8]
    // 0xa4b5ec: ldur            x2, [fp, #-0x38]
    // 0xa4b5f0: ldur            x3, [fp, #-0x40]
    // 0xa4b5f4: add             x0, x3, #1
    // 0xa4b5f8: lsl             x1, x0, #1
    // 0xa4b5fc: StoreField: r2->field_b = r1
    //     0xa4b5fc: stur            w1, [x2, #0xb]
    // 0xa4b600: LoadField: r1 = r2->field_f
    //     0xa4b600: ldur            w1, [x2, #0xf]
    // 0xa4b604: DecompressPointer r1
    //     0xa4b604: add             x1, x1, HEAP, lsl #32
    // 0xa4b608: ldur            x0, [fp, #-0x58]
    // 0xa4b60c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4b60c: add             x25, x1, x3, lsl #2
    //     0xa4b610: add             x25, x25, #0xf
    //     0xa4b614: str             w0, [x25]
    //     0xa4b618: tbz             w0, #0, #0xa4b634
    //     0xa4b61c: ldurb           w16, [x1, #-1]
    //     0xa4b620: ldurb           w17, [x0, #-1]
    //     0xa4b624: and             x16, x17, x16, lsr #2
    //     0xa4b628: tst             x16, HEAP, lsr #32
    //     0xa4b62c: b.eq            #0xa4b634
    //     0xa4b630: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4b634: LoadField: r0 = r4->field_b
    //     0xa4b634: ldur            w0, [x4, #0xb]
    // 0xa4b638: DecompressPointer r0
    //     0xa4b638: add             x0, x0, HEAP, lsl #32
    // 0xa4b63c: cmp             w0, NULL
    // 0xa4b640: b.eq            #0xa4b8f4
    // 0xa4b644: LoadField: r1 = r0->field_23
    //     0xa4b644: ldur            x1, [x0, #0x23]
    // 0xa4b648: tbnz            x1, #0x3f, #0xa4b7d8
    // 0xa4b64c: LoadField: r3 = r0->field_b
    //     0xa4b64c: ldur            x3, [x0, #0xb]
    // 0xa4b650: sub             x0, x3, #1
    // 0xa4b654: cmp             x1, x0
    // 0xa4b658: b.ge            #0xa4b7d8
    // 0xa4b65c: mov             x1, x4
    // 0xa4b660: r0 = _leadingSliverPadding()
    //     0xa4b660: bl              #0xa4b92c  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_leadingSliverPadding
    // 0xa4b664: mov             x3, x0
    // 0xa4b668: ldur            x0, [fp, #-8]
    // 0xa4b66c: stur            x3, [fp, #-0x48]
    // 0xa4b670: LoadField: r1 = r0->field_b
    //     0xa4b670: ldur            w1, [x0, #0xb]
    // 0xa4b674: DecompressPointer r1
    //     0xa4b674: add             x1, x1, HEAP, lsl #32
    // 0xa4b678: cmp             w1, NULL
    // 0xa4b67c: b.eq            #0xa4b8f8
    // 0xa4b680: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa4b680: ldur            w0, [x1, #0x17]
    // 0xa4b684: DecompressPointer r0
    //     0xa4b684: add             x0, x0, HEAP, lsl #32
    // 0xa4b688: cmp             w0, NULL
    // 0xa4b68c: b.ne            #0xa4b6a8
    // 0xa4b690: LoadField: r0 = r1->field_b
    //     0xa4b690: ldur            x0, [x1, #0xb]
    // 0xa4b694: LoadField: r2 = r1->field_23
    //     0xa4b694: ldur            x2, [x1, #0x23]
    // 0xa4b698: sub             x1, x0, x2
    // 0xa4b69c: sub             x0, x1, #1
    // 0xa4b6a0: mov             x4, x0
    // 0xa4b6a4: b               #0xa4b6c0
    // 0xa4b6a8: LoadField: r0 = r1->field_b
    //     0xa4b6a8: ldur            x0, [x1, #0xb]
    // 0xa4b6ac: LoadField: r2 = r1->field_23
    //     0xa4b6ac: ldur            x2, [x1, #0x23]
    // 0xa4b6b0: sub             x1, x0, x2
    // 0xa4b6b4: sub             x0, x1, #1
    // 0xa4b6b8: lsl             x1, x0, #1
    // 0xa4b6bc: mov             x4, x1
    // 0xa4b6c0: ldur            x0, [fp, #-0x38]
    // 0xa4b6c4: ldur            x2, [fp, #-0x30]
    // 0xa4b6c8: stur            x4, [fp, #-0x40]
    // 0xa4b6cc: r1 = Function '<anonymous closure>':.
    //     0xa4b6cc: add             x1, PP, #0x51, lsl #12  ; [pp+0x51188] AnonymousClosure: (0xa4b968), in [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::build (0xa4b2bc)
    //     0xa4b6d0: ldr             x1, [x1, #0x188]
    // 0xa4b6d4: r0 = AllocateClosure()
    //     0xa4b6d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4b6d8: stur            x0, [fp, #-8]
    // 0xa4b6dc: r0 = SliverChildBuilderDelegate()
    //     0xa4b6dc: bl              #0x9d3320  ; AllocateSliverChildBuilderDelegateStub -> SliverChildBuilderDelegate (size=0x2c)
    // 0xa4b6e0: mov             x2, x0
    // 0xa4b6e4: ldur            x0, [fp, #-8]
    // 0xa4b6e8: stur            x2, [fp, #-0x30]
    // 0xa4b6ec: StoreField: r2->field_7 = r0
    //     0xa4b6ec: stur            w0, [x2, #7]
    // 0xa4b6f0: ldur            x3, [fp, #-0x40]
    // 0xa4b6f4: r0 = BoxInt64Instr(r3)
    //     0xa4b6f4: sbfiz           x0, x3, #1, #0x1f
    //     0xa4b6f8: cmp             x3, x0, asr #1
    //     0xa4b6fc: b.eq            #0xa4b708
    //     0xa4b700: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4b704: stur            x3, [x0, #7]
    // 0xa4b708: StoreField: r2->field_b = r0
    //     0xa4b708: stur            w0, [x2, #0xb]
    // 0xa4b70c: r0 = true
    //     0xa4b70c: add             x0, NULL, #0x20  ; true
    // 0xa4b710: StoreField: r2->field_f = r0
    //     0xa4b710: stur            w0, [x2, #0xf]
    // 0xa4b714: StoreField: r2->field_13 = r0
    //     0xa4b714: stur            w0, [x2, #0x13]
    // 0xa4b718: r0 = false
    //     0xa4b718: add             x0, NULL, #0x30  ; false
    // 0xa4b71c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4b71c: stur            w0, [x2, #0x17]
    // 0xa4b720: r1 = Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static.
    //     0xa4b720: add             x1, PP, #0x26, lsl #12  ; [pp+0x26ef8] Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static. (0x7e54fb8bd554)
    //     0xa4b724: ldr             x1, [x1, #0xef8]
    // 0xa4b728: StoreField: r2->field_23 = r1
    //     0xa4b728: stur            w1, [x2, #0x23]
    // 0xa4b72c: StoreField: r2->field_1b = rZR
    //     0xa4b72c: stur            xzr, [x2, #0x1b]
    // 0xa4b730: r0 = SliverList()
    //     0xa4b730: bl              #0xa1c300  ; AllocateSliverListStub -> SliverList (size=0x10)
    // 0xa4b734: mov             x1, x0
    // 0xa4b738: ldur            x0, [fp, #-0x30]
    // 0xa4b73c: stur            x1, [fp, #-8]
    // 0xa4b740: StoreField: r1->field_b = r0
    //     0xa4b740: stur            w0, [x1, #0xb]
    // 0xa4b744: r0 = SliverPadding()
    //     0xa4b744: bl              #0xa01298  ; AllocateSliverPaddingStub -> SliverPadding (size=0x14)
    // 0xa4b748: mov             x2, x0
    // 0xa4b74c: ldur            x0, [fp, #-0x48]
    // 0xa4b750: stur            x2, [fp, #-0x30]
    // 0xa4b754: StoreField: r2->field_f = r0
    //     0xa4b754: stur            w0, [x2, #0xf]
    // 0xa4b758: ldur            x0, [fp, #-8]
    // 0xa4b75c: StoreField: r2->field_b = r0
    //     0xa4b75c: stur            w0, [x2, #0xb]
    // 0xa4b760: ldur            x0, [fp, #-0x38]
    // 0xa4b764: LoadField: r1 = r0->field_b
    //     0xa4b764: ldur            w1, [x0, #0xb]
    // 0xa4b768: LoadField: r3 = r0->field_f
    //     0xa4b768: ldur            w3, [x0, #0xf]
    // 0xa4b76c: DecompressPointer r3
    //     0xa4b76c: add             x3, x3, HEAP, lsl #32
    // 0xa4b770: LoadField: r4 = r3->field_b
    //     0xa4b770: ldur            w4, [x3, #0xb]
    // 0xa4b774: r3 = LoadInt32Instr(r1)
    //     0xa4b774: sbfx            x3, x1, #1, #0x1f
    // 0xa4b778: stur            x3, [fp, #-0x40]
    // 0xa4b77c: r1 = LoadInt32Instr(r4)
    //     0xa4b77c: sbfx            x1, x4, #1, #0x1f
    // 0xa4b780: cmp             x3, x1
    // 0xa4b784: b.ne            #0xa4b790
    // 0xa4b788: mov             x1, x0
    // 0xa4b78c: r0 = _growToNextCapacity()
    //     0xa4b78c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4b790: ldur            x2, [fp, #-0x38]
    // 0xa4b794: ldur            x3, [fp, #-0x40]
    // 0xa4b798: add             x0, x3, #1
    // 0xa4b79c: lsl             x1, x0, #1
    // 0xa4b7a0: StoreField: r2->field_b = r1
    //     0xa4b7a0: stur            w1, [x2, #0xb]
    // 0xa4b7a4: LoadField: r1 = r2->field_f
    //     0xa4b7a4: ldur            w1, [x2, #0xf]
    // 0xa4b7a8: DecompressPointer r1
    //     0xa4b7a8: add             x1, x1, HEAP, lsl #32
    // 0xa4b7ac: ldur            x0, [fp, #-0x30]
    // 0xa4b7b0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4b7b0: add             x25, x1, x3, lsl #2
    //     0xa4b7b4: add             x25, x25, #0xf
    //     0xa4b7b8: str             w0, [x25]
    //     0xa4b7bc: tbz             w0, #0, #0xa4b7d8
    //     0xa4b7c0: ldurb           w16, [x1, #-1]
    //     0xa4b7c4: ldurb           w17, [x0, #-1]
    //     0xa4b7c8: and             x16, x17, x16, lsr #2
    //     0xa4b7cc: tst             x16, HEAP, lsr #32
    //     0xa4b7d0: b.eq            #0xa4b7d8
    //     0xa4b7d4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4b7d8: ldur            x1, [fp, #-0x28]
    // 0xa4b7dc: ldur            d0, [fp, #-0x68]
    // 0xa4b7e0: ldur            x0, [fp, #-0x20]
    // 0xa4b7e4: ldur            d1, [fp, #-0x60]
    // 0xa4b7e8: ldur            x4, [fp, #-0x10]
    // 0xa4b7ec: ldur            x3, [fp, #-0x18]
    // 0xa4b7f0: r0 = UnboundedCustomScrollView()
    //     0xa4b7f0: bl              #0xa4b920  ; AllocateUnboundedCustomScrollViewStub -> UnboundedCustomScrollView (size=0x60)
    // 0xa4b7f4: mov             x2, x0
    // 0xa4b7f8: r0 = false
    //     0xa4b7f8: add             x0, NULL, #0x30  ; false
    // 0xa4b7fc: stur            x2, [fp, #-8]
    // 0xa4b800: StoreField: r2->field_53 = r0
    //     0xa4b800: stur            w0, [x2, #0x53]
    // 0xa4b804: ldur            d0, [fp, #-0x68]
    // 0xa4b808: StoreField: r2->field_57 = d0
    //     0xa4b808: stur            d0, [x2, #0x57]
    // 0xa4b80c: ldur            x1, [fp, #-0x38]
    // 0xa4b810: StoreField: r2->field_4f = r1
    //     0xa4b810: stur            w1, [x2, #0x4f]
    // 0xa4b814: r1 = Instance_Axis
    //     0xa4b814: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa4b818: StoreField: r2->field_b = r1
    //     0xa4b818: stur            w1, [x2, #0xb]
    // 0xa4b81c: StoreField: r2->field_f = r0
    //     0xa4b81c: stur            w0, [x2, #0xf]
    // 0xa4b820: ldur            x1, [fp, #-0x18]
    // 0xa4b824: StoreField: r2->field_13 = r1
    //     0xa4b824: stur            w1, [x2, #0x13]
    // 0xa4b828: StoreField: r2->field_23 = r0
    //     0xa4b828: stur            w0, [x2, #0x23]
    // 0xa4b82c: ldur            x0, [fp, #-0x20]
    // 0xa4b830: StoreField: r2->field_27 = r0
    //     0xa4b830: stur            w0, [x2, #0x27]
    // 0xa4b834: StoreField: r2->field_2b = rZR
    //     0xa4b834: stur            xzr, [x2, #0x2b]
    // 0xa4b838: ldur            d0, [fp, #-0x60]
    // 0xa4b83c: r0 = inline_Allocate_Double()
    //     0xa4b83c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa4b840: add             x0, x0, #0x10
    //     0xa4b844: cmp             x1, x0
    //     0xa4b848: b.ls            #0xa4b8fc
    //     0xa4b84c: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4b850: sub             x0, x0, #0xf
    //     0xa4b854: movz            x1, #0xe15c
    //     0xa4b858: movk            x1, #0x3, lsl #16
    //     0xa4b85c: stur            x1, [x0, #-1]
    // 0xa4b860: StoreField: r0->field_7 = d0
    //     0xa4b860: stur            d0, [x0, #7]
    // 0xa4b864: StoreField: r2->field_33 = r0
    //     0xa4b864: stur            w0, [x2, #0x33]
    // 0xa4b868: ldur            x3, [fp, #-0x10]
    // 0xa4b86c: r0 = BoxInt64Instr(r3)
    //     0xa4b86c: sbfiz           x0, x3, #1, #0x1f
    //     0xa4b870: cmp             x3, x0, asr #1
    //     0xa4b874: b.eq            #0xa4b880
    //     0xa4b878: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4b87c: stur            x3, [x0, #7]
    // 0xa4b880: StoreField: r2->field_37 = r0
    //     0xa4b880: stur            w0, [x2, #0x37]
    // 0xa4b884: r0 = Instance_DragStartBehavior
    //     0xa4b884: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xa4b888: StoreField: r2->field_3b = r0
    //     0xa4b888: stur            w0, [x2, #0x3b]
    // 0xa4b88c: r0 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xa4b88c: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xa4b890: ldr             x0, [x0, #0xf00]
    // 0xa4b894: StoreField: r2->field_3f = r0
    //     0xa4b894: stur            w0, [x2, #0x3f]
    // 0xa4b898: r0 = Instance_Clip
    //     0xa4b898: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa4b89c: ldr             x0, [x0, #0x7c0]
    // 0xa4b8a0: StoreField: r2->field_47 = r0
    //     0xa4b8a0: stur            w0, [x2, #0x47]
    // 0xa4b8a4: r0 = Instance_HitTestBehavior
    //     0xa4b8a4: add             x0, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xa4b8a8: ldr             x0, [x0, #0x1c8]
    // 0xa4b8ac: StoreField: r2->field_4b = r0
    //     0xa4b8ac: stur            w0, [x2, #0x4b]
    // 0xa4b8b0: r0 = RegistryWidget()
    //     0xa4b8b0: bl              #0xa4b914  ; AllocateRegistryWidgetStub -> RegistryWidget (size=0x14)
    // 0xa4b8b4: ldur            x1, [fp, #-0x28]
    // 0xa4b8b8: StoreField: r0->field_f = r1
    //     0xa4b8b8: stur            w1, [x0, #0xf]
    // 0xa4b8bc: ldur            x1, [fp, #-8]
    // 0xa4b8c0: StoreField: r0->field_b = r1
    //     0xa4b8c0: stur            w1, [x0, #0xb]
    // 0xa4b8c4: LeaveFrame
    //     0xa4b8c4: mov             SP, fp
    //     0xa4b8c8: ldp             fp, lr, [SP], #0x10
    // 0xa4b8cc: ret
    //     0xa4b8cc: ret             
    // 0xa4b8d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4b8d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4b8d4: b               #0xa4b2d8
    // 0xa4b8d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b8dc: r9 = scrollController
    //     0xa4b8dc: add             x9, PP, #0x51, lsl #12  ; [pp+0x51190] Field <<EMAIL>>: late final (offset: 0x1c)
    //     0xa4b8e0: ldr             x9, [x9, #0x190]
    // 0xa4b8e4: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xa4b8e4: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xa4b8e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b8ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b8f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b8f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b8f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b8f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4b8fc: SaveReg d0
    //     0xa4b8fc: str             q0, [SP, #-0x10]!
    // 0xa4b900: SaveReg r2
    //     0xa4b900: str             x2, [SP, #-8]!
    // 0xa4b904: r0 = AllocateDouble()
    //     0xa4b904: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4b908: RestoreReg r2
    //     0xa4b908: ldr             x2, [SP], #8
    // 0xa4b90c: RestoreReg d0
    //     0xa4b90c: ldr             q0, [SP], #0x10
    // 0xa4b910: b               #0xa4b860
  }
  get _ _leadingSliverPadding(/* No info */) {
    // ** addr: 0xa4b92c, size: 0x3c
    // 0xa4b92c: EnterFrame
    //     0xa4b92c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4b930: mov             fp, SP
    // 0xa4b934: LoadField: r0 = r1->field_b
    //     0xa4b934: ldur            w0, [x1, #0xb]
    // 0xa4b938: DecompressPointer r0
    //     0xa4b938: add             x0, x0, HEAP, lsl #32
    // 0xa4b93c: cmp             w0, NULL
    // 0xa4b940: b.eq            #0xa4b964
    // 0xa4b944: r0 = EdgeInsets()
    //     0xa4b944: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xa4b948: StoreField: r0->field_7 = rZR
    //     0xa4b948: stur            xzr, [x0, #7]
    // 0xa4b94c: StoreField: r0->field_f = rZR
    //     0xa4b94c: stur            xzr, [x0, #0xf]
    // 0xa4b950: ArrayStore: r0[0] = rZR  ; List_8
    //     0xa4b950: stur            xzr, [x0, #0x17]
    // 0xa4b954: StoreField: r0->field_1f = rZR
    //     0xa4b954: stur            xzr, [x0, #0x1f]
    // 0xa4b958: LeaveFrame
    //     0xa4b958: mov             SP, fp
    //     0xa4b95c: ldp             fp, lr, [SP], #0x10
    // 0xa4b960: ret
    //     0xa4b960: ret             
    // 0xa4b964: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4b964: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa4b968, size: 0xb8
    // 0xa4b968: EnterFrame
    //     0xa4b968: stp             fp, lr, [SP, #-0x10]!
    //     0xa4b96c: mov             fp, SP
    // 0xa4b970: ldr             x0, [fp, #0x20]
    // 0xa4b974: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4b974: ldur            w1, [x0, #0x17]
    // 0xa4b978: DecompressPointer r1
    //     0xa4b978: add             x1, x1, HEAP, lsl #32
    // 0xa4b97c: CheckStackOverflow
    //     0xa4b97c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4b980: cmp             SP, x16
    //     0xa4b984: b.ls            #0xa4ba14
    // 0xa4b988: LoadField: r0 = r1->field_f
    //     0xa4b988: ldur            w0, [x1, #0xf]
    // 0xa4b98c: DecompressPointer r0
    //     0xa4b98c: add             x0, x0, HEAP, lsl #32
    // 0xa4b990: LoadField: r1 = r0->field_b
    //     0xa4b990: ldur            w1, [x0, #0xb]
    // 0xa4b994: DecompressPointer r1
    //     0xa4b994: add             x1, x1, HEAP, lsl #32
    // 0xa4b998: cmp             w1, NULL
    // 0xa4b99c: b.eq            #0xa4ba1c
    // 0xa4b9a0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4b9a0: ldur            w2, [x1, #0x17]
    // 0xa4b9a4: DecompressPointer r2
    //     0xa4b9a4: add             x2, x2, HEAP, lsl #32
    // 0xa4b9a8: cmp             w2, NULL
    // 0xa4b9ac: b.ne            #0xa4b9dc
    // 0xa4b9b0: ldr             x2, [fp, #0x10]
    // 0xa4b9b4: LoadField: r3 = r1->field_23
    //     0xa4b9b4: ldur            x3, [x1, #0x23]
    // 0xa4b9b8: r1 = LoadInt32Instr(r2)
    //     0xa4b9b8: sbfx            x1, x2, #1, #0x1f
    //     0xa4b9bc: tbz             w2, #0, #0xa4b9c4
    //     0xa4b9c0: ldur            x1, [x2, #7]
    // 0xa4b9c4: add             x2, x1, x3
    // 0xa4b9c8: add             x1, x2, #1
    // 0xa4b9cc: mov             x2, x1
    // 0xa4b9d0: mov             x1, x0
    // 0xa4b9d4: r0 = _buildItem()
    //     0xa4b9d4: bl              #0xa4bae4  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_buildItem
    // 0xa4b9d8: b               #0xa4ba08
    // 0xa4b9dc: ldr             x2, [fp, #0x10]
    // 0xa4b9e0: LoadField: r3 = r1->field_23
    //     0xa4b9e0: ldur            x3, [x1, #0x23]
    // 0xa4b9e4: lsl             x1, x3, #1
    // 0xa4b9e8: r3 = LoadInt32Instr(r2)
    //     0xa4b9e8: sbfx            x3, x2, #1, #0x1f
    //     0xa4b9ec: tbz             w2, #0, #0xa4b9f4
    //     0xa4b9f0: ldur            x3, [x2, #7]
    // 0xa4b9f4: add             x2, x3, x1
    // 0xa4b9f8: add             x1, x2, #1
    // 0xa4b9fc: mov             x2, x1
    // 0xa4ba00: mov             x1, x0
    // 0xa4ba04: r0 = _buildSeparatedListElement()
    //     0xa4ba04: bl              #0xa4ba20  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_buildSeparatedListElement
    // 0xa4ba08: LeaveFrame
    //     0xa4ba08: mov             SP, fp
    //     0xa4ba0c: ldp             fp, lr, [SP], #0x10
    // 0xa4ba10: ret
    //     0xa4ba10: ret             
    // 0xa4ba14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ba14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ba18: b               #0xa4b988
    // 0xa4ba1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ba1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildSeparatedListElement(/* No info */) {
    // ** addr: 0xa4ba20, size: 0xc4
    // 0xa4ba20: EnterFrame
    //     0xa4ba20: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ba24: mov             fp, SP
    // 0xa4ba28: AllocStack(0x18)
    //     0xa4ba28: sub             SP, SP, #0x18
    // 0xa4ba2c: CheckStackOverflow
    //     0xa4ba2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4ba30: cmp             SP, x16
    //     0xa4ba34: b.ls            #0xa4bad0
    // 0xa4ba38: tbnz            w2, #0, #0xa4ba58
    // 0xa4ba3c: r0 = 2
    //     0xa4ba3c: movz            x0, #0x2
    // 0xa4ba40: sdiv            x3, x2, x0
    // 0xa4ba44: mov             x2, x3
    // 0xa4ba48: r0 = _buildItem()
    //     0xa4ba48: bl              #0xa4bae4  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_buildItem
    // 0xa4ba4c: LeaveFrame
    //     0xa4ba4c: mov             SP, fp
    //     0xa4ba50: ldp             fp, lr, [SP], #0x10
    // 0xa4ba54: ret
    //     0xa4ba54: ret             
    // 0xa4ba58: r0 = 2
    //     0xa4ba58: movz            x0, #0x2
    // 0xa4ba5c: LoadField: r3 = r1->field_b
    //     0xa4ba5c: ldur            w3, [x1, #0xb]
    // 0xa4ba60: DecompressPointer r3
    //     0xa4ba60: add             x3, x3, HEAP, lsl #32
    // 0xa4ba64: cmp             w3, NULL
    // 0xa4ba68: b.eq            #0xa4bad8
    // 0xa4ba6c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xa4ba6c: ldur            w4, [x3, #0x17]
    // 0xa4ba70: DecompressPointer r4
    //     0xa4ba70: add             x4, x4, HEAP, lsl #32
    // 0xa4ba74: cmp             w4, NULL
    // 0xa4ba78: b.eq            #0xa4badc
    // 0xa4ba7c: LoadField: r3 = r1->field_f
    //     0xa4ba7c: ldur            w3, [x1, #0xf]
    // 0xa4ba80: DecompressPointer r3
    //     0xa4ba80: add             x3, x3, HEAP, lsl #32
    // 0xa4ba84: cmp             w3, NULL
    // 0xa4ba88: b.eq            #0xa4bae0
    // 0xa4ba8c: sdiv            x5, x2, x0
    // 0xa4ba90: r0 = BoxInt64Instr(r5)
    //     0xa4ba90: sbfiz           x0, x5, #1, #0x1f
    //     0xa4ba94: cmp             x5, x0, asr #1
    //     0xa4ba98: b.eq            #0xa4baa4
    //     0xa4ba9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4baa0: stur            x5, [x0, #7]
    // 0xa4baa4: stp             x3, x4, [SP, #8]
    // 0xa4baa8: str             x0, [SP]
    // 0xa4baac: mov             x0, x4
    // 0xa4bab0: ClosureCall
    //     0xa4bab0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa4bab4: ldur            x2, [x0, #0x1f]
    //     0xa4bab8: blr             x2
    // 0xa4babc: r0 = Instance_Divider
    //     0xa4babc: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xa4bac0: ldr             x0, [x0, #0xc28]
    // 0xa4bac4: LeaveFrame
    //     0xa4bac4: mov             SP, fp
    //     0xa4bac8: ldp             fp, lr, [SP], #0x10
    // 0xa4bacc: ret
    //     0xa4bacc: ret             
    // 0xa4bad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4bad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4bad4: b               #0xa4ba38
    // 0xa4bad8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4bad8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4badc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4badc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4bae0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4bae0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildItem(/* No info */) {
    // ** addr: 0xa4bae4, size: 0xe8
    // 0xa4bae4: EnterFrame
    //     0xa4bae4: stp             fp, lr, [SP, #-0x10]!
    //     0xa4bae8: mov             fp, SP
    // 0xa4baec: AllocStack(0x38)
    //     0xa4baec: sub             SP, SP, #0x38
    // 0xa4baf0: SetupParameters(_PositionedListState this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa4baf0: mov             x3, x1
    //     0xa4baf4: stur            x1, [fp, #-0x10]
    //     0xa4baf8: stur            x2, [fp, #-0x18]
    // 0xa4bafc: CheckStackOverflow
    //     0xa4bafc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4bb00: cmp             SP, x16
    //     0xa4bb04: b.ls            #0xa4bbbc
    // 0xa4bb08: r0 = BoxInt64Instr(r2)
    //     0xa4bb08: sbfiz           x0, x2, #1, #0x1f
    //     0xa4bb0c: cmp             x2, x0, asr #1
    //     0xa4bb10: b.eq            #0xa4bb1c
    //     0xa4bb14: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4bb18: stur            x2, [x0, #7]
    // 0xa4bb1c: r1 = <int>
    //     0xa4bb1c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xa4bb20: stur            x0, [fp, #-8]
    // 0xa4bb24: r0 = ValueKey()
    //     0xa4bb24: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xa4bb28: mov             x1, x0
    // 0xa4bb2c: ldur            x0, [fp, #-8]
    // 0xa4bb30: stur            x1, [fp, #-0x20]
    // 0xa4bb34: StoreField: r1->field_b = r0
    //     0xa4bb34: stur            w0, [x1, #0xb]
    // 0xa4bb38: ldur            x2, [fp, #-0x10]
    // 0xa4bb3c: LoadField: r3 = r2->field_b
    //     0xa4bb3c: ldur            w3, [x2, #0xb]
    // 0xa4bb40: DecompressPointer r3
    //     0xa4bb40: add             x3, x3, HEAP, lsl #32
    // 0xa4bb44: cmp             w3, NULL
    // 0xa4bb48: b.eq            #0xa4bbc4
    // 0xa4bb4c: LoadField: r4 = r2->field_f
    //     0xa4bb4c: ldur            w4, [x2, #0xf]
    // 0xa4bb50: DecompressPointer r4
    //     0xa4bb50: add             x4, x4, HEAP, lsl #32
    // 0xa4bb54: cmp             w4, NULL
    // 0xa4bb58: b.eq            #0xa4bbc8
    // 0xa4bb5c: LoadField: r2 = r3->field_13
    //     0xa4bb5c: ldur            w2, [x3, #0x13]
    // 0xa4bb60: DecompressPointer r2
    //     0xa4bb60: add             x2, x2, HEAP, lsl #32
    // 0xa4bb64: stp             x4, x2, [SP, #8]
    // 0xa4bb68: str             x0, [SP]
    // 0xa4bb6c: mov             x0, x2
    // 0xa4bb70: ClosureCall
    //     0xa4bb70: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa4bb74: ldur            x2, [x0, #0x1f]
    //     0xa4bb78: blr             x2
    // 0xa4bb7c: stur            x0, [fp, #-8]
    // 0xa4bb80: r0 = IndexedSemantics()
    //     0xa4bb80: bl              #0xa4bbd8  ; AllocateIndexedSemanticsStub -> IndexedSemantics (size=0x18)
    // 0xa4bb84: mov             x1, x0
    // 0xa4bb88: ldur            x0, [fp, #-0x18]
    // 0xa4bb8c: stur            x1, [fp, #-0x10]
    // 0xa4bb90: StoreField: r1->field_f = r0
    //     0xa4bb90: stur            x0, [x1, #0xf]
    // 0xa4bb94: ldur            x0, [fp, #-8]
    // 0xa4bb98: StoreField: r1->field_b = r0
    //     0xa4bb98: stur            w0, [x1, #0xb]
    // 0xa4bb9c: r0 = RegisteredElementWidget()
    //     0xa4bb9c: bl              #0xa4bbcc  ; AllocateRegisteredElementWidgetStub -> RegisteredElementWidget (size=0x10)
    // 0xa4bba0: ldur            x1, [fp, #-0x10]
    // 0xa4bba4: StoreField: r0->field_b = r1
    //     0xa4bba4: stur            w1, [x0, #0xb]
    // 0xa4bba8: ldur            x1, [fp, #-0x20]
    // 0xa4bbac: StoreField: r0->field_7 = r1
    //     0xa4bbac: stur            w1, [x0, #7]
    // 0xa4bbb0: LeaveFrame
    //     0xa4bbb0: mov             SP, fp
    //     0xa4bbb4: ldp             fp, lr, [SP], #0x10
    // 0xa4bbb8: ret
    //     0xa4bbb8: ret             
    // 0xa4bbbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4bbbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4bbc0: b               #0xa4bb08
    // 0xa4bbc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4bbc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4bbc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4bbc8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa4bbe4, size: 0xa8
    // 0xa4bbe4: EnterFrame
    //     0xa4bbe4: stp             fp, lr, [SP, #-0x10]!
    //     0xa4bbe8: mov             fp, SP
    // 0xa4bbec: ldr             x0, [fp, #0x20]
    // 0xa4bbf0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4bbf0: ldur            w1, [x0, #0x17]
    // 0xa4bbf4: DecompressPointer r1
    //     0xa4bbf4: add             x1, x1, HEAP, lsl #32
    // 0xa4bbf8: CheckStackOverflow
    //     0xa4bbf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4bbfc: cmp             SP, x16
    //     0xa4bc00: b.ls            #0xa4bc80
    // 0xa4bc04: LoadField: r0 = r1->field_f
    //     0xa4bc04: ldur            w0, [x1, #0xf]
    // 0xa4bc08: DecompressPointer r0
    //     0xa4bc08: add             x0, x0, HEAP, lsl #32
    // 0xa4bc0c: LoadField: r1 = r0->field_b
    //     0xa4bc0c: ldur            w1, [x0, #0xb]
    // 0xa4bc10: DecompressPointer r1
    //     0xa4bc10: add             x1, x1, HEAP, lsl #32
    // 0xa4bc14: cmp             w1, NULL
    // 0xa4bc18: b.eq            #0xa4bc88
    // 0xa4bc1c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4bc1c: ldur            w2, [x1, #0x17]
    // 0xa4bc20: DecompressPointer r2
    //     0xa4bc20: add             x2, x2, HEAP, lsl #32
    // 0xa4bc24: cmp             w2, NULL
    // 0xa4bc28: b.ne            #0xa4bc50
    // 0xa4bc2c: ldr             x2, [fp, #0x10]
    // 0xa4bc30: LoadField: r3 = r1->field_23
    //     0xa4bc30: ldur            x3, [x1, #0x23]
    // 0xa4bc34: r1 = LoadInt32Instr(r2)
    //     0xa4bc34: sbfx            x1, x2, #1, #0x1f
    //     0xa4bc38: tbz             w2, #0, #0xa4bc40
    //     0xa4bc3c: ldur            x1, [x2, #7]
    // 0xa4bc40: add             x2, x1, x3
    // 0xa4bc44: mov             x1, x0
    // 0xa4bc48: r0 = _buildItem()
    //     0xa4bc48: bl              #0xa4bae4  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_buildItem
    // 0xa4bc4c: b               #0xa4bc74
    // 0xa4bc50: ldr             x2, [fp, #0x10]
    // 0xa4bc54: LoadField: r3 = r1->field_23
    //     0xa4bc54: ldur            x3, [x1, #0x23]
    // 0xa4bc58: lsl             x1, x3, #1
    // 0xa4bc5c: r3 = LoadInt32Instr(r2)
    //     0xa4bc5c: sbfx            x3, x2, #1, #0x1f
    //     0xa4bc60: tbz             w2, #0, #0xa4bc68
    //     0xa4bc64: ldur            x3, [x2, #7]
    // 0xa4bc68: add             x2, x3, x1
    // 0xa4bc6c: mov             x1, x0
    // 0xa4bc70: r0 = _buildSeparatedListElement()
    //     0xa4bc70: bl              #0xa4ba20  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_buildSeparatedListElement
    // 0xa4bc74: LeaveFrame
    //     0xa4bc74: mov             SP, fp
    //     0xa4bc78: ldp             fp, lr, [SP], #0x10
    // 0xa4bc7c: ret
    //     0xa4bc7c: ret             
    // 0xa4bc80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4bc80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4bc84: b               #0xa4bc04
    // 0xa4bc88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4bc88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa4bc8c, size: 0xb8
    // 0xa4bc8c: EnterFrame
    //     0xa4bc8c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4bc90: mov             fp, SP
    // 0xa4bc94: ldr             x0, [fp, #0x20]
    // 0xa4bc98: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4bc98: ldur            w1, [x0, #0x17]
    // 0xa4bc9c: DecompressPointer r1
    //     0xa4bc9c: add             x1, x1, HEAP, lsl #32
    // 0xa4bca0: CheckStackOverflow
    //     0xa4bca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4bca4: cmp             SP, x16
    //     0xa4bca8: b.ls            #0xa4bd38
    // 0xa4bcac: LoadField: r0 = r1->field_f
    //     0xa4bcac: ldur            w0, [x1, #0xf]
    // 0xa4bcb0: DecompressPointer r0
    //     0xa4bcb0: add             x0, x0, HEAP, lsl #32
    // 0xa4bcb4: LoadField: r1 = r0->field_b
    //     0xa4bcb4: ldur            w1, [x0, #0xb]
    // 0xa4bcb8: DecompressPointer r1
    //     0xa4bcb8: add             x1, x1, HEAP, lsl #32
    // 0xa4bcbc: cmp             w1, NULL
    // 0xa4bcc0: b.eq            #0xa4bd40
    // 0xa4bcc4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4bcc4: ldur            w2, [x1, #0x17]
    // 0xa4bcc8: DecompressPointer r2
    //     0xa4bcc8: add             x2, x2, HEAP, lsl #32
    // 0xa4bccc: cmp             w2, NULL
    // 0xa4bcd0: b.ne            #0xa4bd00
    // 0xa4bcd4: ldr             x2, [fp, #0x10]
    // 0xa4bcd8: LoadField: r3 = r1->field_23
    //     0xa4bcd8: ldur            x3, [x1, #0x23]
    // 0xa4bcdc: r1 = LoadInt32Instr(r2)
    //     0xa4bcdc: sbfx            x1, x2, #1, #0x1f
    //     0xa4bce0: tbz             w2, #0, #0xa4bce8
    //     0xa4bce4: ldur            x1, [x2, #7]
    // 0xa4bce8: add             x2, x1, #1
    // 0xa4bcec: sub             x1, x3, x2
    // 0xa4bcf0: mov             x2, x1
    // 0xa4bcf4: mov             x1, x0
    // 0xa4bcf8: r0 = _buildItem()
    //     0xa4bcf8: bl              #0xa4bae4  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_buildItem
    // 0xa4bcfc: b               #0xa4bd2c
    // 0xa4bd00: ldr             x2, [fp, #0x10]
    // 0xa4bd04: LoadField: r3 = r1->field_23
    //     0xa4bd04: ldur            x3, [x1, #0x23]
    // 0xa4bd08: lsl             x1, x3, #1
    // 0xa4bd0c: r3 = LoadInt32Instr(r2)
    //     0xa4bd0c: sbfx            x3, x2, #1, #0x1f
    //     0xa4bd10: tbz             w2, #0, #0xa4bd18
    //     0xa4bd14: ldur            x3, [x2, #7]
    // 0xa4bd18: add             x2, x3, #1
    // 0xa4bd1c: sub             x3, x1, x2
    // 0xa4bd20: mov             x1, x0
    // 0xa4bd24: mov             x2, x3
    // 0xa4bd28: r0 = _buildSeparatedListElement()
    //     0xa4bd28: bl              #0xa4ba20  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_buildSeparatedListElement
    // 0xa4bd2c: LeaveFrame
    //     0xa4bd2c: mov             SP, fp
    //     0xa4bd30: ldp             fp, lr, [SP], #0x10
    // 0xa4bd34: ret
    //     0xa4bd34: ret             
    // 0xa4bd38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4bd38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4bd3c: b               #0xa4bcac
    // 0xa4bd40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4bd40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83d98, size: 0x70
    // 0xa83d98: EnterFrame
    //     0xa83d98: stp             fp, lr, [SP, #-0x10]!
    //     0xa83d9c: mov             fp, SP
    // 0xa83da0: AllocStack(0x8)
    //     0xa83da0: sub             SP, SP, #8
    // 0xa83da4: SetupParameters(_PositionedListState this /* r1 => r2 */)
    //     0xa83da4: mov             x2, x1
    // 0xa83da8: CheckStackOverflow
    //     0xa83da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83dac: cmp             SP, x16
    //     0xa83db0: b.ls            #0xa83df4
    // 0xa83db4: LoadField: r0 = r2->field_1b
    //     0xa83db4: ldur            w0, [x2, #0x1b]
    // 0xa83db8: DecompressPointer r0
    //     0xa83db8: add             x0, x0, HEAP, lsl #32
    // 0xa83dbc: r16 = Sentinel
    //     0xa83dbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa83dc0: cmp             w0, w16
    // 0xa83dc4: b.eq            #0xa83dfc
    // 0xa83dc8: stur            x0, [fp, #-8]
    // 0xa83dcc: r1 = Function '_schedulePositionNotificationUpdate@2689248967':.
    //     0xa83dcc: add             x1, PP, #0x51, lsl #12  ; [pp+0x51228] AnonymousClosure: (0x97f58c), in [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_schedulePositionNotificationUpdate (0x97ebd0)
    //     0xa83dd0: ldr             x1, [x1, #0x228]
    // 0xa83dd4: r0 = AllocateClosure()
    //     0xa83dd4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa83dd8: ldur            x1, [fp, #-8]
    // 0xa83ddc: mov             x2, x0
    // 0xa83de0: r0 = removeListener()
    //     0xa83de0: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xa83de4: r0 = Null
    //     0xa83de4: mov             x0, NULL
    // 0xa83de8: LeaveFrame
    //     0xa83de8: mov             SP, fp
    //     0xa83dec: ldp             fp, lr, [SP], #0x10
    // 0xa83df0: ret
    //     0xa83df0: ret             
    // 0xa83df4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83df4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83df8: b               #0xa83db4
    // 0xa83dfc: r9 = scrollController
    //     0xa83dfc: add             x9, PP, #0x51, lsl #12  ; [pp+0x51190] Field <<EMAIL>>: late final (offset: 0x1c)
    //     0xa83e00: ldr             x9, [x9, #0x190]
    // 0xa83e04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa83e04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _PositionedListState(/* No info */) {
    // ** addr: 0xa94e88, size: 0xcc
    // 0xa94e88: EnterFrame
    //     0xa94e88: stp             fp, lr, [SP, #-0x10]!
    //     0xa94e8c: mov             fp, SP
    // 0xa94e90: AllocStack(0x10)
    //     0xa94e90: sub             SP, SP, #0x10
    // 0xa94e94: r2 = Sentinel
    //     0xa94e94: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa94e98: r0 = false
    //     0xa94e98: add             x0, NULL, #0x30  ; false
    // 0xa94e9c: stur            x1, [fp, #-8]
    // 0xa94ea0: CheckStackOverflow
    //     0xa94ea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa94ea4: cmp             SP, x16
    //     0xa94ea8: b.ls            #0xa94f4c
    // 0xa94eac: StoreField: r1->field_1b = r2
    //     0xa94eac: stur            w2, [x1, #0x1b]
    // 0xa94eb0: StoreField: r1->field_1f = r0
    //     0xa94eb0: stur            w0, [x1, #0x1f]
    // 0xa94eb4: r0 = UniqueKey()
    //     0xa94eb4: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0xa94eb8: ldur            x2, [fp, #-8]
    // 0xa94ebc: StoreField: r2->field_13 = r0
    //     0xa94ebc: stur            w0, [x2, #0x13]
    //     0xa94ec0: ldurb           w16, [x2, #-1]
    //     0xa94ec4: ldurb           w17, [x0, #-1]
    //     0xa94ec8: and             x16, x17, x16, lsr #2
    //     0xa94ecc: tst             x16, HEAP, lsr #32
    //     0xa94ed0: b.eq            #0xa94ed8
    //     0xa94ed4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa94ed8: r1 = <Set<Element>?>
    //     0xa94ed8: add             x1, PP, #0x46, lsl #12  ; [pp+0x46d30] TypeArguments: <Set<Element>?>
    //     0xa94edc: ldr             x1, [x1, #0xd30]
    // 0xa94ee0: r0 = ValueNotifier()
    //     0xa94ee0: bl              #0x65a810  ; AllocateValueNotifierStub -> ValueNotifier<X0> (size=0x2c)
    // 0xa94ee4: stur            x0, [fp, #-0x10]
    // 0xa94ee8: StoreField: r0->field_7 = rZR
    //     0xa94ee8: stur            xzr, [x0, #7]
    // 0xa94eec: StoreField: r0->field_13 = rZR
    //     0xa94eec: stur            xzr, [x0, #0x13]
    // 0xa94ef0: StoreField: r0->field_1b = rZR
    //     0xa94ef0: stur            xzr, [x0, #0x1b]
    // 0xa94ef4: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0xa94ef4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa94ef8: ldr             x0, [x0, #0xca8]
    //     0xa94efc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa94f00: cmp             w0, w16
    //     0xa94f04: b.ne            #0xa94f10
    //     0xa94f08: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0xa94f0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa94f10: mov             x1, x0
    // 0xa94f14: ldur            x0, [fp, #-0x10]
    // 0xa94f18: StoreField: r0->field_f = r1
    //     0xa94f18: stur            w1, [x0, #0xf]
    // 0xa94f1c: ldur            x1, [fp, #-8]
    // 0xa94f20: ArrayStore: r1[0] = r0  ; List_4
    //     0xa94f20: stur            w0, [x1, #0x17]
    //     0xa94f24: ldurb           w16, [x1, #-1]
    //     0xa94f28: ldurb           w17, [x0, #-1]
    //     0xa94f2c: and             x16, x17, x16, lsr #2
    //     0xa94f30: tst             x16, HEAP, lsr #32
    //     0xa94f34: b.eq            #0xa94f3c
    //     0xa94f38: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa94f3c: r0 = Null
    //     0xa94f3c: mov             x0, NULL
    // 0xa94f40: LeaveFrame
    //     0xa94f40: mov             SP, fp
    //     0xa94f44: ldp             fp, lr, [SP], #0x10
    // 0xa94f48: ret
    //     0xa94f48: ret             
    // 0xa94f4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa94f4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa94f50: b               #0xa94eac
  }
}

// class id: 4696, size: 0x60, field offset: 0xc
//   const constructor, 
class PositionedList extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94e40, size: 0x48
    // 0xa94e40: EnterFrame
    //     0xa94e40: stp             fp, lr, [SP, #-0x10]!
    //     0xa94e44: mov             fp, SP
    // 0xa94e48: AllocStack(0x8)
    //     0xa94e48: sub             SP, SP, #8
    // 0xa94e4c: CheckStackOverflow
    //     0xa94e4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa94e50: cmp             SP, x16
    //     0xa94e54: b.ls            #0xa94e80
    // 0xa94e58: r1 = <PositionedList>
    //     0xa94e58: add             x1, PP, #0x46, lsl #12  ; [pp+0x46d28] TypeArguments: <PositionedList>
    //     0xa94e5c: ldr             x1, [x1, #0xd28]
    // 0xa94e60: r0 = _PositionedListState()
    //     0xa94e60: bl              #0xa94fec  ; Allocate_PositionedListStateStub -> _PositionedListState (size=0x24)
    // 0xa94e64: mov             x1, x0
    // 0xa94e68: stur            x0, [fp, #-8]
    // 0xa94e6c: r0 = _PositionedListState()
    //     0xa94e6c: bl              #0xa94e88  ; [package:scrollable_positioned_list/src/positioned_list.dart] _PositionedListState::_PositionedListState
    // 0xa94e70: ldur            x0, [fp, #-8]
    // 0xa94e74: LeaveFrame
    //     0xa94e74: mov             SP, fp
    //     0xa94e78: ldp             fp, lr, [SP], #0x10
    // 0xa94e7c: ret
    //     0xa94e7c: ret             
    // 0xa94e80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa94e80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa94e84: b               #0xa94e58
  }
}
