// lib: , url: package:scrollable_positioned_list/src/post_mount_callback.dart

// class id: 1051110, size: 0x8
class :: {
}

// class id: 4396, size: 0x40, field offset: 0x40
class _PostMountCallbackElement extends StatelessElement {

  _ mount(/* No info */) {
    // ** addr: 0x891850, size: 0xbc
    // 0x891850: EnterFrame
    //     0x891850: stp             fp, lr, [SP, #-0x10]!
    //     0x891854: mov             fp, SP
    // 0x891858: AllocStack(0x18)
    //     0x891858: sub             SP, SP, #0x18
    // 0x89185c: SetupParameters(_PostMountCallbackElement this /* r1 => r0, fp-0x8 */)
    //     0x89185c: mov             x0, x1
    //     0x891860: stur            x1, [fp, #-8]
    // 0x891864: CheckStackOverflow
    //     0x891864: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x891868: cmp             SP, x16
    //     0x89186c: b.ls            #0x8918fc
    // 0x891870: mov             x1, x0
    // 0x891874: r0 = mount()
    //     0x891874: bl              #0x89190c  ; [package:flutter/src/widgets/framework.dart] ComponentElement::mount
    // 0x891878: ldur            x0, [fp, #-8]
    // 0x89187c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x89187c: ldur            w3, [x0, #0x17]
    // 0x891880: DecompressPointer r3
    //     0x891880: add             x3, x3, HEAP, lsl #32
    // 0x891884: stur            x3, [fp, #-0x10]
    // 0x891888: cmp             w3, NULL
    // 0x89188c: b.eq            #0x891904
    // 0x891890: mov             x0, x3
    // 0x891894: r2 = Null
    //     0x891894: mov             x2, NULL
    // 0x891898: r1 = Null
    //     0x891898: mov             x1, NULL
    // 0x89189c: r4 = LoadClassIdInstr(r0)
    //     0x89189c: ldur            x4, [x0, #-1]
    //     0x8918a0: ubfx            x4, x4, #0xc, #0x14
    // 0x8918a4: r17 = 4917
    //     0x8918a4: movz            x17, #0x1335
    // 0x8918a8: cmp             x4, x17
    // 0x8918ac: b.eq            #0x8918c4
    // 0x8918b0: r8 = PostMountCallback
    //     0x8918b0: add             x8, PP, #0x51, lsl #12  ; [pp+0x51160] Type: PostMountCallback
    //     0x8918b4: ldr             x8, [x8, #0x160]
    // 0x8918b8: r3 = Null
    //     0x8918b8: add             x3, PP, #0x51, lsl #12  ; [pp+0x51168] Null
    //     0x8918bc: ldr             x3, [x3, #0x168]
    // 0x8918c0: r0 = DefaultTypeTest()
    //     0x8918c0: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8918c4: ldur            x0, [fp, #-0x10]
    // 0x8918c8: LoadField: r1 = r0->field_f
    //     0x8918c8: ldur            w1, [x0, #0xf]
    // 0x8918cc: DecompressPointer r1
    //     0x8918cc: add             x1, x1, HEAP, lsl #32
    // 0x8918d0: cmp             w1, NULL
    // 0x8918d4: b.eq            #0x891908
    // 0x8918d8: str             x1, [SP]
    // 0x8918dc: mov             x0, x1
    // 0x8918e0: ClosureCall
    //     0x8918e0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x8918e4: ldur            x2, [x0, #0x1f]
    //     0x8918e8: blr             x2
    // 0x8918ec: r0 = Null
    //     0x8918ec: mov             x0, NULL
    // 0x8918f0: LeaveFrame
    //     0x8918f0: mov             SP, fp
    //     0x8918f4: ldp             fp, lr, [SP], #0x10
    // 0x8918f8: ret
    //     0x8918f8: ret             
    // 0x8918fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8918fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x891900: b               #0x891870
    // 0x891904: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x891904: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x891908: r0 = NullErrorSharedWithoutFPURegs()
    //     0x891908: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}

// class id: 4917, size: 0x14, field offset: 0xc
//   const constructor, 
class PostMountCallback extends StatelessWidget {

  _ createElement(/* No info */) {
    // ** addr: 0xbbd35c, size: 0x50
    // 0xbbd35c: EnterFrame
    //     0xbbd35c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbd360: mov             fp, SP
    // 0xbbd364: AllocStack(0x8)
    //     0xbbd364: sub             SP, SP, #8
    // 0xbbd368: SetupParameters(PostMountCallback this /* r1 => r1, fp-0x8 */)
    //     0xbbd368: stur            x1, [fp, #-8]
    // 0xbbd36c: r0 = _PostMountCallbackElement()
    //     0xbbd36c: bl              #0xbbd3ac  ; Allocate_PostMountCallbackElementStub -> _PostMountCallbackElement (size=0x40)
    // 0xbbd370: r1 = Sentinel
    //     0xbbd370: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbd374: StoreField: r0->field_13 = r1
    //     0xbbd374: stur            w1, [x0, #0x13]
    // 0xbbd378: r1 = Instance__ElementLifecycle
    //     0xbbd378: add             x1, PP, #0xd, lsl #12  ; [pp+0xda00] Obj!_ElementLifecycle@e343c1
    //     0xbbd37c: ldr             x1, [x1, #0xa00]
    // 0xbbd380: StoreField: r0->field_23 = r1
    //     0xbbd380: stur            w1, [x0, #0x23]
    // 0xbbd384: r1 = false
    //     0xbbd384: add             x1, NULL, #0x30  ; false
    // 0xbbd388: StoreField: r0->field_2f = r1
    //     0xbbd388: stur            w1, [x0, #0x2f]
    // 0xbbd38c: r2 = true
    //     0xbbd38c: add             x2, NULL, #0x20  ; true
    // 0xbbd390: StoreField: r0->field_33 = r2
    //     0xbbd390: stur            w2, [x0, #0x33]
    // 0xbbd394: StoreField: r0->field_37 = r1
    //     0xbbd394: stur            w1, [x0, #0x37]
    // 0xbbd398: ldur            x1, [fp, #-8]
    // 0xbbd39c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbbd39c: stur            w1, [x0, #0x17]
    // 0xbbd3a0: LeaveFrame
    //     0xbbd3a0: mov             SP, fp
    //     0xbbd3a4: ldp             fp, lr, [SP], #0x10
    // 0xbbd3a8: ret
    //     0xbbd3a8: ret             
  }
}
