// lib: , url: package:scrollable_positioned_list/src/scroll_view.dart

// class id: 1051113, size: 0x8
class :: {
}

// class id: 5343, size: 0x60, field offset: 0x54
//   const constructor, 
class UnboundedCustomScrollView extends CustomScrollView {

  _ buildViewport(/* No info */) {
    // ** addr: 0xcf2d50, size: 0x90
    // 0xcf2d50: EnterFrame
    //     0xcf2d50: stp             fp, lr, [SP, #-0x10]!
    //     0xcf2d54: mov             fp, SP
    // 0xcf2d58: AllocStack(0x30)
    //     0xcf2d58: sub             SP, SP, #0x30
    // 0xcf2d5c: SetupParameters(dynamic _ /* r2 => r6, fp-0x18 */, dynamic _ /* r3 => r2, fp-0x20 */, dynamic _ /* r5 => r7, fp-0x28 */)
    //     0xcf2d5c: mov             x6, x2
    //     0xcf2d60: stur            x2, [fp, #-0x18]
    //     0xcf2d64: mov             x2, x3
    //     0xcf2d68: mov             x7, x5
    //     0xcf2d6c: stur            x3, [fp, #-0x20]
    //     0xcf2d70: stur            x5, [fp, #-0x28]
    // 0xcf2d74: CheckStackOverflow
    //     0xcf2d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcf2d78: cmp             SP, x16
    //     0xcf2d7c: b.ls            #0xcf2dd8
    // 0xcf2d80: LoadField: r3 = r1->field_33
    //     0xcf2d80: ldur            w3, [x1, #0x33]
    // 0xcf2d84: DecompressPointer r3
    //     0xcf2d84: add             x3, x3, HEAP, lsl #32
    // 0xcf2d88: stur            x3, [fp, #-0x10]
    // 0xcf2d8c: LoadField: r5 = r1->field_27
    //     0xcf2d8c: ldur            w5, [x1, #0x27]
    // 0xcf2d90: DecompressPointer r5
    //     0xcf2d90: add             x5, x5, HEAP, lsl #32
    // 0xcf2d94: stur            x5, [fp, #-8]
    // 0xcf2d98: LoadField: d0 = r1->field_57
    //     0xcf2d98: ldur            d0, [x1, #0x57]
    // 0xcf2d9c: stur            d0, [fp, #-0x30]
    // 0xcf2da0: r0 = UnboundedViewport()
    //     0xcf2da0: bl              #0xcf2ed0  ; AllocateUnboundedViewportStub -> UnboundedViewport (size=0x3c)
    // 0xcf2da4: mov             x1, x0
    // 0xcf2da8: ldur            d0, [fp, #-0x30]
    // 0xcf2dac: ldur            x2, [fp, #-0x20]
    // 0xcf2db0: ldur            x3, [fp, #-0x10]
    // 0xcf2db4: ldur            x5, [fp, #-8]
    // 0xcf2db8: ldur            x6, [fp, #-0x18]
    // 0xcf2dbc: ldur            x7, [fp, #-0x28]
    // 0xcf2dc0: stur            x0, [fp, #-8]
    // 0xcf2dc4: r0 = UnboundedViewport()
    //     0xcf2dc4: bl              #0xcf2de0  ; [package:scrollable_positioned_list/src/viewport.dart] UnboundedViewport::UnboundedViewport
    // 0xcf2dc8: ldur            x0, [fp, #-8]
    // 0xcf2dcc: LeaveFrame
    //     0xcf2dcc: mov             SP, fp
    //     0xcf2dd0: ldp             fp, lr, [SP], #0x10
    // 0xcf2dd4: ret
    //     0xcf2dd4: ret             
    // 0xcf2dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcf2dd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcf2ddc: b               #0xcf2d80
  }
}
