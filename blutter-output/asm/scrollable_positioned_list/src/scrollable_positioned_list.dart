// lib: , url: package:scrollable_positioned_list/src/scrollable_positioned_list.dart

// class id: 1051114, size: 0x8
class :: {
}

// class id: 505, size: 0x24, field offset: 0x8
class _ListDisplayDetails extends Object {

  _ _ListDisplayDetails(/* No info */) {
    // ** addr: 0xa95170, size: 0xdc
    // 0xa95170: EnterFrame
    //     0xa95170: stp             fp, lr, [SP, #-0x10]!
    //     0xa95174: mov             fp, SP
    // 0xa95178: AllocStack(0x20)
    //     0xa95178: sub             SP, SP, #0x20
    // 0xa9517c: SetupParameters(_ListDisplayDetails this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa9517c: mov             x0, x2
    //     0xa95180: stur            x1, [fp, #-8]
    //     0xa95184: stur            x2, [fp, #-0x10]
    // 0xa95188: CheckStackOverflow
    //     0xa95188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9518c: cmp             SP, x16
    //     0xa95190: b.ls            #0xa95244
    // 0xa95194: StoreField: r1->field_f = rZR
    //     0xa95194: stur            xzr, [x1, #0xf]
    // 0xa95198: ArrayStore: r1[0] = rZR  ; List_8
    //     0xa95198: stur            xzr, [x1, #0x17]
    // 0xa9519c: r0 = ItemPositionsNotifier()
    //     0xa9519c: bl              #0x83d24c  ; AllocateItemPositionsNotifierStub -> ItemPositionsNotifier (size=0xc)
    // 0xa951a0: mov             x1, x0
    // 0xa951a4: stur            x0, [fp, #-0x18]
    // 0xa951a8: r0 = ItemPositionsNotifier()
    //     0xa951a8: bl              #0x83d0b8  ; [package:scrollable_positioned_list/src/item_positions_notifier.dart] ItemPositionsNotifier::ItemPositionsNotifier
    // 0xa951ac: ldur            x0, [fp, #-0x18]
    // 0xa951b0: ldur            x1, [fp, #-8]
    // 0xa951b4: StoreField: r1->field_7 = r0
    //     0xa951b4: stur            w0, [x1, #7]
    //     0xa951b8: ldurb           w16, [x1, #-1]
    //     0xa951bc: ldurb           w17, [x0, #-1]
    //     0xa951c0: and             x16, x17, x16, lsr #2
    //     0xa951c4: tst             x16, HEAP, lsr #32
    //     0xa951c8: b.eq            #0xa951d0
    //     0xa951cc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa951d0: r0 = ScrollController()
    //     0xa951d0: bl              #0x6852cc  ; AllocateScrollControllerStub -> ScrollController (size=0x40)
    // 0xa951d4: stur            x0, [fp, #-0x18]
    // 0xa951d8: r16 = false
    //     0xa951d8: add             x16, NULL, #0x30  ; false
    // 0xa951dc: str             x16, [SP]
    // 0xa951e0: mov             x1, x0
    // 0xa951e4: r4 = const [0, 0x2, 0x1, 0x1, keepScrollOffset, 0x1, null]
    //     0xa951e4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33440] List(7) [0, 0x2, 0x1, 0x1, "keepScrollOffset", 0x1, Null]
    //     0xa951e8: ldr             x4, [x4, #0x440]
    // 0xa951ec: r0 = ScrollController()
    //     0xa951ec: bl              #0x685160  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0xa951f0: ldur            x0, [fp, #-0x18]
    // 0xa951f4: ldur            x1, [fp, #-8]
    // 0xa951f8: StoreField: r1->field_b = r0
    //     0xa951f8: stur            w0, [x1, #0xb]
    //     0xa951fc: ldurb           w16, [x1, #-1]
    //     0xa95200: ldurb           w17, [x0, #-1]
    //     0xa95204: and             x16, x17, x16, lsr #2
    //     0xa95208: tst             x16, HEAP, lsr #32
    //     0xa9520c: b.eq            #0xa95214
    //     0xa95210: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa95214: ldur            x0, [fp, #-0x10]
    // 0xa95218: StoreField: r1->field_1f = r0
    //     0xa95218: stur            w0, [x1, #0x1f]
    //     0xa9521c: ldurb           w16, [x1, #-1]
    //     0xa95220: ldurb           w17, [x0, #-1]
    //     0xa95224: and             x16, x17, x16, lsr #2
    //     0xa95228: tst             x16, HEAP, lsr #32
    //     0xa9522c: b.eq            #0xa95234
    //     0xa95230: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa95234: r0 = Null
    //     0xa95234: mov             x0, NULL
    // 0xa95238: LeaveFrame
    //     0xa95238: mov             SP, fp
    //     0xa9523c: ldp             fp, lr, [SP], #0x10
    // 0xa95240: ret
    //     0xa95240: ret             
    // 0xa95244: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa95244: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa95248: b               #0xa95194
  }
}

// class id: 507, size: 0xc, field offset: 0x8
class ItemScrollController extends Object {

  _ scrollTo(/* No info */) {
    // ** addr: 0x8be4e0, size: 0x44
    // 0x8be4e0: EnterFrame
    //     0x8be4e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8be4e4: mov             fp, SP
    // 0x8be4e8: CheckStackOverflow
    //     0x8be4e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8be4ec: cmp             SP, x16
    //     0x8be4f0: b.ls            #0x8be518
    // 0x8be4f4: LoadField: r0 = r1->field_7
    //     0x8be4f4: ldur            w0, [x1, #7]
    // 0x8be4f8: DecompressPointer r0
    //     0x8be4f8: add             x0, x0, HEAP, lsl #32
    // 0x8be4fc: cmp             w0, NULL
    // 0x8be500: b.eq            #0x8be520
    // 0x8be504: mov             x1, x0
    // 0x8be508: r0 = _scrollTo()
    //     0x8be508: bl              #0x8be524  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_scrollTo
    // 0x8be50c: LeaveFrame
    //     0x8be50c: mov             SP, fp
    //     0x8be510: ldp             fp, lr, [SP], #0x10
    // 0x8be514: ret
    //     0x8be514: ret             
    // 0x8be518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8be518: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8be51c: b               #0x8be4f4
    // 0x8be520: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8be520: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ jumpTo(/* No info */) {
    // ** addr: 0x901360, size: 0x48
    // 0x901360: EnterFrame
    //     0x901360: stp             fp, lr, [SP, #-0x10]!
    //     0x901364: mov             fp, SP
    // 0x901368: CheckStackOverflow
    //     0x901368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90136c: cmp             SP, x16
    //     0x901370: b.ls            #0x90139c
    // 0x901374: LoadField: r0 = r1->field_7
    //     0x901374: ldur            w0, [x1, #7]
    // 0x901378: DecompressPointer r0
    //     0x901378: add             x0, x0, HEAP, lsl #32
    // 0x90137c: cmp             w0, NULL
    // 0x901380: b.eq            #0x9013a4
    // 0x901384: mov             x1, x0
    // 0x901388: r0 = _jumpTo()
    //     0x901388: bl              #0x9013a8  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_jumpTo
    // 0x90138c: r0 = Null
    //     0x90138c: mov             x0, NULL
    // 0x901390: LeaveFrame
    //     0x901390: mov             SP, fp
    //     0x901394: ldp             fp, lr, [SP], #0x10
    // 0x901398: ret
    //     0x901398: ret             
    // 0x90139c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90139c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9013a0: b               #0x901374
    // 0x9013a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9013a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _detach(/* No info */) {
    // ** addr: 0x92bcf8, size: 0xc
    // 0x92bcf8: StoreField: r1->field_7 = rNULL
    //     0x92bcf8: stur            NULL, [x1, #7]
    // 0x92bcfc: r0 = Null
    //     0x92bcfc: mov             x0, NULL
    // 0x92bd00: ret
    //     0x92bd00: ret             
  }
}

// class id: 4091, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ScrollablePositionedListState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6fa52c, size: 0x184
    // 0x6fa52c: EnterFrame
    //     0x6fa52c: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa530: mov             fp, SP
    // 0x6fa534: AllocStack(0x20)
    //     0x6fa534: sub             SP, SP, #0x20
    // 0x6fa538: SetupParameters(__ScrollablePositionedListState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6fa538: mov             x0, x1
    //     0x6fa53c: stur            x1, [fp, #-8]
    //     0x6fa540: stur            x2, [fp, #-0x10]
    // 0x6fa544: CheckStackOverflow
    //     0x6fa544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa548: cmp             SP, x16
    //     0x6fa54c: b.ls            #0x6fa6a0
    // 0x6fa550: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6fa550: ldur            w1, [x0, #0x17]
    // 0x6fa554: DecompressPointer r1
    //     0x6fa554: add             x1, x1, HEAP, lsl #32
    // 0x6fa558: cmp             w1, NULL
    // 0x6fa55c: b.ne            #0x6fa568
    // 0x6fa560: mov             x1, x0
    // 0x6fa564: r0 = _updateTickerModeNotifier()
    //     0x6fa564: bl              #0x6fa6d4  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] __ScrollablePositionedListState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6fa568: ldur            x0, [fp, #-8]
    // 0x6fa56c: LoadField: r1 = r0->field_13
    //     0x6fa56c: ldur            w1, [x0, #0x13]
    // 0x6fa570: DecompressPointer r1
    //     0x6fa570: add             x1, x1, HEAP, lsl #32
    // 0x6fa574: cmp             w1, NULL
    // 0x6fa578: b.ne            #0x6fa610
    // 0x6fa57c: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x6fa57c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fa580: ldr             x0, [x0, #0x778]
    //     0x6fa584: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fa588: cmp             w0, w16
    //     0x6fa58c: b.ne            #0x6fa598
    //     0x6fa590: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x6fa594: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fa598: r1 = <_WidgetTicker>
    //     0x6fa598: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c0] TypeArguments: <_WidgetTicker>
    //     0x6fa59c: ldr             x1, [x1, #0x8c0]
    // 0x6fa5a0: stur            x0, [fp, #-0x18]
    // 0x6fa5a4: r0 = _Set()
    //     0x6fa5a4: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x6fa5a8: mov             x1, x0
    // 0x6fa5ac: ldur            x0, [fp, #-0x18]
    // 0x6fa5b0: stur            x1, [fp, #-0x20]
    // 0x6fa5b4: StoreField: r1->field_1b = r0
    //     0x6fa5b4: stur            w0, [x1, #0x1b]
    // 0x6fa5b8: StoreField: r1->field_b = rZR
    //     0x6fa5b8: stur            wzr, [x1, #0xb]
    // 0x6fa5bc: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x6fa5bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fa5c0: ldr             x0, [x0, #0x780]
    //     0x6fa5c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fa5c8: cmp             w0, w16
    //     0x6fa5cc: b.ne            #0x6fa5d8
    //     0x6fa5d0: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x6fa5d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fa5d8: mov             x1, x0
    // 0x6fa5dc: ldur            x0, [fp, #-0x20]
    // 0x6fa5e0: StoreField: r0->field_f = r1
    //     0x6fa5e0: stur            w1, [x0, #0xf]
    // 0x6fa5e4: StoreField: r0->field_13 = rZR
    //     0x6fa5e4: stur            wzr, [x0, #0x13]
    // 0x6fa5e8: ArrayStore: r0[0] = rZR  ; List_4
    //     0x6fa5e8: stur            wzr, [x0, #0x17]
    // 0x6fa5ec: ldur            x1, [fp, #-8]
    // 0x6fa5f0: StoreField: r1->field_13 = r0
    //     0x6fa5f0: stur            w0, [x1, #0x13]
    //     0x6fa5f4: ldurb           w16, [x1, #-1]
    //     0x6fa5f8: ldurb           w17, [x0, #-1]
    //     0x6fa5fc: and             x16, x17, x16, lsr #2
    //     0x6fa600: tst             x16, HEAP, lsr #32
    //     0x6fa604: b.eq            #0x6fa60c
    //     0x6fa608: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6fa60c: b               #0x6fa614
    // 0x6fa610: mov             x1, x0
    // 0x6fa614: ldur            x0, [fp, #-0x10]
    // 0x6fa618: r0 = _WidgetTicker()
    //     0x6fa618: bl              #0x6f03ec  ; Allocate_WidgetTickerStub -> _WidgetTicker (size=0x20)
    // 0x6fa61c: mov             x3, x0
    // 0x6fa620: ldur            x2, [fp, #-8]
    // 0x6fa624: stur            x3, [fp, #-0x18]
    // 0x6fa628: StoreField: r3->field_1b = r2
    //     0x6fa628: stur            w2, [x3, #0x1b]
    // 0x6fa62c: r0 = false
    //     0x6fa62c: add             x0, NULL, #0x30  ; false
    // 0x6fa630: StoreField: r3->field_b = r0
    //     0x6fa630: stur            w0, [x3, #0xb]
    // 0x6fa634: ldur            x0, [fp, #-0x10]
    // 0x6fa638: StoreField: r3->field_13 = r0
    //     0x6fa638: stur            w0, [x3, #0x13]
    // 0x6fa63c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6fa63c: ldur            w1, [x2, #0x17]
    // 0x6fa640: DecompressPointer r1
    //     0x6fa640: add             x1, x1, HEAP, lsl #32
    // 0x6fa644: cmp             w1, NULL
    // 0x6fa648: b.eq            #0x6fa6a8
    // 0x6fa64c: r0 = LoadClassIdInstr(r1)
    //     0x6fa64c: ldur            x0, [x1, #-1]
    //     0x6fa650: ubfx            x0, x0, #0xc, #0x14
    // 0x6fa654: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6fa654: movz            x17, #0x276f
    //     0x6fa658: movk            x17, #0x1, lsl #16
    //     0x6fa65c: add             lr, x0, x17
    //     0x6fa660: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa664: blr             lr
    // 0x6fa668: eor             x2, x0, #0x10
    // 0x6fa66c: ldur            x1, [fp, #-0x18]
    // 0x6fa670: r0 = muted=()
    //     0x6fa670: bl              #0x6efbf4  ; [package:flutter/src/scheduler/ticker.dart] Ticker::muted=
    // 0x6fa674: ldur            x0, [fp, #-8]
    // 0x6fa678: LoadField: r1 = r0->field_13
    //     0x6fa678: ldur            w1, [x0, #0x13]
    // 0x6fa67c: DecompressPointer r1
    //     0x6fa67c: add             x1, x1, HEAP, lsl #32
    // 0x6fa680: cmp             w1, NULL
    // 0x6fa684: b.eq            #0x6fa6ac
    // 0x6fa688: ldur            x2, [fp, #-0x18]
    // 0x6fa68c: r0 = add()
    //     0x6fa68c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x6fa690: ldur            x0, [fp, #-0x18]
    // 0x6fa694: LeaveFrame
    //     0x6fa694: mov             SP, fp
    //     0x6fa698: ldp             fp, lr, [SP], #0x10
    // 0x6fa69c: ret
    //     0x6fa69c: ret             
    // 0x6fa6a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa6a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa6a4: b               #0x6fa550
    // 0x6fa6a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa6a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fa6ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa6ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6fa6d4, size: 0x124
    // 0x6fa6d4: EnterFrame
    //     0x6fa6d4: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa6d8: mov             fp, SP
    // 0x6fa6dc: AllocStack(0x18)
    //     0x6fa6dc: sub             SP, SP, #0x18
    // 0x6fa6e0: SetupParameters(__ScrollablePositionedListState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6fa6e0: mov             x2, x1
    //     0x6fa6e4: stur            x1, [fp, #-8]
    // 0x6fa6e8: CheckStackOverflow
    //     0x6fa6e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa6ec: cmp             SP, x16
    //     0x6fa6f0: b.ls            #0x6fa7ec
    // 0x6fa6f4: LoadField: r1 = r2->field_f
    //     0x6fa6f4: ldur            w1, [x2, #0xf]
    // 0x6fa6f8: DecompressPointer r1
    //     0x6fa6f8: add             x1, x1, HEAP, lsl #32
    // 0x6fa6fc: cmp             w1, NULL
    // 0x6fa700: b.eq            #0x6fa7f4
    // 0x6fa704: r0 = getNotifier()
    //     0x6fa704: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6fa708: mov             x3, x0
    // 0x6fa70c: ldur            x0, [fp, #-8]
    // 0x6fa710: stur            x3, [fp, #-0x18]
    // 0x6fa714: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6fa714: ldur            w4, [x0, #0x17]
    // 0x6fa718: DecompressPointer r4
    //     0x6fa718: add             x4, x4, HEAP, lsl #32
    // 0x6fa71c: stur            x4, [fp, #-0x10]
    // 0x6fa720: cmp             w3, w4
    // 0x6fa724: b.ne            #0x6fa738
    // 0x6fa728: r0 = Null
    //     0x6fa728: mov             x0, NULL
    // 0x6fa72c: LeaveFrame
    //     0x6fa72c: mov             SP, fp
    //     0x6fa730: ldp             fp, lr, [SP], #0x10
    // 0x6fa734: ret
    //     0x6fa734: ret             
    // 0x6fa738: cmp             w4, NULL
    // 0x6fa73c: b.eq            #0x6fa780
    // 0x6fa740: mov             x2, x0
    // 0x6fa744: r1 = Function '_updateTickers@364311458':.
    //     0x6fa744: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c8] AnonymousClosure: (0x6fa7f8), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] __ScrollablePositionedListState&State&TickerProviderStateMixin::_updateTickers (0x6fa830)
    //     0x6fa748: ldr             x1, [x1, #0x8c8]
    // 0x6fa74c: r0 = AllocateClosure()
    //     0x6fa74c: bl              #0xec1630  ; AllocateClosureStub
    // 0x6fa750: ldur            x1, [fp, #-0x10]
    // 0x6fa754: r2 = LoadClassIdInstr(r1)
    //     0x6fa754: ldur            x2, [x1, #-1]
    //     0x6fa758: ubfx            x2, x2, #0xc, #0x14
    // 0x6fa75c: mov             x16, x0
    // 0x6fa760: mov             x0, x2
    // 0x6fa764: mov             x2, x16
    // 0x6fa768: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6fa768: movz            x17, #0xbf5c
    //     0x6fa76c: add             lr, x0, x17
    //     0x6fa770: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa774: blr             lr
    // 0x6fa778: ldur            x0, [fp, #-8]
    // 0x6fa77c: ldur            x3, [fp, #-0x18]
    // 0x6fa780: mov             x2, x0
    // 0x6fa784: r1 = Function '_updateTickers@364311458':.
    //     0x6fa784: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c8] AnonymousClosure: (0x6fa7f8), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] __ScrollablePositionedListState&State&TickerProviderStateMixin::_updateTickers (0x6fa830)
    //     0x6fa788: ldr             x1, [x1, #0x8c8]
    // 0x6fa78c: r0 = AllocateClosure()
    //     0x6fa78c: bl              #0xec1630  ; AllocateClosureStub
    // 0x6fa790: ldur            x3, [fp, #-0x18]
    // 0x6fa794: r1 = LoadClassIdInstr(r3)
    //     0x6fa794: ldur            x1, [x3, #-1]
    //     0x6fa798: ubfx            x1, x1, #0xc, #0x14
    // 0x6fa79c: mov             x2, x0
    // 0x6fa7a0: mov             x0, x1
    // 0x6fa7a4: mov             x1, x3
    // 0x6fa7a8: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6fa7a8: movz            x17, #0xc407
    //     0x6fa7ac: add             lr, x0, x17
    //     0x6fa7b0: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa7b4: blr             lr
    // 0x6fa7b8: ldur            x0, [fp, #-0x18]
    // 0x6fa7bc: ldur            x1, [fp, #-8]
    // 0x6fa7c0: ArrayStore: r1[0] = r0  ; List_4
    //     0x6fa7c0: stur            w0, [x1, #0x17]
    //     0x6fa7c4: ldurb           w16, [x1, #-1]
    //     0x6fa7c8: ldurb           w17, [x0, #-1]
    //     0x6fa7cc: and             x16, x17, x16, lsr #2
    //     0x6fa7d0: tst             x16, HEAP, lsr #32
    //     0x6fa7d4: b.eq            #0x6fa7dc
    //     0x6fa7d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6fa7dc: r0 = Null
    //     0x6fa7dc: mov             x0, NULL
    // 0x6fa7e0: LeaveFrame
    //     0x6fa7e0: mov             SP, fp
    //     0x6fa7e4: ldp             fp, lr, [SP], #0x10
    // 0x6fa7e8: ret
    //     0x6fa7e8: ret             
    // 0x6fa7ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa7ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa7f0: b               #0x6fa6f4
    // 0x6fa7f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa7f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTickers(dynamic) {
    // ** addr: 0x6fa7f8, size: 0x38
    // 0x6fa7f8: EnterFrame
    //     0x6fa7f8: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa7fc: mov             fp, SP
    // 0x6fa800: ldr             x0, [fp, #0x10]
    // 0x6fa804: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6fa804: ldur            w1, [x0, #0x17]
    // 0x6fa808: DecompressPointer r1
    //     0x6fa808: add             x1, x1, HEAP, lsl #32
    // 0x6fa80c: CheckStackOverflow
    //     0x6fa80c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa810: cmp             SP, x16
    //     0x6fa814: b.ls            #0x6fa828
    // 0x6fa818: r0 = _updateTickers()
    //     0x6fa818: bl              #0x6fa830  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] __ScrollablePositionedListState&State&TickerProviderStateMixin::_updateTickers
    // 0x6fa81c: LeaveFrame
    //     0x6fa81c: mov             SP, fp
    //     0x6fa820: ldp             fp, lr, [SP], #0x10
    // 0x6fa824: ret
    //     0x6fa824: ret             
    // 0x6fa828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa828: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa82c: b               #0x6fa818
  }
  _ _updateTickers(/* No info */) {
    // ** addr: 0x6fa830, size: 0x164
    // 0x6fa830: EnterFrame
    //     0x6fa830: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa834: mov             fp, SP
    // 0x6fa838: AllocStack(0x20)
    //     0x6fa838: sub             SP, SP, #0x20
    // 0x6fa83c: SetupParameters(__ScrollablePositionedListState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6fa83c: mov             x2, x1
    //     0x6fa840: stur            x1, [fp, #-8]
    // 0x6fa844: CheckStackOverflow
    //     0x6fa844: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa848: cmp             SP, x16
    //     0x6fa84c: b.ls            #0x6fa97c
    // 0x6fa850: LoadField: r0 = r2->field_13
    //     0x6fa850: ldur            w0, [x2, #0x13]
    // 0x6fa854: DecompressPointer r0
    //     0x6fa854: add             x0, x0, HEAP, lsl #32
    // 0x6fa858: cmp             w0, NULL
    // 0x6fa85c: b.eq            #0x6fa96c
    // 0x6fa860: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6fa860: ldur            w1, [x2, #0x17]
    // 0x6fa864: DecompressPointer r1
    //     0x6fa864: add             x1, x1, HEAP, lsl #32
    // 0x6fa868: cmp             w1, NULL
    // 0x6fa86c: b.eq            #0x6fa984
    // 0x6fa870: r0 = LoadClassIdInstr(r1)
    //     0x6fa870: ldur            x0, [x1, #-1]
    //     0x6fa874: ubfx            x0, x0, #0xc, #0x14
    // 0x6fa878: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6fa878: movz            x17, #0x276f
    //     0x6fa87c: movk            x17, #0x1, lsl #16
    //     0x6fa880: add             lr, x0, x17
    //     0x6fa884: ldr             lr, [x21, lr, lsl #3]
    //     0x6fa888: blr             lr
    // 0x6fa88c: eor             x2, x0, #0x10
    // 0x6fa890: ldur            x0, [fp, #-8]
    // 0x6fa894: stur            x2, [fp, #-0x10]
    // 0x6fa898: LoadField: r1 = r0->field_13
    //     0x6fa898: ldur            w1, [x0, #0x13]
    // 0x6fa89c: DecompressPointer r1
    //     0x6fa89c: add             x1, x1, HEAP, lsl #32
    // 0x6fa8a0: cmp             w1, NULL
    // 0x6fa8a4: b.eq            #0x6fa988
    // 0x6fa8a8: r0 = iterator()
    //     0x6fa8a8: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x6fa8ac: stur            x0, [fp, #-0x18]
    // 0x6fa8b0: LoadField: r2 = r0->field_7
    //     0x6fa8b0: ldur            w2, [x0, #7]
    // 0x6fa8b4: DecompressPointer r2
    //     0x6fa8b4: add             x2, x2, HEAP, lsl #32
    // 0x6fa8b8: stur            x2, [fp, #-8]
    // 0x6fa8bc: ldur            x3, [fp, #-0x10]
    // 0x6fa8c0: CheckStackOverflow
    //     0x6fa8c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa8c4: cmp             SP, x16
    //     0x6fa8c8: b.ls            #0x6fa98c
    // 0x6fa8cc: mov             x1, x0
    // 0x6fa8d0: r0 = moveNext()
    //     0x6fa8d0: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x6fa8d4: tbnz            w0, #4, #0x6fa96c
    // 0x6fa8d8: ldur            x3, [fp, #-0x18]
    // 0x6fa8dc: LoadField: r4 = r3->field_33
    //     0x6fa8dc: ldur            w4, [x3, #0x33]
    // 0x6fa8e0: DecompressPointer r4
    //     0x6fa8e0: add             x4, x4, HEAP, lsl #32
    // 0x6fa8e4: stur            x4, [fp, #-0x20]
    // 0x6fa8e8: cmp             w4, NULL
    // 0x6fa8ec: b.ne            #0x6fa920
    // 0x6fa8f0: mov             x0, x4
    // 0x6fa8f4: ldur            x2, [fp, #-8]
    // 0x6fa8f8: r1 = Null
    //     0x6fa8f8: mov             x1, NULL
    // 0x6fa8fc: cmp             w2, NULL
    // 0x6fa900: b.eq            #0x6fa920
    // 0x6fa904: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6fa904: ldur            w4, [x2, #0x17]
    // 0x6fa908: DecompressPointer r4
    //     0x6fa908: add             x4, x4, HEAP, lsl #32
    // 0x6fa90c: r8 = X0
    //     0x6fa90c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6fa910: LoadField: r9 = r4->field_7
    //     0x6fa910: ldur            x9, [x4, #7]
    // 0x6fa914: r3 = Null
    //     0x6fa914: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d8d0] Null
    //     0x6fa918: ldr             x3, [x3, #0x8d0]
    // 0x6fa91c: blr             x9
    // 0x6fa920: ldur            x2, [fp, #-0x10]
    // 0x6fa924: ldur            x0, [fp, #-0x20]
    // 0x6fa928: LoadField: r1 = r0->field_b
    //     0x6fa928: ldur            w1, [x0, #0xb]
    // 0x6fa92c: DecompressPointer r1
    //     0x6fa92c: add             x1, x1, HEAP, lsl #32
    // 0x6fa930: cmp             w2, w1
    // 0x6fa934: b.eq            #0x6fa960
    // 0x6fa938: StoreField: r0->field_b = r2
    //     0x6fa938: stur            w2, [x0, #0xb]
    // 0x6fa93c: tbnz            w2, #4, #0x6fa94c
    // 0x6fa940: mov             x1, x0
    // 0x6fa944: r0 = unscheduleTick()
    //     0x6fa944: bl              #0x656684  ; [package:flutter/src/scheduler/ticker.dart] Ticker::unscheduleTick
    // 0x6fa948: b               #0x6fa960
    // 0x6fa94c: mov             x1, x0
    // 0x6fa950: r0 = shouldScheduleTick()
    //     0x6fa950: bl              #0x655b90  ; [package:flutter/src/scheduler/ticker.dart] Ticker::shouldScheduleTick
    // 0x6fa954: tbnz            w0, #4, #0x6fa960
    // 0x6fa958: ldur            x1, [fp, #-0x20]
    // 0x6fa95c: r0 = scheduleTick()
    //     0x6fa95c: bl              #0x655924  ; [package:flutter/src/scheduler/ticker.dart] Ticker::scheduleTick
    // 0x6fa960: ldur            x0, [fp, #-0x18]
    // 0x6fa964: ldur            x2, [fp, #-8]
    // 0x6fa968: b               #0x6fa8bc
    // 0x6fa96c: r0 = Null
    //     0x6fa96c: mov             x0, NULL
    // 0x6fa970: LeaveFrame
    //     0x6fa970: mov             SP, fp
    //     0x6fa974: ldp             fp, lr, [SP], #0x10
    // 0x6fa978: ret
    //     0x6fa978: ret             
    // 0x6fa97c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa97c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa980: b               #0x6fa850
    // 0x6fa984: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa984: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fa988: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fa988: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fa98c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fa98c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fa990: b               #0x6fa8cc
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83ec0, size: 0x94
    // 0xa83ec0: EnterFrame
    //     0xa83ec0: stp             fp, lr, [SP, #-0x10]!
    //     0xa83ec4: mov             fp, SP
    // 0xa83ec8: AllocStack(0x10)
    //     0xa83ec8: sub             SP, SP, #0x10
    // 0xa83ecc: SetupParameters(__ScrollablePositionedListState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa83ecc: mov             x0, x1
    //     0xa83ed0: stur            x1, [fp, #-0x10]
    // 0xa83ed4: CheckStackOverflow
    //     0xa83ed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83ed8: cmp             SP, x16
    //     0xa83edc: b.ls            #0xa83f4c
    // 0xa83ee0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa83ee0: ldur            w3, [x0, #0x17]
    // 0xa83ee4: DecompressPointer r3
    //     0xa83ee4: add             x3, x3, HEAP, lsl #32
    // 0xa83ee8: stur            x3, [fp, #-8]
    // 0xa83eec: cmp             w3, NULL
    // 0xa83ef0: b.ne            #0xa83efc
    // 0xa83ef4: mov             x1, x0
    // 0xa83ef8: b               #0xa83f38
    // 0xa83efc: mov             x2, x0
    // 0xa83f00: r1 = Function '_updateTickers@364311458':.
    //     0xa83f00: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c8] AnonymousClosure: (0x6fa7f8), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] __ScrollablePositionedListState&State&TickerProviderStateMixin::_updateTickers (0x6fa830)
    //     0xa83f04: ldr             x1, [x1, #0x8c8]
    // 0xa83f08: r0 = AllocateClosure()
    //     0xa83f08: bl              #0xec1630  ; AllocateClosureStub
    // 0xa83f0c: ldur            x1, [fp, #-8]
    // 0xa83f10: r2 = LoadClassIdInstr(r1)
    //     0xa83f10: ldur            x2, [x1, #-1]
    //     0xa83f14: ubfx            x2, x2, #0xc, #0x14
    // 0xa83f18: mov             x16, x0
    // 0xa83f1c: mov             x0, x2
    // 0xa83f20: mov             x2, x16
    // 0xa83f24: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa83f24: movz            x17, #0xbf5c
    //     0xa83f28: add             lr, x0, x17
    //     0xa83f2c: ldr             lr, [x21, lr, lsl #3]
    //     0xa83f30: blr             lr
    // 0xa83f34: ldur            x1, [fp, #-0x10]
    // 0xa83f38: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa83f38: stur            NULL, [x1, #0x17]
    // 0xa83f3c: r0 = Null
    //     0xa83f3c: mov             x0, NULL
    // 0xa83f40: LeaveFrame
    //     0xa83f40: mov             SP, fp
    //     0xa83f44: ldp             fp, lr, [SP], #0x10
    // 0xa83f48: ret
    //     0xa83f48: ret             
    // 0xa83f4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83f4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83f50: b               #0xa83ee0
  }
  _ activate(/* No info */) {
    // ** addr: 0xa86014, size: 0x48
    // 0xa86014: EnterFrame
    //     0xa86014: stp             fp, lr, [SP, #-0x10]!
    //     0xa86018: mov             fp, SP
    // 0xa8601c: AllocStack(0x8)
    //     0xa8601c: sub             SP, SP, #8
    // 0xa86020: SetupParameters(__ScrollablePositionedListState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa86020: mov             x0, x1
    //     0xa86024: stur            x1, [fp, #-8]
    // 0xa86028: CheckStackOverflow
    //     0xa86028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8602c: cmp             SP, x16
    //     0xa86030: b.ls            #0xa86054
    // 0xa86034: mov             x1, x0
    // 0xa86038: r0 = _updateTickerModeNotifier()
    //     0xa86038: bl              #0x6fa6d4  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] __ScrollablePositionedListState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa8603c: ldur            x1, [fp, #-8]
    // 0xa86040: r0 = _updateTickers()
    //     0xa86040: bl              #0x6fa830  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] __ScrollablePositionedListState&State&TickerProviderStateMixin::_updateTickers
    // 0xa86044: r0 = Null
    //     0xa86044: mov             x0, NULL
    // 0xa86048: LeaveFrame
    //     0xa86048: mov             SP, fp
    //     0xa8604c: ldp             fp, lr, [SP], #0x10
    // 0xa86050: ret
    //     0xa86050: ret             
    // 0xa86054: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa86054: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa86058: b               #0xa86034
  }
}

// class id: 4092, size: 0x3c, field offset: 0x1c
class _ScrollablePositionedListState extends __ScrollablePositionedListState&State&TickerProviderStateMixin {

  _ _scrollTo(/* No info */) async {
    // ** addr: 0x8be524, size: 0x290
    // 0x8be524: EnterFrame
    //     0x8be524: stp             fp, lr, [SP, #-0x10]!
    //     0x8be528: mov             fp, SP
    // 0x8be52c: AllocStack(0x40)
    //     0x8be52c: sub             SP, SP, #0x40
    // 0x8be530: SetupParameters(_ScrollablePositionedListState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8be530: stur            NULL, [fp, #-8]
    //     0x8be534: stur            x1, [fp, #-0x10]
    //     0x8be538: stur            x2, [fp, #-0x18]
    // 0x8be53c: CheckStackOverflow
    //     0x8be53c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8be540: cmp             SP, x16
    //     0x8be544: b.ls            #0x8be7a4
    // 0x8be548: r1 = 3
    //     0x8be548: movz            x1, #0x3
    // 0x8be54c: r0 = AllocateContext()
    //     0x8be54c: bl              #0xec126c  ; AllocateContextStub
    // 0x8be550: mov             x3, x0
    // 0x8be554: ldur            x2, [fp, #-0x10]
    // 0x8be558: stur            x3, [fp, #-0x20]
    // 0x8be55c: StoreField: r3->field_f = r2
    //     0x8be55c: stur            w2, [x3, #0xf]
    // 0x8be560: ldur            x4, [fp, #-0x18]
    // 0x8be564: r0 = BoxInt64Instr(r4)
    //     0x8be564: sbfiz           x0, x4, #1, #0x1f
    //     0x8be568: cmp             x4, x0, asr #1
    //     0x8be56c: b.eq            #0x8be578
    //     0x8be570: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8be574: stur            x4, [x0, #7]
    // 0x8be578: StoreField: r3->field_13 = r0
    //     0x8be578: stur            w0, [x3, #0x13]
    // 0x8be57c: InitAsync() -> Future<void?>
    //     0x8be57c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8be580: bl              #0x661298  ; InitAsyncStub
    // 0x8be584: ldur            x2, [fp, #-0x20]
    // 0x8be588: LoadField: r0 = r2->field_13
    //     0x8be588: ldur            w0, [x2, #0x13]
    // 0x8be58c: DecompressPointer r0
    //     0x8be58c: add             x0, x0, HEAP, lsl #32
    // 0x8be590: ldur            x3, [fp, #-0x10]
    // 0x8be594: LoadField: r1 = r3->field_b
    //     0x8be594: ldur            w1, [x3, #0xb]
    // 0x8be598: DecompressPointer r1
    //     0x8be598: add             x1, x1, HEAP, lsl #32
    // 0x8be59c: cmp             w1, NULL
    // 0x8be5a0: b.eq            #0x8be7ac
    // 0x8be5a4: LoadField: r4 = r1->field_b
    //     0x8be5a4: ldur            x4, [x1, #0xb]
    // 0x8be5a8: sub             x5, x4, #1
    // 0x8be5ac: r1 = LoadInt32Instr(r0)
    //     0x8be5ac: sbfx            x1, x0, #1, #0x1f
    //     0x8be5b0: tbz             w0, #0, #0x8be5b8
    //     0x8be5b4: ldur            x1, [x0, #7]
    // 0x8be5b8: cmp             x1, x5
    // 0x8be5bc: b.le            #0x8be5fc
    // 0x8be5c0: r0 = BoxInt64Instr(r5)
    //     0x8be5c0: sbfiz           x0, x5, #1, #0x1f
    //     0x8be5c4: cmp             x5, x0, asr #1
    //     0x8be5c8: b.eq            #0x8be5d4
    //     0x8be5cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8be5d0: stur            x5, [x0, #7]
    // 0x8be5d4: StoreField: r2->field_13 = r0
    //     0x8be5d4: stur            w0, [x2, #0x13]
    //     0x8be5d8: tbz             w0, #0, #0x8be5f4
    //     0x8be5dc: ldurb           w16, [x2, #-1]
    //     0x8be5e0: ldurb           w17, [x0, #-1]
    //     0x8be5e4: and             x16, x17, x16, lsr #2
    //     0x8be5e8: tst             x16, HEAP, lsr #32
    //     0x8be5ec: b.eq            #0x8be5f4
    //     0x8be5f0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8be5f4: mov             x0, x5
    // 0x8be5f8: b               #0x8be60c
    // 0x8be5fc: r1 = LoadInt32Instr(r0)
    //     0x8be5fc: sbfx            x1, x0, #1, #0x1f
    //     0x8be600: tbz             w0, #0, #0x8be608
    //     0x8be604: ldur            x1, [x0, #7]
    // 0x8be608: mov             x0, x1
    // 0x8be60c: LoadField: r1 = r3->field_2b
    //     0x8be60c: ldur            w1, [x3, #0x2b]
    // 0x8be610: DecompressPointer r1
    //     0x8be610: add             x1, x1, HEAP, lsl #32
    // 0x8be614: tbnz            w1, #4, #0x8be784
    // 0x8be618: r1 = <void?>
    //     0x8be618: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8be61c: r0 = _Future()
    //     0x8be61c: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x8be620: stur            x0, [fp, #-0x28]
    // 0x8be624: StoreField: r0->field_b = rZR
    //     0x8be624: stur            xzr, [x0, #0xb]
    // 0x8be628: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x8be628: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8be62c: ldr             x0, [x0, #0x7a0]
    //     0x8be630: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8be634: cmp             w0, w16
    //     0x8be638: b.ne            #0x8be644
    //     0x8be63c: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x8be640: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8be644: mov             x1, x0
    // 0x8be648: ldur            x0, [fp, #-0x28]
    // 0x8be64c: StoreField: r0->field_13 = r1
    //     0x8be64c: stur            w1, [x0, #0x13]
    // 0x8be650: r1 = <void?>
    //     0x8be650: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8be654: r0 = _AsyncCompleter()
    //     0x8be654: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x8be658: ldur            x2, [fp, #-0x28]
    // 0x8be65c: StoreField: r0->field_b = r2
    //     0x8be65c: stur            w2, [x0, #0xb]
    // 0x8be660: ldur            x3, [fp, #-0x20]
    // 0x8be664: ArrayStore: r3[0] = r0  ; List_4
    //     0x8be664: stur            w0, [x3, #0x17]
    //     0x8be668: ldurb           w16, [x3, #-1]
    //     0x8be66c: ldurb           w17, [x0, #-1]
    //     0x8be670: and             x16, x17, x16, lsr #2
    //     0x8be674: tst             x16, HEAP, lsr #32
    //     0x8be678: b.eq            #0x8be680
    //     0x8be67c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8be680: r16 = true
    //     0x8be680: add             x16, NULL, #0x20  ; true
    // 0x8be684: str             x16, [SP]
    // 0x8be688: ldur            x1, [fp, #-0x10]
    // 0x8be68c: r4 = const [0, 0x2, 0x1, 0x1, canceled, 0x1, null]
    //     0x8be68c: ldr             x4, [PP, #0x7008]  ; [pp+0x7008] List(7) [0, 0x2, 0x1, 0x1, "canceled", 0x1, Null]
    // 0x8be690: r0 = _stopScroll()
    //     0x8be690: bl              #0x8bf524  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_stopScroll
    // 0x8be694: r0 = LoadStaticField(0x958)
    //     0x8be694: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8be698: ldr             x0, [x0, #0x12b0]
    // 0x8be69c: cmp             w0, NULL
    // 0x8be6a0: b.eq            #0x8be7b0
    // 0x8be6a4: LoadField: r3 = r0->field_53
    //     0x8be6a4: ldur            w3, [x0, #0x53]
    // 0x8be6a8: DecompressPointer r3
    //     0x8be6a8: add             x3, x3, HEAP, lsl #32
    // 0x8be6ac: stur            x3, [fp, #-0x38]
    // 0x8be6b0: LoadField: r0 = r3->field_7
    //     0x8be6b0: ldur            w0, [x3, #7]
    // 0x8be6b4: DecompressPointer r0
    //     0x8be6b4: add             x0, x0, HEAP, lsl #32
    // 0x8be6b8: ldur            x2, [fp, #-0x20]
    // 0x8be6bc: stur            x0, [fp, #-0x30]
    // 0x8be6c0: r1 = Function '<anonymous closure>':.
    //     0x8be6c0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c338] AnonymousClosure: (0x8bf778), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_scrollTo (0x8be524)
    //     0x8be6c4: ldr             x1, [x1, #0x338]
    // 0x8be6c8: r0 = AllocateClosure()
    //     0x8be6c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8be6cc: ldur            x2, [fp, #-0x30]
    // 0x8be6d0: mov             x3, x0
    // 0x8be6d4: r1 = Null
    //     0x8be6d4: mov             x1, NULL
    // 0x8be6d8: stur            x3, [fp, #-0x30]
    // 0x8be6dc: cmp             w2, NULL
    // 0x8be6e0: b.eq            #0x8be700
    // 0x8be6e4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8be6e4: ldur            w4, [x2, #0x17]
    // 0x8be6e8: DecompressPointer r4
    //     0x8be6e8: add             x4, x4, HEAP, lsl #32
    // 0x8be6ec: r8 = X0
    //     0x8be6ec: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8be6f0: LoadField: r9 = r4->field_7
    //     0x8be6f0: ldur            x9, [x4, #7]
    // 0x8be6f4: r3 = Null
    //     0x8be6f4: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c340] Null
    //     0x8be6f8: ldr             x3, [x3, #0x340]
    // 0x8be6fc: blr             x9
    // 0x8be700: ldur            x0, [fp, #-0x38]
    // 0x8be704: LoadField: r1 = r0->field_b
    //     0x8be704: ldur            w1, [x0, #0xb]
    // 0x8be708: LoadField: r2 = r0->field_f
    //     0x8be708: ldur            w2, [x0, #0xf]
    // 0x8be70c: DecompressPointer r2
    //     0x8be70c: add             x2, x2, HEAP, lsl #32
    // 0x8be710: LoadField: r3 = r2->field_b
    //     0x8be710: ldur            w3, [x2, #0xb]
    // 0x8be714: r2 = LoadInt32Instr(r1)
    //     0x8be714: sbfx            x2, x1, #1, #0x1f
    // 0x8be718: stur            x2, [fp, #-0x18]
    // 0x8be71c: r1 = LoadInt32Instr(r3)
    //     0x8be71c: sbfx            x1, x3, #1, #0x1f
    // 0x8be720: cmp             x2, x1
    // 0x8be724: b.ne            #0x8be730
    // 0x8be728: mov             x1, x0
    // 0x8be72c: r0 = _growToNextCapacity()
    //     0x8be72c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8be730: ldur            x0, [fp, #-0x38]
    // 0x8be734: ldur            x2, [fp, #-0x18]
    // 0x8be738: add             x1, x2, #1
    // 0x8be73c: lsl             x3, x1, #1
    // 0x8be740: StoreField: r0->field_b = r3
    //     0x8be740: stur            w3, [x0, #0xb]
    // 0x8be744: LoadField: r1 = r0->field_f
    //     0x8be744: ldur            w1, [x0, #0xf]
    // 0x8be748: DecompressPointer r1
    //     0x8be748: add             x1, x1, HEAP, lsl #32
    // 0x8be74c: ldur            x0, [fp, #-0x30]
    // 0x8be750: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8be750: add             x25, x1, x2, lsl #2
    //     0x8be754: add             x25, x25, #0xf
    //     0x8be758: str             w0, [x25]
    //     0x8be75c: tbz             w0, #0, #0x8be778
    //     0x8be760: ldurb           w16, [x1, #-1]
    //     0x8be764: ldurb           w17, [x0, #-1]
    //     0x8be768: and             x16, x17, x16, lsr #2
    //     0x8be76c: tst             x16, HEAP, lsr #32
    //     0x8be770: b.eq            #0x8be778
    //     0x8be774: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8be778: ldur            x0, [fp, #-0x28]
    // 0x8be77c: r0 = Await()
    //     0x8be77c: bl              #0x661044  ; AwaitStub
    // 0x8be780: b               #0x8be79c
    // 0x8be784: ldur            x1, [fp, #-0x10]
    // 0x8be788: mov             x2, x0
    // 0x8be78c: r0 = _startScroll()
    //     0x8be78c: bl              #0x8be7b4  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_startScroll
    // 0x8be790: mov             x1, x0
    // 0x8be794: stur            x1, [fp, #-0x10]
    // 0x8be798: r0 = Await()
    //     0x8be798: bl              #0x661044  ; AwaitStub
    // 0x8be79c: r0 = Null
    //     0x8be79c: mov             x0, NULL
    // 0x8be7a0: r0 = ReturnAsyncNotFuture()
    //     0x8be7a0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8be7a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8be7a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8be7a8: b               #0x8be548
    // 0x8be7ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8be7ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8be7b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8be7b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _startScroll(/* No info */) async {
    // ** addr: 0x8be7b4, size: 0x410
    // 0x8be7b4: EnterFrame
    //     0x8be7b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8be7b8: mov             fp, SP
    // 0x8be7bc: AllocStack(0x60)
    //     0x8be7bc: sub             SP, SP, #0x60
    // 0x8be7c0: SetupParameters(_ScrollablePositionedListState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8be7c0: stur            NULL, [fp, #-8]
    //     0x8be7c4: stur            x1, [fp, #-0x10]
    //     0x8be7c8: stur            x2, [fp, #-0x18]
    // 0x8be7cc: CheckStackOverflow
    //     0x8be7cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8be7d0: cmp             SP, x16
    //     0x8be7d4: b.ls            #0x8beb9c
    // 0x8be7d8: r1 = 6
    //     0x8be7d8: movz            x1, #0x6
    // 0x8be7dc: r0 = AllocateContext()
    //     0x8be7dc: bl              #0xec126c  ; AllocateContextStub
    // 0x8be7e0: mov             x3, x0
    // 0x8be7e4: ldur            x2, [fp, #-0x10]
    // 0x8be7e8: stur            x3, [fp, #-0x20]
    // 0x8be7ec: StoreField: r3->field_f = r2
    //     0x8be7ec: stur            w2, [x3, #0xf]
    // 0x8be7f0: ldur            x4, [fp, #-0x18]
    // 0x8be7f4: r0 = BoxInt64Instr(r4)
    //     0x8be7f4: sbfiz           x0, x4, #1, #0x1f
    //     0x8be7f8: cmp             x4, x0, asr #1
    //     0x8be7fc: b.eq            #0x8be808
    //     0x8be800: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8be804: stur            x4, [x0, #7]
    // 0x8be808: StoreField: r3->field_13 = r0
    //     0x8be808: stur            w0, [x3, #0x13]
    // 0x8be80c: InitAsync() -> Future<void?>
    //     0x8be80c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8be810: bl              #0x661298  ; InitAsyncStub
    // 0x8be814: ldur            x0, [fp, #-0x20]
    // 0x8be818: LoadField: r1 = r0->field_13
    //     0x8be818: ldur            w1, [x0, #0x13]
    // 0x8be81c: DecompressPointer r1
    //     0x8be81c: add             x1, x1, HEAP, lsl #32
    // 0x8be820: ldur            x3, [fp, #-0x10]
    // 0x8be824: LoadField: r2 = r3->field_1b
    //     0x8be824: ldur            w2, [x3, #0x1b]
    // 0x8be828: DecompressPointer r2
    //     0x8be828: add             x2, x2, HEAP, lsl #32
    // 0x8be82c: LoadField: r4 = r2->field_f
    //     0x8be82c: ldur            x4, [x2, #0xf]
    // 0x8be830: r5 = LoadInt32Instr(r1)
    //     0x8be830: sbfx            x5, x1, #1, #0x1f
    //     0x8be834: tbz             w1, #0, #0x8be83c
    //     0x8be838: ldur            x5, [x1, #7]
    // 0x8be83c: cmp             x5, x4
    // 0x8be840: b.le            #0x8be84c
    // 0x8be844: r1 = 1
    //     0x8be844: movz            x1, #0x1
    // 0x8be848: b               #0x8be850
    // 0x8be84c: r1 = -1
    //     0x8be84c: movn            x1, #0
    // 0x8be850: lsl             x4, x1, #1
    // 0x8be854: ArrayStore: r0[0] = r4  ; List_4
    //     0x8be854: stur            w4, [x0, #0x17]
    // 0x8be858: LoadField: r1 = r2->field_7
    //     0x8be858: ldur            w1, [x2, #7]
    // 0x8be85c: DecompressPointer r1
    //     0x8be85c: add             x1, x1, HEAP, lsl #32
    // 0x8be860: LoadField: r2 = r1->field_7
    //     0x8be860: ldur            w2, [x1, #7]
    // 0x8be864: DecompressPointer r2
    //     0x8be864: add             x2, x2, HEAP, lsl #32
    // 0x8be868: LoadField: r4 = r2->field_27
    //     0x8be868: ldur            w4, [x2, #0x27]
    // 0x8be86c: DecompressPointer r4
    //     0x8be86c: add             x4, x4, HEAP, lsl #32
    // 0x8be870: mov             x2, x0
    // 0x8be874: stur            x4, [fp, #-0x28]
    // 0x8be878: r1 = Function '<anonymous closure>':.
    //     0x8be878: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c350] AnonymousClosure: (0x8bf4e8), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_startScroll (0x8be7b4)
    //     0x8be87c: ldr             x1, [x1, #0x350]
    // 0x8be880: r0 = AllocateClosure()
    //     0x8be880: bl              #0xec1630  ; AllocateClosureStub
    // 0x8be884: r16 = <ItemPosition>
    //     0x8be884: add             x16, PP, #0x31, lsl #12  ; [pp+0x316e8] TypeArguments: <ItemPosition>
    //     0x8be888: ldr             x16, [x16, #0x6e8]
    // 0x8be88c: ldur            lr, [fp, #-0x28]
    // 0x8be890: stp             lr, x16, [SP, #8]
    // 0x8be894: str             x0, [SP]
    // 0x8be898: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8be898: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8be89c: r0 = IterableExtension.firstWhereOrNull()
    //     0x8be89c: bl              #0x7379ec  ; [package:collection/src/iterable_extensions.dart] ::IterableExtension.firstWhereOrNull
    // 0x8be8a0: cmp             w0, NULL
    // 0x8be8a4: b.eq            #0x8be9a4
    // 0x8be8a8: ldur            x2, [fp, #-0x10]
    // 0x8be8ac: LoadField: d0 = r0->field_f
    //     0x8be8ac: ldur            d0, [x0, #0xf]
    // 0x8be8b0: stur            d0, [fp, #-0x40]
    // 0x8be8b4: LoadField: r0 = r2->field_1b
    //     0x8be8b4: ldur            w0, [x2, #0x1b]
    // 0x8be8b8: DecompressPointer r0
    //     0x8be8b8: add             x0, x0, HEAP, lsl #32
    // 0x8be8bc: LoadField: r1 = r0->field_b
    //     0x8be8bc: ldur            w1, [x0, #0xb]
    // 0x8be8c0: DecompressPointer r1
    //     0x8be8c0: add             x1, x1, HEAP, lsl #32
    // 0x8be8c4: LoadField: r0 = r1->field_3b
    //     0x8be8c4: ldur            w0, [x1, #0x3b]
    // 0x8be8c8: DecompressPointer r0
    //     0x8be8c8: add             x0, x0, HEAP, lsl #32
    // 0x8be8cc: mov             x1, x0
    // 0x8be8d0: r0 = single()
    //     0x8be8d0: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8be8d4: LoadField: r1 = r0->field_43
    //     0x8be8d4: ldur            w1, [x0, #0x43]
    // 0x8be8d8: DecompressPointer r1
    //     0x8be8d8: add             x1, x1, HEAP, lsl #32
    // 0x8be8dc: cmp             w1, NULL
    // 0x8be8e0: b.eq            #0x8beba4
    // 0x8be8e4: LoadField: d0 = r1->field_7
    //     0x8be8e4: ldur            d0, [x1, #7]
    // 0x8be8e8: ldur            d1, [fp, #-0x40]
    // 0x8be8ec: fmul            d2, d1, d0
    // 0x8be8f0: ldur            x0, [fp, #-0x10]
    // 0x8be8f4: stur            d2, [fp, #-0x48]
    // 0x8be8f8: LoadField: r1 = r0->field_1b
    //     0x8be8f8: ldur            w1, [x0, #0x1b]
    // 0x8be8fc: DecompressPointer r1
    //     0x8be8fc: add             x1, x1, HEAP, lsl #32
    // 0x8be900: LoadField: r2 = r1->field_b
    //     0x8be900: ldur            w2, [x1, #0xb]
    // 0x8be904: DecompressPointer r2
    //     0x8be904: add             x2, x2, HEAP, lsl #32
    // 0x8be908: stur            x2, [fp, #-0x28]
    // 0x8be90c: LoadField: r1 = r2->field_3b
    //     0x8be90c: ldur            w1, [x2, #0x3b]
    // 0x8be910: DecompressPointer r1
    //     0x8be910: add             x1, x1, HEAP, lsl #32
    // 0x8be914: r0 = single()
    //     0x8be914: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8be918: LoadField: r1 = r0->field_3f
    //     0x8be918: ldur            w1, [x0, #0x3f]
    // 0x8be91c: DecompressPointer r1
    //     0x8be91c: add             x1, x1, HEAP, lsl #32
    // 0x8be920: cmp             w1, NULL
    // 0x8be924: b.eq            #0x8beba8
    // 0x8be928: LoadField: d0 = r1->field_7
    //     0x8be928: ldur            d0, [x1, #7]
    // 0x8be92c: ldur            d1, [fp, #-0x48]
    // 0x8be930: fadd            d2, d0, d1
    // 0x8be934: ldur            x0, [fp, #-0x10]
    // 0x8be938: stur            d2, [fp, #-0x40]
    // 0x8be93c: LoadField: r1 = r0->field_1b
    //     0x8be93c: ldur            w1, [x0, #0x1b]
    // 0x8be940: DecompressPointer r1
    //     0x8be940: add             x1, x1, HEAP, lsl #32
    // 0x8be944: LoadField: r0 = r1->field_b
    //     0x8be944: ldur            w0, [x1, #0xb]
    // 0x8be948: DecompressPointer r0
    //     0x8be948: add             x0, x0, HEAP, lsl #32
    // 0x8be94c: LoadField: r1 = r0->field_3b
    //     0x8be94c: ldur            w1, [x0, #0x3b]
    // 0x8be950: DecompressPointer r1
    //     0x8be950: add             x1, x1, HEAP, lsl #32
    // 0x8be954: r0 = single()
    //     0x8be954: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8be958: LoadField: r1 = r0->field_43
    //     0x8be958: ldur            w1, [x0, #0x43]
    // 0x8be95c: DecompressPointer r1
    //     0x8be95c: add             x1, x1, HEAP, lsl #32
    // 0x8be960: cmp             w1, NULL
    // 0x8be964: b.eq            #0x8bebac
    // 0x8be968: LoadField: d0 = r1->field_7
    //     0x8be968: ldur            d0, [x1, #7]
    // 0x8be96c: d1 = 0.000000
    //     0x8be96c: eor             v1.16b, v1.16b, v1.16b
    // 0x8be970: fmul            d2, d0, d1
    // 0x8be974: ldur            d0, [fp, #-0x40]
    // 0x8be978: fsub            d1, d0, d2
    // 0x8be97c: ldur            x1, [fp, #-0x28]
    // 0x8be980: mov             v0.16b, v1.16b
    // 0x8be984: r2 = Instance__Linear
    //     0x8be984: ldr             x2, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0x8be988: r3 = Instance_Duration
    //     0x8be988: add             x3, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x8be98c: ldr             x3, [x3, #0x368]
    // 0x8be990: r0 = animateTo()
    //     0x8be990: bl              #0x678090  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::animateTo
    // 0x8be994: mov             x1, x0
    // 0x8be998: stur            x1, [fp, #-0x28]
    // 0x8be99c: r0 = Await()
    //     0x8be99c: bl              #0x661044  ; AwaitStub
    // 0x8be9a0: b               #0x8beb94
    // 0x8be9a4: ldur            x0, [fp, #-0x10]
    // 0x8be9a8: ldur            x2, [fp, #-0x20]
    // 0x8be9ac: LoadField: r1 = r0->field_1b
    //     0x8be9ac: ldur            w1, [x0, #0x1b]
    // 0x8be9b0: DecompressPointer r1
    //     0x8be9b0: add             x1, x1, HEAP, lsl #32
    // 0x8be9b4: LoadField: r3 = r1->field_b
    //     0x8be9b4: ldur            w3, [x1, #0xb]
    // 0x8be9b8: DecompressPointer r3
    //     0x8be9b8: add             x3, x3, HEAP, lsl #32
    // 0x8be9bc: LoadField: r1 = r3->field_3b
    //     0x8be9bc: ldur            w1, [x3, #0x3b]
    // 0x8be9c0: DecompressPointer r1
    //     0x8be9c0: add             x1, x1, HEAP, lsl #32
    // 0x8be9c4: r0 = single()
    //     0x8be9c4: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8be9c8: LoadField: r1 = r0->field_43
    //     0x8be9c8: ldur            w1, [x0, #0x43]
    // 0x8be9cc: DecompressPointer r1
    //     0x8be9cc: add             x1, x1, HEAP, lsl #32
    // 0x8be9d0: cmp             w1, NULL
    // 0x8be9d4: b.eq            #0x8bebb0
    // 0x8be9d8: LoadField: d0 = r1->field_7
    //     0x8be9d8: ldur            d0, [x1, #7]
    // 0x8be9dc: d1 = 2.000000
    //     0x8be9dc: fmov            d1, #2.00000000
    // 0x8be9e0: fmul            d2, d0, d1
    // 0x8be9e4: r0 = inline_Allocate_Double()
    //     0x8be9e4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8be9e8: add             x0, x0, #0x10
    //     0x8be9ec: cmp             x1, x0
    //     0x8be9f0: b.ls            #0x8bebb4
    //     0x8be9f4: str             x0, [THR, #0x50]  ; THR::top
    //     0x8be9f8: sub             x0, x0, #0xf
    //     0x8be9fc: movz            x1, #0xe15c
    //     0x8bea00: movk            x1, #0x3, lsl #16
    //     0x8bea04: stur            x1, [x0, #-1]
    // 0x8bea08: StoreField: r0->field_7 = d2
    //     0x8bea08: stur            d2, [x0, #7]
    // 0x8bea0c: ldur            x2, [fp, #-0x20]
    // 0x8bea10: StoreField: r2->field_1b = r0
    //     0x8bea10: stur            w0, [x2, #0x1b]
    //     0x8bea14: ldurb           w16, [x2, #-1]
    //     0x8bea18: ldurb           w17, [x0, #-1]
    //     0x8bea1c: and             x16, x17, x16, lsr #2
    //     0x8bea20: tst             x16, HEAP, lsr #32
    //     0x8bea24: b.eq            #0x8bea2c
    //     0x8bea28: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8bea2c: r1 = <void?>
    //     0x8bea2c: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8bea30: r0 = _Future()
    //     0x8bea30: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x8bea34: stur            x0, [fp, #-0x28]
    // 0x8bea38: StoreField: r0->field_b = rZR
    //     0x8bea38: stur            xzr, [x0, #0xb]
    // 0x8bea3c: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x8bea3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8bea40: ldr             x0, [x0, #0x7a0]
    //     0x8bea44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8bea48: cmp             w0, w16
    //     0x8bea4c: b.ne            #0x8bea58
    //     0x8bea50: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x8bea54: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8bea58: mov             x2, x0
    // 0x8bea5c: ldur            x0, [fp, #-0x28]
    // 0x8bea60: stur            x2, [fp, #-0x30]
    // 0x8bea64: StoreField: r0->field_13 = r2
    //     0x8bea64: stur            w2, [x0, #0x13]
    // 0x8bea68: r1 = <void?>
    //     0x8bea68: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8bea6c: r0 = _AsyncCompleter()
    //     0x8bea6c: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x8bea70: ldur            x2, [fp, #-0x28]
    // 0x8bea74: StoreField: r0->field_b = r2
    //     0x8bea74: stur            w2, [x0, #0xb]
    // 0x8bea78: ldur            x3, [fp, #-0x20]
    // 0x8bea7c: StoreField: r3->field_1f = r0
    //     0x8bea7c: stur            w0, [x3, #0x1f]
    //     0x8bea80: ldurb           w16, [x3, #-1]
    //     0x8bea84: ldurb           w17, [x0, #-1]
    //     0x8bea88: and             x16, x17, x16, lsr #2
    //     0x8bea8c: tst             x16, HEAP, lsr #32
    //     0x8bea90: b.eq            #0x8bea98
    //     0x8bea94: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8bea98: r1 = <void?>
    //     0x8bea98: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8bea9c: r0 = _Future()
    //     0x8bea9c: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x8beaa0: stur            x0, [fp, #-0x38]
    // 0x8beaa4: StoreField: r0->field_b = rZR
    //     0x8beaa4: stur            xzr, [x0, #0xb]
    // 0x8beaa8: ldur            x1, [fp, #-0x30]
    // 0x8beaac: StoreField: r0->field_13 = r1
    //     0x8beaac: stur            w1, [x0, #0x13]
    // 0x8beab0: r1 = <void?>
    //     0x8beab0: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8beab4: r0 = _AsyncCompleter()
    //     0x8beab4: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x8beab8: ldur            x3, [fp, #-0x38]
    // 0x8beabc: StoreField: r0->field_b = r3
    //     0x8beabc: stur            w3, [x0, #0xb]
    // 0x8beac0: ldur            x4, [fp, #-0x20]
    // 0x8beac4: StoreField: r4->field_23 = r0
    //     0x8beac4: stur            w0, [x4, #0x23]
    //     0x8beac8: ldurb           w16, [x4, #-1]
    //     0x8beacc: ldurb           w17, [x0, #-1]
    //     0x8bead0: and             x16, x17, x16, lsr #2
    //     0x8bead4: tst             x16, HEAP, lsr #32
    //     0x8bead8: b.eq            #0x8beae0
    //     0x8beadc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x8beae0: mov             x2, x4
    // 0x8beae4: r1 = Function '<anonymous closure>':.
    //     0x8beae4: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c358] AnonymousClosure: (0x8bec0c), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_startScroll (0x8be7b4)
    //     0x8beae8: ldr             x1, [x1, #0x358]
    // 0x8beaec: r0 = AllocateClosure()
    //     0x8beaec: bl              #0xec1630  ; AllocateClosureStub
    // 0x8beaf0: ldur            x3, [fp, #-0x10]
    // 0x8beaf4: StoreField: r3->field_27 = r0
    //     0x8beaf4: stur            w0, [x3, #0x27]
    //     0x8beaf8: ldurb           w16, [x3, #-1]
    //     0x8beafc: ldurb           w17, [x0, #-1]
    //     0x8beb00: and             x16, x17, x16, lsr #2
    //     0x8beb04: tst             x16, HEAP, lsr #32
    //     0x8beb08: b.eq            #0x8beb10
    //     0x8beb0c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8beb10: ldur            x2, [fp, #-0x20]
    // 0x8beb14: r1 = Function '<anonymous closure>':.
    //     0x8beb14: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c360] AnonymousClosure: (0x8bebc4), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_startScroll (0x8be7b4)
    //     0x8beb18: ldr             x1, [x1, #0x360]
    // 0x8beb1c: r0 = AllocateClosure()
    //     0x8beb1c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8beb20: ldur            x1, [fp, #-0x10]
    // 0x8beb24: mov             x2, x0
    // 0x8beb28: r0 = setState()
    //     0x8beb28: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x8beb2c: r1 = Null
    //     0x8beb2c: mov             x1, NULL
    // 0x8beb30: r2 = 4
    //     0x8beb30: movz            x2, #0x4
    // 0x8beb34: r0 = AllocateArray()
    //     0x8beb34: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8beb38: mov             x2, x0
    // 0x8beb3c: ldur            x0, [fp, #-0x28]
    // 0x8beb40: stur            x2, [fp, #-0x30]
    // 0x8beb44: StoreField: r2->field_f = r0
    //     0x8beb44: stur            w0, [x2, #0xf]
    // 0x8beb48: ldur            x0, [fp, #-0x38]
    // 0x8beb4c: StoreField: r2->field_13 = r0
    //     0x8beb4c: stur            w0, [x2, #0x13]
    // 0x8beb50: r1 = <Future<void?>>
    //     0x8beb50: ldr             x1, [PP, #0x33f8]  ; [pp+0x33f8] TypeArguments: <Future<void?>>
    // 0x8beb54: r0 = AllocateGrowableArray()
    //     0x8beb54: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8beb58: mov             x1, x0
    // 0x8beb5c: ldur            x0, [fp, #-0x30]
    // 0x8beb60: StoreField: r1->field_f = r0
    //     0x8beb60: stur            w0, [x1, #0xf]
    // 0x8beb64: r0 = 4
    //     0x8beb64: movz            x0, #0x4
    // 0x8beb68: StoreField: r1->field_b = r0
    //     0x8beb68: stur            w0, [x1, #0xb]
    // 0x8beb6c: r16 = <void?>
    //     0x8beb6c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8beb70: stp             x1, x16, [SP]
    // 0x8beb74: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8beb74: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8beb78: r0 = wait()
    //     0x8beb78: bl              #0x678258  ; [dart:async] Future::wait
    // 0x8beb7c: mov             x1, x0
    // 0x8beb80: stur            x1, [fp, #-0x28]
    // 0x8beb84: r0 = Await()
    //     0x8beb84: bl              #0x661044  ; AwaitStub
    // 0x8beb88: ldur            x1, [fp, #-0x10]
    // 0x8beb8c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8beb8c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8beb90: r0 = _stopScroll()
    //     0x8beb90: bl              #0x8bf524  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_stopScroll
    // 0x8beb94: r0 = Null
    //     0x8beb94: mov             x0, NULL
    // 0x8beb98: r0 = ReturnAsyncNotFuture()
    //     0x8beb98: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8beb9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8beb9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8beba0: b               #0x8be7d8
    // 0x8beba4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8beba4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8beba8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8beba8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8bebac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8bebac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8bebb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8bebb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8bebb4: SaveReg d2
    //     0x8bebb4: str             q2, [SP, #-0x10]!
    // 0x8bebb8: r0 = AllocateDouble()
    //     0x8bebb8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x8bebbc: RestoreReg d2
    //     0x8bebbc: ldr             q2, [SP], #0x10
    // 0x8bebc0: b               #0x8bea08
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8bebc4, size: 0x48
    // 0x8bebc4: r1 = true
    //     0x8bebc4: add             x1, NULL, #0x20  ; true
    // 0x8bebc8: ldr             x2, [SP]
    // 0x8bebcc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x8bebcc: ldur            w3, [x2, #0x17]
    // 0x8bebd0: DecompressPointer r3
    //     0x8bebd0: add             x3, x3, HEAP, lsl #32
    // 0x8bebd4: LoadField: r2 = r3->field_f
    //     0x8bebd4: ldur            w2, [x3, #0xf]
    // 0x8bebd8: DecompressPointer r2
    //     0x8bebd8: add             x2, x2, HEAP, lsl #32
    // 0x8bebdc: LoadField: r4 = r2->field_1f
    //     0x8bebdc: ldur            w4, [x2, #0x1f]
    // 0x8bebe0: DecompressPointer r4
    //     0x8bebe0: add             x4, x4, HEAP, lsl #32
    // 0x8bebe4: LoadField: r5 = r3->field_13
    //     0x8bebe4: ldur            w5, [x3, #0x13]
    // 0x8bebe8: DecompressPointer r5
    //     0x8bebe8: add             x5, x5, HEAP, lsl #32
    // 0x8bebec: r3 = LoadInt32Instr(r5)
    //     0x8bebec: sbfx            x3, x5, #1, #0x1f
    //     0x8bebf0: tbz             w5, #0, #0x8bebf8
    //     0x8bebf4: ldur            x3, [x5, #7]
    // 0x8bebf8: StoreField: r4->field_f = r3
    //     0x8bebf8: stur            x3, [x4, #0xf]
    // 0x8bebfc: ArrayStore: r4[0] = rZR  ; List_8
    //     0x8bebfc: stur            xzr, [x4, #0x17]
    // 0x8bec00: StoreField: r2->field_2b = r1
    //     0x8bec00: stur            w1, [x2, #0x2b]
    // 0x8bec04: r0 = Null
    //     0x8bec04: mov             x0, NULL
    // 0x8bec08: ret
    //     0x8bec08: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8bec0c, size: 0x120
    // 0x8bec0c: EnterFrame
    //     0x8bec0c: stp             fp, lr, [SP, #-0x10]!
    //     0x8bec10: mov             fp, SP
    // 0x8bec14: AllocStack(0x18)
    //     0x8bec14: sub             SP, SP, #0x18
    // 0x8bec18: SetupParameters()
    //     0x8bec18: ldr             x0, [fp, #0x10]
    //     0x8bec1c: ldur            w2, [x0, #0x17]
    //     0x8bec20: add             x2, x2, HEAP, lsl #32
    // 0x8bec24: CheckStackOverflow
    //     0x8bec24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bec28: cmp             SP, x16
    //     0x8bec2c: b.ls            #0x8bed20
    // 0x8bec30: r0 = LoadStaticField(0x958)
    //     0x8bec30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8bec34: ldr             x0, [x0, #0x12b0]
    // 0x8bec38: cmp             w0, NULL
    // 0x8bec3c: b.eq            #0x8bed28
    // 0x8bec40: LoadField: r3 = r0->field_53
    //     0x8bec40: ldur            w3, [x0, #0x53]
    // 0x8bec44: DecompressPointer r3
    //     0x8bec44: add             x3, x3, HEAP, lsl #32
    // 0x8bec48: stur            x3, [fp, #-0x10]
    // 0x8bec4c: LoadField: r0 = r3->field_7
    //     0x8bec4c: ldur            w0, [x3, #7]
    // 0x8bec50: DecompressPointer r0
    //     0x8bec50: add             x0, x0, HEAP, lsl #32
    // 0x8bec54: stur            x0, [fp, #-8]
    // 0x8bec58: r1 = Function '<anonymous closure>':.
    //     0x8bec58: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c368] AnonymousClosure: (0x8bed2c), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_startScroll (0x8be7b4)
    //     0x8bec5c: ldr             x1, [x1, #0x368]
    // 0x8bec60: r0 = AllocateClosure()
    //     0x8bec60: bl              #0xec1630  ; AllocateClosureStub
    // 0x8bec64: ldur            x2, [fp, #-8]
    // 0x8bec68: mov             x3, x0
    // 0x8bec6c: r1 = Null
    //     0x8bec6c: mov             x1, NULL
    // 0x8bec70: stur            x3, [fp, #-8]
    // 0x8bec74: cmp             w2, NULL
    // 0x8bec78: b.eq            #0x8bec98
    // 0x8bec7c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8bec7c: ldur            w4, [x2, #0x17]
    // 0x8bec80: DecompressPointer r4
    //     0x8bec80: add             x4, x4, HEAP, lsl #32
    // 0x8bec84: r8 = X0
    //     0x8bec84: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8bec88: LoadField: r9 = r4->field_7
    //     0x8bec88: ldur            x9, [x4, #7]
    // 0x8bec8c: r3 = Null
    //     0x8bec8c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c370] Null
    //     0x8bec90: ldr             x3, [x3, #0x370]
    // 0x8bec94: blr             x9
    // 0x8bec98: ldur            x0, [fp, #-0x10]
    // 0x8bec9c: LoadField: r1 = r0->field_b
    //     0x8bec9c: ldur            w1, [x0, #0xb]
    // 0x8beca0: LoadField: r2 = r0->field_f
    //     0x8beca0: ldur            w2, [x0, #0xf]
    // 0x8beca4: DecompressPointer r2
    //     0x8beca4: add             x2, x2, HEAP, lsl #32
    // 0x8beca8: LoadField: r3 = r2->field_b
    //     0x8beca8: ldur            w3, [x2, #0xb]
    // 0x8becac: r2 = LoadInt32Instr(r1)
    //     0x8becac: sbfx            x2, x1, #1, #0x1f
    // 0x8becb0: stur            x2, [fp, #-0x18]
    // 0x8becb4: r1 = LoadInt32Instr(r3)
    //     0x8becb4: sbfx            x1, x3, #1, #0x1f
    // 0x8becb8: cmp             x2, x1
    // 0x8becbc: b.ne            #0x8becc8
    // 0x8becc0: mov             x1, x0
    // 0x8becc4: r0 = _growToNextCapacity()
    //     0x8becc4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8becc8: ldur            x2, [fp, #-0x10]
    // 0x8beccc: ldur            x3, [fp, #-0x18]
    // 0x8becd0: add             x4, x3, #1
    // 0x8becd4: lsl             x5, x4, #1
    // 0x8becd8: StoreField: r2->field_b = r5
    //     0x8becd8: stur            w5, [x2, #0xb]
    // 0x8becdc: LoadField: r1 = r2->field_f
    //     0x8becdc: ldur            w1, [x2, #0xf]
    // 0x8bece0: DecompressPointer r1
    //     0x8bece0: add             x1, x1, HEAP, lsl #32
    // 0x8bece4: ldur            x0, [fp, #-8]
    // 0x8bece8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8bece8: add             x25, x1, x3, lsl #2
    //     0x8becec: add             x25, x25, #0xf
    //     0x8becf0: str             w0, [x25]
    //     0x8becf4: tbz             w0, #0, #0x8bed10
    //     0x8becf8: ldurb           w16, [x1, #-1]
    //     0x8becfc: ldurb           w17, [x0, #-1]
    //     0x8bed00: and             x16, x17, x16, lsr #2
    //     0x8bed04: tst             x16, HEAP, lsr #32
    //     0x8bed08: b.eq            #0x8bed10
    //     0x8bed0c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8bed10: r0 = Null
    //     0x8bed10: mov             x0, NULL
    // 0x8bed14: LeaveFrame
    //     0x8bed14: mov             SP, fp
    //     0x8bed18: ldp             fp, lr, [SP], #0x10
    // 0x8bed1c: ret
    //     0x8bed1c: ret             
    // 0x8bed20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bed20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bed24: b               #0x8bec30
    // 0x8bed28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8bed28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x8bed2c, size: 0x348
    // 0x8bed2c: EnterFrame
    //     0x8bed2c: stp             fp, lr, [SP, #-0x10]!
    //     0x8bed30: mov             fp, SP
    // 0x8bed34: AllocStack(0x40)
    //     0x8bed34: sub             SP, SP, #0x40
    // 0x8bed38: SetupParameters()
    //     0x8bed38: ldr             x0, [fp, #0x18]
    //     0x8bed3c: ldur            w3, [x0, #0x17]
    //     0x8bed40: add             x3, x3, HEAP, lsl #32
    //     0x8bed44: stur            x3, [fp, #-0x10]
    // 0x8bed48: CheckStackOverflow
    //     0x8bed48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bed4c: cmp             SP, x16
    //     0x8bed50: b.ls            #0x8bf060
    // 0x8bed54: LoadField: r0 = r3->field_f
    //     0x8bed54: ldur            w0, [x3, #0xf]
    // 0x8bed58: DecompressPointer r0
    //     0x8bed58: add             x0, x0, HEAP, lsl #32
    // 0x8bed5c: stur            x0, [fp, #-8]
    // 0x8bed60: r1 = Function '<anonymous closure>':.
    //     0x8bed60: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c380] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x8bed64: ldr             x1, [x1, #0x380]
    // 0x8bed68: r2 = Null
    //     0x8bed68: mov             x2, NULL
    // 0x8bed6c: r0 = AllocateClosure()
    //     0x8bed6c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8bed70: ldur            x1, [fp, #-8]
    // 0x8bed74: StoreField: r1->field_27 = r0
    //     0x8bed74: stur            w0, [x1, #0x27]
    //     0x8bed78: ldurb           w16, [x1, #-1]
    //     0x8bed7c: ldurb           w17, [x0, #-1]
    //     0x8bed80: and             x16, x17, x16, lsr #2
    //     0x8bed84: tst             x16, HEAP, lsr #32
    //     0x8bed88: b.eq            #0x8bed90
    //     0x8bed8c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8bed90: LoadField: r0 = r1->field_2f
    //     0x8bed90: ldur            w0, [x1, #0x2f]
    // 0x8bed94: DecompressPointer r0
    //     0x8bed94: add             x0, x0, HEAP, lsl #32
    // 0x8bed98: cmp             w0, NULL
    // 0x8bed9c: b.eq            #0x8beda8
    // 0x8beda0: mov             x1, x0
    // 0x8beda4: r0 = dispose()
    //     0x8beda4: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0x8beda8: ldur            x0, [fp, #-0x10]
    // 0x8bedac: LoadField: r2 = r0->field_f
    //     0x8bedac: ldur            w2, [x0, #0xf]
    // 0x8bedb0: DecompressPointer r2
    //     0x8bedb0: add             x2, x2, HEAP, lsl #32
    // 0x8bedb4: stur            x2, [fp, #-8]
    // 0x8bedb8: r1 = <double>
    //     0x8bedb8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x8bedbc: r0 = AnimationController()
    //     0x8bedbc: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x8bedc0: stur            x0, [fp, #-0x18]
    // 0x8bedc4: r16 = Instance_Duration
    //     0x8bedc4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x8bedc8: ldr             x16, [x16, #0x368]
    // 0x8bedcc: str             x16, [SP]
    // 0x8bedd0: mov             x1, x0
    // 0x8bedd4: ldur            x2, [fp, #-8]
    // 0x8bedd8: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x8bedd8: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x8beddc: ldr             x4, [x4, #0x408]
    // 0x8bede0: r0 = AnimationController()
    //     0x8bede0: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x8bede4: ldur            x1, [fp, #-0x18]
    // 0x8bede8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8bede8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8bedec: r0 = forward()
    //     0x8bedec: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x8bedf0: ldur            x0, [fp, #-0x18]
    // 0x8bedf4: ldur            x1, [fp, #-8]
    // 0x8bedf8: StoreField: r1->field_2f = r0
    //     0x8bedf8: stur            w0, [x1, #0x2f]
    //     0x8bedfc: ldurb           w16, [x1, #-1]
    //     0x8bee00: ldurb           w17, [x0, #-1]
    //     0x8bee04: and             x16, x17, x16, lsr #2
    //     0x8bee08: tst             x16, HEAP, lsr #32
    //     0x8bee0c: b.eq            #0x8bee14
    //     0x8bee10: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8bee14: ldur            x0, [fp, #-0x10]
    // 0x8bee18: LoadField: r1 = r0->field_f
    //     0x8bee18: ldur            w1, [x0, #0xf]
    // 0x8bee1c: DecompressPointer r1
    //     0x8bee1c: add             x1, x1, HEAP, lsl #32
    // 0x8bee20: LoadField: r2 = r1->field_23
    //     0x8bee20: ldur            w2, [x1, #0x23]
    // 0x8bee24: DecompressPointer r2
    //     0x8bee24: add             x2, x2, HEAP, lsl #32
    // 0x8bee28: stur            x2, [fp, #-8]
    // 0x8bee2c: r0 = _opacityAnimation()
    //     0x8bee2c: bl              #0x8bf074  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_opacityAnimation
    // 0x8bee30: mov             x4, x0
    // 0x8bee34: ldur            x3, [fp, #-0x10]
    // 0x8bee38: stur            x4, [fp, #-0x20]
    // 0x8bee3c: LoadField: r0 = r3->field_f
    //     0x8bee3c: ldur            w0, [x3, #0xf]
    // 0x8bee40: DecompressPointer r0
    //     0x8bee40: add             x0, x0, HEAP, lsl #32
    // 0x8bee44: LoadField: r5 = r0->field_2f
    //     0x8bee44: ldur            w5, [x0, #0x2f]
    // 0x8bee48: DecompressPointer r5
    //     0x8bee48: add             x5, x5, HEAP, lsl #32
    // 0x8bee4c: mov             x0, x5
    // 0x8bee50: stur            x5, [fp, #-0x18]
    // 0x8bee54: r2 = Null
    //     0x8bee54: mov             x2, NULL
    // 0x8bee58: r1 = Null
    //     0x8bee58: mov             x1, NULL
    // 0x8bee5c: r8 = Animation<double>
    //     0x8bee5c: add             x8, PP, #0x22, lsl #12  ; [pp+0x22320] Type: Animation<double>
    //     0x8bee60: ldr             x8, [x8, #0x320]
    // 0x8bee64: r3 = Null
    //     0x8bee64: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c388] Null
    //     0x8bee68: ldr             x3, [x3, #0x388]
    // 0x8bee6c: r0 = Animation<double>()
    //     0x8bee6c: bl              #0x652c7c  ; IsType_Animation<double>_Stub
    // 0x8bee70: ldur            x1, [fp, #-0x20]
    // 0x8bee74: ldur            x2, [fp, #-0x18]
    // 0x8bee78: r0 = animate()
    //     0x8bee78: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x8bee7c: ldur            x1, [fp, #-8]
    // 0x8bee80: mov             x2, x0
    // 0x8bee84: r0 = parent=()
    //     0x8bee84: bl              #0x652a14  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::parent=
    // 0x8bee88: ldur            x0, [fp, #-0x10]
    // 0x8bee8c: LoadField: r1 = r0->field_f
    //     0x8bee8c: ldur            w1, [x0, #0xf]
    // 0x8bee90: DecompressPointer r1
    //     0x8bee90: add             x1, x1, HEAP, lsl #32
    // 0x8bee94: LoadField: r2 = r1->field_1f
    //     0x8bee94: ldur            w2, [x1, #0x1f]
    // 0x8bee98: DecompressPointer r2
    //     0x8bee98: add             x2, x2, HEAP, lsl #32
    // 0x8bee9c: LoadField: r3 = r2->field_b
    //     0x8bee9c: ldur            w3, [x2, #0xb]
    // 0x8beea0: DecompressPointer r3
    //     0x8beea0: add             x3, x3, HEAP, lsl #32
    // 0x8beea4: stur            x3, [fp, #-8]
    // 0x8beea8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8beea8: ldur            w2, [x0, #0x17]
    // 0x8beeac: DecompressPointer r2
    //     0x8beeac: add             x2, x2, HEAP, lsl #32
    // 0x8beeb0: r4 = LoadInt32Instr(r2)
    //     0x8beeb0: sbfx            x4, x2, #1, #0x1f
    //     0x8beeb4: tbz             w2, #0, #0x8beebc
    //     0x8beeb8: ldur            x4, [x2, #7]
    // 0x8beebc: stur            x4, [fp, #-0x30]
    // 0x8beec0: neg             x2, x4
    // 0x8beec4: stur            x2, [fp, #-0x28]
    // 0x8beec8: LoadField: r5 = r1->field_1b
    //     0x8beec8: ldur            w5, [x1, #0x1b]
    // 0x8beecc: DecompressPointer r5
    //     0x8beecc: add             x5, x5, HEAP, lsl #32
    // 0x8beed0: LoadField: r1 = r5->field_b
    //     0x8beed0: ldur            w1, [x5, #0xb]
    // 0x8beed4: DecompressPointer r1
    //     0x8beed4: add             x1, x1, HEAP, lsl #32
    // 0x8beed8: LoadField: r5 = r1->field_3b
    //     0x8beed8: ldur            w5, [x1, #0x3b]
    // 0x8beedc: DecompressPointer r5
    //     0x8beedc: add             x5, x5, HEAP, lsl #32
    // 0x8beee0: mov             x1, x5
    // 0x8beee4: r0 = single()
    //     0x8beee4: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8beee8: LoadField: r1 = r0->field_43
    //     0x8beee8: ldur            w1, [x0, #0x43]
    // 0x8beeec: DecompressPointer r1
    //     0x8beeec: add             x1, x1, HEAP, lsl #32
    // 0x8beef0: cmp             w1, NULL
    // 0x8beef4: b.eq            #0x8bf068
    // 0x8beef8: LoadField: d0 = r1->field_7
    //     0x8beef8: ldur            d0, [x1, #7]
    // 0x8beefc: d1 = 2.000000
    //     0x8beefc: fmov            d1, #2.00000000
    // 0x8bef00: fmul            d2, d0, d1
    // 0x8bef04: ldur            x0, [fp, #-0x10]
    // 0x8bef08: stur            d2, [fp, #-0x38]
    // 0x8bef0c: LoadField: r1 = r0->field_f
    //     0x8bef0c: ldur            w1, [x0, #0xf]
    // 0x8bef10: DecompressPointer r1
    //     0x8bef10: add             x1, x1, HEAP, lsl #32
    // 0x8bef14: LoadField: r2 = r1->field_1f
    //     0x8bef14: ldur            w2, [x1, #0x1f]
    // 0x8bef18: DecompressPointer r2
    //     0x8bef18: add             x2, x2, HEAP, lsl #32
    // 0x8bef1c: LoadField: r1 = r2->field_b
    //     0x8bef1c: ldur            w1, [x2, #0xb]
    // 0x8bef20: DecompressPointer r1
    //     0x8bef20: add             x1, x1, HEAP, lsl #32
    // 0x8bef24: LoadField: r2 = r1->field_3b
    //     0x8bef24: ldur            w2, [x1, #0x3b]
    // 0x8bef28: DecompressPointer r2
    //     0x8bef28: add             x2, x2, HEAP, lsl #32
    // 0x8bef2c: mov             x1, x2
    // 0x8bef30: r0 = single()
    //     0x8bef30: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8bef34: LoadField: r1 = r0->field_43
    //     0x8bef34: ldur            w1, [x0, #0x43]
    // 0x8bef38: DecompressPointer r1
    //     0x8bef38: add             x1, x1, HEAP, lsl #32
    // 0x8bef3c: cmp             w1, NULL
    // 0x8bef40: b.eq            #0x8bf06c
    // 0x8bef44: LoadField: d0 = r1->field_7
    //     0x8bef44: ldur            d0, [x1, #7]
    // 0x8bef48: d1 = 0.000000
    //     0x8bef48: eor             v1.16b, v1.16b, v1.16b
    // 0x8bef4c: fmul            d2, d0, d1
    // 0x8bef50: ldur            d0, [fp, #-0x38]
    // 0x8bef54: fsub            d3, d0, d2
    // 0x8bef58: ldur            x0, [fp, #-0x28]
    // 0x8bef5c: scvtf           d0, x0
    // 0x8bef60: fmul            d2, d0, d3
    // 0x8bef64: ldur            x1, [fp, #-8]
    // 0x8bef68: mov             v0.16b, v2.16b
    // 0x8bef6c: r0 = jumpTo()
    //     0x8bef6c: bl              #0x677eb8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::jumpTo
    // 0x8bef70: ldur            x0, [fp, #-0x10]
    // 0x8bef74: LoadField: r2 = r0->field_1f
    //     0x8bef74: ldur            w2, [x0, #0x1f]
    // 0x8bef78: DecompressPointer r2
    //     0x8bef78: add             x2, x2, HEAP, lsl #32
    // 0x8bef7c: stur            x2, [fp, #-0x18]
    // 0x8bef80: LoadField: r1 = r0->field_f
    //     0x8bef80: ldur            w1, [x0, #0xf]
    // 0x8bef84: DecompressPointer r1
    //     0x8bef84: add             x1, x1, HEAP, lsl #32
    // 0x8bef88: LoadField: r3 = r1->field_1b
    //     0x8bef88: ldur            w3, [x1, #0x1b]
    // 0x8bef8c: DecompressPointer r3
    //     0x8bef8c: add             x3, x3, HEAP, lsl #32
    // 0x8bef90: LoadField: r4 = r3->field_b
    //     0x8bef90: ldur            w4, [x3, #0xb]
    // 0x8bef94: DecompressPointer r4
    //     0x8bef94: add             x4, x4, HEAP, lsl #32
    // 0x8bef98: stur            x4, [fp, #-8]
    // 0x8bef9c: LoadField: r1 = r4->field_3b
    //     0x8bef9c: ldur            w1, [x4, #0x3b]
    // 0x8befa0: DecompressPointer r1
    //     0x8befa0: add             x1, x1, HEAP, lsl #32
    // 0x8befa4: r0 = single()
    //     0x8befa4: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8befa8: LoadField: r1 = r0->field_3f
    //     0x8befa8: ldur            w1, [x0, #0x3f]
    // 0x8befac: DecompressPointer r1
    //     0x8befac: add             x1, x1, HEAP, lsl #32
    // 0x8befb0: cmp             w1, NULL
    // 0x8befb4: b.eq            #0x8bf070
    // 0x8befb8: ldur            x0, [fp, #-0x10]
    // 0x8befbc: LoadField: r2 = r0->field_1b
    //     0x8befbc: ldur            w2, [x0, #0x1b]
    // 0x8befc0: DecompressPointer r2
    //     0x8befc0: add             x2, x2, HEAP, lsl #32
    // 0x8befc4: ldur            x3, [fp, #-0x30]
    // 0x8befc8: scvtf           d0, x3
    // 0x8befcc: LoadField: d1 = r2->field_7
    //     0x8befcc: ldur            d1, [x2, #7]
    // 0x8befd0: fmul            d2, d0, d1
    // 0x8befd4: LoadField: d0 = r1->field_7
    //     0x8befd4: ldur            d0, [x1, #7]
    // 0x8befd8: fadd            d1, d0, d2
    // 0x8befdc: ldur            x1, [fp, #-8]
    // 0x8befe0: mov             v0.16b, v1.16b
    // 0x8befe4: r2 = Instance__Linear
    //     0x8befe4: ldr             x2, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0x8befe8: r3 = Instance_Duration
    //     0x8befe8: add             x3, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x8befec: ldr             x3, [x3, #0x368]
    // 0x8beff0: r0 = animateTo()
    //     0x8beff0: bl              #0x678090  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::animateTo
    // 0x8beff4: str             x0, [SP]
    // 0x8beff8: ldur            x1, [fp, #-0x18]
    // 0x8beffc: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8beffc: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8bf000: r0 = complete()
    //     0x8bf000: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0x8bf004: ldur            x0, [fp, #-0x10]
    // 0x8bf008: LoadField: r4 = r0->field_23
    //     0x8bf008: ldur            w4, [x0, #0x23]
    // 0x8bf00c: DecompressPointer r4
    //     0x8bf00c: add             x4, x4, HEAP, lsl #32
    // 0x8bf010: stur            x4, [fp, #-8]
    // 0x8bf014: LoadField: r1 = r0->field_f
    //     0x8bf014: ldur            w1, [x0, #0xf]
    // 0x8bf018: DecompressPointer r1
    //     0x8bf018: add             x1, x1, HEAP, lsl #32
    // 0x8bf01c: LoadField: r0 = r1->field_1f
    //     0x8bf01c: ldur            w0, [x1, #0x1f]
    // 0x8bf020: DecompressPointer r0
    //     0x8bf020: add             x0, x0, HEAP, lsl #32
    // 0x8bf024: LoadField: r1 = r0->field_b
    //     0x8bf024: ldur            w1, [x0, #0xb]
    // 0x8bf028: DecompressPointer r1
    //     0x8bf028: add             x1, x1, HEAP, lsl #32
    // 0x8bf02c: d0 = 0.000000
    //     0x8bf02c: eor             v0.16b, v0.16b, v0.16b
    // 0x8bf030: r2 = Instance__Linear
    //     0x8bf030: ldr             x2, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0x8bf034: r3 = Instance_Duration
    //     0x8bf034: add             x3, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x8bf038: ldr             x3, [x3, #0x368]
    // 0x8bf03c: r0 = animateTo()
    //     0x8bf03c: bl              #0x678090  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::animateTo
    // 0x8bf040: str             x0, [SP]
    // 0x8bf044: ldur            x1, [fp, #-8]
    // 0x8bf048: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8bf048: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8bf04c: r0 = complete()
    //     0x8bf04c: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0x8bf050: r0 = Null
    //     0x8bf050: mov             x0, NULL
    // 0x8bf054: LeaveFrame
    //     0x8bf054: mov             SP, fp
    //     0x8bf058: ldp             fp, lr, [SP], #0x10
    // 0x8bf05c: ret
    //     0x8bf05c: ret             
    // 0x8bf060: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bf060: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bf064: b               #0x8bed54
    // 0x8bf068: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8bf068: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8bf06c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8bf06c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8bf070: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8bf070: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _opacityAnimation(/* No info */) {
    // ** addr: 0x8bf074, size: 0x150
    // 0x8bf074: EnterFrame
    //     0x8bf074: stp             fp, lr, [SP, #-0x10]!
    //     0x8bf078: mov             fp, SP
    // 0x8bf07c: AllocStack(0x20)
    //     0x8bf07c: sub             SP, SP, #0x20
    // 0x8bf080: CheckStackOverflow
    //     0x8bf080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bf084: cmp             SP, x16
    //     0x8bf088: b.ls            #0x8bf1bc
    // 0x8bf08c: r1 = <double>
    //     0x8bf08c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x8bf090: r0 = ConstantTween()
    //     0x8bf090: bl              #0x8bf45c  ; AllocateConstantTweenStub -> ConstantTween<X0> (size=0x14)
    // 0x8bf094: mov             x2, x0
    // 0x8bf098: r0 = 0.000000
    //     0x8bf098: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x8bf09c: stur            x2, [fp, #-8]
    // 0x8bf0a0: StoreField: r2->field_b = r0
    //     0x8bf0a0: stur            w0, [x2, #0xb]
    // 0x8bf0a4: StoreField: r2->field_f = r0
    //     0x8bf0a4: stur            w0, [x2, #0xf]
    // 0x8bf0a8: r1 = <double>
    //     0x8bf0a8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x8bf0ac: r0 = TweenSequenceItem()
    //     0x8bf0ac: bl              #0x8bf450  ; AllocateTweenSequenceItemStub -> TweenSequenceItem<X0> (size=0x18)
    // 0x8bf0b0: mov             x2, x0
    // 0x8bf0b4: ldur            x0, [fp, #-8]
    // 0x8bf0b8: stur            x2, [fp, #-0x10]
    // 0x8bf0bc: StoreField: r2->field_b = r0
    //     0x8bf0bc: stur            w0, [x2, #0xb]
    // 0x8bf0c0: d0 = 40.000000
    //     0x8bf0c0: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0x8bf0c4: StoreField: r2->field_f = d0
    //     0x8bf0c4: stur            d0, [x2, #0xf]
    // 0x8bf0c8: r1 = <double>
    //     0x8bf0c8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x8bf0cc: r0 = Tween()
    //     0x8bf0cc: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0x8bf0d0: mov             x2, x0
    // 0x8bf0d4: r0 = 0.000000
    //     0x8bf0d4: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x8bf0d8: stur            x2, [fp, #-8]
    // 0x8bf0dc: StoreField: r2->field_b = r0
    //     0x8bf0dc: stur            w0, [x2, #0xb]
    // 0x8bf0e0: r0 = 1.000000
    //     0x8bf0e0: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x8bf0e4: StoreField: r2->field_f = r0
    //     0x8bf0e4: stur            w0, [x2, #0xf]
    // 0x8bf0e8: r1 = <double>
    //     0x8bf0e8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x8bf0ec: r0 = TweenSequenceItem()
    //     0x8bf0ec: bl              #0x8bf450  ; AllocateTweenSequenceItemStub -> TweenSequenceItem<X0> (size=0x18)
    // 0x8bf0f0: mov             x2, x0
    // 0x8bf0f4: ldur            x0, [fp, #-8]
    // 0x8bf0f8: stur            x2, [fp, #-0x18]
    // 0x8bf0fc: StoreField: r2->field_b = r0
    //     0x8bf0fc: stur            w0, [x2, #0xb]
    // 0x8bf100: d0 = 20.000000
    //     0x8bf100: fmov            d0, #20.00000000
    // 0x8bf104: StoreField: r2->field_f = d0
    //     0x8bf104: stur            d0, [x2, #0xf]
    // 0x8bf108: r1 = <double>
    //     0x8bf108: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x8bf10c: r0 = ConstantTween()
    //     0x8bf10c: bl              #0x8bf45c  ; AllocateConstantTweenStub -> ConstantTween<X0> (size=0x14)
    // 0x8bf110: mov             x2, x0
    // 0x8bf114: r0 = 1.000000
    //     0x8bf114: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0x8bf118: stur            x2, [fp, #-8]
    // 0x8bf11c: StoreField: r2->field_b = r0
    //     0x8bf11c: stur            w0, [x2, #0xb]
    // 0x8bf120: StoreField: r2->field_f = r0
    //     0x8bf120: stur            w0, [x2, #0xf]
    // 0x8bf124: r1 = <double>
    //     0x8bf124: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x8bf128: r0 = TweenSequenceItem()
    //     0x8bf128: bl              #0x8bf450  ; AllocateTweenSequenceItemStub -> TweenSequenceItem<X0> (size=0x18)
    // 0x8bf12c: mov             x3, x0
    // 0x8bf130: ldur            x0, [fp, #-8]
    // 0x8bf134: stur            x3, [fp, #-0x20]
    // 0x8bf138: StoreField: r3->field_b = r0
    //     0x8bf138: stur            w0, [x3, #0xb]
    // 0x8bf13c: d0 = 40.000000
    //     0x8bf13c: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0x8bf140: StoreField: r3->field_f = d0
    //     0x8bf140: stur            d0, [x3, #0xf]
    // 0x8bf144: r1 = Null
    //     0x8bf144: mov             x1, NULL
    // 0x8bf148: r2 = 6
    //     0x8bf148: movz            x2, #0x6
    // 0x8bf14c: r0 = AllocateArray()
    //     0x8bf14c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8bf150: mov             x2, x0
    // 0x8bf154: ldur            x0, [fp, #-0x10]
    // 0x8bf158: stur            x2, [fp, #-8]
    // 0x8bf15c: StoreField: r2->field_f = r0
    //     0x8bf15c: stur            w0, [x2, #0xf]
    // 0x8bf160: ldur            x0, [fp, #-0x18]
    // 0x8bf164: StoreField: r2->field_13 = r0
    //     0x8bf164: stur            w0, [x2, #0x13]
    // 0x8bf168: ldur            x0, [fp, #-0x20]
    // 0x8bf16c: ArrayStore: r2[0] = r0  ; List_4
    //     0x8bf16c: stur            w0, [x2, #0x17]
    // 0x8bf170: r1 = <TweenSequenceItem<double>>
    //     0x8bf170: add             x1, PP, #0x24, lsl #12  ; [pp+0x24fd8] TypeArguments: <TweenSequenceItem<double>>
    //     0x8bf174: ldr             x1, [x1, #0xfd8]
    // 0x8bf178: r0 = AllocateGrowableArray()
    //     0x8bf178: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8bf17c: mov             x2, x0
    // 0x8bf180: ldur            x0, [fp, #-8]
    // 0x8bf184: stur            x2, [fp, #-0x10]
    // 0x8bf188: StoreField: r2->field_f = r0
    //     0x8bf188: stur            w0, [x2, #0xf]
    // 0x8bf18c: r0 = 6
    //     0x8bf18c: movz            x0, #0x6
    // 0x8bf190: StoreField: r2->field_b = r0
    //     0x8bf190: stur            w0, [x2, #0xb]
    // 0x8bf194: r1 = <double>
    //     0x8bf194: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x8bf198: r0 = TweenSequence()
    //     0x8bf198: bl              #0x8bf444  ; AllocateTweenSequenceStub -> TweenSequence<X0> (size=0x14)
    // 0x8bf19c: mov             x1, x0
    // 0x8bf1a0: ldur            x2, [fp, #-0x10]
    // 0x8bf1a4: stur            x0, [fp, #-8]
    // 0x8bf1a8: r0 = TweenSequence()
    //     0x8bf1a8: bl              #0x8bf1c4  ; [package:flutter/src/animation/tween_sequence.dart] TweenSequence::TweenSequence
    // 0x8bf1ac: ldur            x0, [fp, #-8]
    // 0x8bf1b0: LeaveFrame
    //     0x8bf1b0: mov             SP, fp
    //     0x8bf1b4: ldp             fp, lr, [SP], #0x10
    // 0x8bf1b8: ret
    //     0x8bf1b8: ret             
    // 0x8bf1bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bf1bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bf1c0: b               #0x8bf08c
  }
  [closure] bool <anonymous closure>(dynamic, ItemPosition) {
    // ** addr: 0x8bf4e8, size: 0x3c
    // 0x8bf4e8: ldr             x1, [SP, #8]
    // 0x8bf4ec: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x8bf4ec: ldur            w2, [x1, #0x17]
    // 0x8bf4f0: DecompressPointer r2
    //     0x8bf4f0: add             x2, x2, HEAP, lsl #32
    // 0x8bf4f4: ldr             x1, [SP]
    // 0x8bf4f8: LoadField: r3 = r1->field_7
    //     0x8bf4f8: ldur            x3, [x1, #7]
    // 0x8bf4fc: LoadField: r1 = r2->field_13
    //     0x8bf4fc: ldur            w1, [x2, #0x13]
    // 0x8bf500: DecompressPointer r1
    //     0x8bf500: add             x1, x1, HEAP, lsl #32
    // 0x8bf504: r2 = LoadInt32Instr(r1)
    //     0x8bf504: sbfx            x2, x1, #1, #0x1f
    //     0x8bf508: tbz             w1, #0, #0x8bf510
    //     0x8bf50c: ldur            x2, [x1, #7]
    // 0x8bf510: cmp             x3, x2
    // 0x8bf514: r16 = true
    //     0x8bf514: add             x16, NULL, #0x20  ; true
    // 0x8bf518: r17 = false
    //     0x8bf518: add             x17, NULL, #0x30  ; false
    // 0x8bf51c: csel            x0, x16, x17, eq
    // 0x8bf520: ret
    //     0x8bf520: ret             
  }
  _ _stopScroll(/* No info */) {
    // ** addr: 0x8bf524, size: 0x16c
    // 0x8bf524: EnterFrame
    //     0x8bf524: stp             fp, lr, [SP, #-0x10]!
    //     0x8bf528: mov             fp, SP
    // 0x8bf52c: AllocStack(0x18)
    //     0x8bf52c: sub             SP, SP, #0x18
    // 0x8bf530: SetupParameters(_ScrollablePositionedListState this /* r1 => r1, fp-0x10 */, {dynamic canceled = false /* r0, fp-0x8 */})
    //     0x8bf530: stur            x1, [fp, #-0x10]
    //     0x8bf534: ldur            w0, [x4, #0x13]
    //     0x8bf538: ldur            w2, [x4, #0x1f]
    //     0x8bf53c: add             x2, x2, HEAP, lsl #32
    //     0x8bf540: ldr             x16, [PP, #0x7280]  ; [pp+0x7280] "canceled"
    //     0x8bf544: cmp             w2, w16
    //     0x8bf548: b.ne            #0x8bf564
    //     0x8bf54c: ldur            w2, [x4, #0x23]
    //     0x8bf550: add             x2, x2, HEAP, lsl #32
    //     0x8bf554: sub             w3, w0, w2
    //     0x8bf558: add             x0, fp, w3, sxtw #2
    //     0x8bf55c: ldr             x0, [x0, #8]
    //     0x8bf560: b               #0x8bf568
    //     0x8bf564: add             x0, NULL, #0x30  ; false
    //     0x8bf568: stur            x0, [fp, #-8]
    // 0x8bf56c: CheckStackOverflow
    //     0x8bf56c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bf570: cmp             SP, x16
    //     0x8bf574: b.ls            #0x8bf680
    // 0x8bf578: r1 = 1
    //     0x8bf578: movz            x1, #0x1
    // 0x8bf57c: r0 = AllocateContext()
    //     0x8bf57c: bl              #0xec126c  ; AllocateContextStub
    // 0x8bf580: mov             x2, x0
    // 0x8bf584: ldur            x0, [fp, #-0x10]
    // 0x8bf588: stur            x2, [fp, #-0x18]
    // 0x8bf58c: StoreField: r2->field_f = r0
    //     0x8bf58c: stur            w0, [x2, #0xf]
    // 0x8bf590: LoadField: r1 = r0->field_2b
    //     0x8bf590: ldur            w1, [x0, #0x2b]
    // 0x8bf594: DecompressPointer r1
    //     0x8bf594: add             x1, x1, HEAP, lsl #32
    // 0x8bf598: tbz             w1, #4, #0x8bf5ac
    // 0x8bf59c: r0 = Null
    //     0x8bf59c: mov             x0, NULL
    // 0x8bf5a0: LeaveFrame
    //     0x8bf5a0: mov             SP, fp
    //     0x8bf5a4: ldp             fp, lr, [SP], #0x10
    // 0x8bf5a8: ret
    //     0x8bf5a8: ret             
    // 0x8bf5ac: ldur            x1, [fp, #-8]
    // 0x8bf5b0: tbnz            w1, #4, #0x8bf640
    // 0x8bf5b4: LoadField: r1 = r0->field_1b
    //     0x8bf5b4: ldur            w1, [x0, #0x1b]
    // 0x8bf5b8: DecompressPointer r1
    //     0x8bf5b8: add             x1, x1, HEAP, lsl #32
    // 0x8bf5bc: LoadField: r3 = r1->field_b
    //     0x8bf5bc: ldur            w3, [x1, #0xb]
    // 0x8bf5c0: DecompressPointer r3
    //     0x8bf5c0: add             x3, x3, HEAP, lsl #32
    // 0x8bf5c4: stur            x3, [fp, #-8]
    // 0x8bf5c8: LoadField: r1 = r3->field_3b
    //     0x8bf5c8: ldur            w1, [x3, #0x3b]
    // 0x8bf5cc: DecompressPointer r1
    //     0x8bf5cc: add             x1, x1, HEAP, lsl #32
    // 0x8bf5d0: LoadField: r4 = r1->field_b
    //     0x8bf5d0: ldur            w4, [x1, #0xb]
    // 0x8bf5d4: cbz             w4, #0x8bf5f8
    // 0x8bf5d8: r0 = single()
    //     0x8bf5d8: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8bf5dc: LoadField: r1 = r0->field_3f
    //     0x8bf5dc: ldur            w1, [x0, #0x3f]
    // 0x8bf5e0: DecompressPointer r1
    //     0x8bf5e0: add             x1, x1, HEAP, lsl #32
    // 0x8bf5e4: cmp             w1, NULL
    // 0x8bf5e8: b.eq            #0x8bf688
    // 0x8bf5ec: LoadField: d0 = r1->field_7
    //     0x8bf5ec: ldur            d0, [x1, #7]
    // 0x8bf5f0: ldur            x1, [fp, #-8]
    // 0x8bf5f4: r0 = jumpTo()
    //     0x8bf5f4: bl              #0x677eb8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::jumpTo
    // 0x8bf5f8: ldur            x0, [fp, #-0x10]
    // 0x8bf5fc: LoadField: r1 = r0->field_1f
    //     0x8bf5fc: ldur            w1, [x0, #0x1f]
    // 0x8bf600: DecompressPointer r1
    //     0x8bf600: add             x1, x1, HEAP, lsl #32
    // 0x8bf604: LoadField: r2 = r1->field_b
    //     0x8bf604: ldur            w2, [x1, #0xb]
    // 0x8bf608: DecompressPointer r2
    //     0x8bf608: add             x2, x2, HEAP, lsl #32
    // 0x8bf60c: stur            x2, [fp, #-8]
    // 0x8bf610: LoadField: r1 = r2->field_3b
    //     0x8bf610: ldur            w1, [x2, #0x3b]
    // 0x8bf614: DecompressPointer r1
    //     0x8bf614: add             x1, x1, HEAP, lsl #32
    // 0x8bf618: LoadField: r3 = r1->field_b
    //     0x8bf618: ldur            w3, [x1, #0xb]
    // 0x8bf61c: cbz             w3, #0x8bf640
    // 0x8bf620: r0 = single()
    //     0x8bf620: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x8bf624: LoadField: r1 = r0->field_3f
    //     0x8bf624: ldur            w1, [x0, #0x3f]
    // 0x8bf628: DecompressPointer r1
    //     0x8bf628: add             x1, x1, HEAP, lsl #32
    // 0x8bf62c: cmp             w1, NULL
    // 0x8bf630: b.eq            #0x8bf68c
    // 0x8bf634: LoadField: d0 = r1->field_7
    //     0x8bf634: ldur            d0, [x1, #7]
    // 0x8bf638: ldur            x1, [fp, #-8]
    // 0x8bf63c: r0 = jumpTo()
    //     0x8bf63c: bl              #0x677eb8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::jumpTo
    // 0x8bf640: ldur            x0, [fp, #-0x10]
    // 0x8bf644: LoadField: r1 = r0->field_f
    //     0x8bf644: ldur            w1, [x0, #0xf]
    // 0x8bf648: DecompressPointer r1
    //     0x8bf648: add             x1, x1, HEAP, lsl #32
    // 0x8bf64c: cmp             w1, NULL
    // 0x8bf650: b.eq            #0x8bf670
    // 0x8bf654: ldur            x2, [fp, #-0x18]
    // 0x8bf658: r1 = Function '<anonymous closure>':.
    //     0x8bf658: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f498] AnonymousClosure: (0x8bf690), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_stopScroll (0x8bf524)
    //     0x8bf65c: ldr             x1, [x1, #0x498]
    // 0x8bf660: r0 = AllocateClosure()
    //     0x8bf660: bl              #0xec1630  ; AllocateClosureStub
    // 0x8bf664: ldur            x1, [fp, #-0x10]
    // 0x8bf668: mov             x2, x0
    // 0x8bf66c: r0 = setState()
    //     0x8bf66c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x8bf670: r0 = Null
    //     0x8bf670: mov             x0, NULL
    // 0x8bf674: LeaveFrame
    //     0x8bf674: mov             SP, fp
    //     0x8bf678: ldp             fp, lr, [SP], #0x10
    // 0x8bf67c: ret
    //     0x8bf67c: ret             
    // 0x8bf680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bf680: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bf684: b               #0x8bf578
    // 0x8bf688: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8bf688: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8bf68c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8bf68c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8bf690, size: 0xe8
    // 0x8bf690: EnterFrame
    //     0x8bf690: stp             fp, lr, [SP, #-0x10]!
    //     0x8bf694: mov             fp, SP
    // 0x8bf698: AllocStack(0x8)
    //     0x8bf698: sub             SP, SP, #8
    // 0x8bf69c: SetupParameters()
    //     0x8bf69c: ldr             x0, [fp, #0x10]
    //     0x8bf6a0: ldur            w2, [x0, #0x17]
    //     0x8bf6a4: add             x2, x2, HEAP, lsl #32
    //     0x8bf6a8: stur            x2, [fp, #-8]
    // 0x8bf6ac: CheckStackOverflow
    //     0x8bf6ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bf6b0: cmp             SP, x16
    //     0x8bf6b4: b.ls            #0x8bf770
    // 0x8bf6b8: LoadField: r0 = r2->field_f
    //     0x8bf6b8: ldur            w0, [x2, #0xf]
    // 0x8bf6bc: DecompressPointer r0
    //     0x8bf6bc: add             x0, x0, HEAP, lsl #32
    // 0x8bf6c0: LoadField: r1 = r0->field_23
    //     0x8bf6c0: ldur            w1, [x0, #0x23]
    // 0x8bf6c4: DecompressPointer r1
    //     0x8bf6c4: add             x1, x1, HEAP, lsl #32
    // 0x8bf6c8: r0 = value()
    //     0x8bf6c8: bl              #0x6d42f0  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::value
    // 0x8bf6cc: LoadField: d0 = r0->field_7
    //     0x8bf6cc: ldur            d0, [x0, #7]
    // 0x8bf6d0: d1 = 0.500000
    //     0x8bf6d0: fmov            d1, #0.50000000
    // 0x8bf6d4: fcmp            d0, d1
    // 0x8bf6d8: b.lt            #0x8bf738
    // 0x8bf6dc: ldur            x1, [fp, #-8]
    // 0x8bf6e0: LoadField: r2 = r1->field_f
    //     0x8bf6e0: ldur            w2, [x1, #0xf]
    // 0x8bf6e4: DecompressPointer r2
    //     0x8bf6e4: add             x2, x2, HEAP, lsl #32
    // 0x8bf6e8: LoadField: r3 = r2->field_1b
    //     0x8bf6e8: ldur            w3, [x2, #0x1b]
    // 0x8bf6ec: DecompressPointer r3
    //     0x8bf6ec: add             x3, x3, HEAP, lsl #32
    // 0x8bf6f0: LoadField: r0 = r2->field_1f
    //     0x8bf6f0: ldur            w0, [x2, #0x1f]
    // 0x8bf6f4: DecompressPointer r0
    //     0x8bf6f4: add             x0, x0, HEAP, lsl #32
    // 0x8bf6f8: StoreField: r2->field_1b = r0
    //     0x8bf6f8: stur            w0, [x2, #0x1b]
    //     0x8bf6fc: ldurb           w16, [x2, #-1]
    //     0x8bf700: ldurb           w17, [x0, #-1]
    //     0x8bf704: and             x16, x17, x16, lsr #2
    //     0x8bf708: tst             x16, HEAP, lsr #32
    //     0x8bf70c: b.eq            #0x8bf714
    //     0x8bf710: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8bf714: mov             x0, x3
    // 0x8bf718: StoreField: r2->field_1f = r0
    //     0x8bf718: stur            w0, [x2, #0x1f]
    //     0x8bf71c: ldurb           w16, [x2, #-1]
    //     0x8bf720: ldurb           w17, [x0, #-1]
    //     0x8bf724: and             x16, x17, x16, lsr #2
    //     0x8bf728: tst             x16, HEAP, lsr #32
    //     0x8bf72c: b.eq            #0x8bf734
    //     0x8bf730: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8bf734: b               #0x8bf73c
    // 0x8bf738: ldur            x1, [fp, #-8]
    // 0x8bf73c: r0 = false
    //     0x8bf73c: add             x0, NULL, #0x30  ; false
    // 0x8bf740: LoadField: r2 = r1->field_f
    //     0x8bf740: ldur            w2, [x1, #0xf]
    // 0x8bf744: DecompressPointer r2
    //     0x8bf744: add             x2, x2, HEAP, lsl #32
    // 0x8bf748: StoreField: r2->field_2b = r0
    //     0x8bf748: stur            w0, [x2, #0x2b]
    // 0x8bf74c: LoadField: r1 = r2->field_23
    //     0x8bf74c: ldur            w1, [x2, #0x23]
    // 0x8bf750: DecompressPointer r1
    //     0x8bf750: add             x1, x1, HEAP, lsl #32
    // 0x8bf754: r2 = Instance_AlwaysStoppedAnimation
    //     0x8bf754: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f4a0] Obj!AlwaysStoppedAnimation<double>@e25941
    //     0x8bf758: ldr             x2, [x2, #0x4a0]
    // 0x8bf75c: r0 = parent=()
    //     0x8bf75c: bl              #0x652a14  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::parent=
    // 0x8bf760: r0 = Null
    //     0x8bf760: mov             x0, NULL
    // 0x8bf764: LeaveFrame
    //     0x8bf764: mov             SP, fp
    //     0x8bf768: ldp             fp, lr, [SP], #0x10
    // 0x8bf76c: ret
    //     0x8bf76c: ret             
    // 0x8bf770: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bf770: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bf774: b               #0x8bf6b8
  }
  [closure] Future<void> <anonymous closure>(dynamic, Duration) async {
    // ** addr: 0x8bf778, size: 0x94
    // 0x8bf778: EnterFrame
    //     0x8bf778: stp             fp, lr, [SP, #-0x10]!
    //     0x8bf77c: mov             fp, SP
    // 0x8bf780: AllocStack(0x18)
    //     0x8bf780: sub             SP, SP, #0x18
    // 0x8bf784: SetupParameters(_ScrollablePositionedListState this /* r1 */)
    //     0x8bf784: stur            NULL, [fp, #-8]
    //     0x8bf788: movz            x0, #0
    //     0x8bf78c: add             x1, fp, w0, sxtw #2
    //     0x8bf790: ldr             x1, [x1, #0x18]
    //     0x8bf794: ldur            w2, [x1, #0x17]
    //     0x8bf798: add             x2, x2, HEAP, lsl #32
    //     0x8bf79c: stur            x2, [fp, #-0x10]
    // 0x8bf7a0: CheckStackOverflow
    //     0x8bf7a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bf7a4: cmp             SP, x16
    //     0x8bf7a8: b.ls            #0x8bf804
    // 0x8bf7ac: InitAsync() -> Future<void?>
    //     0x8bf7ac: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8bf7b0: bl              #0x661298  ; InitAsyncStub
    // 0x8bf7b4: ldur            x0, [fp, #-0x10]
    // 0x8bf7b8: LoadField: r1 = r0->field_f
    //     0x8bf7b8: ldur            w1, [x0, #0xf]
    // 0x8bf7bc: DecompressPointer r1
    //     0x8bf7bc: add             x1, x1, HEAP, lsl #32
    // 0x8bf7c0: LoadField: r2 = r0->field_13
    //     0x8bf7c0: ldur            w2, [x0, #0x13]
    // 0x8bf7c4: DecompressPointer r2
    //     0x8bf7c4: add             x2, x2, HEAP, lsl #32
    // 0x8bf7c8: r3 = LoadInt32Instr(r2)
    //     0x8bf7c8: sbfx            x3, x2, #1, #0x1f
    //     0x8bf7cc: tbz             w2, #0, #0x8bf7d4
    //     0x8bf7d0: ldur            x3, [x2, #7]
    // 0x8bf7d4: mov             x2, x3
    // 0x8bf7d8: r0 = _startScroll()
    //     0x8bf7d8: bl              #0x8be7b4  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_startScroll
    // 0x8bf7dc: mov             x1, x0
    // 0x8bf7e0: stur            x1, [fp, #-0x18]
    // 0x8bf7e4: r0 = Await()
    //     0x8bf7e4: bl              #0x661044  ; AwaitStub
    // 0x8bf7e8: ldur            x0, [fp, #-0x10]
    // 0x8bf7ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8bf7ec: ldur            w1, [x0, #0x17]
    // 0x8bf7f0: DecompressPointer r1
    //     0x8bf7f0: add             x1, x1, HEAP, lsl #32
    // 0x8bf7f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8bf7f4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8bf7f8: r0 = complete()
    //     0x8bf7f8: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0x8bf7fc: r0 = Null
    //     0x8bf7fc: mov             x0, NULL
    // 0x8bf800: r0 = ReturnAsyncNotFuture()
    //     0x8bf800: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8bf804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bf804: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bf808: b               #0x8bf7ac
  }
  _ _jumpTo(/* No info */) {
    // ** addr: 0x9013a8, size: 0x10c
    // 0x9013a8: EnterFrame
    //     0x9013a8: stp             fp, lr, [SP, #-0x10]!
    //     0x9013ac: mov             fp, SP
    // 0x9013b0: AllocStack(0x20)
    //     0x9013b0: sub             SP, SP, #0x20
    // 0x9013b4: SetupParameters(_ScrollablePositionedListState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x9013b4: stur            x1, [fp, #-8]
    //     0x9013b8: stur            x2, [fp, #-0x10]
    // 0x9013bc: CheckStackOverflow
    //     0x9013bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9013c0: cmp             SP, x16
    //     0x9013c4: b.ls            #0x9014a8
    // 0x9013c8: r1 = 2
    //     0x9013c8: movz            x1, #0x2
    // 0x9013cc: r0 = AllocateContext()
    //     0x9013cc: bl              #0xec126c  ; AllocateContextStub
    // 0x9013d0: mov             x3, x0
    // 0x9013d4: ldur            x2, [fp, #-8]
    // 0x9013d8: stur            x3, [fp, #-0x18]
    // 0x9013dc: StoreField: r3->field_f = r2
    //     0x9013dc: stur            w2, [x3, #0xf]
    // 0x9013e0: ldur            x4, [fp, #-0x10]
    // 0x9013e4: r0 = BoxInt64Instr(r4)
    //     0x9013e4: sbfiz           x0, x4, #1, #0x1f
    //     0x9013e8: cmp             x4, x0, asr #1
    //     0x9013ec: b.eq            #0x9013f8
    //     0x9013f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9013f4: stur            x4, [x0, #7]
    // 0x9013f8: StoreField: r3->field_13 = r0
    //     0x9013f8: stur            w0, [x3, #0x13]
    // 0x9013fc: r16 = true
    //     0x9013fc: add             x16, NULL, #0x20  ; true
    // 0x901400: str             x16, [SP]
    // 0x901404: mov             x1, x2
    // 0x901408: r4 = const [0, 0x2, 0x1, 0x1, canceled, 0x1, null]
    //     0x901408: ldr             x4, [PP, #0x7008]  ; [pp+0x7008] List(7) [0, 0x2, 0x1, 0x1, "canceled", 0x1, Null]
    // 0x90140c: r0 = _stopScroll()
    //     0x90140c: bl              #0x8bf524  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_stopScroll
    // 0x901410: ldur            x2, [fp, #-0x18]
    // 0x901414: LoadField: r0 = r2->field_13
    //     0x901414: ldur            w0, [x2, #0x13]
    // 0x901418: DecompressPointer r0
    //     0x901418: add             x0, x0, HEAP, lsl #32
    // 0x90141c: ldur            x3, [fp, #-8]
    // 0x901420: LoadField: r1 = r3->field_b
    //     0x901420: ldur            w1, [x3, #0xb]
    // 0x901424: DecompressPointer r1
    //     0x901424: add             x1, x1, HEAP, lsl #32
    // 0x901428: cmp             w1, NULL
    // 0x90142c: b.eq            #0x9014b0
    // 0x901430: LoadField: r4 = r1->field_b
    //     0x901430: ldur            x4, [x1, #0xb]
    // 0x901434: sub             x5, x4, #1
    // 0x901438: r1 = LoadInt32Instr(r0)
    //     0x901438: sbfx            x1, x0, #1, #0x1f
    //     0x90143c: tbz             w0, #0, #0x901444
    //     0x901440: ldur            x1, [x0, #7]
    // 0x901444: cmp             x1, x5
    // 0x901448: b.le            #0x901480
    // 0x90144c: r0 = BoxInt64Instr(r5)
    //     0x90144c: sbfiz           x0, x5, #1, #0x1f
    //     0x901450: cmp             x5, x0, asr #1
    //     0x901454: b.eq            #0x901460
    //     0x901458: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90145c: stur            x5, [x0, #7]
    // 0x901460: StoreField: r2->field_13 = r0
    //     0x901460: stur            w0, [x2, #0x13]
    //     0x901464: tbz             w0, #0, #0x901480
    //     0x901468: ldurb           w16, [x2, #-1]
    //     0x90146c: ldurb           w17, [x0, #-1]
    //     0x901470: and             x16, x17, x16, lsr #2
    //     0x901474: tst             x16, HEAP, lsr #32
    //     0x901478: b.eq            #0x901480
    //     0x90147c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x901480: r1 = Function '<anonymous closure>':.
    //     0x901480: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f490] AnonymousClosure: (0x9014b4), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_jumpTo (0x9013a8)
    //     0x901484: ldr             x1, [x1, #0x490]
    // 0x901488: r0 = AllocateClosure()
    //     0x901488: bl              #0xec1630  ; AllocateClosureStub
    // 0x90148c: ldur            x1, [fp, #-8]
    // 0x901490: mov             x2, x0
    // 0x901494: r0 = setState()
    //     0x901494: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x901498: r0 = Null
    //     0x901498: mov             x0, NULL
    // 0x90149c: LeaveFrame
    //     0x90149c: mov             SP, fp
    //     0x9014a0: ldp             fp, lr, [SP], #0x10
    // 0x9014a4: ret
    //     0x9014a4: ret             
    // 0x9014a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9014a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9014ac: b               #0x9013c8
    // 0x9014b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9014b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9014b4, size: 0x94
    // 0x9014b4: EnterFrame
    //     0x9014b4: stp             fp, lr, [SP, #-0x10]!
    //     0x9014b8: mov             fp, SP
    // 0x9014bc: AllocStack(0x8)
    //     0x9014bc: sub             SP, SP, #8
    // 0x9014c0: SetupParameters()
    //     0x9014c0: ldr             x0, [fp, #0x10]
    //     0x9014c4: ldur            w2, [x0, #0x17]
    //     0x9014c8: add             x2, x2, HEAP, lsl #32
    //     0x9014cc: stur            x2, [fp, #-8]
    // 0x9014d0: CheckStackOverflow
    //     0x9014d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9014d4: cmp             SP, x16
    //     0x9014d8: b.ls            #0x901540
    // 0x9014dc: LoadField: r0 = r2->field_f
    //     0x9014dc: ldur            w0, [x2, #0xf]
    // 0x9014e0: DecompressPointer r0
    //     0x9014e0: add             x0, x0, HEAP, lsl #32
    // 0x9014e4: LoadField: r1 = r0->field_1b
    //     0x9014e4: ldur            w1, [x0, #0x1b]
    // 0x9014e8: DecompressPointer r1
    //     0x9014e8: add             x1, x1, HEAP, lsl #32
    // 0x9014ec: LoadField: r0 = r1->field_b
    //     0x9014ec: ldur            w0, [x1, #0xb]
    // 0x9014f0: DecompressPointer r0
    //     0x9014f0: add             x0, x0, HEAP, lsl #32
    // 0x9014f4: mov             x1, x0
    // 0x9014f8: d0 = 0.000000
    //     0x9014f8: eor             v0.16b, v0.16b, v0.16b
    // 0x9014fc: r0 = jumpTo()
    //     0x9014fc: bl              #0x677eb8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::jumpTo
    // 0x901500: ldur            x1, [fp, #-8]
    // 0x901504: LoadField: r2 = r1->field_f
    //     0x901504: ldur            w2, [x1, #0xf]
    // 0x901508: DecompressPointer r2
    //     0x901508: add             x2, x2, HEAP, lsl #32
    // 0x90150c: LoadField: r3 = r2->field_1b
    //     0x90150c: ldur            w3, [x2, #0x1b]
    // 0x901510: DecompressPointer r3
    //     0x901510: add             x3, x3, HEAP, lsl #32
    // 0x901514: LoadField: r2 = r1->field_13
    //     0x901514: ldur            w2, [x1, #0x13]
    // 0x901518: DecompressPointer r2
    //     0x901518: add             x2, x2, HEAP, lsl #32
    // 0x90151c: r1 = LoadInt32Instr(r2)
    //     0x90151c: sbfx            x1, x2, #1, #0x1f
    //     0x901520: tbz             w2, #0, #0x901528
    //     0x901524: ldur            x1, [x2, #7]
    // 0x901528: StoreField: r3->field_f = r1
    //     0x901528: stur            x1, [x3, #0xf]
    // 0x90152c: ArrayStore: r3[0] = rZR  ; List_8
    //     0x90152c: stur            xzr, [x3, #0x17]
    // 0x901530: r0 = Null
    //     0x901530: mov             x0, NULL
    // 0x901534: LeaveFrame
    //     0x901534: mov             SP, fp
    //     0x901538: ldp             fp, lr, [SP], #0x10
    // 0x90153c: ret
    //     0x90153c: ret             
    // 0x901540: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x901540: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x901544: b               #0x9014dc
  }
  _ deactivate(/* No info */) {
    // ** addr: 0x92bc84, size: 0x74
    // 0x92bc84: EnterFrame
    //     0x92bc84: stp             fp, lr, [SP, #-0x10]!
    //     0x92bc88: mov             fp, SP
    // 0x92bc8c: AllocStack(0x8)
    //     0x92bc8c: sub             SP, SP, #8
    // 0x92bc90: SetupParameters(_ScrollablePositionedListState this /* r1 => r0, fp-0x8 */)
    //     0x92bc90: mov             x0, x1
    //     0x92bc94: stur            x1, [fp, #-8]
    // 0x92bc98: CheckStackOverflow
    //     0x92bc98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92bc9c: cmp             SP, x16
    //     0x92bca0: b.ls            #0x92bce8
    // 0x92bca4: LoadField: r1 = r0->field_b
    //     0x92bca4: ldur            w1, [x0, #0xb]
    // 0x92bca8: DecompressPointer r1
    //     0x92bca8: add             x1, x1, HEAP, lsl #32
    // 0x92bcac: cmp             w1, NULL
    // 0x92bcb0: b.eq            #0x92bcf0
    // 0x92bcb4: LoadField: r2 = r1->field_1b
    //     0x92bcb4: ldur            w2, [x1, #0x1b]
    // 0x92bcb8: DecompressPointer r2
    //     0x92bcb8: add             x2, x2, HEAP, lsl #32
    // 0x92bcbc: mov             x1, x2
    // 0x92bcc0: r0 = _detach()
    //     0x92bcc0: bl              #0x92bcf8  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] ItemScrollController::_detach
    // 0x92bcc4: ldur            x1, [fp, #-8]
    // 0x92bcc8: LoadField: r2 = r1->field_b
    //     0x92bcc8: ldur            w2, [x1, #0xb]
    // 0x92bccc: DecompressPointer r2
    //     0x92bccc: add             x2, x2, HEAP, lsl #32
    // 0x92bcd0: cmp             w2, NULL
    // 0x92bcd4: b.eq            #0x92bcf4
    // 0x92bcd8: r0 = Null
    //     0x92bcd8: mov             x0, NULL
    // 0x92bcdc: LeaveFrame
    //     0x92bcdc: mov             SP, fp
    //     0x92bce0: ldp             fp, lr, [SP], #0x10
    // 0x92bce4: ret
    //     0x92bce4: ret             
    // 0x92bce8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92bce8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92bcec: b               #0x92bca4
    // 0x92bcf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92bcf0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92bcf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92bcf4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x97f5c4, size: 0x2a4
    // 0x97f5c4: EnterFrame
    //     0x97f5c4: stp             fp, lr, [SP, #-0x10]!
    //     0x97f5c8: mov             fp, SP
    // 0x97f5cc: AllocStack(0x18)
    //     0x97f5cc: sub             SP, SP, #0x18
    // 0x97f5d0: SetupParameters(_ScrollablePositionedListState this /* r1 => r0, fp-0x8 */)
    //     0x97f5d0: mov             x0, x1
    //     0x97f5d4: stur            x1, [fp, #-8]
    // 0x97f5d8: CheckStackOverflow
    //     0x97f5d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97f5dc: cmp             SP, x16
    //     0x97f5e0: b.ls            #0x97f828
    // 0x97f5e4: r1 = 1
    //     0x97f5e4: movz            x1, #0x1
    // 0x97f5e8: r0 = AllocateContext()
    //     0x97f5e8: bl              #0xec126c  ; AllocateContextStub
    // 0x97f5ec: mov             x2, x0
    // 0x97f5f0: ldur            x0, [fp, #-8]
    // 0x97f5f4: stur            x2, [fp, #-0x10]
    // 0x97f5f8: StoreField: r2->field_f = r0
    //     0x97f5f8: stur            w0, [x2, #0xf]
    // 0x97f5fc: LoadField: r1 = r0->field_f
    //     0x97f5fc: ldur            w1, [x0, #0xf]
    // 0x97f600: DecompressPointer r1
    //     0x97f600: add             x1, x1, HEAP, lsl #32
    // 0x97f604: cmp             w1, NULL
    // 0x97f608: b.eq            #0x97f830
    // 0x97f60c: r0 = of()
    //     0x97f60c: bl              #0x964ffc  ; [package:flutter/src/widgets/page_storage.dart] PageStorage::of
    // 0x97f610: mov             x1, x0
    // 0x97f614: ldur            x0, [fp, #-8]
    // 0x97f618: LoadField: r2 = r0->field_f
    //     0x97f618: ldur            w2, [x0, #0xf]
    // 0x97f61c: DecompressPointer r2
    //     0x97f61c: add             x2, x2, HEAP, lsl #32
    // 0x97f620: cmp             w2, NULL
    // 0x97f624: b.eq            #0x97f834
    // 0x97f628: r0 = readState()
    //     0x97f628: bl              #0x933608  ; [package:flutter/src/widgets/page_storage.dart] PageStorageBucket::readState
    // 0x97f62c: mov             x3, x0
    // 0x97f630: r2 = Null
    //     0x97f630: mov             x2, NULL
    // 0x97f634: r1 = Null
    //     0x97f634: mov             x1, NULL
    // 0x97f638: stur            x3, [fp, #-0x18]
    // 0x97f63c: r4 = 60
    //     0x97f63c: movz            x4, #0x3c
    // 0x97f640: branchIfSmi(r0, 0x97f64c)
    //     0x97f640: tbz             w0, #0, #0x97f64c
    // 0x97f644: r4 = LoadClassIdInstr(r0)
    //     0x97f644: ldur            x4, [x0, #-1]
    //     0x97f648: ubfx            x4, x4, #0xc, #0x14
    // 0x97f64c: cmp             x4, #0x1ff
    // 0x97f650: b.eq            #0x97f668
    // 0x97f654: r8 = ItemPosition?
    //     0x97f654: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3d958] Type: ItemPosition?
    //     0x97f658: ldr             x8, [x8, #0x958]
    // 0x97f65c: r3 = Null
    //     0x97f65c: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d960] Null
    //     0x97f660: ldr             x3, [x3, #0x960]
    // 0x97f664: r0 = DefaultNullableTypeTest()
    //     0x97f664: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x97f668: ldur            x3, [fp, #-8]
    // 0x97f66c: LoadField: r2 = r3->field_1b
    //     0x97f66c: ldur            w2, [x3, #0x1b]
    // 0x97f670: DecompressPointer r2
    //     0x97f670: add             x2, x2, HEAP, lsl #32
    // 0x97f674: ldur            x4, [fp, #-0x18]
    // 0x97f678: cmp             w4, NULL
    // 0x97f67c: b.ne            #0x97f688
    // 0x97f680: r0 = Null
    //     0x97f680: mov             x0, NULL
    // 0x97f684: b               #0x97f6a0
    // 0x97f688: LoadField: r5 = r4->field_7
    //     0x97f688: ldur            x5, [x4, #7]
    // 0x97f68c: r0 = BoxInt64Instr(r5)
    //     0x97f68c: sbfiz           x0, x5, #1, #0x1f
    //     0x97f690: cmp             x5, x0, asr #1
    //     0x97f694: b.eq            #0x97f6a0
    //     0x97f698: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x97f69c: stur            x5, [x0, #7]
    // 0x97f6a0: cmp             w0, NULL
    // 0x97f6a4: b.ne            #0x97f6c0
    // 0x97f6a8: LoadField: r0 = r3->field_b
    //     0x97f6a8: ldur            w0, [x3, #0xb]
    // 0x97f6ac: DecompressPointer r0
    //     0x97f6ac: add             x0, x0, HEAP, lsl #32
    // 0x97f6b0: cmp             w0, NULL
    // 0x97f6b4: b.eq            #0x97f838
    // 0x97f6b8: r0 = 0
    //     0x97f6b8: movz            x0, #0
    // 0x97f6bc: b               #0x97f6d0
    // 0x97f6c0: r1 = LoadInt32Instr(r0)
    //     0x97f6c0: sbfx            x1, x0, #1, #0x1f
    //     0x97f6c4: tbz             w0, #0, #0x97f6cc
    //     0x97f6c8: ldur            x1, [x0, #7]
    // 0x97f6cc: mov             x0, x1
    // 0x97f6d0: StoreField: r2->field_f = r0
    //     0x97f6d0: stur            x0, [x2, #0xf]
    // 0x97f6d4: cmp             w4, NULL
    // 0x97f6d8: b.ne            #0x97f6e4
    // 0x97f6dc: r1 = Null
    //     0x97f6dc: mov             x1, NULL
    // 0x97f6e0: b               #0x97f710
    // 0x97f6e4: LoadField: d0 = r4->field_f
    //     0x97f6e4: ldur            d0, [x4, #0xf]
    // 0x97f6e8: r1 = inline_Allocate_Double()
    //     0x97f6e8: ldp             x1, x4, [THR, #0x50]  ; THR::top
    //     0x97f6ec: add             x1, x1, #0x10
    //     0x97f6f0: cmp             x4, x1
    //     0x97f6f4: b.ls            #0x97f83c
    //     0x97f6f8: str             x1, [THR, #0x50]  ; THR::top
    //     0x97f6fc: sub             x1, x1, #0xf
    //     0x97f700: movz            x4, #0xe15c
    //     0x97f704: movk            x4, #0x3, lsl #16
    //     0x97f708: stur            x4, [x1, #-1]
    // 0x97f70c: StoreField: r1->field_7 = d0
    //     0x97f70c: stur            d0, [x1, #7]
    // 0x97f710: cmp             w1, NULL
    // 0x97f714: b.ne            #0x97f730
    // 0x97f718: LoadField: r1 = r3->field_b
    //     0x97f718: ldur            w1, [x3, #0xb]
    // 0x97f71c: DecompressPointer r1
    //     0x97f71c: add             x1, x1, HEAP, lsl #32
    // 0x97f720: cmp             w1, NULL
    // 0x97f724: b.eq            #0x97f860
    // 0x97f728: d0 = 0.000000
    //     0x97f728: eor             v0.16b, v0.16b, v0.16b
    // 0x97f72c: b               #0x97f734
    // 0x97f730: LoadField: d0 = r1->field_7
    //     0x97f730: ldur            d0, [x1, #7]
    // 0x97f734: ArrayStore: r2[0] = d0  ; List_8
    //     0x97f734: stur            d0, [x2, #0x17]
    // 0x97f738: LoadField: r1 = r3->field_b
    //     0x97f738: ldur            w1, [x3, #0xb]
    // 0x97f73c: DecompressPointer r1
    //     0x97f73c: add             x1, x1, HEAP, lsl #32
    // 0x97f740: cmp             w1, NULL
    // 0x97f744: b.eq            #0x97f864
    // 0x97f748: LoadField: r4 = r1->field_b
    //     0x97f748: ldur            x4, [x1, #0xb]
    // 0x97f74c: cmp             x4, #0
    // 0x97f750: b.le            #0x97f764
    // 0x97f754: sub             x5, x4, #1
    // 0x97f758: cmp             x0, x5
    // 0x97f75c: b.le            #0x97f764
    // 0x97f760: StoreField: r2->field_f = r5
    //     0x97f760: stur            x5, [x2, #0xf]
    // 0x97f764: LoadField: r4 = r1->field_1b
    //     0x97f764: ldur            w4, [x1, #0x1b]
    // 0x97f768: DecompressPointer r4
    //     0x97f768: add             x4, x4, HEAP, lsl #32
    // 0x97f76c: mov             x0, x3
    // 0x97f770: StoreField: r4->field_7 = r0
    //     0x97f770: stur            w0, [x4, #7]
    //     0x97f774: ldurb           w16, [x4, #-1]
    //     0x97f778: ldurb           w17, [x0, #-1]
    //     0x97f77c: and             x16, x17, x16, lsr #2
    //     0x97f780: tst             x16, HEAP, lsr #32
    //     0x97f784: b.eq            #0x97f78c
    //     0x97f788: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x97f78c: LoadField: r0 = r2->field_7
    //     0x97f78c: ldur            w0, [x2, #7]
    // 0x97f790: DecompressPointer r0
    //     0x97f790: add             x0, x0, HEAP, lsl #32
    // 0x97f794: LoadField: r4 = r0->field_7
    //     0x97f794: ldur            w4, [x0, #7]
    // 0x97f798: DecompressPointer r4
    //     0x97f798: add             x4, x4, HEAP, lsl #32
    // 0x97f79c: mov             x2, x3
    // 0x97f7a0: stur            x4, [fp, #-0x18]
    // 0x97f7a4: r1 = Function '_updatePositions@2299048475':.
    //     0x97f7a4: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d940] AnonymousClosure: (0x97f908), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_updatePositions (0x97f940)
    //     0x97f7a8: ldr             x1, [x1, #0x940]
    // 0x97f7ac: r0 = AllocateClosure()
    //     0x97f7ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x97f7b0: ldur            x1, [fp, #-0x18]
    // 0x97f7b4: mov             x2, x0
    // 0x97f7b8: stur            x0, [fp, #-0x18]
    // 0x97f7bc: r0 = addListener()
    //     0x97f7bc: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x97f7c0: ldur            x0, [fp, #-8]
    // 0x97f7c4: LoadField: r1 = r0->field_1f
    //     0x97f7c4: ldur            w1, [x0, #0x1f]
    // 0x97f7c8: DecompressPointer r1
    //     0x97f7c8: add             x1, x1, HEAP, lsl #32
    // 0x97f7cc: LoadField: r2 = r1->field_7
    //     0x97f7cc: ldur            w2, [x1, #7]
    // 0x97f7d0: DecompressPointer r2
    //     0x97f7d0: add             x2, x2, HEAP, lsl #32
    // 0x97f7d4: LoadField: r1 = r2->field_7
    //     0x97f7d4: ldur            w1, [x2, #7]
    // 0x97f7d8: DecompressPointer r1
    //     0x97f7d8: add             x1, x1, HEAP, lsl #32
    // 0x97f7dc: ldur            x2, [fp, #-0x18]
    // 0x97f7e0: r0 = addListener()
    //     0x97f7e0: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x97f7e4: ldur            x0, [fp, #-8]
    // 0x97f7e8: LoadField: r1 = r0->field_1b
    //     0x97f7e8: ldur            w1, [x0, #0x1b]
    // 0x97f7ec: DecompressPointer r1
    //     0x97f7ec: add             x1, x1, HEAP, lsl #32
    // 0x97f7f0: LoadField: r0 = r1->field_b
    //     0x97f7f0: ldur            w0, [x1, #0xb]
    // 0x97f7f4: DecompressPointer r0
    //     0x97f7f4: add             x0, x0, HEAP, lsl #32
    // 0x97f7f8: ldur            x2, [fp, #-0x10]
    // 0x97f7fc: stur            x0, [fp, #-8]
    // 0x97f800: r1 = Function '<anonymous closure>':.
    //     0x97f800: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d970] AnonymousClosure: (0x97f868), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::initState (0x97f5c4)
    //     0x97f804: ldr             x1, [x1, #0x970]
    // 0x97f808: r0 = AllocateClosure()
    //     0x97f808: bl              #0xec1630  ; AllocateClosureStub
    // 0x97f80c: ldur            x1, [fp, #-8]
    // 0x97f810: mov             x2, x0
    // 0x97f814: r0 = addListener()
    //     0x97f814: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x97f818: r0 = Null
    //     0x97f818: mov             x0, NULL
    // 0x97f81c: LeaveFrame
    //     0x97f81c: mov             SP, fp
    //     0x97f820: ldp             fp, lr, [SP], #0x10
    // 0x97f824: ret
    //     0x97f824: ret             
    // 0x97f828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97f828: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97f82c: b               #0x97f5e4
    // 0x97f830: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f830: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97f834: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f834: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97f838: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f838: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97f83c: SaveReg d0
    //     0x97f83c: str             q0, [SP, #-0x10]!
    // 0x97f840: stp             x2, x3, [SP, #-0x10]!
    // 0x97f844: SaveReg r0
    //     0x97f844: str             x0, [SP, #-8]!
    // 0x97f848: r0 = AllocateDouble()
    //     0x97f848: bl              #0xec2254  ; AllocateDoubleStub
    // 0x97f84c: mov             x1, x0
    // 0x97f850: RestoreReg r0
    //     0x97f850: ldr             x0, [SP], #8
    // 0x97f854: ldp             x2, x3, [SP], #0x10
    // 0x97f858: RestoreReg d0
    //     0x97f858: ldr             q0, [SP], #0x10
    // 0x97f85c: b               #0x97f70c
    // 0x97f860: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f860: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97f864: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f864: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x97f868, size: 0xa0
    // 0x97f868: EnterFrame
    //     0x97f868: stp             fp, lr, [SP, #-0x10]!
    //     0x97f86c: mov             fp, SP
    // 0x97f870: AllocStack(0x8)
    //     0x97f870: sub             SP, SP, #8
    // 0x97f874: SetupParameters()
    //     0x97f874: ldr             x0, [fp, #0x10]
    //     0x97f878: ldur            w2, [x0, #0x17]
    //     0x97f87c: add             x2, x2, HEAP, lsl #32
    //     0x97f880: stur            x2, [fp, #-8]
    // 0x97f884: CheckStackOverflow
    //     0x97f884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97f888: cmp             SP, x16
    //     0x97f88c: b.ls            #0x97f8f8
    // 0x97f890: LoadField: r0 = r2->field_f
    //     0x97f890: ldur            w0, [x2, #0xf]
    // 0x97f894: DecompressPointer r0
    //     0x97f894: add             x0, x0, HEAP, lsl #32
    // 0x97f898: LoadField: r1 = r0->field_1b
    //     0x97f898: ldur            w1, [x0, #0x1b]
    // 0x97f89c: DecompressPointer r1
    //     0x97f89c: add             x1, x1, HEAP, lsl #32
    // 0x97f8a0: LoadField: r0 = r1->field_b
    //     0x97f8a0: ldur            w0, [x1, #0xb]
    // 0x97f8a4: DecompressPointer r0
    //     0x97f8a4: add             x0, x0, HEAP, lsl #32
    // 0x97f8a8: LoadField: r1 = r0->field_3b
    //     0x97f8a8: ldur            w1, [x0, #0x3b]
    // 0x97f8ac: DecompressPointer r1
    //     0x97f8ac: add             x1, x1, HEAP, lsl #32
    // 0x97f8b0: r0 = single()
    //     0x97f8b0: bl              #0x643750  ; [dart:core] _GrowableList::single
    // 0x97f8b4: LoadField: r1 = r0->field_3f
    //     0x97f8b4: ldur            w1, [x0, #0x3f]
    // 0x97f8b8: DecompressPointer r1
    //     0x97f8b8: add             x1, x1, HEAP, lsl #32
    // 0x97f8bc: cmp             w1, NULL
    // 0x97f8c0: b.eq            #0x97f900
    // 0x97f8c4: ldur            x2, [fp, #-8]
    // 0x97f8c8: LoadField: r3 = r2->field_f
    //     0x97f8c8: ldur            w3, [x2, #0xf]
    // 0x97f8cc: DecompressPointer r3
    //     0x97f8cc: add             x3, x3, HEAP, lsl #32
    // 0x97f8d0: LoadField: d0 = r1->field_7
    //     0x97f8d0: ldur            d0, [x1, #7]
    // 0x97f8d4: StoreField: r3->field_33 = d0
    //     0x97f8d4: stur            d0, [x3, #0x33]
    // 0x97f8d8: LoadField: r1 = r3->field_b
    //     0x97f8d8: ldur            w1, [x3, #0xb]
    // 0x97f8dc: DecompressPointer r1
    //     0x97f8dc: add             x1, x1, HEAP, lsl #32
    // 0x97f8e0: cmp             w1, NULL
    // 0x97f8e4: b.eq            #0x97f904
    // 0x97f8e8: r0 = Null
    //     0x97f8e8: mov             x0, NULL
    // 0x97f8ec: LeaveFrame
    //     0x97f8ec: mov             SP, fp
    //     0x97f8f0: ldp             fp, lr, [SP], #0x10
    // 0x97f8f4: ret
    //     0x97f8f4: ret             
    // 0x97f8f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97f8f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97f8fc: b               #0x97f890
    // 0x97f900: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f900: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97f904: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97f904: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updatePositions(dynamic) {
    // ** addr: 0x97f908, size: 0x38
    // 0x97f908: EnterFrame
    //     0x97f908: stp             fp, lr, [SP, #-0x10]!
    //     0x97f90c: mov             fp, SP
    // 0x97f910: ldr             x0, [fp, #0x10]
    // 0x97f914: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97f914: ldur            w1, [x0, #0x17]
    // 0x97f918: DecompressPointer r1
    //     0x97f918: add             x1, x1, HEAP, lsl #32
    // 0x97f91c: CheckStackOverflow
    //     0x97f91c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97f920: cmp             SP, x16
    //     0x97f924: b.ls            #0x97f938
    // 0x97f928: r0 = _updatePositions()
    //     0x97f928: bl              #0x97f940  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_updatePositions
    // 0x97f92c: LeaveFrame
    //     0x97f92c: mov             SP, fp
    //     0x97f930: ldp             fp, lr, [SP], #0x10
    // 0x97f934: ret
    //     0x97f934: ret             
    // 0x97f938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97f938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97f93c: b               #0x97f928
  }
  _ _updatePositions(/* No info */) {
    // ** addr: 0x97f940, size: 0x170
    // 0x97f940: EnterFrame
    //     0x97f940: stp             fp, lr, [SP, #-0x10]!
    //     0x97f944: mov             fp, SP
    // 0x97f948: AllocStack(0x20)
    //     0x97f948: sub             SP, SP, #0x20
    // 0x97f94c: SetupParameters(_ScrollablePositionedListState this /* r1 => r0, fp-0x10 */)
    //     0x97f94c: mov             x0, x1
    //     0x97f950: stur            x1, [fp, #-0x10]
    // 0x97f954: CheckStackOverflow
    //     0x97f954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97f958: cmp             SP, x16
    //     0x97f95c: b.ls            #0x97fa9c
    // 0x97f960: LoadField: r1 = r0->field_1b
    //     0x97f960: ldur            w1, [x0, #0x1b]
    // 0x97f964: DecompressPointer r1
    //     0x97f964: add             x1, x1, HEAP, lsl #32
    // 0x97f968: LoadField: r2 = r1->field_7
    //     0x97f968: ldur            w2, [x1, #7]
    // 0x97f96c: DecompressPointer r2
    //     0x97f96c: add             x2, x2, HEAP, lsl #32
    // 0x97f970: LoadField: r1 = r2->field_7
    //     0x97f970: ldur            w1, [x2, #7]
    // 0x97f974: DecompressPointer r1
    //     0x97f974: add             x1, x1, HEAP, lsl #32
    // 0x97f978: LoadField: r3 = r1->field_27
    //     0x97f978: ldur            w3, [x1, #0x27]
    // 0x97f97c: DecompressPointer r3
    //     0x97f97c: add             x3, x3, HEAP, lsl #32
    // 0x97f980: stur            x3, [fp, #-8]
    // 0x97f984: r1 = Function '<anonymous closure>':.
    //     0x97f984: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d948] AnonymousClosure: (0x97fad8), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_updatePositions (0x97f940)
    //     0x97f988: ldr             x1, [x1, #0x948]
    // 0x97f98c: r2 = Null
    //     0x97f98c: mov             x2, NULL
    // 0x97f990: r0 = AllocateClosure()
    //     0x97f990: bl              #0xec1630  ; AllocateClosureStub
    // 0x97f994: ldur            x1, [fp, #-8]
    // 0x97f998: r2 = LoadClassIdInstr(r1)
    //     0x97f998: ldur            x2, [x1, #-1]
    //     0x97f99c: ubfx            x2, x2, #0xc, #0x14
    // 0x97f9a0: mov             x16, x0
    // 0x97f9a4: mov             x0, x2
    // 0x97f9a8: mov             x2, x16
    // 0x97f9ac: r0 = GDT[cid_x0 + 0xea28]()
    //     0x97f9ac: movz            x17, #0xea28
    //     0x97f9b0: add             lr, x0, x17
    //     0x97f9b4: ldr             lr, [x21, lr, lsl #3]
    //     0x97f9b8: blr             lr
    // 0x97f9bc: mov             x1, x0
    // 0x97f9c0: stur            x0, [fp, #-8]
    // 0x97f9c4: r0 = iterator()
    //     0x97f9c4: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0x97f9c8: r1 = LoadClassIdInstr(r0)
    //     0x97f9c8: ldur            x1, [x0, #-1]
    //     0x97f9cc: ubfx            x1, x1, #0xc, #0x14
    // 0x97f9d0: mov             x16, x0
    // 0x97f9d4: mov             x0, x1
    // 0x97f9d8: mov             x1, x16
    // 0x97f9dc: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x97f9dc: movz            x17, #0x292d
    //     0x97f9e0: movk            x17, #0x1, lsl #16
    //     0x97f9e4: add             lr, x0, x17
    //     0x97f9e8: ldr             lr, [x21, lr, lsl #3]
    //     0x97f9ec: blr             lr
    // 0x97f9f0: eor             x1, x0, #0x10
    // 0x97f9f4: eor             x0, x1, #0x10
    // 0x97f9f8: tbnz            w0, #4, #0x97fa60
    // 0x97f9fc: ldur            x0, [fp, #-0x10]
    // 0x97fa00: LoadField: r1 = r0->field_f
    //     0x97fa00: ldur            w1, [x0, #0xf]
    // 0x97fa04: DecompressPointer r1
    //     0x97fa04: add             x1, x1, HEAP, lsl #32
    // 0x97fa08: cmp             w1, NULL
    // 0x97fa0c: b.eq            #0x97faa4
    // 0x97fa10: r0 = of()
    //     0x97fa10: bl              #0x964ffc  ; [package:flutter/src/widgets/page_storage.dart] PageStorage::of
    // 0x97fa14: mov             x3, x0
    // 0x97fa18: ldur            x0, [fp, #-0x10]
    // 0x97fa1c: stur            x3, [fp, #-0x20]
    // 0x97fa20: LoadField: r4 = r0->field_f
    //     0x97fa20: ldur            w4, [x0, #0xf]
    // 0x97fa24: DecompressPointer r4
    //     0x97fa24: add             x4, x4, HEAP, lsl #32
    // 0x97fa28: stur            x4, [fp, #-0x18]
    // 0x97fa2c: cmp             w4, NULL
    // 0x97fa30: b.eq            #0x97faa8
    // 0x97fa34: r1 = Function '<anonymous closure>':.
    //     0x97fa34: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d950] AnonymousClosure: (0x97fab0), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_updatePositions (0x97f940)
    //     0x97fa38: ldr             x1, [x1, #0x950]
    // 0x97fa3c: r2 = Null
    //     0x97fa3c: mov             x2, NULL
    // 0x97fa40: r0 = AllocateClosure()
    //     0x97fa40: bl              #0xec1630  ; AllocateClosureStub
    // 0x97fa44: ldur            x1, [fp, #-8]
    // 0x97fa48: mov             x2, x0
    // 0x97fa4c: r0 = reduce()
    //     0x97fa4c: bl              #0x7f0b44  ; [dart:core] Iterable::reduce
    // 0x97fa50: ldur            x1, [fp, #-0x20]
    // 0x97fa54: ldur            x2, [fp, #-0x18]
    // 0x97fa58: mov             x3, x0
    // 0x97fa5c: r0 = writeState()
    //     0x97fa5c: bl              #0x679b9c  ; [package:flutter/src/widgets/page_storage.dart] PageStorageBucket::writeState
    // 0x97fa60: ldur            x0, [fp, #-0x10]
    // 0x97fa64: LoadField: r1 = r0->field_b
    //     0x97fa64: ldur            w1, [x0, #0xb]
    // 0x97fa68: DecompressPointer r1
    //     0x97fa68: add             x1, x1, HEAP, lsl #32
    // 0x97fa6c: cmp             w1, NULL
    // 0x97fa70: b.eq            #0x97faac
    // 0x97fa74: LoadField: r0 = r1->field_1f
    //     0x97fa74: ldur            w0, [x1, #0x1f]
    // 0x97fa78: DecompressPointer r0
    //     0x97fa78: add             x0, x0, HEAP, lsl #32
    // 0x97fa7c: LoadField: r1 = r0->field_7
    //     0x97fa7c: ldur            w1, [x0, #7]
    // 0x97fa80: DecompressPointer r1
    //     0x97fa80: add             x1, x1, HEAP, lsl #32
    // 0x97fa84: ldur            x2, [fp, #-8]
    // 0x97fa88: r0 = value=()
    //     0x97fa88: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x97fa8c: r0 = Null
    //     0x97fa8c: mov             x0, NULL
    // 0x97fa90: LeaveFrame
    //     0x97fa90: mov             SP, fp
    //     0x97fa94: ldp             fp, lr, [SP], #0x10
    // 0x97fa98: ret
    //     0x97fa98: ret             
    // 0x97fa9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97fa9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97faa0: b               #0x97f960
    // 0x97faa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97faa4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97faa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97faa8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97faac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97faac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] ItemPosition <anonymous closure>(dynamic, ItemPosition, ItemPosition) {
    // ** addr: 0x97fab0, size: 0x28
    // 0x97fab0: ldr             x1, [SP, #8]
    // 0x97fab4: LoadField: d0 = r1->field_f
    //     0x97fab4: ldur            d0, [x1, #0xf]
    // 0x97fab8: ldr             x2, [SP]
    // 0x97fabc: LoadField: d1 = r2->field_f
    //     0x97fabc: ldur            d1, [x2, #0xf]
    // 0x97fac0: fcmp            d1, d0
    // 0x97fac4: b.le            #0x97fad0
    // 0x97fac8: mov             x0, x1
    // 0x97facc: b               #0x97fad4
    // 0x97fad0: mov             x0, x2
    // 0x97fad4: ret
    //     0x97fad4: ret             
  }
  [closure] bool <anonymous closure>(dynamic, ItemPosition) {
    // ** addr: 0x97fad8, size: 0x3c
    // 0x97fad8: d0 = 1.000000
    //     0x97fad8: fmov            d0, #1.00000000
    // 0x97fadc: ldr             x1, [SP]
    // 0x97fae0: LoadField: d1 = r1->field_f
    //     0x97fae0: ldur            d1, [x1, #0xf]
    // 0x97fae4: fcmp            d0, d1
    // 0x97fae8: b.le            #0x97fb0c
    // 0x97faec: d0 = 0.000000
    //     0x97faec: eor             v0.16b, v0.16b, v0.16b
    // 0x97faf0: ArrayLoad: d1 = r1[0]  ; List_8
    //     0x97faf0: ldur            d1, [x1, #0x17]
    // 0x97faf4: fcmp            d1, d0
    // 0x97faf8: r16 = true
    //     0x97faf8: add             x16, NULL, #0x20  ; true
    // 0x97fafc: r17 = false
    //     0x97fafc: add             x17, NULL, #0x30  ; false
    // 0x97fb00: csel            x1, x16, x17, gt
    // 0x97fb04: mov             x0, x1
    // 0x97fb08: b               #0x97fb10
    // 0x97fb0c: r0 = false
    //     0x97fb0c: add             x0, NULL, #0x30  ; false
    // 0x97fb10: ret
    //     0x97fb10: ret             
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a1864, size: 0x228
    // 0x9a1864: EnterFrame
    //     0x9a1864: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1868: mov             fp, SP
    // 0x9a186c: AllocStack(0x18)
    //     0x9a186c: sub             SP, SP, #0x18
    // 0x9a1870: SetupParameters(_ScrollablePositionedListState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9a1870: mov             x0, x2
    //     0x9a1874: stur            x1, [fp, #-8]
    //     0x9a1878: stur            x2, [fp, #-0x10]
    // 0x9a187c: CheckStackOverflow
    //     0x9a187c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1880: cmp             SP, x16
    //     0x9a1884: b.ls            #0x9a1a78
    // 0x9a1888: r1 = 1
    //     0x9a1888: movz            x1, #0x1
    // 0x9a188c: r0 = AllocateContext()
    //     0x9a188c: bl              #0xec126c  ; AllocateContextStub
    // 0x9a1890: mov             x4, x0
    // 0x9a1894: ldur            x3, [fp, #-8]
    // 0x9a1898: stur            x4, [fp, #-0x18]
    // 0x9a189c: StoreField: r4->field_f = r3
    //     0x9a189c: stur            w3, [x4, #0xf]
    // 0x9a18a0: ldur            x0, [fp, #-0x10]
    // 0x9a18a4: r2 = Null
    //     0x9a18a4: mov             x2, NULL
    // 0x9a18a8: r1 = Null
    //     0x9a18a8: mov             x1, NULL
    // 0x9a18ac: r4 = 60
    //     0x9a18ac: movz            x4, #0x3c
    // 0x9a18b0: branchIfSmi(r0, 0x9a18bc)
    //     0x9a18b0: tbz             w0, #0, #0x9a18bc
    // 0x9a18b4: r4 = LoadClassIdInstr(r0)
    //     0x9a18b4: ldur            x4, [x0, #-1]
    //     0x9a18b8: ubfx            x4, x4, #0xc, #0x14
    // 0x9a18bc: r17 = 4695
    //     0x9a18bc: movz            x17, #0x1257
    // 0x9a18c0: cmp             x4, x17
    // 0x9a18c4: b.eq            #0x9a18dc
    // 0x9a18c8: r8 = ScrollablePositionedList
    //     0x9a18c8: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3d900] Type: ScrollablePositionedList
    //     0x9a18cc: ldr             x8, [x8, #0x900]
    // 0x9a18d0: r3 = Null
    //     0x9a18d0: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d908] Null
    //     0x9a18d4: ldr             x3, [x3, #0x908]
    // 0x9a18d8: r0 = ScrollablePositionedList()
    //     0x9a18d8: bl              #0x6fa6b0  ; IsType_ScrollablePositionedList_Stub
    // 0x9a18dc: ldur            x3, [fp, #-8]
    // 0x9a18e0: LoadField: r2 = r3->field_7
    //     0x9a18e0: ldur            w2, [x3, #7]
    // 0x9a18e4: DecompressPointer r2
    //     0x9a18e4: add             x2, x2, HEAP, lsl #32
    // 0x9a18e8: ldur            x0, [fp, #-0x10]
    // 0x9a18ec: r1 = Null
    //     0x9a18ec: mov             x1, NULL
    // 0x9a18f0: cmp             w2, NULL
    // 0x9a18f4: b.eq            #0x9a1918
    // 0x9a18f8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a18f8: ldur            w4, [x2, #0x17]
    // 0x9a18fc: DecompressPointer r4
    //     0x9a18fc: add             x4, x4, HEAP, lsl #32
    // 0x9a1900: r8 = X0 bound StatefulWidget
    //     0x9a1900: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a1904: ldr             x8, [x8, #0x7f8]
    // 0x9a1908: LoadField: r9 = r4->field_7
    //     0x9a1908: ldur            x9, [x4, #7]
    // 0x9a190c: r3 = Null
    //     0x9a190c: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d918] Null
    //     0x9a1910: ldr             x3, [x3, #0x918]
    // 0x9a1914: blr             x9
    // 0x9a1918: ldur            x0, [fp, #-0x10]
    // 0x9a191c: LoadField: r1 = r0->field_1b
    //     0x9a191c: ldur            w1, [x0, #0x1b]
    // 0x9a1920: DecompressPointer r1
    //     0x9a1920: add             x1, x1, HEAP, lsl #32
    // 0x9a1924: LoadField: r0 = r1->field_7
    //     0x9a1924: ldur            w0, [x1, #7]
    // 0x9a1928: DecompressPointer r0
    //     0x9a1928: add             x0, x0, HEAP, lsl #32
    // 0x9a192c: ldur            x2, [fp, #-8]
    // 0x9a1930: cmp             w0, w2
    // 0x9a1934: b.ne            #0x9a193c
    // 0x9a1938: r0 = _detach()
    //     0x9a1938: bl              #0x92bcf8  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] ItemScrollController::_detach
    // 0x9a193c: ldur            x0, [fp, #-8]
    // 0x9a1940: LoadField: r1 = r0->field_b
    //     0x9a1940: ldur            w1, [x0, #0xb]
    // 0x9a1944: DecompressPointer r1
    //     0x9a1944: add             x1, x1, HEAP, lsl #32
    // 0x9a1948: cmp             w1, NULL
    // 0x9a194c: b.eq            #0x9a1a80
    // 0x9a1950: LoadField: r2 = r1->field_1b
    //     0x9a1950: ldur            w2, [x1, #0x1b]
    // 0x9a1954: DecompressPointer r2
    //     0x9a1954: add             x2, x2, HEAP, lsl #32
    // 0x9a1958: LoadField: r3 = r2->field_7
    //     0x9a1958: ldur            w3, [x2, #7]
    // 0x9a195c: DecompressPointer r3
    //     0x9a195c: add             x3, x3, HEAP, lsl #32
    // 0x9a1960: cmp             w3, w0
    // 0x9a1964: b.eq            #0x9a19b4
    // 0x9a1968: mov             x1, x2
    // 0x9a196c: r0 = _detach()
    //     0x9a196c: bl              #0x92bcf8  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] ItemScrollController::_detach
    // 0x9a1970: ldur            x3, [fp, #-8]
    // 0x9a1974: LoadField: r1 = r3->field_b
    //     0x9a1974: ldur            w1, [x3, #0xb]
    // 0x9a1978: DecompressPointer r1
    //     0x9a1978: add             x1, x1, HEAP, lsl #32
    // 0x9a197c: cmp             w1, NULL
    // 0x9a1980: b.eq            #0x9a1a84
    // 0x9a1984: LoadField: r2 = r1->field_1b
    //     0x9a1984: ldur            w2, [x1, #0x1b]
    // 0x9a1988: DecompressPointer r2
    //     0x9a1988: add             x2, x2, HEAP, lsl #32
    // 0x9a198c: mov             x0, x3
    // 0x9a1990: StoreField: r2->field_7 = r0
    //     0x9a1990: stur            w0, [x2, #7]
    //     0x9a1994: ldurb           w16, [x2, #-1]
    //     0x9a1998: ldurb           w17, [x0, #-1]
    //     0x9a199c: and             x16, x17, x16, lsr #2
    //     0x9a19a0: tst             x16, HEAP, lsr #32
    //     0x9a19a4: b.eq            #0x9a19ac
    //     0x9a19a8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9a19ac: mov             x0, x1
    // 0x9a19b0: b               #0x9a19bc
    // 0x9a19b4: mov             x3, x0
    // 0x9a19b8: mov             x0, x1
    // 0x9a19bc: LoadField: r1 = r0->field_b
    //     0x9a19bc: ldur            x1, [x0, #0xb]
    // 0x9a19c0: cbnz            x1, #0x9a19e4
    // 0x9a19c4: ldur            x2, [fp, #-0x18]
    // 0x9a19c8: r1 = Function '<anonymous closure>':.
    //     0x9a19c8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d928] AnonymousClosure: (0x9a1b24), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::didUpdateWidget (0x9a1864)
    //     0x9a19cc: ldr             x1, [x1, #0x928]
    // 0x9a19d0: r0 = AllocateClosure()
    //     0x9a19d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a19d4: ldur            x1, [fp, #-8]
    // 0x9a19d8: mov             x2, x0
    // 0x9a19dc: r0 = setState()
    //     0x9a19dc: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9a19e0: b               #0x9a1a68
    // 0x9a19e4: mov             x0, x3
    // 0x9a19e8: LoadField: r2 = r0->field_1b
    //     0x9a19e8: ldur            w2, [x0, #0x1b]
    // 0x9a19ec: DecompressPointer r2
    //     0x9a19ec: add             x2, x2, HEAP, lsl #32
    // 0x9a19f0: LoadField: r3 = r2->field_f
    //     0x9a19f0: ldur            x3, [x2, #0xf]
    // 0x9a19f4: sub             x2, x1, #1
    // 0x9a19f8: cmp             x3, x2
    // 0x9a19fc: b.le            #0x9a1a1c
    // 0x9a1a00: ldur            x2, [fp, #-0x18]
    // 0x9a1a04: r1 = Function '<anonymous closure>':.
    //     0x9a1a04: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d930] AnonymousClosure: (0x9a1ad8), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::didUpdateWidget (0x9a1864)
    //     0x9a1a08: ldr             x1, [x1, #0x930]
    // 0x9a1a0c: r0 = AllocateClosure()
    //     0x9a1a0c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a1a10: ldur            x1, [fp, #-8]
    // 0x9a1a14: mov             x2, x0
    // 0x9a1a18: r0 = setState()
    //     0x9a1a18: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9a1a1c: ldur            x0, [fp, #-8]
    // 0x9a1a20: LoadField: r1 = r0->field_1f
    //     0x9a1a20: ldur            w1, [x0, #0x1f]
    // 0x9a1a24: DecompressPointer r1
    //     0x9a1a24: add             x1, x1, HEAP, lsl #32
    // 0x9a1a28: LoadField: r2 = r1->field_f
    //     0x9a1a28: ldur            x2, [x1, #0xf]
    // 0x9a1a2c: LoadField: r1 = r0->field_b
    //     0x9a1a2c: ldur            w1, [x0, #0xb]
    // 0x9a1a30: DecompressPointer r1
    //     0x9a1a30: add             x1, x1, HEAP, lsl #32
    // 0x9a1a34: cmp             w1, NULL
    // 0x9a1a38: b.eq            #0x9a1a88
    // 0x9a1a3c: LoadField: r3 = r1->field_b
    //     0x9a1a3c: ldur            x3, [x1, #0xb]
    // 0x9a1a40: sub             x1, x3, #1
    // 0x9a1a44: cmp             x2, x1
    // 0x9a1a48: b.le            #0x9a1a68
    // 0x9a1a4c: ldur            x2, [fp, #-0x18]
    // 0x9a1a50: r1 = Function '<anonymous closure>':.
    //     0x9a1a50: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d938] AnonymousClosure: (0x9a1a8c), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::didUpdateWidget (0x9a1864)
    //     0x9a1a54: ldr             x1, [x1, #0x938]
    // 0x9a1a58: r0 = AllocateClosure()
    //     0x9a1a58: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a1a5c: ldur            x1, [fp, #-8]
    // 0x9a1a60: mov             x2, x0
    // 0x9a1a64: r0 = setState()
    //     0x9a1a64: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9a1a68: r0 = Null
    //     0x9a1a68: mov             x0, NULL
    // 0x9a1a6c: LeaveFrame
    //     0x9a1a6c: mov             SP, fp
    //     0x9a1a70: ldp             fp, lr, [SP], #0x10
    // 0x9a1a74: ret
    //     0x9a1a74: ret             
    // 0x9a1a78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1a78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1a7c: b               #0x9a1888
    // 0x9a1a80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1a80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a1a84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1a84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a1a88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1a88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9a1a8c, size: 0x4c
    // 0x9a1a8c: ldr             x1, [SP]
    // 0x9a1a90: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9a1a90: ldur            w2, [x1, #0x17]
    // 0x9a1a94: DecompressPointer r2
    //     0x9a1a94: add             x2, x2, HEAP, lsl #32
    // 0x9a1a98: LoadField: r1 = r2->field_f
    //     0x9a1a98: ldur            w1, [x2, #0xf]
    // 0x9a1a9c: DecompressPointer r1
    //     0x9a1a9c: add             x1, x1, HEAP, lsl #32
    // 0x9a1aa0: LoadField: r2 = r1->field_1f
    //     0x9a1aa0: ldur            w2, [x1, #0x1f]
    // 0x9a1aa4: DecompressPointer r2
    //     0x9a1aa4: add             x2, x2, HEAP, lsl #32
    // 0x9a1aa8: LoadField: r3 = r1->field_b
    //     0x9a1aa8: ldur            w3, [x1, #0xb]
    // 0x9a1aac: DecompressPointer r3
    //     0x9a1aac: add             x3, x3, HEAP, lsl #32
    // 0x9a1ab0: cmp             w3, NULL
    // 0x9a1ab4: b.eq            #0x9a1acc
    // 0x9a1ab8: LoadField: r1 = r3->field_b
    //     0x9a1ab8: ldur            x1, [x3, #0xb]
    // 0x9a1abc: sub             x3, x1, #1
    // 0x9a1ac0: StoreField: r2->field_f = r3
    //     0x9a1ac0: stur            x3, [x2, #0xf]
    // 0x9a1ac4: r0 = Null
    //     0x9a1ac4: mov             x0, NULL
    // 0x9a1ac8: ret
    //     0x9a1ac8: ret             
    // 0x9a1acc: EnterFrame
    //     0x9a1acc: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1ad0: mov             fp, SP
    // 0x9a1ad4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1ad4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9a1ad8, size: 0x4c
    // 0x9a1ad8: ldr             x1, [SP]
    // 0x9a1adc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9a1adc: ldur            w2, [x1, #0x17]
    // 0x9a1ae0: DecompressPointer r2
    //     0x9a1ae0: add             x2, x2, HEAP, lsl #32
    // 0x9a1ae4: LoadField: r1 = r2->field_f
    //     0x9a1ae4: ldur            w1, [x2, #0xf]
    // 0x9a1ae8: DecompressPointer r1
    //     0x9a1ae8: add             x1, x1, HEAP, lsl #32
    // 0x9a1aec: LoadField: r2 = r1->field_1b
    //     0x9a1aec: ldur            w2, [x1, #0x1b]
    // 0x9a1af0: DecompressPointer r2
    //     0x9a1af0: add             x2, x2, HEAP, lsl #32
    // 0x9a1af4: LoadField: r3 = r1->field_b
    //     0x9a1af4: ldur            w3, [x1, #0xb]
    // 0x9a1af8: DecompressPointer r3
    //     0x9a1af8: add             x3, x3, HEAP, lsl #32
    // 0x9a1afc: cmp             w3, NULL
    // 0x9a1b00: b.eq            #0x9a1b18
    // 0x9a1b04: LoadField: r1 = r3->field_b
    //     0x9a1b04: ldur            x1, [x3, #0xb]
    // 0x9a1b08: sub             x3, x1, #1
    // 0x9a1b0c: StoreField: r2->field_f = r3
    //     0x9a1b0c: stur            x3, [x2, #0xf]
    // 0x9a1b10: r0 = Null
    //     0x9a1b10: mov             x0, NULL
    // 0x9a1b14: ret
    //     0x9a1b14: ret             
    // 0x9a1b18: EnterFrame
    //     0x9a1b18: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1b1c: mov             fp, SP
    // 0x9a1b20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1b20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9a1b24, size: 0x34
    // 0x9a1b24: ldr             x1, [SP]
    // 0x9a1b28: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9a1b28: ldur            w2, [x1, #0x17]
    // 0x9a1b2c: DecompressPointer r2
    //     0x9a1b2c: add             x2, x2, HEAP, lsl #32
    // 0x9a1b30: LoadField: r1 = r2->field_f
    //     0x9a1b30: ldur            w1, [x2, #0xf]
    // 0x9a1b34: DecompressPointer r1
    //     0x9a1b34: add             x1, x1, HEAP, lsl #32
    // 0x9a1b38: LoadField: r2 = r1->field_1b
    //     0x9a1b38: ldur            w2, [x1, #0x1b]
    // 0x9a1b3c: DecompressPointer r2
    //     0x9a1b3c: add             x2, x2, HEAP, lsl #32
    // 0x9a1b40: StoreField: r2->field_f = rZR
    //     0x9a1b40: stur            xzr, [x2, #0xf]
    // 0x9a1b44: LoadField: r2 = r1->field_1f
    //     0x9a1b44: ldur            w2, [x1, #0x1f]
    // 0x9a1b48: DecompressPointer r2
    //     0x9a1b48: add             x2, x2, HEAP, lsl #32
    // 0x9a1b4c: StoreField: r2->field_f = rZR
    //     0x9a1b4c: stur            xzr, [x2, #0xf]
    // 0x9a1b50: r0 = Null
    //     0x9a1b50: mov             x0, NULL
    // 0x9a1b54: ret
    //     0x9a1b54: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0xa4bd44, size: 0x58
    // 0xa4bd44: EnterFrame
    //     0xa4bd44: stp             fp, lr, [SP, #-0x10]!
    //     0xa4bd48: mov             fp, SP
    // 0xa4bd4c: AllocStack(0x8)
    //     0xa4bd4c: sub             SP, SP, #8
    // 0xa4bd50: SetupParameters(_ScrollablePositionedListState this /* r1 => r1, fp-0x8 */)
    //     0xa4bd50: stur            x1, [fp, #-8]
    // 0xa4bd54: r1 = 1
    //     0xa4bd54: movz            x1, #0x1
    // 0xa4bd58: r0 = AllocateContext()
    //     0xa4bd58: bl              #0xec126c  ; AllocateContextStub
    // 0xa4bd5c: mov             x1, x0
    // 0xa4bd60: ldur            x0, [fp, #-8]
    // 0xa4bd64: StoreField: r1->field_f = r0
    //     0xa4bd64: stur            w0, [x1, #0xf]
    // 0xa4bd68: mov             x2, x1
    // 0xa4bd6c: r1 = Function '<anonymous closure>':.
    //     0xa4bd6c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] AnonymousClosure: (0xa4bd9c), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::build (0xa4bd44)
    //     0xa4bd70: ldr             x1, [x1, #0x8e0]
    // 0xa4bd74: r0 = AllocateClosure()
    //     0xa4bd74: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4bd78: r1 = <BoxConstraints>
    //     0xa4bd78: add             x1, PP, #0x37, lsl #12  ; [pp+0x37fa8] TypeArguments: <BoxConstraints>
    //     0xa4bd7c: ldr             x1, [x1, #0xfa8]
    // 0xa4bd80: stur            x0, [fp, #-8]
    // 0xa4bd84: r0 = LayoutBuilder()
    //     0xa4bd84: bl              #0x9f1460  ; AllocateLayoutBuilderStub -> LayoutBuilder (size=0x14)
    // 0xa4bd88: ldur            x1, [fp, #-8]
    // 0xa4bd8c: StoreField: r0->field_f = r1
    //     0xa4bd8c: stur            w1, [x0, #0xf]
    // 0xa4bd90: LeaveFrame
    //     0xa4bd90: mov             SP, fp
    //     0xa4bd94: ldp             fp, lr, [SP], #0x10
    // 0xa4bd98: ret
    //     0xa4bd98: ret             
  }
  [closure] Listener <anonymous closure>(dynamic, BuildContext, BoxConstraints) {
    // ** addr: 0xa4bd9c, size: 0x464
    // 0xa4bd9c: EnterFrame
    //     0xa4bd9c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4bda0: mov             fp, SP
    // 0xa4bda4: AllocStack(0x70)
    //     0xa4bda4: sub             SP, SP, #0x70
    // 0xa4bda8: SetupParameters()
    //     0xa4bda8: ldr             x0, [fp, #0x20]
    //     0xa4bdac: ldur            w3, [x0, #0x17]
    //     0xa4bdb0: add             x3, x3, HEAP, lsl #32
    //     0xa4bdb4: stur            x3, [fp, #-8]
    // 0xa4bdb8: CheckStackOverflow
    //     0xa4bdb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4bdbc: cmp             SP, x16
    //     0xa4bdc0: b.ls            #0xa4c1f4
    // 0xa4bdc4: LoadField: r1 = r3->field_f
    //     0xa4bdc4: ldur            w1, [x3, #0xf]
    // 0xa4bdc8: DecompressPointer r1
    //     0xa4bdc8: add             x1, x1, HEAP, lsl #32
    // 0xa4bdcc: ldr             x2, [fp, #0x10]
    // 0xa4bdd0: r0 = _cacheExtent()
    //     0xa4bdd0: bl              #0xa4c218  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_cacheExtent
    // 0xa4bdd4: ldur            x2, [fp, #-8]
    // 0xa4bdd8: stur            d0, [fp, #-0x68]
    // 0xa4bddc: LoadField: r0 = r2->field_f
    //     0xa4bddc: ldur            w0, [x2, #0xf]
    // 0xa4bde0: DecompressPointer r0
    //     0xa4bde0: add             x0, x0, HEAP, lsl #32
    // 0xa4bde4: LoadField: r1 = r0->field_1b
    //     0xa4bde4: ldur            w1, [x0, #0x1b]
    // 0xa4bde8: DecompressPointer r1
    //     0xa4bde8: add             x1, x1, HEAP, lsl #32
    // 0xa4bdec: LoadField: r3 = r1->field_1f
    //     0xa4bdec: ldur            w3, [x1, #0x1f]
    // 0xa4bdf0: DecompressPointer r3
    //     0xa4bdf0: add             x3, x3, HEAP, lsl #32
    // 0xa4bdf4: stur            x3, [fp, #-0x20]
    // 0xa4bdf8: LoadField: r4 = r0->field_27
    //     0xa4bdf8: ldur            w4, [x0, #0x27]
    // 0xa4bdfc: DecompressPointer r4
    //     0xa4bdfc: add             x4, x4, HEAP, lsl #32
    // 0xa4be00: stur            x4, [fp, #-0x18]
    // 0xa4be04: LoadField: r5 = r0->field_23
    //     0xa4be04: ldur            w5, [x0, #0x23]
    // 0xa4be08: DecompressPointer r5
    //     0xa4be08: add             x5, x5, HEAP, lsl #32
    // 0xa4be0c: stur            x5, [fp, #-0x10]
    // 0xa4be10: r1 = <double>
    //     0xa4be10: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa4be14: r0 = ReverseAnimation()
    //     0xa4be14: bl              #0x9390b4  ; AllocateReverseAnimationStub -> ReverseAnimation (size=0x1c)
    // 0xa4be18: mov             x2, x0
    // 0xa4be1c: ldur            x0, [fp, #-0x10]
    // 0xa4be20: stur            x2, [fp, #-0x28]
    // 0xa4be24: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4be24: stur            w0, [x2, #0x17]
    // 0xa4be28: mov             x1, x2
    // 0xa4be2c: r0 = _ReverseAnimation&Animation&AnimationLazyListenerMixin&AnimationLocalStatusListenersMixin()
    //     0xa4be2c: bl              #0x939008  ; [package:flutter/src/animation/animations.dart] _ReverseAnimation&Animation&AnimationLazyListenerMixin&AnimationLocalStatusListenersMixin::_ReverseAnimation&Animation&AnimationLazyListenerMixin&AnimationLocalStatusListenersMixin
    // 0xa4be30: ldur            x2, [fp, #-8]
    // 0xa4be34: LoadField: r0 = r2->field_f
    //     0xa4be34: ldur            w0, [x2, #0xf]
    // 0xa4be38: DecompressPointer r0
    //     0xa4be38: add             x0, x0, HEAP, lsl #32
    // 0xa4be3c: stur            x0, [fp, #-0x58]
    // 0xa4be40: LoadField: r1 = r0->field_b
    //     0xa4be40: ldur            w1, [x0, #0xb]
    // 0xa4be44: DecompressPointer r1
    //     0xa4be44: add             x1, x1, HEAP, lsl #32
    // 0xa4be48: cmp             w1, NULL
    // 0xa4be4c: b.eq            #0xa4c1fc
    // 0xa4be50: LoadField: r3 = r1->field_13
    //     0xa4be50: ldur            w3, [x1, #0x13]
    // 0xa4be54: DecompressPointer r3
    //     0xa4be54: add             x3, x3, HEAP, lsl #32
    // 0xa4be58: stur            x3, [fp, #-0x50]
    // 0xa4be5c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa4be5c: ldur            w4, [x1, #0x17]
    // 0xa4be60: DecompressPointer r4
    //     0xa4be60: add             x4, x4, HEAP, lsl #32
    // 0xa4be64: stur            x4, [fp, #-0x48]
    // 0xa4be68: LoadField: r5 = r1->field_b
    //     0xa4be68: ldur            x5, [x1, #0xb]
    // 0xa4be6c: stur            x5, [fp, #-0x40]
    // 0xa4be70: LoadField: r1 = r0->field_1b
    //     0xa4be70: ldur            w1, [x0, #0x1b]
    // 0xa4be74: DecompressPointer r1
    //     0xa4be74: add             x1, x1, HEAP, lsl #32
    // 0xa4be78: LoadField: r6 = r1->field_f
    //     0xa4be78: ldur            x6, [x1, #0xf]
    // 0xa4be7c: stur            x6, [fp, #-0x38]
    // 0xa4be80: LoadField: r7 = r1->field_b
    //     0xa4be80: ldur            w7, [x1, #0xb]
    // 0xa4be84: DecompressPointer r7
    //     0xa4be84: add             x7, x7, HEAP, lsl #32
    // 0xa4be88: stur            x7, [fp, #-0x30]
    // 0xa4be8c: LoadField: r8 = r1->field_7
    //     0xa4be8c: ldur            w8, [x1, #7]
    // 0xa4be90: DecompressPointer r8
    //     0xa4be90: add             x8, x8, HEAP, lsl #32
    // 0xa4be94: stur            x8, [fp, #-0x10]
    // 0xa4be98: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xa4be98: ldur            d0, [x1, #0x17]
    // 0xa4be9c: stur            d0, [fp, #-0x70]
    // 0xa4bea0: r0 = PositionedList()
    //     0xa4bea0: bl              #0xa4c20c  ; AllocatePositionedListStub -> PositionedList (size=0x60)
    // 0xa4bea4: mov             x3, x0
    // 0xa4bea8: ldur            x0, [fp, #-0x40]
    // 0xa4beac: stur            x3, [fp, #-0x60]
    // 0xa4beb0: StoreField: r3->field_b = r0
    //     0xa4beb0: stur            x0, [x3, #0xb]
    // 0xa4beb4: ldur            x4, [fp, #-0x50]
    // 0xa4beb8: StoreField: r3->field_13 = r4
    //     0xa4beb8: stur            w4, [x3, #0x13]
    // 0xa4bebc: ldur            x5, [fp, #-0x48]
    // 0xa4bec0: ArrayStore: r3[0] = r5  ; List_4
    //     0xa4bec0: stur            w5, [x3, #0x17]
    // 0xa4bec4: ldur            x1, [fp, #-0x30]
    // 0xa4bec8: StoreField: r3->field_1b = r1
    //     0xa4bec8: stur            w1, [x3, #0x1b]
    // 0xa4becc: ldur            x1, [fp, #-0x10]
    // 0xa4bed0: StoreField: r3->field_1f = r1
    //     0xa4bed0: stur            w1, [x3, #0x1f]
    // 0xa4bed4: ldur            x1, [fp, #-0x38]
    // 0xa4bed8: StoreField: r3->field_23 = r1
    //     0xa4bed8: stur            x1, [x3, #0x23]
    // 0xa4bedc: ldur            d0, [fp, #-0x70]
    // 0xa4bee0: StoreField: r3->field_2b = d0
    //     0xa4bee0: stur            d0, [x3, #0x2b]
    // 0xa4bee4: r6 = Instance_Axis
    //     0xa4bee4: ldr             x6, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa4bee8: StoreField: r3->field_33 = r6
    //     0xa4bee8: stur            w6, [x3, #0x33]
    // 0xa4beec: r7 = false
    //     0xa4beec: add             x7, NULL, #0x30  ; false
    // 0xa4bef0: StoreField: r3->field_37 = r7
    //     0xa4bef0: stur            w7, [x3, #0x37]
    // 0xa4bef4: StoreField: r3->field_3b = r7
    //     0xa4bef4: stur            w7, [x3, #0x3b]
    // 0xa4bef8: ldur            d0, [fp, #-0x68]
    // 0xa4befc: StoreField: r3->field_43 = d0
    //     0xa4befc: stur            d0, [x3, #0x43]
    // 0xa4bf00: r8 = true
    //     0xa4bf00: add             x8, NULL, #0x20  ; true
    // 0xa4bf04: StoreField: r3->field_4f = r8
    //     0xa4bf04: stur            w8, [x3, #0x4f]
    // 0xa4bf08: StoreField: r3->field_57 = r8
    //     0xa4bf08: stur            w8, [x3, #0x57]
    // 0xa4bf0c: StoreField: r3->field_5b = r8
    //     0xa4bf0c: stur            w8, [x3, #0x5b]
    // 0xa4bf10: ldur            x2, [fp, #-8]
    // 0xa4bf14: r1 = Function '<anonymous closure>':.
    //     0xa4bf14: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8e8] AnonymousClosure: (0xa4c2d8), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::build (0xa4bd44)
    //     0xa4bf18: ldr             x1, [x1, #0x8e8]
    // 0xa4bf1c: r0 = AllocateClosure()
    //     0xa4bf1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4bf20: r1 = <ScrollNotification>
    //     0xa4bf20: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xa4bf24: ldr             x1, [x1, #0x110]
    // 0xa4bf28: stur            x0, [fp, #-0x10]
    // 0xa4bf2c: r0 = NotificationListener()
    //     0xa4bf2c: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xa4bf30: mov             x1, x0
    // 0xa4bf34: ldur            x0, [fp, #-0x10]
    // 0xa4bf38: stur            x1, [fp, #-0x30]
    // 0xa4bf3c: StoreField: r1->field_13 = r0
    //     0xa4bf3c: stur            w0, [x1, #0x13]
    // 0xa4bf40: ldur            x0, [fp, #-0x60]
    // 0xa4bf44: StoreField: r1->field_b = r0
    //     0xa4bf44: stur            w0, [x1, #0xb]
    // 0xa4bf48: r0 = FadeTransition()
    //     0xa4bf48: bl              #0x91a518  ; AllocateFadeTransitionStub -> FadeTransition (size=0x18)
    // 0xa4bf4c: mov             x1, x0
    // 0xa4bf50: ldur            x0, [fp, #-0x28]
    // 0xa4bf54: stur            x1, [fp, #-0x10]
    // 0xa4bf58: StoreField: r1->field_f = r0
    //     0xa4bf58: stur            w0, [x1, #0xf]
    // 0xa4bf5c: r0 = false
    //     0xa4bf5c: add             x0, NULL, #0x30  ; false
    // 0xa4bf60: StoreField: r1->field_13 = r0
    //     0xa4bf60: stur            w0, [x1, #0x13]
    // 0xa4bf64: ldur            x2, [fp, #-0x30]
    // 0xa4bf68: StoreField: r1->field_b = r2
    //     0xa4bf68: stur            w2, [x1, #0xb]
    // 0xa4bf6c: r0 = PostMountCallback()
    //     0xa4bf6c: bl              #0xa4c200  ; AllocatePostMountCallbackStub -> PostMountCallback (size=0x14)
    // 0xa4bf70: mov             x3, x0
    // 0xa4bf74: ldur            x0, [fp, #-0x10]
    // 0xa4bf78: stur            x3, [fp, #-0x28]
    // 0xa4bf7c: StoreField: r3->field_b = r0
    //     0xa4bf7c: stur            w0, [x3, #0xb]
    // 0xa4bf80: ldur            x0, [fp, #-0x18]
    // 0xa4bf84: StoreField: r3->field_f = r0
    //     0xa4bf84: stur            w0, [x3, #0xf]
    // 0xa4bf88: ldur            x0, [fp, #-0x20]
    // 0xa4bf8c: StoreField: r3->field_7 = r0
    //     0xa4bf8c: stur            w0, [x3, #7]
    // 0xa4bf90: r1 = Null
    //     0xa4bf90: mov             x1, NULL
    // 0xa4bf94: r2 = 2
    //     0xa4bf94: movz            x2, #0x2
    // 0xa4bf98: r0 = AllocateArray()
    //     0xa4bf98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa4bf9c: mov             x2, x0
    // 0xa4bfa0: ldur            x0, [fp, #-0x28]
    // 0xa4bfa4: stur            x2, [fp, #-0x10]
    // 0xa4bfa8: StoreField: r2->field_f = r0
    //     0xa4bfa8: stur            w0, [x2, #0xf]
    // 0xa4bfac: r1 = <Widget>
    //     0xa4bfac: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa4bfb0: r0 = AllocateGrowableArray()
    //     0xa4bfb0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa4bfb4: mov             x1, x0
    // 0xa4bfb8: ldur            x0, [fp, #-0x10]
    // 0xa4bfbc: stur            x1, [fp, #-0x60]
    // 0xa4bfc0: StoreField: r1->field_f = r0
    //     0xa4bfc0: stur            w0, [x1, #0xf]
    // 0xa4bfc4: r0 = 2
    //     0xa4bfc4: movz            x0, #0x2
    // 0xa4bfc8: StoreField: r1->field_b = r0
    //     0xa4bfc8: stur            w0, [x1, #0xb]
    // 0xa4bfcc: ldur            x0, [fp, #-0x58]
    // 0xa4bfd0: LoadField: r2 = r0->field_2b
    //     0xa4bfd0: ldur            w2, [x0, #0x2b]
    // 0xa4bfd4: DecompressPointer r2
    //     0xa4bfd4: add             x2, x2, HEAP, lsl #32
    // 0xa4bfd8: tbnz            w2, #4, #0xa4c178
    // 0xa4bfdc: ldur            d0, [fp, #-0x68]
    // 0xa4bfe0: ldur            x3, [fp, #-0x50]
    // 0xa4bfe4: ldur            x4, [fp, #-0x48]
    // 0xa4bfe8: ldur            x2, [fp, #-0x40]
    // 0xa4bfec: LoadField: r5 = r0->field_1f
    //     0xa4bfec: ldur            w5, [x0, #0x1f]
    // 0xa4bff0: DecompressPointer r5
    //     0xa4bff0: add             x5, x5, HEAP, lsl #32
    // 0xa4bff4: LoadField: r6 = r5->field_1f
    //     0xa4bff4: ldur            w6, [x5, #0x1f]
    // 0xa4bff8: DecompressPointer r6
    //     0xa4bff8: add             x6, x6, HEAP, lsl #32
    // 0xa4bffc: stur            x6, [fp, #-0x30]
    // 0xa4c000: LoadField: r7 = r0->field_27
    //     0xa4c000: ldur            w7, [x0, #0x27]
    // 0xa4c004: DecompressPointer r7
    //     0xa4c004: add             x7, x7, HEAP, lsl #32
    // 0xa4c008: stur            x7, [fp, #-0x28]
    // 0xa4c00c: LoadField: r8 = r0->field_23
    //     0xa4c00c: ldur            w8, [x0, #0x23]
    // 0xa4c010: DecompressPointer r8
    //     0xa4c010: add             x8, x8, HEAP, lsl #32
    // 0xa4c014: stur            x8, [fp, #-0x20]
    // 0xa4c018: LoadField: r0 = r5->field_7
    //     0xa4c018: ldur            w0, [x5, #7]
    // 0xa4c01c: DecompressPointer r0
    //     0xa4c01c: add             x0, x0, HEAP, lsl #32
    // 0xa4c020: stur            x0, [fp, #-0x18]
    // 0xa4c024: LoadField: r9 = r5->field_f
    //     0xa4c024: ldur            x9, [x5, #0xf]
    // 0xa4c028: stur            x9, [fp, #-0x38]
    // 0xa4c02c: LoadField: r10 = r5->field_b
    //     0xa4c02c: ldur            w10, [x5, #0xb]
    // 0xa4c030: DecompressPointer r10
    //     0xa4c030: add             x10, x10, HEAP, lsl #32
    // 0xa4c034: stur            x10, [fp, #-0x10]
    // 0xa4c038: ArrayLoad: d1 = r5[0]  ; List_8
    //     0xa4c038: ldur            d1, [x5, #0x17]
    // 0xa4c03c: stur            d1, [fp, #-0x70]
    // 0xa4c040: r0 = PositionedList()
    //     0xa4c040: bl              #0xa4c20c  ; AllocatePositionedListStub -> PositionedList (size=0x60)
    // 0xa4c044: mov             x3, x0
    // 0xa4c048: ldur            x0, [fp, #-0x40]
    // 0xa4c04c: stur            x3, [fp, #-0x58]
    // 0xa4c050: StoreField: r3->field_b = r0
    //     0xa4c050: stur            x0, [x3, #0xb]
    // 0xa4c054: ldur            x0, [fp, #-0x50]
    // 0xa4c058: StoreField: r3->field_13 = r0
    //     0xa4c058: stur            w0, [x3, #0x13]
    // 0xa4c05c: ldur            x0, [fp, #-0x48]
    // 0xa4c060: ArrayStore: r3[0] = r0  ; List_4
    //     0xa4c060: stur            w0, [x3, #0x17]
    // 0xa4c064: ldur            x0, [fp, #-0x10]
    // 0xa4c068: StoreField: r3->field_1b = r0
    //     0xa4c068: stur            w0, [x3, #0x1b]
    // 0xa4c06c: ldur            x0, [fp, #-0x18]
    // 0xa4c070: StoreField: r3->field_1f = r0
    //     0xa4c070: stur            w0, [x3, #0x1f]
    // 0xa4c074: ldur            x0, [fp, #-0x38]
    // 0xa4c078: StoreField: r3->field_23 = r0
    //     0xa4c078: stur            x0, [x3, #0x23]
    // 0xa4c07c: ldur            d0, [fp, #-0x70]
    // 0xa4c080: StoreField: r3->field_2b = d0
    //     0xa4c080: stur            d0, [x3, #0x2b]
    // 0xa4c084: r0 = Instance_Axis
    //     0xa4c084: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa4c088: StoreField: r3->field_33 = r0
    //     0xa4c088: stur            w0, [x3, #0x33]
    // 0xa4c08c: r0 = false
    //     0xa4c08c: add             x0, NULL, #0x30  ; false
    // 0xa4c090: StoreField: r3->field_37 = r0
    //     0xa4c090: stur            w0, [x3, #0x37]
    // 0xa4c094: StoreField: r3->field_3b = r0
    //     0xa4c094: stur            w0, [x3, #0x3b]
    // 0xa4c098: ldur            d0, [fp, #-0x68]
    // 0xa4c09c: StoreField: r3->field_43 = d0
    //     0xa4c09c: stur            d0, [x3, #0x43]
    // 0xa4c0a0: r1 = true
    //     0xa4c0a0: add             x1, NULL, #0x20  ; true
    // 0xa4c0a4: StoreField: r3->field_4f = r1
    //     0xa4c0a4: stur            w1, [x3, #0x4f]
    // 0xa4c0a8: StoreField: r3->field_57 = r1
    //     0xa4c0a8: stur            w1, [x3, #0x57]
    // 0xa4c0ac: StoreField: r3->field_5b = r1
    //     0xa4c0ac: stur            w1, [x3, #0x5b]
    // 0xa4c0b0: r1 = Function '<anonymous closure>':.
    //     0xa4c0b0: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8f0] Function: [dart:core] Object::_simpleInstanceOfFalse (0xebd578)
    //     0xa4c0b4: ldr             x1, [x1, #0x8f0]
    // 0xa4c0b8: r2 = Null
    //     0xa4c0b8: mov             x2, NULL
    // 0xa4c0bc: r0 = AllocateClosure()
    //     0xa4c0bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4c0c0: r1 = <ScrollNotification>
    //     0xa4c0c0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xa4c0c4: ldr             x1, [x1, #0x110]
    // 0xa4c0c8: stur            x0, [fp, #-0x10]
    // 0xa4c0cc: r0 = NotificationListener()
    //     0xa4c0cc: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xa4c0d0: mov             x1, x0
    // 0xa4c0d4: ldur            x0, [fp, #-0x10]
    // 0xa4c0d8: stur            x1, [fp, #-0x18]
    // 0xa4c0dc: StoreField: r1->field_13 = r0
    //     0xa4c0dc: stur            w0, [x1, #0x13]
    // 0xa4c0e0: ldur            x0, [fp, #-0x58]
    // 0xa4c0e4: StoreField: r1->field_b = r0
    //     0xa4c0e4: stur            w0, [x1, #0xb]
    // 0xa4c0e8: r0 = FadeTransition()
    //     0xa4c0e8: bl              #0x91a518  ; AllocateFadeTransitionStub -> FadeTransition (size=0x18)
    // 0xa4c0ec: mov             x1, x0
    // 0xa4c0f0: ldur            x0, [fp, #-0x20]
    // 0xa4c0f4: stur            x1, [fp, #-0x10]
    // 0xa4c0f8: StoreField: r1->field_f = r0
    //     0xa4c0f8: stur            w0, [x1, #0xf]
    // 0xa4c0fc: r0 = false
    //     0xa4c0fc: add             x0, NULL, #0x30  ; false
    // 0xa4c100: StoreField: r1->field_13 = r0
    //     0xa4c100: stur            w0, [x1, #0x13]
    // 0xa4c104: ldur            x0, [fp, #-0x18]
    // 0xa4c108: StoreField: r1->field_b = r0
    //     0xa4c108: stur            w0, [x1, #0xb]
    // 0xa4c10c: r0 = PostMountCallback()
    //     0xa4c10c: bl              #0xa4c200  ; AllocatePostMountCallbackStub -> PostMountCallback (size=0x14)
    // 0xa4c110: mov             x2, x0
    // 0xa4c114: ldur            x0, [fp, #-0x10]
    // 0xa4c118: stur            x2, [fp, #-0x18]
    // 0xa4c11c: StoreField: r2->field_b = r0
    //     0xa4c11c: stur            w0, [x2, #0xb]
    // 0xa4c120: ldur            x0, [fp, #-0x28]
    // 0xa4c124: StoreField: r2->field_f = r0
    //     0xa4c124: stur            w0, [x2, #0xf]
    // 0xa4c128: ldur            x0, [fp, #-0x30]
    // 0xa4c12c: StoreField: r2->field_7 = r0
    //     0xa4c12c: stur            w0, [x2, #7]
    // 0xa4c130: ldur            x1, [fp, #-0x60]
    // 0xa4c134: r0 = _growToNextCapacity()
    //     0xa4c134: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4c138: ldur            x2, [fp, #-0x60]
    // 0xa4c13c: r0 = 4
    //     0xa4c13c: movz            x0, #0x4
    // 0xa4c140: StoreField: r2->field_b = r0
    //     0xa4c140: stur            w0, [x2, #0xb]
    // 0xa4c144: LoadField: r1 = r2->field_f
    //     0xa4c144: ldur            w1, [x2, #0xf]
    // 0xa4c148: DecompressPointer r1
    //     0xa4c148: add             x1, x1, HEAP, lsl #32
    // 0xa4c14c: ldur            x0, [fp, #-0x18]
    // 0xa4c150: ArrayStore: r1[1] = r0  ; List_4
    //     0xa4c150: add             x25, x1, #0x13
    //     0xa4c154: str             w0, [x25]
    //     0xa4c158: tbz             w0, #0, #0xa4c174
    //     0xa4c15c: ldurb           w16, [x1, #-1]
    //     0xa4c160: ldurb           w17, [x0, #-1]
    //     0xa4c164: and             x16, x17, x16, lsr #2
    //     0xa4c168: tst             x16, HEAP, lsr #32
    //     0xa4c16c: b.eq            #0xa4c174
    //     0xa4c170: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4c174: b               #0xa4c17c
    // 0xa4c178: mov             x2, x1
    // 0xa4c17c: r0 = Stack()
    //     0xa4c17c: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa4c180: mov             x1, x0
    // 0xa4c184: r0 = Instance_AlignmentDirectional
    //     0xa4c184: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0xa4c188: ldr             x0, [x0, #0x7b0]
    // 0xa4c18c: stur            x1, [fp, #-0x10]
    // 0xa4c190: StoreField: r1->field_f = r0
    //     0xa4c190: stur            w0, [x1, #0xf]
    // 0xa4c194: r0 = Instance_StackFit
    //     0xa4c194: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xa4c198: ldr             x0, [x0, #0x7b8]
    // 0xa4c19c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4c19c: stur            w0, [x1, #0x17]
    // 0xa4c1a0: r0 = Instance_Clip
    //     0xa4c1a0: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa4c1a4: ldr             x0, [x0, #0x7c0]
    // 0xa4c1a8: StoreField: r1->field_1b = r0
    //     0xa4c1a8: stur            w0, [x1, #0x1b]
    // 0xa4c1ac: ldur            x0, [fp, #-0x60]
    // 0xa4c1b0: StoreField: r1->field_b = r0
    //     0xa4c1b0: stur            w0, [x1, #0xb]
    // 0xa4c1b4: r0 = Listener()
    //     0xa4c1b4: bl              #0x9daab0  ; AllocateListenerStub -> Listener (size=0x38)
    // 0xa4c1b8: ldur            x2, [fp, #-8]
    // 0xa4c1bc: r1 = Function '<anonymous closure>':.
    //     0xa4c1bc: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8f8] AnonymousClosure: (0xa4c280), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::build (0xa4bd44)
    //     0xa4c1c0: ldr             x1, [x1, #0x8f8]
    // 0xa4c1c4: stur            x0, [fp, #-8]
    // 0xa4c1c8: r0 = AllocateClosure()
    //     0xa4c1c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4c1cc: mov             x1, x0
    // 0xa4c1d0: ldur            x0, [fp, #-8]
    // 0xa4c1d4: StoreField: r0->field_f = r1
    //     0xa4c1d4: stur            w1, [x0, #0xf]
    // 0xa4c1d8: r1 = Instance_HitTestBehavior
    //     0xa4c1d8: ldr             x1, [PP, #0x6c40]  ; [pp+0x6c40] Obj!HitTestBehavior@e358a1
    // 0xa4c1dc: StoreField: r0->field_33 = r1
    //     0xa4c1dc: stur            w1, [x0, #0x33]
    // 0xa4c1e0: ldur            x1, [fp, #-0x10]
    // 0xa4c1e4: StoreField: r0->field_b = r1
    //     0xa4c1e4: stur            w1, [x0, #0xb]
    // 0xa4c1e8: LeaveFrame
    //     0xa4c1e8: mov             SP, fp
    //     0xa4c1ec: ldp             fp, lr, [SP], #0x10
    // 0xa4c1f0: ret
    //     0xa4c1f0: ret             
    // 0xa4c1f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c1f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c1f8: b               #0xa4bdc4
    // 0xa4c1fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4c1fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _cacheExtent(/* No info */) {
    // ** addr: 0xa4c218, size: 0x68
    // 0xa4c218: d2 = 2.000000
    //     0xa4c218: fmov            d2, #2.00000000
    // 0xa4c21c: d1 = 0.000000
    //     0xa4c21c: eor             v1.16b, v1.16b, v1.16b
    // 0xa4c220: LoadField: r0 = r1->field_b
    //     0xa4c220: ldur            w0, [x1, #0xb]
    // 0xa4c224: DecompressPointer r0
    //     0xa4c224: add             x0, x0, HEAP, lsl #32
    // 0xa4c228: cmp             w0, NULL
    // 0xa4c22c: b.eq            #0xa4c274
    // 0xa4c230: LoadField: d3 = r2->field_1f
    //     0xa4c230: ldur            d3, [x2, #0x1f]
    // 0xa4c234: fmul            d4, d3, d2
    // 0xa4c238: fcmp            d4, d1
    // 0xa4c23c: b.le            #0xa4c248
    // 0xa4c240: mov             v0.16b, v4.16b
    // 0xa4c244: b               #0xa4c270
    // 0xa4c248: fcmp            d1, d4
    // 0xa4c24c: b.le            #0xa4c258
    // 0xa4c250: d0 = 0.000000
    //     0xa4c250: eor             v0.16b, v0.16b, v0.16b
    // 0xa4c254: b               #0xa4c270
    // 0xa4c258: fcmp            d4, d1
    // 0xa4c25c: b.ne            #0xa4c26c
    // 0xa4c260: fadd            d2, d4, d1
    // 0xa4c264: mov             v0.16b, v2.16b
    // 0xa4c268: b               #0xa4c270
    // 0xa4c26c: mov             v0.16b, v4.16b
    // 0xa4c270: ret
    //     0xa4c270: ret             
    // 0xa4c274: EnterFrame
    //     0xa4c274: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c278: mov             fp, SP
    // 0xa4c27c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4c27c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, PointerDownEvent) {
    // ** addr: 0xa4c280, size: 0x58
    // 0xa4c280: EnterFrame
    //     0xa4c280: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c284: mov             fp, SP
    // 0xa4c288: AllocStack(0x8)
    //     0xa4c288: sub             SP, SP, #8
    // 0xa4c28c: SetupParameters()
    //     0xa4c28c: ldr             x0, [fp, #0x18]
    //     0xa4c290: ldur            w1, [x0, #0x17]
    //     0xa4c294: add             x1, x1, HEAP, lsl #32
    // 0xa4c298: CheckStackOverflow
    //     0xa4c298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c29c: cmp             SP, x16
    //     0xa4c2a0: b.ls            #0xa4c2d0
    // 0xa4c2a4: LoadField: r0 = r1->field_f
    //     0xa4c2a4: ldur            w0, [x1, #0xf]
    // 0xa4c2a8: DecompressPointer r0
    //     0xa4c2a8: add             x0, x0, HEAP, lsl #32
    // 0xa4c2ac: r16 = true
    //     0xa4c2ac: add             x16, NULL, #0x20  ; true
    // 0xa4c2b0: str             x16, [SP]
    // 0xa4c2b4: mov             x1, x0
    // 0xa4c2b8: r4 = const [0, 0x2, 0x1, 0x1, canceled, 0x1, null]
    //     0xa4c2b8: ldr             x4, [PP, #0x7008]  ; [pp+0x7008] List(7) [0, 0x2, 0x1, 0x1, "canceled", 0x1, Null]
    // 0xa4c2bc: r0 = _stopScroll()
    //     0xa4c2bc: bl              #0x8bf524  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_stopScroll
    // 0xa4c2c0: r0 = Null
    //     0xa4c2c0: mov             x0, NULL
    // 0xa4c2c4: LeaveFrame
    //     0xa4c2c4: mov             SP, fp
    //     0xa4c2c8: ldp             fp, lr, [SP], #0x10
    // 0xa4c2cc: ret
    //     0xa4c2cc: ret             
    // 0xa4c2d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c2d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c2d4: b               #0xa4c2a4
  }
  [closure] bool <anonymous closure>(dynamic, ScrollNotification) {
    // ** addr: 0xa4c2d8, size: 0x20
    // 0xa4c2d8: ldr             x1, [SP, #8]
    // 0xa4c2dc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4c2dc: ldur            w2, [x1, #0x17]
    // 0xa4c2e0: DecompressPointer r2
    //     0xa4c2e0: add             x2, x2, HEAP, lsl #32
    // 0xa4c2e4: LoadField: r1 = r2->field_f
    //     0xa4c2e4: ldur            w1, [x2, #0xf]
    // 0xa4c2e8: DecompressPointer r1
    //     0xa4c2e8: add             x1, x1, HEAP, lsl #32
    // 0xa4c2ec: LoadField: r0 = r1->field_2b
    //     0xa4c2ec: ldur            w0, [x1, #0x2b]
    // 0xa4c2f0: DecompressPointer r0
    //     0xa4c2f0: add             x0, x0, HEAP, lsl #32
    // 0xa4c2f4: ret
    //     0xa4c2f4: ret             
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83e08, size: 0xb8
    // 0xa83e08: EnterFrame
    //     0xa83e08: stp             fp, lr, [SP, #-0x10]!
    //     0xa83e0c: mov             fp, SP
    // 0xa83e10: AllocStack(0x10)
    //     0xa83e10: sub             SP, SP, #0x10
    // 0xa83e14: SetupParameters(_ScrollablePositionedListState this /* r1 => r0, fp-0x10 */)
    //     0xa83e14: mov             x0, x1
    //     0xa83e18: stur            x1, [fp, #-0x10]
    // 0xa83e1c: CheckStackOverflow
    //     0xa83e1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83e20: cmp             SP, x16
    //     0xa83e24: b.ls            #0xa83eb8
    // 0xa83e28: LoadField: r1 = r0->field_1b
    //     0xa83e28: ldur            w1, [x0, #0x1b]
    // 0xa83e2c: DecompressPointer r1
    //     0xa83e2c: add             x1, x1, HEAP, lsl #32
    // 0xa83e30: LoadField: r2 = r1->field_7
    //     0xa83e30: ldur            w2, [x1, #7]
    // 0xa83e34: DecompressPointer r2
    //     0xa83e34: add             x2, x2, HEAP, lsl #32
    // 0xa83e38: LoadField: r3 = r2->field_7
    //     0xa83e38: ldur            w3, [x2, #7]
    // 0xa83e3c: DecompressPointer r3
    //     0xa83e3c: add             x3, x3, HEAP, lsl #32
    // 0xa83e40: mov             x2, x0
    // 0xa83e44: stur            x3, [fp, #-8]
    // 0xa83e48: r1 = Function '_updatePositions@2299048475':.
    //     0xa83e48: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d940] AnonymousClosure: (0x97f908), in [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_updatePositions (0x97f940)
    //     0xa83e4c: ldr             x1, [x1, #0x940]
    // 0xa83e50: r0 = AllocateClosure()
    //     0xa83e50: bl              #0xec1630  ; AllocateClosureStub
    // 0xa83e54: ldur            x1, [fp, #-8]
    // 0xa83e58: mov             x2, x0
    // 0xa83e5c: stur            x0, [fp, #-8]
    // 0xa83e60: r0 = removeListener()
    //     0xa83e60: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xa83e64: ldur            x0, [fp, #-0x10]
    // 0xa83e68: LoadField: r1 = r0->field_1f
    //     0xa83e68: ldur            w1, [x0, #0x1f]
    // 0xa83e6c: DecompressPointer r1
    //     0xa83e6c: add             x1, x1, HEAP, lsl #32
    // 0xa83e70: LoadField: r2 = r1->field_7
    //     0xa83e70: ldur            w2, [x1, #7]
    // 0xa83e74: DecompressPointer r2
    //     0xa83e74: add             x2, x2, HEAP, lsl #32
    // 0xa83e78: LoadField: r1 = r2->field_7
    //     0xa83e78: ldur            w1, [x2, #7]
    // 0xa83e7c: DecompressPointer r1
    //     0xa83e7c: add             x1, x1, HEAP, lsl #32
    // 0xa83e80: ldur            x2, [fp, #-8]
    // 0xa83e84: r0 = removeListener()
    //     0xa83e84: bl              #0xa8a5dc  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xa83e88: ldur            x0, [fp, #-0x10]
    // 0xa83e8c: LoadField: r1 = r0->field_2f
    //     0xa83e8c: ldur            w1, [x0, #0x2f]
    // 0xa83e90: DecompressPointer r1
    //     0xa83e90: add             x1, x1, HEAP, lsl #32
    // 0xa83e94: cmp             w1, NULL
    // 0xa83e98: b.eq            #0xa83ea0
    // 0xa83e9c: r0 = dispose()
    //     0xa83e9c: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa83ea0: ldur            x1, [fp, #-0x10]
    // 0xa83ea4: r0 = dispose()
    //     0xa83ea4: bl              #0xa83ec0  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] __ScrollablePositionedListState&State&TickerProviderStateMixin::dispose
    // 0xa83ea8: r0 = Null
    //     0xa83ea8: mov             x0, NULL
    // 0xa83eac: LeaveFrame
    //     0xa83eac: mov             SP, fp
    //     0xa83eb0: ldp             fp, lr, [SP], #0x10
    // 0xa83eb4: ret
    //     0xa83eb4: ret             
    // 0xa83eb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83eb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83ebc: b               #0xa83e28
  }
  _ activate(/* No info */) {
    // ** addr: 0xa85f98, size: 0x7c
    // 0xa85f98: EnterFrame
    //     0xa85f98: stp             fp, lr, [SP, #-0x10]!
    //     0xa85f9c: mov             fp, SP
    // 0xa85fa0: AllocStack(0x8)
    //     0xa85fa0: sub             SP, SP, #8
    // 0xa85fa4: SetupParameters(_ScrollablePositionedListState this /* r1 => r0, fp-0x8 */)
    //     0xa85fa4: mov             x0, x1
    //     0xa85fa8: stur            x1, [fp, #-8]
    // 0xa85fac: CheckStackOverflow
    //     0xa85fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85fb0: cmp             SP, x16
    //     0xa85fb4: b.ls            #0xa86008
    // 0xa85fb8: mov             x1, x0
    // 0xa85fbc: r0 = activate()
    //     0xa85fbc: bl              #0xa86014  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] __ScrollablePositionedListState&State&TickerProviderStateMixin::activate
    // 0xa85fc0: ldur            x0, [fp, #-8]
    // 0xa85fc4: LoadField: r1 = r0->field_b
    //     0xa85fc4: ldur            w1, [x0, #0xb]
    // 0xa85fc8: DecompressPointer r1
    //     0xa85fc8: add             x1, x1, HEAP, lsl #32
    // 0xa85fcc: cmp             w1, NULL
    // 0xa85fd0: b.eq            #0xa86010
    // 0xa85fd4: LoadField: r2 = r1->field_1b
    //     0xa85fd4: ldur            w2, [x1, #0x1b]
    // 0xa85fd8: DecompressPointer r2
    //     0xa85fd8: add             x2, x2, HEAP, lsl #32
    // 0xa85fdc: StoreField: r2->field_7 = r0
    //     0xa85fdc: stur            w0, [x2, #7]
    //     0xa85fe0: ldurb           w16, [x2, #-1]
    //     0xa85fe4: ldurb           w17, [x0, #-1]
    //     0xa85fe8: and             x16, x17, x16, lsr #2
    //     0xa85fec: tst             x16, HEAP, lsr #32
    //     0xa85ff0: b.eq            #0xa85ff8
    //     0xa85ff4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa85ff8: r0 = Null
    //     0xa85ff8: mov             x0, NULL
    // 0xa85ffc: LeaveFrame
    //     0xa85ffc: mov             SP, fp
    //     0xa86000: ldp             fp, lr, [SP], #0x10
    // 0xa86004: ret
    //     0xa86004: ret             
    // 0xa86008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa86008: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8600c: b               #0xa85fb8
    // 0xa86010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa86010: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _ScrollablePositionedListState(/* No info */) {
    // ** addr: 0xa95040, size: 0x130
    // 0xa95040: EnterFrame
    //     0xa95040: stp             fp, lr, [SP, #-0x10]!
    //     0xa95044: mov             fp, SP
    // 0xa95048: AllocStack(0x18)
    //     0xa95048: sub             SP, SP, #0x18
    // 0xa9504c: r0 = false
    //     0xa9504c: add             x0, NULL, #0x30  ; false
    // 0xa95050: stur            x1, [fp, #-8]
    // 0xa95054: CheckStackOverflow
    //     0xa95054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa95058: cmp             SP, x16
    //     0xa9505c: b.ls            #0xa95168
    // 0xa95060: StoreField: r1->field_2b = r0
    //     0xa95060: stur            w0, [x1, #0x2b]
    // 0xa95064: StoreField: r1->field_33 = rZR
    //     0xa95064: stur            xzr, [x1, #0x33]
    // 0xa95068: r0 = _ListDisplayDetails()
    //     0xa95068: bl              #0xa9524c  ; Allocate_ListDisplayDetailsStub -> _ListDisplayDetails (size=0x24)
    // 0xa9506c: mov             x1, x0
    // 0xa95070: r2 = Instance_ValueKey
    //     0xa95070: add             x2, PP, #0x33, lsl #12  ; [pp+0x33428] Obj!ValueKey<String>@e14b81
    //     0xa95074: ldr             x2, [x2, #0x428]
    // 0xa95078: stur            x0, [fp, #-0x10]
    // 0xa9507c: r0 = _ListDisplayDetails()
    //     0xa9507c: bl              #0xa95170  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ListDisplayDetails::_ListDisplayDetails
    // 0xa95080: ldur            x0, [fp, #-0x10]
    // 0xa95084: ldur            x1, [fp, #-8]
    // 0xa95088: StoreField: r1->field_1b = r0
    //     0xa95088: stur            w0, [x1, #0x1b]
    //     0xa9508c: ldurb           w16, [x1, #-1]
    //     0xa95090: ldurb           w17, [x0, #-1]
    //     0xa95094: and             x16, x17, x16, lsr #2
    //     0xa95098: tst             x16, HEAP, lsr #32
    //     0xa9509c: b.eq            #0xa950a4
    //     0xa950a0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa950a4: r0 = _ListDisplayDetails()
    //     0xa950a4: bl              #0xa9524c  ; Allocate_ListDisplayDetailsStub -> _ListDisplayDetails (size=0x24)
    // 0xa950a8: mov             x1, x0
    // 0xa950ac: r2 = Instance_ValueKey
    //     0xa950ac: add             x2, PP, #0x33, lsl #12  ; [pp+0x33430] Obj!ValueKey<String>@e14b71
    //     0xa950b0: ldr             x2, [x2, #0x430]
    // 0xa950b4: stur            x0, [fp, #-0x10]
    // 0xa950b8: r0 = _ListDisplayDetails()
    //     0xa950b8: bl              #0xa95170  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ListDisplayDetails::_ListDisplayDetails
    // 0xa950bc: ldur            x0, [fp, #-0x10]
    // 0xa950c0: ldur            x2, [fp, #-8]
    // 0xa950c4: StoreField: r2->field_1f = r0
    //     0xa950c4: stur            w0, [x2, #0x1f]
    //     0xa950c8: ldurb           w16, [x2, #-1]
    //     0xa950cc: ldurb           w17, [x0, #-1]
    //     0xa950d0: and             x16, x17, x16, lsr #2
    //     0xa950d4: tst             x16, HEAP, lsr #32
    //     0xa950d8: b.eq            #0xa950e0
    //     0xa950dc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa950e0: r1 = <double>
    //     0xa950e0: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa950e4: r0 = ProxyAnimation()
    //     0xa950e4: bl              #0x65aa3c  ; AllocateProxyAnimationStub -> ProxyAnimation (size=0x28)
    // 0xa950e8: stur            x0, [fp, #-0x10]
    // 0xa950ec: r16 = Instance_AlwaysStoppedAnimation
    //     0xa950ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f4a0] Obj!AlwaysStoppedAnimation<double>@e25941
    //     0xa950f0: ldr             x16, [x16, #0x4a0]
    // 0xa950f4: str             x16, [SP]
    // 0xa950f8: mov             x1, x0
    // 0xa950fc: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xa950fc: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xa95100: r0 = ProxyAnimation()
    //     0xa95100: bl              #0x65a83c  ; [package:flutter/src/animation/animations.dart] ProxyAnimation::ProxyAnimation
    // 0xa95104: ldur            x0, [fp, #-0x10]
    // 0xa95108: ldur            x3, [fp, #-8]
    // 0xa9510c: StoreField: r3->field_23 = r0
    //     0xa9510c: stur            w0, [x3, #0x23]
    //     0xa95110: ldurb           w16, [x3, #-1]
    //     0xa95114: ldurb           w17, [x0, #-1]
    //     0xa95118: and             x16, x17, x16, lsr #2
    //     0xa9511c: tst             x16, HEAP, lsr #32
    //     0xa95120: b.eq            #0xa95128
    //     0xa95124: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa95128: r1 = Function '<anonymous closure>':.
    //     0xa95128: add             x1, PP, #0x33, lsl #12  ; [pp+0x33438] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa9512c: ldr             x1, [x1, #0x438]
    // 0xa95130: r2 = Null
    //     0xa95130: mov             x2, NULL
    // 0xa95134: r0 = AllocateClosure()
    //     0xa95134: bl              #0xec1630  ; AllocateClosureStub
    // 0xa95138: ldur            x1, [fp, #-8]
    // 0xa9513c: StoreField: r1->field_27 = r0
    //     0xa9513c: stur            w0, [x1, #0x27]
    //     0xa95140: ldurb           w16, [x1, #-1]
    //     0xa95144: ldurb           w17, [x0, #-1]
    //     0xa95148: and             x16, x17, x16, lsr #2
    //     0xa9514c: tst             x16, HEAP, lsr #32
    //     0xa95150: b.eq            #0xa95158
    //     0xa95154: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa95158: r0 = Null
    //     0xa95158: mov             x0, NULL
    // 0xa9515c: LeaveFrame
    //     0xa9515c: mov             SP, fp
    //     0xa95160: ldp             fp, lr, [SP], #0x10
    // 0xa95164: ret
    //     0xa95164: ret             
    // 0xa95168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa95168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9516c: b               #0xa95060
  }
}

// class id: 4695, size: 0x64, field offset: 0xc
//   const constructor, 
class ScrollablePositionedList extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94ff8, size: 0x48
    // 0xa94ff8: EnterFrame
    //     0xa94ff8: stp             fp, lr, [SP, #-0x10]!
    //     0xa94ffc: mov             fp, SP
    // 0xa95000: AllocStack(0x8)
    //     0xa95000: sub             SP, SP, #8
    // 0xa95004: CheckStackOverflow
    //     0xa95004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa95008: cmp             SP, x16
    //     0xa9500c: b.ls            #0xa95038
    // 0xa95010: r1 = <ScrollablePositionedList>
    //     0xa95010: add             x1, PP, #0x33, lsl #12  ; [pp+0x33420] TypeArguments: <ScrollablePositionedList>
    //     0xa95014: ldr             x1, [x1, #0x420]
    // 0xa95018: r0 = _ScrollablePositionedListState()
    //     0xa95018: bl              #0xa95258  ; Allocate_ScrollablePositionedListStateStub -> _ScrollablePositionedListState (size=0x3c)
    // 0xa9501c: mov             x1, x0
    // 0xa95020: stur            x0, [fp, #-8]
    // 0xa95024: r0 = _ScrollablePositionedListState()
    //     0xa95024: bl              #0xa95040  ; [package:scrollable_positioned_list/src/scrollable_positioned_list.dart] _ScrollablePositionedListState::_ScrollablePositionedListState
    // 0xa95028: ldur            x0, [fp, #-8]
    // 0xa9502c: LeaveFrame
    //     0xa9502c: mov             SP, fp
    //     0xa95030: ldp             fp, lr, [SP], #0x10
    // 0xa95034: ret
    //     0xa95034: ret             
    // 0xa95038: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa95038: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9503c: b               #0xa95010
  }
}
