// lib: , url: package:scrollable_positioned_list/src/viewport.dart

// class id: 1051115, size: 0x8
class :: {
}

// class id: 3023, size: 0xc0, field offset: 0xa8
class UnboundedRenderViewport extends RenderViewport {

  late double _maxScrollExtent; // offset: 0xb4
  late double _minScrollExtent; // offset: 0xb0

  _ performLayout(/* No info */) {
    // ** addr: 0x775cac, size: 0x514
    // 0x775cac: EnterFrame
    //     0x775cac: stp             fp, lr, [SP, #-0x10]!
    //     0x775cb0: mov             fp, SP
    // 0x775cb4: AllocStack(0x40)
    //     0x775cb4: sub             SP, SP, #0x40
    // 0x775cb8: SetupParameters(UnboundedRenderViewport this /* r1 => r0, fp-0x10 */)
    //     0x775cb8: mov             x0, x1
    //     0x775cbc: stur            x1, [fp, #-0x10]
    // 0x775cc0: CheckStackOverflow
    //     0x775cc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x775cc4: cmp             SP, x16
    //     0x775cc8: b.ls            #0x77616c
    // 0x775ccc: LoadField: r1 = r0->field_97
    //     0x775ccc: ldur            w1, [x0, #0x97]
    // 0x775cd0: DecompressPointer r1
    //     0x775cd0: add             x1, x1, HEAP, lsl #32
    // 0x775cd4: cmp             w1, NULL
    // 0x775cd8: b.ne            #0x775db4
    // 0x775cdc: r2 = 0.000000
    //     0x775cdc: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x775ce0: r1 = false
    //     0x775ce0: add             x1, NULL, #0x30  ; false
    // 0x775ce4: StoreField: r0->field_af = r2
    //     0x775ce4: stur            w2, [x0, #0xaf]
    // 0x775ce8: StoreField: r0->field_b3 = r2
    //     0x775ce8: stur            w2, [x0, #0xb3]
    // 0x775cec: StoreField: r0->field_b7 = r1
    //     0x775cec: stur            w1, [x0, #0xb7]
    // 0x775cf0: LoadField: r2 = r0->field_73
    //     0x775cf0: ldur            w2, [x0, #0x73]
    // 0x775cf4: DecompressPointer r2
    //     0x775cf4: add             x2, x2, HEAP, lsl #32
    // 0x775cf8: stur            x2, [fp, #-8]
    // 0x775cfc: r0 = LoadClassIdInstr(r2)
    //     0x775cfc: ldur            x0, [x2, #-1]
    //     0x775d00: ubfx            x0, x0, #0xc, #0x14
    // 0x775d04: cmp             x0, #0xe48
    // 0x775d08: b.ne            #0x775d84
    // 0x775d0c: mov             x1, x2
    // 0x775d10: r0 = _initialPageOffset()
    //     0x775d10: bl              #0x67a770  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::_initialPageOffset
    // 0x775d14: d1 = 0.000000
    //     0x775d14: eor             v1.16b, v1.16b, v1.16b
    // 0x775d18: fadd            d2, d0, d1
    // 0x775d1c: ldur            x1, [fp, #-8]
    // 0x775d20: stur            d2, [fp, #-0x20]
    // 0x775d24: r0 = _initialPageOffset()
    //     0x775d24: bl              #0x67a770  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::_initialPageOffset
    // 0x775d28: d1 = 0.000000
    //     0x775d28: eor             v1.16b, v1.16b, v1.16b
    // 0x775d2c: fsub            d2, d1, d0
    // 0x775d30: ldur            d0, [fp, #-0x20]
    // 0x775d34: fcmp            d0, d2
    // 0x775d38: b.le            #0x775d44
    // 0x775d3c: mov             v1.16b, v0.16b
    // 0x775d40: b               #0x775d78
    // 0x775d44: fcmp            d2, d0
    // 0x775d48: b.le            #0x775d54
    // 0x775d4c: mov             v1.16b, v2.16b
    // 0x775d50: b               #0x775d78
    // 0x775d54: fcmp            d0, d1
    // 0x775d58: b.ne            #0x775d64
    // 0x775d5c: fadd            d1, d0, d2
    // 0x775d60: b               #0x775d78
    // 0x775d64: fcmp            d2, d2
    // 0x775d68: b.vc            #0x775d74
    // 0x775d6c: mov             v1.16b, v2.16b
    // 0x775d70: b               #0x775d78
    // 0x775d74: mov             v1.16b, v0.16b
    // 0x775d78: ldur            x1, [fp, #-8]
    // 0x775d7c: r0 = applyContentDimensions()
    //     0x775d7c: bl              #0xd88880  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::applyContentDimensions
    // 0x775d80: b               #0x775da4
    // 0x775d84: mov             x1, x2
    // 0x775d88: d1 = 0.000000
    //     0x775d88: eor             v1.16b, v1.16b, v1.16b
    // 0x775d8c: r0 = LoadClassIdInstr(r1)
    //     0x775d8c: ldur            x0, [x1, #-1]
    //     0x775d90: ubfx            x0, x0, #0xc, #0x14
    // 0x775d94: mov             v0.16b, v1.16b
    // 0x775d98: r0 = GDT[cid_x0 + -0xff8]()
    //     0x775d98: sub             lr, x0, #0xff8
    //     0x775d9c: ldr             lr, [x21, lr, lsl #3]
    //     0x775da0: blr             lr
    // 0x775da4: r0 = Null
    //     0x775da4: mov             x0, NULL
    // 0x775da8: LeaveFrame
    //     0x775da8: mov             SP, fp
    //     0x775dac: ldp             fp, lr, [SP], #0x10
    // 0x775db0: ret
    //     0x775db0: ret             
    // 0x775db4: d1 = 0.000000
    //     0x775db4: eor             v1.16b, v1.16b, v1.16b
    // 0x775db8: mov             x1, x0
    // 0x775dbc: r0 = axis()
    //     0x775dbc: bl              #0x77741c  ; [package:flutter/src/rendering/viewport.dart] RenderViewportBase::axis
    // 0x775dc0: LoadField: r1 = r0->field_7
    //     0x775dc0: ldur            x1, [x0, #7]
    // 0x775dc4: cmp             x1, #0
    // 0x775dc8: b.gt            #0x775df4
    // 0x775dcc: ldur            x1, [fp, #-0x10]
    // 0x775dd0: r0 = size()
    //     0x775dd0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x775dd4: LoadField: d0 = r0->field_7
    //     0x775dd4: ldur            d0, [x0, #7]
    // 0x775dd8: ldur            x1, [fp, #-0x10]
    // 0x775ddc: stur            d0, [fp, #-0x20]
    // 0x775de0: r0 = size()
    //     0x775de0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x775de4: LoadField: d0 = r0->field_f
    //     0x775de4: ldur            d0, [x0, #0xf]
    // 0x775de8: ldur            d4, [fp, #-0x20]
    // 0x775dec: mov             v3.16b, v0.16b
    // 0x775df0: b               #0x775e18
    // 0x775df4: ldur            x1, [fp, #-0x10]
    // 0x775df8: r0 = size()
    //     0x775df8: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x775dfc: LoadField: d0 = r0->field_f
    //     0x775dfc: ldur            d0, [x0, #0xf]
    // 0x775e00: ldur            x1, [fp, #-0x10]
    // 0x775e04: stur            d0, [fp, #-0x20]
    // 0x775e08: r0 = size()
    //     0x775e08: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x775e0c: LoadField: d0 = r0->field_7
    //     0x775e0c: ldur            d0, [x0, #7]
    // 0x775e10: ldur            d4, [fp, #-0x20]
    // 0x775e14: mov             v3.16b, v0.16b
    // 0x775e18: ldur            x0, [fp, #-0x10]
    // 0x775e1c: stur            d4, [fp, #-0x20]
    // 0x775e20: stur            d3, [fp, #-0x28]
    // 0x775e24: LoadField: r1 = r0->field_97
    //     0x775e24: ldur            w1, [x0, #0x97]
    // 0x775e28: DecompressPointer r1
    //     0x775e28: add             x1, x1, HEAP, lsl #32
    // 0x775e2c: cmp             w1, NULL
    // 0x775e30: b.eq            #0x776174
    // 0x775e34: r2 = 0
    //     0x775e34: movz            x2, #0
    // 0x775e38: d5 = 0.000000
    //     0x775e38: eor             v5.16b, v5.16b, v5.16b
    // 0x775e3c: stur            x2, [fp, #-0x18]
    // 0x775e40: CheckStackOverflow
    //     0x775e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x775e44: cmp             SP, x16
    //     0x775e48: b.ls            #0x776178
    // 0x775e4c: LoadField: r1 = r0->field_73
    //     0x775e4c: ldur            w1, [x0, #0x73]
    // 0x775e50: DecompressPointer r1
    //     0x775e50: add             x1, x1, HEAP, lsl #32
    // 0x775e54: LoadField: r3 = r1->field_3f
    //     0x775e54: ldur            w3, [x1, #0x3f]
    // 0x775e58: DecompressPointer r3
    //     0x775e58: add             x3, x3, HEAP, lsl #32
    // 0x775e5c: cmp             w3, NULL
    // 0x775e60: b.eq            #0x776180
    // 0x775e64: LoadField: d0 = r3->field_7
    //     0x775e64: ldur            d0, [x3, #7]
    // 0x775e68: fadd            d2, d0, d5
    // 0x775e6c: mov             x1, x0
    // 0x775e70: mov             v0.16b, v4.16b
    // 0x775e74: mov             v1.16b, v3.16b
    // 0x775e78: r0 = _attemptLayout()
    //     0x775e78: bl              #0x7761c0  ; [package:scrollable_positioned_list/src/viewport.dart] UnboundedRenderViewport::_attemptLayout
    // 0x775e7c: mov             v1.16b, v0.16b
    // 0x775e80: d0 = 0.000000
    //     0x775e80: eor             v0.16b, v0.16b, v0.16b
    // 0x775e84: fcmp            d1, d0
    // 0x775e88: b.eq            #0x775f00
    // 0x775e8c: ldur            x2, [fp, #-0x10]
    // 0x775e90: r3 = true
    //     0x775e90: add             x3, NULL, #0x20  ; true
    // 0x775e94: LoadField: r1 = r2->field_73
    //     0x775e94: ldur            w1, [x2, #0x73]
    // 0x775e98: DecompressPointer r1
    //     0x775e98: add             x1, x1, HEAP, lsl #32
    // 0x775e9c: LoadField: r0 = r1->field_3f
    //     0x775e9c: ldur            w0, [x1, #0x3f]
    // 0x775ea0: DecompressPointer r0
    //     0x775ea0: add             x0, x0, HEAP, lsl #32
    // 0x775ea4: cmp             w0, NULL
    // 0x775ea8: b.eq            #0x776184
    // 0x775eac: LoadField: d2 = r0->field_7
    //     0x775eac: ldur            d2, [x0, #7]
    // 0x775eb0: fadd            d3, d2, d1
    // 0x775eb4: r0 = inline_Allocate_Double()
    //     0x775eb4: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0x775eb8: add             x0, x0, #0x10
    //     0x775ebc: cmp             x4, x0
    //     0x775ec0: b.ls            #0x776188
    //     0x775ec4: str             x0, [THR, #0x50]  ; THR::top
    //     0x775ec8: sub             x0, x0, #0xf
    //     0x775ecc: movz            x4, #0xe15c
    //     0x775ed0: movk            x4, #0x3, lsl #16
    //     0x775ed4: stur            x4, [x0, #-1]
    // 0x775ed8: StoreField: r0->field_7 = d3
    //     0x775ed8: stur            d3, [x0, #7]
    // 0x775edc: StoreField: r1->field_3f = r0
    //     0x775edc: stur            w0, [x1, #0x3f]
    //     0x775ee0: ldurb           w16, [x1, #-1]
    //     0x775ee4: ldurb           w17, [x0, #-1]
    //     0x775ee8: and             x16, x17, x16, lsr #2
    //     0x775eec: tst             x16, HEAP, lsr #32
    //     0x775ef0: b.eq            #0x775ef8
    //     0x775ef4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x775ef8: StoreField: r1->field_4b = r3
    //     0x775ef8: stur            w3, [x1, #0x4b]
    // 0x775efc: b               #0x77613c
    // 0x775f00: ldur            x2, [fp, #-0x10]
    // 0x775f04: ldur            d1, [fp, #-0x20]
    // 0x775f08: r3 = true
    //     0x775f08: add             x3, NULL, #0x20  ; true
    // 0x775f0c: d2 = 1.000000
    //     0x775f0c: fmov            d2, #1.00000000
    // 0x775f10: LoadField: r0 = r2->field_af
    //     0x775f10: ldur            w0, [x2, #0xaf]
    // 0x775f14: DecompressPointer r0
    //     0x775f14: add             x0, x0, HEAP, lsl #32
    // 0x775f18: r16 = Sentinel
    //     0x775f18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x775f1c: cmp             w0, w16
    // 0x775f20: b.eq            #0x7761a8
    // 0x775f24: LoadField: d3 = r2->field_a7
    //     0x775f24: ldur            d3, [x2, #0xa7]
    // 0x775f28: fmul            d4, d1, d3
    // 0x775f2c: LoadField: d5 = r0->field_7
    //     0x775f2c: ldur            d5, [x0, #7]
    // 0x775f30: fadd            d6, d5, d4
    // 0x775f34: LoadField: r0 = r2->field_b3
    //     0x775f34: ldur            w0, [x2, #0xb3]
    // 0x775f38: DecompressPointer r0
    //     0x775f38: add             x0, x0, HEAP, lsl #32
    // 0x775f3c: r16 = Sentinel
    //     0x775f3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x775f40: cmp             w0, w16
    // 0x775f44: b.eq            #0x7761b4
    // 0x775f48: fsub            d4, d2, d3
    // 0x775f4c: fmul            d3, d1, d4
    // 0x775f50: LoadField: d4 = r0->field_7
    //     0x775f50: ldur            d4, [x0, #7]
    // 0x775f54: fsub            d5, d4, d3
    // 0x775f58: fcmp            d0, d6
    // 0x775f5c: b.le            #0x775f68
    // 0x775f60: mov             v3.16b, v6.16b
    // 0x775f64: b               #0x775fc4
    // 0x775f68: fcmp            d6, d0
    // 0x775f6c: b.le            #0x775f78
    // 0x775f70: d3 = 0.000000
    //     0x775f70: eor             v3.16b, v3.16b, v3.16b
    // 0x775f74: b               #0x775fc4
    // 0x775f78: fcmp            d0, d0
    // 0x775f7c: b.ne            #0x775f90
    // 0x775f80: fadd            d3, d6, d0
    // 0x775f84: fmul            d4, d3, d0
    // 0x775f88: fmul            d3, d4, d6
    // 0x775f8c: b               #0x775fc4
    // 0x775f90: fcmp            d0, d0
    // 0x775f94: b.ne            #0x775fb0
    // 0x775f98: fcmp            d6, #0.0
    // 0x775f9c: b.vs            #0x775fb0
    // 0x775fa0: b.ne            #0x775fac
    // 0x775fa4: r0 = 0.000000
    //     0x775fa4: fmov            x0, d6
    // 0x775fa8: cmp             x0, #0
    // 0x775fac: b.lt            #0x775fb8
    // 0x775fb0: fcmp            d6, d6
    // 0x775fb4: b.vc            #0x775fc0
    // 0x775fb8: mov             v3.16b, v6.16b
    // 0x775fbc: b               #0x775fc4
    // 0x775fc0: d3 = 0.000000
    //     0x775fc0: eor             v3.16b, v3.16b, v3.16b
    // 0x775fc4: fcmp            d3, d5
    // 0x775fc8: b.gt            #0x775ffc
    // 0x775fcc: fcmp            d5, d3
    // 0x775fd0: b.le            #0x775fdc
    // 0x775fd4: mov             v3.16b, v5.16b
    // 0x775fd8: b               #0x775ffc
    // 0x775fdc: fcmp            d3, d0
    // 0x775fe0: b.ne            #0x775ff0
    // 0x775fe4: fadd            d4, d3, d5
    // 0x775fe8: mov             v3.16b, v4.16b
    // 0x775fec: b               #0x775ffc
    // 0x775ff0: fcmp            d5, d5
    // 0x775ff4: b.vc            #0x775ffc
    // 0x775ff8: mov             v3.16b, v5.16b
    // 0x775ffc: stur            d3, [fp, #-0x38]
    // 0x776000: fcmp            d6, d3
    // 0x776004: b.le            #0x776010
    // 0x776008: mov             v4.16b, v3.16b
    // 0x77600c: b               #0x77606c
    // 0x776010: fcmp            d3, d6
    // 0x776014: b.le            #0x776020
    // 0x776018: mov             v4.16b, v6.16b
    // 0x77601c: b               #0x77606c
    // 0x776020: fcmp            d6, d0
    // 0x776024: b.ne            #0x776038
    // 0x776028: fadd            d4, d6, d3
    // 0x77602c: fmul            d5, d4, d6
    // 0x776030: fmul            d4, d5, d3
    // 0x776034: b               #0x77606c
    // 0x776038: fcmp            d6, d0
    // 0x77603c: b.ne            #0x776058
    // 0x776040: fcmp            d3, #0.0
    // 0x776044: b.vs            #0x776058
    // 0x776048: b.ne            #0x776054
    // 0x77604c: r0 = 0.000000
    //     0x77604c: fmov            x0, d3
    // 0x776050: cmp             x0, #0
    // 0x776054: b.lt            #0x776060
    // 0x776058: fcmp            d3, d3
    // 0x77605c: b.vc            #0x776068
    // 0x776060: mov             v4.16b, v3.16b
    // 0x776064: b               #0x77606c
    // 0x776068: mov             v4.16b, v6.16b
    // 0x77606c: stur            d4, [fp, #-0x30]
    // 0x776070: LoadField: r0 = r2->field_73
    //     0x776070: ldur            w0, [x2, #0x73]
    // 0x776074: DecompressPointer r0
    //     0x776074: add             x0, x0, HEAP, lsl #32
    // 0x776078: stur            x0, [fp, #-8]
    // 0x77607c: r1 = LoadClassIdInstr(r0)
    //     0x77607c: ldur            x1, [x0, #-1]
    //     0x776080: ubfx            x1, x1, #0xc, #0x14
    // 0x776084: cmp             x1, #0xe48
    // 0x776088: b.ne            #0x776118
    // 0x77608c: mov             x1, x0
    // 0x776090: r0 = _initialPageOffset()
    //     0x776090: bl              #0x67a770  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::_initialPageOffset
    // 0x776094: mov             v1.16b, v0.16b
    // 0x776098: ldur            d0, [fp, #-0x30]
    // 0x77609c: fadd            d2, d0, d1
    // 0x7760a0: ldur            x1, [fp, #-8]
    // 0x7760a4: stur            d2, [fp, #-0x40]
    // 0x7760a8: r0 = _initialPageOffset()
    //     0x7760a8: bl              #0x67a770  ; [package:flutter/src/widgets/page_view.dart] _PagePosition::_initialPageOffset
    // 0x7760ac: ldur            d1, [fp, #-0x38]
    // 0x7760b0: fsub            d2, d1, d0
    // 0x7760b4: ldur            d0, [fp, #-0x40]
    // 0x7760b8: fcmp            d0, d2
    // 0x7760bc: b.le            #0x7760cc
    // 0x7760c0: mov             v1.16b, v0.16b
    // 0x7760c4: d3 = 0.000000
    //     0x7760c4: eor             v3.16b, v3.16b, v3.16b
    // 0x7760c8: b               #0x776108
    // 0x7760cc: fcmp            d2, d0
    // 0x7760d0: b.le            #0x7760e0
    // 0x7760d4: mov             v1.16b, v2.16b
    // 0x7760d8: d3 = 0.000000
    //     0x7760d8: eor             v3.16b, v3.16b, v3.16b
    // 0x7760dc: b               #0x776108
    // 0x7760e0: d3 = 0.000000
    //     0x7760e0: eor             v3.16b, v3.16b, v3.16b
    // 0x7760e4: fcmp            d0, d3
    // 0x7760e8: b.ne            #0x7760f4
    // 0x7760ec: fadd            d1, d0, d2
    // 0x7760f0: b               #0x776108
    // 0x7760f4: fcmp            d2, d2
    // 0x7760f8: b.vc            #0x776104
    // 0x7760fc: mov             v1.16b, v2.16b
    // 0x776100: b               #0x776108
    // 0x776104: mov             v1.16b, v0.16b
    // 0x776108: ldur            x1, [fp, #-8]
    // 0x77610c: r0 = applyContentDimensions()
    //     0x77610c: bl              #0xd88880  ; [package:flutter/src/widgets/scroll_position.dart] ScrollPosition::applyContentDimensions
    // 0x776110: tbnz            w0, #4, #0x77613c
    // 0x776114: b               #0x77615c
    // 0x776118: mov             v1.16b, v3.16b
    // 0x77611c: mov             v0.16b, v4.16b
    // 0x776120: mov             x1, x0
    // 0x776124: r0 = LoadClassIdInstr(r1)
    //     0x776124: ldur            x0, [x1, #-1]
    //     0x776128: ubfx            x0, x0, #0xc, #0x14
    // 0x77612c: r0 = GDT[cid_x0 + -0xff8]()
    //     0x77612c: sub             lr, x0, #0xff8
    //     0x776130: ldr             lr, [x21, lr, lsl #3]
    //     0x776134: blr             lr
    // 0x776138: tbz             w0, #4, #0x77615c
    // 0x77613c: ldur            x1, [fp, #-0x18]
    // 0x776140: add             x2, x1, #1
    // 0x776144: cmp             x2, #0xa
    // 0x776148: b.ge            #0x77615c
    // 0x77614c: ldur            x0, [fp, #-0x10]
    // 0x776150: ldur            d4, [fp, #-0x20]
    // 0x776154: ldur            d3, [fp, #-0x28]
    // 0x776158: b               #0x775e38
    // 0x77615c: r0 = Null
    //     0x77615c: mov             x0, NULL
    // 0x776160: LeaveFrame
    //     0x776160: mov             SP, fp
    //     0x776164: ldp             fp, lr, [SP], #0x10
    // 0x776168: ret
    //     0x776168: ret             
    // 0x77616c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77616c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x776170: b               #0x775ccc
    // 0x776174: r0 = NullCastErrorSharedWithFPURegs()
    //     0x776174: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x776178: r0 = StackOverflowSharedWithFPURegs()
    //     0x776178: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x77617c: b               #0x775e4c
    // 0x776180: r0 = NullCastErrorSharedWithFPURegs()
    //     0x776180: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x776184: r0 = NullCastErrorSharedWithFPURegs()
    //     0x776184: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x776188: stp             q0, q3, [SP, #-0x20]!
    // 0x77618c: stp             x2, x3, [SP, #-0x10]!
    // 0x776190: SaveReg r1
    //     0x776190: str             x1, [SP, #-8]!
    // 0x776194: r0 = AllocateDouble()
    //     0x776194: bl              #0xec2254  ; AllocateDoubleStub
    // 0x776198: RestoreReg r1
    //     0x776198: ldr             x1, [SP], #8
    // 0x77619c: ldp             x2, x3, [SP], #0x10
    // 0x7761a0: ldp             q0, q3, [SP], #0x20
    // 0x7761a4: b               #0x775ed8
    // 0x7761a8: r9 = _minScrollExtent
    //     0x7761a8: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d348] Field <UnboundedRenderViewport._minScrollExtent@2694181789>: late (offset: 0xb0)
    //     0x7761ac: ldr             x9, [x9, #0x348]
    // 0x7761b0: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x7761b0: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
    // 0x7761b4: r9 = _maxScrollExtent
    //     0x7761b4: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d340] Field <UnboundedRenderViewport._maxScrollExtent@2694181789>: late (offset: 0xb4)
    //     0x7761b8: ldr             x9, [x9, #0x340]
    // 0x7761bc: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0x7761bc: bl              #0xec2c84  ; LateInitializationErrorSharedWithFPURegsStub
  }
  _ _attemptLayout(/* No info */) {
    // ** addr: 0x7761c0, size: 0x784
    // 0x7761c0: EnterFrame
    //     0x7761c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7761c4: mov             fp, SP
    // 0x7761c8: AllocStack(0x90)
    //     0x7761c8: sub             SP, SP, #0x90
    // 0x7761cc: r4 = 0.000000
    //     0x7761cc: ldr             x4, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x7761d0: r0 = false
    //     0x7761d0: add             x0, NULL, #0x30  ; false
    // 0x7761d4: mov             x5, x1
    // 0x7761d8: stur            x1, [fp, #-0x18]
    // 0x7761dc: stur            d0, [fp, #-0x50]
    // 0x7761e0: stur            d1, [fp, #-0x58]
    // 0x7761e4: CheckStackOverflow
    //     0x7761e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7761e8: cmp             SP, x16
    //     0x7761ec: b.ls            #0x7767b8
    // 0x7761f0: StoreField: r5->field_af = r4
    //     0x7761f0: stur            w4, [x5, #0xaf]
    // 0x7761f4: StoreField: r5->field_b3 = r4
    //     0x7761f4: stur            w4, [x5, #0xb3]
    // 0x7761f8: StoreField: r5->field_b7 = r0
    //     0x7761f8: stur            w0, [x5, #0xb7]
    // 0x7761fc: LoadField: d3 = r5->field_a7
    //     0x7761fc: ldur            d3, [x5, #0xa7]
    // 0x776200: fmul            d4, d0, d3
    // 0x776204: fsub            d3, d4, d2
    // 0x776208: stur            d3, [fp, #-0x48]
    // 0x77620c: r0 = inline_Allocate_Double()
    //     0x77620c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x776210: add             x0, x0, #0x10
    //     0x776214: cmp             x1, x0
    //     0x776218: b.ls            #0x7767c0
    //     0x77621c: str             x0, [THR, #0x50]  ; THR::top
    //     0x776220: sub             x0, x0, #0xf
    //     0x776224: movz            x1, #0xe15c
    //     0x776228: movk            x1, #0x3, lsl #16
    //     0x77622c: stur            x1, [x0, #-1]
    // 0x776230: StoreField: r0->field_7 = d0
    //     0x776230: stur            d0, [x0, #7]
    // 0x776234: stur            x0, [fp, #-0x10]
    // 0x776238: r6 = inline_Allocate_Double()
    //     0x776238: ldp             x6, x1, [THR, #0x50]  ; THR::top
    //     0x77623c: add             x6, x6, #0x10
    //     0x776240: cmp             x1, x6
    //     0x776244: b.ls            #0x7767e0
    //     0x776248: str             x6, [THR, #0x50]  ; THR::top
    //     0x77624c: sub             x6, x6, #0xf
    //     0x776250: movz            x1, #0xe15c
    //     0x776254: movk            x1, #0x3, lsl #16
    //     0x776258: stur            x1, [x6, #-1]
    // 0x77625c: StoreField: r6->field_7 = d3
    //     0x77625c: stur            d3, [x6, #7]
    // 0x776260: mov             x1, x6
    // 0x776264: mov             x2, x4
    // 0x776268: mov             x3, x0
    // 0x77626c: stur            x6, [fp, #-8]
    // 0x776270: r0 = clamp()
    //     0x776270: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0x776274: ldur            d0, [fp, #-0x50]
    // 0x776278: ldur            d1, [fp, #-0x48]
    // 0x77627c: stur            x0, [fp, #-0x28]
    // 0x776280: fsub            d2, d0, d1
    // 0x776284: r4 = inline_Allocate_Double()
    //     0x776284: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0x776288: add             x4, x4, #0x10
    //     0x77628c: cmp             x1, x4
    //     0x776290: b.ls            #0x77680c
    //     0x776294: str             x4, [THR, #0x50]  ; THR::top
    //     0x776298: sub             x4, x4, #0xf
    //     0x77629c: movz            x1, #0xe15c
    //     0x7762a0: movk            x1, #0x3, lsl #16
    //     0x7762a4: stur            x1, [x4, #-1]
    // 0x7762a8: StoreField: r4->field_7 = d2
    //     0x7762a8: stur            d2, [x4, #7]
    // 0x7762ac: mov             x1, x4
    // 0x7762b0: ldur            x3, [fp, #-0x10]
    // 0x7762b4: stur            x4, [fp, #-0x20]
    // 0x7762b8: r2 = 0.000000
    //     0x7762b8: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x7762bc: r0 = clamp()
    //     0x7762bc: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0x7762c0: mov             x5, x0
    // 0x7762c4: ldur            x4, [fp, #-0x18]
    // 0x7762c8: stur            x5, [fp, #-0x30]
    // 0x7762cc: LoadField: r0 = r4->field_83
    //     0x7762cc: ldur            w0, [x4, #0x83]
    // 0x7762d0: DecompressPointer r0
    //     0x7762d0: add             x0, x0, HEAP, lsl #32
    // 0x7762d4: LoadField: r1 = r0->field_7
    //     0x7762d4: ldur            x1, [x0, #7]
    // 0x7762d8: cmp             x1, #0
    // 0x7762dc: b.gt            #0x776334
    // 0x7762e0: LoadField: d0 = r4->field_77
    //     0x7762e0: ldur            d0, [x4, #0x77]
    // 0x7762e4: r0 = inline_Allocate_Double()
    //     0x7762e4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7762e8: add             x0, x0, #0x10
    //     0x7762ec: cmp             x1, x0
    //     0x7762f0: b.ls            #0x776830
    //     0x7762f4: str             x0, [THR, #0x50]  ; THR::top
    //     0x7762f8: sub             x0, x0, #0xf
    //     0x7762fc: movz            x1, #0xe15c
    //     0x776300: movk            x1, #0x3, lsl #16
    //     0x776304: stur            x1, [x0, #-1]
    // 0x776308: StoreField: r0->field_7 = d0
    //     0x776308: stur            d0, [x0, #7]
    // 0x77630c: StoreField: r4->field_bb = r0
    //     0x77630c: stur            w0, [x4, #0xbb]
    //     0x776310: ldurb           w16, [x4, #-1]
    //     0x776314: ldurb           w17, [x0, #-1]
    //     0x776318: and             x16, x17, x16, lsr #2
    //     0x77631c: tst             x16, HEAP, lsr #32
    //     0x776320: b.eq            #0x776328
    //     0x776324: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x776328: mov             v3.16b, v0.16b
    // 0x77632c: ldur            d0, [fp, #-0x50]
    // 0x776330: b               #0x776388
    // 0x776334: ldur            d0, [fp, #-0x50]
    // 0x776338: LoadField: d1 = r4->field_77
    //     0x776338: ldur            d1, [x4, #0x77]
    // 0x77633c: fmul            d2, d0, d1
    // 0x776340: r0 = inline_Allocate_Double()
    //     0x776340: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x776344: add             x0, x0, #0x10
    //     0x776348: cmp             x1, x0
    //     0x77634c: b.ls            #0x776848
    //     0x776350: str             x0, [THR, #0x50]  ; THR::top
    //     0x776354: sub             x0, x0, #0xf
    //     0x776358: movz            x1, #0xe15c
    //     0x77635c: movk            x1, #0x3, lsl #16
    //     0x776360: stur            x1, [x0, #-1]
    // 0x776364: StoreField: r0->field_7 = d2
    //     0x776364: stur            d2, [x0, #7]
    // 0x776368: StoreField: r4->field_bb = r0
    //     0x776368: stur            w0, [x4, #0xbb]
    //     0x77636c: ldurb           w16, [x4, #-1]
    //     0x776370: ldurb           w17, [x0, #-1]
    //     0x776374: and             x16, x17, x16, lsr #2
    //     0x776378: tst             x16, HEAP, lsr #32
    //     0x77637c: b.eq            #0x776384
    //     0x776380: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x776384: mov             v3.16b, v2.16b
    // 0x776388: ldur            d1, [fp, #-0x48]
    // 0x77638c: d2 = 2.000000
    //     0x77638c: fmov            d2, #2.00000000
    // 0x776390: fmul            d4, d3, d2
    // 0x776394: fadd            d2, d0, d4
    // 0x776398: stur            d2, [fp, #-0x68]
    // 0x77639c: fadd            d4, d1, d3
    // 0x7763a0: stur            d4, [fp, #-0x60]
    // 0x7763a4: r0 = inline_Allocate_Double()
    //     0x7763a4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7763a8: add             x0, x0, #0x10
    //     0x7763ac: cmp             x1, x0
    //     0x7763b0: b.ls            #0x776860
    //     0x7763b4: str             x0, [THR, #0x50]  ; THR::top
    //     0x7763b8: sub             x0, x0, #0xf
    //     0x7763bc: movz            x1, #0xe15c
    //     0x7763c0: movk            x1, #0x3, lsl #16
    //     0x7763c4: stur            x1, [x0, #-1]
    // 0x7763c8: StoreField: r0->field_7 = d2
    //     0x7763c8: stur            d2, [x0, #7]
    // 0x7763cc: stur            x0, [fp, #-0x10]
    // 0x7763d0: r1 = inline_Allocate_Double()
    //     0x7763d0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x7763d4: add             x1, x1, #0x10
    //     0x7763d8: cmp             x2, x1
    //     0x7763dc: b.ls            #0x776880
    //     0x7763e0: str             x1, [THR, #0x50]  ; THR::top
    //     0x7763e4: sub             x1, x1, #0xf
    //     0x7763e8: movz            x2, #0xe15c
    //     0x7763ec: movk            x2, #0x3, lsl #16
    //     0x7763f0: stur            x2, [x1, #-1]
    // 0x7763f4: StoreField: r1->field_7 = d4
    //     0x7763f4: stur            d4, [x1, #7]
    // 0x7763f8: mov             x3, x0
    // 0x7763fc: r2 = 0.000000
    //     0x7763fc: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x776400: r0 = clamp()
    //     0x776400: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0x776404: ldur            d0, [fp, #-0x68]
    // 0x776408: ldur            d1, [fp, #-0x60]
    // 0x77640c: stur            x0, [fp, #-0x38]
    // 0x776410: fsub            d2, d0, d1
    // 0x776414: r1 = inline_Allocate_Double()
    //     0x776414: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x776418: add             x1, x1, #0x10
    //     0x77641c: cmp             x2, x1
    //     0x776420: b.ls            #0x7768ac
    //     0x776424: str             x1, [THR, #0x50]  ; THR::top
    //     0x776428: sub             x1, x1, #0xf
    //     0x77642c: movz            x2, #0xe15c
    //     0x776430: movk            x2, #0x3, lsl #16
    //     0x776434: stur            x2, [x1, #-1]
    // 0x776438: StoreField: r1->field_7 = d2
    //     0x776438: stur            d2, [x1, #7]
    // 0x77643c: ldur            x3, [fp, #-0x10]
    // 0x776440: r2 = 0.000000
    //     0x776440: ldr             x2, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x776444: r0 = clamp()
    //     0x776444: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0x776448: mov             x3, x0
    // 0x77644c: ldur            x0, [fp, #-0x18]
    // 0x776450: stur            x3, [fp, #-0x10]
    // 0x776454: LoadField: r2 = r0->field_97
    //     0x776454: ldur            w2, [x0, #0x97]
    // 0x776458: DecompressPointer r2
    //     0x776458: add             x2, x2, HEAP, lsl #32
    // 0x77645c: cmp             w2, NULL
    // 0x776460: b.eq            #0x7768c8
    // 0x776464: mov             x1, x0
    // 0x776468: r0 = childBefore()
    //     0x776468: bl              #0x7771d4  ; [package:flutter/src/rendering/viewport.dart] _RenderViewportBase&RenderBox&ContainerRenderObjectMixin::childBefore
    // 0x77646c: stur            x0, [fp, #-0x40]
    // 0x776470: cmp             w0, NULL
    // 0x776474: b.eq            #0x7765d4
    // 0x776478: ldur            d0, [fp, #-0x50]
    // 0x77647c: ldur            d1, [fp, #-0x48]
    // 0x776480: fcmp            d0, d1
    // 0x776484: b.le            #0x776494
    // 0x776488: mov             v2.16b, v0.16b
    // 0x77648c: d4 = 0.000000
    //     0x77648c: eor             v4.16b, v4.16b, v4.16b
    // 0x776490: b               #0x7764d0
    // 0x776494: fcmp            d1, d0
    // 0x776498: b.le            #0x7764a8
    // 0x77649c: mov             v2.16b, v1.16b
    // 0x7764a0: d4 = 0.000000
    //     0x7764a0: eor             v4.16b, v4.16b, v4.16b
    // 0x7764a4: b               #0x7764d0
    // 0x7764a8: d4 = 0.000000
    //     0x7764a8: eor             v4.16b, v4.16b, v4.16b
    // 0x7764ac: fcmp            d0, d4
    // 0x7764b0: b.ne            #0x7764bc
    // 0x7764b4: fadd            d2, d0, d1
    // 0x7764b8: b               #0x7764d0
    // 0x7764bc: fcmp            d1, d1
    // 0x7764c0: b.vc            #0x7764cc
    // 0x7764c4: mov             v2.16b, v1.16b
    // 0x7764c8: b               #0x7764d0
    // 0x7764cc: mov             v2.16b, v0.16b
    // 0x7764d0: ldur            x4, [fp, #-0x18]
    // 0x7764d4: ldur            x7, [fp, #-0x28]
    // 0x7764d8: ldur            x6, [fp, #-0x30]
    // 0x7764dc: ldur            x5, [fp, #-0x38]
    // 0x7764e0: fsub            d3, d2, d0
    // 0x7764e4: stur            d3, [fp, #-0x60]
    // 0x7764e8: LoadField: r1 = r4->field_bb
    //     0x7764e8: ldur            w1, [x4, #0xbb]
    // 0x7764ec: DecompressPointer r1
    //     0x7764ec: add             x1, x1, HEAP, lsl #32
    // 0x7764f0: cmp             w1, NULL
    // 0x7764f4: b.eq            #0x7768cc
    // 0x7764f8: LoadField: d2 = r1->field_7
    //     0x7764f8: ldur            d2, [x1, #7]
    // 0x7764fc: fneg            d5, d2
    // 0x776500: r2 = inline_Allocate_Double()
    //     0x776500: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0x776504: add             x2, x2, #0x10
    //     0x776508: cmp             x1, x2
    //     0x77650c: b.ls            #0x7768d0
    //     0x776510: str             x2, [THR, #0x50]  ; THR::top
    //     0x776514: sub             x2, x2, #0xf
    //     0x776518: movz            x1, #0xe15c
    //     0x77651c: movk            x1, #0x3, lsl #16
    //     0x776520: stur            x1, [x2, #-1]
    // 0x776524: StoreField: r2->field_7 = d5
    //     0x776524: stur            d5, [x2, #7]
    // 0x776528: ldur            x1, [fp, #-0x20]
    // 0x77652c: r3 = 0.000000
    //     0x77652c: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x776530: r0 = clamp()
    //     0x776530: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0x776534: mov             x1, x0
    // 0x776538: ldur            x0, [fp, #-0x28]
    // 0x77653c: LoadField: d0 = r0->field_7
    //     0x77653c: ldur            d0, [x0, #7]
    // 0x776540: ldur            x3, [fp, #-0x30]
    // 0x776544: stur            d0, [fp, #-0x80]
    // 0x776548: LoadField: d2 = r3->field_7
    //     0x776548: ldur            d2, [x3, #7]
    // 0x77654c: ldur            x2, [fp, #-0x38]
    // 0x776550: stur            d2, [fp, #-0x78]
    // 0x776554: LoadField: d5 = r2->field_7
    //     0x776554: ldur            d5, [x2, #7]
    // 0x776558: stur            d5, [fp, #-0x70]
    // 0x77655c: LoadField: d1 = r1->field_7
    //     0x77655c: ldur            d1, [x1, #7]
    // 0x776560: ldur            x2, [fp, #-0x18]
    // 0x776564: stur            d1, [fp, #-0x68]
    // 0x776568: r1 = Function 'childBefore':.
    //     0x776568: add             x1, PP, #0x55, lsl #12  ; [pp+0x55398] AnonymousClosure: (0x7773e0), in [package:flutter/src/rendering/viewport.dart] _RenderViewportBase&RenderBox&ContainerRenderObjectMixin::childBefore (0x7771d4)
    //     0x77656c: ldr             x1, [x1, #0x398]
    // 0x776570: r0 = AllocateClosure()
    //     0x776570: bl              #0xec1630  ; AllocateClosureStub
    // 0x776574: ldur            d0, [fp, #-0x80]
    // 0x776578: str             d0, [SP, #8]
    // 0x77657c: ldur            d0, [fp, #-0x60]
    // 0x776580: str             d0, [SP]
    // 0x776584: ldur            x1, [fp, #-0x18]
    // 0x776588: mov             x2, x0
    // 0x77658c: ldur            d0, [fp, #-0x68]
    // 0x776590: ldur            x3, [fp, #-0x40]
    // 0x776594: ldur            d1, [fp, #-0x58]
    // 0x776598: ldur            d2, [fp, #-0x78]
    // 0x77659c: ldur            d3, [fp, #-0x50]
    // 0x7765a0: ldur            d5, [fp, #-0x70]
    // 0x7765a4: r5 = Instance_GrowthDirection
    //     0x7765a4: add             x5, PP, #0x55, lsl #12  ; [pp+0x553a0] Obj!GrowthDirection@e354c1
    //     0x7765a8: ldr             x5, [x5, #0x3a0]
    // 0x7765ac: d4 = 0.000000
    //     0x7765ac: eor             v4.16b, v4.16b, v4.16b
    // 0x7765b0: r0 = layoutChildSequence()
    //     0x7765b0: bl              #0x776944  ; [package:flutter/src/rendering/viewport.dart] RenderViewportBase::layoutChildSequence
    // 0x7765b4: mov             v1.16b, v0.16b
    // 0x7765b8: d0 = 0.000000
    //     0x7765b8: eor             v0.16b, v0.16b, v0.16b
    // 0x7765bc: fcmp            d1, d0
    // 0x7765c0: b.eq            #0x7765d8
    // 0x7765c4: fneg            d0, d1
    // 0x7765c8: LeaveFrame
    //     0x7765c8: mov             SP, fp
    //     0x7765cc: ldp             fp, lr, [SP], #0x10
    // 0x7765d0: ret
    //     0x7765d0: ret             
    // 0x7765d4: d0 = 0.000000
    //     0x7765d4: eor             v0.16b, v0.16b, v0.16b
    // 0x7765d8: ldur            x0, [fp, #-0x18]
    // 0x7765dc: ldur            d1, [fp, #-0x48]
    // 0x7765e0: LoadField: r4 = r0->field_97
    //     0x7765e0: ldur            w4, [x0, #0x97]
    // 0x7765e4: DecompressPointer r4
    //     0x7765e4: add             x4, x4, HEAP, lsl #32
    // 0x7765e8: stur            x4, [fp, #-0x20]
    // 0x7765ec: fneg            d2, d1
    // 0x7765f0: fcmp            d0, d2
    // 0x7765f4: b.le            #0x776600
    // 0x7765f8: d3 = 0.000000
    //     0x7765f8: eor             v3.16b, v3.16b, v3.16b
    // 0x7765fc: b               #0x776634
    // 0x776600: fcmp            d2, d0
    // 0x776604: b.le            #0x776610
    // 0x776608: mov             v3.16b, v2.16b
    // 0x77660c: b               #0x776634
    // 0x776610: fcmp            d0, d0
    // 0x776614: b.ne            #0x776620
    // 0x776618: fadd            d3, d2, d0
    // 0x77661c: b               #0x776634
    // 0x776620: fcmp            d2, d2
    // 0x776624: b.vc            #0x776630
    // 0x776628: mov             v3.16b, v2.16b
    // 0x77662c: b               #0x776634
    // 0x776630: d3 = 0.000000
    //     0x776630: eor             v3.16b, v3.16b, v3.16b
    // 0x776634: ldur            x1, [fp, #-0x40]
    // 0x776638: stur            d3, [fp, #-0x68]
    // 0x77663c: cmp             w1, NULL
    // 0x776640: b.ne            #0x7766b8
    // 0x776644: fcmp            d0, d2
    // 0x776648: b.le            #0x776654
    // 0x77664c: mov             v0.16b, v2.16b
    // 0x776650: b               #0x7766b0
    // 0x776654: fcmp            d2, d0
    // 0x776658: b.le            #0x776664
    // 0x77665c: d0 = 0.000000
    //     0x77665c: eor             v0.16b, v0.16b, v0.16b
    // 0x776660: b               #0x7766b0
    // 0x776664: fcmp            d0, d0
    // 0x776668: b.ne            #0x77667c
    // 0x77666c: fadd            d4, d2, d0
    // 0x776670: fmul            d5, d4, d0
    // 0x776674: fmul            d0, d5, d2
    // 0x776678: b               #0x7766b0
    // 0x77667c: fcmp            d0, d0
    // 0x776680: b.ne            #0x77669c
    // 0x776684: fcmp            d2, #0.0
    // 0x776688: b.vs            #0x77669c
    // 0x77668c: b.ne            #0x776698
    // 0x776690: r1 = 0.000000
    //     0x776690: fmov            x1, d2
    // 0x776694: cmp             x1, #0
    // 0x776698: b.lt            #0x7766a4
    // 0x77669c: fcmp            d2, d2
    // 0x7766a0: b.vc            #0x7766ac
    // 0x7766a4: mov             v0.16b, v2.16b
    // 0x7766a8: b               #0x7766b0
    // 0x7766ac: d0 = 0.000000
    //     0x7766ac: eor             v0.16b, v0.16b, v0.16b
    // 0x7766b0: mov             v4.16b, v0.16b
    // 0x7766b4: b               #0x7766bc
    // 0x7766b8: d4 = 0.000000
    //     0x7766b8: eor             v4.16b, v4.16b, v4.16b
    // 0x7766bc: ldur            d0, [fp, #-0x50]
    // 0x7766c0: stur            d4, [fp, #-0x60]
    // 0x7766c4: fcmp            d1, d0
    // 0x7766c8: b.lt            #0x7766d4
    // 0x7766cc: mov             v2.16b, v1.16b
    // 0x7766d0: b               #0x7766e0
    // 0x7766d4: ldur            x1, [fp, #-0x28]
    // 0x7766d8: LoadField: d1 = r1->field_7
    //     0x7766d8: ldur            d1, [x1, #7]
    // 0x7766dc: mov             v2.16b, v1.16b
    // 0x7766e0: ldur            x5, [fp, #-0x30]
    // 0x7766e4: ldur            x6, [fp, #-0x10]
    // 0x7766e8: stur            d2, [fp, #-0x48]
    // 0x7766ec: LoadField: r1 = r0->field_bb
    //     0x7766ec: ldur            w1, [x0, #0xbb]
    // 0x7766f0: DecompressPointer r1
    //     0x7766f0: add             x1, x1, HEAP, lsl #32
    // 0x7766f4: cmp             w1, NULL
    // 0x7766f8: b.eq            #0x77690c
    // 0x7766fc: LoadField: d1 = r1->field_7
    //     0x7766fc: ldur            d1, [x1, #7]
    // 0x776700: fneg            d5, d1
    // 0x776704: r2 = inline_Allocate_Double()
    //     0x776704: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0x776708: add             x2, x2, #0x10
    //     0x77670c: cmp             x1, x2
    //     0x776710: b.ls            #0x776910
    //     0x776714: str             x2, [THR, #0x50]  ; THR::top
    //     0x776718: sub             x2, x2, #0xf
    //     0x77671c: movz            x1, #0xe15c
    //     0x776720: movk            x1, #0x3, lsl #16
    //     0x776724: stur            x1, [x2, #-1]
    // 0x776728: StoreField: r2->field_7 = d5
    //     0x776728: stur            d5, [x2, #7]
    // 0x77672c: ldur            x1, [fp, #-8]
    // 0x776730: r3 = 0.000000
    //     0x776730: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x776734: r0 = clamp()
    //     0x776734: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0x776738: mov             x1, x0
    // 0x77673c: ldur            x0, [fp, #-0x30]
    // 0x776740: LoadField: d0 = r0->field_7
    //     0x776740: ldur            d0, [x0, #7]
    // 0x776744: ldur            x0, [fp, #-0x10]
    // 0x776748: stur            d0, [fp, #-0x80]
    // 0x77674c: LoadField: d5 = r0->field_7
    //     0x77674c: ldur            d5, [x0, #7]
    // 0x776750: stur            d5, [fp, #-0x78]
    // 0x776754: LoadField: d1 = r1->field_7
    //     0x776754: ldur            d1, [x1, #7]
    // 0x776758: ldur            x2, [fp, #-0x18]
    // 0x77675c: stur            d1, [fp, #-0x70]
    // 0x776760: r1 = Function 'childAfter':.
    //     0x776760: add             x1, PP, #0x55, lsl #12  ; [pp+0x55338] AnonymousClosure: (0x7772bc), in [package:flutter/src/rendering/viewport.dart] _RenderViewportBase&RenderBox&ContainerRenderObjectMixin::childAfter (0x7772f8)
    //     0x776764: ldr             x1, [x1, #0x338]
    // 0x776768: r0 = AllocateClosure()
    //     0x776768: bl              #0xec1630  ; AllocateClosureStub
    // 0x77676c: ldur            d0, [fp, #-0x80]
    // 0x776770: str             d0, [SP, #8]
    // 0x776774: ldur            d0, [fp, #-0x68]
    // 0x776778: str             d0, [SP]
    // 0x77677c: ldur            x1, [fp, #-0x18]
    // 0x776780: mov             x2, x0
    // 0x776784: ldur            d0, [fp, #-0x70]
    // 0x776788: ldur            x3, [fp, #-0x20]
    // 0x77678c: ldur            d1, [fp, #-0x58]
    // 0x776790: ldur            d2, [fp, #-0x48]
    // 0x776794: ldur            d3, [fp, #-0x50]
    // 0x776798: ldur            d4, [fp, #-0x60]
    // 0x77679c: ldur            d5, [fp, #-0x78]
    // 0x7767a0: r5 = Instance_GrowthDirection
    //     0x7767a0: add             x5, PP, #0x55, lsl #12  ; [pp+0x55310] Obj!GrowthDirection@e354a1
    //     0x7767a4: ldr             x5, [x5, #0x310]
    // 0x7767a8: r0 = layoutChildSequence()
    //     0x7767a8: bl              #0x776944  ; [package:flutter/src/rendering/viewport.dart] RenderViewportBase::layoutChildSequence
    // 0x7767ac: LeaveFrame
    //     0x7767ac: mov             SP, fp
    //     0x7767b0: ldp             fp, lr, [SP], #0x10
    // 0x7767b4: ret
    //     0x7767b4: ret             
    // 0x7767b8: r0 = StackOverflowSharedWithFPURegs()
    //     0x7767b8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7767bc: b               #0x7761f0
    // 0x7767c0: stp             q1, q3, [SP, #-0x20]!
    // 0x7767c4: SaveReg d0
    //     0x7767c4: str             q0, [SP, #-0x10]!
    // 0x7767c8: stp             x4, x5, [SP, #-0x10]!
    // 0x7767cc: r0 = AllocateDouble()
    //     0x7767cc: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7767d0: ldp             x4, x5, [SP], #0x10
    // 0x7767d4: RestoreReg d0
    //     0x7767d4: ldr             q0, [SP], #0x10
    // 0x7767d8: ldp             q1, q3, [SP], #0x20
    // 0x7767dc: b               #0x776230
    // 0x7767e0: stp             q1, q3, [SP, #-0x20]!
    // 0x7767e4: SaveReg d0
    //     0x7767e4: str             q0, [SP, #-0x10]!
    // 0x7767e8: stp             x4, x5, [SP, #-0x10]!
    // 0x7767ec: SaveReg r0
    //     0x7767ec: str             x0, [SP, #-8]!
    // 0x7767f0: r0 = AllocateDouble()
    //     0x7767f0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7767f4: mov             x6, x0
    // 0x7767f8: RestoreReg r0
    //     0x7767f8: ldr             x0, [SP], #8
    // 0x7767fc: ldp             x4, x5, [SP], #0x10
    // 0x776800: RestoreReg d0
    //     0x776800: ldr             q0, [SP], #0x10
    // 0x776804: ldp             q1, q3, [SP], #0x20
    // 0x776808: b               #0x77625c
    // 0x77680c: stp             q1, q2, [SP, #-0x20]!
    // 0x776810: SaveReg d0
    //     0x776810: str             q0, [SP, #-0x10]!
    // 0x776814: SaveReg r0
    //     0x776814: str             x0, [SP, #-8]!
    // 0x776818: r0 = AllocateDouble()
    //     0x776818: bl              #0xec2254  ; AllocateDoubleStub
    // 0x77681c: mov             x4, x0
    // 0x776820: RestoreReg r0
    //     0x776820: ldr             x0, [SP], #8
    // 0x776824: RestoreReg d0
    //     0x776824: ldr             q0, [SP], #0x10
    // 0x776828: ldp             q1, q2, [SP], #0x20
    // 0x77682c: b               #0x7762a8
    // 0x776830: SaveReg d0
    //     0x776830: str             q0, [SP, #-0x10]!
    // 0x776834: stp             x4, x5, [SP, #-0x10]!
    // 0x776838: r0 = AllocateDouble()
    //     0x776838: bl              #0xec2254  ; AllocateDoubleStub
    // 0x77683c: ldp             x4, x5, [SP], #0x10
    // 0x776840: RestoreReg d0
    //     0x776840: ldr             q0, [SP], #0x10
    // 0x776844: b               #0x776308
    // 0x776848: stp             q0, q2, [SP, #-0x20]!
    // 0x77684c: stp             x4, x5, [SP, #-0x10]!
    // 0x776850: r0 = AllocateDouble()
    //     0x776850: bl              #0xec2254  ; AllocateDoubleStub
    // 0x776854: ldp             x4, x5, [SP], #0x10
    // 0x776858: ldp             q0, q2, [SP], #0x20
    // 0x77685c: b               #0x776364
    // 0x776860: stp             q2, q4, [SP, #-0x20]!
    // 0x776864: stp             q0, q1, [SP, #-0x20]!
    // 0x776868: stp             x4, x5, [SP, #-0x10]!
    // 0x77686c: r0 = AllocateDouble()
    //     0x77686c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x776870: ldp             x4, x5, [SP], #0x10
    // 0x776874: ldp             q0, q1, [SP], #0x20
    // 0x776878: ldp             q2, q4, [SP], #0x20
    // 0x77687c: b               #0x7763c8
    // 0x776880: stp             q2, q4, [SP, #-0x20]!
    // 0x776884: stp             q0, q1, [SP, #-0x20]!
    // 0x776888: stp             x4, x5, [SP, #-0x10]!
    // 0x77688c: SaveReg r0
    //     0x77688c: str             x0, [SP, #-8]!
    // 0x776890: r0 = AllocateDouble()
    //     0x776890: bl              #0xec2254  ; AllocateDoubleStub
    // 0x776894: mov             x1, x0
    // 0x776898: RestoreReg r0
    //     0x776898: ldr             x0, [SP], #8
    // 0x77689c: ldp             x4, x5, [SP], #0x10
    // 0x7768a0: ldp             q0, q1, [SP], #0x20
    // 0x7768a4: ldp             q2, q4, [SP], #0x20
    // 0x7768a8: b               #0x7763f4
    // 0x7768ac: SaveReg d2
    //     0x7768ac: str             q2, [SP, #-0x10]!
    // 0x7768b0: SaveReg r0
    //     0x7768b0: str             x0, [SP, #-8]!
    // 0x7768b4: r0 = AllocateDouble()
    //     0x7768b4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7768b8: mov             x1, x0
    // 0x7768bc: RestoreReg r0
    //     0x7768bc: ldr             x0, [SP], #8
    // 0x7768c0: RestoreReg d2
    //     0x7768c0: ldr             q2, [SP], #0x10
    // 0x7768c4: b               #0x776438
    // 0x7768c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7768c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7768cc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7768cc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7768d0: stp             q4, q5, [SP, #-0x20]!
    // 0x7768d4: stp             q1, q3, [SP, #-0x20]!
    // 0x7768d8: SaveReg d0
    //     0x7768d8: str             q0, [SP, #-0x10]!
    // 0x7768dc: stp             x6, x7, [SP, #-0x10]!
    // 0x7768e0: stp             x4, x5, [SP, #-0x10]!
    // 0x7768e4: SaveReg r0
    //     0x7768e4: str             x0, [SP, #-8]!
    // 0x7768e8: r0 = AllocateDouble()
    //     0x7768e8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7768ec: mov             x2, x0
    // 0x7768f0: RestoreReg r0
    //     0x7768f0: ldr             x0, [SP], #8
    // 0x7768f4: ldp             x4, x5, [SP], #0x10
    // 0x7768f8: ldp             x6, x7, [SP], #0x10
    // 0x7768fc: RestoreReg d0
    //     0x7768fc: ldr             q0, [SP], #0x10
    // 0x776900: ldp             q1, q3, [SP], #0x20
    // 0x776904: ldp             q4, q5, [SP], #0x20
    // 0x776908: b               #0x776524
    // 0x77690c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x77690c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x776910: stp             q4, q5, [SP, #-0x20]!
    // 0x776914: stp             q2, q3, [SP, #-0x20]!
    // 0x776918: SaveReg d0
    //     0x776918: str             q0, [SP, #-0x10]!
    // 0x77691c: stp             x5, x6, [SP, #-0x10]!
    // 0x776920: stp             x0, x4, [SP, #-0x10]!
    // 0x776924: r0 = AllocateDouble()
    //     0x776924: bl              #0xec2254  ; AllocateDoubleStub
    // 0x776928: mov             x2, x0
    // 0x77692c: ldp             x0, x4, [SP], #0x10
    // 0x776930: ldp             x5, x6, [SP], #0x10
    // 0x776934: RestoreReg d0
    //     0x776934: ldr             q0, [SP], #0x10
    // 0x776938: ldp             q2, q3, [SP], #0x20
    // 0x77693c: ldp             q4, q5, [SP], #0x20
    // 0x776940: b               #0x776728
  }
  _ describeSemanticsClip(/* No info */) {
    // ** addr: 0x7a837c, size: 0x284
    // 0x7a837c: EnterFrame
    //     0x7a837c: stp             fp, lr, [SP, #-0x10]!
    //     0x7a8380: mov             fp, SP
    // 0x7a8384: AllocStack(0x28)
    //     0x7a8384: sub             SP, SP, #0x28
    // 0x7a8388: SetupParameters(UnboundedRenderViewport this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0 */)
    //     0x7a8388: mov             x3, x1
    //     0x7a838c: mov             x0, x2
    //     0x7a8390: stur            x1, [fp, #-8]
    // 0x7a8394: CheckStackOverflow
    //     0x7a8394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a8398: cmp             SP, x16
    //     0x7a839c: b.ls            #0x7a85e8
    // 0x7a83a0: r2 = Null
    //     0x7a83a0: mov             x2, NULL
    // 0x7a83a4: r1 = Null
    //     0x7a83a4: mov             x1, NULL
    // 0x7a83a8: r4 = 60
    //     0x7a83a8: movz            x4, #0x3c
    // 0x7a83ac: branchIfSmi(r0, 0x7a83b8)
    //     0x7a83ac: tbz             w0, #0, #0x7a83b8
    // 0x7a83b0: r4 = LoadClassIdInstr(r0)
    //     0x7a83b0: ldur            x4, [x0, #-1]
    //     0x7a83b4: ubfx            x4, x4, #0xc, #0x14
    // 0x7a83b8: sub             x4, x4, #0xb86
    // 0x7a83bc: cmp             x4, #0x28
    // 0x7a83c0: b.ls            #0x7a83d8
    // 0x7a83c4: r8 = RenderSliver?
    //     0x7a83c4: add             x8, PP, #0x3a, lsl #12  ; [pp+0x3a5f0] Type: RenderSliver?
    //     0x7a83c8: ldr             x8, [x8, #0x5f0]
    // 0x7a83cc: r3 = Null
    //     0x7a83cc: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d350] Null
    //     0x7a83d0: ldr             x3, [x3, #0x350]
    // 0x7a83d4: r0 = DefaultNullableTypeTest()
    //     0x7a83d4: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7a83d8: ldur            x0, [fp, #-8]
    // 0x7a83dc: LoadField: r1 = r0->field_bb
    //     0x7a83dc: ldur            w1, [x0, #0xbb]
    // 0x7a83e0: DecompressPointer r1
    //     0x7a83e0: add             x1, x1, HEAP, lsl #32
    // 0x7a83e4: cmp             w1, NULL
    // 0x7a83e8: b.ne            #0x7a840c
    // 0x7a83ec: mov             x1, x0
    // 0x7a83f0: r0 = size()
    //     0x7a83f0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7a83f4: mov             x2, x0
    // 0x7a83f8: r1 = Instance_Offset
    //     0x7a83f8: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7a83fc: r0 = &()
    //     0x7a83fc: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7a8400: LeaveFrame
    //     0x7a8400: mov             SP, fp
    //     0x7a8404: ldp             fp, lr, [SP], #0x10
    // 0x7a8408: ret
    //     0x7a8408: ret             
    // 0x7a840c: mov             x1, x0
    // 0x7a8410: r0 = axis()
    //     0x7a8410: bl              #0x77741c  ; [package:flutter/src/rendering/viewport.dart] RenderViewportBase::axis
    // 0x7a8414: LoadField: r2 = r0->field_7
    //     0x7a8414: ldur            x2, [x0, #7]
    // 0x7a8418: r0 = BoxInt64Instr(r2)
    //     0x7a8418: sbfiz           x0, x2, #1, #0x1f
    //     0x7a841c: cmp             x2, x0, asr #1
    //     0x7a8420: b.eq            #0x7a842c
    //     0x7a8424: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7a8428: stur            x2, [x0, #7]
    // 0x7a842c: cmp             w0, #2
    // 0x7a8430: b.ne            #0x7a8510
    // 0x7a8434: ldur            x0, [fp, #-8]
    // 0x7a8438: mov             x1, x0
    // 0x7a843c: r0 = size()
    //     0x7a843c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7a8440: mov             x2, x0
    // 0x7a8444: r1 = Instance_Offset
    //     0x7a8444: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7a8448: r0 = &()
    //     0x7a8448: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7a844c: LoadField: d0 = r0->field_7
    //     0x7a844c: ldur            d0, [x0, #7]
    // 0x7a8450: ldur            x1, [fp, #-8]
    // 0x7a8454: stur            d0, [fp, #-0x10]
    // 0x7a8458: r0 = size()
    //     0x7a8458: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7a845c: mov             x2, x0
    // 0x7a8460: r1 = Instance_Offset
    //     0x7a8460: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7a8464: r0 = &()
    //     0x7a8464: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7a8468: LoadField: d0 = r0->field_f
    //     0x7a8468: ldur            d0, [x0, #0xf]
    // 0x7a846c: ldur            x0, [fp, #-8]
    // 0x7a8470: LoadField: r1 = r0->field_bb
    //     0x7a8470: ldur            w1, [x0, #0xbb]
    // 0x7a8474: DecompressPointer r1
    //     0x7a8474: add             x1, x1, HEAP, lsl #32
    // 0x7a8478: cmp             w1, NULL
    // 0x7a847c: b.eq            #0x7a85f0
    // 0x7a8480: LoadField: d1 = r1->field_7
    //     0x7a8480: ldur            d1, [x1, #7]
    // 0x7a8484: fsub            d2, d0, d1
    // 0x7a8488: mov             x1, x0
    // 0x7a848c: stur            d2, [fp, #-0x18]
    // 0x7a8490: r0 = size()
    //     0x7a8490: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7a8494: mov             x2, x0
    // 0x7a8498: r1 = Instance_Offset
    //     0x7a8498: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7a849c: r0 = &()
    //     0x7a849c: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7a84a0: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x7a84a0: ldur            d0, [x0, #0x17]
    // 0x7a84a4: ldur            x1, [fp, #-8]
    // 0x7a84a8: stur            d0, [fp, #-0x20]
    // 0x7a84ac: r0 = size()
    //     0x7a84ac: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7a84b0: mov             x2, x0
    // 0x7a84b4: r1 = Instance_Offset
    //     0x7a84b4: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7a84b8: r0 = &()
    //     0x7a84b8: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7a84bc: LoadField: d0 = r0->field_1f
    //     0x7a84bc: ldur            d0, [x0, #0x1f]
    // 0x7a84c0: ldur            x0, [fp, #-8]
    // 0x7a84c4: LoadField: r1 = r0->field_bb
    //     0x7a84c4: ldur            w1, [x0, #0xbb]
    // 0x7a84c8: DecompressPointer r1
    //     0x7a84c8: add             x1, x1, HEAP, lsl #32
    // 0x7a84cc: cmp             w1, NULL
    // 0x7a84d0: b.eq            #0x7a85f4
    // 0x7a84d4: LoadField: d1 = r1->field_7
    //     0x7a84d4: ldur            d1, [x1, #7]
    // 0x7a84d8: fadd            d2, d0, d1
    // 0x7a84dc: stur            d2, [fp, #-0x28]
    // 0x7a84e0: r0 = Rect()
    //     0x7a84e0: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7a84e4: ldur            d0, [fp, #-0x10]
    // 0x7a84e8: StoreField: r0->field_7 = d0
    //     0x7a84e8: stur            d0, [x0, #7]
    // 0x7a84ec: ldur            d0, [fp, #-0x18]
    // 0x7a84f0: StoreField: r0->field_f = d0
    //     0x7a84f0: stur            d0, [x0, #0xf]
    // 0x7a84f4: ldur            d0, [fp, #-0x20]
    // 0x7a84f8: ArrayStore: r0[0] = d0  ; List_8
    //     0x7a84f8: stur            d0, [x0, #0x17]
    // 0x7a84fc: ldur            d0, [fp, #-0x28]
    // 0x7a8500: StoreField: r0->field_1f = d0
    //     0x7a8500: stur            d0, [x0, #0x1f]
    // 0x7a8504: LeaveFrame
    //     0x7a8504: mov             SP, fp
    //     0x7a8508: ldp             fp, lr, [SP], #0x10
    // 0x7a850c: ret
    //     0x7a850c: ret             
    // 0x7a8510: ldur            x0, [fp, #-8]
    // 0x7a8514: mov             x1, x0
    // 0x7a8518: r0 = size()
    //     0x7a8518: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7a851c: mov             x2, x0
    // 0x7a8520: r1 = Instance_Offset
    //     0x7a8520: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7a8524: r0 = &()
    //     0x7a8524: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7a8528: LoadField: d0 = r0->field_7
    //     0x7a8528: ldur            d0, [x0, #7]
    // 0x7a852c: ldur            x0, [fp, #-8]
    // 0x7a8530: LoadField: r1 = r0->field_bb
    //     0x7a8530: ldur            w1, [x0, #0xbb]
    // 0x7a8534: DecompressPointer r1
    //     0x7a8534: add             x1, x1, HEAP, lsl #32
    // 0x7a8538: cmp             w1, NULL
    // 0x7a853c: b.eq            #0x7a85f8
    // 0x7a8540: LoadField: d1 = r1->field_7
    //     0x7a8540: ldur            d1, [x1, #7]
    // 0x7a8544: fsub            d2, d0, d1
    // 0x7a8548: mov             x1, x0
    // 0x7a854c: stur            d2, [fp, #-0x10]
    // 0x7a8550: r0 = size()
    //     0x7a8550: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7a8554: mov             x2, x0
    // 0x7a8558: r1 = Instance_Offset
    //     0x7a8558: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7a855c: r0 = &()
    //     0x7a855c: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7a8560: LoadField: d0 = r0->field_f
    //     0x7a8560: ldur            d0, [x0, #0xf]
    // 0x7a8564: ldur            x1, [fp, #-8]
    // 0x7a8568: stur            d0, [fp, #-0x18]
    // 0x7a856c: r0 = size()
    //     0x7a856c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7a8570: mov             x2, x0
    // 0x7a8574: r1 = Instance_Offset
    //     0x7a8574: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7a8578: r0 = &()
    //     0x7a8578: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7a857c: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x7a857c: ldur            d0, [x0, #0x17]
    // 0x7a8580: ldur            x1, [fp, #-8]
    // 0x7a8584: LoadField: r0 = r1->field_bb
    //     0x7a8584: ldur            w0, [x1, #0xbb]
    // 0x7a8588: DecompressPointer r0
    //     0x7a8588: add             x0, x0, HEAP, lsl #32
    // 0x7a858c: cmp             w0, NULL
    // 0x7a8590: b.eq            #0x7a85fc
    // 0x7a8594: LoadField: d1 = r0->field_7
    //     0x7a8594: ldur            d1, [x0, #7]
    // 0x7a8598: fadd            d2, d0, d1
    // 0x7a859c: stur            d2, [fp, #-0x20]
    // 0x7a85a0: r0 = size()
    //     0x7a85a0: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7a85a4: mov             x2, x0
    // 0x7a85a8: r1 = Instance_Offset
    //     0x7a85a8: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7a85ac: r0 = &()
    //     0x7a85ac: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x7a85b0: LoadField: d0 = r0->field_1f
    //     0x7a85b0: ldur            d0, [x0, #0x1f]
    // 0x7a85b4: stur            d0, [fp, #-0x28]
    // 0x7a85b8: r0 = Rect()
    //     0x7a85b8: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7a85bc: ldur            d0, [fp, #-0x10]
    // 0x7a85c0: StoreField: r0->field_7 = d0
    //     0x7a85c0: stur            d0, [x0, #7]
    // 0x7a85c4: ldur            d0, [fp, #-0x18]
    // 0x7a85c8: StoreField: r0->field_f = d0
    //     0x7a85c8: stur            d0, [x0, #0xf]
    // 0x7a85cc: ldur            d0, [fp, #-0x20]
    // 0x7a85d0: ArrayStore: r0[0] = d0  ; List_8
    //     0x7a85d0: stur            d0, [x0, #0x17]
    // 0x7a85d4: ldur            d0, [fp, #-0x28]
    // 0x7a85d8: StoreField: r0->field_1f = d0
    //     0x7a85d8: stur            d0, [x0, #0x1f]
    // 0x7a85dc: LeaveFrame
    //     0x7a85dc: mov             SP, fp
    //     0x7a85e0: ldp             fp, lr, [SP], #0x10
    // 0x7a85e4: ret
    //     0x7a85e4: ret             
    // 0x7a85e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a85e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a85ec: b               #0x7a83a0
    // 0x7a85f0: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7a85f0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7a85f4: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7a85f4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7a85f8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7a85f8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x7a85fc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x7a85fc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ performResize(/* No info */) {
    // ** addr: 0x7e2150, size: 0x234
    // 0x7e2150: EnterFrame
    //     0x7e2150: stp             fp, lr, [SP, #-0x10]!
    //     0x7e2154: mov             fp, SP
    // 0x7e2158: AllocStack(0x28)
    //     0x7e2158: sub             SP, SP, #0x28
    // 0x7e215c: SetupParameters(UnboundedRenderViewport this /* r1 => r0, fp-0x8 */)
    //     0x7e215c: mov             x0, x1
    //     0x7e2160: stur            x1, [fp, #-8]
    // 0x7e2164: CheckStackOverflow
    //     0x7e2164: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e2168: cmp             SP, x16
    //     0x7e216c: b.ls            #0x7e2344
    // 0x7e2170: mov             x1, x0
    // 0x7e2174: r0 = performResize()
    //     0x7e2174: bl              #0x7e2384  ; [package:flutter/src/rendering/box.dart] RenderBox::performResize
    // 0x7e2178: ldur            x1, [fp, #-8]
    // 0x7e217c: r0 = axis()
    //     0x7e217c: bl              #0x77741c  ; [package:flutter/src/rendering/viewport.dart] RenderViewportBase::axis
    // 0x7e2180: LoadField: r1 = r0->field_7
    //     0x7e2180: ldur            x1, [x0, #7]
    // 0x7e2184: cmp             x1, #0
    // 0x7e2188: b.gt            #0x7e2260
    // 0x7e218c: ldur            x1, [fp, #-8]
    // 0x7e2190: LoadField: r0 = r1->field_73
    //     0x7e2190: ldur            w0, [x1, #0x73]
    // 0x7e2194: DecompressPointer r0
    //     0x7e2194: add             x0, x0, HEAP, lsl #32
    // 0x7e2198: stur            x0, [fp, #-0x10]
    // 0x7e219c: r0 = size()
    //     0x7e219c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7e21a0: LoadField: d0 = r0->field_7
    //     0x7e21a0: ldur            d0, [x0, #7]
    // 0x7e21a4: ldur            x1, [fp, #-0x10]
    // 0x7e21a8: r0 = LoadClassIdInstr(r1)
    //     0x7e21a8: ldur            x0, [x1, #-1]
    //     0x7e21ac: ubfx            x0, x0, #0xc, #0x14
    // 0x7e21b0: sub             x16, x0, #0xe46
    // 0x7e21b4: cmp             x16, #1
    // 0x7e21b8: b.ls            #0x7e21c4
    // 0x7e21bc: cmp             x0, #0xe49
    // 0x7e21c0: b.ne            #0x7e2248
    // 0x7e21c4: LoadField: r0 = r1->field_43
    //     0x7e21c4: ldur            w0, [x1, #0x43]
    // 0x7e21c8: DecompressPointer r0
    //     0x7e21c8: add             x0, x0, HEAP, lsl #32
    // 0x7e21cc: r2 = inline_Allocate_Double()
    //     0x7e21cc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x7e21d0: add             x2, x2, #0x10
    //     0x7e21d4: cmp             x3, x2
    //     0x7e21d8: b.ls            #0x7e234c
    //     0x7e21dc: str             x2, [THR, #0x50]  ; THR::top
    //     0x7e21e0: sub             x2, x2, #0xf
    //     0x7e21e4: movz            x3, #0xe15c
    //     0x7e21e8: movk            x3, #0x3, lsl #16
    //     0x7e21ec: stur            x3, [x2, #-1]
    // 0x7e21f0: StoreField: r2->field_7 = d0
    //     0x7e21f0: stur            d0, [x2, #7]
    // 0x7e21f4: stur            x2, [fp, #-0x18]
    // 0x7e21f8: r3 = LoadClassIdInstr(r0)
    //     0x7e21f8: ldur            x3, [x0, #-1]
    //     0x7e21fc: ubfx            x3, x3, #0xc, #0x14
    // 0x7e2200: stp             x2, x0, [SP]
    // 0x7e2204: mov             x0, x3
    // 0x7e2208: mov             lr, x0
    // 0x7e220c: ldr             lr, [x21, lr, lsl #3]
    // 0x7e2210: blr             lr
    // 0x7e2214: tbz             w0, #4, #0x7e2334
    // 0x7e2218: ldur            x1, [fp, #-0x10]
    // 0x7e221c: r2 = true
    //     0x7e221c: add             x2, NULL, #0x20  ; true
    // 0x7e2220: ldur            x0, [fp, #-0x18]
    // 0x7e2224: StoreField: r1->field_43 = r0
    //     0x7e2224: stur            w0, [x1, #0x43]
    //     0x7e2228: ldurb           w16, [x1, #-1]
    //     0x7e222c: ldurb           w17, [x0, #-1]
    //     0x7e2230: and             x16, x17, x16, lsr #2
    //     0x7e2234: tst             x16, HEAP, lsr #32
    //     0x7e2238: b.eq            #0x7e2240
    //     0x7e223c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e2240: StoreField: r1->field_4b = r2
    //     0x7e2240: stur            w2, [x1, #0x4b]
    // 0x7e2244: b               #0x7e2334
    // 0x7e2248: r0 = LoadClassIdInstr(r1)
    //     0x7e2248: ldur            x0, [x1, #-1]
    //     0x7e224c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e2250: r0 = GDT[cid_x0 + -0xff4]()
    //     0x7e2250: sub             lr, x0, #0xff4
    //     0x7e2254: ldr             lr, [x21, lr, lsl #3]
    //     0x7e2258: blr             lr
    // 0x7e225c: b               #0x7e2334
    // 0x7e2260: ldur            x1, [fp, #-8]
    // 0x7e2264: r2 = true
    //     0x7e2264: add             x2, NULL, #0x20  ; true
    // 0x7e2268: LoadField: r0 = r1->field_73
    //     0x7e2268: ldur            w0, [x1, #0x73]
    // 0x7e226c: DecompressPointer r0
    //     0x7e226c: add             x0, x0, HEAP, lsl #32
    // 0x7e2270: stur            x0, [fp, #-0x10]
    // 0x7e2274: r0 = size()
    //     0x7e2274: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7e2278: LoadField: d0 = r0->field_f
    //     0x7e2278: ldur            d0, [x0, #0xf]
    // 0x7e227c: ldur            x1, [fp, #-0x10]
    // 0x7e2280: r0 = LoadClassIdInstr(r1)
    //     0x7e2280: ldur            x0, [x1, #-1]
    //     0x7e2284: ubfx            x0, x0, #0xc, #0x14
    // 0x7e2288: sub             x16, x0, #0xe46
    // 0x7e228c: cmp             x16, #1
    // 0x7e2290: b.ls            #0x7e229c
    // 0x7e2294: cmp             x0, #0xe49
    // 0x7e2298: b.ne            #0x7e2320
    // 0x7e229c: LoadField: r0 = r1->field_43
    //     0x7e229c: ldur            w0, [x1, #0x43]
    // 0x7e22a0: DecompressPointer r0
    //     0x7e22a0: add             x0, x0, HEAP, lsl #32
    // 0x7e22a4: r2 = inline_Allocate_Double()
    //     0x7e22a4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x7e22a8: add             x2, x2, #0x10
    //     0x7e22ac: cmp             x3, x2
    //     0x7e22b0: b.ls            #0x7e2368
    //     0x7e22b4: str             x2, [THR, #0x50]  ; THR::top
    //     0x7e22b8: sub             x2, x2, #0xf
    //     0x7e22bc: movz            x3, #0xe15c
    //     0x7e22c0: movk            x3, #0x3, lsl #16
    //     0x7e22c4: stur            x3, [x2, #-1]
    // 0x7e22c8: StoreField: r2->field_7 = d0
    //     0x7e22c8: stur            d0, [x2, #7]
    // 0x7e22cc: stur            x2, [fp, #-8]
    // 0x7e22d0: r3 = LoadClassIdInstr(r0)
    //     0x7e22d0: ldur            x3, [x0, #-1]
    //     0x7e22d4: ubfx            x3, x3, #0xc, #0x14
    // 0x7e22d8: stp             x2, x0, [SP]
    // 0x7e22dc: mov             x0, x3
    // 0x7e22e0: mov             lr, x0
    // 0x7e22e4: ldr             lr, [x21, lr, lsl #3]
    // 0x7e22e8: blr             lr
    // 0x7e22ec: tbz             w0, #4, #0x7e2334
    // 0x7e22f0: ldur            x1, [fp, #-0x10]
    // 0x7e22f4: r2 = true
    //     0x7e22f4: add             x2, NULL, #0x20  ; true
    // 0x7e22f8: ldur            x0, [fp, #-8]
    // 0x7e22fc: StoreField: r1->field_43 = r0
    //     0x7e22fc: stur            w0, [x1, #0x43]
    //     0x7e2300: ldurb           w16, [x1, #-1]
    //     0x7e2304: ldurb           w17, [x0, #-1]
    //     0x7e2308: and             x16, x17, x16, lsr #2
    //     0x7e230c: tst             x16, HEAP, lsr #32
    //     0x7e2310: b.eq            #0x7e2318
    //     0x7e2314: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7e2318: StoreField: r1->field_4b = r2
    //     0x7e2318: stur            w2, [x1, #0x4b]
    // 0x7e231c: b               #0x7e2334
    // 0x7e2320: r0 = LoadClassIdInstr(r1)
    //     0x7e2320: ldur            x0, [x1, #-1]
    //     0x7e2324: ubfx            x0, x0, #0xc, #0x14
    // 0x7e2328: r0 = GDT[cid_x0 + -0xff4]()
    //     0x7e2328: sub             lr, x0, #0xff4
    //     0x7e232c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e2330: blr             lr
    // 0x7e2334: r0 = Null
    //     0x7e2334: mov             x0, NULL
    // 0x7e2338: LeaveFrame
    //     0x7e2338: mov             SP, fp
    //     0x7e233c: ldp             fp, lr, [SP], #0x10
    // 0x7e2340: ret
    //     0x7e2340: ret             
    // 0x7e2344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e2344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e2348: b               #0x7e2170
    // 0x7e234c: SaveReg d0
    //     0x7e234c: str             q0, [SP, #-0x10]!
    // 0x7e2350: stp             x0, x1, [SP, #-0x10]!
    // 0x7e2354: r0 = AllocateDouble()
    //     0x7e2354: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7e2358: mov             x2, x0
    // 0x7e235c: ldp             x0, x1, [SP], #0x10
    // 0x7e2360: RestoreReg d0
    //     0x7e2360: ldr             q0, [SP], #0x10
    // 0x7e2364: b               #0x7e21f0
    // 0x7e2368: SaveReg d0
    //     0x7e2368: str             q0, [SP, #-0x10]!
    // 0x7e236c: stp             x0, x1, [SP, #-0x10]!
    // 0x7e2370: r0 = AllocateDouble()
    //     0x7e2370: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7e2374: mov             x2, x0
    // 0x7e2378: ldp             x0, x1, [SP], #0x10
    // 0x7e237c: RestoreReg d0
    //     0x7e237c: ldr             q0, [SP], #0x10
    // 0x7e2380: b               #0x7e22c8
  }
  _ UnboundedRenderViewport(/* No info */) {
    // ** addr: 0x85921c, size: 0x80
    // 0x85921c: EnterFrame
    //     0x85921c: stp             fp, lr, [SP, #-0x10]!
    //     0x859220: mov             fp, SP
    // 0x859224: AllocStack(0x8)
    //     0x859224: sub             SP, SP, #8
    // 0x859228: r4 = Sentinel
    //     0x859228: ldr             x4, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x85922c: r0 = false
    //     0x85922c: add             x0, NULL, #0x30  ; false
    // 0x859230: mov             x16, x3
    // 0x859234: mov             x3, x1
    // 0x859238: mov             x1, x16
    // 0x85923c: mov             x16, x5
    // 0x859240: mov             x5, x3
    // 0x859244: mov             x3, x16
    // 0x859248: mov             x16, x6
    // 0x85924c: mov             x6, x5
    // 0x859250: mov             x5, x16
    // 0x859254: CheckStackOverflow
    //     0x859254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x859258: cmp             SP, x16
    //     0x85925c: b.ls            #0x859294
    // 0x859260: StoreField: r6->field_af = r4
    //     0x859260: stur            w4, [x6, #0xaf]
    // 0x859264: StoreField: r6->field_b3 = r4
    //     0x859264: stur            w4, [x6, #0xb3]
    // 0x859268: StoreField: r6->field_b7 = r0
    //     0x859268: stur            w0, [x6, #0xb7]
    // 0x85926c: StoreField: r6->field_a7 = d0
    //     0x85926c: stur            d0, [x6, #0xa7]
    // 0x859270: str             x1, [SP]
    // 0x859274: mov             x1, x6
    // 0x859278: r4 = const [0, 0x5, 0x1, 0x4, cacheExtent, 0x4, null]
    //     0x859278: add             x4, PP, #0x5a, lsl #12  ; [pp+0x5af00] List(7) [0, 0x5, 0x1, 0x4, "cacheExtent", 0x4, Null]
    //     0x85927c: ldr             x4, [x4, #0xf00]
    // 0x859280: r0 = RenderViewport()
    //     0x859280: bl              #0x858d9c  ; [package:flutter/src/rendering/viewport.dart] RenderViewport::RenderViewport
    // 0x859284: r0 = Null
    //     0x859284: mov             x0, NULL
    // 0x859288: LeaveFrame
    //     0x859288: mov             SP, fp
    //     0x85928c: ldp             fp, lr, [SP], #0x10
    // 0x859290: ret
    //     0x859290: ret             
    // 0x859294: r0 = StackOverflowSharedWithFPURegs()
    //     0x859294: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x859298: b               #0x859260
  }
  get _ hasVisualOverflow(/* No info */) {
    // ** addr: 0xc4575c, size: 0xc
    // 0xc4575c: LoadField: r0 = r1->field_b7
    //     0xc4575c: ldur            w0, [x1, #0xb7]
    // 0xc45760: DecompressPointer r0
    //     0xc45760: add             x0, x0, HEAP, lsl #32
    // 0xc45764: ret
    //     0xc45764: ret             
  }
  _ updateOutOfBandData(/* No info */) {
    // ** addr: 0xcfd070, size: 0x14c
    // 0xcfd070: EnterFrame
    //     0xcfd070: stp             fp, lr, [SP, #-0x10]!
    //     0xcfd074: mov             fp, SP
    // 0xcfd078: LoadField: r4 = r2->field_7
    //     0xcfd078: ldur            x4, [x2, #7]
    // 0xcfd07c: cmp             x4, #0
    // 0xcfd080: b.gt            #0xcfd0ec
    // 0xcfd084: LoadField: r2 = r1->field_b3
    //     0xcfd084: ldur            w2, [x1, #0xb3]
    // 0xcfd088: DecompressPointer r2
    //     0xcfd088: add             x2, x2, HEAP, lsl #32
    // 0xcfd08c: r16 = Sentinel
    //     0xcfd08c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xcfd090: cmp             w2, w16
    // 0xcfd094: b.eq            #0xcfd174
    // 0xcfd098: LoadField: d0 = r3->field_7
    //     0xcfd098: ldur            d0, [x3, #7]
    // 0xcfd09c: LoadField: d1 = r2->field_7
    //     0xcfd09c: ldur            d1, [x2, #7]
    // 0xcfd0a0: fadd            d2, d1, d0
    // 0xcfd0a4: r0 = inline_Allocate_Double()
    //     0xcfd0a4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xcfd0a8: add             x0, x0, #0x10
    //     0xcfd0ac: cmp             x2, x0
    //     0xcfd0b0: b.ls            #0xcfd180
    //     0xcfd0b4: str             x0, [THR, #0x50]  ; THR::top
    //     0xcfd0b8: sub             x0, x0, #0xf
    //     0xcfd0bc: movz            x2, #0xe15c
    //     0xcfd0c0: movk            x2, #0x3, lsl #16
    //     0xcfd0c4: stur            x2, [x0, #-1]
    // 0xcfd0c8: StoreField: r0->field_7 = d2
    //     0xcfd0c8: stur            d2, [x0, #7]
    // 0xcfd0cc: StoreField: r1->field_b3 = r0
    //     0xcfd0cc: stur            w0, [x1, #0xb3]
    //     0xcfd0d0: ldurb           w16, [x1, #-1]
    //     0xcfd0d4: ldurb           w17, [x0, #-1]
    //     0xcfd0d8: and             x16, x17, x16, lsr #2
    //     0xcfd0dc: tst             x16, HEAP, lsr #32
    //     0xcfd0e0: b.eq            #0xcfd0e8
    //     0xcfd0e4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcfd0e8: b               #0xcfd150
    // 0xcfd0ec: LoadField: r2 = r1->field_af
    //     0xcfd0ec: ldur            w2, [x1, #0xaf]
    // 0xcfd0f0: DecompressPointer r2
    //     0xcfd0f0: add             x2, x2, HEAP, lsl #32
    // 0xcfd0f4: r16 = Sentinel
    //     0xcfd0f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xcfd0f8: cmp             w2, w16
    // 0xcfd0fc: b.eq            #0xcfd198
    // 0xcfd100: LoadField: d0 = r3->field_7
    //     0xcfd100: ldur            d0, [x3, #7]
    // 0xcfd104: LoadField: d1 = r2->field_7
    //     0xcfd104: ldur            d1, [x2, #7]
    // 0xcfd108: fsub            d2, d1, d0
    // 0xcfd10c: r0 = inline_Allocate_Double()
    //     0xcfd10c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xcfd110: add             x0, x0, #0x10
    //     0xcfd114: cmp             x2, x0
    //     0xcfd118: b.ls            #0xcfd1a4
    //     0xcfd11c: str             x0, [THR, #0x50]  ; THR::top
    //     0xcfd120: sub             x0, x0, #0xf
    //     0xcfd124: movz            x2, #0xe15c
    //     0xcfd128: movk            x2, #0x3, lsl #16
    //     0xcfd12c: stur            x2, [x0, #-1]
    // 0xcfd130: StoreField: r0->field_7 = d2
    //     0xcfd130: stur            d2, [x0, #7]
    // 0xcfd134: StoreField: r1->field_af = r0
    //     0xcfd134: stur            w0, [x1, #0xaf]
    //     0xcfd138: ldurb           w16, [x1, #-1]
    //     0xcfd13c: ldurb           w17, [x0, #-1]
    //     0xcfd140: and             x16, x17, x16, lsr #2
    //     0xcfd144: tst             x16, HEAP, lsr #32
    //     0xcfd148: b.eq            #0xcfd150
    //     0xcfd14c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xcfd150: LoadField: r2 = r3->field_43
    //     0xcfd150: ldur            w2, [x3, #0x43]
    // 0xcfd154: DecompressPointer r2
    //     0xcfd154: add             x2, x2, HEAP, lsl #32
    // 0xcfd158: tbnz            w2, #4, #0xcfd164
    // 0xcfd15c: r2 = true
    //     0xcfd15c: add             x2, NULL, #0x20  ; true
    // 0xcfd160: StoreField: r1->field_b7 = r2
    //     0xcfd160: stur            w2, [x1, #0xb7]
    // 0xcfd164: r0 = Null
    //     0xcfd164: mov             x0, NULL
    // 0xcfd168: LeaveFrame
    //     0xcfd168: mov             SP, fp
    //     0xcfd16c: ldp             fp, lr, [SP], #0x10
    // 0xcfd170: ret
    //     0xcfd170: ret             
    // 0xcfd174: r9 = _maxScrollExtent
    //     0xcfd174: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d340] Field <UnboundedRenderViewport._maxScrollExtent@2694181789>: late (offset: 0xb4)
    //     0xcfd178: ldr             x9, [x9, #0x340]
    // 0xcfd17c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xcfd17c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xcfd180: SaveReg d2
    //     0xcfd180: str             q2, [SP, #-0x10]!
    // 0xcfd184: stp             x1, x3, [SP, #-0x10]!
    // 0xcfd188: r0 = AllocateDouble()
    //     0xcfd188: bl              #0xec2254  ; AllocateDoubleStub
    // 0xcfd18c: ldp             x1, x3, [SP], #0x10
    // 0xcfd190: RestoreReg d2
    //     0xcfd190: ldr             q2, [SP], #0x10
    // 0xcfd194: b               #0xcfd0c8
    // 0xcfd198: r9 = _minScrollExtent
    //     0xcfd198: add             x9, PP, #0x5d, lsl #12  ; [pp+0x5d348] Field <UnboundedRenderViewport._minScrollExtent@2694181789>: late (offset: 0xb0)
    //     0xcfd19c: ldr             x9, [x9, #0x348]
    // 0xcfd1a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xcfd1a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xcfd1a4: SaveReg d2
    //     0xcfd1a4: str             q2, [SP, #-0x10]!
    // 0xcfd1a8: stp             x1, x3, [SP, #-0x10]!
    // 0xcfd1ac: r0 = AllocateDouble()
    //     0xcfd1ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xcfd1b0: ldp             x1, x3, [SP], #0x10
    // 0xcfd1b4: RestoreReg d2
    //     0xcfd1b4: ldr             q2, [SP], #0x10
    // 0xcfd1b8: b               #0xcfd130
  }
}

// class id: 4572, size: 0x3c, field offset: 0x34
class UnboundedViewport extends Viewport {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x859174, size: 0xa8
    // 0x859174: EnterFrame
    //     0x859174: stp             fp, lr, [SP, #-0x10]!
    //     0x859178: mov             fp, SP
    // 0x85917c: AllocStack(0x30)
    //     0x85917c: sub             SP, SP, #0x30
    // 0x859180: SetupParameters(UnboundedViewport this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1 */)
    //     0x859180: mov             x0, x1
    //     0x859184: stur            x1, [fp, #-0x10]
    //     0x859188: mov             x1, x2
    // 0x85918c: CheckStackOverflow
    //     0x85918c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x859190: cmp             SP, x16
    //     0x859194: b.ls            #0x859214
    // 0x859198: LoadField: r3 = r0->field_f
    //     0x859198: ldur            w3, [x0, #0xf]
    // 0x85919c: DecompressPointer r3
    //     0x85919c: add             x3, x3, HEAP, lsl #32
    // 0x8591a0: mov             x2, x3
    // 0x8591a4: stur            x3, [fp, #-8]
    // 0x8591a8: r0 = getDefaultCrossAxisDirection()
    //     0x8591a8: bl              #0x8590cc  ; [package:flutter/src/widgets/viewport.dart] Viewport::getDefaultCrossAxisDirection
    // 0x8591ac: mov             x2, x0
    // 0x8591b0: ldur            x0, [fp, #-0x10]
    // 0x8591b4: stur            x2, [fp, #-0x28]
    // 0x8591b8: LoadField: d0 = r0->field_33
    //     0x8591b8: ldur            d0, [x0, #0x33]
    // 0x8591bc: stur            d0, [fp, #-0x30]
    // 0x8591c0: LoadField: r6 = r0->field_1f
    //     0x8591c0: ldur            w6, [x0, #0x1f]
    // 0x8591c4: DecompressPointer r6
    //     0x8591c4: add             x6, x6, HEAP, lsl #32
    // 0x8591c8: stur            x6, [fp, #-0x20]
    // 0x8591cc: LoadField: r3 = r0->field_27
    //     0x8591cc: ldur            w3, [x0, #0x27]
    // 0x8591d0: DecompressPointer r3
    //     0x8591d0: add             x3, x3, HEAP, lsl #32
    // 0x8591d4: stur            x3, [fp, #-0x18]
    // 0x8591d8: r1 = <SliverPhysicalContainerParentData>
    //     0x8591d8: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4f220] TypeArguments: <SliverPhysicalContainerParentData>
    //     0x8591dc: ldr             x1, [x1, #0x220]
    // 0x8591e0: r0 = UnboundedRenderViewport()
    //     0x8591e0: bl              #0x85929c  ; AllocateUnboundedRenderViewportStub -> UnboundedRenderViewport (size=0xc0)
    // 0x8591e4: mov             x1, x0
    // 0x8591e8: ldur            d0, [fp, #-0x30]
    // 0x8591ec: ldur            x2, [fp, #-8]
    // 0x8591f0: ldur            x3, [fp, #-0x18]
    // 0x8591f4: ldur            x5, [fp, #-0x28]
    // 0x8591f8: ldur            x6, [fp, #-0x20]
    // 0x8591fc: stur            x0, [fp, #-8]
    // 0x859200: r0 = UnboundedRenderViewport()
    //     0x859200: bl              #0x85921c  ; [package:scrollable_positioned_list/src/viewport.dart] UnboundedRenderViewport::UnboundedRenderViewport
    // 0x859204: ldur            x0, [fp, #-8]
    // 0x859208: LeaveFrame
    //     0x859208: mov             SP, fp
    //     0x85920c: ldp             fp, lr, [SP], #0x10
    // 0x859210: ret
    //     0x859210: ret             
    // 0x859214: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x859214: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x859218: b               #0x859198
  }
  _ UnboundedViewport(/* No info */) {
    // ** addr: 0xcf2de0, size: 0xf0
    // 0xcf2de0: EnterFrame
    //     0xcf2de0: stp             fp, lr, [SP, #-0x10]!
    //     0xcf2de4: mov             fp, SP
    // 0xcf2de8: r8 = Instance_CacheExtentStyle
    //     0xcf2de8: add             x8, PP, #0x45, lsl #12  ; [pp+0x45df8] Obj!CacheExtentStyle@e35341
    //     0xcf2dec: ldr             x8, [x8, #0xdf8]
    // 0xcf2df0: r4 = Instance_Clip
    //     0xcf2df0: add             x4, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xcf2df4: ldr             x4, [x4, #0x7c0]
    // 0xcf2df8: mov             x0, x2
    // 0xcf2dfc: mov             x2, x6
    // 0xcf2e00: mov             x6, x1
    // 0xcf2e04: mov             x16, x5
    // 0xcf2e08: mov             x5, x3
    // 0xcf2e0c: mov             x3, x16
    // 0xcf2e10: mov             x1, x7
    // 0xcf2e14: StoreField: r6->field_33 = d0
    //     0xcf2e14: stur            d0, [x6, #0x33]
    // 0xcf2e18: StoreField: r6->field_f = r0
    //     0xcf2e18: stur            w0, [x6, #0xf]
    //     0xcf2e1c: ldurb           w16, [x6, #-1]
    //     0xcf2e20: ldurb           w17, [x0, #-1]
    //     0xcf2e24: and             x16, x17, x16, lsr #2
    //     0xcf2e28: tst             x16, HEAP, lsr #32
    //     0xcf2e2c: b.eq            #0xcf2e34
    //     0xcf2e30: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xcf2e34: ArrayStore: r6[0] = rZR  ; List_8
    //     0xcf2e34: stur            xzr, [x6, #0x17]
    // 0xcf2e38: mov             x0, x2
    // 0xcf2e3c: StoreField: r6->field_1f = r0
    //     0xcf2e3c: stur            w0, [x6, #0x1f]
    //     0xcf2e40: ldurb           w16, [x6, #-1]
    //     0xcf2e44: ldurb           w17, [x0, #-1]
    //     0xcf2e48: and             x16, x17, x16, lsr #2
    //     0xcf2e4c: tst             x16, HEAP, lsr #32
    //     0xcf2e50: b.eq            #0xcf2e58
    //     0xcf2e54: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xcf2e58: mov             x0, x3
    // 0xcf2e5c: StoreField: r6->field_23 = r0
    //     0xcf2e5c: stur            w0, [x6, #0x23]
    //     0xcf2e60: ldurb           w16, [x6, #-1]
    //     0xcf2e64: ldurb           w17, [x0, #-1]
    //     0xcf2e68: and             x16, x17, x16, lsr #2
    //     0xcf2e6c: tst             x16, HEAP, lsr #32
    //     0xcf2e70: b.eq            #0xcf2e78
    //     0xcf2e74: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xcf2e78: mov             x0, x5
    // 0xcf2e7c: StoreField: r6->field_27 = r0
    //     0xcf2e7c: stur            w0, [x6, #0x27]
    //     0xcf2e80: ldurb           w16, [x6, #-1]
    //     0xcf2e84: ldurb           w17, [x0, #-1]
    //     0xcf2e88: and             x16, x17, x16, lsr #2
    //     0xcf2e8c: tst             x16, HEAP, lsr #32
    //     0xcf2e90: b.eq            #0xcf2e98
    //     0xcf2e94: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xcf2e98: StoreField: r6->field_2b = r8
    //     0xcf2e98: stur            w8, [x6, #0x2b]
    // 0xcf2e9c: StoreField: r6->field_2f = r4
    //     0xcf2e9c: stur            w4, [x6, #0x2f]
    // 0xcf2ea0: mov             x0, x1
    // 0xcf2ea4: StoreField: r6->field_b = r0
    //     0xcf2ea4: stur            w0, [x6, #0xb]
    //     0xcf2ea8: ldurb           w16, [x6, #-1]
    //     0xcf2eac: ldurb           w17, [x0, #-1]
    //     0xcf2eb0: and             x16, x17, x16, lsr #2
    //     0xcf2eb4: tst             x16, HEAP, lsr #32
    //     0xcf2eb8: b.eq            #0xcf2ec0
    //     0xcf2ebc: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0xcf2ec0: r0 = Null
    //     0xcf2ec0: mov             x0, NULL
    // 0xcf2ec4: LeaveFrame
    //     0xcf2ec4: mov             SP, fp
    //     0xcf2ec8: ldp             fp, lr, [SP], #0x10
    // 0xcf2ecc: ret
    //     0xcf2ecc: ret             
  }
}
