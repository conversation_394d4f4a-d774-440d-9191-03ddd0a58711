// lib: , url: package:share_plus/share_plus.dart

// class id: 1051117, size: 0x8
class :: {
}

// class id: 504, size: 0x8, field offset: 0x8
abstract class Share extends Object {

  static _ shareFiles(/* No info */) {
    // ** addr: 0xa3b8b0, size: 0xb4
    // 0xa3b8b0: EnterFrame
    //     0xa3b8b0: stp             fp, lr, [SP, #-0x10]!
    //     0xa3b8b4: mov             fp, SP
    // 0xa3b8b8: AllocStack(0x18)
    //     0xa3b8b8: sub             SP, SP, #0x18
    // 0xa3b8bc: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r6, fp-0x18 */, {dynamic sharePositionOrigin = Null /* r5, fp-0x8 */})
    //     0xa3b8bc: mov             x6, x2
    //     0xa3b8c0: stur            x2, [fp, #-0x18]
    //     0xa3b8c4: mov             x2, x1
    //     0xa3b8c8: stur            x1, [fp, #-0x10]
    //     0xa3b8cc: ldur            w0, [x4, #0x13]
    //     0xa3b8d0: ldur            w1, [x4, #0x1f]
    //     0xa3b8d4: add             x1, x1, HEAP, lsl #32
    //     0xa3b8d8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cc30] "sharePositionOrigin"
    //     0xa3b8dc: ldr             x16, [x16, #0xc30]
    //     0xa3b8e0: cmp             w1, w16
    //     0xa3b8e4: b.ne            #0xa3b904
    //     0xa3b8e8: ldur            w1, [x4, #0x23]
    //     0xa3b8ec: add             x1, x1, HEAP, lsl #32
    //     0xa3b8f0: sub             w3, w0, w1
    //     0xa3b8f4: add             x0, fp, w3, sxtw #2
    //     0xa3b8f8: ldr             x0, [x0, #8]
    //     0xa3b8fc: mov             x5, x0
    //     0xa3b900: b               #0xa3b908
    //     0xa3b904: mov             x5, NULL
    //     0xa3b908: stur            x5, [fp, #-8]
    // 0xa3b90c: CheckStackOverflow
    //     0xa3b90c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3b910: cmp             SP, x16
    //     0xa3b914: b.ls            #0xa3b95c
    // 0xa3b918: r0 = InitLateStaticField(0x1744) // [package:share_plus_platform_interface/platform_interface/share_plus_platform.dart] SharePlatform::_instance
    //     0xa3b918: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa3b91c: ldr             x0, [x0, #0x2e88]
    //     0xa3b920: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa3b924: cmp             w0, w16
    //     0xa3b928: b.ne            #0xa3b938
    //     0xa3b92c: add             x2, PP, #0x29, lsl #12  ; [pp+0x295c8] Field <SharePlatform._instance@2695348855>: static late (offset: 0x1744)
    //     0xa3b930: ldr             x2, [x2, #0x5c8]
    //     0xa3b934: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xa3b938: mov             x1, x0
    // 0xa3b93c: ldur            x2, [fp, #-0x10]
    // 0xa3b940: ldur            x5, [fp, #-8]
    // 0xa3b944: ldur            x6, [fp, #-0x18]
    // 0xa3b948: r3 = Null
    //     0xa3b948: mov             x3, NULL
    // 0xa3b94c: r0 = shareFiles()
    //     0xa3b94c: bl              #0xa3b964  ; [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::shareFiles
    // 0xa3b950: LeaveFrame
    //     0xa3b950: mov             SP, fp
    //     0xa3b954: ldp             fp, lr, [SP], #0x10
    // 0xa3b958: ret
    //     0xa3b958: ret             
    // 0xa3b95c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3b95c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3b960: b               #0xa3b918
  }
  static _ share(/* No info */) {
    // ** addr: 0xb0217c, size: 0xb0
    // 0xb0217c: EnterFrame
    //     0xb0217c: stp             fp, lr, [SP, #-0x10]!
    //     0xb02180: mov             fp, SP
    // 0xb02184: AllocStack(0x18)
    //     0xb02184: sub             SP, SP, #0x18
    // 0xb02188: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, {dynamic subject = Null /* r5, fp-0x8 */})
    //     0xb02188: mov             x3, x2
    //     0xb0218c: stur            x2, [fp, #-0x18]
    //     0xb02190: mov             x2, x1
    //     0xb02194: stur            x1, [fp, #-0x10]
    //     0xb02198: ldur            w0, [x4, #0x13]
    //     0xb0219c: ldur            w1, [x4, #0x1f]
    //     0xb021a0: add             x1, x1, HEAP, lsl #32
    //     0xb021a4: add             x16, PP, #0x29, lsl #12  ; [pp+0x295c0] "subject"
    //     0xb021a8: ldr             x16, [x16, #0x5c0]
    //     0xb021ac: cmp             w1, w16
    //     0xb021b0: b.ne            #0xb021d0
    //     0xb021b4: ldur            w1, [x4, #0x23]
    //     0xb021b8: add             x1, x1, HEAP, lsl #32
    //     0xb021bc: sub             w4, w0, w1
    //     0xb021c0: add             x0, fp, w4, sxtw #2
    //     0xb021c4: ldr             x0, [x0, #8]
    //     0xb021c8: mov             x5, x0
    //     0xb021cc: b               #0xb021d4
    //     0xb021d0: mov             x5, NULL
    //     0xb021d4: stur            x5, [fp, #-8]
    // 0xb021d8: CheckStackOverflow
    //     0xb021d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb021dc: cmp             SP, x16
    //     0xb021e0: b.ls            #0xb02224
    // 0xb021e4: r0 = InitLateStaticField(0x1744) // [package:share_plus_platform_interface/platform_interface/share_plus_platform.dart] SharePlatform::_instance
    //     0xb021e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb021e8: ldr             x0, [x0, #0x2e88]
    //     0xb021ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb021f0: cmp             w0, w16
    //     0xb021f4: b.ne            #0xb02204
    //     0xb021f8: add             x2, PP, #0x29, lsl #12  ; [pp+0x295c8] Field <SharePlatform._instance@2695348855>: static late (offset: 0x1744)
    //     0xb021fc: ldr             x2, [x2, #0x5c8]
    //     0xb02200: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xb02204: mov             x1, x0
    // 0xb02208: ldur            x2, [fp, #-0x10]
    // 0xb0220c: ldur            x3, [fp, #-0x18]
    // 0xb02210: ldur            x5, [fp, #-8]
    // 0xb02214: r0 = share()
    //     0xb02214: bl              #0xb0222c  ; [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::share
    // 0xb02218: LeaveFrame
    //     0xb02218: mov             SP, fp
    //     0xb0221c: ldp             fp, lr, [SP], #0x10
    // 0xb02220: ret
    //     0xb02220: ret             
    // 0xb02224: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb02224: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb02228: b               #0xb021e4
  }
  static _ shareXFiles(/* No info */) async {
    // ** addr: 0xb0d264, size: 0x74
    // 0xb0d264: EnterFrame
    //     0xb0d264: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d268: mov             fp, SP
    // 0xb0d26c: AllocStack(0x18)
    //     0xb0d26c: sub             SP, SP, #0x18
    // 0xb0d270: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xb0d270: stur            NULL, [fp, #-8]
    //     0xb0d274: mov             x3, x2
    //     0xb0d278: stur            x2, [fp, #-0x18]
    //     0xb0d27c: mov             x2, x1
    //     0xb0d280: stur            x1, [fp, #-0x10]
    // 0xb0d284: CheckStackOverflow
    //     0xb0d284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d288: cmp             SP, x16
    //     0xb0d28c: b.ls            #0xb0d2d0
    // 0xb0d290: InitAsync() -> Future<ShareResult>
    //     0xb0d290: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2df48] TypeArguments: <ShareResult>
    //     0xb0d294: ldr             x0, [x0, #0xf48]
    //     0xb0d298: bl              #0x661298  ; InitAsyncStub
    // 0xb0d29c: r0 = InitLateStaticField(0x1744) // [package:share_plus_platform_interface/platform_interface/share_plus_platform.dart] SharePlatform::_instance
    //     0xb0d29c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb0d2a0: ldr             x0, [x0, #0x2e88]
    //     0xb0d2a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb0d2a8: cmp             w0, w16
    //     0xb0d2ac: b.ne            #0xb0d2bc
    //     0xb0d2b0: add             x2, PP, #0x29, lsl #12  ; [pp+0x295c8] Field <SharePlatform._instance@2695348855>: static late (offset: 0x1744)
    //     0xb0d2b4: ldr             x2, [x2, #0x5c8]
    //     0xb0d2b8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xb0d2bc: mov             x1, x0
    // 0xb0d2c0: ldur            x2, [fp, #-0x10]
    // 0xb0d2c4: ldur            x3, [fp, #-0x18]
    // 0xb0d2c8: r0 = shareXFiles()
    //     0xb0d2c8: bl              #0xb0d2d8  ; [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::shareXFiles
    // 0xb0d2cc: r0 = ReturnAsync()
    //     0xb0d2cc: b               #0x6576a4  ; ReturnAsyncStub
    // 0xb0d2d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0d2d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0d2d4: b               #0xb0d290
  }
}
