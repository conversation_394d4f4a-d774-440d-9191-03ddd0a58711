// lib: , url: package:share_plus_platform_interface/method_channel/method_channel_share.dart

// class id: 1051118, size: 0x8
class :: {
}

// class id: 5868, size: 0x8, field offset: 0x8
class MethodChannelShare extends SharePlatform {

  _ shareFiles(/* No info */) {
    // ** addr: 0xa3b964, size: 0x2a8
    // 0xa3b964: EnterFrame
    //     0xa3b964: stp             fp, lr, [SP, #-0x10]!
    //     0xa3b968: mov             fp, SP
    // 0xa3b96c: AllocStack(0x50)
    //     0xa3b96c: sub             SP, SP, #0x50
    // 0xa3b970: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0 */, dynamic _ /* r5 => r5, fp-0x10 */, dynamic _ /* r6 => r3, fp-0x18 */)
    //     0xa3b970: mov             x4, x2
    //     0xa3b974: mov             x0, x3
    //     0xa3b978: mov             x3, x6
    //     0xa3b97c: stur            x2, [fp, #-8]
    //     0xa3b980: stur            x5, [fp, #-0x10]
    //     0xa3b984: stur            x6, [fp, #-0x18]
    // 0xa3b988: CheckStackOverflow
    //     0xa3b988: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3b98c: cmp             SP, x16
    //     0xa3b990: b.ls            #0xa3bb9c
    // 0xa3b994: r1 = Null
    //     0xa3b994: mov             x1, NULL
    // 0xa3b998: r2 = 8
    //     0xa3b998: movz            x2, #0x8
    // 0xa3b99c: r0 = AllocateArray()
    //     0xa3b99c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3b9a0: stur            x0, [fp, #-0x20]
    // 0xa3b9a4: r16 = "paths"
    //     0xa3b9a4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cc38] "paths"
    //     0xa3b9a8: ldr             x16, [x16, #0xc38]
    // 0xa3b9ac: StoreField: r0->field_f = r16
    //     0xa3b9ac: stur            w16, [x0, #0xf]
    // 0xa3b9b0: ldur            x3, [fp, #-8]
    // 0xa3b9b4: StoreField: r0->field_13 = r3
    //     0xa3b9b4: stur            w3, [x0, #0x13]
    // 0xa3b9b8: r16 = "mimeTypes"
    //     0xa3b9b8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cc40] "mimeTypes"
    //     0xa3b9bc: ldr             x16, [x16, #0xc40]
    // 0xa3b9c0: ArrayStore: r0[0] = r16  ; List_4
    //     0xa3b9c0: stur            w16, [x0, #0x17]
    // 0xa3b9c4: r1 = Function '<anonymous closure>':.
    //     0xa3b9c4: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cc48] AnonymousClosure: (0xa3bc0c), in [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::shareFiles (0xa3b964)
    //     0xa3b9c8: ldr             x1, [x1, #0xc48]
    // 0xa3b9cc: r2 = Null
    //     0xa3b9cc: mov             x2, NULL
    // 0xa3b9d0: r0 = AllocateClosure()
    //     0xa3b9d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa3b9d4: r16 = <String>
    //     0xa3b9d4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa3b9d8: ldur            lr, [fp, #-8]
    // 0xa3b9dc: stp             lr, x16, [SP, #8]
    // 0xa3b9e0: str             x0, [SP]
    // 0xa3b9e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa3b9e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa3b9e8: r0 = map()
    //     0xa3b9e8: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xa3b9ec: LoadField: r1 = r0->field_7
    //     0xa3b9ec: ldur            w1, [x0, #7]
    // 0xa3b9f0: DecompressPointer r1
    //     0xa3b9f0: add             x1, x1, HEAP, lsl #32
    // 0xa3b9f4: mov             x2, x0
    // 0xa3b9f8: r0 = _GrowableList.of()
    //     0xa3b9f8: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa3b9fc: ldur            x1, [fp, #-0x20]
    // 0xa3ba00: ArrayStore: r1[3] = r0  ; List_4
    //     0xa3ba00: add             x25, x1, #0x1b
    //     0xa3ba04: str             w0, [x25]
    //     0xa3ba08: tbz             w0, #0, #0xa3ba24
    //     0xa3ba0c: ldurb           w16, [x1, #-1]
    //     0xa3ba10: ldurb           w17, [x0, #-1]
    //     0xa3ba14: and             x16, x17, x16, lsr #2
    //     0xa3ba18: tst             x16, HEAP, lsr #32
    //     0xa3ba1c: b.eq            #0xa3ba24
    //     0xa3ba20: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa3ba24: r16 = <String, dynamic>
    //     0xa3ba24: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xa3ba28: ldur            lr, [fp, #-0x20]
    // 0xa3ba2c: stp             lr, x16, [SP]
    // 0xa3ba30: r0 = Map._fromLiteral()
    //     0xa3ba30: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa3ba34: mov             x1, x0
    // 0xa3ba38: ldur            x3, [fp, #-0x18]
    // 0xa3ba3c: r2 = "text"
    //     0xa3ba3c: ldr             x2, [PP, #0x7060]  ; [pp+0x7060] "text"
    // 0xa3ba40: stur            x0, [fp, #-8]
    // 0xa3ba44: r0 = []=()
    //     0xa3ba44: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa3ba48: ldur            x0, [fp, #-0x10]
    // 0xa3ba4c: cmp             w0, NULL
    // 0xa3ba50: b.eq            #0xa3bb68
    // 0xa3ba54: LoadField: d0 = r0->field_7
    //     0xa3ba54: ldur            d0, [x0, #7]
    // 0xa3ba58: stur            d0, [fp, #-0x28]
    // 0xa3ba5c: r3 = inline_Allocate_Double()
    //     0xa3ba5c: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xa3ba60: add             x3, x3, #0x10
    //     0xa3ba64: cmp             x1, x3
    //     0xa3ba68: b.ls            #0xa3bba4
    //     0xa3ba6c: str             x3, [THR, #0x50]  ; THR::top
    //     0xa3ba70: sub             x3, x3, #0xf
    //     0xa3ba74: movz            x1, #0xe15c
    //     0xa3ba78: movk            x1, #0x3, lsl #16
    //     0xa3ba7c: stur            x1, [x3, #-1]
    // 0xa3ba80: StoreField: r3->field_7 = d0
    //     0xa3ba80: stur            d0, [x3, #7]
    // 0xa3ba84: ldur            x1, [fp, #-8]
    // 0xa3ba88: r2 = "originX"
    //     0xa3ba88: add             x2, PP, #0x29, lsl #12  ; [pp+0x295d0] "originX"
    //     0xa3ba8c: ldr             x2, [x2, #0x5d0]
    // 0xa3ba90: r0 = []=()
    //     0xa3ba90: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa3ba94: ldur            x0, [fp, #-0x10]
    // 0xa3ba98: LoadField: d0 = r0->field_f
    //     0xa3ba98: ldur            d0, [x0, #0xf]
    // 0xa3ba9c: stur            d0, [fp, #-0x30]
    // 0xa3baa0: r3 = inline_Allocate_Double()
    //     0xa3baa0: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xa3baa4: add             x3, x3, #0x10
    //     0xa3baa8: cmp             x1, x3
    //     0xa3baac: b.ls            #0xa3bbc0
    //     0xa3bab0: str             x3, [THR, #0x50]  ; THR::top
    //     0xa3bab4: sub             x3, x3, #0xf
    //     0xa3bab8: movz            x1, #0xe15c
    //     0xa3babc: movk            x1, #0x3, lsl #16
    //     0xa3bac0: stur            x1, [x3, #-1]
    // 0xa3bac4: StoreField: r3->field_7 = d0
    //     0xa3bac4: stur            d0, [x3, #7]
    // 0xa3bac8: ldur            x1, [fp, #-8]
    // 0xa3bacc: r2 = "originY"
    //     0xa3bacc: add             x2, PP, #0x29, lsl #12  ; [pp+0x295d8] "originY"
    //     0xa3bad0: ldr             x2, [x2, #0x5d8]
    // 0xa3bad4: r0 = []=()
    //     0xa3bad4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa3bad8: ldur            x0, [fp, #-0x10]
    // 0xa3badc: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xa3badc: ldur            d0, [x0, #0x17]
    // 0xa3bae0: ldur            d1, [fp, #-0x28]
    // 0xa3bae4: fsub            d2, d0, d1
    // 0xa3bae8: r3 = inline_Allocate_Double()
    //     0xa3bae8: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xa3baec: add             x3, x3, #0x10
    //     0xa3baf0: cmp             x1, x3
    //     0xa3baf4: b.ls            #0xa3bbdc
    //     0xa3baf8: str             x3, [THR, #0x50]  ; THR::top
    //     0xa3bafc: sub             x3, x3, #0xf
    //     0xa3bb00: movz            x1, #0xe15c
    //     0xa3bb04: movk            x1, #0x3, lsl #16
    //     0xa3bb08: stur            x1, [x3, #-1]
    // 0xa3bb0c: StoreField: r3->field_7 = d2
    //     0xa3bb0c: stur            d2, [x3, #7]
    // 0xa3bb10: ldur            x1, [fp, #-8]
    // 0xa3bb14: r2 = "originWidth"
    //     0xa3bb14: add             x2, PP, #0x29, lsl #12  ; [pp+0x295e0] "originWidth"
    //     0xa3bb18: ldr             x2, [x2, #0x5e0]
    // 0xa3bb1c: r0 = []=()
    //     0xa3bb1c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa3bb20: ldur            x0, [fp, #-0x10]
    // 0xa3bb24: LoadField: d0 = r0->field_1f
    //     0xa3bb24: ldur            d0, [x0, #0x1f]
    // 0xa3bb28: ldur            d1, [fp, #-0x30]
    // 0xa3bb2c: fsub            d2, d0, d1
    // 0xa3bb30: r3 = inline_Allocate_Double()
    //     0xa3bb30: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xa3bb34: add             x3, x3, #0x10
    //     0xa3bb38: cmp             x0, x3
    //     0xa3bb3c: b.ls            #0xa3bbf8
    //     0xa3bb40: str             x3, [THR, #0x50]  ; THR::top
    //     0xa3bb44: sub             x3, x3, #0xf
    //     0xa3bb48: movz            x0, #0xe15c
    //     0xa3bb4c: movk            x0, #0x3, lsl #16
    //     0xa3bb50: stur            x0, [x3, #-1]
    // 0xa3bb54: StoreField: r3->field_7 = d2
    //     0xa3bb54: stur            d2, [x3, #7]
    // 0xa3bb58: ldur            x1, [fp, #-8]
    // 0xa3bb5c: r2 = "originHeight"
    //     0xa3bb5c: add             x2, PP, #0x29, lsl #12  ; [pp+0x295e8] "originHeight"
    //     0xa3bb60: ldr             x2, [x2, #0x5e8]
    // 0xa3bb64: r0 = []=()
    //     0xa3bb64: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa3bb68: r16 = <void?>
    //     0xa3bb68: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xa3bb6c: r30 = Instance_MethodChannel
    //     0xa3bb6c: add             lr, PP, #0x29, lsl #12  ; [pp+0x295f0] Obj!MethodChannel@e112b1
    //     0xa3bb70: ldr             lr, [lr, #0x5f0]
    // 0xa3bb74: stp             lr, x16, [SP, #0x10]
    // 0xa3bb78: r16 = "shareFiles"
    //     0xa3bb78: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cc50] "shareFiles"
    //     0xa3bb7c: ldr             x16, [x16, #0xc50]
    // 0xa3bb80: ldur            lr, [fp, #-8]
    // 0xa3bb84: stp             lr, x16, [SP]
    // 0xa3bb88: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xa3bb88: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xa3bb8c: r0 = invokeMethod()
    //     0xa3bb8c: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xa3bb90: LeaveFrame
    //     0xa3bb90: mov             SP, fp
    //     0xa3bb94: ldp             fp, lr, [SP], #0x10
    // 0xa3bb98: ret
    //     0xa3bb98: ret             
    // 0xa3bb9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3bb9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3bba0: b               #0xa3b994
    // 0xa3bba4: SaveReg d0
    //     0xa3bba4: str             q0, [SP, #-0x10]!
    // 0xa3bba8: SaveReg r0
    //     0xa3bba8: str             x0, [SP, #-8]!
    // 0xa3bbac: r0 = AllocateDouble()
    //     0xa3bbac: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa3bbb0: mov             x3, x0
    // 0xa3bbb4: RestoreReg r0
    //     0xa3bbb4: ldr             x0, [SP], #8
    // 0xa3bbb8: RestoreReg d0
    //     0xa3bbb8: ldr             q0, [SP], #0x10
    // 0xa3bbbc: b               #0xa3ba80
    // 0xa3bbc0: SaveReg d0
    //     0xa3bbc0: str             q0, [SP, #-0x10]!
    // 0xa3bbc4: SaveReg r0
    //     0xa3bbc4: str             x0, [SP, #-8]!
    // 0xa3bbc8: r0 = AllocateDouble()
    //     0xa3bbc8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa3bbcc: mov             x3, x0
    // 0xa3bbd0: RestoreReg r0
    //     0xa3bbd0: ldr             x0, [SP], #8
    // 0xa3bbd4: RestoreReg d0
    //     0xa3bbd4: ldr             q0, [SP], #0x10
    // 0xa3bbd8: b               #0xa3bac4
    // 0xa3bbdc: SaveReg d2
    //     0xa3bbdc: str             q2, [SP, #-0x10]!
    // 0xa3bbe0: SaveReg r0
    //     0xa3bbe0: str             x0, [SP, #-8]!
    // 0xa3bbe4: r0 = AllocateDouble()
    //     0xa3bbe4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa3bbe8: mov             x3, x0
    // 0xa3bbec: RestoreReg r0
    //     0xa3bbec: ldr             x0, [SP], #8
    // 0xa3bbf0: RestoreReg d2
    //     0xa3bbf0: ldr             q2, [SP], #0x10
    // 0xa3bbf4: b               #0xa3bb0c
    // 0xa3bbf8: SaveReg d2
    //     0xa3bbf8: str             q2, [SP, #-0x10]!
    // 0xa3bbfc: r0 = AllocateDouble()
    //     0xa3bbfc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa3bc00: mov             x3, x0
    // 0xa3bc04: RestoreReg d2
    //     0xa3bc04: ldr             q2, [SP], #0x10
    // 0xa3bc08: b               #0xa3bb54
  }
  [closure] String <anonymous closure>(dynamic, String) {
    // ** addr: 0xa3bc0c, size: 0x30
    // 0xa3bc0c: EnterFrame
    //     0xa3bc0c: stp             fp, lr, [SP, #-0x10]!
    //     0xa3bc10: mov             fp, SP
    // 0xa3bc14: CheckStackOverflow
    //     0xa3bc14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3bc18: cmp             SP, x16
    //     0xa3bc1c: b.ls            #0xa3bc34
    // 0xa3bc20: ldr             x1, [fp, #0x10]
    // 0xa3bc24: r0 = _mimeTypeForPath()
    //     0xa3bc24: bl              #0xa3bc3c  ; [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::_mimeTypeForPath
    // 0xa3bc28: LeaveFrame
    //     0xa3bc28: mov             SP, fp
    //     0xa3bc2c: ldp             fp, lr, [SP], #0x10
    // 0xa3bc30: ret
    //     0xa3bc30: ret             
    // 0xa3bc34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3bc34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3bc38: b               #0xa3bc20
  }
  static _ _mimeTypeForPath(/* No info */) {
    // ** addr: 0xa3bc3c, size: 0x3c
    // 0xa3bc3c: EnterFrame
    //     0xa3bc3c: stp             fp, lr, [SP, #-0x10]!
    //     0xa3bc40: mov             fp, SP
    // 0xa3bc44: CheckStackOverflow
    //     0xa3bc44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3bc48: cmp             SP, x16
    //     0xa3bc4c: b.ls            #0xa3bc70
    // 0xa3bc50: r0 = lookupMimeType()
    //     0xa3bc50: bl              #0xa3bc78  ; [package:mime/src/mime_type.dart] ::lookupMimeType
    // 0xa3bc54: cmp             w0, NULL
    // 0xa3bc58: b.ne            #0xa3bc64
    // 0xa3bc5c: r0 = "application/octet-stream"
    //     0xa3bc5c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cc58] "application/octet-stream"
    //     0xa3bc60: ldr             x0, [x0, #0xc58]
    // 0xa3bc64: LeaveFrame
    //     0xa3bc64: mov             SP, fp
    //     0xa3bc68: ldp             fp, lr, [SP], #0x10
    // 0xa3bc6c: ret
    //     0xa3bc6c: ret             
    // 0xa3bc70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3bc70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3bc74: b               #0xa3bc50
  }
  _ share(/* No info */) {
    // ** addr: 0xb0222c, size: 0x228
    // 0xb0222c: EnterFrame
    //     0xb0222c: stp             fp, lr, [SP, #-0x10]!
    //     0xb02230: mov             fp, SP
    // 0xb02234: AllocStack(0x48)
    //     0xb02234: sub             SP, SP, #0x48
    // 0xb02238: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0xb02238: mov             x0, x2
    //     0xb0223c: stur            x2, [fp, #-8]
    //     0xb02240: stur            x3, [fp, #-0x10]
    //     0xb02244: stur            x5, [fp, #-0x18]
    // 0xb02248: CheckStackOverflow
    //     0xb02248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0224c: cmp             SP, x16
    //     0xb02250: b.ls            #0xb023e4
    // 0xb02254: r1 = Null
    //     0xb02254: mov             x1, NULL
    // 0xb02258: r2 = 8
    //     0xb02258: movz            x2, #0x8
    // 0xb0225c: r0 = AllocateArray()
    //     0xb0225c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb02260: r16 = "text"
    //     0xb02260: ldr             x16, [PP, #0x7060]  ; [pp+0x7060] "text"
    // 0xb02264: StoreField: r0->field_f = r16
    //     0xb02264: stur            w16, [x0, #0xf]
    // 0xb02268: ldur            x1, [fp, #-8]
    // 0xb0226c: StoreField: r0->field_13 = r1
    //     0xb0226c: stur            w1, [x0, #0x13]
    // 0xb02270: r16 = "subject"
    //     0xb02270: add             x16, PP, #0x29, lsl #12  ; [pp+0x295c0] "subject"
    //     0xb02274: ldr             x16, [x16, #0x5c0]
    // 0xb02278: ArrayStore: r0[0] = r16  ; List_4
    //     0xb02278: stur            w16, [x0, #0x17]
    // 0xb0227c: ldur            x1, [fp, #-0x18]
    // 0xb02280: StoreField: r0->field_1b = r1
    //     0xb02280: stur            w1, [x0, #0x1b]
    // 0xb02284: r16 = <String, dynamic>
    //     0xb02284: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xb02288: stp             x0, x16, [SP]
    // 0xb0228c: r0 = Map._fromLiteral()
    //     0xb0228c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb02290: mov             x4, x0
    // 0xb02294: ldur            x0, [fp, #-0x10]
    // 0xb02298: stur            x4, [fp, #-8]
    // 0xb0229c: LoadField: d0 = r0->field_7
    //     0xb0229c: ldur            d0, [x0, #7]
    // 0xb022a0: stur            d0, [fp, #-0x20]
    // 0xb022a4: r3 = inline_Allocate_Double()
    //     0xb022a4: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xb022a8: add             x3, x3, #0x10
    //     0xb022ac: cmp             x1, x3
    //     0xb022b0: b.ls            #0xb023ec
    //     0xb022b4: str             x3, [THR, #0x50]  ; THR::top
    //     0xb022b8: sub             x3, x3, #0xf
    //     0xb022bc: movz            x1, #0xe15c
    //     0xb022c0: movk            x1, #0x3, lsl #16
    //     0xb022c4: stur            x1, [x3, #-1]
    // 0xb022c8: StoreField: r3->field_7 = d0
    //     0xb022c8: stur            d0, [x3, #7]
    // 0xb022cc: mov             x1, x4
    // 0xb022d0: r2 = "originX"
    //     0xb022d0: add             x2, PP, #0x29, lsl #12  ; [pp+0x295d0] "originX"
    //     0xb022d4: ldr             x2, [x2, #0x5d0]
    // 0xb022d8: r0 = []=()
    //     0xb022d8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb022dc: ldur            x0, [fp, #-0x10]
    // 0xb022e0: LoadField: d0 = r0->field_f
    //     0xb022e0: ldur            d0, [x0, #0xf]
    // 0xb022e4: stur            d0, [fp, #-0x28]
    // 0xb022e8: r3 = inline_Allocate_Double()
    //     0xb022e8: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xb022ec: add             x3, x3, #0x10
    //     0xb022f0: cmp             x1, x3
    //     0xb022f4: b.ls            #0xb02408
    //     0xb022f8: str             x3, [THR, #0x50]  ; THR::top
    //     0xb022fc: sub             x3, x3, #0xf
    //     0xb02300: movz            x1, #0xe15c
    //     0xb02304: movk            x1, #0x3, lsl #16
    //     0xb02308: stur            x1, [x3, #-1]
    // 0xb0230c: StoreField: r3->field_7 = d0
    //     0xb0230c: stur            d0, [x3, #7]
    // 0xb02310: ldur            x1, [fp, #-8]
    // 0xb02314: r2 = "originY"
    //     0xb02314: add             x2, PP, #0x29, lsl #12  ; [pp+0x295d8] "originY"
    //     0xb02318: ldr             x2, [x2, #0x5d8]
    // 0xb0231c: r0 = []=()
    //     0xb0231c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb02320: ldur            x0, [fp, #-0x10]
    // 0xb02324: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xb02324: ldur            d0, [x0, #0x17]
    // 0xb02328: ldur            d1, [fp, #-0x20]
    // 0xb0232c: fsub            d2, d0, d1
    // 0xb02330: r3 = inline_Allocate_Double()
    //     0xb02330: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xb02334: add             x3, x3, #0x10
    //     0xb02338: cmp             x1, x3
    //     0xb0233c: b.ls            #0xb02424
    //     0xb02340: str             x3, [THR, #0x50]  ; THR::top
    //     0xb02344: sub             x3, x3, #0xf
    //     0xb02348: movz            x1, #0xe15c
    //     0xb0234c: movk            x1, #0x3, lsl #16
    //     0xb02350: stur            x1, [x3, #-1]
    // 0xb02354: StoreField: r3->field_7 = d2
    //     0xb02354: stur            d2, [x3, #7]
    // 0xb02358: ldur            x1, [fp, #-8]
    // 0xb0235c: r2 = "originWidth"
    //     0xb0235c: add             x2, PP, #0x29, lsl #12  ; [pp+0x295e0] "originWidth"
    //     0xb02360: ldr             x2, [x2, #0x5e0]
    // 0xb02364: r0 = []=()
    //     0xb02364: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb02368: ldur            x0, [fp, #-0x10]
    // 0xb0236c: LoadField: d0 = r0->field_1f
    //     0xb0236c: ldur            d0, [x0, #0x1f]
    // 0xb02370: ldur            d1, [fp, #-0x28]
    // 0xb02374: fsub            d2, d0, d1
    // 0xb02378: r3 = inline_Allocate_Double()
    //     0xb02378: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xb0237c: add             x3, x3, #0x10
    //     0xb02380: cmp             x0, x3
    //     0xb02384: b.ls            #0xb02440
    //     0xb02388: str             x3, [THR, #0x50]  ; THR::top
    //     0xb0238c: sub             x3, x3, #0xf
    //     0xb02390: movz            x0, #0xe15c
    //     0xb02394: movk            x0, #0x3, lsl #16
    //     0xb02398: stur            x0, [x3, #-1]
    // 0xb0239c: StoreField: r3->field_7 = d2
    //     0xb0239c: stur            d2, [x3, #7]
    // 0xb023a0: ldur            x1, [fp, #-8]
    // 0xb023a4: r2 = "originHeight"
    //     0xb023a4: add             x2, PP, #0x29, lsl #12  ; [pp+0x295e8] "originHeight"
    //     0xb023a8: ldr             x2, [x2, #0x5e8]
    // 0xb023ac: r0 = []=()
    //     0xb023ac: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb023b0: r16 = <void?>
    //     0xb023b0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xb023b4: r30 = Instance_MethodChannel
    //     0xb023b4: add             lr, PP, #0x29, lsl #12  ; [pp+0x295f0] Obj!MethodChannel@e112b1
    //     0xb023b8: ldr             lr, [lr, #0x5f0]
    // 0xb023bc: stp             lr, x16, [SP, #0x10]
    // 0xb023c0: r16 = "share"
    //     0xb023c0: add             x16, PP, #0x28, lsl #12  ; [pp+0x284c0] "share"
    //     0xb023c4: ldr             x16, [x16, #0x4c0]
    // 0xb023c8: ldur            lr, [fp, #-8]
    // 0xb023cc: stp             lr, x16, [SP]
    // 0xb023d0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xb023d0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xb023d4: r0 = invokeMethod()
    //     0xb023d4: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xb023d8: LeaveFrame
    //     0xb023d8: mov             SP, fp
    //     0xb023dc: ldp             fp, lr, [SP], #0x10
    // 0xb023e0: ret
    //     0xb023e0: ret             
    // 0xb023e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb023e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb023e8: b               #0xb02254
    // 0xb023ec: SaveReg d0
    //     0xb023ec: str             q0, [SP, #-0x10]!
    // 0xb023f0: stp             x0, x4, [SP, #-0x10]!
    // 0xb023f4: r0 = AllocateDouble()
    //     0xb023f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb023f8: mov             x3, x0
    // 0xb023fc: ldp             x0, x4, [SP], #0x10
    // 0xb02400: RestoreReg d0
    //     0xb02400: ldr             q0, [SP], #0x10
    // 0xb02404: b               #0xb022c8
    // 0xb02408: SaveReg d0
    //     0xb02408: str             q0, [SP, #-0x10]!
    // 0xb0240c: SaveReg r0
    //     0xb0240c: str             x0, [SP, #-8]!
    // 0xb02410: r0 = AllocateDouble()
    //     0xb02410: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb02414: mov             x3, x0
    // 0xb02418: RestoreReg r0
    //     0xb02418: ldr             x0, [SP], #8
    // 0xb0241c: RestoreReg d0
    //     0xb0241c: ldr             q0, [SP], #0x10
    // 0xb02420: b               #0xb0230c
    // 0xb02424: SaveReg d2
    //     0xb02424: str             q2, [SP, #-0x10]!
    // 0xb02428: SaveReg r0
    //     0xb02428: str             x0, [SP, #-8]!
    // 0xb0242c: r0 = AllocateDouble()
    //     0xb0242c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb02430: mov             x3, x0
    // 0xb02434: RestoreReg r0
    //     0xb02434: ldr             x0, [SP], #8
    // 0xb02438: RestoreReg d2
    //     0xb02438: ldr             q2, [SP], #0x10
    // 0xb0243c: b               #0xb02354
    // 0xb02440: SaveReg d2
    //     0xb02440: str             q2, [SP, #-0x10]!
    // 0xb02444: r0 = AllocateDouble()
    //     0xb02444: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb02448: mov             x3, x0
    // 0xb0244c: RestoreReg d2
    //     0xb0244c: ldr             q2, [SP], #0x10
    // 0xb02450: b               #0xb0239c
  }
  _ shareXFiles(/* No info */) async {
    // ** addr: 0xb0d2d8, size: 0x14c
    // 0xb0d2d8: EnterFrame
    //     0xb0d2d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d2dc: mov             fp, SP
    // 0xb0d2e0: AllocStack(0x40)
    //     0xb0d2e0: sub             SP, SP, #0x40
    // 0xb0d2e4: SetupParameters(MethodChannelShare this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r5, fp-0x20 */)
    //     0xb0d2e4: stur            NULL, [fp, #-8]
    //     0xb0d2e8: mov             x5, x3
    //     0xb0d2ec: stur            x1, [fp, #-0x10]
    //     0xb0d2f0: stur            x2, [fp, #-0x18]
    //     0xb0d2f4: stur            x3, [fp, #-0x20]
    // 0xb0d2f8: CheckStackOverflow
    //     0xb0d2f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d2fc: cmp             SP, x16
    //     0xb0d300: b.ls            #0xb0d41c
    // 0xb0d304: InitAsync() -> Future<ShareResult>
    //     0xb0d304: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2df48] TypeArguments: <ShareResult>
    //     0xb0d308: ldr             x0, [x0, #0xf48]
    //     0xb0d30c: bl              #0x661298  ; InitAsyncStub
    // 0xb0d310: ldur            x1, [fp, #-0x10]
    // 0xb0d314: ldur            x2, [fp, #-0x18]
    // 0xb0d318: r0 = _getFiles()
    //     0xb0d318: bl              #0xb0d6dc  ; [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::_getFiles
    // 0xb0d31c: mov             x1, x0
    // 0xb0d320: stur            x1, [fp, #-0x18]
    // 0xb0d324: r0 = Await()
    //     0xb0d324: bl              #0x661044  ; AwaitStub
    // 0xb0d328: r1 = Function '<anonymous closure>':.
    //     0xb0d328: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2df50] AnonymousClosure: (0xb0ddb4), in [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::shareXFiles (0xb0d2d8)
    //     0xb0d32c: ldr             x1, [x1, #0xf50]
    // 0xb0d330: r2 = Null
    //     0xb0d330: mov             x2, NULL
    // 0xb0d334: stur            x0, [fp, #-0x18]
    // 0xb0d338: r0 = AllocateClosure()
    //     0xb0d338: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0d33c: ldur            x1, [fp, #-0x18]
    // 0xb0d340: r2 = LoadClassIdInstr(r1)
    //     0xb0d340: ldur            x2, [x1, #-1]
    //     0xb0d344: ubfx            x2, x2, #0xc, #0x14
    // 0xb0d348: r16 = <String>
    //     0xb0d348: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0d34c: stp             x1, x16, [SP, #8]
    // 0xb0d350: str             x0, [SP]
    // 0xb0d354: mov             x0, x2
    // 0xb0d358: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb0d358: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb0d35c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xb0d35c: movz            x17, #0xf28c
    //     0xb0d360: add             lr, x0, x17
    //     0xb0d364: ldr             lr, [x21, lr, lsl #3]
    //     0xb0d368: blr             lr
    // 0xb0d36c: r1 = LoadClassIdInstr(r0)
    //     0xb0d36c: ldur            x1, [x0, #-1]
    //     0xb0d370: ubfx            x1, x1, #0xc, #0x14
    // 0xb0d374: mov             x16, x0
    // 0xb0d378: mov             x0, x1
    // 0xb0d37c: mov             x1, x16
    // 0xb0d380: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0d380: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0d384: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb0d384: movz            x17, #0xd889
    //     0xb0d388: add             lr, x0, x17
    //     0xb0d38c: ldr             lr, [x21, lr, lsl #3]
    //     0xb0d390: blr             lr
    // 0xb0d394: r1 = Function '<anonymous closure>':.
    //     0xb0d394: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2df58] AnonymousClosure: (0xb0dd84), in [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::shareXFiles (0xb0d2d8)
    //     0xb0d398: ldr             x1, [x1, #0xf58]
    // 0xb0d39c: r2 = Null
    //     0xb0d39c: mov             x2, NULL
    // 0xb0d3a0: stur            x0, [fp, #-0x28]
    // 0xb0d3a4: r0 = AllocateClosure()
    //     0xb0d3a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0d3a8: mov             x1, x0
    // 0xb0d3ac: ldur            x0, [fp, #-0x18]
    // 0xb0d3b0: r2 = LoadClassIdInstr(r0)
    //     0xb0d3b0: ldur            x2, [x0, #-1]
    //     0xb0d3b4: ubfx            x2, x2, #0xc, #0x14
    // 0xb0d3b8: r16 = <String>
    //     0xb0d3b8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0d3bc: stp             x0, x16, [SP, #8]
    // 0xb0d3c0: str             x1, [SP]
    // 0xb0d3c4: mov             x0, x2
    // 0xb0d3c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb0d3c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb0d3cc: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xb0d3cc: movz            x17, #0xf28c
    //     0xb0d3d0: add             lr, x0, x17
    //     0xb0d3d4: ldr             lr, [x21, lr, lsl #3]
    //     0xb0d3d8: blr             lr
    // 0xb0d3dc: r1 = LoadClassIdInstr(r0)
    //     0xb0d3dc: ldur            x1, [x0, #-1]
    //     0xb0d3e0: ubfx            x1, x1, #0xc, #0x14
    // 0xb0d3e4: mov             x16, x0
    // 0xb0d3e8: mov             x0, x1
    // 0xb0d3ec: mov             x1, x16
    // 0xb0d3f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0d3f0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0d3f4: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb0d3f4: movz            x17, #0xd889
    //     0xb0d3f8: add             lr, x0, x17
    //     0xb0d3fc: ldr             lr, [x21, lr, lsl #3]
    //     0xb0d400: blr             lr
    // 0xb0d404: ldur            x1, [fp, #-0x10]
    // 0xb0d408: mov             x2, x0
    // 0xb0d40c: ldur            x3, [fp, #-0x28]
    // 0xb0d410: ldur            x5, [fp, #-0x20]
    // 0xb0d414: r0 = shareFilesWithResult()
    //     0xb0d414: bl              #0xb0d424  ; [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::shareFilesWithResult
    // 0xb0d418: r0 = ReturnAsync()
    //     0xb0d418: b               #0x6576a4  ; ReturnAsyncStub
    // 0xb0d41c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0d41c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0d420: b               #0xb0d304
  }
  _ shareFilesWithResult(/* No info */) async {
    // ** addr: 0xb0d424, size: 0x2ac
    // 0xb0d424: EnterFrame
    //     0xb0d424: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d428: mov             fp, SP
    // 0xb0d42c: AllocStack(0x58)
    //     0xb0d42c: sub             SP, SP, #0x58
    // 0xb0d430: SetupParameters(MethodChannelShare this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */)
    //     0xb0d430: stur            NULL, [fp, #-8]
    //     0xb0d434: stur            x1, [fp, #-0x10]
    //     0xb0d438: stur            x2, [fp, #-0x18]
    //     0xb0d43c: stur            x3, [fp, #-0x20]
    //     0xb0d440: stur            x5, [fp, #-0x28]
    // 0xb0d444: CheckStackOverflow
    //     0xb0d444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d448: cmp             SP, x16
    //     0xb0d44c: b.ls            #0xb0d660
    // 0xb0d450: InitAsync() -> Future<ShareResult>
    //     0xb0d450: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2df48] TypeArguments: <ShareResult>
    //     0xb0d454: ldr             x0, [x0, #0xf48]
    //     0xb0d458: bl              #0x661298  ; InitAsyncStub
    // 0xb0d45c: r1 = Null
    //     0xb0d45c: mov             x1, NULL
    // 0xb0d460: r2 = 8
    //     0xb0d460: movz            x2, #0x8
    // 0xb0d464: r0 = AllocateArray()
    //     0xb0d464: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0d468: r16 = "paths"
    //     0xb0d468: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cc38] "paths"
    //     0xb0d46c: ldr             x16, [x16, #0xc38]
    // 0xb0d470: StoreField: r0->field_f = r16
    //     0xb0d470: stur            w16, [x0, #0xf]
    // 0xb0d474: ldur            x1, [fp, #-0x18]
    // 0xb0d478: StoreField: r0->field_13 = r1
    //     0xb0d478: stur            w1, [x0, #0x13]
    // 0xb0d47c: r16 = "mimeTypes"
    //     0xb0d47c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cc40] "mimeTypes"
    //     0xb0d480: ldr             x16, [x16, #0xc40]
    // 0xb0d484: ArrayStore: r0[0] = r16  ; List_4
    //     0xb0d484: stur            w16, [x0, #0x17]
    // 0xb0d488: ldur            x1, [fp, #-0x20]
    // 0xb0d48c: StoreField: r0->field_1b = r1
    //     0xb0d48c: stur            w1, [x0, #0x1b]
    // 0xb0d490: r16 = <String, dynamic>
    //     0xb0d490: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xb0d494: stp             x0, x16, [SP]
    // 0xb0d498: r0 = Map._fromLiteral()
    //     0xb0d498: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb0d49c: mov             x4, x0
    // 0xb0d4a0: ldur            x0, [fp, #-0x28]
    // 0xb0d4a4: stur            x4, [fp, #-0x18]
    // 0xb0d4a8: LoadField: d0 = r0->field_7
    //     0xb0d4a8: ldur            d0, [x0, #7]
    // 0xb0d4ac: stur            d0, [fp, #-0x30]
    // 0xb0d4b0: r3 = inline_Allocate_Double()
    //     0xb0d4b0: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xb0d4b4: add             x3, x3, #0x10
    //     0xb0d4b8: cmp             x1, x3
    //     0xb0d4bc: b.ls            #0xb0d668
    //     0xb0d4c0: str             x3, [THR, #0x50]  ; THR::top
    //     0xb0d4c4: sub             x3, x3, #0xf
    //     0xb0d4c8: movz            x1, #0xe15c
    //     0xb0d4cc: movk            x1, #0x3, lsl #16
    //     0xb0d4d0: stur            x1, [x3, #-1]
    // 0xb0d4d4: StoreField: r3->field_7 = d0
    //     0xb0d4d4: stur            d0, [x3, #7]
    // 0xb0d4d8: mov             x1, x4
    // 0xb0d4dc: r2 = "originX"
    //     0xb0d4dc: add             x2, PP, #0x29, lsl #12  ; [pp+0x295d0] "originX"
    //     0xb0d4e0: ldr             x2, [x2, #0x5d0]
    // 0xb0d4e4: r0 = []=()
    //     0xb0d4e4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb0d4e8: ldur            x0, [fp, #-0x28]
    // 0xb0d4ec: LoadField: d0 = r0->field_f
    //     0xb0d4ec: ldur            d0, [x0, #0xf]
    // 0xb0d4f0: stur            d0, [fp, #-0x38]
    // 0xb0d4f4: r3 = inline_Allocate_Double()
    //     0xb0d4f4: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xb0d4f8: add             x3, x3, #0x10
    //     0xb0d4fc: cmp             x1, x3
    //     0xb0d500: b.ls            #0xb0d684
    //     0xb0d504: str             x3, [THR, #0x50]  ; THR::top
    //     0xb0d508: sub             x3, x3, #0xf
    //     0xb0d50c: movz            x1, #0xe15c
    //     0xb0d510: movk            x1, #0x3, lsl #16
    //     0xb0d514: stur            x1, [x3, #-1]
    // 0xb0d518: StoreField: r3->field_7 = d0
    //     0xb0d518: stur            d0, [x3, #7]
    // 0xb0d51c: ldur            x1, [fp, #-0x18]
    // 0xb0d520: r2 = "originY"
    //     0xb0d520: add             x2, PP, #0x29, lsl #12  ; [pp+0x295d8] "originY"
    //     0xb0d524: ldr             x2, [x2, #0x5d8]
    // 0xb0d528: r0 = []=()
    //     0xb0d528: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb0d52c: ldur            x0, [fp, #-0x28]
    // 0xb0d530: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xb0d530: ldur            d0, [x0, #0x17]
    // 0xb0d534: ldur            d1, [fp, #-0x30]
    // 0xb0d538: fsub            d2, d0, d1
    // 0xb0d53c: r3 = inline_Allocate_Double()
    //     0xb0d53c: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xb0d540: add             x3, x3, #0x10
    //     0xb0d544: cmp             x1, x3
    //     0xb0d548: b.ls            #0xb0d6a0
    //     0xb0d54c: str             x3, [THR, #0x50]  ; THR::top
    //     0xb0d550: sub             x3, x3, #0xf
    //     0xb0d554: movz            x1, #0xe15c
    //     0xb0d558: movk            x1, #0x3, lsl #16
    //     0xb0d55c: stur            x1, [x3, #-1]
    // 0xb0d560: StoreField: r3->field_7 = d2
    //     0xb0d560: stur            d2, [x3, #7]
    // 0xb0d564: ldur            x1, [fp, #-0x18]
    // 0xb0d568: r2 = "originWidth"
    //     0xb0d568: add             x2, PP, #0x29, lsl #12  ; [pp+0x295e0] "originWidth"
    //     0xb0d56c: ldr             x2, [x2, #0x5e0]
    // 0xb0d570: r0 = []=()
    //     0xb0d570: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb0d574: ldur            x0, [fp, #-0x28]
    // 0xb0d578: LoadField: d0 = r0->field_1f
    //     0xb0d578: ldur            d0, [x0, #0x1f]
    // 0xb0d57c: ldur            d1, [fp, #-0x38]
    // 0xb0d580: fsub            d2, d0, d1
    // 0xb0d584: r3 = inline_Allocate_Double()
    //     0xb0d584: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xb0d588: add             x3, x3, #0x10
    //     0xb0d58c: cmp             x0, x3
    //     0xb0d590: b.ls            #0xb0d6bc
    //     0xb0d594: str             x3, [THR, #0x50]  ; THR::top
    //     0xb0d598: sub             x3, x3, #0xf
    //     0xb0d59c: movz            x0, #0xe15c
    //     0xb0d5a0: movk            x0, #0x3, lsl #16
    //     0xb0d5a4: stur            x0, [x3, #-1]
    // 0xb0d5a8: StoreField: r3->field_7 = d2
    //     0xb0d5a8: stur            d2, [x3, #7]
    // 0xb0d5ac: ldur            x1, [fp, #-0x18]
    // 0xb0d5b0: r2 = "originHeight"
    //     0xb0d5b0: add             x2, PP, #0x29, lsl #12  ; [pp+0x295e8] "originHeight"
    //     0xb0d5b4: ldr             x2, [x2, #0x5e8]
    // 0xb0d5b8: r0 = []=()
    //     0xb0d5b8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb0d5bc: r16 = <String>
    //     0xb0d5bc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0d5c0: r30 = Instance_MethodChannel
    //     0xb0d5c0: add             lr, PP, #0x29, lsl #12  ; [pp+0x295f0] Obj!MethodChannel@e112b1
    //     0xb0d5c4: ldr             lr, [lr, #0x5f0]
    // 0xb0d5c8: stp             lr, x16, [SP, #0x10]
    // 0xb0d5cc: r16 = "shareFilesWithResult"
    //     0xb0d5cc: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2df60] "shareFilesWithResult"
    //     0xb0d5d0: ldr             x16, [x16, #0xf60]
    // 0xb0d5d4: ldur            lr, [fp, #-0x18]
    // 0xb0d5d8: stp             lr, x16, [SP]
    // 0xb0d5dc: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xb0d5dc: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xb0d5e0: r0 = invokeMethod()
    //     0xb0d5e0: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xb0d5e4: mov             x1, x0
    // 0xb0d5e8: stur            x1, [fp, #-0x18]
    // 0xb0d5ec: r0 = Await()
    //     0xb0d5ec: bl              #0x661044  ; AwaitStub
    // 0xb0d5f0: cmp             w0, NULL
    // 0xb0d5f4: b.ne            #0xb0d600
    // 0xb0d5f8: r0 = "dev.fluttercommunity.plus/share/unavailable"
    //     0xb0d5f8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2df68] "dev.fluttercommunity.plus/share/unavailable"
    //     0xb0d5fc: ldr             x0, [x0, #0xf68]
    // 0xb0d600: stur            x0, [fp, #-0x10]
    // 0xb0d604: r16 = ""
    //     0xb0d604: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb0d608: stp             x0, x16, [SP]
    // 0xb0d60c: r0 = ==()
    //     0xb0d60c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb0d610: tbnz            w0, #4, #0xb0d620
    // 0xb0d614: r0 = Instance_ShareResultStatus
    //     0xb0d614: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2df70] Obj!ShareResultStatus@e2e1c1
    //     0xb0d618: ldr             x0, [x0, #0xf70]
    // 0xb0d61c: b               #0xb0d64c
    // 0xb0d620: r16 = "dev.fluttercommunity.plus/share/unavailable"
    //     0xb0d620: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2df68] "dev.fluttercommunity.plus/share/unavailable"
    //     0xb0d624: ldr             x16, [x16, #0xf68]
    // 0xb0d628: ldur            lr, [fp, #-0x10]
    // 0xb0d62c: stp             lr, x16, [SP]
    // 0xb0d630: r0 = ==()
    //     0xb0d630: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb0d634: tbnz            w0, #4, #0xb0d644
    // 0xb0d638: r0 = Instance_ShareResultStatus
    //     0xb0d638: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2df78] Obj!ShareResultStatus@e2e1a1
    //     0xb0d63c: ldr             x0, [x0, #0xf78]
    // 0xb0d640: b               #0xb0d64c
    // 0xb0d644: r0 = Instance_ShareResultStatus
    //     0xb0d644: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2df80] Obj!ShareResultStatus@e2e181
    //     0xb0d648: ldr             x0, [x0, #0xf80]
    // 0xb0d64c: stur            x0, [fp, #-0x10]
    // 0xb0d650: r0 = ShareResult()
    //     0xb0d650: bl              #0xb0d6d0  ; AllocateShareResultStub -> ShareResult (size=0xc)
    // 0xb0d654: ldur            x1, [fp, #-0x10]
    // 0xb0d658: StoreField: r0->field_7 = r1
    //     0xb0d658: stur            w1, [x0, #7]
    // 0xb0d65c: r0 = ReturnAsyncNotFuture()
    //     0xb0d65c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb0d660: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0d660: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0d664: b               #0xb0d450
    // 0xb0d668: SaveReg d0
    //     0xb0d668: str             q0, [SP, #-0x10]!
    // 0xb0d66c: stp             x0, x4, [SP, #-0x10]!
    // 0xb0d670: r0 = AllocateDouble()
    //     0xb0d670: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb0d674: mov             x3, x0
    // 0xb0d678: ldp             x0, x4, [SP], #0x10
    // 0xb0d67c: RestoreReg d0
    //     0xb0d67c: ldr             q0, [SP], #0x10
    // 0xb0d680: b               #0xb0d4d4
    // 0xb0d684: SaveReg d0
    //     0xb0d684: str             q0, [SP, #-0x10]!
    // 0xb0d688: SaveReg r0
    //     0xb0d688: str             x0, [SP, #-8]!
    // 0xb0d68c: r0 = AllocateDouble()
    //     0xb0d68c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb0d690: mov             x3, x0
    // 0xb0d694: RestoreReg r0
    //     0xb0d694: ldr             x0, [SP], #8
    // 0xb0d698: RestoreReg d0
    //     0xb0d698: ldr             q0, [SP], #0x10
    // 0xb0d69c: b               #0xb0d518
    // 0xb0d6a0: SaveReg d2
    //     0xb0d6a0: str             q2, [SP, #-0x10]!
    // 0xb0d6a4: SaveReg r0
    //     0xb0d6a4: str             x0, [SP, #-8]!
    // 0xb0d6a8: r0 = AllocateDouble()
    //     0xb0d6a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb0d6ac: mov             x3, x0
    // 0xb0d6b0: RestoreReg r0
    //     0xb0d6b0: ldr             x0, [SP], #8
    // 0xb0d6b4: RestoreReg d2
    //     0xb0d6b4: ldr             q2, [SP], #0x10
    // 0xb0d6b8: b               #0xb0d560
    // 0xb0d6bc: SaveReg d2
    //     0xb0d6bc: str             q2, [SP, #-0x10]!
    // 0xb0d6c0: r0 = AllocateDouble()
    //     0xb0d6c0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb0d6c4: mov             x3, x0
    // 0xb0d6c8: RestoreReg d2
    //     0xb0d6c8: ldr             q2, [SP], #0x10
    // 0xb0d6cc: b               #0xb0d5a8
  }
  _ _getFiles(/* No info */) async {
    // ** addr: 0xb0d6dc, size: 0xa0
    // 0xb0d6dc: EnterFrame
    //     0xb0d6dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d6e0: mov             fp, SP
    // 0xb0d6e4: AllocStack(0x38)
    //     0xb0d6e4: sub             SP, SP, #0x38
    // 0xb0d6e8: SetupParameters(MethodChannelShare this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xb0d6e8: stur            NULL, [fp, #-8]
    //     0xb0d6ec: stur            x1, [fp, #-0x10]
    //     0xb0d6f0: stur            x2, [fp, #-0x18]
    // 0xb0d6f4: CheckStackOverflow
    //     0xb0d6f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d6f8: cmp             SP, x16
    //     0xb0d6fc: b.ls            #0xb0d774
    // 0xb0d700: r1 = 1
    //     0xb0d700: movz            x1, #0x1
    // 0xb0d704: r0 = AllocateContext()
    //     0xb0d704: bl              #0xec126c  ; AllocateContextStub
    // 0xb0d708: mov             x1, x0
    // 0xb0d70c: ldur            x0, [fp, #-0x10]
    // 0xb0d710: stur            x1, [fp, #-0x20]
    // 0xb0d714: StoreField: r1->field_f = r0
    //     0xb0d714: stur            w0, [x1, #0xf]
    // 0xb0d718: InitAsync() -> Future<List<XFile>>
    //     0xb0d718: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2df88] TypeArguments: <List<XFile>>
    //     0xb0d71c: ldr             x0, [x0, #0xf88]
    //     0xb0d720: bl              #0x661298  ; InitAsyncStub
    // 0xb0d724: ldur            x2, [fp, #-0x20]
    // 0xb0d728: r1 = Function '<anonymous closure>':.
    //     0xb0d728: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2df90] AnonymousClosure: (0xb0d77c), in [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::_getFiles (0xb0d6dc)
    //     0xb0d72c: ldr             x1, [x1, #0xf90]
    // 0xb0d730: r0 = AllocateClosure()
    //     0xb0d730: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0d734: r16 = <Future<XFile>>
    //     0xb0d734: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2df98] TypeArguments: <Future<XFile>>
    //     0xb0d738: ldr             x16, [x16, #0xf98]
    // 0xb0d73c: ldur            lr, [fp, #-0x18]
    // 0xb0d740: stp             lr, x16, [SP, #8]
    // 0xb0d744: str             x0, [SP]
    // 0xb0d748: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb0d748: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb0d74c: r0 = map()
    //     0xb0d74c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb0d750: r16 = <XFile>
    //     0xb0d750: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2df40] TypeArguments: <XFile>
    //     0xb0d754: ldr             x16, [x16, #0xf40]
    // 0xb0d758: stp             x0, x16, [SP]
    // 0xb0d75c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0d75c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb0d760: r0 = wait()
    //     0xb0d760: bl              #0x678258  ; [dart:async] Future::wait
    // 0xb0d764: mov             x1, x0
    // 0xb0d768: stur            x1, [fp, #-0x10]
    // 0xb0d76c: r0 = Await()
    //     0xb0d76c: bl              #0x661044  ; AwaitStub
    // 0xb0d770: r0 = ReturnAsync()
    //     0xb0d770: b               #0x6576a4  ; ReturnAsyncStub
    // 0xb0d774: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0d774: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0d778: b               #0xb0d700
  }
  [closure] Future<XFile> <anonymous closure>(dynamic, XFile) {
    // ** addr: 0xb0d77c, size: 0x4c
    // 0xb0d77c: EnterFrame
    //     0xb0d77c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d780: mov             fp, SP
    // 0xb0d784: ldr             x0, [fp, #0x18]
    // 0xb0d788: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb0d788: ldur            w1, [x0, #0x17]
    // 0xb0d78c: DecompressPointer r1
    //     0xb0d78c: add             x1, x1, HEAP, lsl #32
    // 0xb0d790: CheckStackOverflow
    //     0xb0d790: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d794: cmp             SP, x16
    //     0xb0d798: b.ls            #0xb0d7c0
    // 0xb0d79c: LoadField: r0 = r1->field_f
    //     0xb0d79c: ldur            w0, [x1, #0xf]
    // 0xb0d7a0: DecompressPointer r0
    //     0xb0d7a0: add             x0, x0, HEAP, lsl #32
    // 0xb0d7a4: mov             x1, x0
    // 0xb0d7a8: ldr             x2, [fp, #0x10]
    // 0xb0d7ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0d7ac: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb0d7b0: r0 = _getFile()
    //     0xb0d7b0: bl              #0xb0d7c8  ; [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::_getFile
    // 0xb0d7b4: LeaveFrame
    //     0xb0d7b4: mov             SP, fp
    //     0xb0d7b8: ldp             fp, lr, [SP], #0x10
    // 0xb0d7bc: ret
    //     0xb0d7bc: ret             
    // 0xb0d7c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0d7c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0d7c4: b               #0xb0d79c
  }
  _ _getFile(/* No info */) async {
    // ** addr: 0xb0d7c8, size: 0x308
    // 0xb0d7c8: EnterFrame
    //     0xb0d7c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb0d7cc: mov             fp, SP
    // 0xb0d7d0: AllocStack(0x40)
    //     0xb0d7d0: sub             SP, SP, #0x40
    // 0xb0d7d4: SetupParameters(MethodChannelShare this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb0d7d4: stur            NULL, [fp, #-8]
    //     0xb0d7d8: mov             x0, x1
    //     0xb0d7dc: mov             x1, x2
    //     0xb0d7e0: stur            x2, [fp, #-0x10]
    // 0xb0d7e4: CheckStackOverflow
    //     0xb0d7e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0d7e8: cmp             SP, x16
    //     0xb0d7ec: b.ls            #0xb0dac8
    // 0xb0d7f0: InitAsync() -> Future<XFile>
    //     0xb0d7f0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2df40] TypeArguments: <XFile>
    //     0xb0d7f4: ldr             x0, [x0, #0xf40]
    //     0xb0d7f8: bl              #0x661298  ; InitAsyncStub
    // 0xb0d7fc: ldur            x0, [fp, #-0x10]
    // 0xb0d800: LoadField: r1 = r0->field_7
    //     0xb0d800: ldur            w1, [x0, #7]
    // 0xb0d804: DecompressPointer r1
    //     0xb0d804: add             x1, x1, HEAP, lsl #32
    // 0xb0d808: LoadField: r2 = r1->field_7
    //     0xb0d808: ldur            w2, [x1, #7]
    // 0xb0d80c: DecompressPointer r2
    //     0xb0d80c: add             x2, x2, HEAP, lsl #32
    // 0xb0d810: stur            x2, [fp, #-0x18]
    // 0xb0d814: LoadField: r1 = r2->field_7
    //     0xb0d814: ldur            w1, [x2, #7]
    // 0xb0d818: cbz             w1, #0xb0d820
    // 0xb0d81c: r0 = ReturnAsyncNotFuture()
    //     0xb0d81c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb0d820: r0 = getTemporaryDirectory()
    //     0xb0d820: bl              #0x8b6a10  ; [package:path_provider/path_provider.dart] ::getTemporaryDirectory
    // 0xb0d824: mov             x1, x0
    // 0xb0d828: stur            x1, [fp, #-0x20]
    // 0xb0d82c: r0 = Await()
    //     0xb0d82c: bl              #0x661044  ; AwaitStub
    // 0xb0d830: r1 = LoadClassIdInstr(r0)
    //     0xb0d830: ldur            x1, [x0, #-1]
    //     0xb0d834: ubfx            x1, x1, #0xc, #0x14
    // 0xb0d838: mov             x16, x0
    // 0xb0d83c: mov             x0, x1
    // 0xb0d840: mov             x1, x16
    // 0xb0d844: r0 = GDT[cid_x0 + -0xe06]()
    //     0xb0d844: sub             lr, x0, #0xe06
    //     0xb0d848: ldr             lr, [x21, lr, lsl #3]
    //     0xb0d84c: blr             lr
    // 0xb0d850: r1 = "octet-stream"
    //     0xb0d850: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] "octet-stream"
    //     0xb0d854: ldr             x1, [x1, #0xfa0]
    // 0xb0d858: stur            x0, [fp, #-0x20]
    // 0xb0d85c: r0 = extensionFromMime()
    //     0xb0d85c: bl              #0xb0db60  ; [package:mime/src/mime_type.dart] ::extensionFromMime
    // 0xb0d860: mov             x1, x0
    // 0xb0d864: stur            x1, [fp, #-0x28]
    // 0xb0d868: r0 = LoadClassIdInstr(r1)
    //     0xb0d868: ldur            x0, [x1, #-1]
    //     0xb0d86c: ubfx            x0, x0, #0xc, #0x14
    // 0xb0d870: r16 = "jpe"
    //     0xb0d870: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] "jpe"
    //     0xb0d874: ldr             x16, [x16, #0xfa8]
    // 0xb0d878: stp             x16, x1, [SP]
    // 0xb0d87c: mov             lr, x0
    // 0xb0d880: ldr             lr, [x21, lr, lsl #3]
    // 0xb0d884: blr             lr
    // 0xb0d888: tbnz            w0, #4, #0xb0d898
    // 0xb0d88c: r4 = "jpeg"
    //     0xb0d88c: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfb0] "jpeg"
    //     0xb0d890: ldr             x4, [x4, #0xfb0]
    // 0xb0d894: b               #0xb0d89c
    // 0xb0d898: ldur            x4, [fp, #-0x28]
    // 0xb0d89c: ldur            x0, [fp, #-0x20]
    // 0xb0d8a0: ldur            x3, [fp, #-0x18]
    // 0xb0d8a4: stur            x4, [fp, #-0x28]
    // 0xb0d8a8: r1 = Null
    //     0xb0d8a8: mov             x1, NULL
    // 0xb0d8ac: r2 = 6
    //     0xb0d8ac: movz            x2, #0x6
    // 0xb0d8b0: r0 = AllocateArray()
    //     0xb0d8b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0d8b4: mov             x2, x0
    // 0xb0d8b8: ldur            x0, [fp, #-0x20]
    // 0xb0d8bc: stur            x2, [fp, #-0x30]
    // 0xb0d8c0: StoreField: r2->field_f = r0
    //     0xb0d8c0: stur            w0, [x2, #0xf]
    // 0xb0d8c4: r16 = "/"
    //     0xb0d8c4: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xb0d8c8: StoreField: r2->field_13 = r16
    //     0xb0d8c8: stur            w16, [x2, #0x13]
    // 0xb0d8cc: r1 = Instance_Uuid
    //     0xb0d8cc: add             x1, PP, #0xc, lsl #12  ; [pp+0xc770] Obj!Uuid@e0bee1
    //     0xb0d8d0: ldr             x1, [x1, #0x770]
    // 0xb0d8d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0d8d4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0d8d8: r0 = v4()
    //     0xb0d8d8: bl              #0x8b8bd8  ; [package:uuid/uuid.dart] Uuid::v4
    // 0xb0d8dc: ldur            x1, [fp, #-0x30]
    // 0xb0d8e0: ArrayStore: r1[2] = r0  ; List_4
    //     0xb0d8e0: add             x25, x1, #0x17
    //     0xb0d8e4: str             w0, [x25]
    //     0xb0d8e8: tbz             w0, #0, #0xb0d904
    //     0xb0d8ec: ldurb           w16, [x1, #-1]
    //     0xb0d8f0: ldurb           w17, [x0, #-1]
    //     0xb0d8f4: and             x16, x17, x16, lsr #2
    //     0xb0d8f8: tst             x16, HEAP, lsr #32
    //     0xb0d8fc: b.eq            #0xb0d904
    //     0xb0d900: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb0d904: ldur            x16, [fp, #-0x30]
    // 0xb0d908: str             x16, [SP]
    // 0xb0d90c: r0 = _interpolate()
    //     0xb0d90c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb0d910: stur            x0, [fp, #-0x20]
    // 0xb0d914: r0 = current()
    //     0xb0d914: bl              #0x60cb5c  ; [dart:io] IOOverrides::current
    // 0xb0d918: r0 = _Directory()
    //     0xb0d918: bl              #0x60ca0c  ; Allocate_DirectoryStub -> _Directory (size=0x10)
    // 0xb0d91c: mov             x1, x0
    // 0xb0d920: ldur            x2, [fp, #-0x20]
    // 0xb0d924: stur            x0, [fp, #-0x30]
    // 0xb0d928: r0 = _File()
    //     0xb0d928: bl              #0x60ae2c  ; [dart:io] _File::_File
    // 0xb0d92c: r16 = true
    //     0xb0d92c: add             x16, NULL, #0x20  ; true
    // 0xb0d930: str             x16, [SP]
    // 0xb0d934: ldur            x1, [fp, #-0x30]
    // 0xb0d938: r4 = const [0, 0x2, 0x1, 0x1, recursive, 0x1, null]
    //     0xb0d938: ldr             x4, [PP, #0x7eb0]  ; [pp+0x7eb0] List(7) [0, 0x2, 0x1, 0x1, "recursive", 0x1, Null]
    // 0xb0d93c: r0 = create()
    //     0xb0d93c: bl              #0xd6d3f8  ; [dart:io] _Directory::create
    // 0xb0d940: mov             x1, x0
    // 0xb0d944: stur            x1, [fp, #-0x30]
    // 0xb0d948: r0 = Await()
    //     0xb0d948: bl              #0x661044  ; AwaitStub
    // 0xb0d94c: ldur            x3, [fp, #-0x18]
    // 0xb0d950: r0 = LoadClassIdInstr(r3)
    //     0xb0d950: ldur            x0, [x3, #-1]
    //     0xb0d954: ubfx            x0, x0, #0xc, #0x14
    // 0xb0d958: mov             x1, x3
    // 0xb0d95c: r2 = "/"
    //     0xb0d95c: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xb0d960: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb0d960: sub             lr, x0, #1, lsl #12
    //     0xb0d964: ldr             lr, [x21, lr, lsl #3]
    //     0xb0d968: blr             lr
    // 0xb0d96c: mov             x1, x0
    // 0xb0d970: r0 = last()
    //     0xb0d970: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xb0d974: LoadField: r1 = r0->field_7
    //     0xb0d974: ldur            w1, [x0, #7]
    // 0xb0d978: cbnz            w1, #0xb0d9b0
    // 0xb0d97c: ldur            x1, [fp, #-0x18]
    // 0xb0d980: r0 = LoadClassIdInstr(r1)
    //     0xb0d980: ldur            x0, [x1, #-1]
    //     0xb0d984: ubfx            x0, x0, #0xc, #0x14
    // 0xb0d988: r2 = "/"
    //     0xb0d988: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xb0d98c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb0d98c: sub             lr, x0, #1, lsl #12
    //     0xb0d990: ldr             lr, [x21, lr, lsl #3]
    //     0xb0d994: blr             lr
    // 0xb0d998: mov             x1, x0
    // 0xb0d99c: r0 = last()
    //     0xb0d99c: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xb0d9a0: mov             x1, x0
    // 0xb0d9a4: r0 = lookupMimeType()
    //     0xb0d9a4: bl              #0xa3bc78  ; [package:mime/src/mime_type.dart] ::lookupMimeType
    // 0xb0d9a8: cmp             w0, NULL
    // 0xb0d9ac: b.eq            #0xb0d9c0
    // 0xb0d9b0: ldur            x1, [fp, #-0x10]
    // 0xb0d9b4: r0 = name()
    //     0xb0d9b4: bl              #0xb0db08  ; [package:cross_file/src/types/io.dart] XFile::name
    // 0xb0d9b8: mov             x3, x0
    // 0xb0d9bc: b               #0xb0da1c
    // 0xb0d9c0: ldur            x0, [fp, #-0x28]
    // 0xb0d9c4: r1 = Instance_Uuid
    //     0xb0d9c4: add             x1, PP, #0xc, lsl #12  ; [pp+0xc770] Obj!Uuid@e0bee1
    //     0xb0d9c8: ldr             x1, [x1, #0x770]
    // 0xb0d9cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb0d9cc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb0d9d0: r0 = v1()
    //     0xb0d9d0: bl              #0xaac748  ; [package:uuid/uuid.dart] Uuid::v1
    // 0xb0d9d4: mov             x1, x0
    // 0xb0d9d8: r2 = 10
    //     0xb0d9d8: movz            x2, #0xa
    // 0xb0d9dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0d9dc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb0d9e0: r0 = substring()
    //     0xb0d9e0: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xb0d9e4: r1 = Null
    //     0xb0d9e4: mov             x1, NULL
    // 0xb0d9e8: r2 = 6
    //     0xb0d9e8: movz            x2, #0x6
    // 0xb0d9ec: stur            x0, [fp, #-0x18]
    // 0xb0d9f0: r0 = AllocateArray()
    //     0xb0d9f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0d9f4: mov             x1, x0
    // 0xb0d9f8: ldur            x0, [fp, #-0x18]
    // 0xb0d9fc: StoreField: r1->field_f = r0
    //     0xb0d9fc: stur            w0, [x1, #0xf]
    // 0xb0da00: r16 = "."
    //     0xb0da00: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xb0da04: StoreField: r1->field_13 = r16
    //     0xb0da04: stur            w16, [x1, #0x13]
    // 0xb0da08: ldur            x0, [fp, #-0x28]
    // 0xb0da0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0da0c: stur            w0, [x1, #0x17]
    // 0xb0da10: str             x1, [SP]
    // 0xb0da14: r0 = _interpolate()
    //     0xb0da14: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb0da18: mov             x3, x0
    // 0xb0da1c: ldur            x0, [fp, #-0x20]
    // 0xb0da20: stur            x3, [fp, #-0x18]
    // 0xb0da24: r1 = Null
    //     0xb0da24: mov             x1, NULL
    // 0xb0da28: r2 = 6
    //     0xb0da28: movz            x2, #0x6
    // 0xb0da2c: r0 = AllocateArray()
    //     0xb0da2c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0da30: mov             x1, x0
    // 0xb0da34: ldur            x0, [fp, #-0x20]
    // 0xb0da38: StoreField: r1->field_f = r0
    //     0xb0da38: stur            w0, [x1, #0xf]
    // 0xb0da3c: r16 = "/"
    //     0xb0da3c: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xb0da40: StoreField: r1->field_13 = r16
    //     0xb0da40: stur            w16, [x1, #0x13]
    // 0xb0da44: ldur            x0, [fp, #-0x18]
    // 0xb0da48: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0da48: stur            w0, [x1, #0x17]
    // 0xb0da4c: str             x1, [SP]
    // 0xb0da50: r0 = _interpolate()
    //     0xb0da50: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb0da54: stur            x0, [fp, #-0x18]
    // 0xb0da58: r0 = current()
    //     0xb0da58: bl              #0x60cb5c  ; [dart:io] IOOverrides::current
    // 0xb0da5c: r0 = _File()
    //     0xb0da5c: bl              #0x60add8  ; Allocate_FileStub -> _File (size=0x10)
    // 0xb0da60: mov             x1, x0
    // 0xb0da64: ldur            x2, [fp, #-0x18]
    // 0xb0da68: stur            x0, [fp, #-0x20]
    // 0xb0da6c: r0 = _File()
    //     0xb0da6c: bl              #0x60ae2c  ; [dart:io] _File::_File
    // 0xb0da70: ldur            x1, [fp, #-0x10]
    // 0xb0da74: r0 = readAsBytes()
    //     0xb0da74: bl              #0xb0dad0  ; [package:cross_file/src/types/io.dart] XFile::readAsBytes
    // 0xb0da78: mov             x1, x0
    // 0xb0da7c: stur            x1, [fp, #-0x10]
    // 0xb0da80: r0 = Await()
    //     0xb0da80: bl              #0x661044  ; AwaitStub
    // 0xb0da84: ldur            x1, [fp, #-0x20]
    // 0xb0da88: mov             x2, x0
    // 0xb0da8c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0da8c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb0da90: r0 = writeAsBytes()
    //     0xb0da90: bl              #0xd702a8  ; [dart:io] _File::writeAsBytes
    // 0xb0da94: mov             x1, x0
    // 0xb0da98: stur            x1, [fp, #-0x10]
    // 0xb0da9c: r0 = Await()
    //     0xb0da9c: bl              #0x661044  ; AwaitStub
    // 0xb0daa0: r0 = current()
    //     0xb0daa0: bl              #0x60cb5c  ; [dart:io] IOOverrides::current
    // 0xb0daa4: r0 = _File()
    //     0xb0daa4: bl              #0x60add8  ; Allocate_FileStub -> _File (size=0x10)
    // 0xb0daa8: mov             x1, x0
    // 0xb0daac: ldur            x2, [fp, #-0x18]
    // 0xb0dab0: stur            x0, [fp, #-0x10]
    // 0xb0dab4: r0 = _File()
    //     0xb0dab4: bl              #0x60ae2c  ; [dart:io] _File::_File
    // 0xb0dab8: r0 = XFile()
    //     0xb0dab8: bl              #0xb0ddf8  ; AllocateXFileStub -> XFile (size=0x14)
    // 0xb0dabc: ldur            x1, [fp, #-0x10]
    // 0xb0dac0: StoreField: r0->field_7 = r1
    //     0xb0dac0: stur            w1, [x0, #7]
    // 0xb0dac4: r0 = ReturnAsyncNotFuture()
    //     0xb0dac4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb0dac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0dac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0dacc: b               #0xb0d7f0
  }
  [closure] String <anonymous closure>(dynamic, XFile) {
    // ** addr: 0xb0dd84, size: 0x30
    // 0xb0dd84: EnterFrame
    //     0xb0dd84: stp             fp, lr, [SP, #-0x10]!
    //     0xb0dd88: mov             fp, SP
    // 0xb0dd8c: CheckStackOverflow
    //     0xb0dd8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0dd90: cmp             SP, x16
    //     0xb0dd94: b.ls            #0xb0ddac
    // 0xb0dd98: ldr             x1, [fp, #0x10]
    // 0xb0dd9c: r0 = source()
    //     0xb0dd9c: bl              #0xe54bf0  ; [package:petitparser/src/core/exception.dart] ParserException::source
    // 0xb0dda0: LeaveFrame
    //     0xb0dda0: mov             SP, fp
    //     0xb0dda4: ldp             fp, lr, [SP], #0x10
    // 0xb0dda8: ret
    //     0xb0dda8: ret             
    // 0xb0ddac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0ddac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0ddb0: b               #0xb0dd98
  }
  [closure] String <anonymous closure>(dynamic, XFile) {
    // ** addr: 0xb0ddb4, size: 0x44
    // 0xb0ddb4: EnterFrame
    //     0xb0ddb4: stp             fp, lr, [SP, #-0x10]!
    //     0xb0ddb8: mov             fp, SP
    // 0xb0ddbc: CheckStackOverflow
    //     0xb0ddbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0ddc0: cmp             SP, x16
    //     0xb0ddc4: b.ls            #0xb0ddf0
    // 0xb0ddc8: ldr             x0, [fp, #0x10]
    // 0xb0ddcc: LoadField: r1 = r0->field_7
    //     0xb0ddcc: ldur            w1, [x0, #7]
    // 0xb0ddd0: DecompressPointer r1
    //     0xb0ddd0: add             x1, x1, HEAP, lsl #32
    // 0xb0ddd4: LoadField: r0 = r1->field_7
    //     0xb0ddd4: ldur            w0, [x1, #7]
    // 0xb0ddd8: DecompressPointer r0
    //     0xb0ddd8: add             x0, x0, HEAP, lsl #32
    // 0xb0dddc: mov             x1, x0
    // 0xb0dde0: r0 = _mimeTypeForPath()
    //     0xb0dde0: bl              #0xa3bc3c  ; [package:share_plus_platform_interface/method_channel/method_channel_share.dart] MethodChannelShare::_mimeTypeForPath
    // 0xb0dde4: LeaveFrame
    //     0xb0dde4: mov             SP, fp
    //     0xb0dde8: ldp             fp, lr, [SP], #0x10
    // 0xb0ddec: ret
    //     0xb0ddec: ret             
    // 0xb0ddf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0ddf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0ddf4: b               #0xb0ddc8
  }
}
