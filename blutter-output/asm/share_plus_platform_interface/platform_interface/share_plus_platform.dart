// lib: , url: package:share_plus_platform_interface/platform_interface/share_plus_platform.dart

// class id: 1051119, size: 0x8
class :: {
}

// class id: 503, size: 0xc, field offset: 0x8
//   const constructor, 
class ShareResult extends Object {
}

// class id: 5867, size: 0x8, field offset: 0x8
abstract class SharePlatform extends PlatformInterface {

  static late SharePlatform _instance; // offset: 0x1744
  static late final Object _token; // offset: 0x1740

  static SharePlatform _instance() {
    // ** addr: 0xa3bf24, size: 0x8c
    // 0xa3bf24: EnterFrame
    //     0xa3bf24: stp             fp, lr, [SP, #-0x10]!
    //     0xa3bf28: mov             fp, SP
    // 0xa3bf2c: AllocStack(0x10)
    //     0xa3bf2c: sub             SP, SP, #0x10
    // 0xa3bf30: CheckStackOverflow
    //     0xa3bf30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3bf34: cmp             SP, x16
    //     0xa3bf38: b.ls            #0xa3bfa8
    // 0xa3bf3c: r0 = InitLateStaticField(0x1740) // [package:share_plus_platform_interface/platform_interface/share_plus_platform.dart] SharePlatform::_token
    //     0xa3bf3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa3bf40: ldr             x0, [x0, #0x2e80]
    //     0xa3bf44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa3bf48: cmp             w0, w16
    //     0xa3bf4c: b.ne            #0xa3bf5c
    //     0xa3bf50: add             x2, PP, #0x29, lsl #12  ; [pp+0x295f8] Field <SharePlatform._token@2695348855>: static late final (offset: 0x1740)
    //     0xa3bf54: ldr             x2, [x2, #0x5f8]
    //     0xa3bf58: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa3bf5c: stur            x0, [fp, #-8]
    // 0xa3bf60: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xa3bf60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa3bf64: ldr             x0, [x0, #0xc08]
    //     0xa3bf68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa3bf6c: cmp             w0, w16
    //     0xa3bf70: b.ne            #0xa3bf7c
    //     0xa3bf74: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0xa3bf78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa3bf7c: stur            x0, [fp, #-0x10]
    // 0xa3bf80: r0 = MethodChannelShare()
    //     0xa3bf80: bl              #0xa3bfb0  ; AllocateMethodChannelShareStub -> MethodChannelShare (size=0x8)
    // 0xa3bf84: ldur            x1, [fp, #-0x10]
    // 0xa3bf88: mov             x2, x0
    // 0xa3bf8c: ldur            x3, [fp, #-8]
    // 0xa3bf90: stur            x0, [fp, #-8]
    // 0xa3bf94: r0 = []=()
    //     0xa3bf94: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0xa3bf98: ldur            x0, [fp, #-8]
    // 0xa3bf9c: LeaveFrame
    //     0xa3bf9c: mov             SP, fp
    //     0xa3bfa0: ldp             fp, lr, [SP], #0x10
    // 0xa3bfa4: ret
    //     0xa3bfa4: ret             
    // 0xa3bfa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3bfa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3bfac: b               #0xa3bf3c
  }
}

// class id: 6775, size: 0x14, field offset: 0x14
enum ShareResultStatus extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e824, size: 0x64
    // 0xc4e824: EnterFrame
    //     0xc4e824: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e828: mov             fp, SP
    // 0xc4e82c: AllocStack(0x10)
    //     0xc4e82c: sub             SP, SP, #0x10
    // 0xc4e830: SetupParameters(ShareResultStatus this /* r1 => r0, fp-0x8 */)
    //     0xc4e830: mov             x0, x1
    //     0xc4e834: stur            x1, [fp, #-8]
    // 0xc4e838: CheckStackOverflow
    //     0xc4e838: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e83c: cmp             SP, x16
    //     0xc4e840: b.ls            #0xc4e880
    // 0xc4e844: r1 = Null
    //     0xc4e844: mov             x1, NULL
    // 0xc4e848: r2 = 4
    //     0xc4e848: movz            x2, #0x4
    // 0xc4e84c: r0 = AllocateArray()
    //     0xc4e84c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e850: r16 = "ShareResultStatus."
    //     0xc4e850: add             x16, PP, #0x31, lsl #12  ; [pp+0x311c8] "ShareResultStatus."
    //     0xc4e854: ldr             x16, [x16, #0x1c8]
    // 0xc4e858: StoreField: r0->field_f = r16
    //     0xc4e858: stur            w16, [x0, #0xf]
    // 0xc4e85c: ldur            x1, [fp, #-8]
    // 0xc4e860: LoadField: r2 = r1->field_f
    //     0xc4e860: ldur            w2, [x1, #0xf]
    // 0xc4e864: DecompressPointer r2
    //     0xc4e864: add             x2, x2, HEAP, lsl #32
    // 0xc4e868: StoreField: r0->field_13 = r2
    //     0xc4e868: stur            w2, [x0, #0x13]
    // 0xc4e86c: str             x0, [SP]
    // 0xc4e870: r0 = _interpolate()
    //     0xc4e870: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e874: LeaveFrame
    //     0xc4e874: mov             SP, fp
    //     0xc4e878: ldp             fp, lr, [SP], #0x10
    // 0xc4e87c: ret
    //     0xc4e87c: ret             
    // 0xc4e880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e880: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e884: b               #0xc4e844
  }
}
