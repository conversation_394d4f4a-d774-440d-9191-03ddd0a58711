// lib: shimmer, url: package:shimmer/shimmer.dart

// class id: 1051120, size: 0x8
class :: {
}

// class id: 3095, size: 0x6c, field offset: 0x5c
class _Shimmer<PERSON><PERSON>er extends RenderProxyBox {

  get _ alwaysNeedsCompositing(/* No info */) {
    // ** addr: 0x785e08, size: 0x1c
    // 0x785e08: LoadField: r2 = r1->field_57
    //     0x785e08: ldur            w2, [x1, #0x57]
    // 0x785e0c: DecompressPointer r2
    //     0x785e0c: add             x2, x2, HEAP, lsl #32
    // 0x785e10: cmp             w2, NULL
    // 0x785e14: r16 = true
    //     0x785e14: add             x16, NULL, #0x20  ; true
    // 0x785e18: r17 = false
    //     0x785e18: add             x17, NULL, #0x30  ; false
    // 0x785e1c: csel            x0, x16, x17, ne
    // 0x785e20: ret
    //     0x785e20: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0x7937d4, size: 0x260
    // 0x7937d4: EnterFrame
    //     0x7937d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7937d8: mov             fp, SP
    // 0x7937dc: AllocStack(0x48)
    //     0x7937dc: sub             SP, SP, #0x48
    // 0x7937e0: SetupParameters(_ShimmerFilter this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x7937e0: mov             x0, x3
    //     0x7937e4: stur            x3, [fp, #-0x18]
    //     0x7937e8: mov             x3, x1
    //     0x7937ec: stur            x1, [fp, #-8]
    //     0x7937f0: stur            x2, [fp, #-0x10]
    // 0x7937f4: CheckStackOverflow
    //     0x7937f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7937f8: cmp             SP, x16
    //     0x7937fc: b.ls            #0x793a20
    // 0x793800: LoadField: r1 = r3->field_57
    //     0x793800: ldur            w1, [x3, #0x57]
    // 0x793804: DecompressPointer r1
    //     0x793804: add             x1, x1, HEAP, lsl #32
    // 0x793808: cmp             w1, NULL
    // 0x79380c: b.eq            #0x7939fc
    // 0x793810: r0 = size()
    //     0x793810: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x793814: LoadField: d0 = r0->field_7
    //     0x793814: ldur            d0, [x0, #7]
    // 0x793818: ldur            x0, [fp, #-8]
    // 0x79381c: stur            d0, [fp, #-0x38]
    // 0x793820: LoadField: r1 = r0->field_57
    //     0x793820: ldur            w1, [x0, #0x57]
    // 0x793824: DecompressPointer r1
    //     0x793824: add             x1, x1, HEAP, lsl #32
    // 0x793828: cmp             w1, NULL
    // 0x79382c: b.eq            #0x793a28
    // 0x793830: r0 = size()
    //     0x793830: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x793834: LoadField: d0 = r0->field_f
    //     0x793834: ldur            d0, [x0, #0xf]
    // 0x793838: ldur            d1, [fp, #-0x38]
    // 0x79383c: fneg            d2, d1
    // 0x793840: ldur            x1, [fp, #-8]
    // 0x793844: LoadField: d3 = r1->field_63
    //     0x793844: ldur            d3, [x1, #0x63]
    // 0x793848: fsub            d4, d1, d2
    // 0x79384c: fmul            d5, d4, d3
    // 0x793850: fadd            d3, d2, d5
    // 0x793854: fsub            d2, d3, d1
    // 0x793858: stur            d2, [fp, #-0x48]
    // 0x79385c: d3 = 3.000000
    //     0x79385c: fmov            d3, #3.00000000
    // 0x793860: fmul            d4, d1, d3
    // 0x793864: fadd            d1, d2, d4
    // 0x793868: stur            d1, [fp, #-0x40]
    // 0x79386c: d3 = 0.000000
    //     0x79386c: eor             v3.16b, v3.16b, v3.16b
    // 0x793870: fadd            d4, d0, d3
    // 0x793874: stur            d4, [fp, #-0x38]
    // 0x793878: r0 = Rect()
    //     0x793878: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0x79387c: mov             x3, x0
    // 0x793880: ldur            d0, [fp, #-0x48]
    // 0x793884: stur            x3, [fp, #-0x30]
    // 0x793888: StoreField: r3->field_7 = d0
    //     0x793888: stur            d0, [x3, #7]
    // 0x79388c: StoreField: r3->field_f = rZR
    //     0x79388c: stur            xzr, [x3, #0xf]
    // 0x793890: ldur            d0, [fp, #-0x40]
    // 0x793894: ArrayStore: r3[0] = d0  ; List_8
    //     0x793894: stur            d0, [x3, #0x17]
    // 0x793898: ldur            d0, [fp, #-0x38]
    // 0x79389c: StoreField: r3->field_1f = d0
    //     0x79389c: stur            d0, [x3, #0x1f]
    // 0x7938a0: ldur            x4, [fp, #-8]
    // 0x7938a4: LoadField: r5 = r4->field_2f
    //     0x7938a4: ldur            w5, [x4, #0x2f]
    // 0x7938a8: DecompressPointer r5
    //     0x7938a8: add             x5, x5, HEAP, lsl #32
    // 0x7938ac: stur            x5, [fp, #-0x28]
    // 0x7938b0: LoadField: r6 = r5->field_b
    //     0x7938b0: ldur            w6, [x5, #0xb]
    // 0x7938b4: DecompressPointer r6
    //     0x7938b4: add             x6, x6, HEAP, lsl #32
    // 0x7938b8: mov             x0, x6
    // 0x7938bc: stur            x6, [fp, #-0x20]
    // 0x7938c0: r2 = Null
    //     0x7938c0: mov             x2, NULL
    // 0x7938c4: r1 = Null
    //     0x7938c4: mov             x1, NULL
    // 0x7938c8: r4 = LoadClassIdInstr(r0)
    //     0x7938c8: ldur            x4, [x0, #-1]
    //     0x7938cc: ubfx            x4, x4, #0xc, #0x14
    // 0x7938d0: cmp             x4, #0xb71
    // 0x7938d4: b.eq            #0x7938ec
    // 0x7938d8: r8 = ShaderMaskLayer?
    //     0x7938d8: add             x8, PP, #0x57, lsl #12  ; [pp+0x57f98] Type: ShaderMaskLayer?
    //     0x7938dc: ldr             x8, [x8, #0xf98]
    // 0x7938e0: r3 = Null
    //     0x7938e0: add             x3, PP, #0x57, lsl #12  ; [pp+0x57fa0] Null
    //     0x7938e4: ldr             x3, [x3, #0xfa0]
    // 0x7938e8: r0 = DefaultNullableTypeTest()
    //     0x7938e8: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7938ec: ldur            x0, [fp, #-0x20]
    // 0x7938f0: cmp             w0, NULL
    // 0x7938f4: b.ne            #0x793914
    // 0x7938f8: r0 = ShaderMaskLayer()
    //     0x7938f8: bl              #0x79405c  ; AllocateShaderMaskLayerStub -> ShaderMaskLayer (size=0x54)
    // 0x7938fc: mov             x1, x0
    // 0x793900: stur            x0, [fp, #-0x20]
    // 0x793904: r0 = Layer()
    //     0x793904: bl              #0x6d2824  ; [package:flutter/src/rendering/layer.dart] Layer::Layer
    // 0x793908: ldur            x1, [fp, #-0x28]
    // 0x79390c: ldur            x2, [fp, #-0x20]
    // 0x793910: r0 = layer=()
    //     0x793910: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x793914: ldur            x0, [fp, #-8]
    // 0x793918: ldur            x2, [fp, #-0x28]
    // 0x79391c: mov             x1, x0
    // 0x793920: r0 = layer()
    //     0x793920: bl              #0x793ffc  ; [package:shimmer/shimmer.dart] _ShimmerFilter::layer
    // 0x793924: stur            x0, [fp, #-0x20]
    // 0x793928: cmp             w0, NULL
    // 0x79392c: b.eq            #0x793a2c
    // 0x793930: ldur            x3, [fp, #-8]
    // 0x793934: LoadField: r1 = r3->field_5f
    //     0x793934: ldur            w1, [x3, #0x5f]
    // 0x793938: DecompressPointer r1
    //     0x793938: add             x1, x1, HEAP, lsl #32
    // 0x79393c: ldur            x2, [fp, #-0x30]
    // 0x793940: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x793940: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x793944: r0 = createShader()
    //     0x793944: bl              #0x793b00  ; [package:flutter/src/painting/gradient.dart] LinearGradient::createShader
    // 0x793948: ldur            x1, [fp, #-0x20]
    // 0x79394c: mov             x2, x0
    // 0x793950: r0 = shader=()
    //     0x793950: bl              #0x78e0c4  ; [package:flutter/src/rendering/layer.dart] ShaderMaskLayer::shader=
    // 0x793954: ldur            x1, [fp, #-8]
    // 0x793958: r0 = size()
    //     0x793958: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x79395c: ldur            x1, [fp, #-0x18]
    // 0x793960: mov             x2, x0
    // 0x793964: r0 = &()
    //     0x793964: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0x793968: ldur            x1, [fp, #-0x20]
    // 0x79396c: mov             x2, x0
    // 0x793970: r0 = maskRect=()
    //     0x793970: bl              #0x793a88  ; [package:flutter/src/rendering/layer.dart] ShaderMaskLayer::maskRect=
    // 0x793974: ldur            x1, [fp, #-0x20]
    // 0x793978: r2 = Instance_BlendMode
    //     0x793978: add             x2, PP, #0x57, lsl #12  ; [pp+0x57fb0] Obj!BlendMode@e39ce1
    //     0x79397c: ldr             x2, [x2, #0xfb0]
    // 0x793980: r0 = blendMode=()
    //     0x793980: bl              #0x793a34  ; [package:flutter/src/rendering/layer.dart] ShaderMaskLayer::blendMode=
    // 0x793984: ldur            x0, [fp, #-0x28]
    // 0x793988: LoadField: r3 = r0->field_b
    //     0x793988: ldur            w3, [x0, #0xb]
    // 0x79398c: DecompressPointer r3
    //     0x79398c: add             x3, x3, HEAP, lsl #32
    // 0x793990: mov             x0, x3
    // 0x793994: stur            x3, [fp, #-0x20]
    // 0x793998: r2 = Null
    //     0x793998: mov             x2, NULL
    // 0x79399c: r1 = Null
    //     0x79399c: mov             x1, NULL
    // 0x7939a0: r4 = LoadClassIdInstr(r0)
    //     0x7939a0: ldur            x4, [x0, #-1]
    //     0x7939a4: ubfx            x4, x4, #0xc, #0x14
    // 0x7939a8: cmp             x4, #0xb71
    // 0x7939ac: b.eq            #0x7939c4
    // 0x7939b0: r8 = ShaderMaskLayer?
    //     0x7939b0: add             x8, PP, #0x57, lsl #12  ; [pp+0x57f98] Type: ShaderMaskLayer?
    //     0x7939b4: ldr             x8, [x8, #0xf98]
    // 0x7939b8: r3 = Null
    //     0x7939b8: add             x3, PP, #0x57, lsl #12  ; [pp+0x57fb8] Null
    //     0x7939bc: ldr             x3, [x3, #0xfb8]
    // 0x7939c0: r0 = DefaultNullableTypeTest()
    //     0x7939c0: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7939c4: ldur            x0, [fp, #-0x20]
    // 0x7939c8: cmp             w0, NULL
    // 0x7939cc: b.eq            #0x793a30
    // 0x7939d0: ldur            x2, [fp, #-8]
    // 0x7939d4: r1 = Function 'paint':.
    //     0x7939d4: add             x1, PP, #0x39, lsl #12  ; [pp+0x39d68] AnonymousClosure: (0x78c750), in [package:flutter/src/rendering/proxy_box.dart] _RenderProxyBox&RenderBox&RenderObjectWithChildMixin&RenderProxyBoxMixin::paint (0x7928f0)
    //     0x7939d8: ldr             x1, [x1, #0xd68]
    // 0x7939dc: r0 = AllocateClosure()
    //     0x7939dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x7939e0: ldur            x1, [fp, #-0x10]
    // 0x7939e4: ldur            x2, [fp, #-0x20]
    // 0x7939e8: mov             x3, x0
    // 0x7939ec: ldur            x5, [fp, #-0x18]
    // 0x7939f0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x7939f0: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x7939f4: r0 = pushLayer()
    //     0x7939f4: bl              #0x78a8e8  ; [package:flutter/src/rendering/object.dart] PaintingContext::pushLayer
    // 0x7939f8: b               #0x793a10
    // 0x7939fc: mov             x0, x3
    // 0x793a00: LoadField: r1 = r0->field_2f
    //     0x793a00: ldur            w1, [x0, #0x2f]
    // 0x793a04: DecompressPointer r1
    //     0x793a04: add             x1, x1, HEAP, lsl #32
    // 0x793a08: r2 = Null
    //     0x793a08: mov             x2, NULL
    // 0x793a0c: r0 = layer=()
    //     0x793a0c: bl              #0x6d099c  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x793a10: r0 = Null
    //     0x793a10: mov             x0, NULL
    // 0x793a14: LeaveFrame
    //     0x793a14: mov             SP, fp
    //     0x793a18: ldp             fp, lr, [SP], #0x10
    // 0x793a1c: ret
    //     0x793a1c: ret             
    // 0x793a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x793a20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x793a24: b               #0x793800
    // 0x793a28: r0 = NullCastErrorSharedWithFPURegs()
    //     0x793a28: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x793a2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x793a2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x793a30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x793a30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ layer(/* No info */) {
    // ** addr: 0x793ffc, size: 0x60
    // 0x793ffc: EnterFrame
    //     0x793ffc: stp             fp, lr, [SP, #-0x10]!
    //     0x794000: mov             fp, SP
    // 0x794004: AllocStack(0x8)
    //     0x794004: sub             SP, SP, #8
    // 0x794008: LoadField: r0 = r1->field_2f
    //     0x794008: ldur            w0, [x1, #0x2f]
    // 0x79400c: DecompressPointer r0
    //     0x79400c: add             x0, x0, HEAP, lsl #32
    // 0x794010: LoadField: r3 = r0->field_b
    //     0x794010: ldur            w3, [x0, #0xb]
    // 0x794014: DecompressPointer r3
    //     0x794014: add             x3, x3, HEAP, lsl #32
    // 0x794018: mov             x0, x3
    // 0x79401c: stur            x3, [fp, #-8]
    // 0x794020: r2 = Null
    //     0x794020: mov             x2, NULL
    // 0x794024: r1 = Null
    //     0x794024: mov             x1, NULL
    // 0x794028: r4 = LoadClassIdInstr(r0)
    //     0x794028: ldur            x4, [x0, #-1]
    //     0x79402c: ubfx            x4, x4, #0xc, #0x14
    // 0x794030: cmp             x4, #0xb71
    // 0x794034: b.eq            #0x79404c
    // 0x794038: r8 = ShaderMaskLayer?
    //     0x794038: add             x8, PP, #0x57, lsl #12  ; [pp+0x57f98] Type: ShaderMaskLayer?
    //     0x79403c: ldr             x8, [x8, #0xf98]
    // 0x794040: r3 = Null
    //     0x794040: add             x3, PP, #0x57, lsl #12  ; [pp+0x57fc8] Null
    //     0x794044: ldr             x3, [x3, #0xfc8]
    // 0x794048: r0 = DefaultNullableTypeTest()
    //     0x794048: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x79404c: ldur            x0, [fp, #-8]
    // 0x794050: LeaveFrame
    //     0x794050: mov             SP, fp
    //     0x794054: ldp             fp, lr, [SP], #0x10
    // 0x794058: ret
    //     0x794058: ret             
  }
  _ _ShimmerFilter(/* No info */) {
    // ** addr: 0x860760, size: 0xa8
    // 0x860760: EnterFrame
    //     0x860760: stp             fp, lr, [SP, #-0x10]!
    //     0x860764: mov             fp, SP
    // 0x860768: AllocStack(0x8)
    //     0x860768: sub             SP, SP, #8
    // 0x86076c: r0 = Instance_ShimmerDirection
    //     0x86076c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38310] Obj!ShimmerDirection@e2e161
    //     0x860770: ldr             x0, [x0, #0x310]
    // 0x860774: stur            x1, [fp, #-8]
    // 0x860778: mov             x16, x2
    // 0x86077c: mov             x2, x1
    // 0x860780: mov             x1, x16
    // 0x860784: CheckStackOverflow
    //     0x860784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x860788: cmp             SP, x16
    //     0x86078c: b.ls            #0x860800
    // 0x860790: StoreField: r2->field_63 = d0
    //     0x860790: stur            d0, [x2, #0x63]
    // 0x860794: StoreField: r2->field_5b = r0
    //     0x860794: stur            w0, [x2, #0x5b]
    // 0x860798: mov             x0, x1
    // 0x86079c: StoreField: r2->field_5f = r0
    //     0x86079c: stur            w0, [x2, #0x5f]
    //     0x8607a0: ldurb           w16, [x2, #-1]
    //     0x8607a4: ldurb           w17, [x0, #-1]
    //     0x8607a8: and             x16, x17, x16, lsr #2
    //     0x8607ac: tst             x16, HEAP, lsr #32
    //     0x8607b0: b.eq            #0x8607b8
    //     0x8607b4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8607b8: r0 = _LayoutCacheStorage()
    //     0x8607b8: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x8607bc: ldur            x2, [fp, #-8]
    // 0x8607c0: StoreField: r2->field_4f = r0
    //     0x8607c0: stur            w0, [x2, #0x4f]
    //     0x8607c4: ldurb           w16, [x2, #-1]
    //     0x8607c8: ldurb           w17, [x0, #-1]
    //     0x8607cc: and             x16, x17, x16, lsr #2
    //     0x8607d0: tst             x16, HEAP, lsr #32
    //     0x8607d4: b.eq            #0x8607dc
    //     0x8607d8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8607dc: mov             x1, x2
    // 0x8607e0: r0 = RenderObject()
    //     0x8607e0: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x8607e4: ldur            x1, [fp, #-8]
    // 0x8607e8: r2 = Null
    //     0x8607e8: mov             x2, NULL
    // 0x8607ec: r0 = child=()
    //     0x8607ec: bl              #0x895c88  ; [package:flutter/src/rendering/shifted_box.dart] _RenderShiftedBox&RenderBox&RenderObjectWithChildMixin::child=
    // 0x8607f0: r0 = Null
    //     0x8607f0: mov             x0, NULL
    // 0x8607f4: LeaveFrame
    //     0x8607f4: mov             SP, fp
    //     0x8607f8: ldp             fp, lr, [SP], #0x10
    // 0x8607fc: ret
    //     0x8607fc: ret             
    // 0x860800: r0 = StackOverflowSharedWithFPURegs()
    //     0x860800: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x860804: b               #0x860790
  }
  set _ gradient=(/* No info */) {
    // ** addr: 0xc727e4, size: 0x88
    // 0xc727e4: EnterFrame
    //     0xc727e4: stp             fp, lr, [SP, #-0x10]!
    //     0xc727e8: mov             fp, SP
    // 0xc727ec: AllocStack(0x20)
    //     0xc727ec: sub             SP, SP, #0x20
    // 0xc727f0: SetupParameters(_ShimmerFilter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc727f0: mov             x0, x2
    //     0xc727f4: stur            x1, [fp, #-8]
    //     0xc727f8: stur            x2, [fp, #-0x10]
    // 0xc727fc: CheckStackOverflow
    //     0xc727fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72800: cmp             SP, x16
    //     0xc72804: b.ls            #0xc72864
    // 0xc72808: LoadField: r2 = r1->field_5f
    //     0xc72808: ldur            w2, [x1, #0x5f]
    // 0xc7280c: DecompressPointer r2
    //     0xc7280c: add             x2, x2, HEAP, lsl #32
    // 0xc72810: stp             x2, x0, [SP]
    // 0xc72814: r0 = ==()
    //     0xc72814: bl              #0xd60f10  ; [package:flutter/src/painting/gradient.dart] LinearGradient::==
    // 0xc72818: tbnz            w0, #4, #0xc7282c
    // 0xc7281c: r0 = Null
    //     0xc7281c: mov             x0, NULL
    // 0xc72820: LeaveFrame
    //     0xc72820: mov             SP, fp
    //     0xc72824: ldp             fp, lr, [SP], #0x10
    // 0xc72828: ret
    //     0xc72828: ret             
    // 0xc7282c: ldur            x1, [fp, #-8]
    // 0xc72830: ldur            x0, [fp, #-0x10]
    // 0xc72834: StoreField: r1->field_5f = r0
    //     0xc72834: stur            w0, [x1, #0x5f]
    //     0xc72838: ldurb           w16, [x1, #-1]
    //     0xc7283c: ldurb           w17, [x0, #-1]
    //     0xc72840: and             x16, x17, x16, lsr #2
    //     0xc72844: tst             x16, HEAP, lsr #32
    //     0xc72848: b.eq            #0xc72850
    //     0xc7284c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc72850: r0 = markNeedsPaint()
    //     0xc72850: bl              #0x786214  ; [package:flutter/src/rendering/object.dart] RenderObject::markNeedsPaint
    // 0xc72854: r0 = Null
    //     0xc72854: mov             x0, NULL
    // 0xc72858: LeaveFrame
    //     0xc72858: mov             SP, fp
    //     0xc7285c: ldp             fp, lr, [SP], #0x10
    // 0xc72860: ret
    //     0xc72860: ret             
    // 0xc72864: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc72864: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc72868: b               #0xc72808
  }
  set _ percent=(/* No info */) {
    // ** addr: 0xc7286c, size: 0x50
    // 0xc7286c: EnterFrame
    //     0xc7286c: stp             fp, lr, [SP, #-0x10]!
    //     0xc72870: mov             fp, SP
    // 0xc72874: CheckStackOverflow
    //     0xc72874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72878: cmp             SP, x16
    //     0xc7287c: b.ls            #0xc728b4
    // 0xc72880: LoadField: d1 = r1->field_63
    //     0xc72880: ldur            d1, [x1, #0x63]
    // 0xc72884: fcmp            d0, d1
    // 0xc72888: b.ne            #0xc7289c
    // 0xc7288c: r0 = Null
    //     0xc7288c: mov             x0, NULL
    // 0xc72890: LeaveFrame
    //     0xc72890: mov             SP, fp
    //     0xc72894: ldp             fp, lr, [SP], #0x10
    // 0xc72898: ret
    //     0xc72898: ret             
    // 0xc7289c: StoreField: r1->field_63 = d0
    //     0xc7289c: stur            d0, [x1, #0x63]
    // 0xc728a0: r0 = markNeedsPaint()
    //     0xc728a0: bl              #0x786214  ; [package:flutter/src/rendering/object.dart] RenderObject::markNeedsPaint
    // 0xc728a4: r0 = Null
    //     0xc728a4: mov             x0, NULL
    // 0xc728a8: LeaveFrame
    //     0xc728a8: mov             SP, fp
    //     0xc728ac: ldp             fp, lr, [SP], #0x10
    // 0xc728b0: ret
    //     0xc728b0: ret             
    // 0xc728b4: r0 = StackOverflowSharedWithFPURegs()
    //     0xc728b4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc728b8: b               #0xc72880
  }
}

// class id: 4089, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ShimmerState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6fa994, size: 0x98
    // 0x6fa994: EnterFrame
    //     0x6fa994: stp             fp, lr, [SP, #-0x10]!
    //     0x6fa998: mov             fp, SP
    // 0x6fa99c: AllocStack(0x10)
    //     0x6fa99c: sub             SP, SP, #0x10
    // 0x6fa9a0: SetupParameters(__ShimmerState&State&SingleTickerProviderStateMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6fa9a0: stur            x1, [fp, #-8]
    //     0x6fa9a4: stur            x2, [fp, #-0x10]
    // 0x6fa9a8: CheckStackOverflow
    //     0x6fa9a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fa9ac: cmp             SP, x16
    //     0x6fa9b0: b.ls            #0x6faa20
    // 0x6fa9b4: r0 = Ticker()
    //     0x6fa9b4: bl              #0x6efe64  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x6fa9b8: mov             x1, x0
    // 0x6fa9bc: r0 = false
    //     0x6fa9bc: add             x0, NULL, #0x30  ; false
    // 0x6fa9c0: StoreField: r1->field_b = r0
    //     0x6fa9c0: stur            w0, [x1, #0xb]
    // 0x6fa9c4: ldur            x0, [fp, #-0x10]
    // 0x6fa9c8: StoreField: r1->field_13 = r0
    //     0x6fa9c8: stur            w0, [x1, #0x13]
    // 0x6fa9cc: mov             x0, x1
    // 0x6fa9d0: ldur            x2, [fp, #-8]
    // 0x6fa9d4: StoreField: r2->field_13 = r0
    //     0x6fa9d4: stur            w0, [x2, #0x13]
    //     0x6fa9d8: ldurb           w16, [x2, #-1]
    //     0x6fa9dc: ldurb           w17, [x0, #-1]
    //     0x6fa9e0: and             x16, x17, x16, lsr #2
    //     0x6fa9e4: tst             x16, HEAP, lsr #32
    //     0x6fa9e8: b.eq            #0x6fa9f0
    //     0x6fa9ec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6fa9f0: mov             x1, x2
    // 0x6fa9f4: r0 = _updateTickerModeNotifier()
    //     0x6fa9f4: bl              #0x6faa50  ; [package:shimmer/shimmer.dart] __ShimmerState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6fa9f8: ldur            x1, [fp, #-8]
    // 0x6fa9fc: r0 = _updateTicker()
    //     0x6fa9fc: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6faa00: ldur            x1, [fp, #-8]
    // 0x6faa04: LoadField: r0 = r1->field_13
    //     0x6faa04: ldur            w0, [x1, #0x13]
    // 0x6faa08: DecompressPointer r0
    //     0x6faa08: add             x0, x0, HEAP, lsl #32
    // 0x6faa0c: cmp             w0, NULL
    // 0x6faa10: b.eq            #0x6faa28
    // 0x6faa14: LeaveFrame
    //     0x6faa14: mov             SP, fp
    //     0x6faa18: ldp             fp, lr, [SP], #0x10
    // 0x6faa1c: ret
    //     0x6faa1c: ret             
    // 0x6faa20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6faa20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6faa24: b               #0x6fa9b4
    // 0x6faa28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6faa28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6faa50, size: 0x124
    // 0x6faa50: EnterFrame
    //     0x6faa50: stp             fp, lr, [SP, #-0x10]!
    //     0x6faa54: mov             fp, SP
    // 0x6faa58: AllocStack(0x18)
    //     0x6faa58: sub             SP, SP, #0x18
    // 0x6faa5c: SetupParameters(__ShimmerState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6faa5c: mov             x2, x1
    //     0x6faa60: stur            x1, [fp, #-8]
    // 0x6faa64: CheckStackOverflow
    //     0x6faa64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6faa68: cmp             SP, x16
    //     0x6faa6c: b.ls            #0x6fab68
    // 0x6faa70: LoadField: r1 = r2->field_f
    //     0x6faa70: ldur            w1, [x2, #0xf]
    // 0x6faa74: DecompressPointer r1
    //     0x6faa74: add             x1, x1, HEAP, lsl #32
    // 0x6faa78: cmp             w1, NULL
    // 0x6faa7c: b.eq            #0x6fab70
    // 0x6faa80: r0 = getNotifier()
    //     0x6faa80: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6faa84: mov             x3, x0
    // 0x6faa88: ldur            x0, [fp, #-8]
    // 0x6faa8c: stur            x3, [fp, #-0x18]
    // 0x6faa90: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6faa90: ldur            w4, [x0, #0x17]
    // 0x6faa94: DecompressPointer r4
    //     0x6faa94: add             x4, x4, HEAP, lsl #32
    // 0x6faa98: stur            x4, [fp, #-0x10]
    // 0x6faa9c: cmp             w3, w4
    // 0x6faaa0: b.ne            #0x6faab4
    // 0x6faaa4: r0 = Null
    //     0x6faaa4: mov             x0, NULL
    // 0x6faaa8: LeaveFrame
    //     0x6faaa8: mov             SP, fp
    //     0x6faaac: ldp             fp, lr, [SP], #0x10
    // 0x6faab0: ret
    //     0x6faab0: ret             
    // 0x6faab4: cmp             w4, NULL
    // 0x6faab8: b.eq            #0x6faafc
    // 0x6faabc: mov             x2, x0
    // 0x6faac0: r1 = Function '_updateTicker@364311458':.
    //     0x6faac0: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a420] AnonymousClosure: (0x6fab74), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6faac4: ldr             x1, [x1, #0x420]
    // 0x6faac8: r0 = AllocateClosure()
    //     0x6faac8: bl              #0xec1630  ; AllocateClosureStub
    // 0x6faacc: ldur            x1, [fp, #-0x10]
    // 0x6faad0: r2 = LoadClassIdInstr(r1)
    //     0x6faad0: ldur            x2, [x1, #-1]
    //     0x6faad4: ubfx            x2, x2, #0xc, #0x14
    // 0x6faad8: mov             x16, x0
    // 0x6faadc: mov             x0, x2
    // 0x6faae0: mov             x2, x16
    // 0x6faae4: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6faae4: movz            x17, #0xbf5c
    //     0x6faae8: add             lr, x0, x17
    //     0x6faaec: ldr             lr, [x21, lr, lsl #3]
    //     0x6faaf0: blr             lr
    // 0x6faaf4: ldur            x0, [fp, #-8]
    // 0x6faaf8: ldur            x3, [fp, #-0x18]
    // 0x6faafc: mov             x2, x0
    // 0x6fab00: r1 = Function '_updateTicker@364311458':.
    //     0x6fab00: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a420] AnonymousClosure: (0x6fab74), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6fab04: ldr             x1, [x1, #0x420]
    // 0x6fab08: r0 = AllocateClosure()
    //     0x6fab08: bl              #0xec1630  ; AllocateClosureStub
    // 0x6fab0c: ldur            x3, [fp, #-0x18]
    // 0x6fab10: r1 = LoadClassIdInstr(r3)
    //     0x6fab10: ldur            x1, [x3, #-1]
    //     0x6fab14: ubfx            x1, x1, #0xc, #0x14
    // 0x6fab18: mov             x2, x0
    // 0x6fab1c: mov             x0, x1
    // 0x6fab20: mov             x1, x3
    // 0x6fab24: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6fab24: movz            x17, #0xc407
    //     0x6fab28: add             lr, x0, x17
    //     0x6fab2c: ldr             lr, [x21, lr, lsl #3]
    //     0x6fab30: blr             lr
    // 0x6fab34: ldur            x0, [fp, #-0x18]
    // 0x6fab38: ldur            x1, [fp, #-8]
    // 0x6fab3c: ArrayStore: r1[0] = r0  ; List_4
    //     0x6fab3c: stur            w0, [x1, #0x17]
    //     0x6fab40: ldurb           w16, [x1, #-1]
    //     0x6fab44: ldurb           w17, [x0, #-1]
    //     0x6fab48: and             x16, x17, x16, lsr #2
    //     0x6fab4c: tst             x16, HEAP, lsr #32
    //     0x6fab50: b.eq            #0x6fab58
    //     0x6fab54: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6fab58: r0 = Null
    //     0x6fab58: mov             x0, NULL
    // 0x6fab5c: LeaveFrame
    //     0x6fab5c: mov             SP, fp
    //     0x6fab60: ldp             fp, lr, [SP], #0x10
    // 0x6fab64: ret
    //     0x6fab64: ret             
    // 0x6fab68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fab68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fab6c: b               #0x6faa70
    // 0x6fab70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fab70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTicker(dynamic) {
    // ** addr: 0x6fab74, size: 0x38
    // 0x6fab74: EnterFrame
    //     0x6fab74: stp             fp, lr, [SP, #-0x10]!
    //     0x6fab78: mov             fp, SP
    // 0x6fab7c: ldr             x0, [fp, #0x10]
    // 0x6fab80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6fab80: ldur            w1, [x0, #0x17]
    // 0x6fab84: DecompressPointer r1
    //     0x6fab84: add             x1, x1, HEAP, lsl #32
    // 0x6fab88: CheckStackOverflow
    //     0x6fab88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fab8c: cmp             SP, x16
    //     0x6fab90: b.ls            #0x6faba4
    // 0x6fab94: r0 = _updateTicker()
    //     0x6fab94: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6fab98: LeaveFrame
    //     0x6fab98: mov             SP, fp
    //     0x6fab9c: ldp             fp, lr, [SP], #0x10
    // 0x6faba0: ret
    //     0x6faba0: ret             
    // 0x6faba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6faba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6faba8: b               #0x6fab94
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83fb8, size: 0x94
    // 0xa83fb8: EnterFrame
    //     0xa83fb8: stp             fp, lr, [SP, #-0x10]!
    //     0xa83fbc: mov             fp, SP
    // 0xa83fc0: AllocStack(0x10)
    //     0xa83fc0: sub             SP, SP, #0x10
    // 0xa83fc4: SetupParameters(__ShimmerState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa83fc4: mov             x0, x1
    //     0xa83fc8: stur            x1, [fp, #-0x10]
    // 0xa83fcc: CheckStackOverflow
    //     0xa83fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83fd0: cmp             SP, x16
    //     0xa83fd4: b.ls            #0xa84044
    // 0xa83fd8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa83fd8: ldur            w3, [x0, #0x17]
    // 0xa83fdc: DecompressPointer r3
    //     0xa83fdc: add             x3, x3, HEAP, lsl #32
    // 0xa83fe0: stur            x3, [fp, #-8]
    // 0xa83fe4: cmp             w3, NULL
    // 0xa83fe8: b.ne            #0xa83ff4
    // 0xa83fec: mov             x1, x0
    // 0xa83ff0: b               #0xa84030
    // 0xa83ff4: mov             x2, x0
    // 0xa83ff8: r1 = Function '_updateTicker@364311458':.
    //     0xa83ff8: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a420] AnonymousClosure: (0x6fab74), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0xa83ffc: ldr             x1, [x1, #0x420]
    // 0xa84000: r0 = AllocateClosure()
    //     0xa84000: bl              #0xec1630  ; AllocateClosureStub
    // 0xa84004: ldur            x1, [fp, #-8]
    // 0xa84008: r2 = LoadClassIdInstr(r1)
    //     0xa84008: ldur            x2, [x1, #-1]
    //     0xa8400c: ubfx            x2, x2, #0xc, #0x14
    // 0xa84010: mov             x16, x0
    // 0xa84014: mov             x0, x2
    // 0xa84018: mov             x2, x16
    // 0xa8401c: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa8401c: movz            x17, #0xbf5c
    //     0xa84020: add             lr, x0, x17
    //     0xa84024: ldr             lr, [x21, lr, lsl #3]
    //     0xa84028: blr             lr
    // 0xa8402c: ldur            x1, [fp, #-0x10]
    // 0xa84030: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa84030: stur            NULL, [x1, #0x17]
    // 0xa84034: r0 = Null
    //     0xa84034: mov             x0, NULL
    // 0xa84038: LeaveFrame
    //     0xa84038: mov             SP, fp
    //     0xa8403c: ldp             fp, lr, [SP], #0x10
    // 0xa84040: ret
    //     0xa84040: ret             
    // 0xa84044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa84044: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa84048: b               #0xa83fd8
  }
  _ activate(/* No info */) {
    // ** addr: 0xa8605c, size: 0x48
    // 0xa8605c: EnterFrame
    //     0xa8605c: stp             fp, lr, [SP, #-0x10]!
    //     0xa86060: mov             fp, SP
    // 0xa86064: AllocStack(0x8)
    //     0xa86064: sub             SP, SP, #8
    // 0xa86068: SetupParameters(__ShimmerState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa86068: mov             x0, x1
    //     0xa8606c: stur            x1, [fp, #-8]
    // 0xa86070: CheckStackOverflow
    //     0xa86070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa86074: cmp             SP, x16
    //     0xa86078: b.ls            #0xa8609c
    // 0xa8607c: mov             x1, x0
    // 0xa86080: r0 = _updateTickerModeNotifier()
    //     0xa86080: bl              #0x6faa50  ; [package:shimmer/shimmer.dart] __ShimmerState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa86084: ldur            x1, [fp, #-8]
    // 0xa86088: r0 = _updateTicker()
    //     0xa86088: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0xa8608c: r0 = Null
    //     0xa8608c: mov             x0, NULL
    // 0xa86090: LeaveFrame
    //     0xa86090: mov             SP, fp
    //     0xa86094: ldp             fp, lr, [SP], #0x10
    // 0xa86098: ret
    //     0xa86098: ret             
    // 0xa8609c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8609c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa860a0: b               #0xa8607c
  }
}

// class id: 4090, size: 0x28, field offset: 0x1c
class _ShimmerState extends __ShimmerState&State&SingleTickerProviderStateMixin {

  late AnimationController _controller; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x97fb14, size: 0xec
    // 0x97fb14: EnterFrame
    //     0x97fb14: stp             fp, lr, [SP, #-0x10]!
    //     0x97fb18: mov             fp, SP
    // 0x97fb1c: AllocStack(0x20)
    //     0x97fb1c: sub             SP, SP, #0x20
    // 0x97fb20: SetupParameters(_ShimmerState this /* r1 => r2, fp-0x8 */)
    //     0x97fb20: mov             x2, x1
    //     0x97fb24: stur            x1, [fp, #-8]
    // 0x97fb28: CheckStackOverflow
    //     0x97fb28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97fb2c: cmp             SP, x16
    //     0x97fb30: b.ls            #0x97fbf0
    // 0x97fb34: r1 = 1
    //     0x97fb34: movz            x1, #0x1
    // 0x97fb38: r0 = AllocateContext()
    //     0x97fb38: bl              #0xec126c  ; AllocateContextStub
    // 0x97fb3c: ldur            x2, [fp, #-8]
    // 0x97fb40: stur            x0, [fp, #-0x10]
    // 0x97fb44: StoreField: r0->field_f = r2
    //     0x97fb44: stur            w2, [x0, #0xf]
    // 0x97fb48: LoadField: r1 = r2->field_b
    //     0x97fb48: ldur            w1, [x2, #0xb]
    // 0x97fb4c: DecompressPointer r1
    //     0x97fb4c: add             x1, x1, HEAP, lsl #32
    // 0x97fb50: cmp             w1, NULL
    // 0x97fb54: b.eq            #0x97fbf8
    // 0x97fb58: r1 = <double>
    //     0x97fb58: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x97fb5c: r0 = AnimationController()
    //     0x97fb5c: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x97fb60: stur            x0, [fp, #-0x18]
    // 0x97fb64: r16 = Instance_Duration
    //     0x97fb64: add             x16, PP, #0x38, lsl #12  ; [pp+0x38308] Obj!Duration@e3a181
    //     0x97fb68: ldr             x16, [x16, #0x308]
    // 0x97fb6c: str             x16, [SP]
    // 0x97fb70: mov             x1, x0
    // 0x97fb74: ldur            x2, [fp, #-8]
    // 0x97fb78: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x97fb78: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x97fb7c: ldr             x4, [x4, #0x408]
    // 0x97fb80: r0 = AnimationController()
    //     0x97fb80: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x97fb84: ldur            x2, [fp, #-0x10]
    // 0x97fb88: r1 = Function '<anonymous closure>':.
    //     0x97fb88: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a460] AnonymousClosure: (0x97fc00), in [package:shimmer/shimmer.dart] _ShimmerState::initState (0x97fb14)
    //     0x97fb8c: ldr             x1, [x1, #0x460]
    // 0x97fb90: r0 = AllocateClosure()
    //     0x97fb90: bl              #0xec1630  ; AllocateClosureStub
    // 0x97fb94: ldur            x1, [fp, #-0x18]
    // 0x97fb98: mov             x2, x0
    // 0x97fb9c: r0 = addStatusListener()
    //     0x97fb9c: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x97fba0: ldur            x0, [fp, #-0x18]
    // 0x97fba4: ldur            x1, [fp, #-8]
    // 0x97fba8: StoreField: r1->field_1b = r0
    //     0x97fba8: stur            w0, [x1, #0x1b]
    //     0x97fbac: ldurb           w16, [x1, #-1]
    //     0x97fbb0: ldurb           w17, [x0, #-1]
    //     0x97fbb4: and             x16, x17, x16, lsr #2
    //     0x97fbb8: tst             x16, HEAP, lsr #32
    //     0x97fbbc: b.eq            #0x97fbc4
    //     0x97fbc0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97fbc4: LoadField: r0 = r1->field_b
    //     0x97fbc4: ldur            w0, [x1, #0xb]
    // 0x97fbc8: DecompressPointer r0
    //     0x97fbc8: add             x0, x0, HEAP, lsl #32
    // 0x97fbcc: cmp             w0, NULL
    // 0x97fbd0: b.eq            #0x97fbfc
    // 0x97fbd4: ldur            x1, [fp, #-0x18]
    // 0x97fbd8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x97fbd8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x97fbdc: r0 = forward()
    //     0x97fbdc: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x97fbe0: r0 = Null
    //     0x97fbe0: mov             x0, NULL
    // 0x97fbe4: LeaveFrame
    //     0x97fbe4: mov             SP, fp
    //     0x97fbe8: ldp             fp, lr, [SP], #0x10
    // 0x97fbec: ret
    //     0x97fbec: ret             
    // 0x97fbf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97fbf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97fbf4: b               #0x97fb34
    // 0x97fbf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97fbf8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97fbfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97fbfc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, AnimationStatus) {
    // ** addr: 0x97fc00, size: 0xa8
    // 0x97fc00: EnterFrame
    //     0x97fc00: stp             fp, lr, [SP, #-0x10]!
    //     0x97fc04: mov             fp, SP
    // 0x97fc08: ldr             x0, [fp, #0x18]
    // 0x97fc0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97fc0c: ldur            w1, [x0, #0x17]
    // 0x97fc10: DecompressPointer r1
    //     0x97fc10: add             x1, x1, HEAP, lsl #32
    // 0x97fc14: CheckStackOverflow
    //     0x97fc14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97fc18: cmp             SP, x16
    //     0x97fc1c: b.ls            #0x97fc90
    // 0x97fc20: ldr             x0, [fp, #0x10]
    // 0x97fc24: r16 = Instance_AnimationStatus
    //     0x97fc24: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x97fc28: cmp             w0, w16
    // 0x97fc2c: b.eq            #0x97fc40
    // 0x97fc30: r0 = Null
    //     0x97fc30: mov             x0, NULL
    // 0x97fc34: LeaveFrame
    //     0x97fc34: mov             SP, fp
    //     0x97fc38: ldp             fp, lr, [SP], #0x10
    // 0x97fc3c: ret
    //     0x97fc3c: ret             
    // 0x97fc40: LoadField: r0 = r1->field_f
    //     0x97fc40: ldur            w0, [x1, #0xf]
    // 0x97fc44: DecompressPointer r0
    //     0x97fc44: add             x0, x0, HEAP, lsl #32
    // 0x97fc48: LoadField: r1 = r0->field_1f
    //     0x97fc48: ldur            x1, [x0, #0x1f]
    // 0x97fc4c: add             x2, x1, #1
    // 0x97fc50: StoreField: r0->field_1f = r2
    //     0x97fc50: stur            x2, [x0, #0x1f]
    // 0x97fc54: LoadField: r1 = r0->field_b
    //     0x97fc54: ldur            w1, [x0, #0xb]
    // 0x97fc58: DecompressPointer r1
    //     0x97fc58: add             x1, x1, HEAP, lsl #32
    // 0x97fc5c: cmp             w1, NULL
    // 0x97fc60: b.eq            #0x97fc98
    // 0x97fc64: LoadField: r1 = r0->field_1b
    //     0x97fc64: ldur            w1, [x0, #0x1b]
    // 0x97fc68: DecompressPointer r1
    //     0x97fc68: add             x1, x1, HEAP, lsl #32
    // 0x97fc6c: r16 = Sentinel
    //     0x97fc6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97fc70: cmp             w1, w16
    // 0x97fc74: b.eq            #0x97fc9c
    // 0x97fc78: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x97fc78: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x97fc7c: r0 = repeat()
    //     0x97fc7c: bl              #0x92d7b0  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::repeat
    // 0x97fc80: r0 = Null
    //     0x97fc80: mov             x0, NULL
    // 0x97fc84: LeaveFrame
    //     0x97fc84: mov             SP, fp
    //     0x97fc88: ldp             fp, lr, [SP], #0x10
    // 0x97fc8c: ret
    //     0x97fc8c: ret             
    // 0x97fc90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97fc90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97fc94: b               #0x97fc20
    // 0x97fc98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97fc98: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97fc9c: r9 = _controller
    //     0x97fc9c: add             x9, PP, #0x4a, lsl #12  ; [pp+0x4a428] Field <_ShimmerState@**********._controller@**********>: late (offset: 0x1c)
    //     0x97fca0: ldr             x9, [x9, #0x428]
    // 0x97fca4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x97fca4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a1b58, size: 0xf8
    // 0x9a1b58: EnterFrame
    //     0x9a1b58: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1b5c: mov             fp, SP
    // 0x9a1b60: AllocStack(0x10)
    //     0x9a1b60: sub             SP, SP, #0x10
    // 0x9a1b64: SetupParameters(_ShimmerState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a1b64: mov             x4, x1
    //     0x9a1b68: mov             x3, x2
    //     0x9a1b6c: stur            x1, [fp, #-8]
    //     0x9a1b70: stur            x2, [fp, #-0x10]
    // 0x9a1b74: CheckStackOverflow
    //     0x9a1b74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1b78: cmp             SP, x16
    //     0x9a1b7c: b.ls            #0x9a1c38
    // 0x9a1b80: mov             x0, x3
    // 0x9a1b84: r2 = Null
    //     0x9a1b84: mov             x2, NULL
    // 0x9a1b88: r1 = Null
    //     0x9a1b88: mov             x1, NULL
    // 0x9a1b8c: r4 = 60
    //     0x9a1b8c: movz            x4, #0x3c
    // 0x9a1b90: branchIfSmi(r0, 0x9a1b9c)
    //     0x9a1b90: tbz             w0, #0, #0x9a1b9c
    // 0x9a1b94: r4 = LoadClassIdInstr(r0)
    //     0x9a1b94: ldur            x4, [x0, #-1]
    //     0x9a1b98: ubfx            x4, x4, #0xc, #0x14
    // 0x9a1b9c: r17 = 4694
    //     0x9a1b9c: movz            x17, #0x1256
    // 0x9a1ba0: cmp             x4, x17
    // 0x9a1ba4: b.eq            #0x9a1bbc
    // 0x9a1ba8: r8 = Shimmer
    //     0x9a1ba8: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a438] Type: Shimmer
    //     0x9a1bac: ldr             x8, [x8, #0x438]
    // 0x9a1bb0: r3 = Null
    //     0x9a1bb0: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a440] Null
    //     0x9a1bb4: ldr             x3, [x3, #0x440]
    // 0x9a1bb8: r0 = Shimmer()
    //     0x9a1bb8: bl              #0x6faa2c  ; IsType_Shimmer_Stub
    // 0x9a1bbc: ldur            x0, [fp, #-8]
    // 0x9a1bc0: LoadField: r1 = r0->field_b
    //     0x9a1bc0: ldur            w1, [x0, #0xb]
    // 0x9a1bc4: DecompressPointer r1
    //     0x9a1bc4: add             x1, x1, HEAP, lsl #32
    // 0x9a1bc8: cmp             w1, NULL
    // 0x9a1bcc: b.eq            #0x9a1c40
    // 0x9a1bd0: LoadField: r1 = r0->field_1b
    //     0x9a1bd0: ldur            w1, [x0, #0x1b]
    // 0x9a1bd4: DecompressPointer r1
    //     0x9a1bd4: add             x1, x1, HEAP, lsl #32
    // 0x9a1bd8: r16 = Sentinel
    //     0x9a1bd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a1bdc: cmp             w1, w16
    // 0x9a1be0: b.eq            #0x9a1c44
    // 0x9a1be4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9a1be4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9a1be8: r0 = forward()
    //     0x9a1be8: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x9a1bec: ldur            x0, [fp, #-8]
    // 0x9a1bf0: LoadField: r2 = r0->field_7
    //     0x9a1bf0: ldur            w2, [x0, #7]
    // 0x9a1bf4: DecompressPointer r2
    //     0x9a1bf4: add             x2, x2, HEAP, lsl #32
    // 0x9a1bf8: ldur            x0, [fp, #-0x10]
    // 0x9a1bfc: r1 = Null
    //     0x9a1bfc: mov             x1, NULL
    // 0x9a1c00: cmp             w2, NULL
    // 0x9a1c04: b.eq            #0x9a1c28
    // 0x9a1c08: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a1c08: ldur            w4, [x2, #0x17]
    // 0x9a1c0c: DecompressPointer r4
    //     0x9a1c0c: add             x4, x4, HEAP, lsl #32
    // 0x9a1c10: r8 = X0 bound StatefulWidget
    //     0x9a1c10: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a1c14: ldr             x8, [x8, #0x7f8]
    // 0x9a1c18: LoadField: r9 = r4->field_7
    //     0x9a1c18: ldur            x9, [x4, #7]
    // 0x9a1c1c: r3 = Null
    //     0x9a1c1c: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a450] Null
    //     0x9a1c20: ldr             x3, [x3, #0x450]
    // 0x9a1c24: blr             x9
    // 0x9a1c28: r0 = Null
    //     0x9a1c28: mov             x0, NULL
    // 0x9a1c2c: LeaveFrame
    //     0x9a1c2c: mov             SP, fp
    //     0x9a1c30: ldp             fp, lr, [SP], #0x10
    // 0x9a1c34: ret
    //     0x9a1c34: ret             
    // 0x9a1c38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1c38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1c3c: b               #0x9a1b80
    // 0x9a1c40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1c40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a1c44: r9 = _controller
    //     0x9a1c44: add             x9, PP, #0x4a, lsl #12  ; [pp+0x4a428] Field <_ShimmerState@**********._controller@**********>: late (offset: 0x1c)
    //     0x9a1c48: ldr             x9, [x9, #0x428]
    // 0x9a1c4c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a1c4c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa4c2f8, size: 0xa4
    // 0xa4c2f8: EnterFrame
    //     0xa4c2f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c2fc: mov             fp, SP
    // 0xa4c300: AllocStack(0x18)
    //     0xa4c300: sub             SP, SP, #0x18
    // 0xa4c304: SetupParameters(_ShimmerState this /* r1 => r1, fp-0x8 */)
    //     0xa4c304: stur            x1, [fp, #-8]
    // 0xa4c308: r1 = 1
    //     0xa4c308: movz            x1, #0x1
    // 0xa4c30c: r0 = AllocateContext()
    //     0xa4c30c: bl              #0xec126c  ; AllocateContextStub
    // 0xa4c310: mov             x1, x0
    // 0xa4c314: ldur            x0, [fp, #-8]
    // 0xa4c318: StoreField: r1->field_f = r0
    //     0xa4c318: stur            w0, [x1, #0xf]
    // 0xa4c31c: LoadField: r3 = r0->field_1b
    //     0xa4c31c: ldur            w3, [x0, #0x1b]
    // 0xa4c320: DecompressPointer r3
    //     0xa4c320: add             x3, x3, HEAP, lsl #32
    // 0xa4c324: r16 = Sentinel
    //     0xa4c324: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4c328: cmp             w3, w16
    // 0xa4c32c: b.eq            #0xa4c38c
    // 0xa4c330: stur            x3, [fp, #-0x10]
    // 0xa4c334: LoadField: r2 = r0->field_b
    //     0xa4c334: ldur            w2, [x0, #0xb]
    // 0xa4c338: DecompressPointer r2
    //     0xa4c338: add             x2, x2, HEAP, lsl #32
    // 0xa4c33c: cmp             w2, NULL
    // 0xa4c340: b.eq            #0xa4c398
    // 0xa4c344: LoadField: r0 = r2->field_b
    //     0xa4c344: ldur            w0, [x2, #0xb]
    // 0xa4c348: DecompressPointer r0
    //     0xa4c348: add             x0, x0, HEAP, lsl #32
    // 0xa4c34c: mov             x2, x1
    // 0xa4c350: stur            x0, [fp, #-8]
    // 0xa4c354: r1 = Function '<anonymous closure>':.
    //     0xa4c354: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a430] AnonymousClosure: (0xa4c39c), in [package:shimmer/shimmer.dart] _ShimmerState::build (0xa4c2f8)
    //     0xa4c358: ldr             x1, [x1, #0x430]
    // 0xa4c35c: r0 = AllocateClosure()
    //     0xa4c35c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4c360: stur            x0, [fp, #-0x18]
    // 0xa4c364: r0 = AnimatedBuilder()
    //     0xa4c364: bl              #0x7e5888  ; AllocateAnimatedBuilderStub -> AnimatedBuilder (size=0x18)
    // 0xa4c368: ldur            x1, [fp, #-0x18]
    // 0xa4c36c: StoreField: r0->field_f = r1
    //     0xa4c36c: stur            w1, [x0, #0xf]
    // 0xa4c370: ldur            x1, [fp, #-8]
    // 0xa4c374: StoreField: r0->field_13 = r1
    //     0xa4c374: stur            w1, [x0, #0x13]
    // 0xa4c378: ldur            x1, [fp, #-0x10]
    // 0xa4c37c: StoreField: r0->field_b = r1
    //     0xa4c37c: stur            w1, [x0, #0xb]
    // 0xa4c380: LeaveFrame
    //     0xa4c380: mov             SP, fp
    //     0xa4c384: ldp             fp, lr, [SP], #0x10
    // 0xa4c388: ret
    //     0xa4c388: ret             
    // 0xa4c38c: r9 = _controller
    //     0xa4c38c: add             x9, PP, #0x4a, lsl #12  ; [pp+0x4a428] Field <_ShimmerState@**********._controller@**********>: late (offset: 0x1c)
    //     0xa4c390: ldr             x9, [x9, #0x428]
    // 0xa4c394: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4c394: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4c398: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4c398: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] _Shimmer <anonymous closure>(dynamic, BuildContext, Widget?) {
    // ** addr: 0xa4c39c, size: 0xb8
    // 0xa4c39c: EnterFrame
    //     0xa4c39c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c3a0: mov             fp, SP
    // 0xa4c3a4: AllocStack(0x10)
    //     0xa4c3a4: sub             SP, SP, #0x10
    // 0xa4c3a8: SetupParameters()
    //     0xa4c3a8: ldr             x0, [fp, #0x20]
    //     0xa4c3ac: ldur            w1, [x0, #0x17]
    //     0xa4c3b0: add             x1, x1, HEAP, lsl #32
    // 0xa4c3b4: LoadField: r0 = r1->field_f
    //     0xa4c3b4: ldur            w0, [x1, #0xf]
    // 0xa4c3b8: DecompressPointer r0
    //     0xa4c3b8: add             x0, x0, HEAP, lsl #32
    // 0xa4c3bc: LoadField: r1 = r0->field_b
    //     0xa4c3bc: ldur            w1, [x0, #0xb]
    // 0xa4c3c0: DecompressPointer r1
    //     0xa4c3c0: add             x1, x1, HEAP, lsl #32
    // 0xa4c3c4: cmp             w1, NULL
    // 0xa4c3c8: b.eq            #0xa4c43c
    // 0xa4c3cc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4c3cc: ldur            w2, [x1, #0x17]
    // 0xa4c3d0: DecompressPointer r2
    //     0xa4c3d0: add             x2, x2, HEAP, lsl #32
    // 0xa4c3d4: stur            x2, [fp, #-8]
    // 0xa4c3d8: LoadField: r1 = r0->field_1b
    //     0xa4c3d8: ldur            w1, [x0, #0x1b]
    // 0xa4c3dc: DecompressPointer r1
    //     0xa4c3dc: add             x1, x1, HEAP, lsl #32
    // 0xa4c3e0: r16 = Sentinel
    //     0xa4c3e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4c3e4: cmp             w1, w16
    // 0xa4c3e8: b.eq            #0xa4c440
    // 0xa4c3ec: LoadField: r0 = r1->field_37
    //     0xa4c3ec: ldur            w0, [x1, #0x37]
    // 0xa4c3f0: DecompressPointer r0
    //     0xa4c3f0: add             x0, x0, HEAP, lsl #32
    // 0xa4c3f4: r16 = Sentinel
    //     0xa4c3f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4c3f8: cmp             w0, w16
    // 0xa4c3fc: b.eq            #0xa4c44c
    // 0xa4c400: LoadField: d0 = r0->field_7
    //     0xa4c400: ldur            d0, [x0, #7]
    // 0xa4c404: stur            d0, [fp, #-0x10]
    // 0xa4c408: r0 = _Shimmer()
    //     0xa4c408: bl              #0xa4c454  ; Allocate_ShimmerStub -> _Shimmer (size=0x20)
    // 0xa4c40c: ldur            d0, [fp, #-0x10]
    // 0xa4c410: StoreField: r0->field_f = d0
    //     0xa4c410: stur            d0, [x0, #0xf]
    // 0xa4c414: r1 = Instance_ShimmerDirection
    //     0xa4c414: add             x1, PP, #0x38, lsl #12  ; [pp+0x38310] Obj!ShimmerDirection@e2e161
    //     0xa4c418: ldr             x1, [x1, #0x310]
    // 0xa4c41c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa4c41c: stur            w1, [x0, #0x17]
    // 0xa4c420: ldur            x1, [fp, #-8]
    // 0xa4c424: StoreField: r0->field_1b = r1
    //     0xa4c424: stur            w1, [x0, #0x1b]
    // 0xa4c428: ldr             x1, [fp, #0x10]
    // 0xa4c42c: StoreField: r0->field_b = r1
    //     0xa4c42c: stur            w1, [x0, #0xb]
    // 0xa4c430: LeaveFrame
    //     0xa4c430: mov             SP, fp
    //     0xa4c434: ldp             fp, lr, [SP], #0x10
    // 0xa4c438: ret
    //     0xa4c438: ret             
    // 0xa4c43c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4c43c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4c440: r9 = _controller
    //     0xa4c440: add             x9, PP, #0x4a, lsl #12  ; [pp+0x4a428] Field <_ShimmerState@**********._controller@**********>: late (offset: 0x1c)
    //     0xa4c444: ldr             x9, [x9, #0x428]
    // 0xa4c448: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4c448: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4c44c: r9 = _value
    //     0xa4c44c: ldr             x9, [PP, #0x4ea8]  ; [pp+0x4ea8] Field <AnimationController._value@441066280>: late (offset: 0x38)
    // 0xa4c450: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4c450: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83f54, size: 0x64
    // 0xa83f54: EnterFrame
    //     0xa83f54: stp             fp, lr, [SP, #-0x10]!
    //     0xa83f58: mov             fp, SP
    // 0xa83f5c: AllocStack(0x8)
    //     0xa83f5c: sub             SP, SP, #8
    // 0xa83f60: SetupParameters(_ShimmerState this /* r1 => r0, fp-0x8 */)
    //     0xa83f60: mov             x0, x1
    //     0xa83f64: stur            x1, [fp, #-8]
    // 0xa83f68: CheckStackOverflow
    //     0xa83f68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83f6c: cmp             SP, x16
    //     0xa83f70: b.ls            #0xa83fa4
    // 0xa83f74: LoadField: r1 = r0->field_1b
    //     0xa83f74: ldur            w1, [x0, #0x1b]
    // 0xa83f78: DecompressPointer r1
    //     0xa83f78: add             x1, x1, HEAP, lsl #32
    // 0xa83f7c: r16 = Sentinel
    //     0xa83f7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa83f80: cmp             w1, w16
    // 0xa83f84: b.eq            #0xa83fac
    // 0xa83f88: r0 = dispose()
    //     0xa83f88: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa83f8c: ldur            x1, [fp, #-8]
    // 0xa83f90: r0 = dispose()
    //     0xa83f90: bl              #0xa83fb8  ; [package:shimmer/shimmer.dart] __ShimmerState&State&SingleTickerProviderStateMixin::dispose
    // 0xa83f94: r0 = Null
    //     0xa83f94: mov             x0, NULL
    // 0xa83f98: LeaveFrame
    //     0xa83f98: mov             SP, fp
    //     0xa83f9c: ldp             fp, lr, [SP], #0x10
    // 0xa83fa0: ret
    //     0xa83fa0: ret             
    // 0xa83fa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83fa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83fa8: b               #0xa83f74
    // 0xa83fac: r9 = _controller
    //     0xa83fac: add             x9, PP, #0x4a, lsl #12  ; [pp+0x4a428] Field <_ShimmerState@**********._controller@**********>: late (offset: 0x1c)
    //     0xa83fb0: ldr             x9, [x9, #0x428]
    // 0xa83fb4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa83fb4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4483, size: 0x20, field offset: 0x10
//   const constructor, 
class _Shimmer extends SingleChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x860704, size: 0x5c
    // 0x860704: EnterFrame
    //     0x860704: stp             fp, lr, [SP, #-0x10]!
    //     0x860708: mov             fp, SP
    // 0x86070c: AllocStack(0x10)
    //     0x86070c: sub             SP, SP, #0x10
    // 0x860710: CheckStackOverflow
    //     0x860710: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x860714: cmp             SP, x16
    //     0x860718: b.ls            #0x860758
    // 0x86071c: LoadField: d0 = r1->field_f
    //     0x86071c: ldur            d0, [x1, #0xf]
    // 0x860720: stur            d0, [fp, #-0x10]
    // 0x860724: LoadField: r2 = r1->field_1b
    //     0x860724: ldur            w2, [x1, #0x1b]
    // 0x860728: DecompressPointer r2
    //     0x860728: add             x2, x2, HEAP, lsl #32
    // 0x86072c: stur            x2, [fp, #-8]
    // 0x860730: r0 = _ShimmerFilter()
    //     0x860730: bl              #0x860808  ; Allocate_ShimmerFilterStub -> _ShimmerFilter (size=0x6c)
    // 0x860734: mov             x1, x0
    // 0x860738: ldur            d0, [fp, #-0x10]
    // 0x86073c: ldur            x2, [fp, #-8]
    // 0x860740: stur            x0, [fp, #-8]
    // 0x860744: r0 = _ShimmerFilter()
    //     0x860744: bl              #0x860760  ; [package:shimmer/shimmer.dart] _ShimmerFilter::_ShimmerFilter
    // 0x860748: ldur            x0, [fp, #-8]
    // 0x86074c: LeaveFrame
    //     0x86074c: mov             SP, fp
    //     0x860750: ldp             fp, lr, [SP], #0x10
    // 0x860754: ret
    //     0x860754: ret             
    // 0x860758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x860758: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86075c: b               #0x86071c
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc7273c, size: 0xa8
    // 0xc7273c: EnterFrame
    //     0xc7273c: stp             fp, lr, [SP, #-0x10]!
    //     0xc72740: mov             fp, SP
    // 0xc72744: AllocStack(0x10)
    //     0xc72744: sub             SP, SP, #0x10
    // 0xc72748: SetupParameters(_Shimmer this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc72748: mov             x4, x1
    //     0xc7274c: stur            x1, [fp, #-8]
    //     0xc72750: stur            x3, [fp, #-0x10]
    // 0xc72754: CheckStackOverflow
    //     0xc72754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc72758: cmp             SP, x16
    //     0xc7275c: b.ls            #0xc727dc
    // 0xc72760: mov             x0, x3
    // 0xc72764: r2 = Null
    //     0xc72764: mov             x2, NULL
    // 0xc72768: r1 = Null
    //     0xc72768: mov             x1, NULL
    // 0xc7276c: r4 = 60
    //     0xc7276c: movz            x4, #0x3c
    // 0xc72770: branchIfSmi(r0, 0xc7277c)
    //     0xc72770: tbz             w0, #0, #0xc7277c
    // 0xc72774: r4 = LoadClassIdInstr(r0)
    //     0xc72774: ldur            x4, [x0, #-1]
    //     0xc72778: ubfx            x4, x4, #0xc, #0x14
    // 0xc7277c: cmp             x4, #0xc17
    // 0xc72780: b.eq            #0xc72798
    // 0xc72784: r8 = _ShimmerFilter
    //     0xc72784: add             x8, PP, #0x51, lsl #12  ; [pp+0x51670] Type: _ShimmerFilter
    //     0xc72788: ldr             x8, [x8, #0x670]
    // 0xc7278c: r3 = Null
    //     0xc7278c: add             x3, PP, #0x51, lsl #12  ; [pp+0x51678] Null
    //     0xc72790: ldr             x3, [x3, #0x678]
    // 0xc72794: r0 = DefaultTypeTest()
    //     0xc72794: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc72798: ldur            x0, [fp, #-8]
    // 0xc7279c: LoadField: d0 = r0->field_f
    //     0xc7279c: ldur            d0, [x0, #0xf]
    // 0xc727a0: ldur            x1, [fp, #-0x10]
    // 0xc727a4: r0 = percent=()
    //     0xc727a4: bl              #0xc7286c  ; [package:shimmer/shimmer.dart] _ShimmerFilter::percent=
    // 0xc727a8: ldur            x0, [fp, #-8]
    // 0xc727ac: LoadField: r2 = r0->field_1b
    //     0xc727ac: ldur            w2, [x0, #0x1b]
    // 0xc727b0: DecompressPointer r2
    //     0xc727b0: add             x2, x2, HEAP, lsl #32
    // 0xc727b4: ldur            x1, [fp, #-0x10]
    // 0xc727b8: r0 = gradient=()
    //     0xc727b8: bl              #0xc727e4  ; [package:shimmer/shimmer.dart] _ShimmerFilter::gradient=
    // 0xc727bc: ldur            x1, [fp, #-0x10]
    // 0xc727c0: r2 = Instance_ShimmerDirection
    //     0xc727c0: add             x2, PP, #0x38, lsl #12  ; [pp+0x38310] Obj!ShimmerDirection@e2e161
    //     0xc727c4: ldr             x2, [x2, #0x310]
    // 0xc727c8: r0 = forceCompileTimeTreeShaking()
    //     0xc727c8: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xc727cc: r0 = Null
    //     0xc727cc: mov             x0, NULL
    // 0xc727d0: LeaveFrame
    //     0xc727d0: mov             SP, fp
    //     0xc727d4: ldp             fp, lr, [SP], #0x10
    // 0xc727d8: ret
    //     0xc727d8: ret             
    // 0xc727dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc727dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc727e0: b               #0xc72760
  }
}

// class id: 4694, size: 0x28, field offset: 0xc
//   const constructor, 
class Shimmer extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa95264, size: 0x30
    // 0xa95264: EnterFrame
    //     0xa95264: stp             fp, lr, [SP, #-0x10]!
    //     0xa95268: mov             fp, SP
    // 0xa9526c: mov             x0, x1
    // 0xa95270: r1 = <Shimmer>
    //     0xa95270: add             x1, PP, #0x41, lsl #12  ; [pp+0x413e0] TypeArguments: <Shimmer>
    //     0xa95274: ldr             x1, [x1, #0x3e0]
    // 0xa95278: r0 = _ShimmerState()
    //     0xa95278: bl              #0xa95294  ; Allocate_ShimmerStateStub -> _ShimmerState (size=0x28)
    // 0xa9527c: r1 = Sentinel
    //     0xa9527c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa95280: StoreField: r0->field_1b = r1
    //     0xa95280: stur            w1, [x0, #0x1b]
    // 0xa95284: StoreField: r0->field_1f = rZR
    //     0xa95284: stur            xzr, [x0, #0x1f]
    // 0xa95288: LeaveFrame
    //     0xa95288: mov             SP, fp
    //     0xa9528c: ldp             fp, lr, [SP], #0x10
    // 0xa95290: ret
    //     0xa95290: ret             
  }
  _ Shimmer.fromColors(/* No info */) {
    // ** addr: 0xb804c0, size: 0x128
    // 0xb804c0: EnterFrame
    //     0xb804c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb804c4: mov             fp, SP
    // 0xb804c8: AllocStack(0x20)
    //     0xb804c8: sub             SP, SP, #0x20
    // 0xb804cc: r8 = Instance_Duration
    //     0xb804cc: add             x8, PP, #0x38, lsl #12  ; [pp+0x38308] Obj!Duration@e3a181
    //     0xb804d0: ldr             x8, [x8, #0x308]
    // 0xb804d4: r7 = Instance_ShimmerDirection
    //     0xb804d4: add             x7, PP, #0x38, lsl #12  ; [pp+0x38310] Obj!ShimmerDirection@e2e161
    //     0xb804d8: ldr             x7, [x7, #0x310]
    // 0xb804dc: r6 = true
    //     0xb804dc: add             x6, NULL, #0x20  ; true
    // 0xb804e0: r4 = 10
    //     0xb804e0: movz            x4, #0xa
    // 0xb804e4: mov             x0, x3
    // 0xb804e8: mov             x10, x1
    // 0xb804ec: mov             x9, x2
    // 0xb804f0: stur            x1, [fp, #-8]
    // 0xb804f4: stur            x2, [fp, #-0x10]
    // 0xb804f8: stur            x5, [fp, #-0x18]
    // 0xb804fc: StoreField: r10->field_b = r0
    //     0xb804fc: stur            w0, [x10, #0xb]
    //     0xb80500: ldurb           w16, [x10, #-1]
    //     0xb80504: ldurb           w17, [x0, #-1]
    //     0xb80508: and             x16, x17, x16, lsr #2
    //     0xb8050c: tst             x16, HEAP, lsr #32
    //     0xb80510: b.eq            #0xb80518
    //     0xb80514: bl              #0xec0b48  ; WriteBarrierWrappersStub
    // 0xb80518: StoreField: r10->field_f = r8
    //     0xb80518: stur            w8, [x10, #0xf]
    // 0xb8051c: StoreField: r10->field_13 = r7
    //     0xb8051c: stur            w7, [x10, #0x13]
    // 0xb80520: StoreField: r10->field_1b = rZR
    //     0xb80520: stur            xzr, [x10, #0x1b]
    // 0xb80524: StoreField: r10->field_23 = r6
    //     0xb80524: stur            w6, [x10, #0x23]
    // 0xb80528: mov             x2, x4
    // 0xb8052c: r1 = Null
    //     0xb8052c: mov             x1, NULL
    // 0xb80530: r0 = AllocateArray()
    //     0xb80530: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb80534: mov             x2, x0
    // 0xb80538: ldur            x0, [fp, #-0x10]
    // 0xb8053c: stur            x2, [fp, #-0x20]
    // 0xb80540: StoreField: r2->field_f = r0
    //     0xb80540: stur            w0, [x2, #0xf]
    // 0xb80544: StoreField: r2->field_13 = r0
    //     0xb80544: stur            w0, [x2, #0x13]
    // 0xb80548: ldur            x1, [fp, #-0x18]
    // 0xb8054c: ArrayStore: r2[0] = r1  ; List_4
    //     0xb8054c: stur            w1, [x2, #0x17]
    // 0xb80550: StoreField: r2->field_1b = r0
    //     0xb80550: stur            w0, [x2, #0x1b]
    // 0xb80554: StoreField: r2->field_1f = r0
    //     0xb80554: stur            w0, [x2, #0x1f]
    // 0xb80558: r1 = <Color>
    //     0xb80558: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb8055c: ldr             x1, [x1, #0x158]
    // 0xb80560: r0 = AllocateGrowableArray()
    //     0xb80560: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb80564: mov             x1, x0
    // 0xb80568: ldur            x0, [fp, #-0x20]
    // 0xb8056c: stur            x1, [fp, #-0x10]
    // 0xb80570: StoreField: r1->field_f = r0
    //     0xb80570: stur            w0, [x1, #0xf]
    // 0xb80574: r0 = 10
    //     0xb80574: movz            x0, #0xa
    // 0xb80578: StoreField: r1->field_b = r0
    //     0xb80578: stur            w0, [x1, #0xb]
    // 0xb8057c: r0 = LinearGradient()
    //     0xb8057c: bl              #0x7f25a4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb80580: r1 = Instance_Alignment
    //     0xb80580: add             x1, PP, #0x25, lsl #12  ; [pp+0x25370] Obj!Alignment@e13e11
    //     0xb80584: ldr             x1, [x1, #0x370]
    // 0xb80588: StoreField: r0->field_13 = r1
    //     0xb80588: stur            w1, [x0, #0x13]
    // 0xb8058c: r1 = Instance_Alignment
    //     0xb8058c: add             x1, PP, #0x38, lsl #12  ; [pp+0x38318] Obj!Alignment@e13ef1
    //     0xb80590: ldr             x1, [x1, #0x318]
    // 0xb80594: ArrayStore: r0[0] = r1  ; List_4
    //     0xb80594: stur            w1, [x0, #0x17]
    // 0xb80598: r1 = Instance_TileMode
    //     0xb80598: add             x1, PP, #0x26, lsl #12  ; [pp+0x263e8] Obj!TileMode@e399a1
    //     0xb8059c: ldr             x1, [x1, #0x3e8]
    // 0xb805a0: StoreField: r0->field_1b = r1
    //     0xb805a0: stur            w1, [x0, #0x1b]
    // 0xb805a4: ldur            x1, [fp, #-0x10]
    // 0xb805a8: StoreField: r0->field_7 = r1
    //     0xb805a8: stur            w1, [x0, #7]
    // 0xb805ac: r1 = const [0.0, 0.35, 0.5, 0.65, 1.0]
    //     0xb805ac: add             x1, PP, #0x38, lsl #12  ; [pp+0x38320] List<double>(5)
    //     0xb805b0: ldr             x1, [x1, #0x320]
    // 0xb805b4: StoreField: r0->field_b = r1
    //     0xb805b4: stur            w1, [x0, #0xb]
    // 0xb805b8: ldur            x1, [fp, #-8]
    // 0xb805bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb805bc: stur            w0, [x1, #0x17]
    //     0xb805c0: ldurb           w16, [x1, #-1]
    //     0xb805c4: ldurb           w17, [x0, #-1]
    //     0xb805c8: and             x16, x17, x16, lsr #2
    //     0xb805cc: tst             x16, HEAP, lsr #32
    //     0xb805d0: b.eq            #0xb805d8
    //     0xb805d4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb805d8: r0 = Null
    //     0xb805d8: mov             x0, NULL
    // 0xb805dc: LeaveFrame
    //     0xb805dc: mov             SP, fp
    //     0xb805e0: ldp             fp, lr, [SP], #0x10
    // 0xb805e4: ret
    //     0xb805e4: ret             
  }
}

// class id: 6774, size: 0x14, field offset: 0x14
enum ShimmerDirection extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e888, size: 0x64
    // 0xc4e888: EnterFrame
    //     0xc4e888: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e88c: mov             fp, SP
    // 0xc4e890: AllocStack(0x10)
    //     0xc4e890: sub             SP, SP, #0x10
    // 0xc4e894: SetupParameters(ShimmerDirection this /* r1 => r0, fp-0x8 */)
    //     0xc4e894: mov             x0, x1
    //     0xc4e898: stur            x1, [fp, #-8]
    // 0xc4e89c: CheckStackOverflow
    //     0xc4e89c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e8a0: cmp             SP, x16
    //     0xc4e8a4: b.ls            #0xc4e8e4
    // 0xc4e8a8: r1 = Null
    //     0xc4e8a8: mov             x1, NULL
    // 0xc4e8ac: r2 = 4
    //     0xc4e8ac: movz            x2, #0x4
    // 0xc4e8b0: r0 = AllocateArray()
    //     0xc4e8b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e8b4: r16 = "ShimmerDirection."
    //     0xc4e8b4: add             x16, PP, #0x41, lsl #12  ; [pp+0x413d8] "ShimmerDirection."
    //     0xc4e8b8: ldr             x16, [x16, #0x3d8]
    // 0xc4e8bc: StoreField: r0->field_f = r16
    //     0xc4e8bc: stur            w16, [x0, #0xf]
    // 0xc4e8c0: ldur            x1, [fp, #-8]
    // 0xc4e8c4: LoadField: r2 = r1->field_f
    //     0xc4e8c4: ldur            w2, [x1, #0xf]
    // 0xc4e8c8: DecompressPointer r2
    //     0xc4e8c8: add             x2, x2, HEAP, lsl #32
    // 0xc4e8cc: StoreField: r0->field_13 = r2
    //     0xc4e8cc: stur            w2, [x0, #0x13]
    // 0xc4e8d0: str             x0, [SP]
    // 0xc4e8d4: r0 = _interpolate()
    //     0xc4e8d4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e8d8: LeaveFrame
    //     0xc4e8d8: mov             SP, fp
    //     0xc4e8dc: ldp             fp, lr, [SP], #0x10
    // 0xc4e8e0: ret
    //     0xc4e8e0: ret             
    // 0xc4e8e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e8e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e8e8: b               #0xc4e8a8
  }
}
