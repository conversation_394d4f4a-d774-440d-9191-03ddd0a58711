// lib: , url: package:showcaseview/src/enum.dart

// class id: 1051121, size: 0x8
class :: {
}

// class id: 6773, size: 0x14, field offset: 0x14
enum TooltipPosition extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e8ec, size: 0x64
    // 0xc4e8ec: EnterFrame
    //     0xc4e8ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e8f0: mov             fp, SP
    // 0xc4e8f4: AllocStack(0x10)
    //     0xc4e8f4: sub             SP, SP, #0x10
    // 0xc4e8f8: SetupParameters(TooltipPosition this /* r1 => r0, fp-0x8 */)
    //     0xc4e8f8: mov             x0, x1
    //     0xc4e8fc: stur            x1, [fp, #-8]
    // 0xc4e900: CheckStackOverflow
    //     0xc4e900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e904: cmp             SP, x16
    //     0xc4e908: b.ls            #0xc4e948
    // 0xc4e90c: r1 = Null
    //     0xc4e90c: mov             x1, NULL
    // 0xc4e910: r2 = 4
    //     0xc4e910: movz            x2, #0x4
    // 0xc4e914: r0 = AllocateArray()
    //     0xc4e914: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e918: r16 = "TooltipPosition."
    //     0xc4e918: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b248] "TooltipPosition."
    //     0xc4e91c: ldr             x16, [x16, #0x248]
    // 0xc4e920: StoreField: r0->field_f = r16
    //     0xc4e920: stur            w16, [x0, #0xf]
    // 0xc4e924: ldur            x1, [fp, #-8]
    // 0xc4e928: LoadField: r2 = r1->field_f
    //     0xc4e928: ldur            w2, [x1, #0xf]
    // 0xc4e92c: DecompressPointer r2
    //     0xc4e92c: add             x2, x2, HEAP, lsl #32
    // 0xc4e930: StoreField: r0->field_13 = r2
    //     0xc4e930: stur            w2, [x0, #0x13]
    // 0xc4e934: str             x0, [SP]
    // 0xc4e938: r0 = _interpolate()
    //     0xc4e938: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e93c: LeaveFrame
    //     0xc4e93c: mov             SP, fp
    //     0xc4e940: ldp             fp, lr, [SP], #0x10
    // 0xc4e944: ret
    //     0xc4e944: ret             
    // 0xc4e948: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e948: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e94c: b               #0xc4e90c
  }
}
