// lib: , url: package:showcaseview/src/get_position.dart

// class id: 1051122, size: 0x8
class :: {
}

// class id: 502, size: 0x1c, field offset: 0x8
class GetPosition extends Object {

  late final RenderBox? _box; // offset: 0x14
  late final Offset? _boxOffset; // offset: 0x18

  _ getRenderBox(/* No info */) {
    // ** addr: 0x9d2460, size: 0x180
    // 0x9d2460: EnterFrame
    //     0x9d2460: stp             fp, lr, [SP, #-0x10]!
    //     0x9d2464: mov             fp, SP
    // 0x9d2468: AllocStack(0x18)
    //     0x9d2468: sub             SP, SP, #0x18
    // 0x9d246c: SetupParameters(GetPosition this /* r1 => r0, fp-0x8 */)
    //     0x9d246c: mov             x0, x1
    //     0x9d2470: stur            x1, [fp, #-8]
    // 0x9d2474: CheckStackOverflow
    //     0x9d2474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d2478: cmp             SP, x16
    //     0x9d247c: b.ls            #0x9d25d8
    // 0x9d2480: LoadField: r1 = r0->field_7
    //     0x9d2480: ldur            w1, [x0, #7]
    // 0x9d2484: DecompressPointer r1
    //     0x9d2484: add             x1, x1, HEAP, lsl #32
    // 0x9d2488: r0 = _currentElement()
    //     0x9d2488: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x9d248c: cmp             w0, NULL
    // 0x9d2490: b.ne            #0x9d249c
    // 0x9d2494: r3 = Null
    //     0x9d2494: mov             x3, NULL
    // 0x9d2498: b               #0x9d24a8
    // 0x9d249c: mov             x1, x0
    // 0x9d24a0: r0 = findRenderObject()
    //     0x9d24a0: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0x9d24a4: mov             x3, x0
    // 0x9d24a8: mov             x0, x3
    // 0x9d24ac: stur            x3, [fp, #-0x10]
    // 0x9d24b0: r2 = Null
    //     0x9d24b0: mov             x2, NULL
    // 0x9d24b4: r1 = Null
    //     0x9d24b4: mov             x1, NULL
    // 0x9d24b8: r4 = LoadClassIdInstr(r0)
    //     0x9d24b8: ldur            x4, [x0, #-1]
    //     0x9d24bc: ubfx            x4, x4, #0xc, #0x14
    // 0x9d24c0: sub             x4, x4, #0xbba
    // 0x9d24c4: cmp             x4, #0x9a
    // 0x9d24c8: b.ls            #0x9d24dc
    // 0x9d24cc: r8 = RenderBox?
    //     0x9d24cc: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x9d24d0: r3 = Null
    //     0x9d24d0: add             x3, PP, #0x47, lsl #12  ; [pp+0x47d48] Null
    //     0x9d24d4: ldr             x3, [x3, #0xd48]
    // 0x9d24d8: r0 = RenderBox?()
    //     0x9d24d8: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x9d24dc: ldur            x0, [fp, #-0x10]
    // 0x9d24e0: cmp             w0, NULL
    // 0x9d24e4: b.ne            #0x9d24f8
    // 0x9d24e8: r0 = Null
    //     0x9d24e8: mov             x0, NULL
    // 0x9d24ec: LeaveFrame
    //     0x9d24ec: mov             SP, fp
    //     0x9d24f0: ldp             fp, lr, [SP], #0x10
    // 0x9d24f4: ret
    //     0x9d24f4: ret             
    // 0x9d24f8: ldur            x1, [fp, #-8]
    // 0x9d24fc: LoadField: r2 = r1->field_13
    //     0x9d24fc: ldur            w2, [x1, #0x13]
    // 0x9d2500: DecompressPointer r2
    //     0x9d2500: add             x2, x2, HEAP, lsl #32
    // 0x9d2504: r16 = Sentinel
    //     0x9d2504: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9d2508: cmp             w2, w16
    // 0x9d250c: b.ne            #0x9d2518
    // 0x9d2510: mov             x3, x1
    // 0x9d2514: b               #0x9d252c
    // 0x9d2518: r16 = "_box@2697197215"
    //     0x9d2518: add             x16, PP, #0x47, lsl #12  ; [pp+0x47d58] "_box@2697197215"
    //     0x9d251c: ldr             x16, [x16, #0xd58]
    // 0x9d2520: str             x16, [SP]
    // 0x9d2524: r0 = _throwFieldAlreadyInitialized()
    //     0x9d2524: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x9d2528: ldur            x3, [fp, #-8]
    // 0x9d252c: ldur            x0, [fp, #-0x10]
    // 0x9d2530: StoreField: r3->field_13 = r0
    //     0x9d2530: stur            w0, [x3, #0x13]
    //     0x9d2534: ldurb           w16, [x3, #-1]
    //     0x9d2538: ldurb           w17, [x0, #-1]
    //     0x9d253c: and             x16, x17, x16, lsr #2
    //     0x9d2540: tst             x16, HEAP, lsr #32
    //     0x9d2544: b.eq            #0x9d254c
    //     0x9d2548: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9d254c: LoadField: r0 = r3->field_f
    //     0x9d254c: ldur            w0, [x3, #0xf]
    // 0x9d2550: DecompressPointer r0
    //     0x9d2550: add             x0, x0, HEAP, lsl #32
    // 0x9d2554: str             x0, [SP]
    // 0x9d2558: ldur            x1, [fp, #-0x10]
    // 0x9d255c: r2 = Instance_Offset
    //     0x9d255c: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x9d2560: r4 = const [0, 0x3, 0x1, 0x2, ancestor, 0x2, null]
    //     0x9d2560: add             x4, PP, #0x44, lsl #12  ; [pp+0x44950] List(7) [0, 0x3, 0x1, 0x2, "ancestor", 0x2, Null]
    //     0x9d2564: ldr             x4, [x4, #0x950]
    // 0x9d2568: r0 = localToGlobal()
    //     0x9d2568: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0x9d256c: mov             x1, x0
    // 0x9d2570: ldur            x0, [fp, #-8]
    // 0x9d2574: stur            x1, [fp, #-0x10]
    // 0x9d2578: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x9d2578: ldur            w2, [x0, #0x17]
    // 0x9d257c: DecompressPointer r2
    //     0x9d257c: add             x2, x2, HEAP, lsl #32
    // 0x9d2580: r16 = Sentinel
    //     0x9d2580: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9d2584: cmp             w2, w16
    // 0x9d2588: b.ne            #0x9d2594
    // 0x9d258c: mov             x1, x0
    // 0x9d2590: b               #0x9d25a8
    // 0x9d2594: r16 = "_boxOffset@2697197215"
    //     0x9d2594: add             x16, PP, #0x47, lsl #12  ; [pp+0x47d60] "_boxOffset@2697197215"
    //     0x9d2598: ldr             x16, [x16, #0xd60]
    // 0x9d259c: str             x16, [SP]
    // 0x9d25a0: r0 = _throwFieldAlreadyInitialized()
    //     0x9d25a0: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x9d25a4: ldur            x1, [fp, #-8]
    // 0x9d25a8: ldur            x0, [fp, #-0x10]
    // 0x9d25ac: ArrayStore: r1[0] = r0  ; List_4
    //     0x9d25ac: stur            w0, [x1, #0x17]
    //     0x9d25b0: ldurb           w16, [x1, #-1]
    //     0x9d25b4: ldurb           w17, [x0, #-1]
    //     0x9d25b8: and             x16, x17, x16, lsr #2
    //     0x9d25bc: tst             x16, HEAP, lsr #32
    //     0x9d25c0: b.eq            #0x9d25c8
    //     0x9d25c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d25c8: r0 = Null
    //     0x9d25c8: mov             x0, NULL
    // 0x9d25cc: LeaveFrame
    //     0x9d25cc: mov             SP, fp
    //     0x9d25d0: ldp             fp, lr, [SP], #0x10
    // 0x9d25d4: ret
    //     0x9d25d4: ret             
    // 0x9d25d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d25d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d25dc: b               #0x9d2480
  }
  _ getCenter(/* No info */) {
    // ** addr: 0xa4df30, size: 0x58
    // 0xa4df30: EnterFrame
    //     0xa4df30: stp             fp, lr, [SP, #-0x10]!
    //     0xa4df34: mov             fp, SP
    // 0xa4df38: AllocStack(0x10)
    //     0xa4df38: sub             SP, SP, #0x10
    // 0xa4df3c: SetupParameters(GetPosition this /* r1 => r0, fp-0x8 */)
    //     0xa4df3c: mov             x0, x1
    //     0xa4df40: stur            x1, [fp, #-8]
    // 0xa4df44: CheckStackOverflow
    //     0xa4df44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4df48: cmp             SP, x16
    //     0xa4df4c: b.ls            #0xa4df80
    // 0xa4df50: mov             x1, x0
    // 0xa4df54: r0 = getLeft()
    //     0xa4df54: bl              #0xa4e174  ; [package:showcaseview/src/get_position.dart] GetPosition::getLeft
    // 0xa4df58: ldur            x1, [fp, #-8]
    // 0xa4df5c: stur            d0, [fp, #-0x10]
    // 0xa4df60: r0 = getRight()
    //     0xa4df60: bl              #0xa4df88  ; [package:showcaseview/src/get_position.dart] GetPosition::getRight
    // 0xa4df64: ldur            d1, [fp, #-0x10]
    // 0xa4df68: fadd            d2, d1, d0
    // 0xa4df6c: d1 = 0.500000
    //     0xa4df6c: fmov            d1, #0.50000000
    // 0xa4df70: fmul            d0, d2, d1
    // 0xa4df74: LeaveFrame
    //     0xa4df74: mov             SP, fp
    //     0xa4df78: ldp             fp, lr, [SP], #0x10
    // 0xa4df7c: ret
    //     0xa4df7c: ret             
    // 0xa4df80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4df80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4df84: b               #0xa4df50
  }
  _ getRight(/* No info */) {
    // ** addr: 0xa4df88, size: 0xcc
    // 0xa4df88: EnterFrame
    //     0xa4df88: stp             fp, lr, [SP, #-0x10]!
    //     0xa4df8c: mov             fp, SP
    // 0xa4df90: AllocStack(0x10)
    //     0xa4df90: sub             SP, SP, #0x10
    // 0xa4df94: SetupParameters(GetPosition this /* r1 => r0, fp-0x8 */)
    //     0xa4df94: mov             x0, x1
    //     0xa4df98: stur            x1, [fp, #-8]
    // 0xa4df9c: CheckStackOverflow
    //     0xa4df9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4dfa0: cmp             SP, x16
    //     0xa4dfa4: b.ls            #0xa4e034
    // 0xa4dfa8: r16 = true
    //     0xa4dfa8: add             x16, NULL, #0x20  ; true
    // 0xa4dfac: str             x16, [SP]
    // 0xa4dfb0: mov             x1, x0
    // 0xa4dfb4: r4 = const [0, 0x2, 0x1, 0x1, checkDx, 0x1, null]
    //     0xa4dfb4: add             x4, PP, #0x57, lsl #12  ; [pp+0x57908] List(7) [0, 0x2, 0x1, 0x1, "checkDx", 0x1, Null]
    //     0xa4dfb8: ldr             x4, [x4, #0x908]
    // 0xa4dfbc: r0 = _checkBoxOrOffsetIsNull()
    //     0xa4dfbc: bl              #0xa4e054  ; [package:showcaseview/src/get_position.dart] GetPosition::_checkBoxOrOffsetIsNull
    // 0xa4dfc0: tbnz            w0, #4, #0xa4dfd8
    // 0xa4dfc4: r0 = Instance_EdgeInsets
    //     0xa4dfc4: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4dfc8: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xa4dfc8: ldur            d0, [x0, #0x17]
    // 0xa4dfcc: LeaveFrame
    //     0xa4dfcc: mov             SP, fp
    //     0xa4dfd0: ldp             fp, lr, [SP], #0x10
    // 0xa4dfd4: ret
    //     0xa4dfd4: ret             
    // 0xa4dfd8: ldur            x2, [fp, #-8]
    // 0xa4dfdc: r0 = Instance_EdgeInsets
    //     0xa4dfdc: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4dfe0: LoadField: r1 = r2->field_13
    //     0xa4dfe0: ldur            w1, [x2, #0x13]
    // 0xa4dfe4: DecompressPointer r1
    //     0xa4dfe4: add             x1, x1, HEAP, lsl #32
    // 0xa4dfe8: r16 = Sentinel
    //     0xa4dfe8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4dfec: cmp             w1, w16
    // 0xa4dff0: b.eq            #0xa4e03c
    // 0xa4dff4: r0 = size()
    //     0xa4dff4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xa4dff8: mov             x1, x0
    // 0xa4dffc: ldur            x0, [fp, #-8]
    // 0xa4e000: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa4e000: ldur            w2, [x0, #0x17]
    // 0xa4e004: DecompressPointer r2
    //     0xa4e004: add             x2, x2, HEAP, lsl #32
    // 0xa4e008: r16 = Sentinel
    //     0xa4e008: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4e00c: cmp             w2, w16
    // 0xa4e010: b.eq            #0xa4e048
    // 0xa4e014: r0 = bottomRight()
    //     0xa4e014: bl              #0x6a5e18  ; [dart:ui] Size::bottomRight
    // 0xa4e018: LoadField: d1 = r0->field_7
    //     0xa4e018: ldur            d1, [x0, #7]
    // 0xa4e01c: r0 = Instance_EdgeInsets
    //     0xa4e01c: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4e020: ArrayLoad: d2 = r0[0]  ; List_8
    //     0xa4e020: ldur            d2, [x0, #0x17]
    // 0xa4e024: fadd            d0, d1, d2
    // 0xa4e028: LeaveFrame
    //     0xa4e028: mov             SP, fp
    //     0xa4e02c: ldp             fp, lr, [SP], #0x10
    // 0xa4e030: ret
    //     0xa4e030: ret             
    // 0xa4e034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4e034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4e038: b               #0xa4dfa8
    // 0xa4e03c: r9 = _box
    //     0xa4e03c: add             x9, PP, #0x57, lsl #12  ; [pp+0x57910] Field <GetPosition._box@2697197215>: late final (offset: 0x14)
    //     0xa4e040: ldr             x9, [x9, #0x910]
    // 0xa4e044: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e044: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4e048: r9 = _boxOffset
    //     0xa4e048: add             x9, PP, #0x57, lsl #12  ; [pp+0x57918] Field <GetPosition._boxOffset@2697197215>: late final (offset: 0x18)
    //     0xa4e04c: ldr             x9, [x9, #0x918]
    // 0xa4e050: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e050: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _checkBoxOrOffsetIsNull(/* No info */) {
    // ** addr: 0xa4e054, size: 0x120
    // 0xa4e054: EnterFrame
    //     0xa4e054: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e058: mov             fp, SP
    // 0xa4e05c: LoadField: r2 = r4->field_13
    //     0xa4e05c: ldur            w2, [x4, #0x13]
    // 0xa4e060: LoadField: r3 = r4->field_1f
    //     0xa4e060: ldur            w3, [x4, #0x1f]
    // 0xa4e064: DecompressPointer r3
    //     0xa4e064: add             x3, x3, HEAP, lsl #32
    // 0xa4e068: r16 = "checkDx"
    //     0xa4e068: add             x16, PP, #0x57, lsl #12  ; [pp+0x57920] "checkDx"
    //     0xa4e06c: ldr             x16, [x16, #0x920]
    // 0xa4e070: cmp             w3, w16
    // 0xa4e074: b.ne            #0xa4e098
    // 0xa4e078: LoadField: r3 = r4->field_23
    //     0xa4e078: ldur            w3, [x4, #0x23]
    // 0xa4e07c: DecompressPointer r3
    //     0xa4e07c: add             x3, x3, HEAP, lsl #32
    // 0xa4e080: sub             w5, w2, w3
    // 0xa4e084: add             x3, fp, w5, sxtw #2
    // 0xa4e088: ldr             x3, [x3, #8]
    // 0xa4e08c: mov             x5, x3
    // 0xa4e090: r3 = 1
    //     0xa4e090: movz            x3, #0x1
    // 0xa4e094: b               #0xa4e0a0
    // 0xa4e098: r5 = false
    //     0xa4e098: add             x5, NULL, #0x30  ; false
    // 0xa4e09c: r3 = 0
    //     0xa4e09c: movz            x3, #0
    // 0xa4e0a0: lsl             x6, x3, #1
    // 0xa4e0a4: lsl             w3, w6, #1
    // 0xa4e0a8: add             w6, w3, #8
    // 0xa4e0ac: ArrayLoad: r7 = r4[r6]  ; Unknown_4
    //     0xa4e0ac: add             x16, x4, w6, sxtw #1
    //     0xa4e0b0: ldur            w7, [x16, #0xf]
    // 0xa4e0b4: DecompressPointer r7
    //     0xa4e0b4: add             x7, x7, HEAP, lsl #32
    // 0xa4e0b8: r16 = "checkDy"
    //     0xa4e0b8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57928] "checkDy"
    //     0xa4e0bc: ldr             x16, [x16, #0x928]
    // 0xa4e0c0: cmp             w7, w16
    // 0xa4e0c4: b.ne            #0xa4e0e8
    // 0xa4e0c8: add             w6, w3, #0xa
    // 0xa4e0cc: ArrayLoad: r3 = r4[r6]  ; Unknown_4
    //     0xa4e0cc: add             x16, x4, w6, sxtw #1
    //     0xa4e0d0: ldur            w3, [x16, #0xf]
    // 0xa4e0d4: DecompressPointer r3
    //     0xa4e0d4: add             x3, x3, HEAP, lsl #32
    // 0xa4e0d8: sub             w4, w2, w3
    // 0xa4e0dc: add             x2, fp, w4, sxtw #2
    // 0xa4e0e0: ldr             x2, [x2, #8]
    // 0xa4e0e4: b               #0xa4e0ec
    // 0xa4e0e8: r2 = false
    //     0xa4e0e8: add             x2, NULL, #0x30  ; false
    // 0xa4e0ec: LoadField: r3 = r1->field_13
    //     0xa4e0ec: ldur            w3, [x1, #0x13]
    // 0xa4e0f0: DecompressPointer r3
    //     0xa4e0f0: add             x3, x3, HEAP, lsl #32
    // 0xa4e0f4: r16 = Sentinel
    //     0xa4e0f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4e0f8: cmp             w3, w16
    // 0xa4e0fc: b.eq            #0xa4e15c
    // 0xa4e100: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xa4e100: ldur            w3, [x1, #0x17]
    // 0xa4e104: DecompressPointer r3
    //     0xa4e104: add             x3, x3, HEAP, lsl #32
    // 0xa4e108: r16 = Sentinel
    //     0xa4e108: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4e10c: cmp             w3, w16
    // 0xa4e110: b.eq            #0xa4e168
    // 0xa4e114: tbnz            w5, #4, #0xa4e12c
    // 0xa4e118: LoadField: d0 = r3->field_7
    //     0xa4e118: ldur            d0, [x3, #7]
    // 0xa4e11c: fcmp            d0, d0
    // 0xa4e120: b.vc            #0xa4e12c
    // 0xa4e124: r0 = true
    //     0xa4e124: add             x0, NULL, #0x20  ; true
    // 0xa4e128: b               #0xa4e150
    // 0xa4e12c: tbnz            w2, #4, #0xa4e14c
    // 0xa4e130: LoadField: d0 = r3->field_f
    //     0xa4e130: ldur            d0, [x3, #0xf]
    // 0xa4e134: fcmp            d0, d0
    // 0xa4e138: r16 = true
    //     0xa4e138: add             x16, NULL, #0x20  ; true
    // 0xa4e13c: r17 = false
    //     0xa4e13c: add             x17, NULL, #0x30  ; false
    // 0xa4e140: csel            x1, x16, x17, vs
    // 0xa4e144: mov             x0, x1
    // 0xa4e148: b               #0xa4e150
    // 0xa4e14c: r0 = false
    //     0xa4e14c: add             x0, NULL, #0x30  ; false
    // 0xa4e150: LeaveFrame
    //     0xa4e150: mov             SP, fp
    //     0xa4e154: ldp             fp, lr, [SP], #0x10
    // 0xa4e158: ret
    //     0xa4e158: ret             
    // 0xa4e15c: r9 = _box
    //     0xa4e15c: add             x9, PP, #0x57, lsl #12  ; [pp+0x57910] Field <GetPosition._box@2697197215>: late final (offset: 0x14)
    //     0xa4e160: ldr             x9, [x9, #0x910]
    // 0xa4e164: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e164: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4e168: r9 = _boxOffset
    //     0xa4e168: add             x9, PP, #0x57, lsl #12  ; [pp+0x57918] Field <GetPosition._boxOffset@2697197215>: late final (offset: 0x18)
    //     0xa4e16c: ldr             x9, [x9, #0x918]
    // 0xa4e170: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e170: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ getLeft(/* No info */) {
    // ** addr: 0xa4e174, size: 0xbc
    // 0xa4e174: EnterFrame
    //     0xa4e174: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e178: mov             fp, SP
    // 0xa4e17c: AllocStack(0x10)
    //     0xa4e17c: sub             SP, SP, #0x10
    // 0xa4e180: SetupParameters(GetPosition this /* r1 => r0, fp-0x8 */)
    //     0xa4e180: mov             x0, x1
    //     0xa4e184: stur            x1, [fp, #-8]
    // 0xa4e188: CheckStackOverflow
    //     0xa4e188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4e18c: cmp             SP, x16
    //     0xa4e190: b.ls            #0xa4e210
    // 0xa4e194: r16 = true
    //     0xa4e194: add             x16, NULL, #0x20  ; true
    // 0xa4e198: str             x16, [SP]
    // 0xa4e19c: mov             x1, x0
    // 0xa4e1a0: r4 = const [0, 0x2, 0x1, 0x1, checkDx, 0x1, null]
    //     0xa4e1a0: add             x4, PP, #0x57, lsl #12  ; [pp+0x57908] List(7) [0, 0x2, 0x1, 0x1, "checkDx", 0x1, Null]
    //     0xa4e1a4: ldr             x4, [x4, #0x908]
    // 0xa4e1a8: r0 = _checkBoxOrOffsetIsNull()
    //     0xa4e1a8: bl              #0xa4e054  ; [package:showcaseview/src/get_position.dart] GetPosition::_checkBoxOrOffsetIsNull
    // 0xa4e1ac: tbnz            w0, #4, #0xa4e1c0
    // 0xa4e1b0: d0 = -0.000000
    //     0xa4e1b0: ldr             d0, [PP, #0x1368]  ; [pp+0x1368] IMM: double(-0) from 0x8000000000000000
    // 0xa4e1b4: LeaveFrame
    //     0xa4e1b4: mov             SP, fp
    //     0xa4e1b8: ldp             fp, lr, [SP], #0x10
    // 0xa4e1bc: ret
    //     0xa4e1bc: ret             
    // 0xa4e1c0: ldur            x0, [fp, #-8]
    // 0xa4e1c4: LoadField: r1 = r0->field_13
    //     0xa4e1c4: ldur            w1, [x0, #0x13]
    // 0xa4e1c8: DecompressPointer r1
    //     0xa4e1c8: add             x1, x1, HEAP, lsl #32
    // 0xa4e1cc: r16 = Sentinel
    //     0xa4e1cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4e1d0: cmp             w1, w16
    // 0xa4e1d4: b.eq            #0xa4e218
    // 0xa4e1d8: r0 = size()
    //     0xa4e1d8: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xa4e1dc: ldur            x0, [fp, #-8]
    // 0xa4e1e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4e1e0: ldur            w1, [x0, #0x17]
    // 0xa4e1e4: DecompressPointer r1
    //     0xa4e1e4: add             x1, x1, HEAP, lsl #32
    // 0xa4e1e8: r16 = Sentinel
    //     0xa4e1e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4e1ec: cmp             w1, w16
    // 0xa4e1f0: b.eq            #0xa4e224
    // 0xa4e1f4: LoadField: d1 = r1->field_7
    //     0xa4e1f4: ldur            d1, [x1, #7]
    // 0xa4e1f8: r0 = Instance_EdgeInsets
    //     0xa4e1f8: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4e1fc: LoadField: d2 = r0->field_7
    //     0xa4e1fc: ldur            d2, [x0, #7]
    // 0xa4e200: fsub            d0, d1, d2
    // 0xa4e204: LeaveFrame
    //     0xa4e204: mov             SP, fp
    //     0xa4e208: ldp             fp, lr, [SP], #0x10
    // 0xa4e20c: ret
    //     0xa4e20c: ret             
    // 0xa4e210: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4e210: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4e214: b               #0xa4e194
    // 0xa4e218: r9 = _box
    //     0xa4e218: add             x9, PP, #0x57, lsl #12  ; [pp+0x57910] Field <GetPosition._box@2697197215>: late final (offset: 0x14)
    //     0xa4e21c: ldr             x9, [x9, #0x910]
    // 0xa4e220: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e220: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4e224: r9 = _boxOffset
    //     0xa4e224: add             x9, PP, #0x57, lsl #12  ; [pp+0x57918] Field <GetPosition._boxOffset@2697197215>: late final (offset: 0x18)
    //     0xa4e228: ldr             x9, [x9, #0x918]
    // 0xa4e22c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e22c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ getTop(/* No info */) {
    // ** addr: 0xa4e230, size: 0xbc
    // 0xa4e230: EnterFrame
    //     0xa4e230: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e234: mov             fp, SP
    // 0xa4e238: AllocStack(0x10)
    //     0xa4e238: sub             SP, SP, #0x10
    // 0xa4e23c: SetupParameters(GetPosition this /* r1 => r0, fp-0x8 */)
    //     0xa4e23c: mov             x0, x1
    //     0xa4e240: stur            x1, [fp, #-8]
    // 0xa4e244: CheckStackOverflow
    //     0xa4e244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4e248: cmp             SP, x16
    //     0xa4e24c: b.ls            #0xa4e2cc
    // 0xa4e250: r16 = true
    //     0xa4e250: add             x16, NULL, #0x20  ; true
    // 0xa4e254: str             x16, [SP]
    // 0xa4e258: mov             x1, x0
    // 0xa4e25c: r4 = const [0, 0x2, 0x1, 0x1, checkDy, 0x1, null]
    //     0xa4e25c: add             x4, PP, #0x57, lsl #12  ; [pp+0x57930] List(7) [0, 0x2, 0x1, 0x1, "checkDy", 0x1, Null]
    //     0xa4e260: ldr             x4, [x4, #0x930]
    // 0xa4e264: r0 = _checkBoxOrOffsetIsNull()
    //     0xa4e264: bl              #0xa4e054  ; [package:showcaseview/src/get_position.dart] GetPosition::_checkBoxOrOffsetIsNull
    // 0xa4e268: tbnz            w0, #4, #0xa4e27c
    // 0xa4e26c: d0 = -0.000000
    //     0xa4e26c: ldr             d0, [PP, #0x1368]  ; [pp+0x1368] IMM: double(-0) from 0x8000000000000000
    // 0xa4e270: LeaveFrame
    //     0xa4e270: mov             SP, fp
    //     0xa4e274: ldp             fp, lr, [SP], #0x10
    // 0xa4e278: ret
    //     0xa4e278: ret             
    // 0xa4e27c: ldur            x0, [fp, #-8]
    // 0xa4e280: LoadField: r1 = r0->field_13
    //     0xa4e280: ldur            w1, [x0, #0x13]
    // 0xa4e284: DecompressPointer r1
    //     0xa4e284: add             x1, x1, HEAP, lsl #32
    // 0xa4e288: r16 = Sentinel
    //     0xa4e288: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4e28c: cmp             w1, w16
    // 0xa4e290: b.eq            #0xa4e2d4
    // 0xa4e294: r0 = size()
    //     0xa4e294: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xa4e298: ldur            x0, [fp, #-8]
    // 0xa4e29c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4e29c: ldur            w1, [x0, #0x17]
    // 0xa4e2a0: DecompressPointer r1
    //     0xa4e2a0: add             x1, x1, HEAP, lsl #32
    // 0xa4e2a4: r16 = Sentinel
    //     0xa4e2a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4e2a8: cmp             w1, w16
    // 0xa4e2ac: b.eq            #0xa4e2e0
    // 0xa4e2b0: LoadField: d1 = r1->field_f
    //     0xa4e2b0: ldur            d1, [x1, #0xf]
    // 0xa4e2b4: r0 = Instance_EdgeInsets
    //     0xa4e2b4: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4e2b8: LoadField: d2 = r0->field_f
    //     0xa4e2b8: ldur            d2, [x0, #0xf]
    // 0xa4e2bc: fsub            d0, d1, d2
    // 0xa4e2c0: LeaveFrame
    //     0xa4e2c0: mov             SP, fp
    //     0xa4e2c4: ldp             fp, lr, [SP], #0x10
    // 0xa4e2c8: ret
    //     0xa4e2c8: ret             
    // 0xa4e2cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4e2cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4e2d0: b               #0xa4e250
    // 0xa4e2d4: r9 = _box
    //     0xa4e2d4: add             x9, PP, #0x57, lsl #12  ; [pp+0x57910] Field <GetPosition._box@2697197215>: late final (offset: 0x14)
    //     0xa4e2d8: ldr             x9, [x9, #0x910]
    // 0xa4e2dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e2dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4e2e0: r9 = _boxOffset
    //     0xa4e2e0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57918] Field <GetPosition._boxOffset@2697197215>: late final (offset: 0x18)
    //     0xa4e2e4: ldr             x9, [x9, #0x918]
    // 0xa4e2e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e2e8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ getBottom(/* No info */) {
    // ** addr: 0xa4e2ec, size: 0xcc
    // 0xa4e2ec: EnterFrame
    //     0xa4e2ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e2f0: mov             fp, SP
    // 0xa4e2f4: AllocStack(0x10)
    //     0xa4e2f4: sub             SP, SP, #0x10
    // 0xa4e2f8: SetupParameters(GetPosition this /* r1 => r0, fp-0x8 */)
    //     0xa4e2f8: mov             x0, x1
    //     0xa4e2fc: stur            x1, [fp, #-8]
    // 0xa4e300: CheckStackOverflow
    //     0xa4e300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4e304: cmp             SP, x16
    //     0xa4e308: b.ls            #0xa4e398
    // 0xa4e30c: r16 = true
    //     0xa4e30c: add             x16, NULL, #0x20  ; true
    // 0xa4e310: str             x16, [SP]
    // 0xa4e314: mov             x1, x0
    // 0xa4e318: r4 = const [0, 0x2, 0x1, 0x1, checkDy, 0x1, null]
    //     0xa4e318: add             x4, PP, #0x57, lsl #12  ; [pp+0x57930] List(7) [0, 0x2, 0x1, 0x1, "checkDy", 0x1, Null]
    //     0xa4e31c: ldr             x4, [x4, #0x930]
    // 0xa4e320: r0 = _checkBoxOrOffsetIsNull()
    //     0xa4e320: bl              #0xa4e054  ; [package:showcaseview/src/get_position.dart] GetPosition::_checkBoxOrOffsetIsNull
    // 0xa4e324: tbnz            w0, #4, #0xa4e33c
    // 0xa4e328: r0 = Instance_EdgeInsets
    //     0xa4e328: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4e32c: LoadField: d0 = r0->field_1f
    //     0xa4e32c: ldur            d0, [x0, #0x1f]
    // 0xa4e330: LeaveFrame
    //     0xa4e330: mov             SP, fp
    //     0xa4e334: ldp             fp, lr, [SP], #0x10
    // 0xa4e338: ret
    //     0xa4e338: ret             
    // 0xa4e33c: ldur            x2, [fp, #-8]
    // 0xa4e340: r0 = Instance_EdgeInsets
    //     0xa4e340: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4e344: LoadField: r1 = r2->field_13
    //     0xa4e344: ldur            w1, [x2, #0x13]
    // 0xa4e348: DecompressPointer r1
    //     0xa4e348: add             x1, x1, HEAP, lsl #32
    // 0xa4e34c: r16 = Sentinel
    //     0xa4e34c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4e350: cmp             w1, w16
    // 0xa4e354: b.eq            #0xa4e3a0
    // 0xa4e358: r0 = size()
    //     0xa4e358: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xa4e35c: mov             x1, x0
    // 0xa4e360: ldur            x0, [fp, #-8]
    // 0xa4e364: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa4e364: ldur            w2, [x0, #0x17]
    // 0xa4e368: DecompressPointer r2
    //     0xa4e368: add             x2, x2, HEAP, lsl #32
    // 0xa4e36c: r16 = Sentinel
    //     0xa4e36c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4e370: cmp             w2, w16
    // 0xa4e374: b.eq            #0xa4e3ac
    // 0xa4e378: r0 = bottomRight()
    //     0xa4e378: bl              #0x6a5e18  ; [dart:ui] Size::bottomRight
    // 0xa4e37c: LoadField: d1 = r0->field_f
    //     0xa4e37c: ldur            d1, [x0, #0xf]
    // 0xa4e380: r0 = Instance_EdgeInsets
    //     0xa4e380: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4e384: LoadField: d2 = r0->field_1f
    //     0xa4e384: ldur            d2, [x0, #0x1f]
    // 0xa4e388: fadd            d0, d1, d2
    // 0xa4e38c: LeaveFrame
    //     0xa4e38c: mov             SP, fp
    //     0xa4e390: ldp             fp, lr, [SP], #0x10
    // 0xa4e394: ret
    //     0xa4e394: ret             
    // 0xa4e398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4e398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4e39c: b               #0xa4e30c
    // 0xa4e3a0: r9 = _box
    //     0xa4e3a0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57910] Field <GetPosition._box@2697197215>: late final (offset: 0x14)
    //     0xa4e3a4: ldr             x9, [x9, #0x910]
    // 0xa4e3a8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e3a8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4e3ac: r9 = _boxOffset
    //     0xa4e3ac: add             x9, PP, #0x57, lsl #12  ; [pp+0x57918] Field <GetPosition._boxOffset@2697197215>: late final (offset: 0x18)
    //     0xa4e3b0: ldr             x9, [x9, #0x918]
    // 0xa4e3b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4e3b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ getHeight(/* No info */) {
    // ** addr: 0xa4e628, size: 0x54
    // 0xa4e628: EnterFrame
    //     0xa4e628: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e62c: mov             fp, SP
    // 0xa4e630: AllocStack(0x10)
    //     0xa4e630: sub             SP, SP, #0x10
    // 0xa4e634: SetupParameters(GetPosition this /* r1 => r0, fp-0x8 */)
    //     0xa4e634: mov             x0, x1
    //     0xa4e638: stur            x1, [fp, #-8]
    // 0xa4e63c: CheckStackOverflow
    //     0xa4e63c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4e640: cmp             SP, x16
    //     0xa4e644: b.ls            #0xa4e674
    // 0xa4e648: mov             x1, x0
    // 0xa4e64c: r0 = getBottom()
    //     0xa4e64c: bl              #0xa4e2ec  ; [package:showcaseview/src/get_position.dart] GetPosition::getBottom
    // 0xa4e650: ldur            x1, [fp, #-8]
    // 0xa4e654: stur            d0, [fp, #-0x10]
    // 0xa4e658: r0 = getTop()
    //     0xa4e658: bl              #0xa4e230  ; [package:showcaseview/src/get_position.dart] GetPosition::getTop
    // 0xa4e65c: ldur            d1, [fp, #-0x10]
    // 0xa4e660: fsub            d2, d1, d0
    // 0xa4e664: mov             v0.16b, v2.16b
    // 0xa4e668: LeaveFrame
    //     0xa4e668: mov             SP, fp
    //     0xa4e66c: ldp             fp, lr, [SP], #0x10
    // 0xa4e670: ret
    //     0xa4e670: ret             
    // 0xa4e674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4e674: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4e678: b               #0xa4e648
  }
}
