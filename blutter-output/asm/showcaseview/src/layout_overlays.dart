// lib: , url: package:showcaseview/src/layout_overlays.dart

// class id: 1051123, size: 0x8
class :: {
}

// class id: 4088, size: 0x18, field offset: 0x14
class _OverlayBuilderState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x97fca8, size: 0x144
    // 0x97fca8: EnterFrame
    //     0x97fca8: stp             fp, lr, [SP, #-0x10]!
    //     0x97fcac: mov             fp, SP
    // 0x97fcb0: AllocStack(0x18)
    //     0x97fcb0: sub             SP, SP, #0x18
    // 0x97fcb4: SetupParameters(_OverlayBuilderState this /* r1 => r1, fp-0x8 */)
    //     0x97fcb4: stur            x1, [fp, #-8]
    // 0x97fcb8: CheckStackOverflow
    //     0x97fcb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97fcbc: cmp             SP, x16
    //     0x97fcc0: b.ls            #0x97fddc
    // 0x97fcc4: r1 = 1
    //     0x97fcc4: movz            x1, #0x1
    // 0x97fcc8: r0 = AllocateContext()
    //     0x97fcc8: bl              #0xec126c  ; AllocateContextStub
    // 0x97fccc: mov             x1, x0
    // 0x97fcd0: ldur            x0, [fp, #-8]
    // 0x97fcd4: StoreField: r1->field_f = r0
    //     0x97fcd4: stur            w0, [x1, #0xf]
    // 0x97fcd8: LoadField: r2 = r0->field_b
    //     0x97fcd8: ldur            w2, [x0, #0xb]
    // 0x97fcdc: DecompressPointer r2
    //     0x97fcdc: add             x2, x2, HEAP, lsl #32
    // 0x97fce0: cmp             w2, NULL
    // 0x97fce4: b.eq            #0x97fde4
    // 0x97fce8: r0 = LoadStaticField(0x7d4)
    //     0x97fce8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x97fcec: ldr             x0, [x0, #0xfa8]
    // 0x97fcf0: cmp             w0, NULL
    // 0x97fcf4: b.eq            #0x97fde8
    // 0x97fcf8: LoadField: r3 = r0->field_53
    //     0x97fcf8: ldur            w3, [x0, #0x53]
    // 0x97fcfc: DecompressPointer r3
    //     0x97fcfc: add             x3, x3, HEAP, lsl #32
    // 0x97fd00: stur            x3, [fp, #-0x10]
    // 0x97fd04: LoadField: r0 = r3->field_7
    //     0x97fd04: ldur            w0, [x3, #7]
    // 0x97fd08: DecompressPointer r0
    //     0x97fd08: add             x0, x0, HEAP, lsl #32
    // 0x97fd0c: mov             x2, x1
    // 0x97fd10: stur            x0, [fp, #-8]
    // 0x97fd14: r1 = Function '<anonymous closure>':.
    //     0x97fd14: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aee8] AnonymousClosure: (0x97fe10), in [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::initState (0x97fca8)
    //     0x97fd18: ldr             x1, [x1, #0xee8]
    // 0x97fd1c: r0 = AllocateClosure()
    //     0x97fd1c: bl              #0xec1630  ; AllocateClosureStub
    // 0x97fd20: ldur            x2, [fp, #-8]
    // 0x97fd24: mov             x3, x0
    // 0x97fd28: r1 = Null
    //     0x97fd28: mov             x1, NULL
    // 0x97fd2c: stur            x3, [fp, #-8]
    // 0x97fd30: cmp             w2, NULL
    // 0x97fd34: b.eq            #0x97fd54
    // 0x97fd38: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x97fd38: ldur            w4, [x2, #0x17]
    // 0x97fd3c: DecompressPointer r4
    //     0x97fd3c: add             x4, x4, HEAP, lsl #32
    // 0x97fd40: r8 = X0
    //     0x97fd40: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x97fd44: LoadField: r9 = r4->field_7
    //     0x97fd44: ldur            x9, [x4, #7]
    // 0x97fd48: r3 = Null
    //     0x97fd48: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5aef0] Null
    //     0x97fd4c: ldr             x3, [x3, #0xef0]
    // 0x97fd50: blr             x9
    // 0x97fd54: ldur            x0, [fp, #-0x10]
    // 0x97fd58: LoadField: r1 = r0->field_b
    //     0x97fd58: ldur            w1, [x0, #0xb]
    // 0x97fd5c: LoadField: r2 = r0->field_f
    //     0x97fd5c: ldur            w2, [x0, #0xf]
    // 0x97fd60: DecompressPointer r2
    //     0x97fd60: add             x2, x2, HEAP, lsl #32
    // 0x97fd64: LoadField: r3 = r2->field_b
    //     0x97fd64: ldur            w3, [x2, #0xb]
    // 0x97fd68: r2 = LoadInt32Instr(r1)
    //     0x97fd68: sbfx            x2, x1, #1, #0x1f
    // 0x97fd6c: stur            x2, [fp, #-0x18]
    // 0x97fd70: r1 = LoadInt32Instr(r3)
    //     0x97fd70: sbfx            x1, x3, #1, #0x1f
    // 0x97fd74: cmp             x2, x1
    // 0x97fd78: b.ne            #0x97fd84
    // 0x97fd7c: mov             x1, x0
    // 0x97fd80: r0 = _growToNextCapacity()
    //     0x97fd80: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x97fd84: ldur            x2, [fp, #-0x10]
    // 0x97fd88: ldur            x3, [fp, #-0x18]
    // 0x97fd8c: add             x4, x3, #1
    // 0x97fd90: lsl             x5, x4, #1
    // 0x97fd94: StoreField: r2->field_b = r5
    //     0x97fd94: stur            w5, [x2, #0xb]
    // 0x97fd98: LoadField: r1 = r2->field_f
    //     0x97fd98: ldur            w1, [x2, #0xf]
    // 0x97fd9c: DecompressPointer r1
    //     0x97fd9c: add             x1, x1, HEAP, lsl #32
    // 0x97fda0: ldur            x0, [fp, #-8]
    // 0x97fda4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x97fda4: add             x25, x1, x3, lsl #2
    //     0x97fda8: add             x25, x25, #0xf
    //     0x97fdac: str             w0, [x25]
    //     0x97fdb0: tbz             w0, #0, #0x97fdcc
    //     0x97fdb4: ldurb           w16, [x1, #-1]
    //     0x97fdb8: ldurb           w17, [x0, #-1]
    //     0x97fdbc: and             x16, x17, x16, lsr #2
    //     0x97fdc0: tst             x16, HEAP, lsr #32
    //     0x97fdc4: b.eq            #0x97fdcc
    //     0x97fdc8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97fdcc: r0 = Null
    //     0x97fdcc: mov             x0, NULL
    // 0x97fdd0: LeaveFrame
    //     0x97fdd0: mov             SP, fp
    //     0x97fdd4: ldp             fp, lr, [SP], #0x10
    // 0x97fdd8: ret
    //     0x97fdd8: ret             
    // 0x97fddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97fddc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97fde0: b               #0x97fcc4
    // 0x97fde4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97fde4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97fde8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97fde8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x97fe10, size: 0x48
    // 0x97fe10: EnterFrame
    //     0x97fe10: stp             fp, lr, [SP, #-0x10]!
    //     0x97fe14: mov             fp, SP
    // 0x97fe18: ldr             x0, [fp, #0x18]
    // 0x97fe1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97fe1c: ldur            w1, [x0, #0x17]
    // 0x97fe20: DecompressPointer r1
    //     0x97fe20: add             x1, x1, HEAP, lsl #32
    // 0x97fe24: CheckStackOverflow
    //     0x97fe24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97fe28: cmp             SP, x16
    //     0x97fe2c: b.ls            #0x97fe50
    // 0x97fe30: LoadField: r0 = r1->field_f
    //     0x97fe30: ldur            w0, [x1, #0xf]
    // 0x97fe34: DecompressPointer r0
    //     0x97fe34: add             x0, x0, HEAP, lsl #32
    // 0x97fe38: mov             x1, x0
    // 0x97fe3c: r0 = showOverlay()
    //     0x97fe3c: bl              #0x97fe58  ; [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::showOverlay
    // 0x97fe40: r0 = Null
    //     0x97fe40: mov             x0, NULL
    // 0x97fe44: LeaveFrame
    //     0x97fe44: mov             SP, fp
    //     0x97fe48: ldp             fp, lr, [SP], #0x10
    // 0x97fe4c: ret
    //     0x97fe4c: ret             
    // 0x97fe50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97fe50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97fe54: b               #0x97fe30
  }
  _ showOverlay(/* No info */) {
    // ** addr: 0x97fe58, size: 0xb0
    // 0x97fe58: EnterFrame
    //     0x97fe58: stp             fp, lr, [SP, #-0x10]!
    //     0x97fe5c: mov             fp, SP
    // 0x97fe60: AllocStack(0x10)
    //     0x97fe60: sub             SP, SP, #0x10
    // 0x97fe64: SetupParameters(_OverlayBuilderState this /* r1 => r1, fp-0x10 */)
    //     0x97fe64: stur            x1, [fp, #-0x10]
    // 0x97fe68: CheckStackOverflow
    //     0x97fe68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97fe6c: cmp             SP, x16
    //     0x97fe70: b.ls            #0x97fefc
    // 0x97fe74: LoadField: r0 = r1->field_13
    //     0x97fe74: ldur            w0, [x1, #0x13]
    // 0x97fe78: DecompressPointer r0
    //     0x97fe78: add             x0, x0, HEAP, lsl #32
    // 0x97fe7c: cmp             w0, NULL
    // 0x97fe80: b.ne            #0x97fee8
    // 0x97fe84: LoadField: r0 = r1->field_b
    //     0x97fe84: ldur            w0, [x1, #0xb]
    // 0x97fe88: DecompressPointer r0
    //     0x97fe88: add             x0, x0, HEAP, lsl #32
    // 0x97fe8c: cmp             w0, NULL
    // 0x97fe90: b.eq            #0x97ff04
    // 0x97fe94: LoadField: r2 = r0->field_f
    //     0x97fe94: ldur            w2, [x0, #0xf]
    // 0x97fe98: DecompressPointer r2
    //     0x97fe98: add             x2, x2, HEAP, lsl #32
    // 0x97fe9c: stur            x2, [fp, #-8]
    // 0x97fea0: r0 = OverlayEntry()
    //     0x97fea0: bl              #0x6a5798  ; AllocateOverlayEntryStub -> OverlayEntry (size=0x28)
    // 0x97fea4: mov             x1, x0
    // 0x97fea8: ldur            x2, [fp, #-8]
    // 0x97feac: stur            x0, [fp, #-8]
    // 0x97feb0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x97feb0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x97feb4: r0 = OverlayEntry()
    //     0x97feb4: bl              #0x6a55c8  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::OverlayEntry
    // 0x97feb8: ldur            x0, [fp, #-8]
    // 0x97febc: ldur            x1, [fp, #-0x10]
    // 0x97fec0: StoreField: r1->field_13 = r0
    //     0x97fec0: stur            w0, [x1, #0x13]
    //     0x97fec4: ldurb           w16, [x1, #-1]
    //     0x97fec8: ldurb           w17, [x0, #-1]
    //     0x97fecc: and             x16, x17, x16, lsr #2
    //     0x97fed0: tst             x16, HEAP, lsr #32
    //     0x97fed4: b.eq            #0x97fedc
    //     0x97fed8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97fedc: ldur            x2, [fp, #-8]
    // 0x97fee0: r0 = addToOverlay()
    //     0x97fee0: bl              #0x980094  ; [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::addToOverlay
    // 0x97fee4: b               #0x97feec
    // 0x97fee8: r0 = buildOverlay()
    //     0x97fee8: bl              #0x97ff08  ; [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::buildOverlay
    // 0x97feec: r0 = Null
    //     0x97feec: mov             x0, NULL
    // 0x97fef0: LeaveFrame
    //     0x97fef0: mov             SP, fp
    //     0x97fef4: ldp             fp, lr, [SP], #0x10
    // 0x97fef8: ret
    //     0x97fef8: ret             
    // 0x97fefc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97fefc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ff00: b               #0x97fe74
    // 0x97ff04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97ff04: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ buildOverlay(/* No info */) async {
    // ** addr: 0x97ff08, size: 0x138
    // 0x97ff08: EnterFrame
    //     0x97ff08: stp             fp, lr, [SP, #-0x10]!
    //     0x97ff0c: mov             fp, SP
    // 0x97ff10: AllocStack(0x28)
    //     0x97ff10: sub             SP, SP, #0x28
    // 0x97ff14: SetupParameters(_OverlayBuilderState this /* r1 => r1, fp-0x10 */)
    //     0x97ff14: stur            NULL, [fp, #-8]
    //     0x97ff18: stur            x1, [fp, #-0x10]
    // 0x97ff1c: CheckStackOverflow
    //     0x97ff1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97ff20: cmp             SP, x16
    //     0x97ff24: b.ls            #0x980034
    // 0x97ff28: r1 = 1
    //     0x97ff28: movz            x1, #0x1
    // 0x97ff2c: r0 = AllocateContext()
    //     0x97ff2c: bl              #0xec126c  ; AllocateContextStub
    // 0x97ff30: mov             x1, x0
    // 0x97ff34: ldur            x0, [fp, #-0x10]
    // 0x97ff38: stur            x1, [fp, #-0x18]
    // 0x97ff3c: StoreField: r1->field_f = r0
    //     0x97ff3c: stur            w0, [x1, #0xf]
    // 0x97ff40: InitAsync() -> Future<void?>
    //     0x97ff40: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x97ff44: bl              #0x661298  ; InitAsyncStub
    // 0x97ff48: r0 = LoadStaticField(0x7d4)
    //     0x97ff48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x97ff4c: ldr             x0, [x0, #0xfa8]
    // 0x97ff50: cmp             w0, NULL
    // 0x97ff54: b.eq            #0x98003c
    // 0x97ff58: LoadField: r3 = r0->field_53
    //     0x97ff58: ldur            w3, [x0, #0x53]
    // 0x97ff5c: DecompressPointer r3
    //     0x97ff5c: add             x3, x3, HEAP, lsl #32
    // 0x97ff60: stur            x3, [fp, #-0x20]
    // 0x97ff64: LoadField: r0 = r3->field_7
    //     0x97ff64: ldur            w0, [x3, #7]
    // 0x97ff68: DecompressPointer r0
    //     0x97ff68: add             x0, x0, HEAP, lsl #32
    // 0x97ff6c: ldur            x2, [fp, #-0x18]
    // 0x97ff70: stur            x0, [fp, #-0x10]
    // 0x97ff74: r1 = Function '<anonymous closure>':.
    //     0x97ff74: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ae78] AnonymousClosure: (0x980040), in [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::buildOverlay (0x97ff08)
    //     0x97ff78: ldr             x1, [x1, #0xe78]
    // 0x97ff7c: r0 = AllocateClosure()
    //     0x97ff7c: bl              #0xec1630  ; AllocateClosureStub
    // 0x97ff80: ldur            x2, [fp, #-0x10]
    // 0x97ff84: mov             x3, x0
    // 0x97ff88: r1 = Null
    //     0x97ff88: mov             x1, NULL
    // 0x97ff8c: stur            x3, [fp, #-0x10]
    // 0x97ff90: cmp             w2, NULL
    // 0x97ff94: b.eq            #0x97ffb4
    // 0x97ff98: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x97ff98: ldur            w4, [x2, #0x17]
    // 0x97ff9c: DecompressPointer r4
    //     0x97ff9c: add             x4, x4, HEAP, lsl #32
    // 0x97ffa0: r8 = X0
    //     0x97ffa0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x97ffa4: LoadField: r9 = r4->field_7
    //     0x97ffa4: ldur            x9, [x4, #7]
    // 0x97ffa8: r3 = Null
    //     0x97ffa8: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5ae80] Null
    //     0x97ffac: ldr             x3, [x3, #0xe80]
    // 0x97ffb0: blr             x9
    // 0x97ffb4: ldur            x0, [fp, #-0x20]
    // 0x97ffb8: LoadField: r1 = r0->field_b
    //     0x97ffb8: ldur            w1, [x0, #0xb]
    // 0x97ffbc: LoadField: r2 = r0->field_f
    //     0x97ffbc: ldur            w2, [x0, #0xf]
    // 0x97ffc0: DecompressPointer r2
    //     0x97ffc0: add             x2, x2, HEAP, lsl #32
    // 0x97ffc4: LoadField: r3 = r2->field_b
    //     0x97ffc4: ldur            w3, [x2, #0xb]
    // 0x97ffc8: r2 = LoadInt32Instr(r1)
    //     0x97ffc8: sbfx            x2, x1, #1, #0x1f
    // 0x97ffcc: stur            x2, [fp, #-0x28]
    // 0x97ffd0: r1 = LoadInt32Instr(r3)
    //     0x97ffd0: sbfx            x1, x3, #1, #0x1f
    // 0x97ffd4: cmp             x2, x1
    // 0x97ffd8: b.ne            #0x97ffe4
    // 0x97ffdc: mov             x1, x0
    // 0x97ffe0: r0 = _growToNextCapacity()
    //     0x97ffe0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x97ffe4: ldur            x2, [fp, #-0x20]
    // 0x97ffe8: ldur            x3, [fp, #-0x28]
    // 0x97ffec: add             x4, x3, #1
    // 0x97fff0: lsl             x5, x4, #1
    // 0x97fff4: StoreField: r2->field_b = r5
    //     0x97fff4: stur            w5, [x2, #0xb]
    // 0x97fff8: LoadField: r1 = r2->field_f
    //     0x97fff8: ldur            w1, [x2, #0xf]
    // 0x97fffc: DecompressPointer r1
    //     0x97fffc: add             x1, x1, HEAP, lsl #32
    // 0x980000: ldur            x0, [fp, #-0x10]
    // 0x980004: ArrayStore: r1[r3] = r0  ; List_4
    //     0x980004: add             x25, x1, x3, lsl #2
    //     0x980008: add             x25, x25, #0xf
    //     0x98000c: str             w0, [x25]
    //     0x980010: tbz             w0, #0, #0x98002c
    //     0x980014: ldurb           w16, [x1, #-1]
    //     0x980018: ldurb           w17, [x0, #-1]
    //     0x98001c: and             x16, x17, x16, lsr #2
    //     0x980020: tst             x16, HEAP, lsr #32
    //     0x980024: b.eq            #0x98002c
    //     0x980028: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x98002c: r0 = Null
    //     0x98002c: mov             x0, NULL
    // 0x980030: r0 = ReturnAsyncNotFuture()
    //     0x980030: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x980034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980038: b               #0x97ff28
    // 0x98003c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98003c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x980040, size: 0x54
    // 0x980040: EnterFrame
    //     0x980040: stp             fp, lr, [SP, #-0x10]!
    //     0x980044: mov             fp, SP
    // 0x980048: ldr             x0, [fp, #0x18]
    // 0x98004c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x98004c: ldur            w1, [x0, #0x17]
    // 0x980050: DecompressPointer r1
    //     0x980050: add             x1, x1, HEAP, lsl #32
    // 0x980054: CheckStackOverflow
    //     0x980054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980058: cmp             SP, x16
    //     0x98005c: b.ls            #0x98008c
    // 0x980060: LoadField: r0 = r1->field_f
    //     0x980060: ldur            w0, [x1, #0xf]
    // 0x980064: DecompressPointer r0
    //     0x980064: add             x0, x0, HEAP, lsl #32
    // 0x980068: LoadField: r1 = r0->field_13
    //     0x980068: ldur            w1, [x0, #0x13]
    // 0x98006c: DecompressPointer r1
    //     0x98006c: add             x1, x1, HEAP, lsl #32
    // 0x980070: cmp             w1, NULL
    // 0x980074: b.eq            #0x98007c
    // 0x980078: r0 = markNeedsBuild()
    //     0x980078: bl              #0x650960  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::markNeedsBuild
    // 0x98007c: r0 = Null
    //     0x98007c: mov             x0, NULL
    // 0x980080: LeaveFrame
    //     0x980080: mov             SP, fp
    //     0x980084: ldp             fp, lr, [SP], #0x10
    // 0x980088: ret
    //     0x980088: ret             
    // 0x98008c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98008c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980090: b               #0x980060
  }
  _ addToOverlay(/* No info */) async {
    // ** addr: 0x980094, size: 0xd0
    // 0x980094: EnterFrame
    //     0x980094: stp             fp, lr, [SP, #-0x10]!
    //     0x980098: mov             fp, SP
    // 0x98009c: AllocStack(0x30)
    //     0x98009c: sub             SP, SP, #0x30
    // 0x9800a0: SetupParameters(_OverlayBuilderState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x9800a0: stur            NULL, [fp, #-8]
    //     0x9800a4: stur            x1, [fp, #-0x10]
    //     0x9800a8: stur            x2, [fp, #-0x18]
    // 0x9800ac: CheckStackOverflow
    //     0x9800ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9800b0: cmp             SP, x16
    //     0x9800b4: b.ls            #0x980154
    // 0x9800b8: InitAsync() -> Future<void?>
    //     0x9800b8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x9800bc: bl              #0x661298  ; InitAsyncStub
    // 0x9800c0: ldur            x0, [fp, #-0x10]
    // 0x9800c4: LoadField: r1 = r0->field_f
    //     0x9800c4: ldur            w1, [x0, #0xf]
    // 0x9800c8: DecompressPointer r1
    //     0x9800c8: add             x1, x1, HEAP, lsl #32
    // 0x9800cc: cmp             w1, NULL
    // 0x9800d0: b.eq            #0x98014c
    // 0x9800d4: r0 = of()
    //     0x9800d4: bl              #0x91e504  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidget::of
    // 0x9800d8: LoadField: r1 = r0->field_f
    //     0x9800d8: ldur            w1, [x0, #0xf]
    // 0x9800dc: DecompressPointer r1
    //     0x9800dc: add             x1, x1, HEAP, lsl #32
    // 0x9800e0: cmp             w1, NULL
    // 0x9800e4: b.eq            #0x98015c
    // 0x9800e8: r16 = <OverlayState>
    //     0x9800e8: ldr             x16, [PP, #0x4cf8]  ; [pp+0x4cf8] TypeArguments: <OverlayState>
    // 0x9800ec: stp             x1, x16, [SP]
    // 0x9800f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9800f0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9800f4: r0 = findAncestorStateOfType()
    //     0x9800f4: bl              #0x6a4a9c  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorStateOfType
    // 0x9800f8: mov             x1, x0
    // 0x9800fc: ldur            x0, [fp, #-0x10]
    // 0x980100: stur            x1, [fp, #-0x20]
    // 0x980104: LoadField: r2 = r0->field_f
    //     0x980104: ldur            w2, [x0, #0xf]
    // 0x980108: DecompressPointer r2
    //     0x980108: add             x2, x2, HEAP, lsl #32
    // 0x98010c: cmp             w2, NULL
    // 0x980110: b.eq            #0x980160
    // 0x980114: r16 = <OverlayState>
    //     0x980114: ldr             x16, [PP, #0x4cf8]  ; [pp+0x4cf8] TypeArguments: <OverlayState>
    // 0x980118: stp             x2, x16, [SP]
    // 0x98011c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x98011c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x980120: r0 = findAncestorStateOfType()
    //     0x980120: bl              #0x6a4a9c  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorStateOfType
    // 0x980124: mov             x1, x0
    // 0x980128: ldur            x0, [fp, #-0x20]
    // 0x98012c: cmp             w0, NULL
    // 0x980130: b.eq            #0x980138
    // 0x980134: mov             x1, x0
    // 0x980138: cmp             w1, NULL
    // 0x98013c: b.eq            #0x98014c
    // 0x980140: ldur            x2, [fp, #-0x18]
    // 0x980144: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x980144: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x980148: r0 = insert()
    //     0x980148: bl              #0x6a4e34  ; [package:flutter/src/widgets/overlay.dart] OverlayState::insert
    // 0x98014c: r0 = Null
    //     0x98014c: mov             x0, NULL
    // 0x980150: r0 = ReturnAsyncNotFuture()
    //     0x980150: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x980154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980154: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980158: b               #0x9800b8
    // 0x98015c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98015c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x980160: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980160: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a1c50, size: 0x1b4
    // 0x9a1c50: EnterFrame
    //     0x9a1c50: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1c54: mov             fp, SP
    // 0x9a1c58: AllocStack(0x20)
    //     0x9a1c58: sub             SP, SP, #0x20
    // 0x9a1c5c: SetupParameters(_OverlayBuilderState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9a1c5c: mov             x0, x2
    //     0x9a1c60: stur            x1, [fp, #-8]
    //     0x9a1c64: stur            x2, [fp, #-0x10]
    // 0x9a1c68: CheckStackOverflow
    //     0x9a1c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1c6c: cmp             SP, x16
    //     0x9a1c70: b.ls            #0x9a1df8
    // 0x9a1c74: r1 = 1
    //     0x9a1c74: movz            x1, #0x1
    // 0x9a1c78: r0 = AllocateContext()
    //     0x9a1c78: bl              #0xec126c  ; AllocateContextStub
    // 0x9a1c7c: mov             x4, x0
    // 0x9a1c80: ldur            x3, [fp, #-8]
    // 0x9a1c84: stur            x4, [fp, #-0x18]
    // 0x9a1c88: StoreField: r4->field_f = r3
    //     0x9a1c88: stur            w3, [x4, #0xf]
    // 0x9a1c8c: ldur            x0, [fp, #-0x10]
    // 0x9a1c90: r2 = Null
    //     0x9a1c90: mov             x2, NULL
    // 0x9a1c94: r1 = Null
    //     0x9a1c94: mov             x1, NULL
    // 0x9a1c98: r4 = 60
    //     0x9a1c98: movz            x4, #0x3c
    // 0x9a1c9c: branchIfSmi(r0, 0x9a1ca8)
    //     0x9a1c9c: tbz             w0, #0, #0x9a1ca8
    // 0x9a1ca0: r4 = LoadClassIdInstr(r0)
    //     0x9a1ca0: ldur            x4, [x0, #-1]
    //     0x9a1ca4: ubfx            x4, x4, #0xc, #0x14
    // 0x9a1ca8: r17 = 4693
    //     0x9a1ca8: movz            x17, #0x1255
    // 0x9a1cac: cmp             x4, x17
    // 0x9a1cb0: b.eq            #0x9a1cc8
    // 0x9a1cb4: r8 = OverlayBuilder
    //     0x9a1cb4: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5aea8] Type: OverlayBuilder
    //     0x9a1cb8: ldr             x8, [x8, #0xea8]
    // 0x9a1cbc: r3 = Null
    //     0x9a1cbc: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5aeb0] Null
    //     0x9a1cc0: ldr             x3, [x3, #0xeb0]
    // 0x9a1cc4: r0 = OverlayBuilder()
    //     0x9a1cc4: bl              #0x97fdec  ; IsType_OverlayBuilder_Stub
    // 0x9a1cc8: ldur            x0, [fp, #-8]
    // 0x9a1ccc: LoadField: r2 = r0->field_7
    //     0x9a1ccc: ldur            w2, [x0, #7]
    // 0x9a1cd0: DecompressPointer r2
    //     0x9a1cd0: add             x2, x2, HEAP, lsl #32
    // 0x9a1cd4: ldur            x0, [fp, #-0x10]
    // 0x9a1cd8: r1 = Null
    //     0x9a1cd8: mov             x1, NULL
    // 0x9a1cdc: cmp             w2, NULL
    // 0x9a1ce0: b.eq            #0x9a1d04
    // 0x9a1ce4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a1ce4: ldur            w4, [x2, #0x17]
    // 0x9a1ce8: DecompressPointer r4
    //     0x9a1ce8: add             x4, x4, HEAP, lsl #32
    // 0x9a1cec: r8 = X0 bound StatefulWidget
    //     0x9a1cec: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a1cf0: ldr             x8, [x8, #0x7f8]
    // 0x9a1cf4: LoadField: r9 = r4->field_7
    //     0x9a1cf4: ldur            x9, [x4, #7]
    // 0x9a1cf8: r3 = Null
    //     0x9a1cf8: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5aec0] Null
    //     0x9a1cfc: ldr             x3, [x3, #0xec0]
    // 0x9a1d00: blr             x9
    // 0x9a1d04: r0 = LoadStaticField(0x7d4)
    //     0x9a1d04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9a1d08: ldr             x0, [x0, #0xfa8]
    // 0x9a1d0c: cmp             w0, NULL
    // 0x9a1d10: b.eq            #0x9a1e00
    // 0x9a1d14: LoadField: r3 = r0->field_53
    //     0x9a1d14: ldur            w3, [x0, #0x53]
    // 0x9a1d18: DecompressPointer r3
    //     0x9a1d18: add             x3, x3, HEAP, lsl #32
    // 0x9a1d1c: stur            x3, [fp, #-0x10]
    // 0x9a1d20: LoadField: r0 = r3->field_7
    //     0x9a1d20: ldur            w0, [x3, #7]
    // 0x9a1d24: DecompressPointer r0
    //     0x9a1d24: add             x0, x0, HEAP, lsl #32
    // 0x9a1d28: ldur            x2, [fp, #-0x18]
    // 0x9a1d2c: stur            x0, [fp, #-8]
    // 0x9a1d30: r1 = Function '<anonymous closure>':.
    //     0x9a1d30: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5aed0] AnonymousClosure: (0x9a1e04), in [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::reassemble (0x9a2944)
    //     0x9a1d34: ldr             x1, [x1, #0xed0]
    // 0x9a1d38: r0 = AllocateClosure()
    //     0x9a1d38: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a1d3c: ldur            x2, [fp, #-8]
    // 0x9a1d40: mov             x3, x0
    // 0x9a1d44: r1 = Null
    //     0x9a1d44: mov             x1, NULL
    // 0x9a1d48: stur            x3, [fp, #-8]
    // 0x9a1d4c: cmp             w2, NULL
    // 0x9a1d50: b.eq            #0x9a1d70
    // 0x9a1d54: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a1d54: ldur            w4, [x2, #0x17]
    // 0x9a1d58: DecompressPointer r4
    //     0x9a1d58: add             x4, x4, HEAP, lsl #32
    // 0x9a1d5c: r8 = X0
    //     0x9a1d5c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9a1d60: LoadField: r9 = r4->field_7
    //     0x9a1d60: ldur            x9, [x4, #7]
    // 0x9a1d64: r3 = Null
    //     0x9a1d64: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5aed8] Null
    //     0x9a1d68: ldr             x3, [x3, #0xed8]
    // 0x9a1d6c: blr             x9
    // 0x9a1d70: ldur            x0, [fp, #-0x10]
    // 0x9a1d74: LoadField: r1 = r0->field_b
    //     0x9a1d74: ldur            w1, [x0, #0xb]
    // 0x9a1d78: LoadField: r2 = r0->field_f
    //     0x9a1d78: ldur            w2, [x0, #0xf]
    // 0x9a1d7c: DecompressPointer r2
    //     0x9a1d7c: add             x2, x2, HEAP, lsl #32
    // 0x9a1d80: LoadField: r3 = r2->field_b
    //     0x9a1d80: ldur            w3, [x2, #0xb]
    // 0x9a1d84: r2 = LoadInt32Instr(r1)
    //     0x9a1d84: sbfx            x2, x1, #1, #0x1f
    // 0x9a1d88: stur            x2, [fp, #-0x20]
    // 0x9a1d8c: r1 = LoadInt32Instr(r3)
    //     0x9a1d8c: sbfx            x1, x3, #1, #0x1f
    // 0x9a1d90: cmp             x2, x1
    // 0x9a1d94: b.ne            #0x9a1da0
    // 0x9a1d98: mov             x1, x0
    // 0x9a1d9c: r0 = _growToNextCapacity()
    //     0x9a1d9c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9a1da0: ldur            x2, [fp, #-0x10]
    // 0x9a1da4: ldur            x3, [fp, #-0x20]
    // 0x9a1da8: add             x4, x3, #1
    // 0x9a1dac: lsl             x5, x4, #1
    // 0x9a1db0: StoreField: r2->field_b = r5
    //     0x9a1db0: stur            w5, [x2, #0xb]
    // 0x9a1db4: LoadField: r1 = r2->field_f
    //     0x9a1db4: ldur            w1, [x2, #0xf]
    // 0x9a1db8: DecompressPointer r1
    //     0x9a1db8: add             x1, x1, HEAP, lsl #32
    // 0x9a1dbc: ldur            x0, [fp, #-8]
    // 0x9a1dc0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9a1dc0: add             x25, x1, x3, lsl #2
    //     0x9a1dc4: add             x25, x25, #0xf
    //     0x9a1dc8: str             w0, [x25]
    //     0x9a1dcc: tbz             w0, #0, #0x9a1de8
    //     0x9a1dd0: ldurb           w16, [x1, #-1]
    //     0x9a1dd4: ldurb           w17, [x0, #-1]
    //     0x9a1dd8: and             x16, x17, x16, lsr #2
    //     0x9a1ddc: tst             x16, HEAP, lsr #32
    //     0x9a1de0: b.eq            #0x9a1de8
    //     0x9a1de4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x9a1de8: r0 = Null
    //     0x9a1de8: mov             x0, NULL
    // 0x9a1dec: LeaveFrame
    //     0x9a1dec: mov             SP, fp
    //     0x9a1df0: ldp             fp, lr, [SP], #0x10
    // 0x9a1df4: ret
    //     0x9a1df4: ret             
    // 0x9a1df8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1df8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1dfc: b               #0x9a1c74
    // 0x9a1e00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1e00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9a1e04, size: 0x48
    // 0x9a1e04: EnterFrame
    //     0x9a1e04: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1e08: mov             fp, SP
    // 0x9a1e0c: ldr             x0, [fp, #0x18]
    // 0x9a1e10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9a1e10: ldur            w1, [x0, #0x17]
    // 0x9a1e14: DecompressPointer r1
    //     0x9a1e14: add             x1, x1, HEAP, lsl #32
    // 0x9a1e18: CheckStackOverflow
    //     0x9a1e18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1e1c: cmp             SP, x16
    //     0x9a1e20: b.ls            #0x9a1e44
    // 0x9a1e24: LoadField: r0 = r1->field_f
    //     0x9a1e24: ldur            w0, [x1, #0xf]
    // 0x9a1e28: DecompressPointer r0
    //     0x9a1e28: add             x0, x0, HEAP, lsl #32
    // 0x9a1e2c: mov             x1, x0
    // 0x9a1e30: r0 = syncWidgetAndOverlay()
    //     0x9a1e30: bl              #0x9a1e4c  ; [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::syncWidgetAndOverlay
    // 0x9a1e34: r0 = Null
    //     0x9a1e34: mov             x0, NULL
    // 0x9a1e38: LeaveFrame
    //     0x9a1e38: mov             SP, fp
    //     0x9a1e3c: ldp             fp, lr, [SP], #0x10
    // 0x9a1e40: ret
    //     0x9a1e40: ret             
    // 0x9a1e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1e44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1e48: b               #0x9a1e24
  }
  _ syncWidgetAndOverlay(/* No info */) {
    // ** addr: 0x9a1e4c, size: 0x70
    // 0x9a1e4c: EnterFrame
    //     0x9a1e4c: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1e50: mov             fp, SP
    // 0x9a1e54: CheckStackOverflow
    //     0x9a1e54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1e58: cmp             SP, x16
    //     0x9a1e5c: b.ls            #0x9a1eac
    // 0x9a1e60: LoadField: r0 = r1->field_13
    //     0x9a1e60: ldur            w0, [x1, #0x13]
    // 0x9a1e64: DecompressPointer r0
    //     0x9a1e64: add             x0, x0, HEAP, lsl #32
    // 0x9a1e68: cmp             w0, NULL
    // 0x9a1e6c: b.eq            #0x9a1e80
    // 0x9a1e70: LoadField: r2 = r1->field_b
    //     0x9a1e70: ldur            w2, [x1, #0xb]
    // 0x9a1e74: DecompressPointer r2
    //     0x9a1e74: add             x2, x2, HEAP, lsl #32
    // 0x9a1e78: cmp             w2, NULL
    // 0x9a1e7c: b.eq            #0x9a1eb4
    // 0x9a1e80: cmp             w0, NULL
    // 0x9a1e84: b.ne            #0x9a1e9c
    // 0x9a1e88: LoadField: r0 = r1->field_b
    //     0x9a1e88: ldur            w0, [x1, #0xb]
    // 0x9a1e8c: DecompressPointer r0
    //     0x9a1e8c: add             x0, x0, HEAP, lsl #32
    // 0x9a1e90: cmp             w0, NULL
    // 0x9a1e94: b.eq            #0x9a1eb8
    // 0x9a1e98: r0 = showOverlay()
    //     0x9a1e98: bl              #0x97fe58  ; [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::showOverlay
    // 0x9a1e9c: r0 = Null
    //     0x9a1e9c: mov             x0, NULL
    // 0x9a1ea0: LeaveFrame
    //     0x9a1ea0: mov             SP, fp
    //     0x9a1ea4: ldp             fp, lr, [SP], #0x10
    // 0x9a1ea8: ret
    //     0x9a1ea8: ret             
    // 0x9a1eac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1eac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1eb0: b               #0x9a1e60
    // 0x9a1eb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1eb4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a1eb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a1eb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ reassemble(/* No info */) {
    // ** addr: 0x9a2944, size: 0x130
    // 0x9a2944: EnterFrame
    //     0x9a2944: stp             fp, lr, [SP, #-0x10]!
    //     0x9a2948: mov             fp, SP
    // 0x9a294c: AllocStack(0x18)
    //     0x9a294c: sub             SP, SP, #0x18
    // 0x9a2950: SetupParameters(_OverlayBuilderState this /* r1 => r1, fp-0x8 */)
    //     0x9a2950: stur            x1, [fp, #-8]
    // 0x9a2954: CheckStackOverflow
    //     0x9a2954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a2958: cmp             SP, x16
    //     0x9a295c: b.ls            #0x9a2a68
    // 0x9a2960: r1 = 1
    //     0x9a2960: movz            x1, #0x1
    // 0x9a2964: r0 = AllocateContext()
    //     0x9a2964: bl              #0xec126c  ; AllocateContextStub
    // 0x9a2968: mov             x1, x0
    // 0x9a296c: ldur            x0, [fp, #-8]
    // 0x9a2970: StoreField: r1->field_f = r0
    //     0x9a2970: stur            w0, [x1, #0xf]
    // 0x9a2974: r0 = LoadStaticField(0x7d4)
    //     0x9a2974: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9a2978: ldr             x0, [x0, #0xfa8]
    // 0x9a297c: cmp             w0, NULL
    // 0x9a2980: b.eq            #0x9a2a70
    // 0x9a2984: LoadField: r3 = r0->field_53
    //     0x9a2984: ldur            w3, [x0, #0x53]
    // 0x9a2988: DecompressPointer r3
    //     0x9a2988: add             x3, x3, HEAP, lsl #32
    // 0x9a298c: stur            x3, [fp, #-0x10]
    // 0x9a2990: LoadField: r0 = r3->field_7
    //     0x9a2990: ldur            w0, [x3, #7]
    // 0x9a2994: DecompressPointer r0
    //     0x9a2994: add             x0, x0, HEAP, lsl #32
    // 0x9a2998: mov             x2, x1
    // 0x9a299c: stur            x0, [fp, #-8]
    // 0x9a29a0: r1 = Function '<anonymous closure>':.
    //     0x9a29a0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ae90] AnonymousClosure: (0x9a1e04), in [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::reassemble (0x9a2944)
    //     0x9a29a4: ldr             x1, [x1, #0xe90]
    // 0x9a29a8: r0 = AllocateClosure()
    //     0x9a29a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a29ac: ldur            x2, [fp, #-8]
    // 0x9a29b0: mov             x3, x0
    // 0x9a29b4: r1 = Null
    //     0x9a29b4: mov             x1, NULL
    // 0x9a29b8: stur            x3, [fp, #-8]
    // 0x9a29bc: cmp             w2, NULL
    // 0x9a29c0: b.eq            #0x9a29e0
    // 0x9a29c4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a29c4: ldur            w4, [x2, #0x17]
    // 0x9a29c8: DecompressPointer r4
    //     0x9a29c8: add             x4, x4, HEAP, lsl #32
    // 0x9a29cc: r8 = X0
    //     0x9a29cc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9a29d0: LoadField: r9 = r4->field_7
    //     0x9a29d0: ldur            x9, [x4, #7]
    // 0x9a29d4: r3 = Null
    //     0x9a29d4: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5ae98] Null
    //     0x9a29d8: ldr             x3, [x3, #0xe98]
    // 0x9a29dc: blr             x9
    // 0x9a29e0: ldur            x0, [fp, #-0x10]
    // 0x9a29e4: LoadField: r1 = r0->field_b
    //     0x9a29e4: ldur            w1, [x0, #0xb]
    // 0x9a29e8: LoadField: r2 = r0->field_f
    //     0x9a29e8: ldur            w2, [x0, #0xf]
    // 0x9a29ec: DecompressPointer r2
    //     0x9a29ec: add             x2, x2, HEAP, lsl #32
    // 0x9a29f0: LoadField: r3 = r2->field_b
    //     0x9a29f0: ldur            w3, [x2, #0xb]
    // 0x9a29f4: r2 = LoadInt32Instr(r1)
    //     0x9a29f4: sbfx            x2, x1, #1, #0x1f
    // 0x9a29f8: stur            x2, [fp, #-0x18]
    // 0x9a29fc: r1 = LoadInt32Instr(r3)
    //     0x9a29fc: sbfx            x1, x3, #1, #0x1f
    // 0x9a2a00: cmp             x2, x1
    // 0x9a2a04: b.ne            #0x9a2a10
    // 0x9a2a08: mov             x1, x0
    // 0x9a2a0c: r0 = _growToNextCapacity()
    //     0x9a2a0c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9a2a10: ldur            x2, [fp, #-0x10]
    // 0x9a2a14: ldur            x3, [fp, #-0x18]
    // 0x9a2a18: add             x4, x3, #1
    // 0x9a2a1c: lsl             x5, x4, #1
    // 0x9a2a20: StoreField: r2->field_b = r5
    //     0x9a2a20: stur            w5, [x2, #0xb]
    // 0x9a2a24: LoadField: r1 = r2->field_f
    //     0x9a2a24: ldur            w1, [x2, #0xf]
    // 0x9a2a28: DecompressPointer r1
    //     0x9a2a28: add             x1, x1, HEAP, lsl #32
    // 0x9a2a2c: ldur            x0, [fp, #-8]
    // 0x9a2a30: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9a2a30: add             x25, x1, x3, lsl #2
    //     0x9a2a34: add             x25, x25, #0xf
    //     0x9a2a38: str             w0, [x25]
    //     0x9a2a3c: tbz             w0, #0, #0x9a2a58
    //     0x9a2a40: ldurb           w16, [x1, #-1]
    //     0x9a2a44: ldurb           w17, [x0, #-1]
    //     0x9a2a48: and             x16, x17, x16, lsr #2
    //     0x9a2a4c: tst             x16, HEAP, lsr #32
    //     0x9a2a50: b.eq            #0x9a2a58
    //     0x9a2a54: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x9a2a58: r0 = Null
    //     0x9a2a58: mov             x0, NULL
    // 0x9a2a5c: LeaveFrame
    //     0x9a2a5c: mov             SP, fp
    //     0x9a2a60: ldp             fp, lr, [SP], #0x10
    // 0x9a2a64: ret
    //     0x9a2a64: ret             
    // 0x9a2a68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a2a68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a2a6c: b               #0x9a2960
    // 0x9a2a70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a2a70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa4c460, size: 0x5c
    // 0xa4c460: EnterFrame
    //     0xa4c460: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c464: mov             fp, SP
    // 0xa4c468: AllocStack(0x8)
    //     0xa4c468: sub             SP, SP, #8
    // 0xa4c46c: SetupParameters(_OverlayBuilderState this /* r1 => r0, fp-0x8 */)
    //     0xa4c46c: mov             x0, x1
    //     0xa4c470: stur            x1, [fp, #-8]
    // 0xa4c474: CheckStackOverflow
    //     0xa4c474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c478: cmp             SP, x16
    //     0xa4c47c: b.ls            #0xa4c4b0
    // 0xa4c480: mov             x1, x0
    // 0xa4c484: r0 = buildOverlay()
    //     0xa4c484: bl              #0x97ff08  ; [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::buildOverlay
    // 0xa4c488: ldur            x1, [fp, #-8]
    // 0xa4c48c: LoadField: r2 = r1->field_b
    //     0xa4c48c: ldur            w2, [x1, #0xb]
    // 0xa4c490: DecompressPointer r2
    //     0xa4c490: add             x2, x2, HEAP, lsl #32
    // 0xa4c494: cmp             w2, NULL
    // 0xa4c498: b.eq            #0xa4c4b8
    // 0xa4c49c: LoadField: r0 = r2->field_13
    //     0xa4c49c: ldur            w0, [x2, #0x13]
    // 0xa4c4a0: DecompressPointer r0
    //     0xa4c4a0: add             x0, x0, HEAP, lsl #32
    // 0xa4c4a4: LeaveFrame
    //     0xa4c4a4: mov             SP, fp
    //     0xa4c4a8: ldp             fp, lr, [SP], #0x10
    // 0xa4c4ac: ret
    //     0xa4c4ac: ret             
    // 0xa4c4b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c4b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c4b4: b               #0xa4c480
    // 0xa4c4b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4c4b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa8404c, size: 0x40
    // 0xa8404c: EnterFrame
    //     0xa8404c: stp             fp, lr, [SP, #-0x10]!
    //     0xa84050: mov             fp, SP
    // 0xa84054: CheckStackOverflow
    //     0xa84054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84058: cmp             SP, x16
    //     0xa8405c: b.ls            #0xa84084
    // 0xa84060: LoadField: r0 = r1->field_13
    //     0xa84060: ldur            w0, [x1, #0x13]
    // 0xa84064: DecompressPointer r0
    //     0xa84064: add             x0, x0, HEAP, lsl #32
    // 0xa84068: cmp             w0, NULL
    // 0xa8406c: b.eq            #0xa84074
    // 0xa84070: r0 = hideOverlay()
    //     0xa84070: bl              #0xa8408c  ; [package:showcaseview/src/layout_overlays.dart] _OverlayBuilderState::hideOverlay
    // 0xa84074: r0 = Null
    //     0xa84074: mov             x0, NULL
    // 0xa84078: LeaveFrame
    //     0xa84078: mov             SP, fp
    //     0xa8407c: ldp             fp, lr, [SP], #0x10
    // 0xa84080: ret
    //     0xa84080: ret             
    // 0xa84084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa84084: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa84088: b               #0xa84060
  }
  _ hideOverlay(/* No info */) {
    // ** addr: 0xa8408c, size: 0x54
    // 0xa8408c: EnterFrame
    //     0xa8408c: stp             fp, lr, [SP, #-0x10]!
    //     0xa84090: mov             fp, SP
    // 0xa84094: AllocStack(0x8)
    //     0xa84094: sub             SP, SP, #8
    // 0xa84098: SetupParameters(_OverlayBuilderState this /* r1 => r0, fp-0x8 */)
    //     0xa84098: mov             x0, x1
    //     0xa8409c: stur            x1, [fp, #-8]
    // 0xa840a0: CheckStackOverflow
    //     0xa840a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa840a4: cmp             SP, x16
    //     0xa840a8: b.ls            #0xa840d8
    // 0xa840ac: LoadField: r1 = r0->field_13
    //     0xa840ac: ldur            w1, [x0, #0x13]
    // 0xa840b0: DecompressPointer r1
    //     0xa840b0: add             x1, x1, HEAP, lsl #32
    // 0xa840b4: cmp             w1, NULL
    // 0xa840b8: b.eq            #0xa840c8
    // 0xa840bc: r0 = remove()
    //     0xa840bc: bl              #0x64e5d0  ; [package:flutter/src/widgets/overlay.dart] OverlayEntry::remove
    // 0xa840c0: ldur            x1, [fp, #-8]
    // 0xa840c4: StoreField: r1->field_13 = rNULL
    //     0xa840c4: stur            NULL, [x1, #0x13]
    // 0xa840c8: r0 = Null
    //     0xa840c8: mov             x0, NULL
    // 0xa840cc: LeaveFrame
    //     0xa840cc: mov             SP, fp
    //     0xa840d0: ldp             fp, lr, [SP], #0x10
    // 0xa840d4: ret
    //     0xa840d4: ret             
    // 0xa840d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa840d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa840dc: b               #0xa840ac
  }
}

// class id: 4693, size: 0x18, field offset: 0xc
//   const constructor, 
class OverlayBuilder extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa952a0, size: 0x24
    // 0xa952a0: EnterFrame
    //     0xa952a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa952a4: mov             fp, SP
    // 0xa952a8: mov             x0, x1
    // 0xa952ac: r1 = <OverlayBuilder>
    //     0xa952ac: add             x1, PP, #0x57, lsl #12  ; [pp+0x579c0] TypeArguments: <OverlayBuilder>
    //     0xa952b0: ldr             x1, [x1, #0x9c0]
    // 0xa952b4: r0 = _OverlayBuilderState()
    //     0xa952b4: bl              #0xa952c4  ; Allocate_OverlayBuilderStateStub -> _OverlayBuilderState (size=0x18)
    // 0xa952b8: LeaveFrame
    //     0xa952b8: mov             SP, fp
    //     0xa952bc: ldp             fp, lr, [SP], #0x10
    // 0xa952c0: ret
    //     0xa952c0: ret             
  }
}

// class id: 4916, size: 0x1c, field offset: 0xc
//   const constructor, 
class AnchoredOverlay extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbb9968, size: 0x58
    // 0xbb9968: EnterFrame
    //     0xbb9968: stp             fp, lr, [SP, #-0x10]!
    //     0xbb996c: mov             fp, SP
    // 0xbb9970: AllocStack(0x8)
    //     0xbb9970: sub             SP, SP, #8
    // 0xbb9974: SetupParameters(AnchoredOverlay this /* r1 => r1, fp-0x8 */)
    //     0xbb9974: stur            x1, [fp, #-8]
    // 0xbb9978: r1 = 1
    //     0xbb9978: movz            x1, #0x1
    // 0xbb997c: r0 = AllocateContext()
    //     0xbb997c: bl              #0xec126c  ; AllocateContextStub
    // 0xbb9980: mov             x1, x0
    // 0xbb9984: ldur            x0, [fp, #-8]
    // 0xbb9988: StoreField: r1->field_f = r0
    //     0xbb9988: stur            w0, [x1, #0xf]
    // 0xbb998c: mov             x2, x1
    // 0xbb9990: r1 = Function '<anonymous closure>':.
    //     0xbb9990: add             x1, PP, #0x51, lsl #12  ; [pp+0x51140] AnonymousClosure: (0xbb99c0), in [package:showcaseview/src/layout_overlays.dart] AnchoredOverlay::build (0xbb9968)
    //     0xbb9994: ldr             x1, [x1, #0x140]
    // 0xbb9998: r0 = AllocateClosure()
    //     0xbb9998: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb999c: r1 = <BoxConstraints>
    //     0xbb999c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37fa8] TypeArguments: <BoxConstraints>
    //     0xbb99a0: ldr             x1, [x1, #0xfa8]
    // 0xbb99a4: stur            x0, [fp, #-8]
    // 0xbb99a8: r0 = LayoutBuilder()
    //     0xbb99a8: bl              #0x9f1460  ; AllocateLayoutBuilderStub -> LayoutBuilder (size=0x14)
    // 0xbb99ac: ldur            x1, [fp, #-8]
    // 0xbb99b0: StoreField: r0->field_f = r1
    //     0xbb99b0: stur            w1, [x0, #0xf]
    // 0xbb99b4: LeaveFrame
    //     0xbb99b4: mov             SP, fp
    //     0xbb99b8: ldp             fp, lr, [SP], #0x10
    // 0xbb99bc: ret
    //     0xbb99bc: ret             
  }
  [closure] OverlayBuilder <anonymous closure>(dynamic, BuildContext, BoxConstraints) {
    // ** addr: 0xbb99c0, size: 0x94
    // 0xbb99c0: EnterFrame
    //     0xbb99c0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb99c4: mov             fp, SP
    // 0xbb99c8: AllocStack(0x18)
    //     0xbb99c8: sub             SP, SP, #0x18
    // 0xbb99cc: SetupParameters()
    //     0xbb99cc: ldr             x0, [fp, #0x20]
    //     0xbb99d0: ldur            w1, [x0, #0x17]
    //     0xbb99d4: add             x1, x1, HEAP, lsl #32
    //     0xbb99d8: stur            x1, [fp, #-8]
    // 0xbb99dc: r1 = 1
    //     0xbb99dc: movz            x1, #0x1
    // 0xbb99e0: r0 = AllocateContext()
    //     0xbb99e0: bl              #0xec126c  ; AllocateContextStub
    // 0xbb99e4: mov             x1, x0
    // 0xbb99e8: ldur            x0, [fp, #-8]
    // 0xbb99ec: stur            x1, [fp, #-0x10]
    // 0xbb99f0: StoreField: r1->field_b = r0
    //     0xbb99f0: stur            w0, [x1, #0xb]
    // 0xbb99f4: ldr             x2, [fp, #0x18]
    // 0xbb99f8: StoreField: r1->field_f = r2
    //     0xbb99f8: stur            w2, [x1, #0xf]
    // 0xbb99fc: LoadField: r2 = r0->field_f
    //     0xbb99fc: ldur            w2, [x0, #0xf]
    // 0xbb9a00: DecompressPointer r2
    //     0xbb9a00: add             x2, x2, HEAP, lsl #32
    // 0xbb9a04: LoadField: r0 = r2->field_13
    //     0xbb9a04: ldur            w0, [x2, #0x13]
    // 0xbb9a08: DecompressPointer r0
    //     0xbb9a08: add             x0, x0, HEAP, lsl #32
    // 0xbb9a0c: stur            x0, [fp, #-8]
    // 0xbb9a10: r0 = OverlayBuilder()
    //     0xbb9a10: bl              #0xbb9a54  ; AllocateOverlayBuilderStub -> OverlayBuilder (size=0x18)
    // 0xbb9a14: mov             x3, x0
    // 0xbb9a18: r0 = true
    //     0xbb9a18: add             x0, NULL, #0x20  ; true
    // 0xbb9a1c: stur            x3, [fp, #-0x18]
    // 0xbb9a20: StoreField: r3->field_b = r0
    //     0xbb9a20: stur            w0, [x3, #0xb]
    // 0xbb9a24: ldur            x2, [fp, #-0x10]
    // 0xbb9a28: r1 = Function '<anonymous closure>':.
    //     0xbb9a28: add             x1, PP, #0x51, lsl #12  ; [pp+0x51148] AnonymousClosure: (0xbb9a60), in [package:showcaseview/src/layout_overlays.dart] AnchoredOverlay::build (0xbb9968)
    //     0xbb9a2c: ldr             x1, [x1, #0x148]
    // 0xbb9a30: r0 = AllocateClosure()
    //     0xbb9a30: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb9a34: mov             x1, x0
    // 0xbb9a38: ldur            x0, [fp, #-0x18]
    // 0xbb9a3c: StoreField: r0->field_f = r1
    //     0xbb9a3c: stur            w1, [x0, #0xf]
    // 0xbb9a40: ldur            x1, [fp, #-8]
    // 0xbb9a44: StoreField: r0->field_13 = r1
    //     0xbb9a44: stur            w1, [x0, #0x13]
    // 0xbb9a48: LeaveFrame
    //     0xbb9a48: mov             SP, fp
    //     0xbb9a4c: ldp             fp, lr, [SP], #0x10
    // 0xbb9a50: ret
    //     0xbb9a50: ret             
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xbb9a60, size: 0x1d8
    // 0xbb9a60: EnterFrame
    //     0xbb9a60: stp             fp, lr, [SP, #-0x10]!
    //     0xbb9a64: mov             fp, SP
    // 0xbb9a68: AllocStack(0x60)
    //     0xbb9a68: sub             SP, SP, #0x60
    // 0xbb9a6c: SetupParameters()
    //     0xbb9a6c: ldr             x0, [fp, #0x18]
    //     0xbb9a70: ldur            w2, [x0, #0x17]
    //     0xbb9a74: add             x2, x2, HEAP, lsl #32
    //     0xbb9a78: stur            x2, [fp, #-8]
    // 0xbb9a7c: CheckStackOverflow
    //     0xbb9a7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb9a80: cmp             SP, x16
    //     0xbb9a84: b.ls            #0xbb9c2c
    // 0xbb9a88: LoadField: r1 = r2->field_f
    //     0xbb9a88: ldur            w1, [x2, #0xf]
    // 0xbb9a8c: DecompressPointer r1
    //     0xbb9a8c: add             x1, x1, HEAP, lsl #32
    // 0xbb9a90: r0 = findRenderObject()
    //     0xbb9a90: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0xbb9a94: mov             x3, x0
    // 0xbb9a98: r2 = Null
    //     0xbb9a98: mov             x2, NULL
    // 0xbb9a9c: r1 = Null
    //     0xbb9a9c: mov             x1, NULL
    // 0xbb9aa0: stur            x3, [fp, #-0x10]
    // 0xbb9aa4: r4 = LoadClassIdInstr(r0)
    //     0xbb9aa4: ldur            x4, [x0, #-1]
    //     0xbb9aa8: ubfx            x4, x4, #0xc, #0x14
    // 0xbb9aac: sub             x4, x4, #0xbba
    // 0xbb9ab0: cmp             x4, #0x9a
    // 0xbb9ab4: b.ls            #0xbb9ac8
    // 0xbb9ab8: r8 = RenderBox
    //     0xbb9ab8: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0xbb9abc: r3 = Null
    //     0xbb9abc: add             x3, PP, #0x51, lsl #12  ; [pp+0x51150] Null
    //     0xbb9ac0: ldr             x3, [x3, #0x150]
    // 0xbb9ac4: r0 = RenderBox()
    //     0xbb9ac4: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0xbb9ac8: ldur            x1, [fp, #-0x10]
    // 0xbb9acc: r0 = size()
    //     0xbb9acc: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xbb9ad0: ldur            x0, [fp, #-8]
    // 0xbb9ad4: LoadField: r3 = r0->field_b
    //     0xbb9ad4: ldur            w3, [x0, #0xb]
    // 0xbb9ad8: DecompressPointer r3
    //     0xbb9ad8: add             x3, x3, HEAP, lsl #32
    // 0xbb9adc: stur            x3, [fp, #-0x18]
    // 0xbb9ae0: LoadField: r0 = r3->field_f
    //     0xbb9ae0: ldur            w0, [x3, #0xf]
    // 0xbb9ae4: DecompressPointer r0
    //     0xbb9ae4: add             x0, x0, HEAP, lsl #32
    // 0xbb9ae8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb9ae8: ldur            w1, [x0, #0x17]
    // 0xbb9aec: DecompressPointer r1
    //     0xbb9aec: add             x1, x1, HEAP, lsl #32
    // 0xbb9af0: str             x1, [SP]
    // 0xbb9af4: ldur            x1, [fp, #-0x10]
    // 0xbb9af8: r2 = Instance_Offset
    //     0xbb9af8: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xbb9afc: r4 = const [0, 0x3, 0x1, 0x2, ancestor, 0x2, null]
    //     0xbb9afc: add             x4, PP, #0x44, lsl #12  ; [pp+0x44950] List(7) [0, 0x3, 0x1, 0x2, "ancestor", 0x2, Null]
    //     0xbb9b00: ldr             x4, [x4, #0x950]
    // 0xbb9b04: r0 = localToGlobal()
    //     0xbb9b04: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0xbb9b08: ldur            x1, [fp, #-0x10]
    // 0xbb9b0c: stur            x0, [fp, #-8]
    // 0xbb9b10: r0 = size()
    //     0xbb9b10: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xbb9b14: mov             x3, x0
    // 0xbb9b18: ldur            x0, [fp, #-0x18]
    // 0xbb9b1c: stur            x3, [fp, #-0x20]
    // 0xbb9b20: LoadField: r1 = r0->field_f
    //     0xbb9b20: ldur            w1, [x0, #0xf]
    // 0xbb9b24: DecompressPointer r1
    //     0xbb9b24: add             x1, x1, HEAP, lsl #32
    // 0xbb9b28: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbb9b28: ldur            w2, [x1, #0x17]
    // 0xbb9b2c: DecompressPointer r2
    //     0xbb9b2c: add             x2, x2, HEAP, lsl #32
    // 0xbb9b30: str             x2, [SP]
    // 0xbb9b34: ldur            x1, [fp, #-0x10]
    // 0xbb9b38: r2 = Instance_Offset
    //     0xbb9b38: ldr             x2, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xbb9b3c: r4 = const [0, 0x3, 0x1, 0x2, ancestor, 0x2, null]
    //     0xbb9b3c: add             x4, PP, #0x44, lsl #12  ; [pp+0x44950] List(7) [0, 0x3, 0x1, 0x2, "ancestor", 0x2, Null]
    //     0xbb9b40: ldr             x4, [x4, #0x950]
    // 0xbb9b44: r0 = localToGlobal()
    //     0xbb9b44: bl              #0x67fb20  ; [package:flutter/src/rendering/box.dart] RenderBox::localToGlobal
    // 0xbb9b48: ldur            x1, [fp, #-0x20]
    // 0xbb9b4c: mov             x2, x0
    // 0xbb9b50: r0 = bottomRight()
    //     0xbb9b50: bl              #0x6a5e18  ; [dart:ui] Size::bottomRight
    // 0xbb9b54: ldur            x2, [fp, #-8]
    // 0xbb9b58: LoadField: d0 = r2->field_7
    //     0xbb9b58: ldur            d0, [x2, #7]
    // 0xbb9b5c: stur            d0, [fp, #-0x40]
    // 0xbb9b60: fcmp            d0, d0
    // 0xbb9b64: b.vs            #0xbb9b98
    // 0xbb9b68: LoadField: d1 = r2->field_f
    //     0xbb9b68: ldur            d1, [x2, #0xf]
    // 0xbb9b6c: stur            d1, [fp, #-0x38]
    // 0xbb9b70: fcmp            d1, d1
    // 0xbb9b74: b.vs            #0xbb9b98
    // 0xbb9b78: LoadField: d2 = r0->field_7
    //     0xbb9b78: ldur            d2, [x0, #7]
    // 0xbb9b7c: stur            d2, [fp, #-0x30]
    // 0xbb9b80: fcmp            d2, d2
    // 0xbb9b84: b.vs            #0xbb9b98
    // 0xbb9b88: LoadField: d3 = r0->field_f
    //     0xbb9b88: ldur            d3, [x0, #0xf]
    // 0xbb9b8c: stur            d3, [fp, #-0x28]
    // 0xbb9b90: fcmp            d3, d3
    // 0xbb9b94: b.vc            #0xbb9ba0
    // 0xbb9b98: r2 = Instance_Rect
    //     0xbb9b98: ldr             x2, [PP, #0x44b0]  ; [pp+0x44b0] Obj!Rect@e2bf51
    // 0xbb9b9c: b               #0xbb9bc8
    // 0xbb9ba0: r0 = Rect()
    //     0xbb9ba0: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0xbb9ba4: ldur            d0, [fp, #-0x40]
    // 0xbb9ba8: StoreField: r0->field_7 = d0
    //     0xbb9ba8: stur            d0, [x0, #7]
    // 0xbb9bac: ldur            d0, [fp, #-0x38]
    // 0xbb9bb0: StoreField: r0->field_f = d0
    //     0xbb9bb0: stur            d0, [x0, #0xf]
    // 0xbb9bb4: ldur            d0, [fp, #-0x30]
    // 0xbb9bb8: ArrayStore: r0[0] = d0  ; List_8
    //     0xbb9bb8: stur            d0, [x0, #0x17]
    // 0xbb9bbc: ldur            d0, [fp, #-0x28]
    // 0xbb9bc0: StoreField: r0->field_1f = d0
    //     0xbb9bc0: stur            d0, [x0, #0x1f]
    // 0xbb9bc4: mov             x2, x0
    // 0xbb9bc8: ldur            x0, [fp, #-0x18]
    // 0xbb9bcc: ldur            x1, [fp, #-0x10]
    // 0xbb9bd0: stur            x2, [fp, #-0x20]
    // 0xbb9bd4: r0 = size()
    //     0xbb9bd4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xbb9bd8: mov             x1, x0
    // 0xbb9bdc: ldur            x2, [fp, #-8]
    // 0xbb9be0: r0 = center()
    //     0xbb9be0: bl              #0x7dc26c  ; [dart:ui] Size::center
    // 0xbb9be4: mov             x1, x0
    // 0xbb9be8: ldur            x0, [fp, #-0x18]
    // 0xbb9bec: LoadField: r2 = r0->field_f
    //     0xbb9bec: ldur            w2, [x0, #0xf]
    // 0xbb9bf0: DecompressPointer r2
    //     0xbb9bf0: add             x2, x2, HEAP, lsl #32
    // 0xbb9bf4: LoadField: r0 = r2->field_f
    //     0xbb9bf4: ldur            w0, [x2, #0xf]
    // 0xbb9bf8: DecompressPointer r0
    //     0xbb9bf8: add             x0, x0, HEAP, lsl #32
    // 0xbb9bfc: cmp             w0, NULL
    // 0xbb9c00: b.eq            #0xbb9c34
    // 0xbb9c04: ldr             x16, [fp, #0x10]
    // 0xbb9c08: stp             x16, x0, [SP, #0x10]
    // 0xbb9c0c: ldur            x16, [fp, #-0x20]
    // 0xbb9c10: stp             x1, x16, [SP]
    // 0xbb9c14: ClosureCall
    //     0xbb9c14: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbb9c18: ldur            x2, [x0, #0x1f]
    //     0xbb9c1c: blr             x2
    // 0xbb9c20: LeaveFrame
    //     0xbb9c20: mov             SP, fp
    //     0xbb9c24: ldp             fp, lr, [SP], #0x10
    // 0xbb9c28: ret
    //     0xbb9c28: ret             
    // 0xbb9c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb9c2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb9c30: b               #0xbb9a88
    // 0xbb9c34: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbb9c34: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
}
