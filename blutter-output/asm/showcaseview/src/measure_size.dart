// lib: , url: package:showcaseview/src/measure_size.dart

// class id: 1051124, size: 0x8
class :: {
}

// class id: 4087, size: 0x1c, field offset: 0x14
class _MeasureSizeState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xa4c4bc, size: 0x174
    // 0xa4c4bc: EnterFrame
    //     0xa4c4bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c4c0: mov             fp, SP
    // 0xa4c4c4: AllocStack(0x30)
    //     0xa4c4c4: sub             SP, SP, #0x30
    // 0xa4c4c8: SetupParameters(_MeasureSizeState this /* r1 => r0, fp-0x18 */)
    //     0xa4c4c8: mov             x0, x1
    //     0xa4c4cc: stur            x1, [fp, #-0x18]
    // 0xa4c4d0: CheckStackOverflow
    //     0xa4c4d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c4d4: cmp             SP, x16
    //     0xa4c4d8: b.ls            #0xa4c620
    // 0xa4c4dc: r1 = LoadStaticField(0x958)
    //     0xa4c4dc: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xa4c4e0: ldr             x1, [x1, #0x12b0]
    // 0xa4c4e4: cmp             w1, NULL
    // 0xa4c4e8: b.eq            #0xa4c628
    // 0xa4c4ec: LoadField: r3 = r1->field_53
    //     0xa4c4ec: ldur            w3, [x1, #0x53]
    // 0xa4c4f0: DecompressPointer r3
    //     0xa4c4f0: add             x3, x3, HEAP, lsl #32
    // 0xa4c4f4: stur            x3, [fp, #-0x10]
    // 0xa4c4f8: LoadField: r4 = r3->field_7
    //     0xa4c4f8: ldur            w4, [x3, #7]
    // 0xa4c4fc: DecompressPointer r4
    //     0xa4c4fc: add             x4, x4, HEAP, lsl #32
    // 0xa4c500: mov             x2, x0
    // 0xa4c504: stur            x4, [fp, #-8]
    // 0xa4c508: r1 = Function 'postFrameCallback':.
    //     0xa4c508: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d320] AnonymousClosure: (0xa4c654), in [package:showcaseview/src/measure_size.dart] _MeasureSizeState::postFrameCallback (0xa4c690)
    //     0xa4c50c: ldr             x1, [x1, #0x320]
    // 0xa4c510: r0 = AllocateClosure()
    //     0xa4c510: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4c514: ldur            x2, [fp, #-8]
    // 0xa4c518: mov             x3, x0
    // 0xa4c51c: r1 = Null
    //     0xa4c51c: mov             x1, NULL
    // 0xa4c520: stur            x3, [fp, #-8]
    // 0xa4c524: cmp             w2, NULL
    // 0xa4c528: b.eq            #0xa4c548
    // 0xa4c52c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa4c52c: ldur            w4, [x2, #0x17]
    // 0xa4c530: DecompressPointer r4
    //     0xa4c530: add             x4, x4, HEAP, lsl #32
    // 0xa4c534: r8 = X0
    //     0xa4c534: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa4c538: LoadField: r9 = r4->field_7
    //     0xa4c538: ldur            x9, [x4, #7]
    // 0xa4c53c: r3 = Null
    //     0xa4c53c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d328] Null
    //     0xa4c540: ldr             x3, [x3, #0x328]
    // 0xa4c544: blr             x9
    // 0xa4c548: ldur            x0, [fp, #-0x10]
    // 0xa4c54c: LoadField: r1 = r0->field_b
    //     0xa4c54c: ldur            w1, [x0, #0xb]
    // 0xa4c550: LoadField: r2 = r0->field_f
    //     0xa4c550: ldur            w2, [x0, #0xf]
    // 0xa4c554: DecompressPointer r2
    //     0xa4c554: add             x2, x2, HEAP, lsl #32
    // 0xa4c558: LoadField: r3 = r2->field_b
    //     0xa4c558: ldur            w3, [x2, #0xb]
    // 0xa4c55c: r2 = LoadInt32Instr(r1)
    //     0xa4c55c: sbfx            x2, x1, #1, #0x1f
    // 0xa4c560: stur            x2, [fp, #-0x20]
    // 0xa4c564: r1 = LoadInt32Instr(r3)
    //     0xa4c564: sbfx            x1, x3, #1, #0x1f
    // 0xa4c568: cmp             x2, x1
    // 0xa4c56c: b.ne            #0xa4c578
    // 0xa4c570: mov             x1, x0
    // 0xa4c574: r0 = _growToNextCapacity()
    //     0xa4c574: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4c578: ldur            x3, [fp, #-0x18]
    // 0xa4c57c: ldur            x0, [fp, #-0x10]
    // 0xa4c580: ldur            x2, [fp, #-0x20]
    // 0xa4c584: add             x1, x2, #1
    // 0xa4c588: lsl             x4, x1, #1
    // 0xa4c58c: StoreField: r0->field_b = r4
    //     0xa4c58c: stur            w4, [x0, #0xb]
    // 0xa4c590: LoadField: r1 = r0->field_f
    //     0xa4c590: ldur            w1, [x0, #0xf]
    // 0xa4c594: DecompressPointer r1
    //     0xa4c594: add             x1, x1, HEAP, lsl #32
    // 0xa4c598: ldur            x0, [fp, #-8]
    // 0xa4c59c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa4c59c: add             x25, x1, x2, lsl #2
    //     0xa4c5a0: add             x25, x25, #0xf
    //     0xa4c5a4: str             w0, [x25]
    //     0xa4c5a8: tbz             w0, #0, #0xa4c5c4
    //     0xa4c5ac: ldurb           w16, [x1, #-1]
    //     0xa4c5b0: ldurb           w17, [x0, #-1]
    //     0xa4c5b4: and             x16, x17, x16, lsr #2
    //     0xa4c5b8: tst             x16, HEAP, lsr #32
    //     0xa4c5bc: b.eq            #0xa4c5c4
    //     0xa4c5c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4c5c4: LoadField: r0 = r3->field_13
    //     0xa4c5c4: ldur            w0, [x3, #0x13]
    // 0xa4c5c8: DecompressPointer r0
    //     0xa4c5c8: add             x0, x0, HEAP, lsl #32
    // 0xa4c5cc: stur            x0, [fp, #-0x10]
    // 0xa4c5d0: LoadField: r1 = r3->field_b
    //     0xa4c5d0: ldur            w1, [x3, #0xb]
    // 0xa4c5d4: DecompressPointer r1
    //     0xa4c5d4: add             x1, x1, HEAP, lsl #32
    // 0xa4c5d8: cmp             w1, NULL
    // 0xa4c5dc: b.eq            #0xa4c62c
    // 0xa4c5e0: LoadField: r2 = r1->field_b
    //     0xa4c5e0: ldur            w2, [x1, #0xb]
    // 0xa4c5e4: DecompressPointer r2
    //     0xa4c5e4: add             x2, x2, HEAP, lsl #32
    // 0xa4c5e8: stur            x2, [fp, #-8]
    // 0xa4c5ec: r0 = Container()
    //     0xa4c5ec: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4c5f0: stur            x0, [fp, #-0x18]
    // 0xa4c5f4: ldur            x16, [fp, #-0x10]
    // 0xa4c5f8: ldur            lr, [fp, #-8]
    // 0xa4c5fc: stp             lr, x16, [SP]
    // 0xa4c600: mov             x1, x0
    // 0xa4c604: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, key, 0x1, null]
    //     0xa4c604: add             x4, PP, #0x5d, lsl #12  ; [pp+0x5d338] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "key", 0x1, Null]
    //     0xa4c608: ldr             x4, [x4, #0x338]
    // 0xa4c60c: r0 = Container()
    //     0xa4c60c: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4c610: ldur            x0, [fp, #-0x18]
    // 0xa4c614: LeaveFrame
    //     0xa4c614: mov             SP, fp
    //     0xa4c618: ldp             fp, lr, [SP], #0x10
    // 0xa4c61c: ret
    //     0xa4c61c: ret             
    // 0xa4c620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c620: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c624: b               #0xa4c4dc
    // 0xa4c628: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4c628: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4c62c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4c62c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void postFrameCallback(dynamic, Duration) {
    // ** addr: 0xa4c654, size: 0x3c
    // 0xa4c654: EnterFrame
    //     0xa4c654: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c658: mov             fp, SP
    // 0xa4c65c: ldr             x0, [fp, #0x18]
    // 0xa4c660: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4c660: ldur            w1, [x0, #0x17]
    // 0xa4c664: DecompressPointer r1
    //     0xa4c664: add             x1, x1, HEAP, lsl #32
    // 0xa4c668: CheckStackOverflow
    //     0xa4c668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c66c: cmp             SP, x16
    //     0xa4c670: b.ls            #0xa4c688
    // 0xa4c674: ldr             x2, [fp, #0x10]
    // 0xa4c678: r0 = postFrameCallback()
    //     0xa4c678: bl              #0xa4c690  ; [package:showcaseview/src/measure_size.dart] _MeasureSizeState::postFrameCallback
    // 0xa4c67c: LeaveFrame
    //     0xa4c67c: mov             SP, fp
    //     0xa4c680: ldp             fp, lr, [SP], #0x10
    // 0xa4c684: ret
    //     0xa4c684: ret             
    // 0xa4c688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c68c: b               #0xa4c674
  }
  _ postFrameCallback(/* No info */) {
    // ** addr: 0xa4c690, size: 0x120
    // 0xa4c690: EnterFrame
    //     0xa4c690: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c694: mov             fp, SP
    // 0xa4c698: AllocStack(0x20)
    //     0xa4c698: sub             SP, SP, #0x20
    // 0xa4c69c: SetupParameters(_MeasureSizeState this /* r1 => r0, fp-0x8 */)
    //     0xa4c69c: mov             x0, x1
    //     0xa4c6a0: stur            x1, [fp, #-8]
    // 0xa4c6a4: CheckStackOverflow
    //     0xa4c6a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c6a8: cmp             SP, x16
    //     0xa4c6ac: b.ls            #0xa4c7a4
    // 0xa4c6b0: LoadField: r1 = r0->field_13
    //     0xa4c6b0: ldur            w1, [x0, #0x13]
    // 0xa4c6b4: DecompressPointer r1
    //     0xa4c6b4: add             x1, x1, HEAP, lsl #32
    // 0xa4c6b8: r0 = _currentElement()
    //     0xa4c6b8: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0xa4c6bc: cmp             w0, NULL
    // 0xa4c6c0: b.ne            #0xa4c6d4
    // 0xa4c6c4: r0 = Null
    //     0xa4c6c4: mov             x0, NULL
    // 0xa4c6c8: LeaveFrame
    //     0xa4c6c8: mov             SP, fp
    //     0xa4c6cc: ldp             fp, lr, [SP], #0x10
    // 0xa4c6d0: ret
    //     0xa4c6d0: ret             
    // 0xa4c6d4: mov             x1, x0
    // 0xa4c6d8: r0 = findRenderObject()
    //     0xa4c6d8: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0xa4c6dc: r1 = LoadClassIdInstr(r0)
    //     0xa4c6dc: ldur            x1, [x0, #-1]
    //     0xa4c6e0: ubfx            x1, x1, #0xc, #0x14
    // 0xa4c6e4: sub             x16, x1, #0xbba
    // 0xa4c6e8: cmp             x16, #0x9a
    // 0xa4c6ec: b.hi            #0xa4c700
    // 0xa4c6f0: mov             x1, x0
    // 0xa4c6f4: r0 = size()
    //     0xa4c6f4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xa4c6f8: mov             x2, x0
    // 0xa4c6fc: b               #0xa4c704
    // 0xa4c700: r2 = Null
    //     0xa4c700: mov             x2, NULL
    // 0xa4c704: ldur            x1, [fp, #-8]
    // 0xa4c708: stur            x2, [fp, #-0x10]
    // 0xa4c70c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa4c70c: ldur            w0, [x1, #0x17]
    // 0xa4c710: DecompressPointer r0
    //     0xa4c710: add             x0, x0, HEAP, lsl #32
    // 0xa4c714: r3 = LoadClassIdInstr(r0)
    //     0xa4c714: ldur            x3, [x0, #-1]
    //     0xa4c718: ubfx            x3, x3, #0xc, #0x14
    // 0xa4c71c: stp             x2, x0, [SP]
    // 0xa4c720: mov             x0, x3
    // 0xa4c724: mov             lr, x0
    // 0xa4c728: ldr             lr, [x21, lr, lsl #3]
    // 0xa4c72c: blr             lr
    // 0xa4c730: tbnz            w0, #4, #0xa4c744
    // 0xa4c734: r0 = Null
    //     0xa4c734: mov             x0, NULL
    // 0xa4c738: LeaveFrame
    //     0xa4c738: mov             SP, fp
    //     0xa4c73c: ldp             fp, lr, [SP], #0x10
    // 0xa4c740: ret
    //     0xa4c740: ret             
    // 0xa4c744: ldur            x1, [fp, #-8]
    // 0xa4c748: ldur            x0, [fp, #-0x10]
    // 0xa4c74c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4c74c: stur            w0, [x1, #0x17]
    //     0xa4c750: ldurb           w16, [x1, #-1]
    //     0xa4c754: ldurb           w17, [x0, #-1]
    //     0xa4c758: and             x16, x17, x16, lsr #2
    //     0xa4c75c: tst             x16, HEAP, lsr #32
    //     0xa4c760: b.eq            #0xa4c768
    //     0xa4c764: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa4c768: LoadField: r0 = r1->field_b
    //     0xa4c768: ldur            w0, [x1, #0xb]
    // 0xa4c76c: DecompressPointer r0
    //     0xa4c76c: add             x0, x0, HEAP, lsl #32
    // 0xa4c770: cmp             w0, NULL
    // 0xa4c774: b.eq            #0xa4c7ac
    // 0xa4c778: LoadField: r1 = r0->field_f
    //     0xa4c778: ldur            w1, [x0, #0xf]
    // 0xa4c77c: DecompressPointer r1
    //     0xa4c77c: add             x1, x1, HEAP, lsl #32
    // 0xa4c780: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa4c780: ldur            w0, [x1, #0x17]
    // 0xa4c784: DecompressPointer r0
    //     0xa4c784: add             x0, x0, HEAP, lsl #32
    // 0xa4c788: mov             x1, x0
    // 0xa4c78c: ldur            x2, [fp, #-0x10]
    // 0xa4c790: r0 = onSizeChange()
    //     0xa4c790: bl              #0xa4c7ec  ; [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::onSizeChange
    // 0xa4c794: r0 = Null
    //     0xa4c794: mov             x0, NULL
    // 0xa4c798: LeaveFrame
    //     0xa4c798: mov             SP, fp
    //     0xa4c79c: ldp             fp, lr, [SP], #0x10
    // 0xa4c7a0: ret
    //     0xa4c7a0: ret             
    // 0xa4c7a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c7a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c7a8: b               #0xa4c6b0
    // 0xa4c7ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4c7ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4692, size: 0x14, field offset: 0xc
//   const constructor, 
class MeasureSize extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa952d0, size: 0x40
    // 0xa952d0: EnterFrame
    //     0xa952d0: stp             fp, lr, [SP, #-0x10]!
    //     0xa952d4: mov             fp, SP
    // 0xa952d8: AllocStack(0x8)
    //     0xa952d8: sub             SP, SP, #8
    // 0xa952dc: SetupParameters(MeasureSize this /* r1 => r0 */)
    //     0xa952dc: mov             x0, x1
    // 0xa952e0: r1 = <MeasureSize>
    //     0xa952e0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ae70] TypeArguments: <MeasureSize>
    //     0xa952e4: ldr             x1, [x1, #0xe70]
    // 0xa952e8: r0 = _MeasureSizeState()
    //     0xa952e8: bl              #0xa95310  ; Allocate_MeasureSizeStateStub -> _MeasureSizeState (size=0x1c)
    // 0xa952ec: r1 = <State<StatefulWidget>>
    //     0xa952ec: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa952f0: stur            x0, [fp, #-8]
    // 0xa952f4: r0 = LabeledGlobalKey()
    //     0xa952f4: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xa952f8: mov             x1, x0
    // 0xa952fc: ldur            x0, [fp, #-8]
    // 0xa95300: StoreField: r0->field_13 = r1
    //     0xa95300: stur            w1, [x0, #0x13]
    // 0xa95304: LeaveFrame
    //     0xa95304: mov             SP, fp
    //     0xa95308: ldp             fp, lr, [SP], #0x10
    // 0xa9530c: ret
    //     0xa9530c: ret             
  }
}
