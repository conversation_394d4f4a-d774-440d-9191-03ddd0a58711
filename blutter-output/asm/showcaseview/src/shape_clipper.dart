// lib: , url: package:showcaseview/src/shape_clipper.dart

// class id: 1051125, size: 0x8
class :: {
}

// class id: 5443, size: 0x20, field offset: 0x10
class RRectClipper extends CustomClipper<dynamic> {

  _ getClip(/* No info */) {
    // ** addr: 0xcfe7b4, size: 0x320
    // 0xcfe7b4: EnterFrame
    //     0xcfe7b4: stp             fp, lr, [SP, #-0x10]!
    //     0xcfe7b8: mov             fp, SP
    // 0xcfe7bc: AllocStack(0x50)
    //     0xcfe7bc: sub             SP, SP, #0x50
    // 0xcfe7c0: SetupParameters(RRectClipper this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xcfe7c0: stur            x1, [fp, #-8]
    //     0xcfe7c4: stur            x2, [fp, #-0x10]
    // 0xcfe7c8: CheckStackOverflow
    //     0xcfe7c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcfe7cc: cmp             SP, x16
    //     0xcfe7d0: b.ls            #0xcfeac0
    // 0xcfe7d4: LoadField: r0 = r1->field_f
    //     0xcfe7d4: ldur            w0, [x1, #0xf]
    // 0xcfe7d8: DecompressPointer r0
    //     0xcfe7d8: add             x0, x0, HEAP, lsl #32
    // 0xcfe7dc: tbnz            w0, #4, #0xcfe810
    // 0xcfe7e0: LoadField: r0 = r1->field_1b
    //     0xcfe7e0: ldur            w0, [x1, #0x1b]
    // 0xcfe7e4: DecompressPointer r0
    //     0xcfe7e4: add             x0, x0, HEAP, lsl #32
    // 0xcfe7e8: LoadField: d0 = r0->field_1f
    //     0xcfe7e8: ldur            d0, [x0, #0x1f]
    // 0xcfe7ec: LoadField: d1 = r0->field_f
    //     0xcfe7ec: ldur            d1, [x0, #0xf]
    // 0xcfe7f0: fsub            d2, d0, d1
    // 0xcfe7f4: stur            d2, [fp, #-0x30]
    // 0xcfe7f8: r0 = Radius()
    //     0xcfe7f8: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xcfe7fc: ldur            d0, [fp, #-0x30]
    // 0xcfe800: StoreField: r0->field_7 = d0
    //     0xcfe800: stur            d0, [x0, #7]
    // 0xcfe804: StoreField: r0->field_f = d0
    //     0xcfe804: stur            d0, [x0, #0xf]
    // 0xcfe808: mov             x7, x0
    // 0xcfe80c: b               #0xcfe818
    // 0xcfe810: r7 = Instance_Radius
    //     0xcfe810: add             x7, PP, #0x51, lsl #12  ; [pp+0x51138] Obj!Radius@e2bf11
    //     0xcfe814: ldr             x7, [x7, #0x138]
    // 0xcfe818: ldur            x0, [fp, #-8]
    // 0xcfe81c: r1 = Instance_EdgeInsets
    //     0xcfe81c: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xcfe820: stur            x7, [fp, #-0x18]
    // 0xcfe824: LoadField: r2 = r0->field_1b
    //     0xcfe824: ldur            w2, [x0, #0x1b]
    // 0xcfe828: DecompressPointer r2
    //     0xcfe828: add             x2, x2, HEAP, lsl #32
    // 0xcfe82c: LoadField: d0 = r2->field_7
    //     0xcfe82c: ldur            d0, [x2, #7]
    // 0xcfe830: LoadField: d1 = r1->field_7
    //     0xcfe830: ldur            d1, [x1, #7]
    // 0xcfe834: fsub            d2, d0, d1
    // 0xcfe838: stur            d2, [fp, #-0x48]
    // 0xcfe83c: LoadField: d0 = r2->field_f
    //     0xcfe83c: ldur            d0, [x2, #0xf]
    // 0xcfe840: LoadField: d1 = r1->field_f
    //     0xcfe840: ldur            d1, [x1, #0xf]
    // 0xcfe844: fsub            d3, d0, d1
    // 0xcfe848: stur            d3, [fp, #-0x40]
    // 0xcfe84c: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xcfe84c: ldur            d0, [x2, #0x17]
    // 0xcfe850: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xcfe850: ldur            d1, [x1, #0x17]
    // 0xcfe854: fadd            d4, d0, d1
    // 0xcfe858: stur            d4, [fp, #-0x38]
    // 0xcfe85c: LoadField: d0 = r2->field_1f
    //     0xcfe85c: ldur            d0, [x2, #0x1f]
    // 0xcfe860: LoadField: d1 = r1->field_1f
    //     0xcfe860: ldur            d1, [x1, #0x1f]
    // 0xcfe864: fadd            d5, d0, d1
    // 0xcfe868: stur            d5, [fp, #-0x30]
    // 0xcfe86c: r0 = Rect()
    //     0xcfe86c: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0xcfe870: ldur            d0, [fp, #-0x48]
    // 0xcfe874: stur            x0, [fp, #-8]
    // 0xcfe878: StoreField: r0->field_7 = d0
    //     0xcfe878: stur            d0, [x0, #7]
    // 0xcfe87c: ldur            d0, [fp, #-0x40]
    // 0xcfe880: StoreField: r0->field_f = d0
    //     0xcfe880: stur            d0, [x0, #0xf]
    // 0xcfe884: ldur            d0, [fp, #-0x38]
    // 0xcfe888: ArrayStore: r0[0] = d0  ; List_8
    //     0xcfe888: stur            d0, [x0, #0x17]
    // 0xcfe88c: ldur            d0, [fp, #-0x30]
    // 0xcfe890: StoreField: r0->field_1f = d0
    //     0xcfe890: stur            d0, [x0, #0x1f]
    // 0xcfe894: r0 = _NativePath()
    //     0xcfe894: bl              #0x78e3a4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0xcfe898: mov             x1, x0
    // 0xcfe89c: stur            x0, [fp, #-0x20]
    // 0xcfe8a0: r0 = __constructor$Method$FfiNative()
    //     0xcfe8a0: bl              #0x78eb3c  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0xcfe8a4: ldur            x0, [fp, #-0x20]
    // 0xcfe8a8: LoadField: r1 = r0->field_7
    //     0xcfe8a8: ldur            w1, [x0, #7]
    // 0xcfe8ac: DecompressPointer r1
    //     0xcfe8ac: add             x1, x1, HEAP, lsl #32
    // 0xcfe8b0: cmp             w1, NULL
    // 0xcfe8b4: b.eq            #0xcfeac8
    // 0xcfe8b8: LoadField: r2 = r1->field_7
    //     0xcfe8b8: ldur            x2, [x1, #7]
    // 0xcfe8bc: ldr             x1, [x2]
    // 0xcfe8c0: stur            x1, [fp, #-0x28]
    // 0xcfe8c4: cbnz            x1, #0xcfe8d4
    // 0xcfe8c8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xcfe8c8: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xcfe8cc: str             x16, [SP]
    // 0xcfe8d0: r0 = _throwNew()
    //     0xcfe8d0: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xcfe8d4: ldur            x0, [fp, #-0x20]
    // 0xcfe8d8: ldur            x2, [fp, #-0x28]
    // 0xcfe8dc: stur            x2, [fp, #-0x28]
    // 0xcfe8e0: r1 = <Never>
    //     0xcfe8e0: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xcfe8e4: r0 = Pointer()
    //     0xcfe8e4: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xcfe8e8: mov             x1, x0
    // 0xcfe8ec: ldur            x0, [fp, #-0x28]
    // 0xcfe8f0: StoreField: r1->field_7 = r0
    //     0xcfe8f0: stur            x0, [x1, #7]
    // 0xcfe8f4: r2 = 1
    //     0xcfe8f4: movz            x2, #0x1
    // 0xcfe8f8: r0 = __setFillType$Method$FfiNative()
    //     0xcfe8f8: bl              #0xacc2a4  ; [dart:ui] _NativePath::__setFillType$Method$FfiNative
    // 0xcfe8fc: ldur            x2, [fp, #-0x10]
    // 0xcfe900: r1 = Instance_Offset
    //     0xcfe900: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xcfe904: r0 = &()
    //     0xcfe904: bl              #0x68491c  ; [dart:ui] Offset::&
    // 0xcfe908: LoadField: d0 = r0->field_7
    //     0xcfe908: ldur            d0, [x0, #7]
    // 0xcfe90c: stur            d0, [fp, #-0x48]
    // 0xcfe910: LoadField: d1 = r0->field_f
    //     0xcfe910: ldur            d1, [x0, #0xf]
    // 0xcfe914: stur            d1, [fp, #-0x40]
    // 0xcfe918: ArrayLoad: d2 = r0[0]  ; List_8
    //     0xcfe918: ldur            d2, [x0, #0x17]
    // 0xcfe91c: stur            d2, [fp, #-0x38]
    // 0xcfe920: LoadField: d3 = r0->field_1f
    //     0xcfe920: ldur            d3, [x0, #0x1f]
    // 0xcfe924: ldur            x0, [fp, #-0x20]
    // 0xcfe928: stur            d3, [fp, #-0x30]
    // 0xcfe92c: LoadField: r1 = r0->field_7
    //     0xcfe92c: ldur            w1, [x0, #7]
    // 0xcfe930: DecompressPointer r1
    //     0xcfe930: add             x1, x1, HEAP, lsl #32
    // 0xcfe934: cmp             w1, NULL
    // 0xcfe938: b.eq            #0xcfeacc
    // 0xcfe93c: LoadField: r2 = r1->field_7
    //     0xcfe93c: ldur            x2, [x1, #7]
    // 0xcfe940: ldr             x1, [x2]
    // 0xcfe944: stur            x1, [fp, #-0x28]
    // 0xcfe948: cbnz            x1, #0xcfe958
    // 0xcfe94c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xcfe94c: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xcfe950: str             x16, [SP]
    // 0xcfe954: r0 = _throwNew()
    //     0xcfe954: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xcfe958: ldur            x0, [fp, #-0x20]
    // 0xcfe95c: ldur            x2, [fp, #-0x28]
    // 0xcfe960: stur            x2, [fp, #-0x28]
    // 0xcfe964: r1 = <Never>
    //     0xcfe964: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xcfe968: r0 = Pointer()
    //     0xcfe968: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xcfe96c: mov             x1, x0
    // 0xcfe970: ldur            x0, [fp, #-0x28]
    // 0xcfe974: StoreField: r1->field_7 = r0
    //     0xcfe974: stur            x0, [x1, #7]
    // 0xcfe978: ldur            d0, [fp, #-0x48]
    // 0xcfe97c: ldur            d1, [fp, #-0x40]
    // 0xcfe980: ldur            d2, [fp, #-0x38]
    // 0xcfe984: ldur            d3, [fp, #-0x30]
    // 0xcfe988: r0 = __addRect$Method$FfiNative()
    //     0xcfe988: bl              #0x7cf630  ; [dart:ui] _NativePath::__addRect$Method$FfiNative
    // 0xcfe98c: r0 = RRect()
    //     0xcfe98c: bl              #0x789ce8  ; AllocateRRectStub -> RRect (size=0x68)
    // 0xcfe990: mov             x1, x0
    // 0xcfe994: ldur            x2, [fp, #-8]
    // 0xcfe998: ldur            x3, [fp, #-0x18]
    // 0xcfe99c: ldur            x5, [fp, #-0x18]
    // 0xcfe9a0: ldur            x6, [fp, #-0x18]
    // 0xcfe9a4: ldur            x7, [fp, #-0x18]
    // 0xcfe9a8: stur            x0, [fp, #-8]
    // 0xcfe9ac: r0 = RRect.fromRectAndCorners()
    //     0xcfe9ac: bl              #0x79d4ac  ; [dart:ui] RRect::RRect.fromRectAndCorners
    // 0xcfe9b0: ldur            x0, [fp, #-8]
    // 0xcfe9b4: LoadField: d0 = r0->field_7
    //     0xcfe9b4: ldur            d0, [x0, #7]
    // 0xcfe9b8: fcvt            s1, d0
    // 0xcfe9bc: stur            d1, [fp, #-0x30]
    // 0xcfe9c0: r4 = 24
    //     0xcfe9c0: movz            x4, #0x18
    // 0xcfe9c4: r0 = AllocateFloat32Array()
    //     0xcfe9c4: bl              #0xec19f8  ; AllocateFloat32ArrayStub
    // 0xcfe9c8: ldur            d0, [fp, #-0x30]
    // 0xcfe9cc: stur            x0, [fp, #-0x10]
    // 0xcfe9d0: ArrayStore: r0[0] = d0  ; List_8
    //     0xcfe9d0: stur            s0, [x0, #0x17]
    // 0xcfe9d4: ldur            x1, [fp, #-8]
    // 0xcfe9d8: LoadField: d0 = r1->field_f
    //     0xcfe9d8: ldur            d0, [x1, #0xf]
    // 0xcfe9dc: fcvt            s1, d0
    // 0xcfe9e0: StoreField: r0->field_1b = d1
    //     0xcfe9e0: stur            s1, [x0, #0x1b]
    // 0xcfe9e4: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xcfe9e4: ldur            d0, [x1, #0x17]
    // 0xcfe9e8: fcvt            s1, d0
    // 0xcfe9ec: StoreField: r0->field_1f = d1
    //     0xcfe9ec: stur            s1, [x0, #0x1f]
    // 0xcfe9f0: LoadField: d0 = r1->field_1f
    //     0xcfe9f0: ldur            d0, [x1, #0x1f]
    // 0xcfe9f4: fcvt            s1, d0
    // 0xcfe9f8: StoreField: r0->field_23 = d1
    //     0xcfe9f8: stur            s1, [x0, #0x23]
    // 0xcfe9fc: LoadField: d0 = r1->field_27
    //     0xcfe9fc: ldur            d0, [x1, #0x27]
    // 0xcfea00: fcvt            s1, d0
    // 0xcfea04: StoreField: r0->field_27 = d1
    //     0xcfea04: stur            s1, [x0, #0x27]
    // 0xcfea08: LoadField: d0 = r1->field_2f
    //     0xcfea08: ldur            d0, [x1, #0x2f]
    // 0xcfea0c: fcvt            s1, d0
    // 0xcfea10: StoreField: r0->field_2b = d1
    //     0xcfea10: stur            s1, [x0, #0x2b]
    // 0xcfea14: LoadField: d0 = r1->field_37
    //     0xcfea14: ldur            d0, [x1, #0x37]
    // 0xcfea18: fcvt            s1, d0
    // 0xcfea1c: StoreField: r0->field_2f = d1
    //     0xcfea1c: stur            s1, [x0, #0x2f]
    // 0xcfea20: LoadField: d0 = r1->field_3f
    //     0xcfea20: ldur            d0, [x1, #0x3f]
    // 0xcfea24: fcvt            s1, d0
    // 0xcfea28: StoreField: r0->field_33 = d1
    //     0xcfea28: stur            s1, [x0, #0x33]
    // 0xcfea2c: LoadField: d0 = r1->field_47
    //     0xcfea2c: ldur            d0, [x1, #0x47]
    // 0xcfea30: fcvt            s1, d0
    // 0xcfea34: StoreField: r0->field_37 = d1
    //     0xcfea34: stur            s1, [x0, #0x37]
    // 0xcfea38: LoadField: d0 = r1->field_4f
    //     0xcfea38: ldur            d0, [x1, #0x4f]
    // 0xcfea3c: fcvt            s1, d0
    // 0xcfea40: StoreField: r0->field_3b = d1
    //     0xcfea40: stur            s1, [x0, #0x3b]
    // 0xcfea44: LoadField: d0 = r1->field_57
    //     0xcfea44: ldur            d0, [x1, #0x57]
    // 0xcfea48: fcvt            s1, d0
    // 0xcfea4c: StoreField: r0->field_3f = d1
    //     0xcfea4c: stur            s1, [x0, #0x3f]
    // 0xcfea50: LoadField: d0 = r1->field_5f
    //     0xcfea50: ldur            d0, [x1, #0x5f]
    // 0xcfea54: fcvt            s1, d0
    // 0xcfea58: StoreField: r0->field_43 = d1
    //     0xcfea58: stur            s1, [x0, #0x43]
    // 0xcfea5c: ldur            x1, [fp, #-0x20]
    // 0xcfea60: LoadField: r2 = r1->field_7
    //     0xcfea60: ldur            w2, [x1, #7]
    // 0xcfea64: DecompressPointer r2
    //     0xcfea64: add             x2, x2, HEAP, lsl #32
    // 0xcfea68: cmp             w2, NULL
    // 0xcfea6c: b.eq            #0xcfead0
    // 0xcfea70: LoadField: r3 = r2->field_7
    //     0xcfea70: ldur            x3, [x2, #7]
    // 0xcfea74: ldr             x2, [x3]
    // 0xcfea78: stur            x2, [fp, #-0x28]
    // 0xcfea7c: cbnz            x2, #0xcfea8c
    // 0xcfea80: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0xcfea80: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0xcfea84: str             x16, [SP]
    // 0xcfea88: r0 = _throwNew()
    //     0xcfea88: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0xcfea8c: ldur            x0, [fp, #-0x28]
    // 0xcfea90: stur            x0, [fp, #-0x28]
    // 0xcfea94: r1 = <Never>
    //     0xcfea94: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xcfea98: r0 = Pointer()
    //     0xcfea98: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0xcfea9c: mov             x1, x0
    // 0xcfeaa0: ldur            x0, [fp, #-0x28]
    // 0xcfeaa4: StoreField: r1->field_7 = r0
    //     0xcfeaa4: stur            x0, [x1, #7]
    // 0xcfeaa8: ldur            x2, [fp, #-0x10]
    // 0xcfeaac: r0 = __addRRect$Method$FfiNative()
    //     0xcfeaac: bl              #0x78e988  ; [dart:ui] _NativePath::__addRRect$Method$FfiNative
    // 0xcfeab0: ldur            x0, [fp, #-0x20]
    // 0xcfeab4: LeaveFrame
    //     0xcfeab4: mov             SP, fp
    //     0xcfeab8: ldp             fp, lr, [SP], #0x10
    // 0xcfeabc: ret
    //     0xcfeabc: ret             
    // 0xcfeac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcfeac0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcfeac4: b               #0xcfe7d4
    // 0xcfeac8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xcfeac8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xcfeacc: r0 = NullErrorSharedWithFPURegs()
    //     0xcfeacc: bl              #0xec2ba8  ; NullErrorSharedWithFPURegsStub
    // 0xcfead0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xcfead0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ shouldReclip(/* No info */) {
    // ** addr: 0xd04244, size: 0xdc
    // 0xd04244: EnterFrame
    //     0xd04244: stp             fp, lr, [SP, #-0x10]!
    //     0xd04248: mov             fp, SP
    // 0xd0424c: AllocStack(0x20)
    //     0xd0424c: sub             SP, SP, #0x20
    // 0xd04250: SetupParameters(RRectClipper this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xd04250: mov             x4, x1
    //     0xd04254: mov             x3, x2
    //     0xd04258: stur            x1, [fp, #-8]
    //     0xd0425c: stur            x2, [fp, #-0x10]
    // 0xd04260: CheckStackOverflow
    //     0xd04260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd04264: cmp             SP, x16
    //     0xd04268: b.ls            #0xd04318
    // 0xd0426c: mov             x0, x3
    // 0xd04270: r2 = Null
    //     0xd04270: mov             x2, NULL
    // 0xd04274: r1 = Null
    //     0xd04274: mov             x1, NULL
    // 0xd04278: r4 = 60
    //     0xd04278: movz            x4, #0x3c
    // 0xd0427c: branchIfSmi(r0, 0xd04288)
    //     0xd0427c: tbz             w0, #0, #0xd04288
    // 0xd04280: r4 = LoadClassIdInstr(r0)
    //     0xd04280: ldur            x4, [x0, #-1]
    //     0xd04284: ubfx            x4, x4, #0xc, #0x14
    // 0xd04288: r17 = 5443
    //     0xd04288: movz            x17, #0x1543
    // 0xd0428c: cmp             x4, x17
    // 0xd04290: b.eq            #0xd042a8
    // 0xd04294: r8 = RRectClipper
    //     0xd04294: add             x8, PP, #0x51, lsl #12  ; [pp+0x51120] Type: RRectClipper
    //     0xd04298: ldr             x8, [x8, #0x120]
    // 0xd0429c: r3 = Null
    //     0xd0429c: add             x3, PP, #0x51, lsl #12  ; [pp+0x51128] Null
    //     0xd042a0: ldr             x3, [x3, #0x128]
    // 0xd042a4: r0 = DefaultTypeTest()
    //     0xd042a4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xd042a8: ldur            x0, [fp, #-8]
    // 0xd042ac: LoadField: r1 = r0->field_f
    //     0xd042ac: ldur            w1, [x0, #0xf]
    // 0xd042b0: DecompressPointer r1
    //     0xd042b0: add             x1, x1, HEAP, lsl #32
    // 0xd042b4: ldur            x2, [fp, #-0x10]
    // 0xd042b8: LoadField: r3 = r2->field_f
    //     0xd042b8: ldur            w3, [x2, #0xf]
    // 0xd042bc: DecompressPointer r3
    //     0xd042bc: add             x3, x3, HEAP, lsl #32
    // 0xd042c0: cmp             w1, w3
    // 0xd042c4: b.ne            #0xd042dc
    // 0xd042c8: r16 = Instance_EdgeInsets
    //     0xd042c8: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xd042cc: r30 = Instance_EdgeInsets
    //     0xd042cc: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xd042d0: stp             lr, x16, [SP]
    // 0xd042d4: r0 = ==()
    //     0xd042d4: bl              #0xd60b6c  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::==
    // 0xd042d8: tbz             w0, #4, #0xd042e4
    // 0xd042dc: r0 = true
    //     0xd042dc: add             x0, NULL, #0x20  ; true
    // 0xd042e0: b               #0xd0430c
    // 0xd042e4: ldur            x0, [fp, #-8]
    // 0xd042e8: ldur            x1, [fp, #-0x10]
    // 0xd042ec: LoadField: r2 = r0->field_1b
    //     0xd042ec: ldur            w2, [x0, #0x1b]
    // 0xd042f0: DecompressPointer r2
    //     0xd042f0: add             x2, x2, HEAP, lsl #32
    // 0xd042f4: LoadField: r0 = r1->field_1b
    //     0xd042f4: ldur            w0, [x1, #0x1b]
    // 0xd042f8: DecompressPointer r0
    //     0xd042f8: add             x0, x0, HEAP, lsl #32
    // 0xd042fc: stp             x0, x2, [SP]
    // 0xd04300: r0 = ==()
    //     0xd04300: bl              #0xd386f4  ; [dart:ui] Rect::==
    // 0xd04304: eor             x1, x0, #0x10
    // 0xd04308: mov             x0, x1
    // 0xd0430c: LeaveFrame
    //     0xd0430c: mov             SP, fp
    //     0xd04310: ldp             fp, lr, [SP], #0x10
    // 0xd04314: ret
    //     0xd04314: ret             
    // 0xd04318: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd04318: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd0431c: b               #0xd0426c
  }
}
