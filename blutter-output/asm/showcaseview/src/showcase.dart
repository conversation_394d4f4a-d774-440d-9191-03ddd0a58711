// lib: , url: package:showcaseview/src/showcase.dart

// class id: 1051126, size: 0x8
class :: {
}

// class id: 4086, size: 0x38, field offset: 0x14
class _ShowcaseState extends State<dynamic> {

  late final ShowCaseWidgetState showCaseWidgetState; // offset: 0x34

  _ initState(/* No info */) {
    // ** addr: 0x980164, size: 0x30
    // 0x980164: EnterFrame
    //     0x980164: stp             fp, lr, [SP, #-0x10]!
    //     0x980168: mov             fp, SP
    // 0x98016c: CheckStackOverflow
    //     0x98016c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980170: cmp             SP, x16
    //     0x980174: b.ls            #0x98018c
    // 0x980178: r0 = initRootWidget()
    //     0x980178: bl              #0x9801b8  ; [package:showcaseview/src/showcase.dart] _ShowcaseState::initRootWidget
    // 0x98017c: r0 = Null
    //     0x98017c: mov             x0, NULL
    // 0x980180: LeaveFrame
    //     0x980180: mov             SP, fp
    //     0x980184: ldp             fp, lr, [SP], #0x10
    // 0x980188: ret
    //     0x980188: ret             
    // 0x98018c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98018c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980190: b               #0x980178
  }
  _ initRootWidget(/* No info */) {
    // ** addr: 0x9801b8, size: 0x130
    // 0x9801b8: EnterFrame
    //     0x9801b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9801bc: mov             fp, SP
    // 0x9801c0: AllocStack(0x18)
    //     0x9801c0: sub             SP, SP, #0x18
    // 0x9801c4: SetupParameters(_ShowcaseState this /* r1 => r1, fp-0x8 */)
    //     0x9801c4: stur            x1, [fp, #-8]
    // 0x9801c8: CheckStackOverflow
    //     0x9801c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9801cc: cmp             SP, x16
    //     0x9801d0: b.ls            #0x9802dc
    // 0x9801d4: r1 = 1
    //     0x9801d4: movz            x1, #0x1
    // 0x9801d8: r0 = AllocateContext()
    //     0x9801d8: bl              #0xec126c  ; AllocateContextStub
    // 0x9801dc: mov             x1, x0
    // 0x9801e0: ldur            x0, [fp, #-8]
    // 0x9801e4: StoreField: r1->field_f = r0
    //     0x9801e4: stur            w0, [x1, #0xf]
    // 0x9801e8: r0 = LoadStaticField(0x7d4)
    //     0x9801e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9801ec: ldr             x0, [x0, #0xfa8]
    // 0x9801f0: cmp             w0, NULL
    // 0x9801f4: b.eq            #0x9802e4
    // 0x9801f8: LoadField: r3 = r0->field_53
    //     0x9801f8: ldur            w3, [x0, #0x53]
    // 0x9801fc: DecompressPointer r3
    //     0x9801fc: add             x3, x3, HEAP, lsl #32
    // 0x980200: stur            x3, [fp, #-0x10]
    // 0x980204: LoadField: r0 = r3->field_7
    //     0x980204: ldur            w0, [x3, #7]
    // 0x980208: DecompressPointer r0
    //     0x980208: add             x0, x0, HEAP, lsl #32
    // 0x98020c: mov             x2, x1
    // 0x980210: stur            x0, [fp, #-8]
    // 0x980214: r1 = Function '<anonymous closure>':.
    //     0x980214: add             x1, PP, #0x47, lsl #12  ; [pp+0x47da0] AnonymousClosure: (0x9802e8), in [package:showcaseview/src/showcase.dart] _ShowcaseState::initRootWidget (0x9801b8)
    //     0x980218: ldr             x1, [x1, #0xda0]
    // 0x98021c: r0 = AllocateClosure()
    //     0x98021c: bl              #0xec1630  ; AllocateClosureStub
    // 0x980220: ldur            x2, [fp, #-8]
    // 0x980224: mov             x3, x0
    // 0x980228: r1 = Null
    //     0x980228: mov             x1, NULL
    // 0x98022c: stur            x3, [fp, #-8]
    // 0x980230: cmp             w2, NULL
    // 0x980234: b.eq            #0x980254
    // 0x980238: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x980238: ldur            w4, [x2, #0x17]
    // 0x98023c: DecompressPointer r4
    //     0x98023c: add             x4, x4, HEAP, lsl #32
    // 0x980240: r8 = X0
    //     0x980240: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x980244: LoadField: r9 = r4->field_7
    //     0x980244: ldur            x9, [x4, #7]
    // 0x980248: r3 = Null
    //     0x980248: add             x3, PP, #0x47, lsl #12  ; [pp+0x47da8] Null
    //     0x98024c: ldr             x3, [x3, #0xda8]
    // 0x980250: blr             x9
    // 0x980254: ldur            x0, [fp, #-0x10]
    // 0x980258: LoadField: r1 = r0->field_b
    //     0x980258: ldur            w1, [x0, #0xb]
    // 0x98025c: LoadField: r2 = r0->field_f
    //     0x98025c: ldur            w2, [x0, #0xf]
    // 0x980260: DecompressPointer r2
    //     0x980260: add             x2, x2, HEAP, lsl #32
    // 0x980264: LoadField: r3 = r2->field_b
    //     0x980264: ldur            w3, [x2, #0xb]
    // 0x980268: r2 = LoadInt32Instr(r1)
    //     0x980268: sbfx            x2, x1, #1, #0x1f
    // 0x98026c: stur            x2, [fp, #-0x18]
    // 0x980270: r1 = LoadInt32Instr(r3)
    //     0x980270: sbfx            x1, x3, #1, #0x1f
    // 0x980274: cmp             x2, x1
    // 0x980278: b.ne            #0x980284
    // 0x98027c: mov             x1, x0
    // 0x980280: r0 = _growToNextCapacity()
    //     0x980280: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x980284: ldur            x2, [fp, #-0x10]
    // 0x980288: ldur            x3, [fp, #-0x18]
    // 0x98028c: add             x4, x3, #1
    // 0x980290: lsl             x5, x4, #1
    // 0x980294: StoreField: r2->field_b = r5
    //     0x980294: stur            w5, [x2, #0xb]
    // 0x980298: LoadField: r1 = r2->field_f
    //     0x980298: ldur            w1, [x2, #0xf]
    // 0x98029c: DecompressPointer r1
    //     0x98029c: add             x1, x1, HEAP, lsl #32
    // 0x9802a0: ldur            x0, [fp, #-8]
    // 0x9802a4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9802a4: add             x25, x1, x3, lsl #2
    //     0x9802a8: add             x25, x25, #0xf
    //     0x9802ac: str             w0, [x25]
    //     0x9802b0: tbz             w0, #0, #0x9802cc
    //     0x9802b4: ldurb           w16, [x1, #-1]
    //     0x9802b8: ldurb           w17, [x0, #-1]
    //     0x9802bc: and             x16, x17, x16, lsr #2
    //     0x9802c0: tst             x16, HEAP, lsr #32
    //     0x9802c4: b.eq            #0x9802cc
    //     0x9802c8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x9802cc: r0 = Null
    //     0x9802cc: mov             x0, NULL
    // 0x9802d0: LeaveFrame
    //     0x9802d0: mov             SP, fp
    //     0x9802d4: ldp             fp, lr, [SP], #0x10
    // 0x9802d8: ret
    //     0x9802d8: ret             
    // 0x9802dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9802dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9802e0: b               #0x9801d4
    // 0x9802e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9802e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9802e8, size: 0x11c
    // 0x9802e8: EnterFrame
    //     0x9802e8: stp             fp, lr, [SP, #-0x10]!
    //     0x9802ec: mov             fp, SP
    // 0x9802f0: AllocStack(0x10)
    //     0x9802f0: sub             SP, SP, #0x10
    // 0x9802f4: SetupParameters()
    //     0x9802f4: ldr             x0, [fp, #0x18]
    //     0x9802f8: ldur            w2, [x0, #0x17]
    //     0x9802fc: add             x2, x2, HEAP, lsl #32
    //     0x980300: stur            x2, [fp, #-0x10]
    // 0x980304: CheckStackOverflow
    //     0x980304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980308: cmp             SP, x16
    //     0x98030c: b.ls            #0x9803fc
    // 0x980310: LoadField: r0 = r2->field_f
    //     0x980310: ldur            w0, [x2, #0xf]
    // 0x980314: DecompressPointer r0
    //     0x980314: add             x0, x0, HEAP, lsl #32
    // 0x980318: stur            x0, [fp, #-8]
    // 0x98031c: LoadField: r1 = r0->field_f
    //     0x98031c: ldur            w1, [x0, #0xf]
    // 0x980320: DecompressPointer r1
    //     0x980320: add             x1, x1, HEAP, lsl #32
    // 0x980324: cmp             w1, NULL
    // 0x980328: b.ne            #0x98033c
    // 0x98032c: r0 = Null
    //     0x98032c: mov             x0, NULL
    // 0x980330: LeaveFrame
    //     0x980330: mov             SP, fp
    //     0x980334: ldp             fp, lr, [SP], #0x10
    // 0x980338: ret
    //     0x980338: ret             
    // 0x98033c: mov             x1, x0
    // 0x980340: LoadField: r0 = r1->field_33
    //     0x980340: ldur            w0, [x1, #0x33]
    // 0x980344: DecompressPointer r0
    //     0x980344: add             x0, x0, HEAP, lsl #32
    // 0x980348: r16 = Sentinel
    //     0x980348: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x98034c: cmp             w0, w16
    // 0x980350: b.ne            #0x980360
    // 0x980354: r2 = showCaseWidgetState
    //     0x980354: add             x2, PP, #0x47, lsl #12  ; [pp+0x47d10] Field <<EMAIL>>: late final (offset: 0x34)
    //     0x980358: ldr             x2, [x2, #0xd10]
    // 0x98035c: r0 = InitLateFinalInstanceField()
    //     0x98035c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x980360: LoadField: r1 = r0->field_1f
    //     0x980360: ldur            w1, [x0, #0x1f]
    // 0x980364: DecompressPointer r1
    //     0x980364: add             x1, x1, HEAP, lsl #32
    // 0x980368: mov             x0, x1
    // 0x98036c: ldur            x1, [fp, #-8]
    // 0x980370: StoreField: r1->field_2b = r0
    //     0x980370: stur            w0, [x1, #0x2b]
    //     0x980374: ldurb           w16, [x1, #-1]
    //     0x980378: ldurb           w17, [x0, #-1]
    //     0x98037c: and             x16, x17, x16, lsr #2
    //     0x980380: tst             x16, HEAP, lsr #32
    //     0x980384: b.eq            #0x98038c
    //     0x980388: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x98038c: ldur            x0, [fp, #-0x10]
    // 0x980390: LoadField: r2 = r0->field_f
    //     0x980390: ldur            w2, [x0, #0xf]
    // 0x980394: DecompressPointer r2
    //     0x980394: add             x2, x2, HEAP, lsl #32
    // 0x980398: mov             x1, x2
    // 0x98039c: stur            x2, [fp, #-8]
    // 0x9803a0: LoadField: r0 = r1->field_33
    //     0x9803a0: ldur            w0, [x1, #0x33]
    // 0x9803a4: DecompressPointer r0
    //     0x9803a4: add             x0, x0, HEAP, lsl #32
    // 0x9803a8: r16 = Sentinel
    //     0x9803a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9803ac: cmp             w0, w16
    // 0x9803b0: b.ne            #0x9803c0
    // 0x9803b4: r2 = showCaseWidgetState
    //     0x9803b4: add             x2, PP, #0x47, lsl #12  ; [pp+0x47d10] Field <<EMAIL>>: late final (offset: 0x34)
    //     0x9803b8: ldr             x2, [x2, #0xd10]
    // 0x9803bc: r0 = InitLateFinalInstanceField()
    //     0x9803bc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9803c0: LoadField: r1 = r0->field_1b
    //     0x9803c0: ldur            w1, [x0, #0x1b]
    // 0x9803c4: DecompressPointer r1
    //     0x9803c4: add             x1, x1, HEAP, lsl #32
    // 0x9803c8: mov             x0, x1
    // 0x9803cc: ldur            x1, [fp, #-8]
    // 0x9803d0: StoreField: r1->field_2f = r0
    //     0x9803d0: stur            w0, [x1, #0x2f]
    //     0x9803d4: ldurb           w16, [x1, #-1]
    //     0x9803d8: ldurb           w17, [x0, #-1]
    //     0x9803dc: and             x16, x17, x16, lsr #2
    //     0x9803e0: tst             x16, HEAP, lsr #32
    //     0x9803e4: b.eq            #0x9803ec
    //     0x9803e8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9803ec: r0 = Null
    //     0x9803ec: mov             x0, NULL
    // 0x9803f0: LeaveFrame
    //     0x9803f0: mov             SP, fp
    //     0x9803f4: ldp             fp, lr, [SP], #0x10
    // 0x9803f8: ret
    //     0x9803f8: ret             
    // 0x9803fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9803fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980400: b               #0x980310
  }
  ShowCaseWidgetState showCaseWidgetState(_ShowcaseState) {
    // ** addr: 0x980404, size: 0x44
    // 0x980404: EnterFrame
    //     0x980404: stp             fp, lr, [SP, #-0x10]!
    //     0x980408: mov             fp, SP
    // 0x98040c: CheckStackOverflow
    //     0x98040c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980410: cmp             SP, x16
    //     0x980414: b.ls            #0x98043c
    // 0x980418: ldr             x0, [fp, #0x10]
    // 0x98041c: LoadField: r1 = r0->field_f
    //     0x98041c: ldur            w1, [x0, #0xf]
    // 0x980420: DecompressPointer r1
    //     0x980420: add             x1, x1, HEAP, lsl #32
    // 0x980424: cmp             w1, NULL
    // 0x980428: b.eq            #0x980444
    // 0x98042c: r0 = of()
    //     0x98042c: bl              #0x91e504  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidget::of
    // 0x980430: LeaveFrame
    //     0x980430: mov             SP, fp
    //     0x980434: ldp             fp, lr, [SP], #0x10
    // 0x980438: ret
    //     0x980438: ret             
    // 0x98043c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98043c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980440: b               #0x980418
    // 0x980444: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980444: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9d20f8, size: 0x15c
    // 0x9d20f8: EnterFrame
    //     0x9d20f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9d20fc: mov             fp, SP
    // 0x9d2100: AllocStack(0x20)
    //     0x9d2100: sub             SP, SP, #0x20
    // 0x9d2104: SetupParameters(_ShowcaseState this /* r1 => r0, fp-0x8 */)
    //     0x9d2104: mov             x0, x1
    //     0x9d2108: stur            x1, [fp, #-8]
    // 0x9d210c: CheckStackOverflow
    //     0x9d210c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d2110: cmp             SP, x16
    //     0x9d2114: b.ls            #0x9d2240
    // 0x9d2118: mov             x1, x0
    // 0x9d211c: LoadField: r0 = r1->field_33
    //     0x9d211c: ldur            w0, [x1, #0x33]
    // 0x9d2120: DecompressPointer r0
    //     0x9d2120: add             x0, x0, HEAP, lsl #32
    // 0x9d2124: r16 = Sentinel
    //     0x9d2124: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9d2128: cmp             w0, w16
    // 0x9d212c: b.ne            #0x9d213c
    // 0x9d2130: r2 = showCaseWidgetState
    //     0x9d2130: add             x2, PP, #0x47, lsl #12  ; [pp+0x47d10] Field <<EMAIL>>: late final (offset: 0x34)
    //     0x9d2134: ldr             x2, [x2, #0xd10]
    // 0x9d2138: r0 = InitLateFinalInstanceField()
    //     0x9d2138: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9d213c: LoadField: r1 = r0->field_b
    //     0x9d213c: ldur            w1, [x0, #0xb]
    // 0x9d2140: DecompressPointer r1
    //     0x9d2140: add             x1, x1, HEAP, lsl #32
    // 0x9d2144: cmp             w1, NULL
    // 0x9d2148: b.eq            #0x9d2248
    // 0x9d214c: ldur            x2, [fp, #-8]
    // 0x9d2150: r0 = true
    //     0x9d2150: add             x0, NULL, #0x20  ; true
    // 0x9d2154: StoreField: r2->field_1f = r0
    //     0x9d2154: stur            w0, [x2, #0x1f]
    // 0x9d2158: mov             x1, x2
    // 0x9d215c: r0 = recalculateRootWidgetSize()
    //     0x9d215c: bl              #0x9d25ec  ; [package:showcaseview/src/showcase.dart] _ShowcaseState::recalculateRootWidgetSize
    // 0x9d2160: ldur            x0, [fp, #-8]
    // 0x9d2164: LoadField: r1 = r0->field_f
    //     0x9d2164: ldur            w1, [x0, #0xf]
    // 0x9d2168: DecompressPointer r1
    //     0x9d2168: add             x1, x1, HEAP, lsl #32
    // 0x9d216c: cmp             w1, NULL
    // 0x9d2170: b.eq            #0x9d224c
    // 0x9d2174: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9d2174: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9d2178: r0 = _of()
    //     0x9d2178: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x9d217c: ldur            x1, [fp, #-8]
    // 0x9d2180: LoadField: r0 = r1->field_27
    //     0x9d2180: ldur            w0, [x1, #0x27]
    // 0x9d2184: DecompressPointer r0
    //     0x9d2184: add             x0, x0, HEAP, lsl #32
    // 0x9d2188: cmp             w0, NULL
    // 0x9d218c: b.ne            #0x9d222c
    // 0x9d2190: LoadField: r0 = r1->field_2f
    //     0x9d2190: ldur            w0, [x1, #0x2f]
    // 0x9d2194: DecompressPointer r0
    //     0x9d2194: add             x0, x0, HEAP, lsl #32
    // 0x9d2198: stur            x0, [fp, #-0x18]
    // 0x9d219c: LoadField: r2 = r1->field_b
    //     0x9d219c: ldur            w2, [x1, #0xb]
    // 0x9d21a0: DecompressPointer r2
    //     0x9d21a0: add             x2, x2, HEAP, lsl #32
    // 0x9d21a4: cmp             w2, NULL
    // 0x9d21a8: b.eq            #0x9d2250
    // 0x9d21ac: LoadField: r3 = r2->field_b
    //     0x9d21ac: ldur            w3, [x2, #0xb]
    // 0x9d21b0: DecompressPointer r3
    //     0x9d21b0: add             x3, x3, HEAP, lsl #32
    // 0x9d21b4: stur            x3, [fp, #-0x10]
    // 0x9d21b8: LoadField: r2 = r1->field_2b
    //     0x9d21b8: ldur            w2, [x1, #0x2b]
    // 0x9d21bc: DecompressPointer r2
    //     0x9d21bc: add             x2, x2, HEAP, lsl #32
    // 0x9d21c0: cmp             w2, NULL
    // 0x9d21c4: b.eq            #0x9d21c8
    // 0x9d21c8: cmp             w2, NULL
    // 0x9d21cc: b.eq            #0x9d21d0
    // 0x9d21d0: r0 = GetPosition()
    //     0x9d21d0: bl              #0x9d25e0  ; AllocateGetPositionStub -> GetPosition (size=0x1c)
    // 0x9d21d4: mov             x2, x0
    // 0x9d21d8: r0 = Sentinel
    //     0x9d21d8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9d21dc: stur            x2, [fp, #-0x20]
    // 0x9d21e0: StoreField: r2->field_13 = r0
    //     0x9d21e0: stur            w0, [x2, #0x13]
    // 0x9d21e4: ArrayStore: r2[0] = r0  ; List_4
    //     0x9d21e4: stur            w0, [x2, #0x17]
    // 0x9d21e8: ldur            x0, [fp, #-0x10]
    // 0x9d21ec: StoreField: r2->field_7 = r0
    //     0x9d21ec: stur            w0, [x2, #7]
    // 0x9d21f0: r0 = Instance_EdgeInsets
    //     0x9d21f0: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0x9d21f4: StoreField: r2->field_b = r0
    //     0x9d21f4: stur            w0, [x2, #0xb]
    // 0x9d21f8: ldur            x0, [fp, #-0x18]
    // 0x9d21fc: StoreField: r2->field_f = r0
    //     0x9d21fc: stur            w0, [x2, #0xf]
    // 0x9d2200: mov             x1, x2
    // 0x9d2204: r0 = getRenderBox()
    //     0x9d2204: bl              #0x9d2460  ; [package:showcaseview/src/get_position.dart] GetPosition::getRenderBox
    // 0x9d2208: ldur            x0, [fp, #-0x20]
    // 0x9d220c: ldur            x1, [fp, #-8]
    // 0x9d2210: StoreField: r1->field_27 = r0
    //     0x9d2210: stur            w0, [x1, #0x27]
    //     0x9d2214: ldurb           w16, [x1, #-1]
    //     0x9d2218: ldurb           w17, [x0, #-1]
    //     0x9d221c: and             x16, x17, x16, lsr #2
    //     0x9d2220: tst             x16, HEAP, lsr #32
    //     0x9d2224: b.eq            #0x9d222c
    //     0x9d2228: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d222c: r0 = showOverlay()
    //     0x9d222c: bl              #0x9d2254  ; [package:showcaseview/src/showcase.dart] _ShowcaseState::showOverlay
    // 0x9d2230: r0 = Null
    //     0x9d2230: mov             x0, NULL
    // 0x9d2234: LeaveFrame
    //     0x9d2234: mov             SP, fp
    //     0x9d2238: ldp             fp, lr, [SP], #0x10
    // 0x9d223c: ret
    //     0x9d223c: ret             
    // 0x9d2240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d2240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d2244: b               #0x9d2118
    // 0x9d2248: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d2248: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d224c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d224c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d2250: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d2250: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ showOverlay(/* No info */) {
    // ** addr: 0x9d2254, size: 0x120
    // 0x9d2254: EnterFrame
    //     0x9d2254: stp             fp, lr, [SP, #-0x10]!
    //     0x9d2258: mov             fp, SP
    // 0x9d225c: AllocStack(0x28)
    //     0x9d225c: sub             SP, SP, #0x28
    // 0x9d2260: SetupParameters(_ShowcaseState this /* r1 => r1, fp-0x8 */)
    //     0x9d2260: stur            x1, [fp, #-8]
    // 0x9d2264: CheckStackOverflow
    //     0x9d2264: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d2268: cmp             SP, x16
    //     0x9d226c: b.ls            #0x9d2360
    // 0x9d2270: r1 = 2
    //     0x9d2270: movz            x1, #0x2
    // 0x9d2274: r0 = AllocateContext()
    //     0x9d2274: bl              #0xec126c  ; AllocateContextStub
    // 0x9d2278: mov             x2, x0
    // 0x9d227c: ldur            x0, [fp, #-8]
    // 0x9d2280: stur            x2, [fp, #-0x10]
    // 0x9d2284: StoreField: r2->field_f = r0
    //     0x9d2284: stur            w0, [x2, #0xf]
    // 0x9d2288: LoadField: r1 = r0->field_f
    //     0x9d2288: ldur            w1, [x0, #0xf]
    // 0x9d228c: DecompressPointer r1
    //     0x9d228c: add             x1, x1, HEAP, lsl #32
    // 0x9d2290: cmp             w1, NULL
    // 0x9d2294: b.eq            #0x9d2368
    // 0x9d2298: r0 = activeTargetWidget()
    //     0x9d2298: bl              #0x9d2374  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidget::activeTargetWidget
    // 0x9d229c: mov             x3, x0
    // 0x9d22a0: ldur            x2, [fp, #-0x10]
    // 0x9d22a4: stur            x3, [fp, #-0x18]
    // 0x9d22a8: StoreField: r2->field_13 = r0
    //     0x9d22a8: stur            w0, [x2, #0x13]
    //     0x9d22ac: ldurb           w16, [x2, #-1]
    //     0x9d22b0: ldurb           w17, [x0, #-1]
    //     0x9d22b4: and             x16, x17, x16, lsr #2
    //     0x9d22b8: tst             x16, HEAP, lsr #32
    //     0x9d22bc: b.eq            #0x9d22c4
    //     0x9d22c0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9d22c4: r1 = Function '<anonymous closure>':.
    //     0x9d22c4: add             x1, PP, #0x47, lsl #12  ; [pp+0x47d68] AnonymousClosure: (0x9d23d0), in [package:showcaseview/src/showcase.dart] _ShowcaseState::showOverlay (0x9d2254)
    //     0x9d22c8: ldr             x1, [x1, #0xd68]
    // 0x9d22cc: r0 = AllocateClosure()
    //     0x9d22cc: bl              #0xec1630  ; AllocateClosureStub
    // 0x9d22d0: ldur            x1, [fp, #-8]
    // 0x9d22d4: mov             x2, x0
    // 0x9d22d8: r0 = setState()
    //     0x9d22d8: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9d22dc: ldur            x1, [fp, #-8]
    // 0x9d22e0: LoadField: r0 = r1->field_b
    //     0x9d22e0: ldur            w0, [x1, #0xb]
    // 0x9d22e4: DecompressPointer r0
    //     0x9d22e4: add             x0, x0, HEAP, lsl #32
    // 0x9d22e8: cmp             w0, NULL
    // 0x9d22ec: b.eq            #0x9d236c
    // 0x9d22f0: LoadField: r2 = r0->field_b
    //     0x9d22f0: ldur            w2, [x0, #0xb]
    // 0x9d22f4: DecompressPointer r2
    //     0x9d22f4: add             x2, x2, HEAP, lsl #32
    // 0x9d22f8: ldur            x0, [fp, #-0x18]
    // 0x9d22fc: r3 = LoadClassIdInstr(r0)
    //     0x9d22fc: ldur            x3, [x0, #-1]
    //     0x9d2300: ubfx            x3, x3, #0xc, #0x14
    // 0x9d2304: stp             x2, x0, [SP]
    // 0x9d2308: mov             x0, x3
    // 0x9d230c: mov             lr, x0
    // 0x9d2310: ldr             lr, [x21, lr, lsl #3]
    // 0x9d2314: blr             lr
    // 0x9d2318: tbnz            w0, #4, #0x9d2350
    // 0x9d231c: ldur            x1, [fp, #-8]
    // 0x9d2320: LoadField: r0 = r1->field_33
    //     0x9d2320: ldur            w0, [x1, #0x33]
    // 0x9d2324: DecompressPointer r0
    //     0x9d2324: add             x0, x0, HEAP, lsl #32
    // 0x9d2328: r16 = Sentinel
    //     0x9d2328: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9d232c: cmp             w0, w16
    // 0x9d2330: b.ne            #0x9d2340
    // 0x9d2334: r2 = showCaseWidgetState
    //     0x9d2334: add             x2, PP, #0x47, lsl #12  ; [pp+0x47d10] Field <<EMAIL>>: late final (offset: 0x34)
    //     0x9d2338: ldr             x2, [x2, #0xd10]
    // 0x9d233c: r0 = InitLateFinalInstanceField()
    //     0x9d233c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9d2340: LoadField: r1 = r0->field_b
    //     0x9d2340: ldur            w1, [x0, #0xb]
    // 0x9d2344: DecompressPointer r1
    //     0x9d2344: add             x1, x1, HEAP, lsl #32
    // 0x9d2348: cmp             w1, NULL
    // 0x9d234c: b.eq            #0x9d2370
    // 0x9d2350: r0 = Null
    //     0x9d2350: mov             x0, NULL
    // 0x9d2354: LeaveFrame
    //     0x9d2354: mov             SP, fp
    //     0x9d2358: ldp             fp, lr, [SP], #0x10
    // 0x9d235c: ret
    //     0x9d235c: ret             
    // 0x9d2360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d2360: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d2364: b               #0x9d2270
    // 0x9d2368: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d2368: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d236c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d236c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d2370: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d2370: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9d23d0, size: 0x90
    // 0x9d23d0: EnterFrame
    //     0x9d23d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9d23d4: mov             fp, SP
    // 0x9d23d8: AllocStack(0x18)
    //     0x9d23d8: sub             SP, SP, #0x18
    // 0x9d23dc: SetupParameters()
    //     0x9d23dc: ldr             x0, [fp, #0x10]
    //     0x9d23e0: ldur            w1, [x0, #0x17]
    //     0x9d23e4: add             x1, x1, HEAP, lsl #32
    // 0x9d23e8: CheckStackOverflow
    //     0x9d23e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d23ec: cmp             SP, x16
    //     0x9d23f0: b.ls            #0x9d2454
    // 0x9d23f4: LoadField: r2 = r1->field_f
    //     0x9d23f4: ldur            w2, [x1, #0xf]
    // 0x9d23f8: DecompressPointer r2
    //     0x9d23f8: add             x2, x2, HEAP, lsl #32
    // 0x9d23fc: stur            x2, [fp, #-8]
    // 0x9d2400: LoadField: r0 = r1->field_13
    //     0x9d2400: ldur            w0, [x1, #0x13]
    // 0x9d2404: DecompressPointer r0
    //     0x9d2404: add             x0, x0, HEAP, lsl #32
    // 0x9d2408: LoadField: r1 = r2->field_b
    //     0x9d2408: ldur            w1, [x2, #0xb]
    // 0x9d240c: DecompressPointer r1
    //     0x9d240c: add             x1, x1, HEAP, lsl #32
    // 0x9d2410: cmp             w1, NULL
    // 0x9d2414: b.eq            #0x9d245c
    // 0x9d2418: LoadField: r3 = r1->field_b
    //     0x9d2418: ldur            w3, [x1, #0xb]
    // 0x9d241c: DecompressPointer r3
    //     0x9d241c: add             x3, x3, HEAP, lsl #32
    // 0x9d2420: r1 = LoadClassIdInstr(r0)
    //     0x9d2420: ldur            x1, [x0, #-1]
    //     0x9d2424: ubfx            x1, x1, #0xc, #0x14
    // 0x9d2428: stp             x3, x0, [SP]
    // 0x9d242c: mov             x0, x1
    // 0x9d2430: mov             lr, x0
    // 0x9d2434: ldr             lr, [x21, lr, lsl #3]
    // 0x9d2438: blr             lr
    // 0x9d243c: ldur            x1, [fp, #-8]
    // 0x9d2440: StoreField: r1->field_13 = r0
    //     0x9d2440: stur            w0, [x1, #0x13]
    // 0x9d2444: r0 = Null
    //     0x9d2444: mov             x0, NULL
    // 0x9d2448: LeaveFrame
    //     0x9d2448: mov             SP, fp
    //     0x9d244c: ldp             fp, lr, [SP], #0x10
    // 0x9d2450: ret
    //     0x9d2450: ret             
    // 0x9d2454: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d2454: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d2458: b               #0x9d23f4
    // 0x9d245c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d245c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ recalculateRootWidgetSize(/* No info */) {
    // ** addr: 0x9d25ec, size: 0x130
    // 0x9d25ec: EnterFrame
    //     0x9d25ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9d25f0: mov             fp, SP
    // 0x9d25f4: AllocStack(0x18)
    //     0x9d25f4: sub             SP, SP, #0x18
    // 0x9d25f8: SetupParameters(_ShowcaseState this /* r1 => r1, fp-0x8 */)
    //     0x9d25f8: stur            x1, [fp, #-8]
    // 0x9d25fc: CheckStackOverflow
    //     0x9d25fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d2600: cmp             SP, x16
    //     0x9d2604: b.ls            #0x9d2710
    // 0x9d2608: r1 = 1
    //     0x9d2608: movz            x1, #0x1
    // 0x9d260c: r0 = AllocateContext()
    //     0x9d260c: bl              #0xec126c  ; AllocateContextStub
    // 0x9d2610: mov             x1, x0
    // 0x9d2614: ldur            x0, [fp, #-8]
    // 0x9d2618: StoreField: r1->field_f = r0
    //     0x9d2618: stur            w0, [x1, #0xf]
    // 0x9d261c: r0 = LoadStaticField(0x7d4)
    //     0x9d261c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9d2620: ldr             x0, [x0, #0xfa8]
    // 0x9d2624: cmp             w0, NULL
    // 0x9d2628: b.eq            #0x9d2718
    // 0x9d262c: LoadField: r3 = r0->field_53
    //     0x9d262c: ldur            w3, [x0, #0x53]
    // 0x9d2630: DecompressPointer r3
    //     0x9d2630: add             x3, x3, HEAP, lsl #32
    // 0x9d2634: stur            x3, [fp, #-0x10]
    // 0x9d2638: LoadField: r0 = r3->field_7
    //     0x9d2638: ldur            w0, [x3, #7]
    // 0x9d263c: DecompressPointer r0
    //     0x9d263c: add             x0, x0, HEAP, lsl #32
    // 0x9d2640: mov             x2, x1
    // 0x9d2644: stur            x0, [fp, #-8]
    // 0x9d2648: r1 = Function '<anonymous closure>':.
    //     0x9d2648: add             x1, PP, #0x47, lsl #12  ; [pp+0x47d78] AnonymousClosure: (0x9d271c), in [package:showcaseview/src/showcase.dart] _ShowcaseState::recalculateRootWidgetSize (0x9d25ec)
    //     0x9d264c: ldr             x1, [x1, #0xd78]
    // 0x9d2650: r0 = AllocateClosure()
    //     0x9d2650: bl              #0xec1630  ; AllocateClosureStub
    // 0x9d2654: ldur            x2, [fp, #-8]
    // 0x9d2658: mov             x3, x0
    // 0x9d265c: r1 = Null
    //     0x9d265c: mov             x1, NULL
    // 0x9d2660: stur            x3, [fp, #-8]
    // 0x9d2664: cmp             w2, NULL
    // 0x9d2668: b.eq            #0x9d2688
    // 0x9d266c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9d266c: ldur            w4, [x2, #0x17]
    // 0x9d2670: DecompressPointer r4
    //     0x9d2670: add             x4, x4, HEAP, lsl #32
    // 0x9d2674: r8 = X0
    //     0x9d2674: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x9d2678: LoadField: r9 = r4->field_7
    //     0x9d2678: ldur            x9, [x4, #7]
    // 0x9d267c: r3 = Null
    //     0x9d267c: add             x3, PP, #0x47, lsl #12  ; [pp+0x47d80] Null
    //     0x9d2680: ldr             x3, [x3, #0xd80]
    // 0x9d2684: blr             x9
    // 0x9d2688: ldur            x0, [fp, #-0x10]
    // 0x9d268c: LoadField: r1 = r0->field_b
    //     0x9d268c: ldur            w1, [x0, #0xb]
    // 0x9d2690: LoadField: r2 = r0->field_f
    //     0x9d2690: ldur            w2, [x0, #0xf]
    // 0x9d2694: DecompressPointer r2
    //     0x9d2694: add             x2, x2, HEAP, lsl #32
    // 0x9d2698: LoadField: r3 = r2->field_b
    //     0x9d2698: ldur            w3, [x2, #0xb]
    // 0x9d269c: r2 = LoadInt32Instr(r1)
    //     0x9d269c: sbfx            x2, x1, #1, #0x1f
    // 0x9d26a0: stur            x2, [fp, #-0x18]
    // 0x9d26a4: r1 = LoadInt32Instr(r3)
    //     0x9d26a4: sbfx            x1, x3, #1, #0x1f
    // 0x9d26a8: cmp             x2, x1
    // 0x9d26ac: b.ne            #0x9d26b8
    // 0x9d26b0: mov             x1, x0
    // 0x9d26b4: r0 = _growToNextCapacity()
    //     0x9d26b4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9d26b8: ldur            x2, [fp, #-0x10]
    // 0x9d26bc: ldur            x3, [fp, #-0x18]
    // 0x9d26c0: add             x4, x3, #1
    // 0x9d26c4: lsl             x5, x4, #1
    // 0x9d26c8: StoreField: r2->field_b = r5
    //     0x9d26c8: stur            w5, [x2, #0xb]
    // 0x9d26cc: LoadField: r1 = r2->field_f
    //     0x9d26cc: ldur            w1, [x2, #0xf]
    // 0x9d26d0: DecompressPointer r1
    //     0x9d26d0: add             x1, x1, HEAP, lsl #32
    // 0x9d26d4: ldur            x0, [fp, #-8]
    // 0x9d26d8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9d26d8: add             x25, x1, x3, lsl #2
    //     0x9d26dc: add             x25, x25, #0xf
    //     0x9d26e0: str             w0, [x25]
    //     0x9d26e4: tbz             w0, #0, #0x9d2700
    //     0x9d26e8: ldurb           w16, [x1, #-1]
    //     0x9d26ec: ldurb           w17, [x0, #-1]
    //     0x9d26f0: and             x16, x17, x16, lsr #2
    //     0x9d26f4: tst             x16, HEAP, lsr #32
    //     0x9d26f8: b.eq            #0x9d2700
    //     0x9d26fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x9d2700: r0 = Null
    //     0x9d2700: mov             x0, NULL
    // 0x9d2704: LeaveFrame
    //     0x9d2704: mov             SP, fp
    //     0x9d2708: ldp             fp, lr, [SP], #0x10
    // 0x9d270c: ret
    //     0x9d270c: ret             
    // 0x9d2710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d2710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d2714: b               #0x9d2608
    // 0x9d2718: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d2718: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9d271c, size: 0x1c0
    // 0x9d271c: EnterFrame
    //     0x9d271c: stp             fp, lr, [SP, #-0x10]!
    //     0x9d2720: mov             fp, SP
    // 0x9d2724: AllocStack(0x30)
    //     0x9d2724: sub             SP, SP, #0x30
    // 0x9d2728: SetupParameters()
    //     0x9d2728: ldr             x0, [fp, #0x18]
    //     0x9d272c: ldur            w1, [x0, #0x17]
    //     0x9d2730: add             x1, x1, HEAP, lsl #32
    //     0x9d2734: stur            x1, [fp, #-8]
    // 0x9d2738: CheckStackOverflow
    //     0x9d2738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d273c: cmp             SP, x16
    //     0x9d2740: b.ls            #0x9d28cc
    // 0x9d2744: LoadField: r0 = r1->field_f
    //     0x9d2744: ldur            w0, [x1, #0xf]
    // 0x9d2748: DecompressPointer r0
    //     0x9d2748: add             x0, x0, HEAP, lsl #32
    // 0x9d274c: LoadField: r2 = r0->field_f
    //     0x9d274c: ldur            w2, [x0, #0xf]
    // 0x9d2750: DecompressPointer r2
    //     0x9d2750: add             x2, x2, HEAP, lsl #32
    // 0x9d2754: cmp             w2, NULL
    // 0x9d2758: b.ne            #0x9d276c
    // 0x9d275c: r0 = Null
    //     0x9d275c: mov             x0, NULL
    // 0x9d2760: LeaveFrame
    //     0x9d2760: mov             SP, fp
    //     0x9d2764: ldp             fp, lr, [SP], #0x10
    // 0x9d2768: ret
    //     0x9d2768: ret             
    // 0x9d276c: r16 = <State<WidgetsApp>>
    //     0x9d276c: add             x16, PP, #0x40, lsl #12  ; [pp+0x405b0] TypeArguments: <State<WidgetsApp>>
    //     0x9d2770: ldr             x16, [x16, #0x5b0]
    // 0x9d2774: stp             x2, x16, [SP]
    // 0x9d2778: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9d2778: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9d277c: r0 = findRootAncestorStateOfType()
    //     0x9d277c: bl              #0x917b38  ; [package:flutter/src/widgets/framework.dart] Element::findRootAncestorStateOfType
    // 0x9d2780: mov             x2, x0
    // 0x9d2784: ldur            x0, [fp, #-8]
    // 0x9d2788: stur            x2, [fp, #-0x18]
    // 0x9d278c: LoadField: r3 = r0->field_f
    //     0x9d278c: ldur            w3, [x0, #0xf]
    // 0x9d2790: DecompressPointer r3
    //     0x9d2790: add             x3, x3, HEAP, lsl #32
    // 0x9d2794: stur            x3, [fp, #-0x10]
    // 0x9d2798: cmp             w2, NULL
    // 0x9d279c: b.ne            #0x9d27b4
    // 0x9d27a0: mov             x5, x3
    // 0x9d27a4: mov             x3, x0
    // 0x9d27a8: mov             x4, x2
    // 0x9d27ac: r6 = Null
    //     0x9d27ac: mov             x6, NULL
    // 0x9d27b0: b               #0x9d27d8
    // 0x9d27b4: LoadField: r1 = r2->field_f
    //     0x9d27b4: ldur            w1, [x2, #0xf]
    // 0x9d27b8: DecompressPointer r1
    //     0x9d27b8: add             x1, x1, HEAP, lsl #32
    // 0x9d27bc: cmp             w1, NULL
    // 0x9d27c0: b.eq            #0x9d28d4
    // 0x9d27c4: r0 = renderObject()
    //     0x9d27c4: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0x9d27c8: mov             x6, x0
    // 0x9d27cc: ldur            x3, [fp, #-8]
    // 0x9d27d0: ldur            x4, [fp, #-0x18]
    // 0x9d27d4: ldur            x5, [fp, #-0x10]
    // 0x9d27d8: mov             x0, x6
    // 0x9d27dc: stur            x6, [fp, #-0x20]
    // 0x9d27e0: r2 = Null
    //     0x9d27e0: mov             x2, NULL
    // 0x9d27e4: r1 = Null
    //     0x9d27e4: mov             x1, NULL
    // 0x9d27e8: r4 = LoadClassIdInstr(r0)
    //     0x9d27e8: ldur            x4, [x0, #-1]
    //     0x9d27ec: ubfx            x4, x4, #0xc, #0x14
    // 0x9d27f0: sub             x4, x4, #0xbba
    // 0x9d27f4: cmp             x4, #0x9a
    // 0x9d27f8: b.ls            #0x9d280c
    // 0x9d27fc: r8 = RenderBox?
    //     0x9d27fc: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x9d2800: r3 = Null
    //     0x9d2800: add             x3, PP, #0x47, lsl #12  ; [pp+0x47d90] Null
    //     0x9d2804: ldr             x3, [x3, #0xd90]
    // 0x9d2808: r0 = RenderBox?()
    //     0x9d2808: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x9d280c: ldur            x0, [fp, #-0x20]
    // 0x9d2810: ldur            x1, [fp, #-0x10]
    // 0x9d2814: StoreField: r1->field_2f = r0
    //     0x9d2814: stur            w0, [x1, #0x2f]
    //     0x9d2818: ldurb           w16, [x1, #-1]
    //     0x9d281c: ldurb           w17, [x0, #-1]
    //     0x9d2820: and             x16, x17, x16, lsr #2
    //     0x9d2824: tst             x16, HEAP, lsr #32
    //     0x9d2828: b.eq            #0x9d2830
    //     0x9d282c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d2830: ldur            x0, [fp, #-8]
    // 0x9d2834: LoadField: r2 = r0->field_f
    //     0x9d2834: ldur            w2, [x0, #0xf]
    // 0x9d2838: DecompressPointer r2
    //     0x9d2838: add             x2, x2, HEAP, lsl #32
    // 0x9d283c: ldur            x0, [fp, #-0x18]
    // 0x9d2840: stur            x2, [fp, #-0x10]
    // 0x9d2844: cmp             w0, NULL
    // 0x9d2848: b.ne            #0x9d2874
    // 0x9d284c: LoadField: r1 = r2->field_f
    //     0x9d284c: ldur            w1, [x2, #0xf]
    // 0x9d2850: DecompressPointer r1
    //     0x9d2850: add             x1, x1, HEAP, lsl #32
    // 0x9d2854: cmp             w1, NULL
    // 0x9d2858: b.eq            #0x9d28d8
    // 0x9d285c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9d285c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9d2860: r0 = _of()
    //     0x9d2860: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x9d2864: LoadField: r1 = r0->field_7
    //     0x9d2864: ldur            w1, [x0, #7]
    // 0x9d2868: DecompressPointer r1
    //     0x9d2868: add             x1, x1, HEAP, lsl #32
    // 0x9d286c: mov             x0, x1
    // 0x9d2870: b               #0x9d289c
    // 0x9d2874: mov             x0, x2
    // 0x9d2878: LoadField: r1 = r0->field_2f
    //     0x9d2878: ldur            w1, [x0, #0x2f]
    // 0x9d287c: DecompressPointer r1
    //     0x9d287c: add             x1, x1, HEAP, lsl #32
    // 0x9d2880: cmp             w1, NULL
    // 0x9d2884: b.ne            #0x9d2890
    // 0x9d2888: r1 = Null
    //     0x9d2888: mov             x1, NULL
    // 0x9d288c: b               #0x9d2898
    // 0x9d2890: r0 = size()
    //     0x9d2890: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x9d2894: mov             x1, x0
    // 0x9d2898: mov             x0, x1
    // 0x9d289c: ldur            x1, [fp, #-0x10]
    // 0x9d28a0: StoreField: r1->field_2b = r0
    //     0x9d28a0: stur            w0, [x1, #0x2b]
    //     0x9d28a4: ldurb           w16, [x1, #-1]
    //     0x9d28a8: ldurb           w17, [x0, #-1]
    //     0x9d28ac: and             x16, x17, x16, lsr #2
    //     0x9d28b0: tst             x16, HEAP, lsr #32
    //     0x9d28b4: b.eq            #0x9d28bc
    //     0x9d28b8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d28bc: r0 = Null
    //     0x9d28bc: mov             x0, NULL
    // 0x9d28c0: LeaveFrame
    //     0x9d28c0: mov             SP, fp
    //     0x9d28c4: ldp             fp, lr, [SP], #0x10
    // 0x9d28c8: ret
    //     0x9d28c8: ret             
    // 0x9d28cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d28cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d28d0: b               #0x9d2744
    // 0x9d28d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d28d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d28d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d28d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa4c8ac, size: 0xf0
    // 0xa4c8ac: EnterFrame
    //     0xa4c8ac: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c8b0: mov             fp, SP
    // 0xa4c8b4: AllocStack(0x28)
    //     0xa4c8b4: sub             SP, SP, #0x28
    // 0xa4c8b8: SetupParameters(_ShowcaseState this /* r1 => r1, fp-0x8 */)
    //     0xa4c8b8: stur            x1, [fp, #-8]
    // 0xa4c8bc: CheckStackOverflow
    //     0xa4c8bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c8c0: cmp             SP, x16
    //     0xa4c8c4: b.ls            #0xa4c990
    // 0xa4c8c8: r1 = 1
    //     0xa4c8c8: movz            x1, #0x1
    // 0xa4c8cc: r0 = AllocateContext()
    //     0xa4c8cc: bl              #0xec126c  ; AllocateContextStub
    // 0xa4c8d0: mov             x2, x0
    // 0xa4c8d4: ldur            x0, [fp, #-8]
    // 0xa4c8d8: stur            x2, [fp, #-0x10]
    // 0xa4c8dc: StoreField: r2->field_f = r0
    //     0xa4c8dc: stur            w0, [x2, #0xf]
    // 0xa4c8e0: mov             x1, x0
    // 0xa4c8e4: LoadField: r0 = r1->field_33
    //     0xa4c8e4: ldur            w0, [x1, #0x33]
    // 0xa4c8e8: DecompressPointer r0
    //     0xa4c8e8: add             x0, x0, HEAP, lsl #32
    // 0xa4c8ec: r16 = Sentinel
    //     0xa4c8ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4c8f0: cmp             w0, w16
    // 0xa4c8f4: b.ne            #0xa4c904
    // 0xa4c8f8: r2 = showCaseWidgetState
    //     0xa4c8f8: add             x2, PP, #0x47, lsl #12  ; [pp+0x47d10] Field <<EMAIL>>: late final (offset: 0x34)
    //     0xa4c8fc: ldr             x2, [x2, #0xd10]
    // 0xa4c900: r0 = InitLateFinalInstanceField()
    //     0xa4c900: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa4c904: LoadField: r1 = r0->field_23
    //     0xa4c904: ldur            w1, [x0, #0x23]
    // 0xa4c908: DecompressPointer r1
    //     0xa4c908: add             x1, x1, HEAP, lsl #32
    // 0xa4c90c: ldur            x0, [fp, #-8]
    // 0xa4c910: stur            x1, [fp, #-0x20]
    // 0xa4c914: LoadField: r2 = r0->field_2f
    //     0xa4c914: ldur            w2, [x0, #0x2f]
    // 0xa4c918: DecompressPointer r2
    //     0xa4c918: add             x2, x2, HEAP, lsl #32
    // 0xa4c91c: stur            x2, [fp, #-0x18]
    // 0xa4c920: LoadField: r3 = r0->field_b
    //     0xa4c920: ldur            w3, [x0, #0xb]
    // 0xa4c924: DecompressPointer r3
    //     0xa4c924: add             x3, x3, HEAP, lsl #32
    // 0xa4c928: cmp             w3, NULL
    // 0xa4c92c: b.eq            #0xa4c998
    // 0xa4c930: LoadField: r0 = r3->field_f
    //     0xa4c930: ldur            w0, [x3, #0xf]
    // 0xa4c934: DecompressPointer r0
    //     0xa4c934: add             x0, x0, HEAP, lsl #32
    // 0xa4c938: stur            x0, [fp, #-8]
    // 0xa4c93c: r0 = AnchoredOverlay()
    //     0xa4c93c: bl              #0xa4c99c  ; AllocateAnchoredOverlayStub -> AnchoredOverlay (size=0x1c)
    // 0xa4c940: mov             x3, x0
    // 0xa4c944: r0 = true
    //     0xa4c944: add             x0, NULL, #0x20  ; true
    // 0xa4c948: stur            x3, [fp, #-0x28]
    // 0xa4c94c: StoreField: r3->field_b = r0
    //     0xa4c94c: stur            w0, [x3, #0xb]
    // 0xa4c950: ldur            x2, [fp, #-0x10]
    // 0xa4c954: r1 = Function '<anonymous closure>':.
    //     0xa4c954: add             x1, PP, #0x47, lsl #12  ; [pp+0x47d18] AnonymousClosure: (0xa4c9a8), in [package:showcaseview/src/showcase.dart] _ShowcaseState::build (0xa4c8ac)
    //     0xa4c958: ldr             x1, [x1, #0xd18]
    // 0xa4c95c: r0 = AllocateClosure()
    //     0xa4c95c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4c960: mov             x1, x0
    // 0xa4c964: ldur            x0, [fp, #-0x28]
    // 0xa4c968: StoreField: r0->field_f = r1
    //     0xa4c968: stur            w1, [x0, #0xf]
    // 0xa4c96c: ldur            x1, [fp, #-8]
    // 0xa4c970: StoreField: r0->field_13 = r1
    //     0xa4c970: stur            w1, [x0, #0x13]
    // 0xa4c974: ldur            x1, [fp, #-0x18]
    // 0xa4c978: ArrayStore: r0[0] = r1  ; List_4
    //     0xa4c978: stur            w1, [x0, #0x17]
    // 0xa4c97c: ldur            x1, [fp, #-0x20]
    // 0xa4c980: StoreField: r0->field_7 = r1
    //     0xa4c980: stur            w1, [x0, #7]
    // 0xa4c984: LeaveFrame
    //     0xa4c984: mov             SP, fp
    //     0xa4c988: ldp             fp, lr, [SP], #0x10
    // 0xa4c98c: ret
    //     0xa4c98c: ret             
    // 0xa4c990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c994: b               #0xa4c8c8
    // 0xa4c998: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4c998: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, Rect, Offset) {
    // ** addr: 0xa4c9a8, size: 0x140
    // 0xa4c9a8: EnterFrame
    //     0xa4c9a8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c9ac: mov             fp, SP
    // 0xa4c9b0: AllocStack(0x30)
    //     0xa4c9b0: sub             SP, SP, #0x30
    // 0xa4c9b4: SetupParameters()
    //     0xa4c9b4: ldr             x0, [fp, #0x28]
    //     0xa4c9b8: ldur            w2, [x0, #0x17]
    //     0xa4c9bc: add             x2, x2, HEAP, lsl #32
    //     0xa4c9c0: stur            x2, [fp, #-8]
    // 0xa4c9c4: CheckStackOverflow
    //     0xa4c9c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c9c8: cmp             SP, x16
    //     0xa4c9cc: b.ls            #0xa4cadc
    // 0xa4c9d0: LoadField: r0 = r2->field_f
    //     0xa4c9d0: ldur            w0, [x2, #0xf]
    // 0xa4c9d4: DecompressPointer r0
    //     0xa4c9d4: add             x0, x0, HEAP, lsl #32
    // 0xa4c9d8: LoadField: r1 = r0->field_2b
    //     0xa4c9d8: ldur            w1, [x0, #0x2b]
    // 0xa4c9dc: DecompressPointer r1
    //     0xa4c9dc: add             x1, x1, HEAP, lsl #32
    // 0xa4c9e0: cmp             w1, NULL
    // 0xa4c9e4: b.ne            #0xa4ca04
    // 0xa4c9e8: ldr             x1, [fp, #0x20]
    // 0xa4c9ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa4c9ec: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa4c9f0: r0 = _of()
    //     0xa4c9f0: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa4c9f4: LoadField: r1 = r0->field_7
    //     0xa4c9f4: ldur            w1, [x0, #7]
    // 0xa4c9f8: DecompressPointer r1
    //     0xa4c9f8: add             x1, x1, HEAP, lsl #32
    // 0xa4c9fc: mov             x6, x1
    // 0xa4ca00: b               #0xa4ca08
    // 0xa4ca04: mov             x6, x1
    // 0xa4ca08: ldur            x0, [fp, #-8]
    // 0xa4ca0c: stur            x6, [fp, #-0x28]
    // 0xa4ca10: LoadField: r1 = r0->field_f
    //     0xa4ca10: ldur            w1, [x0, #0xf]
    // 0xa4ca14: DecompressPointer r1
    //     0xa4ca14: add             x1, x1, HEAP, lsl #32
    // 0xa4ca18: stur            x1, [fp, #-0x20]
    // 0xa4ca1c: LoadField: r2 = r1->field_2f
    //     0xa4ca1c: ldur            w2, [x1, #0x2f]
    // 0xa4ca20: DecompressPointer r2
    //     0xa4ca20: add             x2, x2, HEAP, lsl #32
    // 0xa4ca24: stur            x2, [fp, #-0x18]
    // 0xa4ca28: LoadField: r3 = r1->field_b
    //     0xa4ca28: ldur            w3, [x1, #0xb]
    // 0xa4ca2c: DecompressPointer r3
    //     0xa4ca2c: add             x3, x3, HEAP, lsl #32
    // 0xa4ca30: cmp             w3, NULL
    // 0xa4ca34: b.eq            #0xa4cae4
    // 0xa4ca38: LoadField: r4 = r3->field_b
    //     0xa4ca38: ldur            w4, [x3, #0xb]
    // 0xa4ca3c: DecompressPointer r4
    //     0xa4ca3c: add             x4, x4, HEAP, lsl #32
    // 0xa4ca40: stur            x4, [fp, #-0x10]
    // 0xa4ca44: r0 = GetPosition()
    //     0xa4ca44: bl              #0x9d25e0  ; AllocateGetPositionStub -> GetPosition (size=0x1c)
    // 0xa4ca48: mov             x2, x0
    // 0xa4ca4c: r0 = Sentinel
    //     0xa4ca4c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4ca50: stur            x2, [fp, #-0x30]
    // 0xa4ca54: StoreField: r2->field_13 = r0
    //     0xa4ca54: stur            w0, [x2, #0x13]
    // 0xa4ca58: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4ca58: stur            w0, [x2, #0x17]
    // 0xa4ca5c: ldur            x0, [fp, #-0x10]
    // 0xa4ca60: StoreField: r2->field_7 = r0
    //     0xa4ca60: stur            w0, [x2, #7]
    // 0xa4ca64: r0 = Instance_EdgeInsets
    //     0xa4ca64: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4ca68: StoreField: r2->field_b = r0
    //     0xa4ca68: stur            w0, [x2, #0xb]
    // 0xa4ca6c: ldur            x0, [fp, #-0x18]
    // 0xa4ca70: StoreField: r2->field_f = r0
    //     0xa4ca70: stur            w0, [x2, #0xf]
    // 0xa4ca74: mov             x1, x2
    // 0xa4ca78: r0 = getRenderBox()
    //     0xa4ca78: bl              #0x9d2460  ; [package:showcaseview/src/get_position.dart] GetPosition::getRenderBox
    // 0xa4ca7c: ldur            x0, [fp, #-0x30]
    // 0xa4ca80: ldur            x1, [fp, #-0x20]
    // 0xa4ca84: StoreField: r1->field_27 = r0
    //     0xa4ca84: stur            w0, [x1, #0x27]
    //     0xa4ca88: ldurb           w16, [x1, #-1]
    //     0xa4ca8c: ldurb           w17, [x0, #-1]
    //     0xa4ca90: and             x16, x17, x16, lsr #2
    //     0xa4ca94: tst             x16, HEAP, lsr #32
    //     0xa4ca98: b.eq            #0xa4caa0
    //     0xa4ca9c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa4caa0: ldur            x0, [fp, #-8]
    // 0xa4caa4: LoadField: r2 = r0->field_f
    //     0xa4caa4: ldur            w2, [x0, #0xf]
    // 0xa4caa8: DecompressPointer r2
    //     0xa4caa8: add             x2, x2, HEAP, lsl #32
    // 0xa4caac: ldr             x1, [fp, #0x18]
    // 0xa4cab0: stur            x2, [fp, #-0x10]
    // 0xa4cab4: r0 = size()
    //     0xa4cab4: bl              #0x684980  ; [dart:ui] Rect::size
    // 0xa4cab8: ldur            x1, [fp, #-0x10]
    // 0xa4cabc: ldr             x2, [fp, #0x10]
    // 0xa4cac0: mov             x3, x0
    // 0xa4cac4: ldr             x5, [fp, #0x18]
    // 0xa4cac8: ldur            x6, [fp, #-0x28]
    // 0xa4cacc: r0 = buildOverlayOnTarget()
    //     0xa4cacc: bl              #0xa4cae8  ; [package:showcaseview/src/showcase.dart] _ShowcaseState::buildOverlayOnTarget
    // 0xa4cad0: LeaveFrame
    //     0xa4cad0: mov             SP, fp
    //     0xa4cad4: ldp             fp, lr, [SP], #0x10
    // 0xa4cad8: ret
    //     0xa4cad8: ret             
    // 0xa4cadc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4cadc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4cae0: b               #0xa4c9d0
    // 0xa4cae4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4cae4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ buildOverlayOnTarget(/* No info */) {
    // ** addr: 0xa4cae8, size: 0x6b8
    // 0xa4cae8: EnterFrame
    //     0xa4cae8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4caec: mov             fp, SP
    // 0xa4caf0: AllocStack(0x90)
    //     0xa4caf0: sub             SP, SP, #0x90
    // 0xa4caf4: SetupParameters(_ShowcaseState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0xa4caf4: stur            x1, [fp, #-8]
    //     0xa4caf8: stur            x2, [fp, #-0x10]
    //     0xa4cafc: stur            x3, [fp, #-0x18]
    //     0xa4cb00: stur            x5, [fp, #-0x20]
    //     0xa4cb04: stur            x6, [fp, #-0x28]
    // 0xa4cb08: CheckStackOverflow
    //     0xa4cb08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4cb0c: cmp             SP, x16
    //     0xa4cb10: b.ls            #0xa4d114
    // 0xa4cb14: r1 = 1
    //     0xa4cb14: movz            x1, #0x1
    // 0xa4cb18: r0 = AllocateContext()
    //     0xa4cb18: bl              #0xec126c  ; AllocateContextStub
    // 0xa4cb1c: mov             x2, x0
    // 0xa4cb20: ldur            x0, [fp, #-8]
    // 0xa4cb24: stur            x2, [fp, #-0x30]
    // 0xa4cb28: StoreField: r2->field_f = r0
    //     0xa4cb28: stur            w0, [x2, #0xf]
    // 0xa4cb2c: LoadField: r1 = r0->field_f
    //     0xa4cb2c: ldur            w1, [x0, #0xf]
    // 0xa4cb30: DecompressPointer r1
    //     0xa4cb30: add             x1, x1, HEAP, lsl #32
    // 0xa4cb34: cmp             w1, NULL
    // 0xa4cb38: b.eq            #0xa4d11c
    // 0xa4cb3c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa4cb3c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa4cb40: r0 = _of()
    //     0xa4cb40: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa4cb44: LoadField: r2 = r0->field_7
    //     0xa4cb44: ldur            w2, [x0, #7]
    // 0xa4cb48: DecompressPointer r2
    //     0xa4cb48: add             x2, x2, HEAP, lsl #32
    // 0xa4cb4c: ldur            x0, [fp, #-8]
    // 0xa4cb50: stur            x2, [fp, #-0x38]
    // 0xa4cb54: LoadField: r1 = r0->field_13
    //     0xa4cb54: ldur            w1, [x0, #0x13]
    // 0xa4cb58: DecompressPointer r1
    //     0xa4cb58: add             x1, x1, HEAP, lsl #32
    // 0xa4cb5c: tbnz            w1, #4, #0xa4cba4
    // 0xa4cb60: LoadField: r1 = r0->field_b
    //     0xa4cb60: ldur            w1, [x0, #0xb]
    // 0xa4cb64: DecompressPointer r1
    //     0xa4cb64: add             x1, x1, HEAP, lsl #32
    // 0xa4cb68: cmp             w1, NULL
    // 0xa4cb6c: b.eq            #0xa4d120
    // 0xa4cb70: mov             x1, x0
    // 0xa4cb74: LoadField: r0 = r1->field_33
    //     0xa4cb74: ldur            w0, [x1, #0x33]
    // 0xa4cb78: DecompressPointer r0
    //     0xa4cb78: add             x0, x0, HEAP, lsl #32
    // 0xa4cb7c: r16 = Sentinel
    //     0xa4cb7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4cb80: cmp             w0, w16
    // 0xa4cb84: b.ne            #0xa4cb94
    // 0xa4cb88: r2 = showCaseWidgetState
    //     0xa4cb88: add             x2, PP, #0x47, lsl #12  ; [pp+0x47d10] Field <<EMAIL>>: late final (offset: 0x34)
    //     0xa4cb8c: ldr             x2, [x2, #0xd10]
    // 0xa4cb90: r0 = InitLateFinalInstanceField()
    //     0xa4cb90: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa4cb94: LoadField: r1 = r0->field_b
    //     0xa4cb94: ldur            w1, [x0, #0xb]
    // 0xa4cb98: DecompressPointer r1
    //     0xa4cb98: add             x1, x1, HEAP, lsl #32
    // 0xa4cb9c: cmp             w1, NULL
    // 0xa4cba0: b.eq            #0xa4d124
    // 0xa4cba4: ldur            x2, [fp, #-8]
    // 0xa4cba8: LoadField: r0 = r2->field_13
    //     0xa4cba8: ldur            w0, [x2, #0x13]
    // 0xa4cbac: DecompressPointer r0
    //     0xa4cbac: add             x0, x0, HEAP, lsl #32
    // 0xa4cbb0: tbz             w0, #4, #0xa4cbc8
    // 0xa4cbb4: r0 = Instance_Offstage
    //     0xa4cbb4: add             x0, PP, #0x47, lsl #12  ; [pp+0x47d20] Obj!Offstage@e1df41
    //     0xa4cbb8: ldr             x0, [x0, #0xd20]
    // 0xa4cbbc: LeaveFrame
    //     0xa4cbbc: mov             SP, fp
    //     0xa4cbc0: ldp             fp, lr, [SP], #0x10
    // 0xa4cbc4: ret
    //     0xa4cbc4: ret             
    // 0xa4cbc8: ldur            x0, [fp, #-0x20]
    // 0xa4cbcc: LoadField: r1 = r2->field_b
    //     0xa4cbcc: ldur            w1, [x2, #0xb]
    // 0xa4cbd0: DecompressPointer r1
    //     0xa4cbd0: add             x1, x1, HEAP, lsl #32
    // 0xa4cbd4: cmp             w1, NULL
    // 0xa4cbd8: b.eq            #0xa4d128
    // 0xa4cbdc: r1 = <Path>
    //     0xa4cbdc: add             x1, PP, #0x26, lsl #12  ; [pp+0x26030] TypeArguments: <Path>
    //     0xa4cbe0: ldr             x1, [x1, #0x30]
    // 0xa4cbe4: r0 = RRectClipper()
    //     0xa4cbe4: bl              #0xa4d1b8  ; AllocateRRectClipperStub -> RRectClipper (size=0x20)
    // 0xa4cbe8: mov             x1, x0
    // 0xa4cbec: r0 = false
    //     0xa4cbec: add             x0, NULL, #0x30  ; false
    // 0xa4cbf0: stur            x1, [fp, #-0x40]
    // 0xa4cbf4: StoreField: r1->field_f = r0
    //     0xa4cbf4: stur            w0, [x1, #0xf]
    // 0xa4cbf8: r2 = Instance_EdgeInsets
    //     0xa4cbf8: ldr             x2, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4cbfc: ArrayStore: r1[0] = r2  ; List_4
    //     0xa4cbfc: stur            w2, [x1, #0x17]
    // 0xa4cc00: ldur            x3, [fp, #-0x20]
    // 0xa4cc04: StoreField: r1->field_1b = r3
    //     0xa4cc04: stur            w3, [x1, #0x1b]
    // 0xa4cc08: d0 = 0.000000
    //     0xa4cc08: eor             v0.16b, v0.16b, v0.16b
    // 0xa4cc0c: fcmp            d0, d0
    // 0xa4cc10: b.eq            #0xa4cd48
    // 0xa4cc14: ldur            x4, [fp, #-0x38]
    // 0xa4cc18: r0 = _GaussianBlurImageFilter()
    //     0xa4cc18: bl              #0x7e5b8c  ; Allocate_GaussianBlurImageFilterStub -> _GaussianBlurImageFilter (size=0x20)
    // 0xa4cc1c: mov             x2, x0
    // 0xa4cc20: r0 = Sentinel
    //     0xa4cc20: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4cc24: stur            x2, [fp, #-0x48]
    // 0xa4cc28: StoreField: r2->field_1b = r0
    //     0xa4cc28: stur            w0, [x2, #0x1b]
    // 0xa4cc2c: StoreField: r2->field_7 = rZR
    //     0xa4cc2c: stur            xzr, [x2, #7]
    // 0xa4cc30: StoreField: r2->field_f = rZR
    //     0xa4cc30: stur            xzr, [x2, #0xf]
    // 0xa4cc34: r0 = Instance_TileMode
    //     0xa4cc34: add             x0, PP, #0x26, lsl #12  ; [pp+0x263e8] Obj!TileMode@e399a1
    //     0xa4cc38: ldr             x0, [x0, #0x3e8]
    // 0xa4cc3c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4cc3c: stur            w0, [x2, #0x17]
    // 0xa4cc40: ldur            x0, [fp, #-0x38]
    // 0xa4cc44: LoadField: d1 = r0->field_7
    //     0xa4cc44: ldur            d1, [x0, #7]
    // 0xa4cc48: stur            d1, [fp, #-0x78]
    // 0xa4cc4c: LoadField: d2 = r0->field_f
    //     0xa4cc4c: ldur            d2, [x0, #0xf]
    // 0xa4cc50: stur            d2, [fp, #-0x70]
    // 0xa4cc54: r1 = Instance_Color
    //     0xa4cc54: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d218] Obj!Color@e28bc1
    //     0xa4cc58: ldr             x1, [x1, #0x218]
    // 0xa4cc5c: d0 = 0.750000
    //     0xa4cc5c: fmov            d0, #0.75000000
    // 0xa4cc60: r0 = withOpacity()
    //     0xa4cc60: bl              #0xd72290  ; [dart:ui] Color::withOpacity
    // 0xa4cc64: stur            x0, [fp, #-0x50]
    // 0xa4cc68: r0 = BoxDecoration()
    //     0xa4cc68: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa4cc6c: mov             x1, x0
    // 0xa4cc70: ldur            x0, [fp, #-0x50]
    // 0xa4cc74: stur            x1, [fp, #-0x60]
    // 0xa4cc78: StoreField: r1->field_7 = r0
    //     0xa4cc78: stur            w0, [x1, #7]
    // 0xa4cc7c: r2 = Instance_BoxShape
    //     0xa4cc7c: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa4cc80: ldr             x2, [x2, #0xca8]
    // 0xa4cc84: StoreField: r1->field_23 = r2
    //     0xa4cc84: stur            w2, [x1, #0x23]
    // 0xa4cc88: ldur            d0, [fp, #-0x78]
    // 0xa4cc8c: r0 = inline_Allocate_Double()
    //     0xa4cc8c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa4cc90: add             x0, x0, #0x10
    //     0xa4cc94: cmp             x2, x0
    //     0xa4cc98: b.ls            #0xa4d12c
    //     0xa4cc9c: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4cca0: sub             x0, x0, #0xf
    //     0xa4cca4: movz            x2, #0xe15c
    //     0xa4cca8: movk            x2, #0x3, lsl #16
    //     0xa4ccac: stur            x2, [x0, #-1]
    // 0xa4ccb0: StoreField: r0->field_7 = d0
    //     0xa4ccb0: stur            d0, [x0, #7]
    // 0xa4ccb4: ldur            d0, [fp, #-0x70]
    // 0xa4ccb8: stur            x0, [fp, #-0x58]
    // 0xa4ccbc: r2 = inline_Allocate_Double()
    //     0xa4ccbc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xa4ccc0: add             x2, x2, #0x10
    //     0xa4ccc4: cmp             x3, x2
    //     0xa4ccc8: b.ls            #0xa4d144
    //     0xa4cccc: str             x2, [THR, #0x50]  ; THR::top
    //     0xa4ccd0: sub             x2, x2, #0xf
    //     0xa4ccd4: movz            x3, #0xe15c
    //     0xa4ccd8: movk            x3, #0x3, lsl #16
    //     0xa4ccdc: stur            x3, [x2, #-1]
    // 0xa4cce0: StoreField: r2->field_7 = d0
    //     0xa4cce0: stur            d0, [x2, #7]
    // 0xa4cce4: stur            x2, [fp, #-0x50]
    // 0xa4cce8: r0 = Container()
    //     0xa4cce8: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4ccec: stur            x0, [fp, #-0x68]
    // 0xa4ccf0: ldur            x16, [fp, #-0x58]
    // 0xa4ccf4: ldur            lr, [fp, #-0x50]
    // 0xa4ccf8: stp             lr, x16, [SP, #8]
    // 0xa4ccfc: ldur            x16, [fp, #-0x60]
    // 0xa4cd00: str             x16, [SP]
    // 0xa4cd04: mov             x1, x0
    // 0xa4cd08: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xa4cd08: add             x4, PP, #0x35, lsl #12  ; [pp+0x35d78] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xa4cd0c: ldr             x4, [x4, #0xd78]
    // 0xa4cd10: r0 = Container()
    //     0xa4cd10: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4cd14: r0 = BackdropFilter()
    //     0xa4cd14: bl              #0x7e5b38  ; AllocateBackdropFilterStub -> BackdropFilter (size=0x1c)
    // 0xa4cd18: mov             x1, x0
    // 0xa4cd1c: ldur            x0, [fp, #-0x48]
    // 0xa4cd20: StoreField: r1->field_f = r0
    //     0xa4cd20: stur            w0, [x1, #0xf]
    // 0xa4cd24: r0 = Instance_BlendMode
    //     0xa4cd24: add             x0, PP, #0x28, lsl #12  ; [pp+0x28268] Obj!BlendMode@e39cc1
    //     0xa4cd28: ldr             x0, [x0, #0x268]
    // 0xa4cd2c: StoreField: r1->field_13 = r0
    //     0xa4cd2c: stur            w0, [x1, #0x13]
    // 0xa4cd30: r0 = true
    //     0xa4cd30: add             x0, NULL, #0x20  ; true
    // 0xa4cd34: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4cd34: stur            w0, [x1, #0x17]
    // 0xa4cd38: ldur            x0, [fp, #-0x68]
    // 0xa4cd3c: StoreField: r1->field_b = r0
    //     0xa4cd3c: stur            w0, [x1, #0xb]
    // 0xa4cd40: mov             x6, x1
    // 0xa4cd44: b               #0xa4ce28
    // 0xa4cd48: ldur            x0, [fp, #-0x38]
    // 0xa4cd4c: r2 = Instance_BoxShape
    //     0xa4cd4c: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa4cd50: ldr             x2, [x2, #0xca8]
    // 0xa4cd54: LoadField: d1 = r0->field_7
    //     0xa4cd54: ldur            d1, [x0, #7]
    // 0xa4cd58: stur            d1, [fp, #-0x78]
    // 0xa4cd5c: LoadField: d2 = r0->field_f
    //     0xa4cd5c: ldur            d2, [x0, #0xf]
    // 0xa4cd60: stur            d2, [fp, #-0x70]
    // 0xa4cd64: r1 = Instance_Color
    //     0xa4cd64: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d218] Obj!Color@e28bc1
    //     0xa4cd68: ldr             x1, [x1, #0x218]
    // 0xa4cd6c: d0 = 0.750000
    //     0xa4cd6c: fmov            d0, #0.75000000
    // 0xa4cd70: r0 = withOpacity()
    //     0xa4cd70: bl              #0xd72290  ; [dart:ui] Color::withOpacity
    // 0xa4cd74: stur            x0, [fp, #-0x38]
    // 0xa4cd78: r0 = BoxDecoration()
    //     0xa4cd78: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa4cd7c: mov             x1, x0
    // 0xa4cd80: ldur            x0, [fp, #-0x38]
    // 0xa4cd84: stur            x1, [fp, #-0x50]
    // 0xa4cd88: StoreField: r1->field_7 = r0
    //     0xa4cd88: stur            w0, [x1, #7]
    // 0xa4cd8c: r0 = Instance_BoxShape
    //     0xa4cd8c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa4cd90: ldr             x0, [x0, #0xca8]
    // 0xa4cd94: StoreField: r1->field_23 = r0
    //     0xa4cd94: stur            w0, [x1, #0x23]
    // 0xa4cd98: ldur            d0, [fp, #-0x78]
    // 0xa4cd9c: r0 = inline_Allocate_Double()
    //     0xa4cd9c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa4cda0: add             x0, x0, #0x10
    //     0xa4cda4: cmp             x2, x0
    //     0xa4cda8: b.ls            #0xa4d160
    //     0xa4cdac: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4cdb0: sub             x0, x0, #0xf
    //     0xa4cdb4: movz            x2, #0xe15c
    //     0xa4cdb8: movk            x2, #0x3, lsl #16
    //     0xa4cdbc: stur            x2, [x0, #-1]
    // 0xa4cdc0: StoreField: r0->field_7 = d0
    //     0xa4cdc0: stur            d0, [x0, #7]
    // 0xa4cdc4: ldur            d0, [fp, #-0x70]
    // 0xa4cdc8: stur            x0, [fp, #-0x48]
    // 0xa4cdcc: r2 = inline_Allocate_Double()
    //     0xa4cdcc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xa4cdd0: add             x2, x2, #0x10
    //     0xa4cdd4: cmp             x3, x2
    //     0xa4cdd8: b.ls            #0xa4d178
    //     0xa4cddc: str             x2, [THR, #0x50]  ; THR::top
    //     0xa4cde0: sub             x2, x2, #0xf
    //     0xa4cde4: movz            x3, #0xe15c
    //     0xa4cde8: movk            x3, #0x3, lsl #16
    //     0xa4cdec: stur            x3, [x2, #-1]
    // 0xa4cdf0: StoreField: r2->field_7 = d0
    //     0xa4cdf0: stur            d0, [x2, #7]
    // 0xa4cdf4: stur            x2, [fp, #-0x38]
    // 0xa4cdf8: r0 = Container()
    //     0xa4cdf8: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4cdfc: stur            x0, [fp, #-0x58]
    // 0xa4ce00: ldur            x16, [fp, #-0x48]
    // 0xa4ce04: ldur            lr, [fp, #-0x38]
    // 0xa4ce08: stp             lr, x16, [SP, #8]
    // 0xa4ce0c: ldur            x16, [fp, #-0x50]
    // 0xa4ce10: str             x16, [SP]
    // 0xa4ce14: mov             x1, x0
    // 0xa4ce18: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xa4ce18: add             x4, PP, #0x35, lsl #12  ; [pp+0x35d78] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xa4ce1c: ldr             x4, [x4, #0xd78]
    // 0xa4ce20: r0 = Container()
    //     0xa4ce20: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4ce24: ldur            x6, [fp, #-0x58]
    // 0xa4ce28: ldur            x2, [fp, #-8]
    // 0xa4ce2c: ldur            x5, [fp, #-0x10]
    // 0xa4ce30: ldur            x4, [fp, #-0x18]
    // 0xa4ce34: ldur            x1, [fp, #-0x20]
    // 0xa4ce38: ldur            x3, [fp, #-0x28]
    // 0xa4ce3c: ldur            x0, [fp, #-0x40]
    // 0xa4ce40: stur            x6, [fp, #-0x38]
    // 0xa4ce44: r0 = ClipPath()
    //     0xa4ce44: bl              #0x9faf10  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xa4ce48: mov             x1, x0
    // 0xa4ce4c: ldur            x0, [fp, #-0x40]
    // 0xa4ce50: stur            x1, [fp, #-0x48]
    // 0xa4ce54: StoreField: r1->field_f = r0
    //     0xa4ce54: stur            w0, [x1, #0xf]
    // 0xa4ce58: r0 = Instance_Clip
    //     0xa4ce58: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d4f8] Obj!Clip@e39b21
    //     0xa4ce5c: ldr             x0, [x0, #0x4f8]
    // 0xa4ce60: StoreField: r1->field_13 = r0
    //     0xa4ce60: stur            w0, [x1, #0x13]
    // 0xa4ce64: ldur            x0, [fp, #-0x38]
    // 0xa4ce68: StoreField: r1->field_b = r0
    //     0xa4ce68: stur            w0, [x1, #0xb]
    // 0xa4ce6c: r0 = GestureDetector()
    //     0xa4ce6c: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa4ce70: ldur            x2, [fp, #-0x30]
    // 0xa4ce74: r1 = Function '<anonymous closure>':.
    //     0xa4ce74: add             x1, PP, #0x47, lsl #12  ; [pp+0x47d28] AnonymousClosure: (0xa4d71c), in [package:showcaseview/src/showcase.dart] _ShowcaseState::buildOverlayOnTarget (0xa4cae8)
    //     0xa4ce78: ldr             x1, [x1, #0xd28]
    // 0xa4ce7c: stur            x0, [fp, #-0x30]
    // 0xa4ce80: r0 = AllocateClosure()
    //     0xa4ce80: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4ce84: ldur            x16, [fp, #-0x48]
    // 0xa4ce88: stp             x16, x0, [SP]
    // 0xa4ce8c: ldur            x1, [fp, #-0x30]
    // 0xa4ce90: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa4ce90: add             x4, PP, #0x25, lsl #12  ; [pp+0x257d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa4ce94: ldr             x4, [x4, #0x7d0]
    // 0xa4ce98: r0 = GestureDetector()
    //     0xa4ce98: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa4ce9c: r1 = Null
    //     0xa4ce9c: mov             x1, NULL
    // 0xa4cea0: r2 = 2
    //     0xa4cea0: movz            x2, #0x2
    // 0xa4cea4: r0 = AllocateArray()
    //     0xa4cea4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa4cea8: mov             x2, x0
    // 0xa4ceac: ldur            x0, [fp, #-0x30]
    // 0xa4ceb0: stur            x2, [fp, #-0x38]
    // 0xa4ceb4: StoreField: r2->field_f = r0
    //     0xa4ceb4: stur            w0, [x2, #0xf]
    // 0xa4ceb8: r1 = <Widget>
    //     0xa4ceb8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa4cebc: r0 = AllocateGrowableArray()
    //     0xa4cebc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa4cec0: mov             x1, x0
    // 0xa4cec4: ldur            x0, [fp, #-0x38]
    // 0xa4cec8: stur            x1, [fp, #-0x30]
    // 0xa4cecc: StoreField: r1->field_f = r0
    //     0xa4cecc: stur            w0, [x1, #0xf]
    // 0xa4ced0: r0 = 2
    //     0xa4ced0: movz            x0, #0x2
    // 0xa4ced4: StoreField: r1->field_b = r0
    //     0xa4ced4: stur            w0, [x1, #0xb]
    // 0xa4ced8: ldur            x0, [fp, #-0x20]
    // 0xa4cedc: LoadField: d0 = r0->field_7
    //     0xa4cedc: ldur            d0, [x0, #7]
    // 0xa4cee0: stur            d0, [fp, #-0x78]
    // 0xa4cee4: LoadField: d1 = r0->field_f
    //     0xa4cee4: ldur            d1, [x0, #0xf]
    // 0xa4cee8: stur            d1, [fp, #-0x70]
    // 0xa4ceec: r0 = Offset()
    //     0xa4ceec: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xa4cef0: ldur            d0, [fp, #-0x78]
    // 0xa4cef4: stur            x0, [fp, #-0x38]
    // 0xa4cef8: StoreField: r0->field_7 = d0
    //     0xa4cef8: stur            d0, [x0, #7]
    // 0xa4cefc: ldur            d0, [fp, #-0x70]
    // 0xa4cf00: StoreField: r0->field_f = d0
    //     0xa4cf00: stur            d0, [x0, #0xf]
    // 0xa4cf04: ldur            x2, [fp, #-8]
    // 0xa4cf08: LoadField: r1 = r2->field_b
    //     0xa4cf08: ldur            w1, [x2, #0xb]
    // 0xa4cf0c: DecompressPointer r1
    //     0xa4cf0c: add             x1, x1, HEAP, lsl #32
    // 0xa4cf10: stur            x1, [fp, #-0x20]
    // 0xa4cf14: cmp             w1, NULL
    // 0xa4cf18: b.eq            #0xa4d194
    // 0xa4cf1c: r0 = _TargetWidget()
    //     0xa4cf1c: bl              #0xa4d1ac  ; Allocate_TargetWidgetStub -> _TargetWidget (size=0x30)
    // 0xa4cf20: mov             x3, x0
    // 0xa4cf24: ldur            x0, [fp, #-0x38]
    // 0xa4cf28: stur            x3, [fp, #-0x40]
    // 0xa4cf2c: StoreField: r3->field_b = r0
    //     0xa4cf2c: stur            w0, [x3, #0xb]
    // 0xa4cf30: ldur            x0, [fp, #-0x18]
    // 0xa4cf34: StoreField: r3->field_f = r0
    //     0xa4cf34: stur            w0, [x3, #0xf]
    // 0xa4cf38: r0 = Instance_RoundedRectangleBorder
    //     0xa4cf38: ldr             x0, [PP, #0x5700]  ; [pp+0x5700] Obj!RoundedRectangleBorder@e14681
    // 0xa4cf3c: StoreField: r3->field_1f = r0
    //     0xa4cf3c: stur            w0, [x3, #0x1f]
    // 0xa4cf40: r0 = Instance_EdgeInsets
    //     0xa4cf40: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4cf44: StoreField: r3->field_2b = r0
    //     0xa4cf44: stur            w0, [x3, #0x2b]
    // 0xa4cf48: ldur            x2, [fp, #-8]
    // 0xa4cf4c: r1 = Function '_getOnTargetTap@1942245815':.
    //     0xa4cf4c: add             x1, PP, #0x47, lsl #12  ; [pp+0x47d30] AnonymousClosure: (0xa4d24c), in [package:showcaseview/src/showcase.dart] _ShowcaseState::_getOnTargetTap (0xa4d284)
    //     0xa4cf50: ldr             x1, [x1, #0xd30]
    // 0xa4cf54: r0 = AllocateClosure()
    //     0xa4cf54: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4cf58: mov             x1, x0
    // 0xa4cf5c: ldur            x0, [fp, #-0x40]
    // 0xa4cf60: StoreField: r0->field_13 = r1
    //     0xa4cf60: stur            w1, [x0, #0x13]
    // 0xa4cf64: r2 = false
    //     0xa4cf64: add             x2, NULL, #0x30  ; false
    // 0xa4cf68: StoreField: r0->field_27 = r2
    //     0xa4cf68: stur            w2, [x0, #0x27]
    // 0xa4cf6c: ldur            x3, [fp, #-8]
    // 0xa4cf70: LoadField: r4 = r3->field_27
    //     0xa4cf70: ldur            w4, [x3, #0x27]
    // 0xa4cf74: DecompressPointer r4
    //     0xa4cf74: add             x4, x4, HEAP, lsl #32
    // 0xa4cf78: ldur            x1, [fp, #-0x20]
    // 0xa4cf7c: stur            x4, [fp, #-0x38]
    // 0xa4cf80: LoadField: r5 = r1->field_3f
    //     0xa4cf80: ldur            w5, [x1, #0x3f]
    // 0xa4cf84: DecompressPointer r5
    //     0xa4cf84: add             x5, x5, HEAP, lsl #32
    // 0xa4cf88: stur            x5, [fp, #-0x18]
    // 0xa4cf8c: LoadField: d0 = r1->field_57
    //     0xa4cf8c: ldur            d0, [x1, #0x57]
    // 0xa4cf90: mov             x1, x3
    // 0xa4cf94: stur            d0, [fp, #-0x70]
    // 0xa4cf98: LoadField: r0 = r1->field_33
    //     0xa4cf98: ldur            w0, [x1, #0x33]
    // 0xa4cf9c: DecompressPointer r0
    //     0xa4cf9c: add             x0, x0, HEAP, lsl #32
    // 0xa4cfa0: r16 = Sentinel
    //     0xa4cfa0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4cfa4: cmp             w0, w16
    // 0xa4cfa8: b.ne            #0xa4cfb8
    // 0xa4cfac: r2 = showCaseWidgetState
    //     0xa4cfac: add             x2, PP, #0x47, lsl #12  ; [pp+0x47d10] Field <<EMAIL>>: late final (offset: 0x34)
    //     0xa4cfb0: ldr             x2, [x2, #0xd10]
    // 0xa4cfb4: r0 = InitLateFinalInstanceField()
    //     0xa4cfb4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa4cfb8: LoadField: r1 = r0->field_b
    //     0xa4cfb8: ldur            w1, [x0, #0xb]
    // 0xa4cfbc: DecompressPointer r1
    //     0xa4cfbc: add             x1, x1, HEAP, lsl #32
    // 0xa4cfc0: cmp             w1, NULL
    // 0xa4cfc4: b.eq            #0xa4d198
    // 0xa4cfc8: ldur            x2, [fp, #-8]
    // 0xa4cfcc: LoadField: r0 = r2->field_b
    //     0xa4cfcc: ldur            w0, [x2, #0xb]
    // 0xa4cfd0: DecompressPointer r0
    //     0xa4cfd0: add             x0, x0, HEAP, lsl #32
    // 0xa4cfd4: cmp             w0, NULL
    // 0xa4cfd8: b.eq            #0xa4d19c
    // 0xa4cfdc: LoadField: r0 = r2->field_1b
    //     0xa4cfdc: ldur            w0, [x2, #0x1b]
    // 0xa4cfe0: DecompressPointer r0
    //     0xa4cfe0: add             x0, x0, HEAP, lsl #32
    // 0xa4cfe4: stur            x0, [fp, #-0x20]
    // 0xa4cfe8: r0 = ToolTipWidget()
    //     0xa4cfe8: bl              #0xa4d1a0  ; AllocateToolTipWidgetStub -> ToolTipWidget (size=0x6c)
    // 0xa4cfec: mov             x3, x0
    // 0xa4cff0: ldur            x0, [fp, #-0x38]
    // 0xa4cff4: stur            x3, [fp, #-0x48]
    // 0xa4cff8: StoreField: r3->field_b = r0
    //     0xa4cff8: stur            w0, [x3, #0xb]
    // 0xa4cffc: ldur            x0, [fp, #-0x10]
    // 0xa4d000: StoreField: r3->field_f = r0
    //     0xa4d000: stur            w0, [x3, #0xf]
    // 0xa4d004: ldur            x0, [fp, #-0x28]
    // 0xa4d008: StoreField: r3->field_13 = r0
    //     0xa4d008: stur            w0, [x3, #0x13]
    // 0xa4d00c: ldur            x0, [fp, #-0x18]
    // 0xa4d010: StoreField: r3->field_27 = r0
    //     0xa4d010: stur            w0, [x3, #0x27]
    // 0xa4d014: r0 = Instance_Color
    //     0xa4d014: ldr             x0, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0xa4d018: StoreField: r3->field_2b = r0
    //     0xa4d018: stur            w0, [x3, #0x2b]
    // 0xa4d01c: r0 = false
    //     0xa4d01c: add             x0, NULL, #0x30  ; false
    // 0xa4d020: StoreField: r3->field_2f = r0
    //     0xa4d020: stur            w0, [x3, #0x2f]
    // 0xa4d024: d0 = 50.000000
    //     0xa4d024: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0xa4d028: StoreField: r3->field_33 = d0
    //     0xa4d028: stur            d0, [x3, #0x33]
    // 0xa4d02c: ldur            d0, [fp, #-0x70]
    // 0xa4d030: StoreField: r3->field_3b = d0
    //     0xa4d030: stur            d0, [x3, #0x3b]
    // 0xa4d034: ldur            x2, [fp, #-8]
    // 0xa4d038: r1 = Function '_getOnTooltipTap@1942245815':.
    //     0xa4d038: add             x1, PP, #0x47, lsl #12  ; [pp+0x47d38] AnonymousClosure: (0xa4d1c4), in [package:showcaseview/src/showcase.dart] _ShowcaseState::_getOnTooltipTap (0xa4d1fc)
    //     0xa4d03c: ldr             x1, [x1, #0xd38]
    // 0xa4d040: r0 = AllocateClosure()
    //     0xa4d040: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4d044: mov             x1, x0
    // 0xa4d048: ldur            x0, [fp, #-0x48]
    // 0xa4d04c: StoreField: r0->field_43 = r1
    //     0xa4d04c: stur            w1, [x0, #0x43]
    // 0xa4d050: r1 = Instance_Duration
    //     0xa4d050: add             x1, PP, #0x28, lsl #12  ; [pp+0x28130] Obj!Duration@e3a131
    //     0xa4d054: ldr             x1, [x1, #0x130]
    // 0xa4d058: StoreField: r0->field_47 = r1
    //     0xa4d058: stur            w1, [x0, #0x47]
    // 0xa4d05c: r1 = false
    //     0xa4d05c: add             x1, NULL, #0x30  ; false
    // 0xa4d060: StoreField: r0->field_4b = r1
    //     0xa4d060: stur            w1, [x0, #0x4b]
    // 0xa4d064: StoreField: r0->field_4f = r1
    //     0xa4d064: stur            w1, [x0, #0x4f]
    // 0xa4d068: r1 = Instance_Duration
    //     0xa4d068: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xa4d06c: ldr             x1, [x1, #0x9c0]
    // 0xa4d070: StoreField: r0->field_53 = r1
    //     0xa4d070: stur            w1, [x0, #0x53]
    // 0xa4d074: r1 = Instance__DecelerateCurve
    //     0xa4d074: ldr             x1, [PP, #0x4e48]  ; [pp+0x4e48] Obj!_DecelerateCurve@e14cc1
    // 0xa4d078: StoreField: r0->field_57 = r1
    //     0xa4d078: stur            w1, [x0, #0x57]
    // 0xa4d07c: ldur            x1, [fp, #-0x20]
    // 0xa4d080: StoreField: r0->field_5b = r1
    //     0xa4d080: stur            w1, [x0, #0x5b]
    // 0xa4d084: d0 = 7.000000
    //     0xa4d084: fmov            d0, #7.00000000
    // 0xa4d088: StoreField: r0->field_63 = d0
    //     0xa4d088: stur            d0, [x0, #0x63]
    // 0xa4d08c: r1 = Null
    //     0xa4d08c: mov             x1, NULL
    // 0xa4d090: r2 = 4
    //     0xa4d090: movz            x2, #0x4
    // 0xa4d094: r0 = AllocateArray()
    //     0xa4d094: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa4d098: mov             x2, x0
    // 0xa4d09c: ldur            x0, [fp, #-0x40]
    // 0xa4d0a0: stur            x2, [fp, #-8]
    // 0xa4d0a4: StoreField: r2->field_f = r0
    //     0xa4d0a4: stur            w0, [x2, #0xf]
    // 0xa4d0a8: ldur            x0, [fp, #-0x48]
    // 0xa4d0ac: StoreField: r2->field_13 = r0
    //     0xa4d0ac: stur            w0, [x2, #0x13]
    // 0xa4d0b0: r1 = <Widget>
    //     0xa4d0b0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa4d0b4: r0 = AllocateGrowableArray()
    //     0xa4d0b4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa4d0b8: mov             x1, x0
    // 0xa4d0bc: ldur            x0, [fp, #-8]
    // 0xa4d0c0: StoreField: r1->field_f = r0
    //     0xa4d0c0: stur            w0, [x1, #0xf]
    // 0xa4d0c4: r0 = 4
    //     0xa4d0c4: movz            x0, #0x4
    // 0xa4d0c8: StoreField: r1->field_b = r0
    //     0xa4d0c8: stur            w0, [x1, #0xb]
    // 0xa4d0cc: mov             x2, x1
    // 0xa4d0d0: ldur            x1, [fp, #-0x30]
    // 0xa4d0d4: r0 = addAll()
    //     0xa4d0d4: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xa4d0d8: r0 = Stack()
    //     0xa4d0d8: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa4d0dc: r1 = Instance_AlignmentDirectional
    //     0xa4d0dc: add             x1, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0xa4d0e0: ldr             x1, [x1, #0x7b0]
    // 0xa4d0e4: StoreField: r0->field_f = r1
    //     0xa4d0e4: stur            w1, [x0, #0xf]
    // 0xa4d0e8: r1 = Instance_StackFit
    //     0xa4d0e8: add             x1, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xa4d0ec: ldr             x1, [x1, #0x7b8]
    // 0xa4d0f0: ArrayStore: r0[0] = r1  ; List_4
    //     0xa4d0f0: stur            w1, [x0, #0x17]
    // 0xa4d0f4: r1 = Instance_Clip
    //     0xa4d0f4: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa4d0f8: ldr             x1, [x1, #0x7c0]
    // 0xa4d0fc: StoreField: r0->field_1b = r1
    //     0xa4d0fc: stur            w1, [x0, #0x1b]
    // 0xa4d100: ldur            x1, [fp, #-0x30]
    // 0xa4d104: StoreField: r0->field_b = r1
    //     0xa4d104: stur            w1, [x0, #0xb]
    // 0xa4d108: LeaveFrame
    //     0xa4d108: mov             SP, fp
    //     0xa4d10c: ldp             fp, lr, [SP], #0x10
    // 0xa4d110: ret
    //     0xa4d110: ret             
    // 0xa4d114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d118: b               #0xa4cb14
    // 0xa4d11c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d11c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d120: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d120: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d124: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d124: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d128: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d128: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d12c: SaveReg d0
    //     0xa4d12c: str             q0, [SP, #-0x10]!
    // 0xa4d130: SaveReg r1
    //     0xa4d130: str             x1, [SP, #-8]!
    // 0xa4d134: r0 = AllocateDouble()
    //     0xa4d134: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4d138: RestoreReg r1
    //     0xa4d138: ldr             x1, [SP], #8
    // 0xa4d13c: RestoreReg d0
    //     0xa4d13c: ldr             q0, [SP], #0x10
    // 0xa4d140: b               #0xa4ccb0
    // 0xa4d144: SaveReg d0
    //     0xa4d144: str             q0, [SP, #-0x10]!
    // 0xa4d148: stp             x0, x1, [SP, #-0x10]!
    // 0xa4d14c: r0 = AllocateDouble()
    //     0xa4d14c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4d150: mov             x2, x0
    // 0xa4d154: ldp             x0, x1, [SP], #0x10
    // 0xa4d158: RestoreReg d0
    //     0xa4d158: ldr             q0, [SP], #0x10
    // 0xa4d15c: b               #0xa4cce0
    // 0xa4d160: SaveReg d0
    //     0xa4d160: str             q0, [SP, #-0x10]!
    // 0xa4d164: SaveReg r1
    //     0xa4d164: str             x1, [SP, #-8]!
    // 0xa4d168: r0 = AllocateDouble()
    //     0xa4d168: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4d16c: RestoreReg r1
    //     0xa4d16c: ldr             x1, [SP], #8
    // 0xa4d170: RestoreReg d0
    //     0xa4d170: ldr             q0, [SP], #0x10
    // 0xa4d174: b               #0xa4cdc0
    // 0xa4d178: SaveReg d0
    //     0xa4d178: str             q0, [SP, #-0x10]!
    // 0xa4d17c: stp             x0, x1, [SP, #-0x10]!
    // 0xa4d180: r0 = AllocateDouble()
    //     0xa4d180: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4d184: mov             x2, x0
    // 0xa4d188: ldp             x0, x1, [SP], #0x10
    // 0xa4d18c: RestoreReg d0
    //     0xa4d18c: ldr             q0, [SP], #0x10
    // 0xa4d190: b               #0xa4cdf0
    // 0xa4d194: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d194: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d198: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d198: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d19c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d19c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> _getOnTooltipTap(dynamic) {
    // ** addr: 0xa4d1c4, size: 0x38
    // 0xa4d1c4: EnterFrame
    //     0xa4d1c4: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d1c8: mov             fp, SP
    // 0xa4d1cc: ldr             x0, [fp, #0x10]
    // 0xa4d1d0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4d1d0: ldur            w1, [x0, #0x17]
    // 0xa4d1d4: DecompressPointer r1
    //     0xa4d1d4: add             x1, x1, HEAP, lsl #32
    // 0xa4d1d8: CheckStackOverflow
    //     0xa4d1d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d1dc: cmp             SP, x16
    //     0xa4d1e0: b.ls            #0xa4d1f4
    // 0xa4d1e4: r0 = _getOnTooltipTap()
    //     0xa4d1e4: bl              #0xa4d1fc  ; [package:showcaseview/src/showcase.dart] _ShowcaseState::_getOnTooltipTap
    // 0xa4d1e8: LeaveFrame
    //     0xa4d1e8: mov             SP, fp
    //     0xa4d1ec: ldp             fp, lr, [SP], #0x10
    // 0xa4d1f0: ret
    //     0xa4d1f0: ret             
    // 0xa4d1f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d1f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d1f8: b               #0xa4d1e4
  }
  _ _getOnTooltipTap(/* No info */) async {
    // ** addr: 0xa4d1fc, size: 0x50
    // 0xa4d1fc: EnterFrame
    //     0xa4d1fc: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d200: mov             fp, SP
    // 0xa4d204: AllocStack(0x10)
    //     0xa4d204: sub             SP, SP, #0x10
    // 0xa4d208: SetupParameters(_ShowcaseState this /* r1 => r1, fp-0x10 */)
    //     0xa4d208: stur            NULL, [fp, #-8]
    //     0xa4d20c: stur            x1, [fp, #-0x10]
    // 0xa4d210: CheckStackOverflow
    //     0xa4d210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d214: cmp             SP, x16
    //     0xa4d218: b.ls            #0xa4d240
    // 0xa4d21c: InitAsync() -> Future<void?>
    //     0xa4d21c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa4d220: bl              #0x661298  ; InitAsyncStub
    // 0xa4d224: ldur            x1, [fp, #-0x10]
    // 0xa4d228: LoadField: r2 = r1->field_b
    //     0xa4d228: ldur            w2, [x1, #0xb]
    // 0xa4d22c: DecompressPointer r2
    //     0xa4d22c: add             x2, x2, HEAP, lsl #32
    // 0xa4d230: cmp             w2, NULL
    // 0xa4d234: b.eq            #0xa4d248
    // 0xa4d238: r0 = Null
    //     0xa4d238: mov             x0, NULL
    // 0xa4d23c: r0 = ReturnAsyncNotFuture()
    //     0xa4d23c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa4d240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d244: b               #0xa4d21c
    // 0xa4d248: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d248: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> _getOnTargetTap(dynamic) {
    // ** addr: 0xa4d24c, size: 0x38
    // 0xa4d24c: EnterFrame
    //     0xa4d24c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d250: mov             fp, SP
    // 0xa4d254: ldr             x0, [fp, #0x10]
    // 0xa4d258: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4d258: ldur            w1, [x0, #0x17]
    // 0xa4d25c: DecompressPointer r1
    //     0xa4d25c: add             x1, x1, HEAP, lsl #32
    // 0xa4d260: CheckStackOverflow
    //     0xa4d260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d264: cmp             SP, x16
    //     0xa4d268: b.ls            #0xa4d27c
    // 0xa4d26c: r0 = _getOnTargetTap()
    //     0xa4d26c: bl              #0xa4d284  ; [package:showcaseview/src/showcase.dart] _ShowcaseState::_getOnTargetTap
    // 0xa4d270: LeaveFrame
    //     0xa4d270: mov             SP, fp
    //     0xa4d274: ldp             fp, lr, [SP], #0x10
    // 0xa4d278: ret
    //     0xa4d278: ret             
    // 0xa4d27c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d27c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d280: b               #0xa4d26c
  }
  _ _getOnTargetTap(/* No info */) async {
    // ** addr: 0xa4d284, size: 0x54
    // 0xa4d284: EnterFrame
    //     0xa4d284: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d288: mov             fp, SP
    // 0xa4d28c: AllocStack(0x10)
    //     0xa4d28c: sub             SP, SP, #0x10
    // 0xa4d290: SetupParameters(_ShowcaseState this /* r1 => r1, fp-0x10 */)
    //     0xa4d290: stur            NULL, [fp, #-8]
    //     0xa4d294: stur            x1, [fp, #-0x10]
    // 0xa4d298: CheckStackOverflow
    //     0xa4d298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d29c: cmp             SP, x16
    //     0xa4d2a0: b.ls            #0xa4d2cc
    // 0xa4d2a4: InitAsync() -> Future<void?>
    //     0xa4d2a4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa4d2a8: bl              #0x661298  ; InitAsyncStub
    // 0xa4d2ac: ldur            x1, [fp, #-0x10]
    // 0xa4d2b0: LoadField: r0 = r1->field_b
    //     0xa4d2b0: ldur            w0, [x1, #0xb]
    // 0xa4d2b4: DecompressPointer r0
    //     0xa4d2b4: add             x0, x0, HEAP, lsl #32
    // 0xa4d2b8: cmp             w0, NULL
    // 0xa4d2bc: b.eq            #0xa4d2d4
    // 0xa4d2c0: r0 = _nextIfAny()
    //     0xa4d2c0: bl              #0xa4d2d8  ; [package:showcaseview/src/showcase.dart] _ShowcaseState::_nextIfAny
    // 0xa4d2c4: r0 = Null
    //     0xa4d2c4: mov             x0, NULL
    // 0xa4d2c8: r0 = ReturnAsyncNotFuture()
    //     0xa4d2c8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa4d2cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d2cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d2d0: b               #0xa4d2a4
    // 0xa4d2d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d2d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _nextIfAny(/* No info */) async {
    // ** addr: 0xa4d2d8, size: 0xf4
    // 0xa4d2d8: EnterFrame
    //     0xa4d2d8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d2dc: mov             fp, SP
    // 0xa4d2e0: AllocStack(0x18)
    //     0xa4d2e0: sub             SP, SP, #0x18
    // 0xa4d2e4: SetupParameters(_ShowcaseState this /* r1 => r1, fp-0x10 */)
    //     0xa4d2e4: stur            NULL, [fp, #-8]
    //     0xa4d2e8: stur            x1, [fp, #-0x10]
    // 0xa4d2ec: CheckStackOverflow
    //     0xa4d2ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d2f0: cmp             SP, x16
    //     0xa4d2f4: b.ls            #0xa4d3c0
    // 0xa4d2f8: InitAsync() -> Future<void?>
    //     0xa4d2f8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa4d2fc: bl              #0x661298  ; InitAsyncStub
    // 0xa4d300: ldur            x1, [fp, #-0x10]
    // 0xa4d304: LoadField: r0 = r1->field_33
    //     0xa4d304: ldur            w0, [x1, #0x33]
    // 0xa4d308: DecompressPointer r0
    //     0xa4d308: add             x0, x0, HEAP, lsl #32
    // 0xa4d30c: r16 = Sentinel
    //     0xa4d30c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4d310: cmp             w0, w16
    // 0xa4d314: b.ne            #0xa4d324
    // 0xa4d318: r2 = showCaseWidgetState
    //     0xa4d318: add             x2, PP, #0x47, lsl #12  ; [pp+0x47d10] Field <<EMAIL>>: late final (offset: 0x34)
    //     0xa4d31c: ldr             x2, [x2, #0xd10]
    // 0xa4d320: r0 = InitLateFinalInstanceField()
    //     0xa4d320: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa4d324: LoadField: r1 = r0->field_13
    //     0xa4d324: ldur            w1, [x0, #0x13]
    // 0xa4d328: DecompressPointer r1
    //     0xa4d328: add             x1, x1, HEAP, lsl #32
    // 0xa4d32c: cmp             w1, NULL
    // 0xa4d330: b.ne            #0xa4d34c
    // 0xa4d334: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4d334: ldur            w1, [x0, #0x17]
    // 0xa4d338: DecompressPointer r1
    //     0xa4d338: add             x1, x1, HEAP, lsl #32
    // 0xa4d33c: cmp             w1, NULL
    // 0xa4d340: b.ne            #0xa4d34c
    // 0xa4d344: r0 = Null
    //     0xa4d344: mov             x0, NULL
    // 0xa4d348: r0 = ReturnAsyncNotFuture()
    //     0xa4d348: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa4d34c: ldur            x0, [fp, #-0x10]
    // 0xa4d350: mov             x1, x0
    // 0xa4d354: r0 = _reverseAnimateTooltip()
    //     0xa4d354: bl              #0xa4d630  ; [package:showcaseview/src/showcase.dart] _ShowcaseState::_reverseAnimateTooltip
    // 0xa4d358: mov             x1, x0
    // 0xa4d35c: stur            x1, [fp, #-0x18]
    // 0xa4d360: r0 = Await()
    //     0xa4d360: bl              #0x661044  ; AwaitStub
    // 0xa4d364: ldur            x0, [fp, #-0x10]
    // 0xa4d368: LoadField: r1 = r0->field_33
    //     0xa4d368: ldur            w1, [x0, #0x33]
    // 0xa4d36c: DecompressPointer r1
    //     0xa4d36c: add             x1, x1, HEAP, lsl #32
    // 0xa4d370: LoadField: r2 = r1->field_13
    //     0xa4d370: ldur            w2, [x1, #0x13]
    // 0xa4d374: DecompressPointer r2
    //     0xa4d374: add             x2, x2, HEAP, lsl #32
    // 0xa4d378: cmp             w2, NULL
    // 0xa4d37c: b.ne            #0xa4d398
    // 0xa4d380: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4d380: ldur            w2, [x1, #0x17]
    // 0xa4d384: DecompressPointer r2
    //     0xa4d384: add             x2, x2, HEAP, lsl #32
    // 0xa4d388: cmp             w2, NULL
    // 0xa4d38c: b.ne            #0xa4d398
    // 0xa4d390: r0 = Null
    //     0xa4d390: mov             x0, NULL
    // 0xa4d394: r0 = ReturnAsyncNotFuture()
    //     0xa4d394: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa4d398: LoadField: r2 = r0->field_b
    //     0xa4d398: ldur            w2, [x0, #0xb]
    // 0xa4d39c: DecompressPointer r2
    //     0xa4d39c: add             x2, x2, HEAP, lsl #32
    // 0xa4d3a0: cmp             w2, NULL
    // 0xa4d3a4: b.eq            #0xa4d3c8
    // 0xa4d3a8: LoadField: r0 = r2->field_b
    //     0xa4d3a8: ldur            w0, [x2, #0xb]
    // 0xa4d3ac: DecompressPointer r0
    //     0xa4d3ac: add             x0, x0, HEAP, lsl #32
    // 0xa4d3b0: mov             x2, x0
    // 0xa4d3b4: r0 = completed()
    //     0xa4d3b4: bl              #0xa4d3cc  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::completed
    // 0xa4d3b8: r0 = Null
    //     0xa4d3b8: mov             x0, NULL
    // 0xa4d3bc: r0 = ReturnAsyncNotFuture()
    //     0xa4d3bc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa4d3c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d3c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d3c4: b               #0xa4d2f8
    // 0xa4d3c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d3c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _reverseAnimateTooltip(/* No info */) async {
    // ** addr: 0xa4d630, size: 0xcc
    // 0xa4d630: EnterFrame
    //     0xa4d630: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d634: mov             fp, SP
    // 0xa4d638: AllocStack(0x20)
    //     0xa4d638: sub             SP, SP, #0x20
    // 0xa4d63c: SetupParameters(_ShowcaseState this /* r1 => r1, fp-0x10 */)
    //     0xa4d63c: stur            NULL, [fp, #-8]
    //     0xa4d640: stur            x1, [fp, #-0x10]
    // 0xa4d644: CheckStackOverflow
    //     0xa4d644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d648: cmp             SP, x16
    //     0xa4d64c: b.ls            #0xa4d6f0
    // 0xa4d650: r1 = 1
    //     0xa4d650: movz            x1, #0x1
    // 0xa4d654: r0 = AllocateContext()
    //     0xa4d654: bl              #0xec126c  ; AllocateContextStub
    // 0xa4d658: mov             x2, x0
    // 0xa4d65c: ldur            x1, [fp, #-0x10]
    // 0xa4d660: stur            x2, [fp, #-0x18]
    // 0xa4d664: StoreField: r2->field_f = r1
    //     0xa4d664: stur            w1, [x2, #0xf]
    // 0xa4d668: InitAsync() -> Future<void?>
    //     0xa4d668: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa4d66c: bl              #0x661298  ; InitAsyncStub
    // 0xa4d670: ldur            x0, [fp, #-0x10]
    // 0xa4d674: LoadField: r1 = r0->field_f
    //     0xa4d674: ldur            w1, [x0, #0xf]
    // 0xa4d678: DecompressPointer r1
    //     0xa4d678: add             x1, x1, HEAP, lsl #32
    // 0xa4d67c: cmp             w1, NULL
    // 0xa4d680: b.ne            #0xa4d68c
    // 0xa4d684: r0 = Null
    //     0xa4d684: mov             x0, NULL
    // 0xa4d688: r0 = ReturnAsyncNotFuture()
    //     0xa4d688: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa4d68c: ldur            x2, [fp, #-0x18]
    // 0xa4d690: r1 = Function '<anonymous closure>':.
    //     0xa4d690: add             x1, PP, #0x47, lsl #12  ; [pp+0x47d40] AnonymousClosure: (0xa4d6fc), in [package:showcaseview/src/showcase.dart] _ShowcaseState::_reverseAnimateTooltip (0xa4d630)
    //     0xa4d694: ldr             x1, [x1, #0xd40]
    // 0xa4d698: r0 = AllocateClosure()
    //     0xa4d698: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4d69c: ldur            x1, [fp, #-0x10]
    // 0xa4d6a0: mov             x2, x0
    // 0xa4d6a4: r0 = setState()
    //     0xa4d6a4: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa4d6a8: ldur            x0, [fp, #-0x10]
    // 0xa4d6ac: LoadField: r1 = r0->field_b
    //     0xa4d6ac: ldur            w1, [x0, #0xb]
    // 0xa4d6b0: DecompressPointer r1
    //     0xa4d6b0: add             x1, x1, HEAP, lsl #32
    // 0xa4d6b4: cmp             w1, NULL
    // 0xa4d6b8: b.eq            #0xa4d6f8
    // 0xa4d6bc: r1 = Null
    //     0xa4d6bc: mov             x1, NULL
    // 0xa4d6c0: r2 = Instance_Duration
    //     0xa4d6c0: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xa4d6c4: ldr             x2, [x2, #0x9c0]
    // 0xa4d6c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa4d6c8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4d6cc: r0 = Future.delayed()
    //     0xa4d6cc: bl              #0x657b70  ; [dart:async] Future::Future.delayed
    // 0xa4d6d0: mov             x1, x0
    // 0xa4d6d4: stur            x1, [fp, #-0x20]
    // 0xa4d6d8: r0 = Await()
    //     0xa4d6d8: bl              #0x661044  ; AwaitStub
    // 0xa4d6dc: ldur            x1, [fp, #-0x10]
    // 0xa4d6e0: r2 = false
    //     0xa4d6e0: add             x2, NULL, #0x30  ; false
    // 0xa4d6e4: StoreField: r1->field_1b = r2
    //     0xa4d6e4: stur            w2, [x1, #0x1b]
    // 0xa4d6e8: r0 = Null
    //     0xa4d6e8: mov             x0, NULL
    // 0xa4d6ec: r0 = ReturnAsyncNotFuture()
    //     0xa4d6ec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa4d6f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d6f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d6f4: b               #0xa4d650
    // 0xa4d6f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d6f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa4d6fc, size: 0x20
    // 0xa4d6fc: r0 = true
    //     0xa4d6fc: add             x0, NULL, #0x20  ; true
    // 0xa4d700: ldr             x1, [SP]
    // 0xa4d704: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4d704: ldur            w2, [x1, #0x17]
    // 0xa4d708: DecompressPointer r2
    //     0xa4d708: add             x2, x2, HEAP, lsl #32
    // 0xa4d70c: LoadField: r1 = r2->field_f
    //     0xa4d70c: ldur            w1, [x2, #0xf]
    // 0xa4d710: DecompressPointer r1
    //     0xa4d710: add             x1, x1, HEAP, lsl #32
    // 0xa4d714: StoreField: r1->field_1b = r0
    //     0xa4d714: stur            w0, [x1, #0x1b]
    // 0xa4d718: ret
    //     0xa4d718: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa4d71c, size: 0xc0
    // 0xa4d71c: EnterFrame
    //     0xa4d71c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d720: mov             fp, SP
    // 0xa4d724: AllocStack(0x8)
    //     0xa4d724: sub             SP, SP, #8
    // 0xa4d728: SetupParameters()
    //     0xa4d728: ldr             x0, [fp, #0x10]
    //     0xa4d72c: ldur            w2, [x0, #0x17]
    //     0xa4d730: add             x2, x2, HEAP, lsl #32
    //     0xa4d734: stur            x2, [fp, #-8]
    // 0xa4d738: CheckStackOverflow
    //     0xa4d738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d73c: cmp             SP, x16
    //     0xa4d740: b.ls            #0xa4d7c8
    // 0xa4d744: LoadField: r1 = r2->field_f
    //     0xa4d744: ldur            w1, [x2, #0xf]
    // 0xa4d748: DecompressPointer r1
    //     0xa4d748: add             x1, x1, HEAP, lsl #32
    // 0xa4d74c: LoadField: r0 = r1->field_33
    //     0xa4d74c: ldur            w0, [x1, #0x33]
    // 0xa4d750: DecompressPointer r0
    //     0xa4d750: add             x0, x0, HEAP, lsl #32
    // 0xa4d754: r16 = Sentinel
    //     0xa4d754: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4d758: cmp             w0, w16
    // 0xa4d75c: b.ne            #0xa4d76c
    // 0xa4d760: r2 = showCaseWidgetState
    //     0xa4d760: add             x2, PP, #0x47, lsl #12  ; [pp+0x47d10] Field <<EMAIL>>: late final (offset: 0x34)
    //     0xa4d764: ldr             x2, [x2, #0xd10]
    // 0xa4d768: r0 = InitLateFinalInstanceField()
    //     0xa4d768: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa4d76c: LoadField: r1 = r0->field_b
    //     0xa4d76c: ldur            w1, [x0, #0xb]
    // 0xa4d770: DecompressPointer r1
    //     0xa4d770: add             x1, x1, HEAP, lsl #32
    // 0xa4d774: cmp             w1, NULL
    // 0xa4d778: b.eq            #0xa4d7d0
    // 0xa4d77c: ldur            x0, [fp, #-8]
    // 0xa4d780: LoadField: r1 = r0->field_f
    //     0xa4d780: ldur            w1, [x0, #0xf]
    // 0xa4d784: DecompressPointer r1
    //     0xa4d784: add             x1, x1, HEAP, lsl #32
    // 0xa4d788: LoadField: r2 = r1->field_b
    //     0xa4d788: ldur            w2, [x1, #0xb]
    // 0xa4d78c: DecompressPointer r2
    //     0xa4d78c: add             x2, x2, HEAP, lsl #32
    // 0xa4d790: cmp             w2, NULL
    // 0xa4d794: b.eq            #0xa4d7d4
    // 0xa4d798: r0 = _nextIfAny()
    //     0xa4d798: bl              #0xa4d2d8  ; [package:showcaseview/src/showcase.dart] _ShowcaseState::_nextIfAny
    // 0xa4d79c: ldur            x1, [fp, #-8]
    // 0xa4d7a0: LoadField: r2 = r1->field_f
    //     0xa4d7a0: ldur            w2, [x1, #0xf]
    // 0xa4d7a4: DecompressPointer r2
    //     0xa4d7a4: add             x2, x2, HEAP, lsl #32
    // 0xa4d7a8: LoadField: r1 = r2->field_b
    //     0xa4d7a8: ldur            w1, [x2, #0xb]
    // 0xa4d7ac: DecompressPointer r1
    //     0xa4d7ac: add             x1, x1, HEAP, lsl #32
    // 0xa4d7b0: cmp             w1, NULL
    // 0xa4d7b4: b.eq            #0xa4d7d8
    // 0xa4d7b8: r0 = Null
    //     0xa4d7b8: mov             x0, NULL
    // 0xa4d7bc: LeaveFrame
    //     0xa4d7bc: mov             SP, fp
    //     0xa4d7c0: ldp             fp, lr, [SP], #0x10
    // 0xa4d7c4: ret
    //     0xa4d7c4: ret             
    // 0xa4d7c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d7c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d7cc: b               #0xa4d744
    // 0xa4d7d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d7d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d7d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d7d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d7d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d7d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4691, size: 0xc4, field offset: 0xc
//   const constructor, 
class Showcase extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa9531c, size: 0x44
    // 0xa9531c: EnterFrame
    //     0xa9531c: stp             fp, lr, [SP, #-0x10]!
    //     0xa95320: mov             fp, SP
    // 0xa95324: mov             x0, x1
    // 0xa95328: r1 = <Showcase>
    //     0xa95328: add             x1, PP, #0x40, lsl #12  ; [pp+0x405c8] TypeArguments: <Showcase>
    //     0xa9532c: ldr             x1, [x1, #0x5c8]
    // 0xa95330: r0 = _ShowcaseState()
    //     0xa95330: bl              #0xa95360  ; Allocate_ShowcaseStateStub -> _ShowcaseState (size=0x38)
    // 0xa95334: r1 = false
    //     0xa95334: add             x1, NULL, #0x30  ; false
    // 0xa95338: StoreField: r0->field_13 = r1
    //     0xa95338: stur            w1, [x0, #0x13]
    // 0xa9533c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa9533c: stur            w1, [x0, #0x17]
    // 0xa95340: StoreField: r0->field_1b = r1
    //     0xa95340: stur            w1, [x0, #0x1b]
    // 0xa95344: r1 = true
    //     0xa95344: add             x1, NULL, #0x20  ; true
    // 0xa95348: StoreField: r0->field_1f = r1
    //     0xa95348: stur            w1, [x0, #0x1f]
    // 0xa9534c: r1 = Sentinel
    //     0xa9534c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa95350: StoreField: r0->field_33 = r1
    //     0xa95350: stur            w1, [x0, #0x33]
    // 0xa95354: LeaveFrame
    //     0xa95354: mov             SP, fp
    //     0xa95358: ldp             fp, lr, [SP], #0x10
    // 0xa9535c: ret
    //     0xa9535c: ret             
  }
}

// class id: 4915, size: 0x30, field offset: 0xc
//   const constructor, 
class _TargetWidget extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbb9c38, size: 0x114
    // 0xbb9c38: EnterFrame
    //     0xbb9c38: stp             fp, lr, [SP, #-0x10]!
    //     0xbb9c3c: mov             fp, SP
    // 0xbb9c40: AllocStack(0x20)
    //     0xbb9c40: sub             SP, SP, #0x20
    // 0xbb9c44: r0 = Instance_EdgeInsets
    //     0xbb9c44: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbb9c48: CheckStackOverflow
    //     0xbb9c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb9c4c: cmp             SP, x16
    //     0xbb9c50: b.ls            #0xbb9d0c
    // 0xbb9c54: LoadField: r2 = r1->field_b
    //     0xbb9c54: ldur            w2, [x1, #0xb]
    // 0xbb9c58: DecompressPointer r2
    //     0xbb9c58: add             x2, x2, HEAP, lsl #32
    // 0xbb9c5c: LoadField: d0 = r2->field_f
    //     0xbb9c5c: ldur            d0, [x2, #0xf]
    // 0xbb9c60: LoadField: d1 = r0->field_f
    //     0xbb9c60: ldur            d1, [x0, #0xf]
    // 0xbb9c64: fsub            d2, d0, d1
    // 0xbb9c68: stur            d2, [fp, #-0x20]
    // 0xbb9c6c: LoadField: d0 = r2->field_7
    //     0xbb9c6c: ldur            d0, [x2, #7]
    // 0xbb9c70: LoadField: d1 = r0->field_7
    //     0xbb9c70: ldur            d1, [x0, #7]
    // 0xbb9c74: fsub            d3, d0, d1
    // 0xbb9c78: stur            d3, [fp, #-0x18]
    // 0xbb9c7c: r0 = targetWidgetContent()
    //     0xbb9c7c: bl              #0xbb9d4c  ; [package:showcaseview/src/showcase.dart] _TargetWidget::targetWidgetContent
    // 0xbb9c80: ldur            d0, [fp, #-0x18]
    // 0xbb9c84: stur            x0, [fp, #-0x10]
    // 0xbb9c88: r2 = inline_Allocate_Double()
    //     0xbb9c88: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0xbb9c8c: add             x2, x2, #0x10
    //     0xbb9c90: cmp             x1, x2
    //     0xbb9c94: b.ls            #0xbb9d14
    //     0xbb9c98: str             x2, [THR, #0x50]  ; THR::top
    //     0xbb9c9c: sub             x2, x2, #0xf
    //     0xbb9ca0: movz            x1, #0xe15c
    //     0xbb9ca4: movk            x1, #0x3, lsl #16
    //     0xbb9ca8: stur            x1, [x2, #-1]
    // 0xbb9cac: StoreField: r2->field_7 = d0
    //     0xbb9cac: stur            d0, [x2, #7]
    // 0xbb9cb0: stur            x2, [fp, #-8]
    // 0xbb9cb4: r1 = <StackParentData>
    //     0xbb9cb4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25780] TypeArguments: <StackParentData>
    //     0xbb9cb8: ldr             x1, [x1, #0x780]
    // 0xbb9cbc: r0 = Positioned()
    //     0xbb9cbc: bl              #0x9f19f8  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xbb9cc0: ldur            x1, [fp, #-8]
    // 0xbb9cc4: StoreField: r0->field_13 = r1
    //     0xbb9cc4: stur            w1, [x0, #0x13]
    // 0xbb9cc8: ldur            d0, [fp, #-0x20]
    // 0xbb9ccc: r1 = inline_Allocate_Double()
    //     0xbb9ccc: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xbb9cd0: add             x1, x1, #0x10
    //     0xbb9cd4: cmp             x2, x1
    //     0xbb9cd8: b.ls            #0xbb9d30
    //     0xbb9cdc: str             x1, [THR, #0x50]  ; THR::top
    //     0xbb9ce0: sub             x1, x1, #0xf
    //     0xbb9ce4: movz            x2, #0xe15c
    //     0xbb9ce8: movk            x2, #0x3, lsl #16
    //     0xbb9cec: stur            x2, [x1, #-1]
    // 0xbb9cf0: StoreField: r1->field_7 = d0
    //     0xbb9cf0: stur            d0, [x1, #7]
    // 0xbb9cf4: ArrayStore: r0[0] = r1  ; List_4
    //     0xbb9cf4: stur            w1, [x0, #0x17]
    // 0xbb9cf8: ldur            x1, [fp, #-0x10]
    // 0xbb9cfc: StoreField: r0->field_b = r1
    //     0xbb9cfc: stur            w1, [x0, #0xb]
    // 0xbb9d00: LeaveFrame
    //     0xbb9d00: mov             SP, fp
    //     0xbb9d04: ldp             fp, lr, [SP], #0x10
    // 0xbb9d08: ret
    //     0xbb9d08: ret             
    // 0xbb9d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb9d0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb9d10: b               #0xbb9c54
    // 0xbb9d14: SaveReg d0
    //     0xbb9d14: str             q0, [SP, #-0x10]!
    // 0xbb9d18: SaveReg r0
    //     0xbb9d18: str             x0, [SP, #-8]!
    // 0xbb9d1c: r0 = AllocateDouble()
    //     0xbb9d1c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb9d20: mov             x2, x0
    // 0xbb9d24: RestoreReg r0
    //     0xbb9d24: ldr             x0, [SP], #8
    // 0xbb9d28: RestoreReg d0
    //     0xbb9d28: ldr             q0, [SP], #0x10
    // 0xbb9d2c: b               #0xbb9cac
    // 0xbb9d30: SaveReg d0
    //     0xbb9d30: str             q0, [SP, #-0x10]!
    // 0xbb9d34: SaveReg r0
    //     0xbb9d34: str             x0, [SP, #-8]!
    // 0xbb9d38: r0 = AllocateDouble()
    //     0xbb9d38: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb9d3c: mov             x1, x0
    // 0xbb9d40: RestoreReg r0
    //     0xbb9d40: ldr             x0, [SP], #8
    // 0xbb9d44: RestoreReg d0
    //     0xbb9d44: ldr             q0, [SP], #0x10
    // 0xbb9d48: b               #0xbb9cf0
  }
  _ targetWidgetContent(/* No info */) {
    // ** addr: 0xbb9d4c, size: 0x15c
    // 0xbb9d4c: EnterFrame
    //     0xbb9d4c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb9d50: mov             fp, SP
    // 0xbb9d54: AllocStack(0x58)
    //     0xbb9d54: sub             SP, SP, #0x58
    // 0xbb9d58: CheckStackOverflow
    //     0xbb9d58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb9d5c: cmp             SP, x16
    //     0xbb9d60: b.ls            #0xbb9e6c
    // 0xbb9d64: LoadField: r0 = r1->field_13
    //     0xbb9d64: ldur            w0, [x1, #0x13]
    // 0xbb9d68: DecompressPointer r0
    //     0xbb9d68: add             x0, x0, HEAP, lsl #32
    // 0xbb9d6c: stur            x0, [fp, #-8]
    // 0xbb9d70: LoadField: r2 = r1->field_f
    //     0xbb9d70: ldur            w2, [x1, #0xf]
    // 0xbb9d74: DecompressPointer r2
    //     0xbb9d74: add             x2, x2, HEAP, lsl #32
    // 0xbb9d78: LoadField: d0 = r2->field_f
    //     0xbb9d78: ldur            d0, [x2, #0xf]
    // 0xbb9d7c: stur            d0, [fp, #-0x38]
    // 0xbb9d80: LoadField: d1 = r2->field_7
    //     0xbb9d80: ldur            d1, [x2, #7]
    // 0xbb9d84: stur            d1, [fp, #-0x30]
    // 0xbb9d88: r0 = ShapeDecoration()
    //     0xbb9d88: bl              #0x7f3e70  ; AllocateShapeDecorationStub -> ShapeDecoration (size=0x1c)
    // 0xbb9d8c: mov             x1, x0
    // 0xbb9d90: r0 = Instance_RoundedRectangleBorder
    //     0xbb9d90: ldr             x0, [PP, #0x5700]  ; [pp+0x5700] Obj!RoundedRectangleBorder@e14681
    // 0xbb9d94: stur            x1, [fp, #-0x20]
    // 0xbb9d98: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb9d98: stur            w0, [x1, #0x17]
    // 0xbb9d9c: ldur            d0, [fp, #-0x38]
    // 0xbb9da0: r0 = inline_Allocate_Double()
    //     0xbb9da0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbb9da4: add             x0, x0, #0x10
    //     0xbb9da8: cmp             x2, x0
    //     0xbb9dac: b.ls            #0xbb9e74
    //     0xbb9db0: str             x0, [THR, #0x50]  ; THR::top
    //     0xbb9db4: sub             x0, x0, #0xf
    //     0xbb9db8: movz            x2, #0xe15c
    //     0xbb9dbc: movk            x2, #0x3, lsl #16
    //     0xbb9dc0: stur            x2, [x0, #-1]
    // 0xbb9dc4: StoreField: r0->field_7 = d0
    //     0xbb9dc4: stur            d0, [x0, #7]
    // 0xbb9dc8: ldur            d0, [fp, #-0x30]
    // 0xbb9dcc: stur            x0, [fp, #-0x18]
    // 0xbb9dd0: r2 = inline_Allocate_Double()
    //     0xbb9dd0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xbb9dd4: add             x2, x2, #0x10
    //     0xbb9dd8: cmp             x3, x2
    //     0xbb9ddc: b.ls            #0xbb9e8c
    //     0xbb9de0: str             x2, [THR, #0x50]  ; THR::top
    //     0xbb9de4: sub             x2, x2, #0xf
    //     0xbb9de8: movz            x3, #0xe15c
    //     0xbb9dec: movk            x3, #0x3, lsl #16
    //     0xbb9df0: stur            x3, [x2, #-1]
    // 0xbb9df4: StoreField: r2->field_7 = d0
    //     0xbb9df4: stur            d0, [x2, #7]
    // 0xbb9df8: stur            x2, [fp, #-0x10]
    // 0xbb9dfc: r0 = Container()
    //     0xbb9dfc: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb9e00: stur            x0, [fp, #-0x28]
    // 0xbb9e04: ldur            x16, [fp, #-0x18]
    // 0xbb9e08: ldur            lr, [fp, #-0x10]
    // 0xbb9e0c: stp             lr, x16, [SP, #0x10]
    // 0xbb9e10: r16 = Instance_EdgeInsets
    //     0xbb9e10: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbb9e14: ldur            lr, [fp, #-0x20]
    // 0xbb9e18: stp             lr, x16, [SP]
    // 0xbb9e1c: mov             x1, x0
    // 0xbb9e20: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x1, margin, 0x3, width, 0x2, null]
    //     0xbb9e20: add             x4, PP, #0x51, lsl #12  ; [pp+0x51540] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x1, "margin", 0x3, "width", 0x2, Null]
    //     0xbb9e24: ldr             x4, [x4, #0x540]
    // 0xbb9e28: r0 = Container()
    //     0xbb9e28: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb9e2c: r0 = GestureDetector()
    //     0xbb9e2c: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbb9e30: stur            x0, [fp, #-0x10]
    // 0xbb9e34: ldur            x16, [fp, #-8]
    // 0xbb9e38: stp             NULL, x16, [SP, #0x10]
    // 0xbb9e3c: r16 = Instance_HitTestBehavior
    //     0xbb9e3c: add             x16, PP, #0x25, lsl #12  ; [pp+0x251d0] Obj!HitTestBehavior@e358e1
    //     0xbb9e40: ldr             x16, [x16, #0x1d0]
    // 0xbb9e44: ldur            lr, [fp, #-0x28]
    // 0xbb9e48: stp             lr, x16, [SP]
    // 0xbb9e4c: mov             x1, x0
    // 0xbb9e50: r4 = const [0, 0x5, 0x4, 0x1, behavior, 0x3, child, 0x4, onLongPress, 0x2, onTap, 0x1, null]
    //     0xbb9e50: add             x4, PP, #0x51, lsl #12  ; [pp+0x51548] List(13) [0, 0x5, 0x4, 0x1, "behavior", 0x3, "child", 0x4, "onLongPress", 0x2, "onTap", 0x1, Null]
    //     0xbb9e54: ldr             x4, [x4, #0x548]
    // 0xbb9e58: r0 = GestureDetector()
    //     0xbb9e58: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbb9e5c: ldur            x0, [fp, #-0x10]
    // 0xbb9e60: LeaveFrame
    //     0xbb9e60: mov             SP, fp
    //     0xbb9e64: ldp             fp, lr, [SP], #0x10
    // 0xbb9e68: ret
    //     0xbb9e68: ret             
    // 0xbb9e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb9e6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb9e70: b               #0xbb9d64
    // 0xbb9e74: SaveReg d0
    //     0xbb9e74: str             q0, [SP, #-0x10]!
    // 0xbb9e78: SaveReg r1
    //     0xbb9e78: str             x1, [SP, #-8]!
    // 0xbb9e7c: r0 = AllocateDouble()
    //     0xbb9e7c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb9e80: RestoreReg r1
    //     0xbb9e80: ldr             x1, [SP], #8
    // 0xbb9e84: RestoreReg d0
    //     0xbb9e84: ldr             q0, [SP], #0x10
    // 0xbb9e88: b               #0xbb9dc4
    // 0xbb9e8c: SaveReg d0
    //     0xbb9e8c: str             q0, [SP, #-0x10]!
    // 0xbb9e90: stp             x0, x1, [SP, #-0x10]!
    // 0xbb9e94: r0 = AllocateDouble()
    //     0xbb9e94: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbb9e98: mov             x2, x0
    // 0xbb9e9c: ldp             x0, x1, [SP], #0x10
    // 0xbb9ea0: RestoreReg d0
    //     0xbb9ea0: ldr             q0, [SP], #0x10
    // 0xbb9ea4: b               #0xbb9df4
  }
}
