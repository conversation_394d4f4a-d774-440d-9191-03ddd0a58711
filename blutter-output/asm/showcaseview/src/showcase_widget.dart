// lib: , url: package:showcaseview/src/showcase_widget.dart

// class id: 1051127, size: 0x8
class :: {
}

// class id: 4085, size: 0x28, field offset: 0x14
class ShowCaseWidgetState extends State<dynamic> {

  _ startShowCase(/* No info */) {
    // ** addr: 0x91e35c, size: 0xa4
    // 0x91e35c: EnterFrame
    //     0x91e35c: stp             fp, lr, [SP, #-0x10]!
    //     0x91e360: mov             fp, SP
    // 0x91e364: AllocStack(0x10)
    //     0x91e364: sub             SP, SP, #0x10
    // 0x91e368: SetupParameters(ShowCaseWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x91e368: stur            x1, [fp, #-8]
    //     0x91e36c: stur            x2, [fp, #-0x10]
    // 0x91e370: CheckStackOverflow
    //     0x91e370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e374: cmp             SP, x16
    //     0x91e378: b.ls            #0x91e3f4
    // 0x91e37c: r1 = 2
    //     0x91e37c: movz            x1, #0x2
    // 0x91e380: r0 = AllocateContext()
    //     0x91e380: bl              #0xec126c  ; AllocateContextStub
    // 0x91e384: mov             x1, x0
    // 0x91e388: ldur            x0, [fp, #-8]
    // 0x91e38c: StoreField: r1->field_f = r0
    //     0x91e38c: stur            w0, [x1, #0xf]
    // 0x91e390: ldur            x2, [fp, #-0x10]
    // 0x91e394: StoreField: r1->field_13 = r2
    //     0x91e394: stur            w2, [x1, #0x13]
    // 0x91e398: LoadField: r2 = r0->field_b
    //     0x91e398: ldur            w2, [x0, #0xb]
    // 0x91e39c: DecompressPointer r2
    //     0x91e39c: add             x2, x2, HEAP, lsl #32
    // 0x91e3a0: cmp             w2, NULL
    // 0x91e3a4: b.eq            #0x91e3fc
    // 0x91e3a8: LoadField: r2 = r0->field_f
    //     0x91e3a8: ldur            w2, [x0, #0xf]
    // 0x91e3ac: DecompressPointer r2
    //     0x91e3ac: add             x2, x2, HEAP, lsl #32
    // 0x91e3b0: cmp             w2, NULL
    // 0x91e3b4: b.ne            #0x91e3c8
    // 0x91e3b8: r0 = Null
    //     0x91e3b8: mov             x0, NULL
    // 0x91e3bc: LeaveFrame
    //     0x91e3bc: mov             SP, fp
    //     0x91e3c0: ldp             fp, lr, [SP], #0x10
    // 0x91e3c4: ret
    //     0x91e3c4: ret             
    // 0x91e3c8: mov             x2, x1
    // 0x91e3cc: r1 = Function '<anonymous closure>':.
    //     0x91e3cc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40678] AnonymousClosure: (0x91e424), in [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::startShowCase (0x91e35c)
    //     0x91e3d0: ldr             x1, [x1, #0x678]
    // 0x91e3d4: r0 = AllocateClosure()
    //     0x91e3d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x91e3d8: ldur            x1, [fp, #-8]
    // 0x91e3dc: mov             x2, x0
    // 0x91e3e0: r0 = setState()
    //     0x91e3e0: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x91e3e4: r0 = Null
    //     0x91e3e4: mov             x0, NULL
    // 0x91e3e8: LeaveFrame
    //     0x91e3e8: mov             SP, fp
    //     0x91e3ec: ldp             fp, lr, [SP], #0x10
    // 0x91e3f0: ret
    //     0x91e3f0: ret             
    // 0x91e3f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e3f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e3f8: b               #0x91e37c
    // 0x91e3fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91e3fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x91e424, size: 0x70
    // 0x91e424: EnterFrame
    //     0x91e424: stp             fp, lr, [SP, #-0x10]!
    //     0x91e428: mov             fp, SP
    // 0x91e42c: ldr             x0, [fp, #0x10]
    // 0x91e430: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91e430: ldur            w1, [x0, #0x17]
    // 0x91e434: DecompressPointer r1
    //     0x91e434: add             x1, x1, HEAP, lsl #32
    // 0x91e438: CheckStackOverflow
    //     0x91e438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e43c: cmp             SP, x16
    //     0x91e440: b.ls            #0x91e48c
    // 0x91e444: LoadField: r2 = r1->field_f
    //     0x91e444: ldur            w2, [x1, #0xf]
    // 0x91e448: DecompressPointer r2
    //     0x91e448: add             x2, x2, HEAP, lsl #32
    // 0x91e44c: LoadField: r0 = r1->field_13
    //     0x91e44c: ldur            w0, [x1, #0x13]
    // 0x91e450: DecompressPointer r0
    //     0x91e450: add             x0, x0, HEAP, lsl #32
    // 0x91e454: StoreField: r2->field_13 = r0
    //     0x91e454: stur            w0, [x2, #0x13]
    //     0x91e458: ldurb           w16, [x2, #-1]
    //     0x91e45c: ldurb           w17, [x0, #-1]
    //     0x91e460: and             x16, x17, x16, lsr #2
    //     0x91e464: tst             x16, HEAP, lsr #32
    //     0x91e468: b.eq            #0x91e470
    //     0x91e46c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x91e470: ArrayStore: r2[0] = rZR  ; List_4
    //     0x91e470: stur            wzr, [x2, #0x17]
    // 0x91e474: mov             x1, x2
    // 0x91e478: r0 = _onStart()
    //     0x91e478: bl              #0x91e494  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::_onStart
    // 0x91e47c: r0 = Null
    //     0x91e47c: mov             x0, NULL
    // 0x91e480: LeaveFrame
    //     0x91e480: mov             SP, fp
    //     0x91e484: ldp             fp, lr, [SP], #0x10
    // 0x91e488: ret
    //     0x91e488: ret             
    // 0x91e48c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e48c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e490: b               #0x91e444
  }
  _ _onStart(/* No info */) {
    // ** addr: 0x91e494, size: 0x70
    // 0x91e494: EnterFrame
    //     0x91e494: stp             fp, lr, [SP, #-0x10]!
    //     0x91e498: mov             fp, SP
    // 0x91e49c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x91e49c: ldur            w2, [x1, #0x17]
    // 0x91e4a0: DecompressPointer r2
    //     0x91e4a0: add             x2, x2, HEAP, lsl #32
    // 0x91e4a4: cmp             w2, NULL
    // 0x91e4a8: b.eq            #0x91e4f8
    // 0x91e4ac: LoadField: r3 = r1->field_13
    //     0x91e4ac: ldur            w3, [x1, #0x13]
    // 0x91e4b0: DecompressPointer r3
    //     0x91e4b0: add             x3, x3, HEAP, lsl #32
    // 0x91e4b4: cmp             w3, NULL
    // 0x91e4b8: b.eq            #0x91e4fc
    // 0x91e4bc: LoadField: r4 = r3->field_b
    //     0x91e4bc: ldur            w4, [x3, #0xb]
    // 0x91e4c0: r3 = LoadInt32Instr(r2)
    //     0x91e4c0: sbfx            x3, x2, #1, #0x1f
    //     0x91e4c4: tbz             w2, #0, #0x91e4cc
    //     0x91e4c8: ldur            x3, [x2, #7]
    // 0x91e4cc: r2 = LoadInt32Instr(r4)
    //     0x91e4cc: sbfx            x2, x4, #1, #0x1f
    // 0x91e4d0: cmp             x3, x2
    // 0x91e4d4: b.ge            #0x91e4e8
    // 0x91e4d8: LoadField: r2 = r1->field_b
    //     0x91e4d8: ldur            w2, [x1, #0xb]
    // 0x91e4dc: DecompressPointer r2
    //     0x91e4dc: add             x2, x2, HEAP, lsl #32
    // 0x91e4e0: cmp             w2, NULL
    // 0x91e4e4: b.eq            #0x91e500
    // 0x91e4e8: r0 = Null
    //     0x91e4e8: mov             x0, NULL
    // 0x91e4ec: LeaveFrame
    //     0x91e4ec: mov             SP, fp
    //     0x91e4f0: ldp             fp, lr, [SP], #0x10
    // 0x91e4f4: ret
    //     0x91e4f4: ret             
    // 0x91e4f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91e4f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x91e4fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91e4fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x91e500: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91e500: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x980448, size: 0x30
    // 0x980448: EnterFrame
    //     0x980448: stp             fp, lr, [SP, #-0x10]!
    //     0x98044c: mov             fp, SP
    // 0x980450: CheckStackOverflow
    //     0x980450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980454: cmp             SP, x16
    //     0x980458: b.ls            #0x980470
    // 0x98045c: r0 = initRootWidget()
    //     0x98045c: bl              #0x980478  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::initRootWidget
    // 0x980460: r0 = Null
    //     0x980460: mov             x0, NULL
    // 0x980464: LeaveFrame
    //     0x980464: mov             SP, fp
    //     0x980468: ldp             fp, lr, [SP], #0x10
    // 0x98046c: ret
    //     0x98046c: ret             
    // 0x980470: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980470: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980474: b               #0x98045c
  }
  _ initRootWidget(/* No info */) {
    // ** addr: 0x980478, size: 0x130
    // 0x980478: EnterFrame
    //     0x980478: stp             fp, lr, [SP, #-0x10]!
    //     0x98047c: mov             fp, SP
    // 0x980480: AllocStack(0x18)
    //     0x980480: sub             SP, SP, #0x18
    // 0x980484: SetupParameters(ShowCaseWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x980484: stur            x1, [fp, #-8]
    // 0x980488: CheckStackOverflow
    //     0x980488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98048c: cmp             SP, x16
    //     0x980490: b.ls            #0x98059c
    // 0x980494: r1 = 1
    //     0x980494: movz            x1, #0x1
    // 0x980498: r0 = AllocateContext()
    //     0x980498: bl              #0xec126c  ; AllocateContextStub
    // 0x98049c: mov             x1, x0
    // 0x9804a0: ldur            x0, [fp, #-8]
    // 0x9804a4: StoreField: r1->field_f = r0
    //     0x9804a4: stur            w0, [x1, #0xf]
    // 0x9804a8: r0 = LoadStaticField(0x7d4)
    //     0x9804a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9804ac: ldr             x0, [x0, #0xfa8]
    // 0x9804b0: cmp             w0, NULL
    // 0x9804b4: b.eq            #0x9805a4
    // 0x9804b8: LoadField: r3 = r0->field_53
    //     0x9804b8: ldur            w3, [x0, #0x53]
    // 0x9804bc: DecompressPointer r3
    //     0x9804bc: add             x3, x3, HEAP, lsl #32
    // 0x9804c0: stur            x3, [fp, #-0x10]
    // 0x9804c4: LoadField: r0 = r3->field_7
    //     0x9804c4: ldur            w0, [x3, #7]
    // 0x9804c8: DecompressPointer r0
    //     0x9804c8: add             x0, x0, HEAP, lsl #32
    // 0x9804cc: mov             x2, x1
    // 0x9804d0: stur            x0, [fp, #-8]
    // 0x9804d4: r1 = Function '<anonymous closure>':.
    //     0x9804d4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40598] AnonymousClosure: (0x9805a8), in [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::initRootWidget (0x980478)
    //     0x9804d8: ldr             x1, [x1, #0x598]
    // 0x9804dc: r0 = AllocateClosure()
    //     0x9804dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x9804e0: ldur            x2, [fp, #-8]
    // 0x9804e4: mov             x3, x0
    // 0x9804e8: r1 = Null
    //     0x9804e8: mov             x1, NULL
    // 0x9804ec: stur            x3, [fp, #-8]
    // 0x9804f0: cmp             w2, NULL
    // 0x9804f4: b.eq            #0x980514
    // 0x9804f8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9804f8: ldur            w4, [x2, #0x17]
    // 0x9804fc: DecompressPointer r4
    //     0x9804fc: add             x4, x4, HEAP, lsl #32
    // 0x980500: r8 = X0
    //     0x980500: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x980504: LoadField: r9 = r4->field_7
    //     0x980504: ldur            x9, [x4, #7]
    // 0x980508: r3 = Null
    //     0x980508: add             x3, PP, #0x40, lsl #12  ; [pp+0x405a0] Null
    //     0x98050c: ldr             x3, [x3, #0x5a0]
    // 0x980510: blr             x9
    // 0x980514: ldur            x0, [fp, #-0x10]
    // 0x980518: LoadField: r1 = r0->field_b
    //     0x980518: ldur            w1, [x0, #0xb]
    // 0x98051c: LoadField: r2 = r0->field_f
    //     0x98051c: ldur            w2, [x0, #0xf]
    // 0x980520: DecompressPointer r2
    //     0x980520: add             x2, x2, HEAP, lsl #32
    // 0x980524: LoadField: r3 = r2->field_b
    //     0x980524: ldur            w3, [x2, #0xb]
    // 0x980528: r2 = LoadInt32Instr(r1)
    //     0x980528: sbfx            x2, x1, #1, #0x1f
    // 0x98052c: stur            x2, [fp, #-0x18]
    // 0x980530: r1 = LoadInt32Instr(r3)
    //     0x980530: sbfx            x1, x3, #1, #0x1f
    // 0x980534: cmp             x2, x1
    // 0x980538: b.ne            #0x980544
    // 0x98053c: mov             x1, x0
    // 0x980540: r0 = _growToNextCapacity()
    //     0x980540: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x980544: ldur            x2, [fp, #-0x10]
    // 0x980548: ldur            x3, [fp, #-0x18]
    // 0x98054c: add             x4, x3, #1
    // 0x980550: lsl             x5, x4, #1
    // 0x980554: StoreField: r2->field_b = r5
    //     0x980554: stur            w5, [x2, #0xb]
    // 0x980558: LoadField: r1 = r2->field_f
    //     0x980558: ldur            w1, [x2, #0xf]
    // 0x98055c: DecompressPointer r1
    //     0x98055c: add             x1, x1, HEAP, lsl #32
    // 0x980560: ldur            x0, [fp, #-8]
    // 0x980564: ArrayStore: r1[r3] = r0  ; List_4
    //     0x980564: add             x25, x1, x3, lsl #2
    //     0x980568: add             x25, x25, #0xf
    //     0x98056c: str             w0, [x25]
    //     0x980570: tbz             w0, #0, #0x98058c
    //     0x980574: ldurb           w16, [x1, #-1]
    //     0x980578: ldurb           w17, [x0, #-1]
    //     0x98057c: and             x16, x17, x16, lsr #2
    //     0x980580: tst             x16, HEAP, lsr #32
    //     0x980584: b.eq            #0x98058c
    //     0x980588: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x98058c: r0 = Null
    //     0x98058c: mov             x0, NULL
    // 0x980590: LeaveFrame
    //     0x980590: mov             SP, fp
    //     0x980594: ldp             fp, lr, [SP], #0x10
    // 0x980598: ret
    //     0x980598: ret             
    // 0x98059c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98059c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9805a0: b               #0x980494
    // 0x9805a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9805a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9805a8, size: 0x1ec
    // 0x9805a8: EnterFrame
    //     0x9805a8: stp             fp, lr, [SP, #-0x10]!
    //     0x9805ac: mov             fp, SP
    // 0x9805b0: AllocStack(0x30)
    //     0x9805b0: sub             SP, SP, #0x30
    // 0x9805b4: SetupParameters()
    //     0x9805b4: ldr             x0, [fp, #0x18]
    //     0x9805b8: ldur            w1, [x0, #0x17]
    //     0x9805bc: add             x1, x1, HEAP, lsl #32
    //     0x9805c0: stur            x1, [fp, #-8]
    // 0x9805c4: CheckStackOverflow
    //     0x9805c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9805c8: cmp             SP, x16
    //     0x9805cc: b.ls            #0x980784
    // 0x9805d0: LoadField: r0 = r1->field_f
    //     0x9805d0: ldur            w0, [x1, #0xf]
    // 0x9805d4: DecompressPointer r0
    //     0x9805d4: add             x0, x0, HEAP, lsl #32
    // 0x9805d8: LoadField: r2 = r0->field_f
    //     0x9805d8: ldur            w2, [x0, #0xf]
    // 0x9805dc: DecompressPointer r2
    //     0x9805dc: add             x2, x2, HEAP, lsl #32
    // 0x9805e0: cmp             w2, NULL
    // 0x9805e4: b.ne            #0x9805f8
    // 0x9805e8: r0 = Null
    //     0x9805e8: mov             x0, NULL
    // 0x9805ec: LeaveFrame
    //     0x9805ec: mov             SP, fp
    //     0x9805f0: ldp             fp, lr, [SP], #0x10
    // 0x9805f4: ret
    //     0x9805f4: ret             
    // 0x9805f8: r16 = <State<WidgetsApp>>
    //     0x9805f8: add             x16, PP, #0x40, lsl #12  ; [pp+0x405b0] TypeArguments: <State<WidgetsApp>>
    //     0x9805fc: ldr             x16, [x16, #0x5b0]
    // 0x980600: stp             x2, x16, [SP]
    // 0x980604: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x980604: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x980608: r0 = findAncestorStateOfType()
    //     0x980608: bl              #0x6a4a9c  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorStateOfType
    // 0x98060c: mov             x2, x0
    // 0x980610: ldur            x0, [fp, #-8]
    // 0x980614: stur            x2, [fp, #-0x18]
    // 0x980618: LoadField: r3 = r0->field_f
    //     0x980618: ldur            w3, [x0, #0xf]
    // 0x98061c: DecompressPointer r3
    //     0x98061c: add             x3, x3, HEAP, lsl #32
    // 0x980620: stur            x3, [fp, #-0x10]
    // 0x980624: cmp             w2, NULL
    // 0x980628: b.ne            #0x980640
    // 0x98062c: mov             x5, x3
    // 0x980630: mov             x3, x0
    // 0x980634: mov             x4, x2
    // 0x980638: r6 = Null
    //     0x980638: mov             x6, NULL
    // 0x98063c: b               #0x980664
    // 0x980640: LoadField: r1 = r2->field_f
    //     0x980640: ldur            w1, [x2, #0xf]
    // 0x980644: DecompressPointer r1
    //     0x980644: add             x1, x1, HEAP, lsl #32
    // 0x980648: cmp             w1, NULL
    // 0x98064c: b.eq            #0x98078c
    // 0x980650: r0 = renderObject()
    //     0x980650: bl              #0xd17ea4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0x980654: mov             x6, x0
    // 0x980658: ldur            x3, [fp, #-8]
    // 0x98065c: ldur            x4, [fp, #-0x18]
    // 0x980660: ldur            x5, [fp, #-0x10]
    // 0x980664: mov             x0, x6
    // 0x980668: stur            x6, [fp, #-0x20]
    // 0x98066c: r2 = Null
    //     0x98066c: mov             x2, NULL
    // 0x980670: r1 = Null
    //     0x980670: mov             x1, NULL
    // 0x980674: r4 = LoadClassIdInstr(r0)
    //     0x980674: ldur            x4, [x0, #-1]
    //     0x980678: ubfx            x4, x4, #0xc, #0x14
    // 0x98067c: sub             x4, x4, #0xbba
    // 0x980680: cmp             x4, #0x9a
    // 0x980684: b.ls            #0x980698
    // 0x980688: r8 = RenderBox?
    //     0x980688: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x98068c: r3 = Null
    //     0x98068c: add             x3, PP, #0x40, lsl #12  ; [pp+0x405b8] Null
    //     0x980690: ldr             x3, [x3, #0x5b8]
    // 0x980694: r0 = RenderBox?()
    //     0x980694: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x980698: ldur            x0, [fp, #-0x20]
    // 0x98069c: ldur            x1, [fp, #-0x10]
    // 0x9806a0: StoreField: r1->field_1b = r0
    //     0x9806a0: stur            w0, [x1, #0x1b]
    //     0x9806a4: ldurb           w16, [x1, #-1]
    //     0x9806a8: ldurb           w17, [x0, #-1]
    //     0x9806ac: and             x16, x17, x16, lsr #2
    //     0x9806b0: tst             x16, HEAP, lsr #32
    //     0x9806b4: b.eq            #0x9806bc
    //     0x9806b8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9806bc: ldur            x0, [fp, #-8]
    // 0x9806c0: LoadField: r2 = r0->field_f
    //     0x9806c0: ldur            w2, [x0, #0xf]
    // 0x9806c4: DecompressPointer r2
    //     0x9806c4: add             x2, x2, HEAP, lsl #32
    // 0x9806c8: ldur            x1, [fp, #-0x18]
    // 0x9806cc: stur            x2, [fp, #-0x10]
    // 0x9806d0: cmp             w1, NULL
    // 0x9806d4: b.ne            #0x980700
    // 0x9806d8: LoadField: r1 = r2->field_f
    //     0x9806d8: ldur            w1, [x2, #0xf]
    // 0x9806dc: DecompressPointer r1
    //     0x9806dc: add             x1, x1, HEAP, lsl #32
    // 0x9806e0: cmp             w1, NULL
    // 0x9806e4: b.eq            #0x980790
    // 0x9806e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9806e8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9806ec: r0 = _of()
    //     0x9806ec: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x9806f0: LoadField: r1 = r0->field_7
    //     0x9806f0: ldur            w1, [x0, #7]
    // 0x9806f4: DecompressPointer r1
    //     0x9806f4: add             x1, x1, HEAP, lsl #32
    // 0x9806f8: mov             x0, x1
    // 0x9806fc: b               #0x980720
    // 0x980700: mov             x0, x2
    // 0x980704: LoadField: r1 = r0->field_1b
    //     0x980704: ldur            w1, [x0, #0x1b]
    // 0x980708: DecompressPointer r1
    //     0x980708: add             x1, x1, HEAP, lsl #32
    // 0x98070c: cmp             w1, NULL
    // 0x980710: b.ne            #0x98071c
    // 0x980714: r0 = Null
    //     0x980714: mov             x0, NULL
    // 0x980718: b               #0x980720
    // 0x98071c: r0 = size()
    //     0x98071c: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x980720: ldur            x2, [fp, #-8]
    // 0x980724: ldur            x1, [fp, #-0x10]
    // 0x980728: StoreField: r1->field_1f = r0
    //     0x980728: stur            w0, [x1, #0x1f]
    //     0x98072c: ldurb           w16, [x1, #-1]
    //     0x980730: ldurb           w17, [x0, #-1]
    //     0x980734: and             x16, x17, x16, lsr #2
    //     0x980738: tst             x16, HEAP, lsr #32
    //     0x98073c: b.eq            #0x980744
    //     0x980740: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x980744: LoadField: r0 = r2->field_f
    //     0x980744: ldur            w0, [x2, #0xf]
    // 0x980748: DecompressPointer r0
    //     0x980748: add             x0, x0, HEAP, lsl #32
    // 0x98074c: stur            x0, [fp, #-0x10]
    // 0x980750: r0 = UniqueKey()
    //     0x980750: bl              #0x7a4a30  ; AllocateUniqueKeyStub -> UniqueKey (size=0x8)
    // 0x980754: ldur            x1, [fp, #-0x10]
    // 0x980758: StoreField: r1->field_23 = r0
    //     0x980758: stur            w0, [x1, #0x23]
    //     0x98075c: ldurb           w16, [x1, #-1]
    //     0x980760: ldurb           w17, [x0, #-1]
    //     0x980764: and             x16, x17, x16, lsr #2
    //     0x980768: tst             x16, HEAP, lsr #32
    //     0x98076c: b.eq            #0x980774
    //     0x980770: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x980774: r0 = Null
    //     0x980774: mov             x0, NULL
    // 0x980778: LeaveFrame
    //     0x980778: mov             SP, fp
    //     0x98077c: ldp             fp, lr, [SP], #0x10
    // 0x980780: ret
    //     0x980780: ret             
    // 0x980784: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980784: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980788: b               #0x9805d0
    // 0x98078c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98078c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x980790: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980790: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ completed(/* No info */) {
    // ** addr: 0xa4d3cc, size: 0xfc
    // 0xa4d3cc: EnterFrame
    //     0xa4d3cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d3d0: mov             fp, SP
    // 0xa4d3d4: AllocStack(0x28)
    //     0xa4d3d4: sub             SP, SP, #0x28
    // 0xa4d3d8: SetupParameters(ShowCaseWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa4d3d8: stur            x1, [fp, #-8]
    //     0xa4d3dc: stur            x2, [fp, #-0x10]
    // 0xa4d3e0: CheckStackOverflow
    //     0xa4d3e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d3e4: cmp             SP, x16
    //     0xa4d3e8: b.ls            #0xa4d4b8
    // 0xa4d3ec: r1 = 1
    //     0xa4d3ec: movz            x1, #0x1
    // 0xa4d3f0: r0 = AllocateContext()
    //     0xa4d3f0: bl              #0xec126c  ; AllocateContextStub
    // 0xa4d3f4: mov             x3, x0
    // 0xa4d3f8: ldur            x2, [fp, #-8]
    // 0xa4d3fc: stur            x3, [fp, #-0x18]
    // 0xa4d400: StoreField: r3->field_f = r2
    //     0xa4d400: stur            w2, [x3, #0xf]
    // 0xa4d404: LoadField: r4 = r2->field_13
    //     0xa4d404: ldur            w4, [x2, #0x13]
    // 0xa4d408: DecompressPointer r4
    //     0xa4d408: add             x4, x4, HEAP, lsl #32
    // 0xa4d40c: cmp             w4, NULL
    // 0xa4d410: b.eq            #0xa4d4a8
    // 0xa4d414: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa4d414: ldur            w0, [x2, #0x17]
    // 0xa4d418: DecompressPointer r0
    //     0xa4d418: add             x0, x0, HEAP, lsl #32
    // 0xa4d41c: cmp             w0, NULL
    // 0xa4d420: b.eq            #0xa4d4c0
    // 0xa4d424: LoadField: r1 = r4->field_b
    //     0xa4d424: ldur            w1, [x4, #0xb]
    // 0xa4d428: r5 = LoadInt32Instr(r0)
    //     0xa4d428: sbfx            x5, x0, #1, #0x1f
    //     0xa4d42c: tbz             w0, #0, #0xa4d434
    //     0xa4d430: ldur            x5, [x0, #7]
    // 0xa4d434: r0 = LoadInt32Instr(r1)
    //     0xa4d434: sbfx            x0, x1, #1, #0x1f
    // 0xa4d438: mov             x1, x5
    // 0xa4d43c: cmp             x1, x0
    // 0xa4d440: b.hs            #0xa4d4c4
    // 0xa4d444: LoadField: r0 = r4->field_f
    //     0xa4d444: ldur            w0, [x4, #0xf]
    // 0xa4d448: DecompressPointer r0
    //     0xa4d448: add             x0, x0, HEAP, lsl #32
    // 0xa4d44c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa4d44c: add             x16, x0, x5, lsl #2
    //     0xa4d450: ldur            w1, [x16, #0xf]
    // 0xa4d454: DecompressPointer r1
    //     0xa4d454: add             x1, x1, HEAP, lsl #32
    // 0xa4d458: r0 = LoadClassIdInstr(r1)
    //     0xa4d458: ldur            x0, [x1, #-1]
    //     0xa4d45c: ubfx            x0, x0, #0xc, #0x14
    // 0xa4d460: ldur            x16, [fp, #-0x10]
    // 0xa4d464: stp             x16, x1, [SP]
    // 0xa4d468: mov             lr, x0
    // 0xa4d46c: ldr             lr, [x21, lr, lsl #3]
    // 0xa4d470: blr             lr
    // 0xa4d474: tbnz            w0, #4, #0xa4d4a8
    // 0xa4d478: ldur            x0, [fp, #-8]
    // 0xa4d47c: LoadField: r1 = r0->field_f
    //     0xa4d47c: ldur            w1, [x0, #0xf]
    // 0xa4d480: DecompressPointer r1
    //     0xa4d480: add             x1, x1, HEAP, lsl #32
    // 0xa4d484: cmp             w1, NULL
    // 0xa4d488: b.eq            #0xa4d4a8
    // 0xa4d48c: ldur            x2, [fp, #-0x18]
    // 0xa4d490: r1 = Function '<anonymous closure>':.
    //     0xa4d490: add             x1, PP, #0x35, lsl #12  ; [pp+0x35d50] AnonymousClosure: (0xa4d4c8), in [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::completed (0xa4d3cc)
    //     0xa4d494: ldr             x1, [x1, #0xd50]
    // 0xa4d498: r0 = AllocateClosure()
    //     0xa4d498: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4d49c: ldur            x1, [fp, #-8]
    // 0xa4d4a0: mov             x2, x0
    // 0xa4d4a4: r0 = setState()
    //     0xa4d4a4: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa4d4a8: r0 = Null
    //     0xa4d4a8: mov             x0, NULL
    // 0xa4d4ac: LeaveFrame
    //     0xa4d4ac: mov             SP, fp
    //     0xa4d4b0: ldp             fp, lr, [SP], #0x10
    // 0xa4d4b4: ret
    //     0xa4d4b4: ret             
    // 0xa4d4b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d4b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d4bc: b               #0xa4d3ec
    // 0xa4d4c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d4c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d4c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4d4c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa4d4c8, size: 0x158
    // 0xa4d4c8: EnterFrame
    //     0xa4d4c8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d4cc: mov             fp, SP
    // 0xa4d4d0: AllocStack(0x10)
    //     0xa4d4d0: sub             SP, SP, #0x10
    // 0xa4d4d4: SetupParameters()
    //     0xa4d4d4: ldr             x0, [fp, #0x10]
    //     0xa4d4d8: ldur            w2, [x0, #0x17]
    //     0xa4d4dc: add             x2, x2, HEAP, lsl #32
    //     0xa4d4e0: stur            x2, [fp, #-8]
    // 0xa4d4e4: CheckStackOverflow
    //     0xa4d4e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d4e8: cmp             SP, x16
    //     0xa4d4ec: b.ls            #0xa4d600
    // 0xa4d4f0: LoadField: r3 = r2->field_f
    //     0xa4d4f0: ldur            w3, [x2, #0xf]
    // 0xa4d4f4: DecompressPointer r3
    //     0xa4d4f4: add             x3, x3, HEAP, lsl #32
    // 0xa4d4f8: LoadField: r0 = r3->field_b
    //     0xa4d4f8: ldur            w0, [x3, #0xb]
    // 0xa4d4fc: DecompressPointer r0
    //     0xa4d4fc: add             x0, x0, HEAP, lsl #32
    // 0xa4d500: cmp             w0, NULL
    // 0xa4d504: b.eq            #0xa4d608
    // 0xa4d508: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa4d508: ldur            w0, [x3, #0x17]
    // 0xa4d50c: DecompressPointer r0
    //     0xa4d50c: add             x0, x0, HEAP, lsl #32
    // 0xa4d510: cmp             w0, NULL
    // 0xa4d514: b.eq            #0xa4d60c
    // 0xa4d518: r1 = LoadInt32Instr(r0)
    //     0xa4d518: sbfx            x1, x0, #1, #0x1f
    //     0xa4d51c: tbz             w0, #0, #0xa4d524
    //     0xa4d520: ldur            x1, [x0, #7]
    // 0xa4d524: add             x4, x1, #1
    // 0xa4d528: r0 = BoxInt64Instr(r4)
    //     0xa4d528: sbfiz           x0, x4, #1, #0x1f
    //     0xa4d52c: cmp             x4, x0, asr #1
    //     0xa4d530: b.eq            #0xa4d53c
    //     0xa4d534: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4d538: stur            x4, [x0, #7]
    // 0xa4d53c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa4d53c: stur            w0, [x3, #0x17]
    //     0xa4d540: tbz             w0, #0, #0xa4d55c
    //     0xa4d544: ldurb           w16, [x3, #-1]
    //     0xa4d548: ldurb           w17, [x0, #-1]
    //     0xa4d54c: and             x16, x17, x16, lsr #2
    //     0xa4d550: tst             x16, HEAP, lsr #32
    //     0xa4d554: b.eq            #0xa4d55c
    //     0xa4d558: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa4d55c: mov             x1, x3
    // 0xa4d560: r0 = _onStart()
    //     0xa4d560: bl              #0x91e494  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::_onStart
    // 0xa4d564: ldur            x0, [fp, #-8]
    // 0xa4d568: LoadField: r1 = r0->field_f
    //     0xa4d568: ldur            w1, [x0, #0xf]
    // 0xa4d56c: DecompressPointer r1
    //     0xa4d56c: add             x1, x1, HEAP, lsl #32
    // 0xa4d570: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4d570: ldur            w2, [x1, #0x17]
    // 0xa4d574: DecompressPointer r2
    //     0xa4d574: add             x2, x2, HEAP, lsl #32
    // 0xa4d578: cmp             w2, NULL
    // 0xa4d57c: b.eq            #0xa4d610
    // 0xa4d580: LoadField: r3 = r1->field_13
    //     0xa4d580: ldur            w3, [x1, #0x13]
    // 0xa4d584: DecompressPointer r3
    //     0xa4d584: add             x3, x3, HEAP, lsl #32
    // 0xa4d588: cmp             w3, NULL
    // 0xa4d58c: b.eq            #0xa4d614
    // 0xa4d590: LoadField: r4 = r3->field_b
    //     0xa4d590: ldur            w4, [x3, #0xb]
    // 0xa4d594: r3 = LoadInt32Instr(r2)
    //     0xa4d594: sbfx            x3, x2, #1, #0x1f
    //     0xa4d598: tbz             w2, #0, #0xa4d5a0
    //     0xa4d59c: ldur            x3, [x2, #7]
    // 0xa4d5a0: r2 = LoadInt32Instr(r4)
    //     0xa4d5a0: sbfx            x2, x4, #1, #0x1f
    // 0xa4d5a4: cmp             x3, x2
    // 0xa4d5a8: b.lt            #0xa4d5f0
    // 0xa4d5ac: r0 = _cleanupAfterSteps()
    //     0xa4d5ac: bl              #0xa4d620  ; [package:showcaseview/src/showcase_widget.dart] ShowCaseWidgetState::_cleanupAfterSteps
    // 0xa4d5b0: ldur            x0, [fp, #-8]
    // 0xa4d5b4: LoadField: r1 = r0->field_f
    //     0xa4d5b4: ldur            w1, [x0, #0xf]
    // 0xa4d5b8: DecompressPointer r1
    //     0xa4d5b8: add             x1, x1, HEAP, lsl #32
    // 0xa4d5bc: LoadField: r0 = r1->field_b
    //     0xa4d5bc: ldur            w0, [x1, #0xb]
    // 0xa4d5c0: DecompressPointer r0
    //     0xa4d5c0: add             x0, x0, HEAP, lsl #32
    // 0xa4d5c4: cmp             w0, NULL
    // 0xa4d5c8: b.eq            #0xa4d618
    // 0xa4d5cc: LoadField: r1 = r0->field_f
    //     0xa4d5cc: ldur            w1, [x0, #0xf]
    // 0xa4d5d0: DecompressPointer r1
    //     0xa4d5d0: add             x1, x1, HEAP, lsl #32
    // 0xa4d5d4: cmp             w1, NULL
    // 0xa4d5d8: b.eq            #0xa4d61c
    // 0xa4d5dc: str             x1, [SP]
    // 0xa4d5e0: mov             x0, x1
    // 0xa4d5e4: ClosureCall
    //     0xa4d5e4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xa4d5e8: ldur            x2, [x0, #0x1f]
    //     0xa4d5ec: blr             x2
    // 0xa4d5f0: r0 = Null
    //     0xa4d5f0: mov             x0, NULL
    // 0xa4d5f4: LeaveFrame
    //     0xa4d5f4: mov             SP, fp
    //     0xa4d5f8: ldp             fp, lr, [SP], #0x10
    // 0xa4d5fc: ret
    //     0xa4d5fc: ret             
    // 0xa4d600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4d600: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4d604: b               #0xa4d4f0
    // 0xa4d608: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d608: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d60c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d60c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d610: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d610: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d614: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d614: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d618: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d618: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d61c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa4d61c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _cleanupAfterSteps(/* No info */) {
    // ** addr: 0xa4d620, size: 0x10
    // 0xa4d620: StoreField: r1->field_13 = rNULL
    //     0xa4d620: stur            NULL, [x1, #0x13]
    // 0xa4d624: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa4d624: stur            NULL, [x1, #0x17]
    // 0xa4d628: r0 = Null
    //     0xa4d628: mov             x0, NULL
    // 0xa4d62c: ret
    //     0xa4d62c: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0xa4d7dc, size: 0xd0
    // 0xa4d7dc: EnterFrame
    //     0xa4d7dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d7e0: mov             fp, SP
    // 0xa4d7e4: AllocStack(0x18)
    //     0xa4d7e4: sub             SP, SP, #0x18
    // 0xa4d7e8: SetupParameters(ShowCaseWidgetState this /* r1 => r3 */)
    //     0xa4d7e8: mov             x3, x1
    // 0xa4d7ec: LoadField: r2 = r3->field_13
    //     0xa4d7ec: ldur            w2, [x3, #0x13]
    // 0xa4d7f0: DecompressPointer r2
    //     0xa4d7f0: add             x2, x2, HEAP, lsl #32
    // 0xa4d7f4: cmp             w2, NULL
    // 0xa4d7f8: b.ne            #0xa4d804
    // 0xa4d7fc: r0 = Null
    //     0xa4d7fc: mov             x0, NULL
    // 0xa4d800: b               #0xa4d84c
    // 0xa4d804: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa4d804: ldur            w0, [x3, #0x17]
    // 0xa4d808: DecompressPointer r0
    //     0xa4d808: add             x0, x0, HEAP, lsl #32
    // 0xa4d80c: cmp             w0, NULL
    // 0xa4d810: b.eq            #0xa4d8a0
    // 0xa4d814: LoadField: r1 = r2->field_b
    //     0xa4d814: ldur            w1, [x2, #0xb]
    // 0xa4d818: r4 = LoadInt32Instr(r0)
    //     0xa4d818: sbfx            x4, x0, #1, #0x1f
    //     0xa4d81c: tbz             w0, #0, #0xa4d824
    //     0xa4d820: ldur            x4, [x0, #7]
    // 0xa4d824: r0 = LoadInt32Instr(r1)
    //     0xa4d824: sbfx            x0, x1, #1, #0x1f
    // 0xa4d828: mov             x1, x4
    // 0xa4d82c: cmp             x1, x0
    // 0xa4d830: b.hs            #0xa4d8a4
    // 0xa4d834: LoadField: r0 = r2->field_f
    //     0xa4d834: ldur            w0, [x2, #0xf]
    // 0xa4d838: DecompressPointer r0
    //     0xa4d838: add             x0, x0, HEAP, lsl #32
    // 0xa4d83c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa4d83c: add             x16, x0, x4, lsl #2
    //     0xa4d840: ldur            w1, [x16, #0xf]
    // 0xa4d844: DecompressPointer r1
    //     0xa4d844: add             x1, x1, HEAP, lsl #32
    // 0xa4d848: mov             x0, x1
    // 0xa4d84c: stur            x0, [fp, #-0x10]
    // 0xa4d850: LoadField: r1 = r3->field_b
    //     0xa4d850: ldur            w1, [x3, #0xb]
    // 0xa4d854: DecompressPointer r1
    //     0xa4d854: add             x1, x1, HEAP, lsl #32
    // 0xa4d858: cmp             w1, NULL
    // 0xa4d85c: b.eq            #0xa4d8a8
    // 0xa4d860: LoadField: r2 = r1->field_b
    //     0xa4d860: ldur            w2, [x1, #0xb]
    // 0xa4d864: DecompressPointer r2
    //     0xa4d864: add             x2, x2, HEAP, lsl #32
    // 0xa4d868: stur            x2, [fp, #-8]
    // 0xa4d86c: r0 = Builder()
    //     0xa4d86c: bl              #0x6a5c84  ; AllocateBuilderStub -> Builder (size=0x10)
    // 0xa4d870: mov             x1, x0
    // 0xa4d874: ldur            x0, [fp, #-8]
    // 0xa4d878: stur            x1, [fp, #-0x18]
    // 0xa4d87c: StoreField: r1->field_b = r0
    //     0xa4d87c: stur            w0, [x1, #0xb]
    // 0xa4d880: r0 = _InheritedShowCaseView()
    //     0xa4d880: bl              #0xa4d8ac  ; Allocate_InheritedShowCaseViewStub -> _InheritedShowCaseView (size=0x14)
    // 0xa4d884: ldur            x1, [fp, #-0x10]
    // 0xa4d888: StoreField: r0->field_f = r1
    //     0xa4d888: stur            w1, [x0, #0xf]
    // 0xa4d88c: ldur            x1, [fp, #-0x18]
    // 0xa4d890: StoreField: r0->field_b = r1
    //     0xa4d890: stur            w1, [x0, #0xb]
    // 0xa4d894: LeaveFrame
    //     0xa4d894: mov             SP, fp
    //     0xa4d898: ldp             fp, lr, [SP], #0x10
    // 0xa4d89c: ret
    //     0xa4d89c: ret             
    // 0xa4d8a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d8a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4d8a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4d8a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa4d8a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4d8a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4611, size: 0x14, field offset: 0x10
//   const constructor, 
class _InheritedShowCaseView extends InheritedWidget {

  _ updateShouldNotify(/* No info */) {
    // ** addr: 0xa89abc, size: 0xb0
    // 0xa89abc: EnterFrame
    //     0xa89abc: stp             fp, lr, [SP, #-0x10]!
    //     0xa89ac0: mov             fp, SP
    // 0xa89ac4: AllocStack(0x20)
    //     0xa89ac4: sub             SP, SP, #0x20
    // 0xa89ac8: SetupParameters(_InheritedShowCaseView this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xa89ac8: mov             x4, x1
    //     0xa89acc: mov             x3, x2
    //     0xa89ad0: stur            x1, [fp, #-8]
    //     0xa89ad4: stur            x2, [fp, #-0x10]
    // 0xa89ad8: CheckStackOverflow
    //     0xa89ad8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa89adc: cmp             SP, x16
    //     0xa89ae0: b.ls            #0xa89b64
    // 0xa89ae4: mov             x0, x3
    // 0xa89ae8: r2 = Null
    //     0xa89ae8: mov             x2, NULL
    // 0xa89aec: r1 = Null
    //     0xa89aec: mov             x1, NULL
    // 0xa89af0: r4 = 60
    //     0xa89af0: movz            x4, #0x3c
    // 0xa89af4: branchIfSmi(r0, 0xa89b00)
    //     0xa89af4: tbz             w0, #0, #0xa89b00
    // 0xa89af8: r4 = LoadClassIdInstr(r0)
    //     0xa89af8: ldur            x4, [x0, #-1]
    //     0xa89afc: ubfx            x4, x4, #0xc, #0x14
    // 0xa89b00: r17 = 4611
    //     0xa89b00: movz            x17, #0x1203
    // 0xa89b04: cmp             x4, x17
    // 0xa89b08: b.eq            #0xa89b20
    // 0xa89b0c: r8 = _InheritedShowCaseView
    //     0xa89b0c: add             x8, PP, #0x47, lsl #12  ; [pp+0x47cf8] Type: _InheritedShowCaseView
    //     0xa89b10: ldr             x8, [x8, #0xcf8]
    // 0xa89b14: r3 = Null
    //     0xa89b14: add             x3, PP, #0x47, lsl #12  ; [pp+0x47d00] Null
    //     0xa89b18: ldr             x3, [x3, #0xd00]
    // 0xa89b1c: r0 = DefaultTypeTest()
    //     0xa89b1c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xa89b20: ldur            x0, [fp, #-0x10]
    // 0xa89b24: LoadField: r1 = r0->field_f
    //     0xa89b24: ldur            w1, [x0, #0xf]
    // 0xa89b28: DecompressPointer r1
    //     0xa89b28: add             x1, x1, HEAP, lsl #32
    // 0xa89b2c: ldur            x0, [fp, #-8]
    // 0xa89b30: LoadField: r2 = r0->field_f
    //     0xa89b30: ldur            w2, [x0, #0xf]
    // 0xa89b34: DecompressPointer r2
    //     0xa89b34: add             x2, x2, HEAP, lsl #32
    // 0xa89b38: r0 = LoadClassIdInstr(r1)
    //     0xa89b38: ldur            x0, [x1, #-1]
    //     0xa89b3c: ubfx            x0, x0, #0xc, #0x14
    // 0xa89b40: stp             x2, x1, [SP]
    // 0xa89b44: mov             lr, x0
    // 0xa89b48: ldr             lr, [x21, lr, lsl #3]
    // 0xa89b4c: blr             lr
    // 0xa89b50: eor             x1, x0, #0x10
    // 0xa89b54: mov             x0, x1
    // 0xa89b58: LeaveFrame
    //     0xa89b58: mov             SP, fp
    //     0xa89b5c: ldp             fp, lr, [SP], #0x10
    // 0xa89b60: ret
    //     0xa89b60: ret             
    // 0xa89b64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa89b64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa89b68: b               #0xa89ae4
  }
}

// class id: 4690, size: 0x3c, field offset: 0xc
//   const constructor, 
class ShowCaseWidget extends StatefulWidget {

  static _ of(/* No info */) {
    // ** addr: 0x91e504, size: 0x68
    // 0x91e504: EnterFrame
    //     0x91e504: stp             fp, lr, [SP, #-0x10]!
    //     0x91e508: mov             fp, SP
    // 0x91e50c: AllocStack(0x10)
    //     0x91e50c: sub             SP, SP, #0x10
    // 0x91e510: CheckStackOverflow
    //     0x91e510: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e514: cmp             SP, x16
    //     0x91e518: b.ls            #0x91e564
    // 0x91e51c: r16 = <ShowCaseWidgetState>
    //     0x91e51c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35d58] TypeArguments: <ShowCaseWidgetState>
    //     0x91e520: ldr             x16, [x16, #0xd58]
    // 0x91e524: stp             x1, x16, [SP]
    // 0x91e528: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x91e528: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x91e52c: r0 = findAncestorStateOfType()
    //     0x91e52c: bl              #0x6a4a9c  ; [package:flutter/src/widgets/framework.dart] Element::findAncestorStateOfType
    // 0x91e530: cmp             w0, NULL
    // 0x91e534: b.eq            #0x91e544
    // 0x91e538: LeaveFrame
    //     0x91e538: mov             SP, fp
    //     0x91e53c: ldp             fp, lr, [SP], #0x10
    // 0x91e540: ret
    //     0x91e540: ret             
    // 0x91e544: r0 = _Exception()
    //     0x91e544: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0x91e548: mov             x1, x0
    // 0x91e54c: r0 = "Please provide ShowCaseView context"
    //     0x91e54c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35d60] "Please provide ShowCaseView context"
    //     0x91e550: ldr             x0, [x0, #0xd60]
    // 0x91e554: StoreField: r1->field_7 = r0
    //     0x91e554: stur            w0, [x1, #7]
    // 0x91e558: mov             x0, x1
    // 0x91e55c: r0 = Throw()
    //     0x91e55c: bl              #0xec04b8  ; ThrowStub
    // 0x91e560: brk             #0
    // 0x91e564: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e564: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e568: b               #0x91e51c
  }
  static _ activeTargetWidget(/* No info */) {
    // ** addr: 0x9d2374, size: 0x5c
    // 0x9d2374: EnterFrame
    //     0x9d2374: stp             fp, lr, [SP, #-0x10]!
    //     0x9d2378: mov             fp, SP
    // 0x9d237c: AllocStack(0x10)
    //     0x9d237c: sub             SP, SP, #0x10
    // 0x9d2380: CheckStackOverflow
    //     0x9d2380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d2384: cmp             SP, x16
    //     0x9d2388: b.ls            #0x9d23c8
    // 0x9d238c: r16 = <_InheritedShowCaseView>
    //     0x9d238c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47d70] TypeArguments: <_InheritedShowCaseView>
    //     0x9d2390: ldr             x16, [x16, #0xd70]
    // 0x9d2394: stp             x1, x16, [SP]
    // 0x9d2398: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9d2398: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9d239c: r0 = dependOnInheritedWidgetOfExactType()
    //     0x9d239c: bl              #0x6396d8  ; [package:flutter/src/widgets/framework.dart] Element::dependOnInheritedWidgetOfExactType
    // 0x9d23a0: cmp             w0, NULL
    // 0x9d23a4: b.ne            #0x9d23b0
    // 0x9d23a8: r0 = Null
    //     0x9d23a8: mov             x0, NULL
    // 0x9d23ac: b               #0x9d23bc
    // 0x9d23b0: LoadField: r1 = r0->field_f
    //     0x9d23b0: ldur            w1, [x0, #0xf]
    // 0x9d23b4: DecompressPointer r1
    //     0x9d23b4: add             x1, x1, HEAP, lsl #32
    // 0x9d23b8: mov             x0, x1
    // 0x9d23bc: LeaveFrame
    //     0x9d23bc: mov             SP, fp
    //     0x9d23c0: ldp             fp, lr, [SP], #0x10
    // 0x9d23c4: ret
    //     0x9d23c4: ret             
    // 0x9d23c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d23c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d23cc: b               #0x9d238c
  }
  _ createState(/* No info */) {
    // ** addr: 0xa9536c, size: 0x24
    // 0xa9536c: EnterFrame
    //     0xa9536c: stp             fp, lr, [SP, #-0x10]!
    //     0xa95370: mov             fp, SP
    // 0xa95374: mov             x0, x1
    // 0xa95378: r1 = <ShowCaseWidget>
    //     0xa95378: add             x1, PP, #0x35, lsl #12  ; [pp+0x35d20] TypeArguments: <ShowCaseWidget>
    //     0xa9537c: ldr             x1, [x1, #0xd20]
    // 0xa95380: r0 = ShowCaseWidgetState()
    //     0xa95380: bl              #0xa95390  ; AllocateShowCaseWidgetStateStub -> ShowCaseWidgetState (size=0x28)
    // 0xa95384: LeaveFrame
    //     0xa95384: mov             SP, fp
    //     0xa95388: ldp             fp, lr, [SP], #0x10
    // 0xa9538c: ret
    //     0xa9538c: ret             
  }
}
