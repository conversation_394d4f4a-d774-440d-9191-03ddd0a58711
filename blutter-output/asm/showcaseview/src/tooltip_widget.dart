// lib: , url: package:showcaseview/src/tooltip_widget.dart

// class id: 1051128, size: 0x8
class :: {
}

// class id: 4083, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ToolTipWidgetState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6fabac, size: 0x184
    // 0x6fabac: EnterFrame
    //     0x6fabac: stp             fp, lr, [SP, #-0x10]!
    //     0x6fabb0: mov             fp, SP
    // 0x6fabb4: AllocStack(0x20)
    //     0x6fabb4: sub             SP, SP, #0x20
    // 0x6fabb8: SetupParameters(__ToolTipWidgetState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6fabb8: mov             x0, x1
    //     0x6fabbc: stur            x1, [fp, #-8]
    //     0x6fabc0: stur            x2, [fp, #-0x10]
    // 0x6fabc4: CheckStackOverflow
    //     0x6fabc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fabc8: cmp             SP, x16
    //     0x6fabcc: b.ls            #0x6fad20
    // 0x6fabd0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6fabd0: ldur            w1, [x0, #0x17]
    // 0x6fabd4: DecompressPointer r1
    //     0x6fabd4: add             x1, x1, HEAP, lsl #32
    // 0x6fabd8: cmp             w1, NULL
    // 0x6fabdc: b.ne            #0x6fabe8
    // 0x6fabe0: mov             x1, x0
    // 0x6fabe4: r0 = _updateTickerModeNotifier()
    //     0x6fabe4: bl              #0x6fad54  ; [package:showcaseview/src/tooltip_widget.dart] __ToolTipWidgetState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6fabe8: ldur            x0, [fp, #-8]
    // 0x6fabec: LoadField: r1 = r0->field_13
    //     0x6fabec: ldur            w1, [x0, #0x13]
    // 0x6fabf0: DecompressPointer r1
    //     0x6fabf0: add             x1, x1, HEAP, lsl #32
    // 0x6fabf4: cmp             w1, NULL
    // 0x6fabf8: b.ne            #0x6fac90
    // 0x6fabfc: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x6fabfc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fac00: ldr             x0, [x0, #0x778]
    //     0x6fac04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fac08: cmp             w0, w16
    //     0x6fac0c: b.ne            #0x6fac18
    //     0x6fac10: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x6fac14: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fac18: r1 = <_WidgetTicker>
    //     0x6fac18: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8c0] TypeArguments: <_WidgetTicker>
    //     0x6fac1c: ldr             x1, [x1, #0x8c0]
    // 0x6fac20: stur            x0, [fp, #-0x18]
    // 0x6fac24: r0 = _Set()
    //     0x6fac24: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x6fac28: mov             x1, x0
    // 0x6fac2c: ldur            x0, [fp, #-0x18]
    // 0x6fac30: stur            x1, [fp, #-0x20]
    // 0x6fac34: StoreField: r1->field_1b = r0
    //     0x6fac34: stur            w0, [x1, #0x1b]
    // 0x6fac38: StoreField: r1->field_b = rZR
    //     0x6fac38: stur            wzr, [x1, #0xb]
    // 0x6fac3c: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x6fac3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fac40: ldr             x0, [x0, #0x780]
    //     0x6fac44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fac48: cmp             w0, w16
    //     0x6fac4c: b.ne            #0x6fac58
    //     0x6fac50: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x6fac54: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fac58: mov             x1, x0
    // 0x6fac5c: ldur            x0, [fp, #-0x20]
    // 0x6fac60: StoreField: r0->field_f = r1
    //     0x6fac60: stur            w1, [x0, #0xf]
    // 0x6fac64: StoreField: r0->field_13 = rZR
    //     0x6fac64: stur            wzr, [x0, #0x13]
    // 0x6fac68: ArrayStore: r0[0] = rZR  ; List_4
    //     0x6fac68: stur            wzr, [x0, #0x17]
    // 0x6fac6c: ldur            x1, [fp, #-8]
    // 0x6fac70: StoreField: r1->field_13 = r0
    //     0x6fac70: stur            w0, [x1, #0x13]
    //     0x6fac74: ldurb           w16, [x1, #-1]
    //     0x6fac78: ldurb           w17, [x0, #-1]
    //     0x6fac7c: and             x16, x17, x16, lsr #2
    //     0x6fac80: tst             x16, HEAP, lsr #32
    //     0x6fac84: b.eq            #0x6fac8c
    //     0x6fac88: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6fac8c: b               #0x6fac94
    // 0x6fac90: mov             x1, x0
    // 0x6fac94: ldur            x0, [fp, #-0x10]
    // 0x6fac98: r0 = _WidgetTicker()
    //     0x6fac98: bl              #0x6f03ec  ; Allocate_WidgetTickerStub -> _WidgetTicker (size=0x20)
    // 0x6fac9c: mov             x3, x0
    // 0x6faca0: ldur            x2, [fp, #-8]
    // 0x6faca4: stur            x3, [fp, #-0x18]
    // 0x6faca8: StoreField: r3->field_1b = r2
    //     0x6faca8: stur            w2, [x3, #0x1b]
    // 0x6facac: r0 = false
    //     0x6facac: add             x0, NULL, #0x30  ; false
    // 0x6facb0: StoreField: r3->field_b = r0
    //     0x6facb0: stur            w0, [x3, #0xb]
    // 0x6facb4: ldur            x0, [fp, #-0x10]
    // 0x6facb8: StoreField: r3->field_13 = r0
    //     0x6facb8: stur            w0, [x3, #0x13]
    // 0x6facbc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6facbc: ldur            w1, [x2, #0x17]
    // 0x6facc0: DecompressPointer r1
    //     0x6facc0: add             x1, x1, HEAP, lsl #32
    // 0x6facc4: cmp             w1, NULL
    // 0x6facc8: b.eq            #0x6fad28
    // 0x6faccc: r0 = LoadClassIdInstr(r1)
    //     0x6faccc: ldur            x0, [x1, #-1]
    //     0x6facd0: ubfx            x0, x0, #0xc, #0x14
    // 0x6facd4: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6facd4: movz            x17, #0x276f
    //     0x6facd8: movk            x17, #0x1, lsl #16
    //     0x6facdc: add             lr, x0, x17
    //     0x6face0: ldr             lr, [x21, lr, lsl #3]
    //     0x6face4: blr             lr
    // 0x6face8: eor             x2, x0, #0x10
    // 0x6facec: ldur            x1, [fp, #-0x18]
    // 0x6facf0: r0 = muted=()
    //     0x6facf0: bl              #0x6efbf4  ; [package:flutter/src/scheduler/ticker.dart] Ticker::muted=
    // 0x6facf4: ldur            x0, [fp, #-8]
    // 0x6facf8: LoadField: r1 = r0->field_13
    //     0x6facf8: ldur            w1, [x0, #0x13]
    // 0x6facfc: DecompressPointer r1
    //     0x6facfc: add             x1, x1, HEAP, lsl #32
    // 0x6fad00: cmp             w1, NULL
    // 0x6fad04: b.eq            #0x6fad2c
    // 0x6fad08: ldur            x2, [fp, #-0x18]
    // 0x6fad0c: r0 = add()
    //     0x6fad0c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x6fad10: ldur            x0, [fp, #-0x18]
    // 0x6fad14: LeaveFrame
    //     0x6fad14: mov             SP, fp
    //     0x6fad18: ldp             fp, lr, [SP], #0x10
    // 0x6fad1c: ret
    //     0x6fad1c: ret             
    // 0x6fad20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fad20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fad24: b               #0x6fabd0
    // 0x6fad28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fad28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fad2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fad2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6fad54, size: 0x124
    // 0x6fad54: EnterFrame
    //     0x6fad54: stp             fp, lr, [SP, #-0x10]!
    //     0x6fad58: mov             fp, SP
    // 0x6fad5c: AllocStack(0x18)
    //     0x6fad5c: sub             SP, SP, #0x18
    // 0x6fad60: SetupParameters(__ToolTipWidgetState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6fad60: mov             x2, x1
    //     0x6fad64: stur            x1, [fp, #-8]
    // 0x6fad68: CheckStackOverflow
    //     0x6fad68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fad6c: cmp             SP, x16
    //     0x6fad70: b.ls            #0x6fae6c
    // 0x6fad74: LoadField: r1 = r2->field_f
    //     0x6fad74: ldur            w1, [x2, #0xf]
    // 0x6fad78: DecompressPointer r1
    //     0x6fad78: add             x1, x1, HEAP, lsl #32
    // 0x6fad7c: cmp             w1, NULL
    // 0x6fad80: b.eq            #0x6fae74
    // 0x6fad84: r0 = getNotifier()
    //     0x6fad84: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6fad88: mov             x3, x0
    // 0x6fad8c: ldur            x0, [fp, #-8]
    // 0x6fad90: stur            x3, [fp, #-0x18]
    // 0x6fad94: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6fad94: ldur            w4, [x0, #0x17]
    // 0x6fad98: DecompressPointer r4
    //     0x6fad98: add             x4, x4, HEAP, lsl #32
    // 0x6fad9c: stur            x4, [fp, #-0x10]
    // 0x6fada0: cmp             w3, w4
    // 0x6fada4: b.ne            #0x6fadb8
    // 0x6fada8: r0 = Null
    //     0x6fada8: mov             x0, NULL
    // 0x6fadac: LeaveFrame
    //     0x6fadac: mov             SP, fp
    //     0x6fadb0: ldp             fp, lr, [SP], #0x10
    // 0x6fadb4: ret
    //     0x6fadb4: ret             
    // 0x6fadb8: cmp             w4, NULL
    // 0x6fadbc: b.eq            #0x6fae00
    // 0x6fadc0: mov             x2, x0
    // 0x6fadc4: r1 = Function '_updateTickers@364311458':.
    //     0x6fadc4: add             x1, PP, #0x57, lsl #12  ; [pp+0x578d8] AnonymousClosure: (0x6fae78), in [package:showcaseview/src/tooltip_widget.dart] __ToolTipWidgetState&State&TickerProviderStateMixin::_updateTickers (0x6faeb0)
    //     0x6fadc8: ldr             x1, [x1, #0x8d8]
    // 0x6fadcc: r0 = AllocateClosure()
    //     0x6fadcc: bl              #0xec1630  ; AllocateClosureStub
    // 0x6fadd0: ldur            x1, [fp, #-0x10]
    // 0x6fadd4: r2 = LoadClassIdInstr(r1)
    //     0x6fadd4: ldur            x2, [x1, #-1]
    //     0x6fadd8: ubfx            x2, x2, #0xc, #0x14
    // 0x6faddc: mov             x16, x0
    // 0x6fade0: mov             x0, x2
    // 0x6fade4: mov             x2, x16
    // 0x6fade8: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6fade8: movz            x17, #0xbf5c
    //     0x6fadec: add             lr, x0, x17
    //     0x6fadf0: ldr             lr, [x21, lr, lsl #3]
    //     0x6fadf4: blr             lr
    // 0x6fadf8: ldur            x0, [fp, #-8]
    // 0x6fadfc: ldur            x3, [fp, #-0x18]
    // 0x6fae00: mov             x2, x0
    // 0x6fae04: r1 = Function '_updateTickers@364311458':.
    //     0x6fae04: add             x1, PP, #0x57, lsl #12  ; [pp+0x578d8] AnonymousClosure: (0x6fae78), in [package:showcaseview/src/tooltip_widget.dart] __ToolTipWidgetState&State&TickerProviderStateMixin::_updateTickers (0x6faeb0)
    //     0x6fae08: ldr             x1, [x1, #0x8d8]
    // 0x6fae0c: r0 = AllocateClosure()
    //     0x6fae0c: bl              #0xec1630  ; AllocateClosureStub
    // 0x6fae10: ldur            x3, [fp, #-0x18]
    // 0x6fae14: r1 = LoadClassIdInstr(r3)
    //     0x6fae14: ldur            x1, [x3, #-1]
    //     0x6fae18: ubfx            x1, x1, #0xc, #0x14
    // 0x6fae1c: mov             x2, x0
    // 0x6fae20: mov             x0, x1
    // 0x6fae24: mov             x1, x3
    // 0x6fae28: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6fae28: movz            x17, #0xc407
    //     0x6fae2c: add             lr, x0, x17
    //     0x6fae30: ldr             lr, [x21, lr, lsl #3]
    //     0x6fae34: blr             lr
    // 0x6fae38: ldur            x0, [fp, #-0x18]
    // 0x6fae3c: ldur            x1, [fp, #-8]
    // 0x6fae40: ArrayStore: r1[0] = r0  ; List_4
    //     0x6fae40: stur            w0, [x1, #0x17]
    //     0x6fae44: ldurb           w16, [x1, #-1]
    //     0x6fae48: ldurb           w17, [x0, #-1]
    //     0x6fae4c: and             x16, x17, x16, lsr #2
    //     0x6fae50: tst             x16, HEAP, lsr #32
    //     0x6fae54: b.eq            #0x6fae5c
    //     0x6fae58: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6fae5c: r0 = Null
    //     0x6fae5c: mov             x0, NULL
    // 0x6fae60: LeaveFrame
    //     0x6fae60: mov             SP, fp
    //     0x6fae64: ldp             fp, lr, [SP], #0x10
    // 0x6fae68: ret
    //     0x6fae68: ret             
    // 0x6fae6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fae6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fae70: b               #0x6fad74
    // 0x6fae74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fae74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTickers(dynamic) {
    // ** addr: 0x6fae78, size: 0x38
    // 0x6fae78: EnterFrame
    //     0x6fae78: stp             fp, lr, [SP, #-0x10]!
    //     0x6fae7c: mov             fp, SP
    // 0x6fae80: ldr             x0, [fp, #0x10]
    // 0x6fae84: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6fae84: ldur            w1, [x0, #0x17]
    // 0x6fae88: DecompressPointer r1
    //     0x6fae88: add             x1, x1, HEAP, lsl #32
    // 0x6fae8c: CheckStackOverflow
    //     0x6fae8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fae90: cmp             SP, x16
    //     0x6fae94: b.ls            #0x6faea8
    // 0x6fae98: r0 = _updateTickers()
    //     0x6fae98: bl              #0x6faeb0  ; [package:showcaseview/src/tooltip_widget.dart] __ToolTipWidgetState&State&TickerProviderStateMixin::_updateTickers
    // 0x6fae9c: LeaveFrame
    //     0x6fae9c: mov             SP, fp
    //     0x6faea0: ldp             fp, lr, [SP], #0x10
    // 0x6faea4: ret
    //     0x6faea4: ret             
    // 0x6faea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6faea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6faeac: b               #0x6fae98
  }
  _ _updateTickers(/* No info */) {
    // ** addr: 0x6faeb0, size: 0x164
    // 0x6faeb0: EnterFrame
    //     0x6faeb0: stp             fp, lr, [SP, #-0x10]!
    //     0x6faeb4: mov             fp, SP
    // 0x6faeb8: AllocStack(0x20)
    //     0x6faeb8: sub             SP, SP, #0x20
    // 0x6faebc: SetupParameters(__ToolTipWidgetState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6faebc: mov             x2, x1
    //     0x6faec0: stur            x1, [fp, #-8]
    // 0x6faec4: CheckStackOverflow
    //     0x6faec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6faec8: cmp             SP, x16
    //     0x6faecc: b.ls            #0x6faffc
    // 0x6faed0: LoadField: r0 = r2->field_13
    //     0x6faed0: ldur            w0, [x2, #0x13]
    // 0x6faed4: DecompressPointer r0
    //     0x6faed4: add             x0, x0, HEAP, lsl #32
    // 0x6faed8: cmp             w0, NULL
    // 0x6faedc: b.eq            #0x6fafec
    // 0x6faee0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6faee0: ldur            w1, [x2, #0x17]
    // 0x6faee4: DecompressPointer r1
    //     0x6faee4: add             x1, x1, HEAP, lsl #32
    // 0x6faee8: cmp             w1, NULL
    // 0x6faeec: b.eq            #0x6fb004
    // 0x6faef0: r0 = LoadClassIdInstr(r1)
    //     0x6faef0: ldur            x0, [x1, #-1]
    //     0x6faef4: ubfx            x0, x0, #0xc, #0x14
    // 0x6faef8: r0 = GDT[cid_x0 + 0x1276f]()
    //     0x6faef8: movz            x17, #0x276f
    //     0x6faefc: movk            x17, #0x1, lsl #16
    //     0x6faf00: add             lr, x0, x17
    //     0x6faf04: ldr             lr, [x21, lr, lsl #3]
    //     0x6faf08: blr             lr
    // 0x6faf0c: eor             x2, x0, #0x10
    // 0x6faf10: ldur            x0, [fp, #-8]
    // 0x6faf14: stur            x2, [fp, #-0x10]
    // 0x6faf18: LoadField: r1 = r0->field_13
    //     0x6faf18: ldur            w1, [x0, #0x13]
    // 0x6faf1c: DecompressPointer r1
    //     0x6faf1c: add             x1, x1, HEAP, lsl #32
    // 0x6faf20: cmp             w1, NULL
    // 0x6faf24: b.eq            #0x6fb008
    // 0x6faf28: r0 = iterator()
    //     0x6faf28: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x6faf2c: stur            x0, [fp, #-0x18]
    // 0x6faf30: LoadField: r2 = r0->field_7
    //     0x6faf30: ldur            w2, [x0, #7]
    // 0x6faf34: DecompressPointer r2
    //     0x6faf34: add             x2, x2, HEAP, lsl #32
    // 0x6faf38: stur            x2, [fp, #-8]
    // 0x6faf3c: ldur            x3, [fp, #-0x10]
    // 0x6faf40: CheckStackOverflow
    //     0x6faf40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6faf44: cmp             SP, x16
    //     0x6faf48: b.ls            #0x6fb00c
    // 0x6faf4c: mov             x1, x0
    // 0x6faf50: r0 = moveNext()
    //     0x6faf50: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x6faf54: tbnz            w0, #4, #0x6fafec
    // 0x6faf58: ldur            x3, [fp, #-0x18]
    // 0x6faf5c: LoadField: r4 = r3->field_33
    //     0x6faf5c: ldur            w4, [x3, #0x33]
    // 0x6faf60: DecompressPointer r4
    //     0x6faf60: add             x4, x4, HEAP, lsl #32
    // 0x6faf64: stur            x4, [fp, #-0x20]
    // 0x6faf68: cmp             w4, NULL
    // 0x6faf6c: b.ne            #0x6fafa0
    // 0x6faf70: mov             x0, x4
    // 0x6faf74: ldur            x2, [fp, #-8]
    // 0x6faf78: r1 = Null
    //     0x6faf78: mov             x1, NULL
    // 0x6faf7c: cmp             w2, NULL
    // 0x6faf80: b.eq            #0x6fafa0
    // 0x6faf84: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6faf84: ldur            w4, [x2, #0x17]
    // 0x6faf88: DecompressPointer r4
    //     0x6faf88: add             x4, x4, HEAP, lsl #32
    // 0x6faf8c: r8 = X0
    //     0x6faf8c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6faf90: LoadField: r9 = r4->field_7
    //     0x6faf90: ldur            x9, [x4, #7]
    // 0x6faf94: r3 = Null
    //     0x6faf94: add             x3, PP, #0x57, lsl #12  ; [pp+0x578c8] Null
    //     0x6faf98: ldr             x3, [x3, #0x8c8]
    // 0x6faf9c: blr             x9
    // 0x6fafa0: ldur            x2, [fp, #-0x10]
    // 0x6fafa4: ldur            x0, [fp, #-0x20]
    // 0x6fafa8: LoadField: r1 = r0->field_b
    //     0x6fafa8: ldur            w1, [x0, #0xb]
    // 0x6fafac: DecompressPointer r1
    //     0x6fafac: add             x1, x1, HEAP, lsl #32
    // 0x6fafb0: cmp             w2, w1
    // 0x6fafb4: b.eq            #0x6fafe0
    // 0x6fafb8: StoreField: r0->field_b = r2
    //     0x6fafb8: stur            w2, [x0, #0xb]
    // 0x6fafbc: tbnz            w2, #4, #0x6fafcc
    // 0x6fafc0: mov             x1, x0
    // 0x6fafc4: r0 = unscheduleTick()
    //     0x6fafc4: bl              #0x656684  ; [package:flutter/src/scheduler/ticker.dart] Ticker::unscheduleTick
    // 0x6fafc8: b               #0x6fafe0
    // 0x6fafcc: mov             x1, x0
    // 0x6fafd0: r0 = shouldScheduleTick()
    //     0x6fafd0: bl              #0x655b90  ; [package:flutter/src/scheduler/ticker.dart] Ticker::shouldScheduleTick
    // 0x6fafd4: tbnz            w0, #4, #0x6fafe0
    // 0x6fafd8: ldur            x1, [fp, #-0x20]
    // 0x6fafdc: r0 = scheduleTick()
    //     0x6fafdc: bl              #0x655924  ; [package:flutter/src/scheduler/ticker.dart] Ticker::scheduleTick
    // 0x6fafe0: ldur            x0, [fp, #-0x18]
    // 0x6fafe4: ldur            x2, [fp, #-8]
    // 0x6fafe8: b               #0x6faf3c
    // 0x6fafec: r0 = Null
    //     0x6fafec: mov             x0, NULL
    // 0x6faff0: LeaveFrame
    //     0x6faff0: mov             SP, fp
    //     0x6faff4: ldp             fp, lr, [SP], #0x10
    // 0x6faff8: ret
    //     0x6faff8: ret             
    // 0x6faffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6faffc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fb000: b               #0x6faed0
    // 0x6fb004: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fb004: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fb008: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fb008: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fb00c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fb00c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fb010: b               #0x6faf4c
  }
  _ _removeTicker(/* No info */) {
    // ** addr: 0x7d7d5c, size: 0x48
    // 0x7d7d5c: EnterFrame
    //     0x7d7d5c: stp             fp, lr, [SP, #-0x10]!
    //     0x7d7d60: mov             fp, SP
    // 0x7d7d64: CheckStackOverflow
    //     0x7d7d64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d7d68: cmp             SP, x16
    //     0x7d7d6c: b.ls            #0x7d7d98
    // 0x7d7d70: LoadField: r0 = r1->field_13
    //     0x7d7d70: ldur            w0, [x1, #0x13]
    // 0x7d7d74: DecompressPointer r0
    //     0x7d7d74: add             x0, x0, HEAP, lsl #32
    // 0x7d7d78: cmp             w0, NULL
    // 0x7d7d7c: b.eq            #0x7d7da0
    // 0x7d7d80: mov             x1, x0
    // 0x7d7d84: r0 = remove()
    //     0x7d7d84: bl              #0xd89c04  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x7d7d88: r0 = Null
    //     0x7d7d88: mov             x0, NULL
    // 0x7d7d8c: LeaveFrame
    //     0x7d7d8c: mov             SP, fp
    //     0x7d7d90: ldp             fp, lr, [SP], #0x10
    // 0x7d7d94: ret
    //     0x7d7d94: ret             
    // 0x7d7d98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d7d98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d7d9c: b               #0x7d7d70
    // 0x7d7da0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d7da0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa8416c, size: 0x94
    // 0xa8416c: EnterFrame
    //     0xa8416c: stp             fp, lr, [SP, #-0x10]!
    //     0xa84170: mov             fp, SP
    // 0xa84174: AllocStack(0x10)
    //     0xa84174: sub             SP, SP, #0x10
    // 0xa84178: SetupParameters(__ToolTipWidgetState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa84178: mov             x0, x1
    //     0xa8417c: stur            x1, [fp, #-0x10]
    // 0xa84180: CheckStackOverflow
    //     0xa84180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84184: cmp             SP, x16
    //     0xa84188: b.ls            #0xa841f8
    // 0xa8418c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa8418c: ldur            w3, [x0, #0x17]
    // 0xa84190: DecompressPointer r3
    //     0xa84190: add             x3, x3, HEAP, lsl #32
    // 0xa84194: stur            x3, [fp, #-8]
    // 0xa84198: cmp             w3, NULL
    // 0xa8419c: b.ne            #0xa841a8
    // 0xa841a0: mov             x1, x0
    // 0xa841a4: b               #0xa841e4
    // 0xa841a8: mov             x2, x0
    // 0xa841ac: r1 = Function '_updateTickers@364311458':.
    //     0xa841ac: add             x1, PP, #0x57, lsl #12  ; [pp+0x578d8] AnonymousClosure: (0x6fae78), in [package:showcaseview/src/tooltip_widget.dart] __ToolTipWidgetState&State&TickerProviderStateMixin::_updateTickers (0x6faeb0)
    //     0xa841b0: ldr             x1, [x1, #0x8d8]
    // 0xa841b4: r0 = AllocateClosure()
    //     0xa841b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa841b8: ldur            x1, [fp, #-8]
    // 0xa841bc: r2 = LoadClassIdInstr(r1)
    //     0xa841bc: ldur            x2, [x1, #-1]
    //     0xa841c0: ubfx            x2, x2, #0xc, #0x14
    // 0xa841c4: mov             x16, x0
    // 0xa841c8: mov             x0, x2
    // 0xa841cc: mov             x2, x16
    // 0xa841d0: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa841d0: movz            x17, #0xbf5c
    //     0xa841d4: add             lr, x0, x17
    //     0xa841d8: ldr             lr, [x21, lr, lsl #3]
    //     0xa841dc: blr             lr
    // 0xa841e0: ldur            x1, [fp, #-0x10]
    // 0xa841e4: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa841e4: stur            NULL, [x1, #0x17]
    // 0xa841e8: r0 = Null
    //     0xa841e8: mov             x0, NULL
    // 0xa841ec: LeaveFrame
    //     0xa841ec: mov             SP, fp
    //     0xa841f0: ldp             fp, lr, [SP], #0x10
    // 0xa841f4: ret
    //     0xa841f4: ret             
    // 0xa841f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa841f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa841fc: b               #0xa8418c
  }
  _ activate(/* No info */) {
    // ** addr: 0xa860a4, size: 0x48
    // 0xa860a4: EnterFrame
    //     0xa860a4: stp             fp, lr, [SP, #-0x10]!
    //     0xa860a8: mov             fp, SP
    // 0xa860ac: AllocStack(0x8)
    //     0xa860ac: sub             SP, SP, #8
    // 0xa860b0: SetupParameters(__ToolTipWidgetState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa860b0: mov             x0, x1
    //     0xa860b4: stur            x1, [fp, #-8]
    // 0xa860b8: CheckStackOverflow
    //     0xa860b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa860bc: cmp             SP, x16
    //     0xa860c0: b.ls            #0xa860e4
    // 0xa860c4: mov             x1, x0
    // 0xa860c8: r0 = _updateTickerModeNotifier()
    //     0xa860c8: bl              #0x6fad54  ; [package:showcaseview/src/tooltip_widget.dart] __ToolTipWidgetState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa860cc: ldur            x1, [fp, #-8]
    // 0xa860d0: r0 = _updateTickers()
    //     0xa860d0: bl              #0x6faeb0  ; [package:showcaseview/src/tooltip_widget.dart] __ToolTipWidgetState&State&TickerProviderStateMixin::_updateTickers
    // 0xa860d4: r0 = Null
    //     0xa860d4: mov             x0, NULL
    // 0xa860d8: LeaveFrame
    //     0xa860d8: mov             SP, fp
    //     0xa860dc: ldp             fp, lr, [SP], #0x10
    // 0xa860e0: ret
    //     0xa860e0: ret             
    // 0xa860e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa860e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa860e8: b               #0xa860c4
  }
}

// class id: 4084, size: 0x4c, field offset: 0x1c
class _ToolTipWidgetState extends __ToolTipWidgetState&State&TickerProviderStateMixin {

  late final AnimationController _scaleAnimationController; // offset: 0x2c
  late final Animation<double> _movingAnimation; // offset: 0x28
  late final AnimationController _movingAnimationController; // offset: 0x24

  _ initState(/* No info */) {
    // ** addr: 0x9807c4, size: 0x3c4
    // 0x9807c4: EnterFrame
    //     0x9807c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9807c8: mov             fp, SP
    // 0x9807cc: AllocStack(0x30)
    //     0x9807cc: sub             SP, SP, #0x30
    // 0x9807d0: SetupParameters(_ToolTipWidgetState this /* r1 => r2, fp-0x8 */)
    //     0x9807d0: mov             x2, x1
    //     0x9807d4: stur            x1, [fp, #-8]
    // 0x9807d8: CheckStackOverflow
    //     0x9807d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9807dc: cmp             SP, x16
    //     0x9807e0: b.ls            #0x980b68
    // 0x9807e4: r1 = 1
    //     0x9807e4: movz            x1, #0x1
    // 0x9807e8: r0 = AllocateContext()
    //     0x9807e8: bl              #0xec126c  ; AllocateContextStub
    // 0x9807ec: mov             x3, x0
    // 0x9807f0: ldur            x0, [fp, #-8]
    // 0x9807f4: stur            x3, [fp, #-0x20]
    // 0x9807f8: StoreField: r3->field_f = r0
    //     0x9807f8: stur            w0, [x3, #0xf]
    // 0x9807fc: r1 = LoadStaticField(0x7d4)
    //     0x9807fc: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x980800: ldr             x1, [x1, #0xfa8]
    // 0x980804: cmp             w1, NULL
    // 0x980808: b.eq            #0x980b70
    // 0x98080c: LoadField: r4 = r1->field_53
    //     0x98080c: ldur            w4, [x1, #0x53]
    // 0x980810: DecompressPointer r4
    //     0x980810: add             x4, x4, HEAP, lsl #32
    // 0x980814: stur            x4, [fp, #-0x18]
    // 0x980818: LoadField: r5 = r4->field_7
    //     0x980818: ldur            w5, [x4, #7]
    // 0x98081c: DecompressPointer r5
    //     0x98081c: add             x5, x5, HEAP, lsl #32
    // 0x980820: mov             x2, x3
    // 0x980824: stur            x5, [fp, #-0x10]
    // 0x980828: r1 = Function '<anonymous closure>':.
    //     0x980828: add             x1, PP, #0x57, lsl #12  ; [pp+0x57970] AnonymousClosure: (0x980d5c), in [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::initState (0x9807c4)
    //     0x98082c: ldr             x1, [x1, #0x970]
    // 0x980830: r0 = AllocateClosure()
    //     0x980830: bl              #0xec1630  ; AllocateClosureStub
    // 0x980834: ldur            x2, [fp, #-0x10]
    // 0x980838: mov             x3, x0
    // 0x98083c: r1 = Null
    //     0x98083c: mov             x1, NULL
    // 0x980840: stur            x3, [fp, #-0x10]
    // 0x980844: cmp             w2, NULL
    // 0x980848: b.eq            #0x980868
    // 0x98084c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x98084c: ldur            w4, [x2, #0x17]
    // 0x980850: DecompressPointer r4
    //     0x980850: add             x4, x4, HEAP, lsl #32
    // 0x980854: r8 = X0
    //     0x980854: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x980858: LoadField: r9 = r4->field_7
    //     0x980858: ldur            x9, [x4, #7]
    // 0x98085c: r3 = Null
    //     0x98085c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57978] Null
    //     0x980860: ldr             x3, [x3, #0x978]
    // 0x980864: blr             x9
    // 0x980868: ldur            x0, [fp, #-0x18]
    // 0x98086c: LoadField: r1 = r0->field_b
    //     0x98086c: ldur            w1, [x0, #0xb]
    // 0x980870: LoadField: r2 = r0->field_f
    //     0x980870: ldur            w2, [x0, #0xf]
    // 0x980874: DecompressPointer r2
    //     0x980874: add             x2, x2, HEAP, lsl #32
    // 0x980878: LoadField: r3 = r2->field_b
    //     0x980878: ldur            w3, [x2, #0xb]
    // 0x98087c: r2 = LoadInt32Instr(r1)
    //     0x98087c: sbfx            x2, x1, #1, #0x1f
    // 0x980880: stur            x2, [fp, #-0x28]
    // 0x980884: r1 = LoadInt32Instr(r3)
    //     0x980884: sbfx            x1, x3, #1, #0x1f
    // 0x980888: cmp             x2, x1
    // 0x98088c: b.ne            #0x980898
    // 0x980890: mov             x1, x0
    // 0x980894: r0 = _growToNextCapacity()
    //     0x980894: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x980898: ldur            x3, [fp, #-8]
    // 0x98089c: ldur            x0, [fp, #-0x18]
    // 0x9808a0: ldur            x2, [fp, #-0x28]
    // 0x9808a4: add             x1, x2, #1
    // 0x9808a8: lsl             x4, x1, #1
    // 0x9808ac: StoreField: r0->field_b = r4
    //     0x9808ac: stur            w4, [x0, #0xb]
    // 0x9808b0: LoadField: r1 = r0->field_f
    //     0x9808b0: ldur            w1, [x0, #0xf]
    // 0x9808b4: DecompressPointer r1
    //     0x9808b4: add             x1, x1, HEAP, lsl #32
    // 0x9808b8: ldur            x0, [fp, #-0x10]
    // 0x9808bc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x9808bc: add             x25, x1, x2, lsl #2
    //     0x9808c0: add             x25, x25, #0xf
    //     0x9808c4: str             w0, [x25]
    //     0x9808c8: tbz             w0, #0, #0x9808e4
    //     0x9808cc: ldurb           w16, [x1, #-1]
    //     0x9808d0: ldurb           w17, [x0, #-1]
    //     0x9808d4: and             x16, x17, x16, lsr #2
    //     0x9808d8: tst             x16, HEAP, lsr #32
    //     0x9808dc: b.eq            #0x9808e4
    //     0x9808e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x9808e4: LoadField: r0 = r3->field_b
    //     0x9808e4: ldur            w0, [x3, #0xb]
    // 0x9808e8: DecompressPointer r0
    //     0x9808e8: add             x0, x0, HEAP, lsl #32
    // 0x9808ec: cmp             w0, NULL
    // 0x9808f0: b.eq            #0x980b74
    // 0x9808f4: r1 = <double>
    //     0x9808f4: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9808f8: r0 = AnimationController()
    //     0x9808f8: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x9808fc: stur            x0, [fp, #-0x10]
    // 0x980900: r16 = Instance_Duration
    //     0x980900: add             x16, PP, #0x28, lsl #12  ; [pp+0x28130] Obj!Duration@e3a131
    //     0x980904: ldr             x16, [x16, #0x130]
    // 0x980908: str             x16, [SP]
    // 0x98090c: mov             x1, x0
    // 0x980910: ldur            x2, [fp, #-8]
    // 0x980914: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x980914: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x980918: ldr             x4, [x4, #0x408]
    // 0x98091c: r0 = AnimationController()
    //     0x98091c: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x980920: ldur            x2, [fp, #-8]
    // 0x980924: LoadField: r0 = r2->field_23
    //     0x980924: ldur            w0, [x2, #0x23]
    // 0x980928: DecompressPointer r0
    //     0x980928: add             x0, x0, HEAP, lsl #32
    // 0x98092c: r16 = Sentinel
    //     0x98092c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x980930: cmp             w0, w16
    // 0x980934: b.eq            #0x98094c
    // 0x980938: r16 = "_movingAnimationController@**********"
    //     0x980938: add             x16, PP, #0x57, lsl #12  ; [pp+0x57988] "_movingAnimationController@**********"
    //     0x98093c: ldr             x16, [x16, #0x988]
    // 0x980940: str             x16, [SP]
    // 0x980944: r0 = _throwFieldAlreadyInitialized()
    //     0x980944: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x980948: ldur            x2, [fp, #-8]
    // 0x98094c: ldur            x0, [fp, #-0x10]
    // 0x980950: StoreField: r2->field_23 = r0
    //     0x980950: stur            w0, [x2, #0x23]
    //     0x980954: ldurb           w16, [x2, #-1]
    //     0x980958: ldurb           w17, [x0, #-1]
    //     0x98095c: and             x16, x17, x16, lsr #2
    //     0x980960: tst             x16, HEAP, lsr #32
    //     0x980964: b.eq            #0x98096c
    //     0x980968: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x98096c: r1 = <double>
    //     0x98096c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x980970: r0 = CurvedAnimation()
    //     0x980970: bl              #0x7e34d0  ; AllocateCurvedAnimationStub -> CurvedAnimation (size=0x1c)
    // 0x980974: mov             x1, x0
    // 0x980978: ldur            x3, [fp, #-0x10]
    // 0x98097c: r2 = Instance_Cubic
    //     0x98097c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb00] Obj!Cubic@e14e01
    //     0x980980: ldr             x2, [x2, #0xb00]
    // 0x980984: stur            x0, [fp, #-0x10]
    // 0x980988: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x980988: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x98098c: r0 = CurvedAnimation()
    //     0x98098c: bl              #0x7e338c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::CurvedAnimation
    // 0x980990: ldur            x2, [fp, #-8]
    // 0x980994: LoadField: r0 = r2->field_27
    //     0x980994: ldur            w0, [x2, #0x27]
    // 0x980998: DecompressPointer r0
    //     0x980998: add             x0, x0, HEAP, lsl #32
    // 0x98099c: r16 = Sentinel
    //     0x98099c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9809a0: cmp             w0, w16
    // 0x9809a4: b.eq            #0x9809bc
    // 0x9809a8: r16 = "_movingAnimation@**********"
    //     0x9809a8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57990] "_movingAnimation@**********"
    //     0x9809ac: ldr             x16, [x16, #0x990]
    // 0x9809b0: str             x16, [SP]
    // 0x9809b4: r0 = _throwFieldAlreadyInitialized()
    //     0x9809b4: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x9809b8: ldur            x2, [fp, #-8]
    // 0x9809bc: ldur            x0, [fp, #-0x10]
    // 0x9809c0: StoreField: r2->field_27 = r0
    //     0x9809c0: stur            w0, [x2, #0x27]
    //     0x9809c4: ldurb           w16, [x2, #-1]
    //     0x9809c8: ldurb           w17, [x0, #-1]
    //     0x9809cc: and             x16, x17, x16, lsr #2
    //     0x9809d0: tst             x16, HEAP, lsr #32
    //     0x9809d4: b.eq            #0x9809dc
    //     0x9809d8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9809dc: LoadField: r0 = r2->field_b
    //     0x9809dc: ldur            w0, [x2, #0xb]
    // 0x9809e0: DecompressPointer r0
    //     0x9809e0: add             x0, x0, HEAP, lsl #32
    // 0x9809e4: cmp             w0, NULL
    // 0x9809e8: b.eq            #0x980b78
    // 0x9809ec: r1 = <double>
    //     0x9809ec: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9809f0: r0 = AnimationController()
    //     0x9809f0: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x9809f4: stur            x0, [fp, #-0x10]
    // 0x9809f8: r16 = Instance_Duration
    //     0x9809f8: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0x9809fc: ldr             x16, [x16, #0x9c0]
    // 0x980a00: str             x16, [SP]
    // 0x980a04: mov             x1, x0
    // 0x980a08: ldur            x2, [fp, #-8]
    // 0x980a0c: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x980a0c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x980a10: ldr             x4, [x4, #0x408]
    // 0x980a14: r0 = AnimationController()
    //     0x980a14: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x980a18: ldur            x0, [fp, #-8]
    // 0x980a1c: LoadField: r1 = r0->field_2b
    //     0x980a1c: ldur            w1, [x0, #0x2b]
    // 0x980a20: DecompressPointer r1
    //     0x980a20: add             x1, x1, HEAP, lsl #32
    // 0x980a24: r16 = Sentinel
    //     0x980a24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x980a28: cmp             w1, w16
    // 0x980a2c: b.ne            #0x980a38
    // 0x980a30: mov             x2, x0
    // 0x980a34: b               #0x980a4c
    // 0x980a38: r16 = "_scaleAnimationController@**********"
    //     0x980a38: add             x16, PP, #0x57, lsl #12  ; [pp+0x57998] "_scaleAnimationController@**********"
    //     0x980a3c: ldr             x16, [x16, #0x998]
    // 0x980a40: str             x16, [SP]
    // 0x980a44: r0 = _throwFieldAlreadyInitialized()
    //     0x980a44: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x980a48: ldur            x2, [fp, #-8]
    // 0x980a4c: ldur            x0, [fp, #-0x10]
    // 0x980a50: StoreField: r2->field_2b = r0
    //     0x980a50: stur            w0, [x2, #0x2b]
    //     0x980a54: ldurb           w16, [x2, #-1]
    //     0x980a58: ldurb           w17, [x0, #-1]
    //     0x980a5c: and             x16, x17, x16, lsr #2
    //     0x980a60: tst             x16, HEAP, lsr #32
    //     0x980a64: b.eq            #0x980a6c
    //     0x980a68: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x980a6c: LoadField: r0 = r2->field_b
    //     0x980a6c: ldur            w0, [x2, #0xb]
    // 0x980a70: DecompressPointer r0
    //     0x980a70: add             x0, x0, HEAP, lsl #32
    // 0x980a74: cmp             w0, NULL
    // 0x980a78: b.eq            #0x980b7c
    // 0x980a7c: r1 = <double>
    //     0x980a7c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x980a80: r0 = CurvedAnimation()
    //     0x980a80: bl              #0x7e34d0  ; AllocateCurvedAnimationStub -> CurvedAnimation (size=0x1c)
    // 0x980a84: mov             x1, x0
    // 0x980a88: ldur            x3, [fp, #-0x10]
    // 0x980a8c: r2 = Instance__DecelerateCurve
    //     0x980a8c: ldr             x2, [PP, #0x4e48]  ; [pp+0x4e48] Obj!_DecelerateCurve@e14cc1
    // 0x980a90: stur            x0, [fp, #-0x10]
    // 0x980a94: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x980a94: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x980a98: r0 = CurvedAnimation()
    //     0x980a98: bl              #0x7e338c  ; [package:flutter/src/animation/animations.dart] CurvedAnimation::CurvedAnimation
    // 0x980a9c: ldur            x0, [fp, #-8]
    // 0x980aa0: LoadField: r1 = r0->field_2f
    //     0x980aa0: ldur            w1, [x0, #0x2f]
    // 0x980aa4: DecompressPointer r1
    //     0x980aa4: add             x1, x1, HEAP, lsl #32
    // 0x980aa8: r16 = Sentinel
    //     0x980aa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x980aac: cmp             w1, w16
    // 0x980ab0: b.ne            #0x980abc
    // 0x980ab4: mov             x3, x0
    // 0x980ab8: b               #0x980ad0
    // 0x980abc: r16 = "_scaleAnimation@**********"
    //     0x980abc: add             x16, PP, #0x57, lsl #12  ; [pp+0x579a0] "_scaleAnimation@**********"
    //     0x980ac0: ldr             x16, [x16, #0x9a0]
    // 0x980ac4: str             x16, [SP]
    // 0x980ac8: r0 = _throwFieldAlreadyInitialized()
    //     0x980ac8: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x980acc: ldur            x3, [fp, #-8]
    // 0x980ad0: ldur            x0, [fp, #-0x10]
    // 0x980ad4: StoreField: r3->field_2f = r0
    //     0x980ad4: stur            w0, [x3, #0x2f]
    //     0x980ad8: ldurb           w16, [x3, #-1]
    //     0x980adc: ldurb           w17, [x0, #-1]
    //     0x980ae0: and             x16, x17, x16, lsr #2
    //     0x980ae4: tst             x16, HEAP, lsr #32
    //     0x980ae8: b.eq            #0x980af0
    //     0x980aec: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x980af0: LoadField: r0 = r3->field_b
    //     0x980af0: ldur            w0, [x3, #0xb]
    // 0x980af4: DecompressPointer r0
    //     0x980af4: add             x0, x0, HEAP, lsl #32
    // 0x980af8: cmp             w0, NULL
    // 0x980afc: b.eq            #0x980b80
    // 0x980b00: LoadField: r0 = r3->field_2b
    //     0x980b00: ldur            w0, [x3, #0x2b]
    // 0x980b04: DecompressPointer r0
    //     0x980b04: add             x0, x0, HEAP, lsl #32
    // 0x980b08: ldur            x2, [fp, #-0x20]
    // 0x980b0c: stur            x0, [fp, #-0x10]
    // 0x980b10: r1 = Function '<anonymous closure>':.
    //     0x980b10: add             x1, PP, #0x57, lsl #12  ; [pp+0x579a8] AnonymousClosure: (0x980b88), in [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::initState (0x9807c4)
    //     0x980b14: ldr             x1, [x1, #0x9a8]
    // 0x980b18: r0 = AllocateClosure()
    //     0x980b18: bl              #0xec1630  ; AllocateClosureStub
    // 0x980b1c: ldur            x1, [fp, #-0x10]
    // 0x980b20: mov             x2, x0
    // 0x980b24: r0 = addStatusListener()
    //     0x980b24: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x980b28: ldur            x1, [fp, #-0x10]
    // 0x980b2c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x980b2c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x980b30: r0 = forward()
    //     0x980b30: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x980b34: ldur            x0, [fp, #-8]
    // 0x980b38: LoadField: r1 = r0->field_b
    //     0x980b38: ldur            w1, [x0, #0xb]
    // 0x980b3c: DecompressPointer r1
    //     0x980b3c: add             x1, x1, HEAP, lsl #32
    // 0x980b40: cmp             w1, NULL
    // 0x980b44: b.eq            #0x980b84
    // 0x980b48: LoadField: r1 = r0->field_23
    //     0x980b48: ldur            w1, [x0, #0x23]
    // 0x980b4c: DecompressPointer r1
    //     0x980b4c: add             x1, x1, HEAP, lsl #32
    // 0x980b50: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x980b50: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x980b54: r0 = forward()
    //     0x980b54: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x980b58: r0 = Null
    //     0x980b58: mov             x0, NULL
    // 0x980b5c: LeaveFrame
    //     0x980b5c: mov             SP, fp
    //     0x980b60: ldp             fp, lr, [SP], #0x10
    // 0x980b64: ret
    //     0x980b64: ret             
    // 0x980b68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980b68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980b6c: b               #0x9807e4
    // 0x980b70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980b70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x980b74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980b74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x980b78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980b78: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x980b7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980b7c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x980b80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980b80: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x980b84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980b84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, AnimationStatus) {
    // ** addr: 0x980b88, size: 0x58
    // 0x980b88: EnterFrame
    //     0x980b88: stp             fp, lr, [SP, #-0x10]!
    //     0x980b8c: mov             fp, SP
    // 0x980b90: ldr             x0, [fp, #0x18]
    // 0x980b94: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x980b94: ldur            w1, [x0, #0x17]
    // 0x980b98: DecompressPointer r1
    //     0x980b98: add             x1, x1, HEAP, lsl #32
    // 0x980b9c: CheckStackOverflow
    //     0x980b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980ba0: cmp             SP, x16
    //     0x980ba4: b.ls            #0x980bd8
    // 0x980ba8: ldr             x0, [fp, #0x10]
    // 0x980bac: r16 = Instance_AnimationStatus
    //     0x980bac: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x980bb0: cmp             w0, w16
    // 0x980bb4: b.ne            #0x980bc8
    // 0x980bb8: LoadField: r0 = r1->field_f
    //     0x980bb8: ldur            w0, [x1, #0xf]
    // 0x980bbc: DecompressPointer r0
    //     0x980bbc: add             x0, x0, HEAP, lsl #32
    // 0x980bc0: mov             x1, x0
    // 0x980bc4: r0 = movingAnimationListener()
    //     0x980bc4: bl              #0x980be0  ; [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::movingAnimationListener
    // 0x980bc8: r0 = Null
    //     0x980bc8: mov             x0, NULL
    // 0x980bcc: LeaveFrame
    //     0x980bcc: mov             SP, fp
    //     0x980bd0: ldp             fp, lr, [SP], #0x10
    // 0x980bd4: ret
    //     0x980bd4: ret             
    // 0x980bd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980bd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980bdc: b               #0x980ba8
  }
  _ movingAnimationListener(/* No info */) {
    // ** addr: 0x980be0, size: 0x88
    // 0x980be0: EnterFrame
    //     0x980be0: stp             fp, lr, [SP, #-0x10]!
    //     0x980be4: mov             fp, SP
    // 0x980be8: AllocStack(0x10)
    //     0x980be8: sub             SP, SP, #0x10
    // 0x980bec: SetupParameters(_ToolTipWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x980bec: stur            x1, [fp, #-8]
    // 0x980bf0: CheckStackOverflow
    //     0x980bf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980bf4: cmp             SP, x16
    //     0x980bf8: b.ls            #0x980c54
    // 0x980bfc: r1 = 1
    //     0x980bfc: movz            x1, #0x1
    // 0x980c00: r0 = AllocateContext()
    //     0x980c00: bl              #0xec126c  ; AllocateContextStub
    // 0x980c04: mov             x1, x0
    // 0x980c08: ldur            x0, [fp, #-8]
    // 0x980c0c: StoreField: r1->field_f = r0
    //     0x980c0c: stur            w0, [x1, #0xf]
    // 0x980c10: LoadField: r3 = r0->field_23
    //     0x980c10: ldur            w3, [x0, #0x23]
    // 0x980c14: DecompressPointer r3
    //     0x980c14: add             x3, x3, HEAP, lsl #32
    // 0x980c18: r16 = Sentinel
    //     0x980c18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x980c1c: cmp             w3, w16
    // 0x980c20: b.eq            #0x980c5c
    // 0x980c24: mov             x2, x1
    // 0x980c28: stur            x3, [fp, #-0x10]
    // 0x980c2c: r1 = Function '<anonymous closure>':.
    //     0x980c2c: add             x1, PP, #0x57, lsl #12  ; [pp+0x579b0] AnonymousClosure: (0x980c68), in [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::movingAnimationListener (0x980be0)
    //     0x980c30: ldr             x1, [x1, #0x9b0]
    // 0x980c34: r0 = AllocateClosure()
    //     0x980c34: bl              #0xec1630  ; AllocateClosureStub
    // 0x980c38: ldur            x1, [fp, #-0x10]
    // 0x980c3c: mov             x2, x0
    // 0x980c40: r0 = addStatusListener()
    //     0x980c40: bl              #0xd243c8  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0x980c44: r0 = Null
    //     0x980c44: mov             x0, NULL
    // 0x980c48: LeaveFrame
    //     0x980c48: mov             SP, fp
    //     0x980c4c: ldp             fp, lr, [SP], #0x10
    // 0x980c50: ret
    //     0x980c50: ret             
    // 0x980c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980c54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980c58: b               #0x980bfc
    // 0x980c5c: r9 = _movingAnimationController
    //     0x980c5c: add             x9, PP, #0x57, lsl #12  ; [pp+0x57940] Field <_ToolTipWidgetState@**********._movingAnimationController@**********>: late final (offset: 0x24)
    //     0x980c60: ldr             x9, [x9, #0x940]
    // 0x980c64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x980c64: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, AnimationStatus) {
    // ** addr: 0x980c68, size: 0xf4
    // 0x980c68: EnterFrame
    //     0x980c68: stp             fp, lr, [SP, #-0x10]!
    //     0x980c6c: mov             fp, SP
    // 0x980c70: AllocStack(0x8)
    //     0x980c70: sub             SP, SP, #8
    // 0x980c74: SetupParameters()
    //     0x980c74: ldr             x0, [fp, #0x18]
    //     0x980c78: ldur            w2, [x0, #0x17]
    //     0x980c7c: add             x2, x2, HEAP, lsl #32
    //     0x980c80: stur            x2, [fp, #-8]
    // 0x980c84: CheckStackOverflow
    //     0x980c84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980c88: cmp             SP, x16
    //     0x980c8c: b.ls            #0x980d30
    // 0x980c90: ldr             x0, [fp, #0x10]
    // 0x980c94: r16 = Instance_AnimationStatus
    //     0x980c94: ldr             x16, [PP, #0x4e98]  ; [pp+0x4e98] Obj!AnimationStatus@e372e1
    // 0x980c98: cmp             w0, w16
    // 0x980c9c: b.ne            #0x980cc4
    // 0x980ca0: LoadField: r0 = r2->field_f
    //     0x980ca0: ldur            w0, [x2, #0xf]
    // 0x980ca4: DecompressPointer r0
    //     0x980ca4: add             x0, x0, HEAP, lsl #32
    // 0x980ca8: LoadField: r1 = r0->field_23
    //     0x980ca8: ldur            w1, [x0, #0x23]
    // 0x980cac: DecompressPointer r1
    //     0x980cac: add             x1, x1, HEAP, lsl #32
    // 0x980cb0: r16 = Sentinel
    //     0x980cb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x980cb4: cmp             w1, w16
    // 0x980cb8: b.eq            #0x980d38
    // 0x980cbc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x980cbc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x980cc0: r0 = reverse()
    //     0x980cc0: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0x980cc4: ldur            x0, [fp, #-8]
    // 0x980cc8: LoadField: r1 = r0->field_f
    //     0x980cc8: ldur            w1, [x0, #0xf]
    // 0x980ccc: DecompressPointer r1
    //     0x980ccc: add             x1, x1, HEAP, lsl #32
    // 0x980cd0: LoadField: r0 = r1->field_23
    //     0x980cd0: ldur            w0, [x1, #0x23]
    // 0x980cd4: DecompressPointer r0
    //     0x980cd4: add             x0, x0, HEAP, lsl #32
    // 0x980cd8: r16 = Sentinel
    //     0x980cd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x980cdc: cmp             w0, w16
    // 0x980ce0: b.eq            #0x980d44
    // 0x980ce4: LoadField: r2 = r0->field_43
    //     0x980ce4: ldur            w2, [x0, #0x43]
    // 0x980ce8: DecompressPointer r2
    //     0x980ce8: add             x2, x2, HEAP, lsl #32
    // 0x980cec: r16 = Sentinel
    //     0x980cec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x980cf0: cmp             w2, w16
    // 0x980cf4: b.eq            #0x980d50
    // 0x980cf8: r16 = Instance_AnimationStatus
    //     0x980cf8: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0x980cfc: cmp             w2, w16
    // 0x980d00: b.ne            #0x980d20
    // 0x980d04: LoadField: r2 = r1->field_b
    //     0x980d04: ldur            w2, [x1, #0xb]
    // 0x980d08: DecompressPointer r2
    //     0x980d08: add             x2, x2, HEAP, lsl #32
    // 0x980d0c: cmp             w2, NULL
    // 0x980d10: b.eq            #0x980d58
    // 0x980d14: mov             x1, x0
    // 0x980d18: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x980d18: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x980d1c: r0 = forward()
    //     0x980d1c: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0x980d20: r0 = Null
    //     0x980d20: mov             x0, NULL
    // 0x980d24: LeaveFrame
    //     0x980d24: mov             SP, fp
    //     0x980d28: ldp             fp, lr, [SP], #0x10
    // 0x980d2c: ret
    //     0x980d2c: ret             
    // 0x980d30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980d30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980d34: b               #0x980c90
    // 0x980d38: r9 = _movingAnimationController
    //     0x980d38: add             x9, PP, #0x57, lsl #12  ; [pp+0x57940] Field <_ToolTipWidgetState@**********._movingAnimationController@**********>: late final (offset: 0x24)
    //     0x980d3c: ldr             x9, [x9, #0x940]
    // 0x980d40: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x980d40: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x980d44: r9 = _movingAnimationController
    //     0x980d44: add             x9, PP, #0x57, lsl #12  ; [pp+0x57940] Field <_ToolTipWidgetState@**********._movingAnimationController@**********>: late final (offset: 0x24)
    //     0x980d48: ldr             x9, [x9, #0x940]
    // 0x980d4c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x980d4c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x980d50: r9 = _status
    //     0x980d50: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0x980d54: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x980d54: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x980d58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980d58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x980d5c, size: 0xe0
    // 0x980d5c: EnterFrame
    //     0x980d5c: stp             fp, lr, [SP, #-0x10]!
    //     0x980d60: mov             fp, SP
    // 0x980d64: AllocStack(0x10)
    //     0x980d64: sub             SP, SP, #0x10
    // 0x980d68: SetupParameters()
    //     0x980d68: ldr             x0, [fp, #0x18]
    //     0x980d6c: ldur            w2, [x0, #0x17]
    //     0x980d70: add             x2, x2, HEAP, lsl #32
    //     0x980d74: stur            x2, [fp, #-8]
    // 0x980d78: CheckStackOverflow
    //     0x980d78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980d7c: cmp             SP, x16
    //     0x980d80: b.ls            #0x980e30
    // 0x980d84: LoadField: r0 = r2->field_f
    //     0x980d84: ldur            w0, [x2, #0xf]
    // 0x980d88: DecompressPointer r0
    //     0x980d88: add             x0, x0, HEAP, lsl #32
    // 0x980d8c: LoadField: r1 = r0->field_b
    //     0x980d8c: ldur            w1, [x0, #0xb]
    // 0x980d90: DecompressPointer r1
    //     0x980d90: add             x1, x1, HEAP, lsl #32
    // 0x980d94: cmp             w1, NULL
    // 0x980d98: b.eq            #0x980e38
    // 0x980d9c: LoadField: r1 = r0->field_43
    //     0x980d9c: ldur            w1, [x0, #0x43]
    // 0x980da0: DecompressPointer r1
    //     0x980da0: add             x1, x1, HEAP, lsl #32
    // 0x980da4: r0 = _currentElement()
    //     0x980da4: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x980da8: cmp             w0, NULL
    // 0x980dac: b.eq            #0x980e20
    // 0x980db0: ldur            x2, [fp, #-8]
    // 0x980db4: LoadField: r0 = r2->field_f
    //     0x980db4: ldur            w0, [x2, #0xf]
    // 0x980db8: DecompressPointer r0
    //     0x980db8: add             x0, x0, HEAP, lsl #32
    // 0x980dbc: LoadField: r1 = r0->field_43
    //     0x980dbc: ldur            w1, [x0, #0x43]
    // 0x980dc0: DecompressPointer r1
    //     0x980dc0: add             x1, x1, HEAP, lsl #32
    // 0x980dc4: r0 = _currentElement()
    //     0x980dc4: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x980dc8: cmp             w0, NULL
    // 0x980dcc: b.eq            #0x980e20
    // 0x980dd0: mov             x1, x0
    // 0x980dd4: r0 = findRenderObject()
    //     0x980dd4: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0x980dd8: r1 = LoadClassIdInstr(r0)
    //     0x980dd8: ldur            x1, [x0, #-1]
    //     0x980ddc: ubfx            x1, x1, #0xc, #0x14
    // 0x980de0: sub             x16, x1, #0xbba
    // 0x980de4: cmp             x16, #0x9a
    // 0x980de8: b.hi            #0x980e20
    // 0x980dec: ldur            x2, [fp, #-8]
    // 0x980df0: mov             x1, x0
    // 0x980df4: r0 = size()
    //     0x980df4: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x980df8: ldur            x2, [fp, #-8]
    // 0x980dfc: LoadField: r0 = r2->field_f
    //     0x980dfc: ldur            w0, [x2, #0xf]
    // 0x980e00: DecompressPointer r0
    //     0x980e00: add             x0, x0, HEAP, lsl #32
    // 0x980e04: stur            x0, [fp, #-0x10]
    // 0x980e08: r1 = Function '<anonymous closure>':.
    //     0x980e08: add             x1, PP, #0x57, lsl #12  ; [pp+0x579b8] AnonymousClosure: (0x980e3c), in [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::initState (0x9807c4)
    //     0x980e0c: ldr             x1, [x1, #0x9b8]
    // 0x980e10: r0 = AllocateClosure()
    //     0x980e10: bl              #0xec1630  ; AllocateClosureStub
    // 0x980e14: ldur            x1, [fp, #-0x10]
    // 0x980e18: mov             x2, x0
    // 0x980e1c: r0 = setState()
    //     0x980e1c: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x980e20: r0 = Null
    //     0x980e20: mov             x0, NULL
    // 0x980e24: LeaveFrame
    //     0x980e24: mov             SP, fp
    //     0x980e28: ldp             fp, lr, [SP], #0x10
    // 0x980e2c: ret
    //     0x980e2c: ret             
    // 0x980e30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980e30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980e34: b               #0x980d84
    // 0x980e38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980e38: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x980e3c, size: 0xe8
    // 0x980e3c: EnterFrame
    //     0x980e3c: stp             fp, lr, [SP, #-0x10]!
    //     0x980e40: mov             fp, SP
    // 0x980e44: AllocStack(0x8)
    //     0x980e44: sub             SP, SP, #8
    // 0x980e48: SetupParameters()
    //     0x980e48: ldr             x0, [fp, #0x10]
    //     0x980e4c: ldur            w1, [x0, #0x17]
    //     0x980e50: add             x1, x1, HEAP, lsl #32
    // 0x980e54: CheckStackOverflow
    //     0x980e54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980e58: cmp             SP, x16
    //     0x980e5c: b.ls            #0x980f00
    // 0x980e60: LoadField: r0 = r1->field_f
    //     0x980e60: ldur            w0, [x1, #0xf]
    // 0x980e64: DecompressPointer r0
    //     0x980e64: add             x0, x0, HEAP, lsl #32
    // 0x980e68: LoadField: r2 = r0->field_47
    //     0x980e68: ldur            w2, [x0, #0x47]
    // 0x980e6c: DecompressPointer r2
    //     0x980e6c: add             x2, x2, HEAP, lsl #32
    // 0x980e70: stur            x2, [fp, #-8]
    // 0x980e74: LoadField: r1 = r0->field_43
    //     0x980e74: ldur            w1, [x0, #0x43]
    // 0x980e78: DecompressPointer r1
    //     0x980e78: add             x1, x1, HEAP, lsl #32
    // 0x980e7c: r0 = _currentElement()
    //     0x980e7c: bl              #0x639c70  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0x980e80: cmp             w0, NULL
    // 0x980e84: b.eq            #0x980f08
    // 0x980e88: mov             x1, x0
    // 0x980e8c: r0 = findRenderObject()
    //     0x980e8c: bl              #0x6852d8  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0x980e90: r1 = LoadClassIdInstr(r0)
    //     0x980e90: ldur            x1, [x0, #-1]
    //     0x980e94: ubfx            x1, x1, #0xc, #0x14
    // 0x980e98: sub             x16, x1, #0xbba
    // 0x980e9c: cmp             x16, #0x9a
    // 0x980ea0: b.hi            #0x980eb0
    // 0x980ea4: mov             x1, x0
    // 0x980ea8: r0 = size()
    //     0x980ea8: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x980eac: b               #0x980eb4
    // 0x980eb0: r0 = Null
    //     0x980eb0: mov             x0, NULL
    // 0x980eb4: cmp             w0, NULL
    // 0x980eb8: b.eq            #0x980f0c
    // 0x980ebc: LoadField: d0 = r0->field_7
    //     0x980ebc: ldur            d0, [x0, #7]
    // 0x980ec0: r2 = inline_Allocate_Double()
    //     0x980ec0: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x980ec4: add             x2, x2, #0x10
    //     0x980ec8: cmp             x0, x2
    //     0x980ecc: b.ls            #0x980f10
    //     0x980ed0: str             x2, [THR, #0x50]  ; THR::top
    //     0x980ed4: sub             x2, x2, #0xf
    //     0x980ed8: movz            x0, #0xe15c
    //     0x980edc: movk            x0, #0x3, lsl #16
    //     0x980ee0: stur            x0, [x2, #-1]
    // 0x980ee4: StoreField: r2->field_7 = d0
    //     0x980ee4: stur            d0, [x2, #7]
    // 0x980ee8: ldur            x1, [fp, #-8]
    // 0x980eec: r0 = value=()
    //     0x980eec: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x980ef0: r0 = Null
    //     0x980ef0: mov             x0, NULL
    // 0x980ef4: LeaveFrame
    //     0x980ef4: mov             SP, fp
    //     0x980ef8: ldp             fp, lr, [SP], #0x10
    // 0x980efc: ret
    //     0x980efc: ret             
    // 0x980f00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x980f00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x980f04: b               #0x980e60
    // 0x980f08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980f08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x980f0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x980f0c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x980f10: SaveReg d0
    //     0x980f10: str             q0, [SP, #-0x10]!
    // 0x980f14: r0 = AllocateDouble()
    //     0x980f14: bl              #0xec2254  ; AllocateDoubleStub
    // 0x980f18: mov             x2, x0
    // 0x980f1c: RestoreReg d0
    //     0x980f1c: ldr             q0, [SP], #0x10
    // 0x980f20: b               #0x980ee4
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a1ebc, size: 0xc0
    // 0x9a1ebc: EnterFrame
    //     0x9a1ebc: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1ec0: mov             fp, SP
    // 0x9a1ec4: AllocStack(0x10)
    //     0x9a1ec4: sub             SP, SP, #0x10
    // 0x9a1ec8: SetupParameters(_ToolTipWidgetState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a1ec8: mov             x4, x1
    //     0x9a1ecc: mov             x3, x2
    //     0x9a1ed0: stur            x1, [fp, #-8]
    //     0x9a1ed4: stur            x2, [fp, #-0x10]
    // 0x9a1ed8: CheckStackOverflow
    //     0x9a1ed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1edc: cmp             SP, x16
    //     0x9a1ee0: b.ls            #0x9a1f74
    // 0x9a1ee4: mov             x0, x3
    // 0x9a1ee8: r2 = Null
    //     0x9a1ee8: mov             x2, NULL
    // 0x9a1eec: r1 = Null
    //     0x9a1eec: mov             x1, NULL
    // 0x9a1ef0: r4 = 60
    //     0x9a1ef0: movz            x4, #0x3c
    // 0x9a1ef4: branchIfSmi(r0, 0x9a1f00)
    //     0x9a1ef4: tbz             w0, #0, #0x9a1f00
    // 0x9a1ef8: r4 = LoadClassIdInstr(r0)
    //     0x9a1ef8: ldur            x4, [x0, #-1]
    //     0x9a1efc: ubfx            x4, x4, #0xc, #0x14
    // 0x9a1f00: r17 = 4689
    //     0x9a1f00: movz            x17, #0x1251
    // 0x9a1f04: cmp             x4, x17
    // 0x9a1f08: b.eq            #0x9a1f20
    // 0x9a1f0c: r8 = ToolTipWidget
    //     0x9a1f0c: add             x8, PP, #0x57, lsl #12  ; [pp+0x57948] Type: ToolTipWidget
    //     0x9a1f10: ldr             x8, [x8, #0x948]
    // 0x9a1f14: r3 = Null
    //     0x9a1f14: add             x3, PP, #0x57, lsl #12  ; [pp+0x57950] Null
    //     0x9a1f18: ldr             x3, [x3, #0x950]
    // 0x9a1f1c: r0 = ToolTipWidget()
    //     0x9a1f1c: bl              #0x6fad30  ; IsType_ToolTipWidget_Stub
    // 0x9a1f20: ldur            x3, [fp, #-8]
    // 0x9a1f24: LoadField: r2 = r3->field_7
    //     0x9a1f24: ldur            w2, [x3, #7]
    // 0x9a1f28: DecompressPointer r2
    //     0x9a1f28: add             x2, x2, HEAP, lsl #32
    // 0x9a1f2c: ldur            x0, [fp, #-0x10]
    // 0x9a1f30: r1 = Null
    //     0x9a1f30: mov             x1, NULL
    // 0x9a1f34: cmp             w2, NULL
    // 0x9a1f38: b.eq            #0x9a1f5c
    // 0x9a1f3c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a1f3c: ldur            w4, [x2, #0x17]
    // 0x9a1f40: DecompressPointer r4
    //     0x9a1f40: add             x4, x4, HEAP, lsl #32
    // 0x9a1f44: r8 = X0 bound StatefulWidget
    //     0x9a1f44: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a1f48: ldr             x8, [x8, #0x7f8]
    // 0x9a1f4c: LoadField: r9 = r4->field_7
    //     0x9a1f4c: ldur            x9, [x4, #7]
    // 0x9a1f50: r3 = Null
    //     0x9a1f50: add             x3, PP, #0x57, lsl #12  ; [pp+0x57960] Null
    //     0x9a1f54: ldr             x3, [x3, #0x960]
    // 0x9a1f58: blr             x9
    // 0x9a1f5c: ldur            x1, [fp, #-8]
    // 0x9a1f60: r0 = _getTooltipWidth()
    //     0x9a1f60: bl              #0x9a1f7c  ; [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::_getTooltipWidth
    // 0x9a1f64: r0 = Null
    //     0x9a1f64: mov             x0, NULL
    // 0x9a1f68: LeaveFrame
    //     0x9a1f68: mov             SP, fp
    //     0x9a1f6c: ldp             fp, lr, [SP], #0x10
    // 0x9a1f70: ret
    //     0x9a1f70: ret             
    // 0x9a1f74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1f74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1f78: b               #0x9a1ee4
  }
  _ _getTooltipWidth(/* No info */) {
    // ** addr: 0x9a1f7c, size: 0x164
    // 0x9a1f7c: EnterFrame
    //     0x9a1f7c: stp             fp, lr, [SP, #-0x10]!
    //     0x9a1f80: mov             fp, SP
    // 0x9a1f84: AllocStack(0x10)
    //     0x9a1f84: sub             SP, SP, #0x10
    // 0x9a1f88: SetupParameters(_ToolTipWidgetState this /* r1 => r0, fp-0x8 */)
    //     0x9a1f88: mov             x0, x1
    //     0x9a1f8c: stur            x1, [fp, #-8]
    // 0x9a1f90: CheckStackOverflow
    //     0x9a1f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a1f94: cmp             SP, x16
    //     0x9a1f98: b.ls            #0x9a20b4
    // 0x9a1f9c: LoadField: r1 = r0->field_b
    //     0x9a1f9c: ldur            w1, [x0, #0xb]
    // 0x9a1fa0: DecompressPointer r1
    //     0x9a1fa0: add             x1, x1, HEAP, lsl #32
    // 0x9a1fa4: cmp             w1, NULL
    // 0x9a1fa8: b.eq            #0x9a20bc
    // 0x9a1fac: LoadField: r1 = r0->field_f
    //     0x9a1fac: ldur            w1, [x0, #0xf]
    // 0x9a1fb0: DecompressPointer r1
    //     0x9a1fb0: add             x1, x1, HEAP, lsl #32
    // 0x9a1fb4: cmp             w1, NULL
    // 0x9a1fb8: b.eq            #0x9a20c0
    // 0x9a1fbc: r0 = of()
    //     0x9a1fbc: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9a1fc0: LoadField: r1 = r0->field_8f
    //     0x9a1fc0: ldur            w1, [x0, #0x8f]
    // 0x9a1fc4: DecompressPointer r1
    //     0x9a1fc4: add             x1, x1, HEAP, lsl #32
    // 0x9a1fc8: LoadField: r0 = r1->field_1f
    //     0x9a1fc8: ldur            w0, [x1, #0x1f]
    // 0x9a1fcc: DecompressPointer r0
    //     0x9a1fcc: add             x0, x0, HEAP, lsl #32
    // 0x9a1fd0: stur            x0, [fp, #-0x10]
    // 0x9a1fd4: cmp             w0, NULL
    // 0x9a1fd8: b.eq            #0x9a20c4
    // 0x9a1fdc: ldur            x1, [fp, #-8]
    // 0x9a1fe0: LoadField: r2 = r1->field_b
    //     0x9a1fe0: ldur            w2, [x1, #0xb]
    // 0x9a1fe4: DecompressPointer r2
    //     0x9a1fe4: add             x2, x2, HEAP, lsl #32
    // 0x9a1fe8: cmp             w2, NULL
    // 0x9a1fec: b.eq            #0x9a20c8
    // 0x9a1ff0: r0 = TextStyle()
    //     0x9a1ff0: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0x9a1ff4: mov             x1, x0
    // 0x9a1ff8: r0 = true
    //     0x9a1ff8: add             x0, NULL, #0x20  ; true
    // 0x9a1ffc: StoreField: r1->field_7 = r0
    //     0x9a1ffc: stur            w0, [x1, #7]
    // 0x9a2000: r3 = Instance_Color
    //     0x9a2000: ldr             x3, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0x9a2004: StoreField: r1->field_b = r3
    //     0x9a2004: stur            w3, [x1, #0xb]
    // 0x9a2008: mov             x2, x1
    // 0x9a200c: ldur            x1, [fp, #-0x10]
    // 0x9a2010: r0 = merge()
    //     0x9a2010: bl              #0x626f48  ; [package:flutter/src/painting/text_style.dart] TextStyle::merge
    // 0x9a2014: ldur            x0, [fp, #-8]
    // 0x9a2018: LoadField: r1 = r0->field_b
    //     0x9a2018: ldur            w1, [x0, #0xb]
    // 0x9a201c: DecompressPointer r1
    //     0x9a201c: add             x1, x1, HEAP, lsl #32
    // 0x9a2020: cmp             w1, NULL
    // 0x9a2024: b.eq            #0x9a20cc
    // 0x9a2028: LoadField: r1 = r0->field_f
    //     0x9a2028: ldur            w1, [x0, #0xf]
    // 0x9a202c: DecompressPointer r1
    //     0x9a202c: add             x1, x1, HEAP, lsl #32
    // 0x9a2030: cmp             w1, NULL
    // 0x9a2034: b.eq            #0x9a20d0
    // 0x9a2038: r0 = of()
    //     0x9a2038: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9a203c: LoadField: r1 = r0->field_8f
    //     0x9a203c: ldur            w1, [x0, #0x8f]
    // 0x9a2040: DecompressPointer r1
    //     0x9a2040: add             x1, x1, HEAP, lsl #32
    // 0x9a2044: LoadField: r0 = r1->field_27
    //     0x9a2044: ldur            w0, [x1, #0x27]
    // 0x9a2048: DecompressPointer r0
    //     0x9a2048: add             x0, x0, HEAP, lsl #32
    // 0x9a204c: stur            x0, [fp, #-0x10]
    // 0x9a2050: cmp             w0, NULL
    // 0x9a2054: b.eq            #0x9a20d4
    // 0x9a2058: ldur            x1, [fp, #-8]
    // 0x9a205c: LoadField: r2 = r1->field_b
    //     0x9a205c: ldur            w2, [x1, #0xb]
    // 0x9a2060: DecompressPointer r2
    //     0x9a2060: add             x2, x2, HEAP, lsl #32
    // 0x9a2064: cmp             w2, NULL
    // 0x9a2068: b.eq            #0x9a20d8
    // 0x9a206c: r0 = TextStyle()
    //     0x9a206c: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0x9a2070: mov             x1, x0
    // 0x9a2074: r0 = true
    //     0x9a2074: add             x0, NULL, #0x20  ; true
    // 0x9a2078: StoreField: r1->field_7 = r0
    //     0x9a2078: stur            w0, [x1, #7]
    // 0x9a207c: r0 = Instance_Color
    //     0x9a207c: ldr             x0, [PP, #0x5138]  ; [pp+0x5138] Obj!Color@e26f11
    // 0x9a2080: StoreField: r1->field_b = r0
    //     0x9a2080: stur            w0, [x1, #0xb]
    // 0x9a2084: mov             x2, x1
    // 0x9a2088: ldur            x1, [fp, #-0x10]
    // 0x9a208c: r0 = merge()
    //     0x9a208c: bl              #0x626f48  ; [package:flutter/src/painting/text_style.dart] TextStyle::merge
    // 0x9a2090: ldur            x1, [fp, #-8]
    // 0x9a2094: LoadField: r2 = r1->field_b
    //     0x9a2094: ldur            w2, [x1, #0xb]
    // 0x9a2098: DecompressPointer r2
    //     0x9a2098: add             x2, x2, HEAP, lsl #32
    // 0x9a209c: cmp             w2, NULL
    // 0x9a20a0: b.eq            #0x9a20dc
    // 0x9a20a4: r0 = Null
    //     0x9a20a4: mov             x0, NULL
    // 0x9a20a8: LeaveFrame
    //     0x9a20a8: mov             SP, fp
    //     0x9a20ac: ldp             fp, lr, [SP], #0x10
    // 0x9a20b0: ret
    //     0x9a20b0: ret             
    // 0x9a20b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a20b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a20b8: b               #0x9a1f9c
    // 0x9a20bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a20bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a20c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a20c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a20c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a20c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a20c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a20c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a20cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a20cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a20d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a20d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a20d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a20d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a20d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a20d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a20dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a20dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9d28dc, size: 0x30
    // 0x9d28dc: EnterFrame
    //     0x9d28dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9d28e0: mov             fp, SP
    // 0x9d28e4: CheckStackOverflow
    //     0x9d28e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d28e8: cmp             SP, x16
    //     0x9d28ec: b.ls            #0x9d2904
    // 0x9d28f0: r0 = _getTooltipWidth()
    //     0x9d28f0: bl              #0x9a1f7c  ; [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::_getTooltipWidth
    // 0x9d28f4: r0 = Null
    //     0x9d28f4: mov             x0, NULL
    // 0x9d28f8: LeaveFrame
    //     0x9d28f8: mov             SP, fp
    //     0x9d28fc: ldp             fp, lr, [SP], #0x10
    // 0x9d2900: ret
    //     0x9d2900: ret             
    // 0x9d2904: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d2904: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d2908: b               #0x9d28f0
  }
  [closure] void onSizeChange(dynamic, Size?) {
    // ** addr: 0xa4c7b0, size: 0x3c
    // 0xa4c7b0: EnterFrame
    //     0xa4c7b0: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c7b4: mov             fp, SP
    // 0xa4c7b8: ldr             x0, [fp, #0x18]
    // 0xa4c7bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4c7bc: ldur            w1, [x0, #0x17]
    // 0xa4c7c0: DecompressPointer r1
    //     0xa4c7c0: add             x1, x1, HEAP, lsl #32
    // 0xa4c7c4: CheckStackOverflow
    //     0xa4c7c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c7c8: cmp             SP, x16
    //     0xa4c7cc: b.ls            #0xa4c7e4
    // 0xa4c7d0: ldr             x2, [fp, #0x10]
    // 0xa4c7d4: r0 = onSizeChange()
    //     0xa4c7d4: bl              #0xa4c7ec  ; [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::onSizeChange
    // 0xa4c7d8: LeaveFrame
    //     0xa4c7d8: mov             SP, fp
    //     0xa4c7dc: ldp             fp, lr, [SP], #0x10
    // 0xa4c7e0: ret
    //     0xa4c7e0: ret             
    // 0xa4c7e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c7e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c7e8: b               #0xa4c7d0
  }
  _ onSizeChange(/* No info */) {
    // ** addr: 0xa4c7ec, size: 0xc0
    // 0xa4c7ec: EnterFrame
    //     0xa4c7ec: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c7f0: mov             fp, SP
    // 0xa4c7f4: AllocStack(0x28)
    //     0xa4c7f4: sub             SP, SP, #0x28
    // 0xa4c7f8: SetupParameters(_ToolTipWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa4c7f8: stur            x1, [fp, #-8]
    //     0xa4c7fc: stur            x2, [fp, #-0x10]
    // 0xa4c800: CheckStackOverflow
    //     0xa4c800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c804: cmp             SP, x16
    //     0xa4c808: b.ls            #0xa4c89c
    // 0xa4c80c: r1 = 2
    //     0xa4c80c: movz            x1, #0x2
    // 0xa4c810: r0 = AllocateContext()
    //     0xa4c810: bl              #0xec126c  ; AllocateContextStub
    // 0xa4c814: ldur            x1, [fp, #-8]
    // 0xa4c818: stur            x0, [fp, #-0x18]
    // 0xa4c81c: StoreField: r0->field_f = r1
    //     0xa4c81c: stur            w1, [x0, #0xf]
    // 0xa4c820: LoadField: r2 = r1->field_1b
    //     0xa4c820: ldur            w2, [x1, #0x1b]
    // 0xa4c824: DecompressPointer r2
    //     0xa4c824: add             x2, x2, HEAP, lsl #32
    // 0xa4c828: StoreField: r0->field_13 = r2
    //     0xa4c828: stur            w2, [x0, #0x13]
    // 0xa4c82c: cmp             w2, NULL
    // 0xa4c830: b.eq            #0xa4c8a4
    // 0xa4c834: LoadField: d0 = r2->field_7
    //     0xa4c834: ldur            d0, [x2, #7]
    // 0xa4c838: stur            d0, [fp, #-0x28]
    // 0xa4c83c: LoadField: d1 = r2->field_f
    //     0xa4c83c: ldur            d1, [x2, #0xf]
    // 0xa4c840: ldur            x2, [fp, #-0x10]
    // 0xa4c844: cmp             w2, NULL
    // 0xa4c848: b.eq            #0xa4c8a8
    // 0xa4c84c: LoadField: d2 = r2->field_f
    //     0xa4c84c: ldur            d2, [x2, #0xf]
    // 0xa4c850: fadd            d3, d1, d2
    // 0xa4c854: stur            d3, [fp, #-0x20]
    // 0xa4c858: r0 = Offset()
    //     0xa4c858: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xa4c85c: ldur            d0, [fp, #-0x28]
    // 0xa4c860: StoreField: r0->field_7 = d0
    //     0xa4c860: stur            d0, [x0, #7]
    // 0xa4c864: ldur            d0, [fp, #-0x20]
    // 0xa4c868: StoreField: r0->field_f = d0
    //     0xa4c868: stur            d0, [x0, #0xf]
    // 0xa4c86c: ldur            x2, [fp, #-0x18]
    // 0xa4c870: StoreField: r2->field_13 = r0
    //     0xa4c870: stur            w0, [x2, #0x13]
    // 0xa4c874: r1 = Function '<anonymous closure>':.
    //     0xa4c874: add             x1, PP, #0x57, lsl #12  ; [pp+0x57900] AnonymousClosure: (0xa382a4), in [package:nuonline/app/modules/donation/views/payment_method_view.dart] _PaymentMethodViewState::onItemPressed (0xa38234)
    //     0xa4c878: ldr             x1, [x1, #0x900]
    // 0xa4c87c: r0 = AllocateClosure()
    //     0xa4c87c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4c880: ldur            x1, [fp, #-8]
    // 0xa4c884: mov             x2, x0
    // 0xa4c888: r0 = setState()
    //     0xa4c888: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa4c88c: r0 = Null
    //     0xa4c88c: mov             x0, NULL
    // 0xa4c890: LeaveFrame
    //     0xa4c890: mov             SP, fp
    //     0xa4c894: ldp             fp, lr, [SP], #0x10
    // 0xa4c898: ret
    //     0xa4c898: ret             
    // 0xa4c89c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c89c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c8a0: b               #0xa4c80c
    // 0xa4c8a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4c8a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4c8a8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4c8a8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa4d8b8, size: 0x590
    // 0xa4d8b8: EnterFrame
    //     0xa4d8b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4d8bc: mov             fp, SP
    // 0xa4d8c0: AllocStack(0x70)
    //     0xa4d8c0: sub             SP, SP, #0x70
    // 0xa4d8c4: SetupParameters(_ToolTipWidgetState this /* r1 => r3, fp-0x8 */)
    //     0xa4d8c4: mov             x3, x1
    //     0xa4d8c8: stur            x1, [fp, #-8]
    // 0xa4d8cc: CheckStackOverflow
    //     0xa4d8cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4d8d0: cmp             SP, x16
    //     0xa4d8d4: b.ls            #0xa4ddb8
    // 0xa4d8d8: LoadField: r0 = r3->field_b
    //     0xa4d8d8: ldur            w0, [x3, #0xb]
    // 0xa4d8dc: DecompressPointer r0
    //     0xa4d8dc: add             x0, x0, HEAP, lsl #32
    // 0xa4d8e0: cmp             w0, NULL
    // 0xa4d8e4: b.eq            #0xa4ddc0
    // 0xa4d8e8: LoadField: r1 = r0->field_f
    //     0xa4d8e8: ldur            w1, [x0, #0xf]
    // 0xa4d8ec: DecompressPointer r1
    //     0xa4d8ec: add             x1, x1, HEAP, lsl #32
    // 0xa4d8f0: mov             x0, x1
    // 0xa4d8f4: StoreField: r3->field_1b = r0
    //     0xa4d8f4: stur            w0, [x3, #0x1b]
    //     0xa4d8f8: ldurb           w16, [x3, #-1]
    //     0xa4d8fc: ldurb           w17, [x0, #-1]
    //     0xa4d900: and             x16, x17, x16, lsr #2
    //     0xa4d904: tst             x16, HEAP, lsr #32
    //     0xa4d908: b.eq            #0xa4d910
    //     0xa4d90c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa4d910: mov             x2, x1
    // 0xa4d914: mov             x1, x3
    // 0xa4d918: r0 = findPositionForContent()
    //     0xa4d918: bl              #0xa4e3b8  ; [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::findPositionForContent
    // 0xa4d91c: r16 = Instance_TooltipPosition
    //     0xa4d91c: add             x16, PP, #0x57, lsl #12  ; [pp+0x578e0] Obj!TooltipPosition@e2e141
    //     0xa4d920: ldr             x16, [x16, #0x8e0]
    // 0xa4d924: cmp             w0, w16
    // 0xa4d928: b.ne            #0xa4d934
    // 0xa4d92c: d1 = 1.000000
    //     0xa4d92c: fmov            d1, #1.00000000
    // 0xa4d930: b               #0xa4d938
    // 0xa4d934: d1 = -1.000000
    //     0xa4d934: fmov            d1, #-1.00000000
    // 0xa4d938: ldur            x0, [fp, #-8]
    // 0xa4d93c: d0 = 1.000000
    //     0xa4d93c: fmov            d0, #1.00000000
    // 0xa4d940: stur            d1, [fp, #-0x40]
    // 0xa4d944: r2 = inline_Allocate_Double()
    //     0xa4d944: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0xa4d948: add             x2, x2, #0x10
    //     0xa4d94c: cmp             x1, x2
    //     0xa4d950: b.ls            #0xa4ddc4
    //     0xa4d954: str             x2, [THR, #0x50]  ; THR::top
    //     0xa4d958: sub             x2, x2, #0xf
    //     0xa4d95c: movz            x1, #0xe15c
    //     0xa4d960: movk            x1, #0x3, lsl #16
    //     0xa4d964: stur            x1, [x2, #-1]
    // 0xa4d968: StoreField: r2->field_7 = d1
    //     0xa4d968: stur            d1, [x2, #7]
    // 0xa4d96c: stur            x2, [fp, #-0x10]
    // 0xa4d970: fcmp            d1, d0
    // 0xa4d974: r16 = true
    //     0xa4d974: add             x16, NULL, #0x20  ; true
    // 0xa4d978: r17 = false
    //     0xa4d978: add             x17, NULL, #0x30  ; false
    // 0xa4d97c: csel            x1, x16, x17, eq
    // 0xa4d980: StoreField: r0->field_1f = r1
    //     0xa4d980: stur            w1, [x0, #0x1f]
    // 0xa4d984: tbnz            w1, #4, #0xa4d9c8
    // 0xa4d988: LoadField: r1 = r0->field_b
    //     0xa4d988: ldur            w1, [x0, #0xb]
    // 0xa4d98c: DecompressPointer r1
    //     0xa4d98c: add             x1, x1, HEAP, lsl #32
    // 0xa4d990: cmp             w1, NULL
    // 0xa4d994: b.eq            #0xa4dde0
    // 0xa4d998: LoadField: r3 = r1->field_b
    //     0xa4d998: ldur            w3, [x1, #0xb]
    // 0xa4d99c: DecompressPointer r3
    //     0xa4d99c: add             x3, x3, HEAP, lsl #32
    // 0xa4d9a0: cmp             w3, NULL
    // 0xa4d9a4: b.eq            #0xa4dde4
    // 0xa4d9a8: mov             x1, x3
    // 0xa4d9ac: r0 = getBottom()
    //     0xa4d9ac: bl              #0xa4e2ec  ; [package:showcaseview/src/get_position.dart] GetPosition::getBottom
    // 0xa4d9b0: mov             v2.16b, v0.16b
    // 0xa4d9b4: ldur            d0, [fp, #-0x40]
    // 0xa4d9b8: d1 = 3.000000
    //     0xa4d9b8: fmov            d1, #3.00000000
    // 0xa4d9bc: fmul            d3, d0, d1
    // 0xa4d9c0: fadd            d1, d2, d3
    // 0xa4d9c4: b               #0xa4da0c
    // 0xa4d9c8: mov             v0.16b, v1.16b
    // 0xa4d9cc: d1 = 3.000000
    //     0xa4d9cc: fmov            d1, #3.00000000
    // 0xa4d9d0: LoadField: r1 = r0->field_b
    //     0xa4d9d0: ldur            w1, [x0, #0xb]
    // 0xa4d9d4: DecompressPointer r1
    //     0xa4d9d4: add             x1, x1, HEAP, lsl #32
    // 0xa4d9d8: cmp             w1, NULL
    // 0xa4d9dc: b.eq            #0xa4dde8
    // 0xa4d9e0: LoadField: r2 = r1->field_b
    //     0xa4d9e0: ldur            w2, [x1, #0xb]
    // 0xa4d9e4: DecompressPointer r2
    //     0xa4d9e4: add             x2, x2, HEAP, lsl #32
    // 0xa4d9e8: cmp             w2, NULL
    // 0xa4d9ec: b.eq            #0xa4ddec
    // 0xa4d9f0: mov             x1, x2
    // 0xa4d9f4: r0 = getTop()
    //     0xa4d9f4: bl              #0xa4e230  ; [package:showcaseview/src/get_position.dart] GetPosition::getTop
    // 0xa4d9f8: mov             v2.16b, v0.16b
    // 0xa4d9fc: ldur            d0, [fp, #-0x40]
    // 0xa4da00: d1 = 3.000000
    //     0xa4da00: fmov            d1, #3.00000000
    // 0xa4da04: fmul            d3, d0, d1
    // 0xa4da08: fadd            d1, d2, d3
    // 0xa4da0c: ldur            x0, [fp, #-8]
    // 0xa4da10: ldur            x1, [fp, #-0x10]
    // 0xa4da14: stur            d1, [fp, #-0x48]
    // 0xa4da18: r2 = -1.000000
    //     0xa4da18: ldr             x2, [PP, #0x5bd8]  ; [pp+0x5bd8] -1
    // 0xa4da1c: r3 = 0.000000
    //     0xa4da1c: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xa4da20: r0 = clamp()
    //     0xa4da20: bl              #0xebf534  ; [dart:core] _Double::clamp
    // 0xa4da24: mov             x2, x0
    // 0xa4da28: ldur            x0, [fp, #-8]
    // 0xa4da2c: stur            x2, [fp, #-0x10]
    // 0xa4da30: LoadField: r1 = r0->field_b
    //     0xa4da30: ldur            w1, [x0, #0xb]
    // 0xa4da34: DecompressPointer r1
    //     0xa4da34: add             x1, x1, HEAP, lsl #32
    // 0xa4da38: cmp             w1, NULL
    // 0xa4da3c: b.eq            #0xa4ddf0
    // 0xa4da40: LoadField: r3 = r1->field_5b
    //     0xa4da40: ldur            w3, [x1, #0x5b]
    // 0xa4da44: DecompressPointer r3
    //     0xa4da44: add             x3, x3, HEAP, lsl #32
    // 0xa4da48: tbnz            w3, #4, #0xa4da68
    // 0xa4da4c: LoadField: r1 = r0->field_2b
    //     0xa4da4c: ldur            w1, [x0, #0x2b]
    // 0xa4da50: DecompressPointer r1
    //     0xa4da50: add             x1, x1, HEAP, lsl #32
    // 0xa4da54: r16 = Sentinel
    //     0xa4da54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4da58: cmp             w1, w16
    // 0xa4da5c: b.eq            #0xa4ddf4
    // 0xa4da60: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa4da60: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa4da64: r0 = reverse()
    //     0xa4da64: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0xa4da68: ldur            x0, [fp, #-8]
    // 0xa4da6c: ldur            d0, [fp, #-0x40]
    // 0xa4da70: ldur            d1, [fp, #-0x48]
    // 0xa4da74: ldur            x2, [fp, #-0x10]
    // 0xa4da78: LoadField: r1 = r0->field_b
    //     0xa4da78: ldur            w1, [x0, #0xb]
    // 0xa4da7c: DecompressPointer r1
    //     0xa4da7c: add             x1, x1, HEAP, lsl #32
    // 0xa4da80: cmp             w1, NULL
    // 0xa4da84: b.eq            #0xa4de00
    // 0xa4da88: mov             x1, x0
    // 0xa4da8c: r0 = _getSpace()
    //     0xa4da8c: bl              #0xa4de60  ; [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::_getSpace
    // 0xa4da90: mov             v2.16b, v0.16b
    // 0xa4da94: ldur            d0, [fp, #-0x40]
    // 0xa4da98: d1 = 10.000000
    //     0xa4da98: fmov            d1, #10.00000000
    // 0xa4da9c: stur            d2, [fp, #-0x58]
    // 0xa4daa0: fmul            d3, d0, d1
    // 0xa4daa4: ldur            d4, [fp, #-0x48]
    // 0xa4daa8: fsub            d5, d4, d3
    // 0xa4daac: stur            d5, [fp, #-0x50]
    // 0xa4dab0: r0 = Offset()
    //     0xa4dab0: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xa4dab4: stur            x0, [fp, #-0x18]
    // 0xa4dab8: StoreField: r0->field_7 = rZR
    //     0xa4dab8: stur            xzr, [x0, #7]
    // 0xa4dabc: ldur            x1, [fp, #-0x10]
    // 0xa4dac0: LoadField: d0 = r1->field_7
    //     0xa4dac0: ldur            d0, [x1, #7]
    // 0xa4dac4: StoreField: r0->field_f = d0
    //     0xa4dac4: stur            d0, [x0, #0xf]
    // 0xa4dac8: ldur            x2, [fp, #-8]
    // 0xa4dacc: LoadField: r1 = r2->field_b
    //     0xa4dacc: ldur            w1, [x2, #0xb]
    // 0xa4dad0: DecompressPointer r1
    //     0xa4dad0: add             x1, x1, HEAP, lsl #32
    // 0xa4dad4: cmp             w1, NULL
    // 0xa4dad8: b.eq            #0xa4de04
    // 0xa4dadc: ldur            d0, [fp, #-0x40]
    // 0xa4dae0: d1 = 7.000000
    //     0xa4dae0: fmov            d1, #7.00000000
    // 0xa4dae4: fmul            d2, d0, d1
    // 0xa4dae8: stur            d2, [fp, #-0x48]
    // 0xa4daec: r0 = Offset()
    //     0xa4daec: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xa4daf0: stur            x0, [fp, #-0x10]
    // 0xa4daf4: StoreField: r0->field_7 = rZR
    //     0xa4daf4: stur            xzr, [x0, #7]
    // 0xa4daf8: ldur            d0, [fp, #-0x48]
    // 0xa4dafc: StoreField: r0->field_f = d0
    //     0xa4dafc: stur            d0, [x0, #0xf]
    // 0xa4db00: r1 = <Offset>
    //     0xa4db00: add             x1, PP, #0x22, lsl #12  ; [pp+0x22100] TypeArguments: <Offset>
    //     0xa4db04: ldr             x1, [x1, #0x100]
    // 0xa4db08: r0 = Tween()
    //     0xa4db08: bl              #0x7e3648  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xa4db0c: mov             x1, x0
    // 0xa4db10: r0 = Instance_Offset
    //     0xa4db10: ldr             x0, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0xa4db14: StoreField: r1->field_b = r0
    //     0xa4db14: stur            w0, [x1, #0xb]
    // 0xa4db18: ldur            x0, [fp, #-0x10]
    // 0xa4db1c: StoreField: r1->field_f = r0
    //     0xa4db1c: stur            w0, [x1, #0xf]
    // 0xa4db20: ldur            x0, [fp, #-8]
    // 0xa4db24: LoadField: r2 = r0->field_27
    //     0xa4db24: ldur            w2, [x0, #0x27]
    // 0xa4db28: DecompressPointer r2
    //     0xa4db28: add             x2, x2, HEAP, lsl #32
    // 0xa4db2c: r16 = Sentinel
    //     0xa4db2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4db30: cmp             w2, w16
    // 0xa4db34: b.eq            #0xa4de08
    // 0xa4db38: r0 = animate()
    //     0xa4db38: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0xa4db3c: ldur            x2, [fp, #-8]
    // 0xa4db40: stur            x0, [fp, #-0x28]
    // 0xa4db44: LoadField: r1 = r2->field_b
    //     0xa4db44: ldur            w1, [x2, #0xb]
    // 0xa4db48: DecompressPointer r1
    //     0xa4db48: add             x1, x1, HEAP, lsl #32
    // 0xa4db4c: stur            x1, [fp, #-0x20]
    // 0xa4db50: cmp             w1, NULL
    // 0xa4db54: b.eq            #0xa4de14
    // 0xa4db58: LoadField: r3 = r1->field_43
    //     0xa4db58: ldur            w3, [x1, #0x43]
    // 0xa4db5c: DecompressPointer r3
    //     0xa4db5c: add             x3, x3, HEAP, lsl #32
    // 0xa4db60: stur            x3, [fp, #-0x10]
    // 0xa4db64: r0 = EdgeInsets()
    //     0xa4db64: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xa4db68: stur            x0, [fp, #-0x38]
    // 0xa4db6c: StoreField: r0->field_7 = rZR
    //     0xa4db6c: stur            xzr, [x0, #7]
    // 0xa4db70: d0 = 10.000000
    //     0xa4db70: fmov            d0, #10.00000000
    // 0xa4db74: StoreField: r0->field_f = d0
    //     0xa4db74: stur            d0, [x0, #0xf]
    // 0xa4db78: ArrayStore: r0[0] = rZR  ; List_8
    //     0xa4db78: stur            xzr, [x0, #0x17]
    // 0xa4db7c: StoreField: r0->field_1f = d0
    //     0xa4db7c: stur            d0, [x0, #0x1f]
    // 0xa4db80: ldur            x1, [fp, #-0x20]
    // 0xa4db84: LoadField: r3 = r1->field_27
    //     0xa4db84: ldur            w3, [x1, #0x27]
    // 0xa4db88: DecompressPointer r3
    //     0xa4db88: add             x3, x3, HEAP, lsl #32
    // 0xa4db8c: ldur            x2, [fp, #-8]
    // 0xa4db90: stur            x3, [fp, #-0x30]
    // 0xa4db94: r1 = Function 'onSizeChange':.
    //     0xa4db94: add             x1, PP, #0x57, lsl #12  ; [pp+0x578e8] AnonymousClosure: (0xa4c7b0), in [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::onSizeChange (0xa4c7ec)
    //     0xa4db98: ldr             x1, [x1, #0x8e8]
    // 0xa4db9c: r0 = AllocateClosure()
    //     0xa4db9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4dba0: stur            x0, [fp, #-8]
    // 0xa4dba4: r0 = MeasureSize()
    //     0xa4dba4: bl              #0xa4de54  ; AllocateMeasureSizeStub -> MeasureSize (size=0x14)
    // 0xa4dba8: mov             x1, x0
    // 0xa4dbac: ldur            x0, [fp, #-8]
    // 0xa4dbb0: stur            x1, [fp, #-0x20]
    // 0xa4dbb4: StoreField: r1->field_f = r0
    //     0xa4dbb4: stur            w0, [x1, #0xf]
    // 0xa4dbb8: ldur            x0, [fp, #-0x30]
    // 0xa4dbbc: StoreField: r1->field_b = r0
    //     0xa4dbbc: stur            w0, [x1, #0xb]
    // 0xa4dbc0: r0 = Center()
    //     0xa4dbc0: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa4dbc4: mov             x1, x0
    // 0xa4dbc8: r0 = Instance_Alignment
    //     0xa4dbc8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa4dbcc: ldr             x0, [x0, #0x898]
    // 0xa4dbd0: stur            x1, [fp, #-8]
    // 0xa4dbd4: StoreField: r1->field_f = r0
    //     0xa4dbd4: stur            w0, [x1, #0xf]
    // 0xa4dbd8: ldur            x0, [fp, #-0x20]
    // 0xa4dbdc: StoreField: r1->field_b = r0
    //     0xa4dbdc: stur            w0, [x1, #0xb]
    // 0xa4dbe0: r0 = Container()
    //     0xa4dbe0: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4dbe4: stur            x0, [fp, #-0x20]
    // 0xa4dbe8: ldur            x16, [fp, #-0x38]
    // 0xa4dbec: r30 = Instance_Color
    //     0xa4dbec: ldr             lr, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xa4dbf0: stp             lr, x16, [SP, #8]
    // 0xa4dbf4: ldur            x16, [fp, #-8]
    // 0xa4dbf8: str             x16, [SP]
    // 0xa4dbfc: mov             x1, x0
    // 0xa4dc00: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, padding, 0x1, null]
    //     0xa4dc00: add             x4, PP, #0x31, lsl #12  ; [pp+0x317c8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "padding", 0x1, Null]
    //     0xa4dc04: ldr             x4, [x4, #0x7c8]
    // 0xa4dc08: r0 = Container()
    //     0xa4dc08: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4dc0c: r0 = GestureDetector()
    //     0xa4dc0c: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa4dc10: stur            x0, [fp, #-8]
    // 0xa4dc14: ldur            x16, [fp, #-0x10]
    // 0xa4dc18: ldur            lr, [fp, #-0x20]
    // 0xa4dc1c: stp             lr, x16, [SP]
    // 0xa4dc20: mov             x1, x0
    // 0xa4dc24: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa4dc24: add             x4, PP, #0x25, lsl #12  ; [pp+0x257d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa4dc28: ldr             x4, [x4, #0x7d0]
    // 0xa4dc2c: r0 = GestureDetector()
    //     0xa4dc2c: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa4dc30: r0 = Material()
    //     0xa4dc30: bl              #0x9e6a2c  ; AllocateMaterialStub -> Material (size=0x40)
    // 0xa4dc34: mov             x1, x0
    // 0xa4dc38: r0 = Instance_MaterialType
    //     0xa4dc38: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2ce18] Obj!MaterialType@e36501
    //     0xa4dc3c: ldr             x0, [x0, #0xe18]
    // 0xa4dc40: stur            x1, [fp, #-0x10]
    // 0xa4dc44: StoreField: r1->field_f = r0
    //     0xa4dc44: stur            w0, [x1, #0xf]
    // 0xa4dc48: StoreField: r1->field_13 = rZR
    //     0xa4dc48: stur            xzr, [x1, #0x13]
    // 0xa4dc4c: r0 = Instance_Color
    //     0xa4dc4c: ldr             x0, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xa4dc50: StoreField: r1->field_1b = r0
    //     0xa4dc50: stur            w0, [x1, #0x1b]
    // 0xa4dc54: r0 = true
    //     0xa4dc54: add             x0, NULL, #0x20  ; true
    // 0xa4dc58: StoreField: r1->field_2f = r0
    //     0xa4dc58: stur            w0, [x1, #0x2f]
    // 0xa4dc5c: r2 = Instance_Clip
    //     0xa4dc5c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa4dc60: ldr             x2, [x2, #0x750]
    // 0xa4dc64: StoreField: r1->field_33 = r2
    //     0xa4dc64: stur            w2, [x1, #0x33]
    // 0xa4dc68: r2 = Instance_Duration
    //     0xa4dc68: add             x2, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa4dc6c: ldr             x2, [x2, #0x368]
    // 0xa4dc70: StoreField: r1->field_37 = r2
    //     0xa4dc70: stur            w2, [x1, #0x37]
    // 0xa4dc74: ldur            x2, [fp, #-8]
    // 0xa4dc78: StoreField: r1->field_b = r2
    //     0xa4dc78: stur            w2, [x1, #0xb]
    // 0xa4dc7c: r0 = ToolTipSlideTransition()
    //     0xa4dc7c: bl              #0xa4de48  ; AllocateToolTipSlideTransitionStub -> ToolTipSlideTransition (size=0x14)
    // 0xa4dc80: mov             x1, x0
    // 0xa4dc84: ldur            x0, [fp, #-0x10]
    // 0xa4dc88: stur            x1, [fp, #-8]
    // 0xa4dc8c: StoreField: r1->field_f = r0
    //     0xa4dc8c: stur            w0, [x1, #0xf]
    // 0xa4dc90: ldur            x0, [fp, #-0x28]
    // 0xa4dc94: StoreField: r1->field_b = r0
    //     0xa4dc94: stur            w0, [x1, #0xb]
    // 0xa4dc98: r0 = FractionalTranslation()
    //     0xa4dc98: bl              #0x935a80  ; AllocateFractionalTranslationStub -> FractionalTranslation (size=0x18)
    // 0xa4dc9c: mov             x2, x0
    // 0xa4dca0: ldur            x0, [fp, #-0x18]
    // 0xa4dca4: stur            x2, [fp, #-0x10]
    // 0xa4dca8: StoreField: r2->field_f = r0
    //     0xa4dca8: stur            w0, [x2, #0xf]
    // 0xa4dcac: r0 = true
    //     0xa4dcac: add             x0, NULL, #0x20  ; true
    // 0xa4dcb0: StoreField: r2->field_13 = r0
    //     0xa4dcb0: stur            w0, [x2, #0x13]
    // 0xa4dcb4: ldur            x0, [fp, #-8]
    // 0xa4dcb8: StoreField: r2->field_b = r0
    //     0xa4dcb8: stur            w0, [x2, #0xb]
    // 0xa4dcbc: ldur            d0, [fp, #-0x58]
    // 0xa4dcc0: r0 = inline_Allocate_Double()
    //     0xa4dcc0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa4dcc4: add             x0, x0, #0x10
    //     0xa4dcc8: cmp             x1, x0
    //     0xa4dccc: b.ls            #0xa4de18
    //     0xa4dcd0: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4dcd4: sub             x0, x0, #0xf
    //     0xa4dcd8: movz            x1, #0xe15c
    //     0xa4dcdc: movk            x1, #0x3, lsl #16
    //     0xa4dce0: stur            x1, [x0, #-1]
    // 0xa4dce4: StoreField: r0->field_7 = d0
    //     0xa4dce4: stur            d0, [x0, #7]
    // 0xa4dce8: stur            x0, [fp, #-8]
    // 0xa4dcec: r1 = <StackParentData>
    //     0xa4dcec: add             x1, PP, #0x25, lsl #12  ; [pp+0x25780] TypeArguments: <StackParentData>
    //     0xa4dcf0: ldr             x1, [x1, #0x780]
    // 0xa4dcf4: r0 = Positioned()
    //     0xa4dcf4: bl              #0x9f19f8  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xa4dcf8: mov             x3, x0
    // 0xa4dcfc: ldur            x0, [fp, #-8]
    // 0xa4dd00: stur            x3, [fp, #-0x18]
    // 0xa4dd04: StoreField: r3->field_13 = r0
    //     0xa4dd04: stur            w0, [x3, #0x13]
    // 0xa4dd08: ldur            d0, [fp, #-0x50]
    // 0xa4dd0c: r0 = inline_Allocate_Double()
    //     0xa4dd0c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa4dd10: add             x0, x0, #0x10
    //     0xa4dd14: cmp             x1, x0
    //     0xa4dd18: b.ls            #0xa4de30
    //     0xa4dd1c: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4dd20: sub             x0, x0, #0xf
    //     0xa4dd24: movz            x1, #0xe15c
    //     0xa4dd28: movk            x1, #0x3, lsl #16
    //     0xa4dd2c: stur            x1, [x0, #-1]
    // 0xa4dd30: StoreField: r0->field_7 = d0
    //     0xa4dd30: stur            d0, [x0, #7]
    // 0xa4dd34: ArrayStore: r3[0] = r0  ; List_4
    //     0xa4dd34: stur            w0, [x3, #0x17]
    // 0xa4dd38: ldur            x0, [fp, #-0x10]
    // 0xa4dd3c: StoreField: r3->field_b = r0
    //     0xa4dd3c: stur            w0, [x3, #0xb]
    // 0xa4dd40: r1 = Null
    //     0xa4dd40: mov             x1, NULL
    // 0xa4dd44: r2 = 2
    //     0xa4dd44: movz            x2, #0x2
    // 0xa4dd48: r0 = AllocateArray()
    //     0xa4dd48: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa4dd4c: mov             x2, x0
    // 0xa4dd50: ldur            x0, [fp, #-0x18]
    // 0xa4dd54: stur            x2, [fp, #-8]
    // 0xa4dd58: StoreField: r2->field_f = r0
    //     0xa4dd58: stur            w0, [x2, #0xf]
    // 0xa4dd5c: r1 = <Widget>
    //     0xa4dd5c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa4dd60: r0 = AllocateGrowableArray()
    //     0xa4dd60: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa4dd64: mov             x1, x0
    // 0xa4dd68: ldur            x0, [fp, #-8]
    // 0xa4dd6c: stur            x1, [fp, #-0x10]
    // 0xa4dd70: StoreField: r1->field_f = r0
    //     0xa4dd70: stur            w0, [x1, #0xf]
    // 0xa4dd74: r0 = 2
    //     0xa4dd74: movz            x0, #0x2
    // 0xa4dd78: StoreField: r1->field_b = r0
    //     0xa4dd78: stur            w0, [x1, #0xb]
    // 0xa4dd7c: r0 = Stack()
    //     0xa4dd7c: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa4dd80: r1 = Instance_AlignmentDirectional
    //     0xa4dd80: add             x1, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0xa4dd84: ldr             x1, [x1, #0x7b0]
    // 0xa4dd88: StoreField: r0->field_f = r1
    //     0xa4dd88: stur            w1, [x0, #0xf]
    // 0xa4dd8c: r1 = Instance_StackFit
    //     0xa4dd8c: add             x1, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xa4dd90: ldr             x1, [x1, #0x7b8]
    // 0xa4dd94: ArrayStore: r0[0] = r1  ; List_4
    //     0xa4dd94: stur            w1, [x0, #0x17]
    // 0xa4dd98: r1 = Instance_Clip
    //     0xa4dd98: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa4dd9c: ldr             x1, [x1, #0x7c0]
    // 0xa4dda0: StoreField: r0->field_1b = r1
    //     0xa4dda0: stur            w1, [x0, #0x1b]
    // 0xa4dda4: ldur            x1, [fp, #-0x10]
    // 0xa4dda8: StoreField: r0->field_b = r1
    //     0xa4dda8: stur            w1, [x0, #0xb]
    // 0xa4ddac: LeaveFrame
    //     0xa4ddac: mov             SP, fp
    //     0xa4ddb0: ldp             fp, lr, [SP], #0x10
    // 0xa4ddb4: ret
    //     0xa4ddb4: ret             
    // 0xa4ddb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ddb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ddbc: b               #0xa4d8d8
    // 0xa4ddc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ddc0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ddc4: stp             q0, q1, [SP, #-0x20]!
    // 0xa4ddc8: SaveReg r0
    //     0xa4ddc8: str             x0, [SP, #-8]!
    // 0xa4ddcc: r0 = AllocateDouble()
    //     0xa4ddcc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4ddd0: mov             x2, x0
    // 0xa4ddd4: RestoreReg r0
    //     0xa4ddd4: ldr             x0, [SP], #8
    // 0xa4ddd8: ldp             q0, q1, [SP], #0x20
    // 0xa4dddc: b               #0xa4d968
    // 0xa4dde0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4dde0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4dde4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4dde4: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4dde8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4dde8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4ddec: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4ddec: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4ddf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ddf0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ddf4: r9 = _scaleAnimationController
    //     0xa4ddf4: add             x9, PP, #0x57, lsl #12  ; [pp+0x578f0] Field <_ToolTipWidgetState@**********._scaleAnimationController@**********>: late final (offset: 0x2c)
    //     0xa4ddf8: ldr             x9, [x9, #0x8f0]
    // 0xa4ddfc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4ddfc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4de00: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4de00: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4de04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4de04: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4de08: r9 = _movingAnimation
    //     0xa4de08: add             x9, PP, #0x57, lsl #12  ; [pp+0x578f8] Field <_ToolTipWidgetState@**********._movingAnimation@**********>: late final (offset: 0x28)
    //     0xa4de0c: ldr             x9, [x9, #0x8f8]
    // 0xa4de10: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4de10: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4de14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4de14: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4de18: SaveReg d0
    //     0xa4de18: str             q0, [SP, #-0x10]!
    // 0xa4de1c: SaveReg r2
    //     0xa4de1c: str             x2, [SP, #-8]!
    // 0xa4de20: r0 = AllocateDouble()
    //     0xa4de20: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4de24: RestoreReg r2
    //     0xa4de24: ldr             x2, [SP], #8
    // 0xa4de28: RestoreReg d0
    //     0xa4de28: ldr             q0, [SP], #0x10
    // 0xa4de2c: b               #0xa4dce4
    // 0xa4de30: SaveReg d0
    //     0xa4de30: str             q0, [SP, #-0x10]!
    // 0xa4de34: SaveReg r3
    //     0xa4de34: str             x3, [SP, #-8]!
    // 0xa4de38: r0 = AllocateDouble()
    //     0xa4de38: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4de3c: RestoreReg r3
    //     0xa4de3c: ldr             x3, [SP], #8
    // 0xa4de40: RestoreReg d0
    //     0xa4de40: ldr             q0, [SP], #0x10
    // 0xa4de44: b               #0xa4dd30
  }
  _ _getSpace(/* No info */) {
    // ** addr: 0xa4de60, size: 0xd0
    // 0xa4de60: EnterFrame
    //     0xa4de60: stp             fp, lr, [SP, #-0x10]!
    //     0xa4de64: mov             fp, SP
    // 0xa4de68: AllocStack(0x8)
    //     0xa4de68: sub             SP, SP, #8
    // 0xa4de6c: SetupParameters(_ToolTipWidgetState this /* r1 => r0, fp-0x8 */)
    //     0xa4de6c: mov             x0, x1
    //     0xa4de70: stur            x1, [fp, #-8]
    // 0xa4de74: CheckStackOverflow
    //     0xa4de74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4de78: cmp             SP, x16
    //     0xa4de7c: b.ls            #0xa4df1c
    // 0xa4de80: LoadField: r1 = r0->field_b
    //     0xa4de80: ldur            w1, [x0, #0xb]
    // 0xa4de84: DecompressPointer r1
    //     0xa4de84: add             x1, x1, HEAP, lsl #32
    // 0xa4de88: cmp             w1, NULL
    // 0xa4de8c: b.eq            #0xa4df24
    // 0xa4de90: LoadField: r2 = r1->field_b
    //     0xa4de90: ldur            w2, [x1, #0xb]
    // 0xa4de94: DecompressPointer r2
    //     0xa4de94: add             x2, x2, HEAP, lsl #32
    // 0xa4de98: cmp             w2, NULL
    // 0xa4de9c: b.eq            #0xa4df28
    // 0xa4dea0: mov             x1, x2
    // 0xa4dea4: r0 = getCenter()
    //     0xa4dea4: bl              #0xa4df30  ; [package:showcaseview/src/get_position.dart] GetPosition::getCenter
    // 0xa4dea8: ldur            x0, [fp, #-8]
    // 0xa4deac: LoadField: r1 = r0->field_b
    //     0xa4deac: ldur            w1, [x0, #0xb]
    // 0xa4deb0: DecompressPointer r1
    //     0xa4deb0: add             x1, x1, HEAP, lsl #32
    // 0xa4deb4: cmp             w1, NULL
    // 0xa4deb8: b.eq            #0xa4df2c
    // 0xa4debc: LoadField: d1 = r1->field_3b
    //     0xa4debc: ldur            d1, [x1, #0x3b]
    // 0xa4dec0: d2 = 2.000000
    //     0xa4dec0: fmov            d2, #2.00000000
    // 0xa4dec4: fdiv            d3, d1, d2
    // 0xa4dec8: fsub            d2, d0, d3
    // 0xa4decc: fadd            d4, d2, d1
    // 0xa4ded0: LoadField: r0 = r1->field_13
    //     0xa4ded0: ldur            w0, [x1, #0x13]
    // 0xa4ded4: DecompressPointer r0
    //     0xa4ded4: add             x0, x0, HEAP, lsl #32
    // 0xa4ded8: LoadField: d5 = r0->field_7
    //     0xa4ded8: ldur            d5, [x0, #7]
    // 0xa4dedc: fcmp            d4, d5
    // 0xa4dee0: b.le            #0xa4def8
    // 0xa4dee4: d4 = 8.000000
    //     0xa4dee4: fmov            d4, #8.00000000
    // 0xa4dee8: fsub            d6, d5, d1
    // 0xa4deec: fsub            d1, d6, d4
    // 0xa4def0: mov             v0.16b, v1.16b
    // 0xa4def4: b               #0xa4df10
    // 0xa4def8: fcmp            d3, d2
    // 0xa4defc: b.le            #0xa4df08
    // 0xa4df00: d1 = 16.000000
    //     0xa4df00: fmov            d1, #16.00000000
    // 0xa4df04: b               #0xa4df0c
    // 0xa4df08: mov             v1.16b, v2.16b
    // 0xa4df0c: mov             v0.16b, v1.16b
    // 0xa4df10: LeaveFrame
    //     0xa4df10: mov             SP, fp
    //     0xa4df14: ldp             fp, lr, [SP], #0x10
    // 0xa4df18: ret
    //     0xa4df18: ret             
    // 0xa4df1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4df1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4df20: b               #0xa4de80
    // 0xa4df24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4df24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4df28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4df28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4df2c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4df2c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ findPositionForContent(/* No info */) {
    // ** addr: 0xa4e3b8, size: 0x270
    // 0xa4e3b8: EnterFrame
    //     0xa4e3b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e3bc: mov             fp, SP
    // 0xa4e3c0: AllocStack(0x30)
    //     0xa4e3c0: sub             SP, SP, #0x30
    // 0xa4e3c4: SetupParameters(_ToolTipWidgetState this /* r1 => r0, fp-0x8 */)
    //     0xa4e3c4: mov             x0, x1
    //     0xa4e3c8: stur            x1, [fp, #-8]
    // 0xa4e3cc: CheckStackOverflow
    //     0xa4e3cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4e3d0: cmp             SP, x16
    //     0xa4e3d4: b.ls            #0xa4e5ec
    // 0xa4e3d8: LoadField: r1 = r0->field_b
    //     0xa4e3d8: ldur            w1, [x0, #0xb]
    // 0xa4e3dc: DecompressPointer r1
    //     0xa4e3dc: add             x1, x1, HEAP, lsl #32
    // 0xa4e3e0: cmp             w1, NULL
    // 0xa4e3e4: b.eq            #0xa4e5f4
    // 0xa4e3e8: LoadField: d0 = r2->field_f
    //     0xa4e3e8: ldur            d0, [x2, #0xf]
    // 0xa4e3ec: stur            d0, [fp, #-0x20]
    // 0xa4e3f0: LoadField: r2 = r1->field_b
    //     0xa4e3f0: ldur            w2, [x1, #0xb]
    // 0xa4e3f4: DecompressPointer r2
    //     0xa4e3f4: add             x2, x2, HEAP, lsl #32
    // 0xa4e3f8: cmp             w2, NULL
    // 0xa4e3fc: b.ne            #0xa4e408
    // 0xa4e400: r0 = Null
    //     0xa4e400: mov             x0, NULL
    // 0xa4e404: b               #0xa4e438
    // 0xa4e408: mov             x1, x2
    // 0xa4e40c: r0 = getHeight()
    //     0xa4e40c: bl              #0xa4e628  ; [package:showcaseview/src/get_position.dart] GetPosition::getHeight
    // 0xa4e410: r0 = inline_Allocate_Double()
    //     0xa4e410: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa4e414: add             x0, x0, #0x10
    //     0xa4e418: cmp             x1, x0
    //     0xa4e41c: b.ls            #0xa4e5f8
    //     0xa4e420: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4e424: sub             x0, x0, #0xf
    //     0xa4e428: movz            x1, #0xe15c
    //     0xa4e42c: movk            x1, #0x3, lsl #16
    //     0xa4e430: stur            x1, [x0, #-1]
    // 0xa4e434: StoreField: r0->field_7 = d0
    //     0xa4e434: stur            d0, [x0, #7]
    // 0xa4e438: cmp             w0, NULL
    // 0xa4e43c: b.ne            #0xa4e448
    // 0xa4e440: d2 = 0.000000
    //     0xa4e440: eor             v2.16b, v2.16b, v2.16b
    // 0xa4e444: b               #0xa4e450
    // 0xa4e448: LoadField: d0 = r0->field_7
    //     0xa4e448: ldur            d0, [x0, #7]
    // 0xa4e44c: mov             v2.16b, v0.16b
    // 0xa4e450: ldur            x0, [fp, #-8]
    // 0xa4e454: ldur            d0, [fp, #-0x20]
    // 0xa4e458: d1 = 2.000000
    //     0xa4e458: fmov            d1, #2.00000000
    // 0xa4e45c: fdiv            d3, d2, d1
    // 0xa4e460: fadd            d2, d0, d3
    // 0xa4e464: stur            d2, [fp, #-0x28]
    // 0xa4e468: LoadField: r1 = r0->field_b
    //     0xa4e468: ldur            w1, [x0, #0xb]
    // 0xa4e46c: DecompressPointer r1
    //     0xa4e46c: add             x1, x1, HEAP, lsl #32
    // 0xa4e470: cmp             w1, NULL
    // 0xa4e474: b.eq            #0xa4e608
    // 0xa4e478: LoadField: r2 = r1->field_b
    //     0xa4e478: ldur            w2, [x1, #0xb]
    // 0xa4e47c: DecompressPointer r2
    //     0xa4e47c: add             x2, x2, HEAP, lsl #32
    // 0xa4e480: cmp             w2, NULL
    // 0xa4e484: b.ne            #0xa4e490
    // 0xa4e488: r0 = Null
    //     0xa4e488: mov             x0, NULL
    // 0xa4e48c: b               #0xa4e4c0
    // 0xa4e490: mov             x1, x2
    // 0xa4e494: r0 = getHeight()
    //     0xa4e494: bl              #0xa4e628  ; [package:showcaseview/src/get_position.dart] GetPosition::getHeight
    // 0xa4e498: r0 = inline_Allocate_Double()
    //     0xa4e498: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa4e49c: add             x0, x0, #0x10
    //     0xa4e4a0: cmp             x1, x0
    //     0xa4e4a4: b.ls            #0xa4e60c
    //     0xa4e4a8: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4e4ac: sub             x0, x0, #0xf
    //     0xa4e4b0: movz            x1, #0xe15c
    //     0xa4e4b4: movk            x1, #0x3, lsl #16
    //     0xa4e4b8: stur            x1, [x0, #-1]
    // 0xa4e4bc: StoreField: r0->field_7 = d0
    //     0xa4e4bc: stur            d0, [x0, #7]
    // 0xa4e4c0: cmp             w0, NULL
    // 0xa4e4c4: b.ne            #0xa4e4d0
    // 0xa4e4c8: d3 = 0.000000
    //     0xa4e4c8: eor             v3.16b, v3.16b, v3.16b
    // 0xa4e4cc: b               #0xa4e4d8
    // 0xa4e4d0: LoadField: d0 = r0->field_7
    //     0xa4e4d0: ldur            d0, [x0, #7]
    // 0xa4e4d4: mov             v3.16b, v0.16b
    // 0xa4e4d8: ldur            x0, [fp, #-8]
    // 0xa4e4dc: ldur            d2, [fp, #-0x28]
    // 0xa4e4e0: ldur            d0, [fp, #-0x20]
    // 0xa4e4e4: d1 = 2.000000
    //     0xa4e4e4: fmov            d1, #2.00000000
    // 0xa4e4e8: fdiv            d4, d3, d1
    // 0xa4e4ec: fsub            d1, d0, d4
    // 0xa4e4f0: stur            d1, [fp, #-0x30]
    // 0xa4e4f4: r1 = LoadStaticField(0x7d4)
    //     0xa4e4f4: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xa4e4f8: ldr             x1, [x1, #0xfa8]
    // 0xa4e4fc: cmp             w1, NULL
    // 0xa4e500: b.eq            #0xa4e61c
    // 0xa4e504: r0 = InitLateStaticField(0x5f8) // [dart:ui] ::window
    //     0xa4e504: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4e508: ldr             x0, [x0, #0xbf0]
    //     0xa4e50c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4e510: cmp             w0, w16
    //     0xa4e514: b.ne            #0xa4e524
    //     0xa4e518: add             x2, PP, #0xd, lsl #12  ; [pp+0xdab0] Field <::.window>: static late final (offset: 0x5f8)
    //     0xa4e51c: ldr             x2, [x2, #0xab0]
    //     0xa4e520: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa4e524: mov             x1, x0
    // 0xa4e528: stur            x0, [fp, #-0x10]
    // 0xa4e52c: r0 = _viewConfiguration()
    //     0xa4e52c: bl              #0xd73d10  ; [dart:ui] SingletonFlutterWindow::_viewConfiguration
    // 0xa4e530: LoadField: r2 = r0->field_1b
    //     0xa4e530: ldur            w2, [x0, #0x1b]
    // 0xa4e534: DecompressPointer r2
    //     0xa4e534: add             x2, x2, HEAP, lsl #32
    // 0xa4e538: stur            x2, [fp, #-0x18]
    // 0xa4e53c: r0 = LoadStaticField(0x7d4)
    //     0xa4e53c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4e540: ldr             x0, [x0, #0xfa8]
    // 0xa4e544: cmp             w0, NULL
    // 0xa4e548: b.eq            #0xa4e620
    // 0xa4e54c: ldur            x1, [fp, #-0x10]
    // 0xa4e550: r0 = _viewConfiguration()
    //     0xa4e550: bl              #0xd73d10  ; [dart:ui] SingletonFlutterWindow::_viewConfiguration
    // 0xa4e554: LoadField: d0 = r0->field_f
    //     0xa4e554: ldur            d0, [x0, #0xf]
    // 0xa4e558: stur            d0, [fp, #-0x20]
    // 0xa4e55c: r0 = EdgeInsets()
    //     0xa4e55c: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xa4e560: mov             x1, x0
    // 0xa4e564: ldur            x2, [fp, #-0x18]
    // 0xa4e568: ldur            d0, [fp, #-0x20]
    // 0xa4e56c: stur            x0, [fp, #-0x10]
    // 0xa4e570: r0 = EdgeInsets.fromViewPadding()
    //     0xa4e570: bl              #0x687fb0  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsets::EdgeInsets.fromViewPadding
    // 0xa4e574: ldur            x1, [fp, #-8]
    // 0xa4e578: LoadField: r2 = r1->field_b
    //     0xa4e578: ldur            w2, [x1, #0xb]
    // 0xa4e57c: DecompressPointer r2
    //     0xa4e57c: add             x2, x2, HEAP, lsl #32
    // 0xa4e580: cmp             w2, NULL
    // 0xa4e584: b.eq            #0xa4e624
    // 0xa4e588: LoadField: r1 = r2->field_13
    //     0xa4e588: ldur            w1, [x2, #0x13]
    // 0xa4e58c: DecompressPointer r1
    //     0xa4e58c: add             x1, x1, HEAP, lsl #32
    // 0xa4e590: LoadField: d0 = r1->field_f
    //     0xa4e590: ldur            d0, [x1, #0xf]
    // 0xa4e594: ldur            x1, [fp, #-0x10]
    // 0xa4e598: LoadField: d1 = r1->field_1f
    //     0xa4e598: ldur            d1, [x1, #0x1f]
    // 0xa4e59c: fsub            d2, d0, d1
    // 0xa4e5a0: ldur            d0, [fp, #-0x28]
    // 0xa4e5a4: fsub            d1, d2, d0
    // 0xa4e5a8: d0 = 50.000000
    //     0xa4e5a8: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0xa4e5ac: fcmp            d1, d0
    // 0xa4e5b0: r16 = true
    //     0xa4e5b0: add             x16, NULL, #0x20  ; true
    // 0xa4e5b4: r17 = false
    //     0xa4e5b4: add             x17, NULL, #0x30  ; false
    // 0xa4e5b8: csel            x1, x16, x17, ge
    // 0xa4e5bc: ldur            d1, [fp, #-0x30]
    // 0xa4e5c0: fcmp            d1, d0
    // 0xa4e5c4: b.lt            #0xa4e5d8
    // 0xa4e5c8: tbz             w1, #4, #0xa4e5d8
    // 0xa4e5cc: r0 = Instance_TooltipPosition
    //     0xa4e5cc: add             x0, PP, #0x57, lsl #12  ; [pp+0x57938] Obj!TooltipPosition@e2e121
    //     0xa4e5d0: ldr             x0, [x0, #0x938]
    // 0xa4e5d4: b               #0xa4e5e0
    // 0xa4e5d8: r0 = Instance_TooltipPosition
    //     0xa4e5d8: add             x0, PP, #0x57, lsl #12  ; [pp+0x578e0] Obj!TooltipPosition@e2e141
    //     0xa4e5dc: ldr             x0, [x0, #0x8e0]
    // 0xa4e5e0: LeaveFrame
    //     0xa4e5e0: mov             SP, fp
    //     0xa4e5e4: ldp             fp, lr, [SP], #0x10
    // 0xa4e5e8: ret
    //     0xa4e5e8: ret             
    // 0xa4e5ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4e5ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4e5f0: b               #0xa4e3d8
    // 0xa4e5f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4e5f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4e5f8: SaveReg d0
    //     0xa4e5f8: str             q0, [SP, #-0x10]!
    // 0xa4e5fc: r0 = AllocateDouble()
    //     0xa4e5fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4e600: RestoreReg d0
    //     0xa4e600: ldr             q0, [SP], #0x10
    // 0xa4e604: b               #0xa4e434
    // 0xa4e608: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4e608: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4e60c: SaveReg d0
    //     0xa4e60c: str             q0, [SP, #-0x10]!
    // 0xa4e610: r0 = AllocateDouble()
    //     0xa4e610: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4e614: RestoreReg d0
    //     0xa4e614: ldr             q0, [SP], #0x10
    // 0xa4e618: b               #0xa4e4bc
    // 0xa4e61c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4e61c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4e620: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4e620: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4e624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4e624: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa840e0, size: 0x8c
    // 0xa840e0: EnterFrame
    //     0xa840e0: stp             fp, lr, [SP, #-0x10]!
    //     0xa840e4: mov             fp, SP
    // 0xa840e8: AllocStack(0x8)
    //     0xa840e8: sub             SP, SP, #8
    // 0xa840ec: SetupParameters(_ToolTipWidgetState this /* r1 => r0, fp-0x8 */)
    //     0xa840ec: mov             x0, x1
    //     0xa840f0: stur            x1, [fp, #-8]
    // 0xa840f4: CheckStackOverflow
    //     0xa840f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa840f8: cmp             SP, x16
    //     0xa840fc: b.ls            #0xa8414c
    // 0xa84100: LoadField: r1 = r0->field_23
    //     0xa84100: ldur            w1, [x0, #0x23]
    // 0xa84104: DecompressPointer r1
    //     0xa84104: add             x1, x1, HEAP, lsl #32
    // 0xa84108: r16 = Sentinel
    //     0xa84108: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8410c: cmp             w1, w16
    // 0xa84110: b.eq            #0xa84154
    // 0xa84114: r0 = dispose()
    //     0xa84114: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa84118: ldur            x0, [fp, #-8]
    // 0xa8411c: LoadField: r1 = r0->field_2b
    //     0xa8411c: ldur            w1, [x0, #0x2b]
    // 0xa84120: DecompressPointer r1
    //     0xa84120: add             x1, x1, HEAP, lsl #32
    // 0xa84124: r16 = Sentinel
    //     0xa84124: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa84128: cmp             w1, w16
    // 0xa8412c: b.eq            #0xa84160
    // 0xa84130: r0 = dispose()
    //     0xa84130: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa84134: ldur            x1, [fp, #-8]
    // 0xa84138: r0 = dispose()
    //     0xa84138: bl              #0xa8416c  ; [package:showcaseview/src/tooltip_widget.dart] __ToolTipWidgetState&State&TickerProviderStateMixin::dispose
    // 0xa8413c: r0 = Null
    //     0xa8413c: mov             x0, NULL
    // 0xa84140: LeaveFrame
    //     0xa84140: mov             SP, fp
    //     0xa84144: ldp             fp, lr, [SP], #0x10
    // 0xa84148: ret
    //     0xa84148: ret             
    // 0xa8414c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8414c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa84150: b               #0xa84100
    // 0xa84154: r9 = _movingAnimationController
    //     0xa84154: add             x9, PP, #0x57, lsl #12  ; [pp+0x57940] Field <_ToolTipWidgetState@**********._movingAnimationController@**********>: late final (offset: 0x24)
    //     0xa84158: ldr             x9, [x9, #0x940]
    // 0xa8415c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa8415c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa84160: r9 = _scaleAnimationController
    //     0xa84160: add             x9, PP, #0x57, lsl #12  ; [pp+0x578f0] Field <_ToolTipWidgetState@**********._scaleAnimationController@**********>: late final (offset: 0x2c)
    //     0xa84164: ldr             x9, [x9, #0x8f0]
    // 0xa84168: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa84168: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _ToolTipWidgetState(/* No info */) {
    // ** addr: 0xa953e4, size: 0xf8
    // 0xa953e4: EnterFrame
    //     0xa953e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa953e8: mov             fp, SP
    // 0xa953ec: AllocStack(0x10)
    //     0xa953ec: sub             SP, SP, #0x10
    // 0xa953f0: r2 = false
    //     0xa953f0: add             x2, NULL, #0x30  ; false
    // 0xa953f4: r0 = Sentinel
    //     0xa953f4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa953f8: d1 = 20.000000
    //     0xa953f8: fmov            d1, #20.00000000
    // 0xa953fc: d0 = 15.000000
    //     0xa953fc: fmov            d0, #15.00000000
    // 0xa95400: mov             x3, x1
    // 0xa95404: stur            x1, [fp, #-8]
    // 0xa95408: CheckStackOverflow
    //     0xa95408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9540c: cmp             SP, x16
    //     0xa95410: b.ls            #0xa954d4
    // 0xa95414: StoreField: r3->field_1f = r2
    //     0xa95414: stur            w2, [x3, #0x1f]
    // 0xa95418: StoreField: r3->field_23 = r0
    //     0xa95418: stur            w0, [x3, #0x23]
    // 0xa9541c: StoreField: r3->field_27 = r0
    //     0xa9541c: stur            w0, [x3, #0x27]
    // 0xa95420: StoreField: r3->field_2b = r0
    //     0xa95420: stur            w0, [x3, #0x2b]
    // 0xa95424: StoreField: r3->field_2f = r0
    //     0xa95424: stur            w0, [x3, #0x2f]
    // 0xa95428: StoreField: r3->field_33 = d1
    //     0xa95428: stur            d1, [x3, #0x33]
    // 0xa9542c: StoreField: r3->field_3b = d0
    //     0xa9542c: stur            d0, [x3, #0x3b]
    // 0xa95430: r1 = <State<StatefulWidget>>
    //     0xa95430: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xa95434: r0 = LabeledGlobalKey()
    //     0xa95434: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xa95438: ldur            x2, [fp, #-8]
    // 0xa9543c: StoreField: r2->field_43 = r0
    //     0xa9543c: stur            w0, [x2, #0x43]
    //     0xa95440: ldurb           w16, [x2, #-1]
    //     0xa95444: ldurb           w17, [x0, #-1]
    //     0xa95448: and             x16, x17, x16, lsr #2
    //     0xa9544c: tst             x16, HEAP, lsr #32
    //     0xa95450: b.eq            #0xa95458
    //     0xa95454: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa95458: r1 = <double>
    //     0xa95458: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa9545c: r0 = ValueNotifier()
    //     0xa9545c: bl              #0x65a810  ; AllocateValueNotifierStub -> ValueNotifier<X0> (size=0x2c)
    // 0xa95460: mov             x1, x0
    // 0xa95464: r0 = 1.000000
    //     0xa95464: ldr             x0, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xa95468: stur            x1, [fp, #-0x10]
    // 0xa9546c: StoreField: r1->field_27 = r0
    //     0xa9546c: stur            w0, [x1, #0x27]
    // 0xa95470: StoreField: r1->field_7 = rZR
    //     0xa95470: stur            xzr, [x1, #7]
    // 0xa95474: StoreField: r1->field_13 = rZR
    //     0xa95474: stur            xzr, [x1, #0x13]
    // 0xa95478: StoreField: r1->field_1b = rZR
    //     0xa95478: stur            xzr, [x1, #0x1b]
    // 0xa9547c: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0xa9547c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa95480: ldr             x0, [x0, #0xca8]
    //     0xa95484: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa95488: cmp             w0, w16
    //     0xa9548c: b.ne            #0xa95498
    //     0xa95490: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0xa95494: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa95498: mov             x1, x0
    // 0xa9549c: ldur            x0, [fp, #-0x10]
    // 0xa954a0: StoreField: r0->field_f = r1
    //     0xa954a0: stur            w1, [x0, #0xf]
    // 0xa954a4: ldur            x1, [fp, #-8]
    // 0xa954a8: StoreField: r1->field_47 = r0
    //     0xa954a8: stur            w0, [x1, #0x47]
    //     0xa954ac: ldurb           w16, [x1, #-1]
    //     0xa954b0: ldurb           w17, [x0, #-1]
    //     0xa954b4: and             x16, x17, x16, lsr #2
    //     0xa954b8: tst             x16, HEAP, lsr #32
    //     0xa954bc: b.eq            #0xa954c4
    //     0xa954c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa954c4: r0 = Null
    //     0xa954c4: mov             x0, NULL
    // 0xa954c8: LeaveFrame
    //     0xa954c8: mov             SP, fp
    //     0xa954cc: ldp             fp, lr, [SP], #0x10
    // 0xa954d0: ret
    //     0xa954d0: ret             
    // 0xa954d4: r0 = StackOverflowSharedWithFPURegs()
    //     0xa954d4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa954d8: b               #0xa95414
  }
}

// class id: 4689, size: 0x6c, field offset: 0xc
//   const constructor, 
class ToolTipWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa9539c, size: 0x48
    // 0xa9539c: EnterFrame
    //     0xa9539c: stp             fp, lr, [SP, #-0x10]!
    //     0xa953a0: mov             fp, SP
    // 0xa953a4: AllocStack(0x8)
    //     0xa953a4: sub             SP, SP, #8
    // 0xa953a8: CheckStackOverflow
    //     0xa953a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa953ac: cmp             SP, x16
    //     0xa953b0: b.ls            #0xa953dc
    // 0xa953b4: r1 = <ToolTipWidget>
    //     0xa953b4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51118] TypeArguments: <ToolTipWidget>
    //     0xa953b8: ldr             x1, [x1, #0x118]
    // 0xa953bc: r0 = _ToolTipWidgetState()
    //     0xa953bc: bl              #0xa954dc  ; Allocate_ToolTipWidgetStateStub -> _ToolTipWidgetState (size=0x4c)
    // 0xa953c0: mov             x1, x0
    // 0xa953c4: stur            x0, [fp, #-8]
    // 0xa953c8: r0 = _ToolTipWidgetState()
    //     0xa953c8: bl              #0xa953e4  ; [package:showcaseview/src/tooltip_widget.dart] _ToolTipWidgetState::_ToolTipWidgetState
    // 0xa953cc: ldur            x0, [fp, #-8]
    // 0xa953d0: LeaveFrame
    //     0xa953d0: mov             SP, fp
    //     0xa953d4: ldp             fp, lr, [SP], #0x10
    // 0xa953d8: ret
    //     0xa953d8: ret             
    // 0xa953dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa953dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa953e0: b               #0xa953b4
  }
}
