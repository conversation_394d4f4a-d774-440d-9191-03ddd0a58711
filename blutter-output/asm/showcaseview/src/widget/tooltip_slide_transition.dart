// lib: , url: package:showcaseview/src/widget/tooltip_slide_transition.dart

// class id: 1051129, size: 0x8
class :: {
}

// class id: 4845, size: 0x14, field offset: 0x10
//   const constructor, 
class ToolTipSlideTransition extends AnimatedWidget {

  _ build(/* No info */) {
    // ** addr: 0xc21a50, size: 0xb4
    // 0xc21a50: EnterFrame
    //     0xc21a50: stp             fp, lr, [SP, #-0x10]!
    //     0xc21a54: mov             fp, SP
    // 0xc21a58: AllocStack(0x18)
    //     0xc21a58: sub             SP, SP, #0x18
    // 0xc21a5c: SetupParameters(ToolTipSlideTransition this /* r1 => r3, fp-0x10 */)
    //     0xc21a5c: mov             x3, x1
    //     0xc21a60: stur            x1, [fp, #-0x10]
    // 0xc21a64: CheckStackOverflow
    //     0xc21a64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc21a68: cmp             SP, x16
    //     0xc21a6c: b.ls            #0xc21afc
    // 0xc21a70: LoadField: r4 = r3->field_b
    //     0xc21a70: ldur            w4, [x3, #0xb]
    // 0xc21a74: DecompressPointer r4
    //     0xc21a74: add             x4, x4, HEAP, lsl #32
    // 0xc21a78: mov             x0, x4
    // 0xc21a7c: stur            x4, [fp, #-8]
    // 0xc21a80: r2 = Null
    //     0xc21a80: mov             x2, NULL
    // 0xc21a84: r1 = Null
    //     0xc21a84: mov             x1, NULL
    // 0xc21a88: r8 = Animation<Offset>
    //     0xc21a88: add             x8, PP, #0x30, lsl #12  ; [pp+0x30ed0] Type: Animation<Offset>
    //     0xc21a8c: ldr             x8, [x8, #0xed0]
    // 0xc21a90: r3 = Null
    //     0xc21a90: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5ae60] Null
    //     0xc21a94: ldr             x3, [x3, #0xe60]
    // 0xc21a98: r0 = Animation<Offset>()
    //     0xc21a98: bl              #0x9da734  ; IsType_Animation<Offset>_Stub
    // 0xc21a9c: ldur            x1, [fp, #-8]
    // 0xc21aa0: r0 = LoadClassIdInstr(r1)
    //     0xc21aa0: ldur            x0, [x1, #-1]
    //     0xc21aa4: ubfx            x0, x0, #0xc, #0x14
    // 0xc21aa8: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xc21aa8: movz            x17, #0x276f
    //     0xc21aac: movk            x17, #0x1, lsl #16
    //     0xc21ab0: add             lr, x0, x17
    //     0xc21ab4: ldr             lr, [x21, lr, lsl #3]
    //     0xc21ab8: blr             lr
    // 0xc21abc: mov             x1, x0
    // 0xc21ac0: ldur            x0, [fp, #-0x10]
    // 0xc21ac4: stur            x1, [fp, #-0x18]
    // 0xc21ac8: LoadField: r2 = r0->field_f
    //     0xc21ac8: ldur            w2, [x0, #0xf]
    // 0xc21acc: DecompressPointer r2
    //     0xc21acc: add             x2, x2, HEAP, lsl #32
    // 0xc21ad0: stur            x2, [fp, #-8]
    // 0xc21ad4: r0 = Transform()
    //     0xc21ad4: bl              #0x9d3c68  ; AllocateTransformStub -> Transform (size=0x24)
    // 0xc21ad8: mov             x1, x0
    // 0xc21adc: ldur            x2, [fp, #-8]
    // 0xc21ae0: ldur            x3, [fp, #-0x18]
    // 0xc21ae4: stur            x0, [fp, #-8]
    // 0xc21ae8: r0 = Transform.translate()
    //     0xc21ae8: bl              #0xa1ce0c  ; [package:flutter/src/widgets/basic.dart] Transform::Transform.translate
    // 0xc21aec: ldur            x0, [fp, #-8]
    // 0xc21af0: LeaveFrame
    //     0xc21af0: mov             SP, fp
    //     0xc21af4: ldp             fp, lr, [SP], #0x10
    // 0xc21af8: ret
    //     0xc21af8: ret             
    // 0xc21afc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc21afc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc21b00: b               #0xc21a70
  }
}
