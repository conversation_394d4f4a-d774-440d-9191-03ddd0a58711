// lib: simple_gesture_detector, url: package:simple_gesture_detector/simple_gesture_detector.dart

// class id: 1051130, size: 0x8
class :: {
}

// class id: 501, size: 0x1c, field offset: 0x8
//   const constructor, 
class SimpleSwipeConfig extends Object {

  _Double field_8;
  _Double field_10;
  SwipeDetectionBehavior field_18;
}

// class id: 4082, size: 0x20, field offset: 0x14
class _SimpleGestureDetectorState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xa4e724, size: 0x120
    // 0xa4e724: EnterFrame
    //     0xa4e724: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e728: mov             fp, SP
    // 0xa4e72c: AllocStack(0x78)
    //     0xa4e72c: sub             SP, SP, #0x78
    // 0xa4e730: SetupParameters(_SimpleGestureDetectorState this /* r1 => r0, fp-0x18 */)
    //     0xa4e730: mov             x0, x1
    //     0xa4e734: stur            x1, [fp, #-0x18]
    // 0xa4e738: CheckStackOverflow
    //     0xa4e738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4e73c: cmp             SP, x16
    //     0xa4e740: b.ls            #0xa4e838
    // 0xa4e744: LoadField: r1 = r0->field_b
    //     0xa4e744: ldur            w1, [x0, #0xb]
    // 0xa4e748: DecompressPointer r1
    //     0xa4e748: add             x1, x1, HEAP, lsl #32
    // 0xa4e74c: cmp             w1, NULL
    // 0xa4e750: b.eq            #0xa4e840
    // 0xa4e754: LoadField: r3 = r1->field_b
    //     0xa4e754: ldur            w3, [x1, #0xb]
    // 0xa4e758: DecompressPointer r3
    //     0xa4e758: add             x3, x3, HEAP, lsl #32
    // 0xa4e75c: stur            x3, [fp, #-0x10]
    // 0xa4e760: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa4e760: ldur            w4, [x1, #0x17]
    // 0xa4e764: DecompressPointer r4
    //     0xa4e764: add             x4, x4, HEAP, lsl #32
    // 0xa4e768: stur            x4, [fp, #-8]
    // 0xa4e76c: cmp             w4, NULL
    // 0xa4e770: b.eq            #0xa4e78c
    // 0xa4e774: mov             x2, x0
    // 0xa4e778: r1 = Function '_onVerticalDragStart@2703246225':.
    //     0xa4e778: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d2f0] AnonymousClosure: (0xa4ea58), in [package:simple_gesture_detector/simple_gesture_detector.dart] _SimpleGestureDetectorState::_onVerticalDragStart (0xa4ea94)
    //     0xa4e77c: ldr             x1, [x1, #0x2f0]
    // 0xa4e780: r0 = AllocateClosure()
    //     0xa4e780: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4e784: mov             x3, x0
    // 0xa4e788: b               #0xa4e790
    // 0xa4e78c: r3 = Null
    //     0xa4e78c: mov             x3, NULL
    // 0xa4e790: ldur            x0, [fp, #-8]
    // 0xa4e794: stur            x3, [fp, #-0x20]
    // 0xa4e798: cmp             w0, NULL
    // 0xa4e79c: b.eq            #0xa4e7b8
    // 0xa4e7a0: ldur            x2, [fp, #-0x18]
    // 0xa4e7a4: r1 = Function '_onVerticalDragUpdate@2703246225':.
    //     0xa4e7a4: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d2f8] AnonymousClosure: (0xa4e8d0), in [package:simple_gesture_detector/simple_gesture_detector.dart] _SimpleGestureDetectorState::_onVerticalDragUpdate (0xa4e90c)
    //     0xa4e7a8: ldr             x1, [x1, #0x2f8]
    // 0xa4e7ac: r0 = AllocateClosure()
    //     0xa4e7ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4e7b0: mov             x3, x0
    // 0xa4e7b4: b               #0xa4e7bc
    // 0xa4e7b8: r3 = Null
    //     0xa4e7b8: mov             x3, NULL
    // 0xa4e7bc: ldur            x0, [fp, #-8]
    // 0xa4e7c0: stur            x3, [fp, #-0x28]
    // 0xa4e7c4: cmp             w0, NULL
    // 0xa4e7c8: b.eq            #0xa4e7e0
    // 0xa4e7cc: ldur            x2, [fp, #-0x18]
    // 0xa4e7d0: r1 = Function '_onVerticalDragEnd@2703246225':.
    //     0xa4e7d0: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d300] AnonymousClosure: (0xa4e868), in [package:simple_gesture_detector/simple_gesture_detector.dart] _SimpleGestureDetectorState::_onVerticalDragEnd (0xa4e8a4)
    //     0xa4e7d4: ldr             x1, [x1, #0x300]
    // 0xa4e7d8: r0 = AllocateClosure()
    //     0xa4e7d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4e7dc: b               #0xa4e7e4
    // 0xa4e7e0: r0 = Null
    //     0xa4e7e0: mov             x0, NULL
    // 0xa4e7e4: stur            x0, [fp, #-8]
    // 0xa4e7e8: r0 = GestureDetector()
    //     0xa4e7e8: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa4e7ec: stur            x0, [fp, #-0x18]
    // 0xa4e7f0: r16 = Instance_HitTestBehavior
    //     0xa4e7f0: ldr             x16, [PP, #0x6c40]  ; [pp+0x6c40] Obj!HitTestBehavior@e358a1
    // 0xa4e7f4: ldur            lr, [fp, #-0x10]
    // 0xa4e7f8: stp             lr, x16, [SP, #0x40]
    // 0xa4e7fc: stp             NULL, NULL, [SP, #0x30]
    // 0xa4e800: ldur            x16, [fp, #-0x20]
    // 0xa4e804: ldur            lr, [fp, #-0x28]
    // 0xa4e808: stp             lr, x16, [SP, #0x20]
    // 0xa4e80c: ldur            x16, [fp, #-8]
    // 0xa4e810: stp             NULL, x16, [SP, #0x10]
    // 0xa4e814: stp             NULL, NULL, [SP]
    // 0xa4e818: mov             x1, x0
    // 0xa4e81c: r4 = const [0, 0xb, 0xa, 0x1, behavior, 0x1, child, 0x2, onHorizontalDragEnd, 0xa, onHorizontalDragStart, 0x8, onHorizontalDragUpdate, 0x9, onLongPress, 0x4, onTap, 0x3, onVerticalDragEnd, 0x7, onVerticalDragStart, 0x5, onVerticalDragUpdate, 0x6, null]
    //     0xa4e81c: add             x4, PP, #0x5d, lsl #12  ; [pp+0x5d308] List(25) [0, 0xb, 0xa, 0x1, "behavior", 0x1, "child", 0x2, "onHorizontalDragEnd", 0xa, "onHorizontalDragStart", 0x8, "onHorizontalDragUpdate", 0x9, "onLongPress", 0x4, "onTap", 0x3, "onVerticalDragEnd", 0x7, "onVerticalDragStart", 0x5, "onVerticalDragUpdate", 0x6, Null]
    //     0xa4e820: ldr             x4, [x4, #0x308]
    // 0xa4e824: r0 = GestureDetector()
    //     0xa4e824: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa4e828: ldur            x0, [fp, #-0x18]
    // 0xa4e82c: LeaveFrame
    //     0xa4e82c: mov             SP, fp
    //     0xa4e830: ldp             fp, lr, [SP], #0x10
    // 0xa4e834: ret
    //     0xa4e834: ret             
    // 0xa4e838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4e838: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4e83c: b               #0xa4e744
    // 0xa4e840: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4e840: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onVerticalDragEnd(dynamic, DragEndDetails) {
    // ** addr: 0xa4e868, size: 0x3c
    // 0xa4e868: EnterFrame
    //     0xa4e868: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e86c: mov             fp, SP
    // 0xa4e870: ldr             x0, [fp, #0x18]
    // 0xa4e874: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4e874: ldur            w1, [x0, #0x17]
    // 0xa4e878: DecompressPointer r1
    //     0xa4e878: add             x1, x1, HEAP, lsl #32
    // 0xa4e87c: CheckStackOverflow
    //     0xa4e87c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4e880: cmp             SP, x16
    //     0xa4e884: b.ls            #0xa4e89c
    // 0xa4e888: ldr             x2, [fp, #0x10]
    // 0xa4e88c: r0 = _onVerticalDragEnd()
    //     0xa4e88c: bl              #0xa4e8a4  ; [package:simple_gesture_detector/simple_gesture_detector.dart] _SimpleGestureDetectorState::_onVerticalDragEnd
    // 0xa4e890: LeaveFrame
    //     0xa4e890: mov             SP, fp
    //     0xa4e894: ldp             fp, lr, [SP], #0x10
    // 0xa4e898: ret
    //     0xa4e898: ret             
    // 0xa4e89c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4e89c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4e8a0: b               #0xa4e888
  }
  _ _onVerticalDragEnd(/* No info */) {
    // ** addr: 0xa4e8a4, size: 0x2c
    // 0xa4e8a4: LoadField: r2 = r1->field_b
    //     0xa4e8a4: ldur            w2, [x1, #0xb]
    // 0xa4e8a8: DecompressPointer r2
    //     0xa4e8a8: add             x2, x2, HEAP, lsl #32
    // 0xa4e8ac: cmp             w2, NULL
    // 0xa4e8b0: b.eq            #0xa4e8c4
    // 0xa4e8b4: StoreField: r1->field_13 = rNULL
    //     0xa4e8b4: stur            NULL, [x1, #0x13]
    // 0xa4e8b8: StoreField: r1->field_1b = rNULL
    //     0xa4e8b8: stur            NULL, [x1, #0x1b]
    // 0xa4e8bc: r0 = Null
    //     0xa4e8bc: mov             x0, NULL
    // 0xa4e8c0: ret
    //     0xa4e8c0: ret             
    // 0xa4e8c4: EnterFrame
    //     0xa4e8c4: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e8c8: mov             fp, SP
    // 0xa4e8cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4e8cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onVerticalDragUpdate(dynamic, DragUpdateDetails) {
    // ** addr: 0xa4e8d0, size: 0x3c
    // 0xa4e8d0: EnterFrame
    //     0xa4e8d0: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e8d4: mov             fp, SP
    // 0xa4e8d8: ldr             x0, [fp, #0x18]
    // 0xa4e8dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4e8dc: ldur            w1, [x0, #0x17]
    // 0xa4e8e0: DecompressPointer r1
    //     0xa4e8e0: add             x1, x1, HEAP, lsl #32
    // 0xa4e8e4: CheckStackOverflow
    //     0xa4e8e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4e8e8: cmp             SP, x16
    //     0xa4e8ec: b.ls            #0xa4e904
    // 0xa4e8f0: ldr             x2, [fp, #0x10]
    // 0xa4e8f4: r0 = _onVerticalDragUpdate()
    //     0xa4e8f4: bl              #0xa4e90c  ; [package:simple_gesture_detector/simple_gesture_detector.dart] _SimpleGestureDetectorState::_onVerticalDragUpdate
    // 0xa4e8f8: LeaveFrame
    //     0xa4e8f8: mov             SP, fp
    //     0xa4e8fc: ldp             fp, lr, [SP], #0x10
    // 0xa4e900: ret
    //     0xa4e900: ret             
    // 0xa4e904: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4e904: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4e908: b               #0xa4e8f0
  }
  _ _onVerticalDragUpdate(/* No info */) {
    // ** addr: 0xa4e90c, size: 0x14c
    // 0xa4e90c: EnterFrame
    //     0xa4e90c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4e910: mov             fp, SP
    // 0xa4e914: LoadField: r3 = r2->field_13
    //     0xa4e914: ldur            w3, [x2, #0x13]
    // 0xa4e918: DecompressPointer r3
    //     0xa4e918: add             x3, x3, HEAP, lsl #32
    // 0xa4e91c: mov             x0, x3
    // 0xa4e920: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4e920: stur            w0, [x1, #0x17]
    //     0xa4e924: ldurb           w16, [x1, #-1]
    //     0xa4e928: ldurb           w17, [x0, #-1]
    //     0xa4e92c: and             x16, x17, x16, lsr #2
    //     0xa4e930: tst             x16, HEAP, lsr #32
    //     0xa4e934: b.eq            #0xa4e93c
    //     0xa4e938: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa4e93c: LoadField: r2 = r1->field_b
    //     0xa4e93c: ldur            w2, [x1, #0xb]
    // 0xa4e940: DecompressPointer r2
    //     0xa4e940: add             x2, x2, HEAP, lsl #32
    // 0xa4e944: cmp             w2, NULL
    // 0xa4e948: b.eq            #0xa4ea4c
    // 0xa4e94c: LoadField: r4 = r1->field_13
    //     0xa4e94c: ldur            w4, [x1, #0x13]
    // 0xa4e950: DecompressPointer r4
    //     0xa4e950: add             x4, x4, HEAP, lsl #32
    // 0xa4e954: cmp             w4, NULL
    // 0xa4e958: b.eq            #0xa4ea3c
    // 0xa4e95c: d0 = 0.000000
    //     0xa4e95c: eor             v0.16b, v0.16b, v0.16b
    // 0xa4e960: LoadField: d1 = r4->field_f
    //     0xa4e960: ldur            d1, [x4, #0xf]
    // 0xa4e964: LoadField: d2 = r3->field_f
    //     0xa4e964: ldur            d2, [x3, #0xf]
    // 0xa4e968: fsub            d3, d1, d2
    // 0xa4e96c: fcmp            d3, d0
    // 0xa4e970: b.ne            #0xa4e97c
    // 0xa4e974: d1 = 0.000000
    //     0xa4e974: eor             v1.16b, v1.16b, v1.16b
    // 0xa4e978: b               #0xa4e990
    // 0xa4e97c: fcmp            d0, d3
    // 0xa4e980: b.le            #0xa4e98c
    // 0xa4e984: fneg            d1, d3
    // 0xa4e988: b               #0xa4e990
    // 0xa4e98c: mov             v1.16b, v3.16b
    // 0xa4e990: r4 = Instance_SimpleSwipeConfig
    //     0xa4e990: add             x4, PP, #0x35, lsl #12  ; [pp+0x35ed8] Obj!SimpleSwipeConfig@e0c0c1
    //     0xa4e994: ldr             x4, [x4, #0xed8]
    // 0xa4e998: LoadField: d2 = r4->field_7
    //     0xa4e998: ldur            d2, [x4, #7]
    // 0xa4e99c: fcmp            d1, d2
    // 0xa4e9a0: b.le            #0xa4ea3c
    // 0xa4e9a4: mov             x0, x3
    // 0xa4e9a8: StoreField: r1->field_13 = r0
    //     0xa4e9a8: stur            w0, [x1, #0x13]
    //     0xa4e9ac: ldurb           w16, [x1, #-1]
    //     0xa4e9b0: ldurb           w17, [x0, #-1]
    //     0xa4e9b4: and             x16, x17, x16, lsr #2
    //     0xa4e9b8: tst             x16, HEAP, lsr #32
    //     0xa4e9bc: b.eq            #0xa4e9c4
    //     0xa4e9c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa4e9c4: fcmp            d3, d0
    // 0xa4e9c8: b.le            #0xa4e9d8
    // 0xa4e9cc: r0 = Instance_SwipeDirection
    //     0xa4e9cc: add             x0, PP, #0x5d, lsl #12  ; [pp+0x5d310] Obj!SwipeDirection@e2e101
    //     0xa4e9d0: ldr             x0, [x0, #0x310]
    // 0xa4e9d4: b               #0xa4e9e0
    // 0xa4e9d8: r0 = Instance_SwipeDirection
    //     0xa4e9d8: add             x0, PP, #0x5d, lsl #12  ; [pp+0x5d318] Obj!SwipeDirection@e2e0e1
    //     0xa4e9dc: ldr             x0, [x0, #0x318]
    // 0xa4e9e0: LoadField: r3 = r1->field_1b
    //     0xa4e9e0: ldur            w3, [x1, #0x1b]
    // 0xa4e9e4: DecompressPointer r3
    //     0xa4e9e4: add             x3, x3, HEAP, lsl #32
    // 0xa4e9e8: cmp             w3, NULL
    // 0xa4e9ec: b.eq            #0xa4e9f8
    // 0xa4e9f0: cmp             w0, w3
    // 0xa4e9f4: b.eq            #0xa4ea3c
    // 0xa4e9f8: StoreField: r1->field_1b = r0
    //     0xa4e9f8: stur            w0, [x1, #0x1b]
    //     0xa4e9fc: ldurb           w16, [x1, #-1]
    //     0xa4ea00: ldurb           w17, [x0, #-1]
    //     0xa4ea04: and             x16, x17, x16, lsr #2
    //     0xa4ea08: tst             x16, HEAP, lsr #32
    //     0xa4ea0c: b.eq            #0xa4ea14
    //     0xa4ea10: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa4ea14: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa4ea14: ldur            w1, [x2, #0x17]
    // 0xa4ea18: DecompressPointer r1
    //     0xa4ea18: add             x1, x1, HEAP, lsl #32
    // 0xa4ea1c: cmp             w1, NULL
    // 0xa4ea20: b.eq            #0xa4ea50
    // 0xa4ea24: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa4ea24: ldur            w2, [x1, #0x17]
    // 0xa4ea28: DecompressPointer r2
    //     0xa4ea28: add             x2, x2, HEAP, lsl #32
    // 0xa4ea2c: LoadField: r1 = r2->field_b
    //     0xa4ea2c: ldur            w1, [x2, #0xb]
    // 0xa4ea30: DecompressPointer r1
    //     0xa4ea30: add             x1, x1, HEAP, lsl #32
    // 0xa4ea34: cmp             w1, NULL
    // 0xa4ea38: b.eq            #0xa4ea54
    // 0xa4ea3c: r0 = Null
    //     0xa4ea3c: mov             x0, NULL
    // 0xa4ea40: LeaveFrame
    //     0xa4ea40: mov             SP, fp
    //     0xa4ea44: ldp             fp, lr, [SP], #0x10
    // 0xa4ea48: ret
    //     0xa4ea48: ret             
    // 0xa4ea4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ea4c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ea50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ea50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ea54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ea54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onVerticalDragStart(dynamic, DragStartDetails) {
    // ** addr: 0xa4ea58, size: 0x3c
    // 0xa4ea58: EnterFrame
    //     0xa4ea58: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ea5c: mov             fp, SP
    // 0xa4ea60: ldr             x0, [fp, #0x18]
    // 0xa4ea64: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4ea64: ldur            w1, [x0, #0x17]
    // 0xa4ea68: DecompressPointer r1
    //     0xa4ea68: add             x1, x1, HEAP, lsl #32
    // 0xa4ea6c: CheckStackOverflow
    //     0xa4ea6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4ea70: cmp             SP, x16
    //     0xa4ea74: b.ls            #0xa4ea8c
    // 0xa4ea78: ldr             x2, [fp, #0x10]
    // 0xa4ea7c: r0 = _onVerticalDragStart()
    //     0xa4ea7c: bl              #0xa4ea94  ; [package:simple_gesture_detector/simple_gesture_detector.dart] _SimpleGestureDetectorState::_onVerticalDragStart
    // 0xa4ea80: LeaveFrame
    //     0xa4ea80: mov             SP, fp
    //     0xa4ea84: ldp             fp, lr, [SP], #0x10
    // 0xa4ea88: ret
    //     0xa4ea88: ret             
    // 0xa4ea8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ea8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ea90: b               #0xa4ea78
  }
  _ _onVerticalDragStart(/* No info */) {
    // ** addr: 0xa4ea94, size: 0x34
    // 0xa4ea94: LoadField: r0 = r2->field_b
    //     0xa4ea94: ldur            w0, [x2, #0xb]
    // 0xa4ea98: DecompressPointer r0
    //     0xa4ea98: add             x0, x0, HEAP, lsl #32
    // 0xa4ea9c: StoreField: r1->field_13 = r0
    //     0xa4ea9c: stur            w0, [x1, #0x13]
    //     0xa4eaa0: ldurb           w16, [x1, #-1]
    //     0xa4eaa4: ldurb           w17, [x0, #-1]
    //     0xa4eaa8: and             x16, x17, x16, lsr #2
    //     0xa4eaac: tst             x16, HEAP, lsr #32
    //     0xa4eab0: b.eq            #0xa4eac0
    //     0xa4eab4: str             lr, [SP, #-8]!
    //     0xa4eab8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    //     0xa4eabc: ldr             lr, [SP], #8
    // 0xa4eac0: r0 = Null
    //     0xa4eac0: mov             x0, NULL
    // 0xa4eac4: ret
    //     0xa4eac4: ret             
  }
}

// class id: 4688, size: 0x2c, field offset: 0xc
//   const constructor, 
class SimpleGestureDetector extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa954e8, size: 0x24
    // 0xa954e8: EnterFrame
    //     0xa954e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa954ec: mov             fp, SP
    // 0xa954f0: mov             x0, x1
    // 0xa954f4: r1 = <SimpleGestureDetector>
    //     0xa954f4: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ae58] TypeArguments: <SimpleGestureDetector>
    //     0xa954f8: ldr             x1, [x1, #0xe58]
    // 0xa954fc: r0 = _SimpleGestureDetectorState()
    //     0xa954fc: bl              #0xa9550c  ; Allocate_SimpleGestureDetectorStateStub -> _SimpleGestureDetectorState (size=0x20)
    // 0xa95500: LeaveFrame
    //     0xa95500: mov             SP, fp
    //     0xa95504: ldp             fp, lr, [SP], #0x10
    // 0xa95508: ret
    //     0xa95508: ret             
  }
}

// class id: 6771, size: 0x14, field offset: 0x14
enum SwipeDetectionBehavior extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e9b4, size: 0x64
    // 0xc4e9b4: EnterFrame
    //     0xc4e9b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e9b8: mov             fp, SP
    // 0xc4e9bc: AllocStack(0x10)
    //     0xc4e9bc: sub             SP, SP, #0x10
    // 0xc4e9c0: SetupParameters(SwipeDetectionBehavior this /* r1 => r0, fp-0x8 */)
    //     0xc4e9c0: mov             x0, x1
    //     0xc4e9c4: stur            x1, [fp, #-8]
    // 0xc4e9c8: CheckStackOverflow
    //     0xc4e9c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e9cc: cmp             SP, x16
    //     0xc4e9d0: b.ls            #0xc4ea10
    // 0xc4e9d4: r1 = Null
    //     0xc4e9d4: mov             x1, NULL
    // 0xc4e9d8: r2 = 4
    //     0xc4e9d8: movz            x2, #0x4
    // 0xc4e9dc: r0 = AllocateArray()
    //     0xc4e9dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e9e0: r16 = "SwipeDetectionBehavior."
    //     0xc4e9e0: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c010] "SwipeDetectionBehavior."
    //     0xc4e9e4: ldr             x16, [x16, #0x10]
    // 0xc4e9e8: StoreField: r0->field_f = r16
    //     0xc4e9e8: stur            w16, [x0, #0xf]
    // 0xc4e9ec: ldur            x1, [fp, #-8]
    // 0xc4e9f0: LoadField: r2 = r1->field_f
    //     0xc4e9f0: ldur            w2, [x1, #0xf]
    // 0xc4e9f4: DecompressPointer r2
    //     0xc4e9f4: add             x2, x2, HEAP, lsl #32
    // 0xc4e9f8: StoreField: r0->field_13 = r2
    //     0xc4e9f8: stur            w2, [x0, #0x13]
    // 0xc4e9fc: str             x0, [SP]
    // 0xc4ea00: r0 = _interpolate()
    //     0xc4ea00: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ea04: LeaveFrame
    //     0xc4ea04: mov             SP, fp
    //     0xc4ea08: ldp             fp, lr, [SP], #0x10
    // 0xc4ea0c: ret
    //     0xc4ea0c: ret             
    // 0xc4ea10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ea10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ea14: b               #0xc4e9d4
  }
}

// class id: 6772, size: 0x14, field offset: 0x14
enum SwipeDirection extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4e950, size: 0x64
    // 0xc4e950: EnterFrame
    //     0xc4e950: stp             fp, lr, [SP, #-0x10]!
    //     0xc4e954: mov             fp, SP
    // 0xc4e958: AllocStack(0x10)
    //     0xc4e958: sub             SP, SP, #0x10
    // 0xc4e95c: SetupParameters(SwipeDirection this /* r1 => r0, fp-0x8 */)
    //     0xc4e95c: mov             x0, x1
    //     0xc4e960: stur            x1, [fp, #-8]
    // 0xc4e964: CheckStackOverflow
    //     0xc4e964: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4e968: cmp             SP, x16
    //     0xc4e96c: b.ls            #0xc4e9ac
    // 0xc4e970: r1 = Null
    //     0xc4e970: mov             x1, NULL
    // 0xc4e974: r2 = 4
    //     0xc4e974: movz            x2, #0x4
    // 0xc4e978: r0 = AllocateArray()
    //     0xc4e978: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4e97c: r16 = "SwipeDirection."
    //     0xc4e97c: add             x16, PP, #0x5d, lsl #12  ; [pp+0x5ddc8] "SwipeDirection."
    //     0xc4e980: ldr             x16, [x16, #0xdc8]
    // 0xc4e984: StoreField: r0->field_f = r16
    //     0xc4e984: stur            w16, [x0, #0xf]
    // 0xc4e988: ldur            x1, [fp, #-8]
    // 0xc4e98c: LoadField: r2 = r1->field_f
    //     0xc4e98c: ldur            w2, [x1, #0xf]
    // 0xc4e990: DecompressPointer r2
    //     0xc4e990: add             x2, x2, HEAP, lsl #32
    // 0xc4e994: StoreField: r0->field_13 = r2
    //     0xc4e994: stur            w2, [x0, #0x13]
    // 0xc4e998: str             x0, [SP]
    // 0xc4e99c: r0 = _interpolate()
    //     0xc4e99c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4e9a0: LeaveFrame
    //     0xc4e9a0: mov             SP, fp
    //     0xc4e9a4: ldp             fp, lr, [SP], #0x10
    // 0xc4e9a8: ret
    //     0xc4e9a8: ret             
    // 0xc4e9ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4e9ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4e9b0: b               #0xc4e970
  }
}
