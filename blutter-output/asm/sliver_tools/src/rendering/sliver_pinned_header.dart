// lib: , url: package:sliver_tools/src/rendering/sliver_pinned_header.dart

// class id: 1051131, size: 0x8
class :: {
}

// class id: 2979, size: 0x58, field offset: 0x58
class RenderSliverPinnedHeader extends RenderSliverSingleBoxAdapter {

  _ performLayout(/* No info */) {
    // ** addr: 0x77d1e8, size: 0x3a0
    // 0x77d1e8: EnterFrame
    //     0x77d1e8: stp             fp, lr, [SP, #-0x10]!
    //     0x77d1ec: mov             fp, SP
    // 0x77d1f0: AllocStack(0x40)
    //     0x77d1f0: sub             SP, SP, #0x40
    // 0x77d1f4: SetupParameters(RenderSliverPinnedHeader this /* r1 => r3, fp-0x18 */)
    //     0x77d1f4: mov             x3, x1
    //     0x77d1f8: stur            x1, [fp, #-0x18]
    // 0x77d1fc: CheckStackOverflow
    //     0x77d1fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77d200: cmp             SP, x16
    //     0x77d204: b.ls            #0x77d574
    // 0x77d208: LoadField: r4 = r3->field_53
    //     0x77d208: ldur            w4, [x3, #0x53]
    // 0x77d20c: DecompressPointer r4
    //     0x77d20c: add             x4, x4, HEAP, lsl #32
    // 0x77d210: stur            x4, [fp, #-0x10]
    // 0x77d214: cmp             w4, NULL
    // 0x77d218: b.eq            #0x77d57c
    // 0x77d21c: LoadField: r5 = r3->field_27
    //     0x77d21c: ldur            w5, [x3, #0x27]
    // 0x77d220: DecompressPointer r5
    //     0x77d220: add             x5, x5, HEAP, lsl #32
    // 0x77d224: stur            x5, [fp, #-8]
    // 0x77d228: cmp             w5, NULL
    // 0x77d22c: b.eq            #0x77d518
    // 0x77d230: mov             x0, x5
    // 0x77d234: r2 = Null
    //     0x77d234: mov             x2, NULL
    // 0x77d238: r1 = Null
    //     0x77d238: mov             x1, NULL
    // 0x77d23c: r4 = LoadClassIdInstr(r0)
    //     0x77d23c: ldur            x4, [x0, #-1]
    //     0x77d240: ubfx            x4, x4, #0xc, #0x14
    // 0x77d244: cmp             x4, #0xc82
    // 0x77d248: b.eq            #0x77d260
    // 0x77d24c: r8 = SliverConstraints
    //     0x77d24c: add             x8, PP, #0x3b, lsl #12  ; [pp+0x3bfb0] Type: SliverConstraints
    //     0x77d250: ldr             x8, [x8, #0xfb0]
    // 0x77d254: r3 = Null
    //     0x77d254: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bfb8] Null
    //     0x77d258: ldr             x3, [x3, #0xfb8]
    // 0x77d25c: r0 = DefaultTypeTest()
    //     0x77d25c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x77d260: ldur            x1, [fp, #-8]
    // 0x77d264: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x77d264: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x77d268: r0 = asBoxConstraints()
    //     0x77d268: bl              #0x77c97c  ; [package:flutter/src/rendering/sliver.dart] SliverConstraints::asBoxConstraints
    // 0x77d26c: ldur            x1, [fp, #-0x10]
    // 0x77d270: r2 = LoadClassIdInstr(r1)
    //     0x77d270: ldur            x2, [x1, #-1]
    //     0x77d274: ubfx            x2, x2, #0xc, #0x14
    // 0x77d278: r16 = true
    //     0x77d278: add             x16, NULL, #0x20  ; true
    // 0x77d27c: str             x16, [SP]
    // 0x77d280: mov             x16, x0
    // 0x77d284: mov             x0, x2
    // 0x77d288: mov             x2, x16
    // 0x77d28c: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x77d28c: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x77d290: ldr             x4, [x4, #0x5c0]
    // 0x77d294: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x77d294: movz            x17, #0xed1d
    //     0x77d298: add             lr, x0, x17
    //     0x77d29c: ldr             lr, [x21, lr, lsl #3]
    //     0x77d2a0: blr             lr
    // 0x77d2a4: ldur            x3, [fp, #-0x18]
    // 0x77d2a8: LoadField: r4 = r3->field_27
    //     0x77d2a8: ldur            w4, [x3, #0x27]
    // 0x77d2ac: DecompressPointer r4
    //     0x77d2ac: add             x4, x4, HEAP, lsl #32
    // 0x77d2b0: stur            x4, [fp, #-8]
    // 0x77d2b4: cmp             w4, NULL
    // 0x77d2b8: b.eq            #0x77d534
    // 0x77d2bc: mov             x0, x4
    // 0x77d2c0: r2 = Null
    //     0x77d2c0: mov             x2, NULL
    // 0x77d2c4: r1 = Null
    //     0x77d2c4: mov             x1, NULL
    // 0x77d2c8: r4 = LoadClassIdInstr(r0)
    //     0x77d2c8: ldur            x4, [x0, #-1]
    //     0x77d2cc: ubfx            x4, x4, #0xc, #0x14
    // 0x77d2d0: cmp             x4, #0xc82
    // 0x77d2d4: b.eq            #0x77d2ec
    // 0x77d2d8: r8 = SliverConstraints
    //     0x77d2d8: add             x8, PP, #0x3b, lsl #12  ; [pp+0x3bfb0] Type: SliverConstraints
    //     0x77d2dc: ldr             x8, [x8, #0xfb0]
    // 0x77d2e0: r3 = Null
    //     0x77d2e0: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bfc8] Null
    //     0x77d2e4: ldr             x3, [x3, #0xfc8]
    // 0x77d2e8: r0 = DefaultTypeTest()
    //     0x77d2e8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x77d2ec: ldur            x1, [fp, #-8]
    // 0x77d2f0: r0 = axis()
    //     0x77d2f0: bl              #0x77b9d4  ; [package:flutter/src/rendering/sliver.dart] SliverConstraints::axis
    // 0x77d2f4: LoadField: r1 = r0->field_7
    //     0x77d2f4: ldur            x1, [x0, #7]
    // 0x77d2f8: cmp             x1, #0
    // 0x77d2fc: b.gt            #0x77d320
    // 0x77d300: ldur            x0, [fp, #-0x18]
    // 0x77d304: LoadField: r1 = r0->field_53
    //     0x77d304: ldur            w1, [x0, #0x53]
    // 0x77d308: DecompressPointer r1
    //     0x77d308: add             x1, x1, HEAP, lsl #32
    // 0x77d30c: cmp             w1, NULL
    // 0x77d310: b.eq            #0x77d580
    // 0x77d314: r0 = size()
    //     0x77d314: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x77d318: LoadField: d0 = r0->field_7
    //     0x77d318: ldur            d0, [x0, #7]
    // 0x77d31c: b               #0x77d33c
    // 0x77d320: ldur            x0, [fp, #-0x18]
    // 0x77d324: LoadField: r1 = r0->field_53
    //     0x77d324: ldur            w1, [x0, #0x53]
    // 0x77d328: DecompressPointer r1
    //     0x77d328: add             x1, x1, HEAP, lsl #32
    // 0x77d32c: cmp             w1, NULL
    // 0x77d330: b.eq            #0x77d584
    // 0x77d334: r0 = size()
    //     0x77d334: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x77d338: LoadField: d0 = r0->field_f
    //     0x77d338: ldur            d0, [x0, #0xf]
    // 0x77d33c: ldur            x0, [fp, #-0x18]
    // 0x77d340: mov             x1, x0
    // 0x77d344: stur            d0, [fp, #-0x20]
    // 0x77d348: r0 = constraints()
    //     0x77d348: bl              #0x7fa688  ; [package:flutter/src/rendering/sliver.dart] RenderSliver::constraints
    // 0x77d34c: LoadField: d0 = r0->field_2b
    //     0x77d34c: ldur            d0, [x0, #0x2b]
    // 0x77d350: ldur            x3, [fp, #-0x18]
    // 0x77d354: stur            d0, [fp, #-0x28]
    // 0x77d358: LoadField: r4 = r3->field_27
    //     0x77d358: ldur            w4, [x3, #0x27]
    // 0x77d35c: DecompressPointer r4
    //     0x77d35c: add             x4, x4, HEAP, lsl #32
    // 0x77d360: stur            x4, [fp, #-8]
    // 0x77d364: cmp             w4, NULL
    // 0x77d368: b.eq            #0x77d554
    // 0x77d36c: ldur            d1, [fp, #-0x20]
    // 0x77d370: mov             x0, x4
    // 0x77d374: r2 = Null
    //     0x77d374: mov             x2, NULL
    // 0x77d378: r1 = Null
    //     0x77d378: mov             x1, NULL
    // 0x77d37c: r4 = LoadClassIdInstr(r0)
    //     0x77d37c: ldur            x4, [x0, #-1]
    //     0x77d380: ubfx            x4, x4, #0xc, #0x14
    // 0x77d384: cmp             x4, #0xc82
    // 0x77d388: b.eq            #0x77d3a0
    // 0x77d38c: r8 = SliverConstraints
    //     0x77d38c: add             x8, PP, #0x3b, lsl #12  ; [pp+0x3bfb0] Type: SliverConstraints
    //     0x77d390: ldr             x8, [x8, #0xfb0]
    // 0x77d394: r3 = Null
    //     0x77d394: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bfd8] Null
    //     0x77d398: ldr             x3, [x3, #0xfd8]
    // 0x77d39c: r0 = DefaultTypeTest()
    //     0x77d39c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x77d3a0: ldur            x0, [fp, #-8]
    // 0x77d3a4: LoadField: d0 = r0->field_23
    //     0x77d3a4: ldur            d0, [x0, #0x23]
    // 0x77d3a8: ldur            d1, [fp, #-0x28]
    // 0x77d3ac: stur            d0, [fp, #-0x38]
    // 0x77d3b0: fsub            d2, d1, d0
    // 0x77d3b4: ldur            d1, [fp, #-0x20]
    // 0x77d3b8: fcmp            d1, d2
    // 0x77d3bc: b.le            #0x77d3c8
    // 0x77d3c0: d3 = 0.000000
    //     0x77d3c0: eor             v3.16b, v3.16b, v3.16b
    // 0x77d3c4: b               #0x77d428
    // 0x77d3c8: fcmp            d2, d1
    // 0x77d3cc: b.le            #0x77d3dc
    // 0x77d3d0: mov             v2.16b, v1.16b
    // 0x77d3d4: d3 = 0.000000
    //     0x77d3d4: eor             v3.16b, v3.16b, v3.16b
    // 0x77d3d8: b               #0x77d428
    // 0x77d3dc: d3 = 0.000000
    //     0x77d3dc: eor             v3.16b, v3.16b, v3.16b
    // 0x77d3e0: fcmp            d1, d3
    // 0x77d3e4: b.ne            #0x77d3fc
    // 0x77d3e8: fadd            d4, d1, d2
    // 0x77d3ec: fmul            d5, d4, d1
    // 0x77d3f0: fmul            d4, d5, d2
    // 0x77d3f4: mov             v2.16b, v4.16b
    // 0x77d3f8: b               #0x77d428
    // 0x77d3fc: fcmp            d1, d3
    // 0x77d400: b.ne            #0x77d41c
    // 0x77d404: fcmp            d2, #0.0
    // 0x77d408: b.vs            #0x77d41c
    // 0x77d40c: b.ne            #0x77d418
    // 0x77d410: r1 = 0.000000
    //     0x77d410: fmov            x1, d2
    // 0x77d414: cmp             x1, #0
    // 0x77d418: b.lt            #0x77d428
    // 0x77d41c: fcmp            d2, d2
    // 0x77d420: b.vs            #0x77d428
    // 0x77d424: mov             v2.16b, v1.16b
    // 0x77d428: stur            d2, [fp, #-0x30]
    // 0x77d42c: LoadField: d4 = r0->field_13
    //     0x77d42c: ldur            d4, [x0, #0x13]
    // 0x77d430: fsub            d5, d2, d4
    // 0x77d434: fcmp            d3, d5
    // 0x77d438: b.le            #0x77d444
    // 0x77d43c: d4 = 0.000000
    //     0x77d43c: eor             v4.16b, v4.16b, v4.16b
    // 0x77d440: b               #0x77d478
    // 0x77d444: fcmp            d5, d3
    // 0x77d448: b.le            #0x77d454
    // 0x77d44c: mov             v4.16b, v5.16b
    // 0x77d450: b               #0x77d478
    // 0x77d454: fcmp            d3, d3
    // 0x77d458: b.ne            #0x77d464
    // 0x77d45c: fadd            d4, d5, d3
    // 0x77d460: b               #0x77d478
    // 0x77d464: fcmp            d5, d5
    // 0x77d468: b.vc            #0x77d474
    // 0x77d46c: mov             v4.16b, v5.16b
    // 0x77d470: b               #0x77d478
    // 0x77d474: d4 = 0.000000
    //     0x77d474: eor             v4.16b, v4.16b, v4.16b
    // 0x77d478: ldur            x0, [fp, #-0x18]
    // 0x77d47c: stur            d4, [fp, #-0x28]
    // 0x77d480: fcmp            d1, d2
    // 0x77d484: r16 = true
    //     0x77d484: add             x16, NULL, #0x20  ; true
    // 0x77d488: r17 = false
    //     0x77d488: add             x17, NULL, #0x30  ; false
    // 0x77d48c: csel            x1, x16, x17, gt
    // 0x77d490: stur            x1, [fp, #-8]
    // 0x77d494: r0 = SliverGeometry()
    //     0x77d494: bl              #0x77bb44  ; AllocateSliverGeometryStub -> SliverGeometry (size=0x58)
    // 0x77d498: ldur            d0, [fp, #-0x20]
    // 0x77d49c: StoreField: r0->field_7 = d0
    //     0x77d49c: stur            d0, [x0, #7]
    // 0x77d4a0: ldur            d1, [fp, #-0x30]
    // 0x77d4a4: ArrayStore: r0[0] = d1  ; List_8
    //     0x77d4a4: stur            d1, [x0, #0x17]
    // 0x77d4a8: ldur            d2, [fp, #-0x38]
    // 0x77d4ac: StoreField: r0->field_f = d2
    //     0x77d4ac: stur            d2, [x0, #0xf]
    // 0x77d4b0: StoreField: r0->field_27 = d0
    //     0x77d4b0: stur            d0, [x0, #0x27]
    // 0x77d4b4: StoreField: r0->field_2f = d0
    //     0x77d4b4: stur            d0, [x0, #0x2f]
    // 0x77d4b8: ldur            x1, [fp, #-8]
    // 0x77d4bc: StoreField: r0->field_43 = r1
    //     0x77d4bc: stur            w1, [x0, #0x43]
    // 0x77d4c0: ldur            d0, [fp, #-0x28]
    // 0x77d4c4: StoreField: r0->field_1f = d0
    //     0x77d4c4: stur            d0, [x0, #0x1f]
    // 0x77d4c8: StoreField: r0->field_37 = d1
    //     0x77d4c8: stur            d1, [x0, #0x37]
    // 0x77d4cc: StoreField: r0->field_4b = d0
    //     0x77d4cc: stur            d0, [x0, #0x4b]
    // 0x77d4d0: d0 = 0.000000
    //     0x77d4d0: eor             v0.16b, v0.16b, v0.16b
    // 0x77d4d4: fcmp            d1, d0
    // 0x77d4d8: r16 = true
    //     0x77d4d8: add             x16, NULL, #0x20  ; true
    // 0x77d4dc: r17 = false
    //     0x77d4dc: add             x17, NULL, #0x30  ; false
    // 0x77d4e0: csel            x1, x16, x17, gt
    // 0x77d4e4: StoreField: r0->field_3f = r1
    //     0x77d4e4: stur            w1, [x0, #0x3f]
    // 0x77d4e8: ldur            x1, [fp, #-0x18]
    // 0x77d4ec: StoreField: r1->field_4f = r0
    //     0x77d4ec: stur            w0, [x1, #0x4f]
    //     0x77d4f0: ldurb           w16, [x1, #-1]
    //     0x77d4f4: ldurb           w17, [x0, #-1]
    //     0x77d4f8: and             x16, x17, x16, lsr #2
    //     0x77d4fc: tst             x16, HEAP, lsr #32
    //     0x77d500: b.eq            #0x77d508
    //     0x77d504: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x77d508: r0 = Null
    //     0x77d508: mov             x0, NULL
    // 0x77d50c: LeaveFrame
    //     0x77d50c: mov             SP, fp
    //     0x77d510: ldp             fp, lr, [SP], #0x10
    // 0x77d514: ret
    //     0x77d514: ret             
    // 0x77d518: r0 = StateError()
    //     0x77d518: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x77d51c: mov             x1, x0
    // 0x77d520: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x77d520: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x77d524: StoreField: r1->field_b = r0
    //     0x77d524: stur            w0, [x1, #0xb]
    // 0x77d528: mov             x0, x1
    // 0x77d52c: r0 = Throw()
    //     0x77d52c: bl              #0xec04b8  ; ThrowStub
    // 0x77d530: brk             #0
    // 0x77d534: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x77d534: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x77d538: r0 = StateError()
    //     0x77d538: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x77d53c: mov             x1, x0
    // 0x77d540: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x77d540: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x77d544: StoreField: r1->field_b = r0
    //     0x77d544: stur            w0, [x1, #0xb]
    // 0x77d548: mov             x0, x1
    // 0x77d54c: r0 = Throw()
    //     0x77d54c: bl              #0xec04b8  ; ThrowStub
    // 0x77d550: brk             #0
    // 0x77d554: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x77d554: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x77d558: r0 = StateError()
    //     0x77d558: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x77d55c: mov             x1, x0
    // 0x77d560: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x77d560: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x77d564: StoreField: r1->field_b = r0
    //     0x77d564: stur            w0, [x1, #0xb]
    // 0x77d568: mov             x0, x1
    // 0x77d56c: r0 = Throw()
    //     0x77d56c: bl              #0xec04b8  ; ThrowStub
    // 0x77d570: brk             #0
    // 0x77d574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77d574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77d578: b               #0x77d208
    // 0x77d57c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x77d57c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x77d580: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x77d580: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x77d584: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x77d584: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
