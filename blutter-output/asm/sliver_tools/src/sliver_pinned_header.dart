// lib: , url: package:sliver_tools/src/sliver_pinned_header.dart

// class id: 1051134, size: 0x8
class :: {
}

// class id: 4482, size: 0x10, field offset: 0x10
//   const constructor, 
class SliverPinnedHeader extends SingleChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x860814, size: 0x4c
    // 0x860814: EnterFrame
    //     0x860814: stp             fp, lr, [SP, #-0x10]!
    //     0x860818: mov             fp, SP
    // 0x86081c: AllocStack(0x8)
    //     0x86081c: sub             SP, SP, #8
    // 0x860820: CheckStackOverflow
    //     0x860820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x860824: cmp             SP, x16
    //     0x860828: b.ls            #0x860858
    // 0x86082c: r0 = RenderSliverPinnedHeader()
    //     0x86082c: bl              #0x860860  ; AllocateRenderSliverPinnedHeaderStub -> RenderSliverPinnedHeader (size=0x58)
    // 0x860830: mov             x1, x0
    // 0x860834: stur            x0, [fp, #-8]
    // 0x860838: r0 = RenderObject()
    //     0x860838: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x86083c: ldur            x1, [fp, #-8]
    // 0x860840: r2 = Null
    //     0x860840: mov             x2, NULL
    // 0x860844: r0 = child=()
    //     0x860844: bl              #0x8990c4  ; [package:flutter/src/rendering/sliver.dart] _RenderSliverSingleBoxAdapter&RenderSliver&RenderObjectWithChildMixin::child=
    // 0x860848: ldur            x0, [fp, #-8]
    // 0x86084c: LeaveFrame
    //     0x86084c: mov             SP, fp
    //     0x860850: ldp             fp, lr, [SP], #0x10
    // 0x860854: ret
    //     0x860854: ret             
    // 0x860858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x860858: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86085c: b               #0x86082c
  }
}
