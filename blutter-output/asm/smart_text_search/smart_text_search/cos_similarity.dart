// lib: , url: package:smart_text_search/smart_text_search/cos_similarity.dart

// class id: 1051136, size: 0x8
class :: {

  static _ textCosineSimilarity(/* No info */) {
    // ** addr: 0xb40014, size: 0x1c8
    // 0xb40014: EnterFrame
    //     0xb40014: stp             fp, lr, [SP, #-0x10]!
    //     0xb40018: mov             fp, SP
    // 0xb4001c: AllocStack(0x40)
    //     0xb4001c: sub             SP, SP, #0x40
    // 0xb40020: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xb40020: mov             x3, x2
    //     0xb40024: stur            x2, [fp, #-8]
    // 0xb40028: CheckStackOverflow
    //     0xb40028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4002c: cmp             SP, x16
    //     0xb40030: b.ls            #0xb401d4
    // 0xb40034: r0 = LoadClassIdInstr(r1)
    //     0xb40034: ldur            x0, [x1, #-1]
    //     0xb40038: ubfx            x0, x0, #0xc, #0x14
    // 0xb4003c: r2 = " "
    //     0xb4003c: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb40040: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb40040: sub             lr, x0, #1, lsl #12
    //     0xb40044: ldr             lr, [x21, lr, lsl #3]
    //     0xb40048: blr             lr
    // 0xb4004c: mov             x1, x0
    // 0xb40050: r0 = toSet()
    //     0xb40050: bl              #0x863cac  ; [dart:core] _GrowableList::toSet
    // 0xb40054: mov             x3, x0
    // 0xb40058: ldur            x1, [fp, #-8]
    // 0xb4005c: stur            x3, [fp, #-0x10]
    // 0xb40060: r0 = LoadClassIdInstr(r1)
    //     0xb40060: ldur            x0, [x1, #-1]
    //     0xb40064: ubfx            x0, x0, #0xc, #0x14
    // 0xb40068: r2 = " "
    //     0xb40068: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb4006c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb4006c: sub             lr, x0, #1, lsl #12
    //     0xb40070: ldr             lr, [x21, lr, lsl #3]
    //     0xb40074: blr             lr
    // 0xb40078: mov             x1, x0
    // 0xb4007c: r0 = toSet()
    //     0xb4007c: bl              #0x863cac  ; [dart:core] _GrowableList::toSet
    // 0xb40080: mov             x3, x0
    // 0xb40084: ldur            x0, [fp, #-0x10]
    // 0xb40088: stur            x3, [fp, #-8]
    // 0xb4008c: LoadField: r1 = r0->field_7
    //     0xb4008c: ldur            w1, [x0, #7]
    // 0xb40090: DecompressPointer r1
    //     0xb40090: add             x1, x1, HEAP, lsl #32
    // 0xb40094: mov             x2, x0
    // 0xb40098: r0 = _GrowableList._ofEfficientLengthIterable()
    //     0xb40098: bl              #0x60b858  ; [dart:core] _GrowableList::_GrowableList._ofEfficientLengthIterable
    // 0xb4009c: stur            x0, [fp, #-0x18]
    // 0xb400a0: r1 = 3
    //     0xb400a0: movz            x1, #0x3
    // 0xb400a4: r0 = AllocateContext()
    //     0xb400a4: bl              #0xec126c  ; AllocateContextStub
    // 0xb400a8: mov             x3, x0
    // 0xb400ac: ldur            x0, [fp, #-0x18]
    // 0xb400b0: stur            x3, [fp, #-0x20]
    // 0xb400b4: StoreField: r3->field_f = r0
    //     0xb400b4: stur            w0, [x3, #0xf]
    // 0xb400b8: ldur            x4, [fp, #-8]
    // 0xb400bc: LoadField: r1 = r4->field_7
    //     0xb400bc: ldur            w1, [x4, #7]
    // 0xb400c0: DecompressPointer r1
    //     0xb400c0: add             x1, x1, HEAP, lsl #32
    // 0xb400c4: mov             x2, x4
    // 0xb400c8: r0 = _GrowableList._ofEfficientLengthIterable()
    //     0xb400c8: bl              #0x60b858  ; [dart:core] _GrowableList::_GrowableList._ofEfficientLengthIterable
    // 0xb400cc: mov             x4, x0
    // 0xb400d0: ldur            x3, [fp, #-0x20]
    // 0xb400d4: stur            x4, [fp, #-0x28]
    // 0xb400d8: StoreField: r3->field_13 = r0
    //     0xb400d8: stur            w0, [x3, #0x13]
    //     0xb400dc: ldurb           w16, [x3, #-1]
    //     0xb400e0: ldurb           w17, [x0, #-1]
    //     0xb400e4: and             x16, x17, x16, lsr #2
    //     0xb400e8: tst             x16, HEAP, lsr #32
    //     0xb400ec: b.eq            #0xb400f4
    //     0xb400f0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xb400f4: ldur            x2, [fp, #-0x10]
    // 0xb400f8: r1 = <String>
    //     0xb400f8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb400fc: r0 = LinkedHashSet.of()
    //     0xb400fc: bl              #0x64d534  ; [dart:collection] LinkedHashSet::LinkedHashSet.of
    // 0xb40100: mov             x1, x0
    // 0xb40104: ldur            x2, [fp, #-8]
    // 0xb40108: stur            x0, [fp, #-0x30]
    // 0xb4010c: r0 = addAll()
    //     0xb4010c: bl              #0xc1ec5c  ; [dart:_compact_hash] _Set::addAll
    // 0xb40110: ldur            x1, [fp, #-0x18]
    // 0xb40114: ldur            x2, [fp, #-0x28]
    // 0xb40118: r0 = _getWordsSimilarityMatrix()
    //     0xb40118: bl              #0xb40898  ; [package:smart_text_search/smart_text_search/cos_similarity.dart] ::_getWordsSimilarityMatrix
    // 0xb4011c: ldur            x3, [fp, #-0x20]
    // 0xb40120: ArrayStore: r3[0] = r0  ; List_4
    //     0xb40120: stur            w0, [x3, #0x17]
    //     0xb40124: ldurb           w16, [x3, #-1]
    //     0xb40128: ldurb           w17, [x0, #-1]
    //     0xb4012c: and             x16, x17, x16, lsr #2
    //     0xb40130: tst             x16, HEAP, lsr #32
    //     0xb40134: b.eq            #0xb4013c
    //     0xb40138: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xb4013c: ldur            x1, [fp, #-0x10]
    // 0xb40140: ldur            x2, [fp, #-0x30]
    // 0xb40144: r0 = _getVector()
    //     0xb40144: bl              #0xb40664  ; [package:smart_text_search/smart_text_search/cos_similarity.dart] ::_getVector
    // 0xb40148: ldur            x1, [fp, #-8]
    // 0xb4014c: ldur            x2, [fp, #-0x30]
    // 0xb40150: stur            x0, [fp, #-8]
    // 0xb40154: r0 = _getVector()
    //     0xb40154: bl              #0xb40664  ; [package:smart_text_search/smart_text_search/cos_similarity.dart] ::_getVector
    // 0xb40158: ldur            x2, [fp, #-0x20]
    // 0xb4015c: r1 = Function '<anonymous closure>': static.
    //     0xb4015c: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2acf8] AnonymousClosure: static (0xb41398), in [package:smart_text_search/smart_text_search/cos_similarity.dart] ::textCosineSimilarity (0xb40014)
    //     0xb40160: ldr             x1, [x1, #0xcf8]
    // 0xb40164: stur            x0, [fp, #-0x10]
    // 0xb40168: r0 = AllocateClosure()
    //     0xb40168: bl              #0xec1630  ; AllocateClosureStub
    // 0xb4016c: ldur            x1, [fp, #-8]
    // 0xb40170: mov             x2, x0
    // 0xb40174: r0 = updateAll()
    //     0xb40174: bl              #0xb40490  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::updateAll
    // 0xb40178: ldur            x2, [fp, #-0x20]
    // 0xb4017c: r1 = Function '<anonymous closure>': static.
    //     0xb4017c: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ad00] AnonymousClosure: static (0xb4114c), in [package:smart_text_search/smart_text_search/cos_similarity.dart] ::textCosineSimilarity (0xb40014)
    //     0xb40180: ldr             x1, [x1, #0xd00]
    // 0xb40184: r0 = AllocateClosure()
    //     0xb40184: bl              #0xec1630  ; AllocateClosureStub
    // 0xb40188: ldur            x1, [fp, #-0x10]
    // 0xb4018c: mov             x2, x0
    // 0xb40190: r0 = updateAll()
    //     0xb40190: bl              #0xb40490  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::updateAll
    // 0xb40194: ldur            x1, [fp, #-8]
    // 0xb40198: ldur            x2, [fp, #-0x10]
    // 0xb4019c: r0 = _dotProduct()
    //     0xb4019c: bl              #0xb40308  ; [package:smart_text_search/smart_text_search/cos_similarity.dart] ::_dotProduct
    // 0xb401a0: ldur            x1, [fp, #-8]
    // 0xb401a4: stur            d0, [fp, #-0x38]
    // 0xb401a8: r0 = _getMagnitude()
    //     0xb401a8: bl              #0xb401dc  ; [package:smart_text_search/smart_text_search/cos_similarity.dart] ::_getMagnitude
    // 0xb401ac: ldur            x1, [fp, #-0x10]
    // 0xb401b0: stur            d0, [fp, #-0x40]
    // 0xb401b4: r0 = _getMagnitude()
    //     0xb401b4: bl              #0xb401dc  ; [package:smart_text_search/smart_text_search/cos_similarity.dart] ::_getMagnitude
    // 0xb401b8: ldur            d1, [fp, #-0x40]
    // 0xb401bc: fmul            d2, d1, d0
    // 0xb401c0: ldur            d1, [fp, #-0x38]
    // 0xb401c4: fdiv            d0, d1, d2
    // 0xb401c8: LeaveFrame
    //     0xb401c8: mov             SP, fp
    //     0xb401cc: ldp             fp, lr, [SP], #0x10
    // 0xb401d0: ret
    //     0xb401d0: ret             
    // 0xb401d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb401d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb401d8: b               #0xb40034
  }
  static _ _getMagnitude(/* No info */) {
    // ** addr: 0xb401dc, size: 0x12c
    // 0xb401dc: EnterFrame
    //     0xb401dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb401e0: mov             fp, SP
    // 0xb401e4: AllocStack(0x20)
    //     0xb401e4: sub             SP, SP, #0x20
    // 0xb401e8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xb401e8: mov             x0, x1
    //     0xb401ec: stur            x1, [fp, #-8]
    // 0xb401f0: CheckStackOverflow
    //     0xb401f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb401f4: cmp             SP, x16
    //     0xb401f8: b.ls            #0xb402f8
    // 0xb401fc: LoadField: r2 = r0->field_7
    //     0xb401fc: ldur            w2, [x0, #7]
    // 0xb40200: DecompressPointer r2
    //     0xb40200: add             x2, x2, HEAP, lsl #32
    // 0xb40204: r1 = Null
    //     0xb40204: mov             x1, NULL
    // 0xb40208: r3 = <X1>
    //     0xb40208: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xb4020c: r0 = Null
    //     0xb4020c: mov             x0, NULL
    // 0xb40210: cmp             x2, x0
    // 0xb40214: b.eq            #0xb40224
    // 0xb40218: r30 = InstantiateTypeArgumentsStub
    //     0xb40218: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xb4021c: LoadField: r30 = r30->field_7
    //     0xb4021c: ldur            lr, [lr, #7]
    // 0xb40220: blr             lr
    // 0xb40224: mov             x1, x0
    // 0xb40228: r0 = _CompactIterable()
    //     0xb40228: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xb4022c: mov             x1, x0
    // 0xb40230: ldur            x0, [fp, #-8]
    // 0xb40234: StoreField: r1->field_b = r0
    //     0xb40234: stur            w0, [x1, #0xb]
    // 0xb40238: r0 = -1
    //     0xb40238: movn            x0, #0
    // 0xb4023c: StoreField: r1->field_f = r0
    //     0xb4023c: stur            x0, [x1, #0xf]
    // 0xb40240: r0 = 2
    //     0xb40240: movz            x0, #0x2
    // 0xb40244: ArrayStore: r1[0] = r0  ; List_8
    //     0xb40244: stur            x0, [x1, #0x17]
    // 0xb40248: r0 = iterator()
    //     0xb40248: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0xb4024c: stur            x0, [fp, #-0x10]
    // 0xb40250: LoadField: r2 = r0->field_7
    //     0xb40250: ldur            w2, [x0, #7]
    // 0xb40254: DecompressPointer r2
    //     0xb40254: add             x2, x2, HEAP, lsl #32
    // 0xb40258: stur            x2, [fp, #-8]
    // 0xb4025c: d0 = 0.000000
    //     0xb4025c: eor             v0.16b, v0.16b, v0.16b
    // 0xb40260: stur            d0, [fp, #-0x20]
    // 0xb40264: CheckStackOverflow
    //     0xb40264: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40268: cmp             SP, x16
    //     0xb4026c: b.ls            #0xb40300
    // 0xb40270: mov             x1, x0
    // 0xb40274: r0 = moveNext()
    //     0xb40274: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xb40278: tbnz            w0, #4, #0xb402e4
    // 0xb4027c: ldur            x3, [fp, #-0x10]
    // 0xb40280: LoadField: r4 = r3->field_33
    //     0xb40280: ldur            w4, [x3, #0x33]
    // 0xb40284: DecompressPointer r4
    //     0xb40284: add             x4, x4, HEAP, lsl #32
    // 0xb40288: stur            x4, [fp, #-0x18]
    // 0xb4028c: cmp             w4, NULL
    // 0xb40290: b.ne            #0xb402c4
    // 0xb40294: mov             x0, x4
    // 0xb40298: ldur            x2, [fp, #-8]
    // 0xb4029c: r1 = Null
    //     0xb4029c: mov             x1, NULL
    // 0xb402a0: cmp             w2, NULL
    // 0xb402a4: b.eq            #0xb402c4
    // 0xb402a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb402a8: ldur            w4, [x2, #0x17]
    // 0xb402ac: DecompressPointer r4
    //     0xb402ac: add             x4, x4, HEAP, lsl #32
    // 0xb402b0: r8 = X0
    //     0xb402b0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb402b4: LoadField: r9 = r4->field_7
    //     0xb402b4: ldur            x9, [x4, #7]
    // 0xb402b8: r3 = Null
    //     0xb402b8: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2ad08] Null
    //     0xb402bc: ldr             x3, [x3, #0xd08]
    // 0xb402c0: blr             x9
    // 0xb402c4: ldur            d1, [fp, #-0x20]
    // 0xb402c8: ldur            x0, [fp, #-0x18]
    // 0xb402cc: LoadField: d2 = r0->field_7
    //     0xb402cc: ldur            d2, [x0, #7]
    // 0xb402d0: fmul            d3, d2, d2
    // 0xb402d4: fadd            d0, d1, d3
    // 0xb402d8: ldur            x0, [fp, #-0x10]
    // 0xb402dc: ldur            x2, [fp, #-8]
    // 0xb402e0: b               #0xb40260
    // 0xb402e4: ldur            d1, [fp, #-0x20]
    // 0xb402e8: fsqrt           d0, d1
    // 0xb402ec: LeaveFrame
    //     0xb402ec: mov             SP, fp
    //     0xb402f0: ldp             fp, lr, [SP], #0x10
    // 0xb402f4: ret
    //     0xb402f4: ret             
    // 0xb402f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb402f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb402fc: b               #0xb401fc
    // 0xb40300: r0 = StackOverflowSharedWithFPURegs()
    //     0xb40300: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xb40304: b               #0xb40270
  }
  static _ _dotProduct(/* No info */) {
    // ** addr: 0xb40308, size: 0x88
    // 0xb40308: EnterFrame
    //     0xb40308: stp             fp, lr, [SP, #-0x10]!
    //     0xb4030c: mov             fp, SP
    // 0xb40310: AllocStack(0x38)
    //     0xb40310: sub             SP, SP, #0x38
    // 0xb40314: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb40314: stur            x1, [fp, #-8]
    //     0xb40318: stur            x2, [fp, #-0x10]
    // 0xb4031c: CheckStackOverflow
    //     0xb4031c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40320: cmp             SP, x16
    //     0xb40324: b.ls            #0xb40388
    // 0xb40328: r1 = 1
    //     0xb40328: movz            x1, #0x1
    // 0xb4032c: r0 = AllocateContext()
    //     0xb4032c: bl              #0xec126c  ; AllocateContextStub
    // 0xb40330: mov             x2, x0
    // 0xb40334: ldur            x0, [fp, #-0x10]
    // 0xb40338: stur            x2, [fp, #-0x18]
    // 0xb4033c: StoreField: r2->field_f = r0
    //     0xb4033c: stur            w0, [x2, #0xf]
    // 0xb40340: ldur            x1, [fp, #-8]
    // 0xb40344: r0 = entries()
    //     0xb40344: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0xb40348: ldur            x2, [fp, #-0x18]
    // 0xb4034c: r1 = Function '<anonymous closure>': static.
    //     0xb4034c: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ad18] AnonymousClosure: static (0xb40390), in [package:smart_text_search/smart_text_search/cos_similarity.dart] ::_dotProduct (0xb40308)
    //     0xb40350: ldr             x1, [x1, #0xd18]
    // 0xb40354: stur            x0, [fp, #-8]
    // 0xb40358: r0 = AllocateClosure()
    //     0xb40358: bl              #0xec1630  ; AllocateClosureStub
    // 0xb4035c: r16 = <double>
    //     0xb4035c: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xb40360: ldur            lr, [fp, #-8]
    // 0xb40364: stp             lr, x16, [SP, #0x10]
    // 0xb40368: r16 = 0.000000
    //     0xb40368: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb4036c: stp             x0, x16, [SP]
    // 0xb40370: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xb40370: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xb40374: r0 = fold()
    //     0xb40374: bl              #0x7d87a0  ; [dart:core] Iterable::fold
    // 0xb40378: LoadField: d0 = r0->field_7
    //     0xb40378: ldur            d0, [x0, #7]
    // 0xb4037c: LeaveFrame
    //     0xb4037c: mov             SP, fp
    //     0xb40380: ldp             fp, lr, [SP], #0x10
    // 0xb40384: ret
    //     0xb40384: ret             
    // 0xb40388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4038c: b               #0xb40328
  }
  [closure] static double <anonymous closure>(dynamic, double, MapEntry<String, double>) {
    // ** addr: 0xb40390, size: 0x100
    // 0xb40390: EnterFrame
    //     0xb40390: stp             fp, lr, [SP, #-0x10]!
    //     0xb40394: mov             fp, SP
    // 0xb40398: AllocStack(0x20)
    //     0xb40398: sub             SP, SP, #0x20
    // 0xb4039c: SetupParameters()
    //     0xb4039c: ldr             x0, [fp, #0x20]
    //     0xb403a0: ldur            w1, [x0, #0x17]
    //     0xb403a4: add             x1, x1, HEAP, lsl #32
    // 0xb403a8: CheckStackOverflow
    //     0xb403a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb403ac: cmp             SP, x16
    //     0xb403b0: b.ls            #0xb40474
    // 0xb403b4: ldr             x0, [fp, #0x10]
    // 0xb403b8: LoadField: r3 = r0->field_f
    //     0xb403b8: ldur            w3, [x0, #0xf]
    // 0xb403bc: DecompressPointer r3
    //     0xb403bc: add             x3, x3, HEAP, lsl #32
    // 0xb403c0: stur            x3, [fp, #-0x10]
    // 0xb403c4: LoadField: r4 = r1->field_f
    //     0xb403c4: ldur            w4, [x1, #0xf]
    // 0xb403c8: DecompressPointer r4
    //     0xb403c8: add             x4, x4, HEAP, lsl #32
    // 0xb403cc: stur            x4, [fp, #-8]
    // 0xb403d0: LoadField: r2 = r0->field_b
    //     0xb403d0: ldur            w2, [x0, #0xb]
    // 0xb403d4: DecompressPointer r2
    //     0xb403d4: add             x2, x2, HEAP, lsl #32
    // 0xb403d8: mov             x1, x4
    // 0xb403dc: r0 = _getValueOrData()
    //     0xb403dc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb403e0: mov             x1, x0
    // 0xb403e4: ldur            x0, [fp, #-8]
    // 0xb403e8: LoadField: r2 = r0->field_f
    //     0xb403e8: ldur            w2, [x0, #0xf]
    // 0xb403ec: DecompressPointer r2
    //     0xb403ec: add             x2, x2, HEAP, lsl #32
    // 0xb403f0: cmp             w2, w1
    // 0xb403f4: b.ne            #0xb40400
    // 0xb403f8: r0 = Null
    //     0xb403f8: mov             x0, NULL
    // 0xb403fc: b               #0xb40404
    // 0xb40400: mov             x0, x1
    // 0xb40404: cmp             w0, NULL
    // 0xb40408: b.ne            #0xb40414
    // 0xb4040c: r2 = 0
    //     0xb4040c: movz            x2, #0
    // 0xb40410: b               #0xb40418
    // 0xb40414: mov             x2, x0
    // 0xb40418: ldr             x1, [fp, #0x18]
    // 0xb4041c: ldur            x0, [fp, #-0x10]
    // 0xb40420: cmp             w0, NULL
    // 0xb40424: b.eq            #0xb4047c
    // 0xb40428: stp             x2, x0, [SP]
    // 0xb4042c: r0 = *()
    //     0xb4042c: bl              #0xebfc6c  ; [dart:core] _Double::*
    // 0xb40430: ldr             x1, [fp, #0x18]
    // 0xb40434: LoadField: d0 = r1->field_7
    //     0xb40434: ldur            d0, [x1, #7]
    // 0xb40438: LoadField: d1 = r0->field_7
    //     0xb40438: ldur            d1, [x0, #7]
    // 0xb4043c: fadd            d2, d0, d1
    // 0xb40440: r0 = inline_Allocate_Double()
    //     0xb40440: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb40444: add             x0, x0, #0x10
    //     0xb40448: cmp             x1, x0
    //     0xb4044c: b.ls            #0xb40480
    //     0xb40450: str             x0, [THR, #0x50]  ; THR::top
    //     0xb40454: sub             x0, x0, #0xf
    //     0xb40458: movz            x1, #0xe15c
    //     0xb4045c: movk            x1, #0x3, lsl #16
    //     0xb40460: stur            x1, [x0, #-1]
    // 0xb40464: StoreField: r0->field_7 = d2
    //     0xb40464: stur            d2, [x0, #7]
    // 0xb40468: LeaveFrame
    //     0xb40468: mov             SP, fp
    //     0xb4046c: ldp             fp, lr, [SP], #0x10
    // 0xb40470: ret
    //     0xb40470: ret             
    // 0xb40474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40474: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40478: b               #0xb403b4
    // 0xb4047c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb4047c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xb40480: SaveReg d2
    //     0xb40480: str             q2, [SP, #-0x10]!
    // 0xb40484: r0 = AllocateDouble()
    //     0xb40484: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb40488: RestoreReg d2
    //     0xb40488: ldr             q2, [SP], #0x10
    // 0xb4048c: b               #0xb40464
  }
  static _ _getVector(/* No info */) {
    // ** addr: 0xb40664, size: 0x234
    // 0xb40664: EnterFrame
    //     0xb40664: stp             fp, lr, [SP, #-0x10]!
    //     0xb40668: mov             fp, SP
    // 0xb4066c: AllocStack(0x40)
    //     0xb4066c: sub             SP, SP, #0x40
    // 0xb40670: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb40670: mov             x0, x1
    //     0xb40674: stur            x1, [fp, #-8]
    //     0xb40678: mov             x1, x2
    //     0xb4067c: stur            x2, [fp, #-0x10]
    // 0xb40680: CheckStackOverflow
    //     0xb40680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40684: cmp             SP, x16
    //     0xb40688: b.ls            #0xb40860
    // 0xb4068c: r16 = <String, double>
    //     0xb4068c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25d90] TypeArguments: <String, double>
    //     0xb40690: ldr             x16, [x16, #0xd90]
    // 0xb40694: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xb40698: stp             lr, x16, [SP]
    // 0xb4069c: r0 = Map._fromLiteral()
    //     0xb4069c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb406a0: ldur            x1, [fp, #-0x10]
    // 0xb406a4: stur            x0, [fp, #-0x10]
    // 0xb406a8: r0 = iterator()
    //     0xb406a8: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0xb406ac: stur            x0, [fp, #-0x20]
    // 0xb406b0: LoadField: r2 = r0->field_7
    //     0xb406b0: ldur            w2, [x0, #7]
    // 0xb406b4: DecompressPointer r2
    //     0xb406b4: add             x2, x2, HEAP, lsl #32
    // 0xb406b8: stur            x2, [fp, #-0x18]
    // 0xb406bc: CheckStackOverflow
    //     0xb406bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb406c0: cmp             SP, x16
    //     0xb406c4: b.ls            #0xb40868
    // 0xb406c8: mov             x1, x0
    // 0xb406cc: r0 = moveNext()
    //     0xb406cc: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xb406d0: tbnz            w0, #4, #0xb40748
    // 0xb406d4: ldur            x3, [fp, #-0x20]
    // 0xb406d8: LoadField: r4 = r3->field_33
    //     0xb406d8: ldur            w4, [x3, #0x33]
    // 0xb406dc: DecompressPointer r4
    //     0xb406dc: add             x4, x4, HEAP, lsl #32
    // 0xb406e0: stur            x4, [fp, #-0x28]
    // 0xb406e4: cmp             w4, NULL
    // 0xb406e8: b.ne            #0xb4071c
    // 0xb406ec: mov             x0, x4
    // 0xb406f0: ldur            x2, [fp, #-0x18]
    // 0xb406f4: r1 = Null
    //     0xb406f4: mov             x1, NULL
    // 0xb406f8: cmp             w2, NULL
    // 0xb406fc: b.eq            #0xb4071c
    // 0xb40700: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb40700: ldur            w4, [x2, #0x17]
    // 0xb40704: DecompressPointer r4
    //     0xb40704: add             x4, x4, HEAP, lsl #32
    // 0xb40708: r8 = X0
    //     0xb40708: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb4070c: LoadField: r9 = r4->field_7
    //     0xb4070c: ldur            x9, [x4, #7]
    // 0xb40710: r3 = Null
    //     0xb40710: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2ad38] Null
    //     0xb40714: ldr             x3, [x3, #0xd38]
    // 0xb40718: blr             x9
    // 0xb4071c: ldur            x1, [fp, #-0x10]
    // 0xb40720: ldur            x2, [fp, #-0x28]
    // 0xb40724: r0 = _hashCode()
    //     0xb40724: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xb40728: ldur            x1, [fp, #-0x10]
    // 0xb4072c: ldur            x2, [fp, #-0x28]
    // 0xb40730: mov             x5, x0
    // 0xb40734: r3 = 0.000000
    //     0xb40734: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb40738: r0 = _set()
    //     0xb40738: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xb4073c: ldur            x0, [fp, #-0x20]
    // 0xb40740: ldur            x2, [fp, #-0x18]
    // 0xb40744: b               #0xb406bc
    // 0xb40748: ldur            x1, [fp, #-8]
    // 0xb4074c: r0 = iterator()
    //     0xb4074c: bl              #0xa5b6f8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0xb40750: stur            x0, [fp, #-0x18]
    // 0xb40754: LoadField: r2 = r0->field_7
    //     0xb40754: ldur            w2, [x0, #7]
    // 0xb40758: DecompressPointer r2
    //     0xb40758: add             x2, x2, HEAP, lsl #32
    // 0xb4075c: stur            x2, [fp, #-8]
    // 0xb40760: ldur            x3, [fp, #-0x10]
    // 0xb40764: CheckStackOverflow
    //     0xb40764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40768: cmp             SP, x16
    //     0xb4076c: b.ls            #0xb40870
    // 0xb40770: mov             x1, x0
    // 0xb40774: r0 = moveNext()
    //     0xb40774: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xb40778: tbnz            w0, #4, #0xb40850
    // 0xb4077c: ldur            x3, [fp, #-0x18]
    // 0xb40780: LoadField: r4 = r3->field_33
    //     0xb40780: ldur            w4, [x3, #0x33]
    // 0xb40784: DecompressPointer r4
    //     0xb40784: add             x4, x4, HEAP, lsl #32
    // 0xb40788: stur            x4, [fp, #-0x20]
    // 0xb4078c: cmp             w4, NULL
    // 0xb40790: b.ne            #0xb407c4
    // 0xb40794: mov             x0, x4
    // 0xb40798: ldur            x2, [fp, #-8]
    // 0xb4079c: r1 = Null
    //     0xb4079c: mov             x1, NULL
    // 0xb407a0: cmp             w2, NULL
    // 0xb407a4: b.eq            #0xb407c4
    // 0xb407a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb407a8: ldur            w4, [x2, #0x17]
    // 0xb407ac: DecompressPointer r4
    //     0xb407ac: add             x4, x4, HEAP, lsl #32
    // 0xb407b0: r8 = X0
    //     0xb407b0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb407b4: LoadField: r9 = r4->field_7
    //     0xb407b4: ldur            x9, [x4, #7]
    // 0xb407b8: r3 = Null
    //     0xb407b8: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2ad48] Null
    //     0xb407bc: ldr             x3, [x3, #0xd48]
    // 0xb407c0: blr             x9
    // 0xb407c4: ldur            x3, [fp, #-0x10]
    // 0xb407c8: r0 = LoadClassIdInstr(r3)
    //     0xb407c8: ldur            x0, [x3, #-1]
    //     0xb407cc: ubfx            x0, x0, #0xc, #0x14
    // 0xb407d0: mov             x1, x3
    // 0xb407d4: ldur            x2, [fp, #-0x20]
    // 0xb407d8: r0 = GDT[cid_x0 + -0x114]()
    //     0xb407d8: sub             lr, x0, #0x114
    //     0xb407dc: ldr             lr, [x21, lr, lsl #3]
    //     0xb407e0: blr             lr
    // 0xb407e4: cmp             w0, NULL
    // 0xb407e8: b.eq            #0xb40878
    // 0xb407ec: LoadField: d0 = r0->field_7
    //     0xb407ec: ldur            d0, [x0, #7]
    // 0xb407f0: d1 = 1.000000
    //     0xb407f0: fmov            d1, #1.00000000
    // 0xb407f4: fadd            d2, d0, d1
    // 0xb407f8: ldur            x1, [fp, #-0x10]
    // 0xb407fc: ldur            x2, [fp, #-0x20]
    // 0xb40800: stur            d2, [fp, #-0x30]
    // 0xb40804: r0 = _hashCode()
    //     0xb40804: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xb40808: ldur            d0, [fp, #-0x30]
    // 0xb4080c: r3 = inline_Allocate_Double()
    //     0xb4080c: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0xb40810: add             x3, x3, #0x10
    //     0xb40814: cmp             x1, x3
    //     0xb40818: b.ls            #0xb4087c
    //     0xb4081c: str             x3, [THR, #0x50]  ; THR::top
    //     0xb40820: sub             x3, x3, #0xf
    //     0xb40824: movz            x1, #0xe15c
    //     0xb40828: movk            x1, #0x3, lsl #16
    //     0xb4082c: stur            x1, [x3, #-1]
    // 0xb40830: StoreField: r3->field_7 = d0
    //     0xb40830: stur            d0, [x3, #7]
    // 0xb40834: ldur            x1, [fp, #-0x10]
    // 0xb40838: ldur            x2, [fp, #-0x20]
    // 0xb4083c: mov             x5, x0
    // 0xb40840: r0 = _set()
    //     0xb40840: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xb40844: ldur            x0, [fp, #-0x18]
    // 0xb40848: ldur            x2, [fp, #-8]
    // 0xb4084c: b               #0xb40760
    // 0xb40850: ldur            x0, [fp, #-0x10]
    // 0xb40854: LeaveFrame
    //     0xb40854: mov             SP, fp
    //     0xb40858: ldp             fp, lr, [SP], #0x10
    // 0xb4085c: ret
    //     0xb4085c: ret             
    // 0xb40860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40860: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40864: b               #0xb4068c
    // 0xb40868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40868: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4086c: b               #0xb406c8
    // 0xb40870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40870: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40874: b               #0xb40770
    // 0xb40878: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb40878: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4087c: SaveReg d0
    //     0xb4087c: str             q0, [SP, #-0x10]!
    // 0xb40880: SaveReg r0
    //     0xb40880: str             x0, [SP, #-8]!
    // 0xb40884: r0 = AllocateDouble()
    //     0xb40884: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb40888: mov             x3, x0
    // 0xb4088c: RestoreReg r0
    //     0xb4088c: ldr             x0, [SP], #8
    // 0xb40890: RestoreReg d0
    //     0xb40890: ldr             q0, [SP], #0x10
    // 0xb40894: b               #0xb40830
  }
  static _ _getWordsSimilarityMatrix(/* No info */) {
    // ** addr: 0xb40898, size: 0x2bc
    // 0xb40898: EnterFrame
    //     0xb40898: stp             fp, lr, [SP, #-0x10]!
    //     0xb4089c: mov             fp, SP
    // 0xb408a0: AllocStack(0x60)
    //     0xb408a0: sub             SP, SP, #0x60
    // 0xb408a4: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb408a4: mov             x3, x1
    //     0xb408a8: mov             x0, x2
    //     0xb408ac: stur            x1, [fp, #-8]
    //     0xb408b0: stur            x2, [fp, #-0x10]
    // 0xb408b4: CheckStackOverflow
    //     0xb408b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb408b8: cmp             SP, x16
    //     0xb408bc: b.ls            #0xb40b04
    // 0xb408c0: LoadField: r1 = r3->field_b
    //     0xb408c0: ldur            w1, [x3, #0xb]
    // 0xb408c4: r2 = LoadInt32Instr(r1)
    //     0xb408c4: sbfx            x2, x1, #1, #0x1f
    // 0xb408c8: r1 = <List<double>>
    //     0xb408c8: ldr             x1, [PP, #0x59d8]  ; [pp+0x59d8] TypeArguments: <List<double>>
    // 0xb408cc: r0 = _GrowableList()
    //     0xb408cc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb408d0: stur            x0, [fp, #-0x40]
    // 0xb408d4: LoadField: r1 = r0->field_b
    //     0xb408d4: ldur            w1, [x0, #0xb]
    // 0xb408d8: r3 = LoadInt32Instr(r1)
    //     0xb408d8: sbfx            x3, x1, #1, #0x1f
    // 0xb408dc: ldur            x4, [fp, #-0x10]
    // 0xb408e0: stur            x3, [fp, #-0x38]
    // 0xb408e4: LoadField: r5 = r4->field_b
    //     0xb408e4: ldur            w5, [x4, #0xb]
    // 0xb408e8: stur            x5, [fp, #-0x30]
    // 0xb408ec: r6 = LoadInt32Instr(r5)
    //     0xb408ec: sbfx            x6, x5, #1, #0x1f
    // 0xb408f0: stur            x6, [fp, #-0x28]
    // 0xb408f4: LoadField: r7 = r0->field_f
    //     0xb408f4: ldur            w7, [x0, #0xf]
    // 0xb408f8: DecompressPointer r7
    //     0xb408f8: add             x7, x7, HEAP, lsl #32
    // 0xb408fc: stur            x7, [fp, #-0x20]
    // 0xb40900: r8 = 0
    //     0xb40900: movz            x8, #0
    // 0xb40904: stur            x8, [fp, #-0x18]
    // 0xb40908: CheckStackOverflow
    //     0xb40908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4090c: cmp             SP, x16
    //     0xb40910: b.ls            #0xb40b0c
    // 0xb40914: cmp             x8, x3
    // 0xb40918: b.ge            #0xb409ac
    // 0xb4091c: mov             x2, x5
    // 0xb40920: r1 = <double>
    //     0xb40920: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xb40924: r0 = AllocateArray()
    //     0xb40924: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb40928: ldur            x2, [fp, #-0x28]
    // 0xb4092c: r1 = 0
    //     0xb4092c: movz            x1, #0
    // 0xb40930: CheckStackOverflow
    //     0xb40930: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40934: cmp             SP, x16
    //     0xb40938: b.ls            #0xb40b14
    // 0xb4093c: cmp             x1, x2
    // 0xb40940: b.ge            #0xb4095c
    // 0xb40944: add             x3, x0, x1, lsl #2
    // 0xb40948: r16 = 0.000000
    //     0xb40948: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb4094c: StoreField: r3->field_f = r16
    //     0xb4094c: stur            w16, [x3, #0xf]
    // 0xb40950: add             x3, x1, #1
    // 0xb40954: mov             x1, x3
    // 0xb40958: b               #0xb40930
    // 0xb4095c: ldur            x3, [fp, #-0x18]
    // 0xb40960: ldur            x1, [fp, #-0x20]
    // 0xb40964: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb40964: add             x25, x1, x3, lsl #2
    //     0xb40968: add             x25, x25, #0xf
    //     0xb4096c: str             w0, [x25]
    //     0xb40970: tbz             w0, #0, #0xb4098c
    //     0xb40974: ldurb           w16, [x1, #-1]
    //     0xb40978: ldurb           w17, [x0, #-1]
    //     0xb4097c: and             x16, x17, x16, lsr #2
    //     0xb40980: tst             x16, HEAP, lsr #32
    //     0xb40984: b.eq            #0xb4098c
    //     0xb40988: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb4098c: add             x8, x3, #1
    // 0xb40990: ldur            x4, [fp, #-0x10]
    // 0xb40994: ldur            x0, [fp, #-0x40]
    // 0xb40998: ldur            x7, [fp, #-0x20]
    // 0xb4099c: ldur            x5, [fp, #-0x30]
    // 0xb409a0: ldur            x3, [fp, #-0x38]
    // 0xb409a4: mov             x6, x2
    // 0xb409a8: b               #0xb40904
    // 0xb409ac: r5 = 0
    //     0xb409ac: movz            x5, #0
    // 0xb409b0: ldur            x4, [fp, #-8]
    // 0xb409b4: ldur            x0, [fp, #-0x10]
    // 0xb409b8: ldur            x3, [fp, #-0x20]
    // 0xb409bc: stur            x5, [fp, #-0x28]
    // 0xb409c0: CheckStackOverflow
    //     0xb409c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb409c4: cmp             SP, x16
    //     0xb409c8: b.ls            #0xb40b1c
    // 0xb409cc: LoadField: r1 = r4->field_b
    //     0xb409cc: ldur            w1, [x4, #0xb]
    // 0xb409d0: r2 = LoadInt32Instr(r1)
    //     0xb409d0: sbfx            x2, x1, #1, #0x1f
    // 0xb409d4: cmp             x5, x2
    // 0xb409d8: b.ge            #0xb40af4
    // 0xb409dc: LoadField: r1 = r4->field_f
    //     0xb409dc: ldur            w1, [x4, #0xf]
    // 0xb409e0: DecompressPointer r1
    //     0xb409e0: add             x1, x1, HEAP, lsl #32
    // 0xb409e4: ArrayLoad: r6 = r1[r5]  ; Unknown_4
    //     0xb409e4: add             x16, x1, x5, lsl #2
    //     0xb409e8: ldur            w6, [x16, #0xf]
    // 0xb409ec: DecompressPointer r6
    //     0xb409ec: add             x6, x6, HEAP, lsl #32
    // 0xb409f0: stur            x6, [fp, #-0x48]
    // 0xb409f4: r7 = 0
    //     0xb409f4: movz            x7, #0
    // 0xb409f8: stur            x7, [fp, #-0x18]
    // 0xb409fc: CheckStackOverflow
    //     0xb409fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40a00: cmp             SP, x16
    //     0xb40a04: b.ls            #0xb40b24
    // 0xb40a08: LoadField: r1 = r0->field_b
    //     0xb40a08: ldur            w1, [x0, #0xb]
    // 0xb40a0c: r2 = LoadInt32Instr(r1)
    //     0xb40a0c: sbfx            x2, x1, #1, #0x1f
    // 0xb40a10: cmp             x7, x2
    // 0xb40a14: b.ge            #0xb40ae8
    // 0xb40a18: LoadField: r1 = r0->field_f
    //     0xb40a18: ldur            w1, [x0, #0xf]
    // 0xb40a1c: DecompressPointer r1
    //     0xb40a1c: add             x1, x1, HEAP, lsl #32
    // 0xb40a20: lsl             x8, x7, #1
    // 0xb40a24: stur            x8, [fp, #-0x30]
    // 0xb40a28: ArrayLoad: r2 = r1[r7]  ; Unknown_4
    //     0xb40a28: add             x16, x1, x7, lsl #2
    //     0xb40a2c: ldur            w2, [x16, #0xf]
    // 0xb40a30: DecompressPointer r2
    //     0xb40a30: add             x2, x2, HEAP, lsl #32
    // 0xb40a34: mov             x1, x6
    // 0xb40a38: r0 = wordsLevenshteinDistance()
    //     0xb40a38: bl              #0xb40b54  ; [package:smart_text_search/smart_text_search/cos_similarity.dart] ::wordsLevenshteinDistance
    // 0xb40a3c: mov             x2, x0
    // 0xb40a40: ldur            x0, [fp, #-0x38]
    // 0xb40a44: ldur            x1, [fp, #-0x28]
    // 0xb40a48: cmp             x1, x0
    // 0xb40a4c: b.hs            #0xb40b2c
    // 0xb40a50: ldur            x3, [fp, #-0x28]
    // 0xb40a54: ldur            x1, [fp, #-0x20]
    // 0xb40a58: ArrayLoad: r0 = r1[r3]  ; Unknown_4
    //     0xb40a58: add             x16, x1, x3, lsl #2
    //     0xb40a5c: ldur            w0, [x16, #0xf]
    // 0xb40a60: DecompressPointer r0
    //     0xb40a60: add             x0, x0, HEAP, lsl #32
    // 0xb40a64: add             x4, x2, #1
    // 0xb40a68: scvtf           d0, x4
    // 0xb40a6c: d1 = 1.000000
    //     0xb40a6c: fmov            d1, #1.00000000
    // 0xb40a70: fdiv            d2, d1, d0
    // 0xb40a74: r2 = inline_Allocate_Double()
    //     0xb40a74: ldp             x2, x4, [THR, #0x50]  ; THR::top
    //     0xb40a78: add             x2, x2, #0x10
    //     0xb40a7c: cmp             x4, x2
    //     0xb40a80: b.ls            #0xb40b30
    //     0xb40a84: str             x2, [THR, #0x50]  ; THR::top
    //     0xb40a88: sub             x2, x2, #0xf
    //     0xb40a8c: movz            x4, #0xe15c
    //     0xb40a90: movk            x4, #0x3, lsl #16
    //     0xb40a94: stur            x4, [x2, #-1]
    // 0xb40a98: StoreField: r2->field_7 = d2
    //     0xb40a98: stur            d2, [x2, #7]
    // 0xb40a9c: r4 = LoadClassIdInstr(r0)
    //     0xb40a9c: ldur            x4, [x0, #-1]
    //     0xb40aa0: ubfx            x4, x4, #0xc, #0x14
    // 0xb40aa4: ldur            x16, [fp, #-0x30]
    // 0xb40aa8: stp             x16, x0, [SP, #8]
    // 0xb40aac: str             x2, [SP]
    // 0xb40ab0: mov             x0, x4
    // 0xb40ab4: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xb40ab4: movz            x17, #0x310f
    //     0xb40ab8: movk            x17, #0x1, lsl #16
    //     0xb40abc: add             lr, x0, x17
    //     0xb40ac0: ldr             lr, [x21, lr, lsl #3]
    //     0xb40ac4: blr             lr
    // 0xb40ac8: ldur            x1, [fp, #-0x18]
    // 0xb40acc: add             x7, x1, #1
    // 0xb40ad0: ldur            x4, [fp, #-8]
    // 0xb40ad4: ldur            x0, [fp, #-0x10]
    // 0xb40ad8: ldur            x5, [fp, #-0x28]
    // 0xb40adc: ldur            x3, [fp, #-0x20]
    // 0xb40ae0: ldur            x6, [fp, #-0x48]
    // 0xb40ae4: b               #0xb409f8
    // 0xb40ae8: mov             x1, x5
    // 0xb40aec: add             x5, x1, #1
    // 0xb40af0: b               #0xb409b0
    // 0xb40af4: ldur            x0, [fp, #-0x40]
    // 0xb40af8: LeaveFrame
    //     0xb40af8: mov             SP, fp
    //     0xb40afc: ldp             fp, lr, [SP], #0x10
    // 0xb40b00: ret
    //     0xb40b00: ret             
    // 0xb40b04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40b04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40b08: b               #0xb408c0
    // 0xb40b0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40b0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40b10: b               #0xb40914
    // 0xb40b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40b14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40b18: b               #0xb4093c
    // 0xb40b1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40b1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40b20: b               #0xb409cc
    // 0xb40b24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb40b24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40b28: b               #0xb40a08
    // 0xb40b2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb40b2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb40b30: stp             q1, q2, [SP, #-0x20]!
    // 0xb40b34: stp             x1, x3, [SP, #-0x10]!
    // 0xb40b38: SaveReg r0
    //     0xb40b38: str             x0, [SP, #-8]!
    // 0xb40b3c: r0 = AllocateDouble()
    //     0xb40b3c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb40b40: mov             x2, x0
    // 0xb40b44: RestoreReg r0
    //     0xb40b44: ldr             x0, [SP], #8
    // 0xb40b48: ldp             x1, x3, [SP], #0x10
    // 0xb40b4c: ldp             q1, q2, [SP], #0x20
    // 0xb40b50: b               #0xb40a98
  }
  static _ wordsLevenshteinDistance(/* No info */) {
    // ** addr: 0xb40b54, size: 0x5f8
    // 0xb40b54: EnterFrame
    //     0xb40b54: stp             fp, lr, [SP, #-0x10]!
    //     0xb40b58: mov             fp, SP
    // 0xb40b5c: AllocStack(0xa0)
    //     0xb40b5c: sub             SP, SP, #0xa0
    // 0xb40b60: SetupParameters(dynamic _ /* r1 => r3, fp-0x20 */, dynamic _ /* r2 => r0, fp-0x28 */)
    //     0xb40b60: mov             x3, x1
    //     0xb40b64: mov             x0, x2
    //     0xb40b68: stur            x1, [fp, #-0x20]
    //     0xb40b6c: stur            x2, [fp, #-0x28]
    // 0xb40b70: CheckStackOverflow
    //     0xb40b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40b74: cmp             SP, x16
    //     0xb40b78: b.ls            #0xb41100
    // 0xb40b7c: LoadField: r1 = r3->field_7
    //     0xb40b7c: ldur            w1, [x3, #7]
    // 0xb40b80: r2 = LoadInt32Instr(r1)
    //     0xb40b80: sbfx            x2, x1, #1, #0x1f
    // 0xb40b84: add             x4, x2, #1
    // 0xb40b88: stur            x4, [fp, #-0x18]
    // 0xb40b8c: LoadField: r1 = r0->field_7
    //     0xb40b8c: ldur            w1, [x0, #7]
    // 0xb40b90: r2 = LoadInt32Instr(r1)
    //     0xb40b90: sbfx            x2, x1, #1, #0x1f
    // 0xb40b94: add             x5, x2, #1
    // 0xb40b98: stur            x5, [fp, #-0x10]
    // 0xb40b9c: lsl             x6, x5, #1
    // 0xb40ba0: mov             x2, x4
    // 0xb40ba4: stur            x6, [fp, #-8]
    // 0xb40ba8: r1 = <List<int>>
    //     0xb40ba8: ldr             x1, [PP, #0x14c0]  ; [pp+0x14c0] TypeArguments: <List<int>>
    // 0xb40bac: r0 = _GrowableList()
    //     0xb40bac: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb40bb0: LoadField: r1 = r0->field_b
    //     0xb40bb0: ldur            w1, [x0, #0xb]
    // 0xb40bb4: r3 = LoadInt32Instr(r1)
    //     0xb40bb4: sbfx            x3, x1, #1, #0x1f
    // 0xb40bb8: stur            x3, [fp, #-0x40]
    // 0xb40bbc: LoadField: r4 = r0->field_f
    //     0xb40bbc: ldur            w4, [x0, #0xf]
    // 0xb40bc0: DecompressPointer r4
    //     0xb40bc0: add             x4, x4, HEAP, lsl #32
    // 0xb40bc4: stur            x4, [fp, #-0x38]
    // 0xb40bc8: ldur            x0, [fp, #-0x10]
    // 0xb40bcc: r5 = 0
    //     0xb40bcc: movz            x5, #0
    // 0xb40bd0: stur            x5, [fp, #-0x30]
    // 0xb40bd4: CheckStackOverflow
    //     0xb40bd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40bd8: cmp             SP, x16
    //     0xb40bdc: b.ls            #0xb41108
    // 0xb40be0: cmp             x5, x3
    // 0xb40be4: b.ge            #0xb40c68
    // 0xb40be8: ldur            x2, [fp, #-8]
    // 0xb40bec: r1 = <int>
    //     0xb40bec: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb40bf0: r0 = AllocateArray()
    //     0xb40bf0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb40bf4: ldur            x2, [fp, #-0x10]
    // 0xb40bf8: r1 = 0
    //     0xb40bf8: movz            x1, #0
    // 0xb40bfc: CheckStackOverflow
    //     0xb40bfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40c00: cmp             SP, x16
    //     0xb40c04: b.ls            #0xb41110
    // 0xb40c08: cmp             x1, x2
    // 0xb40c0c: b.ge            #0xb40c24
    // 0xb40c10: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0xb40c10: add             x3, x0, x1, lsl #2
    //     0xb40c14: stur            wzr, [x3, #0xf]
    // 0xb40c18: add             x3, x1, #1
    // 0xb40c1c: mov             x1, x3
    // 0xb40c20: b               #0xb40bfc
    // 0xb40c24: ldur            x3, [fp, #-0x30]
    // 0xb40c28: ldur            x1, [fp, #-0x38]
    // 0xb40c2c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb40c2c: add             x25, x1, x3, lsl #2
    //     0xb40c30: add             x25, x25, #0xf
    //     0xb40c34: str             w0, [x25]
    //     0xb40c38: tbz             w0, #0, #0xb40c54
    //     0xb40c3c: ldurb           w16, [x1, #-1]
    //     0xb40c40: ldurb           w17, [x0, #-1]
    //     0xb40c44: and             x16, x17, x16, lsr #2
    //     0xb40c48: tst             x16, HEAP, lsr #32
    //     0xb40c4c: b.eq            #0xb40c54
    //     0xb40c50: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb40c54: add             x5, x3, #1
    // 0xb40c58: mov             x0, x2
    // 0xb40c5c: ldur            x4, [fp, #-0x38]
    // 0xb40c60: ldur            x3, [fp, #-0x40]
    // 0xb40c64: b               #0xb40bd0
    // 0xb40c68: mov             x2, x0
    // 0xb40c6c: r5 = 0
    //     0xb40c6c: movz            x5, #0
    // 0xb40c70: ldur            x4, [fp, #-0x18]
    // 0xb40c74: ldur            x3, [fp, #-0x38]
    // 0xb40c78: stur            x5, [fp, #-0x30]
    // 0xb40c7c: CheckStackOverflow
    //     0xb40c7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40c80: cmp             SP, x16
    //     0xb40c84: b.ls            #0xb41118
    // 0xb40c88: cmp             x5, x4
    // 0xb40c8c: b.ge            #0xb40ce8
    // 0xb40c90: ldur            x0, [fp, #-0x40]
    // 0xb40c94: mov             x1, x5
    // 0xb40c98: cmp             x1, x0
    // 0xb40c9c: b.hs            #0xb41120
    // 0xb40ca0: lsl             x0, x5, #1
    // 0xb40ca4: ArrayLoad: r1 = r3[r5]  ; Unknown_4
    //     0xb40ca4: add             x16, x3, x5, lsl #2
    //     0xb40ca8: ldur            w1, [x16, #0xf]
    // 0xb40cac: DecompressPointer r1
    //     0xb40cac: add             x1, x1, HEAP, lsl #32
    // 0xb40cb0: r6 = LoadClassIdInstr(r1)
    //     0xb40cb0: ldur            x6, [x1, #-1]
    //     0xb40cb4: ubfx            x6, x6, #0xc, #0x14
    // 0xb40cb8: stp             xzr, x1, [SP, #8]
    // 0xb40cbc: str             x0, [SP]
    // 0xb40cc0: mov             x0, x6
    // 0xb40cc4: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xb40cc4: movz            x17, #0x310f
    //     0xb40cc8: movk            x17, #0x1, lsl #16
    //     0xb40ccc: add             lr, x0, x17
    //     0xb40cd0: ldr             lr, [x21, lr, lsl #3]
    //     0xb40cd4: blr             lr
    // 0xb40cd8: ldur            x0, [fp, #-0x30]
    // 0xb40cdc: add             x5, x0, #1
    // 0xb40ce0: ldur            x2, [fp, #-0x10]
    // 0xb40ce4: b               #0xb40c70
    // 0xb40ce8: r4 = 0
    //     0xb40ce8: movz            x4, #0
    // 0xb40cec: ldur            x2, [fp, #-0x10]
    // 0xb40cf0: ldur            x3, [fp, #-0x38]
    // 0xb40cf4: stur            x4, [fp, #-0x30]
    // 0xb40cf8: CheckStackOverflow
    //     0xb40cf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40cfc: cmp             SP, x16
    //     0xb40d00: b.ls            #0xb41124
    // 0xb40d04: cmp             x4, x2
    // 0xb40d08: b.ge            #0xb40d6c
    // 0xb40d0c: ldur            x0, [fp, #-0x40]
    // 0xb40d10: r1 = 0
    //     0xb40d10: movz            x1, #0
    // 0xb40d14: cmp             x1, x0
    // 0xb40d18: b.hs            #0xb4112c
    // 0xb40d1c: LoadField: r5 = r3->field_f
    //     0xb40d1c: ldur            w5, [x3, #0xf]
    // 0xb40d20: DecompressPointer r5
    //     0xb40d20: add             x5, x5, HEAP, lsl #32
    // 0xb40d24: r0 = BoxInt64Instr(r4)
    //     0xb40d24: sbfiz           x0, x4, #1, #0x1f
    //     0xb40d28: cmp             x4, x0, asr #1
    //     0xb40d2c: b.eq            #0xb40d38
    //     0xb40d30: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb40d34: stur            x4, [x0, #7]
    // 0xb40d38: r1 = LoadClassIdInstr(r5)
    //     0xb40d38: ldur            x1, [x5, #-1]
    //     0xb40d3c: ubfx            x1, x1, #0xc, #0x14
    // 0xb40d40: stp             x0, x5, [SP, #8]
    // 0xb40d44: str             x0, [SP]
    // 0xb40d48: mov             x0, x1
    // 0xb40d4c: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xb40d4c: movz            x17, #0x310f
    //     0xb40d50: movk            x17, #0x1, lsl #16
    //     0xb40d54: add             lr, x0, x17
    //     0xb40d58: ldr             lr, [x21, lr, lsl #3]
    //     0xb40d5c: blr             lr
    // 0xb40d60: ldur            x0, [fp, #-0x30]
    // 0xb40d64: add             x4, x0, #1
    // 0xb40d68: b               #0xb40cec
    // 0xb40d6c: r5 = 1
    //     0xb40d6c: movz            x5, #0x1
    // 0xb40d70: ldur            x4, [fp, #-0x18]
    // 0xb40d74: ldur            x2, [fp, #-0x10]
    // 0xb40d78: ldur            x3, [fp, #-0x38]
    // 0xb40d7c: stur            x5, [fp, #-0x50]
    // 0xb40d80: CheckStackOverflow
    //     0xb40d80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40d84: cmp             SP, x16
    //     0xb40d88: b.ls            #0xb41130
    // 0xb40d8c: cmp             x5, x4
    // 0xb40d90: b.ge            #0xb41088
    // 0xb40d94: sub             x6, x5, #1
    // 0xb40d98: stur            x6, [fp, #-0x48]
    // 0xb40d9c: r0 = BoxInt64Instr(r6)
    //     0xb40d9c: sbfiz           x0, x6, #1, #0x1f
    //     0xb40da0: cmp             x6, x0, asr #1
    //     0xb40da4: b.eq            #0xb40db0
    //     0xb40da8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb40dac: stur            x6, [x0, #7]
    // 0xb40db0: stur            x0, [fp, #-8]
    // 0xb40db4: r1 = 1
    //     0xb40db4: movz            x1, #0x1
    // 0xb40db8: stur            x1, [fp, #-0x30]
    // 0xb40dbc: CheckStackOverflow
    //     0xb40dbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40dc0: cmp             SP, x16
    //     0xb40dc4: b.ls            #0xb41138
    // 0xb40dc8: cmp             x1, x2
    // 0xb40dcc: b.ge            #0xb4107c
    // 0xb40dd0: ldur            x16, [fp, #-0x20]
    // 0xb40dd4: stp             x0, x16, [SP]
    // 0xb40dd8: r0 = []()
    //     0xb40dd8: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xb40ddc: mov             x3, x0
    // 0xb40de0: ldur            x2, [fp, #-0x30]
    // 0xb40de4: stur            x3, [fp, #-0x60]
    // 0xb40de8: sub             x4, x2, #1
    // 0xb40dec: r0 = BoxInt64Instr(r4)
    //     0xb40dec: sbfiz           x0, x4, #1, #0x1f
    //     0xb40df0: cmp             x4, x0, asr #1
    //     0xb40df4: b.eq            #0xb40e00
    //     0xb40df8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb40dfc: stur            x4, [x0, #7]
    // 0xb40e00: stur            x0, [fp, #-0x58]
    // 0xb40e04: ldur            x16, [fp, #-0x28]
    // 0xb40e08: stp             x0, x16, [SP]
    // 0xb40e0c: r0 = []()
    //     0xb40e0c: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xb40e10: mov             x1, x0
    // 0xb40e14: ldur            x0, [fp, #-0x60]
    // 0xb40e18: r2 = LoadClassIdInstr(r0)
    //     0xb40e18: ldur            x2, [x0, #-1]
    //     0xb40e1c: ubfx            x2, x2, #0xc, #0x14
    // 0xb40e20: stp             x1, x0, [SP]
    // 0xb40e24: mov             x0, x2
    // 0xb40e28: mov             lr, x0
    // 0xb40e2c: ldr             lr, [x21, lr, lsl #3]
    // 0xb40e30: blr             lr
    // 0xb40e34: tst             x0, #0x10
    // 0xb40e38: cset            x2, ne
    // 0xb40e3c: lsl             x2, x2, #1
    // 0xb40e40: ldur            x0, [fp, #-0x40]
    // 0xb40e44: ldur            x1, [fp, #-0x50]
    // 0xb40e48: stur            x2, [fp, #-0x70]
    // 0xb40e4c: cmp             x1, x0
    // 0xb40e50: b.hs            #0xb41140
    // 0xb40e54: ldur            x4, [fp, #-0x50]
    // 0xb40e58: ldur            x3, [fp, #-0x38]
    // 0xb40e5c: ArrayLoad: r5 = r3[r4]  ; Unknown_4
    //     0xb40e5c: add             x16, x3, x4, lsl #2
    //     0xb40e60: ldur            w5, [x16, #0xf]
    // 0xb40e64: DecompressPointer r5
    //     0xb40e64: add             x5, x5, HEAP, lsl #32
    // 0xb40e68: ldur            x0, [fp, #-0x40]
    // 0xb40e6c: ldur            x1, [fp, #-0x48]
    // 0xb40e70: stur            x5, [fp, #-0x68]
    // 0xb40e74: cmp             x1, x0
    // 0xb40e78: b.hs            #0xb41144
    // 0xb40e7c: ldur            x6, [fp, #-0x48]
    // 0xb40e80: ArrayLoad: r7 = r3[r6]  ; Unknown_4
    //     0xb40e80: add             x16, x3, x6, lsl #2
    //     0xb40e84: ldur            w7, [x16, #0xf]
    // 0xb40e88: DecompressPointer r7
    //     0xb40e88: add             x7, x7, HEAP, lsl #32
    // 0xb40e8c: ldur            x8, [fp, #-0x30]
    // 0xb40e90: r0 = BoxInt64Instr(r8)
    //     0xb40e90: sbfiz           x0, x8, #1, #0x1f
    //     0xb40e94: cmp             x8, x0, asr #1
    //     0xb40e98: b.eq            #0xb40ea4
    //     0xb40e9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb40ea0: stur            x8, [x0, #7]
    // 0xb40ea4: mov             x1, x0
    // 0xb40ea8: stur            x1, [fp, #-0x60]
    // 0xb40eac: r0 = LoadClassIdInstr(r7)
    //     0xb40eac: ldur            x0, [x7, #-1]
    //     0xb40eb0: ubfx            x0, x0, #0xc, #0x14
    // 0xb40eb4: stp             x1, x7, [SP]
    // 0xb40eb8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb40eb8: movz            x17, #0x3037
    //     0xb40ebc: movk            x17, #0x1, lsl #16
    //     0xb40ec0: add             lr, x0, x17
    //     0xb40ec4: ldr             lr, [x21, lr, lsl #3]
    //     0xb40ec8: blr             lr
    // 0xb40ecc: r1 = LoadInt32Instr(r0)
    //     0xb40ecc: sbfx            x1, x0, #1, #0x1f
    //     0xb40ed0: tbz             w0, #0, #0xb40ed8
    //     0xb40ed4: ldur            x1, [x0, #7]
    // 0xb40ed8: add             x2, x1, #1
    // 0xb40edc: ldur            x3, [fp, #-0x50]
    // 0xb40ee0: ldur            x1, [fp, #-0x38]
    // 0xb40ee4: stur            x2, [fp, #-0x78]
    // 0xb40ee8: ArrayLoad: r0 = r1[r3]  ; Unknown_4
    //     0xb40ee8: add             x16, x1, x3, lsl #2
    //     0xb40eec: ldur            w0, [x16, #0xf]
    // 0xb40ef0: DecompressPointer r0
    //     0xb40ef0: add             x0, x0, HEAP, lsl #32
    // 0xb40ef4: r4 = LoadClassIdInstr(r0)
    //     0xb40ef4: ldur            x4, [x0, #-1]
    //     0xb40ef8: ubfx            x4, x4, #0xc, #0x14
    // 0xb40efc: ldur            x16, [fp, #-0x58]
    // 0xb40f00: stp             x16, x0, [SP]
    // 0xb40f04: mov             x0, x4
    // 0xb40f08: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb40f08: movz            x17, #0x3037
    //     0xb40f0c: movk            x17, #0x1, lsl #16
    //     0xb40f10: add             lr, x0, x17
    //     0xb40f14: ldr             lr, [x21, lr, lsl #3]
    //     0xb40f18: blr             lr
    // 0xb40f1c: r1 = LoadInt32Instr(r0)
    //     0xb40f1c: sbfx            x1, x0, #1, #0x1f
    //     0xb40f20: tbz             w0, #0, #0xb40f28
    //     0xb40f24: ldur            x1, [x0, #7]
    // 0xb40f28: add             x2, x1, #1
    // 0xb40f2c: ldur            x3, [fp, #-0x48]
    // 0xb40f30: ldur            x1, [fp, #-0x38]
    // 0xb40f34: stur            x2, [fp, #-0x80]
    // 0xb40f38: ArrayLoad: r0 = r1[r3]  ; Unknown_4
    //     0xb40f38: add             x16, x1, x3, lsl #2
    //     0xb40f3c: ldur            w0, [x16, #0xf]
    // 0xb40f40: DecompressPointer r0
    //     0xb40f40: add             x0, x0, HEAP, lsl #32
    // 0xb40f44: r4 = LoadClassIdInstr(r0)
    //     0xb40f44: ldur            x4, [x0, #-1]
    //     0xb40f48: ubfx            x4, x4, #0xc, #0x14
    // 0xb40f4c: ldur            x16, [fp, #-0x58]
    // 0xb40f50: stp             x16, x0, [SP]
    // 0xb40f54: mov             x0, x4
    // 0xb40f58: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb40f58: movz            x17, #0x3037
    //     0xb40f5c: movk            x17, #0x1, lsl #16
    //     0xb40f60: add             lr, x0, x17
    //     0xb40f64: ldr             lr, [x21, lr, lsl #3]
    //     0xb40f68: blr             lr
    // 0xb40f6c: mov             x1, x0
    // 0xb40f70: ldur            x0, [fp, #-0x70]
    // 0xb40f74: r2 = LoadInt32Instr(r0)
    //     0xb40f74: sbfx            x2, x0, #1, #0x1f
    // 0xb40f78: r0 = LoadInt32Instr(r1)
    //     0xb40f78: sbfx            x0, x1, #1, #0x1f
    //     0xb40f7c: tbz             w1, #0, #0xb40f84
    //     0xb40f80: ldur            x0, [x1, #7]
    // 0xb40f84: add             x3, x0, x2
    // 0xb40f88: ldur            x2, [fp, #-0x78]
    // 0xb40f8c: stur            x3, [fp, #-0x88]
    // 0xb40f90: r0 = BoxInt64Instr(r2)
    //     0xb40f90: sbfiz           x0, x2, #1, #0x1f
    //     0xb40f94: cmp             x2, x0, asr #1
    //     0xb40f98: b.eq            #0xb40fa4
    //     0xb40f9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb40fa0: stur            x2, [x0, #7]
    // 0xb40fa4: r1 = Null
    //     0xb40fa4: mov             x1, NULL
    // 0xb40fa8: r2 = 6
    //     0xb40fa8: movz            x2, #0x6
    // 0xb40fac: stur            x0, [fp, #-0x58]
    // 0xb40fb0: r0 = AllocateArray()
    //     0xb40fb0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb40fb4: mov             x2, x0
    // 0xb40fb8: ldur            x0, [fp, #-0x58]
    // 0xb40fbc: stur            x2, [fp, #-0x70]
    // 0xb40fc0: StoreField: r2->field_f = r0
    //     0xb40fc0: stur            w0, [x2, #0xf]
    // 0xb40fc4: ldur            x3, [fp, #-0x80]
    // 0xb40fc8: r0 = BoxInt64Instr(r3)
    //     0xb40fc8: sbfiz           x0, x3, #1, #0x1f
    //     0xb40fcc: cmp             x3, x0, asr #1
    //     0xb40fd0: b.eq            #0xb40fdc
    //     0xb40fd4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb40fd8: stur            x3, [x0, #7]
    // 0xb40fdc: StoreField: r2->field_13 = r0
    //     0xb40fdc: stur            w0, [x2, #0x13]
    // 0xb40fe0: ldur            x3, [fp, #-0x88]
    // 0xb40fe4: r0 = BoxInt64Instr(r3)
    //     0xb40fe4: sbfiz           x0, x3, #1, #0x1f
    //     0xb40fe8: cmp             x3, x0, asr #1
    //     0xb40fec: b.eq            #0xb40ff8
    //     0xb40ff0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb40ff4: stur            x3, [x0, #7]
    // 0xb40ff8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb40ff8: stur            w0, [x2, #0x17]
    // 0xb40ffc: r1 = <int>
    //     0xb40ffc: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb41000: r0 = AllocateGrowableArray()
    //     0xb41000: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb41004: mov             x1, x0
    // 0xb41008: ldur            x0, [fp, #-0x70]
    // 0xb4100c: StoreField: r1->field_f = r0
    //     0xb4100c: stur            w0, [x1, #0xf]
    // 0xb41010: r0 = 6
    //     0xb41010: movz            x0, #0x6
    // 0xb41014: StoreField: r1->field_b = r0
    //     0xb41014: stur            w0, [x1, #0xb]
    // 0xb41018: r2 = Closure: (int, int) => int from Function 'min': static.
    //     0xb41018: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ad58] Closure: (int, int) => int from Function 'min': static. (0x7e54fb03482c)
    //     0xb4101c: ldr             x2, [x2, #0xd58]
    // 0xb41020: r0 = reduce()
    //     0xb41020: bl              #0x8a5ec4  ; [dart:collection] ListBase::reduce
    // 0xb41024: mov             x1, x0
    // 0xb41028: ldur            x0, [fp, #-0x68]
    // 0xb4102c: r2 = LoadClassIdInstr(r0)
    //     0xb4102c: ldur            x2, [x0, #-1]
    //     0xb41030: ubfx            x2, x2, #0xc, #0x14
    // 0xb41034: ldur            x16, [fp, #-0x60]
    // 0xb41038: stp             x16, x0, [SP, #8]
    // 0xb4103c: str             x1, [SP]
    // 0xb41040: mov             x0, x2
    // 0xb41044: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xb41044: movz            x17, #0x310f
    //     0xb41048: movk            x17, #0x1, lsl #16
    //     0xb4104c: add             lr, x0, x17
    //     0xb41050: ldr             lr, [x21, lr, lsl #3]
    //     0xb41054: blr             lr
    // 0xb41058: ldur            x0, [fp, #-0x30]
    // 0xb4105c: add             x1, x0, #1
    // 0xb41060: ldur            x4, [fp, #-0x18]
    // 0xb41064: ldur            x2, [fp, #-0x10]
    // 0xb41068: ldur            x5, [fp, #-0x50]
    // 0xb4106c: ldur            x6, [fp, #-0x48]
    // 0xb41070: ldur            x3, [fp, #-0x38]
    // 0xb41074: ldur            x0, [fp, #-8]
    // 0xb41078: b               #0xb40db8
    // 0xb4107c: mov             x0, x5
    // 0xb41080: add             x5, x0, #1
    // 0xb41084: b               #0xb40d70
    // 0xb41088: mov             x0, x4
    // 0xb4108c: mov             x16, x3
    // 0xb41090: mov             x3, x2
    // 0xb41094: mov             x2, x16
    // 0xb41098: sub             x4, x0, #1
    // 0xb4109c: ldur            x0, [fp, #-0x40]
    // 0xb410a0: mov             x1, x4
    // 0xb410a4: cmp             x1, x0
    // 0xb410a8: b.hs            #0xb41148
    // 0xb410ac: ArrayLoad: r0 = r2[r4]  ; Unknown_4
    //     0xb410ac: add             x16, x2, x4, lsl #2
    //     0xb410b0: ldur            w0, [x16, #0xf]
    // 0xb410b4: DecompressPointer r0
    //     0xb410b4: add             x0, x0, HEAP, lsl #32
    // 0xb410b8: sub             x1, x3, #1
    // 0xb410bc: lsl             x2, x1, #1
    // 0xb410c0: r1 = LoadClassIdInstr(r0)
    //     0xb410c0: ldur            x1, [x0, #-1]
    //     0xb410c4: ubfx            x1, x1, #0xc, #0x14
    // 0xb410c8: stp             x2, x0, [SP]
    // 0xb410cc: mov             x0, x1
    // 0xb410d0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb410d0: movz            x17, #0x3037
    //     0xb410d4: movk            x17, #0x1, lsl #16
    //     0xb410d8: add             lr, x0, x17
    //     0xb410dc: ldr             lr, [x21, lr, lsl #3]
    //     0xb410e0: blr             lr
    // 0xb410e4: r1 = LoadInt32Instr(r0)
    //     0xb410e4: sbfx            x1, x0, #1, #0x1f
    //     0xb410e8: tbz             w0, #0, #0xb410f0
    //     0xb410ec: ldur            x1, [x0, #7]
    // 0xb410f0: mov             x0, x1
    // 0xb410f4: LeaveFrame
    //     0xb410f4: mov             SP, fp
    //     0xb410f8: ldp             fp, lr, [SP], #0x10
    // 0xb410fc: ret
    //     0xb410fc: ret             
    // 0xb41100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb41100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb41104: b               #0xb40b7c
    // 0xb41108: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb41108: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4110c: b               #0xb40be0
    // 0xb41110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb41110: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb41114: b               #0xb40c08
    // 0xb41118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb41118: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4111c: b               #0xb40c88
    // 0xb41120: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb41120: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb41124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb41124: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb41128: b               #0xb40d04
    // 0xb4112c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb4112c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb41130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb41130: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb41134: b               #0xb40d8c
    // 0xb41138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb41138: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4113c: b               #0xb40dc8
    // 0xb41140: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb41140: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb41144: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb41144: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb41148: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb41148: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static double <anonymous closure>(dynamic, String, double) {
    // ** addr: 0xb4114c, size: 0xc8
    // 0xb4114c: EnterFrame
    //     0xb4114c: stp             fp, lr, [SP, #-0x10]!
    //     0xb41150: mov             fp, SP
    // 0xb41154: AllocStack(0x8)
    //     0xb41154: sub             SP, SP, #8
    // 0xb41158: SetupParameters()
    //     0xb41158: eor             v0.16b, v0.16b, v0.16b
    //     0xb4115c: ldr             x0, [fp, #0x20]
    //     0xb41160: ldur            w3, [x0, #0x17]
    //     0xb41164: add             x3, x3, HEAP, lsl #32
    //     0xb41168: stur            x3, [fp, #-8]
    // 0xb41158: d0 = 0.000000
    // 0xb4116c: CheckStackOverflow
    //     0xb4116c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb41170: cmp             SP, x16
    //     0xb41174: b.ls            #0xb411fc
    // 0xb41178: ldr             x0, [fp, #0x10]
    // 0xb4117c: LoadField: d1 = r0->field_7
    //     0xb4117c: ldur            d1, [x0, #7]
    // 0xb41180: fcmp            d1, d0
    // 0xb41184: b.ne            #0xb411c4
    // 0xb41188: LoadField: r1 = r3->field_f
    //     0xb41188: ldur            w1, [x3, #0xf]
    // 0xb4118c: DecompressPointer r1
    //     0xb4118c: add             x1, x1, HEAP, lsl #32
    // 0xb41190: ldr             x2, [fp, #0x18]
    // 0xb41194: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb41194: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb41198: r0 = indexOf()
    //     0xb41198: bl              #0x6ec5f4  ; [dart:collection] ListBase::indexOf
    // 0xb4119c: mov             x1, x0
    // 0xb411a0: ldur            x0, [fp, #-8]
    // 0xb411a4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb411a4: ldur            w2, [x0, #0x17]
    // 0xb411a8: DecompressPointer r2
    //     0xb411a8: add             x2, x2, HEAP, lsl #32
    // 0xb411ac: r0 = LoadInt32Instr(r1)
    //     0xb411ac: sbfx            x0, x1, #1, #0x1f
    //     0xb411b0: tbz             w1, #0, #0xb411b8
    //     0xb411b4: ldur            x0, [x1, #7]
    // 0xb411b8: mov             x1, x0
    // 0xb411bc: r0 = _getGratterSimilarity2()
    //     0xb411bc: bl              #0xb41214  ; [package:smart_text_search/smart_text_search/cos_similarity.dart] ::_getGratterSimilarity2
    // 0xb411c0: b               #0xb411c8
    // 0xb411c4: mov             v0.16b, v1.16b
    // 0xb411c8: r0 = inline_Allocate_Double()
    //     0xb411c8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb411cc: add             x0, x0, #0x10
    //     0xb411d0: cmp             x1, x0
    //     0xb411d4: b.ls            #0xb41204
    //     0xb411d8: str             x0, [THR, #0x50]  ; THR::top
    //     0xb411dc: sub             x0, x0, #0xf
    //     0xb411e0: movz            x1, #0xe15c
    //     0xb411e4: movk            x1, #0x3, lsl #16
    //     0xb411e8: stur            x1, [x0, #-1]
    // 0xb411ec: StoreField: r0->field_7 = d0
    //     0xb411ec: stur            d0, [x0, #7]
    // 0xb411f0: LeaveFrame
    //     0xb411f0: mov             SP, fp
    //     0xb411f4: ldp             fp, lr, [SP], #0x10
    // 0xb411f8: ret
    //     0xb411f8: ret             
    // 0xb411fc: r0 = StackOverflowSharedWithFPURegs()
    //     0xb411fc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xb41200: b               #0xb41178
    // 0xb41204: SaveReg d0
    //     0xb41204: str             q0, [SP, #-0x10]!
    // 0xb41208: r0 = AllocateDouble()
    //     0xb41208: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb4120c: RestoreReg d0
    //     0xb4120c: ldr             q0, [SP], #0x10
    // 0xb41210: b               #0xb411ec
  }
  static _ _getGratterSimilarity2(/* No info */) {
    // ** addr: 0xb41214, size: 0x184
    // 0xb41214: EnterFrame
    //     0xb41214: stp             fp, lr, [SP, #-0x10]!
    //     0xb41218: mov             fp, SP
    // 0xb4121c: AllocStack(0x30)
    //     0xb4121c: sub             SP, SP, #0x30
    // 0xb41220: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb41220: mov             x3, x1
    //     0xb41224: stur            x1, [fp, #-8]
    //     0xb41228: stur            x2, [fp, #-0x10]
    // 0xb4122c: CheckStackOverflow
    //     0xb4122c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb41230: cmp             SP, x16
    //     0xb41234: b.ls            #0xb41380
    // 0xb41238: LoadField: r0 = r2->field_b
    //     0xb41238: ldur            w0, [x2, #0xb]
    // 0xb4123c: r1 = LoadInt32Instr(r0)
    //     0xb4123c: sbfx            x1, x0, #1, #0x1f
    // 0xb41240: mov             x0, x1
    // 0xb41244: mov             x1, x3
    // 0xb41248: cmp             x1, x0
    // 0xb4124c: b.hs            #0xb41388
    // 0xb41250: LoadField: r0 = r2->field_f
    //     0xb41250: ldur            w0, [x2, #0xf]
    // 0xb41254: DecompressPointer r0
    //     0xb41254: add             x0, x0, HEAP, lsl #32
    // 0xb41258: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb41258: add             x16, x0, x3, lsl #2
    //     0xb4125c: ldur            w1, [x16, #0xf]
    // 0xb41260: DecompressPointer r1
    //     0xb41260: add             x1, x1, HEAP, lsl #32
    // 0xb41264: r0 = LoadClassIdInstr(r1)
    //     0xb41264: ldur            x0, [x1, #-1]
    //     0xb41268: ubfx            x0, x0, #0xc, #0x14
    // 0xb4126c: stp             xzr, x1, [SP]
    // 0xb41270: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb41270: movz            x17, #0x3037
    //     0xb41274: movk            x17, #0x1, lsl #16
    //     0xb41278: add             lr, x0, x17
    //     0xb4127c: ldr             lr, [x21, lr, lsl #3]
    //     0xb41280: blr             lr
    // 0xb41284: mov             x3, x0
    // 0xb41288: ldur            x2, [fp, #-0x10]
    // 0xb4128c: stur            x3, [fp, #-0x18]
    // 0xb41290: LoadField: r0 = r2->field_b
    //     0xb41290: ldur            w0, [x2, #0xb]
    // 0xb41294: r1 = LoadInt32Instr(r0)
    //     0xb41294: sbfx            x1, x0, #1, #0x1f
    // 0xb41298: mov             x0, x1
    // 0xb4129c: ldur            x1, [fp, #-8]
    // 0xb412a0: cmp             x1, x0
    // 0xb412a4: b.hs            #0xb4138c
    // 0xb412a8: LoadField: r0 = r2->field_f
    //     0xb412a8: ldur            w0, [x2, #0xf]
    // 0xb412ac: DecompressPointer r0
    //     0xb412ac: add             x0, x0, HEAP, lsl #32
    // 0xb412b0: ldur            x1, [fp, #-8]
    // 0xb412b4: ArrayLoad: r2 = r0[r1]  ; Unknown_4
    //     0xb412b4: add             x16, x0, x1, lsl #2
    //     0xb412b8: ldur            w2, [x16, #0xf]
    // 0xb412bc: DecompressPointer r2
    //     0xb412bc: add             x2, x2, HEAP, lsl #32
    // 0xb412c0: r0 = LoadClassIdInstr(r2)
    //     0xb412c0: ldur            x0, [x2, #-1]
    //     0xb412c4: ubfx            x0, x0, #0xc, #0x14
    // 0xb412c8: mov             x1, x2
    // 0xb412cc: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xb412cc: movz            x17, #0xd35d
    //     0xb412d0: add             lr, x0, x17
    //     0xb412d4: ldr             lr, [x21, lr, lsl #3]
    //     0xb412d8: blr             lr
    // 0xb412dc: mov             x2, x0
    // 0xb412e0: ldur            x0, [fp, #-0x18]
    // 0xb412e4: stur            x2, [fp, #-0x10]
    // 0xb412e8: LoadField: d0 = r0->field_7
    //     0xb412e8: ldur            d0, [x0, #7]
    // 0xb412ec: stur            d0, [fp, #-0x20]
    // 0xb412f0: CheckStackOverflow
    //     0xb412f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb412f4: cmp             SP, x16
    //     0xb412f8: b.ls            #0xb41390
    // 0xb412fc: r0 = LoadClassIdInstr(r2)
    //     0xb412fc: ldur            x0, [x2, #-1]
    //     0xb41300: ubfx            x0, x0, #0xc, #0x14
    // 0xb41304: mov             x1, x2
    // 0xb41308: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xb41308: movz            x17, #0x292d
    //     0xb4130c: movk            x17, #0x1, lsl #16
    //     0xb41310: add             lr, x0, x17
    //     0xb41314: ldr             lr, [x21, lr, lsl #3]
    //     0xb41318: blr             lr
    // 0xb4131c: tbnz            w0, #4, #0xb4136c
    // 0xb41320: ldur            x2, [fp, #-0x10]
    // 0xb41324: ldur            d0, [fp, #-0x20]
    // 0xb41328: r0 = LoadClassIdInstr(r2)
    //     0xb41328: ldur            x0, [x2, #-1]
    //     0xb4132c: ubfx            x0, x0, #0xc, #0x14
    // 0xb41330: mov             x1, x2
    // 0xb41334: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xb41334: movz            x17, #0x384d
    //     0xb41338: movk            x17, #0x1, lsl #16
    //     0xb4133c: add             lr, x0, x17
    //     0xb41340: ldr             lr, [x21, lr, lsl #3]
    //     0xb41344: blr             lr
    // 0xb41348: LoadField: d1 = r0->field_7
    //     0xb41348: ldur            d1, [x0, #7]
    // 0xb4134c: ldur            d2, [fp, #-0x20]
    // 0xb41350: fcmp            d1, d2
    // 0xb41354: b.le            #0xb41360
    // 0xb41358: mov             v0.16b, v1.16b
    // 0xb4135c: b               #0xb41364
    // 0xb41360: mov             v0.16b, v2.16b
    // 0xb41364: ldur            x2, [fp, #-0x10]
    // 0xb41368: b               #0xb412ec
    // 0xb4136c: ldur            d2, [fp, #-0x20]
    // 0xb41370: mov             v0.16b, v2.16b
    // 0xb41374: LeaveFrame
    //     0xb41374: mov             SP, fp
    //     0xb41378: ldp             fp, lr, [SP], #0x10
    // 0xb4137c: ret
    //     0xb4137c: ret             
    // 0xb41380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb41380: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb41384: b               #0xb41238
    // 0xb41388: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb41388: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb4138c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb4138c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb41390: r0 = StackOverflowSharedWithFPURegs()
    //     0xb41390: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xb41394: b               #0xb412fc
  }
  [closure] static double <anonymous closure>(dynamic, String, double) {
    // ** addr: 0xb41398, size: 0xc8
    // 0xb41398: EnterFrame
    //     0xb41398: stp             fp, lr, [SP, #-0x10]!
    //     0xb4139c: mov             fp, SP
    // 0xb413a0: AllocStack(0x8)
    //     0xb413a0: sub             SP, SP, #8
    // 0xb413a4: SetupParameters()
    //     0xb413a4: eor             v0.16b, v0.16b, v0.16b
    //     0xb413a8: ldr             x0, [fp, #0x20]
    //     0xb413ac: ldur            w3, [x0, #0x17]
    //     0xb413b0: add             x3, x3, HEAP, lsl #32
    //     0xb413b4: stur            x3, [fp, #-8]
    // 0xb413a4: d0 = 0.000000
    // 0xb413b8: CheckStackOverflow
    //     0xb413b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb413bc: cmp             SP, x16
    //     0xb413c0: b.ls            #0xb41448
    // 0xb413c4: ldr             x0, [fp, #0x10]
    // 0xb413c8: LoadField: d1 = r0->field_7
    //     0xb413c8: ldur            d1, [x0, #7]
    // 0xb413cc: fcmp            d1, d0
    // 0xb413d0: b.ne            #0xb41410
    // 0xb413d4: LoadField: r1 = r3->field_13
    //     0xb413d4: ldur            w1, [x3, #0x13]
    // 0xb413d8: DecompressPointer r1
    //     0xb413d8: add             x1, x1, HEAP, lsl #32
    // 0xb413dc: ldr             x2, [fp, #0x18]
    // 0xb413e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb413e0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb413e4: r0 = indexOf()
    //     0xb413e4: bl              #0x6ec5f4  ; [dart:collection] ListBase::indexOf
    // 0xb413e8: mov             x1, x0
    // 0xb413ec: ldur            x0, [fp, #-8]
    // 0xb413f0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb413f0: ldur            w2, [x0, #0x17]
    // 0xb413f4: DecompressPointer r2
    //     0xb413f4: add             x2, x2, HEAP, lsl #32
    // 0xb413f8: r0 = LoadInt32Instr(r1)
    //     0xb413f8: sbfx            x0, x1, #1, #0x1f
    //     0xb413fc: tbz             w1, #0, #0xb41404
    //     0xb41400: ldur            x0, [x1, #7]
    // 0xb41404: mov             x1, x0
    // 0xb41408: r0 = _getGratterSimilarity1()
    //     0xb41408: bl              #0xb41460  ; [package:smart_text_search/smart_text_search/cos_similarity.dart] ::_getGratterSimilarity1
    // 0xb4140c: b               #0xb41414
    // 0xb41410: mov             v0.16b, v1.16b
    // 0xb41414: r0 = inline_Allocate_Double()
    //     0xb41414: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb41418: add             x0, x0, #0x10
    //     0xb4141c: cmp             x1, x0
    //     0xb41420: b.ls            #0xb41450
    //     0xb41424: str             x0, [THR, #0x50]  ; THR::top
    //     0xb41428: sub             x0, x0, #0xf
    //     0xb4142c: movz            x1, #0xe15c
    //     0xb41430: movk            x1, #0x3, lsl #16
    //     0xb41434: stur            x1, [x0, #-1]
    // 0xb41438: StoreField: r0->field_7 = d0
    //     0xb41438: stur            d0, [x0, #7]
    // 0xb4143c: LeaveFrame
    //     0xb4143c: mov             SP, fp
    //     0xb41440: ldp             fp, lr, [SP], #0x10
    // 0xb41444: ret
    //     0xb41444: ret             
    // 0xb41448: r0 = StackOverflowSharedWithFPURegs()
    //     0xb41448: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xb4144c: b               #0xb413c4
    // 0xb41450: SaveReg d0
    //     0xb41450: str             q0, [SP, #-0x10]!
    // 0xb41454: r0 = AllocateDouble()
    //     0xb41454: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb41458: RestoreReg d0
    //     0xb41458: ldr             q0, [SP], #0x10
    // 0xb4145c: b               #0xb41438
  }
  static _ _getGratterSimilarity1(/* No info */) {
    // ** addr: 0xb41460, size: 0x1a8
    // 0xb41460: EnterFrame
    //     0xb41460: stp             fp, lr, [SP, #-0x10]!
    //     0xb41464: mov             fp, SP
    // 0xb41468: AllocStack(0x40)
    //     0xb41468: sub             SP, SP, #0x40
    // 0xb4146c: SetupParameters(dynamic _ /* r1 => r3 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb4146c: mov             x3, x1
    //     0xb41470: stur            x2, [fp, #-0x10]
    // 0xb41474: CheckStackOverflow
    //     0xb41474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb41478: cmp             SP, x16
    //     0xb4147c: b.ls            #0xb415f4
    // 0xb41480: LoadField: r0 = r2->field_b
    //     0xb41480: ldur            w0, [x2, #0xb]
    // 0xb41484: r1 = LoadInt32Instr(r0)
    //     0xb41484: sbfx            x1, x0, #1, #0x1f
    // 0xb41488: mov             x0, x1
    // 0xb4148c: r1 = 0
    //     0xb4148c: movz            x1, #0
    // 0xb41490: cmp             x1, x0
    // 0xb41494: b.hs            #0xb415fc
    // 0xb41498: LoadField: r0 = r2->field_f
    //     0xb41498: ldur            w0, [x2, #0xf]
    // 0xb4149c: DecompressPointer r0
    //     0xb4149c: add             x0, x0, HEAP, lsl #32
    // 0xb414a0: LoadField: r4 = r0->field_f
    //     0xb414a0: ldur            w4, [x0, #0xf]
    // 0xb414a4: DecompressPointer r4
    //     0xb414a4: add             x4, x4, HEAP, lsl #32
    // 0xb414a8: r0 = BoxInt64Instr(r3)
    //     0xb414a8: sbfiz           x0, x3, #1, #0x1f
    //     0xb414ac: cmp             x3, x0, asr #1
    //     0xb414b0: b.eq            #0xb414bc
    //     0xb414b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb414b8: stur            x3, [x0, #7]
    // 0xb414bc: mov             x1, x0
    // 0xb414c0: stur            x1, [fp, #-8]
    // 0xb414c4: r0 = LoadClassIdInstr(r4)
    //     0xb414c4: ldur            x0, [x4, #-1]
    //     0xb414c8: ubfx            x0, x0, #0xc, #0x14
    // 0xb414cc: stp             x1, x4, [SP]
    // 0xb414d0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb414d0: movz            x17, #0x3037
    //     0xb414d4: movk            x17, #0x1, lsl #16
    //     0xb414d8: add             lr, x0, x17
    //     0xb414dc: ldr             lr, [x21, lr, lsl #3]
    //     0xb414e0: blr             lr
    // 0xb414e4: ldur            x1, [fp, #-0x10]
    // 0xb414e8: LoadField: r2 = r1->field_b
    //     0xb414e8: ldur            w2, [x1, #0xb]
    // 0xb414ec: r3 = LoadInt32Instr(r2)
    //     0xb414ec: sbfx            x3, x2, #1, #0x1f
    // 0xb414f0: stur            x3, [fp, #-0x28]
    // 0xb414f4: LoadField: d0 = r0->field_7
    //     0xb414f4: ldur            d0, [x0, #7]
    // 0xb414f8: r0 = 0
    //     0xb414f8: movz            x0, #0
    // 0xb414fc: stur            d0, [fp, #-0x30]
    // 0xb41500: CheckStackOverflow
    //     0xb41500: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb41504: cmp             SP, x16
    //     0xb41508: b.ls            #0xb41600
    // 0xb4150c: LoadField: r2 = r1->field_b
    //     0xb4150c: ldur            w2, [x1, #0xb]
    // 0xb41510: r4 = LoadInt32Instr(r2)
    //     0xb41510: sbfx            x4, x2, #1, #0x1f
    // 0xb41514: cmp             x3, x4
    // 0xb41518: b.ne            #0xb415d4
    // 0xb4151c: cmp             x0, x4
    // 0xb41520: b.ge            #0xb415c0
    // 0xb41524: LoadField: r2 = r1->field_f
    //     0xb41524: ldur            w2, [x1, #0xf]
    // 0xb41528: DecompressPointer r2
    //     0xb41528: add             x2, x2, HEAP, lsl #32
    // 0xb4152c: ArrayLoad: r4 = r2[r0]  ; Unknown_4
    //     0xb4152c: add             x16, x2, x0, lsl #2
    //     0xb41530: ldur            w4, [x16, #0xf]
    // 0xb41534: DecompressPointer r4
    //     0xb41534: add             x4, x4, HEAP, lsl #32
    // 0xb41538: stur            x4, [fp, #-0x20]
    // 0xb4153c: add             x2, x0, #1
    // 0xb41540: stur            x2, [fp, #-0x18]
    // 0xb41544: r0 = LoadClassIdInstr(r4)
    //     0xb41544: ldur            x0, [x4, #-1]
    //     0xb41548: ubfx            x0, x0, #0xc, #0x14
    // 0xb4154c: ldur            x16, [fp, #-8]
    // 0xb41550: stp             x16, x4, [SP]
    // 0xb41554: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb41554: movz            x17, #0x3037
    //     0xb41558: movk            x17, #0x1, lsl #16
    //     0xb4155c: add             lr, x0, x17
    //     0xb41560: ldr             lr, [x21, lr, lsl #3]
    //     0xb41564: blr             lr
    // 0xb41568: LoadField: d0 = r0->field_7
    //     0xb41568: ldur            d0, [x0, #7]
    // 0xb4156c: ldur            d1, [fp, #-0x30]
    // 0xb41570: fcmp            d0, d1
    // 0xb41574: b.le            #0xb415ac
    // 0xb41578: ldur            x0, [fp, #-0x20]
    // 0xb4157c: r1 = LoadClassIdInstr(r0)
    //     0xb4157c: ldur            x1, [x0, #-1]
    //     0xb41580: ubfx            x1, x1, #0xc, #0x14
    // 0xb41584: ldur            x16, [fp, #-8]
    // 0xb41588: stp             x16, x0, [SP]
    // 0xb4158c: mov             x0, x1
    // 0xb41590: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb41590: movz            x17, #0x3037
    //     0xb41594: movk            x17, #0x1, lsl #16
    //     0xb41598: add             lr, x0, x17
    //     0xb4159c: ldr             lr, [x21, lr, lsl #3]
    //     0xb415a0: blr             lr
    // 0xb415a4: LoadField: d0 = r0->field_7
    //     0xb415a4: ldur            d0, [x0, #7]
    // 0xb415a8: b               #0xb415b0
    // 0xb415ac: mov             v0.16b, v1.16b
    // 0xb415b0: ldur            x0, [fp, #-0x18]
    // 0xb415b4: ldur            x1, [fp, #-0x10]
    // 0xb415b8: ldur            x3, [fp, #-0x28]
    // 0xb415bc: b               #0xb414fc
    // 0xb415c0: mov             v1.16b, v0.16b
    // 0xb415c4: mov             v0.16b, v1.16b
    // 0xb415c8: LeaveFrame
    //     0xb415c8: mov             SP, fp
    //     0xb415cc: ldp             fp, lr, [SP], #0x10
    // 0xb415d0: ret
    //     0xb415d0: ret             
    // 0xb415d4: mov             x0, x1
    // 0xb415d8: r0 = ConcurrentModificationError()
    //     0xb415d8: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xb415dc: mov             x1, x0
    // 0xb415e0: ldur            x0, [fp, #-0x10]
    // 0xb415e4: StoreField: r1->field_b = r0
    //     0xb415e4: stur            w0, [x1, #0xb]
    // 0xb415e8: mov             x0, x1
    // 0xb415ec: r0 = Throw()
    //     0xb415ec: bl              #0xec04b8  ; ThrowStub
    // 0xb415f0: brk             #0
    // 0xb415f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb415f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb415f8: b               #0xb41480
    // 0xb415fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb415fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb41600: r0 = StackOverflowSharedWithFPURegs()
    //     0xb41600: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xb41604: b               #0xb4150c
  }
}
