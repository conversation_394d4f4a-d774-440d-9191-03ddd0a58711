// lib: , url: package:source_span/src/file.dart

// class id: 1051137, size: 0x8
class :: {
}

// class id: 493, size: 0x1c, field offset: 0x8
class _FileSpan extends SourceSpanMixin
    implements FileSpan {

  _ compareTo(/* No info */) {
    // ** addr: 0x6d634c, size: 0x134
    // 0x6d634c: EnterFrame
    //     0x6d634c: stp             fp, lr, [SP, #-0x10]!
    //     0x6d6350: mov             fp, SP
    // 0x6d6354: AllocStack(0x10)
    //     0x6d6354: sub             SP, SP, #0x10
    // 0x6d6358: SetupParameters(_FileSpan this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x6d6358: mov             x4, x1
    //     0x6d635c: mov             x3, x2
    //     0x6d6360: stur            x1, [fp, #-8]
    //     0x6d6364: stur            x2, [fp, #-0x10]
    // 0x6d6368: CheckStackOverflow
    //     0x6d6368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6d636c: cmp             SP, x16
    //     0x6d6370: b.ls            #0x6d6478
    // 0x6d6374: mov             x0, x3
    // 0x6d6378: r2 = Null
    //     0x6d6378: mov             x2, NULL
    // 0x6d637c: r1 = Null
    //     0x6d637c: mov             x1, NULL
    // 0x6d6380: r4 = 60
    //     0x6d6380: movz            x4, #0x3c
    // 0x6d6384: branchIfSmi(r0, 0x6d6390)
    //     0x6d6384: tbz             w0, #0, #0x6d6390
    // 0x6d6388: r4 = LoadClassIdInstr(r0)
    //     0x6d6388: ldur            x4, [x0, #-1]
    //     0x6d638c: ubfx            x4, x4, #0xc, #0x14
    // 0x6d6390: sub             x4, x4, #0x1ed
    // 0x6d6394: cmp             x4, #2
    // 0x6d6398: b.ls            #0x6d63b0
    // 0x6d639c: r8 = SourceSpan
    //     0x6d639c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c548] Type: SourceSpan
    //     0x6d63a0: ldr             x8, [x8, #0x548]
    // 0x6d63a4: r3 = Null
    //     0x6d63a4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c7c8] Null
    //     0x6d63a8: ldr             x3, [x3, #0x7c8]
    // 0x6d63ac: r0 = DefaultTypeTest()
    //     0x6d63ac: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x6d63b0: ldur            x3, [fp, #-0x10]
    // 0x6d63b4: r0 = LoadClassIdInstr(r3)
    //     0x6d63b4: ldur            x0, [x3, #-1]
    //     0x6d63b8: ubfx            x0, x0, #0xc, #0x14
    // 0x6d63bc: cmp             x0, #0x1ed
    // 0x6d63c0: b.eq            #0x6d63dc
    // 0x6d63c4: ldur            x1, [fp, #-8]
    // 0x6d63c8: mov             x2, x3
    // 0x6d63cc: r0 = compareTo()
    //     0x6d63cc: bl              #0x6d6000  ; [package:source_span/src/span_mixin.dart] SourceSpanMixin::compareTo
    // 0x6d63d0: LeaveFrame
    //     0x6d63d0: mov             SP, fp
    //     0x6d63d4: ldp             fp, lr, [SP], #0x10
    // 0x6d63d8: ret
    //     0x6d63d8: ret             
    // 0x6d63dc: ldur            x4, [fp, #-8]
    // 0x6d63e0: LoadField: r2 = r4->field_b
    //     0x6d63e0: ldur            x2, [x4, #0xb]
    // 0x6d63e4: LoadField: r5 = r3->field_b
    //     0x6d63e4: ldur            x5, [x3, #0xb]
    // 0x6d63e8: r0 = BoxInt64Instr(r2)
    //     0x6d63e8: sbfiz           x0, x2, #1, #0x1f
    //     0x6d63ec: cmp             x2, x0, asr #1
    //     0x6d63f0: b.eq            #0x6d63fc
    //     0x6d63f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6d63f8: stur            x2, [x0, #7]
    // 0x6d63fc: mov             x2, x0
    // 0x6d6400: r0 = BoxInt64Instr(r5)
    //     0x6d6400: sbfiz           x0, x5, #1, #0x1f
    //     0x6d6404: cmp             x5, x0, asr #1
    //     0x6d6408: b.eq            #0x6d6414
    //     0x6d640c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6d6410: stur            x5, [x0, #7]
    // 0x6d6414: mov             x1, x2
    // 0x6d6418: mov             x2, x0
    // 0x6d641c: r0 = compareTo()
    //     0x6d641c: bl              #0x6daaec  ; [dart:core] _IntegerImplementation::compareTo
    // 0x6d6420: cbnz            x0, #0x6d646c
    // 0x6d6424: ldur            x1, [fp, #-8]
    // 0x6d6428: ldur            x0, [fp, #-0x10]
    // 0x6d642c: LoadField: r2 = r1->field_13
    //     0x6d642c: ldur            x2, [x1, #0x13]
    // 0x6d6430: LoadField: r3 = r0->field_13
    //     0x6d6430: ldur            x3, [x0, #0x13]
    // 0x6d6434: r0 = BoxInt64Instr(r2)
    //     0x6d6434: sbfiz           x0, x2, #1, #0x1f
    //     0x6d6438: cmp             x2, x0, asr #1
    //     0x6d643c: b.eq            #0x6d6448
    //     0x6d6440: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6d6444: stur            x2, [x0, #7]
    // 0x6d6448: mov             x2, x0
    // 0x6d644c: r0 = BoxInt64Instr(r3)
    //     0x6d644c: sbfiz           x0, x3, #1, #0x1f
    //     0x6d6450: cmp             x3, x0, asr #1
    //     0x6d6454: b.eq            #0x6d6460
    //     0x6d6458: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6d645c: stur            x3, [x0, #7]
    // 0x6d6460: mov             x1, x2
    // 0x6d6464: mov             x2, x0
    // 0x6d6468: r0 = compareTo()
    //     0x6d6468: bl              #0x6daaec  ; [dart:core] _IntegerImplementation::compareTo
    // 0x6d646c: LeaveFrame
    //     0x6d646c: mov             SP, fp
    //     0x6d6470: ldp             fp, lr, [SP], #0x10
    // 0x6d6474: ret
    //     0x6d6474: ret             
    // 0x6d6478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6d6478: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6d647c: b               #0x6d6374
  }
  _ _FileSpan(/* No info */) {
    // ** addr: 0x705b0c, size: 0x22c
    // 0x705b0c: EnterFrame
    //     0x705b0c: stp             fp, lr, [SP, #-0x10]!
    //     0x705b10: mov             fp, SP
    // 0x705b14: AllocStack(0x28)
    //     0x705b14: sub             SP, SP, #0x28
    // 0x705b18: SetupParameters(_FileSpan this /* r1 => r2 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r3, fp-0x8 */, dynamic _ /* r5 => r5, fp-0x10 */)
    //     0x705b18: mov             x16, x2
    //     0x705b1c: mov             x2, x1
    //     0x705b20: mov             x1, x16
    //     0x705b24: stur            x3, [fp, #-8]
    //     0x705b28: stur            x5, [fp, #-0x10]
    // 0x705b2c: CheckStackOverflow
    //     0x705b2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x705b30: cmp             SP, x16
    //     0x705b34: b.ls            #0x705d30
    // 0x705b38: mov             x0, x1
    // 0x705b3c: StoreField: r2->field_7 = r0
    //     0x705b3c: stur            w0, [x2, #7]
    //     0x705b40: ldurb           w16, [x2, #-1]
    //     0x705b44: ldurb           w17, [x0, #-1]
    //     0x705b48: and             x16, x17, x16, lsr #2
    //     0x705b4c: tst             x16, HEAP, lsr #32
    //     0x705b50: b.eq            #0x705b58
    //     0x705b54: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x705b58: StoreField: r2->field_b = r3
    //     0x705b58: stur            x3, [x2, #0xb]
    // 0x705b5c: StoreField: r2->field_13 = r5
    //     0x705b5c: stur            x5, [x2, #0x13]
    // 0x705b60: cmp             x5, x3
    // 0x705b64: b.lt            #0x705b98
    // 0x705b68: LoadField: r0 = r1->field_f
    //     0x705b68: ldur            w0, [x1, #0xf]
    // 0x705b6c: DecompressPointer r0
    //     0x705b6c: add             x0, x0, HEAP, lsl #32
    // 0x705b70: LoadField: r4 = r0->field_13
    //     0x705b70: ldur            w4, [x0, #0x13]
    // 0x705b74: stur            x4, [fp, #-0x20]
    // 0x705b78: r0 = LoadInt32Instr(r4)
    //     0x705b78: sbfx            x0, x4, #1, #0x1f
    // 0x705b7c: cmp             x5, x0
    // 0x705b80: b.gt            #0x705c30
    // 0x705b84: tbnz            x3, #0x3f, #0x705cbc
    // 0x705b88: r0 = Null
    //     0x705b88: mov             x0, NULL
    // 0x705b8c: LeaveFrame
    //     0x705b8c: mov             SP, fp
    //     0x705b90: ldp             fp, lr, [SP], #0x10
    // 0x705b94: ret
    //     0x705b94: ret             
    // 0x705b98: r1 = Null
    //     0x705b98: mov             x1, NULL
    // 0x705b9c: r2 = 10
    //     0x705b9c: movz            x2, #0xa
    // 0x705ba0: r0 = AllocateArray()
    //     0x705ba0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x705ba4: mov             x2, x0
    // 0x705ba8: r16 = "End "
    //     0x705ba8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10920] "End "
    //     0x705bac: ldr             x16, [x16, #0x920]
    // 0x705bb0: StoreField: r2->field_f = r16
    //     0x705bb0: stur            w16, [x2, #0xf]
    // 0x705bb4: ldur            x3, [fp, #-0x10]
    // 0x705bb8: r0 = BoxInt64Instr(r3)
    //     0x705bb8: sbfiz           x0, x3, #1, #0x1f
    //     0x705bbc: cmp             x3, x0, asr #1
    //     0x705bc0: b.eq            #0x705bcc
    //     0x705bc4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x705bc8: stur            x3, [x0, #7]
    // 0x705bcc: StoreField: r2->field_13 = r0
    //     0x705bcc: stur            w0, [x2, #0x13]
    // 0x705bd0: r16 = " must come after start "
    //     0x705bd0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10928] " must come after start "
    //     0x705bd4: ldr             x16, [x16, #0x928]
    // 0x705bd8: ArrayStore: r2[0] = r16  ; List_4
    //     0x705bd8: stur            w16, [x2, #0x17]
    // 0x705bdc: ldur            x3, [fp, #-8]
    // 0x705be0: r0 = BoxInt64Instr(r3)
    //     0x705be0: sbfiz           x0, x3, #1, #0x1f
    //     0x705be4: cmp             x3, x0, asr #1
    //     0x705be8: b.eq            #0x705bf4
    //     0x705bec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x705bf0: stur            x3, [x0, #7]
    // 0x705bf4: StoreField: r2->field_1b = r0
    //     0x705bf4: stur            w0, [x2, #0x1b]
    // 0x705bf8: r16 = "."
    //     0x705bf8: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x705bfc: StoreField: r2->field_1f = r16
    //     0x705bfc: stur            w16, [x2, #0x1f]
    // 0x705c00: str             x2, [SP]
    // 0x705c04: r0 = _interpolate()
    //     0x705c04: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x705c08: stur            x0, [fp, #-0x18]
    // 0x705c0c: r0 = ArgumentError()
    //     0x705c0c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x705c10: mov             x1, x0
    // 0x705c14: ldur            x0, [fp, #-0x18]
    // 0x705c18: ArrayStore: r1[0] = r0  ; List_4
    //     0x705c18: stur            w0, [x1, #0x17]
    // 0x705c1c: r0 = false
    //     0x705c1c: add             x0, NULL, #0x30  ; false
    // 0x705c20: StoreField: r1->field_b = r0
    //     0x705c20: stur            w0, [x1, #0xb]
    // 0x705c24: mov             x0, x1
    // 0x705c28: r0 = Throw()
    //     0x705c28: bl              #0xec04b8  ; ThrowStub
    // 0x705c2c: brk             #0
    // 0x705c30: mov             x3, x5
    // 0x705c34: r0 = false
    //     0x705c34: add             x0, NULL, #0x30  ; false
    // 0x705c38: r1 = Null
    //     0x705c38: mov             x1, NULL
    // 0x705c3c: r2 = 10
    //     0x705c3c: movz            x2, #0xa
    // 0x705c40: r0 = AllocateArray()
    //     0x705c40: bl              #0xec22fc  ; AllocateArrayStub
    // 0x705c44: mov             x2, x0
    // 0x705c48: r16 = "End "
    //     0x705c48: add             x16, PP, #0x10, lsl #12  ; [pp+0x10920] "End "
    //     0x705c4c: ldr             x16, [x16, #0x920]
    // 0x705c50: StoreField: r2->field_f = r16
    //     0x705c50: stur            w16, [x2, #0xf]
    // 0x705c54: ldur            x3, [fp, #-0x10]
    // 0x705c58: r0 = BoxInt64Instr(r3)
    //     0x705c58: sbfiz           x0, x3, #1, #0x1f
    //     0x705c5c: cmp             x3, x0, asr #1
    //     0x705c60: b.eq            #0x705c6c
    //     0x705c64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x705c68: stur            x3, [x0, #7]
    // 0x705c6c: StoreField: r2->field_13 = r0
    //     0x705c6c: stur            w0, [x2, #0x13]
    // 0x705c70: r16 = " must not be greater than the number of characters in the file, "
    //     0x705c70: add             x16, PP, #0x10, lsl #12  ; [pp+0x10930] " must not be greater than the number of characters in the file, "
    //     0x705c74: ldr             x16, [x16, #0x930]
    // 0x705c78: ArrayStore: r2[0] = r16  ; List_4
    //     0x705c78: stur            w16, [x2, #0x17]
    // 0x705c7c: ldur            x0, [fp, #-0x20]
    // 0x705c80: StoreField: r2->field_1b = r0
    //     0x705c80: stur            w0, [x2, #0x1b]
    // 0x705c84: r16 = "."
    //     0x705c84: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x705c88: StoreField: r2->field_1f = r16
    //     0x705c88: stur            w16, [x2, #0x1f]
    // 0x705c8c: str             x2, [SP]
    // 0x705c90: r0 = _interpolate()
    //     0x705c90: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x705c94: stur            x0, [fp, #-0x18]
    // 0x705c98: r0 = RangeError()
    //     0x705c98: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x705c9c: mov             x1, x0
    // 0x705ca0: ldur            x0, [fp, #-0x18]
    // 0x705ca4: ArrayStore: r1[0] = r0  ; List_4
    //     0x705ca4: stur            w0, [x1, #0x17]
    // 0x705ca8: r0 = false
    //     0x705ca8: add             x0, NULL, #0x30  ; false
    // 0x705cac: StoreField: r1->field_b = r0
    //     0x705cac: stur            w0, [x1, #0xb]
    // 0x705cb0: mov             x0, x1
    // 0x705cb4: r0 = Throw()
    //     0x705cb4: bl              #0xec04b8  ; ThrowStub
    // 0x705cb8: brk             #0
    // 0x705cbc: r0 = false
    //     0x705cbc: add             x0, NULL, #0x30  ; false
    // 0x705cc0: r1 = Null
    //     0x705cc0: mov             x1, NULL
    // 0x705cc4: r2 = 6
    //     0x705cc4: movz            x2, #0x6
    // 0x705cc8: r0 = AllocateArray()
    //     0x705cc8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x705ccc: mov             x2, x0
    // 0x705cd0: r16 = "Start may not be negative, was "
    //     0x705cd0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10938] "Start may not be negative, was "
    //     0x705cd4: ldr             x16, [x16, #0x938]
    // 0x705cd8: StoreField: r2->field_f = r16
    //     0x705cd8: stur            w16, [x2, #0xf]
    // 0x705cdc: ldur            x3, [fp, #-8]
    // 0x705ce0: r0 = BoxInt64Instr(r3)
    //     0x705ce0: sbfiz           x0, x3, #1, #0x1f
    //     0x705ce4: cmp             x3, x0, asr #1
    //     0x705ce8: b.eq            #0x705cf4
    //     0x705cec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x705cf0: stur            x3, [x0, #7]
    // 0x705cf4: StoreField: r2->field_13 = r0
    //     0x705cf4: stur            w0, [x2, #0x13]
    // 0x705cf8: r16 = "."
    //     0x705cf8: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x705cfc: ArrayStore: r2[0] = r16  ; List_4
    //     0x705cfc: stur            w16, [x2, #0x17]
    // 0x705d00: str             x2, [SP]
    // 0x705d04: r0 = _interpolate()
    //     0x705d04: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x705d08: stur            x0, [fp, #-0x18]
    // 0x705d0c: r0 = RangeError()
    //     0x705d0c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x705d10: mov             x1, x0
    // 0x705d14: ldur            x0, [fp, #-0x18]
    // 0x705d18: ArrayStore: r1[0] = r0  ; List_4
    //     0x705d18: stur            w0, [x1, #0x17]
    // 0x705d1c: r0 = false
    //     0x705d1c: add             x0, NULL, #0x30  ; false
    // 0x705d20: StoreField: r1->field_b = r0
    //     0x705d20: stur            w0, [x1, #0xb]
    // 0x705d24: mov             x0, x1
    // 0x705d28: r0 = Throw()
    //     0x705d28: bl              #0xec04b8  ; ThrowStub
    // 0x705d2c: brk             #0
    // 0x705d30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x705d30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x705d34: b               #0x705b38
  }
  _ expand(/* No info */) {
    // ** addr: 0x94fac8, size: 0x360
    // 0x94fac8: EnterFrame
    //     0x94fac8: stp             fp, lr, [SP, #-0x10]!
    //     0x94facc: mov             fp, SP
    // 0x94fad0: AllocStack(0x30)
    //     0x94fad0: sub             SP, SP, #0x30
    // 0x94fad4: SetupParameters(_FileSpan this /* r1 => r3, fp-0x20 */)
    //     0x94fad4: mov             x3, x1
    //     0x94fad8: stur            x1, [fp, #-0x20]
    // 0x94fadc: CheckStackOverflow
    //     0x94fadc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94fae0: cmp             SP, x16
    //     0x94fae4: b.ls            #0x94fe20
    // 0x94fae8: LoadField: r4 = r3->field_b
    //     0x94fae8: ldur            x4, [x3, #0xb]
    // 0x94faec: LoadField: r5 = r2->field_b
    //     0x94faec: ldur            x5, [x2, #0xb]
    // 0x94faf0: cmp             x4, x5
    // 0x94faf4: b.le            #0x94fb18
    // 0x94faf8: r0 = BoxInt64Instr(r5)
    //     0x94faf8: sbfiz           x0, x5, #1, #0x1f
    //     0x94fafc: cmp             x5, x0, asr #1
    //     0x94fb00: b.eq            #0x94fb0c
    //     0x94fb04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94fb08: stur            x5, [x0, #7]
    // 0x94fb0c: mov             x4, x0
    // 0x94fb10: d0 = 0.000000
    //     0x94fb10: eor             v0.16b, v0.16b, v0.16b
    // 0x94fb14: b               #0x94fc30
    // 0x94fb18: cmp             x4, x5
    // 0x94fb1c: b.ge            #0x94fb40
    // 0x94fb20: r0 = BoxInt64Instr(r4)
    //     0x94fb20: sbfiz           x0, x4, #1, #0x1f
    //     0x94fb24: cmp             x4, x0, asr #1
    //     0x94fb28: b.eq            #0x94fb34
    //     0x94fb2c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94fb30: stur            x4, [x0, #7]
    // 0x94fb34: mov             x4, x0
    // 0x94fb38: d0 = 0.000000
    //     0x94fb38: eor             v0.16b, v0.16b, v0.16b
    // 0x94fb3c: b               #0x94fc30
    // 0x94fb40: r0 = BoxInt64Instr(r5)
    //     0x94fb40: sbfiz           x0, x5, #1, #0x1f
    //     0x94fb44: cmp             x5, x0, asr #1
    //     0x94fb48: b.eq            #0x94fb54
    //     0x94fb4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94fb50: stur            x5, [x0, #7]
    // 0x94fb54: mov             x6, x0
    // 0x94fb58: r0 = 60
    //     0x94fb58: movz            x0, #0x3c
    // 0x94fb5c: branchIfSmi(r6, 0x94fb68)
    //     0x94fb5c: tbz             w6, #0, #0x94fb68
    // 0x94fb60: r0 = LoadClassIdInstr(r6)
    //     0x94fb60: ldur            x0, [x6, #-1]
    //     0x94fb64: ubfx            x0, x0, #0xc, #0x14
    // 0x94fb68: cmp             x0, #0x3e
    // 0x94fb6c: b.ne            #0x94fc14
    // 0x94fb70: r0 = BoxInt64Instr(r4)
    //     0x94fb70: sbfiz           x0, x4, #1, #0x1f
    //     0x94fb74: cmp             x4, x0, asr #1
    //     0x94fb78: b.eq            #0x94fb84
    //     0x94fb7c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94fb80: stur            x4, [x0, #7]
    // 0x94fb84: r1 = 60
    //     0x94fb84: movz            x1, #0x3c
    // 0x94fb88: branchIfSmi(r0, 0x94fb94)
    //     0x94fb88: tbz             w0, #0, #0x94fb94
    // 0x94fb8c: r1 = LoadClassIdInstr(r0)
    //     0x94fb8c: ldur            x1, [x0, #-1]
    //     0x94fb90: ubfx            x1, x1, #0xc, #0x14
    // 0x94fb94: cmp             x1, #0x3e
    // 0x94fb98: b.ne            #0x94fbd4
    // 0x94fb9c: d0 = 0.000000
    //     0x94fb9c: eor             v0.16b, v0.16b, v0.16b
    // 0x94fba0: scvtf           d1, x4
    // 0x94fba4: fcmp            d1, d0
    // 0x94fba8: b.ne            #0x94fbd8
    // 0x94fbac: add             x0, x4, x5
    // 0x94fbb0: mul             x1, x0, x4
    // 0x94fbb4: mul             x4, x1, x5
    // 0x94fbb8: r0 = BoxInt64Instr(r4)
    //     0x94fbb8: sbfiz           x0, x4, #1, #0x1f
    //     0x94fbbc: cmp             x4, x0, asr #1
    //     0x94fbc0: b.eq            #0x94fbcc
    //     0x94fbc4: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0x94fbc8: stur            x4, [x0, #7]
    // 0x94fbcc: mov             x4, x0
    // 0x94fbd0: b               #0x94fc30
    // 0x94fbd4: d0 = 0.000000
    //     0x94fbd4: eor             v0.16b, v0.16b, v0.16b
    // 0x94fbd8: cbnz            x4, #0x94fbf8
    // 0x94fbdc: LoadField: d1 = r6->field_7
    //     0x94fbdc: ldur            d1, [x6, #7]
    // 0x94fbe0: fcmp            d1, #0.0
    // 0x94fbe4: b.vs            #0x94fbf8
    // 0x94fbe8: b.ne            #0x94fbf4
    // 0x94fbec: r1 = 0.000000
    //     0x94fbec: fmov            x1, d1
    // 0x94fbf0: cmp             x1, #0
    // 0x94fbf4: b.lt            #0x94fc04
    // 0x94fbf8: LoadField: d1 = r6->field_7
    //     0x94fbf8: ldur            d1, [x6, #7]
    // 0x94fbfc: fcmp            d1, d1
    // 0x94fc00: b.vc            #0x94fc0c
    // 0x94fc04: mov             x4, x6
    // 0x94fc08: b               #0x94fc30
    // 0x94fc0c: mov             x4, x0
    // 0x94fc10: b               #0x94fc30
    // 0x94fc14: d0 = 0.000000
    //     0x94fc14: eor             v0.16b, v0.16b, v0.16b
    // 0x94fc18: r0 = BoxInt64Instr(r4)
    //     0x94fc18: sbfiz           x0, x4, #1, #0x1f
    //     0x94fc1c: cmp             x4, x0, asr #1
    //     0x94fc20: b.eq            #0x94fc2c
    //     0x94fc24: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0x94fc28: stur            x4, [x0, #7]
    // 0x94fc2c: mov             x4, x0
    // 0x94fc30: stur            x4, [fp, #-0x18]
    // 0x94fc34: LoadField: r5 = r3->field_13
    //     0x94fc34: ldur            x5, [x3, #0x13]
    // 0x94fc38: stur            x5, [fp, #-0x10]
    // 0x94fc3c: LoadField: r6 = r2->field_13
    //     0x94fc3c: ldur            x6, [x2, #0x13]
    // 0x94fc40: cmp             x5, x6
    // 0x94fc44: b.le            #0x94fc6c
    // 0x94fc48: r0 = BoxInt64Instr(r5)
    //     0x94fc48: sbfiz           x0, x5, #1, #0x1f
    //     0x94fc4c: cmp             x5, x0, asr #1
    //     0x94fc50: b.eq            #0x94fc5c
    //     0x94fc54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94fc58: stur            x5, [x0, #7]
    // 0x94fc5c: mov             x2, x0
    // 0x94fc60: mov             x0, x3
    // 0x94fc64: mov             x1, x4
    // 0x94fc68: b               #0x94fdc8
    // 0x94fc6c: cmp             x5, x6
    // 0x94fc70: b.ge            #0x94fc98
    // 0x94fc74: r0 = BoxInt64Instr(r6)
    //     0x94fc74: sbfiz           x0, x6, #1, #0x1f
    //     0x94fc78: cmp             x6, x0, asr #1
    //     0x94fc7c: b.eq            #0x94fc88
    //     0x94fc80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94fc84: stur            x6, [x0, #7]
    // 0x94fc88: mov             x2, x0
    // 0x94fc8c: mov             x0, x3
    // 0x94fc90: mov             x1, x4
    // 0x94fc94: b               #0x94fdc8
    // 0x94fc98: r0 = BoxInt64Instr(r6)
    //     0x94fc98: sbfiz           x0, x6, #1, #0x1f
    //     0x94fc9c: cmp             x6, x0, asr #1
    //     0x94fca0: b.eq            #0x94fcac
    //     0x94fca4: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0x94fca8: stur            x6, [x0, #7]
    // 0x94fcac: mov             x2, x0
    // 0x94fcb0: stur            x2, [fp, #-8]
    // 0x94fcb4: r0 = 60
    //     0x94fcb4: movz            x0, #0x3c
    // 0x94fcb8: branchIfSmi(r2, 0x94fcc4)
    //     0x94fcb8: tbz             w2, #0, #0x94fcc4
    // 0x94fcbc: r0 = LoadClassIdInstr(r2)
    //     0x94fcbc: ldur            x0, [x2, #-1]
    //     0x94fcc0: ubfx            x0, x0, #0xc, #0x14
    // 0x94fcc4: cmp             x0, #0x3e
    // 0x94fcc8: b.ne            #0x94fd54
    // 0x94fccc: r0 = BoxInt64Instr(r5)
    //     0x94fccc: sbfiz           x0, x5, #1, #0x1f
    //     0x94fcd0: cmp             x5, x0, asr #1
    //     0x94fcd4: b.eq            #0x94fce0
    //     0x94fcd8: bl              #0xec2638  ; AllocateMintSharedWithFPURegsStub
    //     0x94fcdc: stur            x5, [x0, #7]
    // 0x94fce0: r1 = 60
    //     0x94fce0: movz            x1, #0x3c
    // 0x94fce4: branchIfSmi(r0, 0x94fcf0)
    //     0x94fce4: tbz             w0, #0, #0x94fcf0
    // 0x94fce8: r1 = LoadClassIdInstr(r0)
    //     0x94fce8: ldur            x1, [x0, #-1]
    //     0x94fcec: ubfx            x1, x1, #0xc, #0x14
    // 0x94fcf0: cmp             x1, #0x3e
    // 0x94fcf4: b.ne            #0x94fd2c
    // 0x94fcf8: scvtf           d1, x5
    // 0x94fcfc: fcmp            d1, d0
    // 0x94fd00: b.ne            #0x94fd2c
    // 0x94fd04: add             x2, x5, x6
    // 0x94fd08: r0 = BoxInt64Instr(r2)
    //     0x94fd08: sbfiz           x0, x2, #1, #0x1f
    //     0x94fd0c: cmp             x2, x0, asr #1
    //     0x94fd10: b.eq            #0x94fd1c
    //     0x94fd14: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94fd18: stur            x2, [x0, #7]
    // 0x94fd1c: mov             x2, x0
    // 0x94fd20: mov             x0, x3
    // 0x94fd24: mov             x1, x4
    // 0x94fd28: b               #0x94fdc8
    // 0x94fd2c: LoadField: d0 = r2->field_7
    //     0x94fd2c: ldur            d0, [x2, #7]
    // 0x94fd30: fcmp            d0, d0
    // 0x94fd34: b.vc            #0x94fd44
    // 0x94fd38: mov             x0, x3
    // 0x94fd3c: mov             x1, x4
    // 0x94fd40: b               #0x94fdc8
    // 0x94fd44: mov             x2, x0
    // 0x94fd48: mov             x0, x3
    // 0x94fd4c: mov             x1, x4
    // 0x94fd50: b               #0x94fdc8
    // 0x94fd54: cbnz            x6, #0x94fda4
    // 0x94fd58: r0 = BoxInt64Instr(r5)
    //     0x94fd58: sbfiz           x0, x5, #1, #0x1f
    //     0x94fd5c: cmp             x5, x0, asr #1
    //     0x94fd60: b.eq            #0x94fd6c
    //     0x94fd64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94fd68: stur            x5, [x0, #7]
    // 0x94fd6c: r1 = 60
    //     0x94fd6c: movz            x1, #0x3c
    // 0x94fd70: branchIfSmi(r0, 0x94fd7c)
    //     0x94fd70: tbz             w0, #0, #0x94fd7c
    // 0x94fd74: r1 = LoadClassIdInstr(r0)
    //     0x94fd74: ldur            x1, [x0, #-1]
    //     0x94fd78: ubfx            x1, x1, #0xc, #0x14
    // 0x94fd7c: str             x0, [SP]
    // 0x94fd80: mov             x0, x1
    // 0x94fd84: r0 = GDT[cid_x0 + -0xfb8]()
    //     0x94fd84: sub             lr, x0, #0xfb8
    //     0x94fd88: ldr             lr, [x21, lr, lsl #3]
    //     0x94fd8c: blr             lr
    // 0x94fd90: tbnz            w0, #4, #0x94fda4
    // 0x94fd94: ldur            x2, [fp, #-8]
    // 0x94fd98: ldur            x0, [fp, #-0x20]
    // 0x94fd9c: ldur            x1, [fp, #-0x18]
    // 0x94fda0: b               #0x94fdc8
    // 0x94fda4: ldur            x2, [fp, #-0x10]
    // 0x94fda8: r0 = BoxInt64Instr(r2)
    //     0x94fda8: sbfiz           x0, x2, #1, #0x1f
    //     0x94fdac: cmp             x2, x0, asr #1
    //     0x94fdb0: b.eq            #0x94fdbc
    //     0x94fdb4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94fdb8: stur            x2, [x0, #7]
    // 0x94fdbc: mov             x2, x0
    // 0x94fdc0: ldur            x0, [fp, #-0x20]
    // 0x94fdc4: ldur            x1, [fp, #-0x18]
    // 0x94fdc8: LoadField: r3 = r0->field_7
    //     0x94fdc8: ldur            w3, [x0, #7]
    // 0x94fdcc: DecompressPointer r3
    //     0x94fdcc: add             x3, x3, HEAP, lsl #32
    // 0x94fdd0: stur            x3, [fp, #-8]
    // 0x94fdd4: r0 = LoadInt32Instr(r1)
    //     0x94fdd4: sbfx            x0, x1, #1, #0x1f
    //     0x94fdd8: tbz             w1, #0, #0x94fde0
    //     0x94fddc: ldur            x0, [x1, #7]
    // 0x94fde0: stur            x0, [fp, #-0x28]
    // 0x94fde4: r5 = LoadInt32Instr(r2)
    //     0x94fde4: sbfx            x5, x2, #1, #0x1f
    //     0x94fde8: tbz             w2, #0, #0x94fdf0
    //     0x94fdec: ldur            x5, [x2, #7]
    // 0x94fdf0: stur            x5, [fp, #-0x10]
    // 0x94fdf4: r0 = _FileSpan()
    //     0x94fdf4: bl              #0x705d38  ; Allocate_FileSpanStub -> _FileSpan (size=0x1c)
    // 0x94fdf8: mov             x1, x0
    // 0x94fdfc: ldur            x2, [fp, #-8]
    // 0x94fe00: ldur            x3, [fp, #-0x28]
    // 0x94fe04: ldur            x5, [fp, #-0x10]
    // 0x94fe08: stur            x0, [fp, #-8]
    // 0x94fe0c: r0 = _FileSpan()
    //     0x94fe0c: bl              #0x705b0c  ; [package:source_span/src/file.dart] _FileSpan::_FileSpan
    // 0x94fe10: ldur            x0, [fp, #-8]
    // 0x94fe14: LeaveFrame
    //     0x94fe14: mov             SP, fp
    //     0x94fe18: ldp             fp, lr, [SP], #0x10
    // 0x94fe1c: ret
    //     0x94fe1c: ret             
    // 0x94fe20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94fe20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94fe24: b               #0x94fae8
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf36fc, size: 0x90
    // 0xbf36fc: EnterFrame
    //     0xbf36fc: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3700: mov             fp, SP
    // 0xbf3704: AllocStack(0x8)
    //     0xbf3704: sub             SP, SP, #8
    // 0xbf3708: CheckStackOverflow
    //     0xbf3708: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf370c: cmp             SP, x16
    //     0xbf3710: b.ls            #0xbf3784
    // 0xbf3714: ldr             x0, [fp, #0x10]
    // 0xbf3718: LoadField: r2 = r0->field_b
    //     0xbf3718: ldur            x2, [x0, #0xb]
    // 0xbf371c: LoadField: r3 = r0->field_13
    //     0xbf371c: ldur            x3, [x0, #0x13]
    // 0xbf3720: r0 = BoxInt64Instr(r2)
    //     0xbf3720: sbfiz           x0, x2, #1, #0x1f
    //     0xbf3724: cmp             x2, x0, asr #1
    //     0xbf3728: b.eq            #0xbf3734
    //     0xbf372c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3730: stur            x2, [x0, #7]
    // 0xbf3734: mov             x2, x0
    // 0xbf3738: r0 = BoxInt64Instr(r3)
    //     0xbf3738: sbfiz           x0, x3, #1, #0x1f
    //     0xbf373c: cmp             x3, x0, asr #1
    //     0xbf3740: b.eq            #0xbf374c
    //     0xbf3744: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3748: stur            x3, [x0, #7]
    // 0xbf374c: str             NULL, [SP]
    // 0xbf3750: mov             x1, x2
    // 0xbf3754: mov             x2, x0
    // 0xbf3758: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbf3758: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbf375c: r0 = hash()
    //     0xbf375c: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf3760: mov             x2, x0
    // 0xbf3764: r0 = BoxInt64Instr(r2)
    //     0xbf3764: sbfiz           x0, x2, #1, #0x1f
    //     0xbf3768: cmp             x2, x0, asr #1
    //     0xbf376c: b.eq            #0xbf3778
    //     0xbf3770: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3774: stur            x2, [x0, #7]
    // 0xbf3778: LeaveFrame
    //     0xbf3778: mov             SP, fp
    //     0xbf377c: ldp             fp, lr, [SP], #0x10
    // 0xbf3780: ret
    //     0xbf3780: ret             
    // 0xbf3784: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3784: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf3788: b               #0xbf3714
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7dd08, size: 0xa8
    // 0xd7dd08: EnterFrame
    //     0xd7dd08: stp             fp, lr, [SP, #-0x10]!
    //     0xd7dd0c: mov             fp, SP
    // 0xd7dd10: AllocStack(0x10)
    //     0xd7dd10: sub             SP, SP, #0x10
    // 0xd7dd14: CheckStackOverflow
    //     0xd7dd14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7dd18: cmp             SP, x16
    //     0xd7dd1c: b.ls            #0xd7dda8
    // 0xd7dd20: ldr             x0, [fp, #0x10]
    // 0xd7dd24: cmp             w0, NULL
    // 0xd7dd28: b.ne            #0xd7dd3c
    // 0xd7dd2c: r0 = false
    //     0xd7dd2c: add             x0, NULL, #0x30  ; false
    // 0xd7dd30: LeaveFrame
    //     0xd7dd30: mov             SP, fp
    //     0xd7dd34: ldp             fp, lr, [SP], #0x10
    // 0xd7dd38: ret
    //     0xd7dd38: ret             
    // 0xd7dd3c: r1 = 60
    //     0xd7dd3c: movz            x1, #0x3c
    // 0xd7dd40: branchIfSmi(r0, 0xd7dd4c)
    //     0xd7dd40: tbz             w0, #0, #0xd7dd4c
    // 0xd7dd44: r1 = LoadClassIdInstr(r0)
    //     0xd7dd44: ldur            x1, [x0, #-1]
    //     0xd7dd48: ubfx            x1, x1, #0xc, #0x14
    // 0xd7dd4c: cmp             x1, #0x1ed
    // 0xd7dd50: b.eq            #0xd7dd6c
    // 0xd7dd54: ldr             x16, [fp, #0x18]
    // 0xd7dd58: stp             x0, x16, [SP]
    // 0xd7dd5c: r0 = ==()
    //     0xd7dd5c: bl              #0xd7db60  ; [package:source_span/src/span_mixin.dart] SourceSpanMixin::==
    // 0xd7dd60: LeaveFrame
    //     0xd7dd60: mov             SP, fp
    //     0xd7dd64: ldp             fp, lr, [SP], #0x10
    // 0xd7dd68: ret
    //     0xd7dd68: ret             
    // 0xd7dd6c: ldr             x1, [fp, #0x18]
    // 0xd7dd70: LoadField: r2 = r1->field_b
    //     0xd7dd70: ldur            x2, [x1, #0xb]
    // 0xd7dd74: LoadField: r3 = r0->field_b
    //     0xd7dd74: ldur            x3, [x0, #0xb]
    // 0xd7dd78: cmp             x2, x3
    // 0xd7dd7c: b.ne            #0xd7dd98
    // 0xd7dd80: LoadField: r2 = r1->field_13
    //     0xd7dd80: ldur            x2, [x1, #0x13]
    // 0xd7dd84: LoadField: r1 = r0->field_13
    //     0xd7dd84: ldur            x1, [x0, #0x13]
    // 0xd7dd88: cmp             x2, x1
    // 0xd7dd8c: b.ne            #0xd7dd98
    // 0xd7dd90: r0 = true
    //     0xd7dd90: add             x0, NULL, #0x20  ; true
    // 0xd7dd94: b               #0xd7dd9c
    // 0xd7dd98: r0 = false
    //     0xd7dd98: add             x0, NULL, #0x30  ; false
    // 0xd7dd9c: LeaveFrame
    //     0xd7dd9c: mov             SP, fp
    //     0xd7dda0: ldp             fp, lr, [SP], #0x10
    // 0xd7dda4: ret
    //     0xd7dda4: ret             
    // 0xd7dda8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7dda8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7ddac: b               #0xd7dd20
  }
  get _ length(/* No info */) {
    // ** addr: 0xeb7308, size: 0x10
    // 0xeb7308: LoadField: r2 = r1->field_13
    //     0xeb7308: ldur            x2, [x1, #0x13]
    // 0xeb730c: LoadField: r3 = r1->field_b
    //     0xeb730c: ldur            x3, [x1, #0xb]
    // 0xeb7310: sub             x0, x2, x3
    // 0xeb7314: ret
    //     0xeb7314: ret             
  }
  get _ text(/* No info */) {
    // ** addr: 0xeb7510, size: 0x40
    // 0xeb7510: EnterFrame
    //     0xeb7510: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7514: mov             fp, SP
    // 0xeb7518: CheckStackOverflow
    //     0xeb7518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb751c: cmp             SP, x16
    //     0xeb7520: b.ls            #0xeb7548
    // 0xeb7524: LoadField: r0 = r1->field_7
    //     0xeb7524: ldur            w0, [x1, #7]
    // 0xeb7528: DecompressPointer r0
    //     0xeb7528: add             x0, x0, HEAP, lsl #32
    // 0xeb752c: LoadField: r2 = r1->field_b
    //     0xeb752c: ldur            x2, [x1, #0xb]
    // 0xeb7530: LoadField: r3 = r1->field_13
    //     0xeb7530: ldur            x3, [x1, #0x13]
    // 0xeb7534: mov             x1, x0
    // 0xeb7538: r0 = getText()
    //     0xeb7538: bl              #0x954b40  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xeb753c: LeaveFrame
    //     0xeb753c: mov             SP, fp
    //     0xeb7540: ldp             fp, lr, [SP], #0x10
    // 0xeb7544: ret
    //     0xeb7544: ret             
    // 0xeb7548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7548: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb754c: b               #0xeb7524
  }
  get _ context(/* No info */) {
    // ** addr: 0xeb7550, size: 0x190
    // 0xeb7550: EnterFrame
    //     0xeb7550: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7554: mov             fp, SP
    // 0xeb7558: AllocStack(0x28)
    //     0xeb7558: sub             SP, SP, #0x28
    // 0xeb755c: SetupParameters(_FileSpan this /* r1 => r0, fp-0x18 */)
    //     0xeb755c: mov             x0, x1
    //     0xeb7560: stur            x1, [fp, #-0x18]
    // 0xeb7564: CheckStackOverflow
    //     0xeb7564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7568: cmp             SP, x16
    //     0xeb756c: b.ls            #0xeb76d8
    // 0xeb7570: LoadField: r3 = r0->field_7
    //     0xeb7570: ldur            w3, [x0, #7]
    // 0xeb7574: DecompressPointer r3
    //     0xeb7574: add             x3, x3, HEAP, lsl #32
    // 0xeb7578: stur            x3, [fp, #-0x10]
    // 0xeb757c: LoadField: r4 = r0->field_13
    //     0xeb757c: ldur            x4, [x0, #0x13]
    // 0xeb7580: mov             x1, x3
    // 0xeb7584: mov             x2, x4
    // 0xeb7588: stur            x4, [fp, #-8]
    // 0xeb758c: r0 = getLine()
    //     0xeb758c: bl              #0xc1b388  ; [package:source_span/src/file.dart] SourceFile::getLine
    // 0xeb7590: ldur            x1, [fp, #-0x10]
    // 0xeb7594: ldur            x2, [fp, #-8]
    // 0xeb7598: stur            x0, [fp, #-0x20]
    // 0xeb759c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb759c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb75a0: r0 = getColumn()
    //     0xeb75a0: bl              #0xc1b124  ; [package:source_span/src/file.dart] SourceFile::getColumn
    // 0xeb75a4: cbnz            x0, #0xeb7648
    // 0xeb75a8: ldur            x0, [fp, #-0x20]
    // 0xeb75ac: cbz             x0, #0xeb7640
    // 0xeb75b0: ldur            x3, [fp, #-0x18]
    // 0xeb75b4: ldur            x1, [fp, #-8]
    // 0xeb75b8: LoadField: r2 = r3->field_b
    //     0xeb75b8: ldur            x2, [x3, #0xb]
    // 0xeb75bc: sub             x4, x1, x2
    // 0xeb75c0: cbnz            x4, #0xeb7634
    // 0xeb75c4: ldur            x3, [fp, #-0x10]
    // 0xeb75c8: LoadField: r1 = r3->field_b
    //     0xeb75c8: ldur            w1, [x3, #0xb]
    // 0xeb75cc: DecompressPointer r1
    //     0xeb75cc: add             x1, x1, HEAP, lsl #32
    // 0xeb75d0: LoadField: r2 = r1->field_b
    //     0xeb75d0: ldur            w2, [x1, #0xb]
    // 0xeb75d4: r1 = LoadInt32Instr(r2)
    //     0xeb75d4: sbfx            x1, x2, #1, #0x1f
    // 0xeb75d8: sub             x2, x1, #1
    // 0xeb75dc: cmp             x0, x2
    // 0xeb75e0: b.ne            #0xeb75ec
    // 0xeb75e4: r0 = ""
    //     0xeb75e4: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xeb75e8: b               #0xeb7628
    // 0xeb75ec: mov             x1, x3
    // 0xeb75f0: mov             x2, x0
    // 0xeb75f4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb75f4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb75f8: r0 = getOffset()
    //     0xeb75f8: bl              #0xeb76e0  ; [package:source_span/src/file.dart] SourceFile::getOffset
    // 0xeb75fc: mov             x3, x0
    // 0xeb7600: ldur            x0, [fp, #-0x20]
    // 0xeb7604: stur            x3, [fp, #-0x28]
    // 0xeb7608: add             x2, x0, #1
    // 0xeb760c: ldur            x1, [fp, #-0x10]
    // 0xeb7610: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb7610: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb7614: r0 = getOffset()
    //     0xeb7614: bl              #0xeb76e0  ; [package:source_span/src/file.dart] SourceFile::getOffset
    // 0xeb7618: ldur            x1, [fp, #-0x10]
    // 0xeb761c: ldur            x2, [fp, #-0x28]
    // 0xeb7620: mov             x3, x0
    // 0xeb7624: r0 = getText()
    //     0xeb7624: bl              #0x954b40  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xeb7628: LeaveFrame
    //     0xeb7628: mov             SP, fp
    //     0xeb762c: ldp             fp, lr, [SP], #0x10
    // 0xeb7630: ret
    //     0xeb7630: ret             
    // 0xeb7634: mov             x0, x3
    // 0xeb7638: mov             x3, x1
    // 0xeb763c: b               #0xeb769c
    // 0xeb7640: ldur            x3, [fp, #-0x18]
    // 0xeb7644: b               #0xeb7650
    // 0xeb7648: ldur            x3, [fp, #-0x18]
    // 0xeb764c: ldur            x0, [fp, #-0x20]
    // 0xeb7650: ldur            x4, [fp, #-0x10]
    // 0xeb7654: LoadField: r1 = r4->field_b
    //     0xeb7654: ldur            w1, [x4, #0xb]
    // 0xeb7658: DecompressPointer r1
    //     0xeb7658: add             x1, x1, HEAP, lsl #32
    // 0xeb765c: LoadField: r2 = r1->field_b
    //     0xeb765c: ldur            w2, [x1, #0xb]
    // 0xeb7660: r1 = LoadInt32Instr(r2)
    //     0xeb7660: sbfx            x1, x2, #1, #0x1f
    // 0xeb7664: sub             x2, x1, #1
    // 0xeb7668: cmp             x0, x2
    // 0xeb766c: b.ne            #0xeb7684
    // 0xeb7670: LoadField: r0 = r4->field_f
    //     0xeb7670: ldur            w0, [x4, #0xf]
    // 0xeb7674: DecompressPointer r0
    //     0xeb7674: add             x0, x0, HEAP, lsl #32
    // 0xeb7678: LoadField: r1 = r0->field_13
    //     0xeb7678: ldur            w1, [x0, #0x13]
    // 0xeb767c: r0 = LoadInt32Instr(r1)
    //     0xeb767c: sbfx            x0, x1, #1, #0x1f
    // 0xeb7680: b               #0xeb7694
    // 0xeb7684: add             x2, x0, #1
    // 0xeb7688: mov             x1, x4
    // 0xeb768c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb768c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb7690: r0 = getOffset()
    //     0xeb7690: bl              #0xeb76e0  ; [package:source_span/src/file.dart] SourceFile::getOffset
    // 0xeb7694: mov             x3, x0
    // 0xeb7698: ldur            x0, [fp, #-0x18]
    // 0xeb769c: stur            x3, [fp, #-8]
    // 0xeb76a0: LoadField: r2 = r0->field_b
    //     0xeb76a0: ldur            x2, [x0, #0xb]
    // 0xeb76a4: ldur            x1, [fp, #-0x10]
    // 0xeb76a8: r0 = getLine()
    //     0xeb76a8: bl              #0xc1b388  ; [package:source_span/src/file.dart] SourceFile::getLine
    // 0xeb76ac: ldur            x1, [fp, #-0x10]
    // 0xeb76b0: mov             x2, x0
    // 0xeb76b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb76b4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb76b8: r0 = getOffset()
    //     0xeb76b8: bl              #0xeb76e0  ; [package:source_span/src/file.dart] SourceFile::getOffset
    // 0xeb76bc: ldur            x1, [fp, #-0x10]
    // 0xeb76c0: mov             x2, x0
    // 0xeb76c4: ldur            x3, [fp, #-8]
    // 0xeb76c8: r0 = getText()
    //     0xeb76c8: bl              #0x954b40  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xeb76cc: LeaveFrame
    //     0xeb76cc: mov             SP, fp
    //     0xeb76d0: ldp             fp, lr, [SP], #0x10
    // 0xeb76d4: ret
    //     0xeb76d4: ret             
    // 0xeb76d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb76d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb76dc: b               #0xeb7570
  }
  get _ start(/* No info */) {
    // ** addr: 0xeb79d0, size: 0x5c
    // 0xeb79d0: EnterFrame
    //     0xeb79d0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb79d4: mov             fp, SP
    // 0xeb79d8: AllocStack(0x10)
    //     0xeb79d8: sub             SP, SP, #0x10
    // 0xeb79dc: CheckStackOverflow
    //     0xeb79dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb79e0: cmp             SP, x16
    //     0xeb79e4: b.ls            #0xeb7a24
    // 0xeb79e8: LoadField: r2 = r1->field_7
    //     0xeb79e8: ldur            w2, [x1, #7]
    // 0xeb79ec: DecompressPointer r2
    //     0xeb79ec: add             x2, x2, HEAP, lsl #32
    // 0xeb79f0: stur            x2, [fp, #-0x10]
    // 0xeb79f4: LoadField: r3 = r1->field_b
    //     0xeb79f4: ldur            x3, [x1, #0xb]
    // 0xeb79f8: stur            x3, [fp, #-8]
    // 0xeb79fc: r0 = FileLocation()
    //     0xeb79fc: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xeb7a00: mov             x1, x0
    // 0xeb7a04: ldur            x2, [fp, #-0x10]
    // 0xeb7a08: ldur            x3, [fp, #-8]
    // 0xeb7a0c: stur            x0, [fp, #-0x10]
    // 0xeb7a10: r0 = FileLocation._()
    //     0xeb7a10: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xeb7a14: ldur            x0, [fp, #-0x10]
    // 0xeb7a18: LeaveFrame
    //     0xeb7a18: mov             SP, fp
    //     0xeb7a1c: ldp             fp, lr, [SP], #0x10
    // 0xeb7a20: ret
    //     0xeb7a20: ret             
    // 0xeb7a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7a24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7a28: b               #0xeb79e8
  }
  get _ end(/* No info */) {
    // ** addr: 0xeb7a2c, size: 0x5c
    // 0xeb7a2c: EnterFrame
    //     0xeb7a2c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7a30: mov             fp, SP
    // 0xeb7a34: AllocStack(0x10)
    //     0xeb7a34: sub             SP, SP, #0x10
    // 0xeb7a38: CheckStackOverflow
    //     0xeb7a38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7a3c: cmp             SP, x16
    //     0xeb7a40: b.ls            #0xeb7a80
    // 0xeb7a44: LoadField: r2 = r1->field_7
    //     0xeb7a44: ldur            w2, [x1, #7]
    // 0xeb7a48: DecompressPointer r2
    //     0xeb7a48: add             x2, x2, HEAP, lsl #32
    // 0xeb7a4c: stur            x2, [fp, #-0x10]
    // 0xeb7a50: LoadField: r3 = r1->field_13
    //     0xeb7a50: ldur            x3, [x1, #0x13]
    // 0xeb7a54: stur            x3, [fp, #-8]
    // 0xeb7a58: r0 = FileLocation()
    //     0xeb7a58: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xeb7a5c: mov             x1, x0
    // 0xeb7a60: ldur            x2, [fp, #-0x10]
    // 0xeb7a64: ldur            x3, [fp, #-8]
    // 0xeb7a68: stur            x0, [fp, #-0x10]
    // 0xeb7a6c: r0 = FileLocation._()
    //     0xeb7a6c: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xeb7a70: ldur            x0, [fp, #-0x10]
    // 0xeb7a74: LeaveFrame
    //     0xeb7a74: mov             SP, fp
    //     0xeb7a78: ldp             fp, lr, [SP], #0x10
    // 0xeb7a7c: ret
    //     0xeb7a7c: ret             
    // 0xeb7a80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7a80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7a84: b               #0xeb7a44
  }
}

// class id: 496, size: 0x8, field offset: 0x8
abstract class FileSpan extends Object
    implements SourceSpanWithContext {
}

// class id: 499, size: 0x14, field offset: 0x8
class FileLocation extends SourceLocationMixin
    implements SourceLocation {

  _ FileLocation._(/* No info */) {
    // ** addr: 0x6d61c4, size: 0x17c
    // 0x6d61c4: EnterFrame
    //     0x6d61c4: stp             fp, lr, [SP, #-0x10]!
    //     0x6d61c8: mov             fp, SP
    // 0x6d61cc: AllocStack(0x20)
    //     0x6d61cc: sub             SP, SP, #0x20
    // 0x6d61d0: SetupParameters(FileLocation this /* r1 => r2 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r3, fp-0x8 */)
    //     0x6d61d0: mov             x16, x2
    //     0x6d61d4: mov             x2, x1
    //     0x6d61d8: mov             x1, x16
    //     0x6d61dc: stur            x3, [fp, #-8]
    // 0x6d61e0: CheckStackOverflow
    //     0x6d61e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6d61e4: cmp             SP, x16
    //     0x6d61e8: b.ls            #0x6d6338
    // 0x6d61ec: mov             x0, x1
    // 0x6d61f0: StoreField: r2->field_7 = r0
    //     0x6d61f0: stur            w0, [x2, #7]
    //     0x6d61f4: ldurb           w16, [x2, #-1]
    //     0x6d61f8: ldurb           w17, [x0, #-1]
    //     0x6d61fc: and             x16, x17, x16, lsr #2
    //     0x6d6200: tst             x16, HEAP, lsr #32
    //     0x6d6204: b.eq            #0x6d620c
    //     0x6d6208: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6d620c: StoreField: r2->field_b = r3
    //     0x6d620c: stur            x3, [x2, #0xb]
    // 0x6d6210: tbnz            x3, #0x3f, #0x6d6240
    // 0x6d6214: LoadField: r0 = r1->field_f
    //     0x6d6214: ldur            w0, [x1, #0xf]
    // 0x6d6218: DecompressPointer r0
    //     0x6d6218: add             x0, x0, HEAP, lsl #32
    // 0x6d621c: LoadField: r4 = r0->field_13
    //     0x6d621c: ldur            w4, [x0, #0x13]
    // 0x6d6220: stur            x4, [fp, #-0x18]
    // 0x6d6224: r0 = LoadInt32Instr(r4)
    //     0x6d6224: sbfx            x0, x4, #1, #0x1f
    // 0x6d6228: cmp             x3, x0
    // 0x6d622c: b.gt            #0x6d62b0
    // 0x6d6230: r0 = Null
    //     0x6d6230: mov             x0, NULL
    // 0x6d6234: LeaveFrame
    //     0x6d6234: mov             SP, fp
    //     0x6d6238: ldp             fp, lr, [SP], #0x10
    // 0x6d623c: ret
    //     0x6d623c: ret             
    // 0x6d6240: r1 = Null
    //     0x6d6240: mov             x1, NULL
    // 0x6d6244: r2 = 6
    //     0x6d6244: movz            x2, #0x6
    // 0x6d6248: r0 = AllocateArray()
    //     0x6d6248: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6d624c: mov             x2, x0
    // 0x6d6250: r16 = "Offset may not be negative, was "
    //     0x6d6250: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c538] "Offset may not be negative, was "
    //     0x6d6254: ldr             x16, [x16, #0x538]
    // 0x6d6258: StoreField: r2->field_f = r16
    //     0x6d6258: stur            w16, [x2, #0xf]
    // 0x6d625c: ldur            x3, [fp, #-8]
    // 0x6d6260: r0 = BoxInt64Instr(r3)
    //     0x6d6260: sbfiz           x0, x3, #1, #0x1f
    //     0x6d6264: cmp             x3, x0, asr #1
    //     0x6d6268: b.eq            #0x6d6274
    //     0x6d626c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6d6270: stur            x3, [x0, #7]
    // 0x6d6274: StoreField: r2->field_13 = r0
    //     0x6d6274: stur            w0, [x2, #0x13]
    // 0x6d6278: r16 = "."
    //     0x6d6278: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x6d627c: ArrayStore: r2[0] = r16  ; List_4
    //     0x6d627c: stur            w16, [x2, #0x17]
    // 0x6d6280: str             x2, [SP]
    // 0x6d6284: r0 = _interpolate()
    //     0x6d6284: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6d6288: stur            x0, [fp, #-0x10]
    // 0x6d628c: r0 = RangeError()
    //     0x6d628c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x6d6290: mov             x1, x0
    // 0x6d6294: ldur            x0, [fp, #-0x10]
    // 0x6d6298: ArrayStore: r1[0] = r0  ; List_4
    //     0x6d6298: stur            w0, [x1, #0x17]
    // 0x6d629c: r0 = false
    //     0x6d629c: add             x0, NULL, #0x30  ; false
    // 0x6d62a0: StoreField: r1->field_b = r0
    //     0x6d62a0: stur            w0, [x1, #0xb]
    // 0x6d62a4: mov             x0, x1
    // 0x6d62a8: r0 = Throw()
    //     0x6d62a8: bl              #0xec04b8  ; ThrowStub
    // 0x6d62ac: brk             #0
    // 0x6d62b0: r0 = false
    //     0x6d62b0: add             x0, NULL, #0x30  ; false
    // 0x6d62b4: r1 = Null
    //     0x6d62b4: mov             x1, NULL
    // 0x6d62b8: r2 = 10
    //     0x6d62b8: movz            x2, #0xa
    // 0x6d62bc: r0 = AllocateArray()
    //     0x6d62bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6d62c0: mov             x2, x0
    // 0x6d62c4: r16 = "Offset "
    //     0x6d62c4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c540] "Offset "
    //     0x6d62c8: ldr             x16, [x16, #0x540]
    // 0x6d62cc: StoreField: r2->field_f = r16
    //     0x6d62cc: stur            w16, [x2, #0xf]
    // 0x6d62d0: ldur            x3, [fp, #-8]
    // 0x6d62d4: r0 = BoxInt64Instr(r3)
    //     0x6d62d4: sbfiz           x0, x3, #1, #0x1f
    //     0x6d62d8: cmp             x3, x0, asr #1
    //     0x6d62dc: b.eq            #0x6d62e8
    //     0x6d62e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6d62e4: stur            x3, [x0, #7]
    // 0x6d62e8: StoreField: r2->field_13 = r0
    //     0x6d62e8: stur            w0, [x2, #0x13]
    // 0x6d62ec: r16 = " must not be greater than the number of characters in the file, "
    //     0x6d62ec: add             x16, PP, #0x10, lsl #12  ; [pp+0x10930] " must not be greater than the number of characters in the file, "
    //     0x6d62f0: ldr             x16, [x16, #0x930]
    // 0x6d62f4: ArrayStore: r2[0] = r16  ; List_4
    //     0x6d62f4: stur            w16, [x2, #0x17]
    // 0x6d62f8: ldur            x0, [fp, #-0x18]
    // 0x6d62fc: StoreField: r2->field_1b = r0
    //     0x6d62fc: stur            w0, [x2, #0x1b]
    // 0x6d6300: r16 = "."
    //     0x6d6300: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x6d6304: StoreField: r2->field_1f = r16
    //     0x6d6304: stur            w16, [x2, #0x1f]
    // 0x6d6308: str             x2, [SP]
    // 0x6d630c: r0 = _interpolate()
    //     0x6d630c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6d6310: stur            x0, [fp, #-0x10]
    // 0x6d6314: r0 = RangeError()
    //     0x6d6314: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x6d6318: mov             x1, x0
    // 0x6d631c: ldur            x0, [fp, #-0x10]
    // 0x6d6320: ArrayStore: r1[0] = r0  ; List_4
    //     0x6d6320: stur            w0, [x1, #0x17]
    // 0x6d6324: r0 = false
    //     0x6d6324: add             x0, NULL, #0x30  ; false
    // 0x6d6328: StoreField: r1->field_b = r0
    //     0x6d6328: stur            w0, [x1, #0xb]
    // 0x6d632c: mov             x0, x1
    // 0x6d6330: r0 = Throw()
    //     0x6d6330: bl              #0xec04b8  ; ThrowStub
    // 0x6d6334: brk             #0
    // 0x6d6338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6d6338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6d633c: b               #0x6d61ec
  }
  _ pointSpan(/* No info */) {
    // ** addr: 0x8960dc, size: 0x60
    // 0x8960dc: EnterFrame
    //     0x8960dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8960e0: mov             fp, SP
    // 0x8960e4: AllocStack(0x10)
    //     0x8960e4: sub             SP, SP, #0x10
    // 0x8960e8: CheckStackOverflow
    //     0x8960e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8960ec: cmp             SP, x16
    //     0x8960f0: b.ls            #0x896134
    // 0x8960f4: LoadField: r2 = r1->field_7
    //     0x8960f4: ldur            w2, [x1, #7]
    // 0x8960f8: DecompressPointer r2
    //     0x8960f8: add             x2, x2, HEAP, lsl #32
    // 0x8960fc: stur            x2, [fp, #-0x10]
    // 0x896100: LoadField: r5 = r1->field_b
    //     0x896100: ldur            x5, [x1, #0xb]
    // 0x896104: stur            x5, [fp, #-8]
    // 0x896108: r0 = _FileSpan()
    //     0x896108: bl              #0x705d38  ; Allocate_FileSpanStub -> _FileSpan (size=0x1c)
    // 0x89610c: mov             x1, x0
    // 0x896110: ldur            x2, [fp, #-0x10]
    // 0x896114: ldur            x3, [fp, #-8]
    // 0x896118: ldur            x5, [fp, #-8]
    // 0x89611c: stur            x0, [fp, #-0x10]
    // 0x896120: r0 = _FileSpan()
    //     0x896120: bl              #0x705b0c  ; [package:source_span/src/file.dart] _FileSpan::_FileSpan
    // 0x896124: ldur            x0, [fp, #-0x10]
    // 0x896128: LeaveFrame
    //     0x896128: mov             SP, fp
    //     0x89612c: ldp             fp, lr, [SP], #0x10
    // 0x896130: ret
    //     0x896130: ret             
    // 0x896134: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x896134: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x896138: b               #0x8960f4
  }
  get _ column(/* No info */) {
    // ** addr: 0xeb7944, size: 0x40
    // 0xeb7944: EnterFrame
    //     0xeb7944: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7948: mov             fp, SP
    // 0xeb794c: CheckStackOverflow
    //     0xeb794c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7950: cmp             SP, x16
    //     0xeb7954: b.ls            #0xeb797c
    // 0xeb7958: LoadField: r0 = r1->field_7
    //     0xeb7958: ldur            w0, [x1, #7]
    // 0xeb795c: DecompressPointer r0
    //     0xeb795c: add             x0, x0, HEAP, lsl #32
    // 0xeb7960: LoadField: r2 = r1->field_b
    //     0xeb7960: ldur            x2, [x1, #0xb]
    // 0xeb7964: mov             x1, x0
    // 0xeb7968: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb7968: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb796c: r0 = getColumn()
    //     0xeb796c: bl              #0xc1b124  ; [package:source_span/src/file.dart] SourceFile::getColumn
    // 0xeb7970: LeaveFrame
    //     0xeb7970: mov             SP, fp
    //     0xeb7974: ldp             fp, lr, [SP], #0x10
    // 0xeb7978: ret
    //     0xeb7978: ret             
    // 0xeb797c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb797c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7980: b               #0xeb7958
  }
  get _ line(/* No info */) {
    // ** addr: 0xeb7984, size: 0x3c
    // 0xeb7984: EnterFrame
    //     0xeb7984: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7988: mov             fp, SP
    // 0xeb798c: CheckStackOverflow
    //     0xeb798c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7990: cmp             SP, x16
    //     0xeb7994: b.ls            #0xeb79b8
    // 0xeb7998: LoadField: r0 = r1->field_7
    //     0xeb7998: ldur            w0, [x1, #7]
    // 0xeb799c: DecompressPointer r0
    //     0xeb799c: add             x0, x0, HEAP, lsl #32
    // 0xeb79a0: LoadField: r2 = r1->field_b
    //     0xeb79a0: ldur            x2, [x1, #0xb]
    // 0xeb79a4: mov             x1, x0
    // 0xeb79a8: r0 = getLine()
    //     0xeb79a8: bl              #0xc1b388  ; [package:source_span/src/file.dart] SourceFile::getLine
    // 0xeb79ac: LeaveFrame
    //     0xeb79ac: mov             SP, fp
    //     0xeb79b0: ldp             fp, lr, [SP], #0x10
    // 0xeb79b4: ret
    //     0xeb79b4: ret             
    // 0xeb79b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb79b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb79bc: b               #0xeb7998
  }
}

// class id: 500, size: 0x18, field offset: 0x8
class SourceFile extends Object {

  _ span(/* No info */) {
    // ** addr: 0x705aa8, size: 0x64
    // 0x705aa8: EnterFrame
    //     0x705aa8: stp             fp, lr, [SP, #-0x10]!
    //     0x705aac: mov             fp, SP
    // 0x705ab0: AllocStack(0x18)
    //     0x705ab0: sub             SP, SP, #0x18
    // 0x705ab4: SetupParameters(SourceFile this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0x705ab4: mov             x5, x3
    //     0x705ab8: stur            x3, [fp, #-0x18]
    //     0x705abc: mov             x3, x2
    //     0x705ac0: stur            x2, [fp, #-0x10]
    //     0x705ac4: mov             x2, x1
    //     0x705ac8: stur            x1, [fp, #-8]
    // 0x705acc: CheckStackOverflow
    //     0x705acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x705ad0: cmp             SP, x16
    //     0x705ad4: b.ls            #0x705b04
    // 0x705ad8: r0 = _FileSpan()
    //     0x705ad8: bl              #0x705d38  ; Allocate_FileSpanStub -> _FileSpan (size=0x1c)
    // 0x705adc: mov             x1, x0
    // 0x705ae0: ldur            x2, [fp, #-8]
    // 0x705ae4: ldur            x3, [fp, #-0x10]
    // 0x705ae8: ldur            x5, [fp, #-0x18]
    // 0x705aec: stur            x0, [fp, #-8]
    // 0x705af0: r0 = _FileSpan()
    //     0x705af0: bl              #0x705b0c  ; [package:source_span/src/file.dart] _FileSpan::_FileSpan
    // 0x705af4: ldur            x0, [fp, #-8]
    // 0x705af8: LeaveFrame
    //     0x705af8: mov             SP, fp
    //     0x705afc: ldp             fp, lr, [SP], #0x10
    // 0x705b00: ret
    //     0x705b00: ret             
    // 0x705b04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x705b04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x705b08: b               #0x705ad8
  }
  _ SourceFile.decoded(/* No info */) {
    // ** addr: 0x705d44, size: 0x220
    // 0x705d44: EnterFrame
    //     0x705d44: stp             fp, lr, [SP, #-0x10]!
    //     0x705d48: mov             fp, SP
    // 0x705d4c: AllocStack(0x40)
    //     0x705d4c: sub             SP, SP, #0x40
    // 0x705d50: r0 = 2
    //     0x705d50: movz            x0, #0x2
    // 0x705d54: mov             x4, x1
    // 0x705d58: mov             x3, x2
    // 0x705d5c: stur            x1, [fp, #-8]
    // 0x705d60: stur            x2, [fp, #-0x10]
    // 0x705d64: CheckStackOverflow
    //     0x705d64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x705d68: cmp             SP, x16
    //     0x705d6c: b.ls            #0x705f54
    // 0x705d70: mov             x2, x0
    // 0x705d74: r1 = Null
    //     0x705d74: mov             x1, NULL
    // 0x705d78: r0 = AllocateArray()
    //     0x705d78: bl              #0xec22fc  ; AllocateArrayStub
    // 0x705d7c: stur            x0, [fp, #-0x18]
    // 0x705d80: StoreField: r0->field_f = rZR
    //     0x705d80: stur            wzr, [x0, #0xf]
    // 0x705d84: r1 = <int>
    //     0x705d84: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x705d88: r0 = AllocateGrowableArray()
    //     0x705d88: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x705d8c: mov             x2, x0
    // 0x705d90: ldur            x0, [fp, #-0x18]
    // 0x705d94: stur            x2, [fp, #-0x20]
    // 0x705d98: StoreField: r2->field_f = r0
    //     0x705d98: stur            w0, [x2, #0xf]
    // 0x705d9c: r0 = 2
    //     0x705d9c: movz            x0, #0x2
    // 0x705da0: StoreField: r2->field_b = r0
    //     0x705da0: stur            w0, [x2, #0xb]
    // 0x705da4: mov             x0, x2
    // 0x705da8: ldur            x3, [fp, #-8]
    // 0x705dac: StoreField: r3->field_b = r0
    //     0x705dac: stur            w0, [x3, #0xb]
    //     0x705db0: ldurb           w16, [x3, #-1]
    //     0x705db4: ldurb           w17, [x0, #-1]
    //     0x705db8: and             x16, x17, x16, lsr #2
    //     0x705dbc: tst             x16, HEAP, lsr #32
    //     0x705dc0: b.eq            #0x705dc8
    //     0x705dc4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x705dc8: ldur            x1, [fp, #-0x10]
    // 0x705dcc: r0 = LoadClassIdInstr(r1)
    //     0x705dcc: ldur            x0, [x1, #-1]
    //     0x705dd0: ubfx            x0, x0, #0xc, #0x14
    // 0x705dd4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x705dd4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x705dd8: r0 = GDT[cid_x0 + 0xd889]()
    //     0x705dd8: movz            x17, #0xd889
    //     0x705ddc: add             lr, x0, x17
    //     0x705de0: ldr             lr, [x21, lr, lsl #3]
    //     0x705de4: blr             lr
    // 0x705de8: stur            x0, [fp, #-0x18]
    // 0x705dec: LoadField: r4 = r0->field_b
    //     0x705dec: ldur            w4, [x0, #0xb]
    // 0x705df0: stur            x4, [fp, #-0x10]
    // 0x705df4: r5 = LoadInt32Instr(r4)
    //     0x705df4: sbfx            x5, x4, #1, #0x1f
    // 0x705df8: stur            x5, [fp, #-0x28]
    // 0x705dfc: tbz             x5, #0x3f, #0x705e14
    // 0x705e00: mov             x2, x4
    // 0x705e04: mov             x3, x5
    // 0x705e08: r1 = 0
    //     0x705e08: movz            x1, #0
    // 0x705e0c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x705e0c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x705e10: r0 = checkValidRange()
    //     0x705e10: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x705e14: ldur            x0, [fp, #-8]
    // 0x705e18: ldur            x4, [fp, #-0x10]
    // 0x705e1c: r0 = AllocateUint32Array()
    //     0x705e1c: bl              #0xec1c2c  ; AllocateUint32ArrayStub
    // 0x705e20: mov             x1, x0
    // 0x705e24: ldur            x3, [fp, #-0x28]
    // 0x705e28: ldur            x5, [fp, #-0x18]
    // 0x705e2c: r2 = 0
    //     0x705e2c: movz            x2, #0
    // 0x705e30: r6 = 0
    //     0x705e30: movz            x6, #0
    // 0x705e34: stur            x0, [fp, #-0x10]
    // 0x705e38: r0 = _slowSetRange()
    //     0x705e38: bl              #0xc0b4b4  ; [dart:typed_data] __Uint32List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x705e3c: ldur            x0, [fp, #-0x10]
    // 0x705e40: ldur            x1, [fp, #-8]
    // 0x705e44: StoreField: r1->field_f = r0
    //     0x705e44: stur            w0, [x1, #0xf]
    //     0x705e48: ldurb           w16, [x1, #-1]
    //     0x705e4c: ldurb           w17, [x0, #-1]
    //     0x705e50: and             x16, x17, x16, lsr #2
    //     0x705e54: tst             x16, HEAP, lsr #32
    //     0x705e58: b.eq            #0x705e60
    //     0x705e5c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x705e60: ldur            x3, [fp, #-0x20]
    // 0x705e64: r4 = 0
    //     0x705e64: movz            x4, #0
    // 0x705e68: ldur            x0, [fp, #-0x10]
    // 0x705e6c: ldur            x2, [fp, #-0x28]
    // 0x705e70: stur            x4, [fp, #-0x40]
    // 0x705e74: CheckStackOverflow
    //     0x705e74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x705e78: cmp             SP, x16
    //     0x705e7c: b.ls            #0x705f5c
    // 0x705e80: cmp             x4, x2
    // 0x705e84: b.ge            #0x705f44
    // 0x705e88: ArrayLoad: r1 = r0[r4]  ; List_4
    //     0x705e88: add             x16, x0, x4, lsl #2
    //     0x705e8c: ldur            w1, [x16, #0x17]
    // 0x705e90: ubfx            x1, x1, #0, #0x20
    // 0x705e94: cmp             x1, #0xd
    // 0x705e98: b.ne            #0x705ec4
    // 0x705e9c: add             x1, x4, #1
    // 0x705ea0: cmp             x1, x2
    // 0x705ea4: b.ge            #0x705ecc
    // 0x705ea8: ArrayLoad: r5 = r0[r1]  ; List_4
    //     0x705ea8: add             x16, x0, x1, lsl #2
    //     0x705eac: ldur            w5, [x16, #0x17]
    // 0x705eb0: ubfx            x5, x5, #0, #0x20
    // 0x705eb4: cmp             x5, #0xa
    // 0x705eb8: b.ne            #0x705ecc
    // 0x705ebc: mov             x1, x3
    // 0x705ec0: b               #0x705f34
    // 0x705ec4: cmp             x1, #0xa
    // 0x705ec8: b.ne            #0x705f30
    // 0x705ecc: add             x5, x4, #1
    // 0x705ed0: stur            x5, [fp, #-0x38]
    // 0x705ed4: LoadField: r1 = r3->field_b
    //     0x705ed4: ldur            w1, [x3, #0xb]
    // 0x705ed8: LoadField: r6 = r3->field_f
    //     0x705ed8: ldur            w6, [x3, #0xf]
    // 0x705edc: DecompressPointer r6
    //     0x705edc: add             x6, x6, HEAP, lsl #32
    // 0x705ee0: LoadField: r7 = r6->field_b
    //     0x705ee0: ldur            w7, [x6, #0xb]
    // 0x705ee4: r6 = LoadInt32Instr(r1)
    //     0x705ee4: sbfx            x6, x1, #1, #0x1f
    // 0x705ee8: stur            x6, [fp, #-0x30]
    // 0x705eec: r1 = LoadInt32Instr(r7)
    //     0x705eec: sbfx            x1, x7, #1, #0x1f
    // 0x705ef0: cmp             x6, x1
    // 0x705ef4: b.ne            #0x705f00
    // 0x705ef8: mov             x1, x3
    // 0x705efc: r0 = _growToNextCapacity()
    //     0x705efc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x705f00: ldur            x2, [fp, #-0x38]
    // 0x705f04: ldur            x1, [fp, #-0x20]
    // 0x705f08: ldur            x3, [fp, #-0x30]
    // 0x705f0c: add             x4, x3, #1
    // 0x705f10: lsl             x5, x4, #1
    // 0x705f14: StoreField: r1->field_b = r5
    //     0x705f14: stur            w5, [x1, #0xb]
    // 0x705f18: LoadField: r4 = r1->field_f
    //     0x705f18: ldur            w4, [x1, #0xf]
    // 0x705f1c: DecompressPointer r4
    //     0x705f1c: add             x4, x4, HEAP, lsl #32
    // 0x705f20: lsl             x5, x2, #1
    // 0x705f24: ArrayStore: r4[r3] = r5  ; Unknown_4
    //     0x705f24: add             x2, x4, x3, lsl #2
    //     0x705f28: stur            w5, [x2, #0xf]
    // 0x705f2c: b               #0x705f34
    // 0x705f30: mov             x1, x3
    // 0x705f34: ldur            x2, [fp, #-0x40]
    // 0x705f38: add             x4, x2, #1
    // 0x705f3c: mov             x3, x1
    // 0x705f40: b               #0x705e68
    // 0x705f44: r0 = Null
    //     0x705f44: mov             x0, NULL
    // 0x705f48: LeaveFrame
    //     0x705f48: mov             SP, fp
    //     0x705f4c: ldp             fp, lr, [SP], #0x10
    // 0x705f50: ret
    //     0x705f50: ret             
    // 0x705f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x705f54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x705f58: b               #0x705d70
    // 0x705f5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x705f5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x705f60: b               #0x705e80
  }
  _ location(/* No info */) {
    // ** addr: 0x89613c, size: 0x58
    // 0x89613c: EnterFrame
    //     0x89613c: stp             fp, lr, [SP, #-0x10]!
    //     0x896140: mov             fp, SP
    // 0x896144: AllocStack(0x10)
    //     0x896144: sub             SP, SP, #0x10
    // 0x896148: SetupParameters(SourceFile this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x896148: mov             x3, x2
    //     0x89614c: stur            x2, [fp, #-0x10]
    //     0x896150: mov             x2, x1
    //     0x896154: stur            x1, [fp, #-8]
    // 0x896158: CheckStackOverflow
    //     0x896158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89615c: cmp             SP, x16
    //     0x896160: b.ls            #0x89618c
    // 0x896164: r0 = FileLocation()
    //     0x896164: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0x896168: mov             x1, x0
    // 0x89616c: ldur            x2, [fp, #-8]
    // 0x896170: ldur            x3, [fp, #-0x10]
    // 0x896174: stur            x0, [fp, #-8]
    // 0x896178: r0 = FileLocation._()
    //     0x896178: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0x89617c: ldur            x0, [fp, #-8]
    // 0x896180: LeaveFrame
    //     0x896180: mov             SP, fp
    //     0x896184: ldp             fp, lr, [SP], #0x10
    // 0x896188: ret
    //     0x896188: ret             
    // 0x89618c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89618c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x896190: b               #0x896164
  }
  _ getText(/* No info */) {
    // ** addr: 0x954b40, size: 0x68
    // 0x954b40: EnterFrame
    //     0x954b40: stp             fp, lr, [SP, #-0x10]!
    //     0x954b44: mov             fp, SP
    // 0x954b48: AllocStack(0x8)
    //     0x954b48: sub             SP, SP, #8
    // 0x954b4c: CheckStackOverflow
    //     0x954b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x954b50: cmp             SP, x16
    //     0x954b54: b.ls            #0x954ba0
    // 0x954b58: LoadField: r4 = r1->field_f
    //     0x954b58: ldur            w4, [x1, #0xf]
    // 0x954b5c: DecompressPointer r4
    //     0x954b5c: add             x4, x4, HEAP, lsl #32
    // 0x954b60: r0 = BoxInt64Instr(r3)
    //     0x954b60: sbfiz           x0, x3, #1, #0x1f
    //     0x954b64: cmp             x3, x0, asr #1
    //     0x954b68: b.eq            #0x954b74
    //     0x954b6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x954b70: stur            x3, [x0, #7]
    // 0x954b74: str             x0, [SP]
    // 0x954b78: mov             x1, x4
    // 0x954b7c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x954b7c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x954b80: r0 = sublist()
    //     0x954b80: bl              #0x6e7b20  ; [dart:typed_data] __Uint32List&_TypedList&_IntListMixin&_TypedIntListMixin::sublist
    // 0x954b84: mov             x1, x0
    // 0x954b88: r2 = 0
    //     0x954b88: movz            x2, #0
    // 0x954b8c: r3 = Null
    //     0x954b8c: mov             x3, NULL
    // 0x954b90: r0 = createFromCharCodes()
    //     0x954b90: bl              #0x601670  ; [dart:core] _StringBase::createFromCharCodes
    // 0x954b94: LeaveFrame
    //     0x954b94: mov             SP, fp
    //     0x954b98: ldp             fp, lr, [SP], #0x10
    // 0x954b9c: ret
    //     0x954b9c: ret             
    // 0x954ba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x954ba0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x954ba4: b               #0x954b58
  }
  _ getColumn(/* No info */) {
    // ** addr: 0xc1b124, size: 0x264
    // 0xc1b124: EnterFrame
    //     0xc1b124: stp             fp, lr, [SP, #-0x10]!
    //     0xc1b128: mov             fp, SP
    // 0xc1b12c: AllocStack(0x28)
    //     0xc1b12c: sub             SP, SP, #0x28
    // 0xc1b130: SetupParameters(SourceFile this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc1b130: mov             x3, x1
    //     0xc1b134: mov             x0, x2
    //     0xc1b138: stur            x1, [fp, #-8]
    //     0xc1b13c: stur            x2, [fp, #-0x10]
    // 0xc1b140: CheckStackOverflow
    //     0xc1b140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1b144: cmp             SP, x16
    //     0xc1b148: b.ls            #0xc1b37c
    // 0xc1b14c: tbnz            x0, #0x3f, #0xc1b1f4
    // 0xc1b150: LoadField: r1 = r3->field_f
    //     0xc1b150: ldur            w1, [x3, #0xf]
    // 0xc1b154: DecompressPointer r1
    //     0xc1b154: add             x1, x1, HEAP, lsl #32
    // 0xc1b158: LoadField: r4 = r1->field_13
    //     0xc1b158: ldur            w4, [x1, #0x13]
    // 0xc1b15c: stur            x4, [fp, #-0x18]
    // 0xc1b160: r1 = LoadInt32Instr(r4)
    //     0xc1b160: sbfx            x1, x4, #1, #0x1f
    // 0xc1b164: cmp             x0, x1
    // 0xc1b168: b.gt            #0xc1b268
    // 0xc1b16c: mov             x1, x3
    // 0xc1b170: mov             x2, x0
    // 0xc1b174: r0 = getLine()
    //     0xc1b174: bl              #0xc1b388  ; [package:source_span/src/file.dart] SourceFile::getLine
    // 0xc1b178: mov             x2, x0
    // 0xc1b17c: ldur            x0, [fp, #-8]
    // 0xc1b180: LoadField: r3 = r0->field_b
    //     0xc1b180: ldur            w3, [x0, #0xb]
    // 0xc1b184: DecompressPointer r3
    //     0xc1b184: add             x3, x3, HEAP, lsl #32
    // 0xc1b188: LoadField: r0 = r3->field_b
    //     0xc1b188: ldur            w0, [x3, #0xb]
    // 0xc1b18c: r1 = LoadInt32Instr(r0)
    //     0xc1b18c: sbfx            x1, x0, #1, #0x1f
    // 0xc1b190: mov             x0, x1
    // 0xc1b194: mov             x1, x2
    // 0xc1b198: cmp             x1, x0
    // 0xc1b19c: b.hs            #0xc1b384
    // 0xc1b1a0: LoadField: r4 = r3->field_f
    //     0xc1b1a0: ldur            w4, [x3, #0xf]
    // 0xc1b1a4: DecompressPointer r4
    //     0xc1b1a4: add             x4, x4, HEAP, lsl #32
    // 0xc1b1a8: r0 = BoxInt64Instr(r2)
    //     0xc1b1a8: sbfiz           x0, x2, #1, #0x1f
    //     0xc1b1ac: cmp             x2, x0, asr #1
    //     0xc1b1b0: b.eq            #0xc1b1bc
    //     0xc1b1b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1b1b8: stur            x2, [x0, #7]
    // 0xc1b1bc: stur            x0, [fp, #-0x20]
    // 0xc1b1c0: ArrayLoad: r1 = r4[r2]  ; Unknown_4
    //     0xc1b1c0: add             x16, x4, x2, lsl #2
    //     0xc1b1c4: ldur            w1, [x16, #0xf]
    // 0xc1b1c8: DecompressPointer r1
    //     0xc1b1c8: add             x1, x1, HEAP, lsl #32
    // 0xc1b1cc: r2 = LoadInt32Instr(r1)
    //     0xc1b1cc: sbfx            x2, x1, #1, #0x1f
    //     0xc1b1d0: tbz             w1, #0, #0xc1b1d8
    //     0xc1b1d4: ldur            x2, [x1, #7]
    // 0xc1b1d8: ldur            x3, [fp, #-0x10]
    // 0xc1b1dc: cmp             x2, x3
    // 0xc1b1e0: b.gt            #0xc1b2f4
    // 0xc1b1e4: sub             x0, x3, x2
    // 0xc1b1e8: LeaveFrame
    //     0xc1b1e8: mov             SP, fp
    //     0xc1b1ec: ldp             fp, lr, [SP], #0x10
    // 0xc1b1f0: ret
    //     0xc1b1f0: ret             
    // 0xc1b1f4: mov             x3, x0
    // 0xc1b1f8: r1 = Null
    //     0xc1b1f8: mov             x1, NULL
    // 0xc1b1fc: r2 = 6
    //     0xc1b1fc: movz            x2, #0x6
    // 0xc1b200: r0 = AllocateArray()
    //     0xc1b200: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1b204: mov             x2, x0
    // 0xc1b208: r16 = "Offset may not be negative, was "
    //     0xc1b208: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c538] "Offset may not be negative, was "
    //     0xc1b20c: ldr             x16, [x16, #0x538]
    // 0xc1b210: StoreField: r2->field_f = r16
    //     0xc1b210: stur            w16, [x2, #0xf]
    // 0xc1b214: ldur            x3, [fp, #-0x10]
    // 0xc1b218: r0 = BoxInt64Instr(r3)
    //     0xc1b218: sbfiz           x0, x3, #1, #0x1f
    //     0xc1b21c: cmp             x3, x0, asr #1
    //     0xc1b220: b.eq            #0xc1b22c
    //     0xc1b224: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1b228: stur            x3, [x0, #7]
    // 0xc1b22c: StoreField: r2->field_13 = r0
    //     0xc1b22c: stur            w0, [x2, #0x13]
    // 0xc1b230: r16 = "."
    //     0xc1b230: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1b234: ArrayStore: r2[0] = r16  ; List_4
    //     0xc1b234: stur            w16, [x2, #0x17]
    // 0xc1b238: str             x2, [SP]
    // 0xc1b23c: r0 = _interpolate()
    //     0xc1b23c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1b240: stur            x0, [fp, #-8]
    // 0xc1b244: r0 = RangeError()
    //     0xc1b244: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xc1b248: mov             x1, x0
    // 0xc1b24c: ldur            x0, [fp, #-8]
    // 0xc1b250: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1b250: stur            w0, [x1, #0x17]
    // 0xc1b254: r0 = false
    //     0xc1b254: add             x0, NULL, #0x30  ; false
    // 0xc1b258: StoreField: r1->field_b = r0
    //     0xc1b258: stur            w0, [x1, #0xb]
    // 0xc1b25c: mov             x0, x1
    // 0xc1b260: r0 = Throw()
    //     0xc1b260: bl              #0xec04b8  ; ThrowStub
    // 0xc1b264: brk             #0
    // 0xc1b268: mov             x3, x0
    // 0xc1b26c: r0 = false
    //     0xc1b26c: add             x0, NULL, #0x30  ; false
    // 0xc1b270: r1 = Null
    //     0xc1b270: mov             x1, NULL
    // 0xc1b274: r2 = 10
    //     0xc1b274: movz            x2, #0xa
    // 0xc1b278: r0 = AllocateArray()
    //     0xc1b278: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1b27c: mov             x2, x0
    // 0xc1b280: r16 = "Offset "
    //     0xc1b280: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c540] "Offset "
    //     0xc1b284: ldr             x16, [x16, #0x540]
    // 0xc1b288: StoreField: r2->field_f = r16
    //     0xc1b288: stur            w16, [x2, #0xf]
    // 0xc1b28c: ldur            x3, [fp, #-0x10]
    // 0xc1b290: r0 = BoxInt64Instr(r3)
    //     0xc1b290: sbfiz           x0, x3, #1, #0x1f
    //     0xc1b294: cmp             x3, x0, asr #1
    //     0xc1b298: b.eq            #0xc1b2a4
    //     0xc1b29c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1b2a0: stur            x3, [x0, #7]
    // 0xc1b2a4: StoreField: r2->field_13 = r0
    //     0xc1b2a4: stur            w0, [x2, #0x13]
    // 0xc1b2a8: r16 = " must be not be greater than the number of characters in the file, "
    //     0xc1b2a8: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c798] " must be not be greater than the number of characters in the file, "
    //     0xc1b2ac: ldr             x16, [x16, #0x798]
    // 0xc1b2b0: ArrayStore: r2[0] = r16  ; List_4
    //     0xc1b2b0: stur            w16, [x2, #0x17]
    // 0xc1b2b4: ldur            x0, [fp, #-0x18]
    // 0xc1b2b8: StoreField: r2->field_1b = r0
    //     0xc1b2b8: stur            w0, [x2, #0x1b]
    // 0xc1b2bc: r16 = "."
    //     0xc1b2bc: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1b2c0: StoreField: r2->field_1f = r16
    //     0xc1b2c0: stur            w16, [x2, #0x1f]
    // 0xc1b2c4: str             x2, [SP]
    // 0xc1b2c8: r0 = _interpolate()
    //     0xc1b2c8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1b2cc: stur            x0, [fp, #-8]
    // 0xc1b2d0: r0 = RangeError()
    //     0xc1b2d0: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xc1b2d4: mov             x1, x0
    // 0xc1b2d8: ldur            x0, [fp, #-8]
    // 0xc1b2dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1b2dc: stur            w0, [x1, #0x17]
    // 0xc1b2e0: r4 = false
    //     0xc1b2e0: add             x4, NULL, #0x30  ; false
    // 0xc1b2e4: StoreField: r1->field_b = r4
    //     0xc1b2e4: stur            w4, [x1, #0xb]
    // 0xc1b2e8: mov             x0, x1
    // 0xc1b2ec: r0 = Throw()
    //     0xc1b2ec: bl              #0xec04b8  ; ThrowStub
    // 0xc1b2f0: brk             #0
    // 0xc1b2f4: r4 = false
    //     0xc1b2f4: add             x4, NULL, #0x30  ; false
    // 0xc1b2f8: r1 = Null
    //     0xc1b2f8: mov             x1, NULL
    // 0xc1b2fc: r2 = 10
    //     0xc1b2fc: movz            x2, #0xa
    // 0xc1b300: r0 = AllocateArray()
    //     0xc1b300: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1b304: mov             x2, x0
    // 0xc1b308: r16 = "Line "
    //     0xc1b308: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c7a0] "Line "
    //     0xc1b30c: ldr             x16, [x16, #0x7a0]
    // 0xc1b310: StoreField: r2->field_f = r16
    //     0xc1b310: stur            w16, [x2, #0xf]
    // 0xc1b314: ldur            x0, [fp, #-0x20]
    // 0xc1b318: StoreField: r2->field_13 = r0
    //     0xc1b318: stur            w0, [x2, #0x13]
    // 0xc1b31c: r16 = " comes after offset "
    //     0xc1b31c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c7a8] " comes after offset "
    //     0xc1b320: ldr             x16, [x16, #0x7a8]
    // 0xc1b324: ArrayStore: r2[0] = r16  ; List_4
    //     0xc1b324: stur            w16, [x2, #0x17]
    // 0xc1b328: ldur            x3, [fp, #-0x10]
    // 0xc1b32c: r0 = BoxInt64Instr(r3)
    //     0xc1b32c: sbfiz           x0, x3, #1, #0x1f
    //     0xc1b330: cmp             x3, x0, asr #1
    //     0xc1b334: b.eq            #0xc1b340
    //     0xc1b338: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1b33c: stur            x3, [x0, #7]
    // 0xc1b340: StoreField: r2->field_1b = r0
    //     0xc1b340: stur            w0, [x2, #0x1b]
    // 0xc1b344: r16 = "."
    //     0xc1b344: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1b348: StoreField: r2->field_1f = r16
    //     0xc1b348: stur            w16, [x2, #0x1f]
    // 0xc1b34c: str             x2, [SP]
    // 0xc1b350: r0 = _interpolate()
    //     0xc1b350: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1b354: stur            x0, [fp, #-8]
    // 0xc1b358: r0 = RangeError()
    //     0xc1b358: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xc1b35c: mov             x1, x0
    // 0xc1b360: ldur            x0, [fp, #-8]
    // 0xc1b364: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1b364: stur            w0, [x1, #0x17]
    // 0xc1b368: r0 = false
    //     0xc1b368: add             x0, NULL, #0x30  ; false
    // 0xc1b36c: StoreField: r1->field_b = r0
    //     0xc1b36c: stur            w0, [x1, #0xb]
    // 0xc1b370: mov             x0, x1
    // 0xc1b374: r0 = Throw()
    //     0xc1b374: bl              #0xec04b8  ; ThrowStub
    // 0xc1b378: brk             #0
    // 0xc1b37c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1b37c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1b380: b               #0xc1b14c
    // 0xc1b384: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1b384: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ getLine(/* No info */) {
    // ** addr: 0xc1b388, size: 0x2c0
    // 0xc1b388: EnterFrame
    //     0xc1b388: stp             fp, lr, [SP, #-0x10]!
    //     0xc1b38c: mov             fp, SP
    // 0xc1b390: AllocStack(0x28)
    //     0xc1b390: sub             SP, SP, #0x28
    // 0xc1b394: SetupParameters(SourceFile this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xc1b394: mov             x0, x1
    //     0xc1b398: stur            x1, [fp, #-0x10]
    //     0xc1b39c: stur            x2, [fp, #-0x18]
    // 0xc1b3a0: CheckStackOverflow
    //     0xc1b3a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1b3a4: cmp             SP, x16
    //     0xc1b3a8: b.ls            #0xc1b630
    // 0xc1b3ac: tbnz            x2, #0x3f, #0xc1b530
    // 0xc1b3b0: LoadField: r1 = r0->field_f
    //     0xc1b3b0: ldur            w1, [x0, #0xf]
    // 0xc1b3b4: DecompressPointer r1
    //     0xc1b3b4: add             x1, x1, HEAP, lsl #32
    // 0xc1b3b8: LoadField: r3 = r1->field_13
    //     0xc1b3b8: ldur            w3, [x1, #0x13]
    // 0xc1b3bc: stur            x3, [fp, #-0x20]
    // 0xc1b3c0: r1 = LoadInt32Instr(r3)
    //     0xc1b3c0: sbfx            x1, x3, #1, #0x1f
    // 0xc1b3c4: cmp             x2, x1
    // 0xc1b3c8: b.gt            #0xc1b5a4
    // 0xc1b3cc: LoadField: r3 = r0->field_b
    //     0xc1b3cc: ldur            w3, [x0, #0xb]
    // 0xc1b3d0: DecompressPointer r3
    //     0xc1b3d0: add             x3, x3, HEAP, lsl #32
    // 0xc1b3d4: mov             x1, x3
    // 0xc1b3d8: stur            x3, [fp, #-8]
    // 0xc1b3dc: r0 = first()
    //     0xc1b3dc: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xc1b3e0: r1 = LoadInt32Instr(r0)
    //     0xc1b3e0: sbfx            x1, x0, #1, #0x1f
    //     0xc1b3e4: tbz             w0, #0, #0xc1b3ec
    //     0xc1b3e8: ldur            x1, [x0, #7]
    // 0xc1b3ec: ldur            x2, [fp, #-0x18]
    // 0xc1b3f0: cmp             x2, x1
    // 0xc1b3f4: b.ge            #0xc1b408
    // 0xc1b3f8: r0 = -1
    //     0xc1b3f8: movn            x0, #0
    // 0xc1b3fc: LeaveFrame
    //     0xc1b3fc: mov             SP, fp
    //     0xc1b400: ldp             fp, lr, [SP], #0x10
    // 0xc1b404: ret
    //     0xc1b404: ret             
    // 0xc1b408: ldur            x1, [fp, #-8]
    // 0xc1b40c: r0 = last()
    //     0xc1b40c: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xc1b410: r1 = LoadInt32Instr(r0)
    //     0xc1b410: sbfx            x1, x0, #1, #0x1f
    //     0xc1b414: tbz             w0, #0, #0xc1b41c
    //     0xc1b418: ldur            x1, [x0, #7]
    // 0xc1b41c: ldur            x0, [fp, #-0x18]
    // 0xc1b420: cmp             x0, x1
    // 0xc1b424: b.lt            #0xc1b444
    // 0xc1b428: ldur            x3, [fp, #-8]
    // 0xc1b42c: LoadField: r0 = r3->field_b
    //     0xc1b42c: ldur            w0, [x3, #0xb]
    // 0xc1b430: r1 = LoadInt32Instr(r0)
    //     0xc1b430: sbfx            x1, x0, #1, #0x1f
    // 0xc1b434: sub             x0, x1, #1
    // 0xc1b438: LeaveFrame
    //     0xc1b438: mov             SP, fp
    //     0xc1b43c: ldp             fp, lr, [SP], #0x10
    // 0xc1b440: ret
    //     0xc1b440: ret             
    // 0xc1b444: ldur            x3, [fp, #-8]
    // 0xc1b448: ldur            x1, [fp, #-0x10]
    // 0xc1b44c: mov             x2, x0
    // 0xc1b450: r0 = _isNearCachedLine()
    //     0xc1b450: bl              #0xc1b648  ; [package:source_span/src/file.dart] SourceFile::_isNearCachedLine
    // 0xc1b454: tbnz            w0, #4, #0xc1b488
    // 0xc1b458: ldur            x2, [fp, #-0x10]
    // 0xc1b45c: LoadField: r0 = r2->field_13
    //     0xc1b45c: ldur            w0, [x2, #0x13]
    // 0xc1b460: DecompressPointer r0
    //     0xc1b460: add             x0, x0, HEAP, lsl #32
    // 0xc1b464: cmp             w0, NULL
    // 0xc1b468: b.eq            #0xc1b638
    // 0xc1b46c: r1 = LoadInt32Instr(r0)
    //     0xc1b46c: sbfx            x1, x0, #1, #0x1f
    //     0xc1b470: tbz             w0, #0, #0xc1b478
    //     0xc1b474: ldur            x1, [x0, #7]
    // 0xc1b478: mov             x0, x1
    // 0xc1b47c: LeaveFrame
    //     0xc1b47c: mov             SP, fp
    //     0xc1b480: ldp             fp, lr, [SP], #0x10
    // 0xc1b484: ret
    //     0xc1b484: ret             
    // 0xc1b488: ldur            x2, [fp, #-0x10]
    // 0xc1b48c: ldur            x0, [fp, #-8]
    // 0xc1b490: LoadField: r1 = r0->field_b
    //     0xc1b490: ldur            w1, [x0, #0xb]
    // 0xc1b494: r3 = LoadInt32Instr(r1)
    //     0xc1b494: sbfx            x3, x1, #1, #0x1f
    // 0xc1b498: sub             x1, x3, #1
    // 0xc1b49c: LoadField: r4 = r0->field_f
    //     0xc1b49c: ldur            w4, [x0, #0xf]
    // 0xc1b4a0: DecompressPointer r4
    //     0xc1b4a0: add             x4, x4, HEAP, lsl #32
    // 0xc1b4a4: mov             x7, x1
    // 0xc1b4a8: ldur            x5, [fp, #-0x18]
    // 0xc1b4ac: r8 = 0
    //     0xc1b4ac: movz            x8, #0
    // 0xc1b4b0: r6 = 2
    //     0xc1b4b0: movz            x6, #0x2
    // 0xc1b4b4: CheckStackOverflow
    //     0xc1b4b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1b4b8: cmp             SP, x16
    //     0xc1b4bc: b.ls            #0xc1b63c
    // 0xc1b4c0: cmp             x8, x7
    // 0xc1b4c4: b.ge            #0xc1b518
    // 0xc1b4c8: sub             x0, x7, x8
    // 0xc1b4cc: sdiv            x1, x0, x6
    // 0xc1b4d0: add             x9, x8, x1
    // 0xc1b4d4: mov             x0, x3
    // 0xc1b4d8: mov             x1, x9
    // 0xc1b4dc: cmp             x1, x0
    // 0xc1b4e0: b.hs            #0xc1b644
    // 0xc1b4e4: ArrayLoad: r0 = r4[r9]  ; Unknown_4
    //     0xc1b4e4: add             x16, x4, x9, lsl #2
    //     0xc1b4e8: ldur            w0, [x16, #0xf]
    // 0xc1b4ec: DecompressPointer r0
    //     0xc1b4ec: add             x0, x0, HEAP, lsl #32
    // 0xc1b4f0: r1 = LoadInt32Instr(r0)
    //     0xc1b4f0: sbfx            x1, x0, #1, #0x1f
    //     0xc1b4f4: tbz             w0, #0, #0xc1b4fc
    //     0xc1b4f8: ldur            x1, [x0, #7]
    // 0xc1b4fc: cmp             x1, x5
    // 0xc1b500: b.le            #0xc1b50c
    // 0xc1b504: mov             x7, x9
    // 0xc1b508: b               #0xc1b4b4
    // 0xc1b50c: add             x0, x9, #1
    // 0xc1b510: mov             x8, x0
    // 0xc1b514: b               #0xc1b4b4
    // 0xc1b518: sub             x0, x7, #1
    // 0xc1b51c: lsl             x1, x0, #1
    // 0xc1b520: StoreField: r2->field_13 = r1
    //     0xc1b520: stur            w1, [x2, #0x13]
    // 0xc1b524: LeaveFrame
    //     0xc1b524: mov             SP, fp
    //     0xc1b528: ldp             fp, lr, [SP], #0x10
    // 0xc1b52c: ret
    //     0xc1b52c: ret             
    // 0xc1b530: mov             x5, x2
    // 0xc1b534: r1 = Null
    //     0xc1b534: mov             x1, NULL
    // 0xc1b538: r2 = 6
    //     0xc1b538: movz            x2, #0x6
    // 0xc1b53c: r0 = AllocateArray()
    //     0xc1b53c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1b540: mov             x2, x0
    // 0xc1b544: r16 = "Offset may not be negative, was "
    //     0xc1b544: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c538] "Offset may not be negative, was "
    //     0xc1b548: ldr             x16, [x16, #0x538]
    // 0xc1b54c: StoreField: r2->field_f = r16
    //     0xc1b54c: stur            w16, [x2, #0xf]
    // 0xc1b550: ldur            x4, [fp, #-0x18]
    // 0xc1b554: r0 = BoxInt64Instr(r4)
    //     0xc1b554: sbfiz           x0, x4, #1, #0x1f
    //     0xc1b558: cmp             x4, x0, asr #1
    //     0xc1b55c: b.eq            #0xc1b568
    //     0xc1b560: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1b564: stur            x4, [x0, #7]
    // 0xc1b568: StoreField: r2->field_13 = r0
    //     0xc1b568: stur            w0, [x2, #0x13]
    // 0xc1b56c: r16 = "."
    //     0xc1b56c: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1b570: ArrayStore: r2[0] = r16  ; List_4
    //     0xc1b570: stur            w16, [x2, #0x17]
    // 0xc1b574: str             x2, [SP]
    // 0xc1b578: r0 = _interpolate()
    //     0xc1b578: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1b57c: stur            x0, [fp, #-8]
    // 0xc1b580: r0 = RangeError()
    //     0xc1b580: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xc1b584: mov             x1, x0
    // 0xc1b588: ldur            x0, [fp, #-8]
    // 0xc1b58c: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1b58c: stur            w0, [x1, #0x17]
    // 0xc1b590: r0 = false
    //     0xc1b590: add             x0, NULL, #0x30  ; false
    // 0xc1b594: StoreField: r1->field_b = r0
    //     0xc1b594: stur            w0, [x1, #0xb]
    // 0xc1b598: mov             x0, x1
    // 0xc1b59c: r0 = Throw()
    //     0xc1b59c: bl              #0xec04b8  ; ThrowStub
    // 0xc1b5a0: brk             #0
    // 0xc1b5a4: mov             x4, x2
    // 0xc1b5a8: r0 = false
    //     0xc1b5a8: add             x0, NULL, #0x30  ; false
    // 0xc1b5ac: r1 = Null
    //     0xc1b5ac: mov             x1, NULL
    // 0xc1b5b0: r2 = 10
    //     0xc1b5b0: movz            x2, #0xa
    // 0xc1b5b4: r0 = AllocateArray()
    //     0xc1b5b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1b5b8: mov             x2, x0
    // 0xc1b5bc: r16 = "Offset "
    //     0xc1b5bc: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c540] "Offset "
    //     0xc1b5c0: ldr             x16, [x16, #0x540]
    // 0xc1b5c4: StoreField: r2->field_f = r16
    //     0xc1b5c4: stur            w16, [x2, #0xf]
    // 0xc1b5c8: ldur            x3, [fp, #-0x18]
    // 0xc1b5cc: r0 = BoxInt64Instr(r3)
    //     0xc1b5cc: sbfiz           x0, x3, #1, #0x1f
    //     0xc1b5d0: cmp             x3, x0, asr #1
    //     0xc1b5d4: b.eq            #0xc1b5e0
    //     0xc1b5d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1b5dc: stur            x3, [x0, #7]
    // 0xc1b5e0: StoreField: r2->field_13 = r0
    //     0xc1b5e0: stur            w0, [x2, #0x13]
    // 0xc1b5e4: r16 = " must not be greater than the number of characters in the file, "
    //     0xc1b5e4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10930] " must not be greater than the number of characters in the file, "
    //     0xc1b5e8: ldr             x16, [x16, #0x930]
    // 0xc1b5ec: ArrayStore: r2[0] = r16  ; List_4
    //     0xc1b5ec: stur            w16, [x2, #0x17]
    // 0xc1b5f0: ldur            x0, [fp, #-0x20]
    // 0xc1b5f4: StoreField: r2->field_1b = r0
    //     0xc1b5f4: stur            w0, [x2, #0x1b]
    // 0xc1b5f8: r16 = "."
    //     0xc1b5f8: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1b5fc: StoreField: r2->field_1f = r16
    //     0xc1b5fc: stur            w16, [x2, #0x1f]
    // 0xc1b600: str             x2, [SP]
    // 0xc1b604: r0 = _interpolate()
    //     0xc1b604: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1b608: stur            x0, [fp, #-8]
    // 0xc1b60c: r0 = RangeError()
    //     0xc1b60c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xc1b610: mov             x1, x0
    // 0xc1b614: ldur            x0, [fp, #-8]
    // 0xc1b618: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1b618: stur            w0, [x1, #0x17]
    // 0xc1b61c: r0 = false
    //     0xc1b61c: add             x0, NULL, #0x30  ; false
    // 0xc1b620: StoreField: r1->field_b = r0
    //     0xc1b620: stur            w0, [x1, #0xb]
    // 0xc1b624: mov             x0, x1
    // 0xc1b628: r0 = Throw()
    //     0xc1b628: bl              #0xec04b8  ; ThrowStub
    // 0xc1b62c: brk             #0
    // 0xc1b630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1b630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1b634: b               #0xc1b3ac
    // 0xc1b638: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1b638: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc1b63c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1b63c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1b640: b               #0xc1b4c0
    // 0xc1b644: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1b644: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _isNearCachedLine(/* No info */) {
    // ** addr: 0xc1b648, size: 0x154
    // 0xc1b648: EnterFrame
    //     0xc1b648: stp             fp, lr, [SP, #-0x10]!
    //     0xc1b64c: mov             fp, SP
    // 0xc1b650: mov             x3, x1
    // 0xc1b654: LoadField: r4 = r3->field_13
    //     0xc1b654: ldur            w4, [x3, #0x13]
    // 0xc1b658: DecompressPointer r4
    //     0xc1b658: add             x4, x4, HEAP, lsl #32
    // 0xc1b65c: cmp             w4, NULL
    // 0xc1b660: b.ne            #0xc1b674
    // 0xc1b664: r0 = false
    //     0xc1b664: add             x0, NULL, #0x30  ; false
    // 0xc1b668: LeaveFrame
    //     0xc1b668: mov             SP, fp
    //     0xc1b66c: ldp             fp, lr, [SP], #0x10
    // 0xc1b670: ret
    //     0xc1b670: ret             
    // 0xc1b674: LoadField: r5 = r3->field_b
    //     0xc1b674: ldur            w5, [x3, #0xb]
    // 0xc1b678: DecompressPointer r5
    //     0xc1b678: add             x5, x5, HEAP, lsl #32
    // 0xc1b67c: LoadField: r6 = r5->field_b
    //     0xc1b67c: ldur            w6, [x5, #0xb]
    // 0xc1b680: r7 = LoadInt32Instr(r4)
    //     0xc1b680: sbfx            x7, x4, #1, #0x1f
    //     0xc1b684: tbz             w4, #0, #0xc1b68c
    //     0xc1b688: ldur            x7, [x4, #7]
    // 0xc1b68c: r4 = LoadInt32Instr(r6)
    //     0xc1b68c: sbfx            x4, x6, #1, #0x1f
    // 0xc1b690: mov             x0, x4
    // 0xc1b694: mov             x1, x7
    // 0xc1b698: cmp             x1, x0
    // 0xc1b69c: b.hs            #0xc1b790
    // 0xc1b6a0: LoadField: r6 = r5->field_f
    //     0xc1b6a0: ldur            w6, [x5, #0xf]
    // 0xc1b6a4: DecompressPointer r6
    //     0xc1b6a4: add             x6, x6, HEAP, lsl #32
    // 0xc1b6a8: ArrayLoad: r5 = r6[r7]  ; Unknown_4
    //     0xc1b6a8: add             x16, x6, x7, lsl #2
    //     0xc1b6ac: ldur            w5, [x16, #0xf]
    // 0xc1b6b0: DecompressPointer r5
    //     0xc1b6b0: add             x5, x5, HEAP, lsl #32
    // 0xc1b6b4: r8 = LoadInt32Instr(r5)
    //     0xc1b6b4: sbfx            x8, x5, #1, #0x1f
    //     0xc1b6b8: tbz             w5, #0, #0xc1b6c0
    //     0xc1b6bc: ldur            x8, [x5, #7]
    // 0xc1b6c0: cmp             x2, x8
    // 0xc1b6c4: b.ge            #0xc1b6d8
    // 0xc1b6c8: r0 = false
    //     0xc1b6c8: add             x0, NULL, #0x30  ; false
    // 0xc1b6cc: LeaveFrame
    //     0xc1b6cc: mov             SP, fp
    //     0xc1b6d0: ldp             fp, lr, [SP], #0x10
    // 0xc1b6d4: ret
    //     0xc1b6d4: ret             
    // 0xc1b6d8: sub             x5, x4, #1
    // 0xc1b6dc: cmp             x7, x5
    // 0xc1b6e0: b.ge            #0xc1b71c
    // 0xc1b6e4: add             x5, x7, #1
    // 0xc1b6e8: mov             x0, x4
    // 0xc1b6ec: mov             x1, x5
    // 0xc1b6f0: cmp             x1, x0
    // 0xc1b6f4: b.hs            #0xc1b794
    // 0xc1b6f8: lsl             x8, x5, #1
    // 0xc1b6fc: ArrayLoad: r9 = r6[r5]  ; Unknown_4
    //     0xc1b6fc: add             x16, x6, x5, lsl #2
    //     0xc1b700: ldur            w9, [x16, #0xf]
    // 0xc1b704: DecompressPointer r9
    //     0xc1b704: add             x9, x9, HEAP, lsl #32
    // 0xc1b708: r5 = LoadInt32Instr(r9)
    //     0xc1b708: sbfx            x5, x9, #1, #0x1f
    //     0xc1b70c: tbz             w9, #0, #0xc1b714
    //     0xc1b710: ldur            x5, [x9, #7]
    // 0xc1b714: cmp             x2, x5
    // 0xc1b718: b.ge            #0xc1b72c
    // 0xc1b71c: r0 = true
    //     0xc1b71c: add             x0, NULL, #0x20  ; true
    // 0xc1b720: LeaveFrame
    //     0xc1b720: mov             SP, fp
    //     0xc1b724: ldp             fp, lr, [SP], #0x10
    // 0xc1b728: ret
    //     0xc1b728: ret             
    // 0xc1b72c: sub             x5, x4, #2
    // 0xc1b730: cmp             x7, x5
    // 0xc1b734: b.ge            #0xc1b76c
    // 0xc1b738: add             x5, x7, #2
    // 0xc1b73c: mov             x0, x4
    // 0xc1b740: mov             x1, x5
    // 0xc1b744: cmp             x1, x0
    // 0xc1b748: b.hs            #0xc1b798
    // 0xc1b74c: ArrayLoad: r1 = r6[r5]  ; Unknown_4
    //     0xc1b74c: add             x16, x6, x5, lsl #2
    //     0xc1b750: ldur            w1, [x16, #0xf]
    // 0xc1b754: DecompressPointer r1
    //     0xc1b754: add             x1, x1, HEAP, lsl #32
    // 0xc1b758: r4 = LoadInt32Instr(r1)
    //     0xc1b758: sbfx            x4, x1, #1, #0x1f
    //     0xc1b75c: tbz             w1, #0, #0xc1b764
    //     0xc1b760: ldur            x4, [x1, #7]
    // 0xc1b764: cmp             x2, x4
    // 0xc1b768: b.ge            #0xc1b780
    // 0xc1b76c: StoreField: r3->field_13 = r8
    //     0xc1b76c: stur            w8, [x3, #0x13]
    // 0xc1b770: r0 = true
    //     0xc1b770: add             x0, NULL, #0x20  ; true
    // 0xc1b774: LeaveFrame
    //     0xc1b774: mov             SP, fp
    //     0xc1b778: ldp             fp, lr, [SP], #0x10
    // 0xc1b77c: ret
    //     0xc1b77c: ret             
    // 0xc1b780: r0 = false
    //     0xc1b780: add             x0, NULL, #0x30  ; false
    // 0xc1b784: LeaveFrame
    //     0xc1b784: mov             SP, fp
    //     0xc1b788: ldp             fp, lr, [SP], #0x10
    // 0xc1b78c: ret
    //     0xc1b78c: ret             
    // 0xc1b790: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1b790: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc1b794: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1b794: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc1b798: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1b798: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ getOffset(/* No info */) {
    // ** addr: 0xeb76e0, size: 0x25c
    // 0xeb76e0: EnterFrame
    //     0xeb76e0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb76e4: mov             fp, SP
    // 0xeb76e8: AllocStack(0x28)
    //     0xeb76e8: sub             SP, SP, #0x28
    // 0xeb76ec: SetupParameters(SourceFile this /* r1 => r2 */, dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xeb76ec: mov             x3, x2
    //     0xeb76f0: stur            x2, [fp, #-8]
    //     0xeb76f4: mov             x2, x1
    // 0xeb76f8: CheckStackOverflow
    //     0xeb76f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb76fc: cmp             SP, x16
    //     0xeb7700: b.ls            #0xeb7930
    // 0xeb7704: tbnz            x3, #0x3f, #0xeb77c0
    // 0xeb7708: LoadField: r4 = r2->field_b
    //     0xeb7708: ldur            w4, [x2, #0xb]
    // 0xeb770c: DecompressPointer r4
    //     0xeb770c: add             x4, x4, HEAP, lsl #32
    // 0xeb7710: LoadField: r0 = r4->field_b
    //     0xeb7710: ldur            w0, [x4, #0xb]
    // 0xeb7714: stur            x0, [fp, #-0x18]
    // 0xeb7718: r5 = LoadInt32Instr(r0)
    //     0xeb7718: sbfx            x5, x0, #1, #0x1f
    // 0xeb771c: cmp             x3, x5
    // 0xeb7720: b.ge            #0xeb7830
    // 0xeb7724: mov             x0, x5
    // 0xeb7728: mov             x1, x3
    // 0xeb772c: cmp             x1, x0
    // 0xeb7730: b.hs            #0xeb7938
    // 0xeb7734: LoadField: r6 = r4->field_f
    //     0xeb7734: ldur            w6, [x4, #0xf]
    // 0xeb7738: DecompressPointer r6
    //     0xeb7738: add             x6, x6, HEAP, lsl #32
    // 0xeb773c: r0 = BoxInt64Instr(r3)
    //     0xeb773c: sbfiz           x0, x3, #1, #0x1f
    //     0xeb7740: cmp             x3, x0, asr #1
    //     0xeb7744: b.eq            #0xeb7750
    //     0xeb7748: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb774c: stur            x3, [x0, #7]
    // 0xeb7750: stur            x0, [fp, #-0x20]
    // 0xeb7754: ArrayLoad: r1 = r6[r3]  ; Unknown_4
    //     0xeb7754: add             x16, x6, x3, lsl #2
    //     0xeb7758: ldur            w1, [x16, #0xf]
    // 0xeb775c: DecompressPointer r1
    //     0xeb775c: add             x1, x1, HEAP, lsl #32
    // 0xeb7760: LoadField: r4 = r2->field_f
    //     0xeb7760: ldur            w4, [x2, #0xf]
    // 0xeb7764: DecompressPointer r4
    //     0xeb7764: add             x4, x4, HEAP, lsl #32
    // 0xeb7768: LoadField: r2 = r4->field_13
    //     0xeb7768: ldur            w2, [x4, #0x13]
    // 0xeb776c: r4 = LoadInt32Instr(r1)
    //     0xeb776c: sbfx            x4, x1, #1, #0x1f
    //     0xeb7770: tbz             w1, #0, #0xeb7778
    //     0xeb7774: ldur            x4, [x1, #7]
    // 0xeb7778: r1 = LoadInt32Instr(r2)
    //     0xeb7778: sbfx            x1, x2, #1, #0x1f
    // 0xeb777c: cmp             x4, x1
    // 0xeb7780: b.gt            #0xeb78b8
    // 0xeb7784: add             x1, x3, #1
    // 0xeb7788: cmp             x1, x5
    // 0xeb778c: b.ge            #0xeb77b0
    // 0xeb7790: ArrayLoad: r2 = r6[r1]  ; Unknown_4
    //     0xeb7790: add             x16, x6, x1, lsl #2
    //     0xeb7794: ldur            w2, [x16, #0xf]
    // 0xeb7798: DecompressPointer r2
    //     0xeb7798: add             x2, x2, HEAP, lsl #32
    // 0xeb779c: r1 = LoadInt32Instr(r2)
    //     0xeb779c: sbfx            x1, x2, #1, #0x1f
    //     0xeb77a0: tbz             w2, #0, #0xeb77a8
    //     0xeb77a4: ldur            x1, [x2, #7]
    // 0xeb77a8: cmp             x4, x1
    // 0xeb77ac: b.ge            #0xeb78c0
    // 0xeb77b0: mov             x0, x4
    // 0xeb77b4: LeaveFrame
    //     0xeb77b4: mov             SP, fp
    //     0xeb77b8: ldp             fp, lr, [SP], #0x10
    // 0xeb77bc: ret
    //     0xeb77bc: ret             
    // 0xeb77c0: r1 = Null
    //     0xeb77c0: mov             x1, NULL
    // 0xeb77c4: r2 = 6
    //     0xeb77c4: movz            x2, #0x6
    // 0xeb77c8: r0 = AllocateArray()
    //     0xeb77c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb77cc: mov             x2, x0
    // 0xeb77d0: r16 = "Line may not be negative, was "
    //     0xeb77d0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c788] "Line may not be negative, was "
    //     0xeb77d4: ldr             x16, [x16, #0x788]
    // 0xeb77d8: StoreField: r2->field_f = r16
    //     0xeb77d8: stur            w16, [x2, #0xf]
    // 0xeb77dc: ldur            x3, [fp, #-8]
    // 0xeb77e0: r0 = BoxInt64Instr(r3)
    //     0xeb77e0: sbfiz           x0, x3, #1, #0x1f
    //     0xeb77e4: cmp             x3, x0, asr #1
    //     0xeb77e8: b.eq            #0xeb77f4
    //     0xeb77ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb77f0: stur            x3, [x0, #7]
    // 0xeb77f4: StoreField: r2->field_13 = r0
    //     0xeb77f4: stur            w0, [x2, #0x13]
    // 0xeb77f8: r16 = "."
    //     0xeb77f8: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xeb77fc: ArrayStore: r2[0] = r16  ; List_4
    //     0xeb77fc: stur            w16, [x2, #0x17]
    // 0xeb7800: str             x2, [SP]
    // 0xeb7804: r0 = _interpolate()
    //     0xeb7804: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb7808: stur            x0, [fp, #-0x10]
    // 0xeb780c: r0 = RangeError()
    //     0xeb780c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xeb7810: mov             x1, x0
    // 0xeb7814: ldur            x0, [fp, #-0x10]
    // 0xeb7818: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb7818: stur            w0, [x1, #0x17]
    // 0xeb781c: r4 = false
    //     0xeb781c: add             x4, NULL, #0x30  ; false
    // 0xeb7820: StoreField: r1->field_b = r4
    //     0xeb7820: stur            w4, [x1, #0xb]
    // 0xeb7824: mov             x0, x1
    // 0xeb7828: r0 = Throw()
    //     0xeb7828: bl              #0xec04b8  ; ThrowStub
    // 0xeb782c: brk             #0
    // 0xeb7830: r4 = false
    //     0xeb7830: add             x4, NULL, #0x30  ; false
    // 0xeb7834: r1 = Null
    //     0xeb7834: mov             x1, NULL
    // 0xeb7838: r2 = 10
    //     0xeb7838: movz            x2, #0xa
    // 0xeb783c: r0 = AllocateArray()
    //     0xeb783c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb7840: mov             x2, x0
    // 0xeb7844: r16 = "Line "
    //     0xeb7844: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c7a0] "Line "
    //     0xeb7848: ldr             x16, [x16, #0x7a0]
    // 0xeb784c: StoreField: r2->field_f = r16
    //     0xeb784c: stur            w16, [x2, #0xf]
    // 0xeb7850: ldur            x3, [fp, #-8]
    // 0xeb7854: r0 = BoxInt64Instr(r3)
    //     0xeb7854: sbfiz           x0, x3, #1, #0x1f
    //     0xeb7858: cmp             x3, x0, asr #1
    //     0xeb785c: b.eq            #0xeb7868
    //     0xeb7860: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb7864: stur            x3, [x0, #7]
    // 0xeb7868: StoreField: r2->field_13 = r0
    //     0xeb7868: stur            w0, [x2, #0x13]
    // 0xeb786c: r16 = " must be less than the number of lines in the file, "
    //     0xeb786c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c7b0] " must be less than the number of lines in the file, "
    //     0xeb7870: ldr             x16, [x16, #0x7b0]
    // 0xeb7874: ArrayStore: r2[0] = r16  ; List_4
    //     0xeb7874: stur            w16, [x2, #0x17]
    // 0xeb7878: ldur            x0, [fp, #-0x18]
    // 0xeb787c: StoreField: r2->field_1b = r0
    //     0xeb787c: stur            w0, [x2, #0x1b]
    // 0xeb7880: r16 = "."
    //     0xeb7880: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xeb7884: StoreField: r2->field_1f = r16
    //     0xeb7884: stur            w16, [x2, #0x1f]
    // 0xeb7888: str             x2, [SP]
    // 0xeb788c: r0 = _interpolate()
    //     0xeb788c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb7890: stur            x0, [fp, #-0x10]
    // 0xeb7894: r0 = RangeError()
    //     0xeb7894: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xeb7898: mov             x1, x0
    // 0xeb789c: ldur            x0, [fp, #-0x10]
    // 0xeb78a0: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb78a0: stur            w0, [x1, #0x17]
    // 0xeb78a4: r3 = false
    //     0xeb78a4: add             x3, NULL, #0x30  ; false
    // 0xeb78a8: StoreField: r1->field_b = r3
    //     0xeb78a8: stur            w3, [x1, #0xb]
    // 0xeb78ac: mov             x0, x1
    // 0xeb78b0: r0 = Throw()
    //     0xeb78b0: bl              #0xec04b8  ; ThrowStub
    // 0xeb78b4: brk             #0
    // 0xeb78b8: r3 = false
    //     0xeb78b8: add             x3, NULL, #0x30  ; false
    // 0xeb78bc: b               #0xeb78c4
    // 0xeb78c0: r3 = false
    //     0xeb78c0: add             x3, NULL, #0x30  ; false
    // 0xeb78c4: r1 = Null
    //     0xeb78c4: mov             x1, NULL
    // 0xeb78c8: r2 = 10
    //     0xeb78c8: movz            x2, #0xa
    // 0xeb78cc: r0 = AllocateArray()
    //     0xeb78cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb78d0: r16 = "Line "
    //     0xeb78d0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c7a0] "Line "
    //     0xeb78d4: ldr             x16, [x16, #0x7a0]
    // 0xeb78d8: StoreField: r0->field_f = r16
    //     0xeb78d8: stur            w16, [x0, #0xf]
    // 0xeb78dc: ldur            x1, [fp, #-0x20]
    // 0xeb78e0: StoreField: r0->field_13 = r1
    //     0xeb78e0: stur            w1, [x0, #0x13]
    // 0xeb78e4: r16 = " doesn\'t have "
    //     0xeb78e4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c7b8] " doesn\'t have "
    //     0xeb78e8: ldr             x16, [x16, #0x7b8]
    // 0xeb78ec: ArrayStore: r0[0] = r16  ; List_4
    //     0xeb78ec: stur            w16, [x0, #0x17]
    // 0xeb78f0: StoreField: r0->field_1b = rZR
    //     0xeb78f0: stur            wzr, [x0, #0x1b]
    // 0xeb78f4: r16 = " columns."
    //     0xeb78f4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c7c0] " columns."
    //     0xeb78f8: ldr             x16, [x16, #0x7c0]
    // 0xeb78fc: StoreField: r0->field_1f = r16
    //     0xeb78fc: stur            w16, [x0, #0x1f]
    // 0xeb7900: str             x0, [SP]
    // 0xeb7904: r0 = _interpolate()
    //     0xeb7904: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb7908: stur            x0, [fp, #-0x10]
    // 0xeb790c: r0 = RangeError()
    //     0xeb790c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xeb7910: mov             x1, x0
    // 0xeb7914: ldur            x0, [fp, #-0x10]
    // 0xeb7918: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb7918: stur            w0, [x1, #0x17]
    // 0xeb791c: r0 = false
    //     0xeb791c: add             x0, NULL, #0x30  ; false
    // 0xeb7920: StoreField: r1->field_b = r0
    //     0xeb7920: stur            w0, [x1, #0xb]
    // 0xeb7924: mov             x0, x1
    // 0xeb7928: r0 = Throw()
    //     0xeb7928: bl              #0xec04b8  ; ThrowStub
    // 0xeb792c: brk             #0
    // 0xeb7930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7930: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7934: b               #0xeb7704
    // 0xeb7938: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb7938: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
